----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerLevelLoading : LuaUIBaseView
local ActivityMagicTowerLevelLoading = ui("ActivityMagicTowerLevelLoading")
local ActivityMagicTowerTextItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerTextItem"
-- local ActivityMagicTowerAStarNode = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarNode"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local EDescendantScrollDestination  = import "EDescendantScrollDestination"
local function testLog(...)
    loginfo("[Magic_Tower] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower] ", ...)
end

local DialogTextCount = 5

function ActivityMagicTowerLevelLoading:Ctor()
    self._wtDesPanel = self:Wnd("wt_DesPanel", UIWidgetBase)
    self._wtDescTxt = self:Wnd("DFComputeTextBlock_67", UITextBlock)
    self._wtDescSecondTxt = self:Wnd("DFTextBlockSecond", UITextBlock)
    -- self._wtScrollGridBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1", self._OnGetDialogeCount, self._OnProcessDialog)
    self._wtContentScrollBox = self:Wnd("DFWaterfallScrollView_1", UIWidgetBase)
    self._wtDialogText = {}
    for i = 1, DialogTextCount do 
        self._wtDialogText[i] = self:Wnd("WBP_MorgenGame_TextItem_"..i, UITextBlock)
        self._wtDialogText[i]:Collapsed()
    end
    self._wtClickBtn = self:Wnd("DFButton_169", UIButton)
    if self._wtClickBtn then
        self._wtClickBtn:Event("OnClicked", self._OnSpaceBtnClicked, self)
    end
    
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerLevelLoading:OnOpen()
    -- Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.MagicTower, true, nil, nil, false, 30)
end

function ActivityMagicTowerLevelLoading:_InitEvent()

end

function ActivityMagicTowerLevelLoading:OnInitExtraData(activityID, sceneID, notShowDesc, notEnterPanel, CallBack)
    self._activityID = activityID
    self._notShowDesc = notShowDesc
    self._notEnterPanel = notEnterPanel
    self._CallBack = CallBack or nil
    if sceneID then 
        self._sceneID = sceneID
    else
        local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
        if activityInfo == nil or next(activityInfo) == nil then return end
    
        -- 基础数据
        self.allInfo = activityInfo.arknights_game_info or {}
        if self.allInfo.game_state then 
            self._sceneID = self.allInfo.game_state
            -- self._sceneID = 4
        end
    end
end

function ActivityMagicTowerLevelLoading:OnShowBegin()
    self._delayDelegate = {}
    self:BindBackAction()
    self:_InitEvent()

    self:_InitData()
    self:_RefreshUI()
    self:PlayAnimation(self.WBP_DoctorGames_LevelLoading_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
end

function ActivityMagicTowerLevelLoading:OnHideBegin()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()
    for k,timerHandle in pairs(self._delayDelegate) do
        Timer.CancelDelay(timerHandle)
    end

end

function ActivityMagicTowerLevelLoading:OnClose()
end

function ActivityMagicTowerLevelLoading:OnNavBack()
    Facade.UIManager:CloseUI(self)
end

function ActivityMagicTowerLevelLoading:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    if self._JumpOverHandle then
        self:RemoveInputActionBinding(self._JumpOverHandle)
        self._JumpOverHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )

    self._JumpOverHandle = self:AddInputActionBinding(
        "JumpOver", 
        EInputEvent.IE_Pressed, 
        self._OnSpaceBtnClicked, 
        self, EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerLevelLoading:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    if self._JumpOverHandle then
        self:RemoveInputActionBinding(self._JumpOverHandle)
        self._JumpOverHandle = nil
    end
end

function ActivityMagicTowerLevelLoading:_RefreshUI()
    self:PlayWidgetAnim(self.WBP_DoctorGames_LevelLoading_in)
    if  self._notShowDesc then 
        if self._sceneID then 
            self:_ShowTransition(self._sceneID)
        end
    else
        self:_ShowDesc()
    end
end

function ActivityMagicTowerLevelLoading:_ShowDesc()
    self._wtDesPanel:SelfHitTestInvisible()
    self._wtDescSecondTxt:SelfHitTestInvisible()
    self._wtContentScrollBox:Collapsed()
    local data
    if self._sceneID >= #self._constId then 
        data = self._constId[#self._constId]
    else
        data = self._constId[self._sceneID]
    end
    self._wtDescTxt:SetText(self._constantConfig[data.title].constantValue)
    self._wtDescSecondTxt:SetText(self._constantConfig[data.desc].constantValue)
    --5S后播放专场
    local handle = Timer.DelayCall(
        5,
        function()
            self:_ShowTransition(data.trans)
        end
    )
    table.insert(self._delayDelegate,handle)
end

function ActivityMagicTowerLevelLoading:_ShowTransition(sceneID)
    self._wtDesPanel:Collapsed()
    self._wtDescSecondTxt:Collapsed()
    self._wtContentScrollBox:SelfHitTestInvisible()
    self:RefreshAllItems()
end

function ActivityMagicTowerLevelLoading:_OnGetDialogeCount()
    return self._pageData and #self._pageData or 0
end

-- function ActivityMagicTowerLevelLoading:_OnProcessDialog(position, itemWidget)
--     itemWidget:Collapsed()
--     local time = self._playTime
--     local handle = Timer.DelayCall(
--         time,
--         function()
--             itemWidget:SelfHitTestInvisible()
--             itemWidget:InitData(self._pageData[position].content)
--             itemWidget:PlayAnim()
--             if not IsHD() then 
--                 self._wtContentScrollBox:ScrollToIndex(position)
--             end
--             if position == #self._pageData then --播放到本页最后一个了
--                 self._playTime = 0
--                 if self._page == #self._dialogData then
--                     --展示游戏界面
--                     Module.Activity:OpenMagicTowerPanel()
--                     Facade.UIManager:CloseUI(self)
--                     return
--                 else
--                     self._page = self._page + 1
--                     self._pageData = self._dialogData[self._page]
--                 end
--                 local refreshHangle = Timer.DelayCall(
--                     self._pageData[position].endurance,
--                     function()
--                         self:RefreshAllItems()
--                     end
--                 )
--                 table.insert(self._delayDelegate,refreshHangle)
--             end
--         end
--     )
--     table.insert(self._delayDelegate,handle)
--     self._playTime = self._playTime + self._pageData[position].endurance
-- end

function ActivityMagicTowerLevelLoading:RefreshAllItems()
    local count = self:_OnGetDialogeCount()
    for i = 1 ,count do
        if count <= DialogTextCount then
            local itemWidget = self._wtDialogText[i]
            itemWidget:Collapsed()
            local time = self._playTime
            local handle = Timer.DelayCall(
                time,
                function()
                    itemWidget:SelfHitTestInvisible()
                    itemWidget:InitData(self._pageData[i].content)
                    itemWidget:PlayAnim()
                    -- if not IsHD() then 
                        if i == count or i == DialogTextCount then 
                            self._wtContentScrollBox:ScrollToEnd()
                        else
                            self._wtContentScrollBox:ScrollWidgetIntoView(itemWidget,true, EDescendantScrollDestination.IntoView)
                        end
                    -- end
                    if i == #self._pageData then --播放到本页最后一个了
                        self._playTime = 0
                        if self._page == #self._dialogData then
                            --展示游戏界面
                            if self._notEnterPanel then
                                if self._CallBack then
                                    self:_CallBack()
                                end
                            else
                                Module.Activity:OpenMagicTowerPanel()
                            end
                            Facade.UIManager:CloseUI(self)
                            return
                        else
                            local refreshHangle = Timer.DelayCall(
                                self._pageData[i].endurance,
                                function()
                                    for k,v in pairs(self._wtDialogText) do 
                                        v:Collapsed()
                                    end
                                    self:RefreshAllItems()
                                end
                            )
                            table.insert(self._delayDelegate,refreshHangle)
                            self._page = self._page + 1
                            self._pageData = self._dialogData[self._page]
                        end
                    end
                end
            )
            table.insert(self._delayDelegate,handle)
            self._playTime = self._playTime + self._pageData[i].endurance
        else
            if self._notEnterPanel then
                if self._CallBack then
                    self:_CallBack()
                end
            else
                Module.Activity:OpenMagicTowerPanel()
            end
            Facade.UIManager:CloseUI(self)
            break
        end
    end
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerLevelLoading:_InitData()
    --常量ID写死对应,key对应几周目
    self._constId = {
        [1] = 
        {
            title = 3,
            desc  = 4,
            trans = 9,
        },
        [2] =
        {
            title = 5,
            desc  = 6,
            trans = 11,
        },
        [3] =
        {
            title = 7,
            desc  = 8, 
            trans = 13,
        },
        [4] = 
        {
            title = 3,
            desc  = 4,
            trans = 9,
        },
        [5] =
        {
            title = 5,
            desc  = 6,
            trans = 11,
        },
        [6] =
        {
            title = 7,
            desc  = 8, 
            trans = 13,
        }
    }
    -- 所有数据先从本地表获取
    self:_InitDataConfig()
    self._dialogData = {}
    self._page = 1
    self._playTime = 0
    for k,v in ipairs(self._cutSceneConfig) do
        if v.sceneID == (self._sceneID or 1) then
            if not self._dialogData[v.paragraphID] then 
                self._dialogData[v.paragraphID] = {}
            end
            table.insert(self._dialogData[v.paragraphID],v)
        end
    end
    self._pageData = self._dialogData[self._page]

    -- 如果是撤离前打开这个界面，就不播放音乐
    if not self._notShowDesc then        
        ConfigManager.PlayMusic(11)
        ConfigManager.PlayMusic(1)
        ConfigManager.PlayMusic(3)
    end
end


function ActivityMagicTowerLevelLoading:_InitDataConfig()
    -- 转场文字配置 ---@type table<int64, MorgenTowerCutSceneConfig>
    self._cutSceneConfig = ConfigManager.GetCutSceneConfigTable()
    -- 其它常量配置 ---@type table<int64, MorgenTowerConstantConfig>
    self._constantConfig = ConfigManager.GetConstantConfigTable()
end

function ActivityMagicTowerLevelLoading:_OnSpaceBtnClicked()
    if self._notEnterPanel then
        if self._CallBack then
            self:_CallBack()
        end
    else
        Module.Activity:OpenMagicTowerPanel(self._activityID)
    end
    Facade.UIManager:CloseUI(self)
end

return ActivityMagicTowerLevelLoading