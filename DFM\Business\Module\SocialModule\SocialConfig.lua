----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSocial)
----- LOG FUNCTION AUTO GENERATE END -----------




-- 好友列表上的子控件
UITable[UIName2ID.InviteFriendInfoSlot] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteFriendInfoSlot",
    BPKey = "WBP_TeamSystemInviteButton",
    Anim = {
        FlowInAni = "WBP_TeamSystemInviteButton_in",
        FlowOutAni = "WBP_TeamSystemInviteButton_out",
    }
}
-- 好友列表
UITable[UIName2ID.InviteFriendList] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteFriendList",
    BPKey = "WBP_TeamSystemInviteList",
    SubUIs = {
        UIName2ID.InviteFriendInfoSlot,
        UIName2ID.ChannelInviteButton,
        UIName2ID.CommonEmptyContent,
    },
    Anim = {
        FlowInAni = "Anim_in",
        FlowOutAni = "Anim_out",
    },
    -- azhengzheng:开启缓存，防止内存溢出
    ReConfig = {
        IsPoolEnable = true
    },
    IsModal = true
}
-- 邀请/申请弹窗 主容器
UITable[UIName2ID.InvitePopView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InvitePopView",
    BPKey = "WBP_TeamSystemInviteTip",
    SubUIs = {
        UIName2ID.InvitePopCommonItem,
    },
    Anim = {},
    IsModal = true,
}


UITable[UIName2ID.InvitePopCommonItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InvitePopCommonItem",
    BPKey = "WBP_TeamSystemInviteTip_Subitems",
    SubUIs = {
        UIName2ID.InvitePopCommonPlayerInfo
    },
    Anim = {
        FlowInAni = "WBP_TeamSystemInviteTip_in",
        FlowOutAni = "WBP_TeamSystemInviteTip_out",
    },
}

UITable[UIName2ID.InvitePopCommonPlayerInfo] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InvitePopCommonPlayerInfo",
    BPKey = "WBP_TeamSystemInviteTip_Info",
    SubUIs = {
        UIName2ID.InvitePopCommonTag
    },
    Anim = {
    },
}

UITable[UIName2ID.InvitePopCommonTag] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InvitePopCommonTag",
    BPKey = "WBP_TeamSystemInviteTip_Tags",
    SubUIs = {
    },
    Anim = {
    },
}

-- 下载完成弹窗
UITable[UIName2ID.TeamSystemDownloadTip] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.SocialModule.UI.TeamSystemDownloadTip",
    BPKey = "WBP_TeamSystemDownloadTip",
    Anim = {
        FlowInAni = "WBP_TeamSystemInviteTip_xiazai_in",
    },
}

UITable[UIName2ID.InviteMainPanelHD] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.SocialModule.UI.HD.InviteMainPanelHD",
    BPKey = "WBP_TeamSystemPrepareTip_PC",
    SubUIs = {
        UIName2ID.InvitePanelMem
    }
}

-- 邀请/申请弹窗-队伍人数
UITable[UIName2ID.InvitePanelMem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InvitePanelMem",
    BPKey = "WBP_TeamSystemMember"
}

-- 拒绝理由列表
UITable[UIName2ID.InviteRefuseTipList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteRefuseTipList",
    BPKey = "WBP_TeamSystemInviteTipList"
}

-- 拒绝理由
UITable[UIName2ID.InviteRefuseTip] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteRefuseTip",
    BPKey = "WBP_TeamSystemInviteTipListBox"
}

-- 邀请列表的Slot
UITable[UIName2ID.InviteSlot] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteSlot",
    BPKey = "WBP_TeamSystemInviteTipBox"
}

-- 面对面邀请
UITable[UIName2ID.InviteFaceToFacePanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteFaceToFacePanel",
    BPKey = "WBP_TeamSystemInviteCreateRoom"
}

-- 面对面邀请的玩家条
UITable[UIName2ID.InviteSimplePlayerInfo] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SocialModule.UI.InviteSimplePlayerInfo",
    BPKey = "WBP_TeamSystemInviteCreateRoomBox"
}

UITable[UIName2ID.TeamMainPanel_Gamepad] = {
    UILayer = EUILayer.Pop, 
    LuaPath = "DFM.Business.Module.SocialModule.UI.TeamMainPanel_Gamepad", 
    BPKey = "WBP_GamePadHall_SocialPanel",
    SubUIs = {
        UIName2ID.InputSummaryItemHD,
        UIName2ID.TeamMemberInfoItem_Gamepad,
    },
    Anim = {
        FlowInAni = "WBP_GamePadHall_SocialPanel_in",
        FlowOutAni = "WBP_GamePadHall_SocialPanel_out",
    },
    IsModal = true,
    LinkSubStage = ESubStage.MainFlowLobby
}

UITable[UIName2ID.TeamMemberInfoItem_Gamepad] = {
    UILayer = EUILayer.Sub, 
    LuaPath = "DFM.Business.Module.SocialModule.UI.TeamMemberInfoItem_Gamepad", 
    BPKey = "WBP_GamePadHall_SocialPanelItem",
    SubUIs = {
    },
}

--------------------------------------------------------------------------
---UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
local SocialConfig =
{
    Loc = {
        FunctionIsMakingTXT                 = NSLOCTEXT("SocialModule", "Lua_Social_FunctionIsMakingTXT",    "功能敬请期待"),
        NoTarget                            = NSLOCTEXT("SocialModule", "Lua_Social_NoTarget",               "无目标"),
        TeamInviteContent                   = NSLOCTEXT("SocialModule", "Lua_Social_TeamInviteContent",      "邀请您加入小队"),
        TeamApplyJoinContent                = NSLOCTEXT("SocialModule", "Lua_Social_TeamApplyJoinContent",   "申请加入小队"),
        RoomInviteContent                   = NSLOCTEXT("SocialModule", "Lua_Social_RoomInviteContent",      "邀请您加入队伍"),
        RoomInviteContentWithName            = NSLOCTEXT("SocialModule", "Lua_Social_RoomInviteContentWithName",      "<customstyle color=\"Color_Highlight01\">%s</> 邀请您加入队伍"),
        RoomInviteContentWithNameAndTarget  = NSLOCTEXT("SocialModule", "Lua_Social_RoomInviteContentWithNameAndTarget",      "<customstyle color=\"Color_Highlight01\">%s</> 邀请您加入队伍，目标：%s"),
        Inviting                            = NSLOCTEXT("SocialModule", "Lua_Social_Inviting",               "邀请中"),
        Applying                            = NSLOCTEXT("SocialModule", "Lua_Social_Applying",               "申请中"),
        AppointSuccess                            = NSLOCTEXT("SocialModule", "Lua_Social_AppointSuccess",               "已预约"),
        AppointFailed                            = NSLOCTEXT("SocialModule", "Lua_Social_AppointFailed",               "预约失败"),
        --玩家状态
        PlayerState_InMatch                 = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_InMatch",    "%s %d分钟"),
        PlayerState_InTeam                  = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_InTeam",     "组队中"),
        PlayerState_InRoom                  = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_InRoom",     "房间中"),
        PlayerState_Online                  = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_Online",     "在线"),
        PlayerState_Matching                = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_Matching",   "匹配中"),
        PlayerState_Offline                 = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_Offline",    "离线"),
        PlayerState_Face2FaceTeamRoom       = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_Face2FaceTeamRoom",    "面对面组队房间中"),
        PlayerState_PickHero                = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_PickHero",   "干员选择中"),
        PlayerState_WaitDS                = NSLOCTEXT("SocialModule", "Lua_Social_PlayerState_WaitDS",   "入局中"),
        --玩家关系
        --拒绝组队理由
        DefaultRefuseMsg                    = NSLOCTEXT("SocialModule", "Lua_Social_DefaultRefuseMsg",       "不好意思，现在不方便"),
        --面对面组队
        InputNum                            = NSLOCTEXT("SocialModule", "Lua_Social_InputNum",               "请输入4位数字"),
        NumLimit                            = NSLOCTEXT("SocialModule", "Lua_Social_NumLimit",               "最多只能输入四位数字"),
        OnlyCaptainCreateTeam               = NSLOCTEXT("SocialModule", "Lua_Social_OnlyCaptainCreateTeam",  "只有房主才有资格创建队伍"),
        NeedComplexNum                      = NSLOCTEXT("SocialModule", "Lua_Social_Need_Complex_Num",       "需输入复杂一点数字"),

        --邀请弹窗
        TeamInviteTitle                     = NSLOCTEXT("SocialModule", "Lua_Social_TeamInviteTitle",        "来自%s的邀请"),
        TeamApplyTitle                      = NSLOCTEXT("SocialModule", "Lua_Social_TeamApplyTitle",         "来自%s的申请"),
        TeamInviteMode_SOL01                = NSLOCTEXT("SocialModule", "Lua_Social_TeamInviteMode_SOL01",   "危险行动"),
        TeamInviteMode_SOL02                = NSLOCTEXT("SocialModule", "Lua_Social_TeamInviteMode_SOL02",   "特别行动"),
        TeamInviteMode_MP                   = NSLOCTEXT("SocialModule", "Lua_Social_TeamInviteMode_MP",      "全面战场"),
        TeamInviteMode_None                 = NSLOCTEXT("SocialModule", "Lua_Social_TeamInviteMode_None",    "无目标"),
        TeamSource = {
            [TeamInviteSource.FromAll] = NSLOCTEXT("SocialModule", "Lua_Social_FromAll",             "附近的人/好友"),
            [TeamInviteSource.FromFriend] = NSLOCTEXT("SocialModule", "Lua_Social_FromFriend",       "好友"),
            [TeamInviteSource.FromRecent] = NSLOCTEXT("SocialModule", "Lua_Social_FromRecent",       "最近"),
            [TeamInviteSource.FromNeighbor] = NSLOCTEXT("SocialModule", "Lua_Social_FromNeighbor",   "附近"),
            [TeamInviteSource.FromFace2Face] = NSLOCTEXT("SocialModule", "Lua_Social_FromFace2Face", "面对面组队"),
            [TeamInviteSource.FromWorldChat] = NSLOCTEXT("SocialModule", "Lua_Social_FromWorldChat", "世界频道"),
        },
        --其他
        TeamIsFull              = NSLOCTEXT("SocialModule", "Lua_Social_TeamIsFull",             "队伍满了，无法申请"),
        PlayerIsInTeam          = NSLOCTEXT("SocialModule", "Lua_Social_PlayerIsInTeam",         "对方已在队伍中"),
        SucceedInvite           = NSLOCTEXT("SocialModule", "Lua_Social_SucceedInvite",          "成功发出组队邀请"),
        SucceedApply            = NSLOCTEXT("SocialModule", "Lua_Social_Apply",                  "成功发出入队申请"),
        SucceedRoomInvite            = NSLOCTEXT("SocialModule", "Lua_Social_SucceedRoomInvite",                  "成功发出房间邀请"),
        BlackListPlayer         = NSLOCTEXT("SocialModule", "Lua_Social_BlackListPlayer",        "无法邀请黑名单玩家"),
        InMatching              = NSLOCTEXT("SocialModule", "Lua_Social_InMatching",             "匹配中，无法操作"),
        --二次弹窗文本
        ConfirmLeave            = NSLOCTEXT("SocialModule", "Lua_Social_ConfirmLeave",           "确认离开房间"),
        Confirm                 = NSLOCTEXT("SocialModule", "Lua_Social_Confirm",                "确认"),
        Cancel                  = NSLOCTEXT("SocialModule", "Lua_Social_Cancel",                 "取消"),
        --空背景文本
        EmptyALL                  = NSLOCTEXT("SocialModule", "Lua_Social_EmptyALL",             "没有玩家"),
        EmptyFriend               = NSLOCTEXT("SocialModule", "Lua_Social_EmptyFriend",          "没有好友"),
        EmptyNeighber             = NSLOCTEXT("SocialModule", "Lua_Social_EmptyNeighber",        "没有附近玩家"),
        EmptyRecent               = NSLOCTEXT("SocialModule", "Lua_Social_EmptyRecent",          "没有最近玩家"),
        MapNotDownload          = NSLOCTEXT("SocialModule", "Lua_Social_MapNotDownload",         "(地图未下载)"),
        MapDownloaded           = NSLOCTEXT("SocialModule", "Lua_Social_MapDownloaded",          "%s地图已成功下载"),
        TeammateDownloaded      = NSLOCTEXT("SocialModule", "Lua_Social_TeammateDownloaded",     "<customstyle color=\"Color_Highlight01\">{playerName}</>已完成<customstyle color=\"Color_Highlight01\">{mapName}</>下载"),
        ChoosedMap              = NSLOCTEXT("SocialModule", "Lua_Social_ChoosedMap",             "队长已选择%s地图"),
        Member                  = NSLOCTEXT("SocialModule", "Lua_Social_Member",                 "队友"),
        Captain                 = NSLOCTEXT("SocialModule", "Lua_Social_Captain",                "队长"),
        MapWithSizeMB           = NSLOCTEXT("SocialModule", "Lua_Social_MapWithSizeMB",          "%s（%.1f/%.1fMB）"),
        MapWithSizeGB           = NSLOCTEXT("SocialModule", "Lua_Social_MapWithSizeGB",          "%s（%.1f/%.1fGB）"),
        DownloadNetworkState    = NSLOCTEXT("SocialModule", "Lua_Social_DownloadNetworkState",   "选择的地图未下载，当前处于%s，是否立即下载？"),
        DownloadTipForPC        = NSLOCTEXT("SocialModule", "Lua_Social_DownloadTipForPC",       "对应地图未下载，是否立即下载？"),
        DownloadCenterClosedPC = NSLOCTEXT("SocialModule", "Lua_Social_DownloadCenterClosedPC","资源缺失，请重启游戏修复"),
        NetworkStateWIFI        = NSLOCTEXT("SocialModule", "Lua_Social_NetworkStateWIFI",       "WIFI环境"),
        NetworkStateMobile      = NSLOCTEXT("SocialModule", "Lua_Social_NetworkStateMobile",     "移动网络"),
        MapsNotInMatch          = NSLOCTEXT("SocialModule", "Lua_Social_MapsNotInMatch",         "检测到<customstyle color=\"Color_Highlight01\">%s</>未下载，您将不会被匹配到对应地图"),
        TeamMapsNotInMatch      = NSLOCTEXT("SocialModule", "Lua_Social_TeamMapsNotInMatch",     "检测到您的队伍<customstyle color=\"Color_Highlight01\">%s</>未下载，您的队伍将不会被匹配到对应地图"),
        SymbolCaesura           = NSLOCTEXT("SocialModule", "Lua_Social_SymbolCaesura",          "、"),

        CannotInviteBlack = NSLOCTEXT("SocialModule", "Lua_Social_CannotInviteBlack", "无法向黑名单玩家发起邀请"),
        CannotApplyBlack = NSLOCTEXT("SocialModule", "Lua_Social_CannotApplyBlack", "无法向黑名单玩家发起申请"),
        
        RoomCode = NSLOCTEXT("SocialModule", "Lua_Social_RoomCode", "搜索结果：房间号%s"),

        CreateTeam = NSLOCTEXT("SocialModule", "Lua_Social_CreateTeam", "创建队伍"),
        WaitLeaderCreateTeam = NSLOCTEXT("SocialModule", "Lua_Social_WaitLeaderCreateTeam", "等待房主创建队伍"),

        SeasonLevel = NSLOCTEXT("SocialModule", "Lua_Social_SeasonLevel", "行动等级：%s"),
        Refuse = NSLOCTEXT("SocialModule", "Lua_Social_Refuse", "拒绝(%ds)"),

        MultipleMap = NSLOCTEXT("SocialModule", "Lua_Social_MultipleMap", "随机({MapNum}张)"),
        EnsureText = NSLOCTEXT("SocialModule", "Lua_Social_EnsureText", "知道了（%d）"),

        NoRankDivision = NSLOCTEXT("SocialModule", "Lua_Social_NoRankDivision", "无段位"),

        NewInviteAwaiting = NSLOCTEXT("SocialModule", "Lua_Social_NewInviteAwaiting", "待处理：%s"),
        InviteTitleInvite = NSLOCTEXT("SocialModule", "Lua_Social_InviteTitleInvite", "组队邀请"),
        InviteTitleRecommendInvite = NSLOCTEXT("SocialModule", "Lua_Social_InviteTitleRecommendInvite", "组队推荐"),
        InviteTitleRecommendAddFriend = NSLOCTEXT("SocialModule", "Lua_Social_InviteTitleRecommendAddFriend", "上局队友推荐"),
        InviteTitleRoomInvite = NSLOCTEXT("SocialModule", "Lua_Social_InviteTitleRoomInvite", "自定义房间邀请"),
        InviteTitleApplyJoin = NSLOCTEXT("SocialModule", "Lua_Social_InviteTitleApplyJoin", "入队申请"),

        Ignore = NSLOCTEXT("SocialModule", "Lua_Social_Ignore", "忽略"),
        AddAll = NSLOCTEXT("SocialModule", "Lua_Social_AddAll", "全部添加"),
        BHDNotInstalledTips = NSLOCTEXT("SocialModule", "Lua_Social_BHDNotInstalledTips", "请先下载黑鹰坠落DLC"),
        SOLNotInstalledTips = NSLOCTEXT("SocialModule", "Lua_Social_SOLNotInstalledTips", "游玩<customstyle color=\"Color_Highlight01\">烽火地带</>模式需要下载额外资源，是否确认下载？"),
        MPNotInstalledTips = NSLOCTEXT("SocialModule", "Lua_Social_MPNotInstalledTips", "游玩<customstyle color=\"Color_Highlight01\">全面战场</>模式需要下载额外资源，是否确认下载？"),
        SOLNotInstalledDownloadTips = NSLOCTEXT("SocialModule", "Lua_Social_SOLNotInstalledDownloadTips", "资源大小{ResourceSize}"),
        ResourceDownloadingTips = NSLOCTEXT("SocialModule", "Lua_Social_ResourceDownloadingTips", "资源尚在下载中"),
        NotInvite = NSLOCTEXT("SocialModule", "Lua_Social_NotInvite", "未邀请"),
    },

    Events = {
        evtOnPlayerIdCoolDownTimeRefresh = LuaEvent:NewIns("evtOnPlayerIdCoolDownTimeRefresh"),
        evtOnNtfWindowTimeChanged = LuaEvent:NewIns("evtOnNtfWindowTimeChanged"),
        evtOnInvitePopViewOnShowBegin = LuaEvent:NewIns("evtOnInvitePopViewOnShowBegin"),
    },

    EPanelSource = {
        PreParation = 0,
        Team = 1,
        Room = 2,
    },

    InviteTextColor={
        Normal=Facade.ColorManager:GetSlateColor("Basic_White(50%)"),
        Warn=Facade.ColorManager:GetSlateColor("Basic_White(50%)"),

    },

    FreindListTabInfo = {
        {
            title = NSLOCTEXT("SocialModule", "Lua_Social_FriendList_All",    "全部"),
            imgPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'"
        },
        {
            title = NSLOCTEXT("SocialModule", "Lua_Social_FriendList_Friend",    "好友"),
            imgPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0007.Common_ItemProp_Icon_0007'"
        },
        {
            title = NSLOCTEXT("SocialModule", "Lua_Social_FriendList_Nearby",    "最近"),
            imgPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'"
        },
        {
            title = NSLOCTEXT("SocialModule", "Lua_Social_FriendList_Recently",    "附近"),
            imgPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Marker_Character_Icon_0802.Common_Marker_Character_Icon_0802'"
        },
    },
    TeamSourceToIcon = {
        [TeamInviteSource.FromAll] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'",
        [TeamInviteSource.FromFriend] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0007.Common_ItemProp_Icon_0007'",
        [TeamInviteSource.FromRecent] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'",
        [TeamInviteSource.FromNeighbor] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Marker_Character_Icon_0802.Common_Marker_Character_Icon_0802'",
        [TeamInviteSource.FromFace2Face] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'",
        [TeamInviteSource.FromWorldChat] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'",
    },
    EPlayerState2SteamDisplay = {
        [GlobalPlayerStateEnums.EPlayerState_InMatch]             = "#InGame",
        [GlobalPlayerStateEnums.EPlayerState_InTeam]              = "#InTeam",
        [GlobalPlayerStateEnums.EPlayerState_InRoom]              = "#InRoom",
        [GlobalPlayerStateEnums.EPlayerState_Online]              = "#Online",
        [GlobalPlayerStateEnums.EPlayerState_Matching]            = "#Matching",
        [GlobalPlayerStateEnums.EPlayerState_PickHeroStage]       = "#PickHero",
    },
    --邀请弹窗15s后自动关闭
    DEFAULT_NTF_REMAIN_COLD_TIME = 15,
    --发出邀请的CD
    DEFAULT_COLD_TIME = 15,
    --300秒内忽略特定玩家请求
    DEFAULT_INGORE_TIME = 300,
    -- azhengzheng:预约CD
    DEFAULT_APPOINT_COLD_TIME = 60,
    EMapDownloadType = {
        Downloaded=0,
        NotDownload=1,
    },
    EInvitePopType = {
        Invite = 0,
        Apply = 1,
        RecommendInvite = 2,
        RecommendAddFriend = 3,
    },
}

return SocialConfig
