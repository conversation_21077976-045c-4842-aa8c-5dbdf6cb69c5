----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestDetailOpenPanel : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local QuestDetailOpenPanel = ui("QuestDetailOpenPanel", RewardBaseView)
local EGPInputModeType = import "EGPInputModeType"

function QuestDetailOpenPanel:Ctor()
    self._wtRewardBgPanel = self:Wnd("wtRewardBgPanel", UIWidgetBase)
    self._wtRewardBgPanel:BindCloseCallback(self._OnSkipBtnClick, self)
    self._wtRewardBgPanel:SelfHitTestInvisible()
    self._wtDFScrollBox_Unlock = self:Wnd("DFScrollBox_Unlock", UIWidgetBase)

    self._closeClickCount = -1
    self._lineIdToQuestInfos = {}
    self._seasonQuestList = {}
end

function QuestDetailOpenPanel:OnInitExtraData(title, fCloseCb, questInfos)
    self._title = title or ""

    self._fCallback = fCloseCb
    self._questInfos = questInfos

    if self._questInfos then
        self._lineIdToQuestInfos = {}
        for idx, questInfo in ipairs(self._questInfos) do
            if questInfo.type == QuestType.SeasonQuest then
                table.insert(self._seasonQuestList, questInfo)
            else
                local lineInfo = Server.QuestServer:GetQuestLineInfoByQuest(questInfo)
                if lineInfo then
                    local lineId = lineInfo.questLineId
                    if self._lineIdToQuestInfos[lineId] == nil then
                        self._lineIdToQuestInfos[lineId] = {}
                    end 
                    table.insert(self._lineIdToQuestInfos[lineId], questInfo)
                end
            end
        end
    end
end

function QuestDetailOpenPanel:OnOpen()
    self:AddListeners()
    self._wtRewardBgPanel:SetTitle(self._title)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtDFScrollBox_Unlock)
    for key, value in pairs(self._lineIdToQuestInfos) do
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.QuestUnlockLinePanel, self._wtDFScrollBox_Unlock, nil)
        local tabWidget = getfromweak(weakUIIns)
        tabWidget:_UpdateQuestInfo(Server.QuestServer:GetQuestLineInfoById(key), value, false)
    end
    if #self._seasonQuestList > 0 then
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.QuestUnlockLinePanel, self._wtDFScrollBox_Unlock, nil)
        local tabWidget = getfromweak(weakUIIns)
        tabWidget:_UpdateQuestInfo(nil, self._seasonQuestList, true)
    end
end


function QuestDetailOpenPanel:OnShowBegin()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    Module.ItemDetail:CloseAllPopUI()
    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_RewardPanel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
    Facade.SoundManager:PlayUIAudioEvent("UI_Common_Popup")
    self:HandleTransition(false)
end


function QuestDetailOpenPanel:OnClose()
    if self._hActionSkip then
        self:RemoveInputActionBinding(self._hActionSkip)
        self._hActionSkip = nil
    end
    Facade.UIManager:ClearSubUIByParent(self, self._wtDFScrollBox_Unlock)
    if self._fCallback ~= nil and type(self._fCallback) == GlobalStr.FUNCTION then
        self._fCallback()
    end
    Server.QuestServer.Events.evtShowNewQuestInfo:Invoke()
    self._fCallback = nil
    self:RemoveAllLuaEvent()
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function QuestDetailOpenPanel:OnShow()
    if IsHD() and not self._hActionSkip then
        self._hActionSkip =
            self:AddInputActionBinding(
            "JumpOver",
            EInputEvent.IE_Pressed,
            self._OnSkipBtnClick,
            self,
            EDisplayInputActionPriority.UI_Pop
        )
    end
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)
end

function QuestDetailOpenPanel:OnAnimFinished(anim)
    if anim == self.WBP_RewardPanel_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_RewardPanel_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end

function QuestDetailOpenPanel:AddListeners()
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
end

function QuestDetailOpenPanel:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
        Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
        self:PlayAnimation(self.WBP_RewardPanel_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_RewardPanel_in)
    end
end

function QuestDetailOpenPanel:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end


function QuestDetailOpenPanel:_OnTabPressed()
    self._bTabPressed = true
    self:_OnSkipBtnClick()
end

return QuestDetailOpenPanel
