----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------



local MandelBrickItemviewList = ui("MandelBrickItemviewList")
local MarketConfig = require "DFM.Business.Module.MarketModule.MarketConfig"
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local IVShopItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate"
local FAnchors = import "Anchors"

function MandelBrickItemviewList:Ctor()
    self._wtOutputText = self:Wnd("DFTextBlock_29", UITextBlock)
    self._wtOutputText:SelfHitTestInvisible()
    self._wtItemViewVerticalBox = self:Wnd("DFVerticalBox_70", UIWidgetBase)
    self._wtItemView = self:Wnd("WBP_ShopItemTemplate", IVShopItemTemplate)
end

function MandelBrickItemviewList:OnInitExtraData()
    
end

function MandelBrickItemviewList:OnShow()

end

function MandelBrickItemviewList:OnHide()

end

function MandelBrickItemviewList:OnOpen()
    
end

function MandelBrickItemviewList:OnClose()
    
end

function MandelBrickItemviewList:Init(typeText, itemId, saleInfo, itemWidgetClickedCallback)
    if self._wtOutputText then
        if typeText then
            self._wtOutputText:SelfHitTestInvisible()
            self._wtOutputText:SetText(typeText)
        else
            self._wtOutputText:Collapsed()
        end
    end
    if self._wtItemView and saleInfo then
        local item = ItemBase:NewIns(itemId)
        local itemType = Server.MarketServer:GetMarketItemType(itemId)
        if itemType == EMarketSubPageType.MysticalSkin then
            self._wtItemView:InitMarketItem(item, saleInfo)
            self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, true)
        elseif itemType == EMarketSubPageType.MysticalPendant then
            self._wtItemView:InitMarketItem(item, saleInfo)
            self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, true)
        elseif itemType == EMarketSubPageType.MandelBrick then
            self._wtItemView:InitMarketItem(item, saleInfo)
            self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
            self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, false)
            local iconComp = self._wtItemView:GetComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.ItemIcon)
            if iconComp and saleInfo.sprite_name then
                iconComp:SetIconFromPath(saleInfo.sprite_name)
                local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(iconComp)
                local bottomAnchor = FAnchors()
                bottomAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
                bottomAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
                canvasSlot:SetAnchors(bottomAnchor)
                canvasSlot:SetOffsets(FMargin(0, 0, 0, 0))
                canvasSlot:SetAlignment(FVector2D(0, 0))
                iconComp:SetIconOffset(FMargin(0, 0, 0, 0))
                iconComp:SetScaleBoxStretch(EStretch.ScaleToFill)
            end
            local mysticalSkinName = self._wtItemView:GetComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.TopLeftNewTag)
            if mysticalSkinName then
                mysticalSkinName:RefreshComponent()
                mysticalSkinName:SetQualityColor(FLinearColor("#0D0F1099"))
            end
        elseif itemType == EMarketSubPageType.MeleeWeapon then
            self._wtItemView:InitMarketItem(item, saleInfo)
            self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
            self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, false)
        end
        self._wtItemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
        local OnWidgetClicked = function()
            if itemWidgetClickedCallback then
                itemWidgetClickedCallback(itemId)
            end
        end
        self._wtItemView:BindClickCallback(OnWidgetClicked)
        self._wtItemView:SelfHitTestInvisible()
    end
end

function MandelBrickItemviewList:SetSelected(...)
    if self._wtItemView then
        self._wtItemView:SetSelected(...)
    end
end

return MandelBrickItemviewList