----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

local StoreConfig = Module.Store.Config

---@class NightMarketCardItem : LuaUIBaseView
local NightMarketCardItem = ui("NightMarketCardItem")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"

function NightMarketCardItem:Ctor()
    self._cardCanvas = self:Wnd("DFCanvasPanel_56", UIWidgetBase)
    self._cardImg = self:Wnd("DFCardIcon", UIImage)

    self._priceContainer = self:Wnd("DFCanvasPosReContainer_Origin", UIWidgetBase)
    self._discountRoot = self:Wnd("wtDiscountRoot", UIWidgetBase)
    self._discount = self:Wnd("wtTextDiscount", UITextBlock)
    self._price = self:Wnd("DFTextBlock_6", UITextBlock)
    self._prePrice = self:Wnd("wtTextPriceOrign", UITextBlock)
    self._goodName = self:Wnd("DFTextBlock_41", UITextBlock)

    self._freeContainer = self:Wnd("DFCanvasPosReContainer_Free", UIWidgetBase)
    self._freeItemText1 = self:Wnd("DFTextBlock", UITextBlock)
    self._freeItemText2 = self:Wnd("DFTextBlock_1", UITextBlock)

    self._dfBtn = self:Wnd("DFBtn", UIButton)
    self._dfBtn:Event("OnClicked", self.ShowBuyPop, self)

    self._wtDe02 = self:Wnd("DFCanvas_De_02", UIWidgetBase)
end

function NightMarketCardItem:OnShowBegin()
end

function NightMarketCardItem:SetData(goods, Opacity, needCheckOwned)
    self.goods = goods

    self._cardCanvas:SetRenderOpacity(Opacity)
    if Opacity == 1 then
        self._wtDe02:Collapsed()
    end

    if not goods.prop then
        return
    end

    self._goodName:SetText(LocalizeTool.GetTransStr("LOC_GameItem," .. tostring(goods.prop.id) .. "_Name"))
    self._cardImg:AsyncSetImagePath(goods.cardBg, false)

    local price = goods.dis_price or 1
    local pricePreDis = goods.price or 1
    local discount = 0
    if price ~= 0 then
        self._priceContainer:Visible()
        self._freeContainer:Collapsed()

        discount = math.floor((pricePreDis - price) / pricePreDis * 100 + 0.5) --折扣
        if pricePreDis - price ~= 0 then
            self._discountRoot:Visible()
            self._discount:Visible()
            self._prePrice:Visible()
            self._discount:SetText(tostring(discount) .. "%")
        else
            self._discountRoot:Collapsed()
            self._prePrice:Collapsed()
        end
        local priceStr = string.format(StoreConfig.Loc.ItemPriceNormal,
                Module.Currency:GetRichTxtImgByItemId(goods.currency_type),
                MathUtil.GetNumberFormatStr(price))

        self._price:SetText(priceStr)
        self._prePrice:SetText(tostring(pricePreDis))
    else
        self._priceContainer:Collapsed()
        self._freeContainer:Visible()

        if #goods.other_props > 0 then
            local priceStr1 = string.format(StoreConfig.Loc.ItemPriceNormal,
                Module.Currency:GetRichTxtImgByItemId(16110000020),
                MathUtil.GetNumberFormatStr(goods.prop.num))
            
            local priceStr2 = string.format(StoreConfig.Loc.ItemPriceNormal,
                Module.Currency:GetRichTxtImgByItemId(32320000001),
                MathUtil.GetNumberFormatStr(goods.prop.num))
            
            self._freeItemText1:SetText(priceStr1)
            self._freeItemText2:SetText(priceStr2)

            self._freeItemText2:Visible()
        else
            local priceStr = string.format(StoreConfig.Loc.ItemPriceNormal,
                Module.Currency:GetRichTxtImgByItemId(goods.prop.id),
                MathUtil.GetNumberFormatStr(goods.prop.num))
            
            self._freeItemText1:SetText(priceStr)
            self._freeItemText2:Collapsed()
        end
    end

    if needCheckOwned then
        self:SetCardOwned()
    end
end

function NightMarketCardItem:ShowCard()
    self._cardCanvas:SetRenderOpacity(1)
end

function NightMarketCardItem:ShowBuyPop()
    Module.Store:SetCanPlayMusic(false)
    if Server.StoreServer:GetNMFreeBuff() then
        Facade.UIManager:AsyncShowUI(UIName2ID.StoreNMRewardPop, nil, nil)
    elseif self.goods.dis_price == 0 then
        Facade.UIManager:AsyncShowUI(UIName2ID.StoreNMGiftPop, nil, nil, self.goods)
    else
        local shopData = self:GetShopData()
        Facade.UIManager:AsyncShowUI(UIName2ID.StoreProductPreviewMarket, nil, nil, shopData, true)
    end
end

function NightMarketCardItem:SetCardFree()
    self._priceContainer:Visible()
    self._freeContainer:Visible()
end

function NightMarketCardItem:SetCardOwned()
    if Server.CollectionServer:IsOwnedWeaponSkin(self.goods.prop.id, self.goods.prop.gid) or Server.CollectionServer:IsOwnedMeleeSkin(self.goods.prop.id) then
        self._price:SetText(LocalizeTool.GetTransStr(StoreConfig.Loc.Owned))
        self._freeContainer:Collapsed()
        self._priceContainer:Visible()
        self._discountRoot:Collapsed()
        self._prePrice:Collapsed()
        self._priceContainer:SetRenderOpacity(1)
    end
end

function NightMarketCardItem:SetClickDis()
    self._dfBtn:Collapsed()
end

function NightMarketCardItem:GetShopData()
    local shopData = {}
    self.goods.id = self.goods.prop.id
    shopData.GoodsItems = {}
    shopData.GoodsItems[1] = self.goods
    shopData.IsDiscountIntensity = self.goods.IsDiscountIntensity
    shopData.Price = self.goods.dis_price
    shopData.PricePreDis = self.goods.price
    shopData.CurrencyType = self.goods.currency_type
    shopData.ImageSourceLogo = self.goods.cardBg
    -- shopData.ShopName = LocalizeTool.GetTransStr("LOC_GameItem," .. tostring(self.goods.prop.id) .. "_Name")
    return shopData
end

function NightMarketCardItem:OnAnimFinished(anim)
    if anim == self.Anim_Card_In then
        Module.Store.Config.evtStoreNMCardShowAnimPlay:Invoke()
    elseif anim == self.Anim_Card_Level1 or anim == self.Anim_Card_Level2 then
        Module.Store.Config.evtStoreNMCardSearchAnimPlay:Invoke()
    elseif anim == self.Anim_Card_RefreshPrize then
        self:SetCardOwned()
    end
end

return NightMarketCardItem
