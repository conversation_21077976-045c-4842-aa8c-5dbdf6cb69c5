----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local MPWeaponUpItem = ui("MPWeaponUpItem")

local MPWeaponLevelPartsTable = Facade.TableManager:GetTable("WeaponSystem/MPWeaponLevelParts")
-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION


function MPWeaponUpItem:Ctor()
    self._curIdx = 1

    self._wtCarouselBtnHB = self:Wnd("wtCarouselBtnHB", UIWidgetBase)

    self._wtWeaponNameTB = self:Wnd("wtWeaponNameTB", UITextBlock)

    self._wtCurWeaponLevelTB = self:Wnd("wtCurWeaponLevelTB", UITextBlock)
    self._wtArrowImg = self:Wnd("wtArrowImg", UIImage)
    self._wtEndWeaponLevelTB = self:Wnd("wtEndWeaponLevelTB", UITextBlock)
    self._wtCurAddExpTB = self:Wnd("wtCurAddExpTB", UITextBlock)
    self._wtExpPBImg = self:Wnd("wtExpPBImg", UIImage)
    self._wtExpPBLine = self:Wnd("wtExpPBLine", UIImage)
    self._wtExpPBTB = self:Wnd("wtExpPBTB", UITextBlock)

    self._wtWeaponListWSV = UIUtil.WndWaterfallScrollBox(self, "wtWeaponListWSV", self._OnGetWeaponCount, self._OnProcessWeaponWidget)
    self._wtWeaponListWSV.OnScrolling:Add(self._OnScrolling, self)
    self._wtWeaponListWSV.OnScrollEnd:Add(self._OnScrollEnd, self)

    self._wtComponetsSGB = UIUtil.WndScrollGridBox(self, "wtComponetsSGB", self._OnGetComponetsCount, self._OnProcessComponetsWidget)

    -- azhengzheng:long3武器经验卡
    self._wtExpOverflowTipWB = self:Wnd("wtExpOverflowTipWB", UIWidgetBase)
    self._wtExpOverflowTipTA = UIUtil.WndTipsAnchor(self, "wtExpOverflowTipTA", self._OnOpenExpOverflowTips, self._OnCloseExpOverflowTips)

    self._wtLeftBtn = self:Wnd("DFButton_195", UIButton)
    self._wtRighBtn = self:Wnd("DFButton"    , UIButton)
    self._wtLeftBtn:Collapsed()
    self._wtRighBtn:Collapsed()

    if IsHD() then
        self._length = 2914
        -- self._jumpHandle = self:AddInputActionBinding("JumpOver", EInputEvent.IE_Pressed, self._JumpToNextStep, self, EDisplayInputActionPriority.UI_Pop)
    else
        self._length = 2108
    end
    --杀人星级
    self._wtSharkIcon = self:Wnd("DFImage_95", UIImage)
    self._wtSharkIcon:SetVisibility(ESlateVisibility.Collapsed)
end

function MPWeaponUpItem:OnInitExtraData(weaponsInfo)
    self._weaponsInfo = weaponsInfo
end

function MPWeaponUpItem:OnOpen()
    self:_InitCarouselBtn()

    self:_InitItemBase()

    self._wtWeaponListWSV:RefreshAllItems()

    self:_InitWeaponsInfoTable()

    self:_InitWeaponsInfo()

    self:_StartShowWeapons()
end

function MPWeaponUpItem:OnShow()
    Module.Settlement.Config.Events.evtWeaponExpShow:Invoke()
    --BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        -- 禁用D-pad键导航和默认A键
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
    end
    --- END MODIFICATION
end

--BEGIN MODIFICATION @ VIRTUOS
function MPWeaponUpItem:OnHide()
    if IsHD() then
        -- 还原导航配置
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
end
--- END MODIFICATION

function MPWeaponUpItem:OnClose()
    if IsHD() then
        -- self:RemoveInputActionBinding(self._jumpHandle)
    end

    Facade.UIManager:ClearSubUIByParent(self, self._wtCarouselBtnHB)

    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)

    Module.Settlement:OpenSettlementUI(self.UINavID)

    --调详情页接口:关闭all的动态详情页,解决关闭详情页还存在问题
    Module.ItemDetail:CloseItemDetailPanel()
end

function MPWeaponUpItem:_InitCarouselBtn()
    if #self._weaponsInfo < 2 then
        self._wtCarouselBtnHB:Collapsed()
    else
        self._wtCarouselBtnList = {}
        Facade.UIManager:RemoveSubUIByParent(self, self._wtCarouselBtnHB)

        for key = 1, #self._weaponsInfo do
            local uiIns = Facade.UIManager:AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtCarouselBtnHB)
            table.insert(self._wtCarouselBtnList, uiIns)
        end
    end
end

function MPWeaponUpItem:_InitWeaponsInfoTable()
    self._weaponsInfoTable = {}

    if not MPWeaponLevelPartsTable then
        return
    end

    for _, value in pairs(MPWeaponLevelPartsTable) do
        if not self._weaponsInfoTable[value.RecFunctionId] then
            self._weaponsInfoTable[value.RecFunctionId] = {}
        end

        table.insert(self._weaponsInfoTable[value.RecFunctionId], {Level = value.Level, Exp = value.Exp, ExpSum = value.ExpSum})
    end

    for _, value in pairs(self._weaponsInfoTable) do
        table.sort(
            value,
            function(a, b)
                return a.Level < b.Level
            end
        )
    end
end

function MPWeaponUpItem:_InitWeaponsInfo()
    for _, value in ipairs(self._weaponsInfo) do
        value.curLevel, value.curLevelRemainExp, value.curLevelNeedExp = self:_CalculateWeaponMsg(value.ID, value.old_exp)
        value.startLevel = value.curLevel

        value.endLevel, value.endLevelRemainExp, value.endLevelNeedExp = self:_CalculateWeaponMsg(value.ID, value.exp)

        value.totalExp = value.exp - value.old_exp
    end
end

function MPWeaponUpItem:_CalculateWeaponMsg(id, exp)
    id = tostring(id)

    if not self._weaponsInfoTable[id] then
        logerror("azhengzheng:please check weapon " .. id .. " table info is not exit!")
        return 0, 0, 0
    end

    for _, value in pairs(self._weaponsInfoTable[id]) do
        if value.Exp == 0 or exp < value.Exp + value.ExpSum then
            return value.Level, exp - value.ExpSum, value.Exp
        end
    end

    logerror("azhengzheng:please check weapon " .. id .. " table info is legal!")
    return 0, 0, 0
end

function MPWeaponUpItem:_InitItemBase()
    for _, value in ipairs(self._weaponsInfo) do
        local items = Server.InventoryServer:GetItemsById(value.ID, ESlotGroup.MPApply)

        if items then
            value.itemBase = items[1]
        end

        value.componentsItemBase = {}

        for _, component in ipairs(value.unlock_componets_prop) do
            table.insert(value.componentsItemBase, ItemBase:New(component.id, 1))
        end

        if value.converted_expcard and #value.converted_expcard ~= 0 then
            value.expOverflowTips = {}
            table.insert(value.expOverflowTips, {textContent = Module.Settlement.Config.Loc.ExpOverflowIntoCard, styleRowId = "C000"})

            for _, expCard in pairs(value.converted_expcard) do
                local itemBaseIns = ItemBase:New(expCard.id, expCard.num)

                if itemBaseIns then
                    table.insert(value.componentsItemBase, itemBaseIns)
                    table.insert(value.expOverflowTips, {textContent = StringUtil.Key2StrFormat(Module.Settlement.Config.Loc.ItemNameWithNum, {["itemName"] = itemBaseIns.name, ["num"] = expCard.num}), styleRowId = "C000"})
                end
            end
        end
    end
end

function MPWeaponUpItem:_OnGetWeaponCount()
    return self._weaponsInfo and #self._weaponsInfo or 0
end

function MPWeaponUpItem:_OnProcessWeaponWidget(idx, widget)
    if self._weaponsInfo[idx].itemBase then
        local wtIconImg = widget:Wnd("wtWeaponIconWB", UIWidgetBase):Wnd("wtMainIcon", UIImage)

        if wtIconImg then
            RuntimeIconTool.SetItemIcon(self._weaponsInfo[idx].itemBase, wtIconImg)
        end
    end
end

function MPWeaponUpItem:_OnScrolling()
    self:_CheckAllAnimIsFinish()
end

function MPWeaponUpItem:_OnScrollEnd()
    local curIdx = math.round(self._wtWeaponListWSV:GetScrollOffset() / 2920) + 1

    if self._curIdx ~= curIdx then
        self._curIdx = #self._weaponsInfo < curIdx and #self._weaponsInfo or curIdx

        self:_RefreshView()
    end

    self._wtWeaponListWSV:ScrollToIndex(self._curIdx)
end

function MPWeaponUpItem:_StartShowWeapons()
    if #self._weaponsInfo ~= 1 then
        self._weaponsAnimTimerHandle = Timer:NewIns(3, 0)
        self._weaponsAnimTimerHandle:AddListener(self._ShowWeaponsByTimer, self)
        self._weaponsAnimTimerHandle:Start()
    end

    self:_RefreshView()
end

function MPWeaponUpItem:_ShowWeaponsByTimer()
    self._wtWeaponListWSV:ScrollToIndex(self._curIdx + 1)

    if self._curIdx == #self._weaponsInfo then
        self:_ReleaseShowWeaponsTimer()
    end
end

function MPWeaponUpItem:_RefreshView()
    self:_SetUpLevelState()

    self:_RefreshCarouselBtn()

    self:_RefreshStaticMsg()

    if self._weaponsAnimTimerHandle or not self._notFirstIn then
        self._notFirstIn = true

        self:_StartExpAnim()
    else
        self:_RefreshFinalExp()
    end
end

function MPWeaponUpItem:_RefreshCarouselBtn()
    if self._wtCarouselBtnList then
        for key, value in ipairs(self._wtCarouselBtnList) do
            value:SetIsEnabledStyle(key ~= self._curIdx)
        end
    end
end

function MPWeaponUpItem:_RefreshStaticMsg()
    self._wtComponetsSGB:RefreshAllItems()

    self._wtCurWeaponLevelTB:SetText(string.format(Module.Settlement.Config.Loc.WeaponLevel, self._weaponsInfo[self._curIdx] and self._weaponsInfo[self._curIdx].startLevel or "-"))
    self._wtEndWeaponLevelTB:SetText(self._weaponsInfo[self._curIdx] and self._weaponsInfo[self._curIdx].endLevel or "-")
    self._wtWeaponNameTB:SetText(self._weaponsInfo[self._curIdx].itemBase and self._weaponsInfo[self._curIdx].itemBase.name or "-")

    if self._weaponsInfo[self._curIdx].expOverflowTips and #self._weaponsInfo[self._curIdx].expOverflowTips > 1 then
        self._wtExpOverflowTipWB:Visible()
    else
        self._wtExpOverflowTipWB:Collapsed()
    end
end

function MPWeaponUpItem:_StartExpAnim()
    self._curAddExp = 0
    self._singleAddExp = math.ceil(self._weaponsInfo[self._curIdx].totalExp / 80)
    self._singlePBAddExp = math.ceil((self._weaponsInfo[self._curIdx].curLevelNeedExp - self._weaponsInfo[self._curIdx].curLevelRemainExp + (self._weaponsInfo[self._curIdx].endLevelNeedExp == 0 and 0 or self._weaponsInfo[self._curIdx].endLevelRemainExp)) / 80)

    self._expAnimTimerHandle = Timer:NewIns(0.025, 0)
    self._expAnimTimerHandle:AddListener(self._RefreshExpByTimer, self)
    self._expAnimTimerHandle:Start()

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
end

function MPWeaponUpItem:_RefreshExpByTimer()
    self._curAddExp = self._curAddExp + self._singleAddExp
    self._weaponsInfo[self._curIdx].curLevelRemainExp = self._weaponsInfo[self._curIdx].curLevelRemainExp + self._singlePBAddExp

    if self._weaponsInfo[self._curIdx].totalExp <= self._curAddExp then
        self:_ReleaseExpAnimTimer()
        return
    end

    if self._weaponsInfo[self._curIdx].curLevel ~= self._weaponsInfo[self._curIdx].endLevel then
        if self._weaponsInfo[self._curIdx].curLevelNeedExp <= self._weaponsInfo[self._curIdx].curLevelRemainExp then
            self:_SetUpLevelState(true)

            if self._weaponsInfo[self._curIdx].endLevelNeedExp == 0 then
                self:_ReleaseExpAnimTimer()
                return
            end

            self._weaponsInfo[self._curIdx].curLevel = self._weaponsInfo[self._curIdx].endLevel
            self._weaponsInfo[self._curIdx].curLevelRemainExp = self._weaponsInfo[self._curIdx].curLevelRemainExp - self._weaponsInfo[self._curIdx].curLevelNeedExp
            self._weaponsInfo[self._curIdx].curLevelNeedExp = self._weaponsInfo[self._curIdx].endLevelNeedExp
        end
    end

    if self._weaponsInfo[self._curIdx].curLevel == self._weaponsInfo[self._curIdx].endLevel and self._weaponsInfo[self._curIdx].curLevelNeedExp <= self._weaponsInfo[self._curIdx].curLevelRemainExp then
        self:_ReleaseExpAnimTimer()
        return
    end

    self:_RefreshExp()
end

function MPWeaponUpItem:_RefreshExp()
    self._wtCurAddExpTB:SetText(string.format(Module.Settlement.Config.Loc.plusSignText, MathUtil.GetNumberFormatStr(self._curAddExp)))

    if self._weaponsInfo[self._curIdx].curLevelNeedExp == 0 then
        self._wtExpPBImg:SetPercent(1)
        self._wtExpPBLine:SetPosition(FVector2D(self._length, 0))
        self._wtExpPBTB:SetText(Module.Settlement.Config.Loc.MaxLevel)
    else
        self._wtExpPBImg:SetPercent(self._weaponsInfo[self._curIdx].curLevelRemainExp / self._weaponsInfo[self._curIdx].curLevelNeedExp)
        self._wtExpPBLine:SetPosition(FVector2D(self._length * self._weaponsInfo[self._curIdx].curLevelRemainExp / self._weaponsInfo[self._curIdx].curLevelNeedExp, 0))
        self._wtExpPBTB:SetText(string.format(Module.Settlement.Config.Loc.CurLevelExpSchedule, self._weaponsInfo[self._curIdx].curLevelRemainExp, self._weaponsInfo[self._curIdx].curLevelNeedExp))
    end
end

function MPWeaponUpItem:_RefreshFinalExp()
    self._curAddExp = self._weaponsInfo[self._curIdx].totalExp
    self._weaponsInfo[self._curIdx].curLevel = self._weaponsInfo[self._curIdx].endLevel
    self._weaponsInfo[self._curIdx].curLevelRemainExp = self._weaponsInfo[self._curIdx].endLevelRemainExp
    self._weaponsInfo[self._curIdx].curLevelNeedExp = self._weaponsInfo[self._curIdx].endLevelNeedExp

    self:_RefreshExp()
    self:_SetUpLevelState(true)

    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
end

function MPWeaponUpItem:_ReleaseExpAnimTimer()
    if self._expAnimTimerHandle then
        self._expAnimTimerHandle:Release()
        self._expAnimTimerHandle = nil

        self:_SetUpLevelState(true)
        self:_RefreshFinalExp()
    end
end

function MPWeaponUpItem:_ReleaseShowWeaponsTimer()
    if self._weaponsAnimTimerHandle then
        self._weaponsAnimTimerHandle:Release()
        self._weaponsAnimTimerHandle = nil
    end
end

function MPWeaponUpItem:_CheckAllAnimIsFinish()
    local res = true

    if self._weaponsAnimTimerHandle then
        self:_ReleaseShowWeaponsTimer()
        res = nil
    end

    if self._expAnimTimerHandle then
        self:_ReleaseExpAnimTimer()
        res = nil
    end

    return res
end

function MPWeaponUpItem:_SetUpLevelState(bShow)
    if bShow then
        self._wtArrowImg:Visible()
        self._wtEndWeaponLevelTB:Visible()
        self._wtComponetsSGB:Visible()
    else
        self._wtArrowImg:Collapsed()
        self._wtEndWeaponLevelTB:Collapsed()
        self._wtComponetsSGB:Collapsed()
    end
end

function MPWeaponUpItem:_OnGetComponetsCount()
    return self._weaponsInfo[self._curIdx] and self._weaponsInfo[self._curIdx].componentsItemBase and #self._weaponsInfo[self._curIdx].componentsItemBase or 0
end

function MPWeaponUpItem:_OnProcessComponetsWidget(idx, widget)
    widget:InitItem(self._weaponsInfo[self._curIdx].componentsItemBase[idx + 1])

    widget:BindCustomOnClicked(
        function()
            self:_CheckAllAnimIsFinish()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            widget:_SetSelected(true)
            Module.ItemDetail:OpenItemDetailPanel(
                self._weaponsInfo[self._curIdx].componentsItemBase[idx + 1],
                widget,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                function()
                    widget:_SetSelected()
                end
            )
        end
    )

    local wtBottomRightWB = widget:Wnd("wtBottomRightIconText", UIWidgetBase)

    if wtBottomRightWB then
        if self._weaponsInfo[self._curIdx].componentsItemBase[idx + 1].num == 1 then
            wtBottomRightWB:Collapsed()
        else
            wtBottomRightWB:Visible()
        end
    end
end

function MPWeaponUpItem:_JumpToNextStep()
    if self:_CheckAllAnimIsFinish() then
        Facade.UIManager:CloseUI(self)
    end
end

--解决ESC关闭上一个界面的问题
function MPWeaponUpItem:OnNavBack()
    return false
end

function MPWeaponUpItem:_OnOpenExpOverflowTips()
    if self._expOverflowTipsHandle then
        return
    end

    self._expOverflowTipsHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(self._weaponsInfo[self._curIdx].expOverflowTips, self._wtExpOverflowTipTA)
end

function MPWeaponUpItem:_OnCloseExpOverflowTips()
    if not self._expOverflowTipsHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._expOverflowTipsHandle, self._wtExpOverflowTipTA)
    self._expOverflowTipsHandle = nil
end

return MPWeaponUpItem
