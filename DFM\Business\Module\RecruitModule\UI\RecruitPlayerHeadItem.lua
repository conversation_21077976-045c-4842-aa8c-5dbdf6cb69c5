----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RecruitPlayerHeadItem : LuaUIBaseView
local RecruitPlayerHeadItem = ui("RecruitPlayerHeadItem")
local RecruitLogic = require "DFM.Business.Module.RecruitModule.Logic.RecruitLogic"
local RankIconAbbr = require "DFM.Business.Module.RankingModule.UI.RankIconAbbr"

function RecruitPlayerHeadItem:Ctor()
    -- self._wtBtnClose = self:Wnd("Button_Close", UIButton)
    -- self._wtBtnClose:Event("OnClicked",self.OnCloseBtnClick,self)
    self._wtEmptyHint = self:Wnd("wtEmptyHint", UIWidgetBase)
    self._wtPlayerInfoPanel = self:Wnd("wtPlayerInfoPanel", UIWidgetBase)
    self._wtCommonHeadIcon = self:Wnd("wtCommonHeadIcon", CommonHeadIcon)
    self._wtRankDivisionIcon = self:Wnd("wtRankDivisionIcon", RankIconAbbr)
    self._wtCaptainIcon = self:Wnd("wtCaptainIcon", UIImage)
    self._wtOfflinePanel = self:Wnd("wtOfflinePanel", UIImage)
    self._wtImage_Invisibility = self:Wnd("DFImage_Invisibility", UIImage)
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function RecruitPlayerHeadItem:OnInitExtraData(params)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecruitPlayerHeadItem:OnOpen()
    self:AddListeners()
end

-- UI监听事件、协议
function RecruitPlayerHeadItem:AddListeners()
    self:AddLuaEvent(Server.AccountServer.Events.evtPlayerHeadIconChange, self._OnPlayerHeadIconChange, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecruitPlayerHeadItem:OnClose()
    self:RemoveAllLuaEvent()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecruitPlayerHeadItem:OnShow()

end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecruitPlayerHeadItem:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function RecruitPlayerHeadItem:OnAnimFinished(anim)
end

function RecruitPlayerHeadItem:InitPlayerInfo(playerSimpleInfo, bHideCaptain, bHideRank, btnTbl)
    self._wtEmptyHint:SelfHitTestInvisible()
    self._wtPlayerInfoPanel:Collapsed()
    self._wtCaptainIcon:Collapsed()
    self._wtOfflinePanel:Collapsed()
    self._wtRankDivisionIcon:Collapsed()
    self._playerSimpleInfo = playerSimpleInfo
    if playerSimpleInfo ~= nil then
        self._wtRankDivisionIcon:SetRankIconNone()
        self._wtRankDivisionIcon:SelfHitTestInvisible()
        playerSimpleInfo.level = RecruitLogic.IsInMp() == true and playerSimpleInfo.level or playerSimpleInfo.season_lvl
        self._wtEmptyHint:Collapsed()
        self._wtPlayerInfoPanel:SelfHitTestInvisible()
        if bHideCaptain ~= true and Server.TeamServer:IsCaptial(playerSimpleInfo.player_id) and (Server.TeamServer:HasOtherMembers() or Module.Recruit:IsRecruiting() == true) then
            self._wtCaptainIcon:SelfHitTestInvisible()
        end
        if bHideRank ~= true then
            self._wtRankDivisionIcon:SetRankIconNone()
            self._wtRankDivisionIcon:SelfHitTestInvisible()
            if RecruitLogic.IsInMp() == true then
                if playerSimpleInfo.mp_rank_attended == true then
                    local targetScore = (playerSimpleInfo.show_commander_rank_points ~= nil and playerSimpleInfo.show_commander_rank_points > 0) and playerSimpleInfo.mp_commander_score or playerSimpleInfo.mp_rank_score
                    self._wtRankDivisionIcon:SetTournamentIconByScore(targetScore)
                else
                    self._wtRankDivisionIcon:SetRankIconNone()
                end
            else
                if playerSimpleInfo.sol_rank_attended == true then
                    self._wtRankDivisionIcon:SetRankingIconByScore(playerSimpleInfo.sol_rank_score)
                else
                    self._wtRankDivisionIcon:SetRankIconNone()
                end
            end
        else
            self._wtRankDivisionIcon:Collapsed()
        end
        if playerSimpleInfo.state == TeamMemberState.MemOffline then
            self._wtOfflinePanel:SelfHitTestInvisible()
        end
        if Server.AccountServer:GetPlayerId() == playerSimpleInfo.player_id then
            self._wtCommonHeadIcon:InitPortrait(playerSimpleInfo, HeadIconType.HeadPerInformat)

            -- 我自己是隐身状态，显示隐身状态图标
            if Module.Friend:GetSelfLoginInvisible() then
                self._wtImage_Invisibility:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            else
                self._wtImage_Invisibility:Collapsed()
            end
        else
            if not btnTbl then
                btnTbl =  {
                    HeadButtonType.PlayerInformat,
                    HeadButtonType.AddFriend,
                }
                if Server.TeamServer:IsCaptial() == true and Server.TeamServer:IsMember(playerSimpleInfo.player_id) ~= nil then
                    table.insert(btnTbl, HeadButtonType.KickTeam)
                    table.insert(btnTbl, HeadButtonType.PromotTeam)
                end
                table.insert(btnTbl, HeadButtonType.AddBlack)
                table.insert(btnTbl, HeadButtonType.Report)
                if Server.TeamServer:FindMember(playerSimpleInfo.player_id) ~= nil then
                    table.insert(btnTbl, HeadButtonType.ReportVoice)
                end
            end
            self._wtCommonHeadIcon:InitPortrait(playerSimpleInfo, HeadIconType.HeadList, btnTbl, Server.TeamServer:FindMember(playerSimpleInfo.player_id) and FriendApplySource.TeamRelationshipApply or FriendApplySource.OtherApply)
        end   
    end
end

function RecruitPlayerHeadItem:SetRankIconVisible(bVisible)
    if bVisible == true then
        self._wtRankDivisionIcon:SelfHitTestInvisible()
    else
        self._wtRankDivisionIcon:Collapsed()
    end
end

function RecruitPlayerHeadItem:_OnPlayerHeadIconChange(url)
    if self._playerSimpleInfo and self._playerSimpleInfo.player_id == Server.AccountServer:GetPlayerId() then
        self._playerSimpleInfo.pic_url = url
        self._wtCommonHeadIcon:InitPortrait(self._playerSimpleInfo, HeadIconType.HeadPerInformat)
    end
end

return RecruitPlayerHeadItem
