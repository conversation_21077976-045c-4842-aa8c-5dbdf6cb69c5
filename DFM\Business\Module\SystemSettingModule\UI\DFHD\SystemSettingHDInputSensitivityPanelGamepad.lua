------------------------------------
-- Create By <PERSON><PERSON><PERSON><PERSON>@VIRTUOS
------------------------------------

----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SystemSettingHDInputSensitivityPanelGamepad
local SystemSettingHDInputSensitivityPanelGamepad = ui("SystemSettingHDInputSensitivityPanelGamepad")
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local InputSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.InputSettingLogicHD"
local ESensitivityModeHD = import("ESensitivityModeHD")
local GamepadSensitivityMode = import("EMouseSensitivityMode")
local EGamepadSensitivityPresetType = import("EGamepadSensitivityPresetType")

local _sensitivitySetting = import("ClientSensitivitySettingHD").Get()

-- UI Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function SystemSettingHDInputSensitivityPanelGamepad:Ctor()
    loginfo("[george]<InputPanel> Ctor()")
    self:_BindWidget()
end

function SystemSettingHDInputSensitivityPanelGamepad:_BindWidget()
    self._wtScrollBox = self:Wnd("InputPanelScrollBox", UIWidgetBase)
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
    self._wtItemPanel = self:Wnd("wtItemPanel", UILightWidget)

    self._wtMDVList = self:Wnd("_wtMDVList", UILightWidget)
    self._wtADSList = self:Wnd("_wtADSList", UILightWidget)

    self._wtMDVList_1 = self:Wnd("_wtMDVList_1", UILightWidget) -- 地面载具
    self._wtADSList_1 = self:Wnd("_wtADSList_1", UILightWidget)

    self._wtMDVList_2 = self:Wnd("_wtMDVList_2", UILightWidget) --空中载具
    self._wtADSList_2 = self:Wnd("_wtADSList_2", UILightWidget)

    self._wtMDVList_3 = self:Wnd("_wtMDVList_3", UILightWidget)  --炮手
    self._wtADSList_3 = self:Wnd("_wtADSList_3", UILightWidget)

    self._wtMDVList_4 = self:Wnd("_wtMDVList_4", UILightWidget)  --固定翼
    self._wtADSList_4 = self:Wnd("_wtADSList_4", UILightWidget)

    self._wtReverseAll = self:Wnd("_wtReverseAll", LuaUIBaseView)
    self._wtMDV = self:Wnd("_wtMDV", LuaUIBaseView)
    self._wtMDV_1 = self:Wnd("_wtMDV_1", LuaUIBaseView)
    self._wtMDV_2 = self:Wnd("_wtMDV_2", LuaUIBaseView)
    self._wtMDV_3 = self:Wnd("_wtMDV_3", LuaUIBaseView)
    self._wtMDV_4 = self:Wnd("_wtMDV_4", LuaUIBaseView)

    -- 临时扩大列表宽度
    -- local _innerDropdown = self._wtSensitivityMode:Wnd("WBP_DFTabGroupDroDownBox_Pc", UIWidgetBase)
    -- local _innerSizeBox = _innerDropdown:Wnd("DFSizeBox_1", UILightWidget)
    -- _innerSizeBox:SetMaxDesiredWidth(724)

    self._MDVSettings={
        [GamepadSensitivityMode.ENormalWeapon] = {self._wtMDVList,  self._wtADSList,  self._wtMDV},
        [GamepadSensitivityMode.EVehicleDriveWeapon] = {self._wtMDVList_1,  self._wtADSList_1,  self._wtMDV_1},
        [GamepadSensitivityMode.EHelicopterDriveWeapon] = {self._wtMDVList_2,  self._wtADSList_2,  self._wtMDV_2},
        [GamepadSensitivityMode.EVehicleWeapon] = {self._wtMDVList_3,  self._wtADSList_3,  self._wtMDV_3},
        [GamepadSensitivityMode.EJet] = {self._wtMDVList_4,  self._wtADSList_4,  self._wtMDV_4}
    }

    --BEGIN MODIFICATION @ VIRTUOS
    self._wtSensitivityPresetMode = self:Wnd("_wtSensitivityPresetMode", LuaUIBaseView)

    self._wtADSCustomCheck = self:Wnd("_wtADSCustomCheck", LuaUIBaseView)    -- 武器
    self._wtADSCustomCheck2 = self:Wnd("_wtADSCustomCheck_1", LuaUIBaseView) -- 地面载具
    self._wtADSCustomCheck3 = self:Wnd("_wtADSCustomCheck_2", LuaUIBaseView) -- 空中载具
    self._wtADSCustomCheck4 = self:Wnd("_wtADSCustomCheck_3", LuaUIBaseView) -- 炮手
    self._wtADSCustomCheck5 = self:Wnd("_wtADSCustomCheck_4", LuaUIBaseView) -- 固定翼

    local _wtBaseSensitivity = self:Wnd("WBP_SetUpComponent_SplitBtn", LuaUIBaseView)
    if _wtBaseSensitivity then
        _wtBaseSensitivity:Collapsed()
    end

    local _wtADSBaseSensitivity = self:Wnd("WBP_SetUpComponent_SplitBtn_1", LuaUIBaseView)
    if _wtBaseSensitivity then
        _wtADSBaseSensitivity:Collapsed()
    end

    self._wtCheckAdaptiveTrigger = self:Wnd("WBP_Check_AdaptiveTrigger", LuaUIBaseView) -- 自适应扳机
    if IsPS5() then
        self._wtCheckAdaptiveTrigger:Visible()
    else
        self._wtCheckAdaptiveTrigger:Collapsed()
    end
    --END MODIFICATION
end

function SystemSettingHDInputSensitivityPanelGamepad:_BindBtnEvent()
    self:AddLuaEvent(self._wtReverseAll.evtOnStateChanged, self._OnReverseAllStateChanged, self)

    self:AddLuaEvent(self._wtSensitivityPresetMode.evtOnOptionChanged, self._OnSensitivityModePresetChanged, self)

    self._wtADSCustomCheck:Event("OnStageChanged", self._OnADSCustomCheckStageChanged, self)
    self._wtADSCustomCheck2:Event("OnStageChanged", self._OnADSCustomCheckStageChanged2, self)
    self._wtADSCustomCheck3:Event("OnStageChanged", self._OnADSCustomCheckStageChanged3, self)
    self._wtADSCustomCheck4:Event("OnStageChanged", self._OnADSCustomCheckStageChanged4, self)
    self._wtADSCustomCheck5:Event("OnStageChanged", self._OnADSCustomCheckStageChanged5, self)
end

function SystemSettingHDInputSensitivityPanelGamepad:OnOpen()
    -- loginfo("[george]<InputPanel> OnOpen()")
    self:_BindBtnEvent()
    self:GenerateDynamicSettings()

    local zoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(0)
    local vehicleZoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(1)
    local vehicleZoomData2 = InputSettingLogicHD.GetSortedZoomData_Gamepad(2)
    local vehicleZoomData3 = InputSettingLogicHD.GetSortedZoomData_Gamepad(3)
    local vehicleZoomData4 = InputSettingLogicHD.GetSortedZoomData_Gamepad(4)

    local bUsingForGamepad = true
    for mode, lists in pairs(self._MDVSettings) do
        for j, item in ipairs(lists[1]:GetAllChildren()) do
            if mode == GamepadSensitivityMode.ENormalWeapon then
                item:Init(mode, j, zoomData[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EVehicleDriveWeapon then
                item:Init(mode, j, vehicleZoomData[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EHelicopterDriveWeapon then
                item:Init(mode, j, vehicleZoomData2[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EVehicleWeapon then
                item:Init(mode, j, vehicleZoomData3[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EJet then
                item:Init(mode, j, vehicleZoomData4[j], bUsingForGamepad)
            end
        end
    end

    for mode, lists in pairs(self._MDVSettings) do
        for j, item in ipairs(lists[2]:GetAllChildren()) do
            if mode == GamepadSensitivityMode.ENormalWeapon then
                item:Init(mode, j, zoomData[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EVehicleDriveWeapon then
                item:Init(mode, j, vehicleZoomData[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EHelicopterDriveWeapon then
                item:Init(mode, j, vehicleZoomData2[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EVehicleWeapon then
                item:Init(mode, j, vehicleZoomData3[j], bUsingForGamepad)
            elseif mode == GamepadSensitivityMode.EJet then
                item:Init(mode, j, vehicleZoomData4[j], bUsingForGamepad)
            end
        end
    end

    self:_RefreshADSList()
    self:LoadSensitivityFromPreset(CommonSettingLogicHD.GetDataByID("GamepadSensitivityPresetMode"))
end

function SystemSettingHDInputSensitivityPanelGamepad:_OnReverseAllStateChanged(bEnable)
    if bEnable then
        local subIDs = {
            "bInfantryVerticalMMReversedGamepad",
            "bVehicleVerticalMMReversedGamepad",
            "bHelicopterVerticalMMReversedGamepad",
            "bGunnerVerticalMMReversedGamepad"
        }

        local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()

        for _, id in ipairs(subIDs) do
            CommonSettingLogicHD.SetDataByIDImmediately(id, true)
            local item = curItems[id]
            if not hasdestroy(item) then
                item:ReloadSetting()
            end
        end
    end
end

function SystemSettingHDInputSensitivityPanelGamepad:_RefreshADSList(group, bEnableCustom)
    -- 手柄只有ADSOnly
    if not group then
        self:_RefreshADSList(GamepadSensitivityMode.ENormalWeapon ,CommonSettingLogicHD.GetDataByID("bEnableScopeCustom"))
        self:_RefreshADSList(GamepadSensitivityMode.EVehicleDriveWeapon ,CommonSettingLogicHD.GetDataByID("bEnableDriverADSCustomGamepad"))
        self:_RefreshADSList(GamepadSensitivityMode.EHelicopterDriveWeapon ,CommonSettingLogicHD.GetDataByID("bEnableHelicopterDriverADSCustomGamepad"))
        self:_RefreshADSList(GamepadSensitivityMode.EVehicleWeapon ,CommonSettingLogicHD.GetDataByID("bEnablePassengerADSCustomGamepad"))
        self:_RefreshADSList(GamepadSensitivityMode.EJet ,CommonSettingLogicHD.GetDataByID("bEnableJetADSCustomGamepad"))
        return
    end

    if bEnableCustom then
        self._MDVSettings[group][1]:SelfHitTestInvisible()
        self._MDVSettings[group][2]:Collapsed()
        self._MDVSettings[group][3]:Collapsed()
    else
        self._MDVSettings[group][1]:Collapsed()
        self._MDVSettings[group][2]:Collapsed()
        self._MDVSettings[group][3]:Collapsed()
    end
end

function SystemSettingHDInputSensitivityPanelGamepad:GenerateDynamicSettings()
    for mode,list in pairs(self._MDVSettings) do
        Facade.UIManager:RemoveSubUIByParent(self, list[2])
        Facade.UIManager:RemoveSubUIByParent(self, list[1])
        local zoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(0)
        local vehicleZoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(1)
        local vehicleZoomData2 = InputSettingLogicHD.GetSortedZoomData_Gamepad(2)
        local vehicleZoomData3 = InputSettingLogicHD.GetSortedZoomData_Gamepad(3)
        local vehicleZoomData4 = InputSettingLogicHD.GetSortedZoomData_Gamepad(4)
        if mode == GamepadSensitivityMode.ENormalWeapon then
            for i, tmpData in ipairs(zoomData) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[1])
            end
        elseif mode == GamepadSensitivityMode.EVehicleDriveWeapon then
            for i, tmpData in ipairs(vehicleZoomData) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[1])
            end
        elseif mode == GamepadSensitivityMode.EHelicopterDriveWeapon then
            for i, tmpData in ipairs(vehicleZoomData2) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[1])
            end
        elseif mode == GamepadSensitivityMode.EVehicleWeapon then
            for i, tmpData in ipairs(vehicleZoomData3) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[1])
            end
        elseif mode == GamepadSensitivityMode.EJet then
            for i, tmpData in ipairs(vehicleZoomData4) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[1])
            end
        end
    end
end

function SystemSettingHDInputSensitivityPanelGamepad:OnShow()
    --BEGIN MODIFICATION @ VIRTUOS :
    if not IsConsole() then
        local list = {
            {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
            {actionName = "ResetInput", func = self._OnClickResetBtn, caller = self}}
        local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
        for _, v in ipairs(globalList) do
            table.insert(list, v)
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
    end

    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.InputSetting_Gamepad then

        if IsConsole() then
            Module.CommonBar:SetBottomBarTempInputSummaryList(
                {
                    {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},

                    {actionName = "ResetInput", func = self._OnClickResetBtn, caller = self}},
                false
            )
        end

        -- UI Navigation
        self:_RegisterNavGroup()

        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
        CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
    end
    --END MODIFICATION
end

function SystemSettingHDInputSensitivityPanelGamepad:OnShowBegin()
end

function SystemSettingHDInputSensitivityPanelGamepad:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function SystemSettingHDInputSensitivityPanelGamepad:OnHide()
    -- UI Navigation
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    self:_RemoveNavGroup()

    --BEGIN MODIFICATION @ VIRTUOS :
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
    --END MODIFICATION
end

function SystemSettingHDInputSensitivityPanelGamepad:OnClose()
    for _,list in pairs(self._MDVSettings) do
        Facade.UIManager:ClearSubUIByParent(self, list[2])
        Facade.UIManager:ClearSubUIByParent(self, list[1])
    end
end

function SystemSettingHDInputSensitivityPanelGamepad:_OnSensitivityExpandableStateChanged(bExpanded)
    CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
end

function SystemSettingHDInputSensitivityPanelGamepad:_OnClickResetBtn()
    local function fReset()
        SettingLogicHD.ResetCurrentSettings()
        InputSettingLogicHD.ResetAllADSSensitivity(true)
        for i, lists in pairs(self._MDVSettings) do
            for _, child in ipairs(lists[1]:GetAllChildren()) do
                child:ReloadSetting()
            end
        end
        InputSettingLogicHD.ResetAllZoomratedMDV(true)
        for i, lists in pairs(self._MDVSettings) do
            for _, child in ipairs(lists[1]:GetAllChildren()) do
                child:ReloadSetting()
            end
        end

        self:LoadSensitivityFromPreset(CommonSettingLogicHD.GetDataByID("GamepadSensitivityPresetMode"))
        self:_RefreshADSList()
    end

    local contentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetGamepadInputSensitivityTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    self.confirmWindowHandle =
        Module.CommonTips:ShowConfirmWindow(contentTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

-- UI Navigation
function SystemSettingHDInputSensitivityPanelGamepad:_RegisterNavGroup()
	if not self._NavGroup then
	    self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollBox, self, "Grid1D")
	end

	if self._NavGroup then
        local NavWidgetTable = {}

        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_17",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_18",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_19",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_20",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_21",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_22",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityCurveMode", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityPresetMode", NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_5",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_4",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_8",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_15",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_9",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_10",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_1",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_7",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_6",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_11",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_14",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_12",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_13",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtADSCustomCheck",NavWidgetTable)

        local _wt_wtMDVList = self:Wnd("_wtMDVList", UIWidgetBase)
        local _wt_wtADSList = self:Wnd("_wtADSList", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList, NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("_wtMDV",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_MultipleChoice_6", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtReverseAll", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_1", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_2", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_3", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_4", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Vehicles_1", NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("WBP_Check_Rumble",NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_Check_AdaptiveTrigger", NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_1",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_2",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_6",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_3",NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtADSCustomCheck_1",NavWidgetTable)

        local _wt_wtMDVList_1 = self:Wnd("_wtMDVList_1", UIWidgetBase)
        local _wt_wtADSList_1 = self:Wnd("_wtADSList_1", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_1, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_1, NavWidgetTable)


        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_10",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_12",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_13",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_14",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_7",NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtADSCustomCheck_2",NavWidgetTable)

        local _wt_wtMDVList_2 = self:Wnd("_wtMDVList_2", UIWidgetBase)
        local _wt_wtADSList_2 = self:Wnd("_wtADSList_2", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_2, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_2, NavWidgetTable)

        -- 固定翼
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_9",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_21",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_20",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_19",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_18",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_17",NavWidgetTable)


        self:_AddSelfWidgetToNavWidgetByName("_wtADSCustomCheck_4",NavWidgetTable)

        local _wt_wtMDVList_4 = self:Wnd("_wtMDVList_4", UIWidgetBase)
        local _wt_wtADSList_4 = self:Wnd("_wtADSList_4", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_4, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_4, NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_4",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_5",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_11",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_8",NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtADSCustomCheck_3",NavWidgetTable)

        local _wt_wtMDVList_3 = self:Wnd("_wtMDVList_3", UIWidgetBase)
        local _wt_wtADSList_3 = self:Wnd("_wtADSList_3", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_3, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_3, NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_5",NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_AnalogCursorDeadzone",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_AnalogCursorSpeed",NavWidgetTable)



        --self:_AddSelfWidgetToNavWidgetByName("DFVerticalBox", NavWidgetTable)

		for index, value in ipairs(NavWidgetTable) do
            self._NavGroup:AddNavWidgetToArray(NavWidgetTable[index])
        end

		--self._NavGroup:AddNavWidgetToArray(self._wtScrollBox)
        self._NavGroup:SetScrollRecipient(self._wtScrollBox)
		WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
end

function SystemSettingHDInputSensitivityPanelGamepad:_RemoveNavGroup()
	if self._NavGroup then
		self._NavGroup = nil
	end
	WidgetUtil.RemoveNavigationGroup(self)
end

-- 根据名字查找控件并加入导航组
function SystemSettingHDInputSensitivityPanelGamepad:_AddSelfWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            table.insert(NavWidgetTable, targetWidget)
        end
    end
end

-- 根据SplitBtn控件的名字，查找Slider控件并加入导航组
function SystemSettingHDInputSensitivityPanelGamepad:_AddSliderWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            local Slider = targetWidget:Wnd("Slider", UIWidgetBase)
            if Slider then
                local subSlider = Slider:Wnd("Slider_167", UIWidgetBase)
                if subSlider then
                    table.insert(NavWidgetTable, subSlider)
                end
            end
        end
    end
end

-- MDV和ASD列表会动态显示，需要寻找子控件加入导航组
function SystemSettingHDInputSensitivityPanelGamepad:_AddDynamicListToNavWidget(DynamicList, NavWidgetTable)
    local wtMDVChildren = DynamicList:GetAllChildren()

    for _, child in pairs(wtMDVChildren) do
        local childSlider = child:Wnd("Slider", UIWidgetBase)

        if childSlider then
            local subSlider = childSlider:Wnd("Slider_167", UIWidgetBase)
            if subSlider then
                table.insert(NavWidgetTable, subSlider)
            end
        end
    end
end


function SystemSettingHDInputSensitivityPanelGamepad:LoadSensitivityFromPreset(presetType)
    local bUsePreset = presetType ~= EGamepadSensitivityPresetType.Custom

    local datatable = InputSettingLogicHD.GetPresetDatatable_Gamepad()
    if not datatable then
        return
    end

    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
    for id, item in pairs(curItems) do
        local row = datatable[id]
        if row and not hasdestroy(item)then
            if bUsePreset then
                local value = _sensitivitySetting:GetPresetValueByType(row.PresetValues, presetType)
                CommonSettingLogicHD.SetDataByID(id, value)
                item:ReloadSetting()
            end
            if bUsePreset then
                item:Collapsed()
            else
                item:SelfHitTestInvisible()
            end
            --item:DisableItem(bUsePreset)
        end
    end
end

function SystemSettingHDInputSensitivityPanelGamepad:_OnSensitivityModePresetChanged(presetType)
    self:LoadSensitivityFromPreset(presetType)
end

function SystemSettingHDInputSensitivityPanelGamepad:_OnADSCustomCheckStageChanged(bEnabled)
    self:_RefreshADSList(GamepadSensitivityMode.ENormalWeapon, bEnabled)
end
function SystemSettingHDInputSensitivityPanelGamepad:_OnADSCustomCheckStageChanged2(bEnabled)
    self:_RefreshADSList(GamepadSensitivityMode.EVehicleDriveWeapon, bEnabled)
end
function SystemSettingHDInputSensitivityPanelGamepad:_OnADSCustomCheckStageChanged3(bEnabled)
    self:_RefreshADSList(GamepadSensitivityMode.EHelicopterDriveWeapon, bEnabled)
end
function SystemSettingHDInputSensitivityPanelGamepad:_OnADSCustomCheckStageChanged4(bEnabled)
    self:_RefreshADSList(GamepadSensitivityMode.EVehicleWeapon, bEnabled)
end
function SystemSettingHDInputSensitivityPanelGamepad:_OnADSCustomCheckStageChanged5(bEnabled)
    self:_RefreshADSList(GamepadSensitivityMode.EJet, bEnabled)
end

return SystemSettingHDInputSensitivityPanelGamepad
