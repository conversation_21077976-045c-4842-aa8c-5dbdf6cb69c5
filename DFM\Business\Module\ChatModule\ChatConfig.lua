----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



UITable[UIName2ID.ChatFriendList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatFriendList",
    BPKey = "WBP_Chat_FriendList",
    Anim = {
        FlowInAni = "WBP_Chat_FriendList_in",
        FlowOutAni = "WBP_Chat_FriendList_out",
    }
}

UITable[UIName2ID.ChatInputPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatInputPanel",
    BPKey = "WBP_Chat_ChatMain",
    Anim = {
        FlowInAni = "WBP_Chat_ChatMain_in",
        FlowOutAni = "WBP_Chat_ChatMain_out",
    },
    SubUIs = {
        UIName2ID.ChatPrivate,
        UIName2ID.ChatWorld,
        UIName2ID.ChatTeam,
        UIName2ID.ChatEmojiPanel,
        -- UIName2ID.ChatShare,
        UIName2ID.ChatVoicePanel,
        UIName2ID.ChatVoice_PC,
        UIName2ID.ChatCollege,
    }
}

UITable[UIName2ID.ChatWorld] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatWorld",
    BPKey = "WBP_Chat_World",
    Anim = {
        FlowInAni = "WBP_Chat_World_in",
        FlowOutAni = "WBP_Chat_World_out",
    }
}

UITable[UIName2ID.ChatCollege] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatCollege",
    BPKey = "WBP_Chat_College",
    Anim = {
        FlowInAni = "WBP_Chat_World_in",
        FlowOutAni = "WBP_Chat_World_out",
    }
}

UITable[UIName2ID.ChatPrivate] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatPrivate",
    BPKey = "WBP_Chat_Private",
    SubUIs = {
        UIName2ID.CommonEmptyContent,
    }
}

UITable[UIName2ID.ChatTeam] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatTeam",
    BPKey = "WBP_Chat_Team"
}

UITable[UIName2ID.ChatText] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatText",
    BPKey = "WBP_Chat_Text",
    Anim = {
        FlowInAni = "WBP_Chat_Text_in",
        FlowOutAni = "WBP_Chat_Text_out",
    }
}

UITable[UIName2ID.ChatAppoint] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatAppoint",
    BPKey = "WBP_Chat_Booking",
}

UITable[UIName2ID.ChatVoice] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatVoice",
    BPKey = "WBP_Chat_VoiceBtn"
}

UITable[UIName2ID.ChatFriendSlot] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatFriendSlot",
    BPKey = "WBP_Chat_PrivateBox",
    Anim = {
        FlowInAni = "WBP_Chat_PrivateBox_in",
        FlowOutAni = "WBP_Chat_PrivateBox_out",
    }
}

UITable[UIName2ID.ChatVoicePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatVoicePanel",
    BPKey = "WBP_Chat_Voice",
    Anim = {
        FlowInAni = "WBP_Chat_Voice_in_01",
        FlowOutAni = "WBP_Chat_Voice_out_01",
    }
}

UITable[UIName2ID.ChatEmojiPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatEmojiPanel",
    BPKey = "WBP_Chat_Expression",
    SubUIs = {
        UIName2ID.ChatEmoji
    },
}

UITable[UIName2ID.ChatEmoji] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatEmoji",
    BPKey = "WBP_Chat_Emoji"
}

UITable[UIName2ID.ChatShare] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatShare",
    BPKey = "WBP_Chat_Share_Chat"
}

UITable[UIName2ID.ChatSetting] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatSetting",
    BPKey = "WBP_Chat_SetUp",
    IsModal = true,
    Anim = {
        FlowInAni = "WBP_Chat_SetUp_in",
        FlowOutAni = "WBP_Chat_SetUp_out",
    }
}

UITable[UIName2ID.ChatCheckBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatCheckBox",
    BPKey = "WBP_Chat_CheckBox"
}


UITable[UIName2ID.ChatReport] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatReport",
    BPKey = "WBP_Chat_TextReport",
    IsModal = true,
}

UITable[UIName2ID.AutoMsgMentionPanel] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.ChatModule.UI.AutoMsgMentionPanel",
    BPKey = "WBP_Chat_Chat",
    HUDName = "AutoMsgMentionPanel",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 20
}

UITable[UIName2ID.ChatMsgWidget] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatMsgWidget",
    BPKey = "WBP_ControllerFunctionButtonCommand",
    HUDName = "ChatMsgWidget",
    ZOrderBase = EHUDBaseZorder.Touch,
    ZOrderOffset = 0
}

UITable[UIName2ID.MsgPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ChatModule.UI.MsgPanel",
    BPKey = "WBP_Chat_MsgOperation",
    SubUIs = {
        UIName2ID.MsgPanelWidget,
        UIName2ID.InviteMsgCell,
        UIName2ID.ChatMsgCell,
        UIName2ID.MsgAppoint,
        UIName2ID.ChatMakeAppoint,
    },
    Anim = {
        FlowInAni = "WBP_Chat_MsgOperation_in",
        FlowOutAni = "WBP_Chat_MsgOperation_out"
    },
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 1,
    },
}

UITable[UIName2ID.ChatMsgCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.ChatMsgCell",
    BPKey = "WBP_ChatMsg_Text1"
}

UITable[UIName2ID.InviteMsgCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.InviteMsgCell",
    BPKey = "WBP_ChatMsg_Text2"
}

UITable[UIName2ID.MsgAppoint] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.MsgAppoint",
    BPKey = "WBP_Chat_InGameBooking"
}

-------------HD的配置--------------------
UITable[UIName2ID.ChatText_PC] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.ChatText_PC",
    BPKey = "WBP_Chat_Text_PC"
}

UITable[UIName2ID.ChatVoice_PC] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.ChatVoice_PC",
    BPKey = "WBP_Chat_PCVoice"
}

UITable[UIName2ID.SafeRoomChatText_PC] = {
    UILayer = EUILayer.Sub,
    -- LuaPath = "DFM.Business.Module.ChatModule.UI.HD.ChatText_PC",
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.FrontChatText_PC",
    BPKey = "WBP_SafeRoomChat_Text_PC"
}

UITable[UIName2ID.SafeRoomMsgHistory_PC] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.SafeRoomMsgHistory_PC",
    BPKey = "WBP_SafeRoomChatHistory",
}

UITable[UIName2ID.MsgPanel_PC] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.MsgPanel_PC",
    BPKey = "WBP_ChatInput",
    -- BPKey = "WBP_Chat_MsgOperation",
    HUDName = "MsgPanel_PC",
    ZOrderBase = EHUDBaseZorder.Popup,
    -- ZOrderOffset = 2,
    ZOrderOffset = 551,
    IsControlByUIState = true,
    SubUIs = {
        UIName2ID.ChatMakeAppoint,
    },
}

-- PC端 结算阶段使用的聊天控件
UITable[UIName2ID.MsgPanelTopTournament_PC] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.Game.MsgPanelTopTournament_PC",
    BPKey = "WBP_ChatInput",
    SubUIs = {
        UIName2ID.ChatMakeAppoint,
    },
}

-- 移动端 结算阶段使用的聊天控件
UITable[UIName2ID.MsgPanelTopTournament] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Mobile.Game.ChatMsgWidgetSettlement",
    BPKey = "WBP_ControllerFunctionButtonCommand",
}

-- 移动端 结算阶段使用的聊天便携控件
UITable[UIName2ID.AutoMsg_TopTournament] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Mobile.Game.AutoMsg_TopTournament",
    BPKey = "WBP_Chat_Chat",
}

UITable[UIName2ID.FrontChatMainPanel_PC] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.ChatModule.UI.HD.FrontChatMainPanel_PC",
    BPKey = "WBP_Chat_ChatPanel",
    SubUIs = {
        UIName2ID.ChatEmojiPanel_PC
    },
    Anim = {
        FlowInAni = "Anim_in",
        FlowOutAni = "Anim_out",
    }
}


UITable[UIName2ID.ChatEmojiPanel_PC] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.ChatEmojiPanel",
    BPKey = "WBP_Chat_Expression_PC",
    SubUIs = {
        UIName2ID.ChatEmoji
    },
}

UITable[UIName2ID.MsgPanelWidget] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.MsgPanelWidget",
    BPKey = "WBP_Chat_Msg_Operation_Widget"
}

UITable[UIName2ID.Announcement] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Announcement",
    BPKey = "WBP_Hall_Announcement",
    Anim={
        bManuelAnim=true
    }
}

UITable[UIName2ID.MicUnderageCompliancePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ChatModule.UI.MicUnderageCompliancePop",
    BPKey = "WBP_Protocol_UnderageCompliancePop",
    IsModal = true,
}

UITable[UIName2ID.ChatMakeAppoint] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ChatModule.UI.Children.InGameInviteCard",
    BPKey = "WBP_Chat_MakeAppointment"
}

local ChatConfig =
{
    EMsgInputType = {
        kTxt = 1,
        kVoice = 2
    },
    -- ChatMsgCell 需要用
    EMsgPanelType = {
        kConvenientChat = 1,
        kChat = 2,
        kTeamInvite = 3
    },
    presetChatTable = {
        -- [0] = Facade.TableManager:GetTable("PresetChat"),
        -- [1] = Facade.TableManager:GetTable("PresetChatSOL"),
        -- [2] = Facade.TableManager:GetTable("PresetChat"),
        -- [3] = Facade.TableManager:GetTable("PresetChatRaid"),
        -- [4] = Facade.TableManager:GetTable("PresetChatBattle"),
        -- [5] = Facade.TableManager:GetTable("PresetChat"),
        -- --通过其他事件触发的干员聊天
        -- [10] = Facade.TableManager:GetTable("PresetChatAuto"),
    },
    presetChatFunctionTable = Facade.TableManager:GetTable("PresetChatFunction"),

    Loc = {
        ReturnToEnd = NSLOCTEXT("ChatModule", "Lua_Chat_ReturnToEnd", "回到底部"),
        UnreadMessage = NSLOCTEXT("ChatModule", "Lua_Chat_UnreadMessage", "未读消息{MsgNum}"),
        UnreadMessage_PC = NSLOCTEXT("ChatModule", "Lua_Chat_UnreadMessage_PC", "新增 +{MsgNum}"),

        PlayerExitTeam = NSLOCTEXT("ChatModule", "Lua_Chat_PlayerExitTeam", "%s 离开了队伍"),
        PlayerJoinTeam = NSLOCTEXT("ChatModule", "Lua_Chat_PlayerJoinTeam", "%s 加入了队伍"),

        PauseRecord = NSLOCTEXT("ChatModule", "Lua_Chat_PauseRecord", "手指上滑，取消语音发送"),
        StopRecord = NSLOCTEXT("ChatModule", "Lua_Chat_StopRecord", "松开手指，取消语音发送"),
        RecordFaild = NSLOCTEXT("ChatModule", "Lua_Chat_RecordFaild", "录音失败，请重试"),

        CannotPrivateChat = NSLOCTEXT("ChatModule", "Lua_Chat_CannotPrivateChat", "无法与黑名单玩家私聊"),
        ChatSearch = NSLOCTEXT("ChatModule", "Lua_Chat_ChatSearch", "请输入好友名称"),

        ExceedChatMaxLen = NSLOCTEXT("ChatModule", "Lua_Chat_ExceedChatMaxLen", "超出输入上限"),
        EmptyInput = NSLOCTEXT("ChatModule", "Lua_Chat_EmptyInput", "发送内容为空"),
        ChatVoice = NSLOCTEXT("ChatModule", "Lua_Chat_ChatVoice", "语音"),
        ForbidInput = NSLOCTEXT("ChatModule", "Lua_Chat_ForbidInput", "不在队伍中，无法操作"),
        Seconds = NSLOCTEXT("ChatModule", "Lua_Chat_Seconds", "%ss\""),

        NoFriend = NSLOCTEXT("ChatModule", "Lua_Chat_NoFriend", "暂时没有更多好友"),
        NoStranger = NSLOCTEXT("ChatModule", "Lua_Chat_NoStranger", "暂时没有更多玩家"),

        OnLine = NSLOCTEXT("ChatModule", "Lua_Chat_OnLine", "在线"),
        InMatch = NSLOCTEXT("ChatModule", "Lua_Chat_InMatch", "游戏中"),
        InTeam = NSLOCTEXT("ChatModule", "Lua_Chat_InTeam", "组队中"),
        OffLine = NSLOCTEXT("ChatModule", "Lua_Chat_OffLine", "离线"),

        StayTuned = NSLOCTEXT("ChatModule", "Lua_Chat_StayTuned", "敬请期待"),

        SeasonLevel = NSLOCTEXT("ChatModule", "Lua_Chat_SeasonLevel", "行动等级：Lv%s"),

        AppointContent = NSLOCTEXT("ChatModule", "Lua_Chat_AppointContent", "这局结束一起组队？"),

        Copy = NSLOCTEXT("ChatModule", "Lua_Chat_Copy", "复制"),
        Report = NSLOCTEXT("ChatModule", "Lua_Chat_Report", "举报"),

        VoiceTooShort = NSLOCTEXT("ChatModule", "Lua_Chat_VoiceTooShort", "语音时间太短，发送失败"),
        VoiceRecordComplete = NSLOCTEXT("ChatModule", "Lua_Chat_VoiceRecordComplete", "录制完毕，点击发送"),
        RichTxtVoiceImg = NSLOCTEXT("ChatModule","Lua_Chat_VoiceImg","%s<dfmrichtext type=\"img\" width=\"40\" height=\"28\" id=\"voice\"/>"),
        MsgMe = NSLOCTEXT("ChatModule", "Lua_Chat_MsgMe", "我"),
        MsgYellow = NSLOCTEXT("ChatModule", "Lua_Chat_MsgYellow", "<Chat_Near>%s</>"),
        MsgWhite1 = NSLOCTEXT("ChatModule", "Lua_Chat_MsgWhite1", "%s"),
        ThingsOfMine = NSLOCTEXT("ChatModule", "Lua_Chat_ThingsOfMine", "我这里有%s"),
        ItemTxt = NSLOCTEXT("ChatModule", "Lua_Chat_Item", "物资"),
        InviteYouNextGame = NSLOCTEXT("ChatModule", "Lua_Chat_InviteYouNextGame", "%s邀请你下局一起玩"),
        --聊天
        AppointAccept = NSLOCTEXT("ChatModule", "Lua_Chat_AppointAccept", "等我，已开局%s分钟，马上出来"),
        AppointReject = NSLOCTEXT("ChatModule", "Lua_Chat_AppointReject", "不好意思，不约"),
        ChatTo = NSLOCTEXT("ChatModule", "Lua_Chat_To", "%s发给 %s%s"),
        -- 新版pc频道默认输入框灰字提示
        SwitchChannelTip_PC = NSLOCTEXT("ChatModule", "Lua_Chat_SwitchChannelTip_PC", "Tab切换发言频道"),
        NotInTeamTip_PC = NSLOCTEXT("ChatModule", "Lua_Chat_NotInTeamTip_PC", "当前不在队伍中"),
        NotHavePrivateChatTip_PC = NSLOCTEXT("ChatModule", "Lua_Chat_NotHavePrivateChatTip_PC", "当前没有私聊对象"),
        -- BEGIN MODIFICATION - VIRTUOS
        -- 平台通信权限限制灰字提示
        NotHavePlatformPrivilegeTip = NSLOCTEXT("ChatModule", "Console_Lua_Chat_NotHavePlatformPrivilegeTip", "无法使用此功能，请检查Xbox通信权限设置"),
        NotHavePlatformPrivilegeTip_Sony = NSLOCTEXT("ChatModule", "Console_Lua_Chat_NotHavePlatformPrivilegeTip_Sony", "无法使用此功能，请检查家长控制相关设置"),
        UnableToPrivateChatWithTargetPlayer = NSLOCTEXT("ChatModule", "Console_Lua_Chat_UnableToPrivateChatWithTargetPlayer", "无法与对方私聊"),
        -- 在XSX/PS5不应出现Tab字段，缩减原文本
        SwitchChannelTip_XSX = NSLOCTEXT("ChatModule", "Console_Lua_Chat_SwitchChannelTip_XSX", "LS切换发言频道"),
        SwitchChannelTip_Sony = NSLOCTEXT("ChatModule", "Console_Lua_Chat_SwitchChannelTip_Sony", "L3切换发言频道"),
        -- END MODIFICATION - VIRTUOS

        NoVoiceMsgCurPlat_PC = NSLOCTEXT("ChatModule", "Lua_Chat_NoVoiceMsgCurPlat_PC", "语音消息，当前平台不支持播放"),
        MinorCannotSendMsg = NSLOCTEXT("ChatModule", "Lua_Chat_MinorCannotSendMsg", "当前版本针对部分地区的未成年玩家暂时不提供自由文本和语言聊天，敬请期待后续版本的更新"),
        ChatPrivateNoAnything = NSLOCTEXT("ChatModule", "Lua_Chat_ChatPrivateNoAnything", "请选择一位玩家进行聊天"),

        CampNPC = NSLOCTEXT("ChatModule", "Lua_Chat_CampNPC", "<customstyle color=\"Color_CampNPC\">阵营：</>"),
        ChatTeamLoc = NSLOCTEXT("ChatModule", "Lua_Chat_ChatTeam", "<customstyle color=\"Color_Chat_Team\">队伍：</>"),
        CampNPCLocText = NSLOCTEXT("ChatModule", "Lua_Chat_CampNPCLocText", "阵营"),
        ChatTeamLocText = NSLOCTEXT("ChatModule", "Lua_Chat_ChatTeamLocText", "队伍"),
        ChatNoChannelPCLocText = NSLOCTEXT("ChatModule", "Lua_Chat_ChatNoChannelPCLocText", "无频道"),

        MiniRecruitLabel_PC = NSLOCTEXT("ChatModule", "Lua_Chat_MiniRecruitLabel_PC", "{preLabels}、{labelName}"),
        MiniRecruitModeAndPeople_PC = NSLOCTEXT("ChatModule", "Lua_Chat_MiniRecruitModeAndPeople_PC","{mode}（{numerator}/{denominator}）"),
        MiniRecruitModeRule_PC = NSLOCTEXT("ChatModule", "Lua_Chat_MiniRecruitModeRule_PC","{modeAndPeople}{comma}{labels}  <dfmrichtext itemEnum=\"recruit\" style=\"Chat_Recruit\">[加入队伍]</>"),

        MinorGetAuthorized = NSLOCTEXT("ChatModule", "Lua_Chat_MinorGetAuthorized", "正在获取家长授权状态，请重试"),
        MinorNotAuthorized = NSLOCTEXT("ChatModule", "Lua_Chat_MinorNotAuthorized", "未获得聊天授权，请联系家长"),

        ChatWeaponCodeShareTips = NSLOCTEXT("ChatModule", "Lua_Chat_ChatWeaponCodeShareTips","{codeName}改枪码复制成功"),
        InGameInviteStateExpired    = NSLOCTEXT("ChatModule", "Lua_Chat_InGameInviteStateExpired","[已失效]"),
        InGameInviteStatePending    = NSLOCTEXT("ChatModule", "Lua_Chat_InGameInviteStatePending","[待回应]"),
        InGameInviteStateRejected   = NSLOCTEXT("ChatModule", "Lua_Chat_InGameInviteStateRejected","[已拒绝]"),
        InGameInviteStateAgreed     = NSLOCTEXT("ChatModule", "Lua_Chat_InGameInviteStateAgreed","[已同意]"),
        InGamePreBookInviteChat     = NSLOCTEXT("ChatModule", "Lua_Chat_InGamePreBookInviteChat", "预约邀请组队，下局一起游玩{gameModeSpace}{gameMode} {state}"),
        InGamePreBookInvite         = NSLOCTEXT("ChatModule", "Lua_Chat_InGamePreBookInvite", "预约下局一起游玩{gameModeSpace}{gameMode}"),
        InGamePreBookApplyChat      = NSLOCTEXT("ChatModule", "Lua_Chat_InGamePreBookApplyChat", "预约申请入队，下局一起游玩{gameModeSpace}{gameMode} {state}"),
        InGamePreBookInviteAbridget = NSLOCTEXT("ChatModule", "Lua_Chat_InGamePreBookInviteAbridget", "[可在{timeValue}秒内打开聊天回应预约]"),
        InGamePreBookPendingNum     = NSLOCTEXT("ChatModule", "Lua_Chat_InGamePreBookPendingNum", "{stableText} ({numText})"),
        InGamePreBookShotcutKeys    = NSLOCTEXT("ChatModule", "Lua_Chat_InGamePreBookShotcutKeys", "[{agreeKey}同意][{rejectKey}拒绝]"),
        InGameTeamNumPrefix         = NSLOCTEXT("ChatModule", "Lua_Chat_InGameTeamNumPrefix", "{teamText}{Symbol}{nickName}"),
    },

    EChannel2Source = {
        [ChatChannelType.WorldChat] = {FriendApplySource.WorldChatApply,TeamInviteSource.FromWorldChat},
        [ChatChannelType.CollegeChat] = {FriendApplySource.WorldChatApply,TeamInviteSource.FromWorldChat},
        [ChatChannelType.TeamChat] = {FriendApplySource.TeamApply,TeamInviteSource.FromWorldChat},
        [ChatChannelType.PrivateChatFriend] = {FriendApplySource.PrivateChatApply,TeamInviteSource.FromOther},
        [ChatChannelType.PrivateChatStanger] = {FriendApplySource.PrivateChatApply,TeamInviteSource.FromOther},
        [ChatChannelType.RecruitmentChat] = {FriendApplySource.WorldChatApply,TeamInviteSource.FromWorldChat},
    },

    evtLastChatFriendSelectedNo = LuaEvent:NewIns("evtLastChatFriendSelectedNo"),       --私聊对象改变,将上一次选中的对象设置为非选中
    evtForbidInput = LuaEvent:NewIns("evtForbidInput"),                                 --隐藏输入框
    evtRepeatSendLastMsg = LuaEvent:NewIns("evtRepeatSendLastMsg"),                     --相同消息+1发送
    evtInputEmoji = LuaEvent:NewIns("evtInputEmoji"),                                   --输入表情
    evtVoicePanelCDOver = LuaEvent:NewIns("evtVoicePanelCDOver"),                       --倒计时结束，发送语音
    evtAtPlayer = LuaEvent:NewIns("evtAtPlayer"),                                       --at XX玩家
    evtCopyText = LuaEvent:NewIns("evtCopyText"),                                       --复制文本
    evtVoiceReduceTime = LuaEvent:NewIns("evtVoiceReduceTime"),                         --语音播放倒计时
    evtStopPlayVoice = LuaEvent:NewIns("evtStopPlayVoice"),                             --停止正在播放的语音动画
    evtFriendStateUpdate = LuaEvent:NewIns("evtFriendStateUpdate"),                     --好友状态改变

    -- BEGIN MODIFICATION @ VIRTUOS : 
    -- 进入到局内
    evtEnteredGame = LuaEvent:NewIns("evtEnteredGame"),
    -- 离开局内
    evtLeftGame = LuaEvent:NewIns("evtLeftGame"),
    -- END MODIFICATION

    evtHiddenMsgWidget = LuaEvent:NewIns("evtHiddenMsgWidget"),
    evtOnChatMsgCellClicked = LuaEvent:NewIns("evtOnChatMsgCellClicked"),
    evtSelectMsgStateUI = LuaEvent:NewIns("evtSelectMsgStateUI"),
    evtShowHistoryStateChanged = LuaEvent:NewIns("evtShowHistoryStateChanged"),
    evtSpeakingDurationChange = LuaEvent:NewIns("evtSpeakingDurationChange"),

    evtChatFriendListUpdate         = LuaEvent:NewIns("evtChatFriendListUpdate"),
    evtChatStrangerListUpdate       = LuaEvent:NewIns("evtChatStrangerListUpdate"),
    evtPrivateMsgUpdateSucceed      = LuaEvent:NewIns("evtPrivateMsgUpdateSucceed"),
    evtModelReceiveNewChatPrivate   = LuaEvent:NewIns("evtModelReceiveNewChatPrivate"),
    evtModelChatRemoveSpeech        = LuaEvent:NewIns("evtModelChatRemoveSpeech"),
    evtPrivateMsgRemove             = LuaEvent:NewIns("evtPrivateMsgRemove"),
    evtChatFriendModelChange        = LuaEvent:NewIns("evtChatFriendModelChange"),
    evtReceiveNewWorldChatGroup     = LuaEvent:NewIns("evtReceiveNewWorldChatGroup"),
    evtReceiveNewCollegeChatGroup   = LuaEvent:NewIns("evtReceiveNewCollegeChatGroup"),
    evtReceiveNewTeamChatGroup      = LuaEvent:NewIns("evtReceiveNewTeamChatGroup"),
    evtFetchTeamChatMsgSucceed      = LuaEvent:NewIns("evtFetchTeamChatMsgSucceed"),
    evtOpenPCChatPanelByEnter       = LuaEvent:NewIns("evtOpenPCChatPanelByEnter"),
    evtReceiveNewMsgInMulti         = LuaEvent:NewIns("evtReceiveNewMSgInMulti"),
    evtPrivateMsgFirstGetSucceed    = LuaEvent:NewIns("evtPrivateMsgFirstGetSucceed"),
    evtPrivateMsgFirstGetMult       = LuaEvent:NewIns("evtPrivateMsgFirstGetMult"),
    evtReceiveNewChatPrivateMult    = LuaEvent:NewIns("evtReceiveNewChatPrivateMult"),
    evtFetchTeamChatSucceedMult     = LuaEvent:NewIns("evtFetchTeamChatSucceedMult"),
    evtReceiveNewTeamChatMult       = LuaEvent:NewIns("evtReceiveNewTeamChatMult"),
    evtOtherLogicTriggerChatPC      = LuaEvent:NewIns("evtOtherLogicTriggerChatPC"),
    evtChatBussinessCardState       = LuaEvent:NewIns("evtChatBussinessCardState"),
    evtChatApplyAddBussinessCard    = LuaEvent:NewIns("evtChatApplyAddBussinessCard"),

    evtChatTranslateText            = LuaEvent:NewIns("evtChatTranslateText"),
    evtTextTranslateSucceed         = LuaEvent:NewIns("evtTextTranslateSucceed"),
    evtTextTranslateFailed          = LuaEvent:NewIns("evtTextTranslateFailed"),
    evtRefreshItem                  = LuaEvent:NewIns("evtRefreshItem"),
    evtMiniRecruitModelUpdate       = LuaEvent:NewIns("evtMiniRecruitModelUpdate"),
    evtChatVoiceRecordStart         = LuaEvent:NewIns("evtChatVoiceRecordStart"),
    evtMultMiniRecruitModelUpdate   = LuaEvent:NewIns("evtMultMiniRecruitModelUpdate"),

    evtFriendChatChannelChange      = LuaEvent:NewIns("evtFriendChatChannelChange"),

    evtOnFrontChatMainPanelEnter    = LuaEvent:NewIns("evtOnFrontChatMainPanelEnter"),
    evtOnFrontChatMainPanelExit     = LuaEvent:NewIns("evtOnFrontChatMainPanelExit"),
    --BEGIN MODIFICATION @ VIRTUOS : 聊天界面退出期间的事件
    evtOnFrontChatMainPanelExiting  = LuaEvent:NewIns("evtOnFrontChatMainPanelExiting"),
    --END MODIFICATION

    -- 局内组队邀请状态更新事件
    evtInGameTeamInviteUpdate       = LuaEvent:NewIns("evtInGameTeamInviteUpdate"),
    evtInGamePreBookCardUpdate      = LuaEvent:NewIns("evtInGamePreBookCardUpdate"),
    evtNoReceiveIngameInvite        = LuaEvent:NewIns("evtNoReceiveIngameInvite"),

    --不同面板的世界频道拉取频率
    PanelToInterval = {
        ["WorldChannel"] = 1.0,
        ["Tip"] = 3.0,
        ["CollegeChannel"] = 4.0,
    },

    MAXCHATLEN = 128,    --最长输入文本

    MAXCHATLEN_HD = 128,    --PC最长输入文本

    OPENTRANSLATE = true,

    DEFAULTINTERVAL = 20.0,       --世界频道默认拉取间隔

    MINRECORDTIME = 0.5,         --最短录音时长

    -- 不同频道富文本样式
    EChatChannel2XML = {
        [ChatChannelType.MultiChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_MultiChat", "<customstyle color=\"Color_Chat_World\">%s</>"),
        [ChatChannelType.WorldChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_WorldChat", "<customstyle color=\"Color_Chat_World\">%s</>"),
        [ChatChannelType.CollegeChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_CollegeChat", "<customstyle color=\"Color_Chat_World\">%s</>"),
        [ChatChannelType.TeamChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_TeamChat", "<customstyle color=\"Color_Chat_Team\">%s</>"),
        [ChatChannelType.PrivateChatFriend] =  NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_PrivateChatFriend", "<customstyle color=\"Color_Chat_Friend\">%s</>"),
        [ChatChannelType.PrivateChatStanger] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_PrivateChatStanger", "<customstyle color=\"Color_Chat_Friend\">%s</>"),
        [ChatChannelType.RecruitmentChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2XML_WorldChat", "<customstyle color=\"Color_Chat_World\">%s</>"),
    },

    -- 不同频道富文本超链接样式
    EChatChannel2LinkXML = {
        [ChatChannelType.MultiChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_MultiChat", "<dfmrichtext style=\"Chat_World\">%s</>"),
        [ChatChannelType.WorldChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_WorldChat","<dfmrichtext style=\"Chat_World\">%s</>"),
        [ChatChannelType.CollegeChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_CollegeChat","<dfmrichtext style=\"Chat_World\">%s</>"),
        [ChatChannelType.TeamChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_TeamChat","<dfmrichtext style=\"Chat_Team\">%s</>"),
        [ChatChannelType.PrivateChatFriend] =  NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_PrivateChatFriend", "<dfmrichtext style=\"Chat_Friend\">%s</>"),
        [ChatChannelType.PrivateChatStanger] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_PrivateChatStanger", "<dfmrichtext style=\"Chat_Friend\">%s</>"),
        [ChatChannelType.RecruitmentChat] = NSLOCTEXT("ChatModule", "Lua_Chat_EChatChannel2LinkXML_RecruitmentChat", "<dfmrichtext itemEnum=\"recruit\" style=\"Chat_Recruit\">%s</>"),
    },

    -- 不同频道映射中文名称
    EChannel2CNName = {
        [ChatChannelType.MultiChat] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_MultiChat", "世界"),
        [ChatChannelType.WorldChat] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_WorldChat", "世界"),
        [ChatChannelType.CollegeChat] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_CollegeChat", "高校"),
        [ChatChannelType.TeamChat] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_TeamChat", "队伍"),
        [ChatChannelType.PrivateChatFriend] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_PrivateChatFriend", "私聊"),
        [ChatChannelType.PrivateChatStanger] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_PrivateChatStanger", "私聊"),
        [ChatChannelType.RecruitmentChat] = NSLOCTEXT("ChatModule", "Lua_Chat_ChatChannelType_RecruitmentChat", "招募"),
    },

    -- 特殊功能枚举查找表
    ESpecialRetrieve = {
        WorldChatTailInivte     = 1,  -- 世界频道小尾巴 "邀请组队"
        WorldChatTailApply      = 2,  -- 世界频道小尾巴 "申请入队"
    },

    ESpecialLinkCNName = {
        [1] = NSLOCTEXT("ChatModule", "Lua_Chat_ESpecialLinkCNName_WorldChatTailInivte", "[邀请组队]"),
        [2] = NSLOCTEXT("ChatModule", "Lua_Chat_ESpecialLinkCNName_WorldChatTailApply", "[申请入队]"),
    },

    -- 特殊功能映射的富文本超链接样式
    ESpecialLinkXML = {
        [1] = NSLOCTEXT("ChatModule", "Lua_Chat_ESpecialLinkXML_WorldChatTailInivte", "<dfmrichtext itemEnum=\"tailInvite\" style=\"Chat_Recruit\">%s</>"),
        [2] = NSLOCTEXT("ChatModule", "Lua_Chat_ESpecialLinkXML_WorldChatTailApply", "<dfmrichtext itemEnum=\"tailApply\" style=\"Chat_Recruit\">%s</>"),
    },

    -- 局内发言频道
    EGameSpeakChannelList = {
        DSChatType.DSChatTeam,
        DSChatType.DSChatCamp,
    },

    EGameSpeakChannelMapName = {
        [DSChatType.DSChatTeam] = NSLOCTEXT("ChatModule", "Lua_Chat_EGameSpeakChannelMapName_DSChatTeam", "队伍"),
        [DSChatType.DSChatCamp] = NSLOCTEXT("ChatModule", "Lua_Chat_EGameSpeakChannelMapName_DSChatCamp", "阵营"),
    },

    -- PC局外聊天展示状态枚举
    EFrontChatStatePC = {
        InVisibly   = 1,
        MultiLine   = 2,
        LessLine    = 3,
    },

    -- Emoji富文本样式配置
    EEmojiStyleConfig = {
        PC          = 'type="img" align="0" width="40" height="40"',
        Mobile      = 'type="img" align="0" width="60" height="60"',

        NewPC       = '<dfmrichtext id="%{emojiIndex}" type="img" align="0" width="40" height="40"/>',
        NewMobile   = '<dfmrichtext id="%{emojiIndex}" type="img" align="0" width="48" height="48"/>',
    },

    -- 改枪码映射

    -- 改枪码映射的小枪图标配置
    EWeaponSmallIconMapConfig = {
        ConstText = NSLOCTEXT("ChatModule", "Lua_Chat_EWeaponSmallIconMapConfig_ConstText","<dfmrichtext type=\"img\" id=\"{weaponId}\" align=\"0\"/>丨{weaponName}{extraSymbol}{inputText}  <dfmrichtext itemEnum=\"weaponCode\" style=\"Chat_Copy\">[复制改枪码]</>"),
        ConstTextSelf = NSLOCTEXT("ChatModule", "Lua_Chat_EWeaponSmallIconMapConfig_ConstTextSelf","<dfmrichtext type=\"img\" id=\"{weaponId}\" align=\"0\"/>丨{weaponName}{extraSymbol}{inputText}  <dfmrichtext itemEnum=\"weaponCode\" style=\"Chat_Self\">[复制改枪码]</>"),
        Rifle = 'Arms_Rifle',
        Submachine = 'Arms_SMG',
        Shotgun = 'Arms_Shotgun',
        PrecisionShootingRifle = 'Arms_MR',
        LightMachine = 'Arms_LMG',
        Sniper = 'Arms_SR',
        Pistol = 'Arms_Pistol',
        RifleSelf = 'Arms_Rifle_Black',
        SubmachineSelf = 'Arms_SMG_Black',
        ShotgunSelf = 'Arms_Shotgun_Black',
        PrecisionShootingRifleSelf = 'Arms_MR_Black',
        LightMachineSelf = 'Arms_LMG_Black',
        SniperSelf = 'Arms_SR_Black',
        PistolSelf = 'Arms_Pistol_Black',
    },

    --玩家聊天小窗
    EWindowBtnTypePC = {
        Normal = {
            HeadButtonType.PlayerInformat,
            HeadButtonType.AddFriend,
            HeadButtonType.InviteAppTeam,
            HeadButtonType.Chat,
            HeadButtonType.AddBlack,
            HeadButtonType.Report,
        },
        Private = {
            HeadButtonType.PlayerInformat,
            HeadButtonType.AddFriend,
            HeadButtonType.InviteAppTeam,
            HeadButtonType.Chat,
            HeadButtonType.AddBlack,
            HeadButtonType.Report,
        },
        Team = {
            HeadButtonType.PlayerInformat,
            HeadButtonType.AddFriend,
            HeadButtonType.Chat,
            HeadButtonType.AddBlack,
            HeadButtonType.Report,
        },

        BHDTeam = {
            HeadButtonType.AddFriend,
            HeadButtonType.Chat,
            HeadButtonType.AddBlack,
            HeadButtonType.Report,
            HeadButtonType.ReportVoice,
        },

        BHDPrivate = {
            HeadButtonType.AddFriend,
            HeadButtonType.InviteAppTeam,
            HeadButtonType.AddBlack,
            HeadButtonType.Report,
            HeadButtonType.ReportVoice,
        }

    },

    EGVoiceSpeechTranslateType = {
        SPEECH_TRANSLATE_STST = 0,	--Source Speech -> Source Text
        SPEECH_TRANSLATE_STTT = 1,  --Source Speech -> Source Text -> Target Text
        SPEECH_TRANSLATE_STTS = 2,	--Source Speech -> Source Text -> Target Text -> Target Speech
    },

    --GVocie翻译语言枚举
    GVoiceLangCodeMap = {
        ["zh"] = 0, --EGVoiceLangType.SPEECH_LANGUAGE_ZH,
        ["zh-CN"] = 0, --EGVoiceLangType.SPEECH_LANGUAGE_ZH,
        ["zh-Hans"] = 0, --EGVoiceLangType.SPEECH_LANGUAGE_ZH,
        ["zh-HK"] = 15, --EGVoiceLangType.SPEECH_LANGUAGE_ZH_TW,
        ["zh-Hant"] = 15, --EGVoiceLangType.SPEECH_LANGUAGE_ZH_TW,
        ["zh-TW"] = 15, --EGVoiceLangType.SPEECH_LANGUAGE_ZH_TW,
        ["en"] = 1, --EGVoiceLangType.SPEECH_LANGUAGE_EN,
        ["ja"] = 2, --EGVoiceLangType.SPEECH_LANGUAGE_JA,
        ["th"] = 14, --EGVoiceLangType.SPEECH_LANGUAGE_TH,
        ["id"] = 12, --EGVoiceLangType.SPEECH_LANGUAGE_ID,
        ["pt"] = 10, --EGVoiceLangType.SPEECH_LANGUAGE_PT,
        ["ko"] = 3, --EGVoiceLangType.SPEECH_LANGUAGE_KO,
        ["es"] = 6, --EGVoiceLangType.SPEECH_LANGUAGE_ES,
        ["de"] = 4, --EGVoiceLangType.SPEECH_LANGUAGE_DE,
        ["fr"] = 5, --EGVoiceLangType.SPEECH_LANGUAGE_FR,
        ["ru"] = 9, --EGVoiceLangType.SPEECH_LANGUAGE_RU,
        ["tr"] = 8, --EGVoiceLangType.SPEECH_LANGUAGE_TR,
        ["it"] = 7, --EGVoiceLangType.SPEECH_LANGUAGE_IT,
        ["ar"] = 19, --EGVoiceLangType.SPEECH_LANGUAGE_AR,
        ["ms"] = 13, --EGVoiceLangType.SPEECH_LANGUAGE_MS,
        ["pl"] = 57, --EGVoiceLangType.SPEECH_LANGUAGE_PL,
        ["sv"] = 66, --EGVoiceLangType.SPEECH_LANGUAGE_SV,
        ["uk"] = 69, --EGVoiceLangType.SPEECH_LANGUAGE_UK,
        ["el"] = 37, --EGVoiceLangType.SPEECH_LANGUAGE_EL,
        ["vi"] = 11, --EGVoiceLangType.SPEECH_LANGUAGE_VI,
        ["nl"] = 31, --EGVoiceLangType.SPEECH_LANGUAGE_NL,
        ["no"] = 55, --EGVoiceLangType.SPEECH_LANGUAGE_NO,
        ["da"] = 30, --EGVoiceLangType.SPEECH_LANGUAGE_DA,
        ["ro"] = 59, --EGVoiceLangType.SPEECH_LANGUAGE_RO,
        ["sk"] = 62, --EGVoiceLangType.SPEECH_LANGUAGE_SK,
    },

    ETextTranslateState = {
        FucNotSupport = 1,
        NotTranslated = 2,
        Translating = 3,
        Translated = 4
    },

    ChatMainIcon_PC = {
        [0] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0000.Common_ItemClass_Icon_0000'",
        [1] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Login_Icon_0104.CommonHall_Login_Icon_0104'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_College.CommonHall_Icon_College'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0307.Common_PlayerState_Icon_0307'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/System/Chat/BakedSprite/Chat_PlayerState_Icon_0322.Chat_PlayerState_Icon_0322'",
        [5] = "PaperSprite'/Game/UI/UIAtlas/System/Chat/BakedSprite/Chat_Icon_0110.Chat_Icon_0110'",
    }

}

return ChatConfig
