----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLooting)
----- LOG FUNCTION AUTO GENERATE END -----------



local WarehouseEquipSlotView = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipSlotView"
local LootEquipSlotPanel = require "DFM.Business.Module.LootingModule.UI.Common.LootEquipSlotPanel"
local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"
local WarehouseContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD"
local LootCurrencyWidget = require "DFM.Business.Module.LootingModule.UI.Common.LootCurrencyWidget"

local LootEquipPanel_HD = require "DFM.Business.Module.LootingModule.UIHD.LootEquipPanel_HD"
local LootBackpackPanelV2_HD = require "DFM.Business.Module.LootingModule.UIHD.LootBackpackPanelV2_HD"

local EDescendantScrollDestination = import "EDescendantScrollDestination"

---@class LootDeadbodyPanel_HD : LuaUIBaseView
local LootDeadbodyPanel_HD = ui("LootDeadbodyPanel_HD")

local function log(...)
    loginfo("[LootDeadbodyPanel_HD]", ...)
end

function LootDeadbodyPanel_HD:Ctor()
    self._delayItemMoveHandle = nil
    self._wtMainScrollBox = self:Wnd("wtMainScrollBox", UIScrollBox)
    self._wtMainScrollBox:Event("OnUserScrolled", self._CheckScrollBoxAtBottom, self)
    self._wtMainScrollBox:SetAnimateWheelScrolling(false)
    self._wtScrollMask = self:Wnd("wtScrollMask", UIWidgetBase)
    self._wtLootCurrencyWidget = self:Wnd("WBP_LootPriceWidget_V2_Pc", LootCurrencyWidget)
    ---@type WarehouseContainerPanel_HD[]
    self._containerPanels = setmetatable({}, weakmeta)

    self._wtDeadbodyEquipPanel = self:Wnd("wtDeadbodyEquipPanel", LootEquipPanel_HD)
    self._wtDeadbodyEquipPanel:BindScrollBox(self._wtMainScrollBox)

    self:InitSlotViews()

    for _, containerPanel in ipairs(self._containerPanels) do
        containerPanel:BindScrollBox(self._wtMainScrollBox)
    end
end

function LootDeadbodyPanel_HD:OnShowBegin()
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Module.Looting.Config.Events.evtScrollToItemSlot, self._ScrollToItemSlot, self)
    self:AddLuaEvent(Module.Guide.Config.EGuideEvent.evtGuideModification,self._OnGuideModification, self)
    self:RefreshAppearance()
    self:RefreshView()
end

function LootDeadbodyPanel_HD:OnHide()
    self:RemoveAllLuaEvent()
    self._wtMainScrollBox:ScrollToStart()
end

function LootDeadbodyPanel_HD:RefreshView()
    self._wtDeadbodyEquipPanel:RefreshView()
    self._wtLootCurrencyWidget:RefreshView()
end

function LootDeadbodyPanel_HD:RefreshAppearance()
    self._wtMainScrollBox:ScrollToStart()

    if Module.Looting:CheckDeadbodyShouldHaveArchiveContainer() then
        self._wtArchiveContainerView:SelfHitTestInvisible()
    else
        self._wtArchiveContainerView:Collapsed()
    end
    self:_CheckScrollBoxAtBottom()
    self._wtLootCurrencyWidget:RefreshView()
end

function LootDeadbodyPanel_HD:InitSlotViews()
    self._wtCHContainerView = self:Wnd("wtCHContainerView", WarehouseContainerPanel_HD)
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, ESlotGroup.DeadBody)
    table.insert(self._containerPanels, self._wtCHContainerView)
    self._wtPocketContainerView = self:Wnd("wtPocketContainerView", WarehouseContainerPanel_HD)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, ESlotGroup.DeadBody)
    table.insert(self._containerPanels, self._wtPocketContainerView)
    self._wtArchiveContainerView = self:Wnd("wtArchiveContainerView", WarehouseContainerPanel_HD)
    self._wtArchiveContainerView:InitContainerSlot(ESlotType.ArchiveContainer, ESlotGroup.DeadBody)
    table.insert(self._containerPanels, self._wtArchiveContainerView)
    self._wtBagContainerView = self:Wnd("wtBagContainerView", WarehouseContainerPanel_HD)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, ESlotGroup.DeadBody)
    table.insert(self._containerPanels, self._wtBagContainerView)
end

function LootDeadbodyPanel_HD:_ScrollToItemSlot(slotType, slotGroup)
    if slotGroup ~= ESlotGroup.DeadBody then
        return
    end
    local targetScroll2Widget = self._wtDeadbodyEquipPanel:GetSlotViewByType(slotType)
    if not targetScroll2Widget then
        if slotType == ESlotType.ChestHanging or slotType == ESlotType.ChestHangingContainer then
            targetScroll2Widget = self._wtCHContainerView
        elseif slotType == ESlotType.Pocket then
            targetScroll2Widget = self._wtPocketContainerView
        elseif slotType == ESlotType.Bag or slotType == ESlotType.BagContainer then
            targetScroll2Widget = self._wtBagContainerView
        elseif slotType == ESlotType.ArchiveContainer then
            targetScroll2Widget = self._wtArchiveContainerView
        end
    end
    if targetScroll2Widget then
        self._wtMainScrollBox:EndInertialScrolling()
        self._wtMainScrollBox:ScrollWidgetIntoView(targetScroll2Widget, true, EDescendantScrollDestination.IntoView)
    end
end

function LootDeadbodyPanel_HD:FindItemViewByGID(gid)
    local itemView = self._wtDeadbodyEquipPanel:FindItemViewByGID(gid)
    if not itemView then
        for _, view in ipairs(self._containerPanels) do
            local itemView = view:FindItemViewByGID(gid)
            if itemView then
                return itemView
            end
        end
    end
    return itemView
end

function LootDeadbodyPanel_HD:_PostSlotRefresh()
	-- local allItemViews = {}
	-- for slotType, slot in pairs(self._slotMapping) do
	-- 	if slot.item then
	-- 		table.insert(allItemViews, slot:GetMainItemView())
	-- 	end
	-- end

	-- for item, itemView in pairs(self._wtBagSlotView:GetAllItemViews()) do
	-- 	table.insert(allItemViews, itemView)
	-- end

	-- Module.Looting:ProcessGuideLogic(allItemViews)
end

function LootDeadbodyPanel_HD:_CheckScrollBoxAtBottom()
    LootingLogic.CheckScrollBoxMaskLogic(self._wtMainScrollBox, self._wtScrollMask)

    if self._bDisableScroll and self._lockScrollOffset then
        self._wtMainScrollBox:ScrollToOffset(self._lockScrollOffset)
    end
end

function LootDeadbodyPanel_HD:SetScrollEnable(bEnable)
    self._bDisableScroll = not bEnable
    self._lockScrollOffset = self._wtMainScrollBox:GetScrollOffset()
end

---@param states table<GuideConfig.EExternalModification,boolean>
function LootDeadbodyPanel_HD:_OnGuideModification(key, states)
    if key == "recover" then
        if true == states[Module.Guide.Config.EExternalModification.LootingDeadBodyPanelScrollEnable] then
            -- 恢复异常状态
            self:SetScrollEnable(true)
        end
    end
end

---@param item ItemBase
function LootDeadbodyPanel_HD:_DelayProcessItemMove(itemMoveInfo, reason)
    local targetScroll2Widget = self:FindItemViewByGID(itemMoveInfo.item.gid)
    LootingLogic.ScrollToTargetWidget(self._wtMainScrollBox, itemMoveInfo, reason, targetScroll2Widget)
end

function LootDeadbodyPanel_HD:_OnItemMove(itemMoveInfo)
    local item = itemMoveInfo.item
	---@type ItemLocation
	local newLoc = itemMoveInfo.NewLoc
	local newSlot = newLoc and newLoc.ItemSlot or nil
	---@type ItemLocation
	local oldLoc = itemMoveInfo.OldLoc
	local oldSlot = oldLoc and oldLoc.ItemSlot or nil
    local reason = itemMoveInfo.Reason
	if newSlot and newSlot:GetSlotGroup() == ESlotGroup.DeadBody then
        self:_DelayProcessItemMove(itemMoveInfo, reason)
        --Timer.CancelDelay(self._delayItemMoveHandle)
        --self._delayItemMoveHandle = nil
        --self._delayItemMoveHandle = Timer.DelayCall(Module.Looting.Config.SCROLL_DELAY_SECONDS,
        --        self._DelayProcessItemMove, self, item, reason)
	end
end


return LootDeadbodyPanel_HD