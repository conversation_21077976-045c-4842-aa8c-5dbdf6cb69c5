----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionMysticalSkinWorkshopPage = ui("CollectionMysticalSkinWorkshopPage")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CommonItemHighlight = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.CommonItemHighlight"
local ETipsTriggerReason = import("ETipsTriggerReason")
local EGPInputModeType = import "EGPInputModeType"
local CollectionConfig = Module.Collection.Config

function CollectionMysticalSkinWorkshopPage:Ctor()
    self._wtDropDown = UIUtil.WndDropDownBox(self, "wtDropDown", self._OnSortOptionTabIndexChanged)
    self._wtFilterBtn = self:Wnd("wtFilterBtn", DFCommonButtonOnly)
    self._wtFilterBtn:Event("OnClicked",self._OnFilterBtnClick,self)
    self._wtEmptyBtn = self:Wnd("wtEmptyBtn", DFCommonButtonOnly)
    self._wtEmptyBtn:Event("OnClicked",self._OnEmptyBtnClick,self)
    self._wtSourceItemGridBox = UIUtil.WndScrollGridBox(self, "wtSourceItemGridBox", self._OnGetSourceItemsCount, self._OnProcessSourceItemWidget)
    self._wtSourceDragSelectBox = self:Wnd("wtSourceDragSelectBox", CommonItemHighlight)
    self._wtTargetItemGridBox = UIUtil.WndScrollGridBox(self, "wtTargetItemGridBox", self._OnGetTargetItemsCount, self._OnProcessTargetItemWidget)
    self._wtTargetDragSelectBox = self:Wnd("wtTargetDragSelectBox", CommonItemHighlight)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self._wtRecombineDesTxt = self:Wnd("wtRecombineDesTxt", UITextBlock)
    self._wtRecombineDesTxt:SetText(CollectionConfig.Loc.MysticalSkinRecombineDes)
    self._wtRecombineNumTxt = self:Wnd("wtRecombineNumTxt", UITextBlock)
    self._wtRecombineResultHintTxt = self:Wnd("wtRecombineResultHintTxt", UITextBlock)
    self._wtRecombineResultHintTxt:SetText(CollectionConfig.Loc.MatrixRecombinationHintt)
    self._wtClearTargetListBtn = self:Wnd("wtClearTargetListBtn", DFCommonButtonOnly)
    self._wtClearTargetListBtn:Event("OnClicked", self._OnResetMysticalSkinPool, self)
    self._wtFillTargetListBtn = self:Wnd("wtFillTargetListBtn", DFCommonButtonOnly)
    self._wtFillTargetListBtn:Event("OnClicked", self._OnFillMysticalSkinPool, self)
    self._wtFillTargetListBtn:Event("OnDeClicked", self._OnFillTargetListBtnDeClicked, self)
    self._wtRecombineBtn = self:Wnd("wtRecombineBtn", DFCommonButtonOnly)
    self._wtRecombineBtn:Event("OnClicked", self._OnRecombineMysticalSkins, self)
    self._wtRecombineBtn:Event("OnDeClicked", self._OnRecombineBtnDeClicked, self)
    self._wtRecombineBtn:SetMainTitle(CollectionConfig.Loc.Recombine)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtSourceEmptyHint = self:Wnd("wtSourceEmptyHint", UIWidgetBase)
    self._wtSourceEmptyHint:SetCppValue("Set_Type", 1)
    self._wtSourceEmptyHint:BP_Set_Type()
    self._wtTargetEmptyHint = self:Wnd("wtTargetEmptyHint", UIWidgetBase)
    self._wtTargetEmptyHint:SetCppValue("Set_Type", 4)
    self._wtTargetEmptyHint:SetCppValue("Text", CollectionConfig.Loc.MysticalSkinAddToRecombineHint)
    self._wtTargetEmptyHint:BP_Set_Type()
    self._wtVectorAnimComp = self:Wnd("wtVectorAnimComp", UIWidgetBase)
    self:SetMode(0)
    self._dropDownIndex = -1
    self._sortTitleList = {}
    self._srotFuncList = {}
    self._sourceWeaponSkinItems = {}
    self._targetWeaponSkinItems = {}
    self._selectedSourcePos = -1
    self._selectedTargetPos = -1
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    if IsHD() then
        self._wtFillTargetListBtn:Collapsed()
        Module.CommonBar:RegStackUIInputSummary(self,
        {{actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalSkinPool, caller = self ,bUIOnly = false, bHideIcon = false}})
    end
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    Module.CommonBar:RegStackUITopBarTitle(self,  CollectionConfig.Loc.MatrixWorkshop)
    self:SetType(1)
end


function CollectionMysticalSkinWorkshopPage:OnInitExtraData(mysticalSkinItem)
    self._defaultAddedItem = mysticalSkinItem
    self._sortTitleList = {}
    table.insert(self._sortTitleList, CollectionConfig.Loc.QualitySortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.QualitySortDecend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.WearSortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.WearSortDecend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.GainTimeSortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.GainTimeSortDecend)
    self._srotFuncList = {}
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinQualitySortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinQualitySortDecend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinWearSortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinWearSortDecend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinGainTimeSortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinGainTimeSortDecend)
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionMysticalSkinWorkshopPage:OnOpen()
    CollectionLogic.ClearAllMysticalSkinFilterData()
    self:AddListeners()
    local gameInst = GetGameInstance()
    self._buttonUpHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
    self:RefreshView()
end

---@overload fun(LuaUIBaseView, OnClose)
function CollectionMysticalSkinWorkshopPage:OnClose()
    self:RemoveAllLuaEvent()
    if self._buttonUpHandle then
        local gameInst = GetGameInstance()
		UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._buttonUpHandle)
        self._buttonUpHandle = nil
	end 
end

---@overload fun(LuaUIBaseView, OnShow)
function CollectionMysticalSkinWorkshopPage:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionMysticalSkinWorkshopPage:OnHide()
    self:_OnHideInstruction()
    self:_StopDragTimer()
end


-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionMysticalSkinWorkshopPage:OnAnimFinished(anim)
end


function CollectionMysticalSkinWorkshopPage:AddListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
	self:AddLuaEvent(CollectionConfig.Events.evtOnAddWorkshopItemToTargetPool, self._AddToTargetList, self)
    self:AddLuaEvent(CollectionConfig.Events.evtOnRemoveWorkshopItemFromTargetPool, self._RemoveFromTargetList, self)
    self:AddLuaEvent(CollectionConfig.Events.evtOnWorkshopFilterUpdated, self._OnWorkshopFilterUpdated, self)
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnWeaponSkinApplied, self)
end


function CollectionMysticalSkinWorkshopPage:RefreshView()
    self._selectedSourcePos = Module.Collection.Field:GetSelectedPos()
    if self._selectedSourcePos == -1 then
        CollectionLogic.ClearAllMysticalSkinFilterData()
        self._dropDownIndex = 1
        self._targetWeaponSkinItems = {}
        UIUtil.InitDropDownBox(self._wtDropDown, self._sortTitleList, {}, 0)
    end
    self:_OnRefreshWeaponSkinItems()
end

function CollectionMysticalSkinWorkshopPage:_OnRefreshWeaponSkinItems()
    if self._wtVectorAnimComp then
        self._wtVectorAnimComp:SetType(1)
    end
    self._wtDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._wtDropDown:BP_SetMainTabText(self._sortTitleList[self._dropDownIndex])
    self._wtTargetItemGridBox:Visible()
    if self._defaultAddedItem ~= nil and Server.CollectionServer:IsOwnedWeaponSkin(self._defaultAddedItem.id, self._defaultAddedItem.gid) == true then
        table.insert(self._targetWeaponSkinItems, self._defaultAddedItem)
        self._defaultAddedItem = nil
    end
    if #self._targetWeaponSkinItems > 0 then
        local validTargetSkins = {}
        for index, skinItem in ipairs(self._targetWeaponSkinItems) do
            if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id, skinItem.gid) == true then
                table.insert(validTargetSkins, skinItem)
            end
        end
        self._targetWeaponSkinItems = validTargetSkins
    end
    local param = {
        ["countNum"] = tostring(#self._targetWeaponSkinItems),
        ["maxNum"] = tostring(CollectionConfig.RecombineNum)
    }
    self._wtRecombineNumTxt:SetText(StringUtil.Key2StrFormat(Module.Collection.Config.Loc.NumFormat, param))
    local filterData = CollectionLogic.GetLastMysticalSkinFilterData()
    local selectedQualityIDs = filterData.selectedQualityIDs
    local selectedGunTypeIDs = filterData.selectedGunTypeIDs
    local seasonChoice = filterData.seasonChoice
    local weaponChoice = filterData.weaponChoice
    local wearRange = filterData.wearRange
    self._bShowWearDetailValueOnItem = wearRange.min > 0 or wearRange.max < 5
    local ownedMysticalSkinsWithType = CollectionLogic.GetWeaponSkins(CollectionConfig.EItemGroup.Owned, CollectionConfig.EItemType.Mystical, nil, nil, nil, seasonChoice)
    if table.nums(selectedQualityIDs) > 0 then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            if selectedQualityIDs[skinItem.quality] ~= nil then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    if table.nums(selectedGunTypeIDs) > 0 then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            if selectedGunTypeIDs[skinItem.itemSubType] ~= nil then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    if weaponChoice ~= nil and weaponChoice > 0 then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            if CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id) == weaponChoice then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    if wearRange ~= nil and (wearRange.min > 0 or wearRange.max < 1) then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            local wearValue = skinItem:GetRawPropInfo().mystical_skin_data.wear/ItemConfigTool.GetMysticalWearRate()
            if wearValue >= wearRange.min and wearValue <= wearRange.max then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    self._sourceWeaponSkinItems = {}
    for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
        local bShouldIgnore = false
        for index, targetSkinItem in ipairs(self._targetWeaponSkinItems) do
            if skinItem.id == targetSkinItem.id and skinItem.gid == targetSkinItem.gid or skinItem.quality ~= targetSkinItem.quality then
                bShouldIgnore = true
                break
            end
        end
        if bShouldIgnore == false then
            table.insert(self._sourceWeaponSkinItems, skinItem)
        end
    end
    table.sort(self._sourceWeaponSkinItems, self._srotFuncList[self._dropDownIndex])
    local topItem = self._targetWeaponSkinItems[1]
    self._wtQualityIcon:Collapsed()
    if topItem ~= nil then
        self._wtQualityIcon:AsyncSetImagePath(CollectionConfig.QualityIconMapping[topItem.quality])
        self._wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(topItem.quality))
        self._wtQualityIcon:SelfHitTestInvisible()
    end
    if self._selectedSourcePos == -1 then
        self._wtSourceItemGridBox:RefreshAllItems()
        self._wtTargetItemGridBox:RefreshAllItems()
    else
        self._wtSourceItemGridBox:RefreshVisibleItems()
        self._wtTargetItemGridBox:RefreshVisibleItems()
    end
    if #self._sourceWeaponSkinItems == 0 then
        self._wtSourceEmptyHint:SelfHitTestInvisible()
    else
        self._wtSourceEmptyHint:Collapsed()
    end
    if #self._targetWeaponSkinItems == 0 then
        self._wtTargetEmptyHint:SelfHitTestInvisible()
        self._wtTargetItemGridBox:Collapsed()
    else
        self._wtTargetEmptyHint:Collapsed()
        self._wtTargetItemGridBox:Visible()
    end
    self:_RefreshActionBtns()
end

function CollectionMysticalSkinWorkshopPage:_OnGetTabItemCount()
    return #self._sortTitleList
end


function CollectionMysticalSkinWorkshopPage:_OnSortOptionTabIndexChanged(position)
    if self._dropDownIndex ~= position + 1 then
        self._dropDownIndex = position + 1
        self._wtDropDown:BP_SetMainTabText(self._sortTitleList[self._dropDownIndex])
        self:_OnRefreshWeaponSkinItems()
    end
end

function CollectionMysticalSkinWorkshopPage:_OnGetSourceItemsCount()
    return #self._sourceWeaponSkinItems or 0
end

function CollectionMysticalSkinWorkshopPage:_OnProcessSourceItemWidget(position, itemWidget)
    local index = position + 1
    local item = self._sourceWeaponSkinItems[index]
    if isvalid(item) then
        local bLocked = false
        local lockText = ""
        if #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= item.quality then
            bLocked = true
            lockText = CollectionConfig.Loc.QualityDifferent
        end
        if item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
            bLocked = true
            lockText = CollectionConfig.Loc.CannotRecombine
        end
        itemWidget:BindClickCallback(CreateCallBack(self._OnSourceWeaponSkinItemClick, self, itemWidget, position))
        itemWidget:BindDetailCallback(CreateCallBack(self._OpenDetailPanel, self, item, itemWidget, true))
        if bLocked == false then
            itemWidget:BindDoubleClickCallback(CreateCallBack(self._AddToTargetList, self, item))
            itemWidget:BindDragCallback(CreateCallBack(self._OnDragItem, self, item, true))
        else
            itemWidget:BindDoubleClickCallback(nil)
            itemWidget:BindDragCallback(nil)
        end
        if IsHD() then
            local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local rarity = 0
            local mysticalInfo = item:GetRawPropInfo().mystical_skin_data
            if mysticalInfo then
                rarity = mysticalInfo.rarity
                if Module.Inventory.Config.MysticalSkinRarityTxtMapping[rarity] then
                    rateText = Module.Inventory.Config.MysticalSkinRarityTxtMapping[rarity]
                end
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    wearText = ItemConfigTool.GetMysticalWearConfig(mysticalInfo.wear) or ""
                    wearText = string.format(Module.ItemDetail.Config.Loc.MysticalSkinWearWithValue, wearText, wear)
                end
            end
            local bShowUniqueNumRarityWear = rarity == MysticalSkinRarityType.RarityHigh
            local bShowUniqueNumRarityWearKillCount = rarity == MysticalSkinRarityType.RarityPremium or rarity == MysticalSkinRarityType.RarityExtraordinary
            local bShowWearOnly = item.quality >= ItemConfig.EWeaponSkinQualityType.Unknown and item.quality < ItemConfig.EWeaponSkinQualityType.Orange
            seasonIndex = 1
            local skinConfig = CollectionLogic.GetWeaponSkinDataRow(item.id)
            if skinConfig then 
                local seasonIdList = CollectionLogic.GetWeaponSkinSeasonIDList()
                for index, seasonId in ipairs(seasonIdList) do
                    if skinConfig.SeasonID == seasonId then 
                        seasonIndex = index
                        break
                    end
                end
            end
            local instruction = {
                {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = mysticalInfo.custom_name ~= "" and mysticalInfo.custom_name or item.name}},
                {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = StringUtil.Key2StrFormat(CollectionConfig.Loc.SeasonTip,{["SeasonIndex"] = seasonIndex})}, styleRowId = "C001"},
            }
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.UniqueNum, value = tostring(mysticalInfo.unique_no)}}
                )
                table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Rarity, value = rateText}}
            )
            end
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true or bShowWearOnly == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Wear, value = wearText}}
                )
            end
            if bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.KillCount, value = MathUtil.GetNumberFormatStr(mysticalInfo.kill_cnter)}}
                )
            end
            table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = "MysticalWorkshop_ClickToRemove"}}}}
            )
            itemWidget:SetGivenInstructionContents(instruction)
        end
        itemWidget._wtBtnItem:Collapsed()
        itemWidget:SetCppValue("bHandleClick" , true)
        local itemWearText = ""
        if self._bShowWearDetailValueOnItem == true then
            if mysticalInfo then
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    itemWearText = StringUtil.Key2StrFormat(CollectionConfig.Loc.ItemDetailWear, {["value"] = wear})
                end
            end
        end
        itemWidget:InitCollectionMysticalSkinItem(item, CollectionLogic.GetBaseWeaponIdFromSkinId(item.id), CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid), bLocked, lockText, self._bShowWearDetailValueOnItem and itemWearText or nil)
    end
    if self._selectedSourcePos == position then
        itemWidget:SetSelected(item, true)
    else
        itemWidget:SetSelected(nil, false)
    end
    itemWidget:Visible()
end

function CollectionMysticalSkinWorkshopPage:_OnSourceWeaponSkinItemClick(itemCell, position)
    local index = position + 1
    if isvalid(self._selectedTargetCell) then
        self._selectedTargetCell:SetSelected(nil, false)
    end
    if isvalid(self._selectedSourceCell) then
        if self._selectedSourcePos ~= position then
            self._selectedSourceCell:SetSelected(nil, false)
        end
    end
    local item = self._sourceWeaponSkinItems[index]
    self._selectedSourceCell = itemCell
    if self._selectedSourcePos ~= position then
        self._bApplied = false
        self._selectedSourceCell:SetSelected(item, true)
    end
    self._selectedSourcePos = position
    self._selectedTargetPos = -1
    self._lastSelectedSourcePos = nil
end

function CollectionMysticalSkinWorkshopPage:_OnGetTargetItemsCount()
    return CollectionConfig.RecombineNum or 0
end

function CollectionMysticalSkinWorkshopPage:_OnProcessTargetItemWidget(position, itemWidget)
    local index = position + 1 
    local item = self._targetWeaponSkinItems[index]
    if isvalid(item) then
        local itemWearText = nil
        itemWidget:BindClickCallback(CreateCallBack(self._OnTargetWeaponSkinItemClick, self, itemWidget, position))
        local mysticalInfo = item:GetRawPropInfo().mystical_skin_data
        itemWidget:BindDetailCallback(CreateCallBack(self._OpenDetailPanel, self, item, itemWidget, false))
        itemWidget:BindDoubleClickCallback(CreateCallBack(self._RemoveFromTargetList, self, item))
        itemWidget:BindDragCallback(CreateCallBack(self._OnDragItem, self, item, false))
        if IsHD() then
            local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local ratityID = 0
            if mysticalInfo then
                if mysticalInfo.appearance then
                    ratityID = ItemConfigTool.GetItemRatityID(mysticalInfo.appearance.id)
                    if Module.Inventory.Config.MysticalSkinRarityTxtMapping[ratityID] then
                        rateText = Module.Inventory.Config.MysticalSkinRarityTxtMapping[ratityID]
                    end
                end
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    wearText = ItemConfigTool.GetMysticalWearConfig(mysticalInfo.wear) or ""
                    wearText = string.format(Module.ItemDetail.Config.Loc.MysticalSkinWearWithValue, wearText, wear)
                end
            end
            local bShowUniqueNumRarityWear = ratityID == MysticalSkinRarityType.RarityHigh
            local bShowUniqueNumRarityWearKillCount = ratityID == MysticalSkinRarityType.RarityPremium or ratityID == MysticalSkinRarityType.RarityExtraordinary
            seasonIndex = 1
            local skinConfig = CollectionLogic.GetWeaponSkinDataRow(item.id)
            if skinConfig then 
                local seasonIdList = CollectionLogic.GetWeaponSkinSeasonIDList()
                for index, seasonId in ipairs(seasonIdList) do
                    if skinConfig.SeasonID == seasonId then 
                        seasonIndex = index
                        break
                    end
                end
            end
            local instruction = {
                {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = mysticalInfo.custom_name ~= "" and mysticalInfo.custom_name or item.name}},
                {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = StringUtil.Key2StrFormat(CollectionConfig.Loc.SeasonTip,{["SeasonIndex"] = seasonIndex})}, styleRowId = "C001"},
            }
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.UniqueNum, value = tostring(mysticalInfo.unique_no)}}
                )
                table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Rarity, value = rateText}}
            )
            end
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true or bShowWearOnly == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Wear, value = wearText}}
                )
            end
            if bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.KillCount, value = MathUtil.GetNumberFormatStr(mysticalInfo.kill_cnter)}}
                )
            end
            if bLocked == false then
                table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = "MysticalWorkshop_ClickToAdd"}}}}
            )
            end
            itemWidget:SetGivenInstructionContents(instruction)
        end
        if self._bShowWearDetailValueOnItem == true then
            if mysticalInfo then
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    itemWearText = StringUtil.Key2StrFormat(CollectionConfig.Loc.ItemDetailWear, {["value"] = wear})
                end
            end
        end
        itemWidget:InitCollectionMysticalSkinItem(item, CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid), nil, nil, self._bShowWearDetailValueOnItem and itemWearText or nil)
    else
        itemWidget:InitCollectionMysticalSkinItem()
    end
    if self._selectedTargetPos == position then
        itemWidget:SetSelected(item, true)
    else
        itemWidget:SetSelected(nil, false)
    end
    itemWidget:Visible()
end

function CollectionMysticalSkinWorkshopPage:_OnTargetWeaponSkinItemClick(itemCell, position)
    local index = position + 1
    if isvalid(self._selectedSourceCell) then
        self._selectedSourceCell:SetSelected(nil, false)
    end
    if isvalid(self._selectedTargetCell) then
        if self._selectedTargetPos ~= position then
            self._selectedTargetCell:SetSelected(nil, false)
        end
    end
    local item = self._targetWeaponSkinItems[index]
    if item then
        self._selectedTargetCell = itemCell
        if self._selectedTargetPos ~= position then
            self._bApplied = false
            self._selectedTargetCell:SetSelected(item, true)
        end
        self._selectedTargetPos = position
    else
        if self._selectedSourcePos ~= -1 and self._sourceDragItem == nil then
            self:_AddToTargetList(self._sourceWeaponSkinItems[self._selectedSourcePos+1])
        end
    end
    self._selectedSourcePos = -1
    self._lastSelectedTargetPos = nil
end

function CollectionMysticalSkinWorkshopPage:_AddToTargetList(sourceItem)
    Module.ItemDetail:CloseItemDetailPanel()
    if sourceItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.CannotRecombineTip)
        return
    end
    if #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= sourceItem.quality then
        return
    end
    if #self._targetWeaponSkinItems >= CollectionConfig.RecombineNum then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.MatrixRecombinationEnoughTip)
        return
    end
    local targetIndex = nil
    for index, item in ipairs(self._targetWeaponSkinItems) do
        if item.id == sourceItem.id and item.gid == sourceItem.gid then
            targetIndex = index
            break
        end
    end
    if targetIndex == nil then
        table.insert(self._targetWeaponSkinItems, sourceItem)
        targetIndex = #self._targetWeaponSkinItems - 1
        if targetIndex < 0 then
            targetIndex = 0
        end
        self:_OnRefreshWeaponSkinItems()
        self._wtTargetItemGridBox:ScrollToItem(#self._targetWeaponSkinItems-1, false, false, 10, 0, true)
        self._selectedSourcePos = -1
        self._selectedSourceCell = nil
    end
end

function CollectionMysticalSkinWorkshopPage:_RemoveFromTargetList(targetItem)
    Module.ItemDetail:CloseItemDetailPanel()
    local targetIndex = nil
    for index, item in ipairs(self._targetWeaponSkinItems) do
        if item.id == targetItem.id and item.gid == targetItem.gid then
            targetIndex = index
            break
        end
    end
    if targetIndex ~= nil then
        table.remove(self._targetWeaponSkinItems, targetIndex)
        self:_OnRefreshWeaponSkinItems()
        self._wtTargetItemGridBox:ScrollToItem(#self._targetWeaponSkinItems-1, false, false, 10, 0, true)
        self._selectedTargetPos = -1
        self._selectedTargetCell = nil
    end

end

function CollectionMysticalSkinWorkshopPage:_OnDragItem(item, bFromSource)
    if bFromSource == true then
        self._sourceDragItem = item
        self._targetDragItem = nil
        if self._wtVectorAnimComp then
            self._wtVectorAnimComp:SetType(2)
            self._wtVectorAnimComp:PlayAnimation(self._wtVectorAnimComp.WBP_Collections_ProductionMatrix_Vector_Onetimecycle, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
    else
        self._sourceDragItem = nil
        self._targetDragItem = item
    end
    self:_StartDragTimer()
end

function CollectionMysticalSkinWorkshopPage:_OnUpdateCollectionData()
    self:RefreshView()
end

function CollectionMysticalSkinWorkshopPage:_OnResetMysticalSkinPool()
    self._targetWeaponSkinItems = {}
    self:_OnRefreshWeaponSkinItems()
end

function CollectionMysticalSkinWorkshopPage:_OnFillMysticalSkinPool()
    if #self._targetWeaponSkinItems < CollectionConfig.RecombineNum and #self._targetWeaponSkinItems > 0 and #self._sourceWeaponSkinItems > 0 then
        local weaponSkinItemsForFill = {}
        for index, skinItem in ipairs(self._sourceWeaponSkinItems) do
            if skinItem.quality == self._targetWeaponSkinItems[1].quality then
                table.insert(weaponSkinItemsForFill, skinItem)
            end
        end
        table.sort(weaponSkinItemsForFill, function (a, b)
            local wearA = a:GetRawPropInfo().mystical_skin_data.wear
            local wearB = b:GetRawPropInfo().mystical_skin_data.wear
            if wearA ~= wearB then
                return wearA > wearB
            end
            if a.gid ~= b.gid then
                return a.gid < b.gid
            end
        end)
        for i = 1, CollectionConfig.RecombineNum - #self._targetWeaponSkinItems, 1 do
            table.insert(self._targetWeaponSkinItems, weaponSkinItemsForFill[i])
        end
        self:_OnRefreshWeaponSkinItems()
    elseif #self._targetWeaponSkinItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOneSkinToPool)
    end
end


function CollectionMysticalSkinWorkshopPage:_OnRecombineMysticalSkins()
    if self._targetWeaponSkinItems and #self._targetWeaponSkinItems == CollectionConfig.RecombineNum then
        local fCallbackIns = CreateCallBack(function(self, res)
            if res.result == 0 then
                self._targetWeaponSkinItems = {}
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.MatrixRecombinationSuccessTip)
                local newWeaponSkins = {}
                for i, propChange in ipairs(res.data_change) do
                    propChange.prop.gid = setdefault(propChange.prop.gid, 0)
                    if propChange.change_type == PropChangeType.Add and propChange.reason == ePropChangeReason.CollectionCombineAddSkin then
                        local item = ItemHelperTool.CreateItemByPropInfo(propChange.prop, propChange)
                        if item then
                            table.insert(newWeaponSkins, item)
                        end
                    end
                end
                if #newWeaponSkins > 0 then
                    Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, newWeaponSkins, nil, false, false, true)
                end
            end
        end,self)
        for index, skinItem in ipairs(self._targetWeaponSkinItems) do
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(skinItem.id, skinItem.gid) == true then
                Module.CommonTips:ShowConfirmWindow(
                    CollectionConfig.Loc.ResetSKinTip,
                    function()
                        CollectionLogic.RecombineMysticalSkins(self._targetWeaponSkinItems, fCallbackIns)
                    end,
                    function()
                    end,
                    CollectionConfig.Loc.Cancel,
                    CollectionConfig.Loc.Confirm
                )
                return
            end
        end
        CollectionLogic.RecombineMysticalSkins(self._targetWeaponSkinItems, fCallbackIns)
    else
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddMysticalSkinToRecombineTip)
    end
end


function CollectionMysticalSkinWorkshopPage:_OnRecombineBtnDeClicked()
    if #self._targetWeaponSkinItems < CollectionConfig.RecombineNum then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddMysticalSkinToRecombineTip)
    end
end

function CollectionMysticalSkinWorkshopPage:_OnFillTargetListBtnDeClicked()
    if #self._targetWeaponSkinItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOneSkinToPool)
    end
end

function CollectionMysticalSkinWorkshopPage:_RefreshActionBtns()
    if #self._targetWeaponSkinItems > 0 then
        self._wtClearTargetListBtn:SelfHitTestInvisible()
    else
        self._wtClearTargetListBtn:Collapsed()
    end
    self._wtFillTargetListBtn:SetIsEnabledStyle(#self._targetWeaponSkinItems > 0)
    self._wtRecombineBtn:SetIsEnabledStyle(#self._targetWeaponSkinItems >= CollectionConfig.RecombineNum)
end  

function CollectionMysticalSkinWorkshopPage:_OpenDetailPanel(item, refWidget, bAdd)
    local function fOnDetailPanelLoaded(detailIns)
        if detailIns then
            detailIns:SetDetailBtnVisible(true)
            local fCallbackIns = CreateCallBack(function(self, res)
                Module.Collection:ShowWeaponSkinDetailPage(item)
            end,self)
            detailIns:SetDetailBtnClickedCallback(fCallbackIns, self)
            self._detailView = detailIns
        end
    end
    local btnTypeList = {}
    if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.WeaponSkinApplied)
    else
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.ApplyWeaponSkin)
    end
    if item.quality < ItemConfig.EWeaponSkinQualityType.Orange then
        if bAdd == true then
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.AddWorkshopItemToTargetPool)
        else
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RemoveWorkshopItemFromTargetPool)
        end
    end 
    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Trade)
    Module.ItemDetail:OpenItemDetailPanel(item, refWidget, false, true, btnTypeList, nil, fOnDetailPanelLoaded)
end

function CollectionMysticalSkinWorkshopPage:_OnShowTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowInstruction()
        else
            self:_OnHideInstruction()
        end
    end
end

function CollectionMysticalSkinWorkshopPage:_OnShowInstruction()
    local datas = {}
    table.insert(datas, {
        textContent = CollectionConfig.Loc.MatrixRecombinationTip,
        styleRowId = "C000"
    })
	self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtTipAnchor)
end

function CollectionMysticalSkinWorkshopPage:_OnHideInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtTipCheckBtn:SetSelectState(false, false)
        end
    end
end

function CollectionMysticalSkinWorkshopPage:_StartDragTimer()
    self:_StopDragTimer()
    self._dragTimer = Timer:NewIns(0.1, 0)
    self._dragTimer:AddListener(self._DragTick, self)
    self._dragTimer:Start()
end

function CollectionMysticalSkinWorkshopPage:_StopDragTimer()
    if isvalid(self._refreshTimer) then
        self._dragTimer:Release()
    end
    self._dragTimer = nil
end

function CollectionMysticalSkinWorkshopPage:_DragTick()
    local previewItem = Module.CommonWidget:GetOrCreateDragItemView()
    if previewItem then
        local absolutePoint, size = CollectionLogic.GetGlobalPosAndSizeByWidget(previewItem)
        absolutePoint.X = absolutePoint.X + size.X
        absolutePoint.Y = absolutePoint.Y + size.Y
        if self._sourceDragItem then
            local bInsideSourceBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtSourceItemGridBox, absolutePoint)
            if bInsideSourceBox == false then
                self._wtTargetDragSelectBox:SelfHitTestInvisible()
                if self._sourceDragItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange or #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= self._sourceDragItem.quality or 
                #self._targetWeaponSkinItems >= CollectionConfig.RecombineNum then
                    self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonInvalid)
                else
                    self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
                end
            else
                self._wtTargetDragSelectBox:Collapsed()
            end
        elseif self._targetDragItem then
            local bInsideTargetBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtTargetItemGridBox, absolutePoint)
            if bInsideTargetBox == false then
                self._wtSourceDragSelectBox:SelfHitTestInvisible()
                self._wtSourceDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
            else
                self._wtSourceDragSelectBox:Collapsed()
            end
        end
    end
end

function CollectionMysticalSkinWorkshopPage:OnNativeOnMouseMove(inGeometry, inGestureEvent)
    --[[
    logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPage:OnNativeOnMouseMove")
    if self._sourceDragItem then
        logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPage:OnNativeOnMouseMove sourceDragItem valid")
        local absolutePosition = inGestureEvent:GetScreenSpacePosition()
        local bInsideTargetBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtTargetItemGridBox, absolutePosition)
        if bInsideTargetBox == true then
            self._wtTargetDragSelectBox:SelfHitTestInvisible()
            if self._sourceDragItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange or #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= self._sourceDragItem.quality or 
            #self._targetWeaponSkinItems >= CollectionConfig.RecombineNum then
                self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonInvalid)
            else
                self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
            end
        else
            self._wtTargetDragSelectBox:Collapsed()
        end
    elseif self._targetDragItem then
        logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPage:OnNativeOnMouseMove targetDragItem valid")
        local absolutePosition = inGestureEvent:GetScreenSpacePosition()
        local bInsideSourceBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtSourceItemGridBox, absolutePosition)
        if bInsideSourceBox == true then
            self._wtSourceDragSelectBox:SelfHitTestInvisible()
            self._wtSourceDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
        else
            self._wtSourceDragSelectBox:Collapsed()
        end
    else
        logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPage:OnNativeOnMouseMove no Item valid")
    end
	if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
    end
    --]]
end


function CollectionMysticalSkinWorkshopPage:_OnHandleMouseButtonUpEvent(mouseEvent)
    local popController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
    local lastPopUI = popController ~= nil and popController:TryGetLastPopUI() or nil
    if not hasdestroy(self) and (lastPopUI == self._detailView or hasdestroy(lastPopUI)) then
        local beginTouchPos = mouseEvent:GetScreenSpacePosition()
        local isUnderDetailView = false
        if self._detailView then
            local rootGeometry = self._detailView:GetMainPanel():GetCachedGeometry()
            isUnderDetailView = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
        end
        if self._sourceDragItem or self._targetDragItem then
            self._wtSourceDragSelectBox:Collapsed()
            self._wtTargetDragSelectBox:Collapsed()
            if self._sourceDragItem then
                local rootGeometry = self._wtSourceItemGridBox:GetCachedGeometry()
                local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                if isUnder == false then
                    self:_AddToTargetList(self._sourceDragItem)
                end
            elseif self._targetDragItem then
                local rootGeometry = self._wtTargetItemGridBox:GetCachedGeometry()
                local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                if isUnder == false then
                    self:_RemoveFromTargetList(self._targetDragItem)
                end
            end
            self._sourceDragItem = nil
            self._targetDragItem = nil
        else
            if isUnderDetailView == false and #self._targetWeaponSkinItems == 0 then
                local rootGeometry = self._wtTargetEmptyHint:GetCachedGeometry()
                local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                if isUnder then
                    if self._selectedSourcePos ~= -1 and self._selectedSourceCell ~= nil then
                        self:_AddToTargetList(self._sourceWeaponSkinItems[self._selectedSourcePos+1])
                    end
                end
            end
        end
        local sourceRootGeometry = self._wtSourceItemGridBox:GetCachedGeometry()
        local isUnderSourceRootGeometry = USlateBlueprintLibrary.IsUnderLocation(sourceRootGeometry, beginTouchPos)
        local targetRootGeometry = self._wtTargetItemGridBox:GetCachedGeometry()
        local isUnderTargetRootGeometry = USlateBlueprintLibrary.IsUnderLocation(targetRootGeometry, beginTouchPos)
        local targetRootGeometry2 = self._wtTargetEmptyHint:GetCachedGeometry()
        local isUnderTargetRootGeometry2 = USlateBlueprintLibrary.IsUnderLocation(targetRootGeometry2, beginTouchPos)
        if not isUnderSourceRootGeometry and not isUnderTargetRootGeometry and not isUnderTargetRootGeometry2 and not isUnderDetailView then
            if isvalid(self._selectedSourceCell) then
                self._selectedSourceCell:SetSelected(nil, false)
            end
            if isvalid(self._selectedTargetCell) then
                self._selectedTargetCell:SetSelected(nil, false)
            end
            self._selectedSourcePos = -1
            self._selectedTargetPos = -1
            Module.ItemDetail:CloseAllPopUI()
        end
    end
    self:_StopDragTimer()
    if self._wtVectorAnimComp then
        self._wtVectorAnimComp:SetType(1)
    end
end

function CollectionMysticalSkinWorkshopPage:_OnEmptyBtnClick()
end

function CollectionMysticalSkinWorkshopPage:_OnWorkshopFilterUpdated()
    self._selectedSourcePos = Module.Collection.Field:GetSelectedPos()
    self:_OnRefreshWeaponSkinItems()
end

function CollectionMysticalSkinWorkshopPage:_OnWeaponSkinApplied(res)
    if not self._bApplied then
        if self._detailView then
            self._updating = true
            local item = nil
            local itemWidget = nil
            local btnTypeList = {}   
            if self._selectedSourcePos ~= -1 then
                item = self._sourceWeaponSkinItems[self._selectedSourcePos+1]
                itemWidget = self._selectedSourceCell
                if item.quality < ItemConfig.EWeaponSkinQualityType.Orange then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.AddWorkshopItemToTargetPool)
                end 
            elseif self._selectedTargetPos ~= -1 then
                item = self._targetWeaponSkinItems[self._selectedTargetPos+1]
                itemWidget = self._selectedTargetCell
                table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RemoveWorkshopItemFromTargetPool) 
            end
            if item then
                if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid, res) then
                    self._bApplied = true
                    table.insert(btnTypeList, 1, Module.ItemDetail.Config.ButtonType.WeaponSkinApplied)
                    Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AppearanceApplied)
                else
                    table.insert(btnTypeList, 1, Module.ItemDetail.Config.ButtonType.ApplyWeaponSkin)
                end 
            end   
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Trade)
            self._detailView:UpdateItem(item, itemWidget, false, true, btnTypeList, nil)
            self._detailView:SetDetailBtnVisible(true)
            self:_OnRefreshWeaponSkinItems()      
        end
    end
end


function CollectionMysticalSkinWorkshopPage:_OnFilterBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionMysticalSkinFilterPanel, nil, self, self._targetWeaponSkinItems[1])
end  

return CollectionMysticalSkinWorkshopPage
