---@class ActivityTableStruct : LuaObject
-- 对应ActivityRow表格
ActivityTableStruct = class('ActivityTableStruct', LuaObject)

---@param ActivityTableStruct AT
function ActivityTableStruct:Ctor(ActivityConfig)
    self:InitActivityStruct(ActivityConfig)
end

---@param ActivityTableStruct AT
function ActivityTableStruct:InitActivityStruct(ActivityConfig)

    self.ActivityID = ActivityConfig.ActivityID or 0
    self.Name = ActivityConfig.Name or ""
    self.Desc = ActivityConfig.Desc or ""
    self.Info1 = ActivityConfig.Info1 or ""

    self.ModeLeaning = ActivityConfig.ModeLeaning or 0
    self.RewardShowed1 = ActivityConfig.RewardShowed1 or 0
    self.RewardShowed2 = ActivityConfig.RewardShowed2 or 0
    self.RewardShowed3 = ActivityConfig.RewardShowed3 or 0
    self.RewardShowed4 = ActivityConfig.RewardShowed4 or 0
    self.subtitle = ActivityConfig.subtitle or ""
    
end

return ActivityTableStruct