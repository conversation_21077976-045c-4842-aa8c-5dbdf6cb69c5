----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBattlePass)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class BattlePassMainItem : LuaUIBaseView
local BattlePassMainItem = ui("BattlePassMainItem")
local BattlePassConfig = Module.BattlePass.Config
local BattlePassLogic = require "DFM.Business.Module.BattlePassModule.Logic.BattlePassLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"

--- 生命周期

function BattlePassMainItem:Ctor()
    --- 声明
    self._tSubUIList = {}  -- 子控件列表  {index : item, index, eType}
    
    -- 成员函数
    self._iLastLevel = -1  -- 上一次代表的等级
    self._iLastNum = -1  -- 上一次的类型
    self._iLastCount = -1  -- 上一次奖励的个数
    self._iNameID = nil  -- 父控件
    self._iPosition = -1  -- 该控件所在的位置
    self._tGetReward = {}  -- 可以获得的奖励
    self._tReward = {}  -- 全部奖励
    self._fCBClickMoreBtn = nil  -- 点击更多按钮的回调
    self._iRefreshedCount = 0  -- 已经刷新的Item的个数
    self._bRefreshGetComp = true  -- 是否刷新已获得组件
    self.iFrameID = 0  -- 分帧处理的index
    
    -- 控件声明
    self._wtRewardHorizontalBox =self:Wnd("DFHorizontalBox_57", UIWidgetBase)  -- 奖励水平框
    Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardHorizontalBox)
    self._wtTxtLevel = self:Wnd("DFTextBlock_155", UITextBlock)  -- 等级
    self._btnMore = self:Wnd("DFButton_243",UIButton)
    self._btnMore:Event("OnClicked", self.OnClickBtnMore, self)
    self._wtTxtMore = self:Wnd("DFTextBlock_116", UITextBlock)  -- 折叠标识
    self._wtImgArrow = self:Wnd("DFImage", UIImage)
    self._wtImgNoLimit = self:Wnd("DFImage_231", UIImage)  -- 无穷符号
    self._wtImgNoLimit:Collapsed()
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function BattlePassMainItem:OnInitExtraData(params)
    loginfo("BattlePassMainItem:OnInitExtraData")
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function BattlePassMainItem:OnOpen()
    loginfo("BattlePassMainItem:OnOpen")
    self:AddListeners()
end

-- UI监听事件、协议
function BattlePassMainItem:AddListeners()
    -- 普通事件
    -- self:AddLuaEvent(BattlePassConfig.evtMainPanelTest, self.OnMainPanelTest, self)
    -- ntf协议事件 ntfNameString
    -- Facade.ProtoManager:AddNtfListener("CSAuctionOrderChangeNtf", self.OnCSAuctionOrderChangeNtf, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function BattlePassMainItem:OnClose()
    loginfo("BattlePassMainItem:OnClose")
    
    -- 释放组件
    --for iIndex, item in pairs(self._tSubUIList) do
    --    local wtItem = Facade.UIManager:GetSubUI(self, UIName2ID.IVCommonItemTemplate, item.instID)
    --    if wtItem then
    --        BattlePassLogic.ClearItemViewComp(wtItem)
    --    end 
    --end
    
    self:RemoveAllLuaEvent()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function BattlePassMainItem:OnShow()
    loginfo("BattlePassMainItem:OnShow")
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function BattlePassMainItem:OnHide()
    loginfo("BattlePassMainItem:OnHide")
end


--- 事件处理

function BattlePassMainItem:OnCloseBtnClick()
    --BattlePassBranch.CloseMainPanelProcess()
end

function BattlePassMainItem:OnMainPanelTest()
end

function BattlePassMainItem:OnCSAuctionOrderChangeNtf(ntf)
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function BattlePassMainItem:OnAnimFinished(anim)
end

--- ui 刷新
--- @param iNumberType 是展示数字还是无穷符号， 0 数字，1 无穷符号
--- @param iVisType 蓝图参数
function BattlePassMainItem:RefreshUI(iNumberType, iLevel, iNameID, iVisType)
    -- 是否是代表等级边界
    self:SetVis(iVisType)
    if iVisType == 1 then
        --Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardHorizontalBox)
        for iIndex, item in pairs(self._tSubUIList) do
            local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
            local wtSpecialItem = getfromweak(wtSpecialItemWeak)
            if wtSpecialItem == nil then
                logerror("[BattlePassMainItem] wtSpecialItem is nil")
                return
            end
            wtSpecialItem:Collapsed()
            item.isActive = false
        end
        self._iLastLevel = -1
        self._iLastNum = -1
        self._iLastCount = -1
        return
    end 
    
    -- 获取该等级的奖励
    local tReward = {}

    -- 根据是等级还是无穷奖励来获取奖励配置
    if iNumberType == 0 then
        tReward = BattlePassLogic.GetMainShowRewardByLevel(iLevel)
    elseif iNumberType == 1 then
        tReward = BattlePassLogic.GetMainShowRewardCircle()
    end
    
    self._iNameID = iNameID

    -- 统计个数
    local iCount = 0
    for _ in pairs(tReward) do
        iCount = iCount + 1
    end

    -- 如果参数完全相同则只轻量刷新
    if iNumberType == self._iLastNum and 
            iLevel == self._iLastLevel and
            iCount == self._iLastCount then
        self:RefreshUILight(iNumberType, iLevel)
        return
    end
    
    -- 保存参数
    self._iLastLevel = iLevel
    self._iLastNum = iNumberType
    
    -- 清空数据
    --Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardHorizontalBox)
    for iIndex, item in pairs(self._tSubUIList) do
        local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
        local wtSpecialItem = getfromweak(wtSpecialItemWeak)
        if wtSpecialItem == nil then
            logerror("[BattlePassMainItem] wtSpecialItem is nil")
            return
        end
        wtSpecialItem:Collapsed() 
        item.isActive = false
    end

    -- 已经刷新的个数清0
    self._iRefreshedCount = 0

    --local bTrash = false
    --for iIndex, item in pairs(self._tSubUIList) do
    --    local wtItem = Facade.UIManager:GetSubUI(self, UIName2ID.IVCommonItemTemplate, item.instID)
    --    bTrash = false
    --end
    --if bTrash then
    --    Facade.UIManager:ClearSubUIByParent(self, self._wtRewardHorizontalBox)
    --end
    
    -- 停止播放动效
    self:StopAnimation(self.WBP_BattlePass_Main_Item_Loop)
    
    -- 设置文本
    self:SetNumberType(iNumberType)  -- item函数，设置等级显示状态，0显示等级，1显示无穷符号

    -- 等级文本
    if iNumberType == 0 then
        self._wtTxtLevel:SetText(iLevel)
        self._wtTxtLevel:SelfHitTestInvisible()
    elseif iNumberType == 1 then
        self._wtTxtLevel:SetText(" ")
        self._wtTxtLevel:Collapsed()
    end

    local iIndex = 0  -- 每个奖励的索引下标，默认先插入的小，从0开始

    -- 获取当前奖励的所在位置
    local iPosition = 0
    if 0 == iNumberType then
        iPosition = Server.BattlePassServer:GetPositionByRewardLevel(iLevel)
    else
        iPosition = Module.BattlePass.Field:GetCircularRewardPosition()
    end

    self._iPosition = iPosition
    
    -- 添加奖励
    --self._tSubUIList = {}
    self.tFrameIDList = {}
    for _, tRewardItem in pairs(tReward) do
        self.iFrameID = self.iFrameID + 1
        if self.iFrameID > 999999999 then
            self.iFrameID = 0
        end
        Facade.LuaFramingManager:RegisterFrameTask(self.AddReward, self, {tRewardItem.iID, tRewardItem.iCount, iPosition, iIndex, tRewardItem.eType, self._iLastNum, self._iLastLevel, self.iFrameID, tRewardItem, tRewardItem.instID})
        iIndex = iIndex + 1
        table.insert(self.tFrameIDList, self.iFrameID)
    end
    
    self._iLastCount = iIndex 
end

function BattlePassMainItem:AddReward(iRewardID, iCount, iPosition, iIndex, eType, iLastNum, iLastLevel, iFrameID, tRewardItem, instID)
    if iLastNum ~= self._iLastNum or
            iLastLevel ~= self._iLastLevel then
        return
    end

    local bItemFrame = false
    for _, ID in pairs(self.tFrameIDList) do
        if ID == iFrameID then
            bItemFrame = true
        end
    end
    if not bItemFrame then
        return
    end
    
    -- 设置奖励道具图标
    -- todo 待确认这里的内存是否被正常释放
    local weakUIIns = nil 
    local instanceId =  nil
    local specialItem = nil
    local item = self._tSubUIList[iIndex]
    if item then
        local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
        specialItem = getfromweak(wtSpecialItemWeak)
        if specialItem == nil then
            logerror("[BattlePassMainItem] wtSpecialItem is nil")
            return
        end
        specialItem:SelfHitTestInvisible()
        item.isActive = true
        -- 打印详细日志 
        -- loginfo("[BattlePassMainItem] AddReward index exist: iIndex = %d, iRewardID = %d, iCount = %d, iPosition = %d, iIndex = %d, eType = %d, iLastNum = %d, iLastLevel = %d, iFrameID = %d", iIndex, iRewardID, iCount, iPosition, iIndex, eType, iLastNum, iLastLevel, iFrameID)
    else
        weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.BattlePassSpecialItem, self._wtRewardHorizontalBox)
        specialItem = getfromweak(weakUIIns)
        if specialItem == nil then
            logerror("[BattlePassMainItem] specialItem is nil")
            return
        end
        -- 添加子控件到列表
        self._tSubUIList[iIndex] = {
            instID = instanceId,
            index = iIndex,
            eType = eType,
            tReward = tRewardItem,
            isActive = true
        }
        -- 打印详细日志
        -- loginfo("[BattlePassMainItem] AddReward index nil: iIndex = %d, iRewardID = %d, iCount = %d, iPosition = %d, iIndex = %d, eType = %d, iLastNum = %d, iLastLevel = %d, iFrameID = %d", iIndex, iRewardID, iCount, iPosition, iIndex, eType, iLastNum, iLastLevel, iFrameID)
    end

    local commonItem = specialItem:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)

    specialItem:SetType(0)
    if DFHD_LUA == 1 then
        commonItem:SetRootSize(256, 256)
    else
        commonItem:SetRootSize(192, 192)
    end
    
    local itemData = ItemBase:New(iRewardID, iCount)
    commonItem:InitItem(itemData)

    if self._iNameID and self._iNameID == UIName2ID.BattlePassMain then
        -- 绑定奖励点击回调函数
        local function fItemViewClick()
            Module.BattlePass.Config.Events.evtBattlePassRewardItemClick:Invoke(iPosition, iIndex, true)
        end

        commonItem:BindCustomOnClicked(fItemViewClick)
    end

    if not IsHD() then
        -- 设置点击参数，防止无法滑动
        commonItem:SetCppValue("bHandleClick", true)
        commonItem:SetCppValue("bPreciseClick", true)
    end

    self._iRefreshedCount = self._iRefreshedCount + 1
    if self._iRefreshedCount == self._iLastCount then
        self:RefreshUILight(self._iLastNum, self._iLastLevel)
    end
end

-- 根据服务器数据刷新
function BattlePassMainItem:RefreshUILight(iNumberType, iLevel)
    -- 清空获得奖励列表
    self._tGetReward = {}

    -- 获取当前奖励的所在位置
    local iPosition = 0
    if 0 == iNumberType then
        iPosition = Server.BattlePassServer:GetPositionByRewardLevel(iLevel)
    else
        iPosition = Module.BattlePass.Field:GetCircularRewardPosition()
    end
    
    -- 如果是当前等级的，则设置高亮
    local iCurrLevel = Server.BattlePassServer:GetLevel()
    -- 1是当前态，0是非当前态，调用蓝图函数
    if iLevel == iCurrLevel then
        self:SetType(1)
        self:PlayAnimation(self.WBP_BattlePass_Main_Item_Loop, 0, 1000000, EUMGSequencePlayMode.Forward, 1, true)
    else
        self:SetType(0)
        self:StopAnimation(self.WBP_BattlePass_Main_Item_Loop)
    end

    -- 遍历奖励
    
    for iIndex, item in pairs(self._tSubUIList) do
        if item.isActive then
            local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
            local wtSpecialItem = getfromweak(wtSpecialItemWeak)
            if wtSpecialItem == nil then
                logerror("[BattlePassMainItem] wtSpecialItem is nil")
                return
            end

            local bottomLine = wtSpecialItem:Wnd("PlatformScaleBox_0", UIWidgetBase)  -- 专属奖励线
            if bottomLine then
                if item.tReward.iIsExclusive == 1 then
                    bottomLine:SelfHitTestInvisible()
                else
                    bottomLine:Collapsed()
                end
            end

            wtSpecialItem:StopAnimation(wtSpecialItem.WBP_BattlePass_SpecialRewards_Item_loop)
            wtSpecialItem:PlayAnimation(wtSpecialItem.WBP_BattlePass_SpecialRewards_Item_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
            
            local commonItem = wtSpecialItem:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
            if self._iNameID and self._iNameID == UIName2ID.BattlePassMain then
                -- 如果刷新的是上一次点击的条目，则设置为点击态
                local iLastLevel = Module.BattlePass.Field:GetLevelRewardLastSelectPosition()
                local iLastIndex = Module.BattlePass.Field:GetLevelRewardLastSelectIndex()
                if iLastLevel == iPosition and iLastIndex == iIndex then
                    commonItem:SetSelected(commonItem.item, true)
                else
                    commonItem:SetSelected(commonItem.item, false)
                end
            end

            -- 刷新奖励角标
            self:RefreshItemComponent(commonItem, iPosition, item.eType)

            -- 保存奖励结构
            self._tReward[iIndex] = item
            
            -- 如果可以领取，则保存
            if self:IsRewardGet(item.eType) then
                self._tGetReward[iIndex] = item
            end
        end
    end
end

-- 刷新奖励item角标
function BattlePassMainItem:RefreshItemComponent(itemView, iPosition, eType, bCompGetArg)
    --- 该item所属条件
    -- 是否已经付费
    local ePayType = Server.BattlePassServer:GetPayType()
    -- 是否小于等于当前等级
    local bLevelLow = false
    -- 是否是等级奖励
    local bLevelReward = false

    -- 判断该item所属条件
    local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()
    local iMaxPosition = Server.BattlePassServer:GetPositionByRewardLevel(iMaxLevel)
    if iPosition <= iMaxPosition then
        bLevelReward = true
        local iCurrLevel = Server.BattlePassServer:GetLevel()
        local iItemLevel = Server.BattlePassServer:GetRewardLevelByPosition(iPosition)
        if iItemLevel <= iCurrLevel then
            bLevelLow = true
        end
    end

    --- item 组件是否盖上
    local bCompFree = false
    local bCompPay = false
    local bCompGet = false
    local bCompMP = false
    local bCompSOL = false

    -- 类型角标
    if eType == EBattlePassRewardType.Free then
        bCompFree = true
    end
    if eType == EBattlePassRewardType.MP then
        bCompMP = true
    end
    if eType == EBattlePassRewardType.SOL then
        bCompSOL = true
    end

    -- 锁头展示
    if eType == EBattlePassRewardType.Pay then
        if ePayType == BattlePassType.BATTLE_PASS_TYPE_BASE then
            bCompPay = true 
        end
    end
    if eType == EBattlePassRewardType.SOL then
        if ePayType == BattlePassType.BATTLE_PASS_TYPE_BASE or
                ePayType == BattlePassType.BATTLE_PASS_TYPE_MP then
            bCompPay = true
        end
    end
    if eType == EBattlePassRewardType.MP then
        if ePayType == BattlePassType.BATTLE_PASS_TYPE_BASE or
                ePayType == BattlePassType.BATTLE_PASS_TYPE_SOL then
            bCompPay = true
        end
    end

    if self._bRefreshGetComp then
        if bCompGetArg ~= nil then
            bCompGet = bCompGetArg
        else
            bCompGet = self:IsRewardGet(eType)
        end
    else
        bCompGet = false
    end
    
    -- 设置item的组件
    BattlePassLogic.SetItemViewComp(itemView, bCompFree, bCompPay, bCompGet, bCompMP, bCompSOL)
end

--- 其他函数

-- 感觉下标获取奖励item
function BattlePassMainItem:GetItemByIndex(iIndex)
    return self._wtRewardHorizontalBox:GetChildAt(iIndex) 
end

-- 播放获得动效
function BattlePassMainItem:PlayAcquireAnim()
    for _, item in pairs(self._tGetReward) do
        if item then
            local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
            local wtSpecialItem = getfromweak(wtSpecialItemWeak)
            if wtSpecialItem == nil then
                logerror("[BattlePassMainItem] wtSpecialItem is nil")
                return
            end
            local commonItem = wtSpecialItem:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
            if commonItem then
                commonItem:PlayIVAnimation("WBP_CommonItemTemplate_in_special_BP", 1, EUMGSequencePlayMode.Forward, 1, false)

                Timer.DelayCall(1.07, function()
                    self._bRefreshGetComp = true
                    self:RefreshItemComponent(commonItem, self._iPosition, item.eType)
                end, self)
            end
        end
    end
end

-- 全部播放获得动效
function BattlePassMainItem:PlayAllAcquireAnim()
    for _, item in pairs(self._tReward) do
        if item then
            local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
            local wtSpecialItem = getfromweak(wtSpecialItemWeak)
            if wtSpecialItem == nil then
                logerror("[BattlePassMainItem] wtSpecialItem is nil")
                return
            end
            local commonItem = wtSpecialItem:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
            if commonItem then
                commonItem:PlayIVAnimation("WBP_CommonItemTemplate_in_special_BP", 1, EUMGSequencePlayMode.Forward, 1, false)

                Timer.DelayCall(1.07, function()
                    self._bRefreshGetComp = true
                    self:RefreshItemComponent(commonItem, self._iPosition, item.eType)
                end, self)
            end
        end
    end
end

-- 设置可获得的奖励，不显示获得标记
function BattlePassMainItem:SetAcquireRewardNoGetFlag()
    for _, item in pairs(self._tGetReward) do
        local wtSpecialItemWeak = Facade.UIManager:GetSubUI(self, UIName2ID.BattlePassSpecialItem, item.instID)
        local wtSpecialItem = getfromweak(wtSpecialItemWeak)
        if wtSpecialItem == nil then
            logerror("[BattlePassMainItem] wtSpecialItem is nil")
            return
        end
        local commonItem = wtSpecialItem:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
        if commonItem then
            self:RefreshItemComponent(commonItem, self._iPosition, item.eType, false)
        end
    end
end

-- 奖励是否已经领取
function BattlePassMainItem:IsRewardGet(eType)
    -- 是否已经付费
    local ePayType = Server.BattlePassServer:GetPayType()
    -- 是否小于等于当前等级
    local bLevelLow = false
    -- 是否是等级奖励
    local bLevelReward = false

    -- 判断该item所属条件
    local iMaxLevel = Server.BattlePassServer:GetCurrMaxRewardLevel()
    local iMaxPosition = Server.BattlePassServer:GetPositionByRewardLevel(iMaxLevel)
    if self._iPosition <= iMaxPosition then
        bLevelReward = true
        local iCurrLevel = Server.BattlePassServer:GetLevel()
        local iItemLevel = Server.BattlePassServer:GetRewardLevelByPosition(self._iPosition)
        if iItemLevel <= iCurrLevel then
            bLevelLow = true
        end
    end

    -- 首先奖励等级小于等于当前等级，免费奖励置为已领取，付费奖励且已付费，则置为已领取
    if bLevelLow then
        if eType == EBattlePassRewardType.Free then
            return true
        end
        if eType == EBattlePassRewardType.Pay then
            if ePayType ~= BattlePassType.BATTLE_PASS_TYPE_BASE then
                return true
            end
        end
        if eType == EBattlePassRewardType.MP then
            if ePayType == BattlePassType.BATTLE_PASS_TYPE_MP or ePayType == BattlePassType.BATTLE_PASS_TYPE_UNIVERSAL then
                return true
            end
        end
        if eType == EBattlePassRewardType.SOL then
            if ePayType == BattlePassType.BATTLE_PASS_TYPE_SOL or ePayType == BattlePassType.BATTLE_PASS_TYPE_UNIVERSAL then
                return true
            end
        end
    end
    
    return false
end

function BattlePassMainItem:OnClickBtnMore()
    if self._fCBClickMoreBtn then
        self._fCBClickMoreBtn()
    end 
end

function BattlePassMainItem:SetCBBtnMore(fClick)
    self._fCBClickMoreBtn = fClick
end

function BattlePassMainItem:SetMoreTxt(txt)
    self._wtTxtMore:SetText(txt)
end

function BattlePassMainItem:SetMoreArrowScale(scaleX, scaleY)
    self._wtImgArrow:SetRenderScale(FVector2D(scaleX, scaleY))
end

function BattlePassMainItem:SetRefreshGetComp(bRefreshGetComp)
    self._bRefreshGetComp = bRefreshGetComp 
end

return BattlePassMainItem
