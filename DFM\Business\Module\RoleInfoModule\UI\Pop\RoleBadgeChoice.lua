----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
----- LOG FUNCTION AUTO GENERATE END -----------

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local HeroConfig = require "DFM.Business.Module.HeroModule.HeroConfig"
--- END MODIFICATION

local RoleBadgeChoice = ui("RoleBadgeChoice")

function RoleBadgeChoice:Ctor()
    self._wtCommonPopWinV2 = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtScrollGridBox = UIUtil.WndScrollGridBox(self, "wtHZBox", self._OnGetCostListCount,
        self._OnProcessCostListWidget)

    local fCallbackIns = CreateCallBack(self._OnBtnCloseUIClick, self)
    self._wtCommonPopWinV2:BindCloseCallBack(fCallbackIns)
    self._wtBadgeDisplayTbl = {}
    self._instanceIdTbl = {}
    for index = 1, 8 do
        self._wtBadgeDisplayTbl[index] = self:Wnd("wtBadgeDisplay_" .. index, UIWidgetBase)
        self._wtBadgeDisplayTbl[index]:SetSlotId(index)
    end

    self._badgeTable = Facade.TableManager:GetTable("HZAchievement")
    local heroId = Module.Hero:GetCurShowHeroId()

    self._hzTabel = {}

    local unlockBadgeInfos = {}
    for i, badgeInfo in pairs(self._badgeTable) do
        local badgeId = nil
        for _, id in pairs(badgeInfo.Badgeid) do
            if Server.HeroServer:IsAccessoryUnlocked(heroId, id) then
                badgeId = id
                break
            end
        end
        if badgeId then
            table.insert(unlockBadgeInfos, badgeInfo)
        end
    end

    self:sortBagdgeTable(heroId, unlockBadgeInfos)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtBadgeSlotGroup = self:Wnd("DFWrapBox_378", UIWidgetBase)
    end
    --- END MODIFICATION

    self:AddLuaEvent(Server.RoleInfoServer.Events.evtGetBadgeShow, self._RefreshBadgeDisplay, self)
    Server.RoleInfoServer:FetchGetSelfBadgeShow()
end

function RoleBadgeChoice:sortBagdgeTable(heroId ,unlockBadgeInfos)
    local listSortFunc = function(a, b)
        local aUnlock = Server.HeroServer:IsAccessoryUnlocked(self._heroID, a.BadgeId[1])
        local bUnlock = Server.HeroServer:IsAccessoryUnlocked(self._heroID, b.BadgeId[1])

        if aUnlock and not bUnlock then
            return true
        elseif bUnlock and not aUnlock then
            return false
        else
            return a.Order < b.Order
        end
    end
    table.sort(unlockBadgeInfos, listSortFunc)

    for key, badgeInfo in pairs(unlockBadgeInfos) do
        local badgeId = nil
        for _, id in pairs(badgeInfo.Badgeid) do
            if Server.HeroServer:IsAccessoryUnlocked(heroId, id) then
                badgeId = id
            end
        end
        table.insert(self._hzTabel,
        {
            badgeId = badgeId,
            badgeAchievementId = key,
            heroId = "88000000030"
        })
    end
end

function RoleBadgeChoice:_OnGetCostListCount()
    return #self._hzTabel
end

function RoleBadgeChoice:_OnProcessCostListWidget(position, widget)
    if not widget then
        return
    end

    local badgeInfo = self._hzTabel[position + 1]
    widget:OnInitExtraData(badgeInfo.badgeId, badgeInfo.badgeAchievementId, badgeInfo.heroId, false, false)
    local badgeTbl = Server.RoleInfoServer:GetBadgeShowTbl()
    if badgeTbl[badgeInfo.badgeId] then
        widget:SetUsing(true)
    else
        widget:SetUsing(false)
    end
    widget:Visible()
end

-- BEGIN MODIFICATION @ VIRTUOS
function RoleBadgeChoice:OnShowBegin()
    if IsHD() then
        self:_InitGamepadInputs()
    end
end

function RoleBadgeChoice:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end

-- END MODIFICATION

function RoleBadgeChoice:OnInitExtraData()
    self._wtScrollGridBox:RefreshAllItems()
end

function RoleBadgeChoice:_RefreshBadgeDisplay(badgeList)
    for index = 1, 8 do
        self._wtBadgeDisplayTbl[index]:ResetItem()
        self._wtBadgeDisplayTbl[index]:SetDropEvent()
    end
    for i, info in ipairs(badgeList) do
        if self._wtBadgeDisplayTbl[info.slot] then
            self._wtBadgeDisplayTbl[info.slot]:SetImg(info.prop_id, true, info.unlock_time)
        end
    end
    self:_InitShortcuts()
    self._wtScrollGridBox:RefreshVisibleItems()
end

function RoleBadgeChoice:OnNavBack()
    return false
end

function RoleBadgeChoice:_OnBtnCloseUIClick()
    Facade.UIManager:CloseUI(self)
end

-- BEGIN MODIFICATION @ VIRTUOS
function RoleBadgeChoice:_InitGamepadInputs()
    if not self:IsVisible() then
        return
    end

    -- 开启手柄移动物品功能
    -- Module.CommonWidget:EnableGamepadSwapItem(true)
    -- Module.CommonWidget:EnableGamepadCarryItemFromPop(true)
    -- Module.CommonWidget:EnableGamepadItemShortcutKey(true)

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtScrollGridBox)
            self._navGroup:AddNavWidgetToArray(self._wtBadgeSlotGroup)
            self._navGroup:SetScrollRecipient(self._wtScrollGridBox)
        end
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
    end

    self:_InitShortcuts()

    self:AddLuaEvent(HeroConfig.Events.evtOnPersonalBadgeSelected, self._OnBadgeItemSelected, self)
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtBadgeSlotSelected, self._OnBadgeSlotSelected, self)
end

function RoleBadgeChoice:_DisableGamepadInputs()
    Module.CommonWidget:SetAutoSnapEnabled(false)
    -- 关闭手柄移动物品功能
    Module.CommonWidget:EnableGamepadSwapItem(false)
    Module.CommonWidget:EnableGamepadCarryItemFromPop(false)
    Module.CommonWidget:EnableGamepadItemShortcutKey(false)

    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

    self:_RemoveShortcuts()

    self:RemoveLuaEvent(HeroConfig.Events.evtOnPersonalBadgeSelected)
    self:RemoveLuaEvent(Module.RoleInfo.Config.Event.evtBadgeSlotSelected)
end

function RoleBadgeChoice:_InitShortcuts()
    if not IsHD() then
        return
    end

    self:_RemoveShortcuts()

    if not self._confirmHandler then
        self._confirmHandler = self:AddInputActionBinding("Confirm", EInputEvent.IE_Pressed, self._OnGamepadConfirm, self, EDisplayInputActionPriority.UI_Pop)
    end

    local OperatorBadgeAction = nil
    local bIsEquipped = self:_InternalIsEquipped()
    if bIsEquipped then
        OperatorBadgeAction = "RoleInfo_EquippedBadge_Gamepad"
        self._EquippedBadge = self:AddInputActionBinding(OperatorBadgeAction, EInputEvent.IE_Pressed, self._OnGamepadConfirm,self, EDisplayInputActionPriority.UI_Pop)
    end

    local bIsUnequipped = self:_InternalIsUnequipped()
    if bIsUnequipped then
        OperatorBadgeAction = "RoleInfo_UnequippedBadge_Gamepad"
        self._UnequippedBadge = self:AddInputActionBinding(OperatorBadgeAction, EInputEvent.IE_Pressed, self._OnGamepadConfirm,self, EDisplayInputActionPriority.UI_Pop)
    end

    local summaries = {}
    if OperatorBadgeAction then
        table.insert(summaries, OperatorBadgeAction)
    end
    self._wtCommonPopWinV2:AddSummaries(summaries)
end

function RoleBadgeChoice:_RemoveShortcuts()
    if not IsHD() then
        return 
    end

    self._confirmHandler = self:_InternalRemoveShortcut(self._confirmHandler)
    self._EquipShortcuts = self:_InternalRemoveShortcut(self._EquipShortcuts)
    self._UnequippedBadge = self:_InternalRemoveShortcut(self._UnequippedBadge)
end

function RoleBadgeChoice:_InternalRemoveShortcut(InActionHandler)
    if not IsHD() then
        return nil
    end

    if InActionHandler then
        self:RemoveInputActionBinding(InActionHandler)
    end

    return nil
end

function RoleBadgeChoice:_OnBadgeItemSelected(selectedBadge)
    self._selectedSlot = nil
    self._selectedBadge = selectedBadge
    self:_InitShortcuts()
end

function RoleBadgeChoice:_OnBadgeSlotSelected(selectedSlot)
    self._selectedBadge = nil
    self._selectedSlot = selectedSlot
    self:_InitShortcuts()
end

function RoleBadgeChoice:_InternalIsEquipped()
    local bIsValid = isvalid(self._selectedBadge)
    if not bIsValid then
        return false
    end
    return not self._selectedBadge._wtUsing:IsVisible()
end

function RoleBadgeChoice:_InternalIsUnequipped()
    local bIsValid = isvalid(self._selectedBadge)
    if bIsValid then
        return self._selectedBadge._wtUsing:IsVisible()
    end
    bIsValid = isvalid(self._selectedSlot) and self._selectedSlot.CouldDelBadge
    if not bIsValid then
        return false
    end
    return self._selectedSlot:CouldDelBadge()
end

function RoleBadgeChoice:_OnGamepadConfirm()
    if self._selectedBadge then
        local bIsVisible = self._selectedBadge._wtUsing:IsVisible()
        if not bIsVisible then
            -- Find first empty slot.
            for i, slot in ipairs(self._wtBadgeDisplayTbl) do
                if not slot._wtDelBadgeItem:IsVisible() then
                    Server.RoleInfoServer:FetchSelfBadgeShow(self._selectedBadge._badgeId, slot._slotId)
                    return
                end
            end
        else
            Server.RoleInfoServer:FetchSelfBadgeShow(self._selectedBadge._badgeId, 0)
        end
    elseif self._selectedSlot then
        local bCouldDelBadge = self._selectedSlot and self._selectedSlot.CouldDelBadge and self._selectedSlot:CouldDelBadge()
        if bCouldDelBadge then
            self._selectedSlot:_OnDelBadgeBtn()
        end
    end
end
-- END MODIFICATION

return RoleBadgeChoice