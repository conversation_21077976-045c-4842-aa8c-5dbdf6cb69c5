----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
-- 显示store icon
local UDFMPlatformUtils = import "DFMPlatformUtils"
local EGPInputType = import  "EGPInputType"
-- END MODIFICATION

---@class ProductPreview : LuaUIBaseView
local ProductPreview = ui("ProductPreview")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local UGPGameViewportClient = import "GPGameViewportClient"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local LiteCommonDownload =  require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"

---@enum EProductPreviewDisplayType
local EProductPreviewDisplayType = {
    None = 0,
    Watch = 1, --手表
    WeaponSkin = 2, --武器皮肤
    Adapter = 3, --武器配件
    VehicleSkin = 4, --载具皮肤
    Other = 5, --其他
}

function ProductPreview:Ctor()
    self.getAniDelayTime = 2
    self.getAniLastTime = 1
    self.goodsItems = {}
    self.lastLoadModelID = 0
    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtWaterfallView", self._OnGetItemCount,
        self._OnProcessItemWidget)

    self._wtTextBundleName = self:Wnd("wtBundleName", UITextBlock)
    self._wtCountWidget = self:Wnd("wtStoreCountDown", UIWidgetBase)
    if self._wtCountWidget ~= nil then
        self._wtTextCountDown = self._wtCountWidget:Wnd("wtTextCountDown", UITextBlock)
    end

    self._wtTextWeaponName = self:Wnd("wtTextWeaponName", UITextBlock)
    self._wtTextWeaponDesc = self:Wnd("wtWeaponDesc", UITextBlock)
    self._wtTextPrice = self:Wnd("wtRichTextPrice", UITextBlock)
    self._wtTextContainTip = self:Wnd("wtTextContainTip", UITextBlock)
    self._wtTextGoodsTip = self:Wnd("wtTextGoodsTip", UITextBlock)

    self._wtButtonBuyBundle = self:Wnd("wtButtonBuyBundle", DFCommonButtonOnly)
    self._wtButtonBuyBundle:Event("OnClicked", self._OnButtonBuyBundleClick, self)

    self._wtButtonPreview = self:Wnd("wtButtonPreview", UIButton)
    self._wtButtonPreview:Event("OnClicked", self._OnButtonPreviewClick, self)


    self._wtButtonBuySelect = self:Wnd("wtButtonBuySelect", DFCommonButtonOnly)
    self._wtButtonBuySelect:Event("OnClicked", self._OnButtonBuySelectOneClick, self)
    self._wtButtonBuySelect:BP_SetMainTitle(StoreConfig.Loc.RecommendBundleBuySelectOne)

    self._wtButtonItemPreview = self:Wnd("wtButtonItemPreview", DFCommonButtonOnly)
    self._wtButtonItemPreview:Event("OnClicked", self._OnButtonRoleSkinPreviewClick, self)
    self._wtButtonItemPreview:BP_SetMainTitle(StoreConfig.Loc.RecommendBundleItemPreview)
    
    self._wtDetailCheckBtn = self:Wnd("wtDetailCheckBtn", DFCheckBoxOnly)
    self._wtDetailCheckBtn:Collapsed()
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_257", self._OnHovered, self._OnUnhovered)
    if DFHD_LUA == 1 then
        self._wtDetailCheckBtn:Event("OnCheckStateChanged",self._OnCheckStateChanged, self)
    end

    self._wtDiscountRoot = self:Wnd("wtDiscountRoot", UIWidgetBase)
    self._wtTextDiscount = self:Wnd("wtTextDiscount", UITextBlock)

    self.wtImageIcon = self:Wnd("DFImage_191", UIImage)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)

    --download
    self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)
    self._wtNameIcon = self:Wnd("wt_NameIcon", UIImage)

    self._wtImageRoot = self:Wnd("DFScaleBox_0", UIWidgetBase)
    self._wtImageSparyRoot = self:Wnd("DFScaleBox_Spray", UIWidgetBase)
    self.wtImageSpray = self:Wnd("DFImage_Spray", UIImage)

    -- 语音播放控件
    self._wtStaffLotteryVoiceWidget = self:Wnd("WBP_StaffLottery_Voice", UIWidgetBase)
    self._wtStaffLotteryVoiceWidget:Collapsed()

    self._wtSizeBox_2 = self:Wnd("DFSizeBox_2", UIWidgetBase)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self._wtButtonRoot = self:Wnd("DFVerticalBox_47", UIWidgetBase)
    end
    --- END MODIFICATION

        --视频专用
    ---@type CommonVideoComponent
    self._mediaComponent = self:Wnd("WBP_CommonVideoComponent", CommonVideoComponent)
    self._mediaComponent:InitComponent(false)

    self._videoBG = self:Wnd("WBP_Common_ScaleBg", UIWidgetBase)

    -- if self.wtImageIcon ~= nil and self._wtImageRoot ~= nil then
    --     self._wtImageRoot:Visible()
    --     self.wtImageIcon:Visible()
    --     self.wtImageIcon:AsyncSetImagePath("Texture2D'/Game/UI/UIAtlas/System/Store/Sp/GiftBanner/GiftBanner_100001.GiftBanner_100001'", true)
    -- end

    Module.Store.Field:InitResourceCommercializationTable() -- 初始化语音表 

    self._DisplayType = EProductPreviewDisplayType.None

    if IsHD() then
        self._RotationVec = FVector2D(0, 0)
        self._WeaponRotationZoom = 600
        self._WeaponScaleZoom = 30
    end
end

function ProductPreview:_OnGetItemCount()
    loginfo("[RecommendHomepage] _OnGetItemCount #self.goodsItems:" ..
        #self.goodsItems)
    return #self.goodsItems or 0
end

function ProductPreview:_OnProcessItemWidget(position, itemWidget)
    local curdata = self.goodsItems[position]
    if curdata ~= nil then
        itemWidget:Visible()
        if self.isMallGift then
            local isSellOut = self:_getIsGiftBuyLimited()
            itemWidget:SetItemInfo(position, curdata, self.curSelectIndex, self.buyRecord, self.isMallGift, isSellOut)
        else
            local bIsOwned = nil
                if self:CheckIsSellBuyGoodId(curdata.id) then
                    bIsOwned = true
                end
            itemWidget:SetItemInfo(position, curdata, self.curSelectIndex, self.buyRecord, self.isMallGift, bIsOwned)
        end
        --- BEGIN MODIFICATION @ VIRTUOS : 如果当前为手柄输入，则使用了手柄的自动点击功能，不用再显示Item的选择边框，这个边框会影响手柄的聚焦和点击功能
        if IsHD() and WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad then
            itemWidget:SetSelected(false)
        end
        --- END MODIFICATION
    else
        itemWidget:Collapsed()
    end

    loginfo("[ProductPreview] _OnProcessItemWidget posIdx:" .. position)
end

function ProductPreview:OnCloseBtnClick()

end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function ProductPreview:OnInitExtraData(shopData, isMallGift, isRecommend)
    if shopData == nil then
        return
    end
    self.isMallGift = isMallGift

    if self.isMallGift then
        Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.TopBarTitleGift)
    else
        Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.TopBarTitleBundle)
    end
    -- shopData.BundleItems[1].id = 30000060001
    -- for k,v in pairs( shopData.BundleItems) do
    --     if v.id == 28012140004 then
    --         v.id = 30000060001
    --     end
    -- end

    if self._wtSizeBox_2 then
        if isRecommend then
            self._wtSizeBox_2:Collapsed()
        else
            self._wtSizeBox_2:Visible()
        end
    end

    self.shopData = shopData
    self.curSelectIndex = 1
    self.bIsSellOut = false

    self.goodsItems = {}
    self.buyRecord = nil
    
    self.SetCharacterSuitAvatarWithSequence = "None"

    self:_RefreshShowInfo()

    if self.shopData ~= nil then
        local nameIconPath = self.shopData.ImageSourceLogo
        -- if self.shopData.TabId == 10100004 then
        --     --test
        --     nameIconPath = "Texture2D'/Game/UI/UIAtlas/Texture/SkinWordArt/SkinWordArt_04.SkinWordArt_04'"
        -- end

        if nameIconPath == nil or nameIconPath == "" then
            self._wtTextBundleName:Visible()
            self._wtNameIcon:Collapsed()
            if self._wtTextGoodsTip ~= nil then
                self._wtTextGoodsTip:Collapsed()
            end

            if self.isMallGift then
                self._wtTextBundleName:SetText(self.shopData.GiftName)
            else
                self._wtTextBundleName:SetText(self.shopData.BannerName)
            end
        else
            self._wtTextBundleName:Collapsed()
            self._wtNameIcon:Visible()
            self._wtNameIcon:AsyncSetImagePath(nameIconPath, true)

            if self._wtTextGoodsTip ~= nil then
                self._wtTextGoodsTip:Visible()
                if self.isMallGift then
                    self._wtTextGoodsTip:SetText(self.shopData.GiftName)
                else
                    self._wtTextGoodsTip:SetText(self.shopData.BannerName)
                end
            end
        end
    end



    self._wtWaterFallList:RefreshAllItems()
    self:RefreshSelectItemInfo()

    if self.shopData.IsCollaboration == Server.StoreServer:GetThemeId() then
        local topTopBarGroupRegInfo = {
            stackDefaultThemeID = EThemeIDType.CrossOver,
        }
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTopBarGroupRegInfo)
    else
        local topTopBarGroupRegInfo = {
            stackDefaultThemeID = EThemeIDType.NoTheme,
        }
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTopBarGroupRegInfo)
    end
end

local nosortidx = 9999
function ProductPreview:_sortGoodsItems()
    if self.goodsItems == nil or #self.goodsItems <= 1 then
        return
    end


    for i, v in ipairs(self.goodsItems) do
        v.sort = nosortidx
        for idx, sortinfo in ipairs(self.itemSortInfo) do
            -- if sortinfo.id == 28012140004 then
            --     sortinfo.id = 30000060001
            -- end
            if v.id == sortinfo.id then
                v.sort = sortinfo.sort
            end
        end
    end

    if self.isMallGift then
        table.sort(self.goodsItems, function(a, b)
            local sortA = a.sort
            local sortB = b.sort

            if sortA ~= sortB then
                return sortA < sortB
            else
                return a.id < b.id
            end
        end)
    else
        table.sort(self.goodsItems, function(a, b)
            local feeA = a.price
            local feeB = b.price

            local sortA = a.sort
            local sortB = b.sort

            if feeA == 0 and feeB == 0 then
                if sortA ~= sortB then
                    return sortA < sortB
                else
                    return a.id < b.id
                end
            elseif feeA > 0 and feeB > 0 then
                if sortA ~= sortB then
                    return sortA < sortB
                else
                    if feeA == feeB then
                        return a.id < b.id
                    else
                        return feeA > feeB
                    end
                end
            else
                return feeA > feeB
            end
        end)
    end

end

function ProductPreview:_RefreshShowInfo()
    local price = 0
    local priceOrign = 0
    local discount = 0

    local currencyItemId = 0
    local bIsCash = false
    local bShowDiscount = false

    if self.isMallGift then
        self.goodsItems = self.shopData.BundleItems
        self.itemSortInfo = self:GetItemsSortByGoodsString(self.shopData.ItemsSortIndex)
        self:_sortGoodsItems()
        self.buyRecord = Server.StoreServer:GetGetMallGiftRecordByGoodsId(self.shopData.GoodsId)
        self.bIsSellOut = self:_getIsGiftBuyLimited()
        price = self.shopData.Price
        currencyItemId = self.shopData.CurrencyType
        bIsCash = self.shopData.IsCash == 1
        discount = self.shopData.Discount
        bShowDiscount = self.shopData.IsDiscountIntensity == 1
        priceOrign = self.shopData.PricePreDis
        
        Facade.HallSceneManager:SetDisplayBackground(0)
    else
        self.goodsItems = self.shopData.BundleItems
        self.itemSortInfo = self:GetItemsSortByGoodsString(self.shopData.ItemsSortIndex)
        self:_sortGoodsItems()

        self.buyRecord = Server.StoreServer:GetRecommondBuyRecordByTabId(self.shopData.TabId)

        if self.buyRecord ~= nil then
            self.bIsSellOut = self.buyRecord.is_sold_out
        end

        --设置背景
        Facade.HallSceneManager:SetDisplayBackground(self.shopData.TabId, true)
        
        --设置背景缩放
        Facade.HallSceneManager:SetBackgroundScale("WeaponShowBG")

        price, priceOrign = self:_getBuyBundlePrice()
        currencyItemId = self.shopData.BundleCurrencyType
        bShowDiscount = self.shopData.IsDiscountIntensity == 1
    end

    if self._wtButtonBuyBundle ~= nil then
        local isOwned = true
        for k, v in ipairs(self.goodsItems) do
            if not Server.StoreServer:IsHaved(v.id) and not self:CheckIsSellBuyGoodId(v.id) then
               isOwned = false
               break 
            end
        end
        if self.bIsSellOut or isOwned then
            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
            if self.isMallGift and self.shopData.LimitType ~= nil and self.shopData.LimitType > 0 and
                self.shopData.LimitAmount > 0 then
                self._wtButtonBuyBundle:SetMainTitle(StoreConfig.Loc.BuyLimit)
            else
                self._wtButtonBuyBundle:SetMainTitle(StoreConfig.Loc.RecommendBundleAllItemBuyButtonTip)
            end
            self._wtButtonBuyBundle:SetIsEnabled(false)
        else
            self._wtButtonBuyBundle:SetIsEnabled(true)

            if bIsCash then
                local rechargeInfo = Server.PayServer:GetProductItem(self.shopData.ProductID)
                if rechargeInfo ~= nil then
                    local display_price = ""
                    local display_original_price = ""
                    if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
                        if (IsBuildRegionGA() and IsHD()) or Server.SDKInfoServer:IsAndroidSelfPublishChannel() then
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                            display_price = Module.Reward.Config.Loc.GoToPurchase
                            bShowDiscount = false
                        --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
                        elseif IsXboxSeries() then
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                            display_price = rechargeInfo.current_price or ""
                        elseif IsPS5Family() then
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                            display_price = rechargeInfo.display_price or ""
                        --- END MODIFICATION
                        else
                            display_price, display_original_price = StoreLogic.GetDisplayPriceByProductItemData(rechargeInfo, discount)
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(2)
                            self._wtButtonBuyBundle:SetV1S3SubTitle(display_original_price)
                        end
                    else
                        display_price = string.format("￥%d", price)
                        if priceOrign > price then
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(2)
                            self._wtButtonBuyBundle:SetV1S3SubTitle(priceOrign)
                        else
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                        end
                    end
                    self._wtButtonBuyBundle:SetMainTitle(display_price)
                else
                    if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
                        if (IsBuildRegionGA() and IsHD()) or Server.SDKInfoServer:IsAndroidSelfPublishChannel() then
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                            self._wtButtonBuyBundle:SetMainTitle(Module.Reward.Config.Loc.GoToPurchase)
                            bShowDiscount = false
                        else
                            --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
                            if IsConsole() then
                                self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                        	    self._wtButtonBuyBundle:SetMainTitle("")
                            --- END MODIFICATION
                            elseif priceOrign > price then
                                self._wtButtonBuyBundle:SetV1S3ButtonStyle(2)
                                self._wtButtonBuyBundle:SetMainTitle(string.format("$%d", price))
                                self._wtButtonBuyBundle:SetV1S3SubTitle(string.format("$%d", priceOrign))
                            else
                                self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                                self._wtButtonBuyBundle:SetMainTitle(string.format("$%d", price))
                            end
                        end
                    else
                        if priceOrign > price then
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(2)
                            self._wtButtonBuyBundle:SetMainTitle(string.format("¥%d", price))
                            self._wtButtonBuyBundle:SetV1S3SubTitle(string.format("¥%d", priceOrign))
                        else
                            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                            self._wtButtonBuyBundle:SetMainTitle(string.format("¥%d", price))
                        end
                    end
                end
            else
                if priceOrign > price then
                    self._wtButtonBuyBundle:SetV1S3ButtonStyle(1)
                    local imgPath = Module.Currency:GetImgPathByItemId(currencyItemId)
                    self._wtButtonBuyBundle:SetV1S3TextIcon(imgPath)
                    self._wtButtonBuyBundle:SetMainTitle("  " .. MathUtil.GetNumberFormatStr(price, 1) .. "  ")
                    self._wtButtonBuyBundle:SetV1S3SubTitle("  " .. MathUtil.GetNumberFormatStr(priceOrign, 1) .. "  ")
                else
                    self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                    local priceStr = string.format(StoreConfig.Loc.ItemPriceNormal,
                        Module.Currency:GetRichTxtImgByItemId(currencyItemId),
                        MathUtil.GetNumberFormatStr(price, 1))

                    self._wtButtonBuyBundle:SetMainTitle(priceStr)
                end
            end
        end
    end

    if self._wtDiscountRoot ~= nil and self._wtTextDiscount ~= nil then
        if bShowDiscount and priceOrign > price and self.bIsSellOut == false then
            self._wtDiscountRoot:Visible()
            local disCount = (priceOrign - price) / priceOrign
            local showDiscountStr = string.format("%.0f", disCount * 100)
            self._wtTextDiscount:SetText(StringUtil.SequentialFormat(StoreConfig.Loc.ItemDiscount, showDiscountStr))
        else
            self._wtDiscountRoot:Collapsed()
        end
    end


    local bShowCountDown = self.shopData.IsDisplayCountdown ~= nil and self.shopData.IsDisplayCountdown > 0
    if self._wtCountWidget ~= nil and self._wtTextCountDown ~= nil then
        if bShowCountDown then
            self._wtCountWidget:Visible()
            local timestamp = self.shopData.OfflineTime
            if timestamp > 0 then
                self.tickCountDown = 0
                self.tickDeltaTimeCountDown = 0
                self.offlineSeconds = TimeUtil.GetLocalRemainTime2Seconds(timestamp)
                -- self.offlineSeconds = self.offlineSeconds - 27203*24*60*60 - 9*60*60
                local showStr = ""
                local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self.offlineSeconds)
                if self.offlineSeconds > 3600 * 24 then
                    showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(self.offlineSeconds, "DDHH_Second")
                elseif self.offlineSeconds > 3600 then
                    showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
                else
                    self.tickCountDown = 1
                    showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinSecTip, min, sec)
                end

                self._wtTextCountDown:SetText(showStr)
            end
        else
            self.tickCountDown = 0
            self._wtCountWidget:Collapsed()
        end
    end

    if self._wtTextContainTip ~= nil then
        local bShowTip = false
        if self.isMallGift and self.shopData.LimitType ~= nil and self.shopData.LimitType > 0 and
            self.shopData.LimitAmount > 0 then
            local timestamp = Server.StoreServer:GetBuyLimitResetTimeByLimitType(self.shopData.LimitType)
            if self.bIsSellOut then
                if timestamp > 0 then
                    if self.shopData.LimitType == 1 then
                        timestamp = timestamp + 86400
                    elseif self.shopData.LimitType == 2 then
                        timestamp = timestamp + 604800
                    else
                        timestamp = timestamp + 2592000
                    end

                    bShowTip = true
                    local senconds = TimeUtil.GetServerRemainTime2Seconds(timestamp)
                    local showStr = ""
                    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(senconds)
                    if senconds > 3600 * 24 then
                        showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(senconds, "DDHH_Second")
                    elseif senconds > 3600 then
                        showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
                    else
                        showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinTip, min)
                    end

                    local countdownTime = string.format(StoreConfig.Loc.StoreBuyLimitTimeTip, showStr)
                    self._wtTextContainTip:SetText(countdownTime)
                end
            else
                --show limit info
                local buyCount = 0
                if timestamp ~= 0 and self.buyRecord ~= nil and self.buyRecord.buy_time > timestamp then
                    buyCount = self.buyRecord.num
                end

                bShowTip = true
                local showStr = ""
                if self.shopData.LimitType == 1 then
                    showStr = string.format(StoreConfig.Loc.LimitDayText, buyCount, self.shopData.LimitAmount)
                elseif self.shopData.LimitType == 2 then
                    showStr = string.format(StoreConfig.Loc.LimitWeekText, buyCount, self.shopData.LimitAmount)
                else
                    showStr = string.format(StoreConfig.Loc.LimitMonthText, buyCount, self.shopData.LimitAmount)
                end
                self._wtTextContainTip:SetText(showStr)
            end
        end

        if bShowTip == false then
            local containGoodsStr = string.format(StoreConfig.Loc.RecommendContainGoodsTip, #self.goodsItems or 0)
            self._wtTextContainTip:SetText(containGoodsStr)
        end
    end
end

function ProductPreview:_OnButtonBuyBundleClick()
    if Server.PayServer:IsGarenaEnable() then
        Server.StoreServer:SendShopGetBuyRecordReq()
    end
    local isSellOut = false
    if self.isMallGift then
        isSellOut = self:_getIsGiftBuyLimited()

        if isSellOut then
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.BuyLimit)
        else
            Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData, self.goodsItems,
                self.goodsItems, self.isMallGift, false)
        end
    else
        isSellOut = self.buyRecord ~= nil and self.buyRecord.is_sold_out == true
        local isBundleBuyAll = true
        if isSellOut then
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.BuyLimit)
        else
            if self:_getIsAnyGoodSelled() then
                local toBuyGoods = self:_getBuyBundleItems()
                isBundleBuyAll = false
                if toBuyGoods ~= nil and #toBuyGoods >= 1 then
                    Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData,
                        self.goodsItems,
                        toBuyGoods, self.isMallGift, isBundleBuyAll)
                end
            else
                local toBuyGoods = self:_getBuyBundleItems()
                if toBuyGoods ~= nil and #toBuyGoods >= 1 then
                    Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData,
                        self.goodsItems,
                        toBuyGoods, self.isMallGift, isBundleBuyAll)
                end
            end

        end
    end

    self:ClearTips()
end

function ProductPreview:_checkBuyBundlePriceAndReportEvent()
    local isCash = 0
    local enough = 2
    local price = 0
    local currency_type = 0
    local bundleid = 0
    if self.isMallGift then
        if self.shopData.IsCash ~= nil then
            isCash = self.shopData.IsCash
        end

        bundleid = self.shopData.GoodsId
        price = self.shopData.Price
        currency_type = self.shopData.CurrencyType
    else
        --check currency enough
        bundleid = self.shopData.TabId
        price = self.shopData.DisBundlePrice
        currency_type = self.shopData.BundleCurrencyType
    end
    local currecny = Module.Currency:GetNumByItemId(currency_type)
    local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
    local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

    if currecny >= price then
        enough = 1
    else
        --use currency_type_sub
        if currency_type ~= currency_type_sub then
            if currecny + currecny_sub >= price then
                enough = 1
            end
        end
    end

    self:ReportViewDetailEvent(3, bundleid, isCash, enough)
end

function ProductPreview:_OnHovered()
    self:_ShowTips()
end

function ProductPreview:_OnUnhovered()
    self:_HideTips()
end

function ProductPreview:_OnTipClick()
    if self._wtDetailCheckBtn ~= nil and self._wtDetailCheckBtn:IsVisible() == false then
        return
    end

    if self.hasTipClick == false then
        self:_ShowTips()
    else
        self:_HideTips()
    end
end

function ProductPreview:_ShowTips()
    if self._wtDetailCheckBtn ~= nil and self._wtDetailCheckBtn:IsVisible() == false then
        return
    end

    if self._wtTipHandle then
        return
    end

    self._wtTipHandle = self:ShowCommonMessageWithAnchor(self._TipsText or "",
            self._wtDFTipsAnchor)

    self.hasTipClick = true
end

function ProductPreview:_HideTips()
    if self._wtTipHandle then
        self:RemoveCommonMessageWithAnchor(self._wtTipHandle, self._wtDFTipsAnchor)
        self._wtTipHandle = nil
        self.hasTipClick = false
    end
end

function ProductPreview:_OnCheckStateChanged(bChecked)
    self:_OnUnhovered()
    if bChecked then
        self:_ShowTips()
    else
        self:_HideTips()
    end
end

function ProductPreview:ShowCommonMessageWithAnchor(content, tipsAnchor)
    local function loadFinCall(uiIns)
        if isvalid(tipsAnchor) then
            tipsAnchor:BindTipsWidget(uiIns)
            uiIns:Visible()
        end
    end

    local data = {
        textContent = content,
        styleRowId = "C002"
    }
    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonMessageTips2, loadFinCall, nil, { data })
    return handle
end

function ProductPreview:RemoveCommonMessageWithAnchor(handle, tipsAnchor)
    if handle then
        local uiIns = handle:GetUIIns()
        if uiIns then
            if isvalid(tipsAnchor) then
                tipsAnchor:UnbindTipsWidget()
            end
        end
        Facade.UIManager:CloseUIByHandle(handle)
    end
end

function ProductPreview:_OnButtonBuySelectOneClick()
    if self.buyRecord ~= nil and self.buyRecord.is_sold_out == true then
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.BuyLimit)
    else
        local curdata = self:GetCurGoodsItem()
        if curdata == nil then
            return
        end

        local isOwned = self:CheckIsSellBuyGoodId(curdata.id)
        if isOwned then
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.BuyLimit)
        else
            local toBuyGoods = {}
            table.insert(toBuyGoods, curdata)
            local isBundleBuyAll = false
            local allBundleGoodsToBuy = self:_getBuyBundleItems()
            if allBundleGoodsToBuy ~= nil and #allBundleGoodsToBuy <= 1 then
                Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData, self.goodsItems,
                    toBuyGoods, self.isMallGift, isBundleBuyAll)
            else
                Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyPacks, nil, nil, self.shopData, toBuyGoods,
                    toBuyGoods, self.isMallGift, isBundleBuyAll)
            end
        end

        local enough = 2
        if self.isMallGift == false then
            --check currency enough
            local price = curdata.price

            local currency_type = self.shopData.BundleCurrencyType
            local currecny = Module.Currency:GetNumByItemId(currency_type)
            local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
            local currecny_sub = Module.Currency:GetNumByItemId(currency_type_sub)

            if currecny >= price then
                enough = 1
            else
                --use currency_type_sub
                if currency_type ~= currency_type_sub then
                    if currecny + currecny_sub >= price then
                        enough = 1
                    end
                end
            end
        end

        self:ReportViewDetailEvent(2, curdata.id, 0, enough)
    end


    self:ClearTips()
end

function ProductPreview:_OnButtonRoleSkinPreviewClick()
    if not self._wtButtonItemPreview:IsVisible() then
       return 
    end
    local curdata = self:GetCurGoodsItem()
    if curdata == nil then
        return
    end
    -- 打开配件预览界面  
    local imageSourceLogo = nil
    if self.shopData ~= nil then
        imageSourceLogo = self.shopData.ImageSourceLogo
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, curdata.id, 0, imageSourceLogo)
    -- 埋点
    -- LogAnalysisTool.SignButtonClicked(10154003)
end

function ProductPreview:_OnButtonPreviewClick()
    -- if true then
    --     Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoChangeView, nil, nil, "MallGainBundle", self.getAniDelayTime, self.getAniLastTime)

    --     local itemList = {}
    --     local data_change = self.goodsItems
    --     for _, propChange in ipairs( self.goodsItems) do
    --         if propChange then
    --             local item = ItemBase:New(propChange.id)
    --             local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
    --             local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
    --             if propinfo then
    --                 item:SetRawPropInfo(propinfo)
    --             else
    --                 item:SetRawPropInfo(propChange.prop)
    --             end
    --             table.insert(itemList, item)
    --         end
    --     end

    --     Timer.DelayCall(1, function()
    --         Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, itemList, nil, false, false, true)
    --     end)
    --     return
    -- end

    local curdata = self:GetCurGoodsItem()
    if curdata ~= nil then
        local item = ItemBase:New(curdata.id)
        local skinInfo = pb.WeaponSkinInfo:New()
        skinInfo.skin_id = curdata.prop.id
        skinInfo.skin_gid = curdata.prop.gid
        ItemBase.SetWeaponSkinInfo2PropInfo(curdata.prop, skinInfo)
        ItemBase.SetWeaponSkinID2PropInfo(curdata.prop, curdata.prop.id)
        ItemBase.SetWeaponSkinGUID2PropInfo(curdata.prop, curdata.prop.gid)
        item:SetRawPropInfo(curdata.prop)
        if item ~= nil then
            if item.itemMainType == EItemType.WeaponSkin or item.itemMainType == EItemType.Weapon then
                Module.Collection:ShowWeaponSkinDetailPage(item)
                -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item, nil, true)
            elseif item.itemMainType == EItemType.Adapter and item.itemSubType == ItemConfig.EAdapterItemType.Pendant then
                Module.Collection:ShowHangingDetailPage(item)
            else
                local anchorItem = self._wtButtonPreview
                -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item)
                if self._wtWaterFallList ~= nil then
                    local child = self._wtWaterFallList:GetItemByIndex(self.curSelectIndex)
                    if child ~= nil then
                        anchorItem = child
                    end
                end

                Module.ItemDetail:OpenItemDetailPanel(item, anchorItem, nil, nil, nil, nil, nil, nil, nil, nil)
            end
        end
    end

    self:ClearTips()
end

function ProductPreview:ClearTips()
    if DFHD_LUA == 1 then
        self:_OnUnhovered()
    else
        
        if self._wtDetailCheckBtn ~= nil then
            self._wtDetailCheckBtn:SetIsChecked(false)
        end
        -- self:_OnCheckStateChanged(false)
    end
end

function ProductPreview:RefreshSelectItemInfo()
    self._wtDetailCheckBtn:Collapsed()
    local curdata = self:GetCurGoodsItem()
    if curdata ~= nil then
        local itemData = ItemBase:New(curdata.id)
        if itemData ~= nil then
            if self._wtTextWeaponName ~= nil then
                self._wtTextWeaponName:SetText(itemData.name)
            end

            if self._wtQualityIcon ~= nil then
                self._wtQualityIcon:AsyncSetImagePath(Module.Collection.Config.QualityIconMapping[itemData.quality])
                self._wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(itemData.quality))
                self._wtQualityIcon:SelfHitTestInvisible()
            end

            if self._wtTextWeaponDesc ~= nil then
                local outStr = itemData.description
                local itemMainType = ItemHelperTool.GetMainTypeById(curdata.id)
                if itemMainType == EItemType.WeaponSkin then
                    if itemData.itemSubType == ItemConfig.EWeaponItemType.Melee then
                        local skinsInfo = Facade.TableManager:GetTable("WeaponSkin/MeleeWeaponSkinDataTable")
                        local skinInfo = skinsInfo[curdata.id]
                        if skinInfo then
                            outStr = skinInfo.SkinDescription
                        end
                    else
                        local skinsInfo = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
                        local skinInfo = skinsInfo[curdata.id]
                        if skinInfo then
                            outStr = skinInfo.SkinDescription
                        end
                    end
                end

                self._wtTextWeaponDesc:SetText(outStr)
            end

            -- if itemData.itemMainType == EItemType.WeaponSkin or itemData.itemMainType == EItemType.Weapon then
            --     self._wtButtonPreview:Visible()
            -- else
            --     self._wtButtonPreview:Collapsed()
            -- end

            if itemData.itemMainType == EItemType.Fashion then -- 红角时装
                local isShowFashionDetailTips = HeroHelperTool.IsShowFashionTipsDetail(itemData.id)
                if isShowFashionDetailTips then
                    self._wtDetailCheckBtn:Visible()
                    self._TipsText = Module.Hero.Config.Loc.HeroFashionTips
                end
            end
        end

        if self.isMallGift then
            self._wtTextPrice:Collapsed()
            self._wtButtonBuySelect:Collapsed()
            self._wtButtonItemPreview:Collapsed()
        else
            if self._wtTextPrice ~= nil and self._wtButtonBuySelect ~= nil then
                local isOwned = false 
                if (self:CheckIsSellBuyGoodId(curdata.id) or Server.CollectionServer:IsOwnedWeaponSkin(curdata.id, curdata.prop.gid) or Server.CollectionServer:IsOwnedMeleeSkin(curdata.id)) then
                    isOwned = true
                end
                if curdata.price > 0 then
                    self._wtButtonBuySelect:Visible()

                    if isOwned then
                        self._wtButtonBuySelect:Collapsed()
                        self._wtTextPrice:Collapsed()
                        -- self._wtButtonBuySelect:SetFinishType(1)
                        -- self._wtButtonBuySelect:SetIsEnabled(false)
                        -- self._wtButtonBuySelect:BP_SetMainTitle(StoreConfig.Loc.Owned)
                    else
                        local priceStr = string.format(StoreConfig.Loc.ItemPriceNormal,
                            Module.Currency:GetRichTxtImgByItemId(self.shopData.BundleCurrencyType),
                            MathUtil.GetNumberFormatStr(curdata.price, 1))
                        self._wtTextPrice:Visible()
                        self._wtTextPrice:SetText(priceStr)

                        self._wtButtonBuySelect:SetFinishType(0)
                        self._wtButtonBuySelect:SetIsEnabled(true)
                        self._wtButtonBuySelect:BP_SetMainTitle(StoreConfig.Loc.RecommendBundleBuySelectOne)
                    end
                else
                    self._wtTextPrice:Collapsed()
                    self._wtButtonBuySelect:Collapsed()
                    self._wtDetailCheckBtn:Visible()
                    self._TipsText = StoreConfig.Loc.RecommendBundleItemIsPresentTip
                    if self._wtTextWeaponDesc ~= nil then
                        self._wtTextWeaponDesc:SetText(StoreConfig.Loc.RecommendBundleItemIsPresent)
                    end
                end
            end
        end

        self:RefreshStoreModelInfo(curdata.id)

        if self._wtCommonDownload ~= nil then
            if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
                local moduleName = Module.ExpansionPackCoordinator:GetDownloadCategary(curdata.id)
                if moduleName ~= nil and moduleName ~= "None" then
                    local bDownloaded = self._wtCommonDownload:InitModuleKey(moduleName)
                    if bDownloaded then
                        self._wtCommonDownload:Hidden()
                    else
                        self._wtCommonDownload:Visible()
                    end
                else
                    self._wtCommonDownload:Hidden()
                end
            else
                self._wtCommonDownload:Hidden()
            end
        end
    end
end

function ProductPreview:CheckIsSellBuyGoodId(id)
    local isOwned = false

    if self.buyRecord ~= nil then

        if self.isMallGift then
            if self.shopData.LimitType ~= nil and self.shopData.LimitType > 0 and
                self.buyRecord.num >= self.shopData.LimitAmount then
                isOwned = true
            end
        else
            if self.buyRecord.is_sold_out then
                isOwned = true
            else
                for key, value in pairs(self.buyRecord.item_ids) do
                    if value == id then
                        isOwned = true
                        break
                    end
                end
            end
        end
    end

    return isOwned
end

function ProductPreview:RefreshStoreModelInfo(id)
    if self.lastLoadModelID == id then
        return
    end

    self.lastLoadModelID = id
    -- StoreLogic.ShowBoxModel(id)
    self:OnRefreshModel(id)
end

function ProductPreview:OnRefreshModel(itemId)

    if itemId == nil then
        return
    end

    local itemData = ItemBase:NewIns(itemId)
    if not itemData then
        return
    end

    self._videoBG:Collapsed()
    self._wtButtonItemPreview:Collapsed()

    if self._wtImageRoot ~= nil then
        self._wtImageRoot:Collapsed()
    end

    if self._wtImageSparyRoot ~= nil then
        self._wtImageSparyRoot:Collapsed()
    end

    Facade.HallSceneManager:ResetRootActorOffset()--重新设置rootActor的偏移
    Module.Hero:ShowOperatorWatch("None")--删除手表
    -- 设置组件的Type        
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence") -- 重置干员Sequence
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"StopPlaySequenceVoice") -- 停止播放干员语音

    local uiSettings = UITable[UIName2ID.StoreProductPreview]
    if uiSettings ~= nil then
        if itemData.itemMainType == EItemType.Fashion then
            uiSettings.LinkSubStage = ESubStage.HallHero
        else
            uiSettings.LinkSubStage = ESubStage.HallMall
        end
    end

    self._wtStaffLotteryVoiceWidget:StopAllItemPlayAndAnim() -- 暂停所有音频播放
    self._wtStaffLotteryVoiceWidget:Collapsed()

    if itemData.itemMainType == EItemType.Fashion then -- 红角时装
        self:ShowRewardFashion(itemId)
    elseif itemData.itemMainType == EItemType.WeaponSkin then -- 武器皮肤
        self:ShowRewardWeaponSkin(itemData)
    elseif itemData.itemMainType == EItemType.Adapter then -- 配件
        self:ShowRewardAdapter(itemData)
    elseif itemData.itemMainType == EItemType.VehicleSkin then -- 载具
        self:ShowRewardVehicle(itemData)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.AnimShow then -- 表演动画3p
        self:PlayCharacterAnim(itemData.id)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Gesture then -- 表演手势1p
        self:PlayGesture(itemData.id)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Lines then -- 台词
        self:ShowLinesInfos(itemData)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Watch then -- 手表
        self:ShowOperatorWatch(itemId)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Card then -- 名片
        self:ShowShowCardInfo()
        local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
        -- 静态图集路径
        local iconPath = ""
        if itemAsset ~= nil then
            iconPath = itemAsset.ItemIconPath
        end

        local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
        if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
            iconPath = gameItemRow.MallItemIcon
        end
        if iconPath ~= "" then
            self._wtImageRoot:Visible()
            self:SetSize(4)
            self.wtImageIcon:AsyncSetImagePath(iconPath, true)
        end
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SparyPaint then -- 喷漆
         -- 道具资源信息
         local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
         -- 静态图集路径
         local iconPath = ""
         if itemAsset ~= nil then
             iconPath = itemAsset.ItemIconPath
         end

         local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
         if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
             iconPath = gameItemRow.MallItemIcon
         end
        self:ShowSparyPaintInfo(itemData, iconPath)
    else -- 其他物品
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
        self:_SetDisplayType(EProductPreviewDisplayType.Other)

                -- DFImage_191
        if self.wtImageIcon ~= nil then
            -- 道具资源信息
            local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
            -- 静态图集路径
            local iconPath = ""
            if itemAsset ~= nil then
                iconPath = itemAsset.ItemIconPath
            end

            local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
            if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
                iconPath = gameItemRow.MallItemIcon
            end

            self._wtImageRoot:Visible()
            self._wtImageSparyRoot:Collapsed()

            local bSetSize = false
            if itemData.itemMainType == EItemType.HeroAccessory then
                if itemData.itemSubType == EHeroAccessroy.SparyPaint then
                    self._wtImageRoot:Collapsed()
                    self._wtImageSparyRoot:Visible()
                    local SprayPaintDatas = HeroHelperTool.GetAllSprayPaintData()
                    self.SprayPaintData = SprayPaintDatas[itemData.id]
                    if self.SprayPaintData ~= nil then
                        self:SetBigImgtImg(self.SprayPaintData.SprayPaintDisplay)
                    end
                else
                    self:SetSize(1)
                end

                bSetSize = true
            elseif itemData.itemMainType == EItemType.CollectionProp or itemData.id == ECurrencyItemId.Diamond then
                bSetSize = true
                if IsHD() then
                    self:SetSize(1)
                else
                    self:SetSize(5)
                end
            elseif itemData.itemMainType == EItemType.SocialAppearance then
                bSetSize = true
                if itemData.itemSubType == ESocialAppearanceType.SocialAvatarTab then
                    self:SetSize(1)
                elseif itemData.itemSubType == ESocialAppearanceType.SparyPaint then
                    
                elseif itemData.itemSubType == ESocialAppearanceType.SocialMilitaryTab then
                    self:SetSize(3)
                else
                    bSetSize = false
                end
            end

            if bSetSize == false then
                self:SetSize(0)
            end

            self.wtImageIcon:Hidden()
            if iconPath ~= "" then
                Timer.DelayCall(0.02, function()
                    self.wtImageIcon:AsyncSetImagePath(iconPath, true)
                    self.wtImageIcon:Visible()
                end)
            end

        end
    end
end

function ProductPreview:ShowRewardFashion(itemId)
    local fAllLevelFinishCallback = function()
        local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
        if curSubStage ~= ESubStage.HallHero then
            Facade.GameFlowManager:EnterSubStage(ESubStage.HallHero)
        end
        
        local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(itemId))
        if  self.SetCharacterSuitAvatarWithSequence == "None" then -- 播放干员Sequence
            Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, false, self.SetCharacterSuitAvatarWithSequence, true)
            self.SetCharacterSuitAvatarWithSequence = "State2"
        else -- 干员站姿
            Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, true, self.SetCharacterSuitAvatarWithSequence, true)
        end
        -- --设置当前展示的角色
        -- Module.Hero:SetCurShowHeroById(HeroId)
    end

    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallHero, true, nil, fAllLevelFinishCallback, false, 30)

    self._wtButtonItemPreview:Visible()
    -- self._wtBackgroundImg:Collapsed()
end

-- 3P动画
function ProductPreview:PlayCharacterAnim(itemId)    
    local fAllLevelFinishCallback = function()
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallHero)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade")--停止摄像机淡入淡出
        self:_SetDisplayType(EProductPreviewDisplayType.Other)

        local HeroActionAnimData =  HeroHelperTool.GetAllAnimShowData()
        if HeroActionAnimData[itemId] then
            local belongedHeorIds = HeroActionAnimData[itemId].BelongedHeroIDs
            local defFashionId = Server.HeroServer:GetHeroDefFashionID(belongedHeorIds[1])
            if defFashionId == 0 then
                defFashionId = 30000020006 -- 如果为空，默认为威龙
            end
            local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(defFashionId))
            Module.Hero:PlayActionShowAnim(tostring(HeroId), itemId)
            -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraAppearanceDisplayType", "CharacterShow", itemId)
            -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCharacterActionShow", defFashionId, itemId)

            -- local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(defFashionId))
            -- if HeroId ~= 0 then
            --     Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", tostring(HeroId))
            -- end
        end
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallHero, true, nil, fAllLevelFinishCallback, false, 30)
    
    -- self._wtBackgroundImg:Collapsed()
end

-- 1P手势动画
function ProductPreview:PlayGesture(itemId)
    self:_SetDisplayType(EProductPreviewDisplayType.Other)
    self._videoBG:Visible()
    Timer.DelayCall(0.3, function ()
        self._mediaComponent:Stop()
        self._mediaComponent:Play(itemId)
    end)
    -- self._wtBackgroundImg:Collapsed()
end

-- 显示手表
function ProductPreview:ShowOperatorWatch(itemId)    
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end
    Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 3, EOffsetType.ZOffset)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWatch", itemId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    self:_SetDisplayType(EProductPreviewDisplayType.Watch)

    -- self._wtBackgroundImg:Collapsed()
end

-- 武器皮肤
function ProductPreview:ShowRewardWeaponSkin(item)    

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetWeaponDisplayType", "")
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    --Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), item.id,
    --false, false)  
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeaponAutoBoundAdapter", item:GetRawDescObj(), false, false)
      
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")

    self:_SetDisplayType(EProductPreviewDisplayType.WeaponSkin)

    -- self._wtBackgroundImg:Collapsed()
end
-- 配件
function ProductPreview:ShowRewardAdapter(item)
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end
    Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 6, EOffsetType.ZOffset)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", true)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetShowWeaponPendant", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", true)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), item.id, 
    false, true)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    self:_SetDisplayType(EProductPreviewDisplayType.Adapter)
    
    -- self._wtBackgroundImg:Collapsed()
end

-- 载具
function ProductPreview:ShowRewardVehicle(item)
    if not item then
        return
    end
    local itemId = item.id
    local fAllLevelFinishCallback = function ()
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallVehicle)
        local vehicleId = VehicleHelperTool.GetItemIdBySkinID(itemId)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "LoadVehicleLevel", vehicleId, vehicleId)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVehicleDisplayType", vehicleId, 1)
        Module.Vehicle:SetQualityEnv(itemId)
        Module.Vehicle:LoadVehicleFromSkinID(itemId)
        self:_SetDisplayType(EProductPreviewDisplayType.VehicleSkin)
    end
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    
    if curSubStage ~= ESubStage.HallVehicle then
        Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallVehicle, true, nil, fAllLevelFinishCallback, false, 30)
    else
        fAllLevelFinishCallback()
    end
end

-- 台词包
function ProductPreview:ShowLinesInfos(item)
    local curdata = self:GetCurGoodsItem()

    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    self:_SetDisplayType(EProductPreviewDisplayType.Other)

    if  not curdata or not curdata.item_list then
        return
    end

    local itemIdList  = ""

    for k, v in ipairs(curdata.item_list) do
        if k ~=1 then
            itemIdList = itemIdList..","
        end
        itemIdList = itemIdList..tostring(v.id)
    end
    local linesDataRow = HeroHelperTool.GetLinesData(curdata.item_list[1].id)

    if not linesDataRow then
        logwarning("linesDataRow is nil, id:", curdata.item_list[1].id)
        return
    end

    local belongedHeroID  = linesDataRow.BelongedHeroIDs and linesDataRow.BelongedHeroIDs[1]

    self._wtStaffLotteryVoiceWidget:SetData(itemIdList,tonumber(belongedHeroID))
    self._wtStaffLotteryVoiceWidget:Visible()
end

function ProductPreview:ShowSparyPaintInfo(itemData, iconPath)
    self.DFScaleBox_Spray:Visible()
    -- self._wtBackgroundImg:Collapsed()

    Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    self:_SetDisplayType(EProductPreviewDisplayType.Other)

    if iconPath then
        if itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SparyPaint then --  喷漆
            local SprayPaintDatas = HeroHelperTool.GetAllSprayPaintData()
            self.SprayPaintData = SprayPaintDatas[itemData.id]
            if self.SprayPaintData ~= nil then
                self:SetBigImgtImg(self.SprayPaintData.SprayPaintDisplay)
            end
        end
    end
end
-- 名片
function ProductPreview:ShowShowCardInfo()
    Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    self:_SetDisplayType(EProductPreviewDisplayType.Other)
    -- self._wtBackgroundImg:Visible()
end

--设置喷漆大图
function ProductPreview:SetBigImgtImg(imgPath)
    self:PlayAnimation(self.WBP_Store_ProductPreview_spray, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if not imgPath or string.len(imgPath) <= 0 then return end
    ResImageUtil.AsyncLoadImgObjByPath(imgPath, true, self.SetTextureParam, self)
end

function ProductPreview:SetTextureParam(imageAsset, bAutoResize)
    if self.SprayPaintData then
        local DynamicMatIns = self.wtImageSpray:GetDynamicMaterial()
        if DynamicMatIns and imageAsset then
            DynamicMatIns:SetTextureParameterValue("Texture_Flipbook", imageAsset)
            local XOffset = self.SprayPaintData.SprayPaintOffset[1]
            local YOffset = self.SprayPaintData.SprayPaintOffset[2]
            if XOffset and YOffset then
                if self.SprayPaintData.SprayPaintOffset ~= nil and #self.SprayPaintData.SprayPaintOffset >= 2 then
                    DynamicMatIns:SetScalarParameterValue("X_Flipbook", XOffset)
                    DynamicMatIns:SetScalarParameterValue("Y_Flipbook", YOffset)
                end
            end
        end
    end
end
-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function ProductPreview:OnOpen()
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallWeaponShow)
end

--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
function ProductPreview:_OnServerProductInfoUpdate()
    if IsConsole() and self.shopData ~= nil and self.shopData.IsCash then
        local rechargeInfo = Server.PayServer:GetProductItem(self.shopData.ProductID)
        if rechargeInfo ~= nil then
            local display_price = rechargeInfo.current_price or ""
            if IsPS5Family() then
                display_price = rechargeInfo.display_price or ""
            end
            self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
            self._wtButtonBuyBundle:SetMainTitle(display_price)
        end
    end
end
--- END MODIFICATION

function ProductPreview:RegisterEventListeners()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._ModuleDownloadResult, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged, self._ModuleCheckResult, self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)

    self:AddLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick, self._OnStoreProductViewBundleItemClickClick, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyHotRecommendationSuc, self._OnStoreBuyHotRecommendationSuc,
        self)

    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyMallGiftSuc, self._OnStoreBuyHotRecommendationSuc,
        self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyRecordUpdate, self._OnStoreBuyRecordUpdate, self)

    Facade.ProtoManager:AddNtfListener("CSDirectPurchaseShippingNtf", self.OnPurchaseShippingNtf, self) --oto

    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连

    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnRewardPanelClosed, self) --奖励弹窗关闭

    if IsHD() then
        Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserClose:AddListener(self.OnBrowserClosed, self)
	end
    --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    if IsConsole() then
        self:AddLuaEvent(Server.PayServer.Events.evtOnGetProductInfo, self._OnServerProductInfoUpdate, self)
    end
    --- END MODIFICATION
end

function ProductPreview:UnRegisterEventListeners()
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged)

    self:RemoveLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyHotRecommendationSuc)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyMallGiftSuc)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyRecordUpdate)

    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)
    if IsHD() then
        Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserClose:RemoveListener(self.OnBrowserClosed, self)
    end

    self:RemoveLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnRewardPanelClosed)
    --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    if IsConsole() then
        self:RemoveLuaEvent(Server.PayServer.Events.evtOnGetProductInfo)
    end
    --- END MODIFICATION
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function ProductPreview:OnClose()
    self:RemoveAllLuaEvent()

    self.lastLoadModelID = 0

    -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "Weapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", nil, 0,
        false, false)
end

function ProductPreview:OnShowBegin()
    -- self.wtImageIcon:Collapsed()
    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end

    if IsPS5Family() and self.shopData ~= nil and self.shopData.IsCash then
        local rechargeInfo = Server.PayServer:GetProductItem(self.shopData.ProductID)
        if rechargeInfo then
            UDFMPlatformUtils.ShowPlatformStoreUI()
        end
    end
    --- END MODIFICATION

    local currencyTypeList = { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond }
    if Server.CollectionServer:NeedShowConsumeTickets() then
        table.insert(currencyTypeList, ECurrencyClientId.ConsumerCoupon)
    end
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, currencyTypeList)
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)
    end

    self:RegisterEventListeners()
    self:_RefreshShowInfo()
    self:RefreshSelectItemInfo()
end
-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function ProductPreview:OnShow()
    self.tickCountDown = 0
    self.tickDeltaTimeCountDown = 0

    self.bLoadedModel = false
    self._wtButtonBuySelect:Collapsed()

    self:_RefreshShowInfo()
    self:RefreshSelectItemInfo()

    local viewType = 0
    local bundleid = 0
    if self.isMallGift == true then
        viewType = 2
        bundleid = self.shopData.GoodsId
    else
        viewType = 1
        bundleid = self.shopData.TabId
    end

    LogAnalysisTool.DoSendStoreViewPageReportLog(6, 0, viewType, bundleid)

    LuaTickController:Get():RegisterTick(self)
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function ProductPreview:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    self:_SetDisplayType(EProductPreviewDisplayType.None)

    if IsPS5Family() then
        UDFMPlatformUtils.HidePlatformStoreUI()
    end
end
--- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function ProductPreview:OnHide()
    self.lastLoadModelID = 0
    
    --请求一次补发货
    if Server.PayServer:IsGoogleEnable() then
        Module.Pay:ReapplyReceipt()
    end

    self:UnRegisterEventListeners()

    self:ClearTips()

    LuaTickController:Get():RemoveTick(self)

    Facade.HallSceneManager:ResetRootActorOffset()--重新设置rootActor的偏移

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")

    self._wtStaffLotteryVoiceWidget:Collapsed()

    self._videoBG:Collapsed()
    self._mediaComponent:Stop()
end

function ProductPreview:_OnStoreProductViewBundleItemClickClick(position)
    if self.curSelectIndex == position then
        return
    end

    self.curSelectIndex = position
    self._wtWaterFallList:RefreshAllItems()
    self:RefreshSelectItemInfo()

    self:ClearTips()

    local curdata = self:GetCurGoodsItem()
    if curdata ~= nil then
        self:ReportViewDetailEvent(1, curdata.id, 0, 0)
    end

    self:_RefreshGampadBottomBar()
    self:_OnUnhovered()
end

function ProductPreview:_OnStoreBuyRecordUpdate()
    if self.SendShopGetBuyRecord == true then
        self.SendShopGetBuyRecord = false
        self:_RefreshShowInfo()

        if self.isMallGift == false and self.isRewardPanelClosed == true then
            -- check all reward get
            if self.bIsSellOut then
                self.isRewardPanelClosed = false
                Module.Store.Config.evtProductPreviewGetAndPlayEfx:Invoke(-1)

                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_Store_Bundle_AllGet)
                Timer.DelayCall(0.6, function()
                    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UI_Store_Bundle_AllGet)
                end)
            else
                Module.Store.Config.evtProductPreviewGetAndPlayEfx:Invoke(self.nowGetRewardID)
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_Store_Bundle_Oneshot)
                -- Timer.DelayCall(0.15, function()
                --     Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UI_Store_Bundle_AllGet)
                -- end)
            end
        end

        -- self.curSelectIndex = 1
        self.lastLoadModelID = nil
        self:RefreshSelectItemInfo()
        self._wtWaterFallList:RefreshAllItems()
    end
end

function ProductPreview:Update(dt)
    self.tickDeltaTimeCountDown = self.tickDeltaTimeCountDown + dt
    if self.tickCountDown == 1 and self.tickDeltaTimeCountDown >= 1 then
        self.tickDeltaTimeCountDown = 0
        --update countdown
        self.offlineSeconds = self.offlineSeconds - 1
        if self.offlineSeconds < 0 then
            self.offlineSeconds = 0
        end
        local showStr = ""
        local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self.offlineSeconds)
        if self.offlineSeconds > 3600 * 24 then
            showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(self.offlineSeconds, "DDHH_Second")
        elseif self.offlineSeconds > 3600 then
            showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
        else
            showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinSecTip, min, sec)
        end

        self._wtTextCountDown:SetText(showStr)
    end
end

function ProductPreview:OnPurchaseShippingNtf(res)
    loginfo("[ProductPreview] OnPurchaseShippingNtf")
    if res == nil then
        loginfo("[ProductPreview] OnPurchaseShippingNtf, but res == nil")
        return
    end

    -- --update buy record
    -- Server.StoreServer:SendShopGetBuyRecordReq()
    Module.Reward:EnableNTFCall("Hero", false)

    local itemList = {}
    local data_change = res.data_change
    if data_change and data_change.prop_changes then
        local prop_changes = data_change.prop_changes
        for _, propChange in ipairs(prop_changes) do
            if propChange.prop then
                local item = ItemHelperTool.CreateItemByPropInfo(propChange.prop, propChange)
                table.insert(itemList, item)
            end
        end
    end

    local currency_changes = data_change.currency_changes
    for _, currencyChange in ipairs(currency_changes) do
        if currencyChange.delta > 0 then
            local giftItem = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
            table.insert(itemList, giftItem)
        end
    end

    local bIsBorwerOpen = Module.GCloudSDK:IsWebBrowserOpen()
    if bIsBorwerOpen and IsHD() then
        self.bNeedShowRewardPanel = true
        self.needRewardPanelItemList = itemList
    else
        self.bNeedShowRewardPanel = false
        if #itemList > 1 and self.isMallGift == false then
            if self.shopData.IsCollaboration == 0 then
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoChangeView, nil, nil, "MallGainBundle", self.getAniDelayTime, self.getAniLastTime)
                Timer.DelayCall(1.8, function()
                Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
            end)
            else
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoChangeView, nil, nil, self.shopData.GainThemeBundle ,self.shopData.GainBundleDelayTime, self.getAniLastTime)
                Timer.DelayCall(self.getAniDelayTime + self.shopData.GainBundleDelayTime, function()
                Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
                end)
            end
        else
            Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
        end
    end
end

function ProductPreview:OnBrowserClosed()
    if self.bNeedShowRewardPanel == true and self.needRewardPanelItemList ~= nil then
        if #self.needRewardPanelItemList > 1 and self.isMallGift == false then
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoChangeView, nil, nil, "MallGainBundle", self.getAniDelayTime, self.getAniLastTime)
            Timer.DelayCall(1.8, function()
                Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, self.needRewardPanelItemList, nil, false, false, true)
            end)
        else
            Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, self.needRewardPanelItemList, nil, false, false, true)
        end

        self.bNeedShowRewardPanel = false
        self.needRewardPanelItemList = nil
    end
end

function ProductPreview:_OnRelayConnected()
    self.SendShopGetBuyRecord = true
    Server.StoreServer:SendShopGetBuyRecordReq()
end

function ProductPreview:_OnRewardPanelClosed()
    logerror("[ProductPreview] _OnRewardPanelClosed")
    
    self.isRewardPanelClosed = true
    self.SendShopGetBuyRecord = true
    -- Module.Store.Config.evtProductPreviewGetAndPlayEfx:Invoke()
    Server.StoreServer:SendShopGetBuyRecordReq()

    -- 国内外IOS和海外GooglePlay评分
    if PLATFORM_IOS or (PLATFORM_ANDROID and (not IsBuildRegionCN())) then
        Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.Shop)
    end
end

-----gift
function ProductPreview:_getIsGiftBuyLimited()
    return StoreLogic.GetMallGiftIsBuyLimitedByShopData(self.shopData)
end

-----bundel
function ProductPreview:_getIsAnyGoodSelled()
    for k, v in pairs(self.goodsItems) do
        if Server.StoreServer:IsHaved(v.id) then
            return true
        end
    end
    return false
end

function ProductPreview:_getBuyBundleItems()
    local ret = {}
    for k, v in pairs(self.goodsItems) do
        if v.dis_price > 0 then
            local isSell = self:CheckIsSellBuyGoodId(v.id)
            local bIsOwned = Server.StoreServer:IsHaved(v.id)

            if isSell == false and bIsOwned == false then
                table.insert(ret, v)
            end
        end
    end

    return ret
end

function ProductPreview:_getBuyBundlePrice()
    --  bundleItem {
    --     uint64 id = 1;
    --     int64 num = 2;
    --     int64 dis_price = 3; // 折后价
    --     int64 price = 4; // 原价
    --   }
    local price = 0
    local priceOrign = 0

    local needBuyCount = 0
    for k, v in pairs(self.goodsItems) do
        if v.dis_price > 0 then
            local isSell = self:CheckIsSellBuyGoodId(v.id)
            local collectionItemInfo = Server.CollectionServer:GetWeaponSkinIfOwned(v.id)
            local bIsOwned = collectionItemInfo ~= nil and collectionItemInfo.num > 0

            if isSell == false and bIsOwned == false then
                needBuyCount = needBuyCount + 1
                price = price + v.dis_price
                priceOrign = priceOrign + v.price
            end
        end
    end

    if needBuyCount <= 1 then
        price = priceOrign
    end

    return price, priceOrign
end

function ProductPreview:_OnStoreBuyHotRecommendationSuc(dataChange, isCashBuy, midasInfo)
    if isCashBuy == false then
        --update buy record
        loginfo("[ProductPreview] _OnStoreBuyHotRecommendationSuc isCashBuy == false")
        local itemList = {}
        if dataChange then
            if dataChange.prop_changes then
                local prop_changes = dataChange.prop_changes
                for _, propChange in ipairs(prop_changes) do
                    if propChange.prop then
                        local bShowConsumerCoupon = true
                        if propChange.prop.id == Server.StoreServer:GetTimeLimitComsumeCouponId() then -- 限时消费券只有add时才展示
                           bShowConsumerCoupon = propChange.prop.num > 0
                        end
                        if bShowConsumerCoupon then
                            local item = ItemHelperTool.CreateItemByPropInfo(propChange.prop, propChange)
                            item.bGiveaway = propChange.is_presented
                            table.insert(itemList, item)
                            self.nowGetRewardID = propChange.prop.id
                        end
                    end
                end
            end
        end

        if #itemList > 1 and self.isMallGift == false then
            if self.shopData.IsCollaboration == 0 then
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoChangeView, nil, nil, "MallGainBundle", self.getAniDelayTime, self.getAniLastTime)
                Timer.DelayCall(1.8, function()
                Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
                end)
            else
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoChangeView, nil, nil, self.shopData.GainThemeBundle ,self.shopData.GainBundleDelayTime, self.getAniLastTime)
                Timer.DelayCall(self.shopData.GainBundleDelayTime + self.getAniLastTime, function()
                Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
                end)
            end
        else
            Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
        end
    else
        loginfo("[ProductPreview] _OnStoreBuyHotRecommendationSuc isCashBuy == true")
        Module.Pay:Pay(midasInfo)
    end
    self._wtWaterFallList:ScrollToIndex(1)
end

function ProductPreview:_OnCloseBtnClicked()

end

function ProductPreview:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end

function ProductPreview:_OnTabPressed()
    Facade.UIManager:CloseUI(self)
end

function ProductPreview:ReportViewDetailEvent(eventType, eventParam, payType, payParam)
    local BindType = 0
    local BundleID = 0
    if self.isMallGift then
        BindType = 2
        BundleID = self.shopData.GoodsId
    else
        BindType = 1
        BundleID = self.shopData.TabId
    end

    LogAnalysisTool.DoSendStoreViewDetailEventReportLog(BindType, BundleID, eventType, eventParam, payType, payParam)
end

function ProductPreview:GetItemsSortByGoodsString(goodsStr)
    local items = {}
    if goodsStr == nil then
        return items
    end

    local bGoodsFormat = false
    if string.sub(goodsStr, 1, 2) == "{{" and string.sub(goodsStr, -2) == "}}" then
        bGoodsFormat = true
    end

    if bGoodsFormat == false then
        return items
    end

    goodsStr = goodsStr:sub(2, -2)
    local items = {}

    for item_str in string.gmatch(goodsStr, "{[^}]+}") do
        item_str = item_str:sub(2, -2)
        local item = {}

        local data = {}
        for value in string.gmatch(item_str, "[^,]+") do
            value = tonumber(value)
            table.insert(data, value)
        end

        item.id   = data[1]
        item.sort = data[2]

        table.insert(items, item)
    end

    return items
end

function ProductPreview:_ModuleDownloadResult(moduleName, bSuccess, errorCode, bShowTips)
    if self._wtCommonDownload == nil then
        return
    end

    --if IsMobile() or IsInEditor() then
    logerror("[ProductPreview] _ModuleDownloadResult ")
    local curdata = self:GetCurGoodsItem()
    if curdata ~= nil then
        local nowModuleName = Module.ExpansionPackCoordinator:GetDownloadCategary(curdata.id)
        if nowModuleName ~= nil and nowModuleName ~= "None" then
            local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(nowModuleName)
            if bDownloaded then
                logerror("[ProductPreview] _ModuleDownloadResult bDownloaded:true")
                self._wtCommonDownload:Hidden()
                self:OnRefreshModel(curdata.id)
            else
                logerror("[ProductPreview] _ModuleDownloadResult bDownloaded:false")
                self._wtCommonDownload:Visible()
            end
        else
            self._wtCommonDownload:Hidden()
        end
    end
    --end
end

function ProductPreview:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_ModuleDownloadResult(moduleName, isSuccess)
end

function ProductPreview:_ModuleCheckResult()
    if self._wtCommonDownload == nil then
        return
    end

    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        local curdata = self:GetCurGoodsItem()
        if curdata ~= nil then
            local moduleName = Module.ExpansionPackCoordinator:GetDownloadCategary(curdata.id)
            if moduleName ~= nil and moduleName ~= "None" then
                local bDownloaded = self._wtCommonDownload:InitModuleKey(moduleName)
                if bDownloaded then
                    self._wtCommonDownload:Hidden()
                else
                    self._wtCommonDownload:Visible()
                end
            else
                self._wtCommonDownload:Hidden()
            end
        end
    end
end

function ProductPreview:GetCurGoodsItem()
    return self.goodsItems[self.curSelectIndex]
end

function ProductPreview:_WeaponRotationX(value)
    if not IsHD() then
        return
    end

    self._RotationVec.X = value * -1 * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end

end
function ProductPreview:_WeaponRotationY(value)
    if not IsHD() then
        return
    end

    self._RotationVec.Y = value * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end
end

function ProductPreview:_WeaponScaleUp(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function ProductPreview:_WeaponScaleDown(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * -1 * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()

    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function ProductPreview:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 切换页签的时候也会触发onshowbegin，要确保visible的时候才初始化手柄功能
    if not self:IsVisible() then
        return
    end

    self.hasTipClick = false

    -- 配置keyIcon
    if self._wtButtonBuyBundle then
        self._wtButtonBuyBundle:SetDisplayInputAction("MallPurchase", true, nil, true)
    end

    if self._wtButtonBuySelect then
        self._wtButtonBuySelect:SetDisplayInputAction("MallPurchaseSeparately", true, nil, true)
    end

    if self._wtButtonItemPreview then
        self._wtButtonItemPreview:SetDisplayInputAction("Accessory", true, nil, true)
    end

    -- 配置输入
    if not self._Purchase then
        self._Purchase = self:AddInputActionBinding(
        "MallPurchase", 
        EInputEvent.IE_Pressed, 
        self._OnButtonBuyBundleClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    if not self._PurchaseSeparately then
        self._PurchaseSeparately = self:AddInputActionBinding(
        "MallPurchaseSeparately", 
        EInputEvent.IE_Pressed, 
        self._OnButtonBuySelectOneClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    if not self._ItemPreview then
        self._ItemPreview = self:AddInputActionBinding(
        "Accessory", 
        EInputEvent.IE_Pressed, 
        self._OnButtonRoleSkinPreviewClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    -- 设置导航
    if self._wtWaterFallList then
        if not self._NavGroup_WaterFall then
            self._NavGroup_WaterFall = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList , self, "Hittest")
        end
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._wtWaterFallList)
        self._NavGroup_WaterFall:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WaterFall)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    end
    
    self:_RefreshGampadBottomBar()

    --武器旋转的输入
    if self._RotationX == nil then
        self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
    end
    if self._RotationY == nil then
        self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleUp == nil then
        self._ScaleUp = self:AddAxisInputActionBinding("Common_Right_Trigger_Axis", self._WeaponScaleUp, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleDown == nil then
        self._ScaleDown = self:AddAxisInputActionBinding("Common_Left_Trigger_Axis", self._WeaponScaleDown, self, EDisplayInputActionPriority.UI_Stack)
    end
end

function ProductPreview:_RefreshGampadBottomBar()
    local summaryList = {}
    if self._wtButtonPreview then
        table.insert(summaryList, {actionName = "Store_ItemDetail", func = self._OnButtonPreviewClick, caller = self, bUIOnly = false, bHideIcon = false})
    end
    if self._wtDetailCheckBtn ~= nil and self._wtDetailCheckBtn:IsVisible() then
        table.insert(summaryList, {actionName = "Assembly_Confirm", func = self._OnTipClick, caller = self, bUIOnly = false, bHideIcon = false})
    end
    table.insert(summaryList, {actionName = "RotateItem", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function ProductPreview:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_WaterFall = nil
    self._NavGroup_ButtonRoot = nil

    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end

    if self._PurchaseSeparately then
        self:RemoveInputActionBinding(self._PurchaseSeparately)
        self._PurchaseSeparately = nil
    end

    if self._ItemPreview then
        self:RemoveInputActionBinding(self._ItemPreview)
        self._ItemPreview = nil
    end

end
--- END MODIFICATION

---@param inType  EProductPreviewDisplayType
function ProductPreview:_SetDisplayType(inType)
    if self._DisplayType ~= inType then
        self._DisplayType = inType
        self:_OnDisplayTypeChange(inType)
    end
end

---@param newType EProductPreviewDisplayType
function ProductPreview:_OnDisplayTypeChange(newType)
    if newType == EProductPreviewDisplayType.Watch or newType == EProductPreviewDisplayType.WeaponSkin or newType == EProductPreviewDisplayType.Adapter or newType == EProductPreviewDisplayType.None then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCanInteractState", true)
    else
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetCanInteractState", false)
    end
end

return ProductPreview
