----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingLanguageCell : LuaUIBaseView
local SystemSettingLanguageCell = ui("SystemSettingLanguageCell")
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local UKismetLocalizationLibrary = import "KismetLocalizationLibrary"

local function log(...)
    loginfo("[SystemSettingLanguageCell]", ...)
end

function SystemSettingLanguageCell:Ctor()
    self._wtMainBtn = self:Wnd("Button_231", UIButton)
    self._wtCheckBox = self:Wnd("CheckBox_01", UICheckBox)
    self._wtMainText = self:Wnd("CultureText", UITextBlock)
    self._wtCurProgressImg = self:Wnd("wtCurRankScoreScheduleImg", UIProgressBar)
    self._wtWaitDownBtn = self:Wnd("DFButton_0", UIButton)
    self._wtDowningBtn = self:Wnd("DFButton_1", UIButton)
    self._wtPauseDownBtn = self:Wnd("DFButton", UIButton)

    self._wtMainBtn:Event("OnClicked", self.OnItemClicked, self)
    self._wtCheckBox:SetCallback(self.OnItemClicked, self)

    self._wtWaitDownBtn:Event("OnClicked", self.OnWaitDownClicked, self)
    self._wtDowningBtn:Event("OnClicked", self.OnDowningClicked, self)
    self._wtPauseDownBtn:Event("OnClicked", self.OnPauseClicked, self)

    self.displayName = ""

    self._OnDownloadingPausedCb = nil
    self._OnDownloadingFinishedCb = nil
end

function SystemSettingLanguageCell:GetCulture()
    return self.savedCulture
end

function SystemSettingLanguageCell:OnShow()
    -- 定时刷新功能
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
    self.tickHandle = Timer:NewIns(0.5, 0)
    self.tickHandle:AddListener(function()
        self:RefreshState()
    end, self)
    self.tickHandle:Start()
    -- self:RefreshState()
end

function SystemSettingLanguageCell:OnHide()
    self:ClearTimer()
    self.has_high_low_res = nil
end

function SystemSettingLanguageCell:ClearTimer()
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
end

---@param culture string
---@param fItemSelectCb function 选中回调
function SystemSettingLanguageCell:InitItem(culture, displayInfo, fItemSelectCb, fOnDownloadingPausedCb, fOnDownloadingFinishedCb)
    log("Cur Cell Culture = ", culture)
    self.savedCulture = culture
    self:InitText(displayInfo)
    self.displayName = displayInfo.displayName
    self.ModuleKey = displayInfo.downloadKey
    self.itemSelectedCb = fItemSelectCb

    self._OnDownloadingPausedCb = fOnDownloadingPausedCb
    self._OnDownloadingFinishedCb = fOnDownloadingFinishedCb
    self.has_high_low_res = displayInfo.has_H_or_L_res
end

function SystemSettingLanguageCell:InitText(displayInfo)
    log("Cur Cell Text = ", displayInfo)
    self._wtMainText:SetText(displayInfo.displayName)
end

function SystemSettingLanguageCell:OnItemClicked()
    if self.btnStateType == Module.CommonWidget.Config.EVODownloadType.Downloaded then
        if self.itemSelectedCb then
            local func = self.itemSelectedCb
            func(self.savedCulture)
        end
    elseif self.btnStateType == Module.CommonWidget.Config.EVODownloadType.WaitDown then
        self:OnWaitDownClicked()
    elseif self.btnStateType == Module.CommonWidget.Config.EVODownloadType.Donwing then
        self:OnDowningClicked()
    elseif self.btnStateType == Module.CommonWidget.Config.EVODownloadType.PauseDown then
        self:OnWaitDownClicked()
    end
end

---初始化或者刷新时使用，此时不触发回调
---@param bSelected boolean
function SystemSettingLanguageCell:SetSelected(bSelected)
    self:SetCppValue("BP_Selected", bSelected or false)

    if self.BP_Set_Type then
        self:BP_Set_Type()
    end
end

-- 设置语音包ui下载状态
function SystemSettingLanguageCell:SetDownLoadType(down_type)
    if self.SetType then
        self:SetType(down_type,false)
    end
end

-- 点击开始下载
function SystemSettingLanguageCell:OnWaitDownClicked()
    LiteDownloadManager:DownloadByModuleName(self.ModuleKey)
    self:RefreshState()
end

-- 点击暂停下载
function SystemSettingLanguageCell:OnDowningClicked()
    LiteDownloadManager:CancelByModuleName(self.ModuleKey)
    self:RefreshState()

    if self._OnDownloadingPausedCb then
        self._OnDownloadingPausedCb()
    end
end

-- 点击继续下载
function SystemSettingLanguageCell:OnPauseClicked()
    LiteDownloadManager:DownloadByModuleName(self.ModuleKey)
    self:RefreshState()
end

function SystemSettingLanguageCell:RefreshState()
    if self.ModuleKey == "" then
        self.btnStateType = Module.CommonWidget.Config.EVODownloadType.Downloaded
        self:SetDownLoadType(self.btnStateType)
        self._wtMainText:SetText(self.displayName)
        self:ClearTimer()
        return
    end

    local pufferInitSucceed = LiteDownloadManager:IsPufferInitSucceed()
    if pufferInitSucceed == false then
        self.btnStateType = Module.CommonWidget.Config.EVODownloadType.WaitDown
        self:SetDownLoadType(self.btnStateType)
        return
    end

    if self.ModuleKey == "" then
        self.btnStateType = Module.CommonWidget.Config.EVODownloadType.Downloaded
        self:SetDownLoadType(self.btnStateType)
        self._wtMainText:SetText(self.displayName)
        return
    end

    local bHDHasBeenDownloaded = LiteDownloadManager:IsHDDownloadStatus()
    if bHDHasBeenDownloaded and self.has_high_low_res then -- 是否高清
        self.ModuleKey = LiteDownloadManager:GetHDRuntimeNameByModuleName(self.ModuleKey)
    end

    local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(self.ModuleKey)
    local nowSize = LiteDownloadManager:GetNowSizeByModuleName(self.ModuleKey)
    local totalSize = LiteDownloadManager:GetTotalSizeByModuleName(self.ModuleKey)
    if bDownloaded then -- 下载完成
        self.btnStateType = Module.CommonWidget.Config.EVODownloadType.Downloaded
        self:SetDownLoadType(self.btnStateType)
        self._wtMainText:SetText(self.displayName)

        if self._OnDownloadingFinishedCb and self.isNeedFinishTips then
            self._OnDownloadingFinishedCb() -- 通知到外部下载完成
            self.isNeedFinishTips = nil
        end
        -- if self.isNeedFinishTips then
        --     if string.find(self.ModuleKey, "VoiceAudio") then
        --         Module.IrisSafeHouse:ShowVoDownFinishTipsWindow()
        --     end
        --     
        --     self:OnItemClicked()
        --     self.isNeedFinishTips = nil
        -- end
        self:ClearTimer()
    else
        local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(self.ModuleKey)
        local isWaiting = LiteDownloadManager:IsWaitingByModuleName(self.ModuleKey)

        if totalSize == nil or totalSize <= 0 then
            self.btnStateType = Module.CommonWidget.Config.EVODownloadType.WaitDown
            self:SetDownLoadType(self.btnStateType)
            local parms = {
                cultureName = self.displayName,
                PackageSize = "-MB"
            }
            self._wtMainText:SetText(StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.LocalVOSizeTxt, parms))
            return
        end

        local progressInfo = LiteDownloadManager:GetDownloadProgressInfoByModuleName(self.ModuleKey)
        if isDownloading then -- 下载中
            self.isNeedFinishTips = true
            self.btnStateType = Module.CommonWidget.Config.EVODownloadType.Donwing
            self:SetDownLoadType(self.btnStateType)
        elseif isWaiting then -- 等待其他下载完成
            self.btnStateType = Module.CommonWidget.Config.EVODownloadType.WaitDown
            self:SetDownLoadType(self.btnStateType)
        else
            if progressInfo.PercentValue > 0 then -- 
               self.isNeedFinishTips = true
               self.btnStateType = Module.CommonWidget.Config.EVODownloadType.PauseDown
               self:SetDownLoadType(self.btnStateType)
            else
                self.btnStateType = Module.CommonWidget.Config.EVODownloadType.WaitDown
                self:SetDownLoadType(self.btnStateType)
            end
        end
        if self._wtCurProgressImg ~= nil then
            self._wtCurProgressImg:SetPercent(progressInfo.PercentValue / 100)
        end
        local parms = {
            cultureName = self.displayName,
            PackageSize = progressInfo.FormatTotal2SubSize
        }
        self._wtMainText:SetText(StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.LocalVOSizeTxt, parms))
    end
end

return SystemSettingLanguageCell