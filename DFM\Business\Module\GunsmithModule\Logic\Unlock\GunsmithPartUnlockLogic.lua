----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------


local GunsmithUIParam = require "DFM.Business.DataStruct.GunsmithStruct.GunsmithUIParam"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local EGunsmithPartMPUnlockMethod = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithPartMPUnlockMethod"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GPModularWeaponDescLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithCPPLogic.GPModularWeaponDescLogic"

local GunsmithPartUnlockLogic = {}

function GunsmithPartUnlockLogic.OpenPurchaseUI(description, bEquipWhenPurchaseSuccess)
    Facade.UIManager:AsyncShowUI(UIName2ID.GunsmithFluctuationPopWindow, nil, nil, description, bEquipWhenPurchaseSuccess)
end

function GunsmithPartUnlockLogic.OpenWeaponUpgradePanel(itemBase)
    local uiParam = GunsmithUIParam.SetFromItemBase(itemBase, ESlotGroup.MPApply)
    uiParam:SetMainTabIndex(5)
    local mainUIInst = Facade.UIManager:GetStackUIByUINavId(UIName2ID.GunsmithMainUI)
    if mainUIInst then
        mainUIInst = getfromweak(mainUIInst)
    end
    if not mainUIInst then
        Module.Gunsmith:OpenMainUI(uiParam)
        return
    end
    Facade.UIManager:TryPopToStackUI(mainUIInst)
    mainUIInst:OnRepeatOpen(uiParam)
end

function GunsmithPartUnlockLogic.ProcessUnlockMP(weaponGUID)
    local itemBase = Server.InventoryServer:GetItemByGid(weaponGUID, ESlotGroup.MPApply)
    GunsmithPartUnlockLogic.OpenWeaponUpgradePanel(itemBase)
end

function GunsmithPartUnlockLogic.ProcessUnlock(group, weaponGUID, description, bEquipWhenPurchaseSuccess)
    if group ~= ESlotGroup.MPApply then
        GunsmithPartUnlockLogic.OpenPurchaseUI(description, bEquipWhenPurchaseSuccess)
        return
    end

    -- GunsmithPartUnlockLogic.ProcessUnlockMP(weaponGUID)
    GunsmithPartUnlockLogic.OpenUnlockPathUI(description)
end

--本地坐标转成世界坐标
function GunsmithPartUnlockLogic.GetGlobalPosAndSizeByWidget(widget)
    if widget == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

    if not widget.GetCachedGeometry then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local alignWidgetGeometry = widget:GetCachedGeometry()
	local itemScreenPosLT = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    local itemScreenPosRB = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(1, 1))

    return itemScreenPosLT,  FVector2D(itemScreenPosRB.X - itemScreenPosLT.X, itemScreenPosRB.Y- itemScreenPosLT.Y)
end

--世界坐标转换成本地坐标
function GunsmithPartUnlockLogic.GetLocalPosAndSize(parentWidget, globalPos, globalSize)
    if parentWidget == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local parentWidgetGeometry = parentWidget:GetCachedGeometry()
    if parentWidgetGeometry == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local localPos = parentWidgetGeometry:AbsoluteToLocal(globalPos)
    local localSize = parentWidgetGeometry:AbsoluteToLocal(globalPos + globalSize) - localPos
    return localPos,  localSize
end

--**********
--WaterfallScrollBox慎用,不要刷新count为零情况,刷新零,Box会清除一次对象池;框架冲突
--ClearSubUIByParent:在父类容器的ui被onclose的时候执行,也只能在父容器ui的onclose的时候执行,否则将失去复用。
--Gunsmith成长之路获取subUiInst通用接口(获取/隐藏/清除)
GunsmithPartUnlockLogic.GetSubUiInst = function(caller, uiNavId, container, itemType, index)
    if caller == nil or uiNavId == nil or container == nil then
        return
    end
    --整理表格
    if caller.subInstIdMap == nil then
        caller.subInstIdMap = {}
    end
    if caller.subInstIdMap[uiNavId] == nil then
        caller.subInstIdMap[uiNavId] = {}
    end
    if caller.subInstIdMap[uiNavId][tostring(container)] == nil then
        caller.subInstIdMap[uiNavId][tostring(container)] = {}
    end
    --获取sub
    local uiIns, instanceID
	if itemType == nil then
        Facade.UIManager:ClearSubUIByParent(caller, container)--通过容器清除,只能onclose的时候执行
        caller.subInstIdMap[uiNavId][tostring(container)] = nil
    elseif itemType == 0 then
        for key, instanceID in ipairs(caller.subInstIdMap[uiNavId][tostring(container)] or {}) do
            Facade.UIManager:RemoveSubUI(caller, uiNavId, instanceID)--通过id隐藏sub实例
        end
        caller.subInstIdMap[uiNavId][tostring(container)] = nil
    elseif itemType == 1 then
        if caller.subInstIdMap[uiNavId][tostring(container)] then
            local length = #caller.subInstIdMap[uiNavId][tostring(container)]
            if index and index <= length then
                for key, instId in ipairs(caller.subInstIdMap[uiNavId][tostring(container)] or {}) do
                    if key == index then
                        uiIns, instanceID = Facade.UIManager:GetSubUI(caller, uiNavId, instId)
                        local btn = getfromweak(uiIns)
                        if btn then
                            btn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                            return btn, instanceID
                        end
                    end
                end
            end
        end
        uiIns, instanceID = Facade.UIManager:AddSubUI(caller, uiNavId, container)--获取sub实例
    elseif itemType == -1 then
        for key, instId in ipairs(caller.subInstIdMap[uiNavId][tostring(container)] or {}) do
            uiIns, instanceID = Facade.UIManager:GetSubUI(caller, uiNavId, instId)
            local btn = getfromweak(uiIns)
            if btn then
                btn:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
        return
	end
	if uiIns and instanceID then
		local btn = getfromweak(uiIns)
        if btn then
            table.insert(caller.subInstIdMap[uiNavId][tostring(container)], instanceID)--存储instanceID
            btn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			return btn, instanceID
		end
	end
	return nil
end

--模块通用播放动画接口
GunsmithPartUnlockLogic.OnPlayAnim = function(caller, animName, isLoop, isToStartFrame, isStopAllAnim, startTime, animFinishFunc)
    if caller and type(caller) == "table" then
        --动画列表:解决动画突然暂停,状态不正确的问题
        if caller.animNameOnlyList == nil then
            caller.animNameOnlyList = {}
        end
        --重置all动画
        local stopAllAnimFunc = function ()
            caller:StopAllAnimations()
            --K最后一帧
            for key, anim in pairs(caller.animNameOnlyList or {}) do
                if anim then
                    --将动画定格到第end帧
                    caller:PlayWidgetAnimAt(anim, anim:GetEndTime())
                end
            end
        end
        --动画播放
        if animName then
            if caller.animNameOnlyList[tostring(animName)] == nil then
                caller.animNameOnlyList[tostring(animName)] = animName
            end
            --是否暂停所有动画
            if isStopAllAnim then
                stopAllAnimFunc()
            else
                caller:StopAnimation(animName)
            end
            --是否回调
            if type(animFinishFunc) == "function" then
                caller.OnAnimFinished = animFinishFunc
            end
            --是否循环
            local num = 1
            if type(isLoop) == "boolean" and isLoop then
                num = 0
            end
            --是否k到第一帧
            local isFristFrame = false
            if type(isToStartFrame) == "boolean" then
                isFristFrame = isToStartFrame
            end
            --第几秒开始
            local time = 0
            if type(startTime) == "number" then
                time = startTime
            end
            --播放动画
            caller:PlayAnimation(animName, time, num, EUMGSequencePlayMode.Forward, 1, isFristFrame)
        else
            stopAllAnimFunc()
            caller.animNameOnlyList = nil
        end
    end
end


--获取道具类型的名称
GunsmithPartUnlockLogic.GetNameByItemId = function(itemStruct)
    if itemStruct == nil or itemStruct.id == nil then
        return false, ""
    end
    local isBool, typeName = Module.ItemDetail:IsCommercializeItem(itemStruct)
    if isBool then
        return isBool, typeName
    end
    local itemMainType = ItemHelperTool.GetMainTypeById(itemStruct.id)
    if itemMainType == EItemType.WeaponSkin then--武器皮肤
        return true, Module.Gunsmith.Config.Loc.GunsmithWeaponSkin
    elseif itemMainType == EItemType.VehicleSkin then--载具皮肤
        return true, Module.Gunsmith.Config.Loc.GunsmithVehicleSkin
    elseif itemMainType == EItemType.Vehicle then--载具
        return true, Module.Gunsmith.Config.Loc.GunsmithVehicle
    end
    return false, ""
end


function GunsmithPartUnlockLogic.OpenUnlockPathUI()
    Facade.UIManager:AsyncShowUI(UIName2ID.GunsmithUnlockPathUI)
end

-- function GunsmithPartUnlockLogic.GetUnlockPathItemUIDatas(dataContainer, locks, weaponID)
--     if dataContainer == nil then
--         return
--     end
--     dataContainer:Reset()

--     if locks == nil then
--         return
--     end

--     local levelLockParts = {}
--     local activityLockParts = {}
--     local armoryLockParts = {}
--     local unKnownUnlockParts = {}

--     local fJumpPart = function(itemID, itemUnlockRegistryItem, list)
--         local bIsValid = isvalid(itemUnlockRegistryItem)
--         local jumpID = itemUnlockRegistryItem.jumpID
--         bIsValid = bIsValid and jumpID ~= nil
--         if not bIsValid then
--             return list
--         end
--         if list == nil then
--             list = {}
--         end
--         if list[jumpID] == nil then
--             list[jumpID] = {}
--         end
--         table.insert(list[jumpID], itemID)
--         return list
--     end

--     for _, itemID in pairs(locks) do
--         local itemUnlockRegistryItem = Server.ItemUnlockPathServer:GetPreferredItemUnlockPathRegistryItem(EArmedForceMode.MP, itemID)
--         local bIsValid = isvalid(itemUnlockRegistryItem)
--         if bIsValid then
--             local source = itemUnlockRegistryItem.source or 0
--             if source == EItemUnlockSources.Activity then
--                 activityLockParts = fJumpPart(itemID, itemUnlockRegistryItem, activityLockParts)
--             elseif source == EItemUnlockSources.Armory then
--                 -- activityLockParts = fJumpPart(itemID, itemUnlockRegistryItem, armoryLockParts)
--                 table.insert(armoryLockParts, itemID)
--             end
--         else
--             local bIsInMPWeaponLevelPartsTable = WeaponAssemblyTool.IsInMPWeaponLevelPartsTable(weaponID, itemID)
--             if bIsInMPWeaponLevelPartsTable then
--                 table.insert(levelLockParts, itemID)
--             else
--                 table.insert(unKnownUnlockParts, itemID)
--             end
--         end
--     end

--     if not table.isempty(levelLockParts) then
--         local itemUIData = dataContainer:Add()
--         itemUIData:SetLockParts(levelLockParts)
--         itemUIData:SetMPUnlockMethod(EGunsmithPartMPUnlockMethod.Level)
--     end

--     if not table.isempty(activityLockParts) then
--         for jumpID, parts in pairs(activityLockParts) do
--             if not table.isempty(parts) then
--                 local itemUIData = dataContainer:Add()
--                 itemUIData:SetLockParts(parts)
--                 itemUIData:SetMPUnlockMethod(EGunsmithPartMPUnlockMethod.Activity)
--                 itemUIData:SetMPUnlockjumpID(jumpID)
--             end
--         end
--     end

--     -- if not table.isempty(armoryLockParts) then
--     --     for jumpID, parts in pairs(armoryLockParts) do
--     --         if not table.isempty(parts) then
--     --             local itemUIData = dataContainer:Add()
--     --             itemUIData:SetLockParts(parts)
--     --             itemUIData:SetMPUnlockMethod(EGunsmithPartMPUnlockMethod.Armory)
--     --             itemUIData:SetMPUnlockjumpID(jumpID)
--     --         end
--     --     end
--     -- end
--     if not table.isempty(armoryLockParts) then
--         local itemUIData = dataContainer:Add()
--         itemUIData:SetLockParts(armoryLockParts)
--         itemUIData:SetMPUnlockMethod(EGunsmithPartMPUnlockMethod.Armory)
--     end

--     if not table.isempty(unKnownUnlockParts) then
--         local itemUIData = dataContainer:Add()
--         itemUIData:SetLockParts(unKnownUnlockParts)
--         itemUIData:SetMPUnlockMethod(EGunsmithPartMPUnlockMethod.Unknown)
--     end
-- end

function GunsmithPartUnlockLogic.JumpArmoryModule()
    Module.Armory:OpenArmoryWindow()
end

function GunsmithPartUnlockLogic.GetUnlockPathItemUIDatas(dataContainer, weaponDescription)
    local bIsValid = isvalid(dataContainer) and isvalid(weaponDescription)
    if not bIsValid then
        return
    end
    dataContainer:Reset()
    local lockedSocketGUID2ItemIDs = GunsmithPartUnlockLogic.GetLockSocketGUID2IDsFromMPWeaponDescription(weaponDescription)

    local bIsEmpty = table.isempty(lockedSocketGUID2ItemIDs)
    if not bIsEmpty then
        for socketGUID, itemID in pairs(lockedSocketGUID2ItemIDs) do
            GunsmithPartUnlockLogic._InternalGetUnlockPathItemUIData(dataContainer, weaponDescription, itemID, socketGUID)
        end
    end
    dataContainer:SortFromSortID()
end

function GunsmithPartUnlockLogic.GetLockSocketGUID2IDsFromMPWeaponDescription(weaponDescription)
    local socketGUID2IDs = {}
    if isinvalid(weaponDescription) then
        return socketGUID2IDs
    end

    local parts = weaponDescription:GetAllParts()
    local totalCount = parts:Num()
    if totalCount < 1 then
        return socketGUID2IDs
    end

    local fIsValid = function(itemID)
        return itemID ~= nil and itemID ~= 0
    end

    local invalidSocketGUID = G_GUNSMITH.SOCKET_GUID_INVALID
    for i = 1, totalCount do
        local index = i - 1
        local node = parts:Get(index)
        local itemID = node.ItemId
        local socketGUID = node.SocketGUID
        local itemGUID = node.GUID
        local bIsValidPart = GunsmithPartUnlockLogic._IternalIsValidPart(itemID)
        local bIsValidSocketGUID = fIsValid(socketGUID) and socketGUID ~= invalidSocketGUID
        local bIsLocked = not fIsValid(itemGUID)
        if bIsValidPart and bIsValidSocketGUID and bIsLocked then
            socketGUID2IDs[socketGUID] = itemID
        end
    end
    return socketGUID2IDs
end

function GunsmithPartUnlockLogic._IternalIsValidPart(itemID)
    if itemID == nil or itemID == 0 then
        return false
    end

    local bVirutal = UAssembleWeaponDataLibrary.IsVirutalPartItem(itemID)
    if bVirutal then
        return false
    end

    local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
    local bIsReceiver = itemMainType == EItemType.Receiver
    if bIsReceiver then
        return false
    end

    local bIsAdapter = itemMainType == EItemType.Adapter
    if not bIsAdapter then
        return false
    end

    local bIsPendant = WeaponAssemblyTool.IsPendantPart(itemID)
    if bIsPendant then
        return false
    end

    return true
end

function GunsmithPartUnlockLogic._InternalGetUnlockPathItemUIData(dataContainer, weaponDescription, itemID, socketGUID)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
    local bIsReceiver = itemMainType == EItemType.Receiver
    local bIsPendant = WeaponAssemblyTool.IsPendantPart(itemID)
    if bIsReceiver or bIsPendant then
        return
    end
    local itemUIData = dataContainer:Add()

    itemUIData:SetID(itemID)
    local unlockMethod, jumpID = GunsmithPartUnlockLogic._InternalGetMPUnlockMethod(weaponDescription, itemID)

    local bSelect = unlockMethod == EGunsmithPartMPUnlockMethod.Inventory
    itemUIData:SetSelect(bSelect)

    local bCanSelect = bSelect
    itemUIData:SetCanSelect(bCanSelect)

    itemUIData:SetMPUnlockMethod(unlockMethod)
    itemUIData:SetMPUnlockjumpID(jumpID)
    local bIsFinetune = GPModularWeaponDescLogic.IsFinetune(weaponDescription, socketGUID)
    itemUIData:SetIsFinetune(bIsFinetune)
end

function GunsmithPartUnlockLogic._InternalGetMPUnlockMethod(weaponDescription, itemID, socketGUID)
    local IsUnlockInventoryForMP = GunsmithPartUnlockLogic._InternalIsUnlockInventoryForMP(weaponDescription, itemID)
    if IsUnlockInventoryForMP then
        return EGunsmithPartMPUnlockMethod.Inventory, 0
    end
    local unlockMethod = EGunsmithPartMPUnlockMethod.Unknown
    local jumpID = 0
    local itemUnlockRegistryItem = Server.ItemUnlockPathServer:GetPreferredItemUnlockPathRegistryItem(EArmedForceMode.MP, itemID)
    local bIsValid = isvalid(itemUnlockRegistryItem)
    if bIsValid then
        local source = itemUnlockRegistryItem.source or 0
        jumpID = itemUnlockRegistryItem.jumpID or 0
        if source == EItemUnlockSources.Activity then
            unlockMethod = EGunsmithPartMPUnlockMethod.Activity
        elseif source == EItemUnlockSources.Armory then
            unlockMethod = EGunsmithPartMPUnlockMethod.Armory
        end
    else
        local weaponItemID = GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription, 0)
        local bIsInMPWeaponLevelPartsTable = WeaponAssemblyTool.IsInMPWeaponLevelPartsTable(weaponItemID, itemID)
        if bIsInMPWeaponLevelPartsTable then
            unlockMethod = EGunsmithPartMPUnlockMethod.Level
        end
    end
    return unlockMethod, jumpID
end

function GunsmithPartUnlockLogic._InternalIsUnlockInventoryForMP(weaponDescription, itemID)
    local itemGUID = GunsmithPartUnlockLogic._InternalGetItemGUIDForMPInventory(weaponDescription, itemID)
    return itemGUID ~= 0
end

function GunsmithPartUnlockLogic._InternalGetItemGUIDForMPInventory(weaponDescription, itemID)
    local bIsValid = isvalid(weaponDescription)
    if not bIsValid then
        return 0
    end
    local weaponItemID = GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription, 0)
    local skinID = GPModularWeaponDescLogic.GetSkinID(weaponDescription)
    local groupID = GunsmithUIContextLogic.GetGroupID()
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP(groupID)
    local bContained, itemBase = GunsmithUIContextLogic.ContainsFromInventory(itemID, groupID, 1, bIsMP, weaponItemID, skinID)
    if not bContained or itemBase == nil then
        return 0
    end
    local itemGUID = itemBase.gid or 0
    return itemGUID
end

function GunsmithPartUnlockLogic.SyncPart2MPServerBySelectedItem(dataContainer)
    local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    local selectedIDs = GunsmithPartUnlockLogic.GetSelectedUnlockIDs(dataContainer, frontend)
    -- local bIsEmpty = table.isempty(selectedIDs)
    -- if bIsEmpty then
    --     Module.CommonTips:ShowSimpleTip(Module.Gunsmith.Config.Loc.GunsmithUnlockPathUIEmptyTips)
    --     return
    -- end
    local unlockPartsInfo, lockPartsInfo = GunsmithPartUnlockLogic._InternalGetIDsFromMPWeapondescription(frontend, selectedIDs)

    if not table.isempty(unlockPartsInfo) then
        GunsmithUIContextLogic._InternalLinkPartNodeGUIDBySocketGUIDTable(frontend, unlockPartsInfo)
    end

    GunsmithUIContextLogic.SetWeaponDescription4SyncFromServer()
    local sync = GunsmithUIContextLogic.GetWeaponDescription4Sync()
    GPModularWeaponDescLogic.BuildFromModularWeaponDesc(sync, frontend)

    if not table.isempty(lockPartsInfo) then
        GunsmithUIContextLogic._InternalRemovePartNodeBySocketGUIDTable(sync, lockPartsInfo)
    end

    local propinfo4Sync = WeaponAssemblyTool.Desc_To_PropInfo(sync)
    local propinfo4Frontend = WeaponAssemblyTool.Desc_To_PropInfo(frontend)
    GunsmithUIContextLogic.CSWAssemblyDepositPropUpdateReqByContextProcessor(propinfo4Sync, nil, propinfo4Frontend)
end

function GunsmithPartUnlockLogic._InternalGetIDsFromMPWeapondescription(weaponDescription, selectedIDs)
    local unlockedSocketGUID2GUIDs = {}
    local lockedSocketGUID2ItemIDs = {}
    local bIsNotValid = isinvalid(weaponDescription) or table.isempty(selectedIDs)
    if bIsNotValid then
        return unlockedSocketGUID2GUIDs, lockedSocketGUID2ItemIDs
    end

    local parts = weaponDescription:GetAllParts()
    local totalCount = parts:Num()
    if totalCount < 1 then
        return unlockedSocketGUID2GUIDs, lockedSocketGUID2ItemIDs
    end

    local fIsValid = function(itemID)
        return itemID ~= nil and itemID ~= 0
    end

    local fFilterWeaponPartNode = function(node)
        local bIsValid = isvalid(node)
        if not bIsValid then
            return
        end
        local itemID = node.ItemId
        local bIsValidPart = GunsmithPartUnlockLogic._IternalIsValidPart(itemID)
        if not bIsValidPart then
            return
        end
        local socketGUID = node.SocketGUID
        local bIsValidSocketGUID = fIsValid(socketGUID) and socketGUID ~= invalidSocketGUID
        if not bIsValidSocketGUID then
            return
        end
        local itemGUID = node.GUID
        local bIsUnlocked = fIsValid(itemGUID)
        if bIsUnlocked then
            unlockedSocketGUID2GUIDs[socketGUID] = itemGUID
            return
        end
        local bIsSelected = selectedIDs[itemID]
        if bIsSelected then
            unlockedSocketGUID2GUIDs[socketGUID] = GunsmithPartUnlockLogic._InternalGetItemGUIDForMPInventory(weaponDescription, itemID)
        else
            lockedSocketGUID2ItemIDs[socketGUID] = itemID
        end
    end

    local invalidSocketGUID = G_GUNSMITH.SOCKET_GUID_INVALID
    for i = 1, totalCount do
        local index = i - 1
        local node = parts:Get(index)
        fFilterWeaponPartNode(node)
    end
    return unlockedSocketGUID2GUIDs, lockedSocketGUID2ItemIDs
end

function GunsmithPartUnlockLogic.GetSelectedUnlockIDs(dataContainer, weaponDescription)
    local selectedIDs = {}
    local bIsValid = isvalid(dataContainer) and isvalid(weaponDescription)
    if not bIsValid then
        return selectedIDs
    end
    local fFilterItemData = function(data)
        if isinvalid(data) then
            return
        end
        local bSelect = data:GetSelect()
        if not bSelect then
            return
        end
        local itemID = data:GetID()
        local IsUnlockInventoryForMP = GunsmithPartUnlockLogic._InternalIsUnlockInventoryForMP(weaponDescription, itemID)
        if not IsUnlockInventoryForMP then
            return
        end
        if selectedIDs[itemID] then
            return
        end
        selectedIDs[itemID] = true
    end

    local count = dataContainer:GetCount()
    for i = 1, count do
        local data = dataContainer:Get(i)
        fFilterItemData(data)
    end
    return selectedIDs
end

--海外资源下载(是否需要下载)
function GunsmithPartUnlockLogic.OverseasResourceDownload(resourceName)
    if resourceName == nil then
        return false
    end
    --判断海外是否需要下载
    if IsBuildRegionGlobal() then
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(resourceName)
        if not bDownloaded then
            local fConfirm = function ()
                Module.ExpansionPackCoordinator:DownloadByModuleName(resourceName)
            end
            local fCancel = function ()
            end
            local mainTitle = StringUtil.SequentialFormat(Module.LitePackage.Config.Loc.ModeResourceDownloadTip, Module.IrisSafeHouse.Config.Loc.IrisModeTitle)
            Module.CommonTips:ShowConfirmWindow(mainTitle or "", fConfirm, fCancel)
        end
        return not bDownloaded
    else
        return false
    end
end

--预加载成长之路部分资源
function GunsmithPartUnlockLogic.PreloadingImages()
    --test是否局内加载
    logerror("ssy is in game loading GunsmithPartUnlockLogic get GetSOLData ... ... ")
    --局内到局外(预加载资源)
    for key, items in pairs(Server.GrowthRoadServer:GetSOLData() or {}) do
        for index, item in ipairs(items or {}) do
            if item.iconType == 2 and item.itemData then
                RuntimeIconTool.PreLoadItemIcon(item.itemData)
            elseif item.iconType == 1 and item.iconPath then
                ResImageUtil.StaticAsyncLoadImgObjByPath(item.iconPath, false)
            end
        end
    end
    local path = "PaperSprite'/Game/UI/UIAtlas/System/PathofGrowth/BakedSprite/PatnofGrowth_De_38.PatnofGrowth_De_38'"
    ResImageUtil.StaticAsyncLoadImgObjByPath(path, false)
end

return GunsmithPartUnlockLogic