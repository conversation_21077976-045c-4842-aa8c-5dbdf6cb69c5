----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMComparePrice)
----- LOG FUNCTION AUTO GENERATE END -----------



local ECheckBoxState = import "ECheckBoxState"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local CommonFluctuationPopItem = require "DFM.Business.Module.ComparePriceModule.UI.CommonFluctuationPopItem"
local CurrencyHelperTool       = require "DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
local ECheckBoxState = import "ECheckBoxState"

local ITEM_NAME_MAX = 15

---@class AssemblyPlanConfirmPopItem : CommonFluctuationPopItem
local AssemblyPlanConfirmPopItem = ui("AssemblyPlanConfirmPopItem", CommonFluctuationPopItem)


function AssemblyPlanConfirmPopItem:InitData(index, data)
    self._index = index
    self._itemUIdata = data
    if self._itemUIdata == nil then
        self:_InternalUpdateDefault()
        return
    end
    self:_InternalUpdateTitle(true)
    self:_InternalUpdateName()
    self:_InternalUpdateIcon()
    self:_InternalUpdateSource()
end

-- 设置道具名
function AssemblyPlanConfirmPopItem:_InternalUpdateName()
    local item = self._itemUIdata:GetItemBase()
    local nameStr = item.name
    local itemNum = self._itemUIdata:GetNum()
    local singlePrice = self._itemUIdata:GetSinglePrice()
    local totalPrice = self._itemUIdata:GetTotalPrice()
    local currencyClientType = self._itemUIdata:GetCurrencyClientType()

    local buyChannel = self._itemUIdata:GetCheapBuyChannel()
    if buyChannel == self._ComparePriceConfig.AssemblyChannelType.Inventory then
        if self._itemUIdata:IsShowBigItemView() then    -- 武器装备都是用大框
            self:SetType(2)
        else
            self:SetType(0)
        end
        self._wtSinglePrice:Collapsed()
        if itemNum == 1 then
            self._wtItemName:SetText(nameStr)
        else
            local param = {
                ["nameStr"] = nameStr,
                ["itemNum"] = itemNum
            }
            nameStr = StringUtil.Key2StrFormat(self._ComparePriceConfig.Loc.CommonFluctuationPopItemItemNameText, param)
            self._wtItemName:SetText(nameStr)
        end

    else
        self._wtSinglePrice:SelfHitTestInvisible()
        if itemNum == 1 then
            self._wtItemName:SetText(nameStr)
            if totalPrice > 0 then
                local currencyIconTxt = ECurrencyClientType2RichIconTxtV2[currencyClientType]
                local singlePriceStr = CurrencyHelperTool.GetCurrencyNumFormatStr(singlePrice, CurrencyHelperTool.EKMThousandsType.None)
                local singleText = ""
                local param = {
                    ["currencyIconTxt"] = currencyIconTxt,
                    ["PriceStr"] = singlePriceStr
                }
                singleText = StringUtil.Key2StrFormat(self._ComparePriceConfig.Loc.SinglePriceTextWithoutFlo, param)
                self._wtSinglePrice:SetText(singleText)
            else
                self._wtSinglePrice:Collapsed()
            end

            if self._itemUIdata:IsShowBigItemView() then    -- 武器装备都是用大框
                self:SetType(2)
            else
                self:SetType(0)
            end
        else
            local param = {
                ["nameStr"] = nameStr,
                ["itemNum"] = itemNum
            }
            nameStr = StringUtil.Key2StrFormat(self._ComparePriceConfig.Loc.CommonFluctuationPopItemItemNameText, param)
            self._wtItemName:SetText(nameStr)
            if totalPrice > 0 then
                self:SetType(1)
                local currencyIconTxt = ECurrencyClientType2RichIconTxtV2[currencyClientType]
                local singlePriceStr = CurrencyHelperTool.GetCurrencyNumFormatStr(singlePrice, CurrencyHelperTool.EKMThousandsType.None)
                local singleText = ""
                local param1 = {
                    ["currencyIconTxt"] = currencyIconTxt,
                    ["PriceStr"] = singlePriceStr
                }
                singleText = StringUtil.Key2StrFormat(self._ComparePriceConfig.Loc.SinglePriceTextWithoutFlo, param1)
                self._wtSinglePrice:SetText(singleText)
                local param2 = {
                    ["currencyIconTxt"] = currencyIconTxt,
                    ["priceStr"] = CurrencyHelperTool.GetCurrencyNumFormatStr(totalPrice, CurrencyHelperTool.EKMThousandsType.None)
                }
                self._wtTotlePrice:SetText(StringUtil.Key2StrFormat("{currencyIconTxt}{priceStr}", param2))
            else
                self._wtSinglePrice:Collapsed()
            end
        end
    end
end

-- 武器的itemview显示标准预设
function AssemblyPlanConfirmPopItem:_InternalUpdateIcon()
	Facade.UIManager:RemoveSubUIByParent(self, self._wtItemViewRoot)
    local item = self._itemUIdata:GetItemBase()
    local weakUIIns, instanceId = Module.CommonWidget:CreateIVCommonItemTemplate(self, self._wtItemViewRoot, nil, nil)
    local itemView = getfromweak(weakUIIns)
    -- local slotItemView = itemView.WBP_SlotItemView

    local sortType = self._itemUIdata:GetSortType()
    if not self._itemUIdata:IsShowBigItemView() then
        if self.NormalItemSize then itemView:SetRootSize(self.NormalItemSize.X, self.NormalItemSize.Y) end
    else
        if self.WeaponItemSize then itemView:SetRootSize(self.WeaponItemSize.X, self.WeaponItemSize.Y) end
    end

    -- 武器的itemview显示标准预设
    if sortType == self._ComparePriceConfig.SortType.Weapon then
        item = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(self._itemUIdata:GetID())
    end
    itemView:InitItem(item)
    itemView:SetCppValue("bIsFocusable", self._bItemViewFocusable)
end



-- 设置来源
function AssemblyPlanConfirmPopItem:_InternalUpdateSource() 
    local buyChannel = self._itemUIdata:GetCheapBuyChannel()
    local sourceTypeStr = self._ComparePriceConfig.Loc.SourceAssemblyTypeStr[buyChannel]
    local sourceStr = ""
    
    if buyChannel == self._ComparePriceConfig.AssemblyChannelType.Invalid then
        sourceStr = sourceTypeStr
    else
        sourceStr = string.format(Module.CommonWidget.Config.Loc.SourceStr, sourceTypeStr)
    end
    self._wtSource:SetText(sourceStr)
    self._wtDFCommonCheckBoxWithText:HitTestInvisible()
    self._wtDFCommonCheckBoxWithText:SetCheckedState(ECheckBoxState.Undetermined) 
end

return AssemblyPlanConfirmPopItem