----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSMail)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class MailServer : ServerBase
local MailServer = class("MailServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local SystemMailTable = Facade.TableManager:GetTable("SystemMail")

function MailServer:Ctor()
    self.Events = {
       evtMailOperation = LuaEvent:NewIns("MailServer.evtMailOperation"),               ---MailOption,MailList
       evtReceiveNewMail = LuaEvent:NewIns("MailServer.evtReceiveNewMail"),
       evtDeleteAllMail = LuaEvent:NewIns("MailServer.evtDeleteAllMail"),

       evtMailRedDotRefresh = LuaEvent:NewIns("MailServer.evtMailRedDotRefresh"),       --刷新主界面邮件页签上的红点，同步服务器
       evtReceiveBtnStateChange = LuaEvent:NewIns("evtReceiveBtnStateChange"),          --刷新已经领取按钮的状态

       evtLocalNewMailNumRefresh = LuaEvent:NewIns("evtLocalNewMailNumRefresh"),        --本地刷新邮件页签上的红点，不同步服务器

       evtFetchMailTipsDuration = LuaEvent:NewIns("evtFetchMailTipsDuration"), 

       evtReceiveFailed = LuaEvent:NewIns("evtReceiveFailed"), 
       evtEnableRewardNTFCall = LuaEvent:NewIns("evtEnableRewardNTFCall"),
    }
    self._allMails = {
        [MailType.SystemMail] = {},
        [MailType.NoticeMail] = {},
        [MailType.DealMail] = {},
        [MailType.SecurityMail] = {},
    }
    self._allUnReceiveMails = {
        [MailType.SystemMail] = {},
        [MailType.NoticeMail] = {},
        [MailType.DealMail] = {},
        [MailType.SecurityMail] = {},

    }
    self._newMailNum = {
        [MailType.SystemMail] = 0,
        [MailType.NoticeMail] = 0,
        [MailType.DealMail] = 0,
        [MailType.SecurityMail] = 0,
    }
    self._sortMail = {
        [MailType.SystemMail] = {},
        [MailType.NoticeMail] = {},
        [MailType.DealMail] = {},
        [MailType.SecurityMail] = {},
    }
    self._mailNum = {
        [MailType.SystemMail] = {curNum = 0,maxNum = 100},
        [MailType.NoticeMail] = {curNum = 0,maxNum = 100},
        [MailType.DealMail] = {curNum = 0,maxNum = 100},
        [MailType.SecurityMail] = {curNum = 0,maxNum = 100},
    }
    self._mailBtnState = {                   --一键操作按钮的状态
        [MailType.SystemMail] = {receiveAll = 0,deleteAll = 0,readAll = 0},
        [MailType.NoticeMail] = {receiveAll = 0,deleteAll = 0,readAll = 0},
        [MailType.DealMail] = {receiveAll = 0,deleteAll = 0,readAll = 0},
        [MailType.SecurityMail] = {receiveAll = 0,deleteAll = 0,readAll = 0},
    }
    self._msgID = 1
    self._msgMaxNum = 30

    self._bNeedSaveMsgCenter = false --关闭邮件界面时，是否需要存消息中心到server

    self._playerId2MsgId = {}       --刷新玩家状态用
end

function MailServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSMailNewMailNtf",self._OnCSMailNewMailNtf, self)
end

function MailServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
end

---处理服务器发来的邮件信息，对其中存在的本地化信息做替换
---@param mailInfo pb_CommonMail
---@return pb_CommonMail
function MailServer:HandleLocalizeText(mailInfo)
    if mailInfo then
        mailInfo.Title = LocalizeTool.GetTransStr(mailInfo.Title)
        mailInfo.Sender = LocalizeTool.GetTransStr(mailInfo.Sender)
        mailInfo.Content = LocalizeTool.GetTransStr(mailInfo.Content)
        mailInfo.SendTimestamp = mailInfo.SendTimestamp
    end
    return mailInfo
end

--从服务器拉取当前页面邮件
function MailServer:FetchAllMails(mailType, beginIdx,fCallBack)
    local onCSMailGetListRes = function(res)
        if not res or res.result ~= 0 then
            logerror("拉取邮件失败")
            logtable(res)
            return
        end
        if beginIdx == 1 then
            self._allMails[mailType] = {}
            self._sortMail[mailType] = {}
            self._mailBtnState[mailType].receiveAll = 0
            self._mailBtnState[mailType].readAll = 0
            self._mailBtnState[mailType].deleteAll = 0
            self._HasIncorrectMailModel = false -- 是否有模板错误的邮件（若模板错误，则弹出提示）
        end
        local mailInfos = res.MailList
        for _,mailInfo in pairs(mailInfos) do
            if self:IsCorrectMailModel(mailInfo) then --检查邮件模板是否正确
                mailInfo = self:HandleLocalizeText(mailInfo)
                self._allMails[mailType][mailInfo.MailID] = mailInfo --这个表的邮件排序和MailInfos是不一样的
                local state = {
                              readState       = (1 & mailInfo.State) ~= 0,
                              attachmentState = (2 & mailInfo.State) ~= 0,
                              receiveState    = (4 & mailInfo.State) ~= 0,
                            }
                self._allMails[mailType][mailInfo.MailID].state = state
                self._allMails[mailType][mailInfo.MailID].expirationTime = mailInfo.ExpirationTime
                table.insert(self._sortMail[mailType],mailInfo.MailID)  --保留了MailInfos的排序
                if state.attachmentState and not state.receiveState then
                    self._mailBtnState[mailType].receiveAll = self._mailBtnState[mailType].receiveAll + 1
                end
                if not state.attachmentState or state.receiveState then
                    self._mailBtnState[mailType].readAll = self._mailBtnState[mailType].readAll + 1
                end
                if state.readState and (not state.attachmentState or state.receiveState) then
                    self._mailBtnState[mailType].deleteAll = self._mailBtnState[mailType].deleteAll + 1
                end
            end
        end
        if res.BeginIndex ~= 0 then
            self:FetchAllMails(mailType,res.BeginIndex,fCallBack)
        else
            self._mailNum[mailType].curNum = res.CurMailNum
            self._mailNum[mailType].maxNum = res.MaxMailNum
            self._sortMail[mailType] = self:DeduplicateSortMails(self._sortMail[mailType])
            fCallBack()
        end
    end

    local req = pb.CSMailGetListReq:New()
    req.Type =  mailType
    req.BeginIndex = beginIdx
    req:Request(onCSMailGetListRes,{bNeedResendAfterReconnected = true, bEnableHighFrequency = true})
end

function MailServer:DeduplicateSortMails(sortMails) --对sortmail数组去重，防止重复显示同一封邮件
    local result = {}
    local mailTags = {} --用于标记已存在的邮件
    for _ , mailID in ipairs(sortMails) do
        if not mailTags[mailID] then
            mailTags[mailID] = true
            table.insert(result, mailID)
        end
    end
    return result
end


function MailServer:IsCorrectMailModel(mailInfo)--检查邮件模板是否正确
    local mailcfgID = mailInfo.systemmail_cfg_id
    if mailcfgID then
        if mailcfgID == 0 then
            return true
        end
        for _, row in pairs(SystemMailTable) do
            if row.MailID == mailcfgID then
                return true
            end
        end
    end
    self._HasIncorrectMailModel = true
        return false
end



--从服务器拉取好友送礼邮件
function MailServer:FetchFriendGift(fCallBack)
    local onFriendGetDailyGiftListRes = function(res)
        self._allMails[MailType.FriendMail] = {}
        self._sortMail[MailType.FriendMail] = {}
        if not res or res.result ~= 0 then
            logerror("拉取好友邮件失败")
            logtable(res)
            return
        end
        for _,friendDailyGift in pairs(res.gift_list) do
            local mailID = friendDailyGift.id
            self._allMails[MailType.FriendMail][mailID] = friendDailyGift
            self._allMails[MailType.FriendMail][mailID].readState = (1 & friendDailyGift.state) ~= 0
            table.insert(self._sortMail[MailType.FriendMail],mailID)
        end
        self:_SortFriendGiftList()
        self._mailNum[MailType.FriendMail].curNum = #res.gift_list
        self._mailNum[MailType.FriendMail].maxNum = 100
        fCallBack()
    end

    local req = pb.CSFriendGetDailyGiftListReq:New()
    req:Request(onFriendGetDailyGiftListRes)
end

--领取好友邮件 全部/一封
function MailServer:ReceiveFriendMail(fCallBack,bSingle,mailID)
    local onCSFriendRecvDailyGiftRes = function(res)
        if res and not table.isempty(res.recv_id) then
            local n = 0
            local giftList = {}
            for _,mailID in pairs(res.recv_id) do
                local gift = {
                    mailID = mailID,
                    itemID = self._allMails[MailType.FriendMail][mailID].gift_id,
                    num = self._allMails[MailType.FriendMail][mailID].num
                }
                table.insert(giftList,gift)
                self._allMails[MailType.FriendMail][mailID] = nil
                n = n+1
            end
            self._mailNum[MailType.FriendMail].curNum = math.max(0,self._mailNum[MailType.FriendMail].curNum - n)
            self:_FixSortMial(MailType.FriendMail)
            fCallBack(giftList)
        end
    end
    local recvID = {}
    if bSingle then
        recvID = {mailID}
    else
        recvID = self:_BuildFriendMailRecvList()
    end
    local req = pb.CSFriendRecvDailyGiftReq:New()
    req.recv_id = recvID
    req:Request(onCSFriendRecvDailyGiftRes)
end

--领取好友邮件的排序规则不同于显示的排序规则，单独处理
function MailServer:_BuildFriendMailRecvList()
    local recvList = {}
    for _,mailID in pairs(self._sortMail[MailType.FriendMail]) do
        table.insert(recvList, mailID)
    end
    local function fSort(A,B)
        local mailA = self._allMails[MailType.FriendMail][A]  --FriendDailyGift
        local mailB = self._allMails[MailType.FriendMail][B]
        if Server.FriendServer:GetFriendFav(mailA.player_info.player_id) == Server.FriendServer:GetFriendFav(mailB.player_info.player_id) then
            return mailA.send_time < mailB.send_time
        else
            return Server.FriendServer:GetFriendFav(mailA.player_info.player_id) > Server.FriendServer:GetFriendFav(mailB.player_info.player_id)
        end
    end
    table.sort(recvList, fSort)
    return recvList
end

--好友邮件排序
function MailServer:_SortFriendGiftList()
    local function fSort(A,B)
        local mailA = self._allMails[MailType.FriendMail][A]
        local mailB = self._allMails[MailType.FriendMail][B]
        if mailA.bNew ~= mailB.bNew then
            return mailA.bNew
        elseif Server.FriendServer:GetFriendFav(mailA.player_info.player_id) ~= Server.FriendServer:GetFriendFav(mailB.player_info.player_id) then
            return Server.FriendServer:GetFriendFav(mailA.player_info.player_id) > Server.FriendServer:GetFriendFav(mailB.player_info.player_id)
        else
            return mailA.send_time < mailB.send_time
        end
    end
    table.sort(self._sortMail[MailType.FriendMail],fSort)

end

--获取当前页面邮件数量
function MailServer:GetMailNum(mailType)
    return self._mailNum[mailType]
end

function MailServer:GetMailBtnState(mailType,buttonType)
    return self._mailBtnState[mailType][buttonType] ~= 0
end

function MailServer:GetAllMails(mailType)
    return self._allMails[mailType]
end

function MailServer:GetSortMail(mailType)
    return self._sortMail[mailType]
end

function MailServer:MailModelIsIncorrect()
    return self._HasIncorrectMailModel
end

function MailServer:GetNewMailNum(mailType)
    return self._newMailNum[mailType]
end

--打开邮件界面时，需要打开有新邮件的界面，都有时按下面的优先级
function MailServer:GetNewMailType()
    if  self._newMailNum[MailType.SystemMail] ~= 0 then
        return MailType.SystemMail
    elseif self._newMailNum[MailType.NoticeMail] ~= 0 then
        return MailType.NoticeMail
    elseif self._newMailNum[MailType.DealMail] ~= 0 then
        return MailType.DealMail
    elseif self._newMailNum[MailType.SecurityMail] ~= 0 then
        return MailType.SecurityMail
    elseif self._newMailNum[MailType.GiftMail] ~= 0 then
        return MailType.GiftMail
    end

    return MailType.NoticeMail
end

--获取邮件保存时间
function MailServer:GetMailExpirationTime(mailType,mailID)
    if self._allMails[mailType][mailID].expirationTime then
        return self._allMails[mailType][mailID].expirationTime
    end
    return 0
end


--添加未处理的队友邀请通知到消息中心
function MailServer:AddMsgInfo(inviteInfo)  --message ClientSaveInviteInfo
    if #self._sortMail[MailType.MsgCenterMail] == self._msgMaxNum then
        local lastID = self._sortMail[MailType.MsgCenterMail][#self._sortMail[MailType.MsgCenterMail]]
        table.remove(self._sortMail[MailType.MsgCenterMail], self._msgMaxNum)
        self._allMails[MailType.MsgCenterMail][lastID] = nil
        local deleteId = nil
        for playerId,msgId in pairs(self._playerId2MsgId) do
            if msgId == lastID then
                deleteId = playerId
            end
        end
        if deleteId then
            self._playerId2MsgId[deleteId] = nil
        end
    end

    self._allMails[MailType.MsgCenterMail][self._msgID] = {}
    self._allMails[MailType.MsgCenterMail][self._msgID].inviteInfo = inviteInfo
    self._allMails[MailType.MsgCenterMail][self._msgID].msgID = self._msgID
    local playerId = inviteInfo.inviter.player_id
    self._playerId2MsgId[playerId] = self._msgID

    table.insert(self._sortMail[MailType.MsgCenterMail], 1, self._msgID)
    self._msgID = self._msgID + 1
    self._mailNum[MailType.MsgCenterMail].curNum = #self._sortMail[MailType.MsgCenterMail]
    local newMsgNum = self._newMailNum[MailType.MsgCenterMail] + 1
    self._newMailNum[MailType.MsgCenterMail] = math.min(newMsgNum, 30)
    self.Events.evtReceiveNewMail:Invoke()    --刷新大厅邮件提示

    self._bNeedSaveMsgCenter = true
    self:SaveMsgCenterToServer()
end

function MailServer:RemoveMsgByID(msgID)
    for i,ID in pairs(self._sortMail[MailType.MsgCenterMail]) do
        if ID == msgID then
            table.remove(self._sortMail[MailType.MsgCenterMail], i)
            self._allMails[MailType.MsgCenterMail][msgID] = nil
            self._mailNum[MailType.MsgCenterMail].curNum = math.max(0,self._mailNum[MailType.MsgCenterMail].curNum - 1)
            self._bNeedSaveMsgCenter = true
            break
        end
    end
    self.Events.evtMailRedDotRefresh:Invoke()
end

function MailServer:ClearMsg()
    self._allMails[MailType.MsgCenterMail] = {}
    self._sortMail[MailType.MsgCenterMail] = {}
    self._mailNum[MailType.MsgCenterMail].curNum = 0
    self._newMailNum[MailType.MsgCenterMail] = 0
    self._msgID = 1
    self._bNeedSaveMsgCenter = true

    self.Events.evtMailRedDotRefresh:Invoke()
end

--改动后的消息列表存服务器
function MailServer:SaveMsgCenterToServer()
    if not self._bNeedSaveMsgCenter then
        return
    else
        self._bNeedSaveMsgCenter = false
    end

    local inviteList = {}
    for _,msginfo in pairs(self._allMails[MailType.MsgCenterMail]) do
        if not msginfo.inviteInfo.GM then
            table.insert(inviteList, msginfo.inviteInfo)
        end
    end

    local req = pb.CSTeamSaveInviteListReq:New()
    req.invite_list = inviteList
    req:Request()
end

--刷新消息中心小红点，并重新排序
function MailServer:ReSortMsg()
    local f = function(a,b)
        local inviteInfoA = self._allMails[MailType.MsgCenterMail][a].inviteInfo
        local inviteInfoB = self._allMails[MailType.MsgCenterMail][b].inviteInfo
        if inviteInfoA.state & 1 == 0 and inviteInfoB.state & 1 ~= 0 then
            return false
        end
        if inviteInfoA.state & 1 ~= 0 and inviteInfoB.state & 1 == 0 then
            return true
        end
        if inviteInfoA.flag == inviteInfoB.flag then  --flag 1为新，0为非新
            return inviteInfoA.invite_time > inviteInfoB.invite_time
        else
            return inviteInfoA.flag > inviteInfoB.flag
        end
    end
    table.sort(self._sortMail[MailType.MsgCenterMail], f)
end

function MailServer:ReleaseMsgNew(msgID)
    if self._allMails[MailType.MsgCenterMail][msgID] then
        self._allMails[MailType.MsgCenterMail][msgID].inviteInfo.flag = 0
        self._bNeedSaveMsgCenter = true
    end
end

function MailServer:ReadFriendMail(mailID)
    local onCSMailOptionRes = function(res)
        if res and res.result == 0 then
            if self._allMails[MailType.FriendMail][mailID] then
                self._allMails[MailType.FriendMail][mailID].readState = true
            end
            self:NewMailNumRefresh(MailType.FriendMail,1)
        end
    end

    --local mailInfo = {}
    --mailInfo.mail_id = mailID
    --local mail_infos = {mailInfo}

    local req = pb.CSMailReadReq:New()
    req.type =  MailType.FriendMail
    --req.Option = MailOption.ReadMail
    --req.mail_infos = mail_infos
    req.mail_id=mailID
    req:Request(onCSMailOptionRes)
end

--做批量操作之后更新数据
function MailServer:_RefreshData(mailType,option, mailList)
    if option == MailOption.DeleteAll  then
        ---数据变化
        for _,mailUpdateInfo in pairs(mailList) do
            local mailID = mailUpdateInfo.MailID
            self._allMails[mailType][mailID] = nil
        end
        self:_FixSortMial(mailType)
        self._mailNum[mailType].curNum = math.max(0,self._mailNum[mailType].curNum - #mailList)
        ---UI表现
        self.Events.evtMailOperation:Invoke(MailOption.DeleteAll,mailList)

    elseif option == MailOption.ReadAll then
        ---数据变化
        for _,mailUpdateInfo in pairs(mailList) do
            local mailID = mailUpdateInfo.MailID
            if self._allMails[mailType][mailID] then
                self._allMails[mailType][mailID].state.readState = true
                if mailUpdateInfo.ExpirationTime then
                    self._allMails[mailType][mailID].expirationTime = mailUpdateInfo.ExpirationTime
                end
            end
        end
        ---UI表现
        self.Events.evtMailOperation:Invoke(MailOption.ReadAll,mailList)
    elseif option == MailOption.ReceiveAll then
        for _,mailUpdateInfo in pairs(mailList) do
            local mailID = mailUpdateInfo.MailID
            if self._allMails[mailType][mailID] then
                self._allMails[mailType][mailID].state.receiveState = true
                self._allMails[mailType][mailID].state.readState = true
                if mailUpdateInfo.ExpirationTime then
                    self._allMails[mailType][mailID].expirationTime = mailUpdateInfo.ExpirationTime
                end
            end
        end
        self.Events.evtMailOperation:Invoke(MailOption.ReceiveAll,mailList)
        self._mailBtnState[mailType].receiveAll = math.max(0,self._mailBtnState[mailType].receiveAll - #mailList)
    end

    ---数据更新后刷新小红点(一键删除，不需要更新红点，因为可以被删除的邮件都是没有红点的)
    if option ~= MailOption.DeleteAll then
        self.Events.evtMailRedDotRefresh:Invoke()
    end
    ---数据更新后刷新一键操作按键状态
    self.Events.evtReceiveBtnStateChange:Invoke()
end

--从self.SortMail[mailType]删除邮件，并使key连续
function MailServer:_FixSortMial(mailType)
    local length = #self._sortMail[mailType]
    local deleteKeys = {}
    for key =length,1,-1 do
        local MailID = self._sortMail[mailType][key]
        if not self._allMails[mailType][MailID] then
            table.insert(deleteKeys, key)
        end
    end
    for _,key in ipairs(deleteKeys) do
        table.remove(self._sortMail[mailType], key)
    end
end

--判断邮件列表是否为空
function MailServer:IsMailListEmpty(mailType)
    local empty = false
    if table.isempty(self._allMails[mailType]) then
        empty = true
    end
    return empty
end

function MailServer:GetMailInfo(mailType, mailID)
    return self._allMails[mailType][mailID]
end

function MailServer:GetAllUnReceiveMail(mailType) --获取所有可领取邮件
    self._allUnReceiveMails[mailType] = {}
    for index = 1, #self._sortMail[mailType] do
        local mail = self._allMails[mailType][self._sortMail[mailType][index]]
        if mail.state.receiveState == false  then
            table.insert(self._allUnReceiveMails[mailType],mail)
        end
    end
    return self._allUnReceiveMails[mailType]
end

----------------------------------------------------------------
---新邮件数目相关函数
----------------------------------------------------------------

--- 登录到大厅的时候会调用
function MailServer:FetchServerData()
    loginfo("horis MailServer:FetchServerData()")
    self:FetchMailTips()
end

--拉取邮件红点数量,仅 登录/一键已读/关闭邮件界面 的时候拉新
function MailServer:FetchMailTips()
    local onMailGetBaseInfoRes = function(res)
        if not res or res.result ~= 0 then
            return
        end
        self._newMailNum[MailType.NoticeMail] = 0
        self._newMailNum[MailType.SystemMail] = 0
        self._newMailNum[MailType.DealMail] = 0
        self._newMailNum[MailType.SecurityMail] = 0
        for _,UnreadInfo in pairs(res.UnreadList or {}) do
            self._newMailNum[UnreadInfo.Type] = UnreadInfo.UnreadNum
        end
        --UI刷新显示
        self.Events.evtMailRedDotRefresh:Invoke()
        self.Events.evtReceiveNewMail:Invoke()

--[[         if res.last_expiration_time > 0 then
            local duration = res.last_expiration_time - os.time()
            self.Events.evtFetchMailTipsDuration:Invoke(duration)
        end ]]
    end

    local req = pb.CSMailGetBaseInfoReq:New()
    req:Request(onMailGetBaseInfoRes)
end

---获取新邮件总数目
function MailServer:GetNewMailTotalNum()
    local total = 0
    for _,num in pairs(self._newMailNum) do
        total = total + num
    end
    return total
end

---获取各个mailtype下的新邮件数目
function MailServer:GetAllTypeNewMail()
   return self._newMailNum
end

--收到新邮件提示
function MailServer:_OnCSMailNewMailNtf(ntf)
    self._newMailNum[ntf.Type] = ntf.NewMailNum
    self.Events.evtReceiveNewMail:Invoke()
end

---本地刷新邮件数目,n-被玩家看过的新邮件数目
function MailServer:NewMailNumRefresh(mailType,n)
    self._newMailNum[mailType] = math.max(0,self._newMailNum[mailType] - n)
    self.Events.evtLocalNewMailNumRefresh:Invoke(mailType)
end
----------------------------------------------------------------

--单封邮件的操作
--打开邮件
function MailServer:OpenMail(mailID,mailType,fCallBack)
    if not self._allMails[mailType][mailID].state.readState then
        local onCSMailReadRes = function(res)
            if not res or res.result ~= 0 then
                return
            end

            self._allMails[mailType][mailID].state.readState = true
            self._allMails[mailType][mailID].expirationTime = res.update_info.ExpirationTime

            self:NewMailNumRefresh(mailType,1)
            fCallBack(self._allMails[mailType][mailID])
        end


        local req = pb.CSMailReadReq:New()
        req.type =  mailType
        --req.Option = MailOption.ReadMail
        req.mail_id = mailID
        req:Request(onCSMailReadRes,{bEnableHighFrequency = true})
    else
        fCallBack(self._allMails[mailType][mailID])
    end
end

--删除邮件
function MailServer:DeleteMail(mailID,mailType,fCallBack)
    local onCSMailDeleteRes = function(res)
        if res and res.result == 0 then
            self._allMails[mailType][mailID] = nil
            self._mailNum[mailType].curNum = math.max(0,self._mailNum[mailType].curNum - 1)
            self:_FixSortMial(mailType) --删除邮件会使SortMial的key不连续，fix一下
            self.Events.evtMailOperation:Invoke(MailOption.DeleteMail,self._allMails)
            fCallBack()
        end
    end

    local req = pb.CSMailDeleteReq:New()
    req.type =  mailType
    --req.Option = MailOption.DeleteMail
    req.mail_id = mailID
    req:Request(onCSMailDeleteRes)
end

--接收礼物
function MailServer:ReceiveMail(mailID,attachments,mailType,fCallBack)
    local onCSMailReceiveRes = function(res)
        if res and (res.result == 0 or res.result == Err.DepositSpaceNotEnough) then
            if not table.isempty(res.update_info) then
                if res.update_info.ExpirationTime then
                    self._allMails[mailType][mailID].expirationTime = res.update_info.ExpirationTime
                end
                    self._allMails[mailType][mailID].state.receiveState = true
                for _,attachment in ipairs(res.mail_attachments) do
                    self._allMails[mailType][mailID].mail_attachments[attachment.index+1].prop_info.num =
                    self._allMails[mailType][mailID].mail_attachments[attachment.index+1].prop_info.num-attachment.prop_info.num
                end

                for _,attachment in ipairs(self._allMails[mailType][mailID].mail_attachments) do
                    if  attachment.prop_info.num >0 then
                        self._allMails[mailType][mailID].state.receiveState = false
                    end
                end
                
                --更新一键领取按键的状态计数
                if self._allMails[mailType][mailID].state.receiveState then
                    self._mailBtnState[mailType].receiveAll = self._mailBtnState[mailType].receiveAll -1
                end
                fCallBack(self._allMails[mailType][mailID].expirationTime,res.mail_attachments,res.data_change)
                ---更新一键操作按键的状态
                self.Events.evtReceiveBtnStateChange:Invoke()
                self.Events.evtMailOperation:Invoke(MailOption.ReceiveMail,mailID)
            end

            if res.result == Err.DepositSpaceNotEnough then
                if table.isempty(res.mail_attachments) then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.MailInventoryIsFull)
                else
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.MailFullDepositReceivePart)
                end
            end
        else
            if res.result == Err.DepositAddPropInvalid then --途中出现过ID无效的邮件
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.depositAddPropInvalid)
                return
            end
            Facade.ProtoManager:ManuelHandleErrCode(res)
        end
    end

    local mailInfo = {}
    mailInfo.mail_id = mailID
    mailInfo.attachments = attachments
    local mail_info = mailInfo

    local req = pb.CSMailReceiveReq:New()
    req.type =  mailType
    --req.Option = MailOption.ReceiveMail
    req.mail_info = mail_info
    self.Events.evtEnableRewardNTFCall:Invoke("Hero", false)
    req:Request(onCSMailReceiveRes,{bShowErrTip = false})
end

-----一键操作，多封邮件
--一键已读
function MailServer:ReadAllMail(mailType,fCallBack)
    local onCSMailReadAllRes = function(res)
        if res and res.result == 0 then
            --一键已读后需要从服务器同步红点数量,并将本地记录的非新邮件清空
            self:FetchMailTips()

            self:_RefreshData(mailType, MailOption.ReadAll, res.update_infos)
            fCallBack(res)
            self.Events.evtMailOperation:Invoke(MailOption.ReadAll,mailID)
        end
    end
    local req = pb.CSMailReadAllReq:New()
    req.type =  mailType
    --req.Option = MailOption.ReadAll
    req:Request(onCSMailReadAllRes)
end



--全部领取(单封邮件逐个领取)
function MailServer:ReceiveAllMailByEach(allAttachments,mailType,fCallBack,unReceivedMails,index)

    local onCSMailReceiveBatchRes = function(res)
        if res and res.result == 0 then
            for i = 1, #res.result_list do
                local mailID = res.result_list[i].update_info.MailID
                self:ReceiveCallbackFun(res.result_list[i],mailType,mailID,fCallBack,index)
                local result = res.result_list[i].result
                if result ~= 0 and (result ~= Err.DepositCurrencyOverFlow and result ~= Err.DepositAddPropInvalid) then--阻塞形错误码
                    return
                end
                index = index + 1
            end
            --unReceivedMails =self:GetAllUnReceiveMail(mailType)
            self:ReceiveAllMailByEach(allAttachments,mailType,fCallBack,unReceivedMails,index)
        else
            self.Events.evtReceiveFailed:Invoke()
        end
    end
    if index > #unReceivedMails then
        return
    end
    local mailInfos = {}
    for i = index, index + 2 do
        if i > #unReceivedMails then -- 全部遍历完
            return
        end
        local mailInfo = {}
        mailInfo.mail_id = unReceivedMails[i].MailID
        mailInfo.attachments = allAttachments[i]
        table.insert(mailInfos, mailInfo)
        if #mailInfos == 3 or i == #unReceivedMails then
            local req = pb.CSMailReceiveBatchReq:New()
            req.type =  mailType
            req.mail_infos = mailInfos
            req:Request(onCSMailReceiveBatchRes,{bShowErrTip = false,bEnableHighFrequency = true})
        end
    end

--[[     local req = pb.CSMailReceiveReq:New()
    req.type =  mailType
    req.mail_info = mailInfo
    req:Request(onCSMailReceiveRes,{bShowErrTip = false,bEnableHighFrequency = true}) ]]
end


function MailServer:ReceiveCallbackFun(res,mailType,mailID,fCallBack,index)
    if res and (res.result == 0 or res.result == Err.DepositSpaceNotEnough or res.result == Err.DepositCurrencyOverFlow or res.result == Err.DepositAddPropInvalid) then
        if not table.isempty(res.update_info) and res.result == 0  then
            self._allMails[mailType][mailID].state.readState = true--标记为已读
            if res.update_info.ExpirationTime then
                self._allMails[mailType][mailID].expirationTime = res.update_info.ExpirationTime
            end
            self._allMails[mailType][mailID].state.receiveState = true
            for _,attachment in ipairs(res.mail_attachments) do--遍历单封邮件中的附件物品
                self._allMails[mailType][mailID].mail_attachments[attachment.index+1].prop_info.num =
                self._allMails[mailType][mailID].mail_attachments[attachment.index+1].prop_info.num - attachment.prop_info.num
            end
            --更新一键领取按键的状态计数
            if self._allMails[mailType][mailID].state.receiveState then
                self._mailBtnState[mailType].receiveAll = self._mailBtnState[mailType].receiveAll -1
            end
            fCallBack(self._allMails[mailType][mailID],res,index)
            ---更新一键操作按键的状态
            self.Events.evtReceiveBtnStateChange:Invoke()
            self.Events.evtMailOperation:Invoke(MailOption.ReceiveMail)
            self:NewMailNumRefresh(mailType,1)
        else
            fCallBack(self._allMails[mailType][mailID],res,index)
        end
        if res.result == Err.DepositSpaceNotEnough then
            return
        end
    else
        Facade.ProtoManager:ManuelHandleErrCode(res)
        fCallBack(self._allMails[mailType][mailID],res,index)
    end
end




--删除已读邮件
function MailServer:DeleteAllMail(mailType)
    local onCSMailDeleteAllRes = function(res)
        if res and res.result == 0 then
            self:_RefreshData(mailType, MailOption.DeleteAll, res.update_infos)
            local bDeleteMail = not table.isempty(res.update_infos)
            self.Events.evtDeleteAllMail:Invoke(bDeleteMail,mailType)
            self.Events.evtMailOperation:Invoke(MailOption.DeleteAll,mailID)
        end
    end

    local req = pb.CSMailDeleteAllReq:New()
    req.type =  mailType
    --req.Option = MailOption.DeleteAll
    req:Request(onCSMailDeleteAllRes)
end

--更新消息中心的玩家状态
function MailServer:UpdateMsgCenterPlayerState(fCallBack)
    local idList = table.getkeys(self._playerId2MsgId)
    local function fUpdateState(newStateList)
        for _,playerInfo in pairs(newStateList) do
            for _,MsgInfo in pairs(self._allMails[MailType.MsgCenterMail]) do
                if MsgInfo.inviteInfo.inviter.player_id == playerInfo.player_id then
                    MsgInfo.inviteInfo.state = playerInfo.state
                    MsgInfo.inviteInfo.mode = playerInfo.mode_info
                    MsgInfo.inviteInfo.modes = playerInfo.mode_info_array
                end
            end
        end
        self:ReSortMsg()
        if fCallBack then
            fCallBack()
        end
    end
    Server.SocialServer:ReqPlayerState(idList,fUpdateState)
end

function MailServer:UpdateXunYouVipStatus(configID)
    local req = pb.CSMailAddSystemMailReq:New()
    req.SystemMailCfgID = configID
    local onCSMailAddSystemMailRes = function(res)
        if res and res.result == 0 then
           logerror("MailServer:UpdateXunYouVipStatus success")
        end
    end
    req:Request(onCSMailAddSystemMailRes)
end

return MailServer
