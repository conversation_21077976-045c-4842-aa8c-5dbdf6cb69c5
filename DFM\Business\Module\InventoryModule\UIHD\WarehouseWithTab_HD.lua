----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local Warehouse = require "DFM.Business.Module.InventoryModule.UI.MainV2.Warehouse"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local WarehouseExtSubPanel = require "DFM.Business.Module.InventoryModule.UI.Extension.WarehouseExtSubPanel"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ExtReplaceConfirmWindowItem = require "DFM.Business.Module.InventoryModule.UI.Extension.ExtReplaceConfirmWindowItem"
local ButtonIdConfig = require("DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig")
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local WarehouseWithTabBtn = require("DFM.Business.Module.InventoryModule.UI.Common.WarehouseWithTabBtn")

--BEGIN MODIFICATION @ VIRTUOS : 输入依赖
local UGPInputHelper = import "GPInputHelper"
local EGPInputType = import"EGPInputType"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local UGPUINavigationManager = import("GPUINavigationManager")
local UGPInputDelegates = import "GPInputDelegates"
--END MODIFICATION

---@class WarehouseWithTab_HD : LuaUIBaseView
local WarehouseWithTab_HD = ui("WarehouseWithTab_HD")

WarehouseWithTab_HD.FILTER_TAB_FOLD_THRESHOLD = 80

local function log(...)
    loginfo("[WarehouseWithTab_HD]", ...)
end

local MAX_SAME_ITEM_CLICK_NUM = 2
local MIN_SAME_ITEM_IN_DEPOSIT = 3
local SELECT_ALL_PANEL_SHOW_SECOND = 4
WarehouseWithTab_HD.SUB_CELL_DRAG_DELAY = 0.1


WarehouseWithTab_HD.ARRANGE_BTN_ON_IMG = "PaperSprite'/Game/UI/UIAtlas/System/Warehouse/BakedSprite/Warehouse_Btn_08.Warehouse_Btn_08'"
WarehouseWithTab_HD.ARRANGE_BTN_OFF_IMG = "PaperSprite'/Game/UI/UIAtlas/System/Warehouse/BakedSprite/Warehouse_Btn_01.Warehouse_Btn_01'"
WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY = "WAREHOUSE_LAST_SELECT_TABS_TO_SORT"
WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY_NOT_FIRST = "LAST_SELECT_INDEXS_KEY_NOT_FIRST"

function WarehouseWithTab_HD:Ctor()
    self._bInExtArrangeMode = false
    self._bInExtManagementMode = false
    self._bIsCanInExtManagementMode = true
    self._bShowSellEntrance = true
    self._bShowExtManageExtrance = true
    self._bInReplaceExtMode = false
    self._curFilterMode = eDepositSortClass.none
    self._mapCurrency2SellPrice = {}
    self._sameItemNum = {} -- id : num
    ---@type ItemBase[]
    self._otherSameItems = {}
    ---@type WarehouseTopHint
    self._topHint = nil
    self._mapSortClassId2Pos = {}
    ---@type WarehouseFilterTab[]
    self._filterTabs = {}
    self._mapSortClassId2Pos = {}
    self._bFilterBtnToggle = false
    self._bExtManageBtnToggle = false
    self._delayScrollToItemHandle = nil
    self._filterAnimHandle = nil
    self._depositNum = {}
    self._curDepositIndex = nil
    self._isNomoreTip = false
    self._cacheDepositTabChildren = nil
    ---BEGIN MODIFICATION @ yanmingjing
    self._extArrangeNavGroup = nil
    ---END

    -----------------------------------------------------------------------
    --region Main panel
    self._wtMainCanvas = self:Wnd("wtMainCanvas", UIWidgetBase)
    self._wtWarehouse = self:Wnd("wtWarehouse", Warehouse)
    --endregion
    -----------------------------------------------------------------------

    -----------------------------------------------------------------------
    --region Tab panel

    -- 常驻顶部的主仓库图标
    self._wtMainDepositTab = self:Wnd("WBP_WarehouseExpansionButton_Pc", UIWidgetBase)
    self._wtMainDepositTab:Visible()

    --BEGIN MODIFICATION @ VIRTUOS : 
    self._wtMainDepositTab:SetCppValue("bIsFocusable", true)
    --END MODIFICATION

    self._wtFilterPanel = self:Wnd("wtFilterPanel", UIWidgetBase)
    self._wtExtPanel = self:Wnd("wtExtPanel", UIWidgetBase)
    self._wtDepositTabScrollBox = self:Wnd("wtDepositTabScrollBox", UIWidgetBase)
    self._wtExtVerticalBox = self:Wnd("wtExtVerticalBox", self)

    self._wtSubBtnsPanel = self:Wnd("wtSubBtnsPanel", UIWidgetBase)
    self._wtExtArrangeBtn = self:Wnd("wtExtArrangeBtn", WarehouseWithTabBtn)
    self._wtExtArrangeBtn:SetBtnType(InventoryConfig.WarehouseBtnType.ExtArrangeBtn)
    self._wtExtArrangeBtn:BindBtnClickedCallBack(self._OnExtArrangeBtnPress, self)

    self._wtMaskPanel = self:Wnd("wtMaskPanel", UIWidgetBase)

    --BEGIN MODIFICATION @ VIRTUOS : Set Focusable
    self._wtExtArrangeBtn:SetCppValue("bIsFocusable", true)
    -- 这里的关闭wtButton的Focusable，防止其影响聚焦后查找NavGroup
    local wtExtArrangeAtomBtn = self._wtExtArrangeBtn:Wnd("wtButton", UIWidgetBase)
    wtExtArrangeAtomBtn:SetCppValue("IsFocusable", false)
    --END MODIFICATION

    self._wtSellBtn = self:Wnd("wtSellBtn", WarehouseWithTabBtn)
    self._wtSellBtn:SetBtnType(InventoryConfig.WarehouseBtnType.SellBtn)
    self._wtSellBtn:BindBtnClickedCallBack(self._OnSellBtnClicked, self)
    --BEGIN MODIFICATION @ VIRTUOS : Set Focusable
    self._wtSellBtn:SetCppValue("bIsFocusable", true)
    --END MODIFICATION

    self._wtWishListBtn = self:Wnd("wtWishListBtn", WarehouseWithTabBtn)
    self._wtWishListBtn:SetBtnType(InventoryConfig.WarehouseBtnType.WistBtn)
    self._wtWishListBtn:BindBtnClickedCallBack(self._OnWishListBtnClicked, self)
    self._wtWishListBtn:Collapsed()

    self._wtCollectionHall = self:Wnd("wtCollectionHall", WarehouseWithTabBtn)
    self._wtCollectionHall:SetBtnType(InventoryConfig.WarehouseBtnType.CollectionRoom)
    self._wtCollectionHall:BindBtnClickedCallBack(self._OnCollectionHallBtnClicked, self)
    self._wtCollectionHall:Hidden()
    --endregion
    -----------------------------------------------------------------------

    -----------------------------------------------------------------------
    --region Other panel

    -- 出售按钮点击弹出ui(需要拆出)
    self._sellSubPanel = nil
    self._slotSellSubSlot = self:Wnd("Slot_Warehouse_SellSub", UIWidgetBase)
    self._slotSellSubSlot:Collapsed()

    self._wtExtManageBtn = self:Wnd("wtExtManageBtn", WarehouseWithTabBtn)
    self._wtExtManageBtn:SetBtnType(InventoryConfig.WarehouseBtnType.ExtManagerBtn)
    self._wtExtManageBtn:BindBtnClickedCallBack(self._OnExtManageBtnClick, self)

    -- 整理按钮点击弹出ui(需要拆出)
    self._extArrangePanel = nil
    self._slotExtArrange = self:Wnd("Slot_Warehouse_ExtArrange", UIWidgetBase)
    self._slotExtArrange:Collapsed()

    self._wtMaskImgPanel = self:Wnd("wtMaskImg", UIWidgetBase)
    self._wtMaskImg = self:Wnd("wtMaskImg1", UIImage)
    self._wtMaskImg:Event("OnMouseButtonDownEvent", self._OnMaskImgClicked, self)

    -- 持有一下出售按钮的display action handle
    self._sellHandle = nil

    --endregion
    -----------------------------------------------------------------------

    -----------------------------------------------------------------------
    --region button Tips Init

    -- self._wtDFTipsAnchor_1 = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_1", self._ShowCollectionBtnTips, self._HideCollectionBtnTips)
    -- self._wtDFTipsAnchor_2 = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_2", self._ShowWishListBtnTips, self._HideWishListBtnTips)
    -- self._wtDFTipsAnchor_3 = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_3", self._ShowExtBtnTips, self._HideExtBtnTips)
    -- self._wtDFTipsAnchor_4 = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_4", self._ShowSellBtnTips, self._HideSellBtnTips)
    -- self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor", self._ShowExtManagerBtnTips, self._HideExtManagerBtnTips)
    
    -- self._BtnToTipsText = setmetatable({
    --     -- [self._wtCollectionInBtn] = Module.Inventory.Config.Loc.CollectionInBtnText,
    --     [self._wtWishListBtn] = Module.Inventory.Config.Loc.WishListBtnText,
    --     [self._wtExtArrangeBtn] = Module.Inventory.Config.Loc.ExtArrangeBtnText,
    --     [self._wtSellBtn] = Module.Inventory.Config.Loc.SellBtnText,
    -- }, weakmeta)
    -- self._BtnToTipsHandle = setmetatable({}, weakmeta)

    --endregion
    -----------------------------------------------------------------------

    
end

--==================================================
--region Life function

function WarehouseWithTab_HD:OnOpen()
    -- self:_CheckExtLvlUpHintShouldShow()
end

function WarehouseWithTab_HD:OnClose()
    Timer.CancelDelay(self._filterAnimHandle)
    self._filterAnimHandle = nil
    Timer.CancelDelay(self._delayScrollToItemHandle)
    self._delayScrollToItemHandle = nil
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonUpEvent:Remove(self._OnGlobalTouchUp, self)
end

function WarehouseWithTab_HD:OnShowBegin()
    self:_InitDepositTabList()
    self:_OnCollectionHallBtnVisible()
end

function WarehouseWithTab_HD:OnShow()
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMoveBatch, self._OnItemMoveBatch, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostSortMultiPos, self._OnPostSortMultiPos, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtNotifyExtOrderChanged, self._OnNotifyExtOrderChanged, self)
    self:AddLuaEvent(Module.CommonWidget.Config.Events.evtPostItemMultiSelected, self._OnPostItemMultiSelected, self)
    self:AddLuaEvent(InventoryConfig.Events.evtExtManageDragSortStart, self._OnExtManageDragSortStart, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtInventoryFetchFinished, self._OnCancelSellBtnClick, self)

    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragStart, self._OnGlobalDragStart, self)
    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDrop, self._OnGlobalDragStop, self)
    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragCancelled, self._OnGlobalDragStop, self)
    self:AddLuaEvent(InventoryConfig.Events.evtJumpToExtSlot, self._SelectDepositTab, self)
    self:AddLuaEvent(InventoryConfig.Events.evtSaveExpansionNum, self._SaveExpansionNum, self)
    self:AddLuaEvent(InventoryConfig.Events.evtCompareExpansionNum, self._CompareExpansionNum, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtArrangeFaild, self._WarehouseArrangeFaild, self)
    self:AddLuaEvent(InventoryConfig.Events.evtPopWindowStageChange, self.OnPopWindowStageChange, self)
    self:AddLuaEvent(InventoryConfig.Events.evtSaleCompleted, self._CancelSell, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEnterReplaceExtPanel, self._SetExtReplaceMode, self)
    self:AddLuaEvent(Server.BlackSiteServer.Events.evtBlackSiteUpgradeSuccess2Warehouse, self._BlackSiteUpgradeSuccess, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtExtensionBoxRes, self._OnExtensionBoxRes, self)

    -- BEGIN MODIFICATION @ VIRTUOS : 
    self:AddLuaEvent(InventoryConfig.Events.evtWareHouseDepositTabOnFocusReceived, self._OnDepositTabMouseEnter, self)
    self:AddLuaEvent(InventoryConfig.Events.evtWareHouseDepositTabOnFocusLost, self._OnDepositTabMouseLeave, self)
    self:AddLuaEvent(InventoryConfig.Events.evtBottomBarSettingArrMode, self._OnEvtBottomBarSettingArrMode, self)
    -- END MODIFICATION
    
    if Server.InventoryServer:GetSortEveryEnter() then
        self:AutoArrangeWarehouse()
    end
    if DFHD_LUA == 1 then
    else
        self:AddLuaEvent(InventoryConfig.Events.evtAutoScrollVisible, self._AutoScrollVisible, self)
    end

    -- self:_OnExtManageDragSortStart(false)

    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonUpEvent:Add(self._OnGlobalTouchUp, self)

    -- BEGIN MODIFICATION @ VIRTUOS : 默认是Longer会导致整个Tab太宽，会影响正常情况下Tab往右的UI导航
    self:SetLonger(false)
    -- END MODIFICATION

    self:_EnableInputTypeChangedHandle(true)
end

function WarehouseWithTab_HD:OnHide()
    self:_EnableInputTypeChangedHandle(false)
    self:_CancelSell()
    self:_CancelExtArrange()
    self:_EndExtManagement()
    self:_HideTopHint()
    self:_EndExtReplace()
    if self._reorderableListComponent then
        self._reorderableListComponent:Reset()
    end
    self:_ReleaseClickHandle()

    self:RemoveAllLuaEvent()

    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonUpEvent:Remove(self._OnGlobalTouchUp, self)
end

function WarehouseWithTab_HD:OnInitExtraData()
end

function WarehouseWithTab_HD:_OnGlobalDragStart()
    self._wtSubBtnsPanel:Collapsed()
end

function WarehouseWithTab_HD:_OnGlobalDragStop()
    if not self._bInExtManagementMode and Module.CommonWidget:GetItemOperMode() ~= EItemOperaMode.SellMode then
        self._wtSubBtnsPanel:SelfHitTestInvisible()
    end
end

function WarehouseWithTab_HD:_AutoScrollVisible(bDirection, bVisible)
    if not bDirection then
        if bVisible then
            self._wtSubBtnsPanel:SelfHitTestInvisible()
        else
            self._wtSubBtnsPanel:Collapsed()
        end
    end
end

function WarehouseWithTab_HD:_OnGlobalTouchUp()
    if self._bShouldFoldFilterTab then
        self._bShouldFoldFilterTab = false

        -- self._wtFilterBtn:SetIsChecked(false, true)
        -- self:_OnFilterBtnClicked(false)
    end
end
--endregion
--==================================================

--==================================================
--region Public API

function WarehouseWithTab_HD:PlayInAnim()
    -- self:PlayWidgetAnim(self.In_Anim)
    -- self._wtWarehouse:PlayInAnim()
end

function WarehouseWithTab_HD:GetCurrentShowDepositId()
    return self._wtWarehouse:GetCurrentShowDepositId()
end

function WarehouseWithTab_HD:GetWarehouseWidget()
    return self._wtWarehouse
end

function WarehouseWithTab_HD:ShowFilterBtn(bShow)
    if bShow then
        self._wtFilterBtn:Visible()
    else
        self._wtFilterBtn:Collapsed()
    end
end
function WarehouseWithTab_HD:ShowSellExtrance(bShow)
    self._bShowSellEntrance = bShow

    self:_UpdateSellBtnVisibility(bShow)
end

function WarehouseWithTab_HD:ShowExtManageExtrance(bShow)
    self._bShowExtManageExtrance = bShow

    if bShow then
        self._wtExtManageBtn:Visible()
    else
        self._wtExtManageBtn:Collapsed()
    end
end

function WarehouseWithTab_HD:ShowExtArrangeBtn(bShow)
    if bShow and self._wtWarehouse:IsInGridMode() then
        self._wtExtArrangeBtn:Visible()
    else
        self._wtExtArrangeBtn:Collapsed()
    end
end

function WarehouseWithTab_HD:ShowCollectionInBtn(bShow)
    if bShow then
        self._wtCollectionInBtn:Visible()
    else
        self._wtCollectionInBtn:Collapsed()
    end
end

function WarehouseWithTab_HD:ShowWishListBtn(bShow)
    if bShow then
        self._wtWishListBtn:Visible()
    else
        self._wtWishListBtn:Collapsed()
    end
end

function WarehouseWithTab_HD:_UpdateSellBtnVisibility(bShow)
    if bShow and self._bShowSellEntrance and self._wtWarehouse:IsInGridMode() then
        self._wtSellBtn:Visible()
    else
        self._wtSellBtn:Collapsed()
    end
end

function WarehouseWithTab_HD:SetShowCurNumDeposit(bShowCurNum)
    self._bShowCurNumDeposit = bShowCurNum
    self:_InitDepositTabList()
end

function WarehouseWithTab_HD:BackToWarehouse()
    local extSubPanelParam = Module.Inventory.Field:GetIsInExtSubPanel()
    if extSubPanelParam.bInExtSubPanel then
        self:_StartExtManagement()
        Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, extSubPanelParam.index)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, extSubPanelParam.index)
    end
end


-- 收藏室界面的显隐
function WarehouseWithTab_HD:SetCollectionBtnPanel(bInCollectionRoom)
    if bInCollectionRoom then
        self._wtCollectionHall:Hidden()
        self._wtExtManageBtn:Collapsed()
        self._wtSellBtn:Collapsed()
    end
end

--endregion
--==================================================

--==================================================
--region Private API

function WarehouseWithTab_HD:_ResetDepositTabScrollBox()
    -- 用lua table加速查询
    if not self._cacheDepositTabChildren then
        self._cacheDepositTabChildren = {}
        -- local childWidgets = self._wtDepositTabScrollBox:GetAllChildren()
        local childWidgets = self._wtExtVerticalBox:GetAllChildren()
        if childWidgets and #childWidgets ~= 0 then
            for i, childWidget in pairs(childWidgets) do
                self._cacheDepositTabChildren[i+1] = childWidget
            end
        end
    end

    -- 隐藏子节点
    for i, childWidget in pairs(self._cacheDepositTabChildren) do
        childWidget:Collapsed(nil, true)
    end

    --self._wtDepositTabScrollBox:ClearChildren()
end

-- 调用必须连续
function WarehouseWithTab_HD:_GetOrCreateDepositTabItem(idx)
    -- 走节点缓存
    if idx <= #self._cacheDepositTabChildren then
        local childWidget = self._cacheDepositTabChildren[idx]
        -- childWidget:Show(nil, true)
        childWidget:Visible()
        return childWidget
    end

    --local newTabWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseDepositTab, self._wtDepositTabScrollBox)
    --local childWidget = getfromweak(newTabWeakIns)
    local childWidget = Module.Inventory:ObtainWarehouseDepositTabFromPool()
    if childWidget then
        -- self._wtDepositTabScrollBox:AddChild(childWidget)
        self._wtExtVerticalBox:AddChild(childWidget)
        self._cacheDepositTabChildren[idx] = childWidget
    end
    return childWidget
end

function WarehouseWithTab_HD:_InitDepositTabList(targetSelectTabIndex)
    targetSelectTabIndex = setdefault(targetSelectTabIndex, 1)

    self._curDepositIndex = targetSelectTabIndex
    local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
    local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
    -- if WidgetUtil.IsGamepad() then
    --     -- 使用手柄时，只显示一个加号
    --     availableDepositNum = 1
    -- end
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    local lockSlotNum = Server.InventoryServer:GetLockSlotNum()
    local bHaveVipExt = Server.InventoryServer:IsHaveVipExt()

    ---@type WarehouseDepositTab[]
    self._allDepositTabs = {}
    -- self._wtExtVerticalBox:ClearChildren()
    self:_ResetDepositTabScrollBox()

    local targetShowTabNum = curDepositNum -- Don't show locked
    if WidgetUtil.IsGamepad() then
        targetShowTabNum = curDepositNum + 1
    else
        if self._bShowExtManageExtrance then
            local defaultTagetNum = curDepositNum + availableDepositNum + 1                     -- 仓库常驻锁定图标 + VIP扩容箱槽位
            targetShowTabNum = defaultTagetNum + 1
        end
    end
    targetShowTabNum = math.min(targetShowTabNum, maxDepositNum)

    -- 如果是拍卖行界面就不用显示availableDepositNum
    if self._bShowCurNumDeposit or ItemOperaTool.CheckIsDIYCabinetPanel() then
        targetShowTabNum = curDepositNum
    end

    for i = 1, targetShowTabNum do
        ---@type WarehouseDepositTab 
        -- local newTabWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseDepositTab, self._wtExtVerticalBox)
        -- local newTab = getfromweak(newTabWeakIns)
        local newTab
        if i == 1 then
            newTab = self._wtMainDepositTab
        else
            newTab = self:_GetOrCreateDepositTabItem(i - 1)
        end
        if newTab then
            newTab:InitDepositTab(i)
            newTab:BindBtnCallback(self._OnDepositTabClicked, self)
            newTab:BindDragStayCallback(self._OnDragStayCallback, self)
            newTab:BindLongPressCallback(self._OnTabLongPressedCallback, self)
            self._allDepositTabs[i] = newTab
        end
    end

    Module.Guide:RemoveGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyWarehouseExpansion)
    -- 注册一下新手引导中会引导到的扩容槽位对应的ui
    if self._allDepositTabs[curDepositNum + 1] then
        Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyWarehouseExpansion, self._allDepositTabs[curDepositNum + 1])
    end

    if not self._bInExtArrangeMode then
        self._curDepositTabIndex = 0
        self._curMultiTabIndexs = {}

        self:_SelectDepositTab(targetSelectTabIndex)
    else
        -- 可能是从其他设置（例如设置页面）返回的
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            warehouseTab:SetInMultiMode(true)

            if self._curMultiTabIndexs[i] then
                self:_SetMultiSelected(i, true)
            end
        end
    end
end

function WarehouseWithTab_HD:_OnDepositTabClicked(index)
    if self._bInExtArrangeMode then
        self:_OnDepositTabClicked_ExtArrangeMode(index)
    elseif self._bInReplaceExtMode then
        self:_OnDepositTabClicked_ReplaceExtMode(index)
    else
        self:_OnDepositTabClicked_Normal(index)
    end
end

function WarehouseWithTab_HD:_OnDepositTabClicked_Normal(index)
    local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
    local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHTab)

    if index <= 0 or index > maxDepositNum then
        return
    end

    if index <= curDepositNum then
        -- Cur exist, selet tab
        self:_SelectDepositTab(index)
        self._curDepositIndex = index
    elseif index <= curDepositNum + availableDepositNum then
        -- Available, open ext selection window
        Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, index)
        LogAnalysisTool.SignButtonClicked(10060004)
    elseif index == curDepositNum + availableDepositNum + 1 then
        self:_SelectDepositTab(index)
        self._curDepositIndex = index
    else
        -- Lock, show lock tip
        -- Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseExtSlotLock)
        local title = InventoryConfig.Loc.LevelupWarehouseToUnlockMoreExtSlots
        local confirmBtnText = InventoryConfig.Loc.LevelupWarehouseToUnlockMoreExtSlots_ConfirmBtnText
        local function fOnConfirm()
            Module.BlackSite:Jump(1010, true)
        end
        Module.CommonTips:ShowConfirmWindow(title, fOnConfirm, nil, nil, confirmBtnText)
    end
    if Module.CommonWidget:GetIsItemSellMode() then
        -- 如果当前是出售模式，则尝试聚焦到分页的第一个道具
        self._wtWarehouse:TryFocusToFirstItem()
    end
end

function WarehouseWithTab_HD:_OnDepositTabClicked_ExtArrangeMode(index)
    local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
    local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()

    if index <= 0 or index > maxDepositNum then
        return
    end

    if index <= curDepositNum then
        self:_SetMultiSelected(index)
        self:_PostRefreshTabMultiSelected()
    end
end

function WarehouseWithTab_HD:_OnDepositTabClicked_ReplaceExtMode(index)
    if index == 1 then
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.MainExtCantReplace)
        return
    end
	local extSlot = Server.InventoryServer:GetExtSlotByIndex(index)
	if InventoryLogic.StartExtItemReplacementInvalidation(self._extItem2Replace, extSlot, true) then
        InventoryConfig.Events.evtEnterReplaceExtPanel:Invoke(false)
    end
end


function WarehouseWithTab_HD:_SelectDepositTab(index, item)
    if self._curDepositTabIndex == index then
        return
    end

    if self._allDepositTabs[index].onDropState then
        self._allDepositTabs[index]:ChangeDropState()
        return
    end

    self._curDepositTabIndex = index

    for i = 1, #self._allDepositTabs do
        self._allDepositTabs[i]:SetSelected(i == index)
    end

    local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
    if depositId ~= ESlotType.None then
        self._wtWarehouse:ShowInGridMode(depositId, item)
    else
        self._wtWarehouse:ShowInGridMode(ESlotType.VipDepositContainer, item)
    end
end

function WarehouseWithTab_HD:_SetMultiSelected(index, bForceMultiSelected)
    local tab = self._allDepositTabs[index]

    if bForceMultiSelected == nil then
        local bCurMuitlSelected = self._curMultiTabIndexs[index]
        self._curMultiTabIndexs[index] = not bCurMuitlSelected
        tab:SetMultiSelected(not bCurMuitlSelected)
    else
        self._curMultiTabIndexs[index] = bForceMultiSelected
        tab:SetMultiSelected(bForceMultiSelected)
    end
end

-- 未装备的扩容箱槽拦截交互
function WarehouseWithTab_HD:_SetExtSlotInteraction(bShield)
	local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        -- 只有当tab处于显示状态时才处理，否则不处理
        if warehouseTab:IsShowInPanel() then
            if bShield and i > curDepositNum then
                warehouseTab:HitTestInvisible()
            else
                warehouseTab:Visible()
            end
        end
    end
end

function WarehouseWithTab_HD:_PostRefreshTabMultiSelected()
    -- Post update
    local curSelectNum = 0
    for k, v in pairs(self._curMultiTabIndexs) do
        if v == true then
            curSelectNum = curSelectNum + 1
        end
    end

    if self._extArrangePanel then
        self._extArrangePanel:IsSelectedExpansionBox(curSelectNum)
        self._extArrangePanel:SetCheckBoxState(curSelectNum == Server.InventoryServer:GetCurDepositSlotNum())
    end
end


function WarehouseWithTab_HD:_OnDragStayCallback(index)
    local curDragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
    local item = curDragDropInfo and curDragDropInfo.item

    local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
    local refDepositSlot = Server.InventoryServer:GetSlot(depositId)

    if not item or not refDepositSlot then
        return
    end

    ---@type EquipmentFeature
    local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
    local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()
    if bExtendItem and depositId ~= ESlotType.MainContainer then
        -- 箱子拖到已装箱子的槽位后，不用走是否放入判断的逻辑。只走是否能替换的逻辑
        return
    end

    local bTypeFit = ItemOperaTool.VerifyItemForTargetSlot(item, refDepositSlot, false)
    local bHasSpace = refDepositSlot:TryFindLocationForItem(item)

    -- 类型符合就切到对应的Tab
    if bTypeFit then
        self:_SelectDepositTab(index)
    end

    if bTypeFit and bHasSpace then

    else
        local extItem = Server.InventoryServer:GetExtItemByIndex(index)
        if not extItem then
            return
        end
        local txt
        if not bTypeFit then
            txt = string.format(InventoryConfig.Loc.ExtItemDontSupportThisItem, extItem.name)
        elseif not bHasSpace then
            txt = string.format(InventoryConfig.Loc.ExtItemSpaceNotEnough, extItem.name)
        end

        if txt then
            Module.CommonTips:ShowSimpleTip(txt)
        end
    end
end

function WarehouseWithTab_HD:_OnTabLongPressedCallback(index)
    -- 界面处于某些状态时，无法通过长按进入扩容箱管理页
    if self._bIsCanInExtManagementMode and not ItemOperaTool.bInSettlement and not Module.CommonWidget:IsAuctionMode() and not ItemOperaTool.CheckIsDIYCabinetPanel() then
        -- 先跳转至当前长按的网格再打开扩容箱管理页
        self:_SelectDepositTab(index)
        self._curDepositIndex = index
        self:_StartExtManagement(index)
    end
end

---@param component TabComponentParams
function WarehouseWithTab_HD:_OnFilterTabClicked(mainIndex, subIndex, component)
    log("OnFilterTabClicked", mainIndex, subIndex, component)
    ---@type WarehouseFilterTab
    local selectedTab = component:GetMainTab(mainIndex)
    local sortClassId = selectedTab:GetSortClassId()

    self._curFilterMode = sortClassId
    self._wtWarehouse:ShowInListMode(sortClassId)

    if self._filterTabCallback and self._filterTabIns then
        self._filterTabCallback(self._filterTabIns, self._curFilterMode)
    end
end

function WarehouseWithTab_HD:_RefreshFilterTabStatus()
    for index, filterTab in ipairs(self._filterTabs) do
        local sortClassID = filterTab:GetSortClassId()
        local fFilterFunc = InventoryLogic.GetFilterFunctionBySortClassId(sortClassID)
        local bHasItem = false
        for _, item in Server.InventoryServer:GetItemsIterator() do
            local slot = item.InSlot
            if slot then
                local bInDeposit = slot:IsDepositorySlot()
                if bInDeposit and fFilterFunc(item) then
                    bHasItem = true
                    break
                end
            end
        end

        self._tabComponent:SetTabEnabled(
            index,
            bHasItem,
            function()
                Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.NoFilterItems)
            end
        )
    end
end

function WarehouseWithTab_HD:_OnCollectionHallBtnVisible()
    -- azhengzheng:控制收藏室显隐
    -- 先判断是否在局外
    if Facade.GameFlowManager:CheckIsInFrontEnd() and ItemOperaTool.CheckIsWarehouseMainPanel() then
        local deviceData = Module.BlackSite:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)

        -- 再判断收藏室等级
        if deviceData and deviceData:GetLevel() ~= 0 then
            self._wtCollectionHall:Visible()
        else
            self._wtCollectionHall:Hidden()
        end
    else
        self._wtCollectionHall:Hidden()
    end
end

-- 收藏室入口按钮得特殊处理
function WarehouseWithTab_HD:_SetCollectionRoomBtnInteraction(bInteractive)
    local btnSlateVisibility = self._wtCollectionHall.Visibility
    if btnSlateVisibility == ESlateVisibility.Collapsed or btnSlateVisibility == ESlateVisibility.Hidden then
        return
    end
    if bInteractive then
        self._wtCollectionHall:Visible()
    else
        self._wtCollectionHall:HitTestInvisible()
    end
end

-----------------------------------------------------------------------
--region Ext Manage 扩容管理

function WarehouseWithTab_HD:_OnExtManageBtnClick()
    if self._bInExtManagementMode then
        self:_EndExtManagement()
        return
    elseif self._bInReplaceExtMode then
        self:_EndExtReplace()
        return
    end
    self:_StartExtManagement()
end

function WarehouseWithTab_HD:_CheckExtLvlUpHintShouldShow()
    local bShowLvlUpHint = false
    local allExtIds = Server.InventoryServer:GetAllExtIds()
    for _, extId in ipairs(allExtIds) do
        local slot = Server.InventoryServer:GetSlot(extId)
        local equipItem = slot:GetEquipItem()
        if equipItem and Server.InventoryServer:CheckExtItemCanLvlUp(equipItem.id) then
            bShowLvlUpHint = true
            break
        end
    end

    if bShowLvlUpHint then
        self._wtExtLvlUpHint:SelfHitTestInvisible()
    else
        self._wtExtLvlUpHint:Collapsed()
    end
end

function WarehouseWithTab_HD:_StartExtManagement(targetSelectTabIndex)
    if self._bInExtManagementMode then
        return
    end
    Module.Inventory.Field:SetWHExtManageMode(self._bInExtManagementModeode)
    Module.ItemDetail:CloseItemDetailPanel()
    self._bInExtManagementMode = true
    self:SetLonger(true)
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtManagerMode(true)
    end

    local function fOnDragStart(cell)
        cell:PlayLeftInAnim()
        cell:SetRemoveBtnVisible(false)
        cell:SetDepositIsOperating(true)
        loginfo("SunBillows-_StartExtManagement:fOnDragStart")

        -- 拖拽开始时，bottombar不该响应
        local summaryList = {}
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    end
    local function fOnDragStop(cell)
        cell:PlayLeftOutAnim()
        cell:SetRemoveBtnVisible(true)
        cell:SetDepositIsOperating(false)
        loginfo("SunBillows-_StartExtManagement:fOnDragStop")

        -- 拖拽结束时，bottombar响应
        local summaryList = {}
        table.insert(summaryList, {actionName = "EndManage", func = self._EndExtManagement, caller = self ,bUIOnly = false, bHideIcon = false})
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    end
    local function fOnSwapPosEachStep(lastIndex, newIndex)
        log("fOnSwap", lastIndex, newIndex)
    end
    local function fOnShiftPosFinally(lastIndex, newIndex)
        log("fOnShiftPosFinally", lastIndex, newIndex)

        --remapping
        local cellsInOrder = self._wtExtVerticalBox:GetAllChildren()
        for index, cell in ipairs(cellsInOrder) do
            -- 重新排序
            self._cacheDepositTabChildren[index] = cell
        end
        -- 排序后，当前选中由lastIndex变为newIndex，主仓库被剔除出VerticalBox，所以索引应该减1
        self._curDepositTabIndex = newIndex + 1
        Server.InventoryServer:ShiftExtOrder(lastIndex, newIndex)
        Server.InventoryServer:DoChangeSortConfig()
    end
    local function fOnClickedDeposit(cell)
        cell:OnClicked()
    end
    ---@type ReorderableListParams
    local params = {
        verticalBox = self._wtExtVerticalBox,
        dragDelay = WarehouseWithTab_HD.SUB_CELL_DRAG_DELAY,
        fOnDragStart = fOnDragStart,
        fOnDragStop = fOnDragStop,
        fOnSwapPosEachStep = fOnSwapPosEachStep,
        fOnShiftPosFinally = fOnShiftPosFinally,
        fOnClicked = fOnClickedDeposit
    }
    self._reorderableListComponent = Module.CommonWidget:InitReorderableList(params)
    loginfo("SunBillows-_StartExtManagement:InitReorderableList")
    local minLimit = Server.InventoryServer:IsHaveUsedVipExt() and 2 or 1               -- 由于主仓库被冻结，不算入verticalbox中，所以minlimit需要剔除主仓库
    local maxLimit = Server.InventoryServer:GetCurDepositSlotNum() - 1                  -- 由于主仓库被冻结，不算入verticalbox中，所以maxlimit需要剔除主仓库
    self._reorderableListComponent:SetLimit(minLimit, maxLimit)

    --BEGIN MODIFICATION @ VIRTUOS : 绑定手柄调整ReorderableList的相关方法
    ---@type ReorderableListGamepadParams
    local gamepadParams = {
        ownerWidget = self,
        fOnGamepadMoveBegin = self._fOnGamepadMoveBegin,
        fOnGamepadMoveFinish = self._fOnGamepadMoveFinish,
        fOnGamepadMoveCancel = self._fOnGamepadMoveCancel
    }
    self._reorderableListComponent:InitGamepadParams(gamepadParams)
    --END MODIFICATION

    -- 扩容箱管理模式下，控制其他按钮的交互
    self._wtExtArrangeBtn:HitTestInvisible()
    self._wtSellBtn:HitTestInvisible()
    self._wtWarehouse:HitTestInvisible()
    self._wtExtManageBtn:ChangeState(true)
    self:_SetCollectionRoomBtnInteraction(false)

    InventoryConfig.Events.evtEnterExtManageMode:Invoke(true, true)
    self:OnEnterASMOperation(true)
    local summaryList = {}
    table.insert(summaryList, {actionName = "EndManage", func = self._EndExtManagement, caller = self ,bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end

function WarehouseWithTab_HD:_EndExtManagement()
    if not self._bInExtManagementMode then
        return
    end
    Module.Inventory.Field:SetWHExtManageMode(self._bInExtManagementModeode)
    self._bInExtManagementMode = false
    self._wtWarehouse:SelfHitTestInvisible()
    self:SetLonger(false)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        -- 退出管理时需要保证位置都回正，防止产生位置偏移
        -- item位置虽然回正，但是从逻辑上说item的位置没有变化，所以在拖拽之后需要重新更新verticalbox中子ui的位置
        if warehouseTab:GetDepositIsOperating() then
            warehouseTab:SetRenderTranslation(FVector2D.ZeroVector)
            warehouseTab:PlayLeftOutAnim()
            warehouseTab:SetDepositIsOperating(false)

            -- verticalbox中子ui的位置顺序更新
            local cellsInOrder = self._wtExtVerticalBox:GetAllChildren()
            for index, cell in ipairs(cellsInOrder) do
                -- 重新排序
                self._cacheDepositTabChildren[index] = cell
            end
        end
        warehouseTab:SetInExtManagerMode(false)
    end

    self._wtExtArrangeBtn:SelfHitTestInvisible()
    self._wtSellBtn:SelfHitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(true)

    self._wtExtManageBtn:ChangeState(false)
    
    self:_SelectDepositTab(self._curDepositIndex)
    InventoryConfig.Events.evtEnterExtManageMode:Invoke(false)
    self:OnEnterASMOperation(false)
    WidgetUtil.SetUserFocusToWidget(self._wtExtManageBtn, true)    
    Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
end

function WarehouseWithTab_HD:_OnEndManage()
    self:_EndExtManagement()
end

function WarehouseWithTab_HD:_SetExtReplaceMode(bInReplaceExtMode, ext2Replace)
    if bInReplaceExtMode then
        self:_StartExtReplace(ext2Replace)
        self._extItem2Replace = ext2Replace
    else
        self:_EndExtReplace()
    end
end

function WarehouseWithTab_HD:_BlackSiteUpgradeSuccess()
    self:_InitDepositTabList()
end

function WarehouseWithTab_HD:_StartExtReplace(ext2Replace)
    if self._bInReplaceExtMode then
        return
    end
    self._bInReplaceExtMode = true
    self:SetLonger(true)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtReplaceMode(true, ext2Replace)
    end

    -- 扩容箱管理模式下，控制其他按钮的交互
    self._wtExtArrangeBtn:HitTestInvisible()
    self._wtSellBtn:HitTestInvisible()
    self._wtWarehouse:HitTestInvisible()
    self._wtExtManageBtn:ChangeState(true)
    self:_SetCollectionRoomBtnInteraction(false)

    -- BottomBar设置
    local summaryList = {}
    table.insert(summaryList, {actionName = "EndManage", func = self._EndExtReplace, caller = self ,bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)

    InventoryConfig.Events.evtEnterExtManageMode:Invoke(true, false)
end

function WarehouseWithTab_HD:_EndExtReplace()
    if not self._bInReplaceExtMode then
        return
    end
    self:SetLonger(false)

    self._bInReplaceExtMode = false
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtReplaceMode(false)
    end

    self._wtExtArrangeBtn:SelfHitTestInvisible()
    self._wtSellBtn:SelfHitTestInvisible()
    self._wtWarehouse:SelfHitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(false)
    self._wtExtManageBtn:ChangeState(false)

    self:_SelectDepositTab(self._curDepositIndex)
    InventoryConfig.Events.evtEnterExtManageMode:Invoke(false)
    Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Ext Arrange 扩容整理

function WarehouseWithTab_HD:_InitStartupMultiSelectIndexs()
    self._curMultiTabIndexs = {}

    local bFirst = Facade.ConfigManager:GetUserBoolean(WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY_NOT_FIRST, true)
    if bFirst then
        Facade.ConfigManager:SetUserBoolean(WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY_NOT_FIRST, false)

        -- 默认全选
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            local tabIndex = warehouseTab:GetTabIndex()
            if tabIndex <= curDepositNum then
                self._curMultiTabIndexs[i] = true
            end
        end
    else
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        local lastSelectIndexs = Facade.ConfigManager:GetUserArray(WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY, {})
        for _, index in ipairs(lastSelectIndexs) do
            if index <= curDepositNum then
                self._curMultiTabIndexs[index] = true
            end
        end
    end
end

-- 全选相关逻辑
function WarehouseWithTab_HD:_IsSelectAllDeposit(bSelected)
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    if bSelected then
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            local tabIndex = warehouseTab:GetTabIndex()
            if tabIndex <= curDepositNum then
                self:_SetMultiSelected(tabIndex, true)
            end
        end
    else
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            local tabIndex = warehouseTab:GetTabIndex()
            if tabIndex <= curDepositNum then
                self:_SetMultiSelected(tabIndex, false)
            end
        end
    end
    self:_PostRefreshTabMultiSelected()
end

function WarehouseWithTab_HD:_SaveMultiSelectIndexs()
    local lastSelectIndexs = {}
    for index, value in pairs(self._curMultiTabIndexs) do
        if value then
            table.insert(lastSelectIndexs, index)
        end
    end
    Facade.ConfigManager:SetUserArray(WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY, lastSelectIndexs)
    Facade.ConfigManager:Flush()
end

local handle = nil
function WarehouseWithTab_HD:_OnExtArrangeBtnPress()
    Module.Inventory.Config.Events.evtWareHouseWithTabExtArrangeBtnPressed:Invoke()
    if handle then
        -- 双击
        self:_ReleaseClickHandle()
        self:_ShieldFuncByDoubleExtBtn(false)
        self:_OnExtArrangeBtnDoubleClick()
    else
        -- 当点击整理按钮判断是否双击的这段时间内需要屏蔽某些东西
        self:_ShieldFuncByDoubleExtBtn(true)
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHArrange)
        handle = Timer.DelayCall(0.2, self._OnExtArrangeBtnPress_DelayCheck, self)
    end
end

function WarehouseWithTab_HD:_CheckExtArrangeBtnPress(bChecked)

end

function WarehouseWithTab_HD:_ShieldFuncByDoubleExtBtn(bShield)
    if self._wtSellBtn.Visibility == ESlateVisibility.Collapsed or self._wtSellBtn.Visibility == ESlateVisibility.Hidden or self._bInExtArrangeMode then
        return
    end

    if bShield then
        self._wtSellBtn:HitTestInvisible()
    else
        self._wtSellBtn:Visible()
    end
end

function WarehouseWithTab_HD:_OnExtArrangeBtnPress_DelayCheck()
    -- 单击
    self:_ReleaseClickHandle()
    self:_ShieldFuncByDoubleExtBtn(false)
    self:_OnExtArrangeBtnClick()
end

function WarehouseWithTab_HD:_ReleaseClickHandle()
    if handle then
        Timer.CancelDelay(handle)
        handle = nil
    end
end

-- 双击整理当前页面
function WarehouseWithTab_HD:_OnExtArrangeBtnDoubleClick()
    -- 双击整理时需要判断是否处于整理状态，如果是整理状态则不相应
    if self._bInExtArrangeMode then return end
    Server.InventoryServer:DoSortSlots({self:GetCurrentShowDepositId()})
end

-- 单机展开整理选项
function WarehouseWithTab_HD:_OnExtArrangeBtnClick()
    if self._bInExtArrangeMode then
        if Module.CommonWidget:IsAuctionMode() or ItemOperaTool.CheckIsDIYCabinetPanel() then
            self:_CancelExtArrangeInAuction()
        else
            self:_CancelExtArrange()
        end
    else
        -- 检查仓库是否有道具
        local allDepositIds = Server.InventoryServer:GetAllDepositIds()
        local bHasItem = false
        for _, id in ipairs(allDepositIds) do
            local depositSlot = Server.InventoryServer:GetSlot(id)
            if #depositSlot:GetVisibleItems() > 0 then
                bHasItem = true
                break
            end
        end
        -- 检查是否安装扩容箱
        local bHasExt = Server.InventoryServer:GetCurDepositSlotNum() > 1

        if bHasItem then
            if not bHasExt then
                Server.InventoryServer:DoSortSlots({self:GetCurrentShowDepositId()})
            else
                if Module.CommonWidget:IsAuctionMode() or ItemOperaTool.CheckIsDIYCabinetPanel() then
                    self:_StartExtArrangeInAuction()
                else
                    self:_StartExtArrange()
                end
            end
        else
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseNothing2Manage)
        end
    end
end

function WarehouseWithTab_HD:_StartExtArrange()
    if self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = true
    self._bIsCanInExtManagementMode = false
    self:SetLonger(true)
    -- self:_CancelSell()

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(true)
    self:OnEnterASMOperation(true)
    Module.Inventory.Field:SetWHExtMode(self._bInExtArrangeMode)

    -- 进入整理模式时需要做的一些显隐操作
    Module.CommonBar:SetCurrencyVisible(false)
    self._wtWarehouse:HitTestInvisible()
    self._wtExtManageBtn:HitTestInvisible()
    self._wtSellBtn:HitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(false)
    self._wtExtArrangeBtn:ChangeState(true)
    self._wtWarehouse:SetJumpPanelInteraction(false)

    self:_InitStartupMultiSelectIndexs()

    --BEGIN MODIFICATION @ VIRTUOS : 打开整理界面时，尝试聚焦到 ArrangeBtn
    WidgetUtil.SetUserFocusToWidget(self._wtExtArrangeBtn, true)
    --END MODIFICATION

    -- 加载整理面板
    if not self._extArrangePanel then
        local arrangePanelWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseExtSub, self._slotExtArrange)
        local arrangeSubPanel = getfromweak(arrangePanelWeakIns)
        if arrangeSubPanel then
            self._extArrangePanel = arrangeSubPanel
            arrangeSubPanel:BindConfirmBtnCallBack(self._OnConfirmExtArrangeBtnClicked, self)
            arrangeSubPanel:BindCancelBtnCallBack(self._CancelExtArrange, self)
            arrangeSubPanel:BindSelectAllCheckBoxCallBack(self._IsSelectAllDeposit, self)
            arrangeSubPanel:SetSettingBtn(true)
            arrangeSubPanel:PlaySelfAnim(true)
        end
    end
    self._slotExtArrange:SelfHitTestInvisible()
    if self._extArrangePanel then
        self._extArrangePanel:PlaySelfAnim(true)
    end

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(true)

        if self._curMultiTabIndexs[i] then
            self:_SetMultiSelected(i, true)
        end
    end
    self:_SetExtSlotInteraction(true)
    self:_PostRefreshTabMultiSelected()

    --- BEGIN MODIFICATION @ yanmingjing
    InventoryConfig.Events.evtBottomBarSettingArrMode:Invoke()
    self:AddExtArrangeNavGroup()
    --- END MODIFICATION


    self:PlayWidgetAnim(self.WBP_WarehouseWithTab_kuorongxiang_in)
    Module.ItemDetail:CloseItemDetailPanel()
end

function WarehouseWithTab_HD:_CancelExtArrange()
    if not self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = false
    self._bIsCanInExtManagementMode = true
    self:_SaveMultiSelectIndexs()
    self:SetLonger(false)

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(false)
    self:OnEnterASMOperation(false)
    Module.Inventory.Field:SetWHExtMode(self._bInExtArrangeMode)

    -- 退出整理模式时需要的显隐操作
    -- self._wtSubBtnsPanel:SelfHitTestInvisible()
    -- self._wtMaskImgPanel:Collapsed()
    -- self._wtCollectionInBtn:Visible()
    self._wtWarehouse:SelfHitTestInvisible()
    self._wtExtManageBtn:Visible()
    self._wtSellBtn:Visible()
    self._wtExtArrangeBtn:ChangeState(false)
    self._wtWarehouse:SetJumpPanelInteraction(true)
    self:_SetCollectionRoomBtnInteraction(true)

    --BEGIN MODIFICATION @ VIRTUOS : 退出整理界面时，尝试聚焦到 ArrangeBtn
    WidgetUtil.SetUserFocusToWidget(self._wtExtArrangeBtn, true)

    if self._extArrangePanel and self._extArrangePanel._HideExtManagerTips then
        self._extArrangePanel:_HideExtManagerTips()
    end
    --END MODIFICATION

    Module.CommonBar:SetCurrencyVisible(true)

    -- 隐藏整理面板
    self._slotExtArrange:Collapsed()
    if self._extArrangePanel then
        self._extArrangePanel:PlaySelfAnim(false)
    end

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(false)
        if i == self._curDepositTabIndex then
            warehouseTab:SetSelected(true)
        end
    end
    self:_SetExtSlotInteraction(false)
    self:PlayWidgetAnim(self.WBP_WarehouseWithTab_kuorongxiang_out)
    -- BEGIN MODIFICATION @ yanmingjing
    self:RemoveExtArrangeNavGroup()
    -- END MODIFICATION
    Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
end

function WarehouseWithTab_HD:_StartExtArrangeInAuction()
    if self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = true
    self._bIsCanInExtManagementMode = false
    self:SetLonger(true)
    self._wtWarehouse:HitTestInvisible()
    self._wtExtArrangeBtn:ChangeState(true)
    self:SetArrange(true)

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(true)

    self._wtWarehouse:SetJumpPanelInteraction(false)
    self:_InitStartupMultiSelectIndexs()

    -- 加载整理面板
    if not self._extArrangePanel then
        local arrangePanelWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseExtSub, self._slotExtArrange)
        local arrangeSubPanel = getfromweak(arrangePanelWeakIns)
        if arrangeSubPanel then
            self._extArrangePanel = arrangeSubPanel
            arrangeSubPanel:BindConfirmBtnCallBack(self._OnConfirmExtArrangeBtnClicked, self)
            arrangeSubPanel:BindCancelBtnCallBack(self._CancelExtArrange, self)
            arrangeSubPanel:BindSelectAllCheckBoxCallBack(self._IsSelectAllDeposit, self)
            arrangeSubPanel:SetSettingBtn(false)
            arrangeSubPanel:PlaySelfAnim(true)
            arrangeSubPanel:SetTipsPanel(false)
        end
    end
    self._slotExtArrange:SelfHitTestInvisible()
    if self._extArrangePanel then
        self._extArrangePanel:PlaySelfAnim(true)
    end

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(true)

        if self._curMultiTabIndexs[i] then
            self:_SetMultiSelected(i, true)
        end
    end
    self:_SetExtSlotInteraction(true)
    self:_PostRefreshTabMultiSelected()

    InventoryConfig.Events.evtBottomBarSettingArrMode:Invoke()

    self:PlayWidgetAnim(self.WBP_WarehouseWithTab_kuorongxiang_in)
end

function WarehouseWithTab_HD:_OnEvtBottomBarSettingArrMode()
    -- 更新按键提示
    local curSelectedDepositNum = 0
    for index, bSelected in pairs(self._curMultiTabIndexs) do
        if bSelected then
            curSelectedDepositNum = curSelectedDepositNum + 1
        end
    end
    if Module.CommonWidget:IsAuctionMode() or ItemOperaTool.CheckIsDIYCabinetPanel() then
        -- auction BottomBar设置
        local summaryList = {}
        table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        table.insert(summaryList, {actionName = "SelectAll_Gamepad", func = self._extArrangePanel._ChangeCheckButtonState, caller = self._extArrangePanel ,bUIOnly = false, bHideIcon = false})
        table.insert(summaryList, {actionName = "Exit_Arrange", func = self._CancelExtArrangeInAuction, caller = self ,bUIOnly = false, bHideIcon = false})
        -- table.insert(summaryList, {actionName = "ConfirmArrange", func = self._OnConfirmExtArrangeBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})

        if curSelectedDepositNum > 0 then
        -- table.insert(summaryList, {actionName = "ConfirmArrange_Gamepad", func = self._OnConfirmExtArrangeBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
        end

        -- 【【ALL】【PC】【必现】【CCB接】交易行出售界面手柄往左移动到扩容箱列上后就无法回到右侧了，且无法移动到下方的整理按钮上】https://tapd.woa.com/tapd_fe/20421949/bug/detail/1020421949143845509
        -- she2不做L3仓库选项功能。这里创建导航组销毁时有问题，等待she3重构
        -- self:AddExtArrangeNavGroup()
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    else
        -- BottomBar设置
        local summaryList = {}
        table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
        table.insert(summaryList, {actionName = "WarehouseGoSetting_Gamepad", func = self._extArrangePanel.SimSettingBtnClickByGamepad, caller = self._extArrangePanel ,bUIOnly = false, bHideIcon = false})

       
        -- table.insert(summaryList, {actionName = "ConfirmArrange", func = self._OnConfirmExtArrangeBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
        
        if curSelectedDepositNum > 0 then
        -- table.insert(summaryList, {actionName = "ConfirmArrange_Gamepad", func = self._OnConfirmExtArrangeBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        table.insert(summaryList, {actionName = "SelectAll_Gamepad", func = self._extArrangePanel._ChangeCheckButtonState, caller = self._extArrangePanel ,bUIOnly = false, bHideIcon = false})
        table.insert(summaryList, {actionName = "Common_ToggleTip", func = self._extArrangePanel.ToggleTipsByGamepad, caller = self._extArrangePanel ,bUIOnly = false, bHideIcon = false})
        table.insert(summaryList, {actionName = "Exit_Arrange", func = self._CancelExtArrange, caller = self ,bUIOnly = false, bHideIcon = false})
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    end
end

function WarehouseWithTab_HD:_CancelExtArrangeInAuction()
    if not self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = false
    self._bIsCanInExtManagementMode = true
    self:_SaveMultiSelectIndexs()
    self:SetLonger(false)
    self._wtExtArrangeBtn:ChangeState(false)
    self:SetArrange(false)

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(false)

    -- 退出整理模式时需要的显隐操作
    self._wtWarehouse:SetJumpPanelInteraction(true)
    self._wtWarehouse:SelfHitTestInvisible()

    -- 隐藏整理面板
    self._slotExtArrange:Collapsed()
    if self._extArrangePanel then
        self._extArrangePanel:PlaySelfAnim(false)
    end

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(false)
        if i == self._curDepositTabIndex then
            warehouseTab:SetSelected(true)
        end
    end
    self:_SetExtSlotInteraction(false)
    self:PlayWidgetAnim(self.WBP_WarehouseWithTab_kuorongxiang_out)

    self:RemoveExtArrangeNavGroup()
    -- Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
    Module.CommonBar:SetBottomBarTempInputSummaryList({}, nil, true)
end

function WarehouseWithTab_HD:_OnConfirmExtArrangeBtnClicked()
    local slots2Sort = {}
    for index, bSelected in pairs(self._curMultiTabIndexs) do
        if bSelected then
            local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
            table.insert(slots2Sort, depositId)
        end
    end
    
    if #slots2Sort > 0 then
        Server.InventoryServer:DoSortSlots(slots2Sort)
    end


    -- if not Server.InventoryServer:GetHasSort() then
    --     -- 如果没有整理过，会弹一个前往设置的TopHint
    --     self:_ShowGoToSettingHint()
    -- end

    if Module.CommonWidget:IsAuctionMode() or ItemOperaTool.CheckIsDIYCabinetPanel() then
        self:_CancelExtArrangeInAuction()
    else
        self:_CancelExtArrange()
    end
end

function WarehouseWithTab_HD:_OpenExtSettingTips()
    local text1 = Module.Inventory.Config.Loc.AutoExtText1
    local text2 = Module.Inventory.Config.Loc.AutoExtText2
    local tipsData = {{contentType = ETipsContentType.OnlyText, desc = text1}, {contentType = ETipsContentType.OnlyText, desc = text2}}
    local title = Module.Inventory.Config.Loc.AutoExtText
    local handle = Module.ItemDetail:OpenItemCommonMiniTipsType2(self._wtShowExtSettingTipsBtn, self._wtShowExtSettingTipsBtn, title, nil ,tipsData, true, nil, nil, nil, nil, true)
    if handle == nil then
        return
    end
end

function WarehouseWithTab_HD:_OnArrangeSettingBtnClicked()
    -- Module.Inventory:ShowExtArrangeWindow()
    Module.SystemSetting:ShowSystemSettingMainView(Module.SystemSetting.Config.ESystemSettingPanel.SystemSetting, true)
end

function WarehouseWithTab_HD:_OnMaskImgClicked()
    self:_CancelExtArrange()
    return {}
end

function WarehouseWithTab_HD:_OnWishListBtnClicked()
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        Facade.UIManager:AsyncShowUI(UIName2ID.WishPopView_HD)
    else
        Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.TheCurrentPageCannotOpenTheFavoritesList)
    end
end

function WarehouseWithTab_HD:_OnCollectionInBtnClicked()
    -- logerror("SunBillowsTestForCollectionInButton")
    Module.Collection:ShowMainPanel(3)
end

--endregion
-----------------------------------------------------------------------



function WarehouseWithTab_HD:_OnFilterScrolled(finalOffset)
    local firstFilterTab = #self._filterTabs > 0 and self._filterTabs[1] or nil
    if firstFilterTab then
        local firstTabGeometry = firstFilterTab:GetCachedGeometry()
        local topLeftAbsPos = firstTabGeometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.TOP_LEFT_VECTOR)

        local filterScrollBoxGeometry = self._wtFilterScrollBox:GetCachedGeometry()
        local topLeftLocalPos = filterScrollBoxGeometry:AbsoluteToLocal(topLeftAbsPos)

        -- log(topLeftLocalPos.X, topLeftLocalPos.Y)
        if topLeftLocalPos.Y > WarehouseWithTab_HD.FILTER_TAB_FOLD_THRESHOLD then
            self._bShouldFoldFilterTab = true
        end
    end
end

function WarehouseWithTab_HD:_OnFilterBtnClicked(bIsChecked)
    self:_CancelSell()

    if bIsChecked == nil then
        bIsChecked = not self._bFilterBtnToggle
    end

    self._bFilterBtnToggle = bIsChecked
    -- self._wtFilterBtnBp:SetCppValue("BpSelected", bIsChecked)
    -- self._wtFilterBtnBp:BP_SetStyle()

    if bIsChecked then
        self:_PlayFilterAnim(true)
        
        self._wtWarehouse:ShowInListMode()
        
        self:_UpdateSellBtnVisibility(false)
        self:ShowExtArrangeBtn(false)
    else
        self:_PlayFilterAnim(false)

        self._wtWarehouse:ShowInGridMode()

        self:_UpdateSellBtnVisibility(true)
        self:ShowExtArrangeBtn(true)
    end
end

function WarehouseWithTab_HD:_PlayFilterAnim(bForward)
    if not self._filterAimeTime then
        self._filterAnimTime = self.ClassShow_Anim:GetEndTime()
    end
    Timer.CancelDelay(self._filterAnimHandle)
    self._filterAnimHandle = nil

    if bForward then
        self._wtFilterPanel:SelfHitTestInvisible()
        self:PlayWidgetAnim(self.ClassShow_Anim)
    else
        local function fDelayHideFilterPanel()
            self._wtFilterPanel:Collapsed()
        end
        self._filterAnimHandle = Timer.DelayCall(self._filterAnimTime, fDelayHideFilterPanel)
        self:PlayWidgetAnim(self.ClassShow_Anim, 1, EUMGSequencePlayMode.Reverse)
    end
end

function WarehouseWithTab_HD:_OnSellBtnClicked(bForceEnterSellMode)
    local bInSellMode = Module.CommonWidget:GetIsItemSellMode()
    if bForceEnterSellMode == true or not bInSellMode then
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHSell)

        -- 检查是否有道具可以出售
        local slotTypes2Check = Server.InventoryServer:GetAllDepositIds()
        table.append(slotTypes2Check, {
            ESlotType.MainWeaponLeft,
            ESlotType.MainWeaponRight,
            ESlotType.Helmet,
            ESlotType.BreastPlate,
            ESlotType.BagSpaceContainer,
            ESlotType.SafeBoxContainer,
            ESlotType.KeyChainContainer,
        })
        local bHasItem2Sell = false
        for _, id in ipairs(slotTypes2Check) do
            local depositSlot = Server.InventoryServer:GetSlot(id)
            if #depositSlot:GetVisibleItems() > 0 then
                bHasItem2Sell = true
                break
            end
        end
        if not bHasItem2Sell then
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseNothing2Sell)
        else
            self:_StartSell()
        end
    elseif bForceEnterSellMode == false or bInSellMode then
        self:_CancelSell()
    end
end

function WarehouseWithTab_HD:CheckSellBox(bIsChecked)
    self:_OnSellBtnClicked(bIsChecked)
end

function WarehouseWithTab_HD:_StartSell()
    if Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode then
        return
    end
    Module.CommonWidget:SetItemOperMode(EItemOperaMode.SellMode)
    self._bIsCanInExtManagementMode = false
    self._wtWarehouse:SetSellGridHeight(true)

    self._totalPrice = 0
    self._sameItemNum = {} -- id : num
    ---@type ItemBase[]
    self._otherSameItems = {}

    self:PlayWidgetAnim(self.SellSubPanel_Anim_in)

    -- 出售界面时对某些按钮进行屏蔽
    self._wtExtManageBtn:HitTestInvisible()
    self._wtExtArrangeBtn:HitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(false)
    self._wtSellBtn:ChangeState(true)
    self._wtWarehouse:SetJumpPanelInteraction(false)

    --BEGIN MODIFICATION @ VIRTUOS : 打开出售界面时，尝试聚焦到 SellBtn
    -- WidgetUtil.SetUserFocusToWidget(self._wtSellBtn, true)
    --END MODIFICATION

    -- 加载出售面板
    if not self._sellSubPanel then
        local extPanelWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseSellSub, self._slotSellSubSlot)
        local extSubPanel = getfromweak(extPanelWeakIns)
        if extSubPanel then
            self._sellSubPanel = extSubPanel
            extSubPanel:BindConfirmBtnCallBack(self._OnConfirmSellBtnClick, self)
            extSubPanel:BindCancelBtnCallBack(self._OnCancelSellBtnClick, self)
            --BEGIN MODIFICATION @ VIRTUOS : 绑定输入提示变化回调
            extSubPanel:BindSetInputSummaryCallBack(self._OnSetSellInputSummary, self)
            --END MODIFICATION
        end
    end
    self._slotSellSubSlot:SelfHitTestInvisible()
    -- self._sellSubPanel:RefreshSubPanel()
    self._sellSubPanel:IsSelectionItem(false)
    self._sellSubPanel:RefreshSellPrices(self._totalPrice)

    self._wtWarehouse:SetInMultiSellMode(true)
    InventoryConfig.Events.evtEnterSellMode:Invoke(true)

    self:_SetExtSlotInteraction(true)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInSellMode(true)
    end
    Module.ItemDetail:CloseItemDetailPanel()
    -- 尝试聚焦到当前slot的第一个控件
    self._wtWarehouse:TryFocusToFirstItem()

    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)

    -- self:AddLuaEvent(InventoryConfig.Events.BottomBarSetting, self._SetSellModeInputSummary, self)
    -- BottomBar设置
    
end

function WarehouseWithTab_HD:SetSellModeInputSummary(inputSummary)
    local summaryList = {}
    table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
    if inputSummary then
        for _, summary in pairs(inputSummary) do
            table.insert(summaryList, summary)
        end
    end
    table.insert(summaryList, {actionName = "Exit_Sell", func = self._CancelSell, caller = self ,bUIOnly = false, bHideIcon = false})
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    -- table.insert(summaryList, {actionName = "WareHouseSellItem", func = self._OnConfirmSellBtnClick, caller = self ,bUIOnly = false, bHideIcon = false})
    if self._sellHandle == nil then
        self._sellHandle = inputMonitor:AddDisplayActionBinding("WareHouseSellItem", EInputEvent.IE_Pressed, self._OnConfirmSellBtnClick,self, EDisplayInputActionPriority.UI_Stack)
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end

function WarehouseWithTab_HD:_CancelSell()
    -- if Module.CommonWidget:GetItemOperMode() ~= EItemOperaMode.SellMode then
    --     return
    -- end
    self._bIsCanInExtManagementMode = true
    Module.CommonWidget:SetItemOperMode(EItemOperaMode.Default)
    self._wtWarehouse:SetSellGridHeight(false)

    self._totalPrice = 0
    self._sameItemNum = {} -- id : num
    ---@type ItemBase[]
    self._otherSameItems = {}

    -- 出售界面时对某些按钮进行屏蔽
    self._wtExtManageBtn:Visible()
    self._wtExtArrangeBtn:Visible()
    self:_SetCollectionRoomBtnInteraction(true)
    self._slotSellSubSlot:Collapsed()
    self._wtWarehouse:SetJumpPanelInteraction(true)
    self._wtSellBtn:ChangeState(false)

    self._wtWarehouse:SetInMultiSellMode(false)
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInSellMode(false)
    end

    self:_HideTopHint()
    self:_SetExtSlotInteraction(false)
    InventoryConfig.Events.evtEnterSellMode:Invoke(false)
    self:RemoveLuaEvent(InventoryConfig.Events.evtBottomBarSetting, self)
    Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    if self._sellHandle then
        inputMonitor:RemoveDisplayActoinBingingForHandle(self._sellHandle)
        self._sellHandle = nil
    end
    -- //TODO 如果退出聚焦不了，则尝试用INV NAV MGR的封装
    WidgetUtil.SetUserFocusToWidget(self._wtSellBtn, true)
end

function WarehouseWithTab_HD:_OnCancelSellBtnClick()
    self:_CancelSell()
end

function WarehouseWithTab_HD:_OnConfirmSellBtnClick()
    -- 如果没有选中任何道具，则返回
    if #Module.CommonWidget:GetSelectedItems() <= 0 then
        return
    end
    local itemsToSell = Module.CommonWidget:GetSelectedItems()

    local fOnSellResultCallback =
        CreateCallBack(
        function(ins, result, res)
            if result then
                ins._mapCurrency2SellPrice = {}
                ins:_CancelSell()
            else
            end
        end,
        self
    )

    ItemOperaTool.DoSellItems(itemsToSell, fOnSellResultCallback)
end

--BEGIN MODIFICATION @ VIRTUOS : 
function WarehouseWithTab_HD:_OnSetSellInputSummary(bSelected)
    -- 这个先不用了，vts版本的按键映射与机制和现版本需求不符
    local summaryList = {}
    table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
    table.insert(summaryList, {actionName = "Exit_Sell", func = self._CancelSell, caller = self ,bUIOnly = false, bHideIcon = false})
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    if #Module.CommonWidget:GetSelectedItems() > 0 then
    -- table.insert(summaryList, {actionName = "WareHouseSellItem", func = self._OnConfirmSellBtnClick, caller = self ,bUIOnly = false, bHideIcon = false})
        if self._sellHandle == nil then
            self._sellHandle = inputMonitor:AddDisplayActionBinding("WareHouseSellItem", EInputEvent.IE_Pressed, self._OnConfirmSellBtnClick,self, EDisplayInputActionPriority.UI_Stack)
        end
    else
        if self._sellHandle then
            inputMonitor:RemoveDisplayActoinBingingForHandle(self._sellHandle)
            self._sellHandle = nil
        end
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end
--END MODIFICATION

function WarehouseWithTab_HD:_ShowSelectAllHint(item)
    local tip = string.format(InventoryConfig.Loc.WarehouseTopHint_SelectAll, item.name)
    local btnText = InventoryConfig.Loc.WarehouseTopHintBtnText_SelectAll
    local fConfirmCallback = function()
        local allMatchItems = InventoryLogic.GetItemCanSellInMultiModeById(item.id)

        for _, matchItem in ipairs(allMatchItems) do
            if not Module.CommonWidget:CheckItemMultiSelected(matchItem) then
                Module.CommonWidget:SetItemMultiSelected(matchItem, true, true)
            end
        end
    end
    self:_ShowTopHint(tip, btnText, fConfirmCallback)
end

function WarehouseWithTab_HD:_ShowGoToSettingHint()
    local tip = InventoryConfig.Loc.WarehouseTopHint_GoToSetting
    local btnText = InventoryConfig.Loc.WarehouseTopHintBtnText_GoToSetting
    local function fOnConfirm()
        Module.SystemSetting:ShowSystemSettingMainView(Module.SystemSetting.Config.ESystemSettingPanel.SystemSetting, true)
    end

    self:_ShowTopHint(tip, btnText, fOnConfirm)
end

function WarehouseWithTab_HD:_ShowAutoSortHint(extItem)
    local extItemId = extItem.id
    local extBoxDesc = ItemConfigTool.GetExtentionBoxDescRowById(extItemId)
    if not extBoxDesc then
        return
    end

    local itemSubType = ItemHelperTool.GetSubTypeById(extItemId)
    local subText = InventoryConfig.MapExtType2FirstTimeEquipTip[itemSubType]
    if not subText then
        return
    end

    local tip = string.format(InventoryConfig.Loc.WarehouseTopHint_AutoSortAfterEquipExtItem, subText)
    local btnText = InventoryConfig.Loc.WarehouseTopHintBtnText_AutoSortAfterEquipExtItem

    local function fOnConfirm()
        local refExtId = extItem.InSlot.SlotType
        local refDepositId = Server.InventoryServer:GetDepositIdByExtId(refExtId)

        local depositSortClassTable = Facade.TableManager:GetTable("DepositSortClass")
        local forceExtFirstCls = {}
        for k, row in pairs(depositSortClassTable) do
            table.insert(forceExtFirstCls, tonumber(k))
        end

        Server.InventoryServer:DoSortSlots({ESlotType.MainContainer, refDepositId}, nil, forceExtFirstCls)
    end

    self:_ShowTopHint(tip, btnText, fOnConfirm)
end

function WarehouseWithTab_HD:_ShowTopHint(hintText, btnText, btnCallback, showDuration)
    local geometry = self._wtMainCanvas:GetCachedGeometry()
    local size = geometry:GetLocalSize()

    if self._topHint and not hasdestroy(self._topHint) then
        self._topHint:InitTopHint(hintText, btnText, btnCallback, showDuration)
    elseif not self._selectAllPanelHandle or hasdestroy(self._topHint) then
        ---@param selfView WarehouseWithTab_HD
        ---@param selectAllPanel WarehouseTopHint
        local function fOnSelectAllPanelLoaded(selfView, selectAllPanel)
            selfView._topHint = selectAllPanel
            selfView._topHint:InitTopHint(hintText, btnText, btnCallback, showDuration)
        end
        self._selectAllPanelHandle =
            Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseSelectAllHint_PC, fOnSelectAllPanelLoaded, self)
    end
end

function WarehouseWithTab_HD:_HideTopHint()
    if self._topHint then
        self._topHint:AnimHide()
    elseif self._selectAllPanelHandle then
        self._selectAllPanelHandle:Release()
        self._selectAllPanelHandle = nil
    end
end

function WarehouseWithTab_HD:_OnPostItemMultiSelected(item, bSelected, bFromOnClick)
    local bStillHasItem = false
    if item and Server.InventoryServer:GetItemByGid(item.gid) then
        bStillHasItem = true
    end

    if item and item.aginType ~= 0 then
        local curTime = TimeUtil:GetCurrentTime()
        local endTime = item.aginBeginTime + item.aginDuration
        if curTime < endTime then
            return Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.TimeItemCantSell)
        end
    end

    if not Module.CommonWidget:IsInMultiSelectedMode() then
        return
    end

    -- 某些原因可能会走入这里导致totalprice为nil从而引发luaerror。所以需要进行设初始值
    self._totalPrice = self._totalPrice and self._totalPrice or 0
    if not item then
        -- self._mapCurrency2SellPrice = {}
        for _, selectedItem in ipairs(Module.CommonWidget:GetSelectedItems()) do
            local currencyClientType = Server.ShopServer:GetRecycleCurrencyTypeByItem(selectedItem)
            self._totalPrice = self._totalPrice + selectedItem:GetTotalMallSellPrice()
        end
    else
        local currencyClientType = Server.ShopServer:GetRecycleCurrencyTypeByItem(item)
        local bCurrentSelected = Module.CommonWidget:CheckItemMultiSelected(item)
        if bCurrentSelected then
            if not self._sameItemNum[item.id] then
                self._sameItemNum[item.id] = 0
            end
            self._sameItemNum[item.id] = self._sameItemNum[item.id] + 1
            self:_CheckSameItemClick(item)

            self._totalPrice = self._totalPrice + item:GetTotalMallSellPrice()
        else
            if not self._sameItemNum[item.id] then
                self._sameItemNum[item.id] = 0
            end
            self._sameItemNum[item.id] = self._sameItemNum[item.id] - 1
            if self._sameItemNum[item.id] == 0 then
                self._sameItemNum[item.id] = nil
            end

            self._totalPrice = self._totalPrice - item:GetTotalMallSellPrice()
        end
    end

    local allSelectedItems = Module.CommonWidget:GetSelectedItems()

    if #allSelectedItems == 0 then
        if self._sellSubPanel then
            self._sellSubPanel:IsSelectionItem(false)
        end
    else
        if self._sellSubPanel then
            self._sellSubPanel:IsSelectionItem(true)
        end
    end
    if self._sellSubPanel then
        self._sellSubPanel:RefreshSellPrices(self._totalPrice)
    end
end

function WarehouseWithTab_HD:_OnExtManageDragSortStart(bStart)
    --[[
    local txt = ""
    if bStart then
        txt = InventoryConfig.Loc.DragUpAndDownToSort
    else
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        if curDepositNum == 1 then
            txt = InventoryConfig.Loc.ClickRightEmptySlotToEquipExtItem
        else
            txt = InventoryConfig.Loc.SelectExtItemToManage
        end
    end
    self._wtExtSelectNothingPanel:SetCppValue("Text", txt)
    self._wtExtSelectNothingPanel:BP_Set_Type()
    ]]--
    if bStart then
        Module.CommonBar:SetBottomBarTempInputSummaryList({}, true, true)
    else
        local summaryList = {}
        table.insert(summaryList, {actionName = "EndManage", func = self._EndExtManagement, caller = self ,bUIOnly = false, bHideIcon = false})
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    end
end

function WarehouseWithTab_HD:_CheckSameItemClick(item)
    if self._sameItemNum[item.id] >= MAX_SAME_ITEM_CLICK_NUM then
        local sameItems = InventoryLogic.GetItemCanSellInMultiModeById(item.id)
        if #sameItems >= MIN_SAME_ITEM_IN_DEPOSIT then
            self._otherSameItems = {}
            for _, sameItem in ipairs(sameItems) do
                if not Module.CommonWidget:CheckItemMultiSelected(sameItem) then
                    table.insert(self._otherSameItems, sameItem)
                end
            end
            if #self._otherSameItems > 0 then
                self:_ShowSelectAllHint(item)
            end
        end
    end
end

function WarehouseWithTab_HD:_OnSellAllBtnClick()
    for _, item in ipairs(self._otherSameItems) do
        Module.CommonWidget:SetItemMultiSelected(item, true, true)
    end
    self:_HideTopHint()
end

function WarehouseWithTab_HD:_OnItemMove(itemMoveInfo, changeNum)
    self:_RefreshFilterTabStatus()

    -- 在仓库扩容箱安装成功后应该切换到打开扩容箱的页面
    -- 在仓库卸下扩容箱后应该切换到主仓库并定位到卸下的扩容箱
    if (itemMoveInfo.OldLoc and itemMoveInfo.OldLoc.ItemSlot:IsExtendSlot()) then
        -- Remove
        self:_InitDepositTabList()
        local warehouse = self:GetWarehouseWidget()
        local warehouseSlotView = warehouse:GetWarehouseSlotView()

        local targetItem = itemMoveInfo.item

        -- 本身当前帧SlotView会刷新，需要延迟一下
        local fDelayScrollToItem = function()
            warehouseSlotView:ScrollToItem(targetItem, true, true)
        end
        self._delayScrollToItemHandle = Timer.DelayCall(0.1, fDelayScrollToItem)
        -- 退出扩容箱整理设置
        self:_EndExtManagement()
    elseif (itemMoveInfo.NewLoc and itemMoveInfo.NewLoc.ItemSlot:IsExtendSlot()) then
        -- Add
        local targetExtSlotType = itemMoveInfo.NewLoc.ItemSlot.SlotType
        local targetDepositId = Server.InventoryServer:GetDepositIdByExtId(targetExtSlotType)
        local targetIndex = Server.InventoryServer:GetIndexByDepositId(targetDepositId)
        self:_InitDepositTabList(targetIndex)
    end

    local index
    if itemMoveInfo.OldLoc and itemMoveInfo.NewLoc then
        ---@type ItemSlot
        local oldSlot = itemMoveInfo.OldLoc.ItemSlot
        ---@type ItemSlot
        local newSlot = itemMoveInfo.NewLoc.ItemSlot
        if newSlot ~= oldSlot and newSlot:IsDepositorySlot() and newSlot.SlotType ~= oldSlot.SlotType then
            index = Server.InventoryServer:GetIndexByDepositId(newSlot.SlotType)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                if not tab then
                    log("depositTab is nil, please check self._allDepositTabs")
                    return
                end
                tab:HighlightTab(1, false)
                -- InventoryConfig.Events.evtJumpToExtSlot:Invoke(index)
                self:_SelectDepositTab(index, itemMoveInfo.item)
            end
        elseif itemMoveInfo.Reason == PropChangeType.Modify and itemMoveInfo.bIncreaseOrDecrease then
            -- 如果是modify，则根据道具数量判断是否为放入
            index = Server.InventoryServer:GetIndexByDepositId(newSlot.SlotType)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                if not tab then
                    log("depositTab is nil, please check self._allDepositTabs")
                    return
                end
                tab:HighlightTab(1, false)
                self:_SelectDepositTab(index, itemMoveInfo.item)
            end
        end
    elseif itemMoveInfo.NewLoc then
        local newSlot = itemMoveInfo.NewLoc.ItemSlot
        if newSlot:IsDepositorySlot() then
            index = Server.InventoryServer:GetIndexByDepositId(newSlot.SlotType)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                if not tab then
                    log("depositTab is nil, please check self._allDepositTabs")
                    return
                end
                tab:HighlightTab(1, false)
                self:_SelectDepositTab(index, itemMoveInfo.item)
            end
        end
    end
end

-- 存不同扩容箱的数量
function WarehouseWithTab_HD:_SaveExpansionNum()
    local allDepositIds = Server.InventoryServer:GetAllDepositIds()

    for _, id in ipairs(allDepositIds) do
        local slot = Server.InventoryServer:GetSlot(id)
        self._depositNum[id] = slot and slot:GetItemsNum()
    end
end
-- 将不同扩容箱的数量进行对比
function WarehouseWithTab_HD:_CompareExpansionNum()
    local allDepositIds = Server.InventoryServer:GetAllDepositIds()
    local changeNum

    for _, id in ipairs(allDepositIds) do
        local slot = Server.InventoryServer:GetSlot(id)
        if not self._depositNum[id] then
            self._depositNum[id] = 0
        end
        self._depositNum[id] = slot:GetItemsNum() - self._depositNum[id]
    end

    for depositId, num in pairs(self._depositNum) do
        local index = Server.InventoryServer:GetIndexByDepositId(depositId)
        if index > 0 then
            local tab = self._allDepositTabs[index]
            tab:HighlightTab(num, false)
        end
    end

end

function WarehouseWithTab_HD:_OnItemMoveBatch(itemMoveInfos)
    local bAddNewExtItem = false
	local extItem
	for itemGID, itemMoveInfo in pairs(itemMoveInfos) do
		---@type ItemBase
		local item = itemMoveInfo.item

		---@type ItemLocation
		local newLoc = itemMoveInfo.NewLoc
		local targetSlot = newLoc and newLoc.ItemSlot or nil

		---@type ItemLocation
		local srcLoc = itemMoveInfo.OldLoc
		local sourceSlot = srcLoc and srcLoc.ItemSlot or nil

		if targetSlot and targetSlot:IsExtendSlot() then
			extItem = item
			bAddNewExtItem = true
		elseif sourceSlot and sourceSlot:IsExtendSlot() then
			bAddNewExtItem = false
			break
		end
	end

	if bAddNewExtItem and extItem then
		local itemSubType = ItemHelperTool.GetSubTypeById(extItem.id)
		if itemSubType ~= EExtensionType.Item then
			-- 玩家每次安装一个新的，非物品箱的扩容箱，并且不是替换，都触发一次弹窗提示：
			self:_ShowAutoSortHint(extItem)
		end
	end
end

---@param res pb_CSDepositSortMultiplePosRes
function WarehouseWithTab_HD:_OnPostSortMultiPos(res, capacityChanges)
    if res.result == 0 then
        for depositId, changeNum in pairs(capacityChanges) do
            local index = Server.InventoryServer:GetIndexByDepositId(depositId)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                -- tab:HighlightTab(#res.changes.prop_changes, true)
                if tab then
                    tab:HighlightTab(changeNum, true)
                end
            end
        end
    end

    -- Full refresh
    self._wtWarehouse:ShowInGridMode()
end

function WarehouseWithTab_HD:_OnNotifyExtOrderChanged()
    self:_InitDepositTabList(self._curDepositTabIndex)
end

function WarehouseWithTab_HD:_WarehouseArrangeFaild()
    -- local slots2Sort = {}
    -- for index, bSelected in pairs(self._curMultiTabIndexs) do
    --     if bSelected then
    --         local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
    --         table.insert(slots2Sort, depositId)
    --     end
    -- end
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    local lastSelectIndexs = Facade.ConfigManager:GetUserArray(WarehouseWithTab_HD.LAST_SELECT_INDEXS_KEY, {})

    if curDepositNum == 1 then
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.ArrangeFaildTips)
    elseif #lastSelectIndexs < curDepositNum then
        -- 有可用扩容且未勾选时
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SortSlotFailTips1)
    else
        -- -- 是否开启了"跨类别换行整理"
        -- if Server.InventoryServer:GetSortStype() == eDepositSortStyle.class_first then
        --     -- 是否勾选弹窗中的不再提醒
        --     if self._isNomoreTip==true then
        --         LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SortSlotFail)
        --     else
        --         -- 跳出弹窗
        --         Facade.UIManager:AsyncShowUI(UIName2ID.ArrangePopWindow, nil, nil)
        --     end
        -- else
        --     -- 提示空间不足
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SortSlotFailTips2)
        -- end
    end
end

function WarehouseWithTab_HD:OnPopWindowStageChange()
    self._isNomoreTip=not self._isNomoreTip
end

---@param res pb_CSDepositOperateExtensionBoxRes
function WarehouseWithTab_HD:_OnExtensionBoxRes(res)
    if res.result == 0 then
        -- self:_InternalInitWarehouseExtList(0)

        if res.operation == ExtensionOpType.OpreateUninstall then
            -- 检查物品去向
            local changes = res.changes.prop_changes
            local bToMainWarehouse = false
            local bToExtWarehouse = false
            for _, change in ipairs(changes) do
                local src = change.src.pos
                local target = change.dest.pos

                if src > ESlotType.DepositoryStart and src < ESlotType.DepositoryEnd then
                    if bToMainWarehouse and bToExtWarehouse then
                        break
                    elseif not bToMainWarehouse and target == ESlotType.MainContainer then
                        bToMainWarehouse = true
                    elseif not bToExtWarehouse and target > ESlotType.MainContainer and target < ESlotType.DepositoryEnd then
                        bToExtWarehouse = true
                    end
                end
            end

            local tip
            if bToMainWarehouse and bToExtWarehouse then
                tip = InventoryConfig.Loc.UnequipExtItemSuccessHintSuffix_AllWarehouse
            elseif bToMainWarehouse then
                tip = InventoryConfig.Loc.UnequipExtItemSuccessHintSuffix_MainWarehouse
            elseif bToExtWarehouse then
                tip = InventoryConfig.Loc.UnequipExtItemSuccessHintSuffix_ExtWarehouse
            else

            end
            Module.CommonTips:ShowSimpleTip(tip)
        end

    else
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.UnequipExtItemFailHint)
    end
end

function WarehouseWithTab_HD:AutoArrangeWarehouse()
    -- 只有进入仓库时才会自动整理，其他的例如交易行或者结算转移界面都不会触发自动整理
	local curStackUi = Facade.UIManager:GetCurrentStackUI()
    if curStackUi and curStackUi.UINavID == UIName2ID.WarehouseMain and ItemOperaTool.CheckRunWarehouseLogic() then
        local allDepositIds = Server.InventoryServer:GetAllDepositIds()
        Server.InventoryServer:DoSortSlots(allDepositIds, nil, nil, true)
    end
end

-- 进入整理、出售、扩容箱管理界面时对withtab进行某些设置
function WarehouseWithTab_HD:OnEnterASMOperation(bEnter)
    self._wtMaskPanel:SetRenderOpacity(bEnter and 0.1 or 1)
end

-- 进入收藏室按钮点击
function WarehouseWithTab_HD:_OnCollectionHallBtnClicked()
    -- Module.BlackSite.JumpToMainPanelByUIIdx, self, BlackSiteDefine.EBlackSiteEntranceType.CollectionRoom
    local function fOnConfirmCallback()
        Module.BlackSite:JumpToMainPanelByUIIdx(BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement, true)
    end
    local function fOnCancelCallback()
        return
    end
    Module.CommonTips:ShowConfirmWindow(InventoryConfig.Loc.GoToCollectionRoomTips, fOnConfirmCallback, fOnCancelCallback, InventoryConfig.Loc.CancelText, InventoryConfig.Loc.ConfirmText)
end

--endregion
--==================================================

-----------------------------------------------------------------------
--region 仓库按钮tips

function WarehouseWithTab_HD:ShowBtnTips(btnWidget, TipsAnchor)

	self:HideBtnTips(btnWidget, TipsAnchor)

	local contents = {}
	table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = self._BtnToTipsText[btnWidget]}})

    self._BtnToTipsHandle[btnWidget] = Module.CommonTips:ShowAssembledTips(contents, TipsAnchor)
end

function WarehouseWithTab_HD:HideBtnTips(btnWidget, TipsAnchor)
	if self._BtnToTipsHandle[btnWidget] then
        Module.CommonTips:RemoveAssembledTips(self._BtnToTipsHandle[btnWidget], TipsAnchor)
        self._BtnToTipsHandle[btnWidget] = nil
    end
end

function WarehouseWithTab_HD:_ShowCollectionBtnTips()
    self:ShowBtnTips(self._wtCollectionInBtn, self._wtDFTipsAnchor_1)
end
function WarehouseWithTab_HD:_HideCollectionBtnTips()
    self:HideBtnTips(self._wtCollectionInBtn, self._wtDFTipsAnchor_1)
end

function WarehouseWithTab_HD:_ShowWishListBtnTips()
    self:ShowBtnTips(self._wtWishListBtn, self._wtDFTipsAnchor_2)
end
function WarehouseWithTab_HD:_HideWishListBtnTips()
    self:HideBtnTips(self._wtWishListBtn, self._wtDFTipsAnchor_2)
end

function WarehouseWithTab_HD:_ShowExtBtnTips()
    self:ShowBtnTips(self._wtExtArrangeBtn, self._wtDFTipsAnchor_3)
end
function WarehouseWithTab_HD:_HideExtBtnTips()
    self:HideBtnTips(self._wtExtArrangeBtn, self._wtDFTipsAnchor_2)
end

function WarehouseWithTab_HD:_ShowSellBtnTips()
    self:ShowBtnTips(self._wtSellBtn, self._wtDFTipsAnchor_4)
end
function WarehouseWithTab_HD:_HideSellBtnTips()
    self:HideBtnTips(self._wtSellBtn, self._wtDFTipsAnchor_4)
end

function WarehouseWithTab_HD:_ShowExtManagerBtnTips()
    local data = Module.Inventory.Config.Loc.ExtManagerBtnText
    self:_OnHovered(data, self._wtDFTipsAnchor)
    -- self._hTips = Module.CommonTips:ShowTipsFromTemplate(self._wtDFTipsAnchor)
end
function WarehouseWithTab_HD:_HideExtManagerBtnTips()
    self:_OnUnhovered()
    -- Module.CommonTips:RemoveTipsFromTemplate(self._hTips, self._wtDFTipsAnchor)
end

function WarehouseWithTab_HD:_OnHovered(data, tipsAnchor)
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, tipsAnchor)
        self.hoverHandle = nil
    end

    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = data}
    }}, tipsAnchor)
end

function WarehouseWithTab_HD:_OnUnhovered()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, tipsAnchor)
        self.hoverHandle = nil
    end
end

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function WarehouseWithTab_HD:RegisterExtManageNavGroup()
    self:RemoveExtManageNavGroup()

    if not self._NavGroupExtManage then
        self._NavGroupExtManage = WidgetUtil.RegisterNavigationGroup(self._wtExtVerticalBox, self, "Hittest")
        if self._NavGroupExtManage then
            self._NavGroupExtManage:AddNavWidgetToArray(self._wtExtVerticalBox)
            self._NavGroupExtManage:SetScrollRecipient(self._wtDepositTabScrollBox)
            local ExtManageNavStrategy = self._NavGroupExtManage:GetOwnerNavStrategy()
            if ExtManageNavStrategy then
                ExtManageNavStrategy:SetHitPadding(2.0)
            end    
        end

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupExtManage)
    end

    -- -- 启用NoA动态导航配置
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

end

function WarehouseWithTab_HD:RemoveExtManageNavGroup()
    if self._NavGroupExtManage then
        WidgetUtil.RemoveNavigationGroup(self)
        self._NavGroupExtManage = nil
    end

    -- 重设动态导航配置
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function WarehouseWithTab_HD:RegisterArrangeNavGroup()
    if not IsHD() then
        return 
    end

    self:RemoveArrangeNavGroup()

    -- 更新扩容箱的聚焦状态
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtArrangeMode(true)
    end

    if not self._NavGroupArrange then
        local wtDFVerticalBox_0 = self:Wnd("DFVerticalBox_0", self)
        self._NavGroupArrange = WidgetUtil.RegisterNavigationGroup(wtDFVerticalBox_0, self, "Hittest")
    end

    if self._NavGroupArrange then
        local wtWHExpansionButton = self:Wnd("WBP_WarehouseExpansionButton_Pc", self)
        if wtWHExpansionButton then
            self._NavGroupArrange:AddNavWidgetToArray(wtWHExpansionButton)
        end
        self._NavGroupArrange:AddNavWidgetToArray(self._wtExtVerticalBox)

        if self._wtExtArrangeBtn:IsVisible()  then
            self._NavGroupArrange:AddNavWidgetToArray(self._wtExtArrangeBtn)
        end
        self._NavGroupArrange:SetScrollRecipient(self._wtDepositTabScrollBox)
        self._NavGroupArrange:SetNavSelectorWidgetVisibility(true)
        self._NavGroupArrange:SetAutoUseRootGeometrySize(false)

        local ArrangeNavStrategy = self._NavGroupArrange:GetOwnerNavStrategy()
        if ArrangeNavStrategy then
            ArrangeNavStrategy:SetHitPadding(2.0)
        end

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupArrange)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    end
end

function WarehouseWithTab_HD:RemoveArrangeNavGroup()
    if not IsHD() then
        return 
    end

    if self._NavGroupArrange then
        WidgetUtil.RemoveNavigationGroup(self)
        self._NavGroupArrange = nil
        -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoLR,self)
    end

    -- 更新扩容箱的聚焦状态
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtArrangeMode(false)
    end
end

-- 手柄调整扩容箱顺序
function WarehouseWithTab_HD:_fOnGamepadMoveBegin(self)
    loginfo("_fOnGamepadMoveBegin")
    
    if self._reorderableListComponent then
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    end
end

function WarehouseWithTab_HD:_fOnGamepadMoveFinish(self, lastIndex, newIndex)
    loginfo("_fOnGamepadMoveFinish")

    -- 以下更新扩容箱数据逻辑同步于：fOnShiftPosFinally
    --remapping
    local cellsInOrder = self._wtExtVerticalBox:GetAllChildren()
    for index, cell in ipairs(cellsInOrder) do
        -- 重新排序
        self._cacheDepositTabChildren[index] = cell
    end
    -- 排序后，当前选中由lastIndex变为newIndex
    self._curDepositTabIndex = newIndex
    Server.InventoryServer:ShiftExtOrder(lastIndex, newIndex)
    Server.InventoryServer:DoChangeSortConfig()

    self:_UpdateInputSummay()
end

function WarehouseWithTab_HD:_fOnGamepadMoveCancel(self)
    loginfo("_fOnGamepadMoveCancel")

    self:_UpdateInputSummay()
end

function WarehouseWithTab_HD:_OnDepositTabMouseEnter(InInputSummary)
    self:_UpdateInputSummay(InInputSummary)
end

function WarehouseWithTab_HD:_OnDepositTabMouseLeave(InInputSummary)
    self:_UpdateInputSummay(InInputSummary)
end

function WarehouseWithTab_HD:_UpdateInputSummay(InInputSummary)
    local summaryList = {}
    table.insert(summaryList, {actionName = "EndManage", func = self._EndExtManagement, caller = self ,bUIOnly = false, bHideIcon = false})

    if InInputSummary then
        for key, value in pairs(InInputSummary) do
            table.insert(summaryList, value)
        end
    end

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end

function WarehouseWithTab_HD:OnInputTypeChanged(CurInputType)
    if not IsHD() then
        return 
    end

    if CurInputType ~= EGPInputType.Gamepad then
        if self._extArrangePanel and self._extArrangePanel._HideExtManagerTips then
            self._extArrangePanel:_HideExtManagerTips()
        end
    end
end

-- BEGIN MODIFICATION @ yanmingjing
function WarehouseWithTab_HD:AddExtArrangeNavGroup()
    if not IsHD() then
        return 
    end
    self:RemoveExtArrangeNavGroup()
    if not self._extArrangeNavGroup then
        self._extArrangeNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtMainCanvas, self, "Hittest")
        -- local wtDFVerticalBox_0 = self:Wnd("DFVerticalBox_0", self)
        -- WidgetUtil.RemoveNavigationGroup(wtDFVerticalBox_0)
        if self._extArrangeNavGroup then
            self._extArrangeNavGroup:AddNavWidgetToArray(self._extArrangePanel._wtConfirmExtBtn)
            self._extArrangeNavGroup:AddNavWidgetToArray(self._extArrangePanel._wtSelectAllCheckbox)
            self._extArrangeNavGroup:AddNavWidgetToArray(self._extArrangePanel._wtExtSettingBtn)
            self._wtDepositTabScrollBox:SetScrollOffset(0)
            self._extArrangeNavGroup:AddNavWidgetToArray(self._wtMainDepositTab)
            local cellsInOrder = self._wtExtVerticalBox:GetAllChildren()
            if  cellsInOrder then
                for index, cell in ipairs(cellsInOrder) do
                    -- if index ~= 1 and index ~= 2 then
                    self._extArrangeNavGroup:AddNavWidgetToArray(cell)
                    -- end
                end
            end
            self._extArrangeNavGroup:SetAutoUseRootGeometrySize(false)
        end

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._extArrangeNavGroup)
    end
end
function WarehouseWithTab_HD:RemoveExtArrangeNavGroup()
    if not IsHD() then
        return 
    end

    if self._extArrangeNavGroup then
        WidgetUtil.RemoveNavigationGroup(self._wtMainCanvas)
        self._extArrangeNavGroup = nil
    end

    -- 更新扩容箱的聚焦状态
    -- for i, warehouseTab in ipairs(self._allDepositTabs) do
    --     warehouseTab:SetInExtArrangeMode(false)
    -- end
end
--END MODIFICATION
--END MODIFICATION
function WarehouseWithTab_HD:OnFocusTab(bIsFocus)
    local _wtBG = self:Wnd("DFImage_479", UIButton)
    if bIsFocus then
        _wtBG:SetColorAndOpacity(FLinearColor(0.822786,0.83077,0.83077,0.098039))
    else
        _wtBG:SetColorAndOpacity(FLinearColor(0.822786,0.83077,0.83077,0.047059))
    end
end

-- 绑定输入类型切换事件
function WarehouseWithTab_HD:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._onNotifyInputTypeChangedHandle then
            self._onNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._InitDepositTabList, self))
        end
    else
        if self._onNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._onNotifyInputTypeChangedHandle)
            self._onNotifyInputTypeChangedHandle = nil
        end
    end
end

--endregion
-----------------------------------------------------------------------

return WarehouseWithTab_HD
