----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReport)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ReportMainPanel : LuaUIBaseView
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ReportMainPanel = ui("ReportMainPanel")

local ReportConfig                    = require "DFM.Business.Module.ReportModule.ReportConfig"
local ReportTable = Facade.TableManager:GetTable("ReportConfig")
local EReportMode = import "EReportMode"
local REPORT_LEGAL = 115
--- BEGIN MODIFICATION @ VIRTUOS
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
local UGPInputHelper = import("GPInputHelper")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

local function log(...)
  print("ReportMainPanel", ...)
end

local function printtable(t)
    loginfo("[ReportMainPanel]")
    logtable(t)
end

function ReportMainPanel:Ctor()
    loginfo("ReportMainPanel:Ctor")

    self._reportType = 1
    self._index = 1
    self._reportTypeTbl = {}
    self._reportScene = 1
    self._matchId = 0

    self._wtSelectBtnTbl = {}
    self._bDorDown = false
    self._wtPlayerNickName = self:Wnd("wtPlaryerNickName", UITextBlock)
    self._tabTxtTbl = {}
    self._tabPlayerIdTbl = {}
    self._wtPlayerTab = UIUtil.WndDropDownBox(self, "wtPlayerTab", self._ChangePlayer, self._tabTxtTbl, {}, 0)

    self._wtReportTxt = self:Wnd("wtReportDesc", UIWidgetBase)
    self._wtTextNum = self:Wnd("DFTextBlock_122", UITextBlock)
    self._wtOneTabText = self:Wnd("wtOneTabText", UITextBlock)

    self._wtTextNum:SetText("0/80")
    self._wtReportTxt:Event("OnTextChanged", self._OnInputTextChanged, self)
    self._playerTxtTbl = {}

    self._reportTypeTxt = {
        [ReportConfig.ReportPlayerType.Enemy] = ReportConfig.ReportEnemyType,
        [ReportConfig.ReportPlayerType.Teammate] = ReportConfig.ReportTeammateType,
        [ReportConfig.ReportPlayerType.Killer] = ReportConfig.ReportKillerType,
    }

    self._wtCommonPopWinV2 = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self.CloseBtnOnClick,self)
    self._wtCommonPopWinV2:BindCloseCallBack(fCallbackIns)

    self._wtCommonPopWinV2:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
		{btnText =  Module.Inventory.Config.Loc.ConfirmText, fClickCallback = self._ReportBtnOnClick, caller = self, bNeedClose = false}
	})

    self._wtReportBox = UIUtil.WndScrollGridBox(self, "ScrollGridBox_208", self.OnGetItemCount, self.OnProcessItemWidget)
    
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        local wtKeyIconBox = self._wtPlayerTab:Wnd("DFCommonCheckButton", UIWidgetBase):Wnd("wtKeyIcon", HDKeyIconBox)
        if wtKeyIconBox then
            wtKeyIconBox:Visible()
            wtKeyIconBox:SetOnlyDisplayOnGamepad(true)
            wtKeyIconBox:InitByDisplayInputActionName("Common_ButtonTop", true, 0, true)
        end

        -- Override common pop window gamepad inputs.
        self._wtCommonPopWinV2:OverrideGamepadSetting("KillReport_Confirm", nil, WidgetUtil.EUINavDynamicType.Default)

        -- Initialize tabs group gamepad switch instructions.
        self._wtReportGroup = self:Wnd("wtReportGroup", UIWidgetBase)

        self._wtReportGroup.WBP_GamepadKeyIconBox_Left:SetOnlyDisplayOnGamepad(true)
        self._wtReportGroup.WBP_GamepadKeyIconBox_Left:InitByDisplayInputActionName("Common_SwitchToPrevTab_Shoulder", true, 0, false)
        self._wtReportGroup.WBP_GamepadKeyIconBox_Left:Visible()

        self._wtReportGroup.WBP_GamepadKeyIconBox_Right:SetOnlyDisplayOnGamepad(true)
        self._wtReportGroup.WBP_GamepadKeyIconBox_Right:InitByDisplayInputActionName("Common_SwitchToNextTab_Shoulder", true, 0, false)
        self._wtReportGroup.WBP_GamepadKeyIconBox_Right:Visible()
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS
function ReportMainPanel:OnShowBegin()
    if IsHD() then
        self._OnInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInpurtType)
    end
    UIUtil.InitDropDownBox(self._wtPlayerTab, self._tabTxtTbl, {}, 0)
end

function ReportMainPanel:OnHideBegin()
    if IsHD() then
        if self._OnInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnInputTypeChangedHandle)
        end
    end 
end
--- END MODIFICATION

--2 恶意游戏行为,3 使用作弊工具,1 恶意信息
function ReportMainPanel:OnInitExtraData(playerTable, reportScene, matchId, reportStr, matchTime, reportEntrance, modeType, reportEndCall, reportTabSort)
  log("ReportMainPanel:OnInitExtraData", reportStr)
    self._reportEndCall = reportEndCall
    self._reportScene = reportScene
    self._modeType = modeType and modeType or EReportMode.None
    self._categoryTbl = {}
    self._reportTabSort = reportTabSort
    if self._categoryTbl and Server.ReportServer:GetReportConfigByScene(self._reportScene) then
        deepcopy(self._categoryTbl, Server.ReportServer:GetReportConfigByScene(self._reportScene))
    end
    printtable(self._categoryTbl)
    self:SortTabList()
    self:RefreshReportTbl()

    printtable(self._categoryTbl)
    self._playerTable = playerTable
    self._playerInfo = self._playerTable[1]
    if #playerTable > 1 then
        self._wtPlayerNickName:Collapsed()
        self._wtPlayerTab:Visible()
        for index, info in ipairs(playerTable) do
            local text = info.nick_name--'<customstyle color="Color_Highlight01">' .. info.nick_name .. '</>'
            if info.player_type then
                local param = {
                    ["report_type_name"] = self._reportTypeTxt[info.player_type],
                    ["role_name"] = text
                }
                text = StringUtil.Key2StrFormat(Module.Report.Config.ReportNameStr, param)
            end
            table.insert(self._tabTxtTbl, text)
        end
        self._wtPlayerTab:RefreshTab()
        self:_ChangePlayer(0)
    else
        self._wtPlayerNickName:Visible()
        log("ReportMainPanel:OnInitExtraData", self._playerInfo.nick_name)
        if IsPS5Family() then
            --- using Platform Online ID as player name.
            local callback = function(onlineID)
                self._wtPlayerNickName:SetText(onlineID)
            end
            Module.Social:AsyncGetPS5OnlineIdByUID(self._playerInfo.player_id, callback, self)
        else
            local text = self._playerInfo.nick_name and self._playerInfo.nick_name or "昵称为空"
            self._wtPlayerTab:Collapsed()
            if self._playerInfo.player_type and self._playerInfo.nick_name then
                local param = {
                    ["report_type_name"] = self._reportTypeTxt[self._playerInfo.player_type],
                    ["role_name"] = self._playerInfo.nick_name
                }
                text = StringUtil.Key2StrFormat(Module.Report.Config.ReportNameStr, param)
            end
            self._wtPlayerNickName:SetText(text)
        end

    end
    self._matchId = matchId
    self._reportStr = reportStr
    self._matchTime = matchTime
    self._reportEntrance = reportEntrance
    self._showTips = true
end

function ReportMainPanel:SortTabList()
  if self._reportTabSort == nil or #self._reportTabSort == 0 then
    return
  end
  local tabTable = {}
  for _, categoryNum in ipairs(self._reportTabSort) do
    for i, categoryinfo in ipairs(self._categoryTbl) do
      if categoryinfo.report_category == categoryNum then
        table.insert(tabTable, categoryinfo)
      end
    end
  end
  self._categoryTbl = tabTable
end

function ReportMainPanel:RefreshReportTbl()
    for i, info in ipairs(self._categoryTbl) do
        local reasonTbl = {}
        for index, reasonInfo in ipairs(info.reason_config) do
            local reportMode = ReportTable[reasonInfo.report_reason].ReportMode
            if (self._modeType == EReportMode.None or reportMode == EReportMode.None or reportMode == self._modeType) and reportMode ~= EReportMode.Disabled then
                table.insert(reasonTbl, reasonInfo) 
            end
        end
        info.reason_config = reasonTbl
    end
end

function ReportMainPanel:_ChangePlayer(index)
    self._playerInfo = self._playerTable[index + 1]
end

function ReportMainPanel:FreshCategory()
    for i = 1, 4 do
        if self._categoryTbl[i] then
          local key = self._categoryTbl[i].report_category
          if ReportTable[key].ReportMode ~= EReportMode.Disabled then
            table.insert(self._playerTxtTbl, {keyText = ReportTable[key].ReportName})
          end
        end
    end
    self._wtTabGroup = UIUtil.WndTabGroupBox(self, "wtReportGroup", self.OnGetTabItemCount, self.OnProcessTabItemWidget, self._BoxOnClick)
    if #self._playerTxtTbl == 2 then
      self._wtTabGroup:SetItemOptionSize(FVector2D(1107, self._wtTabGroup.OptionHeight))
    end

    if #self._playerTxtTbl == 1 then
      self._wtTabGroup:Collapsed()
      self._wtOneTabText:Visible()
      self._wtOneTabText:SetText(self._playerTxtTbl[1].keyText)
    end
    self._curSelectedTopIndex = 1
    self:_BoxOnClick(0)
    self._wtTabGroup:RefreshTab()
end

function ReportMainPanel:OnGetTabItemCount()
    return #self._playerTxtTbl
end

function ReportMainPanel:OnProcessTabItemWidget(position, topTabItem)
    local dataIdx = position + 1
    ---@type DroDownData
    local droDownData = self._playerTxtTbl[dataIdx]
    if not droDownData then
        return
    end
    topTabItem.DFCommonCheckButton:SetMainTitleText4AllState(droDownData.keyText)
end


function ReportMainPanel:AddListeners()
end

function ReportMainPanel:OnNavBack()
  return false
end

function ReportMainPanel:OnOpen()
    self:AddListeners()
    self:FreshCategory()
    ReportConfig.evtReportMainPanelOpen:Invoke()
end

function ReportMainPanel:OnGetItemCount()
    return #self.reportTbl
end

function ReportMainPanel:OnProcessItemWidget(index, itemWidget)
    if ReportTable[self.reportTbl[index + 1].report_reason] then
        local text = ReportTable[self.reportTbl[index + 1].report_reason].ReportName    
        if text then
            itemWidget:BP_SetMainTitle(text)
        else
          local nothingStr = Module.CommonWidget.Config.Loc.NothingFound 
          itemWidget:BP_SetMainTitle(self.reportTbl[index + 1].report_reason .. self.reportTbl[index + 1].reason_desc .. nothingStr)
        end
    else
        itemWidget:BP_SetMainTitle(self.reportTbl[index + 1].report_reason)
    end

    local checkWidget = itemWidget:Wnd("wtSelectBox", UICheckBox)
    checkWidget:RemoveEvent("OnCheckStateChanged")
    checkWidget:SetIsChecked(false, false)
    checkWidget:Event("OnCheckStateChanged", self._ReportBoxOnClick, self, self.reportTbl[index + 1].report_reason, itemWidget)
end

function ReportMainPanel:_ReportBoxOnClick(reportIndex, itemWidget ,isCheck)
    if isCheck then
        if #self._reportTypeTbl >= 3 then
            Module.CommonTips:AssignNextTipType_HD(true, false)
            Module.CommonTips:ShowSimpleTip(Module.Report.Config.ReportNoMore)
            itemWidget:Wnd("wtSelectBox", UICheckBox):SetIsChecked(false,false)
            return
        else
            table.insert(self._reportTypeTbl, reportIndex)
        end
        if reportIndex == REPORT_LEGAL then
            self._wtReportTxt:SetHintText(Module.Report.Config.ReportLegalHintTxt)
        end
    else
        local tblIndex = nil
        for index, value in ipairs(self._reportTypeTbl) do
            if reportIndex == value then
                tblIndex = index
            end
        end
        if tblIndex then
            table.remove(self._reportTypeTbl, tblIndex)
        end
        if reportIndex == REPORT_LEGAL then
            self._wtReportTxt:SetHintText(Module.Report.Config.ReportNormalHintTxt)
        end
    end
end

function ReportMainPanel:_BoxOnClick(index)
    if index < 0 or index > #self._categoryTbl - 1 then
        logerror("ReportMainPanel:_BoxOnClick, index out of range, index=", index, "list len=", #self._categoryTbl)
        return
    end
    self._reportType = self._categoryTbl[index + 1].report_category
    self._reportTypeTbl = {}
    self.reportTbl = self._categoryTbl[index + 1].reason_config
    self._wtReportBox:RefreshAllItems()
    self._wtReportTxt:SetHintText(Module.Report.Config.ReportNormalHintTxt)
end

function ReportMainPanel:_ReportBtnOnClick()
    if #self._reportTypeTbl == 0 then
      Module.CommonTips:AssignNextTipType_HD(true, false)
      Module.CommonTips:ShowSimpleTip(Module.Report.Config.ReportNoSelect)
      return
    else
        for _, index in ipairs(self._reportTypeTbl) do
            if index == REPORT_LEGAL and self._wtReportTxt:GetText() == "" then
                Module.CommonTips:AssignNextTipType_HD(true, false)
                Module.CommonTips:ShowSimpleTip(Module.Report.Config.ReportLegalTipTxt)
                return
            end
        end
    end
    local reportTxt = tostring(self._wtReportTxt:GetText())
    local reportInfo = {
        report_scence = self._reportScene,             --举报场景
        report_category = self._reportType,           --举报大类
        report_reason = self._reportTypeTbl,             --举报原因
        reported_player_id = self._playerInfo.player_id,        --被举报人id
        battle_id = self._matchId and self._matchId or 0,                --对局id
        battle_time = self._matchTime,              --对局时间
        reported_profile_url = self._playerInfo.pic_url,     --被举报者头像url
        report_desc = reportTxt,              --举报描述
        report_content = self._reportStr,           --举报玩家发言时，上报被举报者的发言文本；举报个人资料时，填空；举报对局时，仅上报对局内聊天文本
        entrance = self._reportEntrance,
        nick_name = self._playerInfo.nick_name
    }
    Server.ReportServer:FetchTssReport(reportInfo, self._reportEndCall)
    Facade.UIManager:CloseUI(self)
end

function ReportMainPanel:OnClose()
    self:RemoveAllLuaEvent()
    ReportConfig.evtReportMainPanelClose:Invoke()
end

function ReportMainPanel:CloseBtnOnClick()
    Facade.UIManager:CloseUI(self)
    Server.ReportServer:FetchReportConfig()
end

function ReportMainPanel:_OnInputTextChanged(text)
    local num ,str = StringUtil.GetRealWidth(text, 80)
    if num >= 80 and self._showTips then
      Module.CommonTips:AssignNextTipType_HD(true, false)
      Module.CommonTips:ShowSimpleTip(Module.Report.Config.ReportLimitTxt)
      self._showTips = false
    end
    self._wtReportTxt:SetText(str)
    self._wtTextNum:SetText(StringUtil.Key2StrFormat(Module.Report.Config.ReportInputWords, {["curNum"] = math.min(num, 80)}))
end

--- BEGIN MODIFICATION @ VIRTUOS
function ReportMainPanel:_OnInputTypeChanged(curInpurtType)
    if not IsHD() then
        return
    end

    if curInpurtType == EGPInputType.Gamepad then
        self:_InitializeGamepadFeature()
    else
        self:_DisableGamepadFeature()
    end
end

function ReportMainPanel:_InitializeGamepadFeature()
    if not IsHD() then
        return
    end

    if self._wtPlayerTab then
        self._wtPlayerTab:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnPlayerTabToggled, self)
    end

    if not self._gamepadTabToggleHandles then
        self._gamepadTabToggleHandles =
        {
            self:AddInputActionBinding("Common_SwitchToNextTab_Shoulder", EInputEvent.IE_Pressed, self.SelectNextTap, self, EDisplayInputActionPriority.UI_Pop),
            self:AddInputActionBinding("Common_SwitchToPrevTab_Shoulder", EInputEvent.IE_Pressed, self.SelectPrevTap, self, EDisplayInputActionPriority.UI_Pop),
        }
    end

    if not self._navGroups then
        -- Remove common pop window default empty nav group to prevent Nav Group Leaks.
        WidgetUtil.RemoveNavigationGroup(self._wtCommonPopWinV2)
        self._wtCommonPopWinV2._navGroup = nil

        -- Override common pop window nav groups.
        self._navGroups =
        {
            desc = WidgetUtil.RegisterNavigationGroup(self._wtReportTxt, self, "Hittest"),
            types = WidgetUtil.RegisterNavigationGroup(self._wtReportBox, self, "Hittest"),
        }
        if self._navGroups.desc then
            self._navGroups.desc:AddNavWidgetToArray(self._wtReportTxt)
        end

        if self._navGroups.types then
            self._navGroups.types:AddNavWidgetToArray(self._wtReportBox)
            self._navGroups.types:MarkIsStackControlGroup()
        end

        -- WidgetUtil.BuildGroupTree(self._wtCommonPopWinV2)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups.types)
    end

    -- Initialize player tab drop down shortcut.
    if not self._openPlayerTabHandle and self._wtPlayerTab:IsVisible() then
        local openTabEvent = SafeCallBack(
        function()
            if self._wtPlayerTab:GetIsEnabled() then
                self._wtPlayerTab:OpenMenu()
            end
        end, self)

        self._openPlayerTabHandle = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, openTabEvent, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function ReportMainPanel:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    
    self._wtPlayerTab:RemoveEvent("PostOnMenuOpenChanged_GamepadUsed")

    if self._gamepadTabToggleHandles then
        for key, handle in pairs(self._gamepadTabToggleHandles) do
            self:RemoveInputActionBinding(handle)
        end

        self._gamepadTabToggleHandles = nil
    end

    if self._navGroups then
        WidgetUtil.RemoveNavigationGroup(self)
       self._navGroups = nil
    end

    if self._openPlayerTabHandle then
        self:RemoveInputActionBinding(self._openPlayerTabHandle)
        self._openPlayerTabHandle = nil
    end

    -- Make sure player tab drop down menu gamepad inputs is disabled.
    self:_OnPlayerTabToggled(false)
end

function ReportMainPanel:SelectNextTap()
    if not IsHD() then
        return
    end

    local index = self._wtReportGroup:GetCurSelectedIndex()
    if index < self._wtReportGroup:GetMaxIndex() then
      self._wtReportGroup:SetTabIndex(index + 1)
    end
end

function ReportMainPanel:SelectPrevTap()
    if not IsHD() then
        return
    end

    local index = self._wtReportGroup:GetCurSelectedIndex()
    if index > 0 then
      self._wtReportGroup:SetTabIndex(index - 1)
    end
end

function ReportMainPanel:_OnPlayerTabToggled(bOpen)
    if not IsHD() then
        return
    end

    if bOpen then
        if not self._playerNavGroup then
            self._playerNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtPlayerTab.ScrollGridBox, self._wtPlayerTab, "Hittest")
            if self._playerNavGroup then
                self._playerNavGroup:AddNavWidgetToArray(self._wtPlayerTab.ScrollGridBox)
                self._playerNavGroup:SetScrollRecipient(self._wtPlayerTab.ScrollGridBox)
                self._playerNavGroup:MarkIsStackControlGroup()
            end

            WidgetUtil.TryFocusDefaultWidgetByGroup(self._playerNavGroup)
        end

        if not self._closeDropHandle then
            self._closeDropHandle = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, self._wtPlayerTab.CloseMenu, self._wtPlayerTab, EDisplayInputActionPriority.UI_Pop)
        end
    else
        if self._playerNavGroup then
            WidgetUtil.RemoveNavigationGroup(self._wtPlayerTab)
            self._playerNavGroup = nil
        end

        if self._closeDropHandle then
            self:RemoveInputActionBinding(self._closeDropHandle)
            self._closeDropHandle = nil
        end
    end
end
--- END MODIFICATION

return ReportMainPanel

