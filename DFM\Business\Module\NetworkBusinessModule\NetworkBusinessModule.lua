----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMNetworkBusiness)
----- LOG FUNCTION AUTO GENERATE END -----------




---@class NetworkBusinessModule : ModuleBase
local NetworkBusinessModule = class("NetworkBusinessModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local NetworkBusinessLogic = require "DFM.Business.Module.NetworkBusinessModule.Logic.NetworkBusinessLogic"
local XunYouBusinessLogic = require "DFM.Business.Module.NetworkBusinessModule.Logic.XunYouBusinessLogic"
local IDCLogic = require "DFM.Business.Module.NetworkBusinessModule.Logic.IDCInfoLogic"

function NetworkBusinessModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数
---@overload fun(ModuleBase, OnInitModule)
function NetworkBusinessModule:OnInitModule()
    NetworkBusinessLogic.OnInitModule()
    self:AddLuaEvent(Server.MatchServer.Events.evtUpdateUdpEchoAddress, self.SetUdpEchoAddress, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtUpdateAccelAddress, self.SetAccelAddressByDsInfo, self)
    self:AddLuaEvent(Server.MatchServer.Events.evtUpdateIdcData, self.SetIdcData, self)
end

--- 若为非懒加载模块，则在Init后调对应每个OnGameFlowChangeEnter
--- 模块默认[常驻]加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function NetworkBusinessModule:OnLoadModule()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function NetworkBusinessModule:OnUnloadModule()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function NetworkBusinessModule:OnDestroyModule()
    NetworkBusinessLogic.OnDestoryModule()
    self:RemoveLuaEvent(Server.MatchServer.Events.evtUpdateUdpEchoAddress, self.SetUdpEchoAddress, self)
    self:RemoveLuaEvent(Server.MatchServer.Events.evtUpdateAccelAddress, self.SetAccelAddressByDsInfo, self)
    self:RemoveLuaEvent(Server.MatchServer.Events.evtUpdateIdcData, self.SetIdcData, self)
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function NetworkBusinessModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- NetworkControlEventLogic.RemoveLobbyListeners()
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function NetworkBusinessModule:OnGameFlowChangeEnter(gameFlowType)
   if gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.ModeHall then
        -- 暂时屏蔽邮件功能
        --Module.NetworkBusiness:UpdateXunYouVipStatus()
   end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有
-- 模块[Loading]加载资源，区分局内外
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function NetworkBusinessModule:OnLoadingLogin2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function NetworkBusinessModule:OnLoadingGame2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function NetworkBusinessModule:OnLoadingFrontend2Game(gameFlowType)
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------


function NetworkBusinessModule:ShowXunYouSpeedTips()
    Facade.UIManager:AsyncShowUI(UIName2ID.XunYouSpeedTips)
end


function NetworkBusinessModule:GetCurrentNetworkStatus()
    return NetworkBusinessLogic.GetCurrentNetworkStatus()
end

function NetworkBusinessModule:ShowXunYouSpeedUpWindow()
    Facade.UIManager:AsyncShowUI(UIName2ID.XunYouSpeedUpWindow)
end

function NetworkBusinessModule:ShowXunYouSpeedConfirmWindow()
    Facade.UIManager:AsyncShowUI(UIName2ID.XunYouConfirmWindow)
end

function NetworkBusinessModule:GetCurrentNetworkStatusXunYou()
    return NetworkBusinessLogic.GetCurrentNetworkStatusXunYou()
end

function NetworkBusinessModule:AutoShowXunYouSpeedTips()
    logerror("NetworkBusinessModule:AutoShowXunYouSpeedTips..")
    if IsBuildRegionCN() and IsHD() and Module.NetworkBusiness.Field:GetXunYouAutoFlag() then
        -- 仅在大厅展示
        if not Facade.GameFlowManager:CheckIsInFrontEnd() then
            logerror("NetworkBusinessModule:AutoShowXunYouSpeedTips in game now")
            return
        end

        local flowType = Facade.GameFlowManager:GetCurrentGameFlow()
        if flowType ~= EGameFlowStageType.Lobby and flowType ~= EGameFlowStageType.ModeHall then
            logerror("NetworkBusinessModule:AutoShowXunYouSpeedTips not lobby or modehall now")
            return
        end
        Module.NetworkBusiness.Field:SetTipsDone()
        Module.NetworkBusiness.Field:SetXunYouAutoFlag(false)
        Module.CommonTips:ShowSimpleTip(Module.NetworkBusiness.Config.Loc.XunYouSpeedTips, 5)
    end
end

function NetworkBusinessModule:ReportXunYouTLog()
    XunYouBusinessLogic.ReportXunYouTLog()
    Module.NetworkBusiness.Field:SetNeedReportXunYouState(false)
end

function NetworkBusinessModule:GetNeedReportXunYouState()
    return Module.NetworkBusiness.Field:GetNeedReportXunYouState()
end

function NetworkBusinessModule:SetIdcData(idc_list)
    if idc_list then
        local idc_rtt_list = {}
        for i, roundtripAddr in pairs(idc_list) do
            for _, service in pairs(idc_list[i].services) do
                if service.result ~= RttTimeResult.RttTime_Timeout then
                    local result = service.rtt or 300
                    table.insert(idc_rtt_list, result)
                end
            end
        end

        table.sort(idc_rtt_list, function(a, b)
            return a < b
        end)

        Module.NetworkBusiness.Field:SetIdcRttList(idc_rtt_list)
    else
        logerror("[NetworkBusinessModule] setIdcRttData idc_list is nil")
    end
end

function NetworkBusinessModule:SetIdcRttList(idcRttList)
    Module.NetworkBusiness.Field:SetIdcRttList(idcRttList)
end

-- 迅游Android接口
function NetworkBusinessModule:InitXunYou()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        XunYouBusinessLogic.InitXunYou()
    end
end

function NetworkBusinessModule:SetUserToken()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        XunYouBusinessLogic.SetUserToken()
    end
end

function NetworkBusinessModule:SetUdpEchoAddress(ip, port)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        XunYouBusinessLogic.SetUdpEchoAddress(ip, port)
    end
end

function NetworkBusinessModule:AddAccelAddress(ip, port)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        XunYouBusinessLogic.AddAccelAddress(ip, port)
    end
end

function NetworkBusinessModule:ClearAccelerationAddresses()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        XunYouBusinessLogic.ClearAccelerationAddresses()
    end
end

function NetworkBusinessModule:IsUDPProxy()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        return XunYouBusinessLogic.IsUDPProxy()
    end
    return false
end

function NetworkBusinessModule:GetAccelerationStatus()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        return XunYouBusinessLogic.GetAccelerationStatus()
    end
    return 0
end

function NetworkBusinessModule:GetWebUIUrl(position)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        return XunYouBusinessLogic.GetWebUIUrl(position)
    end
    return ""
end

function NetworkBusinessModule:SetAccelAddressByDsInfo(dsInfo)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if not bXunYouEnable then
        return
    end

    if not dsInfo or #dsInfo == 0 then
        return
    end
    
    for _, info in ipairs(dsInfo) do
        if info and info.Ip and info.Port then
            XunYouBusinessLogic.AddAccelAddress(info.Ip, info.Port)
        end
    end

    Module.NetworkBusiness:BeginRound()
end

function NetworkBusinessModule:SetEchoAddressByHostInfo(echoIplist)
    logerror("[NetworkBusinessModule][ SetEchoAddressByHostInfo] domain num", #echoIplist)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if not bXunYouEnable then
        return
    end

    if echoIplist == nil or #echoIplist <= 0 then
        return 
    end

    for _, hostInfo in ipairs(echoIplist) do
        logerror("[NetworkBusinessModule][SetEchoAddressByHostInfo] ds_domain", hostInfo.ds_domain)
        if hostInfo.ds_domain then
            local domain = string.split(hostInfo.ds_domain, ":")[1]
            local port = string.split(hostInfo.ds_domain, ":")[2]
            local OneSDKModule =  import "DFMGameOneSDK"
            local _OneSDK = OneSDKModule.Get(GetGameInstance())
            if _OneSDK and domain and port then
                local ip = _OneSDK:GetAddressByName(domain)
                logwarning("[NetworkBusinessModule][SetEchoAddressByHostInfo] echoIplist ip, port:", ip, port)
                Module.NetworkBusiness:SetUdpEchoAddress(ip, port)
            end
        end
    end
end

function NetworkBusinessModule:CheckAndShowNetworkQualityWindow()
    local avgPing = Module.Settlement:GetLastRoomAvgPing()
    local sdPing = Module.Settlement:GetLastRoomSDPing()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    local bNQEnable = Module.NetworkBusiness:IsNQWindowEnabled()
    local ping = Module.NetworkBusiness.Config.Ping
    local bIsUseXunYou = Module.NetworkBusiness:IsXunYouSpeedNow()
    if avgPing then
        LogAnalysisTool.ReportGamePing(avgPing,bIsUseXunYou)
    end
    logerror("[NetworkBusinessModule] CheckAndShowNetworkQualityWindow avgPing:", avgPing, " sdPing:", sdPing, " bXunYouEnable:", bXunYouEnable, " bNQEnable:", bNQEnable, " ping:", ping, "bIsUseXunYou:",bIsUseXunYou)
    if avgPing > ping and bXunYouEnable and bNQEnable and not bIsUseXunYou then
        local fOnConfirm = function()
            Module.NetworkBusiness.Field:SetShowNetworkQualityWindowTime()
            local webUrl = Module.NetworkBusiness:GetWebUIUrl(Module.NetworkBusiness.Config.OpenXunYouPosition.LobbyWindow)
            Module.GCloudSDK:OpenUrl(webUrl, 3, false, true, "", false)
            LogAnalysisTool.ReportXunYouWindows(true)
        end

        local fOnCancel = function()
            Module.NetworkBusiness.Field:SetShowNetworkQualityWindowTime()
            LogAnalysisTool.ReportXunYouWindows(false)
        end
        local tips = string.format(Module.NetworkBusiness.Config.Loc.NQWindowTips, avgPing)
        Module.CommonTips:ShowConfirmWindow(
            tips,
            fOnConfirm,
            fOnCancel,
            Module.NetworkBusiness.Config.Loc.CancelText,
            Module.NetworkBusiness.Config.Loc.ConfirmText
        )

        -- local tips = string.format(Module.NetworkBusiness.Config.Loc.NQTips, avgPing)
        -- Module.NetworkBusiness.Field:SetShowNetworkQualityWindowTime()
        -- LogAnalysisTool.ReportXunYouWindows(true)
        -- Module.CommonTips:ShowSimpleTip(tips, 5)
    end
end

-- 灰度范围 
function NetworkBusinessModule:IsXunYouEnabled()
    if PLATFORM_ANDROID and IsBuildRegionCN() then
        return Server.AccountServer:CheckExpTagIdValid(Module.NetworkBusiness.Config.XunYouABID)
    end

    return false
end

-- 弹窗间隔
function NetworkBusinessModule:IsNQWindowEnabled()   
    local lastShowTime = Module.NetworkBusiness.Field:GetLastShowNetworkQualityWindowTime()
    if lastShowTime == 0 then
        return true
    end

    local curTime = TimeUtil.GetCurrentTimeMillis()
    local IsNQWindowEnabled = (curTime - lastShowTime) > Module.NetworkBusiness.Config.NQWindowInterval
    logerror("[NetworkBusinessModule] IsNQWindowEnabled:", IsNQWindowEnabled,  "lastShowTime: ", lastShowTime, " curTime:", curTime)
    return IsNQWindowEnabled
end

function NetworkBusinessModule:GetGuid()
    local guid = Module.NetworkBusiness.Config.GUID

    if BUILD_REGION_CN_EXPER then
        guid = Module.NetworkBusiness.Config.GUID_Exper
    end

    return guid
end

function NetworkBusinessModule:GetMailUrl(url)
    logerror("[NetworkBusinessModule] GetMailUrl url:", url)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if not url then
        return true, url
    end

    if not bXunYouEnable then
        return false, url
    end

    if PLATFORM_ANDROID then
        url = XunYouBusinessLogic.GetWebUIUrl(Module.NetworkBusiness.Config.OpenXunYouPosition.Mail)
    end

    if string.find(url, Module.NetworkBusiness.Config.XunYouUrl) then
        local openId = Server.SDKInfoServer:GetOpenIdStr()
        local guid = Module.NetworkBusiness:GetGuid()
        local client_type = Server.AccountServer:GetPlatIdType()
        local result = url.."/?user_openid="..openId.."&guid="..guid.."&client_type="..client_type.."&go=5"
    end
    LogAnalysisTool.ReportXunYouMail()
    return true, url
end

function NetworkBusinessModule:UpdateXunYouVipStatus()
    logerror("[NetworkBusinessModule] UpdateXunYouVipStatus")
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        local curVipStatus = XunYouBusinessLogic.GetAccelerationStatus()
        local lastLoginVipStatus = Module.NetworkBusiness.Field:GetXunYouVipStatus()
        logerror("[NetworkBusinessModule] UpdateXunYouVipStatus curVipStatus:", curVipStatus, " lastLoginVipStatus:", lastLoginVipStatus)
        if curVipStatus ~= lastLoginVipStatus then
            if Module.NetworkBusiness.Field:CheckNeedMailByStatus(curVipStatus, lastLoginVipStatus) then
                -- 需要发邮件
                Server.MailServer:UpdateXunYouVipStatus(Module.NetworkBusiness.Config.XunYouVipStatusMailCfgID)
            end
            Module.NetworkBusiness.Field:SetXunYouVipStatus(curVipStatus)
        end
    end
end

function NetworkBusinessModule:OnApplicationActive(bActive)
    logerror("[NetworkBusinessModule] OnApplicationActive bActive:", bActive)
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if not bXunYouEnable then
        return
    end

    if bActive then
        -- 游戏前台
        XunYouBusinessLogic.GameForeground()
    else
        -- 游戏后台
        XunYouBusinessLogic.GameBackground()
    end
end

function NetworkBusinessModule:BeginRound()
    local bXunYouEnable = Module.NetworkBusiness:IsXunYouEnabled()
    if bXunYouEnable then
        XunYouBusinessLogic.BeginRound()
    end
end

-- 是否迅游加速中
function NetworkBusinessModule:IsXunYouSpeedNow()
    return XunYouBusinessLogic.IsXunYouSpeedNow()
end


--###########IDC相关###########
function NetworkBusinessModule:GetIdcRtt(callback)
    IDCLogic.GetIdcRtt(callback)
end

function NetworkBusinessModule:SetSelectIdc(area)
   
end

function NetworkBusinessModule:GetSelectIdcInfo(callback)
    IDCLogic.GetSelectIdcInfo(callback)
end

function NetworkBusinessModule:OpenSwitchIDCPanel()
    Facade.UIManager:AsyncShowUI(UIName2ID.SwitchIDCPanel)
end

function NetworkBusinessModule:GetIdcDesc(area,delay)
    return IDCLogic.GetIdcDesc(area, delay)
end

function NetworkBusinessModule:GetCurSelectedIdc()
    return Module.NetworkBusiness.Field:GetCurSelectedIdc()
end

function NetworkBusinessModule:SetCurSelectedIdc(area)
    Module.NetworkBusiness.Field:SetCurSelectedIdc(area)
    IDCLogic.SetSelectIdc(area)
end

function NetworkBusinessModule:IsEnableSwitchIDC()
    return IsBuildRegionGA() or IsBuildRegionGlobal()
end
--###########IDC相关###########

return NetworkBusinessModule
