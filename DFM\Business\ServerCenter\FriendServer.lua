----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSFriend)
----- LOG FUNCTION AUTO GENERATE END -----------

--- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
local UDFMPlatformFriendManager = import("DFMPlatformFriendManager")
--- END MODIFICATION

---@class FriendServer : ServerBase
local FriendServer = class("FriendServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local UGameFriend = import "DFMGameFriend"
local GameFriendMgr = UGameFriend.Get(GetWorld())

local function log(...)
    loginfo("", "[FriendServer]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

function FriendServer:Ctor()
    self.Events = {
       evtFriendListGetEnd = LuaEvent:NewIns("FriendServer.evtFriendListGetEnd"),
       evtOnlinePlayerList = LuaEvent:NewIns("FriendServer.evtOnlinePlayerList"),
       evtRecommendFriendList = LuaEvent:NewIns("FriendServer.evtRecommendFriendList"),
       evtFindPlayerList = LuaEvent:NewIns("FriendServer.evtFindPlayerList"),
       evtApplyPlayerList = LuaEvent:NewIns("FriendServer.evtApplyPlayerList"),
       evtFriendList = LuaEvent:NewIns("FriendServer.evtFriendList"),
       evtFriendCoPlayerList = LuaEvent:NewIns("FriendServer.evtFriendCoPlayerList"),
       evtFriendCreateRoom = LuaEvent:NewIns("FriendServer.evtFriendCreateRoom"),
       evtFetchRedDot = LuaEvent:NewIns("FriendServer.evtFetchRedDot"),
       evtBlackList = LuaEvent:NewIns("FriendServer.evtBlackList"),
       evtRecentPlayerList = LuaEvent:NewIns("FriendServer.evtRecentPlayerList"),
       evtSendAddSucess = LuaEvent:NewIns("FriendServer.evtSendAddSucess"),
       evtAddRoomAllPlayer = LuaEvent:NewIns("FriendServer.evtAddRoomAllPlayer"),
       evtAddPlayerToBlackList = LuaEvent:NewIns("FriendServer.evtAddPlayerToBlackList"),
       evtFriendLishChange = LuaEvent:NewIns("FriendServer.evtFriendLishChange"),
       evtFriendListReqSucess = LuaEvent:NewIns("FriendServer.evtFriendListReqSucess"),
    }
    self.redDotNum = 0
    self.getGiftNum = 0
    self.friendIdHash = {}
    self.friendNickList = {}
    self.stateReqList = {}
    self.blackList = {}
    self.blackListData = {}
    self.recommendList = {}
    self.isInit = false

    -- BEGIN MODIFICATION - VIRTUOS
    -- 平台黑名单，目前只有PS5平台使用
    if IsPS5Family() then
        self.platformBlockList = {}
    end
    --  END MODIFICATION - VIRTUOS

    self.FriendList = {} -- 缓存的好友列表
    self.GameFriendList = {}
    self.OpenFriendList = {}
    self.DiscordFriendList = {}
    self.tempDiscordFriendList = {}
    self.gameFriendIdHash = {}
    -- 常量
    self.EXinyongType = {
        ChatLimit = 0,
        AddFriendLimit = 1,
    }
    self._shouldPrint = true

    self._canGetFriend = true
end

function FriendServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSFriendChangeNtf",self.OnCSFriendChangeNtf, self)

    --- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
    if PLATFORM_GEN9 == 1 then
        local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
        if DFMPlatformFriendManager then
            DFMPlatformFriendManager.OnReadPlatformFriendsCompleteDelegate:Add(self._OnReadPlatformFriendsComplete, self)
        end
    end
    --- END MODIFICATION
end

function FriendServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)

    --- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
    if PLATFORM_GEN9 == 1 then
        local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
        if DFMPlatformFriendManager then
            DFMPlatformFriendManager.OnReadPlatformFriendsCompleteDelegate:Remove(self._OnReadPlatformFriendsComplete, self)
        end
    end
    --- END MODIFICATION
end

-------------------------------------------------------------
--新版好友协议server接口
-------------------------------------------------------------

--拉取在线好友数量
function FriendServer:FetchOnLineFriendNum(delayCall)
    local OnCSFriendGetOnlineNumberRes = function(res)
        if res.result == 0 then
            if delayCall then
                delayCall(res.online_friend_number)
            end
        end
    end
    local req = pb.CSFriendGetOnlineNumberReq:New()

    req:Request(OnCSFriendGetOnlineNumberRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function FriendServer:SetTimerEnd()
    self._canGetFriend = true
    self._friendTimer = nil
end

function FriendServer:GetFriendDataByPanel()
    if not self._canGetFriend then
        return
    end
    self._canGetFriend = false
    self._friendTimer = Timer:NewIns(30, 1)
    local timerCallback = CreateCallBack(self.SetTimerEnd, self)
    self._friendTimer:AddListener(timerCallback)
    self._friendTimer:Start()
    self:FetchFriendSectionList()
end


--分页拉取好友列表
function FriendServer:FetchFriendSectionList()
    self.friendNickList = {}
    self.friendIdHash = {}
    self.openFriendIdHash = {}
    self.gameFriendIdHash = {}
    self.discordFriendIdHash = {}
    self.FriendList = {}
    self.GameFriendList = {}
    self.OpenFriendList = {}
    self.DiscordFriendList = {}
    self:_FetchFriendSectionList(0)

    -- 只拉取其中一种好友类型，以防一方拉取完成后刷新好友界面时另一种类型好友为空
    if PLATFORM_GEN9 == 1 then
        self:_FetchPlatformFriendSectionList()
    else
        --- 已绑定discord情况下，获取服务器缓存的discord好友
        if Module.Friend.Field:GetIsDiscordBinded() then
            self:SDKFetchDiscordFriendByServer(0)
        else
            self:_FetchOpenFriendSectionList(0)
        end
    end
    log("FriendServer:FetchFriendSectionList")
end

function FriendServer:GameFetchFriendList()
    self.GameFriendList = {}
    self.gameFriendIdHash = {}
    self:_FetchFriendSectionList(0)
end

--分页拉取游戏好友
function FriendServer:_FetchFriendSectionList(pagIndex)
    local OnCSFriendListRes = function(res)
        if res.result == 0 then
            if pagIndex == 0 and #self.GameFriendList > 0 then
                return
            end
            for _, friendInfo in ipairs(res.player_list) do
                local rankData = {}
                for _, info in ipairs(friendInfo.rank_player_aux_data) do
                    rankData[info.rank_data_type] = info.score
                end
                friendInfo.rank_player_aux_data = rankData
                if self.friendIdHash[friendInfo.player_id] == nil then
                    table.insert(self.FriendList, friendInfo)
                end
                table.insert(self.GameFriendList, friendInfo)
                self.friendNickList[friendInfo.nick_name] = friendInfo
                self.gameFriendIdHash[friendInfo.player_id] = friendInfo
                self.friendIdHash[friendInfo.player_id] = friendInfo
            end
            if res.total_page and pagIndex < res.total_page - 1 then
                loginfo("FriendServer:_FetchFriendSectionList", pagIndex)
                self:_FetchFriendSectionList(pagIndex + 1)
            else
                loginfo("FriendServer:_FetchFriendSectionList break", pagIndex)
                self._shouldPrint = false
                self.Events.evtFriendListGetEnd:Invoke()
                self.Events.evtFriendListReqSucess:Invoke(self.friendIdHash)
            end
        end
    end

    local req = pb.CSFriendListReq:New()
    req.page_index = pagIndex
    req.friend_type = FriendType.GameFriend
    req.should_print_tlog = self._shouldPrint
    req:Request(OnCSFriendListRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

--分页拉取平台好友 --只拉取不刷新
function FriendServer:_FetchOpenFriendSectionList(pagIndex)
    local OnCSFriendListRes = function(res)
        if res.result == 0 then
            if pagIndex == 0 and #self.OpenFriendList > 0 then
                return
            end
            for index, friendInfo in ipairs(res.player_list) do
                local rankData = {}
                for _, info in ipairs(friendInfo.rank_player_aux_data) do
                    rankData[info.rank_data_type] = info.score
                end
                friendInfo.rank_player_aux_data = rankData
                if self.friendIdHash[friendInfo.player_id] == nil then
                    table.insert(self.FriendList, friendInfo)
                end
                table.insert(self.OpenFriendList, friendInfo)
                self.friendNickList[friendInfo.nick_name] = friendInfo
                self.openFriendIdHash[friendInfo.player_id] = friendInfo
                self.friendIdHash[friendInfo.player_id] = friendInfo
            end
            if res.total_page and pagIndex < res.total_page - 1 then
                loginfo("FriendServer:_FetchOpenFriendSectionList", pagIndex)
                self:_FetchOpenFriendSectionList(pagIndex + 1)
            else
                self.Events.evtFriendListGetEnd:Invoke()
                loginfo("FriendServer:_FetchOpenFriendSectionList break", pagIndex)
            end
        end
    end

    local req = pb.CSFriendListReq:New()
    req.page_index = pagIndex
    req.friend_type = FriendType.OpenFriend
    req:Request(OnCSFriendListRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

-----------------------------------------------------------------------
--region Discord好友


--分页拉取Discord好友/向服务器请求版本 -- 未启用
function FriendServer:_FetchDiscordFriendSectionList(pagIndex)
    local OnCSDiscordFriendListRes = function(res)
        if res.result == 0 then
            if pagIndex == 0 and #self.DiscordFriendList > 0 then
                return
            end
            for _, friendInfo in ipairs(res.player_list) do
                local rankData = {}
                for _, info in ipairs(friendInfo.rank_player_aux_data) do
                    rankData[info.rank_data_type] = info.score
                end
                friendInfo.rank_player_aux_data = rankData
                if self.friendIdHash[friendInfo.player_id] == nil then
                    table.insert(self.FriendList, friendInfo)
                end
                table.insert(self.DiscordFriendList, friendInfo)
                self.friendNickList[friendInfo.nick_name] = friendInfo
                self.discordFriendIdHash[friendInfo.player_id] = friendInfo
                self.friendIdHash[friendInfo.player_id] = friendInfo
            end
            if res.total_page and pagIndex < res.total_page - 1 then
                loginfo("FriendServer:_FetchDiscordFriendSectionList", pagIndex)
                self:_FetchDiscordFriendSectionList(pagIndex + 1)
            else
                loginfo("FriendServer:_FetchDiscordFriendSectionList break", pagIndex)
                self.Events.evtFriendListGetEnd:Invoke()
            end
        end
    end

    local req = pb.CSDiscordFriendListReq:New()
    req.page_index = pagIndex
    req.friend_type = FriendType.Discord -- TODO 修改
    req:Request(OnCSDiscordFriendListRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

local DISCORD_FRIEND_PAGE_SIZE = 16

--- 1.前台通过SDK拉取Discord好友并和后台进行绑定
function FriendServer:SDKFetchDiscordFriendByClient(pagIndex)
    loginfo("FriendServer:Discord_SDKFetchDiscordFriendByClient pageIndex", pagIndex)

    local OnSDKFetchDiscordFriend = function(ret)
        local allFriendlist = ret.friendInfoList
        local gameFriendList = {}
        local allNum = 0    --- 总Discord好友数，包括未绑定游戏的好友

        for _, v in pairs(allFriendlist) do
            if isvalid(v) then
                allNum = allNum + 1
                if v.gid ~= "" then
                    local info = {player_id = v.gid, open_nick_name = v.displayName}
                    loginfo("FriendServer:Discord friendGid     : ", v.gid)
                    loginfo("FriendServer:Discord open_nick_name: ", v.displayName)
                    table.insert(gameFriendList, info)
                end
            end
        end
        loginfo("FriendServer:Discord_SDKFetchDiscordFriendByClient curPage friendNum: ", allNum)

        --- 拉取完毕，上传服务器
        if allNum == 0 then
            self:SDKFetchDiscordFriendByClient(0)
            self:SDKUpdateDiscordFriendToServer()
        else
            --- 填充数据，继续拉取
            for _, v in ipairs(gameFriendList) do
                table.insert(self.tempDiscordFriendList, v)
            end
            self:SDKFetchDiscordFriendByClient(pagIndex + 1)
        end
    end

    --- 防止反复调用
    if pagIndex == 1 then
        if self.bIsFetchDiscord then
            return
        end
        GameFriendMgr.OnDiscordQueryFriendNotifyEvent:Add(OnSDKFetchDiscordFriend)
        self.bIsFetchDiscord = true
    elseif pagIndex == 0 then   -- 取消绑定事件，结束拉取
        GameFriendMgr.OnDiscordQueryFriendNotifyEvent:Clear()
        self.bIsFetchDiscord = false
        return
    end
    --- Discord也是分页拉取，全部拉取之后再上报服务器
    UGameFriend.DiscordQueryFriends(pagIndex, DISCORD_FRIEND_PAGE_SIZE)
end

--- 2.先绑定
function FriendServer:SDKUpdateDiscordFriendToServer()
    local length = #self.tempDiscordFriendList
    loginfo("FriendServer:Discord_SDKUpdateDiscordFriendToServer tempDiscordFriendList Length ", length)

    local OnSDKUpdateDiscordFriend = function(res)
        if res.result == 0 then
            self.DiscordFriendList = {}
            self:SDKFetchDiscordFriendByServer(0)
        end
    end

    if length ~= 0 then
        local req = pb.CSFriendUploadDiscordMateListReq:New()
        req.discord_friend_list = self.tempDiscordFriendList
        req:Request(OnSDKUpdateDiscordFriend)
    end
end

--- 3.再拉取
function FriendServer:SDKFetchDiscordFriendByServer(pagIndex)
    loginfo("FriendServer:Discord_SDKFetchDiscordFriendByServer ", pagIndex)

    local OnCSDiscordFriendListRes = function(res)
        if res.result == 0 then
            if pagIndex == 0 and #self.DiscordFriendList > 0 then
                return
            end
            for _, friendInfo in ipairs(res.player_list) do
                local rankData = {}
                for _, info in ipairs(friendInfo.rank_player_aux_data) do
                    rankData[info.rank_data_type] = info.score
                end
                friendInfo.rank_player_aux_data = rankData
                if self.friendIdHash[friendInfo.player_id] == nil then
                    table.insert(self.FriendList, friendInfo)
                end
                table.insert(self.DiscordFriendList, friendInfo)
                self.friendNickList[friendInfo.nick_name] = friendInfo
                self.discordFriendIdHash[friendInfo.player_id] = friendInfo
                self.friendIdHash[friendInfo.player_id] = friendInfo
            end
            if res.total_page and pagIndex < res.total_page - 1 then
                loginfo("FriendServer:Discord_FetchDiscordFriendSectionList", pagIndex)
                self:_FetchDiscordFriendSectionList(pagIndex + 1)
            else
                loginfo("FriendServer:Discord_FetchDiscordFriendSectionList break", pagIndex)
                loginfo("FriendServer:Discord_FetchDiscordFriendSectionList DiscordFriendNum", #self.DiscordFriendList)
                self.Events.evtFriendListGetEnd:Invoke()
            end
        end
    end

    local req = pb.CSFriendGetDiscordMateCacheReq:New()
    req.page_index = pagIndex
    req:Request(OnCSDiscordFriendListRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

--endregion
-----------------------------------------------------------------------

--- BEGIN MODIFICATION @ VIRTUOS: 显示平台好友
--拉取平台好友
function FriendServer:_FetchPlatformFriendSectionList()
    if PLATFORM_GEN9 ~= 1 then
        return
    end

    local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
    if DFMPlatformFriendManager then
        DFMPlatformFriendManager:ReadFriendsList()
        loginfo("FriendServer:_FetchPlatformFriendSectionList")
    end
end

function FriendServer:_OnReadPlatformFriendsComplete(bWasSuccessful)
    if PLATFORM_GEN9 ~= 1 then
        return
    end

    -- 获取friend成功后才更新List
    if not bWasSuccessful then
        return
    end

    self.openFriendIdHash = {}
    self.OpenFriendList = {}

    loginfo("FriendServer:_OnReadPlatformFriendsComplete")
    local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
    if DFMPlatformFriendManager then
        local platformFriends = DFMPlatformFriendManager:GetPlatformFriends()
        for index, platformFriend in ipairs(platformFriends) do
            loginfo("FriendServer:_OnReadPlatformFriendsComplete foreach name: ", platformFriend.PlatformName)

            -- 为了兼容游戏中的好友数据，设置必要的默认值
            local newPlatformFriend = {}
            newPlatformFriend.bIsPlatformOnlyFriend = true
            newPlatformFriend.open_nick_name = platformFriend.PlatformName
            newPlatformFriend.nick_name = platformFriend.PlatformName
            newPlatformFriend.PlatformId = platformFriend.PlatformId
            newPlatformFriend.favorability = 0
            newPlatformFriend.friend_type = FriendType.OpenFriend
            newPlatformFriend.last_logout_time = 0
            if platformFriend.bIsOnline then
                newPlatformFriend.state = GlobalPlayerStateEnums.EPlayerState_Online
            else
                newPlatformFriend.state = GlobalPlayerStateEnums.EPlayerState_Offline
            end
	--- BEGIN MODIFICATION @ VIRTUOS: 补充平台 ID 数据
            newPlatformFriend.plat = Server.AccountServer:GetPlatIdType()
	--- END MODIFICATION
            table.insert(self.OpenFriendList, newPlatformFriend)
            self.openFriendIdHash[newPlatformFriend.PlatformId] = newPlatformFriend
        end
        -- 刷新好友UI
        self.Events.evtFriendListGetEnd:Invoke()
    end
end

-- 将用户添加到平台黑名单中
function FriendServer:AddPlayerToPlatformBlackList(playerId, fCallback)
    if not IsPS5Family() then
        return
    end
    loginfo("FriendServer:AddPlayerToPlatformBlackList playerId:", playerId)
    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
        local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
        if queryRes.bHasUID then
            local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
            if DFMPlatformFriendManager then
                DFMPlatformFriendManager.OnBlockedPlatformUserCompleteDelegate:Add(function(res)
                    if res then
                        loginfo("FriendServer:AddPlayerToPlatformBlackList res is ", res)
                        -- 结果返回后，再次查询平台黑名单，确定是否成功拉黑
                        self:ReqCheckPlayerIsInPS5PlatformBlockList(playerId, fCallback)
                    end
                end)
                DFMPlatformFriendManager:BlockPlayer(queryRes.UID)
            end
        end
    end
    Server.SocialServer:ReqQueryUID(ULuautils.GetUInt64String(playerId), OnReqQueryRes)
end

-- 将用户从平台黑名单中移除
function FriendServer:RemovePlayerFromPlatformBlackList(playerId, fCallback)
    if not IsPS5Family() then
        return
    end
    loginfo("FriendServer:RemovePlayerFromPlatformBlackList")
    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
        local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
        if queryRes.bHasUID then
            local DFMPlatformFriendManager = UDFMPlatformFriendManager.Get(GetWorld())
            if DFMPlatformFriendManager then
                DFMPlatformFriendManager.OnUnblockedPlatformUserCompleteDelegate:Add(function(res)
                    if res then
                        -- 结果返回后，再次查询平台黑名单，确定是否成功移除
                        self:ReqCheckPlayerIsInPS5PlatformBlockList(playerId, fCallback)
                    end
                end)
                DFMPlatformFriendManager:UnblockPlayer(queryRes.UID)
            end
        end
    end
    Server.SocialServer:ReqQueryUID(ULuautils.GetUInt64String(playerId), OnReqQueryRes)
end
--- END MODIFICATION

--拉黑玩家
function FriendServer:AddPlayerToBlackList(playerId)
    local OnCSFriendBlockPlayerRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendAddBlackList)
            self:FetchApplyPlayerList()
            self:FetchFriendGetBlackList()
            if self:CheckIsFriend(playerId) then
                self:FetchFriendSectionList()
            end
        end
    end

    local ReqAddPlayerToBlackList = function()
        local req = pb.CSFriendBlockPlayerReq:New()
        req.player_id = playerId
        req:Request(OnCSFriendBlockPlayerRes)
    end
    
    -- BEGIN MODIFICATION
    if not IsPS5Family() then
        ReqAddPlayerToBlackList()
    else
        local reqGetPlayerProfile = pb.CSAccountGetPlayerProfileReq:New()
        reqGetPlayerProfile.player_id = playerId
        reqGetPlayerProfile.client_flag = 0
        reqGetPlayerProfile:Request(function(res)
            if res.result == 0 and res.plat_id == Server.AccountServer:GetPlatIdType() then
                self:AddPlayerToPlatformBlackList(playerId, function(isInPlatformBlockList)
                    if isInPlatformBlockList then
                        ReqAddPlayerToBlackList()
                    end
                end)
            else
                ReqAddPlayerToBlackList()
            end
        end, { bEnableHighFrequency = true })
    end
    -- END MODIFICATION
end

--界面专用
function FriendServer:GetFriendPanelList()
    return self.FriendList
end

function FriendServer:GetGameFriendPanelList()
    return self.GameFriendList
end

function FriendServer:GetOpenFriendPanelList()
    return self.OpenFriendList
end

function FriendServer:GetDiscordFriendPanelList()
    return self.DiscordFriendList
end

-------------------------------------------------------------------------------

--缓存好友红点数量和好友列表，系统推荐好友
function FriendServer:FetchServerData()
end

function FriendServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse or gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.LobbyBHD then
        self:FetchFriendRedDot()
        self:FetchFriendSectionList()
        self:FetchFriendGetBlackList()
        if self.isInit then return end
        self:FetchRecommendFriendList()
        self.isInit = true
    end
end

function FriendServer:FetchFriendRedDot()
    local onCSFriendGetApplyListRes = function(res)
        if res.result == 0 then
            self.redDotNum = #res.apply_list
            self.Events.evtFetchRedDot:Invoke()
        end
    end
    -- todo 更多的好友网络事件

    local req = pb.CSFriendGetApplyListReq:New()
    req:Request(onCSFriendGetApplyListRes)
end

---添加好友
function FriendServer:AddFriend(playerID, applyMsg, applyType, delayCall, roomId, recommendId)
    roomId = setdefault(roomId, 0)
    local onCSFriendApplyAddRes = function(res)
        printtable(res, "AddFriend")
        if res.result == 0 then
            if delayCall then
                delayCall()
            end
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendAddApplySuccess)
            self.Events.evtSendAddSucess:Invoke(playerID)
        elseif res.result == 24028 then
            LuaGlobalEvents.evtServerShowCreditTip:Invoke(self.EXinyongType.AddFriendLimit)
        elseif res.result == -1 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.AddFriendFail)
        end
    end

    local req = pb.CSFriendApplyAddReq:New()
    req.player_id = playerID
    req.apply_msg = applyMsg
    req.room_id = roomId
    req.source = applyType
    req.recommend_id = recommendId
    req:Request(onCSFriendApplyAddRes,{bBuildEmptyRecv = false, bEnableHighFrequency = true})
    -- 发起添加好友经分上报
    LogAnalysisTool.DoSendFriendApplyLog(applyType)
end

---删除好友
function FriendServer:DeleteFriend(playerID)
    local onCSFriendDelRes = function(res)
        printtable(res, "DeleteFriend")
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendDel)
            self:FetchFriendSectionList()
            self.Events.evtFriendLishChange:Invoke()
        end
    end

    local req = pb.CSFriendDelReq:New()
    req.player_id = playerID
    req:Request(onCSFriendDelRes)
end

function FriendServer:RemarkFriendName(friendId, remarkName, delayCall)
    local OnCSFriendRemarksModifyRes = function(res)
        if res.result == 0 then
            if delayCall then
                delayCall()
            end
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TipsSuccess)
            if self.gameFriendIdHash[friendId] then
                self.gameFriendIdHash[friendId].remarks = remarkName
            end
            if self.friendIdHash[friendId] then
                self.friendIdHash[friendId].remarks = remarkName
            end
            if self.openFriendIdHash[friendId] then
                self.openFriendIdHash[friendId].remarks = remarkName
            end
            if self.discordFriendIdHash[friendId] then
                self.discordFriendIdHash[friendId].remarks = remarkName
            end

            local nickName = self.friendIdHash[friendId].nick_name
            self.friendNickList[nickName].remarks = remarkName

            for _, friendInfo in ipairs(self.FriendList) do
                if friendInfo.player_id == friendId then
                    friendInfo.remarks = remarkName
                    break
                end
            end

            for _, friendInfo in ipairs(self.GameFriendList) do
                if friendInfo.player_id == friendId then
                    friendInfo.remarks = remarkName
                    break
                end
            end

            for _, friendInfo in ipairs(self.OpenFriendList) do
                if friendInfo.player_id == friendId then
                    friendInfo.remarks = remarkName
                    break
                end
            end

            for _, friendInfo in ipairs(self.DiscordFriendList) do
                if friendInfo.player_id == friendId then
                    friendInfo.remarks = remarkName
                    break
                end
            end
        end
    end
    local req = pb.CSFriendRemarksModifyReq:New()
    req.friend_id = friendId
    req.remarks = tostring(remarkName)
    req:Request(OnCSFriendRemarksModifyRes)
end




---同意/拒绝好友申请 agree改为bool值
function FriendServer:ProcessNewFriendReq(playerID, agree)
    local onCSFriendResponseAddRes = function(res)
        if res.result == 0 then
            if agree then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendAdd)
                self:FetchFriendSectionList()
            else
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendRefuse)
            end
            self:FetchApplyPlayerList()
        elseif res.result == 24028 then
            LuaGlobalEvents.evtServerShowCreditTip:Invoke(self.EXinyongType.AddFriendLimit)
            self:FetchApplyPlayerList()
        elseif res.result == 24027 then
            self:FetchApplyPlayerList()
        end
    end

    local req = pb.CSFriendResponseAddReq:New()
    req.player_id = playerID
    req.agree = agree
    req:Request(onCSFriendResponseAddRes)
end

---面对面加好友
function FriendServer:AddFriendFaceToFace(key)
    local onCSFaceToFaceRoomTRes = function(res)
        if res.result == 0 and res.type == 1 then
            self.Events.evtFriendCreateRoom:Invoke(res.player_list)
        end
    end

    local req = pb.CSFaceToFaceRoomTReq:New()
    req.key = key
    req.type = 1
    req.is_join = true
    req:Request(onCSFaceToFaceRoomTRes)
end

--退出面对面加好友的房间
function FriendServer:QuitFriendFaceToFace(key)
    local OnCSFaceToFaceRoomTReq = function(res)
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendRoomQuit)
    end

    local req = pb.CSFaceToFaceRoomTReq:New()
    req.key = key
    req.type = 1
    req.is_join = false
    req:Request(OnCSFaceToFaceRoomTReq)
end

--一键添加面对面好友
function FriendServer:AddAllPlayerFaceToFace(key)
    local onCSFaceToFaceAddAllFriendTRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.FriendAddAllSuccess)
            self.Events.evtAddRoomAllPlayer:Invoke()
        end
    end

    local req = pb.CSFaceToFaceAddAllFriendTReq:New()
    req.key = key
    req:Request(onCSFaceToFaceAddAllFriendTRes)
end

---有数据更新时拉取好友列表
function FriendServer:FetchFriendList()
    local onCSFriendListRes = function(res)
        printtable(res, "FetchFriendList")
        if res.result == 0 then
            --self:_HashFriendTable(res.player_list)
            self.Events.evtFriendList:Invoke(res.player_list)
            self.getGiftNum = res.daily_gift_count
        end
    end
    local req = pb.CSFriendListReq:New()
    req:Request(onCSFriendListRes)
end

---打开界面是拉取好友列表
function FriendServer:ReFetchFriendList(fOnFetchFriendListFinished)
    local onCSFriendListRes = function(res)
        printtable(res, "ReFetchFriendList")
        if res.result == 0 then
            --self:_HashFriendTable(res.player_list)
            self.getGiftNum = res.daily_gift_count
            if fOnFetchFriendListFinished then
                fOnFetchFriendListFinished(res.player_list)
            end
        end
    end
    local req = pb.CSFriendListReq:New()
    req:Request(onCSFriendListRes,{bEnableHighFrequency = true})
end

---批量拉取好友状态数据
function FriendServer:ReFetchFriendStateList(fOnFetchFriendStateListFinished, playerIdList)
    loginfo("FriendServer:ReFetchFriendStateList")
    logtable(playerIdList,true)
    local OnCSStateBatchGetInfoRes = function(res)
        if res.result == 0 then
            local playState = {}
            for _, info in ipairs(res.player_info) do
                playState[info.player_id] = info
            end
            if fOnFetchFriendStateListFinished then
                fOnFetchFriendStateListFinished(playState)
            end
        end
    end
    local req = pb.CSStateBatchGetInfoReq:New()
    req.player_id = playerIdList
    req:Request(OnCSStateBatchGetInfoRes)
end

--拉取系统推荐玩家
function FriendServer:FetchRecommendFriendList()
    local onCSFriendRecommendRes = function(res)
        if res.result == 0 then
            self.Events.evtRecommendFriendList:Invoke(res.player_list)
            self.recommendList = res.player_list
        end
    end

    local req = pb.CSFriendRecommendReq:New()
    req:Request(onCSFriendRecommendRes)
end

function FriendServer:GetRecommendFriendList(isChange)
    loginfo("GetRecommendFriendList", #self.recommendList)
    if #self.recommendList == 0 or isChange == true then
        self:FetchRecommendFriendList()
    else
        self.Events.evtRecommendFriendList:Invoke(self.recommendList)
    end
end


--拉取在线玩家
function FriendServer:FetchOnLinePlayerList()
    local onCSOnlinePlayerListRes = function(res)
        self.Events.evtOnlinePlayerList:Invoke(res.player_list)
    end

    local req = pb.CSFriendOnlinePlayerListReq:New()
    req:Request(onCSOnlinePlayerListRes)
end


--拉取申请列表
function FriendServer:FetchApplyPlayerList()
    local onCSFriendGetApplyListRes = function(res)
        if res.result == 0 then
            self.redDotNum = #res.apply_list
            self.Events.evtApplyPlayerList:Invoke(res.apply_list)
            self.Events.evtFetchRedDot:Invoke()
        end
    end

    local req = pb.CSFriendGetApplyListReq:New()
    req:Request(onCSFriendGetApplyListRes)
end

--查找玩家
function FriendServer:FetchFindPlayer(playerID, nickName, playerIdStr)
    playerIdStr = setdefault(playerIdStr, "")
    local onCSFriendSearchRes = function(res)
        if res.result == 0 then
            self.Events.evtFindPlayerList:Invoke(res.player_info)
        end
    end
    local req = pb.CSFriendSearchReq:New()
    req.search_player_id = playerID
    req.search_player_nick = tostring(nickName)
    req.search_player_id_str = playerIdStr
    req:Request(onCSFriendSearchRes, {bEnableHighFrequency = true})
end

--拉取最近同玩好友列表
function FriendServer:FetchCoPlayerList()
    local OnCSCoPlayerFriendListRes = function(res)
        printtable(res, "FetchCoPlayerList")
        if res.result == 0 then
            self.Events.evtFriendCoPlayerList:Invoke(res.player_list)
        end
    end
    loginfo("CSCoPlayerFriendListReq")
    local req = pb.CSCoPlayerFriendListReq:New()
    req:Request(OnCSCoPlayerFriendListRes)
end

function FriendServer:GetNewFriendMsgNum()
    return self.redDotNum
end

---拉取黑名单列表
function FriendServer:FetchFriendGetBlackList()
    local OnCSFriendGetBlackListRes = function(res)
        if res.result == 0 then
            printtable(res, "FetchFriendGetBlackList")
            self.blackListData = res.black_list
            self:_HashBlackTable(res.black_list)
            self.Events.evtBlackList:Invoke(res.black_list)
        end
    end
    local req = pb.CSFriendGetBlackListReq:New()
    req:Request(OnCSFriendGetBlackListRes)
end

function FriendServer:GetBlackListData()
    return self.blackListData
end

---移除黑名单列表
function FriendServer:FetchFriendRemoveBlackList(playerID, removeAll)
    local OnCSFriendRemoveBlackListRes = function(res)
        if res.result == 0 then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RemoveBlackListSuccess)
            self:FetchFriendGetBlackList()
        end
    end
    
    local ReqRemovePlayerFromBlackList = function()
        local req = pb.CSFriendRemoveBlackListReq:New()
        req.remove_player = playerID
        req.remove_all = removeAll
        req:Request(OnCSFriendRemoveBlackListRes)
    end
    
    --  BEGIN MODIFICATION - VIRTUOS
    if not IsPS5Family() then
        ReqRemovePlayerFromBlackList()
    else
        if removeAll then
        else
            local reqGetPlayerProfile = pb.CSAccountGetPlayerProfileReq:New()
            reqGetPlayerProfile.player_id = playerID
            reqGetPlayerProfile.client_flag = 0
            reqGetPlayerProfile:Request(function(res)
                if res.result == 0 and res.plat_id == Server.AccountServer:GetPlatIdType() then
                    self:RemovePlayerFromPlatformBlackList(playerID, function(isInPlatformBlockList)
                        if isInPlatformBlockList == false then
                            ReqRemovePlayerFromBlackList()
                        end
                    end)
                else
                    ReqRemovePlayerFromBlackList()
                end
            end, { bEnableHighFrequency = true })
        end
    end
    -- END MODIFICATION - VIRTUOS
end

---好友申请批量操作
function FriendServer:FetchFriendResponseAllAdd(agree)
    local OnCSFriendResponseAllAddRes = function(res)
        if res.result == 0 then
            if agree then
                self:FetchFriendSectionList()
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.AgreeAllApply)
            else
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RefuseAllApply)
            end
        elseif res.result == 24028 then
            LuaGlobalEvents.evtServerShowCreditTip:Invoke(self.EXinyongType.AddFriendLimit)
            self:FetchApplyPlayerList()
        end
        self:FetchApplyPlayerList()
        self.Events.evtFetchRedDot:Invoke()
    end

    local req = pb.CSFriendResponseAllAddReq:New()
    req.agree = agree
    req:Request(OnCSFriendResponseAllAddRes)
end

--拉取好友邮件
function FriendServer:FetchFriendMailList()
    local OnCSFriendGetDailyGiftListRes = function(res)
        if res.result == 0 then
            printtable(res, "FetchFriendMailList")
        end
    end

    local req = pb.CSFriendGetDailyGiftListReq:New()
    req:Request(OnCSFriendGetDailyGiftListRes)
end


function FriendServer:PlayerPraise(playerId, delayCall, place)
    local OnCSFriendPraiseDailyRes = function(res)
        if res.result == 0 then
            if delayCall then
                delayCall()
            end
        end
    end

    local req = pb.CSFriendPraiseDailyReq:New()
    req.be_praise_player_id = playerId
    req.thumb_place = place
    req:Request(OnCSFriendPraiseDailyRes)
end

function FriendServer:DoPlayerPraise(playerId, delayCall)
    local OnCSPlayerPraiseRes = function(res)
        if res and res.result == 0 then
            if delayCall then
                delayCall()
            end
        end
    end

    local req = pb.CSPlayerInfoPraiseReq:New()
    req.praise_type = 0
    req.be_praised_player_id = playerId
    req:Request(OnCSPlayerPraiseRes)
end

function FriendServer:UpdatePraiseData(playerId)
    if self:CheckIsGameFriend(playerId) then
        for _, friendInfo in ipairs(self.GameFriendList) do
            if friendInfo.player_id == playerId then
                friendInfo.IsPraise = true
            end
        end
    end
    if self:CheckIsOpenFriend(playerId) then
        for _, friendInfo in ipairs(self.OpenFriendList) do
            if friendInfo.player_id == playerId then
                friendInfo.IsPraise = true
            end
        end
    end
    -- if self:CheckIsDiscordFriend(playerId) then
    --     for _, friendInfo in ipairs(self.DiscordFriendList) do
    --         if friendInfo.player_id == playerId then
    --             friendInfo.IsPraise = true
    --         end
    --     end
    -- end
end

function FriendServer:UpdateFavorabilityData(playerId, changeValue)
    if self:CheckIsGameFriend(playerId) then
        for _, friendInfo in ipairs(self.GameFriendList) do
            if friendInfo.player_id == playerId then
                friendInfo.favorability = friendInfo.favorability + changeValue
            end
        end
    end
end

function FriendServer:OnCSFriendChangeNtf(res)
    printtable(res, "OnCSFriendChangeNtf")
    if res.change_type == FriendChangeType.FriendAdd or 
    res.change_type == FriendChangeType.FriendDel then
        self:FetchFriendSectionList()
        self:FetchApplyPlayerList()
        self.Events.evtFriendLishChange:Invoke()
    elseif res.change_type == FriendChangeType.FriendApply then
        self.redDotNum = self.redDotNum + 1
        self.Events.evtFetchRedDot:Invoke()
        self:FetchApplyPlayerList()
    elseif res.change_type == FriendChangeType.FriendGift then
        --todo 邮件红点
    end
end

--QQ邀请好友组队Ark 后端邀请
function FriendServer:ReqQQArkInvite(teamId, playerId, fCallback)
    local OnCSFriendArkTeamRes = function(res)
        if res.result == 0 then
            if fCallback then
                fCallback(res)
            end
        end
    end
    local req = pb.CSFriendArkTeamReq:New()
    req.teamid = teamId
    req.friend_player_id = playerId
    req:Request(OnCSFriendArkTeamRes)
end


--QQ邀请好友组队Ark --客户端拉起邀请
function FriendServer:GetQQArkInviteData(teamId, fCallback)
    local OnCSFriendClientArkTeamRes = function(res)
        if res.result == 0 then
            if fCallback then
                fCallback(res)
            end
        end
    end
    local req = pb.CSFriendClientArkTeamReq:New()
    req.teamid = teamId
    req:Request(OnCSFriendClientArkTeamRes)
end

--[[
function FriendServer:_HashFriendTable(friendList)
    self.friendList = {}
    self.friendNickList = {}
    for _, friendInfo in ipairs(friendList) do
        self.friendNickList[friendInfo.nick_name] = friendInfo
        self.friendList[friendInfo.player_id] = friendInfo
    end
end
]]
function FriendServer:_HashBlackTable(blackList)
    self.blackList = {}
    for _, playerInfo in ipairs(blackList) do
        self.blackList[playerInfo.player_id] = true
    end
end

--获取好友的缓存数据
function FriendServer:GetDailyGiftNum()
    return self.getGiftNum
end

function FriendServer:GetFriendList()
    return self.friendIdHash
end

function FriendServer:GetFriendNickList()
    return self.friendNickList
end

function FriendServer:GetBlackList()
    return self.blackList
end

--得到该好友的好感度
function FriendServer:GetFriendFav(playerId)
    if self.friendIdHash[playerId] then
        return self.friendIdHash[playerId].favorability
    end
    return 0
end

--拿该好友是否为隐身状态
function FriendServer:GetFriendLoginInvisible(playerId)
    if self.friendIdHash[playerId] then
        return self.friendIdHash[playerId].friend_invisible_login_switch
        -- return true
    end

    return false
end

function FriendServer:CheckIsFriend(playerId)
    local friendIdHash = self:GetFriendList()
    if not friendIdHash[tonumber(playerId)] then
        return false
    end
    return true
end

function FriendServer:CheckIsGameFriend(playerId)
    if not self.gameFriendIdHash[tonumber(playerId)] then
        return false
    end
    return true
end

function FriendServer:GetFriendRemarkById(playerId)
    if self:CheckIsGameFriend(tonumber(playerId)) then
        return self.gameFriendIdHash[tonumber(playerId)].remarks
    end
    if self:CheckIsOpenFriend(tonumber(playerId)) then
        return self.openFriendIdHash[tonumber(playerId)].remarks
    end
    if self:CheckIsDiscordFriend(tonumber(playerId)) then
        return self.discordFriendIdHash[tonumber(playerId)].remarks
    end
    return ""
end

function FriendServer:CheckIsOpenFriend(playerId)
    if not self.openFriendIdHash[tonumber(playerId)] then
        return false
    end
    return true
end

function FriendServer:CheckIsDiscordFriend(playerId)
    if not self.discordFriendIdHash[tonumber(playerId)] then
        return false
    end
    return true
end

function FriendServer:CheckIsBlack(playerId)
    local blackList = self:GetBlackList()
    if not blackList[tonumber(playerId)] then
        return false
    end
    return true
end

function FriendServer:CheckIsFriendByNick(nickName)
    local friendNickList = self:GetFriendNickList()
    if not friendNickList[nickName] then
        return false
    end
    return true
end

-----------------------------------------------------------------------
--region 平台好友Check

-- BEGIN MODIFICATION - VIRTUOS
-- 检查玩家是否在平台黑名单内
function FriendServer:CheckIsInPlatformBlockList(playerId)
    if not IsPS5Family() then
        return false
    end
    if not self.platformBlockList[tonumber(playerId)] then
        return false
    end
    return true
end

-- 获取与目标玩家之间的通信权限
function FriendServer:ReqFriendSpeechJudgment(playerId, fCallback)
    loginfo("FriendServer:ReqFriendSpeechJudgment ", playerId)
    local OnCSFriendSpeechJudgmentRes = function(res)
        if res.result == 0 then
            loginfo("OnCSFriendSpeechJudgmentRes: ", res.IsSpeech)
            if fCallback then
                fCallback(res.IsSpeech)
            end
        end
    end
    local req = pb.CSFriendSpeechJudgmentReq:New()
    req.player_speech = Server.AccountServer:GetPlayerId()
    req.player_listen = playerId
    req:Request(OnCSFriendSpeechJudgmentRes,{bEnableHighFrequency=true})
end
-- 获取PS5平台的黑名单列表（黑名单列表中的Id为openId）
function FriendServer:ReqQueryPS5PlatformBlockList(fCallback)
    if not IsPS5Family() then
        return
    end
    loginfo("FriendServer:ReqQueryPS5PlatformBlockList")
    if IsBuildRegionGlobal() and IsPS5Family() then
        local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        local queryInfo = UDFMGameUrlGeneratorIns:CreateQueryPS5BlockListInfo(Server.AccountServer:GetPlayerIdStr())
        local HttpModule = FHttpModule.Get()
        local newHttpRequest = HttpModule:CreateRequest()
        local httpBindDelegate = newHttpRequest:OnProcessRequestComplete()
        httpBindDelegate:Bind(fCallback)
        newHttpRequest:SetURL(queryInfo.Url)
        newHttpRequest:SetVerb(queryInfo.Verb)
        newHttpRequest:SetContentAsString(queryInfo.RequestBody)
        newHttpRequest:SetHeader(queryInfo.HeaderKey, queryInfo.HeaderValue)
        newHttpRequest:ProcessRequest()
    end
end

-- 查询目标玩家是否在PS5平台的黑名单内
function FriendServer:ReqCheckPlayerIsInPS5PlatformBlockList(openId, fCallback)
    if not IsPS5Family() then
        return
    end
    loginfo("FriendServer:ReqCheckPlayerIsInPS5PlatformBlockList playerid is", openId)
    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
        local isInPS5BlockList = false
        local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        queryRes = UDFMGameUrlGeneratorIns:GenerateQueryPS5PlatformBlockListRes(HttpResponse:GetContentAsString())
        for _, blockPlayerId in ipairs(queryRes.BlockOpenIdList) do
            loginfo("FriendServer:ReqCheckPlayerIsInPS5PlatformBlockList blockPlayerId is", blockPlayerId)
            loginfo("FriendServer:ReqCheckPlayerIsInPS5PlatformBlockList PlayerIdStr is", ULuautils.GetUInt64String(openId))
            if ULuautils.GetUInt64String(openId) == blockPlayerId then
                isInPS5BlockList = true
            end
        end
        loginfo("FriendServer:ReqCheckPlayerIsInPS5PlatformBlockList res is", isInPS5BlockList)
        fCallback(isInPS5BlockList)
    end
    self:ReqQueryPS5PlatformBlockList(OnReqQueryRes)
end
-- END MODIFICATION - VIRTUOS

--endregion
-----------------------------------------------------------------------

return FriendServer



-- -----------------------
-- ---发送消息
-- -----------------------
-- ---世界频道发消息
-- function FriendServer:SendChatMassege(chatChannelType,chatMsgType,msg,propInfo)
--     local chatMsg = self:_GetChstMsg(chatMsgType,msg,propInfo)

--     if chatChannelType == ChatChannelType.WorldChat then
--         local onCSWorldChatSendReq = function(res)
--         end

--         local req = pb.CSWorldChatSendReq:New()
--         req.content =  chatMsg
--         req:Request(onCSWorldChatSendReq)
--     end
-- end

-- function FriendServer:_GetChstMsg(chatMsgType,msg,propInfo)
--     local playerinfo = {
--         player_id = Server.AccountServer:GetPlayerId(),
--         nick_name = Facade.ConfigManager:GetString("lastUserNickName", ""),
--         pic_url = Server.AccountServer:GetProfilePicUrl(),
--     }

--     local chatMsg = {   timestamp = TimeUtil.GetCurrentTime(),
--                         msg_type = chatMsgType,
--                         content = msg,
--                         sender = playerinfo,
--                         prop = propInfo
--                     }

--     return chatMsg
-- end

-- -----------------------
-- ---接收消息
-- -----------------------
-- function FriendServer:_OnCSChatChannelMsgNtf(ntf)
--     if ntf.channel_type == ChatChannelType.WorldChat then
--         self.Events.evtReceiveNewMsg:Invoke(ntf.msg_array)
--     end
-- end