----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SafeHouse3DExitPanel : LuaUIBaseView
local SafeHouse3DExitPanel = ui("SafeHouse3DExitPanel")
local UDFMGameHudDelegates = import("DFMGameHudDelegates")
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 输入设备相关
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
-- END MODIFICATION

local function log(...)
    loginfo("[SafeHouse3DExitPanel]", ...)
end

local SafeHouse3DButtons = {
    {
        ButtonText =  Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelResetText,
        CallBack = function (self)
            self:_OnResetBtnClicked()
        end
    },
    {
        ButtonText = Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelSettingText,
        CallBack = function (self)
            self:_OnSettingBtnClicked()
        end
    },
    {
        ButtonText = Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelExitText,
        CallBack = function (self)
            self:_OnExit3DSafeHouseBtnClicked()
        end
    },
    {
        ButtonText = Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelQuitGameText,
        CallBack = function (self)
            self:_OnExitGameBtnClicked()
        end
    }
}

local RangeButtons = {
    {
        ButtonText =  Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelSettingText,
        CallBack = function (self)
            self:_OnSettingBtnClicked()
        end
    },
    {
        ButtonText = Module.IrisSafeHouse.Config.Loc.RangeExitPanelLevelRangeText,
        CallBack = function (self)
            self:_OnLevelRangeBtnClicked()
        end
    },
    --- SHE3要求删除
    -- {
    --     ButtonText = Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelQuitGameText,
    --     CallBack = function (self)
    --         self:_OnExitGameBtnClicked()
    --     end
    -- }
}

local CollectionRoomButtons = {
    {
        ButtonText =  Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelSettingText,
        CallBack = function (self)
            self:_OnSettingBtnClicked()
        end
    },
    {
        ButtonText = Module.IrisSafeHouse.Config.Loc.ExitCollectionRoomText,
        ---@param self SafeHouse3DExitPanel
        CallBack = function (self)
            self:_OnLeaveCollectionRoomBtnClicked()
        end
    },
    --- SHE3要求删除
    -- {
    --     ButtonText = Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelQuitGameText,
    --     CallBack = function (self)
    --         self:_OnExitGameBtnClicked()
    --     end
    -- }
}

function SafeHouse3DExitPanel:Ctor()
    log("Ctor")
    self._wtButtonRootVerticalBox = self:Wnd("DFVerticalBox_89")
    self._wtCanvasDraw = self:Wnd("DFCanvasPanel_Interactive")

    --BEGIN MODIFICATION @ VIRTUOS: 底部确认的按键提示
    self._wtTabSummaryContainer = self:Wnd("wtInputSummaryHBox", UIWidgetBase)
    --END MODIFICATION
    self._wtIdcDesc = self:Wnd("DFRichTextBlock", UITextBlock)
    -- 根据当前场景选择要生成buttons列表
    local buttonsToSpawn = {}
    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if Server.IrisSafeHouseServer.bIsInRange then
        buttonsToSpawn = RangeButtons
    elseif ItemOperaTool.bIsInCollectionRoom then
        buttonsToSpawn = CollectionRoomButtons
    else
        if currentGameFlow == EGameFlowStageType.SafeHouse then
            buttonsToSpawn = SafeHouse3DButtons
        end
    end

    --BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        for i = #buttonsToSpawn, 1, -1 do
            if buttonsToSpawn[i].ButtonText == Module.IrisSafeHouse.Config.Loc.SafeHouseExitPanelQuitGameText then
                table.remove(buttonsToSpawn, i)
                break
            end
        end
    end
    --END MODIFICATION

    -- 添加buttons
    local firstButton = true
    for _, buttonConfig in ipairs(buttonsToSpawn) do
        if (firstButton == false) then
            local _wtDividerWeak = Facade.UIManager:AddSubUI(self, UIName2ID.SafeHouse3DExitPanelDivider, self._wtButtonRootVerticalBox);
        end
        firstButton = false

        local _wtItemWeak = Facade.UIManager:AddSubUI(self, UIName2ID.SafeHouse3DExitPanelItem, self._wtButtonRootVerticalBox, nil, buttonConfig.ButtonText);
        local _wtItem = getfromweak(_wtItemWeak)
        local _wtButton = _wtItem:Wnd("DFButton_SwitchModeItem", UIButton);
        _wtButton:Event("OnClicked", buttonConfig.CallBack, self)
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 绑定多输入设备切换事件
    if IsHD() then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        self._tmpActionRegs = {}
    end
    --- END MODIFICATION
end

function SafeHouse3DExitPanel:OnOpen()
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandlePostProcessMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
    self:_UpdateIdcInfo()
end

function SafeHouse3DExitPanel:OnClose()
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandlePostProcessMouseButtonUpEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
end

function SafeHouse3DExitPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
	local sceenPos = mouseEvent:GetScreenSpacePosition()
	local geometry = self._wtCanvasDraw:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    if not isUnder then
        self:_ClosePanel()
    end
end

function SafeHouse3DExitPanel:_OnSettingBtnClicked()
    log("_OnSettingBtnClicked")

    self:_ClosePanel()
    Module.SystemSetting:ShowSystemSettingMainView()
end

function SafeHouse3DExitPanel:_OnLevelRangeBtnClicked()
    log("_OnLevelRangeBtnClicked")

    self:_ClosePanel()
    Module.Range:LeaveRange()
end

function SafeHouse3DExitPanel:_OnLeaveCollectionRoomBtnClicked()
    log("_OnLeaveCollectionRoomBtnClicked")

    self:_ClosePanel()
    Module.CollectionRoom:LeaveCollectionRoom(ECollectionRoomLeaveFrom.Setting)
end

function SafeHouse3DExitPanel:_OnResetBtnClicked()
    log("_OnResetBtnClicked")

    self:_ClosePanel()
    Module.IrisSafeHouse:TeleportTo3DSafeHouseDefaultLoc()
end

function SafeHouse3DExitPanel:_OnExit3DSafeHouseBtnClicked()
    log("_OnExit3DSafeHouseBtnClicked")

    self:_ClosePanel()
    Module.IrisSafeHouse:Leave3DSafeHouse(true)
end

function SafeHouse3DExitPanel:_OnExitGameBtnClicked()
    log("_OnExitGameBtnClicked")

    Module.SystemSetting:RequestQuitGame(false)
end

function SafeHouse3DExitPanel:_ClosePanel()
    log("_ClosePanel")

    Facade.UIManager:CloseUI(self)
end

function SafeHouse3DExitPanel:OnNavBack()
    self:_ClosePanel()

    return true
end

function SafeHouse3DExitPanel:OnShowBegin()
    
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadInputs(true)
    end
    -- END MODIFICATION
end

function SafeHouse3DExitPanel:OnHideBegin()

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadInputs(false)
    end
    -- END MODIFICATION
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function SafeHouse3DExitPanel:_EnableGamepadInputs(bEnable)
    if not IsHD() then
        return 
    end
    
    if bEnable then
        self._DFCanvasPanel_Interactive = self:Wnd("DFCanvasPanel_Interactive", UIWidgetBase)
        self._DFVerticalBox = self:Wnd("DFVerticalBox_89", UIWidgetBase)
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._DFCanvasPanel_Interactive, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:AddNavWidgetToArray(self._DFVerticalBox)
                self._wtNavGroup:MarkIsStackControlGroup()
            end
        end
	    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)

        self:AddSummaries({"Select", "Back"})
    else
        if self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end
        self:RemoveSummaries()
    end
end

function SafeHouse3DExitPanel:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return
    end

    if InputType == EGPInputType.Gamepad then
        -- 设置底部按键Icon提示
        if self._wtTabSummaryContainer then
            self._wtTabSummaryContainer:Visible()
        end
    else
        if self._wtTabSummaryContainer then
            self._wtTabSummaryContainer:Collapsed()
        end
    end
end

-- 动态添加按键提示（无功能，仅UI显示）
function SafeHouse3DExitPanel:AddSummaries(actionNames)
    if not IsHD() then
        return 
    end

    self:RemoveSummaries()
    for _, actionName in ipairs(actionNames) do
        local weakUIIns, instanceId
        weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.InputSummaryItemHD, self._wtTabSummaryContainer)
        local summaryItem = getfromweak(weakUIIns)
        summaryItem:SetData(actionName, nil, false, false, nil, nil)
        summaryItem:SetPadding(FMargin(0, 0, 30, 0))
        
        table.insert(
            self._tmpActionRegs,
            {
                actionName = actionName,
                summaryItem = summaryItem,
                summaryItemInsId = instanceId,
            }
        )
    end

    if not WidgetUtil.IsGamepad() then
        self._wtTabSummaryContainer:Collapsed()
    end
end

--移除所有动态添加的按键提示
function SafeHouse3DExitPanel:RemoveSummaries()
    if not IsHD() then
        return 
    end

    for _, reg in ipairs(self._tmpActionRegs) do
        if reg.summaryItemInsId then
            local weakUIIns = Facade.UIManager:RemoveSubUI(self, UIName2ID.InputSummaryItemHD, reg.summaryItemInsId)
            local uiIns = getfromweak(weakUIIns)
            if uiIns then
                uiIns:RemoveBindings()
            end
            reg.summaryItemInsId = nil
        end
    end
    table.empty(self._tmpActionRegs)
end
-- END MODIFICATION

function SafeHouse3DExitPanel:_UpdateIdcInfo()
    local bEnableSwitch = Module.NetworkBusiness:IsEnableSwitchIDC()
    if bEnableSwitch then
        local callback = CreateCallBack(function(self, res)
           if res and res.match_area and res.rtt then
                local desc = Module.NetworkBusiness:GetIdcDesc(res.match_area, res.rtt)
                if self._wtIdcDesc then
                    self._wtIdcDesc:Visible()
                    self._wtIdcDesc:SetText(desc)
                end
           else
                if self._wtIdcDesc then
                    self._wtIdcDesc:Collapsed()
                end
           end
        end, self)
        Module.NetworkBusiness:GetSelectIdcInfo(callback)
    else
        self._wtIdcDesc:Collapsed()
    end
end

return SafeHouse3DExitPanel