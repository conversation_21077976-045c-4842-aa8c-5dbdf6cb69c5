----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerRes)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class UIManager : ManagerBase
local UIManager = {}
---------------------------------------------------------------------------------
--- UIManager关于UMG资源CDO的管理逻辑拆分
---------------------------------------------------------------------------------
--------------------------------------------------------------------------
--- 多资源Stub配合策略
---
---*  三种资源类型
---
---  [常驻] 模块OnLoadModule时，为对外同步创建预加载的常驻资源，极少（存活跨GF）
---  [Loading] 模块监听Loading变化时，在指定Loading为下一阶段准备的资源（存活跨GF）
---  [运行] （不在以上Stub内时）包含运行时打开的、动态加载子UI的资源（单GF内存活）
---
--- *  self.stubPermanentUIRes
---     *对应接口 Facade.UIManager:PreloadPermanentUIAssetList
---     *引用ModuleManager在各个Module:OnLoadModule()时[常驻类型]的资源
---     *和Module.强相关，通常是对外开放创建的子UI
---     *跟随GameFlow切换时ModuleManager的[配置策略]进行卸载
---
--- *  self.stubLoadingUIRes
---     *对应接口 Facade.UIManager:PreloadLoadingUIAssetList
---     *引用ModuleManager在各个Module关心Loading时[非常驻类型]的资源
---     *在入局出局Loading[全部]卸载，GameFlow切换[不清理]
---
--- *  self.stubRuntimeUIRes
---     *引用当前GameFlow打开过的UI资源
---     *跟随GameFlow切换时[全部]卸载
---
--- *  self.stubRuntimeSubUIRes
---     *引用当前GameFlow打开过的SubUI资源
---     *跟随GameFlow切换时[全部]卸载
---
--- *  self.stubDynamicSubUIRes
---     *引用当前GameFlow使用定制化dynamicSubUIList打开过的子UI资源
---     *跟随GameFlow切换时[全部]卸载
---
--- *  self.stubUIImageRes
---     *引用当前GameFlow使用UIImage显示的图片资源
---     *跟随GameFlow切换时[全部]卸载
--------------------------------------------------------------------------
local AnalysisUtil = require "DFM.YxFramework.Util.AnalysisUtil"

function UIManager:Ctor()
    local bWeakRef = false
    --- UI相关资源存根
    ---@type ResStub
    self.stubPermanentUIRes = ResStub:NewIns("stubPermanentUIRes", bWeakRef)
    ---@type ResStub
    self.stubLoadingUIRes = ResStub:NewIns("stubLoadingUIRes", bWeakRef)
    ---@type ResStub
    self.stubRuntimeUIRes = ResStub:NewIns("stubRuntimeUIRes", bWeakRef)
    ---@type ResStub
    self.stubRuntimeSubUIRes = ResStub:NewIns("stubRuntimeSubUIRes", bWeakRef)
    ---@type ResStub
    self.stubDynamicSubUIRes = ResStub:NewIns("stubDynamicSubUIRes", bWeakRef)
    ---@type ResStub
    self.stubUIImageRes = ResStub:NewIns("stubUIImageRes")
    ---@type table<idx, subUIList>
    self._mapUIId2DynamicSubUIIdList = {}
end

---------------------------------------------------------------------------------
--- UI BP Resource 递归查询SubUIList与DynamicSubUIList
---------------------------------------------------------------------------------
local function recursiveGetUIList(UINavID, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)
    fullPathList = fullPathList or {}
    skipPathList = skipPathList or {}
    mapFullPath2IsSubUIRes = mapFullPath2IsSubUIRes or {}
    allSubUINavIDList = allSubUINavIDList or {}

    local uiSettings = UITable[UINavID]
    if uiSettings ~= DummyUISettings then
        -- assertlog(uiSettings,"recursiveGetUIList load ui asset failed, UISettings is nil, Please check UITable, UINavID:"..UINavID)
        if uiSettings then
            if uiSettings.UILayer == EUILayer.Sub then
                local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
                if fullPath then
                    mapFullPath2IsSubUIRes[fullPath] = true
                end
                table.insert(allSubUINavIDList, UINavID)
            end
            if not Facade.UIManager:CheckUIHasBeenLoaded(UINavID) then
                table.insert(fullPathList, UIName2ID.GetBPFullPathByID(UINavID))
                if not VersionUtil.IsShipping() then
                    loginfo('[ Low Memory Log - AsyncLoadCheck ] No cache found in All UIManager stubs, start loading', UIName2ID.GetBPFullPathByID(UINavID))
                end
            else
                table.insert(skipPathList, UIName2ID.GetBPFullPathByID(UINavID))
            end
            if uiSettings.SubUIs then
                for _, subUIId in ipairs(uiSettings.SubUIs) do
                    recursiveGetUIList(subUIId, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)
                end
            end
        else
            assertlog(uiSettings,"recursiveGetUIList load ui asset failed, UISettings is nil, Please check UITable, UINavID:"..UINavID)
            logerror('反向依赖了尚未初始化的模块的UI，UISettings is nil 请检查', UIName2ID.GetNameByID(UINavID), debug.traceback())
        end
    end
    return fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList
end

local function recursiveCheckAnyUIUnload(UINavID)
    local uiSettings = UITable[UINavID]
    if uiSettings ~= DummyUISettings then
        -- assertlog(uiSettings,"recursiveGetUIList load ui asset failed, UISettings is nil, Please check UITable, UINavID:"..UINavID)
        if uiSettings then
            local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
            if not Facade.UIManager:TryGetLoadedResByPath(fullPath) then
                loginfo('[AsyncLoadCheck - recursiveCheckAnyUIUnload] UpdateDynamicSubUIIdListById DynamicSubUIList ----------- 发现未加载的动态SubUI:', fullPath)
                return true
            else
                if Facade.UIManager:CheckIfDynamicSubUIListByUINavID(UINavID) then
                    for _, subUIId in ipairs(Facade.UIManager._mapUIId2DynamicSubUIIdList[UINavID]) do
                        local bHasAnyUIUnload = recursiveCheckAnyUIUnload(subUIId)
                        if bHasAnyUIUnload then
                            return bHasAnyUIUnload
                        end
                    end
                end
                if uiSettings.SubUIs then
                    for _, subUIId in ipairs(uiSettings.SubUIs) do
                        local bHasAnyUIUnload = recursiveCheckAnyUIUnload(subUIId)
                        if bHasAnyUIUnload then
                            return bHasAnyUIUnload
                        end
                    end
                end
            end
        else
            assertlog(uiSettings,"recursiveCheckAnyUIUnload load ui asset failed, UISettings is nil, Please check UITable, UINavID:"..UINavID)
            logerror('反向依赖了尚未初始化的模块的UI，UISettings is nil 请检查', UIName2ID.GetNameByID(UINavID), debug.traceback())
            return true
        end
    end
    return false
end

--- 额外的动态加载子UI
function UIManager:TryAddDynamicSubUIList(UINavID, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)
    fullPathList = fullPathList or {}
    skipPathList = skipPathList or {}
    mapFullPath2IsSubUIRes = mapFullPath2IsSubUIRes or {}
    allSubUINavIDList = allSubUINavIDList or {}
    if self:CheckIfDynamicSubUIListByUINavID(UINavID) then
        for _, subUIId in ipairs(self._mapUIId2DynamicSubUIIdList[UINavID]) do
            if UITable[subUIId] then
                loginfo('[DynamicSubUIList Log] ---- UIManager:TryAddDynamicSubUIList recursiveGetUIList 递归加载中', UITable[subUIId].BPPath)
            end
            recursiveGetUIList(subUIId, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)
        end
    else
        if UITable[UINavID] then
            -- loginfo('[DynamicSubUIList Log] ---- UIManager:TryAddDynamicSubUIList 本次加载没有动态子UI列表', UITable[UINavID].BPPath)
        end
    end
end

function UIManager:UpdateDynamicSubUIIdListById(UINavID, dynamicSubUIList)
    local preDynamicList = self._mapUIId2DynamicSubUIIdList[UINavID]
    local mapId2bRemain = {}
    if dynamicSubUIList and next(dynamicSubUIList) then
        for idx, uiId in ipairs(dynamicSubUIList) do
            mapId2bRemain[uiId] = true
            if UITable[uiId] then
                logwarning('[DynamicSubUIList Log] ---- UIManager:UpdateDynamicSubUIIdListById 需要加载的动态子UI', UITable[uiId].BPPath)
            end
        end
    end
    if preDynamicList and next(preDynamicList) then
        self.stubDynamicSubUIRes:ClearResRefsByCondition(CreateCallBack(self.CheckDynamicSubUIsCondition, self, mapId2bRemain))
        -- for idx, uiId in ipairs(dynamicSubUIList) do
        --     if not mapId2bRemain[uiId] then
        --         local fullPath = UIName2ID.GetBPFullPathByID(uiId)
        --         self.stubRuntimeUIRes:ClearResRefByPath(fullPath)
        --     end
        -- end
    end
    self._mapUIId2DynamicSubUIIdList[UINavID] = dynamicSubUIList
end

function UIManager:CheckDynamicSubUIsCondition(mapId2bRemain, resPath, resInst)
    local uiPath = ResPathUtil.ConvertUIBPPathByFullPath(resPath)
    local className = UIBPPath2Cls[uiPath]
    local uiNavId = UIBPCls2ID[className]
    if UITable[uiNavId] then
        -- logwarning('[DynamicSubUIList Log] ---- UIManager:CheckDynamicSubUIsCondition 已经加载过，需要保留的', UITable[uiNavId].BPPath)
    end
    if not mapId2bRemain[uiNavId] then
        loginfo('[DynamicSubUIList Log] UpdateDynamicSubUIIdListById DynamicSubUIList ----------- 当前动态SubUI被增量减去:', resPath)
    end
    return not mapId2bRemain[uiNavId]
end

function UIManager:CheckIfDynamicSubUIListByUINavID(UINavID)
    return self._mapUIId2DynamicSubUIIdList and self._mapUIId2DynamicSubUIIdList[UINavID] and next(self._mapUIId2DynamicSubUIIdList[UINavID])
end

---------------------------------------------------------------------------------
--- UI BP Resource [常驻]资源加载相关 Public API
---------------------------------------------------------------------------------
--- 预加载BP，可在OnLoadModule阶段调用
---@param UINavID number UINavID
---@param fLoadFinCallback function optional
---@param caller table optional
---@param priority number optional
---@see UIManager#_DoPreloadUIAsset
function UIManager:PreloadPermanentUIAsset(UINavID, fLoadFinCallback, caller, priority)
    loginfo("[ LoadingAndRes Debug ] ------------- UIManager:PreloadPermanentUIAsset [常驻]")
    return self:_DoPreloadUIAsset(UINavID, fLoadFinCallback, caller, priority, self.stubPermanentUIRes)
end

---@param UINavIDList table UINavIDList
---@param fLoadFinCallback function optional
---@param caller table optional
---@param priority number optional
---@see UIManager#_DoPreLoadUIAssetList
function UIManager:PreloadPermanentUIAssetList(UINavIDList, fLoadFinCallback, caller, priority)
    loginfo("[ LoadingAndRes Debug ] ------------- UIManager:PreloadPermanentUIAssetList [常驻]")
    return self:_DoPreLoadUIAssetList(UINavIDList, fLoadFinCallback, caller, priority, self.stubPermanentUIRes)
end

---------------------------------------------------------------------------------
--- UI BP Resource [Loading]资源加载相关 Public API
---------------------------------------------------------------------------------
--- Loading阶段的
---@param UINavID number UINavID
---@param fLoadFinCallback function optional
---@param caller table optional
---@param priority number optional
---@see UIManager#_DoPreloadUIAsset
function UIManager:PreloadLoadingUIAsset(UINavID, fLoadFinCallback, caller, priority)
    loginfo("[ LoadingAndRes Debug ] ------------- UIManager:PreloadLoadingUIAsset [Loading]")
    return self:_DoPreloadUIAsset(UINavID, fLoadFinCallback, caller, priority, self.stubLoadingUIRes)
end

--- Loading阶段的
---@param UINavIDList table UINavIDList
---@param fLoadFinCallback function optional
---@param caller table optional
---@param priority number optional
---@see UIManager#_DoPreLoadUIAssetList
function UIManager:PreloadLoadingUIAssetList(UINavIDList, fLoadFinCallback, caller, priority)
    loginfo("[ LoadingAndRes Debug ] ------------- UIManager:PreloadLoadingUIAssetList [Loading]")
    return self:_DoPreLoadUIAssetList(UINavIDList, fLoadFinCallback, caller, priority, self.stubLoadingUIRes)
end

---@param UINavID number UINavID
---@param fLoadFinCallback function optional
---@param caller table optional
---@param priority number optional
---@param chosenPreloadStub ResStub optional
function UIManager:_DoPreloadUIAsset(UINavID, fLoadFinCallback, caller, priority, chosenPreloadStub)
    chosenPreloadStub = setdefault(chosenPreloadStub, self.stubPermanentUIRes)
    local uiSettings = UITable[UINavID]
    assertlog(uiSettings, 'PreloadPermanentUIAsset failed, uiSettings is nil, Please check UITable')
    local fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList = recursiveGetUIList(UINavID)
    self:TryAddDynamicSubUIList(UINavID, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)

    --- LONG3 资源计数加载期间修复
    local loadingGid = UIUtil.GetAsyncGid()
    self:OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid)

    local fOnPreLoadFin = CreateCallBack(function(self, mapPath2ResIns)
        if AnalysisUtil.ShouldTraceUI(UINavID) then
            AnalysisUtil.StopPreloadTime(UINavID)
        end

        for idx, skipFullPath in ipairs(skipPathList) do
            mapPath2ResIns[skipFullPath] = self:TryGetLoadedResByPath(skipFullPath)
        end

        --- Long1.0 配合TryAddDynamicSubUIList成对使用
        self:_TryHandleSubUIRes(UINavID, mapFullPath2IsSubUIRes, mapPath2ResIns)
        local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
        local uiIns = mapPath2ResIns[fullPath]
        safecall(fLoadFinCallback, caller, uiIns)

        self:OnResPathsFinishAsyncLoading(loadingGid)
    end, self)

    if AnalysisUtil.ShouldTraceUI(UINavID) then
        AnalysisUtil.StartPreloadTime(UINavID)
    end

    --- LONG3 资源计数加载期间修复
    local stub, batchId = Facade.ResourceManager:AsyncLoadResources(chosenPreloadStub, fullPathList, fOnPreLoadFin, nil, priority)
    return stub, batchId, loadingGid
end

---@param UINavIDList table UINavIDList
---@param fLoadFinCallback function optional
---@param caller table optional
---@param priority number optional
---@param chosenPreloadStub ResStub optional
function UIManager:_DoPreLoadUIAssetList(UINavIDList, fLoadFinCallback, caller, priority, chosenPreloadStub)
    chosenPreloadStub = setdefault(chosenPreloadStub, self.stubPermanentUIRes)
    if UINavIDList and next(UINavIDList) then
        local fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList = {}, {}, {}, {}
        for _, UINavID in ipairs(UINavIDList) do
            local uiSettings = UITable[UINavID]
            assertlog(uiSettings)
            if uiSettings == nil then
                logerror(debug.traceback())
            end
            recursiveGetUIList(UINavID, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)
        end

        --- LONG3 资源计数加载期间修复
        local loadingGid = UIUtil.GetAsyncGid()
        self:OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid)

        local fOnPreLoadFin = CreateCallBack(function(self, mapPath2ResIns)
            for idx, skipFullPath in ipairs(skipPathList) do
                mapPath2ResIns[skipFullPath] = self:TryGetLoadedResByPath(skipFullPath)
            end
            
            local mapNavId2UIIns = {}
            for _, UINavID in ipairs(UINavIDList) do
                local uiSettings = UITable[UINavID]
                local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
                local uiIns = mapPath2ResIns[fullPath]
                mapNavId2UIIns[UINavID] = uiIns
            end

            for fullPath, bSubUI in pairs(mapFullPath2IsSubUIRes) do
                if bSubUI then
                    local uiIns = mapPath2ResIns[fullPath]
                    self.stubRuntimeSubUIRes:AddResRef(fullPath, uiIns)
                end
            end

            safecall(fLoadFinCallback, caller, mapNavId2UIIns)
            Facade.LuaCreateUIManager:TryPreCreateUI(mapNavId2UIIns)

            self:OnResPathsFinishAsyncLoading(loadingGid)
        end, self)

        local stub, batchId = Facade.ResourceManager:AsyncLoadResources(chosenPreloadStub, fullPathList, fOnPreLoadFin, nil, priority)
        return stub, batchId, loadingGid
    else
        logerror('UIManager:_DoPreLoadUIAssetList UINavIDList is nil or empty !!!!')
        return
    end
end

---------------------------------------------------------------------------------
--- UI BP Resource [运行]资源加载相关 For UIHandle Only API
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
--- 异步加载单个UI接口(仅允许UIHandle调用)
---@param UINavID number UINavID
---@param fOnResLoadFinished function
---@param caller table
---@param priority number
function UIManager:AsyncLoadUIAsset(UINavID, fOnResLoadFinished, caller, priority)
    local uiSettings = UITable[UINavID]
    assertlog(uiSettings)

    local fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList = recursiveGetUIList(UINavID)
    self:TryAddDynamicSubUIList(UINavID, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)

    --- LONG3 资源计数加载期间修复
    local loadingGid = UIUtil.GetAsyncGid()
    self:OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid)

    local fOnLoadFinCallback = CreateCallBack(function(self, mapPath2ResIns)
        for idx, skipFullPath in ipairs(skipPathList) do
            mapPath2ResIns[skipFullPath] = self:TryGetLoadedResByPath(skipFullPath)
        end

        --- Long1.0 配合TryAddDynamicSubUIList成对使用
        self:_TryHandleSubUIRes(UINavID, mapFullPath2IsSubUIRes, mapPath2ResIns)
        safecall(fOnResLoadFinished, caller, mapPath2ResIns, loadingGid)

        self:OnResPathsFinishAsyncLoading(loadingGid)
    end, self)
    
    local stub, batchId = Facade.ResourceManager:AsyncLoadResources(self.stubRuntimeUIRes, fullPathList, fOnLoadFinCallback, nil, priority)
    return stub, batchId, loadingGid
end

function UIManager:_TryHandleSubUIRes(UINavID, mapFullPath2IsSubUIRes, mapPath2ResIns)
    if self:CheckIfDynamicSubUIListByUINavID(UINavID) then
        local mapResPath2Dynamic = {}
        for _, subUIId in ipairs(self._mapUIId2DynamicSubUIIdList[UINavID]) do
            if UITable[subUIId] then
                local fullPath = UIName2ID.GetBPFullPathByID(subUIId)
                local resIns = mapPath2ResIns[fullPath]
                self.stubDynamicSubUIRes:AddResRef(fullPath, resIns)
                mapResPath2Dynamic[fullPath] = true
            end
        end
        for fullPath, bSubUI in pairs(mapFullPath2IsSubUIRes) do
            if bSubUI and not mapResPath2Dynamic[fullPath] then
                local uiIns = mapPath2ResIns[fullPath]
                self.stubRuntimeSubUIRes:AddResRef(fullPath, uiIns)
            end
        end
    else
        for fullPath, bSubUI in pairs(mapFullPath2IsSubUIRes) do
            if bSubUI then
                local uiIns = mapPath2ResIns[fullPath]
                self.stubRuntimeSubUIRes:AddResRef(fullPath, uiIns)
            end
        end
    end
end

--- 异步加载多个UI接口(仅允许UIHandle调用)
---@param UINavIDList array UINavID[]
---@param fOnResLoadFinished function
---@param caller table
---@param priority number
function UIManager:AsyncLoadUIAssetList(UINavIDList, fOnResLoadFinished, caller, priority)
    local fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList = {}, {}, {}, {}
    for _, UINavID in ipairs(UINavIDList) do
        local uiSettings = UITable[UINavID]
        assertlog(uiSettings)
        recursiveGetUIList(UINavID, fullPathList, skipPathList, mapFullPath2IsSubUIRes, allSubUINavIDList)
    end

    --- LONG3 资源计数加载期间修复
    local loadingGid = UIUtil.GetAsyncGid()
    self:OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid)

    local fOnLoadFinCallback = CreateCallBack(function(self, mapPath2ResIns)
        for idx, skipFullPath in ipairs(skipPathList) do
            mapPath2ResIns[skipFullPath] = self:TryGetLoadedResByPath(skipFullPath)
        end

        for fullPath, bSubUI in pairs(mapFullPath2IsSubUIRes) do
            if bSubUI then
                local uiIns = mapPath2ResIns[fullPath]
                self.stubRuntimeSubUIRes:AddResRef(fullPath, uiIns)
            end
        end

        safecall(fOnResLoadFinished, caller, mapPath2ResIns, loadingGid)

        self:OnResPathsFinishAsyncLoading(loadingGid)
    end, self)

    local stub, batchId = Facade.ResourceManager:AsyncLoadResources(self.stubRuntimeUIRes, fullPathList, fOnLoadFinCallback, nil, priority)
    return stub, batchId, loadingGid
end

---------------------------------------------------------------------------------
--- UI BP Resource 资源查询相关 Public API
---------------------------------------------------------------------------------
function UIManager:CheckUIHasBeenLoaded_Deprecated(UINavID)
    local uiSettings = UITable[UINavID]
    assertlog(uiSettings)
    local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
    assert(fullPath,"check " .. tostring(uiSettings.LuaPath))
    local bNormalFound = self.stubPermanentUIRes:TryGetResByPath(fullPath) ~= nil 
    or self.stubLoadingUIRes:TryGetResByPath(fullPath) ~= nil 
    or self.stubRuntimeSubUIRes:TryGetResByPath(fullPath) ~= nil 
    or self.stubRuntimeUIRes:TryGetResByPath(fullPath) ~= nil

    local bDynamicFound = false
    if not bNormalFound then
        if self:CheckIfDynamicSubUIListByUINavID(UINavID) then
            local bHasAnyUIUnload = recursiveCheckAnyUIUnload(UINavID)
            if not bHasAnyUIUnload then
                bDynamicFound = self.stubDynamicSubUIRes:TryGetResByPath(fullPath) ~= nil
            else
                loginfo('[AsyncLoadCheck - recursiveCheckAnyUIUnload] UpdateDynamicSubUIIdListById DynamicSubUIList 存在未加载的动态SubUI 判定 [整体] 重新加载', fullPath)
            end
            if not bDynamicFound then
                -- loginfo('[AsyncLoadCheck] No cache found in normal UIManager stubs and dynamic stub , start loading', fullPath)
            else
                -- loginfo('[AsyncLoadCheck] Found in dynamic stub', fullPath)
            end
        else
            -- loginfo('[AsyncLoadCheck] No cache found in normal UIManager stubs, start loading', fullPath)
        end
    else
        -- loginfo('[AsyncLoadCheck] Found in normal UIManager stubs', fullPath)
    end

    return bNormalFound or bDynamicFound
end

function UIManager:CheckUIHasBeenLoaded(UINavID)
    local uiSettings = UITable[UINavID]
    assertlog(uiSettings, "uiSettings is invalid, UINavID:" .. tostring(UINavID))
    local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
    assert(fullPath, "fullPath is invalid, LuaPath:" .. tostring(uiSettings.LuaPath), "please check LuaBpAssetConfig(Content/DataTables|Content/R13N/PC/DataTables)")

    local bDynamicFound = false
    if self:CheckIfDynamicSubUIListByUINavID(UINavID) then
        local bHasAnyUIUnload = recursiveCheckAnyUIUnload(UINavID)
        if bHasAnyUIUnload then
            loginfo('[AsyncLoadCheck - recursiveCheckAnyUIUnload] UpdateDynamicSubUIIdListById DynamicSubUIList 中存在未加载的SubUI 判定为需要 [整体] 重新加载', fullPath)
            return not bHasAnyUIUnload
        end
        bDynamicFound = self.stubDynamicSubUIRes:TryGetResByPath(fullPath) ~= nil
    end

    local bNormalFound = false
    if not bDynamicFound then
        bNormalFound = self.stubPermanentUIRes:TryGetResByPath(fullPath) ~= nil 
        or self.stubLoadingUIRes:TryGetResByPath(fullPath) ~= nil 
        or self.stubRuntimeSubUIRes:TryGetResByPath(fullPath) ~= nil 
        or self.stubRuntimeUIRes:TryGetResByPath(fullPath) ~= nil
    end
    return bNormalFound or bDynamicFound
end

function UIManager:TryGetLoadedResByPath(fullPath)
    local loadStubInOrderList = {
        self.stubPermanentUIRes,
        self.stubLoadingUIRes,
        self.stubRuntimeSubUIRes,
        self.stubRuntimeUIRes,
        self.stubDynamicSubUIRes
    }
    local resIns = nil
    for order, stub in ipairs(loadStubInOrderList) do
        resIns = stub:TryGetResByPath(fullPath)
        if resIns then
            -- loginfo("[ LoadingAndRes Debug ] ---TryGetResByPath---在Stub:", stub.stubName, '---中找到资源---', fullPath)
            return resIns
        end
    end
    return resIns
end

---------------------------------------------------------------------------------
--- UI BP Resource 资源清理相关 Public API
---------------------------------------------------------------------------------
--- 按照模块策略清理预加载的资源, ModuleManager调用
function UIManager:ClearPreloadStubByCondition(fConditionCheck)
    self.stubPermanentUIRes:ClearResRefsByCondition(fConditionCheck)
    -- self.stubLoadingUIRes:ClearResRefsByCondition(fConditionCheck)
end

--- 清理全部[运行]过程中资源
function UIManager:ClearUIResStub(bClearSub)
    self:ClearAliveCountList()
    if bClearSub then
        self.stubRuntimeSubUIRes:ClearAllResRefs()
        loginfo('[DynamicSubUIList Log] UpdateDynamicSubUIIdListById bClearSub = true ----------- self.stubRuntimeSubUIRes:ClearAllResRefs()')
    end
    self.stubRuntimeUIRes:ClearAllResRefs()
    self.stubDynamicSubUIRes:ClearAllResRefs()
    loginfo('[DynamicSubUIList Log] UpdateDynamicSubUIIdListById ClearUIResStub ----------- self.stubDynamicSubUIRes:ClearAllResRefs()')
    self._mapUIId2DynamicSubUIIdList = {}
    self.stubUIImageRes:ClearAllResRefs()
end

--- 清理[常驻]和[Loading]资源
function UIManager:ClearExUIResStub()
    self.stubPermanentUIRes:ClearAllResRefs()
    self.stubLoadingUIRes:ClearAllResRefs()
end

function UIManager:ClearUIImageRes()
    self.stubUIImageRes:ClearAllResRefs()
end

---------------------------------------------------------------------------------
--- UI BP Resource 资源清理相关 Private API
---------------------------------------------------------------------------------
--- UIManager销毁时调用
function UIManager:_ReleaseAllResStub()
    self:ClearAliveCountList()
    Facade.ResourceManager:UnloadResources(self.stubPermanentUIRes)
    Facade.ResourceManager:UnloadResources(self.stubLoadingUIRes)
    Facade.ResourceManager:UnloadResources(self.stubRuntimeUIRes)
    Facade.ResourceManager:UnloadResources(self.stubRuntimeSubUIRes)
    Facade.ResourceManager:UnloadResources(self.stubDynamicSubUIRes)
    Facade.ResourceManager:UnloadResources(self.stubUIImageRes)
    self.stubPermanentUIRes = nil
    self.stubLoadingUIRes = nil
    self.stubRuntimeUIRes = nil
    self.stubRuntimeSubUIRes = nil
    self.stubDynamicSubUIRes = nil
    self.stubUIImageRes = nil
end

return UIManager