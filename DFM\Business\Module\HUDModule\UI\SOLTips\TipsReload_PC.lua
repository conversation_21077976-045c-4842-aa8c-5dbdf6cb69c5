----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



local HudConfig = require "DFM.Business.Module.HUDModule.HUDConfig"
local HUDConfig = require "DFM.Business.Module.HUDModule.HUDConfig"
local TipsReload_PC = hud("TipsReload_PC")
local EGameHUDSate = import "GameHUDSate"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local UWeaponSwitchViewModel = import "WeaponSwitchViewModel"
local ECurActiveWeaponType = import "ECurActiveWeaponType"
local ESlateVisibility = import "ESlateVisibility"
local RefUtil = require "DFM.YxFramework.Util.RefUtil"


function TipsReload_PC:Ctor()
    self.currentCarriedAmmoCount = -1
    self.currentAmmoCount = -1
    self.curWarnLevel = 0

    self._wtKeyIconBoxText = self:Wnd("WBP_CommonKeyIconBoxText", HDKeyIconBoxText)
    self._wtKeyIconBox = self._wtKeyIconBoxText._wtKeyIcon
    self._wtTipsText = self._wtKeyIconBoxText._wtRightText

    self:_InitInVisibleGameHudState()

    if UGPGameplayDelegates ~= nil then
        local fOnProcessReload = CreateCallBack(self.OnProcessReload,self)
        UGPGameplayDelegates.Get(GetGameInstance()).OnTryReloadDelegate:Add(fOnProcessReload)
    end

    self.localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if isvalid(self.localCharacter) then
        local fOnCharacterReload = CreateCallBack(self.OnCharacterReload,self)
        self.localCharacter.OnCharacterReloadAmmo:Add(fOnCharacterReload)
    end
end

function TipsReload_PC:_InitInVisibleGameHudState()
    local invisibleStates = {
        EGameHUDSate.GHS_Operating3DUI,
        EGameHUDSate.GHS_Settlement,
        EGameHUDSate.GHS_PrepareTime,
        EGameHUDSate.GHS_Dead,
        EGameHUDSate.GHS_Dying,
        EGameHUDSate.GHS_DriveVehicle,
        EGameHUDSate.GHS_CutScene,
        EGameHUDSate.GHS_UseTelescope,
        EGameHUDSate.GHS_PVEQuestPanelOnly,
        EGameHUDSate.GHS_Monitor,
        EGameHUDSate.GHS_VehicleMode_Skill,
        EGameHUDSate.GHS_Assassinate,
        EGameHUDSate.GHS_WeaponInspect,
        EGameHUDSate.GHS_OpenMap,
        EGameHUDSate.GHS_HudHitFeedBack,
        EGameHUDSate.GHS_UseItem,
        EGameHUDSate.GHS_Aiming,
        EGameHUDSate.GHS_EscPanel,
        EGameHUDSate.GHS_KillerMark,
        EGameHUDSate.GHS_DyingView,
        EGameHUDSate.GHS_BeingRescue,
    }
    for Key = 1, #invisibleStates do
        if invisibleStates[Key] then
            self:AddStateToInVisibleGameHudState(invisibleStates[Key])
        end
    end
end

function TipsReload_PC:OnOpen()
    self:GetWeaponSwitchViewModel()
    -- self.viewModel:OnInit() -- CreateViewModel中已经oninit过了

    self.callbacks = {
        {nil, self.viewModel.OnDelegateGunHashChanged, self.RefreshReloadTips},
        {nil, self.viewModel.OnDelegateAmmoCountChanged, self.RefreshReloadTips},
        {nil, self.viewModel.OnDelegateActiveWeaponChanged, self.OnActiveWeaponChanged}
    }

    self:_InitCallbacks()

    loginfo("TipsReload_PC:OnOpen")
end

function TipsReload_PC:OnShow()
    self:RefreshReloadTips()

    loginfo("TipsReload_PC:OnShow")
end

function TipsReload_PC:OnHide()
    loginfo("TipsReload_PC:OnHide")
end

function TipsReload_PC:OnClose()
    loginfo("TipsReload_PC:OnClose", self._curUIState, 
    'hasdestory:', hasdestroy(self), 
    '_bInLayer:', self._bInLayer, 
    'selfTable:',self,
    'self.callbacks:',self.callbacks)

    self:_RemoveCallbacks()

    if isvalid(self.viewModel) then
        self.viewModel:OnDestroy()
    end
end

function TipsReload_PC:Destroy()
    if isvalid(self.viewModel) then
        loginfo("TipsReload_PC:Destroy rm ref viewmodel")
        RefUtil.RemoveRef(self.viewModel)
        self.viewModel = nil
        self.callbacks = nil
    end
end

function TipsReload_PC:GetWeaponSwitchViewModel()
    if not isvalid(self.viewModel) then
        self.viewModel = UWeaponSwitchViewModel.CreateViewModel(self)
    end
    return self.viewModel
end

function TipsReload_PC:OnCharacterReload(bIsEnter)
    if bIsEnter then
        self:Collapsed()
    else
        self:RefreshReloadTips()
    end
end

function TipsReload_PC:OnProcessReload(CanReload)
    if not self:CheckCurWeaponValidForReloadTips() then
        self:Collapsed()
        return
    end

    if CanReload == true then
        self:Collapsed()
    else
        if self.currentCarriedAmmoCount == 0 then
            -- 无备用弹反馈 显示三秒消失
            self.showText = HUDConfig.Loc.NoAmmo
            self._wtTipsText:SetText(string.format(self.showText))
            self._wtKeyIconBox:Collapsed()
            self:SelfHitTestInvisible()

            Timer.DelayCall(3.0, function()
                self:RefreshReloadTips()
            end, self)
        end
    end
end

function TipsReload_PC:RefreshReloadTips()
    if not self:GetWeaponSwitchViewModel() then
        logerror("[george] RefreshReloadTips(), viewModel not valid")
        return
    end
    if not self:CheckCurWeaponValidForReloadTips() then
        if self:GetVisibility() ~= ESlateVisibility.Collapsed then
            self:Collapsed()
        end
        return
    end

    if self.viewModel.CurActiveWeapon == self.viewModel.FirstGun then
        self.currentAmmoCount = self.viewModel.AmmoCount
        self.currentCarriedAmmoCount = self.viewModel.CarriedAmmoCount
    elseif self.viewModel.CurActiveWeapon == self.viewModel.SecondGun then
        self.currentAmmoCount = self.viewModel.AmmoCountSub
        self.currentCarriedAmmoCount = self.viewModel.CarriedAmmoCountSub
    elseif self.viewModel.CurActiveWeaponType == ECurActiveWeaponType.AbilityItem then
        self.currentAmmoCount = self.viewModel.AmmoCountAbilityItem
        self.currentCarriedAmmoCount = self.viewModel.CarriedAmmoCountAbilityItem
    elseif isvalid(self.viewModel.CurActiveWeapon) then
        self.currentAmmoCount = self.viewModel.CurActiveWeapon:GetCurAmmoNum()
        self.currentCarriedAmmoCount = self.viewModel.CurActiveWeapon:GetCarrieAmmoNum()
    else
        -- 这啥？
        logerror("[george] RefreshReloadTips(), Curweapon not valid")
        
        if self:GetVisibility() ~= ESlateVisibility.Collapsed then
            self:Collapsed()
        end
        self.currentAmmoCount = -1
        self.currentCarriedAmmoCount = -1
        return
    end

    local lastWarnLevel = self.curWarnLevel

    if self.currentAmmoCount > self.viewModel.ClipAmmoSize * self.LowWarningThresholdRatio then
        self.curWarnLevel = 0
        if self:GetVisibility() ~= ESlateVisibility.Collapsed then
            self:Collapsed()
        end
    else
        if UE.HUDStateManager.Get(GetWorld()):IsVisible_Lua(self) then
            if self:GetVisibility() ~= ESlateVisibility.SelfHitTestInvisible then
                self:SelfHitTestInvisible()
            end
        end
        if self.currentAmmoCount > self.viewModel.ClipAmmoSize * self.SuperLowWarningThresholdRatio then
            self.curWarnLevel = 1
            self.showText = HUDConfig.Loc.LowAmmo
        elseif self.currentAmmoCount > 0 then
            self.curWarnLevel = 2
            self.showText = HUDConfig.Loc.LowAmmo
        else
            self.curWarnLevel = 3
            self.showText = HUDConfig.Loc.NeedAmmo
        end

        if self.currentCarriedAmmoCount == 0 then
            -- 缺弹提示
            if self._wtKeyIconBox:GetVisibility() ~= ESlateVisibility.Collapsed then
                self._wtKeyIconBox:Collapsed()
            end
        else
            -- 装弹提示
            if self._wtKeyIconBox:GetVisibility() ~= ESlateVisibility.SelfHitTestInvisible then
                self._wtKeyIconBox:SelfHitTestInvisible()
            end
            self.showText = HUDConfig.Loc.Reload
        end
        self._wtTipsText:SetText(self.showText)

        -- 动画
        self:BP_SetType(self.curWarnLevel)
    end

    if lastWarnLevel ~= self.curWarnLevel then
        loginfo("[george] RefreshReloadTips(), Warn level switchs from "..lastWarnLevel.." to "..self.curWarnLevel)
    end
end

function TipsReload_PC:CheckCurWeaponValidForReloadTips()
    if not self:GetWeaponSwitchViewModel() then
        logerror("[george] CheckCurWeaponValidForReloadTips(), viewModel not valid")
        return
    end
    if self.viewModel.ClipAmmoSize == 0 then
        loginfo("[george] CheckCurWeaponValidForReloadTips(), ClipAmmoSize == 0")
        return false
    end

    if not self.viewModel.bCurWeaponEnableAmmoTip then
        local curWeaponId = 0
        if isvalid(self.viewModel.CurActiveWeapon) then
            curWeaponId = self.viewModel.CurActiveWeapon:GetWeaponId()
        end
        loginfo("[george] CheckCurWeaponValidForReloadTips(), bCurWeaponEnableAmmoTip == false, WeaponId", curWeaponId)
        return false
    end

    if self.viewModel.CurActiveWeaponType == ECurActiveWeaponType.EmptyHand or
    self.viewModel.CurActiveWeaponType == ECurActiveWeaponType.MeleeWeapon or
    self.viewModel.CurActiveWeaponType == ECurActiveWeaponType.ThrowableWeapon then
        loginfo("[george] CheckCurWeaponValidForReloadTips(), Hand, melee or throwable weapon")
        return false
    end

    if isvalid(self.viewModel.CurActiveWeapon) then
        if self.viewModel.CurActiveWeapon:IsSceneWeapon() then -- 巡线载具
            loginfo("CheckCurWeaponValidForReloadTips(), SceneWeapon is invalid")
            return false
        end
    end

    return true
end


function TipsReload_PC:OnActiveWeaponChanged()
    local curActiveWeapon = self.viewModel.CurActiveWeapon
    if isvalid(curActiveWeapon) then
        local ammoDataComponent = curActiveWeapon:GetWeaponDataComponentAmmo()
        if ammoDataComponent then
            if self.onAmmoChangeHandle then
                ammoDataComponent.OnAmmoChanged:Remove(self.onAmmoChangeHandle)
                self.onAmmoChangeHandle = nil
            end
            self.onAmmoChangeHandle = ammoDataComponent.OnAmmoChanged:Add(CreateCPlusCallBack(self.OnAmmoCountChanged, self))
        end

        self.viewModel.CurActiveWeapon.OnStartFireFailed:Add(CreateCPlusCallBack(self.OnFireFailed, self))
    end
    self:RefreshReloadTips()
end

function TipsReload_PC:OnAmmoCountChanged()
    self:RefreshReloadTips()
end

function TipsReload_PC:OnFireFailed(ret)
    if ret == 4 then
        self.showText = HUDConfig.Loc.NoTarget
        self._wtTipsText:SetText(self.showText)
        self._wtTipsText:SetDefaultColorAndOpacity(FSlateColor(FLinearColor("#FF0000FF")))
        self._wtKeyIconBox:Collapsed()
        self:SelfHitTestInvisible()

        Timer.DelayCall(1, function()
            self:RefreshReloadTips()
        end, self)
    end
end

function TipsReload_PC:_InitCallbacks()
    for i = 1, #self.callbacks do
        local callback = self.callbacks[i]
        callback[1] = callback[2]:Add(CreateCallBack(callback[3], self))
    end
end

function TipsReload_PC:_RemoveCallbacks()
    if self.callbacks == nil then
        return
    end
    for i = 1, #self.callbacks do
        local callback = self.callbacks[i]
        callback[2]:Remove(callback[1])
    end
end

return TipsReload_PC
