----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class MandelDrawOnly : LuaUIBaseView
local MandelDrawOnly = ui("MandelDrawOnly")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local FAnchors = import "Anchors"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local MandelBrickBanner = require "DFM.Business.Module.MarketModule.UI.MandelBrick.MandelBrickBanner"
local ActivityConfig = Module.Activity.Config
local FRTIParamData = import "RTIParamData"
local UDFCommonMediaView = import "DFCommonMediaView"

-- todo 测试用
local BattlePassConfig = Module.BattlePass.Config

function MandelDrawOnly:Ctor()
    self.historyAllCount = 0
    self._prizeGroups = {}
    self.coreGroup = {}
    self.history = {}
    self.historyToShow = {}
    self.propToHistory = {}
    self.bHasRestore = false
    self.curSelectGroupIndex = 0
    self.mandelCount = 0
    self.keyCount = 0
    self.isPlayVideo = 0
    self.paddedGoodsAndDrawNum = 0
    self.lastClickDrawTime = 0
    self.bNeedUseKey = true
    self.iType = 0  -- UI类型，nil or 0 曼德尔砖，1 活跃砖
    self.bMultiBricks = false  -- 是否页面要显示多块砖
    self.tActivityMandelBricks = nil  -- 活跃砖列表
    self.iActivityBrickCount = 0  -- 活跃砖种类数量
    self.iTopBarIndex = 1  -- 当前是哪个1级页签
    self.iActivityMandelBrickIDCurrSeason = 0  -- 当期赛季砖
    self.bOpenOtherOverviewButton = false

    self._wtBoxNameText = self:Wnd("wtBoxNameText", UITextBlock)

    self._wtTextDrawOne_Mandel = self:Wnd("DFTextBlock_DrawOne_Mandel", UITextBlock)
    self._wtTextDrawOne_Key = self:Wnd("DFTextBlock_DrawOne_Key", UITextBlock)
    self._wtTextDrawTen_Mandel = self:Wnd("DFTextBlock_DrawTen_Mandel", UITextBlock)
    self._wtTextDrawTen_Key = self:Wnd("DFTextBlock_DrawTen_Key", UITextBlock)

    self._wtGuaranteedGroupHintText = self:Wnd("wtGuaranteedGroupHintText", UITextBlock)
    self._wtGuaranteedPropHintText = self:Wnd("wtGuaranteedPropHintText", UITextBlock)

    self._wtCannotRestoreHintText = self:Wnd("wtCannotRestoreHintText", UITextBlock)
    self._wtCannotRestoreHintImg = self:Wnd("wtCannotRestoreHintImg", UIWidgetBase)

    self._wtSingleActionBtn = self:Wnd("wtSingleActionBtn", UIButton)
    self._wtMultipleActionBtn = self:Wnd("wtMultipleActionBtn", UIButton)
    self._wtClosePrizesPanelBtn = self:Wnd("DFButton_149", UIButton)

    self._wtSingleActionBtn:Event("OnClicked", self._OnDrawOneClick, self)
    self._wtMultipleActionBtn:Event("OnClicked", self._OnDrawTenClick, self)
    self._wtClosePrizesPanelBtn:Event("OnClicked", self._OnClosePrizesPanelBtnClick, self)

    self._keyImage1 = self:Wnd("DFImage_1", UIWidgetBase)

    self._wtPlatformSizeBox_0 = self:Wnd("PlatformSizeBox_0", UIWidgetBase)

    self._wtPlatformPaddingBox_0 = self:Wnd("PlatformPaddingBox_0", UIWidgetBase)
    --新UI框架
    self.uiNavIDList = {
        UIName2ID.StorePrizesList,

    }

    Facade.UIManager:RegSwitchSubUI(self, self.uiNavIDList)

    self._wtSingleActionBtn:BP_SetMainTitle(StoreConfig.Loc.MandelDrawOneTip)
    self._wtMultipleActionBtn:BP_SetMainTitle(StoreConfig.Loc.MandelDrawTenTip)

    self._wtVideoBtn = self:Wnd("wtPlayVideoBtn", UIButton)
    if self._wtVideoBtn ~= nil then
        self._wtVideoBtn:Collapsed()
        self._wtVideoBtn:Event("OnClicked", self._OnButtonVideoClick, self)
    end

    self._wtVideoJump = self:Wnd("wtSkipBtn", CommonSkipOverBg)
    self._wtVideoJump:SetKeepSelfOnClick(true)
    self._wtVideoJump:BindClickEvent(self._OnButtonVideoJumpClick, self)

    ---@type DFMCommonMediaImage 视频播放控件
    self._mediaImage = self:Wnd("DFMCommonMediaImage_336", UIMediaImage)
    self._mediaImage:Collapsed()
    if self._mediaImage and self._mediaImage.OnMediaPlayEnd then
        self._mediaImage.OnMediaPlayEnd:Add(CreateCPlusCallBack(self.OnMediaPlayEnd, self)) -- 在视频播放完毕和视频被跳过的时候都会触发到
    end

    --背景视频专用
    ---@type CommonVideoComponent
    self._mediaBackground = self:Wnd("WBP_CommonVideoComponent_1", CommonVideoComponent)
    self._mediaBackground:InitComponent(false)
    if self._mediaBackground then
        self._mediaBackground:HitTestInvisible()
    end

    self.showRewardsDetail = false

    self.core_time_assured = 0
    self.acquisition_guaranteed = 0
    self.open_times = 0

    self.enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()

    self._keyImage6 = self:Wnd("DFImage_6", UIWidgetBase)
    self._keyImage4 = self:Wnd("DFImage_4", UIWidgetBase)
    self._DrawOneRoot = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._DrawTenRoot = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    -- self:HideGlobalUI()

    self._wtImageArt = self:Wnd("DFImage_244", UIImage)
    self._wtImageBG = self:Wnd("wtFirearmImg", UIImage)

    self._mandelBrickImgOne = self:Wnd("DFImage_3", UIWidgetBase)
    self._mandelBrickImgTen = self:Wnd("DFImage_5", UIWidgetBase)
    self._mandelBrickImgRight = self:Wnd("DFImage_252", UIWidgetBase)


    --奖池总览按钮
    self._wtWBP_DFCommonButtonV2S2 = self:Wnd("wtWBP_DFCommonButtonV2S2", DFCommonButtonOnly)
    self._wtWBP_DFCommonButtonV2S2:Event("OnClicked", self._OnOpenPrizeOverviewButtonClicked, self)

    --概率和抽奖记录总览按钮
    self._wtCommonCheckInstruction = self:Wnd("wtCommonCheckInstruction", DFCommonButtonOnly)
    self._wtCommonCheckInstruction:Event("OnCheckStateChanged", self._OnOpenOtherOverviewButtonClicked, self)
    self._wtCheckBox = self._wtCommonCheckInstruction:Wnd("DFCheckBox_Icon", UICheckBox)

    --曼德尔名字
    self._wtMandelName = self:Wnd("wtMandelName",UITextBlock)

    --已拥有钥匙数
    self._wtOwnedBoxCount = self:Wnd("wtOwnedBoxCount",UITextBlock)

    -- 艺术字容器
    self._wtArtTitlePanel = self:Wnd("DFScaleBox_0", UIWidgetBase)
    
    -- 活跃砖切换面板
    self._wtActivityBrickChangePanel = self:Wnd("PlatformSizeBox_0", UIWidgetBase)

    -- 切换面板
    self._wtMandelBrickBanner = self:Wnd("WBP_Market_Banner", MandelBrickBanner)
    self._wtMandelBrickBannerTitle = self._wtMandelBrickBanner:Wnd("DFCommonRichTextBlock_0", UITextBlock)
    self._wtMandelBrickBannerImageIcon = self._wtMandelBrickBanner:Wnd("DFImage_Icon", UIImage)

    -- 右下角砖数量面板
    self._wtBrickCountPanel = self:Wnd("DFCanvasPanel_4", UIWidgetBase)

    -- 右下角选择奖励面板
    self._wtRewardChoosePanel = self:Wnd("DFVerticalBox_0", UIWidgetBase)
    self._wtRewardChoosePanelRedDot = Module.ReddotTrie:CreateReddotIns(self._wtRewardChoosePanel, nil, nil, nil, FVector2D(-40, -60))   -- 奖励面板红点
    -- 活跃砖挂饰展馆图标
    self._wtPendantGalleryIcon = self:Wnd("DFImage_126", UIImage)
    -- 活跃砖挂饰展馆个数展示
    self._wtPendantGalleryCount = self:Wnd("DFRichTextBlock_55", UITextBlock)
    -- 曼德尔砖高概率奖励选择加号
    self._wtUpRewardChoosePlus = self:Wnd("DFImage_122", UIWidgetBase)
    -- 曼德尔砖高概率武器图标展示
    self._wtUpRewardWeaponIcon = self:Wnd("DFImage_77", UIImage)
    -- 选择的头奖名称
    self._wtUpRewardItemName = self:Wnd("DFTextBlock_79", UITextBlock)
    
    -- 初始化
    self._wtMandelBrickBannerTitle:SelfHitTestInvisible()
    -- 隐藏右下角砖数量面板
    self._wtBrickCountPanel:Collapsed()
    -- 显示右下角选择奖励面板
    self._wtRewardChoosePanel:SelfHitTestInvisible()
    self._wtPendantGalleryIcon:Collapsed()
    self._wtPendantGalleryCount:Collapsed()
    self._wtUpRewardChoosePlus:Collapsed()
    self._wtUpRewardWeaponIcon:Collapsed()

    -- 右下角曼德尔砖样式
    self:SetPrizeMode(0)

    -- 高概率奖励选择按钮
    self._wtUpRewardChooseBtn = self:Wnd("DFButton_129", UIButton)
    self._wtUpRewardChooseBtn:Event("OnClicked", self._OnUpRewardChooseBtnClick, self)

    self._wtUpRewardIcon = self:Wnd("WBP_SlotCompIconImage", UIWidgetBase):Wnd("wtMainIcon", UIImage)
    self._wtUpRewardIcon.OnIconLoaded:Add(CreateCPlusCallBack(self.OnUpRewardIconLoaded,self))

    -- 累抽奖励按钮
    self._wtContinuousRewardPanel = self:Wnd("DFCanvasPanel_12", UIWidgetBase)
    self._wtContinuousRewardPanelRedDot = Module.ReddotTrie:CreateReddotIns(self._wtContinuousRewardPanel, nil, nil, nil, FVector2D(-30, 0))   -- 累抽奖励按钮红点
    self._wtContinuousRewardBtn = self:Wnd("DFButton_448", UIButton)
    self._wtContinuousRewardBtn:Event("OnClicked", self._OnContinuousRewardBtnClick, self)
    self._wtImageContinuousReward = self:Wnd("DFImage_488", UIImage)
end

function MandelDrawOnly:HideGlobalUI()
    if (IsBuildRegionGlobal() or IsBuildRegionGA()) or self.bNeedUseKey == false then
        self._keyImage1:Collapsed()
        self._keyImage4:Collapsed()
        self._keyImage6:Collapsed()
        self._wtTextDrawOne_Key:Collapsed()
        self._wtTextDrawTen_Key:Collapsed()
    end
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
-- type UI处理的类型, nil or 0 曼德尔砖，1 活跃砖, ****填入mandelID时，此参数不生效*****
-- source 开启的来源 1 活动页面
function MandelDrawOnly:OnInitExtraData(mandelID, type, source)
    -- 赋值砖类型
    if type then
        self.iType = type
    end

    if mandelID ~= nil then

        Server.StoreServer:ClearBoxInfoCacheByMandelId(mandelID)
        
        if StoreLogic.IsActivityMandelBrick(mandelID) then
            self.iType = 1
        else
            self.iType = 0
        end
    end
    
    -- UI开启的来源
    self.iSource = source
    
    -- 此处临时处理，后续读配置
    self.iActivityMandelBrickIDCurrSeason = Server.StoreServer:GetCurrSeasonMandelID()

    if IsHD() then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)
    end

    if self.iType == 0 then
        -- 曼德尔砖
        if mandelID then
            self.openWithMandeID = mandelID
            loginfo("[MandelDrawOnly] OnInitExtraData, normal mandelID:" .. mandelID)
        else
            self.openWithMandeID = 16110000014
        end

        -- 其他界面进入
        self:SetPanelPosition(0)
        
        -- 隐藏活跃砖名字
        self._wtBoxNameText:Collapsed()

        -- 显示艺术字容器
        self._wtArtTitlePanel:SelfHitTestInvisible()

        -- 活跃砖切换隐藏
        self._wtActivityBrickChangePanel:Collapsed()
        
        -- 货币栏
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, { StoreLogic.GetMandelTopBarIDByItemID(self.openWithMandeID), ECurrencyClientId.QuantumKey , ECurrencyClientId.MandelCoins, ECurrencyClientId.UnbindDiamond })

        -- 顶栏标题
        Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.TopBarTitleMandel)
        
        -- 上报打开曼德尔砖界面
        local boxId = 0
        local itemInfo = ItemConfigTool.GetItemConfigById(self.openWithMandeID)
        if itemInfo then
            boxId = itemInfo.ConnectedPool or 0
        end
        LogAnalysisTool.ShopEnterMandelOpenUIFlow(boxId,  self.iSource)
    elseif self.iType == 1 then
        -- 曼德尔活跃砖
        if mandelID ~= nil then
            loginfo("[MandelDrawOnly] OnInitExtraData, activity mandelID:" .. mandelID)
            self.openWithMandeID = mandelID

            -- 活跃曼德尔砖无页签
            self:SetPanelPosition(4)
        else
            -- 获取活跃砖数据
            self:GetActivityMandelData()

            if self.iActivityBrickCount > 1 then
                -- 活跃砖种类数量大于1
                
                -- 活跃曼德尔砖有页签
                self:SetPanelPosition(3)

                if self.tActivityMandelBricks[1] then
                    self.openWithMandeID = self.tActivityMandelBricks[1].id
                else
                    Facade.UIManager:CloseUI(self)
                    loginfo("[MandelDrawOnly] mandel data error")
                end

                -- 活跃砖切换显示
                self._wtActivityBrickChangePanel:SelfHitTestInvisible()

                -- banner切换初始化
                self._wtMandelBrickBanner:Init(self.tActivityMandelBricks, CreateCallBack(self.MandelBrickChangedCallback, self), 1)

                -- 设置当前的页签
                self.iTopBarIndex = 1  -- 当前是哪个1级页签

                -- 注册顶栏
                local topTopBarGroupRegInfo = self:GetTopBarRegInfo()
                Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTopBarGroupRegInfo)
                self:RefreshChangeTopBar()
            else
                -- 活跃砖种类数量小于等于1

                -- 活跃曼德尔砖无页签
                self:SetPanelPosition(4)

                -- 活跃砖切换隐藏
                self._wtActivityBrickChangePanel:Collapsed()

                if self.tActivityMandelBricks[1] then
                    self.openWithMandeID = self.tActivityMandelBricks[1].id
                else
                    self.openWithMandeID = self.iActivityMandelBrickIDCurrSeason
                end
            end
        end

        -- 显示活跃砖名字
        self._wtBoxNameText:Collapsed()

        -- 隐藏艺术字容器
        self._wtArtTitlePanel:SelfHitTestInvisible()

        -- 隐藏量子密钥
        self._keyImage4:Collapsed()
        self._wtTextDrawOne_Key:Collapsed()
        self._keyImage6:Collapsed()
        self._wtTextDrawTen_Key:Collapsed()

        -- 顶栏
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })

        -- 顶栏标题
        Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.TopBarTitleMandelActivity)
    end

    -- 根据砖ID刷新UI
    self:RefreshChangeMandelID()

    -- 设置单抽数字
    self._wtTextDrawOne_Mandel:SetText(1)
    self._wtTextDrawOne_Key:SetText(1)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function MandelDrawOnly:OnOpen()
    self:AddLuaEvent(Module.Store.Config.evtMandelDrawKeysBought, self._RefreshCurrencyNum, self)
    self:AddLuaEvent(Server.RewardServer.Events.evtOnDrawShowFinished, self._OnOpenBlindBoxFinished, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelUpRewardChooseResult, self._OnEvtStoreMandelUpRewardChooseResult, self) -- 高概率奖励选择结果
    
    -- self:AddLuaEvent(Module.CommonBar.Config.evtOnChangeButtonClicked, self._OnChangeButtonClicked, self)
    
    --【【CN】【PC】【必现】商城曼德尔转开启后的奖励展示界面，按键盘空格无法关闭页面，鼠标点击可以页面关闭按钮可以关闭】https://tapd.woa.com/Project_D/bugtrace/bugs/view?bug_id=1020421949123169535
    self:AddLuaEvent(Module.Reward.Config.Events.evtShowReward, self._OnShowReward, self)

    self._wtGuaranteedGroupHintText:Hidden()

    self.allDrawDatas = Server.StoreServer:GetDrawByType(1)
    self.MandelItemId = self.openWithMandeID

    self.lotteryPoolID = 0

    self.drawBoxConfig = Server.StoreServer:GetLotteryBoxConfigByID(self.lotteryPoolID)

    -- 读取服务器发来的SpecialItemBuyConf表格，获取数据
    self.buyManderKeysConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(self:GetKeyID())

    self:RefreshCurDrawData()

    -- 预加载曼德尔砖抽奖场景
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.LotteryV2, true, nil, nil, false, 30)
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.Lottery, true, nil, nil, false, 30)

    self._wtRewardChoosePanel:Collapsed()
end

function MandelDrawOnly:GetKeyID()
    return 32320000001
end

function MandelDrawOnly:RefreshCurDrawData()
    --mandel
    self.allMandels = Server.CollectionServer:GetCollectionPropById(self.openWithMandeID)
    if self.allMandels ~= nil then
        self.mandelCount = self.allMandels.num
    else
        self.mandelCount =  0 --表示无网络未知
    end

    --key
    self.allMandelKeys = Server.CollectionServer:GetCollectionPropById(self:GetKeyID())
    if self.allMandelKeys ~= nil then
        self.keyCount = self.allMandelKeys.num
    else
        self.keyCount = 0
    end

    -- 刷新曼德尔砖名字
    self._mandelItem = ItemBase:NewIns(self.openWithMandeID)
    if self._mandelItem then
        self._wtMandelName:SetText(self._mandelItem.name)
    end

    if self.iType == 0 then
        -- 刷新已拥有砖数
        self._wtOwnedBoxCount:SetText(tostring(self.mandelCount))
    elseif self.iType == 1 then
        self._wtOwnedBoxCount:SetText(tostring(self.mandelCount))
    end

    self:_RefreshCurrencyNum(self:GetKeyID() ,0)
end

function MandelDrawOnly:RefreshChangeMandelID()
    if not self.openWithMandeID then
        return
    end
    
    -- 根据ID刷新
    self:SetUp(self.openWithMandeID)
    self:SetVfx(self.openWithMandeID)
    StoreLogic.SetLotteryUIArtLogo(self.openWithMandeID, self._wtImageArt)

    -- 标题曼德尔砖名字
    local itemInfo = ItemConfigTool.GetItemConfigById(self.openWithMandeID)
    if itemInfo then
        self.lotteryPoolID = itemInfo.ConnectedPool or 0
        self.boxName = itemInfo.name
        if self._wtBoxNameText ~= nil then
            self._wtBoxNameText:SetText(itemInfo.name)
            self._wtBoxNameText:PlayAnim_ComputeTextBlock()
        end
    end

    self.drawData = Server.StoreServer:GetDrawByMandelID(self.openWithMandeID)
    
    if self.iType == 1 then
        local MandelConfig = Server.StoreServer:GetFreeMandelBrickConfigByID(self.openWithMandeID)
        if MandelConfig then
            if self._wtMandelBrickBannerTitle then
                self._wtMandelBrickBannerTitle:SetText(MandelConfig.PoolTitle)
            end

            if self._wtBoxNameText ~= nil then
                self._wtBoxNameText:SetText(MandelConfig.PoolTitle)
            end
            
            -- 图片
            self._wtMandelBrickBannerImageIcon:AsyncSetImagePath(MandelConfig.BannerImage)
            self._wtImageBG:AsyncSetImagePath(MandelConfig.CoverImage)
        end
    end

    -- 替换曼德尔砖图标
    self._mandelBrickImgOne:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/RichImage/BakedSprite/RichImage_Store_Icon_0103.RichImage_Store_Icon_0103'")
    self._mandelBrickImgTen:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/RichImage/BakedSprite/RichImage_Store_Icon_0103.RichImage_Store_Icon_0103'")
    self._mandelBrickImgRight:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/RichImage/BakedSprite/RichImage_Store_Icon_0103.RichImage_Store_Icon_0103'")

    -- 奖池数据刷新
    local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
    Module.Store:LoadBlindboxPrizes(self.openWithMandeID, loadPrizeCallback)
    
    -- 藏品数据刷新
    self:RefreshCurDrawData() 
end

function MandelDrawOnly:RefreshChangeTopBar()
    if self.iTopBarIndex == 1 then
        self._wtBoxNameText:SelfHitTestInvisible()
        self._wtActivityBrickChangePanel:Collapsed()
        -- 刷新当季砖的UI
        self.openWithMandeID = self.iActivityMandelBrickIDCurrSeason
        self:RefreshChangeMandelID()
    elseif self.iTopBarIndex == 2 then
        self._wtBoxNameText:Collapsed()
        self._wtActivityBrickChangePanel:SelfHitTestInvisible()
        -- 刷新砖的内容
        if self.openWithMandeID == nil then
            if self.tActivityMandelBricks[1] then
                self.openWithMandeID = self.tActivityMandelBricks[1].id
            end
        end
        self:RefreshChangeMandelID()
    end 
end

function MandelDrawOnly:_loadprize()
    -- loginfo("[MandelDrawOnly] Loadprize")

    -- if self.bHasRestore then
    --     self._wtCannotRestoreHintText:SetText(StoreConfig.Loc.MandelTabItemOwnedTip)
    --     self._wtCannotRestoreHintText:Visible()
    --     self._wtCannotRestoreHintImg:Visible()
    -- else
    --     self._wtCannotRestoreHintText:SetText(StoreConfig.Loc.MandelTabItemCouldRepeatTip)
    --     self._wtCannotRestoreHintText:Visible()
    --     self._wtCannotRestoreHintImg:Collapsed()
    -- end

    -- if self._wtPlatformPaddingBox_0 ~= nil then
    --     self._wtPlatformPaddingBox_0:Hidden()
    -- end

    -- if self.emptyContent ~= nil then
    --     local emptyContent = getfromweak(self.emptyContent)
    --     if emptyContent ~= nil then
    --         emptyContent:Collapsed()
    --     end
    -- end
    -- if self.creatPrizeList ~= true then

    --     self:_CreatePrizeListByGroupId(1)
    --     self:_CreatePrizeListByGroupId(2)
    --     self.creatPrizeList = true
    -- end
end

function MandelDrawOnly:SetNewGiftDetail(prizeGroups, openCount)
    if #prizeGroups > 0 then
        self.acquisition_guaranteed = 0
        self.core_time_assured = 0

        self._prizeGroups = prizeGroups
        

        for key, group in pairs(prizeGroups) do
            if group.core_flag == true and group.prob ~= 0 then
                self.core_time_assured = group.time_assured
                self.coreGroup = group
            end

            for index, item in ipairs(group.items) do
                if item.acquisition_guaranteed > 0 then
                    self.acquisition_guaranteed = item.acquisition_guaranteed
                    self.guaranteedItem = item
                end

                if item.restore_flag ~= nil and item.restore_flag == false then
                    self.bHasRestore = true
                end
            end
        end

        --open_count
        self.open_times = openCount
        if self.core_time_assured > 0 then
            self._wtGuaranteedGroupHintText:SetText(StringUtil.Key2StrFormat(StoreConfig.Loc.GuaranteedGroupHint,
            { remainCount = tostring(self.core_time_assured)} ))

            self._wtGuaranteedGroupHintText:SelfHitTestInvisible()
        end

        if self.acquisition_guaranteed > 0 then
            self._wtGuaranteedPropHintText:SelfHitTestInvisible()
            self._wtGuaranteedPropHintText:SetText(StringUtil.Key2StrFormat(StoreConfig.Loc.GuaranteedPropHint,
            { remainCount = tostring(self.acquisition_guaranteed), name = self.guaranteedItem.name}))
        end

        if self.curSelectGroupIndex == 0 then
            self:_loadprize()
        else
            self:_loadSweephistroy()
        end
    end

    -- 刷新解析指定
    self:RefreshUpReward()

    -- 刷新累抽奖励红点
    self._wtContinuousRewardPanelRedDot:SetReddotVisible(StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId))
    if StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId) then
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C006"))
    else
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C001"))
    end

    -- 更新红点
    self._wtRewardChoosePanelRedDot:SetReddotVisible(Server.StoreServer:IsMandelUpRewardRedDotByMandelID(self.openWithMandeID))

    -- 刷新累抽奖励面板
    self:RefreshContinuousPanel()
end

function MandelDrawOnly:_CreatePrizeListByGroupId(idx)
    -- if self._prizeGroups ~= nil and #self._prizeGroups >= idx then
    --     local weakGroupUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.StorePrizesList,
    --         self._wtPrizesScrollBox)
    --     local groupWidget = getfromweak(weakGroupUIIns)
    --     if groupWidget ~= nil then
    --         groupWidget:SetInfo(self._prizeGroups[idx].items, self._prizeGroups[idx].title_main, nil, 1, idx == 2, 2)
    --     end
    -- end
end

function MandelDrawOnly:_OnMandelPropItemClick(item)
    loginfo("[MandelDrawOnly] _OnMandelPropItemClick item.name:" .. item.name)

    if item then
        if item.itemMainType == EItemType.WeaponSkin then
            Module.Collection:ShowWeaponSkinDetailPage(item)
            -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item)
        else
            Module.ItemDetail:OpenItemDetailPanel(item, detailBtn, nil, nil, nil, nil, nil, nil, nil, nil)
        end
    end
end

function MandelDrawOnly:_OnStorePaddingMandelItems(paddedGoodsToDrawCount)
    self.paddedGoodsAndDrawNum = paddedGoodsToDrawCount
end

function MandelDrawOnly:_OnBoxInfoUpdate(data)
    if data == nil then
        logerror("[MandelDrawOnly]:_OnBoxInfoUpdate : GetGiftInfoByItemId is nil, item id :")
        return
    end

    self:RefreshContinuousPanel()

    -- 刷新累抽奖励红点
    self._wtContinuousRewardPanelRedDot:SetReddotVisible(StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId))
    if StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId) then
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C006"))
    else
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C001"))
    end
end

function MandelDrawOnly:_OnButtonVideoClick()
    self.showRewardsDetail = false
    -- 播放视频
    if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" then
        local needProgressBar = false
        local needSkipBtn = true
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoFullScreenView, nil, nil, self.CurSelectMovies, needProgressBar, needSkipBtn)
    end
end

function MandelDrawOnly:OnMediaPlayEnd()
    if self._mediaImage == nil then
        return
    end

    if self.isPlayVideo == 0 then
        return
    end

    loginfo("[MandelDrawOnly] OnMediaPlayEnd")
    -- self:PlayAnimation(self.WBP_Store_TheChestOpensMain_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self._wtVideoBtn:Visible()
    self._mediaImage:Pause()
    self._mediaImage:Collapsed()
    self._wtVideoJump:Collapsed()

    self.isPlayVideo = 0
end

function MandelDrawOnly:_OnButtonVideoJumpClick()
    if self._mediaImage == nil then
        return
    end

    if self.isPlayVideo == 0 then
        return
    end

    loginfo("[MandelDrawOnly] _OnButtonVideoJumpClick")
    -- self:PlayAnimation(self.WBP_Store_TheChestOpensMain_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

    Timer.DelayCall(1, function()

    end, self)

    self._wtVideoBtn:Visible()
    self._mediaImage:Pause()
    self._mediaImage:Collapsed()
    self._wtVideoJump:Collapsed()

    self.isPlayVideo = 0
end

function MandelDrawOnly:OnMediaPlayBegin()

end

function MandelDrawOnly:OnCloseBtnClick()

end

function MandelDrawOnly:RefreshView()
    Facade.UIManager:CommitTransition(true) --屏蔽视频和背景同时绘制的画面杂乱
    --播放背景视频
    if self.openWithMandeID then
        Module.Store:SetMandelDrawMediaBG(self.openWithMandeID, self._mediaBackground)
    end 
    self._videoPlaytransitionTimer =  Timer.DelayCall(0.2, function()
        Facade.UIManager:CommitTransition(false)
        self._videoPlaytransitionTimer = nil
    end)
    local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
    Module.Store:LoadBlindboxPrizes(self.openWithMandeID, loadPrizeCallback)
end


-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function MandelDrawOnly:OnClose()
    logerror("MandelDrawOnly:OnClose")
    self:RemoveLuaEvent(Server.RewardServer.Events.evtOnDrawShowFinished)
    self:RemoveLuaEvent(Module.Reward.Config.Events.evtShowReward)
    self:RemoveAllLuaEvent()
    -- self:RemoveLuaEvent(Module.CommonBar.Config.evtOnChangeButtonClicked)

    Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self.drawData.LotteryId))
    loginfo("MandelDrawOnly Facade.SoundManager:StopUIAudioEvent musicName = "..Module.Store:GetLotteryMusic(self.drawData.LotteryId))

    self.emptyContent = nil
    self.creatPrizeList = false

    Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self.drawData.LotteryId))

    -- 只有活动页面打开的，才抛出活动相关的事件
    if self.iSource == 1 then
        ActivityConfig.evtActivityBack:Invoke(true)
    end
end

function MandelDrawOnly:OnShowBegin()
    self:RefreshView()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION

    self:_EnableOpenBoxBtn(true)
end

function MandelDrawOnly:OnHideBegin()
    self._mediaBackground:Stop()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION

    self:_ClearTimer()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function MandelDrawOnly:OnShow()
    logerror("MandelDrawOnly:OnShow")
    self.paddedGoodsAndDrawNum = 0

    self:RegisterEventListeners()

    if IsHD() and self._bIsShowingRewardPop ~= true then
        self._inputMonitor = Facade.UIManager:GetInputMonitor()
        self._handle = self._inputMonitor:AddDisplayActionBinding("MandelDrawJumpOverSpace", EInputEvent.IE_Pressed,
        self._OnButtonVideoJumpClick, self, EDisplayInputActionPriority.UI_Pop)
    end

    if self.drawData then
        self.CurSelectMovies = self.drawData.MoviesRowName
    end

    if self.CurSelectMovies == nil or self.CurSelectMovies == "" or self.enablePlayFullScreenCG == false  then
        self._wtVideoBtn:Collapsed()
    else
        self._wtVideoBtn:Visible()
    end

    self._mediaImage:Collapsed()
    self._wtVideoJump:Collapsed()

    self:RefreshCurDrawData()

    LogAnalysisTool.DoSendStoreViewPageReportLog(3, self.MandelItemId, 0, 0)
    
    self:_SetBottombar()

    if Module.Store.Field:GetCanMandelStopAudio() then
        if self.drawData then
            -- 播放进入音乐
            Facade.SoundManager:PlayUIAudioEvent(Module.Store:GetLotteryMusic(self.drawData.LotteryId))
            Module.Store:SetCurMandelMusicName(Module.Store:GetLotteryMusic(self.drawData.LotteryId))
            loginfo("MandelDrawOnly Facade.SoundManager:PlayUIAudioEvent musicName = "..Module.Store:GetLotteryMusic(self.drawData.LotteryId))
        end
    else
        if not self.bOpenOtherOverviewButton then
            Module.Store.Field:SetCanMandelStopAudio(true)
        end
    end
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function MandelDrawOnly:OnHide()
    logerror("MandelDrawOnly:OnHide")
    self:OnMediaPlayEnd()
    self:UnRegisterEventListeners()

    if IsHD() and self._inputMonitor ~= nil and self._handle ~= nil then
        self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._handle)
    end
    if self.showRewardsDetail then
        self.showRewardsDetail = false
    end

    if self.drawData and Module.Store.Field:GetCanMandelStopAudio() then
        Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self.drawData.LotteryId))
        loginfo("MandelDrawOnly Facade.SoundManager:StopUIAudioEvent musicName = "..Module.Store:GetLotteryMusic(self.drawData.LotteryId))
    end
end

function MandelDrawOnly:Update(dt)

end

function MandelDrawOnly:_RefreshCurrencyNum(itemId, buyNum)
    --刷新密钥的数量--32320000001
    local num = self.keyCount + buyNum
    Server.CurrencyServer:RefreshCurrencyNum(itemId, 1, num)
end

function MandelDrawOnly:RegisterEventListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreOpenBoxSuccess, self._OnMandelOpenBoxSucess, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyChange, self._OnServerLotteryItemBuySuc, self)

    self:AddLuaEvent(Module.Store.Config.evtStoreMandelPropItemClick, self._OnMandelPropItemClick, self)
    self:AddLuaEvent(Module.Store.Config.evtStorePaddingMandelItems, self._OnStorePaddingMandelItems, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBoxItem, self._OnBoxInfoUpdate, self)

    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self.RefreshCurDrawData, self)

    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连
end

function MandelDrawOnly:UnRegisterEventListeners()
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreOpenBoxSuccess)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreOpenBoxSuccess)

    self:RemoveLuaEvent(Module.Store.Config.evtStoreMandelPropItemClick)
    self:RemoveLuaEvent(Module.Store.Config.evtStorePaddingMandelItems)

    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)
end

function MandelDrawOnly:_OnUpdateCollectionData()
    self:RefreshCurDrawData()
end

function MandelDrawOnly:_OnOpenBlindBoxFinished()
    local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
    Module.Store:LoadBlindboxPrizesWithOtherInfo(self.openWithMandeID, loadPrizeCallback)
end

function MandelDrawOnly:_OnChangeButtonClicked()    
    self.showRewardsDetail = false
end

------event-----
function MandelDrawOnly:_OnServerLotteryItemBuySuc(dataChange)
    if self.paddedGoodsAndDrawNum > 0 then
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawMandelLotteryItemUpdate)
        if self.paddedGoodsAndDrawNum == 1 then
            self:_openMandelBox(1)
        else
            self:_openMandelBox(10)
        end

        self.paddedGoodsAndDrawNum = 0
        return
    end

    local itemList = {}
    if dataChange and dataChange.prop_changes then
        local prop_changes = dataChange.prop_changes
        for _, propChange in ipairs(prop_changes) do
            if propChange.prop then
                local item = ItemBase:New(propChange.prop.id, propChange.prop.num, propChange.prop.gid)
                item.bGiveaway = propChange.prop.bGiveaway
                local weaponFeature = item:GetFeature(EFeatureType.Weapon)
                if weaponFeature and weaponFeature:IsWeaponSkin() then
                    -- 蓝图需要手动设置一下枪械信息
                    local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                    local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
                    item:SetRawPropInfo(propinfo)
                else
                    item:SetRawPropInfo(propChange.prop)
                end
                table.insert(itemList, item)
            end
        end

        -- local currency_changes = dataChange.currency_changes
        -- for _, currencyChange in ipairs(currency_changes) do
        --     if currencyChange.delta ~= 0 then
        --         local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
        --         table.insert(itemList, item)
        --     end
        -- end
    end
    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList)
end

function MandelDrawOnly:_OnMandelOpenBoxSucess(dataChange)
    self:RefreshCurDrawData()
end

function MandelDrawOnly:_OnRelayConnected()
    Server.StoreServer:SendShopGetBuyRecordReq()
    local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
    Module.Store:LoadBlindboxPrizesWithOtherInfo(self.openWithMandeID, loadPrizeCallback)

    self:RefreshCurDrawData()
end

function MandelDrawOnly:_OnDrawOneClick()
    loginfo("[MandelDrawOnly] _OnDrawOneClick")
    self.showRewardsDetail = false

    local nowClcikTime = Facade.ClockManager:GetLocalTimestamp()
    local timeSpan = nowClcikTime - self.lastClickDrawTime
    if timeSpan < 1.5 then
        loginfo("[MandelDrawOnly] _OnDrawOneClick timeSpan < 1")
        return
    end
    self.lastClickDrawTime = nowClcikTime

    if self.allMandels ~= nil then
        self.mandelCount = self.allMandels.num
    end

    if self.allMandelKeys ~= nil then
        self.keyCount = self.allMandelKeys.num
    end

    local needMandel = 1 - self.mandelCount
    local needKey = 1 - self.keyCount

    -- if (IsBuildRegionGlobal() or IsBuildRegionGA()) or self.bNeedUseKey == false then
    --     needKey = 0
    -- end

    if self.iType == 0 or self.iType == nil then
        if needMandel > 0 or needKey > 0 then
            self:_CheckMandelAndKeyCountNotEnough(needMandel, needKey, 1)
        else
            --draw
            self:_openMandelBox(1)
        end
    elseif self.iType == 1 then
        if needMandel > 0 then
            self:_CheckMandelAndKeyCountNotEnough(needMandel, needKey, 1)
        else
            --draw
            self:_openMandelBox(1)
        end
    end
end

function MandelDrawOnly:_openMandelBox(openCount)
    local mandelItem = Server.CollectionServer:GetCollectionItemById(self.openWithMandeID)
    if mandelItem == nil then
        logerror("[MandelDraw] _openMandelBox but mandelItem == nil")
        return
    end

    if openCount > mandelItem.num then
        loginfo("[MandelDraw] _openMandelBox but openCount > mandelItem.num")
        return
    end

    -- Server.StoreServer:OpenMandelBox({mandelItem}, 1, false)
    Server.RewardServer:OpenBlindBox(mandelItem, openCount)
    self:_EnableOpenBoxBtn(false)
    self._btnEnableTimer = Timer.DelayCall(5, function ()
        self:_EnableOpenBoxBtn(true)
    end,self)
end

function MandelDrawOnly:_EnableOpenBoxBtn(bEnable)
    if bEnable then
        self._wtSingleActionBtn:SetIsEnabled(true)
        self._wtMultipleActionBtn:SetIsEnabled(true)
    else
        self._wtSingleActionBtn:SetIsEnabled(false)
        self._wtMultipleActionBtn:SetIsEnabled(false)
    end
end

function MandelDrawOnly:_CheckMandelAndKeyCountNotEnough(needMandelCount, needKeyCount, drawCount)
    if needMandelCount <= 0 and needKeyCount <= 0 then
        return
    end

    if not self.drawData then
        logerror("[MandelDrawOnly] _CheckMandelAndKeyCountNotEnough but self.drawData == nil")
        return
    end

    self.paddedGoodsAndDrawNum = 0

    if needMandelCount > 0 then
        local function OnGetMandelInfo(SaleListInfo)
            loginfo("[MandelDraw] OnGetMandelInfo")

            local saleInfo = SaleListInfo
            if saleInfo ~= nil and saleInfo.sale_lists ~= nil and #saleInfo.sale_lists > 0 then
                local mandelFee = 0
                local keyFee = 0

                local nowNeedCount = needMandelCount
                for k, v in pairs(saleInfo.sale_lists) do
                    if nowNeedCount <= 0 then
                        break
                    end
                    local couldBuyNum = math.min(nowNeedCount, v.selling_num)
                    mandelFee = mandelFee + couldBuyNum * v.price
                    nowNeedCount = nowNeedCount - couldBuyNum
                end

                if needKeyCount < 0 then
                    needKeyCount = 0
                end
                if self.buyManderKeysConfig ~= nil then
                    keyFee = needKeyCount * self.buyManderKeysConfig.price
                end

                Facade.UIManager:AsyncShowUI(UIName2ID.StoreMandelPaddedGoods, nil, nil, self.drawData.MandelItemId,
                    needMandelCount - nowNeedCount, mandelFee, self.drawData.LotteryKeyId, needKeyCount, keyFee,
                    self.drawData.LotteryId, drawCount)

            else
                Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelInMarketNow)
                LogAnalysisTool.DoSendStorePaddedGoodsEventReportLog(self.drawData.LotteryId, self.drawData.MandelItemId
                    , self.drawData.LotteryKeyId, "", "", 2, 3)
            end
        end

        if Server.MarketServer:CheckIsInSaleList(self.drawData.MandelItemId) then
            -- 拉取拍卖行最新数据再进行购买
            Server.MarketServer:FetchSaleList(self.drawData.MandelItemId, false, OnGetMandelInfo)
        else
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelInMarketNow)
        end
    else
        local keyFee = 0
        if self.buyManderKeysConfig ~= nil then
            keyFee = needKeyCount * self.buyManderKeysConfig.price
        end
        
        local fOnBuySuccessCallback = function ()
            self:_openMandelBox(drawCount)
        end

        Module.Store.OpenStoreMandelBuyKeysPanel(nil, self.buyManderKeysConfig, self.drawData.LotteryId, 0,
            needKeyCount, 1, fOnBuySuccessCallback)
    end
end

function MandelDrawOnly:_OnDrawTenClick()
    loginfo("[MandelDrawOnly] _OnDrawTenClick")
    self.showRewardsDetail = false

    local nowClcikTime = Facade.ClockManager:GetLocalTimestamp()
    local timeSpan = nowClcikTime - self.lastClickDrawTime
    if timeSpan < 1.5 then
        loginfo("[MandelDrawOnly] _OnDrawTenClick timeSpan < 1")
        return
    end
    self.lastClickDrawTime = nowClcikTime

    if self.allMandels ~= nil then
        self.mandelCount = self.allMandels.num
    end

    if self.allMandelKeys ~= nil then
        self.keyCount = self.allMandelKeys.num
    end

    local drawCount = 10
    local needMandel = drawCount - self.mandelCount
    local needKey = drawCount - self.keyCount

    -- if (IsBuildRegionGlobal() or IsBuildRegionGA()) or self.bNeedUseKey == false then
    --     needKey = 0
    -- end

    if self.iType == nil or self.iType == 0 then
        if needMandel > 0 then -- 缺曼德尔砖时，不管缺不缺量子密钥
            self:_CheckMandelAndKeyCountNotEnough(needMandel, needKey, 10)
        elseif needMandel <= 0 and needKey > 0 then -- 只缺量子密钥
            self:_OnBuyKeysClick(needKey , drawCount)
        else
            --draw

            self:_openMandelBox(drawCount)
        end
    elseif self.iType == 1 then
        if needMandel > 0 then
            self:_CheckMandelAndKeyCountNotEnough(needMandel, needKey, 10)
        else
            --draw
            self:_openMandelBox(drawCount)
        end
    end
end

function MandelDrawOnly:_OnShowRewardsStateChanged(bShow)
    if self.showRewardsDetail then
        self.showRewardsDetail = false
    else
        self.showRewardsDetail = true
    end

    -- self:_OnGroupBoxIndexChanged(0)
end

function MandelDrawOnly:_OnShowRewardsClick()
    if self.showRewardsDetail then
        self.showRewardsDetail = false
    else
        self.showRewardsDetail = true
    end

    -- self:_OnGroupBoxIndexChanged(0)
end

function MandelDrawOnly:_OnClosePrizesPanelBtnClick()
    if self.showRewardsDetail then
        self.showRewardsDetail = false

    end
end

function MandelDrawOnly:_OnUpRewardChooseBtnClick()
    logerror("MandelDrawOnly:_OnUpRewardChooseBtnClick")
    Module.Store.Field:SetCanMandelStopAudio(false)
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreParseSpecify, nil, nil, self.openWithMandeID, self.boxId, self.iSource)

    -- 上报点击
    local groupID = nil
    local tCoreGroup = Server.StoreServer:GetMandelLotteryCoreGroup(self.boxId)
    if tCoreGroup then
        groupID = tCoreGroup.group_id
    end
    if groupID then
        LogAnalysisTool.ShopEnterMandelBoxUpUIFlow(self.boxId, groupID, self.iSource)
    end
end

function MandelDrawOnly:_OnContinuousRewardBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreMandelContinuousReward, nil, nil, self.boxId)
end

function MandelDrawOnly:_OnBuyKeysClick(needKey,drawCount)
    LogAnalysisTool.SignButtonClicked(10153005)
    self.showRewardsDetail = false
    local needKeyCount = needKey or 1

    local mandelCount = 0
    if self.allMandels ~= nil then
        mandelCount = self.allMandels.num
    end

    local keyCount = 0
    if self.allMandelKeys ~= nil then
        keyCount = self.allMandelKeys.num
    end

    -- 读取服务器发来的SpecialItemBuyConf表格，获取数据
    self.buyManderKeysConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(self:GetKeyID())

    local fOnBuySuccessCallback = function()
        self:_openMandelBox(drawCount)
    end

    Module.Store.OpenStoreMandelBuyKeysPanel(nil, self.buyManderKeysConfig, 0, mandelCount, needKeyCount, needKeyCount, fOnBuySuccessCallback)
end

function MandelDrawOnly:_showBuyKeyCount(defaultCount,drawCount)
    local mandelCount = 0
    if self.allMandels ~= nil then
        mandelCount = self.allMandels.num
    end

    local keyCount = 0
    if self.allMandelKeys ~= nil then
        keyCount = self.allMandelKeys.num
    end

    -- 读取服务器发来的SpecialItemBuyConf表格，获取数据
    self.buyManderKeysConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(self:GetKeyID())

    local fOnBuySuccessCallback = function()
        self:_openMandelBox(drawCount)
    end
    Module.Store.OpenStoreMandelBuyKeysPanel(nil, self.buyManderKeysConfig, 0, mandelCount, keyCount, defaultCount, fOnBuySuccessCallback)
end


function MandelDrawOnly:_OnCorePrizeDetailBtnClicked(item, detailBtn)
    loginfo("[MandelDraw] _OnCorePrizeDetailBtnClicked")
    if item then
        if item.itemMainType == EItemType.WeaponSkin then
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item)
        else
            Module.ItemDetail:OpenItemDetailPanel(item, detailBtn, nil, nil, nil, nil, nil, nil, nil, nil)
        end
    end

    LogAnalysisTool.SignButtonClicked(10153004)
end

function MandelDrawOnly:_loadSweephistroy()
    -- self._wtCannotRestoreHintText:Collapsed()
    -- self._wtCannotRestoreHintImg:Collapsed()

    -- self._wtPrizesScrollBox:Collapsed()

    -- local newHistory = Server.StoreServer:GetLotteryRecordsByLotteryId(self.lotteryPoolID)
    -- if newHistory ~= nil and self.history ~= nil then
    --     if #self.history ~= #newHistory then
    --         self.history = newHistory
    --         self.historyToShow = {}
    --         self.propToHistory = {}

    --         table.sort(self.history, function(a, b)
    --             return a.open_time > b.open_time
    --         end)

    --         self.historyAllCount = 0
    --         local counter = 0
    --         for historyid, historyInfo in pairs(self.history) do
    --             -- body
    --             if historyInfo.add_props ~= nil and #historyInfo.add_props > 0 then
    --                 self.historyAllCount = self.historyAllCount + #historyInfo.add_props
    --                 for k,v in pairs(historyInfo.add_props) do
    --                     table.insert(self.historyToShow, v)
    --                     counter = counter + 1
    --                     self.propToHistory[counter] = historyInfo
    --                 end
    --             end
    --         end
    --     end

    --     if #self.history > 0 then
    --         if self._wtPlatformPaddingBox_0 ~= nil then
    --             self._wtPlatformPaddingBox_0:Hidden()
    --         end
    --     else
    --         if self._wtPlatformPaddingBox_0 ~= nil then
    --             self._wtPlatformPaddingBox_0:Visible()
    --         end

    --         if not self.emptyContent then
    --             ---创建emptyContent
    --             self.emptyContent = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtPlatformPaddingBox_0)
    --             local emptyContent = getfromweak(self.emptyContent)
    --             local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(emptyContent)
    --             local anchors = FAnchors()
    --             anchors.Minimum = FVector2D(0, 0)
    --             anchors.Maximum = FVector2D(1, 1)
    --             if canvasSlot then
    --                 canvasSlot:SetAnchors(anchors)
    --                 canvasSlot:SetOffsets(FMargin(0,0,0,0))
    --             end
    --             emptyContent:BP_SetText(StoreConfig.Loc.MandelHistoryNoAnyThing)
    --             emptyContent:BP_SetTypeWithParam(1)
    --             emptyContent:SetRenderTranslation(FVector2D(0,-75))
    --             emptyContent:Visible()
    --             self.isContentOwner = true
    --         else
    --             local emptyContent = getfromweak(self.emptyContent)
    --             if emptyContent ~= nil then
    --                 emptyContent:Visible()
    --             end
    --         end
    --     end
    -- end
end

function MandelDrawOnly:_OnShowReward(bIsShowing)
    if not bIsShowing then
        self._bIsShowingRewardPop = false
        if IsHD() and not isvalid(self._handle) then
            self._inputMonitor = Facade.UIManager:GetInputMonitor()
            self._handle = self._inputMonitor:AddDisplayActionBinding("MandelDrawJumpOverSpace", EInputEvent.IE_Pressed,
                self._OnButtonVideoJumpClick, self, EDisplayInputActionPriority.UI_Pop)
        end
    else
        self._bIsShowingRewardPop = true
        if IsHD() and self._inputMonitor ~= nil and self._handle ~= nil then
            self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._handle)
            self._handle = nil
        end
    end
end

function MandelDrawOnly:_OnOpenPrizeOverviewButtonClicked()
    logerror("MandelDrawOnly:_OnOpenPrizeOverviewButtonClicked")
    Module.Store.Field:SetCanMandelStopAudio(false)
    Module.Store.OpenStorePrizeOverviewPanel(nil, nil, self.openWithMandeID)
    LogAnalysisTool.SignButtonClicked(10153001)
end

function MandelDrawOnly:_OnOpenOtherOverviewButtonClicked()
    logerror("MandelDrawOnly:_OnOpenOtherOverviewButtonClicked")
    Module.Store.Field:SetCanMandelStopAudio(false)
    self.bOpenOtherOverviewButton = true
    local fOnCloseCallback = function ()
        self._wtCheckBox:SetIsChecked(false, false)
        Module.Store.Field:SetCanMandelStopAudio(true)
        self.bOpenOtherOverviewButton = false
    end
    Module.Store.OpenStorePrizePopPanel(nil, fOnCloseCallback, self.openWithMandeID)
end

function MandelDrawOnly:MandelBrickChangedCallback(itemId)
    self.openWithMandeID = itemId
    self:RefreshChangeMandelID()
end

function MandelDrawOnly:GetActivityMandelData()
    self.tActivityMandelBricks = CollectionLogic.GetActivityMandelBricks()
    
    if self.tActivityMandelBricks == nil then
        return
    end
    
    self.iActivityBrickCount = #self.tActivityMandelBricks
end

-- 获取顶栏注册信息
function MandelDrawOnly:GetTopBarRegInfo()
    -- 生成信息
    local info = {
        tabTxtList = {
            StoreConfig.Loc.MandelActivityTitleCurrSeason,
            StoreConfig.Loc.MandelActivityTitleBeforeSeason,
        },
        imgPathList =  StoreConfig.MandelActivityTabImgPathList, 
        fCallbackIns = SafeCallBack(self.OnTopTabClick, self),
        defalutIdx = 1,
        tabGroupSize = FVector2D(1904, 96),
        tabSpaceMargin = FMargin(0, 0, 16, 0),
        bTriggerCallback = true,
        bNewReddotTrie = true,
        --reddotTrieRegItemList = reddotTrieList,
    }
    return info
end

function MandelDrawOnly:OnTopTabClick(index)
    self.iTopBarIndex = index
    self:RefreshChangeTopBar()
end

function MandelDrawOnly:OnUpRewardIconLoaded(DFMImage, Tile)
    local weaponMaterial = self._wtUpRewardWeaponIcon:GetDynamicMaterial()
    if weaponMaterial then
        weaponMaterial:SetTextureParameterValue("Texture_Main", Tile.Texture)

        loginfo("MandelDraw:OnUpRewardIconLoaded====RawSize:", Tile.RawSize.X, Tile.RawSize.Y)
        local size = self._wtUpRewardWeaponIcon.slot:GetSize()
        loginfo("MandelDraw:OnUpRewardIconLoaded====self._wtUpRewardWeaponIcon RawSize:", size.X, size.Y)

        local fRatio = Tile.RawSize.Y / Tile.RawSize.X

        self._wtUpRewardWeaponIcon.slot:SetSize(FVector2D(size.X, size.X * fRatio))
    end
    self._wtUpRewardWeaponIcon:SelfHitTestInvisible()
    self._wtUpRewardChoosePlus:Collapsed()
    self._wtUpRewardIcon:Collapsed()
end

function MandelDrawOnly:RefreshUpReward()
    self.boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(self.openWithMandeID)
    if itemInfo then
        self.boxId = itemInfo.ConnectedPool or 0
    end

    local coreGroup = Server.StoreServer:GetMandelLotteryCoreGroup(self.boxId)
    -- 判断是否只有一个核心奖励
    self.iCoreCount = 0
    if coreGroup == nil then
        self.iCoreCount = 0
    else
        for index, item in pairs(coreGroup.prop_list) do
            self.iCoreCount = self.iCoreCount + 1
        end
    end
    if self.iCoreCount <= 1 then
        self._wtRewardChoosePanel:Collapsed()
        return
    else
        self._wtRewardChoosePanel:SelfHitTestInvisible()
    end

    local UpRewardID = Server.StoreServer:GetMandelIDUpReward(self.boxId)
    if UpRewardID == nil then
        -- 未选择高概率奖励
        self._wtUpRewardChoosePlus:SelfHitTestInvisible()
        self._wtUpRewardWeaponIcon:Collapsed()
        self._wtUpRewardItemName:SelfHitTestInvisible()
        self._wtUpRewardItemName:SetText(StoreConfig.Loc.MandelUpRewardSelectTitle)
    else
        -- 已选择高概率奖励
        self._wtUpRewardChoosePlus:Collapsed()
        self._wtUpRewardWeaponIcon:Collapsed()
        self._wtUpRewardItemName:SelfHitTestInvisible()

        -- 设置奖励图标
        local wtItem = ItemBase:New(UpRewardID, 1)
        local param = FRTIParamData()
        param.bShouldMerge = false
        self._wtUpRewardIcon:SelfHitTestInvisible()
        RuntimeIconTool.SetItemIcon(wtItem, self._wtUpRewardIcon, param)
        self._wtUpRewardItemName:SetText(wtItem.name)
    end
    self:_SetBottombar()
end

function MandelDrawOnly:RefreshContinuousPanel()
    local ContinuousRewardCount = StoreLogic.GetMandelContinuousRewardCount(self.boxId)
    if ContinuousRewardCount == nil or ContinuousRewardCount <= 0 then
        self._wtContinuousRewardPanel:Collapsed()
    else
        self._wtContinuousRewardPanel:SelfHitTestInvisible()
    end
    self:_SetBottombar()
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelDrawOnly:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 切换页签的时候也会触发onshowbegin，要确保visible的时候才初始化手柄功能
    if not self:IsVisible() then
        return
    end

    --配置keyIcon
    if self._wtSingleActionBtn and not self._LotteryLongPressHandle then
        self._LotteryLongPressHandle = self:AddInputActionBinding(
            "MallLottery",
            EInputEvent.IE_Pressed,
            self._OnDrawOneClick,
            self,
            EDisplayInputActionPriority.UI_Stack
        )

        self._wtSingleActionBtn:SetDisplayInputActionWithLongPress(self._LotteryLongPressHandle, self, "MallLottery", true, nil, true)
    end
    if self._wtMultipleActionBtn and not self._LotteryTenLongPressHandle then
        self._LotteryTenLongPressHandle = self:AddInputActionBinding(
            "MallLotteryTen",
            EInputEvent.IE_Pressed,
            self._OnDrawTenClick,
            self,
            EDisplayInputActionPriority.UI_Stack
        )
        self._wtMultipleActionBtn:SetDisplayInputActionWithLongPress(self._LotteryTenLongPressHandle, self, "MallLotteryTen", true, nil, true)
    end

    if self.wtWBP_DFCommonButtonV2S2 then
        self.wtWBP_DFCommonButtonV2S2:SetDisplayInputAction("MallCadreDetails", true, nil, true)

        if not self._CadreDetails then
            self._CadreDetails = self:AddInputActionBinding(
            "MallCadreDetails", 
            EInputEvent.IE_Pressed, 
            self._OnOpenPrizeOverviewButtonClicked,
            self, 
            EDisplayInputActionPriority.UI_Stack
            )  
        end
    end

    self:_SetBottombar()

    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function MandelDrawOnly:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 移除输入
    if self._LotteryLongPressHandle then
        self:RemoveInputActionBinding(self._LotteryLongPressHandle)
        self._LotteryLongPressHandle = nil
    end

    if self._LotteryTenLongPressHandle then
        self:RemoveInputActionBinding(self._LotteryTenLongPressHandle)
        self._LotteryTenLongPressHandle = nil
    end

    if self._CadreDetails then
        self:RemoveInputActionBinding(self._CadreDetails)
        self._CadreDetails = nil
    end

    --移除按键提示
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function MandelDrawOnly:_SetBottombar()
    if not IsHD() then
        return 
    end

    --显示按键提示
    local summaryList ={}
    if self._wtRewardChoosePanel and self._wtRewardChoosePanel:IsVisible() then
        table.insert(summaryList, {actionName = "ChoseReward", func = self._OnUpRewardChooseBtnClick, caller = self, bUIOnly = false, bHideIcon = false})
    end

    if self._wtContinuousRewardPanel and self._wtContinuousRewardPanel:IsVisible() then
        table.insert(summaryList, {actionName = "ContinueReward", func = self._OnContinuousRewardBtnClick, caller = self, bUIOnly = false, bHideIcon = false})
    end

    if self._wtVideoBtn and self._wtVideoBtn:IsVisible() then
        table.insert(summaryList, {actionName = "MallDemo", func = self._OnButtonVideoClick, caller = self, bUIOnly = false, bHideIcon = false})
    end

    table.insert(summaryList, {actionName = "ProbabilityForStore",func = self._OnOpenOtherOverviewButtonClicked, caller = self ,bUIOnly = false, bHideIcon = false})

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
end
--- END MODIFICATION

function MandelDrawOnly:_OnEvtStoreMandelUpRewardChooseResult(result)
    if result == 0 then
        local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
        Module.Store:LoadBlindboxPrizesWithOtherInfo(self.openWithMandeID, loadPrizeCallback)
    end
end

function MandelDrawOnly:_ClearTimer()
    if self._videoPlaytransitionTimer then
           Timer.CancelDelay(self._videoPlaytransitionTimer)
           self._videoPlaytransitionTimer = nil
           Facade.UIManager:CommitTransition(false)
       end
   
       if self._btnEnableTimer then
           Timer.CancelDelay(self._btnEnableTimer)
           self._btnEnableTimer = nil
       end
   end


return MandelDrawOnly
