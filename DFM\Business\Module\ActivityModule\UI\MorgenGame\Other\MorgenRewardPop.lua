----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------


--- @class MorgenRewardPop : LuaUIBaseView
local MorgenRewardPop = ui("MorgenRewardPop")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local MorgenRewardItem = require "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenRewardItem"
local MorgenTaskItem = require "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenTaskItem"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
local EGPUINavScrollingCondition = import("EGPUINavScrollingCondition")

function MorgenRewardPop:Ctor()
    self._wtCommonPopWin = self:Wnd("wtRootWindow", CommonPopWindows)
    self._wtCommonPopWin:BindCloseCallBack(CreateCallBack(self.OnCloseBtnClicked, self))
    self._wtCommonPopWin:SetBackgroudClickable(true)

    self._wtRewardText = self:Wnd("DFTextBlock_72", UITextBlock)

    self.TaskPanel = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_91", self._OnGetTaskCount, self._OnProcessTaskWidget)
    
    self.RewardPanel = self:Wnd("DFCanvasPanel_64", UIWidgetBase)
    self.RewardItem1 = self:Wnd("WBP_MorgenGame_Item5", MorgenRewardItem)
    self.RewardItem2 = self:Wnd("WBP_MorgenGame_Item5_1", MorgenRewardItem)
    self.RewardItem1:Event("OnClicked", self.OnItemClicked, self, 1)
    self.RewardItem2:Event("OnClicked", self.OnItemClicked, self, 2)
    
    self.taskItems = {}
    self.rewardItems = {[1] = self.RewardItem1, [2] = self.RewardItem2}
    
    self._wtTitleTabGroup = UIUtil.WndTabGroupBox(self, "wtDFTabV3GroupBoxClass3StaticText", self._OnGroupBoxCount,
    self._OnGroupBoxWidget, self._OnGroupBoxIndexChanged)
    self._wtTabListReddot = {}
    
    if IsHD() and self._wtTitleTabGroup then
        self._wtTabKeyIconLeft = self._wtTitleTabGroup:Wnd("WBP_GamepadKeyIconBox_Left", HDKeyIconBox)
        self._wtTabKeyIconRight = self._wtTitleTabGroup:Wnd("WBP_GamepadKeyIconBox_Right", HDKeyIconBox)
    end
end

-----------------------------------------------------生命周期-----------------------------------------------------
--#region
function MorgenRewardPop:OnInitExtraData(activityID, rewards, curCycle, moneyNum, bIsMeetNPC)
    self._activityID = activityID
    self.rewards = rewards or {}
    self.curCycle = curCycle or 0
    self.moneyNum = moneyNum or 0
    self.bIsMeetNPC = bIsMeetNPC or false

    self.curIndex = 1
end

function MorgenRewardPop:OnOpen()
    self:SetAllText()
end

function MorgenRewardPop:OnClose()
    self.rewardItems = {}
    self.taskItems = {}
end

function MorgenRewardPop:OnShowBegin()
    self:InitGamepad()
    self:AddLuaEvent(MorgenTaskItem.evtTaskRewardClicked, self.OnTaskRewardClicked, self)

    local gamelnst = GetGameInstance()
    self._buttonownHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)

    self:RefreshUI()
end

function MorgenRewardPop:OnHideBegin()
    self:DisableGamepad()
    --鼠标事件移除
	if self._buttonownHandle then
		local gamelnst = GetGameInstance()
		UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._buttonownHandle)
		self._buttonownHandle = nil
	end

    --红点反注册
    for _, redTrie in ipairs(self._wtTabListReddot or {}) do
        redTrie:SetReddotVisible(false)
    end
    self._wtTabListReddot = {}
    self:RemoveAllLuaEvent()
end
--#endregion

-----------------------------------------------------响应操作-----------------------------------------------------
--#region

function MorgenRewardPop:RefreshInfo(rewards)
    self.rewards = rewards or {}
    self:RefreshUI()
end

function MorgenRewardPop:RefreshUI()
    self._wtTitleTabGroup:RefreshTab()

    if self.curIndex == 1 then
        self.RewardPanel:SelfHitTestInvisible()
        self.TaskPanel:Collapsed()
        self.RewardItem1:RefreshInfo(self.rewards[1], self.curCycle)
        self.RewardItem2:RefreshInfo(self.rewards[2], self.curCycle)
        self:InitRewardGamepad()
    else
        self.TaskPanel:SelfHitTestInvisible()
        self.RewardPanel:Collapsed()
        self.TaskPanel:RefreshAllItems()
        self:InitTaskGamepad()
    end
end

function MorgenRewardPop:OnItemClicked(index)
    --- 通过轮次判断是否可以领奖
    if self.curCycle > index then
        --领奖
        local reward_id = self.rewards[index].reward_id
        local fCallBack = SafeCallBack(self.RefreshInfo, self)
        Server.ActivityServer:SendMorgenGetAwardReq(self._activityID, reward_id, fCallBack)
    else
        --提示
        local item = self.rewards[index].prop
        local itemData = ItemBase:New(tonumber(item.id or 0), item.num)
        itemData:SetRawPropInfo(self.rewards[index].prop)
        
        if itemData.itemMainType == EItemType.WeaponSkin then
            local fVisibilityControl = function(_self, bVisible)
                if bVisible then
                    self:Show(true, true)
                    Timer.DelayCall(0.3, function()
                        self:RefreshUI()
                    end, self)
                else
                    self.RewardItem1:SetClicked(false)
                    self:Hide(true, true)
                end
            end
            -- Module.Collection:ShowWeaponSkinDetailPage(itemData)
            Module.Collection:ShowWeaponSkinDetailPage(itemData, CreateCallBack(fVisibilityControl,self))--武器皮肤(包括刀皮)
        else
            Module.ItemDetail:OpenItemDetailPanel(itemData, index == 1 and self.RewardItem1 or self.RewardItem2)
        end

        local curItem = index == 1 and self.RewardItem1 or self.RewardItem2
        local preItem = index == 2 and self.RewardItem1 or self.RewardItem2
        curItem:SetClicked(true)
        preItem:SetClicked(false)

    end
end

--- 任务领奖
function MorgenRewardPop:OnTaskRewardClicked(reward_id)
    --领奖
    local fCallBack = SafeCallBack(self.RefreshInfo, self)
    Server.ActivityServer:SendMorgenGetAwardReq(self._activityID, reward_id, fCallBack)
end

--- 导航栏
function MorgenRewardPop:_OnGroupBoxCount()
    return 2
end

function MorgenRewardPop:_OnGroupBoxWidget(position, itemWidget)
    --设置红点
    if self._wtTabListReddot == nil then
        self._wtTabListReddot = {}
    end
    if self._wtTabListReddot[position] == nil then
        local redTrie = Module.ReddotTrie:CreateReddotIns(itemWidget)
        self._wtTabListReddot[position] = redTrie
        logwarning("[MorgenRewardPop] _OnGroupBoxWidget Create: ", position)
    end
    self:_RefreshTabReddot(position)
end

function MorgenRewardPop:_OnGroupBoxIndexChanged(position)
    self.curIndex = position + 1
    self:RefreshUI()
end

function MorgenRewardPop:_RefreshTabReddot(position)
    if self._wtTabListReddot == nil or self._wtTabListReddot[position] == nil then
        logwarning("[MorgenRewardPop] _RefreshTabReddot uiIns is invalid")
        return
    end

    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if activityInfo == nil or next(activityInfo) == nil then return end
    local morgenInfo = activityInfo.arknights_game_info

    local bShow = false
    if position == 0 then
        for i, reward in ipairs(morgenInfo.rewards) do
            if reward.type == 0 then
                if self.curCycle > reward.cycle and not reward.received then
                    bShow = true
                end
            end
        end
    else
        for i, reward in ipairs(morgenInfo.rewards) do
            if reward.type == 1 then
                if morgenInfo.gain_money_num >= reward.need_money_num and not reward.received then
                    bShow = true
                end
            elseif reward.type == 2 then
                if morgenInfo.meet_reward_npc and not reward.received then
                    bShow = true
                end
            end
        end
    end
    self._wtTabListReddot[position]:SetReddotVisible(bShow, EReddotType.Normal)
    loginfo("[MorgenRewardPop] _RefreshTabReddot, index = " .. position .. ", bShow = " .. tostring(bShow))
end

--- 任务奖励
function MorgenRewardPop:_OnGetTaskCount()
    return #self.rewards - 2
end

function MorgenRewardPop:_OnProcessTaskWidget(index, widget)
    table.insert(self.taskItems, widget)
    local reward = self.rewards[index + 2]
    widget:RefreshInfo(reward, self.moneyNum, self.bIsMeetNPC, index)
end


function MorgenRewardPop:_OnMouseButtonDown(mouseEvent)
    local items = {}
    if self.curIndex == 1 then
        items = self.rewardItems
    else
        items = self.taskItems
    end

    local isUnder = false
    local sceenPos = mouseEvent:GetScreenSpacePosition()
    for i, item in ipairs(items) do
        local geometry = item:GetCachedGeometry()
        isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos) or isUnder
    end

    if not isUnder then
        for i, item in ipairs(items) do
            item:SetClicked(false)
        end
    end
end
--#endregion

-----------------------------------------------------其他函数-----------------------------------------------------
--#region

function MorgenRewardPop:SetAllText()
    self._wtCommonPopWin:SetTitle(ActivityConfig.Loc.MogenRewardText[1])
    self._wtRewardText:SetText(ActivityConfig.Loc.MogenRewardText[2])
    self.RewardItem1:SetText(ActivityConfig.Loc.MogenRewardText[3])
    self.RewardItem2:SetText(ActivityConfig.Loc.MogenRewardText[4])
end

function MorgenRewardPop:OnNavBack()
    self:OnCloseBtnClicked()
    return true
end

function MorgenRewardPop:OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function MorgenRewardPop:InitGamepad()
    if not IsHD() then return end
    self:DisableGamepad()

    -- tab按键绑定
    if self._wtTitleTabGroup then
        self._SelectNextTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab_Trigger", EInputEvent.IE_Pressed
                    , self._wtTitleTabGroup.OnNext, self._wtTitleTabGroup, EDisplayInputActionPriority.UI_Pop)
        self._SelectPrevTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab_Trigger", EInputEvent.IE_Pressed
                    , self._wtTitleTabGroup.OnPrev, self._wtTitleTabGroup, EDisplayInputActionPriority.UI_Pop)
    end

    if self._wtTabKeyIconLeft then
        self._wtTabKeyIconLeft:SelfHitTestInvisible()
        self._wtTabKeyIconLeft:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconLeft:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0, false)
    end

    if self._wtTabKeyIconRight then
        self._wtTabKeyIconRight:SelfHitTestInvisible()
        self._wtTabKeyIconRight:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconRight:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0, false)
    end

end

function MorgenRewardPop:InitRewardGamepad()
    WidgetUtil.RemoveNavigationGroup(self)
    self._tab2NavGroup = nil

    if not self._tab1NavGroup then
        self._tab1NavGroup = WidgetUtil.RegisterNavigationGroup(self.RewardPanel, self, "Hittest")
    end
    if self._tab1NavGroup then
        self._tab1NavGroup:AddNavWidgetToArray(self.RewardItem1)
        self._tab1NavGroup:AddNavWidgetToArray(self.RewardItem2)
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._tab1NavGroup)
        self._tab1NavGroup:MarkIsStackControlGroup()

        ---popui下栈ui onshow慢于popui需要异步
        Timer.DelayCall(0.05, function()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._tab1NavGroup)
        end, self)
    end
end

function MorgenRewardPop:InitTaskGamepad()
    WidgetUtil.RemoveNavigationGroup(self)
    self._tab1NavGroup = nil
    
    if not self._tab2NavGroup then
        self._tab2NavGroup = WidgetUtil.RegisterNavigationGroup(self.TaskPanel, self, "Hittest")
    end
    if self._tab2NavGroup then
        self._tab2NavGroup:AddNavWidgetToArray(self.TaskPanel)
        self._tab2NavGroup:SetScrollRecipient(self.TaskPanel)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._tab2NavGroup)
        self._tab2NavGroup:MarkIsStackControlGroup()
    end
end

function MorgenRewardPop:DisableGamepad()
    if not IsHD() then return end

    if self._SelectNextTabHandle then
        self:RemoveInputActionBinding(self._SelectNextTabHandle)
    end

    if self._SelectPrevTabHandle then
        self:RemoveInputActionBinding(self._SelectPrevTabHandle)
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._tab1NavGroup = nil
    self._tab2NavGroup = nil

end
--#endregion

return MorgenRewardPop