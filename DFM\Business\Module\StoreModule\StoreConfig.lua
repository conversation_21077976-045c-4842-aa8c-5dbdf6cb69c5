----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
UITable[UIName2ID.StoreRecommendBuyMarket] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.RecommendBuyMarket",
    BPKey = "WBP_Store_PacksPurchasePop",
    IsModal = true,
}

UITable[UIName2ID.StoreProductPreviewMarket] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.ProductPreviewMarket",
    BPKey = "WBP_Store_ProductPreview",
    ReConfig = {
        IsPoolEnable = true,
    },

    Anim = {
        FlowInAni = "WBP_Store_ProductPreview_in",
        FlowOutAni = "WBP_Store_ProductPreview_out",
    },
}

UITable[UIName2ID.StoreTimeLimitMarket] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.StoreTimeLimitMarket",
    BPKey = "WBP_Store_WeaponSkinsSale",
    
    SubUIs = {
        UIName2ID.DFButtonCarousel2,
    },

    Anim = {
        FlowInAni = "WBP_Store_WeaponSkinsSale_in",
        FlowOutAni = "WBP_Store_WeaponSkinsSale_out",
    }
}

-- UITable[UIName2ID.StoreBHDMain] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Main.StoreBHDMain",
--     BPKey = "WBP_Store_BHD",

--     Anim = {
--         FlowInAni = "WBP_Store_BHD_in",
--     }
-- }

UITable[UIName2ID.StoreRecommendHomepage] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.RecommendHomepage",
    BPKey = "WBP_Store_RecommendHomepage",
    SubUIs = {
        UIName2ID.VFXDeepThreat,                                  
        UIName2ID.VFXFantasia,                              
        UIName2ID.VFXRacingMaster,
        UIName2ID.VFXPlatinumShadow,
        UIName2ID.VFXVacuumPunk,
        UIName2ID.VFXWaltz,
        UIName2ID.VFXTacticalStrategy,
        UIName2ID.VFXBlackGoldDog,
        UIName2ID.VFXFatDog,
        UIName2ID.VFXTeslaRings,
        UIName2ID.VFXStoryBoard,
        UIName2ID.VFXZeroGray,
        UIName2ID.VFXHunterReward,
    },

    -- SubUIs = {
    --     UIName2ID.StoreCountDown,
    -- },

    Anim = {
        FlowInAni = "WBP_Store_RecommendHomepage_in",
    }
}

UITable[UIName2ID.StoreRecommendHomepageItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreRecommendItem",
    BPKey = "WBP_Store_PacksBtn",
}

UITable[UIName2ID.StoreCountDownView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreCountDown",
    BPKey = "WBP_Store_Countdown",
}

UITable[UIName2ID.StorePacksItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StorePacksItem",
    BPKey = "WBP_Store_PacksItem",
    SubUIs = {
        UIName2ID.IVCommercializeItemTemplate,
    },

}

UITable[UIName2ID.StorePacksItemBuyPacks] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StorePacksItemBuyPacks",
    BPKey = "WBP_Store_PacksItem_2",
    SubUIs = {
        UIName2ID.IVShopItemTemplate,
    },
}

UITable[UIName2ID.StoreWeaponSkinItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreWeaponSkinItem",
    BPKey = "WBP_Store_WeaponSkinsBtn",
}

UITable[UIName2ID.StoreWeaponSkinsSale] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.WeaponSkinsSale",
    BPKey = "WBP_Store_WeaponSkinsSale",

    SubUIs = {
        UIName2ID.DFButtonCarousel2,
    },

    Anim = {
        FlowInAni = "WBP_Store_WeaponSkinsSale_in",
        FlowOutAni = "WBP_Store_WeaponSkinsSale_out",
    }
}

UITable[UIName2ID.StoreWeaponDiscountBundle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.WeaponDiscountBundle",
    BPKey = "WBP_Store_DiscounPackst",
    Anim = {
        FlowInAni = "WBP_Store_DiscounPackst_in",
    }
}


UITable[UIName2ID.StoreMandelDraw] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.MandelDraw",
    BPKey = "WBP_Store_TheChestOpensMain",
    SubUIs = {
        UIName2ID.StorePrizesList,
        UIName2ID.StoreSweepHistory,
        UIName2ID.CommonEmptyContent,
    },

    Anim = {
        FlowInAni = "WBP_Store_TheChestOpensMain_in",
        FlowOutAni = "WBP_Store_TheChestOpensMain_out",
    }
}

UITable[UIName2ID.StoreMandelDrawOnly] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.MandelDrawOnly",
    BPKey = "WBP_Store_TheChestOpensMain",
    SubUIs = {
        UIName2ID.StorePrizesList,
        UIName2ID.StoreSweepHistory,
    },
    Anim = {
        FlowInAni = "WBP_Store_TheChestOpensMain_in",
        FlowOutAni = "WBP_Store_TheChestOpensMain_out",
    }
}

UITable[UIName2ID.StoreActivityMandelDrawOnly] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.ActivityMandelDrawOnly",
    BPKey = "WBP_Store_TheChestOpensMain",
    SubUIs = {
        UIName2ID.StorePrizesList,
        UIName2ID.StoreSweepHistory,
    },
    Anim = {
        FlowInAni = "WBP_Store_TheChestOpensMain_in",
        FlowOutAni = "WBP_Store_TheChestOpensMain_out",
    }
}

UITable[UIName2ID.StoreActivityMandelDrawOnly2] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.ActivityMandelDrawOnly",
    BPKey = "WBP_Store_TheChestOpensMain",
    SubUIs = {
        UIName2ID.StorePrizesList,
        UIName2ID.StoreSweepHistory,
    },
    Anim = {
        FlowInAni = "WBP_Store_TheChestOpensMain_in",
        FlowOutAni = "WBP_Store_TheChestOpensMain_out",
    }
}

UITable[UIName2ID.StoreParseSpecify] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.StoreParseSpecify",
    BPKey = "WBP_Store_ParseSpecify",
}

UITable[UIName2ID.StoreMandelContinuousReward] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreMandelContinuousReward",
    BPKey = "WBP_Hero_RewardsOverview",
    IsModal = true,
}

UITable[UIName2ID.StorePrizeOverview] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.PrizeOverview",
    BPKey = "WBP_Store_PrizeOverview",
    LinkSubStage = ESubStage.HallWeaponShow,
   
}

UITable[UIName2ID.StoreRechargeMainPanle] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.RechargePanel",
    BPKey = "WBP_Store_RechargePanel",

    SubUIs = {
        UIName2ID.StoreRechargeItem,
    },

    Anim = {
        FlowInAni = "WBP_Store_RechargePanel_in",
    }
    
}


UITable[UIName2ID.StoreRechargePanle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.RechargePanel",
    BPKey = "WBP_Store_RechargePanel",
    
    SubUIs = {
        UIName2ID.StoreRechargeItem,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        FlowInAni = "WBP_Store_RechargePanel_in",
    }
}

UITable[UIName2ID.StoreRechargeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreRechargeItem",
    BPKey = "WBP_Store_RechargeBtn",
}

--通用道具购买弹窗
UITable[UIName2ID.PurchasePropsPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.PurchasePropsPopWindow",
    BPKey = "WBP_Store_BuyKeys",
    IsModal = true,
}

UITable[UIName2ID.StoreMandelBuyKeys] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.MandelBuyKeys",
    BPKey = "WBP_Store_BuyKeys",
    IsModal = true,
}

UITable[UIName2ID.StoreMandelPaddedGoods] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.MandelPaddedGoods",
    BPKey = "WBP_Store_PaddedGoods",
    IsModal = true,
}

UITable[UIName2ID.ActivityMandelBuy] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.ActivityMandelBuy",
    BPKey = "WBP_Store_PaddedGoods",
    IsModal = true,
}

UITable[UIName2ID.StoreMandelPaddedGoodItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreMandelPaddedGoodItem",
    BPKey = "WBP_Store_KeyItem",
}

UITable[UIName2ID.StoreRecommendBuyPacks] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.RecommendBuyPacks",
    BPKey = "WBP_Store_PacksPurchasePop",
    IsModal = true,
}


UITable[UIName2ID.StoreMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.StoreMainPanel",
    BPKey = "WBP_Store_Main",
    SubUIs = {
        UIName2ID.StoreRecommendHomepage,
        UIName2ID.StoreWeaponSkinsSale,
        -- UIName2ID.StoreSafetyBoxPanel,
        UIName2ID.StoreMandelDraw,
        UIName2ID.StoreRechargePanle,
        UIName2ID.StaffLotteryMainUI,
        UIName2ID.StoreNightMarket,
        -- UIName2ID.StoreBHDMain,
    },
    -- Anim = {
    --     FlowInAni = "WBP_Store_Main_in",
    --     FlowOutAni = "WBP_Store_Main_out",
    -- },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallMall,
}



UITable[UIName2ID.StoreProductPreview] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.ProductPreview",
    BPKey = "WBP_Store_ProductPreview",
    SubUIs = {
        UIName2ID.StaffLotteryVoice,
        UIName2ID.StorePacksItemView,
    },
    -- LinkSubStage = ESubStage.HallMall,

    ReConfig = {
        IsPoolEnable = true,
    },

    Anim = {
        FlowInAni = "WBP_Store_ProductPreview_in",
        FlowOutAni = "WBP_Store_ProductPreview_out",
    },
    -- IsModal = true,
}

-- UITable[UIName2ID.StoreTabBtnList] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Main.StoreTabBtnList",
--     BPKey = "WBP_Store_ListBtnV1",
--     Anim = {
--         FlowInAni = "WBP_Store_ListBtnV1_in",
--         FlowOutAni = "WBP_Store_ListBtnV1_out",
--     }
-- }

UITable[UIName2ID.StoreBuyButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreBuyButton",
    BPKey = "WBP_Store_ButButton",
    Anim = {
        FlowInAni = "WBP_Store_ButButton_in",
        FlowOutAni = "WBP_Store_ButButton_out",
    }
}

UITable[UIName2ID.StoreBuyHeroWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyHeroWindow",
    BPKey = "WBP_Store_HeroBuy",
    Anim = {
        FlowInAni = "WBP_Store_HeroBuy_in",
        FlowOutAni = "WBP_Store_HeroBuy_out",
    }
}

UITable[UIName2ID.StoreBoxDesc] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBoxDesc",
    BPKey = "WBP_Store_BoxDescription",
    Anim = {
        FlowInAni = "WBP_Store_BoxDescription_in",
        FlowOutAni = "WBP_Store_BoxDescription_out",
    }
}

UITable[UIName2ID.StoreBuyAppearanceWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyAppearanceWindow",
    BPKey = "WBP_Store_DecorationExterior",
    Anim = {
        FlowInAni = "WBP_Store_DecorationExterior_in",
        FlowOutAni = "WBP_Store_DecorationExterior_out",
    }
}

UITable[UIName2ID.StoreBuyGunSkinWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyGunSkinWindow",
    BPKey = "WBP_Store_FirearmsBuy",
    Anim = {
        FlowInAni = "WBP_Store_FirearmsBuy_in",
        FlowOutAni = "WBP_Store_FirearmsBuy_out",
    }
}

UITable[UIName2ID.StoreBuyGunThemeWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyGunThemeWindow",
    BPKey = "WBP_Store_FirearmsExterior",
    Anim = {
        FlowInAni = "WBP_Store_FirearmsExterior_in",
        FlowOutAni = "WBP_Store_FirearmsExterior_out",
    }
}

UITable[UIName2ID.StoreBuyKeyWindow] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyKeyWindow",
    BPKey = "WBP_Store_BuyLuckyBox_Pop",
    Anim = {
        FlowInAni = "WBP_Store_BuyLuckyBox_in",
        FlowOutAni = "WBP_Store_BuyLuckyBox_out",
    }
}

UITable[UIName2ID.StoreBuyPropWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyPropWindow",
    BPKey = "WBP_Store_BuysingleProduct_Pop",
    Anim = {
        FlowInAni = "WBP_Store_BuysingleProduct_in",
        FlowOutAni = "WBP_Store_BuysingleProduct_out",
    }
}

-- UITable[UIName2ID.StoreCouponItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreCouponItem",
--     BPKey = "WBP_Store_Coupon",
--     Anim = {
--         FlowInAni = "WBP_Store_Coupon_in",
--         FlowOutAni = "WBP_Store_Coupon_out",
--     }
-- }

-- UITable[UIName2ID.StoreChargeGetCurrencyItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreChargeGetCurrencyItem",
--     BPKey = "WBP_Store_BuyCurrency",
--     Anim = {
--         FlowInAni = "WBP_Store_BuyCurrency_in",
--         FlowOutAni = "WBP_Store_BuyCurrency_out",
--     }
-- }

-- UITable[UIName2ID.StoreChargeGetCurrencyPanel] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreChargeGetCurrencyPanel",
--     BPKey = "WBP_Store_RechargeInterface",
--     SubUIs = {
--         UIName2ID.StoreChargeGetCurrencyItem,
--     },
--     Anim = {
--         FlowInAni = "WBP_Store_RechargeInterface_in",
--         FlowOutAni = "WBP_Store_RechargeInterface_out",
--     }
-- }

UITable[UIName2ID.StoreCurrencyExchangeWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreCurrencyExchangeWindow",
    BPKey = "WBP_Store_ConfirmPop",
    Anim = {
        FlowInAni = "WBP_Store_ConfirmPop_in",
        FlowOutAni = "WBP_Store_ConfirmPop_out",
    }
}

UITable[UIName2ID.StoreBuyLuckyCoinWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyLuckyCoinWindow",
    BPKey = "WBP_Store_LuckyCoinPop",
}

--通用购买确认弹窗
UITable[UIName2ID.StoreBuyConfigmWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyConfigmWindow",
    BPKey = "WBP_Store_ConfirmPop_2",
    Anim = {
        FlowInAni = "WBP_Store_ConfirmPop_2_in",
        FlowOutAni = "WBP_Store_ConfirmPop_2_out",
    }
}

--普通样式商品，通用
UITable[UIName2ID.StoreProductItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreProductItem",
    BPKey = "WBP_Store_Product",
    Anim = {
        FlowInAni = "WBP_Store_Product_in",
        FlowOutAni = "WBP_Store_Product_out",
    }
}

UITable[UIName2ID.StoreProductPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreProductPanel",
    BPKey = "WBP_Store_ProductPage",
    SubUIs = {
        UIName2ID.StoreProductItem,
    },
    Anim = {
        FlowInAni = "WBP_Store_ProductPage_in",
        FlowOutAni = "WBP_Store_ProductPage_out",
    }
}

UITable[UIName2ID.StoreBoxPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreBoxPanel",
    BPKey = "WBP_Store_BoxPage_Page",
    SubUIs = {
        UIName2ID.StoreBoxFireItem,
    },
    Anim = {
        FlowInAni = "WBP_Store_BoxPage_in",
        FlowOutAni = "WBP_Store_BoxPage_out",
    }
}

UITable[UIName2ID.StorePropPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StorePropPanel",
    BPKey = "WBP_Store_PropPage_Page",
    SubUIs = {
        UIName2ID.StoreProductItem,
    },
    Anim = {
        FlowInAni = "WBP_Store_PropPage_in",
        FlowOutAni = "WBP_Store_PropPage_out",
    }
}

UITable[UIName2ID.StorePropLuckyPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StorePropLuckyPanel",
    BPKey = "WBP_Store_LuckyPage_Page",
    Anim = {
        FlowInAni = "WBP_Store_LuckyPage_in",
        FlowOutAni = "WBP_Store_LuckyPage_out",
    }
}


--[[UITable[UIName2ID.StoreSortItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreSortItem",
    BPKey = "WBP_CommonDroDownBox_Btn",
}--]]

UITable[UIName2ID.StoreGunThemePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreGunThemePanel",
    BPKey = "WBP_Store_FirearmsThemePage",
    SubUIs = {
        UIName2ID.StoreGunThemeItem,
    },
    Anim = {
        FlowInAni = "WBP_Store_FirearmsThemePage_in",
        FlowOutAni = "WBP_Store_FirearmsThemePage_out",
    }
}

UITable[UIName2ID.StoreGunThemeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreGunThemeItem",
    BPKey = "WBP_Store_FirearmsTheme",
    Anim = {
        FlowInAni = "WBP_Store_FirearmsTheme_in",
        FlowOutAni = "WBP_Store_FirearmsTheme_out",
    }
}

UITable[UIName2ID.StoreBuyGunThemeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreBuyGunThemeItem",
    BPKey = "WBP_Store_FirearmsMini",
    Anim = {
        FlowInAni = "WBP_Store_FirearmsMini_in",
        FlowOutAni = "WBP_Store_FirearmsMini_out",
    }
}

UITable[UIName2ID.StorePropTipsList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StorePropTipsList",
    BPKey = "WBP_Store_PropTipsList",
    Anim = {
        FlowInAni = "WBP_Store_PropTipsList_in",
        FlowOutAni = "WBP_Store_PropTipsList_out",
    }
}

UITable[UIName2ID.StoreGunPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreGunPanel",
    BPKey = "WBP_Store_FirearmsPage",
    SubUIs = {
        UIName2ID.StoreBoxFireItem,
    },
    Anim = {
        FlowInAni = "WBP_Store_FirearmsPage_in",
        FlowOutAni = "WBP_Store_FirearmsPage_out",
    }
}

UITable[UIName2ID.StoreDesignerStoryWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreDesignerStoryWindow",
    BPKey = "WBP_Store_FirearmsDesignerStory",
    Anim = {
        FlowInAni = "WBP_Store_FirearmsDesignerStory_in",
        FlowOutAni = "WBP_Store_FirearmsDesignerStory_out",
    }
}

UITable[UIName2ID.StoreGunFilterToggle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreGunFilterToggle",
    BPKey = "WBP_Store_FirearmsToggle",
    Anim = {
        FlowInAni = "WBP_Store_FirearmsToggle_in",
        FlowOutAni = "WBP_Store_FirearmsToggle_out",
    }
}

--宽样式商品，通用
UITable[UIName2ID.StoreBoxItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreBoxItem",
    BPKey = "WBP_Store_Box",
    -- SubUIs = {
    --     UIName2ID.IVMainIconComponent,
    -- },
    Anim = {
        FlowInAni = "WBP_Store_Box_in",
        FlowOutAni = "WBP_Store_Box_out",
    }
}

--宝箱商品，通用
UITable[UIName2ID.StoreBoxFireItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreBoxFireItem",
    BPKey = "WBP_Store_FirearmsItem",
}

-- --武器-宝箱
-- UITable[UIName2ID.StoreWeaponBoxPanel] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreWeaponBoxPanel",
--     BPKey = "WBP_Store_ChestHomePage",
--     Anim = {
--         FlowInAni = "WBP_Store_ChestHomePage_in",
--         FlowOutAni = "WBP_Store_ChestHomePage_out",
--     }
-- }

-- --普通样式商品，通用
-- UITable[UIName2ID.StoreWeaponBoxItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreWeaponBoxItem",
--     BPKey = "WBP_Store_Box_2",
--     Anim = {
--         FlowInAni = "WBP_Store_Box_in",
--         FlowOutAni = "WBP_Store_Box_out",
--     }
-- }



-- UITable[UIName2ID.CorePrizeItem] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.CorePrizeItem",
--     BPKey = "WBP_Store_ItemName",
-- }


UITable[UIName2ID.StorePrizesList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.StorePrizesList",
    SubUIs = {
        UIName2ID.StoreItemBox,
        UIName2ID.StorePrizeBox,
    },
    BPKey = "WBP_Store_PrizesList",
}

UITable[UIName2ID.StoreSweepHistory] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.StoreSweepHistory",

    BPKey = "WBP_Store_SweepHistory",
}

UITable[UIName2ID.StoreItemBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.StoreItemBox",
    BPKey = "WBP_Store_ItemBox",
}

UITable[UIName2ID.StorePrizesPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.StorePrizesPop",
    BPKey = "WBP_Store_AllPrizesPop",
    bManuelAnim = true,
    -- ReConfig = {
    --     IsPoolEnable = true,
    --     MaxPoolLength = 1,
    -- },
    IsModal = true,
}

UITable[UIName2ID.StorePrizeBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.StorePrizeBox",
    BPKey = "WBP_Store_PrizesBox",
}

-- UITable[UIName2ID.StoreSafetyBoxSellPanel] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreSafetyBoxSellPanel",
--     BPKey = "WBP_Store_SafetyBoxSellPanel",
-- }

-- UITable[UIName2ID.StoreSafetyBoxPanel] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StoreSafetyBoxPanel",
--     BPKey = "WBP_Store_SafetyBoxPanel",
--     SubUIs = {
--         UIName2ID.StoreSafetyBoxSellPanel,
--     },
-- }

UITable[UIName2ID.StaffLotteryMainUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryMainUI",
    BPKey = "WBP_StaffLottery_MainUI",
    SubUIs = {
        UIName2ID.StaffLotteryBg,
        UIName2ID.RoleSkinSMYT,
        UIName2ID.StoreLunaBlack,
        UIName2ID.StoreZoyaAngel,
    },
    Anim = {
        FlowInAni = "WBP_StaffLottery_MainUI_in",
        FlowOutAni = "WBP_StaffLottery_MainUI_out",
    }
}

UITable[UIName2ID.StaffLotteryPreview] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StaffLotteryPreview",
    BPKey = "WBP_StaffLottery_Preview_Level2",
    SubUIs = {
        UIName2ID.StaffLotteryVoice,
    },
    Anim = {
        FlowInAni = "WBP_Store_ProductPreview_in",
        FlowOutAni = "WBP_Store_ProductPreview_out",
    },
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.StaffLotteryVoice] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryVoice",
    BPKey = "WBP_StaffLottery_Voice",
    SubUIs = {
        UIName2ID.StaffLotteryVoiceItem,
    },
    Anim = {
        FlowInAni = "WBP_StaffLottery_Voice_in",
        FlowOutAni = "WBP_StaffLottery_Voice_out",
    }
}

UITable[UIName2ID.StaffLotteryVoiceItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryVoiceItem",
    BPKey = "WBP_StaffLottery_VoiceItem",
    Anim = {
        FlowInAni = "WBP_Hero_JobTitle_in",
        FlowOutAni = "WBP_Hero_JobTitle_out",
    }
}

UITable[UIName2ID.StaffLotteryAccessoriesPreview] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StaffLotteryAccessoriesPreview",
    BPKey = "WBP_StaffLottery_Preview_Level3",
    SubUIs = {
        UIName2ID.StaffLotteryVoice,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        FlowInAni = "WBP_Store_ProductPreview_in",
        FlowOutAni = "WBP_Store_ProductPreview_out",
    },
    LinkSubStage = ESubStage.OperatorItem
}

UITable[UIName2ID.StaffLotteryPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StaffLotteryPop",
    BPKey = "WBP_StaffLotteryPop",
    SubUIs = {
        UIName2ID.StaffLotteryPopList,
    },
    IsModal = true,
}


UITable[UIName2ID.StaffLotteryPurchasePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StaffLotteryPurchasePop",
    BPKey = "WBP_Store_PacksPurchasePop",
    IsModal = true,
}

UITable[UIName2ID.HeroPropMysticalSkinPurchasePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.HeroPropMysticalSkinPurchasePop",
    BPKey = "WBP_Store_PacksPurchasePop",
    IsModal = true,
}

UITable[UIName2ID.HeroPaintSchemeSavePurchasePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.HeroPaintSchemeSavePurchasePop",
    BPKey = "WBP_Hero_SavePaintPurchasePop",
    IsModal = true,
}

UITable[UIName2ID.StaffLotteryPopList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryPopList",
    BPKey = "WBP_StaffLotteryPop_List",
    SubUIs = {
        UIName2ID.StaffLotteryPopListItem,
    },
}

UITable[UIName2ID.StaffLotteryPopListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryPopListItem",
    BPKey = "WBP_StaffLotteryPop_ListItem",
}

UITable[UIName2ID.ExchangeMandelCoins] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.ExchangeMandelCoins",
    BPKey = "WBP_Common_ExchangeMandelCoins",
    IsModal = true,
}

UITable[UIName2ID.StoreCollabTabTransition] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.StoreCollabTabTransition",
    Anim = {
        FlowInAni = "Anim_Tab_In",
    },
    BPKey = "WBP_Store_Transition",
    IsModal = true,
}

UITable[UIName2ID.StoreNightMarket] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.NightMarket",
    BPKey = "WBP_Store_NM_Main",
    SubUIs = {
        UIName2ID.StoreNightMarketList,
    },
}

UITable[UIName2ID.StoreNightMarketList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Main.NightMarketList",
    BPKey = "WBP_Store_NM_ShowList",
    -- Anim = {
    --     FlowInAni = "WBP_Store_NM_ShowList_In",
    --     FlowOutAni = "WBP_Store_NM_ShowList_Out",
    -- },
}

UITable[UIName2ID.StoreNMShowPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.NightMarketShowPop",
    BPKey = "WBP_Store_NM_ShowPop",
    SubUIs = {
        UIName2ID.StoreNMCardItem,
    },
    Anim = {
        FlowInAni = "WBP_Store_NM_ShowPop_In",
        FlowOutAni = "WBP_Store_NM_ShowPop_Out",
    },
}

UITable[UIName2ID.StoreNMCardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.NightMarketCardItem",
    BPKey = "WBP_Store_NM_CardItem",
}

UITable[UIName2ID.StoreNMRewardPop] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.NightMarketRewardPop",
    BPKey = "WBP_Store_NM_RewardPop",
}

UITable[UIName2ID.StoreNMGiftPop] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Pop.NightMarketGiftPop",
    BPKey = "WBP_Store_NM_GiftPop",
    Anim = {
        FlowInAni = "WBP_Store_NM_GiftPop_In",
        FlowOutAni = "WBP_Store_NM_GiftPop_Out",
    },
}

----------------------------------------动效蓝图UI—-------------------------------------------
UITable[UIName2ID.VFXDeepThreat] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_DeepThreat",
}

UITable[UIName2ID.VFXFantasia] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_Fantasia",
}

UITable[UIName2ID.VFXRacingMaster] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_RacingMaster",
}

UITable[UIName2ID.VFXPlatinumShadow] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_PlatinumShadow",
}

UITable[UIName2ID.VFXVacuumPunk] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_VacuumPunk",
}

UITable[UIName2ID.VFXWaltz] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_Waltz",
}

UITable[UIName2ID.VFXTacticalStrategy] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_TacticalStrategy",
}

UITable[UIName2ID.VFXBlackGoldDog] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_BlackGoldDog",
}

UITable[UIName2ID.VFXFatDog] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_FatDog",
}

UITable[UIName2ID.VFXTeslaRings] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_TeslaRings",
}

UITable[UIName2ID.VFXStoryBoard] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_StoryBoard",
}

UITable[UIName2ID.VFXZeroGray] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_ZeroGray",
}

UITable[UIName2ID.VFXHunterReward] = {                                   
    UILayer = EUILayer.Sub,
    BPKey = "WBP_Store_HunterReward",
}

----------------------------------------动效蓝图UI—-------------------------------------------
---
---
----------------------------------------抽奖底图UI--------------------------------------------
UITable[UIName2ID.StaffLotteryBg] = {                                   --蚀金玫瑰
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryBgUI",
    BPKey = "WBP_StaffLottery_Bg",
}

UITable[UIName2ID.RoleSkinSMYT] = {                                         --水墨云图
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryBgUI",
    BPKey = "WBP_RoleSkin_SMYT",
}

UITable[UIName2ID.StoreLunaBlack] = {                                         --黑天际线
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryBgUI",
    BPKey = "WBP_Store_LunaBlack",
}

UITable[UIName2ID.StoreZoyaAngel] = {                                         --能天使
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.StoreModule.UI.Sub.StaffLotteryBgUI",
    BPKey = "WBP_Store_ZoyaAngel",
}

----------------------------------------抽奖底图UI--------------------------------------------

local StoreConfig =
{
    StoreTabConfig = Facade.TableManager:GetTable("StoreTab"),
    --StoreChargeConfig = Facade.TableManager:GetTable("StoreCharge"),
    --StoreItemConfig = Facade.TableManager:GetTable("StoreItem"),

    --选中商品变化
    evtSelectItemChange = LuaEvent:NewIns("evtSelectItemChange"),
    --选中排序规则变化
    evtSelectSortItemChange = LuaEvent:NewIns("evtSelectSortItemChange"),
    --打开模型放大界面
    evtOpenModelView = LuaEvent:NewIns("evtOpenModelView"),
    --枪械皮肤筛选
    evtSelectGunFilterChange = LuaEvent:NewIns("evtSelectGunFilterChange"),
    --枪械皮肤需要刷新购买记录
    evtOnWeaponSkinSaleNeedRefreshBuyRecord = LuaEvent:NewIns("evtOnWeaponSkinSaleNeedRefreshBuyRecord"),

    evtStoreMandelPropItemClick = LuaEvent:NewIns("evtStoreMandelPropItemClick"),
    evtStoreRecommendItemClick = LuaEvent:NewIns("evtStoreRecommendItemClick"),
    evtStoreRecommendItemJumpClick = LuaEvent:NewIns("evtStoreRecommendItemJumpClick"),
    evtStoreProductViewBundleItemClick = LuaEvent:NewIns("evtStoreProductViewBundleItemClick"),
    evtStoreSwitchMainTabByIndex = LuaEvent:NewIns("evtStoreSwitchMainTabByIndex"),
    evtStoreMallGiftPostClick = LuaEvent:NewIns("evtStoreMallGiftPostClick"),
    evtStoreWeaponSkinItemClick = LuaEvent:NewIns("evtStoreWeaponSkinItemClick"),
    evtStoreTimeLimitMarketItemClick = LuaEvent:NewIns("evtStoreTimeLimitMarketItemClick"),
    evtStorePaddingMandelItems = LuaEvent:NewIns("evtStorePaddingMandelItems"),
    evtStorePaddingMandelItemsRecord = LuaEvent:NewIns("evtStorePaddingMandelItemsRecord"),
    evtStoreRechargeItemClick = LuaEvent:NewIns("evtStoreRechargeItemClick"),
    evtStoreRecommendItemHover = LuaEvent:NewIns("evtStoreRecommendItemHover"),
    evtStoreRecommendItemUnHover = LuaEvent:NewIns("evtStoreRecommendItemUnHover"),
    evtStoreRecommendItemChangedSelect = LuaEvent:NewIns("evtStoreRecommendItemChangedSelect"),
    evtCloseMandelPaddedGoods = LuaEvent:NewIns("evtCloseMandelPaddedGoods"),
    evtStoreMainPanelRefreshCurrencyStateDone = LuaEvent:NewIns("evtStoreMainPanelRefreshCurrencyStateDone"), -- 商城刷新货币栏完成
    evtStoreMainPanelRefreshCurrencyState = LuaEvent:NewIns("evtStoreMainPanelRefreshCurrencyState"), -- 商城刷新货币栏
    evtStorePlayTransition = LuaEvent:NewIns("evtStorePlayTransition"),

    --product preview
    evtProductPreviewGetAndPlayEfx = LuaEvent:NewIns("evtProductPreviewGetAndPlayEfx"),

    
    evtMandelDrawShow = LuaEvent:NewIns("evtMandelDrawShow"),
    evtMandelDrawHide = LuaEvent:NewIns("evtMandelDrawHide"),

    evtMandelDrawKeysBought = LuaEvent:NewIns("evtMandelDrawKeysBought"),--当购买了曼德尔密匙

    evtStaffLotteryPurchasePopSuccess = LuaEvent:NewIns("evtStaffLotteryPurchasePopSuccess"), -- 干员皮肤抽奖货币购买成功
    evtStaffLotteryStartPurchase = LuaEvent:NewIns("evtStaffLotteryStartPurchase"), -- 干员皮肤抽奖货币开始购买
    evtStoreMediaPlayEnd = LuaEvent:NewIns("evtStoreMediaPlayEnd"), -- 干员皮肤抽奖后的视频播放完毕
    evtStaffLotteryVedioPlay = LuaEvent:NewIns("evtStaffLotteryVedioPlay"), -- 页签动效播放玩播放奖池视频
    

    evtStoreMandelUpRewardSelect = LuaEvent:NewIns("evtStoreMandelUpRewardSelect"), -- 高概率奖励选择事件
    
    evtActivityMandelDrawOnlyOnShowBegin = LuaEvent:NewIns("evtActivityMandelDrawOnlyOnShowBegin"), -- 活动曼德尔砖界面打开
    evtStoreActivityMandelOpenMandelBox = LuaEvent:NewIns("evtStoreActivityMandelOpenMandelBox"), -- 活跃曼德尔砖开箱

    evtStoreNMMainShow = LuaEvent:NewIns("evtStoreNMMainShow"), -- 夜市首页显示

    evtStoreNMCardSearchAnimPlay = LuaEvent:NewIns("evtStoreNMCardSearchAnimPlay"), -- 夜市卡牌动画播放
    evtStoreNMCardShowAnimPlay = LuaEvent:NewIns("evtStoreNMCardShowAnimPlay"), -- 夜市卡牌出现播放


    Loc = {
        HafcoinInsufficient = NSLOCTEXT("StoreModule", "Lua_Store_HafcoinInsufficient", "哈夫币不足"),
        TopBarTitle = NSLOCTEXT("StoreModule", "Lua_Store_TopBarTitle", "商城"),
        TopBarTitleGift = NSLOCTEXT("StoreModule", "Lua_Store_TopBarTitleGift", "礼包"),
        TopBarTitleBundle = NSLOCTEXT("StoreModule", "Lua_Store_TopBarTitleBundle", "捆绑包"),
        TopBarTitleMandel = NSLOCTEXT("StoreModule", "Lua_Store_TopBarTitleMandel", "曼德尔砖"),
        TopBarTitleMandelActivity = NSLOCTEXT("StoreModule", "Lua_Store_TopBarTitleMandelActivity", "图灵砖"),
        CharegeNum = NSLOCTEXT("StoreModule", "Lua_Store_CharegeNum", "%s<customstyle color=\"Color_Highlight01\">+%s</>"),
        ItemPriceNormal = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceNormal", "<dfmrichtext type=\"img\" id=\"%s\"/>%s"),
        ItemPriceNormalBig = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceNormalBig", "<dfmrichtext type=\"img\" width=\"70\" height=\"70\" id=\"%s\"/>%s"),
        ItemPriceNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceNotEnough", "<dfmrichtext type=\"img\" id=\"%s\"/><customstyle color=\"Color_DarkNegative\">%s</>"),
        ItemPriceBuyNormal = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceBuyNormal", "<dfmrichtext type=\"img\" id=\"%s\"/> %s | 购买"),
        ItemPriceBuyNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceBuyNotEnough", "<dfmrichtext type=\"img\" id=\"%s\"/><customstyle color=\"Color_DarkNegative\"> %s | 购买</>"),
        ItemDiscount = NSLOCTEXT("StoreModule", "Lua_Store_ItemDiscount", "-{0}%"),
        SortTitle = NSLOCTEXT("StoreModule", "Lua_Store_SortTitle", "%s%s"),
        NotSelectItem = NSLOCTEXT("StoreModule", "Lua_Store_NotSelectItem", "未选中商品"),
        NoPayInfo = NSLOCTEXT("StoreModule", "Lua_Store_NoPayInfo", "价格信息缺失"),
        NotEnoughMoney = NSLOCTEXT("StoreModule", "Lua_Store_NotEnoughMoney", "货币不足"),
        BuySuc = NSLOCTEXT("StoreModule", "Lua_Store_BuySuc", "购买成功"),
        BuyFail = NSLOCTEXT("StoreModule", "Lua_Store_BuyFail", "购买失败"),
        BuyOwn = NSLOCTEXT("StoreModule", "Lua_Store_BuyOwn", "该商品已拥有，无法购买"),
        BuyHeroWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_BuyHeroWindowTitle", "购买英雄"),
        BuyAppearenceWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_BuyAppearenceWindowTitle", "购买外观"),
        BuyGunSkinWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_BuyGunSkinWindowTitle", "购买枪械皮肤"),
        BuyGunThemeWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_BuyGunThemeWindowTitle", "购买枪械主题"),
        BpTechTipsTitle = NSLOCTEXT("StoreModule", "Lua_Store_BpTechTipsTitle", "蓝图科技"),
        DyeTipsTitle = NSLOCTEXT("StoreModule", "Lua_Store_DyeTipsTitle", "配件染色"),
        RemainTime = NSLOCTEXT("StoreModule", "Lua_Store_RemainTime", "剩余时间：%s"),
        TimeYear = NSLOCTEXT("StoreModule", "Lua_Store_TimeYear", "年"),
        TimeMonth = NSLOCTEXT("StoreModule", "Lua_Store_TimeMonth", "月"),
        TimeWeek = NSLOCTEXT("StoreModule", "Lua_Store_TimeWeek", "周"),
        TimeDay = NSLOCTEXT("StoreModule", "Lua_Store_TimeDay", "天"),
        TimeHour = NSLOCTEXT("StoreModule", "Lua_Store_TimeHour", "时"),
        TimeMin = NSLOCTEXT("StoreModule", "Lua_Store_TimeMin", "分"),
        TimeSec = NSLOCTEXT("StoreModule", "Lua_Store_TimeSec", "秒"),
        RepeatItem = NSLOCTEXT("StoreModule", "Lua_Store_RepeatItem", "重复道具"),
        NoRepeatItem = NSLOCTEXT("StoreModule", "Lua_Store_NoRepeatItem", "不重复道具"),
        TwoPricePlus = NSLOCTEXT("StoreModule", "Lua_Store_TwoPricePlus", "%s+%s"),
        WaitServerData = NSLOCTEXT("StoreModule", "Lua_Store_WaitServerData", "数据刷新中...请稍后再试"),
        BoxTime = NSLOCTEXT("StoreModule", "Lua_Store_BoxTime", " 剩余时间：%s天%s小时"),
        ToggleAll = NSLOCTEXT("StoreModule", "Lua_Store_ToggleAll", "全部"),
        ToggleFilterTitleHero = NSLOCTEXT("StoreModule", "Lua_Store_ToggleFilterTitleHero", "兵种"),
        ToggleFilterTitleWeapon = NSLOCTEXT("StoreModule", "Lua_Store_ToggleFilterTitleWeapon", "枪械"),
        GetItemWindow = NSLOCTEXT("StoreModule", "Lua_Store_GetItemWindow", "获得物品"),
        MustGetTxt = NSLOCTEXT("StoreModule", "Lua_Store_MustGetTxt", "必得物品"),
        RandomGetTxt = NSLOCTEXT("StoreModule", "Lua_Store_RandomGetTxt", "随机物品"),
        RechargeTipsWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowTitle", "充值提示"),
        RechargeTipsWindowConfirm = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowConfirm", "确定"),
        RechargeTipsWindowCancel = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowCancel", "取消"),
        RechargeTipsWindowOtherGear = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowOtherGear", "充值其他档位"),
        RechargeTipsWindowContent = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowContent", "您当前的三角币不足，缺少{lackNum}三角币，推荐您充值{findGear}档位，是否充值？"),
        RechargeTipsWindowGoToChargeView = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowGoToChargeView", "您当前的三角币不足，是否前往充值？"),
        RechargeTipsWindowChargeAndBuy = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTipsWindowChargeAndBuy", "确定充值并购买"),
        DoYouBuyTxt = NSLOCTEXT("StoreModule", "Lua_Store_DoYouBuyTxt", "是否购买%s?"),
        BuyLimit = NSLOCTEXT("StoreModule", "Lua_Store_BuyLimit", "已达限购上限"),
        BuyLimitTen = NSLOCTEXT("StoreModule", "Lua_Store_BuyLimitTen", "当前10连抽抽奖次数不足"),
        ItemLimitTen = NSLOCTEXT("StoreModule", "Lua_Store_ItemLimitTen", "当前物品数量不足10连抽"),
        BoxNoItem = NSLOCTEXT("StoreModule", "Lua_Store_BoxNoItem", "当前宝箱商品已抽完"),
        LimitInfo = NSLOCTEXT("StoreModule", "Lua_Store_LimitInfo", "限购次数"),
        LimitDayText = NSLOCTEXT("StoreModule", "Lua_Store_LimitDayText", "每日限购： %s/%s"),
        LimitWeekText = NSLOCTEXT("StoreModule", "Lua_Store_LimitWeekText", "每周限购： %s/%s"),
        LimitMonthText = NSLOCTEXT("StoreModule", "Lua_Store_LimitMonthText", "每月限购： %s/%s"),
        LimitYearText = NSLOCTEXT("StoreModule", "Lua_Store_LimitYearText", "每年限购： %s/%s"),
        LimitDayTextNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_LimitDayTextNotEnough", "每日限购:<customstyle color=\"Color_DarkNegative\">%s/%s</>"),
        LimitWeekTextNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_LimitWeekTextNotEnough", "每周限购:<customstyle color=\"Color_DarkNegative\">%s/%s</>"),
        LimitMonthTextNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_LimitMonthTextNotEnough", "每月限购:<customstyle color=\"Color_DarkNegative\">%s/%s</>"),
        LimitYearTextNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_LimitYearTextNotEnough", "每年限购:<customstyle color=\"Color_DarkNegative\">%s/%s</>"),
        GunSkinName = NSLOCTEXT("StoreModule", "Lua_Store_GunSkinName", "%s-%s"),
        ExceedMaxStacksNum = NSLOCTEXT("StoreModule", "Lua_Store_ExceedMaxStacksNum", "商品已达到获取上限，请使用后再购买"),
        StoreLocked = NSLOCTEXT("StoreModule", "Lua_Store_StoreLocked", "商店尚未解锁"),
        Prize = NSLOCTEXT("StoreModule", "Lua_Store_Prize", "奖励"),
        CorePrize = NSLOCTEXT("StoreModule", "Lua_Store_CorePrize", "核心奖励"),
        OtherPrize = NSLOCTEXT("StoreModule", "Lua_Store_OtherPrize", "其他奖励"),
        OpenBlindBox = NSLOCTEXT("StoreModule", "Lua_Store_OpenBlindBox", "{CurrencyText}开启{NumOfTimes}次"),
        NumOfBoxesInInventory = NSLOCTEXT("StoreModule", "Lua_Store_NumOfBoxesInInventory", "仓库库存：{NumOfBoxes}"),
        GuaranteedGroupHint = NSLOCTEXT("StoreModule", "Lua_Store_GuaranteedGroupHint", "再开<customstyle color=\"Color_Highlight01\">{remainCount}</>次必然获得<customstyle color=\"Color_Highlight01\">核心奖励</>"),
        GuaranteedPropHint = NSLOCTEXT("StoreModule", "Lua_Store_GuaranteedPropHint", "再开<customstyle color=\"Color_Highlight01\">{remainCount}</>次必然获得<customstyle color=\"Color_Highlight01\">{name}</>"),
        CannotPurchase = NSLOCTEXT("StoreModule", "Lua_Store_CannotPurchase", "无法购买"),
        Owned = NSLOCTEXT("StoreModule", "Lua_Store_Owned", "已拥有"),
        ConfirmBuyBlindBox = NSLOCTEXT("StoreModule", "Lua_Store_ConfirmBuyBlindBox", "是否确认购买%s"),
        Cancel = NSLOCTEXT("StoreModule", "Lua_Store_Cancel", "取消"),
        Purchase = NSLOCTEXT("StoreModule", "Lua_Store_Purchase", "购买"),
        Unbox = NSLOCTEXT("StoreModule", "Lua_Store_Unbox", "概率详览"),
        Exchange = NSLOCTEXT("StoreModule", "Lua_Store_Exchange", "兑换"),
        ExchangeRatio = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeRatio", "兑换比例：<dfmrichtext type=\"img\" id=\"%s\"/>%s=<dfmrichtext type=\"img\" id=\"%s\"/>%s"),
        ExchangeSrcNum = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeSrcNum", "预计花费%s:<dfmrichtext type=\"img\" id=\"%s\"/>%s"),
        ExchangeDstNum = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeDstNum", "预计获取%s:<dfmrichtext type=\"img\" id=\"%s\"/>%s"),
        ExchangeCurrencyWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeCurrencyWindowTitle", "货币兑换"),
        ExchangeCurrencyWindowConfirm = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeCurrencyWindowConfirm", "<dfmrichtext type=\"img\" id=\"%s\"/>%s | 兑换"),
        ExchangeSuc = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeSuc", "兑换成功"),
        ExchangeFail = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeFail", "兑换失败"),
        ExchangeNumZero = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeNumZero", "兑换数量不能为0"),
        BuyConfirmWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_BuyConfirmWindowTitle", "购买确认"),
        BuyConfirmWindowContent = NSLOCTEXT("StoreModule", "Lua_Store_BuyConfirmWindowContent", "确认花费<dfmrichtext type=\"img\" id=\"%s\"/>%s购买%sx%s吗？"),
        BuyConfirmWindowConfirm = NSLOCTEXT("StoreModule", "Lua_Store_BuyConfirmWindowConfirm", "<dfmrichtext type=\"img\" id=\"%s\"/>%s | 购买"),
        BuyConfirmNumZero = NSLOCTEXT("StoreModule", "Lua_Store_BuyConfirmNumZero", "购买数量不能为0"),
        Free = NSLOCTEXT("StoreModule", "Lua_Store_Free", "免费"),
        Got = NSLOCTEXT("StoreModule", "Lua_Store_Got", "已领取"),
        GotFreeItem = NSLOCTEXT("StoreModule", "Lua_Store_GotFreeItem", "今日已领取每日礼包"),
        FreeGetSuc = NSLOCTEXT("StoreModule", "Lua_Store_FreeGetSuc", "领取成功"),
        FreeGetFail = NSLOCTEXT("StoreModule", "Lua_Store_FreeGetFail", "领取失败"),
        BuyLuckyCoinWindowTitle = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowTitle", "购买幸运币"),
        BuyLuckyCoinWindowRatio = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowRatio", "购买一个经验书，将额外赠送一个幸运币"),
        BuyLuckyCoinWindowConfirm = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowConfirm", "<dfmrichtext type=\"img\" id=\"%s\"/> %s| 购买"),
        BuyLuckyCoinWindowConfirmNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowConfirmNotEnough", "<dfmrichtext type=\"img\" id=\"%s\"/><customstyle color=\"Color_DarkNegative\"> %s| 购买</>"),
        BuyLuckyCoinWindowNum = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowNum", "数量:%s"),
        BuyLuckyCoinWindowItemBuy = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowItemBuy", "购买"),
        BuyLuckyCoinWindowItemGift = NSLOCTEXT("StoreModule", "Lua_Store_BuyLuckyCoinWindowItemGift", "赠送"),
        GuaranteedProb = NSLOCTEXT("StoreModule", "Lua_Store_GuaranteedProb", "%s含保底的平均获得概率为%s%%"),
        GuaranteedProbDesc = NSLOCTEXT("StoreModule", "Lua_Store_GuaranteedProbDesc", "核心奖励含多种配色，获得时将随机获得一款"),
        CorePrizeAverageProb = NSLOCTEXT("StoreModule", "Lua_Store_CorePrizeAverageProb", "核心奖励平均概率"),
        CorePrizeCurrentProb = NSLOCTEXT("StoreModule", "Lua_Store_CorePrizeCurrentProb", "核心奖励当前概率"),
        OtherPrizeCurrentProb = NSLOCTEXT("StoreModule", "Lua_Store_OtherPrizeCurrentProb", "其他奖励当前概率"),
        PrizeCurrentProb = NSLOCTEXT("StoreModule", "Lua_Store_PrizeCurrentProb", "奖励当前概率"),
        UnboxingBrick = NSLOCTEXT("StoreModule", "Lua_Store_UnboxingBrick", "开启中，请稍后..."),
        ProbUnit = NSLOCTEXT("StoreModule", "Lua_Store_ProbUnit", "%.2f%%"),

        SafetyBoxEnduranceText = NSLOCTEXT("StoreModule", "Lua_Store_SafetyBoxEnduranceText", "%d天"),
        SafetyBoxActivationText = NSLOCTEXT("StoreModule", "Lua_Store_SafetyBoxActivationText", "当前激活情况"),
        SafetyBoxBuyText = NSLOCTEXT("StoreModule", "Lua_Store_SafetyBoxBuyText", "已购买%s%d天权限"),

        GoToRecharge = NSLOCTEXT("StoreModule", "Lua_Store_GoToRecharge", "充值"),
        RechargeTips = NSLOCTEXT("StoreModule", "Lua_Store_RechargeTips", "您当前的三角币不足，是否前往充值？"),

        -----new -------
        MandelTabReward = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabReward", "奖励"),
        MandelTabHistory = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabHistory", "扫描历史"),
        MandelTabRewardCore = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabRewardCore", "核心奖励"),
        MandelTabRewardOther = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabRewardOther", "其他奖励"),
        MandelTabItemOwnedTip = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabItemOwnedTip", "此标记物品无法重复获取"),
        MandelTabItemCouldRepeatTip = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabItemCouldRepeatTip", "所有物品都可重复获得"),
        
        MandelTabItemRateView = NSLOCTEXT("StoreModule", "Lua_Store_MandelTabItemRateView", "概率总览"),
        MandelDrawPayTips = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawPayTips", "每扫描1次曼德尔砖，需要消耗1个量子密钥"),
        MandelDrawOneTip = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawOneTip", "扫描1次"),
        MandelDrawTenTip = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawTenTip", "扫描10次"),
        RecommendItemJumpTip = NSLOCTEXT("StoreModule", "Lua_Store_RecommendItemJumpTip", "前往"),
        RecommendContainGoodsTip = NSLOCTEXT("StoreModule", "Lua_Store_RecommendContainGoodsTip", "包含%d件物品"),
        RecommendBundleBuySelectOne = NSLOCTEXT("StoreModule", "Lua_Store_RecommendBundleBuySelectOne", "单独购买"),
        RecommendBundleItemPreview = NSLOCTEXT("StoreModule", "Lua_Store_RecommendBundleItemPreview", "配件"),
        BundlePurchaseTipMoneyEnough = NSLOCTEXT("StoreModule", "Lua_Store_BundlePurchaseTipMoneyEnough", "<customstyle color=\"Color_Quality00\">将支付：</> <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s"),
        BundlePurchaseTipMoneyAndBindEnough = NSLOCTEXT("StoreModule", "Lua_Store_BundlePurchaseTipMoneyAndBindEnough", "<customstyle color=\"Color_Quality00\">将支付：</> <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s   <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s"),
        BundlePurchaseTipMoneyNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_BundlePurchaseTipMoneyNotEnough", "<customstyle color=\"Color_Quality00\">将支付：</><dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s   <dfmrichtext type=\"img\" width=\"2\" height=\"55\" id=\"WhiteLine\"/><customstyle color=\"Color_Quality00\">   缺少：</> <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> <customstyle color=\"Color_DarkNegative\">%s </>"),
        BundlePurchaseTipBindMoneyNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_BundlePurchaseTipBindMoneyNotEnough", "<customstyle color=\"Color_Quality00\">将支付：</><dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s  <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s   <dfmrichtext type=\"img\" width=\"2\" height=\"55\" id=\"WhiteLine\"/><customstyle color=\"Color_Quality00\">   缺少：</> <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> <customstyle color=\"Color_DarkNegative\">%s </>"),
        RechargeItemShowDiamond = NSLOCTEXT("StoreModule", "Lua_Store_RechargeItemShowDiamond", "<dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"%s\"/> %s"),
        RechargeItemShowDiamondWithBonus = NSLOCTEXT("StoreModule", "Lua_Store_RechargeItemShowDiamondWithBonus", "<dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"%s\"/> %s <customstyle color=\"Color_Highlight01\">+%s</>"),
        RecommendBundleAllItemBuyButtonTip = NSLOCTEXT("StoreModule", "Lua_Store_RecommendBundleAllItemBuyButtonTip", "已购买"),
        RecommendBundleItemIsPresent = NSLOCTEXT("StoreModule", "Lua_Store_RecommendBundleItemIsPresent", "随礼包赠送"),
        RecommendBundleItemIsPresentTip = NSLOCTEXT("StoreModule", "Lua_Store_RecommendBundleItemIsPresentTip", "整包购买时赠送，或拆分购买获得捆绑包最后一个在售单品时赠送。"),
        RecommendBundleItemGiftTip = NSLOCTEXT("StoreModule", "Lua_Store_RecommendBundleItemGiftTip", "赠"),

        StoreMainTabRecommend = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabRecommend", "热门推荐"),
        StoreMainTabWeaponSkin = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabWeaponSkin", "礼包"),
        StoreMainTabWeaponMarket = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabWeaponMarket", "特供"),
        StoreMainTabWeaponTimeLimitMarket = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabWeaponTimeLimitMarket", "限时返场"),
        StoreMainTabMandel = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabMandel", "量子密钥"),
        StoreMainTabRoleSkin = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabRoleSkin", "干员研究"),
        StoreMainTabChest = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabChest", "研究中心"),
        StoreMainTabRecharge = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabRecharge", "充值"),
        StoreMainTabMarket = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabMarket", "集市"),
        StoreMainTabBHD = NSLOCTEXT("StoreModule", "Lua_Store_StoreMainTabBHD", "黑鹰坠落"),
        StoreWeaponSkinFliterDefault = NSLOCTEXT("StoreModule", "Lua_Store_StoreWeaponSkinFliterDefault", "默认"),
        StoreWeaponSkinFliterQuaility = NSLOCTEXT("StoreModule", "Lua_Store_StoreWeaponSkinFliterQuaility", "稀有度"),

        MandelDrawNoMandelInMarketNow = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawNoMandelInMarketNow", "当前在售曼德尔砖不足，无法购买"),
        MandelDrawMandelInMarketUpdateNow = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawMandelInMarketUpdateNow", "曼德尔砖价格发生变化，购买失败"),
        MandelDrawMandelLotteryItemUpdate = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawMandelLotteryItemUpdate", "购买成功"),
        MandelDrawMandelLotteryItemUpdateWithNotAll = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawMandelLotteryItemUpdateWithNotAll", "库存不足，部分商品购买成功"),
        MandelDrawMandelLotteryItemUpdateWithPriceTip = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawMandelLotteryItemUpdateWithPriceTip", "已为您购买价格更低的曼德尔砖"),

        MandelDrawKeySaleStyle = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawKeySaleStyle", "系统出售"),
        MandelDrawKeySaleStyleFree = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawKeySaleStyleFree", "赠送"),
        MandelDrawMandelSaleStyle = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawMandelSaleStyle", "藏品市场"),
        MandelDrawSaleArmForce = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawSaleArmForce", "军需处"),
        MandelDrawMandelOnlyNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawMandelOnlyNotEnough", "%s数量不足"),

        StoreBuyLimitTimeTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreBuyLimitTimeTip", "%s后重新上架"),
        StoreBuyLimitTimeHourTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreBuyLimitTimeHourTip", "%s时%s分"),
        StoreBuyLimitTimeMinTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreBuyLimitTimeMinTip", "%s分"),
        StoreBuyLimitTimeMinSecTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreBuyLimitTimeMinSecTip", "%s分%s秒"),
        MandelDrawNTip = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawNTip", "扫描%s次"),

        RechargeItemGift = NSLOCTEXT("StoreModule", "Lua_Store_RechargeItemGift", "+%s赠送"),
        StoreVideoSkip = NSLOCTEXT("StoreModule", "Lua_Store_StoreVideoSkip", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/> 跳过"),
        StoreVideoSkipMobile = NSLOCTEXT("StoreModule", "Lua_Store_StoreVideoSkipMobile", "跳过"),

        MandelDrawNoMandelData = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawNoMandelData", "未获得曼德尔砖数据"),
        MandelDrawSweepHistoryNormal = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawSweepHistoryNormal", "执行第%s次扫描，发现%s"),
        MandelDrawSweepHistoryWithColor = NSLOCTEXT("StoreModule", "Lua_Store_MandelDrawSweepHistoryWithColor", "执行第%s次扫描，发现 <customstyle color=\"Color_Quality0%s\">%s</>"),

        MandelActivityTitleCurrSeason = NSLOCTEXT("StoreModule", "Lua_Store_MandelActivityTitleCurrSeason", "当期"),
        MandelActivityTitleBeforeSeason = NSLOCTEXT("StoreModule", "Lua_Store_MandelActivityTitleBeforeSeason", "往期"),

        LogistMandelDrawSweepHistoryNormal = NSLOCTEXT("StoreModule", "Lua_Store_LogistMandelDrawSweepHistoryNormal", "执行第%s次签收，获得%s"),
        LogistMandelDrawSweepHistoryWithColor = NSLOCTEXT("StoreModule", "Lua_Store_LogistMandelDrawSweepHistoryWithColor", "执行第%s次签收，获得 <customstyle color=\"Color_Quality0%s\">%s</>"),

        WeaponSortDefault = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_Default", "全部"),
        WeaponSortRifle = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_Rifle", "突击步枪"),
        WeaponSortSubmachine = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_Submachine", "冲锋枪"),
        WeaponSortShotgun = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_Shotgun", "散弹枪"),
        WeaponSortLightMachine = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_LightMachine", "轻机枪"),
        WeaponSortPrecisionShootingRifle = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_PrecisionShootingRifle", "精确射手步枪"),
        WeaponSortSniper = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_Sniper", "狙击步枪"),
        WeaponSortPistol = NSLOCTEXT("StoreModule", "Lua_Store_Weapon_Sort_Pistol", "手枪"),

        StaffLotteryCountDownDayHour = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryCountDownDayHour", "%s天%s小时"),
        StaffLotteryCountDownHourMin = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryCountDownHourMin", "%s时%s分"),
        StaffLotteryCountDownMinSec = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryCountDownMinSec", "%s分%s秒"),
        StaffLotteryOpenOnce = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryOpenOnce", "开启1次"),
        StaffLotteryAllReward = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryAllReward", "已获得所有奖励"),
        StaffLotteryPopTitle = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPopTitle", "规则说明"),
        StaffLotteryPopListTitle = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPopListTitle", "即时概率"),
        StaffLotteryPreviewRewardTitle = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPreviewRewardTitle", "奖励"),
        StaffLotteryAccessoriesPreviewTitle = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryAccessoriesPreviewTitle", "外观详情"),
        StaffLotteryPopTips = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPopTips", "此类物品不会重复获得"),
        StaffLotteryPurchasePopDesc = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPurchasePopDesc", "每购买1张武器次级经验卡，附赠1张研究资格券"),
        StaffLotteryPurchasePopDescGlobal = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPurchasePopDescGlobal", "每购买1张研究资格券，附赠1张武器次级经验卡"),
        HeroPropMysticalSkinPurchasePopDesc = NSLOCTEXT("StoreModule", "Lua_Store_HeroPropMysticalSkinPurchasePopDesc", "每购买1张普通武器经验券，附赠1张抽奖券"),
        HeroPropMysticalSkinPurchasePopDescGlobal = NSLOCTEXT("StoreModule", "Lua_Store_HeroPropMysticalSkinPurchasePopDescGlobal", "每购买1张抽奖券，附赠1张普通武器经验券"),
        HeroPaintSchemeSavePurchasePopDesc = NSLOCTEXT("StoreModule", "Lua_Store_HeroPaintSchemeSavePurchasePopDesc", "购买将获得"),
        StaffLotteryPopListPercent = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPopListPercent", "%s%%"),
        StaffLotteryPopListOwned = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryPopListOwned", "已拥有"),
        StaffLotteryRewardTipText = NSLOCTEXT("StoreModule", "Lua_Store_StaffLotteryRewardTipText", "奖池"),
        
        ExchangeMandelCoinsSourceTxt = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsSourceTxt", "预计花费"),
        ExchangeMandelCoinsTargetTxt = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsTargetTxt", "预计兑换"),
        ExchangeMandelCoinsSourceCountTxt = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsSourceCountTxt", "<dfmrichtext type=\"img\" width=\"80\" height=\"80\" id=\"diamond\" align=\"0\"/>%s"),
        ExchangeMandelCoinsSourceCountFormatTxt = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsSourceCountFormatTxt", "<dfmrichtext type=\"img\" width=\"80\" height=\"80\" id=\"diamond\" align=\"0\"/>%s"),
        ExchangeMandelCoinsTargetCountTxt = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsTargetCountTxt", "<dfmrichtext type=\"img\" width=\"80\" height=\"80\" id=\"MandelCoins\" align=\"0\"/>%s"),
        ExchangeMandelCoinsTargetCountFormatTxt = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsTargetCountFormatTxt", "<dfmrichtext type=\"img\" width=\"80\" height=\"80\" id=\"MandelCoins\" align=\"0\"/>%s"),
        ExchangeMandelCoinsBtnTextEnough = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsBtnTextEnough", "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"diamond\"/>%d"),
        ExchangeMandelCoinsBtnTextNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_ExchangeMandelCoinsBtnTextNotEnough", "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"diamond\"/><customstyle color=\"Color_DarkNegative\">%d</>"),

        ItemPriceNormalHighColor = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceNormalHighColor", "<dfmrichtext type=\"img\" id=\"%s\"/><customstyle color=\"C000\">%s</>"),
        ItemPriceNormalHighColorNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_ItemPriceNormalHighColorNotEnough", "<dfmrichtext type=\"img\" id=\"%s\"/><customstyle color=\"Color_DarkNegative\">%s</>"),
        StoreMandelPaddedGoodItemNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_StoreMandelPaddedGoodItemNotEnough", "不足"),
        MandelPaddedGoodsPayTxtEnough = NSLOCTEXT("StoreModule", "Lua_Store_MandelPaddedGoodsPayTxt", "将支付：%s"),
        MandelPaddedGoodsPayTxtNotEnough = NSLOCTEXT("StoreModule", "Lua_Store_MandelPaddedGoodsPayTxtNotEnough", "将支付： %s   <dfmrichtext type=\"img\" width=\"2\" height=\"55\" id=\"WhiteLine\"/>   缺少： %s"),
        MandelHistoryNoAnyThing = NSLOCTEXT("StoreModule", "Lua_Store_MandelHistoryNoAnyThing", "暂无扫描历史"),
        
        BuyKeysDesc = NSLOCTEXT("StoreModule", "Lua_Store_BuyKeysDesc", "每购买1个%s，附赠1个%s"),
        OwnedCount = NSLOCTEXT("StoreModule", "Lua_Store_OwnedCount", "拥有%s"),

        BHDBuySuccessfule = NSLOCTEXT("StoreModule", "Lua_Store_BHDBuySuccessfule", "%s购买成功"),
    
        NothingContent = NSLOCTEXT("StoreModule", "Lua_Store_NothingContent", "暂无内容"),
        PrizeOverviewName = NSLOCTEXT("StoreModule", "Lua_Store_PrizeOverviewName", "奖励预览"),
        
        ActivityBrickGetTitle = NSLOCTEXT("StoreModule", "Lua_Store_ActivityBrickGetTitle", "获取途径"),
        ActivityBrickGetDesc = NSLOCTEXT("StoreModule", "Lua_Store_ActivityBrickGetDesc", "参加活动可获得图灵砖"),

        ActivityBrickGetMethod1 = NSLOCTEXT("StoreModule", "Lua_Store_ActivityBrickGetMethod1", "军需处<customstyle color=\"Color_Highlight02\">军需处</>购买"),
        ActivityBrickGetMethod2 = NSLOCTEXT("StoreModule", "Lua_Store_ActivityBrickGetMethod2", "<customstyle color=\"Color_Highlight02\">全面战场</>模式掉落"),
        
        OrangeMyGradeVoS = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyGradeVoS", "图形无暇，数据精确。"),
        OrangeMyGradeVoA = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyGradeVoA", "图形完好，数据准确。"),
        OrangeMyGradeVoB = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyGradeVoB", "图形可用，数据已找回。"),
        OrangeMyGradeVoC = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyGradeVoC", "图形已修复，数据部分缺失。"),

        OrangeMyRare = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyRare", "发现隐藏文件，已解码。"),
        
        OrangeMyWeaponBlitz = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyWeaponBlitz", "正在解构并重组内部零件，特殊材料已启用。"),
        OrangeMyWeaponMedusa = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyWeaponMedusa", "正在解析矿晶成分，警告，美杜莎之眼凿刻完成。"),
        OrangeMyWeaponGame = NSLOCTEXT("StoreModule", "Lua_Store_OrangeMyWeaponGame", "正在优化按键手感，小游戏安装包已下载。"),


        TimeLimitMarketRefreshTime = NSLOCTEXT("StoreModule", "Lua_Store_TimeLimitMarketRefreshTime", "刷新时间：%s"),
        TimeLimitMarketEndTime = NSLOCTEXT("StoreModule", "Lua_Store_TimeLimitMarketEndTime", "%s活动结束"),

        StoreMandelUpRewardChooseTips = NSLOCTEXT("StoreModule", "Lua_Store_StoreMandelUpRewardChooseTips", "在进行曼德尔砖解析时，可以在本期奖励中指定1个核心奖励。当解析获得核心奖励时，必定为所指定的核心奖励。\n指定核心奖励后，可随时更换目标，已解析次数不会重置，保底将会继承。"),

        MandelUpRewardChooseBtnAlready = NSLOCTEXT("StoreModule", "Lua_Store_MandelUpRewardChooseBtnAlready", "已选择"),
        MandelUpRewardChooseBtn = NSLOCTEXT("StoreModule", "Lua_Store_MandelUpRewardChooseBtn", "选择"),
        MandelUpRewardChooseBtnClickTips = NSLOCTEXT("StoreModule", "Lua_Store_MandelUpRewardChooseBtnClickTips", "扫描次数和保底会被继承，是否选择<customstyle color=\"Color_Highlight01\">%s</>作为解析指定的核心奖励？"),

        ActivityMandelBuyBrickSeasonLvLimit = NSLOCTEXT("StoreModule", "Lua_Store_ActivityMandelBuyBrickSeasonLvLimit", "行动等级%d级解锁购买"),
        ActivityMandelBuyBrickSeasonLvLimitShopItem = NSLOCTEXT("StoreModule", "Lua_Store_ActivityMandelBuyBrickSeasonLvLimitShopItem", "<customstyle color=\"Color_DarkNegative\">%d级解锁购买</>"),

        MandelUpRewardAlreadyChoose = NSLOCTEXT("StoreModule", "Lua_Store_MandelUpRewardAlreadyChoose", "已指定核心奖励"),
        MandelUpRewardProbUp = NSLOCTEXT("StoreModule", "Lua_Store_MandelUpRewardProbUp", "概率提升"),
        MandelUpRewardSelectTitle = NSLOCTEXT("StoreModule", "Lua_Store_MandelUpRewardSelectTitle", "解析指定"),

        -- 消费券信息
        TimeLimitCouponInfo = NSLOCTEXT("StoreModule", "Lua_Store_TimeLimitCouponInfo", "<customstyle color=\"Color_Quality00\">优先抵扣消费券，当前拥有</> <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"ConsumerCoupon\" align=\"0\"/> %s，有效期%s"),
        -- 消费券、三角券、三角币
	    TimeLimitdeductInfo1 = NSLOCTEXT("StoreModule", "Lua_Store_TimeLimitdeductInfo1", "<customstyle color=\"Color_Quality00\">成功使用消费券进行抵扣，实际支付</><dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s"),
	    TimeLimitdeductInfo2 = NSLOCTEXT("StoreModule", "Lua_Store_TimeLimitdeductInfo2", "<customstyle color=\"Color_Quality00\">成功使用消费券进行抵扣，实际支付</><dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s  <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s"),
        TimeLimitdeductInfo3 = NSLOCTEXT("StoreModule", "Lua_Store_TimeLimitdeductInfo3", "<customstyle color=\"Color_Quality00\">成功使用消费券进行抵扣，实际支付</><dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s  <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s  <dfmrichtext type=\"img\" width=\"55\" height=\"55\" id=\"%s\" align=\"0\"/> %s"),

        PaymentFlexibilityLimitAndTitle = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitAndTitle", "由于您的账号存在风险行为，系统已暂时限制进行三角币充值和消费，完成下列任务，可解除限制(*单局时长需≥10分钟):"),
        PaymentFlexibilityLimitOrTitle = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitOrTitle", "由于您的账号存在风险行为，系统已暂时限制进行三角币充值和消费，完成下列任一任务，可解除限制(*单局时长需≥10分钟):"),
        PaymentFlexibilityLimitGotoBtnText = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitGotoBtnText", "前往"),
        PaymentFlexibilityLimitSOLTaskDoing = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitSOLTask", "(%d/%d) 在<customstyle color=\"Color_Highlight02\">烽火地带</>玩法中，完成3次【成功撤离】的对局"),
        PaymentFlexibilityLimitMPTaskDoing = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitMPTask", "(%d/%d) 在<customstyle color=\"Color_Highlight02\">全面战场</>玩法中，完成3次【获胜】的对局"),
        PaymentFlexibilityLimitSOLTaskDone = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitSOLTaskDone", "<customstyle color=\"C002\">(%d/%d) 在烽火地带玩法中，完成%d次【成功撤离】的对局</>"),
        PaymentFlexibilityLimitMPTaskDone = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitMPTaskDone", "<customstyle color=\"C002\">(%d/%d) 在全面战场玩法中，完成%d次【获胜】的对局</>"),
        PaymentFlexibilityLimitRestore = NSLOCTEXT("StoreModule", "Lua_Store_PaymentFlexibilityLimitRestore", "消费限制已解除，可前往充值/消费三角币"),

        ContinuousRewardTitle = NSLOCTEXT("StoreModule", "Lua_Store_ContinuousRewardTitle", "累计扫描奖励"),
        ContinuousRewardDesc = NSLOCTEXT("StoreModule", "Lua_Store_ContinuousRewardDesc", "累计获得核心奖励次数，领取阶段奖励"),
        ContinuousRewardTips = NSLOCTEXT("StoreModule", "Lua_Store_ContinuousRewardTips", "1.获得曼德尔砖核心奖励达到累计次数后，可领取对应的次数奖励(每个曼德尔砖单独累计)\n2.单个曼德尔砖的累计奖励仅可领取一次"),
        ContinuousRewardPanelTitle = NSLOCTEXT("StoreModule", "Lua_Store_ContinuousRewardPanelTitle", "获得奖励"),

        CollaborationBundleTab = NSLOCTEXT("StoreModule", "Lua_Store_CollaborationBundleTab", "联动礼包"),
        AlwaysOnline = NSLOCTEXT("StoreModule", "Lua_Store_AlwaysOnline", "长期在线"),
        LotteryTime = NSLOCTEXT("StoreModule", "Lua_Store_LotteryTime", "<customstyle color=\"Color_Highlight01\">{startTime}-{endTime}</>"),-- 2025年7月11日-2025年8月24日

        NightMarketSearch = NSLOCTEXT("StoreModule", "Lua_Store_NightMarketSearch", "掏鸟窝"),
        NightMarketFreeReward = NSLOCTEXT("StoreModule", "Lua_Store_NightMarketFreeReward", "超级幸运礼"),
        NightMarketFreeRewardDesc = NSLOCTEXT("StoreModule", "Lua_Store_NightMarketFreeRewardDesc", "万里挑一的幸运儿就是你，精选好物0元购"),
        FreeToGet = NSLOCTEXT("StoreModule", "Lua_Store_FreeToGet", "免费领取"),

        NightMarketMianTabTip = NSLOCTEXT("StoreModule", "Lua_Store_NightMarketMianTabTip", "搜索鸟窝，好礼特惠不停！"),
        StoreNMBuyLimitTimeHourTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreNMBuyLimitTimeHourTip", "%s时%s分后刷新"),
        StoreNMBuyLimitTimeDayTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreNMBuyLimitTimeDayTip", "%s月%s日活动结束"),

        StoreAppNotReadyTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreAppNotReadyTip", "app未准备好"),
        StoreAppHasOpenedTip = NSLOCTEXT("StoreModule", "Lua_Store_StoreAppHasOpenedTip", "app已打开"),
    },

    TabImgPathList  = {
        "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_03.Store_Icon_03'",
        "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_04.Store_Icon_04'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0001.CommonHall_Market_Icon_0001'",
        "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_14.Store_Icon_14'",
        -- "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0004.CommonHall_Market_Icon_0004'",
    },

    TabImgPathListFor2  = {
        "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_03.Store_Icon_03'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'",
    },

    MandelActivityTabImgPathList  = {
        "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_13.Store_Icon_13'",
        "PaperSprite'/Game/UI/UIAtlas/System/Store/BakedSprite/Store_Icon_12.Store_Icon_12'",
    },

    SortConfig = {
        [EStoreSortType.Default] = NSLOCTEXT("StoreModule", "Lua_Store_Sort_Default", "默认"),
        [EStoreSortType.Time] = NSLOCTEXT("StoreModule", "Lua_Store_Sort_Time", "发布时间"),
        [EStoreSortType.Alphabet] = NSLOCTEXT("StoreModule", "Lua_Store_Sort_Alphabet", "名称"),
        [EStoreSortType.Price] = NSLOCTEXT("StoreModule", "Lua_Store_Sort_Price", "价格"),
    },

    OrangeGradeVOConfig = {
        ["S"] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_S_1.Voice_Gacha_Weapon_S_1",
        ["A"] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_A_1.Voice_Gacha_Weapon_A_1",
        ["B"] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_B_1.Voice_Gacha_Weapon_B_1",
        ["C"] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_C_1.Voice_Gacha_Weapon_C_1",
    },

    OrangeRareSpecialVOConfig = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_Rare_1.Voice_Gacha_Weapon_Rare_1",

    OrangeWeaponVOConfig = {
        [28010050272] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_Game_1.Voice_Gacha_Weapon_Game_1",
        [28020050001] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_Medusa_1.Voice_Gacha_Weapon_Medusa_1",
        [28010050271] = "/Game/WwiseAudio/GPEvents/Voice/Outgame/Gacha_MandelBrick/Voice_Gacha_Weapon_Blitz_1.Voice_Gacha_Weapon_Blitz_1",
    },
}

--干员抽奖需要抽到大奖的次数
StoreConfig.RoleSkinTime = 24

---@enum EStorePrizesPopShowType
StoreConfig.EStorePrizesPopShowType = {
    EStorePrizesPopShowType_OnlyPrize = 1,
    EStorePrizesPopShowType_OnlyHistory = 2,
    EStorePrizesPopShowType_All = 3
}

return StoreConfig
