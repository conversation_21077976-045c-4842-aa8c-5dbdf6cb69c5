local ULuaSubsystem = import "LuaSubsystem"
local EntranceFlow = {}
--------------------------------------------------------------------------
--- 拆分这个入口流程是为了后续方便整个文件load到指定env
--------------------------------------------------------------------------
local log = function(...)
    print('[FlowLog - EntranceFlow]  ',...)
end
--------------------------------------------------------------------------
--- 主流程
--------------------------------------------------------------------------
local bIsPlayBGMOnStartUp = true
function EntranceFlow.LuaWarmupEntrance()
    log("LuaWarmupEntrance")

    -- 在最早的这个时机初始化UILayers
    if not Facade.UIManager:IsFrameRootAvailable() then
        Facade.UIManager:InitUILayers()
    end

    EntranceFlow.GCloudEntrance()

    -- PC等到着色器阶段再开始播，这里就不提前了
    if not IsHD() and bIsPlayBGMOnStartUp then
        -- Lua启动就直接播BGM了
        Facade.SoundManager:PlayLoginStartBGM()
    end
end


function EntranceFlow.HotFixEntrance()
    log("GFHotfix_InitHotfixBusiness")
    --trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "HotUpdate")
end

function EntranceFlow.GCloudEntrance()
    log("GFHotfix_InitGCloudBusiness")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "log LogSdk Display", nil)
    if not Facade.ModuleManager:IsModuleValid("CommonUILibrary") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonUILibrary", false, true)
    end
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "SDKInfo")
    if not Facade.ModuleManager:IsModuleValid("GCloudSDK") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "GCloudSDK") -- 依赖CommonUILibrary
    end
end

-- 启动流程框架支持
local DFGameLaunchManager = import("DFGameLaunchManager").GetGameLaunchManager(GetGameInstance())
local ELaunchStepResult = import "ELaunchStepResult"

--- 设备校验
function EntranceFlow.DeviceCheckEntrance()
    log("GFDeviceCheck_CheckDevice")

    -- 初始化GCloudSDKModule
    EntranceFlow.GCloudEntrance()

    if ULuaExtension.GetLuaErrTipsFlag() or VersionUtil.IsPreRelease() then
        local isLuaDebug=ULuaExtension.IsLuaDebug()
        local LuaDebugPath=ULuaExtension.GetLuaDebugPath()
        local txt=""
        if isLuaDebug then
            local noticeText = "你正在使用LuaDebug！"..LuaDebugPath
            local ConfirmQuitGame = "我知道了，继续"
            Module.GCloudSDK:ShowCommonTip(noticeText, ConfirmQuitGame, nil, true, function()
            end, nil, true) -- 点击确认后不关闭当前窗口，避免IOS异常
        end
    end
    EntranceFlow.DoDeviceCheck() -- 依赖GCloudSDKModule
end

function EntranceFlow.DoDeviceCheck()
    trycall(require, "DFM.YxFramework.Util.ApmUtil")
    local UDFMGameplayGlobalDelegates = import("DFMGameplayGlobalDelegates").Get(GetGameInstance()) -- 暂时保留，主流程验证后移除

    if not VersionUtil.IsVersionUsable() then
        local gameVersion = VersionUtil.GetVersionFull()
        -- 当前版本不可用，需要弹窗拦截
        local DeviceLimitText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_AppVersionLimitText", "当前版本已停止维护，请前往应用商店下载最新版本")
        local ConfirmQuitGame = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGame", "确定")
        Module.GCloudSDK:ShowCommonTip(DeviceLimitText, ConfirmQuitGame, nil, true, function()
            logerror("[aidenliao] Cur Version not available, set from maple!")

            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end, nil, false)

        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("DeviceCheck", ELaunchStepResult.ELSR_Failed, "App Version Limit " .. gameVersion)
        end

        return
    end

    if not ApmUtil.IsDeviceCanLogin() then
        -- 由接口内部开关控制，热更后开启，判断当前设备不满足条件的时候直接拦截踢掉
        local DeviceLimitText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceLimitText", "您当前的机型不在此版本的适配范围内，后续会开放更多机型。\n《三角洲行动》支持多端互通，您可以尝试使用其他平台进行游戏，详细信息请前往官网（df.qq.com）获取。")
        local ConfirmQuitGame = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGame", "确定")
        Module.GCloudSDK:ShowCommonTip(DeviceLimitText, ConfirmQuitGame, nil, true, function()
            log("[aidenliao]当前设备不满足运行要求，强制退出。")

            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end, nil, false) -- 点击确认后不关闭当前窗口，避免IOS异常

        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("DeviceCheck", ELaunchStepResult.ELSR_Failed, "Device Check Limit")
        end

        return
    end

    -- 设备系统驱动版本过低，需要升级
    if ApmUtil.DoesDeviceNeedToUpgrade() then
        local TipCount = Facade.ConfigManager:GetUserInt("TipCount",0)
        if TipCount < 3 then --只提示三次
            Facade.ConfigManager:SetUserInt("TipCount", TipCount + 1)
            local DeviceLimitText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNeedUpdate", "亲爱的干员，您的手机驱动版本过低，会影响游戏画面质量和稳定性，建议您升级手机系统到最新版本。")
            local ConfirmQuitGame = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGame", "确定")
            Module.GCloudSDK:ShowCommonTip(DeviceLimitText, ConfirmQuitGame, nil, true, function()
                if DFGameLaunchManager then
                    DFGameLaunchManager:FinishStep("DeviceCheck", ELaunchStepResult.ELSR_Success, "")
                end
            end, nil, true) -- 点击确认后不关闭当前窗口，避免IOS异常
            return
        end
    end

    if DFGameLaunchManager then
        DFGameLaunchManager:FinishStep("DeviceCheck", ELaunchStepResult.ELSR_Success, "")
    end

    if UDFMGameplayGlobalDelegates then
        UDFMGameplayGlobalDelegates:OnDeviceCheckEnd(true)
    end
end

function EntranceFlow.InstallPathCheckEntrance()
    log("GFInstallPathCheck_CheckPath")
    local UDFMGameplayGlobalDelegates = import("DFMGameplayGlobalDelegates").Get(GetGameInstance())
    -- 仅PC进行中文路径检查  手游直接跳过
    -- if IsHD() then
    --     local UGameSDKManager = import "GameSDKManager"
    --     local gameSDKManager = UGameSDKManager.Get(GetGameInstance())

    --     if gameSDKManager ~= nil then
    --         if not gameSDKManager.IsValidInstallPath() then
    --             local InstallPathLimitText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_InstallPathLimitText", "很抱歉，安装路径请使用英文，感谢配合")
    --             local ConfirmQuitGame = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGame", "确定")
    --             Module.CommonTips:ShowConfirmBasicWindow(
    --                 InstallPathLimitText,
    --                 function()
    --                 log("[viktorhe]检测到非英文安装路径，强制退出。")
    --                 local EQuitPreference = import "EQuitPreference"
    --                 UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
    --                 end,
    --                 nil, nil, ConfirmQuitGame, true)

    --             if DFGameLaunchManager then
    --                 DFGameLaunchManager:FinishStep("InstallPathCheck", ELaunchStepResult.ELSR_Failed, "Find Invalid Install Path")
    --             end

    --             return
    --         end
    --     else
    --         logerror("[viktorhe] InstallPathCheckEntrance gameSDKManager is nil")
    --     end
    -- end

    if DFGameLaunchManager then
        DFGameLaunchManager:FinishStep("InstallPathCheck", ELaunchStepResult.ELSR_Success, "")
    end

    if UDFMGameplayGlobalDelegates then
        UDFMGameplayGlobalDelegates:OnInstallPathCheckEnd(true)
    end
end

function EntranceFlow.VersionUpdateEntrance()
    log("GFVersionUpdate_InitVersionUpdateBusiness")
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "log LogSdkDolphin Display", nil)

    --trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "GCloudSDK")
    --trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonTips")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "VersionUpdate")
end

function EntranceFlow.PufferUpdateEntrance()
    log("GFVersionUpdate_PufferUpdateBusiness")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Permission", false)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LitePackage", false, true)

    -- if CloseModuleType.bIsEnablePSOCompileOnDownloading and (not IsHD()) then
    --     -- 手游才会在这里触发
    --     loginfo("Enable Precompile PSO On Puffer Downloading!")
    --     Module.GCloudSDK:PreCompileAllPSOOnDownloading() -- 触发PSO预编译
    -- end
end

function EntranceFlow.ApplyShippingLogRule()
    local GameInstance = GetGameInstance()
    -- global
    -- 切换两次保证生效
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log global log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log global error", nil)
    -- FATAL
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogTemp Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCheckComponents Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log GameCore Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCheckSubobjects Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogAbilitySystem Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRuntimeIconMaker Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogStreaming Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMovieScene Fatal", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMHUD Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogDeadBody Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogDFMAbility Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogUObjectBase Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogStreamableManager Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMaterial Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSkeletalMesh Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogProperty Fatal", nil) -- 日志超量
    -- WARNING
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log HotUpdate Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMaple Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGarbage Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCDNInfoMgr Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGameLoading Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogDFGameLaunch Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCharacterMovement Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRuntimeSkeletalMesh Warning", nil)
    --UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogHeavyTaskMonitor Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponSystem Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponZoom Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponPAnim Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponDecal Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponCrosshair Error", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponStable Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFile Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogOpenWorldStreaming Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeGame Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogACE Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogDFMGameTss Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogPSOCacheSys Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPVehicle Error", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log DFLogDFMGameHud Error", nil)
    -- DISPLAY
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSdk Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMinimap Warning", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogBlueprintPlunder Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSeamlessTravel Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogReconnect Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSeamlessEnter Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogStartSpotAlloc Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRemapNetGUID Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPActorChannel Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSeamlessObj Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log GPSequence Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPCharacter Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGpCharacterMovement Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRedeploy Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRedeployPoint Fatal", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRedeployHudLogic Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogRedeployView Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogPlayerExit Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogAirDropVehicle Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogStatistics Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPQuest Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogQuestClientProxy Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMarkerManager Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMarkerItem Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSubtitleSubsystem Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSeamlessEnterUI Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCharacterAppearance Warning", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGameVersionUtils Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSdkGVoice Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMGVoice Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log GameCoreDataTable Display", nil) -- 关键日志，先不要修改，等bug定位到在关
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogReplay Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPReplay Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log DFLogDFMVehicle Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCharacterFSM Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPHighlightMoment Display", nil)
    if IsHD() then
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPInput Display", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGamepadInput Display", nil)
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPInput Warning", nil)
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogInput Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPAudio Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogAkAudio Display", nil)
    -- LOG
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMGuide Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSGuide Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMLitePackage Warning", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMGunsmith Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMSettlement Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSSettlement Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMVehicle Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMArmedForce Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSArmedForce Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSInventory Warning", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMReddotTrie Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogLoad Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogInit Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogFSR2 Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogFSR3 Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMemory Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWindows Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log InputHelper Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogPerfGear Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogD3D11RHI Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogD3D12RHI Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMetaperf Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogETWCapture Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPGameFlow Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogETWCapture Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogD3D11Bridge Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogD3D12Bridge Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogPerfMonitor Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogHardwareParamHelper Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogStreamlineD3D12RHIPreInit Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogDataTableHotFix Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogInteractSystem Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogLoadAniOnCommand Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogFortificationSystem Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogSkeletalMeshMergeRuntime Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogDisplayInputAction Display", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGPAttribute Display", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogItem Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogBuffSystem Display", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponSkin Display", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponOb Error", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponManager Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponProjectileReplicate Error", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogMLobbyDisplay Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogHall Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMLogin Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMReconcile Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMPay Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSPay Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMGCloudSDK Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMReward Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogKeySetting Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogCrashSightWin Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogGamelet Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponThermalImaging Log", nil) -- 用来定位外网偶现问题
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LogWeaponAdapterScopeSceneCapture Log", nil) -- 用来定位外网偶现问题


    --- UIManager 精细化管理
    --- UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFManager Warning", nil) -- 日志超量
    --- UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManager Warning", nil) -- 日志超量

    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFResourceManagerStub Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFResourceManager Warning", nil)

    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerRule Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerTheme Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerNav Log", nil)

    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerStack Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerPop Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerSub Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerHUD Warning", nil)

    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerLayer Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerLink Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerAliveCount Warning", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerRes Warning", nil)

    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerAnim Error", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerIns Error", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFUIManagerInput Error", nil)

    --- GameFlowManager 精细化管理
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFGameFlowManager Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFGameFlowManagerBHD Log", nil)


    --- BHD相关紧急开启
    -- 关键日志，先不要修改，等bug定位到在关
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaFBHDController Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMLobbyBHD Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaMReconnect Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSBHD Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSMatch Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSTeam Log", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log LuaSAccount Warning", nil) -- 日志超量
    UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log DFLogDFMGameSDK Log", nil)

    LogUtil.updateLogCategoryCurLevel()
end

function EntranceFlow.PreloadEntrance()
    log("GFLogin_InitPreloadBusiness")

    require("DFM.Business.Misc.DFMGlobalEvents")
    require("DFM.Business.Misc.DFMAudioConst")
    require("DFM.Business.Misc.DFMGlobalConst")
    require("DFM.Business.Misc.DFMGlobalFunction")
    require("DFM.Business.Misc.DFMGlobalModuleConfig")
    require("DFM.Business.ServerCenter.ServerConfig.ServerConfig")
    require("DFM.Business.ServerCenter.ServerConfig.ServerTipCode")
    require("DFM.StandaloneLua.BusinessTool.PSOAutomation") --PSO自动化相关

    local ProtoMappingTool = require("DFM.Business.Proto.ProtoMappingTool")
    ProtoMappingTool.InitProtoMapping()
    if not Facade.ModuleManager:IsModuleValid("CommonWidget") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonWidget", false, true)
    end
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Preload")

    if not Facade.ModuleManager:IsModuleValid("ImGuiPanel") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ImGuiPanel", false, true)
    end

    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Game.ResetClientWorldTimeInSeamless 0", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.AnimDataSafeThread 1", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Localize.EnableNativeTextUpdate 0", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Net.UERepGraph.DisableCheckGameThread 1", nil)
    
    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "AllowAsyncRenderThreadUpdates 0", nil)
    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.LuaCacheNonRefStruct 0", nil)

    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Swim.SwimPutWeapon 0", nil)
    if UE_BUILD_SHIPPING then
        if PLATFORM_ANDROID or PLATFORM_IOS or PLATFORM_OPENHARMONY then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Perf.SuperSceneRecordTime 1800", nil)
        end
    end


    local UGPPerfManager = import "GPPerfManager"
    local GameInstance = GetGameInstance()
    local PerfManager = UGPPerfManager.Get(GameInstance)
    if PerfManager then
        PerfManager:InitDevicePerfManagers()
        log("[viper] InitDevicePerfManagers!!!!!!")
    else
        logerror("[viper] perfmanager = nil")
    end

    --关闭一些shipping开关
    if VersionUtil.IsShipping() then
        local UGameVersionUtils = import "GameVersionUtils"
        local useAllLog = UGameVersionUtils.GetLauncherParamsByKey("allLog")
        if useAllLog == "" or useAllLog == "no" then
            --关闭log
            EntranceFlow.ApplyShippingLogRule()
            log("shipping ApplyShippingLogRule")
        else
            UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log global error", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "log global log", nil)
            log("shipping not ApplyShippingLogRule")
        end

        -- UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.KeepDataTableRefObjectsEnable 0", nil)
        --关闭gm
        CloseModuleType.bIsCloseGM = true
        --关闭lua报错
        --预发布服开启lua报错
        if not UGameVersionUtils.IsPreRelease() then
            CloseModuleType.bIsCloseLuaErrTips = true
        end
        --关闭底部调试信息
        CloseModuleType.bIsCloseDebug = true
        --关闭登陆开发者按钮
        CloseModuleType.bIsCloseLoginInterface = true
    else
        local bApplyShippingLogRule = ULuaSubsystem.GetIsLuaLogApplyShippingRule()
        if bApplyShippingLogRule then
            log("not shipping ApplyShippingLogRule")
            EntranceFlow.ApplyShippingLogRule()
        end
    end
    ULuaExtension.SetLuaErrTipsFlag(not CloseModuleType.bIsCloseLuaErrTips)

    -- APM Strategy Switch
    --具体参数在PerfSightBridge中enum StrategyId
    --关闭kProcessInfoThreadCount线程数获取, 因为枚举线程和ACE冲突导致卡顿
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.Strategy 11 0", nil)
    --关闭kGpuTemperature温度获取, 因为获取方式d3dkmt接口获取, 会和D3D12Core接口冲突导致卡顿等待
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.Strategy 12 0", nil)

    --Metaperf硬件监控开关
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.Enable True", nil)
    -- 硬件数据刷新频率
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.MonitorInterval 6", nil)
    -- 监控GPU, 并上报APM
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.MonitorGPU True", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.MonitorUploadToAPM True", nil)
    -- 监控CPU
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.MonitorCPU True", nil)
    -- 监控频率
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.MonitorFreq True", nil)

    --Metaperf采集开关
    --XPERF UPLOAD LIMIT > 100ms <= 100MB
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.UploadTimeLimit 100", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.UploadTimeLimit 100", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.UploadSizeLimit 150", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.UploadSizeLimit 150", nil)
    --XPERF BUFFER SIZE
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.BufferCount 256", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.BufferCount 256", nil)
    --XPERF CAPTURE MODE: 0: Continuous 1: Jank 2: SamllJank 3: BigJank
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.CaptureMode 1", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.CaptureMode 1", nil)
    --XPERF OFFCPU
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.TraceOffCpu True", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.TraceOffCpu True", nil)
    --ETL UPLOAD
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.UploadEnable True", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.UploadEnable True", nil)
    --0: XPerf Capturer, 1: APM Capturer 2: All Disable But CloudControl 3: All Disable Can Not Use Cloud Control
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "MetaPerf.SwitchCapturer 2", nil)

    if VersionUtil.IsShipping() then
        --Shipping下需要开/关的Cvar
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "ModularWeapon.MergeFallbackDynamic 1", nil)
    end

    -- TaskGraph trigger
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "TaskGraph.SwitchEvent 0", nil)

    if IsHD() then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "PlayerController.DisablePlayerTickIfNoInputComp 0", nil)
    end

    if not IsHD() then
        if not IsBuildRegionCN() then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pc.PlayerUploadW2SFrequency 9999999", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pc.PlayerSendW2SFrequency 9999999", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pc.PlayerUploadW2SFrequencyMP 9999999", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pc.PlayerSendW2SFrequencyMP 9999999", nil)
        else
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pc.PlayerUploadW2SFrequencyMP 9999999", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pc.PlayerSendW2SFrequencyMP 9999999", nil)
        end
    end

    -- floydchen 预算管理开关,针对中配CPU开启Thread <= 12
    if IsHD() and not IsConsole() then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.NetWorkUseGlobalBudget 1", nil)
        
        local UVideoSettingHelper = import("VideoSettingHelper")
        local CPUSystemInfoHD = UVideoSettingHelper.GetSystemInfoHD()
        if CPUSystemInfoHD ~= nil and CPUSystemInfoHD.CPUThreads ~= nil and type(CPUSystemInfoHD.CPUThreads) == "number" and CPUSystemInfoHD.CPUThreads <= 6 then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.StaticTickUseGlobalBudget 1", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.StreamingUseGlobalBudget 1", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.AsyncStreamableUseGlobalBudget 1", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.HeavyTaskUseGlobalBudget 1", nil)
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.RTIUseGlobalBudget 1", nil)
        end
    end

    -- samuelxdeng 主机平台的预算管理开关
    if IsConsole() then -- 关闭所有的预算开关
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.StaticTickUseGlobalBudget 0", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.StreamingUseGlobalBudget 0", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.AsyncStreamableUseGlobalBudget 0", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.HeavyTaskUseGlobalBudget 0", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.RTIUseGlobalBudget 0", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Budget.NetWorkUseGlobalBudget 1", nil) -- 保持NetWorkBudget
        -- 主机常驻，用于消除手柄输入延迟
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.GTSyncType 2", nil) 
    end

    -- 海外默认开启多语言字体
    if not IsBuildRegionCN() then
        log("EntranceFlow.PreloadEntrance r.Font.EnableCultureFontMap 1")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Font.EnableCultureFontMap 1", nil)
    end
end

function EntranceFlow.InitCommonUIModules()
    if not Facade.UIManager:IsFrameRootAvailable() then
        Facade.UIManager:InitUILayers()
    end

    if not Facade.ModuleManager:IsModuleValid("CommonUILibrary") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonUILibrary", false, true)
    end

    if not Facade.ModuleManager:IsModuleValid("CommonWidget") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonWidget", false, true)
    end

    if not Facade.ModuleManager:IsModuleValid("CommonTips") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonTips", false, true)
    end

    if not Facade.ModuleManager:IsModuleValid("CommonBar") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonBar", nil, true)
    end
end

function EntranceFlow.CheckSystemVersionHD()
    log("CheckSystemVersionHD")

    if _WITH_EDITOR == 1 then
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckSystemVersion", ELaunchStepResult.ELSR_Success, "Editor Skip")
        end
        return
    end

    local ULuaExtension = import("LuaExtension")
    if ULuaExtension.Ext_FileExists("C:/DFMTemp/479239F6766A9DE42696B6EDB3EC39D44AF16B18873248D3F28B687F4DACF556.txt") then
        log("通过后门跳过操作系统版本检查")
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckSystemVersion", ELaunchStepResult.ELSR_Success, "SecretDoor")
        end
        return
    end

    local bCheck = false

    local UVideoSettingHelper = import("VideoSettingHelper")
    if bCheck and not UVideoSettingHelper.IsWindowsVerOrGreater(10,0,0) then
        local function _QuitGame()
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end

        local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowSystemVersionWarningHD", "检测到当前操作系统版本过低，请升级后重试。")
        noticeText = string.format(noticeText, minRam)
        local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGameHD", "退出游戏")

        local param = {
            title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示"),
            content = noticeText,
            btnStyle = 1,
            hideCloseBtn = true,
            hideEscBtn = true,
            backgroudUnableClick = true,
            btnText = {
                confirmText,
            },
            btnClick = {
                _QuitGame,
            }
        }
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckSystemVersion", ELaunchStepResult.ELSR_Failed, "Old System Version") -- 主流程失败上报后将会阻塞，玩家点击确定直接退出游戏
        end
    else
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckSystemVersion", ELaunchStepResult.ELSR_Success, "")
        end
    end
end

function EntranceFlow.ShowDriverWarningHD(driverVersion)
    local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_ConfirmHD", "确定")
    local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowGPUDriverWarning2HD", "检测到显卡驱动版本过低：%s，请更新驱动以获取最佳体验。")
    noticeText = string.format(noticeText, driverVersion)

    local param = {
        title = title,
        content = noticeText,
        btnStyle = 1,
        hideCloseBtn = true,
        backgroudUnableClick = true,
        hideEscBtn = true,
        btnText = {
            confirmText,
        },
        btnClick = {
            function()
                log("ShowDriverWarningHD Close")
            end,
        }
    }
    Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
end

function EntranceFlow.GPUDriverWarningAndQuitHD()
    log("GPUDriverWarningAndQuitHD")

    if _WITH_EDITOR == 1 then
        DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Success, "Editor Skip")
        return
    end

    local ULuaExtension = import("LuaExtension")
    if ULuaExtension.Ext_FileExists("C:/DFMTemp/479239F6766A9DE42696B6EDB3EC39D44AF16B18873248D3F28B687F4DACF556.txt") then
        log("通过后门跳过驱动检查")
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Success, "SecretDoor")
        end
        return
    end

    local checkDirver = false

    if not checkDirver then
        DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Success, "")
        return
    end

    local function _QuitGame()
        local UKismetSystemLibrary = import "KismetSystemLibrary"
        local EQuitPreference = import "EQuitPreference"
        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end
    local UVideoSettingHelper = import("VideoSettingHelper")
    EntranceFlow.InitCommonUIModules()
    local EGPDriverCompatibilityHandleMethod = import("EGPDriverCompatibilityHandleMethod")

    local title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示")


    local bSuccess, handleMethod, adapterName, driverVersion = UVideoSettingHelper.CheckGPUDriverVersionV2(nil, nil, nil)
    if bSuccess then
        DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Success, "")
    else
        if handleMethod == EGPDriverCompatibilityHandleMethod.Warning then
            -- warning once
            local videoSetting = import("ClientVideoSettingHD").Get()
            if videoSetting.bOnceGPUDriverWarning then
                DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Success, "Once Warned")
                return
            else
                videoSetting.bOnceGPUDriverWarning = true
                videoSetting:SaveToSaved()
            end

            local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_ConfirmHD", "确定")
            local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowGPUDriverWarning2HD", "检测到显卡驱动版本过低：%s，请更新驱动以获取最佳体验。")
            noticeText = string.format(noticeText, driverVersion)

            local param = {
                title = title,
                content = noticeText,
                btnStyle = 1,
                hideCloseBtn = true,
                backgroudUnableClick = true,
                hideEscBtn = true,
                btnText = {
                    confirmText,
                },
                btnClick = {
                    function()
                        DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Success, "")
                    end,
                }
            }
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
        else
            local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGameHD", "退出游戏")
            local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowGPUDriverWarningHD", "检测到不受支持的显卡驱动版本：%s，请更新驱动程序后重试。")
            noticeText = string.format(noticeText, driverVersion)

            local param = {
                title = title,
                content = noticeText,
                btnStyle = 1,
                hideCloseBtn = true,
                backgroudUnableClick = true,
                hideEscBtn = true,
                btnText = {
                    confirmText,
                },
                btnClick = {
                    _QuitGame,
                }
            }
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
            DFGameLaunchManager:FinishStep("CheckGPUDriver", ELaunchStepResult.ELSR_Failed, "Driver Out Of Date")
        end
    end
end

function EntranceFlow.CheckHardwareModelHD()
    log("CheckHardwareModelHD")

	-- BEGIN MODIFICATION - Liao Lianyu: XSX Skip CheckHardware
	if PLATFORM_GEN9 == 1 then
	    DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Success, "XSX does not require hardware checks")
		return
	end
	-- END MODIFICATION - VIRTUOS

    if _WITH_EDITOR == 1 then
        DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Success, "Editor Skip")
        return
    end

    local ULuaExtension = import("LuaExtension")
    if ULuaExtension.Ext_FileExists("C:/DFMTemp/479239F6766A9DE42696B6EDB3EC39D44AF16B18873248D3F28B687F4DACF556.txt") then
        log("通过后门跳过了硬件检查")
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Success, "SecretDoor")
        end
        return
    end
    EntranceFlow.InitCommonUIModules()
    local UVideoSettingHelper = import("VideoSettingHelper")
    local SystemInfoHD = UVideoSettingHelper.GetSystemInfoHD()

    local function _QuitGame()
        local UKismetSystemLibrary = import "KismetSystemLibrary"
        local EQuitPreference = import "EQuitPreference"
        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
    end

    local bRunIntegratedGPU = false
    local bRunRam = false
    local bRunCPU = false
    local bRunGPU = false


    if bRunIntegratedGPU and UVideoSettingHelper.OnlyOneIntegratedAdapter() then
        local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowIntegratedAdapterWarningHD", "亲爱的玩家，我们的游戏暂时需要独立显卡才能运行。请升级您的硬件以获得最佳体验，感谢理解与支持！")
        noticeText = string.format(noticeText, minRam)
        local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGameHD", "退出游戏")

        local param = {
            title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示"),
            content = noticeText,
            btnStyle = 1,
            hideCloseBtn = true,
            hideEscBtn = true,
            backgroudUnableClick = true,
            btnText = {
                confirmText,
            },
            btnClick = {
                _QuitGame,
            }
        }
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Failed, "Integrated Adapter Check Failed") -- 主流程失败上报后将会阻塞，玩家点击确定直接退出游戏
        end
    else
        local minRam = 15
        if bRunRam and SystemInfoHD.Ram < minRam then
            local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowRamWarningHD", "检测到内存低于%dGB。")
            noticeText = string.format(noticeText, minRam)
            local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGameHD", "退出游戏")

            local param = {
                title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示"),
                content = noticeText,
                btnStyle = 1,
                hideCloseBtn = true,
                hideEscBtn = true,
                backgroudUnableClick = true,
                btnText = {
                    confirmText,
                },
                btnClick = {
                    _QuitGame,
                }
            }
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
            if DFGameLaunchManager then
                DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Failed, "Ram Model Check Failed") -- 主流程失败上报后将会阻塞，玩家点击确定直接退出游戏
            end
        else
            local minCores = 4
            if bRunCPU and SystemInfoHD.CPUCores < minCores then
                local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowCPUModalWarningHD", "检测到CPU核心少于%d核。")
                noticeText = string.format(noticeText, minCores)
                local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGameHD", "退出游戏")

                local param = {
                    title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示"),
                    content = noticeText,
                    btnStyle = 1,
                    hideCloseBtn = true,
                    hideEscBtn = true,
                    backgroudUnableClick = true,
                    btnText = {
                        confirmText,
                    },
                    btnClick = {
                        _QuitGame,
                    }
                }
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
                if DFGameLaunchManager then
                    DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Failed, "CPU Model Check Failed") -- 主流程失败上报后将会阻塞，玩家点击确定直接退出游戏
                end
            else
                -- local EGraphicsQualityLevelHD = import("EGraphicsQualityLevelHD")
                if bRunGPU and not UVideoSettingHelper.AnyGPUSupportedHD() then
                    local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowGPUModalWarningHD", "检测到不受支持的显卡型号。")
                    local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_QuitGameHD", "退出游戏")

                    local param = {
                        title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示"),
                        content = noticeText,
                        btnStyle = 1,
                        hideCloseBtn = true,
                        hideEscBtn = true,
                        backgroudUnableClick = true,
                        btnText = {
                            confirmText,
                        },
                        btnClick = {
                            _QuitGame,
                        }
                    }
                    Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)
                    if DFGameLaunchManager then
                        DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Failed, "GPU Model Check Failed") -- 主流程失败上报后将会阻塞，玩家点击确定直接退出游戏
                    end
                else
                    local function OnFinished()
                        -- 检测通过
                        if DFGameLaunchManager then
                            DFGameLaunchManager:FinishStep("CheckHardwareModel", ELaunchStepResult.ELSR_Success, "")
                        end
                    end
                    -- warning 逻辑
                    if not Facade.ModuleManager:IsModuleValid("SystemSetting") then
                        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SystemSetting", false, true)
                    end
                    Module.SystemSetting:CheckAndShowResizableBarWarningHD(OnFinished)
                end
            end
        end
    end
end

function EntranceFlow.MemoryWarningHD()
    log("MemoryWarningHD")
    EntranceFlow.InitCommonUIModules()

    local minMemoryStr= "4.5G"
    local MinPhyscMemLeftMB = 4608;

    local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowMemoryWarningHD", "系统检测到您的设备剩余可用物理内存低于%s，将会影响游戏性能，建议您关闭一些电脑进程。")
    local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_ConfirmHD", "确定")
    noticeText = string.format(noticeText, minMemoryStr);

    local UHardwareParamHelper = import("HardwareParamHelper")
    local PhyscMemLeftMB = UHardwareParamHelper.GetPlatformPhysicalMemoryLeft();
    local CommitedMemLeftMB = UHardwareParamHelper.GetPlatformCommittedMemoryLeft();
    UHardwareParamHelper.Log("PhyscMemLeftMB:"..PhyscMemLeftMB);
    UHardwareParamHelper.Log("CommitedMemLeftMB:"..CommitedMemLeftMB);

    local function _OnContinue()
        local UDFMGameplayGlobalDelegates = import("DFMGameplayGlobalDelegates").Get(GetGameInstance())
        if UDFMGameplayGlobalDelegates then
            UDFMGameplayGlobalDelegates:OnCheckMemoryEnd()
        end

        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckMemory", ELaunchStepResult.ELSR_Failed, "Memory Warning") -- 非主流程，只要触发失败上报就好
        end
    end

    local function _OnQuit()
        local UKismetSystemLibrary = import "KismetSystemLibrary"
        local EQuitPreference = import "EQuitPreference"
        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
    end

    local UDFMGameGPM = import "DFMGameGPM"
    UDFMGameGPM.ReportOneParam("EntranceFlow", "PhyscMemLeftMB" ,PhyscMemLeftMB, 0)
    UDFMGameGPM.ReportOneParam("EntranceFlow", "CommitedMemLeftMB" ,CommitedMemLeftMB, 0)

    if PhyscMemLeftMB <= MinPhyscMemLeftMB then
        -- local param = {
        --     title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示"),
        --     content = noticeText,
        --     btnStyle = 2,
        --     hideCloseBtn = true,
        --     backgroudUnableClick = true,
        --     btnText = {
        --         NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceQuit", "退出游戏"),
        --         NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceContinue", "仍要继续")
        --     },
        --     btnClick = {
        --         _OnQuit,
        --         _OnContinue
        --     }
        -- }
        -- Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)

        --MS24不弹弹窗只上报
        _OnContinue()
    else
        local UDFMGameplayGlobalDelegates = import("DFMGameplayGlobalDelegates").Get(GetGameInstance())
        if UDFMGameplayGlobalDelegates then
            UDFMGameplayGlobalDelegates:OnCheckMemoryEnd()
        end

        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckMemory", ELaunchStepResult.ELSR_Success, "")
        end
    end

end

function EntranceFlow.VideoMemoryWarningHD()
    local UHardwareParamHelper = import("HardwareParamHelper")
    log("VideoMemoryWarningHD")
    EntranceFlow.InitCommonUIModules()

    local minMemoryStrBelow = "2G"
    local MinPhysMemLeftMBBelow = 2048;

    local minMemoryStrLow = "3G"
    local MinPhysMemLeftMBLow = 3072;

    local minMemoryStrMid = "4G"
    local MinPhyscMemLeftMBMid = 4096;

    local minMemoryStrHigh = "5.6G"
    local MinPhyscMemLeftMBHigh = 5734;

    local minMemoryStrEpic = "6G"
    local MinPhyscMemLeftMBEpic = 6144;

    local minMemoryStrWild = "7G"
    local MinPhyscMemLeftMBWild = 7189;


    local MinPhyscMemLeftMB = 7189;

    --给一定的余量，也就是说，极高配机型低于 6G-margin 才会提示“低于6G”；高配机型低于 5G-margin，会提示“低于5G”
    local margin = 300;

    local noticeText = NSLOCTEXT("GlobalText", "Lua_EntranceFlowVedioMemoryWarningHD", "系统检测到您的设备剩余可用显存低于%s，将会影响游戏性能，建议您关闭一些电脑进程。")
    local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_ConfirmHD", "确定")


    local EGraphicsQualityLevelHD_Below = 4
    local EGraphicsQualityLevelHD_Low = 5
    local EGraphicsQualityLevelHD_Mid = 6
    local EGraphicsQualityLevelHD_High = 7
    local EGraphicsQualityLevelHD_Epic = 8
    local EGraphicsQualityLevelHD_Wild = 9

    local UVideoSettingHelper = import("VideoSettingHelper")
    local BenchmarkLevel = UVideoSettingHelper.GetBenchmarkLevelHD();

    if BenchmarkLevel == EGraphicsQualityLevelHD_Epic then
        UHardwareParamHelper.Log("BenchmarkLevel:Epic")
        noticeText = string.format(noticeText, minMemoryStrEpic)
        MinPhyscMemLeftMB = MinPhyscMemLeftMBEpic
    elseif BenchmarkLevel == EGraphicsQualityLevelHD_High then
        UHardwareParamHelper.Log("BenchmarkLevel:High")
        noticeText = string.format(noticeText, minMemoryStrHigh)
        MinPhyscMemLeftMB = MinPhyscMemLeftMBHigh
    elseif BenchmarkLevel == EGraphicsQualityLevelHD_Mid then
        UHardwareParamHelper.Log("BenchmarkLevel:Mid")
        noticeText = string.format(noticeText, minMemoryStrMid)
        MinPhyscMemLeftMB = MinPhyscMemLeftMBMid
    else
        UHardwareParamHelper.Log("BenchmarkLevel:other")
        noticeText = string.format(noticeText, minMemoryStrMid)
        MinPhyscMemLeftMB = MinPhyscMemLeftMBMid
    end

    MinPhyscMemLeftMB = MinPhyscMemLeftMB - margin

    -- 实际拿到的是当前已经使用的显存

    local videoMemUsedMB = UHardwareParamHelper:GetCurrentUsedVideoMemoryOnLogin()

    -- 实际获取的显存
    local SystemInfoHD = UVideoSettingHelper.GetSystemInfoHD()
    local videoMemLeftMB = SystemInfoHD.VRam*1024 - videoMemUsedMB;

    local UDFMGameGPM = import "DFMGameGPM"
    UDFMGameGPM.ReportOneParam("EntranceFlow", "VideoMemLeftMB" ,videoMemLeftMB, 0)


    UHardwareParamHelper.Log("SystemInfoHD.VRam(MB):")
    UHardwareParamHelper.Log(SystemInfoHD.VRam*1024)

    UHardwareParamHelper.Log("videoMemLeftMB:")
    UHardwareParamHelper.Log(videoMemLeftMB)

    local function _OnConfirm()
        local UDFMGameplayGlobalDelegates = import("DFMGameplayGlobalDelegates").Get(GetGameInstance())
        if UDFMGameplayGlobalDelegates then
            UDFMGameplayGlobalDelegates:OnCheckVideoMemoryEnd();
        end

        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckVideoMemory", ELaunchStepResult.ELSR_Failed, "Memory Warning") -- 非主流程，只要触发失败上报就好
        end
    end

    -- _OnConfirm()
    -- return

    if videoMemLeftMB <= 0 then
        _OnConfirm()
        return
    end

    if videoMemLeftMB <= MinPhyscMemLeftMB then
        -- Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
        --     noticeText,
        --     _OnConfirm,
        --     confirmText
        --     )
        --MS24不弹弹窗只上报
        _OnConfirm()
    else
        local UDFMGameplayGlobalDelegates = import("DFMGameplayGlobalDelegates").Get(GetGameInstance())
        if UDFMGameplayGlobalDelegates then
            UDFMGameplayGlobalDelegates:OnCheckVideoMemoryEnd();
        end

        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("CheckVideoMemory", ELaunchStepResult.ELSR_Success, "")
        end
    end


end

function EntranceFlow.GPUCrashDebuggingWarning()
    log("GPUCrashDebuggingWarning")

    local videoSetting = import("ClientVideoSettingHD").Get()
    if videoSetting:IsGPUCrashDebuggingEnabled() and videoSetting.bOnceGPUCrashDebuggingWarning then

        EntranceFlow.InitCommonUIModules()

        local title = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_DeviceNotice", "登录提示")
        local noticeText = NSLOCTEXT("GlobalText", "Lua_SystemSetting_HDSetting_GPUCrashDebuggingOnceWarningTxt", "检测到您发生多次游戏崩溃，现已经打开GPU调试，这可以提供更多信息帮助我们提升游戏稳定性。如果引起游戏性能下降，您可以在设置中关闭。")
        local confirmText = NSLOCTEXT("GlobalText", "Lua_SystemSetting_HDSetting_GPUCrashDebuggingOnceWarningConfirmTxt", "确认")

        local param = {
            title = title,
            content = noticeText,
            btnStyle = 1,
            hideCloseBtn = true,
            backgroudUnableClick = true,
            hideEscBtn = true,
            btnText = {
                confirmText,
            },
            btnClick = {
                function()
                    DFGameLaunchManager:FinishStep("GPUCrashDebuggingWarning", ELaunchStepResult.ELSR_Success, "")
                end,
            }
        }
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonSetUpPopWindows,nil,nil,param)

        videoSetting.bOnceGPUCrashDebuggingWarning = false
        videoSetting:SaveToSaved()
    else
        DFGameLaunchManager:FinishStep("GPUCrashDebuggingWarning", ELaunchStepResult.ELSR_Success, "")
    end
end

EntranceFlow.bBaseInit = false
function EntranceFlow.LoginEntrance(bInGameDebug)
    log("-------------- Init [Login] Business Start --------------")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Permission", false)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonUILibrary", false, true)
    -- trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Example", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonTips", nil, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonWidget")
    -- BEGIN MODIFICATION @ VIRTUOS ：WeGame不支持Console
    if not  (IsXboxSeries() or IsPS5Family())  then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "WeGameSDK")
    end
    -- END MODIFICATION
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ExpansionPackCoordinator")
    if not Facade.ModuleManager:IsModuleValid("CommonBar") then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonBar", nil, true)
    end

    --- 体验数据本地系统
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ExperienceData", true)

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Account")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Match") -- 依赖AccountServer
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Hope")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Login")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "GPM")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Tss")

    -- BEGIN MODIFICATION - chenweixin
    if DFHD_LUA == 1 and PLATFORM_WINDOWS == 1 then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ACE")
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ParamAdjust")
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "DFOnlineService")
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Discord")
    end
    -- END MODIFICATION - VIRTUOS

    --trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LBS")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "RuntimeDebug")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LocalizeFont")
    log("-------------- Init [Login] Business End !! --------------")

    log("-------------- Init [Server] Business Start --------------")
    ---init business server
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ModuleSwitcher", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ModuleUnlock", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Jump", true)

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Pay")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Collection", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Inventory", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Currency")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "RoleInfo", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "RankingList", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Store", true)

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Gamelet")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ArmedForce", true, true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ItemUnlockPath") -- 物品获取途径Server
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Equipment")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Looting")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ShopStation")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "GameMode",true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ComparePrice") -- 比价购买

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Social")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "FrontEndChat")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Room") -- 依赖MatchServer/AccountServer/GameModeServer/Social
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Team") -- 依赖MatchServer/AccountServer/GameModeServer/Social
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Recruit")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Season") -- FrontendMgr/ProtoMgr
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Chat") -- 依赖TeamServer/估计会有RoomServer中的邀请
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Settlement") -- 依赖 InventoryServer/AccountServer/MatchServer/SeasonServer

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Reward")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Scav")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Property", true)
    if not VersionUtil.IsShipping() then
        trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "GM")
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "GM")
    end
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Quest", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Shop", true) -- 依赖RoleInfoServer(声望)/QuestServer
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ShopMystery",true)

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Preset", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Mail")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Friend")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Report", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Reputation")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Hero", true) -- 依赖InventoryServer/ArmedForceServer
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "PVESettlement")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "PVEEntry")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "WeaponAssembly", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Gunsmith")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "WorldEntrance")

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "IrisSafeHouse")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "BlackSite", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Auction", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Insurance")
    -- trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "SafeHouse")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "SystemSetting",true) --自定义布局数据，需要在登录时拉取一次
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "IrisRaid")

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Localize")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "QuickPatch")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "TipsRecord")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Guide")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Activity", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "LogUpload")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "LitePack")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "FacePop")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "DataTableHotfix")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "ModeHall")

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Transition") -- 依赖SettlementServer/GameModeServer/MatchServer/ArmedForceServer

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Vehicle")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "GrowthRoad", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Ranking", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Tournament", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Market", true)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "BattlePass")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Achievement")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CustomerServices", false)
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Moss")
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Armory", true) -- 军械库

    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "Recovery", true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "PopupSequencer")

    if DFHD_LUA == 1 then
        trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "BHD")
    end
    trycall(Facade.ServerManager.InitServer, Facade.ServerManager, "CollectionRoom", true)
    -- TODO Init More Servers

    GetOnLoginBusinessInit():Invoke()

    if not bInGameDebug then
        -- Lua启动就直接播BGM了
        Facade.SoundManager:PlayLoginStartBGM()
    end

    -- slua.MaxLuaExecTime
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "slua.MaxLuaExecTime 10", nil)

    -- 初始化经分
    require("DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool")
    log("-------------- Init [Server] Business End !! --------------")

    log("-------------- Init [Login Dependence] Business Start --------------")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LitePackage", false, true) -- CommonWidget模块中的CommonVideoView会对LitePackage有依赖
    -- trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonWidget")
    -- trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonBar", nil, true)
    log("-------------- Init [Login Dependence] Business End --------------")

    EntranceFlow.bBaseInit = true
end

EntranceFlow.bLobbyInited = false
function EntranceFlow.LobbyEntrance()
    if not EntranceFlow.bLobbyInited then
        EntranceFlow.DoLobbyEntrance()
        EntranceFlow.bLobbyInited = true
        GetOnLobbyBusinessInit():Invoke()
    else
        log("-------------- Lobby Business Has Inited !! --------------")
    end
end

function EntranceFlow.DoLobbyEntrance()
    log("-------------- Init [Lobby] Business Start --------------")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "NetworkControl")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CDNIcon")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ModuleSwitcher")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ModuleUnlock")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LiveRadio")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Pay")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Gamelet", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ReddotTrie", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ItemDetail", nil, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Invite")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Share")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Social")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Chat")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Inventory", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Currency")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Looting")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Settlement")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "GVoice")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Hall")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "RoleInfo", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "RankingList", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Store", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "FastEquip")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Shop", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ComparePrice")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "BattlefieldEntry", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ArmedForce", false, true)-- 依赖BattlefieldEntry 判断是否进入新旧大厅
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Preparation")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Room")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Archive")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Activity", false, true)

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Reward")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Quest")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "StoryDialog")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SystemSetting", false, true)
    if DFHD_LUA == 1 then
        trycall(Module.SystemSetting.ClientAddTgLog_HDSetting, Module.SystemSetting)
    end
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Recruit", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Mail", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Auction", false, true)
    -- trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SafeHouse", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "BlackSite", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "GameMode")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "PVESettlement")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Hero", false)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "WeaponAssembly", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Map")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Collection", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "IrisSafeHouse", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "HUD", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "HUDToolBar")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Range", false, true)

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "DeepLink")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Team")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LobbyDisplay", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Insurance")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Friend")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Report")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Reputation")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SOL")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SandBoxMap", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "IrisRaid")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "InGame")

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SOLHealthSystem")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Reconnect")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Jump")

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "WeaponDebugView")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "UISceneObject")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Gunsmith", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "PlayerReturn") -- 玩家回流活动模块
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Armory") -- 军械库模块


    if DFHD_LUA == 1 then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Discovery")
    end

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Match")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "FacePop", false, true)
    -- trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "ReddotTrie")

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Vehicle", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Ranking", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Tournament", false, true)
    if DFHD_LUA == 1 then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "LobbyBHD", false, true)
    end
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Market", false, true)

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "BattlePass", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Guide", nil, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Moss", false, true)

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Recovery")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Subscribe")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "MusicPlayer", false, true)
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "AccountBind")

    --- 运营专区11模块（OpAct + CustomService + MicroOfficialWeb）
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActWeChatWelfare")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActWeChatPrivilege")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActWeChatVideoChannel")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActMinorProtection")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActQQChannel")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActQQPrivilege")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActQQBonusCenter")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActQQGameHub")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActXYClub")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActWeChatGameHub")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActPerks")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "OpActXunYouClub")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "MicroOfficialWeb")

    --- DFDevops模块的初始化
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "DFDevops")
    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CollectionRoom", false, true)

    trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "NetworkBusiness")
    -- TODO Init More Modules
    log("-------------- Init [lobby] Business End !! --------------")
end

EntranceFlow.bServerInited = false
function EntranceFlow.ServerFetchEntrance()
    print('Flow_FetchServerBusiness')
    if not EntranceFlow.bServerInited then
        EntranceFlow.DoServerFetchEntrance()
        EntranceFlow.bServerInited = true
    else
        log("-------------- LoginToModeHall ServerBusiness Has Fetched !! --------------")
    end
end


function EntranceFlow.DoServerFetchEntrance()
    log("-------------- Fetch [Server Data] Start --------------")
    trycall(Server.JumpServer.FetchServerData, Server.JumpServer)
    trycall(Server.ModuleSwitcherServer.FetchServerData, Server.ModuleSwitcherServer)
    trycall(Server.ModuleUnlockServer.FetchServerData, Server.ModuleUnlockServer)
    trycall(Server.InventoryServer.FetchServerData, Server.InventoryServer)
    trycall(Server.CollectionServer.FetchServerData, Server.CollectionServer)
    trycall(Server.RecruitServer.FetchServerData, Server.RecruitServer)
    trycall(Server.CurrencyServer.FetchServerData, Server.CurrencyServer)
    trycall(Server.RoleInfoServer.FetchServerData, Server.RoleInfoServer)
    trycall(Server.PayServer.FetchServerData, Server.PayServer)
    trycall(Server.StoreServer.FetchServerData, Server.StoreServer)
    trycall(Server.ShopServer.FetchServerData, Server.ShopServer)
    trycall(Server.ShopMysteryServer.FetchServerData, Server.ShopMysteryServer)
    trycall(Server.ArmedForceServer.FetchServerData, Server.ArmedForceServer)
    trycall(Server.PresetServer.FetchServerData, Server.PresetServer)
    trycall(Server.HeroServer.FetchServerData, Server.HeroServer)
    trycall(Server.QuestServer.FetchServerData, Server.QuestServer)
    trycall(Server.AuctionServer.FetchServerData, Server.AuctionServer)
    trycall(Server.MailServer.FetchServerData, Server.MailServer)
    trycall(Server.FriendServer.FetchServerData, Server.FriendServer)
    trycall(Server.PropertyServer.FetchServerData, Server.PropertyServer)
    trycall(Server.FrontEndChatServer.FetchServerData, Server.FrontEndChatServer)
    trycall(Server.ActivityServer.FetchServerData, Server.ActivityServer)

    -- trycall(Server.SeasonServer.GetPlayerProfileSeasonInfo, Server.SeasonServer)
    trycall(Server.SystemSettingServer.FetchServerData, Server.SystemSettingServer)
    trycall(Server.WeaponAssemblyServer.FetchServerData, Server.WeaponAssemblyServer)
    trycall(Server.GunsmithServer.FetchServerData, Server.GunsmithServer)
    trycall(Server.GameModeServer.FetchServerData, Server.GameModeServer)

    trycall(Server.LocalizeServer.FetchServerData, Server.LocalizeServer)
    trycall(Server.QuickPatchServer.FetchServerData, Server.QuickPatchServer)
    trycall(Server.TipsRecordServer.FetchServerData, Server.TipsRecordServer)
    trycall(Server.ReportServer.FetchServerData, Server.ReportServer)
    trycall(Server.ReputationServer.FetchServerData, Server.ReputationServer)
    trycall(Server.GuideServer.FetchServerData, Server.GuideServer)
    trycall(Server.FacePopServer.FetchServerData, Server.FacePopServer)
    trycall(Server.GrowthRoadServer.FetchServerData, Server.GrowthRoadServer)
    trycall(Server.RankingServer.FetchServerData, Server.RankingServer)
    trycall(Server.TournamentServer.FetchServerData, Server.TournamentServer)
    trycall(Server.MarketServer.FetchServerData, Server.MarketServer)
    trycall(Server.BattlePassServer.FetchServerData, Server.BattlePassServer)
    trycall(Server.RecoveryServer.FetchServerData, Server.RecoveryServer)
    --trycall(Server.LitePackServer.FetchServerData, Server.LitePackServer)
    -- TODO fetch more server data
    log("-------------- Fetch [Server Data] End !! --------------")
end

function EntranceFlow.SwitchLocEntrance()
    log("------------- ReLoad Config Loc Start --------------")
    Facade.ProtoManager:TryDisConnectServer(true)
    ---此处并不需要重载Loc，由于回到登陆后Lua虚拟机重启，会全部重新加载
    ---以下代码可放到登录界面切换语言使用
    -- Facade.SoundManager:StopBGM()
    -- require"DFM.YxFramework.Core.Library.LuaMarcos"
    -- clearlualoctext()
    -- reload("DFM.Business.Misc.DFMGlobalConst")
    -- reload("DFM.YxFramework.Core.LuaGlobalConst")
    -- reload("DFM.Business.ServerCenter.ServerConfig.ServerTipCode")
    -- Facade.ModuleManager:ReloadAllConfig()
    log("------------- ReLoad Config Loc End !! --------------")
end

function EntranceFlow.PostLaunchEntrance()
    if DFHD_LUA == 1 then
        require("DFM.Business.Misc.DFMGlobalConst")
        require("DFM.Business.Misc.DFMAudioConst")
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonWidget")
        if not Facade.ModuleManager:IsModuleValid("CommonBar") then
            trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonBar", nil, true)
        end
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonUILibrary", true)
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "SystemSetting", true, true)
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "PostLaunch", true)
        Module.PostLaunch.ShowFirstPanel()
    end
end
--------------------------------------------------------------------------
--- 其他
--------------------------------------------------------------------------
function EntranceFlow.SingleLobbyEntrance()
    log("-------------- Init [SingleLobby] Business Start --------------")
    ---init InitSingleLobbyBusiness module
    Facade.UIManager:Reset()
    Facade.ActorManager:Reset()
    --[xxww]单机入局显示Debug信息
    if not VersionUtil.IsShipping() then
        Module.GM:ShowPlayerInfoHint()
    end
    EntranceFlow.LobbyEntrance()
    log("-------------- Init [SingleLobby] Business End !! --------------")
end

EntranceFlow.bStanalone = false
function EntranceFlow.StandaloneEntrance(bNewGameFlowTime)
    --判断一下World
    local UGameplayStatics = import "GameplayStatics"
    local LevelName = UGameplayStatics.GetCurrentLevelName(GetGameInstance(), true)
    --因为非单机启动时这个是时间点拿到的名字是一个未定义字段
    --所以login可以代表单机且是login
    --因为考虑到热更，不能把所有东西都放的这么前
    --走login时依赖gameflow的初始化，不走这里
    -- [6.8 Update] [aidenliao] 从局内返回局外时会重启Lua虚拟机，由于当前???景是Loading，因此需要加一个判断
    if LevelName == "Login" or LevelName == "LoadingMap" then
        log("-------------- Skip [InGameDebug] Business --------------")
        return
    end
    local LevelName = UGameplayStatics.GetCurrentLevelName(GetGameInstance(), true)
    if LevelName == "ExampleDisplayLevel" then
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "CommonUILibrary", false, true)
        trycall(Facade.ModuleManager.InitModule, Facade.ModuleManager, "Example", false, true)
    end
    EntranceFlow.bStanalone = true
    log("-------------- Init [InGameDebug] Business Start --------------")
    EntranceFlow.HotFixEntrance()
    EntranceFlow.GCloudEntrance()
    EntranceFlow.VersionUpdateEntrance()
    EntranceFlow.PreloadEntrance()
    --同步加载表格
    local DataTableManager = import "DataTableSystemManagerLite"
    DataTableManagerInstance = DataTableManager:Get()
    DataTableManagerInstance:SyncPreloadAll()
    --依赖表格的逻辑
    EntranceFlow.LoginEntrance(true)
    EntranceFlow.LobbyEntrance()
    --[xxww]单机入局显示Debug信息
    if not VersionUtil.IsShipping() then
        Module.GM:ShowPlayerInfoHint()
    end
    log("-------------- Init [InGameDebug] Business End !! --------------")
end

function EntranceFlow.InGameDebugEntrance()
    --[[
    log("-------------- Init [InGameDebug] Business Start --------------")
    EntranceFlow.HotFixEntrance()
    EntranceFlow.GCloudEntrance()
    EntranceFlow.VersionUpdateEntrance()
    EntranceFlow.PreloadEntrance()
    --同步加载表格
    local DataTableManager = import "DataTableSystemManagerLite"
    DataTableManagerInstance = DataTableManager:Get()
    DataTableManagerInstance:SyncPreloadAll()
    --依赖表格的逻辑
    EntranceFlow.LoginEntrance(true)
    EntranceFlow.LobbyEntrance()
    --[xxww]单机入局显示Debug信息
    if not VersionUtil.IsShipping() then
        Module.GM:ShowPlayerInfoHint()
    end
    log("-------------- Init [InGameDebug] Business End !! --------------")
    ]]--
end

--------------------------------------------------------------------------
--- 业务ShutDown流程
--------------------------------------------------------------------------
function EntranceFlow.ShutDownBusinessEntrance()
    log("-------------- ShutDown [DFM] Business Start --------------")
    if DFMGlobalEvents then
        DFMGlobalEvents.evtBusinessShutDown:Invoke()
    end
    local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    if hudLayerController then
        hudLayerController:Reset()
    end
    Facade.UIManager:Reset()
    Facade.UIManager:ClearExUIResStub()
    Facade.ModuleManager:Release()
    Facade.ServerManager:Release()
    releaseevts(DFMGlobalEvents)
    releaseevts(EntranceGlobalEvents)
    log("-------------- ShutDown [DFM] Business End !! --------------")
end

--------------------------------------------------------------------------
--- 返回大厅Lua虚拟机重启后的业务恢复流程
--------------------------------------------------------------------------
function EntranceFlow.RestartLoginBusinessEntrance()
    log("------------- Restart [DFM] Business Start ------------------")
    EntranceFlow.LoginEntrance(true) -- 由于是直接模拟的重启流程，因此这里不希望再播放登录的音乐，这里直接跳了
    Facade.GameFlowManager:RecoveryState(false) -- 恢复GameFlow状态
end

-- [update 23/11/29] 登陆状态迁移到了C++，lua侧不处理网络相关业务了
-- function EntranceFlow._OnReconnected(connectResultInfo)
--     log("-----------Reconnected------------")
--     LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(false) -- 关闭重连弹窗
--     Facade.ProtoManager.Events.evtOnConnectFail:RemoveListener(EntranceFlow._DoReconnect)
--     Module.Login.Config.Events.evtOnLoginSuccess:RemoveListener(EntranceFlow._OnReconnected)
--     Module.Login.Config.Events.evtOnLoginFailed:RemoveListener(EntranceFlow._OnReconnectFail)
--
--     Facade.ProtoManager.Events.evtOnConnectSuccess:RemoveListener(EntranceFlow._OnReconnected)
--     Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(EntranceFlow._OnReconnected)
--
--     local UGameSDKManager = import "GameSDKManager"
--     local gameConnectIns = UGameSDKManager.GetGameConnectIns(GetGameInstance())
--     if gameConnectIns then
--         gameConnectIns:BroadcastRestartReconnected(true)
--     end
-- end
--
-- function EntranceFlow._OnReconnectFail(connectResultInfo)
--     log("-----------ReConnect Failed------------")
--     EntranceFlow._DoReconnect() -- 弹窗提示玩家网络出现问题，并手动重连
-- end
--
-- -- 通知重连失败，返回登录 [Update 9/12 PC平台直接退出游戏]
-- function EntranceFlow._InvokeReconnectFailed()
--     log("-----------_InvokeReconnectFailed Failed------------")
--     LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(false) -- 关闭重连弹窗
--
--     if IsHD() and IsWeGameEnabled() then
--         -- PC直接退出游戏，这里直接退就好了，不用处理加载失败的过程，那边单独处理
--         local UKismetSystemLibrary = import "KismetSystemLibrary"
--         local EQuitPreference = import "EQuitPreference"
--         UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
--         return
--     end
--
--     Facade.ProtoManager.Events.evtOnConnectFail:RemoveListener(EntranceFlow._DoReconnect)
--     Module.Login.Config.Events.evtOnLoginSuccess:RemoveListener(EntranceFlow._OnReconnected)
--     Module.Login.Config.Events.evtOnLoginFailed:RemoveListener(EntranceFlow._OnReconnectFail)
--
--     Facade.ProtoManager.Events.evtOnConnectSuccess:RemoveListener(EntranceFlow._OnReconnected)
--     Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(EntranceFlow._OnReconnected)
--
--     local UGameSDKManager = import "GameSDKManager"
--     local gameConnectIns = UGameSDKManager.GetGameConnectIns(GetGameInstance())
--     if gameConnectIns then
--         gameConnectIns:BroadcastRestartReconnected(false)
--     end
-- end
--
-- function EntranceFlow.DoReconnectEntrance()
--     -- 开始重连，走断线重连逻辑
--     log("--------------Start Reconnect-------------")
--     Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(EntranceFlow._OnReconnected)
--     Facade.ProtoManager.Events.evtOnConnectFail:AddListener(EntranceFlow._DoReconnect) -- 断线重连失败弹窗提示
--
--     if Facade.ProtoManager:GetIsDisconnected() then
--         -- 先看当前C++侧状态，如果是已经断线了，那就走直接连接，否则主动断线
--         log("--------------Reconnect Directly-------------")
--         -- 注意，这个时候会有断线重连的弹窗弹出来，遵循断线重连的流程就好，直到连接成功
--         -- 移除自动登录的逻辑，因为已经断连了，自动登录是没用的，需要玩家的手动恢复
--         -- Facade.ProtoManager.Events.evtOnConnectFail:RemoveListener(EntranceFlow.DoAutoLogin)
--         -- 连接失败继续弹弹窗，直到连接成功或者玩家手动退出
--         -- 开始走连接逻辑，需要不断确认是否重试
--         EntranceFlow._DoReconnect()
--     else
--         log("--------------Disconnet First-------------")
--         Facade.ProtoManager:TryStartReconnectUseLastConnectedInfo() -- 连接还在，直接尝试主动断连然后开始重连流程，一般这个过程会很快
--     end
-- end
--
-- -- 注意，这里和业务系统的重连不太一样，仅关注连接服务器和登录相关，不做其他业务的短线重连恢复，这个时候其他业务都是刚初始化的状态
-- function EntranceFlow._DoReconnect()
--     log("----------------Do Reconnect---------------")
--     Facade.ProtoManager.Events.evtOnConnectFail:RemoveListener(EntranceFlow._DoReconnect)
--     Module.Login.Config.Events.evtOnLoginSuccess:AddListener(EntranceFlow._OnReconnected)
--
--     local noticeText = NSLOCTEXT("GlobalText", "Lua_Proto_ConnectNotLink", "网络断开, 是否尝试再次连接")
--     local cancelText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_BackToLogin", "返回登录")
--     if IsHD() then
--         cancelText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_ExitGame_PCOnly", "退出游戏")
--     end
--     local confirmText = NSLOCTEXT("GlobalText", "Lua_EntranceFlow_ConfirmReconnect", "确认重连")
--     Module.GCloudSDK:ShowCommonTip(
--         noticeText,
--         confirmText,
--         cancelText,
--         false,
--         EntranceFlow._DoConnectServer,
--         EntranceFlow._InvokeReconnectFailed,
--         true
--     )
-- end
--
-- function EntranceFlow._DoConnectServer()
--     log("----------------Do Connect Server---------------")
--     LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(true)
--     Facade.ProtoManager.Events.evtOnConnectFail:AddListener(EntranceFlow._DoReconnect) -- 每次监听连接失败的事件，在回调里去掉
--     Facade.ProtoManager:ConnectDirectly() -- 直接尝试重连
-- end

function EntranceFlow.RestartLobbyBusinessEntrance()
    EntranceFlow.LobbyEntrance()

    Facade.GameFlowManager:RecoveryState(true)
    LuaGlobalEvents.evtOnPostRestartSystem:Invoke()
end

--------------------------------------------------------------------------
--- World
--------------------------------------------------------------------------
function EntranceFlow.DoForceCleanUpObjects()
    if not EntranceFlow.bBaseInit then
        return
    end
    -- 保底
    require "DFM.YxFramework.Plugin.Memory.MemTool"
    logerror('#[ GFChange VS WorldCleanUp ]-------------------------------CleanAllCppObjects【DoForceCleanUpObjects】',
    ' gameFlowType:', __DebugOnly_GameFlowName(Facade.GameFlowManager:GetCurrentGameFlow()),
    'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
    MemTool.CleanAllCppObjects()
end

--------------------------------------------------------------------------
--- Event
--------------------------------------------------------------------------
function GetOnHudInit()
    require("DFM.BusinessEntrance.EntranceGlobalEvents")
    return EntranceGlobalEvents.evtHudInit
end

function GetOnLobbyBusinessInit()
    require("DFM.BusinessEntrance.EntranceGlobalEvents")
    return EntranceGlobalEvents.evtLobbyBusinessInit
end

function GetOnLoginBusinessInit()
    require("DFM.BusinessEntrance.EntranceGlobalEvents")
    return EntranceGlobalEvents.evtLoginBusinessInit
end

--------------------------------------------------------------------------
--- HUD
--------------------------------------------------------------------------
function EntranceFlow.HUDOnConstructionEntrance(hudTable, baseHudTable)
    log("UECall_CreateHUD_ADFMHUD_OnConstruction ", hudTable, " ", baseHudTable)
    if not string.isempty(hudTable) and not string.isempty(baseHudTable) then
        local baseHudNavID = HUDName2ID[hudTable]
        local hudNavID = HUDName2ID[baseHudTable]
        baseHudNavID = setdefault(baseHudNavID, HUDName2ID.BaseHUD)
        local hudSettings = HUDTable[hudNavID]
        local baseHudSettings = HUDTable[baseHudNavID]

        local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
        if hudLayerController then
            hudLayerController:PreSetHudSetting(hudSettings, baseHudSettings)
        end
    end
end
EntranceFlow.bHudInited = false
function EntranceFlow.HUDBeginPlayEntrance(hudTable, baseHudTable)
    log("UECall_CreateHUD_ADFMHUD_BeginPlay ", hudTable, " ", baseHudTable, " ", EntranceFlow.bLobbyInited)

    -- 临时代码，修复无缝���局缺少loading的问题
    -- ms22会正式处理这个问题
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.LobbyToGame or
    Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
        local cls = require "DFM.Business.Module.PreloadModule.LuaInGamePreloadWrapper"
        local wrapper = cls:NewIns()
        wrapper:Imp_BP_OnFlowStart()
    end
    Facade.UIManager:PreloadHUD(HUDName2ID[hudTable], HUDName2ID[baseHudTable])
end

function EntranceFlow.HUDStartInGameEntrance(hudTable, baseHudTable)
    log("UECall_CreateHUD_ADFMHUD_StartInGame ", hudTable, " ", baseHudTable, " ", EntranceFlow.bLobbyInited)

    Facade.UIManager:RefreshHUD(HUDName2ID[hudTable], HUDName2ID[baseHudTable])
end

function EntranceFlow.HUDEndPlayEntrance(hudTable, baseHudTable)
    log("UECall_DeleteHUD_ADFMHUD_EndPlay ", hudTable, " ", baseHudTable)
    EntranceFlow.bHudInited = false
    Facade.UIManager:GetLayerControllerByType(EUILayer.HUD):Reset()
end

function EntranceFlow.HUDPreloadRuleEntrance(hudTable, baseHudTable)
    log("UECall_CreateHUD_AIrisWorldHUD_PreloadRuleHud ", hudTable, " ", baseHudTable)
    Facade.UIManager:RefreshHUD(HUDName2ID[hudTable], HUDName2ID[baseHudTable])
end

function EntranceFlow.HUDDeletePanelsEntrance(hudTable, baseHudTable)
    log("UECall_DeleteHUD_AIrisWorldHUD_DeletePanels ", hudTable, " ", baseHudTable)
    --什么都不用做
    GetOnHudInit():RemoveAllListener()
end

return EntranceFlow
