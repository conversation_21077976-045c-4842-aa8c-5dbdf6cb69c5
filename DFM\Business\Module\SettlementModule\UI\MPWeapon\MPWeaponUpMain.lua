----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class MPWeaponUpMain : LuaUIBaseView
local MPWeaponUpMain = ui("MPWeaponUpMain")
local CommonSkipOverBg = require"DFM.Business.Module.CommonWidgetModule.UI.FullScreen.CommonSkipOverBg"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function MPWeaponUpMain:Ctor()
    self._wtPlatformBox = self:Wnd("PlatformPaddingBox_1", UIWidgetBase)
    self._wtPlatformBox:Collapsed()
    self._wtMPExpPanel  = self:Wnd("MPExpPanel", UIWidgetBase)
    self._wtMPWeaponUpPanel = self:Wnd("MPWeaponUpPanel", UIWidgetBase)
    self._wtNextStepCSOB = self:Wnd("wtNextStepCSOB", CommonSkipOverBg)
    self._wtNextStepCSOB:BindClickEvent(self._JumpToNextStep, self)
    self._wtNextStepCSOB._needKeepBtn = true
    if IsHD() then
        self._jumpHandle = self:AddInputActionBinding("JumpOver", EInputEvent.IE_Pressed, self._JumpToNextStep, self, EDisplayInputActionPriority.UI_Pop)
        -- 绑定游戏流程事件
        Module.Settlement.Config.Events.evtReadyToShowSettlement:AddListener(self._OnReadyToShowSettlement,self)
        Module.Settlement.Config.Events.evtStartToShowSettlement:AddListener(self._OnStartToShowSettlement,self)
        self._isReadyToShow = false      
    end

    self._selectTab = 0
end

function MPWeaponUpMain:OnInitExtraData(weaponChangeInfo)
    self._weaponChangeInfo = weaponChangeInfo
end

-- UI打开时触发
function MPWeaponUpMain:OnOpen()
    self:_AddEventListener()
end

-- UI打开时触发
function MPWeaponUpMain:OnClose()
    self:_RemoveEventListener()
    if IsHD() then
        self:RemoveInputActionBinding(self._jumpHandle)
    end
    Facade.UIManager:ClearSubUIByParent(self, self._wtMPExpPanel)
    Facade.UIManager:ClearSubUIByParent(self, self._wtMPWeaponUpPanel)
    Module.Settlement:OpenSettlementUI(self.UINavID)
    Module.ItemDetail:CloseItemDetailPanel()
    Module.Gunsmith.Config.Events.evtMPWeaponUpMainOnClose:Invoke()
end

function MPWeaponUpMain:_InitHandle()
    if isvalid(self._dfNavGroup) then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._dfNavGroup)
    else
        self._dfNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtMPExpPanel, self, "Hittest")
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._dfNavGroup)
    end
end

-- UI显示时触发
function MPWeaponUpMain:OnShowBegin()
    self:_InitHandle()
    self:OpenWeaponUp()
end

function MPWeaponUpMain:OpenWeaponUp()
    self._wtMPWeaponUpPanel:SelfHitTestInvisible()
    self._wtMPExpPanel:Collapsed()
    if self._curWeaponPanel then
        return
    end

    self._curWeaponPanel = Facade.UIManager:AddSubUI(self, UIName2ID.MPWeaponUpItem, self._wtMPWeaponUpPanel, nil, self._weaponChangeInfo)
end

function MPWeaponUpMain:OnHideBegin()
end

------------------------------------ Private function ------------------------------------
function MPWeaponUpMain:_AddEventListener()
end

function MPWeaponUpMain:_RemoveEventListener()
end

function MPWeaponUpMain:_JumpToNextStep()
    Facade.UIManager:CloseUI(self)
end

function MPWeaponUpMain:OnNavBack()
    return true
end

-- BEGIN MODIFICATION @ VIRTUOS
function MPWeaponUpMain:_OnReadyToShowSettlement()
    if IsHD() then
        self._isReadyToShow = true
    end
end

function MPWeaponUpMain:_OnStartToShowSettlement()
    if self._isReadyToShow == true and IsHD() then
        --- 在进入大厅的时候再绑一次，防止因为GameFlowChange调用ClearActionBind导致绑定丢失
        self._jumpHandle = self:AddInputActionBinding("JumpOver", EInputEvent.IE_Pressed, self._JumpToNextStep, self, EDisplayInputActionPriority.UI_Pop)
        self._isReadyToShow = false
    end
end
-- END MODIFICATION

return MPWeaponUpMain
