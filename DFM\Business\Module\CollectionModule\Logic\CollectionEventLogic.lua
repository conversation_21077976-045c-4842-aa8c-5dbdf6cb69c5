----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionEventLogic = {}
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
---------------------------------------------------------------------------------
--- 事件Logic，在模块的生命周期切换时、GameFlow、SubStage切换时注册和取消相应的事件
---------------------------------------------------------------------------------
--- 根据所需注册和取消相应的事件
CollectionEventLogic.AddListeners = function(...)
    Server.CollectionServer.Events.evtCollectionUsePropSucess:AddListener(CollectionEventLogic.OnUsePropSucess)
    Server.CollectionServer.Events.evtCollectionOpenBoxSuccess:AddListener(CollectionEventLogic.OnOpenBoxSucess)
    Server.CollectionServer.Events.evtCollectionPropReachMaxNum:AddListener(CollectionEventLogic.OnPropReachMaxNum)
    Server.CollectionServer.Events.evtUpdateCollectionData:AddListener(CollectionEventLogic.OnUpdateCollectionData)
    Server.CollectionServer.Events.evtFetchCollectionData:AddListener(CollectionEventLogic.OnFetchCollectionData)
    Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes:AddListener(CollectionEventLogic.OnWeaponSkinApplied)
    Server.MatchServer.Events.evtStartMatching:AddListener(CollectionEventLogic.OnStartMatching)
    Server.RewardServer.Events.evtOpenBlindBoxWithItemId:AddListener(CollectionEventLogic.OnOpenBlindBoxWithItemId)
end

CollectionEventLogic.RemoveListeners = function(...)
    Server.CollectionServer.Events.evtCollectionUsePropSucess:RemoveListener(CollectionEventLogic.OnUsePropSucess)
    Server.CollectionServer.Events.evtCollectionOpenBoxSuccess:RemoveListener(CollectionEventLogic.OnOpenBoxSucess)
    Server.CollectionServer.Events.evtCollectionPropReachMaxNum:RemoveListener(CollectionEventLogic.OnPropReachMaxNum)
    Server.CollectionServer.Events.evtUpdateCollectionData:RemoveListener(CollectionEventLogic.OnUpdateCollectionData)
    Server.CollectionServer.Events.evtFetchCollectionData:RemoveListener(CollectionEventLogic.OnFetchCollectionData)
    Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes:RemoveListener(CollectionEventLogic.OnWeaponSkinApplied)
    Server.MatchServer.Events.evtStartMatching:RemoveListener(CollectionEventLogic.OnStartMatching)
    Server.RewardServer.Events.evtOpenBlindBoxWithItemId:RemoveListener(CollectionEventLogic.OnOpenBlindBoxWithItemId)
end

CollectionEventLogic.OnOpenBoxSucess = function(res)
    local dataChange = res.change
    local itemList = {}
    for i, propChange in ipairs(dataChange.prop_changes) do
        if propChange.prop.num > 0 and (propChange.change_type == PropChangeType.Add or propChange.change_type == PropChangeType.Modify or propChange.change_type == PropChangeType.PropChangeNone) then
            local item = ItemBase:NewIns(propChange.prop.id)
            item:SetRawPropInfo(propChange.prop)
            item.num = propChange.delta_num or propChange.prop.num
            table.insert(itemList, item)
        end
    end

    if res.send_by_mail == true then
        --Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.PropsSendByMail)
    end

    for _, change in pairs(dataChange.currency_changes) do
        if change.delta and change.delta > 0 and change.currency_id then
            local item = ItemBase:NewIns(change.currency_id, change.delta)
            table.insert(itemList, item)
        end
    end

    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList)
end

CollectionEventLogic.OnUsePropSucess = function(res, usedItems)
    if usedItems then
        for index, item in ipairs(usedItems) do
            local itemConfigRow = ItemConfigTool.GetItemConfigById(item.id)
            if itemConfigRow and itemConfigRow.UsageTip ~= nil and itemConfigRow.UsageTip ~= "" then
                Module.CommonTips:ShowSimpleTip(itemConfigRow.UsageTip)
            end
        end
    end
    local itemList = {}
    if res.related_change then
        local relatedChange = res.related_change.prop_changes
        for i, propChange in ipairs(relatedChange) do
            if propChange.prop.num > 0 and (propChange.change_type == PropChangeType.Add or propChange.change_type == PropChangeType.PropChangeNone) then
                local item = ItemBase:NewIns(propChange.prop.id)
                item:SetRawPropInfo(propChange.prop)
                item.num = propChange.delta_num or propChange.prop.num
                table.insert(itemList, item)
            end
        end
    end
    if res.data_change then
        local dataChange = res.data_change
        for i, propChange in ipairs(dataChange) do
            if propChange.prop.num > 0 and (propChange.change_type == PropChangeType.Add or propChange.change_type == PropChangeType.PropChangeNone) then
                local item = ItemBase:NewIns(propChange.prop.id)
                item:SetRawPropInfo(propChange.prop)
                item.num = propChange.delta_num or propChange.prop.num
                table.insert(itemList, item)
            end
        end
    end
    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList)
end

CollectionEventLogic.OnPropReachMaxNum = function(reachMaxNumPropId)
    Facade.ConfigManager:SetUserArray(string.format("%s_CacheCollectionPropReachMaxNum", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" ), {})
    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.PropReachMaxNum)
end

CollectionEventLogic.OnStartMatching = function()
    local skinPropInfoList = Module.Collection:GetWeaponSkinsCarring()
    Server.CollectionServer:SetWeaponSkinsCarring(skinPropInfoList)
end

CollectionEventLogic.OnUpdateCollectionData = function(itemsChanged)
    local currentStackUI = Facade.UIManager:GetCurrentStackUI()
    if currentStackUI.UINavID ~= UIName2ID.CollectionMainPanel then
        local alreadyProcessSkinInstance = {}
        for index, item in ipairs(itemsChanged) do
            if not alreadyProcessSkinInstance[item.id] and item.itemMainType == EItemType.WeaponSkin and item.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                Module.Collection.Field:SetSkinList()
                if ItemHelperTool.IsMysticalSkin(item.id) then
                    Module.Collection.Field:SetMysticalSkinInstanceList(item.id, nil)
                    alreadyProcessSkinInstance[item.id] = true
                end
            end
        end
    end
end

CollectionEventLogic.OnFetchCollectionData = function()
    local currentStackUI = Facade.UIManager:GetCurrentStackUI()
    if currentStackUI.UINavID ~= UIName2ID.CollectionMainPanel then
        Module.Collection.Field:SetSkinList()
        Module.Collection.Field:ClearMysticalSkinInstanceList()  
    end
end

CollectionEventLogic.OnWeaponSkinApplied = function(res)
    if res and res.result == 0 then
        local skinApplied = {}
        local itemMainType
        local itemSubType
        for index, propChange in ipairs(res.changes.prop_changes) do
            itemMainType = ItemHelperTool.GetMainTypeById(propChange.prop.id)
            itemSubType = ItemHelperTool.GetSubTypeById(propChange.prop.id)
            if propChange.type == PropChangeType.Modify and itemMainType == EItemType.WeaponSkin and itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                skinApplied[propChange.prop.id] = true
            end
        end
        for index, setup in ipairs(res.changes.weapon_skin_setup) do
            itemSubType = ItemHelperTool.GetSubTypeById(setup.skin_id)
            if setup.skin_id > 0 and itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                skinApplied[setup.skin_id] = true
            end
        end
        for index, propChange in ipairs(res.mp_change.prop_changes) do
            itemSubType = ItemHelperTool.GetSubTypeById(propChange.prop.weapon.skin_id)
            if propChange.type == PropChangeType.Modify and propChange.prop.weapon.skin_id > 0 and itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                skinApplied[propChange.prop.weapon.skin_id] = true
            end
        end
        local allMysticalSkinInstanceList = Module.Collection.Field:GetAllMysticalSkinInstanceList()
        local allSkinListToClear = {}
        for skinId, value in pairs(skinApplied) do
            for skinListId, skinInstanceList in pairs(allMysticalSkinInstanceList) do
                if CollectionLogic.GetBaseWeaponIdFromSkinId(skinId) == CollectionLogic.GetBaseWeaponIdFromSkinId(skinListId) then
                    Module.Collection.Field:SetSkinList()
                    table.insert(allSkinListToClear, skinListId)
                end
            end
        end
        for index, skinListId in ipairs(allSkinListToClear) do
            Module.Collection.Field:SetMysticalSkinInstanceList(skinListId, nil)
        end
    end
end

CollectionEventLogic.OnOpenBlindBoxWithItemId = function(itemId)
    --更新藏品曼德尔砖页签红点
    local mandelItem = Server.CollectionServer:GetCollectionItemById(itemId)
    if not mandelItem then
        Module.Collection:RemoveRedDots({itemId})
    end
end

return CollectionEventLogic
