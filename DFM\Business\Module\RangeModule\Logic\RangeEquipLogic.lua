----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRange)
----- LOG FUNCTION AUTO GENERATE END -----------


local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local RangeConfig = require "DFM.Business.Module.RangeModule.RangeConfig"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local IrisSafeHouseEquipLogic = require "DFM.Business.Module.IrisSafeHouseModule.Logic.IrisSafeHouseEquipLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local RangeUtil = require "DFM.Business.Module.RangeModule.Logic.RangeUtil"
local RangeSaveLoadLogic = require "DFM.Business.Module.RangeModule.Logic.RangeSaveLoadLogic"

local ESwitchWeaponContext = import "ESwitchWeaponContext"
local UAmmoDataManager = import "AmmoDataManager"
local FEquipmentInfo = import "EquipmentInfo"
local USafeHouseRangeUtil = import "SafeHouseRangeUtil"
local EWeaponConsumeAmmoType = import "EWeaponConsumeAmmoType"

local function log(...)
    loginfo("[RangeEquipLogic]", ...)
end

local RangeEquipLogic = {}

function RangeEquipLogic.ResetData()
    ---@type table<number, number>
    RangeEquipLogic.weaponAmmoMapping = {}
    ---@type table<number, ItemBase>
    RangeEquipLogic.allAvailableWeapons = {}
    RangeEquipLogic.cacheWeaponAmmoLists = {}
    RangeEquipLogic.bWeaponDirty = false
    RangeEquipLogic.lastSetWeaponSlotType = nil

    Server.InventoryServer:ClearSlotGroup(RangeEquipLogic.GetRangeSlotGroup())
end

function RangeEquipLogic.GetRangeSlotTypes()
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        return RangeConfig.RangeReferenceSlotTypes
    else
        return RangeConfig.RangeReferenceMPSlotTypes
    end
end

function RangeEquipLogic.GetRangeSlotGroup()
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        return RangeConfig.RangeReferenceSlotGroup
    else
        return RangeConfig.RangeReferenceMPSlotGroup
    end
end

function RangeEquipLogic.InitPlayer(context)
    -- if IsStandalone() then
    --     return
    -- end

    local weaponProp = context.weaponProp
    local currentMPBagID = context.currentMPBagID
    local selectHeroId = context.selectHeroId
    
    RangeEquipLogic.ResetData()
    local switchToWeaponType = RangeEquipLogic.InitFakeItems(weaponProp, currentMPBagID)
    
    RangeEquipLogic.InitWeapon(switchToWeaponType)
    RangeEquipLogic.InitAmmos(true)
    RangeEquipLogic.InitEquipment()
    RangeEquipLogic.InitCharacter(selectHeroId)
    RangeEquipLogic.InitState()
end

function RangeEquipLogic.Reset3DSafehousePlayer()
    IrisSafeHouseEquipLogic.InitCharacterEquip(true)
end

function RangeEquipLogic.MarkWeaponDirty()
    RangeEquipLogic.bWeaponDirty = true
end

function RangeEquipLogic.OnRangeAddOrUpdateAssembleWeapon(weaponGid)
    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local slot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        local weaponItem = slot and slot:GetEquipItem()
        if weaponItem and weaponItem.gid == weaponGid then
            RangeEquipLogic.MarkWeaponDirty()
        end
    end
end

function RangeEquipLogic.OnEnterRangeSubStage(bEnter)
    if bEnter then
        RangeEquipLogic.ClearFirstActiveWeapons()
        if RangeEquipLogic.bWeaponDirty then
            RangeEquipLogic.bWeaponDirty = false

            local switchToWeaponType = nil
            if RangeEquipLogic.lastSetWeaponSlotType then
                local slot = Server.InventoryServer:GetSlot(RangeEquipLogic.lastSetWeaponSlotType, RangeEquipLogic.GetRangeSlotGroup())
                switchToWeaponType = slot:GetEquipItem() and RangeEquipLogic.lastSetWeaponSlotType
                RangeEquipLogic.lastSetWeaponSlotType = nil
            end
            if not switchToWeaponType then
                local currentPlayerWeapon = RangeEquipLogic.GetCurrentPlayerWeaponItem(true)
                switchToWeaponType = currentPlayerWeapon and currentPlayerWeapon.InSlot.SlotType
            end
            local character = InGameController:Get():GetGPCharacter()
            USafeHouseRangeUtil.SetWeaponAction(character, true)
            RangeEquipLogic.InitWeapon(switchToWeaponType)
            RangeEquipLogic.InitAmmos(false)
        else
            RangeEquipLogic.CheackAndPlayFirstEquipAnim()
        end
    else
        local character = InGameController:Get():GetGPCharacter()
        if isvalid(character) then
            USafeHouseRangeUtil.SetWeaponAction(character, false)
            USafeHouseRangeUtil.CancelFirstEquipAnim(character)
            local weapon = character:GetRealWeapon()
            if weapon then
                weapon:WeaponCancelReload()
            end

        end
    end
end

local function fAddGid(components)
    for _, component in ipairs(components) do
        if component.prop_data.gid == 0 then
            component.prop_data.gid = GetGid()
        end

        fAddGid(component.prop_data.components)
    end
end

function RangeEquipLogic.CreateItemByProp(propInfo)
    local cloneProp = clone(propInfo)
    local gid = propInfo.gid
    if gid == 0 then
        cloneProp.gid = GetGid()
    end

    local newItem = ItemBase:New(cloneProp.id, cloneProp.num, cloneProp.gid)
    newItem:SetRawPropInfo(cloneProp)
    -- fAddGid(newItem.components)

    return newItem
end

function RangeEquipLogic.InitFakeItems(weaponProp, currentMPBagID)
    local switchToWeaponType = nil
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        switchToWeaponType = RangeEquipLogic.InitFakeItems_SOL(weaponProp)
    else
        switchToWeaponType = RangeEquipLogic.InitFakeItems_MP(weaponProp, currentMPBagID)
    end
    return switchToWeaponType
end

---@param weaponProp pb_PropInfo
function RangeEquipLogic.InitFakeItems_SOL(weaponProp)
    local switchToWeaponType = nil
    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local targetSlot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        targetSlot:ResetSlot()

        local sourceSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
        local equipItem = sourceSlot:GetEquipItem()

        if equipItem then
            -- local cloneProp = clone(equipItem.rawPropInfo)
            local newItem = RangeEquipLogic.CreateItemByProp(equipItem.rawPropInfo)

            targetSlot:AddItem(newItem)
            Server.InventoryServer:AddItemToList(newItem, targetSlot)

            if newItem.itemMainType == EItemType.Receiver and newItem.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                RangeEquipLogic.allAvailableWeapons[newItem.gid] = newItem
            end

            if switchToWeaponType == nil then
                switchToWeaponType = slotType
            end
        end
    end

    local allDepositIds = Server.InventoryServer:GetAllDepositIds()
    local depositSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer, RangeEquipLogic.GetRangeSlotGroup())
    depositSlot:ResetSlot()
    depositSlot:ManualySetSlotSize(10, 999)
    for _, depositId in ipairs(allDepositIds) do
        local slot = Server.InventoryServer:GetSlot(depositId, ESlotGroup.Player)
        if slot then
            local allItems = slot:GetItems()
            for _, item in ipairs(allItems) do
                if item.itemMainType == EItemType.Receiver then
                    -- local cloneProp = clone(item.rawPropInfo)
                    local newItem = RangeEquipLogic.CreateItemByProp(item.rawPropInfo)
        
                    depositSlot:AddItem(newItem)
                    Server.InventoryServer:AddItemToList(newItem, depositSlot)

                    if newItem.itemMainType == EItemType.Receiver and newItem.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                        RangeEquipLogic.allAvailableWeapons[newItem.gid] = newItem
                    end
                end
            end
        end
    end

    -- 改枪带入的武器
    -- 如果是主手枪，替换掉原先主手的位置
    -- 如果是手枪，替换掉原手枪的位置
    if weaponProp then
        local id = weaponProp.id
        local subType = ItemHelperTool.GetSubTypeById(id)
        local targetSlotType = nil
        if subType == ItemConfig.EWeaponItemType.Pistol then
            targetSlotType = ESlotType.Pistrol
        else
            targetSlotType = ESlotType.MainWeaponLeft
        end
        switchToWeaponType = targetSlotType

        local slot = Server.InventoryServer:GetSlot(targetSlotType, RangeEquipLogic.GetRangeSlotGroup())
        local equipItem = slot:GetEquipItem()

        if equipItem then
            slot:RemoveItem(equipItem)

            -- 但保留该预设
            -- RangeEquipLogic.allAvailableWeapons[equipItem.gid] = nil

            depositSlot:AddItem(equipItem)
            Server.InventoryServer:AddItemToList(equipItem, depositSlot)
        end

        local newWeapon = RangeEquipLogic.CreateItemByProp(weaponProp)
        slot:AddItem(newWeapon)
        Server.InventoryServer:AddItemToList(newWeapon, slot)
        RangeEquipLogic.allAvailableWeapons[newWeapon.gid] = newWeapon
    end

    return switchToWeaponType
end

function RangeEquipLogic.InitFakeItems_MP(weaponProp, currentMPBagID)
    local switchToWeaponType = nil
    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local MPPresetItem = Server.ArmedForceServer:GetMPPresetItemByBagIdAndSlotType(currentMPBagID, slotType)
        local targetSlot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        if MPPresetItem then
            local newItem = RangeEquipLogic.CreateItemByProp(MPPresetItem.rawPropInfo)

            targetSlot:AddItem(newItem)
            Server.InventoryServer:AddItemToList(newItem, targetSlot)

            if newItem.itemMainType == EItemType.Receiver and newItem.itemSubType ~= ItemConfig.EWeaponItemType.Melee then
                RangeEquipLogic.allAvailableWeapons[newItem.gid] = newItem
            end

            if switchToWeaponType == nil then
                switchToWeaponType = slotType
            end
        end
    end

    -- 改枪带入的武器
    -- 如果是主手枪，替换掉原先主手的位置
    -- 如果是手枪，替换掉原手枪的位置
    if weaponProp then
        local id = weaponProp.id
        local subType = ItemHelperTool.GetSubTypeById(id)
        local targetSlotType = nil
        if subType == ItemConfig.EWeaponItemType.Pistol then
            targetSlotType = ESlotType.MP_SecondaryWeapon
        else
            targetSlotType = ESlotType.MP_MainWeapon
        end
        switchToWeaponType = targetSlotType

        local slot = Server.InventoryServer:GetSlot(targetSlotType, RangeEquipLogic.GetRangeSlotGroup())
        local equipItem = slot:GetEquipItem()

        if equipItem then
            slot:RemoveItem(equipItem)
            RangeEquipLogic.allAvailableWeapons[equipItem.gid] = nil
        end

        local newWeapon = RangeEquipLogic.CreateItemByProp(weaponProp)
        slot:AddItem(newWeapon)
        Server.InventoryServer:AddItemToList(newWeapon, slot)
        RangeEquipLogic.allAvailableWeapons[newWeapon.gid] = newWeapon
    end

    return switchToWeaponType
end

function RangeEquipLogic.InitWeapon(switchToWeaponType)
    log("InitWeapon", switchToWeaponType)

    local character = InGameController:Get():GetGPCharacter()
    local weaponCmp = UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.WeaponManagerComponent)

    weaponCmp:RemoveAllWeapon(true)

    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local targetSlot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        if targetSlot:IsWeaponSlot() then
            local weapon = targetSlot:GetEquipItem()
            if weapon then
                -- 武器配件依赖有一个GUID
                fAddGid(weapon.components)

                local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(weapon.rawPropInfo)
                -- local weaponDesc = weapon:GetRawDescObj()
                if weaponCmp:BP_AddWeaponDesc(weaponDesc, weapon.gid, slotType) then
                    local newWeapon = weaponCmp:GetWeaponByPosition(slotType)
                    if newWeapon then
                        if Server.IrisSafeHouseServer.bUnlimitedAmmo then
                            -- 无限弹药
                            newWeapon:SetAmmoConumeType(EWeaponConsumeAmmoType.RloadFreeAmmo)
                        else
                            newWeapon:SetAmmoConumeType(EWeaponConsumeAmmoType.Normal)
                        end
                        -- 试用MiniWorld的材质变体
                        USafeHouseRangeUtil.SetupRangeWeapon(newWeapon)
                    end
                end

                if switchToWeaponType == nil then
                    switchToWeaponType = slotType
                end
            end
        end
    end

    if switchToWeaponType then
        -- 避免还有杂七杂八的动画同步问题，这里延迟一点点
        Timer.DelayCall(0.2,function ()
            USafeHouseRangeUtil.SwitchToWeapon(character, switchToWeaponType)
        end)
    end

end

function RangeEquipLogic.InitEquipment()
    local character = InGameController:Get():GetGPCharacter()
    local equipCmp = UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.CharacterEquipComponent)

    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local targetSlot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        if targetSlot:IsEquipmentSlot() or targetSlot:IsEquipContainerSlot() then
            local equipmentType = ESlotTypeToEEquipmentType[slotType]
            local equipment = targetSlot:GetEquipItem()
            if equipment then
                local equipInfo = FEquipmentInfo()
                equipInfo.ItemId = equipment.id
                equipCmp:DoServerEquip(equipmentType, equipInfo)
            else
                equipCmp:DoServerUnequip(equipmentType)
            end
        end
    end
end

function RangeEquipLogic.InitCharacter(selectHeroId)
    local character = InGameController:Get():GetGPCharacter()
    local heroId = tonumber(Server.HeroServer:GetCurUsedHeroId())
    if selectHeroId then
        heroId = selectHeroId
    end
    local fashionAvatarId = Server.HeroServer:GetFashionSuitIdByHeroId(heroId)
    local bDisplayFashion = Server.HeroServer:GetFashionPriorByCategory(FashionPriorCategory.FashionSOLHouse)
    
    character.CharacterFashionComponent:UpdateUsingHeroFashionData(heroId, fashionAvatarId, bDisplayFashion, bDisplayFashion, bDisplayFashion)
    character.CharacterFashionComponent:UpdateUsingHeroFashionWatchData(heroId, Server.HeroServer:GetUsedWatchAccessory(fashionAvatarId))

    character.CharacterFashionComponent.currentCharacterHeroID = heroId
    character.CharacterFashionComponent:OnRep_CharacterAvatarID()

    local playerState = InGameController:Get():GetPlayerState()
    local playerName = Server.RoleInfoServer.nickName or "Default"
    playerState:SetPlayerName(playerName)
    
    playerState.HeroID = heroId

    local heroInfo = ItemConfigTool.GetHeroInfoById(heroId)
    if heroInfo then
        local armId = heroInfo.ArmedForceId
        character.Blackboard.CurrentArmdedForce = armId
    
        log("InitCharacter", heroId, armId)
    else
        logwarning(string.format("[RangeEquipLogic]InitCharacter failed, heroId(%d) not found", heroId))
    end
end

function RangeEquipLogic.InitState()
    local character = InGameController:Get():GetGPCharacter()
    character:PlayerWantStand()
end

function RangeEquipLogic.GetAmmoListForWeapon(weaponId)
    local list = RangeEquipLogic.cacheWeaponAmmoLists[weaponId]
    if list then
        return list
    end

    list = {}
    RangeEquipLogic.cacheWeaponAmmoLists[weaponId] = list

    local ammoMgr = UAmmoDataManager.Get()
    local ammoDataTable = Facade.TableManager:GetTable("WeaponPart/AmmoDataTable")

    for _, row in pairs(ammoDataTable) do
        local itemId = row.ItemId
        local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
        if itemSubType > 9 then
            if ammoMgr:IsMatchWeapon(weaponId, itemId) and ItemConfigTool.GetItemConfigById(itemId) then
                table.insert(list, itemId)
            end
        end
    end

    -- sorted by quality
    table.sort(list, function(lhs, rhs)
        local lConfig = ItemConfigTool.GetItemConfigById(lhs)
        local rConfig = ItemConfigTool.GetItemConfigById(rhs)
        if lConfig.Quality ~= rConfig.Quality then
            return lConfig.Quality < rConfig.Quality
        end

        return lhs < rhs
    end)

    return list
end

function RangeEquipLogic.GetDefaultAmmoForWeapon(weaponId)
    local loadAmmoId = RangeSaveLoadLogic.LoadWeaponAmmo(weaponId)
    if loadAmmoId then
        return loadAmmoId
    end

    local ammoList = RangeEquipLogic.GetAmmoListForWeapon(weaponId)
    if #ammoList > 0 then
        return ammoList[1]
    end

    return -1
end

function RangeEquipLogic.InitAmmos(bReset)
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        RangeEquipLogic.InitAmmos_SOL(bReset)
    else
        RangeEquipLogic.InitAmmos_MP(bReset)
    end
end

function RangeEquipLogic.InitAmmos_SOL(bReset)
    local allAmmoId = {}
    if bReset then
        RangeEquipLogic.weaponAmmoMapping = {}
    end

    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local targetSlot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        if targetSlot:IsWeaponSlot() then
            local weapon = targetSlot:GetEquipItem()
            if weapon then
                local ammoId = RangeEquipLogic.weaponAmmoMapping[weapon.gid]
                if ammoId then
                    -- 有当前选中的子弹
                    table.insert(allAmmoId, ammoId)
                else
                    -- 没有当前选中的子弹
                    ammoId = RangeEquipLogic.GetDefaultAmmoForWeapon(weapon.id)
                    if ammoId > 0 then
                        RangeEquipLogic.weaponAmmoMapping[weapon.gid] = ammoId
                        table.insert(allAmmoId, ammoId)
                    end
                end
            end
        end
    end

    local character = InGameController:Get():GetGPCharacter()
    local invMgr = InGameController:Get():GetGPCharacterInventoryMgr()
    local weaponCmp = UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.WeaponManagerComponent)
    USafeHouseRangeUtil.InitPlayerAmmo_SOL(invMgr, weaponCmp, RangeEquipLogic.weaponAmmoMapping, Server.IrisSafeHouseServer.bUnlimitedAmmo)
end

function RangeEquipLogic.InitAmmos_MP(bReset)
    local character = InGameController:Get():GetGPCharacter()
    local weaponCmp = UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.WeaponManagerComponent)
    USafeHouseRangeUtil.InitPlayerAmmo_MP(weaponCmp, Server.IrisSafeHouseServer.bUnlimitedAmmo)
end

function RangeEquipLogic.OnPlayerReload(bEnter)
    if bEnter then
        return
    end

    if Server.IrisSafeHouseServer.bRangeSOLMode then
        local function fRecoverPlayerAmmo_SOL()
            local invMgr = InGameController:Get():GetGPCharacterInventoryMgr()
            USafeHouseRangeUtil.RecoverPlayerAmmo_SOL(invMgr)
        end
        Timer.DelayCall(0, fRecoverPlayerAmmo_SOL)
    else

    end
end

function RangeEquipLogic.GetCurrentPlayerWeaponItem(bIgnoreMeleeWeapon)
    local character = InGameController:Get():GetGPCharacter()
    local weaponCmp = character and UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.WeaponManagerComponent)
    if not weaponCmp then
        return nil
    end

    local curWeapon = weaponCmp:BP_GetCurrentWeapon()
    if not curWeapon then
        return nil
    end

    local weaponGid = curWeapon:BP_GetWeaponGuid()
    local weaponItem = Server.InventoryServer:GetItemByGid(weaponGid, RangeEquipLogic.GetRangeSlotGroup())

    if weaponItem and bIgnoreMeleeWeapon then
        if weaponItem.itemSubType == ItemConfig.EWeaponItemType.Melee then
            return nil
        end
    end

    return weaponItem
end

-----------------------------------------------------------------------
--region Set Equip API

function RangeEquipLogic.GetCurrentWeaponAmmo(weapon)
    return RangeEquipLogic.weaponAmmoMapping[weapon.gid] or 0
end

function RangeEquipLogic.SetAmmoForWeapon(ammoId, weaponSlotType)
    if Server.IrisSafeHouseServer.bRangeSOLMode then
        local weaponSlot = Server.InventoryServer:GetSlot(weaponSlotType, RangeEquipLogic.GetRangeSlotGroup())
        local weapon = weaponSlot:GetEquipItem()
        if not weapon then
            logwarning("RangeEquipLogic.SetAmmoForWeapon not equip weapon at slot", weaponSlot:GetSlotName())
        end
    
        local ammoMgr = UAmmoDataManager.Get()
        if not ammoMgr:IsMatchWeapon(weapon.id, ammoId) then
            logwarning(string.format("RangeEquipLogic.SetAmmoForWeapon weapon(%d) ammo(%d) not match.", weapon.id, ammoId))
        end

        RangeEquipLogic.weaponAmmoMapping[weapon.gid] = ammoId
        RangeSaveLoadLogic.SaveWeaponAmmo(weapon.id, ammoId)
        RangeEquipLogic.InitAmmos(false)
    else
        logwarning("RangeEquipLogic.SetAmmoForWeapon not allowed in mp mode.")
    end
end

function RangeEquipLogic.SetWeapon(weapon, weaponSlotType)
    local weaponSlot = Server.InventoryServer:GetSlot(weaponSlotType, RangeEquipLogic.GetRangeSlotGroup())
    local equipWeapon = weaponSlot:GetEquipItem()
    if equipWeapon then
        weaponSlot:RemoveItem(equipWeapon)
        local depositSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer, RangeEquipLogic.GetRangeSlotGroup())
        depositSlot:AddItem(equipWeapon)

        RangeEquipLogic.weaponAmmoMapping[equipWeapon.gid] = nil
    end

    if weapon then
        RangeEquipLogic.allAvailableWeapons[weapon.gid] = weapon
        weaponSlot:AddItem(weapon)
        Server.InventoryServer:AddItemToList(weapon, weaponSlot)

        RangeEquipLogic.lastSetWeaponSlotType = weaponSlotType
    end

    RangeEquipLogic.InitAmmos(false)

    RangeEquipLogic.MarkWeaponDirty()

    -- 更新角色展示
    Module.LobbyDisplay:ForceUpdateRangeDisplay()
end

function RangeEquipLogic.SwitchMainWeapons()
    local leftWeaponSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, RangeEquipLogic.GetRangeSlotGroup())
    local rightWeaponSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, RangeEquipLogic.GetRangeSlotGroup())
    local leftWeapon = leftWeaponSlot:GetEquipItem()
    local rightWeapon = rightWeaponSlot:GetEquipItem()
    local bSuccess = false
    if leftWeapon then
        leftWeaponSlot:RemoveItem(leftWeapon)
        rightWeaponSlot:AddItem(leftWeapon)
        bSuccess = true
    end
    if rightWeapon then
        rightWeaponSlot:RemoveItem(rightWeapon)
        leftWeaponSlot:AddItem(rightWeapon)
        bSuccess = true
    end

    if bSuccess then
        RangeEquipLogic.MarkWeaponDirty()
    end

    return bSuccess
end

function RangeEquipLogic.GetAllAvailableWeapons()
    return RangeEquipLogic.allAvailableWeapons
end

function RangeEquipLogic.AddAssembleWeapon(gid, propInfo)
    Server.InventoryServer:OverridePropInfoSkinInfosChecked(propInfo)
    local weaponItem = RangeEquipLogic.allAvailableWeapons[gid]
    if weaponItem then
        weaponItem:SetRawPropInfo(propInfo)
    else
        weaponItem = RangeEquipLogic.CreateItemByProp(propInfo)
    end

    RangeEquipLogic.allAvailableWeapons[weaponItem.gid] = weaponItem

    return weaponItem
end

function RangeEquipLogic.OpenArmedForceView(slotType, slotGroupId)
    Module.ArmedForce.Config.evtSlotViewClicked:Invoke(slotType, slotGroupId)
end

--endregion
-----------------------------------------------------------------------

function RangeEquipLogic.GetAllEquip()
    local equipments = {}
    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local slot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        equipments[slotType] = slot:GetEquipItem()
    end
    return equipments
end

function RangeEquipLogic.ShowRangeEquipProcess()
    -- 如果正在瞄准，退出该状态
    RangeUtil.TryCancelAiming()

    local equipPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.RangeEquipView);
    Module.Range.Field:SetEquipPanelHandle(equipPanelHandle)
end


function RangeEquipLogic.SetUnlimitedAmmo(bUnlimitedAmmo)
    if Server.IrisSafeHouseServer.bUnlimitedAmmo ~= bUnlimitedAmmo then
        Server.IrisSafeHouseServer.bUnlimitedAmmo = bUnlimitedAmmo

        local switchToWeaponType = nil
        if RangeEquipLogic.lastSetWeaponSlotType then
            local slot = Server.InventoryServer:GetSlot(RangeEquipLogic.lastSetWeaponSlotType, RangeEquipLogic.GetRangeSlotGroup())
            switchToWeaponType = slot:GetEquipItem() and RangeEquipLogic.lastSetWeaponSlotType
            RangeEquipLogic.lastSetWeaponSlotType = nil
        end
        if not switchToWeaponType then
            local currentPlayerWeapon = RangeEquipLogic.GetCurrentPlayerWeaponItem(true)
            switchToWeaponType = currentPlayerWeapon and currentPlayerWeapon.InSlot.SlotType
        end
        -- RangeEquipLogic.InitWeapon(switchToWeaponType)
        RangeEquipLogic.SetWeaponConumeType()

        RangeEquipLogic.InitAmmos(false)
    end
end

function RangeEquipLogic.SetWeaponConumeType()
    local character = InGameController:Get():GetGPCharacter()
    local weaponCmp = UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.WeaponManagerComponent)

    for _, slotType in ipairs(RangeEquipLogic.GetRangeSlotTypes()) do
        local targetSlot = Server.InventoryServer:GetSlot(slotType, RangeEquipLogic.GetRangeSlotGroup())
        if targetSlot:IsWeaponSlot() then
            local newWeapon = weaponCmp:GetWeaponByPosition(slotType)
            if newWeapon then
                if Server.IrisSafeHouseServer.bUnlimitedAmmo then
                    -- 无限弹药
                    newWeapon:SetAmmoConumeType(EWeaponConsumeAmmoType.RloadFreeAmmo)
                else
                    newWeapon:SetAmmoConumeType(EWeaponConsumeAmmoType.Normal)
                end
            end
        end
    end
end

function RangeEquipLogic.CheackAndPlayFirstEquipAnim()
    local character = InGameController:Get():GetGPCharacter()
    if isvalid(character) then
        Timer.DelayCall(0.2,function ()
            USafeHouseRangeUtil.SetWeaponAction(character, true)
            RangeUtil.TryCancelAiming()
            USafeHouseRangeUtil.PlayFirstEquipAnim(character)
        end)
    end
end

function RangeEquipLogic.ClearFirstActiveWeapons()
    local character = InGameController:Get():GetGPCharacter()
    if isvalid(character) then
        USafeHouseRangeUtil.ClearFirstActiveWeapons(character)
    end
end


return RangeEquipLogic