----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------


local RoleInfoPersonal = ui("RoleInfoPersonal")
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

-- Console 查看用户档案
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
local UDFMPlatformUtils = import "DFMPlatformUtils"
--- END MODIFICATION

---@class RoleInfoPersonal : LuaUIBaseView

function RoleInfoPersonal:Ctor()
    for i = 1, 6 do
        self["_wtRoleInfoData" .. i] = self:Wnd("wtRoleInfoData" .. i, UIWidgetBase):Wnd("wtRoleInfoText", UITextBlock)
    end
    self._wtBadgeDisplayTbl = {}
    for index = 1, 8 do
        self._wtBadgeDisplayTbl[index] = self:Wnd("wtBadgeDisplay_" .. index, UIWidgetBase)
    end
    self._wtPersonalInformation = self:Wnd("wtRoleInfoData3", UIWidgetBase)
    self._wtPraisedNum = self:Wnd("wtPraisedNum", UITextBlock)
    self._wtPlayerId = self:Wnd("wtPlayerId", UITextBlock)
    self._wtLevelDesc = self:Wnd("DFCommonTextBlock_0", UITextBlock)
    self._wtNameCard = self:Wnd("wtNameCard", UIWidgetBase)
    self._wtPlayerIcon = self._wtNameCard:Wnd("wtPlayerIcon", UIWidgetBase)
    self._wtNickName = self._wtNameCard:Wnd("wtNickName", UITextBlock)
    self._wtChangeNamePanel = self._wtNameCard:Wnd("DFCanvasPanel_1", UIWidgetBase)
    self._wtChangeNameBtn = self._wtNameCard:Wnd("wtLevelBtn", UIWidgetBase)
    self._wtChangeNameBtn:Event("OnClicked", self._RenameBtnOnClick, self)
    self._wtMilitaryImage = self._wtNameCard:Wnd("DFImage_201", UIImage)
    self._wtOpenHead = self._wtNameCard:Wnd("wtOpenHead", UIButton)
    self._wtOpenMilitary = self._wtNameCard:Wnd("wtOpenMilitary", UIButton)
    ---@type RoleInfoTitleItem_Small
    self._wtTitle = self._wtNameCard:Wnd("WBP_RoleInfo_TitleItem_Small", UIWidgetBase)
    self._wtSeasonText = self:Wnd("DFTextBlock_250", UITextBlock)
    self._wtTopTxt = self:Wnd("DFTextBlock_705", UITextBlock)
    self._wtWxPrivilege = self._wtNameCard:Wnd("DFImage_Privilege", UIButton)
    self._wtLevelIcon = self:Wnd("wtLevelIcon", UIWidgetBase)
    self._wtLevelIconBtn = self._wtLevelIcon:Wnd("wtLevelBtn", UIButton)
    self._wtLevelItem = self._wtLevelIcon:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
    self._wtLevelItemName = self._wtLevelIcon:Wnd("wtLevelItemName", UITextBlock)
    self._wtLevelTypeIcon = self._wtLevelIcon:Wnd("DFImage", UIImage)
    self._wtRank = self:Wnd("wtRank", UIWidgetBase)
    self._wtRankIcon = self._wtRank:Wnd("wtRankIcon", UIImage)
    self._wtRankScore = self._wtRank:Wnd("DFTextBlock_452", UITextBlock)
    self._wtRankText = self._wtRank:Wnd("DFTextBlock_369", UITextBlock)
    self._wtRankStarIcon = self._wtRank:Wnd("DFImage_77", UIImage)
    self._wtRankStarNum = self._wtRank:Wnd("DFTextBlock", UITextBlock)
    self._wtBadgeChoice = self:Wnd("WBP_CommonIconButton_1", UIWidgetBase)
    self._wtBadgeChoice:Event("OnClicked", self._RoleBadgeChoiceBtnOnClick, self)
    self._wtRightChange = self:Wnd("wtRightChange", UIWidgetBase)
    self._wtLeftChange = self:Wnd("wtLeftChange", UIWidgetBase)
    self._wtRightChange:Event("OnClicked", self._ChangeMode, self)
    self._wtLeftChange:Event("OnClicked", self._ChangeMode, self)

    self._wtBtnChange = self:Wnd("WBP_CommonIconButton_2", DFCommonButtonOnly)
    self._wtBtnChange:Event("OnClicked", self.OnClickChange, self)

    self._wtReportBtn = self:Wnd("wtReportBtn", UIWidgetBase)
    self._wtAddFriendBtn = self:Wnd("wtAddFriendBtn", UIWidgetBase)
    self._wtWinnerBtn = self:Wnd("wtRankDisplay", UIWidgetBase)
    self._wtWinnerBtn:Event("OnClicked", self.OnClickWinner, self)
    self._wtSaveModelBtn = self:Wnd("wtSaveModelBtn", UIWidgetBase)
    self._wtSaveModelBtn:Event("OnClicked", self.SaveSelfModel, self)

    self._wtRefreshModelBtn = self:Wnd("wtRefreshModelBtn", UIWidgetBase)
    self._wtRefreshModelBtn:Event("OnClicked", self.RefreshSelfModel, self)

    self._wtPraise = self:Wnd("wtPraiseBtn", UIWidgetBase)
    self._wtPraise:Event("OnClicked", self._OnPraiseClick, self)

    self._wtPrivilegePanel = self:Wnd("DFCanvasPanel_PrivilegeButton", UIWidgetBase)
    self._wtPrivilege = self:Wnd("WBP_Firend_PrivilegeButton", UIWidgetBase)
    self._wtPrivilegeBtn = self._wtPrivilege:Wnd("PrivilegeBtn", UIButton)
    self._wtSchoolPanel = self:Wnd("DFCanvasPanel_SchoolButton", UIWidgetBase)
    self._wtCareerBtn = self:Wnd("WBP_CommonIconButton", UIWidgetBase)
    self._wtCareerBtn:Event("OnClicked", self.RefreshCareerData, self)
    -- 截屏分享相关
    self._wtAntherPanel = self:Wnd("wtAntherPanel", UIWidgetBase)
    self._wtShareBtn = self:Wnd("wtShareBtn", UIWidgetBase)
    self._wtShareBtn:Event("OnClicked", self.OnShareClick, self)
    self._copyBtn = self:Wnd("DFButton_55", UIButton)
    self._copyBtn:Event("OnClicked", self.OnCopyBtnClicked, self)
    self._wtAddFriendBtn:Event("OnClicked", self.OnAddFriendBtnClicked, self)
    self._wtReportBtn:Event("OnClicked", self._ReportBtnOnClick, self)


    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtSeasonPanel = self:Wnd("PlatformPaddingBox_19", UIWidgetBase)
        self._wtLevelPanel = self:Wnd("DFCanvasPanel_454", UIWidgetBase)
        self._wtBadgePanel = self:Wnd("DFCanvasPanel_533", UIWidgetBase)
        self._wtBadgeListPanel = self:Wnd("PlatformPaddingBox_24", UIWidgetBase)

        -- Initialize tab switch gamepad shortcut instructions
        self._wtTabKeyIconLeft = self:Wnd("wtTabKeyIconLeft", HDKeyIconBox)
        self._wtTabKeyIconRight = self:Wnd("wtTabKeyIconRight", HDKeyIconBox)
        self._wtTabKeyIconLeft:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconRight:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconLeft:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0, false)
        self._wtTabKeyIconRight:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0, false)
        self._wtTabKeyIconLeft:Visible()
        self._wtTabKeyIconRight:Visible()
    end

    if IsConsole() then
        self._wtPlatformProfileBtn = self:Wnd("wtPlatformProfileBtn", UIWidgetBase)
        self._wtPlatformProfileBtn:Event("OnClicked", self._PlatformProfileBtnOnClick, self)

        -- 增加平台logo
        self._wtPlatformIcon = self._wtNameCard:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION

    self:InitMyData()
end

function RoleInfoPersonal:InitMyData()
    self._gameCenter = GameCenterType.GameCenter_None
    self._isCareer = false
    self._roleData = nil
    self.myPlayerId = Server.AccountServer:GetPlayerId()
    self.bMpStar = false -- MP 武器升星
    self.bSolRoom = false
    self._buttonTimers = {}
    self.iconType = 0
    self.showCommanderRankPoints = 0 -- 展示的段位 胜者为王 or 其他
    self.mpSeasonData = nil
    self.jumpTime = 0
end

function RoleInfoPersonal:OnInitExtraData(playerId)
    self:SetPraise(false)
    self.isMpMode = RoleInfoLogic.IsInMp()

    self._mpData = nil
    self._solData = nil
    self.mpSeasonData = nil

    self._playerId = playerId
    self._seasonLevel = 0
    self._accountLevel = 0

    self:GetModeData()
    self:SetStyle(self.isMpMode and 1 or 0)

    if self._roleData then
        self:_RefreshRolePlayerInfo(self._roleData)
    end

    --偶现id错误无法初始化问题
    loginfo("RoleInfoPersonal:OnInitExtraData playerId = ", playerId, "; myPlayerId =", self.myPlayerId)
    self:SetMyselfOrOther()
    self:ReqServerData()

    self:PlayWidgetAnim(self.WBP_RoleInfo_PersonalHomepage_switch_02)
    self._wtLevelIcon:Collapsed()

    --- BEGIN MODIFICATION @ VIRTUOS
    if PLATFORM_GEN9 == 1 and self._wtPlatformProfileBtn then
        self._wtPlatformProfileBtn:Collapsed()
    end
    --- END MODIFICATION
end

function RoleInfoPersonal:ReqServerData()
    local server = Server.RoleInfoServer

    local f = CreateCallBack(function(self, starWeapons)
        self.starWeapons = starWeapons
        self:SetDefault()
        -- self:RefreshWeaponStar()
    end, self)

    if self.starWeapons == nil then
        server:GetAllStarWeapon(self._playerId, f)
    end

    local c = CreateCallBack(function(self, res)
        self.roomData = res
        self:SetDefault()
        -- self:RefreshCollectRoom()
    end, self)

    if self.roomData == nil then
        server:ReqQueryShowRoom(self._playerId, c)
    end

    server:FetchRoleInfoByPlayerId(self._playerId)
    server:FetchGetBadgeRolePanelShow(self._playerId)
end

function RoleInfoPersonal:SetMyselfOrOther()
    if self._playerId == self.myPlayerId then
        self:_SetupForCurrentPlayer()
    else
        self:_SetupForOtherPlayer()
    end
end

function RoleInfoPersonal:_SetupForCurrentPlayer()
    self._wtPraise:HitTestInvisible()
    self._wtLevelIconBtn:RemoveEvent("OnClicked")
    self._wtLevelIconBtn:Event("OnClicked", self._OnGrowthRoadClick, self)
    self._wtLevelIconBtn:Visible()
    self._wtOpenHead:Event("OnClicked", self._OpenHeadPanelClick, self)
    self._wtOpenMilitary:Event("OnClicked", self._OpenMilitaryPanelClick, self)
    self._wtTitle:BindBtnClick(CreateCallBack(self._OpenTitlePanelClick, self))
    self._wtReportBtn:Collapsed()
    self._wtAddFriendBtn:Collapsed()
    self._wtSaveModelBtn:Collapsed()
    self._wtRefreshModelBtn:Visible()
    self._wtBadgeChoice:Visible()
    self._wtChangeNamePanel:Visible()
    self._wtChangeNameBtn:Visible()

    if not IsHD() and
        Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SubShareBasicInformation) == EFirstLockResult.Open then
        self._wtShareBtn:Visible()
    else
        self._wtShareBtn:Collapsed()
    end
end

function RoleInfoPersonal:_SetupForOtherPlayer()
    self._wtTitle:ClearBtnClick()
    self._wtLevelIconBtn:HitTestInvisible()
    self._wtLevelIconBtn:RemoveEvent("OnClicked")
    self._wtLevelIconBtn:Event("OnClicked", self._OnGrowthRoadClick, self)
    self._wtOpenHead:RemoveEvent("OnClicked")
    self._wtOpenMilitary:RemoveEvent("OnClicked")
    self._wtReportBtn:Visible()
    self._wtSaveModelBtn:Collapsed()
    self._wtRefreshModelBtn:Collapsed()
    self._wtBadgeChoice:Collapsed()
    self._wtChangeNamePanel:Collapsed()
    self._wtPrivilegePanel:Collapsed()
    self._wtChangeNameBtn:HitTestInvisible()
    self._wtShareBtn:Collapsed()
    self._wtPraise:Visible()

    if Module.Friend:CheckIsGameFriend(playerId) then
        self._wtAddFriendBtn:Collapsed()
    end
end

function RoleInfoPersonal:SetSelfPingtai()
    self._wtPrivilegeBtn:RemoveEvent("OnClicked")
    if self:IsMySelf() then
        local channelId = Server.SDKInfoServer:GetChannel()
        if channelId == EChannelType.kChannelWechat then
            if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.WeChatPrivilegePersonalHomepageDisplay) ~=
                EFirstLockResult.Open then
                self:CollapsedPingtai()
                return
            end
            self._wtPrivilegeBtn:Event("OnClicked", self._OpenWxH5, self)
            self._wtPrivilegePanel:Visible()
            self._wtPrivilege:SetType(0)
            if self._gameCenter == GameCenterType.GameCenter_WX then
                self._wtWxPrivilege:Visible()
            else
                self._wtWxPrivilege:Collapsed()
            end
        elseif channelId == EChannelType.kChannelQQ then
            if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.QQPrivilegePersonalHomepageDisplay) ~=
                EFirstLockResult.Open then
                self:CollapsedPingtai()
                return
            end
            self._wtPrivilegeBtn:Event("OnClicked", self._OpenQQH5, self)
            self._wtPrivilegePanel:Visible()
            local launchChannelId = Server.SDKInfoServer:GetLaunchChannelId()
            if self._gameCenter == GameCenterType.GameCenter_QQ then
                self._wtPrivilege:SetType(2)
            else
                self._wtPrivilege:SetType(3)
            end
        end
    else
        self:CollapsedPingtai()
    end
end

function RoleInfoPersonal:CollapsedPingtai()
    self._wtWxPrivilege:Collapsed()
    self._wtPrivilegePanel:Collapsed()
    --- BEGIN MODIFICATION @ VIRTUOS: Console 查看用户档案
    if PLATFORM_GEN9 == 1 then
        self:CreateProfileBtn()
    end
    --- END MODIFICATION
end

function RoleInfoPersonal:SetSelfSchool()
    if self:IsMySelf() and self._stuPriv == StuPrivType.StuPriv_Certified then
        self._wtSchoolPanel:Visible()
        self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._OnHover, self._OnUnhovered)
    else
        self._wtSchoolPanel:Collapsed()
    end
end

function RoleInfoPersonal:_OnHover()
    if self._wtDFTipsAnchor then
        if not self.hoverHandle then
            local Loc = Module.RoleInfo.Config.Loc
            local buffNum = DFMGlobalConst.GetGlobalConstNumber("UniversitySettlementBuff", 0.02)
            local contant = {
                { id = UIName2ID.Assembled_CommonMessageTips_V1,
                    data = { textContent = Loc.StudentBuffTitle, styleRowId = "C001",
                        fontStyleId = "Header3_28pt" } },
                { id = UIName2ID.Assembled_CommonMessageTips_V5, data = { textContent = Loc.StudentBuff } },
                { id = UIName2ID.Assembled_CommonMessageTips_V5,
                    data = { textContent = string.format(Loc.StudentBuffExp, buffNum * 100) } }
            }
            self.hoverHandle = Module.CommonTips:ShowAssembledTips(contant, self._wtDFTipsAnchor)
        end
    end
end

function RoleInfoPersonal:_OnUnhovered()
    if self.hoverHandle and self._wtDFTipsAnchor then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

function RoleInfoPersonal:AddListener()
    local serverEvents = Server.RoleInfoServer.Events

    self:AddLuaEvent(serverEvents.evtRefreshRoleInfoPanel, self._RefreshRolePlayerInfo, self)
    self:AddLuaEvent(serverEvents.evtRefreshSolDate, self._RefreshSolData, self)
    self:AddLuaEvent(serverEvents.evtRefreshMPDate, self._RefreshMPData, self)
    self:AddLuaEvent(serverEvents.evtRolePanelBadgeShow, self._RefreshBadgeList, self)
    self:AddLuaEvent(serverEvents.evtGetBadgeShow, self._RefreshBadgeList, self)
    self:AddLuaEvent(serverEvents.evtSocialAvatarChange, self.RefreshWinnerBtn, self)
    self:AddLuaEvent(serverEvents.evtPlayerBasicInfoUpdate, self.RefreshBasicInfo, self)
    self:AddLuaEvent(serverEvents.evtCommanderRankModeChanged, self.GetBasicInfo, self)
end

function RoleInfoPersonal:OnOpen()
    self:RefreshSeasonText()
end

function RoleInfoPersonal:OnShowBegin()
    Module.Collection:OpenReNameCard(true)
    self:AddListener()

    self:SetStyle(self.isMpMode and 1 or 0)

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    -- END MODIFICATION
end

-- BEGIN MODIFICATION @ VIRTUOS
function RoleInfoPersonal:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end

    self:RemoveAllLuaEvent()
    self.starWeapons = nil
    self.roomData = nil
    self.bMpStar = false
    self.bSolRoom = false
    for btn, timer in pairs(self._buttonTimers) do
        timer:Release()
        btn:SetIsEnabled(true)
    end
    self._buttonTimers = {}
end

-- END MODIFICATION

function RoleInfoPersonal:RefreshCareerData()
    self._isCareer = not self._isCareer
    self._solData = nil
    self._mpData = nil
    self.mpSeasonData = nil
    self:LimitButtonClick(self._wtCareerBtn)
    self:GetModeData()
    self:RefreshSeasonText()
end

function RoleInfoPersonal:RefreshSeasonText()
    local loc = Module.RoleInfo.Config.Loc
    local seasonText = self._isCareer and loc.SeasonAll or loc.SeasonNow
    local topText = self._isCareer and loc.CareerTop or loc.SeasonTop

    self._wtSeasonText:SetText(seasonText)
    self._wtTopTxt:SetText(topText)
end

function RoleInfoPersonal:_RefreshSolRankData()
    if self.isMpMode then
        return
    end

    if not self._solData then
        return Module.RoleInfo.Config.Loc.NoRankDataTxt
    end
    if not self._solData.rank_attended then
        self._wtRankIcon:SetRankIconNone()
        self._wtRankText:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        self._wtRankScore:Collapsed()
        self._wtRankStarIcon:Collapsed()
        self._wtRankStarNum:Collapsed()
        return Module.RoleInfo.Config.Loc.NoRankDataTxt
    end
    self._wtRankScore:Visible()
    local rankInfo = Module.Ranking:GetMinorDataByScore(self._solData.rank_score_max)
    self._wtRankIcon:SetRankingIconByScore(self._solData.rank_score_max)
    self._wtRankScore:SetText(self._solData.rank_score_max)
    self._wtRankText:SetText(rankInfo.RankName)
    self._wtRankStarIcon:SelfHitTestInvisible()
    self._wtRankStarNum:SelfHitTestInvisible()
    self._wtRankStarNum:SetText(Module.Ranking:GetStarNumByScore(self._solData.rank_score_max) or "?")
    return rankInfo.RankName
end

function RoleInfoPersonal:RefreshBasicInfo()
    self.showCommanderRankPoints = 0
    local basicInfo = Server.RoleInfoServer:GetPlayerBasicInfo()
    if basicInfo then
        self.showCommanderRankPoints = basicInfo.show_commander_rank_points
    end

    if self.mpSeasonData == nil then -- 胜者为王相关数据
        Server.RoleInfoServer:FetchMPSeasonDetailData(self._playerId,
            self._isCareer and 0 or Server.TournamentServer:GetCurSerial(), false, true,
            false, CreateCallBack(self.RefreshMPDetailData, self))
    end
end

function RoleInfoPersonal:RefreshMPDetailData(data)
    self.mpSeasonData = data
    self:_RefreshMpRankData()
end

function RoleInfoPersonal:_RefreshMpRankData()
    if self.isMpMode == false then
        return
    end

    local tournamentModule = Module.Tournament

    if self.isMpMode == false or not self._mpData then
        return tournamentModule.Config.Loc.NoneLevel
    end

    if self.showCommanderRankPoints == 1 and self.mpSeasonData == nil then
        return tournamentModule.Config.Loc.NoneLevel
    end

    local rankAttended = false
    local rankScoreMax = 0
    local maxSeasonNo = 0
    local seasonId = nil
    local rankInfo = nil

    if self.showCommanderRankPoints == 0 then
        rankAttended = self._mpData.rank_attended
        rankScoreMax = self._mpData.rank_score_max
        maxSeasonNo = self._mpData.rank_max_season_no
    else
        rankAttended = self.mpSeasonData.commander_attended
        rankScoreMax = self.mpSeasonData.commander_score_max
        maxSeasonNo = self.mpSeasonData.commander_max_season_no
    end

    if not rankAttended then
        if self.showCommanderRankPoints == 0 then
            self._wtRankIcon:SetTournamentIconNone()
        else
            self._wtRankIcon:SetCommanderIconNone()
        end

        self._wtRankText:SetText(tournamentModule.Config.Loc.NoneLevel)
        self._wtRankScore:Collapsed()
        self._wtRankStarIcon:Collapsed()
        self._wtRankStarNum:Collapsed()
        return tournamentModule.Config.Loc.NoneLevel
    end
    self._wtRankScore:Visible()
    if self._isCareer then -- 所有赛季
        seasonId = maxSeasonNo ~= 0 and maxSeasonNo or 1
    else
        seasonId = nil
    end

    if self.showCommanderRankPoints == 0 then
        rankInfo = tournamentModule:GetRankDataByScore(rankScoreMax, seasonId)
        self._wtRankIcon:SetTournamentIconByScore(rankScoreMax, seasonId)
        self._wtRankStarNum:SetText(tournamentModule:GetStarNumByScore(rankScoreMax, seasonId) or "?")
    else
        rankInfo = tournamentModule:GetCommanderRankDataByScore(rankScoreMax, seasonId)
        self._wtRankIcon:SetCommanderIconByScore(rankScoreMax, seasonId)
        self._wtRankStarNum:SetText(tournamentModule:GetCommanderStarNumByScore(rankScoreMax, seasonId) or "?")
    end

    self._wtRankScore:SetText(rankScoreMax)
    self._wtRankStarIcon:SelfHitTestInvisible()
    self._wtRankStarNum:SelfHitTestInvisible()

    if rankInfo then
        self._wtRankText:SetText(rankInfo.Name)
    end

    return rankInfo.Name
end

function RoleInfoPersonal:_RefreshBadgeList(badgeList)
    local badgeMap = {}
    for i, info in ipairs(badgeList) do
        badgeMap[info.slot] = info
    end
    for i, widget in ipairs(self._wtBadgeDisplayTbl) do
        local badge = badgeMap[i]
        if badge then
            widget:SetImg(badge.prop_id, false, badge.unlock_time)
        else
            widget:SetNoreItem()
        end
    end

    self:_InitBadgeInputs()
end

function RoleInfoPersonal:RefreshSelfModel()
    Module.RoleInfo.Config.Event.evtRefreshSelfModel:Invoke()
    self._wtSaveModelBtn:Visible()
    self._wtRefreshModelBtn:Collapsed()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:UpdateShortcutButton()
    end
    -- END MODIFICATION
end

function RoleInfoPersonal:OnClickWinner() -- 胜者为王
    if self._mpData and self.mpSeasonData then
        Facade.UIManager:AsyncShowUI(UIName2ID.CommanderRankPop, nil, nil, self._mpData.rank_score_max,
            self.mpSeasonData.commander_score_max)
    end
end

function RoleInfoPersonal:SaveSelfModel()
    local gameMode = self.isMpMode and MatchGameMode.TDMGameMode or MatchGameMode.WorldGameMode
    Server.RoleInfoServer:SavePlayerAppearance(gameMode)
    self._wtSaveModelBtn:Collapsed()
    self._wtRefreshModelBtn:Visible()
    Module.RoleInfo.Config.Event.evtSaveSelfModel:Invoke()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:UpdateShortcutButton()
    end
    -- END MODIFICATION
end

function RoleInfoPersonal:OnShareClick()
    local PreScreenshotShare = CreateCallBack(function(self)
        self:PreScreenshotShare()
    end, self)

    local AfterScreenshotShare = CreateCallBack(function(self)
        self:AfterScreenshotShare()
    end, self)

    Module.Share:ApplySimpleScreenshot(
        "RoleInfoPersonal",
        PreScreenshotShare,
        AfterScreenshotShare,
        Module.Share.Config.ESocialShareSceneEnum.RoleInfoPersonal)
end

function RoleInfoPersonal:SetMyLevelData()
    if self.bMpStar or self.bSolRoom then return end

    local nextLevel = self.isMpMode and (self._accountLevel + 1) or (self._seasonLevel + 1)
    local levelData = self.isMpMode and
        Server.GrowthRoadServer:GetMPDataByLevel(nextLevel) or
        Server.GrowthRoadServer:GetSOLDataByLevel(nextLevel)
    self:_SetNextLevelItemData(levelData)
end

function RoleInfoPersonal:_SetNextLevelItemData(itemDataTbl)
    if itemDataTbl.itemInfo then
        local itemInfo = itemDataTbl.itemInfo[1] or {}
        if itemInfo.isEmpty then
            self:SetLevelIconByLevel()
        elseif itemInfo.iconType == 1 then -- 普通icon
            self.iconType = 1
            self._wtLevelItemName:SetText(itemInfo.itemName)
            self._wtLevelTypeIcon:AsyncSetImagePath(itemInfo.iconPath, true)

            --尺寸调整
            if itemInfo and itemInfo.itemType then
                if itemInfo.isMap == 1 then
                    self._wtLevelTypeIcon:SetBrushSize(FVector2D(864, 448))
                else
                    local curX = IsHD() and 256 or 192
                    self._wtLevelTypeIcon:SetBrushSize(FVector2D(curX, curX))
                end
            end
        elseif itemInfo.iconType == 2 then -- Item
            self.iconType = 2
            self._wtLevelItem:InitItem(itemInfo.itemData)
            self._wtLevelItemName:SetText(itemInfo.itemData.name)
            if IsHD() then
                self._wtLevelItem:SetRootSize(256, 256)
            else
                self._wtLevelItem:SetRootSize(192, 192)
            end
        end
    else
        self:SetLevelIconByLevel()
    end
end

function RoleInfoPersonal:SetLevelIconByLevel()
    self.iconType = 0

    if self.bMpStar or self.bSolRoom then return end

    local Loc = Module.RoleInfo.Config.Loc
    local levelText = string.format(Loc.LevelText, self.isMpMode and self._accountLevel or self._seasonLevel)

    self._wtLevelItemName:SetText(levelText)
end

function RoleInfoPersonal:_ChangeMode()
    self.isMpMode = not self.isMpMode
    self:SetDefault()
    self:_RefreshLevel()
    self:RefreshLevelText()

    self:GetModeData()
    self:SetStyle(self.isMpMode and 1 or 0)

    self:RefreshChangeBtn()
    self:RefreshWinnerBtn()

    self:UpdateShortcutButton()
end

function RoleInfoPersonal:RefreshChangeBtn()
    if self.isMpMode then
        if self.starWeapons and next(self.starWeapons) then
            self._wtBtnChange:Visible()
        else
            self._wtBtnChange:Collapsed()
        end
    else
        if self:GetCollectionRoomOpen() then
            self._wtBtnChange:Visible()
        else
            self._wtBtnChange:Collapsed()
        end
    end

end

function RoleInfoPersonal:OnClickChange()
    self:LimitButtonClick(self._wtBtnChange)

    self:PlayWidgetAnim(self.WBP_RoleInfo_PersonalHomepage_switch_01)

    Timer.DelayCall(0.2, function()
        self:PlayWidgetAnim(self.WBP_RoleInfo_PersonalHomepage_switch_02)
    end, self)

    Timer.DelayCall(0.1, function()
        if self.isMpMode then
            self.bMpStar = not self.bMpStar
            if self.bMpStar then
                self:RefreshWeaponStar()
            end
        else
            self.bSolRoom = not self.bSolRoom
            if self.bSolRoom then
                self:RefreshCollectRoom()
            end
        end
        self:_RefreshLevel()
        self:RefreshLevelIconStyle()
        self:RefreshLevelText()
    end, self)
end

function RoleInfoPersonal:RefreshWeaponStar()
    if not self.starWeapons then
        logerror('ddh RefreshWeaponStar starWeapons is nil')
        return
    end

    self._wtLevelIcon:RefreshCarousel(self.starWeapons)
end

function RoleInfoPersonal:RefreshCollectRoom()
    if not self.roomData then
        logerror('ddh RefreshCollectRoom roomData is nil')
        return
    end

    if self.bSolRoom == false then
        return
    end

    self._wtLevelTypeIcon:AsyncSetImagePath(Module.RoleInfo.Config.CollectRoomImgPath, true)
    self._wtLevelIcon:RefreshCollectRoom(self.roomData)
end

function RoleInfoPersonal:GetModeData()
    if self.isMpMode then
        self:GetMPSeasonData()
    else
        self:GetSOLSeasonData()
    end
end

function RoleInfoPersonal:GetBasicInfo()
    self.mpSeasonData = nil
    Server.RoleInfoServer:ReqPlayerBasicInfo(self._playerId)
end

function RoleInfoPersonal:GetMPSeasonData()
    if self._mpData == nil then
        local mpMatchInfo = {}
        mpMatchInfo.game_mode = MatchGameMode.TDMGameMode
        mpMatchInfo.game_rule = 0
        Server.RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo(mpMatchInfo, self._playerId, nil, self._isCareer)
        self:GetBasicInfo()
    else
        self:_RefreshMPSeason()
    end
end

function RoleInfoPersonal:GetSOLSeasonData()
    local solMatchInfo = {}
    if self._solData == nil then
        solMatchInfo.game_mode = MatchGameMode.WorldGameMode
        solMatchInfo.game_rule = MatchGameRule.SOLGameRule
        solMatchInfo.sub_mode = 0
        Server.RoleInfoServer:FetchSolSeasonTotalDataByMatchInfo(solMatchInfo, self._playerId, self._isCareer)
    else
        self:_RefreshSolSeason()
    end
end

function RoleInfoPersonal:_RefreshRolePlayerInfo(roleData)
    self._roleData = roleData
    self._picUrl = roleData.pic_url
    self._nick = roleData.nick_name
    self._seasonLevel = roleData.level --sol等级
    self._accountLevel = roleData.account_level --mp等级
    self._militaryId = roleData.military_tag
    self._praisedNum = roleData.praised_num
    self._gameCenter = roleData.game_center
    self._stuPriv = roleData.stu_priv
    self._wtNickName:SetText(roleData.nick_name)
    local remarkName = Server.FriendServer:GetFriendRemarkById(self._playerId)
    if remarkName ~= "" then
        self._wtNickName:SetText(string.format(Module.Friend.Config.QQFriend, remarkName, roleData.nick_name))
    end
    self._wtPraisedNum:SetText(self._praisedNum)
    self._EncryptionIdStr = Module.RoleInfo:GOpenIdEncryption(ULuautils.GetUInt64String(self._playerId))
    self._wtPlayerId:SetText(Module.RoleInfo.Config.Loc.PlayerId .. self._EncryptionIdStr)
    self._wtMilitaryImage:AsyncSetImagePath(Module.RoleInfo:GetMilitary(self._militaryId), true)
    if roleData.title > 0 then
        self._wtTitle:SelfHitTestInvisible()
        self._wtTitle:UpdateByTitleId(roleData.title, roleData.rank_title_adcode, roleData.rank_title_rank_no)
    else
        self._wtTitle:Collapsed()
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and self._wtPlatformIcon then
        self._plat_id = roleData.plat_id
        local platIconPath = Module.Friend:GetPlatformIconPath(self._plat_id)
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
            self._wtPlatformIcon:SelfHitTestInvisible()
        else
            self._wtPlatformIcon:Collapsed()
        end
    end

    -- TRC: replace player name with PS5 online id. Designer and QA ask only show other people's online id
    if IsPS5Family() and roleData.plat_id and self._playerId ~= self.myPlayerId then
        local callback = function(onlineID)
            self._nick = onlineID
            self._wtNickName:SetText(onlineID)
        end
        Module.Social:AsyncGetPS5OnlineIdByUID(self._playerId, callback, self)
    end
    --- END MODIFICATION

    local playerInfo = {
        player_id = self._playerId,
        pic_url = roleData.pic_url,
    }
    self._wtPlayerIcon:InitPortrait(playerInfo, HeadIconType.HeadNore)
    self._wtPlayerIcon:HitTestInvisible()
    self._wtPlayerIcon:CollapsedLevel()

    self:SetDefault()
    self:_RefreshLevel()
    self:RefreshLevelText()
    if IsHD() then
        self:CollapsedPingtai()
    else
        self:SetSelfPingtai()
    end
    self:SetSelfSchool()
    self:RefreshWinnerBtn()
end

function RoleInfoPersonal:RefreshWinnerBtn()
    if self:IsMySelf() and RoleInfoLogic.IsInMp() and Server.TournamentServer:GetCommanderHasAttended() then
        self._wtWinnerBtn:Visible()
        self:ShowRedDot(Module.RoleInfo:GetFirstVictoryUniteRed())
    else
        self._wtWinnerBtn:Collapsed()
    end
end

function RoleInfoPersonal:ShowRedDot(bShow)
    if not self.wtRedDot then
        self.wtRedDot = Module.ReddotTrie:CreateReddotIns(self._wtWinnerBtn)
    end
    self.wtRedDot:SetReddotVisible(bShow, EReddotType.Normal)
end

function RoleInfoPersonal:GetCollectionRoomOpen()
    if self.roomData and self.roomData.show_room_level > 0 then
        return true
    end

    return false
end

function RoleInfoPersonal:SetDefault()
    local curLevel = self.isMpMode and self._accountLevel or self._seasonLevel
    local maxLevel = self.isMpMode and Server.GrowthRoadServer:GetMpMaxLevel() or
        Server.GrowthRoadServer:GetSolMaxLevel()

    if self.isMpMode then
        self.bSolRoom = false
        self.bMpStar = curLevel == maxLevel and self.starWeapons ~= nil and next(self.starWeapons) ~= nil
    else
        self.bMpStar = false
        self.bSolRoom = curLevel == maxLevel and self:GetCollectionRoomOpen()
    end

    self:RefreshLevelIconStyle()
    self:RefreshChangeBtn()

    if self.bSolRoom then
        self:RefreshCollectRoom()
    end
    if self.bMpStar then
        self:RefreshWeaponStar()
    end
    self:_InitLevelInputs()
end

function RoleInfoPersonal:RefreshLevelText()
    local Loc = Module.RoleInfo.Config.Loc
    local str
    if self.bMpStar then
        str = Loc.MPStarDesc
    elseif self.bSolRoom then
        str = Loc.SOLRoomDesc
    else
        if self:IsMySelf() then
            str = string.format(self.isMpMode and Loc.MPLevelText or Loc.SOLLevelText,
                self.isMpMode and self._accountLevel or self._seasonLevel)
        else
            str = self.isMpMode and Loc.MPLevelDesc or Loc.SOLLevelDesc
        end
    end

    self._wtLevelDesc:SetText(str)
end

function RoleInfoPersonal:RefreshLevelIconStyle()
    local bMp = self.isMpMode

    self._wtLevelIcon:Visible()

    if bMp and self.bMpStar then
        self._wtLevelIcon:SetStyle(2, 3, bMp) -- Type isMode isMp
        self._wtLevelIconBtn:Visible()

        if self._wtLevelIcon._wtSlideShow then
            if WidgetUtil.IsGamepad() then -- 手柄使用按钮走焦点击
                self._wtLevelIcon._wtSlideShow:HitTestInvisible()
            else -- 鼠标使用轮播图绑定点击
                self._wtLevelIcon._wtSlideShow:SelfHitTestInvisible()
            end
        end
    elseif bMp == false and self.bSolRoom then
        self._wtLevelIcon:SetStyle(1, 3, bMp)
        self._wtLevelIconBtn:Visible() -- 收藏室 武器 查看别人时也可以打开
    else
        if self:IsMySelf() then
            self._wtLevelIconBtn:Visible()
            if self.iconType == 0 then
                self._wtLevelIcon:SetStyle(0, 0, bMp)
            else
                self._wtLevelIcon:SetStyle(1, self.iconType, bMp)
            end
        else
            self._wtLevelIconBtn:HitTestInvisible()
            self._wtLevelIcon:SetStyle(0, 0, bMp)
        end
    end
end

function RoleInfoPersonal:_RefreshLevel()
    if self:IsMySelf() then
        self:SetMyLevelData()
    else
        self:SetLevelIconByLevel()
    end
end

function RoleInfoPersonal:_RefreshSolData(solData)
    self._solData = solData
    self:_RefreshSolSeason()
end

function RoleInfoPersonal:_RefreshSolSeason()
    if self.isMpMode then
        return
    end

    local solData = self._solData
    local GetNumStr = RoleInfoLogic.GetRoleInfoNumberStr
    local Loc = Module.RoleInfo.Config.Loc

    local dead = solData.total_killed == 0 and 1 or solData.total_killed
    local TotalFight = GetNumStr(solData.total_fight)
    local TotalValue = GetNumStr(solData.total_price)

    local preNum = 0
    if solData.total_fight ~= 0 then
        preNum = tonumber(solData.total_escape / solData.total_fight) * 100
    end
    local WinValue = MathUtil.GetNumberFormatStr(preNum, 1) .. "%"
    local KillEnemy = solData.kill_low_stakes + solData.kill_med_stakes + solData.kill_high_stakes
    local ReceiveValue = GetNumStr(solData.total_gained_price / dead)

    local TotalGameTime
    if solData.total_game_time < 3600 then
        TotalGameTime = GetNumStr(solData.total_game_time / 60) .. Loc.RecordMin
    else
        TotalGameTime = GetNumStr(solData.total_game_time / 3600) .. Loc.RecordHour
    end

    self._wtRoleInfoData1:SetText(TotalGameTime)
    self._wtRoleInfoData2:SetText(TotalFight)
    self._wtRoleInfoData3:SetText(TotalValue)
    self._wtRoleInfoData4:SetText(WinValue)
    self._wtRoleInfoData5:SetText(KillEnemy)
    self._wtRoleInfoData6:SetText(ReceiveValue)
    local skillName = self:_RefreshSolRankData()

    LogAnalysisTool.DoSendCheckPlayerSeasonRecord(ULuautils.GetUInt64String(self._playerId), "sol",
        skillName .. solData.rank_score_max, TotalGameTime, TotalFight, TotalValue, WinValue, KillEnemy,
        ReceiveValue)

    self._wtPersonalInformation:SetStyle(true)
    self._wtPersonalInformation:InitRoleAssetHoverTips(GetNumStr(solData.current_asset),
        GetNumStr(solData.non_current_assets))
end

function RoleInfoPersonal:_RefreshMPData(mpData)
    self._mpData = mpData
    self:_RefreshMPSeason()
end

function RoleInfoPersonal:_RefreshMPSeason()
    if self.isMpMode == false then
        return
    end

    local mpData = self._mpData
    local getNumStr = RoleInfoLogic.GetRoleInfoNumberStr

    local Loc = Module.RoleInfo.Config.Loc

    local TotalGameTime = ""
    local TotalFight = getNumStr(mpData.total_fight)
    local TotalValue = getNumStr(mpData.total_score)
    local WinValue = getNumStr(mpData.total_win)
    local KillEnemy = mpData.total_kill
    local ReceiveValue = ""
    if mpData.total_game_time < 3600 then
        TotalGameTime = getNumStr(mpData.total_game_time / 60) .. Loc.RecordMin
    else
        TotalGameTime = getNumStr(mpData.total_game_time / 3600) .. Loc.RecordHour
    end
    local SDM = 0
    if mpData.total_game_time ~= 0 and mpData.total_score ~= 0 then
        SDM = mpData.total_score / (mpData.total_game_time / 60)
    end
    ReceiveValue = getNumStr(SDM)
    self._wtRoleInfoData1:SetText(TotalGameTime)
    self._wtRoleInfoData2:SetText(TotalFight)
    self._wtRoleInfoData3:SetText(TotalValue)
    self._wtRoleInfoData4:SetText(WinValue)
    self._wtRoleInfoData5:SetText(KillEnemy)
    self._wtRoleInfoData6:SetText(ReceiveValue)

    self._wtPersonalInformation:SetStyle(false)
    local skillName = self:_RefreshMpRankData()

    LogAnalysisTool.DoSendCheckPlayerSeasonRecord(ULuautils.GetUInt64String(self._playerId), "mp",
        skillName .. mpData.rank_score_max, TotalGameTime, TotalFight, TotalValue, WinValue, KillEnemy,
        ReceiveValue)
end

function RoleInfoPersonal:_RefreshMilitaryCard(id)
    if self:IsMySelf() then
        self._militaryId = id
        self._wtMilitaryImage:AsyncSetImagePath(Module.RoleInfo:GetMilitary(self._militaryId), true)
    end
end

function RoleInfoPersonal:_OnGrowthRoadClick()
    if self.isMpMode then
        if self.bMpStar then
            if WidgetUtil.IsGamepad() then
                Facade.UIManager:AsyncShowUI(UIName2ID.ItemBook, nil, nil, 1, self.starWeapons)
            end
            return
        end
        --     Facade.UIManager:AsyncShowUI(UIName2ID.ItemBook, nil, nil, 1, self.starWeapons)
        -- else
        Module.Gunsmith:OpenGrowthRoad(2)
        -- end
    else
        if self.bSolRoom then
            Facade.UIManager:AsyncShowUI(UIName2ID.ItemBook, nil, nil, 0, nil, self.roomData)
        else
            local solModuelName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SOLModule)
            if Module.Gunsmith:OverseasResourceDownload(solModuelName) then
                return
            end
            Module.Gunsmith:OpenGrowthRoad(1)
        end
    end
end

function RoleInfoPersonal:CanJump()
    local curTime = Facade.ClockManager:GetLocalTimestamp()
    if self.jumpTime == 0 or Facade.ClockManager:GetLocalTimestamp() - self.jumpTime > 1 then
        self.jumpTime = Facade.ClockManager:GetLocalTimestamp()
        return true
    end
    return false
end

function RoleInfoPersonal:_OpenHeadPanelClick()
    if not self:CanJump() then
        return
    end
    Module.RoleInfo.Config.Event.evtOpenSocialChangePanel:Invoke(1)
end

function RoleInfoPersonal:_OpenMilitaryPanelClick()
    if not self:CanJump() then
        return
    end
    Module.RoleInfo.Config.Event.evtOpenSocialChangePanel:Invoke(2)
end

function RoleInfoPersonal:_OpenTitlePanelClick()
    if not self:CanJump() then
        return
    end
    Module.RoleInfo.Config.Event.evtOpenSocialChangePanel:Invoke(3)
end

function RoleInfoPersonal:OnCopyBtnClicked()
    if ULuaExtension.RoleInfoCopy2Clipboard then
        if self._playerId then
            ULuaExtension.RoleInfoCopy2Clipboard(self._EncryptionIdStr)
        end
        Module.CommonTips:ShowSimpleTip(Module.RoleInfo.Config.Loc.CopyTips)
    end
end

function RoleInfoPersonal:OnAddFriendBtnClicked()
    Module.Friend:AddFriend(self._playerId, FriendApplySource.PlayerInformationApply, nil, nil, nil, self._plat_id)
end

function RoleInfoPersonal:_ReportBtnOnClick()
    local playerInfo = {
        player_id = self._playerId,
        pic_url = self._picUrl,
        nick_name = self._nick
    }
    Module.Report:ReportPlayer(playerInfo, 2, nil, nil, nil, ReportEntrance.ReportMeteriral, nil, nil, { 4, 9 })
end

function RoleInfoPersonal:_RoleBadgeChoiceBtnOnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.RoleBadgeChoice, nil, nil)
end

function RoleInfoPersonal:_RefreshCdTimer(btn)
    -- 恢复按钮点击功能
    btn:SetIsEnabled(true)

    -- 清除计时器引用
    if self._buttonTimers[btn] then
        self._buttonTimers[btn] = nil
    end
end

function RoleInfoPersonal:LimitButtonClick(btn, delay)
    delay = delay or 1

    btn:SetIsEnabled(false)

    -- 清理已有计时器（如果存在）
    if self._buttonTimers[btn] then
        self._buttonTimers[btn]:Release()
    end

    -- 创建新计时器
    local timer = Timer:NewIns(delay)
    self._buttonTimers[btn] = timer

    -- 绑定回调（使用闭包捕获btn参数）
    timer:AddListener(function()
        self:_RefreshCdTimer(btn)
    end)

    timer:Start()
end

function RoleInfoPersonal:_RenameBtnOnClick()
    Module.Collection:OpenReNameCard()
end

function RoleInfoPersonal:_OnPraiseClick()
    if self:IsMySelf() then
        --Facade.UIManager:AsyncShowUI(UIName2ID.RoleInfoLikeDetail, nil, nil)
    else
        local function praiseClick()
            Server.FriendServer:UpdatePraiseData(self._playerId)
            self._wtPraisedNum:SetText(self._praisedNum + 1)
            self:PlayAnimationForward(self.WBP_RoleInfo_PersonalHomepage_dianzan, 1, false)
        end

        Server.FriendServer:PlayerPraise(self._playerId, CreateCallBack(praiseClick, self), 1)
    end
end

--------------------------------------- 界面分享相关 ------------------------------------
function RoleInfoPersonal:PreScreenshotShare()
    Module.CommonBar:SetTopBarVisible(false)
    self:CollapsedPingtai()
    self._wtAntherPanel:Collapsed()
    self:SetShare(true)
    self._wtSchoolPanel:Collapsed()
end

function RoleInfoPersonal:AfterScreenshotShare()
    Module.CommonBar:SetTopBarVisible(true)
    self._wtAntherPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self:SetSelfPingtai()
    self:SetShare(false)
    self:SetSelfSchool()
end

function RoleInfoPersonal:_OpenWxH5()
    --Module.GCloudSDK:SimpleOpenUrl("https://g0wx.cn/s/2fDiNrNDJ")
    Module.OpActWeChatPrivilege:ShowMainPanel()
end

function RoleInfoPersonal:_OpenQQH5()
    Module.OpActQQPrivilege:OpenQQPrivilegePage()
end

--end
-----------------------------------------------------------------------

-- BEGIN MODIFICATION @ VIRTUOS
function RoleInfoPersonal:_InitBadgeInputs()
    if not IsHD() then return end

    if self._navGroups and self._navGroups.badge then
        if self._wtBadgeChoice:IsVisible() then
            self._navGroups.badge:AddNavWidgetToArray(self._wtBadgeChoice)
        end

        -- self._navGroups.badge:AddNavWidgetToArray(self._wtBadgePanel)
        for i, widget in ipairs(self._wtBadgeDisplayTbl) do
            if widget._itemId ~= nil then
                self._navGroups.badge:AddNavWidgetToArray(widget)
            end
        end
    end
end

function RoleInfoPersonal:IsPopUIExist()
    loginfo("RoleInfoPersonal:IsPopUIExist")
    local popController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
    return popController:IsPopUIExist()
end

function RoleInfoPersonal:_InitLevelInputs()
    if not IsHD() then return end

    if self:IsPopUIExist() then
        return
    end

    if self._navGroups and self._navGroups.level then
        if self._wtBtnChange:IsVisible() then
            self._navGroups.level:AddNavWidgetToArray(self._wtBtnChange)
        end

        if self._wtLevelIconBtn:IsVisible() then
            -- if self.bMpStar then
            --     self._wtLevelIcon:SetCppValue("bIsFocusable", true)
            --     -- self._wtLevelIconBtn:SetCppValue("bIsFocusable", false)
            -- else
            --     self._wtLevelIcon:SetCppValue("bIsFocusable", false)
            --     -- self._wtLevelIconBtn:SetCppValue("bIsFocusable", true)
            -- end
            self._navGroups.level:AddNavWidgetToArray(self._wtLevelIconBtn)
        end

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups.level)
    end
end

function RoleInfoPersonal:_InitGamepadInputs() -- 是不是没有重新init
    if not self:IsVisible() then
        return
    end

    self:_DisableGamepadInputs()

    if not self._navGroups then
        self._navGroups = {
            season = WidgetUtil.RegisterNavigationGroup(self._wtSeasonPanel, self, "Hittest"),
            level = WidgetUtil.RegisterNavigationGroup(self._wtLevelPanel, self, "Hittest"),
            badge = WidgetUtil.RegisterNavigationGroup(self._wtBadgePanel, self, "Hittest"),
        }

        if self._navGroups.season then
            self._navGroups.season:AddNavWidgetToArray(self._wtSeasonPanel)
        end

        self:_InitLevelInputs()
        self:_InitBadgeInputs()

        for key, group in pairs(self._navGroups) do
            group:SetNavSelectorWidgetVisibility(true)
        end
    end

    self:UpdateShortcutButton()

    if not self._switchTabHandlers then
        self._switchTabHandlers =
        {
            self:AddInputActionBinding("Common_SwitchToPrevTab_Trigger", EInputEvent.IE_Pressed, self._ChangeMode,
                self, EDisplayInputActionPriority.UI_Pop),
            self:AddInputActionBinding("Common_SwitchToNextTab_Trigger", EInputEvent.IE_Pressed, self._ChangeMode,
                self, EDisplayInputActionPriority.UI_Pop),
        }
    end
end

function RoleInfoPersonal:UpdateShortcutButton()
    if not self:IsVisible() then
        return
    end

    local bShowSelf = self:IsMySelf()
    self._gamepadSummaryList = {}

    if bShowSelf then
        table.insert(self._gamepadSummaryList,
            { actionName = "Confirm", func = nil, caller = self, bUIOnly = false, bHideIcon = false })
    else
        table.insert(self._gamepadSummaryList,
            { actionName = "RoleInfo_Praise_Gamepad", func = self._OnPraiseClick, caller = self, bUIOnly = false,
                bHideIcon = false })
    end

    table.insert(self._gamepadSummaryList,
        { actionName = "RoleInfo_CopyUID_Gamepad", func = self.OnCopyBtnClicked, caller = self, bUIOnly = false,
            bHideIcon = false })


    if bShowSelf then
        table.insert(self._gamepadSummaryList,
            { actionName = "RoleInfo_Rename_Gamepad", func = self._RenameBtnOnClick, caller = self, bUIOnly = false,
                bHideIcon = false })

        if self._wtSaveModelBtn:IsVisible() then
            table.insert(self._gamepadSummaryList,
                { actionName = "RoleInfo_SaveSelf_Gamepad", func = self.SaveSelfModel, caller = self, bUIOnly = false,
                    bHideIcon = false })
        end
        if self._wtRefreshModelBtn:IsVisible() then
            table.insert(self._gamepadSummaryList,
                { actionName = "RoleInfo_RefreshSelf_GBadgeSyncamepad", func = self.RefreshSelfModel, caller = self,
                    bUIOnly = false, bHideIcon = false })
        end

        if self._wtWinnerBtn:IsVisible() then
            table.insert(self._gamepadSummaryList,
                { actionName = "Rank_Display", func = self.OnClickWinner, caller = self,
                    bUIOnly = false, bHideIcon = false })
        end
    else
        table.insert(self._gamepadSummaryList,
            { actionName = "RoleInfo_AddFriend_Gamepad", func = self.OnAddFriendBtnClicked, caller = self,
                bUIOnly = false, bHideIcon = false })
        table.insert(self._gamepadSummaryList,
            { actionName = "RoleInfo_Report_Gamepad", func = self._ReportBtnOnClick, caller = self, bUIOnly = false,
                bHideIcon = false })
        table.insert(self._gamepadSummaryList,
            { actionName = "RoleInfo_RefreshCareer_Gamepad", func = self.RefreshCareerData, caller = self,
                bUIOnly = false, bHideIcon = false })
    end

    if self.isMpMode == false then
        table.insert(self._gamepadSummaryList,
            { actionName = "Assembly_AllEquipmentValueTips", func = self._ToggleDescriptionTip, caller = self,
                bUIOnly = false, bHideIcon = false })
    end

    Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, true)
end

function RoleInfoPersonal:_TryFocusBadge()
    WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups.badge)
end

function RoleInfoPersonal:_DisableGamepadInputs()
    if self._navGroups then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroups = nil
    end

    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end

    if self._switchTabHandlers then
        for key, value in pairs(self._switchTabHandlers) do
            self:RemoveInputActionBinding(value)
        end
        self._switchTabHandlers = nil
    end

    self._wtLevelIcon:DisableGamepad()
end

function RoleInfoPersonal:_ToggleDescriptionTip()
    if self._wtPersonalInformation.hoverHandle then
        self._wtPersonalInformation:_OnHideRoleAssetTips()
    else
        self._wtPersonalInformation:_OnShowRoleAssetTips()
    end
end

--- Console 查看用户档案
function RoleInfoPersonal:CreateProfileBtn()
    if PLATFORM_GEN9 ~= 1 then
        return
    end

    local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
        local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
        if bSucceeded and UDFMGameUrlGeneratorIns then
            -- 判断是否绑定了同平台的账号
            queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
            if not queryRes.bHasUID then
                return
            end

            if queryRes.bHasUID then
                self._platformId = queryRes.UID
            else
                self._platformId = nil
            end

            local platIDType = Server.AccountServer.GetPlatIdType()
            local success, platData
            success, platData = UDFMPlatformUtils.GetPlatformData(platIDType, platData)
            if success then
                self._wtPlatformProfileBtn:SetImageSoftPath(platData.ViewProfileIcon, platData.ViewProfileIcon)
                self._wtPlatformProfileBtn:Visible()
            end
        end
    end

    Server.SocialServer:ReqQueryUID(ULuautils.GetUInt64String(self._playerId), OnReqQueryRes)
end

function RoleInfoPersonal:_PlatformProfileBtnOnClick()
    if self._platformId ~= nil then
        UDFMPlatformUtils.ShowPlatformProfileUI(self._platformId)
    elseif Server.AccountServer:IsOpenFakePlat() then
        -- 显示错误信息
        Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CrossPlatError)
    end
end

function RoleInfoPersonal:IsMySelf()
    return self._playerId == self.myPlayerId
end

-- END MODIFICATION

return RoleInfoPersonal
