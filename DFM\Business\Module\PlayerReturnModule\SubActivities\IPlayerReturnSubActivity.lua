----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

local Json               = require "DFM.YxFramework.Plugin.Json.Json".createJson()
local PlayerReturnConfig = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"

---@class IPlayerReturnSubActivity
local IPlayerReturnSubActivity = {}

local RegisteredInterfaces = {}

---@return IPlayerReturnSubActivity
function IPlayerReturnSubActivity.Get(activityType, armedForceMode)
    if not RegisteredInterfaces[activityType] then return end
    if armedForceMode == nil then
        armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    end

    ---@type IPlayerReturnSubActivity
    local obj = setmetatable({}, {__index = RegisteredInterfaces[activityType]})
    obj:SetArmedForceMode(armedForceMode)
    return obj
end

function IPlayerReturnSubActivity:SetArmedForceMode(armedForceMode)
    self.armedForceMode = armedForceMode
end

---@return string?
function IPlayerReturnSubActivity:GetVOEventNameWhenFirstEnter()
    assert(false) -- requires override
end

function IPlayerReturnSubActivity:GetArmedForceMode()
    return self.armedForceMode
end


---@return IPlayerReturnSubActivity[]
function IPlayerReturnSubActivity.GetRegisteredTypes()
    return RegisteredInterfaces
end

---@return integer
function IPlayerReturnSubActivity:GetType()
    --- Requires override
    assert(false)
end

---@return IPlayerReturnSubActivity
function IPlayerReturnSubActivity:GetInterfaceByArmedForceMode(armedForceMode)
    assert(false)
end

---@return integer
function IPlayerReturnSubActivity:GetSubPanelNavID()
    --- Requires override
    assert(false)
end

function IPlayerReturnSubActivity:GetRedDotKey()
    assert(self.armedForceMode)
    return "Mode"..tostring(self.armedForceMode).."/SubActivity"..tostring(self:GetType())
end

---@return pb_ActivityInfo
function IPlayerReturnSubActivity:GetActivityInfo()
    for _, activityInfo in pairs(Server.ActivityServer.AllActivityInfos) do
        if activityInfo.return_info and activityInfo.return_info.type == self:GetType() and activityInfo.mode_tag == self.armedForceMode then
            return activityInfo
        end
    end
end

---@return string
function IPlayerReturnSubActivity:GetName()
    local activityInfo = self:GetActivityInfo()
    if activityInfo then 
        return activityInfo.name
    end
    return ""
end

---@return string
function IPlayerReturnSubActivity:GetDesc()
    local activityInfo = self:GetActivityInfo()
    if activityInfo then 
        return activityInfo.desc
    end
    return ""
end

---@return boolean
function IPlayerReturnSubActivity:GetRedDotState()
    --- Requires override
    return false
end

---@return integer progress, integer total
function IPlayerReturnSubActivity:GetDisplayedProgress()
    --- Requires override
    return 0, 0
end

function IPlayerReturnSubActivity:Register()
    RegisteredInterfaces[self:GetType()] = self
end

function IPlayerReturnSubActivity:IsFinished()
    --- Requires override
    return true
end

---@class PlayerReturnJsonStorage
---@field IntroVideoPlayed                  integer                                    = expireTimeLastTimeIntroVideoPlayed
---@field LobbyPopupLastShownDate           table<integer, integer>                    t[EArmedForceMode] = tLastShownDate
---@field PlayerReturnVOPlayed              table<integer, table<integer, integer>>    t[EArmedForceMode][ReturnActivityType] = expireTimeLastTimeVOPlayed
---@field PlayerReturnClickEntrance         table<integer, boolean>                    t[EArmedForceMode] = bHasBeenClicked
---@field DailyMatchLastPopupTime           table<integer, integer>                    t[EArmedForceMode] = lastPopupTimestamp

function IPlayerReturnSubActivity:_InitJsonStorageIfNotPresent()
    local storageID = Server.TipsRecordServer.keys.PlayerReturnLastDayLobbyPopupShown
    local str = Server.TipsRecordServer:GetString(storageID)
    
    -- Json数据应当 { 开头
    if (type(str)~="string") or (not str:starts_with("{")) then
        local data = Json.encode({
            LobbyPopupLastShownDate = {
                _notArray = true
            },
            PlayerReturnVOPlayed = {
                _notArray = true
            },
            PlayerReturnClickEntrance = {
                _notArray = true
            },
            DailyMatchLastPopupTime = {
                _notArray = true
            }
        }, {numberAsKey = true})
        Server.TipsRecordServer:SetString(storageID, data)
    end
end

---@return PlayerReturnJsonStorage
function IPlayerReturnSubActivity:GetJsonStorage()
    self:_InitJsonStorageIfNotPresent()

    local storageID = Server.TipsRecordServer.keys.PlayerReturnLastDayLobbyPopupShown
    local strData = Server.TipsRecordServer:GetString(storageID)
    return Json.decode(strData, nil, {numberAsKey = true})
end

---@param data PlayerReturnJsonStorage
function IPlayerReturnSubActivity:SetJsonStorage(data)
    self:_InitJsonStorageIfNotPresent()

    local storageID = Server.TipsRecordServer.keys.PlayerReturnLastDayLobbyPopupShown
    local strData = Json.encode(data, {numberAsKey = true})
    Server.TipsRecordServer:SetString(storageID, strData)
end

---@param RedDotAgent RedDotAgent
function IPlayerReturnSubActivity:InitRedDotStructure(RedDotAgent)
    local node = RedDotAgent:CreateNode(self:GetRedDotKey())
    node:Update(self:GetRedDotState())
    return node
end

---@param RedDotAgent RedDotAgent
function IPlayerReturnSubActivity:UpdateRedDot(RedDotAgent)
    self:InitRedDotStructure(RedDotAgent)
end

function IPlayerReturnSubActivity:UpdateMainPageButton(wtMainPanel)
    local textDoubleExp = wtMainPanel:Wnd("DFTextBlock_3", UITextBlock)
    local textDoubleMerits = wtMainPanel:Wnd("DFTextBlock_4", UITextBlock)

    if self:GetArmedForceMode() == EArmedForceMode.MP then
        textDoubleExp:SetText(PlayerReturnConfig.Localization.DoubleExperience)
        textDoubleMerits:SetText(PlayerReturnConfig.Localization.DoubleMerits)
    else
        textDoubleExp:Collapsed()
        textDoubleMerits:Collapsed()
    end
end

return IPlayerReturnSubActivity