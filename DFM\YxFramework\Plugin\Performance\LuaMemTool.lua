local function log(...)
    loginfo("[LuaMemTool]", ...)
end

--region Tool Code

-- 用于存储已经处理过的table，避免循环引用
local processed_tables = {}

-- 递归遍历table的函数
local function traverse_table(tbl, tables_list, tables_name, from_1, from_2, from_3)
    -- 如果table已经被处理过，则跳过
    if processed_tables[tbl] then
        return
    end
    
    -- 标记当前table为已处理
    processed_tables[tbl] = true
    
    -- 将当前table添加到列表中
    table.insert(tables_list, tbl)

    -- 将当前table的名称添加到列表中
    local from
    if from_1 then
        from = from_1 .. "." .. from_2 .. "." .. from_3
    elseif from_2 then
        from = from_2 .. "." .. from_3
    else
        from = from_3
    end
    table.insert(tables_name, from)
    
    -- 递归遍历table中的所有值
    for key, value in pairs(tbl) do
        if type(value) == "table" then
            if type(key) == "number" then
                key = "ArrayIdx"
            elseif type(key) ~= "string" then
                key = tostring(key)
            end
            traverse_table(value, tables_list, tables_name, from_2, from_3, key)
        end
    end
end

local function check_table_is_pb(tbl)
    return rawget(tbl, "__pairs") == __pb_pairs
end

function gClassifyAllTables()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.LuaDataTableCheckLargeIteration 0")

    collectgarbage("collect")
    collectgarbage("collect")

    local t1 = os.clock()
    collectgarbage("collect")
    local fullGCCost = os.clock() - t1

    local tables_list = {}
    local tables_name = {}
    local total_pairs = 0
    local pb_total_pairs = 0
    processed_tables = {}  -- 重置已处理table的记录
    
    -- 从_G开始遍历所有table
    traverse_table(_G, tables_list, tables_name, nil, nil, "_G")
    traverse_table(debug.getregistry(), tables_list, tables_name, nil, nil, "_R")

    assert(#tables_list == #tables_name)
    
    local categories = {}
    local default_category = "default"
    local lazy_category = "lazy"
    
    -- 初始化分类统计
    categories[default_category] = {
        count = 0,
        total_pairs = 0
    }
    
    -- 遍历所有table
    for i, tbl in ipairs(tables_list) do
        local category = tables_name[i]
        local postfix
        -- 检查_cname字段
        if tbl.__lazy then
            postfix = lazy_category
        elseif tbl._cname then
            postfix = tbl._cname
        -- 检查__name字段
        elseif tbl.__name then
            postfix = tbl.__name
        else
        end
        if type(postfix) ~= "string" then
            postfix = default_category
        end
        if postfix == default_category
        or postfix == lazy_category then
            category = category .. "@" .. postfix
        else
            category = postfix
        end
        
        -- 初始化分类（如果不存在）
        if not categories[category] then
            categories[category] = {
                count = 0,
                total_pairs = 0
            }
        end
        
        -- 统计当前table的键值对数量
        local pair_count = 0
        local key, value = next(tbl, nil)
        while key do
            pair_count = pair_count + 1
            key, value = next(tbl, key)
        end
        
        -- 更新统计信息
        categories[category].count = categories[category].count + 1
        categories[category].total_pairs = categories[category].total_pairs + pair_count

        if check_table_is_pb(tbl) then
            pb_total_pairs = pb_total_pairs + pair_count
        end
        total_pairs = total_pairs + pair_count
    end
    
    -- 创建并写入CSV文件
    local ULuaSubsystem = import "LuaSubsystem"
    local saveDir = ULuaSubsystem.Convert2PublicSavedPath() .. "LuaMem/"
    if not ULuaExtension.Ext_DirectoryExists(saveDir) then
        ULuaExtension.Ext_MakeDirectory(saveDir)
    end

    local snapshotFileNameFormat = "LuaMem %s.csv"
    local savePath = saveDir .. string.format(snapshotFileNameFormat, tostring(os.date("%Y-%m-%d %H-%M-%S")))
    local file = io.open(savePath, "w")
    if not file then
        error("无法创建CSV文件")
    end
    
    -- 写入CSV头部
    file:write("Category,num,average_kv,total_kv\n")
    
    -- 写入数据
    local minimum_total_pairs = 1000
    local other_category = "other"
    categories[other_category] = {
        count = 0,
        total_pairs = 0
    }
    
    -- 先处理所有类别，将小于阈值的类别合并到other中
    for category, stats in pairs(categories) do
        if category ~= other_category and stats.total_pairs < minimum_total_pairs then
            categories[other_category].count = categories[other_category].count + stats.count
            categories[other_category].total_pairs = categories[other_category].total_pairs + stats.total_pairs
        end
    end
    
    -- 创建排序列表，排除other类别
    local sorted_categories = {}
    for category, stats in pairs(categories) do
        if category ~= other_category and stats.total_pairs >= minimum_total_pairs then
            table.insert(sorted_categories, {category = category, stats = stats})
        end
    end
    
    -- 按total_pairs降序排序
    table.sort(sorted_categories, function(a, b)
        return a.stats.total_pairs > b.stats.total_pairs
    end)
    
    -- 先输出排序后的类别
    for _, item in ipairs(sorted_categories) do
        local category = item.category
        local stats = item.stats
        local avg_pairs = stats.total_pairs / stats.count
        -- 处理类别名称中可能包含的逗号
        local safe_category = string.gsub(category, ",", ";")
        file:write(string.format("%s,%d,%.2f,%d\n", 
        safe_category, stats.count, avg_pairs, stats.total_pairs))
    end
    
    -- 最后输出other类别
    if categories[other_category].count > 0 then
        file:write(string.format("%s,%d,%.2f,%d\n", 
        other_category, categories[other_category].count, categories[other_category].total_pairs / categories[other_category].count, categories[other_category].total_pairs))
    end
    
    -- 在文件末尾添加汇总信息
    file:write("\n")
    file:write(string.format("total_pairs=%d\n", total_pairs))
    file:write(string.format("pb_total_pairs=%d\n", pb_total_pairs))
    file:write(string.format("gc_cost=%f\n", fullGCCost))
    
    file:close()
    print("统计结果已保存到 table_statistics.csv")

    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.LuaDataTableCheckLargeIteration 1")
end

function gFindTablesByCName(target_cname)
    local tables_list = {}
    local tables_name = {}
    processed_tables = {}  -- 重置已处理table的记录
    
    -- 从_G开始遍历所有table
    traverse_table(_G, tables_list, tables_name, nil, nil, "_G")
    traverse_table(debug.getregistry(), tables_list, tables_name, nil, nil, "_R")

    assert(#tables_list == #tables_name)
    
    log("查找_cname为 " .. target_cname .. " 的table：")
    local found_count = 0
    local unique_keys = {}  -- 用于存储已发现的唯一key
    local unique_keys_is_float = {}
    
    -- 遍历所有table
    for i, tbl in ipairs(tables_list) do
        -- 检查_cname字段是否匹配目标值
        if tbl._cname == target_cname then
            found_count = found_count + 1
            
            -- 收集当前table中的所有string类型的key和对应value的类型
            for key, value in pairs(tbl) do
                if type(key) == "string" then
                    local value_type = type(value)

                    if value_type == "number" then
                        local sample_value = value
                        if math.type(sample_value) == "number" then
                            unique_keys_is_float[key] = true
                        end
                    end

                    if not unique_keys[key] and value_type ~= "function" then
                        unique_keys[key] = value_type
                    end
                end
            end
        end
    end
    
    -- 输出所有不重复的key和对应的value类型
    if found_count > 0 then
        log("\n所有不重复的string类型key及其value类型:")
        local sorted_keys = {}
        for key in pairs(unique_keys) do
            table.insert(sorted_keys, key)
        end
        table.sort(sorted_keys)  -- 按字母顺序排序key
        
        for _, key in ipairs(sorted_keys) do
            log(string.format("  [%s] = (%s)", key, unique_keys[key]))
        end
        
        -- 生成特定格式的输出
        log("\n生成的字段定义:")
        local output = "OverrideClassByDynamicUD(" .. target_cname .. ", false, \n"
        for i, key in ipairs(sorted_keys) do
            local lua_type = unique_keys[key]
            local field_type
            
            -- 映射Lua类型到FieldType枚举
            if lua_type == "number" then
                if unique_keys_is_float[key] then
                    field_type = "FieldType.Number"
                else
                    field_type = "FieldType.Integer"
                end
            elseif lua_type == "integer" then
                field_type = "FieldType.Integer"
            elseif lua_type == "string" then
                field_type = "FieldType.String"
            elseif lua_type == "boolean" then
                field_type = "FieldType.Boolean"
            elseif lua_type == "table" then
                field_type = "FieldType.Table"
            elseif lua_type == "userdata" then
                field_type = "FieldType.Userdata"
            elseif lua_type == "function" then
                field_type = "FieldType.Function"
            else
                field_type = "FieldType.Userdata" -- 默认情况
            end
            
            if i == #sorted_keys then
                output = output .. string.format("    {\"%s\", %s}\n", key, field_type)
            else
                output = output .. string.format("    {\"%s\", %s},\n", key, field_type)
            end
        end
        output = output .. ")"
        log(output)
        
        log("\n总共找到 " .. found_count .. " 个table，_cname为 " .. target_cname)
        log("不重复的string类型key总数: " .. #sorted_keys)
    else
        log("未找到_cname为 " .. target_cname .. " 的table")
    end
end

--endregion

--region Test Code

function gTestDynamicUD()
    local ud = NewDynamicUD(
        {"key_1", FieldType.Integer, 111},
        {"key_2", FieldType.Number, 222.222},
        {"key_3", FieldType.String, "fail"},
        {"key_4", FieldType.Boolean, true},
        {"key_5", FieldType.Table},
        {"key_6", FieldType.Userdata},
        {"func_1", FieldType.Function, function(self)
            log("call func_1", self, self.key_1)
        end},
        {"func_2", FieldType.Function, function(self)
            log("call func_2", self, self.key_2)
        end}
    )
    log(ud)
    
    local ud_ins = ud:new()
    log(ud_ins, type(ud_ins), #ud_ins)
    
    log(ud_ins.key_1)
    log(ud_ins.key_2)
    log(ud_ins.key_3)
    log(ud_ins.key_4)
    log(ud_ins.key_5)
    log(ud_ins.key_6)
    
    ud_ins.key_1 = 333
    ud_ins.key_2 = 444.444
    ud_ins.key_3 = "success"
    ud_ins.key_4 = false
    ud_ins.key_5 = {x = 1, y = 2}
    ud_ins.key_6 = GetWorld()
    
    log(ud_ins.key_1)
    log(ud_ins.key_2)
    log(ud_ins.key_3)
    log(ud_ins.key_4)    
    log(ud_ins.key_5, ud_ins.key_5.x, ud_ins.key_5.y)  
    log(ud_ins.key_6)    

    ud_ins.key_5.x = 3
    ud_ins.key_5.y = 4
    log(ud_ins.key_5, ud_ins.key_5.x, ud_ins.key_5.y)

    ud_ins:func_1()
    ud_ins:func_2()
end

function gTestDynamicUDPerf()
    local ud = NewDynamicUD(
        {"key_1", FieldType.Integer, 111},
        {"key_2", FieldType.Number, 222.222},
        {"key_3", FieldType.String, "fail"},
        {"key_4", FieldType.Boolean, true},
        {"key_5", FieldType.Table},
        {"key_6", FieldType.Userdata},
        {"func_1", FieldType.Function, function(self)
            log("call func_1", self, self.key_1)
        end},
        {"func_2", FieldType.Function, function(self)
            log("call func_2", self, self.key_2)
        end}
    )
    local ud_ins = ud:new()
    ud_ins.key_1 = 111
    ud_ins.key_2 = 222.222
    ud_ins.key_3 = "fail"
    ud_ins.key_4 = true
    ud_ins.key_5 = {}
    ud_ins.key_6 = GetWorld()

    local raw_ins = {}
    raw_ins.key_1 = 111
    raw_ins.key_2 = 222.222
    raw_ins.key_3 = "fail"
    raw_ins.key_4 = true
    raw_ins.key_5 = {}
    raw_ins.key_6 = GetWorld()
    raw_ins.func_1 = function(self)
        log("call func_1", self, self.key_1)
    end
    raw_ins.func_2 = function(self)
        log("call func_2", self, self.key_2)
    end

    local wrap_ins = setmetatable({}, {
        __index = function(t, k)
            return rawget(raw_ins, k)
        end
    })

    local tmp
    local num = 1000000
    
    local function test_performance(key_name, type_name)
        log(string.format("\n测试 %s 类型 (%s) 的性能:", type_name, key_name))
        local a = os.clock()
        for i = 1, num do
            tmp = ud_ins[key_name]
        end
        local b = os.clock()
        for i = 1, num do
            tmp = raw_ins[key_name]
        end
        local c = os.clock()
        for i = 1, num do
            tmp = wrap_ins[key_name]
        end
        local d = os.clock()
        log(string.format("UD访问: %.2f ms, 直接访问: %.2f ms, 元表访问: %.2f ms", 
            (b - a) * 1000, (c - b) * 1000, (d - c) * 1000))
    end

    -- 测试各种类型
    test_performance("key_1", "Integer")
    test_performance("key_2", "Number")
    test_performance("key_3", "String")
    test_performance("key_4", "Boolean")
    test_performance("key_5", "Table")
    test_performance("key_6", "Userdata")
    test_performance("func_1", "Function")
end

function gTestDynamicUDSetPerf()
    local ud = NewDynamicUD(
        {"key_1", FieldType.Integer, 111},
        {"key_2", FieldType.Number, 222.222},
        {"key_3", FieldType.String, "fail"},
        {"key_4", FieldType.Boolean, true},
        {"key_5", FieldType.Table},
        {"key_6", FieldType.Userdata},
        {"func_1", FieldType.Function, function(self)
            log("call func_1", self, self.key_1)
        end},
        {"func_2", FieldType.Function, function(self)
            log("call func_2", self, self.key_2)
        end}
    )
    local ud_ins = ud:new()
    ud_ins.key_1 = 111
    ud_ins.key_2 = 222.222
    ud_ins.key_3 = "fail"
    ud_ins.key_4 = true
    ud_ins.key_5 = {}
    ud_ins.key_6 = GetWorld()
    

    local raw_ins = {}
    raw_ins.key_1 = 111
    raw_ins.key_2 = 222.222
    raw_ins.key_3 = "fail"
    raw_ins.key_4 = true
    raw_ins.key_5 = {}
    raw_ins.key_6 = GetWorld()
    raw_ins.func_1 = function(self)
        log("call func_1", self, self.key_1)
    end
    raw_ins.func_2 = function(self)
        log("call func_2", self, self.key_2)
    end

    local wrap_ins = setmetatable({}, {
        __index = function(t, k)
            return rawget(raw_ins, k)
        end,
        __newindex = function(t, k, v)
            rawset(raw_ins, k, v)
        end
    })

    local num = 1000000
    
    local function test_set_performance(key_name, type_name, value)
        log(string.format("\n测试 %s 类型 (%s) 赋值性能:", type_name, key_name))
        local a = os.clock()
        for i = 1, num do
            ud_ins[key_name] = value
        end
        local b = os.clock()
        for i = 1, num do
            raw_ins[key_name] = value
        end
        local c = os.clock()
        for i = 1, num do
            wrap_ins[key_name] = value
        end
        local d = os.clock()
        log(string.format("UD赋值: %.2f ms, 直接赋值: %.2f ms, 元表赋值: %.2f ms", 
            (b - a) * 1000, (c - b) * 1000, (d - c) * 1000))
    end

    -- 测试各种类型赋值
    test_set_performance("key_1", "Integer", 222)
    test_set_performance("key_2", "Number", 333.333)
    test_set_performance("key_3", "String", "test")
    test_set_performance("key_4", "Boolean", false)
    test_set_performance("key_5", "Table", {})
    test_set_performance("key_6", "Userdata", GetWorld())
    -- test_set_performance("func_1", "Function", function() end)
end

function gTestDynamicUDRef()
    local ud = NewDynamicUD(
        {"key_1", FieldType.Integer, 111},
        {"key_2", FieldType.Number, 222.222},
        {"key_3", FieldType.String, "fail"},
        {"key_4", FieldType.Boolean, true},
        {"key_5", FieldType.Table},
        {"key_6", FieldType.Userdata},
        {"func_1", FieldType.Function, function(self)
            log("call func_1", self, self.key_1)
        end},
        {"func_2", FieldType.Function, function(self)
            log("call func_2", self, self.key_2)
        end}
    )
    local ud_ins = ud:new()

    local t1 = setmetatable({}, {
        __gc = function()
            log("__gc t1")
        end
    })
    ud_ins.key_5 = t1

    local t2 = setmetatable({}, {
        __gc = function()
            log("__gc t2")
        end
    })

    ud_ins.key_5 = t2

    ud_ins.key_5 = nil

    Timer.DelayCall(0.1, function()
        collectgarbage("collect")
        collectgarbage("collect")
    end)
end

--endregion