----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------

local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local MemoryUtil = require "DFM.YxFramework.Util.MemoryUtil"
local APawn = import "Pawn"
local FHitResult = import "HitResult"
local HitResult = FHitResult()

local IrisSafeHouseEventLogic = {}

IrisSafeHouseEventLogic.Pawn = nil
IrisSafeHouseEventLogic.AddIrisSafeHouseListeners = function()
    Module.CommonBar.Config.evtOnCommonBarBasisViewReady:AddListener(IrisSafeHouseEventLogic.OnTopBarBasisViewReady_SafeHouse)
    Server.MatchServer.Events.evtStopPreShowLoadingView:AddListener(IrisSafeHouseEventLogic.StartEnterIrisWorld)
    LuaGlobalEvents.evtLuaHandleUEPreGC:AddListener(IrisSafeHouseEventLogic.OnLuaHandleUEPreGC)
    if not isvalid(IrisSafeHouseEventLogic.Pawn) then
        local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        if isvalid(localCtrl) then
            local pawn = localCtrl:GetPawn()
            if isvalid(pawn) and LAI.GetObjectClass(pawn) == APawn then
                IrisSafeHouseEventLogic.Pawn = pawn
            end
        end
    end
end

IrisSafeHouseEventLogic.RemoveIrisSafeHouseListeners = function()
    Module.CommonBar.Config.evtOnCommonBarBasisViewReady:RemoveListener(IrisSafeHouseEventLogic.OnTopBarBasisViewReady_SafeHouse)
    Server.MatchServer.Events.evtStopPreShowLoadingView:RemoveListener(IrisSafeHouseEventLogic.StartEnterIrisWorld)
    LuaGlobalEvents.evtLuaHandleUEPreGC:RemoveListener(IrisSafeHouseEventLogic.OnLuaHandleUEPreGC)
end

function IrisSafeHouseEventLogic.StartEnterIrisWorld()
    if not IsHD() and Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        Facade.UIManager:AsyncShowUI(UIName2ID.IrisWorldEntryMainPanel)
    end
end

function IrisSafeHouseEventLogic.OnTopBarBasisViewReady_SafeHouse(uiIns)
    if IsHD() then
		Module.IrisSafeHouse:InitMainTab()
		Module.IrisSafeHouse:OpenMainTabView(9)
	else
        Facade.UIManager:AsyncShowUI(UIName2ID.IrisWorldEntryMainPanel)
	end
end

IrisSafeHouseEventLogic.OnEnterIrisSafeHouseProcess = function()
end

IrisSafeHouseEventLogic.OnLeaveIrisSafeHouseProcess = function()
end


IrisSafeHouseEventLogic.AddModeHallListeners = function()
    Module.CommonBar.Config.evtOnCommonBarBasisViewReady:AddListener(IrisSafeHouseEventLogic.OnTopBarBasisViewReady_ModeHall)
end

IrisSafeHouseEventLogic.RemoveModeHallListeners = function()
    Module.CommonBar.Config.evtOnCommonBarBasisViewReady:RemoveListener(IrisSafeHouseEventLogic.OnTopBarBasisViewReady_ModeHall)
end

function IrisSafeHouseEventLogic.OnTopBarBasisViewReady_ModeHall(uiIns)
end

function IrisSafeHouseEventLogic.OnLuaHandleUEPreGC()
    if not MemoryUtil.IsLogicLowMemoryDevice() then
        return
    end

    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow ~= EGameFlowStageType.SafeHouse and currentGameFlow ~= EGameFlowStageType.Lobby  then
        loginfo("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC blocked because not in SafeHouse gf")
        return
    end
    if #ItemOperaTool.SafeHouseCharacterIsUsedBy > 0 then
        loginfo("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC blocked because character is used by", table.concat(ItemOperaTool.SafeHouseCharacterIsUsedBy, ","))
        return
    end
    if Server.MatchServer:GetIsWaitForGotoGame() then
        loginfo("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC blocked because IsWaitForGotoGame")
        return
    end
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not isvalid(localCtrl) then
        loginfo("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC unable to find local controller")
        return
    end
    if not localCtrl.bHasSpawnCharacter then
        loginfo("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC hasn't spawn character yet")
        return
    end
    local character = localCtrl:GetPawn()
    if not isvalid(character) then
        logerror("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC unable to find local character")
        return
    end
    local pawn = IrisSafeHouseEventLogic.Pawn
    if not isvalid(pawn) then
        pawn = GetWorld():SpawnActor(APawn, character:K2_GetActorLocation(), character:K2_GetActorRotation())
        IrisSafeHouseEventLogic.Pawn = pawn
    end
    if not isvalid(pawn) then
        -- 必须要有一个Pawn给PlayerController Possess,因为OpenWorldStreaming在Pawn为空时不会Focus
        --bug=147315863 [【CN】【安卓】DFM--四档：Vivo Z6 5G，五档：Vivo iQOO U5x，Realme V11--系统-收藏室--首次进入收藏室渲染呈现黑屏状态](https://tapd.woa.com/r/t?id=147315863&type=bug)
        loginfo("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC unable to spawn a pawn")
        return
    end
    logerror("IrisSafeHouseEventLogic.OnLuaHandleUEPreGC destroy character")

    local weaponCmp = UE.GameplayBlueprintHelper.FindComponentByClass(character, UE.WeaponManagerComponent)
    if isvalid(weaponCmp) then
        weaponCmp:RemoveAllWeapon(true)
    end
   
    pawn:K2_SetActorLocation(character:K2_GetActorLocation(), false, HitResult, false)
    pawn:K2_SetActorRotation(character:K2_GetActorRotation(), false)
    localCtrl:Possess(pawn)
    character:K2_DestroyActor()
    localCtrl.bHasSpawnCharacter = false
end

return IrisSafeHouseEventLogic