----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemDetailContentBase = require("DFM.Business.Module.ItemDetailModule.UI.ItemDetailContentBase")
local ItemDetailConfig      = require("DFM.Business.Module.ItemDetailModule.ItemDetailConfig")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local UAttackLevelDetailCorrectionManager = import "AttackLevelDetailCorrectionManager"
local attackLevelDetailCorrectionMgr = UAttackLevelDetailCorrectionManager.Get()

---@class ItemDetailContentBullet : ItemDetailContentBase
local ItemDetailContentBullet = ui("ItemDetailContentBullet", ItemDetailContentBase)

function ItemDetailContentBullet:Ctor()
    self._itemInfo = nil
	self._piercingLevel = 0

	self._wtArmorPiercingLevelText = self:Wnd("DFTextBlock", UITextBlock)	-- 穿甲等级
	self._wBuffListPanel = self:Wnd("wHeadsetLabelBtn", UIWidgetBase)	-- buff列表
	self._wBuffWrap = self:Wnd("wBuffWrap", UIWidgetBase)	-- buff列表

	self._wEffetListPanel = self:Wnd("WBP_ItemDetailContent_ListContent", UIWidgetBase)	-- 效果列表
	self._wEffetWrap = self:Wnd("DFVerticalBox_212", UIWidgetBase)	-- 效果列表

	-- 基础伤害加成
	self._wtBasicDamageAdd = self:Wnd("wBasicAddition", UIWidgetBase)
	self._wtBasicDamageAdd:SelfHitTestInvisible()
	-- 护甲伤害加成
	self._wtArmorDamageAdd = self:Wnd("wArmorAddition", UIWidgetBase)
	-- self._wtArmorDamageAdd:SelfHitTestInvisible()
	self._wtArmorDamageAdd:Collapsed()

	-- 护甲伤害衰减水平
	self._wtTipsPanel = self:Wnd("WBP_ItemDetailContent_Item", UIWidgetBase)
	self._wtTipsCheckbox = self:Wnd("wtCommonCheckInstruction", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
    if DFHD_LUA == 1 then
        self._wtTipsCheckbox:Event("OnCheckedHovered", self.OnBtnTipsHoverd, self)
        self._wtTipsCheckbox:Event("OnCheckedUnhovered", self.OnBtnTipsUnhoverd, self)
        self._wtTipsCheckbox:Event("OnUncheckedHovered", self.OnBtnTipsHoverd, self)
        self._wtTipsCheckbox:Event("OnUncheckedUnhovered", self.OnBtnTipsUnhoverd, self)
    else
        self._wtTipsCheckbox:Event("OnCheckStateChanged", self.OnBtnTipsClicked, self)
    end
	self._wtArmorDamageLevelText = self:Wnd("DFTextBlock_108", UITextBlock)

	-- 穿透等级
	self._wtPiercingTipsPanel = self:Wnd("WBP_ItemDetailContent_Item_1", UIWidgetBase)
	self._wtPiercingTipsCheckbox = self:Wnd("wTipsCheckbox", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
    if DFHD_LUA == 1 then
        self._wtPiercingTipsCheckbox:Event("OnCheckedHovered", self.OnPiercingBtnTipsHoverd, self)
        self._wtPiercingTipsCheckbox:Event("OnCheckedUnhovered", self.OnBtnTipsUnhoverd, self)
        self._wtPiercingTipsCheckbox:Event("OnUncheckedHovered", self.OnPiercingBtnTipsHoverd, self)
        self._wtPiercingTipsCheckbox:Event("OnUncheckedUnhovered", self.OnBtnTipsUnhoverd, self)
    else
        self._wtPiercingTipsCheckbox:Event("OnCheckStateChanged", self.OnPiercingBtnTipsClicked, self)
    end

	-- 适用枪械
	self._wAccessoryTypeText = self:Wnd("DFTextBlock_1", UITextBlock)
	self._wtAccessoryPanel = self:Wnd("WBP_ItemDetailContent_Item_8", UIWidgetBase)
	self._wtAccessoryCheckbox = self:Wnd("wtCommonCheckInstruction_1", UIWidgetBase):Wnd("DFCheckBox_Icon", DFCheckBoxOnly)
    if DFHD_LUA == 1 then
        self._wtAccessoryCheckbox:Event("OnCheckedHovered", self.OnAcceBtnTipsHoverd, self)
        self._wtAccessoryCheckbox:Event("OnCheckedUnhovered", self.OnBtnTipsUnhoverd, self)
        self._wtAccessoryCheckbox:Event("OnUncheckedHovered", self.OnAcceBtnTipsHoverd, self)
        self._wtAccessoryCheckbox:Event("OnUncheckedUnhovered", self.OnBtnTipsUnhoverd, self)
    else
        self._wtAccessoryCheckbox:Event("OnCheckStateChanged", self.OnAcceBtnTipsClicked, self)
    end
	self._wAccessoryTypeText = self:Wnd("DFTextBlock_1", UITextBlock)
	self._weaponNameList = ""
	self._accessoryTipConfig = {}

	-- 肉体伤害
	self._wtDamegePanel = self:Wnd("WBP_ItemDetailContent_Item_2", UIWidgetBase)
	self._wtDamegeText = self:Wnd("wAccessoryTypeText_1", UITextBlock)
	-- 护甲伤害
	self._wtArmorDamegePanel = self:Wnd("WBP_ItemDetailContent_Item_3", UIWidgetBase)
	self._wtArmorDamegeText = self:Wnd("wAccessoryTypeText_2", UITextBlock)

	-- 穿透衰减等级
	self._wtPiercingReduceLevelTipsPanel = self:Wnd("WBP_ItemDetailContent_Item_4", UIWidgetBase)
	self._wtPiercingReduceLevelText = self:Wnd("DFTextBlock_2", UITextBlock)

	-- 穿透衰减系数
	self._wtPiercingReduceFactorTipsPanel = self:Wnd("WBP_ItemDetailContent_Item_5", UIWidgetBase)
	self._wtPiercingReduceFactorText = self:Wnd("DFTextBlock_3", UITextBlock)
end

function ItemDetailContentBullet:OnAcceBtnTipsHoverd(bChecked)
    self:OnBtnTipsUnhoverd()
	self._handle = Module.ItemDetail:OpenItemCommonMiniTipsByTipsCfg(self._accessoryTipConfig, self._wtAccessoryCheckbox, self._wtAccessoryPanel, true, nil, nil, 40)
end

function ItemDetailContentBullet:OnAcceBtnTipsClicked(bChecked)
    if bChecked then
		self._handle = Module.ItemDetail:OpenItemCommonMiniTipsByTipsCfg(self._accessoryTipConfig, self._wtAccessoryCheckbox, self._wtAccessoryPanel, true, nil, nil, 40)
    else
        Module.ItemDetail:CloseItemTipsByHandle(self._handle)
    end
end

function ItemDetailContentBullet:OnPiercingBtnTipsHoverd(bChecked)
    self:OnBtnTipsUnhoverd()
	self._handle = Module.ItemDetail:OpenArmorPiercingTips(self._itemInfo.id, self._piercingLevel, self._wtPiercingTipsCheckbox, self._wtPiercingTipsPanel, 40)
end

function ItemDetailContentBullet:OnPiercingBtnTipsClicked(bChecked)
    if bChecked then
		self._handle = Module.ItemDetail:OpenArmorPiercingTips(self._itemInfo.id, self._piercingLevel, self._wtPiercingTipsCheckbox, self._wtPiercingTipsPanel, 40)
    else
        Module.ItemDetail:CloseItemTipsByHandle(self._handle)
    end
end

function ItemDetailContentBullet:InitTipData(uiType, tipsConfig, title)
    self._uiType = uiType
    self._tipsConfig = tipsConfig or {}
    self._title = title
end

function ItemDetailContentBullet:OnBtnTipsHoverd(bChecked)
    self:OnBtnTipsUnhoverd()
	self._handle = Module.ItemDetail:OpenArmorDamageLevelTips(self._wtTipsCheckbox, self._wtTipsPanel, 40, self._tipsConfig, self._title)
end

function ItemDetailContentBullet:OnBtnTipsUnhoverd(bChecked)
    if self._handle then
        Module.ItemDetail:CloseItemTipsByHandle(self._handle)
        self._handle = nil
    end
end

function ItemDetailContentBullet:OnBtnTipsClicked(bChecked)
    if bChecked then
		self._handle = Module.ItemDetail:OpenArmorDamageLevelTips(self._wtTipsCheckbox, self._wtTipsPanel, 40, self._tipsConfig, self._title)
    else
        Module.ItemDetail:CloseItemTipsByHandle(self._handle)
    end
end

function ItemDetailContentBullet:Reset()
	self._wBuffListPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

---@param item ItemBase
function ItemDetailContentBullet:SetItem(item, exArgs)
	self._itemInfo = item
    self:_SetDetail()
end

function ItemDetailContentBullet:_SetDetail()
	---@type BulletFeature
	local bulletFeature = self._itemInfo:GetFeature(EFeatureType.Bullet)
	if not bulletFeature then return end

	self._wtArmorPiercingLevelText:SetText(string.format(ItemDetailConfig.Loc.levelText, bulletFeature.penetrateLevel))
	self._piercingLevel = bulletFeature.penetrateLevel
	self:_SetDamageAddition()

	-- local weaponDesc
	-- if bulletFeature.specRecIds and #bulletFeature.specRecIds > 0 then
	-- 	local weaponId = bulletFeature.specRecIds[1]
	-- 	local weaponCfg = ItemConfigTool.GetItemConfigById(weaponId)
	-- 	if weaponCfg then
	-- 		weaponDesc = weaponCfg.Name
	-- 	end
	-- else
		-- weaponDesc = ItemDetailConfig.BulletType2Weapon[bulletFeature.bulletType]
	-- end
	-- if weaponDesc then
	-- 	self._wAccessoryTypeText:SetText(weaponDesc)
	-- else
	-- 	logerror("ItemDetailContentBullet use weapon err", self._itemInfo.id)
	-- end

	-- 子弹类型对应口径
	local bulletCaliber = WeaponHelperTool.GetAmmoCaliberByAmmoType(bulletFeature.bulletType) or ""
	local bulletCaliberStr = StringUtil.SequentialFormat(ItemDetailConfig.Loc.bulletCaliber, bulletCaliber)
	self._weaponNameList = self:GetBulletApplyWeapon(self._itemInfo.id)

	self._accessoryTipConfig = {
		Title = ItemDetailConfig.Loc.applyWeapon,
		TitleIconPath = nil,
		Texts = {self._weaponNameList},
	}
	self._wAccessoryTypeText:SetText(bulletCaliberStr)

	Facade.UIManager:RemoveSubUIByParent(self, self._wBuffWrap)
	if bulletFeature.buffId then
		local bBuffValid = false
		local buffList = {}
		if type(bulletFeature.buffId) == "number" then
			table.insert(buffList, bulletFeature.buffId)
		else
			buffList = bulletFeature.buffId
		end
		for _, buffId in ipairs(buffList) do
			local buffCfg = ItemConfigTool.GetBuffById(buffId)
			if buffCfg then
				Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailBufferItem, self._wBuffWrap, nil, buffCfg)
				bBuffValid = true
			end
		end
		self._wBuffListPanel:SetVisibility(bBuffValid and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
	else
		self._wBuffListPanel:SetVisibility(ESlateVisibility.Collapsed)
	end

	-- 子弹效果
	Facade.UIManager:RemoveSubUIByParent(self, self._wEffetWrap)
	local outInfo = WeaponHelperTool.GetProsAndConsByBulletId(self._itemInfo.id)
	local buffInfo = outInfo.buffInfo
    local debuffInfo = outInfo.debuffInfo
	self._wEffetListPanel:SetVisibility(ESlateVisibility.Collapsed)

	if Server.ArmedForceServer:GetCurArmedForceMode() ~= EArmedForceMode.MP then
		if buffInfo and debuffInfo then
			if #buffInfo ~= 0 or #debuffInfo ~= 0 then
				self._wEffetListPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
				for _,info in ipairs(buffInfo or {})do
					Facade.UIManager:AddSubUI(self, UIName2ID.CommonBuffDesc, self._wEffetWrap, nil, info.txt, true, info.num)
				end
				for _,info in ipairs(debuffInfo or {})do
					Facade.UIManager:AddSubUI(self, UIName2ID.CommonBuffDesc, self._wEffetWrap, nil, info.txt, false, info.num)
				end
			end
		end
	end

	local ammoRow = WeaponHelperTool.GetAmmoConfig(self._itemInfo.id)
	if ammoRow then
		local AttackLevelCorrection = attackLevelDetailCorrectionMgr:GetRowByGroupId(ammoRow.AttackGroupId)
		local armorDamageAttenuationLevel = AttackLevelCorrection.DisplayLevelTxt
		self._wtArmorDamageLevelText:SetText(armorDamageAttenuationLevel)
		local tipsConfig = {}
		local value1 = MathUtil.GetRoundingNum(AttackLevelCorrection.Level1.ArmorDifferenceParam * 100)
		local value2 = MathUtil.GetRoundingNum(AttackLevelCorrection.Level2.ArmorDifferenceParam * 100)
		local value3 = MathUtil.GetRoundingNum(AttackLevelCorrection.Level3.ArmorDifferenceParam * 100)
		local value4 = MathUtil.GetRoundingNum(AttackLevelCorrection.Level4.ArmorDifferenceParam * 100)
		local value5 = MathUtil.GetRoundingNum(AttackLevelCorrection.Level5.ArmorDifferenceParam * 100)
		local value6 = MathUtil.GetRoundingNum(AttackLevelCorrection.Level6.ArmorDifferenceParam * 100)
		table.insert(tipsConfig, {name = ItemDetailConfig.Loc.ArmorDamageLevel.Level_1, value = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.ArmorDamageLevel.DamageRate, {["DamageRate"] = value1}) })
		table.insert(tipsConfig, {name = ItemDetailConfig.Loc.ArmorDamageLevel.Level_2, value = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.ArmorDamageLevel.DamageRate, {["DamageRate"] = value2}) })
		table.insert(tipsConfig, {name = ItemDetailConfig.Loc.ArmorDamageLevel.Level_3, value = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.ArmorDamageLevel.DamageRate, {["DamageRate"] = value3}) })
		table.insert(tipsConfig, {name = ItemDetailConfig.Loc.ArmorDamageLevel.Level_4, value = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.ArmorDamageLevel.DamageRate, {["DamageRate"] = value4}) })
		table.insert(tipsConfig, {name = ItemDetailConfig.Loc.ArmorDamageLevel.Level_5, value = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.ArmorDamageLevel.DamageRate, {["DamageRate"] = value5}) })
		table.insert(tipsConfig, {name = ItemDetailConfig.Loc.ArmorDamageLevel.Level_6, value = StringUtil.Key2StrFormat(ItemDetailConfig.Loc.ArmorDamageLevel.DamageRate, {["DamageRate"] = value6}) })
		self:InitTipData(ItemDetailConfig.EWeaponTipsUIType.BulletDamageLevel, tipsConfig, ItemDetailConfig.Loc.ArmorDamageLevel.Text)

		local bPenetrateAttenuationRatio = ammoRow.AmmoDamageRule.AmmoDamageType == 2
		if bPenetrateAttenuationRatio then
			self._wtPiercingReduceLevelTipsPanel:HitTestInvisible()
			self._wtPiercingReduceFactorTipsPanel:HitTestInvisible()
			self._wtPiercingReduceLevelText:SetText(string.format(ItemDetailConfig.Loc.levelText, ammoRow.AmmoDamageRule.PenetrateAmmoLevelDecrease))
			self._wtPiercingReduceFactorText:SetText(string.format("%.0f %%", tonumber(MathUtil.GetPreciseDecimal(ammoRow.AmmoDamageRule.PenetrateAttenuationRatio, 2) * 100)))
		else
			self._wtPiercingReduceLevelTipsPanel:Collapsed()
			self._wtPiercingReduceFactorTipsPanel:Collapsed()
		end
	else
		loginfo("ItemDetailContentBullet._SetDetail ItemId not in WeaponPart/AmmoDataTable! ItemId:", self._itemInfo.id)
	end

	self._wtDamegePanel:Collapsed()
	self._wtArmorDamegePanel:Collapsed()
end

function ItemDetailContentBullet:PlayInAnim()
	self:PlayWidgetAnim(self.WBP_ContentBullet_in)
end

function ItemDetailContentBullet:_SetDamageAddition()
	local ammoDataTable = Facade.TableManager:GetTable("WeaponPart/AmmoDataTable")
	local damageRate, armorDamageRate
	if ammoDataTable then
		for _, value in pairs(ammoDataTable) do
			if self._itemInfo.id == value.ItemId then
				damageRate = value.DamageRate
				armorDamageRate = value.ArmorDamageRate
				break
			end
		end
	else
		logerror("datatable WeaponPart/AmmoDataTable not load!")
	end

	if not damageRate then
		damageRate = 0
		armorDamageRate = 0
		logerror("datatable WeaponPart/AmmoDataTable not find Item! itemId: " .. self._itemInfo.id)
	end

	-- 处理浮点型精度丢失问题，四舍五入精确到小数点第2位
	damageRate = MathUtil.GetPreciseDecimal(damageRate, 2)

	if damageRate < 0.5 then
		damageRate = 0.5
	-- elseif damageRate > 2.5 then
		-- damageRate = 2.5
	end
	local damageRatePer = string.format("%.0f %%", tonumber(damageRate*100))

	self._wtBasicDamageAdd:SetDescText(damageRatePer)
	self._wtBasicDamageAdd:SetProgress(math.min(damageRate/2.5, 1))

	-- self._wtArmorDamageAdd:SetDescText(MathUtil.GetTheSecondDecimal(armorDamageRate))
	-- self._wtArmorDamageAdd:SetProgress(armorDamageRate/3)
end

function ItemDetailContentBullet:SetBulletDamge(damage, armorDamage)
	local ammoDataTable = Facade.TableManager:GetTable("WeaponPart/AmmoDataTable")
	local damageRate, armorDamageRate, projectileNum
	if ammoDataTable then
		for _, value in pairs(ammoDataTable) do
			if self._itemInfo.id == value.ItemId then
				damageRate = value.DamageRate
				armorDamageRate = value.ArmorDamageRate
				projectileNum = value.ProjectileNum
				break
			end
		end
	else
		logerror("datatable WeaponPart/AmmoDataTable not load!")
	end

	if not damageRate then
		damageRate = 1
		armorDamageRate = 1
		logerror("datatable WeaponPart/AmmoDataTable not find Item! itemId: " .. self._itemInfo.id)
	end

	local bulletDamage = math.round(damage * damageRate)
	local bulletArmorDamageRate = math.round(armorDamage * armorDamageRate)
	local StrHarmValue = tostring(bulletDamage)
	local StrArmorDamageValue = tostring(bulletArmorDamageRate)
	local strProjectileNum = nil

	if projectileNum and projectileNum > 1 then
		local StrVaule = tostring(projectileNum)
		strProjectileNum = string.gsub(StrVaule, "%.0+$", "")
		StrHarmValue = StringUtil.SequentialFormat("{0}*{1}", StrHarmValue, strProjectileNum)
		StrArmorDamageValue = StringUtil.SequentialFormat("{0}*{1}", StrArmorDamageValue, strProjectileNum)

	end

	self._wtDamegePanel:SelfHitTestInvisible()
	self._wtDamegeText:SetText(StrHarmValue)

	self._wtArmorDamegePanel:SelfHitTestInvisible()
	self._wtArmorDamegeText:SetText(StrArmorDamageValue)
end

function ItemDetailContentBullet:GetBulletApplyWeapon(id)
    local weaponNameList = ""
	local AmmoRow = WeaponHelperTool.GetAmmoConfig(id)
	if AmmoRow then
		local WeaponAttributeTable = Facade.TableManager:GetTable("WeaponBase/WeaponAttributeTable")
		for k, Row in pairs(WeaponAttributeTable) do
			if Row.AmmoType == AmmoRow.Type then
				if weaponNameList ~= "" then
					weaponNameList = StringUtil.SequentialFormat("{0}, {1}", weaponNameList, ItemConfigTool.GetItemName(Row.RecId))
				else
					weaponNameList = StringUtil.SequentialFormat("{0}", ItemConfigTool.GetItemName(Row.RecId))
				end
			end
		end
	end
    return weaponNameList
end

function ItemDetailContentBullet:OnClose()
	Facade.UIManager:ClearSubUIByParent(self,self._wBuffWrap)
end


return ItemDetailContentBullet