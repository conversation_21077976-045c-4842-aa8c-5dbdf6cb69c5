----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLitePackage)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LiteDownloadNormalItem : LuaUIBaseView
local LiteDownloadNormalItem = ui("LiteDownloadNormalItem")
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local CommonDownloadBtn = require "DFM.Business.Module.LitePackageModule.UI.CommonDownloadBtn"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local LiteDownloadMBNum = 1024 * 1024
local LiteDownloadGBNum = LiteDownloadMBNum * 1024
local LitePackageConfig = Module.LitePackage.Config
local num_per_row = 2
local DownLoadBtnType = {
    Downloaded = 0,
    WaitDownload = 1,
    Downloading = 2,
}

--标记在哪个面板显示，用于处理不同场景下的不同样式
local PanelType = {
    DownloadCenter = 0, --下载中心
    LobbyDownload = 1, --大厅下载
    CleanUp = 2, --清理界面
}

function LiteDownloadNormalItem:Ctor()
    self._wtQuestName = self:Wnd("TextBlock_QuestName", UITextBlock)
    self:SetCppValue("bPreciseClick", true)
    self._wtOverlapIcon = self:Wnd("Image_Icon", UIWidgetBase)
    self._wtDownloadBtn = self:Wnd("WBP_Common_DownloadButton", CommonDownloadBtn)
    self._wtDownloadBtn:Wnd("Button_Download", UIButton):Event("OnClicked",self._OnDownloadBtnClick,self)
    self._wtDownloadBtnTxt = self._wtDownloadBtn:Wnd("TextBlock_DownloadInfo", UITextBlock)
    self._wtOverLapBtn = self:Wnd("DFButton_Download", UIButton)
    self._wtOverLapBtn:Event("OnClicked",self._SetOverLapState,self)
    self._wtPakGrid = self:Wnd("DFUniformGridPanel_41", UIWidgetBase)
    self._wtCleaningCheckBox = self:Wnd("wtDFCommonCheckBoxWithText", UICheckBox)
    self._wtCleaningCheckBox:Event("OnCheckStateChanged", self.SelectedCleanUp, self)
    self._wtCleaningTxt= self:Wnd("TextBlock_NormalSize", UITextBlock)
    self._wtReddotRoot = self:Wnd("DFCanvasPanel_110", UIWidgetBase)
    self.downloadBtnType = DownLoadBtnType.WaitDownload
    self._wtDownloadBtn:SetType(0)
    self._PakItems = {}
    self._pakItemsInfo = {}
    self._bSelected = false -- 是否被选中（用于清理界面）
    self.panelType = PanelType.LobbyDownload
    self._cleanUpNum = 0
end

---@overload fun(LuaUIBaseView, OnOpen)
function LiteDownloadNormalItem:OnOpen()
    self:AddListeners()
end

function LiteDownloadNormalItem:OnShowBegin()
    -- 定时刷新功能
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
    self:RefreshState()
    self.tickHandle = Timer:NewIns(0.5, 0)
    self.tickHandle:AddListener(function()
        self:RefreshState()
    end, self)
    self.tickHandle:Start()
    
end

function LiteDownloadNormalItem:OnHide()
    self:RemoveAllLuaEvent()
    if self.tickHandle then
        self.tickHandle:Release()
        self.tickHandle = nil
    end
    Facade.UIManager:RemoveSubUIByParent(self, self._wtPakGrid)
end

---@param liteDownloadConfig any
function LiteDownloadNormalItem:RefreshItem(CategoryPaks,categoryID,autoDownloadCategoryId,autoDownloadModuleKey,bShowReddot)
    Facade.UIManager:RemoveSubUIByParent(self,self._wtPakGrid)
    self._PakItems = {}
    self._pakItemsInfo = {}
    self._category = categoryID
    self._categoryPaks = CategoryPaks
    if not CategoryPaks.CategoryTitle then
        CategoryPaks.CategoryTitle = ""
    end
    self._wtQuestName:SetText(CategoryPaks.CategoryTitle)
    if self._category == autoDownloadCategoryId then
        self:_SetOverLapState()
    end
    local i = 1
    for _, pak in pairs(CategoryPaks.Paks) do
        local row_index = (i - 1) // num_per_row  -- 行索引
        local column_index = (i - 1) % num_per_row  -- 列索引
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.LitePackageDownloadItem, self._wtPakGrid)
        local DownloadItem = getfromweak(weakUIIns)
        pak.bShowReddot = self:GetReddotStr(pak.ModuleKey,pak.bShowReddot)
        if DownloadItem then
            local PakInfo = {
                DisplayName = pak.QuestName,
                ModuleKey =  pak.ModuleKey,
                Category = pak.ShowCategory,
                bShowReddot = pak.bShowReddot
            }
            local isAutoDownload=self._category==autoDownloadCategoryId and pak.ModuleKey==autoDownloadModuleKey
            DownloadItem:InitItem(PakInfo,nil,isAutoDownload,bShowReddot)
            DownloadItem.Slot:SetHorizontalAlignment(EHorizontalAlignment.HAlign_Fill)
            DownloadItem.Slot:SetRow(row_index)
            DownloadItem.Slot:SetColumn(column_index)
            table.insert(self._PakItems, DownloadItem)
            table.insert(self._pakItemsInfo, pak)
        end
        i = i + 1
    end
    if bShowReddot and Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Login then
        self._wtRedDotWidget = Module.ReddotTrie:CreateReddotIns(self._wtReddotRoot)
    end
    if self._wtRedDotWidget then
        if self:CheckCategoryReddot(self._category) then
            self._wtRedDotWidget:SetReddotVisible(true,EReddotType.Normal)
        else
            self._wtRedDotWidget:SetReddotVisible(false,EReddotType.Normal)
        end
    end

    self:RefreshState()
    self:OnReddotStatechanged()--刷新一下红点
end

function LiteDownloadNormalItem:GetReddotStr(moduleKey,bShowReddot)--红点是否显示
    if LiteDownloadManager:IsHDDownloadStatus() then
        moduleKey = LiteDownloadManager:GetHDRuntimeNameByModuleName(moduleKey)
    end
    local key = moduleKey.."ShowReddot"
    -- if Facade.ConfigManager:GetString(key,"") == "" then
    --     Facade.ConfigManager:SetString(key,tostring(bShowReddot))
    --     return bShowReddot
    -- else

    -- end
    local bShow = (Facade.ConfigManager:GetString(key,"")=="true" and true or false)
    bShow = ( bShow and not LiteDownloadManager:IsDownloadedByModuleName(moduleKey))
    return bShow
end



function LiteDownloadNormalItem:SetPaksDownloadState()
    --local bHasDownloadPak = false
    local moduleKeys = {}
    for i = 1, #self._PakItems do 
        if  not LiteDownloadManager:IsDownloadedByModuleName(self._pakItemsInfo[i].ModuleKey) then
            table.insert(moduleKeys, self._pakItemsInfo[i].ModuleKey)
        end
    end
    if self.downloadBtnType == DownLoadBtnType.Downloading then --表示该类型资源处于下载状态,否则为暂停下载
        -- 计算资源大小，超过3G则弹窗提醒 allardwang
        -- check space > 3G
        local WIFI_DOWNLOAD_SPACE_GB = 3
        local LiteDownloadGBNum = 1024 * 1024 * 1024
        local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
        local spaceGB = freeSpace / LiteDownloadGBNum


        local allTotalSizeToDownlaod = LiteDownloadManager:GetMultiModuleRemainderSize(moduleKeys)
        local questSize = allTotalSizeToDownlaod / LiteDownloadGBNum
        if spaceGB < WIFI_DOWNLOAD_SPACE_GB + questSize then
            local confirmHandle = function()
            end

            LogAnalysisTool.SignButtonClicked(10200001)
            local content = LitePackageConfig.Loc.LITE_DOWNLOAD_CHECK_SPACE_NOT_ENOUGH
            Module.CommonTips:ShowConfirmWindowWithSingleBtn(content, confirmHandle, nil)
            return
        else
            LiteDownloadManager:CheckAndDownloadAll(moduleKeys)
        end
    elseif self.downloadBtnType == DownLoadBtnType.WaitDownload  then
        for _, modulekey in pairs(moduleKeys) do
            LiteDownloadManager:CancelByModuleName(modulekey)
        end
    end
end

function LiteDownloadNormalItem:InitPanelState() -- 用于区别展示清理界面和下载界面
    if self.panelType == PanelType.CleanUp then --在资源清理面板下
        self._wtDownloadBtn:Collapsed()
        self._wtCleaningCheckBox:SelfHitTestInvisible()
        self._wtCleaningTxt:SelfHitTestInvisible()
        self:SetCategoryDownloadedSizeTxt(self._bSelected)
    end
end

function LiteDownloadNormalItem:SetCategoryDownloadedSizeTxt(bSelected) --用于在资源清理面面板下显示该类别已下载所有包体总大小
    local CategoryDownloadedSize = 0
    for i = 1, #self._categoryPaks.Paks do
        local totalSize = LiteDownloadManager:GetTotalSizeByModuleName(self._categoryPaks.Paks[i].ModuleKey)
        CategoryDownloadedSize = CategoryDownloadedSize + totalSize
    end

    if not bSelected then
        self._wtCleaningTxt:SetText(string.format("%.1fMB", CategoryDownloadedSize / LiteDownloadMBNum))
        return
    end
    self._wtCleaningTxt:SetText(string.format("%.1fMB", CategoryDownloadedSize / LiteDownloadMBNum))
end


function LiteDownloadNormalItem:RefreshState()
    if not (self.panelType == PanelType.CleanUp) then -- 非清理界面才需刷新下载状态
        self:CheckIsDownloadedAll()
    end
end

function LiteDownloadNormalItem:_OnDownloadBtnClick()
    local pufferInitSucceed = LiteDownloadManager:IsPufferInitSucceed()
    if pufferInitSucceed == false then
        return
    end

    if self.downloadBtnType == DownLoadBtnType.WaitDownload then
        self:ProceedDownload()
    elseif self.downloadBtnType == DownLoadBtnType.Downloading then
        self:WaitDownload()
    end
    self:SetPaksDownloadState()
    self:RefreshState()
    Module.LitePackage.Config.evtPakDownloadTrigged:Invoke()
end

function LiteDownloadNormalItem:_OnDownloadBtnDeClick()
    Module.CommonTips:ShowSimpleTip(LitePackageConfig.Loc.LitePackageMainPanel_Download_WaitForHDDownloading)
end

function LiteDownloadNormalItem:_SetOverLapState() -- 负责对资源列表的展开与折叠
    if self._wtPakGrid:GetVisibility() == ESlateVisibility.Collapsed then
        self._wtPakGrid:SelfHitTestInvisible()
        self._wtOverlapIcon:SetRenderTransformAngle(90)
    else
        self._wtPakGrid:Collapsed()
        self._wtOverlapIcon:SetRenderTransformAngle(0)
    end
    LitePackageConfig.evtDownloadCategoryOverlaped:Invoke()
end


function LiteDownloadNormalItem:ProceedDownload()
    self._wtDownloadBtn:SetType(1)
    self.downloadBtnType = DownLoadBtnType.Downloading
end

function LiteDownloadNormalItem:WaitDownload() --暂停样式，玩家主动暂停下载时显示
    self._wtDownloadBtn:SetType(2)
    self.downloadBtnType = DownLoadBtnType.WaitDownload
end

function LiteDownloadNormalItem:CheckIsDownloadedAll() --检查全部下载状态，同步剩余大小
    if not self._categoryPaks then
        return
    end
    local bHasDownloadPak = false --当前类别下是否有包体下载
    local bDownloadedAll = true -- 用来标识该类别资源是否全部下载完
    local restOfCategorySize = 0
    local notFinishedNum = 0 --统计未下载完的数量
    local nowSize = 0
    local totalSize = 0
    local allModulekeys = {}
    for i = 1, #self._categoryPaks.Paks do
        if LiteDownloadManager:IsDownloadingByModuleName(self._categoryPaks.Paks[i].ModuleKey) then
            bHasDownloadPak = true
        end
        if not LiteDownloadManager:IsDownloadedByModuleName(self._categoryPaks.Paks[i].ModuleKey) then
            bDownloadedAll = false
            notFinishedNum = notFinishedNum + 1
        end

        table.insert(allModulekeys, self._categoryPaks.Paks[i].ModuleKey)
        nowSize = nowSize + LiteDownloadManager:GetNowSizeByModuleName(self._categoryPaks.Paks[i].ModuleKey)
        totalSize = totalSize + LiteDownloadManager:GetTotalSizeByModuleName(self._categoryPaks.Paks[i].ModuleKey)
    end

    restOfCategorySize = LiteDownloadManager:GetMultiModuleRemainderSize(allModulekeys)
    if bHasDownloadPak then
        self:ProceedDownload()
    elseif self.downloadBtnType ~= DownLoadBtnType.WaitDownload then  --非玩家主动暂停，显示为待下载状态
        self._wtDownloadBtn:SetType(0)
        self.downloadBtnType = DownLoadBtnType.WaitDownload
    end

    if bDownloadedAll then
        self._wtDownloadBtn:Collapsed()
    else
        self._wtDownloadBtn:SelfHitTestInvisible()
        if restOfCategorySize / LiteDownloadGBNum > 1 then
            self._wtDownloadBtnTxt:SetText(string.format("%.1fGB", restOfCategorySize / LiteDownloadGBNum))
        else
            self._wtDownloadBtnTxt:SetText(string.format("%.1fMB", restOfCategorySize / LiteDownloadMBNum))
        end

    end

    if self.panelType == PanelType.DownloadCenter then --处于下载中心时显示已下载与包体的总数
        self._wtQuestName:SetText(string.format("%s (%s/%s)",self._categoryPaks.CategoryTitle,(#self._categoryPaks.Paks - notFinishedNum) ,#self._categoryPaks.Paks))
    else
        self._wtQuestName:SetText(self._categoryPaks.CategoryTitle)
    end
    -- info 用于计算下载百分比，在按钮上显示下载进度
    local info = {
        PercentValue = math.round(nowSize / totalSize * 1000) / 10
    }
    self:SetDownloadProgress(info)
end

function LiteDownloadNormalItem:SelectedCleanUp(bSelected)
    for _,pakItem in pairs(self._PakItems) do
        pakItem:SetCleanUpCheck(bSelected)
    end
    if bSelected then
        self._cleanUpNum = #self._PakItems
    else
        self._cleanUpNum = 0
    end
    self:SetCategoryDownloadedSizeTxt(bSelected)
    LitePackageConfig.evtSelectedAllCategoryPaks:Invoke(bSelected,self._categoryPaks.ShowCategoryID)
end

function LiteDownloadNormalItem:SetIsChecked(bSelected)
    self._wtCleaningCheckBox:SetIsChecked(bSelected,true)
end

function LiteDownloadNormalItem:SetIsEnabled(bEnabled)
    self._wtDownloadBtn:Wnd("Button_Download", UIButton):SetIsEnabled(bEnabled)
    for _,pakItem in pairs(self._PakItems) do
        pakItem:SetIsEnabled(bEnabled)
    end
end

function LiteDownloadNormalItem:SetPanelType(panelType)
    self.panelType = panelType
    self:InitPanelState()
    if self.panelType == PanelType.CleanUp then
        self:SetIsChecked(self._categoryPaks.bSelected)
        for _,pakItem in pairs(self._PakItems) do --若该类型已勾选，则该类型下的所有包体都勾选
            pakItem:InitCleanUpState()
            pakItem:SetCleanUpCheck(self._categoryPaks.bSelected)
        end
    end
    if self._categoryPaks.bSelected then
        self._cleanUpNum = #self._PakItems
    end
end

function LiteDownloadNormalItem:SetDownloadProgress(info)
    self._wtDownloadBtn:OnlySetPercent(info)
end

--- 单独选中清理某一包体时触发
function LiteDownloadNormalItem:_OnSelectedCleanPak(bSelected,moduleKey,category)

    if self._category and self._category == category  then
        if bSelected then
            self._cleanUpNum = self._cleanUpNum + 1
        else
            self._cleanUpNum = self._cleanUpNum - 1
        end
        self:SetIsChecked(self._cleanUpNum == #self._pakItemsInfo) --若选择的清理包体数量等于该类型所有包体的数量，则勾选该类型
    end
end


function LiteDownloadNormalItem:OnClose()
    self._PakItems = {}
    self._pakItemsInfo = {}
end


function LiteDownloadNormalItem:AddListeners()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadItemSelected, self._OnSelectedCleanPak, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtReddotChanged, self.OnReddotStatechanged, self)
end


function LiteDownloadNormalItem:OnReddotStatechanged()
    local bShow = self:CheckCategoryReddot()
    if self._wtRedDotWidget then
        if bShow then
            self._wtRedDotWidget:SetReddotVisible(true,EReddotType.Normal)
        else
            self._wtRedDotWidget:SetReddotVisible(false,EReddotType.Normal)
        end
    end

end

function LiteDownloadNormalItem:CheckCategoryReddot()
    local LiteDownloadDataTable = Facade.TableManager:GetTable("LitePackageDownload")
    for _, pak in pairs(LiteDownloadDataTable) do
        if pak.ShowCategory == self._category then
            local key = pak.ModuleKey
            local moduleName = ""
            if LiteDownloadManager:IsHDDownloadStatus() then
                modulename = LiteDownloadManager:GetHDRuntimeNameByModuleName(key)
            end
            key = modulename.."ShowReddot"
            if Facade.ConfigManager:GetString(key,"") == "true" and not (LiteDownloadManager:IsDownloadedByModuleName(moduleName)) then
                    return true
            end
        end
   end
   
   return false
end


return LiteDownloadNormalItem
