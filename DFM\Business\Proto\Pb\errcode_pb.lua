require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.errcode_editor_pb"
end

Err = {
Succeed = 0,
JoinMatchLoadProfileFailed = 10005,
JoinMatchDSRoomInvalid = 10006,
GetMatchRoomDBFailed = 10008,
SetMatchRoomDBFailed = 10009,
ServerDisabled = 10010,
CallTimedOut = 10011,
InvalidCallResp = 10012,
InventoryProxyInternalErr = 10013,
ServiceDisabledForVerUpdate = 10014,
InventoryCallRemoteFailed = 10015,
RoomNotExist = 11001,
RoomJoinFailed = 11002,
RoomPlayerNotExist = 11003,
RoomPermissionInvalid = 11004,
RoomRepeatedCreate = 11005,
RoomDBOptionFailed = 11006,
RoomFull = 11007,
RoomPasswordError = 11008,
RoomPasswordInvalid = 11009,
RoomSeatUsed = 11010,
RoomMatchIDInvalid = 11011,
RoomMatchIDNotMatchPlayerNum = 11012,
RoomInMatchState = 11013,
RoomJoinWaitTime = 11014,
RoomStateWaitDS = 11015,
RoomLoadConfigFailed = 11016,
RoomCallStateFailed = 11017,
RoomNotIdle = 11018,
RoomClose = 11019,
RoomPlayerInRoom = 11020,
RoomInviteeInTeam = 11021,
RoomInviteeInRoom = 11022,
RoomInviteeInMatching = 11023,
RoomGetDBFailed = 11024,
RoomSaveDBFailed = 11025,
RoomVersionNotMatch = 11026,
RoomInviteeInFace2FaceRoom = 11027,
RoomCreateRoomIDFailed = 11028,
RoomServiceInMaintenance = 11029,
RoomCallMatchRoomFailed = 11030,
RoomNameDirty = 11031,
RoomCallTssSvrFailed = 11032,
RoomGenDSRoomIDFail = 11033,
RoomInvalidReqParam = 11034,
RoomInviteeGetStatusFail = 11035,
RoomServerBusy = 11036,
RoomMaxOBNumInvalid = 11037,
RoomPlayerNotReady = 11038,
RoomPlayerOverflow = 11039,
RoomNotAllowEditMode = 11040,
RoomNameNeedToUseDefualt = 11041,
RoomTooManyOB = 11042,
RoomTeamNameDirty = 11043,
RoomMaxPlayerNumLessThanCurPlayerNum = 11044,
RoomMaxPlayerNumInvalid = 11045,
RoomTeamInfoInvalid = 11046,
RoomMidSupplyFail = 11047,
RoomChangeSeatFreq = 11048,
TeamNotExist = 12001,
TeamFull = 12002,
TeamMemberNotReady = 12003,
TeamPermissionInvalid = 12004,
TeamPlayerNotExist = 12005,
TeamStateInvalid = 12006,
TeamCallServiceFailed = 12007,
TeamMatchIDInvalid = 12008,
TeamMemberSCAVInvalid = 12009,
TeamAlreadyInTeam = 12010,
TeamInviteeInTeam = 12011,
TeamInviteeInRoom = 12012,
TeamInviteeInMatching = 12013,
TeamAllOffineReleased = 12014,
TeamGetDBFailed = 12015,
TeamSaveDBFailed = 12016,
TeamBusy = 12017,
TeamVersionNotMatch = 12018,
TeamApplyPlayerOffline = 12019,
TeamInviterOffline = 12020,
TeamInviteeInFace2FaceRoom = 12021,
TeamPlayerOffline = 12022,
TeamStateNotIdle = 12023,
TeamPlatGroupNotMatch = 12024,
TeamSocialForbidden = 12025,
TeamServiceInMaintenance = 12026,
TeamGetSeasonInfoFailed = 12027,
TeamPackQuestMiss = 12028,
TeamEquipNotEnough = 12029,
TeamMatchPunishFailed = 12030,
TeamRecruitmentExpire = 12031,
TeamPlayerNotPassGuide = 12032,
TeamRecruitmentPostTypeNotSupport = 12033,
TeamInviteeStateInvalid = 12034,
TeamInviterStateInvalid = 12035,
TeamRecruitmentNotOpen = 12036,
TeamPlayerStateInvalid = 12037,
TeamRecruitmentAlreadyOpen = 12038,
TeamJoinRecruitmentStateInvalid = 12039,
TeamParamInvalid = 12040,
TeamNotUnlockRaid = 12041,
TeamSocialForbiddenPeer = 12042,
TeamLimitByReputation = 12043,
TeamQuickJoinNotOpen = 12044,
TeamNotUnlockBhd = 12045,
TeamNotBoughtBhd = 12046,
TeamBhdVersionInvalid = 12047,
TeamBhdNextLevelReadyFail = 12048,
TeamBhdNextLevelIsRuning = 12049,
TeamBhdServerIsFull = 12050,
TeamBhdServiceIsNotOpen = 12051,
TeamBhdPlatIDNotValid = 12052,
TeamGetSpecialHeroFailed = 12053,
TeamNeedFaceVerify = 12054,
TeamPlayerInMultiplayerTeam = 12055,
TeamPlayerNotInTeam = 12056,
TeamShortNumInvalid = 12057,
TeamShortNumJoinFailed = 12058,
TeamGetShortNumFailed = 12059,
MatchTimeout = 13001,
MatchMatchIDInvalid = 13002,
MatchServiceClose = 13003,
MatchCallDSAFailed = 13004,
MatchRoomNotExist = 13005,
MatchRoomPlayerNotExist = 13006,
MatchCallNumeralFailed = 13007,
MatchNodeNameNone = 13008,
MatchRepeatedAlloc = 13009,
MatchServiceFull = 13010,
MatchCallDBFailed = 13011,
MatchTeamInvalidSize = 13012,
MatchCannotFindPlayer = 13013,
MatchCannotFindTeam = 13014,
MatchGetDBFailed = 13015,
MatchInValidGameMode = 13016,
MatchNotFoundConfig = 13017,
MatchGenBeginMatch = 13018,
MatchCallMatchRoomFailed = 13019,
MatchPlatGroupNotMatch = 13020,
MatchNodeIDInvalid = 13021,
MatchNodeNotExist = 13022,
MatchPlayerNotInMatching = 13023,
MatchBusy = 13024,
MatchPrepareCreateRoomFail = 13025,
MatchGenDSRoomIDFail = 13026,
MatchReqPlayerIsNull = 13027,
MatchTeamMemberCancelMatching = 13028,
DepositInitFailed = 14001,
DepositAddPropInvalid = 14002,
DepositPositionNotExist = 14003,
DepositPresetNotFound = 14004,
DepositPropCanNotStack = 14005,
DepositPropStackFull = 14006,
DepositPropCanNotPutIntoPos = 14007,
DepositPropDescNotFound = 14008,
DepositCurrencyIdInvalid = 14009,
DepositCurrencyNotEnough = 14010,
DepositPropNotComplete = 14011,
DepositPropNotFound = 14012,
DepositPropNotEnough = 14013,
DepositDelPropsFailed = 14014,
DepositSaveDepositFailed = 14015,
DepositEquipPosNotMatch = 14016,
DepositSwapPropFialed = 14017,
DepositNoSafeBoxEquiped = 14018,
DepositEquipmentPartDisabled = 14019,
DepositCompoundCmdInvalid = 14020,
DepositCompoundRootNotFound = 14021,
DepositCompoundNodeNotFound = 14022,
DepositCompoundSlotNotFound = 14023,
DepositCompoundSlotNotEmpty = 14024,
DepositDBNeedInit = 14025,
DepositInvalidReq = 14026,
DepositSortPosFailed = 14027,
DepositRepairDescNotFound = 14028,
DepositDoNotNeedRepair = 14029,
DepositCanNotRepair = 14030,
DepositMoneyNotEnough = 14031,
DepositSpaceNotEnough = 14032,
DepositSpaceHasOccupied = 14034,
DepositCanNotPutIntoSpace = 14035,
DepositNotSupportFeature = 14036,
DepositInvalidPositionTag = 14037,
DepositNotAllowModity = 14038,
DepositExtensionExceed = 14039,
DepositHealthNotFull = 14040,
DepositBindTypeInvalid = 14041,
DepositBindTypeDismatch = 14042,
DepositGenerateGidFailed = 14043,
DepositCantAddModelOnlyProp = 14044,
DepositHasNotDefaultEquipment = 14045,
DepositNewPropFailed = 14046,
DepositNotScavMode = 14047,
DepositInternalError = 14048,
DepositPropSpaceCantUnlock = 14049,
DepositNeedGid = 14050,
DepositAddPosFailed = 14051,
DepositRemovePropFailed = 14052,
DepositSpaceNotFound = 14053,
DepositPropPositionMapInvalid = 14054,
DepositSwapAddFailed = 14055,
DepositSendMailFailed = 14056,
DepositNoAddOptSucc = 14057,
DepositNewSpaceCanNotHold = 14058,
DepositPropSpaceUnlockDescNotFound = 14059,
DepositDefaultPropUnequip = 14060,
DepositDefaultPropDel = 14061,
DepositExtensionCantUpgrade = 14062,
DepositExtensionMatInvalid = 14063,
DepositSwapMultiPropsFailed = 14064,
DepositGetDBFailed = 14065,
DepositReplaceExtensionFailed = 14066,
DepositWeaponArmedForceNotMatch = 14067,
DepositHeroNotEquip = 14068,
DepositHeroNotRoleLoadConfig = 14069,
DepositCurrencyOverFlow = 14070,
DepositCallHeroFailed = 14071,
DepositCallHeroLogicFailed = 14072,
DepositPropNotAllowedIntoDS = 14073,
DepositGuidePropInvalidMove = 14074,
DepositInvalidMeleeWeaponOperation = 14075,
DepositMeleeWeaponLocked = 14076,
DepositWeaponSlotConflict = 14077,
DepositBusy = 14078,
DepositSendTeammatePropsBackFailed = 14079,
DepositUpdateWeaponAttrValueInvalid = 14080,
DepositWeaponHasNoMagazineCapacity = 14081,
DepositWeaponBulletNotEnoughToDel = 14082,
DepositBulletNotMatchWithWeapon = 14083,
DepositPermissionPeriodOverLimit = 14084,
DepositUsePermissionExpired = 14085,
DepositCallCollectionFailed = 14086,
DepositWeaponCantEquip = 14087,
DepositSeasonExpireTimeCannotRenew = 14088,
DepositNotCurrSeason = 14089,
DepositCallArmedForceFailed = 14090,
DepositApplyRentalEquipFailed = 14091,
DepositCompleteRentalEquipFailed = 14092,
DepositRentalButNotAllUnEquiped = 14093,
DepositInvalidSavedCarryIn = 14094,
DepositPropBindTypeNotMatch = 14095,
DepositSavedCarryInUsed = 14096,
DepositEquipVipExtensionBoxFailed = 14097,
******************************** = 14098,
DepositExtensionOperInvalid = 14099,
DepositVipRightRequired = 14100,
DepositForbiddenOperInMatch = 14101,
DepositSavedCarryInRoomIDNotMatch = 14102,
DepositSavedCarryInExpired = 14103,
DepositRepairInterMediateLocked = 14104,
DepositNotTotallyMineCannotOperate = 14105,
DepositSavedCarryInInfoNotExist = 14106,
DepositSettlementBugReissue = 14107,
DepositSettlementBugReissueFail = 14108,
DepositSettlementHighLvBodyCapitalNotEnough = 14109,
DepositSettlementBodyInContainerCapitalTooMuch = 14110,
DepositSeasonDescNotFound = 14111,
DepositPickMoveSpaceNotEnough = 14112,
DepositPropOthersNotAllowedIntoDS = 14113,
DepositCarryInFeeDuplicate = 14114,
DepositCarryInFeeNotExist = 14115,
DepositEntryPropLevelLimit = 14116,
DepositInvalidRecoveryAmount = 14117,
DepositInvalidRecoveryLevel = 14118,
DepositRecoveryLevelNotEnable = 14119,
DepositRecoverySystemDisable = 14120,
DepositEntryPropNightVisionLimit = 14121,
DepositEntryPropThermalImagingLimit = 14122,
DepositRenewForeverSafeBox = 14123,
DepositExceedMaxCarryInFeeRecord = 14124,
DepositCanNotPutIntoAssociatedSpace = 14125,
DepositInvalidShowCabinetType = 14133,
DepositInvalidShowGridNum = 14126,
DepositInvalidShowCabinetID = 14127,
DepositInvalidShelveShowCabinetReq = 14128,
DepositShelveShowCabinetPickupFailed = 14129,
DepositInvalidShowGridId = 14130,
DepositShowGridIdInconsistent = 14131,
DepositInvalidShowItemId = 14132,
DepositShowDIYGridSizeLimit = 14134,
DepositShowInvalidBindType = 14135,
DepositShowCabinetLocked = 14136,
DepositShowGridNotEmpty = 14137,
DepositInvalidShowDepositLocation = 14138,
DepositShowGridEmpty = 14139,
DepositLevelUpShowCabinetInvalidItemId = 14140,
DepositShowExceedMaxGridLevel = 14150,
DepositLevelUpShowGridConfNotFound = 14151,
DepositLevelUpShowGridCostInconsistent = 14152,
DepositMultiGIDProp = 14153,
DepositTryPutOnGunFailed = 14154,
DepositShowDIYBanItem = 14155,
DepositSwapMultiPropsInvalidCoordinate = 14156,
DepositShowGridLevelUpInvalidItem = 14157,
DepositShowExceedSubmitGridCostItem = 14158,
DepositShowGridLevelUpConfNotFound = 14159,
DepositShowGridLevelUpCostItemNotEnough = 14160,
DepositShowGridLevelUpDeny = 14161,
DepositEntryPropBannedItem = 14162,
DepositEntryPropBannedItemType = 14163,
DepositEntryPropBannedItemNum = 14164,
DepositUnApplyMeleeSkinInvalidReq = 14170,
DepositUnApplyMeleeInitSkinNotFound = 14171,
DepositUnApplyMeleeNotFound = 14172,
DepositNetbarPrivSafeBoxNotAllow = 14173,
AccountNotInWhiteList = 15001,
AccountParseGOpenidFailed = 15002,
AccountFetchProfileFailed = 15003,
AccountSaveProfileFailed = 15004,
AccountInsertProfileFailed = 15005,
AccountGenPlayeridByIdmapFailed = 15006,
AccountInsertSessionFailed = 15007,
AccountSaveSessionFailed = 15008,
AccountFetchSessionFailed = 15009,
AccountPlayerMgrAddPlayerFailed = 15010,
AccountRepeatLoginReq = 15011,
AccountInvalidGameNick = 15012,
AccountIllegalSeaon = 15013,
AccountGameNickTooLong = 15014,
AccountGameNickExisted = 15015,
AccountUpdateDBGameNickFailed = 15016,
AccountGetLevelPrizeFailed = 15017,
AccountAddLevelPrizeFailed = 15018,
AccountProfileNotExist = 15019,
AccountSeasonInfoNotInit = 15020,
AccountTerminateSessionFailed = 15021,
AccountGameNickEmpty = 15022,
AccountServerFull = 15023,
AccountPlayerNotRegistered = 15024,
AccountLimitLogin = 15025,
AccountCallTssSvrFailed = 15026,
AccountNickTssFailed = 15027,
AccountNickTssDirty = 15028,
AccountNickInvalid = 15029,
AccountInvalidLanguage = 15030,
AccountRandNickFailed = 15031,
AccountInvalidPayToken = 15032,
AccountNickInvalidUtf8 = 15033,
AccountConfNotFound = 15034,
AccountUnverified = 15035,
AccountForbidden = 15036,
AccountCallHopeFailed = 15037,
AccountPlayerRegisterFailed = 15038,
AccountHopeNoClientInfo = 15039,
AccountBusy = 15040,
AccountNeedUpdateClientVersion = 15041,
AccountIllegalLimitTimeArg = 15042,
AccountFetchNickFailed = 15043,
AccountPunishParamsInvalid = 15044,
AccountModifyNickNameForbidden = 15045,
AccountIpCityLimitLogin = 15046,
AccountQueryOnlineNumFail = 15047,
AccountNickTssCallFailed = 15048,
AccountCantDegrade = 15049,
AccountAddExpFailed = 15050,
AccountSettleAddExpFailed = 15051,
AccountLoadDBSeasonInfoFailed = 15052,
AccountSaveDBSeasonInfoFailed = 15053,
AccountExceedRegisterLimit = 15054,
AccountInvalidReq = 15055,
AccountAlreadyRegister = 15056,
AccountQuerySeasonInfoFailed = 15057,
AccountNickAlphaDigitAndChinese = 15058,
AccountRegisterTimeLimit = 15059,
AccountLoginServiceClosed = 15060,
AccountLoadDBQualificationInfoFailed = 15061,
AccountSaveDBQualificationInfoFailed = 15062,
AccountNickNotChange = 15063,
AccountDelRenameCardFailed = 15064,
AccountModifyPicUrlForbidden = 15065,
AccountNickNotInUnicodeWhiteList = 15066,
AccountNickInUnicodeBlackList = 15067,
AccountCallDepositFailed = 15068,
AccountLoginUnknownError = 15099,
AccountInvalidOpenid = 15091,
AccountCallCollectionFailed = 15092,
AccountAvatarNotUnlock = 15093,
AccountLoadDBMiscDataFailed = 15094,
AccountSaveDBMiscDataFailed = 15095,
AccountMsdkPicUrlInvalid = 15096,
AccountLoadDBPlayerInfoFailed = 15097,
AccountAvatarAlreadyEquip = 15098,
AccountInvalidClientVersion = 15100,
AccountForbiddenLowVersionLogin = 15101,
AccountUpdateMasterVersion = 15102,
AccountForbidLoginPlatform = 15103,
AccountLoadDBConnectionFailed = 15104,
AccountSaveDBConnectionFailed = 15105,
AccountInconsistentConn = 15107,
AccountKickConnInRoom = 15108,
AccountAlreadyDeregister = 15109,
AccountRenameCardCdTimeFailed = 15110,
AccountModifyCountryRegionNumFailed = 15111,
AccountModifyCountryRegionCDFailed = 15112,
AccountVerWhiteListFailed = 15113,
AccountCrossKickInvalidClusterVersion = 15114,
AccountCrossKickNeedReloadProfile = 15115,
AccountCrossKickFailed = 15116,
AccountCallStatesvrFailed = 15117,
AccountBanSetCrossPlatPlay = 15118,
AccountLoadDBPreRegisterFailed = 15119,
AccountAlreadyPreRegister = 15120,
AccountLoginCountryLimit = 15121,
AccountCnSteamForbidOverseaUser = 15122,
AccountDisablePlatRegister = 15123,
AccountRecyleInvalidAvatarId = 15124,
AccountCheckPlatInConsistency = 15125,
AccountRobotIdAreadyUsed = 15126,
AccountLoadDBProfileFailed = 15127,
AccountMsdkCheckDenyLogin = 15150,
SOLOConfigFailed = 16001,
SOLOSCAVRerollNumNotEnough = 16002,
SOLOSCAVMatchNumCoolDown = 16003,
SOLOGetDBFailed = 16004,
SOLOSetDBFailed = 16005,
SOLOGetSeasonInfoFailed = 16006,
SOLOEasterEggCD = 16007,
SOLOBusy = 16008,
PVEChapterInvalid = 17001,
PVELoadConfigFailed = 17002,
PVEGetDBFailed = 17003,
PVESetDBFailed = 17004,
GMTypeInvalid = 18001,
GMAgrInvalid = 18002,
GMServiceClose = 18003,
GMCallServiceFailed = 18004,
GMNotInWhiteList = 18005,
GMGetMatchRecordDBFailed = 18006,
GMSetMatchRecordDBFailed = 18007,
GMGetGameStatDBFailed = 18008,
GMSetGameStatDBFailed = 18009,
GMGetDBFailed = 18010,
GMSetDBFailed = 18011,
GMDelDBFailed = 18012,
GMMarshalFailed = 18013,
AuctionInitDBFailed = 19001,
AuctionPropCanNotSell = 19002,
AuctionGetDBSaleListFailed = 19003,
AuctionInvalidTableIndex = 19004,
AuctionSaleNotFound = 19005,
AuctionSellNotValid = 19008,
AuctionDepositRestoreFailed = 19009,
AuctionDepositCheckFailed = 19010,
AuctionBusy = 19011,
AuctionSaveDBSaleListFailed = 19012,
AuctionPropDescNotFound = 19013,
AuctionInvalidRackDuration = 19014,
AuctionInvalidRackFee = 19015,
AuctionInvalidCurrency = 19016,
AuctionInvalidPrice = 19017,
AuctionInvalidPropGid = 19018,
AuctionInvalidPropNum = 19019,
AuctionDepositExchangeFailed = 19020,
AuctionNotInAutoRackTime = 19021,
AuctionAutoRackDescNotFound = 19022,
AuctionPropNeedInit = 19023,
AuctionGeneratePropGidFailed = 19024,
AuctionUpdateAuctionFailed = 19025,
AuctionSaleListInvalid = 19026,
AuctionGetPlayerAuctionFailed = 19027,
AuctionSavePlayerAuctionFailed = 19028,
AuctionPropNotEnough = 19029,
AuctionCollectionNotFound = 19030,
AuctionUpdatePlayerOrderFailed = 19031,
AuctionPlayerOrderNotFound = 19032,
AuctionOrderCntExceed = 19033,
AuctionPlayerDismatch = 19034,
AuctionInvalidReq = 19035,
AuctionPriceStepNotMatch = 19036,
AuctionNotInAutoBuyTime = 19037,
AuctionAutoBuyDisable = 19038,
AuctionAutoBuyDescNotFound = 19039,
AuctionAutoRackDisable = 19040,
AuctionSystemError = 19041,
AuctionPropDismatch = 19042,
AuctionPriceZero = 19043,
AuctionSendMailFailed = 19044,
AuctionNoMallRecyclePrice = 19045,
AuctionPriceRangeInvalid = 19046,
AuctionPropBinded = 19047,
AuctionGenerateOrderIDFailed = 19048,
AuctionMaxCollectionsExceeded = 19049,
AuctionCallSafehouseFailed = 19050,
AuctionPriceSumIsZero = 19051,
AuctionGetAuctionPropDBFailed = 19052,
AuctionGetSaleListBatchTooMuch = 19053,
AuctionPullOffNumIsNotValid = 19054,
AuctionGameItemNotFound = 19055,
AuctionManualRackIsNil = 19056,
AuctionSellIsLocked = 19057,
AuctionBuyIsLocked = 19058,
AuctionSellTransactionFailed = 19059,
AuctionBuyTransactionFailed = 19060,
AuctionDurationNotFull = 19061,
AuctionDurationLowerThanMinimum = 19062,
AuctionCacheGetTypeListError = 19063,
AuctionPullOffTransactionError = 19064,
AuctionRenewTransactionFailed = 19065,
AuctionRenewNotFound = 19066,
AuctionPlayerBanned = 19067,
AuctionPlayerNotBanned = 19068,
AuctionGMTransactionError = 19069,
AuctionUnknownError = 19070,
AuctionSearchWeaponError = 19071,
AuctionSearchItemError = 19072,
AuctionSearchArmorError = 19073,
AuctionInvalidInsuranceFee = 19074,
AuctionNotFoundDurablityLvl = 19075,
AuctionLoadDBFailed = 19076,
AuctionSaveDBFailed = 19077,
AuctionPropMaxStackNumInvalid = 19078,
AuctionNotFoundMapConfig = 19079,
AuctionSellNonStandardGun = 19080,
AuctionGetSellSlotBonusFailed = 19081,
AuctionMaxRackWeekExceeded = 19082,
AuctionPullOffInCD = 19083,
AuctionServiceDisable = 19084,
AuctionBuyLimit = 19085,
AuctionOptBan = 19086,
AuctionPropMustNotHaveComps = 19087,
AuctionCalcTaxFailed = 19088,
AuctionPartialPurchase = 19089,
AuctionNotEnoughRackTimeAvailable = 19090,
AuctionPropNotInVaildTime = 19091,
AuctionBuyPriceZero = 19092,
AuctionCloseForMatch = 19093,
AuctionGMPriceCalcFailed = 19201,
LotteryPropDescNotFound = 20001,
LotteryDrawFailed = 20002,
LotteryCallDepositFailed = 20003,
LotteryDepositAddFailed = 20004,
LotteryNotBlindBox = 20005,
LotteryBlindBoxDescNotFound = 20006,
LotteryCallSafeHouseFailed = 20007,
LotteryCanNotOpen = 20008,
LotteryDrawDepositFailed = 20009,
LotteryPropNotFound = 20010,
LotteryPropNotEnough = 20011,
LotteryPropExpired = 20012,
LotteryNotFoundBoxConfig = 20013,
LotteryDelBoxFail = 20014,
LotteryCalcFail = 20015,
LotteryLoadDbFail = 20016,
LotterySaveRecordFail = 20017,
LotteryPanic = 20018,
LotteryPrepareFail = 20019,
LotteryPayAndShipFail = 20020,
LotteryClassifyDepositFail = 20021,
LotteryDepositFullToMail = 20022,
MailAttachmentNotExist = 21001,
MailAttachmentReceived = 21002,
MailCallDepositFailed = 21003,
MailEmpty = 21004,
MailLoadConfigFailed = 21005,
MailFull = 21006,
MailAttachmentInvalid = 21007,
MailCallFriendFailed = 21008,
MailGetDBFailed = 21009,
MailSaveDBFailed = 21010,
MailKeyBusy = 21011,
MailGlobalMailOverflow = 21012,
MailCallTransactionFailed = 21013,
MailGenFlowIdFail = 21014,
MailAttachmentIndexInvalid = 21015,
MailCallMpDepositFailed = 21016,
MailCallCollectionFailed = 21017,
MailCallAccountFailed = 21018,
MailCallSeasonFailed = 21019,
MailCallAttributeFailed = 21020,
MailTypeInvalid = 21021,
MailBatchTimeout = 21022,
MailInvalidPlayer = 21023,
MailInvalidUTF8 = 21024,
StateInMatch = 22001,
StateCheckDSRoomIDFailed = 22002,
StateCallServiceFailed = 22003,
StateNotInMatch = 22004,
StateCheckRoomIDFailed = 22005,
StateCheckTeamIDFailed = 22006,
StateInRoom = 22007,
StateInTeam = 22008,
StateInAllocMatch = 22009,
StateBatchLoadDBFailed = 22010,
StateGetDBFailed = 22011,
StateSaveDBFailed = 22012,
StateNotMatching = 22013,
StateLockBusy = 22014,
StateWaitDS = 22015,
StateBatchLoadNumOverflow = 22016,
StateOffline = 22017,
RaidGetConfigFailed = 23001,
RaidGetDBFailed = 23002,
RaidSetDBFailed = 23003,
FriendDBNotExist = 24001,
FriendNumFull = 24002,
FriendCallSvrFailed = 24003,
FriendExist = 24004,
FriendNotExist = 24005,
FriendBusy = 24006,
FriendRepeatedApply = 24007,
FriendDailyGiftNotEnough = 24008,
FriendRepeatedDailyGift = 24009,
FriendInPlayerBlackList = 24010,
FriendGetDBFailed = 24011,
FriendSaveDBFailed = 24012,
FriendRecommendCD = 24013,
FriendGetFavConfigFailed = 24014,
FriendRecvDailyGiftNotEnough = 24015,
FriendPlayerInFriendBlackList = 24020,
FriendPlayerNotExist = 24021,
FriendPlayerNotMatch = 24022,
FriendSocialForbidden = 24023,
FriendCallTssFailed = 24024,
FriendNickDirty = 24025,
FriendInvalidFixType = 24026,
FriendAddResponseInvalid = 24027,
FriendAddFailCreditForbidden = 24028,
FriendBlackListFull = 24029,
FriendAlreadyPraise = 24030,
FriendAccountTypeNotQQ = 24031,
FriendMSDKNotOpen = 24032,
FriendOtherSideFull = 24033,
FriendOtherSideApplyFull = 24034,
FriendOurSideFull = 24035,
FriendRemarksTooLong = 24036,
FriendReqParamsUnreasonable = 24037,
PlayerInfoInvaildParam = 28001,
PlayerInfoGetProfileFailed = 28002,
PlayerInfoPropHasBeenMarked = 28003,
PlayerInfoMarkedPropNotExist = 28004,
PlayerInfoMarkPropsRepeat = 28005,
PlayerInfoUnmarkPropsRepeat = 28006,
PlayerInfoChangeMarkConflict = 28007,
PlayerInfoGetDBFailed = 28008,
PlayerInfoSetDBFailed = 28009,
PlayerInfoMarkOverLimit = 28010,
PlayerInfoServerBusy = 28011,
PlayerInfoGetMsdkPicUrlFailed = 28012,
PlayerInfoCallCollectionFailed = 28013,
PlayerInfoMilitaryTagNotUnlock = 28014,
PlayerInfoMilitaryTagAlreadyEquip = 28015,
PlayerInfoUpdateBasicInfoParamInvalid = 28016,
PlayerInfoTitleNotUnlock = 28017,
PlayerInfoTitleAlreadyEquip = 28018,
PlayerInfoLogUploadTooFreq = 28101,
PlayerInfoCOSFailed = 28102,
PlayerInfoPromptLoadDBFailed = 29000,
PlayerInfoPromptSaveDBFailed = 29001,
PlayerInfoPromptNotFound = 29002,
PlayerInfoPromptNotReleased = 29003,
PlayerInfoPromptDayShowLimit = 29004,
PlayerInfoPromptLifeShowLimit = 29005,
PlayerInfoPromptInvalidReq = 29006,
PlayerInfoAppearanceIllegalMode = 28201,
PlayerInfoQueryMpdepositFailed = 28202,
PlayerInfoQueryHeroFailed = 28203,
PlayerInfoQueryDepositFailed = 28204,
PlayerInfoGarenaGdiRpcFailed = 28205,
PlayerInfoGarenaGdiUnmarshalFailed = 28206,
PlayerInfoGarenaGdiRspFailed = 28207,
PlayerInfoUpdateAccountFailed = 28208,
PlayerInfoSaveDBFailed = 28209,
PlayerInfoPraiseFailed = 28210,
PlayerInfoCallSvrFailed = 28211,
PlayerInfoPraiseOperatingFrequentlyOrCountLimit = 28212,
MPDropTimeLimit = 28301,
MPDropTypeErr = 28302,
MPDropInternalError = 28303,
MPDropSettlementError = 28304,
MPDropDBSaveError = 28305,
PlayerInfoDBCounter = 28306,
PlayerInfoMandelBrickServerLimit = 28307,
PlayerInfoSavePrivacyDBFail = 28507,
PlayerInfoLoadPrivacyDBFail = 28508,
PlayerInfoSetPrivacyDataFail = 28509,
PlayerInfoSaveShareDBFail = 28510,
PlayerInfoLoadShareDBFail = 28511,
PlayerInfoShareRewardConfErr = 28512,
PlayerInfoShareRewardConLimit = 28513,
PlayerInfoShareRewardClose = 28514,
PlayerInfoShareRewardSendFail = 28515,
PlayerInfoUnknown = 28900,
AttrGetSafehouseAttrFail = 109001,
AttrCalAttrFail = 109002,
AttrGetQualityFail = 109003,
AttrChangeQualityFail = 109004,
AttrRequestQualityRepeated = 109005,
AttrQualityIdNotFound = 109006,
AttrQualityChangeOutOfRange = 109007,
AttrQualitySceneNotExist = 109008,
AttrQualityEmptyChangeReq = 109009,
AttrQualityHasBeenUnlocked = 109010,
AttrGetRoleLoadFail = 109011,
AttrGetDBFailed = 109012,
AttrSetDBFailed = 109013,
AttrBusy = 109014,
AttrUnknown = 109900,
WassNodeNotFound = 110003,
WassSlotNotFound = 110004,
WassGetDepositFailed = 110005,
WassSlotNotEmpty = 110006,
WassCantEquip = 110007,
WassTargetNotFound = 110008,
WassRootNotReceiver = 110009,
WassCheckDesignFail = 110010,
WassCallDepositFailed = 110011,
WassDepositResult = 110012,
WassNoChange = 110013,
WassTargetNumNotEnough = 110014,
WassGenerateGidFailed = 110015,
WassIsLock = 110016,
WassNotSameSeries = 110017,
WassGunNotComplete = 110018,
WassStyleHasBeenUnlocked = 110019,
WassReqStyleRepeated = 110020,
WassReqStyleHasEquipped = 110021,
WassReqStyleNotFound = 110022,
WassReqStyleEmpty = 110023,
WassReqCheckSplitGunFailed = 110024,
WassPerkNotEquip = 110025,
WassUnfreezeStyleFailed = 110026,
WassPropWeaponIsNull = 110027,
WassReqStyleSlotRepeat = 110028,
WassReqWrongUnlockCosts = 110029,
WassReqLackPropIdOrGid = 110030,
WassItemDefaultStyleDescNotFound = 110031,
WassDefaultSlotDescNotFound = 110032,
WassGetDBFailed = 110033,
WassSetDBFailed = 110034,
WassCallMPDepositFailed = 110035,
WassCantUpdatePoorWeapon = 110036,
WassWeaponIDEmpty = 110037,
WassWeaponGIDEmpty = 110038,
WassSkinIDEmpty = 110039,
WassCantApplySkin = 110040,
WassCallTssFailed = 110041,
WassNameDirty = 110042,
WassSkinCfgNotFound = 110043,
WassCallCollectionFailed = 110044,
WassFancySkinCfgNotFound = 110045,
WassFancySkinColorCfgNotFound = 110046,
WassFancySkinFuncCfgNotFound = 110047,
WassFancySkinFuncEmpty = 110048,
WassFancyFuncNotUnlocked = 110049,
WassFancySkinNotUnlocked = 110050,
WassFancyFuncCantUnlock = 110051,
WassInvalidParams = 110052,
WassNameIsTooLong = 110053,
WassCallMailFailed = 110054,
WassDesignNotFound = 110055,
WassServerBusy = 110056,
WassGenGidFailed = 110057,
WassSharingCodeIsInValid = 110058,
WassCantChangeDesignName = 110059,
WassWeaponTooFrequently = 110060,
WassWeaponRepeatedRequest = 110061,
WassSysErrorInvalidHandler = 110062,
WassSrvErrorInvalidParams = 110063,
WassGetDesignTooMuch = 110064,
WassDepositNoEnoughSpace = 110100,
WassUnknown = 110900,
HeroGetDepositFailed = 117001,
HeroInvaildParam = 117002,
HeroItemAlreadyHad = 117003,
HeroIllegalItemType = 117004,
HeroPropNotUnlock = 117005,
HeroPropUnknown = 117006,
HeroGidGenFailed = 117007,
HeroNoSuchHero = 117101,
HeroUnknownHeroId = 117102,
HeroRepeatedUnlock = 117103,
HeroNumeralFailed = 117104,
HeroSkinNotExist = 117301,
HeroSkinPositionInvaild = 117302,
HeroSkinPositionEmpty = 117303,
HeroSkinInvaild = 117304,
HeroSkinRepeated = 117305,
HeroAccessoryNotUnlock = 117401,
HeroAccessoryIllegal = 117402,
HeroAccessoryHeroNotMatch = 117403,
HeroAccessorySlotNotEnought = 117404,
HeroAccessoryEquipedRepeated = 117405,
HeroGrowLineConfInvalid = 117501,
HeroGrowLineLevelReachMax = 117502,
HeroGrowLineLevelGoalNotFinish = 117503,
HeroRecruitConfInvalid = 117504,
HeroArmPropFashionStorePosNotUnlocked = 117551,
HeroArmPropFashionStorePosUnlocked = 117552,
HeroArmPropFashionPriceInvalid = 117553,
HeroArmPropFashionLotteryNotExist = 117554,
HeroArmPropFashionStoreDescNotExist = 117555,
HeroArmPropFashionLotteryCostNotEnough = 117556,
HeroArmPropFashionStorePosNotStore = 117557,
HeroArmPropFashionPropProxyFail = 117558,
HeroUnknown = 117900,
HeroLoadDBFailed = 117901,
HeroSaveDBFailed = 117902,
HeroServiceBusy = 117903,
HeroCfgLoadFailed = 117904,
HeroFashionPriorityChangeBan = 117905,
HeroIsUnlocked = 117906,
HeroHasHigherLevelBadge = 117907,
SafehouseLoadDBFail = 118001,
SafehouseSaveDBFail = 118002,
SafehouseDeviceNotFound = 118003,
SafehouseFormulaNotFound = 118004,
SafehouseProduceNotFinish = 118005,
SafehouseResetProduceStateFail = 118006,
SafehouseInvalidFormula = 118008,
SafehouseDelMaterialFail = 118009,
SafehouseFormulaNotInDevice = 118011,
SafehouseInitDBFail = 118012,
SafehouseDeviceInfoNotFound = 118013,
SafehouseDeviceMaxLevel = 118014,
SafehouseDegreeNotEnough = 118015,
SafehouseDeviceConditionIneligible = 118016,
SafehouseSaveDeviceLevelUpFail = 118017,
SafehouseDeviceIsLocked = 118018,
SafehouseDeviceIsUpgrading = 118019,
SafehouseSaveDeviceUpgradingFail = 118020,
SafehouseUnlockSystemFail = 118021,
SafehouseProducingFormula = 118022,
SafehouseGetUnlockInfoFail = 118023,
SafehousePreTaskConditionFail = 118024,
SafehouseNpcFavorConditionFail = 118025,
SafehouseSeasonLevelConditionFail = 118026,
SafehouseManuallyLoopProduce = 118027,
SafehouseNPCWorkInOtherDevice = 118028,
SafehouseModifyLoopProduceFail = 118029,
SafehouseLoopProduceEmpty = 118030,
SafehouseDevicesEmpty = 118031,
SafehouseSCAVDeviceNotFind = 118032,
SafehouseNoVisitor = 118033,
SafehouseInvalidVisitorItem = 118034,
SafehouseVisitorLeave = 118035,
SafehouseNotBuyInfo = 118036,
SafehouseGetInfoItemFail = 118037,
SafehouseGetSellResFail = 118038,
SafehouseGidTypeError = 118039,
SafehouseInfoItemPriceChangeFail = 118040,
SafehouseGenVisitorFail = 118041,
SafehouseSellVisitorFail = 118042,
SafehouseSellItemNumOutOfLimit = 118049,
SafehouseQuerySellItemInfoFail = 118050,
SafehouseReceiveNotifyFail = 118043,
SafehouseSellNoItem = 118044,
SafehouseLeaveFail = 118045,
SafehouseVisitorLock = 118046,
SafehouseIllegalVisitorId = 118047,
SafehouseGetVisitorStatFail = 118048,
SafehouseUnknownWeaponSupplyDeviceID = 118051,
SafehouseUnknownEquipSupplyDeviceID = 118052,
SafehouseGetDailySupplyFail = 118053,
SafehouseSaveSupplyToDepositFail = 118054,
SafehouseSupplyRepeatReceive = 118055,
SafehouseSupplyNotUnlock = 118056,
SafehouseCallDepoisitExchangeFail = 118057,
SafehouseSpeedChangeOnFormulaNotLoop = 118058,
SafehouseIllegalAccelerableItemID = 118059,
SafehouseAccelerableItemOutOfNumLimit = 118060,
SafehouseAcleItemNumNotEnought = 118061,
SafehouseAcceleratedUnlockedFormula = 118062,
SafehouseDeviceHasNoLoopProductionLine = 118065,
SafehouseProductionNotReceivedYet = 118067,
SafehouseMaxProduceQueueLimit = 118069,
SafehousePlayerQualityConditionFail = 118070,
SafehouseVisitorReplaceIllegalFormulaID = 118071,
SafehouseVisitorReplaceOutOfLimit = 118072,
SafehouseServerBusy = 118073,
SafehouseEmptyProduct = 118074,
SafehouseCanNotStopLoopProduce = 118075,
SafehouseDeviceHaveNoProduce = 118076,
SafehouseUpgradeFinished = 118077,
SafehouseProduceFinished = 118078,
SafehouseLoadCfgFailed = 118079,
SafehouseLocked = 118080,
SafehouseQueryBlueprintFailed = 118081,
SafehouseBlueprintLocked = 118082,
SafehouseBlueprintDescNotFound = 118083,
SafehouseBlueprintProduceCD = 118084,
SafehouseProduceLocked = 118085,
SafehouseUpgradeLocked = 118086,
SafehouseGiveSkinFailed = 118087,
SafehouseFormulaNotInTime = 118088,
GeneralSkillGetDBFailed = 121001,
GeneralSkillSaveDBFailed = 121002,
GeneralSkillParamEmpty = 121003,
GeneralSkillSkillIDInvalid = 121004,
GeneralSkillUnlockSkillFailed = 121005,
QuestFetchDBFailed = 122001,
QuestDBDataEmpty = 122002,
QuestNotCompleted = 122003,
QuestNotExistInPlayerDB = 122004,
QuestAlreadyReward = 122005,
QuestRewardPropFailed = 122006,
QuestNotExist = 122007,
QuestAlreadyAccepted = 122008,
QuestSaveDBFailed = 122009,
QuestAlreadyCompleted = 122010,
QuestStateInvalid = 122011,
QuestNotAccepted = 122012,
QuestCheckPropFailed = 122013,
QuestAddExpFailed = 122014,
QuestDelPropFailed = 122015,
QuestSubmitPropIsNotNeeded = 122016,
QuestGetPlayerProfileFailed = 122017,
QuestLevelNotEnough = 122018,
QuestAlreadyPaused = 122019,
QuestPreviousQuestIsNotCompleted = 122020,
QuestObjectiveNotExist = 122021,
QuestObjectiveConfigInvalid = 122022,
QuestObjectiveCheckSafehouseDeviceLevelFailed = 122023,
QuestObjectiveSafehouseDeviceLevelNotSatisfied = 122024,
QuestObjectiveSafehouseFormulaIDNotSatisfied = 122025,
QuestAddCurrencyFailed = 122026,
QuestRewardIDNotExist = 122027,
QuestBusy = 122028,
QuestDepositGetAllFailed = 122029,
QuestDepositPropNumNotEnough = 122030,
QuestCalcSubmitPropFailed = 122031,
QuestConvertMessageTypeFailed = 122032,
QuestCheckFailed = 122033,
QuestVarOutOfRange = 122034,
QuestObjectiveMarkAmountLimit = 122035,
QuestUnknownClass = 122036,
QuestChangeQualityFailed = 122037,
QuestObjectiveHasBeenMarked = 122038,
QuestLineNotOpen = 122039,
QuestDSNtfEmptyData = 122040,
QuestDSNtfRepeatedStateKey = 122041,
QuestShowConditionNotOK = 122042,
QuestRepeatedInReq = 122043,
QuestNotStandAlone = 122044,
QuestEmptyArgList = 122045,
QuestObjValueInvalid = 122046,
QuestObjHasBeenFinished = 122047,
QuestSubmitPropsNotValid = 122048,
QuestCallMailFailed = 122049,
QuestCallDepositFailed = 122050,
QuestContractCannotAccept = 122051,
QuestRewardFailHafCoinFull = 122052,
QuestAcceptFateContractFailedTimeLimit = 122053,
QuestAcceptFateContractFailedSeasonError = 122054,
QuestAcceptFateContractFailedOtherReason = 122055,
QuestAcceptSeasonQuestFailed = 122056,
QuestCollectorRefreshFailCntLimit = 122057,
TssFetchProfileFailed = 123001,
TssCallTssServerFailed = 123002,
TssCallTssServerTimeout = 123003,
TssMsgTooLong = 123004,
TssGetReportCfgFailed = 123005,
TssSendReportReqFailed = 123006,
TssQueryPlayerInfoFailed = 123007,
TssSecurityBackendNotAvailable = 123008,
TssAntiAddUserFailed = 123009,
TssGetFeatureDataFailed = 123010,
SecurityBusy = 123011,
SecurityPicUgcheckIllegal = 123012,
SecurityTimeForbiddenUGC = 123013,
SecurityInvalidSettlementReq = 123014,
SecurityLoadCarryInOutIndexFail = 123015,
SecuritySaveCarryInOutIndexFail = 123016,
SecurityLoadCarryInOutFail = 123017,
SecuritySaveCarryInOutFail = 123018,
SecurityAcquireLockFail = 123019,
SecurityFaceVerifyLoadDBFail = 123020,
TssInvalidReportScence = 123021,
ChatCallTssFailed = 124001,
ChatMsgDirty = 124002,
ChatChannelTimeout = 124003,
ChatCoolDown = 124004,
ChatNotInDSRoom = 124005,
ChatDSRoomNotExist = 124006,
ChatMsgLengthInvalid = 124007,
ChatBusy = 124008,
ChatStangerNumLimit = 124009,
ChatStangerChatCntLimit = 124010,
ChatLoadDBFailed = 124011,
ChatSaveDBFailed = 124012,
ChatPrivateNtfFailed = 124013,
ChatMsgEmpty = 124014,
ChatFillSenderInfoFailed = 124015,
ChatStrangerChatForbidden = 124016,
ChatCheckFriendFailed = 124017,
ChatLoadPrivateSessionFailed = 124018,
ChatNotFriend = 124019,
ChatNoTeam = 124020,
ChatCheckTeamStatusFailed = 124021,
ChatCheckPlayerStatusFailed = 124022,
ChatWorldChatOverFrequency = 124023,
ChatWorldChatRoomNotExist = 124024,
ChatWorldRoomToCreateExistYet = 124025,
ChatWorldInvaildRoomID = 124026,
ChatWorldMemberNumOverLimit = 124027,
ChatFriendBlackList = 124028,
ChatWorldRoomNotNeedTransfer = 124029,
ChatWorldRoomTransferFailed = 124030,
ChatInvaildReadMsgIndex = 124031,
ChatWorldRoomTooMany = 124032,
ChatStrangerChatBan = 124033,
ChatForbidden = 124034,
ChatRemoveSpeechFailed = 124035,
ChatForbiddenParmWrong = 124036,
ChatAppointmentNotFound = 124037,
ChatPlayerNotLogin = 124038,
ChatIlleaglAppointmentText = 124039,
ChatWithSelf = 124040,
ChatPrivateSessionTypeWrong = 124041,
ChatForbiddenByCreditCheck = 124042,
ChatIllegalGunShareCode = 124043,
WorldChatMsgInvalid = 125001,
WorldChatRoomAllocFailed = 125002,
WorldChatCallChatSvrFailed = 125003,
WorldChatBusy = 125004,
WorldChatOverFrequency = 125005,
WorldChatSendLock = 125006,
MallNoSuchGood = 126001,
MallMerchantErr = 126002,
MallInvaildParam = 126003,
MallPriceNotOk = 126004,
MallNotSameSeries = 126005,
MallCallDeposit = 126006,
MallInvaildGun = 126007,
MallGetServerDBFailed = 126008,
MallSetServerDBFailed = 126009,
MallNoEnoughGood = 126010,
MallUpdatePipeTableFailed = 126011,
MallPushPanicBuyFailed = 126012,
MallNotInBuyingTime = 126013,
MallNoSuchMerchant = 126014,
MallGetMerchantRelationFailed = 126015,
MallPanicBuyNoLucky = 126016,
MallInvaildGood = 126017,
MallTraderCfgErr = 126018,
MallNoSuchPanicBuyPipe = 126019,
MallPipeMgrNotInit = 126020,
MallRepeatedBuy = 126021,
MallInvaildGift = 126022,
MallVipLvlNotEnough = 126023,
MallCantBatchBuy = 126024,
MallRushBuySettlementing = 126025,
MallIntimacyMax = 126026,
MallIntimacyDayMax = 126027,
MallCalcMerchantGiftFaield = 126028,
MallCantFindGift = 126029,
MallGetSafeHouseFailed = 126030,
MallBuyNumExceedDailyLimit = 126031,
MallSellNumExceedDailyLimit = 126032,
MallBuyDesignGunNoPosID = 126033,
MallGetPlayerDBFailed = 126034,
MallSetPlayerDBFailed = 126035,
MallNoFoundExchangeDesc = 126036,
MallGetRepLevelFailed = 126037,
MallUnlockSpecifiedTraderFailed = 126038,
MallServerBusy = 126039,
MallCallAttributeServerFailed = 126040,
MallGetSeasonLevelFailed = 126041,
MallSeasonLevelNotEnough = 126042,
MallCannotRecycleFailed = 126043,
MallGetBuyDeadilneTimeFailed = 126044,
MallUnlockExchangeIdInvalid = 126045,
MallGetDBFailed = 126046,
MallSetDBFailed = 126047,
MallHandleGreatEarnPoolFailed = 126048,
MallMysteryShopHasBeenRaffled = 126049,
MallMysteryShopCantGetGlobalData = 126050,
MallMysteryShopHasNoProps = 126051,
MallMysteryShopIsClosed = 126052,
MallStoreIsLocked = 126053,
MallCallRemoteFailed = 126054,
MallItemNotUnlock = 126055,
MallItemHasBeenOffShelves = 126056,
MallItemIsForbiddenRecycled = 126057,
MallBuyOverDueFailed = 126058,
MallGetGoodsExceedLimit = 126059,
MallGetExternalCalcDataFailed = 126060,
MallPropNumExceedUpLimit = 126061,
MallDepositNoEnoughSpace = 126100,
MallDepositNoEnoughMoney = 126101,
MallDepositNoEnoughProp = 126102,
MallDepositNoPropDesc = 126103,
MallFuncJudgeFailed = 126800,
MallUnknown = 126900,
NumeralGetEquipedPropFailed = 127001,
NumeralGetAttrFailed = 127002,
NumeralGetSettlementFailed = 127003,
NumeralGetHeroFailed = 127004,
NumeralGetGeneralSkillFailed = 127005,
NumeralGetSafehouseFailed = 127006,
NumeralGetQuestFailed = 127007,
NumeralGetMarkPropsFailed = 127008,
NumeralGetMPDepositFailed = 127009,
NumeralTDMPanic = 127010,
NumeralGetArmedforceFailed = 127011,
NumeralGetWeaponFailed = 127012,
NumeralGetActivityFailed = 127013,
NumeralSyncToDsaFailed = 127100,
NumeralInvalidReq = 127101,
NumeralGetScavFailed = 127102,
NumeralGetGuideFailed = 127103,
NumeralLoadProfileFailed = 127104,
NumeralLoadDBMiscDataFailed = 127105,
NumeralDepositCheckEquipPriceFailed = 127106,
NumeralGetCollectFailed = 127107,
NumeralSettlementFailed = 127108,
NumeralGetRoomsvrFailed = 127109,
NumeralDsFailedStart = 127200,
NumeralDsFailedHalfJoin = 127201,
NumeralDsFailedEnd = 127250,
NumeralUnknown = 127900,
DsagentAllocFailed = 128001,
DsagentInternalError = 128002,
DsagentNetworkError = 128003,
DsagentVersionUnexpected = 128004,
DsagentRoomNotExist = 128005,
DsagentVersionNotExist = 128006,
DsagentRoomAlreadyExist = 128007,
DsagentSendIdcagentFailed = 128009,
DsagentOverload = 128010,
DsagentDsNotReady = 128011,
DsagentCallDsTimedOut = 128012,
DsagentUnmarshalError = 128013,
DsagentMarshalError = 128014,
DsagentLoadDBError = 128015,
DsagentSaveDBError = 128016,
DsagentDelDBError = 128017,
DsagentMatchHasEnded = 128018,
DsagentMarshalFailed = 128019,
DsagentUnMarshalFailed = 128020,
DsagentServerDisabled = 128021,
DsagentAllocInvalidReq = 128022,
DsagentAllocPortFailed = 128023,
DsagentFatherNotExist = 128024,
DsagentInvalidClusterVersion = 128025,
SettlementInternalError = 129001,
SettlementDBError = 129002,
SettlementBadArguments = 129003,
SettlementMatchNotFound = 129004,
SettlementUnknownMatchMode = 129005,
SettlementBadCalculator = 129006,
SettlementInitCalculatorError = 129007,
SettlementLoadError = 129008,
SettlementInvalidResult = 129009,
SettlementMatchAlreadyDone = 129010,
SettlementInconsistentRoom = 129011,
PresetPresetNotExist = 130002,
PresetPresetIsLocked = 130003,
PresetPresetInvalid = 130004,
PresetBadArguments = 130006,
PresetKeyNotExist = 130007,
PresetShareCodeNotExist = 130008,
PresetIllegalArmedForceID = 130009,
PresetShareGenFailed = 130010,
PresetLoadDBFailed = 130011,
PresetSaveDBFailed = 130012,
PresetServerBusy = 130013,
SupplyDBError = 130809,
SupplyInvalidOperation = 130810,
SupplyConfigNameInvalid = 130811,
SupplyConfigTypeInvalid = 130812,
SupplyPropIDInvalid = 130813,
SupplyConfigInfoEmpty = 130814,
SupplyDelConfigFailed = 130815,
SupplyModifyConfigFailed = 130816,
SupplyRemoveOldestFailed = 130817,
SupplyDBConfigInvalid = 130818,
InsuranceItemNotFound = 131001,
InsuranceInsufficientMoney = 131003,
InsuranceInternalError = 131004,
InsuranceTimedOut = 131005,
InsuranceAddPropFailed = 131006,
InsuranceBadArguments = 131007,
InsuranceRedeemGoodsFailed = 131008,
InsuranceLoadDBFailed = 131009,
InsuranceSaveDBFailed = 131010,
InsuranceBusy = 131011,
WorldGetPlayerStateFailed = 132000,
WorldPlayerStateInvalid = 132001,
WorldFetchWorldDBFailed = 132002,
WorldCreateRoomIDFailed = 132003,
WorldMapNotFound = 132004,
WorldSavePlayerWorldFailed = 132005,
WorldInvalidReq = 132006,
WorldBusy = 132007,
WorldStrongholdNotFound = 132008,
WorldStateIdle = 132009,
WorldStateInMatch = 132010,
WorldStateAllocMatch = 132011,
WorldStateInRoom = 132012,
WorldStateInTeam = 132013,
WorldActorNotFound = 132014,
ProfGetConfigFailed = 134000,
IdcagentDsaAllocFailed = 135001,
IdcagentSendToDsgateFailed = 135002,
IdcagentSendToDSAgentFailed = 135003,
IdcagentSendToRoundtripFailed = 135004,
IdcagentInvalidZoneID = 135005,
IdcagentGetIDCNameFailed = 135006,
IdcagentDSAgentStopNtfIDEmpty = 135007,
IdcagentDSGateStopNtfIDEmpty = 135008,
IdcagentSendSSRoundtripHeartbeatFailed = 135009,
IdcagentSendSSRoundtripStopFailed = 135010,
IdcagentAllocateDSGateFailed = 135011,
IdcagentDSGateForwardClientFailed = 135012,
IdcagentInvalidSettlementNtf = 135013,
IdcagentNtfMarshalFailed = 135014,
IdcagentRetryCacheMsgFailed = 135015,
IdcagentSendToSettlementFailed = 135016,
IdcagentSendDs2AnyMatchInfoNtfFailed = 135017,
IdcagentSendDsagent2AnyBeginMatchResFailed = 135018,
IdcagentSendDsagent2AnyMatchEndNtfFailed = 135019,
IdcagentSendDsagent2AnyPlayerLogoutDsNtfFailed = 135020,
IdcagentSendDsagent2AnyRoomInfoNtfFailed = 135021,
IdcagentSendDsagentQueryReconnectFailed = 135022,
IdcagentSendAny2DsPlayerAbandonMatchFailed = 135023,
IdcagentSendAny2DsPlayerLeaveSafeHouseFailed = 135024,
IdcagentSendAny2DsSyncSafeHousePlayerInfoFailed = 135025,
IdcagentSendDs2AnySyncSafeHousePlayerInfoFailed = 135026,
IdcagentSendAny2DsSyncPlayerInfoFailed = 135027,
IdcagentSendDs2AnySyncPlayerInfoFailed = 135028,
IdcagentSendDs2RawLogTglogFailed = 135029,
IdcagentRetryCacheAddFailed = 135030,
IdcagentExceedMsgPoolSize = 135031,
IdcagentSendToServiceFailed = 135032,
IdcagentBatchGetMsgFailed = 135033,
IdcagentSendDs2AnyDeathAndHurtDetailInfoNtf = 135034,
IdcagentSendDs2AnyRoomStatisticsNtf = 135035,
IdcagentSendDs2RoomRecommendFriendNtf = 135036,
IdcagentSendDsSeasonMatchSettlementAINtf = 135037,
IdcagentSendDSMPLabAILogNtf = 135038,
DsgateMeshSendServiceFailed = 136001,
DsgateAllocIdcagentFailed = 136002,
DsgateGetProfileFailed = 136003,
DsgateSendToIDCAgentFailed = 136004,
DsGateQueryBestIDCFailed = 136005,
DsGateGetIDCFromIDCLBSvrFailed = 136006,
DsGateEmptyIDCAgentID = 136007,
DsGateCallStateSvrFailed = 136008,
DsGateBroadcastHeartbeatToRoundtripSvrFailed = 136009,
DsGateBroadcastStopNtfToRoundtripSvrFailed = 136010,
DsGateSetRoundtripConfigFailed = 136011,
DsGateRetryMsgAckFailed = 136012,
DsGateForwardClientFailed = 136013,
DsGateSendToSettlementFailed = 136014,
DsGateSendToMatchRoomSvrFailed = 136015,
DsGateSendToServiceFailed = 136016,
DsGateDs2RawLogTglogSendFailed = 136017,
DsGateSendSSDs2AnySyncPlayerInfoFailed = 136018,
DsGateSendHandleDsagent2AnyRoomInfoNtfFailed = 136019,
DsGateSendDsagent2AnyPlayerLogoutDsNtfFailed = 136020,
DsGateSendDsagent2AnyMatchEndNtfFailed = 136021,
DsGateSendDsagent2AnyBeginMatchResFailed = 136022,
DsGateSendDs2AnyMatchInfoNtfFailed = 136023,
DsGateSendDs2AnyDeathAndHurtDetailInfoNtfFailed = 136024,
DsGateUnknownFailed = 136999,
SeasonInvalidSerial = 137001,
SeasonInitDBFailed = 137002,
SeasonGetDBFailed = 137003,
SeasonSaveDBFailed = 137004,
SeasonSettleInvalidReq = 137005,
SeasonSettleAddMatchRecFail = 137006,
SeasonSettleAddSeasonRecFail = 137007,
SeasonSetCourseDlockFail = 137008,
SeasonGetRecordsInvalidReq = 137010,
SeasonAddExpFail = 137012,
SeasonAddExpLevelUpFail = 137013,
SeasonLevelUpRewardFailed = 137015,
SeasonLevelCantDegrade = 137016,
SeasonAddExpReqInvalid = 137017,
SeasonSettleAddExpFail = 137018,
SeasonGetRankListInvalidReq = 137019,
SeasonRankRecvAwardInvalidReq = 137020,
SeasonRankRecvAwardNotAttended = 137021,
SeasonRankRecvAwardNotFound = 137022,
SeasonRankRecvAwardConstructProp = 137023,
SeasonNoCourseCfg = 137024,
SeasonNoCourseSelected = 137025,
SeasonGetDepositFailed = 137026,
SeasonGetDBMatchInfoFailed = 137027,
SeasonTargetSeasonNotFound = 137030,
SeasonTargetSeasonDescNotFound = 137031,
SeasonGetInfoInvalidParam = 137032,
SeasonMallGetPropsInvalidReq = 137033,
SeasonMallGetPropsLoadDBFail = 137034,
SeasonMallGetPropsSaveDBFail = 137035,
SeasonMallBuyPropsInvalidReq = 137036,
SeasonMallBuyPropsNotFound = 137037,
SeasonMallBuyPropsSeasonMismatch = 137038,
SeasonMallBuyPropsRankLevelNotMeet = 137039,
SeasonMallBuyPropsExceedLimit = 137040,
SeasonMallBuyPropsNotEnoughMoney = 137041,
SeasonMallNotEnabled = 137042,
SeasonRejectReadHurtDataOfOthers = 137043,
SeasonRejectReadKillDataOfOthers = 137044,
SeasonRejectReadGainDataOfOthers = 137045,
SeasonRejectReadScoreDataOfOthers = 137046,
SeasonThumbDBInvalid = 137047,
SeasonBusyEnqueueFail = 137049,
SeasonLoadDBProfileFail = 137050,
SecurityCompensateInvalidReq = 137400,
SecurityCompensateAcquireDlockFail = 137401,
SecurityCompensateLoadDBFlowsFail = 137402,
SecurityCompensateSaveDBFlowsFail = 137403,
SecurityCompensateSaveDBFlowFail = 137404,
SecurityCompensateDuplicateReq = 137405,
SecurityCompensateNotEnable = 137406,
SecurityCompensateInnerError = 137407,
SecurityCompensateTimeout = 137408,
SecurityCompensateLoadStatesError = 137409,
SecurityCompensateLoadCarryInOutDataError = 137410,
SecurityCompensateLoadTeammatesCarryInOutDataError = 137411,
SecurityCompensatePolicyNotFound = 137412,
SecurityCompensatePolicyNotSampled = 137413,
SecurityCompensateLoadStatError = 137414,
SecurityCompensateDBCarryInOutDataNotFound = 137415,
SecurityCompensateDBSaveFail = 137416,
SecurityCompensateSendMailFail = 137417,
SecurityCompensateExceedCDLimit = 137418,
SecurityCompensateSomeVictimsFail = 137419,
GatewayNeedLoginFirst = 138000,
GatewayNotInWhiteList = 138001,
GatewayReqOverfrequent = 138002,
GatewayDenyClientType = 138003,
GatewayLoginServiceClosed = 138004,
GatewayMetaNotFound = 138005,
GatewayInvalidOpenid = 138006,
GatewayPostConfirmSession = 138007,
MatchGateCallMatchsvrFailed = 139000,
MatchGateLoadDBProfileFailed = 139001,
MatchGateCalcSkillLevelFailed = 139002,
MatchGateConvertClientVersionToDSVersionFailed = 139003,
MatchGateMapLevelUnlock = 139004,
MatchGateServiceInMaintenance = 139005,
MatchGateGetSeasonInfoFailed = 139006,
MatchGateCheckEquipPriceFailed = 139007,
MatchGatePunishFailed = 139008,
MatchGateCallSvrFailed = 139009,
MatchGateBusy = 139010,
MatchGateMapNotOpen = 139011,
MatchGateTeamPlayerVersionIsNotEqual = 139012,
MatchGateRankedMatchNotUnlock = 139013,
MatchGateAddMemberNotMeetCondition = 139014,
MatchGateEquip15CollectionUpLimit = 139015,
MatchGatePackQuestMiss = 139016,
MatchGateCheckPlayerAreaNotEqual = 139017,
MatchGateCallSeasonsvrFailed = 139018,
MatchGateCallDepositsvrFailed = 139019,
MatchGateCallStatesvrFailed = 139020,
MatchGateRankedMatchStopped = 139021,
MatchGateBodyInContainerCapitalUpLimit = 139022,
MatchGateCrossPlatPlayConflict = 139023,
MatchGateForbidRankByReputation = 139024,
MatchGateCheckTickPriceFailed = 139025,
MatchGateCallMatchDatasvrFailed = 139026,
MatchGateArenaConfigNotFound = 139027,
MatchGateArenaClosed = 139028,
MatchGateForbidTeamByReputation = 139029,
MatchGateTdmRankScoreTooLow = 139030,
MatchGateBanTooManyMaps = 139031,
MatchGateInvalidPlatType = 139032,
MatchGateFaceVerifyRequired = 139033,
MatchGateCallDepositDeductFeeFailed = 139034,
MatchGateFaceVerifyTeamRequired = 139035,
MatchGateForbidCommanderByReputation = 139036,
MatchGateForbidCommanderBySeasonAndExpScore = 139037,
RobotRandomProfileFailed = 140000,
RobotRandomWeaponFailed = 140001,
RobotRandomInvalidNum = 140002,
RobotRandomRobotFailed = 140003,
RobotMatchPlayerInvalidNum = 140004,
RobotSvrBusy = 140005,
RobotLoadDBRobotPreRegisterFailed = 140006,
RobotSaveDBRobotPreRegisterFailed = 140007,
RobotAlreadyPreRegister = 140008,
Face2FaceRoomExist = 141000,
Face2FaceRoomMemberFull = 141001,
Face2FaceNotTeamLeader = 141002,
Face2FaceCallTeamsvrFailed = 141003,
Face2FaceInvalidMatchMode = 141004,
Face2FaceTeamFull = 141005,
Face2FaceCallStateFailed = 141006,
Face2FaceCheckTeamIdFailed = 141007,
Face2FaceGetSimpleInfoFailed = 141008,
Face2FaceRoomNotExist = 141009,
RecruitmentInvalidModeInfo = 143000,
RecruitmentLoadConfigFailed = 143001,
RecruitmentCallTeamsvrFailed = 143002,
RecruitmentGetDBFailed = 143003,
RecruitmentGetLobbyFailed = 143004,
RecruitmentCannotFoundTeam = 143005,
RecruitmentCallRecruitmentsvrFailed = 143006,
RecruitmentWorldchatRoomIdNotExist = 143007,
RecruitmentCallSeansonsvrFailed = 143008,
RecruitmentInvalidRequest = 143009,
RecruitmentPunished = 143010,
MatchRoomGenUIDFailed = 144000,
MatchRoomGetDSRoomDBFailed = 144001,
MatchRoomSetDSRoomDBFailed = 144002,
MatchRoomGetProfileFailed = 144003,
MatchRoomReqMatchModeNone = 144004,
MatchRoomInvalidMapID = 144005,
MatchRoomCallDsaFailed = 144006,
MatchRoomBusy = 144007,
MatchRoomCallStateLoadDSRoomIDFailed = 144008,
MatchRoomPlayerNotInRoom = 144009,
MatchRoomPlayerMatchEnd = 144010,
MatchRoomQueryReconnectFailed = 144011,
MatchRoomTdmStageEnd = 144012,
MatchRoomNotFound = 144013,
MatchRoomFull = 144014,
MatchRoomInvalidPara = 144015,
MatchRoomForbidHalfJoin = 144016,
MatchRoomGenDsroomFail = 144017,
MatchRoomGetRobotInfoFail = 144018,
MatchRoomInvalidMatchMode = 144019,
MatchRoomRepeatedRevenge = 144020,
MatchRoomCallClientFailed = 144021,
MatchRoomReconnectCheckPlatGroup = 144022,
MatchRoomGuideMatchConfigFailed = 144023,
MatchRoomSyncPlayerData = 144024,
MatchRoomCallHeroFailed = 144025,
MatchRoomPickHeroTimeout = 144026,
MatchRoomCheckPickHeroStageEnd = 144027,
MatchRoomHalfJoinFail = 144028,
MatchRoomGetPickHeroStageEndTimeFail = 144029,
MatchRoomCallNumeralFail = 144030,
MatchRoomPlayerReconnectVersionError = 144031,
MatchRoomPlayerDisReconnectFail = 144032,
MatchRoomBotHalfJoinFail = 144033,
MatchRoomPlayerReconnectClientTypeError = 144034,
MatchRoomDSRoomIDInvalid = 144035,
MatchRoomCallShopFailed = 144036,
MatchRoomNotFoundRandEventConf = 144037,
MatchRoomLoadMandelFailed = 144038,
MatchRoomSaveMandelFailed = 144039,
MatchRoomMandelInsufficient = 144040,
MPDepositLoadDBFailed = 145000,
MPDepositSaveDBFailed = 145001,
MPDepositArmedForceIDWrong = 145002,
MPDepositBusy = 145003,
MPDepositBagNotFound = 145004,
MPDepositIllegalGid = 145005,
MPDepositPropPosNotMatch = 145006,
MPDepositPosNotFound = 145007,
MPDepositGetPlayerPresetBatchFailed = 145008,
MPDepositGetPlayerBagBatchFailed = 145009,
MPDepositIllegalTalentID = 145010,
MPDepositInvalidReq = 145011,
MPDepositIllegalWeapon = 145012,
MPDepositAddWeaponExpFailed = 145013,
MPDepositComponentNotFound = 145014,
MPDepositWeaponExpIsFull = 145015,
MPDepositUnlockWeaponFailed = 145016,
MPDepositBagNameLenOverLimit = 145017,
MPDepositBagNameIlegal = 145018,
MPDepositPropRepeated = 145019,
MPDepositUnlockCompRepeated = 145020,
MPDepositPropDescNotFound = 145021,
MPDepositCallHeroFailed = 145022,
MPDepositCallHeroLogicFailed = 145023,
MPDepositIlleaglHeroID = 145024,
MPDepositRepeatedUnlockBags = 145025,
MPDepositGetDefPresetFailed = 145026,
MPDepositCallCollectionSvrFailed = 145027,
MPDepositMeeleNotFound = 145028,
MPDepositPropNotFound = 145029,
MPDepositUnlockComponentFailed = 145030,
MPDepositVehicleSlotNotFound = 145031,
MPDepositVehicleUnlocked = 145032,
MPDepositVehiclePartUnlocked = 145033,
MPDepositVehicleInconsitPartOnSlot = 145034,
MPDepositVehicleInvalidProp = 145035,
MPDepositCallCollectionFailed = 145036,
MPDepositVehicleSkinNotFound = 145037,
MPDepositWeaponLvlDescNotFound = 145038,
MPDepositPickUpPartsFailed = 145039,
MPDepositCallWeaponAssemblyFailed = 145040,
MPDepositArmedPresetNotFound = 145041,
MPDepositUnlockPropFailed = 145042,
MPDepositSkinDescNotFound = 145043,
MPDepositSkinBlueprintIDInvalid = 145044,
MPDepositCheckComponentFailed = 145045,
MPDepositExpertNoArmedForceID = 145046,
MPDepositUnapplyMeleeSkinFailed = 145047,
CurrencyLoadDBFailed = 146000,
CurrencySaveDBFailed = 146001,
CurrencyInvalidExchange = 146002,
CurrencyNotEnough = 146003,
CurrencyOverLimit = 146004,
CurrencyLoadMidasFailed = 146005,
CurrencyMidasPresentFailed = 146006,
CurrencyGenFlowIdFailed = 146007,
CurrencyNoFoundExchangeDesc = 146008,
CurrencyDepositChangeFailed = 146009,
CurrencyNotFoundGameItemDesc = 146010,
CurrencyMidasPayFailed = 146011,
CurrencySvrIsBusy = 146012,
PatchVersionCheckHasUpdate = 147000,
ChosenSvrNodeNotFound = 148000,
ChosenSvrDBLoadFailed = 148001,
ChosenSvrDBSaveFailed = 148002,
ChosenSvrBusy = 148003,
ChosenSvrNewSelector = 148004,
ChosenSvrReqInvalid = 148005,
ChosenSvrDBDelFailed = 148006,
ChosenSvrRetryDir = 148007,
ChosenSvrSelectClosed = 148008,
ChosenSvrReportInvalidData = 148009,
ChosenSvrRecommendIdcNotFound = 1480010,
RoundtripSvrHandlerNotFound = 149000,
RoundtripSvrInvalidMsg = 149001,
RoundtripSvrRateLimit = 149002,
RoundtripEncodeFailed = 149003,
ArmedforceLoadDBFailed = 150000,
ArmedforceSaveDBFailed = 150001,
ArmedforceBusy = 150002,
ArmedforceGetTalentFailed = 150003,
ArmedforceIllegalTalentID = 150004,
ArmedforceGetArmedPropFailed = 150005,
ArmedforceIllegalArmedPropID = 150006,
ArmedforceGetHeroIDFailed = 150007,
ArmedforceIllegalHeroID = 150008,
ArmedforceEmptyParam = 150009,
ArmedforceIllegalArmedforceID = 150010,
ArmedforceIllegalStyleID = 150011,
ArmedforceCallWassSvrFailed = 150012,
ArmedforceCallDepostSvrFailed = 150013,
ArmedforceGenPropFailed = 150014,
ArmedforceIllegalParams = 150045,
ArmedforceNameLenOverLimit = 150046,
ArmedforceCallTssFailed = 150047,
ArmedforceNameDirty = 150048,
ArmedforceIllegalOutfitIndex = 150049,
EquipmentRentalCurrencyNotEnough = 150050,
EquipmentRentalNotEnough = 150051,
EquipmentRentalNotFoundPlanData = 150052,
EquipmentRentalNotInTargetState = 150053,
EquipmentRentalInTargetState = 150054,
EquipmentRentalEquipFailed = 150055,
EquipmentRentalUnEquipFailed = 150056,
EquipmentRentalCancelFailed = 150057,
EquipmentRentalModeNotMatch = 150058,
ArmedforceCallCollectionSvrFailed = 150159,
SwitchLoadDBFailed = 151000,
SwitchSaveDBFailed = 151001,
SwitchBusy = 151002,
SwitchIllegalModuleID = 151003,
SwitchIllegalConditionID = 151004,
SwitchConditionNotMet = 151005,
SettingLoadDBFailed = 152000,
SettingSaveDBFailed = 152001,
SettingBusy = 152002,
SettingEmptyKey = 152003,
SettingEmptyType = 152004,
SettingEmptyPutReq = 152005,
SettingEmptyRecord = 152006,
SettingShareCodeGenFailed = 153007,
SettingShareCodeSaveFailed = 153008,
SettingInvaildShareCode = 153009,
SettingTitleLenOverLimit = 153010,
SettingEmptyTitle = 153011,
SettingTitleDirty = 153012,
HopeLoadDBFailed = 153000,
HopeSaveDBFailed = 153001,
HopeBusy = 153002,
HopeGetWrongPlayerInfo = 153003,
HopeCallZKFailed = 153004,
HopeCallMSDKFailed = 153005,
HopeGetOpenIDFailed = 153006,
HopeCallIntlFailed = 1530013,
PayLoadDBFailed = 154000,
PaySaveDBFailed = 154001,
PayCreateGuidFailed = 154003,
PayCallMidasFailed = 154004,
PayParseFailed = 154005,
PayParaInvalid = 154006,
PayIsNotOpen = 154007,
PayServerBusy = 154008,
PayParamIsInvalid = 154009,
PayRiskCtlCheckFailed = 154010,
PayRiskCtlCheckAgain = 154011,
PayTokenIsInvalid = 154012,
PayMiscDataIsInvalid = 154013,
PayParamOverLimit = 154014,
PayGooglePcTokenIsInvalid = 154015,
PayGooglePcInvalidReq = 154016,
PayMidasInRiskControl = 154017,
GuideLoadDBFailed = 155000,
GuideSaveDBFailed = 155001,
GuideBusy = 155002,
GuideNotInStage = 155003,
GuideCannotFindConfig = 155004,
GuideCallDepositsvrFailed = 155005,
GuideLoadProfileFailed = 155006,
GuidePassAll = 155007,
GuideCallMatchRoomFailed = 155008,
GuideGenDSRoomIDFail = 155009,
GuideFuncUnlockFail = 155010,
GuideFuncAlreadyUnlock = 155011,
GuideFuncUnlockSuc = 155012,
GuideDSRoomNotMatch = 155013,
GuideMatchPlayerStateErr = 155014,
ShopLoadDBFailed = 156000,
ShopSaveDBFailed = 156001,
ShopNotFoundConfig = 156002,
ShopInvalidPayMent = 156003,
ShopPayFail = 156004,
ShopChangeSolInventoryFail = 156005,
ShopChangeMpInventoryFail = 156006,
ShopChangeCollectInventoryFail = 156007,
ShopGoodsNotActive = 156008,
ShopGenFlowIdFail = 156009,
ShopClientPriceInvalid = 156010,
ShopLotteryFail = 156011,
ShopBuyListEmpty = 156012,
ShopNotFoundRecord = 156013,
ShopSaveRecordFail = 156014,
ShopBuyLimit = 156015,
ShopCapacityNotEnough = 156016,
ShopDepositRestoreFail = 156017,
ShopBuyListTooMuch = 156018,
ShopCurrencyNotEnough = 156019,
ShopPanic = 156020,
ShopBuyUnknow = 156021,
ShopMeshCallFail = 156022,
ShopServerBusy = 156023,
ShopItemNotOnShelves = 156024,
ShopIsLocked = 156025,
ShopMandelIsNotEnough = 156026,
ShopGenPropFailed = 156027,
ShopPresentPropLimited = 156028,
ShopPresentInvalidParam = 156029,
ShopPresentForbidden = 156030,
ShopPresentDifferentCountryForbidden = 156031,
ShopServerInvalidHandler = 156032,
ShopMidasPayProvideRefuseRefund = 156033,
ShopInnerRpcCallFailed = 156034,
ShopNormalInvalidParam = 156035,
ShopSpecialBackHasBeenEnd = 156036,
ShopSpecialBackItemExists = 156037,
ShopPresentSteamAccountForbidden = 156038,
ShopPresentGiverLevelUnreached = 156039,
ShopPresentReceiverLevelUnreached = 156040,
ShopPresentGiveTimesLimited = 156041,
ShopPresentReceiveTimesLimited = 156042,
ShopPresentFriendLastingTimeUnreached = 156043,
ShopLotteryBuyLotteryItemRoundInvalid = 156044,
ShopNotInCurSpecialBackRecord = 156045,
ShopPresentDifferentAccountPlatForbidden = 156046,
ShopRaffleLuckyNestFailed = 156047,
ShopConstructPropFailed = 156048,
ShopPropNotFoundInLuckyNest = 156049,
ShopClientLimitBindTicketPriceWrong = 156050,
ShopClientParamWrong = 156051,
ShopCantSetUpOwnedProp = 156052,
ShopCallMailMeshFailed = 156053,
CollectionLoadDBFailed = 157000,
CollectionSaveDBFailed = 157001,
CollectionBusy = 157002,
CollectionPropNotFound = 157003,
CollectionPropNotEnough = 157004,
CollectionPropIllegalEffect = 157005,
CollectionGuidFailed = 157006,
CollectionUsePropFailed = 157007,
CollectionCantBeUsed = 157008,
CollectionUsePropCmdIllegal = 157009,
CollectionUsePropDefferentType = 157010,
CollectionIllegalPropType = 157011,
CollectionPropDescNotFound = 157012,
CollectionPropOverLimit = 157013,
CollectionBlueprintNotEnough = 157014,
CollectionCallSvrFailed = 157015,
CollectionParamWorng = 157016,
CollectionPropUseCD = 157017,
CollectionSystemError = 157018,
CollectionMysticalSkinOverLimit = 157019,
CollectionCreateUIDFailed = 157020,
CollectionCombineParaErrNotMystical = 157021,
CollectionCombineParaErrNotFound = 157022,
CollectionCombineParaErrIDWrong = 157023,
CollectionCombineParaErrNotEnough = 157024,
CollectionCombineParaErrClassWrong = 157025,
CollectionCombineNewPropFailed = 157026,
CollectionCombineAddPropFailed = 157027,
CollectionCombineDelPropFailed = 157028,
CollectionMysticalSkinNameInvalid = 157029,
CollectionMysticalSkinNameExists = 157030,
CollectionMysticalSkinNameFailed = 157031,
CollectionPayFailed = 157032,
CollectionSettlememtBluePrintIDInvalid = 157033,
CollectionGunSkinTaskNotFound = 157034,
CollectionGunSkinTaskNotFinished = 157035,
CollectionMysticalSkinPageInvalid = 157036,
CollectionPropUseNoInThisSeason = 157037,
CollectionRightsTimeReachLimit = 157038,
CollectionBattleConsumeUseOverLimit = 157039,
CollectionBattleConsumeReachLimit = 157040,
CollectionNotCurrSeason = 157041,
CollectionMysticalSkinRenameForbid = 157042,
CollectionCombinePendantParaErrNotMystical = 157043,
CollectionCombinePendantParaErrNotFound = 157044,
CollectionCombinePendantParaErrIDWrong = 157045,
CollectionCombinePendantParaErrNotEnough = 157046,
CollectionCombinePendantParaErrClassWrong = 157047,
CollectionCombinePendantNewPropFailed = 157048,
CollectionCombinePendantAddPropFailed = 157049,
CollectionCombinePendantDelPropFailed = 157050,
CollectionMysticalPendantNameInvalid = 157051,
CollectionMysticalPendantNameExists = 157052,
CollectionMysticalPendantNameFailed = 157053,
CollectionMysticalPendantPageInvalid = 157054,
CollectionCfgWrong = 157055,
CollectionMysticalPendantSuitNotFounded = 157056,
CollectionMysticalPendantSuitNotGathered = 157057,
CollectionCombineParaErrClassExceed = 157058,
CollectionBattleConsumeInvalidGameRule = 157059,
CollectionBattleConsumeNotFound = 157060,
CollectionClientPriceWrong = 157061,
DLockHeldByOthers = 158000,
DLockHeldAlready = 158001,
DLockNotExisted = 158002,
DLockIgnoreReq = 158003,
DLockQuorumFail = 158004,
DLockNotReady = 158005,
DLockInnerFailed = 158006,
DLockExceedMaxDuration = 158007,
DLockExceedMaxWaiters = 158008,
DLockDurationTooSmall = 158009,
DLockAmbigousDuration = 158010,
DLockAlreadyExpired = 158011,
ActivityLoadDBFailed = 159000,
ActivitySaveDBFailed = 159001,
ActivityInvaildReqParam = 159002,
ActivityCantFindTask = 159003,
ActivityTaskAlreadyAccepted = 159004,
ActivityTaskIsLocking = 159005,
ActivityTaskNotCompleted = 159006,
ActivityTaskRewarded = 159007,
ActivityTaskRewardFailed = 159008,
ActivityTaskConfigError = 159009,
ActivityCallDepositFailed = 159010,
ActivityTaskNotAccepted = 159011,
ActivityCantFindActv = 159012,
ActivityCantFindActvConfig = 159013,
ActivityCantFindActvDaysConfig = 159014,
ActivityRepeatedAttend = 159015,
ActivityAttendIsFull = 159016,
ActivityOverMaxTrackingNum = 159017,
ActivityCantTracking = 159018,
ActivityIsTracking = 159019,
ActivityIsNotTracking = 159020,
ActivityCantFindActvReward = 159021,
ActivityCantFindActvRewardConfig = 159022,
ActivityPriceNotOk = 159023,
ActivityExchangeAttachMax = 159024,
ActivityNoEnoughCurrency = 159025,
ActivityCantFindActvMilestone = 159026,
ActivityMilestoneReceived = 159027,
ActivitySendPropsFailed = 159028,
ActivityAttendTooEarly = 159029,
ActivityUnknownInventory = 159030,
ActivityCallCollectFailed = 159031,
ActivityCallHeroFailed = 159032,
ActivityCallCurrencyFailed = 159033,
ActivityFinalRewardDuplicate = 159034,
ActivityFinalRewardNotFound = 159035,
ActivityMilestoneRewardNotFound = 159036,
ActivityCantUnTracking = 159037,
ActivityAddPropsOverFlow = 159038,
ActivityAddPropsNoSpace = 159039,
ActivityAddPropsFailed = 159040,
ActivityDryrunSendPropFailed = 159041,
ActivityDryrunSendRPCFailed = 159042,
ActivityCallAccountRPCFailed = 159043,
ActivityCallAccountFailed = 159044,
ActivityCallSeasonRPCFailed = 159045,
ActivityCallSeasonFailed = 159046,
ActivityRewardsMismatch = 159047,
ActivityDailyRefreshExceedMaxTimes = 159048,
ActivityDailyTaskCompleted = 159049,
ActivityAcquireDlockFail = 159050,
ActivityGameItemBuilderFail = 159051,
ActivityPasswordBoxConfigNotFound = 159052,
ActivityPasswordBoxUnlockedAlready = 159053,
ActivityTaskRewardsNotFound = 159054,
ActivityDailyTaskRefreshFail = 159055,
ActivityFinalRewardTypeNotFound = 159056,
ActivityTypeHandlerNotFound = 159057,
ActivityMossConstructConfigNotFound = 159058,
ActivityMossConstructDataInvalid = 159059,
ActivityRefreshDBFail = 159060,
ActivityStarFireAlreadyCharged = 159061,
ActivityStarFirePowerNotEnough = 159062,
ActivitySubmitDuplicate = 159063,
ActivityTaskAlreadyCompleted = 159064,
ActivityDeletePropFailed = 159065,
ActivityAddPropFailed = 159066,
ActivityTaskCannotBeCompleted = 159067,
ActivityTypeInvalid = 159068,
ActivitySequenceRewardConfigNotFound = 159069,
ActivityGoalNotFound = 159070,
ActivityGoalConfigNotFound = 159071,
ActivityEavesdroppingCaseNotFinish = 159072,
ActivityEavesdroppingCaseAleadyRecv = 159073,
ActivityEavesdroppingSpecialAleadyRecv = 159074,
ActivityEavesdroppingCaseNotOpen = 159075,
ActivityEavesdroppingCurrencyNotEnough = 159076,
ActivityEavesdroppingAlreadyAnswered = 159077,
ActivityInternalErr = 159078,
ActivityRelinkConfigNotFound = 159079,
ActivityRelinkMaterialNotEnough = 159080,
ActivityRelinkExchangeLocked = 159081,
ActivityThemeStarDecryptNotEnough = 159082,
ActivityDBInfoInvalid = 159083,
ActivityGoalTypeInvalid = 159084,
ActivityQuestionConfigNotFound = 159085,
ActivityQuestionAnswerDuplicate = 159086,
ActivityQuestionAnswerInvalid = 159087,
ActivityRequestDuplicate = 159088,
ActivityLoadDBProfileFailed = 159089,
ActivitySOCDrawInternalErr = 159090,
ActivitySOCDrawMPPackNumErr = 159091,
ActivitySOCDrawSOLPackNumErr = 159092,
ActivitySOCDrawScissorNumErr = 159093,
ActivitySOCSendCardFriendRelationErr = 159094,
ActivitySOCSendCardSendLimitErr = 159095,
ActivitySOCSendCardRecvLimitErr = 159096,
ActivitySOCSendFriendNoJoinErr = 159097,
ActivitySOCSendCardNumErr = 159098,
ActivitySOCCompositeInternalErr = 159099,
ActivitySOCCompositeOwnedCardNumErr = 159100,
ActivitySOCExchangeOwnedCardNumErr = 159101,
ActivitySOCExchangeBuyLimitNumErr = 159102,
ActivityExchangeLocked = 159103,
ActivitySBCRewardNumInvalid = 159104,
ActivitySBCAddRewardDuplicate = 159105,
ActivitySBCDeleteInvalidReward = 159106,
ActivitySBCAdjustTimeNotEnough = 159107,
ActivityTaskRewardConfigInvalid = 159108,
ActivityExchangePlanNotFound = 159109,
ActivityArknightsRecruitAllHeroRecruited = 159110,
ActivityArknightsRecruitConfigNotFound = 159111,
ActivityCurrencyNotEnough = 159112,
ActivityExchangeCurrencyInvalidArgs = 159113,
ActivityPropSendMail = 159114,
ActivityArknightsRecruitInternal = 159115,
ActivityArknightsGameInternal = 159116,
ActivityArknightsGameInvalidReq = 159117,
ActivityAhsarahTravelInternal = 159118,
ActivityAhsarahTravelInvalidReq = 159119,
IDCLBAllocIdcagentFailed = 160000,
IDCLBGetIDCNameFailed = 160001,
IDCLBSendStopNtfHasEmptyIDCAgentID = 160002,
StressTestKickPlayerFailed = 161000,
IpCityNotFound = 162000,
IpCityFailLoadProfile = 162001,
IpCityInvalidLoginIP = 162002,
ReconciliationInvalidParam = 163000,
ReconciliationCallAuctionFail = 163001,
ReconciliationCallMallFail = 163002,
ReconciliationSysFail = 163003,
ReconciliationBusy = 163004,
ABTSDKNewCtxFailed = 164000,
ABTSDKGetExpFailed = 164001,
ABTSDKExpTagNotExist = 164002,
IDIPBusy = 165000,
IDIPLoadDBFailed = 165001,
IDIPSaveDBFailed = 165002,
ResourceReqIndexOutOfBounds = 166000,
ResouceForwardClientFailed = 166001,
RankBadReqListName = 16700,
RankBadReqPageSize = 16701,
RankBadReqPageNum = 16702,
RankListNotSupported = 16703,
RankBadReqPlayer = 16704,
RankPlayerBanned = 16705,
RankConfNotFound = 16706,
RankLoadDBFriendListFailed = 16707,
RankLoadDBPlayerScoreFailed = 16708,
RankSaveDBPlayerScoreFailed = 16709,
RankLoadDBGlobalListFailed = 16710,
RankSaveDBGlobalListFailed = 16711,
RankLoadDBProfileFailed = 16712,
RankLoadGlobalCachedListFailed = 16713,
RankBadReqScore = 16714,
RankBusy = 16715,
RankBoardSystem = 167001,
RankBoardDBLoadFailed = 167002,
RankBoardGetTooMany = 167003,
RankBoardGetStartIdxInvalid = 167004,
RankBoardRankTypeNotExist = 167005,
RankBoardRankDataInConsistent = 167006,
RankBoardInvaldReqParam = 167007,
RankBoardLoadRankAuxDataFail = 167008,
RankBoardSaveRankAuxDataFail = 167009,
RankSvrBusy = 167010,
RankSaveRankBoardDataFail = 167011,
RankBoardHandBookNotExist = 167012,
RankCallServcieFail = 167013,
RankReadConfFail = 167014,
RankAuxDataNil = 167015,
RankLoadRankAwardScheduleFail = 167016,
RankSaveRankAwardScheduleFail = 167017,
RankBoardDataInvalid = 167018,
RankBoardAdcodeInvalid = 167019,
RankBoardGiveAwardFail = 167020,
RankBoardNotInSeason = 167021,
TSSCreditEnvNotIDC = 168000,
TSSCreditLoadProfileErr = 168001,
TSSCreditLoadCacheErr = 168002,
TSSCreditQueryHttpErr = 168003,
TSSScnLimitQuerySuc = 168004,
TSSScnLimitQueryConfKeyNoFind = 168005,
TSSScnLimitQueryConfFail = 168006,
TSSScnLimitQueryFail = 168007,
MarketInitDBFailed = 169000,
MarketPropCanNotSell = 169001,
MarketGetDBSaleListFailed = 169002,
MarketInvalidTableIndex = 169003,
MarketSaleNotFound = 169004,
MarketSellNotValid = 169005,
MarketDepositRestoreFailed = 169006,
MarketDepositCheckFailed = 169007,
MarketBusy = 169008,
MarketSaveDBSaleListFailed = 169009,
MarketPropDescNotFound = 169010,
MarketInvalidRackDuration = 169011,
MarketInvalidRackFee = 169012,
MarketInvalidCurrency = 169013,
MarketInvalidPrice = 169014,
MarketInvalidPropGid = 169015,
MarketInvalidPropNum = 169016,
MarketDepositExchangeFailed = 169017,
MarketNotInAutoRackTime = 169018,
MarketAutoRackDescNotFound = 169019,
MarketPropNeedInit = 169020,
MarketGeneratePropGidFailed = 169021,
MarketUpdateMarketFailed = 169022,
MarketSaleListInvalid = 169023,
MarketGetPlayerMarketFailed = 169024,
MarketSavePlayerMarketFailed = 169025,
MarketPropNotEnough = 169026,
MarketCollectionNotFound = 169027,
MarketUpdatePlayerOrderFailed = 169028,
MarketPlayerOrderNotFound = 169029,
MarketOrderCntExceed = 169030,
MarketPlayerDismatch = 169031,
MarketInvalidReq = 169032,
MarketPriceStepNotMatch = 169033,
MarketNotInAutoBuyTime = 169034,
MarketAutoBuyDisable = 169035,
MarketAutoBuyDescNotFound = 169036,
MarketAutoRackDisable = 169037,
MarketSystemError = 169038,
MarketPropDismatch = 169039,
MarketPriceZero = 169040,
MarketSendMailFailed = 169041,
MarketNoMallRecyclePrice = 169042,
MarketPriceRangeInvalid = 169043,
MarketPropBinded = 169044,
MarketGenerateOrderIDFailed = 169045,
MarketMaxCollectionsExceeded = 169046,
MarketCallSafehouseFailed = 169047,
MarketPriceSumIsZero = 169048,
MarketGetMarketPropDBFailed = 169049,
MarketGetSaleListBatchTooMuch = 169050,
MarketPullOffNumIsNotValid = 169051,
MarketGameItemNotFound = 169052,
MarketManualRackIsNil = 169053,
MarketSellIsLocked = 169054,
MarketBuyIsLocked = 169055,
MarketSellTransactionFailed = 169056,
MarketBuyTransactionFailed = 169057,
MarketDurationNotFull = 169058,
MarketDurationLowerThanMinimum = 169059,
MarketCacheGetTypeListError = 169060,
MarketPullOffTransactionError = 169061,
MarketRenewTransactionFailed = 169062,
MarketRenewNotFound = 169063,
MarketPlayerBanned = 169064,
MarketPlayerNotBanned = 169065,
MarketGMTransactionError = 169066,
MarketUnknownError = 169067,
MarketSearchSkinError = 169068,
MarketSearchItemError = 169069,
MarketSearchArmorError = 169070,
MarketInvalidInsuranceFee = 169071,
MarketNotFoundDurablityLvl = 169072,
MarketLoadDBFailed = 169073,
MarketSaveDBFailed = 169074,
MarketPropMaxStackNumInvalid = 169075,
MarketNotFoundMapConfig = 169076,
MarketPrepareFailed = 169077,
MarketIsNotOpen = 169078,
MarketBuyLimit = 169079,
MarketSellLimit = 169080,
MarketOffSheldCD = 169081,
MarketAfterBuyForbidSellCD = 169082,
MarketPropIsFrozen = 169083,
MarketForbidSell = 169084,
MarketCurrencyNotEnough = 169085,
MarketWatchLimit = 169086,
MarketGlobalPreBuyOrderLimit = 169087,
MarketPreBuyLimit = 169088,
MarketAddTryTimeFailed = 169089,
MarketOverBuyNumExceed = 169090,
MarketTradeAuditFailed = 169091,
MarketHasSameMeleeSkin = 169092,
BattlePassServerBusy = 170000,
BattlePassInvalidReqParams = 170001,
BattlePassInvalidSeason = 170002,
BattlePassInvalidPrice = 170003,
BattlePassRepeatPurchase = 170004,
BattlePassClueNotReceivalbe = 170005,
BattlePassArchiveNotReceivable = 170006,
BattlePassPackRepeatPurchase = 170007,
BattlePassPropFailed = 170008,
BattlePassClueAlreadyFinished = 170009,
BattlePassLocked = 170010,
BattlePassClueRewardEmpty = 170011,
BattlePassArchiveRewardEmpty = 170012,
BattlePassMaxExprLimit = 170013,
BattlePassLoadDBFail = 170014,
BattlePassPackIsNull = 170015,
AchieveInitDBManager = 172000,
AchieveInitDBManagerLockFail = 172001,
AchieveInitDBManagerLoadData = 172002,
AchieveDBSaveFail = 172003,
AchieveGMCompleteInvalidReq = 172010,
AchieveSendPropsPreCheckFail = 172011,
AchieveSendPropsCommitFail = 172012,
MossAIBusy = 173000,
MossAICallRemoteAgentFail = 173001,
MossAIDisable = 173002,
MossAIInvalidReqParams = 173003,
MossAILoadDBFailed = 173004,
MossAISaveDBFailed = 173005,
MossAINotInActTime = 173006,
MossAIPlayerBan = 173007,
MossAIRemoteErrCode = 173008,
MossAIRoleInvalid = 173009,
PlayerCopyReqParamInvalid = 174000,
PlayerCopyCallServiceFailed = 174001,
PlayerCopyServerBusy = 174002,
PlayerCopyLoadDBFailed = 174003,
BhdSvrServiceBusy = 175000,
BhdChapterNotExist = 175001,
BhdGenDSRoomIDFail = 175002,
BhdNoTeamCanQuickJoin = 175003,
BhdDBErr = 175004,
BhdNoQuickJoinMap = 175005,
BhdNotUnlocked = 175006,
BhdNotBought = 175007,
BhdSvrCallServiceBusy = 175008,
BhdPlayerStateInvalid = 175009,
BhdNextLevelReadyFail = 175010,
BhdGiveRewardFail = 175011,
BhdVersionNotValid = 175012,
BhdNextLevelIsRuning = 175013,
BhdServerIsFull = 175014,
BhdServiceIsNotOpen = 175015,
BhdPlatIDNotValid = 175016,
CrossBypassSendReqFail = 176000,
CrossBypassCallReqFail = 176001,
CrossBypassCalleeIsCaller = 176002,
CrossBypassParseCallerFail = 176003,
CrossBypassLoopRiskDetected = 176004,
MatchDataInvalidReqParam = 177000,
MatchDataLoadDBFail = 177001,
MatchDataSaveDBFail = 177002,
MatchDataGetDBDSRoomFailed = 177004,
MatchDataServerBusy = 177005,
MatchDataNoCourseSelected = 177006,
MatchDataNoCourseCfg = 177007,
MatchDataDlockFail = 177008,
MatchDataGetArenaCfgFail = 177009,
MatchDataGetPingFail = 177010,
MatchDataHasNoPing = 177011,
MatchDataGetIdcToAreaFail = 177012,
MatchDataAreaIsInvalid = 177013,
DirectpurReqParamInvalid = 178000,
DirectpurCallServiceFailed = 178001,
DirectpurServerBusy = 178002,
DirectpurLoadDBFailed = 178003,
DirectpurUpdateDBFailed = 178004,
DirectpurOrderIdInvalid = 178005,
CdpLoadDBFailed = 179001,
SupportServerBusy = 180000,
SupportSvrDataTypeNotValid = 180001,
SupportSvrLoadDBFailed = 180002,
SupportSvrSaveDBFailed = 180003,
SupportSvrReqParamsNotValid = 180004,
}
