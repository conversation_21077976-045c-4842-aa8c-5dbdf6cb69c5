----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class TopBarHD : LuaUIBaseView
local TopBarHD = ui("TopBarHD")
local CommonBarRegLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarRegLogic"
local CommonBarReddotLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarReddotLogic"
local TopBarAvatarMainHD = require "DFM.Business.Module.CommonBarModule.UI.TopBarHD.TopBarAvatarMainHD"
local CommonBarHDTabGroupLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarHDTabGroupLogic2"
local Config = require "DFM.Business.Module.CommonBarModule.CommonBarConfig"
local FAnchors = import("Anchors")
local UILockData = require "DFM.Business.DataStruct.UIDataStruct.UILockData"
local CommonBarUnlockLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarUnlockLogic"

local EHorizontalAlignment = import("EHorizontalAlignment")
local EVerticalAlignment = import "EVerticalAlignment"
local FSlateChildSize = import "SlateChildSize"
local ESlateSizeRule = import "ESlateSizeRule"
local FMargin = import "Margin"

--- BEGIN MODIFICATION @ VIRTUOS
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
--- END MODIFICATION

local function romzhang_(...)
    loginfo("[romzhang] ", ...)
end

TopBarHD.ETabGroupMode = {
    None = 0,
    Primary = 1,
    Secondary = 2,
    Tertiary = 4
}

local ETabGroupMode = TopBarHD.ETabGroupMode

-- 本地调试文件
local debugNetbar = isexist("DFM.Business.Module.CommonBarModule.Debug.DebugNetbar")

function TopBarHD:Ctor()
    self.bViewInitialHide = true

    ---------------------------------------------------------------------------------
    --- 核心数据列表
    ---------------------------------------------------------------------------------
    self._bEnableAutoStackUIResponse = true
    self._currencyTipHandle = nil
    self._reddotProxiesArr = {{}, {}, {}}
    self._lockDataCacheArr = {{}, {}, {}}
    self._tabGroupRegInfoCacheArr = {nil, nil, nil}
    self._onTabChangedProxyArr = {nil, nil, nil}
    self._preIDForShowList = nil

    -- self._usingPrimaryTabGruop = true
    -- self._forceSettingTabIndex = false
    self._cachedTabIndex = {}
    self._curTabGroupMode = ETabGroupMode.None
    self._cacheBackText = Module.CommonBar.Config.Loc.BackText
    self.currentViewUINavID = 0

    ---------------------------------------------------------------------------------
    --- Title和底图
    ---------------------------------------------------------------------------------
    self._wtRetainerBoxTitle = self:Wnd("DFRetainerBox_1", UIWidgetBase)
    self._wtRetainerBoxTitle:SetCppValue("RenderOnInvalidation", true)
    self._wtCPTile = self:Wnd("CanvasPanel_Title", UIWidgetBase)
    self._wtTitle = self:Wnd("Text_Title", UITextBlock)
    ---纹理底图
    self._wtTextureImg = self:Wnd("RTBg01", UIWidgetBase)
    self._wtDFImage_Mask = self:Wnd("DFImage_Mask", UIWidgetBase)

    ---------------------------------------------------------------------------------
    --- Currency
    ---------------------------------------------------------------------------------
    self._wtCanvasRoot = self:Wnd("CanvasPanel_0", UIWidgetBase)
    self._wtHbAll = self:Wnd("HorizontalBox_All", UIWidgetBase)
    ---currency
    self._mapId2InstanceId = {}
    self._wtGP_Currency = self:Wnd("GP_Currency", UIWidgetBase)
    self._wtGP_Currency:ClearChildren()
    self._wtBtn_Currency = self:Wnd("Btn_Currency", UIButton)

    self._wtTipsAnchor_Currency =
        UIUtil.WndTipsAnchor(self, "_wtTipsAnchor_Currency", self._OnShowCurrencyTips, self._OnHideCurrencyTips)
    
    ---------------------------------------------------------------------------------
    --- 头像
    ---------------------------------------------------------------------------------
    -- self._wtAvatarMain = self:Wnd("WBP_TopBarHD_AvatarMain", TopBarAvatarMainHD)
    self._wtAvatarPanel = self:Wnd("_wtAvatarPanel", UILightWidget)
    self._wtAvatarBackground = self:Wnd("_wtAvatarBackground", UIImage)

    ---------------------------------------------------------------------------------
    --- 设置和邮件
    ---------------------------------------------------------------------------------
    self._wtHbCurrencyRight = self:Wnd("DFHb_CurrencyRight", UIWidgetBase)
    self._wtWBP_ButtonSet = self:Wnd("WBP_ButtonSet", UIWidgetBase)
    self._wtBtn_Settings_Icon = self._wtWBP_ButtonSet:Wnd("Image_5", UIImage)
    self._wtBtn_Settings = self._wtWBP_ButtonSet:Wnd("Button_4", UIButton)
    self._wtBtn_Settings:Event("OnClicked", self._OnOpenSettings, self)
    self._wtTipsAnchor_Settings =
        UIUtil.WndTipsAnchor(
        self._wtWBP_ButtonSet,
        "_wtTipsAnchor",
        self._OnShowSettingTips,
        self._OnHideSettingTips,
        self
    )
    self._wtWBP_ButtonMail = self:Wnd("WBP_ButtonMail", UIWidgetBase)
    -- BEGIN MODIFICATION @ VIRTUOS : 绑定邮件按钮的快捷键图标组件
    self._wtButtonSetIcon = self._wtWBP_ButtonSet:Wnd("KeyIcon", HDKeyIconBox)
    self._wtButtonMailIcon = self._wtWBP_ButtonMail:Wnd("KeyIcon", HDKeyIconBox)
    -- END MODIFICATION

    self._wtBtn_Mail = self._wtWBP_ButtonMail:Wnd("Button_1", UIButton)
    self._wtBtn_Mail:Event("OnClicked", self._OnOpenMail, self)
    self._wtTipsAnchor_Mail =
        UIUtil.WndTipsAnchor(self._wtWBP_ButtonMail, "_wtTipsAnchor", self._OnShowMailTips, self._OnHideMailTips, self)

    ---------------------------------------------------------------------------------
    --- Currency左侧列表
    ---------------------------------------------------------------------------------
    self._wtHbCurrencyLeft = self:Wnd("DFHb_CurrencyLeft", UIWidgetBase)
    self._reddotProxyTable = {}
    self._mapConfigIdx2BarIdx = {}
    ---------------------------------------------------------------------------------
    --- 一级|二级|三级页签
    ---------------------------------------------------------------------------------
    self:InitTabGroups()
    ---------------------------------------------------------------------------------
    --- 事件监听列表
    ---------------------------------------------------------------------------------
    self:AddLuaEventListeners()
end

function TopBarHD:InitTabGroups()
    self._wtTabGroupPrimary =
        UIUtil.WndTabGroupBox(
        self,
        "_wtTabGroupPrimary",
        function()
            return self:_OnGetTabItemCount(1)
        end,
        function(_, position, itemWidget) -- fOnProcessTabItemWidget
            Module.Guide:AddGuideWidgetProxy(
                Module.Guide.Config.EGuideProxyWidget[string.format("guideProxyTopBarPrimary%d", position)],
                itemWidget:BP_GetReddotPanel()
            )
            return self:_OnProcessTabItem(position, itemWidget, 1)
        end,
        function(_, tabIndex, lastTabIndex) -- fOnCheckedTabIndexChanged
            return self:_OnTabIndexChanged(tabIndex, lastTabIndex, 1)
        end
    )
    self._wtTabNavigatorPrimary = self:Wnd("_wtTabNavigatorPrimary", UILightWidget)
    self._wtTabNavigatorPrimary:SetCppValue("ActionPriority", EDisplayInputActionPriority.UI_Stack)

    self._wtTabGroupSecondary =
        UIUtil.WndTabGroupBox(
        self,
        "_wtTabGroupSecondary",
        function()
            return self:_OnGetTabItemCount(2)
        end,
        function(_, position, itemWidget)
            return self:_OnProcessTabItem(position, itemWidget, 2)
        end,
        function(_, tabIndex, lastTabIndex)
            return self:_OnTabIndexChanged(tabIndex, lastTabIndex, 2)
        end
    )

    self._wtTabGroupSecondary2 =
        UIUtil.WndTabGroupBox(
        self,
        "_wtTabGroupSecondary2",
        function()
            return self:_OnGetTabItemCount(2)
        end,
        function(_, position, itemWidget)
            return self:_OnProcessTabItem(position, itemWidget, 2)
        end,
        function(_, tabIndex, lastTabIndex)
            return self:_OnTabIndexChanged(tabIndex, lastTabIndex, 2)
        end
    )

    self._wtTabNavigatorSecondary = self:Wnd("_wtTabNavigatorSecondary", UILightWidget)
    self._wtTabNavigatorSecondary:SetCppValue("ActionPriority", EDisplayInputActionPriority.UI_Stack)
    self._wtTabNavigatorSecondary2 = self:Wnd("_wtTabNavigatorSecondary2", UILightWidget)
    self._wtTabNavigatorSecondary2:SetCppValue("ActionPriority", EDisplayInputActionPriority.UI_Stack)

    self._wtTabGroupTertiary =
        UIUtil.WndTabGroupBox(
        self,
        "_wtTabGroupTertiary",
        function()
            return self:_OnGetTabItemCount(3)
        end,
        function(_, position, itemWidget)
            return self:_OnProcessTabItem(position, itemWidget, 3)
        end,
        function(_, tabIndex, lastTabIndex)
            return self:_OnTabIndexChanged(tabIndex, lastTabIndex, 3)
        end
    )

    self._wtTabNavigatorTertiary = self:Wnd("_wtTabNavigatorTertiary", UILightWidget)
    self._wtTabNavigatorTertiary:SetCppValue("ActionPriority", EDisplayInputActionPriority.UI_Stack)

    self._wtTabNavigatorPrimary:Collapsed()
    self._wtTabNavigatorSecondary:Collapsed()
    self._wtTabNavigatorSecondary2:Collapsed()
    self._wtTabNavigatorTertiary:Collapsed()

    self._wtTabGroupPrimary:SelfHitTestInvisible()
    self._wtTabGroupSecondary:SelfHitTestInvisible()
    self._wtTabGroupSecondary2:SelfHitTestInvisible()
    self._wtTabGroupTertiary:SelfHitTestInvisible()

    --- BEGIN MODIFICATION @ VIRTUOS
    self._tabsPanelMap = {
        { navigator = self._wtTabNavigatorPrimary, group = self._wtTabGroupPrimary},
        { navigator = self._wtTabNavigatorSecondary, group = self._wtTabGroupSecondary},
        { navigator = self._wtTabNavigatorSecondary2, group = self._wtTabGroupSecondary2},
        { navigator = self._wtTabNavigatorTertiary, group = self._wtTabGroupTertiary}
    }

    for i = 1, #self._tabsPanelMap do
        local panel = self._tabsPanelMap[i]
        panel.group._wtTabContainer = panel.group:Wnd("CustomTabContainer", UIWidgetBase)
        panel.navigator._wtPrevKeyIcon = panel.navigator:Wnd("PrevKeyIcon", HDKeyIconBox)
        panel.navigator._wtNextKeyIcon = panel.navigator:Wnd("NextKeyIcon", HDKeyIconBox)
    end

end

function TopBarHD:AddLuaEventListeners()
    -- 绑定多输入设备切换事件
    self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))

    self:AddLuaEvent(Facade.UIManager.Events.evtStackUIBeforeChange, self.OnStackViewBeforeChange, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtStackUIChanged, self.OnStackViewChange, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtPopUIChanged, self.OnPopViewChange, self)

    if rawget(Server, "ModuleUnlockServer") then
        self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock, self._ShowModuleNtf, self)
    end

    if rawget(Server, "RoleInfoServer") then
        local debugOldNetbarPriv
        local debugOldCollegePriv
        if debugNetbar then
            debugOldNetbarPriv = Server.RoleInfoServer.netbarPriv
            debugOldCollegePriv = Server.RoleInfoServer.stuPriv
            Server.RoleInfoServer.netbarPriv = 2
            Server.RoleInfoServer.stuPriv = 1
        end

        loginfo("[TopBar] GetNetBarPrivilegeLevel:", Server.RoleInfoServer:GetNetBarPrivilegeLevel())
        loginfo("[TopBar] CollegePrivilegeValid:", Server.RoleInfoServer:IsCollegePrivilegeValid())

        if debugNetbar then
            Server.RoleInfoServer.netbarPriv = debugOldNetbarPriv
            Server.RoleInfoServer.stuPriv = debugOldCollegePriv
        end
    end

    if rawget(Server, "RecoveryServer") then
        self:AddLuaEvent(Server.RecoveryServer.Events.evtUpdateRecoveryAmount, self._OnUpdateRecoveryAmount, self)
        self:AddLuaEvent(Server.RecoveryServer.Events.evtSumbitedRecoverySuccess, self._OnUpdateRecoveryAmount, self)
    end

    if rawget(Server, "HopeServer") then
        self:AddLuaEvent(Server.HopeServer.Events.evtNetworkPingChange, self._OnUpdateNetworkStatus, self)
    end
end


function TopBarHD:OnOpen()
    romzhang_("TopBarHD:OnOpen")
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game then
        -- local bgPanel = self._wtAvatarMain:Wnd("EmptyBackground", UILightWidget)
        -- self._wtAvatarBackground:RemoveFromParent()
        -- bgPanel:AddChild(self._wtAvatarBackground)
        local weakUiIns = Facade.UIManager:AddSubUI(self, UIName2ID.TopBarAvatarMainHD, self._wtAvatarPanel, nil)
        self._wtAvatarMain = getfromweak(weakUiIns)
    else
        -- self._wtAvatarBackground:Collapsed()
    end

    self:_InitShortcuts()
    self:Reset()
end

function TopBarHD:OnHide()
    romzhang_("TopBarHD:OnHide")
    -- self._wtTabGroupPrimary:Collapsed()
    -- self._wtTabGroupSecondary:Collapsed()
    self._wtTabNavigatorPrimary:Collapsed()
    self._wtTabNavigatorSecondary:Collapsed()
    self._wtTabNavigatorSecondary2:Collapsed()
    self._wtTabNavigatorPrimary:UnbindActions()
    self._wtTabNavigatorSecondary:UnbindActions()
    self._wtTabNavigatorSecondary2:UnbindActions()

    -- BEGIN MODIFICATION @ VIRTUOS :
    self:_BindMailAction(false)
    self:_BindSettingsAction(false)
    -- END MODIFICATION

    self:ReleaseReddotProxies(1)
    self:ReleaseReddotProxies(2)
    self:ReleaseReddotProxies(3)

    self:ReleaseLockData(1)
    self:ReleaseLockData(2)
    self:ReleaseLockData(3)

    self._wtTipsAnchor_Currency:HideTipsManually()
    self._wtTipsAnchor_Settings:HideTipsManually()
    self._wtTipsAnchor_Mail:HideTipsManually()

    local Config = Module.CommonBar.Config
    if Config.PCTopCurrencyLeftInfo and next(Config.PCTopCurrencyLeftInfo) then
        self:ReleaseCurrencyLeftReddotProxies()
        for configIdx, tabInfo in ipairs(Config.PCTopCurrencyLeftInfo) do
            local barIndex = self._mapConfigIdx2BarIdx[configIdx]
            local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.TopBarHDIconBtn, barIndex)
            local topBarButton = getfromweak(weakUIIns)
            if topBarButton then
                topBarButton:Reset()
            end
        end
    end
end

function TopBarHD:OnClose()
    romzhang_("TopBarHD:OnClose")
    self._tabGroupRegInfoCacheArr = {nil, nil, nil}
    self._onTabChangedProxyArr = {nil, nil, nil}
    Module.CommonBar.Field:SetTabGroupRegInfoCache(1, nil)
    Module.CommonBar.Field:SetTabGroupRegInfoCache(2, nil)
    Module.CommonBar.Field:SetTabGroupRegInfoCache(3, nil)

    self._preIDForShowList = nil
    self._wtAvatarMain = nil
    self:RemoveAllLuaEvent()
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
    self:Reset()
    self:_RemoveShortcuts()
    Module.CommonBar:CloseBarSwitchMode()
    self._wtAvatarPanel = nil
    --反注册邮件红点
    if self._mailReddotProxy then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._mailReddotProxy)
        self._mailReddotProxy = nil
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 注册打开邮件快捷键
function TopBarHD:_BindMailAction(bEnable)
    if bEnable then
        if not self._hActionMail then
            self._hActionMail =
            self:AddInputActionBinding(
                "OpenMail",
                EInputEvent.IE_Pressed,
                self._OnOpenMail,
                self,
                EDisplayInputActionPriority.UI_Stack
            )
        end
        self._wtButtonMailIcon:InitKeyIcon("OpenMail")
    else
        if self._hActionMail then
            self:RemoveInputActionBinding(self._hActionMail)
            self._hActionMail = nil
        end
    end
end

-- 注册打开设置界面快捷键
function TopBarHD:_BindSettingsAction(bEnable)
    if bEnable then
        if not self._ActionSettings then
            self._ActionSettings =
            self:AddInputActionBinding(
                "OpenSettings",
                EInputEvent.IE_Pressed,
                self._OnOpenSettings,
                self,
                EDisplayInputActionPriority.UI_Stack
            )
        end
        self._wtButtonSetIcon:InitKeyIcon("OpenSettings")
    else
        if self._ActionSettings then
            self:RemoveInputActionBinding(self._ActionSettings)
            self._ActionSettings = nil
        end
    end
end
-- END MODIFICATION

function TopBarHD:OnShow()
    romzhang_("TopBarHD:OnShow")
    if self._wtAvatarMain then
        self._wtAvatarMain:SetAddBtnVisible(true)
    end

    --- BEGIN MODIFICATION @ VIRTUOS（Temp）
    -- 由于Mask Widget会遮挡平台Icon，临时关闭TopBar Mask
    if IsPS5() and self._wtDFImage_Mask then
        self._wtDFImage_Mask:Collapsed()
    end
    --- END MODIFICATION

    if Facade.ModuleManager:IsModuleValid("ExpansionPackCoordinator") then
        self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
    end
end

function TopBarHD:Reset()
    -- self:Show(false)
    self._wtTabGroupPrimary:RevertAllTabOriginalSoundNames()
    self._wtTabGroupSecondary:RevertAllTabOriginalSoundNames()
    self._wtTabGroupSecondary2:RevertAllTabOriginalSoundNames()
    self._wtTabGroupTertiary:RevertAllTabOriginalSoundNames()

    self:SetTopRenderOpacity(1)
    self:SetTopSideStyle(Config.ETopBarStyleFlag.None)
    -- self._wtHbTabGroup:Collapsed()
    self._wtTipsAnchor_Currency:HideTipsManually()
    self._curStyle = 0
    self:SetCurrencyLeftIcons(self._curStyle, false)
    --资源下载设置按钮tip状态
    self:_RefreshSettingButtonState()
end

function TopBarHD:ResetStyle()
    self:SetTopSideStyle(Config.ETopBarStyleFlag.None)
end

function TopBarHD:_InitShortcuts()
    --[[self._hActionOpenTeam =
        inputMonitor:AddDisplayActionBinding(
        "OpenTeam",
        EInputEvent.IE_Pressed,
        self._OnOpenTeam,
        self,
        EDisplayInputActionPriority.UI_Stack
    )]]
end

function TopBarHD:_RemoveShortcuts()
end
--------------------------------------------------------------------------
--- Setting
--------------------------------------------------------------------------

function TopBarHD:_OnOpenSettings()
    -- Module.SystemSetting.ShowSystemSettingMainView()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDMainView, nil, nil, nil)
    local topbarGameMode = Module.CommonBar:GetTopbarGameMode()
    if topbarGameMode == 1 then
        LogAnalysisTool.SignButtonClicked(10120006)
        loginfo("[CommonBar] mp click setting")
    elseif topbarGameMode == 2 then
        LogAnalysisTool.SignButtonClicked(10030010)
        loginfo("[CommonBar] safehouse click setting")
    end
end

function TopBarHD:_OnShowSettingTips()
    if Module.ExpansionPackCoordinator:HaveInstallingQuests() then
        local function onTipLoaded(uiIns)
            --local currencyData = self._preIDForShowList or {}
            --uiIns:SetCurrencyData(currencyData)
            ---- 将Tips绑定到Anchor
            self._wtTipsAnchor_Settings:BindTipsWidget(uiIns)
        end
        -- 创建Tips
        self._DownloadProgressTipHandle = Facade.UIManager:AsyncShowUI(UIName2ID.PackDownloadProgressTipsHD, onTipLoaded)
    else
        self._hSettingTips =
        Module.CommonTips:ShowAssembledTips(
                {{id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = Module.CommonBar.Config.Loc.Setting}}},
                self._wtTipsAnchor_Settings
        )
    end
end

function TopBarHD:_OnHideSettingTips()
    if self._hSettingTips then
        Module.CommonTips:RemoveAssembledTips(self._hSettingTips, self._wtTipsAnchor_Settings)
        self._hSettingTips = nil
    end

    if self._DownloadProgressTipHandle then
        Facade.UIManager:CloseUIByHandle(self._DownloadProgressTipHandle)
        self._DownloadProgressTipHandle = nil
    end
end
--------------------------------------------------------------------------
--- Title
--------------------------------------------------------------------------

function TopBarHD:ChangeTitle(titleText)
    self._wtTitle:SetText(titleText)
end

-- pc 实际上没有这个按钮，直接设置到文字上
function TopBarHD:ChangeBackBtnText(titleText)
    self:ChangeTitle(titleText)
end

function TopBarHD:SetTopBackBarTxtColor(color)
    self._wtTitle:SetColorAndOpacity(color)
end

--------------------------------------------------------------------------
--- Currency
--------------------------------------------------------------------------

function TopBarHD:SetCurrencyVisible(bVisible)
    loginfo("TopBarHD:SetCurrencyVisible", bVisible)

    if bVisible then
        self._wtBtn_Currency:Visible()
        self._wtGP_Currency:SelfHitTestInvisible()
        self._wtTextureImg:SelfHitTestInvisible()
    else
        self._wtGP_Currency:Collapsed()
        self._wtBtn_Currency:Collapsed()
        self._wtTextureImg:Collapsed()
    end
end

function TopBarHD:SetTopCurrencyTypes(currencyIDForShowList, currentView)
    loginfo(
        "TopBarHD:SetTopCurrencyTypes",
        currencyIDForShowList and #currencyIDForShowList,
        currentView and currentView._cname
    )
    if self._preIDForShowList ~= nil and table.equal(self._preIDForShowList, currencyIDForShowList) then
        return
    end

    --- reset
    for id, instanceId in pairs(self._mapId2InstanceId) do
        Facade.UIManager:RemoveSubUI(self, UIName2ID.CurrencyIconNumHD, instanceId)
    end
    self._mapId2InstanceId = {}

    if not Facade.ModuleManager:IsModuleValid("Currency") then
		return
	end

	local currencyIDForShowList = CommonBarRegLogic.SortCurrencyIdInOrder(currencyIDForShowList)
    -- local numList = #currencyIDForShowList
    -- local maxCol = math.floor((numList - 1) / 2)
    for idx, currencyId in ipairs(currencyIDForShowList) do
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CurrencyIconNumHD, self._wtGP_Currency)
        self._mapId2InstanceId[currencyId] = instanceId
        local currencyIconNum = getfromweak(weakUIIns)
        if currencyIconNum then
            local num = Facade.ModuleManager:IsModuleValid("Currency") and Module.Currency:GetRealNum(currencyId) or 0
            currencyIconNum:SetCurrencyInfoById(currencyId, num)
            --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation
            if IsHD() then
                currencyIconNum:SetGamepadFeature()
            end
            --- END MODIFICATION
        end

        -- local col = maxCol - math.floor((idx - 1) / 2)
        -- local row
        -- if idx == numList and idx % 2 == 1 then
        --     row = 1
        -- else
        --     row = 1 - math.floor((idx - 1) % 2)
        --     if row == 1 then
        --         row = 2
        --     end
        -- end
        local col = idx - 1
        local row = 0
        currencyIconNum.Slot:SetColumn(col)
        currencyIconNum.Slot:SetRow(row)

        -- currencyIconNum.Slot:SetPadding(FMargin(20, 0, 0, 0))
    end
    self:BP_ArrangeCurrency()
    self._preIDForShowList = currencyIDForShowList
end

--设置货币栏（按照currencyIDForShowList顺序）
function TopBarHD:SetTopCurrencyTypesOrderly(currencyIDForShowList)
    self:SetTopCurrencyTypes(currencyIDForShowList)
end

function TopBarHD:RefreshCurrencyTypeList(currencyTypeList)
    self:SetTopCurrencyTypesOrderly(currencyTypeList)
end

function TopBarHD:_OnShowCurrencyTips(reason)
    self:_OnHideCurrencyTips()

    local function onTipLoaded(uiIns)
        local currencyData = self._preIDForShowList or {}
        uiIns:SetCurrencyData(currencyData)
        -- 将Tips绑定到Anchor
        self._wtTipsAnchor_Currency:BindTipsWidget(uiIns)
    end
    -- 创建Tips
    self._currencyTipHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CurrencyTipsHD, onTipLoaded)
end

function TopBarHD:_OnHideCurrencyTips(reason)
    -- 解绑Anchor
    self._wtTipsAnchor_Currency:UnBindTipsWidget()
    if self._currencyTipHandle then
        Facade.UIManager:CloseUIByHandle(self._currencyTipHandle)
        self._currencyTipHandle = nil
    end
end

--------------------------------------------------------------------------
--- Recovery
--------------------------------------------------------------------------

function TopBarHD:_ShouldDisplayRecoveryIcon()
    local armedForceMode
    local isInRecoveryState
    if rawget(Server, "ArmedForceServer") then
        armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    end
    if rawget(Server, "RecoveryServer") then
        isInRecoveryState = Server.RecoveryServer:IsInRecoveryState()
    end
    return armedForceMode and isInRecoveryState and armedForceMode == EArmedForceMode.SOL
end


function TopBarHD:_OnUpdateRecoveryAmount(changes, bhasRecoveredAll)
    if bhasRecoveredAll then
        self:PlayWidgetAnim(self.WBP_TopBarHDSlotPanelV1_out_01)
    end
end

function TopBarHD:OnAnimFinished(anim)
    if anim == self.WBP_TopBarHDSlotPanelV1_out_01 then
         local topBarButton = self:GetTopBarHDBtn("Recovery")
        if topBarButton then
            topBarButton:Hide(true, true)
        end
    end
end


--------------------------------------------------------------------------
--- Mail
--------------------------------------------------------------------------

function TopBarHD:_OnOpenMail()
    local topbarGameMode = Module.CommonBar:GetTopbarGameMode()
    if topbarGameMode == 1 then
        LogAnalysisTool.SignButtonClicked(10120005)
        loginfo("[CommonBar] mp click mail")
    elseif topbarGameMode == 2 then
        LogAnalysisTool.SignButtonClicked(10030009)
        loginfo("[CommonBar] safehouse click mail")
    end

    if Module.CommonBar:CheckAnyBlockUI() then
        return
    end
    Module.Mail:ShowMailPanel()
    self:ChangeTitle(Module.CommonBar.Config.Loc.Mail)
end

function TopBarHD:_ToggleMailReddot(bEnalbe)
    if bEnalbe then
        if not self._mailReddotProxy then
            self._mailReddotProxy =
                Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
                self._wtWBP_ButtonMail,
                {
                    {
                        obType = EReddotTrieObserverType.Mail,
                        key = "ReceiveMail"
                    }
                }
            )
        end
    else
        if self._mailReddotProxy then
            Module.ReddotTrie:UnRegisterStaticReddotDot(self._mailReddotProxy)
            self._mailReddotProxy = nil
        end
    end
end

--Team
function TopBarHD:_OnOpenTeam()
    Module.Social:OpenInvitePanel()
end

function TopBarHD:_OnShowMailTips()
    self._hMailTips =
        Module.CommonTips:ShowAssembledTips(
        {{id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = Module.CommonBar.Config.Loc.Mail}}},
        self._wtTipsAnchor_Mail
    )
end

function TopBarHD:_OnHideMailTips()
    if self._hMailTips then
        Module.CommonTips:RemoveAssembledTips(self._hMailTips, self._wtTipsAnchor_Mail)
        self._hMailTips = nil
    end
end

function TopBarHD:_CheckNetbarPrivilegeLevel(level)
    if not self.bNetbarPrivilege then
        return false
    end
    -- 0 级用于固定显示"网吧"这样的固定内容
    if self.netbarPrivilegeLevel == level or level == 0 then
        return true
    elseif level == nil then
        logerror('[TopBarHD] leonhxzhao PriorityTipsTable struct need update')
        return self.netbarPrivilegeLevel > 0
    end
    return false
end

--------------------------------------------------------------------------
--- Friend
--------------------------------------------------------------------------

function TopBarHD:_OnOpenFriendPanel()
    -- Module.Friend:ShowMainPanel()

    local topbarGameMode = Module.CommonBar:GetTopbarGameMode()
    if topbarGameMode == 1 then
        LogAnalysisTool.SignButtonClicked(10120011)
        loginfo("[CommonBar] mp click friend")
    elseif topbarGameMode == 2 then
        LogAnalysisTool.SignButtonClicked(10030013)
        loginfo("[CommonBar] safehouse click friend")
    end

    if Module.CommonBar:CheckAnyBlockUI() then
        return
    end
    Module.Mail:ShowMainPanel(Module.Mail.Config.ESocialType.Friend)
end

--------------------------------------------------------------------------
--- RedDot
--------------------------------------------------------------------------

--------------------------------------------------------------------------
--- Tab Group
--------------------------------------------------------------------------
local function _IsValidTabInfo(info)
    return info and (info.tabTxtList and next(info.tabTxtList) or (info.imgPathList and next(info.imgPathList)))
end

function TopBarHD:ReleaseReddotProxies(tabLevel)
    for _, proxy in pairs(self._reddotProxiesArr[tabLevel]) do
        if proxy.bNewReddotTrie then
            Module.ReddotTrie:UnRegisterStaticReddotDot(proxy[2])
        else
            -- 已废弃2024/6/25
            -- Module.Reddot:UnRegisterIndirect(proxy[1], proxy[2])
            logerror('TopBarHD:ReleaseReddotProxies(tabLevel) 旧红点遗址')
        end
    end
    self._reddotProxiesArr[tabLevel] = {}
end

function TopBarHD:ReleaseLockData(tabLevel)
    for _, cache in pairs(self._lockDataCacheArr[tabLevel]) do
        cache:Release()
    end
    self._lockDataCacheArr[tabLevel] = {}
end

function TopBarHD:RegTopTabGroupInitIdx(tabLevel, initIdx)
    local tabGroupRegInfo = self._tabGroupRegInfoCacheArr[tabLevel]
    if tabLevel == 3 then
        local parentTabGroupRegInfo = self._tabGroupRegInfoCacheArr[2]
		if parentTabGroupRegInfo and parentTabGroupRegInfo.tertiaryTabs then
			tabGroupRegInfo = parentTabGroupRegInfo.tertiaryTabs[parentTabGroupRegInfo.defalutIdx]
		end
    end
    if tabGroupRegInfo then
		CommonBarReddotLogic.OverrideTopTabGroupInfoIdx(tabGroupRegInfo, tabLevel, initIdx)
	else
		logerror('CommonBarReddotLogic.OverrideTopTabGroupInfoIdx [TopBarHD] initIdx & defalutIdx update failed', tabGroupRegInfo, initIdx, " tabLevel:", tabLevel)
	end
end

---@param topTabGroupRegInfo table
function TopBarHD:SetTopTabGroup(topTabGroupRegInfo, tabLevel, overrideIndex, bInvoke)
    topTabGroupRegInfo = CommonBarReddotLogic.NormalizeTopTabGroupRegInfo(topTabGroupRegInfo, tabLevel)

    self:ReleaseReddotProxies(tabLevel)
    self:ReleaseLockData(tabLevel)

    self._tabGroupRegInfoCacheArr[tabLevel] = topTabGroupRegInfo
    Module.CommonBar.Field:SetTabGroupRegInfoCache(tabLevel, topTabGroupRegInfo)

    topTabGroupRegInfo.defalutIdx = setdefault(topTabGroupRegInfo.defalutIdx, 1)
    topTabGroupRegInfo.bTriggerCallback = setdefault(topTabGroupRegInfo.bTriggerCallback, true)
    topTabGroupRegInfo.bReInitChildWidgets = setdefault(topTabGroupRegInfo.bReInitChildWidgets, true)

    if _IsValidTabInfo(topTabGroupRegInfo) then
        if tabLevel == 1 then
            self._curTabGroupMode = self._curTabGroupMode | ETabGroupMode.Primary
        elseif tabLevel == 2 then
            self._curTabGroupMode = self._curTabGroupMode | ETabGroupMode.Secondary
        else
            self._curTabGroupMode = self._curTabGroupMode | ETabGroupMode.Tertiary
        end

        local curTabGroup = self:_GetCurTabGroup(tabLevel)
        local _forceSettingTabIndex
        local _initTab
        local OnTabChangedProxy =
            CreateCallBack(
            function(self, curIndex, lastIndex, bForceCallback, tabLevel)
                if not _forceSettingTabIndex then
                    -- if not self._bIsRecoveringTabGroup then
                    if topTabGroupRegInfo.fCallbackIns then
                        if curIndex ~= lastIndex or bForceCallback then
                            if topTabGroupRegInfo.operaAction == EStackAction.Push or tabLevel == 1 then
                                topTabGroupRegInfo.fCallbackIns(curIndex, lastIndex, tabLevel)
                            else
                                if topTabGroupRegInfo.bPostCallbackWhenPop then
                                    topTabGroupRegInfo.fCallbackIns(curIndex, lastIndex, tabLevel)
                                end
                            end
                            --- MS24 默认记住上次选中的页签，关闭时回到initIdx
							topTabGroupRegInfo.defalutIdx = curIndex
                            if topTabGroupRegInfo.tertiaryTabs then
                                -- 二级页签切换时，恢复三级index
                                if not _initTab then
                                    self:RecoverDefaultTabIndex(3)
                                end
                                local tertiaryTabGroupRegInfo = topTabGroupRegInfo.tertiaryTabs[curIndex]
                                if tertiaryTabGroupRegInfo then
                                    tertiaryTabGroupRegInfo.operaAction = topTabGroupRegInfo.operaAction
                                    self:SetTopTabGroup(tertiaryTabGroupRegInfo, 3, nil, true)
                                else
                                    self:SetTopTabGroup({}, 3)
                                end
                            else
                                if tabLevel ~= 3 then
                                    self:SetTopTabGroup({}, 3)
                                end
                            end
                            topTabGroupRegInfo.operaAction = EStackAction.Push
                        end
                    end
                -- end
                end
            end,
            self
        )

        self._onTabChangedProxyArr[tabLevel] = OnTabChangedProxy

        -- anotherTabGroup:Collapsed()
        _forceSettingTabIndex = true
        curTabGroup:SetTabIndex((overrideIndex or topTabGroupRegInfo.defalutIdx) - 1)
        curTabGroup:RefreshTab()
        _forceSettingTabIndex = nil

        if bInvoke and  topTabGroupRegInfo.bTriggerCallback then
            _initTab = true
            OnTabChangedProxy(overrideIndex or topTabGroupRegInfo.defalutIdx, 0, false, tabLevel)
            _initTab = nil
        else
            topTabGroupRegInfo.operaAction = EStackAction.Push
        end

        -- Timer.DelayCall(
        --     0.02,
        --     function()
        --         Module.Reddot:ForceUpdateReddot(UIName2ID.TopBarHD)
        --     end,
        --     self
        -- )
        if tabLevel == 2 then
            local tabNavigatorSecondary = self:_GetCurTabNavigator(2)
            if CommonBarHDTabGroupLogic.IsShowingPrimaryStackUI() then
                tabNavigatorSecondary:SetCppValue("PrevAction", "Prev02")
                tabNavigatorSecondary:SetCppValue("NextAction", "Next02")
            else
                tabNavigatorSecondary:SetCppValue("PrevAction", "Prev01")
                tabNavigatorSecondary:SetCppValue("NextAction", "Next01")
            end
            tabNavigatorSecondary:InitKeyIcons()
        elseif tabLevel == 3 then
            local tabNavigatorTertiary = self:_GetCurTabNavigator(3)
            tabNavigatorTertiary:SetCppValue("PrevAction", "Prev02")
            tabNavigatorTertiary:SetCppValue("NextAction", "Next02")
            tabNavigatorTertiary:InitKeyIcons()
        end
    else
        if tabLevel == 1 then
            -- 切换一级界面时，不需要隐藏一级tab
            if not CommonBarHDTabGroupLogic.IsChangingPrimaryTab() then
                self._curTabGroupMode = self._curTabGroupMode & ~ETabGroupMode.Primary
            end
        elseif tabLevel == 2 then
            self._curTabGroupMode = self._curTabGroupMode & ~ETabGroupMode.Secondary
            self:SetTopTabGroup({}, 3)
			local stackDefaultThemeID = topTabGroupRegInfo.stackDefaultThemeID
			if stackDefaultThemeID ~= NONE_THEME_ID then
				Facade.UIManager:SetAutoApplyThemeID(stackDefaultThemeID, EApplyThemeIDChangeReason.StackDefault)
			else
            Facade.UIManager:ResetAutoApplyThemeID()
			end
        else
            self._curTabGroupMode = self._curTabGroupMode & ~ETabGroupMode.Tertiary
        end

        self._onTabChangedProxyArr[tabLevel] = nil
        self._tabGroupRegInfoCacheArr[tabLevel] = nil
        Module.CommonBar.Field:SetTabGroupRegInfoCache(tabLevel, nil)
    end
    self:UpdateTabVisiblity()
end

function TopBarHD:AddTertiaryTabGroup(tabGroupInfo)
    if self._curTabGroupMode & ETabGroupMode.Primary == 0 then
        if self._curTabGroupMode & ETabGroupMode.Tertiary > 0 then
            self:RemoveTertiaryTabGroup()
        end
        self:SetTopTabGroup(tabGroupInfo, 3, nil, true)
    else
        logwarning("you cannot add Tertiary Tab when primary Tab is Existing")
    end
end

function TopBarHD:RemoveTertiaryTabGroup()
    self:SetTopTabGroup({}, 3, nil, nil)
end

function TopBarHD:IsTabGroupVisibile(tabLevel)
    local tabNavigator = self:_GetCurTabNavigator(tabLevel)

    return tabNavigator:IsVisible()
end

function TopBarHD:_OnGetTabItemCount(tabLevel)
    if self._tabGroupRegInfoCacheArr[tabLevel] and self._tabGroupRegInfoCacheArr[tabLevel].tabIdxListGen then
        return #self._tabGroupRegInfoCacheArr[tabLevel].tabIdxListGen
    end
    return 0
end

function TopBarHD:_OnProcessTabItem(position, itemWidget, tabLevel)
    local tabGroupRegInfo = self._tabGroupRegInfoCacheArr[tabLevel]
    local index = position + 1
    local tabName = tabGroupRegInfo.tabTxtList[index]
    -- local checkButton = itemWidget:BP_GetCheckButton()
    -- local checkButton = itemWidget:ClassWnd(UDFCommonCheckButton, )
    local checkButton = itemWidget.wtCommonCheckBtn
    if tabName and tabName ~= Module.CommonUILibrary.Config.Loc.NoParam then
        checkButton:SetMainTitleText4AllState(tabName)
    end

    if tabGroupRegInfo.imgPathList and tabGroupRegInfo.imgPathList[index] then
        local imgPath = tabGroupRegInfo.imgPathList[index]
        if checkButton.BP_SetIsShowIcon then
            checkButton:BP_SetIsShowIcon(true)
        end
        checkButton:SetIsShowIcon(true)
        itemWidget:AsyncSetImageIconPathAllState(imgPath, true)
        if checkButton.BP_SetIconSize then
            if tabGroupRegInfo.imgSizeList and tabGroupRegInfo.imgSizeList[index] then
                checkButton:BP_SetIconSize(tabGroupRegInfo.imgSizeList[index])
            else
                checkButton:BP_SetIconSize(FVector2D(0, 0))
            end
        end
    else
        --@todo 置空图片，否则有引用残留
        if checkButton.BP_SetIsShowIcon then
            checkButton:BP_SetIsShowIcon(false)
        end
        checkButton:SetIsShowIcon(false)
    end

    local proxyTable
    local placeOperation
    local placeOperation1 = function(reddot)
        local anchors = FAnchors()
        anchors.Minimum = FVector2D(1, 0)
        anchors.Maximum = FVector2D(1, 0)
        local canvasSlot = reddot.Slot
        canvasSlot:SetAnchors(anchors)
        canvasSlot:SetPosition(FVector2D(0, -8))
        canvasSlot:SetAlignment(FVector2D(1, 0))
        -- reddot:SetRenderScale(FVector2D(0.8,0.8))
    end
    -- local placeOperation2 = function(reddot)
    --     local anchors = FAnchors()
    --     anchors.Minimum = FVector2D(1, 0)
    --     anchors.Maximum = FVector2D(1, 0)
    --     local canvasSlot = reddot.Slot
    --     canvasSlot:SetAnchors(anchors)
    --     canvasSlot:SetPosition(FVector2D(0, 0))
    --     canvasSlot:SetAlignment(FVector2D(1, 0))
    --     -- reddot:SetRenderScale(FVector2D(0.8,0.8))
    -- end
    -- if tabLevel == 1 then
    --     placeOperation = placeOperation1
    -- elseif tabLevel == 2 then
    --     if self._curTabGroupMode & ETabGroupMode.Primary == 0 then
    --         -- 只有二级Tab
    --         placeOperation = placeOperation1
    --     else
    --         -- 双排
    --         placeOperation = placeOperation2
    --     end
    -- else
    --     placeOperation = placeOperation2
    -- end
    proxyTable = self._reddotProxiesArr[tabLevel]

    placeOperation = placeOperation1

    if tabGroupRegInfo.bNewReddotTrie then
        local bCreateNew = false
        local reddotRegInfo = (tabGroupRegInfo.reddotTrieRegItemList or {})[index]
        local reddotProxy = (proxyTable[index] or {})[2] ---@type ReddotDot

        bCreateNew = (reddotRegInfo ~= nil) and (reddotProxy == nil)

        --确保红点对象存在，且依附在正确的控件上，否则重新创建红点
        if reddotProxy then
            bCreateNew = bCreateNew or (proxyTable[index].itemWidget ~= itemWidget)
            bCreateNew = bCreateNew or (reddotProxy:GetTargetWidget() ~= proxyTable[index].realTarget)
        end

        if bCreateNew then
            --销毁旧红点
            if reddotProxy then reddotProxy:DestroyReddot() end
            proxyTable[index] = nil

            ---@type ReddotTrieTabRegItem
            local reddotTrieRegItem = tabGroupRegInfo.reddotTrieRegItemList[index]
            if reddotTrieRegItem and next(reddotTrieRegItem) then
                local newProxy =
                    Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
                    itemWidget:BP_GetReddotPanel(),
                    reddotTrieRegItem.reddotDataConfigWithStyleList
                )
                if newProxy then
                    newProxy:SetDotPlaceMode(
                        EReddotPlaceMode.Custom,
                        placeOperation
                    )
                    proxyTable[index] = {
                        reddotTrieRegItem.uiNavId,
                        newProxy,
                        bNewReddotTrie = tabGroupRegInfo.bNewReddotTrie,
                        itemWidget = itemWidget,
                        realTarget = newProxy:GetTargetWidget(),
                    }
                end
            end
        end
    else
        if tabGroupRegInfo.reddotRegItemList and next(tabGroupRegInfo.reddotRegItemList) and not proxyTable[index] then
            ---@type ReddotTabRegItem
            local reddotRegItem = tabGroupRegInfo.reddotRegItemList[index]
            if next(reddotRegItem) then
                local newProxy =
                    CommonBarReddotLogic.RegReddotOld(
                    itemWidget:BP_GetReddotPanel(),
                    "",
                    reddotRegItem,
                    function(reddot)
                        local anchors = FAnchors()
                        anchors.Minimum = FVector2D(1, 0)
                        anchors.Maximum = FVector2D(1, 0)
                        local canvasSlot = reddot.Slot
                        canvasSlot:SetAnchors(anchors)
                        canvasSlot:SetPosition(FVector2D(26, -22))
                        canvasSlot:SetAlignment(FVector2D(1, 0))
                    end
                )
                proxyTable[index] = {
                    reddotRegItem.uiNavId,
                    newProxy,
                    bNewReddotTrie = tabGroupRegInfo.bNewReddotTrie
                }
            end
        end
    end

    --- 锁定状态刷新，新增隐藏
    local lockDataCache = self._lockDataCacheArr[tabLevel]
    if not lockDataCache[index] then
        if tabGroupRegInfo.lockDataList and tabGroupRegInfo.lockDataList[index] then
            local lockData = tabGroupRegInfo.lockDataList[index]
            if next(lockData) then
                local newLockData =
                    UILockData:NewIns(
                    itemWidget,
                    lockData.firstLockID,
                    lockData.secondLockID,
                    lockData.uiNavID,
                    lockData.fFailedCallback,
                    lockData.failedCallbackCaller,
                    lockData.fCustomLockChecker,
                    lockData.customLockCheckerCaller
                )
                lockDataCache[index] = newLockData
            end
        end
    end
    local lockDataCache = self._lockDataCacheArr and self._lockDataCacheArr[tabLevel] or nil
	CommonBarUnlockLogic.TryUpdateWithLockData(lockDataCache, index, itemWidget)

    local key = string.format(Module.Guide.Config.EGuideProxyWidget.guideProxyTopBarHDFormat, tabLevel or 0, position or 0)
    Module.Guide:AddGuideWidgetProxy(key, itemWidget)
end

function TopBarHD:_GetCurTabGroup(tabLevel)
    if tabLevel == 1 then
        return self._wtTabGroupPrimary
    elseif tabLevel == 2 then
        if self._curTabGroupMode & ETabGroupMode.Primary == 0 then
            return self._wtTabGroupSecondary
        else
            return self._wtTabGroupSecondary2
        end
    else
        return self._wtTabGroupTertiary
    end
end

function TopBarHD:_GetCurTabNavigator(tabLevel)
    if tabLevel == 1 then
        return self._wtTabNavigatorPrimary
    elseif tabLevel == 2 then
        if self._curTabGroupMode & ETabGroupMode.Primary == 0 then
            return self._wtTabNavigatorSecondary
        else
            return self._wtTabNavigatorSecondary2
        end
    else
        return self._wtTabNavigatorTertiary
    end
end

function TopBarHD:_OnTabIndexChanged(tabIndex, lastTabIndex, tabLevel)
    logframe('TopBarHD:_OnTabIndexChanged(tabIndex, lastTabIndex, tabLevel)', tabIndex, lastTabIndex, tabLevel)
    --- 刷新主题
	local dataIdx = tabIndex + 1
    if tabLevel == 1 or tabLevel == 2 then
        self:TryUpdateThemeID(dataIdx, tabLevel)
    end

    local proxy = self._onTabChangedProxyArr[tabLevel]
    Config.evtOnTopBarTabChanged:Invoke(tabIndex, lastTabIndex, tabLevel)
    if proxy then
        proxy(tabIndex + 1, lastTabIndex + 1, true, tabLevel)
    end
    ---fix:在process完强制更新红点
    -- Module.Reddot:ForceUpdateReddot(UIName2ID.TopBar)
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.TobarHDTabIndexChanged, tabLevel, tabIndex, lastTabIndex)
end

function TopBarHD:CheckTopTabGroup(index, bForceCallback, tabLevel)
    local tabGroup = self:_GetCurTabGroup(tabLevel)
    tabGroup:SetTabIndex(index - 1, bForceCallback)
end

--模拟快捷键切换导航页签，带动效，默认执行回调
function TopBarHD:NavigationSetTopTabGroup(index, tabLevel)
    local tabGroup = self:_GetCurTabGroup(tabLevel)
    if tabGroup and tabGroup.NavigationClickByIndex then
        tabGroup:NavigationClickByIndex(index - 1)
    end
end

function TopBarHD:GetCurrentTopTabIndex(tabLevel)
    local tabGroup = self:_GetCurTabGroup(tabLevel)
    if tabGroup and not hasdestroy(tabGroup) then
        return tabGroup:GetCurSelectedIndex() + 1
    else
        logwarning("TopBarHD:GetCurrentTopTabIndex tabGroup is nil")
        return 0
    end
end

--------------------------------------------------------------------------
--- Style
--------------------------------------------------------------------------
    -- 键鼠模式下正常按照style显示，手柄模式下仅大厅首页显示
function TopBarHD:_TrySetAvatarMainVisibility(bVisible)
    if bVisible then
        if WidgetUtil.IsGamepad() and (self.currentViewUINavID and self.currentViewUINavID ~= UIName2ID.IrisWorldEntryMainPanel and self.currentViewUINavID ~= UIName2ID.BattlefieldEntryMainPanel) then
            self._wtAvatarMain:Hide()
        else
            self._wtAvatarMain:Show()
            if WidgetUtil.IsGamepad() then
                self._wtAvatarMain:GamepadEnter()
            else
                self._wtAvatarMain:GamepadExit()
            end
        end
    else
        self._wtAvatarMain:Hide()
    end
end

function TopBarHD:SetTopSideStyle(style, currentView)
    local ETopBarStyleFlag = Config.ETopBarStyleFlag

    -- 非安全屋/MP大厅无法显示
    if
        Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse and
            Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Lobby
     then
        style =
            style &
            ~(ETopBarStyleFlag.Team | ETopBarStyleFlag.Friends | ETopBarStyleFlag.Mail | ETopBarStyleFlag.LiveRadio)
    end

    if style & ETopBarStyleFlag.Currency == 0 then
        self:SetCurrencyVisible(false)
    else
        self:SetCurrencyVisible(true)
    end

    if currentView then
        self.currentViewUINavID = currentView.UINavID
    end

    if self._wtAvatarMain then
        if style & ETopBarStyleFlag.Team == 0 then 
            self:_TrySetAvatarMainVisibility(false)
        else
            self:_TrySetAvatarMainVisibility(true)
        end
    end

    if style & ETopBarStyleFlag.Mail == 0 then
        self._wtWBP_ButtonMail:Collapsed()
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_BindMailAction(false)
        -- END MODIFICATION
	-- reddot
        self:_ToggleMailReddot(false)
    else
        -- reddot
        self._wtWBP_ButtonMail:SelfHitTestInvisible()
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_BindMailAction(true)
        -- END MODIFICATION
        self:_ToggleMailReddot(true)
    end

    if style & ETopBarStyleFlag.Settings == 0 then
        self._wtWBP_ButtonSet:Collapsed()
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_BindSettingsAction(false)
        -- END MODIFICATION
    else
        self._wtWBP_ButtonSet:SelfHitTestInvisible()
        -- BEGIN MODIFICATION @ VIRTUOS :
        self:_BindSettingsAction(true)
        -- END MODIFICATION
    end

    -- 如果显示标题，则TabGroup居中显示。否则TabGroup靠左显示
    if style & ETopBarStyleFlag.Title == 0 then
        -- self._wtTitle:Collapsed()
        self._wtCPTile:Collapsed()
        self:BP_ToggleBarLine(true)
    else
        -- self._wtTitle:SelfHitTestInvisible()
        self._wtCPTile:SelfHitTestInvisible()
        self:BP_ToggleBarLine(false)
    end


    --if rawget(Module, "LiveRadio") then
    --    --- 初始化LiveRadio后才进来
    --    if style & ETopBarStyleFlag.LiveRadio == 0 then
    --        Module.LiveRadio:HideLiveRadioEntrance(Module.LiveRadio.Config.ETipsType.PC_SOLCombatSystem, self)
    --    else
    --        Module.LiveRadio:ShowLiveRadioEntrance(Module.LiveRadio.Config.ETipsType.PC_SOLCombatSystem, self)
    --    end
    --end

    local animMask = ETopBarStyleFlag.Friends | ETopBarStyleFlag.Team
    local playAnim = false
    if self._curStyle then
        if (self._curStyle & animMask) ~= (style & animMask) then
            playAnim = true
        end
    else
        playAnim = true
    end

    if playAnim then
        self:PlayWidgetAnim(self.WBP_TopBarHDSlotPanelV1_in_01)
    end

    self._curStyle = style

    --- Post Style Change Update
    self:UpdateBottomBg()
    local bShowNetSignal = CommonBarRegLogic.GetStackUITopBarNetSignal(currentView)
    self:SetCurrencyLeftIcons(style, bShowNetSignal)
end

function TopBarHD:NeedBottomBg()
    return self._curStyle & Config.ETopBarStyleFlag.Team > 0
end

function TopBarHD:UpdateBottomBg()
    local bShow = self:NeedBottomBg()
    if not bShow then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if not hasdestroy(bottomBar) then
            bShow = bShow or bottomBar:NeedBottomBg()
        end
    end

    if bShow then
        self._wtAvatarBackground:SelfHitTestInvisible()
    else
        self._wtAvatarBackground:Collapsed()
    end
end

function TopBarHD:SetDefaultTopBackBarStyle(bIsGreenStyle)
    -- 手游逻辑，PC先留着接口
end

function TopBarHD:SetTopBarVisible(bVisible)
    if bVisible then
        self:Show(false)
    else
        self:Hide(true, true)
    end
end

function TopBarHD:SetTopBarAndSecPanVisible(bVisible)
    if bVisible then
        self:Show(false)
        self._wtTabNavigatorSecondary:SelfHitTestInvisible()
        self._wtTabNavigatorTertiary:SelfHitTestInvisible()
    else
        self:Hide(true, true)
    end
end

function TopBarHD:SetTopRenderOpacity(renderOpacity)
    self:SetRenderOpacity(renderOpacity)
end

function TopBarHD:SetTopGroupTabVisible(bVisible, tabLevel)
    local tabNavigator = self:_GetCurTabNavigator(tabLevel)
    if bVisible then
        tabNavigator:BindActions()
        tabNavigator:SelfHitTestInvisible()
        if tabLevel == 1 then
            self._curTabGroupMode = self._curTabGroupMode | ETabGroupMode.Primary
        elseif tabLevel == 2 then
            self._curTabGroupMode = self._curTabGroupMode | ETabGroupMode.Secondary
        else
            self._curTabGroupMode = self._curTabGroupMode | ETabGroupMode.Tertiary
        end
        local tab = self:_GetCurTabGroup(tabLevel)
        -- 如果不刷新，可能会残留旧的动画状态
        tab:RefreshTab()
    else
        tabNavigator:UnbindActions()
        tabNavigator:Collapsed()
        if tabLevel == 1 then
            self._curTabGroupMode = self._curTabGroupMode & ~ETabGroupMode.Primary
        elseif tabLevel == 2 then
            self._curTabGroupMode = self._curTabGroupMode & ~ETabGroupMode.Secondary
        else
            self._curTabGroupMode = self._curTabGroupMode & ~ETabGroupMode.Tertiary
        end
    end
end

function TopBarHD:GetHDTopTabGroupMode()
    return self._curTabGroupMode
end

function TopBarHD:OnShowBegin()
    romzhang_("TopBarHD:OnShowBegin")
end

function TopBarHD:OnHideBegin()
end

function TopBarHD:UpdateTabVisiblity()
    if self._curTabGroupMode & ETabGroupMode.Primary > 0 then
        self._wtTabNavigatorPrimary:SelfHitTestInvisible()
        self._wtTabNavigatorPrimary:BindActions()
    else
        self._wtTabNavigatorPrimary:Collapsed()
        self._wtTabNavigatorPrimary:UnbindActions()
    end

    if self._curTabGroupMode & ETabGroupMode.Secondary > 0 then
        if self._curTabGroupMode & ETabGroupMode.Primary == 0 then
            self._wtTabNavigatorSecondary:SelfHitTestInvisible()
            self._wtTabNavigatorSecondary:BindActions()

            self._wtTabNavigatorSecondary2:Collapsed()
            self._wtTabNavigatorSecondary2:UnbindActions()
        else
            self._wtTabNavigatorSecondary2:SelfHitTestInvisible()
            self._wtTabNavigatorSecondary2:BindActions()

            self._wtTabNavigatorSecondary:Collapsed()
            self._wtTabNavigatorSecondary:UnbindActions()
        end
    else
        self._wtTabNavigatorSecondary:Collapsed()
        self._wtTabNavigatorSecondary:UnbindActions()
        self._wtTabNavigatorSecondary2:Collapsed()
        self._wtTabNavigatorSecondary2:UnbindActions()
    end

    if self._curTabGroupMode & ETabGroupMode.Tertiary > 0 then
        self._wtTabNavigatorTertiary:SelfHitTestInvisible()
        self._wtTabNavigatorTertiary:BindActions()
    else
        self._wtTabNavigatorTertiary:Collapsed()
        self._wtTabNavigatorTertiary:UnbindActions()
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    for i = 1, #self._tabsPanelMap do
        local panel = self._tabsPanelMap[i]
        if panel.navigator:IsVisible() and panel.group._wtTabContainer:GetChildrenCount() > 1 then
            panel.navigator._wtPrevKeyIcon:Visible()
            panel.navigator._wtNextKeyIcon:Visible()
        else
            panel.navigator._wtPrevKeyIcon:Collapsed()
            panel.navigator._wtNextKeyIcon:Collapsed()
        end
    end
    --- END MODIFICATION

    self:BP_SetTabStyle(self._curTabGroupMode)

    --- BEGIN MODIFICATION @ VIRTUOS（Temp）
    -- 由于Mask Widget会遮挡平台Icon，临时关闭TopBar Mask
    if IsPS5() and self._wtDFImage_Mask then
        self._wtDFImage_Mask:Collapsed()
    end
    --- END MODIFICATION
end
--------------------------------------------------------------------------
--- Stack UI
--------------------------------------------------------------------------

function TopBarHD:OnStackViewBeforeChange(operaViewId, proStackOperaAction)
    romzhang_("TopBarHD:OnStackViewBeforeChange start")
    if self._wtSideBar then
        self._wtSideBar:SetSideBar({})
        self._wtSideBar:Hide()
    end
end

local function _HandleStackTab(self, uiInst, operaAction)
    local overrideIndex = nil
    -- local stackCount = Facade.UIManager:GetStackUICount()
    -- if operaAction == EStackAction.Push then
    --     -- get last ui
    --     if stackCount > 1 then
    --         if _IsValidTabInfo(self._tabGroupRegInfoCacheArr[2]) then
    --             self._cachedTabIndex[stackCount - 1] = self:GetCurrentTopTabIndex(2)
    --         end
    --     end
    -- elseif operaAction == EStackAction.Pop then
    --     if stackCount > 0 then
    --         local cachedIndex = self._cachedTabIndex[stackCount]
    --         if cachedIndex then
    --             self._cachedTabIndex[stackCount] = nil
    --             overrideIndex = cachedIndex
    --         end
    --     end
    -- end
    if not CommonBarHDTabGroupLogic.IsPrimaryTabUI(uiInst) then
        self:SetTopGroupTabVisible(false, 1)
    end

    -- 如果一级导航有子tab，则跳过二级Tab
    if not CommonBarHDTabGroupLogic.HasPrimarySubTab(uiInst) then
        -- self._wtBackground4Title:SetVisibility(ESlateVisibility.HitTestInvisible)
        local tabGroupRegInfo = CommonBarRegLogic.GetStackUITopBarTabGroupRegInfoProcess(uiInst)
        tabGroupRegInfo.operaAction = operaAction
        self:SetTopTabGroup(tabGroupRegInfo, 2, overrideIndex, true)
    else
        -- self._wtBackground4Title:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function TopBarHD:RecoverDefaultTabIndex(tabLevel)
    local regInfo = self._tabGroupRegInfoCacheArr[tabLevel]
    if tabLevel == 3 then
        local parentTabGroupRegInfo = self._tabGroupRegInfoCacheArr[2]
		if parentTabGroupRegInfo and parentTabGroupRegInfo.tertiaryTabs then
			regInfo = parentTabGroupRegInfo.tertiaryTabs[parentTabGroupRegInfo.defalutIdx]
		end
    end
    if regInfo then
        regInfo.defalutIdx = regInfo.initIdx
    end
end

function TopBarHD:OnStackViewChange(operaViewId, operaAction)
    romzhang_("TopBarHD:OnStackViewChange finish", operaViewId, operaAction)

    local curUIInst = Facade.UIManager:GetCurrentStackUI()
    curUIInst = getfromweak(curUIInst)

    if operaAction == EStackAction.Pop then
		--- MS24 默认记住上次选中的页签，关闭时回到initIdx
        self:RecoverDefaultTabIndex(2)
        self:RecoverDefaultTabIndex(3)
	end
    -- 如果是普通的栈界面关闭，则同时关闭Tab
    if operaAction == EStackAction.Pop and not CommonBarHDTabGroupLogic.IsPrimaryTabUIID(operaViewId) then
        self:SetTopTabGroup({}, 2)
    end
    CommonBarHDTabGroupLogic.HookPrimaryTabChange(curUIInst)
    -- 只与内容区相关的功能
    if not hasdestroy(curUIInst) then
        -- if not CommonBarHDTabGroupLogic.IsPrimaryTabUI(curUIInst) then
        _HandleStackTab(self, curUIInst, operaAction)
        -- end

        local bHide = CommonBarRegLogic.GetStackUIHideBarProcess(curUIInst)
        if bHide then
            self:Hide(true, true)
        else
            self:Show(false)
            local titleText = CommonBarRegLogic.GetStackUITopBarTitleProcess(curUIInst)
            if titleText ~= "" then
                self:ChangeTitle(titleText)
            end

            local style = CommonBarRegLogic.GetStackUITopBarStyleProcess(curUIInst)
            if CommonBarHDTabGroupLogic.IsPrimaryTabUI(curUIInst) then
                style = Module.CommonBar.Config.TopBarStyle.DefaultPrimary | style
                -- 隐藏标题
                style = style & (~Module.CommonBar.Config.ETopBarStyleFlag.Title)
            end

            self:SetTopSideStyle(style, curUIInst)

            local currencyIDForShowList = CommonBarRegLogic.GetStackUITopBarCurrencyTypeListProcess(curUIInst)
            self:SetTopCurrencyTypes(currencyIDForShowList, curUIInst)
        end
    else
        if not CommonBarHDTabGroupLogic.IsChangingPrimaryTab() then
            self:Hide(true, true)
        end
    end
    Timer.DelayCall(
        0.01,
        function()
            Module.CommonBar.Config.evtTopBarFreshFinished:Invoke()
        end,
        self
    )
end

function TopBarHD:OnPopViewChange(curLastPopIdx)
end

function TopBarHD:_ShowModuleNtf(moduleId)
end

function TopBarHD:GetTabGroupWidget(tabLevel)
    if tabLevel == 1 then
        return self._wtHbTabGroup
    else
    end
end

function TopBarHD:GetTopNavigationEntrance()
    if self._wtBtn_Mail:IsVisible() then
        return self._wtBtn_Mail
    elseif self._wtBtn_Settings:IsVisible() then
        return self._wtBtn_Settings
    end
    return nil
end

function TopBarHD:GetBottomNavigationEntrance()
    -- return self._wtBtn_Settings

    return nil
end

function TopBarHD:_OpenRechargeTriangleCurrencyPage()
    if self._childCurrencyWidget1 then
        self._childCurrencyWidget1:OnCharge()
    end
    local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
end

function TopBarHD:_OpenRechargeMandelCurrencyPage()
    if self._childCurrencyWidget2 then
        self._childCurrencyWidget2:OnCharge()
    end
    local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
end

function TopBarHD:OnConfirmBtnInLongPressing(Percent)
    if self._wtCurrencyKeyIcon then
        self._wtCurrencyKeyIcon:BP_UpdateProgressBar(Percent)
    end
end

function TopBarHD:OnConfirmBtnLongPressFinished()
    if self._wtCurrencyKeyIcon then
        self._wtCurrencyKeyIcon:BP_UpdateProgressBar(0)
    end
end

function TopBarHD:OnConfirmBtnInLongPressing2(Percent)
    if self._wtCurrencyKeyIcon2 then
        self._wtCurrencyKeyIcon2:BP_UpdateProgressBar(Percent)
    end
end

function TopBarHD:OnConfirmBtnLongPressFinished2()
    if self._wtCurrencyKeyIcon2 then
        self._wtCurrencyKeyIcon2:BP_UpdateProgressBar(0)
    end
end


-- BEGIN MODIFICATION @ VIRTUOS : 输入设备切换时控制底部社交组件的显隐
function TopBarHD:_OnInputTypeChanged(inputType)
    local ETopBarStyleFlag = Config.ETopBarStyleFlag
    if self._wtAvatarMain then
        if self._curStyle & ETopBarStyleFlag.Team == 0 then 
            self:_TrySetAvatarMainVisibility(false)
        else
            self:_TrySetAvatarMainVisibility(true)
        end
    end

end
-- END MODIFICATION

function TopBarHD:GetOrAddSideBar()
    if not self._wtSideBar then
        local weakSideBar = Facade.UIManager:AddSubUI(self, UIName2ID.SideBarGamepad, self._wtCanvasRoot, nil)
        self._wtSideBar = getfromweak(weakSideBar)
        self._wtSideBar.Slot:SetZOrder(1)
        UIUtil.SetWidgetToParent_Full(self._wtSideBar, self._wtCanvasRoot)
    end

    return self._wtSideBar
end

function TopBarHD:SetSideBar(MainTabInputSummaryV2)
    local sideBar = self:GetOrAddSideBar()
    if sideBar then
        sideBar:SetSideBar(MainTabInputSummaryV2)
    end
end

function TopBarHD:SetIsSideBarVisible(bInVisible)
    local sideBar = self:GetOrAddSideBar()
    if sideBar then
        if bInVisible then
            sideBar:Show()
        else
            sideBar:Hide()
        end
    end
end

--------------------------------------------------------------------------
--- 设置顶部页签主题
--------------------------------------------------------------------------
function TopBarHD:TryUpdateThemeID(dataIdx, tabLevel)
    local tabGroupRegInfo = self._tabGroupRegInfoCacheArr[tabLevel]
    if tabGroupRegInfo and tabGroupRegInfo.themeIDList then
        local themeID = tabGroupRegInfo.themeIDList[dataIdx]
		if themeID then
            Facade.UIManager:SetAutoApplyThemeID(themeID, EApplyThemeIDChangeReason.TabSelection)
		else
			Facade.UIManager:ResetAutoApplyThemeID()
		end
    else
		Facade.UIManager:ResetAutoApplyThemeID()
    end
end

--------------------------------------------------------------------------
--- Setting刷新
--------------------------------------------------------------------------
function TopBarHD:_OnPackStateChanged(packID)
    loginfo("TopBarHD:_OnPackStateChanged"..packID)
    self:_RefreshSettingButtonState()
end

function TopBarHD:_RefreshSettingButtonState()
    loginfo("TopBarHD:_RefreshSettingButtonState")
    if Facade.ModuleManager:IsModuleValid("ExpansionPackCoordinator") then

        if Module.ExpansionPackCoordinator:HaveInstallingQuests() then
            loginfo("TopBarHD:_RefreshSettingButtonState HaveInstallingQuests TRUE")
            self._wtBtn_Settings_Icon:AsyncSetImagePath(Module.CommonBar.Config.SettingButtonState["Download"])
            self._wtWBP_ButtonSet:SetStyle(1)
        else
            loginfo("TopBarHD:_RefreshSettingButtonState HaveInstallingQuests NO")
            self._wtBtn_Settings_Icon:AsyncSetImagePath(Module.CommonBar.Config.SettingButtonState["Normal"])
            self._wtWBP_ButtonSet:SetStyle(0)
        end
    else
        loginfo("TopBarHD:_RefreshSettingButtonState ExpansionPackCoordinator module not loaded")
    end
end

--------------------------------------------------------------------------
--- CurrencyLeft Icon刷新
--------------------------------------------------------------------------
function TopBarHD:ReleaseCurrencyLeftReddotProxies()
    for _, proxy in pairs(self._reddotProxyTable) do
        Module.ReddotTrie:UnRegisterStaticReddotDot(proxy[2])
    end
    self._reddotProxyTable = {}
end

function TopBarHD:SetNetSignVisible(bVisible)
    if bVisible and not IsInEditor() and IsHD() and IsBuildRegionCN() then
        local topBarButton = self:GetTopBarHDBtn("WIFI")
        if topBarButton then
            topBarButton:Show(true, true)
        end
    else
        local topBarButton = self:GetTopBarHDBtn("WIFI")
        if topBarButton then
            topBarButton:Hide(true, true)
        end
    end
end

function TopBarHD:SetCurrencyLeftIcons(curStyle, bShowNetSignal)
    bShowNetSignal = setdefault(bShowNetSignal, false)
    local Config = Module.CommonBar.Config
    local minVisibleBtn
    local curDrawMaxIndx = 0
    local barIdx = 0
    Facade.UIManager:RemoveSubUIByParent(self, self._wtHbCurrencyLeft)
	self:ReleaseCurrencyLeftReddotProxies()

    self._mapConfigIdx2BarIdx = {}
    if Config.PCTopCurrencyLeftInfo and next(Config.PCTopCurrencyLeftInfo) then
        for index, tabInfo in ipairs(Config.PCTopCurrencyLeftInfo) do
            if Module.CommonBar.Field:CheckTabBtnShow(tabInfo, curStyle, bShowNetSignal) then
                barIdx = barIdx + 1
                self._mapConfigIdx2BarIdx[index] = barIdx
                local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.TopBarHDIconBtn, self._wtHbCurrencyLeft, barIdx, tabInfo)
                local topBarButton = getfromweak(weakUIIns)
                if topBarButton then
                    if tabInfo.iconID then
                        topBarButton:SetBtnIcon(tabInfo.iconID)
                    end
                    if tabInfo.btnTitle then
                        topBarButton:SetBtnTitle(tabInfo.btnTitle)
                    end
                    local firstLockID = tabInfo.firstLockID or false
                    local secondLockID = tabInfo.secondLockID or false
                    local lockData = {
                        firstLockID = firstLockID,
                        secondLockID = secondLockID,
                        uiNavID = tabInfo.openUIID,
                        fFailedCallback = tabInfo.openFaildFunc,
                        failedCallbackCaller = nil,

                        fCustomLockChecker = tabInfo.customCheckFunc,
                        customLockCheckerCaller = nil
                    }
                    CommonBarUnlockLogic.InitBarBtnClickedState(topBarButton, tabInfo.openFunc, lockData)
                    topBarButton:SetLastBtnShow(false)
                    curDrawMaxIndx = curDrawMaxIndx + 1
                    local hbSlot = UWidgetLayoutLibrary.SlotAsHorizontalBoxSlot(topBarButton)
                    if hbSlot then
                        local padding = self.BP_CurrencyLeftPadding and self.BP_CurrencyLeftPadding or FMargin(0, 0, 0, 0)
                        local sizeRule = self.BP_CurrencyLeftSizeRule and self.BP_CurrencyLeftSizeRule or ESlateSizeRule.Automatic
                        local horizontalAlign = self.BP_CurrencyLeftHorizontalAlign and self.BP_CurrencyLeftHorizontalAlign or EHorizontalAlignment.HAlign_Left
                        local verticalAlign = self.BP_CurrencyLeftVerticalAlign and self.BP_CurrencyLeftVerticalAlign or EVerticalAlignment.VAlign_Center
                        hbSlot:SetPadding(padding)
                        hbSlot:SetSize(FSlateChildSize(sizeRule))
                        hbSlot:SetHorizontalAlignment(horizontalAlign)
                        hbSlot:SetVerticalAlignment(verticalAlign)
                    end

                    if topBarButton:IsVisible() then
                        if minVisibleBtn == nil then
                            minVisibleBtn = topBarButton
                        end
                    end
                    
                    if topBarButton.bCacheUnlock == true then
                        local placeOperation1 = function(reddot)
                            local anchors = FAnchors()
                            anchors.Minimum = FVector2D(1, 0)
                            anchors.Maximum = FVector2D(1, 0)
                            local canvasSlot = reddot.Slot
                            canvasSlot:SetAnchors(anchors)
                            canvasSlot:SetPosition(FVector2D(0, 0))
                            canvasSlot:SetAlignment(FVector2D(1, 0))
                            -- reddot:SetRenderScale(FVector2D(0.8,0.8))
                        end
                        ---@type ReddotTrieTabRegItem
                        local reddotTrieTabRegItem = tabInfo.reddotTrieTabRegItem
                        if reddotTrieTabRegItem and not self._reddotProxyTable[barIdx] then
                            if next(reddotTrieTabRegItem) then
                                local reddotDataConfigWithStyleList = reddotTrieTabRegItem.reddotDataConfigWithStyleList
                                local newProxy =
                                    Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
                                    topBarButton:BP_GetReddotPanel(),
                                    reddotDataConfigWithStyleList
                                )
                                newProxy:SetDotPlaceMode(
                                    EReddotPlaceMode.Custom,
                                    placeOperation1
                                )
                                self._reddotProxyTable[barIdx] = {
                                    reddotTrieTabRegItem.uiNavId,
                                    newProxy,
                                    bNewReddotTrie = true
                                }
                            end
                        end
                        -- Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget[string.format("guideProxyTopBarMore%d", barIdx)], topBarButton)
                    end
                end
            end
        end
        if minVisibleBtn and minVisibleBtn.SetLastBtnShow then
            minVisibleBtn:SetLastBtnShow(true)
            local hbSlot = UWidgetLayoutLibrary.SlotAsVerticalBoxSlot(minVisibleBtn)
            if hbSlot then
                hbSlot:SetPadding(FMargin(0, 0, 0, 0))
            end
        end
    end
end

function TopBarHD:GetTopBarHDBtn(findEnGame)
    local configIdx
    local Config = Module.CommonBar.Config
    if Config.PCTopCurrencyLeftInfo and next(Config.PCTopCurrencyLeftInfo) then
        for index, tabInfo in ipairs(Config.PCTopCurrencyLeftInfo) do
            if tabInfo.enName == findEnGame then
                configIdx = index
                break
            end
        end
    end
    if configIdx then
        local barIndex = self._mapConfigIdx2BarIdx[configIdx]
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.TopBarHDIconBtn, barIndex)
        local topBarButton = getfromweak(weakUIIns)
        if topBarButton then
            return topBarButton
        else
            logwarning("TopBarHD:GetTopBarHDBtn: No button found for configIdx: " .. tostring(configIdx))
            return nil
        end
    end
end

function TopBarHD:_OnUpdateNetworkStatus(realPing, level)
    local imagePath = Module.CommonBar.Config.NetworkStatusImgPathList[level]
    if imagePath then
        local topBarButton = self:GetTopBarHDBtn("WIFI")
        if topBarButton then
            topBarButton:SetBtnIconByPath(imagePath)
        end
    end
end

return TopBarHD
