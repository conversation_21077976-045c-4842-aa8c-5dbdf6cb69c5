----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class SystemSettingHDEntrance
local SystemSettingHDEntrance = ui("SystemSettingHDEntrance")
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"
local UBasePlayerSettlementComponent = import "BasePlayerSettlementComponent"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local EGPInputModeType = import "EGPInputModeType"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UDFMGameGPM = import "DFMGameGPM"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UGameplayStatics = import "GameplayStatics"
local UGPBattleFieldSystem = import "GPBattleFieldSystem"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local EUINavigation = import "EUINavigation"
local UGPGameHudDelegates = import "GPGameHudDelegates"
local EGameHUDState = import "GameHUDSate"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local ERedeployStateFuture = import "ERedeployStateFuture"

-------------------------------------------------
--调试用 开始 leonhxzhao
-------------------------------------------------
local DebugLogic = {
    reportSelf = false,
}
--这个文件不会提交，所以永远不会将debug版交上去
local DebugPath = "DFM.Business.Module.SystemSettingModule.Debug.SystemSettingDebug_DO_NOT_SUBMIT"
if isexist(DebugPath) then
    DebugLogic = require(DebugPath)
end
-------------------------------------------------
--调试用 结束
-------------------------------------------------

local EscEntrancePanelBtnType = {
	Return = 1,
	Settings = 2,
	LeaveBattle = 3,
	QuitGame = 4,
	BackToModeHall = 5,
    OpenGMPanel = 6,
    StopPIE = 7,
    SafeHouseBreakOut = 8,
    BattleFieldKillSelfRedeploy = 9,
    ReportVoice = 10,
    LogUpload = 11,
    LeaveRange = 12,
    BreakOut = 13,
    TeamInfo = 14,
    Rechallenge = 15,
    VehicleBreakOut = 16,
}

---InGameController
---@return InGameController
local function GetInGameController()
    return InGameController:Get()
end

function SystemSettingHDEntrance:Ctor()
    self:_BindWidget()
    self:_BindBtnEvent()
    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    loginfo("SystemSettingHDEntrance currentGameFlow=" .. currentGameFlow)

    if currentGameFlow == EGameFlowStageType.Game then
        -- 局内不显示 退出游戏，返回模式大厅
        self._wtBtnQuitGame:SetVisibility(ESlateVisibility.Collapsed)
        self._wtBtnBreakOut:SetVisibility(ESlateVisibility.Visible)
        -- 新手关可以返回模式大厅
        if Module.Guide:IsInNewPlayerMatch() then
            self._wtBtnBackToModeHall:SetVisibility(ESlateVisibility.Visible)
            self._wtBtnLeaveBattle:SetVisibility(ESlateVisibility.Collapsed)
        else
            self._wtBtnBackToModeHall:SetVisibility(ESlateVisibility.Collapsed)
            self._wtBtnLeaveBattle:SetVisibility(ESlateVisibility.Visible)
        end
    else
        -- 局外不显示 放弃战斗
        self._wtBtnBreakOut:SetVisibility(ESlateVisibility.Collapsed)
        self._wtBtnLeaveBattle:SetVisibility(ESlateVisibility.Collapsed)
        self._wtBtnTeamInfo:SetVisibility(ESlateVisibility.Collapsed)
        self._wtBtnBackToModeHall:SetVisibility(ESlateVisibility.Collapsed)
        self._wtBtnQuitGame:SetVisibility(ESlateVisibility.Visible)
    end
    -- shipping包不显示 打开GM面板
    if VersionUtil.IsShipping() then
        self._wtBtnOpenGMPanel:SetVisibility(ESlateVisibility.Collapsed)
        -- BEGIN MODIFICATION @ VIRTUOS
        self._wtBtnLogUpload:SetVisibility(ESlateVisibility.Collapsed)
        -- END MODIFICATION
    end
    -- 仅编辑器显示 编辑器StopPlay
    if not IsInEditor() then
        self._wtBtnStopPIE:SetVisibility(ESlateVisibility.Collapsed)
    end
    -- 仅玩家在靶场的时候 显示离开靶场
    if Server.IrisSafeHouseServer.bIsInRange then
        self._wtBtnLeaveRange:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtBtnLeaveRange:SetVisibility(ESlateVisibility.Collapsed)
    end

    if currentGameFlow ~= EGameFlowStageType.SafeHouse
    or Server.IrisSafeHouseServer.bIsInRange or ItemOperaTool.bIsInCollectionRoom then
        self._wtSafeHouseBreakOut:SetVisibility(ESlateVisibility.Collapsed)
    end

    if currentGameFlow == EGameFlowStageType.ModeHall then
        self._wtBtnBackToModeHall:SetVisibility(ESlateVisibility.Collapsed)
        self._wtBtnSettings:SetVisibility(ESlateVisibility.Collapsed)
    end

    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if isvalid(gameState) then
        local GM = gameState.DFMGamePlayerMode
        if (GM ~= EDFMGamePlayMode.GamePlayMode_Breakthrough and GM ~= EDFMGamePlayMode.GamePlayMode_Conquest) then
            self._wtBtnRedeploy:SetVisibility(ESlateVisibility.Collapsed)
        elseif Server.IrisSafeHouseServer.bIsInRange then
            self._wtBtnRedeploy:SetVisibility(ESlateVisibility.Collapsed)
        end
    else
        self._wtBtnRedeploy:SetVisibility(ESlateVisibility.Collapsed)
    end

    --刷新重新部署按钮可用状态
    self:UpdateRedeployButtonVisible()

    local members = self:_GetTeamOtherMembers(currentGameFlow)
    loginfo("SystemSettingHDEntrance members number=" .. #members)
    if #members > 0 then
        Module.SystemSetting.Field:SetCachedTeamMembers(members)
    else
        self._wtBtnVoiceReport:SetVisibility(ESlateVisibility.Collapsed)
    end

    -- 新手关重新挑战
    if Module.Guide:IsInNewPlayerMatch() then
        self._wtBtnRechallenge:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtBtnRechallenge:SetVisibility(ESlateVisibility.Collapsed)
    end

    --最后一个样式设置
    local allButton = self:Wnd("BtnContianer", UIWidgetBase):GetAllChildren()
    local set = false
    for i, _ in ipairs(allButton) do
        local btn = allButton[#allButton - i + 1]
        local DFMButton_22_Visible = (btn.DFMButton_22:GetVisibility() ~= ESlateVisibility.Collapsed and btn.DFMButton_22:GetVisibility() ~= ESlateVisibility.Hidden)
        if not set and DFMButton_22_Visible then
            btn:BP_SetLine(true)
            set = true
        else
            btn:BP_SetLine(false)
        end
    end

    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)

    -- BEGIN MODIFICATION @ VIRTUOS
    self._wtBtnContianer = self:Wnd("BtnContianer", UIWidgetBase)

    if IsConsole() then
        self._wtBtnQuitGame:SetVisibility(ESlateVisibility.Collapsed)
    end
    -- END MODIFICATION
end

function SystemSettingHDEntrance:OnOpen()
    self._bOnceLeaveBattle = false

    UDFMGameplayDelegates.Get(GetWorld()).BreakthroughOnClientRedeployStateChanged:Add(self.OnClientRedeployStateChanged,self)
end

function SystemSettingHDEntrance:_BindWidget()
    self._wtBtnReturn = self:Wnd("Btn_Return", UIWidgetBase):Wnd("Button_94", UIWidgetBase)
    self._wtBtnSettings = self:Wnd("Btn_Settings", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnLeaveBattle = self:Wnd("Btn_LeaveBattle", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnLeaveRange = self:Wnd("Btn_SafeHouseLeaveRange", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnBackToModeHall = self:Wnd("Btn_BackToModeHall", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnQuitGame = self:Wnd("Btn_QuitGame", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnOpenGMPanel = self:Wnd("Btn_OpenGMPanel", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnStopPIE = self:Wnd("Btn_StopPIE", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtSafeHouseBreakOut = self:Wnd("Btn_SafeHouseBreakOut", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnRedeploy = self:Wnd("Btn_Redeploy", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnVoiceReport = self:Wnd("Btn_VoiceReporting", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnLogUpload = self:Wnd("Btn_LogUpload", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnBreakOut = self:Wnd("Btn_BreakOut", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnVehicleBreakOut = self:Wnd("Btn_VehicleBreakOut", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnTeamInfo = self:Wnd("Btn_TeamInfo", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
    self._wtBtnRechallenge = self:Wnd("Btn_Rechallenge", UIWidgetBase):Wnd("DFMButton_22", UIWidgetBase)
end

function SystemSettingHDEntrance:_BindBtnEvent()
    self._wtBtnReturn:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.Return)
    self._wtBtnSettings:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.Settings)
    self._wtBtnLeaveBattle:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.LeaveBattle)
    self._wtBtnLeaveRange:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.LeaveRange)
    self._wtBtnBackToModeHall:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.BackToModeHall)
    self._wtBtnQuitGame:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.QuitGame)
    self._wtBtnOpenGMPanel:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.OpenGMPanel)
    self._wtBtnStopPIE:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.StopPIE)
    self._wtSafeHouseBreakOut:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.SafeHouseBreakOut)
    self._wtBtnRedeploy:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.BattleFieldKillSelfRedeploy)
    self._wtBtnVoiceReport:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.ReportVoice)
    self._wtBtnLogUpload:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.LogUpload)
    self._wtBtnBreakOut:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.BreakOut)
    self._wtBtnVehicleBreakOut:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.VehicleBreakOut)
    self._wtBtnTeamInfo:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.TeamInfo)
    self._wtBtnRechallenge:Event("OnClicked", self._OnClickBtn, self, EscEntrancePanelBtnType.Rechallenge)
end

function SystemSettingHDEntrance:OnShowBegin()
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntraceOnShowBegin:Invoke()
    -- BEGIN MODIFICATION @ VIRTUOS
    self:_SetGamepadNavigation(true)
    local _wtReturnBtn = self:Wnd("Btn_Return", UIWidgetBase)
    if _wtReturnBtn then
        self._wtGamepadKeyIcon = _wtReturnBtn:Wnd("WBP_CommonKeyIcon1",HDKeyIconBox)
        if self._wtGamepadKeyIcon then
            self._wtGamepadKeyIcon:InitByDisplayInputActionName("Back", true, 0.0, false)
        end
    end
    -- END MODIFICATION
    
    -- SOL局内小队仅一人时不显示“队伍信息”
    -- OB也不显示
    local bTeamInfoNeedShow = true
    local gameState = UGameplayStatics.GetGameState(GetWorld())
    local bIsMP = (gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Breakthrough) or (gameState.DFMGamePlayerMode == EDFMGamePlayMode.GamePlayMode_Conquest)
    if not bIsMP then
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if isvalid(playerState) then
            if #playerState.MemberInfoList <= 1 then
                bTeamInfoNeedShow = false
            end
        end
    end
    if UGPBattleFieldSystem.Get(GetWorld()).bIsInObserverMode then
        bTeamInfoNeedShow = false
    end
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if bTeamInfoNeedShow and hudStateManager then
        bTeamInfoNeedShow = not hudStateManager:HasState(UE.GameHUDSate.GHS_Redeploy)
    end
    if bTeamInfoNeedShow then
        self._wtBtnTeamInfo:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtBtnTeamInfo:SetVisibility(ESlateVisibility.Collapsed)
    end

    local character = InGameController:Get():GetGPCharacter()
    if self._wtBtnVehicleBreakOut ~= nil then
        if isvalid(character) then
            if character:GetVehicle() ~= nil and bIsMP then
                self._wtBtnVehicleBreakOut:SetVisibility(ESlateVisibility.Visible)
            else
                self._wtBtnVehicleBreakOut:SetVisibility(ESlateVisibility.Collapsed) -- SOL不显示
            end
        end
    end
end

function SystemSettingHDEntrance:OnShow()
    UDFMGameGPM.BeginExtTag("EXCLUDE_SystemSettingHDEntrance")
    UDFMGameGPM.BeginExclude()
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_EscPanel, true)
    end


end

function SystemSettingHDEntrance:OnHide()
    UDFMGameGPM.EndExtTag("EXCLUDE_SystemSettingHDEntrance")
    UDFMGameGPM.EndExclude()
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:RemoveState(UE.GameHUDSate.GHS_EscPanel, true)
    end
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntranceOnHideBegin:Invoke()
end

-- BEGIN MODIFICATION @ VIRTUOS
function SystemSettingHDEntrance:OnHideBegin()
    self:_SetGamepadNavigation(false)
end
-- END MODIFICATION

function SystemSettingHDEntrance:OnClose()
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntranceOnHideBegin:Invoke()
    -- BEGIN MODIFICATION @ VIRTUOS
    self:_SetGamepadNavigation(false)
    -- END MODIFICATION

    UDFMGameplayDelegates.Get(GetWorld()).BreakthroughOnClientRedeployStateChanged:Remove(self.OnClientRedeployStateChanged,self)
end

function SystemSettingHDEntrance:_GetLeaveBattleWindowContent()
    local character = InGameController:Get():GetGPCharacter()
    local gameMode = UGameplayStatics.GetGameState(GetWorld()).DFMGamePlayerMode
    local matchMode=Server.GameModeServer:GetMatchMode()
    local bIsDefaultStr = self:IsDefaultQuitStr(character,gameMode,matchMode)
    local quitStr = ""
    if bIsDefaultStr then
        quitStr = Module.SystemSetting.Config.Loc.Quit
        return quitStr
    end
    local param = {
        ["Quit"] = Module.SystemSetting.Config.Loc.Quit,
        ["DeductPoint"] = Module.SystemSetting.Config.Loc.DeductPoint
    }
    quitStr = StringUtil.Key2StrFormat(Module.SystemSetting.Config.Loc.ConfirmQuitGame, param)
    return quitStr
end

function SystemSettingHDEntrance:IsDefaultQuitStr(character,gameMode,matchMode) --是否显示默认退出信息，即不显示退出惩罚
    if not IsBuildRegionCN() then
        return true
    end
    if InGameController:Get():IsVictoryUniteMatch() then
        return false
    end
    if (character and gameMode ==  EDFMGamePlayMode.GamePlayMode_SOL) then
        if  matchMode.game_rule == MatchGameRule.SOLGameRule then 
            if character:IsImpendingDeath() or character:IsDead() then --危险行动模式下玩家死亡或濒死则不触发处罚
                return true
            end
        else --非危险行动模式不触发处罚
            return true
        end
    end
    if character and (gameMode ==  EDFMGamePlayMode.GamePlayMode_Breakthrough or gameMode ==  EDFMGamePlayMode.GamePlayMode_Conquest) then
        return true
    end
    return false

end

function SystemSettingHDEntrance:_OnClickBtn(btnType)
    loginfo("SystemSettingHDEntrance:_OnClickBtn, BtnType", btnType)
    -- 返回
    if btnType == EscEntrancePanelBtnType.Return then
        -- self:Hide(true, false)
        ---@todo this is a temp solution
        SystemSettingLogic.HideSystemSettingHDEntrance()
        return
    end
    -- 设置
    if btnType == EscEntrancePanelBtnType.Settings then
        -- self:Hide(true, false)
        ---@todo this is a temp solution
        SystemSettingLogic.HideSystemSettingHDEntrance()
        SystemSettingLogic.OpenSystemSettingHDMainView()
        return
    end
    -- 队伍信息
    if btnType == EscEntrancePanelBtnType.TeamInfo then
        SystemSettingLogic.HideSystemSettingHDEntrance()
        local baseHud = UE.BaseHUD.GetHUD(GetWorld())
        if baseHud then
            baseHud:ShowPanel("TeamInfoHUDView", baseHud)
        end
    end
    -- 离开靶场
    if btnType == EscEntrancePanelBtnType.LeaveRange then
        SystemSettingLogic.HideSystemSettingHDEntrance()

        Module.Range:LeaveRange()
        return
    end
    -- 放弃战斗
    if btnType == EscEntrancePanelBtnType.LeaveBattle then
        local leaveBattleTxt = self:_GetLeaveBattleWindowContent()
        local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
        local confirmTxt = Module.SystemSetting.Config.Loc.HDEntrance.confirm
        self.confirmWindowHandle = Module.CommonTips:ShowConfirmWindow(leaveBattleTxt, CreateCallBack(self._LeaveBattle, self), nil, cancelTxt, confirmTxt)
        return
    end
    -- 返回模式大厅
    if btnType == EscEntrancePanelBtnType.BackToModeHall then
        if Module.Guide:IsInNewPlayerMatch() then
            LogAnalysisTool.SignButtonClicked(10150003)
        end
        Module.CommonTips:ShowConfirmWindow(
            Module.SystemSetting.Config.Loc.ConfirmReturnToModeHall,
            function()
                if Module.Guide:IsInNewPlayerMatch() then
                    LogAnalysisTool.SignButtonClicked(10150004)
                end
                Module.IrisSafeHouse:EnterModeHallFlow()
                SystemSettingLogic.HideSystemSettingHDEntrance()
            end
        )
        return
    end
    -- 退出游戏
    if btnType == EscEntrancePanelBtnType.QuitGame then
        Module.SystemSetting:RequestQuitGame(false)
        return
    end
    -- 打开GM面板
    if btnType == EscEntrancePanelBtnType.OpenGMPanel then
        -- self:Hide(true, false)
        ---@todo this is a temp solution
        SystemSettingLogic.HideSystemSettingHDEntrance()
        Module.GM:OpenGMMainPanel(Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game and not ItemOperaTool.bInSettlement)
        return
    end
    -- 停止编辑器
    if btnType == EscEntrancePanelBtnType.StopPIE then
        local localPC = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        localPC.GPInputManager:ProcessStopPIE()
        return
    end

    if btnType == EscEntrancePanelBtnType.SafeHouseBreakOut then
        SystemSettingLogic.HideSystemSettingHDEntrance()

        Module.IrisSafeHouse:TeleportTo3DSafeHouseDefaultLoc()
        return
    end

    if btnType == EscEntrancePanelBtnType.BattleFieldKillSelfRedeploy then
        SystemSettingLogic.HideSystemSettingHDEntrance()
        local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        if not isvalid(localCtrl) then
            return
        end

        local BreakthroughCharacter = localCtrl:GetPawn()
        if not isvalid(BreakthroughCharacter) then
            return
        end

        if type(BreakthroughCharacter.CanKillSelf) == "function" then
            if BreakthroughCharacter:CanKillSelf(false) == false then
                return
            end
        end

        if type(BreakthroughCharacter.KillSelfRedeployInSetting) == "function" then
            BreakthroughCharacter:KillSelfRedeployInSetting(false)
        end
        return
    end

    if btnType == EscEntrancePanelBtnType.ReportVoice then
        Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingReportVoice)
        SystemSettingLogic.HideSystemSettingHDEntrance()
        return
	end

    -- 日志上报
    if btnType == EscEntrancePanelBtnType.LogUpload then
        Server.LogUploadServer:RequestLogUpload()
        return
    end

    -- 脱离卡死
    if btnType == EscEntrancePanelBtnType.BreakOut then
        local character = InGameController:Get():GetGPCharacter()
        if isvalid(character) then
            character:ClientResetWalkingPosition(false)
            SystemSettingLogic.HideSystemSettingHDEntrance()
        end
        return
    end

    -- 新手关重新挑战
    if btnType == EscEntrancePanelBtnType.Rechallenge then
        LogAnalysisTool.SignButtonClicked(10150001)
        Module.CommonTips:ShowConfirmWindow(
            Module.SystemSetting.Config.Loc.ConfirmRechallenge,
            function()
                LogAnalysisTool.SignButtonClicked(10150002)
                local PlayerController = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
                local BasePlayerSettlementComp = PlayerController:GetComponentByClass(UBasePlayerSettlementComponent)
                BasePlayerSettlementComp:NotifyServerClientQuitMatch()
                SystemSettingLogic.HideSystemSettingHDEntrance()
            end
        )
        return
    end

    if btnType == EscEntrancePanelBtnType.VehicleBreakOut then
        local character = InGameController:Get():GetGPCharacter()
        if isvalid(character) then
            character:TryTeleportVehicleToLegalTransform()
            SystemSettingLogic.HideSystemSettingHDEntrance()
        end
        return
    end
end

function SystemSettingHDEntrance:_LeaveBattle()
    -- BEGIN MODIFICATION @ VIRTUOS
    self:_SetGamepadNavigation(false)
    -- END MODIFICATION

    SystemSettingLogic.HideSystemSettingHDEntrance()

    loginfo("[george] _LeaveBattle()")
    if not self._bOnceLeaveBattle then  
        -- self:Hide(true, false)
        ---@todo this is a temp solution
        local PlayerController = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
        local BasePlayerSettlementComp = PlayerController:GetComponentByClass(UBasePlayerSettlementComponent)
        BasePlayerSettlementComp:NotifyServerClientQuitMatch()
        self._bOnceLeaveBattle = true  
    end
end


function SystemSettingHDEntrance:OnNavBack()
    ---@todo this is a temp solution
    SystemSettingLogic.HideSystemSettingHDEntrance()
    return true
end

--客户端部署状态改变
function SystemSettingHDEntrance:OnClientRedeployStateChanged(InRedeployState)
    self:UpdateRedeployButtonVisible()
end

--刷新重新部署按钮可用状态
function SystemSettingHDEntrance:UpdateRedeployButtonVisible()
    if isvalid(self._wtBtnRedeploy) == false then
        return
    end

    --大战场在FallDown、Focus阶段禁用重新部署按钮
    local bCanClick = self:GetRedeployBtnCanClick()
    self._wtBtnRedeploy:SetIsEnabled(bCanClick)
end

function SystemSettingHDEntrance:GetRedeployBtnCanClick()
    -- if true then
    --     return false
    -- end

    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if isvalid(gameState)==false then
        return false
    end

    local GM = gameState.DFMGamePlayerMode
    if (GM ~= EDFMGamePlayMode.GamePlayMode_Breakthrough and GM ~= EDFMGamePlayMode.GamePlayMode_Conquest) then
        return false
    end

    if Server.IrisSafeHouseServer.bIsInRange then
        return false
    end
    
    --获取UDFMRedeployComponent
    local dfmCharacter = GetInGameController():GetGPCharacter()
    if isinvalid(dfmCharacter) then
        logerror("UpdateRedeployButtonVisible isinvalid dfmCharacter")
        return false
    end
    local redeployComponent = UE.GameplayBlueprintHelper.FindComponentByClass(dfmCharacter, UE.DFMRedeployComponent)
    if isinvalid(redeployComponent) then
        logerror("UpdateRedeployButtonVisible isinvalid redeployComponent")
        return false
    end

    -- 倒下到求救阶段就禁用重新部署按钮
    local clientCurState = redeployComponent.ClientCurState

    if clientCurState >= ERedeployStateFuture.FallDown and clientCurState <= ERedeployStateFuture.TurnCameraToSelf then
        return false
    end

    return true
end

function SystemSettingHDEntrance:_GetTeamOtherMembers(currentGameFlow)
    local members = { }
    if currentGameFlow == EGameFlowStageType.Game then
        -- 局内
        local playerState = InGameController:Get():GetPlayerState()
        local allMembers = playerState and playerState.MemberInfoList or { }
        if #allMembers > 1 then --有队友
            local myId = playerState.Uin
            for i = 1, #allMembers do
                if myId ~= allMembers[i].PlayerUin then
                    local member = {
                        PlayerID = allMembers[i].PlayerUin,
                        PlayerName = allMembers[i].PlayerName,
                    }
                    table.insert(members, member)
                end
            end
        end
        if DebugLogic.reportSelf then
            local member = {
                PlayerID = playerState.Uin,
                PlayerName = "test self",
            }
            table.insert(members, member)
        end
    end
    return members
end

--- BEGIN MODIFICATION @ VIRTUOS
function SystemSettingHDEntrance:_SetGamepadNavigation(enabled)
    if enabled then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtBtnContianer, self, "Hittest")
            self._wtNavGroup:AddNavWidgetToArray(self._wtBtnContianer)
            self._wtNavGroup:MarkIsStackControlGroup()

            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self, WidgetUtil.ENavConfigPriority.UI_Pop)
            WidgetUtil.WrapNavBoundary(self._wtNavGroup, {EUINavigation.Up, EUINavigation.Down})
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end
    else
        if self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

            self._wtNavGroup = nil
        end
    end
end
--- END MODIFICATION

return SystemSettingHDEntrance