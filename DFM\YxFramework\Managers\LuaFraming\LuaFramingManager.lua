----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------



--- 一个简易的Lua分帧任务管理器
-----------------------------------------------------------------------
local deque = require "DFM.YxFramework.Core.Library.deque"

local __frameCount = 0

-- Performance local cache
local getPlatformTimeSecond = getPlatformTimeSecond
local getCostThisFrame = getCostThisFrame
local safeUnpack = function(t)
    if t then
        return unpack(t)
    end
end

local function log(...)
    loginfo(string.format("LuaFramingManager[FrameCount=%d]", __frameCount), ...)
end

local function logw(...)
    logwarning(string.format("LuaFramingManager[FrameCount=%d]", __frameCount), ...)
end

---@class LuaFramingTask
---@field [1] function fTaskCallback
---@field [2] table caller
---@field [3] table param
---@field [4] number maxWaitingFrame
---@field [5] EFrameTaskPriority priority
---@field [6] number id
---@field [7] string debugName
---@field [8] boolean bDone
---@field [9] boolean bMultiTask

---@enum EFrameTaskPriority
EFrameTaskPriority = {
    Low = 0,
    Default = 2,
    Medium = 4,
    High = 6,
    SuperHigh = 8
}

EFrameBudgetConfig = {
    Default         = {Budget = 3, Reason = "Default"},
    FrontEnd        = {Budget = 14, Reason = "FrontEnd"},
    Game            = {Budget = 3, Reason = "Game"},
    Game_StackUI    = {Budget = 6, Reason = "Game_StackUI"},

    -- 业务
    LootPanel       = {Budget = 4, Reason = "LootPanel"},
    RentalPanel     = {Budget = 4, Reason = "RentalPanel"},
}

local order_priorities = {
    EFrameTaskPriority.SuperHigh,
    EFrameTaskPriority.High,
    EFrameTaskPriority.Medium,
    EFrameTaskPriority.Default,
    EFrameTaskPriority.Low,
}

local DEFAULT_WAITING_FRAME = 10
local DEFAULT_PRIORITY = EFrameTaskPriority.Default
-- local PRINT_DEBUG_MSG = IsInEditor()
-- local PRINT_DEBUG_MSG = true
local PRINT_DEBUG_MSG = false
local USE_SAFE_CALL = true
local ENABLE_FRAMING = true
local CHECK_SHOULD_FRAMING = false

local global_task_id = 0
local function genTaskId()
    global_task_id = global_task_id + 1
    return global_task_id
end

local function fCheckCallerValid(caller)
    if type(caller) == "table" then
        local hasDestroy = rawget(caller, "_has_destroy_")
        if hasDestroy == true then
            return false    -- table被回收
        end

        local cppinst = rawget(caller, "__cppinst")
        if cppinst and not isvalid(cppinst) then
            return false    -- cppinst被回收
        end

        return true
    end

    return false
end

local DEFAULT_CALLER = {}

---@class LuaFramingManager : ManagerBase
local LuaFramingManager = class("LuaFramingManager", require("DFM.YxFramework.Managers.ManagerBase"))

-----------------------------------------------------------------------
--region Global

function gOpenFramingDebugMsg(bOpen)
    PRINT_DEBUG_MSG = bOpen
end

--endregion
-----------------------------------------------------------------------

local lfm = nil
local function fPostTickLuaFramingManager(delta)
    lfm:LateUpdate(delta)
end

function LuaFramingManager:Ctor()
    self.bUpdating = false
    self.bManualyControlled = false
    self.currentExecutingTaskId = 0

    lfm = self
    slua.setPostTickFunction(fPostTickLuaFramingManager)

    self:ResetFrameBudget()
    self:InitTasks()

    Facade.UIManager.Events.evtStackUIChanged:AddListener(self.OnStackUIChanged, self)
end

function LuaFramingManager:Destroy()
    Facade.UIManager.Events.evtStackUIChanged:RemoveListener(self.OnStackUIChanged, self)
end

-----------------------------------------------------------------------
--region Frame Budget Config

function LuaFramingManager:SelfUpdateFrameBudget()
    if self.bManualyControlled then
        log("SelfUpdateFrameBudget ignored due to manualy controlled.")
        return
    end

    local gameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
    local stackUICount = Facade.UIManager:GetStackUICount()

    if gameFlowType == EGameFlowStageType.Game then
        if stackUICount > 0 then
            self:UpdateFrameBudgetByCfg(EFrameBudgetConfig.Game_StackUI)
        else
            self:UpdateFrameBudgetByCfg(EFrameBudgetConfig.Game)
        end
    else
        self:UpdateFrameBudgetByCfg(EFrameBudgetConfig.FrontEnd)
    end
end

function LuaFramingManager:UpdateFrameBudgetByCfg(cfg, bManualyControlled)
    if bManualyControlled ~= nil then
        self.bManualyControlled = bManualyControlled
    end

    local budget = cfg.Budget
    local reason = cfg.Reason
    self:UpdateFrameBudget(budget, reason)
end

function LuaFramingManager:UpdateFrameBudget(budget, reason)
    log("UpdateFrameBudget", budget, reason)

    self.currentFrameBudget = budget
    self.currentFrameBudgetReason = reason
end

function LuaFramingManager:ResetFrameBudget()
    self.bManualyControlled = false
    self:SelfUpdateFrameBudget()
end

--endregion
-----------------------------------------------------------------------

function LuaFramingManager:InitTasks()
    self.totalTaskCount = 0
    ---@type table<number, LuaFramingTask>
    self.id2task = {}
    ---@type table<EFrameTaskPriority, LuaFramingTask[]>
    ---@type table<number, LuaFramingTask>
    self.delayAddTasks = deque.new()
    self.tasksOfPriority = {
        [EFrameTaskPriority.Low] = deque.new(),
        [EFrameTaskPriority.Default] = deque.new(),
        [EFrameTaskPriority.Medium] = deque.new(),
        [EFrameTaskPriority.High] = deque.new(),
        [EFrameTaskPriority.SuperHigh] = deque.new(),
    }
    self.taskPool = {}
end

function LuaFramingManager:OnStackUIChanged()
    self:SelfUpdateFrameBudget()
end

function LuaFramingManager:OnGameFlowChangeEnter(gameFlowType)
    self:SelfUpdateFrameBudget()
    self:CheckEnableTick(true)
end

function LuaFramingManager:OnGameFlowChangeLeave(gameFlowType)
    -- 每一次离开一个gameflow，清空所有的task
    self:InitTasks()
end

---@param newTask LuaFramingTask
function LuaFramingManager:AddTaskInternal(newTask)
    self.tasksOfPriority[newTask[5]]:push_right(newTask)
    self.id2task[newTask[6]] = newTask

    if self.totalTaskCount == 0 then
        self:_EnableTick(true)
    end

    self.totalTaskCount = self.totalTaskCount + 1
end

function LuaFramingManager:DirectCallFrameTask(fTaskCallback, caller, param, maxWaitingFrame, priority)
    if USE_SAFE_CALL then
        if caller then
            trycall(fTaskCallback, caller, safeUnpack(param))
        else
            trycall(fTaskCallback, safeUnpack(param))
        end
    else
        if caller then
            fTaskCallback(caller, safeUnpack(param))
        else
            fTaskCallback(safeUnpack(param))
        end
    end
end

function LuaFramingManager:RegisterFrameTask(fTaskCallback, caller, param, maxWaitingFrame, priority)
    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetWorld(), "Lua.Perf.PrintLuaIndexingInfo 1")

    local bShouldFraming = ENABLE_FRAMING
    if CHECK_SHOULD_FRAMING then
        if bShouldFraming then
            local fShouldFraming = caller and caller.ShouldFraming
            if fShouldFraming and not fShouldFraming(caller) then
                bShouldFraming = false
            end
        end
    end

    if not bShouldFraming then
        self:DirectCallFrameTask(fTaskCallback, caller, param, maxWaitingFrame, priority)

        return
    end

    caller = setdefault(caller, DEFAULT_CALLER)
    maxWaitingFrame = setdefault(maxWaitingFrame, DEFAULT_WAITING_FRAME)
    priority = setdefault(priority, DEFAULT_PRIORITY)

    local newTask = self:ObtainTask()
    newTask[1] = fTaskCallback  -- fTaskCallback
    newTask[2] = caller        -- caller
    newTask[3] = param        -- param
    newTask[4] = maxWaitingFrame  -- maxWaitingFrame
    newTask[5] = priority     -- priority

    if PRINT_DEBUG_MSG then
        local debugInfo = debug.getinfo(fTaskCallback)
        local shortSrc = string.match(debugInfo.short_src, ".*/(.*)") or "None"
        local callerName = caller._cname or "Default"
        newTask[7] = string.format("[FrameTask(caller=%s)] %s:%d", callerName, shortSrc, debugInfo.linedefined)

        log(string.format("RegisterFrameTask %s", newTask[7]))
    else
        newTask[7] = caller._cname
    end

    if self.bUpdating then
        self.delayAddTasks:push_right(newTask)
    else
        self:AddTaskInternal(newTask)
    end

    -- UKismetSystemLibrary.ExecuteConsoleCommand(GetWorld(), "Lua.Perf.PrintLuaIndexingInfo 0")

    return newTask[6]
end

function LuaFramingManager:RegisterFrameMultiTask(fTaskCallback, caller, param, maxWaitingFrame, priority)
    local taskId = self:RegisterFrameTask(fTaskCallback, caller, param, maxWaitingFrame, priority)
    local task = self:GetFrameTask(taskId)
    task[9] = true
    return taskId
end

function LuaFramingManager:FlushAll()
    local currentExecutingTaskId = self.currentExecutingTaskId

    local a = getPlatformTimeSecond()
    for i = 1, #order_priorities do
        local tasks = self.tasksOfPriority[order_priorities[i]]
        for task in tasks:iter_left() do
            if task[6] ~= currentExecutingTaskId then   -- 避免Flush自己
                self:_ExecuteTask(task, true)
            end
        end
    end
    local b = getPlatformTimeSecond()

    if PRINT_DEBUG_MSG then
        log(string.format("FlushAll cost=%f", (b - a) * 1000))
    end
end

function LuaFramingManager:FlushOne(caller)
    local currentExecutingTaskId = self.currentExecutingTaskId

    for i = 1, #order_priorities do
        local tasks = self.tasksOfPriority[order_priorities[i]]
        for task in tasks:iter_left() do
            if task[2] == caller then
                if task[6] ~= currentExecutingTaskId then   -- 避免Flush自己
                    self:_ExecuteTask(task, true)
                    return
                end
            end
        end
    end
end

function LuaFramingManager:FlushByTaskId(id)
    local task = self:GetFrameTask(id)
    if task then
        self:_ExecuteTask(task, true) 
    else
        logw("LuaFramingManager:FlushByTaskId id not found", id) 
    end
end

function LuaFramingManager:CancelAllFrameTasks(caller)
    for i = 1, #order_priorities do
        local tasks = self.tasksOfPriority[order_priorities[i]]
        for task in tasks:iter_left() do
            if task[2] == caller then
                task[8] = true
            end
        end
    end

    for task in self.delayAddTasks:iter_right() do
        if task[2] == caller then
            task[8] = true
        end
    end
end

function LuaFramingManager:CancelFrameTask(id)
    local task = self.id2task[id]
    if task then
        task[8] = true
    end

    for task in self.delayAddTasks:iter_right() do
        if task[6] == id then
            task[8] = true
        end
    end
end

function LuaFramingManager:GetFrameTask(id)
    return self.id2task[id]
end

function LuaFramingManager:LateUpdate(delta)
    -- self:_CheckAStrangeBug()

    if self.totalTaskCount == 0 then
        return
    end

    local start = os.clock()
    local budgetCount = getCostThisFrame() * 1000
    local currentFrame = LuaTickController:Get():GetFrameIndex()
    __frameCount = currentFrame   -- use for log
    local bReachFrameBudget = self.currentFrameBudget - budgetCount <= 0

    if PRINT_DEBUG_MSG then
        log(string.format("Current lua cost = %f, budget = %f, available cost for this frame = %f", 
        budgetCount, self.currentFrameBudget, self.currentFrameBudget - budgetCount))
    end

    if bReachFrameBudget then
        return
    end

    self.bUpdating = true
    
    for i = 1, #order_priorities do
        local tasks = self.tasksOfPriority[order_priorities[i]]
        for task in tasks:iter_left() do
            local bFinish = task[8]
            if not bFinish then
                bFinish, bReachFrameBudget = self:_ExecuteTask(task, false)

                if PRINT_DEBUG_MSG then
                    log(string.format("Reach frame budget=%fms, current frame cost=%fms", self.currentFrameBudget, budgetCount))
                end
            end

            if bFinish then
                self.id2task[task[6]] = nil
                tasks:pop_left()
                self:FreeTask(task) -- 返回Pool
                self.totalTaskCount = self.totalTaskCount - 1
                if self.totalTaskCount == 0 then
                    self:_EnableTick(false)
                end
            end

            if bReachFrameBudget then
                break
            end
        end

        if bReachFrameBudget then
            break
        end
    end

    local task = self.delayAddTasks:pop_left()
    while task do
        if not task[8] then
            self:AddTaskInternal(task)
        end

        task = self.delayAddTasks:pop_left()
    end

    self.bUpdating = false

    if PRINT_DEBUG_MSG then
        local costThisFrame = getCostThisFrame() * 1000
        log(string.format("Total lua cost this frame = %fms", costThisFrame))

        local stop = os.clock()
        log(string.format("Total lua cost this frame(2) = %fms", (stop - start) * 1000))
    end
end

---@param task LuaFramingTask
function LuaFramingManager:_ExecuteTask(task, bFlush)
    self.currentExecutingTaskId = task[6]

    local start = getPlatformTimeSecond()
    local param = task[3]
    local bCallerValid = fCheckCallerValid(task[2])
    local bMultiTask = task[9]
    local bFinish = false
    local bCallStatus = true
    local bReachFrameBudget = false
    if bCallerValid then
        while not bFinish and (not bReachFrameBudget or bFlush) do
            if task[2] ~= DEFAULT_CALLER then
                if USE_SAFE_CALL then
                    bCallStatus, bFinish = trycall(task[1], task[2], safeUnpack(param))
                else
                    bFinish = task[1](task[2], safeUnpack(param))
                end
            else
                if USE_SAFE_CALL then
                    bCallStatus, bFinish = trycall(task[1], safeUnpack(param))
                else
                    bFinish = task[1](safeUnpack(param))
                end
            end

            if not bCallStatus then
                logw(string.format("Executing frame task: %s failed, abort", task[7]))
                bFinish = true
                task[8] = true
            end

            if not bMultiTask then
                bFinish = true
                task[8] = true
            else
            end
            bReachFrameBudget = getCostThisFrame() * 1000 > self.currentFrameBudget
        end
    end

    -- local stop = os.clock()
    local stop = getPlatformTimeSecond()
    local cost = (stop - start) * 1000

    if PRINT_DEBUG_MSG then
        log(string.format("Executing frame task: %s, cost = %f", 
            task[7], cost))
    end

    self.currentExecutingTaskId = 0

    return bFinish, bReachFrameBudget, cost
end

function LuaFramingManager:_EnableTick(bEnable)
    local bIsFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
    if bIsFrontEnd then
        bEnable = true
    end

    slua.enablePostTick(bEnable)
end

function LuaFramingManager:CheckEnableTick(bIncludeTick)
    local bEnableTick = self.totalTaskCount > 0
    self:_EnableTick(bEnableTick)

    if bIncludeTick then
        LuaTickController:Get():CheckEnableTick(true)
    end
end

function LuaFramingManager:ObtainTask()
    local num = #self.taskPool
    local task
    if num > 0 then
        task = self.taskPool[num]
        self.taskPool[num] = nil
    else
        task = table.create(8, 8)
    end
    task[6] = genTaskId()
    task[7] = ""
    task[8] = false
    task[9] = false

    return task
end

function LuaFramingManager:FreeTask(task)
    table.insert(self.taskPool, task)
end

return LuaFramingManager