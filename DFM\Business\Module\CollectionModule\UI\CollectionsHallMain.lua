----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class CollectionsHallMain : LuaUIBaseView
local CollectionsHallMain = ui("CollectionsHallMain")
local CollectionConfig = Module.Collection.Config
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local FAnchors = import "Anchors"

function CollectionsHallMain:Ctor()
    -- 下拉框
    self._wtZoomRateDroDownRarity = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self.OnRarityCheckedTabIndexChanged)
    self._wtZoomRateDroDownRarity:Event("OnDropDownBoxProcessItemWidget", self._OnProcessWidget, self)
    self._wtCheckButton = self._wtZoomRateDroDownRarity:Wnd("DFCommonCheckButton", UIWidgetBase)
    if wtKeyIconBox then
        wtKeyIconBox:Visible()
        wtKeyIconBox:SetOnlyDisplayOnGamepad(true)
        wtKeyIconBox:InitByDisplayInputActionName("PendantGunType_GamepadUsed", true, 0, true)
    end
    -- 解锁文本
    self._unlockText = self:Wnd("DFTextBlock_66", UITextBlock)

    -- 查看隐藏效果
    self._wtLookButton = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self._wtLookButton:Event("OnClicked", self._OnLookButtonClicked, self)

    -- 前往获取
    self._wtGotoButton = self:Wnd("wtCommonButtonV1S2_1", DFCommonButtonOnly)
    self._wtGotoButton:Event("OnClicked", self._OnGotoButtonClicked, self)

    -- Canvas
    self._wtCanvasPages = self:Wnd("NameSlot_Panel", UIWidgetBase)

    Module.CommonBar:RegStackUITopBarTitle(self, CollectionConfig.Loc.Title)
    Facade.UIManager:RegSwitchSubUI(self, {
        UIName2ID.CollectionsHall_Panel_S1,
        UIName2ID.CollectionsHall_Panel_S2,
        UIName2ID.CollectionsHall_Panel_S3,
        UIName2ID.CollectionsHall_Panel_S4,
    })

    self._zoomRateReddotList = {}
end

local idx2SeasonID = {
    [0] = 202403,
    [1] = 202501,
    [2] = 202502,
}

local idx2SuitID = {
}

function CollectionsHallMain:_OnProcessWidget(index, widget)
    if self._zoomRateReddotList and self._zoomRateReddotList[index] then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._zoomRateReddotList[index])
    end
    if idx2SuitID[index] then
        self._zoomRateReddotList[index] = Module.ReddotTrie:RegisterStaticReddotDotWithConfig(widget,
        {{obType = EReddotTrieObserverType.Collection, key = string.format("NewCollectionPendant_%s", idx2SuitID[index])}})
    else
        logerror("idx2SuitID Error!")
    end
end

function CollectionsHallMain:InitIdx2SuitID()
    for index, value in pairs(idx2SeasonID) do
        local pendantSuitRow = CollectionConfig.MysticalPendantSuitConfig[value]
        if pendantSuitRow then
            idx2SuitID[index] = pendantSuitRow.SuitId
        end
    end
end

function QueryPendantCnt(pendantID)
    for _, data in ipairs(Server.CollectionServer.suit_infos or {}) do
        for _, pendant_data in pairs(data.pendant_list) do
            if pendant_data.pendant_id == pendantID then
                return pendant_data.cnt
            end
        end
    end
    return 0
end

function QuerySuitPendantCnt(seasonID)
    local hasPendantCnt = 0
    local pendantTotalCnt = 0
    local pendantSuitRow = CollectionConfig.MysticalPendantSuitConfig[seasonID]
    if pendantSuitRow then
        pendantTotalCnt = table.nums(pendantSuitRow.PendantIdsClient or {})
        for _, pendantID in ipairs(pendantSuitRow.PendantIdsClient or {}) do
            if QueryPendantCnt(pendantID) > 0 then
                hasPendantCnt = hasPendantCnt + 1
            end
        end
    end
    return hasPendantCnt, pendantTotalCnt
end

function CollectionsHallMain:OnInitExtraData(seasonID)
    self._ServerSeasonID = tonumber(Module.Ranking:GetCurRankSeasonID() or idx2SeasonID[1])
    self._SeasonIDParam = seasonID or self._ServerSeasonID
    self:InitIdx2SuitID()
end

function CollectionsHallMain:InitDataBySeasonID(seasonID)
    self._curSeasonID = tonumber(seasonID or idx2SeasonID[1])
    self._pendantSuitRow = CollectionConfig.MysticalPendantSuitConfig[self._curSeasonID]
    self._serverSuitData = {}
    self._mandelBrickID = nil
    self._pendantUnlockList = {}
    self._pendantUnlockAnimaList = {}
    self._pendantCnt = 0
    if not self._pendantSuitRow then
        logerror("CollectionsHallMain:InitData() self._curSeasonID not in MysticalPendantSuitConfig! self._curSeasonID: ", self._curSeasonID)
        self._curSeasonID = idx2SeasonID[1]
        self._pendantSuitRow = CollectionConfig.MysticalPendantSuitConfig[self._curSeasonID]
    end
    if not self._pendantSuitRow then
        logerror("self._pendantSuitRow Error! self._curSeasonID ", self._curSeasonID)
        return
    end
    for index, data in ipairs(Server.CollectionServer.suit_infos or {}) do
        if data.season_id == self._curSeasonID then
            deepcopy(self._serverSuitData, data)
        end
    end
    self._suit_id = tonumber(self._serverSuitData.suit_id)
    for _, pendantID in ipairs(self._pendantSuitRow.PendantIdsClient or {}) do
        local mandelBrickID = WeaponHelperTool.GetPendantMandelBrickID(pendantID)
        if mandelBrickID then
            self._mandelBrickID = mandelBrickID
            break
        end
    end
    for _, pendantID in ipairs(self._pendantSuitRow.PendantIdsClient or {}) do
        local pendantCnt = QueryPendantCnt(pendantID)
        local hasPendantCnt = pendantCnt > 0
        table.insert(self._pendantUnlockList, hasPendantCnt)
        if hasPendantCnt then
            self._pendantCnt = self._pendantCnt + 1
        end
        table.insert(self._pendantUnlockAnimaList, Server.CollectionServer:GetNewUnlockPendantOnAnimaRed(pendantID))
    end
    if not self._mandelBrickID then
        logerror("self._mandelBrickID is nil!")
    end

    if Server.CollectionServer:GetNewUnlockCollectionPendantFlagBySuitID(self._suit_id) then
        Server.CollectionServer:SetNewUnlockPendantOnRed(self._suit_id, false)
        Server.CollectionServer:CollectionHallRedDotUpdate()
    end

    if self._wtLookButtonReddot then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._wtLookButtonReddot)
    end
    self._wtLookButtonReddot = Module.ReddotTrie:RegisterStaticReddotDotWithConfig(self._wtLookButton,
    {{obType = EReddotTrieObserverType.Collection, key = string.format("NewCollectionPendantActive_%s", self._suit_id)}})
end

-- 解锁隐藏效果样式刷新
function CollectionsHallMain:UnlockHideeffectsStyleRefresh()
    self:SetUnlock(self._serverSuitData.actived and true or false)
    self._unlockText:SetText(self._serverSuitData.actived and CollectionConfig.Loc.SuitUnlock or CollectionConfig.Loc.SuitNotUnlock)
    self._wtGotoButton:SetVisibility(self._serverSuitData.actived and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
end

function CollectionsHallMain:UnlockHideeffectsStyleRefreshOnEvent()
    self:SetUnlock(true)
    self._unlockText:SetText(CollectionConfig.Loc.SuitUnlock)
    self._wtGotoButton:SetVisibility(ESlateVisibility.Collapsed)
end

-- 解锁挂饰样式刷新
function CollectionsHallMain:UnlockPendantStyleRefresh(index)
    local weakUiIns = Facade.UIManager:SwitchSubUIByIndex(self, index, self._wtCanvasPages, self._pendantUnlockList, self._pendantUnlockAnimaList, index)
    if weakUiIns then
        local uiIns = getfromweak(weakUiIns)
        if uiIns then
            local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(uiIns)
            if slot then
                local commonAnchor = FAnchors()
                commonAnchor.Minimum = FVector2D(0, 0)
                commonAnchor.Maximum = FVector2D(1, 1)
                slot:SetOffsets(FMargin(0, 0, 0, 0))
                slot:SetAnchors(commonAnchor)
                slot:SetAutoSize(true)
            end
            uiIns:UpdateUnlockStyle()
        else
            logerror("CollectionsHallMain:UnlockPendantStyleRefresh uiIns is nil!")
        end
    else
        logerror("CollectionsHallMain:UnlockPendantStyleRefresh weakUiIns is nil!")
    end
end


function CollectionsHallMain:OnShowBegin()
    self:InitDataBySeasonID(self._SeasonIDParam or self._ServerSeasonID)
    if not self._pendantSuitRow then
        logerror("self._pendantSuitRow Error!")
        return
    end
    -- 下拉框
    local cnt = 0
    local initIndex = 0
    local zoomRateDroDownLoc = {}
    local param = {}
    local curIndex = 1
    for value, locs in ipairs(CollectionConfig.SeasonValue2Name) do
        local seasonID = table.keyof(CollectionConfig.seasonId2Value, value)
        if CollectionConfig.MysticalPendantSuitConfig[seasonID] then
            cnt = cnt + 1
            local hasPendantCnt, pendantTotalCnt = QuerySuitPendantCnt(seasonID)
            param["countNum"] = hasPendantCnt
            param["maxNum"] = pendantTotalCnt
            if seasonID == self._SeasonIDParam then
                curIndex = cnt
            end
            if seasonID == self._ServerSeasonID then
                self._curHasPendantCnt = hasPendantCnt
                self._curPendantTotalCnt = pendantTotalCnt
                table.insert(zoomRateDroDownLoc, StringUtil.SequentialFormat(locs[2], StringUtil.Key2StrFormat(CollectionConfig.Loc.NumFormat3, param)))
            else
                table.insert(zoomRateDroDownLoc, StringUtil.SequentialFormat(locs[1], StringUtil.Key2StrFormat(CollectionConfig.Loc.NumFormat3, param)))
            end
            if seasonID == self._ServerSeasonID then -- 只展示至当前赛季的配置
                initIndex = cnt
                break
            end
        else
            logerror("seasonID not in MysticalPendantSuitConfig ", seasonID)
        end
    end
    initIndex = math.max(initIndex, 1)
    UIUtil.InitDropDownBox(self._wtZoomRateDroDownRarity, zoomRateDroDownLoc, {}, curIndex - 1)

    if CollectionConfig.SeasonValue2Name[curIndex] then
        param["countNum"] = self._curHasPendantCnt
        param["maxNum"] = self._curPendantTotalCnt
        self._wtCheckButton:SetMainTitleText4AllState(StringUtil.SequentialFormat(CollectionConfig.SeasonValue2Name[curIndex][1], StringUtil.Key2StrFormat(CollectionConfig.Loc.NumFormat3, param)))
    end

    self:UnlockHideeffectsStyleRefresh()
    self:UnlockPendantStyleRefresh(curIndex)

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadFeature()
    end 
    -- END MODIFICATION

    self:AddLuaEvent(CollectionConfig.Events.evtHangingSuitActived, self.UnlockHideeffectsStyleRefreshOnEvent, self)

    if self._zoomRateReddot then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._zoomRateReddot)
    end
    self._zoomRateReddot = Module.ReddotTrie:RegisterStaticReddotDotWithConfig(self._wtZoomRateDroDownRarity,
    {{obType = EReddotTrieObserverType.Collection, key = "NewCollectionPendant"}})
end

function CollectionsHallMain:OnRarityCheckedTabIndexChanged(idx)
    loginfo("CollectionsHallMain:OnRarityCheckedTabIndexChanged idx =", idx)
    if not idx2SeasonID[idx] then
        logerror("idx error! ", idx)
        return
    end
    self._curSeasonID = idx2SeasonID[idx]
    self:InitDataBySeasonID(self._curSeasonID)
    self:UnlockHideeffectsStyleRefresh()
    self:UnlockPendantStyleRefresh(idx + 1)

    if CollectionConfig.SeasonValue2Name[idx + 1] then
        local param = {}
        local hasPendantCnt, pendantTotalCnt = QuerySuitPendantCnt(self._curSeasonID)
        param["countNum"] = hasPendantCnt
        param["maxNum"] = pendantTotalCnt
        self._wtCheckButton:SetMainTitleText4AllState(StringUtil.SequentialFormat(CollectionConfig.SeasonValue2Name[idx + 1][1], StringUtil.Key2StrFormat(CollectionConfig.Loc.NumFormat3, param)))
    end
end

function CollectionsHallMain:_OnLookButtonClicked()
    local function fFinishCallback(uiIns)
        uiIns:RefreshUI(
            self._pendantSuitRow.SuitEffectName,
            self._pendantSuitRow.SuitEffectDes,
            self._pendantCnt,
            #self._pendantUnlockList,
            self._pendantSuitRow.VideosPath
        )
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionsHall_Pop, fFinishCallback, nil, self._serverSuitData, self._suit_id)

end

function CollectionsHallMain:_OnGotoButtonClicked()
    if self._mandelBrickID then
        Module.Collection:ShowMandelBrickPage(self._mandelBrickID)
    else
        logerror("self._mandelBrickID is nil")
    end
end

function CollectionsHallMain:OnClose()
    Facade.UIManager:UnRegSwitchSubUI(self)
end

-- BEGIN MODIFICATION @ VIRTUOS : Navigation and Input
function CollectionsHallMain:_EnableGamepadFeature()
    if not IsHD() then
        return
    end
    if self._wtZoomRateDroDownRarity then
        self._wtZoomRateDroDownRarity:SetCppValue("bIsFocusable", true)
        if not self._wtZoomRateDroDownNavGroup then
            self._wtZoomRateDroDownNavGroup = WidgetUtil.RegisterNavigationGroup( self._wtZoomRateDroDownRarity, self, "Hittest")
            if self._wtZoomRateDroDownNavGroup then
                self._wtZoomRateDroDownNavGroup:AddNavWidgetToArray(self._wtZoomRateDroDownRarity)
            end
        end
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtZoomRateDroDownNavGroup)
        if self._wtZoomRateDroDownRarity.MenuAnchor then
            self._wtZoomRateDroDownRarity:Event("PostOnMenuOpenChanged_GamepadUsed", self._OnDropDownBoxOpenStateChanged, self)
        end
    end

    self._wtLookButton:SetDisplayInputAction("Common_ButtonLeft", false, nil, true)
    if not self._wtLookButton_handle then
	    self.__wtLookButton_handle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._OnLookButtonClicked, self, EDisplayInputActionPriority.UI_Stack)
    end

    self._wtGotoButton:SetDisplayInputAction("Common_ButtonTop", false, nil, true)
    if not self._wtGotoButton_handle then
	    self._wtGotoButton_handle = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, self._OnGotoButtonClicked, self, EDisplayInputActionPriority.UI_Stack)
    end

    if not self._openWeaponTypeHandle then 
        self._openWeaponTypeHandle = self:AddInputActionBinding("PendantGunType_GamepadUsed", EInputEvent.IE_Pressed, self._OnWeaponTypeDropDownOpen, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function CollectionsHallMain:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    if self._wtLookButton_handle then
        self:RemoveInputActionBinding(self._wtLookButton_handle)
        self._wtLookButton_handle= nil
    end
    if self._wtGotoButton_handle then
        self:RemoveInputActionBinding(self._wtGotoButton_handle)
        self._wtGotoButton_handle= nil
    end
    if self._DropDownNavGroup then
        self._DropDownNavGroup = nil
    end
    if self._wtZoomRateDroDownNavGroup then
        self._wtZoomRateDroDownNavGroup = nil
    end
    WidgetUtil.RemoveNavigationGroup(self)
    if self._openWeaponTypeHandle then 
        self:RemoveInputActionBinding(self._openWeaponTypeHandle)
        self._openWeaponTypeHandle = nil
    end
end

function CollectionsHallMain:_OnDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return
    end
    if bOpen then
        self:_RegisterDropDownNavGroup()
    else
        self:_RemoveDropDownNavGroup()
    end
end

function CollectionsHallMain:_RegisterDropDownNavGroup()
    if not IsHD() then
        return
    end
    if not self._DropDownNavGroup then
        if self._wtZoomRateDroDownRarity and self._wtZoomRateDroDownRarity.ScrollGridBox then
            self._DropDownNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtZoomRateDroDownRarity.ScrollGridBox, self._wtZoomRateDroDownRarity, "Hittest")
        end
        if self._DropDownNavGroup then
            self._DropDownNavGroup:AddNavWidgetToArray(self._wtZoomRateDroDownRarity.ScrollGridBox)
            self._DropDownNavGroup:MarkIsStackControlGroup()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._DropDownNavGroup)
        end
    end
end

function CollectionsHallMain:_RemoveDropDownNavGroup()
    if not IsHD() then
        return
    end
    if self._DropDownNavGroup then
        self._DropDownNavGroup = nil
    end
    WidgetUtil.RemoveNavigationGroup(self._wtZoomRateDroDownRarity)
end

function CollectionsHallMain:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end

    self:RemoveAllLuaEvent()
end

function CollectionsHallMain:Destroy()
    for _, reddot in ipairs(self._zoomRateReddotList) do
        Module.ReddotTrie:UnRegisterStaticReddotDot(reddot)
    end
    self._zoomRateReddotList = nil

    if self._wtLookButtonReddot then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._wtLookButtonReddot)
    end
    self._wtLookButtonReddot = nil

    if self._zoomRateReddot then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._zoomRateReddot)
    end
    self._zoomRateReddot = nil
end

function CollectionsHallMain:_OnWeaponTypeDropDownOpen()
    if self._wtZoomRateDroDownRarity:IsMenuShow() then 
        self._wtZoomRateDroDownRarity:CloseMenu()
    else
        self._wtZoomRateDroDownRarity:OpenMenu()
    end
end

-- END MODIFICATION
return CollectionsHallMain
