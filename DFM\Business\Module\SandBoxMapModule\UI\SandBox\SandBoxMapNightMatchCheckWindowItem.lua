local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSandBoxMap)
----- LOG FUNCTION AUTO GENERATE END -----------




---@class SandBoxMapNightMatchCheckWindowItem : LuaUIBaseView
local SandBoxMapNightMatchCheckWindowItem = ui("SandBoxMapNightMatchCheckWindowItem")

local CommonWidgetConfig = Module.CommonWidget.Config
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos

function SandBoxMapNightMatchCheckWindowItem:Ctor()
    loginfo("SandBoxMapNightMatchCheckWindowItem:Ctor")
    self._wtTipIcon=self:Wnd("Image_Icon",UIImage)
    self._wtTipText=self:Wnd("DFRichTextBlock_61",UITextBlock)
    self._wtTitleText=self:Wnd("DFRichTextBlock",UITextBlock)
    --配装价值
    self._wtEquipValuePanel=self:Wnd("DFHorizontalBox_2",UIWidgetBase)
    self._wtNeedValueText=self:Wnd("DFRichTextBlock_1",UITextBlock)
    self._wtCurValueText=self:Wnd("DFRichTextBlock_2",UITextBlock)
    --总价值
    self._wtTotalValuePanel=self:Wnd("DFHorizontalBox",UIWidgetBase)
    --禁用道具
    self._wtBanItemPanel=self:Wnd("DFSizeBox",UIWidgetBase)
    -- self._wtWaterScroll=UIUtil.WndWaterfallScrollBox(self,"DFWaterfallScrollView_171",self.fGetItemCount,self.fOnProcessItem)
    self._wtScrollBox=self:Wnd("DFScrollBox_92",UIScrollBox)
end

function SandBoxMapNightMatchCheckWindowItem:OnOpen()
    loginfo("SandBoxMapNightMatchCheckWindowItem:OnOpen")
end

function SandBoxMapNightMatchCheckWindowItem:OnClose()
    loginfo("SandBoxMapNightMatchCheckWindowItem:OnClose")
    Facade.UIManager:ClearSubUIByParent(self,self._wtScrollBox)
end

function SandBoxMapNightMatchCheckWindowItem:SetData(type,...)
    loginfo("SandBoxMapNightMatchCheckWindowItem:SetData",type)
    if type<=3 then
        self._wtTipText:SelfHitTestInvisible()
        if type==3 then
            -- local ETextJustify = import("ETextJustify")
            -- self._wtTipText:SetJustification(ETextJustify.Right)
        end
    else
        self._wtTipText:Collapsed()
        if type==4 then
            self._wtEquipValuePanel:SelfHitTestInvisible()
            local paramList={...}
            local CurrencyHelperTool=require"DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
            local requireValue=CurrencyHelperTool.GetCurrencyNumFormatStr(paramList[1],CurrencyHelperTool.EKMThousandsType.None)
            local currentValue=CurrencyHelperTool.GetCurrencyNumFormatStr(paramList[2],CurrencyHelperTool.EKMThousandsType.None)
            self._wtNeedValueText:SetText(string.format(Module.ArmedForce.Config.Loc.RequiredValueNum,requireValue))
            self._wtCurValueText:SetText(string.format(Module.ArmedForce.Config.Loc.CurrentValueNum,currentValue))
        elseif type==5 then
            self._wtTotalValuePanel:SelfHitTestInvisible()
        elseif type==6 then
            self._wtBanItemPanel:SelfHitTestInvisible()
            local paramList={...}
            self._itemList=paramList[1]
            self._bItemBanned=paramList[2]
            -- self._wtWaterScroll:Visible()
            -- self._wtWaterScroll:RefreshAllItems()
            self:ProcessItem()
        end
    end
end

function SandBoxMapNightMatchCheckWindowItem:SetIcon(tipImage)
    loginfo("SandBoxMapNightMatchCheckWindowItem:SetIcon",tipImage)
    if tipImage then
        self._wtTipIcon:SelfHitTestInvisible()
        self._wtTipIcon:AsyncSetImagePath(tipImage,false)
    else
        self._wtTipIcon:Collapsed()
    end
end

function SandBoxMapNightMatchCheckWindowItem:SetTitle(title)
    loginfo("SandBoxMapNightMatchCheckWindowItem:SetTitle",title)
    if title then
        self._wtTitleText:SetText(title)
    end
end

function SandBoxMapNightMatchCheckWindowItem:SetTip(tipText)
    loginfo("SandBoxMapNightMatchCheckWindowItem:SetTip",tipText)
    if tipText then
        self._wtTipText:SetText(tipText)
    end
end

function SandBoxMapNightMatchCheckWindowItem:SetTitleAndTip(title,tipText)
    self:SetTitle(title)
    self:SetTip(tipText)
end

function SandBoxMapNightMatchCheckWindowItem:ProcessItem()
    Facade.UIManager:RemoveSubUIByParent(self,self._wtScrollBox)
    local count=self:fGetItemCount()
    for i=1,count do
        local weakItem,instanceId=Facade.UIManager:AddSubUI(self,UIName2ID.IVCommonItemTemplate,self._wtScrollBox)
        if weakItem then
            local itemWidget=getfromweak(weakItem)
            if itemWidget then
                itemWidget:SetSize(168, 168)
                local banItem = self._itemList[i]
                local item = ItemBase:NewIns(banItem.id, banItem.banItemNum)
                item:ForceSetBindType(banItem.bindType)
                local bindingComp = itemWidget:FindOrAdd(EComp.ExposureItem, UIName2ID.IVInaccessibleComponent, EIVSlotPos.MaskLayer)
                if not self._bItemBanned then
                    bindingComp:SetText(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.ItemCountLimitText,banItem.banItemLimitCount))
                end
                bindingComp:EnableComponent(true)
                itemWidget:Visible()
                itemWidget:InitItem(item)
                itemWidget:RefreshView()
                itemWidget:SetCommonHoverBgHideFreeAnalogCursorHideFocusContentRoot(true)
                itemWidget:BindCustomOnClicked(
                    function()
                        Module.ItemDetail:OpenItemDetailPanel(item, itemWidget)
                    end
                )
            end
        end
    end
end

function SandBoxMapNightMatchCheckWindowItem:fGetItemCount()
    return self._itemList and #self._itemList or 0
end

function SandBoxMapNightMatchCheckWindowItem:fOnProcessItem(position,itemWidget)
    local banItem = self._itemList[position]
    if not banItem then
        return
    end
    local item = ItemBase:NewIns(banItem.id, banItem.banItemNum)
    item:ForceSetBindType(banItem.bindType)
    local bindingComp = itemWidget:FindOrAdd(EComp.ExposureItem, UIName2ID.IVInaccessibleComponent, EIVSlotPos.MaskLayer)
    -- bindingComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Binding)
    if not self._bItemBanned then
        bindingComp:SetText(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.ItemCountLimitText,banItem.banItemLimitCount))
    end
    bindingComp:EnableComponent(true)
    itemWidget:Visible()
    itemWidget:InitItem(item)
    itemWidget:RefreshView()
    itemWidget:BindCustomOnClicked(
        function()
            Module.ItemDetail:OpenItemDetailPanel(item, itemWidget)
        end
    )
end

return SandBoxMapNightMatchCheckWindowItem
