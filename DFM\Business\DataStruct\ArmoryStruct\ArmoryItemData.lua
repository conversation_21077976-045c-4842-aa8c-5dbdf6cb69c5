----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmory)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ArmoryItemData
ArmoryItemData = {} 

local Sort      = require "DFM.Business.DataStruct.Common.Base.Sort"
local Deep      = require "DFM.Business.DataStruct.Common.Base.Deep"

---@class ArmoryUnlockTask
---@field goalID   integer
---@field taskDesc string
---@field taskProgress integer
---@field taskGoal integer

---@enum ArmoryItemType （枚举值关联pb协议）
ArmoryItemData.ArmoryItemType = {
    Weapon  = 1,    -- 武器
    Special = 2,    -- 兵种道具
    Part    = 3,    -- 配件
    Vehicle = 4,    -- 载具
}

---@enum ArmoryItemState
ArmoryItemData.ArmoryItemState = {
    InProgress = 1,
    NoProgress = 2,
    Completed  = 3,
    Unlocked   = 4,
    --这个顺序是按照列表排序顺序(除了播放解锁动画的要临时置顶)来的，不要改动
}
local ArmoryItemState = ArmoryItemData.ArmoryItemState

---用服务器回包初始化ArmoryItemData对象
---@return ArmoryItemData
function ArmoryItemData.Create()
    ---@type ArmoryItemData
    local obj = setmetatable({},{__index = ArmoryItemData})
    obj:Init()
    return obj
end


function ArmoryItemData:Init()
    self.itemID = nil       --注意这个不是物品ID而是所谓的“机匣功能ID”，后端和策划的表结构列名有模糊
    self.actualItemID = nil --这个才是物品ID
    self.relatedID = nil    --关联ID（枪械类: PresetID, 配件载具和兵种道具类: ItemID）

    self.itemType = nil
    self.seasonID = nil

    self.unlockTasks = {} ---@type ArmoryUnlockTask[]
    self.currentUnlockTask = nil ---@type ArmoryUnlockTask
    self.bActivated = false

    self.bUnlocked = false
    self.unlockTime = 0 ---@type integer
    self.queueOrder = 0 ---@type integer

    self.onShelfTime = nil  ---@type integer
    self.offShelfTime = nil  ---@type integer

    self.lastShownTaskStage = nil  --上次被UI展示时的任务阶段
    self.lastShownUnlockState = nil --上次被UI展示时的解锁状态
end

function ArmoryItemData:GetTotalTaskStages()
    return #self.unlockTasks
end

---获取当前正在进行的任务阶段和任务数据，全部已完成则返回最后一个阶段
---@return integer, ArmoryUnlockTask
function ArmoryItemData:GetCurrentStageAndTask()
    for stageIdx, taskStruct in ipairs(self.unlockTasks) do
        if taskStruct.taskGoal > taskStruct.taskProgress then
            return stageIdx, taskStruct
        end
    end
    return #self.unlockTasks, self.unlockTasks[#self.unlockTasks]
end

function ArmoryItemData:GetCurrentTask()
    local stage, task = self:GetCurrentStageAndTask()
    return task
end

function ArmoryItemData:GetCurrentTaskStage()
    local stage, task = self:GetCurrentStageAndTask()
    return stage
end

function ArmoryItemData:GetCurrentTaskProgress()
    local _, task = self:GetCurrentStageAndTask()
    if task then return task.taskProgress, task.taskGoal end
end

---获取当前任务阶段的进度，返回0~1的小数，进度条用
function ArmoryItemData:GetCurrentTaskProgressFloat()
    local _, task = self:GetCurrentStageAndTask()
    if task then return task.taskProgress/task.taskGoal end
end

---获取当前任务阶段的目标描述
function ArmoryItemData:GetCurrentTaskDesc()
    local _, task = self:GetCurrentStageAndTask()
    if task then return task.taskDesc end
end

---获取代表奖品的ItemBase对象
---@return ItemBase
function ArmoryItemData:GetItemBase()
    return Module.Armory.itemBaseCache:Get(self.actualItemID)
end

---返回现在正在进行的阶段和阶段总数
function ArmoryItemData:GetStageInfo()
    local stage, task = self:GetCurrentStageAndTask()
    if task then
        return stage, #self.unlockTasks
    end
end

---@return ArmoryItemState
function ArmoryItemData:GetState()
    if self.bUnlocked then return ArmoryItemState.Unlocked end

    local stage, task = self:GetCurrentStageAndTask()
    if (stage == #self.unlockTasks) and (task.taskGoal == task.taskProgress) then
        return ArmoryItemState.Completed
    end

    if stage == 1 and task.taskProgress == 0 then return ArmoryItemState.NoProgress end
    
    return ArmoryItemState.InProgress
end

function ArmoryItemData:IsOnShelf()
    local now = Facade.ClockManager:GetLocalTimestamp()
    return (self.onShelfTime and self.onShelfTime < now) and 
           (now < self.offShelfTime or self.offShelfTime == nil or self.offShelfTime == 0)
end


return ArmoryItemData