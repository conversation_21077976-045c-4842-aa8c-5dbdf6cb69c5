---@class QuestSeasonActionPhase : LuaUIBaseView

local QuestSeasonActionPhase = ui("QuestSeasonActionPhase")

function QuestSeasonActionPhase:Ctor()
    self._wtStageText = self:Wnd("DFTextBlock_129", UITextBlock)
    self._wtProgressText = self:Wnd("DFTextBlock_54", UITextBlock)
    self._wtBtn = self:Wnd("DFButton_67", UIButton)
    if self._wtBtn then
        self._wtBtn:Event("OnClicked", self._OnClicked, self)
        self._wtBtn:Event("OnHovered", self._OnHovered, self)
        self._wtBtn:Event("OnUnhovered", self._OnUnhovered, self)
    end
    self._bisUnlock = false
    self._bIsSelected = false
    self._stageInfo = nil
end

function QuestSeasonActionPhase:OnShow()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
end

function QuestSeasonActionPhase:OnHide()
    if self.curReddotProxy ~= nil then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Quest,EQuestDynamicDataType.EQuestSeasonReddot,self.curReddotProxy)
        self.curReddotProxy = nil
    end
end

function QuestSeasonActionPhase:SetProgressText(gain,total)
    self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarProgress,gain,total))
end

function QuestSeasonActionPhase:SetIsUnlocked(bisUnlock)
    self._bisUnlock = bisUnlock
end

function  QuestSeasonActionPhase:SetStageInfo(stageInfo)
    self._stageInfo = stageInfo
    self._wtStageText:SetText("0" .. stageInfo.order)
    self:_RegisterReddot()
end

function QuestSeasonActionPhase:SetSelectedState(bIsSelected)
    self._bIsSelected = bIsSelected
    self:_RefreshView()
end

function QuestSeasonActionPhase:_OnClicked()
    Module.Quest.Config.evtQuestSeasonPhaseItemSelected:Invoke(self._stageInfo)
end

function QuestSeasonActionPhase:_OnHovered()
    if self._bisUnlock then
        self:SetType(4)
    else
        self:SetType(1)
    end
end

function QuestSeasonActionPhase:_OnUnhovered()
    self:SetSelectedState(self._bIsSelected)
end

function QuestSeasonActionPhase:_RegisterReddot()
    if self.curReddotProxy == nil then
        local fCheckReddot = function (id)
            local needRet = Server.QuestServer:GetNotifyQuestInSeasonStage(id)
            return needRet
        end
        self.curReddotProxy = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Quest,
            EQuestDynamicDataType.EQuestSeasonReddot,fCheckReddot,self._stageInfo.stageID,self)
    end
end

function QuestSeasonActionPhase:_OnQuestStateUpdate(questId)
    local lineInfo = Server.QuestServer:GetCurrentSeasonLine()
    if lineInfo == nil then 
        return 
    end

    if self._bisUnlock then
        if lineInfo:GetStageIDByQuestID(questId) == self._stageInfo.stageID then
            self:SetProgressText(lineInfo:CalGainStarByStageID(self._stageInfo.stageID),lineInfo:CalTotalStarByStageID(self._stageInfo.stageID))
        end
    else
        if lineInfo and lineInfo:IsUnLockByStageID(self._stageInfo.stageID) then
            self:SetIsUnlocked(true)
            self:_RefreshView()
            self:SetProgressText(lineInfo:CalGainStarByStageID(self._stageInfo.stageID),lineInfo:CalTotalStarByStageID(self._stageInfo.stageID))
            self:PlayWidgetAnim(self.WBP_SeasonalTasks_ActionPhase_Unlock)
        end 
    end
end

function QuestSeasonActionPhase:_RefreshView()
    if self._bIsSelected then
        if self._bisUnlock then
            self:SetType(5)
        else
            self:SetType(2)
        end
    else
        if self._bisUnlock then
            self:SetType(3)
        else
            self:SetType(0)
        end
    end
end

return QuestSeasonActionPhase