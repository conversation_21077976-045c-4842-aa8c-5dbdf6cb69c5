----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
---@class QuestSeasonTaskSet : LuaUIBaseView

local QuestSeasonTaskSet = ui("QuestSeasonTaskSet")

function QuestSeasonTaskSet:Ctor()
    
    self._wtHead = self:Wnd("WBP_SeasonalTasks_TaskEntry_V1", UIWidgetBase)

    self._wtQuestScrollBox =
    UIUtil.WndScrollGridBox(self, "DFScrollGridBox_0", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtListPanel = self:Wnd("DFCanvasPanel_26", UIWidgetBase)

    self._wtHead._wtBtn:Event("OnClicked", self._OnHeadClicked, self)

    self._wtDecoLine = self:Wnd("DFImage", UIWidgetBase)

    self._lineInfo = nil
    self._groupInfo = nil
    self._questList = nil
    self._bIsMainGroup = false
    self._curSelectedQuest = nil
    self._selectedQuestWidget = nil
    self._bIsUnfold = false
    self._bIsSelected = false
end

function QuestSeasonTaskSet:OnShow()
    self:AddLuaEvent(Module.Quest.Config.evtQuestSeasonEntryClicked, self._OnQuestEntryClicked, self)
    self:AddLuaEvent(Module.Quest.Config.evtQuestSeasonGroupUnlocked, self._OnQuestGroupUnlocked, self)
end

function QuestSeasonTaskSet:OnHide()
    -- loginfo("QuestSeasonTaskSet: OnHide Clear lineInfo: ", self._groupInfo.name)    
    self:ClearData()
    self:RemoveAllLuaEvent()
end

function QuestSeasonTaskSet:ClearData()
    self._lineInfo = nil
    self._groupInfo = nil
    self._questList = nil
    self._bIsMainGroup = false
    self._curSelectedQuest = nil
    self._selectedQuestWidget = nil
    self._bIsUnfold = false
    self._bIsSelected = false
end

function QuestSeasonTaskSet:RefreshWithGroup(lineInfo, stageId, groupInfo, bIsUnfold)

    self:ClearData()

    self._questList = nil

    self._lineInfo = lineInfo
    self._groupInfo = groupInfo

    self._wtHead:RefreshWithGroup(lineInfo, stageId, groupInfo)

    self._questList = {}
    for index, value in ipairs(groupInfo.questIdArry) do
        table.insert(self._questList, Server.QuestServer:GetQuestInfoById(value))
    end
    
    self._bIsMainGroup = lineInfo:IsMainGroup(stageId,groupInfo.groupID)

    self:_SortQuestInfos()
    self:SetIsUnfold(bIsUnfold)

    -- loginfo("JackieTest: QuestSeasonTaskSet: RefreshWithGroup: RefreshAllItems ", self._groupInfo.name)
    -- self._wtQuestScrollBox:RefreshAllItems()
end

function QuestSeasonTaskSet:_OnGetItemCount()
    return #self._questList
end

function QuestSeasonTaskSet:_OnProcessItemWidget(position, itemWidget)
    local questInfo = self._questList[position + 1]
    if questInfo then
        -- loginfo("JackieTest: ProcessQuest: ", questInfo.name, " Widget: ", itemWidget._debugName)
        if self._curSelectedQuest then
            itemWidget:RefreshItemWidget(questInfo, self._curSelectedQuest.id == questInfo.id)
            if self._curSelectedQuest.id == questInfo.id then 
                self._selectedQuestWidget = itemWidget
            end
        else
            itemWidget:RefreshItemWidget(questInfo, false)
        end
    end
    if self._selectedQuestWidget then
        -- loginfo("JackieTest: ProcessQuest: SelectedQuest: ", self._selectedQuestWidget._debugName)
    end
end

function QuestSeasonTaskSet:_OnHeadClicked()
    -- loginfo("JackieTest: TaskSet _OnHeadClicked")
    if self._groupInfo == nil then
        return
    end
    Module.Quest.Config.evtQuestSeasonHeadClicked:Invoke(self._groupInfo.groupID, self)
end

function QuestSeasonTaskSet:SetIsUnfold(bIsUnfold)
    self._bIsUnfold = bIsUnfold
    self:_UpdateViews()
end

function QuestSeasonTaskSet:SetIsSelected(bIsSelected)
    self._bIsSelected = bIsSelected
    self:_UpdateViews()
end

function QuestSeasonTaskSet:_SortQuestInfos()
    if self._bIsMainGroup then
        self._questList = QuestLogic.SortQuestList(self._questList, 0)
    end
end

function QuestSeasonTaskSet:ShowDecoLine(bisShow)
    if bisShow then
        self._wtDecoLine:SelfHitTestInvisible()
        self:SetTaskPadding(1)
    else
        self._wtDecoLine:Collapsed()
        self:SetTaskPadding(0)
    end
end

function QuestSeasonTaskSet:SelectDefaultQuest()
    for key, value in pairs(self._questList) do
        if value.state < QuestState.Rewarded then
            self._curSelectedQuest = self._questList[key]
            break
        end
    end
    if self._curSelectedQuest == nil then 
        self._curSelectedQuest = self._questList[1]
    end
    self._wtQuestScrollBox:RefreshAllItems()
    return self._curSelectedQuest
end

function QuestSeasonTaskSet:ClearAllSelection()
    self._curSelectedQuest = nil
    -- loginfo("JackieTest: QuestSeasonTaskSet: ClearAllSelection: RefreshAllItems", self._groupInfo.name)    
    self._wtQuestScrollBox:RefreshAllItems()
end

function QuestSeasonTaskSet:SelectQuestByID(questId)
    if self._groupInfo.groupID ~= self._lineInfo:GetGroupIDByQuestID(questId) then
        logerror("Quest is not in this Group")
        return
    end

    for key, value in pairs(self._questList) do
        if value.id == questId then
            self._curSelectedQuest = self._questList[key]
            break
        end
    end
    self._wtQuestScrollBox:RefreshAllItems()
end

function QuestSeasonTaskSet:_OnQuestEntryClicked(questInfo, itemWidget)
    if self._groupInfo == nil or self._lineInfo == nil then
        return
    end
    local groupID = self._lineInfo:GetGroupIDByQuestID(questInfo.id)
    if groupID == self._groupInfo.groupID then
        if self._curSelectedQuest == nil or self._curSelectedQuest.id ~= questInfo.id then
            self._curSelectedQuest = questInfo
            if self._selectedQuestWidget then
                self._selectedQuestWidget:SetIsSelected(false)
            end
            self._selectedQuestWidget = itemWidget
        end
        Module.Quest.Config.evtQuestSeasonEntryClickedWithGroupInfo:Invoke(
            questInfo, itemWidget,
            self._groupInfo.groupID, self
        )
    end
end

function QuestSeasonTaskSet:_OnQuestGroupUnlocked(groupId)
    if self._groupInfo == nil then
        return
    end
    if groupID == self._groupInfo.groupID then
        -- loginfo("JackieTest: QuestSeasonTaskSet: _OnQuestGroupUnlocked: RefreshAllItems", self._groupInfo.name)
        self._wtQuestScrollBox:RefreshAllItems()
    end
end

function QuestSeasonTaskSet:ToggleFold()
    self:SetIsUnfold(not self._bIsUnfold)
end

function QuestSeasonTaskSet:_UpdateViews()
    self._wtHead:SetIsSelected(self._bIsSelected)
    self._wtHead:SetIsUnfold(self._bIsUnfold)
    if self._bIsUnfold then
        self._wtListPanel:Visible()
    else
        self._wtListPanel:Collapsed()
    end
    self:Collapsed()
    self:Visible()
    -- self:LuaCallInvalidate(2)
end

function QuestSeasonTaskSet:GetLastItem()
    local lastIndex = #self._questList
    local widget = self._wtQuestScrollBox:GetItemByIndex(lastIndex)
    return widget
end

return QuestSeasonTaskSet