----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



---聊天输入的主界面
---@class ChatInputPanel : LuaUIBaseView
local ChatInputPanel = ui("ChatInputPanel")
local ChatField = Module.Chat.Field
local ETextCommit = import "ETextCommit"
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"
local FAnchors = import "Anchors"

function ChatInputPanel:Ctor()
    self._chatChannel = ChatChannelType.World
    --塞聊天记录
    self._wtNamedSlotContent = self:Wnd("DFCanvasPanel_199", UIWidgetBase)
    --塞表情/分享/语音动画
    self._wtNamedSlot = self:Wnd("NamedSlot_180", UIWidgetBase)
    --输入框
    -- self._wtEditTxtInput = self:Wnd("EditableTextBox_224", UIWidgetBase)
    -- self._wtEditTxtInput:Event("OnTextCommitted", self.OnEditTxtInputCommitted, self)
    -- self._wtEditTxtInput:Event("OnTextChanged", self.OnEditTxtInputChanged, self)
    self._wtEditTxtInput = UIUtil.WndInputBoxWithBtn(
        self,
        "WBP_InputBoxWithBtn",
        -1,
        nil,
        CreateCallBack(self.OnEditTxtInputChanged, self),
        CreateCallBack(self.OnEditTxtInputCommitted, self),
        nil,
        nil,
        nil
    )

    self._wtBtnMask = self:Wnd("DFButton_109", UIButton)
    self._wtBtnMask:Event("OnClicked",self._OnMaskBtnClick,self)
    -------

    --发送
    self._wtBtnSend = self:Wnd("wSendButton", UIButton)
    self._wtBtnSend:Event("OnClicked",self._OnSendBtnClick,self)

    --表情
    self._wtCheckBoxEmoji = self:Wnd("wEmojiCheckBox",DFCommonCheckButtonOnly)
    self._wtCheckBoxEmoji:Event("OnCheckedBoxStateChangedNative",self._EmojiCheckStateChanged,self)

    --分享
    self._wtCheckBoxShare = self:Wnd("wShareCheckBox",DFCommonCheckButtonOnly)
    self._wtCheckBoxShare:Event("OnCheckedBoxStateChangedNative",self._ShareCheckStateChanged,self)

    --语音
    self._wtBtnVoice = self:Wnd("wVioceButton",UIButton)
    if IsHD() then
        self._wtBtnVoice:Event("OnClicked",self._OnBtnVoiceClicked,self)
        self._wtHDVoiceSlot = self:Wnd("PCVoice_Slot",UIWidgetBase)
    else
        self._wtBtnVoice:Event("OnPressed",self._OnBtnVoicePressed,self)
        self._wtBtnVoice:Event("OnReleased",self._OnBtnVoiceReleased,self)
        self._wtBtnVoice.OnButtonMouseMove:Add(self._OnButtonMouseMove, self)
    end

    self._wtCanvasInput = self:Wnd("wCanvasPanel_Input", UIWidgetBase)

    self._startRecordTime = nil
    self._endRecordTime = 0
    self._recordDuration = 0


    if DFHD_LUA == 1 then
        self._maxChatLen = Module.Chat.Config.MAXCHATLEN_HD
    else
        self._maxChatLen = Module.Chat.Config.MAXCHATLEN
    end

    -- 此时是否处于录音冷却
    self.mIsVoiceCooling = false
    -- 记录emojiCheckBtn是否应该重置状态
    self.mShouldResetEmojiBtnState = false
end

--------------------------------------------------------------------------
--- 界面状态初始化、重置、销毁相关
--------------------------------------------------------------------------

function ChatInputPanel:GetActualEditInput(editInputInst)
    return editInputInst.wtEditableTextBox
end

function ChatInputPanel:OnInitExtraData(channel)
    self._chatChannel = channel
end

function ChatInputPanel:OnOpen()
--[[     self:AddListeners()
    self:SetContentBG()
    self:SetVoiceButton()
    -- if DFHD_LUA == 1 then
    --     self:FocusKeyboard()
    -- end

    self:_CheckShowModule() ]]

    if IsHD() and self._chatChannel ~= ChatChannelType.PrivateChatFriend and self._chatChannel ~= ChatChannelType.PrivateChatStanger then
        self:Elongation(true)
    else
        self:Elongation(false)
    end

    --聚焦到输入框
    self:GetActualEditInput(self._wtEditTxtInput):SetKeyboardFocus()
end

function ChatInputPanel:OnShowBegin()
    self:SetContentBG()
end

function ChatInputPanel:OnShow()
    self:GetActualEditInput(self._wtEditTxtInput):SetHintText("")
    self:AddListeners()
    --self:SetContentBG()
    self:SetVoiceButton()
    self:_CheckShowModule()
    -- body
    self:OnInputInitVisible()
    if IsHD() and self._chatChannel ~= ChatChannelType.PrivateChatFriend and self._chatChannel ~= ChatChannelType.PrivateChatStanger then
        self:Elongation(true)
    else
        self:Elongation(false)
    end
    self:GetActualEditInput(self._wtEditTxtInput):SetKeyboardFocus()
    self:AddButtonMouseMove_Mobile()
end

function ChatInputPanel:OnInputInitVisible()
    local isPrivateChannel = self._chatChannel == ChatChannelType.PrivateChatFriend
    isPrivateChannel = isPrivateChannel or self._chatChannel == ChatChannelType.PrivateChatStanger
    if isPrivateChannel then
        -- 这里补一条，有概率OnChatFriendChange事件invoke时机早于上面监听事件的时机
        local info = Server.FrontEndChatServer:GetChatPlayerInfo()
        self:HideInput(not info)
    end
end

function ChatInputPanel:OnHide()
    self:ForceRemoveVoiceRecord()
    self:ClearNamedSlot()
    self._wtBtnVoice.OnButtonMouseMove:Clear()
    self:RemoveAllLuaEvent()
    self:GetActualEditInput(self._wtEditTxtInput):SetHintText("")
    -- 补一个移除表情包界面的操作
    self:_CloseEmojiPanel()
end

function ChatInputPanel:OnClose()
    Facade.UIManager:ClearSubUIByParent(self,self._wtNamedSlot)
end

-- 检查功能模块是否解锁
function ChatInputPanel:_CheckShowModule()
    if Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSyetemSocialShare) == EFirstLockResult.Open then
        self._wtCheckBoxShare:Visible()
    else
        self._wtCheckBoxShare:Collapsed()
    end
    -- TODO 目前分享功能是没有的
    self._wtCheckBoxShare:Collapsed()
end

function ChatInputPanel:SetAnchor(uiIns)
    local commonAnchor = FAnchors()
    commonAnchor.Minimum = FVector2D(0, 0)
    commonAnchor.Maximum = FVector2D(1, 1)
    uiIns.Slot:SetAnchors(commonAnchor)
    uiIns.Slot:SetOffsets(FMargin(0, 0, 0, 0))
    --self.currentChildPage = uiIns
end

function ChatInputPanel:AddButtonMouseMove_Mobile()
    if IsHD() then
        return
    end

    self._wtBtnVoice.OnButtonMouseMove:Clear()
    self._wtBtnVoice.OnButtonMouseMove:Add(self._OnButtonMouseMove, self)
end

--------------------------------------------------------------------------
--- 事件监听、控件事件相关
--------------------------------------------------------------------------

function ChatInputPanel:AddListeners()
    self:AddLuaEvent(Module.Chat.Config.evtChatFriendModelChange, self.OnChatFriendChange, self)
    self:AddLuaEvent(Module.Chat.Config.evtRepeatSendLastMsg, self.OnRepeatSendLastMsg, self)
    self:AddLuaEvent(Module.Chat.Config.evtForbidInput, self.ForbidInput, self)
    self:AddLuaEvent(Module.Chat.Config.evtInputEmoji, self.OnInputEmoji, self)

    --语音
    self:AddLuaEvent(Module.Chat.Config.evtVoicePanelCDOver, self.OnVoicePanelCDOver, self)
    self:AddLuaEvent(Module.GVoice.Config.Events.evtGVoiceUploadVoiceFile, self.OnUploadVoiceFileComplete, self)
    self:AddLuaEvent(Module.GVoice.Config.Events.evtGVoiceStreamSpeechToText, self.OnRefreshSpeechTxt, self)

    --特殊功能,@ 复制
    self:AddLuaEvent(Module.Chat.Config.evtAtPlayer, self.OnAtPlayer, self)
    self:AddLuaEvent(Module.Chat.Config.evtCopyText, self.OnCopyText, self)

    self:AddLuaEvent(Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock,self._ShowModuleNtf,self)
    self:AddLuaEvent(Module.Chat.Config.evtChatVoiceRecordStart,self.OnChatVoiceRecordStart,self)
end

function ChatInputPanel:OnChatFriendChange(info)
    self:_CloseEmojiPanel()
    self:HideInput(not info)
end

--发送相同消息出去（+1跟风）
function ChatInputPanel:OnRepeatSendLastMsg(chatMsg)
    if not Module.Chat:CheckIfMinorChatAllowed() then
        return
    end
    if table.isempty(chatMsg) then
        return
    end
    chatMsg.text = string.gsub(chatMsg.text, '<Chat_At2>.-</>', function (word)
        return string.sub(word, 11,string.len(word)-3)
    end)
    if self._chatChannel == ChatChannelType.PrivateChatFriend or self._chatChannel == ChatChannelType.PrivateChatStanger then
        Server.FrontEndChatServer:ChatPrivateSend(Server.FrontEndChatServer:GetChatPlayerInfo().player_id,chatMsg.text,chatMsg.emoji_list)
    elseif self._chatChannel == ChatChannelType.TeamChat then
        Server.FrontEndChatServer:ChatTeamSend(chatMsg.text,chatMsg.emoji_list)
    elseif self._chatChannel == ChatChannelType.WorldChat then
        Server.FrontEndChatServer:ChatWorldSend(chatMsg.text,chatMsg.emoji_list)
    elseif self._chatChannel == ChatChannelType.CollegeChat then
        Server.FrontEndChatServer:ChatCollegeSend(chatMsg.text,chatMsg.emoji_list)
    end
end

function ChatInputPanel:ForbidInput(bForbid)
    if bForbid then
        self._wtBtnMask:Visible()
        self._wtCheckBoxEmoji:SetVisibility(ESlateVisibility.HitTestInvisible)
        if self._chatChannel == ChatChannelType.TeamChat then
            self:GetActualEditInput(self._wtEditTxtInput):SetHintText(Module.Chat.Config.Loc.ForbidInput)
        end
    else
        self._wtBtnMask:Collapsed()
        self._wtCheckBoxEmoji:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self:GetActualEditInput(self._wtEditTxtInput):SetHintText("")
    end
end

--添加表情
function ChatInputPanel:OnInputEmoji(emojiId)
    local msg = self:GetActualEditInput(self._wtEditTxtInput):GetText()
    local newMsg = msg .. emojiId
    local _,fixMsg = StringUtil.GetRealWidth(newMsg,self._maxChatLen)
    if newMsg ~= fixMsg then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ExceedChatMaxLen)
    else
        self:GetActualEditInput(self._wtEditTxtInput):SetText(newMsg)
        Module.Chat.Field:SetChatInput(newMsg)
    end
end

function ChatInputPanel:OnVoicePanelCDOver(recordTime)
    if IsHD() then
        self._recordDuration = recordTime
        logerror("Horis Chat Voice Start Upload ")
        Module.GVoice:UploadRecordedFile()
    else
        self._voiceUIins = nil
        self._wtBtnVoice:ActiveRelease()
    end
end

--上传GVoice服务器完成，把fileId发给我们的服务器
function ChatInputPanel:OnUploadVoiceFileComplete(bSuc, filePath, fileId)
    loginfo("Horis Chat Voice Upload Finish", bSuc, filePath, fileId)
    if not bSuc then
        return
    end
    self._fileId = fileId
    self:SendVoiceData()
end

function ChatInputPanel:OnRefreshSpeechTxt(text)
    loginfo("GVoice Horis Chat SpeechTxt Translate completed",text)
    self._voiceText = text
    self:SendVoiceData()
end

--添加@
function ChatInputPanel:OnAtPlayer(atMsg)
    local msg = self:GetActualEditInput(self._wtEditTxtInput):GetText()
    if not string.find(msg,atMsg,1,true) then
        --跟添加表情一样,直接塞进去
        self:OnInputEmoji(atMsg)
    end
end

--添加@
function ChatInputPanel:OnCopyText(copyText)
    self:GetActualEditInput(self._wtEditTxtInput):SetText(copyText)
    Module.Chat.Field:SetChatInput(copyText)
end

function ChatInputPanel:_ShowModuleNtf(moduleId, isUnLock)
    if moduleId==SwitchSystemID.SwitchSyetemSocialShare then
        if isUnLock then
            self._wtCheckBoxShare:Visible()
        else
            self._wtCheckBoxShare:Collapsed()
        end
        
    end
end

function ChatInputPanel:OnEditTxtInputCommitted(text, commitType)
    text = tostring(text)
    local _,fixText = StringUtil.GetRealWidth(text, self._maxChatLen)
    if fixText ~= text then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ExceedChatMaxLen)
    end
    self:GetActualEditInput(self._wtEditTxtInput):SetText(fixText)
    Module.Chat.Field:SetChatInput(fixText)
   if DFHD_LUA == 1 then
        if commitType == ETextCommit.OnEnter then
            if fixText == text then
                if text ~= "" then
                    self:_OnSendBtnClick()
                end
            end
            Timer.DelayCall(0.001, self.FocusKeyboard, self)
        end
    end
end

function ChatInputPanel:OnEditTxtInputChanged(text)
    text = tostring(text)
    local _,fixText = StringUtil.GetRealWidth(text, self._maxChatLen)
    if fixText ~= text then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ExceedChatMaxLen)
        self:GetActualEditInput(self._wtEditTxtInput):SetText(fixText)
    end

    Module.Chat.Field:SetChatInput(fixText)
end

function ChatInputPanel:_OnMaskBtnClick()
    Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ForbidInput)
end

--发送聊天内容出去
function ChatInputPanel:_OnSendBtnClick()
    if not Module.Chat:CheckIfMinorChatAllowed() then
        return
    end
    local msg = self:GetActualEditInput(self._wtEditTxtInput):GetText()
    -- 这里主要移除安卓输入法自带或操作系统自带的emoji表情包字符串
    msg = ChatLogic.RemoveUnofficialEmojiStr(msg)
    if string.len(msg) == 0 then
        msg = ""
        Module.Chat.Field:SetChatInput("")
    end

    if msg == "" or ChatLogic.InvalidMsgCheck(msg) then
        self:GetActualEditInput(self._wtEditTxtInput):SetText("")
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.EmptyInput)
        return
    end
    local emojiList = {}
    msg = string.gsub(msg, '%[@.-%]', function (word)
        if ChatField:CheckNickValid(word) then
            return '<Chat_At2>' .. word .. '</>'
        else
            return word
        end
    end)
    msg,emojiList = self:_CheckEmoji(msg)
    self:_CloseEmojiPanel()
    if self._chatChannel == ChatChannelType.PrivateChatFriend or self._chatChannel == ChatChannelType.PrivateChatStanger then
        local playerId = Module.Chat.Field.PrivateMsgViewModel:GetCurChatPlayerId()
        Server.FrontEndChatServer:ChatPrivateSend(playerId, msg, emojiList)
    elseif self._chatChannel == ChatChannelType.TeamChat then
        Server.FrontEndChatServer:ChatTeamSend(msg,emojiList)
    elseif self._chatChannel == ChatChannelType.WorldChat then
        Server.FrontEndChatServer:ChatWorldSend(msg,emojiList)
    elseif self._chatChannel == ChatChannelType.CollegeChat then
        Server.FrontEndChatServer:ChatCollegeSend(msg,emojiList)
    end
    self:GetActualEditInput(self._wtEditTxtInput):SetText("")
    Module.Chat.Field:SetChatInput("")
end

function ChatInputPanel:_EmojiCheckStateChanged(bIsChecked)
    if not bIsChecked then
        if self._EmojiPanel then
            self._EmojiPanel:Collapsed()
        end
        self.mShouldResetEmojiBtnState = false
        self._wtCheckBoxEmoji:SetIsChecked(false)
    else
        if self._EmojiPanel then
            self._EmojiPanel:Visible()
        else
            Facade.UIManager:RemoveSubUIByParent(self,self._wtNamedSlot)
            self:ClearNamedSlot()
            local uiIns = Facade.UIManager:AddSubUI(self, UIName2ID.ChatEmojiPanel, self._wtNamedSlot, nil,self,self._wtCheckBoxEmoji)
            self._EmojiPanel = getfromweak(uiIns)
            self._EmojiPanel:Visible()
        end
        self.mShouldResetEmojiBtnState = true
        self._wtCheckBoxEmoji:SetIsChecked(true)
    end
end

function ChatInputPanel:_ShareCheckStateChanged(bIsChecked)
    self:_CloseEmojiPanel()

    if true then
        self._wtCheckBoxShare:SetIsChecked(false)
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.StayTuned)
    else
        if not bIsChecked then
            if self._SharePanel then
                self._SharePanel:Collapsed()
            end
        else
            if self._SharePanel then
                self._wtNamedSlot:ClearChildren()
                self._wtNamedSlot:AddChild(self._SharePanel)
                self._SharePanel:Visible()
            else
                Facade.UIManager:RemoveSubUIByParent(self,self._wtNamedSlot)
                self:ClearNamedSlot()
                local uiIns = Facade.UIManager:AddSubUI(self,UIName2ID.ChatShare, self._wtNamedSlot)
                self._SharePanel = getfromweak(uiIns)
            end
        end
    end
end

function ChatInputPanel:_OnBtnVoiceClicked()
    -- 信用系统决定其是否能说语音
    if not Module.Chat:AllowVoiceUse() then
        return
    end

    if Module.GVoice:CheckMicrophoneAvailable(true, true) then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtHDVoiceSlot)
        Facade.UIManager:AddSubUI(self,UIName2ID.ChatVoice_PC, self._wtHDVoiceSlot)
        self._fileId = nil
        self._voiceText = nil
    end
end

--------------------------------------------------------------------------
--- 界面数据、具体的业务逻辑相关
--------------------------------------------------------------------------

function ChatInputPanel:SetVoiceButton()
    if Module.GVoice:IsEnable() then
        self._wtBtnVoice:Visible()
    else
        self._wtBtnVoice:Collapsed()
    end
end

--设置聊天背景 世界/私聊
function ChatInputPanel:SetContentBG()
    self:ForbidInput(false)
    local weakUiIns
    local uiIns
    Facade.UIManager:RemoveSubUIByParent(self, self._wtNamedSlotContent)
    if self._chatChannel == ChatChannelType.PrivateChatFriend or self._chatChannel == ChatChannelType.PrivateChatStanger then
        weakUiIns = Facade.UIManager:AddSubUI(self,UIName2ID.ChatPrivate, self._wtNamedSlotContent)
        uiIns = getfromweak(weakUiIns)
        local bHide = not Module.Chat.Field:GetNeedChatPlayerInfo()
        self:HideInput(bHide)
    elseif self._chatChannel == ChatChannelType.WorldChat then
        local weakUiIns = Facade.UIManager:AddSubUI(self,UIName2ID.ChatWorld, self._wtNamedSlotContent)
        uiIns = getfromweak(weakUiIns)
        -- 移除这里对UIInst的持有 防止内存不释放
        -- Module.Chat.Field:SetWorldChannelUIins(uiIns)
        self:HideInput(false)
    elseif self._chatChannel == ChatChannelType.CollegeChat then
        local weakUiIns = Facade.UIManager:AddSubUI(self,UIName2ID.ChatCollege, self._wtNamedSlotContent)
        uiIns = getfromweak(weakUiIns)
        self:HideInput(false)
    elseif self._chatChannel == ChatChannelType.TeamChat then
        weakUiIns = Facade.UIManager:AddSubUI(self,UIName2ID.ChatTeam, self._wtNamedSlotContent)
        uiIns = getfromweak(weakUiIns)
        self:HideInput(false)
    end

    self:SetAnchor(uiIns)
    self:GetActualEditInput(self._wtEditTxtInput):SetText(Module.Chat.Field:GetChatInput())
end

function ChatInputPanel:FocusKeyboard()
    self:GetActualEditInput(self._wtEditTxtInput):SetKeyboardFocus()
end

--检测其中的Emoji
function ChatInputPanel:_CheckEmoji(msg)
    local min,max = Module.Chat.Field:GetEmojiIdRange()
    local ret = ""
    local strList = StringUtil.StringSplit(msg,"%%")
    local emojiList = {}
    for index = 1,#strList do
        local str = strList[index]
        if index == 1 and string.sub(msg, 1, 1) ~="%" then
            ret = ret .. str
        else
            local numLen = 0
            for i = 1,#str do
                local charisnum = tonumber(string.sub(str, i, i))
                if charisnum then
                    local num = tonumber(string.sub(str, 1, i))
                    if num and num >= min and num <= max then
                        numLen = i
                    end
                end
            end
            if numLen > 0 then
                -- local emoji = '<dfmrichtext id="%' .. string.sub(str, 1, numLen) .. '"/>'
                -- local pos = string.len(ret)
                -- table.insert(emojiList, {emoji_content = emoji, position = pos})
                -- ret = ret .. string.sub(str, numLen + 1)
                local pos = string.len(ret)
                local indexStr = string.sub(str, 1, numLen)
                local emojiIndex = tonumber(indexStr)
                if ChatLogic.CheckEmojiValid(emojiIndex, pos) then
                    table.insert(emojiList, {emoji_id = emojiIndex, position = pos})
                end
                ret = ret .. string.sub(str, numLen + 1)
            else
                ret = ret .. str
            end
        end
    end

    return ret,emojiList
end

function ChatInputPanel:_CloseEmojiPanel()
    if not self.mShouldResetEmojiBtnState then
        return
    end

    self.mShouldResetEmojiBtnState = false
    self._wtCheckBoxEmoji:SetIsChecked(false)
    if self._EmojiPanel then
        self._EmojiPanel:Collapsed()
    end
end

--------------------------------------- 语音录音相关 ------------------------------------
function ChatInputPanel:ForceRemoveVoiceRecord()
    if self._voiceUIins then
        Facade.UIManager:CloseUI(self._voiceUIins)
    end

    self:RemoveVoiceRecordCool()
end

function ChatInputPanel:AddVoiceRecordCoolTimer()
    self.mIsVoiceCooling = true

    self._timerVoiceCoolHandle = Timer:NewIns(0.275, 1)  -- 动画时长为0.27s
    self._timerVoiceCoolHandle:AddListener(self.RemoveVoiceRecordCool, self)
    self._timerVoiceCoolHandle:Start()
end

function ChatInputPanel:RemoveVoiceRecordCool()
    if self._timerVoiceCoolHandle then
        self._timerVoiceCoolHandle:Release()
        self._timerVoiceCoolHandle = nil
    end

    self.mIsVoiceCooling = false
end

--语音输入
function ChatInputPanel:_OnBtnVoicePressed()
    -- 信用系统决定其是否能说语音
    if not Module.Chat:AllowVoiceUse() then
        return
    end

    if self.mIsVoiceCooling then
        return
    end
    self:AddVoiceRecordCoolTimer()

    self:_CloseEmojiPanel()
    self._wtCheckBoxShare:SetIsChecked(false)

    if Module.GVoice:CheckMicrophoneAvailable(true, true) then
        self._startRecordTime = nil

        Facade.UIManager:RemoveSubUIByParent(self,self._wtNamedSlot)
        self:ClearNamedSlot()
        local uiIns = Facade.UIManager:AddSubUI(self, UIName2ID.ChatVoicePanel, self._wtNamedSlot)
        self._voiceUIins = getfromweak(uiIns)
        self._fileId = nil
        self._voiceText = nil

        self._isUnderVoiceBtn = true
    end
end

function ChatInputPanel:_OnBtnVoiceReleased()
    --self._wtCheckBoxShare:SetIsChecked(false)
    if self._voiceUIins then
        Facade.UIManager:CloseUI(self._voiceUIins)
        self._voiceUIins = nil
    else
        return
    end

    if not self._startRecordTime then
        self._startRecordTime = os.clock()
    end
    self._endRecordTime = os.clock()
    local recordTime =  self._endRecordTime - self._startRecordTime
    -- 用完即弃置
    self._startRecordTime = nil
    Module.GVoice:StopRecording()
    if recordTime < Module.Chat.Config.MINRECORDTIME then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.VoiceTooShort)
        return
    end
    self._recordDuration = math.min(15, math.ceil(recordTime))

    if self._isUnderVoiceBtn then
        logerror("Horis Chat Voice Start Upload ")
        Module.GVoice:UploadRecordedFile()
    end
    self._isUnderVoiceBtn = false
end

function ChatInputPanel:_OnButtonMouseMove(geometry,mouseEvent)
    if not self._wtBtnVoice:IsPressed() then
        return
    end
    local sceenPos = mouseEvent:GetScreenSpacePosition()
    self._isUnderVoiceBtn = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    if self._voiceUIins then
        self._voiceUIins:ChangeState(self._isUnderVoiceBtn)
    end
end

function ChatInputPanel:SendVoiceData()
    if not Module.Chat:CheckIfMinorChatAllowed() then
        return
    end
    loginfo("Horis Chat Voice SendVoiceData",self._fileId, self._voiceText)
    local isCN = IsBuildRegionCN()
    -- 兼容下海外版，海外版不会有voiceText
    if isCN and (not self._fileId or not self._voiceText) then
        return
    elseif (not self._fileId) then
        return
    end

    if self._chatChannel == ChatChannelType.PrivateChatFriend or self._chatChannel == ChatChannelType.PrivateChatStanger then
        Server.FrontEndChatServer:ChatPrivateSend(Server.FrontEndChatServer:GetChatPlayerInfo().player_id,"",{},
        ChatMsgType.Voice,self._fileId,self._recordDuration,self._voiceText)
    elseif self._chatChannel == ChatChannelType.TeamChat then
        Server.FrontEndChatServer:ChatTeamSend("",{},ChatMsgType.Voice,self._fileId,self._recordDuration,self._voiceText)
    elseif self._chatChannel == ChatChannelType.WorldChat then
        Server.FrontEndChatServer:ChatWorldSend("",{},ChatMsgType.Voice,self._fileId,self._recordDuration,self._voiceText)
    elseif self._chatChannel == ChatChannelType.CollegeChat then
        Server.FrontEndChatServer:ChatCollegeSend("",{},ChatMsgType.Voice,self._fileId,self._recordDuration,self._voiceText)
    end
    self._fileId = nil
    self._voiceText = nil
end

function ChatInputPanel:OnChatVoiceRecordStart()
    self._startRecordTime = os.clock()
end

--end
-----------------------------------------------------------------------

function ChatInputPanel:HideInput(bHide)
    if bHide then
        self._wtCanvasInput:Collapsed()
    else
        self._wtCanvasInput:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function ChatInputPanel:ClearNamedSlot()
    self._EmojiPanel=nil
    self._SharePanel=nil
    self._voiceUIins=nil
end

return ChatInputPanel
