----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSPay)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class PayServer : ServerBase
local PayServer = class("PayServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local PayRechargeItemStruct = require "DFM.Business.DataStruct.PayStruct.PayRechargeItemStruct"
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local Json = JsonFactory.createJson()

function PayServer:Ctor()
    self.Events = {
        --服务器物品信息变化
        evtRechargeDataChanged = LuaEvent:NewIns("PayServer.evtRechargeDataChanged"),
        --服务器下单失败，返回错误码
        evtOnPayServerError = LuaEvent:NewIns("PayServer.evtOnPayServerError"),
        --物品档位信息变化
        evtProductInfoChanged = LuaEvent:NewIns("PayServer.evtProductInfoChanged"),
        --支付柔性限制应答
        evtPayQueryMidasRiskInfoLimitRes = LuaEvent:NewIns("PayServer.evtPayQueryMidasRiskInfoLimitRes"),
    }

    --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    --从midas获取到了商品的具体信息
    if IsConsole() then
        self.Events.evtOnGetProductInfo = LuaEvent:NewIns("PayServer.evtOnGetProductInfo")
        self.Events.evtOnFirstEnterModeHall = LuaEvent:NewIns("PayServer.evtOnFirstEnterModeHall")
	end
    --- END MODIFICATION

    self._offer_id = ""
    --地区代码 参考 ISO 3166
    self._region_code = nil
    --货币代码 参考 ISO 4217
    self._currency_code = nil
    --服务器是否开了充值
    self._isServerEnable = false
    --充值档位信息
    self._rechargeItemList = {}
    --其他产品档位信息
    self._productItemList = {}

    self._registration_region = ""
    self._registration_numericCode = nil

    --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    --第一次启动调用补发货
    if IsConsole() then
        self._shouldReapplyReceipt = true
    end

    if IsPS5Family() then
        self._bIsPSShopEmpty = false
    end
	--- END MODIFICATION

    Facade.ProtoManager:AddNtfListener("CSUpdatePlayerBalanceNtf", self.OnCSUpdatePlayerBalanceNtf, self)
end

function PayServer:OnInitServer()

end

function PayServer:OnDestroyServer()

end

--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
function PayServer:OnGameFlowChangeEnter(gameFlowType)
    -- XSX版本第一次进入模式选择界面的时候调用补发货
    if IsConsole() and gameFlowType == EGameFlowStageType.ModeHall and self._shouldReapplyReceipt then
        self._shouldReapplyReceipt = false
        self.Events.evtOnFirstEnterModeHall:Invoke()
        self:FetchServerData()
    end
end

function PayServer:UpdatePSShopState(bIsEmpty)
    if not IsPS5Family() then
        return
    end
    self._bIsPSShopEmpty = bIsEmpty
end

function PayServer:IsPS5ShopEmpty()
    if not IsPS5Family() then
        return
    end
    return self._bIsPSShopEmpty
end
--- END MODIFICATION

function PayServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function PayServer:OnLoadingGame2Frontend(gameFlowType)
    self:FetchServerData()
end

function PayServer:FetchServerData()
    self:ReqRecharegeItemList()
end

--支付功能是否可用
function PayServer:IsEnable()
    --服务器是否开了充值
    if not self._isServerEnable then
        return false
    end
    return true
end

--- Centauri是否可用(海外和steam使用)
function PayServer:IsCentauriEnable()
    --海外
    if self:IsGoogleEnable() then
        -- 海外开启google的时候就不用Centauri
        return false
    elseif IsBuildRegionGlobal() then
        return true
        --国内steam
    else
        if VersionUtil.IsGameChannelSteam() then
            return true
        end
    end
    return false
end

--- Centauri是否可用(海外和steam使用)
function PayServer:IsGarenaEnable()
    if IsBuildRegionGA() then
        return true
    end
    return false
end

function PayServer:ReqVerifySteamDlcOwnership(steamId, appIdList)
    local req = pb.CSShopVerifyDLCOwnershipNtf:New()
    req.steam_id = steamId
    req.dlc_app_id_list = appIdList
    req:Request()
end

function PayServer:ReqBuyGoods(goodsInfo, callback)
    --日韩未成年
    local need_riskctl_check = (Server.SDKInfoServer:GetPlayerAdultState() <= 0) and (Server.SDKInfoServer:IsRegionJapan() or Server.SDKInfoServer:IsRegionKorea())
    local req = pb.CSBuyGoodsReq:New()
    req.product_id = goodsInfo.product_id
    req.quantity = goodsInfo.num
    req.discount = goodsInfo.discount
    req.delta = goodsInfo.delta
    req.provide_midas_appid = goodsInfo.business_id
    req.pay_channel = self:GetPayChannel()
    req.need_riskctl_check = need_riskctl_check
    req:Request(callback)
end

function PayServer:ReqAccountUpdatePayToken()
    local callback = function (res)
        loginfo("PayServer ReqAccountUpdatePayToken res result:", res.result)
    end

    if self._region_code == nil or self._region_code == "" then
        local overrideCurrency
        self._region_code, overrideCurrency = Server.SDKInfoServer:GetRegionCodeByCurrencyCode(self._currency_code)
        -- 这里如果有override，要么是regionCode没有对应的配置 要么是currencyCode也是空的
        if overrideCurrency then
            logerror("PayServer ReqAccountUpdatePayToken Region not found, fallback to US/USD")
            self._currency_code = overrideCurrency
        end
    end
    local req = pb.CSAccountUpdatePayTokenReq:New()
    req.token = self:GetPayToken()

    logerror("PayServer ReqAccountUpdatePayToken ", req.token.region, " ", req.token.currency_type)
    logerror("PayServer ReqAccountUpdatePayToken self _region_code", self._region_code)
    logerror("PayServer ReqAccountUpdatePayToken self _currency_code", self._currency_code)
    req:Request(callback)
end

function PayServer:GetPayToken()
    local openId = Server.SDKInfoServer:GetOpenIdStr()
    local openKey = ""
    local sessionId = ""
    local sessionType = ""
    local pf = Server.SDKInfoServer:GetPF()
    local pfkey = Server.SDKInfoServer:GetPFKey()
    local payChannel = self:GetPayChannel()
    local subChannel = "1"
    local regionCode = self._region_code or self:GetRegistrationRegion()
    local currencyCode = self._currency_code or self:GetRegistrationCurrencyCode()
    if IsBuildRegionGlobal() then
        if IsHD() and not self:IsGoogleEnable() then
            -- Global PC 强制使用归属区 Global iOS/Google 使用商店地区 _region_code
            regionCode = self:GetRegistrationRegion()
            currencyCode = self:GetRegistrationCurrencyCode()
        end
    end
    local cancelUrl = nil
    local returnUrl = nil
    local channelId = Server.SDKInfoServer:GetChannel()

    if self:IsCentauriEnable() or self:IsGoogleEnable() then
        openKey = "hy_openkey"
        sessionId = "hy_gameid"
        sessionType = "st_dummy"
    elseif self:IsGarenaEnable() then
        openKey = Server.SDKInfoServer:GetToken()
        sessionId = "hy_gameid"
        sessionType = "st_dummy"
    else
        if channelId == EChannelType.kChannelWechat then
            openKey = Server.SDKInfoServer:GetToken()
            sessionId = "itopid"
            sessionType = "itop"
        elseif channelId == EChannelType.kChannelQQ then
            openKey = Server.SDKInfoServer:GetPayToken()
            sessionId = "itopid"
            sessionType = "itop"
        else --guest
            openKey = "openKey"
            sessionId = "hy_gameid"
            sessionType = "st_dummy"
        end
    end

    local token = {
        openid = openId,
        openkey = openKey,
        session_id = sessionId,
        session_type = sessionType,
        pf = pf,
        pfkey = pfkey,
        reason = nil,
        pay_channel = payChannel,
        sub_channel = subChannel,
        region = regionCode,
        currency_type = currencyCode,
        cancel_url = cancelUrl,
        return_url = returnUrl,
    }

    return token
end

function PayServer:GetPayChannel()
    local payChannel = ""
    if self:IsGoogleEnable() then
        payChannel = "gwallet"
    elseif self:IsCentauriEnable() then
        if PLATFORM_ANDROID then
            payChannel = "gwallet"
            if IsBuildRegionGlobal() and IsChannelStoreAPK() then
                local payChannelMap = {
                    [10002] = "os_samsung",
                    [10003] = "os_huawei",
                    [10004] = "os_xiaomi",
                    [10005] = "os_oppo",
                    [10006] = "os_vivo",
                }
                
                local StoreChannel = Server.SDKInfoServer:GetStoreChannel()
                if StoreChannel ~= nil and payChannelMap[StoreChannel] ~= nil then
                    payChannel = payChannelMap[StoreChannel]
                end
            end
        elseif PLATFORM_IOS then
            payChannel = "iap"
        elseif PLATFORM_WINDOWS then
            if VersionUtil.IsGameChannelOfficial() then
                payChannel = "os_midaspay"
            elseif VersionUtil.IsGameChannelWeGame() then
                payChannel = "os_midaspay"
            elseif VersionUtil.IsGameChannelSteam() then
                payChannel = "os_steam"
            elseif VersionUtil.IsGameChannelEpic() then
                payChannel = "os_midaspay"
            else
                payChannel = "os_midaspay"
            end
        --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
        elseif IsXboxSeries() then
            payChannel = "os_microsoftstore"
        elseif IsPS5Family() then
            payChannel = "os_ps5"
        --- END MODIFICATION
        end
    else
        if PLATFORM_IOS then
            payChannel = "iap"
        end
    end
    return payChannel
end

--请求所有充值档位信息
function PayServer:ReqRecharegeItemList()
    local fOnCSGetRechargeConfigRes = function(res)
        if res.result == 0 then
            logerror("PayServer:ReqRecharegeItemList")
            logtable(res)
            self._isServerEnable = res.is_open
            local allRechargeItemNum = {}
            for k, v in ipairs(res.recharge_list) do
                allRechargeItemNum[v.product_id] = v.balance
            end

            for k, v in ipairs(res.recharge_list) do
                if self._rechargeItemList[v.product_id] then
                    self._rechargeItemList[v.product_id]:RefreshServerInfo(v)
                    self._rechargeItemList[v.product_id]:ParsePresent(v.present_rule, allRechargeItemNum)
                else
                    self._rechargeItemList[v.product_id] = PayRechargeItemStruct:New(v)
                    self._rechargeItemList[v.product_id]:ParsePresent(v.present_rule, allRechargeItemNum)
                end
            end
            self.Events.evtRechargeDataChanged:Invoke()
        else
            logerror("PayServer:ReqRecharegeItemList res.result:" .. tostring(res.result))
        end
    end
    local req = pb.CSGetRechargeConfigReq:New()
    --req:Request(fOnCSGetRechargeConfigRes)
    local channel = Server.SDKInfoServer:GetChannel() or EChannelType.kChannelNone
    if channel == EChannelType.kChannelNone then
        req:Request(fOnCSGetRechargeConfigRes, { whiteListErrorShow = { Err.PayCallMidasFailed } })
    else
        req:Request(fOnCSGetRechargeConfigRes)
    end
end

--获取所有充值档位信息
function PayServer:GetRechargeItemList()
    local list = table.tolist(self._rechargeItemList)
    table.sort(list, function(a, b)
        return a.gear < b.gear
    end)
    return list
end

--通过productId获取充值档位信息
function PayServer:GetRechargeItem(productId)
    if IsBuildRegionGA() then
        --GA直购查询时去掉前缀
        logerror("PayServer:GetRechargeItem Garena Remove Prefix", productId, productId:gsub("^garena.df.dc.", ""))
        productId = productId:gsub("^garena.df.dc.", "")
    end
    if productId and self._rechargeItemList then
        return self._rechargeItemList[productId]
    end
    return nil
end

--通过productId获取产品信息
function PayServer:GetProductItem(productId)
    if IsBuildRegionGA() then
        --GA直购查询时去掉前缀
        logerror("PayServer:GetProductItem Garena Remove Prefix", productId, productId:gsub("^garena.df.dc.", ""))
        productId = productId:gsub("^garena.df.dc.", "")
    end
    if productId and self._productItemList then
        return self._productItemList[productId]
    end
    return nil
end

--刷新组件中的物品信息
function PayServer:RefreshProductInfo(productInfoList)
    if productInfoList == nil then
        logerror("PayServer:RefreshProductInfo productInfoList is nil")
        return
    end

    local region_code = ""
    local currency_code = ""

    for _, productInfo in pairs(productInfoList) do
        if IsBuildRegionGA() then
            local id = ""
            if PLATFORM_ANDROID then
                id = productInfo.id
                currency_code = productInfo.priceCode
            elseif PLATFORM_IOS then
                id = tostring(productInfo.item_id)
                currency_code = productInfo.currency
            end
            if string.isempty(id) then
                logerror("PayServer:RefreshProductInfo GA id is empty")
            else
                if self._rechargeItemList[id] then
                    logerror("PayServer:RefreshProductInfo GA rechargeItem id:", id)
                    self._rechargeItemList[id]:RefreshGAProductInfo(productInfo)
                else
                    logerror("PayServer:RefreshProductInfo GA productItem id:", id)
                    if self._productItemList[id] then
                        self._productItemList[id]:RefreshGAProductInfo(productInfo)
                    else
                        local fakeServerInfo = { product_id = id }
                        self._productItemList[id] = PayRechargeItemStruct:New(fakeServerInfo)
                        self._productItemList[id]:RefreshGAProductInfo(productInfo)
                    end
                end
            end
            
        else
            if string.isempty(productInfo.unified_product_id) then
                logerror("PayServer:RefreshProductInfo Global unified_product_id is empty")
            else
                if self._rechargeItemList[productInfo.unified_product_id] then
                    logerror("PayServer:RefreshProductInfo Global rechargeItem unified_product_id:", productInfo.unified_product_id)
                    self._rechargeItemList[productInfo.unified_product_id]:RefreshProductInfo(productInfo)
                else
                    logerror("PayServer:RefreshProductInfo Global productItem unified_product_id:", productInfo.unified_product_id)
                    if self._productItemList[productInfo.unified_product_id] then
                        self._productItemList[productInfo.unified_product_id]:RefreshProductInfo(productInfo)
                    else
                        local fakeServerInfo = { product_id = productInfo.unified_product_id }
                        self._productItemList[productInfo.unified_product_id] = PayRechargeItemStruct:New(fakeServerInfo)
                        self._productItemList[productInfo.unified_product_id]:RefreshProductInfo(productInfo)
                    end
                end
            end
            region_code = productInfo.region_code
            currency_code = productInfo.currency_code
            logerror("PayServer:RefreshProductInfo Global region_code ", region_code)
            logerror("PayServer:RefreshProductInfo Global currency_code ", currency_code)

        end

    end

    self._region_code = region_code
    self._currency_code = currency_code

    if PLATFORM_WINDOWS then
        self._registration_region = region_code
        self._registration_currencyCode = currency_code
    end

    self:ReqAccountUpdatePayToken()

--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    if IsConsole() then
        self.Events.evtOnGetProductInfo:Invoke()
    end
--- END MODIFICATION

    self.Events.evtProductInfoChanged:Invoke()
end

function PayServer:SetOfferId(offerId)
    self._offer_id = offerId
    logerror("PayServer SetOfferId offerId:", offerId)
end

function PayServer:GetOfferId()
    return self._offer_id
end

function PayServer:ReqAccountUpdateRiskctlInfo(age)
    local callback = function (res)
        loginfo("PayServer ReqAccountUpdateRiskctlInfo res result:", res.result)
    end
    local req = pb.CSAccountUpdateRiskctlInfoReq:New()
    req.adult_status = Server.SDKInfoServer:GetPlayerAdultState()
    req.register_region = Server.SDKInfoServer:GetRegionCode()
    req.player_age = age or 0
    req:Request(callback)
end

function PayServer:OnCSUpdatePlayerBalanceNtf(ntf)
    if ntf.reason == UpdatePlayerBalanceReason.UpdateBalanceRechargeCurrency then
        Server.CurrencyServer:ReqAllCurrencyData()
    end
end

function PayServer:GetRegistrationRegion()
    return self._registration_region or ""
end

function PayServer:GetRegistrationnumericCode()
    return self._registration_numericCode or ""
end

function PayServer:GetRegistrationCurrencyCode()
    return self._registration_currencyCode or ""
end

function PayServer:GetCurrencyCode()
    return self._currency_code
end

function PayServer:SetRegistrationRegion(regionNumericCode)
    if not regionNumericCode then
        logerror("[PayServer] SetRegistrationRegion regionNumericCode is nil")
        return
    end
    local regionNumericCodeStr = string.format("%03d", regionNumericCode)
    logerror("[PayServer] SetRegistrationRegion regionNumericCode:", regionNumericCode, "regionNumericCodeStr:", regionNumericCodeStr)
    if string.isempty(regionNumericCodeStr) then
        logerror("[PayServer] SetRegistrationRegion regionNumericCodeStr is nil")
        return
    end
    local dt = Facade.TableManager:GetTable("RegionCode")
    if dt then
        local regionCodeConfig = dt[regionNumericCodeStr]
        if regionCodeConfig then
            self._registration_region = regionCodeConfig.RegionCode
            self._registration_numericCode = regionCodeConfig.NumericCode
            self._registration_currencyCode = regionCodeConfig.CurrencyCode
            local currencyCodes = regionCodeConfig.CurrencyCodes
            local payChannel = self:GetPayChannel()
            if currencyCodes then
                local currencyCode = currencyCodes:Get(payChannel)
                --- BEGIN MODIFICATION @ VIRTUOS: 只有配置了特殊货币格式的渠道才重载
                if currencyCode then
                    self._registration_currencyCode = currencyCode
                    loginfo("[PayServer] SetRegistrationRegion registration_currencyCode payChannel:", payChannel, "currencyCode:", currencyCode)
                else
                    logwarning("[PayServer] SetRegistrationRegion registration_currencyCode payChannel:", payChannel, "is not config")
                end
                --- END MODIFICATION
            end
            logerror("[PayServer] SetRegistrationRegion regionCode:", self._registration_region)
            logerror("[PayServer] SetRegistrationRegion regionnumericCode:", self._registration_numericCode)
            logerror("[PayServer] SetRegistrationRegion registration_currencyCode:", self._registration_currencyCode)
        else
            logerror("[PayServer] SetRegistrationRegion regionCodeConfig is nil")
        end
    else
        logerror("[PayServer] SetRegistrationRegion dt is nil")
    end
end

function PayServer:IsGoogleEnable()
    local ChannelId = Server.SDKInfoServer:GetChannel()
    return IsHD() and (ChannelId == EChannelType.kChannelGooglePlay or ChannelId == EChannelType.kChannelGooglePGS)
end

function PayServer:FillClientGwalletInfo(gwallet_info)
    local language = LocalizeTool.GetCurrentCulture()
    -- 语言特殊处理
    if language == 'zh' then
        language = 'zh-Hans' -- 需要和Intl控制台对应
    end

    if REGION_CN then
        language = 'zh-CN' -- 国服定死zh-CN
    end

    gwallet_info.req_from = IsHD() and ClientGwalletReqType.From_Pc or ClientGwalletReqType.From_Mobile
    gwallet_info.user_ip = "" -- 客户端获取不到自己的公网ip，这个字段要服务器来填
    gwallet_info.language_code = language
    gwallet_info.user_access_token = Server.SDKInfoServer:GetRefreshToken()
    logerror("PayServer:FillClientGwalletInfo()")
    logtable(gwallet_info,true)
end

function PayServer:ReqUpdateGwalletInfo()
    local req = pb.CSAccountUpdateGwalletInfoReq:New()
    req.gwallet_info = req.gwallet_info or pb.ClientGwalletInfo:New()
    self:FillClientGwalletInfo(req.gwallet_info)

    local fOnCSAccountUpdateGwalletInfoRes = function(res)
        if res.result == 0 then
            log("PayServer:ReqUpdateGwalletInfo success")
        else
            logerror("PayServer:ReqUpdateGwalletInfo res.result:" .. tostring(res.result))
        end
    end
    req:Request(fOnCSAccountUpdateGwalletInfoRes)
    logerror("PayServer:ReqUpdateGwalletInfo()")
end

function PayServer:GetMidasbuyURL(region_code)
    local dt = Facade.TableManager:GetTable("MidasbuyURLConfig")
    for _, value in pairs(dt) do
        if string.lower(region_code) == string.lower(value.RegionCode) then
            return value.URLString
        end
    end
    logerror("PayServer:GetMidasbuyURL region is not configured, using KG ", region_code)
    return "KG"
end

function PayServer:ReqGetProductInfo(productIdList, fOnCSPayQueryRechargeGoodListRes)
    local req = pb.CSPayQueryRechargeGoodListReq:New()
    req.product_id_list = productIdList

    local fOnCSPayQueryRechargeGoodListRes = function(res)
        if res.result == 0 then
            logerror("PayServer:ReqGetProductInfo success")
            local productInfoList = {}      
            for _, MidasRechargeProductItem in pairs(res.product_list) do
                local productInfo = {}
                productInfo.unified_product_id = MidasRechargeProductItem.product_id
                --渠道物品ID
                productInfo.platform_product_id = MidasRechargeProductItem.channel_product_id
                --物品名称
                productInfo.product_name = MidasRechargeProductItem.product_name
                --这里的地区代码不准确
                ----地区代码 参考 ISO 3166
                --productInfo.region_code = MidasRechargeProductItem.region
                
                local channel_info = MidasRechargeProductItem.channel_info_return
                if nil == channel_info then
                    logerror("PayServer:ReqGetProductInfo channel info is nil!")
                else
                    --货币代码 参考 ISO 4217
                    productInfo.currency_code = channel_info.local_currency
                    --原始价格
                    productInfo.original_price = channel_info.local_price_micros
                    --当前带币种符号价格
                    productInfo.current_price = channel_info.local_price_micros
                    --展示价格, 不同平台个性化展示 	android:USD＄89.99 ios、xbox、switch、steam:＄89.99 epic、ps:89.99USD
                    productInfo.display_price = channel_info.local_formatted_price
                end
                --原价倍率 科学计数法
                productInfo.decimal_point  = 6

                table.insert(productInfoList, productInfo)
            end
            Server.PayServer:RefreshProductInfo(productInfoList)
        else
            logerror("PayServer:ReqGetProductInfo res.result:" .. tostring(res.result))
        end
    end
    req:Request(fOnCSPayQueryRechargeGoodListRes)
    logerror("PayServer:ReqGetProductInfo()")
end

function PayServer:ReqMidasReprovide(callback)
    local req = pb.CSPayDoMidasReprovideReq:New()
    req:Request(callback)
    logerror("PayServer:ReqMidasReprovide()")
end

function PayServer:ReqPayQueryMidasRiskInfo(source, fCallbackContinue, fCallbackLimit)
    local req = pb.CSPayQueryMidasRiskInfoReq:New()
    req.source = source
    logerror("PayServer:ReqPayQueryMidasRiskInfo() source", source)
    local fPayQueryMidasRiskInfoRes = function(res)
        -- 测试用 
        --res.result = 0
        --res.level = 0
        --res.SOLNeedNum = 5
        --res.MPNeedNum = 3
        --res.relation = 1
        --res.SOLMatchNum = 3
        --res.MPMatchNum = 1
        
        if res.result == 0 then
            logerror("PayServer:ReqPayQueryMidasRiskInfo success level", res.level, " SOLNeedNum", res.SOLNeedNum, " MPNeedNum", res.MPNeedNum, " relation", res.relation, " SOLMatchNum", res.SOLMatchNum, " MPMatchNum", res.MPMatchNum)
            if res.level <= 0 then
                logerror("PayServer:ReqPayQueryMidasRiskInfo level <= 0 level", res.level)
                if fCallbackContinue then
                    fCallbackContinue()
                end
            else
                logerror("PayServer:ReqPayQueryMidasRiskInfo level > 0 level", res.level)
                Server.PayServer.Events.evtPayQueryMidasRiskInfoLimitRes:Invoke(res)
                if fCallbackLimit then
                    fCallbackLimit()
                end
            end
        else
            logerror("PayServer:ReqPayQueryMidasRiskInfo res.result:" .. tostring(res.result))
            if fCallbackContinue then
                fCallbackContinue()
            end
        end
    end
    req:Request(fPayQueryMidasRiskInfoRes)
end

return PayServer