----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class StaffLotteryPreview : LuaUIBaseView
local StaffLotteryPreview = ui("StaffLotteryPreview")
local StoreConfig = Module.Store.Config
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local HeroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData")
local headIconTbl = Facade.TableManager:GetTable("SocialAvatarDataTable")
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local EGPInputType = import  "EGPInputType"
local UIThemeUtil = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"

function StaffLotteryPreview:Ctor()
    self._wtBackgroundImg = self:Wnd("DFImage_191", UIImage) -- 背景图
    
    self._wtCountWidget = self:Wnd("wtStoreCountDown", UIWidgetBase) -- 奖池的时间信息
    if self._wtCountWidget ~= nil then
        self._wtTextCountDown = self._wtCountWidget:Wnd("wtTextCountDown", UITextBlock)
    end

    self._wtTitleImg = self:Wnd("DFImage_Title", UIImage) -- 文字图片，调用一个蓝图函数，传奖池id过去，然后需要换图时，重构同学修改蓝图就可以

    self._wtRewardNameTxt = self:Wnd("wtTextWeaponName", UITextBlock) -- 奖品名称

    self._wtQualityIconImg = self:Wnd("wtQualityIcon", UIImage) -- 品质图标

    self._wtRewardDescTxt = self:Wnd("wtWeaponDesc", UITextBlock) -- 奖品描述信息

    self._wtAccessoriesPreviewBtn = self:Wnd("wtButtonItemPreview", DFCommonButtonOnly) -- 配件预览按钮
    self._wtAccessoriesPreviewBtn:Event("OnClicked", self._OnAccessoriesPreviewbtnClick, self)
    -- self._wtAccessoriesPreviewShowdow = self:Wnd("DFThemeShadowImage", UIImage)
    --UIThemeUtil.CheckIfAutoApplyTheme(self._wtAccessoriesPreviewShowdow)

    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtWaterfallView", self._OnGetItemCount, self._OnProcessItemWidget) -- 奖品列表
    
    ---@type StaffLotteryVoice
    self._wtStaffLotteryVoiceWidget = self:Wnd("WBP_StaffLottery_Voice", UIWidgetBase) -- 语音播放控件
    
    self._wtWeaponSkinPreviewBtn = self:Wnd("wtButtonPreview", DFCommonButtonOnly) -- 武器皮肤预览按钮
    self._wtWeaponSkinPreviewBtn:Event("OnClicked", self._OnWeaponSkinPreviewBtnClicked, self)

    self._wtPlatformPaddingBox = self:Wnd("PlatformPaddingBox_1", UIWidgetBase) -- 奖励详情根节点
    
    self._wtImageSparyRoot = self:Wnd("DFScaleBox_Spray", UIWidgetBase)
    self.wtImageSpray = self:Wnd("DFImage_Spray", UIImage)
    self.wtImageSpray:Collapsed()

	self._videoBG = self:Wnd("WBP_Common_ScaleBg", UIWidgetBase)
    
    --视频专用
    ---@type CommonVideoComponent
    self._mediaComponent = self:Wnd("WBP_CommonVideoComponent", CommonVideoComponent)
    self._mediaComponent:InitComponent(false)

    Module.Store.Field:InitResourceCommercializationTable() -- 初始化语音表

    -- 返回按钮
    Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.StaffLotteryPreviewRewardTitle)
    -- 隐藏货币栏    
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    
    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)

    self.AllWidgetList = {}

    self.curSelectedItemId = 0

    -- Module.Store:SetCanPlayMusic(true)

    --download
    self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)
    self:_SetDownloadWidgetVisible(false)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self._wtButtonRoot = self:Wnd("DFVerticalBox_47", UIWidgetBase)
        self._RotationVec = FVector2D(0, 0)
        self._WeaponRotationZoom = 600
        self._WeaponScaleZoom = 30
    end
    --- END MODIFICATION
end

function StaffLotteryPreview:_OnGetItemCount()
    return #self.LotteryProbDistributionBySortIndex
end

function StaffLotteryPreview:_OnProcessItemWidget(position, itemWidget)
    local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[position]
    if not LotteryProbDistribution then
        logerror("StaffLotteryPreview:_OnProcessItemWidget: LotteryProbDistribution is nil at position " .. tostring(position))
        return
    end
    local goodInfo = {id = LotteryProbDistribution.SpecialItem ~= 0 and LotteryProbDistribution.SpecialItem or tonumber(LotteryProbDistribution.RewardItem), count = 1}
    
    local bOwned = false -- 是否已拥有
    local IDs = {}
    for i = 1, #self.LotteryProbDistributionBySortIndex, 1 do
        table.insert(IDs, self.LotteryProbDistributionBySortIndex[i].SortIndex)
    end

    for j = 1, #self.propNumIds do  
        if IDs[position] == self.propNumIds[j] then -- 说明已拥有
            bOwned = true
            break
        end
    end

    itemWidget:SetItemInfo(position, goodInfo, self.currentSelectedRewardIndex, nil, nil, bOwned)
    itemWidget:SetSelected(self.currentSelectedRewardIndex == position)
    self.AllWidgetList[position] = itemWidget
    if self.currentSelectedRewardIndex == position then
        if self.AllWidgetList[position].itemID == self.curSelectedItemId then
            return
        end
        if self.currentSelectedRewardIndex then
            self.curSelectedItemId = self.AllWidgetList[position].itemID
        end
    end
    
end

-- customParms:如有，则显示的内容直接以customParms内为准
-- {rewards = ...(list)StoreLotteryProbDistributionItem , heroId = ...}
function StaffLotteryPreview:OnInitExtraData(currentLotteryId, lotteryPoolInfo, customParms, isCollaboration)
    self.LotteryProbDistributionBySortIndex = {}

    if customParms then
        for index, LotteryProbDistribution in pairs(customParms.rewards) do    
            self.LotteryProbDistributionBySortIndex[LotteryProbDistribution.SortIndex] = LotteryProbDistribution
        end
        self.belongedHeroID = customParms.heroId
        self.lotteryPoolInfo = nil
        self.propNumIds = {}
        self.rewardProps = nil
        self._IsCustomMode = true
        self.currentLotteryId = nil
        self._currentLotteryInfo = nil
        self.LotteryProbDistributionTable = nil
    else
        self.currentLotteryId = currentLotteryId
        -- 数据
        self._currentLotteryInfo, self.LotteryProbDistributionTable = Module.Store.Field:GetShopLotteryDataByLotteryID(self.currentLotteryId)
        
        self.propNumIds = setdefault(self.propNumIds, {})
        for index, LotteryProbDistribution in ipairs(self.LotteryProbDistributionTable) do    
            self.LotteryProbDistributionBySortIndex[LotteryProbDistribution.SortIndex] = LotteryProbDistribution
        end
        if lotteryPoolInfo then
            self.lotteryPoolInfo = lotteryPoolInfo
            self.propNumIds = lotteryPoolInfo.prop_num_ids -- 在该奖池抽奖过的奖品序列id列表
        end
        self.rewardProps = Module.Store.Field:GetRewardProps() -- 奖品道具信息d
        self._IsCustomMode = false
    end

    self.currentSelectedRewardIndex = 1 -- 当前选中奖品的index，默认选中第一个

    --将获得的放在后排
    local commonElems = {}
    local otherElems = {}

    for index, LotteryProbDistribution in pairs(self.LotteryProbDistributionBySortIndex) do
        if self:IsOwned(self.propNumIds, index) then
            table.insert(commonElems, LotteryProbDistribution)
        else
            table.insert(otherElems, LotteryProbDistribution)
        end
    end

    self.LotteryProbDistributionBySortIndex = otherElems
    for index, value in ipairs(commonElems) do
        table.insert(self.LotteryProbDistributionBySortIndex, value)
    end

    self.Type = 5


    self._wtCountWidget:Collapsed() --默认不显示倒计时

    if isCollaboration then
        local topTopBarGroupRegInfo = {
            stackDefaultThemeID = EThemeIDType.CrossOver,
        }
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTopBarGroupRegInfo)
    else
        local topTopBarGroupRegInfo = {
            stackDefaultThemeID = EThemeIDType.NoTheme,
        }
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, topTopBarGroupRegInfo)
    end
        
end

function StaffLotteryPreview:IsOwned(tb, value)
    for index, v in ipairs(tb) do
        if value == v then
            return true
        end
    end
    return false
end


function StaffLotteryPreview:OnOpen()    
    -- 设置倒计时信息
    if self._currentLotteryInfo then
        self:RefreshRemainingTime()
        --1秒更新一次倒计时
        if not self._timerHandle then
            self._timerHandle = Timer:NewIns(1,0)
            self._timerHandle:AddListener(self.RefreshRemainingTime, self)
            self._timerHandle:Start()
        end
    end
    -- 先隐藏奖品详情根节点
    self._wtPlatformPaddingBox:Collapsed()

    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallWeaponShow)
end
function StaffLotteryPreview:OnClose()    
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function StaffLotteryPreview:OnShowBegin()    

    self:_SetDownloadWidgetVisible(false)

    self.SetCharacterSuitAvatarWithSequence = "None" -- 只有第一次是None，播放sequence；后面都是State2，显示角色的站姿
    -- 设置奖励列表数据
    self._wtWaterFallList:RefreshAllItems()
    self._wtWaterFallList:ScrollToIndexToScreen(self.currentSelectedRewardIndex, 0.5, 0.5) 

    -- 展示所选中的奖品信息
    -- 延迟一帧，不然刷不出来背景
    Timer.DelayCall(0, function()
        self:_ShowSelectedRewardInfo()
    end)

    if self._IsCustomMode then
        local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
        if LotteryProbDistribution and LotteryProbDistribution.CustomId then
            StoreLogic.SetLotteryUIArtLogo(LotteryProbDistribution.CustomId, self._wtTitleImg , true)
        end
    else
        if not self._currentLotteryInfo then
            logerror("StaffLotteryPreview:OnShowBegin: _currentLotteryInfo is nil!!")
        else
            StoreLogic.SetLotteryUIArtLogo(self._currentLotteryInfo.LotteryId, self._wtTitleImg , true)
        end
    end
    
    self:AddLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick, self._OnRewardClicked, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._ModuleDownloadResult, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged, self._ModuleCheckResult, self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION

    if WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad then
        self.firstOpen = true
    end
end

function StaffLotteryPreview:OnShow()  
    --self:_ShowSelectedRewardInfo()  
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function StaffLotteryPreview:OnHideBegin()
    if IsHD() and WidgetUtil.IsGamepad() then
        self:_DisableGamepadFeature()
    end
end
--- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function StaffLotteryPreview:OnHide()
    Module.Store:SetCanPlayMusic(true)
    Facade.HallSceneManager:ResetRootActorOffset()--重新设置rootActor的偏移
    Module.Hero:ShowOperatorWatch("None")--删除手表

    self:RemoveLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick)

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")

    self.Type = 5 -- 默认为5
    self._wtBackgroundImg:Collapsed()
    self.DFScaleBox_Spray:Collapsed()        
    self._wtStaffLotteryVoiceWidget:Collapsed()
    self._videoBG:Collapsed()
    self._mediaComponent:Stop()
    self.AllWidgetList = {}
    self.curSelectedItemId = 0

    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged)
end

function StaffLotteryPreview:_OnRewardClicked(index, goodInfo, uiIns)

    if not goodInfo.id then
        logwarning("StaffLotteryPreview:_OnRewardClicked: goodInfo.id = nil!!")
        return
    end
    local itemData = ItemBase:New(goodInfo.id, goodInfo.num)
    self.currentSelectedRewardIndex = index

    if WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad and not self.firstOpen and self._wtAccessoriesPreviewBtn:IsVisible() then
        self:_OnAccessoriesPreviewbtnClick()
    end

    if self.firstOpen then
        self.firstOpen = false
    end
    
    -- 更新列表显示
    -- self._wtWaterFallList:RefreshAllItems()
    -- 展示所选中的奖品信息
    self:_ShowSelectedRewardInfo()
    -- 埋点
    local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
    if not LotteryProbDistribution then
        return
    end
    local itemId = LotteryProbDistribution.SpecialItem ~= 0 and LotteryProbDistribution.SpecialItem or LotteryProbDistribution.RewardItem
    self.curSelectedItemId = itemId
   
    --设置UI选择状态
    for key, value in pairs(self.AllWidgetList) do
        value:SetSelected(value and value.idx == index)
    end

    LogAnalysisTool.DoSendStoreViewPageReportLog(8, 0, 4, itemId)
end

-- 配件预览按钮点击事件
function StaffLotteryPreview:_OnAccessoriesPreviewbtnClick()    
    local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
    if not LotteryProbDistribution then
        return
    end
    -- 打开配件预览界面    
    Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, LotteryProbDistribution.SpecialItem, LotteryProbDistribution.LotteryId)
    Facade.UIManager:CommitTransition(true)
    -- 埋点
    LogAnalysisTool.SignButtonClicked(10154003)
end
-- 武器皮肤预览按钮点击事件
function StaffLotteryPreview:_OnWeaponSkinPreviewBtnClicked()
    -- 打开武器bundle预览界面    
    local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
    if not LotteryProbDistribution then
        return
    end

    if self._IsCustomMode  then
        if LotteryProbDistribution.CustomItemBase then
            local itemMainType = ItemHelperTool.GetMainTypeById(LotteryProbDistribution.CustomItemBase.id)
            if itemMainType == EItemType.Adapter then
                Module.Collection:ShowHangingDetailPage(LotteryProbDistribution.CustomItemBase)
            else
                Module.Collection:ShowWeaponSkinDetailPage(LotteryProbDistribution.CustomItemBase)
            end
        end
    else
        local itemId = LotteryProbDistribution.SpecialItem ~= 0 and LotteryProbDistribution.SpecialItem or LotteryProbDistribution.RewardItem
        local item = ItemBase:New(itemId)
        local props = self.rewardProps and self.rewardProps[LotteryProbDistribution.SortIndex]
        if not props or not props[1] then
            return
        end
        local skinInfo = pb.WeaponSkinInfo:New()
        skinInfo.skin_id = props[1].id
        skinInfo.skin_gid = props[1].gid
        
    
        ItemBase.SetWeaponSkinInfo2PropInfo(props[1], skinInfo)
        ItemBase.SetWeaponSkinID2PropInfo(props[1], props[1].id)
        ItemBase.SetWeaponSkinGUID2PropInfo(props[1], props[1].gid)
        if item then
            item:SetRawPropInfo(props[1])
            local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
            if itemMainType == EItemType.Adapter then
                Module.Collection:ShowHangingDetailPage(item)
            else
                Module.Collection:ShowWeaponSkinDetailPage(item)
            end
        end
    end

end

function StaffLotteryPreview:_ShowSelectedRewardInfo()    
    local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
    if not LotteryProbDistribution then
        return
    end
    local itemId = LotteryProbDistribution.SpecialItem ~= 0 and LotteryProbDistribution.SpecialItem or LotteryProbDistribution.RewardItem
    if not itemId then
        return
    end
    local itemData = ItemBase:New(itemId)
    StoreLogic.RegisterBottomAction(self, ItemHelperTool.GetMainTypeById(itemId) == 30)
    -- 设置品质图标
    if itemData then
        self._wtQualityIconImg:AsyncSetImagePath(Module.Collection.Config.QualityIconMapping[itemData.quality])
        self._wtQualityIconImg:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(itemData.quality))
    end
    -- 设置奖励名字
    if itemData then
        self._wtRewardNameTxt:SetText(itemData.name)
    end
    -- 设置奖励描述
    if itemData then
        local outStr = itemData.description
        local itemMainType = ItemHelperTool.GetMainTypeById(itemData.id)
        if itemMainType == EItemType.WeaponSkin then
            if itemData.itemSubType == ItemConfig.EWeaponItemType.Melee then
                local skinsInfo = Facade.TableManager:GetTable("WeaponSkin/MeleeWeaponSkinDataTable")
                local skinInfo = skinsInfo[itemData.id]
                if skinInfo then
                    outStr = skinInfo.SkinDescription
                end
            else
                local skinsInfo = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
                local skinInfo = skinsInfo[itemData.id]
                if skinInfo then
                    outStr = skinInfo.SkinDescription
                end
            end
        elseif itemMainType == EItemType.Fashion then -- 红角皮肤
            local fashionData = HeroFashionDataTable[itemData.id]
            if fashionData and fashionData.Desc then
                outStr = fashionData.Desc
            end
        end

        self._wtRewardDescTxt:SetText(outStr)
    end
    -- 是否显示配件预览按钮
    if itemData then
        if itemData.itemMainType == EItemType.Fashion then -- 红角皮肤时装
            self._wtAccessoriesPreviewBtn:Visible()
        else
            self._wtAccessoriesPreviewBtn:Collapsed()
        end

        if self._wtCommonDownload ~= nil then
            if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
                local moduleName = Module.ExpansionPackCoordinator:GetDownloadCategary(itemData.id)
                if moduleName ~= nil and moduleName ~= "None" then

                    self.nowSelectDownloaded = LiteDownloadManager:GetModuleStateByModuleName(moduleName) == 1
                    self._wtCommonDownload:InitModuleKey(moduleName)
                    self:_SetDownloadWidgetVisible(not self.nowSelectDownloaded)
                else
                    self:_SetDownloadWidgetVisible(false)
                end
                loginfo("StaffLotteryPreview:_ShowSelectedRewardInfo: moduleName = " .. tostring(moduleName) .. ", nowSelectDownloaded = " .. tostring(self.nowSelectDownloaded).. ", itemId = " .. tostring(itemData.id))
            else
                self.nowSelectDownloaded = true
                self:_SetDownloadWidgetVisible(false)
            end
        end
    end
    -- 是否显示进入商城bundle预览界面的按钮
    if itemData then
        if itemData.itemMainType == EItemType.WeaponSkin or itemData.itemMainType == EItemType.Adapter then -- 武器皮肤 --挂饰
            self._wtWeaponSkinPreviewBtn:Visible()
        else
            self._wtWeaponSkinPreviewBtn:Collapsed()
        end
    end
    -- 是否显示语音播放控件
    if itemData then
        if itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Lines then -- 台词
            self._wtWrapBoxList = self._wtStaffLotteryVoiceWidget:SetData(LotteryProbDistribution.RewardItem, self.belongedHeroID)
            self._wtStaffLotteryVoiceWidget:Visible()
            self:_EnableVoiceNavGroup()
        else
            self._wtStaffLotteryVoiceWidget:StopAllItemPlayAndAnim() -- 暂停所有音频播放
            self._wtStaffLotteryVoiceWidget:Collapsed()
        end
    end
    -- 显示奖励背景图
    if itemData then
        -- 道具资源信息
        local itemAsset = ItemConfigTool.GetItemAssetById(itemId)
        -- 静态图集路径
        local iconPath = nil
        if itemAsset and itemAsset.ItemIconPath then
            iconPath = itemAsset.ItemIconPath
        end
        
        self.DFScaleBox_Spray:Collapsed()        
        self._videoBG:Collapsed()

        local gameItemRow = ItemConfigTool.GetItemConfigById(itemId)
        if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
            iconPath = gameItemRow.MallItemIcon
        end

        if iconPath then
            self._wtBackgroundImg:AsyncSetImagePath(iconPath)
        end
        Facade.HallSceneManager:ResetRootActorOffset()--重新设置rootActor的偏移
        Module.Hero:ShowOperatorWatch("None")--删除手表
        -- 设置组件的Type        
	    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence") -- 重置干员Sequence
	    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero,"StopPlaySequenceVoice") -- 停止播放干员语音
        if itemData.itemMainType == EItemType.Fashion then -- 红角时装
            self:ShowRewardFashion(itemId)
            
            local fashionData = HeroFashionDataTable[itemId]
            if fashionData then
                self.belongedHeroID = fashionData.BelongedHeroID -- 皮肤所属的角色id
            end
        elseif itemData.itemMainType == EItemType.WeaponSkin then -- 武器皮肤
            self:ShowRewardWeaponSkin(itemData)
        elseif itemData.itemMainType == EItemType.Adapter then -- 配件
            self:ShowRewardAdapter(itemData)
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.AnimShow then -- 表演动画3p
            self:PlayCharacterAnim(itemData.id)
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Gesture then -- 表演手势1p
            self:PlayGesture(itemData.id)
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Lines then -- 台词
            self:ShowLinesInfo()
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Watch then -- 手表
            self:ShowOperatorWatch(itemId)
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Card then -- 名片
            self:ShowShowCardInfo()
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Execution then -- 处决
            self:ShowShowVedioInfo(itemData.id)
        elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SparyPaint then -- 喷漆
            self:ShowSparyPaintInfo(itemData, iconPath)
        elseif itemData.itemMainType == EItemType.VehicleSkin then -- 载具皮肤
            self:ShowVehicleSkin(itemId)
        elseif itemData.itemMainType == EItemType.SocialAppearance and itemData.itemSubType == ESocialAppearanceType.SocialAvatarTab then
            -- 社交定制外观-头像
            -- local headIcon = headIconTbl[iItemID].ResourceName
            -- BattlePassLogic.ShowRewardSocialAvatarTab(iItemID, tItem, iUINameID, wtUI, bMoveDone)
            self:ResetToMallScene()
            self:SetSize(1)
        elseif itemData.itemMainType == EItemType.SocialAppearance and itemData.itemSubType == ESocialAppearanceType.SocialMilitaryTab then
            -- 社交定制外观-军牌
            self:SetSize(3)
            self:ResetToMallScene()
        else -- 其他物品
            self.Type = 0
            Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
        end
    end
    -- 显示奖励详情根节点    
    self._wtPlatformPaddingBox:Visible()

    -- 背景设置    

    if self._IsCustomMode then
        if LotteryProbDistribution.CustomId then
            Facade.HallSceneManager:SetDisplayBackground(LotteryProbDistribution.CustomId,true)
        end
    else
        if LotteryProbDistribution.LotteryId then
            Facade.HallSceneManager:SetDisplayBackground(LotteryProbDistribution.LotteryId)
        end
    end
     --设置背景缩放
    Facade.HallSceneManager:SetBackgroundScale("WeaponShowBG")

    if WidgetUtil.IsGamepad() then
        self:_RefreshGampadBottomBar()
    end
    
end

--设置喷漆大图
function StaffLotteryPreview:SetBigImgtImg(imgPath)
    if not imgPath or string.len(imgPath) <= 0 then return end
    ResImageUtil.AsyncLoadImgObjByPath(imgPath, true, self.SetTextureParam, self)
end

function StaffLotteryPreview:SetTextureParam(imageAsset, bAutoResize)
    if self.SprayPaintData then
        local DynamicMatIns = self.wtImageSpray:GetDynamicMaterial()
        self.wtImageSpray:SelfHitTestInvisible()
        if DynamicMatIns then
            DynamicMatIns:SetTextureParameterValue("Texture_Flipbook", imageAsset)

            if self.nowSelectDownloaded then
                DynamicMatIns:SetScalarParameterValue("X_Flipbook", self.SprayPaintData.SprayPaintOffset[1])
                DynamicMatIns:SetScalarParameterValue("Y_Flipbook", self.SprayPaintData.SprayPaintOffset[2])
            end
        end
    end
end

function StaffLotteryPreview:RefreshRemainingTime()
    if not self._currentLotteryInfo then
        return
    end
    if self._currentLotteryInfo.IsDisplayCountdown == 0 then -- 不显示倒计时
        self._wtCountWidget:Collapsed()
    else
        -- 计算剩余时间的秒数
        local remainingTime = self._currentLotteryInfo.EndTime - Facade.ClockManager:GetLocalTimestamp()
        -- 将剩余时间拆分为天、小时、分钟和秒
        local day = math.floor(remainingTime / (24 * 3600))
        local hour = math.floor((remainingTime % (24 * 3600)) / 3600)
        local min = math.floor((remainingTime % 3600) / 60)
        local sec = remainingTime % 60
        -- 根据规则生成显示字符串
        local timeStr = nil
        if day >= 1 then
            timeStr = string.format(StoreConfig.Loc.StaffLotteryCountDownDayHour, day, hour)
        elseif day == 0 and hour >= 1 then
            timeStr = string.format(StoreConfig.Loc.StaffLotteryCountDownHourMin, hour, min)
        elseif day == 0 and hour == 0 then
            timeStr = string.format(StoreConfig.Loc.StaffLotteryCountDownMinSec, min, sec)
        end
        -- 设置显示字符串
        if timeStr then
            self._wtTextCountDown:SetText(timeStr)
            
            self._wtCountWidget:Visible()
        end
    end
end

function StaffLotteryPreview:ShowRewardFashion(itemId)
    self.Type = 5

    local fAllLevelFinishCallback = function()

        local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(itemId))
        --设置当前展示的角色
        Module.Hero:SetCurShowHeroById(HeroId)
        if  self.SetCharacterSuitAvatarWithSequence == "None" then -- 播放干员Sequence
            Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, false, self.SetCharacterSuitAvatarWithSequence, true)
            self.SetCharacterSuitAvatarWithSequence = "State2"
        else -- 干员站姿
            Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, true, self.SetCharacterSuitAvatarWithSequence, true)
        end
        
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallHero, fAllLevelFinishCallback)

    self._wtBackgroundImg:Collapsed() 
end
-- 3P动画
function StaffLotteryPreview:PlayCharacterAnim(itemId)    
    self.Type = 4

    local fAllLevelFinishCallback = function()
        -- Facade.GameFlowManager:EnterSubStage(ESubStage.HallHero)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade")--停止摄像机淡入淡出

        local HeroActionAnimData =  HeroHelperTool.GetAllAnimShowData()
        if HeroActionAnimData[itemId] then
            local belongedHeorIds = HeroActionAnimData[itemId].BelongedHeroIDs
            local defFashionId = Server.HeroServer:GetHeroDefFashionID(belongedHeorIds[1])
            if defFashionId == 0 then
                defFashionId = 30000020006 -- 如果为空，默认为威龙
            end
            local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(defFashionId))
            Module.Hero:PlayActionShowAnim(tostring(HeroId), itemId)
        end
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallHero, fAllLevelFinishCallback)
    
    self._wtBackgroundImg:Collapsed()
end

-- 1P手势动画
function StaffLotteryPreview:PlayGesture(itemId)
    self.Type = 0
    self._videoBG:Visible()
    self._mediaComponent:Stop()
    self._mediaComponent:Play(itemId)
    -- Timer.DelayCall(0.3, function ()
    --     self._mediaComponent:Stop()
    --     self._mediaComponent:Play(itemId)
    -- end)
    self._wtBackgroundImg:Collapsed()
end

-- 显示手表
function StaffLotteryPreview:ShowOperatorWatch(itemId)    
    self.Type = 4
    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 3, EOffsetType.ZOffset)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWatch", itemId)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end

    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)

    self._wtBackgroundImg:Collapsed()
end
-- 武器皮肤
function StaffLotteryPreview:ShowRewardWeaponSkin(item)    
    self.Type = 5
   
    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), item.id,
        -- false, false)    
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeaponAutoBoundAdapter", item:GetRawDescObj(), false, false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)

    self._wtBackgroundImg:Collapsed()
end
-- 配件
function StaffLotteryPreview:ShowRewardAdapter(item)    
    self.Type = 5

    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetShowWeaponPendant", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetEnableTrans", true)
        
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", true)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), item.id, 
        false, false)
        Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 6,EOffsetType.ZOffset)
    end

    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)
    
    self._wtBackgroundImg:Collapsed()
end
-- 台词
function StaffLotteryPreview:ShowLinesInfo()
    self.Type = 4

    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end

    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)

    self._wtBackgroundImg:Collapsed()
end
-- 喷漆
function StaffLotteryPreview:ShowSparyPaintInfo(itemData, iconPath)
    self.Type = 2
    self.DFScaleBox_Spray:Visible()
    self._wtBackgroundImg:Collapsed()

    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)

    if iconPath then
        if itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SparyPaint then --  喷漆
            local SprayPaintDatas = HeroHelperTool.GetAllSprayPaintData()
            self.SprayPaintData = SprayPaintDatas[itemData.id]
            if self.SprayPaintData ~= nil then
                self:SetBigImgtImg(self.SprayPaintData.SprayPaintDisplay)
            end
        end
    end
end
-- 名片
function StaffLotteryPreview:ShowShowCardInfo()
    self.Type = 1
    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)
    self._wtBackgroundImg:Visible()
    --展示名片
    self:SetSize(6)
end

-- 视频
function StaffLotteryPreview:ShowShowVedioInfo(itemId)
    self.Type = 7
    self:PlayGesture(itemId)

    -- local fAllLevelFinishCallback = function ()
    --     Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    --     Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    --     Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    -- end
    -- Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)
    -- self._wtBackgroundImg:Visible()
end

--载具皮肤
function StaffLotteryPreview:ShowVehicleSkin(itemId)
    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVehicleDisplayType", itemId, 1)
        Module.Vehicle:SetQualityEnv(itemId)
        Module.Vehicle:LoadVehicleFromSkinID(itemId)
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallVehicle, fAllLevelFinishCallback)
    self._wtBackgroundImg:Collapsed()
end

--恢复到默认商城场景
function StaffLotteryPreview:ResetToMallScene()
    local fAllLevelFinishCallback = function ()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    end
    Module.Store:EnterSubLevel(itemId, ESubStage.HallMall, fAllLevelFinishCallback)
    self._wtBackgroundImg:Visible()
end


function StaffLotteryPreview:_ModuleDownloadResult(moduleName, bSuccess, errorCode)
    if self._wtCommonDownload == nil then
        return
    end

    logerror("[StaffLotteryPreview] _ModuleDownloadResult ")
    local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
    if not LotteryProbDistribution then
        return
    end
    local itemId = LotteryProbDistribution.SpecialItem ~= 0 and LotteryProbDistribution.SpecialItem or LotteryProbDistribution.RewardItem
    if not itemId then
        return
    end

    local nowModuleName = Module.ExpansionPackCoordinator:GetDownloadCategary(itemId)
    if nowModuleName ~= nil and nowModuleName ~= "None" then
        local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(nowModuleName)
        if bDownloaded then
            logerror("[StaffLotteryPreview] _ModuleDownloadResult bDownloaded:true")
            -- 注意角色下载像个逻辑
            if IsMobile() or IsInEditor() then
                if self:_IsTargetChildModule(nowModuleName, "MiniWorldLong1") or self:_IsTargetChildModule(nowModuleName, "OtherSupplement") then
                    local worldcomposition = GetWorld().Worldcomposition
                    if worldcomposition then
                        -- worldcomposition:RequestReinitialize()
                        worldcomposition:Reinitialize()
                    end
                    logwarning("[echewzhu] StaffLotteryPreview worldcomposition:Reinitialize")
                end
            end

            self:_SetDownloadWidgetVisible(false)
            self:_ShowSelectedRewardInfo()
        else
            logerror("[StaffLotteryPreview] _ModuleDownloadResult bDownloaded:false")
            self:_SetDownloadWidgetVisible(true)
        end
    else
        self:_SetDownloadWidgetVisible(false)
    end
end

function StaffLotteryPreview:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_ModuleDownloadResult(moduleName, isSuccess, 0)
end

function StaffLotteryPreview:_IsTargetChildModule(downloadType, targetChildModule)
	local childModules = LiteDownloadManager:GetRegisterRuntimeChildModules(downloadType)
	if childModules ~= nil then
		for index, childModule in ipairs(childModules) do
			if childModule == targetChildModule then
				logwarning("[gorden] StaffLotteryPreview _IsTargetChildModule childModule:"..childModule)
				return true
			end
		end
	end
	logwarning("[gorden] StaffLotteryPreview _IsTargetChildModule return false downloadType:"..downloadType)
	return false
end

function StaffLotteryPreview:_ModuleCheckResult()
    if self._wtCommonDownload == nil then
        return
    end

    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        local LotteryProbDistribution = self.LotteryProbDistributionBySortIndex[self.currentSelectedRewardIndex]
        if not LotteryProbDistribution then
            return
        end
        local itemId = LotteryProbDistribution.SpecialItem ~= 0 and LotteryProbDistribution.SpecialItem or LotteryProbDistribution.RewardItem
        if not itemId then
            return
        end

        local moduleName = Module.ExpansionPackCoordinator:GetDownloadCategary(itemId)
        local bDownloaded
        if moduleName ~= nil and moduleName ~= "None" then
            bDownloaded = LiteDownloadManager:GetModuleStateByModuleName(moduleName) == 1
            self._wtCommonDownload:InitModuleKey(moduleName)
            self:_SetDownloadWidgetVisible(not bDownloaded)
        else
            self:_SetDownloadWidgetVisible(false)
        end
        loginfo("[StaffLotteryPreview] _ModuleCheckResult moduleName:"..tostring(moduleName).." bDownloaded:"..tostring(bDownloaded).."itemID:"..tostring(itemId))
    end
end

function StaffLotteryPreview:_WeaponRotationX(value)
    if not IsHD() then
        return
    end

    self._RotationVec.X = value * -1 * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end

end
function StaffLotteryPreview:_WeaponRotationY(value)
    if not IsHD() then
        return
    end

    self._RotationVec.Y = value * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end
end

function StaffLotteryPreview:_WeaponScaleUp(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function StaffLotteryPreview:_WeaponScaleDown(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * -1 * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()

    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function StaffLotteryPreview:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 设置导航
    if self._wtWaterFallList then
        if not self._NavGroup_WaterFall then
            self._NavGroup_WaterFall = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList , self, "Hittest")
        end
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._wtWaterFallList)
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._wtStaffLotteryVoiceWidget._wtWrapBoxList)
        self._NavGroup_WaterFall:SetScrollRecipient(self._wtWaterFallList)
        self._NavGroup_WaterFall:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        local NavStrategy = self._NavGroup_WaterFall:GetOwnerNavStrategy()
        if NavStrategy then
            NavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocused)
        end
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WaterFall)
    end

    -- if self._wtButtonRoot then
    --     if not self._NavGroup_ButtonRoot then
    --         self._NavGroup_ButtonRoot = WidgetUtil.RegisterNavigationGroup(self._wtButtonRoot , self, "Hittest")
    --     end
    --     self._NavGroup_ButtonRoot:AddNavWidgetToArray(self._wtButtonRoot)
    --     self._NavGroup_ButtonRoot:MarkIsStackControlGroup()
    -- end

    if WidgetUtil.IsGamepad() then
        self:_RefreshGampadBottomBar()
    end

    --武器旋转的输入
    if self._RotationX == nil then
        self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
    end
    if self._RotationY == nil then
        self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleUp == nil then
        self._ScaleUp = self:AddAxisInputActionBinding("Common_Right_Trigger_Axis", self._WeaponScaleUp, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleDown == nil then
        self._ScaleDown = self:AddAxisInputActionBinding("Common_Left_Trigger_Axis", self._WeaponScaleDown, self, EDisplayInputActionPriority.UI_Stack)
    end
end

function StaffLotteryPreview:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_WaterFall = nil
    self._NavGroup_ButtonRoot = nil
    -- self._NavGroup_WrapBoxList = nil

    if self._AccessoryDetails then
        self:RemoveInputActionBinding(self._AccessoryDetails)
        self._AccessoryDetails = nil
    end
end

function StaffLotteryPreview:_RefreshGampadBottomBar()
    local summaryList = {}
    if self._wtWeaponSkinPreviewBtn and self._wtWeaponSkinPreviewBtn:IsVisible() then
        table.insert(summaryList, {actionName = "Store_ItemDetail", func = self._OnWeaponSkinPreviewBtnClicked, caller = self, bUIOnly = false, bHideIcon = false})
    else
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end

    table.insert(summaryList, {actionName = "RotateItem", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})

    if self._wtAccessoriesPreviewBtn then
        self._wtAccessoriesPreviewBtn:SetDisplayInputAction("Accessory", true, nil, true)
    end

    if not self._AccessoryDetails then
        self._AccessoryDetails = self:AddInputActionBinding(
        "Accessory", 
        EInputEvent.IE_Pressed, 
        self._OnAccessoriesPreviewbtnClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
end

function StaffLotteryPreview:_EnableVoiceNavGroup()
    if not IsHD() then
        return 
    end

    -- 设置导航
    -- if self._wtWrapBoxList then
    --     if not self._NavGroup_WrapBoxList then
    --         self._NavGroup_WrapBoxList = WidgetUtil.RegisterNavigationGroup(self._wtWrapBoxList , self, "Hittest")
    --     end
    --     self._NavGroup_WrapBoxList:AddNavWidgetToArray(self._wtWrapBoxList)
    --     self._NavGroup_WrapBoxList:SetScrollRecipient(self._wtWrapBoxList)
    --     self._NavGroup_WrapBoxList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WrapBoxList)
    -- end

end

--- END MODIFICATION


function StaffLotteryPreview:_SetDownloadWidgetVisible(isVisible)
    logerror("[StaffLotteryPreview] _SetDownloadWidgetVisible isVisible:"..tostring(isVisible).."debug traceback: \n"..debug.traceback())
    if self._wtCommonDownload then
        if isVisible then
            self._wtCommonDownload:Visible()
        else
            self._wtCommonDownload:Collapsed()
        end
    end
end

return StaffLotteryPreview