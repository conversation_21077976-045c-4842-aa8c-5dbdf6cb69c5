----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class TournamentMainPanel : LuaUIBaseView
local TournamentMainPanel = ui("TournamentMainPanel")
local TournamentConfig = Module.Tournament.Config
local TournamentLogic = require "DFM.Business.Module.TournamentModule.Logic.TournamentLogic"
local CommonWidgetConfig=Module.CommonWidget.Config
local RankingConfig=Module.Ranking.Config

function TournamentMainPanel:Ctor()
    loginfo("TournamentMainPanel:Ctor")
    self._wtRankLadderBtn=self:Wnd("WBP_Ranking_Main_Item_C_5",UIWidgetBase)--天梯排名
    self._wtRankRecorderBtn=self:Wnd("WBP_Ranking_Main_Item_1",UIWidgetBase)--排位记录
    self._wtRankRewardBtn=self:Wnd("WBP_Ranking_Main_Item_2",UIWidgetBase)--全部奖励
    self._wtLevelPreviewBtn=self:Wnd("wtCommonIconBtnFind",DFCommonButtonOnly)--段位预览
    self._wtSeasonDescBtn=self:Wnd("wtCommonCheckInstruction",DFCheckBoxOnly)--赛季详情
    self._wtMaxRewardItem=self:Wnd("WBP_CommonItemTemplate",IVCommonItemTemplate)--最高奖励
    self._wtNextRewardItem=self:Wnd("WBP_CommonItemTemplate_1",IVCommonItemTemplate)--下一奖励
    self._wtNextRewardText=self:Wnd("DFTextBlock_2",UITextBlock)--下一奖励文本
    self._wtLevelNameBox=self:Wnd("DFHorizontalBox_108",UIWidgetBase)
    self._wtRankScoreTitle=self:Wnd("DFTextBlock_401",UITextBlock)
    self._wtLevelProgressBox=self:Wnd("DFHorizontalBox_2",UIWidgetBase)
    self._wtLevelNameText=self:Wnd("DFTextBlock_143",UITextBlock)--段位名
    self._wtRankScoreText=self:Wnd("DFRichTextBlock_48",UITextBlock)--段位分
    self._progressList=self:MultiWnd("WBP_Ranking_Progressbar",UIWidgetBase)--进度条列表
    self._wtSeasonNameText=self:Wnd("DFTextBlock_75",UITextBlock)--赛季名
    self._wtSeasonDurationText=self:Wnd("DFTextBlock_299",UITextBlock)--赛季时间
    self._wtSeasonDescText=self:Wnd("DFTextBlock_180",UITextBlock)--赛季描述
    self._wtRankBGSlot=self:Wnd("DFNamedSlot_85",UIWidgetBase)
    self._wtCoreRewardIcon=self:Wnd("CDNImage_MainGain",UIImage)--核心奖励图片
    self._wtCoreRewardButton=self:Wnd("DFButton_253",UIButton)--核心奖励按钮
    self._wtCoreRewardText=self:Wnd("DFRichTextBlock_GainTips",UITextBlock)--核心奖励文本
    self._progressList=self:MultiWnd("WBP_Ranking_Progressbar",UIWidgetBase)--进度条列表
    self._wtRankStarItem=self:Wnd("WBP_SettlementSectionStarBar",UIWidgetBase)--段位星控件
    self._wtLevelDescText=self:Wnd("DFTextBlock_608",UITextBlock)
    
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtRightPanel = self:Wnd("DFVerticalBox_1", UIWidgetBase)
        self._wtLeftPanel = self:Wnd("DFCanvasPanel_273", UIWidgetBase)
    end
    -- END MODIFICATION

    self._wtLevelDescText:Collapsed()
    self._wtRankScoreTitle:SetText(Module.Tournament.Config.Loc.CurrentScore)
    self._wtRankLadderBtn:AddClickCallback(CreateCallBack(self.OpenRankLadderPanel, self))
    self._wtRankRecorderBtn:AddClickCallback(CreateCallBack(self.OpenRankRecorderPanel, self))
    self._wtRankRewardBtn:AddClickCallback(CreateCallBack(self.OpenRankRewardPanel, self))
    self._wtRankLadderBtn:HideExtraBtnString()
    self._wtRankRecorderBtn:HideExtraBtnString()
    self._wtRankRewardBtn:HideExtraBtnString()
    self._wtRankRecorderBtn:SetTitleText(Module.Tournament.Config.Loc.RankingRecord)
    self._wtRankLadderBtn:SetTitleText(Module.Tournament.Config.Loc.TournamentLadder)
    self._wtLevelPreviewBtn:Event("OnClicked",self.OpenLevelPreviewPanel,self)
    self._wtSeasonDescBtn:Event("OnCheckStateChanged",self.OpenSeasonDescPanel,self)
    self._wtCoreRewardButton:Event("OnClicked",self.OnCoreRewardBtnClicked,self)
    self._wtRankLadderBtn:Collapsed()

    for k,v in pairs(self._progressList) do
        v:Collapsed()
    end
    
end

function TournamentMainPanel:OnInitExtraData(params)
    loginfo("TournamentMainPanel:OnInitExtraData")
end

function TournamentMainPanel:OnOpen()
    loginfo("TournamentMainPanel:OnOpen")
    self:AddBtnReddots()
    self:AddListeners()
    self:AddMouseButtonDownEvent()
    self:RefreshView()
    self:RefreshNewUnlockReddot()
    Server.TournamentServer:ReqSeasonRankInfo()--重新拉取一下赛季信息，因为后台可能出现刷新延迟的情况

end

---@overload fun(LuaUIBaseView, OnShowBegin)
function TournamentMainPanel:OnShowBegin()
    TournamentConfig.evtTournamentMainUIShow:Invoke()
    self:RefreshCoreRewardView()--cdn图片每次显示都要重新设置，设置有延迟，所以放到OnShowBegin里调用

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    -- END MODIFICATION
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function TournamentMainPanel:OnShow()
    loginfo("TournamentMainPanel:OnShow")
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function TournamentMainPanel:OnHide()
    loginfo("TournamentMainPanel:OnHide")

end

-- BEGIN MODIFICATION @ VIRTUOS
function TournamentMainPanel:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end
-- END MODIFICATION

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function TournamentMainPanel:OnClose()
    loginfo("TournamentMainPanel:OnClose")
    
    self:RemoveAllLuaEvent()
    self:RemoveMouseButtonDownEvent()
    self:RemoveAllActionHandle()
    Facade.UIManager:ClearSubUIByParent(self,self._wtRankBGSlot)

end

function TournamentMainPanel:AddBtnReddots()
    loginfo("TournamentMainPanel:AddBtnReddots")
    self._wtRankRewardBtn:AddReddot(EReddotTrieObserverType.Tournament,"NewReward")
end

-- UI监听事件、协议
function TournamentMainPanel:AddListeners()
    self:AddLuaEvent(Server.TournamentServer.Events.evtTournamentInfoUpdated, self.OnTournamentInfoUpdated, self)
end

function TournamentMainPanel:AddMouseButtonDownEvent()
    loginfo("TournamentMainPanel:AddMouseButtonDownEvent")
    local gameInst=GetGameInstance()
    if gameInst then
        self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnMouseButtonDown,self)
    end
end

function TournamentMainPanel:RemoveMouseButtonDownEvent()
    loginfo("TournamentMainPanel:RemoveMouseButtonDownEvent")
    if self._mouseButtonDownHandle then
        local gameInst=GetGameInstance()
        if gameInst then
            self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._mouseButtonDownHandle)
        end
    end
end

function TournamentMainPanel:RemoveAllActionHandle()
    loginfo("TournamentMainPanel:RemoveAllActionHandle")
    if self._openRecordPanelActionHandle then
        self:RemoveInputActionBinding(self._openRecordPanelActionHandle)
        self._openRecordPanelActionHandle=nil
    end
    if self._openRewardPanelActionHandle then
        self:RemoveInputActionBinding(self._openRewardPanelActionHandle)
        self._openRewardPanelActionHandle=nil
    end
end

function TournamentMainPanel:OnTournamentInfoUpdated()
    loginfo("TournamentMainPanel:OnTournamentInfoUpdated")
    self:RefreshView()
    self:RefreshCoreRewardView()
end

function TournamentMainPanel:RefreshView()
    loginfo("TournamentMainPanel:RefreshView")
    self:RefreshNextRewardItem()
    --self:RefreshMaxRewardItem()
    self:InitSeasonName()
    self:InitLevelName()
    self:RefreshCoreRewardView()
end

function TournamentMainPanel:RefreshNewUnlockReddot()
    loginfo("TournamentMainPanel:RefreshNewUnlockReddot")
    if not Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.isTournamentPanelOpenedOnce) then
        Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.isTournamentPanelOpenedOnce,true)
        Server.TournamentServer:BroadcastUpdateReddot()
    end

end

function TournamentMainPanel:RefreshCoreRewardView()
    loginfo("TournamentMainPanel:RefreshCoreRewardView")
    local seasonConfig=Module.Tournament:GetSeasonConfigBySerial(Server.TournamentServer:GetCurSerial())
    local imgUrl=string.format(Module.Tournament.Config.CoreRewardIconUrlPrefix,seasonConfig and seasonConfig.CoreAwardPicture or "")
    local itemId=seasonConfig and seasonConfig.CoreAward
    self._coreRewardLevel=seasonConfig and seasonConfig.CoreTier
    if self._wtCoreRewardIcon and self._wtCoreRewardIcon.SetCDNImage then
        self._wtCoreRewardIcon:SetCDNImage(imgUrl,false,Module.CDNIcon.Config.ECdnTagEnum.Activity)
    end
    local item=ItemBase:New(itemId or 0)
    local majorInfo=Module.Tournament:GetRankDataByMajor(self._coreRewardLevel)
    local levelName=majorInfo and majorInfo.Type or "?"
    local rewardInfo=Module.Tournament:GetLevelRewardsInfo(self._coreRewardLevel)
    local gameCount=rewardInfo and rewardInfo.RewardConditionPar or "?"
    self._wtCoreRewardText:SetText(StringUtil.Key2StrFormat(Module.Tournament.Config.Loc.CoreRewardDesc, {levelName=levelName,gameCount=gameCount,rewardName=item.name}))
end

function TournamentMainPanel:OpenRankLadderPanel()
    loginfo("TournamentMainPanel:OpenRankLadderPanel")
    Facade.UIManager:AsyncShowUI(UIName2ID.LadderRankingWindow,nil,nil,RankingConfig.RankModeType.Tournament)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.Rank)

end

function TournamentMainPanel:OpenRankRecorderPanel()
    loginfo("TournamentMainPanel:OpenRankRecorderPanel")
    Facade.UIManager:AsyncShowUI(UIName2ID.RankingRecordWindow,nil,nil,RankingConfig.RankModeType.Tournament)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.Record)

end

function TournamentMainPanel:OpenRankRewardPanel()
    loginfo("TournamentMainPanel:OpenRankRewardPanel")
    TournamentConfig.evtOpenRankRewardPanelBtnClicked:Invoke()
    Module.Tournament:ShowRewardPanel(Server.TournamentServer:GetMajorLevel(),Module.Ranking.Config.RankModeType.Tournament)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.Reward)

end

function TournamentMainPanel:OpenLevelPreviewPanel()
    loginfo("TournamentMainPanel:OpenLevelPreviewPanel")

    Facade.UIManager:AsyncShowUI(UIName2ID.TournamentPreviewWindow,nil,nil,TournamentConfig.ETournamentPreviewType.LevelPreview)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.LevelDesc)

end

function TournamentMainPanel:OpenSeasonDescPanel()
    loginfo("TournamentMainPanel:OpenSeasonDescPanel")
    self._wtSeasonDescBtn:SetIsChecked(false,false)
    Facade.UIManager:AsyncShowUI(UIName2ID.TournamentPreviewWindow,nil,nil,TournamentConfig.ETournamentPreviewType.SeasonDesc)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.SeasonDesc)

end

function TournamentMainPanel:OnCoreRewardBtnClicked()
    loginfo("TournamentMainPanel:OnCoreRewardBtnClicked")
    if self._coreRewardLevel then
        Module.Tournament:ShowRewardPanel(self._coreRewardLevel,RankingConfig.RankModeType.Tournament)
    else
        logerror("TournamentMainPanel:OnCoreRewardBtnClicked, coreRewardLevel is nil!!!")
    end
end

function TournamentMainPanel:InitSeasonName()
    loginfo("TournamentMainPanel:InitSeasonName")
    local seasonConfig=Module.Tournament:GetSeasonConfigBySerial(Server.TournamentServer:GetCurSerial())
    if seasonConfig then
        local startTimeStr=""
        local endTimeStr=""
        local timeOffset=IsBuildRegionCN() and 8*3600 or 0
        if seasonConfig.TimeDisplayDigit==2 then
            startTimeStr=TimeUtil.TransUnixTimestamp2YYMMStr(Server.TournamentServer:GetUIStartTime()+timeOffset,"YY/MM")
            endTimeStr=TimeUtil.TransUnixTimestamp2YYMMStr(Server.TournamentServer:GetUIEndTime()+timeOffset,"YY/MM")
        else
            startTimeStr=TimeUtil.TransUnixTimestamp2YYMMDDStr(Server.TournamentServer:GetUIStartTime()+timeOffset,"YY/MM/DD")
            endTimeStr=TimeUtil.TransUnixTimestamp2YYMMDDStr(Server.TournamentServer:GetUIEndTime()+timeOffset,"YY/MM/DD")
        end
        self._wtSeasonDurationText:SetText(StringUtil.Key2StrFormat(Module.Tournament.Config.Loc.SeasonDuration,{startTime=startTimeStr,endTime=endTimeStr}))
        self._wtSeasonNameText:SetText(seasonConfig.Name or "?")
        self._wtSeasonDescText:SetText(seasonConfig.SeasonDescription or "?")
    end
    
end

function TournamentMainPanel:InitLevelName()
    loginfo("TournamentMainPanel:InitLevelName")

    if not Server.TournamentServer:GetHasAttended() then--无段位
        self:SetType(0)
        self._wtRankScoreText:SetText(Module.Tournament.Config.Loc.LevelDescNone)

        Facade.UIManager:RemoveSubUIByParent(self,self._wtRankBGSlot)
        local weakIns=Facade.UIManager:AddSubUI(self,Module.Tournament.Config.RankLevel2BGMap[0],self._wtRankBGSlot)
        local uiIns=getfromweak(weakIns)
        if uiIns then
            uiIns:PlayWidgetAnim(uiIns.WBP_Mp_Ranking_In)
        end
        
    else
        self:SetType(1)
        self._wtLevelDescText:Collapsed()
        local minorLevelInfo=Module.Tournament:GetRankDataByMinor(Server.TournamentServer:GetMinorLevel())
        if minorLevelInfo then
            self._wtLevelNameText:SetText(minorLevelInfo.Name)
            local bgIndex=minorLevelInfo.BadgeBlueprint~=0 and minorLevelInfo.BadgeBlueprint or Server.TournamentServer:GetMajorLevel()
            Facade.UIManager:RemoveSubUIByParent(self,self._wtRankBGSlot)
            local weakIns=Facade.UIManager:AddSubUI(self,Module.Tournament.Config.RankLevel2BGMap[bgIndex],self._wtRankBGSlot)
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:PlayWidgetAnim(uiIns.WBP_Mp_Ranking_In)
                if uiIns.SetRomaNum then
                    uiIns:SetRomaNum(Module.Tournament:GetMinorIndexByMinor(minorLevelInfo.ID)-1)
                end
            end
                
            local rankScore=Server.TournamentServer:GetRankScore()
            for k,v in pairs(self._progressList) do
                v:Collapsed()
            end
            if minorLevelInfo.TierTypeID==Module.Tournament:GetDefinedMaxMajorLevel() then--传奇段位
                local starNum=Module.Tournament:GetStarNumByScore(rankScore)
                self._wtRankScoreText:SetText(string.format(Module.Ranking.Config.Loc.RankScoreDivide,rankScore,minorLevelInfo.MinPoint+Module.Tournament:GetRankConstant()*starNum))
                if self._progressList[1] then
                    self._progressList[1]:SelfHitTestInvisible()
                    self._progressList[1]:BPSetPercent(Module.Tournament:GetLastStarPercentByScore(rankScore))
                end
                self._wtRankStarItem:SetRankStarNum(-1)
                self._wtRankStarItem:SetRankStarState(starNum)
            else
                local starNum=Module.Tournament:GetStarNumByScore(rankScore)
                local nextRankData=Module.Tournament:GetNextRankDataByScore(rankScore)
                local maxPoint=nextRankData and nextRankData.MinPoint or rankScore
                self._wtRankScoreText:SetText(string.format(Module.Tournament.Config.Loc.RankScoreDivide,rankScore,maxPoint))
                local starsDivided=minorLevelInfo.StarsDivided or 1
                for i=1,starsDivided do--进度条是用小进度条拼的，用几个显示几个
                    if self._progressList[i] then
                        self._progressList[i]:SelfHitTestInvisible()
                        if i<starNum then
                            self._progressList[i]:BPSetPercent(1)
                        elseif i>starNum then
                            self._progressList[i]:BPSetPercent(0)
                        else
                            local percent=Module.Tournament:GetLastStarPercentByScore(rankScore)
                            self._progressList[i]:BPSetPercent(percent)
                        end
                    end
                end
                self._wtRankStarItem:SetRankStarNum(starsDivided)
                self._wtRankStarItem:SetRankStarState(starNum)
            end
        else
            logerror("TournamentMainPanel:InitLevelName minorLevelInfo is nil!!!")
        end
    end

end

function TournamentMainPanel:RefreshMaxRewardItem()--最高奖励（暂时屏蔽)
    loginfo("TournamentMainPanel:RefreshMaxRewardItem")
    local rewardInfo=nil
    for i=Module.Tournament:GetDefinedMaxMajorLevel(),0,-1 do
        rewardInfo=Module.Tournament:GetLevelRewardsInfo(i)
        if rewardInfo then
            break
        end
    end
    if rewardInfo then
        local reward=rewardInfo.Rewards[1]
        if reward then
            local id,num=Module.Tournament:ParseRewardStr(reward)
            local itemBase = ItemBase:NewIns(id, num)
            self._wtMaxRewardItem:InitItem(itemBase)
            if table.contains(Server.TournamentServer:GetReceivedLevels(),rewardInfo.TierID) then
                self._wtMaxRewardItem:FindOrAdd(CommonWidgetConfig.EIVWarehouseTempComponent.GetMask, UIName2ID.IVGetMaskComponent, CommonWidgetConfig.EIVSlotPos.MaskLayer,CommonWidgetConfig.EIVCompOrder.MaskLayerOrder)
            elseif table.contains(Server.TournamentServer:GetNotReceivedLevels(),rewardInfo.TierID) then
                local rewardCanvas=self._wtMaxRewardItem:Wnd("DFCanvasPanel_0",UIWidgetBase)
                if rewardCanvas then
                    rewardCanvas:SelfHitTestInvisible()
                    self._wtMaxRewardItem:PlayWidgetAnim(self._wtMaxRewardItem.WBP_CommonItemTemplate_Loop)
                end
            end
            self._wtMaxRewardItem:BindCustomOnClicked(
            function()
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)
                Module.Tournament:ShowRewardPanel(rewardInfo.TierID,Module.Ranking.Config.RankModeType.Tournament)
                LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.MaxReward)
            end)
        else
            logerror("TournamentMainPanel:RefreshMaxRewardItem reward is nil!!!")
        end
        
    else
        logerror("TournamentMainPanel:RefreshMaxRewardItem rewardInfo is nil!!!")
    end
    
end

function TournamentMainPanel:RefreshNextRewardItem()--下一奖励
    loginfo("TournamentMainPanel:RefreshNextRewardItem")
    local notReceivedLevels=Server.TournamentServer:GetNotReceivedLevels() or {}
    local receivedLevels=Server.TournamentServer:GetReceivedLevels() or {}
    local rewardTable=Module.Tournament:GetCurSeasonRewardsTable() or {}

    local rewardInfo=nil
    if notReceivedLevels[1] then--未领取
        rewardInfo=Module.Tournament:GetLevelRewardsInfo(notReceivedLevels[1])
    elseif table.nums(receivedLevels)==table.nums(rewardTable) then--全部已领取
        self._wtNextRewardItem:Collapsed()
        self._wtNextRewardText:Collapsed()
        return
    else--未达成
        for k,v in pairs(Module.Tournament:GetSortedRewardsTable() or {}) do
            if not table.contains(notReceivedLevels,v.TierID) and not table.contains(receivedLevels,v.TierID) then
                rewardInfo=v
                break
            end
        end
    end
    if rewardInfo then
        local reward=rewardInfo.Rewards[1]
        if reward then
            local id,num=Module.Tournament:ParseRewardStr(reward)
            local itemBase = ItemBase:NewIns(id, num)
            self._wtNextRewardItem:InitItem(itemBase)
            self._wtNextRewardItem:StopIVAnimation("WBP_Itemview_LightEffect_01_Loop")
            if table.contains(notReceivedLevels,rewardInfo.TierID) then
                self._wtNextRewardItem:PlayIVAnimation("WBP_Itemview_LightEffect_01_Loop",0)
            end
            self._wtNextRewardItem:BindCustomOnClicked(--打开奖励界面领奖
                function()
                    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)
                    Module.Tournament:ShowRewardPanel(rewardInfo.TierID,Module.Ranking.Config.RankModeType.Tournament)
                    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.NextReward)
                end)
        else
            logerror("TournamentMainPanel:RefreshNextRewardItem reward is nil!!!")
        end
  
    else
        logerror("TournamentMainPanel:RefreshNextRewardItem rewardInfo is nil!!!")
    end
    
end

function TournamentMainPanel:_OnMouseButtonDown(mouseEvent)
    local screenPosition=mouseEvent:GetScreenSpacePosition()

end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function TournamentMainPanel:OnAnimFinished(anim)
end

-- BEGIN MODIFICATION @ VIRTUOS
function TournamentMainPanel:_InitGamepadInputs()
    if not IsHD() then
        return
    end

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        self._navGroup:AddNavWidgetToArray(self._wtLeftPanel)

        self._navGroup:MarkIsStackControlGroup()
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end

    if not self._gamepadSummaryList then
        self._gamepadSummaryList = {
            {actionName = "TournamentPreview", func = self.OpenLevelPreviewPanel, caller = self},
            {actionName = "Ranking_SeasonRules", func = self.OpenSeasonDescPanel, caller = self},
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)
    end
    self._wtRankRecorderBtn:SetKeyIconByActionName("Common_ButtonLeft_Gamepad",true)
    self._wtRankRewardBtn:SetKeyIconByActionName("Common_ButtonTop",true)
    if not self._openRecordPanelActionHandle then
        self._openRecordPanelActionHandle=self:AddInputActionBinding("Common_ButtonLeft_Gamepad",EInputEvent.IE_Pressed,self.OpenRankRecorderPanel,self,EDisplayInputActionPriority.UI_Stack)
    end
    if not self._openRewardPanelActionHandle then
        self._openRewardPanelActionHandle=self:AddInputActionBinding("Common_ButtonTop",EInputEvent.IE_Pressed,self.OpenRankRewardPanel,self,EDisplayInputActionPriority.UI_Stack)
    end
end

function TournamentMainPanel:_DisableGamepadInputs()
    if not IsHD() then
        return
    end
    
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end

    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end
end
-- END MODIFICATION

return TournamentMainPanel    
