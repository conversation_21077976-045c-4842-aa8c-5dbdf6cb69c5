----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local SystemSettingHDCloudSettingWindow = ui("SystemSettingHDCloudSettingWindow")
local CloudSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CloudSettingLogicHD"

function SystemSettingHDCloudSettingWindow:Ctor()
    -- data
    self._bAutoUpload = CloudSettingLogicHD.GetAutoUploadState()

    -- bind & init widget
    self._wtCommonPopWin = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtCommonPopWin:BindCloseCallBack(CreateCallBack(self._OnCloseBtnClicked, self))

    self._wtCloudSettingSlots = {}
    table.insert(self._wtCloudSettingSlots, self:Wnd("WBP_SetUp_PopWindow_ProgramItem_2", UIWidgetBase))
    table.insert(self._wtCloudSettingSlots, self:Wnd("WBP_SetUp_PopWindow_ProgramItem_3", UIWidgetBase))
    table.insert(self._wtCloudSettingSlots, self:Wnd("WBP_SetUp_PopWindow_ProgramItem_4", UIWidgetBase))
    table.insert(self._wtCloudSettingSlots, self:Wnd("WBP_SetUp_PopWindow_ProgramItem_5", UIWidgetBase))
    table.insert(self._wtCloudSettingSlots, self:Wnd("WBP_SetUp_PopWindow_ProgramItem_6", UIWidgetBase))
    self._wtLocalSaveSlot = self:Wnd("WBP_SetUp_PopWindow_ProgramItem_1", UIWidgetBase)
    table.insert(self._wtCloudSettingSlots, self._wtLocalSaveSlot)
    for _, v in pairs(self._wtCloudSettingSlots) do
        v:Collapsed()
    end

    self._wtCloudSettingSlotCount = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtAutoUpload = self:Wnd("wtDFCommonCheckBoxWithText", UICheckBox)
    self._wtAutoUpload:SetIsChecked(self._bAutoUpload)
    self._wtAutoUpload:Event("OnCheckStateChanged", self._OnAutoUploadChecked, self)

    self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_286", self._OnShowTips, self._OnHideTips, nil)
    self._wtSaveToCloud = self:Wnd("WBP_SetUp_PopWindow_ProgramAdd", UIWidgetBase)
    self._wtSaveToCloudBtn = self._wtSaveToCloud:Wnd("DFButton_0", UIWidgetBase)
    self._wtSaveToCloudBtn:Event("OnClicked", self._OnSaveToCloudButtonClicked, self)
    self._wtSaveToLocal = self:Wnd("WBP_SetUp_PopWindow_ProgramAdd_1", UIWidgetBase)
    self._wtSaveToLocalBtn = self._wtSaveToLocal:Wnd("DFButton_0", UIWidgetBase)
    self._wtSaveToLocalBtn:Event("OnClicked", self._OnSaveToLocalButtonClicked, self)

    -- 预下载 名字校验用
    local config = Server.SDKInfoServer:GetPlayerNameConf()
    if config == nil then
        CloudSettingLogicHD.ReqAccountUnicodeConf()
    end
end

function SystemSettingHDCloudSettingWindow:OnShow()
    self:_UpdateSlotsDisplay()
    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtCloudSettingSlotStateChanged, self._OnCloudSettingSlotStateChanged, self)
end

function SystemSettingHDCloudSettingWindow:OnHide()
    self:RemoveLuaEvent(Module.SystemSetting.Config.Event.evtCloudSettingSlotStateChanged, self)
end

function SystemSettingHDCloudSettingWindow:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function SystemSettingHDCloudSettingWindow:_OnCloudSettingSlotStateChanged()
    self:_UpdateSlotsDisplay()
end

function SystemSettingHDCloudSettingWindow:_OnAutoUploadChecked(bIsChecked)
    CloudSettingLogicHD.SetAutoUploadState(bIsChecked)
    self:_UpdateSlotsDisplay()
end

function SystemSettingHDCloudSettingWindow:_OnShowTips()
    self._tipsHandle = Module.CommonTips:ShowCommonMessageWithAnchor(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.AutoUploadTip, self._wtTipsAnchor)
end

function SystemSettingHDCloudSettingWindow:_OnHideTips()
    if self._tipsHandle and self._tipsHandle.GetUIIns then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipsHandle, self._wtTipsAnchor)
        self._tipsHandle = nil
    end
end

function SystemSettingHDCloudSettingWindow:_OnNameConfirmed(newName)
    if newName then
        local index = self._creatingLocalSlot and CloudSettingLogicHD.localIndex or CloudSettingLogicHD.GetFreeIndex()
        CloudSettingLogicHD.SetName(index, newName)
    end
end

function SystemSettingHDCloudSettingWindow:_OnSaveToCloudButtonClicked()
    self._creatingLocalSlot = false
    local fOnNameConfirmed = CreateCallBack(self._OnNameConfirmed, self)
    CloudSettingLogicHD.CreateNameWindow(fOnNameConfirmed, false)
end

function SystemSettingHDCloudSettingWindow:_OnSaveToLocalButtonClicked()
    self._creatingLocalSlot = true
    local fOnNameConfirmed = CreateCallBack(self._OnNameConfirmed, self)
    CloudSettingLogicHD.CreateNameWindow(fOnNameConfirmed, true)
end

function SystemSettingHDCloudSettingWindow:_UpdateSlotsDisplay()
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    -- 更新槽显示
    local bHasLocal = false
    for i, slotWidget in ipairs(self._wtCloudSettingSlots) do
        local index = meta.Slots and meta.Slots[i] and meta.Slots[i].index or nil
        if index == CloudSettingLogicHD.localIndex then
            bHasLocal = true
            self._wtLocalSaveSlot:SetIndex(index)
        elseif i < CloudSettingLogicHD.localIndex or not bHasLocal then
            slotWidget:SetIndex(index)
        end
    end
    local cloudSlotCnt = 0
    local localSlotCnt = 0
    if meta and meta.Slots then
        if #meta.Slots < CloudSettingLogicHD.maxSlotCount then
            for i, v in ipairs(meta.Slots) do
                if CloudSettingLogicHD.IsLocal(v.index) then
                    localSlotCnt = localSlotCnt + 1
                else
                    cloudSlotCnt = cloudSlotCnt + 1
                end
            end
        else
            localSlotCnt = 1
            cloudSlotCnt = CloudSettingLogicHD.maxSlotCount - localSlotCnt
        end
    end

    if cloudSlotCnt >= CloudSettingLogicHD.maxSlotCount - 1 then
        self._wtSaveToCloud:Collapsed()
    else
        self._wtSaveToCloud:SelfHitTestInvisible()
    end
    if localSlotCnt >= 1 then
        self._wtSaveToLocal:Collapsed()
    else
        self._wtSaveToLocal:SelfHitTestInvisible()
    end
end

return SystemSettingHDCloudSettingWindow
