----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class GunUnlockGrowthRoadItem : LuaUIBaseView
--成长之路item
local GunUnlockGrowthRoadItem = ui("GunUnlockGrowthRoadItem")
local GunsmithPartUnlockLogic = require "DFM.Business.Module.GunsmithModule.Logic.Unlock.GunsmithPartUnlockLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"

function GunUnlockGrowthRoadItem:Ctor()
    --道具等级/点击按钮/展开按钮
    self._wtLvlText = self:Wnd("wLvlText", UITextBlock)
    self._wtChickBtn = self:Wnd("DFButton_2", UIButton)
    self._wtChickBtn:Event("OnClicked",   self._OnClicked,  self)--按钮点击事件
    self._wtChickBtn:Event("OnHovered",   self._OnHovered,  self)--按钮悬浮事件
    self._wtChickBtn:Event("OnUnhovered", self._OnUnhovered,  self)--按钮悬浮结束
    self._wtShowImage = self:Wnd("DFImage_199", UIImage)
    --分割线隐藏
    self._wtLinePanel = self:Wnd("DFCanvasPanel_7", UIWidgetBase)
    --道具容器
    self._wtPropPanel = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
    --首尾间隙控件
    self._wtHeadAndTailImg = self:Wnd("HeadAndTailImg", UIImage)
end

function GunUnlockGrowthRoadItem:_OnClicked()
    if self._isClick then
        return
    end
    --播放特效
    self:PlayAnimation(self.WBP_PathofGrowth_Detail_Selected, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self._isClick = not self._isClick
    self:_SetItemState()
    Module.Gunsmith.Config.Events.evtGunsmithPropItemClicked:Invoke(self)
end

function GunUnlockGrowthRoadItem:itemFunc()
    self._isClick = false
    self._isHovered = false
    self:_SetItemState()
end

function GunUnlockGrowthRoadItem:_OnHovered()
    if WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad then
        self:_OnClicked()
        return
    end
    if self._isClick then
        return
    end
    self._isHovered = true
    self:_SetItemState()
end

function GunUnlockGrowthRoadItem:_OnUnhovered()
    if self._isClick then
        return
    end
    self._isHovered = false
    self:_SetItemState()
end

function GunUnlockGrowthRoadItem:OnOpen()
end

function GunUnlockGrowthRoadItem:_SetItemState()
    if self.SetType then
        local state = 3
        if self._isClick and not self._isUnLock and self._isCurLevel then
            state = 8
        elseif self._isClick and not self._isUnLock then
            state = 5
        elseif self._isClick and self._isUnLock then
            state = 2
        elseif self._isHovered and not self._isUnLock and self._isCurLevel then
            state = 7
        elseif self._isHovered and not self._isUnLock then
            state = 4
        elseif self._isHovered and self._isUnLock then
            state = 1
        elseif not self._isUnLock and self._isCurLevel then
            state = 6
        elseif not self._isUnLock then
            state = 3
        else
            state = 0
        end
        self:SetType(0)
        self:SetType(state)
    end
end

--重置变量
function GunUnlockGrowthRoadItem:_ReSetItemStateData()
   self._isClick = false
   self._isUnLock = false
   self._isHovered = false
   self._isCurLevel = false
end

function GunUnlockGrowthRoadItem:InitItemData(callBack, curLevel, maxLevel, itemData)
    if callBack == nil or curLevel == nil or itemData == nil or maxLevel == nil then
        return
    end
    --父类对象池函数
    self._callBack = callBack
    self._curLevel = curLevel
    self.itemData = itemData
    self._maxLevel = maxLevel
    --重置变量
    self:_ReSetItemStateData()
    if maxLevel == itemData.Level then
        self._wtLinePanel:SetVisibility(ESlateVisibility.Collapsed)
    else
        self._wtLinePanel:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    end
    --首尾缝隙处理(第一个显示控件)
    if self._wtHeadAndTailImg then
        if itemData.Level == 1 then
            self._wtHeadAndTailImg:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        else
            self._wtHeadAndTailImg:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
    --初始化数据
    if itemData.Level then
        --是否当前为解锁
        if curLevel + 1 == itemData.Level then
            self._isCurLevel = true
        end
        --是否解锁
        self._isUnLock = curLevel >= itemData.Level
        self._wtLvlText:SetText(itemData.Level)
    end
    if self.SetImage then
        local isUnLock = self._isUnLock and 1 or 0
        self:SetImage(isUnLock)
    end
    --刷新
    self:_RefreshPropList()
    --初始化item
    if callBack.GetPropData then
        if self.itemData == callBack.GetPropData() then
            self._isClick = true
        end
    end
    --等all的item添加完成后在重新刷新状态
    if itemData.Level == curLevel then
        if callBack.GetIsItem then
            local isBool = callBack.GetIsItem()
            if isBool then
                self:_OnClicked()
                return
            end
        end
    else
        if curLevel > maxLevel and itemData.Level == maxLevel then
            if callBack.GetIsItem then
                local isBool = callBack.GetIsItem()
                if isBool then
                    self:_OnClicked()
                    return
                end
            end
        end
    end
    --初始化按钮状态
    self:_SetItemState()
end

function GunUnlockGrowthRoadItem:_RefreshPropList()
    self._wtShowImage:SetVisibility(ESlateVisibility.Collapsed)

    self:_AddRegisterFrameTask()--清空分帧任务
    self:GetPropItem(-1)--清空sub

    if self.itemData == nil or self._curLevel == nil then
        return
    end
    for index, itemData in ipairs(self.itemData.itemInfo or {}) do
        if self.itemData.Level ~= self._curLevel + 1 then
            if index > 3 then
                self._wtShowImage:SetVisibility(ESlateVisibility.HitTestSelfOnly)
                break
            end
        end
        self:_CreateAndInitItem(itemData, index)
    end
end

function GunUnlockGrowthRoadItem:_AddRegisterFrameTask(itemData, index)
    if itemData and index then
        Facade.LuaFramingManager:RegisterFrameTask(self._CreateAndInitItem, self, {itemData, index})
    else
        Facade.LuaFramingManager:CancelAllFrameTasks()
    end
end

function GunUnlockGrowthRoadItem:_CreateAndInitItem(itemData, index)
    if self._callBack == nil or itemData == nil then
        return
    end
    local listItem = self:GetPropItem(1, index)
    if listItem then
        if DFHD_LUA == 1 then
            listItem:SetItemSize(256, 256)
        else
            listItem:SetItemSize(192, 192)
        end
        listItem:SetPropIcon(itemData)
        --获取MP武器,解决SOL带出武器MP仍未解锁的情况
        if self:_IsWeaponUnLock(itemData) then
            listItem:IsUnLock(true)
        else
            listItem:IsUnLock(self._isUnLock)
        end
        listItem:IsChecked(false)
        listItem:IsShowBg(true)
    end
end

--兼容SOL带出武器MP也可以使用的解锁判断
function GunUnlockGrowthRoadItem:_IsWeaponUnLock(prop)
    if prop == nil or prop.battleType ~= 2 then
        return false
    end
    local itemData = prop.itemData
    if itemData == nil or itemData.GetFeature == nil then
        return false
    end
    local weaponFeature = itemData:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsWeapon() then
        local weapon = Server.InventoryServer:GetItemsById(tonumber(itemData.id), ESlotGroup.MPApply)
        if weapon and #weapon > 0 then
            return true
        end
    end
    return false
end

function GunUnlockGrowthRoadItem:GetPropItem(tabType, index)
    local uiNavId = UIName2ID.GunUnlockGrowthRoadListItem
    local container = self._wtPropPanel
    return GunsmithPartUnlockLogic.GetSubUiInst(self, uiNavId, container, tabType, index)
end

function GunUnlockGrowthRoadItem:OnClose()
    self:_AddRegisterFrameTask()--清空分帧任务
    self:GetPropItem()
end

return GunUnlockGrowthRoadItem