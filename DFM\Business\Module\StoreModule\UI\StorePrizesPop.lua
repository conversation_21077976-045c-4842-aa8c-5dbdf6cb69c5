----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class StorePrizesPop : LuaUIBaseView
local StorePrizesPop = ui("StorePrizesPop")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local StoreConfig = Module.Store.Config


--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavScrollingCondition = import("EGPUINavScrollingCondition")
--- END MODIFICATION

function StorePrizesPop:Ctor()
        ---空状态slot
    self._wtEmptySlot = self:Wnd("EmptySlot", UIWidgetBase)

    self._wtCommonPopWin = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked,self)
    self._wtCommonPopWin:BindCloseCallBack(fCallbackIns)
    self._wtCommonPopWin:SetBackgroudClickable(true)

    --概率总览
    self._wtPrizesScrollBox = UIUtil.WndScrollBox(self, "wtPrizesScrollBox", self._OnGetItemCount, self._OnProcessItemWidget)
    self._inPrizeGroups = {}
    self.OnNavBack = nil

    --抽奖历史记录
    self._wtDFWaterfallScrollView_399 = UIUtil.WndWaterfallScrollBox(self, "wtDFWaterfallScrollView_399", self._OnGetHistoryCount, self._OnProcessHistoryWidget)

    self._wtTabGroupBox = UIUtil.WndTabGroupBox(self, "wtDFTabV3GroupBoxClass3Dynamic", self._OnGroupBoxCount,
    self._OnGroupBoxWidget, self._OnGroupBoxIndexChanged)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() and self._wtTabGroupBox then
        self._wtTabKeyIconLeft = self._wtTabGroupBox:Wnd("WBP_GamepadKeyIconBox_Left", HDKeyIconBox)
        self._wtTabKeyIconRight = self._wtTabGroupBox:Wnd("WBP_GamepadKeyIconBox_Right", HDKeyIconBox)
    end
    --- END MODIFICATION

    self.historyToShow = {}
    self.propToHistory = {}
    self._initSuccess = false
    self._mandelId = nil
    self._lotteryPoolID = nil
end

---@param inOnlyShowType EStorePrizesPopShowType
function StorePrizesPop:OnInitExtraData(fCallbackIns , mandelId, isHideShowSwitchBox , inCustomRewardsLsit)
    self._initSuccess = false
    self._mandelId = mandelId
    self._customRewardsLsit = inCustomRewardsLsit
    self:_ProcessCustomRewards()
    self._fCloseCallbackIns = fCallbackIns

    if self.SetType then
        if isHideShowSwitchBox then
            self:SetType(1)
            --self._wtTabGroupBox:Collapsed()
        else
            self:SetType(0)
            --self._wtTabGroupBox:Visible()
        end
    end

end

function StorePrizesPop:OnLoadedPrizeGroup(prizeGroup)
    if not prizeGroup then
        logerror("[jobsjunlin]StorePrizesPop:OnLoadedPrizeGroup prizeGroup is nil. mandelId:" .. self._mandelId)
        return
    end
    self._initSuccess = true
    self._inPrizeGroups =  prizeGroup
    self:_BuildHistoryData()
    self:_BuildProbsData()
    self._wtTabGroupBox:SetTabIndex(0)
end

function StorePrizesPop:_ProcessCustomRewards()
    if not self._customRewardsLsit then
        return
    end
    local sumPercentProp = 0
    for k, v in ipairs(self._customRewardsLsit) do
        for i, item in ipairs(v.items) do
            item.percentDisplayProb = item.prob or 0
            sumPercentProp = sumPercentProp + item.percentDisplayProb
        end
    end
    local lastItem = self._customRewardsLsit[#self._customRewardsLsit]
    lastItem = lastItem and lastItem.items and lastItem.items[#lastItem.items]
    if lastItem  then
        lastItem.percentDisplayProb =  100-(sumPercentProp - lastItem.percentDisplayProb)  --向上取整导致概率超1合规修正
        lastItem.percentDisplayProb = lastItem.percentDisplayProb < 0 and 0 or lastItem.percentDisplayProb
    end
end

function StorePrizesPop:OnOpen()
    self._wtCommonPopWin:SetTitle(Module.Store.Config.Loc.MandelTabItemRateView)
    self:AddListeners()

    if self._mandelId then
        local itemInfo = ItemConfigTool.GetItemConfigById(self._mandelId)
        if itemInfo then
            self._lotteryPoolID = itemInfo.ConnectedPool or nil
        end
        
        if not  self._lotteryPoolID then
            return
        end

        local loadPrizeCallback = CreateCallBack(self.OnLoadedPrizeGroup, self)
        Module.Store:LoadBlindboxPrizes(self._mandelId, loadPrizeCallback)    
    else
        self:OnLoadedPrizeGroup(self._customRewardsLsit)
    end
end


function StorePrizesPop:OnClose()
    self:RemoveAllLuaEvent()
    if self._fCloseCallbackIns then
        self._fCloseCallbackIns()
    end
    Module.ItemDetail:CloseAllPopUI()
    self._loaded = false
end


function StorePrizesPop:OnActivate()
    self._wtCommonPopWin:SetTitle(Module.Store.Config.Loc.MandelTabItemRateView)
    self:AddListeners()
end


function StorePrizesPop:OnDeactivate()
    self:RemoveAllLuaEvent()
    if self._fCloseCallbackIns then
        self._fCloseCallbackIns()
    end
    Module.ItemDetail:CloseAllPopUI()
    self._loaded = false
end


function StorePrizesPop:OnShowBegin()
      --设置选中下标
    -- if self._loaded ~= true then
    --     self._loaded = true
    --     self._wtTabGroupBox:SetTabIndex(0)
    -- end

end



function StorePrizesPop:OnShow()
    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self._delayEnableGamepadHandler = Timer.DelayCall(0.01,self._EnableGamepadFeature,self)
        --self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function StorePrizesPop:OnHideBegin()
  if IsHD() then
      self:_DisableGamepadFeature()
  end

  self:_CheckAndCloseTimer()
end
--- END MODIFICATION

function StorePrizesPop:OnAnimFinished(anim)
end


function StorePrizesPop:AddListeners()
    self:AddLuaEvent(Module.Reward.Config.Events.evtOpenCollectionDetailPage, self._SetVisible, self)
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
end


function StorePrizesPop:_OnGetItemCount()
    return #self._prizeGroups
end

---@param groupWidget StorePrizesList
function StorePrizesPop:_OnProcessItemWidget(index, groupWidget)
    local group = self._prizeGroups[index+1]
    if group then
        groupWidget:SetMainId(self._mandelId)
        groupWidget:CheckLoadUIAndSetInfo(group.items, group.title_sub, index == 0 and self._guaranteedProbs or nil , 2, index == 2)
    end
end

function StorePrizesPop:_RefreshWidget()
    if not self._initSuccess then
        logerror("[jobsjunlin]StorePrizesPop:_RefreshWidget _initSuccess is false. mandelId:" .. self._mandelId)
        return
    end

    self._wtCommonPopWin:SetTitle(Module.Store.Config.Loc.Unbox)
    self:_SetEmptyStyle(false)
    self:_CheckAndCloseTimer()

    if  self.curSelectGroupIndex == 0 then
        self._wtDFWaterfallScrollView_399:Collapsed()
        self._wtPrizesScrollBox:Visible()
        self._wtPrizesScrollBox:RefreshAllItems()
        self._scrollToStartTimer = Timer.DelayCall(0.5, function()
            self._wtPrizesScrollBox:ScrollToStart()
        end)
        Timer.DelayCall(0.5, function()
           --self:_AutoFocuseItem()
        end, self)
    elseif self.curSelectGroupIndex == 1 then
        self._wtDFWaterfallScrollView_399:Visible()
        self._wtPrizesScrollBox:Collapsed()
        self._wtDFWaterfallScrollView_399:RefreshAllItems()
        self._wtDFWaterfallScrollView_399:ScrollToStart()
        local bIsEmpty = #self.historyToShow == 0
        self:_SetEmptyStyle(bIsEmpty)
        Timer.DelayCall(0.5, function()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._historyListGroup)
        end, self)
    end
end

function StorePrizesPop:_AutoFocuseItem()
    if WidgetUtil.IsGamepad() then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._prizesListGroup)
        local curItemWidget = self._wtPrizesScrollBox:GetItemByIndex(1)
        if curItemWidget then
            WidgetUtil.SetUserFocusToWidget(curItemWidget,false)
        end
    end
end

function StorePrizesPop:_CheckAndCloseTimer()
    if self._scrollToStartTimer then
        Timer.CancelDelay(self._scrollToStartTimer)
        self._scrollToStartTimer = nil
    end
    if self._delayEnableGamepadHandler then
        Timer.CancelDelay(self._delayEnableGamepadHandler)
        self._delayEnableGamepadHandler = nil
    end
    
end

function StorePrizesPop:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function StorePrizesPop:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end

function StorePrizesPop:_SetVisible(bStackUIVisible,eType)
    if bStackUIVisible == true then
        self:Hide(true)
    else
        self:Show(false)
    end
end

function StorePrizesPop:_OnTabPressed()
    Facade.UIManager:CloseUI(self)
end

function StorePrizesPop:_OnGetHistoryCount()
    return #self.historyToShow or 0
end

---@param widget StoreSweepHistory
function StorePrizesPop:_OnProcessHistoryWidget(index,widget)
    local prop = self.historyToShow[index]
    local historyInfo = self.propToHistory[index]
    if self._boxOpenCount then
        widget:SetInfo(self._boxOpenCount - index+1, historyInfo, prop)
    else
        widget:SetInfo(#self.historyToShow - index+1, historyInfo, prop)
    end

    if widget.SetStyle then
        widget:SetStyle(1)
    end

    if widget.SetBg then
        local hasBg = index % 2 == 0
        widget:SetBg(hasBg and 0 or 1)
    end
end

function StorePrizesPop:_OnGroupBoxCount()
    return 2
end

function StorePrizesPop:_OnGroupBoxWidget(position, itemWidget)

    if position == 0 then
        itemWidget:SetMainTitle(StoreConfig.Loc.MandelTabItemRateView)
    else
        itemWidget:SetMainTitle(StoreConfig.Loc.MandelTabHistory)
    end

    --是否解锁显示
    -- itemWidget:SetIsLocked(false)

end

function StorePrizesPop:_OnGroupBoxIndexChanged(position)
    if not self._initSuccess then
        return
    end
    if position == 0 then
        self.curSelectGroupIndex = 0
        LogAnalysisTool.SignButtonClicked(10153003)

    else
        self.curSelectGroupIndex = 1
        LogAnalysisTool.SignButtonClicked(10153002)
    end
    self:_RefreshWidget()
    Module.ItemDetail:CloseAllPopUI()
    --swap page
end

function StorePrizesPop:_BuildHistoryData()
    if not self._lotteryPoolID then
        return
    end
    local boxinfo = Server.StoreServer:GetBoxInfo(self._lotteryPoolID)
    self._boxOpenCount = boxinfo and boxinfo.info_list and boxinfo.info_list[1] and boxinfo.info_list[1].open_count
    local history = Server.StoreServer:GetLotteryRecordsByLotteryId(self._lotteryPoolID)
    self.historyToShow = {}
    self.propToHistory = {}

    if history == nil or #history == 0 then
        return
    end

    table.sort(history, function(a, b)
        return a.open_time > b.open_time
    end)

    local index = 0
    for historyid, historyInfo in pairs(history) do
        -- body
        if historyInfo.add_props ~= nil and #historyInfo.add_props > 0 then
            local tReverseAddPropsTable = {}
            for k,v in pairs(historyInfo.add_props) do
                table.insert(tReverseAddPropsTable, 1, v)
            end
            for k,v in pairs(tReverseAddPropsTable) do
                table.insert(self.historyToShow, v)
                index = index + 1
                self.propToHistory[index] = historyInfo
            end
        end
    end

end


function StorePrizesPop:_BuildProbsData()
    local bHasRestore = false
    self._prizeGroups = {}
    self._guaranteedProbs = {}
    local tempGroup = {}
    for index, group in ipairs(self._inPrizeGroups) do
        table.insert(tempGroup, group)
        for index, item in ipairs(group.items) do
            if item.restore_flag ~= nil and item.restore_flag == false then
                bHasRestore = true
            end
            if item.prob_showed and item.prob_showed > 0 then
                local propName = string.format('<customstyle color="Color_Quality%s">%s</>', string.format("%02d",item.quality-1), item.name)
                table.insert(self._guaranteedProbs, string.format(Module.Store.Config.Loc.GuaranteedProb, propName, string.format("%.2f", item.prob_showed)))
            end
        end
    end

    self._prizeGroups = tempGroup
    if #self._guaranteedProbs > 0 then
        table.insert(self._guaranteedProbs, Module.Store.Config.Loc.GuaranteedProbDesc)
        table.insert(self._prizeGroups,1,{items={},title_sub=Module.Store.Config.Loc.CorePrizeAverageProb})
    end

end

function StorePrizesPop:_SetEmptyStyle(bIsEmpty)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
    if bIsEmpty then
        self._wtEmptySlot:Visible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            uiIns:BP_SetTypeWithParam(1)
            uiIns:Visible()
            uiIns:BP_SetText(Module.Store.Config.Loc.NothingContent)
        end
    else
        self._wtEmptySlot:Collapsed()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function StorePrizesPop:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end
 
    -- tab按键绑定
    if self._wtTabGroupBox then
        self._SelectNextTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab_Trigger", EInputEvent.IE_Pressed
                    , self._wtTabGroupBox.OnNext, self._wtTabGroupBox, EDisplayInputActionPriority.UI_Pop)
        self._SelectPrevTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab_Trigger", EInputEvent.IE_Pressed
                    , self._wtTabGroupBox.OnPrev, self._wtTabGroupBox, EDisplayInputActionPriority.UI_Pop)
    end

    if self._wtTabKeyIconLeft then
        self._wtTabKeyIconLeft:SelfHitTestInvisible()
        self._wtTabKeyIconLeft:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconLeft:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0, false)
    end

    if self._wtTabKeyIconRight then
        self._wtTabKeyIconRight:SelfHitTestInvisible()
        self._wtTabKeyIconRight:SetOnlyDisplayOnGamepad(true)
        self._wtTabKeyIconRight:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0, false)
    end

    -- 抽奖历史导航
    self._historyListGroup = WidgetUtil.RegisterNavigationGroup(self._wtDFWaterfallScrollView_399, self, "Hittest")
    if self._historyListGroup then
        self._historyListGroup:AddNavWidgetToArray(self._wtDFWaterfallScrollView_399)
        self._historyListGroup:SetScrollRecipient(self._wtDFWaterfallScrollView_399)
        self._historyListGroup:MarkIsStackControlGroup()
    end

    -- 概率总览导航
    self._prizesListGroup = WidgetUtil.RegisterNavigationGroup(self._wtPrizesScrollBox, self, "Hittest")
    if self._prizesListGroup then
        self._prizesListGroup:AddNavWidgetToArray(self._wtPrizesScrollBox)
        self._prizesListGroup:SetScrollRecipient(self._wtPrizesScrollBox)
        self._prizesListGroup:MarkIsStackControlGroup()
        ---popui下栈ui onshow慢于popui需要异步
        Timer.DelayCall(0.05, function()
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._prizesListGroup)
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        end, self)
    end
end

function StorePrizesPop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._SelectNextTabHandle then
        self:RemoveInputActionBinding(self._SelectNextTabHandle)
    end

    if self._SelectPrevTabHandle then
        self:RemoveInputActionBinding(self._SelectPrevTabHandle)
    end
    
    WidgetUtil.RemoveNavigationGroup(self)
    self._prizesListGroup = nil
    self._historyListGroup = nil
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

end
--- END MODIFICATION

return StorePrizesPop
