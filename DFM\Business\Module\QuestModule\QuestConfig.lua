----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

------------------- SOL任务系统 -------------------
-- 任务系统主界面
UITable[UIName2ID.QuestMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestMainPanel"),
    BPKey = "WBP_QuestMainPanel",
    SubUIs = {
        UIName2ID.QuestChapterItem
    },
    Anim = {
        FlowInAni = "WBP_TaskChapter_in",
        FlowOutAni = "WBP_TaskChapter_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.QuestChapterItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestChapterItem"),
    BPKey = "WBP_QuestChapterItem",
    SubUIs = {
        UIName2ID.QuestLockStateItem
    }
    -- Anim = {
    -- 	bManuelAnim = true,
    -- 	-- FlowInAni = "WBP_TaskChapter_Item_in",
    -- 	-- FlowOutAni = "WBP_TaskChapter_Item_out"
    -- }
}
UITable[UIName2ID.QuestLockStateItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestLockStateItem"),
    BPKey = "WBP_QuestLockStateItem"
}

-- 任务线界面
UITable[UIName2ID.QuestLinePanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestLinePanel"),
    BPKey = "WBP_QuestLinePanel",
    SubUIs = {
        UIName2ID.QuestLineItem,
        UIName2ID.QuestLineOneComponent,
        UIName2ID.QuestLineTwoComponent,
        UIName2ID.QuestLineThreeComponent,
        UIName2ID.QuestLineFourComponent,
        UIName2ID.QuestSeasonLineHeadEntry
    },
    Anim = {
        FlowInAni = "WBP_TaskChapterCircleProgress_in",
        FlowOutAni = "WBP_TaskChapterCircleProgress_out"
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true
    }
}
UITable[UIName2ID.QuestLineItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.LinePanel.QuestLineItemBase"),
    BPKey = "WBP_QuestLineItem",
    SubUIs = {
        UIName2ID.QuestLineItemTip,
        UIName2ID.IVCommonItemTemplate
    }
}

UITable[UIName2ID.QuestLineItemTip] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonTipsModule.UI.Assembled_CommonTipsFrame"),
    BPKey = "WBP_CommonAssembledTipsFrame",
    SubUIs = {
        UIName2ID.Assembled_CommonMessageTips_V1,
        UIName2ID.Assembled_CommonMessageTips_V2,
        UIName2ID.Assembled_CommonHDKeyTips_V1,
        UIName2ID.Assembled_CommonKeyTips_V2
    }
}

UITable[UIName2ID.QuestLineOneComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.LinePanel.QuestLineItemComponent"),
    BPKey = "WBP_QuestLineOneComponent",
    Anim = {
        FlowInAni = "WBP_TaskChapterComponent_New_1_in",
    }
}
UITable[UIName2ID.QuestLineTwoComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.LinePanel.QuestLineItemComponent"),
    BPKey = "WBP_QuestLineTwoComponent",
    Anim = {
        FlowInAni = "WBP_TaskChapterComponent_New_2_in",
    }
}
UITable[UIName2ID.QuestLineThreeComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.LinePanel.QuestLineItemComponent"),
    BPKey = "WBP_QuestLineThreeComponent",
    Anim = {
        FlowInAni = "WBP_TaskChapterComponent_New_3_in",
    }
}

UITable[UIName2ID.QuestLineFourComponent] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.LinePanel.QuestLineItemComponent"),
    BPKey = "WBP_QuestLineFourComponent",
    Anim = {
        FlowInAni = "WBP_TaskChapterComponent_New_4_in",
    }
}

-- 任务详情界面
UITable[UIName2ID.QuestDetailView] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestDetailView"),
    BPKey = "WBP_QuestDetailView",
    SubUIs = {
        UIName2ID.QuestMapDetailItem,
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.QuestDetailPanel
    },
    Anim = {
        bManuelAnim = true
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.QuestDetailItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestDetailItem"),
    BPKey = "WBP_QuestDetailItem",
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.QuestTaskDetailItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestTaskDetailItem"),
    BPKey = "WBP_TaskDetail_Item",
    SubUIs = {
        UIName2ID.QuestObjectiveItem,
        UIName2ID.QuestConditionItem
    },
    Anim = {
        FlowInAni = "WBP_TaskDetail_Item_in",
        FlowOutAni = "WBP_TaskDetail_Item_out"
    }
}

UITable[UIName2ID.QuestObjectiveItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestObjectiveItem"),
    BPKey = "WBP_QuestObjectiveItem",
    SubUIs = {
        UIName2ID.WeaponRestrictionsComponent
    }
}

UITable[UIName2ID.QuestConditionItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestConditionItem"),
    BPKey = "WBP_TaskDetail_ChallengeItem"
}

UITable[UIName2ID.QuestUnlockLinePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestUnlockLinePanel"),
    BPKey = "WBP_TaskChapter_UnlockTopBar",
    SubUIs = {
        UIName2ID.QuestUnlockItem
    }
}

UITable[UIName2ID.QuestUnlockItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestUnlockItem"),
    BPKey = "WBP_TaskChapter_UnlockItem"
}

UITable[UIName2ID.QuestMapDetailItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestMapDetailItem"),
    BPKey = "WBP_QuestMapDetailItem"
}

-- 任务CG详情界面
UITable[UIName2ID.QuestCGView] = {
    UILayer = EUILayer.Top,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestDetailView"),
    BPKey = "WBP_QuestCGView"
}

UITable[UIName2ID.QuestObjectiveCompoDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestObjectiveCompoDesc"),
    BPKey = "WBP_QuestObjectiveCompoDesc"
}

-- 任务描述界面
UITable[UIName2ID.QuestDescView] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestDescView"),
    BPKey = "WBP_QuestDescView",
    Anim = {
        FlowInAni = "WBP_TaskDetail_Description_in",
        FlowOutAni = "WBP_TaskDetail_Description_out"
    },
    IsModal = true,
}

UITable[UIName2ID.QuestUpdateTips] = {
    UILayer = EUILayer.Tip,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestUpdateTips"),
    BPKey = "WBP_QuestTargetUpdate"
}

UITable[UIName2ID.QuestUpdateTips_PC] = {
    UILayer = EUILayer.Tip,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestUpdateTips"),
    BPKey = "WBP_QuestTargetUpdate_PC"
}

-- TODO DELETE
-- 任务奖励预览界面
UITable[UIName2ID.QuestRewardView] = {
    UILayer = EUILayer.Top,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestRewardView"),
    BPKey = "WBP_QuestRewardView"
}

------------------- 大世界任务系统 -------------------
-- 任务系统主界面
-- UITable[UIName2ID.BigWorldQuestMainPanel] = {
-- 	UILayer = EUILayer.Stack,
-- 	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.BigWorld.BigWorldQuestMainPanel"),
-- 	BPKey = "WBP_BigWorldQuestMainPanel",
-- 	SubUIs = {
-- 		UIName2ID.BigWorldQuestItem,
-- 		UIName2ID.BigWorldQuestObjectiveItem
-- 	}
-- }
-- UITable[UIName2ID.BigWorldQuestItem] = {
-- 	UILayer = EUILayer.Sub,
-- 	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.BigWorld.BigWorldQuestItem"),
-- 	BPKey = "WBP_BigWorldQuestItem",
-- 	SubUIs = {
-- 		UIName2ID.BigWorldQuestObjectiveItem,
-- 		UIName2ID.RewardItem
-- 	}
-- }
-- UITable[UIName2ID.BigWorldQuestObjectiveItem] = {
-- 	UILayer = EUILayer.Sub,
-- 	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.BigWorld.BigWorldQuestObjectiveItem"),
-- 	BPKey = "WBP_BigWorldQuestObjectiveItem",
-- 	SubUIs = {
-- 		UIName2ID.ItemsSubmitItem,
-- 	}
-- }

-------------------- 大世界任务提交物品 --------------------------------
-- UITable[UIName2ID.MissionTurnInItemPanel] = {
-- 	UILayer = EUILayer.Pop,
-- 	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.BigWorld.MissionTurnInItems.MissionTurnInItemPanel"),
-- 	BPKey = "WBP_MissionTurnInItems",
-- 	SubUIs = {
-- 		UIName2ID.MissionTurnInItemBox
-- 	}
-- }

-- UITable[UIName2ID.MissionTurnInItemBox] = {
-- 	UILayer = EUILayer.Sub,
-- 	LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.BigWorld.MissionTurnInItems.MissionTurnInItemsBox"),
-- 	BPKey = "WBP_MissionTurnInItemsBox",
-- }



------------------- SOL赛季任务 ----------------------
UITable[UIName2ID.QuestSeasonalTutorial] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonalTutorial"),
    BPKey = "WBP_SeasonalTasks_FirstTime_Pop",
    SubUIs = {
        UIName2ID.QuestSeasonalTutorialItemBase,
        UIName2ID.DFButtonCarousel2,
        UIName2ID.QuestSeasonalTutorialItem1,
        UIName2ID.QuestSeasonalTutorialItem2,
        UIName2ID.QuestSeasonalTutorialItem3,
        UIName2ID.QuestSeasonalTutorialItem4,
        UIName2ID.QuestSeasonalTutorialItem5,
    },
    IsModal = true,
}

UITable[UIName2ID.QuestSeasonalTutorialItem1] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonalTutorialItem"),
    BPKey = "WBP_SeasonalTasks_FirstTimePopItem_01",
}

UITable[UIName2ID.QuestSeasonalTutorialItem2] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonalTutorialItem"),
    BPKey = "WBP_SeasonalTasks_FirstTimePopItem_02",
}

UITable[UIName2ID.QuestSeasonalTutorialItem3] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonalTutorialItem"),
    BPKey = "WBP_SeasonalTasks_FirstTimePopItem_03",
}

UITable[UIName2ID.QuestSeasonalTutorialItem4] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonalTutorialItem"),
    BPKey = "WBP_SeasonalTasks_FirstTimePopItem_04",
}

UITable[UIName2ID.QuestSeasonalTutorialItem5] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonalTutorialItem"),
    BPKey = "WBP_SeasonalTasks_FirstTimePopItem_05",
}

if IsHD() then 
    UITable[UIName2ID.QuestSeasonMainPanel] = {
        UILayer = EUILayer.Stack,
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonMainPanel"),
        BPKey = "WBP_SeasonalTasks_Main",
        SubUIs = {
            UIName2ID.QuestSeasonEntranceMain,
            UIName2ID.QuestSeasonEntranceSub,
            UIName2ID.QuestSeasonConditionItem
        },
        Anim = {
            FlowInAni = "WBP_SeasonalTasks_Main_in",
        },
        ReConfig = {
            IsPoolEnable = true
        }
    }
else
    UITable[UIName2ID.QuestSeasonMainPanel] = {
        UILayer = EUILayer.Sub,
        LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonMainPanel"),
        BPKey = "WBP_SeasonalTasks_Main",
        SubUIs = {
            UIName2ID.QuestSeasonEntranceMain,
            UIName2ID.QuestSeasonEntranceSub,
            UIName2ID.QuestSeasonConditionItem
        },
        Anim = {
            FlowInAni = "WBP_SeasonalTasks_Main_in",
        },
    }
end


UITable[UIName2ID.QuestSeasonEntranceMain] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonEntranceMain"),
    BPKey = "WBP_SeasonalTasks_Entrance_01",
    SubUIs = {
        UIName2ID.QuestSeasonDot
    }
}

UITable[UIName2ID.QuestSeasonDot] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonDot"),
    BPKey = "WBP_SeasonalTasks_Dot",
}

UITable[UIName2ID.QuestSeasonEntranceSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonEntranceSub"),
    BPKey = "WBP_SeasonalTasks_Entrance_02",
}

UITable[UIName2ID.QuestSeasonConditionItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonConditionItem"),
    BPKey = "WBP_SeasonalTasks_ConditionItem",
}


UITable[UIName2ID.QuestSeasonCollectionPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.Collection.QuestSeasonCollectionPanel"),
    BPKey = "WBP_SeasonalTasks_EntrustmentPanel",
    SubUIs = {
        UIName2ID.QuestSeasonCollectorItem,
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.QuestSeasonCollectorItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.Collection.QuestSeasonCollectorItem"),
    BPKey = "WBP_SeasonalTasks_EntrustmentItem",
}

UITable[UIName2ID.QuestSeasonCollectorRewardPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.Collection.QuestSeasonCollectorRewardPanel"),
    BPKey = "WBP_Hero_RewardsOverview",
    IsModal = true,
}

UITable[UIName2ID.QuestSeasonFactContractPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.Fate.QuestSeasonFactContractPanel"),
    BPKey = "WBP_SeasonalTasks_ContractPanel",
    SubUIs = {
        UIName2ID.QuestSeasonCollectorItem,
    },
    Anim = {
        FlowInAni = "WBP_SeasonalTasks_ContractPanel_in",
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.QuestSeasonFactContractCard] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.Fate.QuestSeasonFactContractCard"),
    BPKey = "WBP_SeasonalTasks_ContractCard",
}

UITable[UIName2ID.QuestSeasonFactContractDetail] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.Fate.QuestSeasonFactContractDetail"),
    BPKey = "WBP_SeasonalTasks_ContractDetail",
    SubUIs = {
        UIName2ID.QuestDetailPanel,
    },
    ReConfig = {
        IsPoolEnable = true
    },
    Anim = {
        bManuelAnim = true
    }
}



UITable[UIName2ID.QuestSeasonListPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonListPanel"),
    BPKey = "WBP_SeasonalTasks_ListPanel",
    SubUIs = {
        UIName2ID.QuestSeasonActionPhase,
        UIName2ID.QuestSeasonListNormal,
        UIName2ID.QuestSeasonListLocked
    },
    Anim = {
        FlowInAni = "WBP_SeasonalTasks_ListPanel",
    },
}

UITable[UIName2ID.QuestSeasonActionPhase] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonActionPhase"),
    BPKey = "WBP_SeasonalTasks_ActionPhase",
}

UITable[UIName2ID.QuestSeasonListNormal] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonListNormal"),
    BPKey = "WBP_SeasonalTasks_ListNormal",
    SubUIs = {
        UIName2ID.QuestDetailPanel,
    },
    Anim = {
        FlowInAni = "WBP_SeasonalTasks_ListNormal_in"
    }
}

UITable[UIName2ID.QuestDetailPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.SOL.QuestDetailPanel"),
    BPKey = "WBP_QuestDetailPanel",
    SubUIs = {
        UIName2ID.QuestTaskDetailItem
    }
}

UITable[UIName2ID.QuestSeasonListLocked] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonListLocked"),
    BPKey = "WBP_SeasonalTasks_ListLocked",
    SubUIs = {
        UIName2ID.QuestSeasonConditionItem
    },
    Anim = {
        FlowInAni = "WBP_SeasonalTasks_ListLocked"
    }
}

UITable[UIName2ID.QuestSeasonTaskSet] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonTaskSet"),
    BPKey = "WBP_SeasonalTasks_TaskSet",
    SubUIs = {
        UIName2ID.QuestSeasonTaskHead,
        UIName2ID.QuestSeasonTaskEntry
    }
}

UITable[UIName2ID.QuestSeasonTaskHead] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonTaskHead"),
    BPKey = "WBP_SeasonalTasks_TaskEntry_V1",
}

UITable[UIName2ID.QuestSeasonTaskEntry] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonTaskEntry"),
    BPKey = "WBP_SeasonalTasks_TaskEntry_V2",
}

UITable[UIName2ID.QuestSeasonLineHeadEntry] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.Season.QuestSeasonLineHeadEntry"),
    BPKey = "WBP_SeasonalTasks_BigEntry",
}


------------------- 通用接口 -------------------
UITable[UIName2ID.CGPlayPanel] = {
    UILayer = EUILayer.Top,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.CGPlayPanel"),
    BPKey = "WBP_CGPlayPanel"
}

UITable[UIName2ID.NPCRoleInfoPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "QuestModule.UI.NPCRoleInfoPanel"),
    BPKey = "WBP_TaskDetailRoleDescription",
    Anim = {
        FlowInAni = "WBP_TaskDetailRoleDescription_in",
        FlowOutAni = "WBP_TaskDetailRoleDescription_out"
    },
    IsModal = true,
}

local QuestConfig = {
    EEnterToDetailType = {
        None = 0,
        Chapter = 1,
        Jump = 2
    },
    EQuestSeasonType = {
        Hard = 1,
        Easy = 2,
    },
    EQuestSeasonGuideType = {
        Main = 1,
        Collection = 2,
        FateContract = 3
    },
    EQuestSeasonConditionType = {
        Star = 1,
        Collection = 2,
        Fate = 3,
    },
    QuestlineId2Jumpid = {
        [1001] = 1609100001,
        [1002] = 1609100002,
        [1003] = 1609100003,
        [1004] = 1609100004,
        [1006] = 1609100005,
    },
    
    QuestSeasonListPanelJumpID = 161710001,
    QuestSeasonMainJumpID = 161700001,

    Loc = {
        quest = NSLOCTEXT("QuestModule", "Lua_Quest", "任务"),
        mission = NSLOCTEXT("QuestModule", "Lua_Quest_Mission", "主线任务"),
        trader = NSLOCTEXT("QuestModule", "Lua_Quest_Trader", "军需处任务"),
        finish = NSLOCTEXT("QuestModule", "Lua_Quest_Finish", "完成"),
        watched = NSLOCTEXT("QuestModule", "Lua_Quest_Watched", "已观看"),
        receive = NSLOCTEXT("QuestModule", "Lua_Quest_Receive", "领取奖励"),
        accept = NSLOCTEXT("QuestModule", "Lua_Quest_Accept", "接取"),
        rewardNoGet = NSLOCTEXT("QuestModule", "Lua_Quest_RewardNoGet", "前置奖励未领取"),
        unAccept = NSLOCTEXT("QuestModule", "Lua_Quest_UnAccept", "待接取"),
        Received = NSLOCTEXT("QuestModule", "Lua_Quest_Received", "已领取"),
        Paused = NSLOCTEXT("QuestModule", "Lua_Quest_Paused", "暂停"),
        Tracing = NSLOCTEXT("QuestModule", "Lua_Quest_Tracing", "追踪"),
        UnTracing = NSLOCTEXT("QuestModule", "Lua_Quest_UnTracing", "取消追踪"),
        LvlLimitPureTxt = NSLOCTEXT("QuestModule", "Lua_Quest_LvlLimitPureTxt", "行动等级%d级解锁"),
        WatchFinish = NSLOCTEXT("QuestModule", "Lua_Quest_WatchFinish", "观看完成"),
        questFinish = NSLOCTEXT("QuestModule", "Lua_Quest_QuestFinish", "任务完成"),
        SubmitFailed = NSLOCTEXT("QuestModule", "Lua_Quest_SubmitFailed", "没有满足条件的提交道具..."),
        SubmitConfirm = NSLOCTEXT("QuestModule", "Lua_Quest_SubmitConfirm", "确认提交%sx%d吗?"),
        SubmitEquippedConfirm = NSLOCTEXT("QuestModule", "Lua_Quest_SubmitEquippedConfirm", "有%s处于装备中，是否确定提交?"),
        closeText = NSLOCTEXT("QuestModule", "Lua_Quest_closeText", "关闭"),
        underWay = NSLOCTEXT("QuestModule", "Lua_Quest_underWay", "进行中"),
        rewardReceived = NSLOCTEXT("QuestModule", "Lua_Quest_rewardReceived", "奖励已领取"),
        restart = NSLOCTEXT("QuestModule", "Lua_Quest_Restart", "重新开始"),
        submit = NSLOCTEXT("QuestModule", "Lua_Quest_Submit", "提交物品"),
        watchCgGetReward = NSLOCTEXT("QuestModule", "Lua_Quest_watchCgGetReward", "观看完CG领取奖励"),
        trackObjectiveFail = NSLOCTEXT("QuestModule", "Lua_Quest_TrackObjectiveFail", "单地图同时追踪数量已达上限"),
        questRewardFullToMail = NSLOCTEXT("QuestModule", "Lua_Quest_QuestRewardFullToMail", "仓库容量已满，任务奖励将通过邮件发放"),
        questCurrencyFullToMail = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCurrencyFullToMail", "货币已达上限，任务奖励将通过邮件发放"),
        questRewardCurrencyBothFullToMail = NSLOCTEXT("QuestModule","Lua_Quest_QuestRewardCurrencyBothFullToMail","仓库及货币容量已满，任务奖励将通过邮件发放"),
        openQuestLineSucc = NSLOCTEXT("QuestModule", "Lua_Quest_OpenQuestLineSucc", "成功解锁新章节"),
        showQuestLineSucc = NSLOCTEXT("QuestModule", "Lua_Quest_ShowQuestLineSucc", "解锁新章节"),
        showQuestSucc = NSLOCTEXT("QuestModule", "Lua_Quest_ShowQuestSucc", "解锁隐藏任务"),
        noItemsToSubmit = NSLOCTEXT("QuestModule", "Lua_Quest_noItemsToSubmit", "未持有任务道具"),
        MissionCommit = NSLOCTEXT("QuestModule", "Lua_Mission_Commit", "上交"),
        MissionNoItems = NSLOCTEXT("QuestModule", "Lua_Mission_NoItems", "未获取任务道具"),
        MissionNoAllItems = NSLOCTEXT("QuestModule", "Lua_Mission_NoAllItems", "未获取全部任务道具"),
        underDevelopment = NSLOCTEXT("QuestModule", "Lua_Quest_UnderDevelopment", "功能开发中"),
        questLineLocked = NSLOCTEXT("QuestModule", "Lua_Quest_QuestLineLocked", "任务章节尚未解锁，需要先完成前置任务"),
        questLineLocked2 = NSLOCTEXT("QuestModule", "Lua_Quest_QuestLineLocked2", "此章节未解锁"),
        OpenLimitFormat1 = NSLOCTEXT("QuestModule", "Lua_Quest_OpenLimitFormat1", "行动等级达到%d"),
        OpenLimitFormat2 = NSLOCTEXT("QuestModule", "Lua_Quest_OpenLimitFormat2", "行动等级达到%d(已完成)"),
        idFormat = NSLOCTEXT("QuestModule", "Lua_Quest_idFormat", "%s（ID:%d）"),
        objectiveFinish = NSLOCTEXT("QuestModule", "Lua_Quest_ObjectiveFinish", "已完成"),
        timeLimit = NSLOCTEXT("QuestModule", "Lua_Quest_TimeLimit", "限时"),
        levelText = NSLOCTEXT("QuestModule", "Lua_Quest_LevelText", "LV.%s"),
        decimalsText = NSLOCTEXT("QuestModule", "Lua_Quest_DecimalsText", "%.1f"),
        percentText = NSLOCTEXT("QuestModule", "Lua_Quest_PercentText", "%.1f%%"),
        progressText = NSLOCTEXT("QuestModule", "Lua_Quest_ProgressText", "%s/%s"),

        noUnderwayQuest = NSLOCTEXT("QuestModule", "Lua_Quest_NoUnderwayQuest", "此章节暂无进行中的任务"),
        noRewadUnaAcceptedQuest = NSLOCTEXT("QuestModule", "Lua_Quest_NoRewadUnaAcceptedQuest", "此章节暂无待接取的任务"),
        noUnRewardedQuest = NSLOCTEXT("QuestModule", "Lua_Quest_NoUnRewardedQuest", "此章节暂无待领奖的任务"),
        allQuestsDone = NSLOCTEXT("QuestModule", "Lua_Quest_allQuestsDone", "此章节任务全部完成，敬请期待"),
        FinishQuestFormat1 = NSLOCTEXT("QuestModule", "Lua_Quest_FinishQuestFormat1", "完成任务[%s]"),
        FinishQuestFormat2 = NSLOCTEXT("QuestModule", "Lua_Quest_FinishQuestFormat2", "完成任务[%s](已完成)"),
        questId = NSLOCTEXT("QuestModule", "Lua_Quest_questId", "(ID:%d)"),
        overtime = NSLOCTEXT("QuestModule", "Lua_Quest_overtime", "已超时"),
        gunUpgradeStation = NSLOCTEXT("QuestModule", "Lua_Quest_gunUpgradeStation", "改枪台"),
        plus = NSLOCTEXT("QuestModule", "Lua_Quest_plus", "+"),
        targetMapLocked = NSLOCTEXT("QuestModule", "Lua_Quest_targetMapLocked", "目标地图暂未开放"),
        objectiveUpdate = NSLOCTEXT("QuestModule", "Lua_Quest_objectiveUpdate", "更新进度"),
        objectiveComplete = NSLOCTEXT("QuestModule", "Lua_Quest_objectiveComplete", "目标完成"),
        objectiveReset = NSLOCTEXT("QuestModule", "Lua_Quest_objectiveReset", "目标重置"),
        questFailed = NSLOCTEXT("QuestModule", "Lua_Quest_QuestFailed", "任务失败"),
        underWayQuestCount = NSLOCTEXT("QuestModule", "Lua_Quest_underWayQuestCount", "进行中[%d]"),
        unAcceptQuestCount = NSLOCTEXT("QuestModule", "Lua_Quest_unAcceptQuestCount", "待接取[%d]"),
        RewardingQuestCount = NSLOCTEXT("QuestModule", "Lua_Quest_RewardingQuestCount", "可领奖[%d]"),
        officer = NSLOCTEXT("QuestModule", "Lua_Quest_officer", "%s长官"),
        prestige = NSLOCTEXT("QuestModule", "Lua_Quest_prestige", "声望"),
        basicalDamage = NSLOCTEXT("QuestModule", "Lua_Quest_basicalDamage", "Damage"),
        advantageousRange = NSLOCTEXT("QuestModule", "Lua_Quest_advantageousRange", "EffectiveRange"),
        recoilControl = NSLOCTEXT("QuestModule", "Lua_Quest_recoilControl", "RecoilControl"),
        controlSpeed = NSLOCTEXT("QuestModule", "Lua_Quest_controlSpeed", "Handling"),
        weaponStability = NSLOCTEXT("QuestModule", "Lua_Quest_weaponStability", "Stability"),
        waistShot = NSLOCTEXT("QuestModule", "Lua_Quest_waistShot", "WaistShot"),
        TaskLineRefreshTimeDay = NSLOCTEXT("QuestModule", "Lua_Quest_TaskLineRefreshTimeDay", "%d天%d时%d分"),
        TaskLineRefreshTimeHour = NSLOCTEXT("QuestModule", "Lua_Quest_TaskLineRefreshTimeHour", "%d时%d分"),
        TaskLineRefreshTimeMin = NSLOCTEXT("QuestModule", "Lua_Quest_TaskLineRefreshTimeMin", "%d分"),
        TaskLineRefreshTimeSimpleDay = NSLOCTEXT("QuestModule", "Lua_Quest_TaskLineRefreshTimeSimpleDay", "%d天"),
        TaskLineRefreshTimeSimpleHour = NSLOCTEXT("QuestModule", "Lua_Quest_TaskLineRefreshTimeSimpleHour", "%d时"),
        QuestTypeLocMission = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocMission", "主线"),
        QuestTypeLocImportant = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocImportant", "重要"),
        QuestTypeLocBranch = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocBranch", "支线"),
        QuestTypeLocSeason = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocSeason", "赛季"),
        LineQuestsDroDownLoc_All = NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_All", "全部"),
        LineQuestsDroDownLoc_Mission = NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Mission", "主线任务"),
        LineQuestsDroDownLoc_Important = NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Important", "重要任务"),
        LineQuestsDroDownLoc_Branch = NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Branch", "支线任务"),
        LineQuestsDroDownLoc_Season = NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Season", "赛季任务"),
        QuestNeedPre_Mission = NSLOCTEXT("QuestModule", "Lua_Quest_QuestNeedPre_Mission", "需要完成主线任务[%s]"),
        QuestNeedPre_Important = NSLOCTEXT("QuestModule", "Lua_Quest_QuestNeedPre_Important", "需要完成重要任务[%s]"),
        QuestNeedPre_Branch = NSLOCTEXT("QuestModule", "Lua_Quest_QuestNeedPre_Branch", "需要完成支线任务[%s]"),
        QuestNeedPre_Season = NSLOCTEXT("QuestModule", "Lua_Quest_QuestNeedPre_Season", "需要完成赛季任务[%s]"),
        LvlLimitTabPureTxt = NSLOCTEXT("QuestModule", "Lua_Quest_LvlLimitTabPureTxt", "%d级解锁"),
        LvlLimitSimpleTabPureTxt = NSLOCTEXT("QuestModule", "Lua_Quest_LvlLimitSimpleTabPureTxt", "%d级"),
        CanAccept = NSLOCTEXT("QuestModule", "Lua_Quest_CanAccept", "后可接取"),
        QuestCompoleteToGet = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCompoleteToGet", "待领奖"),
        NewQuestOpen = NSLOCTEXT("QuestModule", "Lua_Quest_NewQuestOpen", "新任务发布"),
        QuestUnKnow = NSLOCTEXT("QuestModule", "Lua_Quest_QuestUnKnow", "未知"),
        ResetAllWhenSettlementFailed = NSLOCTEXT("QuestModule","Lua_Quest_ResetAllWhenSettlementFailed","任务过程中如撤离失败 则重置整个任务进度"),
        ResetAllWhenFailed = NSLOCTEXT("QuestModule", "Lua_Quest_ResetAllWhenFailed", "单局任务目标全部完成否则重置整个任务进度"),
        OpenAllQuestText = NSLOCTEXT("QuestModule", "Lua_Quest_OpenAllQuestText", "展开全文"),
        CloseAllQuestText = NSLOCTEXT("QuestModule", "Lua_Quest_CloseAllQuestText", "收起"),
        objectGoToStation = NSLOCTEXT("QuestModule", "Lua_Quest_objectGoToStation", "前往"),
        QuestDeatilView = NSLOCTEXT("QuestModule", "Lua_Quest_QuestDeatilView", "任务详情"),
        NeedSelectSubCount = NSLOCTEXT("QuestModule", "Lua_Quest_NeedSelectSubCount", "请添加提交道具数量"),
        CurSeasonTaskClosed = NSLOCTEXT("QuestModule", "Lua_Quest_CurSeasonTaskClosed", "当前赛季任务已关闭"),
        QuestDescNoLimit = NSLOCTEXT("QuestModule", "Lua_Quest_QuestDescNoLimit", "无要求"),
        NotUnLock = NSLOCTEXT("QuestModule", "Lua_Quest_NotUnLock", "未解锁"),
        GiveUpQuest = NSLOCTEXT("QuestModule", "Lua_Quest_GiveUpQuest", "放弃任务将清空当前任务的已完成进度，放弃后可重新接取，不影响其他已完成任务进度，是否放弃？"),
        GiveUpSubmitQuest = NSLOCTEXT("QuestModule", "Lua_Quest_GiveUpSubmitQuest", "放弃任务将清空当前任务的已完成进度，<customstyle color=\"Color_Highlight01\">*已提交的任务物品将不会返还</>，请谨慎操作，是否放弃？"),
        SubmitConfirmBindTypeTip = NSLOCTEXT("QuestModule", "Lua_Quest_SubmitConfirmBindTypeTip", "确认提交%sx%d吗? \n * 目前选中了<customstyle color=\"Color_Highlight01\">非绑定道具</>，请谨慎选择"),
        -------------------------------------------she3.0
        QuestCollectorTitle = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorTitle", "收集者"),
        QuestCollectorEmpty = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorEmpty", "已完成本赛季所有委托"),
        QuestCollectorRewardProgress = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorRewardProgress", "(%s/%s)赛季累计完成%s次委托"),
        QuestCollectorRefreshCount = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorRefreshCount", "剩余刷新次数:%s"),
        QuestCollectorCompleted = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorCompleted", "已提交"),
        QuestCollectorSubmit = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorSubmit", "提交"),
        QuestCollectorSubmitTip = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorSubmitTip", "确定提交以下物品？"),
        QuestCollectorLockSuccessTip = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorLockSuccessTip", "锁定成功，委托不会被刷新"),
        QuestCollectorAllLockTip = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorAllLockTip", "所有委托都被锁定，无法刷新"),
        ---------------------------------------------------

        QuestTypeLocSeasonMain = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocSeasonMain", "赛季主线"),
        QuestTypeLocSeasonBranch = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocSeasonBranch", "赛季支线"),
        QuestTypeLocSeasonBranchHard = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocSeasonBranchHard", "支线：强攻"),
        QuestTypeLocSeasonBranchEasy = NSLOCTEXT("QuestModule", "Lua_Quest_QuestTypeLocSeasonBranchEasy", "支线：后勤"),
        

        QuestSeasonStarProgress = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonStarProgress", "%d/%d"),

        QuestSeason = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeason", "赛季任务"),
        QuestSeasonCollection = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonCollection", "收集者"),
        QuestSeasonCollectionRefresh = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonCollectionRefresh", "刷新委托"),
        QuestSeasonCollectionProgress = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonCollectionProgress", "赛季完成: %d/%d"),

        QuestSeasonContract = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonContract", "命运契约"),
        QuestSeasonContractProgress = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonContractProgress", "已完成 %d/%d"),
        QuestSeasonContractNone = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonContractNone", "暂未接取契约"),
        QuestSeasonLocked = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonLocked", "%d级解锁赛季任务"),

        QuestSeasonContractAcceptedTip = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonContractAcceptedTip", "同时只允许接取1个"),

        QuestSeasonRemainTime = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonRemainTime", "赛季剩余%d天"),
        QuestSeasonConditionProgress = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonConditionProgress", "(%d/%d)"),

        QuestSeasonStageConditionMission = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonStageConditionMission", "完成上阶段所有主线任务"),
        QuestSeasonStageConditionStar = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonStageConditionStar", "在上阶段行动计划中取得%d星"),
        QuestSeasonLinePanelStar = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonLinePanelStar", "总星数 %d/%d"),

        QuestSeasonOvertimeReward = NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonOvertimeReward", "获得安全箱外观"),

        QuestCollectorLock = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorLock", "锁定"),
        QuestCollectorLockd = NSLOCTEXT("QuestModule", "Lua_Quest_QuestCollectorLockd", "已锁定"),

        QuestAllCollectorLocked = NSLOCTEXT("QuestModule", "Lua_Quest_QuestAllCollectorLocked", "所有委托都被锁定，无法刷新"),
    },
    QuestSeasonTutorialTitleLoc = {
        NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonTutorialTitleLoc_Quest", "赛季任务"),
        NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonTutorialTitleLoc_Collection", "收集者"),
        NSLOCTEXT("QuestModule", "Lua_Quest_QuestSeasonTutorialTitleLoc_Fate", "命运契约"),
    },
    LineQuestsDroDownLoc = {
        NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_All", "全部"),
        NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Mission", "主线任务"),
        NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Important", "重要任务"),
        NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Branch", "支线任务"),
        NSLOCTEXT("QuestModule", "Lua_Quest_LineQuestsDroDownLoc_Season", "赛季任务")
    },
    TabImgPathList = {
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0207.CommonHall_Merchant_Icon_0207'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0208.CommonHall_Merchant_Icon_0208'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0210.CommonHall_Merchant_Icon_0210'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0209.CommonHall_Merchant_Icon_0209'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0206.CommonHall_Merchant_Icon_0206'",
        "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Breakthroug_Icon_0001.Common_Breakthroug_Icon_0001'"
    },
    TabImgPathListNoSeason = {
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0207.CommonHall_Merchant_Icon_0207'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0208.CommonHall_Merchant_Icon_0208'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0210.CommonHall_Merchant_Icon_0210'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0209.CommonHall_Merchant_Icon_0209'",
        "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0206.CommonHall_Merchant_Icon_0206'"
    },
    questReddot = "QuestReddot_%s",

    -- seasonTutorialConfig = {
    --     {
    --         Bp = UIName2ID.QuestSeasonalTutorialItem1,
    --         Title = Module.Quest.Config.Loc.SeasonalTutorialTitle.Page1,
    --         Text = Module.Quest.Config.Loc.SeasonalTutorial.Page1
    --     },
    --     {
    --         Bp = UIName2ID.QuestSeasonalTutorialItem2,
    --         Title = Module.Quest.Config.Loc.SeasonalTutorialTitle.Page2,
    --         Text = Module.Quest.Config.Loc.SeasonalTutorial.Page2
    --     },
    --     {
    --         Bp = UIName2ID.QuestSeasonalTutorialItem3,
    --         Title = Module.Quest.Config.Loc.SeasonalTutorialTitle.Page3,
    --         Text = Module.Quest.Config.Loc.SeasonalTutorial.Page3
    --     },
    -- },

    -- public events
    evtQuestItemSelected = LuaEvent:NewIns("evtQuestItemSelected"),
    evtQuestRewardPrewiewStateUpdate = LuaEvent:NewIns("QuestRewardPrewiewStateUpdate"),
    evtQuestLineChanged = LuaEvent:NewIns("evtQuestLineChanged"),
    evtJumpToQuestLineItem = LuaEvent:NewIns("evtJumpToQuestLineItem"),
    evtQuestChapterLoadFinish = LuaEvent:NewIns("evtQuestChapterLoadFinish"),
    evtQuestChapterBtnClicked = LuaEvent:NewIns("evtQuestChapterBtnClicked"),
    evtQuestItemLoadFinish = LuaEvent:NewIns("evtQuestItemLoadFinish"),
    evtQuestDetailBtnClicked = LuaEvent:NewIns("evtQuestDetailBtnClicked"),
    evtQuestDetailLoadFinish = LuaEvent:NewIns("evtQuestDetailLoadFinish"),
    evtQuestStateChange = LuaEvent:NewIns("evtQuestStateChange"),
    evtQuestTaskItemUpdate = LuaEvent:NewIns("evtQuestTaskItemUpdate"),
    evtQuestMainPanelOnHideBegin = LuaEvent:NewIns("evtQuestMainPanelOnHideBegin"),
    -- BEGIN MODIFICATION @ VIRTUOS : 在quest main panel，这个事件把能点击的按钮发送出去构建navgroup
	evtQuestItemClickable = LuaEvent:NewIns("evtQuestItemClickable"), 
    evtQuestObjectiveClickable = LuaEvent:NewIns("evtQuestObjectiveClickable"),
	-- END MODIFICATION
    -- BigWorld
    evtBigWorldQuestItemSelected = LuaEvent:NewIns("evtBigWorldQuestItemSelected"),
    evtQuestSeasonCollectorTimeRefresh = LuaEvent:NewIns("evtQuestSeasonCollectorTimeRefresh"),
    evtQuestSeasonPhaseItemSelected = LuaEvent:NewIns("evtQuestSeasonPhaseItemSelected"),
    evtQuestSeasonEntryClicked = LuaEvent:NewIns("evtQuestSeasonEntryClicked"),
    evtQuestSeasonHeadClicked = LuaEvent:NewIns("evtQuestSeasonHeadClicked"),
    evtQuestSeasonEntryClickedWithGroupInfo = LuaEvent:NewIns("evtQuestSeasonEntryClickedWithGroupInfo"),
    evtQuestSeasonGroupUnlocked = LuaEvent:NewIns("evtQuestSeasonGroupUnlocked"),
    -- 拓扑图手柄聚焦事件
    evtQuestLinePanelJumpedFinish = LuaEvent:NewIns("evtQuestLinePanelJumpedFinish"),
    evtQuestJumpToDetailPanel = LuaEvent:NewIns("evtQuestJumpToDetailPanel"),
}

return QuestConfig
