----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------




local ActivityMagicTowerAStarNode = {}

function ActivityMagicTowerAStarNode.New(x, y)
    -- -- 参数检查
    -- assert(type(x) == "number", "x must be number")
    -- assert(type(y) == "number", "y must be number")
    
    local o = {
        x = x, y = y,
        g = 0, h = 0, f = 0,
        parent = nil
    }
    
    -- 安全的元表设置
    local mt = { __index = ActivityMagicTowerAStarNode }
    setmetatable(o, mt)
    
    return o
end

return ActivityMagicTowerAStarNode