----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------


local GuideConfig        = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic         = require "DFM.Business.Module.GuideModule.GuideLogic"
local UDFMHudHelper      = import "DFMHudHelper"
local EMediaDownloadType = import "EMediaDownloadType"

local GuideUILogic       = require "DFM.Business.Module.GuideModule.Logic.GuideUILogic"

local JsonFactory        = require "DFM.YxFramework.Plugin.Json.Json"
local Json               = JsonFactory.createJson()


---@class GuidePopWindowUI : LuaUIBaseView
local GuidePopWindowUI = ui("GuidePopWindowUI")

local function log(...) loginfo("[GuidePopWindowUI]", ...) end
local function warn(...) logwarning("[GuidePopWindowUI]", ...) end
local function err(...) logerror("[GuidePopWindowUI]", ...) end

function GuidePopWindowUI:Ctor()
    self._wtRootPanel = self:Wnd("DFCanvasPanel_141", UIWidgetBase)

    self._wtTitle = self:Wnd("wtTitle", UITextBlock)

    self._wtContentImg = self:Wnd("wtContentImg", UIImage)
    self._wtMediaImage = self:Wnd("wtMediaImage", UIMediaImage)
    -- self._wtWebMedia = self:Wnd("DFStreamMediaImage_268", UIWidgetBase)

    self._wtDesc = self:Wnd("wtDesc", UITextBlock)
    self._wtBtnClose = self:Wnd("wtCommonButtonClose", UIButton)
    self._wtBtnClose:Event("OnClicked", self._OnCloseBtnClick, self)
    self._wtNamedSlot = self:Wnd("wtNamedSlot", UIWidgetBase)
    self._wtNamedSlot_VideoComp = self:Wnd("wtNamedSlot_VideoComponent", UIWidgetBase)

    self._wtBtnPre = self:Wnd("wtBtnPre", UIButton)
    self._wtBtnPre:Event("OnClicked", self._OnBtnPreClick, self)
    self._wtBtnNext = self:Wnd("wtBtnNext", UIButton)
    self._wtBtnNext:Event("OnClicked", self._OnBtnNextClick, self)

    self._wtDotBox = self:Wnd("wtDotBox", UILightWidget)

    self._DotIns = {}

    self._wtBtnPlayVideo = self:Wnd("DFButton_0", UIButton)
    self._wtBtnPlayVideo:Event("OnClicked", self._OnClickPlayVideo, self)
    self._wtBtnPlayVideo:SetVisibility(ESlateVisibility.Collapsed)

    self:Reset()
end

--==================================================
--region Life function
function GuidePopWindowUI:Reset()
    ---@type GuidePopWindowUIShowData
    self._showData = nil
    self._bSelfActive = false
    self._videoName = nil
    self._fullScreenVideoName = nil
    self._fullScreenUiHandle = nil
    self._curIdx = 1
    self._bHideHud = false
    self._bWaitImgLoad = false
end

function GuidePopWindowUI:RecycleSelf()
    self:Hide()
    if self._bSelfActive then
        Module.Guide.Field:ReturnCacheSubUI(UIName2ID.GuidePopWindowUI, self)
        self._bSelfActive = false
    end
end

function GuidePopWindowUI:OnOpen()
end

---@return CommonVideoComponent
function GuidePopWindowUI:_TryGetVideoComponent()
    local ins = nil
    if self._videoCompInstanceId then
        local weakIns = Facade.UIManager:GetSubUI(self, UIName2ID.CommonVideocomponent, self._videoCompInstanceId)
        ins = getfromweak(weakIns)
    else
        self._wtNamedSlot_VideoComp:ClearChildren()
        weakIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonVideocomponent, self._wtNamedSlot_VideoComp)
        self._videoCompInstanceId = instanceId
        ins = getfromweak(weakIns)
    end
    return ins
end

function GuidePopWindowUI:OnClose()
    self._bClose = true

    self:_StopMedia()
end

function GuidePopWindowUI:SetData(showData)
    if not showData then return end
    self._showData = showData
end

function GuidePopWindowUI:OnShowBegin()
    self:_StopMedia()
    self._bSelfActive = true
    self:_InitDots()
    self:_UpdateShow()
    if #self._showData.descInfo > 1 then
        self:_PreloadResources(1, #self._showData.descInfo)
    end

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_Tutorial_Popup_Large)

    local curGuideUIState = Module.Guide:GetGuideUIState(UIName2ID.GuidePopWindowUI)
    loginfo("GuidePopWindowUI OnShowBegin with guideShowUIState ", curGuideUIState)
    self:SetRenderOpacity(Module.Guide:GetGuideUIState(UIName2ID.GuidePopWindowUI) and 1 or 0)

    if self._showData.bHideHudUI then
        GuideLogic.SetOnlyShowGuideState(true, GuideConfig.EGuideShowUIFlag.PopWindowUI)
        self._bHideHud = true
        if IsHD() then
        else
            UDFMHudHelper.SetPlayerMoveState(GetWorld(), false)
        end
    end


    self:_PlayAudio()
end

function GuidePopWindowUI:OnHideBegin()
    if self._bHideHud then
        self._bHideHud = false
        GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.PopWindowUI)
        if IsHD() then
        else
            UDFMHudHelper.SetPlayerMoveState(GetWorld(), true)
        end
    end
end

function GuidePopWindowUI:OnHide()
    self:_StopMedia()
    GuideUILogic.GetPopWindowStub():ClearAllResRefs()

    self:RemoveAllLuaEvent()
end

function GuidePopWindowUI:OnInitExtraData(showData)
    self:SetData(showData)
end

function GuidePopWindowUI:_UpdateShow()
    self:_StopMedia()

    local totalNum = #self._showData.descInfo
    log("GuidePopWindowUI:_UpdateShow()", self._curIdx, totalNum)
    self._wtMediaImage:SetVisibility(ESlateVisibility.Collapsed)
    -- self._wtWebMedia:SetVisibility(ESlateVisibility.Collapsed)

    self._wtContentImg:SetVisibility(ESlateVisibility.Collapsed)
    self._wtBtnPlayVideo:SetVisibility(ESlateVisibility.Collapsed)
    self._wtNamedSlot:SetVisibility(ESlateVisibility.Collapsed)
    self._wtNamedSlot:ClearChildren()
    self._wtNamedSlot_VideoComp:SetVisibility(ESlateVisibility.Collapsed)

    self._fullScreenVideoName = nil
    self._videoName = nil
    self._bWaitImgLoad = false

    if not self._showData then return end

    if not self._showData.descInfo then
        return
    end

    self:_StopMedia()


    local descInfo = self._showData.descInfo[self._curIdx]
    self._wtTitle:SetText(descInfo.title)
    self._wtDesc:SetText(descInfo.descText)

    if descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Image then
        -- 仅图片
        self:_LoadImage(descInfo)
    elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.ImageAndPlayBtn then
        self:_LoadImage(descInfo)
        self._wtBtnPlayVideo:SetVisibility(ESlateVisibility.Visible)
    elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Video then
        self._videoName = descInfo.exContentParams[1]
        log("GuidePopWindowUI show video ", self._videoName)

        local MediaResTable = Facade.TableManager:GetTable("MediaResTable")
        if not MediaResTable then
            err("GuidePopWindowUI:_TryPlayMedia MediaResTable is nil")
            return
        end
        local row = MediaResTable[self._videoName]
        if not row then
            err("GuidePopWindowUI:_TryPlayMedia row is nil for videoName", self._videoName)
        end
        if
            row.MediaDownloadType == EMediaDownloadType.EMDT_Web or
            row.MediaDownloadType == EMediaDownloadType.EMDT_Both_Web
        then
            -- texture wait for play
            self:_LoadImage(descInfo)
            self._wtBtnPlayVideo:SetVisibility(ESlateVisibility.Visible)
            -- self._wtWebMedia:Play(self._videoName)
            -- self._wtWebMedia:SetVisibility(ESlateVisibility.Visible)

            --- 等待点击播视频 see GuidePopWindowUI:_OnClickPlayVideo()
        elseif row.MediaDownloadType == EMediaDownloadType.EMT_PC_Web and not IsHD() then
            assert(false, "contat developer to solve this issue, GuidePopWindowUI:_UpdateShow() not support EMT_PC_Web on Mobile")
        else
            -- not streaming video
            self._wtMediaImage:SetVisibility(ESlateVisibility.Visible)
            self._wtMediaImage:Play(self._videoName)
        end
    elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.SubWBP then
        self:_LoadSubBP(descInfo.exContentParams[1])
    elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.FullScreenVideo then
        self:_LoadImage(descInfo)
        local videoName = descInfo.exContentParams[1]
        assert(videoName and videoName ~= "", "GuidePopWindowUI:_UpdateShow() videoName is nil")
        self._fullScreenVideoName = videoName
        -- wait click play btn
        self._wtBtnPlayVideo:SetVisibility(ESlateVisibility.Visible)
    end

    self:_UpdateDotsAndBtn()
end

---@param descInfo GuidePopWindowShowData_DescInfo
function GuidePopWindowUI:_LoadImage(descInfo)
    self._bWaitImgLoad = true
    self._wtContentImg:SetVisibility(ESlateVisibility.Collapsed)

    local descImage = FLuaHelper.SoftObjectPtrToString(descInfo.Image)
    local bAutoResize = true

    local function OnImageLoadFinished(imageAsset)
        -- loginfo("GuidePopWindowUI finish load img")
        if not hasdestroy(imageAsset) then
            local classname = LAI.GetObjectClassName(imageAsset)
            if classname == "PaperSprite" then
                self._wtContentImg:SetBrushFromAtlasInterface(imageAsset, false)
                if bAutoResize then
                    self._wtContentImg:SetBrushSize(imageAsset.BakedSourceDimension)
                end
            else
                self._wtContentImg:SetBrushFromTexture(imageAsset, false)
            end
            if self._bWaitImgLoad then
                self._wtContentImg:SetVisibility(ESlateVisibility.Visible)
            else
                logwarning("GuidePopWindowUI img finish load,but not show now", self._curIdx)
            end
        end
        self._bWaitImgLoad = false
    end

    -- use preload's stub intead of stubUIImages
    -- ResImageUtil.StaticAsyncLoadImgObjByPath(descImage, true, OnImageLoadFinished)
    Facade.ResourceManager:AsyncLoadResource(GuideUILogic.GetPopWindowStub(), descImage, function(mapPath2ResIns)
        local imageAsset = mapPath2ResIns[descImage]
        OnImageLoadFinished(imageAsset)
    end)
end

function GuidePopWindowUI:_LoadSubBP(subBpConfigRowName)
    local subWbpTable = GuideConfig.TableGuideWBPConfig[subBpConfigRowName]
    if subWbpTable then
        log("_UpdateShow SubWBP", subWbpTable.WBPPath.AssetPathName)
        -- local bpSoftClass  = subWbpTable.WBPPath
        -- self:UseSlotAsChildBp(bpSoftClass)
        GuideUILogic.CreateUI(self, subWbpTable.WBPPath, function(uiIns)
            if hasdestroy(uiIns) then
                err("_LoadSubBP in creation uiIns is destroyed")
                return
            end
            self._wtNamedSlot:SetVisibility(ESlateVisibility.Visible)
            self._wtNamedSlot:AddChild(uiIns)
        end)
    else
        err("GuidePopWindowUI show subWBP error, not find config ", subBpConfigRowName)
    end
end

function GuidePopWindowUI:_PreloadResources(l, r)
    local assetPaths = {}

    local insertSubWBP = function(subWbpTableIdx)
        local subWbpTable = GuideConfig.TableGuideWBPConfig[subWbpTableIdx]
        if subWbpTable then
            log("preload sub ui", subWbpTable.WBPPath.AssetPathName)
            local path = subWbpTable.WBPPath.AssetPathName .. "_C"
            table.insert(assetPaths, path)
        else
            err("could not find subWBP config", subWbpTableIdx)
        end
    end
    local tryInsertImage = function(imagePath)
        if imagePath and imagePath ~= "" then
            table.insert(assetPaths, imagePath)
        else
            warn("GuidePopWindowUI:_PreloadShowResources imagePath is nil or empty")
        end
    end


    for idx = l, r do
        local descInfo = self._showData.descInfo[idx]
        if descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Image then
            tryInsertImage(FLuaHelper.SoftObjectPtrToString(descInfo.Image))
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.ImageAndPlayBtn then
            tryInsertImage(FLuaHelper.SoftObjectPtrToString(descInfo.Image))
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Video then
            -- do nothing  for now (when not the web media)
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.SubWBP then
            insertSubWBP(descInfo.exContentParams[1])
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.FullScreenVideo then
            tryInsertImage(FLuaHelper.SoftObjectPtrToString(descInfo.Image))
        end
    end

    Facade.ResourceManager:AsyncLoadResources(
        GuideUILogic.GetPopWindowStub(),
        assetPaths,
        function(mapPath2ResIns)
            for path, resIns in pairs(mapPath2ResIns) do
                if hasdestroy(resIns) then
                    warn("PreloadResources failed, resource is invalid", path)
                else
                    log("PreloadResources success", path)
                end
            end
        end
    )
end

function GuidePopWindowUI:_OnBtnNextClick()
    if self._showData and self._showData.descInfo then
        if self._curIdx >= #self._showData.descInfo then
            return
        end

        self._curIdx = self._curIdx + 1
        self:_UpdateShow()
        self:PlayWidgetAnim(self.Amina_qiehuan)
    end
end

function GuidePopWindowUI:_OnBtnPreClick()
    if self._showData and self._showData.descInfo then
        if self._curIdx < 2 then
            return
        end

        self._curIdx = self._curIdx - 1
        self:_UpdateShow()
        self:PlayWidgetAnim(self.Amina_qiehuan)
    end
end

function GuidePopWindowUI:_InitDots()
    self._DotIns = {}
    self._wtDotBox:ClearChildren()
    Facade.UIManager:ClearSubUIByParent(self, self._wtDotBox)
    self._wtDotBox:SetVisibility(ESlateVisibility.Collapsed)
    self._wtBtnNext:SetVisibility(ESlateVisibility.Collapsed)
    self._wtBtnPre:SetVisibility(ESlateVisibility.Collapsed)

    if not self._showData or not self._showData.descInfo or #self._showData.descInfo <=1 then
        return
    end


    self._wtDotBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local len = #self._showData.descInfo

    for i = 1, len do
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtDotBox)
        if weakUIIns then
            table.insert(self._DotIns, weakUIIns)
            -- if IsHD() then     -- 只在PC上才会有事件，手游体验不好
            --     weakUIIns:Event("OnHovered", self.OnHoverCarousel, self, i)
            --     weakUIIns:Event("OnUnHovered", self.OnUnHoverCarousel, self, i)
            -- end
        end
    end
end

function GuidePopWindowUI:_UpdateDotsAndBtn()
    if self._showData and self._showData.descInfo then
        local totalNum = #self._showData.descInfo
        for idx, wdi in ipairs(self._DotIns) do
            local di = getfromweak(wdi)
            if idx == self._curIdx then
                di:SetStyle(1)
            else
                di:SetStyle(0)
            end
        end

        self._wtBtnClose:SetVisibility(self._curIdx >= totalNum and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
        self._wtBtnNext:SetVisibility(self._curIdx >= totalNum and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
        self._wtBtnPre:SetVisibility(self._curIdx < 2 and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
    end
end

---@param endReason GuideConfig.EPopWindowUICloseReason
function GuidePopWindowUI:_ManualClose(endReason)
    if self._showData then
        if self._showData.callbackOnEnd then
            self._showData.callbackOnEnd(endReason)
            return
        end
    end

    self:RecycleSelf()
end

function GuidePopWindowUI:_OnCloseBtnClick()
    self:_ManualClose(GuideConfig.EPopWindowUICloseReason.CloseBtnClicked)
end

function GuidePopWindowUI:_TryPlayInWindowStreamVideo()
    assert(self._videoName, "GuidePopWindowUI:_TryPlayInWindowStreamVideo videoName is nil or empty")
    -- 小窗内视频，一般比较简短，暂时不需要额外上报时长
    local MediaResTable = Facade.TableManager:GetTable("MediaResTable")
    if not MediaResTable then
        err("clickedPlay::_videoName MediaResTable is nil")
        return false
    end

    local row = MediaResTable[self._videoName]
    if not row then
        err("clickedPlay::_videoName row is nil for videoName", self._videoName)
        return false
    end

    assert(row.MediaDownloadType == EMediaDownloadType.EMDT_Web, "exception: none strem video should not be occur here!")

    --  self._wtWebMedia:StopMedia()

    local videoComponent = self:_TryGetVideoComponent()
    if hasdestroy(videoComponent) then
        err("GuidePopWindowUI:_OnClickPlayVideo ins is destroyed")
        return false
    end
    videoComponent:Stop()
    videoComponent:InitComponent(false)
    videoComponent:Play(self._videoName)
    self._wtNamedSlot_VideoComp:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    self._wtBtnPlayVideo:SetVisibility(ESlateVisibility.Collapsed)

    -- self._wtWebMedia:Play(self._videoName)
    -- self._wtWebMedia:SetVisibility(ESlateVisibility.Visible)
    -- self._wtWebMedia:SetVisibility(ESlateVisibility.Visible)
    -- self._wtWebMedia:Play(self._videoName)
end

function GuidePopWindowUI:_TryPlayFullScreenVideo()
    assert(self._fullScreenVideoName and self._fullScreenVideoName ~= "", "GuidePopWindowUI:_OnClickPlayVideo fullScreenVideoName is nil or empty")

    -- up values
    local targetVideo = self._fullScreenVideoName
    local videoLen = nil
    local startTime = nil
    local bReported = false

    local fDoReport = function(playDuration, bFullPlayed)
        if bReported then
            warn("_TryPlayFullScreenVideo already reported, skip this time", targetVideo, Module.Guide.Field:GetCurGuideId())
            return
        end
        bReported = true

        playDuration = playDuration or 0
        bFullPlayed = bFullPlayed or false
        -- .2f for playDuration
        local playDurationStr = string.format("%.2f", playDuration)

        if not Module.Guide:IsGuiding() then
            warn("_TryPlayFullScreenVideo not in guide, skip report")
            return
        end

        local guideId = Module.Guide.Field:GetCurGuideId()
        local guideCfg = Module.Guide.Field:GetGuideCfg(guideId)

        log("_TryPlayFullScreenVideo guideId", guideId, guideCfg.IsLogStep, targetVideo, playDuration, bFullPlayed)
        -- also needed to be a guide step with IsLogStep == true, otherwise skip report
        if not guideCfg.IsLogStep then
            return
        end

        local logId = GuideLogic.GetStepExLogId(guideId, GuideConfig.EGuideData2ELogExType[GuideConfig.EGuideData.PopWindow].FullScreenVideo)
        local payload = Json.encode({
            video_name = targetVideo,
            play_duration = playDurationStr,
            is_fullly_played = bFullPlayed,
        })
        log("_TryPlayFullScreenVideo  serilized json payload", payload)

        LogAnalysisTool.DoSendClientCommonEventLog(logId, true, payload)
    end

    local fOnGetVideoLen = function(len)
        log("_TryPlayFullScreenVideo fOnGetVideoLen", targetVideo, videoLen)
        videoLen = len
    end

    local fOnMediaPlayEnd = function(uiIns) ---@param uiIns CommonVideoFullScreenView
        log("_TryPlayFullScreenVideo onMediaPlayEnd", targetVideo, bReported)
        if (uiIns) then
            assert(uiIns._bAutoClose == true, "GuidePopWindowUI:_TryPlayFullScreenVideo onMediaPlayEnd uiIns._bAutoClose should be true")
            local dur = os.clock() - startTime
            local bFullPlayed = not uiIns.bWasSkip or false
            fDoReport(dur, bFullPlayed)
        else 
            err("_TryPlayFullScreenVideo onMediaPlayEnd uiIns is nil")
        end
        self._wtRootPanel:SelfHitTestInvisible()
    end

    local fOnMediaPlayBegin = function(uiIns) ---@param uiIns CommonVideoFullScreenView
        log("_TryPlayFullScreenVideo onMediaPlayBegin", targetVideo)
        if hasdestroy(uiIns) then
            err("_TryPlayFullScreenVideo exception: full screen video ins ", targetVideo)
        end
        startTime = os.clock()

        -- TODO: handle pause and resume event?

        local fOnCloseOrRecycle = function(uiInsToo) ---@param uiInsToo CommonVideoFullScreenView
            local dur = os.clock() - startTime
            local bFullPlayed = not uiInsToo.bWasSkip or false
            -- 在这里是正常 report
            fDoReport(dur, bFullPlayed)
            -- 重新显示引导弹窗
            self._wtRootPanel:SelfHitTestInvisible()
        end
        uiIns:AddCloseOrRecycleCallback(fOnCloseOrRecycle)
    end


    local bStartSuccess, uiHandle = Module.CommonWidget:ShowFullScreenVideoView(
        targetVideo,
        false,
        true,
        fOnMediaPlayEnd,
        nil,
        3,
        6,
        true,
        fOnGetVideoLen,
        fOnMediaPlayBegin
    )

    log("_TryPlayFullScreenVideo bPlaySuccess", bStartSuccess)

    if bStartSuccess then
        -- TODO: maybe we should  sync the life time of this uiHandle with the GuidePopWindowUI?
        self._fullScreenUiHandle = uiHandle
        self._wtRootPanel:Collapsed()
    else
        self._wtRootPanel:SelfHitTestInvisible()
    end
end

function GuidePopWindowUI:_OnClickPlayVideo()
    log("_OnClickPlayVideo", self._fullScreenVideoName, self._videoName, IsHD())

    -- she2 的临时，单独上报当前步骤,点击了播放按钮
    if Module.Guide:IsGuiding() then
        local curGuideId = Module.Guide.Field:GetCurGuideId()
        local id = GuideLogic.GetStepExLogId(curGuideId, GuideConfig.EGuideData2ELogExType[GuideConfig.EGuideData.PopWindow].PlayVideoTrigger)
        LogAnalysisTool.DoSendClientCommonEventLog(id)
    end

    if self._showData.descInfo[self._curIdx].exContentType == GuideConfig.EGuidePopWindowItemExContentType.ImageAndPlayBtn then
        log("_OnClickPlayVideo use other proxy playing logic...")
        self:_ManualClose(GuideConfig.EPopWindowUICloseReason.PlayVideoBtnClicked)
        return
    end

    if self._videoName then
        self:_TryPlayInWindowStreamVideo()
        return
    end

    self:_TryPlayFullScreenVideo()
end

-- function GuidePopWindowUI:_OnMediaPlayEnd()
--     self._wtMediaImage.OnMediaPlayEnd:Clear()
--     self:_PlayMedia()
-- end


function GuidePopWindowUI:_StopMedia()
    if not self._videoName then return end
    self._wtMediaImage:StopMedia(true)
    -- self._wtWebMedia:StopMedia()
    local videoComponent = self:_TryGetVideoComponent()
    if videoComponent then
        videoComponent:Stop()
    end

    self._videoName = nil
end

function GuidePopWindowUI:_PlayAudio()
    if not self._showData then return end
    local audioEvent = self._showData.audio
    if audioEvent then
        Facade.SoundManager:PlayGuideAudio(audioEvent)
    end
end

return GuidePopWindowUI
