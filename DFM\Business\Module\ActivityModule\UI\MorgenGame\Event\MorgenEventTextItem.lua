---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenEventTextItem : LuaUIBaseView
local MorgenEventTextItem = ui("MorgenEventTextItem")
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

function MorgenEventTextItem:Ctor()
	self._wtText = self:Wnd("DFTextBlock_26", UITextBlock)
end

function MorgenEventTextItem:RefreshInfo(text)
	-- self._wtText:SetText(ActivityLogic.ReplaceSpecialStr(ActivityLogic.HandleLocalizeText(text)))
	self._wtText:SetText(text)
end

function MorgenEventTextItem:RefreshUI()
end

return MorgenEventTextItem