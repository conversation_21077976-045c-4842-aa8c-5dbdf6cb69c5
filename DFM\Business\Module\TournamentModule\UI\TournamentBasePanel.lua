----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class TournamentBasePanel : LuaUIBaseView
local TournamentBasePanel = ui("TournamentBasePanel")

function TournamentBasePanel:Ctor()
    loginfo("TournamentBasePanel:Ctor")
    self._wtPanelSlot=self:Wnd("DFNamedSlot_35",UIWidgetBase)
    
end

function TournamentBasePanel:OnInitExtraData(defaultIndex)
    loginfo("TournamentBasePanel:OnInitExtraData",defaultIndex)
    Facade.UIManager:RegSwitchSubUI(self,{UIName2ID.TournamentMainPanel,UIName2ID.CommanderMainPanel})
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, self:GenSecondTabRegInfo(defaultIndex))
end

function TournamentBasePanel:OnOpen()
    loginfo("TournamentBasePanel:OnOpen")

end

function TournamentBasePanel:OnClose()
    loginfo("TournamentBasePanel:OnClose")
    Facade.UIManager:UnRegSwitchSubUI(self)
    
end

function TournamentBasePanel:GenSecondTabRegInfo(defaultIndex)
    local fCallbackIns=CreateCallBack(self.OnTabIdxChanged, self)
    local secondTabRegInfo={
        tabTxtList={Module.Tournament.Config.Loc.TournamentTitle,Module.Tournament.Config.Loc.CommanderTitle},
        fCallbackIns=fCallbackIns,
        defalutIdx=defaultIndex or 1,
        bNewReddotTrie=true,
        reddotTrieRegItemList={
            {reddotDataConfigWithStyleList={{key="NewReward",obType=EReddotTrieObserverType.Tournament},{key="NewUnlock",obType=EReddotTrieObserverType.Tournament}}},
            {reddotDataConfigWithStyleList={{key="NewRewardCommander",obType=EReddotTrieObserverType.Tournament},{key="NewUnlockCommander",obType=EReddotTrieObserverType.Tournament}}},
        },
        imgPathList={
            "PaperSprite'/Game/UI/UIAtlas/System/Rank/BakedSprite/Rank_Icon_0002.Rank_Icon_0002'",
            "PaperSprite'/Game/UI/UIAtlas/System/BattlePointMatch/BakedSprite/BattlePointMatch_Icon_05.BattlePointMatch_Icon_05'",
        },
    }
    return secondTabRegInfo
end

function TournamentBasePanel:OnTabIdxChanged(curIndex,lastIndex,tabLevel)
    loginfo("TournamentBasePanel:OnTabIdxChanged",curIndex)
    Facade.UIManager:SwitchSubUIByIndex(self,curIndex,self._wtPanelSlot)
    local title=curIndex==1 and Module.Tournament.Config.Loc.TournamentTitle or Module.Tournament.Config.Loc.CommanderTitle
    Module.CommonBar:ChangeBackBtnText(title)
end

return TournamentBasePanel    
