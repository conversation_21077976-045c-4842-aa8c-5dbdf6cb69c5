----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonConditionItem : LuaUIBaseView

local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"

local QuestSeasonConditionItem = ui("QuestSeasonConditionItem")
function QuestSeasonConditionItem:Ctor()
    self._descText = self:Wnd("wDescRichText", UITextBlock)
    self._decoLine = self:Wnd("DFImage", UIWidgetBase)

    self._desc = nil
    self._progressNum = 0
    self._goal = 0
end

function QuestSeasonConditionItem:OnInitExtraData(description)
    self._desc = description
end

function QuestSeasonConditionItem:SetProgress(progressNum, goal)
    self._progressNum = progressNum
    self._goal = goal
    self:_UpdateView()

end

function QuestSeasonConditionItem:SetProgressByType(type, goal)
    self._progressNum = QuestLogic:GetConditionProgress(type)
    self._goal = goal
    self:_UpdateView()
end

function QuestSeasonConditionItem:_UpdateView()
    if self._progressNum == nil or self._goal == nil then
        logerror("Invalid progress or goal")
        return
    end
    local str = string.format(Module.Quest.Config.Loc.QuestSeasonConditionProgress, self._progressNum, self._goal)
    self._descText:SetText(str .. self._desc)
    if self._progressNum >= self._goal then 
        self:SetType(1)
    else
        self:SetType(0)
    end
end

function QuestSeasonConditionItem:SetHideDecoLine(bShouldHide)
    if bShouldHide == true then
        self._decoLine:Collapsed()    
    else
        self._decoLine:SelfHitTestInvisible()
    end
end

return QuestSeasonConditionItem