----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RecruitSocialPanel : LuaUIBaseView
local RecruitSocialPanel = ui("RecruitSocialPanel")
local RecruitConfig = Module.Recruit.Config
local RecruitLogic = require "DFM.Business.Module.RecruitModule.Logic.RecruitLogic"
local RecruitTeamMemberInfoItem = require("DFM.Business.Module.RecruitModule.UI.RecruitTeamMemberInfoItem")

--- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPUINavigationUtils = import("GPUINavigationUtils")
--- END MODIFICATION

function RecruitSocialPanel:Ctor()
    self._wtIsPublishingIcon = self:Wnd("wtIsPublishingIcon", UIImage)
    self._wtTeamMemberNumText = self:Wnd("wtTeamMemberNumText", UITextBlock)
    self._teamMemberWidgets = {}
    for i = 1, 4, 1 do
        local teamMemberItem = self:Wnd(string.format("wtTeamMemberInfo_%s", tostring(i)), RecruitTeamMemberInfoItem)
        if teamMemberItem then
            table.insert(self._teamMemberWidgets, teamMemberItem)
        end
    end
    self._wtExitTeamBtn = self:Wnd("wtExitTeamBtn", DFCommonButtonOnly)
    self._wtExitTeamBtn:Event("OnClicked",self._OnExitTeamBtnClick,self)
    self._wtSubPanelContainer = self:Wnd("wtSubPanelContainer", UIWidgetBase)
    self._tabIndex = 1
    local ETopBarStyleFlag = Module.CommonBar.Config.ETopBarStyleFlag
    Module.CommonBar:RegStackUITopBarStyle(self, ETopBarStyleFlag.DefaultSecondary & ~(ETopBarStyleFlag.Team | ETopBarStyleFlag.Friends))
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    Module.CommonBar:RegStackUITopBarTitle(self, RecruitConfig.Loc.SocialTitle)

    --- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self._wtRootCanvas = self:Wnd("CanvasPanel_0", UIWidgetBase)

        -- 将不需要聚焦的 Widget 的 Focusable 属性设置为关闭
        for index, memberWidget in ipairs(self._teamMemberWidgets) do
            memberWidget:SetTeamMemberPanelFocusable(false)
        end
    end
    --- END MODIFICATION

    -- azhengzheng:组队码按钮
    self._wtTeamCodeWB = self:Wnd("wtTeamCodeWB", DFCommonButtonOnly)
    self._wtTeamCodeWB:Event("OnClicked", self._OnTeamCodeBtnClicked, self)
    self._wtTeamCodeWB:Collapsed()

    -- azhengzheng:复制队伍码按钮
    self._wtCopyTeamCodeWB = self:Wnd("wtCopyTeamCodeWB", DFCommonButtonOnly)
    self._wtCopyTeamCodeWB:Event("OnClicked", self._OnCopyTeamCodeBtnClicked, self)
    self._wtCopyTeamCodeWB:Collapsed()
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function RecruitSocialPanel:OnInitExtraData(tabIndex)
    self._tabIndex = tabIndex and tabIndex or 1
    local tabTitles = {}
    local uiNavIDList = {}
    for index, tabInfo in ipairs(RecruitConfig.SocialTabInfo) do
        table.insert(uiNavIDList, tabInfo.uiNavID)
        table.insert(tabTitles, tabInfo.title)
    end
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, {
        tabTxtList = tabTitles,
        fCallbackIns = SafeCallBack(self._OnTabIndexChanged, self),
        defalutIdx = self._tabIndex,
        bTriggerCallback = true,
        bReInitChildWidgets = true,
        bNewReddotTrie = true,
    })
    Facade.UIManager:RegSwitchSubUI(self, uiNavIDList)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecruitSocialPanel:OnOpen()
    self:AddListeners()
    self:_OnTeamMemberInfoChanged()
end

function RecruitSocialPanel:OnDeactivate()
    Facade.UIManager:UnRegSwitchSubUI(self)
end

-- UI监听事件、协议
function RecruitSocialPanel:AddListeners()
    self:AddLuaEvent(RecruitConfig.Events.evtOnUpdateRecruitmentState, self._OnTeamMemberInfoChanged, self)
    self:AddLuaEvent(RecruitConfig.Events.evtOnTeamMemberInfoChanged, self._OnTeamMemberInfoChanged, self)
end


-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecruitSocialPanel:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:UnRegSwitchSubUI(self)
end

--- BEGIN MODIFICATION @ VIRTUOS : Navigation
function RecruitSocialPanel:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    Server.GameModeServer:FetchEntranceDifficulty()
end

function RecruitSocialPanel:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 添加导航组
    if not self._rootGroup then
        self._rootGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootCanvas, self, "Hittest")
    
        local wtTeamMemberContainer = self:Wnd("Panel_Left", UIWidgetBase)
        if not self._navGroup then
            self._navGroup = WidgetUtil.RegisterNavigationGroup(wtTeamMemberContainer, self, "Hittest")
            if self._navGroup then
                for index, memberWidget in ipairs(self._teamMemberWidgets) do
                    self._navGroup:AddNavWidgetToArray(memberWidget)
                end
                -- 添加退出按钮
                self._wtExitTeamBtn:SetCppValue("bIsFocusable", true)
                self._navGroup:AddNavWidgetToArray(self._wtExitTeamBtn)

                -- azhengzheng:组队码按钮
                self._wtTeamCodeWB:SetCppValue("bIsFocusable", true)
                self._navGroup:AddNavWidgetToArray(self._wtTeamCodeWB)

                -- azhenzheng:复制组队码按钮
                self._wtCopyTeamCodeWB:SetCppValue("bIsFocusable", true)
                self._navGroup:AddNavWidgetToArray(self._wtCopyTeamCodeWB)
            end
        end

        self:UpdateNavGroupTree()
    end
end

function RecruitSocialPanel:UpdateNavGroupTree()
    -- 添加导航组
    if self._navGroup and self._wtRootCanvas then
        WidgetUtil.BuildGroupTree(self._wtRootCanvas)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end
end
--- END MODIFICATION

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecruitSocialPanel:OnShow()
    self:RefreshView()
end

--- BEGIN MODIFICATION @ VIRTUOS : Navigation
function RecruitSocialPanel:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function RecruitSocialPanel:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    self._wtExitTeamBtn:SetCppValue("bIsFocusable", false)

    -- azhengzheng:组队码按钮
    self._wtTeamCodeWB:SetCppValue("bIsFocusable", false)

    -- azhengzheng:复制组队码按钮
    self._wtCopyTeamCodeWB:SetCppValue("bIsFocusable", false)

    if self._navGroup or self._rootGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
        self._rootGroup = nil
    end
end
-- END MODIFICATION

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecruitSocialPanel:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function RecruitSocialPanel:OnAnimFinished(anim)
end

function RecruitSocialPanel:RefreshView()

end

function RecruitSocialPanel:_OnTeamMemberInfoChanged()
    local maxMemberNum = RecruitLogic.GetTeamMemberMaxNum()
    self._wtTeamMemberNumText:SelfHitTestInvisible()
    local teamMemberNum = Server.TeamServer:GetTeamNum()
    self._wtTeamMemberNumText:SetText(string.format(RecruitConfig.Loc.MyTeam, tostring(math.max(teamMemberNum, 1)), tostring(maxMemberNum)))
    local bIsInTeam = Server.TeamServer:IsInTeam()
    for index, memberWidget in ipairs(self._teamMemberWidgets) do
        memberWidget:InitPlayerInfo(nil)
        if index > maxMemberNum then
            memberWidget:Collapsed()
        else
            memberWidget:SelfHitTestInvisible()
        end
    end
    if bIsInTeam then
        local myInfo = Server.TeamServer:GetMyInfo()
        local myPlayerSimpleInfo = myInfo.PlayerSimpleInfo
        local otherMembersInfo = Server.TeamServer:GetOtherMembers()
        local sortedPlayerInfo = RecruitLogic.SortMembersBySeat(otherMembersInfo)
        local memberInfo = nil
        for index, memberWidget in ipairs(self._teamMemberWidgets) do
            memberInfo = nil
            if index == 1 then
                memberInfo = myPlayerSimpleInfo
            elseif sortedPlayerInfo[index-1] ~= nil and index <= maxMemberNum then
                memberInfo = sortedPlayerInfo[index-1].PlayerSimpleInfo
            end
            memberWidget:InitPlayerInfo(memberInfo)
        end
        if Module.Recruit:IsRecruiting() == true or #otherMembersInfo > 0 then
            self._wtExitTeamBtn:SelfHitTestInvisible()
        else
            self._wtExitTeamBtn:Collapsed() 
        end
    else
        local SOLRankScore = Server.RankingServer:GetRankScore()
        local SOLAttended = Server.RankingServer:GetHasAttended()
        local MPRankScore = Server.TournamentServer:GetRankScore()
        local MPCommanderRankScore = Server.TournamentServer:GetCommanderRankScore()
        local MPAttended = Server.TournamentServer:GetHasAttended()
        local myPlayerSimpleInfo = {
            player_id = Server.AccountServer:GetPlayerId(),
            nick_name = Server.RoleInfoServer.nickName or "",
            pic_url = Server.RoleInfoServer.picUrl,
            level = Server.RoleInfoServer.accountLevel,
            season_lvl = Server.RoleInfoServer.seasonLevel,
            gender = Server.RoleInfoServer.gender,
            safehouse_degree = 0,
            sol_rank_score = SOLRankScore,
            sol_rank_attended = SOLAttended,
            mp_rank_score = MPRankScore,
            mp_commander_score = MPCommanderRankScore,
            mp_rank_attended = MPAttended
        }
        --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
        -- if IsConsole() then
            myPlayerSimpleInfo.plat = Server.AccountServer:GetPlatIdType()
        -- end
        --- END MODIFICATION
        self._teamMemberWidgets[1]:InitPlayerInfo(myPlayerSimpleInfo)
        self._wtExitTeamBtn:Collapsed() 
    end
    self:SetTeamType(Module.Recruit:IsRecruiting() and 0 or 1)
end

function RecruitSocialPanel:_OnExitTeamBtnClick()
    if Server.TeamServer:IsCaptial() then
        Module.Recruit:StopRecruitmentAndExitTeam()
    else
        Server.TeamServer:ExitTeam()
    end
end

function RecruitSocialPanel:_OnTabIndexChanged(TabIndex, LastTabIndex)
    self._tabIndex = TabIndex
    local weakUiIns = Facade.UIManager:SwitchSubUIByIndex(self, self._tabIndex, self._wtSubPanelContainer)

    --- BEGIN MODIFICATION @ VIRTUOS : Navigation  
    if IsHD() and self._navGroup then
        self:UpdateNavGroupTree()
    end
    --- END MODIFICATION
end

function RecruitSocialPanel:OnNavBack()
    Facade.UIManager:CloseUI(self)
    return true
end

-- azhengzheng:组队码按钮
function RecruitSocialPanel:_OnTeamCodeBtnClicked()
    Module.Recruit:OpenTeamCodePop()
end

-- azhengzheng:复制组队码按钮
function RecruitSocialPanel:_OnCopyTeamCodeBtnClicked()
    -- Module.CommonTips:ShowSimpleTip("复制组队码功能正在开发！")
end

return RecruitSocialPanel
