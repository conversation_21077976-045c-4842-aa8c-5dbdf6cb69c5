---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenRuleItem : LuaUIBaseView
local MorgenRuleItem = ui("MorgenRuleItem")

function MorgenRuleItem:Ctor()
	self._wtImage = self:Wnd("DFImage_85", UIImage)
	self._wtTitle = self:Wnd("DFTextBlock_53", UITextBlock)
	self._wtText = self:Wnd("DFTextBlock_110", UITextBlock)
end

function MorgenRuleItem:RefreshInfo(title, text, image)
	self._wtText:SetText(text)
	self._wtTitle:SetText(title)
	self._wtImage:AsyncSetImagePath(image)
end

function MorgenRuleItem:RefreshUI()
end

return MorgenRuleItem