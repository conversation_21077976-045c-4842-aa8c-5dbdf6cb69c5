----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class InventoryModule : ModuleBase
local InventoryModule = class("InventoryModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local InventoryEvtLogic = require "DFM.Business.Module.InventoryModule.InventoryEvtLogic"
local InventoryLoadLogic = require "DFM.Business.Module.InventoryModule.Logic.InventoryLoadLogic"
local LootingAudioLogic = require "DFM.Business.Module.LootingModule.LootingAudioLogic"

local ItemView = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemView"
local WarehouseWithTab=require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseWithTab"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local InventoryField = require "DFM.Business.Module.InventoryModule.InventoryField"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"

local ULuaSubsystem = import "LuaSubsystem"
local UWidgetBlueprintLibrary = import "WidgetBlueprintLibrary"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"

local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
local UGPUserInterfaceUtil = import "GPUserInterfaceUtil"

local function log(...)
    loginfo("[InventoryModule]", ...)
end

function InventoryModule:Ctor()
end

function InventoryModule:OnInitModule()
	self:InitBaseItemSize()
	if IsInEditor() then
		-- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized, self)
		-- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized_V2, self)
		-- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized_V3, self)
		ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized_V4, self)
	end

    -- Debug日志，分辨率相关
    if not VersionUtil.IsShipping() then
        self:_OnNotifyResolutionResized_Debug(0, 0)
        ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized_Debug, self)
    end
    --~
end

function InventoryModule:OnLoadModule()
end

function InventoryModule:OnDestroyModule()
	if IsInEditor() then
		-- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized, self)
		-- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized_V2, self)
		-- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized_V3, self)
		ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized_V4, self)
	end

    -- Debug日志，分辨率相关
    if not VersionUtil.IsShipping() then
        ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized_Debug, self)
    end
    --~
end

---@param gameFlowType EGameFlowStageType
function InventoryModule:OnGameFlowChangeEnter(gameFlowType)
	if gameFlowType == EGameFlowStageType.SafeHouse
	or gameFlowType == EGameFlowStageType.ModeHall
	or gameFlowType == EGameFlowStageType.Lobby then
		-- self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
		-- self:AddLuaEvent(Server.InventoryServer.Events.evtItemMoveBatch, self._OnItemMoveBatch, self)
	end
	if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
		InventoryEvtLogic.AddBlackSiteListeners()
    end

	local bDebugPreCreate = false
	if bDebugPreCreate and gameFlowType == EGameFlowStageType.SafeHouse then
		self:ShowMainPanel(nil,nil,nil,true)
	end
	self.Field.singleClickEquipTargetItem = nil
	self.Field.bSingleClickEquipOrReplace = true
end

---@param gameFlowType EGameFlowStageType
function InventoryModule:OnGameFlowChangeLeave(gameFlowType)
	if gameFlowType == EGameFlowStageType.SafeHouse
	or gameFlowType == EGameFlowStageType.ModeHall
	or gameFlowType == EGameFlowStageType.Lobby then
		self:RemoveAllLuaEvent()
	end
	if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        InventoryEvtLogic.RemoveBlackSiteListeners()
    end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function InventoryModule:OnLoadingLogin2Frontend(gameFlowType)
    InventoryLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function InventoryModule:OnLoadingGame2Frontend(gameFlowType)
    InventoryLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function InventoryModule:OnLoadingFrontend2Game(gameFlowType)
    InventoryLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

---------------private api--------------------
-- function InventoryModule:_OnItemMove(itemMoveInfo)
-- 	---@type ItemBase
-- 	local item = itemMoveInfo.item
-- 	---@type ItemLocation
-- 	local newLoc = itemMoveInfo.NewLoc
-- 	local targetSlot = newLoc and newLoc.ItemSlot or nil
-- 	if targetSlot and targetSlot:IsExtendSlot() then
-- 		local itemSubType = ItemHelperTool.GetSubTypeById(item.id)
-- 		if itemSubType ~= EExtensionType.Item then
-- 			-- 玩家每次安装一个新的，非物品箱的扩容箱，都触发一次弹窗提示：
-- 			self:ShowFirstTimeEquipExtItemConfirmWindow(item)
-- 		end
-- 	end
-- end

function InventoryModule:_OnItemMoveBatch(itemMoveInfos)
	local bAddNewExtItem = false
	local extItem
	for itemGID, itemMoveInfo in pairs(itemMoveInfos) do
		---@type ItemBase
		local item = itemMoveInfo.item

		---@type ItemLocation
		local newLoc = itemMoveInfo.NewLoc
		local targetSlot = newLoc and newLoc.ItemSlot or nil

		---@type ItemLocation
		local srcLoc = itemMoveInfo.OldLoc
		local sourceSlot = srcLoc and srcLoc.ItemSlot or nil

		if targetSlot and targetSlot:IsExtendSlot() then
			extItem = item
			bAddNewExtItem = true
		elseif sourceSlot and sourceSlot:IsExtendSlot() then
			bAddNewExtItem = false
			break
		end
	end

	if bAddNewExtItem and extItem then
		local itemSubType = ItemHelperTool.GetSubTypeById(extItem.id)
		if itemSubType ~= EExtensionType.Item then
			-- 玩家每次安装一个新的，非物品箱的扩容箱，并且不是替换，都触发一次弹窗提示：
			self:ShowFirstTimeEquipExtItemConfirmWindow(extItem)
		end
	end
end

---------------public api--------------------

--- 判断是走
function InventoryModule:CheckRunWarehouseLogic()
	return ItemOperaTool.CheckRunWarehouseLogic()
end

function InventoryModule:OpenItemSubmitPanel(itemList, submitDataList, zeroStoredItemList, fSubmitFunc)
	self:CloseItemSubmitPanel()
	local handle = Facade.UIManager:AsyncShowUI(UIName2ID.ItemsSubmitPanel, nil, nil, itemList, submitDataList, zeroStoredItemList, fSubmitFunc)
	self.Field:SetSubmitPanelHandle(handle)
end

function InventoryModule:GetItemSubmitPanel()
	return self.Field:GetSubmitPanelHandle()
end

function InventoryModule:CloseItemSubmitPanel()
	local handle = self:GetItemSubmitPanel()
	if handle then
		-- Stack UI 无需自己关闭，即不需要Facade.UIManager:CloseUIByHandle(handle)
		-- Pop UI需要手动关闭
		Facade.UIManager:CloseUIByHandle(handle)
		self.Field:SetSubmitPanelHandle(nil)
	end
end

function InventoryModule:CalMaxNumOfItemCanPlaceInDeposit(itemId)
	local allDepositIds = Server.InventoryServer:GetAllDepositIds()
	return Server.InventoryServer:CalMaxNumOfItemCanPlace(itemId, allDepositIds)	
end

function InventoryModule:ShowMainPanel(bOpenFromArmedForce, navTabIndex,targetItem, bTestPreCreate)
	local  function fShowMainPanel()
		bOpenFromArmedForce = setdefault(bOpenFromArmedForce, false)
		if bTestPreCreate then
			local _afterCreate = function(uiIns)
				uiIns:Hide()
			end
			Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseMain, nil, _afterCreate, bOpenFromArmedForce, navTabIndex,targetItem)
		else
			Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseMain, nil, nil, bOpenFromArmedForce, navTabIndex,targetItem)
		end
	end
	fShowMainPanel()
--[[ 	if Server.InventoryServer:GetSortEveryEnter() then
		local allDepositIds = Server.InventoryServer:GetAllDepositIds()
		fShowMainPanel()
		Timer.DelayCall(0.1,function ()
			Server.InventoryServer:DoSortSlots(allDepositIds, nil, nil, true)
		end
		)

		else
		fShowMainPanel()
	end ]]
end

function InventoryModule:ShowEquipView(fCallback)
	local fOnCreateFin =  CreateCallBack(function(self, uiIns)
		uiIns:OnEquipmentBtnClick()
		uiIns:SwitchEquipmentView(2)
		fCallback()
	end, self)
	Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseMain, fOnCreateFin)
end

function InventoryModule:ShowSupplyView(fCallback)
	local fOnCreateFin =  CreateCallBack(function(self, uiIns)
		uiIns:OnEquipmentBtnClick()
		uiIns:SwitchEquipmentView(1)
		fCallback()
	end, self)
	Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseMain, fOnCreateFin)
end

function InventoryModule:ShowKeyUnlockWindow(mapId)
	Facade.UIManager:AsyncShowUI(UIName2ID.KeyUnlockWindow, nil, nil, mapId)
end

function InventoryModule:ShowLevelUpItemConfirmWindow(item)
	InventoryLogic.ShowLevelUpItemConfirmWindow(item)
end

function InventoryModule:ShowRepairConfirmWindow(item)
	InventoryLogic.ShowRepairConfirmWindow(item)
end

function InventoryModule:ShowSellMissionItemConfirmWindow(item, sellNum)
	InventoryLogic.ShowSellMissionItemConfirmWindow(item, sellNum)
end

function InventoryModule:ShowExtArrangeWindow()
	InventoryLogic.ShowExtArrangeWindow()
end

---@param item ItemBase
function InventoryModule:ShowFirstTimeEquipExtItemConfirmWindow(item)
	InventoryLogic.ShowFirstTimeEquipExtItemConfirmWindow(item)
end

function InventoryModule:SwitchAutoSortMode(bAutoSort)
	Server.InventoryServer:DoSwitchAutoSortMode(bAutoSort)
end

function InventoryModule:GetAutoSort()
	return Server.InventoryServer:GetAutoSort()
end

function InventoryModule:UpdateItemUnitSize(targetSize)
	loginfo("InventoryModule:UpdateItemUnitSize", targetSize)
	ItemConfig.DefaultItemViewSize = targetSize

	-- 特殊处理一下
	local slot2update = {}
	local slotGroup = ESlotGroup.MainRental
	if Facade.UIManager:GetCurrentStackUIId() == UIName2ID.WarehouseMain then
		slot2update = {
			ESlotType.BagSpaceContainer,
			ESlotType.ChestHangingContainer,
			ESlotType.Pocket,
			ESlotType.MainContainer
		}
		slotGroup = ESlotGroup.Player
	else
		slot2update = {
			ESlotType.BagSpaceContainer,
			ESlotType.ChestHangingContainer,
			ESlotType.Pocket,
		}
	end
	for _, slotType in ipairs(slot2update) do
		local slot = Server.InventoryServer:GetSlot(slotType, slotGroup)
		if slot then
			slot:RecalculateGeometry()
		end
	end

	-- 保守修改，仅限竞技场
	local inGameController = InGameController:Get()
	if inGameController:IsArena() then
		local arenaSlot2update = {
			ESlotType.ChestHangingContainer,
			ESlotType.Pocket,
			ESlotType.BagContainer,
		}
		for _, slotType in ipairs(arenaSlot2update) do
			local slot = Server.InventoryServer:GetSlot(slotType)
			if slot then
				slot:RecalculateGeometry()
			end
		end
	end

	Module.Inventory.Config.Events.evtUpdateItemUnitSize:Invoke(targetSize)
end

function InventoryModule:SetItemLabelMark(markWidget, itemId)
	InventoryLogic.SetItemLabelMark(markWidget, itemId)
end

function InventoryModule:ShowAdapterSelectWindow(fLoadCallback)
	Facade.UIManager:AsyncShowUI(UIName2ID.InvAdapterSelectWindow, fLoadCallback)
end

function InventoryModule:ShowPerkSelectWindow(fLoadCallback)
	Facade.UIManager:AsyncShowUI(UIName2ID.InvPerkSelectWindow, fLoadCallback)
end

function InventoryModule:CreateItemView(itemStruct, bDraggable, fCallBack, compParentWidget, compWidget)
	InventoryLogic.CreateItemView(itemStruct, bDraggable, fCallBack, compParentWidget, compWidget)
end

function InventoryModule:CommonOnDropForPerk(item, slot, x, y)
	return InventoryLogic.CommonOnDropForPerk(item, slot, x, y)
end

function InventoryModule:CommonOnDropForFastEquip(operation, slot, x, y, index, bRotated, bShowTips)
	local result = InventoryLogic.CommonOnDropForFastEquip(operation, slot, x, y, index, bRotated, bShowTips)
	if result == Module.Inventory.Config.EFastEquipCheckResult.Success then
	    local itemID = operation.DefaultDragVisual and operation.DefaultDragVisual.item.id
	    if slot:IsEquipableSlot() then
	        LootingAudioLogic.PlayEquipAudio(itemID)
	    else
	        LootingAudioLogic.PlayDropAudio(itemID)
	    end
	elseif result == Module.Inventory.Config.EFastEquipCheckResult.InMatching then
		Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.MatchingText)
	end
	return result
end

function InventoryModule:CommonOnDropForBullet(inOperation, slot, x, y, index, bRotated)
	local result = InventoryLogic.CommonOnDropForBullet(inOperation, slot, x, y, index, bRotated)
	if result == ELoadAmmoResult.Success then
	    local itemID = inOperation.DefaultDragVisual and inOperation.DefaultDragVisual.item.id
	    if slot:IsEquipableSlot() then
	        LootingAudioLogic.PlayEquipAudio(itemID)
	    else
	        LootingAudioLogic.PlayDropAudio(itemID)
	    end
	elseif result == ELoadAmmoResult.InMatching then
		Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.MatchingText)
	end
	return result
end

function InventoryModule:CommonItemDoubleClickedInWH(item, curSelectDepositId)
	InventoryLogic.CommonItemDoubleClickedInWH(item, curSelectDepositId)
end

function InventoryModule:CommonItemDoubleClickedInWH2Slot(item, curSelectDepositId, depositIds)
	InventoryLogic.CommonItemDoubleClickedInWH2Slot(item, curSelectDepositId, depositIds)
end

function InventoryModule:CommonBuildDragDropInfoInWH(item, itemPreview)
	return InventoryLogic.CommonBuildDragDropInfoInWH(item, itemPreview)
end

function InventoryModule:OpenDetailPanelInWH(item, refWidget)
	-- 也可能是收藏室
	if ItemOperaTool.CheckIsDIYCabinetPanel() then
		InventoryLogic.OpenDetailPanelInCollectionRoom(item, refWidget)
		return
	end
	InventoryLogic.OpenDetailPanelInWH(item, refWidget)
end

function InventoryModule:DoSelectItemInWH(itemView, bSelected, bOnlyTop)
	InventoryEvtLogic.SetSelected(itemView, bSelected, nil, nil, nil, nil, bOnlyTop)
end

function InventoryModule:RegisterCommonClickBehavior()
	InventoryEvtLogic.BindLuaEvents()	
end

function InventoryModule:UnregisterCommonClickBehavior()
	InventoryEvtLogic.UnBindLuaEvents()
end

function InventoryModule:CommonItemDoubleClickedInWHForCarry(item, curSelectDepositId)
	InventoryLogic.CommonItemDoubleClickedInWHForCarry(item, curSelectDepositId)
end

function InventoryModule:GetFilterFunctionBySortClassId(sortClassId)
	return InventoryLogic.GetFilterFunctionBySortClassId(sortClassId)
end

function InventoryModule:StartExtItemReplacement(extItem)
	return InventoryLogic.StartExtItemReplacement(extItem)
end

function InventoryModule:IsOpenWarehouse()
	return Module.Inventory.Field:GetIsShowWarehouse()
end

function InventoryModule:CleanMissionItemArray()
	return Module.Inventory.Field:CleanMissionItemArray()
end
function InventoryModule:GetMissionItemArray()
	return Module.Inventory.Field:GetMissionItemArray()
end
function InventoryModule:SetMissionItemArray(data)
	return Module.Inventory.Field:SetMissionItemArray(data)
end

function InventoryModule:CheckIsEquippedWeapon(item)
	return InventoryLogic.CheckIsEquippedWeapon(item)
end

function InventoryModule:GetWHExtMode()
	return Module.Inventory.Field:GetWHExtMode()
end

function InventoryModule:GetWHExtManageMode()
	return Module.Inventory.Field:GetWHExtManageMode()
end

-----------------------------------------------------------------------
--region Private

local ITEM_UNIT_SIZE_HD_CONSTANT = 128

function InventoryModule:InitBaseItemSize()
	local world = GetWorld()
	local viewportClient = world and world:GetGameViewport() or nil
	local viewportSize = viewportClient and viewportClient:GetViewportSize() or nil
	if viewportSize then
		-- self:_OnNotifyResolutionResized(viewportSize.X, viewportSize.Y)
		-- self:_OnNotifyResolutionResized_V2(viewportSize.X, viewportSize.Y)
		-- self:_OnNotifyResolutionResized_V3(viewportSize.X, viewportSize.Y)
		self:_OnNotifyResolutionResized_V4(viewportSize.X, viewportSize.Y)
	end
end

function InventoryModule:_OnNotifyResolutionResized(newSizeX, newSizeY)
	log(newSizeX, newSizeY)

	local safePadding = UWidgetBlueprintLibrary.GetSafeZonePadding(GetWorld(), FVector4(), FVector2D(0, 0), FVector4())
	log(safePadding.X, safePadding.Y, safePadding.Z, safePadding.W)

	local paddingWidth = safePadding.X + safePadding.Z
	local paddingHeight = safePadding.Y + safePadding.W

	--- 合理的做法是使用宽高比，因为DPI本身会随宽高比缩放
	local var = newSizeX / newSizeY
	local first = (1280 + paddingWidth) / (720 + paddingHeight)
	--- 解释一些这里的参数：
	--- 1280：标准宽度
	--- 720：标准高度
	--- 10：仓库右侧格子数
	--- 5：仓库左侧格子数
	--- 80：道具最大长度
	--- 70：道具最小长度
	local minSize = 70
	local maxSize = 84
	local second = (1280 + (10 + 5) * (maxSize - minSize) + paddingWidth) / (720 + paddingHeight)

	-- local var = newSizeX
	-- local first = 1280
	-- local second = 1430
	local paddingOffset = 5
	local targetSize
	

	if var < first then
		targetSize = minSize
	elseif var < second then
		targetSize = math.lerp(minSize, maxSize, (var - first) / (second - first))
	else
		targetSize = maxSize
	end
	targetSize = targetSize + paddingOffset
	log("Target size", targetSize)
	self:UpdateItemUnitSize(targetSize)
end

function InventoryModule:_OnNotifyResolutionResized_V2(newSizeX, newSizeY)
	log("Screen size: ", newSizeX, newSizeY)

	local dpi = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
	log("DPI: ", dpi)

	local safePadding = UWidgetBlueprintLibrary.GetSafeZonePadding(GetWorld(), FVector4(), FVector2D(0, 0), FVector4())
	log("Safezone padding: ", safePadding.X, safePadding.Y, safePadding.Z, safePadding.W)
	local safezoneTotalWidth = safePadding.X + safePadding.Z

	local screenWidth = newSizeX / dpi
	local leftPanelBaseWidth = 12 + 10
	local rightPanelBaseWidth = 90 + 20
	local minCenterGap = 30
	local minSize = DFMGlobalConst.GetDepositConstNumber("MinimumGridSize", 70)
	local maxSize = DFMGlobalConst.GetDepositConstNumber("MaximumGridSize", 84)
	local maxTargetSize = (screenWidth - leftPanelBaseWidth - rightPanelBaseWidth - safezoneTotalWidth - minCenterGap) / 15
	log("Max targetSize: ", maxTargetSize)

	local paddingOffset = 5
	local targetSize = math.clamp(maxTargetSize, minSize + paddingOffset, maxSize + paddingOffset)

	log("Target size", targetSize)
	self:UpdateItemUnitSize(targetSize*1.75)
end

function InventoryModule:_OnNotifyResolutionResized_V3(newSizeX, newSizeY)
	log("Screen size: ", newSizeX, newSizeY)

	local dpi = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
	log("DPI: ", dpi)

	local safePadding = UWidgetBlueprintLibrary.GetSafeZonePadding(GetWorld(), FVector4(), FVector2D(0, 0), FVector4())
	log("Safezone padding: ", safePadding.X, safePadding.Y, safePadding.Z, safePadding.W)
	local safezoneTotalWidth = safePadding.X + safePadding.Z

	local screenWidth = newSizeX / dpi
	local screenHeight = newSizeY / dpi
	local ratio = screenWidth / screenHeight
	local first = 1280 / 720
	local second = 19 / 9
	local minSize = 70
	local maxSize = 80

	local targetSize
	if ratio < first then
		targetSize = minSize
	elseif ratio < second then
		targetSize = math.lerp(minSize, maxSize, (ratio - first) / (second - first))
	else
		targetSize = maxSize
	end

	-- pc x 2
	targetSize = targetSize * 2

	log("Target size", targetSize)
	self:UpdateItemUnitSize(targetSize)
end

function InventoryModule:_OnNotifyResolutionResized_V4(newSizeX, newSizeY)
	log("Screen size: ", newSizeX, newSizeY)

	if IsHD() then
		log("Target size(HD)", ITEM_UNIT_SIZE_HD_CONSTANT)
		self:UpdateItemUnitSize(ITEM_UNIT_SIZE_HD_CONSTANT)
		
		return
	end

	local dpi = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
	log("DPI: ", dpi)

	local safePadding = UWidgetBlueprintLibrary.GetSafeZonePadding(GetWorld(), FVector4(), FVector2D(0, 0), FVector4())
	log("Safezone padding: ", safePadding.X, safePadding.Y, safePadding.Z, safePadding.W)

	local screenWidth = newSizeX / dpi - safePadding.X - safePadding.Z
	local compareWidth = 2560

	local minSize = 140
	local maxSize = 150

	local targetSize = minSize + (screenWidth - compareWidth) / 15
	targetSize = math.min(targetSize, maxSize)

	-- if IsHD() then
	-- 	local factor = 0.9
	-- 	targetSize = targetSize * factor
	-- end
	
	-- 折叠屏临时处理，防止重叠
    -- if DFMGameNotch:IsFoldDevice() then
    --     targetSize = 140
    -- end
    --~

	log("Target size", targetSize)
	self:UpdateItemUnitSize(targetSize)
end

-- Debug日志，分辨率相关
function InventoryModule:_OnNotifyResolutionResized_Debug(newSizeX, newSizeY)
    local Vector2DToString = function(InVector2D)
        return string.format("%d %d", math.floor(InVector2D.X), math.floor(InVector2D.Y))
    end
    
    log(
        string.format("InventoryModuleSizeDebug====Begin: ======================================================NewSize(%s)    IsFoldDevice:%s    FoldStatus:%d(-1:None 0:Large 1:Halfing 2:Small)",
            Vector2DToString(FVector2D(newSizeX, newSizeY)),
            tostring(DFMGameNotch:IsFoldDevice()),
            DFMGameNotch:GetFoldStatus()
        )
    )
    
    local ViewportSize = UWidgetLayoutLibrary.GetViewportSize(GetWorld())
    local ViewportScale = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
    local ViewportLocalSize = USlateBlueprintLibrary.GetLocalSize(UWidgetLayoutLibrary.GetViewportWidgetGeometry(GetWorld()))
    local ViewportAbsoluteSize = USlateBlueprintLibrary.GetAbsoluteSize(UWidgetLayoutLibrary.GetViewportWidgetGeometry(GetWorld()))
    
    log(
        string.format("InventoryModuleSizeDebug====01       ViewportSize(%s)    ViewportScale(%.6f)    ViewportSizeScale(%s)    ViewportLocalSize(%s)    ViewportAbsoluteSize(%s)", 
            Vector2DToString(ViewportSize),
            ViewportScale,
            Vector2DToString(ViewportSize / ViewportScale),
            Vector2DToString(ViewportLocalSize),
            Vector2DToString(ViewportAbsoluteSize)
        )
    )

    log(
        string.format("InventoryModuleSizeDebug====02       DisplayMetrics(%s)    WindowSize(%s)    WindowViewportSize(%s)", 
            Vector2DToString(UGPUserInterfaceUtil.GetDisplayMetrics_SlateApplication()),
            Vector2DToString(UGPUserInterfaceUtil.GetSizeInScreen_ActiveTopLevelWindow()),
            Vector2DToString(UGPUserInterfaceUtil.GetViewportSize_ActiveTopLevelWindow())
        )
    )
    
    log("InventoryModuleSizeDebug====End ======================================================")
end
--~

function InventoryModule:InitItemScrollBoxParams(scrollbox)
	if IsHD() then
		scrollbox:SetOverrideScrollAmount(ITEM_UNIT_SIZE_HD_CONSTANT)
	end
end

--------------------------------------------------------------------------
--- Public 跳转页面 API
--------------------------------------------------------------------------
function InventoryModule:Jump()
	local openUINavIdList = {} -- 跳转接口返回 按顺序实际需要打开/重新显示的所有界面id
	self:ShowMainPanel()
	table.insert(openUINavIdList, UIName2ID.WarehouseMain)
    return openUINavIdList
end

--endregion
-----------------------------------------------------------------------

function InventoryModule:GetWishList(sortIndex)
    return InventoryLogic.GetWishList(sortIndex)
end

---检查该场景下的所需道具是否可添加到愿望单
---@param param table {EWishSceneType,id,other} -- 用于标记该按钮是否点击过
---@param wishNeedItemInfos WishNeedItemInfo[]  -- 该场景下所需道具列表
---@return EWishBtnStatus
function InventoryModule:CheckWishNeedItemInfos(param, wishNeedItemInfos)
    return InventoryLogic.CheckWishNeedItemInfos(param, wishNeedItemInfos)
end



function InventoryModule:SetIsInDetailPanel(bInDetailPanel)
	Module.Inventory.Field:SetIsInDetailPanel(bInDetailPanel)
end

function InventoryModule:GetMysticalRatityInfoBySkinId(mysticalPropInfo, bSpecial)
	return InventoryLogic.GetMysticalRatityInfoBySkinId(mysticalPropInfo, bSpecial)
end

function InventoryModule:GetMysticalPendantInfoByDetailInfos(detailInfos, itemQuality)
	return InventoryLogic.GetMysticalPendantInfoByDetailInfos(detailInfos, itemQuality)
end

-----------------------------------------------------------------------
--region 悬浮提示显示
-- function InventoryModule:ShowCommonHoverTipsHD(data, caller)
-- 	self.Field:ShowCommonHoverTipsHD(data, caller)
-- end

-- function InventoryModule:CloseCommonHoverTipsHD(bClosed, caller)
--     self.Field:CloseCommonHoverTipsHD(bClosed, caller)
-- end

-- function InventoryModule:GetCommonHoverTipsHD()
--     return self.Field:GetCommonHoverTipsHD()
-- end

-- function InventoryModule:RemoveCommonHoverTipsHD()
--     self.Field:RemoveCommonHoverTipsHD()
-- end

--endregion
-----------------------------------------------------------------------

--region WarehouseDepositTab对象池
local function getField()
	return Module.Inventory.Field
end

function InventoryModule:ObtainWarehouseDepositTabFromPool()
	return getField():ObtainWarehouseDepositTab()
end

function InventoryModule:FreeWarehouseDepositTabToPool(warehouseIV)
	getField():FreeWarehouseDepositTab(warehouseIV)
end

function InventoryModule:PreloadWarehouseDepositTab(num)
	Facade.UIRegisterManager:PreLoadGlobalRegisterUI()
	getField():PreloadWarehouseDepositTab(num)
end

function InventoryModule:CleanWarehouseDepositTabPool()
	getField():CleanWarehouseDepositTabPool()
end
--endregion

function InventoryModule:CheckShowNotifyReissueWindow()
	if Server.InventoryServer:ConsumeNotifyReissue() then
		log("CheckShowNotifyReissueWindow")
		Module.CommonTips:ShowConfirmWindowWithSingleBtn(Module.Inventory.Config.Loc.NotifyReissueText)
	end
end

function InventoryModule:GetSingleClickEquipTargetItem()
	local singleClickEquipTargetItem = self.Field.singleClickEquipTargetItem
	self.Field.singleClickEquipTargetItem = nil
	local bSingleClickEquipOrReplace = self.Field.bSingleClickEquipOrReplace
	self.Field.bSingleClickEquipOrReplace = nil
	return singleClickEquipTargetItem, bSingleClickEquipOrReplace
end

function InventoryModule:SetSingleClickEquipTargetItem(item, bEquipOrReplace)
	self.Field.singleClickEquipTargetItem = item
	self.Field.bSingleClickEquipOrReplace = bEquipOrReplace
end

function InventoryModule:GetCurDepositId()
	return self.Field:GetCurDepositId()
end

function InventoryModule:CheckSafeBoxExpiredStatus(groupId)
	return InventoryLogic.CheckSafeBoxExpiredStatus(groupId)
end

function InventoryModule:CheckKeyChainExpiredStatus(groupId)
	return InventoryLogic.CheckKeyChainExpiredStatus(groupId)
end

function InventoryModule:CheckDragItemType(healthItemId, equipmentItem)
	return InventoryLogic.CheckDragItemType(healthItemId, equipmentItem)
end

function InventoryModule:CheckItemDurability(equipmentItem)
	return InventoryLogic.CheckItemDurability(equipmentItem)
end

function InventoryModule:SetInvSlotViewType(inInvSlotViewType)
    self.Field:SetInvSlotViewType(inInvSlotViewType)
end

function InventoryModule:GetInvSlotViewType()
    return self.Field:GetInvSlotViewType()
end

function InventoryModule:OpenItemSplitPanel(item, parentWidget)
    return InventoryLogic.OpenItemSplitPanel(item, parentWidget)
end

function InventoryModule:GetNavWidgetFromSlot(Direction)
    return InventoryLogic.GetNearestNavTarget(Direction)
end

function InventoryModule:GetIsYMode()
    return self.Field:GetIsYMode()
end

function InventoryModule:SetIsYMode(bNewState)
	self.Field:SetIsYMode(bNewState)
end

function InventoryModule:GetNavNextTarget(item)
	return InventoryLogic.GetNavNextTarget(item)
end


return InventoryModule