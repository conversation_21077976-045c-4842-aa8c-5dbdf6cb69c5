----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

local RoleInfoAchievement = ui("RoleInfoAchievement")
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local HeroBadgeDetail = require "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.HeroBadgeDetail"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
--- END MODIFICATION

---@class RoleInfoAchievement : LuaUIBaseView

function RoleInfoAchievement:Ctor()
    self._achievementList = {}
    self._wtWaterfallScrollBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_68", self.OnGetItemCount,
        self._OnProcessWidget)

    self._wtCommonDropDownBox = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self._OnOptionChanged)
    self._wtDetail = self:Wnd("WBP_Achievement_Overview_1", UIWidgetBase)
    self._wtHeroDetail = self:Wnd("WBP_AchievementDetails_1", HeroBadgeDetail) --徽章详情

    self._count = 0
    self._SeriesType = 0 -- 0 sol , 1 mp

    self.serverData = {}
    self.acheieveCurcountMap = { [0] = 0, [2] = 0, [3] = 0, [4] = 0, [5] = 0 } -- 0 所有 2 -- 5 徽章等级
    self.acheieveAllcountMap = { [0] = 0, [2] = 0, [3] = 0, [4] = 0, [5] = 0 } -- 0 所有 2 -- 5 徽章等级

    self.lastUnlockTime = 0
    self.lastUnlockBadgeId = 0 -- 最新解锁
    self._RefreshItemHandler = nil
    self._lastReqTime = 0

    self.reqUseTime = 0
end

function RoleInfoAchievement:OnGetItemCount()
    return #self._achievementList
end

function RoleInfoAchievement:GetAchievementSeries()
    local t = Facade.TableManager:GetTable("AchievementSeries")

    local fun = HeroHelperTool.GetHZAchievementDataTableRow

    self._achievementList = {}
    local arr = {}
    local info
    for _, v in pairs(t) do
        if (v.SeriesType == self._SeriesType + 1) or v.SeriesType == 0 then
            local achieveIdArr = {}
            for _, achieveId in pairs(v.AchievementID) do
                info = fun(achieveId)
                if info and info.IsEnable == 1 then
                    table.insert(achieveIdArr, achieveId)
                end
            end

            v.AchievementID = achieveIdArr
            table.insert(arr, v)
        end
    end

    table.sort(arr, function(a, b)
        return a.SeriesOrder > b.SeriesOrder
    end)

    self._achievementList = arr
end

function RoleInfoAchievement:_OnProcessWidget(index, widget)
    local serverData = {}
    local dataTable = self._achievementList[index]

    if dataTable then
        if next(self.serverData) then
            for _, value in pairs(dataTable.AchievementID) do
                if self.serverData[value] then
                    serverData[value] = self.serverData[value]
                end
            end
        end

        widget:Refresh(index, dataTable, serverData, self._SeriesType)
    end
end

function RoleInfoAchievement:GetMaxLevel(achieveId)
    local badgeLevel = 0
    local achieveTable = HeroHelperTool.GetHZAchievementDataTableRow(achieveId)
    for i = 1, 4, 1 do
        if achieveTable.BadgeId and achieveTable.BadgeId[i] then
            local isUnlocked = Server.HeroServer:IsAccessoryUnlocked(Module.Hero:GetCurShowHeroId(),
                achieveTable.BadgeId[i])
            if isUnlocked then
                badgeLevel = i
            end
        end
    end
    return badgeLevel
end

function RoleInfoAchievement:GetCareerServerData(data)
    local myData = {}
    myData.bCareer = true
    myData.progressInfo = {}
    myData.maxBadgeLv = self:GetMaxLevel(data.id)
    self.serverData[data.id] = myData

    local dataTable = HeroHelperTool.GetHZAchievementDataTableRow(data.id)

    for i = 1, 4, 1 do --  i 等级 1 -- 4
        local Mission = dataTable['Mission' .. i]

        Mission = string.gsub(Mission, "%(", "")
        Mission = string.gsub(Mission, "%)", "")
        local MissionText = string.split(Mission, "-") or {}

        myData.progressInfo[i] = {}


        for k, v in ipairs(MissionText) do
            local MissionText2 = string.split(v, ",") or {}
            if #MissionText2 ~= 0 then
                -- 成就ID
                local AchievementID = tonumber(MissionText2[1])
                local AchievementName = ""
                local AchievementDataTableRow = HeroHelperTool.GetAchievementDataTableRow(AchievementID)
                if AchievementDataTableRow then
                    AchievementName = AchievementDataTableRow.Name
                end

                -- 当前等级总次数
                local totalCount = tonumber(MissionText2[2]) or 1
                -- 当前完成次数
                local curCount = 0

                local bUseGoal = false
                for _, achievement in ipairs(data.single_match_achievements) do
                    if achievement.id == AchievementID then
                        if achievement.use_goal == false then
                            curCount = achievement.num
                        else
                            bUseGoal = true
                            local goal = achievement.goal
                            curCount = goal.cur_value
                            totalCount = goal.max_value
                        end
                    end
                end

                local info = {}

                info.achieveId = AchievementID
                info.curCount = curCount
                info.totalCount = totalCount

                local progress = curCount / totalCount
                if progress > 0 and progress < 0.01 then
                    progress = 0.01
                end

                info.progress = progress > 1 and 1 or progress
                if bUseGoal == false then
                    info.progressDesc = StringUtil.Key2StrFormat(Module.Hero.Config.Loc.AchievementDesc
                        , { ["AchievementName"] = AchievementName, ["count"] = totalCount })
                else
                    info.progressDesc = self:GetGoalRemark(AchievementID)
                end

                myData.progressInfo[i][k] = info
            end
        end
    end
end

function RoleInfoAchievement:GetGoalRemark(goalId)
    local activityGoalsTable = Facade.TableManager:GetTable("ActivityTaskGoals")
    for _, goalInfo in pairs(activityGoalsTable) do
        if goalInfo and goalInfo.GoalID == goalId then
            return goalInfo.Remark and goalInfo.Remark or ""
        end
    end
    return ""
end

function RoleInfoAchievement:GetNoCareerServerData(data)
    local myData = {}
    myData.bCareer = false
    myData.mpProgressInfo = {}
    myData.solProgressInfo = {}
    myData.id = data.id
    myData.maxBadgeLv = 1
    self.serverData[data.id] = myData

    local bShowQuestion = false
    if data.mp_goals and data.sol_goals and #data.mp_goals > 0 and #data.sol_goals > 0 then
        bShowQuestion = true
    end

    myData.bShowQuestion = bShowQuestion

    local func = function(goals, bMp)
        local info = {}
        for k, v in ipairs(goals) do
            local progress = 0
            if data.completed then
                progress = 1
            else
                progress = MathUtil.GetPreciseDecimal(v.combined_progress, 6)
            end
            info[k] = {}

            if progress > 0 and progress < 0.01 then
                progress = 0.01
            end

            info[k].progress = progress
            info[k].progressDesc = self:GetGoalRemark(v.id) -- 非生涯描述写死的
            info[k].curCount = v.cur_value
            info[k].totalCount = v.max_value
            info[k].achieveId = v.id
        end
        return info
    end

    -- 1.双模式都有各自条件，每个模式显示自己的条件，
    -- 2.都没条件，只显示解锁时间
    -- 3.只有一个配置了条件，两个模式共用这个条件

    if bShowQuestion then
        myData.mpProgressInfo = func(data.mp_goals, true)
        myData.solProgressInfo = func(data.sol_goals, false)
    else
        local goals
        if data.mp_goals and #data.mp_goals > 0 then
            goals = data.mp_goals
        end

        if data.sol_goals and #data.sol_goals > 0 then
            goals = data.sol_goals
        end

        if goals then
            myData.mpProgressInfo = func(goals, true)
            myData.solProgressInfo = func(goals, false)
        end
    end

    self:SetDefaultComplet(myData)
end

function RoleInfoAchievement:SetDefaultComplet(data)
    local bDefault = false

    local t = HeroHelperTool.GetHZAchievementDataTableRow(data.id)
    if t and t.AchievementType == 0 then --其他模块获取，获取了徽章进度即为 100%
        for _, badgeId in pairs(t.BadgeId) do
            local curAccessoryData = Server.HeroServer:GetSelectedAccessory(Module.Hero:GetCurShowHeroId(), badgeId)
            if curAccessoryData and curAccessoryData.is_unlock then
                bDefault = true
            end
        end
    end

    if bDefault then
        local info = {}
        info.progress = 1
        info.progressDesc = ''
        info.curCount = 1
        info.totalCount = 1
        info.achieveId = data.id
        data.mpProgressInfo[1] = info
        data.solProgressInfo[1] = info
    end
end

-- 处理其他模块解锁的成就
function RoleInfoAchievement:DealOtherUnlockAchieve()
    local t = Facade.TableManager:GetTable("AchievementSeries")
    local arr = {}
    for _, v in pairs(t) do
        table.insert(arr, v)
    end

    local HeroConfig = Module.Hero.Config
    for _, value in pairs(arr) do
        if value.AchievementID then
            for _, id in pairs(value.AchievementID) do
                local info = HeroHelperTool.GetHZAchievementDataTableRow(id)
                if not info then
                    logerror("HeroBadgeDetail:Show info is nil", id)
                else
                    if info.AchievementType == 0 then
                        if info.Model ~= HeroConfig.EBadgeType.Career then
                            local data = { id = id }
                            self:GetNoCareerServerData(data)
                        end
                    end
                end
            end
        end
    end
end

function RoleInfoAchievement:ReqServerData()
    local curTime = Facade.ClockManager:GetLocalTimestamp()

    if self._lastReqTime > curTime then
        return
    end

    self:DealOtherUnlockAchieve()

    if IsInEditor() then
        curTime = Facade.ClockManager:GetLocalTimestamp()
        logerror("ddh reqtime11 " .. curTime)
    end
    local HeroConfig = Module.Hero.Config
    local callback = function(serverDatas)
        for key, serverData in pairs(serverDatas) do
            local id = serverData.id
            local info = HeroHelperTool.GetHZAchievementDataTableRow(id)
            if info then
                if info.AchievementType == 0 then -- 其他模块解锁的成就，不请求
                    if info.Model ~= HeroConfig.EBadgeType.Career then
                        local data = {}
                        data.id = id
                        self:GetNoCareerServerData(data)
                    end
                else
                    if info.IsEnable == 1 then
                        local isReq = false
                        if info.Hide == -1 then -- 解锁才显示   ddh todo
                            local curAccessoryData = Server.HeroServer:GetSelectedAccessory(Module.Hero:GetCurShowHeroId()
                                ,
                                info.BadgeId[1])
                            if curAccessoryData and curAccessoryData.is_unlock then
                                isReq = true
                            end
                        else isReq = true end
                        if isReq then
                            if info.Model == HeroConfig.EBadgeType.Career then
                                self:GetCareerServerData(serverData)
                            else
                                self:GetNoCareerServerData(serverData)
                            end
                        end
                    end
                end
            end
        end

        if IsInEditor() then
            logerror("ddh reqtime22 " .. Facade.ClockManager:GetLocalTimestamp())
        end
        self:Refresh()
    end

    Server.RoleInfoServer:DoAchieveBatchQuery(callback)
    self.reqUseTime = curTime
    self._lastReqTime = curTime + 5
end

function RoleInfoAchievement:Refresh()
    self:GetAchievementSeries()
    self._wtWaterfallScrollBox:RefreshAllItems()
    self:CalAchieve()
    self:RefreshDetail()

    self._wtWaterfallScrollBox:ForceSetScrollBarVisible(true)
end

function RoleInfoAchievement:RepositionScrollViewOffset()
    if self._RefreshItemHandler then
        Timer.CancelDelay(self._RefreshItemHandler)
        self._RefreshItemHandler = nil
    end
    if not self._wtWaterfallScrollBox or not isvalid(self._wtWaterfallScrollBox) then
        loginfo("UpdateSysAfterTranslated:RepositionScrollViewOffset self._wtWaterfallScrollBox is not valid")
        return
    end

    local function f()
        self._wtWaterfallScrollBox:ForceSetScrollBarVisible(true)
        self._wtWaterfallScrollBox:RefreshVisibleItemSizeAndPos()
    end

    self._RefreshItemHandler = Timer.DelayCall(0.2, f, self)
end

function RoleInfoAchievement:_OnOptionChanged(id)
    self._SeriesType = id
    Module.RoleInfo.Field:SetAcheievementId(-1)

    self:InitOpenList()
    self:Refresh()
end

function RoleInfoAchievement:InitOpenList(id)
    local openList = Module.RoleInfo.Field:GetOpenAchievementList()
    for key, value in pairs(openList) do
        openList[key] = 0
    end
end

function RoleInfoAchievement:OnInitExtraData()
    self:InitOpenList()
    self:ReqServerData()
end

function RoleInfoAchievement:RefreshDetail()
    local RoleInfoField = Module.RoleInfo.Field
    local id = RoleInfoField:GetAcheievementId()
    if id > 0 then
        self._wtDetail:SetVisibility(ESlateVisibility.Collapsed)
        self._wtHeroDetail:SetVisibility(ESlateVisibility.Visible)

        self._wtHeroDetail:Refresh(id, self.serverData[id], self._SeriesType)
    else
        self._wtDetail:SetVisibility(ESlateVisibility.Visible)
        self._wtHeroDetail:SetVisibility(ESlateVisibility.Collapsed)
        self:RefreshAchieveDetail()
    end
end

function RoleInfoAchievement:CalAchieve()
    -- 0 所有
    self.acheieveCurcountMap = { [0] = 0, [2] = 0, [3] = 0, [4] = 0, [5] = 0 }
    self.acheieveAllcountMap = { [0] = 0, [2] = 0, [3] = 0, [4] = 0, [5] = 0 }

    local calMap = {}

    self.lastUnlockTime = 0
    local heroBadgeData = Facade.TableManager:GetTable("HeroBadgeData")
    local heroId = Module.Hero:GetCurShowHeroId()
    for _, value in pairs(self._achievementList) do
        for _, achiveId in pairs(value.AchievementID) do
            -- 全部页签下，会计算重复
            if calMap[achiveId] == nil then
                calMap[achiveId] = 1

                local achieveTable = HeroHelperTool.GetHZAchievementDataTableRow(achiveId)

                if self.serverData[achiveId] then
                    for _, badgeId in pairs(achieveTable.BadgeId) do
                        local heroBadgeInfo = heroBadgeData[badgeId]
                        if heroBadgeInfo then
                            local curAccessoryData = Server.HeroServer:GetSelectedAccessory(heroId, badgeId)
                            local badgeLv = heroBadgeInfo.BadgeLevel

                            if self.acheieveAllcountMap[badgeLv] ~= nil then
                                self.acheieveAllcountMap[badgeLv] = self.acheieveAllcountMap[badgeLv] + 1
                                self.acheieveAllcountMap[0] = self.acheieveAllcountMap[0] + 1

                                if curAccessoryData and curAccessoryData.is_unlock then
                                    if curAccessoryData.unlocked_timestamp > self.lastUnlockTime then
                                        self.lastUnlockTime = curAccessoryData.unlocked_timestamp
                                        self.lastUnlockBadgeId = badgeId
                                    end
                                    self.acheieveCurcountMap[badgeLv] = self.acheieveCurcountMap[badgeLv] + 1
                                    self.acheieveCurcountMap[0] = self.acheieveCurcountMap[0] + 1
                                end
                            else
                                logerror(" RoleInfoAchievement:CalAchieve badgeLv is not exist", badgeLv)
                            end
                        end
                    end
                end
            end
        end
    end
end

function RoleInfoAchievement:GetLastBadgeSeries()
    for key, value in pairs(self._achievementList) do
        for _, achieveId in pairs(value.AchievementID) do
            local achieveTable = HeroHelperTool.GetHZAchievementDataTableRow(achieveId)
            if achieveTable then
                for _, badgeId in pairs(achieveTable.BadgeId) do
                    if badgeId == self.lastUnlockBadgeId then
                        return value
                    end
                end
            end
        end
    end
end

function RoleInfoAchievement:RefreshAchieveDetail()
    local allAchieveCount = self._wtDetail:Wnd("WBP_AchievementCount", UIWidgetBase)

    local allText = allAchieveCount:Wnd('DFRichTextBlock_30', UITextBlock)
    local timeText = self._wtDetail:Wnd("DFTextBlock_3", UITextBlock)
    local nameText = self._wtDetail:Wnd("DFTextBlock", UITextBlock)
    local seriesText = self._wtDetail:Wnd("DFTextBlock_1", UITextBlock)

    local img = self._wtDetail:Wnd("DFImage_150", UIImage)

    local heroBadgeData = Facade.TableManager:GetTable("HeroBadgeData")
    local heroBadgeInfo = heroBadgeData[self.lastUnlockBadgeId]

    if heroBadgeInfo then
        img:AsyncSetImagePath(heroBadgeInfo.BadgeImage)
        nameText:SetText(heroBadgeInfo.BadgeName)
        local t = self:GetLastBadgeSeries()
        if t then
            seriesText:SetText(t.SeriesName)
        end
    end

    allText:SetText(self.acheieveCurcountMap[0] .. '/' .. self.acheieveAllcountMap[0])
    -- 一共4个徽章等级 2 -- > 5
    for i = 1, 4, 1 do
        local achieveCount = self._wtDetail:Wnd('WBP_AchievementCount_' .. i, UIWidgetBase)
        local text = achieveCount:Wnd('DFRichTextBlock_30', UITextBlock)

        text:SetText(self.acheieveCurcountMap[i + 1] .. '/' .. self.acheieveAllcountMap[i + 1])
    end

    timeText:SetText(TimeUtil.TransUnixTimestamp2YYMMDDStr(self.lastUnlockTime or 0, "YY/MM/DD"))
end

function RoleInfoAchievement:AddEventListener()
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtAchievementListSelectChange, self.RepositionScrollViewOffset, self)
    self:AddLuaEvent(Module.RoleInfo.Config.Event.evtAchievementSelectChange, self.RefreshDetail, self)
end

function RoleInfoAchievement:OnOpen()
    Module.RoleInfo.Field:SetAcheievementIndex(-1)
end

function RoleInfoAchievement:OnClose()
    self.serverData = {}
end

function RoleInfoAchievement:OnShowBegin()
    if RoleInfoLogic.IsInMp() then
        self._SeriesType = 1
    else
        self._SeriesType = 0
    end

    local txtArr = { Module.CommonBar.Config.Loc.ModeSOLText, Module.CommonBar.Config.Loc.ModeMPText }
    UIUtil.InitDropDownBox(self._wtCommonDropDownBox, txtArr, {}, self._SeriesType)

    self:AddEventListener()
    self:Refresh()

    --- BEGIN MODIFICATION @ VIRTUOS
    self:_InitGamepadInputs()
    --- END MODIFICATION
end

function RoleInfoAchievement:OnHideBegin()
    self:RemoveAllLuaEvent()

    local field = Module.RoleInfo.Field
    field:SetAcheievementIndex(-1)
    field:SetAcheievementId(-1)

    -- BEGIN MODIFICATION @ VIRTUOS
    self:_DisableGamepadInputs()
    -- END MODIFICATION
end

function RoleInfoAchievement:_InitGamepadInputs()
    if not IsHD() or not self:IsVisible() then
        return
    end

    self:_DisableGamepadInputs()

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterfallScrollBox, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtWaterfallScrollBox)
            self._navGroup:SetScrollRecipient(self._wtWaterfallScrollBox)
            -- self._navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
        -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end
end

function RoleInfoAchievement:_DisableGamepadInputs()
    if not IsHD() then
        return
    end

    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

return RoleInfoAchievement
