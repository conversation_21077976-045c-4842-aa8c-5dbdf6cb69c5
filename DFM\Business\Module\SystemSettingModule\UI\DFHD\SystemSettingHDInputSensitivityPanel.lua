----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SystemSettingHDInputSensitivityPanel
local SystemSettingHDInputSensitivityPanel = ui("SystemSettingHDInputSensitivityPanel")
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local InputSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.InputSettingLogicHD"
local ESensitivityModeHD = import("ESensitivityModeHD")
local MouseSensitivityMode = import("EMouseSensitivityMode")
--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

function SystemSettingHDInputSensitivityPanel:Ctor()
    loginfo("[george]<InputPanel> Ctor()")
    self:_BindWidget()
end

function SystemSettingHDInputSensitivityPanel:_BindWidget()
    self._wtScrollBox = self:Wnd("InputPanelScrollBox", UIWidgetBase)
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
    self._wtItemPanel = self:Wnd("wtItemPanel", UILightWidget)

    self._wtMDVList = self:Wnd("_wtMDVList", UILightWidget)
    self._wtADSList = self:Wnd("_wtADSList", UILightWidget)

    self._wtMDVList_1 = self:Wnd("_wtMDVList_1", UILightWidget) -- 地面载具
    self._wtADSList_1 = self:Wnd("_wtADSList_1", UILightWidget)

    self._wtMDVList_2 = self:Wnd("_wtMDVList_2", UILightWidget) --空中载具
    self._wtADSList_2 = self:Wnd("_wtADSList_2", UILightWidget)

    self._wtMDVList_3 = self:Wnd("_wtMDVList_3", UILightWidget)  --炮手
    self._wtADSList_3 = self:Wnd("_wtADSList_3", UILightWidget)

    self._wtMDVList_4 = self:Wnd("_wtMDVList_4", UILightWidget)  -- 固定翼
    self._wtADSList_4 = self:Wnd("_wtADSList_4", UILightWidget)

    self._wtSensitivityMode = self:Wnd("_wtSensitivityMode", LuaUIBaseView)
    self._wtSensitivityMode_1 = self:Wnd("_wtSensitivityMode_1", LuaUIBaseView)
    self._wtSensitivityMode_2 = self:Wnd("_wtSensitivityMode_2", LuaUIBaseView)
    self._wtSensitivityMode_3 = self:Wnd("_wtSensitivityMode_3", LuaUIBaseView)
    self._wtSensitivityMode_4 = self:Wnd("_wtSensitivityMode_4", LuaUIBaseView)

    self._wtReverseAll = self:Wnd("_wtReverseAll", LuaUIBaseView)
    self._wtMDV = self:Wnd("_wtMDV", LuaUIBaseView)
    self._wtMDV_1 = self:Wnd("_wtMDV_1", LuaUIBaseView)
    self._wtMDV_2 = self:Wnd("_wtMDV_2", LuaUIBaseView)
    self._wtMDV_3 = self:Wnd("_wtMDV_3", LuaUIBaseView)
    self._wtMDV_4 = self:Wnd("_wtMDV_4", LuaUIBaseView)

    -- 临时扩大列表宽度
    -- local _innerDropdown = self._wtSensitivityMode:Wnd("WBP_DFTabGroupDroDownBox_Pc", UIWidgetBase)
    -- local _innerSizeBox = _innerDropdown:Wnd("DFSizeBox_1", UILightWidget)
    -- _innerSizeBox:SetMaxDesiredWidth(724)

    self._MDVSettings={
        [MouseSensitivityMode.ENormalWeapon] = {self._wtMDVList,  self._wtADSList,  self._wtMDV},
        [MouseSensitivityMode.EVehicleDriveWeapon] = {self._wtMDVList_1,  self._wtADSList_1,  self._wtMDV_1},
        [MouseSensitivityMode.EHelicopterDriveWeapon] = {self._wtMDVList_2,  self._wtADSList_2,  self._wtMDV_2},
        [MouseSensitivityMode.EVehicleWeapon] = {self._wtMDVList_3,  self._wtADSList_3,  self._wtMDV_3},
        [MouseSensitivityMode.EJet] = {self._wtMDVList_4,  self._wtADSList_4,  self._wtMDV_4}
    }
    
end

function SystemSettingHDInputSensitivityPanel:_BindBtnEvent()
    self:AddLuaEvent(self._wtSensitivityMode.evtOnOptionChanged, self._OnSensitivityModeChanged, self)
    self:AddLuaEvent(self._wtReverseAll.evtOnStateChanged, self._OnReverseAllStateChanged, self)
    self:AddLuaEvent(self._wtSensitivityMode_1.evtOnOptionChanged, self._OnSensitivityModeChanged_1, self)
    self:AddLuaEvent(self._wtSensitivityMode_2.evtOnOptionChanged, self._OnSensitivityModeChanged_2, self)
    self:AddLuaEvent(self._wtSensitivityMode_3.evtOnOptionChanged, self._OnSensitivityModeChanged_3, self)
    self:AddLuaEvent(self._wtSensitivityMode_4.evtOnOptionChanged, self._OnSensitivityModeChanged_4, self)
end

function SystemSettingHDInputSensitivityPanel:OnOpen()
    -- loginfo("[george]<InputPanel> OnOpen()")
    self:_BindBtnEvent()
    self:GenerateDynamicSettings()


    CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)

    local zoomData = InputSettingLogicHD.GetSortedZoomData(0)
    local vehicleZoomData = InputSettingLogicHD.GetSortedZoomData(1)
    local vehicleZoomData2 = InputSettingLogicHD.GetSortedZoomData(2)
    local vehicleZoomData3 = InputSettingLogicHD.GetSortedZoomData(3)
    local vehicleZoomData4 = InputSettingLogicHD.GetSortedZoomData(4)

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
    -- 键鼠界面Item
    local bUsingForGamepad = false

    for mode, lists in pairs(self._MDVSettings) do
        for j, item in ipairs(lists[1]:GetAllChildren()) do
            if mode == MouseSensitivityMode.ENormalWeapon then
                item:Init(mode, j, zoomData[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EVehicleDriveWeapon then
                item:Init(mode, j, vehicleZoomData[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EHelicopterDriveWeapon then
                item:Init(mode, j, vehicleZoomData2[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EVehicleWeapon then
                item:Init(mode, j, vehicleZoomData3[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EJet then
                item:Init(mode, j, vehicleZoomData4[j], bUsingForGamepad)
            end
        end
    end

    for mode, lists in pairs(self._MDVSettings) do
        for j, item in ipairs(lists[2]:GetAllChildren()) do
            if mode == MouseSensitivityMode.ENormalWeapon then
                item:Init(mode, j, zoomData[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EVehicleDriveWeapon then
                item:Init(mode, j, vehicleZoomData[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EHelicopterDriveWeapon then
                item:Init(mode, j, vehicleZoomData2[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EVehicleWeapon then
                item:Init(mode, j, vehicleZoomData3[j], bUsingForGamepad)
            elseif mode == MouseSensitivityMode.EJet then
                item:Init(mode, j, vehicleZoomData4[j], bUsingForGamepad)
            end
        end
    end
    -- END MODIFICATION

    -- for i, item in ipairs(self._wtADSList:GetAllChildren()) do
    --     item:Init(i, zoomData[i])
    -- end

    -- for i, item in ipairs(self._wtMDVList:GetAllChildren()) do
    --     item:Init(i, zoomData[i])
    -- end

    self:_RefreshSensitivityMode()
end

function SystemSettingHDInputSensitivityPanel:_OnSensitivityModeChanged(mode)
    self:_RefreshSensitivityMode(MouseSensitivityMode.ENormalWeapon, mode)
end

function SystemSettingHDInputSensitivityPanel:_OnSensitivityModeChanged_1(mode)
    self:_RefreshSensitivityMode(MouseSensitivityMode.EVehicleDriveWeapon, mode)
end

function SystemSettingHDInputSensitivityPanel:_OnSensitivityModeChanged_2(mode)
    self:_RefreshSensitivityMode(MouseSensitivityMode.EHelicopterDriveWeapon , mode)
end

function SystemSettingHDInputSensitivityPanel:_OnSensitivityModeChanged_3(mode)
    self:_RefreshSensitivityMode(MouseSensitivityMode.EVehicleWeapon , mode)
end

function SystemSettingHDInputSensitivityPanel:_OnSensitivityModeChanged_4(mode)
    self:_RefreshSensitivityMode(MouseSensitivityMode.EJet , mode)
end

function SystemSettingHDInputSensitivityPanel:_OnReverseAllStateChanged(bEnable)
    if bEnable then
        local subIDs = {
            "bInfantryVerticalMMReversed",
            "bVehicleVerticalMMReversed",
            "bHelicopterVerticalMMReversed",
            "bGunnerVerticalMMReversed"
        }

        local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()

        for _, id in ipairs(subIDs) do
            CommonSettingLogicHD.SetDataByIDImmediately(id, true)
            local item = curItems[id]
            if not hasdestroy(item) then
                item:ReloadSetting()
            end
        end
    end
end

function SystemSettingHDInputSensitivityPanel:_ReloadADSItems(group)
    local lists = self._MDVSettings[group]
    for _, item in ipairs(lists[2]:GetAllChildren()) do
        item:ReloadSetting()
    end
end

function SystemSettingHDInputSensitivityPanel:_RefreshSensitivityMode(group, mode)
    if not group then
        self:_RefreshSensitivityMode(MouseSensitivityMode.ENormalWeapon ,CommonSettingLogicHD.GetDataByID("SensitivityMode"))
        self:_RefreshSensitivityMode(MouseSensitivityMode.EVehicleDriveWeapon ,CommonSettingLogicHD.GetDataByID("SensitivityMode_Driver"))
        self:_RefreshSensitivityMode(MouseSensitivityMode.EHelicopterDriveWeapon ,CommonSettingLogicHD.GetDataByID("SensitivityMode_HelicopterDriver"))
        self:_RefreshSensitivityMode(MouseSensitivityMode.EVehicleWeapon ,CommonSettingLogicHD.GetDataByID("SensitivityMode_Passenger"))
        self:_RefreshSensitivityMode(MouseSensitivityMode.EJet ,CommonSettingLogicHD.GetDataByID("SensitivityMode_Jet"))
        return
    end

    if mode == ESensitivityModeHD.MDVOnly then
        self._MDVSettings[group][1]:Collapsed()
        self._MDVSettings[group][2]:Collapsed()
        self._MDVSettings[group][3]:SelfHitTestInvisible()

        -- self._wtMDVList:Collapsed()
        -- self._wtADSList:Collapsed()
        -- self._wtMDV:SelfHitTestInvisible()
    elseif mode == ESensitivityModeHD.MDVXADS then
        self._MDVSettings[group][1]:Collapsed()
        self._MDVSettings[group][2]:SelfHitTestInvisible()
        self._MDVSettings[group][3]:SelfHitTestInvisible()

        self:_ReloadADSItems(group)

        -- self._wtMDVList:Collapsed()
        -- self._wtADSList:SelfHitTestInvisible()
        -- self._wtMDV:SelfHitTestInvisible()
    elseif mode == ESensitivityModeHD.ZoomratedMDV then
        self._MDVSettings[group][1]:SelfHitTestInvisible()
        self._MDVSettings[group][2]:Collapsed()
        self._MDVSettings[group][3]:Collapsed()

        -- self._wtMDVList:SelfHitTestInvisible()
        -- self._wtADSList:Collapsed()
        -- self._wtMDV:Collapsed()
    else
        self._MDVSettings[group][1]:Collapsed()
        self._MDVSettings[group][2]:SelfHitTestInvisible()
        self._MDVSettings[group][3]:Collapsed()

        self:_ReloadADSItems(group)

        -- self._wtMDVList:Collapsed()
        -- self._wtADSList:SelfHitTestInvisible()
        -- self._wtMDV:Collapsed()
    end
end

function SystemSettingHDInputSensitivityPanel:GenerateDynamicSettings()
    for mode,list in pairs(self._MDVSettings) do
        Facade.UIManager:RemoveSubUIByParent(self, list[2])
        Facade.UIManager:RemoveSubUIByParent(self, list[1])
        local zoomData = InputSettingLogicHD.GetSortedZoomData(0)
        local vehicleZoomData = InputSettingLogicHD.GetSortedZoomData(1)
        local vehicleZoomData2 = InputSettingLogicHD.GetSortedZoomData(2)
        local vehicleZoomData3 = InputSettingLogicHD.GetSortedZoomData(3)
        local vehicleZoomData4 = InputSettingLogicHD.GetSortedZoomData(4)
        if mode == MouseSensitivityMode.ENormalWeapon then
            for i, tmpData in ipairs(zoomData) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[2])
            end

            for i, tmpData in ipairs(zoomData) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDMDVItem, list[1])
            end
        elseif mode == MouseSensitivityMode.EVehicleDriveWeapon then
            for i, tmpData in ipairs(vehicleZoomData) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[2])
            end

            for i, tmpData in ipairs(vehicleZoomData) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDMDVItem, list[1])
            end
        elseif mode == MouseSensitivityMode.EHelicopterDriveWeapon then
            for i, tmpData in ipairs(vehicleZoomData2) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[2])
            end

            for i, tmpData in ipairs(vehicleZoomData2) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDMDVItem, list[1])
            end
        elseif mode == MouseSensitivityMode.EVehicleWeapon then
            for i, tmpData in ipairs(vehicleZoomData3) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[2])
            end

            for i, tmpData in ipairs(vehicleZoomData3) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDMDVItem, list[1])
            end
        elseif mode == MouseSensitivityMode.EJet then
            for i, tmpData in ipairs(vehicleZoomData4) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDADSItem, list[2])
            end

            for i, tmpData in ipairs(vehicleZoomData4) do
                local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDMDVItem, list[1])
            end
        end
    end
end

function SystemSettingHDInputSensitivityPanel:OnShowBegin()
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    -- if IsHD() then
    --     WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    -- end
    --END MODIFICATION
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.InputSetting then
        local list = {
                {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
                {actionName = "ResetInput", func = self._OnClickResetBtn, caller = self}
        }
        local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
        for _, v in ipairs(globalList) do
            table.insert(list, v)
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
    end
end

function SystemSettingHDInputSensitivityPanel:OnShow()
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    if IsHD() then
        self:_RegisterNavGroup()
    end
    --END MODIFICATION

    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.InputSetting then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
    end
end

function SystemSettingHDInputSensitivityPanel:OnHideBegin()
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    -- 恢复默认导航配置
    if IsHD() then
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
    --END MODIFICATION
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function SystemSettingHDInputSensitivityPanel:OnHide()
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    if IsHD() then
        self:_RemoveNavGroup()
    end
    --END MODIFICATION

    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
end

function SystemSettingHDInputSensitivityPanel:OnClose()
    for _,list in pairs(self._MDVSettings) do
        Facade.UIManager:ClearSubUIByParent(self, list[2])
        Facade.UIManager:ClearSubUIByParent(self, list[1])
    end
end

function SystemSettingHDInputSensitivityPanel:_OnSensitivityExpandableStateChanged(bExpanded)
    CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
end

function SystemSettingHDInputSensitivityPanel:_OnClickResetBtn()
    local function fReset()
        SettingLogicHD.ResetCurrentSettings()
        InputSettingLogicHD.ResetAllADSSensitivity()
        for i, lists in pairs(self._MDVSettings) do
            for _, child in ipairs(lists[2]:GetAllChildren()) do
                child:ReloadSetting()
            end
        end
        InputSettingLogicHD.ResetAllZoomratedMDV()
        for i, lists in pairs(self._MDVSettings) do
            for _, child in ipairs(lists[1]:GetAllChildren()) do
                child:ReloadSetting()
            end
        end
        self:_RefreshSensitivityMode()
    end

    local contentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInputSensitivityTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    self.confirmWindowHandle =
        Module.CommonTips:ShowConfirmWindow(contentTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function SystemSettingHDInputSensitivityPanel:_RegisterNavGroup()
    if not IsHD() then
        return
    end

    if not self._NavGroup then
        self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollBox, self, "Hittest")
    end

	if self._NavGroup then
        local NavWidgetTable = {}

        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_4",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_5",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_1",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_3",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_6",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_SplitBtn_7",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityMode", NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("_wtMDV",NavWidgetTable)

        local _wt_wtMDVList = self:Wnd("_wtMDVList", UIWidgetBase)
        local _wt_wtADSList = self:Wnd("_wtADSList", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList, NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_MultipleChoice_6", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Tactics", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtReverseAll", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("DFVerticalBox", NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_1", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_2", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_3", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_4", NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Vehicles_1", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityMode_1", NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_2",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_3",NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("_wtMDV_1",NavWidgetTable)
        local _wt_wtMDVList_1 = self:Wnd("_wtMDVList_1", UIWidgetBase)
        local _wt_wtADSList_1 = self:Wnd("_wtADSList_1", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_1, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_1, NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_4",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_5",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_7",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityMode_2",NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("_wtMDV_2",NavWidgetTable)
        local _wt_wtMDVList_2 = self:Wnd("_wtMDVList_2", UIWidgetBase)
        local _wt_wtADSList_2 = self:Wnd("_wtADSList_2", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_2, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_2, NavWidgetTable)

        -- 固定翼
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_1",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_6",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_10",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_12",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_13",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_14",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_15",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityMode_4",NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("_wtMDV_4",NavWidgetTable)
        local _wt_wtMDVList_4 = self:Wnd("_wtMDVList_4", UIWidgetBase)
        local _wt_wtADSList_4 = self:Wnd("_wtADSList_4", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_4, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_4, NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_11",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_9",NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("SplitBtn_Vehicles_8",NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtSensitivityMode_3",NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("_wtMDV_3",NavWidgetTable)
        local _wt_wtMDVList_3 = self:Wnd("_wtMDVList_3", UIWidgetBase)
        local _wt_wtADSList_3 = self:Wnd("_wtADSList_3", UIWidgetBase)
        -- 从wtMDVList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtMDVList_3, NavWidgetTable)
        -- 从wtADSList中查找子控件，并添加到NavWidget
        self:_AddDynamicListToNavWidget(_wt_wtADSList_3, NavWidgetTable)
		
		for index, value in ipairs(NavWidgetTable) do
            self._NavGroup:AddNavWidgetToArray(NavWidgetTable[index])
        end

		--self._NavGroup:AddNavWidgetToArray(self._wtScrollBox)
        self._NavGroup:SetScrollRecipient(self._wtScrollBox)
		WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
end

function SystemSettingHDInputSensitivityPanel:_RemoveNavGroup()
    if not IsHD() then
        return
    end

	if self._NavGroup then
		self._NavGroup = nil
	end
	WidgetUtil.RemoveNavigationGroup(self)
end

-- 根据名字查找控件并加入导航组
function SystemSettingHDInputSensitivityPanel:_AddSelfWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if not IsHD() then
        return
    end

    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            table.insert(NavWidgetTable, targetWidget)
        end
    end
end

-- 根据SplitBtn控件的名字，查找Slider控件并加入导航组
function SystemSettingHDInputSensitivityPanel:_AddSliderWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if not IsHD() then
        return
    end

    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            local Slider = targetWidget:Wnd("Slider", UIWidgetBase)
            if Slider then
                local subSlider = Slider:Wnd("Slider_167", UIWidgetBase)
                if subSlider then
                    table.insert(NavWidgetTable, subSlider)
                end
            end
        end
    end
end

-- MDV和ASD列表会动态显示，需要寻找子控件加入导航组
function SystemSettingHDInputSensitivityPanel:_AddDynamicListToNavWidget(DynamicList, NavWidgetTable)
    if not IsHD() then
        return
    end

    local wtMDVChildren = DynamicList:GetAllChildren()

    for _, child in pairs(wtMDVChildren) do 
        local childSlider = child:Wnd("Slider", UIWidgetBase)

        if childSlider then
            local subSlider = childSlider:Wnd("Slider_167", UIWidgetBase)
            if subSlider then
                table.insert(NavWidgetTable, subSlider)
            end
        end
    end
end

--END MODIFICATION

return SystemSettingHDInputSensitivityPanel
