----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CollectionCommonItemView : LuaUIBaseView
local CollectionCommonItemView = ui("CollectionCommonItemView")
local IVShopItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVShopItemTemplate"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

function CollectionCommonItemView:Ctor() 
    self._wtSeriesTitle = self:Wnd("wtSeriesTitle",UITextBlock)
    self._wtItemTemplate = self:Wnd("wtItemTemplate",IVShopItemTemplate)
end

function CollectionCommonItemView:EnableTitle(bEnable, title)
    if not hasdestroy(self._wtSeriesTitle) then
        if bEnable == true and title and not string.isempty(title) then
            self._wtSeriesTitle:SelfHitTestInvisible()
            self._wtSeriesTitle:SetText(title)
        else
            self._wtSeriesTitle:Collapsed()
        end
    end
end

function CollectionCommonItemView:InitCollectionWeaponSkinItem(item, bCheckCurrentInstance)
    self.item = item
    if not hasdestroy(self._wtItemTemplate) then
        local bIsInRandomPool = false
        if ItemHelperTool.IsMysticalSkin(item.id) and not bCheckCurrentInstance then
            local ownedSkins = Server.CollectionServer:GetOwnedWeaponSkins()
            if ownedSkins[item.id] then
                for gid, value in pairs(ownedSkins[item.id]) do
                    bIsInRandomPool = CollectionLogic.CheckIsSkinInRandomPool(item.id, gid)
                    if bIsInRandomPool then
                        break
                    end 
                end
            end
        else
            bIsInRandomPool = CollectionLogic.CheckIsSkinInRandomPool(item.id, item.gid)
        end
        local baseWeaponId = CollectionLogic.GetBaseWeaponIdFromSkinId(item.id)
        self._wtItemTemplate:InitCollectionWeaponSkinItem(item, 
        baseWeaponId,  
        Server.CollectionServer:IsOwnedWeaponSkin(item.id),
        CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) and not CollectionLogic.CheckIsRandomPoolEnabled(baseWeaponId), 
        ItemHelperTool.IsMysticalSkin(item.id) and Server.CollectionServer:GetWeaponSkinInstanceNum(item.id) or nil,
        Server.CollectionServer:GetRightsType(item.id, item.gid),
        bIsInRandomPool)
    end
end

function CollectionCommonItemView:InitCollectionRandomSkinItem(item)
    self.item = item
    if not hasdestroy(self._wtItemTemplate) then
        self._wtItemTemplate:InitCollectionRandomSkinItem(item, CollectionLogic.CheckIsRandomPoolEnabled(item.baseWeaponId))
    end
end

function CollectionCommonItemView:InitCollectionCamouflageItem(info, bIsMasterChallenge, currentNum, maxNum, bActivated)
    self.item = nil
    if not hasdestroy(self._wtItemTemplate) then
        self._wtItemTemplate:InitCollectionCamouflageItem(info, bIsMasterChallenge, currentNum, maxNum, bActivated)
    end
end

function CollectionCommonItemView:InitCollectionMandelBrickItem(item)
    self.item = item
    if not hasdestroy(self._wtItemTemplate) then
        self._wtItemTemplate:InitCollectionMandelBrickItem(self.item)
    end
end

function CollectionCommonItemView:SetSize(x, y)
    local sizeBox =  self._wtItemTemplate:Wnd("SizeBox_0", UIWidgetBase)
    sizeBox:SetCppValue("WidthOverride",x) 
    sizeBox:SetCppValue("HeightOverride",y) 
end


function CollectionCommonItemView:SetSelected(bValue)
    self._wtItemTemplate:SetSelected(nil, bValue)
end

function CollectionCommonItemView:OnClicked()
    self._wtItemTemplate:OnClicked()
end

function CollectionCommonItemView:BindClickCallback(fClickCb)
    self._wtItemTemplate:BindClickCallback(fClickCb)
end

function CollectionCommonItemView:GetItemView()
    return self._wtItemTemplate
end

function CollectionCommonItemView:SetButtonEnable(bEnable)
    self._wtItemTemplate:SetButtonEnable(bEnable)
end

return CollectionCommonItemView
