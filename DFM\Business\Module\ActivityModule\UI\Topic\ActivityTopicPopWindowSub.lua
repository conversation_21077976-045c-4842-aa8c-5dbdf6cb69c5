----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityTopicPopWindowSub : LuaUIBaseView
local ActivityTopicPopWindowSub = ui("ActivityTopicPopWindowSub")

function ActivityTopicPopWindowSub:Ctor()
    --页面图片
    self._wtImage = self:Wnd("wMapImage", DFCDNImage)
    --页面描述
    self._wtDesc = self:Wnd("TextBlock_119", UITextBlock)
    --页面标题
    self._wtTitle = self:Wnd("_wtMapNameText", UITextBlock)

    --图标
    self._wtIconImg = self:Wnd("DFImage_95", UIImage)
    --策划某改
    self._wtSizeBox = self:Wnd("PlatformSizeBox_0", UIWidgetBase)
end

---------------------------------------------------生命周期-----------------------------------------------------
function ActivityTopicPopWindowSub:OnShowBegin()

end

function ActivityTopicPopWindowSub:OnHide()

end

function ActivityTopicPopWindowSub:_InitEvent()

end

-----------------------------------------------------UI刷新-----------------------------------------------------
function ActivityTopicPopWindowSub:RefreshItemWidget(desc, PicUrl, title)
    if desc then
        self._wtDesc:SetText(desc)
    end
    if PicUrl and PicUrl ~= "" then
        local backImg = "Resource/Texture/Activity/" .. PicUrl
        local bAutoResize = false
        if self._data and self._data.bAutoResize then
            bAutoResize = true
        end
        self._wtImage:SetCDNImage(backImg, bAutoResize, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    end
    if title then
        self._wtTitle:SetText(title)
    end
end

function ActivityTopicPopWindowSub:SetTopicItem(data)
    if data then
        self._data = data
        if data.isIcon == false then
            self._wtIconImg:Collapsed()
        end
        if data.iconX and data.iconY then
            self._wtSizeBox:SetWidthOverride(data.iconX)
            self._wtSizeBox:SetHeightOverride(data.iconY)
        end
    end
end

return ActivityTopicPopWindowSub