----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMJump)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class JumpModule : ModuleBase
local JumpModule = class("JumpModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local JumpLogic = require "DFM.Business.Module.JumpModule.JumpLogic"

function JumpModule:Ctor()
    self._bBindEvent = false
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function JumpModule:OnInitModule()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function JumpModule:OnLoadModule()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function JumpModule:OnUnloadModule()
    self:_UnBindEvent()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function JumpModule:OnDestroyModule()
    self:_UnBindEvent()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function JumpModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        self:_UnBindEvent()
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function JumpModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        self:_BindEvent()
    end
end

function JumpModule:_BindEvent()
    if not self._bBindEvent then
        Facade.UIManager.Events.evtStackUIChanged:AddListener(JumpLogic.ProcessOnStackUIPop)
        self.Config.evtJumpModeCallback:AddListener(JumpLogic.JumpModeSucess)

        local fURLCallbackIns =
            CreateCallBack(
            function(self, lastUrl)
                logerror("JumpModule:fURLCallbackIns, lastUrl:", lastUrl)
                -- 判断非异账号
                local result = lastUrl:match("?jump=(.*)")
                local jumpID = Server.JumpServer:GetJumpIdByDeepline(result)
                if jumpID > 0 then
                    Module.Jump:JumpByID(jumpID)
                end
                return false
            end,
            self
        )
        if self._deepLineHand == nil then
            logerror("JumpModule:AddDeepLinkCallback")
            self._deepLineHand = Module.DeepLink:AddDeepLinkCallback(fURLCallbackIns)
            local url = Module.DeepLink:GetLastDeepLinkUrl()
            if url ~= "" then
                logerror("JumpModule:fURLCallbackIns, url:", url)
                local result = url:match("?jump=(.*)")
                local jumpID = Server.JumpServer:GetJumpIdByDeepline(result)
                if jumpID > 0 then
                    Module.Jump:JumpByID(jumpID)
                end
            end
        end

        self._bBindEvent = true
    end
end

function JumpModule:_UnBindEvent()
    if self._bBindEvent then
        Facade.UIManager.Events.evtStackUIChanged:RemoveListener(JumpLogic.ProcessOnStackUIPop)
        self.Config.evtJumpModeCallback:RemoveListener(JumpLogic.JumpModeSucess)
        self._bBindEvent = false
        if self._deepLineHand then
            logerror("JumpModule:RemoveDeepLinkCallback self._deepLineHand:", self._deepLineHand)
            Module.DeepLink:RemoveDeepLinkCallback(self._deepLineHand)
            self._deepLineHand = nil
        end
    end
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------
--@param moduleName: EJumpToModule 跳转的系统名
--@param jumpArgs: 跳转函数的参数，可为空，jumpCallback(jumpArgs)
--@param jumpCallback: 具体跳转的逻辑，如果为空，则默认调用 Module[moduleName]:Jump(jumpArgs)
--                     如果不为空，需要返回实际打开/重新激活的 uiNavIdList
--@param jumpType: EJumpType 跳转方式，默认是EJumpType.KeepModuleSingle
--@param backType: EJumpBackType 跳转的界面被关闭时的返回策略，默认是EJumpBackType.CloseOnlySelf
function JumpModule:JumpTo(moduleName, jumpArgs, jumpCallback, jumpType, backType)
    return JumpLogic.JumpTo(moduleName, jumpArgs, jumpCallback, jumpType, backType)
end

function JumpModule:CheckCanJumpByID(jumpID, checkJumpArgs, needTip)
    return JumpLogic.CheckCanJumpByID(jumpID, checkJumpArgs, needTip)
end

--@param jumpID: 跳转的表格id
function JumpModule:JumpByID(jumpID, jumpArgs, jumpCallback, checkJumpArgs, jumpSource)
    --使用id读取跳转表格内容
    return JumpLogic.JumpByID(jumpID, jumpArgs, jumpCallback, jumpSource)
end

function JumpModule:TriggerJumpModeSuccess()
    self.Config.evtJumpModeCallback:Invoke()
end

function JumpModule:HasJumpSingleInWait()
    local currentJumpIndex = Module.Jump.Field:GetCurrentJumpIndex()
    local data = Module.Jump.Field:GetCurrentJumpSingleData(currentJumpIndex)
    if data and data:InWaitStage() then
        return true
    end
    return false
end
return JumpModule
