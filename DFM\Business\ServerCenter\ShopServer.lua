----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSShop)
----- LOG FUNCTION AUTO GENERATE END -----------



local SlotConfig = require "DFM.Business.DataStruct.InventoryStruct.SlotConfig"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemSortTool = require "DFM.StandaloneLua.BusinessTool.ItemSortTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ReqPartDigestWrapper = require "DFM.YxFramework.Managers.Proto.Wrapper.ReqPartDigestWrapper"
require "DFM.Business.DataStruct.ShopStruct.ShopItemStruct"
require "DFM.Business.DataStruct.ShopStruct.ShopItemDetailStruct"
require "DFM.Business.DataStruct.ShopStruct.MerchantStruct"
local DEFALUT_SHOP_OPT_TYPE = EShopOptType.Purchase
local RECYCLE_PARAM = 0
local DEFAULT_FETCH_THRESHOld = 300 --拉取动态指导价默认间隔
local DEFAULT_CHECK_THRESHOld = 5 --拉取动态指导价更新间隔

local EItemId2RecyclePriceID = {}
EItemId2RecyclePriceID.currencyClientType = "1"
EItemId2RecyclePriceID.num = "2"                          -- 原生价：满耐久
EItemId2RecyclePriceID.zeroNum = "3"                      --- 原生价：0耐久
EItemId2RecyclePriceID.mallNum = "4"                      --- 回收价：满耐久
EItemId2RecyclePriceID.mallZeroNum = "5"                  --- 回收价：0耐久
EItemId2RecyclePriceID.recyclePricePercent = "6"          --- 回收比例

local PriceConfigInfoDefine = MakeDynamicUDClass(
    {EItemId2RecyclePriceID.currencyClientType, FieldType.Integer, 0},
    {EItemId2RecyclePriceID.recyclePricePercent, FieldType.Number, 0}
)


local ShopServer = class("ShopServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

function ShopServer:Ctor()
    self.Tables = {
        TraderLocalTable = Facade.TableManager:GetTable("Trader"),
        MallLocalTable = Facade.TableManager:GetTable("Mall"),
    }
	self.Events = {
        evtShopItemReGenerated = LuaEvent:NewIns("ShopServer.evtShopItemReGenerated"),
        evtMerchantDataChanged = LuaEvent:NewIns("ShopServer.evtMerchantDataChanged"),
        evtMerchantLvChanged = LuaEvent:NewIns("ShopServer.evtMerchantLvChanged"),
        evtTimeChangeFresh = LuaEvent:NewIns("ShopServer.evtTimeChangeFresh"),
        evtSItemFreshIsInPansicing = LuaEvent:NewIns("ShopServer.evtSItemFreshIsInPansicing"),
        evtShopOptTypeChanged = LuaEvent:NewIns("ShopServer.evtShopOptTypeChanged"),
        evtShopFinished = LuaEvent:NewIns("ShopServer.evtShopFinished"),
        evtPanicSItemTickFresh = LuaEvent:NewIns("ShopServer.evtPanicSItemTickFresh"),
        evtMixShopItemsFresh = LuaEvent:NewIns("ShopServer.evtMixShopItemsFresh"),
        evtMerchantGiftFresh = LuaEvent:NewIns("ShopServer.evtMerchantGiftFresh"),
        evtUnlockNewExchangeIdListChanged = LuaEvent:NewIns("ShopServer.evtUnlockNewExchangeIdListChanged"),
        evtSellRes = LuaEvent:NewIns("InventoryServer.evtSellRes"),
        evtDynamicGuidPriceFinishFetch = LuaEvent:NewIns("ShopServer.evtDynamicGuidPriceFinishFetch"),
        evtLabelNo1ConfigUpdated = LuaEvent:NewIns("ShopServer.evtLabelNo1ConfigUpdated"),
    }
    ---@class PriceConfig
    ---@field itemId number
    ---@field recyclePricePercent number
    ---@field currencyType number

    ---@type EShopOptType
    self._currentShopOptType = DEFALUT_SHOP_OPT_TYPE

    --- Arrays
    ---@type array<_,ShopItemStruct>
    self.mallShopItemList = {}
    ---@type array<_,ShopItemStruct> 即将上下架需要刷新的商品列表
    self._mallRefreshShopItemList = {}
    ---@type array<_,ShopItemStruct>
    self.curMixShopItems = {}

    --- Maps
    ---@type table<merchantId, Slot>
    self._mapMerchantId2Slots = {}
    -- include on sell or not
    ---@type table<itemId, buyPrice>
    self._mapItemId2BuyPrice = {}
    ---@type table<exchangeId, ShopItemStruct>
    self._mapExchangeID2SItem = {}
    ---@type table<itemId, exchangeIdList>
    self._mapID2ExchangeIdList = {}
    self._mapItemId2MallRecycle = {}
    ---@type table<itemId, recyclePrice>
    self._mapItemId2PriceConfig = {}
    ---@type table<itemId, dynamicGuidePrice>
    self._mapItemId2DynamicGuidePrice = {}
    ---@type table<itemId, boolean>
    self.cannotRecycleList = {}

    -- ---@type table<SlotType, SlotConfig>
    -- self._mapPropSlotId2Config = {}

    self._cacheIdDur2DynamicGuidePrice = {}
    self._cacheIdDur2MallRecyclePrice = {}

    self._cacheMapLimitValues = {}

    ---@type table<exchangeId, ItemDetailInfo>
    -- self._mapExchangeId2SItemDetail = {}

    --- 是否上架直购（不包含抢购）
    ---@type table<itemId, boolean>
    self._mapItemId2IsOnSell = {}

    --- 当前商人页签列表(用本地数据初始化)
    self._mapMainId2DividedTimeConfig = ShopHelperTool.GetTraderPageLabelConfig()
    --- 当前商人商品页签倒计时列表
    self._curActiveLabelNo1List, self._curRefreshLableNo1List = self:_InitActiveAndRefreshLabelNo1List()

    self.curHour = self:GetCurHour()
    self.sync_digest = ""

    -- self.limitItems = {}
    self._mapPrintedErrorItems = {}

    self._shopCdnVersion = nil

    self._merchantStructList = {}
    self._mapPrestigeId2Mid = {}
    self._mapMid2LimitShopItem = {}
    self._mapServerTimp2LimitSItemList = {}
    self._guidPriceCD = DEFAULT_FETCH_THRESHOld
    self._startLimitTick = false
    self._isHardCheck = false
	self:AddListeners()

end
--------------------------------------------------------------------------
--- 生命周期
--------------------------------------------------------------------------
function ShopServer:OnInitServer()
    -- self:InitShopTabConfig()
    self:InitMerchantDatas()
end

function ShopServer:OnDestroyServer()
    self:RemoveAllLuaEvent()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self.buyTimerHandle = nil
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
function ShopServer:OnLoadingLogin2Frontend(gameFlowType)
    -- self._mapExchangeId2SItemDetail = {}
    self.sync_digest = ""
    self:FetchServerData()
end

function ShopServer:OnLoadingGame2Frontend(gameFlowType)
    -- self._mapExchangeId2SItemDetail = {}
    self.Tables = {
        TraderLocalTable = Facade.TableManager:GetTable("Trader"),
        MallLocalTable = Facade.TableManager:GetTable("Mall"),
    }
    self.sync_digest = ""
    self:FetchServerData()
end

function ShopServer:OnLoadingFrontend2Game(gameFlowType)
    -- self:GenerateInGameItemDetailInfo()
    self.mallShopItemList = {}
    self:PrintTableLength("进局内清理self.mallShopItemList",self.mallShopItemList)
    self._mallRefreshShopItemList = {} --下一小时内上下架会变化的商品(_PartTwoFetchBuyGoods)
    self.curMixShopItems = {}
    self._mapExchangeID2SItem = {}
    self._mapID2ExchangeIdList = {}
    self._mapMid2LimitShopItem = {}
    self._mapServerTimp2LimitSItemList = {}
    self._mapItemId2IsOnSell = {} --id对应商品是否上架(_PartTwoFetchBuyGoods)
    self.Tables = {}
    self.buyTimerHandle = nil
    self.lastBuyTimeStamp = nil

    --局内用不到的价格缓存
    self._mapItemId2MallRecycle = {} --商店回收价配装(_PartOneFetchRecycleGoods)
    self:PrintTableLength("进局内清理self._mapItemId2MallRecycle", self._mapItemId2MallRecycle)
    self._mapItemId2BuyPrice = {} --商品价格配置(_PartTwoFetchBuyGoods)
    self._newUnlockExchangeIdList = {} --新解锁ExchangeIdList局内不需要(_PartTwoFetchBuyGoods -> _FetchUnlockExchangeIdList)

    --限购商品
    self._cacheMapLimitValues = {} --_FetchGoodsLimit

    -- self._mapExchangeId2SItemDetail = {}
    self:FetchDynamicGuidePirce()
end

--------------------------------------------------------------------------
--- 流程、事件相关
--------------------------------------------------------------------------
function ShopServer:AddListeners()
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtPrestigeLvUpgraded, self.OnPrestigeLvUpgraded, self)
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtSeasonLevelUpdate, self.OnSeasonLevelUpgraded, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self.OnUpdateQuestState, self)
    self:AddLuaEvent(Server.TeamServer.Events.evtJoinTeam, self.OnJoinTeam, self)
    
    Facade.ProtoManager:AddNtfListener("CSMallPanicBuyNtf", self._OnCSMallPanicBuyNtf, self)
    Facade.ProtoManager:AddNtfListener("CSMallMerchantChangeNtf", self._OnCSMallMerchantChangeNtf, self)
end

function ShopServer:OnGameFlowChangeEnter(gameFlowType)
    --只有局外有tick
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        Timer.FastAddObjTimer(self, 1, 0, self.Update)
    end
    --进入SOL时 进入局内时
    if gameFlowType == EGameFlowStageType.SafeHouse then
        self:FetchDynamicGuidePirce()
    end
    self:OnLoadingFrontEnd2MP(gameFlowType)

end

function ShopServer:OnLoadingFrontEnd2MP(gameFlowType)
    logerror('ShopServer OnGameFlowChangeEnter', table.keyof(EGameFlowStageType, gameFlowType))
    if gameFlowType == EGameFlowStageType.LobbyToGame and Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
        self._mapItemId2PriceConfig = {}
        self:PrintTableLength("进MP清理self._mapItemId2PriceConfig", self._mapItemId2PriceConfig)
    end
end

function ShopServer:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        Timer.FastRemoveObjTimer(self)
    end
end

function ShopServer:Update(dt)
    if self.lastBuyPanicSItem and self.lastBuyPanicSItem:GetIsPanicSItem() then
        if table.contains(self.curMixShopItems, self.lastBuyPanicSItem) then
            self:_DoFreshPansicShopItem(self.lastBuyPanicSItem.exchangeId)
        end
    end

    --- 限购倒计时
    self:SyncServerLimitGoods()

    -- 动态指导价(登录前不允许拉取数据，局内不允许拉取数据)
    self:SyncServerGuidPrice()

    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow ~= EGameFlowStageType.Login and gameflow ~= EGameFlowStageType.None and gameflow ~= EGameFlowStageType.Game then
    -- 商品和页签倒计时
        self:RefreshCacheLabelConfig()
        self:RefreshCacheSItemList()
        -- 与服务器同步倒计时
        self:SyncSItemAndLabelNo1Info()
    end

end

function ShopServer:SyncServerLimitGoods()
    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if self._startLimitTick and self._mapServerTimp2LimitSItemList and gameflow ~= EGameFlowStageType.Login and gameflow ~= EGameFlowStageType.None and gameflow ~= EGameFlowStageType.Game then
        local curTimestamp = Facade.ClockManager:GetLocalTimestamp()
        if curTimestamp % 60 == 0 then --总是在整点更新
            for _, timeStamp in pairs(table.keys(self._mapServerTimp2LimitSItemList)) do
                if curTimestamp >= timeStamp then
                    local limitSItemsList = self._mapServerTimp2LimitSItemList[timeStamp]
                    if limitSItemsList and #limitSItemsList > 0 then
                        --即使当前没买，也需要刷新，因为UI需要知道下一个刷新点时间戳_-- 我屈服了,she2再看
                        for _, limitSItem in pairs(limitSItemsList) do
                            if self._bForceCheck then --强刷新
                                self:_FetchGoodsLimit()
                                self._mapServerTimp2LimitSItemList = {}
                                break
                            --弱更新
                            elseif limitSItem.limitMallInfo and limitSItem.limitMallInfo.buy_num and limitSItem.limitMallInfo.buy_num > 0 then
                                self:_FetchGoodsLimit()
                                self._mapServerTimp2LimitSItemList = {}
                                break
                            end
                        end
                    end
                end
                if self._mapServerTimp2LimitSItemList == {} then
                    break
                end
            end
        end
    end
end

function ShopServer:SyncServerGuidPrice()
    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow ~= EGameFlowStageType.Login and gameflow ~= EGameFlowStageType.None and gameflow ~= EGameFlowStageType.Game then
        self._guidPriceCD = self._guidPriceCD - 1
        if self._guidPriceCD <= 0 then
            self:FetchDynamicGuidePirce()
            self._guidPriceCD = DEFAULT_FETCH_THRESHOld
        end
    end
end

--刷新即将上下架的页签列表
function ShopServer:RefreshCacheLabelConfig()
    if self._curRefreshLableNo1List and next(self._curRefreshLableNo1List) then
        local updatedLabelNo1List = {}
        for _, labelNo1 in pairs(self._curRefreshLableNo1List) do
            local refreshConfig = self._mapMainId2DividedTimeConfig[labelNo1]
            if refreshConfig and refreshConfig.bIsLimitTime == 1 then
                local curTimeStamp = Facade.ClockManager:GetLocalTimestamp()
                if table.keyof(self._curActiveLabelNo1List, labelNo1) == 0 then
                    -- 上架
                    if curTimeStamp >= refreshConfig.startTime and curTimeStamp <= refreshConfig.endTime then
                        table.insert(self._curActiveLabelNo1List, labelNo1)
                        table.insert(updatedLabelNo1List, labelNo1)
                        --上架暂时不移除refresh列表，因为保不准接下来1h内要下架
                    end
                --下架
                else
                    if curTimeStamp > refreshConfig.endTime then
                        table.removebyvalue(self._curActiveLabelNo1List, labelNo1)
                        table.insert(updatedLabelNo1List, labelNo1)
                        table.removebyvalue(self._curRefreshLableNo1List, labelNo1)
                    end
                end
            end
        end
        if #updatedLabelNo1List > 0 then
            self.curAvailableLabelNo1List = self:GetCurAvailableLabelNo1List()
            self.Events.evtLabelNo1ConfigUpdated:Invoke(updatedLabelNo1List)
        end
    end
end

-- 刷新即将上下架的商品列表
function ShopServer:RefreshCacheSItemList()
    if self._mallRefreshShopItemList and next(self._mallRefreshShopItemList) then
        local refreshSItemList = {}
        local bNewUnlockExchangeIdListChanged = false
        for _, sItem in pairs(self._mallRefreshShopItemList) do
            --上架
            if sItem:CheckIsOnShelve() then
                if self._mapExchangeID2SItem[sItem.exchangeId] == nil then
                    table.insert(self.mallShopItemList, sItem)
                    self._mapExchangeID2SItem[sItem.exchangeId] = sItem
                    self._mapID2ExchangeIdList = setdefault(self._mapID2ExchangeIdList, {})
                    self._mapID2ExchangeIdList[sItem.item.id] = setdefault(self._mapID2ExchangeIdList[sItem.item.id], {})
                    table.insert(self._mapID2ExchangeIdList[sItem.item.id], sItem.exchangeId)
                    if sItem.presetId and sItem.presetId ~= 0 then
                        self._mapID2ExchangeIdList[sItem.presetId] = setdefault(self._mapID2ExchangeIdList[sItem.presetId], {})
                        table.insert(self._mapID2ExchangeIdList[sItem.presetId], sItem.exchangeId)
                    end
                    table.insert(refreshSItemList, sItem)
                    if table.keyof(self._newUnlockExchangeIdList, sItem.exchangeId) == 0 then
                        table.insert(self._newUnlockExchangeIdList, sItem.exchangeId)
                        bNewUnlockExchangeIdListChanged = true
                    end
                end
            --下架
            else
                if self._mapExchangeID2SItem[sItem.exchangeId] ~= nil then
                    table.removebyvalue(self.mallShopItemList, sItem)
                    self._mapExchangeID2SItem[sItem.exchangeId] = nil
                    table.insert(refreshSItemList, sItem)
                    table.removebyvalue(self._mallRefreshShopItemList, sItem)
                end
                if self._mapID2ExchangeIdList and self._mapID2ExchangeIdList[sItem.item.id] and table.keyof(self._mapID2ExchangeIdList[sItem.item.id], sItem.exchangeId) ~= 0 then
                    table.removebyvalue(self._mapID2ExchangeIdList[sItem.item.id], sItem.exchangeId, true)
                end
                if self._mapID2ExchangeIdList and self._mapID2ExchangeIdList[sItem.presetId] and table.keyof(self._mapID2ExchangeIdList[sItem.presetId], sItem.exchangeId) ~= 0 then
                    table.removebyvalue(self._mapID2ExchangeIdList[sItem.presetId], sItem.exchangeId, true)
                end
            end
        end
        if #refreshSItemList > 0 then
            self.Events.evtShopItemReGenerated:Invoke(refreshSItemList)
        end
        -- 处理红点商品
        if bNewUnlockExchangeIdListChanged then
            self.Events.evtUnlockNewExchangeIdListChanged:Invoke(self._newUnlockExchangeIdList)
        end
    end
end

-- 与后台同步刷新时间戳拉取新配置
function ShopServer:SyncSItemAndLabelNo1Info()
    if self._nextRefreshTimeStamp and type(self._nextRefreshTimeStamp) == "number" then
        local curTimeStamp = Facade.ClockManager:GetLocalTimestamp()
        if curTimeStamp >= self._nextRefreshTimeStamp then
            self._nextRefreshTimeStamp = nil
            self:FetchDividedTimeConfig()
        end
    end
end

-- 拉取分段时间配置
function ShopServer:FetchDividedTimeConfig()
    self:_FetchTraderPageLabelConfig()
    self:TryCompareShopCdnVer() --仅拉取商品列表
end

function ShopServer:GetCurHour()
    local localTimestamp = Facade.ClockManager:GetLocalTimestamp()
    local hour = localTimestamp > 0 and TimeUtil.GetCurrentHourByStamp(localTimestamp) or TimeUtil.GetCurrentHour()
    if self:IsBuildRegionCN() then
        return hour + 8
    elseif (self:IsBuildRegionGlobal() or self:IsBuildRegionGA()) then
        return hour
    end
    return hour
end

function ShopServer:IsBuildRegionCN()
    return BUILD_REGION_CN or BUILD_REGION_CN_EXPER
end

function ShopServer:IsBuildRegionGlobal()
    return BUILD_REGION_GLOBAL or BUILD_REGION_GLOBAL_EXPER
end

function ShopServer:IsBuildRegionGA()
    return BUILD_REGION_GA or BUILD_REGION_GA_EXPER
end

---@param res pb_CSGMRes
function ShopServer:AdjustTime(res)
    self:FreshWhenHourChanged()
end

function ShopServer:FreshWhenHourChanged()
    -- self.curHour = self:GetCurHour()
    -- local timeChangeCallBackIns = CreateCallBack(self._OnTimeChangeFromServer, self)
    -- local fOnFetchGoodsLimitCallback = function()
    --     self:FreshMerchantsOnly(timeChangeCallBackIns)
    -- end
    -- self:_FetchGoodsLimit(fOnFetchGoodsLimitCallback)
    -- loginfo('！！！！！！！！！整点时间刷新啦')
end

function ShopServer:_OnTimeChangeFromServer()
	self.Events.evtTimeChangeFresh:Invoke()
end

function ShopServer:_OnCSMallPanicBuyNtf(ntf)
    logwarning('抢购完成！！！！')
    local bSuccess
    if ntf.change and ntf.change.add_props then
        bSuccess = true
    else
        bSuccess = false
    end
    ---@type SinglePropMsg
    local singlePropMsg = {
        bSuccess = bSuccess,
        shopItemBuyInfo = {shopItem = self.lastBuyPanicSItem, curBuyNum = self.lastBuyPanicNum},
        result = ntf.result or 0,
        prop_changes = ntf.change.add_props,
    }
    self:HandleSinglePropFinished(singlePropMsg)
end

function ShopServer:_OnCSMallMerchantChangeNtf(ntf)
    local intimacyDeltas = {}
    local bLvChange = false
    for _,merchantData in pairs(ntf.changes) do
        if merchantData.merchant then
            local merchantDataOld = self:GetMerchantDataById(merchantData.merchant.id)

            local intimacyDelta = merchantData.merchant.intimacy - merchantDataOld.intimacy
            if intimacyDelta ~= 0 then
                table.insert(intimacyDeltas,{id = merchantData.merchant.id, delta = intimacyDelta})
            end
            local vipCreditDelta = merchantData.merchant.vip_score - merchantDataOld.vip_score

            local intiLvDelta = merchantData.merchant.intimacy_lvl - merchantDataOld.intimacy_lvl
            local vipLvDelta = merchantData.merchant.vip_lvl - merchantDataOld.vip_lvl
            if vipLvDelta > 0 or intiLvDelta > 0 then
                bLvChange = true
            end

            local merchantStruct = self:GetMerchantStructById(merchantData.merchant.id)
            merchantStruct:SetMerchantData(merchantData.merchant)

            loginfo('id为',merchantData.merchant.id,'的商人好感度变化为',intimacyDelta,'Vip积分变化为',vipCreditDelta)
        end
    end
    if ntf.changes then
        -- dump(ntf.changes)
    end

    --- TODO 事件拆分
    self.Events.evtMerchantDataChanged:Invoke(intimacyDeltas,self._tempSendItem)
    self._tempSendItem = nil
    if bLvChange then
        self.Events.evtMerchantLvChanged:Invoke()
    end
end

--------------------------------------------------------------------------
--- 购买方式相关
--------------------------------------------------------------------------
function ShopServer:SetCurrentOptType(shopOptType)
    if self._currentShopOptType ~= shopOptType then
        self._currentShopOptType = shopOptType
        self.Events.evtShopOptTypeChanged:Invoke(shopOptType)
    end
end

function ShopServer:CheckExchangeTypeEnable(exchangeType)
    if self._currentShopOptType == EShopOptType.Purchase then
        --- 合并直购和兑换
        return exchangeType == EShopOptType.Purchase or exchangeType == EShopOptType.Exchange
    elseif self._currentShopOptType == EShopOptType.Exchange then
        --- 只显示兑换时
        return exchangeType == EShopOptType.Exchange
    end
end

--------------------------------------------------------------------------
--- 商品分类相关
--------------------------------------------------------------------------
-- function ShopServer:InitShopTabConfig()
--     self._mapPropSlotId2Config = {}
--     for propSlotId, slotConfig in pairs(SlotConfig.AllSlotConfigs) do
--         if propSlotId > ESlotType.GoodsStart and propSlotId < ESlotType.GoodsEnd then
--             self._mapPropSlotId2Config[propSlotId] = slotConfig
--         end
--     end
-- end

-- function ShopServer:CheckIsShopSlotConfig(propSlotId)
--    return self._mapPropSlotId2Config[propSlotId]
-- end


--------------------------------------------------------------------------
--- 商人基本数据
--------------------------------------------------------------------------
---@param shopItem ShopItemStruct
-- function ShopServer:FilterShopItem(shopItem, curPropSlotId, curPropSlotSubId)
--     local itemId = shopItem.item.id
--     local slotConfig = self._mapPropSlotId2Config[curPropSlotId]
--     -- print('check slot type:', curPropSlotId, curPropSlotSubId)
--     return slotConfig:CheckItemFit(itemId)
-- end


function ShopServer:InitMerchantDatas()
    self._merchantStructList = {}
    if not self.Tables.TraderLocalTable then self.Tables.TraderLocalTable = Facade.TableManager:GetTable("Trader") end
    if self.Tables.TraderLocalTable then
        for traderId, traderRow in pairs(self.Tables.TraderLocalTable) do
            local traderConfig = {
                TraderID = traderRow.TraderID,
                TraderName = traderRow.TraderName,
                TraderTitle = traderRow.TraderTitle,
                TraderCenterType = traderRow.TraderCenterType,
                TraderTypeNameList = traderRow.TraderTypeNameList,
                TraderTypeIDList = traderRow.TraderTypeIDList,
                TraderIconPath = traderRow.TraderIconPath,
                UnlockRepLv = traderRow.UnlockRepLv,
            }
            local merchantStruct = MerchantStruct:NewIns(traderConfig)
            table.insert(self._merchantStructList, merchantStruct)
            self._mapPrestigeId2Mid[merchantStruct.prestigeId] = traderConfig.TraderID
        end
    end
    table.sort(self._merchantStructList, function(a, b)
        return a.merchantId < b.merchantId
    end)
end

function ShopServer:GetMerchantIdList()
    local midList = {}
    for idx, merchantStruct in ipairs(self._merchantStructList) do
        if  idx <= 5 then
            table.insert(midList, merchantStruct.merchantId)
        end
    end
    return midList
end

function ShopServer:GetMerchantStructList()
    return self._merchantStructList
end

function ShopServer:GetMerchantDataById(merchantId)
    for idx, merchantStruct in ipairs(self._merchantStructList) do
        if merchantStruct.merchantId == merchantId then
            return merchantStruct:GetMerchantData()
        end
    end
end

function ShopServer:GetMerchantStructById(merchantId)
    for idx, merchantStruct in ipairs(self._merchantStructList) do
        if merchantStruct.merchantId == merchantId then
            return merchantStruct
        end
    end
end

function ShopServer:GetMerchantStructByPid(pid)
    for idx, merchantStruct in ipairs(self._merchantStructList) do
        if merchantStruct:GetPrestigeId() == pid then
            return merchantStruct
        end
    end
end


--------------------------------------------------------------------------
--- 商人协议相关
--------------------------------------------------------------------------
--- 每个Server用于拉取全量数据的接口
function ShopServer:FetchServerData()
    loginfo('ShopServer:FetchServerData()')
    self:FetchMerchantsWithGoods()
end

function ShopServer:FreshMerchantsOnly(fCallbackIns)
    self._mapMerchantId2Slots = {}
    local fOnGetMerchantsRes = function(res)
        if res.result == 0 then
            for _, merchant in ipairs(res.merchants) do
                local merchantStruct = self:GetMerchantStructById(merchant.id)
                if merchantStruct then
                    merchantStruct:SetMerchantData(merchant)
                end
                self._mapMerchantId2Slots[merchant.id] = {}
                local TraderTable = self.Tables.TraderLocalTable
                if not TraderTable then
                    self.Tables.TraderLocalTable = Facade.TableManager:GetTable("Trader")
                    TraderTable = self.Tables.TraderLocalTable
                end
                local merchantRow = TraderTable[tostring(merchant.id)]
                if merchantRow then
                    local defaultSlot = merchantRow.DefaultSlot
                    table.insert(self._mapMerchantId2Slots[merchant.id], defaultSlot)

                    local allMatchSlots = StringUtil.StringSplit(merchantRow.allMatchSlots, ",")
                    if allMatchSlots then
                        for _,Slot in pairs(allMatchSlots) do
                            table.insert(self._mapMerchantId2Slots[merchant.id], Slot)
                        end
                    end
                end
            end
			if fCallbackIns then
				fCallbackIns()
                fCallbackIns = nil
			end
        else
            logerror('拉取商人失败')
        end
    end
    local req = pb.CSMallGetMerchantsReq:New()
    req:Request(fOnGetMerchantsRes, {bNeedResendAfterReconnected = true})
end

--- 拉取页签配置
---@struct TraderPageConfig
---@field label_no1 number
---@field start_time number
---@field end_time number
function ShopServer:_FetchTraderPageLabelConfig(fCallback)
        local fOnGetTraderPageLabelConfigRes = function(res)
            if res.result == 0 then
                -- Config配置
                if res.cfg_list then
                    self._mapMainId2DividedTimeConfig = {}
                    for _, sconfig in ipairs(res.cfg_list) do
                        self._mapMainId2DividedTimeConfig[sconfig.label_no1] = {
                            bIsLimitTime = sconfig.limit_time,
                            startTime = sconfig.start_time,
                            endTime = sconfig.end_time,
                        }
                    end
                    self._curActiveLabelNo1List, self._curRefreshLableNo1List = self:_InitActiveAndRefreshLabelNo1List()
                end
                -- nextRefreshtime
                if res.next_get_time and res.next_get_time ~= 0 then
                    self._nextRefreshTimeStamp = res.next_get_time
                else
                    self._nextRefreshTimeStamp = setdefault(self._nextRefreshTimeStamp, Facade.ClockManager:GetLocalTimestamp())
                    self._nextRefreshTimeStamp = self._nextRefreshTimeStamp + 60*60
                    logerror('ShopServer:_FetchTraderPageLabelConfig res.next_get_time invalid =',res.next_get_time,'客户端自增self._nextRefreshTimeStamp =',self._nextRefreshTimeStamp)
                end
                self.curAvailableLabelNo1List = self:GetCurAvailableLabelNo1List()
                if fCallback then
                    fCallback()
                end
                self.Events.evtLabelNo1ConfigUpdated:Invoke()
            end
        end
        local req = pb.CSMallGetLabelNo1ConfigReq:New()
        req:Request(fOnGetTraderPageLabelConfigRes)
end

--- 根据页签id拉取商品列表
-- function ShopServer:_FetchTraderPageGoods(mainId, fCallback)
--     if not mainId then return end
--     -- local tbMall = self.Tables.MallLocalTable
--     -- if not tbMall then
--     --     self.Tables.MallLocalTable = Facade.TableManager:GetTable("Mall")
--     --     tbMall = self.Tables.MallLocalTable
--     -- end
--     local fOnGetTraderPageGoodsRes = function(res)
--         if res.result == 0 then
--             if res.trader_id then
--                 -- 清理缓存mallShopItemList中label_no1的商品
--                 local tempShopItemList = {}
--                 for _, shopItem in pairs(self.mallShopItemList) do
--                     if shopItem.labelNo1 ~= res.label_no1 then
--                         table.insert(tempShopItemList, shopItem)
--                     else
--                         self._mapExchangeID2SItem[shopItem.exchangeId] = nil --清理_mapExchangeID2SItem, _mapExchangeId2SItemDetail依赖这个，但在进局内时才会Generate
--                         self._mapItemId2BuyPrice[shopItem:GetItemID()] = nil --清理旧商品价格列表
--                     end
--                 end
--                 --生成新页签商品列表, 根据page来的，我只传了一个label
--                 if res.page_list and #res.page_list > 0 and res.page_list[1].buy_props then
--                     for _, prop in pairs(res.page_list[1].buy_props) do
--                         local newSItem = self:GenShopItemByProp(prop)
--                         if newSItem then
--                             table.insert(tempShopItemList, newSItem)
--                             self._mapExchangeID2SItem[prop.exchange_id] = newSItem
--                         end
--                     end
--                 end
--                 self.mallShopItemList = tempShopItemList
--                 self._closetMainIdTimeStemp = self:GetClosetSItemUpdateTimeStemp()
--             end
--             if fCallback then
--                 fCallback()
--             end
--             self.Events.evtLabelNo1SItemListUpdated:Invoke(mainId)
--             self:_FetchGoodsLimit()
--         end
--     end
--     local req = pb.CSMallGetTraderPageGoodsReq:New()
--     -- req.trader_id = math.floor(tonumber(mainId) / 10000)
--     req.trader_id = math.floor(tonumber(mainId) / 10000)
--     req.page_list = {{
--         label_no1 = mainId,
--     }}
--     req:Request(fOnGetTraderPageGoodsRes, {bEnableHighFrequency = true})

-- end

---@param mainId number LabelNo1页签id
---@return boolean 是否有倒计时
---@return number 刷新时间戳（start or end）
function ShopServer:CheckIsFloatingLabelNo1(labelNo1)
    if labelNo1 and self._mapMainId2DividedTimeConfig then
        local labelConfig = self._mapMainId2DividedTimeConfig[labelNo1]
        if labelConfig.bIsLimitTime == 1 then
            return true, labelConfig.startTime, labelConfig.endTime
        end
    end
    return false, 0, 0
end

function ShopServer:_InitActiveAndRefreshLabelNo1List()
    local activeLabelNo1List = {}
    local refreshLabelNo1List = {}
    if self._mapMainId2DividedTimeConfig then
        for mainId, labelConfig in pairs(self._mapMainId2DividedTimeConfig) do
            if labelConfig.bIsLimitTime == 1 then
                local curTimeStamp = Facade.ClockManager:GetLocalTimestamp()
                if labelConfig.startTime <= curTimeStamp and curTimeStamp <= labelConfig.endTime then
                    table.insert(activeLabelNo1List, mainId)
                end
                table.insert(refreshLabelNo1List, mainId)
            else
                table.insert(activeLabelNo1List, mainId)
            end
        end
    end
    return activeLabelNo1List, refreshLabelNo1List
end

function ShopServer:GetCurAvailableLabelNo1List()
    local activeLabelNo1List = {}
    if self._mapMainId2DividedTimeConfig then
        for mainId, limitTimeInfo in pairs(self._mapMainId2DividedTimeConfig) do
            if ShopHelperTool.CheckLabelNo1Valid(mainId) then --本地TraderPageTable有对应配置
                if limitTimeInfo.bIsLimitTime == 0 then
                    table.insert(activeLabelNo1List, mainId)
                else
                    local curTimeStemp = Facade.ClockManager:GetLocalTimestamp()
                    if curTimeStemp >= limitTimeInfo.startTime and curTimeStemp <= limitTimeInfo.endTime then
                        table.insert(activeLabelNo1List, mainId)
                    end
                end
            end
        end
    end

    return activeLabelNo1List
end

function ShopServer:CheckIsLabelNo1Available(curLabelNo1)
    if not self.curAvailableLabelNo1List then
        self.curAvailableLabelNo1List = self:GetCurAvailableLabelNo1List()
    end
    if curLabelNo1 then
        for _, lableNo1 in pairs(self.curAvailableLabelNo1List) do
            if lableNo1 == curLabelNo1 then
                return true
            end
        end
    end
    return false
end

function ShopServer:FetchMerchantsWithGoods(fOnFetchGoodsCallback)
	local fOnFetchAllGoods = function()
        self:_PartFetchAllGoods(fOnFetchGoodsCallback)
    end
	self:FreshMerchantsOnly(fOnFetchAllGoods)
    self:_FetchTraderPageLabelConfig() -- 页签配置
    self:_PartFetchDynamicGuidePirce() --动态指导价
end

--------------------------------------------------------------------------
--- 商品协议相关（打开界面对比cdn）
--------------------------------------------------------------------------
function ShopServer:TryCompareShopCdnVer(bOnlyGoods)
    if self.isFetchingBuyGoods then
        logerror('ShopServer:TryCompareShopCdnVer 正在拉取商品列表中')
        return
    end
    bOnlyGoods = setdefault(bOnlyGoods, true)
    --第一次拉取如果版本信息和服务器一致则无需拉取服务器数
    local fOnPartResCallback = function(res, reqWrapperIns)
        if res.version_info ~= nil then
            -- if res.version_info.ver == self._shopCdnVersion then
            if res.version_info.load_time == self._shopCdnVersion then
                reqWrapperIns:Reset()
                logwarning("[ShopServer Cdn Version] TryCompareShopCdnVer fOnPartResCallback version_info has no change:",self._shopCdnVersion)
            else
                logerror("[ShopServer Cdn Version] TryCompareShopCdnVer fOnPartResCallback version_info changed:",self._shopCdnVersion," ====> ", res.version_info.load_time)
                reqWrapperIns:Reset()
                if bOnlyGoods then
                    self:_PartTwoFetchBuyGoods() --仅拉取商品列表
                else
                    self:FetchMerchantsWithGoods()
                end
            end
        end
    end

    local fOnSuccessCallback = function(time)
        logwarning("[ShopServer Cdn Version] TryCompareShopCdnVer fOnSuccessCallback total times:", time)
    end

    local fOnFailureCallback = function(time)
        logerror("[ShopServer Cdn Version] TryCompareShopCdnVer fOnFailureCallback get times:", time)
    end

    local req = pb.CSMallGetBuyGoodsReq:New()
    local partParam = {
        start_index = 0,
        get_num = 0,
        time = 0,
        reqIns = req,
        fOnSuccessCallback = fOnSuccessCallback,
        fOnFailureCallback = fOnFailureCallback,
    }
    req:PartRequest(fOnPartResCallback, {maxWaitTime = 10}, nil, partParam)
end
--------------------------------------------------------------------------
--- 商品协议相关（新分段拉取）
--------------------------------------------------------------------------
function ShopServer:_PartFetchAllGoods(fOnFetchGoodsCallback)
    self:_PartOneFetchRecycleGoods()
    self:_PartTwoFetchBuyGoods(fOnFetchGoodsCallback)
end

function ShopServer:_PartOneFetchRecycleGoods()
    if self.reqInsRecycleRunning then
        self.reqInsRecycleRunning:Reset()
        self.reqInsRecycleRunning = nil
    end
    --- 价格列表
     -- ---@type table<number, table>
    -- self._mapItemId2RecyclePrice = {}
    ---@type table<itemId, boolean>
    self.cannotRecycleList = {}

    -- self._cacheIdDur2DynamicGuidePrice = {}
    -- self._cacheIdDur2MallRecyclePrice = {}

    local fOnGetRecycleGoodsRes = function(res, reqWrapperIns)
        if res.result == 0 then
            --- shop price (for recycle)
            if res.recyle_props then
                for _, prop in ipairs(res.recyle_props) do
                    local propItemId = prop.prop_info.id
                    if ItemConfigTool.GetItemConfigById(propItemId) then
                        if prop.exchange_type == EShopOptType.Purchase then
                            local priceInfo = prop.prices[1]
                            if priceInfo then
                                local currencyClientType = MapCurrencyId2ClientType[priceInfo.money_type]
                                -- self._mapItemId2RecyclePrice[propItemId] = setdefault(self._mapItemId2RecyclePrice[propItemId], RecyclePriceInfo:new())
                                self._mapItemId2PriceConfig[propItemId] = setdefault(self._mapItemId2PriceConfig[propItemId], PriceConfigInfoDefine:new())
                                self._mapItemId2PriceConfig[propItemId][EItemId2RecyclePriceID.currencyClientType] = currencyClientType
                                self._mapItemId2PriceConfig[propItemId][EItemId2RecyclePriceID.recyclePricePercent] = prop.recycle_price_percent
                                -- self._mapItemId2PriceConfig[propItemId][EItemId2RecyclePriceID.zeroNum] = prop.zero_dur_recycle_price --貌似废弃了
                                -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.num] = self._mapItemId2RecyclePrice[propItemId] --由动态指导价设置
                                -- self._mapItemId2RecyclePrice[propItemId][EItemId2RecyclePriceID.zeroNum] = prop.zero_dur_recycle_price
                                self._mapItemId2MallRecycle[propItemId] = prop.mall_recycle_price
                                -- self._mapItemId2RecyclePrice[propItemId][EItemId2RecyclePriceID.mallNum] = prop.mall_recycle_price
                                -- self._mapItemId2RecyclePrice[propItemId][EItemId2RecyclePriceID.mallZeroNum] = prop.zero_dur_mall_recycle_price
                                if self._cacheIdDur2MallRecyclePrice[propItemId] then
                                    self._cacheIdDur2MallRecyclePrice[propItemId] = nil
                                end
                                -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.currencyClientType] = currencyClientType
                                -- -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.num] = self._mapItemId2RecyclePrice[propItemId]
                                -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.zeroNum] = prop.zero_dur_recycle_price
                                -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.mallNum] = prop.mall_recycle_price
                                -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.mallZeroNum] = prop.zero_dur_mall_recycle_price
                                -- ItemId2RecyclePriceData[EItemId2RecyclePriceID.recyclePricePercent] = prop.recycle_price_percent
                                -- self._mapItemId2RecyclePrice[propItemId] = ItemId2RecyclePriceData
                                -- self._mapItemId2RecyclePrice[propItemId] = {
                                --     currencyClientType = currencyClientType,
                                --     num = priceInfo.price,                          --- 原生价：满耐久
                                --     zeroNum = prop.zero_dur_recycle_price,          --- 原生价：0耐久

                                --     mallNum = prop.mall_recycle_price,              --- 回收价：满耐久
                                --     mallZeroNum = prop.zero_dur_mall_recycle_price, --- 回收价：0耐久
                                -- }


								-- logwarning('_PartOneFetchRecycleGoods [分段拉取 - 回收]id 为'..propItemId..'的商品回收价格为：', priceInfo.price, priceInfo.money_type)

                                -- if self.cannotRecycleList[propItemId] ~= nil then
                                --     loginfo('[Sell Item Log]id 为'..propItemId..'的商品的不可回收配置被再次刷新为', prop.cannot_recycle)
                                -- -- else
                                -- --     loginfo('id 为'..propItemId..'的商品的不可回收配置被初次刷新为', prop.cannot_recycle)
                                -- end
                                if prop.cannot_recycle == true then
                                    table.insert(self.cannotRecycleList, propItemId)
                                end
                                -- self.cannotRecycleList[propItemId] = prop.cannot_recycle
                            else
                                if not VersionUtil.EnablePerformanceMode() then
                                    loginfo('[Sell Item Log]id 为'..propItemId..'的商品回收价格为空')
                                end
                            end
                        end
                    else
                        if not VersionUtil.EnablePerformanceMode() then
                            loginfo('[Sell Item Log]Item表不存在ID为'..propItemId..'的行')
                        end
                    end
                end
            else
                logerror('[Sell Item Log]ShopServer:_PartOneFetchRecycleGoods() res.recyle_props is nil')
            end
            if res.is_finish then
                if self.reqInsRecycleRunning then
                    self.reqInsRecycleRunning:Reset()
                    self.reqInsRecycleRunning = nil
                end
                self:PrintTableLength("self._mapItemId2PriceConfig",self._mapItemId2PriceConfig)
                self:PrintTableLength("self._mapItemId2MallRecycle",self._mapItemId2MallRecycle)
            end
        else
            if res.result ~= -1 then
                if self.reqInsRecycleRunning then
                    self.reqInsRecycleRunning:Reset()
                    self.reqInsRecycleRunning = nil
                end
                logerror('ShopServer:_PartOneFetchRecycleGoods 商品[回收]协议拉取失败',res.result)
            else
                logerror('ShopServer:_PartOneFetchRecycleGoods 商品[回收]协议拉取超时，保留reqIns 等待重连重拉')
            end
            -- logerror('_PartOneFetchRecycleGoods 商品[回收]协议拉取失败, 已经拉取次数:', reqWrapperIns.time)
        end
    end
    local fOnSuccessCallback = function(time)
        loginfo('[Sell Item Log] _PartOneFetchRecycleGoods [分段拉取 - 回收] 已完成, 总共拉取次数:', time)
    end
    local fOnFailureCallback = function(time)
        logerror('[Sell Item Log] _PartOneFetchRecycleGoods 商品[回收]协议拉取失败, 已经拉取次数:', time)
    end
    local req = pb.CSMallGetRecycleGoodsReq:New()
    local partParam = {
        start_index = 0,
        get_num = 500,
        time = 0,
        reqIns = req,
        fOnSuccessCallback = fOnSuccessCallback,
        fOnFailureCallback = fOnFailureCallback,
    }
    self.reqInsRecycleRunning = req:PartRequest(fOnGetRecycleGoodsRes, {bNeedResendAfterReconnected = true, maxWaitTime = 10}, nil, partParam)
end

function ShopServer:_PartTwoFetchBuyGoods(fOnFetchGoodsCallback)
    if self.reqInsBuyRunning then
        self.reqInsBuyRunning:Reset()
        self.reqInsBuyRunning = nil
    end
    --- 商品列表
    ---@type table<itemId, boolean>
    local tempMapItemId2IsOnSell = {}
    -- self._mapItemId2IsOnSell = {}
    ---@type table<number, ShopItemStruct> array
    local tempMallShopItemList = {}
    -- self.mallShopItemList = {}
    ---@type table<number, table>
    local tempMapItemId2BuyPrice = {}
    ---@type table<number, SItem>
    local tempRefreshShopItemList = {} --即将上下架的商品列表
    ---@type table<number, table>
    local tempMapExchangeId2SItem = {}
    local tempMapId2ExchangeIdList = {} --方便根据id快速查找

    local fOnGetBuyGoodsRes = function(res)
        if res.result == 0 then
            self.isFetchingBuyGoods = true
            --- shop props (for buy)
            for _, prop in ipairs(res.buy_props) do
                local newShopItem = self:GenShopItemByProp(prop)
                if newShopItem then
                    local purchasePriceConfig, isOnSell = self:GenItemPurchasePriceByProp(prop)
                    -- mallSItemList, exchangeId2SItem
                    if newShopItem:CheckIsOnShelve() then
                        table.insert(tempMallShopItemList, newShopItem)
                        tempMapExchangeId2SItem[prop.exchange_id] = newShopItem --原有的map当前只收录上架的商品
                        -- self._mapExchangeID2SItem[prop.exchange_id] = newShopItem --原有的map当前只收录上架的商品
                        declare_if_nil(tempMapId2ExchangeIdList, prop.prop_info.id, {})
                        if table.keyof(tempMapId2ExchangeIdList[prop.prop_info.id], prop.exchange_id) == 0 then
                            table.insert(tempMapId2ExchangeIdList[prop.prop_info.id], prop.exchange_id)
                        end
                        if prop.preset_id ~= 0 then
                            declare_if_nil(tempMapId2ExchangeIdList, prop.preset_id, {})
                            if table.keyof(tempMapId2ExchangeIdList[prop.preset_id], prop.exchange_id) == 0 then
                                table.insert(tempMapId2ExchangeIdList[prop.preset_id], prop.exchange_id)
                            end
                        end
                    end
                    if newShopItem.isLimitTime == 1 then
                        table.insert(tempRefreshShopItemList, newShopItem)
                    end
                    -- 处理buyPrice（包含需要刷新的商品）
                    if newShopItem:GetExchangeType() == EShopOptType.Purchase then
                        --table<itemid, priceConfig>
                        if next(purchasePriceConfig) then
                            tempMapItemId2BuyPrice[newShopItem:GetItemID()] = purchasePriceConfig
                            if isOnSell then
                                tempMapItemId2IsOnSell[newShopItem:GetItemID()] = isOnSell
                            end
                        end
                    end
                else
                    dump(prop)
                    logerror('ShopServer:_PartTwoFetchBuyGoods 生成ShopItem失败exchangeId=', prop.exchange_id)
                end
            end
            if res.is_finish then
                --设置缓存的商品列表
                self.mallShopItemList = tempMallShopItemList
                self._mapItemId2BuyPrice = tempMapItemId2BuyPrice
                self._mapItemId2IsOnSell = tempMapItemId2IsOnSell
                self._mallRefreshShopItemList = tempRefreshShopItemList
                self._mapExchangeID2SItem = tempMapExchangeId2SItem
                self._mapID2ExchangeIdList = tempMapId2ExchangeIdList
                self:PrintTableLength("self.mallShopItemList",self.mallShopItemList)
                if res.version_info ~= nil then
                    self._shopCdnVersion = res.version_info.load_time
                    logwarning("[ShopServer Cdn Version] 当前商店 cdn更新为 version_info.load_time:",self._shopCdnVersion)
                end
                self:_FetchGoodsLimit(fOnFetchGoodsCallback)
                self.isFetchingBuyGoods = false
                self:_FetchUnlockExchangeIdList()
                if self.reqInsBuyRunning then
                    self.reqInsBuyRunning:Reset()
                    self.reqInsBuyRunning = nil
                end
                logwarning('[分段拉取 - 购买] 已完成 总商品数：',#self.mallShopItemList)
            end
        else
            if res.result ~= -1 then
                self.isFetchingBuyGoods = false
                if self.reqInsBuyRunning then
                    self.reqInsBuyRunning:Reset()
                    self.reqInsBuyRunning = nil
                end
                logerror('商品[售卖]协议拉取失败',res.result)
            else
                logerror('ShopServer:_PartTwoFetchBuyGoods 商品[售卖]协议拉取超时，保留reqIns 等待重连重拉')
            end
        end
    end
    local req = pb.CSMallGetBuyGoodsReq:New()
    self.reqInsBuyRunning =req:PartRequest(fOnGetBuyGoodsRes, {bNeedResendAfterReconnected = true, maxWaitTime = 10})
end

function ShopServer:GenShopItemByProp(prop)
    local newShopItem
    if prop then
        local tbMall = self.Tables.MallLocalTable
        if not tbMall then
            self.Tables.MallLocalTable = Facade.TableManager:GetTable("Mall")
            tbMall = self.Tables.MallLocalTable
        end
        if prop.for_sale then
            local propItemId = prop.prop_info.id
            if ItemConfigTool.GetItemConfigById(propItemId) then
                local localInfo = tbMall[tostring(prop.exchange_id)]
                local propExchangeId = prop.exchange_id
                newShopItem = ShopItemStruct:NewIns(prop, localInfo)
            else
                if not VersionUtil.EnablePerformanceMode() then
                    loginfo('Item表不存在ID为'..propItemId..'的行')
                end
            end
        else
            --- 未上架商品目前无处理
        end
    else
        logerror('ShopServer:GenShopItemByProp prop is nil ')
    end
    return newShopItem
end

--- 生成直购Purchase商品购买价格配置
---@param prop table
---@return table priceConfig
---@return boolean isOnSell
function ShopServer:GenItemPurchasePriceByProp(prop)
    local priceConfig = {}
    local isOnSell = false
    local propItemId = prop.prop_info.id
    if prop and prop.for_sale and ItemConfigTool.GetItemConfigById(propItemId) then
        if prop.exchange_type == EShopOptType.Purchase then
            local propExchangeId = prop.exchange_id
            if prop.prices[1] and prop.prices[1].price then
                local money_type = prop.prices[1].money_type
                local price = prop.prices[1].price
                local currencyClientType = MapCurrencyId2ClientType[money_type]
                --通过itemid找currencyid如果有绑定&非绑则转换 
                local currencyId = Server.CurrencyServer:ConvertCurrencyIdByItemId(money_type)
                currencyId = Server.CurrencyServer:ConvertCurrencyId(currencyId, ECurrencyBindType.BindAndUnbind)
                priceConfig = {
                    currencyClientType = currencyClientType,
                    currencyId = currencyId,
                    num = price,
                }
                if price > 0 then
                    isOnSell = true
                end
            else
                local tbMall = self.Tables.MallLocalTable
                if not tbMall then
                    self.Tables.MallLocalTable = Facade.TableManager:GetTable("Mall")
                    tbMall = self.Tables.MallLocalTable
                end
                priceConfig = {
                    currencyClientType = ECurrencyClientType.OnlyUnBind,
                    currencyId = ECurrencyClientId.Tina,
                    num = tbMall[tostring(propExchangeId)] and tbMall[tostring(propExchangeId)]['CostItem1Num'] or 0,
                }
            end
        end
    else
        logerror('ShopServer:GetShpItemPrice prop is nil ')
    end
    return priceConfig, isOnSell
end

--------------------------------------------------------------------------
-- 动态指导价（string拉取）
--------------------------------------------------------------------------

---@param bForceFetch 强制拉取（会打断现有拉取）
function ShopServer:FetchDynamicGuidePirce()
    --距离上一次拉新时间距离5s则强制失败
    if self._lastDynamicPriceFetchedTimestamp then
        local delta = Facade.ClockManager:GetLocalTimestamp() - self._lastDynamicPriceFetchedTimestamp
        if delta < DEFAULT_CHECK_THRESHOld then
            return
        end
    end
    self:_PartFetchDynamicGuidePirce()
    self._guidPriceCD = DEFAULT_FETCH_THRESHOld
end

function ShopServer:_PartFetchDynamicGuidePirce()
    loginfo('ShopServer:FetchDynamicGuidePirce')
    if self.reqInsAutoPriceRunning then
        self.reqInsAutoPriceRunning:Reset()
        self.reqInsAutoPriceRunning = nil
    end
    local bIsUpdated = false
    local fOnGetAutoPriceRes = function(res)
        if res.result == 0 then
            if res.price_list then
                for _, prop in pairs(res.price_list) do
                    self._mapItemId2DynamicGuidePrice = setdefault(self._mapItemId2DynamicGuidePrice, {})
                    if self._mapItemId2DynamicGuidePrice[prop.prop_id] and self._mapItemId2DynamicGuidePrice[prop.prop_id] ~= prop.price and not bIsUpdated then
                        bIsUpdated = true
                    end
                    self._mapItemId2DynamicGuidePrice[prop.prop_id] = prop.price
                    if self._cacheIdDur2DynamicGuidePrice[prop.prop_id] then
                        self._cacheIdDur2DynamicGuidePrice[prop.prop_id] = nil
                    end
                end
            end
            if res.finish then
                self._lastDynamicPriceFetchedTimestamp = Facade.ClockManager:GetLocalTimestamp()
                self.Events.evtDynamicGuidPriceFinishFetch:Invoke(bIsUpdated)
                if self.reqInsAutoPriceRunning then
                    self.reqInsAutoPriceRunning:Reset()
                    self.reqInsAutoPriceRunning = nil
                end
                self:PrintTableLength("self._mapItemId2DynamicGuidePrice",self._mapItemId2DynamicGuidePrice)
            end
            if res.new_sync_digest then
                self.sync_digest = res.new_sync_digest
            end
        else
            if res.result ~= -1 then
                self._lastDynamicPriceFetchedTimestamp = Facade.ClockManager:GetLocalTimestamp()
                self.Events.evtDynamicGuidPriceFinishFetch:Invoke(bIsUpdated)
                if self.reqInsAutoPriceRunning then
                    self.reqInsAutoPriceRunning:Reset()
                    self.reqInsAutoPriceRunning = nil
                end
                logerror('ShopServer:_PartFetchDynamicGuidePirce 动态指导价协议拉取失败',res.result)
            else
                logerror('ShopServer:_PartFetchDynamicGuidePirce 动态指导价协议拉取超时，保留reqIns 等待重连重拉')
            end
        end
    end

    local fOnSuccessCallback = function(res)
        loginfo('[Sell Item Log] _PartFetchDynamicGuidePirce [分段拉取 - 回收] 已完成')
    end

    local fOnFailureCallback = function(res)
        logerror('[Sell Item Log] _PartFetchDynamicGuidePirce 商品[回收]协议拉取失败, 最终digest', res.new_sync_digest)
    end
    
    local req = pb.CSAuctionAutoLoadGuidePriceReq:New()
    local partParam = {
        reqIns = req,
        start_digest = self.sync_digest or "",
        fOnPartResCallback = fOnGetAutoPriceRes,
        fOnSuccessCallback = fOnSuccessCallback,
        fOnFailureCallback = fOnFailureCallback
    }
    local reqPartDigestWrapper = ReqPartDigestWrapper:NewIns(req, partParam)
    -- req:Request(fOnGetAutoPriceRes)
    self.reqInsAutoPriceRunning = req:PartRequest(fOnGetAutoPriceRes, {bNeedResendAfterReconnected = true, maxWaitTime = 10}, reqPartDigestWrapper, partParam) --断线重连重发
end

function ShopServer:OnPrestigeLvUpgraded(mapPid2LvChange)
    if not self.isFetchingBuyGoods and next(self._mapExchangeID2SItem)  then
        self:_FetchUnlockExchangeIdList()
    end
end

function ShopServer:OnSeasonLevelUpgraded(seasonLevel, preSeasonLevel)
    if not self.isFetchingBuyGoods then
        self:_FetchUnlockExchangeIdList()
    end
end

function ShopServer:OnUpdateQuestState(id)
    if Server.QuestServer:GetQuestInfoById(id) and Server.QuestServer:GetQuestInfoById(id).state == QuestState.Rewarded and not self.isFetchingBuyGoods then
        self:_FetchUnlockExchangeIdList()
    end
end

function ShopServer:OnJoinTeam()
    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow == EGameFlowStageType.SafeHouse then
        self:FetchDynamicGuidePirce()
    end
end

function ShopServer:_FreshShopItemsByLimit(limit_items)
    local deltaExchangeIdList = {}
    local deltaStr = ""
    local illExchangeIdList = {}
    self._mapServerTimp2LimitSItemList = {}
    for k,v in pairs(limit_items) do
        local shopItem = self._mapExchangeID2SItem[v.exchange_id]
        local alreadyBuyNum = v.buy_num
        local alreadyRecycleNum = v.recycle_num_daily
        -- local localInfo = self.Tables.MallLocalTable[tostring(v.exchange_id)]
        if shopItem then
            local prop = shopItem:GetRawProp()
            if shopItem:GetRawItemNum() ~= - 1 then
                shopItem:InitLimitInfo(v)
                -- shopItem:InitShopItemStruct(prop, localInfo)
                shopItem:InitAmountInfo(prop)

                local exchangeId = shopItem.exchangeId
                local curLimitNum = prop.prop_info.num
                local cacheNum = self._cacheMapLimitValues[exchangeId]
                if cacheNum then
                    if cacheNum ~= curLimitNum then
                        table.insert(deltaExchangeIdList, exchangeId)
                        if curLimitNum and exchangeId then
                            deltaStr = deltaStr.."exchangeId:"..exchangeId.." curLimitNum:"..curLimitNum
                        end
                    end
                end
                self._cacheMapLimitValues[exchangeId] = curLimitNum
            end
            --MS23：兼顾局内的shopItemCount
            -- if self._mapExchangeId2SItemDetail then
            --     for _, SItemDetail in pairs(self._mapExchangeId2SItemDetail) do
            --         if SItemDetail.exchangeId == v.exchange_id then
            --             SItemDetail:UpdateItemCount(shopItem)
            --         end
            --     end
            -- end
            self._mapMid2LimitShopItem[shopItem.merchantId] = setdefault(self._mapMid2LimitShopItem[shopItem.merchantId], {})
            local key = table.keyof(self._mapMid2LimitShopItem[shopItem.merchantId], shopItem)
            if key == 0 then
                table.insert(self._mapMid2LimitShopItem[shopItem.merchantId], shopItem)
            else
                self._mapMid2LimitShopItem[shopItem.merchantId][key] = shopItem
            end
            --限购商品刷新时间戳
            self._mapServerTimp2LimitSItemList = setdefault(self._mapServerTimp2LimitSItemList, {})
            if shopItem and shopItem.limitMallInfo and shopItem.limitMallInfo.next_period_start and shopItem.limitMallInfo.next_period_start ~= 0 then
                self._mapServerTimp2LimitSItemList[shopItem.limitMallInfo.next_period_start] = setdefault(self._mapServerTimp2LimitSItemList[shopItem.limitMallInfo.next_period_start], {})
                table.insert(self._mapServerTimp2LimitSItemList[shopItem.limitMallInfo.next_period_start], shopItem)
            end
        else
            table.insert(illExchangeIdList, v.exchange_id)
        end
    end
    loginfo('限量商品数量更新！', deltaStr)
end

function ShopServer:GetLimitShopItemListByMid(mid, limitType)
    local limitTimes = {}
    if mid and self._mapMid2LimitShopItem and self._mapMid2LimitShopItem[mid] then
        if not limitType then
            return self._mapMid2LimitShopItem[mid]
        else
            for _, SItem in pairs(self._mapMid2LimitShopItem[mid]) do
                if SItem:TryGetLimitType() == limitType then
                    table.insert(limitTimes, SItem)
                end
            end
        end
    end
    return limitTimes
end

function ShopServer:FreshLimitGoods(fOnFreshLimitGoodsCallback)
    self:_FetchGoodsLimit(fOnFreshLimitGoodsCallback)
end

function ShopServer:_FetchGoodsLimit(fOnFetchGoodsLimitCallback)
    local fOnGetAllLimitRes = function(res)
        if res.result == 0 then
            self:_FreshShopItemsByLimit(res.limit_items)
            -- self.limitItems = res.limit_items
            self.Events.evtShopItemReGenerated:Invoke()
            if fOnFetchGoodsLimitCallback then
                fOnFetchGoodsLimitCallback(res.limit_items)
                fOnFetchGoodsLimitCallback = nil
            end
        else
            logerror('商品Limit数量协议拉取失败')
        end
    end
    local req = pb.CSMallGetPlayerDailyLimitGoodsReq:New()
    req:Request(fOnGetAllLimitRes, {bEnableHighFrequency = true})
end

function ShopServer:FetchMerchantNewGiftById(merchant_id)
    -- local req = pb.CSMallGetMerchantGiftReq:New()
    -- req.merchant_id = merchant_id
    -- local OnGetMerchantGiftRes = function(res)
    --     if res.result == 0 then
    --         loginfo('商人:',merchant_id,'拉取新礼物')
    --         if res.merchant.id ~= 0 then
    --             local merchantStruct = self:GetMerchantStructById(merchant_id)
    --             merchantStruct:SetMerchantData(res.merchant)
    --             self.Events.evtMerchantGiftFresh:Invoke(res)
    --             dump(res.new_gifts)
    --         end
    --     end
    -- end
    -- req:Request(OnGetMerchantGiftRes)
end

function ShopServer:GetSlotListByMerchantId(merchant_id)
    return self._mapMerchantId2Slots[merchant_id]
end

function ShopServer:GetAllGiftListByMerchantId(merchant_id)
    local merchantData = self:GetMerchantDataById(merchant_id)
    if merchantData then
        return merchantData.gifts or {}
    end
    return {}
end

function ShopServer:GetMerchantIdBySlotId(slot_id)
    for mid,slotList in pairs(self._mapMerchantId2Slots) do
        for _,slotId in pairs(slotList) do
            if slotId == tostring(slot_id) then
                return mid
            end
        end
    end
end

--------------------------------------------------------------------------
--- 商品拉取相关 (按照页签) V2
--------------------------------------------------------------------------
function ShopServer:FreshPageShopItems(mainPageId, subPageId)
    if subPageId == 0 then
        subPageId = nil
    end
    if self.mallShopItemList and #self.mallShopItemList > 0 then
        self:_DoFreshMixPageShopItems(mainPageId, subPageId)
    else
        local fOnFetchGoodsCallback = function()
            self:_DoFreshMixPageShopItems(mainPageId, subPageId)
        end
        self:FetchMerchantsWithGoods(fOnFetchGoodsCallback)
    end
end

function ShopServer:_CheckIsMatchPage(shopItem, mainPageId, subPageId)
    local bMatchMain = true
    local bMatchSub = true
    if mainPageId then
        bMatchMain = shopItem.labelNo1 ==  mainPageId 
    end
    if subPageId then
        bMatchSub = shopItem.labelNo2 ==  subPageId 
    end
    return bMatchMain and bMatchSub
end

function ShopServer:_DoFreshMixPageShopItems(mainPageId, subPageId)
    self.curMixShopItems = {}
    --- Normal
    for _,shopItem in ipairs(self.mallShopItemList) do
        -- loginfo('_DoFreshMixPageShopItems 判断条件', shopItem.item.name, 'exchange_type:', shopItem:GetExchangeType(), '需要的兑换类型：', currentOptType,'是否匹配页签', self:_CheckIsMatchPage(shopItem, mainPageId, subPageId), shopItem.item.id, shopItem.labelNo1, '需要的页签', mainPageId, subPageId)
        if self:CheckExchangeTypeEnable(shopItem:GetExchangeType())
        and self:_CheckIsMatchPage(shopItem, mainPageId, subPageId) then
            -- loginfo('_DoFreshMixPageShopItems 商品名称', shopItem.item.name, 'exchange_type:', currentOptType)
            table.insert(self.curMixShopItems, shopItem)
        end
    end
    loginfo('_DoFreshMixPageShopItems self.curMixShopItems 商品数量',#self.curMixShopItems)
    self.Events.evtMixShopItemsFresh:Invoke(self.curMixShopItems)

    --- MS22 deprecated
    --- Panic
    -- local label_ids = {}
    -- if mainPageId then
    --     table.insert(label_ids, mainPageId)
    -- end
    -- if subPageId then
    --     table.insert(label_ids, subPageId)
    -- end
    -- dump(label_ids)
    -- self:_FreshPanicGoodsByPageId(label_ids, self.curMixShopItems)
end

function ShopServer:_FreshPanicGoodsByPageId(label_ids, curMixShopItems)
    -- fresh data diff with Normal!!!
    -- local fOnGetSubGoodsRes = function(res)
    --     if res.result == 0 then
    --         for _,gridPage in pairs(res.mall_grid_pages) do
    --             local sub_slot_id = gridPage.slot_sub_id
    --             for _,prop in ipairs(gridPage.props) do
    --                 local propItemId = prop.prop_info.id
    --                 if ItemConfigTool.GetItemConfigById(propItemId) then
    --                     local propExchangeId = prop.exchange_id
    --                     if self:CheckExchangeTypeEnable(prop.exchange_type) then
    --                         local localInfo = self.Tables.MallLocalTable[tostring(prop.exchange_id)]
    --                         local shopItem
    --                         if self._mapExchangeID2SItem[propExchangeId] == nil then
    --                             shopItem = ShopItemStruct:NewIns(prop, localInfo)
    --                             self._mapExchangeID2SItem[propExchangeId] = shopItem
    --                         else
    --                             shopItem = self._mapExchangeID2SItem[propExchangeId]
    --                             shopItem:InitShopItemStruct(prop, localInfo)
    --                         end
    --                         table.insert(curMixShopItems, shopItem)
    --                     end
    --                 else
    --                     if not VersionUtil.EnablePerformanceMode() then
    --                         logwarning('Item表不存在ID为'..propItemId..'的行')
    --                     end
    --                 end
    --             end
    --         end
    --         if self.limitItems then
    --             self:_FreshShopItemsByLimit(self.limitItems)
    --         end
    --         self.Events.evtMixShopItemsFresh:Invoke(curMixShopItems)
    --     else
    --     end
    -- end
    -- local req = pb.CSMallGetSubGoodsReq:New()
    -- req.label_ids = label_ids
    -- req:Request(fOnGetSubGoodsRes)
end

function ShopServer:FreshFliterPageShopItems(allPageIdList, fShopItemFilterFunc)
    ---Normal
    self.curMixShopItems = {}
    for _,shopItem in ipairs(self.mallShopItemList) do
        if self:CheckExchangeTypeEnable(shopItem:GetExchangeType()) then
            if fShopItemFilterFunc then
                if fShopItemFilterFunc(shopItem) then
                    table.insert(self.curMixShopItems, shopItem)
                end
            else
                table.insert(self.curMixShopItems, shopItem)
            end
        end
    end
    loginfo('FreshFliterPageShopItems self.curMixShopItems 商品数量',#self.curMixShopItems)
    self.Events.evtMixShopItemsFresh:Invoke(self.curMixShopItems)

    --- MS22 deprecated
    ---Panic
    -- self:_FreshPanicGoodsByPageFliterFunc(allPageIdList, fShopItemFilterFunc, self.curMixShopItems)
end

function ShopServer:_FreshPanicGoodsByPageFliterFunc(allPageIdList, fShopItemFilterFunc, curMixShopItems)
    -- fresh data diff with Normal!!!
    -- local fOnGetSubGoodsRes = function(res)
    --     if res.result == 0 then
    --         for _,gridPage in pairs(res.mall_grid_pages) do
    --             local sub_slot_id = gridPage.slot_sub_id
    --             for _,prop in ipairs(gridPage.props) do
    --                 local propItemId = prop.prop_info.id
    --                 if ItemConfigTool.GetItemConfigById(propItemId) then
    --                     local propExchangeId = prop.exchange_id
    --                     if self:CheckExchangeTypeEnable(prop.exchange_type) then
    --                         local localInfo = self.Tables.MallLocalTable[tostring(prop.exchange_id)]
    --                         local shopItem
    --                         if self._mapExchangeID2SItem[propExchangeId] == nil then
    --                             shopItem = ShopItemStruct:NewIns(prop, localInfo)
    --                             self._mapExchangeID2SItem[propExchangeId] = shopItem
    --                         else
    --                             shopItem = self._mapExchangeID2SItem[propExchangeId]
    --                             shopItem:InitShopItemStruct(prop, localInfo)
    --                         end
    --                         if fShopItemFilterFunc then
    --                             if fShopItemFilterFunc(shopItem) then
    --                                 table.insert(curMixShopItems, shopItem)
    --                             end
    --                         else
    --                             table.insert(curMixShopItems, shopItem)
    --                         end
    --                     end
    --                 else
    --                     if not VersionUtil.EnablePerformanceMode() then
    --                         logwarning('Item表不存在ID为'..propItemId..'的行')
    --                     end
    --                 end
    --             end
    --         end
    --         self.Events.evtMixShopItemsFresh:Invoke(curMixShopItems)
    --     else
    --     end
    -- end
    -- local req = pb.CSMallGetSubGoodsReq:New()
    -- req.label_ids = allPageIdList
    -- req:Request(fOnGetSubGoodsRes)
end

function ShopServer:GetCurMixShopItems()
    return self.curMixShopItems
end

--------------------------------------------------------------------------
--- 商人商品解锁已读状态相关
--------------------------------------------------------------------------
function ShopServer:FetchUnlockNewExchangedIdList()

end

-- 所有已解锁商品
function ShopServer:_FetchUnlockExchangeIdList()
    local fOnFetchUnlockExchangeIdList = function(res)
        if res.result == 0 then
            local curUnlockExchangeIdList = {} --所有已解锁，且存在于当前商品列表中的exchangeIdList
            for idx, exchangeId in pairs(res.unlock_exchange_ids) do
                local shopItem = self:GetShopItemByExchangeId(exchangeId)
                if shopItem then
                    table.insert(curUnlockExchangeIdList, exchangeId)
                end
            end
            local fCallback = function(res)
                self._newUnlockExchangeIdList = {}
                local clickedList = res.clicked_exchange_ids
                if clickedList then
                    for _, unlockedEId in ipairs(curUnlockExchangeIdList) do
                        if table.keyof(clickedList, unlockedEId) == 0 then --没点过红点，则新解锁
                            table.insert(self._newUnlockExchangeIdList, unlockedEId)
                        end
                    end
                end
                self.Events.evtUnlockNewExchangeIdListChanged:Invoke(self._newUnlockExchangeIdList)
            end
            self:_FetchClickedExchangeIdList(fCallback)

            -- self._unlockedExchangeIdList = res.unlock_exchange_ids
            -- self._mapExchangedId2bUnlockNew = {}

            -- for idx, exchangeId in ipairs(res.unlock_exchange_ids) do
            --     local shopItem = self:GetShopItemByExchangeId(exchangeId)
            --     if shopItem then
            --         self._mapExchangedId2bUnlockNew[exchangeId] = true
            --     elseif next(self._mapExchangeID2SItem) then
            --         logerror('该解锁商品不存在于商品列表中', exchangeId)
            --     end
            -- end
            -- self.Events.evtUnlockNewExchangeIdListChanged:Invoke(newUnlockExchangeIdList)
        else
        end
    end
    local req = pb.CSMallGetUnlockExchangeIdReq:New()
    req:Request(fOnFetchUnlockExchangeIdList)
end

--所有点了红点的商品
function ShopServer:_FetchClickedExchangeIdList(fCallback)
    local fOnFetchClickedExchangeIdList = function(res)
        if res.result == 0 then
            -- self._clickedExchangeIdList = {}
            -- for idx, clickedId in pairs(res.clicked_exchange_ids) do
            --     local shopItem = self:GetShopItemByExchangeId(clickedId)
            --     if shopItem then
            --         table.insert(self._clickedExchangeIdList, clickedId)
            --     end
            -- end
            if fCallback then fCallback(res) end
        end
    end
    local req = pb.CSMallGetClickedExchangeIdReq:New()
    req:Request(fOnFetchClickedExchangeIdList)
end

function ShopServer:GetAllUnlockExchangeIdList()
    if self._newUnlockExchangeIdList then
        return self._newUnlockExchangeIdList
    end
    -- if  self._unlockedExchangeIdList then
    --     return self._unlockedExchangeIdList
    -- end
    return {}
end


function ShopServer:DebugClearAllUnlockExchangeIdList()
    if self._newUnlockExchangeIdList then
        self._newUnlockExchangeIdList = {}
    end
    -- if  self._unlockedExchangeIdList then
    --     self._unlockedExchangeIdList = {}
    -- end
    return {}
end


function ShopServer:GetIsUnlockNewByMid(mid)
    if self._newUnlockExchangeIdList ~= nil then
        for idx, exchangeId in ipairs(self._newUnlockExchangeIdList) do
            local shopItem = self:GetShopItemByExchangeId(exchangeId)
            if not shopItem then
                logerror('ShopServer:GetIsUnlockNewByMid 该解锁商品不存在于商品列表中', exchangeId)
            else
                if not self:CheckIsLabelNo1Available(shopItem.labelNo1) then
                    logerror('ShopServer:GetIsUnlockNewByMid 该解锁商品页签未上架', exchangeId)
                end
                if shopItem and self:CheckIsLabelNo1Available(shopItem.labelNo1) then
                    if shopItem.merchantId == mid then
                        return true
                    end
                end
            end
        end
    end
    return false
end

function ShopServer:GetIsUnlockNewByLabelNo1(labelNo1)
    if self._newUnlockExchangeIdList ~= nil then
        for idx, exchangeId in ipairs(self._newUnlockExchangeIdList) do
            local shopItem = self:GetShopItemByExchangeId(exchangeId)
            if not shopItem then
                logerror('ShopServer:GetIsUnlockNewByMid 该解锁商品不存在于商品列表中', exchangeId)
            else
                if not self:CheckIsLabelNo1Available(shopItem.labelNo1) then
                    logerror('ShopServer:GetIsUnlockNewByMid 该解锁商品页签未上架', exchangeId)
                end
                if shopItem and self:CheckIsLabelNo1Available(shopItem.labelNo1) then
                    if shopItem.labelNo1 == labelNo1 then
                        return true
                    end
                end
            end
        end
    end
    return false
end

function ShopServer:GetIsUnlockNewByLabelNo2(labelNo2)
    if self._newUnlockExchangeIdList ~= nil then
        for idx, exchangeId in ipairs(self._newUnlockExchangeIdList) do
            local shopItem = self:GetShopItemByExchangeId(exchangeId)
            if not shopItem then
                logerror('ShopServer:GetIsUnlockNewByMid 该解锁商品不存在于商品列表中', exchangeId)
            else
                if not self:CheckIsLabelNo1Available(shopItem.labelNo1) then
                    logerror('ShopServer:GetIsUnlockNewByMid 该解锁商品页签未上架', exchangeId)
                end
                if shopItem and self:CheckIsLabelNo1Available(shopItem.labelNo1) then
                    if shopItem.labelNo2 == labelNo2 then
                        return true
                    end
                end
            end
        end
    end
    return false
end


function ShopServer:GetIsUnlockNewByExchangeId(exchangeId)
    if self._newUnlockExchangeIdList and table.keyof(self._newUnlockExchangeIdList, exchangeId) ~= 0 then
        return true
    end
    -- if (self._mapExchangedId2bUnlockNew and self._mapExchangedId2bUnlockNew[exchangeId]) then
    --     return true
    -- end
    return false
end

function ShopServer:DelayToDelUnlockExchangeId(exchangeId)
    declare_if_nil(self, '_delayToDelExchangeIdList', {})
    if table.keyof(self._delayToDelExchangeIdList, exchangeId) == 0 then
        table.insert(self._delayToDelExchangeIdList, exchangeId)
    end
end

function ShopServer:ClearDelayToDelUnlockExchangeIdList()
    if self._delayToDelExchangeIdList and next(self._delayToDelExchangeIdList) then
        local fOnResCallback = CreateCallBack(function(self, res)
            self._delayToDelExchangeIdList = {}
        end, self)
        self:DoReqDelUnlockExchangeIdList(self._delayToDelExchangeIdList, fOnResCallback)
    end
end

function ShopServer:DoReqDelUnlockExchangeIdList(delExchangeIdList, fOnResCallback)
    local fOnFetchUnlockExchangeIdList = function(res)
        if res.result == 0 then
            for idx, exchangeId in ipairs(delExchangeIdList) do
                table.removebyvalue(self._newUnlockExchangeIdList, exchangeId)
                -- table.removebyvalue(self._unlockedExchangeIdList, exchangeId)
            end
            -- self._mapExchangedId2bUnlockNew = {}

            -- for idx, exchangeId in ipairs(self._unlockedExchangeIdList) do
            --     local shopItem = self:GetShopItemByExchangeId(exchangeId)
            --     if shopItem then
            --         self._mapExchangedId2bUnlockNew[exchangeId] = true
            --     else
            --         logerror('该解锁商品不存在于商品列表中', exchangeId)
            --     end
            -- end
            if fOnResCallback then
                fOnResCallback(res)
            end
            self.Events.evtUnlockNewExchangeIdListChanged:Invoke(self._unlockedExchangeIdList)
        else
        end
    end
    local req = pb.CSMallDelUnlockExchangeIdReq:New()
    req.unlock_exchange_ids = delExchangeIdList
    req:Request(fOnFetchUnlockExchangeIdList, {bEnableHighFrequency = true})
end

--------------------------------------------------------------------------
--- 商人赠礼相关
--------------------------------------------------------------------------
--- 请勿直接调用此方法，使用ShopModule:DoSendGift(targetMerchantId, item, curGiveNum)
function ShopServer:DoGiveSingleItemReq(targetMerchantId, item, curGiveNum)
    -- targetMerchantId = setdefault(targetMerchantId, 1)
    -- curGiveNum = setdefault(curGiveNum, 1)
    -- local prop = item:GetRawPropInfo()
    -- self._tempSendItem = item
    -- prop.num = curGiveNum
    -- local props = {}
    -- table.insert(props, prop)
    -- self:_DoSendGiftsToMerchant(targetMerchantId, props)
end

--- 请勿直接调用此方法，使用ShopModule:DoSendGift(targetMerchantId, item, curGiveNum)
function ShopServer:_DoSendGiftsToMerchant(merchant_id, props)
    -- local req = pb.CSMallGiftReq:New()
    -- req.merchant_id = merchant_id
    -- req.props = props
    -- local OnMallGiftRes = function(res)
    --     if res.result == 0 then
    --     end
    -- end
    -- req:Request(OnMallGiftRes)
end

--------------------------------------------------------------------------
--- 接受赠礼
--------------------------------------------------------------------------
function ShopServer:DoReceiveGiftsReq(targetMerchantId, fCallbackIns)
    -- targetMerchantId = setdefault(targetMerchantId, 1)
    -- local req = pb.CSMallReceiveGiftReq:New()
    -- local OnReceiveGiftRes = function(res)
    --     if fCallbackIns then
    --         fCallbackIns(res)
    --         fCallbackIns = nil
    --     end
    -- end
    -- local merchantData = self:GetMerchantDataById(targetMerchantId)
    -- req.gifts = {{merchant_id = targetMerchantId, gifts = merchantData.gifts}}
    -- req:Request(OnReceiveGiftRes)
end

--------------------------------------------------------------------------
--- 货币兑换相关 废弃
--------------------------------------------------------------------------
-- function ShopServer:DoBuyBankNoteByGold(num, fCallback)
--     local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(ECurrencyClientType.AimenGold)
-- 	if num <= currencyNum then
-- 		local req = pb.CSMallBuyReq:New()
-- 		req.mall_prop = {}
-- 		req.mall_prop.prop_info = {}
-- 		req.mall_prop.prop_info.id = ECurrencyItemId.UnBindBankNote
-- 		req.mall_prop.prop_info.num = num * CurrencyExchangeCnt
-- 		req.prices = {}
-- 		table.insert(req.prices, {money_type = ECurrencyItemId.AimenGold, price = num})

-- 		local function OnBuyBankNoteRes(res)
-- 			print('buy bank note : ', num * CurrencyExchangeCnt)
--             if fCallback then
--                 fCallback(res)
--                 fCallback = nil
--             else
--                 LuaGlobalEvents.evtServerShowTip:Invoke('兑换纸币成功！')
--             end
-- 		end
-- 		req:Request(OnBuyBankNoteRes)
-- 	else
-- 		print('gold isnt enough')
-- 	end
-- end

--------------------------------------------------------------------------
--- 价格获取（购买）
--------------------------------------------------------------------------
function ShopServer:GetMerchantDiscount(merchantId)
    -- local TraderTable = self.Tables.TraderLocalTable
    -- local merchantRow = TraderTable[tostring(merchantId)]
    -- local merchantData = self:GetMerchantDataById(merchantId)
    -- local discount = 1
    -- if merchantData then
    --     if merchantData.intimacy_lvl >= 0 then
    --         discount = merchantRow['Lv'..merchantData.intimacy_lvl..'FavDiscount']
    --     else
    --         discount = merchantRow['LvN'..math.abs(merchantData.intimacy_lvl)..'FavDiscount']
    --     end
    --     discount = MathUtil.GetTheSecondDecimal(discount)
    --     --print('merchantId 为'..merchantId..'的商人折扣为'..discount..'好感度等级为'..merchantData.intimacy_lvl)
    -- end
    -- return discount
    return 1
end

---only Buy
---获得道具[购买]本地表原价
function ShopServer:GetShopLocalBuyPrice(itemId)
    local shopItem = self:GetPurchaseShopItemById(itemId)
    local currencyClientType = self:GetClientCurrencyTypeById(itemId)
    if shopItem then
        local finalBuyPrice = shopItem:GetShopLocalBuyPrice()
        return finalBuyPrice, currencyClientType
    end
    return 0, currencyClientType
end

---only Buy
---获得道具[购买]服务器原价
function ShopServer:GetShopOriginBuyPrice(itemId)
    local buyPriceInfo = self._mapItemId2BuyPrice[itemId]
    if buyPriceInfo then
        return buyPriceInfo.num, buyPriceInfo.currencyClientType
    else
        logwarning('商品表中无法找到id为'..itemId..'的商品')
        return 0, ECurrencyClientType.OnlyUnBind
    end
    return 0, ECurrencyClientType.OnlyUnBind
end

-- must on sale
--- 获得道具[购买]服务器价*商人折扣（常用）
function ShopServer:GetShopBuyPriceById(itemId)
    local shopItem = self:GetPurchaseShopItemById(itemId)
    local currencyClientType = self:GetClientCurrencyTypeById(itemId)
    local currencyId = self:GetClientCurrencyIdById(itemId)
    if shopItem then
        local buyPrice = shopItem:GetShopPropBuyPrice()
        if buyPrice then
            return buyPrice, currencyClientType, currencyId
        else
            logwarning('ItemId为'..itemId..'的商品尚未上架')
            return 0, currencyClientType, currencyId
        end
    else
        logwarning('ItemId为'..itemId..'的商品ShopItem不存在')
        return 0, currencyClientType, currencyId
    end
end

-- must on sale
--- 获得道具[购买]服务器价*商人折扣（常用）
function ShopServer:GetShopBuyPriceByExchangeId(exchangeId)
    local shopItem = self:GetPurchaseShopItemByExchangeId(exchangeId)
    local currencyClientType = ECurrencyClientType.OnlyUnBind
    if shopItem then
        local buyPrice = shopItem:GetShopPropBuyPrice()
        local currencyClientType = self:GetClientCurrencyTypeById(shopItem.item.id)
        if buyPrice then
            return buyPrice, currencyClientType
        else
            logwarning('ExchangeId为'..exchangeId..'的商品尚未上架')
            return 0, currencyClientType
        end
    else
        logwarning('ExchangeId为'..exchangeId..'的商品ShopItem不存在')
        return 0, currencyClientType
    end
end

-- must on sale
--- 获得道具[购买]服务器价*商人折扣（常用）
function ShopServer:GetShopBuyPriceByItem(item)
    local buyPrice = 0
    local currencyClientType = ECurrencyClientType.OnlyUnBind
    local currencyId = ECurrencyClientId.Tina
    if not self:CheckIfHasPrice(item.id) then
        if not VersionUtil.EnablePerformanceMode() then
            if not self._mapPrintedErrorItems[item.id] then
                self._mapPrintedErrorItems[item.id] = 1
                logwarning('item:',item.name,'仅表现物品，不存在商品购买价格')
            end
        end
        return buyPrice, currencyClientType, currencyId
    end
    local findShopItem = self:TryGetShopItemByItem(item)
    if findShopItem then
        return self:GetShopBuyPriceByExchangeId(findShopItem.exchangeId)
    end
    if self:CheckItemBuyable(item, true) then
        local itemIdPrice, currencyClientType, currencyId = Server.ShopServer:GetShopBuyPriceById(item.id)
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        if weaponFeature and weaponFeature:IsWeapon() then
            local function GetShopBuyPriceFromPropInfo(propInfo)
                if not propInfo.num then
                    loginfo("propInfo num should not be zero, id = ", propInfo.id)
                    return 0
                end
                local num = math.max(propInfo.num, 1)
                local comTotalBuyPrice = self:GetShopBuyPriceById(propInfo.id)
                for _, component in ipairs(propInfo.components) do
                    comTotalBuyPrice = comTotalBuyPrice + GetShopBuyPriceFromPropInfo(component.prop_data)
                end
                return comTotalBuyPrice
            end
            -- 注：Components里面是没有机匣的数据的
            if item.rawPropInfo then
                buyPrice = buyPrice + GetShopBuyPriceFromPropInfo(item.rawPropInfo)
            elseif item.RawDescObj then
                for key, part in pairs(item.RawDescObj:GetAllParts()) do
                    buyPrice = buyPrice + self:GetShopBuyPriceById(part.ItemId) * part.Num
                end
            end
        else
            buyPrice = self:GetShopBuyPriceById(item.id)
        end
        buyPrice = MathUtil.GetRoundingNum(buyPrice)
        logwarning('item:',item.name,'未找到完全符合当前Item的商品表对应的武器商品，递归得出该武器商人购买价格')
        return buyPrice, currencyClientType, currencyId
    else
        logwarning('item:',item.name,'未找到完全符合当前Item的商品表对应的商品，用id找商品代替')
        local buyPrice, currencyClientType, currencyId = Server.ShopServer:GetShopBuyPriceById(item.id)
        return buyPrice, currencyClientType, currencyId
    end
end

--------------------------------------------------------------------------
--- 回收Check（回收、出售）
--------------------------------------------------------------------------
function ShopServer:CheckIfHasPrice(itemId)
	if (ItemConfigTool.GetItemConfigById(itemId) and ItemConfigTool.GetItemConfigById(itemId).IsModelOnly) or math.floor(itemId / 10 ^ 9) == EItemType.Hero or  math.floor(itemId / 10 ^ 9) ==  EItemType.Fashion then
		return false
	else
		return true
	end
end

function ShopServer:CheckIsCanRecycle(itemId)
    if self.cannotRecycleList and itemId then
        for _, id in ipairs(self.cannotRecycleList) do
            if id == itemId then
                return false
            end
        end
    end
    return true
    -- local bCannotRecycle = self.cannotRecycleList[itemId]
    -- if bCannotRecycle ~= nil then
    --     return (not bCannotRecycle)
    -- else
    --     return true
    -- end
    -- return bCannotRecycle ~= nil and (not bCannotRecycle) or true
end

--------------------------------------------------------------------------
--- 价值计算公式（请勿直接调用）
--------------------------------------------------------------------------
function ShopServer:_CalcDynamicGuidePriceByItemId(itemId, zeroRecyclePrice, dynamicGuidePrice, durabilityPer)
    local finalDynamicGuidePrice = 0
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)

    if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.BreastPlate or itemSubType == EEquipmentType.Helmet) then
        -- finalDynamicGuidePrice = zeroRecyclePrice + (dynamicGuidePrice - zeroRecyclePrice) * durabilityPer
        finalDynamicGuidePrice = dynamicGuidePrice * durabilityPer
        if not VersionUtil.EnablePerformanceMode() then
            loginfo('[Sell Item Log] [Rule 1:zeroPrice + (dynamicGuidePrice - zeroPrice) * durabilityPer] itemId ', itemId, '表格满耐久回收价格为: ', dynamicGuidePrice, ', 表格0耐久回收价格为: ', zeroRecyclePrice,  '当前耐久及当前回收价为: ', durabilityPer, finalDynamicGuidePrice)
        end
    else
        finalDynamicGuidePrice = dynamicGuidePrice * (RECYCLE_PARAM + (1 - RECYCLE_PARAM)* durabilityPer)
        if not VersionUtil.EnablePerformanceMode() then
            -- loginfo('[Sell Item Log] [Rule 2:dynamicGuidePrice * (RECYCLE_PARAM + (1 - RECYCLE_PARAM)* durabilityPer)] itemId ', itemId, '表格满耐久回收价格为: ', dynamicGuidePrice, ', RECYCLE_PARAM: ', RECYCLE_PARAM,  '当前耐久及当前回收价为: ', durabilityPer, finalDynamicGuidePrice)
        end
    end
    return finalDynamicGuidePrice
end

--------------------------------------------------------------------------
--- 回收道具
--------------------------------------------------------------------------
function ShopServer:GenSellPropAndMallRecyclePriceForReq(items, sellNums)
    local sellProps = {}
    local prices = {}

    for i, item in ipairs(items) do
        if not Server.ShopServer:CheckIsCanRecycle(item.id) then
            logwarning('[Sell Item Log] DoSellItemsReq failed, cant recycle')
            return LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.CannotRecycleItem)
        end

        --- 回收的物品信息
        local propInfo = clone(item.rawPropInfo)
        if sellNums and sellNums[i] then
            propInfo.num = sellNums[i]
        end
        table.insert(sellProps, propInfo)

        ---  回收的价格信息
        local mallRecyclePriceCurNum = item:GetSingleMallSellPrice() * sellNums[i]
        local currencyId = self:GetRecycleCurrencyIdByItem(item)

        table.insert(prices, {money_type = currencyId, price = mallRecyclePriceCurNum})
        loginfo('[Sell Item Log] 名为：', item.name, '的道具出售数目为:', propInfo.num, ' Mall最终回收价为：', mallRecyclePriceCurNum, ' 回收货币：', currencyId)
    end

    return sellProps, prices
end

--- 请求出售单个道具
function ShopServer:DoSellItemReq(item, sellNum, fSellResultCallback)
    self:DoSellItemsReq({item}, {sellNum}, fSellResultCallback)
end

--- 请求出售道具
function ShopServer:DoSellItemsReq(items, sellNums, fSellResultCallback)
    -- 检查是否被引导拦截操作
    for _, item in pairs(items) do
        if Server.GuideServer:IsGuideItemDisableOp(item.id) then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.CantSellGuideItem)
            return
        end
    end

    local req = pb.CSMallSellReq:New()
    req.merchant_id = 1
    local sellProps, prices = self:GenSellPropAndMallRecyclePriceForReq(items, sellNums)
    ---@type pb_PropInfo[]
    req.sell_props = sellProps
    req.prices = prices

    local function fSellReqCallback(res)
        if res.result == 0 then
            local currencyRtStr = ""
            if res.get_moneys then
                for _, money in ipairs(res.get_moneys) do
                    if table.contains(ECurrencyItemId, money.money_type) then
                        local currencyClientType = MapCurrencyId2ClientType[money.money_type]
                        local iconRtStr = ECurrencyClientType2RichIconTxt[currencyClientType]
                        currencyRtStr = string.format("%s %s%d",currencyRtStr, iconRtStr, money.price)
                    end
                end
            end

            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHSellSuccess)
            if not Server.RecoveryServer:IsInRecoveryState(true) then
                LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.SellSuccess, currencyRtStr))
            end

            if fSellResultCallback then
                fSellResultCallback(true, res)
            end
        else
            if res.result == Err.MallCallDeposit then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SellFailMallCallDeposit)
            elseif res.result == Err.DepositCurrencyOverFlow then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SellFailMallCallDeposit)
            else
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SellFail)
            end
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHSellError)

            if fSellResultCallback then
                fSellResultCallback(false)
            end
        end

        self.Events.evtSellRes:Invoke(res)
    end
    -- logtable(req)
    req:Request(fSellReqCallback, {bShowErrTip = false})
end

--------------------------------------------------------------------------
--- 价值获取（参考价物品原生价 or Mall回收价）
--------------------------------------------------------------------------
--- 获得道具id对应的参考价（不包含枪上的配件等），不是整数，需要自己MathUtil.GetRoundingNum(finalDynamicGuidePrice)
--- 如果该道具有耐久度变化,调用Item:GetTotalSellPrice()或者传入道具的具体耐久度!!!
---@param itemId number
---@param durabilityPer number
---@param bMallRecycle boolean
---@return number dynamicGuidePrice
---@return number currencyClientType
function ShopServer:GetShopDynamicGuidePrice(itemId, durabilityPer, bMallRecycle)
    bMallRecycle = setdefault(bMallRecycle, false)
    durabilityPer = setdefault(durabilityPer, 1.0)

    --- 检查是否有价值
    local dynamicGuidePrice = 0
    local zeroRecyclePrice = 0
    local currencyClientType = ECurrencyClientType.OnlyUnBind
    if not self:CheckIfHasPrice(itemId) then
        if not VersionUtil.EnablePerformanceMode() then
            if not self._mapPrintedErrorItems[itemId] then
                self._mapPrintedErrorItems[itemId] = 1
                logwarning('[Sell Item Log]itemId:',itemId,'仅表现物品，不存在商品回收价格')
            end
        end
        return dynamicGuidePrice, currencyClientType
    end

    -- 有缓存则不用再算
    if bMallRecycle then
        declare_if_nil(self._cacheIdDur2MallRecyclePrice, itemId, {})
        local cachePrice = self._cacheIdDur2MallRecyclePrice[itemId][durabilityPer]
        if cachePrice then
            return cachePrice.finalDynamicGuidePrice, cachePrice.currencyClientType
        end
    else
        declare_if_nil(self._cacheIdDur2DynamicGuidePrice, itemId, {})
        local cachePrice = self._cacheIdDur2DynamicGuidePrice[itemId][durabilityPer]
        if cachePrice then
            return cachePrice.finalDynamicGuidePrice, cachePrice.currencyClientType
        end
    end

    --- 读取对应的配置
    local bResultValid = true
    local bInFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
    local bHasMallRecyclePrice = bMallRecycle and self._mapItemId2MallRecycle and self._mapItemId2MallRecycle[itemId]
    local bHasRecyclePriceConfig = self._mapItemId2PriceConfig and self._mapItemId2PriceConfig[itemId]
    if (bHasMallRecyclePrice or not bMallRecycle) and bHasRecyclePriceConfig then
        dynamicGuidePrice, zeroRecyclePrice, currencyClientType = self:_ReadItemId2RecyclePrice(itemId, bMallRecycle, dynamicGuidePrice, zeroRecyclePrice, currencyClientType)
    else
        logerror('ShopServer:GetShopDynamicGuidePrice (bHasMallRecyclePrice or not bMallRecycle)',(bHasMallRecyclePrice or not bMallRecycle),'bHasRecyclePriceConfig',bHasRecyclePriceConfig)
        local config = ItemConfigTool.GetItemConfigById(itemId)
        if IsInEditor() and not Facade.GameFlowManager:CheckIsInFrontEnd() and config then
            --TODo： 单机没有拉这部分数据，编辑器下使用本地配置
            dynamicGuidePrice = config.InitialGuidePrice
            logerror('ShopServer:_ReadItemId2RecyclePrice itemID: ', itemId, ' dynamicGuidePrice is nil, read default price: ', dynamicGuidePrice)
            -- dynamicGuidePrice = config.InitialGuidePrice
        else
            bResultValid = false
            --- 不存在则报错
            if config then
                dynamicGuidePrice = config.InitialGuidePrice
                if not VersionUtil.EnablePerformanceMode() then
                    if not self._mapPrintedErrorItems[itemId] then
                        self._mapPrintedErrorItems[itemId] = 1
                        logerror('商品 ItemID:'..itemId..' 未找到回收价，请检查此Item的回收价是否配置到物品表！'..ItemConfigTool.GetItemConfigById(itemId).Name)
                    end
                    -- LuaGlobalEvents.evtServerShowTip:Invoke(str)
                end
            else
                logerror('[Sell Item Log]请删除商城中已被道具表删除的道具，ID:', itemId)
            end
        end
    end

    --- 计算与缓存
    local finalDynamicGuidePrice = self:_CalcDynamicGuidePriceByItemId(itemId, zeroRecyclePrice, dynamicGuidePrice, durabilityPer)
    if bResultValid then
        if bMallRecycle then
            self._cacheIdDur2MallRecyclePrice[itemId][durabilityPer] = {
                currencyClientType = currencyClientType,
                finalDynamicGuidePrice = finalDynamicGuidePrice
            }
        else
            self._cacheIdDur2DynamicGuidePrice[itemId][durabilityPer] = {
                currencyClientType = currencyClientType,
                finalDynamicGuidePrice = finalDynamicGuidePrice
            }
        end
        -- logwarning('[Sell Item Log]本次回收价有效，正常计入缓存，ID和耐久:', itemId, durabilityPer, finalDynamicGuidePrice, currencyClientType)
    else
        -- logwarning('[Sell Item Log]本次回收价未找到，不计入缓存，ID:', itemId)
    end
    return finalDynamicGuidePrice, currencyClientType
end

--- 获得道具回收时货币类型
function ShopServer:GetRecycleCurrencyTypeByItem(item)
    local currencyClientType = ECurrencyClientType.OnlyUnBind
    return currencyClientType
end

--- 获得道具考虑绑定态的回收时货币类型
--- 一般情况下不包含绑定纸币和非绑定的混合态
function ShopServer:GetRecycleCurrencyIdByItem(item)
    local currencyType = self:GetRecycleCurrencyTypeByItem(item)
    local currencyIds = MapCurrencyClientType2Ids[currencyType]
    if currencyIds and next(currencyIds) then
        return currencyIds[1]
    else
        logerror('[Sell Item Log] 不存在此道具的回收货币类型配置, 使用默认非绑定', item.nam)
        return ECurrencyItemId.UnBindBankNote
    end
end

--- 获得道具表格配置的货币类型
--- 一般情况下不包含绑定纸币和非绑定的混合态
function ShopServer:GetClientCurrencyTypeById(itemId)
    local currencyClientType
    if self._mapItemId2BuyPrice and self._mapItemId2BuyPrice[itemId] then
        currencyClientType = self._mapItemId2BuyPrice[itemId].currencyClientType
    else
        currencyClientType = ECurrencyClientType.OnlyUnBind
    end
    return currencyClientType
end

--- 获得道具表格配置的货币类型
--- 一般情况下不包含绑定纸币和非绑定的混合态
function ShopServer:GetClientCurrencyIdById(itemId)
    local currencyId
    if self._mapItemId2BuyPrice and self._mapItemId2BuyPrice[itemId] then
        currencyId = self._mapItemId2BuyPrice[itemId].currencyId
    else
        currencyId = ECurrencyClientId.Tina
    end
    return currencyId
end

---@param item ItemBase
---@param durabilityPer number
---@param bMallRecycle boolean
function ShopServer:GetShopStackDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle)
    local singleDynamicGuidePrice = self:GetShopSingleDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle)
    singleDynamicGuidePrice = MathUtil.GetRoundingNum(singleDynamicGuidePrice * item.num)
    return singleDynamicGuidePrice
end

---@param item ItemBase
---@param durabilityPer number
---@param bMallRecycle boolean
function ShopServer:GetShopSingleDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle, bCalcBullets)
    --- 0.有没有耐久
    local itemFeature = item:GetFeature()
    if durabilityPer == nil then
        local curDurability, maxDurability, maxDurabilityOrigin = 0, 0, 0
        --需要计算耐久度的道具
        if (itemFeature.IsHelmet and itemFeature:IsHelmet()) or
         (itemFeature.IsBreastPlate and itemFeature:IsBreastPlate()) or
         (itemFeature.IsMedicine and itemFeature.IsDurabilityMedicine and itemFeature:IsMedicine() and itemFeature.isDurabilityItem) or --只有耐久度的药品
         (itemFeature.IsKey and itemFeature:IsKey()) then
            if itemFeature.GetDurabilityValue then
            curDurability, maxDurability = itemFeature:GetDurabilityValue()
            end
            if itemFeature.GetDurabilityValueOriginal then
                curDurability, maxDurabilityOrigin = itemFeature:GetDurabilityValueOriginal()
            end
            curDurability = setdefault(curDurability, 0)
            maxDurability = setdefault(maxDurability, 0)
            maxDurabilityOrigin = setdefault(maxDurabilityOrigin, 0)
            local durability = 1
            -- 没有耐久度
            if curDurability == 0 and maxDurability == 0 then
                loginfo('ShopServer:GetShopSingleDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle, bCalcBullets)  name =', item.name,' 异常耐久度 maxDurability = 0，curDurability = 0', 'price = ', 1)
                return 1 --保底0/0卖不出去, 异常耐久直接返回1
            else
                maxDurabilityOrigin = maxDurabilityOrigin == 0 and (maxDurability == 0 and 1 or maxDurability) or maxDurabilityOrigin
                --有耐久度且是头盔护甲
                if itemFeature:GetFeatureType() == EFeatureType.Equipment and (itemFeature:IsHelmet() or itemFeature:IsBreastPlate()) and self._mapItemId2PriceConfig[item.id] then
                    local recyclePricePercent = self._mapItemId2PriceConfig[item.id][EItemId2RecyclePriceID.recyclePricePercent] or 0
                    if bMallRecycle then
                        --军需处售卖公式 
                        if recyclePricePercent then
                            durabilityPer = (recyclePricePercent * maxDurability + (1 - recyclePricePercent) * curDurability)  / maxDurabilityOrigin --max = 0, cur = 1 => 0.45 * 0 + 0.55 * 1 /155
                        else
                            logerror('ShopServer:GetShopSingleDynamicGuidePriceByItem ', item.id, '没配recyclePricePercent')
                        end
                        loginfo('ShopServer:GetShopSingleDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle, bCalcBullets) name =', item.name, '有耐久度且是头盔护甲 recyclePricePercent= ', recyclePricePercent, 'maxDurability = ', maxDurability, 'curDurability = ', curDurability, 'maxDurabilityOrigin = ', maxDurabilityOrigin, 'durabilityPer = ', durabilityPer)
                    else
                        --回收价公式
                        local modifyParam = item.modifyParam or 0
                        local modifyParam2 = item.modifyParam2 or 0
                        if recyclePricePercent then
                            -- durabilityPer = (recyclePricePercent * maxDurability + (1 - recyclePricePercent) * curDurability)  / maxDurabilityOrigin --max = 0, cur = 1 => 0.45 * 0 + 0.55 * 1 /155
                            local n = MathUtil.GetNumPrecision((maxDurability / maxDurabilityOrigin - 1) * modifyParam, 3)
                            local factor = MathUtil.GetNumPrecision(math.exp(n), 3)

                            local n2 = MathUtil.GetNumPrecision((curDurability / maxDurabilityOrigin - 1) * modifyParam2, 3)
                            local factor2 = MathUtil.GetNumPrecision(math.exp(n2), 3)

                            durabilityPer = (curDurability * (1 - recyclePricePercent)) / maxDurabilityOrigin * factor2 + (maxDurability * recyclePricePercent) / maxDurabilityOrigin * factor
                        else
                            logerror('ShopServer:GetShopSingleDynamicGuidePriceByItem ', item.id, '没配recyclePricePercent')
                        end
                        loginfo('ShopServer:GetShopSingleDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle, bCalcBullets) name =', item.name, '有耐久度且是头盔护甲 recyclePricePercent= ', recyclePricePercent, 'maxDurability = ', maxDurability, 'curDurability = ', curDurability, 'maxDurabilityOrigin = ', maxDurabilityOrigin, 'durabilityPer = ', durabilityPer, 'ModifyParam = ',modifyParam)
                    end
                else
                    --有耐久但不是头盔护甲
                    durabilityPer = curDurability / maxDurabilityOrigin
                    loginfo('ShopServer:GetShopSingleDynamicGuidePriceByItem(item, durabilityPer, bMallRecycle, bCalcBullets)  name =', item.name,'有耐久但不是头盔护甲 curDurability= ', curDurability, 'maxDurabilityOrigin = ', maxDurabilityOrigin, 'durabilityPer = ', durabilityPer)
                    if self._mapItemId2PriceConfig then
                        loginfo('或没有价格配置', self._mapItemId2PriceConfig[item.id], '#self._mapItemId2PriceConfig =', #self._mapItemId2PriceConfig)
                    else
                        loginfo('或没有价格配置 self._mapItemId2PriceConfig is nil')
                    end

                end
            end
        else
            durabilityPer = 1
        end
    end
    --- 1.不同分类不同耐久算法
    local totalDynamicGuidePrice = 0
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsWeapon() then
        if weaponFeature:IsShowPoorWeapon() then
            -- 次级预设使用对应的次级预设ID计算价格
            totalDynamicGuidePrice = self:GetShopDynamicGuidePrice(weaponFeature:GetPoorWeaponId(), durabilityPer, bMallRecycle)
        else
            local function GetSellPriceFromPropInfo(propInfo)
                if not propInfo.num then
                    loginfo("[Sell Item Log] propInfo num should not be zero, id = ", propInfo.id)
                    return 0
                end
                local num = math.max(propInfo.num, 1)
                local comTotalDynamicGuidePrice = self:GetShopDynamicGuidePrice(propInfo.id, durabilityPer, bMallRecycle) * num
                if propInfo.components then
                    for _, component in ipairs(propInfo.components) do
                        comTotalDynamicGuidePrice = comTotalDynamicGuidePrice + GetSellPriceFromPropInfo(component.prop_data)
                    end
                end
                --机匣数据
                bCalcBullets = setdefault(bCalcBullets, true)
                if propInfo.weapon and propInfo.weapon.load_bullets and bCalcBullets then
                    for _, bullet in ipairs(propInfo.weapon.load_bullets) do
                        comTotalDynamicGuidePrice = comTotalDynamicGuidePrice + GetSellPriceFromPropInfo(bullet)
                    end
                end
                return comTotalDynamicGuidePrice
            end
            -- 注：Components里面是没有机匣的数据的
            if item.rawPropInfo then
                totalDynamicGuidePrice = totalDynamicGuidePrice + GetSellPriceFromPropInfo(item.rawPropInfo)
            elseif item.RawDescObj then
                totalDynamicGuidePrice = 0
                for key, part in pairs(item.RawDescObj:GetAllParts()) do
                    totalDynamicGuidePrice =
                        totalDynamicGuidePrice + self:GetShopDynamicGuidePrice(part.ItemId, durabilityPer, bMallRecycle) * part.Num
                end
            end
        end
    else
        totalDynamicGuidePrice = self:GetShopDynamicGuidePrice(item.id, durabilityPer, bMallRecycle)
    end
    if bMallRecycle then
        totalDynamicGuidePrice = MathUtil.GetRoundingNum(totalDynamicGuidePrice)
    else
        totalDynamicGuidePrice = math.floor(totalDynamicGuidePrice) --回收价向下取整
    end
    return totalDynamicGuidePrice
end

--------------------------------------------------------------------------
--- 指定兑换相关（同一商品）
--------------------------------------------------------------------------
--- 请勿直接调用此方法，使用ShopModule:DoDesignatedShopProp(shopItem, curBuyNum, targetSlotType)
---@param shopItem ShopItemStruct
---@param curBuyNum number
---@param finalPriceList table
---@param targetSlotType SlotType
---@param entranceType LogAnalysisTool.EMallExtranceType
function ShopServer:DoDesignatedSinglePropReq(shopItem, curBuyNum, finalPriceList, targetSlotType, entranceType)
    if self.lastBuyTimeStamp and TimeUtil.GetCurrentTimeMillis() - self.lastBuyTimeStamp < 0.5 then
        logerror('ShopServer:DoBuySinglePropReq 0.5s高频发送拦截')
        return
    end
    self.lastBuyTimeStamp = TimeUtil.GetCurrentTimeMillis()
    entranceType = setdefault(entranceType, LogAnalysisTool.EMallExtranceType.ShopMain)
    if not shopItem then
        logwarning('shopitem is nil')
        return false
    end

    local bEnoughBuy = shopItem:GetIsCostEnough(curBuyNum)
    if not bEnoughBuy then
        logwarning('cant enough buy')
        return false
    end

    local req = pb.CSMallBuyReq:New()
    req.mall_prop = clone(shopItem:GetRawProp())
    req.mall_prop.prop_info.num = curBuyNum
    req.exchange_type = shopItem:GetExchangeType()
    req.entrance_type = entranceType

    if ItemHelperTool.IsBuyToSlotType(targetSlotType) then
        req.mall_prop.prop_info.position = targetSlotType
        req.mall_prop.prop_info.loc.pos = targetSlotType
        req.mall_prop.prop_info.loc.space_id = 0
    end
    req.prices = finalPriceList

    local fOnDesignatedSinglePropRes = function(res)
        ---@type SinglePropMsg
        local singlePropMsg = {
            bSuccess = true,
            shopItemBuyInfo = {shopItem = shopItem, curBuyNum = curBuyNum, targetSlotType = targetSlotType},
            result = res.result,
            prop_changes = res.prop_changes,
        }
        if res.result == 0 then
            if res.prop_changes and res.prop_changes.prop_changes and #res.prop_changes.prop_changes > 0 then
                singlePropMsg.bSuccess = true
            else
                singlePropMsg.bSuccess = false
            end
        else
            singlePropMsg.bSuccess = false
        end
        self:HandleSinglePropFinished(singlePropMsg)
    end
    dump(req)
    req:Request(fOnDesignatedSinglePropRes, {bEnableHighFrequency = false})
    return true
end

--------------------------------------------------------------------------
--- 购买相关（同一商品，包含抢购）
--------------------------------------------------------------------------
ShopServer.ShopReqPriceHandler = {
    [EShopOptType.Purchase] = function(self, req, shopItem, curBuyNum)
        local totalBuyPrice = shopItem:GetShopPropBuyPrice() * curBuyNum
        local currencyType = self:GetClientCurrencyTypeById(shopItem.item.id)
        req.prices = self:GenTotalPricesForReq(totalBuyPrice, currencyType)
        logwarning('[ ArmedForce ]-------------------buy--------------', shopItem.item.name)
        return req
    end,
    [EShopOptType.Currency] = function(self, req, shopItem, curBuyNum)
        --wait for currency option
        return req
    end,
    [EShopOptType.Exchange] = function(self, req, shopItem, curBuyNum)
        local costItemIdNums = shopItem:GetCostList()
        for _,costItemIdNumInfo in pairs(costItemIdNums) do
            if table.contains(ECurrencyItemId, costItemIdNumInfo.itemId) then
                local prices = self:GenTotalPricesForReq(costItemIdNumInfo.num * curBuyNum)
                for _, price in pairs(prices) do
                    table.insert(req.prices, price)
                end
            else
                local needNum = costItemIdNumInfo.num * curBuyNum
                local ownItemList = Server.InventoryServer:GetItemsById(costItemIdNumInfo.itemId)
                table.sort(ownItemList, ItemSortTool.SortShopCostItems)
                local findNum = 0
                local findGid = 0
                for _, item in pairs(ownItemList) do
                    findGid = item.gid
                    if needNum > 0 then
                        findNum = item.num
                        if findNum < needNum then
                            table.insert(req.prices, {money_type = costItemIdNumInfo.itemId, price = findNum, gid = findGid})
                        else
                            findNum = needNum
                            table.insert(req.prices, {money_type = costItemIdNumInfo.itemId, price = needNum, gid = findGid})
                        end
                        needNum = needNum - findNum
                    else
                        break
                    end
                end
            end
        end
        return req
    end,
}

-- 获取批量购买/兑换 shop
ShopServer.ShopReqTotalPriceHandler = {
    [EShopOptType.Exchange] = function(self, req, shopItemExchangeInfoList)
        local costItemId2Num = {}
        for _, exchangeInfo in pairs(shopItemExchangeInfoList) do
            local shopItem = exchangeInfo.shopItem
            local exchangeNum = exchangeInfo.curBuyNum

            local costItemList = shopItem:GetCostList()
            for _, costItemNumInfo in pairs(costItemList) do
                if costItemNumInfo and costItemNumInfo.itemId and costItemNumInfo.num then
                    if costItemId2Num[costItemNumInfo.itemId] then
                        costItemId2Num[costItemNumInfo.itemId] = costItemId2Num[costItemNumInfo.itemId] + costItemNumInfo.num * exchangeNum
                    else
                        costItemId2Num[costItemNumInfo.itemId] = costItemNumInfo.num * exchangeNum
                    end
                end
            end
        end
        for itemId, needNum in pairs(costItemId2Num) do
            local ownItemList = Server.InventoryServer:GetItemsById(itemId)
            table.sort(ownItemList, ItemSortTool.SortShopCostItems)
            local findNum = 0
            local findGid = 0
            for _, item in pairs(ownItemList) do
                findGid = item.gid
                if needNum > 0 then
                    findNum = item.num
                    if findNum < needNum then
                        table.insert(req.total_prices, {money_type = itemId, price = findNum, gid = findGid})
                    else
                        findNum = needNum
                        table.insert(req.total_prices, {money_type = itemId, price = needNum, gid = findGid})
                    end
                    needNum = needNum - findNum
                else
                    break
                end
            end
        end
    end
}

function ShopServer:GenTotalPricesForReq(totalPrice, currencyClientType, optPrices)
    currencyClientType = setdefault(currencyClientType, ECurrencyClientType.OnlyUnBind)
    local prices = setdefault(optPrices, {})
    if totalPrice >= 0 then
        local currencyIds = MapCurrencyClientType2Ids[currencyClientType]
        if currencyIds and next(currencyIds) then
            table.insert(prices, {money_type = currencyIds[1], price = totalPrice})
        end
    end
    return prices
end

--- 请勿直接调用此方法，使用ShopModule:DoShopProp(shopItem, curBuyNum, targetSlotType)
---@param shopItem ShopItemStruct
---@param curBuyNum number
---@param targetSlotType SlotType
---@param entranceType LogAnalysisTool.EMallExtranceType
function ShopServer:DoBuySinglePropReq(shopItem, curBuyNum, targetSlotType, entranceType, bShowSuccessTip)
    if self.lastBuyTimeStamp and TimeUtil.GetCurrentTimeMillis() - self.lastBuyTimeStamp < 0.5 then
        logerror('ShopServer:DoBuySinglePropReq 0.5s高频发送拦截',TimeUtil.GetCurrentTimeMillis(),'self=',self.lastBuyTimeStamp)
        return
    end
    self.lastBuyTimeStamp = TimeUtil.GetCurrentTimeMillis()
    bShowSuccessTip = setdefault(bShowSuccessTip, true)
    entranceType = setdefault(entranceType, LogAnalysisTool.EMallExtranceType.ShopMain)
    if not shopItem then
        logwarning('shopitem is nil')
        return 
    end

    if shopItem:GetIsPanicSItem() then
        if not self:IsAnyPanicSItemBuying() then
            self.lastBuyPanicSItem = shopItem
            self.lastBuyPanicNum = curBuyNum
            self.lastBuyPanicSItem:SetIsInPanicing(true)
            self.Events.evtSItemFreshIsInPansicing:Invoke(self.lastBuyPanicSItem)
        else
            logwarning('cant pansic buy two shopitems')
            return
        end
    end

    local bEnoughBuy = shopItem:GetIsCostEnough(curBuyNum)
    if not bEnoughBuy then
        logwarning('cant enough buy')
        return
    end

    local req = pb.CSMallBuyReq:New()
    req.mall_prop = clone(shopItem:GetRawProp())
    req.mall_prop.prop_info.num = curBuyNum
    req.exchange_type = shopItem:GetExchangeType()
    req.entrance_type = entranceType

    if ItemHelperTool.IsBuyToSlotType(targetSlotType) then
        req.mall_prop.prop_info.position = targetSlotType
        req.mall_prop.prop_info.loc.pos = targetSlotType
        req.mall_prop.prop_info.loc.space_id = 0
    end
    local optType = shopItem:GetExchangeType()
    req.prices = {}
    req = ShopServer.ShopReqPriceHandler[optType](self, req, shopItem, curBuyNum)

    local fOnSinglePropRes = function(res)
        ---@type SinglePropMsg
        local singlePropMsg = {
            bSuccess = true,
            shopItemBuyInfo = {shopItem = shopItem, curBuyNum = curBuyNum, targetSlotType = targetSlotType},
            result = res.result,
            prop_changes = res.prop_changes,
            bShowSuccessTip = bShowSuccessTip
        }
        if res.result == 0 then
            if res.prop_changes and res.prop_changes.prop_changes and #res.prop_changes.prop_changes > 0 then
                singlePropMsg.bSuccess = true
                self:HandleSinglePropFinished(singlePropMsg)
            else
                logwarning('抢购中！！！！')
            end
        else
            singlePropMsg.bSuccess = false
            self:HandleSinglePropFinished(singlePropMsg)
        end
    end
    dump(req)
    req:Request(fOnSinglePropRes, {bEnableHighFrequency = true})
end

--- 处理商店单商品购买完成的情况
function ShopServer:HandleSinglePropFinished(singlePropMsg)
    local shopItemBuyInfo = singlePropMsg.shopItemBuyInfo
    local shopItem = shopItemBuyInfo.shopItem
    local targetSlotType = shopItemBuyInfo.targetSlotType
    local bBuyAndEquip = ItemHelperTool.IsBuyToSlotType(targetSlotType)
    local bShowSuccessTip = singlePropMsg.bShowSuccessTip

    local shopPropName = shopItem.item.name
    --- 刷新商品包内数量
    local needShowNum = shopItem.tradeContentCount > 1
    if needShowNum then
        shopPropName = shopPropName..string.format("x%s", shopItem.tradeContentCount)
    end

    if singlePropMsg.bSuccess then
        -- self:_CheckIsAutoUseGift(shopItemBuyInfo.shopItem, shopItemBuyInfo.curBuyNum, singlePropMsg.prop_changes)
        if bShowSuccessTip then
            if shopItem then
                if shopItem:GetIsPanicSItem() then
                    LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.PanicSuccess)
                else
                    local optType = shopItem:GetExchangeType()
                    local isCollectionItem = false
                    if shopItem.item and (shopItem.item.itemMainType == EItemType.WeaponSkin or shopItem.item.itemMainType == EItemType.CollectionProp) then isCollectionItem = true end
                    if optType == EShopOptType.Exchange then
                        if bBuyAndEquip then
                            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.ExchangeAndEquipSuccess, shopPropName))
                        elseif isCollectionItem then
                            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.ExchangeCollectionSuccess, shopPropName))
                        else
                            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.ExchangeSuccess, shopPropName))
                        end
                    else
                        if bBuyAndEquip then
                            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.BuyAndEquipSuccess, shopPropName))
                        elseif isCollectionItem then
                            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.BuyCollectionSuccess, shopPropName))
                        else
                            LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.BuySuccess, shopPropName))
                        end
                    end
                end
            else
                logwarning(' shop item is nil, but buy success')
            end
        else
            loginfo('ShopServer:HandleSinglePropFinished(singlePropMsg) dont show successTip')
        end
    else
        if shopItem then
            if singlePropMsg.result ~= Err.DepositSpaceNotEnough then --弹出req错误提示即可
                if shopItem:GetIsPanicSItem() then
                    LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.PanicFail, shopPropName))
                else
                    LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.BuyFail, shopPropName))
                end
            end
        else
            logwarning(' shop item is nil, buy failed')
        end
        self:TryCompareShopCdnVer()
    end
    self.Events.evtShopFinished:Invoke(singlePropMsg)
    self:_ResetPanicSItem(shopItem)
    if shopItem:GetRawItemNum() ~= - 1 then
        self:_FetchGoodsLimit()
    end
end

function ShopServer:_CheckIsBuyToEquipPos(targetSlotType)
    if targetSlotType and (targetSlotType < ESlotType.DepositoryStart
    or targetSlotType > ESlotType.DepositoryEnd) then
        return true
    end
    return false
end

function ShopServer:_CheckIsAutoUseGift(shopItem, curBuyNum, prop_changes)
    if Server.RewardServer:GetIsGiftAutoUse(shopItem.item.id) then
        if prop_changes.prop_changes then
            for idx, propInfoArray in ipairs(prop_changes.prop_changes) do
                local propInfo = propInfoArray.prop
                local newAddItem = Server.InventoryServer:GetItemByGid(propInfo.gid)
                local rewardFeature = newAddItem:GetFeature(EFeatureType.Reward)
                if rewardFeature and rewardFeature:IsRandomGift() then
                    Server.RewardServer:UseGift(newAddItem)
                end
            end
        end
    end
end

function ShopServer:IsAnyPanicSItemBuying()
    if self.lastBuyPanicSItem then
        return true
    else
        return false
    end
end

--- 重置抢购
function ShopServer:_ResetPanicSItem(shopItem)
    if shopItem == self.lastBuyPanicSItem then
        self.lastBuyPanicSItem:SetIsInPanicing(false)
        self.Events.evtSItemFreshIsInPansicing:Invoke(self.lastBuyPanicSItem)
        self.lastBuyPanicSItem = nil
        self.lastBuyPanicNum = nil
    end
end

--- 刷新抢购
function ShopServer:_DoFreshPansicShopItem(exchangeId)
    local req = pb.CSMallGetPanicBuyStateReq:New()
    req.exchange_id = exchangeId
    local OnGetPanicBuyStateRes = function(res)
        if res.result == 0 then
            self._mapExchangeID2SItem[exchangeId]:SetPanicWaitingNum(res.waiting_num)
            local pansicDataMsg = {
                shopItem = self._mapExchangeID2SItem[exchangeId],
                WaitingNum = res.waiting_num,
            }
            self.Events.evtPanicSItemTickFresh:Invoke(pansicDataMsg)
        end
    end
    req:Request(OnGetPanicBuyStateRes, {bShowLoading = false})
end

---@class ShopItemBuyInfo
---@field shopItem ShopItemStruct
---@field curBuyNum number
---@field targetSlotType ESlotType

--------------------------------------------------------------------------
--- 购买相关带回调（同一商品，包含抢购）
--------------------------------------------------------------------------
---@class SinglePropMsg
---@field bSuccess boolean
---@field shopItemBuyInfo ShopItemBuyInfo
---@field result res.result
---@field prop_changes res.prop_changes

--- 请勿直接调用此方法，使用ShopModule:DoShopPropWithCallback(shopItem, curBuyNum, targetSlotType, fCallbackIns)
---@param shopItem ShopItemStruct
---@param curBuyNum number
---@param targetSlotType SlotType
---@param fCallbackIns function
---@param entranceType LogAnalysisTool.EMallExtranceType
function ShopServer:DoBuySinglePropReqWithCallback(shopItem, curBuyNum, targetSlotType, fCallbackIns, entranceType)
    -- 限制发送频率
    if self.lastBuyTimeStamp and TimeUtil.GetCurrentTimeMillis() - self.lastBuyTimeStamp < 0.5 then
        logerror('ShopServer:DoBuySinglePropReq 0.5s高频发送拦截')
        return
    end
    self.lastBuyTimeStamp = TimeUtil.GetCurrentTimeMillis()
    entranceType = setdefault(entranceType, LogAnalysisTool.EMallExtranceType.ShopMain)
    ---@type SinglePropMsg
    local singlePropMsg = {}
    if not shopItem then
        logwarning('shopitem is nil')
        if fCallbackIns then
            singlePropMsg.bSuccess = false
            fCallbackIns(singlePropMsg)
            fCallbackIns = nil
        end
        return 
    end
    if shopItem:GetIsPanicSItem() then
        if not self:IsAnyPanicSItemBuying() then
            self.lastBuyPanicSItem = shopItem
            self.lastBuyPanicNum = curBuyNum
            self.lastBuyPanicSItem:SetIsInPanicing(true)
            self.Events.evtSItemFreshIsInPansicing:Invoke(self.lastBuyPanicSItem)
        else
            logwarning('cant pansic buy two shopitems')
            if fCallbackIns then
                singlePropMsg.bSuccess = false
                fCallbackIns(singlePropMsg)
                fCallbackIns = nil
            end
            return
        end
    end

    local bEnoughBuy = shopItem:GetIsCostEnough(curBuyNum)
    if not bEnoughBuy then
        logwarning('cant enough buy')
        if fCallbackIns then
            singlePropMsg.bSuccess = false
            fCallbackIns(singlePropMsg)
            fCallbackIns = nil
        end
        return
    end

    local req = pb.CSMallBuyReq:New()
    req.mall_prop = clone(shopItem:GetRawProp())
    req.mall_prop.prop_info.num = curBuyNum
    req.exchange_type = shopItem:GetExchangeType()
    req.entrance_type = entranceType
    if ItemHelperTool.IsBuyToSlotType(targetSlotType) then
        req.mall_prop.prop_info.position = targetSlotType
        req.mall_prop.prop_info.loc.pos = targetSlotType
        req.mall_prop.prop_info.loc.space_id = 0
    end
    req.prices = {}
    local optType = shopItem:GetExchangeType()
    req = ShopServer.ShopReqPriceHandler[optType](self, req, shopItem, curBuyNum)

    local OnMallBuyRes = function(res)
        ---@type SinglePropMsg
        singlePropMsg = {
            bSuccess = true,
            shopItemBuyInfo = {shopItem = shopItem, curBuyNum = curBuyNum,targetSlotType = targetSlotType},
            result = res.result,
            prop_changes = res.prop_changes,
        }
        if res.result == 0 then
            if res.prop_changes and res.prop_changes.prop_changes and #res.prop_changes.prop_changes > 0 then
                singlePropMsg.bSuccess = true
                if fCallbackIns then
                    fCallbackIns(singlePropMsg)
                    fCallbackIns = nil
                end
            end
        else
            singlePropMsg.bSuccess = false
            self:TryCompareShopCdnVer()
            if fCallbackIns then
                fCallbackIns(singlePropMsg)
                fCallbackIns = nil
            end
        end
    end
    dump(req)
    req:Request(OnMallBuyRes, {bEnableHighFrequency = true})
end

--------------------------------------------------------------------------
--- 购买相关带回调（多个商品）
--------------------------------------------------------------------------
---@class MultiPropMsg
---@field bSuccess boolean
---@field shopItemBuyInfoList ShopItemBuyInfo[]
---@field result res.result
---@field prop_changes res.prop_changes

--- 请勿直接调用此方法，使用ShopModule:DoShopMultiProp(shopItemBuyInfoList, fCallbackIns)
---@param shopItemBuyInfoList shopItemBuyInfo[]
---@param fCallbackIns fCallbackIns
---@param bEnoughBuy bool
---@param needCurrencyIds array
---@param currencyId2CostNum map
---@param entranceType LogAnalysisTool.EMallExtranceType
---@param bShowErrTip bShowErrTip
function ShopServer:DoBuyMultiPropReq(shopItemBuyInfoList, fCallbackIns, bEnoughBuy, needCurrencyIds, currencyId2CostNum, entranceType, bShowErrTip)
    entranceType = setdefault(entranceType, LogAnalysisTool.EMallExtranceType.Equipment)
    bShowErrTip = setdefault(bShowErrTip, true)
    ---@type MultiPropMsg
    local multiPropMsg = {}
    if not bEnoughBuy then
        logwarning('cant enough batch buy')
        multiPropMsg.bSuccess = false
        self:HandleMultiPropFinished(multiPropMsg, fCallbackIns)
        return 
    end
    
    if not VersionUtil.EnablePerformanceMode() then
        for k, v in ipairs(shopItemBuyInfoList) do
            if v.shopItem:GetExchangeType() ~= EShopOptType.Purchase then
                logwarning('cant batch exchange Items , please check shop opt type')
                multiPropMsg.bSuccess = false
                self:HandleMultiPropFinished(multiPropMsg, fCallbackIns)
                return
            end
        end
    end

    local buyReq = pb.CSMallBatchBuyReq:New()
    buyReq.props = {}
    buyReq.total_prices = {}
    buyReq.send_drug_to_chesthang = false
    buyReq.exchange_type = DEFALUT_SHOP_OPT_TYPE
    buyReq.entrance_type = entranceType

    if shopItemBuyInfoList and next(shopItemBuyInfoList) then
        for _, shopItemInfo in ipairs(shopItemBuyInfoList) do
            local shopItem = shopItemInfo.shopItem
            local buyNum = shopItemInfo.curBuyNum
            local purchaseLimit = shopItem:GetItemPurchaseLimit()

            local targetSlotType = shopItemInfo.targetSlotType
            local bSendDrugToChesthang = shopItemInfo.bSendDrugToChesthang

            if buyNum > purchaseLimit and purchaseLimit > 0 then
                while buyNum > 0 do
                    local num = math.min(purchaseLimit, buyNum)
                    local mallPropInfo = self:_GenMultiPropInfoForReq(shopItem, targetSlotType, bSendDrugToChesthang, buyReq)
                    mallPropInfo.prop_info.num = num
                    table.insert(buyReq.props, mallPropInfo)
                    buyNum = buyNum - num
                end
            else
                local mallPropInfo = self:_GenMultiPropInfoForReq(shopItem, targetSlotType, bSendDrugToChesthang, buyReq)
                mallPropInfo.prop_info.num = buyNum
                table.insert(buyReq.props, mallPropInfo)
            end
        end

        local prices = {}
        for currencyId, totalBuyPrice in pairs(currencyId2CostNum) do
            prices = self:GenTotalPricesForReq(totalBuyPrice, currencyId, prices)
        end
        buyReq.total_prices = prices
        
        -- dump(buyReq)
        local fOnMultiBuyCallback = function(res)
            loginfo('ShopServer:OnBuyMultiPropRes', shopItemBuyInfoList, fCallbackIns, res)
            ---@type MultiPropMsg
            local multiPropMsg = {}
            if res.result == 0 then
                multiPropMsg = {
                    bSuccess = true,
                    result = res.result,
                    shopItemBuyInfoList = shopItemBuyInfoList,
                    prop_changes = res.prop_changes,
                }
            else
                multiPropMsg.bSuccess = false
            end
            self:HandleMultiPropFinished(multiPropMsg, fCallbackIns)
        end
        buyReq:Request(fOnMultiBuyCallback, {bShowErrTip = bShowErrTip})
    else
        multiPropMsg.bSuccess = false
        self:HandleMultiPropFinished(multiPropMsg, fCallbackIns)
    end
end

function ShopServer:_GenMultiPropInfoForReq(shopItem, targetSlotType, bSendDrugToChesthang, buyReq)
    local mallPropInfo = clone(shopItem:GetRawProp())

    if ItemHelperTool.IsBuyToSlotType(targetSlotType) then
        mallPropInfo.prop_info.position = targetSlotType
        mallPropInfo.prop_info.loc.pos = targetSlotType
        mallPropInfo.prop_info.loc.space_id = 0
    end
    if bSendDrugToChesthang then
        mallPropInfo.prop_info.position = targetSlotType
        if not buyReq.send_drug_to_chesthang then
            buyReq.send_drug_to_chesthang = true
        end
    end
    return mallPropInfo
end

function ShopServer:DoExchangeMutiPropReq(shopItemExchangeInfoList, fCallbackIns, bEnoughExchange, entranceType, bShowErrTip, bAlwaysShowFullTip)
    entranceType = setdefault(entranceType, LogAnalysisTool.EMallExtranceType.Equipment)
    bShowErrTip = setdefault(bShowErrTip, true)
    ---@type MultiPropMsg
    local multiPropMsg = {}
    if not bEnoughExchange then
        logwarning('ShopServer:DoExchangeMutiPropReq cant enough batch exchange')
        multiPropMsg.bSuccess = false
        self:HandleMultiPropFinished(multiPropMsg, fCallbackIns)
        return
    end
    if not VersionUtil.EnablePerformanceMode() then
        for k, v in ipairs(shopItemExchangeInfoList) do
            if v.shopItem:GetExchangeType() ~= EShopOptType.Exchange then
                logwarning('cant batch purchase Items , please check shop opt type')
                multiPropMsg.bSuccess = false
                self:HandleMultiPropFinished(multiPropMsg, fCallbackIns)
                return
            end
        end
    end

    local buyReq = pb.CSMallBatchBuyReq:New()
    buyReq.props = {}
    buyReq.total_prices = {}
    buyReq.send_drug_to_chesthang = false
    buyReq.exchange_type = EShopOptType.Exchange
    buyReq.entrance_type = entranceType

    if shopItemExchangeInfoList and next(shopItemExchangeInfoList) then
        for _, shopItemInfo in ipairs(shopItemExchangeInfoList) do
            local shopItem = shopItemInfo.shopItem
            local buyNum = shopItemInfo.curBuyNum
            local purchaseLimit = shopItem:GetItemPurchaseLimit()
            local targetSlotType = shopItemInfo.targetSlotType
            local bSendDrugToChesthang = shopItemInfo.bSendDrugToChesthang
            
            if buyNum > purchaseLimit and purchaseLimit > 0  then --She1正式：purchaseLimit == 0死循环
                while buyNum > 0 do
                    local num = math.min(purchaseLimit, buyNum)
                    local mallPropInfo = self:_GenMultiPropInfoForReq(shopItem, targetSlotType, bSendDrugToChesthang, buyReq)
                    mallPropInfo.prop_info.num = num
                    table.insert(buyReq.props, mallPropInfo)
                    buyNum = buyNum - num
                end
            else
                local mallPropInfo = self:_GenMultiPropInfoForReq(shopItem, targetSlotType, bSendDrugToChesthang, buyReq)
                mallPropInfo.prop_info.num = buyNum
                table.insert(buyReq.props, mallPropInfo)
            end
        end
        --设置price
        ShopServer.ShopReqTotalPriceHandler[EShopOptType.Exchange](self, buyReq, shopItemExchangeInfoList)
        
        local fOnMutiExchangeCallback = function(res)
            loginfo('ShopServer:DoExchangeMutiPropReq', shopItemExchangeInfoList, fCallbackIns)
            ---@type MultiPropMsg
            local multiPropMsg = {}
            if res.result == 0 then
                multiPropMsg = {
                    bSuccess = true,
                    result = res.result,
                    shopItemBuyInfoList = shopItemExchangeInfoList,
                    prop_changes = res.prop_changes,
                }
            else
                multiPropMsg.bSuccess = false
                multiPropMsg.result = res.result
            end
            self:HandleMultiPropFinished(multiPropMsg, fCallbackIns, bAlwaysShowFullTip, true)
        end
        buyReq:Request(fOnMutiExchangeCallback, {bShowErrTip = bShowErrTip})
    else
        multiPropMsg.bSuccess = false
        self:HandleMultiPropFinished(multiPropMsg, fCallbackIns, bAlwaysShowFullTip, true)
    end

end

--- 处理商店单商品购买完成的情况
function ShopServer:HandleMultiPropFinished(multiPropMsg, fCallbackIns, bAlwaysShowFullTip, bExchange)
    if multiPropMsg.bSuccess then
        local shopItemBuyInfoList = multiPropMsg.shopItemBuyInfoList
        local nameStr = ""
        local nameStrTable = {}
        local bHasAutoEquiped = false
        if shopItemBuyInfoList and next(shopItemBuyInfoList) then
            for _, shopItemInfo in ipairs(shopItemBuyInfoList) do
                local shopItem = shopItemInfo.shopItem
                local buyNum = shopItemInfo.curBuyNum
                local targetSlotType = shopItemInfo.targetSlotType
                local bBuyAndEquip = ItemHelperTool.IsBuyToSlotType(shopItemInfo.targetSlotType)
                if bBuyAndEquip or bAlwaysShowFullTip then
                    table.insert(nameStrTable, tostring(shopItem.item.name))
                end
            end
            nameStrTable = table.unique(nameStrTable, true)
            nameStr = table.concat(nameStrTable,"、")
            if #shopItemBuyInfoList == 1 then
                local serverTip = bExchange and ServerTipCode.MultiExchangeSingleSuccess or ServerTipCode.MultiBuySingleSuccess
                LuaGlobalEvents.evtServerShowTip:Invoke(string.format(serverTip, nameStr))
            else
                nameStr = "" -- 不一一列举
                local serverTip = bExchange and ServerTipCode.MultiExchangeSuccess or ServerTipCode.MultiBuySuccess
                LuaGlobalEvents.evtServerShowTip:Invoke(string.format(serverTip, nameStr))
            end
            if bExchange then
                self:_FetchGoodsLimit()
            end
        end
    else
        if multiPropMsg.result == Err.DepositSpaceNotEnough then
            if bExchange then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.MultiExchangeNoSpaceFail)
            end
        else
            local serverTip = bExchange and ServerTipCode.MultiExchangeFail or ServerTipCode.MultiBuyFail
            LuaGlobalEvents.evtServerShowTip:Invoke(serverTip)
        end
        self:TryCompareShopCdnVer()
    end
    if fCallbackIns then
        fCallbackIns(multiPropMsg)
        fCallbackIns = nil
    end
end

function ShopServer:CheckEnoughBuyMultiProp(shopItemBuyInfoList)
    local currencyType2CostNum = {}
    local bEnoughBuy = true
    local needCurrencyTypes = {}
    for _, shopItemInfo in ipairs(shopItemBuyInfoList) do
        local shopItem = shopItemInfo.shopItem
        local buyNum = shopItemInfo.curBuyNum

        local currencyClientType = self:GetClientCurrencyTypeById(shopItem.item.id)
        declare_if_nil(currencyType2CostNum, currencyClientType, 0)
        currencyType2CostNum[currencyClientType] = currencyType2CostNum[currencyClientType] + shopItem:GetShopPropBuyPrice() * buyNum
    end

    if next(currencyType2CostNum) then
        for currencyClientType, totalBuyPrice in pairs(currencyType2CostNum) do
            if totalBuyPrice > Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType) then
                table.insert(needCurrencyTypes, currencyClientType)
            end
        end
    else
        for _, currencyClientType in pairs(ECurrencyClientType) do
            currencyType2CostNum[currencyClientType] = 0
        end
    end
    if #needCurrencyTypes > 0 then
        bEnoughBuy = false
    end
    --- 是否不足，不足的货币类型列表，所需的货币类型-数目键值对
    return bEnoughBuy, needCurrencyTypes, currencyType2CostNum
end

--------------------------------------------------------------------------
--- 购买指定propinfo的枪支（单个武器商品）
--------------------------------------------------------------------------
---@class AssemblyPropMsg
---@field bSuccess boolean
---@field assemblyItem ItemBase
---@field targetSlotType ESlotType
---@field result res.result
---@field prop_changes res.prop_changes

--- 请勿直接调用此方法，使用ShopModule:DoAssemblyWeaponProp(assemblyItem, targetSlotType)
---@param assemblyItem ItemBase
---@param targetSlotType SlotType
function ShopServer:DoBuyAssemblyWeaponReq(assemblyItem, targetSlotType, fCallbackIns)
    local weaponFeature = assemblyItem:GetFeature(EFeatureType.Weapon)
    assertlog(weaponFeature, 'DoBuyAssemblyWeaponReq only can be used for weapon')
    local req = pb.CSMallBuyPlayerDesignWeaponReq:New()
    ---@type pb_PropInfo gun_prop
    req.gun_prop = assemblyItem:GetRawPropInfo()
    ---@type pb_PropPrice[] total_prices
    local buyPrice, currencyClientType = self:GetShopBuyPriceByItem(assemblyItem)
    local prices = self:GenTotalPricesForReq(buyPrice)
    req.total_prices = prices
    req.posID = targetSlotType
    local fOnBuyDesignWeaponCallback = function(res)
        ---@type AssemblyPropMsg
        local assemblyPropMsg = {
            assemblyItem = assemblyItem,
            targetSlotType = targetSlotType,
            result = res.result,
        }
        if res.result == 0 then
            logwarning('[ ArmedForce ]-------------DoBuyAssemblyWeaponReq Success')
            assemblyPropMsg.bSuccess = true
            assemblyPropMsg.prop_changes = res.change.prop_changes
        else
            assemblyPropMsg.bSuccess = false
        end
        self:HandleAssemblyPropFinished(assemblyPropMsg, fCallbackIns)
    end
    req:Request(fOnBuyDesignWeaponCallback)
end


function ShopServer:HandleAssemblyPropFinished(assemblyPropMsg, fCallbackIns)
    local nameStr = assemblyPropMsg.assemblyItem.name
    if assemblyPropMsg.bSuccess then
        LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.AssemblyBuySuccess, nameStr))
    else
        LuaGlobalEvents.evtServerShowTip:Invoke(string.format(ServerTipCode.AssemblyBuyFail, nameStr))
        self:TryCompareShopCdnVer()
    end
    if fCallbackIns then
        fCallbackIns(assemblyPropMsg)
        fCallbackIns = nil
    end
end
--------------------------------------------------------------------------
--- 商品SItem获取
--------------------------------------------------------------------------

--- 通过Id获取要[交易]的商品（id可能对应多个商品)
---@param id number item.id
function ShopServer:GetShopItemListById(id, fOptionalFilterFunc)
    local shopItemList = {}
    if self._mapID2ExchangeIdList and next(self._mapID2ExchangeIdList) then
        local exchangeIdList = self._mapID2ExchangeIdList[id]
        if exchangeIdList then
            for _, exchangeId in ipairs(exchangeIdList) do
                if self._mapExchangeID2SItem then
                    local shopItem = self._mapExchangeID2SItem[exchangeId]
                    if shopItem then
                        if fOptionalFilterFunc then
                            if fOptionalFilterFunc(shopItem) then
                                table.insert(shopItemList, shopItem)
                            end
                        else
                            table.insert(shopItemList, shopItem)
                        end
                    end
                end
            end
        end
    end
    return shopItemList
end

--- 通过Id获取要[购买]的商品（当同一Id对应直购ExchangeId唯一时，此接口生效）
function ShopServer:GetPurchaseShopItemById(id)
    --快速搜索
    if self._mapID2ExchangeIdList and next(self._mapID2ExchangeIdList) then
        local exchangeIdList = self._mapID2ExchangeIdList[id]
        if exchangeIdList then
            for _, exchangeId in ipairs(exchangeIdList) do
                local shopItem = self._mapExchangeID2SItem[exchangeId]
                if shopItem and shopItem:GetExchangeType() == EShopOptType.Purchase then
                    return shopItem
                end
            end
        end
    end
    logwarning('ShopServer:GetPurchaseShopItemById无法找到[itemId]为',id,'的商品')
    return nil
end

--- 通过Id获取要[兑换]的商品列表
function ShopServer:GetExchangeShopItemListById(id)
    local exchangeSItemList = {}
    if self._mapID2ExchangeIdList and next(self._mapID2ExchangeIdList) then
        local exchangeIdList = self._mapID2ExchangeIdList[id]
        if exchangeIdList then
            for _, exchangeId in ipairs(exchangeIdList) do
                local shopItem = self._mapExchangeID2SItem[exchangeId]
                if shopItem and shopItem:GetExchangeType() == EShopOptType.Exchange then
                    table.insert(exchangeSItemList, shopItem)
                end
            end
        end
    end
    return exchangeSItemList
end

function ShopServer:GetShopItemByExchangeId(exchangeId)
    if self._mapExchangeID2SItem[exchangeId] then
        return self._mapExchangeID2SItem[exchangeId]
    end

    -- logwarning('无法找到[exchangeId]为',exchangeId,'的商品')
    return nil
end

function ShopServer:GetSItemDetailStructByExchangeId(exchangeId)
    -- if self._mapExchangeId2SItemDetail then
    --     for _, SItemDetail in pairs(self._mapExchangeId2SItemDetail) do
    --         if SItemDetail.exchangeId == exchangeId then
    --             return SItemDetail
    --         end
    --     end
    -- end
    return self:GetShopItemByExchangeId(exchangeId)
end

function ShopServer:GetShopItemByQuestId(questId)
    local questShopItems = {}
    for _,shopItem in pairs(self.mallShopItemList) do
        if shopItem.unlockQuestId ~= 0 and shopItem.unlockQuestId == questId then
            table.insert(questShopItems, shopItem)
        end
    end
    return questShopItems
end

--- 通过ExchangeId获取要[购买]的商品（ExchangeId作为商品主键，常用此接口）
function ShopServer:GetPurchaseShopItemByExchangeId(exchangeId)
    if self._mapExchangeID2SItem[exchangeId] and self._mapExchangeID2SItem[exchangeId]:GetExchangeType() == EShopOptType.Purchase then
        return self._mapExchangeID2SItem[exchangeId]
    end
    
    for _,shopItem in ipairs(self.mallShopItemList) do
        if shopItem.exchangeId == exchangeId and shopItem:GetExchangeType() == EShopOptType.Purchase then
            local shopItem = shopItem
            return shopItem
        end
    end
    for _,shopItem in ipairs(self.curMixShopItems) do
        if shopItem.exchangeId == exchangeId and shopItem:GetExchangeType() == EShopOptType.Purchase then
            local shopItem = shopItem
            return shopItem
        end
    end
    -- logwarning('无法找到[exchangeId]为',exchangeId,'的商品')
    return nil
end


--- 通过Id判断是否可以在商店购买/兑换，且返回原因
---@param id number item.id
---@return bPass boolean 可购买/兑换（总结论）
---@return bCanBuyOrExchange boolean 存在于商店可购买/兑换列表
---@return hasAnyUnlockItem boolean 是否存在未解锁的项
---@return hasAnyNotSufficientItem boolean 是否存在不足够的项
function ShopServer:CheckShopItemListById(id)
    local bCanBuyOrExchange = false
    local hasAnyUnlockItem = false
    local hasAnyNotSufficientItem = false
    if self._mapID2ExchangeIdList and next(self._mapID2ExchangeIdList) then
        local exchangeIdList = self._mapID2ExchangeIdList[id]
        if exchangeIdList then
            for _, exchangeId in ipairs(exchangeIdList) do
                local shopItem = self._mapExchangeID2SItem[exchangeId]
                if shopItem then
                    bCanBuyOrExchange = true
                    local isUnlock = shopItem:GetIsUnlock()
                    local isSufficient = shopItem:GetIsSufficient()
                    -- 核心判断：存在解锁且足够的项，直接返回成功
                    if isUnlock and isSufficient then
                        return true, true, true, true
                    end
                    -- 记录是否存在未解锁的项
                    if not isUnlock then
                        hasAnyUnlockItem = true
                    end
                    -- 记录是否存在不足够的项
                    if not isSufficient then
                        hasAnyNotSufficientItem = true
                    end
                end
            end
        end
    end
    if not bCanBuyOrExchange then
        return false, false, false, false  -- 未找到对应ID的项
    end
    return false, true, hasAnyUnlockItem, hasAnyNotSufficientItem
end

--- 通过Id获取商店直售价/判断是否可以在商店直购
---@param id number item.id
function ShopServer:CheckItemPurchasePriceById(id)
    local shopItem = self:GetPurchaseShopItemById(id)
    if shopItem then
        return shopItem:GetShopPropBuyPrice()
    end
    return nil
end

function ShopServer:TryGetShopItemByItem(item, bIgnoreEnoughBuy, bIgnoreSpaceEnough)
    local findShopItem
    local shopItem = self:GetPurchaseShopItemById(item.id)
    if shopItem and self:CheckShopItemBuyableWithReason(shopItem, 1, nil, bIgnoreEnoughBuy) and ItemBaseTool.CompareItem(shopItem.item, item) then
        findShopItem = shopItem
        return findShopItem
    else
        local fOptionalFilterFunc = SafeCallBack(function(shopItem)
            local bCanBuy = self:CheckShopItemBuyableWithReason(shopItem, 1, nil, bIgnoreEnoughBuy, bIgnoreSpaceEnough)
            local bPurchase = (shopItem:GetExchangeType() == EShopOptType.Purchase)
            return bCanBuy and bPurchase
        end)
        local shopItemList = self:GetShopItemListById(item.id, fOptionalFilterFunc)
        for k, v in ipairs(shopItemList) do
            if ItemBaseTool.CompareItem(v.item, item) then
                findShopItem = v
                return findShopItem
            end
        end
    end
    return findShopItem
end

--- 获取全部商品（默认为直购）
function ShopServer:GetShopItems()
    local curTypeShopItems = {}
    for _,shopItem in ipairs(self.mallShopItemList) do
        if self:CheckExchangeTypeEnable(shopItem:GetExchangeType()) and self:CheckItemIdIsOnSell(shopItem.item.id) then
            table.insert(curTypeShopItems, shopItem)
        end
    end
    return curTypeShopItems
end

--- 获取全部指定MainType商品（默认为直购）
function ShopServer:GetShopItemsByMainType(curMainType)
    local curTypeShopItems = {}
    for _,shopItem in ipairs(self.mallShopItemList) do
        if self:CheckExchangeTypeEnable(shopItem:GetExchangeType()) and self:CheckItemIdIsOnSell(shopItem.item.id) then
            local itemMainType = ItemHelperTool.GetMainTypeById(shopItem.item.id)
            if itemMainType == curMainType then
                table.insert(curTypeShopItems, shopItem)
            end
        end
    end
    return curTypeShopItems
end

function ShopServer:GetShopItemsByItermID(itemID)
    local curTypeShopItems = {}
    for _,shopItem in ipairs(self.mallShopItemList) do
        if self:CheckExchangeTypeEnable(shopItem:GetExchangeType()) and self:CheckItemIdIsOnSell(shopItem.item.id) then
            local itemMainType = ItemHelperTool.GetMainTypeById(shopItem.item.id)
            if shopItem.item.id == itemID then
                table.insert(curTypeShopItems, shopItem)
            end
        end
    end
    return curTypeShopItems
end


--- 获取SlotType对应的商品（默认为直购）
function ShopServer:GetShopItemsForSlotType(slotType)
    local mapSlotType2MainTypeList = {
        [ESlotType.MainWeaponLeft] = { [EItemType.Weapon] = {}, [EItemType.Receiver] = {} },
        [ESlotType.MainWeaponRight] = { [EItemType.Weapon] = {}, [EItemType.Receiver] = {} },
        [ESlotType.Medicine] = { [EItemType.Medicine] = {} },
        [ESlotType.Helmet] = { [EItemType.Equipment] = {EEquipmentType.Helmet} },
        [ESlotType.BreastPlate] = { [EItemType.Equipment] = {EEquipmentType.BreastPlate} },
        [ESlotType.ChestHanging] = { [EItemType.Equipment] = {EEquipmentType.ChestHanging} },
        [ESlotType.Bag] = { [EItemType.Equipment] = {EEquipmentType.Bag} },
        [ESlotType.SafeBox] = { [EItemType.Equipment] = {EEquipmentType.SafeBox} },
        [ESlotType.BulletLeft] = { [EItemType.Bullet] = {} },
        [ESlotType.BulletRight] = { [EItemType.Bullet] = {} },
    }

    loginfo("ShopServer:GetShopItemsForSlotType 开始获取SlotType="..slotType)
    local curTypeShopItems = {}
    if mapSlotType2MainTypeList[slotType] then  
        for _,shopItem in ipairs(self.mallShopItemList) do
            local itemMainType = ItemHelperTool.GetMainTypeById(shopItem.item.id)
            local validSubTypes = mapSlotType2MainTypeList[slotType][itemMainType]
            if validSubTypes then
                --loginfo("ShopServer:GetShopItemsForSlotType validSubTypes=["..slotType.."]["..itemMainType.."] name="
                --    ..shopItem.item.name.." order="..shopItem.order)
                local itemSubType = nil
                if table.nums(validSubTypes) == 0 then
                    itemSubType = 0
                else
                    itemSubType = ItemHelperTool.GetSubTypeById(shopItem.item.id)
                    if not table.contains(validSubTypes, itemSubType) then
                        itemSubType = nil
                    end
                end
                if itemSubType then
                    local bIsOnSell = self:CheckItemIdIsOnSell(shopItem.item.id)
                    local curShopItemOptType = shopItem:GetExchangeType()
                    loginfo("ShopServer:GetShopItemsForSlotType itemSubType="..itemSubType.." 商品支持的购买方式=" ..curShopItemOptType.." 商品"..(bIsOnSell and "在" or "不在").."售")
                    if curShopItemOptType == EShopOptType.Purchase and bIsOnSell then
                        table.insert(curTypeShopItems, shopItem)
                        loginfo("ShopServer:GetShopItemsForSlotType 符合条件 加入列表", shopItem.item.name, shopItem.exchangeId)
                    end
                end
            end
        end
        --loginfo("ShopServer:GetShopItemsForSlotType 获取SlotType="..slotType.."对应的商品完成 数量="..#curTypeShopItems)
    else
        logerror('ShopServer:GetShopItemsForSlotType(slotType) slotType 未对应 itemType')
    end
    return curTypeShopItems
end

--- 获取可能配装推荐的商品（默认为直购）
function ShopServer:GetShopItemsForOutfit()
    local validTypes = {
        [EItemType.Weapon] = {},
        [EItemType.Bullet] = {},
        [EItemType.Equipment] = {
            EEquipmentType.Helmet,
            EEquipmentType.BreastPlate,
            EEquipmentType.ChestHanging,
            EEquipmentType.Bag,
            EEquipmentType.SafeBox
        },
        [EItemType.Medicine] = {},
        [EItemType.Receiver] = {}
    }

    local curTypeShopItems = {}
    for _,shopItem in ipairs(self.mallShopItemList) do
        local itemMainType = ItemHelperTool.GetMainTypeById(shopItem.item.id)
        local validSubTypes = validTypes[itemMainType]
        if validSubTypes then
            local itemSubType = nil
            if table.nums(validSubTypes) == 0 then
                itemSubType = 0
            else
                itemSubType = ItemHelperTool.GetSubTypeById(shopItem.item.id)
                if not table.contains(validSubTypes, itemSubType) then
                    itemSubType = nil
                end
            end
            if itemSubType then
                if shopItem:GetExchangeType() == EShopOptType.Purchase and self:CheckItemIdIsOnSell(shopItem.item.id) and shopItem:GetIsUnlock() then
                    curTypeShopItems[itemMainType] = curTypeShopItems[itemMainType] or {}
                    curTypeShopItems[itemMainType][itemSubType] = curTypeShopItems[itemMainType][itemSubType] or {}
                    table.insert(curTypeShopItems[itemMainType][itemSubType], shopItem)
                end
            end
        end
    end
    return curTypeShopItems
end

function ShopServer:GetShopItemsForExchange()
    local bFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
    local exchangeShopItems = {}
    if bFrontEnd then --局外
        for _, shopItem in ipairs(self.mallShopItemList) do
            if shopItem:GetExchangeType() == EShopOptType.Exchange then
                table.insert(exchangeShopItems, shopItem)
            end
        end
    else
        -- if self._mapExchangeId2SItemDetail and #self._mapExchangeId2SItemDetail > 0 then
        --     for _, shopItemDetial in pairs(self._mapExchangeId2SItemDetail) do
        --         if shopItemDetial:GetExchangeType() == EShopOptType.Exchange then
        --             table.insert(exchangeShopItems, shopItemDetial)
        --         end
        --     end
        -- end
    end

    return exchangeShopItems
end

function ShopServer:GeUnlockShopItemMapByPrestigeLvUp(mapPid2LvChange)
    local upgradedMids = {}
    for prestigeId, lvChange in pairs(mapPid2LvChange) do
        local mid = self._mapPrestigeId2Mid[prestigeId]
        if mid then
            upgradedMids[mid] = lvChange
        end
    end
    local mapMid2UnlockedShopItems = {}
    local mid2MerchantStruct = {}
    for _, shopItem in ipairs(self.mallShopItemList) do
        local mid = shopItem.merchantId
        local merchantStruct = mid2MerchantStruct[mid] or self:GetMerchantStructById(mid)
        declare_if_nil(mid2MerchantStruct, mid, merchantStruct)
        local lvChange = upgradedMids[mid]
        if lvChange then
            if shopItem.unlockLv > lvChange.oldLv and shopItem.unlockLv <= lvChange.newLv then
                declare_if_nil(mapMid2UnlockedShopItems, mid, {})
                table.insert(mapMid2UnlockedShopItems[mid], shopItem)
            end
        end
    end
    for mid, lvChange in pairs(upgradedMids) do
        if mapMid2UnlockedShopItems[mid] == nil then
            mapMid2UnlockedShopItems[mid] = {}
        end
    end
    return mapMid2UnlockedShopItems
end
--------------------------------------------------------------------------
--- 商品SItem购买检查
--------------------------------------------------------------------------
--- 是否上架购买， 给除商店外使用，不包含兑换
---@param itemId number item.id
function ShopServer:CheckItemIdIsOnSell(itemId)
    local bOnSell = self._mapItemId2IsOnSell[itemId] or false
    return bOnSell
end

--- 是否有购买方式，给商店内部使用，包含兑换等等
---@param id number item.id
function ShopServer:CheckItemIdIsExchangeAble(id)
    local shopItemList = self:GetShopItemListById(id)
    local bExchangeAble = #shopItemList > 0
    return bExchangeAble
end

--- 是否可以购买
---@param shopItem ShopItemStruct
---@param curBuyNum number
function ShopServer:CheckShopItemBuyableWithReason(shopItem, curBuyNum, targetSlotType, bIgnoreEnoughBuy, bIgnoreSpaceEnough)
    curBuyNum = setdefault(curBuyNum, 1)
    local bCanBuy
    local reason
    if shopItem and self:CheckItemIdIsExchangeAble(shopItem.item.id) then
        if shopItem:GetIsUnlock() then
            if shopItem:GetIsSufficient() then
                local totalPrice = shopItem:GetShopPropBuyPrice() * curBuyNum
                local bEnoughBuy = shopItem:GetIsCostEnough(curBuyNum)
                if bEnoughBuy or bIgnoreEnoughBuy then
                    local bSpaceEnough
                    if not bIgnoreSpaceEnough then
                    --判断当前商品类型,如果购买的藏品道具,则这里不判断slot
                        if not ShopHelperTool.CheckSItemBelongToCollection(shopItem) then
                            local slots
                            if targetSlotType then
                                slots = {}
                                slots[1] = Server.InventoryServer:GetSlot(targetSlotType)
                                local equipItem = slots[1]:GetEquipItem()
                                -- 需要检查卸下的装备，仓库是否放得下
                                if equipItem then
                                    bSpaceEnough = Server.InventoryServer:GetItemAvaiableDepositSlot(equipItem) --装备确实这样判断就足够了
                                end
                            else
                                slots = Server.InventoryServer:GetAllItemAvaiableDepositSlots(shopItem.item)
                            end
                            if not bSpaceEnough then
                                if not slots then
                                    bSpaceEnough = false
                                else
                                    local totalNum = 0
                                    for _, slot in ipairs(slots) do
                                        local num = slot:CalNumOfItemCanPlaced(shopItem.item.id)
                                        totalNum = totalNum + num
                                    end
                                    -- if shopItem.item:IsBullet() then
                                    --     curBuyNum = math.floor(curBuyNum / shopItem.item.maxStackCount)                                
                                    -- end
                                    local occupySpace = 1
                                    if shopItem.GetCurOccuSpace then
                                        occupySpace = shopItem:GetCurOccuSpace(curBuyNum)
                                    end
                                    if totalNum and totalNum >= occupySpace then
                                        bSpaceEnough = true
                                    else
                                        bSpaceEnough = false
                                    end
                                end
                            end
                        else
                            bSpaceEnough = true
                        end
                        if shopItem.item and (shopItem.item.itemMainType == EItemType.WeaponSkin or shopItem.item.itemMainType == EItemType.CollectionProp) then bIgnoreSpaceEnough = true end
                        if bSpaceEnough then
                            bCanBuy = true
                            reason = ECantBuyReason.None
                        else
                            bCanBuy = false
                            reason = ECantBuyReason.NoSpace
                        end
                    else
                        bCanBuy = true
                        reason = ECantBuyReason.None
                    end
                else
                    bCanBuy = false
                    reason = ECantBuyReason.NotEnoughCost
                end
            else
                bCanBuy = false
                reason = ECantBuyReason.SellOut
            end
        else
            bCanBuy = false
            reason = ECantBuyReason.Locked
        end
    else
        bCanBuy = false
        reason = ECantBuyReason.NotOnSell
    end
    return bCanBuy, reason
end

function ShopServer:CheckItemIdBuyableWithReason(itemId, bIgnoreEnoughBuy)
    local shopItem = self:GetPurchaseShopItemById(itemId)
    return self:CheckShopItemBuyableWithReason(shopItem, 1, nil, bIgnoreEnoughBuy)
end

---@param item ItemBase
function ShopServer:CheckItemBuyable(item, bIgnoreEnoughBuy)
    local bFinalCanBuy = true
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsWeapon() then
        local function CheckBuyableFromPropInfo(propInfo, bIgnoreEnoughBuy)
            if not propInfo.num then
                loginfo("propInfo num should not be zero, id = ", propInfo.id)
                return 0
            end
            local bCanBuy = true
            --- 此处虚空物品跳过检查
            if self:CheckIfHasPrice(propInfo.id) then
                bCanBuy = self:CheckItemIdBuyableWithReason(propInfo.id, bIgnoreEnoughBuy)
            end
            -- loginfo("[ ArmedForce ]-------------------check itemid buyable ", ItemConfigTool.GetItemConfigById(propInfo.id).Name, propInfo.id, bCanBuy, '是否为虚空物品:', not self:CheckIfHasPrice(propInfo.id))
            for _, component in ipairs(propInfo.components) do
                if bCanBuy then
                    bCanBuy = bCanBuy and CheckBuyableFromPropInfo(component.prop_data, bIgnoreEnoughBuy)
                else
                    break
                end
            end
            return bCanBuy
        end

        local function CheckBuyableFromRawDescParts(rawDesParts, bIgnoreEnoughBuy)
            local bCanBuy = self:CheckItemIdBuyableWithReason(item.id, bIgnoreEnoughBuy)
            for key, part in pairs(rawDesParts) do
                if not bCanBuy then
                    bCanBuy = bCanBuy and self:CheckItemIdBuyableWithReason(part.ItemId, bIgnoreEnoughBuy)
                else
                    break
                end
            end
        end
        -- 注：Components里面是没有机匣的数据的
        if item.rawPropInfo then
            bFinalCanBuy = CheckBuyableFromPropInfo(item.rawPropInfo, bIgnoreEnoughBuy)
        elseif item.RawDescObj then
            bFinalCanBuy = CheckBuyableFromRawDescParts(item.RawDescObj:GetAllParts(), bIgnoreEnoughBuy)
        end
    else
        bFinalCanBuy = self:CheckItemIdBuyableWithReason(item.id, bIgnoreEnoughBuy)
    end
    loginfo("[ ArmedForce ]-------------------check [item] buyable ", item.name, item.id, bFinalCanBuy)
    return bFinalCanBuy
end

---only Buy
function ShopServer:GetItemMallProp(itemId)
    local shopItem = self:GetPurchaseShopItemById(itemId)
    if shopItem then
        return shopItem:GetRawProp()
    else
        return false
    end
end

--------------------------------------------------------------------------
--- 生成局内详情页数据
--------------------------------------------------------------------------
---@class ItemDetailInfo
---@field exchangeId number
---@field merchantId number
---@field itemName string
---@field isUnlock bool
---@field priceItem PriceItem (包含isCostEnough,GetItemCostList)
---@field isSufficient bool
---@field unlockLvText string

function ShopServer:GenerateInGameItemDetailInfo()
    self._mapExchangeId2SItemDetail = {}
    if self._mapExchangeID2SItem then
        for exchangeId, sItem in pairs(self._mapExchangeID2SItem) do
            local sItemDetailStruct = ShopItemDetailStruct:NewIns(sItem)
            -- self._mapExchangeId2SItemDetail[exchangeId] = sItemDetailStruct
            table.insert(self._mapExchangeId2SItemDetail, sItemDetailStruct)
        end
    end
end

function ShopServer:_ReadItemId2RecyclePrice(itemID, bMallRecycle, dynamicGuidePrice, zeroRecyclePrice, currencyClientType)
    if bMallRecycle then
        if Facade.GameFlowManager:CheckIsInFrontEnd() then
            dynamicGuidePrice = self._mapItemId2MallRecycle[itemID]
            -- zeroRecyclePrice = recyclePriceData[EItemId2RecyclePriceID.mallZeroNum]
        end
    else
        if self._mapItemId2DynamicGuidePrice[itemID] then
            dynamicGuidePrice = self._mapItemId2DynamicGuidePrice[itemID]
        else
            logerror('ShopServer:_ReadItemId2RecyclePrice 指定道具没有动态指导价', itemID)
        end
        -- zeroRecyclePrice = priceConfig[EItemId2RecyclePriceID.zeroNum]
    end
    local priceConfig = self._mapItemId2PriceConfig[itemID]
    currencyClientType = priceConfig[EItemId2RecyclePriceID.currencyClientType]
    if dynamicGuidePrice == nil then
        local config = ItemConfigTool.GetItemConfigById(itemID)
        if config and config.InitialGuidePrice then 
            dynamicGuidePrice = config.InitialGuidePrice
        else
            dynamicGuidePrice = 0
        end
        logerror('ShopServer:_ReadItemId2RecyclePrice itemID: ', itemID, ' dynamicGuidePrice is nil, read default price: ', dynamicGuidePrice)
    end
    return dynamicGuidePrice, zeroRecyclePrice, currencyClientType
end

---@param isOpen 是否开启对限购商品的更新检查
function ShopServer:SetLimitClicker(isOpen, bForceCheck)
    self._startLimitTick = isOpen
    self._bForceCheck = bForceCheck
    self._bForceCheck = setdefault(self._bForceCheck, false)
    if isOpen then
        self:FreshLimitGoods()
    end
end

function ShopServer:PrintTableLength(prefix, targetTable)
    prefix = setdefault(prefix, "")
    local count = 0
    if targetTable then
        for _, val in pairs(targetTable) do
            count = count + 1
        end
    end
    logerror('ShopServer:PrintTableLength '..prefix..' count = ',count)
end

return ShopServer