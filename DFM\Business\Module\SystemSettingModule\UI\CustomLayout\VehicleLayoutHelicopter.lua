----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



--直升机的自定义布局
---@class VehicleLayoutHelicopter
local VehicleLayoutHelicopter = ui("VehicleLayoutHelicopter")

function VehicleLayoutHelicopter:Ctor()
    self._wtThrottleSliderButton = self:Wnd("ThrottleSliderButton",UIWidgetBase)
    self._wtLeftVehicleJoystick = self:Wnd("LeftVehicleJoystick",UIWidgetBase)
    self._wtRightVehicleJoystick = self:Wnd("RightVehicleJoystick",UIWidgetBase)
    self._wtFlyModeBtn = self:Wnd("FlyModeBtn",UIWidgetBase)
    self._wtDragBtnHighOutputCancel = self:Wnd("DragBtnHighOutput_Cancel",UIWidgetBase)
end

function VehicleLayoutHelicopter:OnOpen()
    self._wtThrottleSliderButton:Collapsed()
    self._wtLeftVehicleJoystick:Collapsed()
    self._wtRightVehicleJoystick:Collapsed()
    self._wtFlyModeBtn:Collapsed()
    self._wtDragBtnHighOutputCancel:Collapsed()
end

function VehicleLayoutHelicopter:OnClose()
end

return VehicleLayoutHelicopter