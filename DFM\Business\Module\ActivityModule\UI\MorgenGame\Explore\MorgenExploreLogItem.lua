---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_MorgenGame_Item2
---@class MorgenExploreLogItem : LuaUIBaseView
local MorgenExploreLogItem = ui("MorgenExploreLogItem")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"

function MorgenExploreLogItem:Ctor()
    self._wtTimeText = self:Wnd("DFTextBlock_192", UITextBlock)
    self._wtResultText = self:Wnd("DFTextBlock_0", UITextBlock)
    self._wtRoundText = self:Wnd("DFTextBlock_143", UITextBlock)
    self._wtBorderText = self:Wnd("DFTextBlock", UITextBlock)
    self._wtCostText = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtBringText = self:Wnd("DFRichTextBlock_61", UITextBlock)

    self._wtAutoImage = self:Wnd("DFImage_110", UIImage)
end

function MorgenExploreLogItem:RefreshInfo(history)
	-- 0失败，1撤离，2中退，3通关
	self:SetType(history.result)

	local timeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(history.time, "YYMMDDHHMMSS")
    if self._wtTimeText ~= nil then
        self._wtTimeText:SetText(timeStr or "")
    end

    
	self._wtResultText:SetText(ActivityConfig.Loc.MogenResultText[history.result + 1])
    local roundIdx = history.cycle == 2 and 4 or 3
	self._wtRoundText:SetText(ActivityConfig.Loc.MogenRewardText[roundIdx])
	self._wtCostText:SetText(history.cost_san_num)
    
    local StoneImg = "ArknightsCurrenc1"
    local MoneyText = StringUtil.Key2StrFormat(Module.Activity.Config.Loc.CurrencyStr, {img = StoneImg, num = history.gain_money_num})
	self._wtBringText:SetText(MoneyText)
    
    if history.is_auto then
        self._wtAutoImage:SelfHitTestInvisible()
        self._wtBorderText:SetText(ActivityLogic.HandleLocalizeText(history.location))
    else
        self._wtAutoImage:Collapsed()

        local levelConfig = ConfigManager.GetLevelConfigTable()
        local curLevelInfo = levelConfig[history.level] or {}
        local levelName = ActivityLogic.HandleLocalizeText(curLevelInfo.levelName)
        local levelSubName = ActivityLogic.HandleLocalizeText(curLevelInfo.levelSubName)
        self._wtBorderText:SetText(levelName.."-"..levelSubName)
    end

end

function MorgenExploreLogItem:RefreshUI()
end

return MorgenExploreLogItem