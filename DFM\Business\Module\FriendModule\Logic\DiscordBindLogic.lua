----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMAccountBind)
----- LOG FUNCTION AUTO GENERATE END -----------



local DiscordBindLogic = {}

local UGameLogin = import "DFMGameLogin"
local UGameFriend = import "DFMGameFriend"
local GameFriendMgr = UGameFriend.Get(GetWorld())
local UGameLoginIns = nil
if UGameLogin then
    UGameLoginIns = UGameLogin.Get(GetGameInstance())
end

function DiscordBindLogic.InitDiscordBindLogic()
    if GameFriendMgr and IsBuildRegionGlobal() and not IsInEditor() then
        Module.Login.Config.Events.evtOnLoginSuccess :AddListener(DiscordBindLogic.QueryUserInfo)

        -- DiscordBindLogic.QueryUserInfo()
        UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Add(DiscordBindLogic.CheckDiscordBindInfo)
        GameFriendMgr.OnDiscordBaseResultNotifyEvent :Add(DiscordBindLogic.OnDiscordFriendAuthorizeResult)
    end
end

function DiscordBindLogic.DeInitDiscordBindLogic()
    if GameFriendMgr and IsBuildRegionGlobal() then
        UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Clear()
        GameFriendMgr.OnDiscordBaseResultNotifyEvent :Clear()
    end
end

-----------------------------------------------------------------------
--region 请求用户信息

--- 登陆后查询好友信息
function DiscordBindLogic.QueryUserInfo()
    return UGameLoginIns:QueryUserInfo()
end

function DiscordBindLogic.CheckDiscordBindInfo(tab)
    loginfo("DiscordBindLogic.QueryUserInfoCallback CheckDiscordBindInfo")
    logtable(tab)

    local bIsDiscordBinded = false
    local discordOpenId = 0
    if tab.bindList ~= "" then
        local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
        local Json = JsonFactory.createJson()
        local bindList = Json.decode(tab.bindList)
        if bindList then
            for _,bindInfo in pairs(bindList) do
                if bindInfo and bindInfo.channelid == EChannelType.kChannelDiscord then
                    loginfo("DiscordBindLogic.CheckDiscordBindInfo Discord Binded!")
                    bIsDiscordBinded = true
                    discordOpenId = bindInfo.openid -- nil
                end
            end
        end
    end
    Module.Friend.Field:SetIsDiscordBinded(bIsDiscordBinded)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Discord好友相关

--- 用户首次绑定Discord好友
function DiscordBindLogic.DiscordBindFriend()
    loginfo("DiscordBindLogic.DiscordBindFriend Manual Bind")
    return UGameFriend.DiscordAuthorize()
end

--- 检查Discord绑定状态
function DiscordBindLogic.CheckDiscordFriendBindState()
    local bIsDiscordBinded = Module.Friend.Field:GetIsDiscordBinded()
    local bIsFriendBinded  = Module.Friend.Field:GetIsDiscordFriendBinded()
    loginfo("DiscordBindLogic.CheckDiscordFriendBindState bIsDiscordBinded:", bIsDiscordBinded)
    loginfo("DiscordBindLogic.CheckDiscordFriendBindState bIsFriendBinded:", bIsFriendBinded)
    if bIsDiscordBinded and not bIsFriendBinded then
        loginfo("DiscordBindLogic.CheckDiscordFriendBindState Auto Bind")
        return UGameFriend.DiscordAuthorize()
    end
end

--- 接受Discord绑定好友信息回调
function DiscordBindLogic.OnDiscordFriendAuthorizeResult(method_id, ret_code, ret_msg, third_code, third_msg, extra_json)
    loginfo("DiscordBindLogic.OnDiscordBaseResult", ret_code)
    if ret_code == 0 then
        --- 获取绑定信息
        -- UGameLoginIns:QueryUserInfo()
        Module.Friend.Field:SetIsDiscordFriendBinded(true)

        --- she3.0由前台自行请求好友数据
        Server.FriendServer:SDKFetchDiscordFriendByClient(1)
    else
        Module.Friend.Field:SetIsDiscordFriendBinded(false)
    end
end

--- She3.0暂未启用
--- 获取用户的绑定信息，主要是为了Discord的openid
function DiscordBindLogic.OnQueryUserInfo(ret)
    local tab = LoginUtil.ParseLoginRet(ret)
    local bIsDiscordBinded = false
    local discordOpenId = 0
    if tab.bindList then
        local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
        local Json = JsonFactory.createJson()
        local bindList = Json.decode(tab.bindList)
        if bindList then
            for _,bindInfo in pairs(bindList) do
                if bindInfo and bindInfo.channelid == EChannelType.kChannelDiscord then
                    bIsDiscordBinded = true
                    discordOpenId = bindInfo.openid
                end
            end
        end
    end

    if bIsDiscordBinded then
        --- 告知服务器openid
        Server.FriendServer:SentDiscordFriendBinded(discordOpenId)
    end

    Module.Friend:SetIsDiscordBinded(bIsDiscordBinded)
end

--endregion
-----------------------------------------------------------------------

return DiscordBindLogic