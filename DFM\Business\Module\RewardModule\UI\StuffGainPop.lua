----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class StuffGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local StuffGainPop = ui("StuffGainPop", RewardBaseView)
local EGPInputModeType = import "EGPInputModeType"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CommonVideoComponent = require "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoComponent"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"
local ThemeHelperTool = require "DFM.StandaloneLua.BusinessTool.ThemeHelperTool"
local FAnchors = import "Anchors"

function StuffGainPop:Ctor()
    self._wtRewardDetail = self:Wnd("wtRewardDetail", RewardDetail)
    self._wtRewardDetail:BindJumpClick(self._OnSkipBtnClick, self)
    self._wtRewardDetail:SetShowSkipTxt(true)
    self._wtBgImg = self:Wnd("wtBgImg", UIImage)
    self._wtCollaborationTag = self:Wnd("wtCollaborationTag", UIWidgetBase) 
    self._wtCardImg = self:Wnd("wtCardImg", UIImage)
    self._wtHeroIcon = self:Wnd("wtHeroIcon", UIImage)
    self._wtVideoPlayer = self:Wnd("wtVideoPlayer", CommonVideoComponent)
    self._wtVideoPlayer:InitComponent(false)
    self._wtSprayPaintImg = self:Wnd("wtSprayPaintImg", UIImage)
    self._wtProfilePhoto = self:Wnd("wtProfilePhoto", UIImage)
    self._wtBrandImg = self:Wnd("wtBrandImg", UIImage)
    self._wtOrnamentImg = self:Wnd("wtOrnamentImg", UIImage)
    self._wtDownloadPanel = self:Wnd("wtDownloadPanel", UIWidgetBase)
    self._closeClickCount = -1
end


function StuffGainPop:OnInitExtraData(type, itemId)
    self._type = type or 0
    self._itemId = itemId
end

function StuffGainPop:OnOpen()
    self:_AddListeners()
    Module.Guide:SendMsg(EGuideMsgSig.RewardStuffGainPop, "OnOpen")
end

function StuffGainPop:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtDownloadPanel)
    self._wtVideoPlayer:Stop()
    Module.Guide:SendMsg(EGuideMsgSig.RewardStuffGainPop, "OnClose")
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function StuffGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

function StuffGainPop:OnHide()
    self._wtVideoPlayer:Stop()
end

function StuffGainPop:OnShowBegin()
    self:_RefreshWidget()
    self._wtRewardDetail:AddJumpInputAction()
end

function StuffGainPop:OnAnimFinished(anim)
    if anim == self.WBP_Hero_ShowHero_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_Hero_ShowHero_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end

function StuffGainPop:_AddListeners()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._OnDownloadResult,self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
end

function StuffGainPop:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
        Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
        self:PlayAnimation(self.WBP_Hero_ShowHero_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_Hero_ShowHero_in)
        self._wtRewardDetail:SkipInAnimation()
    end
end

function StuffGainPop:_RefreshWidget()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    self._wtRewardDetail:SetType(2)
    self._wtRewardDetail:SetDesc(Module.Reward.Config.Loc.Universal)
    local themeID = ThemeHelperTool.TryGetItemMapThemeID(self._itemId)
    local itemThemeResRow
    if themeID then
        itemThemeResRow = ThemeHelperTool.TryGetItemThemeResRow(themeID, tostring(self._itemId))
    end
    if self._type ~= nil then
        if itemThemeResRow then
            self._wtBgImg:AsyncSetImagePath(itemThemeResRow.RewardBgImage)
        else
            self:SetDefaultBgImg(self._type)
        end
    end
    self:_RefreshResource()
    self:_RefreshDownloadBtn()
    self._wtCollaborationTag:SetVisibility((itemThemeResRow and self._type ~= nil) and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
    self:SetStyleAndType(itemThemeResRow and 1 or 0, self._type)

    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_Hero_ShowHero_in, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
    Facade.SoundManager:PlayUIAudioEvent("UI_Common_Popup")
    self:HandleTransition(false)
end

function StuffGainPop:_RefreshResource()
    if self._type ~= nil then
        --干员名片
        if self._type == 0 then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainHeroBusinessCard)
            local heroCardDataTable = HeroHelperTool.GetHeroCardData()
            local heroCardRow = heroCardDataTable[self._itemId]
            if heroCardRow then
                self._wtRewardDetail:SetQuality(heroCardRow.CardLevel)
                self._wtRewardDetail:SetName(heroCardRow.CardName)
                self._wtCardImg:AsyncSetImagePath(heroCardRow.CardPath)
                for index, heroId in ipairs(heroCardRow.BelongedHeroIDs) do
                    local heroData = HeroHelperTool.GetHeroProto(heroId)
                    if heroData then
                        self._wtRewardDetail:SetDesc(heroData.Name)
                        break
                    end
                end
            end
        --台词
        elseif self._type == 1 then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainKillLine)
            local heroLineDataTable = HeroHelperTool.GetAllLinesData()
            local heroLineRow = heroLineDataTable[self._itemId]
            if heroLineRow then
                self._wtRewardDetail:SetQuality(heroLineRow.LinesLevel)
                self._wtRewardDetail:SetName(heroLineRow.LinesName)
                for index, heroId in ipairs(heroLineRow.BelongedHeroIDs) do
                    local heroData = HeroHelperTool.GetHeroProto(heroId)
                    if heroData then
                        self._wtHeroIcon:AsyncSetImagePath(heroData.Icon)
                        self._wtRewardDetail:SetDesc(heroData.Name)
                        --播放语音
                        Module.Hero:StopPlayHero2DVoice()
                        Module.Hero:PlayHero2DVoice(heroId, heroLineRow.LinesRowName)
                        break
                    end
                end
            end
        --喷漆
        elseif self._type == 2 then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainSprayPrint)
            local sprayDataTable = HeroHelperTool.GetAllSprayPaintData()
            local sprayRow = sprayDataTable[self._itemId]
            if sprayRow then
                self._wtRewardDetail:SetQuality(sprayRow.SprayPaintLevel)
                self._wtRewardDetail:SetName(sprayRow.SprayPaintName)
                -- self._wtSprayPaintImg:AsyncSetImagePath(sprayRow.SprayPaintImage)
                if not sprayRow.SprayPaintDisplay or string.len(sprayRow.SprayPaintDisplay) <= 0 then return end
                self._wtSprayPaintImg:Collapsed()
                ResImageUtil.AsyncLoadImgObjByPath(sprayRow.SprayPaintDisplay, true, self.SetSprayTextureParam, self)
                for index, heroId in ipairs(sprayRow.BelongedHeroIDs) do
                    local heroData = HeroHelperTool.GetHeroProto(heroId)
                    if heroData then
                        self._wtRewardDetail:SetDesc(heroData.Name)
                        break
                    end
                end
            end
        --头像
        elseif self._type == 3 then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainProfilePhoto)
            local socialDataTable = Facade.TableManager:GetTable("SocialAvatarDataTable")
            local socialRow = socialDataTable[self._itemId]
            if socialRow then
                self._wtRewardDetail:SetQuality(ItemConfigTool.GetItemQuality(self._itemId))
                self._wtRewardDetail:SetName(socialRow.AvatarName)
                self._wtProfilePhoto:AsyncSetImagePath(socialRow.ResourceName)
            end
        --军牌
        elseif self._type == 4 then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainBrand)
            local socialDataTable = Facade.TableManager:GetTable("SocialAvatarDataTable")
            local socialRow = socialDataTable[self._itemId]
            if socialRow then
                self._wtRewardDetail:SetQuality(ItemConfigTool.GetItemQuality(self._itemId))
                self._wtRewardDetail:SetName(socialRow.AvatarName)
                self._wtBrandImg:AsyncSetImagePath(socialRow.ResourceName)
            else
                self._wtBrandImg:Collapsed()
            end
        --挂饰
        elseif self._type == 5 then
            self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainPendant)
            self._wtRewardDetail:SetQuality(ItemConfigTool.GetItemQuality(self._itemId))
            self._wtRewardDetail:SetName(ItemConfigTool.GetItemName(self._itemId))
            local itemInfo = ItemConfigTool.GetItemConfigById(self._itemId)
            self._wtOrnamentImg:Collapsed()
            if itemInfo and itemInfo.Pictures[1] and itemInfo.Pictures[1] ~= "" then
                ResImageUtil.AsyncLoadImgObjByPath(itemInfo.Pictures[1], true, self.SetOrnamentImg, self)
            end
        end
    end
end

function StuffGainPop:SetSprayTextureParam(imageAsset, bAutoResize)
    if imageAsset then
        local DynamicMatIns = self._wtSprayPaintImg:GetDynamicMaterial()
        if DynamicMatIns then
            DynamicMatIns:SetTextureParameterValue("Texture_Flipbook", imageAsset)
            local SprayPaintData = HeroHelperTool.GetAllSprayPaintData()
            if SprayPaintData then
                DynamicMatIns:SetScalarParameterValue("X_Flipbook", SprayPaintData[tostring(self._itemId)].SprayPaintOffset[1])
                DynamicMatIns:SetScalarParameterValue("Y_Flipbook", SprayPaintData[tostring(self._itemId)].SprayPaintOffset[2])
            end
        end
    end
    self._wtSprayPaintImg:SelfHitTestInvisible()
end


function StuffGainPop:SetOrnamentImg(imageAsset, bAutoResize)
    self._wtOrnamentImg:SetImage(imageAsset)
    self._wtOrnamentImg:SelfHitTestInvisible()
end

function StuffGainPop:_OnDownloadResult(moduleName, bDownloaded, errorCode)
    local moduleKey = Module.ExpansionPackCoordinator:GetDownloadCategary(self._itemId)
    if moduleName == moduleKey then
        self:_RefreshResource()
        self:_RefreshDownloadBtn()
    end
end

function StuffGainPop:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_OnDownloadResult(moduleName, isSuccess, 0)
end

function StuffGainPop:_RefreshDownloadBtn()
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        if hasdestroy(self._wtCommonDownload) then
            Facade.UIManager:RemoveSubUIByParent(self, self._wtDownloadPanel)
            local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.LitePackageCommonDownload, self._wtDownloadPanel)
            self._wtCommonDownload = getfromweak(weakUIIns)
            if not hasdestroy(self._wtCommonDownload) then
                self._wtCommonDownload:SetUIPositionType(2)
                local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtCommonDownload)
                if isvalid(slot) then
                    local anchor = FAnchors()
                    anchor.Minimum = FVector2D(0, 0)
                    anchor.Maximum = FVector2D(1.0, 1.0)
                    slot:SetAnchors(anchor)
                    slot:SetAlignment(FVector2D(0, 0))
                    slot:SetOffsets(FMargin(0, 0, 0, 0))
                end
            end
        end
        if not hasdestroy(self._wtCommonDownload) then
            local moduleKey = Module.ExpansionPackCoordinator:GetDownloadCategary(self._itemId)
            local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleKey)
            if not bDownloaded then
                self._wtCommonDownload:InitModuleKey(moduleKey)
                self._wtCommonDownload:SelfHitTestInvisible()
            else
                self._wtCommonDownload:Collapsed()
            end
        end
    end
end

function StuffGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end


function StuffGainPop:_OnTabPressed()
    self._bTabPressed = true
    self:StopAnimation(self.WBP_Hero_ShowHero_in)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_Hero_ShowHero_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

return StuffGainPop