----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local SettlementHighLightKillItem = ui("SettlementHighLightKillItem")

function SettlementHighLightKillItem:Ctor()
    self._wtKillMsgTB = self:Wnd("wtKillMsgTB", UITextBlock)
end

function SettlementHighLightKillItem:OnInitExtraData(killMsg)
    self._killMsg = killMsg
end

function SettlementHighLightKillItem:OnOpen()
    if self._killMsg.stars then
        self._wtKillMsgTB:SetText(string.format(Module.Settlement.Config.Loc.HighLightKillMsgTBWithStars, self._killMsg.gameNick, self._killMsg.rankName, self._killMsg.stars))
    elseif not self._killMsg.rankName then
        self._wtKillMsgTB:SetText(string.format(Module.Settlement.Config.Loc.HighLightKillMsgTB, self._killMsg.gameNick, NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_NoRankDataTxt", "无段位")))
    else
        self._wtKillMsgTB:SetText(string.format(Module.Settlement.Config.Loc.HighLightKillMsgTB, self._killMsg.gameNick, self._killMsg.rankName))
    end
end

return SettlementHighLightKillItem