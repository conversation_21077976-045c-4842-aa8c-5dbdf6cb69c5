----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------


local log = function(...) loginfo("[GuideDataBase]", ...) end
local warn = function(...) logwarning("[GuideDataBase]", ...) end
local error = function(...) logerror("[GuideDataBase]", ...) end

local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"
local UGPInputDelegates = import("GPInputDelegates")

---@class GuideDataBase : LuaObject
local GuideDataBase = class('GuideDataBase', LuaObject)


---@param guideId number
---@param guideCfg FGuideConfig
function GuideDataBase:Ctor(guideId, guideCfg)
    self._guideId = guideId
    self._guideCfg = guideCfg
    self._bPause = false
    self._bEnd = false
    self._delayTimer = nil
    self._guideType= guideCfg and  guideCfg.GuideType ---@type GuideConfig.EGuideData
    -- check?
    -- if not table.contains(Module.Guide.Config.EGuideData) then
    -- end
end

function GuideDataBase:Destroy()
    self._bEnd = true
    self:ReleaseTimer()
    self:RemoveAllLuaEvent()
end

function GuideDataBase:StartGuide()
    if Module.Guide.Field:IsStatePause() then
        self._bPause = true
    end

    -- for temp skip guide
    self.bOverrideTimerOperate = false
    self:OnPreStartGuide()
    self:_RegisterEvents()

    -- Specific events.
    self:AddLuaEvent(GuideConfig.EGuideEvent.evtStepAutoTimeEnd, self.EndGuide, self) -- always listen to step auto time end event

    self:_TryStartTimer()

    self:OnStartGuide()

    if self._bEnd then return end

    if self._bPause then
        self:Pause()
    end
end

--- default switch to branch 1 if pass nil
---@param idx number|nil
function GuideDataBase:EndGuide(idx)
    if self._bEnd then return end
    self._bEnd = true
    self:RemoveAllLuaEvent()
    self:ReleaseTimer()
    idx = setdefault(idx, 1)
    self:OnEndGuide(idx)
    local nextGuideId = self._guideCfg.NextGuideId[idx]
    GuideLogic.DoEndGuide(nextGuideId, idx)
end

function GuideDataBase:ReleaseTimer()
    if self._delayTimer then
        Timer.CancelDelay(self._delayTimer)
        self._delayTimer = nil
    end
end

function GuideDataBase:GetGuideId()
    return self._guideId
end

function GuideDataBase:GetGuideCfg()
    return self._guideCfg
end

function GuideDataBase:GetGuideType()
    return self._guideType
end

function GuideDataBase:Pause(...)
    loginfo("GuideDataBase Pause, guide id:", self._guideId)
    self._bPause = true

    -- self:ReleaseTimer()
    self:OnPause(...)
end

function GuideDataBase:Restart()
    loginfo("GuideDataBase Restart, guide id:", self._guideId)
    self._bPause = false
    -- self:_TryStartTimer()
    self:OnRestart()
end

---@enum EGuideDataEventType
local EGuideDataEventType = {
    Normal = 1,
    GuideMessage = 2,
}

local function matchExpectArguments(expectArgs, arguments)
    if not expectArgs or #expectArgs == 0 then
        return true -- No expectations, always valid
    end

    local expectCount = #expectArgs
    if #arguments < expectCount then
        return false
    end

    for i, expectArg in ipairs(expectArgs) do
        -- property path: read a property should index from global Module or other tables
        if string.find(expectArg, '.', '0', true) ~= nil then
            local evaluatedArg = GuideLogic.GetValueByPath(expectArg) -- Evaluate the path
            if evaluatedArg ~= arguments[i] then
                return false
            end
        else
            if expectArg ~= tostring(arguments[i]) then
                return false
            end
        end
    end
    return true
end

function GuideDataBase:_RegisterEvents()
    local evtInfos = {} ---@type EGuideDataEventInfo[]
    local guideMsgIds = {}


    -- preprocess EndEvetns
    for idx, eventStr in ipairs(self._guideCfg.EndEvent) do
        ---@class EGuideDataEventInfo
        ---@field name string
        ---@field type EGuideDataEventType|nil
        ---@field expectArgs string[]|nil
        local evtInfo =  {
            type = EGuideDataEventType.Normal, -- default type
        }
        -- handle evtName(arg1, arg2...)
        if string.sub(eventStr, #eventStr) == ')' then
            -- Extract event name and arguments using string.match
            local evtName, argStr = string.match(eventStr, "^(.-)%((.*)%)$")
            if not evtName or not argStr then
                assert(false, "Failed to parse end event", eventStr)
            end
            evtInfo.name = evtName

            -- BUG: one event cannot be listen in multiple times with different args
            -- TODO: translate redundant event str from like gdEvtOnGuideMsg(EGuideMsgSig.xxxxx) -> xxxxx directly
            -- TODO: translate redundant event str from like gdEvtOnGuideMsg(EGuideMsgSig.xxxxx) -> msg(xxxxx) directly
            -- ??? any problems and much more time costs on migrating the old data?
            if evtName == "gdEvtOnGuideMsg" then
                evtInfo.type = EGuideDataEventType.GuideMessage
                table.insert(guideMsgIds, idx)
            end

            ---@type string[]
            local expectArgs = {}
            for arg in string.gmatch(argStr, "[^, ]+") do
                table.insert(expectArgs, arg)
            end
            evtInfo.expectArgs = expectArgs
        else -- raw event str
            evtInfo.name = eventStr
        end

        evtInfos[idx] = evtInfo
    end

    -- register normal events
    do
        local listenedEvents = {}
        for idx, evtInfo in ipairs(evtInfos) do
            if evtInfo.type == EGuideDataEventType.Normal then
                local eventName = evtInfo.name
                local expectArgs = evtInfo.expectArgs

                -- BUG: one event cannot be listen in multiple times with different args
                -- Should listen it and dispatch
                -- TODO: check this at compile time(guide field loading phase)
                assert(listenedEvents[eventName] == nil, "Event already registered: " .. eventName)
                listenedEvents[eventName] = true

                local _OnEventInvoke = function(guideData, ...)
                    if self._bPause then
                        warn(" Guide is paused, ignore event invoke: ", guideData._guideId, ...)
                    end
                    local arguments = { ... }
                    if matchExpectArguments(expectArgs, arguments) then
                        self:EndGuide(idx)
                    end
                end
                self:AddLuaEvent(GuideConfig.EGuideEvent[eventName], _OnEventInvoke, self)
            end
        end
    end


    -- register guide bus event
    if #guideMsgIds > 0 then
        local _onGuideMsg = function(guideData, ...)
            if self._bPause then
                warn(" Guide is paused, ignore event invoke: ", guideData._guideId, ...)
            end
            local arguments = { ... }
            for _, idx in ipairs(guideMsgIds) do
                local evtInfo = evtInfos[idx]
                -- assert( evtInfo.type == EGuideDataEventType.GuideMessage, "Guide message event type mismatch" )
                if matchExpectArguments(evtInfo.expectArgs, arguments) then
                    self:EndGuide(idx)
                end
            end
        end
        self:AddLuaEvent(GuideConfig.EGuideEvent.gdEvtOnGuideMsg, _onGuideMsg, self)
    end
end

function GuideDataBase:_TryStartTimer()
    if self._bPause then
        -- loginfo("GuideDataBase: Guide is paused, skip timer start")
        -- return
    end

    if self._guideCfg.AutoNextGuideTime and self._guideCfg.AutoNextGuideTime > 0 then
        if self.bOverrideTimerOperate == true then
            Facade.LuaFramingManager:RegisterFrameTask(self.EndGuide, self)
        else
            self:ReleaseTimer()
            local onAutoTimeEnd = function()
                GuideConfig.EGuideEvent.evtStepAutoTimeEnd:Invoke()
                self._delayTimer = nil
            end
            self._delayTimer = Timer.DelayCall(self._guideCfg.AutoNextGuideTime, onAutoTimeEnd)
        end
    end
end

-- END MODIFICATION

-- 子类重写
function GuideDataBase:Resize(x, y)
end

-- 子类重写
function GuideDataBase:OnPreStartGuide()
end

-- 子类重写
function GuideDataBase:OnStartGuide()
end

-- 子类重写
function GuideDataBase:OnEndGuide(idx)
end

-- 子类重写
function GuideDataBase:OnPause(...)
end

-- 子类重写
function GuideDataBase:OnRestart()
end

-- 子类重写
function GuideDataBase:OnInputTypeChanged(inputType)
    -- Default implementation does nothing
    loginfo("GuideDataBase: OnInputTypeChanged, inputType:", inputType)
end

function GuideDataBase:OnFoldStatusChanged(foldState, expandScreenCount)
end

return GuideDataBase
