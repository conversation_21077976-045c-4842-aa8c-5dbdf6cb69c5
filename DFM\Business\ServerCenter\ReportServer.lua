----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSReport)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ReportServer : ServerBase
local ReportServer = class("ReportServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local GameplayStatics = import("GameplayStatics")
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"

local function log(...)
    loginfo("", "[ReportServer]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

function ReportServer:Ctor()
    self.Events = {
    }
    self._reportConfig = {}
end

function ReportServer:OnInitServer()
end

function ReportServer:OnDestroyServer()
    self:RemoveBindDSReportNTF()
end

function ReportServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
    self.ReqTableList = {}
    self.ReqInfoTableList = {}
end

function ReportServer:OnLoadingGame2Frontend(gameFlowType)
    self:FetchServerData()
    self.ReqTableList = {}
    self.ReqInfoTableList = {}
end

function ReportServer:OnLoadingFrontend2Game(gameFlowType)
end

function ReportServer:RemoveCacheReportData()
    self.mCacheReportData = {}
end

--同一局只允许举报同一个玩家一次
function ReportServer:AddSingleCacheReportData(mathchId, playerId)
    if (not self.mCacheReportData) then
        self.mCacheReportData = {}
    end

    if not self.mCacheReportData[mathchId] then
        self.mCacheReportData[mathchId] = {}
    end

    self.mCacheReportData[mathchId][playerId] = 1
end

--判断该玩家和matchId当前是否可以举报
function ReportServer:CanReportThisPerson(mathchId, playerId)
    if (not self.mCacheReportData) or (not self.mCacheReportData[mathchId]) then
        return true
    end

    if (self.mCacheReportData[mathchId] == nil) or (next(self.mCacheReportData[mathchId]) == nil) then
        return true
    end

    local isExist = self.mCacheReportData[mathchId][playerId]

    if isExist == nil then
        return true
    end

    return false
end

function ReportServer:FetchServerData()
    self:FetchReportConfig()
end

function ReportServer:FetchReportConfig()
    local OnCSTssLoadReportConfigRes = function(res)
        if res.result == 0 then
            self._reportConfig = res.config
            printtable(self._reportConfig, "ReportConfig")
            self:InitReportConfigTbl()
        end
    end

    local req = pb.CSTssLoadReportConfigReq:New()
    req:Request(OnCSTssLoadReportConfigRes)
end

function ReportServer:BindDSReportNTF()
    if not self.mHasBindReportDS then
        -- logerror("ReportServer:BindDSReportNTF", isvalid(UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult), UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult)
        if isvalid(UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult) then
            UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult:Add(self.OnPlayerReportDataNTF,self)
            self.mHasBindReportDS = true
        end
    end
end

function ReportServer:RemoveBindDSReportNTF()
    if self.mHasBindReportDS then
        -- logerror("ReportServer:RemoveBindDSReportNTF", isvalid(UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult), UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult)
        if isvalid(UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult) then
            UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPlayerReportDataNTFResult:Remove(self.OnPlayerReportDataNTF,self)
            self.mHasBindReportDS = false
        end
    end
end

function ReportServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game then
        self:BindDSReportNTF()
    end

    if gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.SafeHouse then
        self:RemoveBindDSReportNTF()
    end
end

function ReportServer:OnGameFlowChangeLeave(gameFlowType)
end

function ReportServer:GetReportConfigByScene(scene)
    if self.config == nil then
        logerror("ReportServer:GetReportConfigByScene, self.config == nil")
        return nil
    end
    if scene < 1 or scene > #self.config then
        logerror("ReportServer:GetReportConfigByScene, index out of range, scene=", scene, "list len=", #self.config)
        return nil
    end
    return self.config[scene]
end

--初始化config数据
function ReportServer:InitReportConfigTbl()
    self.config = {}
    for _, configInfo in ipairs(self._reportConfig.scene_display_config) do
        local categroyTbl = {}
        for _, value in ipairs(configInfo.category_config) do
            for _, categoryInfo in ipairs(self._reportConfig.category_config) do
                if categoryInfo.report_category == value then
                    table.insert(categroyTbl, categoryInfo)
                end
            end
        end
        printtable(categroyTbl, "categroyTbl")
        self.config[configInfo.report_scene] = categroyTbl
    end
    printtable(self.config, "Report_Config")
end

function ReportServer:FetchTssReport(reportInfo, reportEndCall)
    if not self:CanReportThisPerson(reportInfo.battle_id, reportInfo.reported_player_id) then
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReportSuccess)
        return
    end

    if Server.AccountServer:IsPlayerInGame() then
        self:BindDSReportNTF()
    end

    -- 局内时机都走DS的举报逻辑
    if self.mHasBindReportDS then
        local playerCtrl = GameplayStatics.GetPlayerController(GetWorld(), 0)
        if playerCtrl then
            if not self.ReqTableList then
                self.ReqTableList = {}
            end
            if not self.ReqInfoTableList then
                self.ReqInfoTableList = {}
            end


            table.insert(self.ReqTableList, reportEndCall)
            table.insert(self.ReqInfoTableList, reportInfo)

            playerCtrl:PlayerReportData(reportInfo.report_scence, reportInfo.report_category, reportInfo.report_reason, reportInfo.reported_player_id, reportInfo.report_desc, reportInfo.entrance, LocalizeTool.GetReportId())
        end
        return
    end


    local OnCSTssReportRes = function(res)
        if res.result == 0 then
            if reportEndCall then
                reportEndCall()
            end
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReportSuccess)
            self:AddSingleCacheReportData(reportInfo.battle_id, reportInfo.reported_player_id)
        else
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReportFail)
        end
    end
    local req = pb.CSTssReportReq:New()
    req.report_scence = reportInfo.report_scence
    req.report_category = reportInfo.report_category
    req.report_reason = reportInfo.report_reason
    req.reported_player_id = reportInfo.reported_player_id
    req.battle_id = reportInfo.battle_id
    req.reported_profile_url = reportInfo.reported_profile_url
    req.report_desc = reportInfo.report_desc
    req.report_content = reportInfo.report_content
    req.entrance = reportInfo.entrance
    req.nick_name = reportInfo.nick_name
    req.language_id = LocalizeTool.GetReportId()
    req:Request(OnCSTssReportRes)
end

function ReportServer:OnPlayerReportDataNTF(RequestId, Result)
    logerror("ReportServer:OnPlayerReportDataNTF", Result == 0, Result)
    if Result == 0 then
        if #self.ReqTableList > 0 then
            local reportEndCallInst = table.remove(self.ReqTableList, 1)
            if reportEndCallInst then
                reportEndCallInst()
            end
        end

        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReportSuccess)

        if #self.ReqInfoTableList > 0 then
            local InfoInst = table.remove(self.ReqInfoTableList, 1)
            self:AddSingleCacheReportData(InfoInst.battle_id, InfoInst.reported_player_id)
        end
    else
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.ReportFail)
    end
end

return ReportServer