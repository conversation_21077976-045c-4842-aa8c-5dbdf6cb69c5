----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------



local ReddotDataTrie = require "DFM.Business.Module.ReddotTrieModule.ReddotBase.ReddotDataTrie"
local CollectionRoomManifest = require "DFM.Business.Module.ReddotTrieModule.DotUnit.CollectionRoomDotUnit.Defination.CollectionRoomManifest"
local ReddotTrieConfig = require "DFM.Business.Module.ReddotTrieModule.ReddotTrieConfig"

local CollectionRoomDataTrie = ReddotDataTrie:New(EReddotTrieObserverType.CollectionRoom)

function CollectionRoomDataTrie:InitTrie()
    self:GenerateSubTrie(CollectionRoomManifest)
end
--建议在函数中对id做判空,以防阻塞创建数据节点
CollectionRoomDataTrie.CheckCabinetUnLockFun = function(id)
    --fix: 请补充
    return false
end

CollectionRoomDataTrie.CheckCollectionCanShelveFun = function(id)
    -- 收藏室未解锁直接返回false
    local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    if not deviceData or deviceData:GetLevel() == 0 then
        return false
    end
    return CollectionRoomDataTrie.CheckItemCanShelveFun()
end

CollectionRoomDataTrie.CheckCabinetCanLevelFun = function(id)
    -- 收藏室未解锁直接返回false
    local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    if not deviceData or deviceData:GetLevel() == 0 then
        return false
    end
    return CollectionRoomDataTrie.CheckCabinetCanLevelUp()
end


-----------------------------------------------------------------------
--region CheckCabinetShelveFun

CollectionRoomDataTrie.CheckItemCanShelveFun = function()
    local collectionItems = Server.CollectionRoomServer:GetCollectionItemInfo()
    for _, itemInfo in ipairs(collectionItems) do
        if Server.CollectionRoomServer:IsCabinetUnlocked(itemInfo.SlotType) and CollectionRoomDataTrie.CheckCabinetShelveFun(itemInfo) then
            return true
        end
    end
    return false
end

-- 展柜是否已上架
CollectionRoomDataTrie.CheckCabinetShelveFun = function(itemInfo)
    local bShevle = Server.CollectionRoomServer:GetCabinetItemByGrid(itemInfo.SlotType, 1, itemInfo.SlotID)
    local itemId = itemInfo.ItemID
    if bShevle or CollectionRoomDataTrie.IsClickRedPoint(itemId, true) or itemInfo.IsLimited then
        return false
    else
        local bindCondition = function (item)
            return item.id == tonumber(itemId) and not item:CheckIsBind() and item.InSlot:IsExtContainerSlot()
        end
        local itemNumInWH = Server.InventoryServer:GetItemNumByCondition(bindCondition)
        return itemNumInWH > 0
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region CheckCabinetCanLevelFun

CollectionRoomDataTrie.CheckCabinetCanLevelUp = function()
    local collectionItems = Server.CollectionRoomServer:GetCollectionItemInfo()
    for _, itemInfo in ipairs(collectionItems) do
        local upgradeItems = CollectionRoomDataTrie.GetCabinetUpgradeData(itemInfo)
        local canLevelUp = CollectionRoomDataTrie.IsCabinetCanLevelUp(upgradeItems, itemInfo.SlotType, itemInfo.SlotID)
        local cabinetIsUnLock = Server.CollectionRoomServer:IsCabinetUnlocked(itemInfo.SlotType)
        if not CollectionRoomDataTrie.IsClickRedPoint(itemInfo.ItemID, false) and canLevelUp and cabinetIsUnLock then
            return true
        end
    end
    return false
end

-- 展柜是否可升级
CollectionRoomDataTrie.IsCabinetCanLevelUp = function(upgradeItems, cabinetType, gridId)
    local gridLevel = Server.CollectionRoomServer:GetCabinetGridLevel(cabinetType, 1, gridId)
    gridLevel = gridLevel or 0

    local itemList = upgradeItems[gridLevel + 1]
    local bShelveItem = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, 1, gridId)

    if table.isempty(itemList) or not bShelveItem then
        return false
    end
    local upgradItem2Info = {}

    for key, value in ipairs(itemList.items) do
        -- 哈夫币单独算
        if value ~= "17020000010" then
            declare_if_nil(upgradItem2Info, key, {})
            upgradItem2Info[key].itemID = tonumber(value)
            upgradItem2Info[key].item2Bind = tonumber(itemList.bindInfo[key])
            upgradItem2Info[key].item2Quan = tonumber(itemList.quantity[key])
        else
            upgradItem2Info.currency = tonumber(itemList.quantity[key])
        end
    end
    local canLevelUp = true

    -- 首先判断价格是否满足条件
    if upgradItem2Info.currency then
        canLevelUp = upgradItem2Info.currency > Server.InventoryServer:GetPlayerCurrencyNum() and false or true
    end

    -- 各个道具数量是否足够，有一个不足就返回false
    for index, value in ipairs(upgradItem2Info) do
        local function fIsSpecifyBindItem(item)
            return item.id == value.itemID and not item:CheckIsBind()
        end
        local function fIsSpecifyUnBindItem(item)
            return item.id == value.itemID
        end
        local items
        -- 首先从仓库拿到满足条件的道具
        if tonumber(value.item2Bind) == 0 then
            items = Server.InventoryServer:GetItemsByCondition(fIsSpecifyBindItem)
        elseif tonumber(value.item2Bind) == 1  then
            items = Server.InventoryServer:GetItemsByCondition(fIsSpecifyUnBindItem)
        end
        if table.isempty(items) or #items < tonumber(value.item2Quan) then
            canLevelUp = false
            break
        end
    end
    return canLevelUp
end

-- 展位升级所需道具list
CollectionRoomDataTrie.GetCabinetUpgradeData = function(gridInfo)
    local upgradeItems = {}
    -- declare_if_nil(upgradeItems, gridInfo.SlotType, {})
    -- declare_if_nil(upgradeItems[gridInfo.SlotType], gridInfo.SlotID, {})
    if gridInfo.Level1Item and gridInfo.Level1ItemBind and gridInfo.Level1ItemQuan then
        local itemList1 = {}
        itemList1.items = string.split(gridInfo.Level1Item, ",")
        itemList1.bindInfo = string.split(gridInfo.Level1ItemBind, ",")
        itemList1.quantity = string.split(gridInfo.Level1ItemQuan, ",")
        -- table.insert(upgradeItems[gridInfo.SlotType][gridInfo.SlotID], itemList1)
        table.insert(upgradeItems, itemList1)
    end
    if gridInfo.Level2Item and gridInfo.Level2ItemBind and gridInfo.Level2ItemQuan then
        local itemList2 = {}
        itemList2.items = string.split(gridInfo.Level2Item, ",")
        itemList2.bindInfo = string.split(gridInfo.Level2ItemBind, ",")
        itemList2.quantity = string.split(gridInfo.Level2ItemQuan, ",")
        -- table.insert(upgradeItems[gridInfo.SlotType][gridInfo.SlotID], itemList2)
        table.insert(upgradeItems, itemList2)
    end
    return upgradeItems
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public Func

CollectionRoomDataTrie.IsClickRedPoint = function(itemId, bShelve)
    local displayAndSpecialItemList
    if bShelve then
        displayAndSpecialItemList = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.CollectionRoomShelveMap)
    else
        displayAndSpecialItemList = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.CollectionRoomLevelMap)
    end
    return displayAndSpecialItemList[tostring(itemId)]
end

--endregion
-----------------------------------------------------------------------

return CollectionRoomDataTrie
