----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMVersionUpdate)
----- LOG FUNCTION AUTO GENERATE END -----------

loginfo = function (...)
    SdkUtil.LogInfo("dolphin",...)
end

logwarning = function (...)
    SdkUtil.LogWarning("dolphin",...)
end

logerror = function (...)
    SdkUtil.LogError("dolphin",...)
end

---@class VersionUpdateModule : ModuleBase
local VersionUpdateModule = class("VersionUpdateModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local VersionUpdateLogic = require "DFM.Business.Module.VersionUpdateModule.Logic.VersionUpdateLogic"
local VersionUpdateEventLogic = require "DFM.Business.Module.VersionUpdateModule.Logic.VersionUpdateEventLogic"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local UGameVersionUtils = import "GameVersionUtils"
local UBlueprintPathsLibrary = import "BlueprintPathsLibrary"
local LitePackageWrapper = import "LitePackageWrapper"
local litePackageMgr = LitePackageWrapper.GetInstance(GetGameInstance())

local bRestartLua = false

function VersionUpdateModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
function VersionUpdateModule:OnInitModule()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认加载资源（预加载UI蓝图、需要用到的图片等等
function VersionUpdateModule:OnLoadModule()
    -- VersionUpdateLogic.AddListeners()
	UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnLaunchToLogin_OnAppVersionChange:Add(self.OnAppVersionChange, self)
    
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
function VersionUpdateModule:OnUnloadModule()
    -- VersionUpdateLogic.RemoveListeners()
    Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginVersionUpdate:RemoveListener(self.BeginUpdateGame,self)
    Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:RemoveListener(self.OnAnnouncementFinishPanel,self)

    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnLaunchToLogin_OnAppVersionChange:Remove(self.OnAppVersionChange, self)
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------

-------------------------------------------------------------------------
--region Announcement

function VersionUpdateModule:OnAppVersionChange()
    logerror("VersionUpdateModule:OnAppVersionChange")
    --> write ToWriteGameAppVersionStr
    local toWriteAppversion = UGameVersionUtils.ToWriteGameAppVersionStr
    logerror("VersionUpdateModule:OnAppVersionChange() toWriteAppversion:"..toWriteAppversion)
    Facade.ConfigManager:SetString("LastAppVersionForPuffer", toWriteAppversion)
end

-- 这里的公告只针对更新，紧急公告不放在这个时候处理
function VersionUpdateModule:OnAnnouncementFinishPanel(contentType, bConfirm)
    logwarning("VersionUpdateModule:OnAnnouncementFinishPanel",contentType,bConfirm)

    Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginVersionUpdate:RemoveListener(self.BeginUpdateGame,self)
    Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:RemoveListener(self.OnAnnouncementFinishPanel,self)

    -- 结束公告
    -- 只有强更公告点取消是会强制退出的
    if contentType == EAnnounceContentType.ForceUpdate then
        if bConfirm then
            -- 开始更新指定版本
            VersionUpdateLogic.OnAnnounceConfirmCallback(true)
        else
            -- 退出游戏
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end
        return
    end

    if contentType == EAnnounceContentType.ShouldUpdate then
        if bConfirm then
            -- 开始下载当前更新/跳过更新
            VersionUpdateLogic.OnAnnounceConfirmCallback(true)
        else
            -- 非强更点取消直接跳过更新就好了
            VersionUpdateLogic.OnAnnounceConfirmCallback(false)
        end

        return
    end

    -- 啥也不是就直接过
    VersionUpdateLogic.OnAnnounceConfirmCallback(true)
end

---@param newVersionDesc string 新版本的版本号下载相关描述
---@param bIsForceUpdate boolean 是否是强更
---@param bIsForceOpen boolean 是否强制打开，不管内容是否存在，没有内容要展示
---@param bIsAppUpdate boolean 是否是App更新
---@param versionStr string
---@param downloadSizeStr string
function VersionUpdateModule:ShowAnnounceStep(newVersionDesc, bIsForceUpdate, bIsForceOpen, bIsAppUpdate, versionStr, downloadSizeStr)
    -- dolphin会传下来一个是否是强更的标记，根据这个标记展示
    Module.GCloudSDK.Config.Events.evtOnLoadAnnounceData:RemoveListener(self.ShowAnnounceStep, self) -- 用完即去

    if bIsForceUpdate then
        -- 先看强更和非强更公告有没有，有的话展示，没的话直接走
        -- App更新需要强行打开公告，不管有没有内容
        local ret = Module.GCloudSDK:OpenAnnouncePanel(EAnnounceContentType.ForceUpdate, false, newVersionDesc, bIsForceOpen, bIsAppUpdate, versionStr, downloadSizeStr)
        if ret then
            Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginVersionUpdate:AddListener(self.BeginUpdateGame,self)
            Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:AddListener(self.OnAnnouncementFinishPanel,self)
            return
        end

        -- 没有的话得直接走了
        self:OnAnnouncementFinishPanel(EAnnounceContentType.ForceUpdate, true)
    else
        -- 没有强更，看非强更
        -- App更新需要强行打开公告，不管有没有内容
        local ret = Module.GCloudSDK:OpenAnnouncePanel(EAnnounceContentType.ShouldUpdate, false, newVersionDesc, bIsForceOpen, bIsAppUpdate, versionStr, downloadSizeStr)
        if ret then
            Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginVersionUpdate:AddListener(self.BeginUpdateGame,self)
            Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:AddListener(self.OnAnnouncementFinishPanel,self)
            return
        end

        -- 没有的话得直接走，不能阻塞
        self:OnAnnouncementFinishPanel(EAnnounceContentType.ShouldUpdate, true)
    end
end

--endregion
-----------------------------------------------------------------------

---开始版本更新模块 （蓝图lanchToLogin调用）
--- 开始更新流程
function VersionUpdateModule:BeginVersionUpdateStep()
    logwarning("[VersionUpdate] BeginVersionUpdateStep >> isDebugMode:", tostring(self:IsDebugMode()))

    -- @akihikofeng PS5下载空间清理
    -- 在下载资源前清理旧的热更文件，以及插件日志，旧版本残留压缩包
    if IsPS5Family() then
        logerror("[VersionUpdate] PS5 clean files before version update")
        -- 插件日志清理
        self:CleanupDirectory(FPaths.ProjectPersistentDownloadDir()..".gcloud/")
        self:CleanupDirectory(FPaths.ProjectPersistentDownloadDir().."intl/log/")
        -- 清理残留的压缩文件包（上一轮没下载完杀进程）
        self:CleanupDolphinCures()
        -- 清理旧版本热更（此时没拉新，删除的旧版本可能是更旧的版本）
        logerror("[VersionUpdate] Cleanup old version before pulling version info")
        local version = UGameVersionUtils.GetVersion()
        self:CleanupDolphinOldVersion(version)
        local freeSpace = litePackageMgr:GetDeviceFreeSpace()
        logerror("[VersionUpdate] Precheck device space free "..freeSpace)
    end

    --启动C++侧状态机
    if self:IsDebugMode() then
        VersionUpdateLogic.BeginVersionUpdateStep_Debug()
    else
        VersionUpdateLogic.BeginVersionUpdateStep()
    end
end

-- @akihikofeng 清理目录
---@param folderPath string
function VersionUpdateModule:CleanupDirectory(folderPath)
    local expansionFolderPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(folderPath, "")
    if ULuaExtension.Ext_DirectoryExists(expansionFolderPath) then
        logerror("[VersionUpdate] Cleanup directory "..expansionFolderPath)
        ULuaExtension.DeleteMoLiDirectoryRecursivelyByPath(expansionFolderPath)
    end
end

-- @akihikofeng 清理文件
---@param filePath string
function VersionUpdateModule:CleanupFile(filePath)
    local expansionFilePath = UBlueprintPathsLibrary.ConvertRelativePathToFull(filePath, "")
    if ULuaExtension.Ext_FileExists(expansionFilePath) then
        logerror("[VersionUpdate] Cleanup file "..expansionFilePath)
        -- Lua侧缺少导出Platform的文件删除API，经测试Lua原生OS删除也可用。
        -- 需要注意删已挂载的pak可能会崩溃，不过下次进入就没问题了。
        os.remove(expansionFilePath)
    end
end

-- @akihikofeng 清理Dolphin压缩包残留
function VersionUpdateModule:CleanupDolphinCures()
    local dolphinPath = FPaths.ProjectPersistentDownloadDir() .."dolphin/"
    dolphinPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(dolphinPath, "")
    if ULuaExtension.Ext_DirectoryExists(dolphinPath) then
        local files = {}
        files = ULuaExtension.Ext_FindFilesRecursive(files, dolphinPath, "*.cures", true, false, false) -- files
        if #files > 0 then
            for k, v in pairs(files) do
                logerror("[VersionUpdate] Find cures to clean "..v)
                self:CleanupFile(v)
            end
        end
    else
        logerror("[VersionUpdate] Dolphin directory does not exist")
    end
end

-- @akihikofeng 清理Dolphin旧版本目录
---@param currentVersion string
function VersionUpdateModule:CleanupDolphinOldVersion(currentVersion)
    local dolphinPath = FPaths.ProjectPersistentDownloadDir() .."dolphin/"
    dolphinPath = UBlueprintPathsLibrary.ConvertRelativePathToFull(dolphinPath, "")
    if ULuaExtension.Ext_DirectoryExists(dolphinPath) then
        local files = {}
        files = ULuaExtension.Ext_FindFilesRecursive(files, dolphinPath, "*", false, true, false) -- directory
        if #files > 0 then
            for k, v in pairs(files) do
                if string.find(v, currentVersion) == nil then
                    logerror("[VersionUpdate] Cleanup dolphin old version directory "..v)
                    self:CleanupDirectory(v)
                end
            end
        end
    else
        logerror("[VersionUpdate] Dolphin directory does not exist")
    end
end

function VersionUpdateModule:BeginVersionUpdate()
    if not self:IsEnable() then
        logwarning("[VersionUpdate] VersionUpdateModule:BeginVersionUpdate not enable, skip version update")
        VersionUpdateLogic.SkipVersionUpdateStep()
        return
    end

    if VersionUpdateLogic.IsSkipByLauncher() then
        logwarning("[VersionUpdate] VersionUpdateModule:BeginVersionUpdate skip version update by launcher")
        VersionUpdateLogic.SkipVersionUpdateStep()
        return
    end

    -- 都没有那就过了
    self:BeginVersionUpdateStep()
    --self._mainPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.VersionUpdateMainPanel, onUILoad)
end

function VersionUpdateModule:OnlyShowMainPanel()
    logwarning("VersionUpdateModule:OnlyShowMainPanel")
    --Facade.UIManager:AsyncShowUI(UIName2ID.VersionUpdateMainPanel)
end

function VersionUpdateModule:CloseMainPanel()
    logwarning("[VersionUpdate] VersionUpdateModule.CloseMainPanel...")
    --Facade.UIManager:CloseUIByHandle(self._mainPanelHandle)
end

function VersionUpdateModule:IsEnable()
    return VersionUpdateLogic.IsEnable()
end

--- 是否测试模式
function VersionUpdateModule:IsDebugMode()
    return VersionUpdateLogic.IsDebugMode()
end

--- 开始更新流程
function VersionUpdateModule:BeginUpdateGame()
    logwarning("[VersionUpdate] BeginUpdateGame >> isDebugMode:", tostring(self:IsDebugMode()))
    Module.GCloudSDK.Config.Events.evtOnAnnouncementBeginVersionUpdate:RemoveListener(self.BeginUpdateGame,self)
    Module.GCloudSDK.Config.Events.evtOnAnnouncementFinishPanel:RemoveListener(self.OnAnnouncementFinishPanel,self)

    VersionUpdateLogic.OnAnnounceConfirmCallback(true) -- 强更默认这里进来就是确定也只有确定选项
end

--- 完成更新流程
---@param bSuc any
function VersionUpdateModule:FinishVersionUpdateStep(bSuc)
    logwarning("[VersionUpdate] FinishVersionUpdateStep >> suc :", tostring(bSuc))
    Module.GCloudSDK:UpdatePercentFloat(100)
    self:ShowProgressbar(false)
    local errorCode = VersionUpdateLogic.GetErrorCode()
    local errortype = VersionUpdateLogic.GetErrorType()
    local isFatalError = VersionUpdateLogic.IsFatalError(errorCode, errortype)
    --关闭C++侧状态机 避免一直tick
    VersionUpdateLogic.FinishVersionUpdateStep()

    if bSuc and not IsInEditor() then
        VersionUpdateLogic.LoadResource()
        UGameVersionUtils.UpdateStringTableVersion()
    end

    self:CloseMainPanel()
    local DFGameLaunchManager = import("DFGameLaunchManager").GetGameLaunchManager(GetGameInstance())
    local ELaunchStepResult = import "ELaunchStepResult"

    if bSuc then
        if bRestartLua then
            Module.VersionUpdate.Config.flowEvtVersionUpdateFinished:Invoke("RestartLua")
            UDFMGameplayGlobalDelegates.Get(GetWorld()):OnVersionUpdateEnd(false, "RestartLua")
        else
            Module.VersionUpdate.Config.flowEvtVersionUpdateFinished:Invoke()
            UDFMGameplayGlobalDelegates.Get(GetWorld()):OnVersionUpdateEnd(true, "")
        end

        -- 设备更新后需要重启
        if PLATFORM_ANDROID then

            if ApmUtil.DoesDeviceNeedToReboot() then

                logwarning("[cobyli] VersionUpdateModule DoesDeviceNeedToReboot return true")

                local HasRebooted = Facade.ConfigManager:GetBoolean("HasRebootedAfterUpdate", false)
                if not HasRebooted then

                    Facade.ConfigManager:SetBoolean("HasRebootedAfterUpdate", true)
	
                    local NeedToRebootText = NSLOCTEXT("VersionUpdateModule", "Lua_EntranceFlow_NeedReboot", "亲爱的干员，游戏更新完毕，请您重新启动游戏以使更新生效。")
                    local ConfirmQuitGame = NSLOCTEXT("VersionUpdateModule", "Lua_EntranceFlow_QuitGame", "确定")
                    Module.GCloudSDK:ShowCommonTip(NeedToRebootText, ConfirmQuitGame, nil, true, function()
                        local EQuitPreference = import "EQuitPreference"
                        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
                    end, nil, false)

                    return
                end
            end

        end

        -- 有可能测试版本失败了，不过这里不能阻塞
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("VersionUpdate", ELaunchStepResult.ELSR_Success, "")
        end
    else
        if isFatalError then
            --UDFMGameplayGlobalDelegates.Get(GetWorld()):OnVersionUpdateEnd(false)
                -- 异常上报
            if DFGameLaunchManager then
                DFGameLaunchManager:FinishStep("VersionUpdate", ELaunchStepResult.ELSR_Failed, tostring(errorCode))
            end
        else
            -- 异常上报
            if DFGameLaunchManager then
                DFGameLaunchManager:FinishStep("VersionUpdate", ELaunchStepResult.ELSR_Warning, tostring(errorCode))
            end
        end
    end
end

--- 弹出公告弹窗
---@param versionStr string 版本号
---@param bIsForceUpdate boolean 是否是强更
---@param bIsAppUpdate boolean 是否是App更新
---@param bIsWifi boolean 是否wifi
---@param downloadSize number 下载大小
function VersionUpdateModule:ShowUpdateAnnounceWindow(versionStr, bIsForceUpdate, bIsAppUpdate, bIsWifi, downloadSize)
    VersionUpdateLogic.ShowUpdateAnnounceWindow(versionStr, bIsForceUpdate, bIsAppUpdate, bIsWifi, downloadSize)
end

--- 弹出用户确认是否更新的弹窗
function VersionUpdateModule:ShowUpdateConfirmWindow(updateDesc, bIsForceUpdate)
    logwarning("[VersionUpdate] ShowUpdateConfirmWindow >> updateDesc :", updateDesc, "bIsForceUdpate: ", tostring(bIsForceUpdate))
    VersionUpdateLogic.ShowUpdateConfirmWindow(updateDesc, bIsForceUpdate)
end

--- 更新失败弹窗
---@param errorCode number 错误码
---@param errorType number 错误类型
function VersionUpdateModule:ShowUpdateFailWindow(errorCode, errorType)
    logwarning("[VersionUpdate] ShowUpdateFailWindow >>errorCode:", errorCode, " >> errorType :", errorType)
    VersionUpdateLogic.ShowUpdateFailWindow(errorCode, errorType)
end

--- 是否显示进度条
---@param bShow boolean true:显示 false:不显示
function VersionUpdateModule:ShowProgressbar(bShow)
    loginfo("[VersionUpdate] ShowProgressbar >> bShow :", bShow)
    Module.GCloudSDK:UpdateLeftTimeVisibility(bShow)
end

--- 同步当前进行的更新步骤 程序更新、资源更新、首包解压
---@param step number
function VersionUpdateModule:SetUpdateStep(step)
    loginfo("[VersionUpdate] SetUpdateStep >> step :", step)
    Module.VersionUpdate.Field:SetUpdateStep(step)
    VersionUpdateLogic.OnUpdateStep(step)
    self:UpdateProgressDesc()
end

--- 同步当前更新步骤到了哪个状态
---@param state number
function VersionUpdateModule:SetUpdateState(state)
    loginfo("[VersionUpdate] SetUpdateState >> state :", state)
    Module.VersionUpdate.Field:SetUpdateState(state)
    self:ShowProgressbar(state == Module.VersionUpdate.Config.EVersionUpdateState.Downloading)
    self:UpdateProgressDesc()

    -- @akihikofeng PS5 Hook一下更新流程来清理文件
    if IsPS5Family() then
        -- 在拉取到新版本信息后， 开始下载之前，再次清理旧版本热更
        if state == Module.VersionUpdate.Config.EVersionUpdateState.BeginUpdate or state == Module.VersionUpdate.Config.EVersionUpdateState.ConfirmingToAnnounce then
            -- 此时拉到了新版本数据，可以删除真正的旧版本热更
            logerror("[VersionUpdate] Cleanup old version begin update")
            local version = UGameVersionUtils.GetVersion()
            self:CleanupDolphinOldVersion(version)
            local freeSpace = litePackageMgr:GetDeviceFreeSpace()
            logerror("[VersionUpdate] Precheck device space free "..freeSpace)

        -- 解压失败、或资源热更成功后，清理新版本的残留压缩包
        elseif state == Module.VersionUpdate.Config.EVersionUpdateState.Failed or state == Module.VersionUpdate.Config.EVersionUpdateState.Finished then
            logerror("[VersionUpdate] Update failed, cleanup cures")
            -- Bug Case: 这一轮没解压成功，压缩包留在机器上，导致下次完全无法进入游戏（<300MB物理，C++ UGLS_CheckPhysicSpace层阻止进入游戏）
            self:CleanupDolphinCures()
        end
    end
end

--- 刷新阶段描述
function VersionUpdateModule:UpdateProgressDesc()
    if Module.VersionUpdate.Field:CheckNeedUpdateProgressDesc() then
        Module.GCloudSDK:UpdateLoadingTips(Module.VersionUpdate.Field:GetProgressDesc())
        Module.VersionUpdate.Field:FinishUpdateProgressDesc()
    end
end

--- 准备重启lua虚拟机
function VersionUpdateModule:PrepareRestartLua()
    bRestartLua = true
end

--- 检测是否有CDN云控版本更新公告
function VersionUpdateModule:CheckShowAppCDNInfoWindow()
    return VersionUpdateLogic.CheckShowAppCDNInfoWindow()
end

--- 检测是否有CDN云控公告
function VersionUpdateModule:CheckShowCDNAnnounceWindow()
    return VersionUpdateLogic.CheckShowCDNAnnounceInfo()
end

--是否跳转商店下载
function VersionUpdateModule:IsAppStoreDownload()
    return VersionUpdateLogic.IsAppStoreDownload()
end

return VersionUpdateModule
