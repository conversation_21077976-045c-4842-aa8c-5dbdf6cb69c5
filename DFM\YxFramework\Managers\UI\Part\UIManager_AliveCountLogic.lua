----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerAliveCount)
----- LOG FUNCTION AUTO GENERATE END -----------

local ULuaSubsystem                 = import "LuaSubsystem"

---@class UIManager : ManagerBase
local UIManager = {}
local UGameplayStatics = import "GameplayStatics"
local RefUtil = require "DFM.YxFramework.Util.RefUtil"
---------------------------------------------------------------------------------
--- UIManager 关于AliveCount逻辑拆分
---------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------
----------------------------------------------------------------------------------------------------------------
function UIManager:Ctor()
    self._clearTimeWhenUltimating = 0
    self._mapUINavID2AliveCount = {}
    self._mapUINavID2TokenIDMap = {}
    self._mapUINavID2ClearTimerHandle = {}

    self._mapWeakOwerIns2bSubHistory = setmetatable({}, weakmeta_key)
    self._mapLoadingGid2SubHistory = {}
    self._mapLoadingGid2ClearTimerHandle = {}

    self._mapUITokenID2IsLuaPendingKill = {}
end

function UIManager:Destroy()
    self._mapUINavID2AliveCount = {}
    self._mapUINavID2TokenIDMap = {}
    self._mapUINavID2ClearTimerHandle = {}

    self._mapWeakOwerIns2bSubHistory = setmetatable({}, weakmeta_key)
    self._mapLoadingGid2SubHistory = {}
    self._mapLoadingGid2ClearTimerHandle = {}

    self._mapUITokenID2IsLuaPendingKill = {}
end

---------------------------------------------------------------------------------
--- UIManager LuaPendingKillRelease
---------------------------------------------------------------------------------
function UIManager:MarkLuaPendingKill(uiTokenID)
    if uiTokenID then
        -- if not VersionUtil.IsShipping() then
        --     logwarning('[Low Memory Log - LuaPendingKill ] ******* MarkLuaPendingKill -----【UIManager MarkLuaPendingKill】 -------------', uiTokenID)
        -- end
        self._mapUITokenID2IsLuaPendingKill[uiTokenID] = true
    end
end

function UIManager:UnMarkLuaPendingKill(uiTokenID)
    if uiTokenID then
        self._mapUITokenID2IsLuaPendingKill[uiTokenID] = false
    end
end

function UIManager:IsLuaPendingKill(uiTokenID)
    if uiTokenID then
        return self._mapUITokenID2IsLuaPendingKill[uiTokenID] or false
    else
        return false
    end
end

function UIManager:UnbindWhenLuaPendingKill(uiIns)
    if uiIns and uiIns.UITokenID and self:IsLuaPendingKill(uiIns.UITokenID) then
        self:DoRemoveLuaRef(uiIns)
    end
end

function UIManager:DoRemoveLuaRef(uiIns)
    if isvalid(uiIns) then
        if not VersionUtil.IsShipping() then
            logerror('[Low Memory Log - LuaPendingKill - Finished ] ******* After RemoveFromParent -----【RefUtil.RemoveRef】 -------------', uiIns, uiIns._cname, uiIns.UITokenID)
        end
        RefUtil.RemoveRef(uiIns)
    end
end

---------------------------------------------------------------------------------
--- UIManager OwnerSubHistory
---------------------------------------------------------------------------------
function UIManager:BindOwnerSubHistory(ownerIns, subUINavID)
    declare_if_nil(self._mapWeakOwerIns2bSubHistory, ownerIns, {})
    self._mapWeakOwerIns2bSubHistory[ownerIns][subUINavID] = true
end

function UIManager:UnbindOwnerSubHistory(ownerIns, subUINavID)
    declare_if_nil(self._mapWeakOwerIns2bSubHistory, ownerIns, {})
    self._mapWeakOwerIns2bSubHistory[ownerIns][subUINavID] = false
end

---------------------------------------------------------------------------------
--- UIManager AsyncLoad OwnerSubHistory
---------------------------------------------------------------------------------
local asyncKeepHistroyTime = 10
function UIManager:OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid)
    if loadingGid and next(allSubUINavIDList) then
        declare_if_nil(self._mapLoadingGid2SubHistory, loadingGid, {})
        for _, loadingUINavID in ipairs(allSubUINavIDList) do
            if not self._mapLoadingGid2SubHistory[loadingGid][loadingUINavID] then
                self._mapLoadingGid2SubHistory[loadingGid][loadingUINavID] = true
            end
            -- if not VersionUtil.IsShipping() then
            --     logwarning("[Low Memory Log - AliveCount] AsyncLoadingAliveCount 加载开始 OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid) ", loadingUINavID, loadingGid)
            -- end
        end

        --- 设置超时历史检查
        local preHandle = self._mapLoadingGid2ClearTimerHandle[loadingGid]
        if preHandle then
            Timer.CancelDelay(preHandle)
            self._mapLoadingGid2ClearTimerHandle[loadingGid] = nil
        end
        self._mapLoadingGid2ClearTimerHandle[loadingGid] = Timer.DelayCall(asyncKeepHistroyTime, self._OnResPathsCancelAsyncLoading, self, loadingGid)
    end
end

function UIManager:OnResPathsFinishAsyncLoading(loadingGid)
    if loadingGid then
        if self._mapLoadingGid2SubHistory then
            if self._mapLoadingGid2SubHistory[loadingGid] ~= nil then
                -- if next(self._mapLoadingGid2SubHistory[loadingGid]) then
                --     for loadingUINavID, bExist in pairs(self._mapLoadingGid2SubHistory[loadingGid]) do
                --         if bExist then
                --             if not VersionUtil.IsShipping() then
                --                 logwarning("[Low Memory Log - AliveCount] AsyncLoadingAliveCount 加载结束 OnResPathsFinishAsyncLoading(allSubUINavIDList, loadingGid) ", loadingUINavID, loadingGid)
                --             end
                --         end
                --     end
                -- end
                self._mapLoadingGid2SubHistory[loadingGid] = nil
            end
        end

        --- 解除超时历史检查
        local preHandle = self._mapLoadingGid2ClearTimerHandle[loadingGid]
        if preHandle then
            Timer.CancelDelay(preHandle)
            self._mapLoadingGid2ClearTimerHandle[loadingGid] = nil
        end
    end
end

function UIManager:_OnResPathsCancelAsyncLoading(loadingGid)
    --- 超时历史检查生效
    if not VersionUtil.IsShipping() then
        logwarning("[Low Memory Log - AliveCount] AsyncLoadingAliveCount 加载超时，强制解除Histroy _OnResPathsCancelAsyncLoading(loadingGid) ", loadingGid)
    end
    
    self:OnResPathsFinishAsyncLoading(loadingGid)
    if loadingGid and self._mapLoadingGid2ClearTimerHandle and self._mapLoadingGid2ClearTimerHandle[loadingGid] ~= nil then
        self._mapLoadingGid2ClearTimerHandle[loadingGid] = nil
    end
end

---------------------------------------------------------------------------------
--- UIManager AliveCount
---------------------------------------------------------------------------------
function UIManager:InitAliveCount(uiNavId, uiTokenID)
    if uiNavId == nil or uiTokenID == nil then return 0 end
    declare_if_nil(self._mapUINavID2AliveCount, uiNavId, 0)
    local aliveCount = self._mapUINavID2AliveCount[uiNavId] + 1
    self._mapUINavID2AliveCount[uiNavId] = aliveCount

    declare_if_nil(self._mapUINavID2TokenIDMap, uiNavId, {})
    local mapTokenID2bExist = self._mapUINavID2TokenIDMap[uiNavId]
    mapTokenID2bExist[uiTokenID] = true
    return aliveCount
end

function UIManager:IsAliveCountExist(uiNavId, uiTokenID)
    declare_if_nil(self._mapUINavID2TokenIDMap, uiNavId, {})
    local mapTokenID2bExist = self._mapUINavID2TokenIDMap[uiNavId]
    return mapTokenID2bExist[uiTokenID]
end

function UIManager:GetAliveCount(uiNavId)
    if uiNavId == nil then return 0 end
    declare_if_nil(self._mapUINavID2AliveCount, uiNavId, 0)
    declare_if_nil(self._mapUINavID2TokenIDMap, uiNavId, {})
    local aliveCount = self._mapUINavID2AliveCount[uiNavId]
    return aliveCount or 0
end

function UIManager:AddAliveCount(uiNavId, uiTokenID)
    local bResult = false
    if uiNavId == nil or uiTokenID == nil then return 0, false end
    declare_if_nil(self._mapUINavID2AliveCount, uiNavId, 0)
    declare_if_nil(self._mapUINavID2TokenIDMap, uiNavId, {})
    local mapTokenID2bExist = self._mapUINavID2TokenIDMap[uiNavId]
    local aliveCount = self._mapUINavID2AliveCount[uiNavId]
    if mapTokenID2bExist[uiTokenID] == false then
        mapTokenID2bExist[uiTokenID] = true
        aliveCount = self._mapUINavID2AliveCount[uiNavId] + 1
        self._mapUINavID2AliveCount[uiNavId] = aliveCount
        bResult = true
        self:OnProcessAliveCountChanged(uiNavId, uiTokenID)
    end
    return aliveCount, bResult
end

function UIManager:DecAliveCount(uiNavId, uiTokenID)
    local bResult = false
    if uiNavId == nil or uiTokenID == nil then return 0, false end
    declare_if_nil(self._mapUINavID2AliveCount, uiNavId, 0)
    declare_if_nil(self._mapUINavID2TokenIDMap, uiNavId, {})
    local mapTokenID2bExist = self._mapUINavID2TokenIDMap[uiNavId]
    local aliveCount = self._mapUINavID2AliveCount[uiNavId]
    if mapTokenID2bExist[uiTokenID] == true then
        mapTokenID2bExist[uiTokenID] = false
        if aliveCount and aliveCount > 0 then
            aliveCount = aliveCount - 1
            self._mapUINavID2AliveCount[uiNavId] = aliveCount
        end
        bResult = true
        self:OnProcessAliveCountChanged(uiNavId, uiTokenID)
    end
    return aliveCount, bResult
end

function UIManager:OnProcessAliveCountChanged(uiNavId, uiTokenID)
    local aliveCount = self._mapUINavID2AliveCount[uiNavId] or 0
    if ALIVE_COUNT_CLEAR_SUBUI then
        local preHandle = self._mapUINavID2ClearTimerHandle[uiNavId]
        if preHandle then
            -- logerror("[Low Memory Log - AliveCount]  OnProcessAliveCountChanged Timer.CancelDelay!!! ", aliveCount, uiNavId, uiTokenID)
            Timer.CancelDelay(preHandle)
            self._mapUINavID2ClearTimerHandle[uiNavId] = nil
        end
        if aliveCount <= 0 then
            if self:IsLuaPendingKill(uiTokenID) and LUA_PENDING_KILL_ENABLE then
                self:_OnLuaUIBaseViewResChecked(aliveCount, uiNavId, uiTokenID, 0, true)
            else
                local clearTime = self._clearTimeWhenUltimating ~= 0 and self._clearTimeWhenUltimating or DEFAULT_CLEAR_RES_TIME
                -- logerror("[Low Memory Log - AliveCount]  OnProcessAliveCountChanged Timer.DelayCall ", aliveCount, uiNavId, uiTokenID)
                self._mapUINavID2ClearTimerHandle[uiNavId] = Timer.DelayCall(clearTime, self._OnLuaUIBaseViewResChecked, self, aliveCount, uiNavId, uiTokenID, clearTime, false)
            end
        end
    end
end

function UIManager:_OnLuaUIBaseViewResChecked(aliveCount, uiNavId, uiTokenID, clearTime, bLuaPendingKill)
    if aliveCount <= 0 and not VersionUtil.IsShipping() then
        local uiSettings = UITable[uiNavId]
        if uiSettings then
            if bLuaPendingKill then
                loginfo("[Low Memory Log - AliveCount - LuaPendingKill]  _OnLuaUIBaseViewResChecked AsyncLoadingAliveCount TryAliveCountClearResRef 整棵树 --------------Immediate Call ", uiSettings.BPKey, uiNavId, uiTokenID)
            else
                loginfo("[Low Memory Log - AliveCount]  _OnLuaUIBaseViewResChecked AsyncLoadingAliveCount TryAliveCountClearResRef 整棵树 --------------Timer Call ", clearTime, uiSettings.BPKey, uiNavId, uiTokenID)
            end
        end
    end
    self:TryAliveCountClearResRef(uiNavId, uiTokenID)
    self._mapUINavID2ClearTimerHandle[uiNavId] = nil
end

local function recursiveCheckAnyUINeedClearResRef(uiNavId)
    local uiSettings = UITable[uiNavId]
    if uiSettings ~= DummyUISettings then
        -- assertlog(uiSettings,"recursiveCheckAnyUINeedClearResRef load ui asset failed, UISettings is nil, Please check UITable, uiNavId:"..uiNavId)
        if uiSettings then
            local layerType = uiSettings.UILayer
            if Facade.UIManager:CheckIfDynamicSubUIListByUINavID(uiNavId) then
                for _, subUIId in ipairs(Facade.UIManager._mapUIId2DynamicSubUIIdList[uiNavId]) do
                    recursiveCheckAnyUINeedClearResRef(subUIId)
                end
            end
            if uiSettings.SubUIs then
                for _, subUIId in ipairs(uiSettings.SubUIs) do
                    recursiveCheckAnyUINeedClearResRef(subUIId)
                end
            end
            if (layerType == EUILayer.Sub) and not Facade.UIManager:CheckSubUINavIDIsAnyResRef(uiNavId) then
                Facade.UIManager.stubRuntimeUIRes:ClearResRefByPath(uiSettings.FullPath)
                Facade.UIManager.stubLoadingUIRes:ClearResRefByPath(uiSettings.FullPath)
                -- self.stubPermanentUIRes:ClearResRefByPath(uiSettings.FullPath)
                Facade.UIManager.stubRuntimeSubUIRes:ClearResRefByPath(uiSettings.FullPath)
                Facade.UIManager.stubDynamicSubUIRes:ClearResRefByPath(uiSettings.FullPath)
                if not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                    logwarning("[Low Memory Log - AliveCount] 父UI关闭时主动Check子UI列表 self.stubRuntimeUIRes:ClearResRefByPath SubUI subUIPackIns <= 0 and aliveCount <= 0 ", uiSettings.BPKey, uiNavId)
                end
            end
        else
            assertlog(uiSettings,"recursiveCheckAnyUIUnload load ui asset failed, UISettings is nil, Please check UITable, uiNavId:"..uiNavId)
            logerror('反向依赖了尚未初始化的模块的UI，UISettings is nil 请检查', UIName2ID.GetNameByID(uiNavId), debug.traceback())
        end
    end
end

function UIManager:TryAliveCountClearResRef(uiNavId, uiTokenID)
    local aliveCount = Facade.UIManager:GetAliveCount(uiNavId)
    if aliveCount <= 0 then
        local uiSettings = UITable[uiNavId]
        if uiSettings then
            local layerType = uiSettings.UILayer
            local reConfig = uiSettings.ReConfig

            if uiSettings.FullPath then
                if (layerType ~= EUILayer.Sub) then
                    if not self:CheckUINavIDIsAnyResRef(uiNavId) then
                        self.stubRuntimeUIRes:ClearResRefByPath(uiSettings.FullPath)
                        self.stubLoadingUIRes:ClearResRefByPath(uiSettings.FullPath)
                        self.stubPermanentUIRes:ClearResRefByPath(uiSettings.FullPath)
                        if not VersionUtil.IsShipping() then
                            if self:IsLuaPendingKill(uiTokenID) and LUA_PENDING_KILL_ENABLE then
                                logwarning("[Low Memory Log - AliveCount - LuaPendingKill] AsyncLoadingAliveCount self.stubRuntimeUIRes:ClearResRefByPath aliveCount <= 0 ", uiSettings.BPKey, uiNavId)
                            else
                                logwarning("[Low Memory Log - AliveCount] AsyncLoadingAliveCount self.stubRuntimeUIRes:ClearResRefByPath aliveCount <= 0 ", uiSettings.BPKey, uiNavId)
                            end
                        end
                        --- 性能损耗
                        recursiveCheckAnyUINeedClearResRef(uiNavId)
                    end
                else
                    if not self:CheckSubUINavIDIsAnyResRef(uiNavId) then
                        self.stubRuntimeUIRes:ClearResRefByPath(uiSettings.FullPath)
                        self.stubLoadingUIRes:ClearResRefByPath(uiSettings.FullPath)
                        -- self.stubPermanentUIRes:ClearResRefByPath(uiSettings.FullPath)
                        self.stubRuntimeSubUIRes:ClearResRefByPath(uiSettings.FullPath)
                        self.stubDynamicSubUIRes:ClearResRefByPath(uiSettings.FullPath)
                        if not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                            if self:IsLuaPendingKill(uiTokenID) and LUA_PENDING_KILL_ENABLE then
                                logwarning("[Low Memory Log - AliveCount - LuaPendingKill] AsyncLoadingAliveCount self.stubRuntimeUIRes:ClearResRefByPath SubUI subUIPackIns <= 0 and aliveCount <= 0 ", uiSettings.BPKey, uiNavId)
                            else
                                logwarning("[Low Memory Log - AliveCount] AsyncLoadingAliveCount self.stubRuntimeUIRes:ClearResRefByPath SubUI subUIPackIns <= 0 and aliveCount <= 0 ", uiSettings.BPKey, uiNavId)
                            end
                        end
                    end
                end
            end
        end
    end
end

function UIManager:TryLayerTypeClearResRef(UINavIDList, chosenStub)
    if self:GetIsLowMemoryState() then
        for _, uiNavId in ipairs(UINavIDList) do
            local uiSettings = UITable[uiNavId]
            if uiSettings then
                local layerType = uiSettings.UILayer
                if layerType == EUILayer.Stack then
                    chosenStub:ClearResRefByPath(uiSettings.FullPath)
                    if not VersionUtil.IsShipping() and ALIVE_COUNT_CLEAR_SUBUI_LOG then
                        logwarning("[Low Memory Log - LayerResClear] AsyncLoadingAliveCount chosenStub:ClearResRefByPath TryLayerTypeClearResRef Stack ", uiSettings.BPKey, uiNavId)
                    end
                end
            end
        end
    end
end

function UIManager:CheckUINavIDIsAnyResRef(uiNavID)
    if _WITH_EDITOR == 1 then
        local existResRefSubHistroyLoading = 0
        for loadingGid, subHistoryTb in pairs(self._mapLoadingGid2SubHistory) do
            if subHistoryTb and subHistoryTb[uiNavID] == true then
                existResRefSubHistroyLoading = existResRefSubHistroyLoading + 1
            end
        end
        if existResRefSubHistroyLoading > 0 then
            logerror("[Low Memory Log - AliveCount] CheckUINavIDIsAnyResRef AsyncLoadingAliveCount NormalUI is in async loading process ", uiNavID, UIName2ID.GetNameByID(uiNavID), existResRefSubHistroyLoading)
        end

        local resultCount = existResRefSubHistroyLoading
        if resultCount <= 0 and ALIVE_COUNT_CLEAR_SUBUI_LOG then
            logwarning("[Low Memory Log - AliveCount] CheckUINavIDIsAnyResRef NormalUI SubHistroy ref <= 0 ", uiNavID, UIName2ID.GetNameByID(uiNavID), resultCount, existResRefSubUIPack, existResRefSubHistroyParent)
        end
        return resultCount > 0
    else
        --- 优化计算
        for loadingGid, subHistoryTb in pairs(self._mapLoadingGid2SubHistory) do
            if subHistoryTb and subHistoryTb[uiNavID] == true then
                logerror("[Low Memory Log - AliveCount] CheckUINavIDIsAnyResRef AsyncLoadingAliveCount NormalUI is in async loading process ", uiNavID, UIName2ID.GetNameByID(uiNavID))
                return true
            end
        end
        return false
    end
end

function UIManager:CheckSubUINavIDIsAnyResRef(uiNavID)
    -- if not VersionUtil.IsShipping() and not VersionUtil.EnablePerformanceMode() then
    if _WITH_EDITOR == 1 then
        local existResRefSubUIPack = 0
        for weakOwner, subUIPackIns in pairs(self._mapWeakOwnerUI2Pack) do
            if not hasdestroy(weakOwner) and subUIPackIns and subUIPackIns:IsAnyInsNeedResRef(uiNavID) then
                existResRefSubUIPack = existResRefSubUIPack + 1
            end
        end
        local existResRefSubHistroyParent = 0
        for weakOwner, subHistoryTb in pairs(self._mapWeakOwerIns2bSubHistory) do
            if not hasdestroy(weakOwner) and subHistoryTb and subHistoryTb[uiNavID] == true then
                existResRefSubHistroyParent = existResRefSubHistroyParent + 1
            end
        end

        local existResRefSubHistroyLoading = 0
        for loadingGid, subHistoryTb in pairs(self._mapLoadingGid2SubHistory) do
            if subHistoryTb and subHistoryTb[uiNavID] == true then
                existResRefSubHistroyLoading = existResRefSubHistroyLoading + 1
            end
        end
        if existResRefSubHistroyLoading > 0 then
            logerror("[Low Memory Log - AliveCount] CheckSubUINavIDIsAnyResRef AsyncLoadingAliveCount SubUI is in async loading process ", uiNavID, UIName2ID.GetNameByID(uiNavID), existResRefSubHistroyLoading)
        end

        local resultCount = existResRefSubUIPack + existResRefSubHistroyParent + existResRefSubHistroyLoading
        if resultCount <= 0 and ALIVE_COUNT_CLEAR_SUBUI_LOG then
            logwarning("[Low Memory Log - AliveCount] CheckSubUINavIDIsAnyResRef SubUIOwnerPack ref <= 0 && SubHistroy ref <= 0 ", uiNavID, UIName2ID.GetNameByID(uiNavID), resultCount, existResRefSubUIPack, existResRefSubHistroyParent)
        end
        return resultCount > 0
    else
        --- 优化计算
        for weakOwner, subUIPackIns in pairs(self._mapWeakOwnerUI2Pack) do
            if not hasdestroy(weakOwner) and subUIPackIns and subUIPackIns:IsAnyInsNeedResRef(uiNavID) then
                return true
            end
        end

        for weakOwner, subHistoryTb in pairs(self._mapWeakOwerIns2bSubHistory) do
            if not hasdestroy(weakOwner) and subHistoryTb and subHistoryTb[uiNavID] == true then
                return true
            end
        end

        for loadingGid, subHistoryTb in pairs(self._mapLoadingGid2SubHistory) do
            if subHistoryTb and subHistoryTb[uiNavID] == true then
                logerror("[Low Memory Log - AliveCount] CheckSubUINavIDIsAnyResRef AsyncLoadingAliveCount SubUI is in async loading process ", uiNavID, UIName2ID.GetNameByID(uiNavID))
                return true
            end
        end
        return false
    end
end

local originalRefSetting = FINAL_CLOSE_CLEAR_REF
function UIManager:UltimateClearAllPoolWithRes(bCallFullGC, bImmediateCallGC)
    bCallFullGC = setdefault(bCallFullGC, false)
    bImmediateCallGC = setdefault(bImmediateCallGC, false)
    if not VersionUtil.IsShipping() then
        logwarning("[Low Memory Log - AliveCount]  UltimateClearAllPoolWithRes Start!!! ", bCallFullGC, bImmediateCallGC)
    end
    self._clearTimeWhenUltimating = 0.05
    FINAL_CLOSE_CLEAR_REF = true
    self:_InternalClearPool()

    if self._ultimateFinishTimer then
        Timer.CancelDelay(self._ultimateFinishTimer)
        self._ultimateFinishTimer = nil
    end

    if LUA_PENDING_KILL_ENABLE then
        self:UltimateClearFinish(bCallFullGC, bImmediateCallGC, true, 0)
    else
        local delayFullGCTime = 0.2
        if bImmediateCallGC == false then
            delayFullGCTime = ULuaSubsystem.GetLuaReleaseCallGCDelayTime()
        end
        self._ultimateFinishTimer = Timer.DelayCall(delayFullGCTime,function(self)
            self:UltimateClearFinish(bCallFullGC, bImmediateCallGC, false, delayFullGCTime)
        end, self)
    end
end

function UIManager:UltimateClearFinish(bCallFullGC, bImmediateCallGC, bImmediateOperation, delayFullGCTime)
     self._clearTimeWhenUltimating = 0
    FINAL_CLOSE_CLEAR_REF = originalRefSetting
    if bCallFullGC then
        ULuaSubsystem.CheckFullGC(bImmediateCallGC, false)
    end
    if not VersionUtil.IsShipping() then
        if bImmediateOperation then
            logwarning("[Low Memory Log - AliveCount]  UltimateClearAllPoolWithRes Finish!!! Immediate Call ", 0, bCallFullGC, bImmediateCallGC)
        else
            logwarning("[Low Memory Log - AliveCount]  UltimateClearAllPoolWithRes Finish!!! TimerCall", delayFullGCTime, bCallFullGC, bImmediateCallGC)
        end
    end
    self.Events.evtUltimateClearFinished:Invoke(bCallFullGC)
end

return UIManager
