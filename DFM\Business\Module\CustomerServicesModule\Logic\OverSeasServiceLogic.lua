----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCustomerServices)
----- LOG FUNCTION AUTO GENERATE END -----------




local OverSeasServiceLogic = {}
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local UAppSetting = import "AppSetting"
local UWeGameManager = nil
local WeGameManager = nil
if PLATFORM_WINDOWS then
	UWeGameManager = import "WeGameSDKManager"
	WeGameManager = UWeGameManager.Get(GetGameInstance())
end
local UGameGPM = import "DFMGameGPM"

local this = OverSeasServiceLogic

function OverSeasServiceLogic._SaltEncode(_kf_params,_salt)
	local bytes = {};
	for i = 1, #_kf_params do
		local cur_unicode = string.byte(_kf_params, i) + _salt;
		if( cur_unicode >= 33 and cur_unicode <= 126 ) then
			bytes[i] = cur_unicode;
		end
		if( cur_unicode > 126 ) then
			bytes[i] = 32 + cur_unicode - 126;
		end
		if( cur_unicode < 33) then
			bytes[i] = 126 - (32 .. cur_unicode);
		end
	end

	return bytes;
end

function OverSeasServiceLogic._BytesToStr(_bytes)
	local result = "";
	for i = 1, #_bytes do
		local cur_str = string.char(_bytes[i]);
		result = result .. cur_str;
	end

	return result
end

function OverSeasServiceLogic._urlEncode(s)
	s = string.gsub(s, "([^%w%.%- ])", function(c) return string.format("%%%02X", string.byte(c)) end)
	return string.gsub(s, " ", "+")
end

function OverSeasServiceLogic._urlDecode(s)
	s = string.gsub(s, '%%(%x%x)', function(h) return string.char(tonumber(h,16)) end)
	return s
end

function OverSeasServiceLogic._GetFreshSalt()
    -- 加密key
    local timestamp = os.time();
    local salt = timestamp % 10;
    if ( salt == 9 ) then
        salt = 8
    end

    if ( salt == 0 ) then
        salt = 1
    end
    return timestamp, salt
end

function OverSeasServiceLogic._GetPlatId()
	if PLATFORM_IOS then
		return Module.CustomerServices.Config.PlatIds.IOS
	elseif PLATFORM_ANDROID then
		return Module.CustomerServices.Config.PlatIds.Android
	elseif PLATFORM_OPENHARMONY then
		return Module.CustomerServices.Config.PlatIds.OpenHarmaoney
	elseif PLATFORM_PS4 then

	-- BEGIN MODIFICATION @ VIRTUOS: 在XBOX平台启用客服按钮
	elseif PLATFORM_PS5 then
		return Module.CustomerServices.Config.PlatIds.PS5
	elseif PLATFORM_XSX then
	-- END MODIFICATION
		return Module.CustomerServices.Config.PlatIds.XBox
	elseif PLATFORM_SWITCH then
		return Module.CustomerServices.Config.PlatIds.NintendoSwitch
	elseif PLATFORM_WINDOWS then
		if WeGameManager ~= nil and IsWeGameEnabled() then
			if WeGameManager:GetPlatformType() == UE.EWeGamePlatformType.PlatformTypeSteam then
				return Module.CustomerServices.Config.PlatIds.Steam
			elseif WeGameManager:GetPlatformType() == UE.EWeGamePlatformType.PlatformTypeEpic then
				return Module.CustomerServices.Config.PlatIds.Epic
			end
			return Module.CustomerServices.Config.PlatIds.Windows
		end
	end
	return Module.CustomerServices.Config.PlatIds.Windows
end

function OverSeasServiceLogic._MakeCNParamStr(SceneId)

end

-- 获取客户端时区
function OverSeasServiceLogic._GetClientTimeZone()
	local now = os.time()
	local difftime = os.difftime(now, os.time(os.date("!*t", now)))
	return difftime / 3600
end

function OverSeasServiceLogic._MakeGlobalParamStr(SceneId)
	local isLogin = Server.AccountServer:GetPlayerId() ~= 0

	local ParamStr = ""
	if isLogin then
		local eParamStr = ""
		eParamStr = "openid=" .. Server.SDKInfoServer:GetOpenIdStr()

		if Server.SDKInfoServer:GetChannel() ~=nil then
			eParamStr = eParamStr .. "&" .. "channelid=" .. Server.SDKInfoServer:GetChannel()
		end

		eParamStr = eParamStr .. "&" .. "area_id=" .. Facade.ConfigManager:GetString("lastUserAreaId", "")
		loginfo("OverSeasServiceLogic:_MakeGlobalParamStr-eParamStr,before encrypt:" , eParamStr)

		local kftimestamp, Salt = this._GetFreshSalt()
		local eParamBytes = this._SaltEncode(eParamStr, Salt)
		eParamStr = this._BytesToStr(eParamBytes)
		loginfo("OverSeasServiceLogic:_MakeGlobalParamStr-eParamStr,after encrypt:" , eParamStr)

		ParamStr = "?encryption=" .. eParamStr .. "&kftimestamp=" ..  kftimestamp
	end

	if string.len(ParamStr) > 0 then
		ParamStr = ParamStr .. "&" .. "scene_id=" .. SceneId
	else
		ParamStr = "?scene_id=" .. SceneId
	end

	ParamStr = ParamStr .. "&" .. "platid=" .. this._GetPlatId()

	ParamStr = ParamStr .. "&" .. "appid=" .. UAppSetting.Get().SdkGameId

	ParamStr = ParamStr .. "&" .. "version=" .. VersionUtil.GetVersionFull()

	ParamStr = ParamStr .. "&" .. "timeZone=" .. this._GetClientTimeZone()

	if isLogin then
		if Server.RoleInfoServer.nickName ~= nil and string.len(Server.RoleInfoServer.nickName) > 0 then
			loginfo("OverSeasServiceLogic:_MakeGlobalParamStr-nickName:" , Server.RoleInfoServer.nickName)
			ParamStr = ParamStr .. "&" .. "role=" .. this._urlEncode(Server.RoleInfoServer.nickName)			
		end
		if Server.RoleInfoServer.picUrl ~= nil and string.len(Server.RoleInfoServer.picUrl) > 0 then
			loginfo("OverSeasServiceLogic:_MakeGlobalParamStr-picUrl:" , Server.RoleInfoServer.picUrl)
			ParamStr = ParamStr .. "&" .. "roleicon=" .. this._urlEncode(Server.RoleInfoServer.picUrl)
		end
	end

	if IsMobile() then
		--local safeZone = {}
		--safeZone.SafePadding = FVector4(0, 0, 0, 0)
		--safeZone.SafePaddingScale = FVector2D(0, 0)
		--safeZone.SpillOverPadding = FVector4(0, 0, 0, 0)
		--safeZone.SafePadding,safeZone.SafePaddingScale,safeZone.SpillOverPadding = UWidgetBlueprintLibrary.GetSafeZonePadding(
		--		GetWorld(),
		--		safeZone.SafePadding,
		--		safeZone.SafePaddingScale,
		--		safeZone.SpillOverPadding)
		--ParamStr = ParamStr .. "&" .. "safe="..safeZone.SafePadding.X
	end

	local isAgreeUsePrivateData = Facade.ConfigManager:GetBoolean("ReadServiceFlag", false)
	if isAgreeUsePrivateData then
		local deviceModel =  ULuaExtension.GetDeviceMakeAndModel()
		local deviceId = ULuaExtension.GetDeviceID()
		local xid = ""
		if UGameGPM then
			xid = UGameGPM.GetXID()
		end
		local deviceInfo = "device=" .. deviceModel .. "&" .. "deviceId=" .. deviceId .. "&" .. "xid=" .. xid
		if PLATFORM_IOS then
			local UDFMGameTDM = import "DFMGameTDM"
			local tdmIns = UDFMGameTDM.Get(GetGameInstance())
			if tdmIns then
				local idfv = tdmIns:GetIDFV() or ""
				deviceInfo = deviceInfo .. "&" .. "idfv="..idfv
			end
		end
		loginfo("deviceInfo:" , deviceInfo)
		local dataenc = ULuaExtension.XOREncodeData(deviceInfo , Module.CustomerServices.Config.DeviceInfoEncKey)
		--loginfo("enc deviceInfo:" , dataenc)
		--loginfo("dec deviceInfo:" , ULuaExtension.XOREncodeData(dataenc , Module.CustomerServices.Config.DeviceInfoEncKey))

		if dataenc then
			ParamStr = ParamStr .. "&" .. "data_enc=" .. dataenc
		end
	end
	local osVersion = ULuaExtension.GetOSVersion()
	if osVersion then
		ParamStr = ParamStr .. "&" .. "osVersion=" ..osVersion
	end

	if LocalizeTool.GetCurrentCulture() ~= nil then
		ParamStr = ParamStr .. "&" .. "lang_type=" .. LocalizeTool.GetCurrentCulture()
	end

	if isLogin and Server.SDKInfoServer:GetRegionNumericCode() ~= nil then
		ParamStr = ParamStr .. "&" .. "sCountry=" ..Server.SDKInfoServer:GetRegionNumericCode()
	end

	if isLogin and Server.SDKInfoServer:GetCurrentLoginRegion() ~= nil then
		ParamStr = ParamStr .. "&" .. "region=" ..Server.SDKInfoServer:GetCurrentLoginRegion()
	end
	-- BEGIN MODIFICATION @ VIRTUOS: 在Console平台启用客服按钮
	if PLATFORM_WINDOWS or PLATFORM_XSX or PLATFORM_PS5 then
	-- END MODIFICATION
		ParamStr = ParamStr .. "&" .. "topc=1"
	end

	-- BEGIN MODIFICATION @ akihikofeng: 在Console平台增加二维码供移动端使用
	if PLATFORM_XSX or PLATFORM_PS5 then
		logerror("[akihikofeng] console overseas add tohost")
		ParamStr = ParamStr .. "&" .. "tohost=1"
	end
	-- END MODIFICATION

	loginfo("OverSeasServiceLogic:_MakeGlobalParamStr-final ParamStr:" , ParamStr)

	return ParamStr
end

function OverSeasServiceLogic.CallServiceByScene(SceneId, bUseInsideWebView)
	loginfo("OverSeasServiceLogic:CallServiceByScene-" , SceneId)
	
	local UrlKey = "CN"
	if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
		UrlKey = "Global"
	end
	if not VersionUtil.IsShipping() then
		UrlKey = UrlKey .. "_Test"
	end

	loginfo("UrlKey:" , UrlKey)
	local BaseURL = Module.CustomerServices.Config.ServiceURLs[UrlKey]
	if BaseURL == nil or string.len(BaseURL) == 0 then
		logerror("BaseURL is nil,current UrlKey is " , UrlKey)
		return
	end

	local ExtURL = ""
	if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
		ExtURL = this._MakeGlobalParamStr(SceneId)
	elseif IsBuildRegionCN() then
		logerror("customer service logic for cn is in CustomerServicesLogic.lua")
		return
	end

	local FinalURL = BaseURL .. ExtURL

	local extraJson = "{\"notch_full_screen\": 1, \"BG_COLOR\": \"000000\",\"PROGRESS_TYPE\": 1}"

	local isLogin = Server.AccountServer:GetPlayerId() ~= 0
	if isLogin then
		FinalURL = Module.GCloudSDK:GetEncodeUrl(FinalURL)
	end

	-- BEGIN MODIFICATION @ VIRTUOS: 在XBOX,PS5平台启用客服按钮
	if (PLATFORM_WINDOWS or PLATFORM_XSX or PLATFORM_PS5) and bUseInsideWebView == false then
	-- END MODIFICATION
		Module.GCloudSDK:LaunchURL(FinalURL)
	else
		Module.GCloudSDK:OpenUrl(FinalURL, UE.EINTLWebViewOrientation.kLandscape, false, true, extraJson, false)
	end
end

function OverSeasServiceLogic.GarenaCallService()
	loginfo("OverSeasServiceLogic:GarenaCallService")
	local GarenaCode2Region = Facade.TableManager:GetTable("GarenaCode2Region")
	local GarenaRegion2Url = Facade.TableManager:GetTable("GarenaRegion2Url")
	local countryCode = Server.PayServer:GetRegistrationnumericCode()
	if countryCode == "" then
		countryCode = 0
	end
	countryCode = string.format("%03d", countryCode)
	loginfo("OverSeasServiceLogic.GarenaCallService CountryCode:", countryCode)
	local region = "Other"
	for k, v in pairs(GarenaCode2Region) do
		if countryCode == v.CountryCode then
			region = v.Region
		end
	end
	local url = "https://csredirect.sea.df.garena.sg"
	for k, v in pairs(GarenaRegion2Url) do
		if region == v.Region then
			url = v.CustomerServiceUrl
		end
	end
	loginfo("OverSeasServiceLogic.GarenaCallService url:", url)

	-- if PLATFORM_WINDOWS then
	-- 	Module.GCloudSDK:LaunchURL(url)
	-- else
	-- 	Module.GCloudSDK:OpenUrl(url, 3, false,false, "",true)
	-- end
	Module.GCloudSDK:OpenUrl(url, 3, false,false, "", true, {
		useSDKWebBrowser = true,
		additionalParams = "access_token"
	})
end

return OverSeasServiceLogic
