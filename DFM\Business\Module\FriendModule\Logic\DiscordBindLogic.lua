----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMAccountBind)
----- LOG FUNCTION AUTO GENERATE END -----------



local DiscordBindLogic = {}

local UGameLogin = import "DFMGameLogin"
local UGameFriend = import "DFMGameFriend"
local GameFriendMgr = UGameFriend.Get(GetWorld())
local UGameLoginIns = nil
if UGameLogin then
    UGameLoginIns = UGameLogin.Get(GetGameInstance())
end

DiscordBindLogic.evtDiscordBindFriendSuccess = LuaEvent:NewIns("DiscordBindLogic.evtDiscordBindFriendSuccess")

function DiscordBindLogic.InitDiscordBindLogic()
    -- if GameFriendMgr and IsBuildRegionGlobal() then
    if GameFriendMgr and IsBuildRegionGlobal() and not IsInEditor() then
        Module.Login.Config.Events.evtOnLoginSuccess :AddListener(DiscordBindLogic.QueryUserInfo)
        UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Add(DiscordBindLogic.CheckDiscordBindInfo)
        GameFriendMgr.OnDiscordBaseResultNotifyEvent :Add(DiscordBindLogic.OnDiscordFriendAuthorizeResult)
    end
end

function DiscordBindLogic.DeInitDiscordBindLogic()
    if GameFriendMgr and IsBuildRegionGlobal() then
        UGameLoginIns.OnLoginQueryUserInfoRetDelegate:Clear()
        GameFriendMgr.OnDiscordBaseResultNotifyEvent :Clear()
    end
end

-----------------------------------------------------------------------
--region 请求用户信息

--- 登陆后查询好友信息
function DiscordBindLogic.QueryUserInfo()
    Module.Friend.Field:SetIsManulBindDiscordFriend(true)
    return UGameLoginIns:QueryUserInfo()
end

function DiscordBindLogic.CheckDiscordBindInfo(tab)
    loginfo("DiscordBindLogic.QueryUserInfoCallback CheckDiscordBindInfo")
    logtable(tab)
    
    local bIsDiscordBinded = false
    local discordOpenId = 0
    if tab.bindList ~= "" then
        local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
        local Json = JsonFactory.createJson()
        local bindList = Json.decode(tab.bindList)
        if bindList then
            for _,bindInfo in pairs(bindList) do
                if bindInfo and bindInfo.channelid == EChannelType.kChannelDiscord then
                    loginfo("DiscordBindLogic.CheckDiscordBindInfo Discord Binded!")
                    bIsDiscordBinded = true
                    discordOpenId = bindInfo.openid -- nil
                end
            end
        end
    end
    Module.Friend.Field:SetIsDiscordBinded(bIsDiscordBinded)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Discord好友相关

--- 用户首次绑定Discord好友
function DiscordBindLogic.DiscordBindFriend()
    loginfo("DiscordBindLogic.DiscordBindFriend Manual Bind")
    return UGameFriend.DiscordAuthorize()
end

--- 检查Discord绑定状态
function DiscordBindLogic.CheckDiscordFriendBindState()
    local bIsDiscordBinded = Module.Friend.Field:GetIsDiscordBinded()
    local bIsFriendBinded  = Module.Friend.Field:GetIsDiscordFriendBinded()
    loginfo("DiscordBindLogic.CheckDiscordFriendBindState bIsDiscordBinded:", bIsDiscordBinded)
    loginfo("DiscordBindLogic.CheckDiscordFriendBindState bIsFriendBinded:", bIsFriendBinded)
    if bIsDiscordBinded and not bIsFriendBinded then
        loginfo("DiscordBindLogic.CheckDiscordFriendBindState Auto Bind")
        return UGameFriend.DiscordAuthorize()
    end
end

--- 接受Discord绑定好友信息回调
function DiscordBindLogic.OnDiscordFriendAuthorizeResult(method_id, ret_code, ret_msg, third_code, third_msg, extra_json)
    loginfo("DiscordBindLogic.OnDiscordBaseResult", ret_code)
    if ret_code == 0 then
        --- 获取绑定信息
        -- UGameLoginIns:QueryUserInfo()
        Module.Friend.Field:SetIsDiscordBinded(true)
        Module.Friend.Field:SetIsDiscordFriendBinded(true)
        
        DiscordBindLogic.evtDiscordBindFriendSuccess:Invoke()
        
        UGameFriend.SetDiscordOnline()  -- 设置Discord丰富态，仅在线状态
        
        --- she3.0由前台自行请求好友数据
        Server.FriendServer:SDKFetchDiscordFriendByClient(1)
    else
        Module.Friend.Field:SetIsDiscordBinded(false)
        Module.Friend.Field:SetIsDiscordFriendBinded(false)
        Module.CommonTips:ShowSimpleTip(Module.AccountBind.Config.Loc.GarenaAccountBindCancelledInfo)
        
        if not VersionUtil.IsShipping() then
            if ret_code == 9999 then
                loginfo("DiscordBindLogic.OnDiscordBaseResult third_code", third_code)
                loginfo("DiscordBindLogic.OnDiscordBaseResult third_msg", third_msg)
                Module.CommonTips:ShowSimpleTip("NoShippingTips : Third-party error, Message has been copied to Clipboard")
                ULuautils.ClipboardCopy(tostring(third_code))
            elseif ret_code == 5 then
                loginfo("DiscordBindLogic.OnDiscordBaseResult ret_msg", ret_msg)
                Module.CommonTips:ShowSimpleTip("NoShippingTips : INTL-Server error")
                ULuautils.ClipboardCopy(tostring(ret_msg))
            else
                loginfo("DiscordBindLogic.OnDiscordBaseResult ret_msg", ret_msg)
                Module.CommonTips:ShowSimpleTip("NoShippingTips : Other error")
                ULuautils.ClipboardCopy(tostring(ret_msg))
            end
        end
    end

    -- 经分上报
    if Module.Friend.Field:GetIsManulBindDiscordFriend() then
        LogAnalysisTool.DoSendDiscordManulBindResLog(ret_code == 0)
        Module.Friend.Field:SetIsManulBindDiscordFriend(false)
    end
end

--endregion
-----------------------------------------------------------------------

return DiscordBindLogic