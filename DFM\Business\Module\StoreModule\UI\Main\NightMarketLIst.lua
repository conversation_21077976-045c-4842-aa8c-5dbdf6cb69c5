----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class NightMarketList : LuaUIBaseView
local NightMarketList = ui("NightMarketList")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"

function NightMarketList:Ctor()
    self._dfBox = self:Wnd("DFHorizontalBox_104", UIWidgetBase)

    self._cardItem1 = self:Wnd("WBP_Store_NM_CardItem_01", UIWidgetBase)
    self._cardItem2 = self:Wnd("WBP_Store_NM_CardItem_02", UIWidgetBase)
    self._cardItem3 = self:Wnd("WBP_Store_NM_CardItem_03", UIWidgetBase)
    self._cardItem4 = self:Wnd("WBP_Store_NM_CardItem_04", UIWidgetBase)
    self._cardItem5 = self:Wnd("WBP_Store_NM_CardItem_05", UIWidgetBase)
    self._cardItemList = { self._cardItem1, self._cardItem2, self._cardItem3, self._cardItem4, self._cardItem5 }

    self._wtCurrencyImg = self:Wnd("DFImage_53", UIImage)
    self._wtCurrencyNum = self:Wnd("DFTextBlock", UITextBlock)

    self._wtTime = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtDay = self:Wnd("DFTextBlock_3", UITextBlock)

    self._wtTipBtn = self:Wnd("wtTipsBtn", UICheckBox)
    self._wtTipBtn:Event("OnCheckStateChanged", self.OnTipBtnClick, self)

    self._wtLine = self:Wnd("DFImage_1", UIImage) -- 竖线
end

function NightMarketList:OnInitExtraData()
end

function NightMarketList:OnShowBegin()
    if Server.StoreServer:IsHiddenBoxRemain() then
        self:OnUpdataTime()
        self._wtTime:Visible()
        self._wtLine:Visible()
    else
        self._wtTime:Collapsed()
        self._wtLine:Collapsed()
    end
    self:OnUpdataDayTime()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

function NightMarketList:SetCard(goods)
    for index, card in ipairs(self._cardItemList) do
        if goods[index] ~= nil then
            card:SetData(goods[index], 1, true)
        end
    end
end

function NightMarketList:OnTipBtnClick()
    local itemDescDataList = {}
    table.insert(itemDescDataList, LocalizeTool.GetTransStr("LOC_StoreLuckyNest,LuckyNest_Rules"))
    Module.CommonWidget:ShowCommonPopWindowV2Waterfall(nil, nil, StoreConfig.Loc.StaffLotteryPopTitle, 
    nil, itemDescDataList, true)
end

function NightMarketList:OnUpdataTime()
    local timestamp = Facade.ClockManager:GetServerTimestamp()  -- 当前时间戳

    -- 获取今天午夜00:00:00的时间戳
    local midnight_timestamp = os.time({year = os.date('%Y', timestamp), month = os.date('%m', timestamp), day = os.date('%d', timestamp), hour = 24, min = 0, sec = 0})

    local seconds_until_midnight = midnight_timestamp - timestamp

    local hour = math.floor(seconds_until_midnight / 3600)
    local min = math.floor((seconds_until_midnight % 3600) / 60)
    showStr = string.format(StoreConfig.Loc.StoreNMBuyLimitTimeHourTip, hour, min)

    self._wtTime:SetText(showStr)
end

function NightMarketList:OnUpdataDayTime()
    local timestamp = Server.StoreServer:GetNMEndTime()

    local month = tonumber(os.date('%m', timestamp)) 
    local day = tonumber(os.date('%d', timestamp)) 

    showStr = string.format(StoreConfig.Loc.StoreNMBuyLimitTimeDayTip, 12, 31)

    self._wtDay:SetText(showStr)
end

function NightMarketList:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function NightMarketList:Func()
end

function NightMarketList:_EnableGamepadFeature()
    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_WaterFall = nil
    -- 设置导航
    if self._dfBox then
        if not self._NavGroup_WaterFall then
            self._NavGroup_WaterFall = WidgetUtil.RegisterNavigationGroup(self._dfBox , self, "Hittest")
        end
    end

    if self._NavGroup_WaterFall then
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._cardItem1)
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._cardItem2)
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._cardItem3)
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._cardItem4)
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._cardItem5)
        self._NavGroup_WaterFall:SetScrollRecipient(self._dfBox)
        self._NavGroup_WaterFall:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Default)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WaterFall)
    end

    self:_SetBottomBar()
end

function NightMarketList:_SetBottomBar()

    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
    local summaryList = {}
    table.insert(summaryList, {actionName = "SelectForStore", func = nil, caller = self, bUIOnly = false, bHideIcon = false})

    table.insert(summaryList, {actionName = "Assembly_Confirm",func = self.OnTipBtnClick, caller = self ,bUIOnly = false, bHideIcon = false})   


    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function NightMarketList:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
    WidgetUtil.RemoveNavigationGroup(self)
end

return NightMarketList