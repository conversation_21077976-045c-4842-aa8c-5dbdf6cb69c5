----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



local ChatMsgWidgetSettlement = ui("ChatMsgWidgetSettlement")

local EDFMGamePlayMode = import "EDFMGamePlayMode"
local EBreakthroughStage = import("EBreakthroughStage")
local UGPGameHudDelegates = import "GPGameHudDelegates"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local UHUDStateManager = UE.HUDStateManager
local EGameHUDState = import "GameHUDSate"

function ChatMsgWidgetSettlement:Ctor()
    logframe("ChatMsgWidget:Ctor")
    self._wtDFMButton = self:Wnd("ShowMsgBtn", UIButton)
    self._wtDFMButton:Event("OnPressed", self._OnDFMButtonPressed, self)
    self._wtDFMButton:Event("OnReleased", self._OnDFMButtonReleased, self)
    self._wtTypeWidget = self:Wnd("WBP_ControllerButtonType",UIWidgetBase)
    self:AddLuaEvent(Module.Chat.Config.evtSelectMsgStateUI, self._OnRefreshSelectMsgState, self)
    --self:AddLuaEvent(Server.ChatServer.evtChatAppointChange, self._OnChatAppointChange, self)
    --红点
    --self._reddot = Module.Reddot:Register(self._wtDFMButton,EReddotType.Normal)
    local key = string.format("ChatUnreadMsg_Type%s", 0)
    local data = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.ChatMsgWidget, key)
    self._reddot = Module.ReddotTrie:RegisterStaticReddotDot(self._wtDFMButton,{{reddotData=data,reddotStyle={reddotType = EReddotType.Normal}}})

    self._btnEnable = true
end

function ChatMsgWidgetSettlement:Destroy()
    self._reddot=nil
end

function ChatMsgWidgetSettlement:OnHide()
    Module.Chat.Config.evtHiddenMsgWidget:Invoke()
end

function ChatMsgWidgetSettlement:_OnRefreshSelectMsgState()
    logframe("ChatMsgWidget:_OnRefreshSelectMsgState")
    self:BP_SetMsgWidgetState(1)
    self._wtTypeWidget:SetIsPressed(false)
    self._btnEnable = true
end

-- 0 for X
function ChatMsgWidgetSettlement:_OnDFMButtonPressed()
    logframe("ChatMsgWidget:_OnDFMButtonPressed")
    if self._btnEnable then
        self._wtTypeWidget:SetIsPressed(true)
        self._btnReleaseEnable = true
    else
        self._btnReleaseEnable = false
    end
end

function ChatMsgWidgetSettlement:_OnDFMButtonReleased()
    logframe("ChatMsgWidget:_OnDFMButtonReleased",self._btnEnable)
    if self._btnReleaseEnable then
        self._btnEnable = false
        Module.GVoice:OpenMsgPanel(self._wtDFMButton,
        nil, nil,
        LuaGlobalConst.TOP_RIGHT_VECTOR,
        LuaGlobalConst.TOP_LEFT_VECTOR,
        5)
        self:BP_SetMsgWidgetState(0)
    end
end

function ChatMsgWidgetSettlement:OnOpen()
    self._wtTypeWidget:SetIsPressed(false)
end

function ChatMsgWidgetSettlement:OnClose()
    if self._reddot ~= nil then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._reddot)
    end

    if self._msgPanelHandle ~= nil then
        Facade.UIManager:CloseUIByHandle(self._msgPanelHandle)
    end
    
    local world=GetWorld()
    if isinvalid(world) then
        logframe("UnRegisterDelegate isinvalid(world) return")
        return
    end
end

return ChatMsgWidgetSettlement