----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local ArmedForcePresetLogic = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForcePresetLogic"
local ArmedForceApplyLogic  = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.ArmedForceApplyLogic"
local CurrencyHelperTool    = require "DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
local WarehouseContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD"
local QuickOperationLogic         = require "DFM.Business.Module.ArmedForceModule.Logic.QuickOperation.QuickOperationLogic"
local OutfitDataLogic            = require "DFM.Business.Module.ArmedForceModule.Logic.ArmedForce.OutfitDataLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
-- 导入C++定义的枚举类型
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import  "EGPInputType"

local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
---@class AssemblyHDPresetMainPanel : LuaUIBaseView
local AssemblyHDPresetMainPanel = ui("AssemblyHDPresetMainPanel")
local ArmedForceConfig = Module.ArmedForce.Config
local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent
local EIVSlotPos = Module.CommonWidget.Config.EIVSlotPos
local EIVCompOrder = Module.CommonWidget.Config.EIVCompOrder
local EMouseCursor = import("EMouseCursor")

local function log(...)
	loginfo("[AssemblyHDPresetMainPanel]", ...)
end



function AssemblyHDPresetMainPanel:Ctor()
    self._curPriceValue = 0
    self._bApplyAll = true
    self._systemMainTabGroupDataList = {}
    self._customMainTabGroupDataList = {}
    self._curOutfitID = nil

    self._customTabIndex = -1
    self._systemTabIndex = -1
    self._customTabItems = {}
    self._systemTabItems = {}

    -- 请求方案道具价格Timer
    self._fetchOutfitDataTimerHandles = {}
    self._bFetchOutfitDataCD = {}

    self._wtMainRoot = self:Wnd("MainRoot", UIWidgetBase)

    self._wtNotInteractive = self:Wnd("wtNotInteractive", UIImage)
    self:_InitContainerSlotViews()
    self:_DisabelHoverTipsFocus()
    self._wtCustomDFCommonMainTabList = self:Wnd("wtCustomDFCommonMainTabList", DFCommonMainTabList)
    self._wtSystemDFCommonMainTabList = self:Wnd("wtSystemDFCommonMainTabList", DFCommonMainTabList)

    self._wtCustomDFCommonMainTabList:SetDefaultFocus(false)
    self._wtSystemDFCommonMainTabList:SetDefaultFocus(false)

    self._wtAssemblyMapNeed = self:Wnd("WBP_AssemblyMapNeed", UIWidgetBase)
	-- self._wtDrugRoot = self:Wnd("wtDrugRoot", UIWidgetBase)
	self._wtConfirmBtn = self:Wnd("WBP_CommonButtonV1S1", DFCommonButtonOnly)
    self:SetInteractive(false)
    self._wtSavedPlanBtn = self:Wnd("WBP_DFCommonButtonV1S2", DFCommonButtonOnly)
    self._wtSavedPlanBtn:Event("OnClicked", self._OnSavedPlanBtnClicked, self)
    self._wtSavedPlanBtn:Event("OnDeClicked", self._OnSavedPlanBtnDeClicked, self)

    self._wtMedicineOperationBtn = self:Wnd("WBP_CommonButtonV3S1_1", DFCommonButtonOnly)
    self._wtMedicineOperationBtn:Event("OnClicked", self._OnMedicineOperationBtnClicked, self)

    self._wtBulletOperationBtn = self:Wnd("WBP_CommonButtonV3S1_2", DFCommonButtonOnly)
    self._wtBulletOperationBtn:Event("OnClicked", self._OnBulletOperationBtnClicked, self)
	-- self._wtImageCurrency = self:Wnd("Image_Currency", UIImage)
	-- self._wtCurrencyValue = self:Wnd("wtTextBlock_value", UITextBlock)
    self._wtWeightBtn = self:Wnd("WBP_WeightButton", WeightButton)

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    Module.CommonBar:RegStackUITopBarTitle(self, Module.ArmedForce.Config.Loc.PresetTitle)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self._wtSlotViews = self:MultiWnd("WBP_AssemblyEquipSlotView")
        self._wtCustomNavCanvas = self:Wnd("CanvasPanel_1", UIWidgetBase)
        self._wtCustomNavCanvas_Buttons = self:Wnd("DFCanvasPanel_100", UIWidgetBase)
    end
    --- END MODIFICATION
end

--==================================================
--region Life function
function AssemblyHDPresetMainPanel:OnOpen()
	self:AddLuaEvent(Module.ArmedForce.Config.evtApplyOutfitFinished, self._OnApplyOutfitFinished, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtApplyOutfitError, self._evtApplyOutfitError, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtUpdateOutfitEquipPosition, self._OnRefreshBtnState, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtPresetFitSampleItem, self._OnPresetFitSampleItem, self)
    self:AddLuaEvent(Server.PresetServer.Events.evtCustomOutfitNameUpdate, self.RefreshCustomTabName, self)
    self:AddLuaEvent(Server.PresetServer.Events.evtCustomOutfitUpdate, self.RefreshCustomOutfit, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtPresetFitSampleItemFinished, self._OnPresetFitSampleItemFinished, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtInputSummaryListChanged, self.SetInputSummary, self)
    self._curOutfitID = nil
    self:GenLiveTabList() -- 生成页签数据
    -- self._wtCustomWaterFallList:RefreshAllItems()
    -- self._wtSystemWaterFallList:RefreshAllItems()
    Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.OutFit)
	-- -- 关闭时的确认弹窗
	-- Module.CommonBar:BindBackHandler(self.CloseMainPanel, self)
    Module.CommonBar:BindPersistentBackHandler(self.CloseMainPanel, self)
end

function AssemblyHDPresetMainPanel:OnActivate()
    self._curOutfitID = nil
    self:GenLiveTabList() -- 生成页签数据
    -- self._wtCustomWaterFallList:RefreshAllItems()
    -- self._wtSystemWaterFallList:RefreshAllItems()
    Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.OutFit)
end

function AssemblyHDPresetMainPanel:OnDeactivate()
    Module.CommonBar:BindPersistentBackHandler()                    -- 取消绑定返回处理 
    Module.CommonTips:CloseStrongTip()
    self:ResetFetchOutfitDataTimers()
    Module.ArmedForce.Field:ClearMapPos2InvPresetItemData()
    Module.ArmedForce.Field:ClearMapPos2FitPresetData()
    Module.ArmedForce.Field:ClearCurOperationOutfit()
    Module.ArmedForce.Field:ClearCurOperationOutfitID()
	if Server.ArmedForceServer:CheckIsRentalStatus() then
        Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.MainRental)
    else
        Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.Player)
    end
    Server.InventoryServer:ClearSlotGroup(ESlotGroup.OutFit)
end

function AssemblyHDPresetMainPanel:OnClose()
    Module.CommonBar:BindPersistentBackHandler()                    -- 取消绑定返回处理 
    Module.CommonTips:CloseStrongTip()
    self:ResetFetchOutfitDataTimers()
    self:RemoveAllLuaEvent()
    Module.ArmedForce.Field:ClearMapPos2InvPresetItemData()
    Module.ArmedForce.Field:ClearMapPos2FitPresetData()
    Module.ArmedForce.Field:ClearCurOperationOutfit()
    Module.ArmedForce.Field:ClearCurOperationOutfitID()
	if Server.ArmedForceServer:CheckIsRentalStatus() then
        Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.MainRental)
    else
        Server.ArmedForceServer:SetCurSlotGroupId(ESlotGroup.Player)
    end
    Server.InventoryServer:ClearSlotGroup(ESlotGroup.OutFit)
end
function AssemblyHDPresetMainPanel:OnShowBegin()
    if WidgetUtil.IsGamepad() then
        self._wtNotInteractive:Visible()
    end
    self:AddLuaEvent(Module.ComparePrice.Config.Events.evtFluctuationPopWindowClosed, self._OnFluctuationPopWindowClosed, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtLocalMoveResult, self._OnLocalMoveResult, self)
    self:AddLuaEvent(Server.AuctionServer.Events.evtOnCSAuctionGetSaleListBatchRes, self._OnCSAuctionGetSaleListBatchRes, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtBottomBarSetting, self._BottomBarSummaryListChangedByItemView, self)
    Module.Inventory:SetInvSlotViewType(Module.Inventory.Config.EInvSlotViewType.Assembly_Preset)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
    self._curShowTipsIndex = 0

    Module.Inventory:RegisterCommonClickBehavior()
    self:_ResetContainerSlotViewsContainerSlot()
    self:UsedCurOperationOutfit()
    self:RefreshView()
    Server.ShopServer:SetLimitClicker(true)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:SetInputSummary()
        self:_EnableGamepadFeature()
        Module.CommonWidget:BindCommonIVInputLogic()
        -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        -- local InvSlotViewType = Module.Inventory:GetInvSlotViewType()
        Module.CommonWidget:SetGamepadSwapItem(false)
        self:_EnableInputTypeChangedHandle(true)
        self:_EnableSimulatedMouseDragHandle(true)

        -- 开启手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(true)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(true)
        Module.CommonWidget:EnableGamepadItemShortcutKey(true)
    end
    --- END MODIFICATION
end

function AssemblyHDPresetMainPanel:OnHideBegin()
    self:RemoveLuaEvent(Module.ComparePrice.Config.Events.evtFluctuationPopWindowClosed)
    self:RemoveLuaEvent(Server.InventoryServer.Events.evtLocalMoveResult)
    self:RemoveLuaEvent(Server.AuctionServer.Events.evtOnCSAuctionGetSaleListBatchRes)
    self:RemoveLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart)
    Module.Inventory:SetInvSlotViewType(Module.Inventory.Config.EInvSlotViewType.Default)
    self:SetInteractive(false)
    self:ReleaseTimer()
    self:_SetAllSlotViewOnHide()
    Module.Inventory:UnregisterCommonClickBehavior()
    -- Server.InventoryServer:CalculateAllCarryItemsWeight()
    Server.ShopServer:SetLimitClicker(false)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
        Module.CommonWidget:UnbindCommonIVInputLogic()
        -- 页面关闭时清除物品交换的记录 （每一个需要物品交换功能的主页面都需要）
        Module.CommonWidget:SetGamepadSwapItem(false)
        Module.CommonWidget:StopFocusItemView()
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self:_EnableInputTypeChangedHandle(false)
        self:_EnableSimulatedMouseDragHandle(false)
        
        -- 关闭手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(false)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(false)
        Module.CommonWidget:EnableGamepadItemShortcutKey(false)
    end
    --- END MODIFICATION
    self:CloseHoverTips()
end
function AssemblyHDPresetMainPanel:OnInitExtraData()

end


function AssemblyHDPresetMainPanel:OnAnimFinished(anim)
    if anim == self.WBP_AssemblyDefaultMain_in then
        self._wtNotInteractive:Collapsed()
    end
end
--endregion
--==================================================


--==================================================
--region Public API
--endregion
--==================================================


--==================================================
--region Private API

function AssemblyHDPresetMainPanel:_InitContainerSlotViews()
    local fPostEquipItemRefresh = function (itemView)
        if itemView.item then
            itemView:SetCustomInteractivity(true)
            itemView:SetHandleDrag(false)
            itemView:SetHandleDoubleClick(false)
            itemView:SetCppValue("bHandleClick", false)
            itemView:SetCppValue("bHandleClickR", false)
            local maskComponent = itemView:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
            if maskComponent then
                itemView:EnableComponent(EComp.GreyMask, true)
                itemView:SetCursor(EMouseCursor.Default)
            end
            if itemView.item.bNeedBuy then
                local goods = Module.ArmedForce.Field:GetMapArmedForceGoods(itemView.item.id)
                local propInfo = itemView.item:GetRawPropInfo()
                local durabilitylvl
                if propInfo then
                    durabilitylvl = Server.AuctionServer:GetArmorDurabilitylvlByDurability(itemView.item.id, propInfo.health_max)
                end
                if goods and goods:GetIsSufficient(durabilitylvl) then
                    itemView:EnableComponent(EComp.SoldOutAbnormal, false)
                else
                    local component = itemView:FindOrAdd(EComp.SoldOutAbnormal, UIName2ID.IVSoldOutAbnormalComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
                    itemView:EnableComponent(EComp.SoldOutAbnormal, true)
                    component:PlayAnimation(component.Anim_Reminder_Icon_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
                end
            else
                itemView:EnableComponent(EComp.SoldOutAbnormal, false)
            end
        end
    end

    local fPostContainerViewItemRefresh = function (itemView)
        if itemView.item then
            local function fOnItemViewClickedRCallback()
                if self._curOutfitID <= 0 then
                    self:_OnLeftDecClicked(itemView)
                end
            end
            itemView:SetCustomInteractivity(true)
            -- 设置tips逻辑 
            self:SetTipsLogic(itemView, fOnItemViewClickedRCallback)

            if self._curOutfitID <= 0 then
                -- 可拖动
                itemView:SetHandleDrag(true)
            else
                itemView:SetHandleDrag(false)
            end
            -- 不可双击
            itemView:SetHandleDoubleClick(false)
            itemView:EnableComponent(EComp.GreyMask, false)
            itemView:SetCursor(EMouseCursor.Default)
            if itemView.item.bNeedBuy then
                local goods = Module.ArmedForce.Field:GetMapArmedForceGoods(itemView.item.id)
                local propInfo = itemView.item:GetRawPropInfo()
                local durabilitylvl
                if propInfo then
                    durabilitylvl = Server.AuctionServer:GetArmorDurabilitylvlByDurability(itemView.item.id, propInfo.health_max)
                end

                if goods and goods:GetIsSufficient(durabilitylvl) then
                    local component = itemView:FindOrAdd(EComp.NeedBuyIcon, UIName2ID.IVShoppingCartComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
                    itemView:EnableComponent(EComp.NeedBuyIcon, true)
                    itemView:EnableComponent(EComp.SoldOutAbnormal, false)
                else
                    local component = itemView:FindOrAdd(EComp.SoldOutAbnormal, UIName2ID.IVSoldOutAbnormalComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
                    itemView:EnableComponent(EComp.SoldOutAbnormal, true)
                    component:PlayAnimation(component.Anim_Reminder_Icon_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
                    itemView:EnableComponent(EComp.NeedBuyIcon, false)
                end
            else
                itemView:EnableComponent(EComp.NeedBuyIcon, false)
                itemView:EnableComponent(EComp.SoldOutAbnormal, false)
            end
        end
    end
    logerror("【AssemblyHDPresetMainPanel】管理warehouse对象池 InitContainerSlotViews")
    local fObtainCustomWarehouseIVFromPool = function ()
        return Module.ArmedForce.Field:ObtainWarehouseIV(Module.ArmedForce.Config.EAssemblyWarehouseIVPoolType.Preset)
    end

    local fFreeWarehouseIVToPool = function (warehouseIV)
        Module.ArmedForce.Field:FreeWarehouseIV(Module.ArmedForce.Config.EAssemblyWarehouseIVPoolType.Preset, warehouseIV)
    end

    self._wtAssemblyBackpack = self:Wnd("WBP_AssemblyBackpack_PC", UIWidgetBase)
    self._wtMainScrollBox = self._wtAssemblyBackpack:Wnd("wtMainScrollBox", UIScrollBox)

    -- 胸挂
    self._wtCHContainerView = self._wtAssemblyBackpack:Wnd("wtCHContainerView", WarehouseContainerPanel_HD)
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, ESlotGroup.OutFit)
    self._wtCHContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtChestHangingCapacity = self._wtCHContainerView:GetCapacityText()
    self._wtChestHangingSlotView = self._wtCHContainerView:GetContainerSlotView()
    self._wtChestHangingSlotView:BindItemViewPostRefreshFunc(fPostContainerViewItemRefresh)
    self._wtChestHangingSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 口袋
    self._wtPocketContainerView = self._wtAssemblyBackpack:Wnd("wtPocketContainerView", WarehouseContainerPanel_HD)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, ESlotGroup.OutFit)
    self._wtPocketCapacity = self._wtPocketContainerView:GetCapacityText()
    self._wtPocketSlotView = self._wtPocketContainerView:GetContainerSlotView()
    self._wtPocketSlotView:BindItemViewPostRefreshFunc(fPostContainerViewItemRefresh)
    self._wtPocketSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 背包
    self._wtBagContainerView = self._wtAssemblyBackpack:Wnd("wtBagContainerView", WarehouseContainerPanel_HD)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, ESlotGroup.OutFit)
    self._wtBagContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtBagCapacity = self._wtBagContainerView:GetCapacityText()
    self._wtBagSlotView = self._wtBagContainerView:GetContainerSlotView()
    self._wtBagSlotView:BindItemViewPostRefreshFunc(fPostContainerViewItemRefresh)
    self._wtBagSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 安全箱
    self._wtSafeBoxContainerView = self._wtAssemblyBackpack:Wnd("wtSafeBoxContainerView", WarehouseContainerPanel_HD)
    self._wtSafeBoxContainerView:InitContainerSlot(ESlotType.SafeBoxContainer, ESlotGroup.OutFit)
    self._wtSafeBoxContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtSafeBoxCapacity = self._wtSafeBoxContainerView:GetCapacityText()
    self._wtSafeBoxSlotView = self._wtSafeBoxContainerView:GetContainerSlotView()
    self._wtSafeBoxSlotView:SetSlotMaxPreviewSize(5, 0)
    self._wtSafeBoxSlotView:BindItemViewPostRefreshFunc(fPostContainerViewItemRefresh)
    self._wtSafeBoxSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)
end

function AssemblyHDPresetMainPanel:_ResetContainerSlotViewsContainerSlot()
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, ESlotGroup.OutFit)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, ESlotGroup.OutFit)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, ESlotGroup.OutFit)
    self._wtSafeBoxContainerView:InitContainerSlot(ESlotType.SafeBoxContainer, ESlotGroup.OutFit)
end

function AssemblyHDPresetMainPanel:SetTipsLogic(itemView, fOnItemViewClickedRCallback)
    local function ShowCommonHoverTipsHD(bHovered)
        if bHovered then
            if itemView.item then
                if itemView.item:IsMedicine() then
                    local contents = {}
                    local healthFeature = itemView.item:GetFeature(EFeatureType.Health)
                    local drugEffectStr = nil
                    local curDurability, maxDurability = healthFeature:GetDurabilityValue()
                    if healthFeature:IsArmorMedicine() then
                        drugEffectStr = string.format(Module.ArmedForce.Config.Loc.DrugEffectStr[EDispensingMedicineType.Armor], curDurability, maxDurability)
                    elseif healthFeature:IsDragMedicine() then
                        drugEffectStr = string.format(Module.ArmedForce.Config.Loc.DrugEffectStr[EDispensingMedicineType.HP], curDurability, maxDurability)
                    elseif healthFeature:IsBUFFMedicine() then
                        drugEffectStr = string.format(Module.ArmedForce.Config.Loc.DrugEffectStr[EDispensingMedicineType.BUFF], curDurability, maxDurability)
                    end
                    -- 标题
                    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = itemView.item.name, styleRowId = "C001"}})
                    -- 占格数
                    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_DrugOccupancy, data = {textContent = string.format(Module.ArmedForce.Config.Loc.DrugOccupancyStr, itemView.item.width, itemView.item.length), item = itemView.item}})
                    if drugEffectStr then
                        -- 药品效果
                        table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = drugEffectStr, styleRowId = "C002"}})
                    end
                    -- 药品描述
                    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = itemView.item.description, styleRowId = "C002"}})
                    if self._curOutfitID <= 0 then
                        if not WidgetUtil.IsGamepad() then
                            table.insert(contents,{id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = itemView.item.bNeedBuy and "AssemblyQuickOperation_Remove" or "AssemblyQuickOperation_RemoveToInventory"}}}})
                        end
                    end
                    -- table.insert(contents,{id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = itemView.item.bNeedBuy and "AssemblyQuickOperation_Remove" or "AssemblyQuickOperation_RemoveToInventory"}}}})
                    Module.ArmedForce:ShowAssembledTips(contents, itemView)
                elseif itemView.item:IsBullet() then
                    local weapon = Module.ArmedForce:GetPlayerMatchWeaponByBulletId(itemView.item.id)
                    local contents = QuickOperationLogic.GetBulletHoverTipsContents(weapon, itemView.item)
                    if not table.isempty(contents) then
                        local tipsContents = {}
                        -- 标题
                        table.insert(tipsContents, {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = itemView.item.name, styleRowId = "C001"}})
                        -- 子弹详情
                        for index, str in ipairs(contents) do
                            table.insert(tipsContents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = str, styleRowId = "C002"}})
                        end
                        if self._curOutfitID <= 0 then
                            if not WidgetUtil.IsGamepad() then
                                table.insert(tipsContents,{id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = itemView.item.bNeedBuy and "AssemblyQuickOperation_Remove" or "AssemblyQuickOperation_RemoveToInventory"}}}})
                            end
                        end
                        -- table.insert(tipsContents,{id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = itemView.item.bNeedBuy and "AssemblyQuickOperation_Remove" or "AssemblyQuickOperation_RemoveToInventory"}}}})
                        Module.ArmedForce:ShowAssembledTips(tipsContents, itemView)
                    end
                else
                    -- Module.ArmedForce:ShowAssembledTips({{
                    --     id = UIName2ID.Assembled_CommonHDKeyTips_V1,
                    --     data = {actionName = "AssemblyQuickOperation_RemoveToInventory"}
                    -- }}, itemView, 1)
                end
            end         
        else
            Module.ArmedForce:RemoveAssembledTips(itemView)
        end
    end

    -- 重载OnHide
    itemView.OnHide = function(itemView)
        Module.ArmedForce:RemoveAssembledTips(itemView)
        if itemView:Super().OnHide then
            itemView:Super().OnHide(itemView)
        end
    end
    -- 重载OnShow
    itemView.OnShow = function(itemView)
        if itemView:Super().OnShow then
            itemView:Super().OnShow(itemView)
        end
        local mousePos = UWidgetLayoutLibrary.GetMousePositionOnPlatform()
        local bInside = UIUtil.CheckAbsolutePointInsideWidget(itemView, mousePos)
        if bInside then
            ShowCommonHoverTipsHD(true)
        else
            ShowCommonHoverTipsHD(false)
        end
    end
    
    -- -- 重载OnShow
    -- itemView._OnHovered = function (itemView)
    --     if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
    --         return
    --     end
    --     ShowCommonHoverTipsHD(true)
    -- end
    -- itemView._OnUnhovered = function (itemView)
    --     if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
    --         return
    --     end
    --     ShowCommonHoverTipsHD(false)
    -- end
    itemView.ShowTip = function(itemView)
        if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
            return
        end
        ShowCommonHoverTipsHD(true)
    end
    
    itemView.HideTip = function(itemView)
        if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
            return
        end
        ShowCommonHoverTipsHD(false)
    end
    local function fOnClickCallback()
        CommonWidgetConfig.Events.evtItemSingleSelected:Invoke(itemView)
    end
    -- 需要拖拽
    itemView:SetCppValue("bHandleClick", true)
    itemView:BindCustomOnClicked(fOnClickCallback)
    itemView:SetCppValue("bHandleHover", true)
    -- itemView:Event("OnHovered", itemView._OnHovered, itemView)
    -- itemView:Event("OnUnhovered", itemView._OnUnhovered, itemView)
    itemView:SetCppValue("bHandleClickR", true)
    -- 重载OnShow
    itemView.OnClickedR = function (itemView, InGeometry, InMouseEvent)
        fOnItemViewClickedRCallback()
        ShowCommonHoverTipsHD(false)
    end
end

-- 点击左边itemview移除
function AssemblyHDPresetMainPanel:_OnLeftDecClicked(itemView)
	Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
    local operateItem = itemView.item
    if not operateItem then
        return
    end
    local result = OutfitDataLogic.DecreaseItemProcess(operateItem)
    if result then
        local slot = operateItem.InSlot
        local containerView = self:_GetContainerView(slot.SlotType)
        local slotView = containerView:GetContainerSlotView()
        slotView:RemoveItem(operateItem)
        Server.InventoryServer:RemoveItemFromList(operateItem.gid, ESlotGroup.OutFit)
        operateItem:RemoveSelf()
        self:_RefreshCapacity()
        self:_OnRefreshBtnState()

        if operateItem and operateItem:IsBullet() then
            Module.ArmedForce.Config.evtOutFitBulletNumChanged:Invoke(operateItem.id)
        end
        Server.InventoryServer:CalculateAllCarryItemsWeight()
    end
end

function AssemblyHDPresetMainPanel:_GetContainerView(slotType)
    local containerView
    if slotType == ESlotType.BagContainer then
        containerView = self._wtBagContainerView
    elseif slotType == ESlotType.ChestHangingContainer then
        containerView = self._wtCHContainerView
    elseif slotType == ESlotType.Pocket then
        containerView = self._wtPocketContainerView
    elseif slotType == ESlotType.SafeBoxContainer then
        containerView = self._wtSafeBoxContainerView
    end
    return containerView
end

function AssemblyHDPresetMainPanel:RefreshEquipmentItems()

end

function AssemblyHDPresetMainPanel:SetContainerItems(slotType)
    local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
    if presetSlot:IsContainerSlot() then
        if slotType == ESlotType.Pocket then
            presetSlot:InitPocketContainer()
        else
            local containerType = presetSlot:GetContainerType()
            local equipSlot = Server.InventoryServer:GetSlot(containerType, ESlotGroup.OutFit)
            local equipment = equipSlot:GetEquipItem()
            if equipment then
                presetSlot:InitContainerByEquipmentId(equipment.id)
            end
        end
        local containerView = self:_GetContainerView(slotType)
        containerView:InitContainerSlot(slotType, ESlotGroup.OutFit)
        containerView:RefreshView() -- 必须放在这里，后面都是单个itemview刷新的，保留itemview缓存信息
    end
end

function AssemblyHDPresetMainPanel:_RefreshCapacity(inSlotType)
    local function fRefreshCapacity(slotType)
        local containerView = self:_GetContainerView(slotType)
        if containerView then
            containerView:RefreshCapacity()
            Module.ArmedForce.Config.evtOutFitCapacityChanged:Invoke(slotType)
        end
    end

    if inSlotType then
        fRefreshCapacity(inSlotType)
    else
        -- 设置胸挂
        fRefreshCapacity(ESlotType.ChestHangingContainer)
        -- 设置口袋
        fRefreshCapacity(ESlotType.Pocket)
        -- 设置背包
        fRefreshCapacity(ESlotType.BagContainer)
        -- 设置安全箱
        fRefreshCapacity(ESlotType.SafeBoxContainer)
    end

end

function AssemblyHDPresetMainPanel:RefreshContainerItems()
    self:SetContainerItems(ESlotType.ChestHangingContainer) -- 胸挂
    self:SetContainerItems(ESlotType.Pocket) -- 口袋
    self:SetContainerItems(ESlotType.BagContainer)
    self:SetContainerItems(ESlotType.SafeBoxContainer)

    -- local interval = 0.2
    -- local maxNum = 10
    -- local allItems = {}
    -- local allQuickOperationDataInfo = ArmedForceField:GetAllQuickOperationDataInfo()

    -- local function fSetSlotView(slotType)
    --     local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
    --     presetSlot:ResetSlot()

    --     local playerContainerSlot = Server.InventoryServer:GetSlot(slotType)

    --     local items = playerContainerSlot:GetItems()
    --     table.append(allItems, items)

    --     if slotType == ESlotType.Pocket then
    --         presetSlot:InitPocketContainer()
    --     else
    --         local containerType = playerContainerSlot:GetContainerType()
    --         local playerEquipContainerSlot = Server.InventoryServer:GetSlot(containerType)

    --         local presetBindEquipSlot = Server.InventoryServer:GetSlot(containerType, ESlotGroup.OutFit)
    --         presetBindEquipSlot:ResetSlot()
    --         local equipment = playerEquipContainerSlot:GetEquipItem()
    --         if equipment then
    --             presetSlot:InitContainerByEquipmentId(equipment.id)

    --             local presetEquiment = ItemBase:NewIns(equipment.id)
    --             local propInfo = {}
    --             local rawPropInfo = equipment:GetRawPropInfo()
    --             if rawPropInfo then -- 这里后续要注意局内拾取到的道具是否没有rawpropinfo
    --                 deepcopy(propInfo, rawPropInfo)
    --                 presetEquiment:SetRawPropInfo(propInfo)
    --             end
    --             presetEquiment:AddToSlot(presetBindEquipSlot)
    --             Server.InventoryServer:AddItemToList(presetEquiment, presetBindEquipSlot)
    --         end
    --     end
    --     local containerView = self:_GetContainerView(slotType)
    --     containerView:RefreshView() -- 必须放在这里，后面都是单个itemview刷新的，保留itemview缓存信息
    --     containerView:_CheckContainerSlotVisibility()
    -- end

    -- fSetSlotView(ESlotType.ChestHangingContainer) -- 胸挂
    -- fSetSlotView(ESlotType.Pocket) -- 口袋
    -- fSetSlotView(ESlotType.BagContainer)
    -- fSetSlotView(ESlotType.SafeBoxContainer)

    -- local groups = fSplitTable(allItems, maxNum)
    -- -- 放置原先的道具(分批)
    -- for i, group in ipairs(groups) do
    --     loginfo("AssemblyHDQuickOperationMainView:InitContainerItems Groups ======================================> group:", i)
    --     Timer.DelayCall(i * interval, function ()
    --         for index, item in ipairs(group) do
    --             local slotType = item.InSlot.SlotType
    --             local slotView = self:_GetSlotView(slotType)
    --             local newLoc = item.InSlot:GetItemLocation(item)
    --             if newLoc then
    --                 local itemMainType = ItemHelperTool.GetMainTypeById(item.id)
    --                 local  quickOperationDataInfo = allQuickOperationDataInfo[item.id]
    --                 local operateItem = ItemBase:NewIns(item.id)
    --                 local gid = item.gid -- 保证非操作类型的道具用的gid是原生的

    --                 local quickOperationDataInfo = Module.ArmedForce:GetQuickOperationDataInfo(item.id)
    --                 if quickOperationDataInfo then
    --                     gid =  GetGid()
    --                 end

    --                 local propInfo = {}
    --                 local rawPropInfo = item:GetRawPropInfo()
    --                 if rawPropInfo then -- 这里后续要注意局内拾取到的道具是否没有rawpropinfo
    --                     deepcopy(propInfo, rawPropInfo)
    --                     propInfo.gid = gid
    --                     operateItem:SetRawPropInfo(propInfo)
    --                 end
    --                 -- end
    --                 if operateItem then
    --                     local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
    --                     operateItem:AddToSlot(presetSlot)
    --                     Server.InventoryServer:AddItemToList(operateItem, presetSlot)
    --                     local pbLocation = newLoc:ToPbLocation()
    --                     local targetLoc = presetSlot:SetItemPosFromPb(operateItem, pbLocation)
    --                     operateItem:FreshRawPropInfoByLoc(targetLoc)
    --                     local operateItemView = slotView:AddItem(operateItem, targetLoc, targetLoc.bRotated)
    --                     if quickOperationDataInfo then
    --                         quickOperationDataInfo:CalculateMoveNum(operateItem, operateItem.num)
    --                         Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(item.id)
    --                     end
    --                 end
    --             end
    --         end
    --         self:_RefreshCapacity()
    --     end, self)
    -- end
end


--- 当处理按钮点击时
function AssemblyHDPresetMainPanel:OnBtnProcessClicked()
    -- for _, slotType in ipairs({ESlotType.ChestHanging, ESlotType.Bag, ESlotType.SafeBox}) do
    --     local result = OutfitDataLogic.CheckCanPlaceItems(slotType)
    --     if not result then
    --         Module.CommonTips:ShowSimpleTip(ArmedForceConfig.Loc.ContainerSpaceLack, 2)
    --         return
    --     end
    -- end

    local bIsOutfit = Server.ArmedForceServer:GetCurSlotGroupId() == ESlotGroup.OutFit

    if bIsOutfit then
        if self._bNotInteractive then
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ClickTooSoonPleaseWait)
            return
        end
        self:_EnquiryBuy(function()
            Module.CommonTips:ShowStrongTip(Module.ArmedForce.Config.Loc.TheOutfitplanIsBeingImplemented, self, true, 0.1, function ()
                return self._bNotInteractive
            end)
            self:ReleaseTimer()
            self:SetInteractive(true)
            self._notInteractiveTimerHandle = Timer.DelayCall(15, function ()
                if self._bNotInteractive then
                    logerror("AssemblyHDPresetMainPanel:OnBtnProcessClicked 耗时15秒还未处理完推荐配装")
                    self:SetInteractive(false)
                end
            end, self)
            loginfo("[AssemblyHDPresetMainPanel] OnBtnProcessClicked")
            OutfitDataLogic.ApplyOperationOutfit()

            -- 当玩家在某推荐方案下，点击应用配装时，记录玩家信息、推荐方案的id；
            if self._curOutfitID < 0 then -- 自定义方案
                LogAnalysisTool.SetPresetModuleUseValueByStr("UseDiyPreset", 1)
            elseif self._curOutfitID == 0 then --上次方案
                LogAnalysisTool.SetPresetModuleUseValueByStr("UseLastPreset", 1)
            elseif self._curOutfitID > 0 then -- 系统推荐方案
                OutfitDataLogic.SendAnalysisData(Module.ArmedForce.Config.EAnalysisType.Apply,{OutfitID = self._curOutfitID, IsNotModify = 1})
                if self._curOutfitID == Module.ArmedForce.Config.EPlanLevel.Lv1 then
                    LogAnalysisTool.SetPresetModuleUseValueByStr("UseLowPreset", 1)
                elseif self._curOutfitID == Module.ArmedForce.Config.EPlanLevel.Lv2 then
                    LogAnalysisTool.SetPresetModuleUseValueByStr("UseMidPreset", 1)
                elseif self._curOutfitID == Module.ArmedForce.Config.EPlanLevel.Lv3 then
                    LogAnalysisTool.SetPresetModuleUseValueByStr("UseHighPreset", 1)
                end
            end
        end)
    else
        self:CloseMainPanel()
    end
    return true
end

function AssemblyHDPresetMainPanel:ReleaseTimer()
    if self._notInteractiveTimerHandle then
        Timer.CancelDelay(self._notInteractiveTimerHandle)
        self._notInteractiveTimerHandle = nil
    end
end

--- 购买确认
function AssemblyHDPresetMainPanel:_EnquiryBuy(fToPocess)
    if self._bApplyAll then
        local prePriceValue = self._curPriceValue
        logerror("[AssemblyHDPresetMainPanel] 应用配装方案 pre价值", prePriceValue)
        local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
        OutfitDataLogic.DoBatchFetchOutfitData(curOperationOutfit, function ()
            local curPriceValue = OutfitDataLogic.GetNeedBuyItemsCost()
            logerror("[AssemblyHDPresetMainPanel] 应用配装方案 前后总价对比", prePriceValue, curPriceValue)
            if curPriceValue <= prePriceValue then
                fToPocess()
            else
                local itemDataList = OutfitDataLogic.GeneratesOutfitPlanItemDataList()
                Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.PriceChangesConfirmPlanPricesTips)
                Facade.UIManager:AsyncShowUI(UIName2ID.AssemblyPlanConfirmWindow, nil, nil, itemDataList, function (bConfirm)
                    if bConfirm then
                        local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum() 
                        if currencyNum < curPriceValue then
                            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InsufficientCurrencyToUseThisOutfit)
                        else
                            fToPocess()
                        end

                    end
                end)
            end

        end)
    else
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InsufficientCurrencyToUseThisOutfit)
    end
end


--- 当保存按钮点击时
function AssemblyHDPresetMainPanel:_OnSavedPlanBtnClicked()
    OutfitDataLogic.OpenSaveSchemeWindow()
end
function AssemblyHDPresetMainPanel:_OnSavedPlanBtnDeClicked()
    Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.PleaseEditThePlanBeforeSavingIt)
end

--- 当药品按钮点击时
function AssemblyHDPresetMainPanel:_OnMedicineOperationBtnClicked()
    local fOnConfirm = function ()
        OutfitDataLogic.RecordOutfitQuickOperationDatas(Module.ArmedForce.Config.ESubViewType.Medicine)
    end

    local itemHealthtab = Module.ArmedForce.Config.itemHealthtab
    local ids = table.keys(itemHealthtab)
    if not table.isempty(ids) then
        Server.AuctionServer:DoBatchFetchSaleList(ids)
    end

    Module.ArmedForce.Field:SetWaitToFocusItem(self._wtMedicineOperationBtn)
    Module.ArmedForce.Config.evtArmedForceQuickOperationTryOpen:Invoke()
    Facade.UIManager:AsyncShowUI(UIName2ID.AssemblyHDQuickOperationMainView, nil, self, Module.ArmedForce.Config.ESubViewType.Medicine, fOnConfirm)
end


--- 当子弹按钮点击时
function AssemblyHDPresetMainPanel:_OnBulletOperationBtnClicked()
    local fOnConfirm = function ()
        OutfitDataLogic.RecordOutfitQuickOperationDatas(Module.ArmedForce.Config.ESubViewType.Bullet)
    end
    local mainWeaponLeftItem = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.OutFit):GetEquipItem()
    local mainWeaponRightItem = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, ESlotGroup.OutFit):GetEquipItem()
    local pistrolItem = Server.InventoryServer:GetSlot(ESlotType.Pistrol, ESlotGroup.OutFit):GetEquipItem()
    if not mainWeaponLeftItem and not mainWeaponRightItem and not pistrolItem then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.NeedToEquipAWeaponFirst)
        return
    end

    local matchIds = {} -- 拍卖行全图鉴
    local typeInfo = Module.ArmedForce.Config.SlotType2Type[ESlotType.BulletLeft] -- 子弹类型都是一样的
    if typeInfo then
        matchIds = Server.AuctionServer:GetSaleIdListFromType(typeInfo.mainType, typeInfo.subType)
    end
    if not table.isempty(matchIds) then
        -- 重新拉取最新价格
        Server.AuctionServer:DoBatchFetchSaleList(matchIds)
    end
    Module.ArmedForce.Field:SetWaitToFocusItem(self._wtBulletOperationBtn)
    Module.ArmedForce.Config.evtArmedForceQuickOperationTryOpen:Invoke()
    Facade.UIManager:AsyncShowUI(UIName2ID.AssemblyHDQuickOperationMainView, nil, self, Module.ArmedForce.Config.ESubViewType.Bullet, fOnConfirm)
end

function AssemblyHDPresetMainPanel:GenLiveTabList()
    self:_InitFilter()
    self:_CreateTab()
end

local OutfitID2ModuleID ={
    [Module.ArmedForce.Config.EPlanLevel.Lv1] = SwitchModuleID.ModuleArmedLow,
    [Module.ArmedForce.Config.EPlanLevel.Lv2] = SwitchModuleID.ModuleArmedMiddle,
    [Module.ArmedForce.Config.EPlanLevel.Lv3] = SwitchModuleID.ModuleArmedHigh,
}

function AssemblyHDPresetMainPanel:_InitFilter()
    local loadLastEquipment = Server.PresetServer:GetSOLArmedForceLoadLastEquipment()
    local bLastEmpty = false
    if table.isempty(loadLastEquipment) then
        bLastEmpty = true
    end

    self._customMainTabGroupDataList = {}
    if not bLastEmpty then
        table.insert(self._customMainTabGroupDataList,{outfitID = 0, keyText = Module.ArmedForce.Config.Loc.LastPresetTitle})
    end

    local customOutfits = Server.PresetServer:GetSOLArmedForceOutfit()
    for _, customOutfit in pairs(customOutfits) do
        if customOutfit then
            local i = customOutfit.index + 1
            local keyText = customOutfit.name ~= "" and customOutfit.name or Module.ArmedForce.Config.Loc.EmptyPlanString-- 有自定义名字时用server下发的
            table.insert(self._customMainTabGroupDataList,{outfitID = -i, keyText = keyText}) -- 自定义的outfitID都是<0
        end
    end
    table.sort(self._customMainTabGroupDataList, function (a, b)
        return a.outfitID > b.outfitID
    end)

    self:BP_SetPosition(#self._customMainTabGroupDataList)
    
    self._systemMainTabGroupDataList = {}
    for i = 1, 3, 1 do
        local moduleID = OutfitID2ModuleID[i]
        local moduleUnlockData = {
            moduleID = moduleID,
            fOnClickCallback = self._OnCheckedBoxStateChangedNativeBySystem,
            caller = self,
            bGrey = true,
            bShowUnlockTips = true,
            bIntercept = true,
        }
        table.insert(self._systemMainTabGroupDataList,{moduleUnlockData = moduleUnlockData, outfitID = Module.ArmedForce.Config.EPlanLevel["Lv" .. i], keyText = Module.ArmedForce.Config.Loc.PlanLevel[i]})
    end
end

function AssemblyHDPresetMainPanel:_CreateTab()
    self._wtCustomDFCommonMainTabList:FreshMainTabByDataList(self._customMainTabGroupDataList, CreateCallBack(self._OnProcessCustomTab, self))
	self._wtCustomDFCommonMainTabList:SetCallback(self._OnCheckedBoxStateChangedNativeByCustom, self)
    self._wtSystemDFCommonMainTabList:FreshMainTabByDataList(self._systemMainTabGroupDataList, CreateCallBack(self._OnProcessSystemTab, self))
    self._wtSystemDFCommonMainTabList:SetCallback(self._OnCheckedBoxStateChangedNativeBySystem, self)
    self._wtCustomDFCommonMainTabList:SetTabIndex(0)
    self._wtSystemDFCommonMainTabList:SetTabIndex(-1, false)
end

function AssemblyHDPresetMainPanel:_OnProcessCustomTab(tabIndex, mainTab)
    self._customTabItems[tabIndex] = mainTab
    loginfo("AssemblyHDPresetMainPanel:_OnProcessCustomTab",self._customTabIndex,tabIndex,mainTab)
    if self._customTabIndex >= 0 and tabIndex == self._customTabIndex then
        if mainTab then
            WidgetUtil.SetUserFocusToWidget(mainTab, false)
        end
    end
end

function AssemblyHDPresetMainPanel:_OnProcessSystemTab(tabIndex, mainTab)
    self._systemTabItems[tabIndex] = mainTab
    loginfo("AssemblyHDPresetMainPanel:_OnProcessSystemTab+++++",self._systemTabIndex,tabIndex,mainTab)
    if self._systemTabIndex >= 0 and tabIndex == self._systemTabIndex then
        if mainTab then
            WidgetUtil.SetUserFocusToWidget(mainTab, false)
        end
    end
end


function AssemblyHDPresetMainPanel:_OnCheckedBoxStateChangedNativeByCustom(idx)
    self._customTabIndex = idx
    self._systemTabIndex = -1
    loginfo("AssemblyHDPresetMainPanel:自定义",self._customTabIndex)
    self._wtSystemDFCommonMainTabList:SetTabIndex(-1, false) --不要显示选择框
    local data = self._customMainTabGroupDataList[idx + 1]
    if data then
        local outfitID = data.outfitID
        self:_OnCheckedBoxStateChangedNative(outfitID)
    end
end

function AssemblyHDPresetMainPanel:_OnCheckedBoxStateChangedNativeBySystem(idx)
    self._systemTabIndex = idx
    self._customTabIndex = -1
    loginfo("AssemblyHDPresetMainPanel:系统",self._systemTabIndex)
    self._wtCustomDFCommonMainTabList:SetTabIndex(-1, false) --不要显示选择框
    local data = self._systemMainTabGroupDataList[idx + 1]
    if data then
        local outfitID = data.outfitID
        self:_OnCheckedBoxStateChangedNative(outfitID)
    end
end


function AssemblyHDPresetMainPanel:_OnCheckedBoxStateChangedNative(curOutfitID)
    if not self._curOutfitID then
        self._curOutfitID = curOutfitID
        Module.ArmedForce.Field:SetCurOperationOutfitID(self._curOutfitID)
        self:GenCurOperationOutfit()
    else
        self._curOutfitID = curOutfitID
        Module.ArmedForce.Field:SetCurOperationOutfitID(self._curOutfitID)
        self:GenCurOperationOutfit()
        self:UsedCurOperationOutfit()
        self:RefreshView()
    end
    OutfitDataLogic.SendAnalysisData(Module.ArmedForce.Config.EAnalysisType.View,{OutfitID = self._curOutfitID > 0 and self._curOutfitID or 0})
end


function AssemblyHDPresetMainPanel:RefreshCustomTabName(index)
    local customOutfit = Server.PresetServer:GetSOLArmedForceOutfit(index)
    if customOutfit then
        local schemeIndex = Module.ArmedForce:Index_To_SchemeIndex(index)
        local outfitID = Module.ArmedForce:Index_To_OutfitID(index)
        local customMainTabGroupData = table.find(self._customMainTabGroupDataList, function (v, k)
            return v.outfitID == outfitID
        end)
        if customMainTabGroupData then
            local keyText = customOutfit.name ~= "" and customOutfit.name or Module.ArmedForce.Config.Loc.EmptyPlanString -- 有自定义名字时用server下发的
            customMainTabGroupData.keyText = keyText

            if outfitID <= 0 then
                self._wtCustomDFCommonMainTabList:GetTabGroupMainBox():RefreshTab()
            end

            -- -- 获取对应下标
            -- local widgetIndex = schemeIndex
            -- local loadLastEquipment = Server.PresetServer:GetSOLArmedForceLoadLastEquipment()
            -- if not table.isempty(loadLastEquipment) then
            --     widgetIndex = widgetIndex + 1 -- 需要加上头部的上次配装
            -- end

            -- local weakUIIns, _ = Facade.UIManager:GetSubUI(self, UIName2ID.DFCommonTabV2S1, customMainTabGroupData.outfitID)
            -- local widget = getfromweak(weakUIIns)
            -- if widget then
            --     widget:SetMainTitle(keyText)
            --     widget:SetIsChecked(customMainTabGroupData.outfitID == self._curOutfitID)
            -- end
        end
    end
end

function AssemblyHDPresetMainPanel:RefreshCustomOutfit(index)
    if self._wtCustomDFCommonMainTabList._wtNavGroup then
        self._wtCustomDFCommonMainTabList._wtNavGroup:SetFocusRecoveryEnabled(false)
    end
    self._curOutfitID = Module.ArmedForce:Index_To_OutfitID(index)
    OutfitDataLogic.SendAnalysisData(Module.ArmedForce.Config.EAnalysisType.View,{OutfitID = self._curOutfitID > 0 and self._curOutfitID or 0})
    -- 更新Tab名
    self:RefreshCustomTabName(index)
    local schemeIndex = Module.ArmedForce:Index_To_SchemeIndex(index)
    -- schemeIndex为自定义方案理解的下标（因为后台首个方案的index为0）
    -- 控件下，首个下标也是为0，但是要注意上次配装方案，这个是强行塞进去的，所以，如果保存的是方案1，那么如果没上次配装方案时都得减一
    -- 获取对应下标
    local widgetIndex = schemeIndex
    local loadLastEquipment = Server.PresetServer:GetSOLArmedForceLoadLastEquipment()
    if table.isempty(loadLastEquipment) then
        widgetIndex = widgetIndex - 1 -- 需要加上头部的上次配装
    end
    self._wtCustomDFCommonMainTabList:SetTabIndex(widgetIndex, true)
    if self._wtCustomDFCommonMainTabList._wtNavGroup then
        self._wtCustomDFCommonMainTabList._wtNavGroup:SetFocusRecoveryEnabled(true)
    end

    -- -- 更新方案数据
    -- self:GenCurOperationOutfit()
    -- self:UsedCurOperationOutfit()
    -- self:RefreshView()
end

function AssemblyHDPresetMainPanel:_OnLocalMoveResult(moveCmd)
    OutfitDataLogic.OnLocalMoveResult(moveCmd)
    self:_RefreshCapacity()
    self:_OnRefreshBtnState()
end

function AssemblyHDPresetMainPanel:_OnCSAuctionGetSaleListBatchRes()
    self:_OnRefreshBtnState()
end

function AssemblyHDPresetMainPanel:_OnPresetFitSampleItem(slotType)
    local outFitSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.OutFit)
    if outFitSlot:IsContainerSlot() and self:IsVisible() then
        self:SetContainerItems(slotType)
    end
end

function AssemblyHDPresetMainPanel:_OnPresetFitSampleItemFinished()
    if self:IsVisible() then
        self:RefreshContainerItems()
        self:_OnRefreshBtnState()
    end
end

function AssemblyHDPresetMainPanel:_CheckSoldOutAbnormal()
    local bSoldOutAbnormal = OutfitDataLogic.CheckPresetDataSoldOutAbnormal()
    if bSoldOutAbnormal then
        self._wtAssemblyMapNeed:SelfHitTestInvisible()
        self._wtAssemblyMapNeed:PlayAnimation(self._wtAssemblyMapNeed.Anim_AssemblyMapNeed_Icon_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
    else
        self._wtAssemblyMapNeed:Collapsed()
        self._wtAssemblyMapNeed:StopAnimation(self._wtAssemblyMapNeed.Anim_AssemblyMapNeed_Icon_loop)
    end
end

function AssemblyHDPresetMainPanel:_OnApplyOutfitFinished()
    Timer.DelayCall(0.2,function() 
        self:SetInteractive(false)
        self:CloseMainPanel()
    end, self)
end

function AssemblyHDPresetMainPanel:_evtApplyOutfitError()
    Timer.DelayCall(0.2,function() 
        self:SetInteractive(false)
        self:CloseMainPanel()
    end, self)
end

function AssemblyHDPresetMainPanel:CloseMainPanel()
    local curView = Facade.UIManager:GetCurrentStackUI()
    if not self._bNotInteractive and curView and curView.UINavID == UIName2ID.AssemblyHDPresetMainPanel then
        Facade.UIManager:CloseUI(self)
    end
end

function AssemblyHDPresetMainPanel:_OnFluctuationPopWindowClosed()
	self:RefreshView()
end

function AssemblyHDPresetMainPanel:RefreshView()
    if not self._curOutfitID then
        return
    end
	if self._curOutfitID > 0 then
		-- Server.ArmedForceServer:SetStyleRecord(self._curOutfitID)
        local moduleID = OutfitID2ModuleID[self._curOutfitID]
        local moduleUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(moduleID)
        if moduleUnlockInfo and not moduleUnlockInfo.bIsUnlocked then
            Module.CommonTips:ShowSimpleTip(moduleUnlockInfo.unlocktips)
        end
        Module.ModuleUnlock:SetButtonModuleUnlock(self._wtConfirmBtn, {moduleID = moduleID, fOnClickCallback = self.OnBtnProcessClicked, caller = self, bGrey = true, bIntercept = true, bShowUnlockTips = true})
	else
        self._wtConfirmBtn:SetIsEnabledStyle(true)
        self._wtConfirmBtn:RemoveEvent("OnClicked")
        self._wtConfirmBtn:Event("OnClicked", self.OnBtnProcessClicked, self)
        self._wtConfirmBtn:Event("OnDeClicked", self.OnBtnProcessClicked, self)
    end

	-- self:_OnRefreshBtnState()
end

function AssemblyHDPresetMainPanel:GenCurOperationOutfit()
	OutfitDataLogic.SendAnalysisData(Module.ArmedForce.Config.EAnalysisType.View,{OutfitID = self._curOutfitID > 0 and self._curOutfitID or 0})
	local curOperationOutfit = OutfitDataLogic.GenCurOperationOutfit(self._curOutfitID)
    self:_CheckAndFetchOutfitData(curOperationOutfit)
    Module.ArmedForce.Field:SetCurOperationOutfit(curOperationOutfit)
end

function AssemblyHDPresetMainPanel:UsedCurOperationOutfit()
    local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
    Timer.DelayCall(0, function ()
        OutfitDataLogic.UsedCurOperationOutfit(curOperationOutfit, true)
    end, self)
end

function AssemblyHDPresetMainPanel:_OnRefreshBtnState()
    self._bApplyAll = true
    -- 操作按钮
    self._curPriceValue = OutfitDataLogic.GetNeedBuyItemsCost()
    local btnText = ArmedForceConfig.Loc.UsePlanBtnText
    if self._curPriceValue > 0 then
        local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum() 
        local priceStr = CurrencyHelperTool.GetCurrencyNumFormatStr(self._curPriceValue, CurrencyHelperTool.EKMThousandsType.None)
        local priceIconText = ECurrencyClientType2RichIconTxtV2[ECurrencyClientType.OnlyUnBind]
        if currencyNum < self._curPriceValue then
            self._bApplyAll = false
            btnText = string.format(ArmedForceConfig.Loc.BuyLimitedPriceBtnText, priceIconText, priceStr)
        else
            local param = {
                ["currencyIconTxt"] = priceIconText,
                ["buyPriceStr"] = priceStr
            }
            btnText = StringUtil.Key2StrFormat(ArmedForceConfig.Loc.BuyPriceBtnText,param)
        end
    end
    self._wtConfirmBtn:SetMainTitle(btnText)

    -- BEGIN MODIFICATION @ VIRTUOS : 刷新购买、装备按钮图标
    if IsHD() then
        self:_InitShortcuts()
    end
    -- END MODIFICATION

    -- 保存方案按钮
    if self._curOutfitID > 0 then
        self._wtSavedPlanBtn:Collapsed()
    else
        self._wtSavedPlanBtn:Visible()
        local bEmptyPlan = self:CheckIsEmptyPlan()
        self._wtSavedPlanBtn:SetIsEnabledStyle(not bEmptyPlan)
    end

    -- 快速配装按钮
    if self._curOutfitID > 0 then
        self._wtMedicineOperationBtn:Collapsed()
        self._wtBulletOperationBtn:Collapsed()
    else
        self._wtMedicineOperationBtn:Visible()
        self._wtBulletOperationBtn:Visible()
    end

    self:_CheckSoldOutAbnormal()
end

function AssemblyHDPresetMainPanel:CheckIsEmptyPlan()
    local index = Module.ArmedForce:OutfitID_To_Index(self._curOutfitID)
    local customOutfit = Server.PresetServer:GetSOLArmedForceOutfit(index)
    if not string.isempty(customOutfit.name) then
        return false
    else
        local num = 0
        local curOperationOutfit = Module.ArmedForce.Field:GetCurOperationOutfit()
        if curOperationOutfit then
            for _, v in ipairs(curOperationOutfit.items) do
                if v.position ~= ESlotType.SafeBox then
                    num = num + table.nums(v.load_props)
                end
            end
            return num <= 0
        else
            return true
        end
    end
end


function AssemblyHDPresetMainPanel:SetInteractive(bInteractive)
    self._bNotInteractive = bInteractive
    if self._bNotInteractive then
        self._wtNotInteractive:Visible()
        self._wtConfirmBtn:SetIsEnabledStyle(false)
    else
        self._wtNotInteractive:Collapsed()
        self._wtConfirmBtn:SetIsEnabledStyle(true)
        self._wtConfirmBtn:RemoveEvent("OnDeClicked")
        self._wtConfirmBtn:Event("OnDeClicked", self.OnBtnProcessClicked, self)
    end
end

function AssemblyHDPresetMainPanel:_SetAllSlotViewOnHide()
    local function fSetItemViewOnHide(slotType)
        local slotView = self:_GetSlotView(slotType)
        if slotView then
            local allItemView = slotView:GetAllItemViews()
            for index, itemView in pairs(allItemView) do
                if itemView.OnHide then
                    itemView:OnHide()
                end
            end
        end
    end
    fSetItemViewOnHide(ESlotType.ChestHangingContainer)
    fSetItemViewOnHide(ESlotType.Pocket)
    fSetItemViewOnHide(ESlotType.BagContainer)
    fSetItemViewOnHide(ESlotType.SafeBoxContainer)
end

-- 根据slotType获取slotView
function AssemblyHDPresetMainPanel:_GetSlotView(slotType)
    local slotView
    if slotType == ESlotType.BagContainer then
        slotView = self._wtBagSlotView
    elseif slotType == ESlotType.ChestHangingContainer then
        slotView = self._wtChestHangingSlotView
    elseif slotType == ESlotType.Pocket then
        slotView = self._wtPocketSlotView
    elseif slotType == ESlotType.SafeBoxContainer then
        slotView = self._wtSafeBoxSlotView
    end
    -- todo 多个容器
    return slotView
end

function AssemblyHDPresetMainPanel:_CheckAndFetchOutfitData(operationOutfit)
    local curOutfitID = self._curOutfitID
    if not self._bFetchOutfitDataCD[curOutfitID] then
        self._bFetchOutfitDataCD[curOutfitID] = true
        if self._fetchOutfitDataTimerHandles[curOutfitID] then
            Timer.CancelDelay(self._fetchOutfitDataTimerHandles[curOutfitID])
        end
        OutfitDataLogic.DoBatchFetchOutfitData(operationOutfit)
        self._fetchOutfitDataTimerHandles[curOutfitID] = Timer.DelayCall(3, function ()
            self._bFetchOutfitDataCD[curOutfitID] = nil
            self._fetchOutfitDataTimerHandles[curOutfitID] = nil
        end, self)
    else
        logwarning("AssemblyHDPresetMainPanel:_CheckAndFetchOutfitData is CDing", curOutfitID)
    end

end

function AssemblyHDPresetMainPanel:ResetFetchOutfitDataTimers()
    for _, timerHandle in pairs(self._fetchOutfitDataTimerHandles) do
        Timer.CancelDelay(timerHandle)
    end
    self._fetchOutfitDataTimerHandles = {}
    self._bFetchOutfitDataCD = {}
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function AssemblyHDPresetMainPanel:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end
    self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtMainRoot, self, "Hittest")

    self._NavGroup_MyProgram = self._wtCustomDFCommonMainTabList._wtNavGroup --WidgetUtil.RegisterNavigationGroup(self._wtCustomDFCommonMainTabList, self, "Hittest")
    if self._NavGroup_MyProgram then
        self._OnFocusNavGroupReceivedHandle_MyProgram = self._NavGroup_MyProgram.OnNavGroupFocusReceivedEvent:Add(CreateCPlusCallBack(self._OnNavGroupFocusReceivedEvent, self))
        self._OnFocusNavGroupLostHandle_MyProgram = self._NavGroup_MyProgram.OnNavGroupFocusLostEvent:Add(CreateCPlusCallBack(self._OnNavGroupFocusLostEvent, self))
        self._NavGroup_MyProgram:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Default)
        local navStrategy = self._NavGroup_MyProgram:GetOwnerNavStrategy()
        if navStrategy then
            navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
        end
    end
    self._NavGroup_SystemRecommendation =  self._wtSystemDFCommonMainTabList._wtNavGroup --WidgetUtil.RegisterNavigationGroup(self._wtSystemDFCommonMainTabList, self, "Hittest")
    if self._NavGroup_SystemRecommendation then
        self._OnFocusNavGroupReceivedHandle_SystemRecommendation = self._NavGroup_SystemRecommendation.OnNavGroupFocusReceivedEvent:Add(CreateCPlusCallBack(self._OnNavGroupFocusReceivedEvent, self))
        self._OnFocusNavGroupLostHandle_SystemRecommendation = self._NavGroup_SystemRecommendation.OnNavGroupFocusLostEvent:Add(CreateCPlusCallBack(self._OnNavGroupFocusLostEvent, self))
        self._NavGroup_SystemRecommendation:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Default)
        local navStrategy = self._NavGroup_SystemRecommendation:GetOwnerNavStrategy()
        if navStrategy then
            navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
        end
    end

    self._NavGroup_Equipments = WidgetUtil.RegisterNavigationGroup(self._wtCustomNavCanvas, self, "Hittest")
    if self._NavGroup_Equipments then
        for i, Widget in ipairs(self._wtSlotViews) do
            self._NavGroup_Equipments:AddNavWidgetToArray(Widget)
        end
        self._NavGroup_Equipments:SetAutoUseRootGeometrySize(false)
        local navStrategy = self._NavGroup_Equipments:GetOwnerNavStrategy()
        if navStrategy then
            navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
        end
    end

    -- Back Pack
    self._NavGroup_BackPack = WidgetUtil.RegisterNavigationGroup(self._wtMainScrollBox, self, "Hittest")
    self._NavGroup_BackPack.OnNavGroupFocusReceivedEvent:Add(self._OnEquipWareHouseFocusReceived, self)
    if self._NavGroup_BackPack then
        self._NavGroup_BackPack:AddNavWidgetToArray(self._wtMainScrollBox)
        self._NavGroup_BackPack:SetScrollRecipient(self._wtMainScrollBox)
        self._NavGroup_BackPack:SetNavSelectorWidgetVisibility(true)
        local BackPackNavStrategy = self._NavGroup_BackPack:GetOwnerNavStrategy()
        if BackPackNavStrategy then
            BackPackNavStrategy:SetHitPadding(4.0)
            BackPackNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
        end
    end

    -- Safe Box
    self._NavGroup_SafeBox = WidgetUtil.RegisterNavigationGroup(self._wtSafeBoxContainerView, self, "Hittest")
    self._NavGroup_SafeBox.OnNavGroupFocusReceivedEvent:Add(self._OnEquipWareHouseFocusReceived, self)
    if self._NavGroup_SafeBox then
        self._NavGroup_SafeBox:AddNavWidgetToArray(self._wtSafeBoxContainerView)
        self._NavGroup_SafeBox:SetNavSelectorWidgetVisibility(true)
        local SafeBoxNavStrategy = self._NavGroup_SafeBox:GetOwnerNavStrategy()
        if SafeBoxNavStrategy then
            SafeBoxNavStrategy:SetHitPadding(4.0)
            SafeBoxNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
        end
    end

    -- Buttons
    self._NavGroup_Buttons = WidgetUtil.RegisterNavigationGroup(self._wtCustomNavCanvas_Buttons, self, "Hittest")
    if self._NavGroup_Buttons then
        self._NavGroup_Buttons:AddNavWidgetToArray(self._wtCustomNavCanvas_Buttons)
        local ButtonsNavStrategy = self._NavGroup_Buttons:GetOwnerNavStrategy()
        if ButtonsNavStrategy then
            ButtonsNavStrategy:SetHitPadding(4.0)
            ButtonsNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
        end
    end    

    WidgetUtil.BuildGroupTree(self._wtMainRoot) 
    local waitToFocusItem = Module.ArmedForce.Field:GetWaitToFocusItem()
    if isvalid(waitToFocusItem) then
        WidgetUtil.SetUserFocusToWidget(waitToFocusItem, true)
    else
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_MyProgram)
    end
    
    Module.ArmedForce.Field:SetWaitToFocusItem(nil)
end

function AssemblyHDPresetMainPanel:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup = nil

    self._NavGroup_Equipments = nil
    self._NavGroup_BackPack = nil
    -- self._NavGroup_SafeBox = nil
    self._NavGroup_Buttons = nil

    if self._NavGroup_MyProgram and self._OnFocusNavGroupReceivedHandle_MyProgram  then
        self._NavGroup_MyProgram.OnNavGroupFocusReceivedEvent:Remove(self._OnFocusNavGroupReceivedHandle_MyProgram)
        self._OnFocusNavGroupReceivedHandle_MyProgram = nil
        self._NavGroup_MyProgram = nil
    end

    if self._NavGroup_SystemRecommendation and self._OnFocusNavGroupReceivedHandle_SystemRecommendation  then
        self._NavGroup_SystemRecommendation.OnNavGroupFocusReceivedEvent:Remove(self._OnFocusNavGroupReceivedHandle_SystemRecommendation)
        self._OnFocusNavGroupReceivedHandle_SystemRecommendation = nil
        self._NavGroup_SystemRecommendation = nil
    end
    self._customTabItems = {}
    self._systemTabItems = {}

    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    self:_RemoveShortcuts()
end

-- 关闭页面中 “问号” 提示的可聚焦
function AssemblyHDPresetMainPanel:_DisabelHoverTipsFocus()
    if not IsHD() then
        return 
    end

    local containerSlotType = {
        ESlotType.ChestHangingContainer, -- 胸挂
        ESlotType.Pocket, -- 口袋
        ESlotType.BagContainer,
        ESlotType.SafeBoxContainer,
    }
    for _, slotType in ipairs(containerSlotType) do
        local containerView = self:_GetContainerView(slotType)
        if containerView then
            local wtTipsCheckBox = containerView:Wnd("wtHoverBtn", UIWidgetBase)
            local checkBtn = wtTipsCheckBox:Wnd("DFCheckBox_Icon", UICheckBox)
            if checkBtn then
                checkBtn:SetCppValue("IsFocusable", false)
            end
        end
    end
end

function AssemblyHDPresetMainPanel:_InitShortcuts()
    if not IsHD() then
        return 
    end
    self:_RemoveShortcuts()
    -- 确认方案
    self._ConfirmHandle = self:AddInputActionBinding("Assembly_Preset_Confirm", EInputEvent.IE_Pressed, self.OnBtnProcessClicked,self, EDisplayInputActionPriority.UI_Stack)
    self._wtConfirmBtn:SetDisplayInputActionWithLongPress(self._ConfirmHandle, self, "Assembly_Preset_Confirm", true, nil, true)
    if self._wtSavedPlanBtn:IsVisible() then
        -- 保存方案
        self._SaveOptionsHandle = self:AddInputActionBinding("Assembly_SaveOptions_Gamepad", EInputEvent.IE_Pressed, self._OnSavedPlanBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtSavedPlanBtn:SetDisplayInputActionWithLongPress(self._SaveOptionsHandle, self, "Assembly_SaveOptions_Gamepad", true, nil, true)
    end
end

function AssemblyHDPresetMainPanel:_RemoveShortcuts()
    if not IsHD() then
        return 
    end

    if self._ConfirmHandle then
        self:RemoveInputActionBinding(self._ConfirmHandle)
        self._ConfirmHandle = nil
    end
    if self._SaveOptionsHandle then
        self:RemoveInputActionBinding(self._SaveOptionsHandle)
        self._SaveOptionsHandle = nil
    end
end


function AssemblyHDPresetMainPanel:SetInputSummary(inputSummaryList, itemView)
    if self:IsInHideBeginState() or self:IsInHideState() then
        return
    end
    if self._is_enable_ then
        -- 是否悬浮到仓库的itemView上
        local bHoverWarehouseItemView = itemView ~= nil
        local bHoverWarehouseItemViewIsvalid = self:_CheckItemViewCanShowSummaryList(itemView)
        local summaryList = {}
        -- self._waitInputSummaryList = inputSummaryList
        if not bHoverWarehouseItemView or (bHoverWarehouseItemView and not table.isempty(inputSummaryList)) then
            -- 详情
            table.insert(summaryList, {actionName = "Assembly_Details_Gamepad",func = self.ShowHoverTips, caller = self ,bUIOnly = false, bHideIcon = false})
        end

        if bHoverWarehouseItemView and bHoverWarehouseItemViewIsvalid and self._curOutfitID <= 0 then
            local function fRemovefunc()
                self:_OnLeftDecClicked(itemView)
            end
            -- 放回
            table.insert(summaryList, {actionName = itemView.item.bNeedBuy and "Assembly_RemoveItemView_Gamepad" or "Assembly_RemoveItemViewToInventory_Gamepad",func = fRemovefunc, caller = self ,bUIOnly = false, bHideIcon = false})
        end

        if not bHoverWarehouseItemView or bHoverWarehouseItemViewIsvalid then
            if inputSummaryList then
                for _, summary in pairs(inputSummaryList) do
                    table.insert(summaryList, summary)
                end
            end
        end

        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false)
    end
end

function AssemblyHDPresetMainPanel:_CheckItemViewCanShowSummaryList(itemView)
    local bcanShowSummaryList = false
    if itemView and itemView.item and itemView.item.InSlot and itemView.item.InSlot:IsContainerSlot() then
        bcanShowSummaryList = true
    end
    return bcanShowSummaryList
end

function AssemblyHDPresetMainPanel:_BottomBarSummaryListChangedByItemView(inputSummaryList, itemView)
    self:SetInputSummary(inputSummaryList, itemView)
end


--- END MODIFICATION

--endregion
--==================================================
local ContainerSlotList = 
{
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.BagContainer,
    ESlotType.SafeBoxContainer
}
function AssemblyHDPresetMainPanel:ShowHoverTips()
    if not IsHD() then
        return 
    end
    -- 通过按键逐个显示HoverTips
    if self._curShowTipsIndex < 4 then
        local lastHoveTip = self:_GetContainerView(ContainerSlotList[self._curShowTipsIndex])
        if lastHoveTip and lastHoveTip.OnUnHoverTipByGamepad then
            lastHoveTip:OnUnHoverTipByGamepad()
        end
        self._curShowTipsIndex = self._curShowTipsIndex + 1
        local hoverTip = self:_GetContainerView(ContainerSlotList[self._curShowTipsIndex])
        if hoverTip and hoverTip.OnHoverTipByGamepad then
            hoverTip:OnHoverTipByGamepad()
            if self._wtMainScrollBox then
                self._wtMainScrollBox:ScrollWidgetIntoView(hoverTip, true, EDescendantScrollDestination.IntoView)
            end
        end
    elseif self._curShowTipsIndex == 4 then
        self:CloseHoverTips()
    end
end

function AssemblyHDPresetMainPanel:CloseHoverTips()
    if not IsHD() then
        return 
    end
    
    if self._curShowTipsIndex ~= 0 then
        local hoverTip = self:_GetContainerView(ContainerSlotList[self._curShowTipsIndex])
        if hoverTip and hoverTip.OnUnHoverTipByGamepad then
            hoverTip:OnUnHoverTipByGamepad()
        end
        self._curShowTipsIndex = 0
    end
end

function AssemblyHDPresetMainPanel:_ExitSwapItemMode()
    if Module.CommonWidget:IsInSwapingItem() then
        Module.CommonWidget:SetGamepadSwapItem(false)
    end
    -- she3移入InventoryNavManager统一控制
    Module.Inventory.Config.Events.evtRemoveYModeBlankItemVisibility:Invoke()
    Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(false)
end

-- 绑定输入类型切换事件
function AssemblyHDPresetMainPanel:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end
    if bEnable then
        if not self._onNotifyInputTypeChangedHandle then
            self._onNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end
    else
        if self._onNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._onNotifyInputTypeChangedHandle)
            self._onNotifyInputTypeChangedHandle = nil
        end
    end
end

-- 切换到非手柄时直接隐藏Tip
function AssemblyHDPresetMainPanel:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end
    -- 从手柄切到键鼠
    if not (inputType == EGPInputType.Gamepad) then
        -- 退出手柄移动物品状态
        self:_ExitSwapItemMode()
        -- -- 关闭手柄打开的HoverTips
        -- if self._wtEquipPanel and self._wtEquipPanel.CloseHoverTips then
        --     self._wtEquipPanel:CloseHoverTips()
        -- end
    end
    -- WarehousePanel
    -- self._wtAssemblyBackpack:OnInputTypeChanged(inputType)
end

-- 绑定手柄模拟鼠标左键拖拽事件
function AssemblyHDPresetMainPanel:_EnableSimulatedMouseDragHandle(bEnable)
    logwarning("yanmingjing:_EnableSimulatedMouseDragHandle:", bEnable)
    if not IsHD() then
        return 
    end
    if bEnable then
        if not self._onNotifySimulatedMouseDragHandle then
            self._onNotifySimulatedMouseDragHandle = WidgetUtil.BindSimulatedMouseDragEvent(self._OnGlobalSwapItem, self)
        end
    else
        if self._onNotifySimulatedMouseDragHandle then
            WidgetUtil.UnBindSimulatedMouseDragEvent(self._onNotifySimulatedMouseDragHandle)
            self._onNotifySimulatedMouseDragHandle = nil
        end
    end
end

function AssemblyHDPresetMainPanel:_OnGlobalSwapItem(bIsSwapItem)
    if not IsHD() then
        return 
    end
    self:_UpdateInputSummaryOnDrag(bIsSwapItem, false)
    UDFNavigationSelectorBase.SetForceHideSelectorRoot(bIsSwapItem)
    -- if WidgetUtil.IsGamepad() and bIsSwapItem == true then
    --     -- 在手柄拖拽时不希望导航到这些按钮
    --     self:_SetEnableHeroInfoBtns(false)
    -- else
    --     self:_SetEnableHeroInfoBtns(true)
    -- end
end

function AssemblyHDPresetMainPanel:_UpdateInputSummaryOnDrag(bIsDragging, bIsItemDragStart)
    -- logwarning("yanmingjing_UpdateInputSummaryOnDrag bIsDragging: " .. tostring(bIsDragging) .. " bIsItemDragStart: " .. tostring(bIsItemDragStart))
    if not IsHD() then
        return 
    end
    if bIsDragging then
        -- Module.CommonWidget:UnBindHoverViewDisplayInput()
        local summaryList = {}
        -- 交换物品
        table.insert(summaryList, {actionName = "SimulateDropItem_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        table.insert(summaryList, {actionName = "WareHouseExitSwapItem", func = self._ExitSwapItemMode, caller = self ,bUIOnly = false, bHideIcon = false})
        
        if bIsItemDragStart then
            table.insert(summaryList, {actionName = "RotateItemOnDrag_Gamepad", func = self.ToggleDragItemSpinByGamepad, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    else
        -- Module.CommonWidget:BindHoverViewDisplayInput()
    end
end

function AssemblyHDPresetMainPanel:_DisableDynamicNavConfig()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

---@param dragDropInfo ItemDragDropInfo
function AssemblyHDPresetMainPanel:_OnGlobalItemDragStart(dragDropInfo, operation, pointerEvent)
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_UpdateInputSummaryOnDrag(true, true)
        Module.CommonWidget:SetShouldTryFocusPostDrag(true)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    end
    --END MODIFICATION
end

function AssemblyHDPresetMainPanel:ToggleDragItemSpinByGamepad()
    if not IsHD() then
        return 
    end

    if Module.CommonWidget:IsInSwapingItem() == true then
        Module.CommonWidget:ToggleDragItemSpin()
    end
end

function  AssemblyHDPresetMainPanel:_OnEquipWareHouseFocusReceived()
    self:_DisableDynamicNavConfig()
    
    -- NoA导航影响拖拽放下，不好通过调用OnClicked模拟
    if Module.CommonWidget:GetIsDragging() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    else
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
    end

end


function AssemblyHDPresetMainPanel:_OnNavGroupFocusReceivedEvent(navGroup)
    if navGroup and (navGroup == self._NavGroup_MyProgram or navGroup == self._NavGroup_SystemRecommendation) then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoDPadUpDown_NoA, self)
        if not self._tabUpHandler then
            self._tabUpHandler = self:AddInputActionBinding("Common_DpadUp_Gamepad",EInputEvent.IE_Pressed,self.SwitchToUpTab,self,EDisplayInputActionPriority.UI_Stack)
        end
        if not self._tabDownHandler then
            self._tabDownHandler = self:AddInputActionBinding("Common_DpadDown_Gamepad",EInputEvent.IE_Pressed,self.SwitchToDownTab,self,EDisplayInputActionPriority.UI_Stack)
        end
        if self._customTabIndex >= 0 then -- 如果是自定义的
            local tabItem = self._customTabItems[self._customTabIndex]
            if tabItem then
                WidgetUtil.SetUserFocusToWidget(tabItem, true)
            end
        elseif self._systemTabIndex >= 0 then -- 如果是系统的
            local tabItem = self._systemTabItems[self._systemTabIndex]
            if tabItem then
                WidgetUtil.SetUserFocusToWidget(tabItem, true)
            end
        end
    end
end


function AssemblyHDPresetMainPanel:_OnNavGroupFocusLostEvent(navGroup)
    if navGroup and (navGroup == self._NavGroup_MyProgram or navGroup == self._NavGroup_SystemRecommendation) then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        if self._tabUpHandler then
            self:RemoveInputActionBinding(self._tabUpHandler)
            self._tabUpHandler = nil
        end
        if self._tabDownHandler then
            self:RemoveInputActionBinding(self._tabDownHandler)
            self._tabDownHandler = nil
        end
    end
end


function AssemblyHDPresetMainPanel:SwitchToUpTab()
    loginfo("[AssemblyHDPresetMainPanel] _OnNavGroupReceived SwitchToUpTab")
    self:SwitchTab(-1)
end

function AssemblyHDPresetMainPanel:SwitchToDownTab()
    loginfo("[AssemblyHDPresetMainPanel] _OnNavGroupReceived SwitchToDownTab")
    self:SwitchTab(1)
end

function AssemblyHDPresetMainPanel:SwitchTab(next)
    loginfo("[AssemblyHDPresetMainPanel] _OnNavGroupReceived SwitchTab", next)
    if self._customTabIndex >= 0 then -- 如果是自定义的
        loginfo("[AssemblyHDPresetMainPanel] _OnNavGroupReceived SwitchTab 自定义方案下标", self._customTabIndex)
        if self._customMainTabGroupDataList[self._customTabIndex + 1 + next] then --因为tab下标是从0开始的，需要加1,再检查加上next后有没有数据
            self._wtCustomDFCommonMainTabList:SetTabIndex(self._customTabIndex + next)
        else
            if next == 1 then
                self._wtSystemDFCommonMainTabList:SetTabIndex(0)
            else
                self._wtSystemDFCommonMainTabList:SetTabIndex(#self._systemMainTabGroupDataList - 1)
            end
        end
    elseif self._systemTabIndex >= 0 then -- 如果是系统的
        loginfo("[AssemblyHDPresetMainPanel] _OnNavGroupReceived SwitchTab 系统方案下标", self._systemTabIndex)
        if self._systemMainTabGroupDataList[self._systemTabIndex + 1 + next] then --因为tab下标是从0开始的，需要加1,再检查加上next后有没有数据
            self._wtSystemDFCommonMainTabList:SetTabIndex(self._systemTabIndex + next)
        else
            if next == 1 then
                self._wtCustomDFCommonMainTabList:SetTabIndex(0)
            else
                self._wtCustomDFCommonMainTabList:SetTabIndex(#self._customMainTabGroupDataList - 1)
            end
        end
    end
end

return AssemblyHDPresetMainPanel