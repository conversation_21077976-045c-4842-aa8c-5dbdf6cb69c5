----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local GunsmithSceneUI = require "DFM.Business.Module.GunsmithModule.UI.GunsmithSceneUI"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GunsmithTLogLogic = require "DFM.Business.Module.GunsmithModule.Logic.TLog.GunsmithTLogLogic"
local EGunsmithEnvironmentCameraFocusCenterPoint = require "DFM.Business.Module.GunsmithModule.Data.Environment.EGunsmithEnvironmentCameraFocusCenterPoint"
local GunsmithFinetuneLogic = require "DFM.Business.Module.GunsmithModule.Logic.Finetune.GunsmithFinetuneLogic"
local UGPInputHelper = import "GPInputHelper"
local LiteDownloadManager   = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"

local EGunsmithMainUIPagination = {
    Unknown         = 0,
    Preview         = 1,
    Finetune        = 2,
    Solution        = 3,
    Appearance      = 4,
    WeaponUpgrade   = 5,
}

local EGunsmithMainUIPagination2UINavID = {
    UIName2ID.GunsmithMainPreviewUI,
    UIName2ID.GunsmithFinetuneMainUI,
    UIName2ID.GunsmithSolutionMainUI,
    UIName2ID.GunsmithMainAppearanceUI,
    UIName2ID.WeaponUpgradePanel,
}

local DefaultMainTabIndex = EGunsmithMainUIPagination.Preview

---@class GunsmithMainUI : GunsmithSceneUI
local GunsmithMainUI = ui("GunsmithMainUI", GunsmithSceneUI)

function GunsmithMainUI:Ctor()
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    end
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    self._wt_Slot_Preview = self:Wnd("wt_Slot_Preview", UIWidgetBase)
    self._wt_Slot_Finetune = self:Wnd("wt_Slot_Finetune", UIWidgetBase)
    self._wt_Slot_Solution = self:Wnd("wt_Slot_Solution", UIWidgetBase)
    self._wt_Slot_Appearance = self:Wnd("wt_Slot_Appearance", UIWidgetBase)
    self._wt_Slot_WeaponUpgrade = self:Wnd("wt_Slot_WeaponUpgrade", UIWidgetBase)
    self._lut_slot = { self._wt_Slot_Preview, self._wt_Slot_Finetune, self._wt_Slot_Solution, self._wt_Slot_Appearance, self._wt_Slot_WeaponUpgrade }

    self:_InternalOnInitialize()
    -- self:_InitializeMainTab()

    self._bResetUIState = false
    self._focusUIPagination = DefaultMainTabIndex
    self._focusMianTabIndex = nil
end

function GunsmithMainUI:OnInitExtraData(uiParam)
    GunsmithSceneUI.OnInitExtraData(self, uiParam)
    self:_InternalProcessFocusMianTabIndex()
    self._focusUIPagination = self._focusMianTabIndex or DefaultMainTabIndex
    self:_InitializeMainTab()
end

function GunsmithMainUI:Destroy()
    self._lut_slot = nil
end

function GunsmithMainUI:DebugGamescom(uiParam)
    self._uiParam = uiParam
    GunsmithUIContextLogic.ProcessContextFromUIParam(uiParam)
    self:OnProcessUIUpdate(true)
end

function GunsmithMainUI:OnShowBegin()
    GunsmithTLogLogic.TLogEnterGunsmith()
    GunsmithSceneUI.OnShowBegin(self)
    self:ProcessCommonBarBackRegisterEvent()
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.GunsmithMainUIOnShowBegin)
end

function GunsmithMainUI:OnShow()
    GunsmithSceneUI.OnShow(self)

    local bAutoDownloadPak = Module.Gunsmith.Config.Const.GUNSMITH_AUTO_DOWNLOAD_PAK and IsMobile()
    -- if bAutoDownloadPak then
    --     if LiteDownloadManager:IsSupportLitePackage() then
    --         local netState = LiteDownloadManager:GetNetworkState()
    --         if netState == 1 then
    --             local autodownloadModules = {"FashionLong1Only", "FashionLong2Only", "FashionLong3Only"}
    --             local need_download_modules = {}
    --             for k, moduleName in pairs(autodownloadModules) do
    --                 local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
    --                 if bDownloaded == false then
    --                     local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(moduleName)
    --                     local isWaiting = LiteDownloadManager:IsWaitingByModuleName(moduleName)

    --                     if isDownloading == false and isWaiting == false then
    --                         table.insert(need_download_modules, moduleName)
    --                     end
    --                 end
    --             end

    --             if #need_download_modules > 0 then
    --                 LiteDownloadManager:CheckAndDownloadAll(need_download_modules, EDownloadTrigger.GunSmith, EDownloadStyle.Auto)
    --             end

    --             loginfo("[GunsmithMainUI] LiteDownloadManager:CheckAndDownloadAll, count:"..tostring(#need_download_modules))
    --         end
    --     end
    -- end
end

function GunsmithMainUI:OnHide()
    GunsmithSceneUI.OnHide(self)
    self:ProcessCommonBarBackUnregisterEvent()
    --@todo MS24临时修复代码
    if DFHD_LUA == 1 then
        local idList = {"GunsmithSkinDefaultMainUI", "GunsmithFinetuneMainUI", "GunsmithMainPreviewUI"}
        for _, id in ipairs(idList) do
            UGPInputHelper.WantInputMode_GameAndUI(GetGameInstance(), id, false, false)
        end
    end
end

function GunsmithMainUI:GetTitle()
    return Module.Gunsmith.Config.Loc.GunsmithMainPreviewTitle
end

function GunsmithMainUI:_InitializeMainTab()
    local tabTextList = Module.Gunsmith.Config.MainUIConfig.TabText
    local tabImageList = Module.Gunsmith.Config.MainUIConfig.TabImage
    local groupID = self:GetGroupIDFromUIParam()
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP(groupID)
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange(groupID)
    local bShowWeaponUpgrade = bIsMP and not bIsRange
    if bShowWeaponUpgrade then
        tabTextList = Module.Gunsmith.Config.MainUIConfig.MPExceptRangeTabText
        tabImageList = Module.Gunsmith.Config.MainUIConfig.MPExceptRangeTabImage
    end

    local appearanceTabTxtList = Module.Gunsmith.Config.MainUIConfig.SubTabPartText
    local appearanceImgPathList = Module.Gunsmith.Config.MainUIConfig.SubTabPartImage
    if not IsHD() then
        appearanceTabTxtList = {}
    end

    local appearanceTabLockData = {
        firstLockID = Module.Gunsmith.Config.Const.GUNSMITH_MAINVIEW_APPEARANCETAB,
    }

    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, {
        tabTxtList = tabTextList,
        imgPathList = tabImageList,
        fCallbackIns = SafeCallBack(self._OnMainTabClicked, self),
        defalutIdx = self._focusUIPagination,
        bTriggerCallback = false,
        tertiaryTabs = {
            [EGunsmithMainUIPagination.Appearance] = {
                tabTxtList = appearanceTabTxtList,
                imgPathList = appearanceImgPathList,
                fCallbackIns = SafeCallBack(self._OnMainAppearanceSubTabClicked, self),
                defalutIdx = 1,
            }
        },
        lockDataList = {
            [EGunsmithMainUIPagination.Appearance] = appearanceTabLockData
        }
    })

end

function GunsmithMainUI:_OnMainTabClicked(curActiveIndex, lastActiveIndex)
    if curActiveIndex == EGunsmithMainUIPagination.Unknown then
        return
    end

    if self._focusUIPagination == curActiveIndex then
        return
    end
    self:_InternalProcessTabUpdate(curActiveIndex)
end

function GunsmithMainUI:_OnMainAppearanceSubTabClicked(curActiveIndex, lastActiveIndex, tabLevel)
    if self._focusUIPagination ~= EGunsmithMainUIPagination.Appearance or tabLevel ~= 3 then
        return
    end

    local partUI = self:GetChild(EGunsmithMainUIPagination2UINavID[EGunsmithMainUIPagination.Appearance], 0)
    if partUI ~= nil then
        partUI:_OnTabIndexChanged(curActiveIndex, lastActiveIndex)
    end
end

function GunsmithMainUI:_OnCloseProcessCommonBarBack()
    local function fOnConfim()
        self._bResetUIState = true
        self:ProcessCommonBarBackUnregisterEvent()
        Module.CommonBar:ForceInvokeBack()
    end

    local function fOnCancel()
        -- self._bResetUIState = true
        LogAnalysisTool.SignButtonClicked(Module.Gunsmith.Config.Const.GUNSMITH_MAINUI_SIMULATE_CANCEL_BUTTON_ID)
    end

    GunsmithTLogLogic.TLogExitGunsmith()

    local bSimulate = GunsmithUIContextLogic.GetIsSimulateState()
    -- local bIsFromUserPropInfo = GunsmithUIContextLogic.GetIsFromUserPropInfo()
    local bShowTips = bSimulate
    if not bShowTips then
        fOnConfim()
        return
    end

    LogAnalysisTool.SignButtonClicked(Module.Gunsmith.Config.Const.GUNSMITH_MAINUI_SIMULATE_BUTTON_ID)

    Module.CommonTips:ShowConfirmWindow(Module.Gunsmith.Config.Loc.GunsmithPreviewOnCloseWhenSimulatingText, fOnConfim, fOnCancel)
end

function GunsmithMainUI:OnProcessContextFromUIParam()
    GunsmithSceneUI.OnProcessContextFromUIParam(self, false)
end

function GunsmithMainUI:OnForceProcessUI()
    local bFirstProcess = self:GetFristProcessOnSceneLoadedFromPush()

    local focus = self._focusUIPagination
    self._focusUIPagination = EGunsmithMainUIPagination.Unknown
    if bFirstProcess then
        focus = self._focusMianTabIndex or DefaultMainTabIndex
    end
    GunsmithUIContextLogic.UpdateMissionData()
    local bIsSubStageChangeEnter = GunsmithUIContextLogic.GetIsSubStageChangeEnter()
    if not bIsSubStageChangeEnter then
        Module.CommonBar:CheckTopTabGroup(focus, true, 2)
    end

    --- pc CheckTopTabGroup， bForceCallback 函数没有回调
    if self._focusUIPagination == EGunsmithMainUIPagination.Unknown then
        self:_InternalProcessTabUpdate(focus, false)
    end
end

function GunsmithMainUI:OnProcessSceneObject()
end

function GunsmithMainUI:_InternalOnInitialize()
    self:RegisterUIWindows(EGunsmithMainUIPagination2UINavID)
end

function GunsmithMainUI:_InternalProcessTabUpdate(index, bFromUserClicked)
    self._focusUIPagination = index
    bFromUserClicked = setdefault(bFromUserClicked, false)

    if self._focusUIPagination == EGunsmithMainUIPagination.Appearance then
        LogAnalysisTool.SignButtonClicked(Module.Gunsmith.Config.Const.GUNSMITH_MAINUI_SKIN_BUTTON_ID)
    end

    local bIsProcessedContext = GunsmithFinetuneLogic.GetIsProcessContext(self)

    local slot = self._lut_slot[index]
    local ui = self:ShowUIWindowFromIndex(index, slot, self._uiParam)
    if ui ~= nil then
        ui:OpenFromMainUI(bFromUserClicked, bIsProcessedContext)
    end

    -- self:ProcessCommonBarBackRegisterEvent()
end

function GunsmithMainUI:ProcessCommonBarBackRegisterEvent()
    self._bResetUIState = false
    -- Module.CommonBar:BindBackHandler(self._OnCloseProcessCommonBarBack, self)
    Module.CommonBar:BindPersistentBackHandler(self._OnCloseProcessCommonBarBack, self)
end

function GunsmithMainUI:ProcessCommonBarBackUnregisterEvent()
    Module.CommonBar:BindPersistentBackHandler()
    self:_InternalProcessUIStateOnHide()
end

function GunsmithMainUI:_InternalProcessUIStateOnHide()
    if not self._bResetUIState then
        return
    end

    self._bResetUIState = false
    GunsmithUIContextLogic.SetUIStateDefault()
end

function GunsmithMainUI:GetGroupIDFromUIParam()
    local bIsNotValid = isinvalid(self._uiParam)
    if bIsNotValid then
        return
    end
    return self._uiParam:GetGroupID()
end

function GunsmithMainUI:_InternalProcessFocusMianTabIndex()
    self._focusMianTabIndex = nil
    local bIsNotValid = isinvalid(self._uiParam)
    if bIsNotValid then
        return
    end

    self._focusMianTabIndex = self._uiParam:GetMainTabIndex()
    self._uiParam:SetMainTabIndex(nil)
end

return GunsmithMainUI