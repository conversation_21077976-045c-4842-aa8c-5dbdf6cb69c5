----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------



local LanguagePopView = ui("LanguagePopView")

local LoginLogic = require("DFM.Business.Module.LoginModule.LoginLogic")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local UGPAudioStatics = import"GPAudioStatics"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local LoginConfig = Module.Login.Config
local EVersionType= import"EVersionType"
local LitePackageConfig = Module.LitePackage.Config

local function log(...)
    loginfo("[LanguagePopView]", ...)
end

function LanguagePopView:Ctor()
    self._curLanguage = LoginLogic.GetCurrentCulture()
    self._preLanguage = self._curLanguage
    self._checkBoxMap = {} -- 以语言码保存checkBox对象

    self._cultureKeyList = {} -- 保存当前语言的配置信息的Key，存在先后顺序

    self._wtCommonPopWinV2 = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self.CloseSelf,self)
    self._wtCommonPopWinV2:BindCloseCallBack(fCallbackIns)

    -- self._wtCancelBtn = self:Wnd("WBP_CommonButtonV1S1", UIWidgetBase):Wnd("Button_Common", UIButton)
    -- self._wtConfirmBtn = self:Wnd("WBP_CommonButtonV1S2", UIWidgetBase):Wnd("Button_Common", UIButton)
    -- self._wtConfirmBtn:Event("OnClicked", self._OnConfirmBtnClick, self)
    -- self._wtCancelBtn:Event("OnClicked", self._OnCloseBtnClick, self)

    -- 重载按钮音频
    -- self._wtCancelBtn:OverLoadSound(DFMAudioRes.UICancel)
    -- self._wtConfirmBtn:OverLoadSound(DFMAudioRes.UIConfirm)
    self._wtCommonPopWinV2:SetCloseSound(DFMAudioRes.UIClose)

    self._bIsGMView = false

    self:InitCultureList()
    self:SetBtnType()
end

function LanguagePopView:InitExtraData(bIsGMView)
    bIsGMView = bIsGMView or false
    if not bIsGMView then
        return
    end

    logwarning("Open Language GM View!!!")
    self._bIsGMView = true
    self:InitCultureList()
end

function LanguagePopView:SetBtnType()
    local btnIns = self._wtCommonPopWinV2:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterCancelConfirm,
    {
        { btnText = LitePackageConfig.Loc.LitePackageMainPanel_Download_Cancel,
            fClickCallback = self._OnCloseBtnClick, caller = self, bNeedClose = false },
        { btnText = LitePackageConfig.Loc.LitePackageMainPanel_Download_Start, fClickCallback = self._OnConfirmBtnClick, caller = self,
            bNeedClose = false },
    }, true)
    if #btnIns > 1 then
        self._wtCancelBtn = btnIns[1]
        self._wtConfirmBtn = btnIns[2]
    end

    -- 重载按钮音频
    if self._wtCancelBtn and self._wtCancelBtn.Button_Common then
        self._wtCancelBtn.Button_Common:OverLoadSound(DFMAudioRes.UICancel)
    end

    if self._wtConfirmBtn and self._wtConfirmBtn.Button_Common then
        self._wtConfirmBtn.Button_Common:OverLoadSound(DFMAudioRes.UIConfirm)
    end

end



function LanguagePopView:InitCultureList()
    log("InitCultureList()")

    self._cultureKeyList = {} -- 先置空
    self._cultureConfig = Facade.TableManager:GetTable("Localize/LocalizeCultureConfig")
    for cultureKey, value in pairs(self._cultureConfig) do
        if not self._cultureKeyList[cultureKey] then
            if VersionUtil.IsShipping() then
                if table.contains(value.Versions, EVersionType.Shipping) and LocalizeTool.CheckPlatform(value.Platforms) then
                    table.insert(self._cultureKeyList, cultureKey)
                end
            else
                if table.contains(value.Versions, EVersionType.Test) and LocalizeTool.CheckPlatform(value.Platforms) then
                    table.insert(self._cultureKeyList, cultureKey)
                end
            end
        end
        -- table.insert(self._cultureKeyList, cultureKey)
    end
    -- 排序，目前是乱序
    table.sort(self._cultureKeyList, function(a, b)
        return a < b
    end)
end

function LanguagePopView:OnOpen()
    self:_InitScrollGridBox()
end

function LanguagePopView:_InitScrollGridBox()
    log("LanguagePopView:_InitScrollGridBox()")
    self._wtCultureList = UIUtil.WndScrollGridBox(self, "CultureList", self.OnGetItemCount, self.OnProcessItemWidget)
    self._wtCultureList:RefreshAllItems()
end

function LanguagePopView:OnGetItemCount()
    log("LanguagePopView:OnGetItemCount()", #self._cultureKeyList)
    return #self._cultureKeyList
end

---@param itemWidget SystemSettingLanguageCell
function LanguagePopView:OnProcessItemWidget(position, itemWidget)
    log("LanguagePopView:OnProcessItemWidget()", position, itemWidget)
    local cultureInfo = self._cultureConfig[self._cultureKeyList[position]]
    if cultureInfo then
        local cultureSign = cultureInfo.CultureSign -- 获取语言码
        local function OnWidgetClicked()
            self._curLanguage = cultureSign
            self:RefrshView(cultureSign, self._checkBoxMap)--刷新当前选中效果
        end

        -- 替换为图片
        local displayLanguageText = string.format("<dfmrichtext type=\"img\" id=\"SetUpText_%s\"/>", cultureSign)
        if cultureSign == "zh-Hans" then
            displayLanguageText = "<dfmrichtext type=\"img\" id=\"SetUpText_zh\"/>" -- 海外主机单独支持简体中文
        end
        local downloadInfo = {
            cultureWwise = cultureSign,
            downloadKey = "",
            displayName = displayLanguageText
        }

        if cultureSign ~= "en" and cultureSign ~= "zh-Hans" then
            local pakCulture = string.lower(cultureSign)
            pakCulture = string.gsub(pakCulture, "-", "_")
            -- 手游需要额外下载资源
            if not IsHD() and not IsInEditor() then
                downloadInfo.downloadKey = "Loc_" .. pakCulture
            end
        end

        -- 下载完毕回调，提示用户
        local function onCultureResDownloadFinished()
            loginfo("Culture res download finished!", cultureSign, downloadInfo.downloadKey)
            -- 弹窗提示即可
            Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.CultureResDownloadFinished, {["CultureName"] = displayLanguageText}))
        end

        itemWidget:InitItem(cultureSign, downloadInfo, OnWidgetClicked, nil, onCultureResDownloadFinished)
        if string.lower(cultureSign) == string.lower(self._curLanguage) then
            itemWidget:SetSelected(true)
        else
            itemWidget:SetSelected(false)
        end
        self._checkBoxMap[cultureSign] = itemWidget
    end
end

function LanguagePopView:RefrshView(selectedCulture)
    for culture, itemWidget in pairs(self._checkBoxMap) do
        if string.lower(culture) == string.lower(selectedCulture) then
            itemWidget:SetSelected(true)
        else
            itemWidget:SetSelected(false)
        end
    end
end

function LanguagePopView:CloseSelf()
    -- 由于从按钮关闭UI时不播放动画，该问题等待框架层统一处理，暂时先手动播放
    self._wtCommonPopWinV2:PlayAnimation(self._wtCommonPopWinV2.Ani_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    Timer.DelayCall(0.4,function()
        Facade.UIManager:CloseUI(self)
    end)
end

function LanguagePopView:_OnCloseBtnClick()
    self._wtCommonPopWinV2:_OnCloseBtnClick()
end

function LanguagePopView:_GetTextByCultureSign(culture)
    log("GetTextByCultureSign", culture)

    for _, cultureKey in pairs(self._cultureKeyList) do
        if self._cultureConfig[cultureKey].CultureSign == culture then
            return self._cultureConfig[cultureKey].CultureDisplayName
        end
    end
    return "Miss Culture Text"
end

---点击确定后要么切换成功要么直接关闭弹窗
function LanguagePopView:_OnConfirmBtnClick()
    log("LanguagePopView:_OnConfirmBtnClick()")
    if self._curLanguage == self._preLanguage then
        self:CloseSelf()     
        return
    end

    local retVal = LocalizeTool.SetCurrentCulture(self._curLanguage)
    if retVal then
        Module.CommonTips:ShowSimpleTip(string.format(LoginConfig.Loc.LanguageTip,self:_GetTextByCultureSign(self._curLanguage)))

        -- 刷新音频本地化
        local language2VoiceCulture = LocalizeTool.GetLanguageVoiceCulture()
        LocalizeTool.SetCurrentAudioCulture(language2VoiceCulture)
        
        self:CloseSelf()
        Module.Login.Config.Events.evtOnLanguageChanged:Invoke()
    else
        Module.CommonTips:ShowSimpleTip(LoginConfig.Loc.LanguageFailTip)
        self:CloseSelf()
    end
end

function LanguagePopView:OnNavBack()
    return false
end

return LanguagePopView