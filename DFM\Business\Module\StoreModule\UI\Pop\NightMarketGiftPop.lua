----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class NightMarketGiftPop : LuaUIBaseView
local NightMarketGiftPop = ui("NightMarketGiftPop")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"

function NightMarketGiftPop:Ctor()
    self._freeGetBtn = self:Wnd("wtCommonButtonV1S1", UIButton)
    self._freeGetBtn:Event("OnClicked", self.GetFree, self)

    self._richText = self:Wnd("DFRichTextBlock_1", UITextBlock)
    self._numText = self:Wnd("DFTextBlock_103", UITextBlock)

    self._goodName = self:Wnd("DFTextBlock_42", UITextBlock)
    self._goodDesc = self:Wnd("DFTextBlock", UITextBlock)
    self._tipText = self:Wnd("DFTextBlock_1", UITextBlock)
end

function NightMarketGiftPop:OnInitExtraData(goods)
    self.goods = goods
    self._tipText:Collapsed()
    local currencyType = goods.currency_type
    if currencyType == nil then
        currencyType = ***********
    end
    local priceStr = string.format(StoreConfig.Loc.ItemPriceNormal,
            Module.Currency:GetRichTxtImgByItemId(currencyType),
            MathUtil.GetNumberFormatStr(0))
    self._richText:SetText(priceStr)

end

function NightMarketGiftPop:OnShowBegin()
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestSuc, self._OnStoreBuySuc, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestUpdate, self._OnStoreBuyRecordUpdate, self)

    self._freeGetBtn:SetMainTitle(Module.Store.Config.Loc.FreeToGet)
    self._numText:SetText(self.goods.prop.num)
    self._goodName:SetText(LocalizeTool.GetTransStr("LOC_StoreLuckyNestEventReward," .. tostring(self.goods.index_id) .. "_RewardName"))
    

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_LuckyNest_Free_Popup)
    self:_OnStoreBuyRecordUpdate()

    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

function NightMarketGiftPop:GetFree()
    local currency_type_sub = StoreLogic.GetSubstituteCurrencyItemID()
    if #self.goods.other_props > 0 then
        Server.StoreServer:SendShopBuyLuckyNestItemReq(16110000020, self.goods.currency_type, 0, currency_type_sub, 0)
        Server.StoreServer:SendShopBuyLuckyNestItemReq(32320000001, self.goods.currency_type, 0, currency_type_sub, 0)
    else
        Server.StoreServer:SendShopBuyLuckyNestItemReq(self.goods.prop.id, self.goods.currency_type, 0, currency_type_sub, 0)
    end
end

function NightMarketGiftPop:_OnStoreBuySuc()
end

function NightMarketGiftPop:_OnStoreBuyRecordUpdate()
    local luckyNestRecord = Server.StoreServer:GetLuckyNestBoughtGoods()

    if luckyNestRecord and luckyNestRecord[self.goods.prop.id] then
        self._freeGetBtn:SetIsEnabled(false)
        self._richText:Collapsed()
    else
        self._freeGetBtn:SetIsEnabled(true)
        self._richText:Visible()
    end
end

function NightMarketGiftPop:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function NightMarketGiftPop:OnHide()
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestSuc)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestUpdate)
end

function NightMarketGiftPop:_EnableGamepadFeature()

    -- 配置keyIcon
    if self._freeGetBtn then
        self._freeGetBtn:SetDisplayInputAction("MallPurchase", true, nil, true)
    end

    -- 配置输入
    if not self._Purchase then
        self._Purchase = self:AddInputActionBinding(
        "MallPurchase", 
        EInputEvent.IE_Pressed, 
        self.GetFree,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

end

function NightMarketGiftPop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end

end

return NightMarketGiftPop