----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local ArenaTeamDetailInfoItem = ui("ArenaTeamDetailInfoItem")

local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"

--- BEGIN MODIFICATION @ VIRTUOS
local UDFMPlatformUtils = import "DFMPlatformUtils"
--- END MODIFICATION

local ETeamIdx2ShortName = {[1] = "A", [2] = "B", [3] = "C", [4] = "D", [5] = "E"}

function ArenaTeamDetailInfoItem:Ctor()
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtDFButton = self:Wnd("DFButton_0", UIButton)
        if isvalid(self._wtDFButton) then
            -- 关闭作为容器的Button的聚焦，以支持内部实际有功能的Button的手柄导航
            self._wtDFButton:SetCppValue("IsFocusable", false)
        end
    end
    -- END MODIFICATION
    self._wtRankTB = self:Wnd("wtRankTB", UITextBlock)
    self._wtHeroAvatarIconImg = self:Wnd("wtHeroAvatarIconImg", UIImage)
    self._wtTeamNameTB = self:Wnd("wtTeamNameTB", UITextBlock)
    self._wtHeroExpertIconImg = self:Wnd("wtHeroExpertIconImg", UIImage)
    self._wtGameNickTB = self:Wnd("wtGameNickTB", UITextBlock)
    self._wtKillCountTB = self:Wnd("wtKillCountTB", UITextBlock)
    self._wtDeadCountTB = self:Wnd("wtDeadCountTB", UITextBlock)
    self._wtAssistCountTB = self:Wnd("wtAssistCountTB", UITextBlock)
    self._wtWeaponBtn = self:Wnd("wtWeaponBtn", UIButton)
    self._wtWeaponWB = self:Wnd("wtWeaponWB", IVCommonItemTemplate)
    self._wtWeaponTA = UIUtil.WndTipsAnchor(self, "wtWeaponTA", self._OnShowWeaponTA, self._OnHideWeaponTA)
    self._wtHelmetBtn = self:Wnd("wtHelmetBtn", UIButton)
    self._wtHelmetWB = self:Wnd("wtHelmetWB", IVCommonItemTemplate)
    self._wtHelmetTA = UIUtil.WndTipsAnchor(self, "wtHelmetTA", self._OnShowHelmetTA, self._OnHideHelmetTA)
    self._wtBreastPlateBtn = self:Wnd("wtBreastPlateBtn", UIButton)
    self._wtBreastPlateWB = self:Wnd("wtBreastPlateWB", IVCommonItemTemplate)
    self._wtBreastPlateTA = UIUtil.WndTipsAnchor(self, "wtBreastPlateTA", self._OnShowBreastPlateTA, self._OnHideBreastPlateTA)

    --- BEGIN MODIFICATION @ VIRTUOS: Platform icon
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION

    -- azhengzheng:详情
    self._wtTipsBtn = self:Wnd("wtTipsBtn", UIButton)
    self._wtTipsBtn:Event("OnClicked", self._OnTipsBtnClicked, self)
end

function ArenaTeamDetailInfoItem:OnHideBegin()
    self:_OnHideWeaponTA()
    self:_OnHideHelmetTA()
    self:_OnHideBreastPlateTA()
end

function ArenaTeamDetailInfoItem:RefreshView(teamPlayerInfo)
    if not teamPlayerInfo then
        return
    end

    self._teamPlayerInfo = teamPlayerInfo

    self._wtRankTB:SetText(teamPlayerInfo.rank == 0 and "-" or teamPlayerInfo.rank)
    local heroAvatarIcon = HeroHelperTool.GetHeroAvatarIcon(teamPlayerInfo.heroID)

    if heroAvatarIcon then
        self._wtHeroAvatarIconImg:AsyncSetImagePath(heroAvatarIcon)
    end

    self._wtTeamNameTB:SetText(ETeamIdx2ShortName[teamPlayerInfo.teamIdx] or "-")
    local heroExpertIcon = HeroHelperTool.GetHeroExpertIcon(teamPlayerInfo.heroID)

    if heroExpertIcon then
        self._wtHeroExpertIconImg:AsyncSetImagePath(heroExpertIcon)
    end

    self._wtGameNickTB:SetText(teamPlayerInfo.gameNick or "-")
    self._wtKillCountTB:SetText(teamPlayerInfo.killCount)
    self._wtDeadCountTB:SetText(teamPlayerInfo.deadCount or "-")
    self._wtAssistCountTB:SetText(teamPlayerInfo.assistCount or "-")

    local onItemBaseClicked = function()
    end

    if teamPlayerInfo.weapon then
        self._weaponName = teamPlayerInfo.weapon.name or "-"
        self._wtWeaponWB:InitItem(teamPlayerInfo.weapon)
        self._wtWeaponWB:BindCustomOnClicked(onItemBaseClicked)
        self._wtWeaponWB:SetVisibility(ESlateVisibility.HitTestInvisible)
        self._wtWeaponBtn:Visible()
    else
        self._wtWeaponBtn:Collapsed()
    end

    if teamPlayerInfo.helmet then
        self._helmetName = teamPlayerInfo.helmet.name
        self._wtHelmetWB:InitItem(teamPlayerInfo.helmet)
        self._wtHelmetWB:BindCustomOnClicked(onItemBaseClicked)
        self._wtHelmetWB:SetVisibility(ESlateVisibility.HitTestInvisible)
        self._wtHelmetBtn:Visible()
    else
        self._wtHelmetBtn:Collapsed()
    end

    if teamPlayerInfo.breastPlate then
        self._breastPlateName = teamPlayerInfo.breastPlate.name
        self._wtBreastPlateWB:InitItem(teamPlayerInfo.breastPlate)
        self._wtBreastPlateWB:BindCustomOnClicked(onItemBaseClicked)
        self._wtBreastPlateWB:SetVisibility(ESlateVisibility.HitTestInvisible)
        self._wtBreastPlateBtn:Visible()
    else
        self._wtBreastPlateBtn:Collapsed()
    end

    if teamPlayerInfo.bMySelf then
        self:BPSetType(2)
    elseif teamPlayerInfo.bMyTeam then
        self:BPSetType(1)
    else
        self:BPSetType(0)
    end

    --- BEGIN MODIFICATION @ VIRTUOS: TRC/XR - update platform icon
    -- 这里的实现非常临时！！！
    -- 传入数据中的platID是0，且受接口限制，现在需要分平台操作。
    if IsConsole() then
        if teamPlayerInfo.platID ~= 0 then
            self:SetPlatformIDType(teamPlayerInfo.platID, teamPlayerInfo.bMySelf)
            
            if IsPS5() and teamPlayerInfo.platID == PlatIDType.Plat_Playstation then
                local callback = function(onlineID)
                    self._wtGameNickTB:SetText(onlineID)
                end
                Module.Social:AsyncGetPS5OnlineIdByUID(teamPlayerInfo.playerID, callback, self)
            end
        else
            self._onlineIdReqSuccess = false
 
            local req = pb.CSAccountGetPlayerProfileReq:New()
            req.player_id = teamPlayerInfo.playerID
            req.client_flag = 0
            req:Request(function(res)
                -- 如果同一个OpenId绑定了两个Console平台，这里查询到的不一定跟当前平台一致，所以如果是PS5已经设置过就不用再更新了
                if not self._onlineIdReqSuccess then
                    self:SetPlatformIDType(res.plat_id, teamPlayerInfo.bMySelf)
                end
            end, {bEnableHighFrequency = true})
 
            -- Override platform icon to ps5 if ps5 online id found.
            if IsPS5() then
                local callback = function(onlineID)
                    self._onlineIdReqSuccess = true
 
                    self._wtGameNickTB:SetText(onlineID)
 
                    self:SetPlatformIDType(PlatIDType.Plat_Playstation, teamPlayerInfo.bMySelf)
                end
                Module.Social:AsyncGetPS5OnlineIdByUID(teamPlayerInfo.playerID, callback, self)
            end
        end
    end
    --- END MODIFICATION
end

function ArenaTeamDetailInfoItem:_OnTipsBtnClicked()
    -- azhengzheng:历史战绩要求 因为在看别人战绩时 bMySelf表示的是别人自己 显示高亮 但不能用于屏蔽自己
    if not self._teamPlayerInfo or self._teamPlayerInfo.playerID == Server.AccountServer:GetPlayerId() then
        return
    end

    local info = {
        player_id = self._teamPlayerInfo.playerID,
        nick_name = self._teamPlayerInfo.gameNick,
        report_type = 3,
    }
    local btnTbl = {
        HeadButtonType.AddFriend,
        HeadButtonType.Report,
    }

    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, info, self, btnTbl, FriendApplySource.MpSettlementApply,
            TeamInviteSource.FromAll, nil, true, UWidgetLayoutLibrary.GetMousePositionOnViewport(GetWorld()))
        return
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, info, self._wtTipsBtn, btnTbl, FriendApplySource.MpSettlementApply,
        TeamInviteSource.FromAll, nil, true)
end

function ArenaTeamDetailInfoItem:_OnShowWeaponTA()
    if self._weaponTAHandle then
        return
    end

    self._weaponTAHandle = Module.CommonTips:ShowCommonMessagesWithAnchor({{textContent = self._weaponName, styleRowId = "C000"}}, self._wtWeaponTA)
end

function ArenaTeamDetailInfoItem:_OnHideWeaponTA()
    if not self._weaponTAHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._weaponTAHandle, self._wtWeaponTA)
    self._weaponTAHandle = nil
end

function ArenaTeamDetailInfoItem:_OnShowHelmetTA()
    if self._helmetTAHandle then
        return
    end

    self._helmetTAHandle = Module.CommonTips:ShowCommonMessagesWithAnchor({{textContent = self._helmetName, styleRowId = "C000"}}, self._wtHelmetTA)
end

function ArenaTeamDetailInfoItem:_OnHideHelmetTA()
    if not self._helmetTAHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._helmetTAHandle, self._wtHelmetTA)
    self._helmetTAHandle = nil
end

function ArenaTeamDetailInfoItem:_OnShowBreastPlateTA()
    if self._breastPlateTAHandle then
        return
    end

    self._breastPlateTAHandle = Module.CommonTips:ShowCommonMessagesWithAnchor({{textContent = self._breastPlateName, styleRowId = "C000"}}, self._wtBreastPlateTA)
end

function ArenaTeamDetailInfoItem:_OnHideBreastPlateTA()
    if not self._breastPlateTAHandle then
        return
    end

    Module.CommonTips:RemoveCommonMessageWithAnchor(self._breastPlateTAHandle, self._wtBreastPlateTA)
    self._breastPlateTAHandle = nil
end

--- BEGIN MODIFICATION @ VIRTUOS
function ArenaTeamDetailInfoItem:SetPlatformIDType(platID, isSelf)
    if IsConsole() and self._wtPlatformIcon then
        -- if "is self" using black version plat icon, otherwise the default one.
        local platIconPath = Module.Friend:GetPlatformIconPath(platID, isSelf)
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)

            -- TRC: In order to show sprite asset original color, using white brush color to prevent any overrides.
            self._wtPlatformIcon:SetColorAndOpacity(FLinearColor(1, 1, 1, 1))
            self._wtPlatformIcon:SelfHitTestInvisible()
        else
            self._wtPlatformIcon:Collapsed()
        end

        if not Server.AccountServer:IsCrossPlat() then
            logerror("ArenaTeamDetailInfoItem:SetPlatformIDType", "not cross plat")
            self._wtPlatformIcon:Collapsed()
        end

        logerror("ArenaTeamDetailInfoItem:SetPlatformIDType Hide Arena Platform Icon in She2")
        if not Facade.GameFlowManager:CheckIsInFrontEnd() then
            self._wtPlatformIcon:Collapsed()
        end
    end
end
--- END MODIFICATION

return ArenaTeamDetailInfoItem