----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityRadioHotPopWindow : LuaUIBaseView
local ActivityRadioHotPopWindow = ui("ActivityRadioHotPopWindow")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local ActivityConfig = Module.Activity.Config

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function ActivityRadioHotPopWindow:Ctor()
    self._activityID = 0
    self._activityInfo = {}
    self._sbcInfo = {}
    self._exchangePropList = {}

    -- root
    self._wtRootWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)

    -- 时间周期文本
    self._wtTimeCycleTxt = self:Wnd("DFTextBlock_143", UITextBlock)

    -- 本期热品列表
    self._wtScrollGridBox = UIUtil.WndScrollGridBox(self, "DFScrollGridBox_49", self._OnGetActivityRromptCount, self._OnProcessActivityRromptWidget)
    
    -- 本期热品介绍文本
    self._wtDescTxt = self:Wnd("DFTextBlock_6", UITextBlock)

    -- 可兑换物品列表
    self._wtExchangeScrollBox = UIUtil.WndScrollGridBox(self, "DFScrollGridBox", self._OnGetExchangeRromptCount, self._OnProcessExchangeRromptWidget)

    -- 添加/移除按钮
    local btnMode = CommonPopWindows.EConfirmBtnType.CenterConfirm
    local confirmBtnBindData = {
        btnText = ActivityConfig.Loc.Confirm,
        fClickCallback = self._OnConfirmBtnClicked,
        caller = self,
        bNeedClose = false
    }
    self._wtConfirmBtns = self._wtRootWindow:SetConfirmBtnType(btnMode, {confirmBtnBindData}, false)

    -- 手柄适配
    if IsHD() then
        for _, btn in pairs(self._wtConfirmBtns or {}) do
            btn:SetDisplayInputAction("Common_ButtonBottom_Gamepad", false, nil, true)
        end
    end
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityRadioHotPopWindow:OnOpen()
    -- 设置关闭回调
    local fCallbackIns = CreateCallBack(self._OnClosePopUI,self)
    self._wtRootWindow:BindCloseCallBack(fCallbackIns)

    self:RefreshUI()

    if IsHD() then
        self:_InitGamepadInputs()
    end
end

function ActivityRadioHotPopWindow:OnInitExtraData(activityID, activityInfo)
    self._activityID = activityID or 0
    self._activityInfo = activityInfo or {}

    self._sbcInfo = activityInfo.sbc_info
    if self._sbcInfo == nil or next(self._sbcInfo) == nil then return end

    -- 活动周期 ---@type string
    self._version = self._sbcInfo.version or ""

    -- 本期热品 ---@type PropInfo[]
    self._exchangePropList = self._sbcInfo.exchange_props or {}

    -- 本期军需处可兑换物资 ---@type uint64[]
    self._shopExchangeIDList = self._sbcInfo.shop_exchange_ids or {}
end

function ActivityRadioHotPopWindow:OnClose()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end

-----------------------------------------------------UI刷新-----------------------------------------------------
function ActivityRadioHotPopWindow:RefreshUI()
    self:_OnSetTimeCycleTxt()
    self:_OnSetDescTxt()
    self._wtScrollGridBox:RefreshAllItems()
    self._wtExchangeScrollBox:RefreshAllItems()
end

function ActivityRadioHotPopWindow:_OnSetTimeCycleTxt()
    self._wtTimeCycleTxt:SetText(self._version)
end

function ActivityRadioHotPopWindow:_OnSetDescTxt()
    local descTxt = self._sbcInfo.exchange_doc or ""
    self._wtDescTxt:SetText(descTxt)
end

function ActivityRadioHotPopWindow:_OnGetActivityRromptCount()
    return #self._exchangePropList
end

function ActivityRadioHotPopWindow:_OnProcessActivityRromptWidget(position, itemWidget)
    local index = position + 1
    local prop = self._exchangePropList[index] or {}
    local itemReward = ItemBase:New(tonumber(prop.id), prop.num)

    itemWidget:InitItem(itemReward)
    if itemWidget._wtItemIcon and type(itemWidget._wtItemIcon.Visible) == "function" then
        itemWidget._wtItemIcon:Visible()
    end
end

function ActivityRadioHotPopWindow:_OnGetExchangeRromptCount()
    return #self._shopExchangeIDList
end

function ActivityRadioHotPopWindow:_OnProcessExchangeRromptWidget(position, itemWidget)
    local index = position + 1
    local propID = self._shopExchangeIDList[index] or 0
    local itemReward = ItemBase:New(propID)

    itemWidget:InitItem(itemReward)
    if itemWidget._wtItemIcon and type(itemWidget._wtItemIcon.Visible) == "function" then
        itemWidget._wtItemIcon:Visible()
    end
end

function ActivityRadioHotPopWindow:_InitGamepadInputs()
    if not self._ScrollNavGroup then
        self._ScrollNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollGridBox, self, "Hittest")
        if self._ScrollNavGroup then
            self._ScrollNavGroup:AddNavWidgetToArray(self._wtScrollGridBox)
        end
    end

    if not self._ExchangeNavGroup then
        self._ExchangeNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtExchangeScrollBox, self, "Hittest")
        if self._ExchangeNavGroup then
            self._ExchangeNavGroup:AddNavWidgetToArray(self._wtExchangeScrollBox)
        end
    end

    if self._ScrollNavGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._ScrollNavGroup)
    end

    if not self._confirmBtnHandle then
        self._confirmBtnHandle = self:AddInputActionBinding("Common_ButtonLeft_Gamepad", EInputEvent.IE_Pressed, self._OnConfirmBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    end
end

function ActivityRadioHotPopWindow:_DisableGamepadInputs()
    if self._ScrollNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._ScrollNavGroup = nil
    end

    if self._ExchangeNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._ExchangeNavGroup = nil
    end
    
    if self._confirmBtnHandle then
        self:RemoveInputActionBinding(self._confirmBtnHandle)
        self._confirmBtnHandle = nil
    end
end

function ActivityRadioHotPopWindow:_OnConfirmBtnClicked()
    self:_OnClosePopUI()
end

function ActivityRadioHotPopWindow:_OnClosePopUI()
    Facade.UIManager:CloseUI(self)
end

function ActivityRadioHotPopWindow:OnNavBack()
    return false
end

return ActivityRadioHotPopWindow
