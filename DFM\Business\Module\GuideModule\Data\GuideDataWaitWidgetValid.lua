----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------


local log = function(...) loginfo("[GuideDataWaitWidgetValid]", ...) end
local warn = function(...) logwarning("[GuideDataWaitWidgetValid]", ...) end
local error = function(...) logerror("[GuideDataWaitWidgetValid]", ...) end

local GuideDataBase = require "DFM.Business.Module.GuideModule.Data.GuideDataBase"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideDataVisibility : GuideDataBase
local GuideDataVisibility = class('GuideDataVisibility', GuideDataBase)

---@class GuideDataWaitWidgetValid : GuideDataBase
local GuideDataWaitWidgetValid = class('GuideDataWaitWidgetValid', GuideDataBase)

function GuideDataWaitWidgetValid:Ctor()
    self._targetWidgetId = nil
    self._maxRetryTimes = 5
    self._retryInterval = 1 -- in sec
    self._processCount = 0
end

function GuideDataWaitWidgetValid:Destroy()
    if self._timerHandler then
        Timer.CancelDelay(self._timerHandler)
        self._timerHandler = nil
    end
end

function GuideDataWaitWidgetValid:OnStartGuide()
    self._processCount = 0
    self:_StartInternal()
end

function GuideDataWaitWidgetValid:OnPause()
    self._processCount = 0
    if self._timerHandler then
        Timer.CancelDelay(self._timerHandler)
        self._timerHandler = nil
    end
end

function GuideDataWaitWidgetValid:OnRestart()
    self._processCount = 0
    self:_StartInternal()
end

function GuideDataWaitWidgetValid:_StartInternal()
    self:_ReloadArgs()
    self:_CheckComplete()
end

function GuideDataWaitWidgetValid:_ReloadArgs()
    self._targetWidgetId = tonumber(self._guideCfg.OptArgs[1])
    assert(self._targetWidgetId ~= nil, "GuideDataWaitWidgetValid: Target widget ID is not set in guide config")

    local l = #self._guideCfg.OptArgs
    if l >= 2 then
        self._maxRetryTimes = tonumber(self._guideCfg.OptArgs[2]) or self._maxRetryTimes
    end
    if l >= 3 then
        self._retryInterval = tonumber(self._guideCfg.OptArgs[3]) or self._retryInterval
    end
    log("_ReloadArgs", self._targetWidgetId, self._maxRetryTimes, self._retryInterval)
end

function GuideDataWaitWidgetValid:_CheckComplete()
    if self:_IsTargetUIValid() then
        self:EndGuide(#self._guideCfg.NextGuideId)
        return
    end
    log("_CheckComplete", self._processCount)

    if self._timerHandler then
        Timer.CancelDelay(self._timerHandler)
        self._timerHandler = nil
    end

    if self._processCount >= self._maxRetryTimes then
        self:EndGuide(#self._guideCfg.NextGuideId - 1)
        return
    end
    self._timerHandler = Timer.DelayCall(self._retryInterval, self._StartInternal, self)
    self._processCount = self._processCount + 1
end

function GuideDataWaitWidgetValid:_IsTargetUIValid()
    local widgetConfig = GuideConfig.TableGuideWidgetConfig[self._targetWidgetId]
    local targetWidget, targetUi, targetWidgetList =
        GuideLogic.GetWidgetByPath(widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, widgetConfig.IsHudUI)

    log(" _IsTargetUIValid", widgetConfig.BpPath, widgetConfig.WidgetName, widgetConfig.UIName, "-->", widgetConfig.IsHudUI, targetWidget, targetUi,
        targetWidgetList)

    local target =
        targetWidget and targetWidget or
        targetWidgetList and targetWidgetList[1] or -- first valid widget
        nil

    if hasdestroy(target) then
        log("Target widget is nil or has been destroyed")
        return false
    end

    local visibility =
        target.GetVisibility and target:GetVisibility() or
        target.Visibility or
        ESlateVisibility.Collapsed
    log("visibility", visibility)

    return visibility ~= ESlateVisibility.Hidden and -- 2
        visibility ~= ESlateVisibility.Collapsed     -- 1
end

return GuideDataWaitWidgetValid
