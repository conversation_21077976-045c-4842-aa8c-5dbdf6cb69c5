----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideDataBase = require "DFM.Business.Module.GuideModule.Data.GuideDataBase"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"

---@class GuideDataHDWeakClick : GuideDataBase
local GuideDataHDWeakClick = class('GuideDataHDWeakClick', GuideDataBase)

function GuideDataHDWeakClick:Ctor()
    self._dialogHandle = nil
end

function GuideDataHDWeakClick:Destroy()
    local cfgId = tonumber(self._guideCfg.Args)
    if Module.Guide then
        Module.Guide:CloseGuideHDWeakClickUI(cfgId)
    end
end

function GuideDataHDWeakClick:OnStartGuide()
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:OpenGuideHDWeakClickUI(cfgId)
end

function GuideDataHDWeakClick:OnEndGuide(idx)
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:CloseGuideHDWeakClickUI(cfgId)
end

function GuideDataHDWeakClick:OnPause(uiIns)
end

function GuideDataHDWeakClick:OnRestart()
end

function GuideDataHDWeakClick:OnInputTypeChanged(inputType)
    -- 重新定位一下, 比如BottonBar 位置会刷新
    local cfgId = tonumber(self._guideCfg.Args)
    Module.Guide:OpenGuideHDWeakClickUI(cfgId)
end

return GuideDataHDWeakClick