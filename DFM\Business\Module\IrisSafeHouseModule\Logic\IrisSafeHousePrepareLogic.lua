----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"


local Config = Module.IrisSafeHouse.Config

local IrisSafeHousePrepareLogic = {}

local _allEvents = {}

local _bMainTabViewOpened = false

local _stateMap = {
    [Config.EMathingState.None] = {
        fStartClicked = function() end,
        fMapClicked = function() end,
        fAutoMatchClicked = function(self, bIsChecked) return true end,
        fGetStartButtonText = function() return "" end,
    },
    --备战
    [Config.EMathingState.Prepare] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIMatchBattleReady)
            --是队员，没有选图能力
            if Server.TeamServer:IsMember() then
                Module.ArmedForce:ShowMainPanel(Module.ArmedForce.Config.EEnterFrom.PrepareFlow)
            else
                local matchMode = Server.GameModeServer:GetMatchMode()
                Module.SandBoxMap:EnterSandBoxMap(Module.SandBoxMap.Config.EEnterFrom.PrepareFlow,
                    MatchGameRule.SOLGameRule, matchMode and matchMode.map_id)
                --Module.IrisSafeHouse.Config.evtPrepareBtnClicked:Invoke()
            end
        end,
        fMapClicked = function()
            local matchMode = Server.GameModeServer:GetMatchMode()
            if matchMode and matchMode.map_id and matchMode.map_id~=0 then
                Module.SandBoxMap:Jump(matchMode and matchMode.map_id)
            else
                -- Module.CommonTips:ShowSimpleTip(Config.Loc.NotPrepareText)
                Module.SandBoxMap:Jump()
            end
        end,
        fAutoMatchClicked = function(self, bIsChecked)
            if Server.TeamServer:IsCaptial() then
                self:_OnAutoMatchOptionChanged(bIsChecked)
            else
                Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.OnlyCaptaionCanOperate)
                -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
                return false
            end
            return true
        end,
        fGetStartButtonText = function()
            return Config.Loc.Prepare
        end,
    },
    --出发
    [Config.EMathingState.Go] = {
        fStartClicked = function()
            logwarning("captainStartBtnClick")
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIMatchEnterGame)
            --赛季刚重置的时候有一段保护时间
            local bInProtectTime,sec=Module.Ranking:IsInSeasonInitProtectTime()
            if bInProtectTime then
                Module.CommonTips:ShowSimpleTip(string.format(Module.Tournament.Config.Loc.SeasonInInitProtectTime,sec))
                return
            end
            
            local matchMode = Server.GameModeServer:GetMatchMode()
            --判断是否无状态
            if Server.GameModeServer:CheckUnknowMode(matchMode) then
                Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.NeedTarget)
                return
            end
            --判断是否非matching和等待入局不允许再次发起匹配
            if Server.MatchServer:GetIsMatching() or Server.MatchServer:GetIsWaitForGotoGame() then
                return
            end
            
            -- 单局推荐方案经分，每次入局时记录一条数据
            Module.ArmedForce:SendAnalysisData(Module.ArmedForce.Config.EAnalysisType.Modify)

            --队长和单人调的接口不一样
            if Server.TeamServer:IsCaptial() then

                local fCallback = SafeCallBack(function()
                    --直接发matching，有未准备会发ntf和错误码
                    Server.TeamServer:SendTeamMatching()
                end)
                --检测不可入局状态
                logwarning("captainStart")
                IrisSafeHousePrepareLogic.CheckNotAllowedState(fCallback)
            else
                --allardwang check map ready
                --[[if LiteDownloadManager:IsSupportLitePackage() then
                    local checkMapid = Server.GameModeServer:GetMapID()
                    local moduleName = LiteDownloadManager:GetModuleNameByMapId(checkMapid)
                    local bIsReady = LiteDownloadManager:IsDownloadedByModuleName(moduleName)
                    if bIsReady == false then
                        Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.MapNotReady)
                        return
                    end
                end]]

                local fCallback = SafeCallBack(function()
                    local fSendMatch = function ()
                        local autoMatch = Server.TeamServer:GetAutoMatchTeamMates()
                        if matchMode.game_rule==MatchGameRule.ArenaGameRule then
                            Server.TeamServer:SendPersonMatchingWithParams(Server.GameModeServer:GetMatchModes() or {matchMode}, autoMatch, 0, 0)
                        else
                            Server.TeamServer:SendPersonMatchingWithParams({matchMode}, autoMatch, 0, 0)
                        end
                    end
                    if Server.RoomServer:GetIsInRoom() then
                        Module.Room:ExitRoom(fSendMatch)
                    else
                        fSendMatch()
                    end
                end)
                --检测不可入局状态
                IrisSafeHousePrepareLogic.CheckNotAllowedState(fCallback)

            end
            Module.IrisSafeHouse.Config.evtGoBtnClicked:Invoke()
        end,
        fMapClicked = function()
            -- if DFHD_LUA == 1 then
            --     IrisSafeHousePrepareLogic.StartModifyMap()
            -- else
                local matchMode = Server.GameModeServer:GetMatchMode()
                Module.SandBoxMap:Jump(matchMode and matchMode.map_id)
            -- end
        end,
        fAutoMatchClicked = function(self, bIsChecked)
            local matchMode = Server.GameModeServer:GetMatchMode()
            local addMemberType = matchMode.add_member_type
            if addMemberType == MapBoardAddMemberType.MustAddMember then
                Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.ModeForceAddMember)
                -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
                return false
            elseif addMemberType == MapBoardAddMemberType.MustNotAddMember then
                Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.ModeForceNotAddMember)
                -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
                return false
            end
            self:_OnAutoMatchOptionChanged(bIsChecked)
            return true
        end,
        fGetStartButtonText = function()
            if not Server.TeamServer:IsInTeam() then
                return Config.Loc.SingleGo
            end
            local readyNum, totalNum = IrisSafeHousePrepareLogic.GetReadyAndTotalNum()
            if totalNum == 1 then
                return Config.Loc.SingleGo
            end
            return string.format(Config.Loc.CapitalGo, readyNum, totalNum)
        end,
    },
    --可准备
    [Config.EMathingState.Ready] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIMatchEnterGame)
            --判断是否无状态
            local matchMode = Server.GameModeServer:GetMatchMode()
            if Server.GameModeServer:CheckUnknowMode(matchMode) then
                Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.WaitForCaptialSelectTarget)
                return
            end

            --如果开启排位，检查是否满足赛季等级
            if Server.TeamServer:GetEnableSOLRankMatch() then
                local bIsLocked,needLevel=IrisSafeHousePrepareLogic.CheckRankLockedBySeasonLevel()
                if bIsLocked then
                    Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.CantMatchRankReadyTipText)
                    return
                end
            end


            local fCallback = SafeCallBack(function()
                Server.TeamServer:SendMatchReady(true)
                if Server.TeamServer:IsInTeam() and Server.TeamServer:GetTeamNum() > 1 then
                    local req = pb.CSTeamBroadcastTReq:New()
                    req.team_id = Server.TeamServer:GetTeamID()
                    req.broadcast_type = TeamBroadcastType.TBTEquitpReady
                    req:Request()
                end
            end)
            --检测不可入局状态
            IrisSafeHousePrepareLogic.CheckNotAllowedState(fCallback)

            Module.IrisSafeHouse.Config.evtReadyBtnClicked:Invoke()
        end,
        fMapClicked = function()
            local matchMode = Server.GameModeServer:GetMatchMode()
            Module.SandBoxMap:EnterSandBoxMap(nil, MatchGameRule.SOLGameRule, matchMode.map_id)
        end,
        fAutoMatchClicked = function(self, bIsChecked)
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.OnlyCaptaionCanOperate)
            -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
            return false
        end,
        fGetStartButtonText = function()
            local readyNum, totalNum = IrisSafeHousePrepareLogic.GetReadyAndTotalNum()
            return StringUtil.SequentialFormat('{0}[{1}/{2}]',Config.Loc.Ready,readyNum, totalNum)
        end,
    },
    --取消准备
    [Config.EMathingState.Cancelready] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)
            Server.TeamServer:SendMatchReady(false)
        end,
        fMapClicked = function()
            local matchMode = Server.GameModeServer:GetMatchMode()
            Module.SandBoxMap:EnterSandBoxMap(nil, MatchGameRule.SOLGameRule, matchMode.map_id)
        end,
        fAutoMatchClicked = function(self, bIsChecked)
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.OnlyCaptaionCanOperate)
            -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
            return false
        end,
        fGetStartButtonText = function()
            local readyNum, totalNum = IrisSafeHousePrepareLogic.GetReadyAndTotalNum()
            return StringUtil.SequentialFormat('{0}[{1}/{2}]',Config.Loc.CancelReady,readyNum, totalNum)
        end,
    },
    --队长匹配
    [Config.EMathingState.Matching] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)
            Server.TeamServer:SendCancelMatching()
        end,
        fMapClicked = function()
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.CantSetInMatching)
            -- Module.SandBoxMap:EnterSandBoxMap()
        end,
        fAutoMatchClicked = function(self, bIsChecked)
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.CantSetInMatching)
            -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
            return false
        end,
        fGetStartButtonText = function()
            return Config.Loc.CancelAction
        end,
    },
    --队员匹配
    [Config.EMathingState.TeammateMatching] = {
        fStartClicked = function()
            --TODO
            --不能点或提示
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.OnlyCaptaionCanOperate)
        end,
        fMapClicked = function()
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.CantSetInMatching)
            -- local matchMode = Server.GameModeServer:GetMatchMode()
            -- Module.SandBoxMap:EnterSandBoxMap(nil, MatchGameRule.SOLGameRule, matchMode.map_id)
        end,
        fAutoMatchClicked = function(self, bIsChecked)
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.CantSetInMatching)
            -- self._wtAutoMatchWidget:SetIsChecked(not bIsChecked, false)
            return false
        end,
        fGetStartButtonText = function()
            return Config.Loc.Matching
        end,
    },
}

function IrisSafeHousePrepareLogic.InitEvents()
    loginfo("[IrisSafeHousePrepareLogic]:InitEvents")
    _allEvents.hSeatInfoChanged =
    Module.Team.Config.evtSeatInfoChanged:AddListener(IrisSafeHousePrepareLogic.OnPrepareStateRefreshed)
    -- _allEvents.hCaptialChanged =
    -- Server.TeamServer.Events.evtCaptialChanged:AddListener(IrisSafeHousePrepareLogic._OnCaptialChanged)
    _allEvents.hAlreadyEnterMatch =
    Server.TeamServer.Events.evtAlreadyEnterMatch:AddListener(IrisSafeHousePrepareLogic.OnPrepareStateRefreshed)
    _allEvents.hTeammateStateChanged =
    Server.TeamServer.Events.evtTeammateStateChanged:AddListener(IrisSafeHousePrepareLogic._OnTeammateStateChanged)
    _allEvents.hTeammateJoined =
    Server.TeamServer.Events.evtTeammateJoined:AddListener(IrisSafeHousePrepareLogic.OnPrepareStateRefreshed)
    _allEvents.hStartMatching =
    Server.MatchServer.Events.evtStartMatching:AddListener(IrisSafeHousePrepareLogic._OnStartMatching)
    _allEvents.hPrepareJoinMatch =
    Server.MatchServer.Events.evtPrepareJoinMatch:AddListener(IrisSafeHousePrepareLogic._OnPrepareJoinMatch)
    _allEvents.hEndMatching =
    Server.MatchServer.Events.evtEndMatching:AddListener(IrisSafeHousePrepareLogic._OnEndMatching)
    _allEvents.hEndMatchReturnSOLMoney =
    Server.TeamServer.Events.evtEndMatchReturnSOLMoney:AddListener(IrisSafeHousePrepareLogic._OnEndMatchReturnSOLMoney)
    --队伍信息更新
    _allEvents.hTeamInfosUpdated =
    Server.TeamServer.Events.evtTeamInfosUpdated:AddListener(IrisSafeHousePrepareLogic.OnPrepareStateRefreshed)
    --装备变化通知
    _allEvents.hTeammateEquipChange =
    Server.TeamServer.Events.evtTeammateEquipChange:AddListener(IrisSafeHousePrepareLogic.OnPrepareStateRefreshed)
    _allEvents.hMatchGatePunish =
    Server.TeamServer.Events.evtMatchGatePunishFailed:AddListener(IrisSafeHousePrepareLogic._OnMatchGatePunishFailed)
    -- 队友离队
    _allEvents.hTeammateLef =
    Server.TeamServer.Events.evtTeammateLeft:AddListener(IrisSafeHousePrepareLogic.OnPrepareStateRefreshed)
    _allEvents.hNewSeasonComing =
    Server.RankingServer.Events.evtNewSeasonComing:AddListener(IrisSafeHousePrepareLogic._OnNewSeasonComing)

    IrisSafeHousePrepareLogic.OnPrepareStateRefreshed()
end

function IrisSafeHousePrepareLogic.RemoveEvents()
    loginfo("[IrisSafeHousePrepareLogic]:RemoveEvents")
    Module.Team.Config.evtSeatInfoChanged:RemoveListenerByHandle(_allEvents.hSeatInfoChanged)
    -- Server.TeamServer.Events.evtCaptialChanged:RemoveListenerByHandle(_allEvents.hCaptialChanged)
    Server.TeamServer.Events.evtAlreadyEnterMatch:RemoveListenerByHandle(_allEvents.hAlreadyEnterMatch)
    Server.TeamServer.Events.evtTeammateStateChanged:RemoveListenerByHandle(_allEvents.hTeammateStateChanged)
    Server.TeamServer.Events.evtTeammateJoined:RemoveListenerByHandle(_allEvents.hTeammateJoined)
    Server.MatchServer.Events.evtStartMatching:RemoveListenerByHandle(_allEvents.hStartMatching)
    Server.MatchServer.Events.evtPrepareJoinMatch:RemoveListenerByHandle(_allEvents.hPrepareJoinMatch)
    Server.MatchServer.Events.evtEndMatching:RemoveListenerByHandle(_allEvents.hEndMatching)
    Server.TeamServer.Events.evtEndMatchReturnSOLMoney:RemoveListenerByHandle(_allEvents.hEndMatchReturnSOLMoney)
    Server.TeamServer.Events.evtTeamInfosUpdated:RemoveListenerByHandle(_allEvents.hTeamInfosUpdated)
    Server.TeamServer.Events.evtTeammateEquipChange:RemoveListenerByHandle(_allEvents.hTeammateEquipChange)
    Server.TeamServer.Events.evtMatchGatePunishFailed:RemoveListenerByHandle(_allEvents.hMatchGatePunish)
    Server.TeamServer.Events.evtTeammateLeft:RemoveListenerByHandle(_allEvents.hTeammateLef)
    Server.RankingServer.Events.evtNewSeasonComing:RemoveListenerByHandle(_allEvents.hNewSeasonComing)

    _allEvents = {}
end

function IrisSafeHousePrepareLogic.SetPrepareState(newState)
    --logwarning("preparelogicsetpreparestate",newState,debug.traceback())
    loginfo("[HallPrepareRegion]logic:SetPrepareState:"..tostring(newState))
    local preState = IrisSafeHousePrepareLogic.GetPrepareState()
    Module.IrisSafeHouse.Field:SetPrepareState(newState)
    Module.IrisSafeHouse.Config.evtPrepareStateChanged:Invoke(newState,preState)
end

function IrisSafeHousePrepareLogic.GetPrepareState()
    return Module.IrisSafeHouse.Field:GetPrepareState()
end

function IrisSafeHousePrepareLogic.OnPrepareStateRefreshed()
    loginfo("[HallPrepareRegion]logic:_OnPrepareStateRefreshed")
    if Server.MatchServer:GetIsWaitForGotoGame() then
        loginfo("[IrisSafeHousePrepareLogic] GetIsWaitForGotoGame no need change startText")
        return
    end
    if Server.MatchServer:GetIsMatching() then

        if Server.TeamServer:IsMember() then
            --队友匹配中
            IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.TeammateMatching)
        else
            --队长匹配中
            IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Matching)
            logerror("[IrisSafeHousePrepareLogic]captial in matching")
        end
    elseif Server.GameModeServer:CheckNeedArmedForce() then
        if Module.ArmedForce:HasOutfitFlowFlag() then
            if Server.TeamServer:IsMember() then
                if Server.TeamServer:IsReady() then
                    local bNeedSku,skuItemId=IrisSafeHousePrepareLogic.CheckNeedSkuItem()
                    if bNeedSku then
                        local item=Server.InventoryServer:GetItemFromID(skuItemId)
                        if item then
                            --取消准备
                            IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Cancelready)
                        else
                            Server.TeamServer:SendMatchReady(false)
                        end
                    else
                        local bMoneyEnough=IrisSafeHousePrepareLogic.CheckMoneyIsEnough()
                        if bMoneyEnough then
                            IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Cancelready)
                        else
                            Server.TeamServer:SendMatchReady(false)
                        end
                    end
                else
                    --可准备
                    IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Ready)
                end
            else
                local matchId = Server.GameModeServer:GetMatchModeID()
                if matchId and matchId ~= 0 then
                    --出发
                    IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Go)
                else
                    --备战
                    IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Prepare)
                end
            end
        else
            --备战
            IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Prepare)
        end
    else
        if Server.TeamServer:IsMember() then
            if Server.TeamServer:IsReady() then
                local bNeedSku,skuItemId=IrisSafeHousePrepareLogic.CheckNeedSkuItem()
                if bNeedSku then
                    local item=Server.InventoryServer:GetItemFromID(skuItemId)
                    if item then
                        --取消准备
                        IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Cancelready)
                    else
                        Server.TeamServer:SendMatchReady(false)
                    end
                else
                    local bMoneyEnough=IrisSafeHousePrepareLogic.CheckMoneyIsEnough()
                    if bMoneyEnough then
                        IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Cancelready)
                    else
                        Server.TeamServer:SendMatchReady(false)
                    end
                end
            else
                --可准备
                IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Ready)
            end
        else
            local matchId = Server.GameModeServer:GetMatchModeID()
            if matchId and matchId ~= 0 then
                --出发
                IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Go)
            else
                --备战
                IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Prepare)
            end
        end
    end
end

function IrisSafeHousePrepareLogic._OnStartMatching()
    loginfo("[IrisSafeHousePrepareLogic] _OnStartMatching")
    IrisSafeHousePrepareLogic.OnPrepareStateRefreshed()
    local matchMode = Server.GameModeServer:GetMatchMode()
    if matchMode.game_rule==MatchGameRule.SOLGameRule then
        local matchModeId = matchMode.match_mode_id
        -- local mapName=Module.GameMode:GetStandardMapNameByMatchModeId(matchModeId,false)
        local needMoney=Server.GameModeServer:GetCostMoneyByMatchModeId(matchModeId)
        if needMoney>0 then
            Module.CommonTips:ShowSimpleTip(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.SOLCostMoneyText,StringUtil.NumberWithThousandBit(needMoney)))
        end
        local skuItemId=Server.GameModeServer:GetSkuItemIdByMatchModeId(matchModeId)
        if skuItemId~=0 then
            local tipText=StringUtil.SequentialFormat(Module.SandBoxMap.Config.Loc.ItemNumText,ItemConfigTool.GetItemName(skuItemId),1)
            Module.CommonTips:ShowSimpleTip(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.SOLCostSkuText,tipText))
        end
    end
end

function IrisSafeHousePrepareLogic._OnPrepareJoinMatch()
    loginfo("IrisSafeHousePrepareLogic._OnPrepareJoinMatch")
    if Server.TeamServer:IsMember() then
        IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Ready)
    else
        IrisSafeHousePrepareLogic.SetPrepareState(Config.EMathingState.Go)
    end
end

function IrisSafeHousePrepareLogic._OnEndMatching()
    loginfo("[IrisSafeHousePrepareLogic] _OnEndMatching")
    -- if Server.AccountServer:IsInPickHeroStage() then
    --     loginfo("[IrisSafeHousePrepareLogic] _OnEndMatching: IsInPickHeroStage")
    --     return
    -- end
    -- if Server.AccountServer:IsPlayerInGame() then
    --     loginfo("[IrisSafeHousePrepareLogic] _OnEndMatching: IsPlayerInGame")
    --     return
    -- end
    -- if Server.MatchServer:GetIsWaitForGotoGame() then
    --     loginfo("[IrisSafeHousePrepareLogic] _OnEndMatching: GetIsWaitForGotoGame")
    --     return
    -- end
    IrisSafeHousePrepareLogic.OnPrepareStateRefreshed()
    -- local matchMode = Server.GameModeServer:GetMatchMode()
    -- if matchMode.game_rule==MatchGameRule.SOLGameRule then
    --     local matchModeId = matchMode.match_mode_id
    --     local needMoney=Server.GameModeServer:GetCostMoneyByMatchModeId(matchModeId)
    --     if needMoney>0 then
    --         Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.SOLReturnMoneyText)
    --     end
    -- end
    if Server.MatchServer:GetIsIdle() then
        Module.LitePackage:CheckWIFIDownloadQuests(1)
    end
end

function IrisSafeHousePrepareLogic._OnEndMatchReturnSOLMoney(bReturnToMail)
    bReturnToMail=setdefault(bReturnToMail,true)
    loginfo("[IrisSafeHousePrepareLogic] _OnEndMatchReturnSOLMoney",bReturnToMail)
    local matchMode = Server.GameModeServer:GetMatchMode()
    if matchMode.game_rule==MatchGameRule.SOLGameRule then
        local matchModeId = matchMode.match_mode_id
        local needMoney=Server.GameModeServer:GetCostMoneyByMatchModeId(matchModeId)
        if needMoney>0 then
            Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.SOLReturnMoneyText)
        end
        local skuItemId=Server.GameModeServer:GetSkuItemIdByMatchModeId(matchModeId)
        if skuItemId~=0 then
            local tipText=StringUtil.SequentialFormat(Module.SandBoxMap.Config.Loc.ItemNumText,ItemConfigTool.GetItemName(skuItemId),1)
            if bReturnToMail then
                Module.CommonTips:ShowSimpleTip(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.SOLReturnSkuToMailText,tipText))
            else
                Module.CommonTips:ShowSimpleTip(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.SOLReturnSkuToInventoryText,tipText))
            end
        end
    end
end

function IrisSafeHousePrepareLogic._OnTeammateStateChanged(playerId)
    IrisSafeHousePrepareLogic.OnPrepareStateRefreshed()
end

function IrisSafeHousePrepareLogic._OnCaptialChanged()
    IrisSafeHousePrepareLogic.OnPrepareStateRefreshed()
end

function IrisSafeHousePrepareLogic.GetStateMap()
    return _stateMap
end

function IrisSafeHousePrepareLogic.StartModifyMap()
    Module.CommonBar:ChangeStackUITab(
        UIName2ID.SandBoxMapView,
        function()
            Module.SandBoxMap:EnterSandBoxMap()
        end
    )
end

--不可入局道具,限制道具数量
function IrisSafeHousePrepareLogic.CheckCantTakenProps()
    loginfo("IrisSafeHousePrepareLogic.CheckCantTakenProps")
    local checkContainerList={
        ESlotType.ChestHangingContainer,--胸挂
        ESlotType.BagContainer,--背包
        ESlotType.SafeBoxContainer,--保险箱
        ESlotType.Pocket,--口袋
    }

    local banItemTypeList,banItemIdList,banItemCountLimtList=Server.GameModeServer:GetModeBanItemData()
    local cantTakenPropList = {}
    local cantTakenMorePropList = {}
    local takenBanLimitCountList={}
    for k,v in pairs(checkContainerList) do
        local slot = Server.InventoryServer:GetSlot(v, ESlotGroup.Player)
        local slotItems=slot:GetItems()
        for i, j in pairs(slotItems or {}) do
            -- if ItemHelperTool.IsCantIntoDsItemById(j.id) then
            --     table.insert(cantTakenPropList, j)
            -- end
            local idstr = string.sub(j.id, 1, 4)
            if table.contains(banItemTypeList or {},idstr) then
                table.insert(cantTakenPropList, j)
            end
            if table.contains(banItemIdList or {},j.id) then
                table.insert(cantTakenPropList, j)
            end
            for _,limitData in pairs(banItemCountLimtList or {}) do
                if limitData.item_id==j.id then
                    if takenBanLimitCountList[j.id] then
                        takenBanLimitCountList[j.id]=takenBanLimitCountList[j.id]+j.num
                    else
                        takenBanLimitCountList[j.id]=j.num
                    end
                    if takenBanLimitCountList[j.id]>=limitData.limit_count then
                        local bNeedAdd=true
                        for _,banItem in pairs(cantTakenMorePropList) do
                            if banItem.id==j.id then
                                bNeedAdd=false
                                banItem.banItemNum=takenBanLimitCountList[j.id]
                                break
                            end
                        end
                        if bNeedAdd then
                            j.banItemLimitCount=limitData.limit_count-1
                            j.banItemNum=takenBanLimitCountList[j.id]
                            table.insert(cantTakenMorePropList, j)
                        end
                    end
                end
            end
        end
    end
    
    return cantTakenPropList,cantTakenMorePropList
end

function IrisSafeHousePrepareLogic._MatchGatePunishFailed(ntf)
    local mode = Server.ArmedForceServer:GetCurArmedForceMode()
    if mode == EArmedForceMode.MP then
        return
    end
    if ntf then
        --队伍中有人被禁赛
        local playerName = ntf.punish_nick_name
        local punishInfo = ntf.punish_info
        loginfo("IrisSafeHousePrepareLogic:OnMatchGatePunishFailed", playerName)
        if playerName and punishInfo then
            local punishMsg = Server.AccountServer:SerializePunishNtfMessage(punishInfo)
            -- 直接解析封禁原因，如果解析失败就走旧有逻辑
            if punishMsg then
                Module.CommonTips:ShowSimpleTip(punishMsg)
                return
            end
            
            local puishReasonStr = ""
            if punishInfo.reason then
                local accountPunishData = Facade.TableManager:GetTable("AccountPunishReason")
                local findedMsgData = table.find(accountPunishData,
                    function(v, k) return k == tostring(punishInfo.reason) end)
                if findedMsgData then
                    puishReasonStr = findedMsgData.Message
                else
                    puishReasonStr = punishInfo.reason
                end
            end

            local limitTime = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(punishInfo.over_time)
            local tips = string.format(Module.Preparation.Config.Tips.MatchGatePunishFailed, playerName, puishReasonStr,
                limitTime)
            Module.CommonTips:ShowSimpleTip(tips)
        end
    end
end

function IrisSafeHousePrepareLogic.CheckEquipValueLacked()
    loginfo("IrisSafeHousePrepareLogic.CheckEquipValueLacked")
    local equipValue=Module.ArmedForce:GetAllEquipmentValue()
    local needValue=Server.GameModeServer:GetMapNeedValue()
    return equipValue<needValue,equipValue,needValue
end

function IrisSafeHousePrepareLogic.CheckMoneyIsEnough()
    loginfo("IrisSafeHousePrepareLogic.CheckMoneyIsEnough")
    local matchMode = Server.GameModeServer:GetMatchMode()
    if matchMode.game_rule~=MatchGameRule.SOLGameRule then
        return true
    end
    local matchModeId = matchMode.match_mode_id
    local mapName=Module.GameMode:GetStandardMapNameByMatchModeId(matchModeId,false)
    local curMoney=Module.Currency:GetNum(ECurrencyClientId.Tina)
    local needMoney=Server.GameModeServer:GetCostMoneyByMatchModeId(matchModeId)
    if needMoney>0 then
        return needMoney<=curMoney,curMoney,needMoney,mapName
    else
        return true,curMoney,needMoney,mapName
    end
end

function IrisSafeHousePrepareLogic.CheckNeedSkuItem()
    loginfo("IrisSafeHousePrepareLogic.CheckNeedSkuItem")
    local matchMode = Server.GameModeServer:GetMatchMode()
    if matchMode.game_rule~=MatchGameRule.SOLGameRule then
        return false
    end
    local matchModeId = matchMode.match_mode_id
    local skuItemId=Server.GameModeServer:GetSkuItemIdByMatchModeId(matchModeId)
    if skuItemId~=0 then
        return true,skuItemId,matchMode.map_id or 0
    else
        return false
    end
end

function IrisSafeHousePrepareLogic.CheckSeasonLevelLacked() --检查赛季等级够不够
    local matchMode=Server.GameModeServer:GetMatchMode()
    local matchModeId = Server.GameModeServer:GetMatchModeID()
    local needLevel=0
    if matchMode.game_rule==MatchGameRule.ArenaGameRule then
        needLevel = Server.GameModeServer:GetArenaUnlockLevel(matchMode.game_rule)
    else
        needLevel = Server.GameModeServer:GetNeedLevelByMatchModeId(matchModeId) or 0
    end
    local seasonLevel = Server.RoleInfoServer.seasonLevel
    logwarning("prepareregionsafehouse checkseasonlevel", "needLevel", needLevel, "seasonLevel", seasonLevel)
    return seasonLevel < needLevel
end

function IrisSafeHousePrepareLogic.CheckLitePackLacked() --检查大小包有没有下载
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
        local matchModes=Server.GameModeServer:GetMatchModes()
        local mapId=0
        local bIsDownload=false
        for key, value in pairs(matchModes) do
            mapId = value.map_id
            local moduleName = Module.ExpansionPackCoordinator:GetModuleNameByMapId(mapId)
            local isDownload = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleName)
            logwarning("IrisSafeHousePrepareLogic checkismapdownload", "mapid", mapId, "moduleName", moduleName, "isDownload",isDownload)
            if isDownload then
                bIsDownload=true
            end
        end
        return not bIsDownload, mapId
    -- BEGIN Virtuos Modification @ PengBofeng
    elseif IsPS5() then
        local matchModes=Server.GameModeServer:GetMatchModes()
        local mapId=0
        local bIsDownload=false
        for key, value in pairs(matchModes) do
            mapId = value.map_id
            local moduleName = Module.ExpansionPackCoordinator:GetModuleNameByMapId(mapId)
            local isDownload = Module.ExpansionPackCoordinator:IsDownloadedByMapID(mapId)
            logwarning("IrisSafeHousePrepareLogic checkismapdownload", "mapid", mapId, "moduleName", moduleName, "isDownload",isDownload)
            if isDownload then
                bIsDownload=true
            end
        end
        return not bIsDownload, mapId
    -- END Modification
    else
        return false
    end
end

--如果开启排位，检查是否满足赛季等级
function IrisSafeHousePrepareLogic.CheckRankLockedBySeasonLevel()
    local data=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleRankSOL)
    if data then
        return not data.bIsUnlocked,data.unlock1Condition,data.unlocktips
    end
    return false,0,''
end

function IrisSafeHousePrepareLogic.GetNotAllowNightEquip()
    local equipList,weaponList=Server.GameModeServer:GetNotAllowNightEquip()
    local bTakeNight,equipTakeList=Module.ArmedForce:CheckNightVisionLimitByList(equipList)
    local bTakeImaging,weaponTakeList=Module.ArmedForce:CheckThermalImagingLimitByList(weaponList)
    return equipTakeList,weaponTakeList
end

function IrisSafeHousePrepareLogic.GetModeEquipTicket()
    local commonList,specialList=Server.GameModeServer:GetModeEquipTicket()
    return commonList,specialList
end

function IrisSafeHousePrepareLogic.CheckNotAllowedState(_fCallback) --检查不可入局状态
    if not Module.IrisSafeHouse:GetEnableMatchClientCheck() then
        if _fCallback then
            _fCallback()
        end
        return
    end
    --检查赛季等级
    local isSeasonLevelLacked = IrisSafeHousePrepareLogic.CheckSeasonLevelLacked()
    if isSeasonLevelLacked then
        local goText=Module.IrisSafeHouse.Config.Loc.SingleGo
        local readyText=Module.IrisSafeHouse.Config.Loc.Ready
        local levelNotEnoughText=Module.IrisSafeHouse.Config.Loc.LevelNotEnough
        local tips=""
        if Server.TeamServer:IsMember()then
            tips=string.format(levelNotEnoughText,readyText)
        else
            tips=string.format(levelNotEnoughText,goText)
        end
        Module.CommonTips:ShowSimpleTip(tips)
        return
    end

    --检测有没有下载地图
    local isMapNotDownload, mapId = IrisSafeHousePrepareLogic.CheckLitePackLacked()
    if isMapNotDownload then
        -- BEGIN Virtuos Modification @ Pengbofeng
        if IsPS5() then --For PlayGO
            Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.MapNotReady)
            return
        end
        -- END Modification
        Module.Social:ShowNotDownloadTips(mapId)
        return
    end

    --检查装备券是否满足
    local commonList,specialList=Server.GameModeServer:GetModeEquipTicket()
    if not table.isempty(commonList) or not table.isempty(specialList) then
        local equipId=Server.ArmedForceServer:GetCurRentalPlan_ConsumableID()
        if equipId~=0 and not table.contains(commonList,equipId) and not table.contains(specialList,equipId) then
            Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,2,equipId)
            return
        end
    end

    --检测装备价值
    local equipValueLacked,currentValue,mapNeedValue = IrisSafeHousePrepareLogic.CheckEquipValueLacked()
    if equipValueLacked then
        -- Facade.UIManager:AsyncShowUI(UIName2ID.ReminderDetails, nil, nil, 1, mapNeedValue, currentValue)
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,4,mapNeedValue,currentValue)
        return
    end

    --检测装备等级，头盔，护甲，子弹
    local equipLevels = Server.GameModeServer:GetEquipLevelByMatchModeId()
    local equipedLevels=Module.ArmedForce:CheckPlayerBodyItemsEntryQuality(true)
    local level1=equipLevels[1] or 0
    local level2=equipLevels[2] or 0
    local level3=equipLevels[3] or 0
    local bHelmet=level1~=0 and level1<equipedLevels[ESlotType.Helmet] or false
    local bBreast=level2~=0 and level2<equipedLevels[ESlotType.BreastPlate] or false
    local bBullet=level3~=0 and level3<equipedLevels[ESlotType.BulletLeft] or false
    if bHelmet or bBreast or bBullet then
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,3,bHelmet,bBreast,bBullet)
        return
    end

    --检查是否携带禁止夜战装备
    local nightVisonList,thermalImagingList = IrisSafeHousePrepareLogic.GetNotAllowNightEquip()
    if not table.isempty(nightVisonList) or not table.isempty(thermalImagingList) then
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,1,nightVisonList,thermalImagingList)
        return
    end

    --检测不可入局道具
    local cantTakenPropList,cantTakenMorePropList = IrisSafeHousePrepareLogic.CheckCantTakenProps()
    if #cantTakenPropList > 0 then
        -- Facade.UIManager:AsyncShowUI(UIName2ID.ReminderDetails, nil, nil, 0, cantTakenPropList)
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,6,cantTakenPropList,true)
        return
    end

    --检测携带道具不可超过指定数量
    if #cantTakenMorePropList > 0 then
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,6,cantTakenMorePropList,false)
        return
    end

    --检测携带道具总价值
    local bValueMoreLimit,limitValue=Server.InventoryServer:FindValuableItem()
    if not bValueMoreLimit then
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapNightMatchCheckWindow,nil,nil,5,StringUtil.NumberWithThousandBit(limitValue))
        -- Module.CommonTips:ShowConfirmWindowWithSingleBtn(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.CantTakenHeavyValueProps,StringUtil.NumberWithThousandBit(limitValue)))
        return
    end
    
    --检查sol入局哈夫币是否足够
    local bMoneyIsEnough,curMoney,needMoney,mapName=IrisSafeHousePrepareLogic.CheckMoneyIsEnough()
    if not bMoneyIsEnough then
        local mainTitle=StringUtil.SequentialFormat(Module.SandBoxMap.Config.Loc.SOLMoneyNotEnoughText,mapName,StringUtil.NumberWithThousandBit(needMoney))
        local secondTitle=StringUtil.SequentialFormat(Module.SandBoxMap.Config.Loc.SOLHasMoneyText,StringUtil.NumberWithThousandBit(curMoney))
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapSOLMoneyLimitWindow, nil, nil, mainTitle, secondTitle,true)
        return
    end

    --检查sol入局sku门票是否足够
    local bNeedSku,skuItemId,mapId=IrisSafeHousePrepareLogic.CheckNeedSkuItem()
    if bNeedSku then
        local item=Server.InventoryServer:GetItemFromID(skuItemId)
        if not item then
            local exchangeCompleteCheapItemList={}
            local cheapBuyItem = {
                id = skuItemId,
                num = 1,
                targetSlotType = ESlotType.None
            }
            table.insert(exchangeCompleteCheapItemList, cheapBuyItem)
            local popWindowHandler=nil
            local fBuyCallback=function (bSuccess)
                if bSuccess then
                    if popWindowHandler then
                        Facade.UIManager:CloseUIByHandle(popWindowHandler)
                        popWindowHandler=nil
                    end
                    IrisSafeHousePrepareLogic.CheckNotAllowedState(_fCallback)
                elseif Module.SandBoxMap:GetSkuAutoPurchaseState(mapId) then
                    Timer.DelayCall(1,function()
                        local itemName=StringUtil.SequentialFormat(Module.SandBoxMap.Config.Loc.ItemNumText,ItemConfigTool.GetItemName(skuItemId),1)
                        Module.CommonTips:ShowSimpleTip(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.SOLAutoPurchaseSkuFailText,itemName)) 
                    end)
                end
            end
            if not Module.SandBoxMap:GetSkuAutoPurchaseState(mapId) then
                --弹购买弹窗
                local fOnConfirmBtnClick = function(confirmFluctuationList)
                    Module.ComparePrice:DoCheapBuyBatch(confirmFluctuationList, CSCheapBuyScene.CheapBuyScene_Match, fBuyCallback)
                end
                popWindowHandler=Facade.UIManager:AsyncShowUI(UIName2ID.QuickFillPopWindow, nil, nil, exchangeCompleteCheapItemList, fOnConfirmBtnClick,1)
                LogAnalysisTool.DoSendAutoPurchaseSkuLog(2,false,skuItemId,mapId)
            else
                --自动从拍卖行购买
                local fFetchPrice=function()
                    Module.ComparePrice:DoCheapBuyBatch(exchangeCompleteCheapItemList, CSCheapBuyScene.CheapBuyScene_Match, fBuyCallback)
                end
                Server.AuctionServer:FetchSaleList(skuItemId,nil,fFetchPrice)
            end
            return
        end
    end

    --检查队友是否未准备
    if Server.TeamServer:GetTeamNum()>1 and Server.TeamServer:IsCaptial() then
        local readyNum, totalNum = IrisSafeHousePrepareLogic.GetReadyAndTotalNum()
        loginfo("readyNum",readyNum,"totalNum",totalNum)
        if readyNum~=totalNum then
            local otherMembers=Server.TeamServer:GetOtherMembers()
            local notReadyMembers={}
            for k,v in pairs(otherMembers)do
                if not Server.TeamServer:IsReady(v.PlayerID) or Server.TeamServer:IsInMatch(v.PlayerID) then
                    table.insert(notReadyMembers,v)
                end
            end
            local tips=""
            local length=#notReadyMembers
            for k,v in pairs(notReadyMembers)do
                tips=tips..v.PlayerName
                if k~=length then
                    tips=tips..","
                end
            end
            Module.CommonTips:ShowSimpleTip(string.format(Server.TeamServer.Loc.TeammateCantStart,tips))
            return
        end
    end

    -- --如果开启排位，检查是否满足赛季等级
    -- if Server.TeamServer:GetEnableRankMatch() then
    --     local bIsLocked,needLevel=IrisSafeHousePrepareLogic.CheckRankLockedBySeasonLevel()
    --     if bIsLocked then
    --         Module.CommonTips:ShowSimpleTip(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.RankLevelTipText,needLevel))
    --         return
    --     end
    -- end

    --检查raid是否使用配装券
    if IrisSafeHousePrepareLogic.CheckRaidIsUseRental(_fCallback) then
        return
    end

    --raid检测携带子弹数量足够,必须放最后
    if not IrisSafeHousePrepareLogic.CheckRaidBulletIsEnough(_fCallback) then
        return
    end

    if _fCallback then
        _fCallback()
    end

end

--raid检查是否使用配装券
function IrisSafeHousePrepareLogic.CheckRaidIsUseRental(_fCallback)
    local matchMode=Server.GameModeServer:GetMatchMode()
    if matchMode and (matchMode.game_rule==MatchGameRule.RaidGameRule or matchMode.game_rule==MatchGameRule.PVERaidGameRule) then
        local bIsRental=Server.ArmedForceServer:CheckIsRentalStatus()
        if bIsRental then
            local fConfirm=function()
                IrisSafeHousePrepareLogic.CheckRaidBulletIsEnough(_fCallback)
            end
            Module.CommonTips:ShowConfirmWindow(Module.IrisSafeHouse.Config.Loc.UsingRentalTipText,fConfirm)
            return true
        end
    end
    return false
end

--raid检测携带子弹数量足够
function IrisSafeHousePrepareLogic.CheckRaidBulletIsEnough(_fCallback)
    local matchMode=Server.GameModeServer:GetMatchMode()
    if matchMode and (matchMode.game_rule==MatchGameRule.RaidGameRule or matchMode.game_rule==MatchGameRule.PVERaidGameRule) then
        local bEnough,data=Module.ArmedForce:CheckRaidBulletEnough(matchMode.match_mode_id)
        if not bEnough then
            if table.isempty(data) then
                local fConfirm=function()
                     Module.ArmedForce:ShowMainPanel()
                end
                Module.CommonTips:ShowConfirmWindowWithSingleBtn(Module.IrisSafeHouse.Config.Loc.TakeNoWeaponText,fConfirm,Module.IrisSafeHouse.Config.Loc.GotoArmedForce)
            else
                local num=0
                if data[ESlotType.MainWeaponLeft] then
                    num=data[ESlotType.MainWeaponLeft].checkBulletNum
                elseif data[ESlotType.MainWeaponRight] then
                    num=data[ESlotType.MainWeaponRight].checkBulletNum
                elseif data[ESlotType.Pistrol] then
                    num=data[ESlotType.Pistrol].checkBulletNum
                end
                local fConfirm=function()
                     if _fCallback then
                         _fCallback()
                     end 
                end
                Module.CommonTips:ShowConfirmWindow(StringUtil.SequentialFormat(Module.IrisSafeHouse.Config.Loc.BulletNotEnoughText,num),fConfirm)
            end
            return false
        end
    end
    return true
end

function IrisSafeHousePrepareLogic.GetReadyAndTotalNum()
    local members = Server.TeamServer:GetMembers()
    local readyNum = 0
    local totalNum = Server.TeamServer:GetTeamNum()
    for _, v in pairs(members) do
        if Server.TeamServer:IsCaptial(v.PlayerID) or
            (v.State & TeamMemberState.MemReady ~= 0 and v.State & TeamMemberState.MemInMatch == 0) then --不在比赛中
            readyNum = readyNum + 1
        end
    end
    return readyNum, totalNum
end

function IrisSafeHousePrepareLogic.OnMapBtnClicked()
    if DFHD_LUA == 1 then
        LogAnalysisTool.SignButtonClicked(10030025)
    else
        LogAnalysisTool.SignButtonClicked(20030009)
    end
    local prepareState = IrisSafeHousePrepareLogic.GetPrepareState()
    logwarning("[IrisSafehousePrepare] View:_OnMapBtnClicked", prepareState)
    local stateInfo = _stateMap[prepareState]
    if stateInfo ~= nil then
        stateInfo.fMapClicked()
    end
end

function IrisSafeHousePrepareLogic.OnStartBtnClicked()
    local prepareState = IrisSafeHousePrepareLogic.GetPrepareState()
    logwarning("[IrisSafehousePrepare] View:_OnStartBtnClicked", prepareState)
    local stateInfo = _stateMap[prepareState]
    if stateInfo ~= nil then
        stateInfo.fStartClicked()
    end
end

function IrisSafeHousePrepareLogic._OnNewSeasonComing()
    loginfo("IrisSafeHousePrepareLogic._OnNewSeasonComing")
    local prepareState=IrisSafeHousePrepareLogic.GetPrepareState()
    if prepareState==Config.EMathingState.Matching then
        Server.TeamServer:SendCancelMatching()
    elseif prepareState==Config.EMathingState.Cancelready then
        Server.TeamServer:SendMatchReady(false)
    end
end

function IrisSafeHousePrepareLogic._OnMatchGatePunishFailed(ntf)
    if ntf then
        --队伍中有人被禁赛
        local playerName = ntf.punish_nick_name
        local punishInfo = ntf.punish_info
        loginfo("PreparationView:OnMatchGatePunishFailed", playerName)
        if playerName and punishInfo then
            local punishMsg = Server.AccountServer:SerializePunishNtfMessage(punishInfo)
            -- 直接解析封禁原因，如果解析失败就走旧有逻辑
            if punishMsg then
                Module.CommonTips:ShowSimpleTip(punishMsg)
                return
            end

            local puishReasonStr = ""
            if punishInfo.reason then
                local accountPunishData = Facade.TableManager:GetTable("AccountPunishReason")
                local findedMsgData = table.find(accountPunishData,
                    function(v, k) return k == tostring(punishInfo.reason) end)
                if findedMsgData then
                    puishReasonStr = findedMsgData.Message
                else
                    puishReasonStr = punishInfo.reason
                end
            end

            local limitTime = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(punishInfo.over_time)
            local tips = string.format(Module.Preparation.Config.Tips.MatchGatePunishFailed, playerName, puishReasonStr,
                limitTime)
            Module.CommonTips:ShowSimpleTip(tips)
        end
    end
end

function IrisSafeHousePrepareLogic.GetConfig()
    return Config
end

--如果开启排位，检查是否满足排位分限制
function IrisSafeHousePrepareLogic.CheckRankLockedByRankScore()
    local configData=Facade.TableManager:GetTable("RankScoreLimitConfig")--Module.IrisSafeHouse.Field:GetSOLRankScoreLimitConfig()
    if configData then
        local id=Server.RankingServer:GetMinorLevel()
        logwarning(string.format("[IrisSafeHousePrepareLogic] SOL rankscorelimitconfig current id=%d ",id))
        if id==0 then
            id=1
        end
        local data=configData[id]
        if data then
            local matchMode=Server.GameModeServer:GetMatchMode()
            local mapId=matchMode.map_id
            logwarning(string.format("[IrisSafeHousePrepareLogic] SOL rankscorelimitconfig current mapId=%d ",mapId))
            if data.ScoreLimit1MapID==mapId then
                return data.ScoreLimit1UpperLimit==0
            elseif data.ScoreLimit2MapID==mapId then
                return data.ScoreLimit2UpperLimit==0
            elseif data.ScoreLimit3MapID==mapId then
                return data.ScoreLimit3UpperLimit==0
            elseif data.ScoreLimit4MapID==mapId then
                return data.ScoreLimit4UpperLimit==0
            elseif data.ScoreLimit5MapID==mapId then
                return data.ScoreLimit5UpperLimit==0
            elseif data.ScoreLimit6MapID==mapId then
                return data.ScoreLimit6UpperLimit==0
            elseif data.ScoreLimit7MapID==mapId then
                return data.ScoreLimit7UpperLimit==0
            elseif data.ScoreLimit8MapID==mapId then
                return data.ScoreLimit8UpperLimit==0
            elseif data.ScoreLimit9MapID==mapId then
                return data.ScoreLimit9UpperLimit==0
            elseif data.ScoreLimit10MapID==mapId then
                return data.ScoreLimit10UpperLimit==0
            elseif data.ScoreLimit11MapID==mapId then
                return data.ScoreLimit11UpperLimit==0
            elseif data.ScoreLimit12MapID==mapId then
                return data.ScoreLimit12UpperLimit==0
            else
                logwarning(string.format("[IrisSafeHousePrepareLogic] SOL rankscorelimitconfig  mapId=%d not exist!",mapId))
                return data.DefaultUpperLimit==0
            end
        else
            logwarning(string.format("[IrisSafeHousePrepareLogic] SOL rankscorelimitconfig id=%d failed!",id))
        end
    else
        logwarning("[IrisSafeHousePrepareLogic] SOL get rankscorelimitconfig failed!")
    end
    return false
end

return IrisSafeHousePrepareLogic
