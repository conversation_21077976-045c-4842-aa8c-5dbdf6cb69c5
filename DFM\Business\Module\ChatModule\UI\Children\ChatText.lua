----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



---聊天文本
---@class ChatText : LuaUIBaseView
local ChatText = ui("ChatText")
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"
local ChatField = Module.Chat.Field
local EChannel2Source = Module.Chat.Config.EChannel2Source

function ChatText:Ctor()
    self._wtTextName = self:Wnd("TextBlock_84",UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtTextContent = self:Wnd("RichTextBlock_0",UITextBlock)
    self._wtTextTime = self:Wnd("TextBlock_66",UITextBlock)
    --
    self._canvasTip = self:Wnd("CanvasPanel_Tip",UIWidgetBase)
    self._canvasContent = self:Wnd("CanvasPanel_Content",UIWidgetBase)
    self._wtSlotAppoint = self:Wnd("DFNamedSlot_166", UIWidgetBase)
    self._wtTxtTip = self:Wnd("TextBlock_120",UITextBlock)

    --语音
    self._wtVoice = self:Wnd("WBP_Chat_VoiceBtn",UIWidgetBase)

    --+1按键
    self._wtBtnRepeat = self:Wnd("wButton+1", UITextBlock)
    self._wtBtnRepeat:Event("OnClicked",self._OnRepeatBtnClick,self)

    --文本按键
    self._wtBtnText = self:Wnd("Button_6", UITextBlock)
    self._wtBtnText:Event("OnClicked",self._OnTextBtnClick,self)

    --头像按键
    self._wtHeadBtn = self:Wnd("wtHeadBtn", UIButton)
    self._wtHeadBtn:Event("OnPressed",self._OnHeadBtnPressed,self)
    self._wtHeadBtn:Event("OnActiveReleased",self._OnHeadBtnReleased,self)
    self._wtHeadBtn:Event("OnClicked",self._OnHeadBtnClick,self)

    --好友预约
    self._wtAppoint = self:Wnd("WBP_Chat_Booking", UIWidgetBase)

    -- 排位积分图标
    self._wtCommonFlatRank = self:Wnd("WBP_Common_SmallFlatRank_C_0",UIWidgetBase)
    self._WtDFCanvasPanel_175 = self:Wnd("DFCanvasPanel_175",UIWidgetBase)

    -- 称号控件
    ---@type RoleInfoTitleItem_Small
    self._wtRoleInfoTitleImage = self:Wnd("WBP_RoleInfo_TitleItem_Small",UIWidgetBase)
    self._wtRoleInfoTitleImage:SelfHitTestInvisible()

    -- 文本翻译
    self._wtTranslationTextRoot = self:Wnd("DFCanvasPanel_TranslationText",UIWidgetBase)
    self._wtTranslationText = self:Wnd("RichTextBlock_Translation",UITextBlock)
    self._wtTranslateBtnRoot = self:Wnd("DFCanvasPanel_TranslationBtn",UIWidgetBase)
    self._wtTranslateBtn = self:Wnd("Button_Translation",UIButton)
    self._wtTranslateBtn:Event("OnClicked",self._OnTranslateBtnClick,self)
    self._wtTranslateLoading = self:Wnd("DFCanvasPanel_Loading",UIWidgetBase)


    --超过时间显示 Msg上面的时间条
    self._timeLimit = 5 * 60

    --长按0.5S,@玩家
    self._atTime = 0.5

    self._playerInfo = nil
    self._position = 0
end

function ChatText:OnInitExtraData()
end

function ChatText:OnOpen()
end

function ChatText:OnShow()
    local function f()
        local geometry = self:GetCachedGeometry()
        local size = geometry:GetLocalSize()
        if size.Y > 1 then
            Module.Chat.Field:SetChatTextHeight(size.Y)
        end
    end
    Timer.DelayCall(0.1, f, self)

    self:RemoveLinkClickBind()
    self:AddLinkClickBind()
end

function ChatText:OnHide()
    self:RemoveLinkClickBind()
end

function ChatText:AddLinkClickBind()
    if isvalid(self._wtTextContent) then
        local decoratorIns = self._wtTextContent:GetDecoratorByClass(self.ChatLinkClickReplySymbol)
        if decoratorIns then
            decoratorIns.OnClicked:Add(self._OnRichTextClicked, self)
        end
    end
end

function ChatText:RemoveLinkClickBind()
    if isvalid(self._wtTextContent) then
        local decoratorIns = self._wtTextContent:GetDecoratorByClass(self.ChatLinkClickReplySymbol)
        if decoratorIns then
            decoratorIns.OnClicked:Clear(self._OnRichTextClicked, self)
        end
    end
end

function ChatText:_OnRichTextClicked(metadataStruct)
    loginfo("ChatText:_OnRichTextClicked")

    for key, value in pairs(metadataStruct.Metadata) do
        if key == "itemEnum" then
            -- 最后处理改枪码消息
            if (value == "weaponCode") then
                self:CopyWeaponCodeToClipboard()
                return
            end

            return
        end
    end
end

-- 将聊天消息携带的改枪码复制到系统粘贴板
function ChatText:CopyWeaponCodeToClipboard()
    ULuautils.ClipboardCopy(tostring(self._chatMsg.custom_chat_weaponCode))
    local targetTipsText = StringUtil.Key2StrFormat(
        Module.Chat.Config.Loc.ChatWeaponCodeShareTips,
        {
            ["codeName"] = ChatLogic.SplitLast(self._chatMsg.custom_chat_weaponCode),
        }
    )
    Module.CommonTips:ShowSimpleTip(targetTipsText)
end

---@param chatMsg ChatMsgContent  聊天内容
---@param playerInfo PlayerSimpleInfo
---@param lastChatMsg ChatMsgContent 上一条聊天内容
---@param position num 索引ID
---@param bShowTranslate bool 是否显示翻译文本
function ChatText:SetInfo(chatMsg,playerInfo,lastChatMsg,position,bShowTranslate)
    self._chatMsg = chatMsg
    self._showText = ""
    self._position = position
    bShowTranslate = setdefault(bShowTranslate, Module.Chat.Config.OPENTRANSLATE)
    if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
        --取消海外移动端语音转文本功能
        bShowTranslate = false
    end
    if self:ShowTime(chatMsg.timestamp/1000,lastChatMsg.timestamp/1000) then
        local unixTimeStamp = chatMsg.timestamp/1000
        local timeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(unixTimeStamp)
        self:SafeSetText(self._wtTextTime, timeStr)
        self._wtTextTime:Visible()
    else
        self._wtTextTime:Collapsed()
    end

    if not chatMsg.msg_type then
        self:ChangeToAppoint(chatMsg, playerInfo)
        return
    else
        self._wtSlotAppoint:Collapsed()
    end

    --设置聊天内容
    self:SetTranslateState(Module.Chat.Config.ETextTranslateState.FucNotSupport)
    local isSelf = playerInfo.player_id == Server.AccountServer:GetPlayerId()
    if chatMsg.msg_type == ChatMsgType.Voice then
        self._wtVoice:Visible()
        self._wtVoice:SetInfo(chatMsg.voice_data,self._position)
        if bShowTranslate and string.len(chatMsg.voice_data.translated_text)>0 then
            self._showText = chatMsg.voice_data.translated_text
            self._wtTextContent:Visible()
            self:SafeSetText(self._wtTextContent, self._showText)
        else
            self._wtTextContent:Collapsed()
        end
    else
        self._wtTextContent:Visible()
        self._wtVoice:Collapsed()
        self._showText = chatMsg.showText
        self:SafeSetText(self._wtTextContent, self._showText)

        if (IsBuildRegionGlobal() or IsBuildRegionGA()) and not isSelf then
            local existTransText = Module.Chat.Field.TranslationModel:GetTranslation(self._showText)
            if existTransText ~= nil then
                self:SetTranslatedText(existTransText)
            else
                self:SetUntranslatedState()
            end
        end
    end


    --队伍频道玩家加入离开
    if chatMsg.flag and chatMsg.flag == 1 then
        self:ShowTeamTip(chatMsg.text,chatMsg.nickName)
        return
    end
    self._canvasTip:Collapsed()
    self._canvasContent:Visible()

    self._playerInfo = playerInfo
    -- local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    -- if curGameFlow == EGameFlowStageType.SafeHouse then
    --     playerInfo.level = playerInfo.season_lvl or 0
    -- else
    --     playerInfo.level = playerInfo.level or 0
    -- end
    --设置玩家信息
    ChatLogic.SetRankDataForCommonTips(playerInfo)
    self:SetIsSelf(isSelf)
    self:SetNickNameSwidget(playerInfo)

    local channel = Module.Chat.Field:GetChannel()
    if isSelf then
        self._wtPlayerIcon:InitPortrait(playerInfo, HeadIconType.HeadNore)
    elseif channel == ChatChannelType.PrivateChatFriend or channel == ChatChannelType.PrivateChatStanger then
        self:NewSetPrivateHeadIcon(channel, playerInfo)
    else
        local btnTbl =  {
            HeadButtonType.AddFriend,
            HeadButtonType.InviteAppTeam,
            HeadButtonType.PlayerInformat,
            HeadButtonType.Chat,
            HeadButtonType.AddBlack,
            HeadButtonType.Report,
        }
        if Server.TeamServer:FindMember(playerInfo.player_id) then
            btnTbl =  {
                HeadButtonType.AddFriend,
                HeadButtonType.PlayerInformat,
                HeadButtonType.Chat,
                HeadButtonType.AddBlack,
                HeadButtonType.Report,
            }
        end
        local source = EChannel2Source[channel]
        local reportText = Module.Chat.Field:ConvertShowText(self._showText)
        if source then
            self._wtPlayerIcon:InitPortrait(playerInfo, HeadIconType.HeadList,btnTbl,source[1],source[2],nil,reportText)
        else
            if playerInfo then
                loginfo(string.format(
                    "something except happen in chatText setinfo func playerId:%s  nickName:%s",
                    tostring(playerInfo.player_id), tostring(playerInfo.nick_name)))
            end
        end
        self._wtPlayerIcon:SetParentBtn(self._wtHeadBtn)
    end

    -- 设置排位或积分内容
    self:SetCommonFlatRankContent(playerInfo)

    -- 设置称号信息
    self._wtRoleInfoTitleImage:UpdateByTitleId(playerInfo.title, playerInfo.rank_title_adcode, playerInfo.rank_title_rank_no)

    self._wtBtnRepeat:Collapsed()
end

function ChatText:SetNickNameSwidget(playerInfo)
    local isFriend = Server.FriendServer:CheckIsFriend(playerInfo.player_id)
    local remarkName = Server.FriendServer:GetFriendRemarkById(playerInfo.player_id)
    local isFriendChannel = (Module.Chat.Field:GetChannel() == ChatChannelType.PrivateChatFriend)
    -- 仅好友私聊频道展示好友的备注名
    if isFriendChannel and isFriend and remarkName ~= "" then
        remarkName = ChatLogic.FullRemarkNameText(playerInfo.nick_name, remarkName)
        self:SafeSetText(self._wtTextName, remarkName)
    else
        self:SafeSetText(self._wtTextName, playerInfo.nick_name)
    end
end

function ChatText:NewSetPrivateHeadIcon(channel, playerInfo)
    local btnTbl =  {
        HeadButtonType.AddFriend,
        HeadButtonType.InviteAppTeam,
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddBlack,
        HeadButtonType.Report,
    }

    local source = EChannel2Source[channel]
    local reportText = Module.Chat.Field:ConvertShowText(self._showText)

    if source then
        self._wtPlayerIcon:InitPortrait(playerInfo, HeadIconType.HeadList,btnTbl,source[1],source[2],nil,reportText)
    else
        if playerInfo then
            loginfo(string.format(
                "something except happen in chatText setinfo func playerId:%s  nickName:%s",
                tostring(playerInfo.player_id), tostring(playerInfo.nick_name)))
        end
    end

    self._wtPlayerIcon:SetParentBtn(self._wtHeadBtn)
end

function ChatText:ChangeToAppoint(chatMsg,playerInfo)
    self._canvasTip:Collapsed()
    self._canvasContent:Collapsed()
    self._wtSlotAppoint:SelfHitTestInvisible()

    self._wtAppoint:SetInfo(chatMsg,playerInfo)
end
--end
-----------------------------------------------------------------------
--把chatMsg的文本和表情拼起来

function ChatText:GetPosition()
    return self._position
end

function ChatText:ShowTime(curtime,lasttime)
    if curtime == lasttime or curtime - lasttime > self._timeLimit then
        return true
    end
    return math.floor(curtime/86400) ~= math.floor(lasttime/86400)
end

function ChatText:ShowTeamTip(text,nickName)
    text = string.format(text, nickName)
    self._canvasTip:Visible()
    self._canvasContent:Collapsed()
    self:SafeSetText(self._wtTxtTip, text)
end

function ChatText:_OnRepeatBtnClick()
    local msg = self._chatMsg
    Module.Chat.Config.evtRepeatSendLastMsg:Invoke(msg)
end

--是不是自己发出去的聊天文本
function ChatText:SetIsSelf(bself)
    self:SetCppValue("Set_Scale",bself)
    self:BP_Set_Type()
    self._isSelf = bself
end

--------------------------------------- 排位积分相关 ------------------------------------
function ChatText:SetCommonFlatRankContent(playerInfo)
    self._WtDFCanvasPanel_175:Visible()
    ChatLogic.ChatSetCommonFlatRankContent(playerInfo, self._wtCommonFlatRank)
end
--end
-----------------------------------------------------------------------

--显示二级操作
function ChatText:_OnTextBtnClick()
    --  这个功能暂时毙
    -- if not self._isSelf and self._chatMsg.msg_type == ChatMsgType.Str or self._chatMsg.msg_type == ChatMsgType.Voice then
    --     local reportInfo = {
    --         playerInfo = self._playerInfo,
    --         showText = self._showText or ""
    --     }
    --     Facade.UIManager:AsyncShowUI(UIName2ID.ChatReport, nil, nil, reportInfo, self._wtBtnText)
    -- end
end

function ChatText:_OnHeadBtnPressed()
    ---DelayCall比实际传进去的time会早一帧,给补上
    Timer.DelayCall(self._atTime + 0.04, function()
        self._wtHeadBtn:ActiveRelease()
    end, self)
end

function ChatText:_OnHeadBtnReleased()
    --私聊频道没有@
    if Module.Chat.Field:GetChannel() == ChatChannelType.PrivateChatFriend or
        Module.Chat.Field:GetChannel() == ChatChannelType.PrivateChatStanger or
        Module.Friend:CheckIsBlack(self._playerInfo.player_id) then
            return
    end
    if self._playerInfo.player_id ~= Server.AccountServer:GetPlayerId() then
        local atNick = "[@" .. self._playerInfo.nick_name .. "]"
        Module.Chat.Config.evtAtPlayer:Invoke(atNick)
        ChatField:AddNickToList(atNick)
    end
end

function ChatText:_OnHeadBtnClick()
    if not self.mMiliTaryReqCount then
        self.mMiliTaryReqCount = 0
    end

    self.mMiliTaryReqCount = (self.mMiliTaryReqCount + 1) % 100

    local count = self.mMiliTaryReqCount

    local OnCSGetMilitaryTagCallback = function(tag)
        if count ~= self.mMiliTaryReqCount then
            return
        end
        self._wtPlayerIcon._playerInfo.military_tag  = tag
        self._wtPlayerIcon:ActiveClick()
    end

    Server.FrontEndChatServer:ReqPlayerMilitaryTag(OnCSGetMilitaryTagCallback, self._playerInfo.player_id)
end

function ChatText:HideRepeatBtn(bHide)
    if bHide then
        self._wtBtnRepeat:Collapsed()
    else
        self._wtBtnRepeat:Visible()
    end
end

function ChatText:GetChatMsg()
    self._chatMsg.showText = self._showText
    return self._chatMsg
end

function ChatText:OnClose()
    self:RemoveAllLuaEvent()
end

--文本翻译
function ChatText:SetTranslateState(tState)
    loginfo(string.format("ChatText:SetTranslateState"))
    self._wtTranslationTextRoot:SetVisibility(ESlateVisibility.Collapsed)
    self._wtTranslateBtnRoot:SetVisibility(ESlateVisibility.Collapsed)
    self:StopWidgetAnim(self.WBP_Chat_Text_Loading)

    if tState == Module.Chat.Config.ETextTranslateState.FucNotSupport then
    elseif tState == Module.Chat.Config.ETextTranslateState.NotTranslated then
        self._wtTranslateBtnRoot:SetVisibility(ESlateVisibility.Visible)
        self._wtTranslateBtn:SetVisibility(ESlateVisibility.Visible)
        self._wtTranslateLoading:SetVisibility(ESlateVisibility.Collapsed)
    elseif tState == Module.Chat.Config.ETextTranslateState.Translating then
        self._wtTranslateBtnRoot:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        self._wtTranslateBtn:SetVisibility(ESlateVisibility.Collapsed)
        self._wtTranslateLoading:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        self:PlayWidgetAnim(self.WBP_Chat_Text_Loading, 0, EUMGSequencePlayMode.Forward,1.0, true)
    elseif tState == Module.Chat.Config.ETextTranslateState.Translated then
        self._wtTranslationTextRoot:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    end
end

function ChatText:SetTranslatedText(transContent)
    loginfo(string.format("ChatText:SetTranslatedText %s",transContent))
    self.showTranslation = transContent
    self:SafeSetText(self._wtTranslationText, self.showTranslation)
    self:SetTranslateState(Module.Chat.Config.ETextTranslateState.Translated)
end

function ChatText:GetShowText()
    return self._showText
end

function ChatText:SetUntranslatedState()
    loginfo(string.format("ChatText:SetUntranslatedState"))
    self.showTranslation = ""
    self:SafeSetText(self._wtTranslationText, self.showTranslation)
    self:SetTranslateState(Module.Chat.Config.ETextTranslateState.NotTranslated)
    --Module.Chat.Config.evtRefreshItem:Invoke(self.position)
end

function ChatText:_OnTranslateBtnClick()
    loginfo("ChatText:_OnTranslateBtnClick")
    local isSelf = (self._playerInfo.player_id == Server.AccountServer:GetPlayerId())
    if isSelf then
        logerror("ChatText:_OnTranslateBtnClick isSelf")
        return
    end

    Module.Chat.Field.TranslationModel:AddListenersIfNotAdded()
    self:SetTranslateState(Module.Chat.Config.ETextTranslateState.Translating)
    Module.Chat.Config.evtRefreshItem:Invoke(self._position)
    Module.Chat.Config.evtChatTranslateText:Invoke(self._showText)
end

-- 仅应对目前篡改msg_type导致的空消息设置报错
-- 后续后端会应对这种情形 拦截
function ChatText:SafeSetText(widgetIns, text)
    if not widgetIns then
        return
    end

    if text then
        widgetIns:SetText(text)
    end
end

return ChatText
