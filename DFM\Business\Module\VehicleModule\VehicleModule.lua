----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMVehicle)
----- LOG FUNCTION AUTO GENERATE END -----------



local VehiclePreloadLogic = require "DFM.Business.Module.VehicleModule.Logic.VehiclePreloadLogic"
local VehicleMainLogic = require "DFM.Business.Module.VehicleModule.Logic.VehicleMainLogic"
local VehicleEnvironmentLogic = require "DFM.Business.Module.VehicleModule.Logic.VehicleEnvironmentLogic"
local VehicleUIContextLogic = require "DFM.Business.Module.VehicleModule.Logic.VehicleUIContextLogic"
local VehicleInspectorLogic = require "DFM.Business.Module.VehicleModule.Logic.Inspector.VehicleInspectorLogic"
local VehicleSkinLogic = require "DFM.Business.Module.VehicleModule.Logic.Skin.VehicleSkinLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"

---@class VehicleModule : ModuleBase
local VehicleModule = class("VehicleModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))

function VehicleModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
function VehicleModule:OnInitModule()

end

function VehicleModule:OnLoadModule()
end

function VehicleModule:OnUnloadModule()
end

function VehicleModule:OnDestroyModule()

end

function VehicleModule:OnSubStageChangeLeave(preSubStageType)
    if preSubStageType ~= ESubStage.HallVehicle then
        return
    end

    self:RemoveLuaEvent(Facade.UIManager.Events.evtPostProcessStackUIChanged, self)
    self:_OnProcessModuleLeave()
end

function VehicleModule:OnSubStageChangeEnter(curSubStageType)
    if curSubStageType ~= ESubStage.HallVehicle then
        return
    end
    self:AddLuaEvent(Facade.UIManager.Events.evtPostProcessStackUIChanged, self._OnProcessModuleEnter, self)
end

function VehicleModule:OnGameFlowChangeLeave(gameFlowType)
end

function VehicleModule:OnGameFlowChangeEnter(gameFlowType)
end

function VehicleModule:OnLoadingLogin2Frontend(gameFlowType)
    VehiclePreloadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

function VehicleModule:OnLoadingGame2Frontend(gameFlowType)
    VehiclePreloadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

function VehicleModule:OnLoadingFrontend2Game(gameFlowType)
    VehiclePreloadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end


---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------
function VehicleModule:OpenStackUIWithParam(uiID, uiParam)
    -- 不能改装载具就不能进入对应界面
    local bColudChangeVehicle = VehicleUIContextLogic.IsCouldChangeVehicle(uiParam)
    if not bColudChangeVehicle then
        return
    end
    Facade.UIManager:AsyncShowUI(uiID, nil, nil, uiParam)
end

function VehicleModule:OpenMainUI(uiParam)
    VehicleMainLogic.OpenMainUI(uiParam)
end

function VehicleModule:OpenInspectorMainUI(uiParam)
    VehicleInspectorLogic.OpenMainUI(uiParam)
end

function VehicleModule:LoadVehicleFromSkinID(iItemID, iActorId)
    VehicleUIContextLogic.LoadVehicleFromSkinID(iItemID, iActorId)
end

function VehicleModule:ProcessDisposeByActorID(iActorId)
    VehicleUIContextLogic.ProcessDisposeByActorID(iActorId)
end

function VehicleModule:LoadStandardVehicleFromVehicleID(iVehicleID, iActorId)
    VehicleUIContextLogic.LoadStandardVehicleFromVehicleID(iVehicleID, iActorId)
end

-- 检查载具资源是否下载完整
function VehicleModule:IsCouldChangeVehicleByPak(bDownloadPak)
    return VehicleUIContextLogic.IsCouldChangeVehicleByPak(bDownloadPak)
end

-- todo:准备删除,请接入Module.LitePackage:GetDownloadCategary(skinID)
-- 检查载具相关资源是否下载
---@param vehicleItemID 载具道具ID或者载具皮肤道具ID
---@param bIsDefaultDownLoaded 当检查到传入vehicleItemID非载具相关ID时的返回值(默认为true)
function VehicleModule:IsDownloadedVehiclePak(vehicleItemID, bIsDefaultDownLoaded)
    bIsDefaultDownLoaded = setdefault(bIsDefaultDownLoaded, true)
    local mainType = ItemHelperTool.GetMainTypeById(vehicleItemID)
    local bIsVehicle = mainType == EItemType.Vehicle
    local bIsVehicleSkin = mainType == EItemType.VehicleSkin
    -- 不是载具相关资源时默认下载了
    if not bIsVehicle and not bIsVehicleSkin then
        return bIsDefaultDownLoaded
    end
    local pakCategory
    if bIsVehicle then
        pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(vehicleItemID)
    elseif bIsVehicleSkin then
        pakCategory = Module.ExpansionPackCoordinator:GetDownloadCategary(vehicleItemID)
    end

    local bIsDownLoaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(pakCategory)
    return bIsDownLoaded
end

---------------------------------------------------------------------------------
--- Private
---------------------------------------------------------------------------------
function VehicleModule:_OnProcessModuleEnter()
    VehicleEnvironmentLogic.Enable()
end

function VehicleModule:_OnProcessModuleLeave()
    VehicleEnvironmentLogic.Disable()
end

function VehicleModule:SetQualityEnv(id)
    local quality = ItemHelperTool.GetQualityTypeById(id)
    local data = EVehicleQualityColor[quality]
    if data then 
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetScalarParameterValue","MandeerEmissiveIntensity",data.Brightness)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallVehicle, "SetVectorParameterValue","MandeerEmissiveColor",data.Color)
        if IsHD() then 
            -- local cppDisplayTypeStr = SubStaget2DisplayType[ESubStage.HallVehicle]
            -- local lightGroupName = string.format("%s_Main", cppDisplayTypeStr)
            -- LightUtil.ActiveLightGroupOnly(lightGroupName)
            if data.Light then 
                LightUtil.ActiveLightGroup(data.Light)
            end
        end
    end
end

return VehicleModule