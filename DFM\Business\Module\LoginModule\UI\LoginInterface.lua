----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------



local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local UGameVersionUtils = import "GameVersionUtils"
local UGameSDKManager = import "GameSDKManager"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local EQuitPreference = import "EQuitPreference"
local FAnchors = import "Anchors"
local ULuaExtension = import("LuaExtension")
local UZipLibrary = import "ZipLibrary"
local LoginLogic = require("DFM.Business.Module.LoginModule.LoginLogic")
local UISimulatorUtil = require "DFM.YxFramework.Managers.UI.Util.UISimulatorUtil"
local DFMGameBrowser = import "DFMGameBrowser"
local ULuaSubsystem          = import "LuaSubsystem"
local LuaSubsystem           = ULuaSubsystem.Get()
local LoginUtil = require("DFM.Business.Module.LoginModule.LoginUtil")

local LiteDownloadManager         = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"

local function log(...)
    loginfo("[LoginInterface]", ...)
end

-- 登录阶段
local ELoginPanelType = {
    ThirdPartLoginType = 0, -- 初始
    ConfirmLoginType = 1,-- 登录过程中
    HideAllUI = 2
}

local EWeGameLoginState = {
    InValidState = 0, -- 没有使用WeGame登录
    QQ = 1,
    WeChat = 2,
    Email = 3,
    Google = 4,
}

local LoginInteractLogic = require("DFM.Business.Module.LoginModule.LoginInteractLogic")
local LoginInterface = ui("LoginInterface")
local LoginConfig = Module.Login.Config
local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"

function LoginInterface:Ctor()
    logerror("[LoginInterface][Ctor]...")
    self._wtThirdPartLoginPanel = self:Wnd("ThirdPartLoginPanel", UIWidgetBase)
    self._wtHorBox_LoginBtnList = self:Wnd("HorBox_LoginBtnList", UIWidgetBase)
    self._wtLoginSuccessPanel = self:Wnd("LoginSuccessPanel", UIWidgetBase)
    self._wtDebugPanel = self:Wnd("DebugPanel", UIWidgetBase)

    -- animLoading图标
    self._wtLoadingAnim = self:Wnd("WBP_CommonReconnectLoading", UIWidgetBase)
    -- 用户名显示
    self._wtUserNameTB = self:Wnd("PlayerNameTB", UITextBlock)

    -- qq登录按钮/微信登录按钮/安全提示按钮
    self._wtQQLoginBtn = self:Wnd("QQLoginBtn", UIButton)
    self._wtQQLoginBtn:Event("OnClicked", self._OnThirdPartLoginBtnClick, self, EChannelType.kChannelQQ)
    self._wtWXLoginBtn = self:Wnd("WXLoginBtn", UIButton)
    self._wtWXLoginBtn:Event("OnClicked", self._OnThirdPartLoginBtnClick, self, EChannelType.kChannelWechat)
    self._wtLoginAgePop = self:Wnd("AgePopBtn", UIButton)
    if self._wtLoginAgePop then
        self._wtLoginAgePop:Event("OnClicked", self._OnAgePopBtnClick, self)
    end

    -- 语言按钮/修复按钮(删除plc和lua热更)/客服按钮/公告按钮
    self._wtLanguageBtn = self:Wnd("LanguageBtn", UIButton)
    self._wtLanguageBtn:Event("OnClicked", self._OnLanguageBtnClick, self, EChannelType.kChannelWechat)
    self._wtLanguageText = self:Wnd("TextBlock_96", UITextBlock)
    self._wtFixBtn = self:Wnd("FixBtn", UIButton)
    self._wtFixBtn:Event("OnClicked", self._OnFixBtnClick, self)
    self._wtAnnounceBtn = self:Wnd("AnnounceBtn", UIButton)
    self._wtAnnounceBtn:Event("OnClicked", self._ShowEmergencyAnnounce, self)
    self._wtCustomerServiceBtn = self:Wnd("CustomerServiceBtn", UIButton)
    self._wtCustomerServiceBtn:Event("OnClicked", self._OnCustomerServiceBtnClick, self)
    if Module.CustomerServices:CheckEntranceEnable(CustomerServicesEntranceType.LoginPage) then
        self._wtCustomerServiceBtn:Visible()
    else
        self._wtCustomerServiceBtn:Collapsed()
    end

    -- 选服按钮/选服名称TB/账号输入TB//账号太长提示/账号登录按钮
    self._wtServerSelectBtn = self:Wnd("ServerSelectBtn", UIButton)
    self._wtServerSelectBtn:Event("OnClicked", self._OnServerSelectBtnClick, self)
    self._wtServerAddrNameTB = self:Wnd("ServerAddrNameTB", UITextBlock)
    self._wtAccountInputTB = self:Wnd("AccountInputTB", UITextBlock)
    self._wtAccountLoginBtn = self:Wnd("AccountLoginBtn", UIButton)
    self._wtAccountLoginBtn:Event("OnClicked", self._OnAccountLoginBtnClick, self)

    -- Debug弹窗按钮
    self._wtDebugBtn = self:Wnd("DebugDisplayBtn", UIButton)
    self._wtDebugBtn:Event("OnClicked", self._OnDebugBtnClick, self)
    self._bOpenDebug = false
    if CloseModuleType and CloseModuleType.bIsCloseLoginInterface then
        self._wtDebugPanel:Collapsed()
        self._wtDebugBtn:Collapsed()
    end

    -- 下载按钮弹窗
    self._wtDownloadBtn = self:Wnd("DownloadBtn", UIButton)
    self._wtDownloadBtn:Event("OnClicked", self._OnDownloadBtnClick, self)

    self._wtCodeHotFixTxt = self:Wnd("CodeHotFixTxt", UITextBlock)

    -- Debug面板控件
    -- 版本号TB/切换账号/GCloud测试/详细版本信息/手动热更/删除lua热更/删除plc热更
    self._wtCurVersion = self:Wnd("CurVersion", UIWidgetBase)
    self._wtCurVersion:SetText(VersionUtil.GetVersionFull())
    if IsHD() then
        self._wtCurVersion:SetText(VersionUtil.GetWeGameVersion())
    end
    self._wtCurVersion:Event("OnTextCommitted", self._OnNewVersionCommitted, self)
    self._wtSwitchUserBtn = self:Wnd("SwitchUserBtn", UIButton)
    self._wtSwitchUserBtn:Event("OnClicked", self._OnSwitchUserBtnClick, self)
    self._wtBtnGCloudTest = self:Wnd("GCloudTestBtn", UIButton)
    self._wtBtnGCloudTest:Event("OnClicked", self._OnBtnGCloudTestClick, self)
    self._wtVersionDetailBtn = self:Wnd("VersionDetailBtn", UIButton)
    self._wtVersionDetailBtn:Event("OnClicked", self._OnVersionDetailBtnClick, self)
    self._wtDisableGuideBtn = self:Wnd("DisableGuideBtn", UIButton)
    self._wtDisableGuideBtn:Event("OnClicked", self._OnDisableGuide, self)

    self._wtDeleteLuaHotfixBtn = self:Wnd("DeleteLuaHotfixBtn", UIButton)
    self._wtDeleteLuaHotfixBtn:Event("OnClicked", self._OnDeleteLuaHotfixBtnClick, self)
    self._wtDeletePakHotfixBtn = self:Wnd("DeletePakHotfixBtn", UIButton)
    self._wtDeletePakHotfixBtn:Event("OnClicked", self._OnDeletePakHotfixBtnClick, self)

    self._wtDebugServerAddrTB = self:Wnd("DebugServerAddrTB", UITextBlock)
    self._wtDebugServerSelectBtn = self:Wnd("DebugServerSelectBtn", UIButton)
    self._wtDebugServerSelectBtn:Event("OnClicked", self._OnServerSelectBtnClick, self)
    
    self._wtTextBlockVersion = self:Wnd("DFTextBlock_Ver", UITextBlock)
    self._wtCheckBoxOpenMicrocosm = self:Wnd("OpenMicrocosmCB", UICheckBox)
    self._wtCheckBoxOpenMicrocosm:SetCallback(self._OnOpenMicrocosmChange, self)
    
    self._wtCheckBoxDisableShaderCompile = self:Wnd("DisableShaderCompileCB", UICheckBox)
    self._wtCheckBoxDisableShaderCompile:SetCallback(self._OnDisableShaderCompileChange, self)

    self._wtCheckBoxDisableLog = self:Wnd("DisableLogCB", UICheckBox)
    self._wtCheckBoxDisableLog:SetCallback(self._OnDisableLogChange, self)
    self._wtCheckBoxUseNewProto = self:Wnd("UseNewProto", UICheckBox)
    self.IsWinPack = false
    self._hasShowLoginButton = false
    self.bIsQRCodePanelShow = false
    self._lastClickTime = 0;
    if DFHD_LUA ~= 1 and PLATFORM_WINDOWS and _WITH_EDITOR ~= 1 then
        self.IsWinPack = true
    end

    -- 扫码登录
    self._wtQRCodeLoginBtn = self:Wnd("ScanLoginBtn",UIButton)
    self._wtQRCodeLoginBtn:Event("OnClicked",self._OnQRCodePanelClick, self)
    self._wtQRCodePanel = self:Wnd("ScanPanel", UIWidgetBase)
    self._wtQRCodeBtnQQ = self:Wnd("QQScanBtn", UIButton)
    self._wtQRCodeBtnWeChat = self:Wnd("WeChatScanBtn", UIButton)
    self._wtQRCodeBtnQQ:Event("OnClicked",self._OnQRCodeLoginBtnClick,self, EChannelType.kChannelQQ)
    self._wtQRCodeBtnWeChat:Event("OnClicked",self._OnQRCodeLoginBtnClick,self, EChannelType.kChannelWechat)
    -- 阅读腾讯服务协议 隐私协议
    self._wtServiceInfoPanel = self:Wnd("DFCanvasPanel_66", UIWidgetBase)
    self._wtServiceAgreePanel = self:Wnd("DFHorizontalBox_175", UIWidgetBase)
    if IsBuildRegionGlobal() or IsBuildRegionGA() then
        self._wtServiceInfoPanel:SetVisibility(ESlateVisibility.Collapsed)
        if PLATFORM_WINDOWS then
            self._wtServiceAgreePanel:SetVisibility(ESlateVisibility.Collapsed)
            self._bServiceAgreeFlag = true
            Module.Login.Field:SetServiceAgreeFlag(true)
        end
    end
    self._wtServiceAgreeBtn = self:Wnd("WBP_DFCommonCheckBoxWithText", UIWidgetBase):Wnd("DFCheckBox_Icon",UICheckBox)
    self._wtServiceAgreeBtn:SetCallback(self._OnServiceAgreeBtnClick, self)
    self._wtserviceLinkText = self:Wnd("DFRichTextBlock_Rule", UITextBlock)
    -- PC扫码登录面板设置
    if DFHD_LUA == 1 or self.IsWinPack then
        self._wtLoginWebPanel = self:Wnd("LoginWebPanel", UIWidgetBase)
        self._wtLoginWebPanel:SetVisibility(ESlateVisibility.Collapsed)
        self._wtQRCodeLoginBtn:SetVisibility(ESlateVisibility.Collapsed)
    end

    -- BEGIN MODIFICATION @ VIRTUOS
    if PLATFORM_GEN9 == 1 then
        -- Popup reconnect window if connection failed.
        self._bShouldShowReconnectWindow = true

        -- Hide service accept toggle which is not supported on Console.
        local wtServiceAgreeGroup = self:Wnd("DFHorizontalBox_175", UIWidgetBase)
        wtServiceAgreeGroup:Collapsed()

        -- Hide right side buttons bar which is not supported on Console.
        local wtSideBtnsGroup = self:Wnd("DFSizeBox_1", UIWidgetBase)
        wtSideBtnsGroup:Collapsed()
    end
    -- END MODIFICATION

    self:AddListeners()
end

function LoginInterface:OnShowBegin()
    if Facade.ModuleManager:IsModuleValid("CommonBar") then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    end
    self:BindBackAction(self._OnQuitBtnClicked, self)
end

function LoginInterface:OnHideBegin()
    Facade.UIManager:ClearSubUIByParent(self, self._wtHorBox_LoginBtnList)
    self:UnBindBackAction()
end

function LoginInterface:_OnQuitBtnClicked()
    -- @yixiaoguan 实现封装暴露到Module
    local fCancelLeaveGameHandle = CreateCallBack(function(self)
        Module.CommonBar:BindBackHandler(self._OnQuitBtnClicked, self)
    end, self)

    local fConfirmLeaveGameHandle = CreateCallBack(function(self)
    end, self)
    Module.CommonBar:FlowBackQuitGame(fCancelLeaveGameHandle, fConfirmLeaveGameHandle)
end

function LoginInterface:AddListeners()
    self:AddLuaEvent(Module.Login.Config.Events.evtOnLanguageChanged, self._OnLanguageChanged, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtServerAddrChanged, self._OnServerAddrChanged, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnLoginStartDirectLogin, self._OnOnLoginStartDirectLogin, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnLoginStartGetThirdPartInfo,self._OnLoginStartGetThirdPartInfo, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnLoginStartConnect, self._OnLoginStartConnect, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnStartRegister, self._OnStartRegister, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnRegisterSuccess, self._OnRegisterSuccess, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnLoginFailed, self._OnLoginFailed, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnPrivacyStatusChange, self._UpdateServiceAgreeState, self)
    self:AddLuaEvent(Module.Login.Config.Events.evtOnPrivacyPanelConfirm, self._OnThirdPartLoginBtnClick, self) 
    self:AddLuaEvent(Module.Login.Config.Events.evtOnRefreshLoginPanel, self._OnRefreshLoginPanel, self)
    Facade.ProtoManager.Events.evtOnConnectStayInQueue:AddListener(self._OnConnectStayInQueue, self)
    Facade.ProtoManager.Events.evtOnDisconnect:AddListener(self._OnDisConnect, self)
    Module.GCloudSDK.Config.Events.evtOnLoadAnnounceData:AddListener(self._OnLoadAnnounceData, self)
    Module.GCloudSDK.Config.Events.evtOnEmergencyAnnounceClosed:AddListener(self._OnEmergencyAnnounceClosed, self)

    -- BEGIN MODIFICATION @ VIRTUOS: Popup reconnect window if connection failed. 
    if PLATFORM_GEN9 then
        local UGameLogin = import "DFMGameLogin"
        local gameLoginIns = UGameLogin.Get(GetGameInstance())
        if gameLoginIns then
            gameLoginIns.OnGameGetFocusFromLIP:Add(CreateCPlusCallBack(self._OnGameGetFocusFromLIP, self))
        end
    end
    -- END MODIFICATION
end

-- BEGIN MODIFICATION @ VIRTUOS: Popup reconnect window if connection failed. 
function LoginInterface:_OnGameGetFocusFromLIP(bGameGetFocus)
    logerror("[LoginInterface][OnGameGetFocusFromLIP]...", bGameGetFocus)

    self._bShouldShowReconnectWindow = bGameGetFocus
end
-- END MODIFICATION

function LoginInterface:_OnDisConnect()
    logerror("[LoginInterface][OnDisConnect]...")
    self:_OnLoginFailed()
    -- BEGIN MODIFICATION - VIRTUOS
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetWorld())
        DFMOnlineIdentityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
    end
    -- END MODIFICATION - VIRTUOS
end

-- 控制台直接修改客户端版本
function LoginInterface:_OnNewVersionCommitted()
    local newVer = self._wtCurVersion:GetText()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        string.format("DebugUtils.SetClientVersion %s", newVer),
        nil
    )
    UGameVersionUtils.SetResVersion(tostring(newVer))
end

-- 切换账号
function LoginInterface:_OnSwitchUserBtnClick()
    Module.Login:SwitchUser(true)
end

-- 禁用引导
function LoginInterface:_OnDisableGuide()
    Server.GuideServer.ForceSkipGuide = true
end

-- GCloud测试
function LoginInterface:_OnBtnGCloudTestClick()
    Module.GCloudSDK:ShowMainPanel()
end

-- 详细版本信息
function LoginInterface:_OnVersionDetailBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.VersionDetail, fHideVersionDetailWindow)
end

-- 删除Lua热更
function LoginInterface:_OnDeleteLuaHotfixBtnClick()
    loginfo("[LoginInterface][OnDeleteLuaHotfixBtnClick]..")
    local LuaSubsystem = import("LuaSubsystem").Get()
    if not LuaSubsystem then
        return
    end
    local localLuaSourcePath = LuaSubsystem:Convert2PublicSavedPath() .. "LuaSource"
    ULuaExtension.DeleteMoLiDirectoryRecursivelyByPath(localLuaSourcePath)
    Facade.ConfigManager:SetString("LastHotUpdateTimestamp", "0")
    Facade.ConfigManager:SetString("LastHotUpdateVersion", "")
end

-- 删除Pak热更
function LoginInterface:_OnDeletePakHotfixBtnClick()
    loginfo("[LoginInterface][OnDeletePakHotfixBtnClick]..")
    local DolphinManager = import("DolphinManager").Get(GetGameInstance())
    if DolphinManager then
        DolphinManager:ClearFiles()
    end
end

-- 执行puffer深度修复
function LoginInterface:_DoPufferFixDeep()
    loginfo("[LoginInterface][_DoPufferFixDeep]..")
    LiteDownloadManager:SetAllPakFixDeep(true)
end

-- 登录开发账号
function LoginInterface:_OnAccountLoginBtnClick()
    if VersionUtil.IsShipping() and not VersionUtil.IsTestClientType() then
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.MobileNotAllowAccountLogin)
        Module.Login.Config.Events.evtOnLoginFailed:Invoke()
        return
    end

    local openId = tostring(self._wtAccountInputTB:GetText())
    if string.match(openId, "%d+") == openId then
      
        LoginInteractLogic._DirectLoginFlow(openId)
    else
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.AccountNeedAllNum)
    end
end

--下载按钮
function LoginInterface:_OnDownloadBtnClick()
    if DFHD_LUA == 0 then
        Module.LitePackage:ShowMainPanel(false)
    end
end

-- 开发按钮
function LoginInterface:_OnDebugBtnClick()
    if VersionUtil.IsShipping() then
        return
    end
    if self._bOpenDebug then
        self._wtDebugPanel:Collapsed()
    else
        self._wtDebugPanel:Visible()
    end
    self._bOpenDebug = not self._bOpenDebug
end


-- 第三方平台登录
function LoginInterface:_OnThirdPartLoginBtnClick(channel)
    logerror("[LoginInterface][OnThirdPartLoginBtnClick] channel:", channel)
    if self._bServiceAgreeFlag == false and channel ~= EChannelType.kChannelNone then
        logerror("[LoginInterface][OnThirdPartLoginBtnClick] service need agreed before login")
        if (IsBuildRegionGlobal() or IsBuildRegionGA()) and not PLATFORM_WINDOWS then
            Facade.UIManager:AsyncShowUI(UIName2ID.PrivacyPanel, nil, nil, channel)
        else
            Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.ReadServiceInfo, 5.0)
        end
        return
    end

    if LoginUtil.IsSimulator() then
        logerror("[LoginInterface][OnThirdPartLoginBtnClick] is simulator")
        local fOnLoginFailed = function()
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            LoginConfig.Loc.SimulatorNotAllow,
            fOnLoginFailed,
            LoginConfig.Loc.ExitClient
        )
        return
    end

    -- 防止重复点击
    local curTime = TimeUtil.GetCurrentTime()
    if curTime - self._lastClickTime <= 5 and channel ~= EChannelType.kChannelNone then
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.ClickTooOften)
        logerror("[LoginInterface][OnThirdPartLoginBtnClick] repeat click")
        return
    else
        self._lastClickTime = curTime;
    end

    if Module.Login:CheckLockNow() then
        logerror("[LoginInterface][OnThirdPartLoginBtnClick] login failed too many times")
        local tips = Module.Login.Field:GetForBidLoginTips()
        if tips then
            Module.CommonTips:ShowSimpleTip(tips)
        end
        return
    end

    Module.Login.Field:SetLoginState(true)
    Module.GCloudSDK:LuaReportLoginEvent({Login = "LoginBtnClicked"})
    Server.SDKInfoServer:SetLoginSubChannelID(channel)
    if IsBuildRegionGlobal() then
        LoginLogic.NativeLoginINTL(channel)
    elseif IsBuildRegionGA() then
        if not channel or channel == EChannelType.kChannelNone then
            Facade.UIManager:AsyncShowUI(UIName2ID.MoreChiocesPopView)
        else
            Server.SDKInfoServer:SetLoginThirdType(channel)
            LoginInteractLogic.GetThirdPartInfo(channel)
        end
    else
        if DFHD_LUA == 1 or self.IsWinPack or _WITH_EDITOR == 1 then
            -- 扫码登录
            self:_OpenWebLoginPanel(channel)
        else
            LoginInteractLogic.GetThirdPartInfo(channel)
        end
    end
end

---@desc 刷新登录间隔时间
---@param panelType number
function LoginInterface:_OnRefreshLoginPanel(panelType)
    self:ShowLoginPanel(panelType)
end

function LoginInterface:_OnQRCodePanelClick()
    logwarning("[LoginInterface][OnQRCodePanelClick] bIsQRCodePanelShow:", self.bIsQRCodePanelShow)
    if self.bIsQRCodePanelShow == false then
        self._wtQRCodePanel:SetVisibility(ESlateVisibility.Visible)
        self.bIsQRCodePanelShow = true
    else
        self._wtQRCodePanel:SetVisibility(ESlateVisibility.Collapsed)
        self.bIsQRCodePanelShow = false
    end
end

function LoginInterface:_OnServiceAgreeBtnClick(bIsChecked)
    if bIsChecked and (IsMobile()) then
        local jsonInfo
        if PLATFORM_ANDROID then
            local deviceId = ULuaExtension.GetDeviceID()
            local model =  ULuaExtension.GetDeviceMakeAndModel()
            jsonInfo =  string.format("{\"AndroidID\":\"{%s}\",  \"Model\":\"{%s}\"}", deviceId, model)
        else
            local model =  ULuaExtension.GetDeviceMakeAndModel();
            jsonInfo =  string.format("{\"Model\":\"{%s}\"}", model)
        end
        logerror("_OnServiceAgreeBtnClick jsonInfo:",jsonInfo)
        local UGameLogin = import "DFMGameLogin"
        local gameLoginIns = UGameLogin.Get(GetGameInstance())
        if gameLoginIns then
            gameLoginIns:SetSensitiveInfo(jsonInfo)
        end
    end

    logerror("[LoginInterface][OnServiceAgreeBtnClick] bIsChecked:", bIsChecked)
    self._bServiceAgreeFlag = bIsChecked
    Module.Login.Field:SetServiceAgreeFlag(bIsChecked)
    Module.Login.Config.Events.evtOnSetServiceAgree:Invoke(self._bServiceAgreeFlag)
end

function LoginInterface:_UpdateServiceAgreeState()
    self._bServiceAgreeFlag = Module.Login.Field:GetServiceAgreeFlag()
    self._wtServiceAgreeBtn:SetIsChecked(self._bServiceAgreeFlag, false)
    Module.Login.Config.Events.evtOnSetServiceAgree:Invoke(self._bServiceAgreeFlag)
end

function LoginInterface:_OnQRCodeLoginBtnClick(channel)
    logwarning("[LoginInterface][OnQRCodeLoginBtnClick] channel:", channel)
    if self._bServiceAgreeFlag == false then
        logerror("[LoginInterface][OnQRCodeLoginBtnClick] bServiceAgreeFlag = false")
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.ReadServiceInfo, 5.0)
        return
    end

    local curTime = TimeUtil.GetCurrentTime()
    if curTime - self._lastClickTime <= 5 then
        logerror("[LoginInterface][OnQRCodeLoginBtnClick] click too fast")
        return
    else
        self._lastClickTime = curTime;
    end

    if LoginUtil.IsSimulator() then
        logerror("[LoginInterface update][OnThirdPartLoginBtnClick] is simulator")
        local fOnLoginFailed = function()
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            local EQuitPreference = import "EQuitPreference"
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            LoginConfig.Loc.SimulatorNotAllow,
            fOnLoginFailed,
            LoginConfig.Loc.ExitClient
        )
        return
    end

    LoginInteractLogic.GetThirdPartInfo(channel, true)
    self._wtQRCodePanel:SetVisibility(ESlateVisibility.Collapsed)
    self.bIsQRCodePanelShow = false
end

function LoginInterface:OnInitExtraData(...)
    logerror("LoginInterface:OnInitExtraData")
end

function LoginInterface:OnOpen()
    --写入热更版本号
    local hotUpdateVersion = tostring(UGameVersionUtils.GetVersion()) .. "." .. tostring(LuaSubsystem:GetMoLiVersion())
    local versionApp = UGameVersionUtils.GetAppProductVersion()
    local versionRes = UGameVersionUtils.GetResVersion()
    self._wtCodeHotFixTxt:SetText(string.format(Module.Login.Config.Loc.Version, versionApp, versionRes, hotUpdateVersion))
    
    --写入crashsight版本号
    Module.GCloudSDK:SetHotfixData("CodeHotUpdateVersion",hotUpdateVersion)
    Module.GCloudSDK:SetHotfixData("ResHotUpdateVersion", versionRes)
    Facade.ProtoManager:SetReconnectState(false)
    Facade.ProtoManager:SetQuickReconnectState(false)
    Module.GCloudSDK:LuaReportLoginEvent({Login = "OpenLoginInterface"})
    
    --设置UI
    self:_SetUIButtonEnable()
    if VersionUtil.IsShipping()  then
       self._wtServerSelectBtn:Collapsed()
       self._wtDebugServerSelectBtn:Collapsed()
    else
         Module.GM:OpenGMButtonPanel()
    end

    -- Maple修改服务器地址
    Module.GCloudSDK:InitMapleServerUrl()

    self:InitServerAddrDisplay()
    self:InitAccountLoginName()

    local LoginPanelType = ELoginPanelType.ThirdPartLoginType
    if PLATFORM_ANDROID and IsBuildRegionGA() then
        --获取上次登录的渠道
        local lastChannelId = Server.SDKInfoServer:GetLastConnectChannelID()
        log("AutoLogin channelId:",lastChannelId)
        if lastChannelId == 10 and Module.Login.Field:GetAutoLogin() and Module.Login.Field:GetIsFirstEnterLogin() then
            LoginPanelType = ELoginPanelType.ConfirmLoginType
        end
    end
    self:ShowLoginPanel(LoginPanelType)

    self:PopEmergencyAnnounce()
    
    -- 处理异账号
    self:HandleDiffAccount() 
	-- 隐藏loading进度条等即可，视频照常播放
    Module.GCloudSDK:SetLoadingPanelVisibility(false)

    --设置是否启动小宇宙
    local isOpenMicrocosm = UGameVersionUtils.IsOpenMicrocosm()
    self._wtCheckBoxOpenMicrocosm:SetIsChecked(isOpenMicrocosm,false)
    self:_OnOpenMicrocosmChange(isOpenMicrocosm)

    --设置是否编译Shader
    local isDisableShaderCompile = UGameVersionUtils.IsDisableShaderCompile()
    self._wtCheckBoxDisableShaderCompile:SetIsChecked(isDisableShaderCompile, false)
    self:_OnDisableShaderCompileChange(isDisableShaderCompile)
    
    --设置是否显示日志
    local isDisableLog = UGameVersionUtils.IsDisableLog()
    self._wtCheckBoxDisableLog:SetIsChecked(isDisableLog,false)
    self:_OnDisableLogChange(isDisableLog)

    self:_UpdateServiceAgreeState()

    -- 用户协议超链接
    if isvalid(self._wtserviceLinkText) then
        local decoratorIns = self._wtserviceLinkText:GetDecoratorByClass(self.BpTextBlockDecorator)
        if decoratorIns then
            decoratorIns.OnClicked:Add(self._OnRichTextClicked, self)
        end
    end
    if IsBuildRegionCN() then
        self._wtserviceLinkText:SetText(Module.Login.Config.Loc.RuleLinkUrl)
    elseif IsBuildRegionGlobal() then
        self._wtserviceLinkText:SetText(Module.Login.Config.Loc.RuleLinkUrl_Global)
    elseif IsBuildRegionGA() then
        self._wtserviceLinkText:SetText(Module.Login.Config.Loc.RuleLinkUrl_Garena)
    end
    

    -- 国服版本屏蔽语言切换
    if IsBuildRegionCN() and (not IsInEditor()) then
        self._wtLanguageBtn:Collapsed()
        self._wtLanguageText:Collapsed()
    else
        self._wtLanguageBtn:Visible()
        self._wtLanguageText:Visible()
    end

    self._wtTextBlockVersion:Collapsed()
    self:_SetUIButtonEnableExtra()
    Module.GCloudSDK:ReqNotificationPermisson()
end

function LoginInterface:_OnRichTextClicked(metadataStruct)
	for key, value in pairs(metadataStruct.Metadata) do
		if key == "link" then
            if not IsHD() then
                Module.GCloudSDK:OpenUrl(value)
            elseif DFMGameBrowser and DFMGameBrowser.LaunchURL then
                local parms = ""
                local error = ""
                DFMGameBrowser.LaunchURL(value,parms,error)
                logframe("Mail open link:",value,"with error:",error)
            end
		end
	end
end

-- 多平台自动登录
function LoginInterface:TryAutoLogin()
    logerror("[LoginInterface][TryAutoLogin]...")
    local UGameLogin = import "DFMGameLogin"
    local gameLoginIns = UGameLogin.Get(GetGameInstance())
    if gameLoginIns.bNeedShowTips then
        logerror("[LoginInterface][TryAutoLogin] bNeedShowTips,Stop AutoLogin")
        return
    end
    LoginLogic.GameAutoLogin()
end

function LoginInterface:InitAccountLoginName()
    logwarning("[LoginInterface][InitAccountLoginName]...")
    -- local userName = Server.SDKInfoServer:GetUserName()
    local openId = Server.SDKInfoServer:GetOpenIdStr()
    self._wtAccountInputTB:SetText(openId)
end

function LoginInterface:InitServerAddrDisplay()
    local content = Server.SDKInfoServer:GetServerAddrDisplay()
    self._wtServerAddrNameTB:SetText(content)
    self._wtDebugServerAddrTB:SetText(content)
end

function LoginInterface:OnClose()
    logerror("[LoginInterface][OnClose]...")
    self:RemoveAllLuaEvent()
end

-- 展示不同阶段的登录界面
function LoginInterface:ShowLoginPanel(eLoginPanelType)
    logwarning("[LoginInterface][ShowLoginPanel]...")
    if eLoginPanelType == ELoginPanelType.ThirdPartLoginType then
        self._wtThirdPartLoginPanel:Visible()
        self._wtHorBox_LoginBtnList:Visible()
        self._wtLoginSuccessPanel:Collapsed()
        LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(false)
        self:ShowLoadingAnimation(false)
        --判断应该显示哪些登录入口
        self:CheckShowLoginButton()
    elseif eLoginPanelType == ELoginPanelType.ConfirmLoginType then
        self._wtThirdPartLoginPanel:Collapsed()
        self._wtHorBox_LoginBtnList:Visible()
        local userName = Server.SDKInfoServer:GetUserName()
        self._wtUserNameTB:SetText(userName)
        self._wtLoginSuccessPanel:Visible()
        LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(true)
        self:ShowLoadingAnimation(true)
    else
        logerror("[LoginInterface][ShowLoginPanel] eLoginPanelType error")
        self._wtThirdPartLoginPanel:Visible()
        self._wtHorBox_LoginBtnList:Visible()
        self._wtLoginSuccessPanel:Collapsed()
    end
end

function LoginInterface:_OpenWebLoginPanel(channel)
    Facade.UIManager:AsyncShowUI(UIName2ID.LoginWebPanel, nil,nil, channel)
end

local hasInitLoginButton = false
function LoginInterface:CheckShowLoginButton()
    if self._hasShowLoginButton then
        logerror("[LoginInterface][ShowLoginPanel] hasShowLoginButton == true")
        return
    end

    local loginlist = Server.SDKInfoServer:GetLoginList()
    if _WITH_EDITOR == 1 then
        --editor
        self:_OnDebugBtnClick()
    end

    if not loginlist or loginlist == "" then
        logerror("[LoginInterface][ShowLoginPanel] not fin login list")
        return
    end
   
    -- 排列渠道显示
    local str = StringUtil.StringSplit(loginlist, ",")
    local loginconfig = Module.Login.Config

    self._count = #str
    self._line = 1

    if self._count > 4 then
        self._line = 2
        self._StartPosX = -450 - (4 - 1) * 0.5 * 400
    elseif self._count >= 2 and self._count < 4 then
        self._StartPosX = -450 - (self._count - 1) * 0.5 * 100
    else
        self._StartPosX = -200 - (self._count - 1) * 0.5 * 100
    end
    
    self._StartPosY = -350 - (self._line - 1) * 0.5 * 100

    Facade.UIManager:RemoveSubUIByParent(self, self._wtHorBox_LoginBtnList)
    for index, id in ipairs(str) do
        for key, value in pairs(EChannelType) do
            if value == tonumber(id) then
                -- 改成新的AddSubUI
                local weakContentWidget, contentInstanceID = Facade.UIManager:AddSubUI(self, UIName2ID.LoginButton, self._wtHorBox_LoginBtnList)
                local contentWidgetIns = getfromweak(weakContentWidget)
                if contentWidgetIns then
                    contentWidgetIns:SetChannel(value, self._OnThirdPartLoginBtnClick, self, value)
                end
            end
        end
    end

    if IsBuildRegionGlobal() or (IsBuildRegionGA() and PLATFORM_IOS) then
        local value = EChannelType.kChannelNone
        -- add LI Pass
        local weakContentWidget, contentInstanceID = Facade.UIManager:AddSubUI(self, UIName2ID.LoginButton, self._wtHorBox_LoginBtnList)
        local contentWidgetIns = getfromweak(weakContentWidget)
        if contentWidgetIns then
            contentWidgetIns:SetChannel(value, self._OnThirdPartLoginBtnClick, self, value)
            -- set ... style
            contentWidgetIns._wtIcon:Collapsed()
            local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(contentWidgetIns._wtLoginBtn)
            if slot then
                slot:SetSize(FVector2D(96, 96))
            end
        end
    end

    self._hasShowLoginButton = true
end

function LoginInterface:_OnServerSelectBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.LoginServerList, fHideServerListWindow)
end

function LoginInterface:_OnLanguageChanged()
end

-- when server addr changed --
function LoginInterface:_OnServerAddrChanged()
    local serverName = Server.SDKInfoServer:GetServerAddrDisplay()
    logerror("[LoginInterface][OnServerAddrChanged] serverName:",serverName)
    self._wtServerAddrNameTB:SetText(serverName)
    self._wtDebugServerAddrTB:SetText(serverName)
end

-- when receive events --
function LoginInterface:_OnOnLoginStartDirectLogin()
    self:ShowLoginPanel(ELoginPanelType.ConfirmLoginType)
end

function LoginInterface:_OnLoginStartGetThirdPartInfo()
    self._bOpenDebug = false
    self._wtDebugPanel:Collapsed()
end

function LoginInterface:_OnLoginStartConnect()
    self:ShowLoginPanel(ELoginPanelType.ConfirmLoginType)
end

-- 已废弃
function LoginInterface:_OnStartRegister()
    logerror("[LoginInterface][OnStartRegister]...")
    local playerName = Server.SDKInfoServer:GetUserName()
    Facade.UIManager:AsyncShowUI(UIName2ID.RegisterView, nil, nil, playerName)
end

-- 已废弃
function LoginInterface:_OnRegisterSuccess()
    self:ShowLoginPanel(ELoginPanelType.ConfirmLoginType)
end

function LoginInterface:_OnLoginFailed()
    logerror("[LoginInterface][OnLoginFailed]...")
    Module.Login.Field:SetLoginState(false)

    -- BEGIN MODIFICATION @ VIRTUOS: Popup reconnect window if connection failed.
    log("LoginInterface:_OnLoginFailed")
    if PLATFORM_GEN9 == 1 then
        if self._bShouldShowReconnectWindow and Module.Login.Field:GetConnectSucceed() == false then
            self._bShouldShowReconnectWindow = false
            local function ConfirmFunc()
                self:TryAutoLogin()
                self._bShouldShowReconnectWindow = true
            end

            local UGameFlowDelegates = import "GameFlowDelegates"
            local curGameFlow = UGameFlowDelegates.GetGameFlowDelegates(GetGameInstance()):GetCurGameFlowStage()
            if curGameFlow == EGameFlowStageType.Login then
                Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
                    NSLOCTEXT("CommonTipsModule", "Console_Lua_Proto_ConnectNotLink", "与三角洲行动网络断开，是否尝试再次连接"),
                    ConfirmFunc,
                    NSLOCTEXT("CommonTipsModule", "Lua_Proto_ConfirmReconnect", "确认重连"))
            else
                UECall_StartReconnectingNtf(true)
            end
        end
    else
        self:ShowLoginPanel(ELoginPanelType.ThirdPartLoginType)
    end
    -- END MODIFICATION

end

local _StayInQueueRefreshInterval = 1.0
function LoginInterface:_OnConnectStayInQueue(connectResult)
    self._StayInQueueEstimateTime = connectResult and connectResult.WaitingEstimateTime or 0
    self._StayInQueueLen = connectResult and connectResult.WaitingQueueLen or 0
    self._stayInQueuePos = connectResult and connectResult.WaitingQueuePosition or 0

    if self._StayInQueueStateTimer then
        Timer.CancelDelay(self._StayInQueueStateTimer)
		self._StayInQueueStateTimer = nil
    end

    if self._StayInQueueEstimateTime > 0 then
        local RefreshTimes = math.ceil(self._StayInQueueEstimateTime / _StayInQueueRefreshInterval)
        self._StayInQueueStateTimer = Timer.DelayCallSomeTimes(_StayInQueueRefreshInterval, RefreshTimes, self._OnConnectStayInQueueRefresh, self)
    end
end

function LoginInterface:_OnConnectStayInQueueRefresh()
    self._StayInQueueEstimateTime = self._StayInQueueEstimateTime - _StayInQueueRefreshInterval
    if self._StayInQueueEstimateTime <= 0 then
        self._StayInQueueEstimateTime = 0
    end
    local tips = string.format(Module.Login.Config.Loc.StayInQueueTip, self._StayInQueueLen, self._stayInQueuePos, self._StayInQueueEstimateTime)
    Module.CommonTips:ShowSimpleTip(tips, _StayInQueueRefreshInterval, false)

    -- 倒计时结束
    if self._StayInQueueEstimateTime == 0 then
        self._OnConnectStayInQueue(nil)
    end
end

function LoginInterface:_OnLanguageBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.LanguagePopView)
end

-- 热更修复按钮
function LoginInterface:_OnFixBtnClick()
    logwarning("LoginInterface][OnFixBtnClick]...")

    -- Module.CommonTips:ShowConfirmWindowWithNotice(
    --     Module.Login.Config.Loc.FixTipsText,
    --     Module.Login.Config.Loc.FixNoticeText,
    --     function()
    --         self:_OnDeleteLuaHotfixBtnClick()
    --         self:_OnDeletePakHotfixBtnClick()

    --         Module.Login.Config.flowEvtReOpenLogin:Invoke()
    --     end,
    --     function()
    --     end,
    --     Module.Login.Config.Loc.FixCancelBtnText,
    --     Module.Login.Config.Loc.FixConfirmBtnText
    -- )

    Module.CommonTips:ShowConfirmWindowWithNotice(
        Module.Login.Config.Loc.FixPufferTipsText,
        Module.Login.Config.Loc.FixPufferNoticeText,
        function()
            self:_DoPufferFixDeep()
            local UKismetSystemLibrary = import "KismetSystemLibrary"
            if PLATFORM_IOS then
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.GMTestCrash")
            else
                UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
            end
        end,
        function()
        end,
        Module.Login.Config.Loc.FixCancelBtnText,
        Module.Login.Config.Loc.FixPufferConfirmBtnText
    )
end

-- 客服按钮用于上报日志
function LoginInterface:_OnCustomerServiceBtnClick()
    logwarning("[LoginInterface][OnCustomerServiceBtnClick]...")
    Module.CustomerServices:OpenEntrance(CustomerServicesEntranceType.LoginPage)
end

function LoginInterface:_OnAgePopBtnClick()
    if not Module.Login.Field.ageBtnClickEnable then -- 不让点就返回
        logwarning("[LoginInterface][OnAgePopBtnClick] skip btn click when cooling")
        return
    end

    local function fFinishCallback(uiIns)
        Module.Login.Field.ageBtnClickEnable = true -- 创建出来之后才让点
    end

    Module.Login.Field.ageBtnClickEnable = false -- 点击后不让点了就
    Facade.UIManager:AsyncShowUI(UIName2ID.LoginAgePop, fFinishCallback)
end

function LoginInterface:_OnOpenMicrocosmChange(bIsChecked)
    if IsWorldTypePIE() then
        UGameVersionUtils.SetOpenMicrocosm(bIsChecked)

        local USpeedUpEditorLibrary = import "SpeedUpEditorLibrary"
        local ESpeedUpEditorOptimizeType = import "ESpeedUpEditorOptimizeType"
        if USpeedUpEditorLibrary then
            local OptMode = bIsChecked and ESpeedUpEditorOptimizeType.MicrocosmOn or ESpeedUpEditorOptimizeType.MicrocosmOff
            USpeedUpEditorLibrary.TurnOnCommonOptimization(GetGameInstance(), OptMode)
        end
    else
        self._wtCheckBoxOpenMicrocosm:SetVisibility(ESlateVisibility.Collapsed)
    end

end

function LoginInterface:_OnDisableShaderCompileChange(bIsChecked)
    if IsWorldTypePIE() then
        local cmdStr = "r.shader.CloseShaderCompile 0"
        if bIsChecked then
            cmdStr = "r.shader.CloseShaderCompile 1"
        end
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmdStr)
        UGameVersionUtils.SetDisableShaderCompile(bIsChecked)
    else
        self._wtCheckBoxDisableShaderCompile:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function LoginInterface:_OnDisableLogChange(bIsChecked)
    if IsInEditor() then
        local cmdStr = "log reset"
        if bIsChecked then
            cmdStr = "log Global off"
        end
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmdStr)
        UGameVersionUtils.SetDisableLog(bIsChecked)
    else
        self._wtCheckBoxDisableLog:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function LoginInterface:_OnHandleWeGameLoginResult(bSuccess)
end
-- DFHD_LUA end

function LoginInterface:_ShowEmergencyAnnounce()
    logerror("[LoginInterface][ShowEmergencyAnnounce]")

    self._announceAutoLoginState = false

    if not Module.GCloudSDK:IsAnnouncementEnable() then
        logerror("[LoginInterface][ShowEmergencyAnnounce] not IsAnnouncementEnable")
        Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.NoUsableAnnounceData)
        return
    end

    if Module.GCloudSDK:AnnounceDataValid() then
        local ret = Module.GCloudSDK:OpenAnnouncePanel(EAnnounceContentType.Emergency, self._announceAutoLoginState)
        if not ret then
            Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.NoUsableAnnounceData)
        end
    else
        Module.GCloudSDK:LoadNoticeData()
    end
end

function LoginInterface:_OnLoadAnnounceData()
    logerror("[LoginInterface][OnLoadAnnounceData]")

    local ret = Module.GCloudSDK:OpenAnnouncePanel(EAnnounceContentType.Emergency, self._announceAutoLoginState)
    if not ret then
        if self._announceAutoLoginState then
            logerror("[LoginInterface][OnLoadAnnounceData] TryAutoLogin")
            -- 没有公告就直接尝试自动登录
            self:TryAutoLogin()
        else
            Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.NoUsableAnnounceData)
        end
    end
end

-- 在进入登录界面时拉取公告信息并弹窗，随后自动登录
function LoginInterface:PopEmergencyAnnounce()
    logerror("[LoginInterface][PopEmergencyAnnounce]")

    self._announceAutoLoginState = true

    if not Module.GCloudSDK:IsAnnouncementEnable() then
        logerror("[LoginInterface][PopEmergencyAnnounce] not IsAnnouncementEnable")
        self:TryAutoLogin()
        return
    end

    if Module.GCloudSDK:AnnounceDataValid() then
        local ret = Module.GCloudSDK:OpenAnnouncePanel(EAnnounceContentType.Emergency, self._announceAutoLoginState)
        if not ret then
            -- 没有公告就直接尝试自动登录
            self:TryAutoLogin()
        end
    else
        Module.GCloudSDK:LoadNoticeData()
    end
end

function LoginInterface:HandleDiffAccount()
    logerror("[LoginInterface][HandleDiffAccount]... ")

    local UGameLogin = import "DFMGameLogin"
    local gameLoginIns = UGameLogin.Get(GetGameInstance())
    
    if Module.Login.Field:GetDirectLogin() == true then
        logerror("[LoginInterface][HandleDiffAccount] direct login ")
        Module.Login.Field:SetDirectLogin(false)
        self:_OnServiceAgreeBtnClick(true)
        local channelId = Server.SDKInfoServer:GetLaunchChannelId()
        LoginInteractLogic.GetThirdPartInfo(channelId)
        return
    end

    if gameLoginIns then
        local bNeedHandleDiffAccount = gameLoginIns:GetDiffAccountFlag()
        local openId = Facade.ConfigManager:GetString("lastOpenId", "")
        logerror("[LoginInterface][HandleDiffAccount] bNeedShowTips:",gameLoginIns.bNeedShowTips)
        if gameLoginIns.bNeedShowTips then
            -- 需要展示弹窗
            local fOnConfirmCallbackIns = CreateCallBack(function(self)
                local UGameLogin = import "DFMGameLogin"
                local gameLoginIns = UGameLogin.Get(GetGameInstance())
                if gameLoginIns then
                    gameLoginIns:HandleDiffAccount()
                end
            end, self)
            Module.GCloudSDK:ShowCommonTip(Module.Login.Config.Loc.NeedSwitchAccount, Module.Login.Config.Loc.FixConfirmBtnText , Module.Login.Config.Loc.FixCancelBtnText, false, fOnConfirmCallbackIns, nil, true)
            gameLoginIns.bNeedShowTips = false
        end
        if bNeedHandleDiffAccount and openId ~= "" then
            -- 异账号
            logerror("[LoginInterface][HandleDiffAccount]...")
            if Module.Login.Field:GetLogoutRet() == false then
                --没有调用过Logout，说明点击太早了
                logerror("[LoginInterface] re handle again...")
                gameLoginIns:HandleDiffAccount()
                Module.Login.Field:SetLogoutRet(true)
            else
                self:_OnServiceAgreeBtnClick(true)
                Module.Login:SwitchUser(true)
                gameLoginIns:SetDiffAccountFlag(false)
            end
        end

        local bNeedHandleGameCenter = gameLoginIns:GetHandleGameCenter()
        if bNeedHandleGameCenter and openId ~= "" then
            -- 游戏中心
            logerror("[LoginInterface][HandleGameCenter]...")
            gameLoginIns:HandleGameCenter()
            gameLoginIns:SetHandleGameCenter(false)
        end
    end
end

-- 紧急公告关闭时触发
function LoginInterface:_OnEmergencyAnnounceClosed()
    logerror("[LoginInterface][OnEmergencyAnnounceClosed]...")
    self:TryAutoLogin() -- 公告关闭开始尝试走自动登录，自动登录失败再看其他的逻辑 [TODO: 或许应该在这个时候再展示登录按钮]
end

function LoginInterface:_SetUIButtonEnableExtra()
    if VersionUtil.IsInReview() then
        -- 提审版本
        logerror("[LoginInterface][SetUIButtonEnableExtra] IsInReview...")
        self._wtAnnounceBtn:Collapsed()
        self._wtDebugPanel:Collapsed()
        self._wtDebugBtn:Collapsed()
        self._wtQRCodeLoginBtn:Collapsed()
        self._wtServerSelectBtn:Collapsed()
        self._wtDebugServerSelectBtn:Collapsed()
        if PLATFORM_IOS then
            self._wtCustomerServiceBtn:Collapsed()
        end
    elseif (IsMobile()) and VersionUtil.IsShipping() then
        -- 移动端
        --self._wtFixBtn:Collapsed()
        self._wtDebugPanel:Collapsed()
        self._wtDebugBtn:Collapsed()
    end
    if IsBuildRegionGA() or IsBuildRegionGlobal() then
        self._wtQRCodeLoginBtn:Collapsed()
    end
end

function LoginInterface:_SetUIButtonEnable()
    local buildTarget = VersionUtil.GetGameBuildConfiguration()
    local loginUITbale = Facade.TableManager:GetTable("LoginUI")
    if not loginUITbale then
        logerror("[LoginInterface][SetUIButtonEnable] loginUITbale is nil")
        return
    end

    local data
    for k, v in pairs(loginUITbale) do
        if loginUITbale[k].TargetBuildConfiguration == buildTarget then
            data = v
        end
    end

    if data then
        if data.bShowDevelopBtn then
             self._wtDebugBtn:SetVisibility(ESlateVisibility.Visible) 
        else
            self._wtDebugBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShowDownloadBtn then
            self._wtDownloadBtn:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtDownloadBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShowLanguageBtn then
            self._wtLanguageBtn:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtLanguageBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShowServiceBtn then
            self._wtCustomerServiceBtn:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtCustomerServiceBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShowAnnounceBtn then
            self._wtAnnounceBtn:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtAnnounceBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShowFixBtn then
            self._wtFixBtn:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtFixBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShowScanLoginBtn then
            self._wtQRCodeLoginBtn:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtQRCodeLoginBtn:SetVisibility(ESlateVisibility.Collapsed)
        end

        if data.bShopDevelopPanelBtn then
            self._wtDebugPanel:SetVisibility(ESlateVisibility.Visible)
        else
            self._wtDebugPanel:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
end

function LoginInterface:ShowLoadingAnimation(bShow)
   if PLATFORM_ANDROID or PLATFORM_IOS then
        if bShow then
            self:PlayAnimation(self.WBP_LoginInterface_loop, 0, 100, EUMGSequencePlayMode.Forward, 1, true)
        else
            self:StopAnimation(self.WBP_LoginInterface_loop)
        end
    end
end


return LoginInterface
