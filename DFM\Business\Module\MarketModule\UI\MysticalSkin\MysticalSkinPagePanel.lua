----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
--- END MODIFICATION

local MysticalSkinPagePanel = ui("MysticalSkinPagePanel")
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local MandelBrickTradeDetailPanel = require "DFM.Business.Module.MarketModule.UI.MandelBrick.MandelBrickTradeDetailPanel"
local MarketConfig = require "DFM.Business.Module.MarketModule.MarketConfig"
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local DEFAULT_REFRESH_BUTTON_CD_TIME = 10
function MysticalSkinPagePanel:Ctor()
    self._wtItemViewList = UIUtil.WndWaterfallScrollBox(self, "wtWaterFallList", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtOwnNumberText = self:Wnd("DFTextBlock_697", UITextBlock)
    self._wtPriceLineChart = self:Wnd("WBP_Market_TradeDetailPanel", MandelBrickTradeDetailPanel)
    self._wtScreenBtn = self:Wnd("WBP_CommonCheckBoxV3S1_58", DFCommonCheckButtonOnly)
    self._wtScreenBtn:Event("OnUncheckedClicked", self._OnScreenBtnClicked, self)
    self._wtScreenBtn:Event("OnCheckedClicked", self._OnScreenBtnClicked, self)
    self._wtOwnNumberText = self:Wnd("DFTextBlock_697", UITextBlock)
    self._wtBuyBtn = self:Wnd("WBP_DFCommonButtonV1S2_67", DFCommonButtonOnly)
    self._wtBuyBtn:Event("OnClicked", self._OnBuyBtnBtnClicked, self)
    self._wtBuyBtn:Event("OnDeClicked", self._OnBuyBtnBtnClicked, self)
    self._wtSellBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtSellBtn:Event("OnClicked", self._OnSellBtnClicked, self)
    self._wtSellBtn:Event("OnDeClicked", self._OnSellBtnClicked, self)
    self._wtMysticalSkinQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self._wtMysticalSkinNameText = self:Wnd("wtSkinNameText", UITextBlock)
    self._wtMysticalSkinDescriptionText = self:Wnd("_wtTextContent_1", UITextBlock)
    self._wtTransactionOpenTimeText = self:Wnd("DFTextBlock_56", UITextBlock)
    self._wtNowTotalNum = self:Wnd("DFTextBlock_82", UITextBlock)
    self._wtMyCollection = self:Wnd("WBP_DFCommonButtonV3S1", DFCommonButtonOnly)
    self._wtMyCollection:Event("OnClicked", self.OnMyCollectionBtnClicked, self)
    self._wtCollectionWorkshop = self:Wnd("WBP_DFCommonButtonV3S1_1", DFCommonButtonOnly)
    self._wtCollectionWorkshop:Event("OnClicked", self.OnCollectionWorkshopBtnClicked, self)
    self._saleDataList = {}
    self._selectedPos = 0
    self._selectedCell = nil
    self._curItemId = nil
    self._refreshCdTime = DEFAULT_REFRESH_BUTTON_CD_TIME
    self._selectedSeasons = {}
    self._selectedOwnedIds = {}
    self._selectedGradeIds = {}
    self._selectedWeaponIds = {}
end

function MysticalSkinPagePanel:OnInitExtraData(curSubPageType)
    self._curSubPageType = curSubPageType
end

function MysticalSkinPagePanel:OnActivate()
    loginfo('SubUIOwnerPack MysticalSkinPagePanel:OnActivate()')
end

function MysticalSkinPagePanel:OnDeactivate()
    loginfo('SubUIOwnerPack MysticalSkinPagePanel:OnDeactivate()')
end

function MysticalSkinPagePanel:OnShow()
    -- todo 目前框架控制会进入两次OnShow：
    self:StartTimer()
    self:RefreshMarketIsOpen()
    -- MarketConfig.Events.evtOpenMysticalSkinUI:Invoke()
end

function MysticalSkinPagePanel:OnHide()
    self:StopTimer()
    self:StopOpenMarketTimer()
    self:StopCloseMarketTimer()
end

function MysticalSkinPagePanel:OnShowBegin()
    MarketConfig.Events.evtMarketSkinPageOnShowBegin:Invoke()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketSaleListChanged, self.RefreshRightPanel, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.RefreshModel, self)
	--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION
end

function MysticalSkinPagePanel:OnHideBegin()
    MarketConfig.Events.evtMarketSkinPageOnHideBegin:Invoke()
    self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded)
    self:RemoveLuaEvent(Server.MarketServer.Events.evtMarketSaleListChanged)
    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION
end

function MysticalSkinPagePanel:OnOpen()
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketLoopSaleListChanged, self.RefreshTransactionInfoPanel, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketTypeListChanged, self.RefreshItemViewList, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMysticalSkinTypeListChanged, self.RefreshMysticalSkinList, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMysticalPendantTypeListChanged, self.RefreshMysticalPendantList, self)
    self:AddLuaEvent(Server.MarketServer.Events.evtMarketPullOffGoodSucceed, self.RefreshOwnNumber, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtFetchCollectionData, self.RefreshOwnNumber, self)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self.OnRelayConnected, self)  --断线重连
    self:RefreshItemViewList()
    self:RefreshOwnNumber() -- 初始状态先刷新一次市场开放状态
end

function MysticalSkinPagePanel:OnClose()
    Facade.UIManager:ClearSubUIByParent(self, self._wtMandelBrickScrollBox)
    self:RemoveAllLuaEvent()
end

function MysticalSkinPagePanel:Init()

end

function MysticalSkinPagePanel:InitSelectedPos()
    if self._saleDataList and not table.isempty(self._saleDataList) then
        self._selectedPos = 1
    else
        self._selectedPos = 0
    end
    self._selectedCell = nil
end

function MysticalSkinPagePanel:RefreshItemViewList()
    if self._curSubPageType == EMarketSubPageType.MysticalSkin then
        self:RefreshMysticalSkinList()
    elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
        self:RefreshMysticalPendantList()
    end
end

function MysticalSkinPagePanel:RefreshMysticalSkinList(res)
    if self._curSubPageType == EMarketSubPageType.MysticalSkin then
        -- 数据准备
        local mysticalSkinSaleList = {}
        if res and res.type_lists then
            for _, v in ipairs(res.type_lists) do
                mysticalSkinSaleList[v.prop_id] = v
            end
        else
            mysticalSkinSaleList = Server.MarketServer:GetMysticalSkinSaleList()
        end
        local saleListsCategories = MarketLogic.ClassifyAndSortSaleLists(mysticalSkinSaleList)
        self._saleDataList = {}
        for categoryId, saleLists in ipairs(saleListsCategories) do
            -- local marketTabInfo = Module.Market.Field:GetMarketTabInfo()
            -- local categoryName = ""
            -- for _, subTabInfo in ipairs(marketTabInfo[EMarketSubPageType.MysticalSkin].subTabList[3].subTabContentList) do -- 滚动框内只有武器类别显示
            --     if subTabInfo.subTabContentId == categoryId then
            --         categoryName = subTabInfo.subTabContentName
            --         break
            --     end
            -- end
            -- local bListHead = true
            for _, saleList in ipairs(saleLists) do
                for _, saleInfoMap in ipairs(saleList) do
                    for itemId, saleInfo in pairs(saleInfoMap) do
                        local saleData = {
                            -- categoryName = bListHead and categoryName or nil,
                            itemId = itemId,
                            saleInfo = saleInfo
                        }
                        table.insert(self._saleDataList, saleData)
                        -- bListHead = false
                    end
                end
            end
        end
        self:InitSelectedPos()
        -- 刷新滚动框
        self._wtItemViewList:RefreshAllItems()
        if self._saleDataList and #self._saleDataList > 0 then
            self._curItemId = self._saleDataList[1].itemId
            Server.MarketServer:FetchSaleList(self._curItemId)
        end
    end
end

function MysticalSkinPagePanel:RefreshMysticalPendantList(res)
    if self._curSubPageType == EMarketSubPageType.MysticalPendant then
        -- 数据准备
        local mysticalPendantSaleList = {}
        if res and res.type_lists then
            for _, v in ipairs(res.type_lists) do
                mysticalPendantSaleList[v.prop_id] = v
            end
        else
            mysticalPendantSaleList = Server.MarketServer:GetMysticalPendantSaleList()
        end
        self._saleDataList = {}
        for itemId, saleInfo in pairs(mysticalPendantSaleList) do
            local saleData = {
                itemId = itemId,
                saleInfo = saleInfo
            }
            table.insert(self._saleDataList, saleData)
        end
        table.sort(self._saleDataList, function(a, b)
            return ItemConfigTool.GetItemQuality(a.itemId) > ItemConfigTool.GetItemQuality(b.itemId)
        end)
        self:InitSelectedPos()
        -- 刷新滚动框
        self._wtItemViewList:RefreshAllItems()
        if self._saleDataList and #self._saleDataList > 0 then
            self._curItemId = self._saleDataList[1].itemId
            Server.MarketServer:FetchSaleList(self._curItemId)
        end
    end
end

function MysticalSkinPagePanel:_OnGetItemCount()
    return self._saleDataList and #self._saleDataList or 0
end

function MysticalSkinPagePanel:_OnProcessItemWidget(position, itemWidget)
    if self._saleDataList then
        local saleData = self._saleDataList[position]
        local OnItemClick = CreateCallBack(function(self, itemId)
            self:ItemWidgetClickedCallback(itemId, itemWidget, position)
        end, self)
        itemWidget:Init(saleData.categoryName, saleData.itemId, saleData.saleInfo, OnItemClick)
        if self._selectedPos and position == self._selectedPos then
            self._selectedCell = itemWidget
            itemWidget:SetSelected(itemWidget.item, true)
        else
            itemWidget:SetSelected(itemWidget.item, false)
        end
    end
end

function MysticalSkinPagePanel:ItemWidgetClickedCallback(itemId, itemWidget, position)
    if self._selectedPos == position then
        return
    end
    if itemId then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, false)
        end
        self._selectedPos = position
        self._selectedCell = itemWidget
        if self._selectedCell then
            self._selectedCell:SetSelected(self._selectedCell.item, true)
        end
        Server.MarketServer:FetchSaleList(itemId)
    end
end

function MysticalSkinPagePanel:RefreshRightPanel(saleListInfo)
    if saleListInfo then
        local itemType = Server.MarketServer:GetMarketItemType(saleListInfo.prop_id)
        if itemType == self._curSubPageType then
            self._curItemId = saleListInfo.prop_id
            self:RefreshMysticalSkinDetail(saleListInfo)
            self:RefreshDataChart(saleListInfo)
            self:RefreshOwnNumber()
            self:RefreshModel()
        end
    end
end

function MysticalSkinPagePanel:RefreshTransactionInfoPanel(saleListInfo)
    if saleListInfo then
        local itemType = Server.MarketServer:GetMarketItemType(saleListInfo.prop_id)
        if itemType == self._curSubPageType then
            self:RefreshMysticalSkinDetail(saleListInfo)
            self:RefreshDataChart(saleListInfo)
        end
    end
end

function MysticalSkinPagePanel:RefreshMysticalSkinDetail(saleListInfo)
    if self._curItemId then
        local itemQuality
        if self._curSubPageType == EMarketSubPageType.MysticalSkin then
            itemQuality = ItemHelperTool.GetQualityTypeById(self._curItemId)
        elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
            itemQuality = ItemConfigTool.GetItemQuality(self._curItemId)
        end
        if itemQuality then
            local bAutoResize = false
            self._wtMysticalSkinQualityIcon:AsyncSetImagePath(Module.Hero.Config.QualityIconMapping[itemQuality], bAutoResize)
            self._wtMysticalSkinQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(itemQuality))
        end
        local item = ItemBase:NewIns(self._curItemId)
        if item.name then
            self._wtMysticalSkinNameText:SetText(item.name)
        end
        if item.description then
            self._wtMysticalSkinDescriptionText:SetText(tostring(item.description))
            if tostring(item.description) == "" then
                self._wtMysticalSkinDescriptionText:Collapsed()
            else
                self._wtMysticalSkinDescriptionText:SelfHitTestInvisible()
            end
        end
        if self._wtNowTotalNum and saleListInfo and saleListInfo.cur_num then
            if saleListInfo.cur_num <= 999 then
                self._wtNowTotalNum:SetText(string.format(MarketConfig.Loc.NowMarketTotalNum, saleListInfo.cur_num))
            else
                self._wtNowTotalNum:SetText(string.format(MarketConfig.Loc.NowMarketTotalNum, MarketConfig.Loc.TooManyNum))
            end
        end
        if self._wtTransactionOpenTimeText then
            local function secondsToTime(seconds)
                local hours = math.floor(seconds / 3600)
                local minutes = math.floor((seconds % 3600) / 60)
                local bNextDay = hours > 24
                if hours > 24 then
                    hours = hours - 24
                end
                return hours, minutes, bNextDay
            end
            local marketOpenTime = Server.MarketServer:GetMarketOpenTime()
            local marketCloseTime = Server.MarketServer:GetMarketCloseTime()
            if marketOpenTime == 0 and marketCloseTime == 0 then
                self._wtTransactionOpenTimeText:Collapsed()
            else
                local openHours, openMinutes = secondsToTime(marketOpenTime)
                local closeHours, closeMinutes, bNextDay = secondsToTime(marketCloseTime)
                local bNextDayText = bNextDay and MarketConfig.Loc.NextDay or ""
                self._wtTransactionOpenTimeText:SetText(StringUtil.Key2StrFormat(string.format(MarketConfig.Loc.TransactionOpenTime, openHours, openMinutes, closeHours, closeMinutes), {["NextDay"] = bNextDayText}))
                self._wtTransactionOpenTimeText:SelfHitTestInvisible()
            end
        end
    end
end

function MysticalSkinPagePanel:RefreshDataChart(saleListInfo)
    local itemType = Server.MarketServer:GetMarketItemType(saleListInfo.prop_id)
    if itemType == self._curSubPageType then
        -- self:StartTimer()
        if self._wtPriceLineChart then
            self._wtPriceLineChart:Init(saleListInfo)
        end
    end
end

function MysticalSkinPagePanel:RefreshOwnNumber()
    if self._curItemId then
        local ownNumber = MarketLogic.GetMarketItemNumById(self._curItemId)
        if self._curSubPageType == EMarketSubPageType.MysticalSkin then   
            self._wtOwnNumberText:SetText(string.format(MarketConfig.Loc.CurOwnMysticalSkinNumber, ownNumber))
        elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
            self._wtOwnNumberText:SetText(string.format(MarketConfig.Loc.CurOwnMysticalPendantNumber, ownNumber))
        end
        if ownNumber > 0 and MarketLogic.JudgeMarketIsOpen() then
            self._wtSellBtn:SetIsEnabledStyle(true)
        else
            self._wtSellBtn:SetIsEnabledStyle(false)
        end
    end
end

function MysticalSkinPagePanel:StartTimer()
    self:StopTimer()
    self._timerHandle = Timer:NewIns(1, 0)
    self._timerHandle:AddListener(self.RefreshSaleList, self)
    self._timerHandle:Start()
end

function MysticalSkinPagePanel:StopTimer()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end

function MysticalSkinPagePanel:RefreshSaleList()
    if self._curItemId then
        if self._refreshCdTime > 0 then
            self._refreshCdTime = self._refreshCdTime - 1
        else
            self._refreshCdTime = DEFAULT_REFRESH_BUTTON_CD_TIME
            Server.MarketServer:FetchSaleList(self._curItemId, true)
        end
    end
end

function MysticalSkinPagePanel:_CreateCommonItemViewByItem(itembase)
	local itemFeature = itembase:GetFeature()
	local featureType = itemFeature:GetFeatureType()
	if featureType == EFeatureType.Weapon then
		local itemUI = Module.CommonWidget:CreateIVCommonItemTemplateBindOwnerBySize(self, 336, 168)
		itemUI:EnableComponent(EComp.ItemQuality, false)
		return itemUI
	end
	return Module.CommonWidget:CreateIVCommonItemTemplateBindOwnerBySize(self, 168, 168)
end

function MysticalSkinPagePanel:RefreshModel(curSubStageType)
    if self._curItemId then
        local mysticalSkinItem = MarketLogic.CreateMysticalSkinItem(self._curItemId)
        if self._curSubPageType == EMarketSubPageType.MysticalSkin then
            if not curSubStageType or curSubStageType == ESubStage.HallCollectionNew then
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayType", "Weapon")
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
                if self._curItemId then
                    if isvalid(mysticalSkinItem) then
                        local weaponDesc = WeaponHelperTool.GetWeaponDescriptionFromSkinID(mysticalSkinItem.id)
                        mysticalSkinItem:SetRawDescObj(weaponDesc)
                        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeapon", weaponDesc, mysticalSkinItem.id, false, true)
                    end
                end
            end
        elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
            if not curSubStageType or curSubStageType == ESubStage.CollectionHanging then
                if isvalid(mysticalSkinItem) then
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetEnableTrans",false)
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetIsAdapter", true)
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayWeapon", mysticalSkinItem:GetRawDescObj(), mysticalSkinItem.id, 
                    false, false)
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "DestroyWatch")
                else
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "ResetDisplayItem")
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "ResetWeapon")
                end
                if IsHD() then 
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayType","Main")
                else
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.CollectionHanging, "SetDisplayType","Main_Mobile")
                end
            end
        end
    end
end

function MysticalSkinPagePanel:_OnDetailRewardsBtnClicked()
	local function fOnClose()
		Module.ItemDetail.Field:SetPrizePop(false)
	end
    local function fOnLoad()
		Module.ItemDetail.Field:SetPrizePop(true)
	end
	if not Facade.GameFlowManager:CheckIsInFrontEnd() then
		Module.CommonTips:ShowSimpleTip(Module.ItemDetail.Config.Loc.contentGiftButtonText)
		return
	end
	Module.Store.OpenStorePrizePopPanel(fOnLoad, fOnClose, self._curItemId)
	local socialMainHandle = Module.Mail:GetSocialMainUIins()
	if socialMainHandle then
		Facade.UIManager:CloseUI(socialMainHandle)
	end
end

function MysticalSkinPagePanel:_OnBuyBtnBtnClicked()
    if self._curItemId then
        if MarketLogic.JudgeMarketIsOpen() then
            Module.Market:JumpToMysticalSkinBuyPage(self._curSubPageType, self._curItemId)
        else
            Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MarketIsNotOpen)
        end
    end
end

function MysticalSkinPagePanel:_OnSellBtnClicked()
    if self._curItemId then
        if MarketLogic.JudgeMarketIsOpen() then
            local ownNumber = MarketLogic.GetMarketItemNumById(self._curItemId)
            if self._curSubPageType == EMarketSubPageType.MysticalSkin then
                if ownNumber <= 0 then
                    Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MysticalSkinInsUnowned)
                    return
                end
            elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
                if ownNumber <= 0 then
                    Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MysticalPendantInsUnowned)
                    return
                end
            end
            Module.Market:JumpToMysticalSkinSellPage(self._curItemId)
        else
            Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MarketIsNotOpen)
        end
    end
end

function MysticalSkinPagePanel:_OnScreenBtnClicked()
    -- local function onUILoaded(ui)
    -- end
    if self._wtScreenBtn then
        self._wtScreenBtn:SetIsChecked(false)
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.MysticalSkinHomepageScreening, onUILoaded, nil,
     self._curSubPageType, CreateCallBack(self.ScreeningItemViewList, self), CreateCallBack(self.OnScreeningPanelClose, self),
     self._selectedSeasons, self._selectedOwnedIds, self._selectedGradeIds, self._selectedWeaponIds)
end

function MysticalSkinPagePanel:ScreeningItemViewList(screeningParam)

end

function MysticalSkinPagePanel:OnScreeningPanelClose()
    if self._wtScreenBtn then
        if table.isempty(self._selectedSeasons) and table.isempty(self._selectedOwnedIds) and table.isempty(self._selectedGradeIds) and table.isempty(self._selectedWeaponIds) then
            self._wtScreenBtn:SetMainTitle(Module.Market.Config.Loc.Screening)
        else
            self._wtScreenBtn:SetMainTitle(Module.Market.Config.Loc.InScreening)
        end
    end
end

function MysticalSkinPagePanel:RefreshMarketIsOpen()
    local marketOpenTimePeriods = MarketLogic.GetMarketOpenTimePeriods()
    if marketOpenTimePeriods and not table.isempty(marketOpenTimePeriods) then
        if #marketOpenTimePeriods == 1 then -- 单个时间段
            local timePeriod = marketOpenTimePeriods[1]
            if timePeriod[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod[2] then
                self:OpenMarket(timePeriod[2] - Facade.ClockManager:GetLocalTimestamp())
            elseif Facade.ClockManager:GetLocalTimestamp() < timePeriod[1] then
                self:CloseMarket(timePeriod[1] - Facade.ClockManager:GetLocalTimestamp())
            elseif Facade.ClockManager:GetLocalTimestamp() > timePeriod[2] then
                self:CloseMarket(timePeriod[1] + 24 * 60 * 60 - Facade.ClockManager:GetLocalTimestamp())
            end
        elseif #marketOpenTimePeriods == 2 then -- 两个时间段
            local timePeriod1 = marketOpenTimePeriods[1]
            local timePeriod2 = marketOpenTimePeriods[2]
            if Facade.ClockManager:GetLocalTimestamp() < timePeriod1[1] then
                self:CloseMarket(timePeriod1[1] - Facade.ClockManager:GetLocalTimestamp())
            elseif timePeriod1[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod1[2] then
                self:OpenMarket(timePeriod1[2] - Facade.ClockManager:GetLocalTimestamp())
            elseif timePeriod1[2] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod2[1] then
                self:CloseMarket(timePeriod2[1] - Facade.ClockManager:GetLocalTimestamp())
            elseif timePeriod2[1] <= Facade.ClockManager:GetLocalTimestamp() and Facade.ClockManager:GetLocalTimestamp() <= timePeriod2[2] then
                self:OpenMarket(timePeriod2[2] - Facade.ClockManager:GetLocalTimestamp())
            elseif Facade.ClockManager:GetLocalTimestamp() > timePeriod2[2] then
                self:CloseMarket(timePeriod1[1] + 24 * 60 * 60 - Facade.ClockManager:GetLocalTimestamp())
            end
        end
    end
end

function MysticalSkinPagePanel:CloseMarket(timeToOpen)
    self._wtBuyBtn:SetIsEnabledStyle(false)
    self._wtSellBtn:SetIsEnabledStyle(false)
    self:RefreshOwnNumber()
    self:StopOpenMarketTimer()
    self._OpenMarketTimerHandle = Timer.DelayCall(timeToOpen + 1, function ()
        self:RefreshMarketIsOpen()
    end, self)
end

function MysticalSkinPagePanel:StopOpenMarketTimer()
    if self._OpenMarketTimerHandle then
        Timer.CancelDelay(self._OpenMarketTimerHandle)
        self._OpenMarketTimerHandle = nil
    end
end

function MysticalSkinPagePanel:OpenMarket(timeToClose)
    self._wtBuyBtn:SetIsEnabledStyle(true)
    self._wtSellBtn:SetIsEnabledStyle(true)
    self:RefreshOwnNumber()
    self:StopCloseMarketTimer()
    self._CloseMarketTimerHandle = Timer.DelayCall(timeToClose + 1, function ()
        self:RefreshMarketIsOpen()
    end, self)
end

function MysticalSkinPagePanel:StopCloseMarketTimer()
    if self._CloseMarketTimerHandle then
        Timer.CancelDelay(self._CloseMarketTimerHandle)
        self._CloseMarketTimerHandle = nil
    end
end

function MysticalSkinPagePanel:OnMyCollectionBtnClicked()
    if MarketLogic.JudgeMarketIsOpen() then
        Module.Market:JumpToMysticalSkinBuyPage(self._curSubPageType)
    else
        Module.CommonTips:ShowSimpleTip(Module.Market.Config.Loc.MarketIsNotOpen)
    end
end

function MysticalSkinPagePanel:OnCollectionWorkshopBtnClicked()
    Module.Collection:ShowMainPanel(6)
end

function MysticalSkinPagePanel:OnRelayConnected()
    logwarning("MysticalSkinPagePanel:OnRelayConnected")
    if self._curSubPageType == EMarketSubPageType.MysticalSkin then
        self:RefreshMysticalSkinList()
    elseif self._curSubPageType == EMarketSubPageType.MysticalPendant then
        self:RefreshMysticalPendantList()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MysticalSkinPagePanel:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    if not self:IsVisible() then
        return
    end

    -- 购买按键响应
    if not self._buyHandle then
        self._buyHandle = self:AddInputActionBinding("Common_ButtonLeft", EInputEvent.IE_Pressed, self._OnBuyBtnBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtBuyBtn then
            self._wtBuyBtn:SetDisplayInputAction("Common_ButtonLeft", true, nil, true)
        end
    end

    -- 售出按键响应
    if not self._sellHandle then
        self._sellHandle = self:AddInputActionBinding("Common_ButtonTop", EInputEvent.IE_Pressed, self._OnSellBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtSellBtn then
            self._wtSellBtn:SetDisplayInputAction("Common_ButtonTop", true, nil, true)
        end
    end

    if not self._screenBtnHandle then
        self._screenBtnHandle = self:AddInputActionBinding("MysticalSkinMainScreening", EInputEvent.IE_Pressed, self._OnScreenBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtScreenBtn then
            self._wtScreenBtn:SetDisplayInputAction("MysticalSkinMainScreening", true, 0, true)
        end
    end

    if not self._myCollectionHandle then
        self._myCollectionHandle = self:AddInputActionBinding("MysticalSkinMyCollection", EInputEvent.IE_Pressed, self.OnMyCollectionBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtMyCollection then
            self._wtMyCollection:SetDisplayInputActionWithLongPress(self._myCollectionHandle, self, "MysticalSkinMyCollection", true, nil, true)
        end
    end

    if not self._workshopHandle then
        self._workshopHandle = self:AddInputActionBinding("MysticalSkinWorkshop", EInputEvent.IE_Pressed, self.OnCollectionWorkshopBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        if self._wtCollectionWorkshop then
            self._wtCollectionWorkshop:SetDisplayInputActionWithLongPress(self._workshopHandle, self, "MysticalSkinWorkshop", true, nil, true)
        end
    end

    -- 注册导航
    if not self._NavGroup_ItemViewList then
        self._NavGroup_ItemViewList = WidgetUtil.RegisterNavigationGroup(self._wtItemViewList, self, "Hittest")
        if self._NavGroup_ItemViewList then
            self._NavGroup_ItemViewList:AddNavWidgetToArray(self._wtItemViewList)
            self._NavGroup_ItemViewList:SetScrollRecipient(self._wtItemViewList)
            self._NavGroup_ItemViewList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_ItemViewList)
        end
    end
end

function MysticalSkinPagePanel:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    if self._buyHandle then
        self:RemoveInputActionBinding(self._buyHandle)
        self._buyHandle = nil
    end

    if self._sellHandle then
        self:RemoveInputActionBinding(self._sellHandle)
        self._sellHandle = nil
    end

    if self._screenBtnHandle then
        self:RemoveInputActionBinding(self._screenBtnHandle)
        self._screenBtnHandle = nil
    end

    if self._myCollectionHandle then
        self:RemoveInputActionBinding(self._myCollectionHandle)
        self._myCollectionHandle = nil
    end

    if self._workshopHandle then
        self:RemoveInputActionBinding(self._workshopHandle)
        self._workshopHandle = nil
    end

    if self._NavGroup_ItemViewList then
        WidgetUtil.RemoveNavigationGroup(self)
        self._NavGroup_ItemViewList = nil
    end
end
--- END MODIFICATION

return MysticalSkinPagePanel