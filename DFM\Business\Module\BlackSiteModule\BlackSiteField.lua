----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBlackSite)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class BlackSiteField
local BlackSiteField = class("BlackSiteField", require "DFM.YxFramework.Managers.Module.FieldBase")
local WeaponAssemblyTool = require("DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool")

require "DFM.Business.DataStruct.BlackSiteStruct.BlackSiteProductionLine"

function BlackSiteField:Ctor()
    self._curWeeks = nil
    self._curTabIdx = BlackSiteDefine.EBlackSiteEntranceType.Construct
    self._itemBase = {}
    self._productionLine = {}
    self._allProduceList = {}
    self._itemUsageAndSourceList = {}
    self._lastSelection = {}
    self._lastCollectFormulaTimestamp = 0
    self._lastFocusWidget = nil
    self._lastProduceFocusWidget = nil
    self._isEnteringCollectionRoom = nil
    self._btnCDFinishList = {}
    self._deviceOpenCacheList = {}
end

function BlackSiteField:ResetCache()
    self._curWeeks = nil
    self._itemBase = {}
    self._productionLine = {}
    self._allProduceList = {}
    self._itemUsageAndSourceList = {}
    self._lastSelection = {}
    self._lastFocusWidget = nil
    self._lastProduceFocusWidget = nil
    self._isEnteringCollectionRoom = nil
    self._btnCDFinishList = {}
    self._deviceOpenCacheList = {}
    logerror("BlackSiteField:ResetCache()")
end

function BlackSiteField:GetItemBase(id, num)
    return self._itemBase[id] and (num and self._itemBase[id]:ClientSetNum(num) or self._itemBase[id]) or self:CacheItemBase(id, num)
end

function BlackSiteField:CacheItemBase(id, num)
    self._itemBase[id] = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(id, num)

    if not self._itemBase[id] then
        self._itemBase[id] = ItemBase:NewIns(id, num)
    end

    return self._itemBase[id]
end

function BlackSiteField:GetAllProduceList(data)
    return self._allProduceList[data.Id] or self:CacheAllProduceList(data)
end

function BlackSiteField:CacheAllProduceList(data)
    self._allProduceList[data.Id] = self:GetProductionLine(data)
    return self._allProduceList[data.Id]
end

function BlackSiteField:ClearAllProduceList()
    self._allProduceList = {}
end

function BlackSiteField:GetProductionLine(data)
    return self._productionLine[data.Id] and self._productionLine[data.Id]:UpdateData(data) or self:CacheProductionLine(data)
end

function BlackSiteField:CacheProductionLine(data)
    self._productionLine[data.Id] = BlackSiteProductionLine:NewIns(data)

    local productList = self._productionLine[data.Id]:GetProductList()

    -- azhengzheng:提前缓存 防止打开特勤处制造界面时卡顿
    if productList and productList[1] and productList[1].Id then
        self._itemBase[productList[1].Id] = self:GetItemBase(productList[1].Id)
    end

    return self._productionLine[data.Id]
end

function BlackSiteField:SetLastSelection(deviceId, formulaId)
    self._lastSelection[deviceId] = formulaId
end

function BlackSiteField:GetLastSelection(deviceId)
    return self._lastSelection[deviceId]
end

function BlackSiteField:GetItemUsageAndSourceList(itemId)
    if not itemId then
        logerror("azhengzheng:Get item usage and source list error,item ID is nil")
        local errorRes = {}
        errorRes.UsageList = {}
        errorRes.SourceList = {}
        return errorRes
    end

    return self._itemUsageAndSourceList[itemId] or self:CacheItemUsageAndSourceList(itemId)
end

function BlackSiteField:CacheItemUsageAndSourceList(itemId)
    self._itemUsageAndSourceList[itemId] = {}
    self._itemUsageAndSourceList[itemId].UsageList = {}
    self._itemUsageAndSourceList[itemId].SourceList = {}
    local itemName = self:GetItemBase(itemId).name
    local allDeviceData = Server.BlackSiteServer:GetAllDeviceData()

    for _, deviceData in pairs(allDeviceData) do
        if deviceData:GetCanProduce() then
            local replacedIDList = {}
            local unlockFormulaIDList = {}
            local deviceName = deviceData:GetName()
            local produceList = deviceData:GetProduceList()

            -- azhengzheng:对已解锁的配方进行去重
            for _, produce in pairs(produceList) do
                replacedIDList[produce:GetReplacedId()] = true
            end

            for _, produce in pairs(produceList) do
                local formulaId = produce:GetFormulaId()
                unlockFormulaIDList[formulaId] = true

                if not replacedIDList[formulaId] and produce:CheckWithinTheTimeFrame() then
                    local level = produce:GetLevel()
                    local productList = produce:GetProductList()
                    local materialList = produce:GetMaterialList()
                    local isMaterialEnough = produce:IsMaterialEnough()

                    for _, product in pairs(productList) do
                        if itemId == product.Id then
                            local sourceInfo = {}
                            sourceInfo[formulaId] = {}
                            sourceInfo[formulaId].name = itemName
                            sourceInfo[formulaId].bEnough = isMaterialEnough
                            sourceInfo[formulaId].bUnlock = true
                            sourceInfo[formulaId].deviceName = deviceName
                            sourceInfo[formulaId].Level = level
                            sourceInfo[formulaId].materialList = {}

                            for _, material in pairs(materialList) do
                                table.insert(sourceInfo[formulaId].materialList, {itemId = material.Id})
                            end

                            table.insert(self._itemUsageAndSourceList[itemId].SourceList, sourceInfo)
                            break
                        end
                    end

                    for _, material in pairs(materialList) do
                        if itemId == material.Id then
                            local usageInfo = {}
                            usageInfo[formulaId] = {}
                            usageInfo[formulaId].name = self:GetItemBase(productList[1].Id).name
                            usageInfo[formulaId].bEnough = isMaterialEnough
                            usageInfo[formulaId].bUnlock = true
                            usageInfo[formulaId].deviceName = deviceName
                            usageInfo[formulaId].Level = level
                            usageInfo[formulaId].useMaterialList = {}

                            for _, item in pairs(materialList) do
                                table.insert(usageInfo[formulaId].useMaterialList, {itemId = item.Id})
                            end

                            table.insert(self._itemUsageAndSourceList[itemId].UsageList, usageInfo)
                            break
                        end
                    end
                end
            end

            -- azhengzheng:找出未解锁的配方
            if not deviceData:IsMaxLevel() then
                local maxLevelProduceList = deviceData:GetMaxLevelProduceList()

                for _, produce in pairs(maxLevelProduceList) do
                    local formulaId = produce:GetFormulaId()

                    if not unlockFormulaIDList[formulaId] and produce:GetReplacedId() == 0 and produce:CheckWithinTheTimeFrame() then
                        local level = produce:GetLevel()
                        local productList = produce:GetProductList()
                        local materialList = produce:GetMaterialList()
                        local isMaterialEnough = produce:IsMaterialEnough()

                        for _, product in pairs(productList) do
                            if itemId == product.Id then
                                local sourceInfo = {}
                                sourceInfo[formulaId] = {}
                                sourceInfo[formulaId].name = itemName
                                sourceInfo[formulaId].bEnough = isMaterialEnough
                                sourceInfo[formulaId].bUnlock = false
                                sourceInfo[formulaId].deviceName = deviceName
                                sourceInfo[formulaId].Level = level
                                sourceInfo[formulaId].materialList = {}

                                for _, material in pairs(materialList) do
                                    table.insert(sourceInfo[formulaId].materialList, {itemId = material.Id})
                                end

                                table.insert(self._itemUsageAndSourceList[itemId].SourceList, sourceInfo)
                                break
                            end
                        end

                        for _, material in pairs(materialList) do
                            if itemId == material.Id then
                                local usageInfo = {}
                                usageInfo[formulaId] = {}
                                usageInfo[formulaId].name = self:GetItemBase(productList[1].Id).name
                                usageInfo[formulaId].bEnough = isMaterialEnough
                                usageInfo[formulaId].bUnlock = false
                                usageInfo[formulaId].deviceName = deviceName
                                usageInfo[formulaId].Level = level
                                usageInfo[formulaId].useMaterialList = {}

                                for _, item in pairs(materialList) do
                                    table.insert(usageInfo[formulaId].useMaterialList, {itemId = item.Id})
                                end

                                table.insert(self._itemUsageAndSourceList[itemId].UsageList, usageInfo)
                                break
                            end
                        end
                    end
                end
            end
        end
    end

    return self._itemUsageAndSourceList[itemId]
end

function BlackSiteField:ClearItemUsageAndSourceList()
    self._itemUsageAndSourceList = {}
end

function BlackSiteField:GetCurWeeks()
    if self._curWeeks then
        return self._curWeeks
    end

    return self:SetCurWeeks()
end

function BlackSiteField:SetCurWeeks()
    local localTimestamp = Facade.ClockManager:GetLocalTimestamp()

    if not localTimestamp then
        logerror("azhengzheng:local timestamp is nil!")
        return 0
    end

    self._curWeeks = localTimestamp < 345600 and 1 or math.floor((localTimestamp - 345600) / 604800) + 2
    return self._curWeeks
end

function BlackSiteField:SetCurTabIdx(idx)
    self._curTabIdx = idx or self._curTabIdx
end

function BlackSiteField:GetCurTabIdx()
    return self._curTabIdx
end

function BlackSiteField:CheckFormulaCollectBtnCDFinish()
    local localTimestamp = Facade.ClockManager:GetLocalTimestamp()

    if math.abs(localTimestamp - self._lastCollectFormulaTimestamp) >= BlackSiteDefine.EBlackSiteBtnCD.FormulaCollect then
        self._lastCollectFormulaTimestamp = localTimestamp
        return true
    end

    return false
end

function BlackSiteField:SetLastFocusWidget(widget)
    self._lastFocusWidget = widget
end

function BlackSiteField:GetLastFocusWidget()
    local lastFocusWidget = self._lastFocusWidget
    self._lastFocusWidget = nil
    return lastFocusWidget
end

function BlackSiteField:SetLastProduceFocusWidget(widget)
    self._lastProduceFocusWidget = widget
end

function BlackSiteField:GetLastProduceFocusWidget()
    local lastProduceFocusWidget = self._lastProduceFocusWidget
    self._lastProduceFocusWidget = nil
    return lastProduceFocusWidget
end

function BlackSiteField:SetIsEnteringCollectionRoom(isEntering)
    self._isEnteringCollectionRoom = isEntering
end

function BlackSiteField:GetIsEnteringCollectionRoom()
    return self._isEnteringCollectionRoom
end

function BlackSiteField:CheckBtnCDFinishByType(type, tip)
    if not type or not BlackSiteDefine.EBlackSiteBtnCD[type] then
        if tip then
            Module.CommonTips:ShowSimpleTip(tip)
        end

        return false
    end

    local localTimestamp = Facade.ClockManager:GetLocalTimestamp()

    if not self._btnCDFinishList[type] or localTimestamp - self._btnCDFinishList[type] >= BlackSiteDefine.EBlackSiteBtnCD[type] then
        self._btnCDFinishList[type] = localTimestamp
        return true
    end

    if tip then
        Module.CommonTips:ShowSimpleTip(tip)
    end

    return false
end

function BlackSiteField:GetDeviceOpenCacheByID(deviceID)
    if not deviceID then
        return false
    end

    if self._deviceOpenCacheList[deviceID] then
        return true
    end

    local deviceData = Module.BlackSite:GetDeviceData(deviceID)

    if deviceData and deviceData:GetBShowInMain() ~= 0 then
        self._deviceOpenCacheList[deviceID] = true
    end

    return self._deviceOpenCacheList[deviceID]
end

return BlackSiteField