----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SystemSettingHDGamePlayPanel
local SystemSettingHDGamePlayPanel = ui("SystemSettingHDGamePlayPanel")
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SystemSettingPanelSortPanelHD = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingPanelSortPanelHD"

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

function SystemSettingHDGamePlayPanel:Ctor()
    self:_BindWidget()
    --self:_InitOrderList()
    --BEGIN MODIFICATION @ VIRTUOS :
    self._OrderListOpened = false
    --END MODIFICATION
end

function SystemSettingHDGamePlayPanel:_BindWidget()
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
    self._wtSortOrderList = self:Wnd("wtSortOrderList", UIWidgetBase)
    self._wtSortlistExpandable = self:Wnd("wtSortlistExpandable", UIWidgetBase)
    self._wtRootScrollBox = self:Wnd("wtRootScrollBox", UILightWidget)
    self._wtItemPanel = self:Wnd("wtItemPanel", UILightWidget)
    self._wtAutoNewlineBtn = self:Wnd("WBP_SetUpComponent_Check_3", UIWidgetBase)
    self._wtSortEveryEnterBtn = self:Wnd("WBP_SetUpComponent_Check_4", UIWidgetBase)
    self._wtDrugDurationSlider = self:Wnd("WBP_SetUpComponent", UIWidgetBase)
    self._wtSecondLanguageForPeakGameBtn = self:Wnd("SecondLanguageForPeakGame", UIWidgetBase)
    self._wtSecondLanguageTxt = self._wtSecondLanguageForPeakGameBtn:Wnd("TextBlock", UITextBlock)
    self._wtSecondLanguageForPeakGameBtn:Wnd("_wtJumpBtn",UIButton):Event("OnClicked",self._OpenSecondLanguage,self)
    -- BEGIN MODIFICATION - VIRTUOS
    self._wtInGameTextChatSwitcher = self:Wnd("wtInGameTextChatSwitcher",UIWidgetBase)
    self._wtSortListSlot = self._wtSortlistExpandable:Wnd("DFNamedSlot_97", UIWidgetBase)
    self._wtSetUpComponent_Cycle = self:Wnd("WBP_SetUpComponent_Cycle", UIWidgetBase)
    self._wtSetUpComponent_Check_System_2 = self:Wnd("Check_System_2", UIWidgetBase)
    self._wtSetUpComponent_ListTitle_Crossplay = self:Wnd("WBP_SetUpComponent_ListTitle_Crossplay", UIWidgetBase)
    self._wtSetUpComponent_Crossplay = self:Wnd("WBP_SetUpComponent_Crossplay", UIWidgetBase)
    -- END MODIFICATION - VIRTUOS
    self._wtSortPanel = self:Wnd("WBP_SetUp_Game_SortPanel", SystemSettingPanelSortPanelHD)
end

function SystemSettingHDGamePlayPanel:_BindBtnEvent()
    self._wtSortlistExpandable:Event("OnExpandStateChanged", self._OnSortlistExpandStateChanged, self)
    self._wtAutoNewlineBtn:Event("OnStageChanged", self._OnAutoNewlineBtnStageChanged, self)
    self._wtSortEveryEnterBtn:Event("OnStageChanged", self._OnSortEveryEnterBtnStageChanged, self)
    if IsPS5() then
        self._wtSetUpComponent_Crossplay:Event("OnStageChanged", self._OnCrossPlaySwicthChanged, self)
    end

    self:AddLuaEvent(self._wtSortPanel.evtOnFocusLost, self._OnSortPanelFocusLost, self)
    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtSetUserFocusToCrossplay, self.SetFocusToCrossplay, self)
    self:AddLuaEvent(Module.SystemSetting.Config.Event.evtSetSecondLanguage, self.OnSetSecondLanguage, self)
end

function SystemSettingHDGamePlayPanel:_OnSortlistExpandStateChanged(bExpanded)
    if bExpanded then
        self._wtRootScrollBox:ScrollToEnd()
    --BEGIN MODIFICATION @ VIRTUOS :
        self:_RegisterSortListNav()
        self._OrderListOpened = true
    else
        self:_RemoveSortListNav()
        self._OrderListOpened = false
    end
    --END MODIFICATION
end

function SystemSettingHDGamePlayPanel:_OnAutoNewlineBtnStageChanged(stage)
    self:_MarkSettingChange()
end

function SystemSettingHDGamePlayPanel:_OnSortEveryEnterBtnStageChanged(stage)
    self:_MarkSettingChange()
end

-- BEGIN MODIFICATION - VIRTUOS
-- 跨平台开关修改需要向服务器发送请求，给一个冷却时间，不要频繁修改
function SystemSettingHDGamePlayPanel:_OnCrossPlaySwicthChanged()
    self._wtSetUpComponent_Crossplay:DisableItem(true)
    self._crossPlaySettingTimerHandle = Timer:NewIns(3)
    self._crossPlaySettingTimerHandle:AddListener(function()
        if not self.__cppinst then
            return
        end

        self._wtSetUpComponent_Crossplay:DisableItem(false)
        self._crossPlaySettingTimerHandle:Stop()
        self._crossPlaySettingTimerHandle:Release()
    end)
    self._crossPlaySettingTimerHandle:Start()
end
-- END MODIFICATION - VIRTUOS

function SystemSettingHDGamePlayPanel:OnOpen()
    -- BEGIN MODIFICATION - VIRTUOS
    if DFCONSOLE_LUA == 1 then
        -- 隐藏是否开启局内文字聊天开关
        -- 隐藏 "鼠标滚轮切换武器包含近战武器"
        self._wtSetUpComponent_Cycle:Collapsed()
        -- 2024/9/15 ：临时隐藏 收藏道具优先放入回收系统
        self._wtSetUpComponent_Check_System_2:Collapsed()
        if IsPS5() and self._wtSetUpComponent_ListTitle_Crossplay and self._wtSetUpComponent_Crossplay then
            -- 跨平台设置在PS5平台显示
            self._wtSetUpComponent_ListTitle_Crossplay:Visible()
            self._wtSetUpComponent_Crossplay:Visible()
        end
    else
        self._wtSetUpComponent_Cycle:Visible()
    end

    -- if self._wtSetUpComponent_ListTitle_Crossplay and self._wtSetUpComponent_Crossplay then
    --     -- 跨平台设置在PS5平台显示
    --     self._wtSetUpComponent_ListTitle_Crossplay:Visible()
    --     self._wtSetUpComponent_Crossplay:Visible()
    -- end

    if not IsPS5() or Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
        if self._wtSetUpComponent_ListTitle_Crossplay and self._wtSetUpComponent_Crossplay then
            -- 跨平台设置在非PS5平台和局内隐藏
            self._wtSetUpComponent_ListTitle_Crossplay:Collapsed()
            self._wtSetUpComponent_Crossplay:Collapsed()
        end
    end
    -- END MODIFICATION - VIRTUOS


    self:_BindBtnEvent()

    CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
    self:InitSecondLanguageTxt()
end

function SystemSettingHDGamePlayPanel:SetFocusToCrossplay()
    Timer.DelayCall(0.01, function()
        if self._wtSetUpComponent_Crossplay then
            WidgetUtil.SetUserFocusToWidget(self._wtSetUpComponent_Crossplay, true)
        end
    end)
end

function SystemSettingHDGamePlayPanel:InitSecondLanguageTxt()
    local clientGameSettingHD = import("ClientGameSettingHD").Get()
    if clientGameSettingHD then
        local cultureSign = clientGameSettingHD.SecondLanguage
        if cultureSign ~= "" then
            local displayLanguageText = string.format("<dfmrichtext type=\"img\" id=\"SetUpText_%s\"/>", cultureSign)
        else
            displayLanguageText = Module.SystemSetting.Config.Loc.NotSelect
        end
        if cultureSign == "zh-Hans" then
            displayLanguageText = "<dfmrichtext type=\"img\" id=\"SetUpText_zh\"/>" -- 海外主机单独支持简体中文
        end
        self._wtSecondLanguageTxt:SetText(Module.SystemSetting.Config.Loc.SecondLanguageIs,displayLanguageText)
    end

end

function SystemSettingHDGamePlayPanel:OnSetSecondLanguage(txt)
    self._wtSecondLanguageTxt:SetText(Module.SystemSetting.Config.Loc.SecondLanguageIs,txt)
end

function SystemSettingHDGamePlayPanel:OnShowBegin()
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.GamePlaySetting then
        -- BEGIN MODIFICATION @ VIRTUOS
        if not IsConsole() then
            local list = {
                -- BEGIN MODIFICATION @ VIRTUOS : 增加确认按键提示
                {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
                -- END MODIFICATION
                {
                    actionName = "Reset",
                    func = self._OnReset,
                    caller = self
                }
            }
            local globolList = Module.SystemSetting.Field:GetGlobalSummaryList()
            for _, v in ipairs(globolList) do
                table.insert(list, v)
            end
            Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
            --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
            self:_RegisterNavGroup()
            --END MODIFICATION
        end
        --END MODIFICATION
    end
end

function SystemSettingHDGamePlayPanel:OnShow()
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.GamePlaySetting then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
        self:_InitOrderList()

        -- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() then
            Module.CommonBar:SetBottomBarTempInputSummaryList(
                {
                    -- BEGIN MODIFICATION @ VIRTUOS : 增加确认按键提示
                    {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
                    -- END MODIFICATION
                    {
                        actionName = "Reset",
                        func = self._OnReset,
                        caller = self
                    }
                },
                false
            )
            --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
            self:_RegisterNavGroup()
            --END MODIFICATION
        end
        --END MODIFICATION
    end
    self._wtDrugDurationSlider:SelfHitTestInvisible()
end

function SystemSettingHDGamePlayPanel:_OnReset()
    local fReset = function()
        SettingLogicHD.ResetCurrentSettings()
    end
    local resetInputTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetGamePlayTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    Module.CommonTips:ShowConfirmWindow(resetInputTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

function SystemSettingHDGamePlayPanel:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    if self._bSettingChange then
        self._bSettingChange = false
        Server.InventoryServer:DoChangeSortConfig()
    end
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    self:_RemoveNavGroup()
    self:_CloseSortList()
    --END MODIFICATION
end

function SystemSettingHDGamePlayPanel:OnHide()
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
    if self._reorderableListComponent then
        self._reorderableListComponent:Reset()
    end
end

function SystemSettingHDGamePlayPanel:_InitOrderList()
    self._mapClsID2OrderCell = {}
    self._wtSortOrderList:ClearChildren()

    local sortOrderList = Server.InventoryServer:GetSortClsOrder()
    for _, clsType in ipairs(sortOrderList) do
        ---@type SystemSettingPanelSortOrderCell
        --- TODO 需要改成AddSubUI关联Owner
        local newCell = Facade.UIManager:CreateSubUIBindOwner(self, UIName2ID.SystemSettingPanelSortOrderCellHD)
        newCell:Init(clsType)
        newCell:BindClickedCallback(self._OnCellClicked, self)

        self._mapClsID2OrderCell[clsType] = newCell
        self._wtSortOrderList:AddChild(newCell)
    end

    ---@param cell SystemSettingPanelSortOrderCell
    local function fOnDragStart(cell)
        cell:SetSelected(true)
    end
    ---@param cell SystemSettingPanelSortOrderCell
    local function fOnDragStop(cell)
        cell:SetSelected(false)
    end
    local function fOnSwapPosEachStep(lastIndex, newIndex)
        -- Server.InventoryServer:LocalSwapSortOrder(lastIndex, newIndex)
        -- self:_MarkSettingChange()
    end
    local function fOnShiftPosFinally(lastIndex, newIndex)
        Server.InventoryServer:ShiftSwapSortOrder(lastIndex, newIndex)
        self:_MarkSettingChange()
    end
    ---@type ReorderableListParams
    local params = {
        verticalBox = self._wtSortOrderList,
        rootScrollBox = self._wtRootScrollBox,
        upAutoScrollArea = self._wtUpAutoScrollArea,
        downAutoScrollArea = self._wtDownAutoScrollArea,
        -- fOnSwapPosEachStep = fOnSwapPosEachStep,
        fOnDragStart = fOnDragStart,
        fOnDragStop = fOnDragStop,
        fOnShiftPosFinally = fOnShiftPosFinally
    }
    self._reorderableListComponent = Module.CommonWidget:InitReorderableList(params)

    --BEGIN MODIFICATION @ VIRTUOS :
    ---@type ReorderableListGamepadParams
    local gamepadParams = {
        ownerWidget = self,
        fOnGamepadMoveBegin = self._fOnGamepadMoveBegin,
        fOnGamepadMoveFinish = self._fOnGamepadMoveFinish,
        fOnGamepadMoveCancel = self._fOnGamepadMoveCancel
    }
    self._reorderableListComponent:InitGamepadParams(gamepadParams)
    --END MODIFICATION

end

function SystemSettingHDGamePlayPanel:_MarkSettingChange()
    self._bSettingChange = true
end

function SystemSettingHDGamePlayPanel:_OnClickBtn()
end

function SystemSettingHDGamePlayPanel:_OpenSecondLanguage()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingSecondLanguagePopView)
end


--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function SystemSettingHDGamePlayPanel:_RegisterNavGroup()
	if not self._NavGroup then
	    self._NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootScrollBox, self, "Grid1D")
	end

	if self._NavGroup then
		self._NavGroup:AddNavWidgetToArray(self._wtRootScrollBox)
        self._NavGroup:SetScrollRecipient(self._wtRootScrollBox)

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
end

function SystemSettingHDGamePlayPanel:_RemoveNavGroup()
	if self._NavGroup then
		self._NavGroup = nil
	end
	WidgetUtil.RemoveNavigationGroup(self)
end

function SystemSettingHDGamePlayPanel:_RegisterSortListNav()
    -- Navigation
    self:_RemoveSortListNav()

    if not self._SortListNavGroup then
        self._SortListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtSortListSlot, self._wtSortListSlot, "Hittest")
    end
    if self._SortListNavGroup then
		self._SortListNavGroup:AddNavWidgetToArray(self._wtSortListSlot)
        self._SortListNavGroup:SetScrollRecipient(self._wtRootScrollBox)
        self._SortListNavGroup:MarkIsStackControlGroup()
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._SortListNavGroup)
	end

    self._normalSettingInputSnapshot = Module.CommonBar:TakeBottomBarTempInputSummarySnapshot()
    -- Input
    self:_SetInputSummaryToOrederList()

end

function SystemSettingHDGamePlayPanel:_RemoveSortListNav()
    -- Navigation
    if self._SortListNavGroup then
        WidgetUtil.RemoveNavigationGroup(self._wtSortListSlot)
        self._SortListNavGroup = nil
    end

    if self._normalSettingInputSnapshot then
        Module.CommonBar:ApplyBottomBarTempInputSummarySnapshot(self._normalSettingInputSnapshot, true)
        self._normalSettingInputSnapshot = nil
    end
    -- Input
    -- local summaryList = {}
    -- table.insert(summaryList, {actionName = "Select", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
    -- table.insert(summaryList, {actionName = "Reset", func = self._OnReset, caller = self, bUIOnly = false, bHideIcon = false})
    -- Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, false)

end

function SystemSettingHDGamePlayPanel:_CloseSortList()
    local wtCheckBox = self._wtSortlistExpandable:Wnd("WBP_SetUpComponent_CheckOrder", UIWidgetBase):Wnd("DFCheckBox_101", UIWidgetBase)

    if wtCheckBox and self._OrderListOpened == true then
        wtCheckBox:NavigationClick()
    end
end

function SystemSettingHDGamePlayPanel:_fOnGamepadMoveBegin(self)
    loginfo("_fOnGamepadMoveBegin")
end

function SystemSettingHDGamePlayPanel:_fOnGamepadMoveFinish(self, lastIndex, newIndex)
    if self._OrderListOpened == false then
        return
    end

    if lastIndex ~= 0 and newIndex ~= 0 and lastIndex ~= newIndex then
        Server.InventoryServer:ShiftSwapSortOrder(lastIndex, newIndex)
        self:_MarkSettingChange()
    end

    -- Input
    self:_SetInputSummaryToOrederList()
end

function SystemSettingHDGamePlayPanel:_fOnGamepadMoveCancel(self)
    if self._OrderListOpened == false then
        return
    end
    -- Input
    self:_SetInputSummaryToOrederList()
end

function SystemSettingHDGamePlayPanel:_SetInputSummaryToOrederList()
    -- Input
    local summaryList = {}
    table.insert(summaryList, {actionName = "Select", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
    table.insert(summaryList, {actionName = "Back", func = self._CloseSortList, caller = self, bUIOnly = false, bHideIcon = false})

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end
--END MODIFICATION

function SystemSettingHDGamePlayPanel:_OnSortPanelFocusLost()
    self:_ResetSortList()
end

function SystemSettingHDGamePlayPanel:_OnCellClicked()
    self:_ResetSortList()
end

function SystemSettingHDGamePlayPanel:_ResetSortList()
    -- 重置状态
    for _, cell in pairs(self._mapClsID2OrderCell) do
        cell:ResetAll()
    end
    self._reorderableListComponent:ResetAll()
end

function SystemSettingHDGamePlayPanel:OnClose()
    self:RemoveAllLuaEvent()
end

return SystemSettingHDGamePlayPanel