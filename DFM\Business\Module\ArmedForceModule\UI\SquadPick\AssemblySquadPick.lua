----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------



local AssemblySquadPick = ui("AssemblySquadPick")
---@class AssemblySquadPick : LuaUIBaseView
local ArmedForceConfig = Module.ArmedForce.Config
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local GameplayStatics = import("GameplayStatics")
local SlateBlueprintLibrary = import("SlateBlueprintLibrary")
local LobbyDisplayLogic = require "DFM.Business.Module.LobbyDisplayModule.Logic.LobbyDisplayLogic"
local HeroLogic = require "DFM.Business.Module.HeroModule.Logic.HeroLogic"
local EMatchCtrlType = import "EMatchCtrlType"
local MemoryTool = require("DFM.StandaloneLua.BusinessTool.MemoryTool")

local BottomBarState = {
    PickHeroView = 1,
    PickHeroHide = 2,
    PickHeroEndView = 3,
    PickHeroEndHide = 4,
    PickHeroViewOnlyView=5,
}

local ProcessItems = {
    HeroItems = 1,
    SceneSubLevel = 2,
}
-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
local UGPInputHelper = import("GPInputHelper")
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"

-- END MODIFICATION

--#region 生命周期

function AssemblySquadPick:Ctor()
    logwarning("AssemblySquadPick:Ctor")
    --兵种名字控件
    self._playerWidget01=self:Wnd("WBP_Assembly_Label_01",LuaUIBaseView)
    self._playerWidget02=self:Wnd("WBP_Assembly_Label_02",LuaUIBaseView)
    self._playerWidget03=self:Wnd("WBP_Assembly_Label_03",LuaUIBaseView)
    self._playerWidget04=self:Wnd("WBP_Assembly_Label_04",LuaUIBaseView)

    --计时文本
    self._wtTimeText=self:Wnd("wtCurrentLevelText",UITextBlock)
    self._wtTimePanel=self:Wnd("Right",UIWidgetBase)
    --麦克风/喇叭
    self._wtMicroBtn=self:Wnd("wtMicroBtn",UIButton)
    self._wtVoiceBtn=self:Wnd("wtVoiceBtn",UIButton)
    self._wtMicroCtrlBtn=self:Wnd("WBP_ControllerButtonType_1",UIWidgetBase)
    self._wtVoiceCtrlBtn=self:Wnd("WBP_ControllerButtonType",UIWidgetBase)
    self._wtMicroIcon=self._wtMicroCtrlBtn:Wnd("Image_Icon",UIWidgetBase)
    self._wtVoiceIcon=self._wtVoiceCtrlBtn:Wnd("Image_Icon",UIWidgetBase)
    --查看能力
    self._wtVeiwSkillBtn=self:Wnd("WBP_CommonButtonV3S1_91",DFCommonButtonOnly)
    if self._wtVeiwSkillBtn then
        self._wtVeiwSkillBtn:BP_SetMainTitle(Module.Hero.Config.Loc.HeroSkillPanelTitleName)

        self._wtVeiwSkillBtn:Event("OnClicked",self._ShowSkillPanel,self)
        if IsHD() then
            self._wtVeiwSkillBtn:Collapsed()
        end
    end

    if not IsHD() then
        
        if self._wtMicroBtn then
            self._wtMicroBtn:Event("OnClicked",self.OnMicroBtnClick,self)
        end
        
        if self._wtVoiceBtn then
            self._wtVoiceBtn:Event("OnClicked",self.OnVoiceBtnClick,self)
        end
    end
    
    
    --英雄控件box
    self._wtHeroScrollBox=self:Wnd("ScrollBox_70",UIScrollBox)
    if self._wtHeroScrollBox then
        self._wtHeroScrollBox:Event("OnScrollEnd",self.OnScrollEnd,self)
    end
    --兵种tab栏
    self._wtHeroArmedTabGroup=UIUtil.WndTabGroupBox(self, "wtDFTabV3GroupBoxClass3Dynamic",self.OnGetTabItemCount, self.OnProcessTabItemWidget, self.OnCheckedTabIndexChanged)
    self._wtTabGroupPaddingBox=self:Wnd("PlatformPaddingBox_0",UIWidgetBase)
    if self._wtTabGroupPaddingBox then
        if Module.ArmedForce:GetIsShowHeroArmedTab() then
            self._wtTabGroupPaddingBox:SelfHitTestInvisible()
        else
            self._wtTabGroupPaddingBox:Collapsed()
        end
    end
    
    --名字控件父面板
    self._wtNameMainPanel=self:Wnd("MainContentPanel",UIWidgetBase)

    self._playerWidgetList={
        self._playerWidget01,
        self._playerWidget02,
        self._playerWidget03,
        self._playerWidget04,

    }
    for k,v in pairs(self._playerWidgetList)do
        v:Collapsed()
    end


    --兵种资产
    self.selectedHeroInsId=nil
    self.previewedHeroInsId=nil
    self._hasConfirmedOnce=false

    self.stageEndTime=0
    self.timeLength=0
    
    self.teammateReadyState={}
    self._IndextoFashionSuitTable={}
    self._cardInfos={}

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)

    --self:AddLuaEvent(Module.GVoice.Config.Events.evtGVoiceRoomMemberRoomStateChange,self._OnGVoiceRoomMemberRoomStateChange, self)

    self._wtChatWidget=self:Wnd("WBP_AssemblyChatInput_HD",UIWidgetBase)--PC聊天框
    self._wtChatBtn=self:Wnd("WBP_ControllerFunctionButtonCommand_Mobile",UIWidgetBase)--手游聊天按钮
    self._wtChatCanvas=self:Wnd("WBP_ChatView_Mobile",UIWidgetBase)--手游聊天记录

    self:AddLuaEvent(Module.ArmedForce.Config.evtAssemblySquadPickProcessIsReady,self._OnProcessIsReady, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self._OnSceneSubLevelLoaded, self)
end

function AssemblySquadPick:OnInitExtraData(dsRoomId,solRoomTeamRes,needWaitGameplayConfig)
    loginfo("AssemblySquadPick:OnInitExtraData",dsRoomId,needWaitGameplayConfig)
    self._dsRoomId=dsRoomId
    self._solRoomTeamRes=solRoomTeamRes
    self._needWaitGameplayConfig=needWaitGameplayConfig
   
end

function AssemblySquadPick:OnOpen()
    logwarning("AssemblySquadPick:OnOpen")
    self:AddListeners()

    self:InitHeroProperties()--初始化兵种资产

    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if armedForceMode and armedForceMode == EArmedForceMode.SOL then
        Facade.SoundManager:PlayOutgameMusicByName(GlobalMusicRes.CharacterSelectSOL)
    elseif armedForceMode and armedForceMode == EArmedForceMode.MP then
        Facade.SoundManager:PlayOutgameMusicByName(GlobalMusicRes.CharacterSelectBF)
    else
        Facade.SoundManager:PlayOutgameMusicByName(GlobalMusicRes.CharacterSelectSOL)
    end
    if IsHD() then
        self:PlayAnimation(self.WBP_Hero_MainPanel_Pc_in,0,1,0,1,false)
    else
        self:PlayAnimation(self.WBP_Hero_MainPanel_in,0,1,0,1,false)
    end
    
    if IsHD() then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Loading, false)
    
        self._hActionOpenChat =
        self:AddInputActionBinding(
        "OpenChat",
        EInputEvent.IE_Pressed,
        self._OnOpenChat,
        self,
        EDisplayInputActionPriority.UI_Chat)
        
        local inputDelegate=UGPInputDelegates and UGPInputDelegates.Get(GetGameInstance())
        self._inputTypeChangedHandle = inputDelegate and inputDelegate.OnInputTypeChanged and inputDelegate.OnInputTypeChanged:Add(CreateCPlusCallBack(self.OnInputTypeChanged, self))
    end
    self:SetTimerToUpdateArmedTabPosition()
end

function AssemblySquadPick:OnShowBegin()
    logwarning("AssemblySquadPick:OnShowBegin")
    --bug=125212445 【CN】【安卓】【必现】输入按键面板打开时进入单局不会自动关闭
    self:SetKeyboardFocus()

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
    -- END MODIFICATION
end

function AssemblySquadPick:OnHideBegin()
    logwarning("AssemblySquadPick:OnHideBegin")

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_EnableGamepadFeature(false)
    end
    -- END MODIFICATION
end

function AssemblySquadPick:OnShow()
    logwarning("AssemblySquadPick:OnShow")
   
    ArmedForceConfig.evtAssemblySquadPickOpenFinish:Invoke()

    self:SwitchBottomBarState()
end

function AssemblySquadPick:OnHide()
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "ResetVideoIds")
    logwarning("AssemblySquadPick:OnHide")
end

function AssemblySquadPick:OnClose()
    logwarning("AssemblySquadPick:OnClose")
    self:RemoveListeners()

    if self._updateArmedTabPosTimer then
        self._updateArmedTabPosTimer:Stop()
    end
    if self._updatePositionTimer then
        self._updatePositionTimer:Stop()
    end
    if self._timer then
        self._timer:Stop()
    end
    Timer.CancelDelay(self._daojishiDelayHandle)
    Timer.CancelDelay(self._resetUpdateLabelPositionTimerDelayHandle)
    Timer.CancelDelay(self._showCardDisplayPanelDelayHandle)

    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtHeroScrollBox)
    ArmedForceConfig.evtAssemblySquadPickClose:Invoke()

    Facade.UIManager:CloseUIByHandle(self._microPanelHandle)
    Facade.UIManager:CloseUIByHandle(self._voicePanelHandle)

    if IsHD() then
        if self._hActionOpenChat then
            self:RemoveInputActionBinding(self._hActionOpenChat)
            self._hActionOpenChat = nil
        end
        if self._navGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._navGroup = nil
        end
        if self._inputTypeChangedHandle then
            local inputDelegate=UGPInputDelegates and UGPInputDelegates.Get(GetGameInstance())
            if inputDelegate and inputDelegate.OnInputTypeChanged then
                inputDelegate.OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
                self._inputTypeChangedHandle = nil
            end
        end
    end
    Timer.CancelDelay(self._focusDefaultItemDelayHandle)

    --Facade.UIManager:CommitTransition(false)--关闭黑幕
    Facade.SoundManager:StopBGM()

    --关闭组队灯光组
    local LightGroupName = IsHD() and "MatchDisplay_Main_HD" or "MatchDisplay_Main"
    LightUtil.DeactivateLightGroup(LightGroupName)
end

function AssemblySquadPick:AddListeners()
    Facade.ProtoManager:AddNtfListener("CSMatchRoomSolChangeHeroNtf", self.OnCSMatchRoomSolChangeHeroNtf, self)--队友兵种信息改变的通知
    --Facade.ProtoManager:AddNtfListener("CSMatchRoomSolReadyNtf", self.OnCSMatchRoomSolReadyNtf, self)--队友兵种信息改变的通知
    -- Facade.ProtoManager:AddNtfListener("CSPlayerJoinMatchNtf", self.OnPlayerJoinMatch, self)--入局通知
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected,self)  --断线重连

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsPS5Family() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager and DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate then
            self._OnlineIdUpdateHandle = DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate:Add(
                CreateCPlusCallBack(self.HandleOnlineIdCacheUpdate, self)
            )
        end
    end
    --END MODIFICATION

end

function AssemblySquadPick:RemoveListeners()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected,self)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsPS5Family() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        if DFMOnlineIdentityManager and DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate and self._OnlineIdUpdateHandle then
            DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate:Remove(self._OnlineIdUpdateHandle)
            self._OnlineIdUpdateHandle = nil
        end
    end
    --END MODIFICATION
end

function AssemblySquadPick:SetTimerToUpdateArmedTabPosition()
    self._updateArmedTabPosTimer=Timer:NewIns(0.5,10)
    self._updateArmedTabPosTimer:AddListener(self.FixArmedTabPosition,self)
    self._updateArmedTabPosTimer:Start()
end

function AssemblySquadPick:_OnRelayConnected()
    logwarning("AssemblySquadPick:_OnRelayConnected")
    local fCSMatchRoomGetSolRoomTeamTRes=function(res)
        if res.result==0 then
            self._solRoomTeamRes=res
            local playerInfoArray=res.player_info_array
            playerInfoArray=self:_SortListByPlayerIdx(playerInfoArray)

            local playerWidgetList=self._playerWidgetList

            --BEGIN MODIFICATION @ VIRTUOS :
            if IsPS5Family() then
                self:UpdatePS5OnlineIdsByPlayerInfo(res.player_info_array)
            end 
	        --END MODIFICATION

           
            if not self._hasInitPlayerInfo then--包含计时逻辑，需要判断，避免重复执行
                self._hasInitPlayerInfo=true
                self:DoSetTickTimer(res)
                self:DoInitPlayerName(res)
                self:DoInitPlayerCharacter(res)
            else
                for k,v in pairs(playerInfoArray)do
                    local playerWidget=playerWidgetList[k]
                    playerWidget:SetInfo(v.hero_info.hero_id,v.nick_name,v.player_id)
    
                    local avatarId = Server.HeroServer:GetFashionSuitIdByHeroData(v.hero_info)
                    self:SetCharacterAvatarWithSlotId(k, avatarId)

                    --BEGIN MODIFICATION @ VIRTUOS : 
                    if not IsPS5Family() then
                        playerWidget:SetInfo(v.hero_info.hero_id,v.nick_name,v.player_id)
                    else
                        local playerNickName = v.nick_name
                        local curPS5OnlineId = self:GetPS5OnlineIdByOpenId(v.player_id)
                        
                        if curPS5OnlineId ~= nil then
                            playerNickName = curPS5OnlineId
                        end
                        playerWidget:SetInfo(v.hero_info.hero_id,playerNickName,v.player_id)
                    end
                    --END MODIFICATION
                end

            end
        else
            logerror("AssemblySquadPick:_OnRelayConnected, fCSMatchRoomGetSolRoomTeamTRes",res.result)
            self:_DoCloseUI()
            
        end
    end
    Server.ArmedForceServer:ReqGetSolRoomTeam(self._dsRoomId, fCSMatchRoomGetSolRoomTeamTRes)
end

function AssemblySquadPick:OnCSMatchRoomSolChangeHeroNtf(ntf)
    logwarning("AssemblySquadPick:OnCSMatchRoomSolChangeHeroNtf","playerId",ntf.player_id,"heroId",ntf.hero_info.hero_id)
    local roomId=ntf.room_id
    local playerId=ntf.player_id
    local heroInfo=ntf.hero_info
    if roomId~=self._dsRoomId then return end--有roomId判断一下roomId

    local index=-1
    local playerWidget={}
    for k,v in pairs(self._playerWidgetList)do
        if v._playerId==playerId then
            index=k
            playerWidget=v
            break
        end
    end
    if playerWidget.SetHeroInfo then
        playerWidget:SetHeroInfo(heroInfo.hero_id)
    end
    if self._cardInfos[index] then
        self._cardInfos[index].hero_id=heroInfo.hero_id
        self._cardInfos[index].accessories=self:FilterAccessories(heroInfo.accessories)
        self:LoadImageFromCardInfo(self._cardInfos[index])
    end

    if playerId~=Server.AccountServer:GetPlayerId() then-- 玩家模型预览的时候设置过了
        
        local avatarId = Server.HeroServer:GetFashionSuitIdByHeroData(heroInfo)
        self:SetCharacterAvatarWithSlotId(index, avatarId)
        self._IndextoFashionSuitTable[index] = avatarId

    end
    

    if playerId==Server.AccountServer:GetPlayerId()then
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.MatchCharacterSelect)
        self._hasConfirmedOnce=true

        Module.Hero:UseHeroById(heroInfo.hero_id)

        local heroInsId=nil
        local previewItem=self:GetHeroItemByInstId(self.previewedHeroInsId)
        local previewHeroId=previewItem and previewItem:GetHeroId()
        if previewHeroId==heroInfo.hero_id then
            heroInsId=self.previewedHeroInsId
        else
            heroInsIdList=self:GetInsIdListByHeroId(heroInfo.hero_id)
            heroInsId=heroInsIdList and heroInsIdList[1]
        end
        local heroItem=self:GetHeroItemByInstId(heroInsId)
        if heroItem then
            local selectedHeroItem=self:GetHeroItemByInstId(self.selectedHeroInsId)
            if selectedHeroItem then
                selectedHeroItem:OnUnOperated()
                selectedHeroItem:HideCurUseTip()
                selectedHeroItem:SwitchClickAnimWidgetsVisibility(false)

            end
            heroItem:OnOperated()
            heroItem:ShowCurUseTip(self._wtHeroScrollBox)
            heroItem:SwitchClickAnimWidgetsVisibility(true)
            heroItem:PlayWidgetAnim(heroItem.WBP_Hero_HeroItem01_click)

            self.selectedHeroInsId=heroInsId
            if self.previewedHeroInsId~=self.selectedHeroInsId then
                self:OnPreviewHero(self.selectedHeroInsId)
            end
            
        end
            
        
    end
end

function AssemblySquadPick:OnCSMatchRoomSolReadyNtf(ntf)
    logwarning("AssemblySquadPick:OnCSMatchRoomSolReadyNtf","roomId",ntf.room_id,"playerId",ntf.player_id)
    local roomId=ntf.room_id
    local playerId=ntf.player_id
    if roomId~=self._dsRoomId then return end--有roomId判断一下roomId

    local index=-1
    local playerWidget={}
    for k,v in pairs(self._playerWidgetList)do
        if v._playerId==playerId then
            index=k
            playerWidget=v
            self.teammateReadyState[k]=true
            break
        end
    end
   
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetTeammateState", index, true)

    if playerId==Server.AccountServer:GetPlayerId() then
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetDisplayType", "Center")

        self:ResetUpdatePositionTimer(0.1, 10)
        Timer.CancelDelay(self._resetUpdateLabelPositionTimerDelayHandle)
        self._resetUpdateLabelPositionTimerDelayHandle=Timer.DelayCall(1,function()
            self:ResetUpdatePositionTimer(1,0)
        end,self)

        
        
        
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.MatchBattleConfirm)
    end

end

function AssemblySquadPick:ResetUpdatePositionTimer(interval,count)
    loginfo("AssemblySquadPick:ResetUpdatePositionTimer","interval",interval,"count",count)
    --更新名字控件位置的时间句柄
    if self._updatePositionTimer then
        self._updatePositionTimer:Stop()
        self._updatePositionTimer=nil
    end

    self._updatePositionTimer=Timer:NewIns(interval,count)
    self._updatePositionTimer:AddListener(self.UpdateWidgetPosition,self)
    self._updatePositionTimer:Start()
end

function AssemblySquadPick:UpdateWidgetPosition()
    --设置名字控件位置
    for i=1,#self._cardInfos do
        local position=Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "GetCharacterPositionBySlot", i)
        if position then
            local zOffset=IsHD() and 90 or 12
            position.Z=position.Z+zOffset
        end
        local uiPosition=self:WorldSpace2WidgetSpace(position, self._wtNameMainPanel)
        if uiPosition then
            local playerNameWidget=self._playerWidgetList[i]
            local canvasSlot=UWidgetLayoutLibrary.SlotAsCanvasSlot(playerNameWidget)
            canvasSlot:SetPosition(FVector2D(uiPosition.X,uiPosition.Y))
            --控件位置确定再显示控件
            playerNameWidget:SelfHitTestInvisible()
        end
    end
    
end

--[[
    主流程匹配場景API:

-- 一个人或者三个人
-- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetDisplayType", "Center")
-- 两个人
-- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetDisplayType", "Left")


-- 設置隊友確認情況
-- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetTeammateState", SlotId, bSelect)  -- SlotId = 1, 2, 3; bSelect = 是否確認選擇

]]

function AssemblySquadPick:DoSetTickTimer(res)
    loginfo("AssemblySquadPick:DoSetTickTimer","localTime",Facade.ClockManager:GetLocalTimestamp(),"endTime",res.stage_end_time)
    self.stageEndTime=res.stage_end_time-8 + Module.ArmedForce.Config.GMPickHeroExtraTime --留8s展示名片用
    self._timer = Timer:NewIns(1, 0)
    self._timer:AddListener(self.Tick, self)
    self._timer:Start()
    self:Tick()
    local tickTitle=self:Wnd("DFTextBlock_234",UITextBlock)
    if tickTitle then 
        tickTitle:SetText(Module.Hero.Config.Loc.PleaseSelectHero)
    end
            
    self:ResetUpdatePositionTimer(0.1,10)
    --相机过渡完后每秒刷新一次位置，防止一直切后台
    Timer.CancelDelay(self._resetUpdateLabelPositionTimerDelayHandle)
    self._resetUpdateLabelPositionTimerDelayHandle=Timer.DelayCall(1,function()
        self:ResetUpdatePositionTimer(1,0)
    end,self)
end

function AssemblySquadPick:DoInitPlayerName(res)
    loginfo("AssemblySquadPick:DoInitPlayerName")
    local teamInfoTable={
        room_id=res.room_id,
        room_start_time=res.room_start_time,
        stage_end_time=res.stage_end_time,
        player_info_array={},
    }
    for k,v in pairs(res.player_info_array)do
        teamInfoTable.player_info_array[k]={
            player_id=v.player_id,
            hero_info={
                hero_id=v.hero_info.hero_id,
                fashion_list=v.hero_info.fashion_list,
                fashion_equipped=v.hero_info.fashion_equipped,
            },
            nick_name=v.nick_name,
            player_idx=v.player_idx,
            is_bot=v.is_bot,
            

        }
        

        local heroInfo=v.hero_info
        local accessories=self:FilterAccessories(heroInfo.accessories)
        self._cardInfos[k]={
            player_idx=v.player_idx,
            player_id=v.player_id,
            nick_name=v.nick_name,
            hero_id=heroInfo.hero_id,
            accessories=accessories,
            title=v.title,
            rank_title_adcode=v.rank_title_adcode,
            rank_title_rank_no=v.rank_title_rank_no,
            is_bot=v.is_bot,
        }
    end
    self._cardInfos=self:_SortListByPlayerIdx(self._cardInfos)
    logtable(teamInfoTable,true)
            

    for k,v in pairs(self._cardInfos)do
        self:LoadImageFromCardInfo(v)
    end
    local playerWidgetList=self._playerWidgetList

    local playerInfoArray=res.player_info_array
    playerInfoArray=self:_SortListByPlayerIdx(playerInfoArray)
    for k,v in pairs(playerInfoArray)do
        local playerWidget=playerWidgetList[k]

        --BEGIN MODIFICATION @ VIRTUOS : 
        if not IsPS5Family() then
            playerWidget:SetInfo(v.hero_info.hero_id,v.nick_name,v.player_id)
        else
            local playerNickName = v.nick_name
            local curPS5OnlineId = self:GetPS5OnlineIdByOpenId(v.player_id)
            
            if curPS5OnlineId ~= nil then
                playerNickName = curPS5OnlineId
            end
            playerWidget:SetInfo(v.hero_info.hero_id,playerNickName,v.player_id)
        end
        --END MODIFICATION

        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() then
            if not Server.AccountServer:IsCrossPlat() then
                logerror("AssemblySquadPick:DoInitPlayerName IsCrossPlat is false")
                playerWidget:SetPlatformIDType(nil)
            end

            logerror("AssemblySquadPick:DoInitPlayerName v.plat_id", v.plat_id)
            if v.plat_id then
                playerWidget:SetPlatformIDType(v.plat_id)
                -- 记录到对应cardInfo
                if self._cardInfos[k].player_id == v.player_id then
                    self._cardInfos[k].plat_id = v.plat_id
                end
            else
                self:_SetPlayerPlatfomIcon(v.player_id, k, playerWidget)
            end
        end
        --- END MODIFICATION

        if v.player_id==Server.AccountServer:GetPlayerId()then
            self._playerWidget=playerWidget
            
            local insIdList=self:GetInsIdListByHeroId(v.hero_info.hero_id)
            local defaultInsId=insIdList and insIdList[1]
            local heroItem=self:GetHeroItemByInstId(defaultInsId)
            if heroItem then
                heroItem:OnOperated()
                heroItem:ShowCurUseTip(self._wtHeroScrollBox)
                heroItem:SwitchClickAnimWidgetsVisibility(true)
                heroItem:PlayWidgetAnim(heroItem.WBP_Hero_HeroItem01_click)
                self:OnPreviewHero(defaultInsId)
                self.selectedHeroInsId=defaultInsId
                self.previewedHeroInsId=defaultInsId
            else
                logerror("AssemblySquadPick:DoInitPlayerName default heroItem not found","heroInsId",defaultInsId)
            end
        end
    end
    self:_InitMemberMicroState()
end

function AssemblySquadPick:FilterAccessories(accessories)--只要选中的
    loginfo("AssemblySquadPick:FilterAccessories")
    local filtedAccessories={}
    for k,v in pairs(accessories or {})do
        if v.is_selected then
            table.insert(filtedAccessories,v)
        end
    end
    return filtedAccessories
end

--玩家的控件
function AssemblySquadPick:GetPlayerWidget()
    return self._playerWidget
end

function AssemblySquadPick:InitHeroProperties()
    loginfo("AssemblySquadPick:InitHeroProperties")
    local AllHeros=Server.HeroServer:GetHeroData()
    logwarning("AssemblySquadPick:InitHeroProperties","HeroNum",table.nums(AllHeros))
    Facade.UIManager:RemoveSubUIByParent(self,self._wtHeroScrollBox)
    AllHeros=table.tolist(AllHeros)
    table.sort(AllHeros,function(a,b)
        if a.armed_force_id~=b.armed_force_id then
            return a.armed_force_id<b.armed_force_id
        else
            return a.expert_id<b.expert_id
        end

    end)
    local _index=0
    self._heroArmedId2HeroInsIds={}
    self._heroInsIdList={}
    for _, v in pairs(AllHeros) do
        _index=_index+1
        local heroListItem = self:GetHeroListItemByInstId(v.armed_force_id)
        if not heroListItem then
            local weakIns=Facade.UIManager:AddSubUI(self,UIName2ID.HeroMainViewHeroListItem,self._wtHeroScrollBox,v.armed_force_id)
            heroListItem=getfromweak(weakIns)
            local armedForceName=HeroHelperTool.GetHeroArmedForceName(v.hero_id)
            if heroListItem then
                heroListItem:SetTitle(armedForceName)
            end
        end
        local heroItem=nil
        local repeatedNum=math.max(1,Module.ArmedForce.Config.GMPickHeroRepeatedHeroNum)
        for i=1,repeatedNum do
            local insId=tonumber(v.hero_id..i)
            heroItem=heroListItem and heroListItem:AddHeroItem(insId)
            self._heroArmedId2HeroInsIds[v.armed_force_id]=self._heroArmedId2HeroInsIds[v.armed_force_id] or {}
            table.insert(self._heroArmedId2HeroInsIds[v.armed_force_id],insId)
        
            -- BEGIN MODIFICATION @ VIRTUOS : Navigation
            if IsHD() then
                if heroItem then
                    heroItem._bIsOpenNavClick = true
                end
            end
            -- END MODIFICATION
            local data = {
                index = _index,
                heroId = v.hero_id,
                insId=insId,
                caller = self,
                fCallback = CreateCallBack(self.OnHeroClicked,self),
                fHoverCallback = CreateCallBack(self.OnHeroHovered,self),
                fEnterCallback = CreateCallBack(self.OnHeroEnter,self),
                fLeaveCallback = CreateCallBack(self.OnHeroLeave,self),
    
                useSelectColor=true,
            }
        
            if heroItem then
                heroItem:InitData(data,true)
                local unlock=Server.HeroServer:IsCanUseHero(v.hero_id)
                heroItem:ToggleLockMask(not unlock)
                heroItem:SetAbandonDefaultSelect(true)
                table.insert(self._heroInsIdList, insId)

                logwarning("AssemblySquadPick:InitHeroProperties","heroInsId",insId,"currentHeroId",Module.Hero:GetCurShowHeroId())
                
            end
        
        end
	end
    Module.ArmedForce.Config.evtAssemblySquadPickProcessIsReady:Invoke(ProcessItems.HeroItems)
    self._wtHeroArmedTabGroup:RefreshTab()
    
    if #self._heroInsIdList<=20 then
        local idx=1
        for k,v in pairs(self._heroInsIdList)do
            local heroItem=self:GetHeroItemByInstId(v)
            if heroItem then
                heroItem:PlayShowAnim(idx*0.03+0.05)
            end
            idx=idx+1
        end
    end

end

function AssemblySquadPick:FixArmedTabPosition()
    self:ForceLayoutPrepass()
    local scrollBoxGeometry=self._wtHeroScrollBox:GetCachedGeometry()
    local scrollBoxAbsPos=scrollBoxGeometry and scrollBoxGeometry:GetAbsolutePosition()
    local mainCanvasGeometry=self._wtNameMainPanel:GetCachedGeometry()
    local scrollBoxLocalPosToMainCanvas=mainCanvasGeometry and mainCanvasGeometry:AbsoluteToLocal(scrollBoxAbsPos)
    loginfo("AssemblySquadPick:FixArmedTabPosition",scrollBoxAbsPos ,scrollBoxLocalPosToMainCanvas)

    if scrollBoxAbsPos and scrollBoxAbsPos.X~=0 and scrollBoxAbsPos.Y~=0 and scrollBoxLocalPosToMainCanvas then
        --self._updateArmedTabPosTimer:Stop()
        local isOverLength=self:GetIsScrollBoxLengthOverSizeBox()
        local contentPaddingLeft=self:GetContentPaddingLeft()
        local offsetX=isOverLength and 0 or contentPaddingLeft
        local paddingBoxCanvasSlot=UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtTabGroupPaddingBox)
        local FAnchors = import "Anchors"
        local anchor=FAnchors()
        anchor.Minimum=FVector2D(0,0)
        anchor.Maximum=FVector2D(0,0)
        paddingBoxCanvasSlot:SetAnchors(anchor)
        paddingBoxCanvasSlot:SetAlignment(FVector2D(0,1))
        paddingBoxCanvasSlot:SetPosition(FVector2D(scrollBoxLocalPosToMainCanvas.X+offsetX,scrollBoxLocalPosToMainCanvas.Y-10))
        self._wtTabGroupPaddingBox:SetPadding(FMargin(0,0,0,0))
    end
    
end

function AssemblySquadPick:OnHeroClicked(heroItem)
    logwarning("AssemblySquadPick:OnHeroClicked")
    self:DoHeroClick(heroItem)
    self:ChangeHero()
    
end

function AssemblySquadPick:DoHeroClick(heroItem)
    local heroInsId=heroItem:GetInsId()
    self:OnPreviewHero(heroInsId)
    ArmedForceConfig.evtAssemblySquadPickHeroClicked:Invoke()
end

function AssemblySquadPick:OnHeroHovered(heroItem)
    
end

function AssemblySquadPick:OnHeroEnter(heroItem)
    if WidgetUtil.IsGamepad() then
        self:DoHeroClick(heroItem)
    end
end

function AssemblySquadPick:OnHeroLeave(heroItem)
    
end

function AssemblySquadPick:GetTabIndexByHeroInsId(heroInsId)
    loginfo("AssemblySquadPick:GetTabIndexByHeroInsId",heroInsId)
    for k,v in pairs(self._heroArmedId2HeroInsIds or {})do
        if table.contains(v,heroInsId)then
            return k-1
        end
    end
end

function AssemblySquadPick:OnPreviewHero(heroInsId)
    loginfo("AssemblySquadPick:OnPreviewHero",heroInsId)
    if heroInsId==self.previewedHeroInsId then
        return
    end
    local heroItem=self:GetHeroItemByInstId(heroInsId)
    local heroId=heroItem and heroItem:GetHeroId()
    if not Server.HeroServer:IsCanUseHero(heroId) then
        Module.CommonTips:ShowSimpleTip(Module.Hero.Config.Loc.TipHeroLock)
        return
    end

    if self.previewedHeroInsId then
        local heroItem=self:GetHeroItemByInstId(self.previewedHeroInsId)
        if heroItem then
            heroItem:OnUnSelect()
        end
    end

    local currentHeroItem=self:GetHeroItemByInstId(heroInsId)
    if currentHeroItem then
        currentHeroItem:OnSelect()
        self._wtHeroArmedTabGroup:SetTabIndex(self:GetTabIndexByHeroInsId(heroInsId),false)
        self:TryScrollHeroItemIntoView(currentHeroItem)
        self.previewedHeroInsId=heroInsId
    end
        
    local fashionSuitId=Server.HeroServer:GetFashionSuitIdByHeroId(heroId)
    self:SetCharacterAvatarWithSlotId(1, fashionSuitId)
    self:OnHighLevelFashionSelected(heroId, fashionSuitId)
    self._IndextoFashionSuitTable[1]=fashionSuitId
    local playerWidget=self:GetPlayerWidget()
    if playerWidget then
        playerWidget:SetHeroInfo(heroId)
    else
        logerror("playerWidget not valid")
    end
    
end

function AssemblySquadPick:TryScrollHeroItemIntoView(heroItem)
    loginfo("AssemblySquadPick:TryScrollHeroItemIntoView")
    if heroItem then
        local offsetLeft,offsetRight=self:GetScrollOffsetsForItemIntoView(heroItem)
        local curOffset=self._wtHeroScrollBox:GetScrollOffset()
        if curOffset<offsetRight or curOffset>offsetLeft then
            self._wtHeroScrollBox:ScrollToOffset(curOffset<offsetRight and offsetRight or offsetLeft)
        end
    else
        logerror("AssemblySquadPick:TryScrollHeroItemIntoView, heroItem is nil!!!")
    end
end

function AssemblySquadPick:OnHighLevelFashionSelected(heroId, inFashionId)
    loginfo("AssemblySquadPick:OnHighLevelFashionSelected",inFashionId)
    --更换背景视频材质
    local isHighLevel = HeroLogic.IsHighLevel(inFashionId)
    local playId = isHighLevel and inFashionId or heroId
    
    --设置布料材质
    -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "LoadAndSetClothMaterial", "MatchSceneMesh", inFashionId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "LoadSceneBP", "None", inFashionId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SwitchVideoMaterial", playId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "LoadAndSetLightLevel", inFashionId)


end

function AssemblySquadPick:OnInputTypeChanged(inputType)
    loginfo("AssemblySquadPick:OnInputTypeChanged",inputType)
    if self:IsVisible() then--需要判断一下IsVisible，因为打开查看技能界面的时候需要屏蔽这个逻辑
        self:SwitchBottomBarState(inputType)
    end
end

function AssemblySquadPick:SwitchBottomBarState(inputType)
    loginfo("AssemblySquadPick:SwitchBottomBarState",inputType)
    inputType=inputType or WidgetUtil.GetCurrentInputType()
    local summaryList={}
    if inputType==EGPInputType.Gamepad then
        summaryList={
            {actionName = "SelectHero",func = self.ChangeHero, caller = self ,bUIOnly = false, bHideIcon = false},
            {actionName = "ViewSkillsSquadPick",func = self._ShowSkillPanel, caller = self ,bUIOnly = false, bHideIcon = false},
        }
    else
        summaryList={
            {actionName = "ViewSkillsSquadPick",func = self._ShowSkillPanel, caller = self ,bUIOnly = false, bHideIcon = false},
        }
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true,true)
    
end

function AssemblySquadPick:_OnSelectHero()
	
end

function AssemblySquadPick:ChangeHero()
    loginfo("AssemblySquadPick:ChangeHero")
    if self.previewedHeroInsId then 
        if self.previewedHeroInsId ~= self.selectedHeroInsId then
            local heroItem=self:GetHeroItemByInstId(self.previewedHeroInsId)
            local heroId=heroItem and heroItem:GetHeroId()
            if heroId then
                Server.ArmedForceServer:ReqSetSolRoomHero(self._dsRoomId, heroId)
            else
                logerror("AssemblySquadPick:ChangeHero heroId is nil!!!")
            end
        else
            logwarning("AssemblySquadPick:ChangeHero previewedHeroInsId == selectedHeroInsId!")
        end
    else
        logerror("AssemblySquadPick:ChangeHero previewedHeroInsId is nil!!!")
    end
    

end

function AssemblySquadPick:SetHeroReady()
    logwarning("AssemblySquadPick:SetHeroReady")
    MemoryTool.ProcessReleaseResourceOnSOLHeroReady()

    if self.selectedHeroInsId and self.selectedHeroInsId ~= self.previewedHeroInsId then
        self:OnPreviewHero(self.selectedHeroInsId)
    elseif self.selectedHeroInsId then
        self:TryScrollHeroItemIntoView(self:GetHeroItemByInstId(self.selectedHeroInsId))
    end
    
    Server.ArmedForceServer:ReqSolReady(self._dsRoomId)
    ArmedForceConfig.evtAssemblySquadPickConfirmClicked:Invoke()
    
end

function AssemblySquadPick:Tick()
    self._remainTime=self.stageEndTime-Facade.ClockManager:GetLocalTimestamp()

    if self._remainTime>10 then -- 10秒外
        if self._wtTimeText then
            self._wtTimeText:SetText(self._remainTime)
        end
    elseif self._remainTime>0 then -- (0-10]秒内
        if self._remainTime<=5 then -- (0-5]秒内
            if self._remainTime==5 and not self._isSetTextColor then
                self._isSetTextColor=true
                if self._wtTimeText then
                    local color = Facade.ColorManager:GetSlateColor("Highlight01")
                    self._wtTimeText:SetColorAndOpacity(color)
                end
            end
            self:PlayAnimation(self.WBP_Hero_MainPanel_daojishi02,0,1,0,1,false)
            Timer.CancelDelay(self._daojishiDelayHandle)
            self._daojishiDelayHandle=Timer.DelayCall(self.WBP_Hero_MainPanel_daojishi02:GetEndTime()*0.6,function(self)
                if self._wtTimeText then
                    self._wtTimeText:SetText(self._remainTime)
                end
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.CharacterSelectCountdownStage2)
            end,self)
        else -- [6-10]秒
            if self._wtTimeText then
                self._wtTimeText:SetText(self._remainTime)
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.CharacterSelectCountdownStage1)
            end
        end
    else
        self._wtTimePanel:Collapsed()
        self._timer:Stop()
        logwarning("AssemblySquadPick:Tick timerStop")

        self._wtHeroScrollBox:HitTestInvisible()
        for k,v in pairs(self._heroInsIdList)do
            if v~=self.selectedHeroInsId then
                local heroItem=self:GetHeroItemByInstId(v)
                if heroItem then
                    heroItem:ToggleLockMask(true)
                    if Server.HeroServer:IsCanUseHero(heroItem:GetHeroId()) then
                        local lockComp=heroItem:Wnd("WBP_SlotCompMaskSmallLock",UIWidgetBase)
                        local lockIcon1=lockComp and lockComp:Wnd("Image_Lock_Up",UIWidgetBase)
                        local lockIcon2=lockComp and lockComp:Wnd("Image_Lock_Down",UIWidgetBase)
                        if lockIcon1 and lockIcon2 then
                            lockIcon1:Collapsed()
                            lockIcon2:Collapsed()
                        end
                    end
                end
            end
        end
        self:SetHeroReady()
        self._showCardDisplayPanelDelayHandle=Timer.DelayCall(2,function(self)--留两秒等待时间，防止卡最后一秒选英雄拿不到回包
            self:ShowCardDisplayPanel()
        end,self)
            
    end
end

function AssemblySquadPick:ShowCardDisplayPanel()
    loginfo("AssemblySquadPick:ShowCardDisplayPanel")
    Module.ArmedForce.Config.evtAssemblySquadPickReadyToClose:Invoke()--清理技能ui，视频ui
    Facade.UIManager:CloseUI(self) -- 这里直接关掉选人界面，展示名片就好
    Facade.UIManager:AsyncShowUI(UIName2ID.AssemblyCardDisplay,fOnCardDisplayCreated,nil,self._cardInfos, self._IndextoFashionSuitTable,self._needWaitGameplayConfig)
            
end

function AssemblySquadPick:OnMicroBtnClick()
    loginfo("AssemblySquadPick:OnMicroBtnClick")

    --暂时屏蔽PC,pc默认是按住说话EGVoiceButtonType.TeamPress
    if not IsHD() then
        loginfo("AssemblySquadPick:OnMicroBtnClick not hd")
        Facade.UIManager:CloseUIByHandle(self._microPanelHandle)
        Facade.UIManager:CloseUIByHandle(self._voicePanelHandle)
        local anchor, center, offset = self:GetCorretMicPanelParam()
        self._microPanelHandle=Module.GVoice:OpenMicPanel(self._wtMicroBtn, function (self,uiIns)
            -- disable btn
            if self._wtMicroBtn and self._wtMicroBtn.SetVisibility then
                self._wtMicroBtn:SetVisibility(ESlateVisibility.HitTestInvisible)
            end
            if self._wtMicroCtrlBtn and self._wtMicroCtrlBtn.SetIsPressed then
                self._wtMicroCtrlBtn:SetIsPressed(true)
            end
        end, 
        self, anchor , center, offset,
        CreateCallBack(
            function (self)
                if self._wtMicroBtn and self._wtMicroBtn.SetVisibility then
                    self._wtMicroBtn:SetVisibility(ESlateVisibility.Visible)
                end
                if self._wtMicroCtrlBtn and self._wtMicroCtrlBtn.SetIsPressed then
                    self._wtMicroCtrlBtn:SetIsPressed(false)
                end
                local microType = Module.GVoice:GetMicrophoneButtonType()
                local micIcon = Module.GVoice:GetMicrophoneButtonImagePath(microType)
                if self._wtMicroIcon and self._wtMicroIcon.AsyncSetImagePath then
                    self._wtMicroIcon:AsyncSetImagePath(micIcon)
                end
                self:_RefreshSelfLabelMicroType(microType)
            end, self))
        
    else
        loginfo("AssemblySquadPick:OnMicroBtnClick is hd")
    end
end

-- 从蓝图中读取二级界面的吸附数据 供后续二级界面正确挂载到本控件上
function AssemblySquadPick:GetCorretMicPanelParam()
    -- 先设置常规默认值
    local anchor = LuaGlobalConst.TOP_RIGHT_VECTOR
    local center = LuaGlobalConst.TOP_LEFT_VECTOR
    local offset = FVector2D(5, 5)
    -- 试图从蓝图读取预设值
    anchor = self.ChatVoiceBtnAnchor or anchor
    center = self.ChatVoiceBtnCenter or center
    offset = self.ChatVoiceBtnOffset or offset

    return anchor, center, offset
end

function AssemblySquadPick:OnVoiceBtnClick()
    loginfo("AssemblySquadPick:OnVoiceBtnClick")
    if not IsHD() then
        loginfo("AssemblySquadPick:OnVoiceBtnClick not hd")
        Facade.UIManager:CloseUIByHandle(self._microPanelHandle)
        Facade.UIManager:CloseUIByHandle(self._voicePanelHandle)
        local anchor, center, offset = self:GetCorretSpeakPanelParam()
        self._voicePanelHandle=Module.GVoice:OpenSpeakerPanel(self._wtMicroBtn, function (self,uiIns)
            -- disable btn
            if self._wtVoiceBtn and self._wtVoiceBtn.SetVisibility then
                self._wtVoiceBtn:SetVisibility(ESlateVisibility.HitTestInvisible)
            end
            if self._wtVoiceCtrlBtn and self._wtVoiceCtrlBtn.SetIsPressed then
                self._wtVoiceCtrlBtn:SetIsPressed(true)
            end
        end, 
        self, anchor , center, offset,
        CreateCallBack(
            function (self)
                if self._wtVoiceBtn and self._wtVoiceBtn.SetVisibility then
                    self._wtVoiceBtn:SetVisibility(ESlateVisibility.Visible)
                end
                if self._wtVoiceCtrlBtn and self._wtVoiceCtrlBtn.SetIsPressed then
                    self._wtVoiceCtrlBtn:SetIsPressed(false)
                end
                local speakerbuttonType = Module.GVoice:GetSpeakerButtonType()
                local icon = Module.GVoice:GetSpeakerButtonImagePath(speakerbuttonType)
                if self._wtVoiceIcon and self._wtVoiceIcon.AsyncSetImagePath then
                    self._wtVoiceIcon:AsyncSetImagePath(icon)
                end
            end, self))
    else
        loginfo("AssemblySquadPick:OnVoiceBtnClick is hd")
    end
end

function AssemblySquadPick:OnScrollEnd()
end

function AssemblySquadPick:CheckToHideUseTip(scrollOffset)
    loginfo("AssemblySquadPick:CheckToHideUseTip",scrollOffset)
    local selectedItem=self:GetHeroItemByInstId(self.selectedHeroInsId)
    if selectedItem then
        local offsetLeft,offsetRight=self:GetScrollOffsetsForItemIntoView(selectedItem)
        local itemSizeX=self:GetItemSizeX()
        local limitOffsetLeft=offsetLeft+itemSizeX
        local limitOffsetRight=offsetRight-itemSizeX
        if scrollOffset<limitOffsetRight or scrollOffset>limitOffsetLeft then
            local useTipIns=selectedItem:GetCurUseTip()
            if useTipIns then
                useTipIns:Collapsed()
            end
        end
    end
end

function AssemblySquadPick:HideUseTip()
    local selectedItem=self:GetHeroItemByInstId(self.selectedHeroInsId)
    local useTipIns=selectedItem and selectedItem:GetCurUseTip()
    loginfo("AssemblySquadPick:HideUseTip",self.selectedHeroInsId,selectedItem,useTipIns)
    if useTipIns then
        useTipIns:Collapsed()
    end        
end

function AssemblySquadPick:TryShowUseTip()
    local selectedItem=self:GetHeroItemByInstId(self.selectedHeroInsId)
    local useTipIns=selectedItem and selectedItem:GetCurUseTip()
    if useTipIns then
        local offsetLeft,offsetRight=self:GetScrollOffsetsForItemIntoView(selectedItem)
        local itemSizeX=self:GetItemSizeX()
        local limitOffsetLeft=offsetLeft+itemSizeX
        local limitOffsetRight=offsetRight-itemSizeX
        local scrollOffset=self._wtHeroScrollBox:GetScrollOffset()
        if scrollOffset>limitOffsetRight and scrollOffset<limitOffsetLeft then
            useTipIns:SelfHitTestInvisible()
        end
    end        
end

function AssemblySquadPick:OnGetTabItemCount()
    loginfo("AssemblySquadPick:OnGetTabItemCount")
    return table.nums(self._heroArmedId2HeroInsIds or {})
end

--position从0开始
function AssemblySquadPick:OnProcessTabItemWidget(position, itemWidget)
    local armedInfo=HeroHelperTool.GetArmedForceInfoByArmId(position+1)
    itemWidget:AsyncSetImageIconPathAllState(armedInfo and armedInfo.Icon or "")
end

function AssemblySquadPick:OnCheckedTabIndexChanged(curIndex, lastIndex)
    loginfo("AssemblySquadPick:OnCheckedTabIndexChanged",curIndex, lastIndex)
    local scrollOffset=0
    local scrollBoxSizeX=self:GetScrollBoxSizeX()
    local listLength=self:GetItemListSizeX(curIndex+1)
    if listLength>=scrollBoxSizeX then
        scrollOffset=self:GetScrollOffsetStartByIndex(curIndex+1)
    else
        local offsetStart=self:GetScrollOffsetStartByIndex(curIndex+1)
        local offsetEnd=self:GetScrollOffsetEndByIndex(curIndex+1)
        local curOffset=self._wtHeroScrollBox:GetScrollOffset()
        if curOffset>=offsetEnd and curOffset<=offsetStart then
            scrollOffset=curOffset
        elseif curOffset<offsetEnd then
            scrollOffset=offsetEnd
        else
            scrollOffset=offsetStart
        end
    end
    self._wtHeroScrollBox:ScrollToOffset(scrollOffset)
    self:CheckToHideUseTip(scrollOffset)
end

function AssemblySquadPick:GetScrollOffsetStartByIndex(index)--左对齐
    local contentPaddingLeft=self:GetContentPaddingLeft()
    local contentPaddingRight=self:GetContentPaddingRight()
    local innerSlotPaddingX=self:GetInnerSlotPaddingX()
    local itemSizeX=self:GetItemSizeX()
    local scrollOffset=0
    for i=1,index-1 do
        local heroItemNum=self._heroArmedId2HeroInsIds[i] and #self._heroArmedId2HeroInsIds[i] or 0
        scrollOffset=scrollOffset+contentPaddingLeft+contentPaddingRight+math.max(heroItemNum-1,0)*innerSlotPaddingX+heroItemNum*itemSizeX
    end    
    scrollOffset=scrollOffset+contentPaddingLeft
    return scrollOffset
end

function AssemblySquadPick:GetScrollOffsetEndByIndex(index)--右对齐
    local scrollOffsetStart=self:GetScrollOffsetStartByIndex(index)
    local scrollBoxSizeX=self:GetScrollBoxSizeX()
    local listLength=self:GetItemListSizeX(index)
    local scrollOffsetEnd=scrollOffsetStart-(scrollBoxSizeX-math.min(scrollBoxSizeX,listLength))
    return scrollOffsetEnd
end

function AssemblySquadPick:GetContentPaddingLeft()
    local contentPadding=self._wtHeroScrollBox.ContentPadding
    local contentPaddingLeft=contentPadding and contentPadding.Left or 0
    return contentPaddingLeft
end

function AssemblySquadPick:GetContentPaddingRight()
    local contentPadding=self._wtHeroScrollBox.ContentPadding
    local contentPaddingRight=contentPadding and contentPadding.Right or 0
    return contentPaddingRight
end

function AssemblySquadPick:GetInnerSlotPaddingX()
    local heroListItem=self:GetHeroListItemByInstId(1)
    local innerSlotPadding=heroListItem and heroListItem:GetInnerSlotPadding()
    local innerSlotPaddingX=innerSlotPadding and innerSlotPadding.X or 0
    return innerSlotPaddingX
end

function AssemblySquadPick:GetItemSizeX()
    local heroListItem=self:GetHeroListItemByInstId(1)
    local heroInsIdList=self._heroArmedId2HeroInsIds[1]
    local heroItem=heroListItem and heroListItem:GetHeroItem(heroInsIdList and heroInsIdList[1])
    local itemSize=heroItem and heroItem:GetDesiredSize()
    local itemSizeX=itemSize and itemSize.X or 0
    return itemSizeX
end

function AssemblySquadPick:GetScrollBoxSizeX()
    local catchedGeometry=self._wtHeroScrollBox:GetCachedGeometry()
    local localSize=catchedGeometry and catchedGeometry:GetLocalSize()
    local scrollBoxSizeX=math.floor(localSize and localSize.X or 0)
    return scrollBoxSizeX
end

function AssemblySquadPick:GetItemListSizeX(index)
    local itemSizeX=self:GetItemSizeX()
    local innerSlotPaddingX=self:GetInnerSlotPaddingX()
    local heroInsIdList=self._heroArmedId2HeroInsIds[index]
    local heroIdNum=heroInsIdList and #heroInsIdList or 0
    local listLength=heroIdNum*itemSizeX+math.max(heroIdNum-1,0)*innerSlotPaddingX
    return listLength    
end

function AssemblySquadPick:GetScrollOffsetsForItemIntoView(heroItem)
    local offsetRight=0
    if heroItem then
        for k,v in pairs(self._heroArmedId2HeroInsIds or {})do
            local insId=heroItem:GetInsId()
            if not table.contains(v,insId)then
                local contentPaddingLeft=self:GetContentPaddingLeft()
                local contentPaddingRight=self:GetContentPaddingRight()
                offsetRight=offsetRight+self:GetItemListSizeX(k)+contentPaddingLeft+contentPaddingRight
            else
                local index=0
                for k1,v1 in pairs(v)do
                    index=index+1
                    if v1==insId then
                        break
                    end
                end
                local itemSizeX=self:GetItemSizeX()
                local innerSlotPaddingX=self:GetInnerSlotPaddingX()
                local contentPaddingLeft=self:GetContentPaddingLeft()
                offsetRight=offsetRight+index*itemSizeX+(index-1)*innerSlotPaddingX+contentPaddingLeft
                break

            end
        end
    end
    local scrollBoxSizeX=self:GetScrollBoxSizeX()
    local itemSizeX=self:GetItemSizeX()
    local scrollOffsetRight=offsetRight-scrollBoxSizeX
    local scrollOffsetLeft=scrollOffsetRight+scrollBoxSizeX-itemSizeX

    return scrollOffsetLeft,scrollOffsetRight
end

function AssemblySquadPick:GetIsScrollBoxLengthOverSizeBox()
    local sizeBox=self:Wnd("DFSizeBox_0",UIWidgetBase)
    local sizeBoxGeometry=sizeBox and sizeBox:GetCachedGeometry()
    local sizeBoxSize=sizeBoxGeometry and sizeBoxGeometry:GetLocalSize()
    local sizeBoxSizeX=sizeBoxSize and sizeBoxSize.X or 0
    local contentPaddingLeft=self:GetContentPaddingLeft()
    local contentPaddingRight=self:GetContentPaddingRight()
    local allItemSizeX=0
    for k,v in pairs(self._heroArmedId2HeroInsIds or {})do
        allItemSizeX=allItemSizeX+self:GetItemListSizeX(k)+contentPaddingLeft+contentPaddingRight
    end 
    return allItemSizeX>sizeBoxSizeX   
end

-- 从蓝图中读取二级界面的吸附数据 供后续二级界面正确挂载到本控件上
function AssemblySquadPick:GetCorretSpeakPanelParam()
    -- 先设置常规默认值
    local anchor = LuaGlobalConst.TOP_RIGHT_VECTOR
    local center = LuaGlobalConst.TOP_LEFT_VECTOR
    local offset = FVector2D(5, 5)
    -- 试图从蓝图读取预设值
    anchor = self.ChatVoiceBtnAnchor or anchor
    center = self.ChatVoiceBtnCenter or center
    offset = self.ChatVoiceBtnOffset or offset

    return anchor, center, offset
end


-- 关卡资源准备完毕 场景模型设置需要在这个事件之后
function AssemblySquadPick:_OnSceneSubLevelLoaded(curSubStageType)
    loginfo("AssemblySquadPick:_OnSceneSubLevelLoaded",curSubStageType)
    if  curSubStageType == ESubStage.HallMatch then
        self:InitSceneDisplayCtrlType()
        Module.ArmedForce.Config.evtAssemblySquadPickProcessIsReady:Invoke(ProcessItems.SceneSubLevel)
        if self._hasInitPlayerInfo then--从视频界面退回来的时候会触发场景重新加载
            Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetDisplayType", "CenterNear")
            for k,v in pairs(self._IndextoFashionSuitTable)do
                self:SetCharacterAvatarWithSlotId(k, v)
                if k == 1 then
                    local heroItem=self:GetHeroItemByInstId(self.selectedHeroInsId)
                    local heroId=heroItem and heroItem:GetHeroId() or 0
                    self:OnHighLevelFashionSelected(tostring(heroId), v)
                end
            end
        end

    end
end

function AssemblySquadPick:_OnProcessIsReady(process)
    loginfo("AssemblySquadPick:_OnProcessIsReady",process)
    if process==ProcessItems.HeroItems then
        self._heroItemsIsReady=true
    elseif process==ProcessItems.SceneSubLevel then
        self._sceneSubLevelIsReady=true
    end
    if self._sceneSubLevelIsReady and self._heroItemsIsReady then
        if not self._hasInitPlayerInfo then--首次加载
            self._hasInitPlayerInfo=true
            
            local res=self._solRoomTeamRes
            --BEGIN MODIFICATION @ VIRTUOS :
            if IsPS5Family() then
                self:UpdatePS5OnlineIdsByPlayerInfo(res.player_info_array)
            end 
            --END MODIFICATION

            self:DoSetTickTimer(res)
            self:DoInitPlayerName(res)
            self:DoInitPlayerCharacter(res)
            
        end
    end
end

function AssemblySquadPick:DoInitPlayerCharacter(res)
    loginfo("AssemblySquadPick:DoInitPlayerCharacter")

    local playerInfoArray=res.player_info_array
    playerInfoArray=self:_SortListByPlayerIdx(playerInfoArray)

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetDisplayType", "CenterNear")
    
    if Module.ArmedForce.Config.GMPickHeroAllSameModel == 1 then
        local gmArray = {
            playerInfoArray[1],
            playerInfoArray[1],
            playerInfoArray[1],
        }
        playerInfoArray = gmArray
    end

    for k,v in pairs(playerInfoArray)do
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetTeammateState", k, false)
        if k~=1 then
            local fashionSuitId = Server.HeroServer:GetFashionSuitIdByHeroData(v.hero_info)
            self:SetCharacterAvatarWithSlotId(k, fashionSuitId)
            self._IndextoFashionSuitTable[k]=fashionSuitId
        end
    end
                
end

--初始化所有人的麦的状态
function AssemblySquadPick:_InitMemberMicroState()
    loginfo("AssemblySquadPick:_InitMemberMicroState")
    local playerNum=0
    local playerWidget=nil
    for k,v in pairs(self._playerWidgetList)do
        if v._playerId then
            playerNum=playerNum+1
            if v._playerId==Server.AccountServer:GetPlayerId() then
                playerWidget=v
                local microType=Module.GVoice:GetMicrophoneButtonType()
                loginfo("selfmicroActionType",microType)
                v:SetMicroImage(microType)
            else
                if v._wtMicroImg then
                    v._wtMicroImg:Collapsed()
                end
             
            end
        end
    end

    if not IsHD() then
        if self._wtChatWidget then
            self._wtChatWidget:Collapsed()
        end
        if self._wtMicroBtn and self._wtVoiceBtn then
            self._wtMicroBtn:Visible()
            self._wtVoiceBtn:Visible()

            local microType=Module.GVoice:GetMicrophoneButtonType()
            local micIcon = Module.GVoice:GetMicrophoneButtonImagePath(microType)
            if self._wtMicroIcon then
                self._wtMicroIcon:AsyncSetImagePath(micIcon)
            end

            local voiceType=Module.GVoice:GetSpeakerButtonType()
            local voiceIcon = Module.GVoice:GetSpeakerButtonImagePath(voiceType)
            if self._wtVoiceIcon then
                self._wtVoiceIcon:AsyncSetImagePath(voiceIcon)
            end
        end
        if self._wtChatCanvas then
            self._wtChatCanvas:SelfHitTestInvisible()
        end
        if self._wtChatBtn then
            self._wtChatBtn:Collapsed()
        end
    else
        if self._wtChatCanvas then
            self._wtChatCanvas:Collapsed()
        end
        if self._wtChatBtn then
            self._wtChatBtn:Collapsed()
        end
        if self._wtMicroBtn and self._wtVoiceBtn then
            self._wtMicroBtn:Collapsed()
            self._wtVoiceBtn:Collapsed()
        end
        if self._wtChatWidget then
            self._wtChatWidget:SelfHitTestInvisible()
            -- local operaPanel=self._wtChatWidget:Wnd("MsgOperationPanel",UIWidgetBase)
            -- local msgPanel=self._wtChatWidget:Wnd("MsgHistoryPanel",UIWidgetBase)
            -- if operaPanel then
            --     operaPanel.Slot:SetPosition(FVector2D(40,-420))
            -- end
            -- if msgPanel then
            --     msgPanel.Slot:SetPosition(FVector2D(40,-420))
            -- end
        end

    end

    if playerNum<=1 then
        if playerWidget then
            --playerWidget:SetMicroImageVisible(false)
        end
    end
    

end

--刷新队友名字标签麦的状态
function AssemblySquadPick:_OnGVoiceRoomMemberRoomStateChange(_,_,memberId,GVoiceType)
    loginfo("AssemblySquadPick:_OnGVoiceRoomMemberRoomStateChange",memberId,GVoiceType)
    for k,v in pairs(self._playerWidgetList)do
        if v._playerId and tostring(v._playerId)==tostring(memberId) then
            local microType=nil
            if GVoiceType==EGVoiceRoomMemberState.kCompleteCodeRoomMemberMicOpen then
                microType=EGVoiceButtonType.All
            else
                microType=EGVoiceButtonType.Close
            end
                
            v:SetMicroImage(microType)
        end
    end
end

--刷新自己名字标签麦的状态
function AssemblySquadPick:_RefreshSelfLabelMicroType(microType)
    loginfo("AssemblySquadPick:_RefreshSelfLabelMicroType",microType)
    for k,v in pairs(self._playerWidgetList)do
        if v._playerId and tostring(v._playerId)==tostring(Server.AccountServer:GetPlayerId()) then
            v:SetMicroImage(microType)
        end
    end
end

--根据player_idx字段对list重新排序，自己放在第一位
function AssemblySquadPick:_SortListByPlayerIdx(inList)
    table.sort(inList,function(a,b)return a.player_idx<b.player_idx end)
    local newList={}
    for k,v in pairs(inList)do
        if v.player_id ==Server.AccountServer:GetPlayerId()then
            table.insert(newList,1,v)
        else
            table.insert(newList,v)
        end
    end
    return newList

end

function AssemblySquadPick:WorldSpace2WidgetSpace(worldLoc, parentWidget,index)
    worldLoc=worldLoc or {}
    local playerCtrl = GameplayStatics.GetPlayerController(GetWorld(), 0)
    local screenLoc = Module.LobbyDisplay.ProjectWorldToScreen(playerCtrl, worldLoc, true)
    local uiLoc = SlateBlueprintLibrary.ScreenToWidgetLocal(GetWorld(), parentWidget:GetCachedGeometry(), screenLoc, nil)
    
    return uiLoc
end

function AssemblySquadPick:_OnOpenChat()
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        local curInputType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        if (curInputType == EGPInputType.Gamepad) and (self._wtChatWidget.bInOperation) then
            self._wtChatWidget:HideMsgOperationPanel()
            return
        end

        if not self._wtChatWidget.navGroupContext then
            self._wtChatWidget.navGroupContext = self._wtNavGroup
        end
    end
    --- END MODIFICATION

    --if not self._wtChatWidget:IsVisible() and not self._wtSkillInfoPanel:IsVisible() then
        loginfo("AssemblySquadPick:_OnOpenChat")
        self._wtChatWidget:ShowMsgOperationPanel()
    --end
end

function AssemblySquadPick:_ShowSkillPanel()
    loginfo("AssemblySquadPick:_ShowSkillPanel")
    self:HideUseTip()
    local heroItem=self:GetHeroItemByInstId(self.previewedHeroInsId)
    local heroId=heroItem and heroItem:GetHeroId() or 0
    Module.Hero:ShowHeroSkillPanel(nil,heroId,CreateCallBack(self._OnSkillPanelHide, self),IsHD())
    
end

function AssemblySquadPick:_OnSkillPanelHide()
    loginfo("AssemblySquadPick:_OnSkillPanelHide")
    self:TryShowUseTip()
    

end

function AssemblySquadPick:_ActionDisplay()
end

function AssemblySquadPick:_DoCloseUI()
    loginfo("AssemblySquadPick:_DoCloseUI")
    Facade.UIManager:CloseUI(self)
end

function AssemblySquadPick:InitSceneDisplayCtrlType()
    loginfo("AssemblySquadPick:InitSceneDisplayCtrlType")
    local matchModeInfo=nil
    local matchCtrlType=EMatchCtrlType.MP_MatchCtrl
    if Server.AccountServer:IsInRoom() then
        matchModeInfo=Server.RoomServer:GetRoomMode()
        if matchModeInfo then
            if matchModeInfo.game_mode==MatchGameMode.TDMGameMode then
                matchCtrlType=EMatchCtrlType.MP_MatchCtrl
            else
                matchCtrlType=EMatchCtrlType.SOL_MatchCtrl
            end
        else
            matchCtrlType=EMatchCtrlType.MP_MatchCtrl
            logerror("AssemblySquadPick:InitSceneDisplayCtrlType room MatchModeInfo is nil!!!")
        end
    else
        if Facade.GameFlowManager:GetCurrentGameFlow()==EGameFlowStageType.Lobby then
            matchCtrlType=EMatchCtrlType.MP_MatchCtrl

        elseif Facade.GameFlowManager:GetCurrentGameFlow()==EGameFlowStageType.SafeHouse then
            matchCtrlType=EMatchCtrlType.SOL_MatchCtrl
        else
            matchCtrlType=EMatchCtrlType.MP_MatchCtrl
            logerror("AssemblySquadPick:InitSceneDisplayCtrlType curArmedForceMode not valid!!!")
        end
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetMatchCtrlType", matchCtrlType)
    
end

function AssemblySquadPick:GetCardPathByHeroId(heroId)
    loginfo("AssemblySquadPick:GetCardPathByHeroId",heroId)
    return HeroHelperTool.GetSelectedHeroCardPath(tostring(heroId))
end

function AssemblySquadPick:GetCardPathByCardId(cardId)
    loginfo("AssemblySquadPick:GetCardPathByCardId",cardId)
    return HeroHelperTool.GetHeroCardPathByCardId(cardId)
end

function AssemblySquadPick:FindCardIdFromAccessories(accessories)
    loginfo("AssemblySquadPick:FindCardIdFromAccessories")
    for key, accessory in pairs(accessories or {}) do
        if ItemHelperTool.GetSubTypeById(accessory.item.prop_id) == EHeroAccessroy.Card then
            return accessory.item.prop_id
        end
    end
end

function AssemblySquadPick:LoadImageFromCardInfo(cardInfo)
    if cardInfo then
        if cardInfo.player_id==Server.AccountServer:GetPlayerId()then
            local cardPath=self:GetCardPathByHeroId(cardInfo.hero_id)
            if cardPath then
                if Facade.UIManager.stubUIImageRes then
                    Facade.ResourceManager:AsyncLoadResource(Facade.UIManager.stubUIImageRes, cardPath)
                else
                    logerror("AssemblySquadPick:LoadImageFromCardInfo, stubUIImageRes is nil!!!")
                end
            else
                logerror("AssemblySquadPick:LoadImageFromCardInfo, cardPath is nil!!!","heroId",cardInfo.hero_id)
            end
        else
            local cardId=self:FindCardIdFromAccessories(cardInfo.accessories)
            local cardPath=self:GetCardPathByCardId(cardId)
            if cardPath then
                if Facade.UIManager.stubUIImageRes then
                    Facade.ResourceManager:AsyncLoadResource(Facade.UIManager.stubUIImageRes, cardPath)
                else
                    logerror("AssemblySquadPick:LoadImageFromCardInfo, stubUIImageRes is nil!!!")
                end
            else
                logerror("AssemblySquadPick:LoadImageFromCardInfo, cardPath is nil!!!","cardId",cardId)
            end
        end
    else
        logerror("AssemblySquadPick:LoadImageFromCardInfo, cardInfo is nil!!!")
    end
end

function AssemblySquadPick:GetHeroListItemByInstId(instId)
    local weakInst=Facade.UIManager:GetSubUI(self,UIName2ID.HeroMainViewHeroListItem,instId)
    local uiInst=getfromweak(weakInst)
    return uiInst
end

function AssemblySquadPick:GetHeroItemByInstId(instId)
    for k,v in pairs(self._heroArmedId2HeroInsIds or {})do
        local heroListItem=self:GetHeroListItemByInstId(k)
        local heroItem=heroListItem and heroListItem:GetHeroItem(instId)
        if heroItem then
            return heroItem
        end
    end
end

function AssemblySquadPick:GetInsIdListByHeroId(heroId)
    local insIdList={}
    for k,v in pairs(self._heroInsIdList or {})do
        local insIdStr=tostring(v)
        local heroIdStr=tostring(heroId)
        if string.sub(insIdStr,1,string.len(heroIdStr))==heroIdStr then
            table.insert(insIdList,v)
        end
    end
    return insIdList
end

function AssemblySquadPick:OnAnimFinished(anim)
    loginfo("AssemblySquadPick:OnAnimFinished")
    if anim==self.WBP_Hero_MainPanel_in or anim==self.WBP_Hero_MainPanel_Pc_in then--ui首次打开的时候尝试滚动初始化一次
        loginfo("AssemblySquadPick:OnAnimFinished, anim in")
        if not self.scrollInitTriggeredOnce then
            self.scrollInitTriggeredOnce=true
            if self.selectedHeroInsId then
                self:TryScrollHeroItemIntoView(self:GetHeroItemByInstId(self.selectedHeroInsId))
            end
        end
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
function AssemblySquadPick:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        self.ScrollBox_70 = self:Wnd("ScrollBox_70", UIWidgetBase)
        self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self.ScrollBox_70, self, "Hittest")
        if self._wtNavGroup then
            self._wtNavGroup:AddNavWidgetToArray(self.ScrollBox_70)
            self._wtNavGroup:SetScrollRecipient(self.ScrollBox_70)
            --self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            Timer.CancelDelay(self._focusDefaultItemDelayHandle)
            local heroInsIdLen=math.min(self._heroInsIdList and #self._heroInsIdList or 0,20)
            local delayTime=math.max(heroInsIdLen*0.03+0.05,0.5)
            self._focusDefaultItemDelayHandle=Timer.DelayCall(delayTime,function()--delay一下，heroItem的showAnim会设置Collapsed导致focus不到
                local heroItem=self:GetPreviewHeroItem()
                if heroItem then
                    WidgetUtil.SetUserFocusToWidget(heroItem,false)
                end
             
            end)

        end
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

        self:_InitShortcuts()
    else
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

        self:_RemoveShortcuts()
    end
end

function AssemblySquadPick:_InitShortcuts()
    self._ConfirmChangeHero = self:AddInputActionBinding("SelectionOfficers", EInputEvent.IE_Pressed, self.ChangeHero,self, EDisplayInputActionPriority.UI_Stack)
    local armedTabSwitchKeyLeft=self._wtHeroArmedTabGroup:Wnd("WBP_GamepadKeyIconBox_Left",UIWidgetBase)
    local armedTabSwitchKeyRight=self._wtHeroArmedTabGroup:Wnd("WBP_GamepadKeyIconBox_Right",UIWidgetBase)
    if armedTabSwitchKeyLeft then
        armedTabSwitchKeyLeft:InitKeyIcon("Common_LeftShoulder_Gamepad")
    end
    if armedTabSwitchKeyRight then
        armedTabSwitchKeyRight:InitKeyIcon("Common_RightShoulder_Gamepad")
    end
    self._switchPreTabHandle = self:AddInputActionBinding("Common_LeftShoulder_Gamepad", EInputEvent.IE_Pressed, self.SwitchPrevTab,self, EDisplayInputActionPriority.UI_Stack)
    self._switchNextTabHandle = self:AddInputActionBinding("Common_RightShoulder_Gamepad", EInputEvent.IE_Pressed, self.SwitchNextTab,self, EDisplayInputActionPriority.UI_Stack)
end

function AssemblySquadPick:_RemoveShortcuts()
    if self._ConfirmChangeHero then
        self:RemoveInputActionBinding(self._ConfirmChangeHero)
        self._ConfirmChangeHero= nil
    end
    if self._switchPreTabHandle then
        self:RemoveInputActionBinding(self._switchPreTabHandle)
        self._switchPreTabHandle= nil
    end
    if self._switchNextTabHandle then
        self:RemoveInputActionBinding(self._switchNextTabHandle)
        self._switchNextTabHandle= nil
    end
end

function AssemblySquadPick:SwitchPrevTab()
    local curIndex=self._wtHeroArmedTabGroup:GetCurSelectedIndex()
    self._wtHeroArmedTabGroup:SetTabIndex(math.max(curIndex-1,0))
end

function AssemblySquadPick:SwitchNextTab()
    local curIndex=self._wtHeroArmedTabGroup:GetCurSelectedIndex()
    local maxIndex=self._wtHeroArmedTabGroup:GetMaxIndex()
    self._wtHeroArmedTabGroup:SetTabIndex(math.min(curIndex+1,maxIndex))
end



function AssemblySquadPick:_SetPlayerPlatfomIcon(id, playerindex ,playerWidget)
    -- 判断是否为AI
    local isAI = false
    if id == 0 or (self._cardInfos[playerindex] and self._cardInfos[playerindex].is_bot) then
        isAI = true
    end
    logerror("AssemblySquadPick:_SetPlayerPlatfomIcon",id,playerindex,playerWidget,isAI)
    if isAI and Server.AccountServer:IsCrossPlat() then
        -- ai默认设置为PC平台
        playerWidget:SetPlatformIDType(PlatIDType.Plat_PC)
        if self._cardInfos[playerindex] then
            self._cardInfos[playerindex].plat_id = PlatIDType.Plat_PC
        end
        return
    end

    local req = pb.CSAccountGetPlayerProfileReq:New()
    req.player_id = id
    req.client_flag = 0

    req:Request(function(res)
        if not self:IsValid() then
            return
        end

        if res.plat_id ~= nil  then
            -- 设置对应的平台标识
            playerWidget:SetPlatformIDType(res.plat_id)
            -- 记录到对应cardInfo
            if self._cardInfos[playerindex].player_id == id then
                self._cardInfos[playerindex].plat_id = res.plat_id
            end
        end
    end, {bEnableHighFrequency = true})
end
-- END MODIFICATION

local HeroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData")

function AssemblySquadPick:SetCharacterAvatarWithSlotId(SlotId, AvatarId)
    loginfo("AssemblySquadPick:SetCharacterAvatarWithSlotId","SlotId",SlotId,"AvatarId",AvatarId)
    -- local SlotCharacter = Facade.HallSceneManager:CallSceneCharacterCtrlFunctionBySubstage(ESubStage.HallMatch, "GetPlayerCharacterBySlot", SlotId)
    -- if SlotCharacter ~= nil then
    --     local ReplaceHeroID = SlotCharacter:IsAvatarPakExist(AvatarId)
    --     -- 返回不为0，即返回了对应的HeroID，此时原AvatarId指向的资源不存在
    --     if ReplaceHeroID ~= 0 then
    --         for _, v in ipairs(HeroFashionDataTable) do
    --             if v.BelongedHeroID == ReplaceHeroID and v.IsDefEquip then
    --                 AvatarId = v.FashionID
    --                 break
    --             end
    --         end
    --     end
    -- end
    
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "ResetCharacterSequence", SlotId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetCharacterAvatarWithSlot", SlotId, AvatarId)
end

function AssemblySquadPick:GetPreviewHeroItem()
    local defaultHeroId=nil
    for k,v in pairs(self._solRoomTeamRes and self._solRoomTeamRes.player_info_array or {})do
        if v.player_id==Server.AccountServer:GetPlayerId() then
            defaultHeroId=v.hero_info and v.hero_info.hero_id
            break
        end
    end
    local insIdList=self:GetInsIdListByHeroId(defaultHeroId)
    local defaultInsId=insIdList and insIdList[1]
    local heroInsId=self.previewedHeroInsId or defaultInsId
    local heroItem=self:GetHeroItemByInstId(heroInsId)
    loginfo("AssemblySquadPick:GetPreviewHeroItem",heroInsId,heroItem)
    return heroItem
end

--BEGIN MODIFICATION @ VIRTUOS : 
function AssemblySquadPick:UpdatePS5OnlineIdsByPlayerInfo(playerInfoArray)
    if IsPS5Family() and playerInfoArray ~= nil then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager then
            for k, v in pairs(playerInfoArray) do
               DFMOnlineIdentityManager:AddPendingUpdateOpenId(v.player_id)
            end
            DFMOnlineIdentityManager:BeginUpdatePendingOpenId()
        end
    end
end

function AssemblySquadPick:GetPS5OnlineIdByOpenId(openId)
    if IsPS5Family() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager then
            local PS5OnlineId = DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(openId)
            if not string.isempty(PS5OnlineId) then
                return PS5OnlineId
            end
        end
    end

    return nil
end

function AssemblySquadPick:HandleOnlineIdCacheUpdate()
    if IsPS5Family() and self:IsVisible() then
        for key, value in pairs(self._playerWidgetList) do
            if value then
                value:SetPlayerNameToPS5OnlineId()
            end
        end
    end
end
--END MODIFICATION

return AssemblySquadPick
