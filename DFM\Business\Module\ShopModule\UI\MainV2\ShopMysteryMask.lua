----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMShop)
----- LOG FUNCTION AUTO GENERATE END -----------



local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local UDFMGameHudDelegates = import "DFMGameHudDelegates"

---@class ShopMysteryMask : LuaUIBaseView
local ShopMysteryMask = ui("ShopMysteryMask")

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

---1. 读表然后计算倒计时
function ShopMysteryMask:Ctor()
    --倒计时
    self._wtCountdowntxt = self:Wnd("DFTextBlock_CountDown",UITextBlock)
    self._wtCountdownTipCheckBox = self:Wnd("WBP_DFCommonCheckInstruction", DFCheckBoxOnly)
    self._wtCountdownTipAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_60", self._OnShowCDTips, self._OnHideCDTips)
    if DFHD_LUA == 1 then
        self._wtCountdownTipCheckBox:ClearSound()
    else
        self._wtCountdownTipCheckBox:SetCallback(self._OnCheckStateChanged, self)
    end
    -- self._wtCDTipCheckBox = self:Wnd("WBP_DFCommonCheckInstruction", DFCheckBoxOnly)
    -- self._wtCDTipCheckBox:Event("OnCheckStateChanged",self._OnStateChanged, self)
    self._raffleBtn = self:Wnd("WBP_DFCommonButtonV1S1",DFCommonButtonOnly)
    if self._raffleBtn then self._raffleBtn:Collapsed() end
    -- self._raffleBtn:Event("OnClicked", self.OnProcessRaffleBtnClicked, self)
    -- self._raffleBtn:Event("OnDeClicked", self.OnProcessRaffleBtnDeClicked, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    -- if IsHD() then
    --     self._raffleBtn:SetDisplayInputAction("Assembly_Equip_Gamepad", true, nil, true)
    -- end
    --- END MODIFICATION

    self._maskBG = self:Wnd("CanvasPanel_42", LuaUIBaseView)
    self._fullCanvas = self:Wnd("CanvasPanel_13", LuaUIBaseView)
    self._mysteryCD = 0
end

function ShopMysteryMask:OnInitExtraData()
    self:AddCountDownTimer()
    self:FreshMysteryShopItems()
    self:UpdateMysteryReddot()
end

function ShopMysteryMask:OnDeactivate()
    if self.timerHandler then
        self.timerHandler:Release()
        self.timerHandler = nil
    end
end

function ShopMysteryMask:OnShowBegin()
    self:AddLuaEvent(Module.Shop.Config.evtCurMerchanLabelIdChanged, self.OnCurLabelChanged, self)
    self:PlayMaskAnim(self.WBP_Shop_Mask_in)
    self:SetShowForm()
    self:FreshTipCheck()
    if DFHD_LUA == 0 then
        UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self._OnHandleMouseButtonUpEvent, self)    
    end
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    --- END MODIFICATION
end

function ShopMysteryMask:UpdateMysteryReddot()
    -- 神秘商人红点显示则关闭红点
    local reddotData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Shop, "DoRaffle")
    if reddotData and reddotData.reddotFlag then
        Timer.DelayCall(1, function ()
            local timeStampList = Server.TipsRecordServer:GetArray(Server.TipsRecordServer.keys.ShopMysteryRaffleHintShownMap, 1)
            timeStampList = setdefault(timeStampList, {})
            local curTimeStamp = Facade.ClockManager:GetLocalTimestamp()
            --去重
            for _, timeStamp in ipairs(timeStampList) do
                if tonumber(curTimeStamp) == tonumber(timeStamp) then
                    return
                end
            end
            table.insert(timeStampList, curTimeStamp)
            Server.TipsRecordServer:SetArray(Server.TipsRecordServer.keys.ShopMysteryRaffleHintShownMap, timeStampList) --存当前时间戳和显示情况
        end)
    end
end


function ShopMysteryMask:OnHideBegin()
    if DFHD_LUA == 0 then
        UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
    end
    self:RemoveLuaEvent(Module.Shop.Config.evtCurMerchanLabelIdChanged, self.OnCurLabelChanged)
    -- LuaTickController:Get():RemoveTick(self)
end

function ShopMysteryMask:OnClose()
    if self.timerHandler then
        self.timerHandler:Release()
        self.timerHandler = nil
    end
    
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_DisableGamepadInputs()
    end
    --- END MODIFICATION
end

function ShopMysteryMask:OnCurLabelChanged()
    self._isMystery = Module.Shop.Field:GetIsMystery()
    if not self._isMystery then
        self:StopAnimation(self.WBP_Shop_Mask_in)
        self:StopAnimation(self.WBP_Shop_Mask_loop)
        self:StopAnimation(self.WBP_Shop_Mask_yfout)
    end
end

function ShopMysteryMask:FreshTipCheck()
    self:_OnHideCDTips()
    if self._wtCDTipCheckBox then
        self._wtCDTipCheckBox:SetIsChecked(false)
    end
end

function ShopMysteryMask:AddCountDownTimer()
    self._mysteryCD = self:GetCountDownRefreshTimeStamp()
    if self.timerHandler then
        self.timerHandler:Release()
        self.timerHandler = nil
    end
    self.timerHandler = Timer:NewIns(1, 0 )
    self.timerHandler:AddListener(self.CountTime, self)
    self.timerHandler:Start()
    -- LuaTickController:Get():RegisterTick(self, 1)
    
end

function ShopMysteryMask:FreshMysteryShopItems()
    local checkFCallback = function (nextPeriod)
        local serverTimeStamp = Facade.ClockManager:GetLocalTimestamp()
        local cd = nextPeriod - serverTimeStamp
        if cd ~= self._mysteryCD then
            loginfo(string.format("ShopMysteryMask:AddCountDownTimer() ServerCD = %s myCD = %s", cd, self._mysteryCD))
            self._mysteryCD = cd
        end
        self:SetCountDownText(self._mysteryCD)
        self:SetShowForm()
    end
    Server.ShopMysteryServer:FetchMysteryData(checkFCallback)
end

function ShopMysteryMask:SetShowForm()
    local mysteryState = Server.ShopMysteryServer:GetMysteryState()
    if mysteryState == EMysteryState.Raffled then
        self:SetMaskBGVisible(false) --只保留倒计时
    else
        self:SetMaskBGVisible(true)
    end
end

function ShopMysteryMask:CountTime(dt)
    self:SetCurCD()
    self:SetCountDownText(self._mysteryCD)
end

function ShopMysteryMask:SetCurCD()
    if self._mysteryCD == 0 and not self.isCDUp then
        self.isCDUp = true
        self._mysteryCD = self:GetCountDownRefreshTimeStamp()
        local fCallback = CreateCallBack (function (self,nextPeriod)
            local serverTimeStamp = Facade.ClockManager:GetLocalTimestamp()
            local cd = nextPeriod - serverTimeStamp
            if self._mysteryCD ~= cd then
                self._mysteryCD = cd
                self:SetCountDownText(self._mysteryCD)
            end
            Module.Shop.Config.evtShopMysteryCDUp:Invoke()
            self:CDUp()
            self:UpdateMysteryReddot() --在倒计时到了之后再刷新红点, 在事件之后
        end, self)
        Server.ShopMysteryServer:FetchMysteryData(fCallback)
    elseif self._mysteryCD > 0 then
        self._mysteryCD = self._mysteryCD - 1
    end

    if self._mysteryCD ~= 0 and self.isCDUp then
        self.isCDUp = false
    end
end

function ShopMysteryMask:CDUp()
    self:SetShowForm()
    self:PlayMaskAnim(self.WBP_Shop_Mask_in)
    -- self:PlayMaskAnim(self.WBP_Shop_Mask_loop, self.WBP_Shop_Mask_in:GetEndTime(), 0, EUMGSequencePlayMode.Forward, 1)
    self:Reset()
end

function ShopMysteryMask:OnShow()
    self:Reset()
end

function ShopMysteryMask:OnAnimFinished(anim)
    --开始进入播放loop动画,直到抽奖了
    if anim == self.WBP_Shop_Mask_in then
        -- self:PlayMaskAnim(self.WBP_Shop_Mask_loop, 0, 0, EUMGSequencePlayMode.Forward, 1)
        self:AutoRaffle()
    elseif anim == self.WBP_Shop_Mask_yfout then
        self:SetMaskBGVisible(false)
    end
end

function ShopMysteryMask:OnHide()
    self:Reset()
end

function ShopMysteryMask:AutoRaffle()
    local mysteryState = Server.ShopMysteryServer:GetMysteryState()
    if mysteryState == EMysteryState.NotRaffled then
        self:OnProcessRaffleBtnClicked()
    end
end

function ShopMysteryMask:Reset()
    self:_OnHideCDTips()
    -- self._raffleBtn:SetBtnEnable(true)
    self._isShownTip = false
end

function ShopMysteryMask:OnProcessRaffleBtnClicked()
    local fCloseAnimCallback = function (res)
        if res.result == 0 then
            local nextPeriod = res.count_down_seconds
            local serverTimeStamp = Facade.ClockManager:GetLocalTimestamp()
            local cd = nextPeriod - serverTimeStamp
            if cd and self._mysteryCD ~= cd then
                self._mysteryCD = cd
                self:SetCountDownText(self._mysteryCD)
            end
        end
        Module.Shop.Config.evtShopMysteryDoRaffle:Invoke()
        self:PlayMaskAnim(self.WBP_Shop_Mask_yfout)
    end
    Server.ShopMysteryServer:DoReqRaffleMysteryShopItems(fCloseAnimCallback)
    -- self._raffleBtn:SetBtnEnable(false)

    --埋点上报
    LogAnalysisTool.SignButtonClicked(ButtonIdEnum.ShopMysteryRaffle)
end

function ShopMysteryMask:SetMaskBGVisible(isVisible)
    self._fullCanvas:SetVisibility(isVisible and ESlateVisibility.Visible or ESlateVisibility.SelfHitTestInvisible)
    self._maskBG:SetVisibility(isVisible and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
    
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_SetRaffleBtnGamepadInput(isVisible)
    end
    --- END MODIFICATION
end

--设置倒计时文本
function ShopMysteryMask:SetCountDownText(cd)
    if self._wtCountdowntxt then
        local countdown = TimeUtil.GetSecondsFormatHHMMSSString(cd)
        self._wtCountdowntxt:SetText(string.format(Module.Shop.Config.Loc.RefreshTime, countdown))
    end
end

-----------------------------------------------------------------------
--region 播放动画

function ShopMysteryMask:PlayMaskAnim(anim, startTime, loopNumer, playMode, speedScale)
    startTime = setdefault(startTime, 0)
    loopNumer = setdefault(loopNumer, 1)
    playMode = setdefault(playMode, EUMGSequencePlayMode.Forward)
    speedScale = setdefault(speedScale, 1)
    -- if self.curAnim then
    --     self:StopWidgetAnim(self.curAnim)
    --     self.curAnim = nil
    -- end
    self:PlayAnimation(anim, startTime, loopNumer, EUMGSequencePlayMode.Forward, 1, true)
    -- self.curAnim = anim
end

function ShopMysteryMask:GetRefreshTimeStampSec()
    local refreshTable = Module.Shop.Config.ShopMysteryRefresh
end

function ShopMysteryMask:GetCountDownRefreshTimeStamp()
    local refreshTable = Module.Shop.Config.ShopMysteryRefresh
    local refreshTimeStampList = {}
    local curTimeStamp = Facade.ClockManager:GetLocalTimestamp()
    for _, setting in pairs(refreshTable) do
        local currYear = os.date("%Y", curTimeStamp)
        local currMonth = os.date("%m", curTimeStamp)
        local currDay = os.date("%d", curTimeStamp)
        if setting.FirstRefreshTime then
            local firstRefreshTimeStrList = string.split(setting.FirstRefreshTime, ":")
            if #firstRefreshTimeStrList == 2 then
                local firstRefreshHour = tonumber(firstRefreshTimeStrList[1])
                local firstRefreshMin = tonumber(firstRefreshTimeStrList[2])
                local firstRefreshTimestamp = os.time{year = currYear, month = currMonth, day = currDay, hour = firstRefreshHour, min = firstRefreshMin, sec = 0}
                table.insert(refreshTimeStampList, firstRefreshTimestamp)
            end
        end
        if setting.secondRefreshTime then
            local secondRefreshTimeStrList = string.split(setting.secondRefreshTime, ":")
            if #secondRefreshTimeStrList == 2 then
                local secondRefreshHour = tonumber(secondRefreshTimeStrList[1])
                local secondRefreshMin = tonumber(secondRefreshTimeStrList[2])
                local secondRefreshTimestamp = os.time{year = currYear, month = currMonth, day = currDay, hour = secondRefreshHour, min = secondRefreshMin, sec = 0}
                table.insert(refreshTimeStampList, secondRefreshTimestamp)
            end
        end
    end
    table.sort(refreshTimeStampList, function (left, right) return left < right end)
    local nextDayFrirstRefreshTimestamp = refreshTimeStampList[1] + 86400
    table.insert(refreshTimeStampList, nextDayFrirstRefreshTimestamp)

    for idx, timestamp in ipairs(refreshTimeStampList) do
        if curTimeStamp < timestamp then
            return timestamp - curTimeStamp
        end
    end
    return 0
end

--endregion
-----------------------------------------------------------------------

--- BEGIN MODIFICATION @ VIRTUOS
function ShopMysteryMask:_InitGamepadInputs()
    if not self:IsVisible() then
        return
    end

    if not self._gamepadSummaryList then
        self._gamepadSummaryList =
        {
            {actionName = "ShopDetail", func = nil, caller = nil, bUIOnly = true},
            {actionName = "ShopDepartmentLevel", func = function ()
                Module.Shop:ShowPrestigeUsagePanel(Module.Shop.Field:GetCurTargetMerchantId())
            end, caller = nil, bUIOnly = false},
            {actionName = "Common_ToggleTip", func = self._ToggleTips, caller = self, bUIOnly = false},
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)
    end
end

function ShopMysteryMask:_DisableGamepadInputs()
    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end

    self:_SetRaffleBtnGamepadInput(false)
end

function ShopMysteryMask:_SetRaffleBtnGamepadInput(enabled)
    if enabled then
        if not self._raffleClickHandle then
            local raffleClick = SafeCallBack(
            function()
                -- if self._raffleBtn:GetIsEnabled() then
                --     self._raffleBtn:ButtonClick()
                -- end
            end, self)

            self._raffleClickHandle = self:AddInputActionBinding("Confirm", EInputEvent.IE_Pressed, raffleClick, self, EDisplayInputActionPriority.UI_Pop)
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
        end
    else
        if self._raffleClickHandle then
            self:RemoveInputActionBinding(self._raffleClickHandle)
            WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
            self._raffleClickHandle = nil
        end
    end
end
--- END MODIFICATION

-----------------------------------------------------------------------
--region 设置anchor

function ShopMysteryMask:_OnShowCDTips()
    self:_OnHideCDTips()
    self._cdTipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(Module.Shop.Config.Loc.ShopMysteryCDTip, self._wtCountdownTipAnchor)
end

function ShopMysteryMask:_OnHideCDTips()
    -- 解绑Anchor
    if self._cdTipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._cdTipHandle, self._wtCountdownTipAnchor)
        self._cdTipHandle = nil
    end
end

function ShopMysteryMask:_OnHandleMouseButtonUpEvent(mouseEvent)
    local absolutePoint = mouseEvent:GetScreenSpacePosition()
    if self._cdTipHandle then
        local bInsideTipBtn = UIUtil.CheckAbsolutePointInsideWidget(self._cdTipHandle:GetUIIns(), absolutePoint)
        local bInsideCheckBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtCountdownTipCheckBox, absolutePoint)
        if not bInsideTipBtn and not bInsideCheckBox then
            self._wtCountdownTipCheckBox:SetIsChecked(false)
        end
    end
end

function ShopMysteryMask:_OnCheckStateChanged(bChecked)
    if bChecked then
        self:_OnShowCDTips()
    else
        self:_OnHideCDTips()
    end
end

function ShopMysteryMask:_ToggleTips()
    if not IsHD() then
        return 
    end
    if self._wtCountdownTipCheckBox then
        if self._wtCountdownTipCheckBox:GetIsChecked() then
            self._wtCountdownTipCheckBox:SetIsChecked(false, false)
            self:_OnHideCDTips()
        else
            self._wtCountdownTipCheckBox:SetIsChecked(true, false)
            self:_OnShowCDTips()
        end
    end
end

--endregion
-----------------------------------------------------------------------

return ShopMysteryMask