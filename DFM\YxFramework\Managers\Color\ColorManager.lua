----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ColorManager : ManagerBase
local ColorManager = class("ColorManager", require "DFM.YxFramework.Managers.ManagerBase")

---------------------------------------------------------------------------------------------------------------
--- 颜色管理器
--- [ColorManager]
---------------------------------------------------------------------------------------------------------------

function ColorManager:Ctor()
    loginfo("ColorManager:Ctor")
    ---@type table<ColorKey, FLinearColor>
    self._cachedLinerColorMap = {}
    self._cachedSlateColorMap = {}
    self._cachedImageColorKey2RowName = {}
    self._cachedTextColorKey2RowName = {}
end

-- function ColorManager:OnGameFlowChangeEnter(gameFlowType)
--     if gameFlowType == EGameFlowStageType.Login then
--         self:PreLoadAssetByName()
--     end
-- end

--- 预加载Color配置表
-- function ColorManager:PreLoadAssetByName()
--     self._cachedLinerColorMap = {}
--     self._cachedSlateColorInfo = {}
--     local linerColorConfigTable = Facade.TableManager:GetTable("ColorStyleTable_Image")
--     if linerColorConfigTable then
--         for k, v in pairs(linerColorConfigTable) do
--             self._cachedLinerColorMap[v.ColorKey] = v.ColorAndOpacity
--         end
--     end

--     local slateColorConfigTable = Facade.TableManager:GetTable("ColorStyleTable_Text") --SlateColor
--     if slateColorConfigTable then
--         for k,v in pairs(slateColorConfigTable) do
--             self._cachedSlateColorInfo[v.ColorKey] = {
--                 -- slateColor = v.ColorAndOpacity,
--                 outlineSetting = v.OutlineSettings,
--                 shadowOffset = v.ShadowOffset,
--                 shadowColorAndOpacity = v.ShadowColorAndOpacity,
--                 margin = v.Margin,
--                 lineHeightPercentage = v.LineHeightPercentage
--             }
--             self._cachedSlateColorMap[v.ColorKey] = v.ColorAndOpacity
--         end
--     end
-- end

function ColorManager:Destroy()
	loginfo("ColorManager:Destroy")
    self._cachedLinerColorMap = {}
    self._cachedSlateColorMap = {}
    self._cachedImageColorKey2RowName = {}
    self._cachedTextColorKey2RowName = {}
end

function ColorManager:GetLinerColor(colorKey, opacity)
    local rowName = self:GetLinerRowNameByColorKey(colorKey)
    if rowName ~= "" then
        return self:GetLinerColorByRowName(rowName, opacity)
    end
    return FLinearColor()
end

---@field opacity number 透明度（-1表示默认读表）
function ColorManager:GetLinerColorByRowName(rowName, opacity)
    rowName = setdefault(rowName,"")
    opacity = setdefault(opacity, -1)
    if rowName ~= "" then
        local key = rowName..'_'..opacity
        if self._cachedLinerColorMap[key] ~= nil then
            return self._cachedLinerColorMap[key]
        else
            local linerColor = self:GetDefaultLinerColorByRowName(rowName)
            if opacity ~= -1 then
                linerColor.A = opacity
            end
            self._cachedLinerColorMap[key] = linerColor
            return self._cachedLinerColorMap[key]
        end
    end
    return FLinearColor()
end

function ColorManager:GetSlateColor(colorKey, opacity)
    local rowName = self:GetSlateRowNameByColorKey(colorKey)
    if rowName ~= "" then
        return self:GetSlateColorByRowName(rowName, opacity)
    end
    return FSlateColor()
end

---@field opacity number 透明度（-1表示默认读表）
function ColorManager:GetSlateColorByRowName(rowName, opacity)
    rowName = setdefault(rowName,"")
    opacity = setdefault(opacity, -1)
    if rowName ~= "" then
        local key = rowName..'_'..opacity
        if self._cachedSlateColorMap[key] ~= nil then
            return self._cachedSlateColorMap[key]
        else
            local originSlateColor = self:GetDefaultSlateColorByRowName(rowName)
            if opacity ~= -1 then
                local linerColor = FLinearColor(self:GetHexBySlateColor(originSlateColor))
                linerColor.A = opacity
                local slateColor = FSlateColor(linerColor)
                self._cachedSlateColorMap[key] = slateColor
            else
                self._cachedSlateColorMap[key] = originSlateColor
            end
            return self._cachedSlateColorMap[key]
        end
    end
    return FSlateColor()
end

-----------------------------------------------------------------------
--region Getter: TextColor相关配置信息

function ColorManager:GetTextColorInfoByKey(colorKey)
    if colorKey and self._cachedSlateColorInfo then
        return self._cachedSlateColorInfo[colorKey]
    end
end

function ColorManager:GetTextColorOutlineSetting(colorKey)
    local colorInfo = self:GetTextColorInfoByKey (colorKey)
    if colorInfo then
        return colorInfo.outlineSetting
    end
end

function ColorManager:GetTextColorShadowOffset(colorKey)
    local colorInfo = self:GetTextColorInfoByKey (colorKey)
    if colorInfo then
        return colorInfo.shadowOffset
    end
end

function ColorManager:GetTextColorShadowColorAndOpacity(colorKey)
    local colorInfo = self:GetTextColorInfoByKey (colorKey)
    if colorInfo then
        return colorInfo.shadowColorAndOpacity
    end
end

function ColorManager:GetTextColormargin(colorKey)
    local colorInfo = self:GetTextColorInfoByKey (colorKey)
    if colorInfo then
        return colorInfo.margin
    end
end

function ColorManager:GetTextColorLineHeightPercentage(colorKey)
    local colorInfo = self:GetTextColorInfoByKey (colorKey)
    if colorInfo then
        return colorInfo.lineHeightPercentage
    end
end

--endregion
-----------------------------------------------------------------------

function ColorManager:GetLinerRowNameByColorKey(colorKey)
    colorKey = setdefault(colorKey, "")
    if colorKey ~= "" then
        if self._cachedImageColorKey2RowName[colorKey] then
            return self._cachedImageColorKey2RowName[colorKey]
        end

        local linerColorConfigTable = Facade.TableManager:GetTable("ColorStyleTable_Image")
        if linerColorConfigTable then
            for k, v in pairs(linerColorConfigTable) do
                if v.ColorKey == colorKey then
                    self._cachedImageColorKey2RowName[colorKey] = k
                    return k
                end
            end
        end
    end
    return ""
end

function ColorManager:GetSlateRowNameByColorKey(colorKey)
    colorKey = setdefault(colorKey, "")
    if colorKey ~= "" then
        if self._cachedTextColorKey2RowName[colorKey] then
            return self._cachedTextColorKey2RowName[colorKey]
        end
        local slateColorConfigTable = Facade.TableManager:GetTable("ColorStyleTable_Text")
        if slateColorConfigTable then
            for k, v in pairs(slateColorConfigTable) do
                if v.ColorKey == colorKey then
                    self._cachedTextColorKey2RowName[colorKey] = k
                    return k
                end
            end
        end
    end
    return ""
end

function ColorManager:GetDefaultLinerColorByRowName(rowName)
    local linerColorConfigTable = Facade.TableManager:GetTable("ColorStyleTable_Image")
    if linerColorConfigTable[rowName] then
        return linerColorConfigTable[rowName].ColorAndOpacity
    end
    return FLinearColor()
end

function ColorManager:GetDefaultSlateColorByRowName(rowName)
    local slateColorConfigTable = Facade.TableManager:GetTable("ColorStyleTable_Text")
    if slateColorConfigTable[rowName] then
        return slateColorConfigTable[rowName].ColorAndOpacity
    end
    return FSlateColor()
end


function ColorManager:GetHexByKey(colorKey)
    local linerColor = self:_GetLinerColorByKey(colorKey)
    self:GetHexByLinerColor(linerColor)
end

function ColorManager:GetHexByLinerColor(linerColor)
    if linerColor then
        local myColor = linerColor:ToFColor(false)--保持linercolor对象线性颜色不变，不进行颜色值的映射转换
        if myColor then
            return myColor:ToHex()
        else
            return nil
        end
    end
end

function ColorManager:GetHexBySlateColor(slateColor)
    if slateColor then
        return self:GetHexByLinerColor(slateColor:GetSpecifiedColor())
    end
end

return ColorManager