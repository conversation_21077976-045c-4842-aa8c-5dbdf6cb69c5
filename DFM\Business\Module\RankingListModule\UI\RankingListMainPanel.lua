----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RankingListMainPanel : UIWidgetBase
local RankingListMainPanel = ui("RankingListMainPanel")
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local UGPInputDelegates = import "GPInputDelegates"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPInputType = import "EGPInputType"
local CommonSocialTitleItem_Big = require "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.CommonSocialTitleItem_Big"

local RankListTimerTbl = {
    [RankDataType.SOL_SCORE] = false,
    [RankDataType.SOL_BRINGOUT_VALUE] = true,
    [RankDataType.SOL_KILL] = true,
    -- [RankDataType.SOL_REWARD] = true,
    -- [RankDataType.SOL_KILL_BOSS] = true,
    -- [RankDataType.SOL_MANDEL] = true,
    [RankDataType.TDM_SCORE] = false,
    [RankDataType.TDM_KILL] = true,
    [RankDataType.TDM_CAPTURE_ZONE_POINT] = true,
    [RankDataType.TDM_RESCUE_POINT] = true,
    -- [RankDataType.TDM_ENGINEER_POINT] = true,
    -- [RankDataType.TDM_TACTICAL_POINT] = true,
    [RankDataType.TDM_VICTORY_UNIT_SCORE] = false,
}

function RankingListMainPanel:Ctor()

    self._isCN = IsBuildRegionCN()
    self._index = 1             --当前排行榜类型 段位 击败干员...
    self._areaIndex = 1         --国服 战区 好友    
    self._selectIndex = 1
    self._openByOut = false     --是否通过外部打开
    self._bLastViewIsRoleInfo = false
    self._bClickChangeMode = false
    self._logSource = {}
    self._selectWidget = nil
    self._lastSelectIndex = 0
    self._isMp = RoleInfoLogic.IsInMp()
    self._rankList = {}
    self._wtChangModeBtn = self:Wnd("WBP_Reputation_Item", UIWidgetBase):Wnd("DFButton_0", UIButton)
    self._wtChangModeImg = self:Wnd("WBP_Reputation_Item", UIWidgetBase):Wnd("DFImage_36", UIImage)
    self._wtChangModeBtn:Event("OnClicked",self._ModeChangeClick ,self)
    self._wtHistoryBtn = self:Wnd("WBP_Reputation_Item_1", UIWidgetBase)
    self._wtHistoryBtn:Wnd("DFButton_0", UIButton):Event("OnClicked",self._OpenHistory ,self)
    -- if not IsBuildRegionCN() then
    --     self._wtHistoryBtn:Collapsed()
    -- end
    self._wtHistoryBtn:Collapsed()

    self._wtTipsBtn = self:Wnd("WBP_Reputation_Item_2", UIWidgetBase):Wnd("DFButton_0", UIButton)
    self._wtTipsBtn:Event("OnClicked",self._OpenTipsClick ,self)
    self._wtRatingBtn = self:Wnd("WBP_CommonIconButton_1", UIButton)
    self._wtRatingBtn:Event("OnClicked",self._OpenRatingDetails ,self)
    self._wtChangeArea = self:Wnd("WBP_CommonIconButton", UIButton)
    self._wtChangeArea:Event("OnClicked",self._OpenChangeArea ,self)
    self._wtSelfRankInfomation = self:Wnd("wtSelfRankInfomation", UIWidgetBase)
    self._wtAreaPanel = self:Wnd("wtAreaPanel", UIWidgetBase)
    self._wtTitle = self:Wnd("WBP_RoleInfo_TitleItem", CommonSocialTitleItem_Big)

    self._wtCheckInstruction = self:Wnd("wtCommonCheckInstruction", UIWidgetBase)  -- She3:双模式段位排行榜分端手
    --self._wtCheckInstruction:SelfHitTestInvisible()
    self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_88")
    self._tipHandle = nil
    self._tipContent = ""
    self._isNeedInstruction = false

    self._wtScoreText = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtAreaImage = self:Wnd("wtTitle_Bg", UIImage)

    self._RankDataTypeTbl = {}
    self._RankDataType = nil
    self._ProvinceIndex = 1
    self._CityIndex = 1
    self._isOpenChalleng = false
    self._wtProvinceDropDownBox = UIUtil.WndDropDownBox(self, "wtProvinceDropDownBox", self._ChangeProvinceDrop)
    self._wtCityDropDownBox = UIUtil.WndDropDownBox(self, "wtCityDropDownBox", self._ChangeCityDrop)

    self._tabGroupData = {}

    Module.CommonBar:RegStackUITopBarTitle(self, Module.RankingList.Config.Loc.RankingListMainTitle)
    if DFHD_LUA == 1 then
        local ETopBarStyleFlag = Module.CommonBar.Config.ETopBarStyleFlag
        Module.CommonBar:RegStackUITopBarStyle(
                self,
                ETopBarStyleFlag.DefaultSecondary & ~(ETopBarStyleFlag.Team | ETopBarStyleFlag.Friends | ETopBarStyleFlag.Currency)
        )
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end

    self._playerId = Server.AccountServer:GetPlayerId()
    self._selfRankData = Server.RankingListServer:GetSelfRankDataMap()
    self._playerName = ""

    if not self._isCN then
        Server.RankingListServer:SetRankingListCheck()
    end

    if IsHD() then
        self._wtCheckInstruction:Event("OnUnCheckedHovered",self._OnTipShow,self)
        self._wtCheckInstruction:Event("OnCheckedHovered",self._OnTipShow,self)
        self._wtCheckInstruction:Event("OnUnCheckedUnhovered",self._OnTipUnShow,self)
        self._wtCheckInstruction:Event("OnCheckedUnhovered",self._OnTipUnShow,self)
    else
        self:OnClickSpaceEventCallAdd()
        self._wtCheckInstruction:Event("OnUnCheckedClicked",self._OnTipShow,self)
        self._wtCheckInstruction:Event("OnCheckedClicked",self._OnTipUnShow,self)
    end
end

function RankingListMainPanel:GetSelfAdcode()
    self._adcode = Server.RankingListServer:GetAdcode()
    if tonumber(self._adcode) == 0 then
        self._isOpenChalleng = true
        self._adcode = Server.RankingListServer:GetLocalAcode()
    end
    if tonumber(self._adcode)  ~= 0 then
        local ProvinceIndex, CityIndex = Module.RankingList:GetCityProvinceIndexByAreaId(self._adcode, true)
        self._ProvinceIndex = ProvinceIndex
        self._CityIndex = CityIndex
    end
    self._provinceId = 11
    if tonumber(self._adcode)  ~= 0 then
        self._provinceId = tonumber(string.sub(self._adcode, 1, 2))
    end
end

function RankingListMainPanel:CheckAreaIsSelf()
    if (not self._areaId) or tonumber(self._areaId) == 0 then
        return true
    end
    local ProvinceId = string.sub(self._areaId, 1, 2)
    local CityId = string.sub(self._areaId, 3, 4)
    local SelfProvinceId = string.sub(self._adcode, 1, 2)
    local SelfCityId = string.sub(self._adcode, 3, 4)
    if ProvinceId == SelfProvinceId then
        if tonumber(CityId) == 0 or CityId == SelfCityId then
            return true
        else
            return false
        end
    else
        return false
    end
end


function RankingListMainPanel:OnInitExtraData()
    self._openByOut = true
    Module.RankingList.Field:SetSelectIndex(1)
    self._selectIndex = 1
    self._lastSelectIndex = 0
    self._isMp = RoleInfoLogic.IsInMp()
    self._wtRankList = UIUtil.WndScrollGridBox(self, "wtRankList", self._OnGetRankListCount, self._OnProcessRankListWidget)
    self:CreateFriendRankTbl()
    Server.RankingListServer:GetSelfHandBookData()
    Server.RankingListServer:GetRankArea()
    Server.FriendServer:GetFriendDataByPanel()
    self:TrackReport()
end

function RankingListMainPanel:OnShowBegin()
    self:RefreshSelectIndex()
    if self._isCN then
        self:GetSelfAdcode()
        UIUtil.InitDropDownBox(self._wtProvinceDropDownBox, Module.RankingList:GetProvinceTextTbl(), {}, self._ProvinceIndex - 1)
        UIUtil.InitDropDownBox(self._wtCityDropDownBox, Module.RankingList:GetCityTextTbl(self._provinceId), {}, self._CityIndex - 1)
    end
    self:OnSetRankListTab()
    self:ChangeModeIcon()
    self:_RefreshSubTabAreaIndex()
    self:_RefreshCheckInstruction()
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, self._tabGroupData)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self._RefrshRoleMode, self)
    self:AddLuaEvent(Server.RankingListServer.Events.evtGetRankListEnd,self.RefreshRankList, self)
    if not self._inputTypeChangedHandle then
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:_EnableGamepadFeature()
end

function RankingListMainPanel:TrackReport()
    if self._isMp then
        LogAnalysisTool.SignButtonClicked(IsHD() and 10120038 or 20120038)
    else
        LogAnalysisTool.SignButtonClicked(IsHD() and 10030038 or 20030038)
    end
end

function RankingListMainPanel:OnHideBegin()
    Module.RankingList.Field:SetData(self._areaIndex,self._index,self._selectIndex,self._wtRankList:GetScrollOffset())
    self:RemoveAllLuaEvent()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end

function RankingListMainPanel:RefreshSelectIndex()
    local Field = Module.RankingList.Field
    self._lastSelectIndex = Field:GetSelectIndex()
    self._areaIndex = Field:GetAreaIndex()
    self._index = Field:GetIndex()

    if Field:GetLastViewIsRoleMain() then
        self._bLastViewIsRoleInfo = true
        Field:SetLastViewIsRoleMain(false)
    else
        self._bLastViewIsRoleInfo = false
    end
end

function RankingListMainPanel:OnClose()
    if not IsHD() then
        self:OnClickSpaceEventCallRemove()
    end
end

--创建好友排行榜数据
function RankingListMainPanel:CreateFriendRankTbl()
    self._friendTbl = {}
    local friendTbl = Server.FriendServer:GetFriendPanelList()
    deepcopy(self._friendTbl, friendTbl)

    self._selfFriendTbl = {
        rank_player_aux_data = self._selfRankData,
        --- BEGIN MODIFICATION @ VIRTUOS
        plat_id = Server.AccountServer:GetPlatIdType(),
        --- END MODIFICATION
        player_id = Server.AccountServer:GetPlayerId(),
        nick_name = Server.RoleInfoServer.nickName or "",
        pic_url = Server.RoleInfoServer.picUrl,
        level = Server.RoleInfoServer.accountLevel,
        season_level = Server.RoleInfoServer.seasonLevel,
        mp_rank_score = Server.TournamentServer:GetRankScore(),
        mp_attended = Server.TournamentServer:GetHasAttended(),
        sol_rank_score = Server.RankingServer:GetRankScore(),
        sol_attended = Server.RankingServer:GetHasAttended(),
    }
    self._playerName = self._selfFriendTbl.nick_name
    table.insert(self._friendTbl, self._selfFriendTbl)
end

function RankingListMainPanel:RefreshFriendRankList()
    self._rankList = self._friendTbl

    local function fsortfunc(a, b)
        local id_a = a.rank_player_aux_data[self._RankDataType] and a.rank_player_aux_data[self._RankDataType] or 0
        local id_b = b.rank_player_aux_data[self._RankDataType] and b.rank_player_aux_data[self._RankDataType] or 0

        local solA = a.sol_rank_score and a.sol_rank_score or 0
        local solB = b.sol_rank_score and b.sol_rank_score or 0

        local mpA = a.mp_rank_score and a.mp_rank_score or 0
        local mpB = b.mp_rank_score and b.mp_rank_score or 0
        if id_a ~= id_b then
            return id_a > id_b
        end
        if self._isMp then
            if mpA ~= mpB then
                return mpA > mpB
            end
        else
            if solA ~= solB then
                return solA > solB
            end
        end
        if id_a ~= id_b then
            return id_a > id_b
        end
        return a.player_id < b.player_id
    end
    table.sort(self._rankList, fsortfunc)
    local RankNum = 0
    for index, info in ipairs(self._rankList) do
        if info.player_id == Server.AccountServer:GetPlayerId() then
            RankNum = index
            break
        end
    end
    self:RefreshRankMode(self._selectIndex)
    self._wtSelfRankInfomation:RefreshFriendWidget(self._selfFriendTbl, RankNum, self._RankDataType, self._isMp, nil, true)
    self._wtRankList:RefreshAllItems()
    self:PlayChangeDataAnimation()
    self:LogRankBoardExposureFlow()
end

function RankingListMainPanel:RefreshRankList(rankList, rankType, areaId)
    if self._isCN then
        if self._areaIndex == 3 then
            return
        end
    else
        if self._areaIndex == 2 then
            return
        end
    end
    if self._RankDataType == rankType and self._areaId == areaId then
        self._rankList = rankList
    end
    -- BEGIN MODIFICATION @ VIRTUOS : TRC: replace player name with ps5 online id
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        if DFMOnlineIdentityManager then
            for index, info in ipairs(self._rankList) do
                if info and info.plat_id == PlatIDType.Plat_Playstation then
                    DFMOnlineIdentityManager:AddPendingUpdateOpenId(info.player_id)
                end
            end
            DFMOnlineIdentityManager:BeginUpdatePendingOpenId()
        end
    end
    -- END MODIFICATION
    local RankNum = 0
    for index, info in ipairs(self._rankList) do
        if info.player_id == Server.AccountServer:GetPlayerId() then
            RankNum = index
            break
        end
    end

    self:RefreshRankMode(self._selectIndex)
    if self._isCN then
        self._wtSelfRankInfomation:RefreshFriendWidget(self._selfFriendTbl, RankNum, self._RankDataType, self._isMp, nil, self:CheckAreaIsSelf(), self._isNeedInstruction)
    else
        self._wtSelfRankInfomation:RefreshFriendWidget(self._selfFriendTbl, RankNum, self._RankDataType, self._isMp, nil, true, self._isNeedInstruction)
    end

    self._wtRankList:RefreshAllItems()
    self:PlayChangeDataAnimation()
    self:LogRankBoardExposureFlow()
    self:SetRankListScroll()
end

function RankingListMainPanel:SetRankListScroll()
    local length = #self._rankList
    if length > 0 and self._selectIndex > 1 then
        self._wtRankList:SetScrollOffset(Module.RankingList.Field:GetLastOffsetOfEnd() ,false ,true ,0)
    end
end

function RankingListMainPanel:LogRankBoardExposureFlow()
    if self._openByOut then
        self._openByOut = false
        self._logSource.ExposureType = 1
    end

    self._logSource.DataTypelD = self._RankDataType
    local region = 1
    self._logSource.RankBoardRegionAdcode = nil
    if self._areaIndex == 3 then
        region = 4
    elseif self._areaIndex == 2 then
        if self._CityIndex > 1 then     -- 市
            region = 3
        else
            region = 2                  -- 省
        end
        self._logSource.RankBoardRegionAdcode = self._areaId
    end

    self._logSource.RankBoardRegion = region
    LogAnalysisTool.DoRankBoardExposureFlow(self._logSource)
    self._bLastViewIsRoleInfo = false
end

function RankingListMainPanel:OnSetRankListTab()
    local RankTabs
    local rankImgs
    local config = Module.RankingList.Config
    if self._isMp then
        RankTabs = config.Loc.MpRankListTabTxt
        rankImgs = config.MpRankListImgTbl
        self._RankDataTypeTbl = config.MpRankDataType
    else
        RankTabs = config.Loc.SolRankListTabTxt
        rankImgs = config.SolRankListImgTbl
        self._RankDataTypeTbl = config.SolRankDataType
    end

    local RankListTertiaryTabs = {}
    local RankListSecondTab = config.Loc.RankingAreaTxt

    if self._isCN then
        RankListSecondTab = config.Loc.RankingAreaTxt
    else
        RankListSecondTab = config.Loc.RankingAreaGlobalTxt
    end
    -- TODO: old version remove SocialTab(index)

    for i = 1, 6 do
        local SeconTblTbl = {
            tabTxtList = RankListSecondTab,
            fCallbackIns = SafeCallBack(self._OnSubTabClick, self),
            defalutIdx = self._areaIndex,
            bPostCallbackWhenPop = true,
            bNewReddotTrie = true,
        }
        if self._isCN then
            local reddotTrieRegRankList = {}
            for index = 1, 2 do
                table.insert(reddotTrieRegRankList, {
                    uiNavId = UIName2ID.TopBar,
                    reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.RankingList, key = string.format("RankingListMessage_Type%s_AreaType%s", i, index)  }}
                })
            end
            SeconTblTbl.reddotTrieRegItemList = reddotTrieRegRankList
            table.insert(RankListTertiaryTabs, SeconTblTbl)
        else
            table.insert(RankListTertiaryTabs, SeconTblTbl)
        end
    end
    local reddotTrieRegItemList = {}
    for index = 1, 6 do
        table.insert(reddotTrieRegItemList, {
            uiNavId = UIName2ID.TopBar,
            reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.RankingList, key = "RankingListMessage_Type" .. index}}
        })
    end

    if self._index > #RankTabs then
        self._index =1
    end

    self._tabGroupData = {
        tabTxtList = RankTabs,
        imgPathList = rankImgs,
        fCallbackIns = SafeCallBack(self._OnMainTabClick, self),
        tabGroupSize = FVector2D(1136, 96),
        tabSpaceMargin = FMargin(0, 0, 16, 0),
        defalutIdx = self._index,
        tertiaryTabs = RankListTertiaryTabs,
        bPostCallbackWhenPop = true,
        bNewReddotTrie = true,
        uiNavId = UIName2ID.RankingListMainPanel,
    }

    if self._isCN then
        --self._tabGroupData.reddotTrieRegItemList = reddotTrieRegItemList
    end
end

function RankingListMainPanel:_OnGetRankListCount()
    return #self._rankList
end

function RankingListMainPanel:_OnProcessRankListWidget(index, widget)
    widget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    widget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    widget:SetButtonEnable(not IsHD() or not WidgetUtil.IsGamepad())
    widget:SetVisibility(IsHD() and WidgetUtil.IsGamepad() and ESlateVisibility.Visible or ESlateVisibility.SelfHitTestInvisible)
    if self._isCN then
        if self._areaIndex == 3 then
            widget:RefreshFriendWidget(self._rankList[index + 1], index + 1, self._RankDataType, self._isMp, self)
        else
            widget:RefreshRankWidget(self._rankList[index + 1], index + 1, self._RankDataType, self._isMp, self)
        end
    else
        if self._areaIndex == 2 then
            widget:RefreshFriendWidget(self._rankList[index + 1], index + 1, self._RankDataType, self._isMp, self)
        else
            widget:RefreshRankWidget(self._rankList[index + 1], index + 1, self._RankDataType, self._isMp, self)
        end
    end

    if self._selectIndex == index + 1 then
        if IsHD() and WidgetUtil.IsGamepad()then
            Timer.DelayCall(0.2, function()
                WidgetUtil.SetUserFocusToWidget(widget, true)
            end, self)
        else
            widget:IsSelect()
            self._selectWidget = widget
        end
    else
        widget:NoSelect()
    end
end

function RankingListMainPanel:_ChangeProvinceDrop(index)
    self._wtProvinceDropDownBox:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._ProvinceIndex = index + 1
    self._CityIndex = 1
    local ProvinceInfo = Module.RankingList:GetProvinceInfoByIndex(index + 1)
    UIUtil.InitDropDownBox(self._wtCityDropDownBox, Module.RankingList:GetCityTextTbl(ProvinceInfo.orderId), {}, 0)
    self._areaId = Module.RankingList:GetAreaIDByIndex(self._ProvinceIndex, self._CityIndex)
    Server.RankingListServer:GetRankListBy(self._RankDataType, self._areaId)
    self:_ChangeAreaImage()
end

function RankingListMainPanel:_ChangeCityDrop(index)
    self._wtCityDropDownBox:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._CityIndex = index + 1
    self._areaId = Module.RankingList:GetAreaIDByIndex(self._ProvinceIndex, self._CityIndex)
    Server.RankingListServer:GetRankListBy(self._RankDataType, self._areaId)
    self:_ChangeAreaImage()
end


function RankingListMainPanel:_RefreshSubTabAreaIndex()
    local tabls = self._tabGroupData.tertiaryTabs
    for index = 1, #tabls do
        local tbl = tabls[index]
        tbl.defalutIdx = self._areaIndex
    end
end

function RankingListMainPanel:SetExposureType()
    if self._bLastViewIsRoleInfo == false then
        if self._bClickChangeMode then
            self._logSource.ExposureType = 3
        else
            self._logSource.ExposureType = 2
        end
    else
        self._logSource.ExposureType = nil
    end
end

function RankingListMainPanel:_OnMainTabClick(index)
    self:SetExposureType()
    self._index = index
    self._RankDataType = self._RankDataTypeTbl[index]
    local score_content = Module.RankingList.Config.Loc.RankTypeListScoreTxt[self._RankDataType]
    if score_content ~= nil then
        self._wtScoreText:SetText(score_content)
    end

    if RankListTimerTbl[self._RankDataType] then
        self._wtRatingBtn:Visible()
    else
        self._wtRatingBtn:Collapsed()
    end
    self:_EnableGamepadFeature()
    self:_RefreshSubTabAreaIndex()
    self:_RefreshCheckInstruction()
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, self._tabGroupData)
end

function RankingListMainPanel:_OnSubTabClick(index)
    self._areaIndex = index
    self:SetExposureType()
    self:_EnableGamepadFeature()
    if self._lastSelectIndex > 0 then
        self._selectIndex = self._lastSelectIndex
        self._lastSelectIndex = 0
    else
        self._selectIndex = 1
    end

    if self._isCN then
        if self._areaIndex == 2 then
            self._wtAreaPanel:Visible()
            if self._RankDataType == RankDataType.TDM_VICTORY_UNIT_SCORE then  -- 巅峰赛忽略市
                -- self._wtCityDropDownBox:Collapsed()
                self._wtCityDropDownBox:Hidden()
                self._areaId = Module.RankingList:GetAreaIDByIndex(self._ProvinceIndex, 1)
            else
                self._wtCityDropDownBox:Visible()
                self._areaId = Module.RankingList:GetAreaIDByIndex(self._ProvinceIndex, self._CityIndex)
            end
            Server.RankingListServer:GetRankListBy(self._RankDataType, self._areaId)
            self:_ChangeAreaImage()
            if self._isOpenChalleng then
                self:_OpenChangeArea()
                self._isOpenChalleng = false
            end
            self._wtAreaImage:HitTestInvisible()

            Server.RankingListServer:SetRankingListCheck()
        elseif self._areaIndex == 1 then    --国服  
            self._areaId = 0
            self._wtAreaPanel:Collapsed()
            self._wtAreaImage:HitTestInvisible()
            self:_ChangeAreaImage()
            Server.RankingListServer:GetRankListBy(self._RankDataType, 0)
        elseif self._areaIndex == 3 then    --好友
            self._wtAreaPanel:Collapsed()
            self._wtAreaImage:Collapsed()
            self:RefreshFriendRankList()
            self._areaId = 0
        end
    else
        self._wtAreaImage:Collapsed()
        if self._areaIndex == 1 then
            self._areaId = 0
            self._wtAreaPanel:Collapsed()
            Server.RankingListServer:GetRankListBy(self._RankDataType, 0)
        elseif self._areaIndex == 2 then
            self._wtAreaPanel:Collapsed()
            self:RefreshFriendRankList()
            self._areaId = 0
        end
    end
    self._bClickChangeMode = false
end

function RankingListMainPanel:_ChangeAreaImage()
    local imagePath = Module.RankingList:GetAreaImageById(self._areaId)
    if self._areaId == 0 then  -- 国服背景固定
        imagePath = Module.RankingList.Config.ChAreaImg
    end
    self._wtAreaImage:AsyncSetImagePath(imagePath, true)
end

function RankingListMainPanel:_ModeChangeClick()
    self._isMp = not self._isMp
    self._bClickChangeMode = true
    self:OnSetRankListTab()
    self:ChangeModeIcon()
    Module.CommonBar:SetTopTabGroup(self._tabGroupData, 2, 1, true)
    self:PlayChangeModeAnimation()
end

function RankingListMainPanel:_OpenHistory()
    Facade.UIManager:AsyncShowUI(UIName2ID.RankingListHistory, nil, nil, self._RankDataType, self._isMp, self._playerName)
end

function RankingListMainPanel:_OpenTipsClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.RankingListIllustrate, nil, nil, "RankBoardDes")
end

function RankingListMainPanel:_OpenRatingDetails()
    Facade.UIManager:AsyncShowUI(UIName2ID.RankingListRatingDetails, nil, nil, self._RankDataType, self._isMp)
end

function RankingListMainPanel:_OpenChangeArea()
    Facade.UIManager:AsyncShowUI(UIName2ID.RankingListChangeChallenge)
end

function RankingListMainPanel:ChangeModeIcon()
    local Config = Module.RankingList.Config
    local imgPath = ''
    if self._isMp then
        imgPath = Config.MpChangeImg
    else
        imgPath = Config.SolChangeImg
    end
    self._wtChangModeImg:AsyncSetImagePath(imgPath, true)
end

function RankingListMainPanel:_SetDisplayType()
    local TypeName = "CharacterR"
    if DFHD_LUA == 1 then
        TypeName = "CharacterR_HD"
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetDisplayType", TypeName)
end

function RankingListMainPanel:OnHallCharacterSetup()
    if self.changeWeapon then
        local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(self.changeWeapon.rawPropInfo)
        Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "EquipWeapon", weaponDesc)
        self.changeWeapon  = nil
    end

    if self.equipInfo then
        for _, info in ipairs(self.equipInfo) do
            if info.position == ESlotType.MainWeaponLeft or info.position == ESlotType.MP_MainWeapon then
                local propsInfo = info.load_props[1]
                local weapDesc = WeaponAssemblyTool.PropInfo_To_Desc(propsInfo)
                --WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weapDesc, appearance.equip_skin_props)
                Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "EquipWeapon", weapDesc)


            elseif info.position == ESlotType.Bag or info.position == ESlotType.Helmet or info.position == ESlotType.ChestHanging or info.position == ESlotType.BreastPlate then
                Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "Equip", tostring(info.load_props[1].id))
            end
        end
        self.equipInfo = nil
    end
end


function RankingListMainPanel:_RefrshRoleMode(curSubStageType)
    self:_SetDisplayType()
    if not curSubStageType or curSubStageType == ESubStage.HallIndividual then
        self:RefreshModeByPlayerId()
    end
end

function RankingListMainPanel:RefreshModeByPlayerId()
    local SetPlayerModule = CreateCallBack(function(self, res)
        local appearance = res.appearance
        local fashionSuitId = 0
        for _, v in ipairs(appearance.hero.fashion_equipped) do
            if v.slot == eHeroFashionPosition.FashionSuit then
                fashionSuitId = v.id
                break
            end
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetCharacterAvatar", fashionSuitId)
        local character = Facade.HallSceneManager:GetSceneCharacterAvatarBySubstage(ESubStage.HallIndividual)
        if character then
            character.HallCharacterSetup:Add(CreateCPlusCallBack(self.OnHallCharacterSetup,self))
        end
        local CurUsedWatchItemId = 0
        for _, v in ipairs(res.appearance.hero.accessories) do
            if v.is_selected then
                if Server.HeroServer:IsWatchAccessory(v.item.prop_id) then
                    CurUsedWatchItemId = v.item.prop_id
                    break
                end
            end
        end

        local GMWatchItemId = Module.LobbyDisplay:GetDisplayWatchItemId()
        if GMWatchItemId ~= 0 then
            CurUsedWatchItemId = GMWatchItemId
        end
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallIndividual, "SetCharacterWatch", CurUsedWatchItemId)
        self.equipInfo = appearance.equip_props
        for _, info in ipairs(appearance.equip_props) do
            if info.position == ESlotType.MainWeaponLeft or info.position == ESlotType.MP_MainWeapon then
                local propsInfo = info.load_props[1]
                if propsInfo then
                    local weapDesc = WeaponAssemblyTool.PropInfo_To_Desc(propsInfo)
                    --WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weapDesc, appearance.equip_skin_props)
                    Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "EquipWeapon", weapDesc)
                else
                    Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "UnequipWeapon")
                end
            elseif info.position == ESlotType.Bag or info.position == ESlotType.Helmet or info.position == ESlotType.ChestHanging or info.position == ESlotType.BreastPlate then
                Facade.HallSceneManager:CallSceneCharacterAvatarFunctionBySubstage(ESubStage.HallIndividual, "Equip", tostring(info.load_props[1].id))
            end
        end
    end, self)

    if self._isMp then
        Server.RoleInfoServer:GetMPPlayerShowAvatarId(self._playerId, SetPlayerModule)
    else
        Server.RoleInfoServer:GetSolPlayerShowAvatarId(self._playerId, SetPlayerModule)
    end
end

function RankingListMainPanel:_selectIndexChange(index, widget)
    if self._selectIndex ~= index then
        self._selectIndex = index
        if self._selectWidget then
            self:RefreshRankMode(index)
            self._selectWidget:NoSelect()
        end
    end
    self._selectWidget = widget
end

function RankingListMainPanel:RefreshRankMode(index)
    if self._rankList[index] then
        self._playerId = self._rankList[index].player_id
        self._playerInfo = self._rankList[index]
    else
        return
    end
    self:RefreshModeByPlayerId()
    self:RefreshTitle()
end

function RankingListMainPanel:RefreshTitle()
    local callback = CreateCallBack(self.CallRefreshTitle, self)
    Server.RankingListServer:RefreshPlayerTitle(self._playerId, callback, self._playerInfo and self._playerInfo.is_privacy or false)
end

function RankingListMainPanel:CallRefreshTitle(res, isPrivacy)
    if isPrivacy then
        self._wtTitle:UpdateInfo(Module.RankingList.Config.Loc.PrivacyName, res.title, res.rank_title_adcode, res.rank_title_rank_no)
    else
        self._wtTitle:UpdateInfo(res.nick_name, res.title, res.rank_title_adcode, res.rank_title_rank_no)
    end
end

function RankingListMainPanel:PlayChangeModeAnimation()
    self:StopAnimation(self.WBP_RankingList_Main_refresh)
    self:PlayAnimation(self.WBP_RankingList_Main_refresh, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function RankingListMainPanel:PlayChangeDataAnimation()
    if IsHD() then
        self:StopAnimation(self.WBP_RankingList_Main_in_02_PC)
        self:PlayAnimation(self.WBP_RankingList_Main_in_02_PC, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:StopAnimation(self.WBP_RankingList_Main_in_02)
        self:PlayAnimation(self.WBP_RankingList_Main_in_02, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end

end

function RankingListMainPanel:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad  then
        self:_EnableGamepadFeature()
    else
        self:_DisableGamepadFeature()
    end
    self._wtRankList:RefreshVisibleItems()
end

function RankingListMainPanel:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtNavGroupRankList then
        self._wtNavGroupRankList = WidgetUtil.RegisterNavigationGroup(self._wtRankList, self, "Hittest")
        if self._wtNavGroupRankList then
            self._wtNavGroupRankList:AddNavWidgetToArray(self._wtRankList)
            self._wtNavGroupRankList:SetScrollRecipient(self._wtRankList)
            --- BEGIN MODIFICATION @ VIRTUOS
            if not IsConsole() then
                self._wtNavGroupRankList:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            end
            --- END MODIFICATION
        end
    end
    if self._wtCityDropDownBox then
        if not self._wtCityCheckButton then
            self._wtCityCheckButton = self._wtCityDropDownBox:Wnd("DFCommonCheckButton", UIWidgetBase)
        end
        self._wtCityCheckButton:Event("OnCheckButtonStateChanged",self._OnCityDropDownBoxOpenStateChanged,self)
    end
    if self._wtProvinceDropDownBox then
        if not self._wtProvinceCheckButton then
            self._wtProvinceCheckButton = self._wtProvinceDropDownBox:Wnd("DFCommonCheckButton", UIWidgetBase)
        end
        self._wtProvinceCheckButton:Event("OnCheckButtonStateChanged",self._OnProvinceDropDownBoxOpenStateChanged,self)
    end
    --- BEGIN MODIFICATION @ VIRTUOS
    if not IsConsole() then
        self:_DisableGamepadA()
    end
    --- END MODIFICATION
    if not hasdestroy(self._selectWidget) then
        WidgetUtil.SetUserFocusToWidget(self._selectWidget, true)
    else
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupRankList)
    end
    self._PCActionList = {}
    if RankListTimerTbl[self._RankDataType] then
        table.insert(self._PCActionList, {actionName = "RankingList_Detail_Gamepad",func = self._OpenRatingDetails, caller = self, bUIOnly = false, bHideIcon = false})
    end
    table.insert(self._PCActionList, {actionName = "RankingList_SwitchMode_Gamepad",func = self._ModeChangeClick, caller = self, bUIOnly = false, bHideIcon = false})
    table.insert(self._PCActionList, {actionName = "RankingList_Rule_Gamepad",func = self._OpenTipsClick, caller = self, bUIOnly = false, bHideIcon = false})
    if self._isCN and self._areaIndex == 2 then
        table.insert(self._PCActionList, {actionName = "RankingList_Challenge_Gamepad",func = self._OpenChangeArea, caller = self, bUIOnly = false, bHideIcon = false})
    end
    -- if self._isCN then
    --     table.insert(self._PCActionList, {actionName = "RankingList_History_Gamepad",func = self._OpenHistory, caller = self, bUIOnly = false, bHideIcon = false})
    -- end
    self:_RegStackUIInputSummary(self._PCActionList, false)
end

function RankingListMainPanel:_DisableGamepadFeature()
    if not IsHD() then
        return
    end
    if self._wtProvinceCheckButton then
        self._wtProvinceCheckButton:RemoveEvent("OnCheckButtonStateChanged")
    end
    if self._wtCityCheckButton then
        self._wtCityCheckButton:RemoveEvent("OnCheckButtonStateChanged")
    end
    --- BEGIN MODIFICATION @ VIRTUOS
    if not IsConsole() then
        self:_EnableGamepadA()
    end
    --- END MODIFICATION
    self:_RemoveCityDropDownNavGroup()
    self:_RemoveProvinceDropDownNavGroup()
    self._wtNavGroupRankList = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function RankingListMainPanel:_DisableGamepadA()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function RankingListMainPanel:_EnableGamepadA()
    if not IsHD() then
        return
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
        self._NavConfigHandler = nil
    end
end

function RankingListMainPanel:_RegStackUIInputSummary(summary, bReplace)
    if IsHD() then
        Module.CommonBar:SetBottomBarTempInputSummaryList(summary or {}, bReplace, bReplace)
    end
end

function RankingListMainPanel:_OnCityDropDownBoxOpenStateChanged(eCheckButtonState)
    if not IsHD() then
        return
    end
    if eCheckButtonState == ECheckButtonState.UncheckedPressed then
        self:_RegisterCityDropDownNavGroup()
        self:_InitDropDownShortcuts()
    elseif eCheckButtonState == ECheckButtonState.Unchecked then
        self:_RemoveCityDropDownNavGroup()
        self:_RemoveDropDownShortcuts()
    end
end

function RankingListMainPanel:_RegisterCityDropDownNavGroup()
    if not IsHD() then
        return
    end
    if not self._CityDropDownListNavGroup then
        if self._wtCityDropDownBox and self._wtCityDropDownBox.ScrollGridBox then
            self._CityDropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtCityDropDownBox.ScrollGridBox, self._wtCityDropDownBox, "Hittest")
        end
        if self._CityDropDownListNavGroup then
            self._CityDropDownListNavGroup:AddNavWidgetToArray(self._wtCityDropDownBox.ScrollGridBox)
            self._CityDropDownListNavGroup:SetScrollRecipient(self._wtCityDropDownBox.ScrollGridBox)
            self._CityDropDownListNavGroup:MarkIsStackControlGroup()
            if Module.RankingList:GetCityTextNoAllTbl(self._provinceId)[self._CityIndex] then
                self._wtCityDropDownBox.ScrollGridBox:ScrollToItem(self._CityIndex-1, false, false, 10, 0, false)
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._CityDropDownListNavGroup)
        end
    end
end

function RankingListMainPanel:_RemoveCityDropDownNavGroup()
    if not IsHD() then
        return
    end
    WidgetUtil.RemoveNavigationGroup(self._wtCityDropDownBox)
    self._CityDropDownListNavGroup = nil
end

function RankingListMainPanel:_OnProvinceDropDownBoxOpenStateChanged(eCheckButtonState)
    if not IsHD() then
        return
    end
    if eCheckButtonState == ECheckButtonState.UncheckedPressed then
        self:_RegisterProvinceDropDownNavGroup()
        self:_InitDropDownShortcuts()
    elseif eCheckButtonState == ECheckButtonState.Unchecked then
        self:_RemoveProvinceDropDownNavGroup()
        self:_RemoveDropDownShortcuts()
    end
end

function RankingListMainPanel:_RegisterProvinceDropDownNavGroup()
    if not IsHD() then
        return
    end
    if not self._ProvinceDropDownListNavGroup then
        if self._wtProvinceDropDownBox and self._wtProvinceDropDownBox.ScrollGridBox then
            self._ProvinceDropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtProvinceDropDownBox.ScrollGridBox, self._wtProvinceDropDownBox, "Hittest")
        end
        if self._ProvinceDropDownListNavGroup then
            self._ProvinceDropDownListNavGroup:AddNavWidgetToArray(self._wtProvinceDropDownBox.ScrollGridBox)
            self._ProvinceDropDownListNavGroup:SetScrollRecipient(self._wtProvinceDropDownBox.ScrollGridBox)
            self._ProvinceDropDownListNavGroup:MarkIsStackControlGroup()
            if Module.RankingList:GetProvinceTextTbl()[self._ProvinceIndex] then
                self._wtProvinceDropDownBox.ScrollGridBox:ScrollToItem(self._ProvinceIndex-1, false, false, 10, 0, false)
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._ProvinceDropDownListNavGroup)
        end
    end
end

function RankingListMainPanel:_RemoveProvinceDropDownNavGroup()
    if not IsHD() then
        return
    end
    WidgetUtil.RemoveNavigationGroup(self._wtProvinceDropDownBox)
    self._ProvinceDropDownListNavGroup = nil
end

function RankingListMainPanel:_InitDropDownShortcuts()
    if not self._closeDropDownHandler then
        self._closeDropDownHandler = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function RankingListMainPanel:_RemoveDropDownShortcuts()
    if self._closeDropDownHandler then
        self:RemoveInputActionBinding(self._closeDropDownHandler)
        self._closeDropDownHandler = nil
    end
end

function RankingListMainPanel:_CloseDropDown()
    self._wtCityDropDownBox:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._wtProvinceDropDownBox:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
end

function RankingListMainPanel:_OnMouseDown(mouseEvent)
    local screenPos = mouseEvent:GetScreenSpacePosition()
    local geometry = self._wtCheckInstruction:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, screenPos)
    if not isUnder then
        self._wtCheckInstruction:SetIsChecked(false)
        self:_OnTipUnShow()
    end
end

function RankingListMainPanel:OnClickSpaceEventCallAdd()
    if self._OnMouseButtonDownEventHandle then
        return
    end
    self._OnMouseButtonDownEventHandle = UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnMouseDown, self)
end

function RankingListMainPanel:OnClickSpaceEventCallRemove()
    if not self._OnMouseButtonDownEventHandle  then
        return
    end
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Remove(
            self._OnMouseButtonDownEventHandle)
    self._OnMouseButtonDownEventHandle =nil
end

function RankingListMainPanel:_OnTipShow()
    if not self._tipHandle then
        --local content = string.format(Module.RankingList.Config.Loc.RankingDeviceInstruct, 130, 150, 'PC', 'PC')
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor( {{textContent = self._tipContent, styleRowId = "C002"}}, self._wtTipsAnchor)
    end
    --self._wtTipsAnchor:SelfHitTestInvisible()
end

function RankingListMainPanel:_OnTipUnShow()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        --self._wtTipsAnchor:Collapsed()
        self._tipHandle = nil
    end
end

function RankingListMainPanel:_RefreshCheckInstruction()
    local deltaData = Server.RankingListServer:GetSelfDeltaData()
    if deltaData == nil or next(deltaData) == nil then
        return
    end
    local key = nil
    if self._isMp then
        if self._index == 1 and self._areaIndex ~= 3 then  -- Mp段位(非好友)
            key = RankDataType.TDM_SCORE
        end
        -- TODO: Mp巅峰赛
    else
        if self._index == 1 and self._areaIndex ~= 3 then  -- SOL段位(非好友)
            key = RankDataType.SOL_SCORE
        end
    end
    self._isNeedInstruction = false
    if key ~= nil then
        local delta_mobile = deltaData[key][1]
        local delta_pc = deltaData[key][2]
        -- 在变化分小的平台登录才提示
        if (IsHD() and delta_mobile > delta_pc) then
            self._isNeedInstruction = true
            self._wtCheckInstruction:SelfHitTestInvisible()
            self._tipContent = string.format(Module.RankingList.Config.Loc.RankingDeviceInstruct, delta_mobile, delta_pc,
                    Module.RankingList.Config.Loc.RankingDeviceMobile,
                    Module.RankingList.Config.Loc.RankingDevicePC)
        elseif not(IsHD()) and delta_pc > delta_mobile then
            self._isNeedInstruction = true
            self._wtCheckInstruction:SelfHitTestInvisible()
            self._tipContent = string.format(Module.RankingList.Config.Loc.RankingDeviceInstruct, delta_mobile, delta_pc,
                    Module.RankingList.Config.Loc.RankingDevicePC,
                    Module.RankingList.Config.Loc.RankingDeviceMobile)
        else
            self._wtCheckInstruction:Collapsed()
        end
    else
        self._wtCheckInstruction:Collapsed()
    end
end

return RankingListMainPanel