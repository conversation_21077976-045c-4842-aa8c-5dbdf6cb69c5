----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



---私聊的玩家Slot
---@class ChatFriendSlot : LuaUIBaseView
local ChatFriendSlot = ui("ChatFriendSlot")
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"
function ChatFriendSlot:Ctor()
    self._wtTextName = self:Wnd("TextBlock_01",UITextBlock)
    self._wtTextState = self:Wnd("TextBlock",UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtBtn = self:Wnd("Button_513", UIButton)
    self._wtBtn:Event("OnClicked",self._OnBtnClick,self)
    self._wtRedDot = self:Wnd("WBP_CommonPoint_V1",UIWidgetBase)
    self._wtCompSelected = self:Wnd("WBP_SlotCompSelected",UIWidgetBase)
    -- 排位积分图标
    self._wtCommonFlatRank = self:Wnd("WBP_Common_SmallFlatRank_C_0",UIWidgetBase)
    self._WtDFCanvasPanel_0 = self:Wnd("DFCanvasPanel_0",UIWidgetBase)
    --self._wtImageBg = self:Wnd("Image_Bg",UIImage)
    self._state = nil
    self._playerId = nil
end

--------------------------------------------------------------------------
--- 界面状态初始化、重置、销毁相关
--------------------------------------------------------------------------
function ChatFriendSlot:OnInitExtraData()
end

function ChatFriendSlot:OnOpen()
    self:AddLuaEvent(Module.Chat.Config.evtLastChatFriendSelectedNo, self._OnLastChatFriendSelectedNo, self)
    self:AddLuaEvent(Module.Chat.Config.evtChatFriendModelChange, self._OnChatFriendChange, self)
end

function ChatFriendSlot:OnClose()
    self:RemoveAllLuaEvent()
end

--------------------------------------------------------------------------
--- 事件监听、控件事件相关
--------------------------------------------------------------------------

--聊天对象改变，设置上一个聊天对象的选中状态
function ChatFriendSlot:_OnLastChatFriendSelectedNo(playerId)
    if playerId == self._playerId then
        self:SetSelected(false)
    end
end

function ChatFriendSlot:_OnChatFriendChange(info)
    if info and info.player_id == self._playerId then
        self:SetSelected(true)
    end
end

function ChatFriendSlot:_OnBtnClick()
    local lastChatplayerInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
    if lastChatplayerInfo and lastChatplayerInfo.player_id == self._playerId then
        return
    elseif lastChatplayerInfo then
        Module.Chat.Config.evtLastChatFriendSelectedNo:Invoke(lastChatplayerInfo.player_id)
    end

    local playerInfo = Module.Chat.Field.PrivateViewModel:GetSinglePlayerInfo(self._playerId)
    Server.FrontEndChatServer:SetChatPlayerInfo(playerInfo)
    self:SetSelected(true)
    self._wtRedDot:Collapsed()
end

--------------------------------------------------------------------------
--- 界面数据、具体的业务逻辑相关
--------------------------------------------------------------------------
function ChatFriendSlot:SetInfo(info,bFriend)
    -- TODO 有没有可能info是一个不正确的值呢 nil或不符合我们需要的数据结构
    self._playerId = info.player_id
    self._bFriend = bFriend
    -- 名字展示相关
    local width,nick_clamp = StringUtil.GetShowWidth(info.nick_name, 10)
    local isFriend = Server.FriendServer:CheckIsFriend(self._playerId)
    local remarkName = Server.FriendServer:GetFriendRemarkById(self._playerId)
    if isFriend and remarkName ~= "" then
        remarkName = ChatLogic.FullRemarkNameText(nick_clamp, remarkName)
        width,nick_clamp = StringUtil.GetShowWidth(remarkName, 10)
    end

    if width > 14 then
        self._wtTextName:SetText(nick_clamp .. "...")
    elseif isFriend and remarkName ~= "" then
        self._wtTextName:SetText(remarkName)
    else
        self._wtTextName:SetText(info.nick_name)
    end

    -- 等级展示(目前sol和mp各有对应模式的等级)
    local playerInfo = info
    self._wtPlayerIcon:InitPortrait(playerInfo, HeadIconType.HeadNore)
    self._state = Module.Social:GetOnlinePlayerStateFromStateCode(info.state)
    local curChatPlayer = Server.FrontEndChatServer:GetChatPlayerInfo()
    if curChatPlayer and curChatPlayer.player_id == info.player_id then
        self:SetSelected(true)
    else
        self:SetSelected(false)
    end

    self:RefreshRedDot()

    local curChatplayerInfo = Server.FrontEndChatServer:GetChatPlayerInfo()
    if curChatplayerInfo and self._playerId == curChatplayerInfo.player_id then
        Module.Chat.Config.evtFriendStateUpdate:Invoke(playerInfo)
    end

    -- 设置排位或积分内容
    self:SetCommonFlatRankContent(info)
end

--设置选中状态
---@param idx int 0-在线，1-游戏中，2-离线
function ChatFriendSlot:SetSelected(bSelected)
    bSelected = setdefault(bSelected, false)
    local idx = 0

    local playerInfo = Module.Chat.Field.PrivateViewModel:GetSinglePlayerInfo(self._playerId)
    if self._state == GlobalPlayerStateEnums.EPlayerState_InMatch or (
        self._state == GlobalPlayerStateEnums.EPlayerState_InTeam and playerInfo.member_num > 1) then
        idx = 1
        if self._state == GlobalPlayerStateEnums.EPlayerState_InMatch then
            self._wtTextState:SetText(Module.Chat.Config.Loc.InMatch)
        else
            idx = 0
            self._wtTextState:SetText(Module.Chat.Config.Loc.InTeam)
        end
    elseif self._state == GlobalPlayerStateEnums.EPlayerState_Offline then
        idx = 2
        --self._wtImageBg:SetBrushFromSoftAtlasInterface(self.Chat_Bg_Select,false)
        self._wtTextState:SetText(Module.Chat.Config.Loc.OffLine)
    else
        idx = 0
        --self._wtImageBg:SetBrushFromSoftAtlasInterface(self.Chat_Bg_Select,false)
        self._wtTextState:SetText(Module.Chat.Config.Loc.OnLine)
    end
    self._wtTextState:SetColorAndOpacity(Module.Social:GetPlayerStateColor(self._state))

    self:SetCppValue("Set_Type",idx)
    self:BP_SetType()
    if bSelected then
        self._wtCompSelected:Visible()
        --self._wtImageBg:SetBrushFromSoftAtlasInterface(self.Chat_Bg_Default,false)
    else
        self._wtCompSelected:Collapsed()
        --self._wtImageBg:SetBrushFromSoftAtlasInterface(self.Chat_Bg_Select,false)
        --self._wtImageBg:Collapsed()
    end
    --self:BP_SetSelected(bSelected)
end

--更新一下红点
function ChatFriendSlot:RefreshRedDot()
    local chatInfo = Server.FrontEndChatServer:GetPrivateChatSummary(self._playerId,self._bFriend)
    local unReadNum = chatInfo and chatInfo.unread_msg_num or 0
    if unReadNum > 0 then
        self._wtRedDot:HitTestInvisible()
    else
        self._wtRedDot:Collapsed()
    end
end

--------------------------------------- 排位积分相关 ------------------------------------
function ChatFriendSlot:SetCommonFlatRankContent(playerInfo)
    self._WtDFCanvasPanel_0:Visible()
    ChatLogic.ChatSetCommonFlatRankContent(playerInfo, self._wtCommonFlatRank)
end
--end
-----------------------------------------------------------------------

return ChatFriendSlot
