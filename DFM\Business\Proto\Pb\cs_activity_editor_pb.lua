--cs_activity.protoencode&decode functions.
function pb.pb_ActivityAwardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityAward) or {} 
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __received = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    local __mode_config = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __mode_config ~= 0 then tb.mode_config = __mode_config end
    local __image = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    local __prop_mode = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __prop_mode ~= 0 then tb.prop_mode = __prop_mode end
    local __type = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __priority = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __priority ~= 0 then tb.priority = __priority end
    return tb
end

function pb.pb_ActivityAwardEncode(tb, encoder)
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(1))    end
    if(tb.received) then    encoder:addbool(2, tb.received)    end
    if(tb.mode_config) then    encoder:addi32(3, tb.mode_config)    end
    if(tb.image) then    encoder:addstr(4, tb.image)    end
    if(tb.prop_mode) then    encoder:addi32(5, tb.prop_mode)    end
    if(tb.type) then    encoder:addu32(6, tb.type)    end
    if(tb.priority) then    encoder:addi64(7, tb.priority)    end
end

function pb.pb_ActivityMilestoneAwardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityMilestoneAward) or {} 
    local __currency_num = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __currency_num ~= 0 then tb.currency_num = __currency_num end
    tb.prop = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.prop[k] = pb.pb_ActivityAwardDecode(v)
    end
    local __reward_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __reward_id ~= 0 then tb.reward_id = __reward_id end
    local __image = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    local __need_choose = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __need_choose ~= false then tb.need_choose = __need_choose end
    local __choose_prop_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __choose_prop_id ~= 0 then tb.choose_prop_id = __choose_prop_id end
    return tb
end

function pb.pb_ActivityMilestoneAwardEncode(tb, encoder)
    if(tb.currency_num) then    encoder:addu64(1, tb.currency_num)    end
    if(tb.prop) then
        for i=1,#(tb.prop) do
            pb.pb_ActivityAwardEncode(tb.prop[i], encoder:addsubmsg(2))
        end
    end
    if(tb.reward_id) then    encoder:addu64(3, tb.reward_id)    end
    if(tb.image) then    encoder:addstr(4, tb.image)    end
    if(tb.need_choose) then    encoder:addbool(5, tb.need_choose)    end
    if(tb.choose_prop_id) then    encoder:addu64(6, tb.choose_prop_id)    end
end

function pb.pb_ActivityExchangePropDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityExchangeProp) or {} 
    local __exchange_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __currency_num = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __currency_num ~= 0 then tb.currency_num = __currency_num end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(3))
    local __exchanged_count = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __exchanged_count ~= 0 then tb.exchanged_count = __exchanged_count end
    local __exchange_count_max = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __exchange_count_max ~= 0 then tb.exchange_count_max = __exchange_count_max end
    local __image = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    local __unlock_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    local __prop_mode = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __prop_mode ~= 0 then tb.prop_mode = __prop_mode end
    tb.pay_props = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.pay_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __unlock_currency_id = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __unlock_currency_id ~= 0 then tb.unlock_currency_id = __unlock_currency_id end
    local __unlock_currency_num = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __unlock_currency_num ~= 0 then tb.unlock_currency_num = __unlock_currency_num end
    local __unlocked = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __unlocked ~= false then tb.unlocked = __unlocked end
    local __unlock_mode = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __unlock_mode ~= 0 then tb.unlock_mode = __unlock_mode end
    local __currency_id = decoder:getu64(14)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __refresh_internal = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __refresh_internal ~= 0 then tb.refresh_internal = __refresh_internal end
    local __next_refresh_time = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __next_refresh_time ~= 0 then tb.next_refresh_time = __next_refresh_time end
    local __refresh_desc = decoder:getstr(17)
    if not PB_USE_DEFAULT_TABLE or __refresh_desc ~= "" then tb.refresh_desc = __refresh_desc end
    tb.plans = {}
    for k,v in pairs(decoder:getsubmsgary(18)) do
        tb.plans[k] = pb.pb_ActivityExchangePlanDecode(v)
    end
    local __sol_default_plan = decoder:getu64(19)
    if not PB_USE_DEFAULT_TABLE or __sol_default_plan ~= 0 then tb.sol_default_plan = __sol_default_plan end
    local __mp_default_plan = decoder:getu64(20)
    if not PB_USE_DEFAULT_TABLE or __mp_default_plan ~= 0 then tb.mp_default_plan = __mp_default_plan end
    local __selected_plan = decoder:getu64(21)
    if not PB_USE_DEFAULT_TABLE or __selected_plan ~= 0 then tb.selected_plan = __selected_plan end
    local __is_focus = decoder:getbool(22)
    if not PB_USE_DEFAULT_TABLE or __is_focus ~= false then tb.is_focus = __is_focus end
    local __back_image = decoder:getstr(23)
    if not PB_USE_DEFAULT_TABLE or __back_image ~= "" then tb.back_image = __back_image end
    return tb
end

function pb.pb_ActivityExchangePropEncode(tb, encoder)
    if(tb.exchange_id) then    encoder:addu64(1, tb.exchange_id)    end
    if(tb.currency_num) then    encoder:addu64(2, tb.currency_num)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(3))    end
    if(tb.exchanged_count) then    encoder:addi32(4, tb.exchanged_count)    end
    if(tb.exchange_count_max) then    encoder:addi32(5, tb.exchange_count_max)    end
    if(tb.image) then    encoder:addstr(6, tb.image)    end
    if(tb.unlock_time) then    encoder:addi64(7, tb.unlock_time)    end
    if(tb.prop_mode) then    encoder:addi32(8, tb.prop_mode)    end
    if(tb.pay_props) then
        for i=1,#(tb.pay_props) do
            pb.pb_PropInfoEncode(tb.pay_props[i], encoder:addsubmsg(9))
        end
    end
    if(tb.unlock_currency_id) then    encoder:addi64(10, tb.unlock_currency_id)    end
    if(tb.unlock_currency_num) then    encoder:addi64(11, tb.unlock_currency_num)    end
    if(tb.unlocked) then    encoder:addbool(12, tb.unlocked)    end
    if(tb.unlock_mode) then    encoder:addu32(13, tb.unlock_mode)    end
    if(tb.currency_id) then    encoder:addu64(14, tb.currency_id)    end
    if(tb.refresh_internal) then    encoder:addi64(15, tb.refresh_internal)    end
    if(tb.next_refresh_time) then    encoder:addi64(16, tb.next_refresh_time)    end
    if(tb.refresh_desc) then    encoder:addstr(17, tb.refresh_desc)    end
    if(tb.plans) then
        for i=1,#(tb.plans) do
            pb.pb_ActivityExchangePlanEncode(tb.plans[i], encoder:addsubmsg(18))
        end
    end
    if(tb.sol_default_plan) then    encoder:addu64(19, tb.sol_default_plan)    end
    if(tb.mp_default_plan) then    encoder:addu64(20, tb.mp_default_plan)    end
    if(tb.selected_plan) then    encoder:addu64(21, tb.selected_plan)    end
    if(tb.is_focus) then    encoder:addbool(22, tb.is_focus)    end
    if(tb.back_image) then    encoder:addstr(23, tb.back_image)    end
end

function pb.pb_ActivityExchangePlanDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityExchangePlan) or {} 
    local __plan_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __plan_id ~= 0 then tb.plan_id = __plan_id end
    tb.prop = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.prop[k] = pb.pb_PropInfoDecode(v)
    end
    local __plan_name = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __plan_name ~= "" then tb.plan_name = __plan_name end
    return tb
end

function pb.pb_ActivityExchangePlanEncode(tb, encoder)
    if(tb.plan_id) then    encoder:addu64(1, tb.plan_id)    end
    if(tb.prop) then
        for i=1,#(tb.prop) do
            pb.pb_PropInfoEncode(tb.prop[i], encoder:addsubmsg(2))
        end
    end
    if(tb.plan_name) then    encoder:addstr(3, tb.plan_name)    end
end

function pb.pb_ActivityExchangePageDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityExchangePage) or {} 
    local __page_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __page_id ~= 0 then tb.page_id = __page_id end
    local __format = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __format ~= 0 then tb.format = __format end
    local __name = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    tb.exchange_ids = decoder:getu64ary(4)
    local __unlock_time = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    local __back_image = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __back_image ~= "" then tb.back_image = __back_image end
    local __icon = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __icon ~= "" then tb.icon = __icon end
    return tb
end

function pb.pb_ActivityExchangePageEncode(tb, encoder)
    if(tb.page_id) then    encoder:addu64(1, tb.page_id)    end
    if(tb.format) then    encoder:addu32(2, tb.format)    end
    if(tb.name) then    encoder:addstr(3, tb.name)    end
    if(tb.exchange_ids) then    encoder:addu64(4, tb.exchange_ids)    end
    if(tb.unlock_time) then    encoder:addi64(5, tb.unlock_time)    end
    if(tb.back_image) then    encoder:addstr(6, tb.back_image)    end
    if(tb.icon) then    encoder:addstr(7, tb.icon)    end
end

function pb.pb_AttendStateDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AttendState) or {} 
    local __day = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __day ~= 0 then tb.day = __day end
    local __state = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(3))
    tb.daily_award = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.daily_award[k] = pb.pb_ActivityAwardDecode(v)
    end
    return tb
end

function pb.pb_AttendStateEncode(tb, encoder)
    if(tb.day) then    encoder:addu32(1, tb.day)    end
    if(tb.state) then    encoder:addu32(2, tb.state)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(3))    end
    if(tb.daily_award) then
        for i=1,#(tb.daily_award) do
            pb.pb_ActivityAwardEncode(tb.daily_award[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_AttendInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AttendInfo) or {} 
    local __attend_times = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __attend_times ~= 0 then tb.attend_times = __attend_times end
    local __first_attend_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __first_attend_time ~= 0 then tb.first_attend_time = __first_attend_time end
    local __last_attend_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __last_attend_time ~= 0 then tb.last_attend_time = __last_attend_time end
    local __next_attend_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __next_attend_time ~= 0 then tb.next_attend_time = __next_attend_time end
    tb.states = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.states[k] = pb.pb_AttendStateDecode(v)
    end
    tb.daily_award = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.daily_award[k] = pb.pb_ActivityAwardDecode(v)
    end
    return tb
end

function pb.pb_AttendInfoEncode(tb, encoder)
    if(tb.attend_times) then    encoder:addu32(1, tb.attend_times)    end
    if(tb.first_attend_time) then    encoder:addi64(2, tb.first_attend_time)    end
    if(tb.last_attend_time) then    encoder:addi64(3, tb.last_attend_time)    end
    if(tb.next_attend_time) then    encoder:addi64(4, tb.next_attend_time)    end
    if(tb.states) then
        for i=1,#(tb.states) do
            pb.pb_AttendStateEncode(tb.states[i], encoder:addsubmsg(5))
        end
    end
    if(tb.daily_award) then
        for i=1,#(tb.daily_award) do
            pb.pb_ActivityAwardEncode(tb.daily_award[i], encoder:addsubmsg(10))
        end
    end
end

function pb.pb_ActivityObjectiveDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityObjective) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __completed = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __completed ~= false then tb.completed = __completed end
    local __progress = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __progress ~= 0 then tb.progress = __progress end
    local __progress_max = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __progress_max ~= 0 then tb.progress_max = __progress_max end
    tb.conditions = pb.pb_ActivityObjectiveConditionsDecode(decoder:getsubmsg(5))
    tb.window = decoder:getu32ary(6)
    local __goal_type = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __goal_type ~= 0 then tb.goal_type = __goal_type end
    tb.pay_props = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.pay_props[k] = pb.pb_PropInfoDecode(v)
    end
    tb.get_props = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.get_props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_ActivityObjectiveEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.completed) then    encoder:addbool(2, tb.completed)    end
    if(tb.progress) then    encoder:addi64(3, tb.progress)    end
    if(tb.progress_max) then    encoder:addi64(4, tb.progress_max)    end
    if(tb.conditions) then    pb.pb_ActivityObjectiveConditionsEncode(tb.conditions, encoder:addsubmsg(5))    end
    if(tb.window) then    encoder:addu32(6, tb.window)    end
    if(tb.goal_type) then    encoder:addi32(7, tb.goal_type)    end
    if(tb.pay_props) then
        for i=1,#(tb.pay_props) do
            pb.pb_PropInfoEncode(tb.pay_props[i], encoder:addsubmsg(8))
        end
    end
    if(tb.get_props) then
        for i=1,#(tb.get_props) do
            pb.pb_PropInfoEncode(tb.get_props[i], encoder:addsubmsg(9))
        end
    end
end

function pb.pb_ActivityObjectiveConditionsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityObjectiveConditions) or {} 
    local __check_basic_condition1 = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __check_basic_condition1 ~= false then tb.check_basic_condition1 = __check_basic_condition1 end
    local __check_basic_condition2 = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __check_basic_condition2 ~= false then tb.check_basic_condition2 = __check_basic_condition2 end
    local __check_basic_condition3 = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __check_basic_condition3 ~= false then tb.check_basic_condition3 = __check_basic_condition3 end
    local __check_basic_condition4 = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __check_basic_condition4 ~= false then tb.check_basic_condition4 = __check_basic_condition4 end
    local __check_basic_condition5 = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __check_basic_condition5 ~= false then tb.check_basic_condition5 = __check_basic_condition5 end
    local __check_basic_condition6 = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __check_basic_condition6 ~= false then tb.check_basic_condition6 = __check_basic_condition6 end
    tb.ext_conditions = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.ext_conditions[k] = pb.pb_ActivityObjectiveConditions_ExtConditionsEntryDecode(v)
    end
    return tb
end

function pb.pb_ActivityObjectiveConditionsEncode(tb, encoder)
    if(tb.check_basic_condition1) then    encoder:addbool(1, tb.check_basic_condition1)    end
    if(tb.check_basic_condition2) then    encoder:addbool(2, tb.check_basic_condition2)    end
    if(tb.check_basic_condition3) then    encoder:addbool(3, tb.check_basic_condition3)    end
    if(tb.check_basic_condition4) then    encoder:addbool(4, tb.check_basic_condition4)    end
    if(tb.check_basic_condition5) then    encoder:addbool(5, tb.check_basic_condition5)    end
    if(tb.check_basic_condition6) then    encoder:addbool(6, tb.check_basic_condition6)    end
    if(tb.ext_conditions) then
        for i=1,#(tb.ext_conditions) do
            pb.pb_ActivityObjectiveConditions_ExtConditionsEntryEncode(tb.ext_conditions[i], encoder:addsubmsg(10))
        end
    end
end

function pb.pb_ActivityTaskInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityTaskInfo) or {} 
    local __task_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __task_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __task_type ~= 0 then tb.task_type = __task_type end
    local __state = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __accept_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __accept_time ~= 0 then tb.accept_time = __accept_time end
    local __complete_time = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __complete_time ~= 0 then tb.complete_time = __complete_time end
    tb.var = decoder:geti64ary(6)
    tb.objectives = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.objectives[k] = pb.pb_ActivityObjectiveDecode(v)
    end
    tb.child = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.child[k] = pb.pb_ActivityTaskInfoDecode(v)
    end
    tb.award = pb.pb_ActivityAwardDecode(decoder:getsubmsg(9))
    local __start_time = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __end_time = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    local __task_line = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __task_line ~= 0 then tb.task_line = __task_line end
    local __sequence_order = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __sequence_order ~= 0 then tb.sequence_order = __sequence_order end
    local __start_limit_time = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __start_limit_time ~= 0 then tb.start_limit_time = __start_limit_time end
    local __end_limit_time = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __end_limit_time ~= 0 then tb.end_limit_time = __end_limit_time end
    local __is_last_sequence = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __is_last_sequence ~= false then tb.is_last_sequence = __is_last_sequence end
    local __invalid = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __invalid ~= false then tb.invalid = __invalid end
    tb.awards = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.awards[k] = pb.pb_ActivityAwardDecode(v)
    end
    local __jump_id = decoder:getu64(22)
    if not PB_USE_DEFAULT_TABLE or __jump_id ~= 0 then tb.jump_id = __jump_id end
    local __mode_tag = decoder:getu64(23)
    if not PB_USE_DEFAULT_TABLE or __mode_tag ~= 0 then tb.mode_tag = __mode_tag end
    local __info1 = decoder:getstr(24)
    if not PB_USE_DEFAULT_TABLE or __info1 ~= "" then tb.info1 = __info1 end
    local __info2 = decoder:getstr(25)
    if not PB_USE_DEFAULT_TABLE or __info2 ~= "" then tb.info2 = __info2 end
    local __name = decoder:getstr(30)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __desc = decoder:getstr(31)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __weight = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __weight ~= 0 then tb.weight = __weight end
    local __task_slot = decoder:getu32(40)
    if not PB_USE_DEFAULT_TABLE or __task_slot ~= 0 then tb.task_slot = __task_slot end
    tb.required_props = {}
    for k,v in pairs(decoder:getsubmsgary(50)) do
        tb.required_props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_ActivityTaskInfoEncode(tb, encoder)
    if(tb.task_id) then    encoder:addu64(1, tb.task_id)    end
    if(tb.task_type) then    encoder:addi32(2, tb.task_type)    end
    if(tb.state) then    encoder:addi32(3, tb.state)    end
    if(tb.accept_time) then    encoder:addi64(4, tb.accept_time)    end
    if(tb.complete_time) then    encoder:addi64(5, tb.complete_time)    end
    if(tb.var) then    encoder:addi64(6, tb.var)    end
    if(tb.objectives) then
        for i=1,#(tb.objectives) do
            pb.pb_ActivityObjectiveEncode(tb.objectives[i], encoder:addsubmsg(7))
        end
    end
    if(tb.child) then
        for i=1,#(tb.child) do
            pb.pb_ActivityTaskInfoEncode(tb.child[i], encoder:addsubmsg(8))
        end
    end
    if(tb.award) then    pb.pb_ActivityAwardEncode(tb.award, encoder:addsubmsg(9))    end
    if(tb.start_time) then    encoder:addi64(10, tb.start_time)    end
    if(tb.end_time) then    encoder:addi64(11, tb.end_time)    end
    if(tb.task_line) then    encoder:addu64(12, tb.task_line)    end
    if(tb.sequence_order) then    encoder:addi32(13, tb.sequence_order)    end
    if(tb.start_limit_time) then    encoder:addi64(14, tb.start_limit_time)    end
    if(tb.end_limit_time) then    encoder:addi64(15, tb.end_limit_time)    end
    if(tb.is_last_sequence) then    encoder:addbool(16, tb.is_last_sequence)    end
    if(tb.invalid) then    encoder:addbool(20, tb.invalid)    end
    if(tb.awards) then
        for i=1,#(tb.awards) do
            pb.pb_ActivityAwardEncode(tb.awards[i], encoder:addsubmsg(21))
        end
    end
    if(tb.jump_id) then    encoder:addu64(22, tb.jump_id)    end
    if(tb.mode_tag) then    encoder:addu64(23, tb.mode_tag)    end
    if(tb.info1) then    encoder:addstr(24, tb.info1)    end
    if(tb.info2) then    encoder:addstr(25, tb.info2)    end
    if(tb.name) then    encoder:addstr(30, tb.name)    end
    if(tb.desc) then    encoder:addstr(31, tb.desc)    end
    if(tb.weight) then    encoder:addu32(32, tb.weight)    end
    if(tb.task_slot) then    encoder:addu32(40, tb.task_slot)    end
    if(tb.required_props) then
        for i=1,#(tb.required_props) do
            pb.pb_PropInfoEncode(tb.required_props[i], encoder:addsubmsg(50))
        end
    end
end

function pb.pb_ActivityInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityInfo) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    local __actv_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __actv_type ~= 0 then tb.actv_type = __actv_type end
    local __start_date = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __start_date ~= 0 then tb.start_date = __start_date end
    local __end_date = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __end_date ~= 0 then tb.end_date = __end_date end
    local __is_tracking = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_tracking ~= false then tb.is_tracking = __is_tracking end
    tb.attend_info = pb.pb_AttendInfoDecode(decoder:getsubmsg(6))
    tb.task_info = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.task_info[k] = pb.pb_ActivityTaskInfoDecode(v)
    end
    tb.expired_task_info = {}
    for k,v in pairs(decoder:getsubmsgary(99)) do
        tb.expired_task_info[k] = pb.pb_ActivityTaskInfoDecode(v)
    end
    local __content = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __content ~= "" then tb.content = __content end
    local __currency_id = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __currency_num = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __currency_num ~= 0 then tb.currency_num = __currency_num end
    local __tab_belonging = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __tab_belonging ~= 0 then tb.tab_belonging = __tab_belonging end
    local __manual_tracking = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __manual_tracking ~= false then tb.manual_tracking = __manual_tracking end
    local __order_weight = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __order_weight ~= 0 then tb.order_weight = __order_weight end
    tb.milestone_award = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.milestone_award[k] = pb.pb_ActivityMilestoneAwardDecode(v)
    end
    tb.exchange_item = {}
    for k,v in pairs(decoder:getsubmsgary(16)) do
        tb.exchange_item[k] = pb.pb_ActivityExchangePropDecode(v)
    end
    tb.prop_exchange_items = {}
    for k,v in pairs(decoder:getsubmsgary(17)) do
        tb.prop_exchange_items[k] = pb.pb_ActivityExchangePropDecode(v)
    end
    tb.final_award = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.final_award[k] = pb.pb_ActivityAwardDecode(v)
    end
    local __final_award_received = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __final_award_received ~= false then tb.final_award_received = __final_award_received end
    tb.recruit_ms_tasks = {}
    for k,v in pairs(decoder:getsubmsgary(30)) do
        tb.recruit_ms_tasks[k] = pb.pb_ActivityTaskInfoDecode(v)
    end
    local __next_refresh_time = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __next_refresh_time ~= 0 then tb.next_refresh_time = __next_refresh_time end
    local __available_refresh_times = decoder:geti32(44)
    if not PB_USE_DEFAULT_TABLE or __available_refresh_times ~= 0 then tb.available_refresh_times = __available_refresh_times end
    local __mode_leaning = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __mode_leaning ~= 0 then tb.mode_leaning = __mode_leaning end
    local __mode_tag = decoder:geti32(33)
    if not PB_USE_DEFAULT_TABLE or __mode_tag ~= 0 then tb.mode_tag = __mode_tag end
    local __card_display = decoder:getu32(34)
    if not PB_USE_DEFAULT_TABLE or __card_display ~= 0 then tb.card_display = __card_display end
    local __info1 = decoder:getstr(35)
    if not PB_USE_DEFAULT_TABLE or __info1 ~= "" then tb.info1 = __info1 end
    local __info2 = decoder:getstr(36)
    if not PB_USE_DEFAULT_TABLE or __info2 ~= "" then tb.info2 = __info2 end
    local __name = decoder:getstr(37)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __desc = decoder:getstr(38)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __details = decoder:getstr(39)
    if not PB_USE_DEFAULT_TABLE or __details ~= "" then tb.details = __details end
    local __reward_showed1 = decoder:getu64(40)
    if not PB_USE_DEFAULT_TABLE or __reward_showed1 ~= 0 then tb.reward_showed1 = __reward_showed1 end
    local __reward_showed2 = decoder:getu64(41)
    if not PB_USE_DEFAULT_TABLE or __reward_showed2 ~= 0 then tb.reward_showed2 = __reward_showed2 end
    local __reward_showed3 = decoder:getu64(42)
    if not PB_USE_DEFAULT_TABLE or __reward_showed3 ~= 0 then tb.reward_showed3 = __reward_showed3 end
    local __manual_track = decoder:getbool(43)
    if not PB_USE_DEFAULT_TABLE or __manual_track ~= false then tb.manual_track = __manual_track end
    local __details_show_way = decoder:getu32(46)
    if not PB_USE_DEFAULT_TABLE or __details_show_way ~= 0 then tb.details_show_way = __details_show_way end
    local __info3 = decoder:getstr(47)
    if not PB_USE_DEFAULT_TABLE or __info3 ~= "" then tb.info3 = __info3 end
    local __time_show_way = decoder:getu32(48)
    if not PB_USE_DEFAULT_TABLE or __time_show_way ~= 0 then tb.time_show_way = __time_show_way end
    local __task_show_way = decoder:getu32(49)
    if not PB_USE_DEFAULT_TABLE or __task_show_way ~= 0 then tb.task_show_way = __task_show_way end
    tb.classify_config = {}
    for k,v in pairs(decoder:getsubmsgary(50)) do
        tb.classify_config[k] = pb.pb_NewbieGoalTypesClassifyConfigDecode(v)
    end
    tb.answers = {}
    for k,v in pairs(decoder:getsubmsgary(51)) do
        tb.answers[k] = pb.pb_ActivityQuestionAnswerDecode(v)
    end
    tb.exchange_pages = {}
    for k,v in pairs(decoder:getsubmsgary(52)) do
        tb.exchange_pages[k] = pb.pb_ActivityExchangePageDecode(v)
    end
    local __invalid = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __invalid ~= false then tb.invalid = __invalid end
    local __finished = decoder:getbool(101)
    if not PB_USE_DEFAULT_TABLE or __finished ~= false then tb.finished = __finished end
    local __listing_tips = decoder:getbool(102)
    if not PB_USE_DEFAULT_TABLE or __listing_tips ~= false then tb.listing_tips = __listing_tips end
    local __label_desc = decoder:getstr(103)
    if not PB_USE_DEFAULT_TABLE or __label_desc ~= "" then tb.label_desc = __label_desc end
    local __end_notice_time = decoder:geti64(104)
    if not PB_USE_DEFAULT_TABLE or __end_notice_time ~= 0 then tb.end_notice_time = __end_notice_time end
    local __is_important = decoder:getbool(105)
    if not PB_USE_DEFAULT_TABLE or __is_important ~= false then tb.is_important = __is_important end
    local __important_pic = decoder:getstr(106)
    if not PB_USE_DEFAULT_TABLE or __important_pic ~= "" then tb.important_pic = __important_pic end
    local __important_video = decoder:getstr(107)
    if not PB_USE_DEFAULT_TABLE or __important_video ~= "" then tb.important_video = __important_video end
    local __second_important_pic = decoder:getstr(108)
    if not PB_USE_DEFAULT_TABLE or __second_important_pic ~= "" then tb.second_important_pic = __second_important_pic end
    local __module_type = decoder:geti32(109)
    if not PB_USE_DEFAULT_TABLE or __module_type ~= 0 then tb.module_type = __module_type end
    local __important_desc = decoder:getstr(110)
    if not PB_USE_DEFAULT_TABLE or __important_desc ~= "" then tb.important_desc = __important_desc end
    local __cooperation_label = decoder:getstr(111)
    if not PB_USE_DEFAULT_TABLE or __cooperation_label ~= "" then tb.cooperation_label = __cooperation_label end
    tb.kol_weapons = {}
    for k,v in pairs(decoder:getsubmsgary(200)) do
        tb.kol_weapons[k] = pb.pb_KOLWeaponTasteConfigDecode(v)
    end
    tb.jump_to_config = {}
    for k,v in pairs(decoder:getsubmsgary(210)) do
        tb.jump_to_config[k] = pb.pb_ActivityJumpConfigDecode(v)
    end
    tb.password_boxes = {}
    for k,v in pairs(decoder:getsubmsgary(220)) do
        tb.password_boxes[k] = pb.pb_PasswordBoxDecode(v)
    end
    tb.chip_maps = {}
    for k,v in pairs(decoder:getsubmsgary(221)) do
        tb.chip_maps[k] = pb.pb_MapInfoDecode(v)
    end
    tb.moss_construct_info = pb.pb_MossConstructInfoDecode(decoder:getsubmsg(222))
    tb.newbie_challenge_info = pb.pb_NewbieChallengeInfoDecode(decoder:getsubmsg(223))
    tb.lottery_info = pb.pb_LotteryInfoDecode(decoder:getsubmsg(224))
    tb.star_fire_info = pb.pb_StarFireInfoDecode(decoder:getsubmsg(225))
    tb.archive_info = pb.pb_ArchiveInfoDecode(decoder:getsubmsg(226))
    tb.relink_info = pb.pb_RelinkInfoDecode(decoder:getsubmsg(227))
    tb.return_info = pb.pb_ReturnInfoDecode(decoder:getsubmsg(228))
    tb.act_theme_eavesdropping = pb.pb_ActThemeEavesdroppingInfoDecode(decoder:getsubmsg(229))
    tb.survey_boss_info = pb.pb_SurveyBossInfoDecode(decoder:getsubmsg(230))
    tb.act_theme_star = pb.pb_ActThemeStarInfoDecode(decoder:getsubmsg(231))
    tb.make_drink_info = pb.pb_MakeDrinkInfoDecode(decoder:getsubmsg(232))
    tb.act_theme_she_one_card_info = pb.pb_ActThemeSheOneCardInfoDecode(decoder:getsubmsg(233))
    tb.weapon_delivery_info = pb.pb_WeaponDeliveryInfoDecode(decoder:getsubmsg(234))
    tb.sbc_info = pb.pb_SBCInfoDecode(decoder:getsubmsg(235))
    tb.mp_commander_info = pb.pb_MPCommanderInfoDecode(decoder:getsubmsg(236))
    tb.simple_milestone_info = pb.pb_SimpleMilestoneInfoDecode(decoder:getsubmsg(237))
    tb.pandora_info = pb.pb_PandoraInfoDecode(decoder:getsubmsg(238))
    tb.arknights_exchange_info = pb.pb_ArknightsExchangeInfoDecode(decoder:getsubmsg(239))
    tb.arknights_recruit_info = pb.pb_ArknightsRecruitInfoDecode(decoder:getsubmsg(240))
    tb.arknights_game_info = pb.pb_ArknightsGameInfoDecode(decoder:getsubmsg(241))
    tb.ahsarah_travel_info = pb.pb_AhsarahTravelInfoDecode(decoder:getsubmsg(242))
    return tb
end

function pb.pb_ActivityInfoEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.actv_type) then    encoder:addi32(2, tb.actv_type)    end
    if(tb.start_date) then    encoder:addi64(3, tb.start_date)    end
    if(tb.end_date) then    encoder:addi64(4, tb.end_date)    end
    if(tb.is_tracking) then    encoder:addbool(5, tb.is_tracking)    end
    if(tb.attend_info) then    pb.pb_AttendInfoEncode(tb.attend_info, encoder:addsubmsg(6))    end
    if(tb.task_info) then
        for i=1,#(tb.task_info) do
            pb.pb_ActivityTaskInfoEncode(tb.task_info[i], encoder:addsubmsg(7))
        end
    end
    if(tb.expired_task_info) then
        for i=1,#(tb.expired_task_info) do
            pb.pb_ActivityTaskInfoEncode(tb.expired_task_info[i], encoder:addsubmsg(99))
        end
    end
    if(tb.content) then    encoder:addstr(8, tb.content)    end
    if(tb.currency_id) then    encoder:addu64(9, tb.currency_id)    end
    if(tb.currency_num) then    encoder:addu64(10, tb.currency_num)    end
    if(tb.tab_belonging) then    encoder:addi32(11, tb.tab_belonging)    end
    if(tb.manual_tracking) then    encoder:addbool(12, tb.manual_tracking)    end
    if(tb.order_weight) then    encoder:addi32(13, tb.order_weight)    end
    if(tb.milestone_award) then
        for i=1,#(tb.milestone_award) do
            pb.pb_ActivityMilestoneAwardEncode(tb.milestone_award[i], encoder:addsubmsg(14))
        end
    end
    if(tb.exchange_item) then
        for i=1,#(tb.exchange_item) do
            pb.pb_ActivityExchangePropEncode(tb.exchange_item[i], encoder:addsubmsg(16))
        end
    end
    if(tb.prop_exchange_items) then
        for i=1,#(tb.prop_exchange_items) do
            pb.pb_ActivityExchangePropEncode(tb.prop_exchange_items[i], encoder:addsubmsg(17))
        end
    end
    if(tb.final_award) then
        for i=1,#(tb.final_award) do
            pb.pb_ActivityAwardEncode(tb.final_award[i], encoder:addsubmsg(15))
        end
    end
    if(tb.final_award_received) then    encoder:addbool(20, tb.final_award_received)    end
    if(tb.recruit_ms_tasks) then
        for i=1,#(tb.recruit_ms_tasks) do
            pb.pb_ActivityTaskInfoEncode(tb.recruit_ms_tasks[i], encoder:addsubmsg(30))
        end
    end
    if(tb.next_refresh_time) then    encoder:addi64(31, tb.next_refresh_time)    end
    if(tb.available_refresh_times) then    encoder:addi32(44, tb.available_refresh_times)    end
    if(tb.mode_leaning) then    encoder:addi32(32, tb.mode_leaning)    end
    if(tb.mode_tag) then    encoder:addi32(33, tb.mode_tag)    end
    if(tb.card_display) then    encoder:addu32(34, tb.card_display)    end
    if(tb.info1) then    encoder:addstr(35, tb.info1)    end
    if(tb.info2) then    encoder:addstr(36, tb.info2)    end
    if(tb.name) then    encoder:addstr(37, tb.name)    end
    if(tb.desc) then    encoder:addstr(38, tb.desc)    end
    if(tb.details) then    encoder:addstr(39, tb.details)    end
    if(tb.reward_showed1) then    encoder:addu64(40, tb.reward_showed1)    end
    if(tb.reward_showed2) then    encoder:addu64(41, tb.reward_showed2)    end
    if(tb.reward_showed3) then    encoder:addu64(42, tb.reward_showed3)    end
    if(tb.manual_track) then    encoder:addbool(43, tb.manual_track)    end
    if(tb.details_show_way) then    encoder:addu32(46, tb.details_show_way)    end
    if(tb.info3) then    encoder:addstr(47, tb.info3)    end
    if(tb.time_show_way) then    encoder:addu32(48, tb.time_show_way)    end
    if(tb.task_show_way) then    encoder:addu32(49, tb.task_show_way)    end
    if(tb.classify_config) then
        for i=1,#(tb.classify_config) do
            pb.pb_NewbieGoalTypesClassifyConfigEncode(tb.classify_config[i], encoder:addsubmsg(50))
        end
    end
    if(tb.answers) then
        for i=1,#(tb.answers) do
            pb.pb_ActivityQuestionAnswerEncode(tb.answers[i], encoder:addsubmsg(51))
        end
    end
    if(tb.exchange_pages) then
        for i=1,#(tb.exchange_pages) do
            pb.pb_ActivityExchangePageEncode(tb.exchange_pages[i], encoder:addsubmsg(52))
        end
    end
    if(tb.invalid) then    encoder:addbool(100, tb.invalid)    end
    if(tb.finished) then    encoder:addbool(101, tb.finished)    end
    if(tb.listing_tips) then    encoder:addbool(102, tb.listing_tips)    end
    if(tb.label_desc) then    encoder:addstr(103, tb.label_desc)    end
    if(tb.end_notice_time) then    encoder:addi64(104, tb.end_notice_time)    end
    if(tb.is_important) then    encoder:addbool(105, tb.is_important)    end
    if(tb.important_pic) then    encoder:addstr(106, tb.important_pic)    end
    if(tb.important_video) then    encoder:addstr(107, tb.important_video)    end
    if(tb.second_important_pic) then    encoder:addstr(108, tb.second_important_pic)    end
    if(tb.module_type) then    encoder:addi32(109, tb.module_type)    end
    if(tb.important_desc) then    encoder:addstr(110, tb.important_desc)    end
    if(tb.cooperation_label) then    encoder:addstr(111, tb.cooperation_label)    end
    if(tb.kol_weapons) then
        for i=1,#(tb.kol_weapons) do
            pb.pb_KOLWeaponTasteConfigEncode(tb.kol_weapons[i], encoder:addsubmsg(200))
        end
    end
    if(tb.jump_to_config) then
        for i=1,#(tb.jump_to_config) do
            pb.pb_ActivityJumpConfigEncode(tb.jump_to_config[i], encoder:addsubmsg(210))
        end
    end
    if(tb.password_boxes) then
        for i=1,#(tb.password_boxes) do
            pb.pb_PasswordBoxEncode(tb.password_boxes[i], encoder:addsubmsg(220))
        end
    end
    if(tb.chip_maps) then
        for i=1,#(tb.chip_maps) do
            pb.pb_MapInfoEncode(tb.chip_maps[i], encoder:addsubmsg(221))
        end
    end
    if(tb.moss_construct_info) then    pb.pb_MossConstructInfoEncode(tb.moss_construct_info, encoder:addsubmsg(222))    end
    if(tb.newbie_challenge_info) then    pb.pb_NewbieChallengeInfoEncode(tb.newbie_challenge_info, encoder:addsubmsg(223))    end
    if(tb.lottery_info) then    pb.pb_LotteryInfoEncode(tb.lottery_info, encoder:addsubmsg(224))    end
    if(tb.star_fire_info) then    pb.pb_StarFireInfoEncode(tb.star_fire_info, encoder:addsubmsg(225))    end
    if(tb.archive_info) then    pb.pb_ArchiveInfoEncode(tb.archive_info, encoder:addsubmsg(226))    end
    if(tb.relink_info) then    pb.pb_RelinkInfoEncode(tb.relink_info, encoder:addsubmsg(227))    end
    if(tb.return_info) then    pb.pb_ReturnInfoEncode(tb.return_info, encoder:addsubmsg(228))    end
    if(tb.act_theme_eavesdropping) then    pb.pb_ActThemeEavesdroppingInfoEncode(tb.act_theme_eavesdropping, encoder:addsubmsg(229))    end
    if(tb.survey_boss_info) then    pb.pb_SurveyBossInfoEncode(tb.survey_boss_info, encoder:addsubmsg(230))    end
    if(tb.act_theme_star) then    pb.pb_ActThemeStarInfoEncode(tb.act_theme_star, encoder:addsubmsg(231))    end
    if(tb.make_drink_info) then    pb.pb_MakeDrinkInfoEncode(tb.make_drink_info, encoder:addsubmsg(232))    end
    if(tb.act_theme_she_one_card_info) then    pb.pb_ActThemeSheOneCardInfoEncode(tb.act_theme_she_one_card_info, encoder:addsubmsg(233))    end
    if(tb.weapon_delivery_info) then    pb.pb_WeaponDeliveryInfoEncode(tb.weapon_delivery_info, encoder:addsubmsg(234))    end
    if(tb.sbc_info) then    pb.pb_SBCInfoEncode(tb.sbc_info, encoder:addsubmsg(235))    end
    if(tb.mp_commander_info) then    pb.pb_MPCommanderInfoEncode(tb.mp_commander_info, encoder:addsubmsg(236))    end
    if(tb.simple_milestone_info) then    pb.pb_SimpleMilestoneInfoEncode(tb.simple_milestone_info, encoder:addsubmsg(237))    end
    if(tb.pandora_info) then    pb.pb_PandoraInfoEncode(tb.pandora_info, encoder:addsubmsg(238))    end
    if(tb.arknights_exchange_info) then    pb.pb_ArknightsExchangeInfoEncode(tb.arknights_exchange_info, encoder:addsubmsg(239))    end
    if(tb.arknights_recruit_info) then    pb.pb_ArknightsRecruitInfoEncode(tb.arknights_recruit_info, encoder:addsubmsg(240))    end
    if(tb.arknights_game_info) then    pb.pb_ArknightsGameInfoEncode(tb.arknights_game_info, encoder:addsubmsg(241))    end
    if(tb.ahsarah_travel_info) then    pb.pb_AhsarahTravelInfoEncode(tb.ahsarah_travel_info, encoder:addsubmsg(242))    end
end

function pb.pb_NewbieChallengeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_NewbieChallengeInfo) or {} 
    tb.final_award_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.final_award_list[k] = pb.pb_NewbieChallengeFinalAwardDecode(v)
    end
    return tb
end

function pb.pb_NewbieChallengeInfoEncode(tb, encoder)
    if(tb.final_award_list) then
        for i=1,#(tb.final_award_list) do
            pb.pb_NewbieChallengeFinalAwardEncode(tb.final_award_list[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_NewbieChallengeFinalAwardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_NewbieChallengeFinalAward) or {} 
    tb.final_award = pb.pb_ActivityAwardDecode(decoder:getsubmsg(1))
    local __final_award_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __final_award_type ~= 0 then tb.final_award_type = __final_award_type end
    local __image = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    return tb
end

function pb.pb_NewbieChallengeFinalAwardEncode(tb, encoder)
    if(tb.final_award) then    pb.pb_ActivityAwardEncode(tb.final_award, encoder:addsubmsg(1))    end
    if(tb.final_award_type) then    encoder:addu32(2, tb.final_award_type)    end
    if(tb.image) then    encoder:addstr(3, tb.image)    end
end

function pb.pb_MossConstructInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MossConstructInfo) or {} 
    tb.installed_prop_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.installed_prop_list[k] = pb.pb_InstalledPropDecode(v)
    end
    tb.calculate_info = pb.pb_MossCalculateInfoDecode(decoder:getsubmsg(2))
    tb.moss_desc_info = pb.pb_MossDescInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_MossConstructInfoEncode(tb, encoder)
    if(tb.installed_prop_list) then
        for i=1,#(tb.installed_prop_list) do
            pb.pb_InstalledPropEncode(tb.installed_prop_list[i], encoder:addsubmsg(1))
        end
    end
    if(tb.calculate_info) then    pb.pb_MossCalculateInfoEncode(tb.calculate_info, encoder:addsubmsg(2))    end
    if(tb.moss_desc_info) then    pb.pb_MossDescInfoEncode(tb.moss_desc_info, encoder:addsubmsg(3))    end
end

function pb.pb_MossCalculateInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MossCalculateInfo) or {} 
    local __current_hardware_value = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __current_hardware_value ~= 0 then tb.current_hardware_value = __current_hardware_value end
    local __current_software_value = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __current_software_value ~= 0 then tb.current_software_value = __current_software_value end
    local __max_power = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __max_power ~= 0 then tb.max_power = __max_power end
    local __last_hardware_value = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __last_hardware_value ~= 0 then tb.last_hardware_value = __last_hardware_value end
    local __last_software_value = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __last_software_value ~= 0 then tb.last_software_value = __last_software_value end
    local __current_mandel_brick_num = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __current_mandel_brick_num ~= 0 then tb.current_mandel_brick_num = __current_mandel_brick_num end
    return tb
end

function pb.pb_MossCalculateInfoEncode(tb, encoder)
    if(tb.current_hardware_value) then    encoder:addu64(1, tb.current_hardware_value)    end
    if(tb.current_software_value) then    encoder:addu64(2, tb.current_software_value)    end
    if(tb.max_power) then    encoder:addu64(3, tb.max_power)    end
    if(tb.last_hardware_value) then    encoder:addu64(4, tb.last_hardware_value)    end
    if(tb.last_software_value) then    encoder:addu64(5, tb.last_software_value)    end
    if(tb.current_mandel_brick_num) then    encoder:addu64(6, tb.current_mandel_brick_num)    end
end

function pb.pb_MossDescInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MossDescInfo) or {} 
    local __left_pic = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __left_pic ~= "" then tb.left_pic = __left_pic end
    local __left_title = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __left_title ~= "" then tb.left_title = __left_title end
    local __left_text = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __left_text ~= "" then tb.left_text = __left_text end
    local __right_pic = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __right_pic ~= "" then tb.right_pic = __right_pic end
    local __right_title = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __right_title ~= "" then tb.right_title = __right_title end
    local __right_text = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __right_text ~= "" then tb.right_text = __right_text end
    local __up_text = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __up_text ~= "" then tb.up_text = __up_text end
    local __total_max_num = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __total_max_num ~= 0 then tb.total_max_num = __total_max_num end
    return tb
end

function pb.pb_MossDescInfoEncode(tb, encoder)
    if(tb.left_pic) then    encoder:addstr(1, tb.left_pic)    end
    if(tb.left_title) then    encoder:addstr(2, tb.left_title)    end
    if(tb.left_text) then    encoder:addstr(3, tb.left_text)    end
    if(tb.right_pic) then    encoder:addstr(4, tb.right_pic)    end
    if(tb.right_title) then    encoder:addstr(5, tb.right_title)    end
    if(tb.right_text) then    encoder:addstr(6, tb.right_text)    end
    if(tb.up_text) then    encoder:addstr(7, tb.up_text)    end
    if(tb.total_max_num) then    encoder:addu32(8, tb.total_max_num)    end
end

function pb.pb_LotteryInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryInfo) or {} 
    local __currency_effective_day = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __currency_effective_day ~= 0 then tb.currency_effective_day = __currency_effective_day end
    local __LotteryPoolId = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __LotteryPoolId ~= 0 then tb.LotteryPoolId = __LotteryPoolId end
    tb.get_currency_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.get_currency_info_list[k] = pb.pb_LotteryGetCurrencyInfoDecode(v)
    end
    local __lottery_item_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __lottery_item_id ~= 0 then tb.lottery_item_id = __lottery_item_id end
    return tb
end

function pb.pb_LotteryInfoEncode(tb, encoder)
    if(tb.currency_effective_day) then    encoder:addu32(1, tb.currency_effective_day)    end
    if(tb.LotteryPoolId) then    encoder:addu64(2, tb.LotteryPoolId)    end
    if(tb.get_currency_info_list) then
        for i=1,#(tb.get_currency_info_list) do
            pb.pb_LotteryGetCurrencyInfoEncode(tb.get_currency_info_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.lottery_item_id) then    encoder:addu64(4, tb.lottery_item_id)    end
end

function pb.pb_StarFireInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StarFireInfo) or {} 
    tb.map_config = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.map_config[k] = pb.pb_StarFireAreaConfigDecode(v)
    end
    tb.daily_info = pb.pb_StartFireDailyInfoDecode(decoder:getsubmsg(2))
    local __current_phase = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __current_phase ~= 0 then tb.current_phase = __current_phase end
    local __once_got_key = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __once_got_key ~= false then tb.once_got_key = __once_got_key end
    tb.task_info = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.task_info[k] = pb.pb_StarFireTaskInfoDecode(v)
    end
    tb.david_award = pb.pb_ActivityAwardDecode(decoder:getsubmsg(6))
    local __david_charge_task_num = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __david_charge_task_num ~= 0 then tb.david_charge_task_num = __david_charge_task_num end
    return tb
end

function pb.pb_StarFireInfoEncode(tb, encoder)
    if(tb.map_config) then
        for i=1,#(tb.map_config) do
            pb.pb_StarFireAreaConfigEncode(tb.map_config[i], encoder:addsubmsg(1))
        end
    end
    if(tb.daily_info) then    pb.pb_StartFireDailyInfoEncode(tb.daily_info, encoder:addsubmsg(2))    end
    if(tb.current_phase) then    encoder:addu32(3, tb.current_phase)    end
    if(tb.once_got_key) then    encoder:addbool(4, tb.once_got_key)    end
    if(tb.task_info) then
        for i=1,#(tb.task_info) do
            pb.pb_StarFireTaskInfoEncode(tb.task_info[i], encoder:addsubmsg(5))
        end
    end
    if(tb.david_award) then    pb.pb_ActivityAwardEncode(tb.david_award, encoder:addsubmsg(6))    end
    if(tb.david_charge_task_num) then    encoder:addu32(7, tb.david_charge_task_num)    end
end

function pb.pb_StarFireAreaConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StarFireAreaConfig) or {} 
    local __area_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __area_id ~= 0 then tb.area_id = __area_id end
    local __task_line_index = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_line_index ~= 0 then tb.task_line_index = __task_line_index end
    local __pic_url = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __pic_url ~= "" then tb.pic_url = __pic_url end
    local __current_sequence = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __current_sequence ~= 0 then tb.current_sequence = __current_sequence end
    local __tower_pic_url = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __tower_pic_url ~= "" then tb.tower_pic_url = __tower_pic_url end
    return tb
end

function pb.pb_StarFireAreaConfigEncode(tb, encoder)
    if(tb.area_id) then    encoder:addu64(1, tb.area_id)    end
    if(tb.task_line_index) then    encoder:addu64(2, tb.task_line_index)    end
    if(tb.pic_url) then    encoder:addstr(3, tb.pic_url)    end
    if(tb.current_sequence) then    encoder:addi32(4, tb.current_sequence)    end
    if(tb.tower_pic_url) then    encoder:addstr(5, tb.tower_pic_url)    end
end

function pb.pb_StarFireTaskInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StarFireTaskInfo) or {} 
    local __task_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __clue_text = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __clue_text ~= "" then tb.clue_text = __clue_text end
    local __clue_signature = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __clue_signature ~= "" then tb.clue_signature = __clue_signature end
    local __stage = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __stage ~= 0 then tb.stage = __stage end
    return tb
end

function pb.pb_StarFireTaskInfoEncode(tb, encoder)
    if(tb.task_id) then    encoder:addu64(1, tb.task_id)    end
    if(tb.clue_text) then    encoder:addstr(2, tb.clue_text)    end
    if(tb.clue_signature) then    encoder:addstr(3, tb.clue_signature)    end
    if(tb.stage) then    encoder:addu32(4, tb.stage)    end
end

function pb.pb_StartFireDailyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StartFireDailyInfo) or {} 
    tb.task_info = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.task_info[k] = pb.pb_ActivityTaskInfoDecode(v)
    end
    local __next_refresh_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __next_refresh_time ~= 0 then tb.next_refresh_time = __next_refresh_time end
    local __activity_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_StartFireDailyInfoEncode(tb, encoder)
    if(tb.task_info) then
        for i=1,#(tb.task_info) do
            pb.pb_ActivityTaskInfoEncode(tb.task_info[i], encoder:addsubmsg(1))
        end
    end
    if(tb.next_refresh_time) then    encoder:addi64(2, tb.next_refresh_time)    end
    if(tb.activity_id) then    encoder:addu64(3, tb.activity_id)    end
end

function pb.pb_ArchiveInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArchiveInfo) or {} 
    tb.enemy_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.enemy_info_list[k] = pb.pb_ArchiveEnemyInfoDecode(v)
    end
    tb.task_line_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.task_line_info_list[k] = pb.pb_ArchiveTaskLineInfoDecode(v)
    end
    tb.hidden_tasks = decoder:getu64ary(3)
    local __is_first = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_first ~= false then tb.is_first = __is_first end
    local __final_award_task_num = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __final_award_task_num ~= 0 then tb.final_award_task_num = __final_award_task_num end
    tb.sequence_award_list = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.sequence_award_list[k] = pb.pb_ArchiveSequenceInfoDecode(v)
    end
    return tb
end

function pb.pb_ArchiveInfoEncode(tb, encoder)
    if(tb.enemy_info_list) then
        for i=1,#(tb.enemy_info_list) do
            pb.pb_ArchiveEnemyInfoEncode(tb.enemy_info_list[i], encoder:addsubmsg(1))
        end
    end
    if(tb.task_line_info_list) then
        for i=1,#(tb.task_line_info_list) do
            pb.pb_ArchiveTaskLineInfoEncode(tb.task_line_info_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.hidden_tasks) then    encoder:addu64(3, tb.hidden_tasks)    end
    if(tb.is_first) then    encoder:addbool(5, tb.is_first)    end
    if(tb.final_award_task_num) then    encoder:addu32(6, tb.final_award_task_num)    end
    if(tb.sequence_award_list) then
        for i=1,#(tb.sequence_award_list) do
            pb.pb_ArchiveSequenceInfoEncode(tb.sequence_award_list[i], encoder:addsubmsg(8))
        end
    end
end

function pb.pb_ArchiveEnemyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArchiveEnemyInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __code = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __code ~= "" then tb.code = __code end
    local __level = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __level ~= "" then tb.level = __level end
    local __birthday = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __birthday ~= "" then tb.birthday = __birthday end
    local __height = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __height ~= "" then tb.height = __height end
    local __weight = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __weight ~= "" then tb.weight = __weight end
    local __camp = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __camp ~= "" then tb.camp = __camp end
    local __map = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __map ~= "" then tb.map = __map end
    tb.underling = decoder:getu64ary(10)
    local __abstract = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __abstract ~= "" then tb.abstract = __abstract end
    local __image = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    local __type = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __locked = decoder:getbool(14)
    if not PB_USE_DEFAULT_TABLE or __locked ~= false then tb.locked = __locked end
    local __displayed = decoder:getbool(15)
    if not PB_USE_DEFAULT_TABLE or __displayed ~= false then tb.displayed = __displayed end
    local __task_id = decoder:getu64(16)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __propId = decoder:getu64(17)
    if not PB_USE_DEFAULT_TABLE or __propId ~= 0 then tb.propId = __propId end
    return tb
end

function pb.pb_ArchiveEnemyInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.code) then    encoder:addstr(3, tb.code)    end
    if(tb.level) then    encoder:addstr(4, tb.level)    end
    if(tb.birthday) then    encoder:addstr(5, tb.birthday)    end
    if(tb.height) then    encoder:addstr(6, tb.height)    end
    if(tb.weight) then    encoder:addstr(7, tb.weight)    end
    if(tb.camp) then    encoder:addstr(8, tb.camp)    end
    if(tb.map) then    encoder:addstr(9, tb.map)    end
    if(tb.underling) then    encoder:addu64(10, tb.underling)    end
    if(tb.abstract) then    encoder:addstr(11, tb.abstract)    end
    if(tb.image) then    encoder:addstr(12, tb.image)    end
    if(tb.type) then    encoder:addu32(13, tb.type)    end
    if(tb.locked) then    encoder:addbool(14, tb.locked)    end
    if(tb.displayed) then    encoder:addbool(15, tb.displayed)    end
    if(tb.task_id) then    encoder:addu64(16, tb.task_id)    end
    if(tb.propId) then    encoder:addu64(17, tb.propId)    end
end

function pb.pb_ArchiveTaskLineInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArchiveTaskLineInfo) or {} 
    local __task_line = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_line ~= 0 then tb.task_line = __task_line end
    local __start_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __last_sequence_order = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __last_sequence_order ~= 0 then tb.last_sequence_order = __last_sequence_order end
    local __is_open = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_open ~= false then tb.is_open = __is_open end
    local __map = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __map ~= "" then tb.map = __map end
    tb.sequence_one_enemy_map = decoder:getstrary(7)
    local __sequence_one_enemy_camp = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __sequence_one_enemy_camp ~= "" then tb.sequence_one_enemy_camp = __sequence_one_enemy_camp end
    return tb
end

function pb.pb_ArchiveTaskLineInfoEncode(tb, encoder)
    if(tb.task_line) then    encoder:addu64(1, tb.task_line)    end
    if(tb.start_time) then    encoder:addi64(2, tb.start_time)    end
    if(tb.last_sequence_order) then    encoder:addu32(4, tb.last_sequence_order)    end
    if(tb.is_open) then    encoder:addbool(5, tb.is_open)    end
    if(tb.map) then    encoder:addstr(6, tb.map)    end
    if(tb.sequence_one_enemy_map) then    encoder:addstr(7, tb.sequence_one_enemy_map)    end
    if(tb.sequence_one_enemy_camp) then    encoder:addstr(8, tb.sequence_one_enemy_camp)    end
end

function pb.pb_ArchiveSequenceInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArchiveSequenceInfo) or {} 
    local __task_line = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_line ~= 0 then tb.task_line = __task_line end
    local __sequence = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __sequence ~= 0 then tb.sequence = __sequence end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(3))
    local __received = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    local __begin_text = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __begin_text ~= "" then tb.begin_text = __begin_text end
    local __end_text = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __end_text ~= "" then tb.end_text = __end_text end
    return tb
end

function pb.pb_ArchiveSequenceInfoEncode(tb, encoder)
    if(tb.task_line) then    encoder:addu64(1, tb.task_line)    end
    if(tb.sequence) then    encoder:addi32(2, tb.sequence)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(3))    end
    if(tb.received) then    encoder:addbool(4, tb.received)    end
    if(tb.begin_text) then    encoder:addstr(5, tb.begin_text)    end
    if(tb.end_text) then    encoder:addstr(6, tb.end_text)    end
end

function pb.pb_RelinkInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RelinkInfo) or {} 
    tb.material_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.material_list[k] = pb.pb_RelinkMaterialInfoDecode(v)
    end
    local __relink_prop_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __relink_prop_id ~= 0 then tb.relink_prop_id = __relink_prop_id end
    local __limit_make_num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __limit_make_num ~= 0 then tb.limit_make_num = __limit_make_num end
    local __today_make_num = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __today_make_num ~= 0 then tb.today_make_num = __today_make_num end
    local __total_use_time = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __total_use_time ~= 0 then tb.total_use_time = __total_use_time end
    return tb
end

function pb.pb_RelinkInfoEncode(tb, encoder)
    if(tb.material_list) then
        for i=1,#(tb.material_list) do
            pb.pb_RelinkMaterialInfoEncode(tb.material_list[i], encoder:addsubmsg(1))
        end
    end
    if(tb.relink_prop_id) then    encoder:addu64(2, tb.relink_prop_id)    end
    if(tb.limit_make_num) then    encoder:addi32(3, tb.limit_make_num)    end
    if(tb.today_make_num) then    encoder:addi32(4, tb.today_make_num)    end
    if(tb.total_use_time) then    encoder:addi32(5, tb.total_use_time)    end
end

function pb.pb_RelinkMaterialInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RelinkMaterialInfo) or {} 
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __unlocked = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __unlocked ~= false then tb.unlocked = __unlocked end
    local __related_task_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __related_task_id ~= 0 then tb.related_task_id = __related_task_id end
    return tb
end

function pb.pb_RelinkMaterialInfoEncode(tb, encoder)
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(1))    end
    if(tb.unlocked) then    encoder:addbool(2, tb.unlocked)    end
    if(tb.related_task_id) then    encoder:addu64(3, tb.related_task_id)    end
end

function pb.pb_SurveyBossInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SurveyBossInfo) or {} 
    tb.required_tasks = decoder:getu64ary(2)
    local __boss_image = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __boss_image ~= "" then tb.boss_image = __boss_image end
    local __level_image = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __level_image ~= "" then tb.level_image = __level_image end
    local __triggered = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __triggered ~= false then tb.triggered = __triggered end
    local __type = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __file_desc = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __file_desc ~= "" then tb.file_desc = __file_desc end
    return tb
end

function pb.pb_SurveyBossInfoEncode(tb, encoder)
    if(tb.required_tasks) then    encoder:addu64(2, tb.required_tasks)    end
    if(tb.boss_image) then    encoder:addstr(3, tb.boss_image)    end
    if(tb.level_image) then    encoder:addstr(4, tb.level_image)    end
    if(tb.triggered) then    encoder:addbool(5, tb.triggered)    end
    if(tb.type) then    encoder:addi32(6, tb.type)    end
    if(tb.file_desc) then    encoder:addstr(7, tb.file_desc)    end
end

function pb.pb_ActivityQuestionAnswerDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityQuestionAnswer) or {} 
    local __task_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __goal_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __goal_id ~= 0 then tb.goal_id = __goal_id end
    local __answer = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __answer ~= 0 then tb.answer = __answer end
    return tb
end

function pb.pb_ActivityQuestionAnswerEncode(tb, encoder)
    if(tb.task_id) then    encoder:addu64(1, tb.task_id)    end
    if(tb.goal_id) then    encoder:addu64(2, tb.goal_id)    end
    if(tb.answer) then    encoder:addu32(3, tb.answer)    end
end

function pb.pb_SurveyBossExchangeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SurveyBossExchangeInfo) or {} 
    local __exchange_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    return tb
end

function pb.pb_SurveyBossExchangeInfoEncode(tb, encoder)
    if(tb.exchange_id) then    encoder:addu64(1, tb.exchange_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
end

function pb.pb_MakeDrinkInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MakeDrinkInfo) or {} 
    tb.material_infos = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.material_infos[k] = pb.pb_DrinkMaterialInfoDecode(v)
    end
    tb.drink_info = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.drink_info[k] = pb.pb_DrinkInfoDecode(v)
    end
    local __total_make_time = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __total_make_time ~= 0 then tb.total_make_time = __total_make_time end
    tb.unlocked_combinations = decoder:getu64ary(4)
    return tb
end

function pb.pb_MakeDrinkInfoEncode(tb, encoder)
    if(tb.material_infos) then
        for i=1,#(tb.material_infos) do
            pb.pb_DrinkMaterialInfoEncode(tb.material_infos[i], encoder:addsubmsg(1))
        end
    end
    if(tb.drink_info) then
        for i=1,#(tb.drink_info) do
            pb.pb_DrinkInfoEncode(tb.drink_info[i], encoder:addsubmsg(2))
        end
    end
    if(tb.total_make_time) then    encoder:addi32(3, tb.total_make_time)    end
    if(tb.unlocked_combinations) then    encoder:addu64(4, tb.unlocked_combinations)    end
end

function pb.pb_DrinkMaterialInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DrinkMaterialInfo) or {} 
    local __material_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __material_id ~= 0 then tb.material_id = __material_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    return tb
end

function pb.pb_DrinkMaterialInfoEncode(tb, encoder)
    if(tb.material_id) then    encoder:addu64(1, tb.material_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
end

function pb.pb_DrinkInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DrinkInfo) or {} 
    local __drink_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __drink_id ~= 0 then tb.drink_id = __drink_id end
    local __unlocked = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __unlocked ~= false then tb.unlocked = __unlocked end
    return tb
end

function pb.pb_DrinkInfoEncode(tb, encoder)
    if(tb.drink_id) then    encoder:addu64(1, tb.drink_id)    end
    if(tb.unlocked) then    encoder:addbool(2, tb.unlocked)    end
end

function pb.pb_SimpleMilestoneInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SimpleMilestoneInfo) or {} 
    local __current_score = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __current_score ~= 0 then tb.current_score = __current_score end
    local __today_score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __today_score ~= 0 then tb.today_score = __today_score end
    local __daily_max_score = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __daily_max_score ~= 0 then tb.daily_max_score = __daily_max_score end
    tb.score_descs = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.score_descs[k] = pb.pb_SimpleMilestoneScoreDescDecode(v)
    end
    return tb
end

function pb.pb_SimpleMilestoneInfoEncode(tb, encoder)
    if(tb.current_score) then    encoder:addi64(1, tb.current_score)    end
    if(tb.today_score) then    encoder:addi64(2, tb.today_score)    end
    if(tb.daily_max_score) then    encoder:addi64(3, tb.daily_max_score)    end
    if(tb.score_descs) then
        for i=1,#(tb.score_descs) do
            pb.pb_SimpleMilestoneScoreDescEncode(tb.score_descs[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_SimpleMilestoneScoreDescDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SimpleMilestoneScoreDesc) or {} 
    local __icon = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __icon ~= "" then tb.icon = __icon end
    local __text = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __text ~= "" then tb.text = __text end
    return tb
end

function pb.pb_SimpleMilestoneScoreDescEncode(tb, encoder)
    if(tb.icon) then    encoder:addstr(1, tb.icon)    end
    if(tb.text) then    encoder:addstr(2, tb.text)    end
end

function pb.pb_LotteryGetCurrencyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryGetCurrencyInfo) or {} 
    local __get_currency_name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __get_currency_name ~= "" then tb.get_currency_name = __get_currency_name end
    local __get_currency_url = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __get_currency_url ~= 0 then tb.get_currency_url = __get_currency_url end
    return tb
end

function pb.pb_LotteryGetCurrencyInfoEncode(tb, encoder)
    if(tb.get_currency_name) then    encoder:addstr(1, tb.get_currency_name)    end
    if(tb.get_currency_url) then    encoder:addu64(2, tb.get_currency_url)    end
end

function pb.pb_WeaponDeliveryInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponDeliveryInfo) or {} 
    local __sol_currency_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __sol_currency_id ~= 0 then tb.sol_currency_id = __sol_currency_id end
    return tb
end

function pb.pb_WeaponDeliveryInfoEncode(tb, encoder)
    if(tb.sol_currency_id) then    encoder:addu64(1, tb.sol_currency_id)    end
end

function pb.pb_PasswordBoxDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PasswordBox) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __unlocked = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __unlocked ~= false then tb.unlocked = __unlocked end
    local __passphrase = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __passphrase ~= "" then tb.passphrase = __passphrase end
    tb.password = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.password[k] = pb.pb_PasswordDigitDecode(v)
    end
    local __name = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __pic = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __pic ~= "" then tb.pic = __pic end
    local __desc = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __required_chip_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __required_chip_id ~= 0 then tb.required_chip_id = __required_chip_id end
    local __required_chip_num = decoder:getu64(14)
    if not PB_USE_DEFAULT_TABLE or __required_chip_num ~= 0 then tb.required_chip_num = __required_chip_num end
    local __mapid = decoder:getu64(15)
    if not PB_USE_DEFAULT_TABLE or __mapid ~= 0 then tb.mapid = __mapid end
    tb.chip_maps = {}
    for k,v in pairs(decoder:getsubmsgary(16)) do
        tb.chip_maps[k] = pb.pb_MapInfoDecode(v)
    end
    return tb
end

function pb.pb_PasswordBoxEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.unlocked) then    encoder:addbool(2, tb.unlocked)    end
    if(tb.passphrase) then    encoder:addstr(3, tb.passphrase)    end
    if(tb.password) then
        for i=1,#(tb.password) do
            pb.pb_PasswordDigitEncode(tb.password[i], encoder:addsubmsg(4))
        end
    end
    if(tb.name) then    encoder:addstr(10, tb.name)    end
    if(tb.pic) then    encoder:addstr(11, tb.pic)    end
    if(tb.desc) then    encoder:addstr(12, tb.desc)    end
    if(tb.required_chip_id) then    encoder:addu64(13, tb.required_chip_id)    end
    if(tb.required_chip_num) then    encoder:addu64(14, tb.required_chip_num)    end
    if(tb.mapid) then    encoder:addu64(15, tb.mapid)    end
    if(tb.chip_maps) then
        for i=1,#(tb.chip_maps) do
            pb.pb_MapInfoEncode(tb.chip_maps[i], encoder:addsubmsg(16))
        end
    end
end

function pb.pb_MapInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MapInfo) or {} 
    local __pic = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __pic ~= "" then tb.pic = __pic end
    local __title = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __title ~= "" then tb.title = __title end
    local __desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    return tb
end

function pb.pb_MapInfoEncode(tb, encoder)
    if(tb.pic) then    encoder:addstr(1, tb.pic)    end
    if(tb.title) then    encoder:addstr(2, tb.title)    end
    if(tb.desc) then    encoder:addstr(3, tb.desc)    end
end

function pb.pb_PasswordDigitDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PasswordDigit) or {} 
    local __index = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __index ~= 0 then tb.index = __index end
    local __digit = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __digit ~= 0 then tb.digit = __digit end
    local __unlocked = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __unlocked ~= false then tb.unlocked = __unlocked end
    return tb
end

function pb.pb_PasswordDigitEncode(tb, encoder)
    if(tb.index) then    encoder:addi32(1, tb.index)    end
    if(tb.digit) then    encoder:addi32(2, tb.digit)    end
    if(tb.unlocked) then    encoder:addbool(3, tb.unlocked)    end
end

function pb.pb_KOLWeaponTasteConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_KOLWeaponTasteConfig) or {} 
    local __weapon_share_code = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_share_code ~= "" then tb.weapon_share_code = __weapon_share_code end
    local __weapon_name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __weapon_name ~= "" then tb.weapon_name = __weapon_name end
    local __weapon_desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __weapon_desc ~= "" then tb.weapon_desc = __weapon_desc end
    local __weapon_preset_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __weapon_preset_id ~= 0 then tb.weapon_preset_id = __weapon_preset_id end
    local __kol_name = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __kol_name ~= "" then tb.kol_name = __kol_name end
    local __kol_avatar = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __kol_avatar ~= "" then tb.kol_avatar = __kol_avatar end
    local __kol_social = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __kol_social ~= "" then tb.kol_social = __kol_social end
    local __task_mode = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __task_mode ~= 0 then tb.task_mode = __task_mode end
    return tb
end

function pb.pb_KOLWeaponTasteConfigEncode(tb, encoder)
    if(tb.weapon_share_code) then    encoder:addstr(1, tb.weapon_share_code)    end
    if(tb.weapon_name) then    encoder:addstr(2, tb.weapon_name)    end
    if(tb.weapon_desc) then    encoder:addstr(3, tb.weapon_desc)    end
    if(tb.weapon_preset_id) then    encoder:addu64(4, tb.weapon_preset_id)    end
    if(tb.kol_name) then    encoder:addstr(10, tb.kol_name)    end
    if(tb.kol_avatar) then    encoder:addstr(11, tb.kol_avatar)    end
    if(tb.kol_social) then    encoder:addstr(12, tb.kol_social)    end
    if(tb.task_mode) then    encoder:addu32(13, tb.task_mode)    end
end

function pb.pb_ActivityJumpConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityJumpConfig) or {} 
    local __jump_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __jump_id ~= 0 then tb.jump_id = __jump_id end
    local __desc = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __actv_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    return tb
end

function pb.pb_ActivityJumpConfigEncode(tb, encoder)
    if(tb.jump_id) then    encoder:addu64(1, tb.jump_id)    end
    if(tb.desc) then    encoder:addstr(2, tb.desc)    end
    if(tb.actv_id) then    encoder:addu64(3, tb.actv_id)    end
end

function pb.pb_NewbieGoalTypesClassifyConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_NewbieGoalTypesClassifyConfig) or {} 
    local __newbie_task_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __newbie_task_type ~= 0 then tb.newbie_task_type = __newbie_task_type end
    tb.included_goal_types = decoder:getu32ary(2)
    local __name = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    return tb
end

function pb.pb_NewbieGoalTypesClassifyConfigEncode(tb, encoder)
    if(tb.newbie_task_type) then    encoder:addu32(1, tb.newbie_task_type)    end
    if(tb.included_goal_types) then    encoder:addu32(2, tb.included_goal_types)    end
    if(tb.name) then    encoder:addstr(3, tb.name)    end
end

function pb.pb_PandoraInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PandoraInfo) or {} 
    local __app_id = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __app_id ~= "" then tb.app_id = __app_id end
    local __open_args = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __open_args ~= "" then tb.open_args = __open_args end
    return tb
end

function pb.pb_PandoraInfoEncode(tb, encoder)
    if(tb.app_id) then    encoder:addstr(1, tb.app_id)    end
    if(tb.open_args) then    encoder:addstr(2, tb.open_args)    end
end

function pb.pb_ArknightsExchangeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsExchangeInfo) or {} 
    tb.page_info = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.page_info[k] = pb.pb_ArknightsExchangePageInfoDecode(v)
    end
    return tb
end

function pb.pb_ArknightsExchangeInfoEncode(tb, encoder)
    if(tb.page_info) then
        for i=1,#(tb.page_info) do
            pb.pb_ArknightsExchangePageInfoEncode(tb.page_info[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_ArknightsExchangePageInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsExchangePageInfo) or {} 
    local __exchange_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __exchange_type ~= 0 then tb.exchange_type = __exchange_type end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    tb.exchange_ids = decoder:getu64ary(3)
    local __unlock_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    return tb
end

function pb.pb_ArknightsExchangePageInfoEncode(tb, encoder)
    if(tb.exchange_type) then    encoder:addu32(1, tb.exchange_type)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.exchange_ids) then    encoder:addu64(3, tb.exchange_ids)    end
    if(tb.unlock_time) then    encoder:addi64(4, tb.unlock_time)    end
end

function pb.pb_ArknightsRecruitInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitInfo) or {} 
    local __recruit_permission_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __recruit_permission_id ~= 0 then tb.recruit_permission_id = __recruit_permission_id end
    local __sol_money_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __sol_money_id ~= 0 then tb.sol_money_id = __sol_money_id end
    tb.board_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.board_infos[k] = pb.pb_ArknightsRecruitBoardInfoDecode(v)
    end
    tb.hero_infos = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.hero_infos[k] = pb.pb_ArknightsRecruitHeroInfoDecode(v)
    end
    tb.collect_infos = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.collect_infos[k] = pb.pb_ArknightsRecruitCollectInfoDecode(v)
    end
    tb.mp_money_info = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.mp_money_info[k] = pb.pb_ArknightsRecruitMPMoneyInfoDecode(v)
    end
    tb.ad_info = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.ad_info[k] = pb.pb_ArknightsRecruitAdInfoDecode(v)
    end
    local __last_mp_money_update_time = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __last_mp_money_update_time ~= 0 then tb.last_mp_money_update_time = __last_mp_money_update_time end
    local __has_open = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __has_open ~= false then tb.has_open = __has_open end
    return tb
end

function pb.pb_ArknightsRecruitInfoEncode(tb, encoder)
    if(tb.recruit_permission_id) then    encoder:addu64(13, tb.recruit_permission_id)    end
    if(tb.sol_money_id) then    encoder:addu64(2, tb.sol_money_id)    end
    if(tb.board_infos) then
        for i=1,#(tb.board_infos) do
            pb.pb_ArknightsRecruitBoardInfoEncode(tb.board_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.hero_infos) then
        for i=1,#(tb.hero_infos) do
            pb.pb_ArknightsRecruitHeroInfoEncode(tb.hero_infos[i], encoder:addsubmsg(5))
        end
    end
    if(tb.collect_infos) then
        for i=1,#(tb.collect_infos) do
            pb.pb_ArknightsRecruitCollectInfoEncode(tb.collect_infos[i], encoder:addsubmsg(6))
        end
    end
    if(tb.mp_money_info) then
        for i=1,#(tb.mp_money_info) do
            pb.pb_ArknightsRecruitMPMoneyInfoEncode(tb.mp_money_info[i], encoder:addsubmsg(7))
        end
    end
    if(tb.ad_info) then
        for i=1,#(tb.ad_info) do
            pb.pb_ArknightsRecruitAdInfoEncode(tb.ad_info[i], encoder:addsubmsg(8))
        end
    end
    if(tb.last_mp_money_update_time) then    encoder:addi64(9, tb.last_mp_money_update_time)    end
    if(tb.has_open) then    encoder:addbool(12, tb.has_open)    end
end

function pb.pb_ArknightsRecruitBoardInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitBoardInfo) or {} 
    local __state = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __refresh_cost = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __refresh_cost ~= 0 then tb.refresh_cost = __refresh_cost end
    local __time_cost = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __time_cost ~= 0 then tb.time_cost = __time_cost end
    local __recruit_permission_cost = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __recruit_permission_cost ~= 0 then tb.recruit_permission_cost = __recruit_permission_cost end
    local __money_cost = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __money_cost ~= 0 then tb.money_cost = __money_cost end
    local __hero_num = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __hero_num ~= 0 then tb.hero_num = __hero_num end
    local __recruit_end_time = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __recruit_end_time ~= 0 then tb.recruit_end_time = __recruit_end_time end
    local __recruit_ad = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __recruit_ad ~= 0 then tb.recruit_ad = __recruit_ad end
    tb.recruit_hero_ids = decoder:getu64ary(10)
    local __type = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.candidate_recruit_ads = decoder:getu64ary(12)
    return tb
end

function pb.pb_ArknightsRecruitBoardInfoEncode(tb, encoder)
    if(tb.state) then    encoder:addu32(1, tb.state)    end
    if(tb.refresh_cost) then    encoder:addi64(3, tb.refresh_cost)    end
    if(tb.time_cost) then    encoder:addi64(4, tb.time_cost)    end
    if(tb.recruit_permission_cost) then    encoder:addi64(5, tb.recruit_permission_cost)    end
    if(tb.money_cost) then    encoder:addi64(6, tb.money_cost)    end
    if(tb.hero_num) then    encoder:addi64(7, tb.hero_num)    end
    if(tb.recruit_end_time) then    encoder:addi64(8, tb.recruit_end_time)    end
    if(tb.recruit_ad) then    encoder:addu64(9, tb.recruit_ad)    end
    if(tb.recruit_hero_ids) then    encoder:addu64(10, tb.recruit_hero_ids)    end
    if(tb.type) then    encoder:addu32(11, tb.type)    end
    if(tb.candidate_recruit_ads) then    encoder:addu64(12, tb.candidate_recruit_ads)    end
end

function pb.pb_ArknightsRecruitAdInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitAdInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    return tb
end

function pb.pb_ArknightsRecruitAdInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.desc) then    encoder:addstr(3, tb.desc)    end
end

function pb.pb_ArknightsRecruitHeroInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitHeroInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __short_desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __short_desc ~= "" then tb.short_desc = __short_desc end
    local __group_desc = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __group_desc ~= "" then tb.group_desc = __group_desc end
    tb.level_info = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.level_info[k] = pb.pb_ArknightsRecruitHeroLevelInfoDecode(v)
    end
    local __level = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __icon_image = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __icon_image ~= "" then tb.icon_image = __icon_image end
    return tb
end

function pb.pb_ArknightsRecruitHeroInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.short_desc) then    encoder:addstr(3, tb.short_desc)    end
    if(tb.group_desc) then    encoder:addstr(4, tb.group_desc)    end
    if(tb.level_info) then
        for i=1,#(tb.level_info) do
            pb.pb_ArknightsRecruitHeroLevelInfoEncode(tb.level_info[i], encoder:addsubmsg(5))
        end
    end
    if(tb.level) then    encoder:addu32(6, tb.level)    end
    if(tb.icon_image) then    encoder:addstr(7, tb.icon_image)    end
end

function pb.pb_ArknightsRecruitHeroLevelInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitHeroLevelInfo) or {} 
    local __level = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __greet_desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __greet_desc ~= "" then tb.greet_desc = __greet_desc end
    local __intro_desc = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __intro_desc ~= "" then tb.intro_desc = __intro_desc end
    local __image = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    tb.rewards = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.rewards[k] = pb.pb_PropInfoDecode(v)
    end
    local __simple_image = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __simple_image ~= "" then tb.simple_image = __simple_image end
    local __collect_image = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __collect_image ~= "" then tb.collect_image = __collect_image end
    local __collect_simple_image = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __collect_simple_image ~= "" then tb.collect_simple_image = __collect_simple_image end
    return tb
end

function pb.pb_ArknightsRecruitHeroLevelInfoEncode(tb, encoder)
    if(tb.level) then    encoder:addu32(1, tb.level)    end
    if(tb.greet_desc) then    encoder:addstr(3, tb.greet_desc)    end
    if(tb.intro_desc) then    encoder:addstr(4, tb.intro_desc)    end
    if(tb.image) then    encoder:addstr(5, tb.image)    end
    if(tb.rewards) then
        for i=1,#(tb.rewards) do
            pb.pb_PropInfoEncode(tb.rewards[i], encoder:addsubmsg(6))
        end
    end
    if(tb.simple_image) then    encoder:addstr(7, tb.simple_image)    end
    if(tb.collect_image) then    encoder:addstr(8, tb.collect_image)    end
    if(tb.collect_simple_image) then    encoder:addstr(9, tb.collect_simple_image)    end
end

function pb.pb_ArknightsRecruitCollectInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitCollectInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    tb.hero_ids = decoder:getu64ary(3)
    tb.rewards = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.rewards[k] = pb.pb_PropInfoDecode(v)
    end
    local __received = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    return tb
end

function pb.pb_ArknightsRecruitCollectInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.hero_ids) then    encoder:addu64(3, tb.hero_ids)    end
    if(tb.rewards) then
        for i=1,#(tb.rewards) do
            pb.pb_PropInfoEncode(tb.rewards[i], encoder:addsubmsg(4))
        end
    end
    if(tb.received) then    encoder:addbool(5, tb.received)    end
end

function pb.pb_ArknightsRecruitMPMoneyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsRecruitMPMoneyInfo) or {} 
    local __time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __money_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __money_num ~= 0 then tb.money_num = __money_num end
    local __game_mode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    return tb
end

function pb.pb_ArknightsRecruitMPMoneyInfoEncode(tb, encoder)
    if(tb.time) then    encoder:addi64(1, tb.time)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
    if(tb.money_num) then    encoder:addi64(3, tb.money_num)    end
    if(tb.game_mode) then    encoder:addi32(4, tb.game_mode)    end
end

function pb.pb_ActivityRewardExpandInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityRewardExpandInfo) or {} 
    local __tips_id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __tips_id ~= 0 then tb.tips_id = __tips_id end
    return tb
end

function pb.pb_ActivityRewardExpandInfoEncode(tb, encoder)
    if(tb.tips_id) then    encoder:addi32(1, tb.tips_id)    end
end

function pb.pb_CSActivityGetReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetReq) or {} 
    tb.activity_ids = decoder:getu64ary(1)
    local __is_part = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_part ~= false then tb.is_part = __is_part end
    return tb
end

function pb.pb_CSActivityGetReqEncode(tb, encoder)
    if(tb.activity_ids) then    encoder:addu64(1, tb.activity_ids)    end
    if(tb.is_part) then    encoder:addbool(2, tb.is_part)    end
end

function pb.pb_CSActivityGetResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    local __max_tracking_num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __max_tracking_num ~= 0 then tb.max_tracking_num = __max_tracking_num end
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.preview_infos = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.preview_infos[k] = pb.pb_ActivityPreviewInfoDecode(v)
    end
    tb.prop_jump_infos = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.prop_jump_infos[k] = pb.pb_ActivityPropJumpInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityGetResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.max_tracking_num) then    encoder:addi32(3, tb.max_tracking_num)    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.preview_infos) then
        for i=1,#(tb.preview_infos) do
            pb.pb_ActivityPreviewInfoEncode(tb.preview_infos[i], encoder:addsubmsg(5))
        end
    end
    if(tb.prop_jump_infos) then
        for i=1,#(tb.prop_jump_infos) do
            pb.pb_ActivityPropJumpInfoEncode(tb.prop_jump_infos[i], encoder:addsubmsg(6))
        end
    end
end

function pb.pb_ActivityPreviewInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityPreviewInfo) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __start_date = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __start_date ~= 0 then tb.start_date = __start_date end
    local __end_date = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __end_date ~= 0 then tb.end_date = __end_date end
    local __mode_tag = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __mode_tag ~= 0 then tb.mode_tag = __mode_tag end
    local __tab_belonging = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __tab_belonging ~= 0 then tb.tab_belonging = __tab_belonging end
    local __order_weight = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __order_weight ~= 0 then tb.order_weight = __order_weight end
    local __name = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __info2 = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __info2 ~= "" then tb.info2 = __info2 end
    local __label_desc = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __label_desc ~= "" then tb.label_desc = __label_desc end
    local __is_important = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_important ~= false then tb.is_important = __is_important end
    local __important_pic = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __important_pic ~= "" then tb.important_pic = __important_pic end
    local __important_video = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __important_video ~= "" then tb.important_video = __important_video end
    local __second_important_pic = decoder:getstr(13)
    if not PB_USE_DEFAULT_TABLE or __second_important_pic ~= "" then tb.second_important_pic = __second_important_pic end
    local __module_type = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __module_type ~= 0 then tb.module_type = __module_type end
    local __important_desc = decoder:getstr(15)
    if not PB_USE_DEFAULT_TABLE or __important_desc ~= "" then tb.important_desc = __important_desc end
    return tb
end

function pb.pb_ActivityPreviewInfoEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.start_date) then    encoder:addi64(2, tb.start_date)    end
    if(tb.end_date) then    encoder:addi64(3, tb.end_date)    end
    if(tb.mode_tag) then    encoder:addi32(4, tb.mode_tag)    end
    if(tb.tab_belonging) then    encoder:addi32(5, tb.tab_belonging)    end
    if(tb.order_weight) then    encoder:addi32(6, tb.order_weight)    end
    if(tb.name) then    encoder:addstr(7, tb.name)    end
    if(tb.info2) then    encoder:addstr(8, tb.info2)    end
    if(tb.label_desc) then    encoder:addstr(9, tb.label_desc)    end
    if(tb.is_important) then    encoder:addbool(10, tb.is_important)    end
    if(tb.important_pic) then    encoder:addstr(11, tb.important_pic)    end
    if(tb.important_video) then    encoder:addstr(12, tb.important_video)    end
    if(tb.second_important_pic) then    encoder:addstr(13, tb.second_important_pic)    end
    if(tb.module_type) then    encoder:addi32(14, tb.module_type)    end
    if(tb.important_desc) then    encoder:addstr(15, tb.important_desc)    end
end

function pb.pb_ActivityCurrencyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityCurrencyInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    tb.get_activity_ids = decoder:getu64ary(3)
    local __use_activity_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __use_activity_id ~= 0 then tb.use_activity_id = __use_activity_id end
    tb.get_activity_infos = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.get_activity_infos[k] = pb.pb_ActivityCurrencyJumpInfoDecode(v)
    end
    tb.use_activity_info = pb.pb_ActivityCurrencyJumpInfoDecode(decoder:getsubmsg(6))
    local __is_hide = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_hide ~= false then tb.is_hide = __is_hide end
    return tb
end

function pb.pb_ActivityCurrencyInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.num) then    encoder:addi64(2, tb.num)    end
    if(tb.get_activity_ids) then    encoder:addu64(3, tb.get_activity_ids)    end
    if(tb.use_activity_id) then    encoder:addu64(4, tb.use_activity_id)    end
    if(tb.get_activity_infos) then
        for i=1,#(tb.get_activity_infos) do
            pb.pb_ActivityCurrencyJumpInfoEncode(tb.get_activity_infos[i], encoder:addsubmsg(5))
        end
    end
    if(tb.use_activity_info) then    pb.pb_ActivityCurrencyJumpInfoEncode(tb.use_activity_info, encoder:addsubmsg(6))    end
    if(tb.is_hide) then    encoder:addbool(7, tb.is_hide)    end
end

function pb.pb_ActivityCurrencyJumpInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityCurrencyJumpInfo) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __desc = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __jump_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __jump_id ~= 0 then tb.jump_id = __jump_id end
    return tb
end

function pb.pb_ActivityCurrencyJumpInfoEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.desc) then    encoder:addstr(2, tb.desc)    end
    if(tb.jump_id) then    encoder:addu64(3, tb.jump_id)    end
end

function pb.pb_CSActivityReturnGetReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReturnGetReq) or {} 
    return tb
end

function pb.pb_CSActivityReturnGetReqEncode(tb, encoder)
end

function pb.pb_CSActivityReturnGetResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReturnGetRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.activity_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityReturnGetResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_infos) then
        for i=1,#(tb.activity_infos) do
            pb.pb_ActivityInfoEncode(tb.activity_infos[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSActivityGetPropJumpReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetPropJumpReq) or {} 
    return tb
end

function pb.pb_CSActivityGetPropJumpReqEncode(tb, encoder)
end

function pb.pb_CSActivityGetPropJumpResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetPropJumpRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.prop_jump_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.prop_jump_infos[k] = pb.pb_ActivityPropJumpInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityGetPropJumpResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.prop_jump_infos) then
        for i=1,#(tb.prop_jump_infos) do
            pb.pb_ActivityPropJumpInfoEncode(tb.prop_jump_infos[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_ActivityPropJumpInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityPropJumpInfo) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    tb.jump_pair = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.jump_pair[k] = pb.pb_ActivityJumpPairDecode(v)
    end
    return tb
end

function pb.pb_ActivityPropJumpInfoEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.jump_pair) then
        for i=1,#(tb.jump_pair) do
            pb.pb_ActivityJumpPairEncode(tb.jump_pair[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_ActivityJumpPairDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActivityJumpPair) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __jump_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __jump_id ~= 0 then tb.jump_id = __jump_id end
    return tb
end

function pb.pb_ActivityJumpPairEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.jump_id) then    encoder:addu64(2, tb.jump_id)    end
end

function pb.pb_ReturnInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ReturnInfo) or {} 
    local __type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.new_content_part_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.new_content_part_infos[k] = pb.pb_ReturnNewContentPartInfoDecode(v)
    end
    local __match_mode_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    local __consumable_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __consumable_id ~= 0 then tb.consumable_id = __consumable_id end
    local __type_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __type_id ~= 0 then tb.type_id = __type_id end
    tb.images = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.images[k] = pb.pb_FightRewardBackImageDecode(v)
    end
    local __login_day_num = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __login_day_num ~= 0 then tb.login_day_num = __login_day_num end
    local __tdm_use_double_exp_card_num = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __tdm_use_double_exp_card_num ~= 0 then tb.tdm_use_double_exp_card_num = __tdm_use_double_exp_card_num end
    local __tdm_use_double_score_card_num = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __tdm_use_double_score_card_num ~= 0 then tb.tdm_use_double_score_card_num = __tdm_use_double_score_card_num end
    return tb
end

function pb.pb_ReturnInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addu32(1, tb.type)    end
    if(tb.new_content_part_infos) then
        for i=1,#(tb.new_content_part_infos) do
            pb.pb_ReturnNewContentPartInfoEncode(tb.new_content_part_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.match_mode_id) then    encoder:addu64(4, tb.match_mode_id)    end
    if(tb.consumable_id) then    encoder:addu64(5, tb.consumable_id)    end
    if(tb.type_id) then    encoder:addu64(6, tb.type_id)    end
    if(tb.images) then
        for i=1,#(tb.images) do
            pb.pb_FightRewardBackImageEncode(tb.images[i], encoder:addsubmsg(7))
        end
    end
    if(tb.login_day_num) then    encoder:addi32(8, tb.login_day_num)    end
    if(tb.tdm_use_double_exp_card_num) then    encoder:addi64(9, tb.tdm_use_double_exp_card_num)    end
    if(tb.tdm_use_double_score_card_num) then    encoder:addi64(10, tb.tdm_use_double_score_card_num)    end
end

function pb.pb_FightRewardBackImageDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_FightRewardBackImage) or {} 
    local __task_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __image = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    return tb
end

function pb.pb_FightRewardBackImageEncode(tb, encoder)
    if(tb.task_id) then    encoder:addu64(1, tb.task_id)    end
    if(tb.image) then    encoder:addstr(2, tb.image)    end
end

function pb.pb_ReturnNewContentPartInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ReturnNewContentPartInfo) or {} 
    local __title = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __title ~= "" then tb.title = __title end
    local __desc = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    tb.task_id_list = decoder:getu64ary(3)
    local __image = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    local __video = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __video ~= "" then tb.video = __video end
    tb.image_list = decoder:getstrary(6)
    tb.desc_list = decoder:getstrary(7)
    return tb
end

function pb.pb_ReturnNewContentPartInfoEncode(tb, encoder)
    if(tb.title) then    encoder:addstr(1, tb.title)    end
    if(tb.desc) then    encoder:addstr(2, tb.desc)    end
    if(tb.task_id_list) then    encoder:addu64(3, tb.task_id_list)    end
    if(tb.image) then    encoder:addstr(4, tb.image)    end
    if(tb.video) then    encoder:addstr(5, tb.video)    end
    if(tb.image_list) then    encoder:addstr(6, tb.image_list)    end
    if(tb.desc_list) then    encoder:addstr(7, tb.desc_list)    end
end

function pb.pb_CSActivityTaskDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTask) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __state = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    tb.objectives = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.objectives[k] = pb.pb_ActivityObjectiveDecode(v)
    end
    return tb
end

function pb.pb_CSActivityTaskEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
    if(tb.state) then    encoder:addi32(3, tb.state)    end
    if(tb.objectives) then
        for i=1,#(tb.objectives) do
            pb.pb_ActivityObjectiveEncode(tb.objectives[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_CSActivityTaskAcceptReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTaskAcceptReq) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    return tb
end

function pb.pb_CSActivityTaskAcceptReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
end

function pb.pb_CSActivityTaskAcceptResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTaskAcceptRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.task = pb.pb_CSActivityTaskDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityTaskAcceptResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.task) then    pb.pb_CSActivityTaskEncode(tb.task, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityTaskReceiveAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTaskReceiveAwardReq) or {} 
    tb.tasks = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.tasks[k] = pb.pb_CSActivityTaskDecode(v)
    end
    return tb
end

function pb.pb_CSActivityTaskReceiveAwardReqEncode(tb, encoder)
    if(tb.tasks) then
        for i=1,#(tb.tasks) do
            pb.pb_CSActivityTaskEncode(tb.tasks[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_CSActivityTaskReceiveAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTaskReceiveAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.tasks = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.tasks[k] = pb.pb_CSActivityTaskDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityTaskReceiveAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.tasks) then
        for i=1,#(tb.tasks) do
            pb.pb_CSActivityTaskEncode(tb.tasks[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(5))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityAttendReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAttendReq) or {} 
    tb.actv_id = decoder:getu64ary(1)
    local __activity_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityAttendReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.activity_id) then    encoder:addu64(2, tb.activity_id)    end
end

function pb.pb_CSActivityAttendResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAttendRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.mp_data_change = pb.pb_CSMPDataChangeDecode(decoder:getsubmsg(4))
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_CSActivityAttendResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.mp_data_change) then    pb.pb_CSMPDataChangeEncode(tb.mp_data_change, encoder:addsubmsg(4))    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(5))    end
end

function pb.pb_CSActivityRecvAttendAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRecvAttendAwardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __day_number = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __day_number ~= 0 then tb.day_number = __day_number end
    local __type = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    return tb
end

function pb.pb_CSActivityRecvAttendAwardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.day_number) then    encoder:addu32(2, tb.day_number)    end
    if(tb.type) then    encoder:addu32(10, tb.type)    end
end

function pb.pb_CSActivityRecvAttendAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRecvAttendAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    tb.mp_data_change = pb.pb_CSMPDataChangeDecode(decoder:getsubmsg(3))
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(4))
    tb.activity_infos = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.activity_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityRecvAttendAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
    if(tb.mp_data_change) then    pb.pb_CSMPDataChangeEncode(tb.mp_data_change, encoder:addsubmsg(3))    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(4))    end
    if(tb.activity_infos) then
        for i=1,#(tb.activity_infos) do
            pb.pb_ActivityInfoEncode(tb.activity_infos[i], encoder:addsubmsg(5))
        end
    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(6))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityTrackReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTrackReq) or {} 
    tb.actv_id = decoder:getu64ary(1)
    return tb
end

function pb.pb_CSActivityTrackReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
end

function pb.pb_CSActivityTrackResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTrackRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    local __max_tracing_num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __max_tracing_num ~= 0 then tb.max_tracing_num = __max_tracing_num end
    return tb
end

function pb.pb_CSActivityTrackResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.max_tracing_num) then    encoder:addi32(3, tb.max_tracing_num)    end
end

function pb.pb_CSActivityUntrackReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUntrackReq) or {} 
    tb.actv_id = decoder:getu64ary(1)
    return tb
end

function pb.pb_CSActivityUntrackReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
end

function pb.pb_CSActivityUntrackResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUntrackRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityUntrackResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSActivityExchangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchange) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    tb.exchange_id = decoder:getu64ary(2)
    local __currency_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __currency_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __currency_num ~= 0 then tb.currency_num = __currency_num end
    local __password_digit_index = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __password_digit_index ~= 0 then tb.password_digit_index = __password_digit_index end
    return tb
end

function pb.pb_CSActivityExchangeEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.exchange_id) then    encoder:addu64(2, tb.exchange_id)    end
    if(tb.currency_id) then    encoder:addu64(3, tb.currency_id)    end
    if(tb.currency_num) then    encoder:addi64(4, tb.currency_num)    end
    if(tb.password_digit_index) then    encoder:addu32(10, tb.password_digit_index)    end
end

function pb.pb_CSActivityExchangeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangeReq) or {} 
    tb.exchanges = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.exchanges[k] = pb.pb_CSActivityExchangeDecode(v)
    end
    return tb
end

function pb.pb_CSActivityExchangeReqEncode(tb, encoder)
    if(tb.exchanges) then
        for i=1,#(tb.exchanges) do
            pb.pb_CSActivityExchangeEncode(tb.exchanges[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_CSActivityExchangeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.mp_data_change = pb.pb_CSMPDataChangeDecode(decoder:getsubmsg(4))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityExchangeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.mp_data_change) then    pb.pb_CSMPDataChangeEncode(tb.mp_data_change, encoder:addsubmsg(4))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(5))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityPropExchangeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityPropExchangeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __exchange_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __plan_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __plan_id ~= 0 then tb.plan_id = __plan_id end
    local __time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    return tb
end

function pb.pb_CSActivityPropExchangeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.exchange_id) then    encoder:addu64(2, tb.exchange_id)    end
    if(tb.plan_id) then    encoder:addu64(3, tb.plan_id)    end
    if(tb.time) then    encoder:addi64(4, tb.time)    end
end

function pb.pb_CSActivityPropExchangeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityPropExchangeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.activity_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityPropExchangeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_infos) then
        for i=1,#(tb.activity_infos) do
            pb.pb_ActivityInfoEncode(tb.activity_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(6))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityChangeExchangePlanReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityChangeExchangePlanReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __exchange_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __plan_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __plan_id ~= 0 then tb.plan_id = __plan_id end
    return tb
end

function pb.pb_CSActivityChangeExchangePlanReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.exchange_id) then    encoder:addu64(2, tb.exchange_id)    end
    if(tb.plan_id) then    encoder:addu64(3, tb.plan_id)    end
end

function pb.pb_CSActivityChangeExchangePlanResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityChangeExchangePlanRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSActivityChangeExchangePlanResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSActivityFocusExchangeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityFocusExchangeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __exchange_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __is_focus = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_focus ~= false then tb.is_focus = __is_focus end
    return tb
end

function pb.pb_CSActivityFocusExchangeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.exchange_id) then    encoder:addu64(2, tb.exchange_id)    end
    if(tb.is_focus) then    encoder:addbool(3, tb.is_focus)    end
end

function pb.pb_CSActivityFocusExchangeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityFocusExchangeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSActivityFocusExchangeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSActivityMilestoneDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityMilestone) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    tb.stages = decoder:geti32ary(2)
    return tb
end

function pb.pb_CSActivityMilestoneEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.stages) then    encoder:addi32(2, tb.stages)    end
end

function pb.pb_CSActivityReceiveMilestoneAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReceiveMilestoneAwardReq) or {} 
    tb.actvs = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.actvs[k] = pb.pb_CSActivityMilestoneDecode(v)
    end
    local __actv_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    tb.stages = decoder:geti32ary(3)
    return tb
end

function pb.pb_CSActivityReceiveMilestoneAwardReqEncode(tb, encoder)
    if(tb.actvs) then
        for i=1,#(tb.actvs) do
            pb.pb_CSActivityMilestoneEncode(tb.actvs[i], encoder:addsubmsg(1))
        end
    end
    if(tb.actv_id) then    encoder:addu64(2, tb.actv_id)    end
    if(tb.stages) then    encoder:addi32(3, tb.stages)    end
end

function pb.pb_CSActivityReceiveMilestoneAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReceiveMilestoneAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityReceiveMilestoneAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityChangeMilestoneAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityChangeMilestoneAwardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __reward_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __reward_id ~= 0 then tb.reward_id = __reward_id end
    local __prop_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    return tb
end

function pb.pb_CSActivityChangeMilestoneAwardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.reward_id) then    encoder:addu64(2, tb.reward_id)    end
    if(tb.prop_id) then    encoder:addu64(3, tb.prop_id)    end
end

function pb.pb_CSActivityChangeMilestoneAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityChangeMilestoneAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSActivityChangeMilestoneAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSActivityReceiveFinalAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReceiveFinalAwardReq) or {} 
    tb.actv_ids = decoder:getu64ary(1)
    tb.newbie_type_list = decoder:getu32ary(2)
    return tb
end

function pb.pb_CSActivityReceiveFinalAwardReqEncode(tb, encoder)
    if(tb.actv_ids) then    encoder:addu64(1, tb.actv_ids)    end
    if(tb.newbie_type_list) then    encoder:addu32(2, tb.newbie_type_list)    end
end

function pb.pb_CSActivityReceiveFinalAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReceiveFinalAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityReceiveFinalAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityUpdateProgressNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUpdateProgressNtf) or {} 
    tb.data = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.data[k] = pb.pb_CSActivityTaskDecode(v)
    end
    return tb
end

function pb.pb_CSActivityUpdateProgressNtfEncode(tb, encoder)
    if(tb.data) then
        for i=1,#(tb.data) do
            pb.pb_CSActivityTaskEncode(tb.data[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_CSActivityRefreshDailyActivityReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRefreshDailyActivityReq) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    return tb
end

function pb.pb_CSActivityRefreshDailyActivityReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
end

function pb.pb_CSActivityRefreshDailyActivityResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRefreshDailyActivityRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityRefreshDailyActivityResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityCollectionSubmitPropsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityCollectionSubmitPropsReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityCollectionSubmitPropsReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_PropInfoEncode(tb.props[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSActivityCollectionSubmitPropsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityCollectionSubmitPropsRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityCollectionSubmitPropsResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityExchangePasswordReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangePasswordReq) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    local __box_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __box_id ~= 0 then tb.box_id = __box_id end
    local __digit_idx = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __digit_idx ~= 0 then tb.digit_idx = __digit_idx end
    return tb
end

function pb.pb_CSActivityExchangePasswordReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.box_id) then    encoder:addu64(2, tb.box_id)    end
    if(tb.digit_idx) then    encoder:addu32(3, tb.digit_idx)    end
end

function pb.pb_CSActivityExchangePasswordResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangePasswordRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityExchangePasswordResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityExchangeMilestoneAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangeMilestoneAwardReq) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    tb.stages = decoder:getu32ary(2)
    return tb
end

function pb.pb_CSActivityExchangeMilestoneAwardReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.stages) then    encoder:addu32(2, tb.stages)    end
end

function pb.pb_CSActivityExchangeMilestoneAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangeMilestoneAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityExchangeMilestoneAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityInstallPropReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityInstallPropReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.props[k] = pb.pb_InstalledPropDecode(v)
    end
    tb.del_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.del_props[k] = pb.pb_InstalledPropDecode(v)
    end
    return tb
end

function pb.pb_CSActivityInstallPropReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_InstalledPropEncode(tb.props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.del_props) then
        for i=1,#(tb.del_props) do
            pb.pb_InstalledPropEncode(tb.del_props[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSActivityInstallPropResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityInstallPropRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.moss_construct_info = pb.pb_MossConstructInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityInstallPropResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.moss_construct_info) then    pb.pb_MossConstructInfoEncode(tb.moss_construct_info, encoder:addsubmsg(2))    end
end

function pb.pb_InstalledPropDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_InstalledProp) or {} 
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __num = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __unit_power = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __unit_power ~= 0 then tb.unit_power = __unit_power end
    local __max_num = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __max_num ~= 0 then tb.max_num = __max_num end
    local __total_num = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __total_num ~= 0 then tb.total_num = __total_num end
    local __bind_num = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __bind_num ~= 0 then tb.bind_num = __bind_num end
    return tb
end

function pb.pb_InstalledPropEncode(tb, encoder)
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(1))    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
    if(tb.num) then    encoder:addu32(3, tb.num)    end
    if(tb.unit_power) then    encoder:addu32(4, tb.unit_power)    end
    if(tb.max_num) then    encoder:addu32(5, tb.max_num)    end
    if(tb.total_num) then    encoder:addi32(6, tb.total_num)    end
    if(tb.bind_num) then    encoder:addi32(7, tb.bind_num)    end
end

function pb.pb_CSActivityUninstallPropReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUninstallPropReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.props[k] = pb.pb_InstalledPropDecode(v)
    end
    return tb
end

function pb.pb_CSActivityUninstallPropReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_InstalledPropEncode(tb.props[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSActivityUninstallPropResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUninstallPropRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.moss_construct_info = pb.pb_MossConstructInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityUninstallPropResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.moss_construct_info) then    pb.pb_MossConstructInfoEncode(tb.moss_construct_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityMossReceiveMSAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityMossReceiveMSAwardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityMossReceiveMSAwardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityMossReceiveMSAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityMossReceiveMSAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivityMossReceiveMSAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivityAnimationUpdateReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAnimationUpdateReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityAnimationUpdateReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityAnimationUpdateResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAnimationUpdateRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.moss_construct_info = pb.pb_MossConstructInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityAnimationUpdateResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.moss_construct_info) then    pb.pb_MossConstructInfoEncode(tb.moss_construct_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityStarFireChargeTowerReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityStarFireChargeTowerReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    return tb
end

function pb.pb_CSActivityStarFireChargeTowerReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_id) then    encoder:addu64(3, tb.task_id)    end
end

function pb.pb_CSActivityStarFireChargeTowerResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityStarFireChargeTowerRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityStarFireChargeTowerResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityArchiveFirstOpenReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArchiveFirstOpenReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityArchiveFirstOpenReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityArchiveFirstOpenResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArchiveFirstOpenRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSActivityArchiveFirstOpenResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSActivityArchiveAudioFinishReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArchiveAudioFinishReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_line = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_line ~= 0 then tb.task_line = __task_line end
    local __sequence_order = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __sequence_order ~= 0 then tb.sequence_order = __sequence_order end
    return tb
end

function pb.pb_CSActivityArchiveAudioFinishReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_line) then    encoder:addu64(2, tb.task_line)    end
    if(tb.sequence_order) then    encoder:addi32(3, tb.sequence_order)    end
end

function pb.pb_CSActivityArchiveAudioFinishResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArchiveAudioFinishRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSActivityArchiveAudioFinishResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSActivityReceiveSequenceAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReceiveSequenceAwardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_line = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_line ~= 0 then tb.task_line = __task_line end
    local __sequence_order = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __sequence_order ~= 0 then tb.sequence_order = __sequence_order end
    return tb
end

function pb.pb_CSActivityReceiveSequenceAwardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_line) then    encoder:addu64(2, tb.task_line)    end
    if(tb.sequence_order) then    encoder:addi32(3, tb.sequence_order)    end
end

function pb.pb_CSActivityReceiveSequenceAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityReceiveSequenceAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.activity_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityReceiveSequenceAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_infos) then
        for i=1,#(tb.activity_infos) do
            pb.pb_ActivityInfoEncode(tb.activity_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_CSActivityGetCurrencyReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetCurrencyReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityGetCurrencyReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityGetCurrencyResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetCurrencyRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __currency_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __currency_num = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __currency_num ~= 0 then tb.currency_num = __currency_num end
    return tb
end

function pb.pb_CSActivityGetCurrencyResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.currency_id) then    encoder:addu64(2, tb.currency_id)    end
    if(tb.currency_num) then    encoder:addu64(3, tb.currency_num)    end
end

function pb.pb_CSActivityGetAcceptedActivityReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetAcceptedActivityReq) or {} 
    return tb
end

function pb.pb_CSActivityGetAcceptedActivityReqEncode(tb, encoder)
end

function pb.pb_CSActivityGetAcceptedActivityResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetAcceptedActivityRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.star_fire_objectives = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.star_fire_objectives[k] = pb.pb_NumeralActivityObjectiveDecode(v)
    end
    tb.quest_objectives = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.quest_objectives[k] = pb.pb_NumeralActivityObjectiveDecode(v)
    end
    return tb
end

function pb.pb_CSActivityGetAcceptedActivityResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.star_fire_objectives) then
        for i=1,#(tb.star_fire_objectives) do
            pb.pb_NumeralActivityObjectiveEncode(tb.star_fire_objectives[i], encoder:addsubmsg(2))
        end
    end
    if(tb.quest_objectives) then
        for i=1,#(tb.quest_objectives) do
            pb.pb_NumeralActivityObjectiveEncode(tb.quest_objectives[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSActivityRelinkReceiveMSAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRelinkReceiveMSAwardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityRelinkReceiveMSAwardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityRelinkReceiveMSAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRelinkReceiveMSAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivityRelinkReceiveMSAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivityRelinkExchangeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRelinkExchangeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityRelinkExchangeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityRelinkExchangeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityRelinkExchangeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.activity_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivityRelinkExchangeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_infos) then
        for i=1,#(tb.activity_infos) do
            pb.pb_ActivityInfoEncode(tb.activity_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivityGetBannersReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetBannersReq) or {} 
    return tb
end

function pb.pb_CSActivityGetBannersReqEncode(tb, encoder)
end

function pb.pb_CSActivityGetBannersResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityGetBannersRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.banners = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.banners[k] = pb.pb_BannerDecode(v)
    end
    return tb
end

function pb.pb_CSActivityGetBannersResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.banners) then
        for i=1,#(tb.banners) do
            pb.pb_BannerEncode(tb.banners[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_BannerDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Banner) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __diy_string1 = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __diy_string1 ~= "" then tb.diy_string1 = __diy_string1 end
    local __diy_string2 = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __diy_string2 ~= "" then tb.diy_string2 = __diy_string2 end
    local __appear_time = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __appear_time ~= 0 then tb.appear_time = __appear_time end
    local __unlock_time = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    local __disappear_time = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __disappear_time ~= 0 then tb.disappear_time = __disappear_time end
    local __position = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __position ~= 0 then tb.position = __position end
    local __weight = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __weight ~= 0 then tb.weight = __weight end
    local __mode_tag = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __mode_tag ~= 0 then tb.mode_tag = __mode_tag end
    local __mode_visibility = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __mode_visibility ~= 0 then tb.mode_visibility = __mode_visibility end
    local __is_new = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __is_new ~= false then tb.is_new = __is_new end
    local __is_hot = decoder:getbool(21)
    if not PB_USE_DEFAULT_TABLE or __is_hot ~= false then tb.is_hot = __is_hot end
    local __jump_id = decoder:getu64(22)
    if not PB_USE_DEFAULT_TABLE or __jump_id ~= 0 then tb.jump_id = __jump_id end
    local __pic_resource = decoder:getstr(34)
    if not PB_USE_DEFAULT_TABLE or __pic_resource ~= "" then tb.pic_resource = __pic_resource end
    local __is_subtitle_show_time = decoder:getbool(40)
    if not PB_USE_DEFAULT_TABLE or __is_subtitle_show_time ~= false then tb.is_subtitle_show_time = __is_subtitle_show_time end
    local __connected_event = decoder:getu64(41)
    if not PB_USE_DEFAULT_TABLE or __connected_event ~= 0 then tb.connected_event = __connected_event end
    local __banner_type = decoder:getu32(42)
    if not PB_USE_DEFAULT_TABLE or __banner_type ~= 0 then tb.banner_type = __banner_type end
    return tb
end

function pb.pb_BannerEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.diy_string1) then    encoder:addstr(2, tb.diy_string1)    end
    if(tb.diy_string2) then    encoder:addstr(3, tb.diy_string2)    end
    if(tb.appear_time) then    encoder:addu64(4, tb.appear_time)    end
    if(tb.unlock_time) then    encoder:addu64(5, tb.unlock_time)    end
    if(tb.disappear_time) then    encoder:addu64(6, tb.disappear_time)    end
    if(tb.position) then    encoder:addu32(7, tb.position)    end
    if(tb.weight) then    encoder:addi32(8, tb.weight)    end
    if(tb.mode_tag) then    encoder:addi32(9, tb.mode_tag)    end
    if(tb.mode_visibility) then    encoder:addi32(10, tb.mode_visibility)    end
    if(tb.is_new) then    encoder:addbool(20, tb.is_new)    end
    if(tb.is_hot) then    encoder:addbool(21, tb.is_hot)    end
    if(tb.jump_id) then    encoder:addu64(22, tb.jump_id)    end
    if(tb.pic_resource) then    encoder:addstr(34, tb.pic_resource)    end
    if(tb.is_subtitle_show_time) then    encoder:addbool(40, tb.is_subtitle_show_time)    end
    if(tb.connected_event) then    encoder:addu64(41, tb.connected_event)    end
    if(tb.banner_type) then    encoder:addu32(42, tb.banner_type)    end
end

function pb.pb_CSActivityOneClickRewardClaimReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityOneClickRewardClaimReq) or {} 
    local __actv_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actv_id ~= 0 then tb.actv_id = __actv_id end
    local __task_line = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_line ~= 0 then tb.task_line = __task_line end
    return tb
end

function pb.pb_CSActivityOneClickRewardClaimReqEncode(tb, encoder)
    if(tb.actv_id) then    encoder:addu64(1, tb.actv_id)    end
    if(tb.task_line) then    encoder:addu64(2, tb.task_line)    end
end

function pb.pb_CSActivityOneClickRewardClaimResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityOneClickRewardClaimRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    tb.actv_infos = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.actv_infos[k] = pb.pb_ActivityInfoDecode(v)
    end
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityOneClickRewardClaimResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
    if(tb.actv_infos) then
        for i=1,#(tb.actv_infos) do
            pb.pb_ActivityInfoEncode(tb.actv_infos[i], encoder:addsubmsg(3))
        end
    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_ActThemeEavesdroppingCaseDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeEavesdroppingCase) or {} 
    local __case_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __case_id ~= 0 then tb.case_id = __case_id end
    local __unlock_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    local __required_points = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __required_points ~= 0 then tb.required_points = __required_points end
    local __cur_points = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __cur_points ~= 0 then tb.cur_points = __cur_points end
    local __is_answered = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_answered ~= false then tb.is_answered = __is_answered end
    local __doctor_score = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __doctor_score ~= 0 then tb.doctor_score = __doctor_score end
    local __zoya_score = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __zoya_score ~= 0 then tb.zoya_score = __zoya_score end
    local __received = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_ActThemeEavesdroppingCaseEncode(tb, encoder)
    if(tb.case_id) then    encoder:addu64(1, tb.case_id)    end
    if(tb.unlock_time) then    encoder:addi64(2, tb.unlock_time)    end
    if(tb.required_points) then    encoder:addi32(3, tb.required_points)    end
    if(tb.cur_points) then    encoder:addi32(4, tb.cur_points)    end
    if(tb.is_answered) then    encoder:addbool(5, tb.is_answered)    end
    if(tb.doctor_score) then    encoder:addu32(6, tb.doctor_score)    end
    if(tb.zoya_score) then    encoder:addu32(7, tb.zoya_score)    end
    if(tb.received) then    encoder:addbool(8, tb.received)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_PropInfoEncode(tb.props[i], encoder:addsubmsg(9))
        end
    end
end

function pb.pb_ActThemeEavesdroppingSpecialAnswerAwardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeEavesdroppingSpecialAnswerAward) or {} 
    local __special_answer = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __special_answer ~= 0 then tb.special_answer = __special_answer end
    tb.doctor_prop = pb.pb_PropInfoDecode(decoder:getsubmsg(2))
    tb.zoya_prop = pb.pb_PropInfoDecode(decoder:getsubmsg(3))
    local __received_special_answer = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __received_special_answer ~= false then tb.received_special_answer = __received_special_answer end
    return tb
end

function pb.pb_ActThemeEavesdroppingSpecialAnswerAwardEncode(tb, encoder)
    if(tb.special_answer) then    encoder:addu32(1, tb.special_answer)    end
    if(tb.doctor_prop) then    pb.pb_PropInfoEncode(tb.doctor_prop, encoder:addsubmsg(2))    end
    if(tb.zoya_prop) then    pb.pb_PropInfoEncode(tb.zoya_prop, encoder:addsubmsg(3))    end
    if(tb.received_special_answer) then    encoder:addbool(4, tb.received_special_answer)    end
end

function pb.pb_ActThemeEavesdroppingInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeEavesdroppingInfo) or {} 
    tb.cases = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.cases[k] = pb.pb_ActThemeEavesdroppingCaseDecode(v)
    end
    tb.special_answer_info = pb.pb_ActThemeEavesdroppingSpecialAnswerAwardDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_ActThemeEavesdroppingInfoEncode(tb, encoder)
    if(tb.cases) then
        for i=1,#(tb.cases) do
            pb.pb_ActThemeEavesdroppingCaseEncode(tb.cases[i], encoder:addsubmsg(1))
        end
    end
    if(tb.special_answer_info) then    pb.pb_ActThemeEavesdroppingSpecialAnswerAwardEncode(tb.special_answer_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityEavesdroppingAnswerReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityEavesdroppingAnswerReq) or {} 
    local __act_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __act_id ~= 0 then tb.act_id = __act_id end
    local __case_num = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __case_num ~= 0 then tb.case_num = __case_num end
    local __answer = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __answer ~= 0 then tb.answer = __answer end
    return tb
end

function pb.pb_CSActivityEavesdroppingAnswerReqEncode(tb, encoder)
    if(tb.act_id) then    encoder:addu64(1, tb.act_id)    end
    if(tb.case_num) then    encoder:addu64(2, tb.case_num)    end
    if(tb.answer) then    encoder:addu32(3, tb.answer)    end
end

function pb.pb_CSActivityEavesdroppingAnswerResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityEavesdroppingAnswerRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityEavesdroppingAnswerResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityEavesdroppingReceiveCaseAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityEavesdroppingReceiveCaseAwardReq) or {} 
    local __act_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __act_id ~= 0 then tb.act_id = __act_id end
    local __case_num = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __case_num ~= 0 then tb.case_num = __case_num end
    return tb
end

function pb.pb_CSActivityEavesdroppingReceiveCaseAwardReqEncode(tb, encoder)
    if(tb.act_id) then    encoder:addu64(1, tb.act_id)    end
    if(tb.case_num) then    encoder:addu64(2, tb.case_num)    end
end

function pb.pb_CSActivityEavesdroppingReceiveCaseAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityEavesdroppingReceiveCaseAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivityEavesdroppingReceiveCaseAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivityEavesdroppingReceiveSpecialAnswerAwardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityEavesdroppingReceiveSpecialAnswerAwardReq) or {} 
    local __act_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __act_id ~= 0 then tb.act_id = __act_id end
    return tb
end

function pb.pb_CSActivityEavesdroppingReceiveSpecialAnswerAwardReqEncode(tb, encoder)
    if(tb.act_id) then    encoder:addu64(1, tb.act_id)    end
end

function pb.pb_CSActivityEavesdroppingReceiveSpecialAnswerAwardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityEavesdroppingReceiveSpecialAnswerAwardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivityEavesdroppingReceiveSpecialAnswerAwardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
end

function pb.pb_ActThemeStarInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeStarInfo) or {} 
    tb.obtained_core_tech = decoder:getu64ary(1)
    local __decryption_times = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __decryption_times ~= 0 then tb.decryption_times = __decryption_times end
    return tb
end

function pb.pb_ActThemeStarInfoEncode(tb, encoder)
    if(tb.obtained_core_tech) then    encoder:addu64(1, tb.obtained_core_tech)    end
    if(tb.decryption_times) then    encoder:addi32(2, tb.decryption_times)    end
end

function pb.pb_CSActivityStarDecryptReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityStarDecryptReq) or {} 
    local __act_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __act_id ~= 0 then tb.act_id = __act_id end
    return tb
end

function pb.pb_CSActivityStarDecryptReqEncode(tb, encoder)
    if(tb.act_id) then    encoder:addu64(1, tb.act_id)    end
end

function pb.pb_CSActivityStarDecryptResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityStarDecryptRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __decryption_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __decryption_id ~= 0 then tb.decryption_id = __decryption_id end
    tb.actv_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivityStarDecryptResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.decryption_id) then    encoder:addu64(2, tb.decryption_id)    end
    if(tb.actv_info) then    pb.pb_ActivityInfoEncode(tb.actv_info, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivityAnswerReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAnswerReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __goal_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __goal_id ~= 0 then tb.goal_id = __goal_id end
    local __answer = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __answer ~= 0 then tb.answer = __answer end
    return tb
end

function pb.pb_CSActivityAnswerReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
    if(tb.goal_id) then    encoder:addu64(3, tb.goal_id)    end
    if(tb.answer) then    encoder:addu32(4, tb.answer)    end
end

function pb.pb_CSActivityAnswerResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAnswerRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityAnswerResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityTriggerReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTriggerReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityTriggerReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityTriggerResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityTriggerRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityTriggerResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityUnlockExchangeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUnlockExchangeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __exchange_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    return tb
end

function pb.pb_CSActivityUnlockExchangeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.exchange_id) then    encoder:addu64(2, tb.exchange_id)    end
end

function pb.pb_CSActivityUnlockExchangeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityUnlockExchangeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityUnlockExchangeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_CSActivityMakeDrinkReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityMakeDrinkReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __base_drink_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __base_drink_id ~= 0 then tb.base_drink_id = __base_drink_id end
    tb.material_ids = decoder:getu64ary(3)
    local __move_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __move_time ~= 0 then tb.move_time = __move_time end
    return tb
end

function pb.pb_CSActivityMakeDrinkReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.base_drink_id) then    encoder:addu64(2, tb.base_drink_id)    end
    if(tb.material_ids) then    encoder:addu64(3, tb.material_ids)    end
    if(tb.move_time) then    encoder:addi64(4, tb.move_time)    end
end

function pb.pb_CSActivityMakeDrinkResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityMakeDrinkRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityMakeDrinkResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityExchangePropsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangePropsReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    return tb
end

function pb.pb_CSActivityExchangePropsReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
end

function pb.pb_CSActivityExchangePropsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityExchangePropsRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityExchangePropsResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_ActThemeSheOneCardKeyValueDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSheOneCardKeyValue) or {} 
    local __id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_ActThemeSheOneCardKeyValueEncode(tb, encoder)
    if(tb.id) then    encoder:addi32(1, tb.id)    end
    if(tb.num) then    encoder:addi32(2, tb.num)    end
end

function pb.pb_ActThemeSheOneCardInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSheOneCardInfo) or {} 
    local __sol_pack_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __sol_pack_id ~= 0 then tb.sol_pack_id = __sol_pack_id end
    local __mp_pack_num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __mp_pack_num ~= 0 then tb.mp_pack_num = __mp_pack_num end
    local __scissor_num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __scissor_num ~= 0 then tb.scissor_num = __scissor_num end
    local __send_low_card_per_day = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __send_low_card_per_day ~= 0 then tb.send_low_card_per_day = __send_low_card_per_day end
    local __send_high_card_per_day = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __send_high_card_per_day ~= 0 then tb.send_high_card_per_day = __send_high_card_per_day end
    local __send_high_card_num = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __send_high_card_num ~= 0 then tb.send_high_card_num = __send_high_card_num end
    local __send_low_card_num = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __send_low_card_num ~= 0 then tb.send_low_card_num = __send_low_card_num end
    local __next_send_num_refresh_time = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __next_send_num_refresh_time ~= 0 then tb.next_send_num_refresh_time = __next_send_num_refresh_time end
    local __is_new_card_received = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_new_card_received ~= false then tb.is_new_card_received = __is_new_card_received end
    local __mp_pack_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __mp_pack_id ~= 0 then tb.mp_pack_id = __mp_pack_id end
    local __scissor_id = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __scissor_id ~= 0 then tb.scissor_id = __scissor_id end
    local __next_prob = decoder:getdouble(13)
    if not PB_USE_DEFAULT_TABLE or __next_prob ~= 0 then tb.next_prob = __next_prob end
    tb.owned_card_list = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.owned_card_list[k] = pb.pb_ActThemeSheOneCardKeyValueDecode(v)
    end
    return tb
end

function pb.pb_ActThemeSheOneCardInfoEncode(tb, encoder)
    if(tb.sol_pack_id) then    encoder:addu64(1, tb.sol_pack_id)    end
    if(tb.mp_pack_num) then    encoder:addi32(2, tb.mp_pack_num)    end
    if(tb.scissor_num) then    encoder:addi32(3, tb.scissor_num)    end
    if(tb.send_low_card_per_day) then    encoder:addi32(5, tb.send_low_card_per_day)    end
    if(tb.send_high_card_per_day) then    encoder:addi32(6, tb.send_high_card_per_day)    end
    if(tb.send_high_card_num) then    encoder:addi32(7, tb.send_high_card_num)    end
    if(tb.send_low_card_num) then    encoder:addi32(8, tb.send_low_card_num)    end
    if(tb.next_send_num_refresh_time) then    encoder:addu64(9, tb.next_send_num_refresh_time)    end
    if(tb.is_new_card_received) then    encoder:addbool(10, tb.is_new_card_received)    end
    if(tb.mp_pack_id) then    encoder:addu64(11, tb.mp_pack_id)    end
    if(tb.scissor_id) then    encoder:addu64(12, tb.scissor_id)    end
    if(tb.next_prob) then    encoder:adddouble(13, tb.next_prob)    end
    if(tb.owned_card_list) then
        for i=1,#(tb.owned_card_list) do
            pb.pb_ActThemeSheOneCardKeyValueEncode(tb.owned_card_list[i], encoder:addsubmsg(14))
        end
    end
end

function pb.pb_CSActivitySOCNewCardNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCNewCardNtf) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivitySOCNewCardNtfEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivitySOCNewMPPackNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCNewMPPackNtf) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivitySOCNewMPPackNtfEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_ActThemeSheOneCardShopBuyRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSheOneCardShopBuyRecord) or {} 
    local __buy_count = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __buy_count ~= 0 then tb.buy_count = __buy_count end
    local __last_refresh_timestamp = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __last_refresh_timestamp ~= 0 then tb.last_refresh_timestamp = __last_refresh_timestamp end
    local __next_refresh_timestamp = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __next_refresh_timestamp ~= 0 then tb.next_refresh_timestamp = __next_refresh_timestamp end
    return tb
end

function pb.pb_ActThemeSheOneCardShopBuyRecordEncode(tb, encoder)
    if(tb.buy_count) then    encoder:addi32(1, tb.buy_count)    end
    if(tb.last_refresh_timestamp) then    encoder:addu64(2, tb.last_refresh_timestamp)    end
    if(tb.next_refresh_timestamp) then    encoder:addu64(3, tb.next_refresh_timestamp)    end
end

function pb.pb_ActThemeSheOneCardSendRecvFlowDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSheOneCardSendRecvFlow) or {} 
    local __op_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __op_type ~= 0 then tb.op_type = __op_type end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __timestamp = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    local __card_id = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __card_id ~= 0 then tb.card_id = __card_id end
    return tb
end

function pb.pb_ActThemeSheOneCardSendRecvFlowEncode(tb, encoder)
    if(tb.op_type) then    encoder:addi32(1, tb.op_type)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.timestamp) then    encoder:addu64(3, tb.timestamp)    end
    if(tb.card_id) then    encoder:addi32(4, tb.card_id)    end
end

function pb.pb_ActThemeSheOneCardRecvCardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSheOneCardRecvCard) or {} 
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __card_id = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __card_id ~= 0 then tb.card_id = __card_id end
    return tb
end

function pb.pb_ActThemeSheOneCardRecvCardEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.card_id) then    encoder:addi32(4, tb.card_id)    end
end

function pb.pb_ActThemeSheOneCardMPFlowDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSheOneCardMPFlow) or {} 
    local __pass_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __pass_type ~= 0 then tb.pass_type = __pass_type end
    local __timestamp = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    local __prob = decoder:getdouble(3)
    if not PB_USE_DEFAULT_TABLE or __prob ~= 0 then tb.prob = __prob end
    local __add_prob = decoder:getdouble(4)
    if not PB_USE_DEFAULT_TABLE or __add_prob ~= 0 then tb.add_prob = __add_prob end
    return tb
end

function pb.pb_ActThemeSheOneCardMPFlowEncode(tb, encoder)
    if(tb.pass_type) then    encoder:addi32(1, tb.pass_type)    end
    if(tb.timestamp) then    encoder:addu64(2, tb.timestamp)    end
    if(tb.prob) then    encoder:adddouble(3, tb.prob)    end
    if(tb.add_prob) then    encoder:adddouble(4, tb.add_prob)    end
end

function pb.pb_ActThemeSOCShopCostDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSOCShopCost) or {} 
    local __id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_ActThemeSOCShopCostEncode(tb, encoder)
    if(tb.id) then    encoder:addi32(1, tb.id)    end
    if(tb.num) then    encoder:addi32(2, tb.num)    end
end

function pb.pb_ActThemeSOCShopItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ActThemeSOCShopItem) or {} 
    local __id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __tag = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __tag ~= 0 then tb.tag = __tag end
    local __buy_limit = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __buy_limit ~= 0 then tb.buy_limit = __buy_limit end
    local __buy_limit_recover_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __buy_limit_recover_time ~= 0 then tb.buy_limit_recover_time = __buy_limit_recover_time end
    local __reward_item = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __reward_item ~= 0 then tb.reward_item = __reward_item end
    local __reward_num = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __reward_num ~= 0 then tb.reward_num = __reward_num end
    local __cost_desc = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __cost_desc ~= "" then tb.cost_desc = __cost_desc end
    tb.costs = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.costs[k] = pb.pb_ActThemeSOCShopCostDecode(v)
    end
    local __buy_count = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __buy_count ~= 0 then tb.buy_count = __buy_count end
    local __next_refresh_timestamp = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __next_refresh_timestamp ~= 0 then tb.next_refresh_timestamp = __next_refresh_timestamp end
    return tb
end

function pb.pb_ActThemeSOCShopItemEncode(tb, encoder)
    if(tb.id) then    encoder:addi32(1, tb.id)    end
    if(tb.tag) then    encoder:addi32(2, tb.tag)    end
    if(tb.buy_limit) then    encoder:addi32(3, tb.buy_limit)    end
    if(tb.buy_limit_recover_time) then    encoder:addi32(4, tb.buy_limit_recover_time)    end
    if(tb.reward_item) then    encoder:addu64(5, tb.reward_item)    end
    if(tb.reward_num) then    encoder:addi32(6, tb.reward_num)    end
    if(tb.cost_desc) then    encoder:addstr(7, tb.cost_desc)    end
    if(tb.costs) then
        for i=1,#(tb.costs) do
            pb.pb_ActThemeSOCShopCostEncode(tb.costs[i], encoder:addsubmsg(8))
        end
    end
    if(tb.buy_count) then    encoder:addi32(9, tb.buy_count)    end
    if(tb.next_refresh_timestamp) then    encoder:addu64(10, tb.next_refresh_timestamp)    end
end

function pb.pb_CSActivitySOCDrawReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCDrawReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __draw_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __draw_type ~= 0 then tb.draw_type = __draw_type end
    return tb
end

function pb.pb_CSActivitySOCDrawReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.draw_type) then    encoder:addu32(2, tb.draw_type)    end
end

function pb.pb_CSActivitySOCDrawResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCDrawRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.cards = decoder:geti32ary(2)
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivitySOCDrawResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.cards) then    encoder:addi32(2, tb.cards)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivitySOCSendRecvFlowsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCSendRecvFlowsReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivitySOCSendRecvFlowsReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivitySOCSendRecvFlowsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCSendRecvFlowsRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.send_recv_flows = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.send_recv_flows[k] = pb.pb_ActThemeSheOneCardSendRecvFlowDecode(v)
    end
    return tb
end

function pb.pb_CSActivitySOCSendRecvFlowsResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.send_recv_flows) then
        for i=1,#(tb.send_recv_flows) do
            pb.pb_ActThemeSheOneCardSendRecvFlowEncode(tb.send_recv_flows[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSActivitySOCGetRecvCardsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCGetRecvCardsReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivitySOCGetRecvCardsReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivitySOCGetRecvCardsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCGetRecvCardsRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.recv_cards = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.recv_cards[k] = pb.pb_ActThemeSheOneCardRecvCardDecode(v)
    end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivitySOCGetRecvCardsResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.recv_cards) then
        for i=1,#(tb.recv_cards) do
            pb.pb_ActThemeSheOneCardRecvCardEncode(tb.recv_cards[i], encoder:addsubmsg(11))
        end
    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivitySOCMPFlowsReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCMPFlowsReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivitySOCMPFlowsReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivitySOCMPFlowsResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCMPFlowsRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.mp_flows = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.mp_flows[k] = pb.pb_ActThemeSheOneCardMPFlowDecode(v)
    end
    return tb
end

function pb.pb_CSActivitySOCMPFlowsResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.mp_flows) then
        for i=1,#(tb.mp_flows) do
            pb.pb_ActThemeSheOneCardMPFlowEncode(tb.mp_flows[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSActivitySOCSendCardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCSendCardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __target_player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __target_player_id ~= 0 then tb.target_player_id = __target_player_id end
    local __card_id = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __card_id ~= 0 then tb.card_id = __card_id end
    return tb
end

function pb.pb_CSActivitySOCSendCardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.target_player_id) then    encoder:addu64(2, tb.target_player_id)    end
    if(tb.card_id) then    encoder:addi32(3, tb.card_id)    end
end

function pb.pb_CSActivitySOCSendCardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCSendCardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivitySOCSendCardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivitySOCCompositeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCCompositeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __composite_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __composite_type ~= 0 then tb.composite_type = __composite_type end
    return tb
end

function pb.pb_CSActivitySOCCompositeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.composite_type) then    encoder:addu32(2, tb.composite_type)    end
end

function pb.pb_CSActivitySOCCompositeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCCompositeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __card_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __card_id ~= 0 then tb.card_id = __card_id end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivitySOCCompositeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.card_id) then    encoder:addi32(2, tb.card_id)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivitySOCExchangeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCExchangeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __shop_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __shop_id ~= 0 then tb.shop_id = __shop_id end
    return tb
end

function pb.pb_CSActivitySOCExchangeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.shop_id) then    encoder:addi32(2, tb.shop_id)    end
end

function pb.pb_CSActivitySOCExchangeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCExchangeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(2))
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSActivitySOCExchangeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(2))    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(3))    end
end

function pb.pb_CSActivitySOCGetShopReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCGetShopReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivitySOCGetShopReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivitySOCGetShopResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySOCGetShopRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.shop_items = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.shop_items[k] = pb.pb_ActThemeSOCShopItemDecode(v)
    end
    return tb
end

function pb.pb_CSActivitySOCGetShopResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.shop_items) then
        for i=1,#(tb.shop_items) do
            pb.pb_ActThemeSOCShopItemEncode(tb.shop_items[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_SBCInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SBCInfo) or {} 
    local __current_adjust_time = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __current_adjust_time ~= 0 then tb.current_adjust_time = __current_adjust_time end
    tb.awards = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.awards[k] = pb.pb_SBCAwardDecode(v)
    end
    tb.award_adjust_time = decoder:geti32ary(3)
    tb.news_id = decoder:getu64ary(4)
    tb.exchange_props = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.exchange_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __exchange_doc = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __exchange_doc ~= "" then tb.exchange_doc = __exchange_doc end
    local __total_adjust_time = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __total_adjust_time ~= 0 then tb.total_adjust_time = __total_adjust_time end
    tb.chosen_reward_ids = decoder:getu64ary(8)
    tb.shop_exchange_ids = decoder:getu64ary(9)
    local __version = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __version ~= "" then tb.version = __version end
    local __back_image_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __back_image_id ~= 0 then tb.back_image_id = __back_image_id end
    return tb
end

function pb.pb_SBCInfoEncode(tb, encoder)
    if(tb.current_adjust_time) then    encoder:addi32(1, tb.current_adjust_time)    end
    if(tb.awards) then
        for i=1,#(tb.awards) do
            pb.pb_SBCAwardEncode(tb.awards[i], encoder:addsubmsg(2))
        end
    end
    if(tb.award_adjust_time) then    encoder:addi32(3, tb.award_adjust_time)    end
    if(tb.news_id) then    encoder:addu64(4, tb.news_id)    end
    if(tb.exchange_props) then
        for i=1,#(tb.exchange_props) do
            pb.pb_PropInfoEncode(tb.exchange_props[i], encoder:addsubmsg(5))
        end
    end
    if(tb.exchange_doc) then    encoder:addstr(6, tb.exchange_doc)    end
    if(tb.total_adjust_time) then    encoder:addi32(7, tb.total_adjust_time)    end
    if(tb.chosen_reward_ids) then    encoder:addu64(8, tb.chosen_reward_ids)    end
    if(tb.shop_exchange_ids) then    encoder:addu64(9, tb.shop_exchange_ids)    end
    if(tb.version) then    encoder:addstr(10, tb.version)    end
    if(tb.back_image_id) then    encoder:addu64(11, tb.back_image_id)    end
end

function pb.pb_SBCAwardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SBCAward) or {} 
    local __reward_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __reward_id ~= 0 then tb.reward_id = __reward_id end
    tb.selected_props = pb.pb_PropInfoDecode(decoder:getsubmsg(2))
    local __selected = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __selected ~= false then tb.selected = __selected end
    return tb
end

function pb.pb_SBCAwardEncode(tb, encoder)
    if(tb.reward_id) then    encoder:addu64(1, tb.reward_id)    end
    if(tb.selected_props) then    pb.pb_PropInfoEncode(tb.selected_props, encoder:addsubmsg(2))    end
    if(tb.selected) then    encoder:addbool(3, tb.selected)    end
end

function pb.pb_CSActivitySBCAdjustReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySBCAdjustReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __news_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __news_id ~= 0 then tb.news_id = __news_id end
    local __is_auto = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_auto ~= false then tb.is_auto = __is_auto end
    return tb
end

function pb.pb_CSActivitySBCAdjustReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.news_id) then    encoder:addu64(2, tb.news_id)    end
    if(tb.is_auto) then    encoder:addbool(3, tb.is_auto)    end
end

function pb.pb_CSActivitySBCAdjustResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySBCAdjustRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivitySBCAdjustResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivitySBCChangeRewardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySBCChangeRewardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __reward_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __reward_id ~= 0 then tb.reward_id = __reward_id end
    local __change_type = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __change_type ~= 0 then tb.change_type = __change_type end
    tb.reward_ids = decoder:getu64ary(4)
    return tb
end

function pb.pb_CSActivitySBCChangeRewardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.reward_id) then    encoder:addu64(2, tb.reward_id)    end
    if(tb.change_type) then    encoder:addu32(3, tb.change_type)    end
    if(tb.reward_ids) then    encoder:addu64(4, tb.reward_ids)    end
end

function pb.pb_CSActivitySBCChangeRewardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivitySBCChangeRewardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivitySBCChangeRewardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_MPCommanderInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPCommanderInfo) or {} 
    local __score = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __rank_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __rank_type ~= 0 then tb.rank_type = __rank_type end
    return tb
end

function pb.pb_MPCommanderInfoEncode(tb, encoder)
    if(tb.score) then    encoder:addi64(1, tb.score)    end
    if(tb.rank_type) then    encoder:addu32(2, tb.rank_type)    end
end

function pb.pb_CSActivityArknightsRecruitReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsRecruitReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __recruit_ad = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __recruit_ad ~= 0 then tb.recruit_ad = __recruit_ad end
    return tb
end

function pb.pb_CSActivityArknightsRecruitReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
    if(tb.recruit_ad) then    encoder:addu64(3, tb.recruit_ad)    end
end

function pb.pb_CSActivityArknightsRecruitResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsRecruitRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityArknightsRecruitResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSActivityArknightsRefreshAdReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsRefreshAdReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    return tb
end

function pb.pb_CSActivityArknightsRefreshAdReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
end

function pb.pb_CSActivityArknightsRefreshAdResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsRefreshAdRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    return tb
end

function pb.pb_CSActivityArknightsRefreshAdResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSActivityArknightsChooseHeroReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsChooseHeroReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __hero_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    return tb
end

function pb.pb_CSActivityArknightsChooseHeroReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
    if(tb.hero_id) then    encoder:addu64(3, tb.hero_id)    end
end

function pb.pb_CSActivityArknightsChooseHeroResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsChooseHeroRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityArknightsChooseHeroResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityArknightsReceiveCollectRewardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsReceiveCollectRewardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __isAll = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __isAll ~= false then tb.isAll = __isAll end
    local __collect_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __collect_id ~= 0 then tb.collect_id = __collect_id end
    return tb
end

function pb.pb_CSActivityArknightsReceiveCollectRewardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.isAll) then    encoder:addbool(2, tb.isAll)    end
    if(tb.collect_id) then    encoder:addu64(3, tb.collect_id)    end
end

function pb.pb_CSActivityArknightsReceiveCollectRewardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsReceiveCollectRewardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_CSActivityArknightsReceiveCollectRewardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(100))    end
end

function pb.pb_CSActivityArknightsUpdateMPMoneyNoticeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsUpdateMPMoneyNoticeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityArknightsUpdateMPMoneyNoticeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityArknightsUpdateMPMoneyNoticeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsUpdateMPMoneyNoticeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __mp_money_update_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __mp_money_update_time ~= 0 then tb.mp_money_update_time = __mp_money_update_time end
    return tb
end

function pb.pb_CSActivityArknightsUpdateMPMoneyNoticeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.mp_money_update_time) then    encoder:addi64(2, tb.mp_money_update_time)    end
end

function pb.pb_ArknightsGameInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsGameInfo) or {} 
    local __san_num = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __san_num ~= 0 then tb.san_num = __san_num end
    tb.san_records = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.san_records[k] = pb.pb_ArknightsGameSanRecordDecode(v)
    end
    tb.match_records = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.match_records[k] = pb.pb_ArknightsGameMatchRecordDecode(v)
    end
    tb.npc_records = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.npc_records[k] = pb.pb_ArknightsGameNpcRecordDecode(v)
    end
    tb.best_match_record = pb.pb_ArknightsGameMatchRecordDecode(decoder:getsubmsg(5))
    tb.rewards = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.rewards[k] = pb.pb_ArknightsGameRewardDecode(v)
    end
    local __last_san_update_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __last_san_update_time ~= 0 then tb.last_san_update_time = __last_san_update_time end
    local __gain_money_num = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __gain_money_num ~= 0 then tb.gain_money_num = __gain_money_num end
    local __meet_reward_npc = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __meet_reward_npc ~= false then tb.meet_reward_npc = __meet_reward_npc end
    local __game_state = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __game_state ~= 0 then tb.game_state = __game_state end
    return tb
end

function pb.pb_ArknightsGameInfoEncode(tb, encoder)
    if(tb.san_num) then    encoder:addi64(1, tb.san_num)    end
    if(tb.san_records) then
        for i=1,#(tb.san_records) do
            pb.pb_ArknightsGameSanRecordEncode(tb.san_records[i], encoder:addsubmsg(2))
        end
    end
    if(tb.match_records) then
        for i=1,#(tb.match_records) do
            pb.pb_ArknightsGameMatchRecordEncode(tb.match_records[i], encoder:addsubmsg(3))
        end
    end
    if(tb.npc_records) then
        for i=1,#(tb.npc_records) do
            pb.pb_ArknightsGameNpcRecordEncode(tb.npc_records[i], encoder:addsubmsg(4))
        end
    end
    if(tb.best_match_record) then    pb.pb_ArknightsGameMatchRecordEncode(tb.best_match_record, encoder:addsubmsg(5))    end
    if(tb.rewards) then
        for i=1,#(tb.rewards) do
            pb.pb_ArknightsGameRewardEncode(tb.rewards[i], encoder:addsubmsg(6))
        end
    end
    if(tb.last_san_update_time) then    encoder:addi64(7, tb.last_san_update_time)    end
    if(tb.gain_money_num) then    encoder:addi64(8, tb.gain_money_num)    end
    if(tb.meet_reward_npc) then    encoder:addbool(9, tb.meet_reward_npc)    end
    if(tb.game_state) then    encoder:addi32(10, tb.game_state)    end
end

function pb.pb_ArknightsGameRewardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsGameReward) or {} 
    local __cycle = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __cycle ~= 0 then tb.cycle = __cycle end
    local __received = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    local __image = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(5))
    local __reward_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __reward_id ~= 0 then tb.reward_id = __reward_id end
    local __type = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __need_money_num = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __need_money_num ~= 0 then tb.need_money_num = __need_money_num end
    local __desc = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    return tb
end

function pb.pb_ArknightsGameRewardEncode(tb, encoder)
    if(tb.cycle) then    encoder:addi64(1, tb.cycle)    end
    if(tb.received) then    encoder:addbool(3, tb.received)    end
    if(tb.image) then    encoder:addstr(4, tb.image)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(5))    end
    if(tb.reward_id) then    encoder:addu64(6, tb.reward_id)    end
    if(tb.type) then    encoder:addu32(7, tb.type)    end
    if(tb.need_money_num) then    encoder:addi64(8, tb.need_money_num)    end
    if(tb.desc) then    encoder:addstr(9, tb.desc)    end
end

function pb.pb_ArknightsGameSanRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsGameSanRecord) or {} 
    local __time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __san_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __san_num ~= 0 then tb.san_num = __san_num end
    local __game_mode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    return tb
end

function pb.pb_ArknightsGameSanRecordEncode(tb, encoder)
    if(tb.time) then    encoder:addi64(1, tb.time)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
    if(tb.san_num) then    encoder:addi64(3, tb.san_num)    end
    if(tb.game_mode) then    encoder:addi32(4, tb.game_mode)    end
end

function pb.pb_ArknightsGameMatchRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsGameMatchRecord) or {} 
    local __time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    local __result = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __cost_san_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __cost_san_num ~= 0 then tb.cost_san_num = __cost_san_num end
    local __gain_money_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __gain_money_num ~= 0 then tb.gain_money_num = __gain_money_num end
    local __cycle = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __cycle ~= 0 then tb.cycle = __cycle end
    local __level = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __is_auto = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_auto ~= false then tb.is_auto = __is_auto end
    local __location = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __location ~= "" then tb.location = __location end
    return tb
end

function pb.pb_ArknightsGameMatchRecordEncode(tb, encoder)
    if(tb.time) then    encoder:addi64(1, tb.time)    end
    if(tb.result) then    encoder:addu32(2, tb.result)    end
    if(tb.cost_san_num) then    encoder:addi64(3, tb.cost_san_num)    end
    if(tb.gain_money_num) then    encoder:addi64(4, tb.gain_money_num)    end
    if(tb.cycle) then    encoder:addi64(5, tb.cycle)    end
    if(tb.level) then    encoder:addi64(6, tb.level)    end
    if(tb.is_auto) then    encoder:addbool(7, tb.is_auto)    end
    if(tb.location) then    encoder:addstr(8, tb.location)    end
end

function pb.pb_ArknightsGameNpcRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsGameNpcRecord) or {} 
    local __name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __tag = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __tag ~= "" then tb.tag = __tag end
    local __intro = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __intro ~= "" then tb.intro = __intro end
    tb.descs = decoder:getstrary(4)
    local __image = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    return tb
end

function pb.pb_ArknightsGameNpcRecordEncode(tb, encoder)
    if(tb.name) then    encoder:addstr(1, tb.name)    end
    if(tb.tag) then    encoder:addstr(2, tb.tag)    end
    if(tb.intro) then    encoder:addstr(3, tb.intro)    end
    if(tb.descs) then    encoder:addstr(4, tb.descs)    end
    if(tb.image) then    encoder:addstr(5, tb.image)    end
end

function pb.pb_CSActivityArknightsGameStartReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameStartReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityArknightsGameStartReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityArknightsGameStartResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameStartRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSActivityArknightsGameStartResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSActivityArknightsGameEndReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameEndReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __cost_san_num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __cost_san_num ~= 0 then tb.cost_san_num = __cost_san_num end
    local __gain_money_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __gain_money_num ~= 0 then tb.gain_money_num = __gain_money_num end
    local __result = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __cycle = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __cycle ~= 0 then tb.cycle = __cycle end
    local __level = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __meet_reward_npc = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __meet_reward_npc ~= false then tb.meet_reward_npc = __meet_reward_npc end
    local __checksum = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __checksum ~= "" then tb.checksum = __checksum end
    local __game_state = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __game_state ~= 0 then tb.game_state = __game_state end
    return tb
end

function pb.pb_CSActivityArknightsGameEndReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.cost_san_num) then    encoder:addi64(2, tb.cost_san_num)    end
    if(tb.gain_money_num) then    encoder:addi64(3, tb.gain_money_num)    end
    if(tb.result) then    encoder:addu32(4, tb.result)    end
    if(tb.cycle) then    encoder:addi64(5, tb.cycle)    end
    if(tb.level) then    encoder:addi64(6, tb.level)    end
    if(tb.meet_reward_npc) then    encoder:addbool(7, tb.meet_reward_npc)    end
    if(tb.checksum) then    encoder:addstr(8, tb.checksum)    end
    if(tb.game_state) then    encoder:addi32(9, tb.game_state)    end
end

function pb.pb_CSActivityArknightsGameEndResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameEndRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(5))
    local __gain_money_num = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __gain_money_num ~= 0 then tb.gain_money_num = __gain_money_num end
    return tb
end

function pb.pb_CSActivityArknightsGameEndResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(5))    end
    if(tb.gain_money_num) then    encoder:addi64(6, tb.gain_money_num)    end
end

function pb.pb_CSActivityArknightsGameExploreStartReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameExploreStartReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    return tb
end

function pb.pb_CSActivityArknightsGameExploreStartReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
end

function pb.pb_CSActivityArknightsGameExploreStartResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameExploreStartRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.records = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.records[k] = pb.pb_ArknightsGameExploreRecordDecode(v)
    end
    return tb
end

function pb.pb_CSActivityArknightsGameExploreStartResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.records) then
        for i=1,#(tb.records) do
            pb.pb_ArknightsGameExploreRecordEncode(tb.records[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_ArknightsGameExploreRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArknightsGameExploreRecord) or {} 
    local __event_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __event_id ~= 0 then tb.event_id = __event_id end
    local __position_desc = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __position_desc ~= "" then tb.position_desc = __position_desc end
    local __npc_desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __npc_desc ~= "" then tb.npc_desc = __npc_desc end
    local __cost_san_num = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __cost_san_num ~= 0 then tb.cost_san_num = __cost_san_num end
    local __gain_money_num = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __gain_money_num ~= 0 then tb.gain_money_num = __gain_money_num end
    return tb
end

function pb.pb_ArknightsGameExploreRecordEncode(tb, encoder)
    if(tb.event_id) then    encoder:addu64(1, tb.event_id)    end
    if(tb.position_desc) then    encoder:addstr(2, tb.position_desc)    end
    if(tb.npc_desc) then    encoder:addstr(3, tb.npc_desc)    end
    if(tb.cost_san_num) then    encoder:addi64(5, tb.cost_san_num)    end
    if(tb.gain_money_num) then    encoder:addi64(6, tb.gain_money_num)    end
end

function pb.pb_CSActivityArknightsGameExploreEndReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameExploreEndReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __event_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __event_id ~= 0 then tb.event_id = __event_id end
    return tb
end

function pb.pb_CSActivityArknightsGameExploreEndReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.event_id) then    encoder:addu64(2, tb.event_id)    end
end

function pb.pb_CSActivityArknightsGameExploreEndResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameExploreEndRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_CSActivityArknightsGameExploreEndResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(5))    end
end

function pb.pb_CSActivityArknightsGameReceiveCycleRewardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameReceiveCycleRewardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __cycle = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __cycle ~= 0 then tb.cycle = __cycle end
    local __reward_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __reward_id ~= 0 then tb.reward_id = __reward_id end
    return tb
end

function pb.pb_CSActivityArknightsGameReceiveCycleRewardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.cycle) then    encoder:addi64(2, tb.cycle)    end
    if(tb.reward_id) then    encoder:addu64(3, tb.reward_id)    end
end

function pb.pb_CSActivityArknightsGameReceiveCycleRewardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityArknightsGameReceiveCycleRewardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_CSActivityArknightsGameReceiveCycleRewardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(5))    end
end

function pb.pb_AhsarahTravelInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelInfo) or {} 
    local __self_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __self_id ~= 0 then tb.self_id = __self_id end
    tb.line_infos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.line_infos[k] = pb.pb_AhsarahTravelLineInfoDecode(v)
    end
    tb.hero_info = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.hero_info[k] = pb.pb_AhsarahTravelHeroInfoDecode(v)
    end
    local __today_progress = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __today_progress ~= 0 then tb.today_progress = __today_progress end
    local __daily_max_progress = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __daily_max_progress ~= 0 then tb.daily_max_progress = __daily_max_progress end
    tb.score_descs = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.score_descs[k] = pb.pb_AhsarahTravelScoreDescDecode(v)
    end
    return tb
end

function pb.pb_AhsarahTravelInfoEncode(tb, encoder)
    if(tb.self_id) then    encoder:addu64(1, tb.self_id)    end
    if(tb.line_infos) then
        for i=1,#(tb.line_infos) do
            pb.pb_AhsarahTravelLineInfoEncode(tb.line_infos[i], encoder:addsubmsg(2))
        end
    end
    if(tb.hero_info) then
        for i=1,#(tb.hero_info) do
            pb.pb_AhsarahTravelHeroInfoEncode(tb.hero_info[i], encoder:addsubmsg(3))
        end
    end
    if(tb.today_progress) then    encoder:addi64(4, tb.today_progress)    end
    if(tb.daily_max_progress) then    encoder:addi64(5, tb.daily_max_progress)    end
    if(tb.score_descs) then
        for i=1,#(tb.score_descs) do
            pb.pb_AhsarahTravelScoreDescEncode(tb.score_descs[i], encoder:addsubmsg(6))
        end
    end
end

function pb.pb_AhsarahTravelScoreDescDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelScoreDesc) or {} 
    local __desc = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __num = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= "" then tb.num = __num end
    return tb
end

function pb.pb_AhsarahTravelScoreDescEncode(tb, encoder)
    if(tb.desc) then    encoder:addstr(1, tb.desc)    end
    if(tb.num) then    encoder:addstr(2, tb.num)    end
end

function pb.pb_AhsarahTravelLineInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelLineInfo) or {} 
    local __hero_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __state = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __progress = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __progress ~= 0 then tb.progress = __progress end
    tb.messages = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.messages[k] = pb.pb_AhsarahTravelMilestoneMessageDecode(v)
    end
    tb.final_rewards = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.final_rewards[k] = pb.pb_PropInfoDecode(v)
    end
    local __received = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    return tb
end

function pb.pb_AhsarahTravelLineInfoEncode(tb, encoder)
    if(tb.hero_id) then    encoder:addu64(1, tb.hero_id)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.state) then    encoder:addu32(3, tb.state)    end
    if(tb.progress) then    encoder:addi64(4, tb.progress)    end
    if(tb.messages) then
        for i=1,#(tb.messages) do
            pb.pb_AhsarahTravelMilestoneMessageEncode(tb.messages[i], encoder:addsubmsg(5))
        end
    end
    if(tb.final_rewards) then
        for i=1,#(tb.final_rewards) do
            pb.pb_PropInfoEncode(tb.final_rewards[i], encoder:addsubmsg(6))
        end
    end
    if(tb.received) then    encoder:addbool(7, tb.received)    end
end

function pb.pb_AhsarahTravelMilestoneMessageDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelMilestoneMessage) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __progress = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __progress ~= 0 then tb.progress = __progress end
    local __title = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __title ~= "" then tb.title = __title end
    local __desc = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    tb.images = decoder:getstrary(5)
    local __time = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __time ~= "" then tb.time = __time end
    tb.like_hero_ids = decoder:getu64ary(7)
    tb.relays = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.relays[k] = pb.pb_AhsarahTravelMilestoneReplyDecode(v)
    end
    tb.reply_choices = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.reply_choices[k] = pb.pb_AhsarahTravelPlayerReplyDecode(v)
    end
    local __replied = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __replied ~= false then tb.replied = __replied end
    tb.show_rewards = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.show_rewards[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_AhsarahTravelMilestoneMessageEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.progress) then    encoder:addi64(2, tb.progress)    end
    if(tb.title) then    encoder:addstr(3, tb.title)    end
    if(tb.desc) then    encoder:addstr(4, tb.desc)    end
    if(tb.images) then    encoder:addstr(5, tb.images)    end
    if(tb.time) then    encoder:addstr(6, tb.time)    end
    if(tb.like_hero_ids) then    encoder:addu64(7, tb.like_hero_ids)    end
    if(tb.relays) then
        for i=1,#(tb.relays) do
            pb.pb_AhsarahTravelMilestoneReplyEncode(tb.relays[i], encoder:addsubmsg(8))
        end
    end
    if(tb.reply_choices) then
        for i=1,#(tb.reply_choices) do
            pb.pb_AhsarahTravelPlayerReplyEncode(tb.reply_choices[i], encoder:addsubmsg(9))
        end
    end
    if(tb.replied) then    encoder:addbool(10, tb.replied)    end
    if(tb.show_rewards) then
        for i=1,#(tb.show_rewards) do
            pb.pb_PropInfoEncode(tb.show_rewards[i], encoder:addsubmsg(11))
        end
    end
end

function pb.pb_AhsarahTravelPlayerReplyDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelPlayerReply) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __desc = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    return tb
end

function pb.pb_AhsarahTravelPlayerReplyEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.desc) then    encoder:addstr(2, tb.desc)    end
end

function pb.pb_AhsarahTravelMilestoneReplyDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelMilestoneReply) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __hero_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __desc = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    local __time = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __time ~= "" then tb.time = __time end
    tb.rewards = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.rewards[k] = pb.pb_PropInfoDecode(v)
    end
    local __received = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __received ~= false then tb.received = __received end
    local __at_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __at_id ~= 0 then tb.at_id = __at_id end
    tb.reply_choices = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.reply_choices[k] = pb.pb_AhsarahTravelPlayerReplyDecode(v)
    end
    local __replied = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __replied ~= false then tb.replied = __replied end
    return tb
end

function pb.pb_AhsarahTravelMilestoneReplyEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.hero_id) then    encoder:addu64(2, tb.hero_id)    end
    if(tb.desc) then    encoder:addstr(3, tb.desc)    end
    if(tb.time) then    encoder:addstr(4, tb.time)    end
    if(tb.rewards) then
        for i=1,#(tb.rewards) do
            pb.pb_PropInfoEncode(tb.rewards[i], encoder:addsubmsg(5))
        end
    end
    if(tb.received) then    encoder:addbool(6, tb.received)    end
    if(tb.at_id) then    encoder:addu64(7, tb.at_id)    end
    if(tb.reply_choices) then
        for i=1,#(tb.reply_choices) do
            pb.pb_AhsarahTravelPlayerReplyEncode(tb.reply_choices[i], encoder:addsubmsg(8))
        end
    end
    if(tb.replied) then    encoder:addbool(9, tb.replied)    end
end

function pb.pb_AhsarahTravelHeroInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AhsarahTravelHeroInfo) or {} 
    local __hero_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __hero_name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __hero_name ~= "" then tb.hero_name = __hero_name end
    local __image = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __image ~= "" then tb.image = __image end
    return tb
end

function pb.pb_AhsarahTravelHeroInfoEncode(tb, encoder)
    if(tb.hero_id) then    encoder:addu64(1, tb.hero_id)    end
    if(tb.hero_name) then    encoder:addstr(2, tb.hero_name)    end
    if(tb.image) then    encoder:addstr(3, tb.image)    end
end

function pb.pb_CSActivityAhsarahTravelTrackReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelTrackReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __hero_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    return tb
end

function pb.pb_CSActivityAhsarahTravelTrackReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.hero_id) then    encoder:addu64(2, tb.hero_id)    end
end

function pb.pb_CSActivityAhsarahTravelTrackResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelTrackRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityAhsarahTravelTrackResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityAhsarahTravelLikeReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelLikeReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __message_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __message_id ~= 0 then tb.message_id = __message_id end
    return tb
end

function pb.pb_CSActivityAhsarahTravelLikeReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.message_id) then    encoder:addu64(2, tb.message_id)    end
end

function pb.pb_CSActivityAhsarahTravelLikeResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelLikeRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(5))
    local __gain_currency_num = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __gain_currency_num ~= 0 then tb.gain_currency_num = __gain_currency_num end
    local __gain_currency_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __gain_currency_id ~= 0 then tb.gain_currency_id = __gain_currency_id end
    return tb
end

function pb.pb_CSActivityAhsarahTravelLikeResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(5))    end
    if(tb.gain_currency_num) then    encoder:addi64(6, tb.gain_currency_num)    end
    if(tb.gain_currency_id) then    encoder:addu64(7, tb.gain_currency_id)    end
end

function pb.pb_CSActivityAhsarahTravelReplyReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelReplyReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __message_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __message_id ~= 0 then tb.message_id = __message_id end
    local __reply_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __reply_id ~= 0 then tb.reply_id = __reply_id end
    local __player_reply_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __player_reply_id ~= 0 then tb.player_reply_id = __player_reply_id end
    return tb
end

function pb.pb_CSActivityAhsarahTravelReplyReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.message_id) then    encoder:addu64(2, tb.message_id)    end
    if(tb.reply_id) then    encoder:addu64(3, tb.reply_id)    end
    if(tb.player_reply_id) then    encoder:addu64(4, tb.player_reply_id)    end
end

function pb.pb_CSActivityAhsarahTravelReplyResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelReplyRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSActivityAhsarahTravelReplyResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
end

function pb.pb_CSActivityAhsarahTravelReceiveReplyRewardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelReceiveReplyRewardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __reply_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __reply_id ~= 0 then tb.reply_id = __reply_id end
    return tb
end

function pb.pb_CSActivityAhsarahTravelReceiveReplyRewardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.reply_id) then    encoder:addu64(2, tb.reply_id)    end
end

function pb.pb_CSActivityAhsarahTravelReceiveReplyRewardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelReceiveReplyRewardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_CSActivityAhsarahTravelReceiveReplyRewardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(5))    end
end

function pb.pb_CSActivityAhsarahTravelReceiveLineRewardReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelReceiveLineRewardReq) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __hero_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    return tb
end

function pb.pb_CSActivityAhsarahTravelReceiveLineRewardReqEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.hero_id) then    encoder:addu64(2, tb.hero_id)    end
end

function pb.pb_CSActivityAhsarahTravelReceiveLineRewardResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSActivityAhsarahTravelReceiveLineRewardRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.activity_info = pb.pb_ActivityInfoDecode(decoder:getsubmsg(2))
    tb.data_change = pb.pb_DataChangeDecode(decoder:getsubmsg(3))
    tb.currency_infos = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.currency_infos[k] = pb.pb_ActivityCurrencyInfoDecode(v)
    end
    tb.expand_info = pb.pb_ActivityRewardExpandInfoDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_CSActivityAhsarahTravelReceiveLineRewardResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.activity_info) then    pb.pb_ActivityInfoEncode(tb.activity_info, encoder:addsubmsg(2))    end
    if(tb.data_change) then    pb.pb_DataChangeEncode(tb.data_change, encoder:addsubmsg(3))    end
    if(tb.currency_infos) then
        for i=1,#(tb.currency_infos) do
            pb.pb_ActivityCurrencyInfoEncode(tb.currency_infos[i], encoder:addsubmsg(4))
        end
    end
    if(tb.expand_info) then    pb.pb_ActivityRewardExpandInfoEncode(tb.expand_info, encoder:addsubmsg(5))    end
end

