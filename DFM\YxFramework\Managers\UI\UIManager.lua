----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManager)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class UIManager : ManagerBase
local UIManager = class("UIManager", require("DFM.YxFramework.Managers.ManagerBase"))
---------------------------------------------------------------------------------------------------------------
--- UIManager，负责UI蓝图资源的加载、缓存管理策略、ui实例创建和层级特性、实例缓存等等整套UI管理体系
--- [UIManager]
---
--- UIManager放置核心打开关闭、交互屏蔽接口逻辑
--- *更多逻辑见YxFramework/Managers/UI/Part/UIManager_XXXLogic
---     *UIManager_ResLogic
---     *UIManager_InsLogic
---     *UIManager_LayerLogic
---     *UIManager_RuleLogic
---     *UIManager_UECallLogic
---     *UIManager_SubPackLogic
---     *UIManager_LinkLogic
---     *UIManager_NavigationLogic (DFHD_LUA == 1)
---------------------------------------------------------------------------------------------------------------
require("DFM.YxFramework.Managers.UI.UIGlobalConst")
require("DFM.YxFramework.Managers.UI.Handle.SingleUIHandle")
require("DFM.YxFramework.Managers.UI.Handle.BatchUIHandle")

require("DFM.YxFramework.Managers.UI.Util.UIUtil")
require("DFM.YxFramework.Managers.Resource.Stub.ResStub")
require("DFM.YxFramework.Managers.Resource.Util.ResPathUtil")
require("DFM.YxFramework.Managers.UI.LuaUIBaseView")
local InputMonitor = require "DFM.YxFramework.Managers.UI.Input.InputMonitor"
local InputDebugHelper = require "DFM.YxFramework.Managers.UI.Input.Debug.InputDebugHelper"
local UDFMLocalizationManager = import("DFMLocalizationManager")
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local AnalysisUtil = require "DFM.YxFramework.Util.AnalysisUtil"
local UISimulatorUtil = require "DFM.YxFramework.Managers.UI.Util.UISimulatorUtil"
local UIReleaseUtil = require "DFM.YxFramework.Managers.UI.Util.UIReleaseUtil"

function UIManager:Ctor()
    self._bTicking = false

    if DFHD_LUA == 1 then
        -- UWidgetUtil.ToggleTabNavigation(false)
        -- UWidgetUtil.ToggleKeyNavigation(false)
        -- UWidgetUtil.ToggleAnalogNavigation(false)
    end

    self.Events = {
        evtStackUIBeforeChange = LuaEvent:NewIns("evtStackUIBeforeChange"), -- 栈UI切换之前
        evtStackUIBeforeChangePhase2 = LuaEvent:NewIns("evtStackUIBeforeChangePhase2"), -- 栈UI切换之前, 前一个UI Hide之后
        evtStackUIChanged = LuaEvent:NewIns("evtStackUIChanged"), -- 栈UI切换后(OnOpen/OnShow已经走完)
        evtPostProcessStackUIChanged = LuaEvent:NewIns("evtPostProcessStackUIChanged"), -- Post Process evtStackUIChanged

        evtStackUIPopAllUI = LuaEvent:NewIns("evtStackUIPopAllUI"), -- 栈UI弹出所有UI(可能因为参数保留底部一个)

        evtStackLinkSubStageNotChanged = LuaEvent:NewIns("evtStackLinkSubStageNotChanged"),
        evtPopLinkSubStageNotChanged = LuaEvent:NewIns("evtPopLinkSubStageNotChanged"),
        evtTransitionStateChanged = LuaEvent:NewIns("evtTransitionStateChanged"), ---转场黑幕进入或退出的完成时刻

        evtPopUIBeforeChange = LuaEvent:NewIns("evtPopUIBeforeChange"), -- POP UI切换前
        evtPopUIChanged = LuaEvent:NewIns("evtPopUIChanged"), -- POP UI切换后(OnOpen/OnShow已经走完)

        evtBackRootUIChanged = LuaEvent:NewIns("evtBackRootUIChanged"), 

        evtPostValue = LuaEvent:NewIns("evtPostValue"),
        evtPostStringValue = LuaEvent:NewIns("evtPostStringValue"),

        evtHUDUICreated = LuaEvent:NewIns("evtHUDUICreated"),

        evtOnMouseMove = LuaEvent:NewIns("evtOnMouseMove"),
        evtOnCultureChanged = LuaEvent:NewIns("evtOnCultureChanged"), -- 语言切换之后

        evtUltimateClearFinished = LuaEvent:NewIns("evtUltimateClearFinished"),
        evtLayerBinaryKeyReset = LuaEvent:NewIns("evtLayerBinaryKeyReset"),
        evtAndroidBackEnableChanged = LuaEvent:NewIns("evtAndroidBackEnableChanged"),
        evtApplyThemeIDChanged = LuaEvent:NewIns("evtApplyThemeIDChanged")
    }

    --- 交互监视器
    self._inputMonitor = InputMonitor:Get()

    --- Tick
    ---@type LuaUIBaseView[]
    self.cacheUIForCurrentFrame = {}

    -- LuaTickController:Get():RegisterTick(self)
    if not VersionUtil.IsShipping() then
        InputDebugHelper.BindDebugButtons(self)
    end

    if UDFMLocalizationManager then
        local gameInst = GetGameInstance()
        UDFMLocalizationManager.Get(gameInst).OnCultureChanged:Add(self._OnCultureChanged, self)
    end
    self.bEnableAndroidBack = true
end

---------------------------------------------------------------------------------
--- UIManager交互相关 Public API
---------------------------------------------------------------------------------
function UIManager:GetInputMonitor()
    return self._inputMonitor
end

function UIManager:EnableInput(eInputChangeReason)
    self._inputMonitor:EnableInputProcess(eInputChangeReason)
end

function UIManager:DisableInput(eInputChangeReason)
    self._inputMonitor:DisableInputProcess(eInputChangeReason)
end

function UIManager:IsInputEnable()
    self._inputMonitor:IsInputEnableProcess()
end
--------------------------------------------------------------------------
--- UIManager Public API 打开、关闭UI面板: 
--- 1.HUD
--------------------------------------------------------------------------
---@param hudNavID number 来自于HUDTable里配置的HUDName2ID 当前
---@param baseHudNavID number 来自于HUDTable里配置的HUDName2ID 默认
function UIManager:PreloadHUD(hudNavID, baseHudNavID)
    logframe("UIManager:PreloadHUD ", hudNavID, " ", baseHudNavID)
    baseHudNavID = setdefault(baseHudNavID, HUDName2ID.BaseHUD)
    local hudSettings = HUDTable[hudNavID]
    local baseHudSettings = HUDTable[baseHudNavID]
    self.mapLayer2Controller[EUILayer.HUD]:PreloadBySettings(hudSettings, baseHudSettings)
end

---@param hudNavID number 来自于HUDTable里配置的HUDName2ID 当前
---@param baseHudNavID number 来自于HUDTable里配置的HUDName2ID 默认
function UIManager:RefreshHUD(hudNavID, baseHudNavID)
    logframe("UIManager:RefreshHUD ", hudNavID, " ", baseHudNavID)
    baseHudNavID = setdefault(baseHudNavID, HUDName2ID.BaseHUD)
    local hudSettings = HUDTable[hudNavID]
    local baseHudSettings = HUDTable[baseHudNavID]
    self.mapLayer2Controller[EUILayer.HUD]:RefreshBySettings(hudSettings, baseHudSettings)
end

--- 此方法为了适应UECall_DeleteHUD_ADFMHUD_EndPlay只能销毁部分HUD的逻辑
--- 仍未启用
---@param hudNavID number 来自于HUDTable里配置的HUDName2ID 当前
---@param baseHudNavID number 来自于HUDTable里配置的HUDName2ID 默认
function UIManager:ClearHUD(hudNavID, baseHudNavID)
    logframe("UIManager:ClearHUD ", hudNavID, " ", baseHudNavID)
    baseHudNavID = setdefault(baseHudNavID, HUDName2ID.BaseHUD)
    local hudSettings = HUDTable[hudNavID]
    local baseHudSettings = HUDTable[baseHudNavID]
    self.mapLayer2Controller[EUILayer.HUD]:ClearHUDBySettings(hudSettings, baseHudSettings)
end

--------------------------------------------------------------------------
--- UIManager Public API 打开、关闭UI面板: 
--- 2.普通LuaUI，配置静态SubUIList
--------------------------------------------------------------------------
--- 异步打开配置好lua脚本的UI
---@param UINavID number 来自于UITable里配置的UIName2ID
---@param fLoadFinCallback function
---@param caller table
---@param ... any 调用传入UIIns:OnInitExtraData(...)
function UIManager:AsyncShowUI(UINavID, fLoadFinCallback, caller, ...)
    --- 1.动态子UI资源[Res]检查
    self:UpdateDynamicSubUIIdListById(UINavID, {})
    --- 2.Handle代理UI实例[Layer]流程
    return self:_InternalAsyncShowUI(UINavID, fLoadFinCallback, caller, ...)
end

---use this api when u need to close ui!
function UIManager:CloseUI(uiIns)
    if uiIns and not hasdestroy(uiIns) and uiIns.GetUISettings then
        local bShouldTraceUI = AnalysisUtil.ShouldTraceUI()
        if bShouldTraceUI then
            AnalysisUtil.StartUICloseTime(uiIns.UINavID)
            self:_InternalCloseUI(uiIns)
            AnalysisUtil.StopUICloseTime(uiIns.UINavID)
        else
            self:_InternalCloseUI(uiIns)
        end
    else
        if not VersionUtil.EnablePerformanceMode() then
            if uiIns and uiIns._cname and __DebugOnly_UIState then
                logwarning("UIManager:CloseUI(uiIns) the ui needed to close is nil or hasdestroy:", uiIns._cname, hasdestroy(uiIns), __DebugOnly_UIState(uiIns._curUIState))
            else
                logwarning("UIManager:CloseUI(uiIns) the ui needed to close is nil or hasdestroy uiIns:", uiIns)
            end
        end
    end
end

---use this api when u need to close ui!
function UIManager:CloseUIByHandle(uiHandle)
    if uiHandle == nil or hasdestroy(uiHandle) then
        return
    end
    local uiIns = uiHandle:GetUIIns()
    if uiIns then
        self:CloseUI(uiIns)
    else
        uiHandle:CancelLoadProcess()
    end
end

--------------------------------------------------------------------------
--- UIManager Public API 打开、关闭UI面板: 
--- 3.普通LuaUI，定制化动态dynamicSubUIList + 配置静态SubUIList
--------------------------------------------------------------------------
--- 异步打开配置好lua脚本的UI
---@param UINavID number 来自于UITable里配置的UIName2ID
---@param dynamicSubUIList table UINavIDList
---@param fLoadFinCallback function
---@param caller table
---@param ... args 调用传入UIIns:OnInitExtraData(...)
function UIManager:AsyncShowUICustomlized(UINavID, dynamicSubUIList, fLoadFinCallback, caller, ...)
    --- 1.动态子UI资源[Res]检查
    self:UpdateDynamicSubUIIdListById(UINavID, dynamicSubUIList)
    --- 2.Handle代理UI实例[Layer]流程
    return self:_InternalAsyncShowUI(UINavID, fLoadFinCallback, caller, ...)
end

---@param UINavID number 来自于UITable里配置的UIName2ID
---@param dynamicSubUIList table UINavIDList
---@param fLoadFinCallback function
---@param caller table
---@param ... args 调用传入UIIns:OnInitExtraData(...)
function UIManager:AsyncCreateSubUICustomlized(UINavID, dynamicSubUIList, fLoadFinCallback, caller, ...)
    --- 1.动态子UI资源[Res]检查
    self:UpdateDynamicSubUIIdListById(UINavID, dynamicSubUIList)
    --- 2.Handle代理UI实例[Layer]流程
    return self:AsyncCreateSubUI(UINavID, fLoadFinCallback, caller, ...)
end

function UIManager:CloseUIByBatchHandle(uiBatchHandle)
    if uiBatchHandle == nil or hasdestroy(uiBatchHandle) then
        return
    end
    local uiInsList = uiBatchHandle:GetObjInsList()
    if uiInsList then
        for idx, uiIns in ipairs(uiInsList) do
            self:CloseUI(uiIns)
        end
    else
        uiBatchHandle:CancelLoadProcess()
    end
end

--------------------------------------------------------------------------
--- UIManager Public API 创建子UI: 
--- 5.子UI的创建操作（子控件需手动加入父容器控件中AddChild）
--------------------------------------------------------------------------
--- 异步
function UIManager:AsyncCreateSubUI(UINavID, fLoadFinCallback, caller, ...)
    if AnalysisUtil.ShouldTraceUI() then
        AnalysisUtil.StartAddSubUITime(UINavID)
    end

    local fOnLoadFinCallback = CreateCallBack(function(self, uiIns)
        --- UIIns 已经创建好（非Sub类型的UI此时已经加入根节点）
        self:_OnLuaUIBaseViewCreated(uiIns)
        --- UIIns 注册彻底关闭回调
        -- uiIns:AddCloseCallBack(self._OnLuaUIBaseViewMuted, self)
        safecall(fLoadFinCallback, caller, uiIns)

        if AnalysisUtil.ShouldTraceUI() then
            AnalysisUtil.StopAddSubUITime(UINavID)
        end
    end, self)
    ---@type SingleUIHandle
    local uiHandle = SingleUIHandle:NewIns(UINavID, fOnLoadFinCallback, nil, ...)
    uiHandle:AsyncLoadRes(false, false)
    return uiHandle
end

--- 异步仅仅加载资源（允许UI调用，准备异步子UI）
function UIManager:AsyncLoadUIResOnly(UINavID, fLoadFinCallback, caller)
    local stub, loadBatchId
    local uiSettings = UITable[UINavID]
    if uiSettings == nil then
        logbox('UI配置不存在, 请检查EntranceFlow中的模块初始化顺序 :', debug.traceback())
        return
    end
    stub, loadBatchId = Facade.UIManager:AsyncLoadUIAsset(UINavID, fLoadFinCallback, caller)
    return loadBatchId
end

--------------------------------------------------------------------------
--- 通过该方法创建SubUI时，需检查两处配置
--- 1.使用AsyncShowUI打开的UI:
---     必须在创建者的XXXConfig中加入SubUIs = { (待创建的SubUI) };
--- 2.使用AsyncShowUICustomlized打开的UI:
---     要么在创建者的XXXConfig中加入SubUIs = { (待创建的SubUI) };
---     要么作为动态id列表传入打开参数;
---
--- 否则会失败, 因为此时可能加载了未Loaded的资源;
---
--- [注意] SubUIList与DynamicSubUIList为叠加关系;
--------------------------------------------------------------------------
---@param UINavID number
---@param ... arges
---@return uiIns
function UIManager:CreateSubUI(UINavID, ...)
    local uiSettings = UITable[UINavID]
    if not uiSettings then
        logbox("CreateSubUI failed, uiSettings 不存在, UINavId == "..tostring(UINavID))
        return
    end

    if uiSettings.UILayer ~= EUILayer.Sub then
        logbox("CreateSubUI failed, uiSettings.UILayer ~= EUILayer.Sub, UINavId == "..tostring(UINavID))
        return
    end

    local bShouldTraceUI = AnalysisUtil.ShouldTraceUI()
    if bShouldTraceUI then
        AnalysisUtil.StartAddSubUITime(UINavID)
    end

    local fullPath = UIName2ID.GetBPFullPathByID(UINavID)
    local asset = self:TryGetLoadedResByPath(fullPath)
    if asset == nil then
        logerror(fullPath and uiSettings.BPPath ..", is not a preloaded sub view, 请检查当前AddSubUI的父UITable里是否正确配置了子UI到SubUIs,如果该子UI有嵌套子UI树,可以考虑配置根节点子UI")
        if bShouldTraceUI then
            AnalysisUtil.StopAddSubUITime(UINavID)
        end
        return
    end
    
    -- [optimization 0] 开启同步加载
    -- if not asset then
    --     asset = ULuaExtension.FindUIResObject(GetWorld(), fullPath)
    --     self.stubRuntimeUIRes:AddResRef(fullPath, asset)
    -- end

    -- [optimization 1] 单独handle
    -- local uiHandle = SingleUIHandle:NewIns(UINavID, nil, nil, ...)
    -- return uiIns, uiHandle
    
    -- [optimization 2] 复用UIHandle，不需要每次都创建一个新的handle，handle不再作为返回值返回
    local uiHandle = self:_GetSubUIHandle()
    uiHandle.UINavID = UINavID
    uiHandle.extraParams = {...}
    uiHandle.extraParams.n = select("#", ...)
    local uiIns = uiHandle:CreateObjByLoadedRes(asset)
    if uiIns then
        self:_OnLuaUIBaseViewCreated(uiIns)
        -- uiIns:AddCloseCallBack(self._OnLuaUIBaseViewMuted, self)
    end

    if bShouldTraceUI then
        AnalysisUtil.StopAddSubUITime(UINavID)
    end
    uiHandle:Reset()
    return uiIns
end

--- 创建子UI并绑定owner
--- OwnerIns 关闭后，清理UI资源
---@param ownerIns LuaUIBaseView
function UIManager:CreateSubUIBindOwner(ownerIns, UINavID, ...)
    self:BindOwnerSubHistory(ownerIns, UINavID)
    return self:CreateSubUI(UINavID, ...)
end

--------------------------------------------------------------------------
--- UIManager Private API: 
--- SubUI复用同一个Handle
--------------------------------------------------------------------------
function UIManager:_GetSubUIHandle()
    if not self._subUIHandle then
        self._subUIHandle = SingleUIHandle:NewIns(0)
    else
        self._subUIHandle:Reset()
    end
    return self._subUIHandle
end

-----------------------------------------------------------------------
--region ui cache for current frame
--Try to replace CheckParentNextTick
--Maybe there are better solution

function UIManager:CacheUIForCurrentFrame(ui)
    table.insert(self.cacheUIForCurrentFrame, ui)

    if not self._bTicking then
        self._bTicking = true
        LuaTickController:Get():RegisterTick(self)
    end
end

function UIManager:ForceCheckPendingUIParents()
    self.cacheUIForCurrentFrame = {}
end

--endregion
-----------------------------------------------------------------------


function UIManager:EnableLifeFunctionFraming(bEnable)
    loginfo("[UIManager] EnableLifeFunctionFraming", bEnable)
    gEnableLifeFunctionFraming(bEnable)
end

---------------------------------------------------------------------------------
--- UIManager语言切换相关 Private API
---------------------------------------------------------------------------------
function UIManager:_OnCultureChanged(newCulture)
    loginfo("[UIManager] _OnCultureChanged", newCulture)
    LocalizeTool.RefreshCulture() -- 语言切换之后需要刷新这个全局的值
    self.Events.evtOnCultureChanged:Invoke(newCulture)
end

--------------------------------------------------------------------------
--- UIManager 内部生命周期
--------------------------------------------------------------------------
function UIManager:Update(dt)
    self:ForceCheckPendingUIParents()

    LuaTickController:Get():RemoveTick(self)
    self._bTicking = false
end

---@param gameFlowType EGameFlowStageType
function UIManager:OnGameFlowChangeLeave(gameFlowType)
    if DFHD_LUA == 1 then
        self:ClearNavigationSelector()
    end
    logwarning('#[ GFChange VS WorldCleanUp ]------------------------【OnGameFlowChangeLeave】 gameFlowType:', 
    __DebugOnly_GameFlowName(gameFlowType),
    'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))

    -- self:TryUnloadAllLinkSubStage(gameFlowType)
    if not GF_CheckIsLoadingFlowType(gameFlowType) then
        loginfo('#[ GFChange VS WorldCleanUp ]---------------------------------------- UIManager:Reset()【OnGameFlowChangeLeave】',
        ' gameFlowType:', __DebugOnly_GameFlowName(gameFlowType),
        'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
        self:Reset()
    end
    if self._inputMonitor then
        self._inputMonitor:OnGameFlowChangeLeave(gameFlowType)
    end
    UIReleaseUtil.FlushAllFramingTask()
end

---@param gameFlowType EGameFlowStageType
function UIManager:OnGameFlowChangeEnter(gameFlowType)
    -- loginfo('#[ 统一根节点RootUI Debug ]------------------------【OnGameFlowChangeEnter】 gameFlowType:', __DebugOnly_GameFlowName(gameFlowType))
    logwarning('#[ GFChange VS WorldCleanUp ]------------------------【OnGameFlowChangeEnter】 gameFlowType:', 
    __DebugOnly_GameFlowName(Facade.GameFlowManager:GetCurrentGameFlow()),
    'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
    local world = GetWorld()
    local worldName = UKismetSystemLibrary.GetObjectName(world)
    if not IsCurrentWorldValid() then
        loginfo('#[ worldName gameflow check ]------------------------【OnGameFlowChangeEnter】 worldName delay:', worldName)
        -- hack here for PIE with ds connected
        local timer = Timer:NewIns(LuaGlobalConst.TIMER_DURACTION_FOR_ONE_FLAME)
        local fDelayUtilWorldInited = function()
            if not IsCurrentWorldValid() then
                -- keep waiting
            else
                -- stop timer and init ui layers
                timer:Stop()
                timer:Release()
                timer = nil

                Facade.UIManager:InitUILayers()
            end
        end
        timer:AddListener(fDelayUtilWorldInited)
        timer:Start()
    else
        loginfo('#[ worldName gameflow check ]------------------------【OnGameFlowChangeEnter】 worldName:', worldName)
        self:InitUILayers()
    end
    
    if not GF_CheckIsLoadingFlowType(gameFlowType) then
        if self._lowMemoryHandle then
            Timer.CancelDelay(self._lowMemoryHandle)
            self._lowMemoryHandle = nil
        end
        if self._highMemoryHandle then
            Timer.CancelDelay(self._highMemoryHandle)
            self._highMemoryHandle = nil
        end
        self:CheckIsLowMemoryState()
    end

    if self._inputMonitor then
        self._inputMonitor:OnGameFlowChangeEnter(gameFlowType)
    end
    -- 临时方案

    --
    if DFHD_LUA == 1 then
        self:CreateNavigationSelector()
    end
end

function UIManager:OnLoadingBeforeChange(gameFlowType)
    -- logwarning("[ LoadingAndRes Debug ] ------------- UIManager:OnLoadingLogin2Frontend(gameFlowType) 清空LoadingStub", __DebugOnly_GameFlowName(gameFlowType))
    logwarning('#[ GFChange VS WorldCleanUp ]------------------------ self.stubLoadingUIRes:ClearAllResRefs 清空上次的LoadingStub【OnLoadingBeforeChange】',
    ' gameFlowType:', __DebugOnly_GameFlowName(gameFlowType),
    'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
    self.stubLoadingUIRes:ClearAllResRefs()
end

function UIManager:OnLoadingLogin2Frontend(gameFlowType)
end

function UIManager:OnLoadingFrontend2Game(gameFlowType)
end

function UIManager:OnLoadingGame2Frontend(gameFlowType)
    loginfo('#[ worldName gameflow check ]------------------------OnLoadingGame2Frontend IsInFrontEnd ExecuteConsoleCommand:', __DebugOnly_GameFlowName(gameFlowType))
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Slate.EnableOptInGame 0", nil)
end

function UIManager:Reset()
    logwarning("[ GameFlow Debug ] ******* Lua Clean All UI-------------when gameflow leave, clear all ins in layers")
    --- 1.实例清理
    self:_ClearUIInsOfLayers()
    --- 2.状态切换
    self:_ResetRuleOfLayers(ELayerRuleChangeReason.Superuser)
    self:_CloseFrameRootUI()
    --- 3.Handle清理
    SingleUIHandle.RemoveAllHandle()
    BatchUIHandle.RemoveAllHandle()
    --- 4.资源清理
    self:ClearUIResStub(true)
    self:SetStackBottomKeepMode(false, "UIManager:Reset")

    logwarning("[Low Memory Log - GameFlow Debug ] ******* Lua Clean All UI-------------when gameflow leave end, CheckIsLowMemoryState")
    if self._lowMemoryHandle then
        Timer.CancelDelay(self._lowMemoryHandle)
        self._lowMemoryHandle = nil
    end
    if self._highMemoryHandle then
        Timer.CancelDelay(self._highMemoryHandle)
        self._highMemoryHandle = nil
    end
end

function UIManager:SetFlagEnableAndroidBack(bEnableAndroidBack)
    self.bEnableAndroidBack = bEnableAndroidBack
    local layerController = self.mapLayer2Controller[EUILayer.Pop]
    if layerController then
        layerController:OnAndroidBackEnableChanged(bEnableAndroidBack)
    end
    self.Events.evtAndroidBackEnableChanged:Invoke(bEnableAndroidBack)
end

function UIManager:OnPostManagerCleanUp()
    -- loginfo('#[ GFChange VS WorldCleanUp ]----------------------------------------【OnPostManagerCleanUp】',
    -- ' gameFlowType:', __DebugOnly_GameFlowName(Facade.GameFlowManager:GetCurrentGameFlow()),
    -- 'worldName:', UKismetSystemLibrary.GetObjectName(GetWorld()))
    -- self:Reset()
end

function UIManager:OnForceCleanUpUI(uiInLayer)
    if uiInLayer and not hasdestroy(uiInLayer) then
        --- 确定非手动强制清理关闭UI
        self:_OnLuaUIBaseViewClosed(uiInLayer)

        --- 递归解绑子UIHistroy
        self:HandleBindHistory(false, uiInLayer, uiInLayer.UINavID)
    end
end

function UIManager:Destroy()
    if not VersionUtil.IsShipping() then
        InputDebugHelper.UnbindDebugButtons(self)
    end

    if UDFMLocalizationManager then
        local gameInst = GetGameInstance()
        UDFMLocalizationManager.Get(gameInst).OnCultureChanged:Remove(self._OnCultureChanged, self)
    end

    if self._lowMemoryHandle then
        Timer.CancelDelay(self._lowMemoryHandle)
        self._lowMemoryHandle = nil
    end
    if self._highMemoryHandle then
        Timer.CancelDelay(self._highMemoryHandle)
        self._highMemoryHandle = nil
    end

    self.bRePoolEnable = false
    --- 0.实例清理
    -- self:_ClearUIInsOfLayers()
    --- 1.状态切换
    self:_CloseFrameRootUI()

    --- 2.实例清理和Controller销毁
    self:ClearSubUIPack()
    self:_CloseLayerController()
    self:_InternalClearList()
    
    --- 3.资源Stub清理和销毁
    self:_ReleaseAllResStub()

    --- 4.管理逻辑清理
    LuaTickController:Get():RemoveTick(self)
    if DFHD_LUA == 1 then
        UWidgetUtil.ToggleTabNavigation(true)
        UWidgetUtil.ToggleKeyNavigation(true)
        UWidgetUtil.ToggleAnalogNavigation(true)
    end
end

extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_ResLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_AliveCountLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_InsLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_LayerLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_RuleLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_UECallLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_SubPackLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_LinkLogic")
extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_ThemeLogic")
if DFHD_LUA == 1 then
    extendclass(UIManager, require "DFM.YxFramework.Managers.UI.Part.UIManager_NavigationLogic")
end

return UIManager
