----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--里程碑
---@class ActivityTaraItem4 : LuaUIBaseView
local ActivityTaraItem4 = ui("ActivityTaraItem4")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local EGPInputType = import"EGPInputType"
local Config = Module.Activity.Config

function ActivityTaraItem4:Ctor()
    self._wtLock = self:Wnd("DFCanvasPanel_64", UIWidgetBase)
    self._wtFNum = self:Wnd("DFTextBlock_51"  , UITextBlock)
    self._wtFBtn = self:Wnd("DFCheckBox_2"    , UICheckBox)
    self._wtFBtn:Event("OnCheckStateChanged"  , self._OnReddotClicked, self)
    self._wtFBtn:Event("OnUncheckedHovered"   , self._OnHovered, self)
    self._wtRedd = self:Wnd("CanvasPanel_0"   , UIWidgetBase)
    self._wtReddIns = Module.ReddotTrie:CreateReddotIns(self._wtRedd, nil, nil, nil, FVector2D(0, 12))
end

function ActivityTaraItem4:_OnReddotClicked(isBool)
    local state = self:GetReddotState()
    local isUnlock = self:IsUnLock()
    --需要播放动画
    if state == 1 and isUnlock then
        self:SetReddotState(2)
        self:_RefreshReddot()
        self:_SetBtnState(true)
        self._isAnim = true
        --是否播放动画
        Config.evtOnClickTaraItem:Invoke(self._index, true)
        return
    end
    self:_RefreshReddot()
    self:_OnClicked(isBool)
end

function ActivityTaraItem4:OnAnimFinished(anim)
    if anim == self.WBP_PatrolAsala_Tab_Q1ZNK_unlock then
        self:SetLockBtnState()
        self:_RefreshReddot()
    end
end

function ActivityTaraItem4:SetReddotState(value)
    Server.ActivityServer:SetUserInt(self:GetReddotKey(), value)
end

function ActivityTaraItem4:GetReddotState()
    return Server.ActivityServer:GetUserInt(self:GetReddotKey(), 0)
end

function ActivityTaraItem4:GetReddotKey()
    local hero = self._hero
    local data = self._data
    if hero and data and hero.act_id and hero.count then
        if hero.heroId and data.count then
            local key = hero.act_id..hero.heroId..data.count
            return key
        end
    end
    return ""
end

function ActivityTaraItem4:_OnHovered()
    if IsHD() and WidgetUtil.IsGamepad() then
        self:_OnReddotClicked(true)
        Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Comment, self)
    end
end

function ActivityTaraItem4:InitData(data, index, defaultIndex, hero)
    self._data  = data
    self._index = index
    self._hero = hero
    if data and hero then
        self:_AddEventListener()
        self:SetLockBtnState()
        self:_RefreshReddot()
        self:_SetBtnState(index == defaultIndex)
        Timer.DelayCall(0, self.SetHandleState, self, index == defaultIndex)
    end
end

function ActivityTaraItem4:SetHandleState(isbool)
    if isbool then
        WidgetUtil.SetUserFocusToWidget(self, isbool)
    end
end

function ActivityTaraItem4:SetLockBtnState()
    local isUnLock = self:IsUnLock()
    local state = self:GetReddotState()
    --调用一下动画
    local isbool = self:_PlayUnlockAnim()
    if not isbool then
        state = 2
    end
    local data = self._data
    if data then
        if isUnLock and state ~= 1 then
            self._wtFNum:SetText(ActivityLogic.LocalizeText(data.title or ""))
        else
            self._wtFNum:SetText(data.count or 0)
        end
    end
    if isUnLock and state ~= 1 then
        self._wtLock:SetVisibility(ESlateVisibility.Collapsed)
    else
        self._wtLock:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    end
end

function ActivityTaraItem4:IsUnLock()
    local data = self._data
    local hero = self._hero
    if data and hero then
        if data.count and hero.count then
            return hero.count >= data.count
        end
    end
    return false
end

function ActivityTaraItem4:_RefreshReddot()
    local hero = self._hero
    local data = self._data
    if hero == nil or data == nil then
        return
    end
    --红点逻辑处理
    if hero.count and data.count then
        local state = 0
        local received = false
        if hero.count >= data.count then
            state = self:GetReddotState()
            for _, new in ipairs(data.news or {}) do
                if not new.received and new.rewards and #new.rewards > 0 then
                    received = true
                    break
                end
            end
        end
        self:_SetReddot(state == 1 or received)
    end
end

function ActivityTaraItem4:_SetReddot(isBool)
    if self._wtReddIns then
        if isBool then
            self._wtReddIns:SetReddotVisible(true, EReddotType.Normal)
        else
            self._wtReddIns:SetReddotVisible(false)
        end
    end
end

function ActivityTaraItem4:_SetBtnState(isBool)
    if self.SetColor then
        local state = 0
        if self:IsUnLock() then
            if isBool then
                state = 1
            else
                state = 0
            end
        else
            if isBool then
                state = 3
            else
                state = 2
            end
        end
        self:SetColor(state)
    end
    self._wtFBtn:SetIsChecked(isBool)
    if isBool then
        self:PlayAnimation(self.WBP_PatrolAsala_Tab_Q1ZNK_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function ActivityTaraItem4:_AddEventListener()
    self:AddLuaEvent(Config.evtOnClickTaraItem, self._OnClickTaraItem, self)
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

function ActivityTaraItem4:_OnClickTaraItem(index)
    self:_SetBtnState(index == self._index)
end

function ActivityTaraItem4:_OnAddTaraHandleAdaptation(handleType, ...)
    -- if handleType == ETaraHandleType.Anim then
    --     self:_PublishAnimation(...)
    -- end
end

function ActivityTaraItem4:_PlayUnlockAnim()
    local state = self:GetReddotState()
    if state == 1 and self:IsUnLock() then
        local key = "KEY_"..self:GetReddotKey()
        local isbool = Server.ActivityServer:GetUserInt(key, 1)
        if isbool == 1 then
            return false
        end
        Server.ActivityServer:SetUserInt(key, 2)
        self._isAnim = true
        self:_PublishAnimation()
    end
    return true
end

function ActivityTaraItem4:_PublishAnimation(time)
    local state = self:GetReddotState()
    local isUnlock = self:IsUnLock()
    if self._isAnim and isUnlock and state == 2 then
        self._isAnim = false
        self:PlayAnimation(self.WBP_PatrolAsala_Tab_Q1ZNK_unlock, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
    end
end

function ActivityTaraItem4:OnAnimFinished(anim)
    if anim == self.WBP_PatrolAsala_Tab_Q1ZNK_unlock then
        self:SetLockBtnState()
        self:_RefreshReddot()
    end
end

function ActivityTaraItem4:OnShowBegin()
end

function ActivityTaraItem4:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function ActivityTaraItem4:OnClose()
    self:_SetReddot()
    self._wtReddIns = nil
end

function ActivityTaraItem4:_OnClicked(isBool)
    if isBool then
        self:_SetBtnState(isBool)
        Config.evtOnClickTaraItem:Invoke(self._index)
    end
end

return ActivityTaraItem4