----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local ButtonIdConfig = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
---@class BattleFiledTeamCell : LuaUIBaseView
local BattleFiledTeamCell = ui("BattleFiledTeamCell")

local function log(...)
    loginfo("xxww", "[BattleFiledTeamCell]", ...)
end

function BattleFiledTeamCell:Ctor()
    self._wtPlayerScore = self:Wnd("DFTextBlock_0", UITextBlock)
    self._wtPlayerName = self:Wnd("DFTextBlock_1", UITextBlock)

    self._wtCaptureCount = self:Wnd("wtCaptureCount", UITextBlock)
    self._wtKillCount = self:Wnd("wtKillCount", UITextBlock)
    self._wtResurgenceCount = self:Wnd("wtRescueCountTB", UITextBlock)
    self._wtDFImage_Icon_3 = self:Wnd("DFImage_Icon_3", UIImage)
    -- 成就
    self._wtAchievementSGB = UIUtil.WndScrollGridBox(self, "wtAchievementSGB", self._OnGetAchievementCount, self._OnProcessAchievementWidget)
    self._achievementList = {}

    -- 中补
    self._wtJoinMiddle = self:Wnd("DFSizeBox_129", UIWidgetBase)

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 平台logo
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
end

function BattleFiledTeamCell:_OnLikeBtnHit()
    log("BattleFiledTeamCell:_OnLikeBtnHit")
    Module.CommonTips:ShowSimpleTip(Module.Settlement.Config.Loc.FunctionIsMakingTXT)
    local req = pb.CSPlayerInfoPraiseReq:New()
    req.be_praised_player_id = self.playerId
    req.praise_type = 0
    if not Server.SettlementServer:GetTDMSettlementIsAi(self.playerId) then
        log("BattleFiledTeamCell:_OnLikeBtnHit like player")
        req:Request(function()
        end)
    else
        log("BattleFiledTeamCell:_OnLikeBtnHit like ai")
    end
end

---@param playerInfo pb_TDMPlayer
function BattleFiledTeamCell:RefreshView(playerInfo)
    local myPlayerId = Server.AccountServer:GetPlayerId()
    self.tdmData = Server.SettlementServer:GetTDMSettlementInfo()
    self.playerId = playerInfo.player_id
    local playerName = Server.SettlementServer:GetTDMSettlementPlayerInfoNameById(self.playerId)
    local playerScore = playerInfo.score
    local playerLevel = playerInfo.basic_info.level
    self._achievementList = playerInfo.game_achievements

    self._wtJoinMiddle:Collapsed()
    if playerInfo.is_join_at_middle then
        self._wtJoinMiddle:SelfHitTestInvisible()
    end
    --- BEGIN MODIFICATION @ VIRTUOS
    local playerPlatID = Module.Settlement:GetPlatIDByPlayerInfo(playerInfo)
    logerror("function BattleFiledTeamCell:RefreshView playerPlatID", playerPlatID)

    if IsPS5Family() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager then
            local PS5OnlineId = DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(self.playerId)
            if not string.isempty(PS5OnlineId) then
                playerName = PS5OnlineId
            end
        end
    end
    --- END MODIFICATION

    if myPlayerId == self.playerId then
        self:SetCppValue("SetType", 1)
    else
        self:SetCppValue("SetType", 0)
    end
    self:BP_Refresh()
    self._wtPlayerName:SetText(playerName)
    self._wtPlayerScore:SetText(playerScore)
    self._wtResurgenceCount:SetText(playerInfo.rescue_contrib)
    self._wtKillCount:SetText(playerInfo.kill_ai_count + playerInfo.kill_player_count)
    if InGameController:Get():IsCaptureFlag() then
        self._wtDFImage_Icon_3:AsyncSetImagePath(Module.Settlement.Config.CaptureFlagImage)
        self._wtCaptureCount:SetText(playerInfo.capture_flag_num)
    else
        self._wtCaptureCount:SetText(playerInfo.capture_contrib)
    end
    self:PlayAnimationForward(self.WBP_Settlement_Battle_Role_in, 1.0, false)
    local achievementTable = Facade.TableManager:GetTable("Achievement")
    if achievementTable and self._achievementList then
        table.sort(
            self._achievementList,
            function(achievementA, achievementB)
                local orderA, orderB = achievementTable[achievementA.id] and achievementTable[achievementA.id].Order or 0
                    , achievementTable[achievementB.id] and achievementTable[achievementB.id].Order or 0
                return orderA < orderB
            end
        )
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and playerPlatID and self._wtPlatformIcon then
        -- 默认折叠
        self._wtPlatformIcon:Collapsed()
        local platIconPath = Module.Friend:GetPlatformIconPath(playerPlatID)
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
            self._wtPlatformIcon:SelfHitTestInvisible()
        end

        logerror("BattleFiledTeamCell:RefreshView", "playerId", self.playerId, "is not cross plat", self._wtPlayerName:GetText())
        if not Server.AccountServer:IsCrossPlat() then
            self._wtPlatformIcon:Collapsed()
        end
    end
    --- END MODIFICATION

    self._wtAchievementSGB:RefreshAllItems()
end
function BattleFiledTeamCell:_OnGetAchievementCount()
    if Server.SettlementServer:IsInHumanMachineRoom() then
        return 0
    end
    local ret = 0
    if next(self._achievementList) then
        return math.min(#self._achievementList, Module.Settlement.Config.MAX_SHOW_ACHIEVEMENT_NUM)
    end
    return ret
end

function BattleFiledTeamCell:_OnProcessAchievementWidget(idx, widget)
    widget:SetInfoBySelf(self._achievementList[idx + 1].id)
end

return BattleFiledTeamCell
