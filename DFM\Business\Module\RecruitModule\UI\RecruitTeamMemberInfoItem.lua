----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RecruitTeamMemberInfoItem : LuaUIBaseView
local RecruitTeamMemberInfoItem = ui("RecruitTeamMemberInfoItem")
local RecruitConfig = Module.Recruit.Config
local RecruitLogic = require "DFM.Business.Module.RecruitModule.Logic.RecruitLogic"
local RankIconAbbr = require "DFM.Business.Module.RankingModule.UI.RankIconAbbr"
local RecruitPlayerHeadItem = require "DFM.Business.Module.RecruitModule.UI.RecruitPlayerHeadItem"

function RecruitTeamMemberInfoItem:Ctor()
    self._wtEmptyHint = self:Wnd("wtEmptyHint", UIWidgetBase)
    self._wtTeamMemberInfoPanel = self:Wnd("wtTeamMemberInfoPanel", UIWidgetBase)
    self._wtPlayerHead = self:Wnd("wtPlayerHead", RecruitPlayerHeadItem)
    self._wtName = self:Wnd("wtName", UITextBlock)
    self._wtRankIcon = self:Wnd("wtRankIcon", RankIconAbbr)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
    self._wtMemberType = self:Wnd("wtMemberType", UITextBlock)
    --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
    self._wtPlatformIcon = self:Wnd("DFImage", UIImage)
    --- END MODIFICATION
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function RecruitTeamMemberInfoItem:OnInitExtraData(params)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecruitTeamMemberInfoItem:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecruitTeamMemberInfoItem:OnClose()
    self:RemoveAllLuaEvent()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecruitTeamMemberInfoItem:OnShow()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecruitTeamMemberInfoItem:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function RecruitTeamMemberInfoItem:OnAnimFinished(anim)
end

function RecruitTeamMemberInfoItem:InitPlayerInfo(playerSimpleInfo)
    self._wtEmptyHint:SelfHitTestInvisible()
    self._wtRankIcon:SetRankIconNone()
    self._wtRankIcon:SelfHitTestInvisible()
    self._wtRankDivision:SetText(RecruitConfig.Loc.NoRankDivision)
    self._wtRankDivision:SelfHitTestInvisible()
    self._wtTeamMemberInfoPanel:Collapsed()
    if playerSimpleInfo ~= nil then
        self._wtEmptyHint:Collapsed()
        self._wtTeamMemberInfoPanel:SelfHitTestInvisible()
        btnTbl =  {
            HeadButtonType.PlayerInformat,
            HeadButtonType.AddFriend,
        }
        if Server.TeamServer:IsCaptial() == true and Server.TeamServer:IsMember(playerSimpleInfo.player_id) ~= nil then
            table.insert(btnTbl, HeadButtonType.KickTeam)
            table.insert(btnTbl, HeadButtonType.PromotTeam)
        end
        if Server.TeamServer:FindMember(playerSimpleInfo.player_id) ~= nil then
            table.insert(btnTbl, HeadButtonType.ReportVoice)
        end
        self._wtPlayerHead:InitPlayerInfo(playerSimpleInfo, false, false, btnTbl)
        local playerName = playerSimpleInfo.nick_name
        if playerSimpleInfo.nick_name then
            local remarkName = Server.FriendServer:GetFriendRemarkById(playerSimpleInfo.player_id)
            if remarkName ~= "" then
                playerName = string.format(Module.Friend.Config.QQFriend, remarkName, playerSimpleInfo.nick_name)
            else
                playerName = playerSimpleInfo.nick_name
            end
        end
        self._wtName:SetText(playerName)
        if RecruitLogic.IsInMp() == true then
            if playerSimpleInfo.mp_rank_attended == true then
                local targetScore = (playerSimpleInfo.show_commander_rank_points ~= nil and playerSimpleInfo.show_commander_rank_points > 0) and playerSimpleInfo.mp_commander_score or playerSimpleInfo.mp_rank_score
                self._wtRankIcon:SetTournamentIconByScore(targetScore)
                local rankInfo = Module.Tournament:GetRankDataByScore(targetScore)
                self._wtRankDivision:SetText(rankInfo and rankInfo.Name or RecruitConfig.Loc.NoRankDivision)
            else
                self._wtRankIcon:SetRankIconNone()
            end
        else
            if playerSimpleInfo.sol_rank_attended == true then
                self._wtRankIcon:SetRankingIconByScore(playerSimpleInfo.sol_rank_score)
                local rankInfo = Module.Ranking:GetMinorDataByScore(playerSimpleInfo.sol_rank_score)
                self._wtRankDivision:SetText(rankInfo and rankInfo.RankName or RecruitConfig.Loc.NoRankDivision)
            else
                self._wtRankIcon:SetRankIconNone()
            end
        end
        self._wtPlayerHead:SetRankIconVisible(false)
        self._wtMemberType:SetText(playerSimpleInfo.player_id == Server.AccountServer:GetPlayerId() and RecruitConfig.Loc.Self or RecruitConfig.Loc.InMyTeam)

        --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
        if self._wtPlatformIcon then
            local platID = playerSimpleInfo.plat

            local platIconPath = Module.Friend:GetPlatformIconPath(platID, false, false)
            if platIconPath then
                self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)

                -- Set icon color to pure white if this player is from ps5 platform too.
                local linearColor = Facade.ColorManager:GetLinerColor("LightPositive")
                if IsPS5() and platID == PlatIDType.Plat_Playstation then
                    linearColor = FLinearColor(1,1,1,1)
                end
                self._wtPlatformIcon:SetColorAndOpacity(linearColor)

                self._wtPlatformIcon:SelfHitTestInvisible()
            else
                self._wtPlatformIcon:Collapsed()
            end
        end
        --- using Platform Online ID as player name.
        if IsPS5() then
            local callback = function(onlineID)
                self._wtName:SetText(onlineID)
            end
            Module.Social:AsyncGetPS5OnlineIdByUID(playerSimpleInfo.player_id, callback, self)
        end
        --- END MODIFICATION

        -- 隐身下这里等同于离线
        if playerSimpleInfo.player_id == Server.AccountServer:GetPlayerId() and Module.Friend:GetSelfLoginInvisible() then
            self._wtMemberType:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C012"))
            self._wtMemberType:SetText(Module.Friend.Config.SimpleInvisibleStr)
            self._wtPlatformIcon:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C012"))
        else
            self._wtMemberType:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C005"))
            self._wtPlatformIcon:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C005"))
        end
    end 
end

--- BEGIN MODIFICATION @ VIRTUOS
function RecruitTeamMemberInfoItem:SetTeamMemberPanelFocusable(_bIsFocusable)
    if not IsHD() then
        return 
    end
    self._wtTeamMemberInfoPanel:SetCppValue("IsFocusable", _bIsFocusable)
end
--- END MODIFICATION

return RecruitTeamMemberInfoItem
