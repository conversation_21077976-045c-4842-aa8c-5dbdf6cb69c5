----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local CommonConfig = Module.CommonWidget.Config
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------

---@class FriendBox : LuaUIBaseView
local FriendBox = ui("FriendBox")

require("DFM.YxFramework.Managers.Resource.Util.ResImageUtil")
local EButtonState = import "EButtonState"

function FriendBox:Ctor()
    loginfo("FriendBox:Ctor")
    self._wtPlayerNameText = self:Wnd("wtPlayerNameText", UITextBlock)
    self._wtIntimacyText = self:Wnd("wtIntimacyText", UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtPlayerState = self:Wnd("wtPlayerState", UITextBlock)
    self._wtplatIcon = self:Wnd("wtplatIcon", UIImage)

    self._wtTeamAdd = self:Wnd("wtTeamAdd", UIWidgetBase)
    self._wtTeamAdd:Event("OnClicked", self._OnBtnFriendAddTeam, self)

    self._wtTeamFriend = self:Wnd("TeamBtn", UIWidgetBase)
    self._wtTeamFriend:Event("OnClicked", self._OnBtnSendFriendOnTeam, self)

    self._wtInviteLoadImage = self:Wnd("Image", UIWidgetBase)

    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)

    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)

    self._wtMilitary = self:Wnd("WBP_Friend_BrandAvatar", UIWidgetBase)

    self._wtPrivilege = self:Wnd("WBP_Firend_PrivilegeButton", UIWidgetBase)
    self._wtPrivilegeBtn = self._wtPrivilege:Wnd("PrivilegeBtn", UIButton)
    self._wtPrivilegeBtn:Event("OnClicked", self._OnClickPrivilege,self)

    self._wtFavPanel = self:Wnd("Image_6634", UIWidgetBase)
    self._wtOffLineImage = self:Wnd("Masks", UIWidgetBase)
    self._wtPraise = self:Wnd("WBP_TeamMatesPraise", UIWidgetBase)
    self._wtPraise:Event("OnClicked", self._OnPraiseClick, self)

    self._friendInfo = {}

    self:AddLuaEvent(Module.Friend.Config.Events.evtFriendSuccessfullySentAnInvitationToFriends, self.RefreshInviteColdDownTime, self)
end

function FriendBox:ShowUI(data, panelType)
    self._panelType = panelType
    self._isOpenFriend = panelType == 2 or panelType == 3
    self._friendInfo = data
    local playerName = self._friendInfo.nick_name
    loginfo("FriendData")
    logtable(self._friendInfo,true)
    if playerName then
        self._wtPlayerNameText:SetText(playerName)
    end
    self._wtIntimacyText:SetText(self._friendInfo.favorability)
    self._wtplatIcon:Visible()
    self._wtMilitary:SetMilitary(data.military_tag)
    FriendLogic.SetPlayerStateCode(self._wtPlayerState, self._friendInfo, self._wtplatIcon)
    self._wtFavPanel:Visible()

    -- self._playIconInfo = {
    --     pic_url = self._friendInfo.pic_url,
    --     player_id = self._friendInfo.player_id,
    --     level = 0,
    --     nick_name = self._friendInfo.nick_name,
    --     military_tag = self._friendInfo.military_tag,
    -- }

    self._playIconInfo = self._friendInfo

    --- BEGIN MODIFICATION @ VIRTUOS
    self._wtplatIcon:AsyncSetImagePath(Module.Friend:GetPlatformIconPath(self._friendInfo.plat, false, false), false)
    --- END MODIFICATION

    self:RefreshInviteColdDownTime(self._friendInfo.player_id)

    local isSol = Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse
    if isSol then
        self._playIconInfo.level = self._friendInfo.season_level
        self._playIconInfo.rank_attended = self._friendInfo.sol_attended
        self._playIconInfo.rank_score = self._friendInfo.sol_rank_score
    else
        self._playIconInfo.level = self._friendInfo.level
        self._playIconInfo.rank_attended = self._friendInfo.mp_attended
        self._playIconInfo.rank_score = self._friendInfo.mp_rank_score
    end

    if self._friendInfo.IsPraise then
        self._wtPraise:SetBtnEnable(false)
    else
        self._wtPraise:SetBtnEnable(true)
    end
    
    --- Discord不支持点赞
    if panelType == 3 then
        self._wtPraise:Collapsed()
    end

    FriendLogic.InitFriendRank(self._wtRankIcon, self._wtRankDivision, self._friendInfo)

    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.Chat,
        HeadButtonType.Report,
    }

    if FriendLogic.IsTheSamePlatformWithMe(self._friendInfo) then
        table.insert(btnTbl, HeadButtonType.InviteAppTeam)
    end

    if self._isOpenFriend then
        self._wtFavPanel:Collapsed()
        self._wtIntimacyText:Collapsed()
    else
        self._wtFavPanel:Visible()
        self._wtIntimacyText:Visible()
        table.insert(btnTbl, HeadButtonType.DelFriend)
    end

    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl)
    if self._friendInfo.remarks ~= "" then
        self._wtPlayerNameText:SetText(string.format(Module.Friend.Config.QQFriend, self._friendInfo.remarks, self._friendInfo.nick_name))
    else
        if self._friendInfo.friend_type == FriendType.OpenGameFriend or self._panelType == 3 then   -- Discord好友特殊处理
            self._wtPlayerNameText:SetText(string.format(Module.Friend.Config.QQFriend, self._friendInfo.open_nick_name, self._friendInfo.nick_name))
        elseif self._friendInfo.friend_type == FriendType.OpenFriend then
            self._wtPlayerNameText:SetText(string.format(Module.Friend.Config.QQFriend, self._friendInfo.open_nick_name, self._friendInfo.nick_name))
        end
    end
    local FriendTeamId = self._friendInfo.team_id
    local SelfFriendId = Server.TeamServer:GetTeamId()
    local state = Module.Social:GetOnlinePlayerStateFromStateCode(self._friendInfo.state)

    if state == GlobalPlayerStateEnums.EPlayerState_InMatch then
        self._wtTeamAdd:Visible()
    else
        self._wtTeamAdd:Collapsed()
    end
    if Module.Friend:IsFriendOffLineOrInvisible(state, self._friendInfo.player_id) then
        self._wtplatIcon:Collapsed()
        self._wtOffLineImage:HitTestInvisible()
    else
        self._wtOffLineImage:Collapsed()
    end

    self._wtPrivilege:Visible()
    if self._friendInfo.game_center == GameCenterType.GameCenter_None then
        self._wtPrivilege:Collapsed()
    elseif self._friendInfo.game_center == GameCenterType.GameCenter_QQ and Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.QQPrivilegeFriendsInterfaceDisplay) == EFirstLockResult.Open then
        self._wtPrivilege:SetPintTaiType(self._friendInfo.game_center)
    elseif self._friendInfo.game_center == GameCenterType.GameCenter_WX and Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.WeChatPrivilegeFriendsInterfaceDisplay) == EFirstLockResult.Open then
        self._wtPrivilege:SetPintTaiType(self._friendInfo.game_center)
    else
        self._wtPrivilege:Collapsed()
    end

    -- if state == GlobalPlayerStateEnums.EPlayerState_Offline or state == GlobalPlayerStateEnums.EPlayerState_InMatch or 
    -- (FriendTeamId ~= 0 and SelfFriendId ~= 0 and FriendTeamId == SelfFriendId) then
    --     self._wtTeamFriend:Collapsed()
    -- else
    --     self._wtTeamFriend:Visible()
    --     local bInvite = Module.Social:IsInvite(self._friendInfo)
    --     local teamImage = bInvite and CommonConfig.InviteTeamBtnImage or CommonConfig.JoinTeamBtnImage
    --     self._wtTeamFriend:AsyncSetImageIconPathAllState(teamImage)
    -- end

    -- azhengzheng:覆写旧逻辑，新增预约功能
    if (not FriendLogic.IsTheSamePlatformWithMe(self._friendInfo)) or Module.Friend:IsFriendOffLineOrInvisible(state, self._friendInfo.player_id) or (FriendTeamId ~= 0 and SelfFriendId ~= 0 and FriendTeamId == SelfFriendId) then
        self._wtTeamFriend:Collapsed()
    else
        self._wtTeamFriend:Visible()

        if state == GlobalPlayerStateEnums.EPlayerState_InMatch then
            self._wtTeamFriend:AsyncSetImageIconPathAllState(CommonConfig.ReservationTeamUpImg)
        else
            if Module.Social:IsInvite(self._friendInfo) then
                self._wtTeamFriend:AsyncSetImageIconPathAllState(CommonConfig.InviteTeamBtnImage)
            else
                self._wtTeamFriend:AsyncSetImageIconPathAllState(CommonConfig.JoinTeamBtnImage)
            end
        end

        local funCallBack = function(loadCaller, imageAsset, bAutoResize)
            self._wtTeamFriend:SetImageIconState(imageAsset, EButtonState.Disabled)
        end

        ResImageUtil.AsyncLoadImgObjByPath(CommonConfig.TeamUpFinishImg, true, funCallBack, self)
    end
end

function FriendBox:OnClose()
    self:RemoveAllLuaEvent()
end

function FriendBox:_OnBtnChatFriendClick()
    Module.Chat:ChatWithPlayer(self._friendInfo)
end

function FriendBox:_OnBtnFriendAddTeam()
    Module.Chat:AppointFriend(self._friendInfo.player_id)
end

function FriendBox:_OnPraiseClick()
    self._wtPraise:SetBtnEnable(false)
    Server.FriendServer:UpdatePraiseData(self._friendInfo.player_id)
    if self._isOpenFriend then
        Server.FriendServer:PlayerPraise(self._friendInfo.player_id, nil, 3)
    else
        Server.FriendServer:PlayerPraise(self._friendInfo.player_id, nil, 2)
    end

end

function FriendBox:_OnClickPrivilege()
    if self._friendInfo.game_center == GameCenterType.GameCenter_WX then
        Module.CommonTips:ShowSimpleTip(Module.Friend.Config.FriendWxTips)
    elseif self._friendInfo.game_center == GameCenterType.GameCenter_QQ then
        Module.OpActQQPrivilege:OpenQQPrivilegePage()
    end
end

function FriendBox:RefreshInviteColdDownTime(playerId)
    if not self._friendInfo or not self._friendInfo.player_id or self._friendInfo.player_id ~= playerId then
        return
    end

    self:_SetColdTimeImage(Module.Social:GetCoolDownPlayerTime(playerId) > 0)
end

function FriendBox:_SetColdTimeImage(isCool)
    if isCool then
        self._wtTeamFriend:SetBtnEnable(false)
    else
        self._wtTeamFriend:SetBtnEnable(true)
    end
end

function FriendBox:_OnBtnSendFriendOnTeam()
    if Server.MatchServer:GetIsMatching() then
        Module.CommonTips:ShowSimpleTip(Module.Social.Config.Loc.InMatching)
        return
    end

    -- 预约跟旧的组队逻辑是同一套
    Module.Social:TeamInvite(self._friendInfo,TeamInviteSource.FromFriend,true)
    self:_SetColdTimeImage(true)
end

return FriendBox