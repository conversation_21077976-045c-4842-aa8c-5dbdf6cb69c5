----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class PlayerReturnSignInItem
local PlayerReturnSignInItem = ui("PlayerReturnSignInItem")

local SubUIInfo     = require "DFM.Business.DataStruct.UIDataStruct.SubUIInfo"
local ActivityLogic  = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

function PlayerReturnSignInItem:Ctor()
    self._wtDayCountLabel   = self:Wnd("DFTextBlock_155"    , UITextBlock)
    self._wtRewardsList     = self:Wnd("DFHorizontalBox_57" , UIWidgetBase)
    self._wtItemWidgets     = {} ---@type SubUIInfo[]

    self._wtCommonHover = self:Wnd("WBP_CommonHoverBg", UIWidgetBase)
    self._wtCommonHover:Collapsed()
end

---@class PlayerReturnSignInItemState
---@field dayLabel      string
---@field items         ItemBase[]
---@field signInState   0未签到  1已签到  2已领取
---@field callback      function 点击签到回调, 为空则是查看详情

---@param state PlayerReturnSignInItemState
function PlayerReturnSignInItem:SetState(state)
    self._state = state

    self:RemoveItems()

    local iconSize = IsHD() and 256 or 192

    self._wtDayCountLabel:SetText(state.dayLabel)

    for _, item in ipairs(state.items) do
        local widget = SubUIInfo.AddSubUI(self, UIName2ID.IVCommonItemTemplate, self._wtRewardsList)
        table.insert(self._wtItemWidgets, widget)

        local uiIns = widget:GetIns() ---@type IVCommonItemTemplate
        uiIns:SetSize(iconSize, iconSize)
        uiIns:InitItem(item)

        uiIns:Visible()
    end

    self:SetClaimed(state.signInState == 2)
    self:SetHighlighted(state.signInState == 1)

    self:SetSignInCallback(state.callback)
end

-- 设置是否已领取
function PlayerReturnSignInItem:SetClaimed(claimed)
    if not claimed then return end
    for _, itemWidget in ipairs(self._wtItemWidgets) do
        itemWidget:GetIns():SetAlreadyGetState()
    end
end

-- 设置物品点击后的行为（签到）
function PlayerReturnSignInItem:SetSignInCallback(fCallback)
    for _, itemWidget in ipairs(self._wtItemWidgets) do
        if fCallback then
            itemWidget:GetIns():BindCustomOnClicked(fCallback)
        else
            itemWidget:GetIns():BindDefaultOnClicked(true)
        end
    end
end

-- 设置高亮状态（当天签到的那一个应当高亮）
function PlayerReturnSignInItem:SetHighlighted(bState)
    -- 蓝图里面把高亮状态命名为type了
    local type = bState and 1 or 0
    self:SetType(type)
    for _, itemWidget in ipairs(self._wtItemWidgets) do
        if bState then
            itemWidget:GetIns():PlayIVAnimation("WBP_CommonItemTemplate_in_special_01",0)
        else
            itemWidget:GetIns():StopIVAnimation("WBP_CommonItemTemplate_in_special_01")
        end
    end
end

function PlayerReturnSignInItem:RemoveItems()
    for _, itemWidget in pairs(self._wtItemWidgets) do
        itemWidget:Remove()
    end
    self._wtItemWidgets = {}
end

---@return IVCommonItemTemplate
function PlayerReturnSignInItem:GetFirstItem()
    if self._wtItemWidgets and self._wtItemWidgets[1] then
        local widget = self._wtItemWidgets[1]:GetIns()
        return widget
    end
end

return PlayerReturnSignInItem
