----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonLineHeadEntry : LuaUIBaseView

local FAnchors = import "Anchors"

local QuestSeasonLineHeadEntry = ui("QuestSeasonLineHeadEntry")

function QuestSeasonLineHeadEntry:Ctor()   
    self._wtGroupTypeText = self:Wnd("DFTextBlock_44", UITextBlock)
    self._wtGroupNameText = self:Wnd("DFTextBlock_101", UITextBlock)
    self._wtStarText = self:Wnd("DFTextBlock_182", UITextBlock)
    self._wtStarIconList = {
        self:Wnd("DFImage_7", UIWidgetBase),
        self:Wnd("DFImage_2", UIWidgetBase),
        self:Wnd("DFImage_3", UIWidgetBase),        
        self:Wnd("DFImage_4", UIWidgetBase),
        self:Wnd("DFImage_5", UIWidgetBase),                
    }

    self._wtLockedPanel = self:Wnd("DFCanvas_Locked", UIWidgetBase)
    self._wtLockedCountdownText = self:Wnd("DFTextBlock_72", UITextBlock)

    self._wtDecoline = self:Wnd("DFImage_7", UIWidgetBase)
    self._wtDecoIcon = self:Wnd("DFImage_94", UIWidgetBase)

    self._groupInfo = nil
    self._timerHandle = nil
    self._lineInfo = nil
    self._slot = nil
    self._bShowDecoLine = false
    self._bFinishUpdatePosition = false
end

function QuestSeasonLineHeadEntry:OnInitExtraData(lineInfo, stageID, groupID, bShowDecoLine)
    self._groupInfo = lineInfo:GetGroupInfo(groupID)
    self._lineInfo = lineInfo
    self._bShowDecoLine = bShowDecoLine

    if lineInfo:IsMainGroup(stageID, groupID) then
        self._wtGroupTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonMain)
        self:SetType(0)
    else
        local EQuestSeasonType = Module.Quest.Config.EQuestSeasonType
        if self._groupInfo.type == EQuestSeasonType.Hard then
            self._wtGroupTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonBranchHard)
        elseif self._groupInfo.type == EQuestSeasonType.Easy then
            self._wtGroupTypeText:SetText(Module.Quest.Config.Loc.QuestTypeLocSeasonBranchEasy)
        end
        self:SetType(1)
    end

    self._wtGroupNameText:SetText(self._groupInfo.name)
    
    if self._groupInfo.rewardStar <= 5 then
        for i = 1, self._groupInfo.rewardStar, 1 do
            self._wtStarIconList[i]:SelfHitTestInvisible()
        end
        for i = self._groupInfo.rewardStar + 1, 5, 1 do
            self._wtStarIconList[i]:Collapsed()
        end
        self._wtStarText:Collapsed()
    else
        self._wtStarIconList[1]:SelfHitTestInvisible()
        for i = 2, 5 do
            self._wtStarIconList[i]:Collapsed()
        end
        self._wtStarText:SetText(tostring(self._groupInfo.rewardStar))
        self._wtStarText:SelfHitTestInvisible()
    end    
    
    if lineInfo:IsUnlockByGroupId(groupID) then
        self._wtLockedPanel:Collapsed()
    else
        self._wtLockedPanel:Visible()

        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end

        self:_UpdateRemainCountTime()

        self._timerHandle = Timer:NewIns(5, 0)
        self._timerHandle:AddListener(self._UpdateRemainCountTime, self)
        self._timerHandle:Start()
    
    end
    

    if bShowDecoLine == true then
        self._wtDecoline:SelfHitTestInvisible()
    else
        self._wtDecoline:Collapsed()
    end

end

function OnHide()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end    
end

function QuestSeasonLineHeadEntry:_UpdateRemainCountTime()
    
    local remainTime = self._lineInfo:GetUnlockTimeByGroupId(self._groupInfo.groupID)

    if remainTime <= 0 then 
        self._wtLockedPanel:Collapsed()
        
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end

        return
    end
    
    local str = ""
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remainTime)
    if day > 0 then
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min)
    elseif hour > 0 then
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min)
    elseif min > 0 then
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min)
    else
        str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1)        
    end

    self._wtLockedCountdownText:Visible()
    self._wtLockedCountdownText:SetText(str)
end

function QuestSeasonLineHeadEntry:SetPosition(lastGroup, bIsAbove)

    self._slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self)
    self._slot:SetAutoSize(true)
    
    local anchor = FAnchors()
    anchor.Minimum = FVector2D(0, 0.5)
    anchor.Maximum = FVector2D(0, 0.5)

    self._slot:SetAnchors(anchor)
    self._slot:SetAlignment(FVector2D(0, 0.5))
    self._slot:SetAutoSize(true)
    
    if lastGroup == nil then
        self._slot:SetPosition(FVector2D(22, 0))
        self._bFinishUpdatePosition = true
        Module.Quest.Field:UpdateQuestLineContentSize({X = 22, Y = 0})
    else
        self._slot:SetAnchors(anchor)
        local function SetPositionCallback()
            if lastGroup:GetCachedGeometry():GetLocalSize().X == 0 or not lastGroup._bFinishUpdatePosition then
                Timer.DelayCall(0, SetPositionCallback, self)
            else
                local pos = FVector2D(0,0)
                if bIsAbove then
                    local Yoffset = lastGroup:GetCachedGeometry():GetLocalSize().Y
                    local decoSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtDecoline)
                    Yoffset = Yoffset + decoSlot:GetPosition().Y
                    if self._bShowDecoLine then
                        Yoffset = Yoffset + decoSlot:GetPosition().Y + decoSlot:GetSize().Y
                    end
                    pos = lastGroup._slot:GetPosition() - FVector2D(0, Yoffset)
                    self._slot:SetPosition(lastGroup._slot:GetPosition() - FVector2D(0, Yoffset))
                    self._bFinishUpdatePosition = true
                else
                    local Yoffset = lastGroup:GetCachedGeometry():GetLocalSize().Y
                    local decoSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(lastGroup._wtDecoline)
                    Yoffset = Yoffset + decoSlot:GetPosition().Y
                    if lastGroup._bShowDecoLine then
                        Yoffset = Yoffset + decoSlot:GetPosition().Y + decoSlot:GetSize().Y
                    end
                    pos = lastGroup._slot:GetPosition() + FVector2D(0, Yoffset)
                    self._slot:SetPosition(lastGroup._slot:GetPosition() + FVector2D(0, Yoffset))
                    self._bFinishUpdatePosition = true
                end
                Module.Quest.Field:UpdateQuestLineContentSize({X = pos.X, Y = pos.Y})
            end
        end
        SetPositionCallback()
    end
end

return QuestSeasonLineHeadEntry