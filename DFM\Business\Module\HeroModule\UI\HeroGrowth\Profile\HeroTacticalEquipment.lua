----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------




local HeroLogic = require "DFM.Business.Module.HeroModule.Logic.HeroLogic"
local HeroConfig= require "DFM.Business.Module.HeroModule.HeroConfig"
local UGPInputHelper = import "GPInputHelper"
local UWidgetBlueprintLibrary = import "WidgetBlueprintLibrary"
local UKismetInputLibrary = import "KismetInputLibrary"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local HeroArchiveInformation = require "DFM.Business.Module.HeroModule.UI.HeroGrowth.Profile.HeroArchiveInformation"
local EGPUINavWidgetFocusedAction   = import    ("EGPUINavWidgetFocusedAction")
local EUINavigation = import("EUINavigation")
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--- END MODIFICATION

local EArchiveTye = {
    Tactic = 1,
    Psychology = 2,
    Action = 3
}

---@class HeroTacticalEquipment : LuaUIBaseView
local HeroTacticalEquipment = ui("HeroTacticalEquipment")

function HeroTacticalEquipment:Ctor()
    self._wtProfileList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_70", self._OnGetProfileCount, self._OnProcessProfileListItemWidget)
    self._wtDetailInfoContent = self:Wnd("WBP_HeroArchiveInformation_C_5", HeroArchiveInformation)
    self._wtHotzone = self:Wnd("Hotzone", UIWidgetBase)
    self._wtEquipmentCarouselPanel = self:Wnd("DFHorizontal_Box", UIWidgetBase)

    -- self._wtLeftBtn = self:Wnd("DFButton_103", UIButton)
    -- self._wtLeftBtn:Event("OnClicked", self._GoBackward, self)

    self._wtCarousel1 = self:Wnd("WBP_DFCommonButtonCarousel_3", UIWidgetBase)
    self._wtCarouselBtn1 = self._wtCarousel1:Wnd("DFButton_Icon", DFCommonButtonOnly)

    self._wtCarousel2 = self:Wnd("WBP_DFCommonButtonCarousel", UIWidgetBase)
    self._wtCarouselBtn2 = self._wtCarousel2:Wnd("DFButton_Icon", DFCommonButtonOnly)

    self._wtCarousel3 = self:Wnd("WBP_DFCommonButtonCarousel_1", UIWidgetBase)
    self._wtCarouselBtn3 = self._wtCarousel3:Wnd("DFButton_Icon", DFCommonButtonOnly)
    self._wtBGPanelMobile = self:Wnd("WBP_Common_ScaleBg_Mobile", UIWidgetBase)
    self._wtBGCDNImg = self:Wnd("DFCDNImage_85", DFCDNImage)
    self._wtBGPanelHD = self:Wnd("WBP_Common_ScaleBg_PC", UIWidgetBase)
    self._wtBGImg = self:Wnd("DFImage_5", UIImage)

    -- self._wtRightBtn = self:Wnd("DFButton_135", UIButton)
    -- self._wtRightBtn:Event("OnClicked", self._GoForward, self)

    self.carouselList = {
        self._wtCarousel1,
        self._wtCarousel2,
        self._wtCarousel3
    }

    self.carouselBtnList = {
        self._wtCarouselBtn1,
        self._wtCarouselBtn2,
        self._wtCarouselBtn3,
    }
    self._fileListGeometryList = {}
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, HeroConfig.Loc.HeroLineTitle)
    if DFHD_LUA == 1 then
        self:InjectLua()
    end

    -- BEGIN MODIFICATION @ VIRTUOS
    self._wtDetailInfoScrollBox = self._wtDetailInfoContent:Wnd("DFScrollBox_0", UIWidgetBase)
    -- END MODIFICATION
end

function HeroTacticalEquipment:OnInitExtraData(heroId)
    self._heroId = setdefault(heroId, Module.Hero.Field:GetCurSelectHeroId())
    self.reddotList = {}
    self._fileListGeometryList = {}
    self:InitArchiveTypeList()
    self:InitArchiveType2InfoList()
    self._curArchivePageId = self:GetFirstArchiveIdByType(self.curSelectedType)
    self._wtProfileList:RefreshAllItems()
    self:ShowArchiveViewById(self._curArchivePageId, true)
end

function HeroTacticalEquipment:InitArchiveTypeList()
    self._archiveTypeList = {}
    local archiveInfoList = HeroLogic.GetArchiveInfoListByHeroId(self._heroId)
    if #archiveInfoList > 0 then
        for _, archiveInfo in pairs(archiveInfoList) do
            if table.keyof(self._archiveTypeList, archiveInfo.ArchiveType) == 0 then
                table.insert(self._archiveTypeList, archiveInfo.ArchiveType)
            end
        end
        table.sort(self._archiveTypeList)
        self.curSelectedType = self._archiveTypeList[1]
    end
end

function HeroTacticalEquipment:InitArchiveType2InfoList()
    self._mapUnlockedArchiveType2InfoList = {}
    local heroLineData = Server.HeroServer:GetHeroLineData(self._heroId)
    if heroLineData then
        for _, archive in pairs(heroLineData.archives) do
            self._mapUnlockedArchiveType2InfoList[archive.archive_type] = {}
            for _, id in pairs(archive.archive_ids) do
                local info = HeroLogic.GetArchiveInfoByID(id)
                if info then
                    table.insert(self._mapUnlockedArchiveType2InfoList[archive.archive_type], info)
                end
            end
        end
    end
end


function HeroTacticalEquipment:GetFirstArchiveIdByType(type)
    if self._mapUnlockedArchiveType2InfoList and self._mapUnlockedArchiveType2InfoList[type] and self._mapUnlockedArchiveType2InfoList[type][1] then
        return self._mapUnlockedArchiveType2InfoList[type][1].ArchiveID
    end
    return -1
end

function HeroTacticalEquipment:OnShowBegin()
    -- self:AddLuaEvent(Server.HeroServer.Events.evtHeroArchiveReddotsChanged, self._UpdateReddotVisibility, self)
    local gameInst = GetGameInstance()
    self._moveHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseMoveEvent:Add(self._OnHandleMouseButtonMoveEvent, self)
    self._downHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Add(self.OnHandleMouseButtonDownEvent, self)
    self._upHandle = UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self.OnHandleMouseButtonUpEvent, self)
    --默认展示第一部分
    -- self:ShowArchiveViewById(self._curArchivePageId, true)

    --- BEGIN MODIFICATION @ VIRTUOS
    self:_InitializeGamepadFeature()
    --- END MODIFICATION
end

function HeroTacticalEquipment:OnShow()
end

function HeroTacticalEquipment:OnHideBegin()
    -- self:RemoveLuaEvent(Server.HeroServer.Events.evtHeroArchiveReddotsChanged, self._UpdateReddotVisibility)
    local gameInst = GetGameInstance()
    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseMoveEvent:Remove(self._OnHandleMouseButtonMoveEvent, self)
    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self.OnHandleMouseButtonDownEvent, self)
    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self.OnHandleMouseButtonUpEvent, self)
    self:RemoveCDNDownloadListener()

    --- BEGIN MODIFICATION @ VIRTUOS
    self:_DisableGamepadFeature()
    --- END MODIFICATION

end

function HeroTacticalEquipment:OnHide()
    -- self:RefreshPanel()
    --反注册黄点
    for _, reddotList in ipairs(self.reddotList) do
        Module.ReddotTrie:UnRegisterStaticReddotDot(reddotList)
    end
    self.reddotList = {}
end

function HeroTacticalEquipment:_OnGetProfileCount()
    self._archiveTypeList = setdefault(self._archiveTypeList, {})
    return #self._archiveTypeList
end

function HeroTacticalEquipment:_OnProcessProfileListItemWidget(position, itemWidget)
    -- local index = position + 1
    local archiveType = self._archiveTypeList[position]

    local btnCallback = CreateCallBack(function (self, archiveType)
        self:ShowArchiveViewById(self:GetFirstArchiveIdByType(archiveType))
        self.curSelectedType = archiveType
        Module.Hero.Config.Events.evtOnSelectedArchiveTypeChanged:Invoke(archiveType)
        -- self._wtProfileList:RefreshAllItems()
    end, self, archiveType)

    itemWidget:InitItem(self._heroId, archiveType)
    itemWidget:BindClickCallback(btnCallback)
    itemWidget:SetIsSelected(archiveType == self.curSelectedType)
    self._fileListGeometryList[position] = itemWidget and itemWidget:GetCachedGeometry() or nil
    --注册红点
    -- if not self.reddotList or not self.reddotList[archiveId] then
    --     self.reddotList[archiveId] = Module.ReddotTrie:CreateReddotIns(itemWidget, EReddotType.Strong)
    -- end
end

function HeroTacticalEquipment:ShowArchiveViewById(archiveId, bForceShow)
    if self._curArchivePageId ~= archiveId or bForceShow then
        self._curArchivePageId = archiveId
        local archiveType = self:GetArchiveTypeById(archiveId)
        if archiveType then
            if archiveType == EArchiveTye.Tactic then
                self._curEquipmentPageIndex = 1 --当前选中的装备id
                self._equipmentInfoList = self._mapUnlockedArchiveType2InfoList[EArchiveTye.Tactic]
                if #self._equipmentInfoList >= 2 then
                    for index, carousel in pairs(self.carouselList) do
                        if index <= #self._equipmentInfoList then
                            carousel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                            self.carouselBtnList[index]:Event("OnClicked", function(self)
                                self:_ShowEquipment(index)
                            end, self)
                            -- self.carouselBtnList[index]:SetCallback(function (self, bIsChecked)
                            --     self:_ShowEquipment(bIsChecked, index)
                            -- end, self)
                        else
                            carousel:SetVisibility(ESlateVisibility.Collapsed)
                        end
                    end
                    self._wtEquipmentCarouselPanel:Setvisibility(ESlateVisibility.SelfHitTestInvisible)
                else
                    self._wtEquipmentCarouselPanel:Setvisibility(ESlateVisibility.Collapsed)
                end
                self:RefreshEquipmentInfoView(self._curEquipmentPageIndex)

                LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.HeroLineArchiveTacticInfo)

            --判断archiveId是否属于 —— 战术心理评估界面
            elseif archiveType == EArchiveTye.Psychology then
                self._curEquipmentPageIndex = -1
                self._curArchivePageId = archiveId
                self._wtEquipmentCarouselPanel:Setvisibility(ESlateVisibility.Collapsed)
                self._wtDetailInfoContent:FreshView(self._curArchivePageId, self._heroId)

                LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.HeroLineArchiveMentalStateAssessment)

            --打开行动档案
            elseif archiveType == EArchiveTye.Action then
                self._curEquipmentPageIndex = -1
                self._wtEquipmentCarouselPanel:Setvisibility(ESlateVisibility.Collapsed)
                self._wtDetailInfoContent:FreshView(self._curArchivePageId, self._heroId)

                LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.HeroLineArchiveActionArchive)

            end
            -- 删除archiveType对应红点archiveType
            if self:GetIsArchiveUnlockNewByType(archiveType) then
                Server.HeroServer:UpdateGrowLineReddot(self._heroId, archiveType)
            end
            --设置bg
            local archiveInfo = HeroLogic.GetArchiveInfoByID(self._curArchivePageId)
            self:FreshArchiveCDNBGImg(archiveInfo)
        -- else
        end
    end
end

function HeroTacticalEquipment:FreshArchiveCDNBGImg(archiveInfo)
    archiveInfo = setdefault(archiveInfo, HeroLogic.GetArchiveInfoByID(self._curArchivePageId))
    --设置bg
    if archiveInfo then
        if DFHD_LUA == 0 then
            local BGCDNUrl = archiveInfo.ArchiveBGImgUrl and archiveInfo.ArchiveBGImgUrl or ""
            if BGCDNUrl and BGCDNUrl ~= "" then
                BGCDNUrl = string.format("Resource/Texture/Hero/%s" , BGCDNUrl)
                self.cdnBGUrl = BGCDNUrl
                -- if not Module.CDNIcon:IsDownloadInDisk(self.cdnBGUrl) and not self.bAddCDNDownloadListener then
                --     self._wtBGPanelHD:SelfHitTestInvisible()
                --     self._wtBGPanelMobile:Collapsed()
                --     self:AddCDNDownloadListener()
                -- else
                -- end
                self._wtBGPanelHD:Collapsed()
                self._wtBGPanelMobile:SelfHitTestInvisible()
                self._wtBGCDNImg:SetCDNImage(self.cdnBGUrl, false, Module.CDNIcon.Config.ECdnTagEnum.Hero)
            else
                logerror('HeroTacticalEquipment:FreshArchiveCDNBGImg BGCDNUrl = ',BGCDNUrl)
            end
        else
            self._wtBGPanelHD:SelfHitTestInvisible()
            self._wtBGPanelMobile:Collapsed()
            self._wtBGImg:AsyncSetImagePath(archiveInfo.ArchiveBGImg or "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/HeroLine/David/David_Portraits_01.David_Portraits_01'")
        end
    end
end

-- function HeroTacticalEquipment:AddCDNDownloadListener()
--     self:AddLuaEvent(Module.CDNIcon.Config.Events.evtOnCDNImgDownloaded, self.OnCDNImgDownloaded, self)
--     self.bAddCDNDownloadListener = true
-- end

-- function HeroTacticalEquipment:OnCDNImgDownloaded(Path)
--     if self.cdnBGUrl and self.cdnBGUrl == Path then
--         self._wtBGPanelHD:Collapsed()
--         self._wtBGPanelMobile:SelfHitTestInvisible()
--         self._wtBGCDNImg:SetCDNImage(self.cdnBGUrl, false, Module.CDNIcon.Config.ECdnTagEnum.Hero)
--         self:RemoveCDNDownloadListener()
--     end
-- end

function HeroTacticalEquipment:RemoveCDNDownloadListener()
    -- self:RemoveLuaEvent(Module.CDNIcon.Config.Events.evtOnCDNImgDownloaded, self.OnCDNImgDownloaded)
    -- self.bAddCDNDownloadListener = false
end

function HeroTacticalEquipment:GetArchiveTypeById(archiveId)
    if self._mapUnlockedArchiveType2InfoList then
        for archiveType, infoList in pairs(self._mapUnlockedArchiveType2InfoList) do
            for _, info in pairs(infoList) do
                if info.ArchiveID == archiveId then
                    return archiveType
                end
            end
        end
    end
    return nil
end

function HeroTacticalEquipment:GetIsArchiveUnlockNewByType(archiveType)
    local heroLineData = Server.HeroServer:GetHeroLineData(self._heroId)
    if heroLineData and heroLineData.archives then
        for _, archiveInfo in pairs(heroLineData.archives) do
            if archiveInfo.archive_type == archiveType then
                return archiveInfo.red_point
            end
        end
    end
    return false
end

function HeroTacticalEquipment:_ShowEquipment(index)
    if self._curEquipmentPageIndex ~= index then
        self._curEquipmentPageIndex = index
        self:RefreshEquipmentInfoView(self._curEquipmentPageIndex)
    end
end

function HeroTacticalEquipment:RefreshEquipmentInfoView(curEquipmentPageIndex)
    for idx, carouselBtn in ipairs(self.carouselList) do
        if idx == curEquipmentPageIndex then
            carouselBtn:SetStyle(1)
        else
            carouselBtn:SetStyle(0)
        end
    end

    -- 切换背景图片
    local equipmentInfo = self._equipmentInfoList[self._curEquipmentPageIndex]
    if equipmentInfo then
        self:FreshArchiveCDNBGImg(equipmentInfo)
        self._wtDetailInfoContent:FreshView(equipmentInfo.ArchiveID, self._heroId)
    end
end

-- function HeroTacticalEquipment:GenerateArchiveTypeList()
--     self._archiveTypeList = setdefault(self._archiveTypeList, {})
--     if self._mapUnlockedArchiveType2InfoList then
--         for _, archiveList in pairs(self._mapUnlockedArchiveType2InfoList) do
--             table.sort(archiveList, function(leftArchive, rightArchive)
--                 return leftArchive.ArchiveID < rightArchive.ArchiveID end)
--         end
--         for _, infoList in pairs(self._mapUnlockedArchiveType2InfoList) do
--             table.insert(self._archiveTypeList, infoList[1].ArchiveID)
--         end
--     end
-- end

function HeroTacticalEquipment:RefreshPanel()
    self._wtDetailInfoContent:FreshView(nil)
    for index, carousel in pairs(self.carouselList) do
        carousel:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--- 鼠标滚轮事件
function HeroTacticalEquipment:Imp_OnMouseWheel(inGeometry, inGestureEvent)
    if DFHD_LUA == 1 and self:IsNotOnOtherInteraction(inGestureEvent) and not WidgetUtil.IsGamepad() then
        local delta = UKismetInputLibrary.PointerEvent_GetWheelDelta(inGestureEvent)
        if delta > 0 then --向前滚动
            self:_GoBackward()
        else --向后滚动
            self:_GoForward()
        end
    end
end

function HeroTacticalEquipment:OnHandleMouseButtonDownEvent(inGestureEvent)
    if DFHD_LUA == 0 and UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) and self:IsNotOnOtherInteraction(inGestureEvent) then
        if inGestureEvent and inGestureEvent.GetScreenSpacePosition then
            self.startPos = inGestureEvent:GetScreenSpacePosition()
            loginfo('HeroTacticalEquipment bd ', self.startPos.X, self.startPos.Y)
        end
    end
end

--- 手指左右滑动事件
function HeroTacticalEquipment:OnHandleMouseButtonUpEvent(inGestureEvent)
    if DFHD_LUA == 0 then
        if inGestureEvent and inGestureEvent.GetScreenSpacePosition and self.startPos and self.startPos ~= FVector2D(0,0) then
            local screenDelta = inGestureEvent:GetScreenSpacePosition() - self.startPos
            loginfo('HeroTacticalEquipment up screenDelta ', screenDelta.X, screenDelta.Y)
                if math.abs(screenDelta.X) > math.abs(screenDelta.Y) and math.abs(screenDelta.X) > 50 then --向右滑动
                    if screenDelta.X > 0 then
                        self:_GoForward()
                    else --向左滑动
                        self:_GoBackward()
                    end
                end
            self.startPos = FVector2D(0,0)
        end
    end
end

--- 手指左右滑动事件
function HeroTacticalEquipment:_OnHandleMouseButtonMoveEvent(inGestureEvent)
    -- if DFHD_LUA == 0 and self:IsNotOnOtherInteraction(inGestureEvent) then
    --     if inGestureEvent and inGestureEvent.GetScreenSpacePosition and self.startPos and self.startPos ~= FVector2D(0,0) then
    --         local screenDelta = inGestureEvent:GetScreenSpacePosition() - self.startPos
    --         loginfo('HeroTacticalEquipment move screenDelta ', screenDelta.X, screenDelta.Y)
    --             if math.abs(screenDelta.X) > math.abs(screenDelta.Y) and math.abs(screenDelta.X) > 5 then --向右滑动
    --                 if screenDelta.X > 0 then
    --                     self:_GoForward()
    --                 else --向左滑动
    --                     self:_GoBackward()
    --                 end
    --             end
    --         self.startPos = FVector2D(0,0)
    --     end
    -- end
end

function HeroTacticalEquipment:IsNotOnOtherInteraction(inGestureEvent)
    local bInHeroFile = false
    local informationGeometry = self._wtDetailInfoContent and self._wtDetailInfoContent:GetCachedGeometry() or nil
    if self._fileListGeometryList and informationGeometry then
        for _, heroFileGeo in pairs(self._fileListGeometryList) do
            if heroFileGeo and heroFileGeo:IsUnderLocation(inGestureEvent:GetScreenSpacePosition()) then
                bInHeroFile = true
            end
        end
        if not informationGeometry:IsUnderLocation(inGestureEvent:GetScreenSpacePosition()) and not bInHeroFile then
            return true
        end
    end
    return false
end

function HeroTacticalEquipment:IsInHotZone(inGestureEvent)
    local EUIState = LuaUIBaseView.EUIState
    local isActive = not (self._curUIState == EUIState.HideBegin or self._curUIState == EUIState.Hide)
    local geometry = self._wtHotzone and self._wtHotzone:GetCachedGeometry() or nil
    local informationGeometry = self._wtDetailInfoContent and self._wtDetailInfoContent:GetCachedGeometry() or nil
    if geometry and isActive and self._fileListGeometryList and informationGeometry then
        local bInHeroFile = false
        for _, heroFileGeo in pairs(self._fileListGeometryList) do
            if heroFileGeo and heroFileGeo:IsUnderLocation(inGestureEvent:GetScreenSpacePosition()) then
                bInHeroFile = true
            end
        end
        if geometry:IsUnderLocation(inGestureEvent:GetScreenSpacePosition()) and not bInHeroFile and not informationGeometry:IsUnderLocation(inGestureEvent:GetScreenSpacePosition()) then
            return true
        else
            return false
        end
    end
    return false
end

function HeroTacticalEquipment:_GoBackward()
    if self._curEquipmentPageIndex ~= -1 then
        local lastPageIndex = self._curEquipmentPageIndex
        if self._curEquipmentPageIndex > 1 then
            self._curEquipmentPageIndex = self._curEquipmentPageIndex - 1
        elseif self._curEquipmentPageIndex == 1 then
            self._curEquipmentPageIndex = #self._equipmentInfoList
        end
        if lastPageIndex ~= self._curEquipmentPageIndex then
            self:RefreshEquipmentInfoView(self._curEquipmentPageIndex)
        end
    end
end

function HeroTacticalEquipment:_GoForward()
    if self._curEquipmentPageIndex ~= -1 then
        local lastPageIndex = self._curEquipmentPageIndex
        if self._curEquipmentPageIndex < #self._equipmentInfoList then
            self._curEquipmentPageIndex = self._curEquipmentPageIndex + 1
        elseif self._curEquipmentPageIndex == #self._equipmentInfoList then
            self._curEquipmentPageIndex = 1
        end
        if lastPageIndex ~= self._curEquipmentPageIndex then
            self:RefreshEquipmentInfoView(self._curEquipmentPageIndex)
        end
    end
end

function HeroTacticalEquipment:_GoForwardOnPressed()
    loginfo('HeroTacticalEquipment:_GoForwardOnPressed 按下')
end

function HeroTacticalEquipment:_GoBackwardOnPressed()
    loginfo('HeroTacticalEquipment:_GoBackwardOnPressed 按下')
end

function HeroTacticalEquipment:_GoForwardOnReleased()
    loginfo('HeroTacticalEquipment:_GoForwardOnReleased 松开')
end

function HeroTacticalEquipment:_GoBackwardOnReleased()
    loginfo('HeroTacticalEquipment:_GoBackwardOnReleased 松开')
end
-- function HeroTacticalEquipment:_UpdateReddotVisibility()
--     for archiveId, reddotWidget in pairs(self.reddotList) do
--         local flag = Server.HeroServer:GetArchiveReddotStateById(archiveId)
--         reddotWidget:SetReddotVisible(flag)
--     end
-- end

--- BEGIN MODIFICATION @ VIRTUOS
function HeroTacticalEquipment:_InitializeGamepadFeature()
    if not IsHD() then
        return
    end
    self._toggleNextTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab", EInputEvent.IE_Pressed, self._GoForward, self, EDisplayInputActionPriority.UI_Stack)
    self._togglePrevTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab", EInputEvent.IE_Pressed, self._GoBackward, self, EDisplayInputActionPriority.UI_Stack)

    -- 添加滚动输入支持
    self._ScrollDetailInfoHandle = self:AddAxisInputActionBinding("Common_Right_Y", self._ScrollDetailInfo, self, EDisplayInputActionPriority.UI_Stack)

    local summaryList = {
        {actionName = "SwitchPage_RightStick", func = nil, caller = self, bUIOnly = true, bHideIcon = false}
    }
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)

    -- Initialize Navigation
    if not self._profileListNavGroup then
        self._profileListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtProfileList, self, "Hittest")
        if self._profileListNavGroup then
            self._profileListNavGroup:AddNavWidgetToArray(self._wtProfileList)
            self._profileListNavGroup:SetScrollRecipient(self._wtProfileList)
            WidgetUtil.BindCustomBoundaryNavRule(self._profileListNavGroup, self._CustomBoundaryRule, self)
        end
    end

    WidgetUtil.TryFocusDefaultWidgetByGroup(self._profileListNavGroup)
end

function HeroTacticalEquipment:_CustomBoundaryRule(direction)
    if direction == EUINavigation.Down then
        self._wtProfileList:ScrollToIndex(1)
        local item = self._wtProfileList:GetItemByIndex(1)
        return item
    elseif direction == EUINavigation.Up then
        local lastIdx = #self._archiveTypeList
        self._wtProfileList:ScrollToIndex(lastIdx)
        local item = self._wtProfileList:GetItemByIndex(lastIdx)
        return item
    else
        return nil
    end
end

function HeroTacticalEquipment:_DisableGamepadFeature()
    if not IsHD() then
        return
    end

    self:RemoveInputActionBinding(self._toggleNextTabHandle)
    self:RemoveInputActionBinding(self._togglePrevTabHandle)

    if self._ScrollDetailInfoHandle then
        self:RemoveInputActionBinding(self._ScrollDetailInfoHandle)
        self._ScrollDetailInfoHandle = nil
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    -- Disable Navigation
    if self._profileListNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._profileListNavGroup = nil
    end
end

function HeroTacticalEquipment:_ScrollDetailInfo(inValue)
    if not IsHD() then
        return
    end
    
    if WidgetUtil.IsEquipZero(inValue) or self._wtDetailInfoScrollBox == nil then
        return
    end

    local currentOffset = self._wtDetailInfoScrollBox:GetScrollOffset()
    self._wtDetailInfoScrollBox:SetScrollOffset(math.max(0, currentOffset - 8 * (inValue / math.abs(inValue))), true, true, 15)
end
--- END MODIFICATION

return HeroTacticalEquipment