--cs_room.protoencode&decode functions.
function pb.pb_RoomBaseInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomBaseInfo) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __RoomName = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __RoomName ~= "" then tb.RoomName = __RoomName end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(3))
    local __State = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    local __IsPublic = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __IsPublic ~= false then tb.IsPublic = __IsPublic end
    local __Password = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __Password ~= "" then tb.Password = __Password end
    local __AllowEditMode = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __AllowEditMode ~= false then tb.AllowEditMode = __AllowEditMode end
    local __EnableEditMode = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __EnableEditMode ~= false then tb.EnableEditMode = __EnableEditMode end
    local __OwnerPlayerID = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __OwnerPlayerID ~= 0 then tb.OwnerPlayerID = __OwnerPlayerID end
    local __OwnerNickName = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __OwnerNickName ~= "" then tb.OwnerNickName = __OwnerNickName end
    local __CurPlayerNum = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __CurPlayerNum ~= 0 then tb.CurPlayerNum = __CurPlayerNum end
    local __MaxPlayerNum = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __MaxPlayerNum ~= 0 then tb.MaxPlayerNum = __MaxPlayerNum end
    local __TeamMemberSize = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __TeamMemberSize ~= 0 then tb.TeamMemberSize = __TeamMemberSize end
    local __TeamNum = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __TeamNum ~= 0 then tb.TeamNum = __TeamNum end
    local __CurOBNum = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __CurOBNum ~= 0 then tb.CurOBNum = __CurOBNum end
    local __MaxOBNum = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __MaxOBNum ~= 0 then tb.MaxOBNum = __MaxOBNum end
    local __CreateTime = decoder:geti64(17)
    if not PB_USE_DEFAULT_TABLE or __CreateTime ~= 0 then tb.CreateTime = __CreateTime end
    local __MatchSequence = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __MatchSequence ~= 0 then tb.MatchSequence = __MatchSequence end
    return tb
end

function pb.pb_RoomBaseInfoEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.RoomName) then    encoder:addstr(2, tb.RoomName)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(3))    end
    if(tb.State) then    encoder:addu32(4, tb.State)    end
    if(tb.IsPublic) then    encoder:addbool(5, tb.IsPublic)    end
    if(tb.Password) then    encoder:addstr(6, tb.Password)    end
    if(tb.AllowEditMode) then    encoder:addbool(7, tb.AllowEditMode)    end
    if(tb.EnableEditMode) then    encoder:addbool(8, tb.EnableEditMode)    end
    if(tb.OwnerPlayerID) then    encoder:addu64(9, tb.OwnerPlayerID)    end
    if(tb.OwnerNickName) then    encoder:addstr(10, tb.OwnerNickName)    end
    if(tb.CurPlayerNum) then    encoder:addu32(11, tb.CurPlayerNum)    end
    if(tb.MaxPlayerNum) then    encoder:addu32(12, tb.MaxPlayerNum)    end
    if(tb.TeamMemberSize) then    encoder:addu32(13, tb.TeamMemberSize)    end
    if(tb.TeamNum) then    encoder:addu32(14, tb.TeamNum)    end
    if(tb.CurOBNum) then    encoder:addu32(15, tb.CurOBNum)    end
    if(tb.MaxOBNum) then    encoder:addu32(16, tb.MaxOBNum)    end
    if(tb.CreateTime) then    encoder:addi64(17, tb.CreateTime)    end
    if(tb.MatchSequence) then    encoder:addu32(18, tb.MatchSequence)    end
end

function pb.pb_RoomTdmParamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomTdmParam) or {} 
    local __PlayerNum = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __PlayerNum ~= 0 then tb.PlayerNum = __PlayerNum end
    local __BeginVoteRatio = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __BeginVoteRatio ~= 0 then tb.BeginVoteRatio = __BeginVoteRatio end
    local __ConquestSectorVoteRatio = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __ConquestSectorVoteRatio ~= 0 then tb.ConquestSectorVoteRatio = __ConquestSectorVoteRatio end
    local __GameTimeRatio = decoder:getfloat(4)
    if not PB_USE_DEFAULT_TABLE or __GameTimeRatio ~= 0 then tb.GameTimeRatio = __GameTimeRatio end
    local __ReviveTimeRatio = decoder:getfloat(5)
    if not PB_USE_DEFAULT_TABLE or __ReviveTimeRatio ~= 0 then tb.ReviveTimeRatio = __ReviveTimeRatio end
    return tb
end

function pb.pb_RoomTdmParamEncode(tb, encoder)
    if(tb.PlayerNum) then    encoder:addu32(1, tb.PlayerNum)    end
    if(tb.BeginVoteRatio) then    encoder:addfloat(2, tb.BeginVoteRatio)    end
    if(tb.ConquestSectorVoteRatio) then    encoder:addfloat(3, tb.ConquestSectorVoteRatio)    end
    if(tb.GameTimeRatio) then    encoder:addfloat(4, tb.GameTimeRatio)    end
    if(tb.ReviveTimeRatio) then    encoder:addfloat(5, tb.ReviveTimeRatio)    end
end

function pb.pb_RoomParamKeyValueDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomParamKeyValue) or {} 
    local __ParamID = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __ParamID ~= 0 then tb.ParamID = __ParamID end
    local __ParamValue = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __ParamValue ~= "" then tb.ParamValue = __ParamValue end
    local __ParamType = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ParamType ~= 0 then tb.ParamType = __ParamType end
    return tb
end

function pb.pb_RoomParamKeyValueEncode(tb, encoder)
    if(tb.ParamID) then    encoder:addu32(1, tb.ParamID)    end
    if(tb.ParamValue) then    encoder:addstr(2, tb.ParamValue)    end
    if(tb.ParamType) then    encoder:addu32(3, tb.ParamType)    end
end

function pb.pb_RoomParamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomParam) or {} 
    local __TemplateID = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __TemplateID ~= 0 then tb.TemplateID = __TemplateID end
    tb.ParamList = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.ParamList[k] = pb.pb_RoomParamKeyValueDecode(v)
    end
    return tb
end

function pb.pb_RoomParamEncode(tb, encoder)
    if(tb.TemplateID) then    encoder:addu32(1, tb.TemplateID)    end
    if(tb.ParamList) then
        for i=1,#(tb.ParamList) do
            pb.pb_RoomParamKeyValueEncode(tb.ParamList[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_RoomMemberInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMemberInfo) or {} 
    local __PlayerID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __PlayerID ~= 0 then tb.PlayerID = __PlayerID end
    local __SeatID = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __SeatID ~= 0 then tb.SeatID = __SeatID end
    local __TeamID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __TeamName = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __TeamName ~= "" then tb.TeamName = __TeamName end
    local __IsTeamLeader = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __IsTeamLeader ~= false then tb.IsTeamLeader = __IsTeamLeader end
    local __IsObserver = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __IsObserver ~= false then tb.IsObserver = __IsObserver end
    local __ObserverSeatID = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __ObserverSeatID ~= 0 then tb.ObserverSeatID = __ObserverSeatID end
    local __State = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __State ~= 0 then tb.State = __State end
    local __EnterTime = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __EnterTime ~= 0 then tb.EnterTime = __EnterTime end
    local __TotalDepositPrice = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __TotalDepositPrice ~= 0 then tb.TotalDepositPrice = __TotalDepositPrice end
    local __LastKillPlayerNum = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __LastKillPlayerNum ~= 0 then tb.LastKillPlayerNum = __LastKillPlayerNum end
    local __LastBluePrintSpecialId = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __LastBluePrintSpecialId ~= 0 then tb.LastBluePrintSpecialId = __LastBluePrintSpecialId end
    local __LoseStreakCnt = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __LoseStreakCnt ~= 0 then tb.LoseStreakCnt = __LoseStreakCnt end
    local __IsSettle = decoder:getbool(14)
    if not PB_USE_DEFAULT_TABLE or __IsSettle ~= false then tb.IsSettle = __IsSettle end
    local __PlatId = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __PlatId ~= 0 then tb.PlatId = __PlatId end
    local __OfflineTime = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __OfflineTime ~= 0 then tb.OfflineTime = __OfflineTime end
    local __TotalKillPlayerNum = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __TotalKillPlayerNum ~= 0 then tb.TotalKillPlayerNum = __TotalKillPlayerNum end
    local __TotalAssistNum = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __TotalAssistNum ~= 0 then tb.TotalAssistNum = __TotalAssistNum end
    return tb
end

function pb.pb_RoomMemberInfoEncode(tb, encoder)
    if(tb.PlayerID) then    encoder:addu64(1, tb.PlayerID)    end
    if(tb.SeatID) then    encoder:addu32(2, tb.SeatID)    end
    if(tb.TeamID) then    encoder:addu32(3, tb.TeamID)    end
    if(tb.TeamName) then    encoder:addstr(4, tb.TeamName)    end
    if(tb.IsTeamLeader) then    encoder:addbool(5, tb.IsTeamLeader)    end
    if(tb.IsObserver) then    encoder:addbool(6, tb.IsObserver)    end
    if(tb.ObserverSeatID) then    encoder:addu32(7, tb.ObserverSeatID)    end
    if(tb.State) then    encoder:addu32(8, tb.State)    end
    if(tb.EnterTime) then    encoder:addi64(9, tb.EnterTime)    end
    if(tb.TotalDepositPrice) then    encoder:addi64(10, tb.TotalDepositPrice)    end
    if(tb.LastKillPlayerNum) then    encoder:addi32(11, tb.LastKillPlayerNum)    end
    if(tb.LastBluePrintSpecialId) then    encoder:addu64(12, tb.LastBluePrintSpecialId)    end
    if(tb.LoseStreakCnt) then    encoder:addi32(13, tb.LoseStreakCnt)    end
    if(tb.IsSettle) then    encoder:addbool(14, tb.IsSettle)    end
    if(tb.PlatId) then    encoder:addi32(15, tb.PlatId)    end
    if(tb.OfflineTime) then    encoder:addi64(16, tb.OfflineTime)    end
    if(tb.TotalKillPlayerNum) then    encoder:addi32(17, tb.TotalKillPlayerNum)    end
    if(tb.TotalAssistNum) then    encoder:addi32(18, tb.TotalAssistNum)    end
end

function pb.pb_HeroBanInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroBanInfo) or {} 
    local __HeroID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __HeroID ~= 0 then tb.HeroID = __HeroID end
    local __ChoosedNum = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __ChoosedNum ~= 0 then tb.ChoosedNum = __ChoosedNum end
    local __ChooseLimit = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ChooseLimit ~= 0 then tb.ChooseLimit = __ChooseLimit end
    return tb
end

function pb.pb_HeroBanInfoEncode(tb, encoder)
    if(tb.HeroID) then    encoder:addu64(1, tb.HeroID)    end
    if(tb.ChoosedNum) then    encoder:addu32(2, tb.ChoosedNum)    end
    if(tb.ChooseLimit) then    encoder:addu32(3, tb.ChooseLimit)    end
end

function pb.pb_RoomMemberBattleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMemberBattleInfo) or {} 
    local __PlayerID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __PlayerID ~= 0 then tb.PlayerID = __PlayerID end
    local __TotalKillPlayerNum = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __TotalKillPlayerNum ~= 0 then tb.TotalKillPlayerNum = __TotalKillPlayerNum end
    local __TotalAssistNum = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __TotalAssistNum ~= 0 then tb.TotalAssistNum = __TotalAssistNum end
    return tb
end

function pb.pb_RoomMemberBattleInfoEncode(tb, encoder)
    if(tb.PlayerID) then    encoder:addu64(1, tb.PlayerID)    end
    if(tb.TotalKillPlayerNum) then    encoder:addi32(2, tb.TotalKillPlayerNum)    end
    if(tb.TotalAssistNum) then    encoder:addi32(3, tb.TotalAssistNum)    end
end

function pb.pb_RoomTeamInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomTeamInfo) or {} 
    local __TeamID = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __TeamName = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __TeamName ~= "" then tb.TeamName = __TeamName end
    local __TotalDepositPrice = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __TotalDepositPrice ~= 0 then tb.TotalDepositPrice = __TotalDepositPrice end
    local __MantelBrickNum = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __MantelBrickNum ~= 0 then tb.MantelBrickNum = __MantelBrickNum end
    tb.HeroBanInfoList = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.HeroBanInfoList[k] = pb.pb_HeroBanInfoDecode(v)
    end
    local __LastManderlBrickRoomID = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __LastManderlBrickRoomID ~= 0 then tb.LastManderlBrickRoomID = __LastManderlBrickRoomID end
    local __SpawnPointType = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __SpawnPointType ~= 0 then tb.SpawnPointType = __SpawnPointType end
    return tb
end

function pb.pb_RoomTeamInfoEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu32(1, tb.TeamID)    end
    if(tb.TeamName) then    encoder:addstr(4, tb.TeamName)    end
    if(tb.TotalDepositPrice) then    encoder:addu64(5, tb.TotalDepositPrice)    end
    if(tb.MantelBrickNum) then    encoder:addu32(6, tb.MantelBrickNum)    end
    if(tb.HeroBanInfoList) then
        for i=1,#(tb.HeroBanInfoList) do
            pb.pb_HeroBanInfoEncode(tb.HeroBanInfoList[i], encoder:addsubmsg(7))
        end
    end
    if(tb.LastManderlBrickRoomID) then    encoder:addu64(8, tb.LastManderlBrickRoomID)    end
    if(tb.SpawnPointType) then    encoder:addi32(9, tb.SpawnPointType)    end
end

function pb.pb_RoomInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomInfo) or {} 
    tb.Base = pb.pb_RoomBaseInfoDecode(decoder:getsubmsg(1))
    tb.Players = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.Players[k] = pb.pb_RoomMemberInfoDecode(v)
    end
    tb.Observers = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.Observers[k] = pb.pb_RoomMemberInfoDecode(v)
    end
    tb.TdmParam = pb.pb_RoomTdmParamDecode(decoder:getsubmsg(4))
    tb.TeamInfo = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.TeamInfo[k] = pb.pb_RoomTeamInfoDecode(v)
    end
    tb.Param = pb.pb_RoomParamDecode(decoder:getsubmsg(6))
    return tb
end

function pb.pb_RoomInfoEncode(tb, encoder)
    if(tb.Base) then    pb.pb_RoomBaseInfoEncode(tb.Base, encoder:addsubmsg(1))    end
    if(tb.Players) then
        for i=1,#(tb.Players) do
            pb.pb_RoomMemberInfoEncode(tb.Players[i], encoder:addsubmsg(2))
        end
    end
    if(tb.Observers) then
        for i=1,#(tb.Observers) do
            pb.pb_RoomMemberInfoEncode(tb.Observers[i], encoder:addsubmsg(3))
        end
    end
    if(tb.TdmParam) then    pb.pb_RoomTdmParamEncode(tb.TdmParam, encoder:addsubmsg(4))    end
    if(tb.TeamInfo) then
        for i=1,#(tb.TeamInfo) do
            pb.pb_RoomTeamInfoEncode(tb.TeamInfo[i], encoder:addsubmsg(5))
        end
    end
    if(tb.Param) then    pb.pb_RoomParamEncode(tb.Param, encoder:addsubmsg(6))    end
end

function pb.pb_RoomMatchRoundMemberInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMatchRoundMemberInfo) or {} 
    local __MatchSequence = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __MatchSequence ~= 0 then tb.MatchSequence = __MatchSequence end
    local __MatchStartTime = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __MatchStartTime ~= 0 then tb.MatchStartTime = __MatchStartTime end
    local __TeamID = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __SeatID = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __SeatID ~= 0 then tb.SeatID = __SeatID end
    local __IsObserver = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __IsObserver ~= false then tb.IsObserver = __IsObserver end
    local __ObserverSeatID = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __ObserverSeatID ~= 0 then tb.ObserverSeatID = __ObserverSeatID end
    local __KillPlayerNum = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __KillPlayerNum ~= 0 then tb.KillPlayerNum = __KillPlayerNum end
    local __GameResult = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __GameResult ~= 0 then tb.GameResult = __GameResult end
    local __BluePrintSpecialId = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __BluePrintSpecialId ~= 0 then tb.BluePrintSpecialId = __BluePrintSpecialId end
    local __Salary = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __Salary ~= 0 then tb.Salary = __Salary end
    local __BrickBonus = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __BrickBonus ~= 0 then tb.BrickBonus = __BrickBonus end
    local __KillBonus = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __KillBonus ~= 0 then tb.KillBonus = __KillBonus end
    local __FailKillBonus = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __FailKillBonus ~= 0 then tb.FailKillBonus = __FailKillBonus end
    local __FailSupply = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __FailSupply ~= 0 then tb.FailSupply = __FailSupply end
    local __SupplyTime = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __SupplyTime ~= 0 then tb.SupplyTime = __SupplyTime end
    local __TotalDepositPrice = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __TotalDepositPrice ~= 0 then tb.TotalDepositPrice = __TotalDepositPrice end
    return tb
end

function pb.pb_RoomMatchRoundMemberInfoEncode(tb, encoder)
    if(tb.MatchSequence) then    encoder:addu32(1, tb.MatchSequence)    end
    if(tb.MatchStartTime) then    encoder:addi64(2, tb.MatchStartTime)    end
    if(tb.TeamID) then    encoder:addu32(3, tb.TeamID)    end
    if(tb.SeatID) then    encoder:addu32(4, tb.SeatID)    end
    if(tb.IsObserver) then    encoder:addbool(5, tb.IsObserver)    end
    if(tb.ObserverSeatID) then    encoder:addu32(6, tb.ObserverSeatID)    end
    if(tb.KillPlayerNum) then    encoder:addi32(7, tb.KillPlayerNum)    end
    if(tb.GameResult) then    encoder:addu32(8, tb.GameResult)    end
    if(tb.BluePrintSpecialId) then    encoder:addu64(9, tb.BluePrintSpecialId)    end
    if(tb.Salary) then    encoder:addi32(10, tb.Salary)    end
    if(tb.BrickBonus) then    encoder:addi32(11, tb.BrickBonus)    end
    if(tb.KillBonus) then    encoder:addi32(12, tb.KillBonus)    end
    if(tb.FailKillBonus) then    encoder:addi32(13, tb.FailKillBonus)    end
    if(tb.FailSupply) then    encoder:addi32(14, tb.FailSupply)    end
    if(tb.SupplyTime) then    encoder:addi64(15, tb.SupplyTime)    end
    if(tb.TotalDepositPrice) then    encoder:addi64(16, tb.TotalDepositPrice)    end
end

function pb.pb_RoomMatchMemberInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMatchMemberInfo) or {} 
    local __PlayerID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __PlayerID ~= 0 then tb.PlayerID = __PlayerID end
    tb.RoundMemberInfo = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.RoundMemberInfo[k] = pb.pb_RoomMatchRoundMemberInfoDecode(v)
    end
    local __TotalKillPlayerNum = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __TotalKillPlayerNum ~= 0 then tb.TotalKillPlayerNum = __TotalKillPlayerNum end
    local __TotalAssistNum = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __TotalAssistNum ~= 0 then tb.TotalAssistNum = __TotalAssistNum end
    return tb
end

function pb.pb_RoomMatchMemberInfoEncode(tb, encoder)
    if(tb.PlayerID) then    encoder:addu64(1, tb.PlayerID)    end
    if(tb.RoundMemberInfo) then
        for i=1,#(tb.RoundMemberInfo) do
            pb.pb_RoomMatchRoundMemberInfoEncode(tb.RoundMemberInfo[i], encoder:addsubmsg(2))
        end
    end
    if(tb.TotalKillPlayerNum) then    encoder:addi32(3, tb.TotalKillPlayerNum)    end
    if(tb.TotalAssistNum) then    encoder:addi32(4, tb.TotalAssistNum)    end
end

function pb.pb_RoomMatchRoundTeamInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMatchRoundTeamInfo) or {} 
    local __MatchSequence = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __MatchSequence ~= 0 then tb.MatchSequence = __MatchSequence end
    local __MatchStartTime = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __MatchStartTime ~= 0 then tb.MatchStartTime = __MatchStartTime end
    tb.Players = decoder:getu64ary(3)
    return tb
end

function pb.pb_RoomMatchRoundTeamInfoEncode(tb, encoder)
    if(tb.MatchSequence) then    encoder:addu32(1, tb.MatchSequence)    end
    if(tb.MatchStartTime) then    encoder:addi64(2, tb.MatchStartTime)    end
    if(tb.Players) then    encoder:addu64(3, tb.Players)    end
end

function pb.pb_RoomMatchTeamInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMatchTeamInfo) or {} 
    local __TeamID = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    tb.RoundTeamInfo = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.RoundTeamInfo[k] = pb.pb_RoomMatchRoundTeamInfoDecode(v)
    end
    return tb
end

function pb.pb_RoomMatchTeamInfoEncode(tb, encoder)
    if(tb.TeamID) then    encoder:addu32(1, tb.TeamID)    end
    if(tb.RoundTeamInfo) then
        for i=1,#(tb.RoundTeamInfo) do
            pb.pb_RoomMatchRoundTeamInfoEncode(tb.RoundTeamInfo[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSRoomCreateReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomCreateReq) or {} 
    local __RoomName = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __RoomName ~= "" then tb.RoomName = __RoomName end
    local __IsPublic = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsPublic ~= false then tb.IsPublic = __IsPublic end
    local __Password = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __Password ~= "" then tb.Password = __Password end
    local __AllowEditMode = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __AllowEditMode ~= false then tb.AllowEditMode = __AllowEditMode end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(5))
    tb.TdmParam = pb.pb_RoomTdmParamDecode(decoder:getsubmsg(6))
    local __MatchSequence = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __MatchSequence ~= 0 then tb.MatchSequence = __MatchSequence end
    tb.Param = pb.pb_RoomParamDecode(decoder:getsubmsg(8))
    return tb
end

function pb.pb_CSRoomCreateReqEncode(tb, encoder)
    if(tb.RoomName) then    encoder:addstr(1, tb.RoomName)    end
    if(tb.IsPublic) then    encoder:addbool(2, tb.IsPublic)    end
    if(tb.Password) then    encoder:addstr(3, tb.Password)    end
    if(tb.AllowEditMode) then    encoder:addbool(4, tb.AllowEditMode)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(5))    end
    if(tb.TdmParam) then    pb.pb_RoomTdmParamEncode(tb.TdmParam, encoder:addsubmsg(6))    end
    if(tb.MatchSequence) then    encoder:addu32(7, tb.MatchSequence)    end
    if(tb.Param) then    pb.pb_RoomParamEncode(tb.Param, encoder:addsubmsg(8))    end
end

function pb.pb_CSRoomCreateResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomCreateRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    local __room_default_name = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __room_default_name ~= "" then tb.room_default_name = __room_default_name end
    return tb
end

function pb.pb_CSRoomCreateResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
    if(tb.room_default_name) then    encoder:addstr(3, tb.room_default_name)    end
end

function pb.pb_CSRoomChangeSettingTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeSettingTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __RoomName = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __RoomName ~= "" then tb.RoomName = __RoomName end
    local __IsPublic = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsPublic ~= false then tb.IsPublic = __IsPublic end
    local __Password = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __Password ~= "" then tb.Password = __Password end
    local __AllowEditMode = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __AllowEditMode ~= false then tb.AllowEditMode = __AllowEditMode end
    tb.Mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(6))
    tb.TdmParam = pb.pb_RoomTdmParamDecode(decoder:getsubmsg(7))
    local __MatchSequence = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __MatchSequence ~= 0 then tb.MatchSequence = __MatchSequence end
    tb.Param = pb.pb_RoomParamDecode(decoder:getsubmsg(9))
    return tb
end

function pb.pb_CSRoomChangeSettingTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.RoomName) then    encoder:addstr(2, tb.RoomName)    end
    if(tb.IsPublic) then    encoder:addbool(3, tb.IsPublic)    end
    if(tb.Password) then    encoder:addstr(4, tb.Password)    end
    if(tb.AllowEditMode) then    encoder:addbool(5, tb.AllowEditMode)    end
    if(tb.Mode) then    pb.pb_MatchModeInfoEncode(tb.Mode, encoder:addsubmsg(6))    end
    if(tb.TdmParam) then    pb.pb_RoomTdmParamEncode(tb.TdmParam, encoder:addsubmsg(7))    end
    if(tb.MatchSequence) then    encoder:addu32(8, tb.MatchSequence)    end
    if(tb.Param) then    pb.pb_RoomParamEncode(tb.Param, encoder:addsubmsg(9))    end
end

function pb.pb_CSRoomChangeSettingTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeSettingTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeSettingTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomJoinTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomJoinTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __Password = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __Password ~= "" then tb.Password = __Password end
    return tb
end

function pb.pb_CSRoomJoinTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.Password) then    encoder:addstr(2, tb.Password)    end
end

function pb.pb_CSRoomJoinTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomJoinTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomJoinTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomGetInfoTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetInfoTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    return tb
end

function pb.pb_CSRoomGetInfoTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
end

function pb.pb_CSRoomGetInfoTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetInfoTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomGetInfoTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomGetListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetListReq) or {} 
    local __GameMode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __GameMode ~= 0 then tb.GameMode = __GameMode end
    return tb
end

function pb.pb_CSRoomGetListReqEncode(tb, encoder)
    if(tb.GameMode) then    encoder:addu32(1, tb.GameMode)    end
end

function pb.pb_CSRoomGetListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetListRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.RoomList = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.RoomList[k] = pb.pb_RoomBaseInfoDecode(v)
    end
    return tb
end

function pb.pb_CSRoomGetListResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.RoomList) then
        for i=1,#(tb.RoomList) do
            pb.pb_RoomBaseInfoEncode(tb.RoomList[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSRoomChangeReadyStateTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeReadyStateTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __IsReady = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsReady ~= false then tb.IsReady = __IsReady end
    return tb
end

function pb.pb_CSRoomChangeReadyStateTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.IsReady) then    encoder:addbool(2, tb.IsReady)    end
end

function pb.pb_CSRoomChangeReadyStateTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeReadyStateTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeReadyStateTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeEditModeTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeEditModeTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __EnableEditMode = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __EnableEditMode ~= false then tb.EnableEditMode = __EnableEditMode end
    return tb
end

function pb.pb_CSRoomChangeEditModeTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.EnableEditMode) then    encoder:addbool(2, tb.EnableEditMode)    end
end

function pb.pb_CSRoomChangeEditModeTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeEditModeTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeEditModeTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeCampTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeCampTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    return tb
end

function pb.pb_CSRoomChangeCampTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
end

function pb.pb_CSRoomChangeCampTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeCampTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeCampTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomQuitTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomQuitTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    return tb
end

function pb.pb_CSRoomQuitTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
end

function pb.pb_CSRoomQuitTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomQuitTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRoomQuitTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSRoomKickMemberTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomKickMemberTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __KickPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __KickPlayerID ~= 0 then tb.KickPlayerID = __KickPlayerID end
    return tb
end

function pb.pb_CSRoomKickMemberTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.KickPlayerID) then    encoder:addu64(2, tb.KickPlayerID)    end
end

function pb.pb_CSRoomKickMemberTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomKickMemberTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomKickMemberTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeSeatTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeSeatTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __FromSeatID = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __FromSeatID ~= 0 then tb.FromSeatID = __FromSeatID end
    local __IsFromObserverSeat = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsFromObserverSeat ~= false then tb.IsFromObserverSeat = __IsFromObserverSeat end
    local __ToSeatID = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __ToSeatID ~= 0 then tb.ToSeatID = __ToSeatID end
    local __IsToObserverSeat = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __IsToObserverSeat ~= false then tb.IsToObserverSeat = __IsToObserverSeat end
    return tb
end

function pb.pb_CSRoomChangeSeatTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.FromSeatID) then    encoder:addu32(2, tb.FromSeatID)    end
    if(tb.IsFromObserverSeat) then    encoder:addbool(3, tb.IsFromObserverSeat)    end
    if(tb.ToSeatID) then    encoder:addu32(4, tb.ToSeatID)    end
    if(tb.IsToObserverSeat) then    encoder:addbool(5, tb.IsToObserverSeat)    end
end

function pb.pb_CSRoomChangeSeatTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeSeatTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeSeatTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeOwnerTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeOwnerTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __NewOWnerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __NewOWnerID ~= 0 then tb.NewOWnerID = __NewOWnerID end
    return tb
end

function pb.pb_CSRoomChangeOwnerTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.NewOWnerID) then    encoder:addu64(2, tb.NewOWnerID)    end
end

function pb.pb_CSRoomChangeOwnerTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeOwnerTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeOwnerTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomInviteTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomInviteTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __InviteeID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __InviteeID ~= 0 then tb.InviteeID = __InviteeID end
    local __Source = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __Source ~= 0 then tb.Source = __Source end
    return tb
end

function pb.pb_CSRoomInviteTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.InviteeID) then    encoder:addu64(2, tb.InviteeID)    end
    if(tb.Source) then    encoder:addu32(3, tb.Source)    end
end

function pb.pb_CSRoomInviteTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomInviteTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRoomInviteTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSRoomResponseInviteTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomResponseInviteTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __IsAgree = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    local __InviterID = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __InviterID ~= 0 then tb.InviterID = __InviterID end
    local __RefuseMessage = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __RefuseMessage ~= "" then tb.RefuseMessage = __RefuseMessage end
    return tb
end

function pb.pb_CSRoomResponseInviteTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.IsAgree) then    encoder:addbool(2, tb.IsAgree)    end
    if(tb.InviterID) then    encoder:addu64(3, tb.InviterID)    end
    if(tb.RefuseMessage) then    encoder:addstr(10, tb.RefuseMessage)    end
end

function pb.pb_CSRoomResponseInviteTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomResponseInviteTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomResponseInviteTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomApplyLeaderTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomApplyLeaderTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    return tb
end

function pb.pb_CSRoomApplyLeaderTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
end

function pb.pb_CSRoomApplyLeaderTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomApplyLeaderTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomApplyLeaderTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomApplyLeaderResponseTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomApplyLeaderResponseTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __ApplyPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ApplyPlayerID ~= 0 then tb.ApplyPlayerID = __ApplyPlayerID end
    local __IsAgree = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    return tb
end

function pb.pb_CSRoomApplyLeaderResponseTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.ApplyPlayerID) then    encoder:addu64(2, tb.ApplyPlayerID)    end
    if(tb.IsAgree) then    encoder:addbool(3, tb.IsAgree)    end
end

function pb.pb_CSRoomApplyLeaderResponseTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomApplyLeaderResponseTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomApplyLeaderResponseTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeLeaderTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeLeaderTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __LeaderPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __LeaderPlayerID ~= 0 then tb.LeaderPlayerID = __LeaderPlayerID end
    return tb
end

function pb.pb_CSRoomChangeLeaderTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.LeaderPlayerID) then    encoder:addu64(2, tb.LeaderPlayerID)    end
end

function pb.pb_CSRoomChangeLeaderTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeLeaderTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeLeaderTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeTeamLeaderTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeTeamLeaderTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __TeamLeaderPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __TeamLeaderPlayerID ~= 0 then tb.TeamLeaderPlayerID = __TeamLeaderPlayerID end
    return tb
end

function pb.pb_CSRoomChangeTeamLeaderTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.TeamLeaderPlayerID) then    encoder:addu64(2, tb.TeamLeaderPlayerID)    end
end

function pb.pb_CSRoomChangeTeamLeaderTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeTeamLeaderTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeTeamLeaderTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomGetMatchModeListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetMatchModeListReq) or {} 
    local __game_mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    return tb
end

function pb.pb_CSRoomGetMatchModeListReqEncode(tb, encoder)
    if(tb.game_mode) then    encoder:addu32(1, tb.game_mode)    end
end

function pb.pb_RoomMatchModeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomMatchModeInfo) or {} 
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(1))
    tb.param_template_list = decoder:getu32ary(2)
    return tb
end

function pb.pb_RoomMatchModeInfoEncode(tb, encoder)
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(1))    end
    if(tb.param_template_list) then    encoder:addu32(2, tb.param_template_list)    end
end

function pb.pb_CSRoomGetMatchModeListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetMatchModeListRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.mode_info_array = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.mode_info_array[k] = pb.pb_MatchModeInfoDecode(v)
    end
    tb.mode_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.mode_info_list[k] = pb.pb_RoomMatchModeInfoDecode(v)
    end
    return tb
end

function pb.pb_CSRoomGetMatchModeListResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.mode_info_array) then
        for i=1,#(tb.mode_info_array) do
            pb.pb_MatchModeInfoEncode(tb.mode_info_array[i], encoder:addsubmsg(2))
        end
    end
    if(tb.mode_info_list) then
        for i=1,#(tb.mode_info_list) do
            pb.pb_RoomMatchModeInfoEncode(tb.mode_info_list[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSRoomBeginMatchTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomBeginMatchTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    return tb
end

function pb.pb_CSRoomBeginMatchTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
end

function pb.pb_CSRoomBeginMatchTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomBeginMatchTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRoomBeginMatchTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSRoomChangeNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeNtf) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __Type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __Type ~= 0 then tb.Type = __Type end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSRoomChangeNtfEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.Type) then    encoder:addu32(2, tb.Type)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(3))    end
end

function pb.pb_CSRoomResponseInviteNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomResponseInviteNtf) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __IsAgree = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    local __RefuseMessage = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __RefuseMessage ~= "" then tb.RefuseMessage = __RefuseMessage end
    return tb
end

function pb.pb_CSRoomResponseInviteNtfEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.IsAgree) then    encoder:addbool(2, tb.IsAgree)    end
    if(tb.RefuseMessage) then    encoder:addstr(10, tb.RefuseMessage)    end
end

function pb.pb_CSRoomBeInvitedTipsNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomBeInvitedTipsNtf) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    tb.Inviter = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(2))
    local __Source = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __Source ~= 0 then tb.Source = __Source end
    return tb
end

function pb.pb_CSRoomBeInvitedTipsNtfEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.Inviter) then    pb.pb_PlayerSimpleInfoEncode(tb.Inviter, encoder:addsubmsg(2))    end
    if(tb.Source) then    encoder:addu32(3, tb.Source)    end
end

function pb.pb_CSRoomApplyLeaderNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomApplyLeaderNtf) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __ApplyPlayerID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ApplyPlayerID ~= 0 then tb.ApplyPlayerID = __ApplyPlayerID end
    return tb
end

function pb.pb_CSRoomApplyLeaderNtfEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.ApplyPlayerID) then    encoder:addu64(2, tb.ApplyPlayerID)    end
end

function pb.pb_CSRoomApplyLeaderResponseNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomApplyLeaderResponseNtf) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __IsAgree = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __IsAgree ~= false then tb.IsAgree = __IsAgree end
    return tb
end

function pb.pb_CSRoomApplyLeaderResponseNtfEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.IsAgree) then    encoder:addbool(3, tb.IsAgree)    end
end

function pb.pb_CSRoomKickMemberNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomKickMemberNtf) or {} 
    local __KickedPlayerID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __KickedPlayerID ~= 0 then tb.KickedPlayerID = __KickedPlayerID end
    return tb
end

function pb.pb_CSRoomKickMemberNtfEncode(tb, encoder)
    if(tb.KickedPlayerID) then    encoder:addu64(1, tb.KickedPlayerID)    end
end

function pb.pb_CSRoomChangeTeamNameTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeTeamNameTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __TeamID = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __TeamName = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __TeamName ~= "" then tb.TeamName = __TeamName end
    return tb
end

function pb.pb_CSRoomChangeTeamNameTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.TeamID) then    encoder:addu32(2, tb.TeamID)    end
    if(tb.TeamName) then    encoder:addstr(3, tb.TeamName)    end
end

function pb.pb_CSRoomChangeTeamNameTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeTeamNameTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeTeamNameTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomBatchChangeTeamNameTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomBatchChangeTeamNameTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    tb.TeamInfos = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.TeamInfos[k] = pb.pb_RoomTeamInfoDecode(v)
    end
    return tb
end

function pb.pb_CSRoomBatchChangeTeamNameTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.TeamInfos) then
        for i=1,#(tb.TeamInfos) do
            pb.pb_RoomTeamInfoEncode(tb.TeamInfos[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSRoomBatchChangeTeamNameTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomBatchChangeTeamNameTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomBatchChangeTeamNameTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomChangeTeamInfoTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeTeamInfoTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    local __TeamID = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __TeamID ~= 0 then tb.TeamID = __TeamID end
    local __ChangeType = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ChangeType ~= 0 then tb.ChangeType = __ChangeType end
    tb.TeamInfo = pb.pb_RoomTeamInfoDecode(decoder:getsubmsg(4))
    tb.MemberBattles = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.MemberBattles[k] = pb.pb_RoomMemberBattleInfoDecode(v)
    end
    return tb
end

function pb.pb_CSRoomChangeTeamInfoTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
    if(tb.TeamID) then    encoder:addu32(2, tb.TeamID)    end
    if(tb.ChangeType) then    encoder:addu32(3, tb.ChangeType)    end
    if(tb.TeamInfo) then    pb.pb_RoomTeamInfoEncode(tb.TeamInfo, encoder:addsubmsg(4))    end
    if(tb.MemberBattles) then
        for i=1,#(tb.MemberBattles) do
            pb.pb_RoomMemberBattleInfoEncode(tb.MemberBattles[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_CSRoomChangeTeamInfoTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomChangeTeamInfoTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Info = pb.pb_RoomInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomChangeTeamInfoTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Info) then    pb.pb_RoomInfoEncode(tb.Info, encoder:addsubmsg(2))    end
end

function pb.pb_CSRoomGetBaseInfoTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetBaseInfoTReq) or {} 
    local __RoomID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __RoomID ~= 0 then tb.RoomID = __RoomID end
    return tb
end

function pb.pb_CSRoomGetBaseInfoTReqEncode(tb, encoder)
    if(tb.RoomID) then    encoder:addu64(1, tb.RoomID)    end
end

function pb.pb_CSRoomGetBaseInfoTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRoomGetBaseInfoTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.Base = pb.pb_RoomBaseInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRoomGetBaseInfoTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.Base) then    pb.pb_RoomBaseInfoEncode(tb.Base, encoder:addsubmsg(2))    end
end

