----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"


---@class StoreParseSpecify : LuaUIBaseView
local StoreParseSpecify = ui("StoreParseSpecify")
local StoreConfig = Module.Store.Config
local BattlePassLogic = require "DFM.Business.Module.BattlePassModule.Logic.BattlePassLogic"
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CommonWidgetConfig = Module.CommonWidget.Config
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVCompOrder = Module.CommonWidget.Config.EIVCompOrder
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EGPInputType = import  "EGPInputType"

--- 生命周期

function StoreParseSpecify:Ctor()
    -- 蓝图控件引用
    -- self._wtBtnClose = self:Wnd("Button_Close", UIButton)
    -- self._wtBtnClose:Event("OnClicked",self.OnCloseBtnClick,self)

    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_0", self._waterFallListGetItemCount,
            self._waterFallListProcessItemWidget)
    self._wtTipsCheckBox = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly) -- 当前赛季经验加成说明
    if DFHD_LUA == 1 then
        self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_100", self._ShowTips, self._HideTips)
    else
        self._wtTipsAnchor = self:Wnd("DFTipsAnchor_100", UIWidgetBase)
        self._wtTipsCheckBox:Event("OnCheckStateChanged", self._OnTipsCheckBoxStateChanged, self)
    end
    self._wtImgQualityIcon = self:Wnd("wtQualityIcon", UIImage)  -- 奖励道具品质图标
    self._wtTxtRewardName = self:Wnd("wtTextWeaponName", UITextBlock)  -- 奖励道具名
    self._wtBtnRewardDetail = self:Wnd("wtButtonPreview", DFCommonButtonOnly)  -- 奖励道具详情按钮
    self._wtTxtRewardDesc = self:Wnd("wtWeaponDesc", UITextBlock)  -- 道具描述
    self._wtChooseBtn = self:Wnd("wtSingleActionBtn", DFCommonButtonOnly)  -- 选择按钮
    self._wtImgBackGround = self:Wnd("DFImage_50", UIImage)  -- 背景底图

    --- 控件处理
    -- self._wtBtnClose:Event("OnClicked",self.OnCloseBtnClick,self)  
    
    self._wtBtnRewardDetail:Event("OnClicked", self.OnBtnRewardDetailClick, self)
    self._wtChooseBtn:Event("OnClicked", self.OnBtnChooseClick, self)

    --- 成员变量声明

    -- self._wtBtnClose = self:Wnd("Button_Close", UIButton)
    -- self._wtBtnClose:Event("OnClicked",self.OnCloseBtnClick,self)

    self._iChooseRewardID = nil -- 选中的核心奖励ID
    self._iChooseRewardPos = nil -- 选中的核心奖励位置
    self._bFirstChoose = false -- 是否第一次选中
    self._tTipsHandler = nil   -- Tips 缓存
    self._iSelectRewardID = nil -- 点击的奖励的ID
    self._iSelectRewardPos = nil -- 点击的奖励的位置
    self._iSelectRewardCount = nil -- 点击的奖励的数量
    self._iSelectRewardItemNumID = nil  -- 点击的奖励的NumID
    self._iSelectRewardItemConfig = nil  -- 选择的奖励的配置
    self._iLastSelectRewardPos = 0  -- 上一次选择的奖励的位置

    --- 预定义


    --- 初始化逻辑
    Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.MandelUpRewardSelectTitle)
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function StoreParseSpecify:OnInitExtraData(lotteryID, boxID, sourceID)
    if lotteryID == nil or boxID == nil then
        Facade.UIManager:CloseUI(self)
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.StoreBoxNotFound)
        return
    end
    
    self._iLotteryID = lotteryID
    self._iBoxID = boxID
    self._iSourceID = sourceID
    
    self._tBoxInfo = Server.StoreServer:GetBoxInfo(self._iBoxID)
    if self._tBoxInfo == nil then
        Facade.UIManager:CloseUI(self)
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.StoreBoxNotFound)
        return
    end

end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function StoreParseSpecify:OnOpen()
    loginfo("StoreParseSpecify:OnOpen")

    -- 注册监听事件
    self:AddListenersOpen()

    self:RefreshUIByServer()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function StoreParseSpecify:OnClose()
    loginfo("StoreParseSpecify:OnClose")

    -- 移除lua事件
    self:RemoveListenersOpen()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function StoreParseSpecify:OnShow()
    loginfo("StoreParseSpecify:OnShow")
end

function StoreParseSpecify:OnShowBegin()
    loginfo("StoreParseSpecify:OnShowBegin")

    if IsHD() then
        self:_EnableGamepadFeature()
    end

    -- 注册监听事件
    self:AddListenersShow()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function StoreParseSpecify:OnHide()
    loginfo("StoreParseSpecify:OnHide")

    -- 隐藏Tips
    self:_HideTips()

    -- 移除监听事件
    self:RemoveListenersShow()
end

function StoreParseSpecify:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function StoreParseSpecify:AddListenersShow()
    -- 普通事件
    -- self:AddLuaEvent(BattlePassConfig.evtMainPanelTest, self.OnMainPanelTest, self)
    -- ntf协议事件 ntfNameString
    -- Facade.ProtoManager:AddNtfListener("CSAuctionOrderChangeNtf", self.OnCSAuctionOrderChangeNtf, self)

    -- 移除事件监听
    self:RemoveListenersShow()

    -- 服务器数据更新事件
    --self:AddLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate, self.RefreshUIServer, self)
end

-- 移除show监听的事件
function StoreParseSpecify:RemoveListenersShow()
    -- 服务器数据更新事件
    --self:RemoveLuaEvent(Server.BattlePassServer.Events.evtBattlePassInfoUpdate)
end

-- 监听open开始的事件
function StoreParseSpecify:AddListenersOpen()
    -- 移除监听的事件 
    self:RemoveListenersOpen()

    -- 奖励点击事件
    --self:AddLuaEvent(BattlePassConfig.Events.evtBattlePassRewardTabItemClick, self.OnBattlePassRewardTabItemClick, self)
    
    self:_AddMouseButtonDownEvent()
    self:AddLuaEvent(Module.Store.Config.evtStoreMandelUpRewardSelect, self.OnEvtStoreMandelUpRewardSelect, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelUpRewardChooseResult, self.OnEvtStoreMandelUpRewardChooseResult, self) -- 高概率奖励选择结果
end

-- 移除open监听的事件
function StoreParseSpecify:RemoveListenersOpen()
    -- 服务器数据更新事件
    --self:RemoveLuaEvent(BattlePassConfig.Events.evtBattlePassRewardTabItemClick)
    
    self:_RemoveMouseButtonDownEvent()
    self:RemoveLuaEvent(Module.Store.Config.evtStoreMandelUpRewardSelect)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreMandelUpRewardChooseResult)
end

--- 事件处理

function StoreParseSpecify:_waterFallListGetItemCount()
    if self._tCoreGroup == nil or self._tCoreGroup.prop_list == nil then
        return 0
    end 
    
    return #self._tCoreGroup.prop_list
end

function StoreParseSpecify:_waterFallListProcessItemWidget(position, itemWidget)
    local itemID = self._tCoreGroup.prop_list[position].prop_id
    local itemCount = self._tCoreGroup.prop_list[position].num
    local itemNumID = self._tCoreGroup.prop_list[position].num_id

    local fClickCallback = CreateCallBack(function()
        StoreConfig.evtStoreMandelUpRewardSelect:Invoke(position, itemID, itemCount, itemNumID)
    end, self)

    -- 设置icon
    itemWidget:SetMandelUpRewardItemInfo(position, itemID, 1, fClickCallback)

    -- 获取已经选择的奖励所在的位置
    if self._iChooseRewardPos == nil and itemID == self._iChooseRewardID then
        self._iChooseRewardPos = position
    end

    -- 选中的道具
    if itemID == self._iChooseRewardID then
        itemWidget:SetTopRightTips(true, StoreConfig.Loc.MandelUpRewardProbUp)
    else
        itemWidget:SetTopRightTips(false)
    end
    
    -- 进行第一次选择
    if not self._bFirstChoose then
        if self._iChooseRewardPos then
            self._bFirstChoose = true
            StoreConfig.evtStoreMandelUpRewardSelect:Invoke(self._iChooseRewardPos, itemID, itemCount, itemNumID)
        end
    end

    -- 设置选中状态
    if self._iSelectRewardPos == position then
        itemWidget:SetSelected(true)
    else
        itemWidget:SetSelected(false)
    end
end

function StoreParseSpecify:_ShowTips()
    if self._tTipsHandler then
        Module.CommonTips:RemoveAssembledTips(self._tTipsHandler)
        self._tTipsHandler = nil
    end

    self._tTipsHandler = Module.CommonTips:ShowAssembledTips({
        {
            id = UIName2ID.Assembled_CommonMessageTips_V1,
            data = {
                textContent = StoreConfig.Loc.StoreMandelUpRewardChooseTips,
            }
        },
    }, self._wtTipsAnchor)

    self.hasTipClick = true
end

function StoreParseSpecify:_HideTips()
    local wtBtn = self._wtTipsCheckBox
    if self._tTipsHandler then
        Module.CommonTips:RemoveAssembledTips(self._tTipsHandler)
        self._tTipsHandler = nil
        wtBtn:SetSelectState(false)
    end

    self.hasTipClick = false
end

function StoreParseSpecify:_OnTipClick()
    if self._wtTipsAnchor ~= nil and self._wtTipsAnchor:IsVisible() == false then
        return
    end

    if self.hasTipClick == false then
        self:_ShowTips()
    else
        self:_HideTips()
    end
end

function StoreParseSpecify:_OnTipsCheckBoxStateChanged(bCheck)
    if bCheck then
        self:_ShowTips()
    else
        self:_HideTips()
    end
end

function StoreParseSpecify:_OnMouseButtonDown(mouseEvent)
    local screenPosition=mouseEvent:GetScreenSpacePosition()
    local seasonDescBtnGeometry=self._wtTipsCheckBox:GetCachedGeometry()
    local isUnderSeasonDescBtn=USlateBlueprintLibrary.IsUnderLocation(seasonDescBtnGeometry, screenPosition)
    if not isUnderSeasonDescBtn then
        self:_HideTips()
    end
end

function StoreParseSpecify:OnEvtStoreMandelUpRewardSelect(position, itemID, itemCount, itemNumID)
    self._iLastSelectRewardPos = position 
    
    self._iSelectRewardID = itemID
    self._iSelectRewardPos = position
    self._iSelectRewardCount = itemCount
    self._iSelectRewardItemNumID = itemNumID

    self._iSelectRewardItemConfig = Server.StoreServer:GetLotteryBoxPropSingleConfigByID(self._tCoreGroup.group_id, self._iSelectRewardID)
    
    local lastItem = self:GetItemByPos(self._iLastSelectRewardPos)
    local nowSelectItem = self:GetItemByPos(self._iSelectRewardPos)
    if lastItem then
        lastItem:SetSelected(false)
    end
    if nowSelectItem then
        nowSelectItem:SetSelected(true)
    end
    
    -- 刷新道具详情相关内容
    self:RefreshRewardDetail()

    -- 刷新选择按钮
    self:RefreshChooseBtn()
    
    -- 设置底图
    if self._iSelectRewardItemConfig and self._iSelectRewardItemConfig.SpriteName then
        self._wtImgBackGround:AsyncSetImagePath(self._iSelectRewardItemConfig.SpriteName) 
    end


    -- 延迟刷新
    Timer.DelayCall(0.1,  function()
        self._wtWaterFallList:RefreshAllItems() 
    end, self)
end

function StoreParseSpecify:OnEvtStoreMandelUpRewardChooseResult(result)
    if result == 0 then
        Facade.UIManager:CloseUI(self)
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelUpRewardAlreadyChoose)
    end 
end

function StoreParseSpecify:OnBtnRewardDetailClick()
    if self._iSelectRewardID == nil then 
        return 
    end
    
    local itemData = ItemBase:NewIns(self._iSelectRewardID)

    -- 展示枪械皮肤详情
    if itemData.itemMainType == EItemType.WeaponSkin then
        local itemWeapon = nil
        itemWeapon = Server.CollectionServer:GetWeaponSkinIfExists(self._iSelectRewardID)
        if itemWeapon == nil then
            local propInfo = {
                id = self._iSelectRewardID,
                gid = 0,
                num = self._iSelectRewardCount
            }
            itemWeapon = ItemHelperTool.CreateItemByPropInfo(propInfo)
        end
        Module.Collection:ShowWeaponSkinDetailPage(itemWeapon)
    elseif itemData.itemMainType == EItemType.Fashion then
        Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, self._iSelectRewardID)
    else
        Module.ItemDetail:OpenItemDetailPanel(itemData, self._wtBtnRewardDetail)
    end
end

function StoreParseSpecify:OnBtnChooseClick()
    local fConfirmWindowProcess = CreateCallBack(function()
        if self._tCoreGroup ~= nil then
            Server.StoreServer:ReqShopSetMandelBoxUp(self._iBoxID, self._tCoreGroup.group_id, self._iSelectRewardID, self._iSelectRewardItemNumID, self._iSourceID)
        else
            logerror("[StoreParseSpecify] OnBtnChooseClick self._tCoreGroup is nil")
        end
    end, self)

    local tItem = ItemBase:New(self._iSelectRewardID, 1)
    local sTitle = string.format(StoreConfig.Loc.MandelUpRewardChooseBtnClickTips, tItem.name)
    Module.CommonTips:ShowConfirmWindow(sTitle, fConfirmWindowProcess, nil, nil, nil, nil, nil, nil, nil, nil, true)
end

--- 刷新函数

function StoreParseSpecify:RefreshUIByServer()
    self:_MakeCache()

    self._wtWaterFallList:RefreshItemCount()
    self._wtWaterFallList:RefreshAllItems()
end

function StoreParseSpecify:RefreshUINoServer()
end

function StoreParseSpecify:RefreshUIServerUpdate()
end

function StoreParseSpecify:RefreshRewardDetail()
    -- 设置道具名
    local tItem = ItemBase:New(self._iSelectRewardID, 1)
    self._wtTxtRewardName:SetText(tItem.name)

    -- 设置品质图标 
    if tItem.quality > 0 then
        self:SetQualityIconType(tItem.quality)
        self._wtImgQualityIcon:SetColorAndOpacity(BattlePassLogic.GetQualityIconColorByNum(tItem.quality))
        self._wtImgQualityIcon:SelfHitTestInvisible()
    else
        self._wtImgQualityIcon:Collapsed()
    end

    -- 设置描述文本
    local sDesc = ItemDetailLogic.GetItemDesc(tItem)
    self._wtTxtRewardDesc:SetText(sDesc)
end

function StoreParseSpecify:RefreshChooseBtn()
    if self._iSelectRewardID == self._iChooseRewardID then
        self._wtChooseBtn:SetMainTitle(StoreConfig.Loc.MandelUpRewardChooseBtnAlready) 
        self._wtChooseBtn:SetBtnEnable(false)
    else
        self._wtChooseBtn:SetMainTitle(StoreConfig.Loc.MandelUpRewardChooseBtn)
        self._wtChooseBtn:SetBtnEnable(true)
    end
end

---- 其他函数

function StoreParseSpecify:_MakeCache()
    self._iChooseRewardID = Server.StoreServer:GetMandelIDUpReward(self._iBoxID)
    if self._iChooseRewardID == nil then
        -- 当没有被选择的高概率核心奖励时，选择第一个
        self._iChooseRewardPos = 1
    end

    self._tCoreGroup = Server.StoreServer:GetMandelLotteryCoreGroup(self._iBoxID)
    if self._tCoreGroup == nil then
        Facade.UIManager:CloseUI(self)
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.StoreBoxNotFound)
        return
    end
end

function StoreParseSpecify:_AddMouseButtonDownEvent()
    loginfo("StoreParseSpecify:AddMouseButtonDownEvent")
    local gameInst=GetGameInstance()
    if gameInst then
        self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnMouseButtonDown,self)
    end
end

function StoreParseSpecify:_RemoveMouseButtonDownEvent()
    loginfo("StoreParseSpecify:RemoveMouseButtonDownEvent")
    if self._mouseButtonDownHandle then
        local gameInst=GetGameInstance()
        if gameInst then
            self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._OnMouseButtonDown)
        end
    end
end

function StoreParseSpecify:GetItemByPos(position)
    return self._wtWaterFallList:GetItemByIndex(position - 1)
end

function StoreParseSpecify:_EnableGamepadFeature()
    if self._wtChooseBtn then
        self._wtChooseBtn:SetDisplayInputAction("Confirm", true, nil, true)
    end

    self.hasTipClick = false

    --配置输入
    if not self._choseReward then
        self._choseReward = self:AddInputActionBinding(
        "Confirm", 
        EInputEvent.IE_Pressed, 
        self._HandleChooseBtnByGamepad,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    -- 设置导航
    if self._wtWaterFallList then
        if not self._NavGroup_WaterFall then
            self._NavGroup_WaterFall = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList , self, "Hittest")
        end
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._wtWaterFallList)
        self._NavGroup_WaterFall:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WaterFall)

        -- 十字键导航时需要触发绑定在DiaplayAciton 'A' 的 确认选择功能，需要设置NoA_Direction
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    end

    local summaryList = {}
    if self._wtBtnRewardDetail then
        table.insert(summaryList, {actionName = "Store_ItemDetail", func = self.OnBtnRewardDetailClick, caller = self, bUIOnly = false, bHideIcon = false})
    end
    if self._wtTipsAnchor ~= nil and self._wtTipsAnchor:IsVisible() then
        table.insert(summaryList, {actionName = "Assembly_Confirm", func = self._OnTipClick, caller = self, bUIOnly = false, bHideIcon = false})
    end
    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function StoreParseSpecify:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    -- 移除输入
    if self._choseReward then
        self:RemoveInputActionBinding(self._choseReward)
        self._choseReward = nil
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_WaterFall = nil
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function StoreParseSpecify:_HandleChooseBtnByGamepad()
    if not IsHD() then
        return
    end

    if self._iSelectRewardID ~= self._iChooseRewardID then
        self:OnBtnChooseClick()
    end
end

return StoreParseSpecify 