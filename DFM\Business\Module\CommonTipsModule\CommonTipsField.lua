----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonTips)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CommonTipsField : FieldBase
local CommonTipsField = class("CommonTipsField", require "DFM.YxFramework.Managers.Module.FieldBase")
require("DFM.Business.Proto.Pb.errcode_pb")
local CommonTipsLogic = require "DFM.Business.Module.CommonTipsModule.CommonTipsLogic"
local EGameHUDState = import "GameHUDSate"
local ExtendHUDState = require("DFM.YxFramework.Managers.UI.Layer.HUD.ExtendHUDState")

local function xxww_(...)
    loginfo("[xxww] ", ...)
end

local function log(...)
    print("[CommonTipsField]", ...)
end

function CommonTipsField:Ctor()
    -- 缓存网络链接提示界面Handle
    self.netLoadingHandle = nil
    -- 延迟窗口Handle
    self.delayWindHandle = nil
    -- 缓存错误信息
    self._cacheErrInfo = {}
    -- 不再显示错误信息弹窗
    self._bDontShowAnyMore = false
    -- 错误码映射表
    self._mapErrCode2String = {}
    -- 缓存纯文本描述弹窗的实例
    self.descTipIns = nil
    --买活重玩
    self.purchaseReplayView = nil

    -- 缓存Tips对象池
    self._commonTipsUIInsPool = {}
    -- 缓存激活态的tips
    self._activateTipsUIInsList = {}
    -- 缓存失活态的tips
    self._inactivatePopTipsUIInsList = {}
    -- 缓存失活态的tips
    self._inactivateHudTipsUIInsList = {}

    -- 通用强提醒Tips弹窗Handle
    self._strongTipHandle = nil
    self._strongTipText = nil
    self.cppCommonMessageTipHandle = nil

    self:_ParseErrorCode()

    if IsHD() then
        self.bAssignNextTipType = false
        self.bAssignNextTipPopTips = false
        self.bAssignNextTipPopCenter = false
        self.states = ExtendHUDState:NewIns()
        self.usePopTipsStates = {
            EGameHUDState.GHS_EscPanel,
            EGameHUDState.GHS_OpenMap,
            EGameHUDState.GHS_RealOpenBag,
            EGameHUDState.GHS_RealOpenLoot,
            EGameHUDState.GHS_HackPCInfoView,
            EGameHUDState.GHS_FingerprintView,
            EGameHUDState.GHS_SOLCaptureView,
            EGameHUDState.GHS_ContractAccess,
            EGameHUDState.GHS_Settlement,
            EGameHUDState.GHS_LiveSpectate,
            EGameHUDState.GHS_RedeploySelectVehicle,
            EGameHUDState.GHS_Redeploy,
            EGameHUDState.GHS_AirDropMainHUD,
            EGameHUDState.GHS_TeamInfoDetail,
            EGameHUDState.GHS_RedeployCommanderView,
            EGameHUDState.GHS_BreakthroughSupportReleaseView,
            EGameHUDState.GHS_SOLInterceptView,
            EGameHUDState.GHS_HackPCOperationMain
        }
        self.NOTShowHudTipsStates = {
            EGameHUDState.GHS_CutScene,
            EGameHUDState.GHS_OpenMap,
        }
    end
    -- Timer.DelayCallSomeTimes(
    --     2,
    --     100,
    --     function()
    --         Module.CommonTips:ShowSimpleTip("Tips测试")
    --     end
    -- )
    -- Timer.DelayCallSomeTimes(
    --     5,
    --     100,
    --     function()
    --         Module.CommonTips:AssignNextTipType_HD(true, true)
    --     end
    -- )
end

------------------------------------ Override function ------------------------------------
function CommonTipsField:OnDeclareField()
    LuaErrModuleLoaded = true
    self._errTimerIns = Timer:NewIns(1, 0)
    self._errTimerIns:AddListener(self._TickShowErrInfo, self)
    self._errTimerIns:Start()

end

function CommonTipsField:OnClearField()
    if self._errTimerIns then
        self._errTimerIns:Release()
        self._errTimerIns = nil
    end
end

function CommonTipsField:OnDestroyField()
end

------------------------------------ private function ------------------------------------
function CommonTipsField:_ParseErrorCode()
    -- local errorCodeEnv = {}
    -- local errcodeFile = loadfile(FPaths.ProjectDir() .. "LuaSource/" .. "FrontEnd/Proto/Pb/errcode_pb.lua")
    -- setfenv(errcodeFile, errorCodeEnv)
    -- errcodeFile()
    for str, code in pairs(Err) do
        if type(code) == "number" then
            self._mapErrCode2String[code] = str
        end
    end
end

function CommonTipsField:_TickShowErrInfo()
    if #self._cacheErrInfo > 0 then
        -- 取一个就行
        local errStr = self._cacheErrInfo[1]
        self._cacheErrInfo = {}
        self:_ShowLuaScriptErrWindow(errStr)
    end
end

function CommonTipsField:_ShowLuaScriptErrWindow(traceback)
    if self._bDontShowAnyMore then
        return
    end
    CommonTipsLogic.ShowLuaScriptErrWindow(traceback)
end

------------------------------------ public function ------------------------------------
function CommonTipsField:CacheErrInfo(errStr)
    if self._bDontShowAnyMore then
        return
    end
    table.insert(self._cacheErrInfo, errStr)
end

function CommonTipsField:GetCacheErrInfo()
    return self._cacheErrInfo
end

function CommonTipsField:GetErrCode2String(errorCode)
    local commonErrorConfig = Module.CommonTips.Config.GetErrorTable()
    if commonErrorConfig and commonErrorConfig[tostring(errorCode)] then
        local errorConfig = commonErrorConfig[tostring(errorCode)] 
        local tipStr = errorConfig.ExtDesc
        if tipStr and string.len(tipStr) > 0 then
            --loginfo("errorCode: ", errorCode, " 已扩展自定义配置")
            return tipStr
        else
            tipStr = errorConfig.BaseDesc
            if tipStr and string.len(tipStr) > 0 then
                --loginfo("errorCode: ", errorCode, " 读取服务器原生配置")
                return string.format("%s: %s", tostring(errorCode), tipStr)
            end
        end
    end
    -- 保底逻辑
    if VersionUtil.IsShipping() then
        logwarning("receive no config protocol errorcode: ", errorCode)
        return string.format(Module.CommonTips.Config.Loc.NotFoundErrorCodeConfigShipping, tostring(errorCode))
    else
        if self._mapErrCode2String and self._mapErrCode2String[errorCode] then
            return string.format(Module.CommonTips.Config.Loc.NotFoundErrorCodeConfig, tostring(errorCode), self._mapErrCode2String[errorCode])
        else
            return string.format(Module.CommonTips.Config.Loc.NotFoundErrorCodeConfig, tostring(errorCode), "")
        end
    end
end

function CommonTipsField:SetDontShowAnyMore(bShow)
    self._bDontShowAnyMore = bShow
end

---从缓存Tips对象池中获得uiIns实例
---@param fGetCommonTipsCallback function 获得uiIns实例的回调方法
function CommonTipsField:GetCommonTips(fGetCommonTipsCallback)
    -- log("tips in pool count:",table.nums(self._commonTipsUIInsPool))
    -- log("activateTips count:",table.nums(self._activateTipsUIInsList))
    -- log("inactivatePopTips count:",table.nums(self._inactivatePopTipsUIInsList))
    -- log("inactivateHudTips count:",table.nums(self._inactivateHudTipsUIInsList))
    local bUsePopTips = Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game or (IsHD() and (CommonTipsLogic.GetShouldShowPopTipsInGame_HD() or (self.bAssignNextTipType and self.bAssignNextTipPopTips)))
    if bUsePopTips and self._inactivatePopTipsUIInsList and next(self._inactivatePopTipsUIInsList) then
        local uiIns = table.remove(self._inactivatePopTipsUIInsList, 1)
        if not hasdestroy(uiIns) then
            self:SetCommonTipsActivate(uiIns, true)
            if fGetCommonTipsCallback then
                fGetCommonTipsCallback(uiIns)
            end
        else
            self:GetCommonTips(fGetCommonTipsCallback)
        end
    elseif self._inactivateHudTipsUIInsList and next(self._inactivateHudTipsUIInsList) then
        local uiIns = table.remove(self._inactivateHudTipsUIInsList, 1)
        if not hasdestroy(uiIns) then
            self:SetCommonTipsActivate(uiIns, true)
            if fGetCommonTipsCallback then
                fGetCommonTipsCallback(uiIns)
            end
        else
            self:GetCommonTips(fGetCommonTipsCallback)
        end
    else
        local function loadFinCall(uiIns)
            self._activateTipsUIInsList[uiIns] = uiIns
            table.insert(self._commonTipsUIInsPool, uiIns)
            if fGetCommonTipsCallback then
                fGetCommonTipsCallback(uiIns)
            end
        end
        if bUsePopTips then
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonPopTips, loadFinCall)
        else
            if IsHD() then
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonHudTips_PC, loadFinCall)
            else
                Facade.UIManager:AsyncShowUI(UIName2ID.CommonHudTips, loadFinCall)
            end
        end
        
    end
end

---获得当前激活态的tips实例list
---@return table
function CommonTipsField:GetActivateTips()
    return self._activateTipsUIInsList
end

---设置改tips实例状态
---@param commonTipsUInIns CommonPopTips
---@param bActivate boolean 是否激活
function CommonTipsField:SetCommonTipsActivate(commonTipsUInIns, bActivate)
    if not commonTipsUInIns then
        return
    end
    if bActivate then
        commonTipsUInIns:SelfHitTestInvisible()
        self._activateTipsUIInsList[commonTipsUInIns] = commonTipsUInIns
    else
        if hasdestroy(commonTipsUInIns) then
            table.removebyvalue(self._commonTipsUIInsPool,commonTipsUInIns)
        else
            commonTipsUInIns:SetPlaybackSpeed(commonTipsUInIns.Anima_AutoOut,1)
            commonTipsUInIns:StopAllAnimations()
            commonTipsUInIns:Collapsed()
            commonTipsUInIns:_ReleaseTimer()
            if commonTipsUInIns.UINavID == UIName2ID.CommonPopTips then
                table.insert(self._inactivatePopTipsUIInsList, commonTipsUInIns)
            elseif commonTipsUInIns.UINavID == UIName2ID.CommonHudTips then
                table.insert(self._inactivateHudTipsUIInsList, commonTipsUInIns)
            end
        end
        self._activateTipsUIInsList[commonTipsUInIns] = nil
    end
end

---清空tips缓存
function CommonTipsField:ClearCommonTipsUIIns()
    for key, uiIns in pairs(self._commonTipsUIInsPool) do
        if uiIns then
            if not hasdestroy(uiIns) then
                uiIns:_ReleaseTimer()
                Facade.UIManager:CloseUI(uiIns)
            else
                uiIns = nil
            end
        end
    end
    -- Tips对象池
    self._commonTipsUIInsPool = {}
    -- 激活态的tips
    self._activateTipsUIInsList = {}
    -- 失活态的tips
    self._inactivatePopTipsUIInsList = {}
    -- 失活态的tips
    self._inactivateHudTipsUIInsList = {}
end

function CommonTipsField:GetStrongTipHandle()
    return self._strongTipHandle
end

function CommonTipsField:SetStrongTipHandle(strongTipHandle)
    self._strongTipHandle = strongTipHandle
end

function CommonTipsField:CheckAndClearStrongTipHandle()
    if self._strongTipHandle then
        local uiIns = self._strongTipHandle:GetUIIns()
        Facade.UIManager:CloseUI(uiIns)
        self._strongTipHandle = nil
    end 
end

function CommonTipsField:GetStrongTipText()
    return self._strongTipText
end

function CommonTipsField:SetStrongTipText(strongTipText)
    self._strongTipText = strongTipText
end

return CommonTipsField
