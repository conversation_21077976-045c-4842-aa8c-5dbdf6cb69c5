----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------



local MPCammanderBigRankUp = ui("MPCammanderBigRankUp")

function MPCammanderBigRankUp:Ctor()
    self.item1 = self:Wnd("WBP_CommanderUpgrades_01", UIWidgetBase)
    self.item2 = self:Wnd("WBP_CommanderUpgrades_02", UIWidgetBase)
    self.item3 = self:Wnd("WBP_CommanderUpgrades_03", UIWidgetBase)
    self.item4 = self:Wnd("WBP_CommanderUpgrades_04", UIWidgetBase)
    self.item1:Collapsed()
    self.item2:Collapsed()
    self.item3:Collapsed()
    self.item4:Collapsed()
end

function MPCammanderBigRankUp:OnInitExtraData(targetRank)
    self._targetRank = targetRank
    if self._targetRank > 4 then
        self._targetRank = 4
    end
    Module.Settlement.Field:SetIsSpecialPopOpen(true)
end

function MPCammanderBigRankUp:OnOpen()
    self:_BindBigRankWidget()
end

function MPCammanderBigRankUp:OnClose()
    Module.Settlement.Field:SetIsSpecialPopOpen()
end

function MPCammanderBigRankUp:_BindBigRankWidget()
    local wbp = self["item" .. tostring(self._targetRank)]
    self:PlayAnimation(self.WBP_BattlePointMatch_RankUpgrade_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if wbp then
        wbp:SelfHitTestInvisible()

        if wbp["WBP_CommanderUpgrades_0" .. self._targetRank .. "_in"] then
            wbp:PlayAnimation(wbp["WBP_CommanderUpgrades_0" .. self._targetRank .. "_in"], 0, 1, EUMGSequencePlayMode.Forward, 1, false)

            Timer.DelayCall(
                wbp["WBP_CommanderUpgrades_0" .. self._targetRank .. "_in"]:GetEndTime(),
                function()
                    self:_CloseSelf()
                end,
                self
            )

        else
            self:_CloseSelf()
        end
    else
        self:_CloseSelf()
    end
end
function MPCammanderBigRankUp:_CloseSelf()
    Facade.UIManager:CloseUI(self)
end

function MPCammanderBigRankUp:OnNavBack()
    return true
end

return MPCammanderBigRankUp