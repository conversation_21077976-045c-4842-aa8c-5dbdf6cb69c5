----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPay)
----- LOG FUNCTION AUTO GENERATE END -----------



local CentauriGarenaLogic = {}
local this = CentauriGarenaLogic
local DFMGameGarena = import "DFMGameGarena"
local _garenaIns = DFMGameGarena.Get(GetGameInstance())
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local LocalizeTool= require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local Json = JsonFactory.createJson()
local _appid = "100151"
local _region = ""
local _pc_url = ""
local _access_token = ""

local _idc = "singapore" -- 米大师IDC，即米大师服务所在国家，目前支持香港、加拿大、越南、日本等,其中国内为local(必传)
local _env = "test" -- 环境参数设置：现网环境release，沙箱test.建议应用在接入时先在沙箱环境测试通过后，切换至release再发布(必传)
local _idcInfo = "" -- 主要用于多区域部署时业务可由后台下发IP域名配置，json格式，由midas提供。为空时会取客户端预埋IP进行连接(非必传)

--移动端防高频点击
local _bPay = false
local _payTimerHandle = nil
local _payTimeout = 30

-- 打点上报相关
local _ProductId = 0

CentauriGarenaLogic.AddListener = function ()
    Module.Login.Config.Events.evtOnLoginSuccess:AddListener(this._OnLoginSuccess)
    Server.PayServer.Events.evtRechargeDataChanged:AddListener(this._OnServerRechargeDataChanged)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:AddListener(this._OnGCloudSDKWebBrowserCallback)
    Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserCallback:AddListener(this._OnSDKWebBrowserCallback)

end

CentauriGarenaLogic.RemoveListener = function ()
    Module.Login.Config.Events.evtOnLoginSuccess:RemoveListener(this._OnLoginSuccess)
    Server.PayServer.Events.evtRechargeDataChanged:RemoveListener(this._OnServerRechargeDataChanged)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:RemoveListener(this._OnGCloudSDKWebBrowserCallback)
    Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserCallback:RemoveListener(this._OnSDKWebBrowserCallback)

end

local function find_region(data, region)
    for _, value in pairs(data) do
        if string.find(value.RegionCode,region) ~= nil then
            return value
        end
    end
    return nil
end
CentauriGarenaLogic._OnLoginSuccess = function()
    if not IsBuildRegionGA() then
        return
    end
    CentauriGarenaLogic._Init()
end
CentauriGarenaLogic._Init = function ()
    if CentauriGarenaLogic.UseMShop() then
        _region = Server.PayServer:GetRegistrationRegion()
        _access_token = Server.SDKInfoServer:GetAccessToken()
        local dt = Facade.TableManager:GetTable("GarenaPayUrl")
        if dt then
            logerror("CentauriGarenaLogic start init region " .. _region)
            local payUrlConfig = find_region(dt, _region)
            if not payUrlConfig then
                logerror("CentauriGarenaLogic not find region, try SG")
                payUrlConfig = find_region(dt, "SG")
            end
            if not payUrlConfig then
                logerror("CentauriGarenaLogic:_OnLoginSuccess payUrlConfig is nil")
                return
            end
            if VersionUtil.IsShipping() then
                _pc_url = payUrlConfig.ProductionURL
            else
                _pc_url = payUrlConfig.SandboxURL
            end
        else
            return
        end
        local source = Server.SDKInfoServer:IsAndroidSelfPublishChannel() and "emb" or "epc"
        _pc_url = _pc_url ..
            (_pc_url:sub(-1) ~= "?" and "&" or "") .. 
                "source=" .. source .. 
                "&app_id=" .. _appid ..    
                "&access_token=" .. _access_token
        if not string.find(_pc_url, "region=") then
            _pc_url = _pc_url .. "&region=" .. _region
        end 
        return
    end
    local openId = Server.SDKInfoServer:GetOpenIdStr()
    local zoneId = Server.SDKInfoServer:GetZoneId() --游戏服务器大区id,游戏不分大区则默认zoneId ="1"
    local offerId = Server.PayServer:GetOfferId()
    if VersionUtil.IsShipping() then
        _env = "release"
        _idc = "singaporegarena"
    end
    local pay_token = Server.PayServer:GetPayToken()
    logerror("CentauriGarenaLogic _OnLoginSuccess offerId:", offerId, " zoneid:", zoneId, " pf", pay_token.pf)
    _garenaIns.IDC = _idc
    _garenaIns.IDCInfo = _idcInfo
    _garenaIns.Env = _env

    _garenaIns.OpenId = openId
    _garenaIns.PF = pay_token.pf
    _garenaIns.ZoneId = zoneId .. "_0"
    _garenaIns.OfferId = offerId
    _garenaIns.CurContry = "TW"
    _garenaIns.CurCurrency = "TWD"
    _garenaIns.payChannel = "os_garena"
    local extras = "serverId=" .. zoneId .. "&roleId=0&local=zh_TW"
    if PLATFORM_IOS then
        extras = extras .. "&prefix=garena.df.dc."
    end
    _garenaIns.ChannelExtras = extras
    _garenaIns:Init()
end

CentauriGarenaLogic.GetProductInfo = function (productIdList)
    if CentauriGarenaLogic.UseMShop() then
        return
    end
    logerror("CentauriGarenaLogic GetProductInfo")
    _garenaIns:GetProductInfo()
    CentauriGarenaLogic._AddGetProductInfoLoading()
end

CentauriGarenaLogic.OnGetProductInfoFinished = function (_, respInfo)
    CentauriGarenaLogic._RemoveGetProductInfoLoading()
    --respInfo = [[{"ret":"0","msg":"succ","productInfo":[{"name":" Delta Coin × 60","itemId":"1","appPoints":60,"iconUrl":"https:\/\/cdn-gop.garenanow.com\/gop\/app\/test\/0000\/100\/151\/point.png","price":"HK$8.00","isPromo":false,"promoPoints":0,"oneTimePromoPoint":0,"rebateCardId":0,"appItemId":0,"pointName":" Delta Coin","priceCode":"HKD","eventId":"","microPrice":8000000,"originalMicroPrice":8000000,"promo_amount":0,"id":"1","isInPromotion":false,"rebateId":0,"app_item_id":0,"app_point_amount":60,"one_time_promo_amount":0},{"name":" Delta Coin × 300","itemId":"2","appPoints":300,"iconUrl":"https:\/\/cdn-gop.garenanow.com\/gop\/app\/test\/0000\/100\/151\/point.png","price":"HK$38.00","isPromo":false,"promoPoints":0,"oneTimePromoPoint":0,"rebateCardId":0,"appItemId":0,"pointName":" Delta Coin","priceCode":"HKD","eventId":"","microPrice":38000000,"originalMicroPrice":38000000,"promo_amount":0,"id":"2","isInPromotion":false,"rebateId":0,"app_item_id":0,"app_point_amount":300,"one_time_promo_amount":0},{"name":" Delta Coin × 680","itemId":"3","appPoints":680,"iconUrl":"https:\/\/cdn-gop.garenanow.com\/gop\/app\/test\/0000\/100\/151\/point.png","price":"HK$79.90","isPromo":false,"promoPoints":0,"oneTimePromoPoint":0,"rebateCardId":0,"appItemId":0,"pointName":" Delta Coin","priceCode":"HKD","eventId":"","microPrice":79900000,"originalMicroPrice":79900000,"promo_amount":0,"id":"3","isInPromotion":false,"rebateId":0,"app_item_id":0,"app_point_amount":680,"one_time_promo_amount":0},{"name":" Delta Coin × 1280","itemId":"4","appPoints":1280,"iconUrl":"https:\/\/cdn-gop.garenanow.com\/gop\/app\/test\/0000\/100\/151\/point.png","price":"HK$149.00","isPromo":false,"promoPoints":0,"oneTimePromoPoint":0,"rebateCardId":0,"appItemId":0,"pointName":" Delta Coin","priceCode":"HKD","eventId":"","microPrice":149000000,"originalMicroPrice":149000000,"promo_amount":0,"id":"4","isInPromotion":false,"rebateId":0,"app_item_id":0,"app_point_amount":1280,"one_time_promo_amount":0},{"name":" Delta Coin × 3280","itemId":"5","appPoints":3280,"iconUrl":"https:\/\/cdn-gop.garenanow.com\/gop\/app\/test\/0000\/100\/151\/point.png","price":"HK$389.00","isPromo":false,"promoPoints":0,"oneTimePromoPoint":0,"rebateCardId":0,"appItemId":0,"pointName":" Delta Coin","priceCode":"HKD","eventId":"","microPrice":389000000,"originalMicroPrice":389000000,"promo_amount":0,"id":"5","isInPromotion":false,"rebateId":0,"app_item_id":0,"app_point_amount":3280,"one_time_promo_amount":0},{"name":" Delta Coin × 6480","itemId":"6","appPoints":6480,"iconUrl":"https:\/\/cdn-gop.garenanow.com\/gop\/app\/test\/0000\/100\/151\/point.png","price":"HK$769.00","isPromo":false,"promoPoints":0,"oneTimePromoPoint":0,"rebateCardId":0,"appItemId":0,"pointName":" Delta Coin","priceCode":"HKD","eventId":"","microPrice":769000000,"originalMicroPrice":769000000,"promo_amount":0,"id":"6","isInPromotion":false,"rebateId":0,"app_item_id":0,"app_point_amount":6480,"one_time_promo_amount":0}]}]]
    if string.isempty(respInfo) then
        logerror("CentauriGarenaLogic.OnGetProductInfoFinished respInfo is nil")
        return
    end
    local resp = Json.decode(respInfo)
    if resp == nil then
        logerror("CentauriGarenaLogic.OnGetProductInfoFinished respInfo not json :", respInfo)
        return
    end
    if resp.ret ~= "0" and resp.ret ~= 0 then 
        logerror("CentauriGarenaLogic.OnGetProductInfoFinished resultCode:", resp.ret)
        return
    end
    Server.PayServer:RefreshProductInfo(resp.productInfo)
end

CentauriGarenaLogic._AddPayLock = function ()
    logerror("CentauriGarenaLogic._AddPayLock _bPay = true")
    _bPay = true
    _payTimerHandle = Timer.DelayCall(_payTimeout, function ()
        logerror("CentauriGarenaLogic._AddPayLock Timeout _bPay = false")
        _bPay = false
    end)
end

CentauriGarenaLogic._AddGetProductInfoLoading = function ()
    if IsMobile() and IsBuildRegionGA() then
        logerror("Pay CentauriGarenaLogic:AddGetProductInfoLoading")
        if CentauriGarenaLogic._getProductInfoLoading then
            logerror("Pay CentauriGarenaLogic:AddGetProductInfoLoading is loading now")
            return
        end

        if CentauriGarenaLogic._getProductInfoLoadingTimer then
            Timer.CancelDelay(CentauriGarenaLogic._getProductInfoLoadingTimer)
            CentauriGarenaLogic._getProductInfoLoadingTimer = nil
        end

        --15s后自动去掉锁
        CentauriGarenaLogic._getProductInfoLoadingTimer = Timer.DelayCall(15, function()
            logerror("Pay CentauriGarenaLogic:AddGetProductInfoLoading RemoveGetProductInfoLoading by Timer")
            LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(false)
        end, CentauriGarenaLogic)
        --LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(true)
        CentauriGarenaLogic._getProductInfoLoading = true
    end
end

CentauriGarenaLogic._RemoveGetProductInfoLoading = function ()
    if IsMobile() and IsBuildRegionGA() then
        logerror("Pay CentauriGarenaLogic:RemoveGetProductInfoLoading")
        if CentauriGarenaLogic._getProductInfoLoading then
            if CentauriGarenaLogic._getProductInfoLoadingTimer then
                Timer.CancelDelay(CentauriGarenaLogic._getProductInfoLoadingTimer)
                CentauriGarenaLogic._getProductInfoLoadingTimer = nil
            end
            LuaGlobalEvents.evtShowProtoWaitLoading:Invoke(false)
            CentauriGarenaLogic._getProductInfoLoading = false
        end
    end
end

CentauriGarenaLogic.IsGetProductInfoLoading = function ()
    if IsMobile() and IsBuildRegionGA() then
        return CentauriGarenaLogic._getProductInfoLoading
    end
    return false
end

--去除支付锁
CentauriGarenaLogic._RemovePayLock = function ()
    logerror("CentauriGarenaLogic._RemovePayLock _bPay = false")
    _bPay = false
    if _payTimerHandle then
        logerror("CentauriGarenaLogic._RemovePayLock CancelDelay")
        Timer.CancelDelay(_payTimerHandle)
        _payTimerHandle = nil
    end
end

CentauriGarenaLogic.OnPayFinished = function(resultCode, resultMsg)
    if PLATFORM_IOS or PLATFORM_ANDROID then
        CentauriGarenaLogic._RemovePayLock()
        -- GooglePlay和苹果IAP上报
        if resultCode == 0 then
            if not CentauriGarenaLogic.UseMShop() then
                local productId = CentauriGarenaLogic.GetCachedProductId()
                local num = 0
                if productId and productId ~= 0 then
                    local item = Server.PayServer:GetRechargeItem(productId)
                    if item == nil then
                        item = Server.PayServer:GetProductItem(productId)
                    end
                    if item then
                        num = item.original_price
                        local multiplier = 100
                        if item.decimal_point then
                            multiplier = 10 ^ item.decimal_point
                        end
                        num = num / multiplier
                        logerror("PayModule:OnGarenaPayFinished ProductId OriginalPrice DecimalPoint", productId, item.original_price, item.decimal_point)
                    else
                        logerror("PayModule:OnGarenaPayFinished Cached ProductId is Invalid", productId)
                        return
                    end
                end
                Module.GCloudSDK:OnPaySucceedReport(Server.PayServer:GetCurrencyCode(), num)
            end
        end
    end
end

CentauriGarenaLogic.Pay = function (productId)

    if CentauriGarenaLogic.UseMShop() then
        logerror("[CentauriGarenaLogic] Pay mshop url:", _pc_url)
        local webContentTitle = Module.Pay.Config.Loc.WebViewTitle
        if PLATFORM_WINDOWS and IsBuildRegionGA() then
            local scale = 0.8
            Module.GCloudSDK:OpenSDKWebBrowserUrl({url = _pc_url .. "&app_point_amount=" .. productId, title = webContentTitle, width_scale = scale, height_scale = scale})
        else
            Module.GCloudSDK:OpenUrl(_pc_url .. "&app_point_amount=" .. productId, 1, false,
            true, '{\"webview_window_scale\":1}', false, { useSDKWebBrowser = true })
        end
        return
    end
    if PLATFORM_IOS or PLATFORM_ANDROID then
        if _bPay then
            logerror("CentauriGarenaLogic.Pay _bPay")
            Module.CommonTips:ShowSimpleTip(Module.Pay.Config.Loc.NotPayFinish)
            return
        end
        CentauriGarenaLogic._RemovePayLock()
        CentauriGarenaLogic._AddPayLock()
    end
    logerror("[CentauriGarenaLogic] Pay productId :", productId)
    _garenaIns.productId = productId
    _ProductId = productId
    _garenaIns:Pay()
end

CentauriGarenaLogic.PayGoods = function (productId)

    if CentauriGarenaLogic.UseMShop() then
        logerror("[CentauriGarenaLogic] Pay mshop url:", _pc_url)
        local scale = 0.8
        local viewportSize = UWidgetLayoutLibrary.GetViewportSize(GetWorld())
        local webContentWidth = viewportSize.X * scale
        local webContentHeight = viewportSize.Y * scale
        local webContentTitle = Module.Pay.Config.Loc.WebViewTitle

        if PLATFORM_WINDOWS and IsBuildRegionGA() then
            Module.GCloudSDK:OpenSDKWebBrowserUrl({url = _pc_url .. "&app_item_id=" .. productId, title = webContentTitle, width_scale = scale, height_scale = scale})
        else
            Module.GCloudSDK:OpenUrl(_pc_url .. "&app_item_id=" .. productId, 1, false,
                true, '{\"webview_window_scale\":1}', false, { useSDKWebBrowser = true })
        end
        Module.GCloudSDK:OnPayMidasbuyReport()
        return
    end
    logerror("[CentauriGarenaLogic] PayGoods productId :", productId)
    _garenaIns.productId = productId
    _ProductId = productId
    _garenaIns:PayGoods()
end

CentauriGarenaLogic._OnServerRechargeDataChanged = function ()
    local rechargeItemList = Server.PayServer:GetRechargeItemList()
    logerror("[CentauriGarenaLogic] _OnServerRechargeDataChanged")
    if rechargeItemList == nil then
        logerror("CentauriLogic._OnServerRechargeDataChanged rechargeItemList is nil")
        return
    end
    local productIdList = {}
    for _, rechargeItem in pairs(rechargeItemList) do
        table.insert(productIdList, rechargeItem.product_id)
    end
    CentauriGarenaLogic.GetProductInfo(productIdList)
end

CentauriGarenaLogic._OnGCloudSDKWebBrowserCallback = function (type, jsonData)
    loginfo("CentauriGarenaLogic._OnThirdSDKWebBrowserCallback type:", type, " jsonData:", jsonData)
    if jsonData then
        local JsonData = Json.decode(jsonData)
        if JsonData.goToRecharge then
            Module.Store:ShowStoreRechargeMainPanle()
        end
    end
end

CentauriGarenaLogic._OnSDKWebBrowserCallback = function (param)
    loginfo("CentauriGarenaLogic._OnSDKWebBrowserCallback param:", param)
    if param == "goToRecharge" then
        loginfo("CentauriGarenaLogic._OnSDKWebBrowserCallback goToRecharge")
        Module.Store:ShowStoreRechargeMainPanle()
    end
end


CentauriGarenaLogic.UseMShop = function ()
    return IsHD() or Server.SDKInfoServer:IsAndroidSelfPublishChannel()
end

CentauriGarenaLogic.GetCachedProductId = function() 
    return _ProductId
end

return CentauriGarenaLogic
