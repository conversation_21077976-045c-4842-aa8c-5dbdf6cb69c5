---@class QuestSeasonLine : LuaObject
local QuestSeasonLineStruct = class("QuestSeasonLineStruct", LuaObject)

---@class SeasonLineStruct
---@field public seasonLineId number
---@field public name string
---@field public desc string
---@field public curSeasonID number
---@field public curServerStamp number
---@field public seasonInfos table
---@field public table_StageArry SeasonLineStage[]
---@field public table_GroupArry SeasonLineGroup[]
---@field public _unLockConditionList table
---@field public _overConditionList table

---@class SeasonLineStage
---@field public stageID number
---@field public name string
---@field public desc string
---@field public order number
---@field public mainGroup number
---@field public branchIdArr table
---@field public unlockNeedStar number

---@class SeasonLineGroup
---@field public groupID number
---@field public name string
---@field public type number
---@field public order number
---@field public questIdArry table
---@field public rewardStar number
---@field public unlockTime number

function QuestSeasonLineStruct:Ctor(lineCfg)
    self.seasonLineId = lineCfg.LineID
    self.name = lineCfg.Name
    self.desc = lineCfg.Desc

    -- 当前的赛季ID
    self.curSeasonID = 0
    -- 服务器时间戳
    self.curServerStamp = 0
    -- 所有的赛季信息
    self.seasonInfos = {}

    self:InitSeasonLineCfg(lineCfg)
end

function QuestSeasonLineStruct:HotFixQuestLine(lineCfg)
    self.seasonLineId = lineCfg.LineID
    self.name = lineCfg.Name
    self.desc = lineCfg.Desc
    self:InitSeasonLineCfg(lineCfg)
end

function QuestSeasonLineStruct:InitSeasonLineCfg(lineCfg)
    self.table_SeasonIdAry = {}
    for _, value in ipairs(lineCfg.SeasonIdArr) do
        table.insert(self.table_SeasonIdAry, value)
    end

    self.table_StageArry = {}
    self.table_GroupArry = {}
    for _, value in ipairs(lineCfg.SeasonStageIdArr) do
        local stageCfg = Facade.TableManager:GetRowByKey("SeasonQuestStage", tostring(value))
        if stageCfg then
            local list = {}
            table.insert(list, stageCfg.StageMainGroup)
            table.append(list, stageCfg.StageSubGroupArr)
            for _, groupId in ipairs(list) do
                local groupCfg = Facade.TableManager:GetRowByKey("SeasonQuestGroup", tostring(groupId))
                if groupCfg then
                    self:InitSeasonGroupCfg(groupCfg)
                end
            end

            self:InitSeasonStageCfg(stageCfg)
        end
    end

    self._unLockConditionList = {}
    self._unLockConditionList[lineCfg.FinalQuestUnlockType1] = {
        param = lineCfg.FinalQuestUnlockParam1,
        desc = lineCfg.FinalQuestUnlockDesc1
    }
    self._unLockConditionList[lineCfg.FinalQuestUnlockType2] = {
        param = lineCfg.FinalQuestUnlockParam2,
        desc = lineCfg.FinalQuestUnlockDesc2
    }
    self._unLockConditionList[lineCfg.FinalQuestUnlockType3] = {
        param = lineCfg.FinalQuestUnlockParam3,
        desc = lineCfg.FinalQuestUnlockDesc3
    }

    self._overConditionList = {}
    self._overConditionList[lineCfg.OverTimeType1] = {
        param = lineCfg.OverTimeParam1,
        desc = lineCfg.OverTimeDesc1
    }
    self._overConditionList[lineCfg.OverTimeType2] = {
        param = lineCfg.OverTimeParam2,
        desc = lineCfg.OverTimeDesc2
    }
    self._overConditionList[lineCfg.OverTimeType3] = 
    {
        param = lineCfg.OverTimeParam3,
        desc = lineCfg.OverTimeDesc3
    }

    self._seasonQuestGuideImg1 = lineCfg.SeasonQuestGuideImg1
    self._seasonQuestGuideImg2 = lineCfg.SeasonQuestGuideImg2
    self._seasonQuestGuideImg3 = lineCfg.SeasonQuestGuideImg3

    self._overTimeRewardID = lineCfg.OverTimeRewardID
    self._overTimeRewardCount = lineCfg.OverTimeRewardCount
    self._finalQuestID = lineCfg.FinalQuestID

    -- 任务线开启限制（任务id列表）
    self.openLimits = lineCfg.OpenLimits
    -- 未开启是否显示
    self.bUnopenShow = lineCfg.IsUnopenShow
    -- 任务线解锁玩家必须达到的SOL等级
    self.openLevel = lineCfg.OpenLevel
end

function QuestSeasonLineStruct:InitSeasonStageCfg(stageCfg)
    self.table_StageArry[stageCfg.StageID] = {
        stageID = stageCfg.StageID,
        name = stageCfg.Name,
        desc = stageCfg.Desc,
        order = stageCfg.StageSequence,
        mainGroup = stageCfg.StageMainGroup,
        branchIdArr = stageCfg.StageSubGroupArr,
        unlockNeedStar = stageCfg.StageUnlockStarCount
    }
end

function QuestSeasonLineStruct:InitSeasonGroupCfg(groupCfg)
    self.table_GroupArry[groupCfg.GroupID] = {
        groupID = groupCfg.GroupID,
        name = groupCfg.Name,
        type = groupCfg.GroupType,
        order = groupCfg.Sequence,
        questIdArry = groupCfg.QuestIdArr,
        rewardStar = groupCfg.GropStarCount,
        unlockTime = groupCfg.GropUnlockTime
    }
end

function QuestSeasonLineStruct:UpdateQuestLineData(cur_season_id, season_info, serverStamp)
    if #self.table_SeasonIdAry > 0 then
        self.curSeasonID = cur_season_id
        self.curServerStamp = serverStamp
        self.seasonInfos = season_info
    end
end

function QuestSeasonLineStruct:IsCurSeasonLine()
    local isCurSeasonLine = false
    if #self.table_SeasonIdAry > 0 then
        for _, value in ipairs(self.table_SeasonIdAry) do
            if value == self.curSeasonID then
                isCurSeasonLine = true
            end
        end
    end
    return isCurSeasonLine
end

function QuestSeasonLineStruct:GetSeasonStartTime()
    local startTime = 0
    if self:IsCurSeasonLine() and self.curSeasonID > 0 then
        for key, value in pairs(self.seasonInfos) do
            if table.contains(self.table_SeasonIdAry, value.season_id) and value.start_stamp > startTime then
                startTime = value.start_stamp
            end
        end
    end
    return startTime
end

function QuestSeasonLineStruct:GetSeasonTimeDelta()
    local deltaTime = 0
    local endTime = 0
    if self:IsCurSeasonLine() and self.curSeasonID > 0 then
        for key, value in pairs(self.seasonInfos) do
            if table.contains(self.table_SeasonIdAry, value.season_id) and value.end_stamp > endTime then
                endTime = value.end_stamp
            end
        end
    end
    deltaTime = endTime - Facade.ClockManager:GetLocalTimestamp()
    return deltaTime
end

function QuestSeasonLineStruct:IsSeasonLineOpen()
    local seasonLevel = Server.RoleInfoServer.seasonLevel
    local openLevel = self.openLevel
    return seasonLevel >= openLevel
end

--#region 任务线相关

function QuestSeasonLineStruct:CalGainStar()
    local star = 0
    for index, value in pairs(self.table_StageArry) do
        star = star + self:CalGainStarByStageID(index)
    end
    return star
end

--#endregion
--#region 阶段相关
function QuestSeasonLineStruct:GetStageIDList()
    return table.getkeys(self.table_StageArry)
end

function QuestSeasonLineStruct:GetStageInfoByID(stageId)
    return self.table_StageArry[stageId]
end

function QuestSeasonLineStruct:GetMainGroupIDByStageID(stageId)
    local stageInfo = self:GetStageInfoByID(stageId)
    if stageInfo then
        return stageInfo.mainGroup
    end
    return -1
end

function QuestSeasonLineStruct:GetBranchGroupIDsByStageID(stageId)
    local stageInfo = self:GetStageInfoByID(stageId)
    if stageInfo then
        return stageInfo.branchIdArr
    end
    return {}
end

function QuestSeasonLineStruct:GetSortedBranchGroupByStageIDAndType(stageId, type)
    local branchGroups = self:GetBranchGroupIDsByStageID(stageId)
    local res = {}
    for index, value in ipairs(branchGroups) do
        local group = self:GetGroupInfo(value)
        if(group.type == type) then
            res[group.order] = value
        end
    end
    return res
end

function QuestSeasonLineStruct:GetAllGroupIDsByStageID(stageId)
    local stageInfo = self:GetStageInfoByID(stageId)
    local list = {}
    if stageInfo then
        table.insert(list, stageInfo.mainGroup)
        table.append(list, stageInfo.branchIdArr)
    end
    return list
end

function QuestSeasonLineStruct:CalGainStarByStageID(stageId)
    local groupIds = self:GetAllGroupIDsByStageID(stageId)
    local starNum = 0
    for index, value in ipairs(groupIds) do
        if self:IsGroupComplete(value) then
            starNum = starNum + self:GetGroupInfo(value).rewardStar
        end
    end
    return starNum
end

function QuestSeasonLineStruct:CalTotalStarByStageID(stageId)
    local groupIds = self:GetAllGroupIDsByStageID(stageId)
    local starNum = 0
    for index, value in ipairs(groupIds) do
        starNum = starNum + self:GetGroupInfo(value).rewardStar
    end
    return starNum
end

function QuestSeasonLineStruct:GetPrevStageInfoByID(stageId)
    local curStage = self:GetStageInfoByID(stageId)
    
    if curStage == nil then
        return nil
    end

    local order = self:GetStageInfoByID(stageId).order
    if order <= 1 then
        return nil
    end
    local stageInfo = nil
    for index, value in pairs(self.table_StageArry) do
        if value and value.order == order - 1 then
            stageInfo = value
        end
    end
    return stageInfo
end

function QuestSeasonLineStruct:GetNextStageInfoByID(stageId)
    local order = self:GetStageInfoByID(stageId).order
    local stageInfo = nil
    for index, value in pairs(self.table_StageArry) do
        if value.order == order + 1 then
            stageInfo = value
        end
    end
    return stageInfo
end

function QuestSeasonLineStruct:IsUnLockByStageID(stageId)

    local stageInfo = self:GetStageInfoByID(stageId)

    if stageInfo == nil then
        return false
    end

    if stageInfo.order == 1 then
        return true
    end

    local prevStageInfo = self:GetPrevStageInfoByID(stageId)
    local prevStar = 0
    local isPreMainComplete = nil
    if prevStageInfo then
        prevStar = self:CalGainStarByStageID(prevStageInfo.stageID)
        local mainGroupId = self:GetMainGroupIDByStageID(prevStageInfo.stageID)
        isPreMainComplete = self:IsGroupComplete(mainGroupId)
    end
    return self.table_StageArry[stageId].unlockNeedStar <= prevStar and isPreMainComplete
end

function QuestSeasonLineStruct:GetStageIDByQuestID(questId)
    local groupId = self:GetGroupIDByQuestID(questId)
    local stageId = 0
    for key, value in pairs(self.table_StageArry) do
        if value.mainGroup == groupId or table.contains(value.branchIdArr, groupId) then
            stageId = key
            break
        end
    end
    return stageId
end

function QuestSeasonLineStruct:GetStageIDByGroupID(groupId)
    local stageId = 0
    for key, value in pairs(self.table_StageArry) do
        if value.mainGroup == groupId or table.contains(value.branchIdArr, groupId) then
            stageId = key
            break
        end
    end
    return stageId
end

--#endregion

--#region 组相关
function QuestSeasonLineStruct:GetGroupIDList()
    return table.getkeys(self.table_GroupArry)
end

function QuestSeasonLineStruct:GetGroupInfo(groupId)
    return self.table_GroupArry[groupId]
end

function QuestSeasonLineStruct:GetAllQuestIdsByGroupID(groupId)
    local groupInfo = self:GetGroupInfo(groupId)
    if not groupInfo then
        return {}
    end
    return groupInfo.questIdArry
end

function QuestSeasonLineStruct:IsMainGroup(stageId, groupId)
    local mainGroup = self:GetMainGroupIDByStageID(stageId)
    return mainGroup == groupId
end

function QuestSeasonLineStruct:IsBranchGroyp(stageId, groupId)
    local branchArr = self:GetBranchGroupIDsByStageID(stageId)
    return table.contains(branchArr, groupId)
end

function QuestSeasonLineStruct:IsGroupComplete(groupId)
    local groupInfo = self:GetGroupInfo(groupId)
    if not groupInfo then
        return
    end
    return Server.QuestServer:IsQuestCompleted(groupInfo.questIdArry)
end

function QuestSeasonLineStruct:GetCompletedQuestNum(groupId)
    local questIds = self:GetAllQuestIdsByGroupID(groupId)
    local num = 0
    for index, value in ipairs(questIds) do
        local questInfo = Server.QuestServer:GetQuestInfoById(value)
        if questInfo then
            if questInfo.state >= QuestState.Completed then
                num = num + 1
            end
        end
    end
    return num
end

function QuestSeasonLineStruct:GetGroupIDByQuestID(questId)
    local groupId = 0
    for key, value in pairs(self.table_GroupArry) do
        if table.contains(value.questIdArry, questId) then
            groupId = key
            break
        end
    end
    return groupId
end

function QuestSeasonLineStruct:IsUnlockByGroupId(groupId)
    local deltaTime = self:GetUnlockTimeByGroupId(groupId)
    if deltaTime <= 0 then 
        local rootQuestInfo = self:GetRootQuestByGroupId(groupId)
        rootQuestInfo = Server.QuestServer:GetQuestInfoById(rootQuestInfo)
        if rootQuestInfo and rootQuestInfo.state <= QuestState.Unread then
            rootQuestInfo:UpdateState(QuestState.Unaccepted)
        end
        return true
    else
        return false
    end
end

function QuestSeasonLineStruct:GetUnlockTimeByGroupId(groupId)
    local groupInfo = self:GetGroupInfo(groupId)
    if groupInfo == nil then
        return 0
    end
    local seasonStartTime = Server.QuestServer:GetCurrentSeasonStartTime()
    local seasonStartTime = 0
    local deltaTime = (groupInfo.unlockTime * 60) - (Facade.ClockManager:GetLocalTimestamp() - seasonStartTime)  
    return deltaTime
end

function QuestSeasonLineStruct:GetRootQuestByGroupId(groupId)
    local groupInfo = self:GetGroupInfo(groupId)
    if groupInfo == nil then
        return -1
    end
    return groupInfo.questIdArry[1]
end
--#endregion

function QuestSeasonLineStruct:ForechSetQuestData(callback)
    for key, value in pairs(self.table_GroupArry) do
       for _, questId in ipairs(value.questIdArry) do
            if callback then
                callback(questId, self.seasonLineId, self:GetStageIDByGroupID(key), key)
            end
       end
    end
end

--#region 收集者相关

--#endregion

--#region 命运契约相关

--#endregion
return QuestSeasonLineStruct
