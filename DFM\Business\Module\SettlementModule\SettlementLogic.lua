----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

local function xxwwinfo(...)
    loginfo("[xxww] ", ...)
end

local function xxwwwarning(...)
    logwarning("[xxww] @warning@", ...)
end

local function xxwwerror(...)
    logerror("[xxww] @error@", ...)
end

local function printtable(t, prefix)
    xxwwinfo(prefix)
    logtable(t, true)
end

-- local FLuaHelper = import "FLuaHelper"
local AchievementTable = Facade.TableManager:GetTable("AchievementDescConfig")
local UIManager = Facade.UIManager
local SettlementConfig = require("DFM.Business.Module.SettlementModule.SettlementConfig")
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ELoadingDestination = import "ELoadingDestination"
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local ULuaExtension = import "LuaExtension"
local UDFMGameTss = import "DFMGameTss"
local DFMGameTss = UDFMGameTss.Get(GetGameInstance())

-- local EMatchOverReason =
-- {
-- 	Escaped = 0,--	// 成功逃离
-- 	Dead = 1,	--	// 死亡
-- 	TimeOver = 2,--	// 超时
-- 	Quit = 3, --		// 主动退出
-- 	GiveUp = 4,	--	// 局外放弃比赛
-- 	Complete =5,--	//pve任务完成
-- 	Fail = 6,	--	//pve任务失败
-- };
local EMatchOverReason = import "EMatchOverReason"
local UDFMSettlementManager = import "DFMSettlementManager"
local UGPGameplayGlobalDelegates = import "GPGameplayGlobalDelegates"
local UGPBattleFieldSystem = import "GPBattleFieldSystem"
local SettlementLogic = {}
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local UDFMGameLoadingManager = import("DFMGameLoadingManager")
local UDFMReplaySystemBlueprintHelper = import "DFMReplaySystemBlueprintHelper"
local UDFMGameGPM = import "DFMGameGPM"
local AGPSequenceActor = import "GPSequenceActor"
local ASettlementCutSceneFocusActor = import "SettlementCutSceneFocusActor"

--BEGIN MODIFICATION - VIRTUOS
local UDFMAchievementsManager = import "DFMAchievementsManager"
local DFMAchievementsManager = UDFMAchievementsManager.Get(GetWorld())
--END MODIFICATION - VIRTUOS

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

SettlementLogic.ShowPreSettlementTitleViewProcess = function(matchOverReason)
    xxwwinfo("SettlementLogic:ShowPreSettlementTitleViewProcess")
    if matchOverReason == EMatchOverReason.Dead then
        UIManager:AsyncShowUI(UIName2ID.PreEvacuateTitleWinView)
    elseif matchOverReason == EMatchOverReason.Escaped then
        UIManager:AsyncShowUI(UIName2ID.PreEvacuateTitleFailView)
    else
        xxwwinfo("SettlementLogic:ShowPreSettlementTitleViewProcess", matchOverReason)
    end
end

SettlementLogic.StartSettlement = function()
    SettlementConfig.Events.evtSettlementStart:Invoke()
end

SettlementLogic.EndSettlement = function()
    SettlementConfig.Events.evtSettlementEnd:Invoke()
end

SettlementLogic.SettlementEnterSpectateMode = function()
    --SettlementConfig.Events.evtSettlementEnterSpectateMode:Invoke()
    return SettlementLogic._SettlementEnterSpectateMode()
end

SettlementLogic.SettlementEnterReplayMode = function()
    return SettlementLogic._SettlementEnterReplayMode()
end
SettlementLogic.SettlementRepeatReplayMode = function()
    return SettlementLogic._SettlementRepeatReplayMode()
end

SettlementLogic.AddListener = function()
    Server.SettlementServer.Events.evtOnReqForSettlementInfoFailed:AddListener(SettlementLogic._OnReqForSettlementInfoFailed)
    Server.SettlementServer.Events.evtSOLSettlementDo:AddListener(SettlementLogic._OnSOLSettlementDo)
    Server.SettlementServer.Events.evtTDMSettlementDo:AddListener(SettlementLogic._OnTDMSettlementDo)
    Server.SettlementServer.Events.evtRaidSettlementDo:AddListener(SettlementLogic._OnRaidSettlementDo)
    Server.SettlementServer.Events.evtArenaSettlementDo:AddListener(SettlementLogic._OnArenaSettlementDo)
    Server.SettlementServer.Events.evtExceptionSettlementDo:AddListener(SettlementLogic._OnExceptionSettlementDo)
    Server.SettlementServer.Events.evtReceiveEmptyNtf:AddListener(SettlementLogic._OnReceiveEmptyNtf)
    SettlementConfig.Events.evtSettlementStart:AddListener(SettlementLogic._OnSettlementStart)
    SettlementConfig.Events.evtSettlementEnd:AddListener(SettlementLogic._OnSettlementEnd)
    SettlementConfig.Events.evtSettlementEnterSpectateMode:AddListener(SettlementLogic._SettlementEnterSpectateMode)
    SettlementConfig.Events.evtEliminationReplayEnd:AddListener(SettlementLogic._OnEliminationReplayEnd)
    SettlementConfig.Events.evtFinishLoadCutSceneLevel:AddListener(SettlementLogic._OnFinishLoadCutSceneLevel)
    local GameInst = GetGameInstance()
    UGPGameplayGlobalDelegates.Get(GameInst).OnPlayerExitSpectateMode:Clear()
    UGPGameplayGlobalDelegates.Get(GameInst).OnPlayerExitSpectateMode:Add(SettlementLogic._OnPlayerExitSpectateMode)
    UGPGameplayGlobalDelegates.Get(GameInst).OnKillcamEndShowSettlement:Clear()
    UGPGameplayGlobalDelegates.Get(GameInst).OnKillcamEndShowSettlement:Add(SettlementLogic._OnKillcamEndShowSettlement)

    --BEGIN MODIFICATION - VIRTUOS
    --登陆成功回调，查询平台成就
    if PLATFORM_GEN9 ==1 then
        Module.Login.Config.Events.evtOnLoginSuccess:AddListener(SettlementLogic._OnLoginSuccess)
    end
    --END MODIFICATION - VIRTUOS
end

SettlementLogic.AddListenerToClientPlayerMatchOver = function()
    xxwwinfo("SettlementLogic.AddListenerToClientPlayerMatchOver")
    local GameInst = GetGameInstance()
    UDFMGameplayDelegates.Get(GameInst).OnClientPlayerMatchOver:Remove(SettlementLogic._OnPlayerMatchOver)
    UDFMGameplayDelegates.Get(GameInst).OnClientPlayerMatchOver:Add(SettlementLogic._OnPlayerMatchOver)
end

SettlementLogic._OnPlayerMatchOver = function(matchOverReason)
    xxwwinfo("SettlementLogic._OnPlayerMatchOver")
    if DFMGameTss then
        local PlayerSettlementComponent = Facade.GameFlowManager:GetPlayerSettlementComponent()
        if isvalid(PlayerSettlementComponent) then
            PlayerSettlementComponent:ServerReceiveSecReportData(DFMGameTss:GetSdkCoreData())
        end
    end
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if isvalid(playerState) then
        local PlayerMatchInfo = playerState:GetPlayerMatchInfo()
        if isvalid(PlayerMatchInfo) then
            Module.Settlement.Field.AvgPing = PlayerMatchInfo.AvgPing
            Module.Settlement.Field.SDPing = PlayerMatchInfo.SDPing
        end
    end
    if matchOverReason == EMatchOverReason.MatchEnd then
        --local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        --if currentGameFlow == EGameFlowStageType.Game or currentGameFlow == EGameFlowStageType.GameSettlement then
        --    if InGameController:Get():IsMPMode() or InGameController:Get():IsConquestMode() then
        --        SettlementLogic._TryLoadSettlementCutSceneLevel()
        --    end
        --end
    elseif matchOverReason == EMatchOverReason.Quit then
        Timer.DelayCall(2, function()
            local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            if currentGameFlow == EGameFlowStageType.Game then
                if not Module.Guide:IsInNewPlayerMatch() then
                    SettlementLogic.EndSettlement()
                end
            end
        end)
    end
    if Facade.GameFlowManager:IsInOBMode() then
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
            if matchOverReason ~= EMatchOverReason.Quit then
                if InGameController:Get():IsSOLMode() then
                    Timer.DelayCall(20, function()
                        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
                            SettlementLogic.EndSettlement()
                        end
                    end)
                else
                    SettlementLogic.EndSettlement()
                end
            end
        end
    end
end

SettlementLogic.OnSubStageChangeEnterProcess = function(nextStageType)
    -- 当切换场景的时候做的逻辑 -- 比如去掉遮罩
end

SettlementLogic.GetExtractionPointName = function(escapePointEnum)
    xxwwinfo("SettlementLogic:GetExtractionPointName_", escapePointEnum)
    if not escapePointEnum or escapePointEnum < 0 then
        return "Error Extraction Place"
    end

    local function InitExitPointTable()
        local ExitPointTable = Facade.TableManager:GetTable("SOLExitConfig")
        local ExitPoint = {}
        for id, v in pairs(ExitPointTable) do
            ExitPoint[id] = v.PlayerExitName
        end
        return ExitPoint
    end

    local ExitPoint = InitExitPointTable()
    return ExitPoint[tostring(escapePointEnum)]
end

function SettlementLogic._OnExceptionSettlementDo(res)
    Module.CommonTips:ShowConfirmWindow(Module.Settlement.Config.Loc.SettlementException,
        function()
            if res.mode == ESettlementNtf.SNTF_RAID then
                Server.SettlementServer:ReqForRaidSettlementInfo(res.room_id)
            elseif res.mode == ESettlementNtf.SNTF_TDM then
                --大战场目前只有局内结算，和策划对的是先不展示
                --self:ReqForTDMSettlementInfo(res.room_id)
            elseif res.mode == ESettlementNtf.SNTF_SOL then
                Server.SettlementServer:ReqForSOLSettlementInfo(res.room_id)
            end
        end,
        function()
            local req = pb.CSSettlementGetHasUnsettledMatchReq:New()
            req.delete_room_id = res.room_id
            req:Request(function(res)
                if res.result == 0 then
                    xxwwinfo("SettlementLogic._OnExceptionSettlementDo not see")
                else
                    xxwwwarning("SettlementServer:ExceptionSettlementReq error res.result ==", res.result)
                end
            end, { maxWaitTime = 999 })
            SettlementConfig.Events.evtNotSeeExceptionSettlement:Invoke()
        end
    )
end

function SettlementLogic:_OnReceiveEmptyNtf()
    -- 如果收到EmptyNtf的时候还在局内，说明不是中退
    --local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    --if currentGameFlow == EGameFlowStageType.Game or currentGameFlow == EGameFlowStageType.GameSettlement then
    --    if InGameController:Get():IsMPMode() or InGameController:Get():IsConquestMode() then
    --        SettlementLogic._TryLoadSettlementCutSceneLevel()
    --    end
    --end
end

function SettlementLogic._OnRaidSettlementDo()
    local raidSettlementNtfInfo = Server.SettlementServer:GetRaidSettlementNtfInfo()
    local myPlayerId = Server.AccountServer:GetPlayerId()
    for _, player in pairs(raidSettlementNtfInfo.raid_player_status_array) do
        if tonumber(player.player_id) == tonumber(myPlayerId) then
            if player.leave == true then
                return
            end
        end
    end
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    settlementMgrIns:CloseForceQuit()
    Module.Settlement:SetInSettlementing(true)

    local raidSettlementUIIDList = nil

    if Server.SettlementServer:GetRaidTaskSucc() then
        raidSettlementUIIDList = {UIName2ID.RaidSuccess, UIName2ID.RaidTeamInfo, UIName2ID.RaidTeamDetailInfo}
    else
        raidSettlementUIIDList = {UIName2ID.RaidFail, UIName2ID.RaidTeamInfo, UIName2ID.RaidTeamDetailInfo}
    end

    Facade.SoundManager:PlayUIAudioEvent("Ingame_Sound_Mute")
    Module.Settlement.Field:SetRaidSettlementUIIDList(raidSettlementUIIDList)
    Facade.UIManager:AsyncShowUI(raidSettlementUIIDList[1])

    Module.Settlement.Config.Events.evtSettlementStart:Invoke()
end

function SettlementLogic._OnArenaSettlementDo()
    if Server.SettlementServer:GetArenaResult() == ArenaResult.AR_None then
        return
    end

    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    settlementMgrIns:CloseForceQuit()
    Module.Settlement.Field:SetArenaSettlementUIIDList({UIName2ID.ArenaResult, UIName2ID.ArenaResultDetail, UIName2ID.ArenaTeamInfo, UIName2ID.ArenaTeamDetailInfo})
    Facade.UIManager:AsyncShowUI(UIName2ID.ArenaResult)
    Module.Settlement.Config.Events.evtSettlementStart:Invoke()
end

function SettlementLogic._OnTDMSettlementDo()
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    settlementMgrIns:CloseForceQuit()
    settlementMgrIns:HideAllPlayerActor()
    Module.Settlement:SetInSettlementing(true)
    SettlementLogic.PreloadAchievementIcon()
    if not SettlementLogic.CheckNeedToLatelyLoadLevel() then
        SettlementLogic._DoTDMSettlement_Internal()
    else
        SettlementLogic._TryLoadSettlementCutSceneLevel()
        Module.Settlement.Field.delayStartSettlementTimerHandle = Timer.DelayCall(10, function()
            Module.Settlement.Field.delayStartSettlementTimerHandle = nil
            SettlementLogic._DoTDMSettlement_Internal()
        end)
    end
end

function SettlementLogic._DoTDMSettlement_Internal()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.DiscardVTDecal 1", nil)
    local UICacheList = {}
    if Server.SettlementServer:IsTDMCommanderGame() then
        table.insert(UICacheList, UIUtil.CreateCacheInfo(UIName2ID.TopTournament))
    else
        table.insert(UICacheList, UIUtil.CreateCacheInfo(UIName2ID.BF_Team))
    end
    table.insert(UICacheList, UIUtil.CreateCacheInfo(UIName2ID.GF_CaptureTips))
    table.insert(UICacheList, UIUtil.CreateCacheInfo(UIName2ID.GF_Tips))
    Facade.UIManager:BatchShowStackUI(UICacheList)
    Module.Settlement.Config.Events.evtSettlementStart:Invoke()

    --BEGIN MODIFICATION - VIRTUOS
    --解锁平台成就
    if PLATFORM_GEN9 == 1 and Server.SettlementServer:GetMPSettlementInfo() and Server.SettlementServer.tdmPlayerInfo then
        local playerInfo = Server.SettlementServer.tdmPlayerInfo[Server.AccountServer:GetPlayerId()]
        if playerInfo then
            for _, v in pairs(playerInfo.game_achievements) do
                --目前没有进度型成就，进度默认为100
                SettlementLogic.GrantPlatformAchievement(v.id, 100)
           end
        end
    end
     --END MODIFICATION - VIRTUOS

end

function SettlementLogic.CheckEndCutScene()
    xxwwinfo("SettlementLogic.CheckEndCutScene")
    if Module.Settlement.bEndCutscene == true then
        -- xxwwinfo("SettlementLogic.CheckEndCutScene bEndCutscene normal quit")
        -- local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
        -- if settlementMgrIns then
        --     settlementMgrIns:SettlementEndBroadcast()
        -- else
        --     logerror("settlementMgrIns is nil!")
        -- end
        xxwwinfo("SettlementLogic.CheckEndCutScene OK")
        SettlementLogic.animHandle:Release()
        SettlementLogic.animHandle = nil
        local UICacheList = {}
        table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.BreakthroughSuccess)
        Facade.UIManager:BatchShowStackUI(UICacheList)
        Module.Settlement.Config.Events.evtSettlementStart:Invoke()
    else
        xxwwinfo("SettlementLogic.CheckEndCutScene bEndCutscene == false")
    end
end

function SettlementLogic._OnTDMGetMap2CutSceneSector(mapid, offense_stronghold)
    if Module.Settlement.Field.cutSceneSector then
        return Module.Settlement.Field.cutSceneSector
    end
    local curSector = 1
    -- 这里判断使用哪个tag=EndS*的SequenceActor身上的Sequence来运镜
    if Module.Settlement.Config.Map2CutScene[mapid] ~= nil then
        if Module.Settlement.Config.Map2CutScene[mapid] == 0 then
            curSector = offense_stronghold + 1
            local CutSceneFVector = Module.Settlement.Config.MapID2CutSceneFVector[mapid]
            curSector = math.clamp(curSector, 1, #CutSceneFVector)
        else
            curSector = Module.Settlement.Config.Map2CutScene[mapid]
        end
        xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector find curSector=%d for mapid=%d", curSector, mapid))
    else
        xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector no lua config for mapid=%d", mapid))
        local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
        if Module.Settlement.Config.bUseEarlyLoadPlan then
            if Module.Settlement.Field.mapIdTagSector then
                curSector = Module.Settlement.Field.mapIdTagSector
                xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector use Module.Settlement.Field.mapIdTagSector=%d for mapid=%d", curSector, mapid))
            else
                curSector = offense_stronghold + 1
                if next(Module.Settlement.Field.sector2LevelSequence) then
                    local keys = table.keys(Module.Settlement.Field.sector2LevelSequence)
                    local max = math.max(table.unpack(keys))
                    local min = math.min(table.unpack(keys))
                    if curSector < min then
                        curSector = min
                    elseif curSector > max then
                        curSector = max
                    else
                        xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector fail to find level sequence for curSector=%d", curSector))
                    end
                else
                    xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutModule.Settlement.Field.sector2LevelSequence is empty"))
                end
            end
        else
            local cutSceneSectorForThisMap = settlementMgrIns:GetCutSceneSectorByMapID()
            if cutSceneSectorForThisMap > 0 then
                curSector = cutSceneSectorForThisMap
                xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector use cutSceneSectorForThisMap=%d for mapid=%d", curSector, mapid))
            else
                curSector = offense_stronghold + 1
                local sectors, min, max
                sectors, min, max = settlementMgrIns:GetAllCutSceneSectors(sectors, min, max)
                if sectors:Num() > 0 then
                    if not table.contains(sectors, curSector) then
                        if curSector < min then
                            curSector = min
                        elseif curSector > max then
                            curSector = max
                        else
                            xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector fail to find sequenceActor for curSector=%d", curSector))
                        end
                    end
                else
                    xxwwerror("SettlementLogic._OnTDMGetMap2CutSceneSector no sectors found")
                end
            end
        end
    end
    xxwwinfo(string.format("SettlementLogic._OnTDMGetMap2CutSceneSector finally use curSector=%d for mapid=%d", curSector, mapid))
    Module.Settlement.Field.cutSceneSector = curSector
    return curSector
end

function SettlementLogic._TryLoadSettlementCutSceneLevel()
    xxwwinfo("SettlementLogic:_TryLoadSettlementCutSceneLevel start load SettlementCutscene level")
    Module.Settlement.Field.bHasTriggerLoadLevelInThisGF = true
    local function fAllLevelFinishCallback(list, reason)
        if reason == EOperateStreamLevelReason.Success then
            xxwwinfo("SettlementLogic:_TryLoadSettlementCutSceneLevel succeeded")
            Module.Settlement.Field.bHasFinishLoadLevelInThisGF = true
            if SettlementLogic.CheckNeedToEarlyLoadLevel() then
                SettlementLogic.LoadLevelSequenceAndFocusLocation()
            end
            Module.Settlement.Config.Events.evtFinishLoadCutSceneLevel:Invoke()
        else
            xxwwerror("SettlementLogic:_TryLoadSettlementCutSceneLevel failed, reason = " .. tostring(reason))
        end
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevels({"SettlementCutscene"}, {}, true, nil, fAllLevelFinishCallback, false, 30)
end

function SettlementLogic._OnSOLSettlementInternal()
    xxwwinfo("SettlementLogic._OnSOLSettlementInternal")
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    settlementMgrIns:CloseForceQuit()
    ------------------------------------------------ShowUI------------------------------------------
    local UICacheList = {}
    local TabNameList = {}
    local endGameType = Server.SettlementServer:GetEscapeGameType()

    local TrophyViewID=nil
    if IsHD() then
        TrophyViewID=UIName2ID.EvacuatePrivateTrophyInfoViewHD
    else
        TrophyViewID=UIName2ID.EvacuatePrivateTrophyInfoView
    end

    if Server.SettlementServer.bIsGuide == true then
        if endGameType == EGspPlayerResultType.kGspPlayerResultEscaped then
            --table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.SOLGuideSuccess)
            UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.EvacuationResultInfo,TrophyViewID}
            TabNameList={Module.Settlement.Config.Loc.TabNamePersonal,Module.Settlement.Config.Loc.TabNameTrophy}

            xxwwinfo("Settlement Guide Succ")
        else
            --table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.SOLGuideFail)
            UICacheList={UIName2ID.EvacuateResultTip}

            xxwwinfo("Settlement1 Guide Fail")
        end
    else
        if Server.SettlementServer.settlementInfo and Server.SettlementServer.playerInfo then
            local EnumSettlementMode = Module.Settlement.Config.EnumSettlementMode
            if endGameType == EGspPlayerResultType.kGspPlayerResultEscaped then
                if table.nums(Server.SettlementServer.settlementInfo.teammate_array) <= 0 then
                    Server.SettlementServer.settlementMode = EnumSettlementMode.SingleEscape
                    --table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.SingleEscape)
                    UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.EvacuationResultInfo,TrophyViewID}
                    TabNameList={Module.Settlement.Config.Loc.TabNamePersonal,Module.Settlement.Config.Loc.TabNameTrophy}
                    xxwwinfo("Settlement1")
                else
                    Server.SettlementServer.settlementMode = EnumSettlementMode.MultiEscape
                    --table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.TeamEscape)
                    UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.EvacuationResultInfo,UIName2ID.EvacuateTeamInfoView,TrophyViewID}
                    TabNameList={Module.Settlement.Config.Loc.TabNamePersonal,Module.Settlement.Config.Loc.TabNameTeam,Module.Settlement.Config.Loc.TabNameTrophy}
                    xxwwinfo("Settlement2")
                end
            else
                if table.nums(Server.SettlementServer.settlementInfo.teammate_array) <= 0 then
                    Server.SettlementServer.settlementMode = EnumSettlementMode.SingleNotEscape
                    --table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.SingleFailed)
                    if endGameType==EGspPlayerResultType.kGspPlayerResultKilled then
                        UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.DeathDamageInfoViewNew,UIName2ID.EvacuationResultInfo}
                        TabNameList={Module.Settlement.Config.Loc.KilledDetail,Module.Settlement.Config.Loc.TabNamePersonal}
                    else
                        UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.EvacuationResultInfo}
                        TabNameList={Module.Settlement.Config.Loc.TabNamePersonal}
                    end

                    xxwwinfo("Settlement3")
                else
                    Server.SettlementServer.settlementMode = EnumSettlementMode.MultiNotEscape
                    --table.insertto(UICacheList, Module.Settlement.Config.SettlementMode2UI.TeamFail)
                    if endGameType==EGspPlayerResultType.kGspPlayerResultKilled then
                        UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.DeathDamageInfoViewNew,UIName2ID.EvacuationResultInfo,UIName2ID.EvacuateTeamInfoView}
                        TabNameList={Module.Settlement.Config.Loc.KilledDetail,Module.Settlement.Config.Loc.TabNamePersonal,Module.Settlement.Config.Loc.TabNameTeam}
                    else
                        UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.EvacuationResultInfo,UIName2ID.EvacuateTeamInfoView}
                        TabNameList={Module.Settlement.Config.Loc.TabNamePersonal,Module.Settlement.Config.Loc.TabNameTeam}
                    end

                    xxwwinfo("Settlement4")
                end
            end
        else
            xxwwerror("SettlementServer._OnSOLSettlementInternal settlement is fail!")
        end
    end

    -- azhengzheng:判断是否参加排位赛 暂时写法
    if false and Server.SettlementServer.playerInfo.is_ranked_match then -- 暂时关闭 排位赛移出局内
        if endGameType == EGspPlayerResultType.kGspPlayerResultEscaped then
            table.insert(UICacheList, #UICacheList, UIName2ID.SOLRankSettlement)
            table.insert(TabNameList, #TabNameList, Module.Settlement.Config.Loc.TabNameRank)
        else
            table.insert(UICacheList, UIName2ID.SOLRankSettlement)
            table.insert(TabNameList, Module.Settlement.Config.Loc.TabNameRank)
        end
    end

    Module.Settlement.Config.Events.evtSettlementStart:Invoke()

    xxwwinfo("Settlement UICacheList")
    ------------------------------------------------ShowUI------------------------------------------
    local fLoadFinCallback = function()
        SettlementConfig.Events.evtSettlementUILoadFinished:Invoke()
    end
    
    Module.Settlement:SetSettlementUIIDList(UICacheList)
    Module.Settlement:SetSwitchedTabList({})
    Facade.UIManager:AsyncShowUI(UICacheList[1],fLoadFinCallback)
    

    if IsHD() and #TabNameList>1 then
        Module.Settlement:SetSettlementTabNameList(TabNameList)
        -- Facade.UIManager:AsyncShowUI(UIName2ID.SettlementGroupTopHD)
        --[xxww] 挪到切gameflow的时候show
       -- Module.CommonBar:ShowBottomBar()
    end
    xxwwinfo("SettlementLogic:_OnSOLSettlementInternal client data OK!")
    Server.InventoryServer.Events.evtInventoryFetchFinished:RemoveListener(SettlementLogic._OnSOLSettlementInternal)
    
end

function SettlementLogic._OnSOLSettlementInternal_ForKillcam()
    UICacheList={UIName2ID.EvacuateResultTip,UIName2ID.DeathDamageInfoViewNew,UIName2ID.EvacuationResultInfo,UIName2ID.EvacuateTeamInfoView}
    TabNameList={Module.Settlement.Config.Loc.KilledDetail,Module.Settlement.Config.Loc.TabNamePersonal,Module.Settlement.Config.Loc.TabNameTeam}

    Module.Settlement.Config.Events.evtSettlementStart:Invoke()

    xxwwinfo("Settlement UICacheList")
    ------------------------------------------------ShowUI------------------------------------------
    local fLoadFinCallback = function()
        SettlementConfig.Events.evtSettlementUILoadFinished:Invoke()
    end
    
    Module.Settlement:SetSettlementUIIDList(UICacheList)
    Module.Settlement:SetSwitchedTabList({})
    Facade.UIManager:AsyncShowUI(UICacheList[1],fLoadFinCallback)
    

    if IsHD() and #TabNameList>1 then
        Module.Settlement:SetSettlementTabNameList(TabNameList)
        -- Facade.UIManager:AsyncShowUI(UIName2ID.SettlementGroupTopHD)
    end
end

function SettlementLogic._OnSOLSettlementDo()
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    settlementMgrIns:CloseForceQuit()
    local req = pb.CSSettlementGetHasUnsettledMatchReq:New()
    req.delete_room_id = Server.MatchServer:GetDsRoomId()
    req:Request(
        function()
            xxwwinfo("SettlementLogic:_OnSOLSettlementDo  CSSettlementGetHasUnsettledMatchReq dsroomid ",
                Server.MatchServer:GetDsRoomId())
        end
    )
    --玩家主动退出游戏 不显示UI
    if Server.SettlementServer:GetEndGameType() == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeQuitGame then
        SettlementConfig.Events.evtSettlementUILoadFinished:Invoke()
        return
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.AvfMediaPlayerEnable 1", nil)
    Module.Settlement:SetInSettlementing(true)

    Server.AccountServer:GetStateInfo()
    Server.InventoryServer:FetchAllItems() -- 需要在结算后拉取仓库数据
    --Server.CollectionServer:FetchCollectionData()
    Server.RoleInfoServer:FetchAllRoleInfo()
    Timer.DelayCall(2.0, function()
        SettlementLogic._OnSOLSettlementInternal()
    end, self)

    --BEGIN MODIFICATION - VIRTUOS
    --解锁平台成就
    if PLATFORM_GEN9 == 1 and Server.SettlementServer.settlementInfo and Server.SettlementServer.playerInfo then
        for _, v in pairs(Server.SettlementServer.playerInfo.game_achievements) do
            --目前没有进度型成就，进度默认为100
            SettlementLogic.GrantPlatformAchievement(v.id, 100)
        end
    end
    --END MODIFICATION - VIRTUOS    
end

function SettlementLogic._OnReqForSettlementInfoFailed(modeEnum)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole()  then 
        local  function BtnFunc()
            logerror("SettlementLogic._OnReqForSettlementInfoFailed settlement fail!")
            Module.InGame.Config.flowEvtReturnToLobby:Invoke()        
        end
    
        Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
            Module.Settlement.Config.Loc.SettlementFail2Retry_Gen9,
            BtnFunc,
            Module.Settlement.Config.Loc.ReturnToTheLobby)
    else    
        xxwwerror("SettlementLogic._OnReqForSettlementInfoFailed settlement fail!")
        Module.CommonTips:ShowConfirmWindow(Module.Settlement.Config.Loc.SettlementFail2Retry,
            function()
                if modeEnum == ESettlementNtf.SNTF_SOL then
                    Server.SettlementServer:ReqForSOLSettlementInfo()
                elseif modeEnum == ESettlementNtf.SNTF_RAID then
                    Server.SettlementServer:ReqForRaidSettlementInfo()
                elseif modeEnum == ESettlementNtf.SNTF_TDM then
                    Server.SettlementServer:ReqForTDMSettlementInfo()
                elseif modeEnum == ESettlementNtf.SNTF_Arena then
                    Server.SettlementServer:ReqForArenaSettlementInfo()
                end
            end,
            function()
                Module.Settlement:UECall_QuitGame()
            end,
            Module.Settlement.Config.Loc.Settlement2SafeHouse,
            Module.Settlement.Config.Loc.SettlementRetry
        )
    end
    --- END MODIFICATION
end

function SettlementLogic.RemoveListener()
    xxwwinfo("SettlementLogic._RemoveListener")
    Server.SettlementServer.Events.evtOnReqForSettlementInfoFailed:RemoveListener(SettlementLogic._OnReqForSettlementInfoFailed)
    SettlementConfig.Events.evtSettlementStart:RemoveListener(SettlementLogic._OnSettlementStart)
    SettlementConfig.Events.evtSettlementEnd:RemoveListener(SettlementLogic._OnSettlementEnd)
    SettlementConfig.Events.evtSettlementEnterSpectateMode:RemoveListener(SettlementLogic._SettlementEnterSpectateMode)
    SettlementConfig.Events.evtEliminationReplayEnd:RemoveAllListener(SettlementLogic._OnEliminationReplayEnd)
    local GameInst = GetGameInstance()
    UGPGameplayGlobalDelegates.Get(GameInst).OnPlayerExitSpectateMode:Clear()
    UGPGameplayGlobalDelegates.Get(GameInst).OnKillcamEndShowSettlement:Clear()
    UDFMGameplayDelegates.Get(GameInst).OnClientPlayerMatchOver:Remove(SettlementLogic._OnPlayerMatchOver)
end

SettlementLogic._OnSettlementStart = function()
    xxwwinfo("SettlementLogic._OnSettlementStart")
    -- 通知Looting模块
    Module.Looting:NotifyEnterSettlement()
    Server.InventoryServer:NotifyEnterSettlement()
    UDFMGameGPM.BeginExtTag("EXCLUDE_Settlement")
    UDFMGameGPM.BeginExclude()
    --隐藏HUD
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
        hudStateManager:AddState(UE.GameHUDSate.GHS_Settlement, true)
    end
    -- 停止局内音效
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)

    local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())
    if DFMGameLoadingManager then
        DFMGameLoadingManager:WarmUpLoadingView()
    end
    -- 上报
    local flow = pb.AchievementFlow:New()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()
    if settlementInfo and settlementInfo.player and settlementInfo.player.game_achievements then
        local achievemnts  = ""
        for _, achievement in ipairs(settlementInfo.player.game_achievements) do
            achievemnts = achievemnts .. achievement.id .. ","
        end
        -- remove last ","
        achievemnts = achievemnts:sub(1, -2)
        flow.Achievements = achievemnts
    end
    flow:Request(function(res)
        if res and res.code == 0 then
            loginfo("report achievment flow success")
        else
            logerror("report achievment flow failed")
        end
    end)
end

SettlementLogic._OnSettlementEnd = function()
    xxwwinfo("SettlementLogic._OnSettlementEnd")
    SettlementLogic.ClearLevelSequenceAndFocusLocation()
    Module.Settlement.Field.bHasLoadCutSceneData = false
    Module.Settlement.Field.mapIdTagSector = nil
    --恢复HUD
    Module.Settlement:SetInSettlementing(false)
    -- 结算完成后重置bGetNtf，防止下一局弱网条件下卡结算
    -- https://tapd.woa.com/r/t?id=129082526&type=bug
    Server.SettlementServer.bGetNtf = false
    Facade.UIManager:CloseAllPopUI()
    UDFMGameGPM.EndExtTag("EXCLUDE_Settlement")
    UDFMGameGPM.EndExclude()
    --Facade.UIManager:PopAllUI(true, false, MapPopAllUIReason2Str.FinishSettlement)
    if not Facade.GameFlowManager:CheckIsInFrontEnd() then
        -- 预表现Loading
        xxwwinfo("UDFMGameLoadingManager",UDFMGameLoadingManager)
        if UDFMGameLoadingManager then
            local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
            DFMGameLoadingManager.OnLoadingViewVisible:Add(SettlementLogic._OnSettlementInternal, SettlementLogic)
            local gamePlayerMode = InGameController:Get():GetGamePlayerMode()
            local settlementDestination = ELoadingDestination.ELD_NONE
            if EDFMGamePlayMode.GamePlayMode_Conquest == gamePlayerMode or EDFMGamePlayMode.GamePlayMode_Breakthrough == gamePlayerMode then
                settlementDestination = ELoadingDestination.ELD_BattlefieldHall
            elseif EDFMGamePlayMode.GamePlayMode_SOL == gamePlayerMode or EDFMGamePlayMode.GamePlayMode_Raid == gamePlayerMode then
                settlementDestination = ELoadingDestination.ELD_SOLHall
                -- TODO: raid @chuanjia
            end
            xxwwinfo("LoadingView: GPMode & Dest: " .. tostring(gamePlayerMode) .. " " .. tostring(settlementDestination))
            LuaGlobalEvents.evtStartPreshowLoadingView:Invoke(settlementDestination) -- TODO: 传入LoadingDestination @xxww

            -- 释放单局资源入口
            if DFMGameLoadingManager and DFMGameLoadingManager.PrepareReleaseGameResource then
                DFMGameLoadingManager:PrepareReleaseGameResource()
            end
        end
    else
        xxwwinfo("gameFlow is in frontEnd")
    end
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:RemoveState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
        hudStateManager:RemoveState(UE.GameHUDSate.GHS_Settlement, true)
    end

    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementWin)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementLose)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicBFSettlementWin)
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicBFSettlementFail)
end

-- 处理Loading预表现后的逻辑
SettlementLogic._OnSettlementInternal = function()
    xxwwinfo("[aidenliao] SettlementLogic._OnSettlementInternal")

    -- 移除监听
    if UDFMGameLoadingManager then
        local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
        DFMGameLoadingManager.OnLoadingViewVisible:Remove(SettlementLogic._OnSettlementInternal, SettlementLogic)
    end

    -- 通知流程变更
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    if settlementMgrIns then
        settlementMgrIns:SettlementEndBroadcast()
    else
        logerror("settlementMgrIns is nil!")
    end
    -- 恢复Proto到局外状态
    Facade.ProtoManager:ResetSystemReconnectState()

    -- 将局内的经分信息统一发送
    LogAnalysisTool.SetIsInGame(false)
    LogAnalysisTool.SendAllInGameData()

    local gamePlayerMode = InGameController:Get():GetGamePlayerMode()
    -- 如果是MP模式，需要在Loading界面出来的时候卸载场景
    if EDFMGamePlayMode.GamePlayMode_Conquest == gamePlayerMode or EDFMGamePlayMode.GamePlayMode_Breakthrough == gamePlayerMode then
        SettlementLogic.UnLoadCurrentMap()
    end
end

SettlementLogic._SettlementEnterSpectateMode = function()
    xxwwinfo("SettlementLogic._SettlementEnterSpectateMode")
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    if settlementMgrIns then
        UGPBattleFieldSystem.Get(GetWorld()).IsSpectateUnderFPP = false
        local IsRetOk = settlementMgrIns:InspectTeam()
        if IsRetOk then
            --恢复HUD
            --Module.Settlement:SetInSettlementing(false)
            local hudStateManager = UE.HUDStateManager.Get(GetWorld())
            if hudStateManager then
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_Settlement, true)
            end
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementWin)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementLose)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
            if IsHD() then
                Module.CommonBar:ShowTopBar()
            end
            return true
        end
    else
        logerror("settlementMgrIns is nil!")
    end
    return false
end

SettlementLogic._SettlementEnterReplayMode = function()
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    if settlementMgrIns then
        if UDFMReplaySystemBlueprintHelper.RequestMyNearestKillcam(GetWorld()) then
            --恢复HUD
            --Module.Settlement:SetInSettlementing(false)
            local hudStateManager = UE.HUDStateManager.Get(GetWorld())
            if hudStateManager then
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_Settlement, true)
            end
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementWin)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementLose)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
            return true
        end
    else
        logerror("settlementMgrIns is nil!")
    end
    return false
end

SettlementLogic._SettlementRepeatReplayMode = function()
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    if settlementMgrIns then
        if UDFMReplaySystemBlueprintHelper.RepeatKillcam(GetWorld()) then
            --恢复HUD
            --Module.Settlement:SetInSettlementing(false)
            local hudStateManager = UE.HUDStateManager.Get(GetWorld())
            if hudStateManager then
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_Settlement, true)
            end
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementWin)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.MusicSOLSettlementLose)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
            Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementBarLoop)
            return true
        end
    else
        logerror("settlementMgrIns is nil!")
    end
    return false
end

SettlementLogic._OnPlayerExitSpectateMode = function()
    if ItemOperaTool.bInSettlement then
        xxwwinfo("SettlementLogic._OnPlayerExitSpectateMode")
        if not IsHD() then
            Module.SystemSetting:CloseMainPanel()
        else
            Module.CommonBar:CloseTopBar()
        end
        local hudStateManager = UE.HUDStateManager.Get(GetWorld())
        if hudStateManager then
            hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
            hudStateManager:AddState(UE.GameHUDSate.GHS_Settlement, true)
        end
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
        SettlementConfig.Events.evtSettlementExitSpectateMode:Invoke()
    end
end

SettlementLogic._OnKillcamEndShowSettlement = function()
    xxwwinfo("SettlementLogic._OnKillcamEndShowSettlement")
    --SettlementLogic._OnSOLSettlementInternal()
    Module.Settlement.Config.Events.evtReplayBackToGameplay:Invoke()
end


SettlementLogic.SetAvatarState = function(bodyPartSwitcher, curValue, maxValue)
    local StatusSwitcher = bodyPartSwitcher:Wnd("WidgetSwitcher_PartStatus", UIWidgetSwitcher)
    curValue = curValue or 0
    curValue = math.round(curValue)
    maxValue = maxValue or 1
    maxValue = math.round(maxValue)
    if curValue < 1 then
        StatusSwitcher:SetActiveWidgetIndex(2)
    elseif curValue < maxValue then
        StatusSwitcher:SetActiveWidgetIndex(1)
    else
        StatusSwitcher:SetActiveWidgetIndex(0)
    end
end

SettlementLogic.GenerateAddExpTab = function(addExps)
    addExps = addExps or {}
    local addExpTab = {}
    local totalExp = 0
    for i, v in pairs(EAddExpReason) do
        addExpTab[v] = 0
    end

    for i, v in pairs(addExps) do
        local type = v.reason
        local value = v.add_exp
        addExpTab[type] = value
        totalExp = totalExp + v.add_exp
    end

    printtable(addExpTab, "addExpTab")
    return addExpTab, totalExp
end

SettlementLogic.GenerateAttrValueTab = function(attrs)
    local attrTab = {}
    for i, v in pairs(attrs) do
        local enumType = v.type
        local value = v.value or 0
        attrTab[enumType] = value
    end
    return attrTab
end

SettlementLogic.GenerateBuffArrayTab = function(buffs)
    xxwwinfo("SettlementLogic:GenerateBuffArrayTab")
    local buffTab = {}
    for i, v in pairs(buffs) do
        local eBodyPart = v.body_part
        local eBuffType = v.type
        if not buffTab[eBodyPart] then
            buffTab[eBodyPart] = { eBuffType }
        else
            table.insert(buffTab[eBodyPart], eBuffType)
        end
    end
    return buffTab
end

SettlementLogic.GetKillList = function(endGameInfo)
    local killArray = endGameInfo.kill_array or {}
    local killMap = {}
    local killPlayerNum = 0
    local killAINum = 0
    local killBossNum = 0
    for i, v in pairs(killArray) do
        if v.enemy_type == EGspEnemyType.kGspEnemyTypePlayer or v.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
            killPlayerNum = killPlayerNum + 1
        elseif v.enemy_type == EGspEnemyType.kGspEnemyTypeAI then
            killAINum = killAINum + 1
        elseif v.enemy_type == EGspEnemyType.kGspEnemyTypeBoss then
            killBossNum = killBossNum + 1
        end
    end
    killMap[EGspEnemyType.kGspEnemyTypePlayer] = killPlayerNum
    killMap[EGspEnemyType.kGspEnemyTypeAI] = killAINum
    killMap[EGspEnemyType.kGspEnemyTypeBoss] = killBossNum
    return killMap
end

SettlementLogic.GetAchievementName = function(id)
    xxwwinfo("SettlementLogic.GetAchievementName", id, type(id))
    id = tonumber(id)
    local ty = id // 1000
    local tyStr = tostring(ty)
    local subty = math.fmod(id, 1000)
    local achievementName = AchievementTable[tyStr].AchievementNameArr[subty]
    return (achievementName or "A table needs to be configured for the ID") .. id
end

SettlementLogic.GetAchievementIcon = function(id)
    xxwwinfo("SettlementLogic.GetAchievementIcon", id, type(id))
    id = tonumber(id)
    local ty = id // 1000
    local tyStr = tostring(ty)
    local subty = math.fmod(id, 1000)
    local achievementIcon = AchievementTable[tyStr].AchievementIconArr[subty]

    if achievementIcon then
        return FLuaHelper.SoftObjectPtrToString(achievementIcon) -- TODO:FLuaHelper这个为啥不能import
    else
        return ""
    end
end

SettlementLogic.PlayOpenAudio = function()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISettlementPopup)
end

SettlementLogic.PlayTextChangeAudio = function()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
end
SettlementLogic.StopTextChangeAudio = function()
    Facade.SoundManager:StopUIAudioEvent(DFMAudioRes.UISettlementNumberLoop)
end

SettlementLogic.GetSlotTypeFromProp = function(prop)
    local slotType = prop.position
    return slotType
end


SettlementLogic.ShowRaidSettlementUIStacks = function(raidSettlementType)
    xxwwinfo("SettlementLogic.ShowRaidSettlementUIStacks", raidSettlementType)
    local EnumSettlementMode = Module.Settlement.Config.EnumSettlementMode
    Server.SettlementServer.settlementMode = EnumSettlementMode.RaidSettlement
    local UICacheList = {}
    local ERaidSettlementType = {
        RaidSettlementSuccess = 0, -- // raid成功
        RaidSettlementFail = 1 -- // raid失败
    }

    local raidSettlementStack = {}
    if raidSettlementType then
        raidSettlementStack = Module.Settlement.Config.SettlementMode2UI.Raid[ERaidSettlementType.RaidSettlementSuccess]
    else
        raidSettlementStack = Module.Settlement.Config.SettlementMode2UI.Raid[ERaidSettlementType.RaidSettlementFail]
    end
    table.insertto(UICacheList, raidSettlementStack)
    UIManager:BatchShowStackUI(UICacheList)
end

SettlementLogic.ShowSettlementReturnConfirmWindow = function()
    Facade.UIManager:AsyncShowUI(UIName2ID.SettlementReturnConfirmWindow)
end

SettlementLogic.ShowSettlementBindingTeammateTips = function(bFail, teammateReturnMode)
    UIManager:AsyncShowUI(UIName2ID.SettlementReturnBindingTeammateTips, nil, nil, bFail, teammateReturnMode)
end

SettlementLogic.GetTeammateReturnMode = function()
    local teammateReturnMode = Module.Settlement.Config.TeammateReturnMode.None
    local notMyItems = Server.SettlementServer.notMyItemsProps
    local bBag = false
    local bChestHanging = false
    for index, propInfo in ipairs(notMyItems) do
        if propInfo.position == ESlotType.Bag and propInfo.bind_player ~= Server.AccountServer:GetPlayerId() then
            bBag = true
        elseif propInfo.position == ESlotType.ChestHanging and propInfo.bind_player ~= Server.AccountServer:GetPlayerId() then
            bChestHanging = true
        end
    end
    if bBag and bChestHanging then
        teammateReturnMode = Module.Settlement.Config.TeammateReturnMode.Both
    elseif bBag and not bChestHanging then
        teammateReturnMode = Module.Settlement.Config.TeammateReturnMode.Bag
    elseif not bBag and bChestHanging then
        teammateReturnMode = Module.Settlement.Config.TeammateReturnMode.ChestHanging
    end
    return teammateReturnMode
end

---@desc 获取成就id列表
---@return number[]
SettlementLogic.GetAchievements = function()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()
    if not settlementInfo or not settlementInfo.player then
        return {}
    end
    local achievements = {}
    for _, achievement in ipairs(settlementInfo.player.game_achievements) do
        table.insert(achievements, achievement.id)
    end
    return achievements
end

---@desc 获取分享页面展示数据
---@return number[]
SettlementLogic.GetSharePlayerData = function()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()
    if not settlementInfo or not settlementInfo.player then
        return {}
    end
    if settlementInfo.player.type == EGspPlayerGameStatusType.kGspPlayerGameStatusTypePlaying then
        xxwwerror("SettlementLogic.GetSharePlayerData Impossible! Player is playing.")
        return {}
    end
    local dataList = {}
    local killPlayerNum = 0
    ---@type pb_GspEndGamePlayerStatus
    local playerInfo
    if settlementInfo.player.type == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame then
        playerInfo = settlementInfo.player.end_game
    else
        playerInfo = settlementInfo.player.quit_game
    end
    table.insert(dataList, playerInfo.carry_out_profit_price + playerInfo.cost_price) -- 带出价值
    table.insert(dataList, playerInfo.play_time) -- 生存时长
    for _, killInfo in ipairs(playerInfo.kill_array) do
        if killInfo.enemy_type == EGspEnemyType.kGspEnemyTypePlayer or killInfo.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
            killPlayerNum = killPlayerNum + 1
        end
    end
    table.insert(dataList, killPlayerNum) -- 击杀干员数
    table.insert(dataList, settlementInfo.player.money_paper) -- 带出的合同金额
    return dataList
end

SettlementLogic.UnLoadCurrentMap = function()
    if not SettlementLogic.NeedToUnLoadMap() then
        return
    end
    if SettlementConfig.ACTIVE_UNLOAD_MAP and not IsHD() then
        if isvalid(GetWorld().WorldComposition) then
            local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
            settlementMgrIns:OnSettlementPreUnloadGameLevels()
            loginfo("[xxww] SettlementLogic.UnLoadCurrentMap")
            GetWorld().WorldComposition:UseStreamingSettings("Iris_Entry", {})
            if Facade.UIManager:CycleSyncIsLowMemoryState() then
                -- 只在低内存设备上主动GC
                loginfo("[xxww] SettlementLogic.UnLoadCurrentMap ULuaExtension.ForceGC()")
                ULuaExtension.ForceGC()
            end
        end
    end
end

SettlementLogic.InvokeGameNotifyCloseNetDirver = function()
    local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())
    if settlementMgrIns then
        settlementMgrIns:OnSettlementPreUnloadGameLevels()
    end
end

--BEGIN MODIFICATION - VIRTUOS
--登陆成功回调，查询平台成就
SettlementLogic._OnLoginSuccess = function()
    if PLATFORM_GEN9 ~= 1 then
        return
    end

    loginfo("SettlementLogic._OnLoginSuccess")
    if DFMAchievementsManager then
        DFMAchievementsManager:OnLoginCompleted()
    end
end

--解锁平台成就
SettlementLogic.GrantPlatformAchievement = function(AchievementID, Progress)
    if PLATFORM_GEN9 ~= 1 then
        return
    end
    
    if DFMAchievementsManager then
        DFMAchievementsManager:GrantAchievement(AchievementID, Progress)
    end
end
--END MODIFICATION - VIRTUOS

SettlementLogic.NeedToUnLoadMap = function()
    -- SOL模式仅在低内存设备上卸载场景
    if InGameController:Get():IsSOLMode() and not Facade.UIManager:CycleSyncIsLowMemoryState() then
        return false
    end
    return true
end

SettlementLogic.PreloadAchievementIcon = function()
    if Server.SettlementServer:IsInHumanMachineRoom() then
        xxwwinfo("SettlementLogic.PreloadAchievementIcon blocked because in human machine room.")
        return
    end
    local tdmSettlementInfo = Server.SettlementServer:GetTDMSettlementInfo()
    local myPlayerId = Server.AccountServer:GetPlayerId()
    local achievementIDs = {}
    if tdmSettlementInfo then
        local mvpTeam, myTeam
        for _, camp in pairs(tdmSettlementInfo.camp_list) do
            for _, team in pairs(camp.team_list) do
                if mvpTeam == nil and camp.is_winner == 1 and team.team_rank == 1 then
                    mvpTeam = team
                    for _, player in pairs(team.player_list) do
                        for index = 1, SettlementConfig.MAX_SHOW_ACHIEVEMENT_NUM do
                            if not player.game_achievements[index] then
                                break
                            end
                            table.insert(achievementIDs, player.game_achievements[index].id)
                        end
                    end
                end
                for _, player in ipairs(team.player_list) do
                    if myTeam == nil and tostring(myPlayerId) == tostring(player.player_id) then
                        myTeam = team
                        for _, player in pairs(team.player_list) do
                            for index = 1, SettlementConfig.MAX_SHOW_ACHIEVEMENT_NUM do
                                if not player.game_achievements[index] then
                                    break
                                end
                                table.insert(achievementIDs, player.game_achievements[index].id)
                            end
                        end
                    end
                end
            end
        end
        achievementIDs = table.unique(achievementIDs, true)
    end
    local logContent = {"SettlementLogic.PreloadAchievementIcon start to load achieve num ="}
    table.insert(logContent, #achievementIDs)
    table.insert(logContent, "achievements:")
    for _, achieveId in ipairs(achievementIDs) do
        table.insert(logContent, achieveId)
    end
    logContent = table.concat(logContent, " ")
    xxwwinfo(logContent)
    local achievementTable = Module.Achievement:GetTable()
    local loadSuccessNum = 0
    local callBack = function(imageAsset, bAutoResize)
        loadSuccessNum = loadSuccessNum + 1
        if loadSuccessNum == #achievementIDs then
            xxwwinfo("SettlementLogic.PreloadAchievementIcon succeed to load achieve num =", loadSuccessNum)
        end
    end
    for _, achieveId in ipairs(achievementIDs) do
        if achievementTable[achieveId] then
            if achievementTable[achieveId].IconPath ~="" then
                ResImageUtil.StaticAsyncLoadImgObjByPath(achievementTable[achieveId].IconPath, true, callBack)
            end
        end
    end
    return achievementIDs
end

SettlementLogic._OnEliminationReplayEnd = function()
    -- azhengzheng:强制卸载场景，防止回到死亡详情界面时场景漏出（不清楚为啥创建的底部播放视频的界面不显示，待后续排查）
    loginfo("SettlementLogic._OnEliminationReplayEnd")
    if isvalid(GetWorld().WorldComposition) then
        GetWorld().WorldComposition:UseStreamingSettings("Iris_Entry", {})
        ULuaExtension.ForceGC()
        Module.Settlement.Field:SetCurMapIsUnload(true, true)
    end

    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, true)
        hudStateManager:AddState(UE.GameHUDSate.GHS_Settlement, true)
    end

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)

    Module.Settlement.Config.Events.evtOpenRootCutscene:Invoke()
    SettlementLogic.CloseEmptyStackUI()
end

function SettlementLogic._OnFinishLoadCutSceneLevel()
    loginfo("SettlementLogic._OnFinishLoadCutSceneLevel")
    if Module.Settlement.Field.delayStartSettlementTimerHandle then
        Timer.CancelDelay(Module.Settlement.Field.delayStartSettlementTimerHandle)
        Module.Settlement.Field.delayStartSettlementTimerHandle = nil
        SettlementLogic._DoTDMSettlement_Internal()
    else
        loginfo("SettlementLogic._OnFinishLoadCutSceneLevel Module.Settlement.Field.delayStartSettlementTimerHandle is nil")
    end
end

function SettlementLogic.GetCommanderGameContributorName(titleId)
    if table.isempty(Module.Settlement.Field.contributionTypeToName) then
        local table = Facade.TableManager:GetTable("BattleField/OutstandingContributionTable")
        for _, row in pairs(table) do
            Module.Settlement.Field.contributionTypeToName[row.OutStandingContributionType] = row.ContributorName
        end
    end
    return Module.Settlement.Field.contributionTypeToName[titleId]
end

function SettlementLogic.GetCommanderGameMotto(roomId, bWin)
    local candidateMottoIds = {}
    local IDToMotto = {}
    local mottoTable = Facade.TableManager:GetTable("CommanderGameMotto")
    for _, mottoConfig in pairs(mottoTable) do
        if bWin then
            if mottoConfig.WinText ~= "" then
                table.insert(candidateMottoIds, mottoConfig.ID)
                IDToMotto[mottoConfig.ID] = tostring(mottoConfig.WinText)
            end
        else
            if mottoConfig.LoseText ~= "" then
                table.insert(candidateMottoIds, mottoConfig.ID)
                IDToMotto[mottoConfig.ID] = tostring(mottoConfig.LoseText)
            end
        end
    end
    table.sort(candidateMottoIds, function(a, b)
        if a == b or a == nil or b == nil then
            return false
        end
        return a < b
    end)
    local digitNum = 0
    roomId = bWin and roomId or math.floor(roomId / 10)
    local sum = 0
    while roomId > 0 and digitNum < 10 do
        sum = sum + roomId % 10
        roomId = math.floor(roomId / 100)
        digitNum = digitNum + 1
    end
    local index = sum % #candidateMottoIds + 1
    return IDToMotto[candidateMottoIds[index]]
end

function SettlementLogic.ShowEmptyStackUI()
    Module.Settlement.Field.emptyStackUIHandle = Facade.UIManager:AsyncShowUI(UIName2ID.EmptyStackUI)
end

function SettlementLogic.CloseEmptyStackUI()
    local handle = Module.Settlement.Field.emptyStackUIHandle
    if handle then
        Facade.UIManager:CloseUIByHandle(handle)
        Module.Settlement.Field.emptyStackUIHandle = nil
    end
end

function SettlementLogic._OnHasTeammatePropInSettlement()
    local settlementInfo = Server.SettlementServer:GetSettlementInfo()

    if not settlementInfo or not settlementInfo.player then
        return false
    end
    local rewardInfo = settlementInfo.player.end_game
    local bHasTeamProp = false
    local teamArray = Server.SettlementServer:GetTeammateArray()
    local teamPlayerIDs = {}
    for _, v in pairs(teamArray) do
        local info = v
        table.insert(teamPlayerIDs, info.basic_info.player_id)
    end
    for i, info in ipairs(rewardInfo.carry_out_props) do
        for index, prop in ipairs(info.load_props) do
            if table.contains(teamPlayerIDs, prop.bind_player) then
                bHasTeamProp = true
            end
        end
    end
    return bHasTeamProp
end

function SettlementLogic.CheckHasLuaConfig()
    local mapid = InGameController:Get():GetCurrentMapId()
    xxwwinfo("SettlementLogic.CheckHasLuaConfig", mapid, Module.Settlement.Config.Map2CutScene[mapid or 0])
    return mapid and Module.Settlement.Config.Map2CutScene[mapid or 0] ~= nil or false
end

function SettlementLogic.CheckNeedToEarlyLoadLevel()
    local function func()
        if InGameController:Get():IsTDMMode() or Server.MatchServer:IsInTDMGameMode() then
            if SettlementLogic.CheckHasLuaConfig() then
                return false
            end
            local modeInfo = Server.MatchServer:GetMatchModeInfo()
            if modeInfo and modeInfo.game_mode == MatchGameMode.TDMGameMode then
                return false
            end

            return Module.Settlement.Config.bUseEarlyLoadPlan
        end
        return false
    end
    local result = func()
    xxwwinfo("SettlementLogic.CheckNeedToEarlyLoadLevel", result)
    return result
end

function SettlementLogic.CheckNeedToLatelyLoadLevel()
    local function func()
        if InGameController:Get():IsTDMMode() or Server.MatchServer:IsInTDMGameMode() then
            if SettlementLogic.CheckHasLuaConfig() then
                return false
            end
            if Module.Settlement.Config.bUseEarlyLoadPlan then
                return not Module.Settlement.Field.bHasLoadCutSceneData
            end
            return not Module.Settlement.Field.bHasTriggerLoadLevelInThisGF
        end
        return false
    end
    local result = func()
    xxwwinfo("SettlementLogic.CheckNeedToLatelyLoadLevel", result)
    return result
end

function SettlementLogic.ClearLevelSequenceAndFocusLocation()
    xxwwinfo("SettlementLogic.ClearLevelSequenceAndFocusLocation")
    Module.Settlement.Field.sector2LevelSequence = {}
    Module.Settlement.Field.sector2FocusLocation = {}
end

function SettlementLogic.LoadLevelSequenceAndFocusLocation()
    Module.Settlement.Field.bHasLoadCutSceneData = true
    local SequenceActors
    SequenceActors = UGameplayStatics.GetAllActorsOfClass(GetWorld(), AGPSequenceActor, SequenceActors)
    local FocusActors
    FocusActors = UGameplayStatics.GetAllActorsOfClass(GetWorld(), ASettlementCutSceneFocusActor, FocusActors)
    local mapid = InGameController:Get():GetCurrentMapId()
    xxwwinfo("SettlementLogic.LoadLevelSequenceAndFocusLocation", mapid, #SequenceActors, #FocusActors)
    for _, SequenceActor in pairs(SequenceActors) do
        SettlementLogic.LoadDataFromSequenceActor(SequenceActor)
    end
    for _, FocusActor in pairs(FocusActors) do
        SettlementLogic.LoadDataFromFocusActor(FocusActor)
    end
end

function SettlementLogic.LoadDataFromSequenceActor(SequenceActor)
    local mapid = InGameController:Get():GetCurrentMapId()
    local mapidTag = "mapid=" .. mapid
    for _, tag in pairs(SequenceActor.Tags) do
        local sector = string.sub_start(tag, "EndMatchS")
        if sector then
            sector = tonumber(sector)
            if sector > 0 then
                Module.Settlement.Field.sector2LevelSequence[sector] = FSoftObjectPath()
                local softObjectPath
                softObjectPath = UKismetSystemLibrary.BreakSoftObjectPath(SequenceActor.LevelSequence, softObjectPath)
                Module.Settlement.Field.sector2LevelSequence[sector]:SetPath(softObjectPath)
                if SequenceActor:ActorHasTag(mapidTag) then
                    Module.Settlement.Field.mapIdTagSector = sector
                end
                xxwwinfo("SettlementLogic.LoadDataFromSequenceActor load level sequence", sector, softObjectPath, Module.Settlement.Field.mapIdTagSector)
            end
            break
        end
    end
end

function SettlementLogic.LoadDataFromFocusActor(FocusActor)
    for _, tag in pairs(FocusActor.Tags) do
        local sector = string.sub_start(tag, "EndMatchS")
        if sector then
            sector = tonumber(sector)
            if sector > 0 then
                local loc = FocusActor:K2_GetActorLocation()
                local rebasedLoc = UGameplayStatics.RebaseLocalOriginOntoZero(FocusActor, loc)
                Module.Settlement.Field.sector2FocusLocation[sector] = FVector(rebasedLoc.X, rebasedLoc.Y, rebasedLoc.Z)
                xxwwinfo("SettlementLogic.LoadDataFromFocusActor load focus location", sector, loc, rebasedLoc)
            end
            break
        end
    end
end

return SettlementLogic