----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSRanking)
----- LOG FUNCTION AUTO GENERATE END -----------


--积分赛Server
---@class TournamentServer : ServerBase
local TournamentServer = class("TournamentServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

function TournamentServer:Ctor()
    loginfo("TournamentServer:Ctor")
    self:InitData()
end

function TournamentServer:OnInitServer()
    loginfo("TournamentServer:OnInitServer")

    Facade.ProtoManager:AddNtfListener("CSSeasonRankScoreChangedNtf", self._OnSeasonRankScoreChangedNtf, self)
    
end

function TournamentServer:InitData()
    loginfo("TournamentServer:InitData")

    self.Events = {
        evtTournamentInfoUpdated = LuaEvent:NewIns("evtTournamentInfoUpdated"),
        evtCommanderAbilitiesPercentageUpdated = LuaEvent:NewIns("evtCommanderAbilitiesPercentageUpdated"),
        evtTournamentReddotInfoUpdate = LuaEvent:NewIns("evtTournamentReddotInfoUpdate"),
        evtNewSeasonComing = LuaEvent:NewIns("evtNewSeasonComing"),
        
    }

    self.seasonRankInfo={}--赛季排位信息
    self.seasonRankList={}--天梯排名
end

function TournamentServer:FetchServerData()
    loginfo("TournamentServer:FetchServerData")
    --self:ReqSeasonRankInfo()
    --self:ReqSeasonRankList(SeasonRankList.TDM_Friends)
    --self:ReqSeasonRankList(SeasonRankList.TDM_Global)

end

function TournamentServer:OnLoadingLogin2Frontend(gameFlowType)
    logwarning("TournamentServer:OnLoadingLogin2Frontend",gameFlowType)
    --self:FetchServerData()
end

function TournamentServer:OnLoadingGame2Frontend(gameFlowType)--局内出来不用拉，分数变动会有ntf
    logwarning("TournamentServer:OnLoadingGame2Frontend",gameFlowType)
    --self:FetchServerData()

end

function TournamentServer:OnDestroyServer()
    loginfo("TournamentServer:OnDestroyServer")

    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
end

function TournamentServer:_OnSeasonRankScoreChangedNtf(ntf)--赛季分变动
    loginfo("TournamentServer:_OnSeasonRankScoreChangedNtf")
    logtable(ntf,true)
    if ntf.mode==2 or ntf.mode==4 then--积分赛/指挥官
        self:ReqSeasonRankInfo()
    end
end

---@param serial 0默认为当前赛季
function TournamentServer:ReqSeasonRankInfo(serial,fCallback)--拉取赛季信息，包括初始赛季/当前赛季/当前分数/最高分数/已领取奖励等
    loginfo("TournamentServer:ReqSeasonRankInfo",serial)
    if self._isInPreReqNewSeasonTime then
        logerror("TournamentServer:ReqSeasonRankInfo, now in pre req new season time!!!")
        return
    end
    serial=serial or 0
    local fSeasonGetInfoRes=function(res)
        loginfo("TournamentServer:ReqSeasonRankInfo fSeasonGetInfoRes",res.result)
        if res.result==0 then
            self.seasonRankInfo.CurSerial=res.cur_serial
            self.seasonRankInfo.BeginSerial=res.begin_serial--注册账号的赛季
            self.seasonRankInfo.PrevAttendedSerial=res.prev_serial--上个参与的赛季
            self.seasonRankInfo.StartTime=res.start_time
            self.seasonRankInfo.EndTime=res.end_time
            self.seasonRankInfo.UIStartTime=res.UIStartTime
            self.seasonRankInfo.UIEndTime=res.UIEndTime
            self.seasonRankInfo.NextSerialStartTime=res.next_serial_start_time
            self.seasonRankInfo.LastSeasonRewards=self:FetchSimplePropInfoFromList(res.mp_awards)--上赛季奖励(结算奖励)
            self.seasonRankInfo[serial]={}
            self.seasonRankInfo[serial].RankSummary={}
            self.seasonRankInfo[serial].RankSummary.HasAttended=res.season_info.mp_rank_summary.has_attended--是否参加过积分赛，某一赛季参与过之后，后续的赛季都为true
            self.seasonRankInfo[serial].RankSummary.Score=res.season_info.mp_rank_summary.score--当前积分
            self.seasonRankInfo[serial].RankSummary.ScoreMax=res.season_info.mp_rank_summary.score_max--最高积分
            self.seasonRankInfo[serial].RankSummary.ScoreShoot=res.season_info.mp_rank_summary.score_shoot--射击分
            self.seasonRankInfo[serial].RankSummary.ScoreTactics=res.season_info.mp_rank_summary.score_tactics--战术分
            self.seasonRankInfo[serial].RankSummary.ScoreVehicle=res.season_info.mp_rank_summary.score_vehicle--载具分
            self.seasonRankInfo[serial].RankSummary.LevelsRewarded=res.season_info.mp_rank_summary.levels_rewarded--已领取的奖励
            self.seasonRankInfo[serial].RankSummary.RankEvents=res.season_info.mp_rank_summary.rank_events--赛季事件
            self.seasonRankInfo[serial].RankSummary.MajorSummaries=res.season_info.mp_rank_summary.major_summaries--段位数据
            local levelMinor,levelMajor=self:CalSeasonLevelByScore(self.seasonRankInfo[serial].RankSummary.Score,serial==0 and self.seasonRankInfo.CurSerial or serial,self.seasonRankInfo[serial].RankSummary.HasAttended)
            local levelMinorMax,levelMajorMax=self:CalSeasonLevelByScore(self.seasonRankInfo[serial].RankSummary.ScoreMax,serial==0 and self.seasonRankInfo.CurSerial or serial,self.seasonRankInfo[serial].RankSummary.HasAttended)
            self.seasonRankInfo[serial].RankSummary.LevelMinor=levelMinor
            self.seasonRankInfo[serial].RankSummary.LevelMajor=levelMajor
            self.seasonRankInfo[serial].RankSummary.LevelMinorMax=levelMinorMax
            self.seasonRankInfo[serial].RankSummary.LevelMajorMax=levelMajorMax
            self.seasonRankInfo.CommanderLastSeasonRewards=self:FetchSimplePropInfoFromList(res.mp_commander_awards)--指挥官上赛季奖励(结算奖励)
            self.seasonRankInfo[serial].CommanderRankSummary={}
            self.seasonRankInfo[serial].CommanderRankSummary.HasAttended=res.season_info.commander_summary.has_attended--是否参加过指挥官，某一赛季参与过之后，后续的赛季都为true
            self.seasonRankInfo[serial].CommanderRankSummary.Score=res.season_info.commander_summary.score--当前积分
            self.seasonRankInfo[serial].CommanderRankSummary.ScoreMax=res.season_info.commander_summary.score_max--最高积分
            self.seasonRankInfo[serial].CommanderRankSummary.LevelsRewarded=res.season_info.commander_summary.levels_rewarded--已领取的奖励
            self.seasonRankInfo[serial].CommanderRankSummary.RankEvents=res.season_info.commander_summary.rank_events--赛季事件
            self.seasonRankInfo[serial].CommanderRankSummary.MajorSummaries=res.season_info.commander_summary.major_summaries--段位数据
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData={}
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalFight=res.season_info.commander_summary.info.total_fight--对局数
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalFightAsCommander=res.season_info.commander_summary.info.total_fight_as_commander--指挥场次
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalWinAsCommander=res.season_info.commander_summary.info.total_win_as_commander--指挥胜场
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalKill=res.season_info.commander_summary.info.total_kill--总击杀
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalGameTime=res.season_info.commander_summary.info.total_game_time--总游戏时长
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalVehicleKill=res.season_info.commander_summary.info.total_vehicle_kill--载具击杀
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalVehicleUseTime=res.season_info.commander_summary.info.total_vehicle_use_time--载具使用时间
            self.seasonRankInfo[serial].CommanderRankSummary.AbilityData.TotalHelp=res.season_info.commander_summary.info.total_help--救助数
            self.seasonRankInfo[serial].CommanderRankSummary.Tile2CountMap=self:TransCommanderTitleListToMap(res.season_info.commander_summary.titles)
            local commanderLevelMinor,commanderLevelMajor=self:CalCommanderSeasonLevelByScore(self.seasonRankInfo[serial].CommanderRankSummary.Score,serial==0 and self.seasonRankInfo.CurSerial or serial,self.seasonRankInfo[serial].CommanderRankSummary.HasAttended)
            local commanderLevelMinorMax,commanderLevelMajorMax=self:CalCommanderSeasonLevelByScore(self.seasonRankInfo[serial].CommanderRankSummary.ScoreMax,serial==0 and self.seasonRankInfo.CurSerial or serial,self.seasonRankInfo[serial].CommanderRankSummary.HasAttended)
            self.seasonRankInfo[serial].CommanderRankSummary.LevelMinor=commanderLevelMinor
            self.seasonRankInfo[serial].CommanderRankSummary.LevelMajor=commanderLevelMajor
            self.seasonRankInfo[serial].CommanderRankSummary.LevelMinorMax=commanderLevelMinorMax
            self.seasonRankInfo[serial].CommanderRankSummary.LevelMajorMax=commanderLevelMajorMax
            if serial==0 or serial==self.seasonRankInfo.CurSerial then
                self.seasonRankInfo[serial].RankSummary.LevelsNotRewarded=self:CalNotReceivedLevels(levelMajorMax, self.seasonRankInfo[serial].RankSummary.LevelsRewarded,
                self.seasonRankInfo[serial].RankSummary.MajorSummaries,self.seasonRankInfo.CurSerial,self.seasonRankInfo[serial].RankSummary.HasAttended)
                self.seasonRankInfo[serial].CommanderRankSummary.LevelsNotRewarded=self:CalCommanderNotReceivedLevels(levelMajorMax, self.seasonRankInfo[serial].CommanderRankSummary.LevelsRewarded,
                self.seasonRankInfo[serial].CommanderRankSummary.MajorSummaries,self.seasonRankInfo.CurSerial,self.seasonRankInfo[serial].CommanderRankSummary.HasAttended)
                self.seasonRankInfo[self.seasonRankInfo.CurSerial]=self.seasonRankInfo[serial]
                self.seasonRankInfo[0]=self.seasonRankInfo[serial]
                    
            end
            local simpleSeasonInfo=self:GenSimpleSeasonInfo(self.seasonRankInfo)
            if self.seasonRankInfo.PrevAttendedSerial~=0 and not self.seasonRankInfo[self.seasonRankInfo.PrevAttendedSerial] then--提前拉取一下prevSeasonInfo，判断指挥官模式是否解锁能用到
                self:ReqSeasonRankInfo(self.seasonRankInfo.PrevAttendedSerial)
            end

            logtable(simpleSeasonInfo,true)
            if fCallback then
                fCallback()
            end
            self.Events.evtTournamentInfoUpdated:Invoke()
            self:UpdateFirstVictoryUniteRed()
        end
    end
    local req=pb.CSSeasonGetInfoReq:New()--请求不带mode字段，回包里只取mp的赛季信息
    req.serial=serial
    req.mode=MatchGameType.MPGameType
    req:Request(fSeasonGetInfoRes,{bEnableHighFrequency=true})
end

function TournamentServer:ReqGetCommanderAbilitiesPercentage()--胜者为王能力排名(指挥、载具击杀、步战击杀、救援)
    loginfo("TournamentServer:ReqGetCommanderAbilitiesPercentage")
    local fGetPlayerRankPercentageRes=function(res)
        loginfo("TournamentServer:ReqGetCommanderAbilitiesPercentage fGetPlayerRankPercentageRes",res.result)
        if res.result==0 then
            self.CommanderAbilitiesPercentageMap={}
            for k,v in pairs(res.list)do
                self.CommanderAbilitiesPercentageMap[v.rank_data_type]=v.percentage
            end
            logtable(self.CommanderAbilitiesPercentageMap,true)
            self.CommanderAbilitiesPercentageMap=self.gmAbilityPercentInfo or self.CommanderAbilitiesPercentageMap

            self.Events.evtCommanderAbilitiesPercentageUpdated:Invoke()
        end
    end
    local rankDataType={
        RankDataType.TDM_VICTORY_UNIT_COMMAND_SCORE,
        RankDataType.TDM_VICTORY_UNIT_INFANTRY_KILL_SCORE,
        RankDataType.TDM_VICTORY_UNIT_VEHICLE_KILL_SCORE,
        RankDataType.TDM_VICTORY_UNIT_INFANTRY_RESCUE_SCORE
    }
    local req=pb.CSRankGetPlayerRankPercentageReq:New()
    req.rank_data_type=rankDataType
    req:Request(fGetPlayerRankPercentageRes)
end

-- 个人信息每赛季首次胜者为王红点
function TournamentServer:UpdateFirstVictoryUniteRed()
    local TipsRecordServer = Server.TipsRecordServer
    local key = TipsRecordServer.keys.FirstVictoryUniteRed

    if TipsRecordServer:GetNumber(key) ~= 1 and self:GetCommanderHasAttended() then
        Server.RoleInfoServer.Events.evtSocialAvatarChange:Invoke()
    end
end

---@param levels list 要领取的段位（大段位）
function TournamentServer:ReqSeasonRankAward(levels,fCallback)--领取奖励
    loginfo("TournamentServer:ReqSeasonRankAward")
    logtable(levels,true)
    local fSeasonRankRecvAwardRes=function(res)
        loginfo("TournamentServer:ReqSeasonRankAward fSeasonRankRecvAwardRes",res.result)
        if res.result==0 then
            if fCallback then
                fCallback()
            end
        end

    end
    local req=pb.CSSeasonRankRecvAwardReq:New()
    req.levels=levels
    req.mode=2--mp
    req:Request(fSeasonRankRecvAwardRes)

end

---@param levels list 要领取的段位（大段位）
function TournamentServer:ReqCommanderSeasonRankAward(levels,fCallback)--领取胜者为王奖励
    loginfo("TournamentServer:ReqCommanderSeasonRankAward")
    logtable(levels,true)
    local fSeasonRankRecvAwardRes=function(res)
        loginfo("TournamentServer:ReqCommanderSeasonRankAward fSeasonRankRecvAwardRes",res.result)
        if res.result==0 then
            if fCallback then
                fCallback()
            end
        end

    end
    local req=pb.CSSeasonCommanderRecvAwardReq:New()
    req.levels=levels
    req:Request(fSeasonRankRecvAwardRes)

end

---@param listType SeasonRankList 好友/全服
function TournamentServer:ReqSeasonRankList(listType,fCallback,pageNo,rankList)--天梯排名
    loginfo("TournamentServer:ReqSeasonRankList",listType)
    --[[local req=pb.CSSeasonGetRankListReq:New()
    req.which=listType
    req.page_no=pageNo or 1
    req.page_size=50

    
    local fSeasonGetRankListRes=function(res)
        loginfo("TournamentServer:ReqSeasonRankList fSeasonGetRankListRes",res.result,"type",listType,"page",res.page,"pageMax",res.page_max)
        if res.result==0 then
            self.seasonRankList[listType]=rankList or {}
            self.seasonRankList.Punished=res.punished
            self.seasonRankList.PunishInfo=res.punish_item
            for k,v in pairs(res.list or {})do
                --- BEGIN MODIFICATION @ VIRTUOS 增加平台logo
                if PLATFORM_GEN9 == 1 then
                    table.insert(self.seasonRankList[listType],{playerId=v.player_id,score=v.score,nick=v.nick,logo=v.logo,game_center=v.game_center,plat_id=v.plat_id})
                else
                    table.insert(self.seasonRankList[listType],{playerId=v.player_id,score=v.score,nick=v.nick,logo=v.logo,game_center=v.game_center})
                end
                --- END MODIFICATION
            end
            if res.page<10 and res.page<res.page_max then--一页默认50个，拉n页
                self:ReqSeasonRankList(listType, fCallback,res.page+1,self.seasonRankList[listType])
            else
                table.sort(self.seasonRankList[listType],function(a,b)return a.score>b.score end)
                --logtable(self.seasonRankList[listType],true)
                logtable({punished=self.seasonRankList.Punished,punishInfo=self.seasonRankList.PunishInfo},true)
                if fCallback then
                    fCallback()
                end

            end
            
        end
    end
    
    req:Request(fSeasonGetRankListRes,{bEnableHighFrequency=true})]]
end

function TournamentServer:BroadcastUpdateReddot()
    loginfo("TournamentServer:BroadcastUpdateReddot")
    self.Events.evtTournamentReddotInfoUpdate:Invoke()
end

function TournamentServer:BroadcastNewSeasonComing()
    loginfo("TournamentServer:BroadcastNewSeasonComing")
    self.Events.evtNewSeasonComing:Invoke()
end

function TournamentServer:SetIsInPreReqNewSeasonTime(isInPreReqNewSeasonTime)
    logerror("TournamentServer:SetIsInPreReqNewSeasonTime",isInPreReqNewSeasonTime)
    self._isInPreReqNewSeasonTime=isInPreReqNewSeasonTime
end

function TournamentServer:GetIsInPreReqNewSeasonTime()
    loginfo("TournamentServer:GetIsInPreReqNewSeasonTime")
    return self._isInPreReqNewSeasonTime
end

function TournamentServer:CalSeasonLevelByScore(score,curSerial,hasAttended)--通过排位分计算段位
    loginfo("TournamentServer:CalSeasonLevelByScore","score",score,"curSerial",curSerial,"hasAttended",hasAttended)
    score=score or 0
    local TournamentTierTable=Facade.TableManager:GetTable("TournamentTier")
    if table.nums(TournamentTierTable)==0 then
        logerror("TournamentServer:CalSeasonLevelByScore empty table!!!")
        return 0,0
    end
    if not hasAttended then
        return 0,0
    end
    local newTournamentTierTable={}
    for k,v in pairs(TournamentTierTable)do
        if table.contains(v.SeasonID or {},curSerial) then
            table.insert(newTournamentTierTable,v)
        end
    end
    table.sort(newTournamentTierTable,function(a,b)return a.MinPoint<b.MinPoint end)
    local rankData=nil
    for k,v in pairs(newTournamentTierTable)do
        if score>=v.MinPoint then
            rankData=v
        else
            break
        end
    end

    if rankData then
        return rankData.ID,rankData.TierTypeID
    else
        local minRankData=newTournamentTierTable[1]
        return minRankData.ID,minRankData.TierTypeID
    end

end

function TournamentServer:CalNotReceivedLevels(maxMajorLevel,rewardedLevels,majorSummaries,curSerial,hasAttended)--筛选未领取段位
    loginfo("TournamentServer:CalNotReceivedLevels","maxMajorLevel",maxMajorLevel,"curSerial",curSerial,"hasAttended",hasAttended)
    logtable(rewardedLevels,true)

    local TournamentRewardsTable=Facade.TableManager:GetTable("TournamentRewards")
    if table.nums(TournamentRewardsTable)==0 then
        logerror("TournamentServer:CalNotReceivedLevels empty table!!!")
        return {}
    end
    if not hasAttended then
        return {}
    end
    local notReceivedLevels={}
    for k,v in pairs(TournamentRewardsTable)do
        if v.SeasonID==curSerial and v.TierID<=maxMajorLevel and not table.contains(rewardedLevels,v.TierID)then
            local gameCount=0
            for i,j in pairs(majorSummaries or {})do
                if j.majorid>=v.TierID then
                    gameCount=gameCount+j.total_game_cnt
                end
            end
            if gameCount>=v.RewardConditionPar then
                table.insert(notReceivedLevels,v.TierID)
            end
            
        end
    end
    table.sort(notReceivedLevels,function(a,b)return a<b end)
    return notReceivedLevels
end

function TournamentServer:CalCommanderSeasonLevelByScore(score,curSerial,hasAttended)--通过排位分计算段位
    loginfo("TournamentServer:CalCommanderSeasonLevelByScore","score",score,"curSerial",curSerial,"hasAttended",hasAttended)
    score=score or 0
    local TournamentTierTable=Facade.TableManager:GetTable("CommanderTier")
    if not TournamentTierTable or table.nums(TournamentTierTable)==0 then
        logerror("TournamentServer:CalCommanderSeasonLevelByScore empty table!!!")
        return 0,0
    end
    if not hasAttended then
        return 0,0
    end
    local newTournamentTierTable={}
    for k,v in pairs(TournamentTierTable)do
        if table.contains(v.SeasonID or {},curSerial) then
            table.insert(newTournamentTierTable,v)
        end
    end
    table.sort(newTournamentTierTable,function(a,b)return a.MinPoint<b.MinPoint end)
    local rankData=nil
    for k,v in pairs(newTournamentTierTable)do
        if score>=v.MinPoint then
            rankData=v
        else
            break
        end
    end

    if rankData then
        return rankData.ID,rankData.TierTypeID
    else
        local minRankData=newTournamentTierTable[1]
        return minRankData.ID,minRankData.TierTypeID
    end

end

function TournamentServer:CalCommanderNotReceivedLevels(maxMajorLevel,rewardedLevels,majorSummaries,curSerial,hasAttended)--筛选未领取段位
    loginfo("TournamentServer:CalCommanderNotReceivedLevels","maxMajorLevel",maxMajorLevel,"curSerial",curSerial,"hasAttended",hasAttended)
    logtable(rewardedLevels,true)

    local TournamentRewardsTable=Facade.TableManager:GetTable("CommanderRewards")
    if table.nums(TournamentRewardsTable)==0 then
        logerror("TournamentServer:CalCommanderNotReceivedLevels empty table!!!")
        return {}
    end
    if not hasAttended then
        return {}
    end
    local notReceivedLevels={}
    for k,v in pairs(TournamentRewardsTable)do
        if v.SeasonID==curSerial and v.TierID<=maxMajorLevel and not table.contains(rewardedLevels,v.TierID)then
            local gameCount=0
            for i,j in pairs(majorSummaries or {})do
                if j.majorid>=v.TierID then
                    gameCount=gameCount+j.total_game_cnt
                end
            end
            if gameCount>=v.RewardConditionPar then
                table.insert(notReceivedLevels,v.TierID)
            end
            
        end
    end
    table.sort(notReceivedLevels,function(a,b)return a<b end)
    return notReceivedLevels
end

function TournamentServer:TransCommanderTitleListToMap(titleList)--称号list转成map
    loginfo("TournamentServer:TransCommanderTitleListToMap")
    local commanderTitle2Count={}
    for k,v in pairs(titleList or {})do
        commanderTitle2Count[v.id]=v.times
    end
    return commanderTitle2Count
end

function TournamentServer:GenSimpleSeasonInfo(seasonRankInfo)--生成简要赛季信息，打log用
    loginfo("TournamentServer:GenSimpleSeasonInfo")
    local simpleSeasonInfo={}
    simpleSeasonInfo.CurSerial=seasonRankInfo.CurSerial
    simpleSeasonInfo.BeginSerial=seasonRankInfo.BeginSerial
    simpleSeasonInfo.PrevAttendedSerial=seasonRankInfo.PrevAttendedSerial
    simpleSeasonInfo.StartTime=seasonRankInfo.StartTime
    simpleSeasonInfo.EndTime=seasonRankInfo.EndTime
    simpleSeasonInfo.UIStartTime=seasonRankInfo.UIStartTime
    simpleSeasonInfo.UIEndTime=seasonRankInfo.UIEndTime
    simpleSeasonInfo.NextSerialStartTime=seasonRankInfo.NextSerialStartTime
    for i=1,seasonRankInfo.CurSerial do
        if seasonRankInfo[i] and seasonRankInfo[i].RankSummary then
            simpleSeasonInfo[i]={}
            simpleSeasonInfo[i].RankSummary={}
            simpleSeasonInfo[i].RankSummary.HasAttended=seasonRankInfo[i].RankSummary.HasAttended
            simpleSeasonInfo[i].RankSummary.Score=seasonRankInfo[i].RankSummary.Score
            
            simpleSeasonInfo[i].CommanderRankSummary={}
            simpleSeasonInfo[i].CommanderRankSummary.HasAttended=seasonRankInfo[i].CommanderRankSummary.HasAttended
            simpleSeasonInfo[i].CommanderRankSummary.Score=seasonRankInfo[i].CommanderRankSummary.Score

        end
    end        
    return simpleSeasonInfo
end

function TournamentServer:FetchSimplePropInfoFromList(propInfoList)
    loginfo("TournamentServer:FetchSimplePropInfoFromList")
    local simplePropInfoList={}
    for k,v in pairs(propInfoList or {})do
        table.insert(simplePropInfoList,{id=v.id,num=v.num})
    end
    return simplePropInfoList

end

function TournamentServer:GetSeasonRankInfo(serial)
    loginfo("TournamentServer:GetSeasonRankInfo",serial)
    serial=serial or 0
    return self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary
end

function TournamentServer:GetCommanderSeasonRankInfo(serial)
    loginfo("TournamentServer:GetCommanderSeasonRankInfo",serial)
    serial=serial or 0
    return self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary
end

function TournamentServer:GetCurSerial()
    loginfo("TournamentServer:GetCurSerial")
    return self.seasonRankInfo.CurSerial
end

function TournamentServer:GetBeginSerial()
    loginfo("TournamentServer:GetBeginSerial")
    return self.seasonRankInfo.BeginSerial
end

function TournamentServer:GetPrevAttendedSerial()--上个参与赛季
    loginfo("TournamentServer:GetPrevAttendedSerial")
    return self.seasonRankInfo.PrevAttendedSerial
end

function TournamentServer:GetSeasonStartTime()
    loginfo("TournamentServer:GetSeasonStartTime")
    return self.seasonRankInfo.StartTime or 0
end

function TournamentServer:GetSeasonEndTime()
    loginfo("TournamentServer:GetSeasonEndTime")
    return self.seasonRankInfo.EndTime or 0--老服的赛季结束时间会延长（如果希望玩家看到正常的结束时间，用下面的UIEndTime）
end

function TournamentServer:GetNextSerialStartTime()
    loginfo("TournamentServer:GetNextSerialStartTime")
    return self.seasonRankInfo.NextSerialStartTime or 0--有下赛季配置的时候才不为0
end

function TournamentServer:GetUIStartTime()
    loginfo("TournamentServer:GetUIStartTime")
    return self.seasonRankInfo.UIStartTime or 0
end

function TournamentServer:GetUIEndTime()
    loginfo("TournamentServer:GetUIEndTime")
    return self.seasonRankInfo.UIEndTime or 0--正常结束时间
end

function TournamentServer:GetLastSeasonRewards()--上赛季奖励(结算奖励)
    loginfo("TournamentServer:GetLastSeasonRewards")
    return self.seasonRankInfo.LastSeasonRewards or {}
end

function TournamentServer:GetHasAttended(serial)--是否参加过积分赛
    loginfo("TournamentServer:GetHasAttended",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.HasAttended
    else
        return false
    end
end

function TournamentServer:GetMinorLevel(serial)--小段位
    loginfo("TournamentServer:GetMinorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.LevelMinor
    else
        return 0
    end
end

function TournamentServer:GetMaxMinorLevel(serial)--最高小段位
    loginfo("TournamentServer:GetMaxMinorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.LevelMinorMax
    else
        return 0
    end
end

function TournamentServer:GetMajorLevel(serial)--大段位
    loginfo("TournamentServer:GetMajorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.LevelMajor
    else
        return 0
    end
end

function TournamentServer:GetMaxMajorLevel(serial)--最高大段位
    loginfo("TournamentServer:GetMaxMajorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.LevelMajorMax
    else
        return 0
    end
end

function TournamentServer:GetRankScore(serial)--排位分
    loginfo("TournamentServer:GetRankScore",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.Score
    else
        return 0
    end
end

function TournamentServer:GetMaxRankScore(serial)--最高排位分
    loginfo("TournamentServer:GetMaxRankScore",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.ScoreMax
    else
        return 0
    end
    
end

---@param listType SeasonRankList
function TournamentServer:GetSeasonRankList(listType)--天梯列表：好友/全服
    loginfo("TournamentServer:GetSeasonRankList",listType)
    listType=listType or SeasonRankList.SOL_Friends
    return self.seasonRankList[listType] or {}
end

function TournamentServer:GetIsRankListPunished()--排行榜是否被封禁
    loginfo("TournamentServer:GetIsRankListPunished")
    return self.seasonRankList.Punished
end

function TournamentServer:GetRankListPunishInfo()--排行榜封禁信息
    loginfo("TournamentServer:GetRankListPunishInfo")
    return self.seasonRankList.PunishInfo
end

function TournamentServer:GetNotReceivedLevels()--未领取段位
    loginfo("TournamentServer:GetNotReceivedLevels")
    if self.seasonRankInfo[0] and self.seasonRankInfo[0].RankSummary and self.seasonRankInfo[0].RankSummary.LevelsNotRewarded then
        return self.seasonRankInfo[0].RankSummary.LevelsNotRewarded
    else
        return {}
    end
end

function TournamentServer:GetReceivedLevels()--已领取段位
    loginfo("TournamentServer:GetReceivedLevels")
    if self.seasonRankInfo[0] and self.seasonRankInfo[0].RankSummary and self.seasonRankInfo[0].RankSummary.LevelsRewarded then
        return self.seasonRankInfo[0].RankSummary.LevelsRewarded
    else
        return {}
    end
end

function TournamentServer:GetRankEvents(serial)--获取赛季事件
    loginfo("TournamentServer:GetRankEvents",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
        return self.seasonRankInfo[serial].RankSummary.RankEvents
    else
        return nil
    end

end

function TournamentServer:GetMajorSummaries(serial,majorLevel)--获取历史段位数据
    loginfo("TournamentServer:GetMajorSummaries","serial",serial,"majorLevel",majorLevel)
    serial=serial or 0
    if not majorLevel then--所有段位
        if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
            return self.seasonRankInfo[serial].RankSummary.MajorSummaries
        else
            return nil
        end
    else--指定段位
        local majorSummaries=nil
        if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].RankSummary then
            majorSummaries=self.seasonRankInfo[serial].RankSummary.MajorSummaries
        end
        for k,v in pairs(majorSummaries or {})do
            if v.majorid==majorLevel then
                return v
            end
        end
        return nil

    end

end

function TournamentServer:GetCommanderLastSeasonRewards()--上赛季奖励(结算奖励)
    loginfo("TournamentServer:GetCommanderLastSeasonRewards")
    return self.seasonRankInfo.CommanderLastSeasonRewards or {}
end

function TournamentServer:GetCommanderHasAttended(serial)--是否参加过指挥官模式
    loginfo("TournamentServer:GetCommanderHasAttended",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.HasAttended
    else
        return false
    end
end

function TournamentServer:GetCommanderMinorLevel(serial)--小段位
    loginfo("TournamentServer:GetCommanderMinorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.LevelMinor
    else
        return 0
    end
end

function TournamentServer:GetCommanderMaxMinorLevel(serial)--最高小段位
    loginfo("TournamentServer:GetCommanderMaxMinorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.LevelMinorMax
    else
        return 0
    end
end

function TournamentServer:GetCommanderMajorLevel(serial)--大段位
    loginfo("TournamentServer:GetCommanderMajorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.LevelMajor
    else
        return 0
    end
end

function TournamentServer:GetCommanderMaxMajorLevel(serial)--最高大段位
    loginfo("TournamentServer:GetCommanderMaxMajorLevel",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.LevelMajorMax
    else
        return 0
    end
end

function TournamentServer:GetCommanderRankScore(serial)--排位分
    loginfo("TournamentServer:GetCommanderRankScore",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.Score
    else
        return 0
    end
end

function TournamentServer:GetCommanderMaxRankScore(serial)--最高排位分
    loginfo("TournamentServer:GetCommanderMaxRankScore",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.ScoreMax
    else
        return 0
    end
    
end

function TournamentServer:GetCommanderNotReceivedLevels()--未领取段位
    loginfo("TournamentServer:GetCommanderNotReceivedLevels")
    if self.seasonRankInfo[0] and self.seasonRankInfo[0].CommanderRankSummary and self.seasonRankInfo[0].CommanderRankSummary.LevelsNotRewarded then
        return self.seasonRankInfo[0].CommanderRankSummary.LevelsNotRewarded
    else
        return {}
    end
end

function TournamentServer:GetCommanderReceivedLevels()--已领取段位
    loginfo("TournamentServer:GetCommanderReceivedLevels")
    if self.seasonRankInfo[0] and self.seasonRankInfo[0].CommanderRankSummary and self.seasonRankInfo[0].CommanderRankSummary.LevelsRewarded then
        return self.seasonRankInfo[0].CommanderRankSummary.LevelsRewarded
    else
        return {}
    end
end

function TournamentServer:GetCommanderRankEvents(serial)--获取赛季事件
    loginfo("TournamentServer:GetCommanderRankEvents",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.RankEvents
    else
        return nil
    end

end

function TournamentServer:GetCommanderMajorSummaries(serial,majorLevel)--获取历史段位数据
    loginfo("TournamentServer:GetCommanderMajorSummaries","serial",serial,"majorLevel",majorLevel)
    serial=serial or 0
    if not majorLevel then--所有段位
        if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
            return self.seasonRankInfo[serial].CommanderRankSummary.MajorSummaries
        else
            return nil
        end
    else--指定段位
        local majorSummaries=nil
        if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
            majorSummaries=self.seasonRankInfo[serial].CommanderRankSummary.MajorSummaries
        end
        for k,v in pairs(majorSummaries or {})do
            if v.majorid==majorLevel then
                return v
            end
        end
        return nil

    end

end

function TournamentServer:GetCommanderTitleCountMap(serial)--获取胜者为王赛季获得称号次数信息
    loginfo("TournamentServer:GetCommanderTitleCountMap",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.Tile2CountMap
    else
        return nil
    end
end

function TournamentServer:GetCommanderAbilityData(serial)--获取胜者为王赛季能力数据
    loginfo("TournamentServer:GetCommanderAbilityData",serial)
    serial=serial or 0
    if self.seasonRankInfo[serial] and self.seasonRankInfo[serial].CommanderRankSummary then
        return self.seasonRankInfo[serial].CommanderRankSummary.AbilityData
    else
        return nil
    end
end

function TournamentServer:GetCommanderAbilitiesPercentageMap()--获取胜者为王各项能力排名
    loginfo("TournamentServer:GetCommanderAbilitiesPercentageMap")
    return self.CommanderAbilitiesPercentageMap
end

function TournamentServer:SetGMAbilityPercentInfo(gmAbilityPercentStr)
    loginfo("TournamentServer:SetGMAbilityPercentInfo",gmAbilityPercentStr)
    if gmAbilityPercentStr==nil then
        self.gmAbilityPercentInfo=nil
    else
        local abilityOrder={
            RankDataType.TDM_VICTORY_UNIT_COMMAND_SCORE,
            RankDataType.TDM_VICTORY_UNIT_VEHICLE_KILL_SCORE,
            RankDataType.TDM_VICTORY_UNIT_INFANTRY_KILL_SCORE,
            RankDataType.TDM_VICTORY_UNIT_INFANTRY_RESCUE_SCORE
        }
        self.gmAbilityPercentInfo={}
        local percentList=string.split(gmAbilityPercentStr,",")
        local percentList1={}
        for k,v in pairs(percentList)do
            table.insert(percentList1,tonumber(v))
        end
        for k,v in pairs(abilityOrder)do
            self.gmAbilityPercentInfo[v]=percentList1[k] or 1
        end
    end
end

return TournamentServer
