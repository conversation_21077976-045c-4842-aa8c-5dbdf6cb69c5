----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPay)
----- LOG FUNCTION AUTO GENERATE END -----------



local CentauriLogic = {}
local this = CentauriLogic
local DFMGameCentauri = import "DFMGameCentauri"
local _centauriIns = DFMGameCentauri.Get(GetGameInstance())
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local LocalizeTool= require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local Json = JsonFactory.createJson()
--- BEGIN MODIFICATION @ VIRTUOS: PS5提审使用特定的域名
local UGameVersionUtils = import "GameVersionUtils"
--- END MODIFICATION

--#region centauri sdk
local _enbleLog = true -- sdk日志开关
local _enbleSaveLog = true -- sdk存储日志开关
--#endregion
local _businessId ="900001018" -- 业务ID

local _bCheckWebBrowserPaySuc = false

CentauriLogic.AddListener = function ()
    Module.Login.Config.Events.evtOnLoginSuccess:AddListener(this._OnLoginSuccess)
    Server.PayServer.Events.evtRechargeDataChanged:AddListener(this._OnServerRechargeDataChanged)
    Server.PayServer.Events.evtOnPayServerError:AddListener(this._OnPayServerError)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:AddListener(this._OnGCloudSDKWebBrowserCallback)
    Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserCallback:AddListener(this._OnSDKWebBrowserCallback)
    Server.CurrencyServer.Events.evtCurrencyRefreshDiamondNum:AddListener(this._OnCurrencyRefreshDiamondNum)
--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    if IsConsole() then
        Server.PayServer.Events.evtOnFirstEnterModeHall:AddListener(this.ReapplyReceipt)
    end
--- END MODIFICATION
end

CentauriLogic.RemoveListener = function ()
    Module.Login.Config.Events.evtOnLoginSuccess:RemoveListener(this._OnLoginSuccess)
    Server.PayServer.Events.evtRechargeDataChanged:RemoveListener(this._OnServerRechargeDataChanged)
    Server.PayServer.Events.evtOnPayServerError:RemoveListener(this._OnPayServerError)
    Module.GCloudSDK.Config.Events.evtOnGCloudSDKWebBrowserCallback:RemoveListener(this._OnGCloudSDKWebBrowserCallback)
    Module.GCloudSDK.Config.Events.evtOnSDKWebBrowserCallback:RemoveListener(this._OnSDKWebBrowserCallback)
    Server.CurrencyServer.Events.evtCurrencyRefreshDiamondNum:RemoveListener(this._OnCurrencyRefreshDiamondNum)
--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    if IsConsole() then
        Server.PayServer.Events.evtOnFirstEnterModeHall:RemoveListener(this.ReapplyReceipt)
    end
--- END MODIFICATION
end

CentauriLogic._OnLoginSuccess = function ()
    CentauriLogic._Init()
end

CentauriLogic._Init = function ()
    if not _centauriIns then
        logerror("CentauriLogic._Init _centauriIns is nil")
        return
    end
    local openId = Server.SDKInfoServer:GetOpenIdStr()
    local offerId = Server.PayServer:GetOfferId()
    local idcInfo = "{\"release\":{\"k_domain\":\"ms.singaporepaya.com\",\"k_domain_hb\":\"reportms.singaporepaya.com\",\"k_ip_list\":[\"*************\"]},\"sandbox\":{\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]},\"dev\":{\"k_domain\":\"presandbox.centauriglobal.com\",\"k_domain_hb\":\"presandbox.centauriglobal.com\",\"k_ip_list\":[]}}"
    if IsBuildRegionCN() then
        idcInfo = "{\"release\":{\"k_domain\":\"prod-sdk.sgmidas.df.qq.com\",\"k_domain_hb\":\"prod-log.sgmidas.df.qq.com\",\"k_ip_list\":[\"***********\"]},\"sandbox\":{\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]},\"dev\":{\"k_domain\":\"presandbox.centauriglobal.com\",\"k_domain_hb\":\"presandbox.centauriglobal.com\",\"k_ip_list\":[]}}"
    end

    --- BEGIN MODIFICATION @ VIRTUOS: PS5提审使用特定的域名
    if IsPS5() and UGameVersionUtils.IsInReview() then
        idcInfo = "{\"release\":{\"k_domain\":\"ms-sony-submission-env.singaporepaya.com\",\"k_domain_hb\":\"ms-sony-submission-env.singaporepaya.com\",\"k_ip_list\":[]},\"sandbox\":{\"k_domain\":\"sandbox.centauriglobal.com\",\"k_domain_hb\":\"sandbox.centauriglobal.com\",\"k_ip_list\":[\"*************\"]},\"dev\":{\"k_domain\":\"presandbox.centauriglobal.com\",\"k_domain_hb\":\"presandbox.centauriglobal.com\",\"k_ip_list\":[]}}"
    end
    --- END MODIFICATION
    
    local paymentMethod = Module.Pay.Config.CentauriPaymentMethod.UNKNOW
    if PLATFORM_ANDROID then
        paymentMethod = Module.Pay.Config.CentauriPaymentMethod.GOOGLE_PLAY
    elseif PLATFORM_IOS then
        paymentMethod = Module.Pay.Config.CentauriPaymentMethod.APPLE_STORE
    elseif PLATFORM_WINDOWS then
        if VersionUtil.IsGameChannelOfficial() then
            paymentMethod = Module.Pay.Config.CentauriPaymentMethod.MP
        elseif VersionUtil.IsGameChannelWeGame() then
            paymentMethod = Module.Pay.Config.CentauriPaymentMethod.MP
        elseif VersionUtil.IsGameChannelSteam() then
            paymentMethod = Module.Pay.Config.CentauriPaymentMethod.STEAM
        elseif VersionUtil.IsGameChannelEpic() then
            paymentMethod = Module.Pay.Config.CentauriPaymentMethod.MP
        else
            paymentMethod = Module.Pay.Config.CentauriPaymentMethod.MP
        end
    --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
    elseif IsXSX() then
        paymentMethod = Module.Pay.Config.CentauriPaymentMethod.MICROSOFT_STORE
    elseif IsPS5() then
        paymentMethod = Module.Pay.Config.CentauriPaymentMethod.PLAY_STATION
    --- END MODIFICATION
    end

    _centauriIns.EnableLog = _enbleLog
    _centauriIns.EnableSaveLog = _enbleSaveLog
    _centauriIns.IDCInfo = idcInfo --域名 ip 信息，json 格式 
    _centauriIns.bSandbox = (not VersionUtil.IsShipping())
    _centauriIns.OpenId = openId
    if IsHD() then
        --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
        if IsConsole() then
            _centauriIns.ServerId = tostring(Server.SDKInfoServer:GetAreaId())
        else
            _centauriIns.ServerId = Server.SDKInfoServer:GetZoneId() or "1" --游戏服务器大区 id ,游戏不分大区则默认值为 1 
        end
        --- END MODIFICATION
    else
        _centauriIns.ServerId = Server.SDKInfoServer:GetZoneId() or "1" --移动端使用与后台一致的ID
    end
    
    _centauriIns.OfferId = offerId
	
    _centauriIns.RegionCode = Server.PayServer:GetRegistrationRegion()
    _centauriIns.CurrencyCode = Server.PayServer:GetRegistrationCurrencyCode()

    _centauriIns.PaymentMethod = paymentMethod

    _businessId = _centauriIns.BusinessId

    loginfo("CentauriLogic._Init _businessId:", _businessId)
    logerror("CentauriLogic._Init RegionCode:", Server.PayServer:GetRegistrationRegion(), " Currency:", Server.PayServer:GetRegistrationCurrencyCode())
    
    _centauriIns:Init()
end

local _bPay = false
local _Currency = ""
local _Price = 0
local _payTimerHandle = nil
local _payTimeout = 30

--- BEGIN MODIFICATION @ VIRTUOS: 增加一个公开的初始化函数，用于外部调用
CentauriLogic.ReInit = function ()
    CentauriLogic._Init()
end
--- END MODIFICATION

CentauriLogic.Pay = function (pay_info)
    if not _centauriIns then
        logerror("CentauriLogic.Pay _centauriIns is nil")
        return
    end
    if PLATFORM_ANDROID and Server.SDKInfoServer:IsAndroidSelfPublishChannel() then
        local url = 'https://www.midasbuy.com/midasbuy/'
        if not VersionUtil.IsShipping() then
            url = 'https://sandbox.midasbuy.com/midasbuy/'
        end
        local registrationRegion = Server.PayServer:GetRegistrationRegion()
        local regionURL = Server.PayServer:GetMidasbuyURL(registrationRegion)
        regionURL = string.lower(regionURL)
        if regionURL == '' then
            url = url .. '?appid=**********'
        else
            url = url .. regionURL .. '/buy/?appid=**********'
        end

        local function urlEncode(url)
            if url == nil then return "" end
            url = string.gsub(url, "([^%w%-%.%_%~])", function(c)
                return string.format("%%%02X", string.byte(c))
            end)
            return url
        end
        
        local openId = Server.AccountServer:GetPlayerIdStr()
        local playerId = Module.RoleInfo:GOpenIdEncryption(openId)
        local zoneId = tostring(Server.SDKInfoServer:GetZoneId())
        local playerName = Server.SDKInfoServer:GetUserName()
        local storeChannel = tostring(Server.SDKInfoServer:GetStoreChannel())
        url = url .. '&openid=' .. openId
        url = url .. '&charac_id=' .. playerId
        url = url .. '&zoneid=' .. zoneId
        url = url .. '&charac_name=' .. playerName
        url = url .. '&from=__mds_buy_dfm.pack.' .. storeChannel
        if VersionUtil.IsShipping() and not VersionUtil.IsRelease() then
            -- shippingTest拉起选择界面
            url = 'https://www.midasbuy.com/static_page/newversion.html?redirect=' .. urlEncode(url)
        end
        logerror("[CentauriLogic] Pay MidasBuy url:", url)
        local webContentTitle = Module.Pay.Config.Loc.WebViewTitle
        Module.GCloudSDK:OpenUrl(url, 1, false,
                true, '{\"webview_window_scale\":1}', false, { useSDKWebBrowser = true })
        --Module.GCloudSDK:OpenSDKWebBrowserUrl({url = url, title = webContentTitle, width_scale = scale, height_scale = scale})
        Module.GCloudSDK:OnPayMidasbuyReport()
        return
    end
    -- local payInfo = {
    --     data = {
    --         app_id = "1460000404",
    --         res_app_id = "",
    --         biz_app_id = "",
    --         transaction_id = "E-230811080011546028",
    --         reference_id = "",
    --         transaction = {
    --             pay_desc = "",
    --             pay_channel = "os_steam",
    --             sub_channel = "1",
    --             currency = "USD",
    --             amount = "",
    --             region = "US",
    --             application_context = nil,
    --             coin_info = nil
    --         },
    --         buy_info = {
    --             login_number = "",
    --             login_token = "",
    --             login_type = 1,
    --             pay_number = "",
    --             pay_token = "",
    --             provide_number = "1926974186468563355",
    --             server_id = "",
    --             role_id = "",
    --             platform = 3,
    --             address = nil,
    --             user_ip = ""
    --         },
    --         purchase_list = {
    --             {
    --                 product_id = "10086",
    --                 quantity = "1",
    --                 product_type = "bg",
    --                 product_name = "10086",
    --                 price = "10",
    --                 provide_app_id = "900000934",
    --                 server_id = "1",
    --                 role_id = "123",
    --                 provide_id = "",
    --                 provide_id_type = "",
    --                 role_name = "",
    --                 area = "",
    --                 partition = "",
    --                 platid = "",
    --                 join_model = {
    --                     list = {}
    --                 },
    --                 real_product_id = ""
    --             }
    --         },
    --         language = "en",
    --         scene = ""
    --     },
    --     sign = "vdqXvH5DQw=="
    -- }

    -- local jsonData = Json.encode(payInfo)
    -- local jsonStr = tostring(jsonData)
    if PLATFORM_WINDOWS and _bPay then
        logerror("CentauriLogic.Pay _bPay")
        Module.CommonTips:ShowSimpleTip(Module.Pay.Config.Loc.NotPayFinish)
        return
    end

    logerror("CentauriLogic.Pay pay_info:", pay_info)
    _centauriIns.PayInfo = pay_info

    if PLATFORM_ANDROID or PLATFORM_IOS then
        -- ReportRevenue
        local pay_info_decode = Json.decode(pay_info)
        local function tryGet(t, ...)
            local current = t
            for i = 1, select("#", ...) do
                local key = select(i, ...)
                if current[key] ~= nil then
                    current = current[key]
                else
                    return nil
                end
            end
            return current
        end
        if pay_info_decode == nil then
            logerror("CentauriLogic.Pay pay_info_decode is nil")
        else
            local price = 0
            local currency = tryGet(pay_info_decode, "data", "transaction", "currency")
            local productID = tryGet(pay_info_decode, "data", "purchase_list", 1, "product_id")
            local original_price_multiplier = 100
            if currency and productID then
                local productItemData = Server.PayServer:GetRechargeItem(productID) or Server.PayServer:GetProductItem(productID)
                if productItemData then
                    if productItemData.decimal_point then
                        original_price_multiplier = 10 ^ productItemData.decimal_point
                    end
                    price = ""
                    if productItemData.original_price then
                        price = productItemData.original_price / original_price_multiplier
                    end
                    logerror("CentauriLogic.Pay currency&price: ", currency, price)
                    logerror("CentauriLogic.Pay multiplier: ", original_price_multiplier)
                    _Currency = currency
                    _Price = tostring(price)
                else
                    logerror("CentauriLogic.Pay productID not found ", productID)
                end
            else
                logerror("CentauriLogic.Pay currency or productId not found")
            end
        end
    end

    if PLATFORM_WINDOWS then
        CentauriLogic._RemovePayLock()
        CentauriLogic._AddPayLock()
    end

    _centauriIns:Pay()
end

--增加支付锁
CentauriLogic._AddPayLock = function ()
    if PLATFORM_WINDOWS then
        loginfo("CentauriLogic._AddPayLock _bPay = true")
        _bPay = true
        _payTimerHandle = Timer.DelayCall(_payTimeout, function ()
            loginfo("CentauriLogic._AddPayLock _bPay = false")
            _bPay = false
        end)
    end
end

--去除支付锁
CentauriLogic._RemovePayLock = function ()
    if PLATFORM_WINDOWS then
        loginfo("CentauriLogic._RemovePayLock _bPay = false")
        _bPay = false
        if _payTimerHandle then
            loginfo("CentauriLogic._RemovePayLock CancelDelay")
            Timer.CancelDelay(_payTimerHandle)
            _payTimerHandle = nil
        end
    end
end

--官网渠道需要通过Pay返回的url打开网页支付
CentauriLogic.OnCtiFinished = function (resultCode, respInfo)
    loginfo("CentauriLogic.OnCtiFinished resultCode:", resultCode, "--respInfo:", respInfo)
    if PLATFORM_WINDOWS then
        if resultCode == 0 and not string.isempty(respInfo) and string.starts_with(respInfo, "http") then
            local webContentTitle = Module.Pay.Config.Loc.WebViewTitle
            local scale = 0.8
            _bCheckWebBrowserPaySuc = true
            Module.GCloudSDK:OpenSDKWebBrowserUrl({url = respInfo, title = webContentTitle, width_scale = scale, height_scale = scale})
            if PLATFORM_WINDOWS then
                CentauriLogic._RemovePayLock()
            end
        end
    elseif PLATFORM_ANDROID or PLATFORM_IOS then
        loginfo("CentauriLogic.OnCtiFinished Mobile Pay Finished")
        if resultCode == 0 then
            Module.GCloudSDK:OnPaySucceedReport(_Currency, _Price)
            loginfo("CentauriLogic.OnCtiFinished ReportRevenue", _Currency, _Price)
        end
        Module.Pay:OnPayFinished(resultCode, respInfo)
    end
end

--- 支付结束
---@param resultCode number 返回码
---@param resultMsg string 错误信息
CentauriLogic.OnPayFinished = function(resultCode, resultMsg)
    if PLATFORM_WINDOWS then
        CentauriLogic._RemovePayLock()
    end
end

--GetProductInfo不支持并发，所以加个标记，避免多次请求崩溃
local _bGetProductInfo = false
--获取物品信息
CentauriLogic.GetProductInfo = function (productIdList)
    if not _centauriIns then
        logerror("CentauriLogic.GetProductInfo _centauriIns is nil")
        return
    end
    if productIdList == nil then
        logerror("CentauriLogic.GetProductInfo productIdList is nil")
        return
    end
    if #productIdList < 1 then
        logerror("CentauriLogic.GetProductInfo productIdList is zero")
        return
    end
    if _bGetProductInfo then
        logerror("CentauriLogic.GetProductInfo _bGetProductInfo")
        return
    end
    _bGetProductInfo = true
    _centauriIns.SkuLists = productIdList
    _centauriIns:GetProductInfo()
end

--获取物品信息结束
CentauriLogic.OnGetProductInfoFinished = function (resultCode, respInfo)
    _bGetProductInfo = false
    if resultCode ~= 0 then
        logerror("CentauriLogic.OnGetProductInfoFinished resultCode:", resultCode)
        return
    end
    if string.isempty(respInfo) then
        logerror("CentauriLogic.OnGetProductInfoFinished respInfo is nil")
        return
    end

    --- BEGIN MODIFICATION @ VIRTUOS: 接入PS5米大师支付
    if IsPS5() then
        if respInfo == "[]" then
            Server.PayServer:UpdatePSShopState(true)
            logerror("CentauriLogic.OnGetProductInfoFinished respInfo is []")
            return
        end
        Server.PayServer:UpdatePSShopState(false)
    end
    --- END MODIFICATION

    logerror("CentauriLogic.OnGetProductInfoFinished respInfo:", respInfo)
    local productInfoList = Json.decode(respInfo)
    Server.PayServer:RefreshProductInfo(productInfoList)
end

--- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
--补发货结束
CentauriLogic.OnReapplyReceiptFinished = function (resultCode, respInfo)
    if IsConsole() then
        if resultCode ~= 0 then
            logerror("CentauriLogic.OnReapplyReceiptFinished resultCode:", resultCode)
            return
        end
        
        -- 发货成功重新刷新货币
        Server.CurrencyServer:ReqAllCurrencyData()
    end
end
--- END MODIFICATION

--请求补发货
CentauriLogic.ReapplyReceipt = function ()
    if not _centauriIns then
        logerror("CentauriLogic.ReapplyReceipt _centauriIns is nil")
        return
    end
    _centauriIns:ReapplyReceipt()
end

--服务器物品信息变化
CentauriLogic._OnServerRechargeDataChanged = function ()
    local rechargeItemList = Server.PayServer:GetRechargeItemList()
    if rechargeItemList == nil then
        logerror("CentauriLogic._OnServerRechargeDataChanged rechargeItemList is nil")
        return
    end
    --local productIdList = {"PRO-F1MU0LH3JPSE"}
    local productIdList = {}
    for _, rechargeItem in pairs(rechargeItemList) do
        table.insert(productIdList, rechargeItem.product_id)
    end
    CentauriLogic.GetProductInfo(productIdList)
end

CentauriLogic.GetBusinessId = function ()
    return _businessId
end

--服务器下单失败，返回错误码
CentauriLogic._OnPayServerError = function (errorCode)
    if errorCode == Err.PayRiskCtlCheckFailed then
        if Server.SDKInfoServer:IsRegionJapan() then
            --Module.CommonTips:ShowConfirmWindowWithSingleBtn(Module.Pay.Config.Loc.PayJapanComplianceLimitTip)
            Facade.UIManager:AsyncShowUI(UIName2ID.PayComplianceConfirmWindow, nil, nil, Module.Pay.Config.Loc.PayJapanComplianceLimitTitle, Module.Pay.Config.Loc.PayJapanComplianceLimitTip)
        elseif Server.SDKInfoServer:IsRegionKorea() then
            --Module.CommonTips:ShowConfirmWindowWithSingleBtn(Module.Pay.Config.Loc.PayKoreaComplianceLimitTip)
            Facade.UIManager:AsyncShowUI(UIName2ID.PayComplianceConfirmWindow, nil, nil, Module.Pay.Config.Loc.PayKoreaComplianceLimitTitle, Module.Pay.Config.Loc.PayKoreaComplianceLimitTip)
        end
    elseif errorCode == Err.PayRiskCtlCheckAgain then
        if Server.SDKInfoServer:IsRegionJapan() then
            Facade.UIManager:AsyncShowUI(UIName2ID.PayJapanComplianceWindow)
        elseif Server.SDKInfoServer:IsRegionKorea() then
            local age = 0
            --是否成年
            if Server.SDKInfoServer:GetPlayerAdultState() > 0 then
                --组件没有传年龄，设置一个虚假年龄 >= 19
                age = 25
            else
                --组件没有传年龄，设置一个虚假年龄 < 19
                age = 13
            end
            Server.PayServer:ReqAccountUpdateRiskctlInfo(age)
        end
    end
end


CentauriLogic._OnGCloudSDKWebBrowserCallback = function (type, jsonData)
    loginfo("CentauriLogic._OnThirdSDKWebBrowserCallback type:", type, " jsonData:", jsonData)
    CentauriLogic._CheckWebBrowserPaySuc()
    if jsonData then
        local JsonData = Json.decode(jsonData)
        if JsonData.goToRecharge then
            Module.Store:ShowStoreRechargeMainPanle()
        end
    end
end

CentauriLogic._OnSDKWebBrowserCallback = function (param)
    loginfo("CentauriLogic._OnSDKWebBrowserCallback param:", param)
    CentauriLogic._CheckWebBrowserPaySuc()
    if param == "goToRecharge" then
        loginfo("CentauriLogic._OnSDKWebBrowserCallback goToRecharge")
        Module.Store:ShowStoreRechargeMainPanle()
    end
end

CentauriLogic._CheckWebBrowserPaySuc = function ()
    _bCheckWebBrowserPaySuc = true
    Server.CurrencyServer:ReqAllCurrencyData()
end

CentauriLogic._OnCurrencyRefreshDiamondNum = function (oldDiamondNum, newDiamondNum)
    oldDiamondNum = oldDiamondNum or 0
    newDiamondNum = newDiamondNum or 0
    loginfo("CentauriLogic._OnCurrencyRefreshDiamondNum ", oldDiamondNum, newDiamondNum, _bCheckWebBrowserPaySuc)
    if _bCheckWebBrowserPaySuc then
        _bCheckWebBrowserPaySuc = false
        if newDiamondNum > oldDiamondNum then
            Module.Pay:OnPayFinished(0)
        end
    end
end

return CentauriLogic
