----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionMysticalPendantWorkshopPagePanel = ui("CollectionMysticalPendantWorkshopPagePanel")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CommonItemHighlight = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.CommonItemHighlight"
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
local ETipsTriggerReason = import("ETipsTriggerReason")
local EGPInputModeType = import "EGPInputModeType"
local CollectionConfig = Module.Collection.Config

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local ECheckButtonState = import"ECheckButtonState"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
-- END MODIFICATION

function CollectionMysticalPendantWorkshopPagePanel:Ctor()
    self._wtDropDown = UIUtil.WndDropDownBox(self, "wtDropDown", self._OnSortOptionTabIndexChanged)
    self._wtFilterBtn = self:Wnd("wtFilterBtn", DFCommonButtonOnly)
    self._wtFilterBtn:Event("OnClicked",self._OnFilterBtnClick,self)
    self._wtEmptyBtn = self:Wnd("wtEmptyBtn", DFCommonButtonOnly)
    self._wtEmptyBtn:Event("OnClicked",self._OnEmptyBtnClick,self)
    self._wtSourceItemGridBox = UIUtil.WndScrollGridBox(self, "wtSourceItemGridBox", self._OnGetSourceItemsCount, self._OnProcessSourceItemWidget)
    self._wtSourceDragSelectBox = self:Wnd("wtSourceDragSelectBox", CommonItemHighlight)
    self._wtTargetItemGridBox = UIUtil.WndScrollGridBox(self, "wtTargetItemGridBox", self._OnGetTargetItemsCount, self._OnProcessTargetItemWidget)
    self._wtTargetDragSelectBox = self:Wnd("wtTargetDragSelectBox", CommonItemHighlight)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self._wtRecombineDesTxt = self:Wnd("wtRecombineDesTxt", UITextBlock)
    self._wtRecombineDesTxt:SetText(CollectionConfig.Loc.MysticalPendantExchangeDes)
    self._wtRecombineNumTxt = self:Wnd("wtRecombineNumTxt", UITextBlock)
    self._wtRecombineResultHintTxt = self:Wnd("wtRecombineResultHintTxt", UITextBlock)
    self._wtRecombineResultHintTxt:SetText(CollectionConfig.Loc.MatrixExchangeHint)
    self._wtClearTargetListBtn = self:Wnd("wtClearTargetListBtn", DFCommonButtonOnly)
    self._wtClearTargetListBtn:Event("OnClicked", self._OnResetMysticalPendantPool, self)
    self._wtFillTargetListBtn = self:Wnd("wtFillTargetListBtn", DFCommonButtonOnly)
    self._wtFillTargetListBtn:Event("OnClicked", self._OnFillMysticalPendantPool, self)
    self._wtFillTargetListBtn:Event("OnDeClicked", self._OnFillTargetListBtnDeClicked, self)
    self._wtRecombineBtn = self:Wnd("wtRecombineBtn", DFCommonButtonOnly)
    self._wtRecombineBtn:Event("OnClicked", self._OnExchangeMysticalPendants, self)
    self._wtRecombineBtn:Event("OnDeClicked", self._OnExchangeBtnDeClicked, self)
    self._wtRecombineBtn:SetMainTitle(CollectionConfig.Loc.Exchange)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtSourceEmptyHint = self:Wnd("wtSourceEmptyHint", UIWidgetBase)
    self._wtSourceEmptyHint:SetCppValue("Text", CollectionConfig.Loc.NoPendantsToExchange)
    self._wtSourceEmptyHint:SetCppValue("Set_Type", 1)
    self._wtSourceEmptyHint:BP_Set_Type()
    self._wtTargetEmptyHint = self:Wnd("wtTargetEmptyHint", UIWidgetBase)
    self._wtTargetEmptyHint:SetCppValue("Text", CollectionConfig.Loc.MysticalPentantAddToExchangeHint)
    self._wtTargetEmptyHint:SetCppValue("Set_Type", 4)
    self._wtTargetEmptyHint:BP_Set_Type()
    self._wtVectorAnimComp = self:Wnd("wtVectorAnimComp", UIWidgetBase)
    self:SetMode(1)
    self._dropDownIndex = -1
    self._sortTitleList = {}
    self._srotFuncList = {}
    self._sourcePendantItems = {}
    self._targetPendantItems = {}
    self._selectedSourcePos = -1
    self._selectedTargetPos = -1
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    if IsHD() then
        self._wtFillTargetListBtn:Collapsed()
    end
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    self:SetType(0)
end


function CollectionMysticalPendantWorkshopPagePanel:OnInitExtraData()
    self._sortTitleList = {}
    table.insert(self._sortTitleList, CollectionConfig.Loc.QualitySortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.QualitySortDecend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.GainTimeSortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.GainTimeSortDecend)
    self._srotFuncList = {}
    table.insert(self._srotFuncList, CollectionLogic.MysticalPendantQualitySortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalPendantQualitySortDecend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalPendantGainTimeSortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalPendantGainTimeSortDecend)
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionMysticalPendantWorkshopPagePanel:OnOpen()
    self:SetHasTabBar(not IsHD())
end

---@overload fun(LuaUIBaseView, OnClose)
function CollectionMysticalPendantWorkshopPagePanel:OnClose()
    self._selectedSourceCell = nil
    self._selectedTargetCell = nil
    self:RemoveAllLuaEvent()
end

function CollectionMysticalPendantWorkshopPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionMysticalPendantWorkshopPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
    self:_StopDragTimer()
end

---@overload fun(LuaUIBaseView, OnShow)
function CollectionMysticalPendantWorkshopPagePanel:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionMysticalPendantWorkshopPagePanel:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionMysticalPendantWorkshopPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_ProductionMatrix_in then
        if not hasdestroy(self._selectedSourceCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedSourceCell, true)
        elseif not hasdestroy(self._selectedTargetCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedTargetCell, true)
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        self:AddListeners()
    else
        if bFullReset == true then
            self._selectedSourcePos = -1
            self._selectedSourceCell = nil
            self._selectedTargetPos = -1
            self._selectedTargetCell = nil
            self._bShouldIgnoreRefresh = false
            self._bKeepTargetList = false
        end
        self:RemoveAllLuaEvent()
    end
end

function CollectionMysticalPendantWorkshopPagePanel:AddListeners()
	self:AddLuaEvent(CollectionConfig.Events.evtOnAddWorkshopItemToTargetPool, self._AddToTargetList, self)
    self:AddLuaEvent(CollectionConfig.Events.evtOnRemoveWorkshopItemFromTargetPool, self._RemoveFromTargetList, self)
    self:AddLuaEvent(CollectionConfig.Events.evtOnWorkshopFilterUpdated, self._OnWorkshopFilterUpdated, self)
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnPendantEquiped, self)
end


function CollectionMysticalPendantWorkshopPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    if bResetTab then
        self._dropDownIndex = 1
        UIUtil.InitDropDownBox(self._wtDropDown, self._sortTitleList, {}, 0)
    else
        self._dropDownIndex = self._dropDownIndex > 0 and self._dropDownIndex or 1   
    end
    self:_OnRefreshPendantItems(bResetList)
end

function CollectionMysticalPendantWorkshopPagePanel:_OnRefreshPendantItems(bResetList)
    if self._bShouldIgnoreRefresh then
        return
    end
    self._wtDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._sourcePendantItems = {}
    if not self._bKeepTargetList then
        if bResetList then
            self._targetPendantItems = {}
        elseif #self._targetPendantItems > 0 then
            local validTargetPendents = {}
            for index, pendentItem in ipairs(self._targetPendantItems) do
                if Server.CollectionServer:IsOwnedHanging(pendentItem.id, pendentItem.gid) == true then
                    table.insert(validTargetPendents, pendentItem)
                end
            end
            self._targetPendantItems = validTargetPendents
        end
    end
    local filterData = CollectionLogic.GetLastMysticalPendantFilterData()
    local selectedQualityIDs = filterData.selectedQualityIDs
    local seasonChoice = filterData.seasonChoice
    local ownedMysticalPendants = CollectionLogic.GetPendants(CollectionConfig.EItemGroup.Owned, CollectionConfig.EItemType.Mystical, nil, nil, nil, seasonChoice)
    for index, pendantItem in ipairs(ownedMysticalPendants) do
        local bShouldIgnore = false
        for index, targetPendantItem in ipairs(self._targetPendantItems) do
            if pendantItem.id == targetPendantItem.id and pendantItem.gid == targetPendantItem.gid or pendantItem.quality ~= targetPendantItem.quality then
                bShouldIgnore = true
                break
            end
        end
        if bShouldIgnore == false and (table.nums(selectedQualityIDs) == 0 or table.nums(selectedQualityIDs) > 0 and selectedQualityIDs[pendantItem.quality] ~= nil)  then
            table.insert(self._sourcePendantItems, pendantItem)
        end
    end
    self._consumeNum = CollectionConfig.RecombineNum
    self._outputNum = 1
    self._targetSLotNum = CollectionConfig.DefaultWorkshopSlotNum
    if #self._targetPendantItems > 0 then
        local exchangeInfoDataRow = Facade.TableManager:GetRowByKey("MysticalPendantCombineBaseDataTable", tostring(self._targetPendantItems[1].quality))
        if isvalid(exchangeInfoDataRow) then
            if exchangeInfoDataRow.CombineConsumeNum > 0 then
                self._consumeNum = exchangeInfoDataRow.CombineConsumeNum
            end
            if exchangeInfoDataRow.CombineOutputNum > 0 then
                self._outputNum = exchangeInfoDataRow.CombineOutputNum
            end
        end
        self._wtRecombineNumTxt:SetText(tostring(#self._targetPendantItems))
        self._wtRecombineNumTxt:SelfHitTestInvisible()
        self._wtQualityIcon:AsyncSetImagePath(CollectionConfig.QualityIconMapping[self._targetPendantItems[1].quality])
        self._wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(self._targetPendantItems[1].quality))
        self._wtQualityIcon:SelfHitTestInvisible()
        local batchNum = math.floor(#self._targetPendantItems/self._consumeNum)
        local validNum = batchNum*self._consumeNum
        local brickNum = batchNum*self._outputNum
        self._wtRecombineResultHintTxt:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.MatrixExchangeRuleHint,
                {["pendantNum"] = tostring(math.max(validNum, self._consumeNum)),
                ["qualityName"] = CollectionConfig.QualityType2Name[self._targetPendantItems[1].quality],
                ["brickNum"] = tostring(math.max(brickNum, 1))}))
        self._wtRecombineBtn:SetMainTitle(CollectionConfig.Loc.Exchange.." "..tostring(brickNum))
    else
        self._wtRecombineNumTxt:Collapsed()
        self._wtRecombineResultHintTxt:SetText(CollectionConfig.Loc.MatrixExchangeHint)
        self._wtQualityIcon:Collapsed()
        self._wtRecombineBtn:SetMainTitle(CollectionConfig.Loc.Exchange)
    end
    if #self._targetPendantItems > 18 then
        self._targetSLotNum = (math.ceil(#self._targetPendantItems/2)+1)*2
    end
    table.sort(self._sourcePendantItems, self._srotFuncList[self._dropDownIndex])
    if bResetList then
        self._selectedSourcePos = -1
        self._selectedTargetPos = -1
        self._selectedSourceCell = nil
        self._selectedTargetCell = nil
        self._lastSelectedSourcePos = -1
        self._lastSelectedTargetPos = -1
        if IsHD() and WidgetUtil.IsGamepad() and #self._sourcePendantItems > 0 then
            self._lastSelectedSourcePos = 0
        end
        self._wtDropDown:BP_SetMainTabText(self._sortTitleList[self._dropDownIndex] or "")
        self._wtTargetItemGridBox:Visible()
        self._wtSourceItemGridBox:RefreshAllItems()
        self._wtTargetItemGridBox:RefreshAllItems()
    else
        if #self._targetPendantItems == 0 then
            self._selectedTargetPos = -1
            if self._lastSelectedSourcePos < self._firstVisibleSourcePos or self._lastSelectedSourcePos > self._lastVisibleSourcePos then
                self._lastSelectedSourcePos = self._firstVisibleSourcePos
            end
        end
        if #self._sourcePendantItems == 0 then
            self._selectedSourcePos = -1
            self._lastSelectedTargetPos = #self._targetPendantItems-1
        end
        if #self._targetPendantItems == 0 then
            self._selectedTargetPos = -1
        end
        self._wtSourceItemGridBox:RefreshVisibleItems()
        self._wtTargetItemGridBox:RefreshVisibleItems()
    end
    if self._wtVectorAnimComp then
        self._wtVectorAnimComp:SetType(1)
    end
    if #self._sourcePendantItems == 0 then
        self._wtSourceEmptyHint:SelfHitTestInvisible()
    else
        self._wtSourceEmptyHint:Collapsed()
    end
    if #self._targetPendantItems == 0 then
        self._wtTargetEmptyHint:SelfHitTestInvisible()
        self._wtTargetItemGridBox:Hidden()
    else
        self._wtTargetEmptyHint:Collapsed()
        self._wtTargetItemGridBox:Visible()
    end
    self._shortcutList = {}
    if IsHD() and WidgetUtil.IsGamepad() then
        if self._selectedSourcePos > -1 or self._selectedTargetPos > -1 then
            table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = self._OpenDetailPanel, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        if self._selectedSourcePos > -1 then
            table.insert(self._shortcutList, {actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalPendantPool, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
    else
        table.insert(self._shortcutList, {actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalPendantPool, caller = self ,bUIOnly = false, bHideIcon = false}) 
    end
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
    self:UpdateBackground()
    self:_RefreshActionBtns()
    self._bKeepTargetList = false
end


function CollectionMysticalPendantWorkshopPagePanel:OnRefreshModel(curSubStageType)

end


function CollectionMysticalPendantWorkshopPagePanel:_OnGetTabItemCount()
    return #self._sortTitleList
end


function CollectionMysticalPendantWorkshopPagePanel:_OnSortOptionTabIndexChanged(position)
    if self._dropDownIndex ~= position + 1 then
        self._dropDownIndex = position + 1
        self:_OnRefreshPendantItems(true)
        --UIUtil.SetDropDownBoxByIndex(self._wtDropDown, position)
    else
        if IsHD() and WidgetUtil.IsGamepad() then
            self._wtDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnGetSourceItemsCount()
    return #self._sourcePendantItems or 0
end

function CollectionMysticalPendantWorkshopPagePanel:_OnProcessSourceItemWidget(position, itemWidget)
    self._firstVisibleSourcePos = math.min(self._firstVisibleSourcePos or 0, position)
    self._lastVisibleSourcePos = math.max(self._lastVisibleSourcePos or 0, position)
    local item = self._sourcePendantItems[position + 1]
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetCppValue("bHandleClick", true)
    itemWidget:SetButtonEnable(false)
    itemWidget._wtItemIcon:SetCppValue("bIsVolatile", true)
    itemWidget._wtItemIcon:ForceVolatile(true)
    if isvalid(item) then 
        local bLocked = false
        local lockText = ""
        if #self._targetPendantItems > 0 and self._targetPendantItems[1].quality ~= item.quality then
            bLocked = true
            lockText = CollectionConfig.Loc.QualityDifferent
        end
        if item.quality > ItemConfig.EWeaponSkinQualityType.Orange then
            bLocked = true
            lockText = CollectionConfig.Loc.CannotExchange
        end
        itemWidget:BindClickCallback(CreateCallBack(self._OnSourcePendantItemClick, self, itemWidget, position))
        if IsHD() and WidgetUtil.IsGamepad() then
            itemWidget:BindDetailCallback(nil) 
            itemWidget:BindDoubleClickCallback(nil)
            itemWidget:BindDragCallback(nil)
        else
            itemWidget:BindDetailCallback(CreateCallBack(self._OpenDetailPanel, self, item, itemWidget, true)) 
            if bLocked == false then
                itemWidget:BindDoubleClickCallback(CreateCallBack(self._AddToTargetList, self, item))
                itemWidget:BindDragCallback(CreateCallBack(self._OnDragItem, self, item, true))
            else
                itemWidget:BindDoubleClickCallback(nil)
                itemWidget:BindDragCallback(nil)
            end
        end
        if IsHD() then
            local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local kill_cnter_text = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local mysticalInfo = item:GetRawPropInfo().mystical_pendant_data
            if mysticalInfo then
                local pendantMysticalRow = ItemConfigTool.GetPendantMysticalTable(mysticalInfo.show[1].appearance.id)
                local detailInfos = {}
                if pendantMysticalRow then
                    if pendantMysticalRow.DetailId01 > 0 then
                        table.insert(detailInfos, ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId01))
                    end
                    if pendantMysticalRow.DetailId02 > 0 then
                        table.insert(detailInfos, ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId02))
                    end
                    if pendantMysticalRow.DetailId03 > 0 then
                        table.insert(detailInfos, ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId03))
                    end
                end
                for index, mysticalPendantShowInfo in ipairs(detailInfos) do
                    if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypeMaterial then
                        if Module.ItemDetail.Config.EPendantColor[mysticalPendantShowInfo.Level] then
                            rateText = Module.ItemDetail.Config.EPendantColor[mysticalPendantShowInfo.Level]
                        end
                    end
                    if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypeEffect then
                        if Module.ItemDetail.Config.EPendantEffects[mysticalPendantShowInfo.Level] then
                            wearText = Module.ItemDetail.Config.EPendantEffects[mysticalPendantShowInfo.Level]
                        end
                    end
                    if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypePattern then
                        if Module.ItemDetail.Config.EPendantPlaceholderOne[mysticalPendantShowInfo.Level] then
                            kill_cnter_text = Module.ItemDetail.Config.EPendantPlaceholderOne[mysticalPendantShowInfo.Level]
                        end
                    end
                end
            end
            seasonIndex = 1
            local pendantConfig = CollectionLogic.GetPendantDataRow(item.id)
            if pendantConfig then 
                local seasonIdList = CollectionLogic.GetPendantSeasonList()
                for index, seasonId in ipairs(seasonIdList) do
                    if pendantConfig.SeasonID == seasonId then 
                        seasonIndex = index
                        break
                    end
                end
            end
            local instruction = {
                {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = mysticalInfo.custom_name ~= "" and mysticalInfo.custom_name or item.name}},
                {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = StringUtil.Key2StrFormat(CollectionConfig.Loc.SeasonTip,{["SeasonIndex"] = seasonIndex})}, styleRowId = "C001"},
            }
            if item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.UniqueNum, value = tostring(mysticalInfo.unique_no)}}
                )
            end
            table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.PendantColor, value = rateText}}
            )
            if item.quality >= ItemConfig.EWeaponSkinQualityType.Purple then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.PatternTemplate, value = wearText}}
                )
            end
            if item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Accessory, value = kill_cnter_text}}
                )
                if ItemHelperTool.GetQualityTypeById(item.id) == Module.ItemDetail.Config.MysticalPendantPlusId then
                    table.insert(instruction, 
                        {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.HiddenProperty, value = Module.ItemDetail.Config.Loc.PendantHideFeature}}
                    )
                end
            end
            if bLocked == false then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = "MysticalWorkshop_ClickToAdd"}}}}
                )
            end
            itemWidget:SetGivenInstructionContents(instruction)
        end
        itemWidget:InitCollectionMysticalPendantItem(item, CollectionLogic.CheckIfPendantAppliedOnAnyWeapon(item.id, item.gid), bLocked, lockText)
    end
    if IsHD() and WidgetUtil.IsGamepad() and self._selectedTargetPos == -1 and self._lastSelectedSourcePos == position then
        WidgetUtil.SetUserFocusToWidget(itemWidget, true)
        itemWidget:ShowGivenInstruction()
    elseif self._selectedSourcePos == position then
        self._selectedSourceCell = itemWidget
        itemWidget:SetSelected(item, true)
    else
        itemWidget:SetSelected(nil, false)
    end
    itemWidget:Visible()
end

function CollectionMysticalPendantWorkshopPagePanel:_OnSourcePendantItemClick(itemCell, position)
    self._selectedTargetPos = -1
    if self._selectedSourcePos ~= position then
        if not hasdestroy(self._selectedTargetCell) then
            self._selectedTargetCell:SetSelected(nil, false)
        end
        if not hasdestroy(self._selectedSourceCell) then
            self._selectedSourceCell:SetSelected(nil, false)
        end
        self._selectedSourceCell = itemCell
        self._selectedSourcePos = position
        self._lastSelectedSourcePos = position
        self._selectedSourceCell:SetSelected(nil, true)
        self._bShouldIgnoreItemClick = false
    elseif IsHD() and WidgetUtil.IsGamepad() then
        if self._bShouldIgnoreItemClick then
            self._bShouldIgnoreItemClick = false
            return
        end
        local item = self._sourcePendantItems[position + 1]
        self:_AddToTargetList(item)
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnGetTargetItemsCount()
    return self._targetSLotNum or 0
end

function CollectionMysticalPendantWorkshopPagePanel:_OnProcessTargetItemWidget(position, itemWidget)
    self._firstVisibleTargetPos = math.min(self._firstVisibleTargetPos or 0, position)
    self._lastVisibleTargetPos = math.max(self._lastVisibleTargetPos or 0, position)
    local item = self._targetPendantItems[position + 1]
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad() and isvalid(item))
    itemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetButtonEnable(false)
    itemWidget:BindClickCallback(CreateCallBack(self._OnTargetPendantItemClick, self, itemWidget, position))
    itemWidget:BindDetailCallback(nil)
    itemWidget:BindDoubleClickCallback(nil)
    itemWidget:BindDragCallback(nil)
    if isvalid(item) then
        if WidgetUtil.IsGamepad() then
        else
            itemWidget:BindDetailCallback(CreateCallBack(self._OpenDetailPanel, self, item, itemWidget, false))
            itemWidget:BindDoubleClickCallback(CreateCallBack(self._RemoveFromTargetList, self, item))
            itemWidget:BindDragCallback(CreateCallBack(self._OnDragItem, self, item, false))
        end
        if IsHD() then
            local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local kill_cnter_text = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local mysticalInfo = item:GetRawPropInfo().mystical_pendant_data
            if mysticalInfo then
                local pendantMysticalRow = ItemConfigTool.GetPendantMysticalTable(mysticalInfo.show[1].appearance.id)
                local detailInfos = {}
                if pendantMysticalRow then
                    if pendantMysticalRow.DetailId01 > 0 then
                        table.insert(detailInfos, ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId01))
                    end
                    if pendantMysticalRow.DetailId02 > 0 then
                        table.insert(detailInfos, ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId02))
                    end
                    if pendantMysticalRow.DetailId03 > 0 then
                        table.insert(detailInfos, ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId03))
                    end
                end
                for index, mysticalPendantShowInfo in ipairs(detailInfos) do
                    if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypeMaterial then
                        if Module.ItemDetail.Config.EPendantColor[mysticalPendantShowInfo.Level] then
                            rateText = Module.ItemDetail.Config.EPendantColor[mysticalPendantShowInfo.Level]
                        end
                    end
                    if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypeEffect then
                        if Module.ItemDetail.Config.EPendantEffects[mysticalPendantShowInfo.Level] then
                            wearText = Module.ItemDetail.Config.EPendantEffects[mysticalPendantShowInfo.Level]
                        end
                    end
                    if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypePattern then
                        if Module.ItemDetail.Config.EPendantPlaceholderOne[mysticalPendantShowInfo.Level] then
                            kill_cnter_text = Module.ItemDetail.Config.EPendantPlaceholderOne[mysticalPendantShowInfo.Level]
                        end
                    end
                end
            end
            seasonIndex = 1
            local pendantConfig = CollectionLogic.GetPendantDataRow(item.id)
            if pendantConfig then 
                local seasonIdList = CollectionLogic.GetPendantSeasonList()
                for index, seasonId in ipairs(seasonIdList) do
                    if pendantConfig.SeasonID == seasonId then 
                        seasonIndex = index
                        break
                    end
                end
            end
            local instruction = {
                {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = mysticalInfo.custom_name ~= "" and mysticalInfo.custom_name or item.name}},
                {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = StringUtil.Key2StrFormat(CollectionConfig.Loc.SeasonTip,{["SeasonIndex"] = seasonIndex})}, styleRowId = "C001"},
            }
            if item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.UniqueNum, value = tostring(mysticalInfo.unique_no)}}
                )
            end
            table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.PendantColor, value = rateText}}
            )
            if item.quality >= ItemConfig.EWeaponSkinQualityType.Purple then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.PatternTemplate, value = wearText}}
                )
            end
            if item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Accessory, value = kill_cnter_text}}
                )
                if ItemHelperTool.GetQualityTypeById(item.id) == Module.ItemDetail.Config.MysticalPendantPlusId then
                    table.insert(instruction, 
                        {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.HiddenProperty, value = Module.ItemDetail.Config.Loc.PendantHideFeature}}
                    )
                end
            end
            table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = "MysticalWorkshop_ClickToRemove"}}}}
            )
            itemWidget:SetGivenInstructionContents(instruction)
        end
        itemWidget:InitCollectionMysticalPendantItem(item, item and CollectionLogic.CheckIfPendantAppliedOnAnyWeapon(item.id, item.gid) or nil)
    else
        itemWidget:InitCollectionMysticalPendantItem()
    end
    if IsHD() and WidgetUtil.IsGamepad() and self._selectedSourcePos == -1 and self._lastSelectedTargetPos == position then
        WidgetUtil.SetUserFocusToWidget(itemWidget, true)
        itemWidget:ShowGivenInstruction()
    elseif self._selectedTargetPos == position then
        itemWidget:SetSelected(item, true)
        self._selectedTargetCell = itemWidget
    else
        itemWidget:SetSelected(nil, false)
    end
    itemWidget:Visible()
end

function CollectionMysticalPendantWorkshopPagePanel:_OnTargetPendantItemClick(itemCell, position)
    local item = self._targetPendantItems[position + 1]
    if isvalid(item) then
        self._selectedSourcePos = -1
        if self._selectedTargetPos ~= position then
            if not hasdestroy(self._selectedSourceCell) then
                self._selectedSourceCell:SetSelected(nil, false)
            end
            if not hasdestroy(self._selectedTargetCell) then
                self._selectedTargetCell:SetSelected(nil, false)
            end
            self._selectedTargetCell = itemCell
            self._selectedTargetPos = position
            self._lastSelectedTargetPos = position
            self._selectedTargetCell:SetSelected(nil, true)
            self._bShouldIgnoreItemClick = false
        elseif IsHD() and WidgetUtil.IsGamepad() then
            if self._bShouldIgnoreItemClick then
                self._bShouldIgnoreItemClick = false
                return
            end
            self._selectedTargetCell:HideGivenInstruction()
            self:_RemoveFromTargetList(item)
        end
    elseif self._selectedSourcePos ~= -1 then
        self:_AddToTargetList(self._sourcePendantItems[self._selectedSourcePos+1])
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_AddToTargetList(sourceItem)
    Module.ItemDetail:CloseItemDetailPanel()
    if sourceItem then
        if sourceItem.quality > ItemConfig.EWeaponSkinQualityType.Orange then
            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.CannotExchangeTip)
            return
        end
        if #self._targetPendantItems > 0 then
            if self._targetPendantItems[1].quality ~= sourceItem.quality then
                return
            end
        end
        local targetIndex = nil
        for index, item in ipairs(self._targetPendantItems) do
            if item.id == sourceItem.id and item.gid == sourceItem.gid then
                targetIndex = index
                break
            end
        end
        if targetIndex == nil then
            table.insert(self._targetPendantItems, sourceItem)
            if self._lastSelectedSourcePos+1 >= #self._sourcePendantItems then
                self._lastSelectedSourcePos = #self._sourcePendantItems -2
                self._bShouldIgnoreItemClick = true
            end
            if not IsHD() or not WidgetUtil.IsGamepad() then
                self._selectedSourcePos = -1
            end
            self:_OnRefreshPendantItems(false)
            self._wtTargetItemGridBox:ScrollToItem(#self._targetPendantItems-1, false, false, 10, 0, true)
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RemoveFromTargetList(targetItem)
    Module.ItemDetail:CloseItemDetailPanel()
    if targetItem then
        local targetIndex = nil
        for index, item in ipairs(self._targetPendantItems) do
            if item.id == targetItem.id and item.gid == targetItem.gid then
                targetIndex = index
                break
            end
        end
        if targetIndex ~= nil then
            table.remove(self._targetPendantItems, targetIndex)
            if self._lastSelectedTargetPos+1 > #self._targetPendantItems then
                self._lastSelectedTargetPos = #self._targetPendantItems  -1
                self._bShouldIgnoreItemClick = true
            end
            if not IsHD() or not WidgetUtil.IsGamepad() then
                self._selectedTargetPos = -1
            end
            self:_OnRefreshPendantItems(false)
            self._wtTargetItemGridBox:ScrollToItem(#self._targetPendantItems-1, false, false, 10, 0, true)
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnDragItem(item, bFromSource)
    if bFromSource == true then
        self._sourceDragItem = item
        self._targetDragItem = nil
        if self._wtVectorAnimComp then
            self._wtVectorAnimComp:SetType(2)
            self._wtVectorAnimComp:PlayAnimation(self._wtVectorAnimComp.WBP_Collections_ProductionMatrix_Vector_Onetimecycle, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
    else
        self._sourceDragItem = nil
        self._targetDragItem = item
    end
    self:_StartDragTimer()
end


function CollectionMysticalPendantWorkshopPagePanel:_OnResetMysticalPendantPool()
    self._targetPendantItems = {}
    self:_OnRefreshPendantItems(false)
end

function CollectionMysticalPendantWorkshopPagePanel:_OnFillMysticalPendantPool()
    if #self._targetPendantItems > 0 and #self._sourcePendantItems > 0 then
        --local pendantItemsForFill = {}
        for index, pendantItem in ipairs(self._sourcePendantItems) do
            if pendantItem.quality == self._targetPendantItems[1].quality and not CollectionLogic.CheckIfPendantAppliedOnAnyWeapon(pendantItem.id, pendantItem.gid) then
                table.insert(self._targetPendantItems, pendantItem)
            end
        end
        --[[
        table.sort(pendantItemsForFill, function (a, b)
            local rarityA = a:GetRawPropInfo().mystical_pendant_data.show[1].rarity
            local rarityB = b:GetRawPropInfo().mystical_pendant_data.show[1].rarity
            if rarityA and rarityB then
                if rarityA ~= rarityB then
                    return rarityA < rarityB
                end
            elseif not rarityA then
                return true
            elseif not rarityB then
                return false
            end
            if a.id ~= b.id then
                return a.id < b.id
            end
        end)
        for index, pendantItem in ipairs(pendantItemsForFill) do
            table.insert(self._targetPendantItems, pendantItem)
        end
        --]]
        self:_OnRefreshPendantItems(false)
    elseif #self._targetPendantItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOnePendantToPool)
    end
end


function CollectionMysticalPendantWorkshopPagePanel:_OnExchangeMysticalPendants()
    if self._targetPendantItems and #self._targetPendantItems > 0 then
        local batchNum = math.floor(#self._targetPendantItems/self._consumeNum)
        local validNum = batchNum*self._consumeNum
        local brickNum = batchNum*self._outputNum
        local exchangeItems = {}
        for index, pendantItem in ipairs(self._targetPendantItems) do
            if index <= validNum then
                table.insert(exchangeItems, pendantItem)
            else
                break
            end
        end
        local fCallbackIns = CreateCallBack(function(self, res)
            if res.result == 0 then
                if self._bKeepTargetList then
                    for i = 1, #exchangeItems, 1 do
                        if #self._targetPendantItems > 0 then
                            table.remove(self._targetPendantItems, 1)
                        end
                    end
                end
                self._bShouldIgnoreRefresh = false
                self:_OnRefreshPendantItems(true)
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.MatrixExchangeSuccessTip)
                local newPendants = {}
                for i, propChange in ipairs(res.data_change) do
                    propChange.prop.gid = setdefault(propChange.prop.gid, 0)
                    if (propChange.change_type == PropChangeType.Add or propChange.change_type == PropChangeType.Modify) and propChange.reason == ePropChangeReason.CollectionCombineAddPendant then
                        local item = ItemHelperTool.CreateItemByPropInfo(propChange.prop, propChange)
                        item.num = propChange.delta_num or propChange.prop.num
                        if item then
                            table.insert(newPendants, item)
                        end
                    end
                end
                if #newPendants > 0 then
                    Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, newPendants, nil, false, false, true)
                end
            end
        end,self)
        Module.CommonTips:ShowConfirmWindow(
            StringUtil.Key2StrFormat(CollectionConfig.Loc.MatrixExchangeConfirmationTip,
                {["pendantNum"] = tostring(validNum),
                ["qualityName"] = CollectionConfig.QualityType2Name[self._targetPendantItems[1].quality],
                ["brickNum"] = tostring(brickNum)}),
            function()
                self._bShouldIgnoreRefresh = true
                self._bKeepTargetList = #self._targetPendantItems > validNum
                CollectionLogic.ExchangeMysticalPendants(exchangeItems, batchNum>1 ,fCallbackIns)
            end,
            function()
            end,
            CollectionConfig.Loc.Cancel,
            CollectionConfig.Loc.Confirm
        )
    else
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOnePendantToPool)
    end
end


function CollectionMysticalPendantWorkshopPagePanel:_OnExchangeBtnDeClicked()
    if #self._targetPendantItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOnePendantToPool)
    elseif #self._targetPendantItems < self._consumeNum then
        Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(CollectionConfig.Loc.AddMysticalPendantToExchangeTip,
                {["pendantNum"] = self._consumeNum}))
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnFillTargetListBtnDeClicked()
    if #self._targetPendantItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOnePendantToPool)
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RefreshActionBtns()
    if #self._targetPendantItems > 0 then
        self._wtClearTargetListBtn:SelfHitTestInvisible()
    else
        self._wtClearTargetListBtn:Collapsed()
    end
    self._wtFillTargetListBtn:SetIsEnabledStyle(#self._targetPendantItems > 0 and #self._sourcePendantItems > 0)
    self._wtRecombineBtn:SetIsEnabledStyle(#self._targetPendantItems >= self._consumeNum)
    self:_AddExchangeInputAction()
end  

function CollectionMysticalPendantWorkshopPagePanel:_OpenDetailPanel(item, refWidget, bAdd)
    if IsHD() and WidgetUtil.IsGamepad() then
        if not item then
            if self._selectedSourcePos > -1 then
                item = self._sourcePendantItems[self._selectedSourcePos+1]
                refWidget = self._selectedSourceCell
                bAdd = true
            elseif self._selectedTargetPos > -1 then
                item = self._targetPendantItems[self._selectedTargetPos+1]
                refWidget = self._selectedTargetCell
                bAdd = false
            end
        end
    end
    if not item then
        return
    end
    local function fOnDetailPanelLoaded(detailIns)
        if detailIns then
            detailIns:SetDetailBtnVisible(true)
            local fCallbackIns = CreateCallBack(function(self, res)
                Module.Collection:ShowHangingDetailPage(item)
            end,self)
            detailIns:SetDetailBtnClickedCallback(fCallbackIns, self)
            self._detailView = detailIns
            if IsHD() and WidgetUtil.IsGamepad() then
                self._bShouldIgnoreItemClick = true
            end
        end
    end
    local function fOnDetailPanelClosed(detailIns)
        if IsHD() and WidgetUtil.IsGamepad() then
            WidgetUtil.SetUserFocusToWidget(refWidget, true)
        end
    end
    local btnTypeList = {}
    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.EquipPendant)
    if item.quality <= ItemConfig.EWeaponSkinQualityType.Orange then
        if bAdd == true then
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.AddWorkshopItemToTargetPool)
        else
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RemoveWorkshopItemFromTargetPool)
        end
    end 
    if item.quality > ItemConfig.EWeaponSkinQualityType.Blue then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Trade)
    end
    Module.ItemDetail:OpenItemDetailPanel(item, refWidget, false, true, btnTypeList, nil, fOnDetailPanelLoaded, nil, nil, fOnDetailPanelClosed)
end

function CollectionMysticalPendantWorkshopPagePanel:_OnShowTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowInstruction()
        else
            self:_OnHideInstruction()
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnShowInstruction()
    local datas = {}
    table.insert(datas, {
        textContent = CollectionConfig.Loc.MatrixExchangeTip,
        styleRowId = "C000"
    })
	self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtTipAnchor)
    if IsHD() and WidgetUtil.IsGamepad() then
        self._bShouldIgnoreItemClick = true
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnHideInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtTipCheckBtn:SetSelectState(false, false)
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_StartDragTimer()
    self:_StopDragTimer()
    self._dragTimer = Timer:NewIns(0.1, 0)
    self._dragTimer:AddListener(self._DragTick, self)
    self._dragTimer:Start()
end

function CollectionMysticalPendantWorkshopPagePanel:_StopDragTimer()
    if isvalid(self._refreshTimer) then
        self._dragTimer:Release()
    end
    self._dragTimer = nil
end

function CollectionMysticalPendantWorkshopPagePanel:_DragTick()
    local previewItem = Module.CommonWidget:GetOrCreateDragItemView()
    if previewItem then
        local absolutePoint = UWidgetLayoutLibrary.GetMousePositionOnPlatform()
        if self._sourceDragItem then
            local bInsideSourceBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtSourceItemGridBox, absolutePoint)
            if bInsideSourceBox == false then
                self._wtTargetDragSelectBox:SelfHitTestInvisible()
                if self._sourceDragItem.quality > ItemConfig.EWeaponSkinQualityType.Orange or #self._targetPendantItems > 0 and self._targetPendantItems[1].quality ~= self._sourceDragItem.quality then
                    self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonInvalid)
                else
                    self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
                end
            else
                self._wtTargetDragSelectBox:Collapsed()
            end
        elseif self._targetDragItem then
            local bInsideTargetBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtTargetItemGridBox, absolutePoint)
            if bInsideTargetBox == false then
                self._wtSourceDragSelectBox:SelfHitTestInvisible()
                self._wtSourceDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
            else
                self._wtSourceDragSelectBox:Collapsed()
            end
        end
    end
end


function CollectionMysticalPendantWorkshopPagePanel:OnHandleMouseButtonUpEvent(mouseEvent)
    local popController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
    local lastPopUI = popController ~= nil and popController:TryGetLastPopUI() or nil
    if not hasdestroy(self) and (lastPopUI == self._detailView or hasdestroy(lastPopUI)) then
        local beginTouchPos = mouseEvent:GetScreenSpacePosition()
        local isUnderDetailView = false
        if not hasdestroy(self._detailView) then
            local detailViewMainPanel = self._detailView:GetMainPanel()
            if not hasdestroy(detailViewMainPanel) then
                local rootGeometry = detailViewMainPanel:GetCachedGeometry()
                isUnderDetailView = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
            end
        end
        if not IsHD() or not WidgetUtil.IsGamepad() then
            if self._sourceDragItem or self._targetDragItem then
                self._wtSourceDragSelectBox:Collapsed()
                self._wtTargetDragSelectBox:Collapsed()
                if self._sourceDragItem then
                    local rootGeometry = self._wtSourceItemGridBox:GetCachedGeometry()
                    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                    if isUnder == false then
                        self:_AddToTargetList(self._sourceDragItem)
                    end
                elseif self._targetDragItem then
                    local rootGeometry = self._wtTargetItemGridBox:GetCachedGeometry()
                    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                    if isUnder == false then
                        self:_RemoveFromTargetList(self._targetDragItem)
                    end
                end
                self._sourceDragItem = nil
                self._targetDragItem = nil
            else
                if isUnderDetailView == false and #self._targetPendantItems == 0 then
                    local rootGeometry = self._wtTargetEmptyHint:GetCachedGeometry()
                    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                    if isUnder then
                        if self._selectedSourcePos ~= -1 and self._selectedSourceCell ~= nil then
                            self:_AddToTargetList(self._sourcePendantItems[self._selectedSourcePos+1])
                        end
                    end
                end
            end
        end
        local sourceRootGeometry = self._wtSourceItemGridBox:GetCachedGeometry()
        local isUnderSourceRootGeometry = USlateBlueprintLibrary.IsUnderLocation(sourceRootGeometry, beginTouchPos)
        local targetRootGeometry = self._wtTargetItemGridBox:GetCachedGeometry()
        local isUnderTargetRootGeometry = USlateBlueprintLibrary.IsUnderLocation(targetRootGeometry, beginTouchPos)
        local targetRootGeometry2 = self._wtTargetEmptyHint:GetCachedGeometry()
        local isUnderTargetRootGeometry2 = USlateBlueprintLibrary.IsUnderLocation(targetRootGeometry2, beginTouchPos)
        if not isUnderSourceRootGeometry and not isUnderTargetRootGeometry and not isUnderTargetRootGeometry2 and not isUnderDetailView then
            if isvalid(self._selectedSourceCell) then
                self._selectedSourceCell:SetSelected(nil, false)
            end
            if isvalid(self._selectedTargetCell) then
                self._selectedTargetCell:SetSelected(nil, false)
            end
            self._selectedSourcePos = -1
            self._selectedTargetPos = -1
            Module.ItemDetail:CloseAllPopUI()
        end
    end
    self:_StopDragTimer()
    if self._wtVectorAnimComp then
        self._wtVectorAnimComp:SetType(1)
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnEmptyBtnClick()
    if self._selectedTargetPos ~= -1 and self._selectedTargetCell ~= nil then
        self:_RemoveFromTargetList(self._targetPendantItems[self._selectedTargetPos+1])
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnWorkshopFilterUpdated()
    self:_OnRefreshPendantItems(true)
end

function CollectionMysticalPendantWorkshopPagePanel:_OnPendantEquiped()
    if self._detailView then
        local item = nil
        local itemWidget = nil
        local btnTypeList = {}   
        if self._selectedSourcePos ~= -1 then
            item = self._sourcePendantItems[self._selectedSourcePos+1]
            itemWidget = self._selectedSourceCell
            if item.quality <= ItemConfig.EWeaponSkinQualityType.Orange then
                table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.AddWorkshopItemToTargetPool)
            end 
        elseif self._selectedTargetPos ~= -1 then
            item = self._targetPendantItems[self._selectedTargetPos+1]
            itemWidget = self._selectedTargetCell
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RemoveWorkshopItemFromTargetPool) 
        end
        if item then
            if CollectionLogic.CheckIfPendantAppliedOnAnyWeapon(item.id, item.gid) then
                table.insert(btnTypeList, 1, Module.ItemDetail.Config.ButtonType.PendantEquiped)
                --bug=140316354 【ALL|WW】【PC】【必现】藏品-典藏工坊的应用挂饰后的文字弹窗有2个一样的
                --Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.SuccessfullyEquipedPendant)
            else
                table.insert(btnTypeList, 1, Module.ItemDetail.Config.ButtonType.EquipPendant)
            end 
            if item.quality > ItemConfig.EWeaponSkinQualityType.Blue then
                table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Trade)
            end
        end   
        self._detailView:UpdateItem(item, itemWidget, false, true, btnTypeList, nil)
        self._detailView:SetDetailBtnVisible(true)
        self:_OnRefreshPendantItems(false)      
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OnFilterBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionMysticalPendantFilterPanel, nil, self, self._targetPendantItems[1])
end  

function CollectionMysticalPendantWorkshopPagePanel:ClosePopup()
    self:_OnHideInstruction()
end

function CollectionMysticalPendantWorkshopPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end

function CollectionMysticalPendantWorkshopPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

--初始化相关
function CollectionMysticalPendantWorkshopPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._wtClearTargetListBtn:SetDisplayInputAction("Collection_Clear_Gamepad", true, nil, true)
    self._wtRecombineBtn:SetDisplayInputAction("Collection_Recombine_Gamepad", true, nil, true)
    if self._wtDropDown then
        if not self._wtSortModeCheckButton then
            self._wtSortModeCheckButton = self._wtDropDown:Wnd("DFCommonCheckButton", UIWidgetBase)
        end
        if self._wtSortModeCheckButton then
            local keyIcon = self._wtSortModeCheckButton:Wnd("wtKeyIcon", HDKeyIconBox)
            if keyIcon then
                keyIcon:SetOnlyDisplayOnGamepad(true)
                keyIcon:InitByDisplayInputActionName("Collection_Sort_Gamepad", true, 0, false)   
            end
            self._wtSortModeCheckButton:Event("OnCheckButtonStateChanged",self._OnSortModeDropDownBoxOpenStateChanged,self)
        end
    end
    if not self._wtDropDownHandle then
        self._wtDropDownHandle = self:AddInputActionBinding("Collection_Sort_Gamepad", EInputEvent.IE_Pressed, self._OpenDropDown,self, EDisplayInputActionPriority.UI_Stack)
    end 
    self._wtFilterBtn:SetDisplayInputAction("Collection_FilterRight_Gamepad", true, nil, true)   
    if not self._wtFilterBtnHandle then
        self._wtFilterBtnHandle = self:AddInputActionBinding("Collection_FilterRight_Gamepad", EInputEvent.IE_Pressed, self._OnFilterBtnClick,self, EDisplayInputActionPriority.UI_Stack)
    end  
    if not self._wtNavGroupScourcePendant then
        self._wtNavGroupScourcePendant = WidgetUtil.RegisterNavigationGroup(self._wtSourceItemGridBox, self, "Hittest")
        if self._wtNavGroupScourcePendant then
            local navStrategy = self._wtNavGroupScourcePendant:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupScourcePendant:SetScrollRecipient(self._wtSourceItemGridBox)
            self._wtNavGroupScourcePendant:AddNavWidgetToArray(self._wtSourceItemGridBox)
            self._wtNavGroupScourcePendant:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            self._wtNavGroupScourcePendant:MarkIsStackControlGroup()
        end
    end
    if not self._wtNavGroupTargetPendant then
        self._wtNavGroupTargetPendant = WidgetUtil.RegisterNavigationGroup(self._wtTargetItemGridBox, self, "Hittest")
        if self._wtNavGroupTargetPendant then
            local navStrategy = self._wtNavGroupTargetPendant:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupTargetPendant:SetScrollRecipient(self._wtTargetItemGridBox)
            self._wtNavGroupTargetPendant:AddNavWidgetToArray(self._wtTargetItemGridBox)
            self._wtNavGroupTargetPendant:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            self._wtNavGroupTargetPendant:MarkIsStackControlGroup()
        end
    end 
    self._wtGainedFromActivityHint = self:Wnd("wtGainedFromActivityHint", UIWidgetBase)
    if not self._wtNavGroupTipCheckBtn then 
        self._wtNavGroupTipCheckBtn = WidgetUtil.RegisterNavigationGroup(self._wtGainedFromActivityHint, self, "Hittest")
        if self._wtNavGroupTipCheckBtn then
            local navStrategy = self._wtNavGroupTipCheckBtn:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupTipCheckBtn:AddNavWidgetToArray(self._wtTipCheckBtn)
        end
    end
    if not self._hNavigationChangedFocus then
        self._hNavigationChangedFocus = self._wtNavGroupScourcePendant.OnNavGroupFocusReceivedEvent:Add(self._SourceGroupOnFocus, self)
    end
    if not self._hNavigationChangedFocus2 then
        self._hNavigationChangedFocus2 = self._wtNavGroupTargetPendant.OnNavGroupFocusReceivedEvent:Add(self._TargetGroupOnFocus, self)
    end
    self:_AddClearTargetPoolInputAction()
    self._bShouldIgnoreItemClick = true
    if not hasdestroy(self._selectedSourceCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedSourceCell, true)
    elseif not hasdestroy(self._selectedTargetCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedTargetCell, true)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
end

function CollectionMysticalPendantWorkshopPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self._bShouldIgnoreItemClick = false
    self:_RemoveClearTargetPoolInputAction()
    self:_RemoveExchangeInputAction()
    self:_RemoveSortModeDropDownNavGroup()
    self:_RemoveDropDownShortcuts()
    if self._wtFilterBtnHandle then
        self:RemoveInputActionBinding(self._wtFilterBtnHandle)
    end
    if self._wtDropDownHandle then
        self:RemoveInputActionBinding(self._wtDropDownHandle)
    end
    if self._wtSortModeCheckButton then
        self._wtSortModeCheckButton:RemoveEvent("OnCheckButtonStateChanged")
    end
    if self._hNavigationChangedFocus then
        self._wtNavGroupScourcePendant.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus)
    end
    if self._hNavigationChangedFocus2 then
        self._wtNavGroupTargetPendant.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus2)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = nil
    self._wtDropDownHandle = nil
    self._wtFilterBtnHandle = nil
    self._wtNavGroupScourcePendant = nil
    self._wtNavGroupTargetPendant = nil
    self._wtNavGroupTipCheckBtn = nil
    self._wtClearTargetListBtnHandle = nil
    self._wtExchangeBtnHandle = nil
    self._hNavigationChangedFocus = nil
    self._hNavigationChangedFocus2 = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionMysticalPendantWorkshopPagePanel:OnInputTypeChanged(inputType)
    Module.ItemDetail:CloseAllPopUI()
    self._bShouldIgnoreItemClick = true
    self._wtSourceItemGridBox:RefreshVisibleItems()
    self._wtTargetItemGridBox:RefreshVisibleItems()
    if #self._sourcePendantItems == 0 and #self._targetPendantItems == 0 then
        self._shortcutList = {}
        CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_SetDefaultGamepadFocus(bPriorityOnSource)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._bShouldIgnoreItemClick = true
    if bPriorityOnSource then
        WidgetUtil.TryFocusDefaultWidgetByGroup(#self._sourcePendantItems > 0 and self._wtNavGroupScourcePendant or self._wtNavGroupTargetPendant)
    else
        WidgetUtil.TryFocusDefaultWidgetByGroup(#self._targetPendantItems > 0 and self._wtNavGroupTargetPendant or self._wtNavGroupScourcePendant)
    end
end

--下拉菜单导航相关
function CollectionMysticalPendantWorkshopPagePanel:_OnDropDownBoxOpenStateChanged(bOpen)
    if not IsHD() then
        return 
    end
    if bOpen then
        self:_RegisterDropDownNavGroup()
    else
        self:_RemoveDropDownNavGroup()
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_SourceGroupOnFocus()
    self._bShouldIgnoreItemClick = true
    self._shortcutList = {}
    if #self._sourcePendantItems > 0 then
        table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = self._OpenDetailPanel, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    table.insert(self._shortcutList, {actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalPendantPool, caller = self ,bUIOnly = false, bHideIcon = false}) 
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
end

function CollectionMysticalPendantWorkshopPagePanel:_TargetGroupOnFocus()
    self._bShouldIgnoreItemClick = true
    self._shortcutList = {}
    if #self._targetPendantItems > 0 then
        table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = self._OpenDetailPanel, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
end

function CollectionMysticalPendantWorkshopPagePanel:_AddClearTargetPoolInputAction()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtClearTargetListBtnHandle then 
        self._wtClearTargetListBtnHandle = self:AddHoldInputActionBinding("Collection_Clear_Gamepad",self._OnResetMysticalPendantPool, self, EDisplayInputActionPriority.UI_Stack)
        self:AddHoldInputActionReleaseBinding(self._wtClearTargetListBtnHandle, self.OnClearTargetListBtnLongPressFinished, self)
        if self._wtClearTargetListBtn then
            local _wtCommonButtonHD = self._wtClearTargetListBtn:Wnd("DFCommonButton", UIWidgetBase)
            if _wtCommonButtonHD then
                self._wtClearTargetListBtnKeyIcon = _wtCommonButtonHD:Wnd("KeyIcon", UIWidgetBase)
                if self._wtClearTargetListBtnKeyIcon then
                    self._wtClearTargetListBtnKeyIcon:BP_ShowHoldProgressBarTips(true)
                end
            end
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RemoveClearTargetPoolInputAction()
    if self._wtClearTargetListBtnHandle then
        self:RemoveHoldInputActionBinding(self._wtClearTargetListBtnHandle)
    end
    self._wtClearTargetListBtnHandle = nil
end


function CollectionMysticalPendantWorkshopPagePanel:_AddExchangeInputAction()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtExchangeBtnHandle then 
        self._wtExchangeBtnHandle = self:AddHoldInputActionBinding("Collection_Recombine_Gamepad",self._OnExchangeMysticalPendants, self, EDisplayInputActionPriority.UI_Stack)
        -- 监听confirm按钮长按结束事件
        self:AddHoldInputActionReleaseBinding(self._wtExchangeBtnHandle, self.OnExchangeBtnLongPressFinished, self)
        if self._wtRecombineBtn then
            local _wtCommonButtonHD = self._wtRecombineBtn:Wnd("DFCommonButton_PCOnly", UIWidgetBase)
            if _wtCommonButtonHD then
                self._wtExchangeBtnKeyIcon = _wtCommonButtonHD:Wnd("KeyIcon", UIWidgetBase)
                if self._wtExchangeBtnKeyIcon then
                    self._wtExchangeBtnKeyIcon:BP_ShowHoldProgressBarTips(true)
                end
            end
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RemoveExchangeInputAction()
    if self._wtExchangeBtnHandle then
        self:RemoveHoldInputActionBinding(self._wtExchangeBtnHandle)
    end
    self._wtExchangeBtnHandle = nil
end

function CollectionMysticalPendantWorkshopPagePanel:_OnSortModeDropDownBoxOpenStateChanged(eCheckButtonState)
    if not IsHD() then
        return
    end
    if eCheckButtonState == ECheckButtonState.UncheckedPressed then
        self:_RegisterSortModeDropDownNavGroup()
        self:_InitDropDownShortcuts()
        self._bShouldIgnoreItemClick = true
    elseif eCheckButtonState == ECheckButtonState.Unchecked then
        self:_RemoveSortModeDropDownNavGroup()
        self:_RemoveDropDownShortcuts()
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RegisterSortModeDropDownNavGroup()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._SortModeDropDownListNavGroup then
        if self._wtDropDown and self._wtDropDown.ScrollGridBox then
            self._SortModeDropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtDropDown.ScrollGridBox, self._wtDropDown, "Hittest")
        end
        if self._SortModeDropDownListNavGroup then   
            self._SortModeDropDownListNavGroup:AddNavWidgetToArray(self._wtDropDown.ScrollGridBox)
            self._SortModeDropDownListNavGroup:SetScrollRecipient(self._wtDropDown.ScrollGridBox)
            self._SortModeDropDownListNavGroup:MarkIsStackControlGroup()
            if self._sortTitleList[self._dropDownIndex] then
                self._wtDropDown.ScrollGridBox:ScrollToItem(self._dropDownIndex - 1, false, false, 10, 0, false)
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._SortModeDropDownListNavGroup)
        end
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RemoveSortModeDropDownNavGroup()
    if not IsHD() then
        return
    end
    WidgetUtil.RemoveNavigationGroup(self._wtDropDown)
    if self._SortModeDropDownListNavGroup then
        self._SortModeDropDownListNavGroup = nil
    end     
end

function CollectionMysticalPendantWorkshopPagePanel:_InitDropDownShortcuts()
    if not self._closeDropDownHandler then
        self._closeDropDownHandler = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_RemoveDropDownShortcuts()
    if self._closeDropDownHandler then
        self:RemoveInputActionBinding(self._closeDropDownHandler)
        self._closeDropDownHandler = nil
    end
end

function CollectionMysticalPendantWorkshopPagePanel:_OpenDropDown()
    self._wtSortModeCheckButton:NavigationClick()
end

function CollectionMysticalPendantWorkshopPagePanel:_CloseDropDown()
    self._wtSortModeCheckButton:NavigationClick()
end

function CollectionMysticalPendantWorkshopPagePanel:OnExchangeBtnLongPressFinished()
    if not IsHD() then
        return 
    end
    if self._wtExchangeBtnKeyIcon then
        self._wtExchangeBtnKeyIcon:BP_UpdateProgressBar(0) 
    end
end

function CollectionMysticalPendantWorkshopPagePanel:OnClearTargetListBtnLongPressFinished()
    if not IsHD() then
        return 
    end
    if self._wtClearTargetListBtnKeyIcon then
        self._wtClearTargetListBtnKeyIcon:BP_UpdateProgressBar(0) 
    end
end
-- END MODIFICATION


return CollectionMysticalPendantWorkshopPagePanel
