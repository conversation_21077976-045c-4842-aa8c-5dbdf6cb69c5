----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFManager)
----- LOG FUNCTION AUTO GENERATE END -----------



local TablesUseLessGCPairs = {
     "weaponpart/partsfunctiondatatable",
     "GameItem",
     "CommonItemAsset",
     "RankAreaConfig",
     "ActivityTaskGoals",
     "LuaAsset/LuaBpAssetConfig",
     "QuestRewards",
     "QuestObjectives",
     "ErrcodeMsg",
     "ActivityTask",
     "LotteryBoxPropConfig",
     "SocialAvatarDataTable",
     "Quest",
     "AuctionRelatedSearch",
     "Audio/UIAudioTable",
}
local TablesUseLessGCPairsMapping = {}
for _, tableName in ipairs(TablesUseLessGCPairs) do
     TablesUseLessGCPairsMapping[string.lower(tableName)] = true
end



local TableWhiteList = {}

function TableWhiteList.IsTableUseLessGCPairs(tableName)
     if tableName == nil then
          return false
     end
     tableName = string.lower(tableName)
     local bInList = TablesUseLessGCPairsMapping[tableName]
     if bInList ~= nil then
          return true
     end
     return false
end

return TableWhiteList