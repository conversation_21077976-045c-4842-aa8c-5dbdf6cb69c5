----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReddotTrie)
----- LOG FUNCTION AUTO GENERATE END -----------



local ObserverBase = require "DFM.Business.Module.ReddotTrieModule.DotUnit.ObserverBase"
local StoreDataTrie = require "DFM.Business.Module.ReddotTrieModule.DotUnit.StoreDotUnit.StoreTrie.StoreDataTrie"
local StoreManifest = require "DFM.Business.Module.ReddotTrieModule.DotUnit.StoreDotUnit.Defination.StoreManifest"

---@class StoreObserver : ObserverBase
local StoreDataObserver = class("StoreDataObserver", ObserverBase)

function StoreDataObserver:Init()
    self.name = "Store"
    self._obType = EReddotTrieObserverType.Store
    StoreManifest.subTries[1].childNodes = Server.StoreServer:GetReddotChildNodes()
    StoreDataTrie:InitTrie()
    self._reddotDataTrie = StoreDataTrie
    self:RegisterSupplyDataNode()
end

-- 补充通过读配置表增加的数据节点, 不可依赖业务模块,可使用Server方法
function StoreDataObserver:RegisterSupplyDataNode()
end

return StoreDataObserver
