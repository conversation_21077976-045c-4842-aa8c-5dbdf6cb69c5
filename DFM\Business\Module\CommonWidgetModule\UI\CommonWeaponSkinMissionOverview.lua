----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CommonWeaponSkinMissionOverview : LuaUIBaseView
local CommonWeaponSkinMissionOverview = ui("CommonWeaponSkinMissionOverview")
local CommonWidgetLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonWidgetLogic"
local CommonWidgetConfig = Module.CommonWidget.Config

function CommonWeaponSkinMissionOverview:Ctor() 
    self._wtCommonPopWin = self:Wnd("wtCommonPopWin", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked,self)
    self._wtCommonPopWin:BindCloseCallBack(fCallbackIns)
    self._wtCommonPopWin:SetBackgroudClickable(true)
    self._wtMissionDetailTitleText = self:Wnd("wtMissionDetailTitleText", UITextBlock)
    self._wtSOLSkinUnlockPreConditionIcon = self:Wnd("wtSOLSkinUnlockPreConditionIcon", UIImage)
    self._wtSOLSkinUnlockConditionIcon = self:Wnd("wtSOLSkinUnlockConditionIcon", UIImage)
    self._wtMPSkinUnlockPreConditionIcon = self:Wnd("wtMPSkinUnlockPreConditionIcon", UIImage)
    self._wtMPSkinUnlockConditionIcon = self:Wnd("wtMPSkinUnlockConditionIcon", UIImage)
    self._wtSOLSkinUnlockPreConditionBox = self:Wnd("wtSOLSkinUnlockPreConditionBox", UIWidgetBase) 
    self._wtSOLSkinUnlockConditionBox = self:Wnd("wtSOLSkinUnlockConditionBox", UIWidgetBase) 
    self._wtMPSkinUnlockPreConditionBox = self:Wnd("wtMPSkinUnlockPreConditionBox", UIWidgetBase) 
    self._wtMPSkinUnlockConditionBox = self:Wnd("wtMPSkinUnlockConditionBox", UIWidgetBase) 
end

function CommonWeaponSkinMissionOverview:OnInitExtraData(skinTaskInfo)
    self._skinTaskInfo = skinTaskInfo
end

function CommonWeaponSkinMissionOverview:OnOpen()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtSOLSkinUnlockPreConditionBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtSOLSkinUnlockConditionBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtMPSkinUnlockPreConditionBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtMPSkinUnlockConditionBox)
end

function CommonWeaponSkinMissionOverview:OnShow()
    self._wtMissionDetailTitleText:SetText(CommonWidgetConfig.Loc.WeaponSkinLocked)
    self._wtSOLSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_De_21.Collections_De_21'")
    self._wtSOLSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
    self._wtMPSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_De_21.Collections_De_21'")
    self._wtMPSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
    if self._skinTaskInfo then
        local bFinished = self._skinTaskInfo.MP_TaskInfo.bFinished == true or self._skinTaskInfo.SOL_TaskInfo.bFinished == true
        self._wtMissionDetailTitleText:SetText(bFinished == true and CommonWidgetConfig.Loc.WeaponSkinUnlocked or CommonWidgetConfig.Loc.WeaponSkinLocked)
        local SOLTaskInfo = self._skinTaskInfo.SOL_TaskInfo
        local MPTaskInfo = self._skinTaskInfo.MP_TaskInfo
        if SOLTaskInfo ~= nil then
            if SOLTaskInfo.bFinished then
                self._wtSOLSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'")
                self._wtSOLSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'")
            elseif MPTaskInfo.bFinished then
                self._wtSOLSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
                self._wtSOLSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
            elseif SOLTaskInfo.bUnlocked then
                self._wtSOLSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'")
                self._wtSOLSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_De_21.Collections_De_21'")
            else
                self._wtSOLSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_De_21.Collections_De_21'")
                self._wtSOLSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
            end
            for index, taskInfo in ipairs(SOLTaskInfo.pre_Tasks) do
                local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonWeaponSkinMissionProgressItem, self._wtSOLSkinUnlockPreConditionBox, nil, taskInfo, false)
                local widget = getfromweak(weakUIIns)
                if widget then
                    if MPTaskInfo.bFinished then
                        widget:SetStyle(2) 
                    else
                        if taskInfo.curValue == taskInfo.maxValue then
                            widget:SetStyle(1)
                        else
                            widget:SetStyle(0)
                        end
                    end
                end
            end
            for index, taskInfo in ipairs(SOLTaskInfo.tasks) do
                local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonWeaponSkinMissionProgressItem, self._wtSOLSkinUnlockConditionBox, nil, taskInfo, false)
                local widget = getfromweak(weakUIIns)
                if widget then
                    if MPTaskInfo.bFinished or not SOLTaskInfo.bUnlocked then
                        widget:SetStyle(2) 
                    else
                        if taskInfo.curValue == taskInfo.maxValue then
                            widget:SetStyle(1)
                        else
                            widget:SetStyle(0)
                        end
                    end
                end
            end
        end
        if MPTaskInfo ~= nil then
            if MPTaskInfo.bFinished then
                self._wtMPSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'")
                self._wtMPSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'")
            elseif SOLTaskInfo.bFinished then
                self._wtMPSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
                self._wtMPSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
            elseif MPTaskInfo.bUnlocked then
                self._wtMPSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'")
                self._wtMPSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_De_21.Collections_De_21'")
            else
                self._wtMPSkinUnlockPreConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/System/CollectionsWarehouse/BakedSprite/Collections_De_21.Collections_De_21'")
                self._wtMPSkinUnlockConditionIcon:AsyncSetImagePath("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Lock.Common_De_Lock'")
            end
            for index, taskInfo in ipairs(MPTaskInfo.pre_Tasks) do
                local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonWeaponSkinMissionProgressItem, self._wtMPSkinUnlockPreConditionBox, nil, taskInfo, false)
                local widget = getfromweak(weakUIIns)
                if widget then
                    if SOLTaskInfo.bFinished then
                        widget:SetStyle(2) 
                    else
                        if taskInfo.curValue == taskInfo.maxValue then
                            widget:SetStyle(1)
                        else
                            widget:SetStyle(0)
                        end
                    end
                end
            end
            for index, taskInfo in ipairs(MPTaskInfo.tasks) do
                local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonWeaponSkinMissionProgressItem, self._wtMPSkinUnlockConditionBox, nil, taskInfo, false)
                local widget = getfromweak(weakUIIns)
                if widget then
                    if SOLTaskInfo.bFinished or not MPTaskInfo.bUnlocked then
                        widget:SetStyle(2) 
                    else
                        if taskInfo.curValue == taskInfo.maxValue then
                            widget:SetStyle(1)
                        else
                            widget:SetStyle(0)
                        end
                    end
                end
            end
        end
    end
end

function CommonWeaponSkinMissionOverview:OnClose()
    Facade.UIManager:ClearSubUIByParent(self, self._wtSOLSkinUnlockPreConditionBox)
    Facade.UIManager:ClearSubUIByParent(self, self._wtSOLSkinUnlockConditionBox)
    Facade.UIManager:ClearSubUIByParent(self, self._wtMPSkinUnlockPreConditionBox)
    Facade.UIManager:ClearSubUIByParent(self, self._wtMPSkinUnlockConditionBox)
end

function CommonWeaponSkinMissionOverview:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end


function CommonWeaponSkinMissionOverview:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end


return CommonWeaponSkinMissionOverview
