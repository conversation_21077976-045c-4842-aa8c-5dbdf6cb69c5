----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionsWeaponPagePanel = ui("CollectionsWeaponPagePanel")
ECheckButtonState = import"ECheckButtonState"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionConfig = Module.Collection.Config
local UGunPresetTableManager = import "GunPresetTableManager"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

function CollectionsWeaponPagePanel:Ctor()
    if not IsHD() then
        self._wtSortDropDown = UIUtil.WndDropDownBox(self, "wtSortDropDown", self._OnCheckedTabIndexChanged)    
    end
    self._wtWeaponListScrollBox = UIUtil.WndWaterfallScrollBox(self, "wtWeaponListScrollBox", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtBoxArea = self:Wnd("wtBoxArea", UIWidgetBase)
    self._wtAppearanceBtn = self:Wnd("wtAppearanceBtn", DFCommonButtonOnly)
    self._wtAppearanceBtn:Event("OnClicked", self._ShowSkinListPage, self)
    self._wtEmptyHint = self:Wnd("wtEmptyHint", UIWidgetBase)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._selectedPos = -1
    self._selectedSeriesPos = -1
    self._selectedCell = nil
    self._weaponSkinItems = {}
    self._weaponSeriesList = {}
    self._weaponNum = 0
    self._dropDownDataList = {}
    self._animType = 1
    self._mainTabIndex = -1
    self._subTabIndex = -1
    self._redDotInsMap = setmetatable({}, weakmeta_key)
end


function CollectionsWeaponPagePanel:OnInitExtraData(mainTabIndex)
    self._mainTabIndex = mainTabIndex or self._mainTabIndex
    self._subTabIndex = 1
    if not IsHD() then
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        if mainTabInfo.subTabList then
            self._dropDownDataList = {}
            if mainTabInfo.subTabList then        
                self._subTabList = mainTabInfo.subTabList
                for key, subTabInfo in pairs(self._subTabList) do
                    table.insert(self._dropDownDataList, subTabInfo.subTabName)
                end
            end
        end
        self._wtSortDropDown:SelfHitTestInvisible()
        self._wtSortDropDown:BP_SetMainTabText("")
    end 
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionsWeaponPagePanel:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CollectionsWeaponPagePanel:OnClose()
    table.empty(self._redDotInsMap)
    Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
end


function CollectionsWeaponPagePanel:OnShowBegin()
    self:AddListeners()
    self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtWeaponListScrollBox, self)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionsWeaponPagePanel:OnHideBegin()
    self:RemoveAllLuaEvent()
    if self._scrollStopHandle then
		UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
		self._scrollStopHandle = nil
	end
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CollectionsWeaponPagePanel:OnShow()
    if self._bJumpToWeaponSkinPage == true then
        self._wtWeaponListScrollBox:RefreshVisibleItems()
        self._bJumpToWeaponSkinPage = false
    end
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionsWeaponPagePanel:OnHide()
end

function CollectionsWeaponPagePanel:AddListeners()
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnAppliedWeaponSkin, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self.OnRefreshModel, self)
end


-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionsWeaponPagePanel:OnAnimFinished(anim)
end

function CollectionsWeaponPagePanel:RefreshView(mainTabIndex, bResetTab)
    self._selectedPos = Module.Collection.Field:GetSelectedPos()
    if self._selectedPos == -1 then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin)
        self._subTabIndex = 1
        if not IsHD() then
            UIUtil.InitDropDownBox(self._wtSortDropDown, self._dropDownDataList, {}, 0)
            self._wtSortDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
            self._wtSortDropDown:BP_SetMainTabText(self._dropDownDataList[self._subTabIndex])
        end
    end 
    self:_OnRefreshWeaponItems()
end


function CollectionsWeaponPagePanel:_OnGetItemCount()
    return self._weaponNum
end


function CollectionsWeaponPagePanel:_OnProcessItemWidget(position, itemWidget)
    local targetIndex = 0
    for seriesIndex, weaponSeriesData in ipairs(self._weaponSeriesList) do
        for weaponIndex, baseWeaponId in ipairs(weaponSeriesData.weapons) do
            targetIndex = targetIndex + 1
            if targetIndex == position then
                local fClickCb = CreateCallBack(self._OnWeaponListItemClick, self, itemWidget, weaponIndex-1, seriesIndex-1)
                itemWidget:BindClickCallback(fClickCb)
                local fCheckFunc = CreateCallBack(function(self, curUpdateReddotObType)
                    local collectionRedDots = CollectionLogic.GetPropRedDots()
                    local weaponSkins = CollectionLogic.GetWeaponSkins(CollectionConfig.EItemGroup.Owned, CollectionConfig.EItemType.Any, baseWeaponId)
                    for index, skinItem in ipairs(weaponSkins) do
                        if not table.isempty(collectionRedDots[skinItem.id]) and collectionRedDots[skinItem.id][skinItem.gid] == true then
                            loginfo('CollectionsWeaponPagePanel:_OnProcessItemWidget(position, itemWidget) name = ', skinItem.name, 'baseWeaponId = ' , baseWeaponId,'true')
                            return true
                        end
                    end
                    loginfo('CollectionsWeaponPagePanel:_OnProcessItemWidget(position, itemWidget)', 'baseWeaponId = ' , baseWeaponId,'false')
                    return false
                end,self)

                if isvalid(self._redDotInsMap[itemWidget]) then
                    Module.ReddotTrie:UpdateDynamicReddot(itemWidget, EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, self._redDotInsMap[itemWidget], fCheckFunc, nil)
                else
                    self._redDotInsMap[itemWidget] = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Collection, ECollectionDynamicDataType.WeaponSkin, fCheckFunc, nil ,itemWidget, {EReddotType.Normal})
                end
                if self._selectedPos == -1 or (self._selectedPos == weaponIndex-1 and self._selectedSeriesPos == seriesIndex-1) then
                    self:_OnWeaponListItemClick(itemWidget, weaponIndex-1, seriesIndex-1)
                else
                    itemWidget:SetSelected(false)
                end
                itemWidget:SetSize(548, 254)
                itemWidget:SelfHitTestInvisible()
                if weaponIndex == 1 then
                    itemWidget:InitCollectionWeaponItem(baseWeaponId, ItemConfig.MapWeaponItemType2Name[weaponSeriesData.gunTypeId])
                else
                    itemWidget:InitCollectionWeaponItem(baseWeaponId)
                end
                return
            end
        end
    end
end

function CollectionsWeaponPagePanel:OnTertiaryTabIndexChanged(TabIndex, LastTabIndex)
    self._subTabIndex = TabIndex
    self._selectedCell = nil
    self:_OnRefreshWeaponItems(true)
end

function CollectionsWeaponPagePanel:_OnCheckedTabIndexChanged(position, lastPosition)
    self._subTabIndex = position + 1
    self._wtSortDropDown:BP_SetMainTabText(self._dropDownDataList[self._subTabIndex])
    self._selectedCell = nil
    self:_OnRefreshWeaponItems(true)
end

function CollectionsWeaponPagePanel:_OnRefreshWeaponItems()
    self._wtAppearanceBtn:Collapsed()
    self._wtEmptyHint:Collapsed()
    self:OnRefreshModel(ESubStage.HallCollectionNew)
    self:UpdateBackground()
    self:RegStackUIInputSummary()
    if self._selectedPos == -1 then
        local tabInfo = Module.Collection.Field:GetTabInfo()
        local mainTabInfo = tabInfo[self._mainTabIndex]
        self._weaponSeriesList = CollectionLogic.GetWeaponSeriesList(mainTabInfo.subTabList[self._subTabIndex].subTabId)
        self._weaponNum = 0
        for index, weaponSeriesData in ipairs(self._weaponSeriesList) do
            self._weaponNum = self._weaponNum + #weaponSeriesData.weapons
        end
        self._wtWeaponListScrollBox:RefreshAllItems()
    else
        self._wtWeaponListScrollBox:RefreshVisibleItems()
    end
    if #self._weaponSeriesList == 0 then
        self._wtEmptyHint:SelfHitTestInvisible()
    end
end


function CollectionsWeaponPagePanel:OnRefreshModel(curSubStageType)
    if not curSubStageType or curSubStageType == ESubStage.HallCollectionNew then
        -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
        -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayType", "Weapon")
        if self._weaponSeriesList and #self._weaponSeriesList > 0 and self._selectedSeriesPos > -1 and self._selectedPos > -1 then
            local weaponDesc, partIndexs
            local weaponSkinApplied = nil
            local weaponSkins = CollectionLogic.GetWeaponSkins(CollectionConfig.EItemGroup.Any, CollectionConfig.EItemType.Any, self._weaponSeriesList[self._selectedSeriesPos+1].weapons[self._selectedPos+1])
            for index, weaponSkin in ipairs(weaponSkins) do
                if CollectionLogic.CheckIfSkinAppliedOnWeapon(weaponSkin.id, weaponSkin.gid) then
                    weaponSkinApplied = weaponSkin
                    break
                end
            end
            if weaponSkinApplied ~= nil then
                weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(weaponSkinApplied.id)
                if isvalid(weaponDesc) then
                    if weaponSkinApplied.gid > 0 then
                        WeaponAssemblyTool.SetWeaponDescriptionSkinInfoFromPropInfo(weaponDesc, weaponSkinApplied:GetRawPropInfo())
                    end
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeapon", weaponDesc, weaponSkinApplied.id, false, false)
                end
            else
                local baseWeaponId = self._weaponSeriesList[self._selectedSeriesPos+1].weapons[self._selectedPos+1]
                partIndexs = {}
                local weaponItem = Module.Collection.Field:GetWeaponItem(baseWeaponId)
                if weaponItem == nil then
                    weaponItem = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(baseWeaponId)
                    Module.Collection.Field:SetWeaponItem(baseWeaponId, weaponItem)
                end
                local presetConfig = UGunPresetTableManager.Get():GetGunByItemId(weaponItem.id)
                if presetConfig ~= nil then
                    weaponDesc, partIndexs = UAssembleWeaponDataLibrary.GetWeaponDescAndPartIndexsFromPreset(presetConfig, partIndexs)
                end
                if isvalid(weaponDesc) then
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeapon", weaponDesc, baseWeaponId, false, true)
                end 
            end
        end
    end
end


function CollectionsWeaponPagePanel:_OnWeaponListItemClick(itemWidget, pos, seriesPos)
    if isvalid(self._selectedCell) then
        self._selectedCell:SetSelected(false)
    end
    self._selectedPos = pos
    self._selectedSeriesPos = seriesPos
    Module.Collection.Field:SetSelectedPos(pos)
    self._selectedCell = itemWidget
    self._selectedCell:SetSelected(true)
    self:OnRefreshModel(ESubStage.HallCollectionNew)
    self:_RefreshItemUI()
end



function CollectionsWeaponPagePanel:_RefreshItemUI()
    self._wtAppearanceBtn:SetIsEnabled(true)
    self._wtAppearanceBtn:SetMainTitle(CollectionConfig.Loc.Appearance)
    self._wtAppearanceBtn:SelfHitTestInvisible()
end

function CollectionsWeaponPagePanel:_ShowSkinListPage()
    if self._selectedPos > -1 and self._selectedSeriesPos > -1 then
        local baseWeaponId = self._weaponSeriesList[self._selectedSeriesPos+1].weapons[self._selectedPos+1]
        Facade.UIManager:AsyncShowUI(UIName2ID.CollectionsWeaponSkinPage, nil, nil, baseWeaponId)
        LogAnalysisTool.AddRecIDs(baseWeaponId)
        self._bJumpToWeaponSkinPage = true
    end
end

function CollectionsWeaponPagePanel:ClosePopup()

end

function CollectionsWeaponPagePanel:_OnAppliedWeaponSkin()
    self._wtWeaponListScrollBox:RefreshVisibleItems()
end

function CollectionsWeaponPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end


function CollectionsWeaponPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

function CollectionsWeaponPagePanel:BindRegStackUIInputSummaryCallback(callback, caller)
    self._regStackUIInputSummaryCallback = SafeCallBack(callback, caller)
end

function CollectionsWeaponPagePanel:RegStackUIInputSummary()
    if self._regStackUIInputSummaryCallback then
        self._regStackUIInputSummaryCallback()
    end
end

return CollectionsWeaponPagePanel
