local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local LazyObject = require "DFM.YxFramework.Core.Class.LazyObject"
---------------------------------------------------------------------------------
--- 此处放置Server、Module都可能用得到的道具类型判断方法
---------------------------------------------------------------------------------
local ItemHelperTool = {}
ItemHelperTool.CacheExchangeList = {}

--- slotType
ItemHelperTool.IsPresetSlotType = function(targetSlotType)
    return ItemHelperTool.IsEquipmentSlotType(targetSlotType) or ItemHelperTool.IsContainerSlotType(targetSlotType)
end

ItemHelperTool.IsEquipmentSlotType = function(targetSlotType)
    return targetSlotType > ESlotType.EquipmentStart and targetSlotType < ESlotType.EquipmentEnd
end

ItemHelperTool.IsContainerSlotType = function(targetSlotType)
    return targetSlotType > ESlotType.ContainerStart and targetSlotType < ESlotType.ContainerEnd
end

ItemHelperTool.IsBagSlotType = function(targetSlotType)
    return targetSlotType == ESlotType.ChestHanging or targetSlotType == ESlotType.Bag or targetSlotType == ESlotType.SafeBox
end

ItemHelperTool.IsBuyToSlotType = function(targetSlotType)
    return targetSlotType and (targetSlotType < ESlotType.DepositoryStart or targetSlotType > ESlotType.DepositoryEnd)
end

ItemHelperTool.IsMainWeaponSlotType = function(targetSlotType)
    return targetSlotType >= ESlotType.MainWeaponLeft and targetSlotType <= ESlotType.MainWeaponRight or
    targetSlotType >= ESlotType.PVEMainWeaponLeft and targetSlotType <= ESlotType.PVEMainWeaponRight or 
    targetSlotType >= ESlotType.MP_MainWeapon and targetSlotType <= ESlotType.MP_SecondaryWeapon
end

ItemHelperTool.IsSOLWeaponSlotType = function(targetSlotType)
    return targetSlotType == ESlotType.MainWeaponLeft or targetSlotType == ESlotType.MainWeaponRight or targetSlotType == ESlotType.Pistrol
end

ItemHelperTool.IsStackSlotType = function(targetSlotType)
    return targetSlotType >= ESlotType.Medicine and targetSlotType <= ESlotType.Medicine
end

ItemHelperTool.IsMPEquipmentSlotType = function(targetSlotType)
    return targetSlotType > ESlotType.MP_Begin and targetSlotType < ESlotType.MP_End
end


---------------------------------------------------------------------------------
--- 是否是兵种道具槽位（包含SOL+MP 的 1+2 槽位）
---------------------------------------------------------------------------------
ItemHelperTool.IsArmedForceUniqueSlotType = function(targetSlotType)
    return (targetSlotType >= ESlotType.MP_ArmedForceProp1 and targetSlotType <= ESlotType.MP_ArmedForceProp2)
    or (targetSlotType >= ESlotType.ArmedForceProp1 and targetSlotType <= ESlotType.ArmedForceProp2 and targetSlotType ~= ESlotType.KeyChain)
end

ItemHelperTool.IsArmedForceWithTDMSlotType = function(targetSlotType)
    return (targetSlotType >= ESlotType.MP_ArmedForceProp1 and targetSlotType <= ESlotType.MP_ArmedForceProp2)
    or (targetSlotType >= ESlotType.ArmedForceProp1 and targetSlotType <= ESlotType.ArmedForceProp2 and targetSlotType ~= ESlotType.KeyChain)
    or (targetSlotType == ESlotType.MP_ArmedForceTDMProp)
end

---------------------------------------------------------------------------------
--- 是否是兵种道具1的槽位（包含SOL+MP）
---------------------------------------------------------------------------------
ItemHelperTool.IsArmedForceProp1SlotType = function(targetSlotType)
    return (targetSlotType == ESlotType.ArmedForceProp1) or (targetSlotType == ESlotType.MP_ArmedForceProp1) or (targetSlotType == ESlotType.MP_ArmedForceTDMProp)
end

---------------------------------------------------------------------------------
--- 是否是兵种道具2的槽位（包含SOL+MP）
---------------------------------------------------------------------------------
ItemHelperTool.IsArmedForceProp2SlotType = function(targetSlotType)
    return (targetSlotType == ESlotType.ArmedForceProp2) or (targetSlotType == ESlotType.MP_ArmedForceProp2) or (targetSlotType == ESlotType.MP_ArmedForceTDMProp)
end

---------------------------------------------------------------------------------
--- 是否是SOL的兵种道具槽位
---------------------------------------------------------------------------------
ItemHelperTool.IsSOLArmedForceSlotType = function(targetSlotType)
    return (targetSlotType >= ESlotType.ArmedForceProp1 and targetSlotType <= ESlotType.ArmedForceProp2) and targetSlotType ~= ESlotType.KeyChain
end

---------------------------------------------------------------------------------
--- 是否是MP的兵种道具槽位
---------------------------------------------------------------------------------
ItemHelperTool.IsMPArmedForceSlotType = function(targetSlotType)
    return (targetSlotType >= ESlotType.MP_ArmedForceProp1 and targetSlotType <= ESlotType.MP_ArmedForceProp2)
end

---------------------------------------------------------------------------------
--- 是否是子弹槽位
---------------------------------------------------------------------------------
ItemHelperTool.IsBulletSlotType = function(targetSlotType)
    return (targetSlotType >= ESlotType.BulletLeft and targetSlotType <= ESlotType.BulletRight)
end

---------------------------------------------------------------------------------
--- 兵种道具分类：EExtraGameItemType
--- 101可以放在SOL和MP的1槽位 ESlotType.ArmedForceProp1 ESlotType.MP_ArmedForceProp1
--- 102可以放在SOL和MP的2槽位 ESlotType.ArmedForceProp2 ESlotType.MP_ArmedForceProp2
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
--- 是否是兵种道具（对应SOL+MP 的 1+2 槽位）
---------------------------------------------------------------------------------
ItemHelperTool.IsArmedForceUniquePropById = function(id)
    local itemData = ItemConfigTool.GetItemConfigById(id)
    assertlog(itemData, 'itemData is nil, please check item id;[', id,']')
    return itemData.GameItemType == EExtraGameItemType.ArmedForceProp1 or itemData.GameItemType == EExtraGameItemType.ArmedForceProp2
end

ItemHelperTool.IsArmedForceUniquePropByItem = function(item)
    return item.gameItemType == EExtraGameItemType.ArmedForceProp1 or item.gameItemType == EExtraGameItemType.ArmedForceProp2
end

---------------------------------------------------------------------------------
--- 是否是兵种道具1（对应SOL+MP 的1槽位）
---------------------------------------------------------------------------------
ItemHelperTool.IsArmedForce1PropById = function(id)
    local itemData = ItemConfigTool.GetItemConfigById(id)
    assertlog(itemData, 'itemData is nil, please check item id;[', id,']')
    return itemData.GameItemType == EExtraGameItemType.ArmedForceProp1
end

ItemHelperTool.IsArmedForce1PropByItem = function(item)
    return item.gameItemType == EExtraGameItemType.ArmedForceProp1
end
---------------------------------------------------------------------------------
--- 是否是兵种道具2（对应SOL+MP 的2槽位）
---------------------------------------------------------------------------------
ItemHelperTool.IsArmedForce2PropById = function(id)
    local itemData = ItemConfigTool.GetItemConfigById(id)
    assertlog(itemData, 'itemData is nil, please check item id;[', id,']')
    return itemData.GameItemType == EExtraGameItemType.ArmedForceProp2
end

ItemHelperTool.IsArmedForce2PropByItem = function(item)
    return item.gameItemType == EExtraGameItemType.ArmedForceProp2
end

--- itemId
ItemHelperTool.GetDepositIdByExtId = function(extId)
    return math.modf(extId / 1000)
end

ItemHelperTool.GetStartExtIdByDepositId = function(depositId)
    return depositId * 1000 + 1
end

--- 判断道具id是否有效
ItemHelperTool.CheckIdIsValid = function(itemId)
    if itemId == nil then
        return false
    end
    if string.len(itemId) == 11 and string.match(itemId, "^%d+$") then
        return true
    else
        return false
    end
end

ItemHelperTool.GetCombinedTypeById = function(itemId)
    if itemId == nil then
        return
    end
    -- 确保 itemId 是一个数字
    itemId = tonumber(itemId)
    if itemId == nil then
        return
    end
    return math.floor(tonumber(itemId) / 10 ^ 7)
end

--- 获取道具类型
ItemHelperTool.GetMainTypeById = function(itemId)
    if itemId == nil then
        return
    end
    -- BEGIN MODIFICATION @ VIRTUOS
    return math.floor(tonumber(itemId) / 10 ^ 9)
    -- END MODIFICATION
end

--- 获取道具二级类型
ItemHelperTool.GetSubTypeById = function(itemId)
    if itemId == nil then
        return
    end
    return math.floor((itemId - ItemHelperTool.GetMainTypeById(itemId) * 10 ^ 9) / 10 ^ 7)
end

--- 获取道具三级类型
ItemHelperTool.GetThirdTypeById = function(itemId)
    if itemId == nil then
        return
    end
    local thirdIdStr = string.sub(tostring(itemId), 5, 6)
    return tonumber(thirdIdStr)
end

--- 获取道具的品质类型
function ItemHelperTool.GetQualityTypeById(itemId)
    if itemId == nil then
        return
    end
    local qualityIdStr = string.sub(tostring(itemId), 7, 7)
    return tonumber(qualityIdStr) or 0
end

--- 获取道具药品及消耗品类型
ItemHelperTool.GetMedicalDurableTypeById = function(itemId)
    if itemId == nil then
        return
    end
    local durableIdStr = string.sub(tostring(itemId), 8, 8)
    return tonumber(durableIdStr)
end

ItemHelperTool.GetHealthInfoById = function(itemId)
    if itemId == nil then
        return
    end
    local ItemHealthTable = Facade.TableManager:GetTable("ItemHealth")
    if type(itemId) == "number" then
        itemId = tostring(itemId)
    end
    return ItemHealthTable[itemId]
end

local MONEY_TYPE_MAX = 10
ItemHelperTool.GetIsExchangeCostItemById = function(itemId)
    if itemId == nil then
        return
    end
    local mallCfg = Facade.TableManager:GetTable("Mall")
    local exchangeShopItems = Server.ShopServer:GetShopItemsForExchange()
    if exchangeShopItems and next(exchangeShopItems) then
        for k, v in ipairs(exchangeShopItems) do
            for i = 1, MONEY_TYPE_MAX do
                local localInfo = mallCfg[v.exchangeId]
                if localInfo and localInfo['CostItem'..i..'ID'] and localInfo['CostItem'..i..'ID'] == itemId then
                    return true
                end
            end
        end
    end
    return false
end

ItemHelperTool.GetCanExchangeIdListById = function(itemId)
    if itemId == nil then
        return
    end
    if ItemHelperTool.CacheExchangeList[itemId] == nil then
        ItemHelperTool.CacheExchangeList = {}

        local canExchangeIdList = {}
        local exchangeShopItems = Server.ShopServer:GetShopItemsForExchange()
        if exchangeShopItems and next(exchangeShopItems) then
            for k, v in ipairs(exchangeShopItems) do
                if v:CheckIsInCostList(itemId) then
                    table.insert(canExchangeIdList, v.exchangeId)
                end
            end
        end
        ItemHelperTool.CacheExchangeList[itemId] = canExchangeIdList
    end
    return ItemHelperTool.CacheExchangeList[itemId]
end

function ItemHelperTool.GetIconByExtItemId_Selected(extItemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(extItemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(extItemId)

    ensure(itemMainType == EItemType.ExtendItem)

    return ItemConfig.MapExtSubType2IconPath_Selected[itemSubType]
end

function ItemHelperTool.GetIconByExtItemId_NonSelected(extItemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(extItemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(extItemId)

    ensure(itemMainType == EItemType.ExtendItem)

    return ItemConfig.MapExtSubType2IconPath_NonSelected[itemSubType]
end

function ItemHelperTool.GetTypeIconAndTipsByExtItemId(extItemId)
    local extensionBoxDescTable = Facade.TableManager:GetTable("ExtensionBoxDesc")
    local itemCfg
    for _, cfg in ipairs(extensionBoxDescTable) do
        if extItemId == cfg.ItemID then
            itemCfg = cfg
            break
        end
    end
    if not itemCfg then
        logerror('ExtensionBoxDesc表格中传入的extItemId没有对应的配置，请检查')
        return
    end
    return itemCfg.TypeIcon, itemCfg.TypeTips
end

-- function ItemHelperTool.GetQualityColorByExtItemId(extItemId)
--     local itemMainType = ItemHelperTool.GetMainTypeById(extItemId)
--     ensure(itemMainType == EItemType.ExtendItem)

--     local qualityType = ItemHelperTool.GetQualityTypeById(extItemId)

-- end

-- function ItemHelperTool.GetContainerItemSizeCfg(id)
--     local itemMainType = ItemHelperTool.GetMainTypeById(id)
--     if itemMainType == EItemType.Equipment then
--         local itemSubType = ItemHelperTool.GetSubTypeById(id)
--         local row
--         if itemSubType == EEquipmentType.Bag then
--             row = ItemConfigTool.GetBagFuncTable(id)
--         elseif itemSubType == EEquipmentType.ChestHanging then
--             row = ItemConfigTool.GetChestHangingFuncTable(id)
--         elseif itemSubType == EEquipmentType.SafeBox then
--             row = ItemConfigTool.GetSafeBoxFuncTable(id)
--         end

--         if row then
--             return row.GainEquipPosLength, row.GainEquipPosWidth, row.TotalCapacity
--         end
--     end

--     return 0, 0, 0
-- end

function ItemHelperTool.GetBagCapacityExtendValue(id)
    local itemMainType = ItemHelperTool.GetMainTypeById(id)
    if itemMainType == EItemType.Equipment then
        local itemSubType = ItemHelperTool.GetSubTypeById(id)
        local row
        if itemSubType == EEquipmentType.Bag then
            row = ItemConfigTool.GetBagFuncTable(id)
        elseif itemSubType == EEquipmentType.ChestHanging then
            row = ItemConfigTool.GetChestHangingFuncTable(id)
        elseif itemSubType == EEquipmentType.SafeBox then
            row = ItemConfigTool.GetSafeBoxFuncTable(id)
        end

        if row then
            return row.BagIncrementY
        end
    end

    return 0, 0
end

function ItemHelperTool.IsCantIntoDsItemById(id)
    -- 获取道具id前四位
    local idstr = string.sub(id, 1, 4)
    local cantIntoDSstr = DFMGlobalConst.GetGlobalConst("ItemNotAllowedIntoDS", 1)
    local strList=string.split(cantIntoDSstr,",")
    return table.contains(strList or {},idstr)
end

function ItemHelperTool.IsExposureItem(id)
    -- 获取道具id前四位
    local idstr = string.sub(id, 1, 4)
    -- local cantIntoDSstr =  DFMGlobalConst.GetGlobalConst("ItemNotAllowedIntoDS", 1)
    if idstr == "1611" then
        return true
    else
        return false
    end
end

function ItemHelperTool.FindPermissonInfo(id)
    local permissionInfo = Facade.TableManager:GetTable("SafeAndCardPackConfig")
    if permissionInfo then
        for _, permissionItemInfo in ipairs(permissionInfo) do
            if tonumber(permissionItemInfo.VirtualItemID) == id then
                return permissionItemInfo
            end
        end
    end
end

function ItemHelperTool.FindPermissonArray(type)
    local permissionInfo = Facade.TableManager:GetTable("SafeAndCardPackConfig")
    local itemArr = {}
    if permissionInfo then
        for _, permissionItemInfo in ipairs(permissionInfo) do
            if permissionItemInfo.Type == type then
                itemArr[permissionItemInfo.VirtualItemID] = permissionItemInfo
            end
        end
    end
    return itemArr
end


function ItemHelperTool.FindSafeBoxSkinData()
    local safeboxSkinInfos = Facade.TableManager:GetTable("SafeBoxSkin")
    local safeboxSkinArr = {}
    if safeboxSkinInfos then
        for _, skinInfo in ipairs(safeboxSkinInfos) do
            local skinItem = ItemBase:New(tonumber(skinInfo.SkinID))
            table.insert(safeboxSkinArr, skinItem)
        end
    end
    return safeboxSkinArr
end

function ItemHelperTool.FindSafeBoxSkinByID(SkinID)
    local safeboxSkinInfo = Facade.TableManager:GetTable("SafeBoxSkin")
    if safeboxSkinInfo then
        for _, safeboxSkin in ipairs(safeboxSkinInfo) do
            if SkinID == tonumber(safeboxSkin.SkinID) then
                return safeboxSkin
            end
        end
    end
end

function ItemHelperTool.IsDecipheredBrickTypeById(itemID)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemID)
    local itemThirdType = ItemHelperTool.GetThirdTypeById(itemID)

    local bIsGift = itemMainType and itemMainType == EItemType.Gift
    local bIsBlindBoxGift = itemSubType and itemSubType == EGiftType.BlindBoxGift
    local bIsDecipheredBrick = itemThirdType and itemThirdType == EMandelBrickType.DecipheredBrick

    if bIsGift and bIsBlindBoxGift and bIsDecipheredBrick then
        return true
    end
    return false
end

function ItemHelperTool.IsMasterSkinPattern(patternId)
    return patternId >= 20000
end

function ItemHelperTool.IsMysticalSkin(itemID)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
    if itemMainType ~= EItemType.WeaponSkin then
        return false
    end

    local itemThirdType = ItemHelperTool.GetThirdTypeById(itemID)
    return itemThirdType == 0
end

function ItemHelperTool.IsMysticalPendant(itemID)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemID)
    if itemMainType ~= EItemType.Adapter or itemSubType ~= ItemConfig.EAdapterItemType.Pendant then
        return false
    end

    local itemThirdType = ItemHelperTool.GetThirdTypeById(itemID)
    return itemThirdType == 64
end

function ItemHelperTool.CreateItemByPropInfo(propInfo, dataChange, bShouldSetSkinInfo)
    return ItemHelperTool._InternalCreateItemByPropInfo(nil, propInfo, dataChange, bShouldSetSkinInfo)
end

function ItemHelperTool.LazyCreateCreateItemByPropInfo(propInfo, dataChange, bShouldSetSkinInfo)
    local lazyObj = LazyObject.CreateLazyObject(ItemHelperTool._InternalCreateItemByPropInfo, propInfo, dataChange, bShouldSetSkinInfo)
    return lazyObj
end

function ItemHelperTool._InternalCreateItemByPropInfo(t, propInfo, dataChange, bShouldSetSkinInfo)
    local item = t
    if propInfo then
        propInfo.num = propInfo.num or 1
        propInfo.gid = propInfo.gid or 0
        propInfo.weapon = propInfo.weapon or {}
        if item then
            item = ItemBase:NewFromOutIns(t, propInfo.id, propInfo.num or 1, propInfo.gid)
        else
            item = ItemBase:NewIns(propInfo.id, propInfo.num or 1, propInfo.gid)
        end
        if item.itemMainType == EItemType.WeaponSkin then
            if item.itemSubType == ItemConfig.EWeaponItemType.Melee then
                local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
                local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                if weaponDescription == nil then
                    weaponDescription = item:GetRawDescObj()
                end
                local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
                propInfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
                if propInfo ~= nil then
                    if propInfo.weapon ~= nil and propInfo.weapon.skinInfo ~= nil then
                        propInfo.weapon.skinInfo.color = item.quality
                    end
                end
            elseif ItemHelperTool.IsMysticalSkin(item.id) or bShouldSetSkinInfo then
                local skinInfo = pb.WeaponSkinInfo:New()
                skinInfo.skin_id = propInfo.id
                skinInfo.skin_gid = propInfo.gid
                ItemBase.SetWeaponSkinInfo2PropInfo(propInfo, skinInfo)
                item:SetSkinDescFromrPropInfo(propInfo, true)
                if dataChange then
                    item.exceedPercent = dataChange.exceed_percent 
                end 
            end
        end
        if item.itemMainType ~= EItemType.Weapon then
            item:SetRawPropInfo(propInfo)
        elseif propInfo.bind_type then
            item:ForceSetBindType(propInfo.bind_type)
        end
    end
    return item
end

function ItemHelperTool.IsRequireAmmoWeapon(itemID)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
    if itemMainType == EItemType.Weapon or itemMainType == EItemType.Receiver then
        local itemSubType = ItemHelperTool.GetSubTypeById(itemID)
        if itemSubType == ItemConfig.EWeaponItemType.EmptyHand
                or itemSubType == ItemConfig.EWeaponItemType.Melee
                or itemSubType == ItemConfig.EWeaponItemType.Bow then
            return false
        end
        return true
    end
    return false
end

function ItemHelperTool.GetSpecialExchangeInfo(inItemID)
    local specialExchangeConfigTable = Facade.TableManager:GetTable("SpecialExchangeConfig")
    if isinvalid(specialExchangeConfigTable) then
        return
    end
    for _, exchangeConfig in pairs(specialExchangeConfigTable) do
        local itemID = tonumber(exchangeConfig.ItemID)
        if itemID == inItemID then
            return exchangeConfig
        end
    end

    return nil
end

function ItemHelperTool.GetSpecialExchangeType(inItemID)
    local exchangeConfig = ItemHelperTool.GetSpecialExchangeInfo(inItemID)
    if isinvalid(exchangeConfig) then
        return nil
    end
    return tonumber(exchangeConfig.Type)
end

function ItemHelperTool.GetSpecialExchangeID(inItemID)
    local exchangeConfig = ItemHelperTool.GetSpecialExchangeInfo(inItemID)
    if isinvalid(exchangeConfig) then
        return nil
    end
    return tonumber(exchangeConfig.ExchangeID)
end

return ItemHelperTool