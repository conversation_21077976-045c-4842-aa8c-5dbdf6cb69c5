----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



local AssemblyChatHD = require "DFM.Business.Module.ArmedForceModule.UI.SquadPick.AssemblyChatHD"
local MsgPanelTopTournament_PC = ui("MsgPanelTopTournament_PC", AssemblyChatHD)

local EGameHUDState = import "GameHUDSate"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local EBreakthroughStage = import("EBreakthroughStage")
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local EDFMGamePlayMode = import "EDFMGamePlayMode"

--- BEGIN MODIFICATION @ VIRTUOS
local UGameFlowDelegates = import "GameFlowDelegates"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local UGPInputHelper = import "GPInputHelper"
--- END MODIFICATION

local function GetInGameController()
    return InGameController:Get()
end

function MsgPanelTopTournament_PC:Ctor()
    -- self:Super().Ctor(self)

    self.uiName = "MsgPanelTopTournament_PC"
    self._maxChatLen = 81

    -- self.bInMPKillMarkerViewPosition = false
    -- self.killerMarkOffsetY = 230

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 是否在局内
    self._bIsInGame = false 
    self._switchChatWindowHandle = -1
    --- END MODIFICATION

    self._wtSlotForContent = self:Wnd("DFNamedSlot_MakeAppointment", UIWidgetBase)
end

function MsgPanelTopTournament_PC:OnOpen()
    AssemblyChatHD.OnOpen(self)

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 手动触发一次GameFlow刷新，防止进入到局内了还没有加载出来（SOL模式会存在这个问题）
    local _curGameFlow = UGameFlowDelegates.GetGameFlowDelegates(GetGameInstance()):GetCurGameFlowStage()
    self:_GameFlowChangeEnter(_curGameFlow)
    --- END MODIFICATION
end

function MsgPanelTopTournament_PC:_BindEvent()
    AssemblyChatHD._BindEvent(self)
    --- BEGIN MODIFICATION @ VIRTUOS: 输入绑定处理
    -- 若在局内，先不进行输入绑定，而是根据输入类型来决定是否启用输入
    if not self._bIsInGame then
        self:RemoveInputActionBinding(self._switchChatWindowHandle)
        self._switchChatWindowHandle = self:AddInputActionBinding("OpenChat", EInputEvent.IE_Pressed, self._OnSwitchChatWindow, self, EDisplayInputActionPriority.UI_Chat)
    end
    --- END MODIFICATION
    
    LuaGlobalEvents.evtGameFlowChangeEnter:AddListener(self._GameFlowChangeEnter, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    LuaGlobalEvents.evtGameFlowChangeLeave:AddListener(self._GameFlowChangeLeave, self)
    -- 绑定多输入设备切换事件
    self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    --- END MODIFICATION
    
end

function MsgPanelTopTournament_PC:_UnbindEvent()
    AssemblyChatHD._UnbindEvent(self)
    
    self:RemoveInputActionBinding(self._switchChatWindowHandle)

    LuaGlobalEvents.evtGameFlowChangeEnter:RemoveListener(self._GameFlowChangeEnter, self)

    --- BEGIN MODIFICATION @ VIRTUOS
    LuaGlobalEvents.evtGameFlowChangeLeave:RemoveListener(self._GameFlowChangeLeave, self)

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS: 进入游戏后，也触发一次InputBinding判断
function MsgPanelTopTournament_PC:_GameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game or gameFlowType == EGameFlowStageType.GameSettlement then
        self._bIsInGame = true

        local curInpurtType = UGPInputHelper.GetCurrentInputType(GetGameInstance())
        self:_OnInputTypeChanged(curInpurtType)
    end
end

-- 离开游戏，Chat重新绑定
function MsgPanelTopTournament_PC:_GameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game or gameFlowType == EGameFlowStageType.GameSettlement then
        self._bIsInGame = false
        self:RemoveInputActionBinding(self._switchChatWindowHandle)
        self._switchChatWindowHandle = self:AddInputActionBinding("OpenChat", EInputEvent.IE_Pressed, self._OnSwitchChatWindow, self, EDisplayInputActionPriority.UI_Chat)
    end
end
------ END MODIFICATION

function MsgPanelTopTournament_PC:SetOffset(offset)
    self._wtMsgOperationPanel:SetRenderTranslation(offset)
    self._wtMsgHistoryPanel:SetRenderTranslation(offset)
end

function MsgPanelTopTournament_PC:_OnTextChanged(inText)
    inText = tostring(inText)
    -- 字符长度限制
    local _,fixText = StringUtil.GetRealWidth(inText, self._maxChatLen)
    if fixText ~= inText then
        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.ExceedChatMaxLen)
        self._wtMsgInputTB:SetText(fixText)
    end
    self.oldText = fixText
end

--- BEGIN MODIFICATION @ VIRTUOS
function MsgPanelTopTournament_PC:_OnInputTypeChanged(InputType)
    -- 局内 + 手柄操作时，不能打开聊天窗口
    if self._bIsInGame then
        if InputType == EGPInputType.Gamepad then
            self:RemoveInputActionBinding(self._switchChatWindowHandle)
        else
            self:RemoveInputActionBinding(self._switchChatWindowHandle)
            self._switchChatWindowHandle = self:AddInputActionBinding("OpenChat", EInputEvent.IE_Pressed, self._OnSwitchChatWindow, self, EDisplayInputActionPriority.UI_Chat)
        end
    end
end
--- END MODIFICATION

function MsgPanelTopTournament_PC:OnShow()
    AssemblyChatHD.OnShow(self)
    self:AddLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.SomethingInviteStateChange, self)
end

function MsgPanelTopTournament_PC:OnHide()
    self:RemoveLuaEvent(Module.Chat.Config.evtInGameTeamInviteUpdate, self.SomethingInviteStateChange)
    self:_RemoveChatMakeAppointContent()
end

function MsgPanelTopTournament_PC:OnClose()
    AssemblyChatHD.OnClose(self)
end

function MsgPanelTopTournament_PC:HideMsgOperationPanel()
    AssemblyChatHD.HideMsgOperationPanel(self)
    self:_RemoveChatMakeAppointContent()
end

function MsgPanelTopTournament_PC:ShowMsgOperationPanel()
    AssemblyChatHD.ShowMsgOperationPanel(self)
    self:UpdateChatMakeAppointState()
end

function MsgPanelTopTournament_PC:SomethingInviteStateChange(reason, startIndexVal)
    if Module.Team.Field.GameTeamInviteViewModel:GetNoReceiveSignal() then
        self._wtSlotForContent:Collapsed()
        return
    end

    Module.Chat.Config.evtInGamePreBookCardUpdate:Invoke(reason)
    if Module.Team.Field.GameTeamInviteViewModel:GetPendingSum() <= 0 then
        self._wtSlotForContent:Collapsed()
    else
        if not self.chatMakeAppoint and (self.bInOperation) then
            self:UpdateChatMakeAppointState()
        end
        self._wtSlotForContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    end
end

function MsgPanelTopTournament_PC:UpdateChatMakeAppointState()
    self:_RemoveChatMakeAppointContent()
    if Module.Team.Field.GameTeamInviteViewModel:GetPendingSum() > 0 then
        local dataValue, index = Module.Team.Field.GameTeamInviteViewModel:GetTheOldestPendingReq()
        if dataValue then
            self.chatMakeAppoint = Facade.UIManager:AddSubUI(self, UIName2ID.ChatMakeAppoint, self._wtSlotForContent)
            self._wtSlotForContent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        end
    end
end

function MsgPanelTopTournament_PC:_RemoveChatMakeAppointContent()
    self.chatMakeAppoint = nil
    Facade.UIManager:RemoveSubUIByParent(self, self._wtSlotForContent)
end

function MsgPanelTopTournament_PC:_ClearChatMakeAppointContent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtSlotForContent)
end

function MsgPanelTopTournament_PC:_OnMouseButtonDown(mouseEvent)
    if self.bInOperation then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = self._wtMsgOperationPanel:GetCachedGeometry()
        local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)

        local geometry2 = self._wtSlotForContent:GetCachedGeometry()
        local isUnder2 = USlateBlueprintLibrary.IsUnderLocation(geometry2, sceenPos)

        -- 点击聊天框外 关闭聊天框
        if (not isUnder) and (not isUnder2) then
            loginfo("MsgPanelHDBase:_OnMouseButtonDown, Close input panel")
            self:HideMsgOperationPanel()
        end
    end
end

return MsgPanelTopTournament_PC