----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class HeroMainPanel : LuaUIBaseView
local HeroMainPanel                = ui("HeroMainPanel")
local HeroDataTable                = Facade.TableManager:GetTable("Hero/HeroData")
local HeroFashionDataTable         = Facade.TableManager:GetTable("Hero/HeroFashionData")
-- local HeroMainTabGroup = require"DFM.Business.Module.HeroModule.UI.HeroMain.HeroMainTabGroupView"
local HeroSkillIcon                = require "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillIcon"
local HeroLogic                    = require "DFM.Business.Module.HeroModule.Logic.HeroLogic"
local HeroConfig                   = require "DFM.Business.Module.HeroModule.HeroConfig"
local HeroHelperTool               = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local UDFMGameHudDelegates         = import "DFMGameHudDelegates"
local HeroPropItem                 = require "DFM.Business.Module.HeroModule.UI.HeroMain.HeroPropItem"
local HeroSkillDetailPanel         = require "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillDetailView"
local LobbyDisplayLogic            = require "DFM.Business.Module.LobbyDisplayModule.Logic.LobbyDisplayLogic"
local EComp                        = Module.CommonWidget.Config.EIVWarehouseTempComponent
local SkillHelperTool              = require "DFM.StandaloneLua.BusinessTool.SkillHelperTool"
local ItemConfigTool               = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool               = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ECheckBoxState               = import "ECheckBoxState"
local UDFMLightCoordinateSubsystem = import "DFMLightCoordinateSubsystem"
local FAnchors                     = import "Anchors"
local ButtonIdConfig               = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local UHallLevelSequenceManager = import "HallLevelSequenceManager"
local EUINavigation 			   = import "EUINavigation"
local ArmedForceDataTable 		   = Facade.TableManager:GetTable("ArmedForceData")


local BottomBarState = Module.Hero.Config.BottomBarState

local OPERATE_BTN_CLICK_CD = 0.5

function HeroMainPanel:Ctor()
	self:InitData()
	self:InitPanel()
	--拉取一下活动,方便后续使用
	Server.ActivityServer:InitActivityInfo()
	--拉取一下商城干员抽奖数据
	Server.StoreServer:SendHeroDrawData()

	-- BEGIN MODIFICATION @ VIRTUOS
	if IsHD() then
		self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
	end
	-- END MODIFICATION
end

function HeroMainPanel:InitData()
end

function HeroMainPanel:_RegisterEvents()
	self:AddLuaEvent(Server.HeroServer.Events.evtMPUsedHeroIdChanged, self._OnSelectedHeroIdChange, self)
	self:AddLuaEvent(Server.HeroServer.Events.evtSOLUsedHeroIdChanged, self._OnSelectedHeroIdChange, self)

	self:AddLuaEvent(Module.Hero.Config.Events.evtMainContentPanelShowChanged, self._OnMainContentPanelShowChanged, self)

	self:AddLuaEvent(Server.HeroServer.Events.evtHeroUnlockNtf, self._OnHeroUnlockNtf, self)
	--self:AddLuaEvent(Server.HeroServer.Events.evtAccessoryUnlockNtf, self._OnHeroUnlockNtf, self)

	self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnEnterExpertLevel, self)
	self:AddLuaEvent(Module.Room.Config.Events.evtbKickOutOfRoom, self.OnbKickOutOfRoom, self)
	self:AddLuaEvent(Module.Hero.Config.Events.evtSelectHeroIdForShowChanged, self._OnHeroSelectedChanged, self)
end

function HeroMainPanel:_UnregisterEvents()
	self:RemoveLuaEvent(Server.HeroServer.Events.evtMPUsedHeroIdChanged, self._OnSelectedHeroIdChange)
	self:RemoveLuaEvent(Server.HeroServer.Events.evtSOLUsedHeroIdChanged, self._OnSelectedHeroIdChange)

	self:RemoveLuaEvent(Module.Hero.Config.Events.evtMainContentPanelShowChanged, self._OnMainContentPanelShowChanged)

	self:RemoveLuaEvent(Server.HeroServer.Events.evtHeroUnlockNtf, self._OnHeroUnlockNtf)

	self:RemoveLuaEvent(LuaGlobalEvents.evtSceneLoaded, self._OnEnterExpertLevel)
	self:RemoveLuaEvent(Module.Room.Config.Events.evtbKickOutOfRoom, self.OnbKickOutOfRoom)
	self:RemoveLuaEvent(Module.Hero.Config.Events.evtSelectHeroIdForShowChanged, self._OnHeroSelectedChanged)
end

function HeroMainPanel:InitPanel()
	self._preScrollCurrOffset = 0

	Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {}) -- ECurrencyClientId.Special, ECurrencyClientId.Diamond
	if DFHD_LUA == 1 then
		Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
	else
		Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
	end

	Module.CommonBar:RegStackUITopBarTitle(self, Module.Hero.Config.Loc.HeroTitle)
	Module.CommonBar:BindBackHandler(self._PlayCloseAnim, self)

	-- Module.CommonBar:RegStackUIInputSummary(self,
	--         {{actionName = "SelectHero",func = self._OnSelectHero, caller = self ,bUIOnly = true, bHideIcon = false},
	-- 		{actionName = "ShowAppearance",func = self._OnShowAppearance, caller = self ,bUIOnly = true, bHideIcon = false}})

	-- Module.CommonBar:SetCurrencyVisible(true)

	self._wtLeftContentPanel = self:Wnd("CanvasPanel_47", UIWidgetBase)
	self._wtRightContentPanel = self:Wnd("Right", UIWidgetBase)

	if IsHD() then
		self._wtHeroItemRoot = self:Wnd("ScrollBox", UIScrollBox)
		if self:Wnd("ScrollBox_70", UIScrollBox) then
			self:Wnd("ScrollBox_70", UIScrollBox):Collapsed()
		end
	else
		self._wtHeroItemRoot = self:Wnd("ScrollBox_70", UIScrollBox)
		if self:Wnd("ScrollBox", UIScrollBox) then
			self:Wnd("ScrollBox", UIScrollBox):Collapsed()
		end
	end
	-- self._wtHeroItemRoot = self:Wnd("ScrollBox_70", UIScrollBox)
	self._wtHeroItemRoot:SetDeviation(0.01)
	--TextBlock_79
	-- self._wtExpertMainNameText = self:Wnd("TextBlock_69", UITextBlock)
	-- self._wtHeroFXNameText = self:Wnd("TextBlock_1fx", UITextBlock)
	self._wtHeroTitleNameText = self:Wnd("TextBlock_125", UITextBlock)
	-- self._wtHeroNameIcon = self:Wnd("DFImage_26", UIImage)
	-- self._wtHeroNameIconfx = self:Wnd("DFMImage_fxzi", UIImage)

	self._wtHeroName = self:Wnd("DFTextBlock_97", UITextBlock)
	self._wtHeroNameMiddle = self:Wnd("DFTextBlock", UITextBlock)
	self._wtHeroNameBackward = self:Wnd("DFTextBlock_1", UITextBlock)
	-- self._wtExpertZoomNameText = self:Wnd("TextBlock_367", UITextBlock)
	-- self._wtHeroZoomNameText = self:Wnd("RichTextBlock_168", UITextBlock)

	-- self._wtSkillTitleName = self:Wnd("TextBlock",UITextBlock)
	-- self._wtSkillTitleName:SetText(HeroConfig.Loc.SkillTitleName)
	-- self._wtTalentName = self:Wnd("TextBlock_216",UITextBlock)
	-- self._wtTalentName:SetText(HeroConfig.Loc.TalentName)

	-- self._wtImageIconBG = self:Wnd("Image_Icon_BG",UIImage)
	self._wtImageIcon = self:Wnd("Image_Icon_S", UIImage)

	self._wtHeroDetailBtn = self:Wnd("HeroDetailBtn", DFCommonButtonOnly)
	self._wtHeroDetailBtn:Event("OnClicked", self._OnOpenHeroProfile, self)

	-- 天赋技能
	self._wtSkillIcon1 = self:Wnd("HeroSkill1", HeroSkillIcon)
	self._wtSkil1Selected = self._wtSkillIcon1:Wnd("DFImage_Selected", UIImage)
	self._wtSkil1Selected:Collapsed()

	self._wtSkillIcon2 = self:Wnd("HeroSkill2", HeroSkillIcon)
	self._wtSkill2Selected = self._wtSkillIcon2:Wnd("DFImage_Selected", UIImage)
	self._wtSkill2Selected:Collapsed()


	self._wtHeroBuy = self:Wnd("HeroBuy", DFCommonButtonOnly)
	self._wtHeroBuy:Visible()
	self._wtHeroBuy:Event("OnClicked", self._OnHeorOperateBtnClick, self)
	self._wtHeroOperateBtnClickCallback = nil

	---@type DFCommonButtonOnly
	self._wtHeroAppearanceButton = self:Wnd("HeroAppearanceButton", DFCommonButtonOnly)
	self._wtHeroAppearanceButton:Event("OnClicked", self._OnFashionBtnClick, self)

	-- self._wtExpertTitle = self:Wnd("DFTextBlock_202",UITextBlock)
	-- self._wtExpertTitle:SetText(Module.Hero.Config.Loc.HeroSelectTitileName)

	-- 道具
	self._wtHeroPropItem1 = self:Wnd("WBP_Hero_PropsItem1", HeroPropItem)
	self._wtHeroPropItem1Selected = self._wtHeroPropItem1:Wnd("DFImage_Selected", UIImage)
	self._wtHeroPropItem1Selected:Collapsed()

	self._wtHeroPropItem2 = self:Wnd("WBP_Hero_PropsItem2", HeroPropItem)
	self._wtHeroPropItem2Selected = self._wtHeroPropItem2:Wnd("DFImage_Selected", UIImage)
	self._wtHeroPropItem2Selected:Collapsed()


	-- self._wtLeftPanel = self:Wnd("LeftPanel", UIWidgetBase)

	self._wtMainContentPanel = self:Wnd("MainContentPanel", UIWidgetBase)
	--按钮隐藏
	self._wtHeroOperateButton = self:Wnd("HeroOperateButton", UIWidgetBase)
	self._wtHeroOperateButton:Collapsed()
	--按钮容器
	self._wtHeroBtnPanel = self:Wnd("DFNamedSlot_193", UIWidgetBase)
	-- self._wtHeroBtnPanel:SetVisibility(ESlateVisibility.Collapsed)
	-- ExpertInfoButton
	self._wtExpertInfoButton = self:Wnd("WBP_CommonButtonV3S1", DFCheckBoxOnly)
	self._wtExpertInfoCheckBox = self._wtExpertInfoButton:Wnd("DFCheckBox_Icon", UICheckBox)


	-- Tips容器
	-- 角色天赋tip容器
	self._HeroTalentTipPanel = self:Wnd("HeroTalentTipPanel", UIWidgetBase)

	self._HeroSkillButton = self:Wnd("WBP_DFCommonButtonV3S1", DFCommonButtonOnly)
	self._HeroSkillButton:SetMainTitle(HeroConfig.Loc.ViewSkills)
	self._HeroSkillButton:Event("OnClicked", self._OnShowSkillPanel, self)

	---干员招募按钮
	self._wtApproachButton = self:Wnd("WBP_Hero_Approach_C_0", UIWidgetBase)
	-- if self._wtApproachButton and self._wtApproachButton.BindJumpClick then
    --     self._wtApproachButton:BindJumpClick(self._OnApproachButtonClick, self)
    -- end
	self._wtApproachButton:Collapsed()

	self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)

	self._toAppearancePanel = false

	-- HeroItem行数
	self._CELL_ROW_COUNT = 1
	self._animStartDelayTime = 0.05
	self._animDeltaTime = 0.03

	-- PC tip position
	-- self._NormalTipPosition = FVector2D(-1300, 325)
	-- if DFHD_LUA == 1 then
	-- 	self._BottomTipPosition = FVector2D(-1300, 1070)
	-- else
	-- 	self._BottomTipPosition = FVector2D(-1300, 931)
	-- end
	-- mobile tip position

	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	self._UltimateSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLUltimateSkillID or
		SkillHelperTool.GetMPUltimateSkillID
	self._SupportSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLSupportSkillID or
		SkillHelperTool.GetMPSupportSkillID
	self._ActiveSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLActiveSkillID or
		SkillHelperTool.GetMPActiveSkillID
	self._PassiveSkillFunc = armedForceMode == EArmedForceMode.SOL and SkillHelperTool.GetSOLPassiveSkillID or
		SkillHelperTool.GetMPPassiveSkillID

end

-- function HeroMainPanel:_OnProcessChildWidget(Position, ItemWidget)
-- 	local itemCount = #self._allHeroItems
-- 	if self._allHeroItems then
-- 		local rowIdx = Position / self._CELL_ROW_COUNT
-- 		if rowIdx <= itemCount then
-- 			ItemWidget:PlayShowAnim(rowIdx * self._animDeltaTime + self._animStartDelayTime)
-- 		end
-- 	end
-- end

function HeroMainPanel:_IsJumpView(isBool)
	if isBool ~= nil then
		self.__isJumpView = isBool
	end
	return self.__isJumpView
end

--==================================================
--region Life function
function HeroMainPanel:OnOpen()
	self:HandleHeroPanelState()
end

function HeroMainPanel:HandleHeroPanelState()
	if UHallLevelSequenceManager then
		local onSeqStopped = UHallLevelSequenceManager.Get(GetWorld()).OnSequenceStopped
		onSeqStopped:Add(self.OperatorCommitTransition, self)
	end
	self.heroIdList = {}
	self._wtHeroItemRoot:ClearChildren()
	self.selectedBox = { self._wtSkil1Selected, self._wtSkill2Selected, self._wtHeroPropItem1Selected,
		self._wtHeroPropItem2Selected }
	for _, v in pairs(Server.HeroServer:GetHeroData()) do
		table.insert(self.heroIdList, v.hero_id)
	end
	table.sort(self.heroIdList, function(a, b)
		local heroInfoA = HeroDataTable[tostring(a)]
		local heroInfoB = HeroDataTable[tostring(b)]
		if heroInfoA.ArmedForceID == heroInfoB.ArmedForceID then
			return heroInfoA.ExpertID < heroInfoB.ExpertID
		else
			return heroInfoA.ArmedForceID < heroInfoB.ArmedForceID
		end
	end)

	self._allHeroItems = {}
	self._armedForceId2HeroItemUIInst = {}
	self._onOperateExpertEvent = nil
	self._armedWrapDict = {}
	for i, v in ipairs(self.heroIdList) do
		local uins
		if IsHD() then
			local heroData = HeroDataTable[tostring(v)]
			if not self._armedWrapDict[heroData.ArmedForceID] then
				local weakUins = Facade.UIManager:AddSubUI(self, UIName2ID.HeroMainViewHeroListItem, self._wtHeroItemRoot)
				self._armedWrapDict[heroData.ArmedForceID] = weakUins

				local armedForceInfo = ArmedForceDataTable[tostring(heroData.ArmedForceID)]
				if armedForceInfo and getfromweak(weakUins) then
					getfromweak(weakUins):SetTitle(armedForceInfo.Name)
				end
			end
			local wrapUI = self._armedWrapDict[heroData.ArmedForceID]
			if getfromweak(wrapUI) then
				uins = getfromweak(wrapUI):AddHeroItem()
			end
		else
			local weakUins = Facade.UIManager:AddSubUI(self, UIName2ID.HeroMainViewHeroItem, self._wtHeroItemRoot)
			uins = getfromweak(weakUins)
		end

		if not uins then
			logerror("[echewzhu]HeroMainPanel:uins is invalid")
			return
		end
		if uins then
			if DFHD_LUA == 1 then
				uins:SetType(0)
			else
				uins:SetType(1)
			end
		end

		local onEnterCallback = CreateCallBack(function(self, uins)
			if self._toAppearancePanel then
				return
			end
			--保存悬浮id
			local oldHoverdId = self._hoverHeroId
			self._hoverHeroId = uins:GetHeroId()
			if oldHoverdId ~= self._hoverHeroId then
				Module.Hero.Config.Events.evtOnHoverHeroChanged:Invoke(self._hoverHeroId)
			end

			if WidgetUtil.IsGamepad() then
				Module.Hero:ShowHeroById(self._hoverHeroId)
			end

			--self:SwitchBottomBarState(BottomBarState.Hovering)
			if self.curSeletcHeroId == uins:GetHeroId() then
				return
			end

			if Server.HeroServer:IsCanUseHero(v) then
				uins:ToggleLockMask(false)
			else
				uins:ToggleLockMask(true)
			end

			if WidgetUtil.IsGamepad() then
				self.curSeletcHeroId = uins:GetHeroId()
				self:SetSelectedHeroCell(uins)
			end
		end, self)

		local onClickCallback = CreateCallBack(function(self, uins)
			--保存点击的干员id:解决手机端跳转回按钮不刷新问题
			self._androidHeroId = uins:GetHeroId()
			-- Module.Hero:ShowHeroById(self._androidHeroId)

			if DFHD_LUA == 1 then
				if self.curSeletcHeroId == Server.HeroServer:GetCurUsedHeroId() then
					return
				end

				if WidgetUtil.IsGamepad() then
					--是否拥有该干员
					Module.Hero:ShowHeroById(self._androidHeroId)
					if Server.HeroServer:IsCanUseHero(v) then
						self.curSeletcHeroId = v
						Module.Hero:UseHeroById(v)
						LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.SetToFight)
					else
						local unLockInfo, heroInfo = self:_GetHeroInfoData(v)
						self:_IsJumpView(true)
						self.curBottomBarState = nil
						if unLockInfo and heroInfo then
							Module.Hero:ShowUnLockHeroPanel(heroInfo, unLockInfo)
						else
							loginfo("heroId is ", v, " get unLockInfo is nil")
						end
					end
				else
					-- 键鼠 已经点击选中过该英雄，再次点击，设置为出战/查看解锁信息
					if self.curSeletcHeroId == v then
						self:_OnSelectHero()
					else
						Module.Hero:ShowHeroById(self._hoverHeroId)
						self.curSeletcHeroId = uins:GetHeroId()
						self:SetSelectedHeroCell(uins)
					end
				end
				---------------------------------------------------------------------------------------------------*****************************************************
				-- self._onOperateExpertEvent(v)
			else
				Module.Hero:ShowHeroById(self._androidHeroId)
				if not self._toAppearancePanel then
					if self.curSeletcHeroId == uins:GetHeroId() then
						return
					end
					self:SetSelectedHeroCell(uins)
					self:SetFileBtnState(v)
				end
			end

			-- Module.Hero.Config.Events.evtOnHeroItemClicked:Invoke(uins:GetHeroId())
		end, self)

		local function onHoverCallback(uins)
		end

		local onLeaveCallback = CreateCallBack(function(self, uins)
			if self._curUIState ~= 6 then
				self:SwitchBottomBarState(BottomBarState.Common)
			else
				self.curBottomBarState = BottomBarState.Common
			end
		end, self)

		local onRightButtonCallback = CreateCallBack(function(self)
			-- pc RMB fashion
			self:_OnFashionBtnClick()

		end, self)
		
		-- BEGIN MODIFICATION @ VIRTUOS : uins may be nil.
		if uins then
			if Server.HeroServer:IsCanUseHero(v) then
				uins:ToggleLockMask(false)
			else
				uins:ToggleLockMask(true)
			end

			uins:InitData({ index = i, heroId = v, fCallback = onClickCallback, fHoverCallback = onHoverCallback,
				fEnterCallback = onEnterCallback, fLeaveCallback = onLeaveCallback, fRightButtonCallback = onRightButtonCallback ,bShowGetMethodTips = true , bShowRecruitmentInfo = true})
		end
		-- END MODIFICATION

		self._allHeroItems[i] = uins

		local heroInfo = HeroDataTable[tostring(v)]
		if heroInfo then
			if not self._armedForceId2HeroItemUIInst[heroInfo.ArmedForceID] then
				self._armedForceId2HeroItemUIInst[heroInfo.ArmedForceID] = uins
			end
		end
	end

	self._onOperateExpertEvent = function(heroId)
		for _, uins in pairs(self._allHeroItems) do
			if uins:GetHeroId() == tonumber(heroId) then
				uins:OnOperated()
			else
				uins:OnUnOperated()
			end
		end
	end

	-- self:_OnSelectedHeroId()

	if self._selectHeroCell then
		local offset = (self._selectHeroCell:GetIndex() - 1) * 240
		self._wtHeroItemRoot:ScrollToOffset(offset, 0.1, 0.01)
	end

	UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnHandleMouseButtonUpEvent, self)
	self._wtHeroAppearanceButton:BP_SetMainTitle(Module.Hero.Config.Loc.HeroAppearanceTitle)

	-- 注册显示专家信息tips
	self._wtExpertInfoCheckBox:Event("OnCheckStateChanged", self._ToggleExpertTalentInfo, self)

	if not VersionUtil.IsShipping() then
		-- 注册快捷键
		self:_InitShortCuts()
	end
end

function HeroMainPanel:_OnHeroSelectedChanged(curSelectHeroId, curSelectHeroFashionSuitId)
	self:SetBottomBarActions(curSelectHeroId)
end

function HeroMainPanel:SetBottomBarActions(curHeroUsedID)
	curHeroUsedID = setdefault(curHeroUsedID, self._hoverHeroId)
	local defActionName = "UnlockHero"
	local isUnlock = Server.HeroServer:IsCanUseHero(curHeroUsedID)
	-- if WidgetUtil.IsGamepad() then
	-- 	defActionName = "Unlock"
	-- end
	if isUnlock then
		defActionName = "SelectHero"
	end
	if curHeroUsedID then
		local summaryList = {
			--设为出战
			{ actionName = defActionName, func = self._OnSelectHero, caller = self, bUIOnly = false, bHideIcon = false},--, disable = true
			{ actionName = "ShowAppearance", func = self._OnShowAppearance, caller = self, bUIOnly = false, bHideIcon = false,
				reddot = {
					{
						obType = EReddotTrieObserverType.Hero,
						key = string.format("NewUnlockCommerceItem_Customization%s", 1)
					},
				} },
			{ actionName = "ViewOperatorSkills", func = self._OnShowSkillPanel, caller = self, bUIOnly = false, bHideIcon = false },
			{ actionName = "OpenHeroProfile", func = self._OnOpenHeroProfile, caller = self, bUIOnly = false, bHideIcon = false,
				reddot = {
					-- {
					-- 	obType = EReddotTrieObserverType.Hero,
					-- 	key = string.format("NewUnlockArchive_Hero%s", curHeroUsedID)
					-- },
					{
						obType = EReddotTrieObserverType.Hero,
						key = string.format("CanTakeRewards_Hero%s", curHeroUsedID)
					},
					{
						obType = EReddotTrieObserverType.Hero,
						key = string.format("GeneralRedPoint_Hero%s", curHeroUsedID)
					}
				} },
			--BEGIN MODIFICATION @ VIRTUOS :
			--{actionName = "Common_ToggleTip", func = nil, caller = self, bUIOnly = true, bHideIcon = false},
			--{actionName = "Back_Gamepad", func = nil, caller = self, bUIOnly = true, bHideIcon = false},
			--END MODIFICATION
		}
		local isUnlock = Server.HeroServer:IsCanUseHero(curHeroUsedID)
		if not isUnlock then
			table.remove(summaryList, 4)

			local bIsRecruitable = HeroHelperTool.IsRecruitableHero(curHeroUsedID)
			if bIsRecruitable then
				table.insert(summaryList, { actionName = "OpenHeroRecruit", func = self._OpenHeroRecruitPanel, caller = self, bUIOnly = false, bHideIcon = false })
				-- BEGIN MODIFICATION @ VIRTUOS : 手柄专用输入
				table.insert(summaryList, { actionName = "OpenHeroRecruit_Gamepad", func = self._OpenHeroRecruitPanel, caller = self, bUIOnly = false, bHideIcon = false })
				-- END MODIFICATION
			end
		end

		--BEGIN MODIFICATION @ VIRTUOS :
		table.insert(summaryList, {actionName = "Common_ToggleTip", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
		table.insert(summaryList, {actionName = "Back_Gamepad", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
		--END MODIFICATION

		Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
	end
end

function HeroMainPanel:OnClose()
	UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "DeactivateSmokeEffects")

	if UHallLevelSequenceManager then
		UHallLevelSequenceManager.Get(GetWorld()):UnBindOnSeqStopped(self)
	end

	self:RemoveAllLuaEvent()

	if not VersionUtil.IsShipping() then
		-- 取消注册快捷键
		self:_UninitShortCuts()
	end
	-- self:_UnregisterSkillPanelShortCuts()
	--隐藏界面时，停止镜头淡入淡出
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade")

	Facade.UIManager:ClearSubUIByParent(self, self._wtHeroItemRoot)

	self:_GetBtnUiIns()


	if Facade.UIManager:GetIsLowMemoryState() then  -- 只在低内存设备卸载音频缓存池资源
		local UGPAudioStatics = import "GPAudioStatics"
		UGPAudioStatics.UnloadAllNonPlayingAudioEvents()
	end

end

-- 展示干员天赋信息
function HeroMainPanel:_ToggleExpertTalentInfo(bChecked)
	local armId = HeroHelperTool.GetHeroArmedForceId(self.curSeletcHeroId)
	local fCallback = function(panel)
		-- self._wtHeroChartPanel = panel
		-- self:_RefreshExpertInfoTips(armId)
		-- self._HeroTalentTipPanel:AddChild(self._wtHeroChartPanel)
		panel:AddCloseCallBack(function()
			self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Unchecked)
		end)
	end
	self:_IsJumpView(true)
	Facade.UIManager:AsyncShowUI(UIName2ID.HeroChartPanel, fCallback, nil, armId) --AsyncCreateSubUI
end

function HeroMainPanel:_PlayCloseAnim()
end

function HeroMainPanel:OnShowBegin()
	Server.HeroServer:SendHaroUnlockInfo()--拉取干员解锁数据

	self:_RegisterEvents()

	--清空干员解锁数据
	self:_IsJumpView(false)

	if self._quitHandle then
		Timer.CancelDelay(self._quitHandle)
	end
	local curHeroUsedID = Server.HeroServer:GetCurUsedHeroId()
	local heroIdStr = Module.Hero:GetCurShowHeroId()
	local lastUIID = Facade.UIManager:GetLastStackUIId()
	if lastUIID == UIName2ID.HeroCommercialMainPanel and self._toAppearancePanel == false then
		-- self:PlayWidgetAnim(self.Switch, 1, EUMGSequencePlayMode.Forward)
		-- self:PlayWidgetAnim(self.Switch)
		local bSelected = curHeroUsedID == tostring(heroIdStr)
		self._onOperateExpertEvent(tonumber(curHeroUsedID))
		if bSelected then
			self._wtHeroBuy:SetIsEnabledStyle(false)
			self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroMPUsingBtnText)

		else
			self._wtHeroBuy:SetIsEnabledStyle(true)
			self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroUseBtnText)
		end
		self:_RefreshHeroInfoByHeroId(Module.Hero:GetCurShowHeroId())
	end

	-- self:_RefreshHeroInfoByHeroId(Module.Hero:GetCurShowHeroId())

	self._wtHeroItemRoot:SelfHitTestInvisible()
	local curSelectedHeroId = self.curSeletcHeroId and self.curSeletcHeroId or curHeroUsedID
	self:SetFileBtnState(curSelectedHeroId)
	self:SetBottomBarActions(curSelectedHeroId)

	-- BEGIN MODIFICATION @ VIRTUOS :
	--绑定手柄按键
	self:_EnableExpertInfoGamepadInput(true)
	--绑定手柄导航
	self:_RegisterUINavigation()
	-- END MODIFICATION

	self._OprateBtnClickCDTimer = nil
end

function HeroMainPanel:OnShow()
	--跳转回调弹窗界面
	local unLockId = Module.Hero.Field:GetUnLockId()
	if unLockId then
		if not Server.HeroServer:IsCanUseHero(unLockId) then
			local unLockInfo, heroInfo = self:_GetHeroInfoData(unLockId)
			if unLockInfo and heroInfo then
				Module.Hero:ShowUnLockHeroPanel(heroInfo, unLockInfo)
			else
				loginfo("heroId get unLockInfo is nil")
			end
		end
	end

	if IsHD() then
		self:PlayAnimation(self.WBP_Hero_MainPanel_in_pc, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	else
		self:PlayAnimation(self.WBP_Hero_MainPanel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
	end
	self._wtMainContentPanel:SetRenderOpacity(1)
	self._wtHeroItemRoot:SetRenderOpacity(1) --保底措施

	local heroAppearanceButtonReddotKey = string.format("NewUnlockCommerceItem_Customization%s", 1)
	self._heroAppearanceButtonReddot = Module.ReddotTrie:RegisterStaticReddotDot(self._wtHeroAppearanceButton,
		{ { reddotData = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero,
			heroAppearanceButtonReddotKey) } })

	--定位首选的干员，优先展示的
	self:_RouterHeroList()

	--红点显示特殊逻辑
	Module.Hero:SetFirstEnterUsedHeroId(curHeroUsedID)
	Server.HeroServer.Events.evtOnCommerceReadStateChanged:Invoke()
	if self.curSeletcHeroId ~= Module.Hero:GetCurShowHeroId() then
		self:_RefreshHeroInfoByHeroId(self.curSeletcHeroId)
	end
	self._toAppearancePanel = false

	-- 当前没有遮罩的话，加一个遮罩。框架在当帧比较晚的时候设置的，得延迟一帧才能生效
	local delayRefreshMask = function()
		local displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallHero)
		if displayCtrlActor then
			displayCtrlActor.HallExpertLevelSequenceComponent:SetCameraFadeIfCurDisableRender()
		end
	end
	Timer.DelayCall(0, delayRefreshMask)
end

function HeroMainPanel:_HideMainPanel()
	self._wtLeftContentPanel:Collapsed()
	self._wtRightContentPanel:Collapsed()
end

function HeroMainPanel:_ShowMainPanel()
	self._wtHeroSkillDetailPanel:PlayHideAnim()
	self._wtLeftContentPanel:Visible()
	self._wtRightContentPanel:Visible()

end

function HeroMainPanel:OnHide()

	-- if IsHD() then
	-- 	self:PlayWidgetAnim(self.WBP_Hero_MainPanel_out_pc, 1, EUMGSequencePlayMode.Forward,1.0, false)
	-- else
	-- 	self:PlayWidgetAnim(self.WBP_Hero_MainPanel_out, 1, EUMGSequencePlayMode.Forward,1.0, false)
	-- end
	if not self:_IsJumpView() then
		Module.Hero:ResetShowHeroId()
	end

	self:UnReigsterHeroLineReddot()
	--隐藏界面时，停止镜头淡入淡出
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade")

	Module.ReddotTrie:UnRegisterStaticReddotDot(self._heroAppearanceButtonReddot)

end

function HeroMainPanel:OnHideBegin()
	self:_UnregisterEvents()
	Server.HeroServer:SendHaroUnlockInfo()--拉取干员解锁数据
	self._wtHeroItemRoot:Collapsed()

	-- BEGIN MODIFICATION @ VIRTUOS :
	--移除手柄快捷键
	self:_EnableExpertInfoGamepadInput(false)
	--移除手柄导航设置
	self:_RemoveUINavigation()
	-- END MODIFICATION

end

function HeroMainPanel:OnInitExtraData()
end

--endregion
--==================================================


--==================================================
--region Public API

function HeroMainPanel:OnSubViewChanged(subViewType)
	if subViewType == Module.Hero.Config.ESubViewType.HeroMain then
		if not self._subType or self._subType == Module.Hero.Config.ESubViewType.AssemblyMain then
			self:PlayWidgetAnim(self.peizhuang_out_zhuanjia_in)
		elseif self._subType == Module.Hero.Config.ESubViewType.HeroAppearance then
			self:PlayWidgetAnim(self.shizhuang_out_zhuanjia_in)
		else
			-- self:PlayWidgetAnim(self.WBP_Hero_MainPanel_in)
		end
		self._subType = subViewType
	elseif subViewType == Module.Hero.Config.ESubViewType.HeroAppearance then
		self._subType = subViewType
	elseif subViewType == Module.Hero.Config.ESubViewType.AssemblyMain then
		self._subType = subViewType
	end
end

-- pc Hovered mobile clicked
-- SetCharacterSelected
function HeroMainPanel:SetSelectedHeroCell(uins)
	if self._selectHeroCell then
		self._selectHeroCell:OnUnSelect()
	end

	self._selectHeroCell = uins
	self._selectHeroCell:OnSelect()
	self.curSeletcHeroId = uins:GetHeroId()
	local heroInfo = HeroDataTable[tostring(self.curSeletcHeroId)]
	self:_RefreshHeroInfoByHeroId(self.curSeletcHeroId)
	uins:SetSelectedExpertIcon(heroInfo.ArmedForceId)
	if not self:IsAnimationPlaying(IsHD() and self.WBP_Hero_MainPanel_in_pc or self.WBP_Hero_MainPanel_in) then
		self:PlayAnimation(self.Switch, 0, 1, EUMGSequencePlayMode.Forward, 1.0, false)
	end

	if IsHD() then
		WidgetUtil.FocusWidgetByProxy(self._wtNavGroup, {self.curSeletcHeroId}, false)
	end
end

function HeroMainPanel:SetFileBtnState(curHeroUsedID)
	if not curHeroUsedID then return end
	local curHeroID = tonumber(curHeroUsedID)
	if DFHD_LUA == 0 then
		if Server.HeroServer:IsCanUseHero(curHeroID) then --只有手游显示
			self._wtHeroDetailBtn:SetIsEnabledStyle(true)
		else
			self._wtHeroDetailBtn:SetIsEnabledStyle(false)
		end
	else
		self._wtHeroDetailBtn:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function HeroMainPanel:SetOperatedHeroCell(uins)
	if self._operateHeroCell then
		self._operateHeroCell:OnUnSelect()
	end

	self._selectHeroCell = uins
	self._selectHeroCell:OnSelect()
	self.curSeletcHeroId = uins:GetHeroId()
	self:_RefreshHeroInfoByHeroId(self.curSeletcHeroId)

	if IsHD() then
		WidgetUtil.FocusWidgetByProxy(self._wtNavGroup, {self.curSeletcHeroId}, false)
	end
end

--endregion
--==================================================


--==================================================
--region Private API

function HeroMainPanel:_RefreshHeroInfoByHeroId(heroId)
	heroId = setdefault(heroId, 0)
	local heroInfo = HeroDataTable[tostring(heroId)]
	if heroInfo then

		Module.Hero:SetCurShowHeroById(heroId)

		self._wtHeroTitleNameText:SetText(heroInfo.Title)
		self._wtHeroNameBackward:SetText(heroInfo.Name)
		self._wtHeroNameMiddle:SetText(heroInfo.Name)
		self._wtHeroName:SetText(heroInfo.Name)
		-- self._wtHeroFXNameText:SetText(heroInfo.Name)
		self._wtImageIcon:AsyncSetImagePath(heroInfo.ExpertIcon, false)
		self._wtImageIcon:SetColorAndOpacity(Module.Hero.Config.ExpertIconColorMapping[heroInfo.ArmedForceId])
		-- self._wtImageIconBG:AsyncSetImagePath(heroInfo.BackgroundIcon)

		self:_RefreshSkillInfo(heroId)
		self:_RefreshHeroOperate(heroId)
		self:_RefreshHeroItems(heroId)
		self:_RouterHeroList()
		self:RegisterHeroLineReddot(heroId)
		-- 设置角色模型
		self:_OnSwitchHeroDisplayStage(heroId)

		self._wtHeroTitleNameText:SetColorAndOpacity(Module.Hero.Config.ExpertNameColorMapping[heroInfo.ArmedForceID])

		self:_SetExpertSceneAttrs(heroInfo)

		Module.Hero.Config.Events.evtOnHeroItemClicked:Invoke(heroId)

		local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
		LogAnalysisTool.DoSendHeroSelectedReport(armedForceMode, tonumber(heroId))

		-- ---招募干员按钮显示和隐藏
		-- if not IsHD() then
		-- 	if HeroHelperTool.IsRecruitableHero(heroId) then
		-- 		self._wtApproachButton:Visible()
		-- 	else
		-- 		self._wtApproachButton:Collapsed()
		-- 	end
		-- end
	end
end

function HeroMainPanel:_SetExpertSceneAttrs(heroInfo)

	local heroFashionSuitIdStr = tostring(Server.HeroServer:GetFashionSuitIdByHeroId(heroInfo.HeroId)) --Module.Hero:GetCurShowHeroFashionSuitId()
	--播放角色seq
	Module.Hero:PlayCharacterAnimSeqByFashionId(self.curSeletcHeroId, heroFashionSuitIdStr, false, "None")
	logerror("[echewzhu]HeroMainPanel:_SetExpertSceneAttrs : heroFashionSuitIdStr = "..heroFashionSuitIdStr)
	self:_RefreshSkillIconColor(heroInfo)


	-- UDFMLightCoordinateSubsystem.Get(GetWorld()):ActivateLightGroupOnly(Module.Hero.Config.ExpertLightGroupNameMapping[heroInfo.ArmedForceID])
end

function HeroMainPanel:_RefreshHeroItems(heroId)

	self:_InitSkillAndTalentIntro(heroId)
end

function HeroMainPanel:_RefreshSkillIconColor(heroInfo)
	-- self._wtSkillIcon2:SetIconColor(Module.Hero.Config.ExpertIconColorMapping[heroInfo.ArmedForceID])
	self._wtSkillIcon2:SetTextColor(Module.Hero.Config.ExpertNameColorMapping[heroInfo.ArmedForceID])
end

function HeroMainPanel:_RefreshSkillInfo(heroId)
	local rawData = Server.HeroServer:GetCurModeExpertDataByHeroId(heroId)
	-- local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtSkillTips)

	if rawData then
		local fSkillIconClickCallback = function(inSkillId, Idx)
			-- local fCallback = function(panel)
			-- 	self._wtSkillTips = panel
			-- 	-- self._HeroTipsPanel:AddChild(self._wtSkillTips)
			-- 	self._wtSkillTips:SetSkillTipInfo(inSkillId)
			-- 	local commonAnchor = FAnchors()
			-- 	commonAnchor.Minimum = LuaGlobalConst.TOP_RIGHT_VECTOR
			-- 	-- commonAnchor.Maximum = LuaGlobalConst.TOP_RIGHT_VECTOR
			-- 	self._SkillTipsSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtSkillTips)
			-- 	self._SkillTipsSlot:SetAnchors(commonAnchor)


			-- 	if Idx == 2 then
			-- 		self._SkillTipsSlot:SetPosition(self._NormalTipPosition)
			-- 		self:_OnShowIdxChanged(Idx)
			-- 	else
			-- 		self._wtSkillTips:SetIconColor(FLinearColor("FFFFFFFF"))
			-- 		self._SkillTipsSlot:SetPosition(self._BottomTipPosition)
			-- 		self:_OnShowIdxChanged(Idx)
			-- 	end
			-- 	self._SkillTipsSlot:SetSize(FVector2D(658, 290))
			-- end
			-- if self._wtSkillTips == nil then
			-- else

			-- 	self._wtSkillTips:SetSkillTipInfo(inSkillId)
			-- 	if Idx == 2 then
			-- 		local heroInfo = HeroDataTable[tostring(heroId)]
			-- 		self._wtSkillTips:SetIconColor(Module.Hero.Config.ExpertIconColorMapping[heroInfo.ArmedForceID])
			-- 		self._SkillTipsSlot:SetPosition(self._NormalTipPosition)
			-- 		self:_OnShowIdxChanged(Idx)
			-- 	else
			-- 		self._wtSkillTips:SetIconColor(FLinearColor("#FFFFFFFF"))
			-- 		self._SkillTipsSlot:SetPosition(self._BottomTipPosition)
			-- 		self:_OnShowIdxChanged(Idx)
			-- 	end
			-- end
		end

		local PassiveSkillId = self._PassiveSkillFunc(heroId)
		self._wtSkillIcon1:SetExtraData(PassiveSkillId, function()
			fSkillIconClickCallback(PassiveSkillId, 1)
		end)
		self._wtSkillIcon1:SetSkillText(SkillHelperTool.GetSkillNameById(PassiveSkillId),
			SkillHelperTool.GetSkillBuffNameById(PassiveSkillId))

		local UltimateSkillId = self._UltimateSkillFunc(heroId)
		self._wtSkillIcon2:SetExtraData(UltimateSkillId, function()
			fSkillIconClickCallback(UltimateSkillId, 2)
		end)
		self._wtSkillIcon2:SetSkillText(SkillHelperTool.GetSkillNameById(UltimateSkillId),
			SkillHelperTool.GetSkillBuffNameById(UltimateSkillId))
	end
end

function HeroMainPanel:_InitSkillAndTalentIntro(heroId)
	local rawData = Server.HeroServer:GetCurModeExpertDataByHeroId(heroId)

	if rawData then
		local fCallbackfunction = function(SkillId, Idx)
			-- local fCallback = function (panel)
			-- 	self._wtSkillTips = panel
			-- 	-- self._HeroTipsPanel:AddChild(self._wtSkillTips)
			-- 	self._wtSkillTips:SetSkillTipInfo(SkillId)
			-- 	self._SkillTipsSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtSkillTips)
			-- 	self:_OnShowIdxChanged(Idx)
			-- 	local commonAnchor = FAnchors()
			-- 	-- commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
			-- 	commonAnchor.Minimum = LuaGlobalConst.TOP_RIGHT_VECTOR
			-- 	self._SkillTipsSlot:SetAnchors(commonAnchor)
			-- 	self._SkillTipsSlot:SetPosition(self._NormalTipPosition)
			-- 	self._SkillTipsSlot:SetSize(FVector2D(658, 290))
			-- end
			-- if self._wtSkillTips == nil then
			-- else
			-- 	self:_OnShowIdxChanged(Idx)
			-- 	self:_OnHeroItemSelected(SkillId)
			-- 	self._SkillTipsSlot:SetPosition(self._NormalTipPosition)
			-- end
		end
		local SkillId = self._SupportSkillFunc(heroId)

		self._wtHeroPropItem1:SetExtraData(SkillId, function()
			fCallbackfunction(SkillId, 3)
		end)

		self._wtHeroPropItem1:SetSkillText(SkillHelperTool.GetSkillNameById(SkillId),
			SkillHelperTool.GetSkillBuffNameById(SkillId))

		local SkillId2 = self._ActiveSkillFunc(heroId)

		self._wtHeroPropItem2:SetExtraData(SkillId2, function()
			fCallbackfunction(SkillId2, 4)
		end)

		self._wtHeroPropItem2:SetSkillText(SkillHelperTool.GetSkillNameById(SkillId2),
			SkillHelperTool.GetSkillBuffNameById(SkillId2))
	end
end

function HeroMainPanel:_GetHeroInfoData(heroId)
	heroId = tonumber(heroId)
	if heroId == nil then
		loginfo("heroId is empty, get data failed")
		return
	end
	local InfoTable = HeroLogic.GetHeroUnlockInfo()
	--活动临时变量
	local isBool = false
	--整理解锁数据
	local unLockInfo = {}
	for key, value in pairs(InfoTable or {}) do
		if value and heroId == value.HeroId then
			local list = {}
			if value.GetType == 1 then
				local Goods = Server.StoreServer:GetMallGiftDataByGoodsID(tonumber(value.Parameter))
				if Goods then
					list.CurrencyType = Goods.CurrencyType
					list.Price = Goods.Price
				end
			end
			if value.GetType == 3 then
				list.IsBool = Server.BattlePassServer:IsRewardIDExist(heroId)
				if list.IsBool then
					local desc = self:_GetBPUnLockDesc()
					if desc then
						list.IsBool = true
						list.Desc = desc
					end
				end
			end
			if value.GetType == 6 then
				isBool = Server.ActivityServer:CheckActivityExistsByID(tonumber(value.Parameter))
				list.IsBool = isBool
			end
			if value.GetType == 7 then
				list.IsBool = HeroHelperTool.IsRecruitableHero(value.HeroId)
			end
			list.HeroId = value.HeroId
			list.GetType = value.GetType
			list.Parameter = tostring(value.Parameter)
			list.Name = value.Name
			table.insert(unLockInfo, list)
		end
	end
	--重新确定数据
	for index, value in ipairs(unLockInfo or {}) do
		if value.GetType == 2 then
			value.IsBool = isBool
			break
		end
	end
	--重排序
	table.sort(unLockInfo, function(a, b)
		if a.GetType < b.GetType then
			return true
		end
		return false
	end)

	local heroInfo = HeroDataTable[tostring(heroId)]

	return unLockInfo, heroInfo
end

function HeroMainPanel:_GetBtnClickCallback(btnData, heroId)
	--购买/活动/通行证
	self:_IsJumpView(true)
	if btnData and btnData.GetType == 1 then
		local heroInfo = HeroDataTable[tostring(heroId)]
		Module.Hero:ShowUnLockHeroPanel(heroInfo, heroData)
	elseif btnData and btnData.GetType == 2 and btnData.Parameter then
		local jumpId = tonumber(btnData.Parameter)
		if jumpId then
			Module.Jump:JumpByID(jumpId)
		else
			loginfo("Jump activity failed, JumpId judged as empty")
		end
	elseif btnData and btnData.GetType == 3 and btnData.Parameter then
		local jumpId = tonumber(btnData.Parameter)
		if jumpId then
			Module.Jump:JumpByID(jumpId)
		else
			loginfo("Jump pass failed, JumpId judged as empty")
		end
	else
		loginfo("Failed to execute function jump, type judgment error")
	end
end

function HeroMainPanel:_GetBPUnLockDesc()
	local id = SwitchModuleID.ModuleBattlePass
	local info = Server.ModuleUnlockServer:GetModuleUnlockInfoById(id)
	local desc = nil
	if info and info.bIsUnlocked ~= true then
		if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
			if info.unlock1Condition then
				desc = string.format(Module.Hero.Config.Loc.HeroSOLUnlock, info.unlock1Condition)
			end
		else
			if info.unlock2Condition then
				desc = string.format(Module.Hero.Config.Loc.HeroMPUnlock, info.unlock2Condition)
			end
		end
	end
	return desc
end

function HeroMainPanel:_RefreshHeroOperate(heroId)
	--清除容器新增按钮项
	self:_GetBtnUiIns(0)

	if Server.HeroServer:IsCanUseHero(heroId) then
		-- self._wtHeroPricePanel:SetVisibility(ESlateVisibility.Collapsed)
		local bSelected = Server.HeroServer:GetCurUsedHeroId() == tostring(heroId)

		if bSelected then
			-- self._wtHeroUsingPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
			self._wtHeroBuy:SetIsEnabledStyle(false)
			self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroMPUsingBtnText)
			self._onOperateExpertEvent(heroId)
		else
			self._wtHeroBuy:SetIsEnabledStyle(true)
			self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroUseBtnText)

			self._wtHeroOperateBtnClickCallback = function()
				Module.Hero:UseHeroById(heroId)
				---------------------------------------------------------------------------------------------------*****************************************************
				self._onOperateExpertEvent(heroId)
			end
		end
	else
		--刷新未解锁干员相关按钮显示
		self:_GetBtnUiIns(0)
		local unLockInfo, heroInfo = self:_GetHeroInfoData(heroId)
		if unLockInfo then
			local btnData = self:_SetBtnText(unLockInfo)
			if btnData then
				--按钮跳转设置
				self._wtHeroOperateBtnClickCallback = function()
					--购买/活动/通行证
					if btnData == nil then
						return
					end
					self:_IsJumpView(true)

					if btnData.GetType == 1 then
						Module.Hero:ShowUnLockHeroPanel(heroInfo, unLockInfo)
					else
						Module.Jump:JumpByID(tonumber(btnData.Parameter or 0))
					end

					loginfo("hero jump name is ", btnData.Name, ", jump id is ", btnData.Parameter)
				end
			else
				loginfo("BtnData data is empty, jump function setting failed")
			end
		else
			loginfo("heroId get unLockInfo is nil")
		end
	end
end

function HeroMainPanel:_SetBtnText(heroInfo)
	local itemData1 = nil
	local itemData2 = nil
	local LimitedTimeTxt = nil
	for index, value in ipairs(heroInfo or {}) do
		if value and value.GetType == 2 and value.Name then
			LimitedTimeTxt = value.Name
		end
		if value and value.GetType == 1 and value.CurrencyType and value.Price then
			--是购买就显示金币
			local type = Module.Hero.Config.Loc.HeroPrice
			local gold = Module.Currency:GetRichTxtImgByItemId(value.CurrencyType)
			local price = MathUtil.GetNumberFormatStr(value.Price)
			self._wtHeroBuy:SetIsEnabledStyle(true)
			self._wtHeroBuy:BP_SetMainTitle(string.format(type, gold, price))
			itemData1 = value
			break
		elseif value and value.GetType == 2 and value.IsBool and value.Name then
			self._wtHeroBuy:SetIsEnabledStyle(true)
			self._wtHeroBuy:BP_SetMainTitle(value.Name)
			itemData1 = value
			break
		elseif value and value.GetType == 3 and value.Name and value.IsBool then
			if value.Desc then
				self._wtHeroBuy:SetIsEnabledStyle(false)
				value.IsShow = true
			else
				self._wtHeroBuy:SetIsEnabledStyle(true)
				value.IsShow = false
			end
			self._wtHeroBuy:BP_SetMainTitle(value.Name)
			itemData1 = value
			break
		elseif value and value.GetType == 7 and value.Name and value.IsBool then
			self._wtHeroBuy:SetIsEnabledStyle(true)
			self._wtHeroBuy:BP_SetMainTitle(value.Name)
			itemData1 = value
			break
		end
	end
	self._tipTxt = nil
	if itemData1 then
		for index, value in ipairs(heroInfo or {}) do
			local isGreater = false
			if itemData1.GetType == 3 and value.IsShow then
				isGreater = itemData1.GetType <= value.GetType
			else
				isGreater = itemData1.GetType < value.GetType
			end
			if value and isGreater then
				if value.GetType == 2 and value.IsBool and value.Name then
					local btn = self:_GetBtnUiIns(1)
					if btn then
						btn:SetMainTitle(value.Name)
						btn:BindJumpClick(self._OnBtnJump, self, value.Parameter)
					else
						loginfo("Failed to obtain the first button control")
					end
					itemData2 = value
					break
				elseif value.GetType == 3 and value.Name and value.IsBool then
					local btn = nil
					if value.Desc then
						btn = self:_GetBtnUiIns(2)
						self._tipTxt = btn
					else
						btn = self:_GetBtnUiIns(1)
					end
					if btn then
						if value.Desc then
							btn:SetType(0)
							btn:SetMainTitle(value.Desc)
						else
							btn:SetMainTitle(value.Name)
							btn:BindJumpClick(self._OnBtnJump, self, value.Parameter)
						end
					end
					itemData2 = value
					break
				elseif value.GetType == 7 and value.Name and value.IsBool then
					local btn = self:_GetBtnUiIns(1)
					if btn then
						btn:SetMainTitle(value.Name)
						btn:BindJumpClick(self._OnApproachButtonClick, self)
					end
					itemData2 = value
					break
				end
			end
		end
	else
		--没有数据，显示未解锁
		self._wtHeroBuy:SetIsEnabledStyle(false)
		self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroIsLock)
		if LimitedTimeTxt then
			local btn = self:_GetBtnUiIns(2)
			if btn then
				btn:SetType(0)
				btn:SetMainTitle(LimitedTimeTxt)
			else
				loginfo("Failed to obtain the second button control")
			end
		end
	end
	--临时提示
	self:_SetBtnTipDesc(itemData1, itemData2)

	return itemData1, itemData2
end

function HeroMainPanel:_SetBtnTipDesc(itemData1, itemData2)
    if itemData1 == nil then
        return
    end
    local desc1, desc2
    if itemData1.GetType == 1 then
        desc1 = Module.Hero.Config.Loc.HeroGetBtnText
        desc2 = itemData1.Name
    else
        desc1 = Module.Hero.Config.Loc.HeroGoToObtain
        desc2 = itemData1.Name
		--只有通行证会出现未解锁
		if itemData1.GetType == 3 and itemData1.Desc then
			desc1 = Module.Hero.Config.Loc.HeroIsLock
			desc2 = itemData1.Name
		end
    end
    if itemData2 then
        desc2 = nil
    end
    if desc1 then
        self._wtHeroBuy:SetMainTitle(desc1)
    end
    if desc2 then
		if self._tipTxt == nil then
			self._tipTxt = self:_GetBtnUiIns(2)
		end
		self._tipTxt:SetType(0)
		self._tipTxt:SetMainTitle(desc2)
		self._tipTxt = nil
    end
end

function HeroMainPanel:_OnBtnJump(id)
	self:_IsJumpView(true)
	local jumpId = tonumber(id)
	if jumpId then
		Module.Jump:JumpByID(jumpId)
	else
		loginfo("Mini button id is empty, jump failed")
	end
end

--按钮对象池(获取/隐藏/清除)
function HeroMainPanel:_GetBtnUiIns(wedgetType)
	if self._listWedget == nil then
		self._listWedget = {}
	end
	local uiIns, instanceID
	if wedgetType == nil then
		Facade.UIManager:ClearSubUIByParent(self, self._wtHeroBtnPanel)
		self._listWedget = nil
	elseif wedgetType == 0 then
		for key, value in pairs(self._listWedget or {}) do
			if key == 1 then
				Facade.UIManager:RemoveSubUI(self, UIName2ID.HeroApproach, value)
			else
				Facade.UIManager:RemoveSubUI(self, UIName2ID.AssemblyTips, value)
			end
		end
		self._listWedget = {}
	elseif wedgetType == 1 then
		uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.HeroApproach, self._wtHeroBtnPanel)
	elseif wedgetType == 2 then
		uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.AssemblyTips, self._wtHeroBtnPanel)
	end
	if uiIns and instanceID then
		local btn = getfromweak(uiIns)
		if btn and self._listWedget then
			self._listWedget[wedgetType] = instanceID
			return btn
		end
	end
	return nil
end

-- 场景加载完成后的回调
function HeroMainPanel:_OnEnterExpertLevel(curSubStageType)
	local lastUIID = Facade.UIManager:GetLastStackUIId()
	if lastUIID == UIName2ID.HeroAppearancePropPanel then
		return
	end
	if curSubStageType == ESubStage.HallHero then
		local heroIdStr = nil--Module.Hero:GetCurShowHeroId()
		if heroIdStr == nil then
			--heroIdStr = Server.HeroServer:GetCurUsedHeroId()
			heroIdStr = Module.Hero:GetCurShowHeroId()
		end

		local heroFashionSuitIdStr = tostring(Server.HeroServer:GetCurUsedHeroFashionId())
		if self._curHeroFashionID ~= heroFashionSuitIdStr then
			self._curHeroFashionID = heroFashionSuitIdStr
		end

		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterRotation")
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraDisplayType", "Character", self._curHeroFashionID
			, true)
		for _, uins in pairs(self._allHeroItems) do
			if tostring(uins:GetHeroId()) == heroIdStr then
				if self._selectHeroCell then
					self._selectHeroCell:OnUnSelect()
				end

				self._selectHeroCell = uins
				self._selectHeroCell:OnSelect()
				self.curSeletcHeroId = uins:GetHeroId()
				self:SetFileBtnState(self.curSeletcHeroId)
				local heroTableInfo = HeroDataTable[tostring(self.curSeletcHeroId)]
				uins:SetSelectedExpertIcon(heroTableInfo.ArmedForceId)
				if IsHD() then
					WidgetUtil.FocusWidgetByProxy(self._wtNavGroup, {self.curSeletcHeroId}, false)
				end

				break
			end
		end
		self:_OnPanelOpen()

		if self.curSeletcHeroId then
			self:_RefreshHeroInfoByHeroId(self.curSeletcHeroId)
		end
	end
end

function HeroMainPanel:_OnPanelOpen()
	local heroIdStr = Module.Hero:GetCurShowHeroId()

	local curHeroUsedID = Server.HeroServer:GetCurUsedHeroId()
	local heroInfo = HeroDataTable[tostring(curHeroUsedID)]
	if heroInfo == nil then
		return
	end
	-- self._wtExpertMainNameText:SetText(heroInfo.Name)
	-- local TitleName = string.format("%s - %s",HeroHelperTool.GetExpertTypeName(heroInfo.ArmedForceId), heroInfo.Title)
	self._wtHeroTitleNameText:SetText(heroInfo.Title)
	-- self._wtHeroNameIcon:AsyncSetImagePath(heroInfo.CodeNameImage)
	-- self._wtHeroNameIconfx:AsyncSetImagePath(heroInfo.CodeNameImage)
	self._wtHeroTitleNameText:SetColorAndOpacity(Module.Hero.Config.ExpertNameColorMapping[heroInfo.ArmedForceID])
	-- self._wtHeroFXNameText:SetText(heroInfo.Name)
	self._wtHeroNameBackward:SetText(heroInfo.Name)
	self._wtHeroNameMiddle:SetText(heroInfo.Name)
	self._wtHeroName:SetText(heroInfo.Name)
	self._wtImageIcon:AsyncSetImagePath(heroInfo.ExpertIcon, false)
	self._wtImageIcon:SetColorAndOpacity(Module.Hero.Config.ExpertIconColorMapping[heroInfo.ArmedForceId])
	-- self._wtImageIconBG:AsyncSetImagePath(heroInfo.BackgroundIcon)
	self:_RefreshSkillInfo(curHeroUsedID)
	self:_InitSkillAndTalentIntro(curHeroUsedID)
	-- self:_SetExpertSceneAttrs(heroInfo)
	-- self:PlayWidgetAnim(self.Switch, 1, EUMGSequencePlayMode.Forward, 1.0)
	local bSelected = curHeroUsedID == tostring(heroIdStr)
	-- self._onOperateExpertEvent(tonumber(curHeroUsedID))
	if bSelected then
		self._wtHeroBuy:SetIsEnabledStyle(false)
		self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroMPUsingBtnText)

	else
		self._wtHeroBuy:SetIsEnabledStyle(true)
		self._wtHeroBuy:BP_SetMainTitle(Module.Hero.Config.Loc.HeroUseBtnText)
	end
end

-- 刷新ExpertTipsInfo
function HeroMainPanel:_RefreshExpertInfoTips(ArmId)

	self._wtHeroChartPanel:SetTalentTipInfo(ArmId)
end

function HeroMainPanel:_RegisterSkillPanelShortCuts() --SetBottomBarTempInputSummaryList
	local checkHeroId = self._hoverHeroId and self._hoverHeroId or Server.HeroServer:GetCurUsedHeroId()
	Module.CommonBar:SetBottomBarTempInputSummaryList({
		{ actionName = "SkillShow", func = self._OnShowSkillByMedia, caller = self, bUIOnly = false, bHideIcon = false },
		{ actionName = "Back", func = self._OnHideSkillPanel, caller = self, bUIOnly = false, bHideIcon = false },
		{ actionName = "OpenHeroProfile", func = self._OnOpenHeroProfile, caller = self, bUIOnly = false, bHideIcon = false,
			reddot = {
				-- {
				-- 	obType = EReddotTrieObserverType.Hero,
				-- 	key = string.format("NewUnlockArchive_Hero%s", checkHeroId)
				-- },
				{
					obType = EReddotTrieObserverType.Hero,
					key = string.format("CanTakeRewards_Hero%s", checkHeroId)
				},
				{
					obType = EReddotTrieObserverType.Hero,
					key = string.format("GeneralRedPoint_Hero%s", checkHeroId)
				}
			} },
	}, true, false)

end

function HeroMainPanel:_UnregisterSkillPanelShortCuts()
	-- Module.CommonBar:RecoverBottomBarInputSummaryList()
	self:SetBottomBarActions()
end

function HeroMainPanel:SwitchBottomBarState(state)
	if self:IsInHideBeginState() or self:IsInHideState() then
		return
	end
	if self.curBottomBarState == state then
		return
	end

	-- BEGIN MODIFICATION @ VIRTUOS
	-- 聚焦在Hero时用手柄切换Tab后会触发MouseLeave事件，错误地在页面隐藏时仍在更新
	if WidgetUtil.IsGamepad() and (not self:IsVisible()) then
		return
	end
	-- END MODIFICATION

	self.curBottomBarState = state
	if state == BottomBarState.Hovering then

		local defActionName = "UnlockHero"
		-- if WidgetUtil.IsGamepad() then
		-- 	defActionName = "Unlock"
		-- end
		if self._hoverHeroId then
			local summaryList = {}
			local isUnlock = Server.HeroServer:IsCanUseHero(self._hoverHeroId)
			if isUnlock then
				defActionName = "SelectHero"
			end
			summaryList = {
				--设为出战
				{ actionName = defActionName, func = self._OnSelectHero, caller = self, bUIOnly = false, bHideIcon = false, disable = true},
				--自定义
				{ actionName = "ShowAppearance", func = self._OnShowAppearance, caller = self, bUIOnly = false, bHideIcon = false,
					reddot = {
						{
							obType = EReddotTrieObserverType.Hero,
							key = string.format("NewUnlockCommerceItem_Customization%s", 1)
						},
					} },
				--查看能力
				{ actionName = "ViewOperatorSkills", func = self._OnShowSkillPanel, caller = self, bUIOnly = false, bHideIcon = false },
				--干员档案
				{ actionName = "OpenHeroProfile", func = self._OnOpenHeroProfile, caller = self, bUIOnly = false, bHideIcon = false,
					reddot = {
						-- {
						-- 	obType = EReddotTrieObserverType.Hero,
						-- 	key = string.format("NewUnlockArchive_Hero%s", self._hoverHeroId)
						-- },
						{
							obType = EReddotTrieObserverType.Hero,
							key = string.format("CanTakeRewards_Hero%s", self._hoverHeroId)
						},
						{
							obType = EReddotTrieObserverType.Hero,
							key = string.format("GeneralRedPoint_Hero%s", self._hoverHeroId)
						}
					} },
					--BEGIN MODIFICATION @ VIRTUOS :
					{actionName = "Common_ToggleTip", func = nil, caller = self, bUIOnly = true, bHideIcon = false},
					{actionName = "Back_Gamepad", func = nil, caller = self, bUIOnly = true, bHideIcon = false},
					--END MODIFICATION
			}
			if not isUnlock then
				table.remove(summaryList, 4) -- 没解锁不看干员档案

				--未解锁可招募显示招募
				local bIsRecruitable = HeroHelperTool.IsRecruitableHero(self._hoverHeroId)
				if bIsRecruitable then
					table.insert(summaryList, { actionName = "OpenHeroRecruit", func = self._OpenHeroRecruitPanel, caller = self, bUIOnly = false, bHideIcon = false })
					-- BEGIN MODIFICATION @ VIRTUOS : 手柄专用输入
					table.insert(summaryList, { actionName = "OpenHeroRecruit_Gamepad", func = self._OpenHeroRecruitPanel, caller = self, bUIOnly = false, bHideIcon = false })
					-- END MODIFICATION
				end
			end
			Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
		end
		self.bShowSkillPanel = false
	end
	if state == BottomBarState.Common then
		if self.bShowSkillPanel == false then
			-- Module.CommonBar:RecoverBottomBarInputSummaryList()
			self:SetBottomBarActions()
		end
		-- Module.CommonBar:SetBottomBarTempInputSummaryList({
		--     {}
		-- }, false)
	end
	if state == BottomBarState.Lock then
		Module.CommonBar:SetBottomBarTempInputSummaryList({
			{ actionName = "ShowLock", func = self._OnShowAppearance, caller = self, bUIOnly = true, bHideIcon = false }
		}, false, false)
	end
	if state == BottomBarState.Quit then
		-- Module.CommonBar:RecoverBottomBarInputSummaryList()
		self:SetBottomBarActions()
		-- Module.CommonBar:SetBottomBarInputSummaryList({}, false)
	end
end

-------------------------------------------------------------------------------------
function HeroMainPanel:_OnDetailHeroBtnClick()
end

function HeroMainPanel:_RefreshExpertScene(heroInfo)
	if heroInfo then
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetSceneEffectColorOverLife", "HeroSmoke_Color02",
			"Smoke_Color", Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceID])
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetSceneEffectColorOverLife", "HeroSmoke_Color01",
			"HeroSmoke_Color01", Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceID])
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetSceneEffectColorOverLife", "Glow_Color", "Glow_Color",
			Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceID])
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetSceneEffectColorOverLife", "Number_Color",
			"Number_Color", Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceID])
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetSceneEffectColorOverLife", "Glow_Color1", "Glow_Color"
			, Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceID])
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetSceneEffectColorOverLife", "Number_Color1",
			"Number_Color", Module.Hero.Config.ExpertSceneEffectColor[heroInfo.ArmedForceID])
	end
end

function HeroMainPanel:_OnFashionBtnClick()

	if self._toAppearancePanel then
		logwarning("HeroMainPanel:_OnFashionBtnClick return click")
		return
	end
	--记录跳转,清理状态
	self:_IsJumpView(true)
	self.curBottomBarState = nil
	Module.Hero:ShowHeroCommercialPanel()
	LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.EnterCustom)
	self._toAppearancePanel = true
end

local EHallMainDisplayType = import "EHallMainDisplayType"
function HeroMainPanel:_OnZoomBtnClick()
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.Hero)

	Module.CommonBar:BindBackHandler(self._OnCloseModelView, self)
	Module.Hero.Config.Events.evtMainContentPanelShowChanged:Invoke(false)
	self._wtHeroFashionDesc:SetVisibility(ESlateVisibility.Visible)
end

function HeroMainPanel:_OnCloseModelView()
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.Fashion)

	Module.Hero.Config.Events.evtMainContentPanelShowChanged:Invoke(true)
	self._wtHeroFashionDesc:SetVisibility(ESlateVisibility.Collapsed)
end

function HeroMainPanel:_OnCloseFashionView()
	if self._wtHeroAppearancePanel then
		self._wtHeroAppearancePanel:HidePanel()
	end
end

--出战
function HeroMainPanel:_OnHeorOperateBtnClick()
	if self._OprateBtnClickCDTimer then
		return
	end
	self._OprateBtnClickCDTimer = Timer.DelayCall(OPERATE_BTN_CLICK_CD, function()
		if self then
			self._OprateBtnClickCDTimer = nil
		end

	end)
	if self._wtHeroOperateBtnClickCallback then
		self._wtHeroOperateBtnClickCallback()
		LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.SetToFight)
	end
end

function HeroMainPanel:_OnSelectedHeroIdChange()
	self:_RefreshHeroOperate(self.curSeletcHeroId)
	if self._onOperateExpertEvent then
		self._onOperateExpertEvent(self.curSeletcHeroId)
	end

	local heroInfo = HeroDataTable[tostring(self.curSeletcHeroId)]
	if heroInfo then
		Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.Hero.Config.Loc.UseHeroChangedTips,{heroName = heroInfo.Name}))
	end
end

function HeroMainPanel:_OnHeroArmedPropChanged(changedHeroId)
	local heroId = Module.Hero:GetCurShowHeroId()
	if heroId == changedHeroId then
		self:_RefreshHeroInfoByHeroId(heroId)
	end
end

-- 设置专家界面角色背景
function HeroMainPanel:_OnSwitchHeroDisplayStage(heroId)


	local heroFashionSuitIdStr = tostring(Module.Hero:GetCurShowHeroFashionSuitId())
	--卸载子关卡
	-- HeroLogic.OnSceneSubLevelUnload(self._curHeroFashionID)

	if self._curHeroFashionID ~= heroFashionSuitIdStr then
		self._curHeroFashionID = heroFashionSuitIdStr
	end

	if self._bToggleFarCamera then
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraDisplayType", "CharacterFar",
			self._curHeroFashionID, false)
	else
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraDisplayType", "Character", self._curHeroFashionID
			, false)
	end
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterRotation")


end

function HeroMainPanel:_OnHeroUnlockNtf(heroIds)
	if heroIds then
		local bFresh = false
		for _, heroId in ipairs(heroIds) do
			if heroId == self.curSeletcHeroId then
				bFresh = true
				break
				-----------------------------------------------------
				---------------未解锁接入------------------
				--  Module.CommonBar:SetBottomBarTempInputSummaryList({
				-- {actionName = "ResetInput", func = self._OnClickResetBtn, caller = self}
				-- }, false)
			end
		end
		if bFresh then
			self:_RefreshHeroInfoByHeroId(self.curSeletcHeroId)
		end

		--刷新干员item
		for _, uiIns in pairs(self._allHeroItems) do
			if uiIns and uiIns:GetHeroId() then
				uiIns:RefreshView()
			end
		end
	end
end

function HeroMainPanel:_OnMainContentPanelShowChanged(bShow)
	if bShow then
		self._wtMainContentPanel:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtMainContentPanel:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function HeroMainPanel:_OnHeroItemSelected(SkillId)
	if SkillId then
		self._wtSkillTips:SetSkillTipInfo(SkillId)
	end
end

function HeroMainPanel:_OnShowIdxChanged(Idx)
	if self._showTipsIdx ~= Idx then
		self._showTipsIdx = Idx
		self:_OnSwitchTipsPanel()
	else
		self._showTipsIdx = 0
		self:_OnSwitchTipsPanel()
	end
end

function HeroMainPanel:_OnSwitchTipsPanel()
	if self._showTipsIdx > 0 and self._showTipsIdx <= 4 then
		if self._wtSkillTips then
			self._wtSkillTips:SelfHitTestInvisible()
		end
		-- self._wtSkillTips:PlayInAnim()
		-- if self._wtHeroChartPanel then
		-- 	self._wtHeroChartPanel:Collapsed()
		-- end

		-- self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Unchecked)
	elseif self._showTipsIdx == 5 then -- 专家信息(原兵种道具信息) tips
		-- if self._wtHeroChartPanel then
		-- 	self._wtHeroChartPanel:SelfHitTestInvisible()
		-- end

		-- self._wtSkillTips:PlayOutAnim()
		if self._wtSkillTips then
			self._wtSkillTips:Collapsed()
		end

		-- self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Checked)
	else
		-- self._wtSkillTips:PlayOutAnim()
		if self._wtSkillTips then
			self._wtSkillTips:Collapsed()
		end

		-- if self._wtHeroChartPanel then
		-- 	self._wtHeroChartPanel:Collapsed()
		-- end

		-- self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Unchecked)
	end

	self:ResolveSelectedState()
end

function HeroMainPanel:ResolveSelectedState()
	for index, value in ipairs(self.selectedBox) do
		if self._showTipsIdx == index then
			value:Visible()
		else
			value:Collapsed()
		end
	end
end

function HeroMainPanel:_OnMouseEnterExpertInfoButton()
	if DFHD_LUA == 1 then
		-- local armId = HeroHelperTool.GetHeroArmedForceId(self.curSeletcHeroId)
		-- self:_RefreshExpertInfoTips(armId)
		-- self._wtHeroChartPanel:PlayInAnim()
		-- self._wtHeroChartPanel:SelfHitTestInvisible()
		-- self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Checked)

		local armId = HeroHelperTool.GetHeroArmedForceId(self.curSeletcHeroId)
		local fCallback = function(panel)
			self._wtHeroChartPanel = panel
			self:_RefreshExpertInfoTips(armId)
			self._HeroTalentTipPanel:AddChild(self._wtHeroChartPanel)
			self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Checked)
		end
		if self._wtHeroChartPanel == nil then
		else
			self._wtHeroChartPanel:SelfHitTestInvisible()
			self:_RefreshExpertInfoTips(armId)
			self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Checked)
		end
	end
end

function HeroMainPanel:_OnMouseLeaveExpertInfoButton()
	if DFHD_LUA == 1 then
		local armId = HeroHelperTool.GetHeroArmedForceId(self.curSeletcHeroId)
		-- self:_RefreshExpertInfoTips(armId)
		self._wtHeroChartPanel:Collapsed()
		-- self._wtHeroChartPanel:PlayOutAnim()
		self._wtExpertInfoCheckBox:SetCheckedState(ECheckBoxState.Unchecked)
	end
end

function HeroMainPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
	local absolutePoint = mouseEvent:GetScreenSpacePosition()
	if self._showTipsIdx ~= 0 then
		local bInsidePropItem1 = UIUtil.CheckAbsolutePointInsideWidget(self._wtHeroPropItem1, absolutePoint)
		local bInsidePropItem2 = UIUtil.CheckAbsolutePointInsideWidget(self._wtHeroPropItem2, absolutePoint)
		local bInsideSkill1 = UIUtil.CheckAbsolutePointInsideWidget(self._wtSkillIcon1, absolutePoint)
		local bInsideSkill2 = UIUtil.CheckAbsolutePointInsideWidget(self._wtSkillIcon2, absolutePoint)
		local bInsideExpert = UIUtil.CheckAbsolutePointInsideWidget(self._wtExpertInfoButton, absolutePoint)
		if bInsidePropItem1 or
			bInsidePropItem2 or
			bInsideSkill1 or
			bInsideSkill2 or
			bInsideExpert then
			-- self._showTipsIdx = self._showTipsIdx
		else
			self._showTipsIdx = 0
			self:_OnSwitchTipsPanel()
		end
	end
end

-- 科隆暂时功能 Start
function HeroMainPanel:_InitShortCuts()
	-- 切换角色
	-- 展示全身
	self._hActionToggleFirstHero =
	self:AddInputActionBinding(
		"Expert_One",
		EInputEvent.IE_Pressed,
		self._SwitchToFirstHero,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	self._hActionToggleSecondHero =
	self:AddInputActionBinding(
		"Expert_Two",
		EInputEvent.IE_Pressed,
		self._SwitchToSecondHero,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	self._hActionToggleThirdHero =
	self:AddInputActionBinding(
		"Expert_Three",
		EInputEvent.IE_Pressed,
		self._SwitchToThirdHero,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	self._hActionToggleFourthHero =
	self:AddInputActionBinding(
		"Expert_Four",
		EInputEvent.IE_Pressed,
		self._SwitchToFourthHero,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	self._hActionToggleDragon =
	self:AddInputActionBinding(
		"SwitchToDragon",
		EInputEvent.IE_Pressed,
		self._SwitchToDragon,
		self,
		EDisplayInputActionPriority.UI_Stack
	)
	-- 切换镜头
	self._ToggleCamera =
	self:AddInputActionBinding(
		"ToggleExpertCamera",
		EInputEvent.IE_Pressed,
		self._OnToggleCamera,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	-- 隐藏显示UI
	self._SetSelfVisible =
	self:AddInputActionBinding(
		"SetHeroPanelVisible",
		EInputEvent.IE_Pressed,
		self._OnShowSelf,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	self._SetSelfHidden =
	self:AddInputActionBinding(
		"SetHeroPanelHidden",
		EInputEvent.IE_Pressed,
		self._OnHideSelf,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	self._SetHeroOperate =
	self:AddInputActionBinding(
		"SetHeroOperate",
		EInputEvent.IE_Pressed,
		self._OnOperateHero,
		self,
		EDisplayInputActionPriority.UI_Stack
	)
	self._OpenHeroProfile =
	self:AddInputActionBinding(
		"OpenHeroProfile",
		EInputEvent.IE_Pressed,
		self._OnOpenHeroProfile,
		self,
		EDisplayInputActionPriority.UI_Stack
	)

	-- --P键打开干员招募
	-- self._openHeroRecruitHandle =
	-- self:AddInputActionBinding(
	-- 	"OpenHeroRecruit",
	-- 	EInputEvent.IE_Pressed,
	-- 	self._OpenHeroRecruitPanel,
	-- 	self,
	-- 	EDisplayInputActionPriority.UI_Stack
	-- )


end

function HeroMainPanel:_UninitShortCuts()
	if self._hActionToggleFirstHero then
		self:RemoveInputActionBinding(self._hActionToggleFirstHero)
		self._hActionToggleFirstHero = nil
	end

	if self._hActionToggleSecondHero then
		self:RemoveInputActionBinding(self._hActionToggleSecondHero)
		self._hActionToggleSecondHero = nil
	end

	if self._hActionToggleThirdHero then
		self:RemoveInputActionBinding(self._hActionToggleThirdHero)
		self._hActionToggleThirdHero = nil
	end

	if self._hActionToggleFourthHero then
		self:RemoveInputActionBinding(self._hActionToggleFourthHero)
		self._hActionToggleFourthHero = nil
	end

	if self._hActionToggleDragon then
		self:RemoveInputActionBinding(self._hActionToggleDragon)
		self._hActionToggleDragon = nil
	end

	if self._ToggleCamera then
		self:RemoveInputActionBinding(self._ToggleCamera)
		self._ToggleCamera = nil
	end

	if self._SetSelfVisible then
		self:RemoveInputActionBinding(self._SetSelfVisible)
		self._SetSelfVisible = nil
	end

	if self._SetSelfHidden then
		self:RemoveInputActionBinding(self._SetSelfHidden)
		self._SetSelfHidden = nil
	end

	if self._SetHeroOperate then
		self:RemoveInputActionBinding(self._SetHeroOperate)
		self._SetHeroOperate = nil
	end
	if self._openHeroRecruitHandle then
		self:RemoveInputActionBinding(self._openHeroRecruitHandle)
		self._openHeroRecruitHandle = nil
	end
end

function HeroMainPanel:_SwitchToFirstHero()
	self:_RefreshHeroInfoByHeroId(88000000030)
	self.curSeletcHeroId = 88000000030
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", self.curSeletcHeroId)
end

function HeroMainPanel:_SwitchToSecondHero()
	self:_RefreshHeroInfoByHeroId(88000000027)
	self.curSeletcHeroId = 88000000027
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", self.curSeletcHeroId)
end

function HeroMainPanel:_SwitchToThirdHero()
	self:_RefreshHeroInfoByHeroId(88000000029)
	self.curSeletcHeroId = 88000000029
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", self.curSeletcHeroId)
end

function HeroMainPanel:_SwitchToFourthHero()
	self:_RefreshHeroInfoByHeroId(88000000028)
	self.curSeletcHeroId = 88000000028
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", self.curSeletcHeroId)
end

function HeroMainPanel:_SwitchToDragon()
	self:_RefreshHeroInfoByHeroId(88000000025)
	self.curSeletcHeroId = 88000000025
	Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", self.curSeletcHeroId)
end

function HeroMainPanel:_OnToggleCamera()
	self._bToggleFarCamera = not self._bToggleFarCamera
	if self._bToggleFarCamera then
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCurrentCharacterSeqEnd")
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetDisplayType", "CharacterFar")
	else
		Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetDisplayType", "Character")
	end
end

function HeroMainPanel:_OnShowSelf()
	for _, layerType in pairs(EUILayer) do
		Facade.UIManager:SetLayerVisible(layerType, true, ELayerRuleChangeReason.BusinessPending)
	end
	UKismetSystemLibrary.ExecuteConsoleCommand(
		GetGameInstance(),
		"EnableAllScreenMessages",
		nil
	)
end

function HeroMainPanel:_OnHideSelf()
	for _, layerType in pairs(EUILayer) do
		Facade.UIManager:SetLayerVisible(layerType, false, ELayerRuleChangeReason.BusinessPending)
	end
	UKismetSystemLibrary.ExecuteConsoleCommand(
		GetGameInstance(),
		"DisableAllScreenMessages",
		nil
	)
end

function HeroMainPanel:_OnOperateHero()
	self:_OnHeorOperateBtnClick()
end

--打开干员档案
function HeroMainPanel:_OnOpenHeroProfile()
	loginfo('HeroMainPanel:_OnOpenHeroProfile()')
	local checkHeroId = self._hoverHeroId and self._hoverHeroId or Server.HeroServer:GetCurUsedHeroId()
	if self.curSeletcHeroId then
		checkHeroId = self.curSeletcHeroId
	end
	local isUnlock = Server.HeroServer:IsCanUseHero(checkHeroId)
	if isUnlock then
		self:_IsJumpView(true)
		HeroLogic.ShowHeroProfile(tonumber(checkHeroId), nil)
		LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.GrowLineEntrance)
	end
end

-- 科隆暂时功能 End

function HeroMainPanel:_OnSelectHero()	
	if DFHD_LUA == 1 then
		if WidgetUtil.IsGamepad() then
			return
		end

		if tostring(self.curSeletcHeroId) == Server.HeroServer:GetCurUsedHeroId() then
			return
		end

		--是否拥有该干员
		if Server.HeroServer:IsCanUseHero(self.curSeletcHeroId) then
			Module.Hero:UseHeroById(self.curSeletcHeroId)
			self._onOperateExpertEvent(self.curSeletcHeroId)
		else
			local unLockInfo, heroInfo = self:_GetHeroInfoData(self.curSeletcHeroId)
			self:_IsJumpView(true)
			self.curBottomBarState = nil
			if unLockInfo and heroInfo then
				Module.Hero:ShowUnLockHeroPanel(heroInfo, unLockInfo)
			else
				loginfo("heroId is ", self.curSeletcHeroId, " get unLockInfo is nil")
			end
		end
		-------------------------------------------------------------------------------------------------*****************************************************
		-- self._onOperateExpertEvent(self.curSeletcHeroId)
	end
end

function HeroMainPanel:_OnShowAppearance()
	self:_OnFashionBtnClick()
end

function HeroMainPanel:_OnShowSkillPanel()
	if self.curSeletcHeroId then
		local fOnSkillPanelCreateCallback = function(uins)
			self.bShowSkillPanel = true
			self._HeroSkillPanel = uins
			Module.Hero.Config.Events.evtOnHeroTopSkillPanelShow:Invoke()
		end
		local fOnSkillPanelCloseCallback = function(bShowVideoPanel)
			if self._wtMainContentPanel then
				self._wtMainContentPanel:SetRenderOpacity(1)
			end
			if not bShowVideoPanel then
				self:SetBottomBarActions()
			end

			Module.Hero.Config.Events.evtOnHeroTopSkillPanelClose:Invoke()
			----这里应该注册专家界面本身的快捷键
		end
		-- self:_RegisterSkillPanelShortCuts()
		Module.Hero.Config.Events.evtOnHeroViewSkillBtnClicked:Invoke()
		self._topUIHandle = Module.Hero:ShowHeroSkillPanel(fOnSkillPanelCreateCallback, self.curSeletcHeroId,
			fOnSkillPanelCloseCallback)
		-- self:PlayWidgetAnim(self.WBP_Hero_MainPanel_in_jineng, 1, EUMGSequencePlayMode.Forward,1.0, false)
		self._wtMainContentPanel:SetRenderOpacity(0.1)
		LogAnalysisTool.SignButtonClicked(ButtonIdConfig.Enum.ViewSkill)
	end
end

function HeroMainPanel:_OnHideSkillPanel()
	if self._HeroSkillPanel then
		self.bShowSkillPanel = false
		if self._topUIHandle then
			Facade.UIManager:CloseUIByHandle(self._topUIHandle)
		end
	end

	-- self:PlayWidgetAnim(self.WBP_Hero_MainPanel_out_jineng, 1, EUMGSequencePlayMode.Forward,1.0, false)
	self._wtMainContentPanel:SetRenderOpacity(1)
	self:_UnregisterSkillPanelShortCuts()
end

-- 干员能力视频演示
function HeroMainPanel:_OnShowSkillByMedia()
	self:_IsJumpView(true)
	self:_OnHideSkillPanel()
	Module.Hero:ShowHeroSkillPanel(nil, self.curSeletcHeroId)
end

function HeroMainPanel:OnbKickOutOfRoom()
	Facade.UIManager:CloseUI(self)
end

--endregion
--==================================================

-----------------------------------------------------------------------
--region HeroLine

--mobile端成长线红点
function HeroMainPanel:RegisterHeroLineReddot(heroId)
	if DFHD_LUA == 0 then
		self:UnReigsterHeroLineReddot()
		-- local data1 = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero,
		-- 	string.format("NewUnlockArchive_Hero%s", heroId))
		local data2 = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero,
			string.format("CanTakeRewards_Hero%s", heroId))
		local data3 = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Hero,
			string.format("GeneralRedPoint_Hero%s", heroId))
			-- if data1 and data2 and data3 then
		if data2 and data3 then
			self.heroLineReddotMobile = Module.ReddotTrie:RegisterStaticReddotDot(self._wtHeroDetailBtn, {
				-- { reddotData = data1, reddotStyle = { placeOffset = FVector2D(15, 15) } },
				{ reddotData = data2, reddotStyle = { placeOffset = FVector2D(15, 15) } },
				{ reddotData = data3, reddotStyle = { placeOffset = FVector2D(15, 15) } },
			})
		end
	end
end

function HeroMainPanel:UnReigsterHeroLineReddot()
	if self.heroLineReddotMobile then
		Module.ReddotTrie:UnRegisterStaticReddotDot(self.heroLineReddotMobile)
		self.heroLineReddotMobile = nil
	end
end

--endregion
-----------------------------------------------------------------------

function HeroMainPanel:_OpenHeroRecruitPanel()
	if not self.curSeletcHeroId then
		return
	end
	local bIsRecruitable = HeroHelperTool.IsRecruitableHero(self.curSeletcHeroId)
	if bIsRecruitable then
		Module.Hero.ShowHeroRecruitmentPanel(nil,nil,self.curSeletcHeroId)
	end
end

function HeroMainPanel:_OnApproachButtonClick()
	self:_IsJumpView(true)
 	self:_OpenHeroRecruitPanel()
end

--黑屏表现
function HeroMainPanel:OperatorCommitTransition(bSet)
	Facade.UIManager:CommitTransition(bSet)
end

function HeroMainPanel:_RouterHeroList()
	local showHeroID = tonumber(Module.Hero:GetCurShowHeroId())
	local curHeroUsedID = tonumber(Server.HeroServer:GetCurUsedHeroId())
	local useIndex = -1
	local showIndex = -1
	local unreadIndex = -1
	local finalIndex = -1
	if self.heroIdList then
		for index, _ in ipairs(self.heroIdList) do
			local heroId = self.heroIdList[index]
			if showHeroID == heroId then
				showIndex = index
			end

			if curHeroUsedID == heroId then
				useIndex = index
			end

			local bIsUnlock = Server.HeroServer:IsOwningHero(heroId)
			if  bIsUnlock and  not Server.HeroServer:IsReadedHero(heroId) then
				unreadIndex = index
				break
			end
		end
	end
	if unreadIndex>0 then
		finalIndex = unreadIndex
	elseif showIndex > 0 then
		finalIndex = showIndex
	elseif useIndex > 0 then
		finalIndex = useIndex
	end
	if finalIndex > 0 then
		local child = self._wtHeroItemRoot:GetItemByIndex(finalIndex - 1)
		if child then
			self._wtHeroItemRoot:ScrollWidgetIntoView(child, true, EDescendantScrollDestination.IntoView)
		end
	end

end

-- BEGIN MODIFICATION @ VIRTUOS :
function HeroMainPanel:_RegisterUINavigation()
    if not IsHD() then
        return
    end

    -- 导航组
    self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtHeroItemRoot, self, "Hittest")
    self._wtNavGroup:AddNavWidgetToArray(self._wtHeroItemRoot)
    -- 启用默认导航配置
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
	WidgetUtil.WrapNavBoundary(self._wtNavGroup, {EUINavigation.Left, EUINavigation.Right})
    -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
	WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._FocusProxyMaker, self._FocusProxyResolver, self)
end

function HeroMainPanel:_RemoveUINavigation()
    if not IsHD() then
        return
    end

    -- 移除导航数据
    WidgetUtil.RemoveNavigationGroup(self)
    -- 恢复默认导航配置
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function HeroMainPanel:_EnableExpertInfoGamepadInput(enabled)
    if not IsHD() then
        return
    end

	if enabled then
		if self._expertInfoBtnHandle == nil then
			self._expertInfoBtnHandle = self:AddInputActionBinding("Common_ToggleTip", EInputEvent.IE_Pressed, self._ToggleExpertTalentInfo, self, EDisplayInputActionPriority.UI_Stack)
		end
	else
		if self._expertInfoBtnHandle then
			self:RemoveInputActionBinding(self._expertInfoBtnHandle)
			self._expertInfoBtnHandle = nil
		end
	end
end

function HeroMainPanel:_FocusProxyMaker(inWidget)
    -- Maker会将当前聚焦的UWidget传入
    -- 但聚焦的控件往往是一些button之类的原子控件，不一定是上层带有业务逻辑的Item，因此需要通过GetParentWidgetByClassName找到上层的Item
	local uiIns = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_Hero_HeroItem01_C")
	local heroId = uiIns:GetHeroId()
    return {heroId}
end

function HeroMainPanel:_FocusProxyResolver(inProxyHandle)
    local heroId = inProxyHandle[1]
    for _, uiIns in ipairs(self._allHeroItems) do
		if uiIns then
			if uiIns:GetHeroId() == heroId then
				return uiIns
			end
		end
	end
    return nil
end

-- END MODIFICATION

return HeroMainPanel
