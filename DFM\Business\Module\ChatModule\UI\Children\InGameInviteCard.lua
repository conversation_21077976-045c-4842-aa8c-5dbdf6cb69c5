----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMChat)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class InGameInviteCard : LuaUIBaseView
local InGameInviteCard = ui("InGameInviteCard")
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"
local InputSummaryItemHD = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"

function InGameInviteCard:Ctor()
    self._wtNoReceiveInviteChecbox = self:Wnd("wtDFCommonCheckBoxWithText",DFCheckBoxWithText)
    self._wtNoReceiveInviteChecbox:Event("OnCheckStateChanged",self.OnNoReceiveCheckboxStateChanged,self)
    -- 标题
    self._wtTitleTxt = self:Wnd("DFTextBlock_67",UITextBlock)
    -- 邀请信息
    self._wtInviteContent = self:Wnd("DFRichTextBlock_0",UITextBlock)
    if IsHD() then
        self._wtInputSummaryAgree = self:Wnd("WBP_TopBarHD_InputSummary", InputSummaryItemHD)
        self._wtInputSummaryRefuse = self:Wnd("WBP_TopBarHD_InputSummary_1", InputSummaryItemHD)
        self._wtInputSummaryAgree:Visible()
        self._wtInputSummaryRefuse:Visible()
    else
        self._wtRefuseBtn = self:Wnd("WBP_CommonButtonV2S2_37", DFCommonButtonOnly)
        self._wtRefuseBtn:Event("OnClicked",self.OnRefuseBtnClick, self)
        self._wtAgressBtn = self:Wnd("WBP_CommonButtonV2S1", DFCommonButtonOnly)
        self._wtAgressBtn:Event("OnClicked",self.OnAgreeBtnClick, self)
    end
    -- 进度条
    self._wtProgressBar = self:Wnd("DFProgressBar_56", UIProgressBar)
end

function InGameInviteCard:OnOpen()
    self:UpdateAllContent()
    self:AddLuaEvent(Module.Chat.Config.evtInGamePreBookCardUpdate, self.SomethingChange, self)

    if IsHD() then
        local acceptEventProxyAgree = SafeCallBack(self.OnAgreeBtnClick, self)
        self._wtInputSummaryAgree.bUseActionNameOverride = false
        self._wtInputSummaryAgree:SetDataByActionOrAxisName("QuickResYes", acceptEventProxyAgree)
        self._wtInputSummaryAgree.eventProxy = acceptEventProxyAgree

        local acceptEventProxyRefuse = SafeCallBack(self.OnRefuseBtnClick, self)
        self._wtInputSummaryRefuse.bUseActionNameOverride = false
        self._wtInputSummaryRefuse:SetDataByActionOrAxisName("QuickResNo", acceptEventProxyRefuse)
        self._wtInputSummaryRefuse.eventProxy = acceptEventProxyRefuse
    end
end

function InGameInviteCard:OnClose()
    self:RemoveAllLuaEvent()
end

function InGameInviteCard:OnShow()
    if IsHD() then
        self._wtInputSummaryAgree:Visible()
        self._wtInputSummaryRefuse:Visible()
    end
end

function InGameInviteCard:OnHide()
    self:ClearTween()
end

function InGameInviteCard:OnNoReceiveCheckboxStateChanged(bChecked)
    -- 勾选本局不再接收
    if bChecked then
        Module.Chat.Config.evtNoReceiveIngameInvite:Invoke()
    else
        Module.Chat.Config.evtNoReceiveIngameInvite:Invoke()
    end
end

function InGameInviteCard:OnRefuseBtnClick()
    local dataValue, index = Module.Team.Field.GameTeamInviteViewModel:GetTheOldestPendingReq()
    if dataValue and index then
        Module.Team.Field.GameTeamInviteViewModel:RejectInviteByIndex(index)
    end
end

function InGameInviteCard:OnAgreeBtnClick()
    local dataValue, index = Module.Team.Field.GameTeamInviteViewModel:GetTheOldestPendingReq()
    if dataValue and index then
        Module.Team.Field.GameTeamInviteViewModel:AgreeInviteByIndex(index)
    end
end

function InGameInviteCard:SomethingChange(reason)
    self:UpdateAllContent()
end

function InGameInviteCard:UpdateAllContent()
    if Module.Team.Field.GameTeamInviteViewModel:GetPendingSum() > 0 then
        local dataValue, index = Module.Team.Field.GameTeamInviteViewModel:GetTheOldestPendingReq()
        if dataValue then
            self:UpdateTopContent(dataValue)
            self:UpdateMiddleContent(dataValue)
            self:UpdateBottomContent(dataValue)
        end
    end
end

function InGameInviteCard:UpdateTopContent(dataValue)

    local titleText = ""
    if REGION_CN then
        titleText = Module.Preparation.Config.Tips.InviteTeam
        if dataValue.inviteType == "TeamApply" then
            titleText = Module.Preparation.Config.Tips.ApplicateTeam
        end
    else
        local targetTxt = "[" .. tostring(Module.Team.Field.GameTeamInviteViewModel:GetPendingSum()) .. "]"
        self._wtTitleTxt:SetText(targetTxt)
        return
    end

    local targetTxt = StringUtil.Key2StrFormat(
        Module.Chat.Config.Loc.InGamePreBookPendingNum,
        {
            ["stableText"] = titleText,
            ["numText"] = Module.Team.Field.GameTeamInviteViewModel:GetPendingSum(),
        }
    )

    self._wtTitleTxt:SetText(targetTxt)
end

function InGameInviteCard:UpdateMiddleContent(dataValue)
    local modeName = Module.Team.Field.GameTeamInviteViewModel:GetTargetModeStr(dataValue) or ""
    local targetLocText = Module.Chat.Config.Loc.InGamePreBookInvite
    local displayContent = ChatLogic.InGameInviteContentMake(targetLocText, modeName, dataValue.custom_invitestate)
    local showTxt = string.format(
        "<customstyle color=\"Color_Chat_Friend\">%s：</>%s", ChatLogic.GetInGameInviteOrApplyNickName(dataValue), displayContent)
    self._wtInviteContent:SetText(showTxt)
end

function InGameInviteCard:UpdateBottomContent(dataValue)
    local curTime = TimeUtil:GetCurrentTime()
    local costTime = math.max(0, curTime - dataValue.custom_arrive_time)
    if costTime <= 0 then
        self._wtProgressBar:SetPercent(0.0)
    else
        local targetNum = math.max(0, (1-costTime*1.0/Module.Social.Config.DEFAULT_APPOINT_COLD_TIME))
        self._wtProgressBar:SetPercent(targetNum)
    end
    if (Module.Social.Config.DEFAULT_APPOINT_COLD_TIME - costTime) > 0 then
        self:StartTween(Module.Social.Config.DEFAULT_APPOINT_COLD_TIME-costTime)
    end
end

-------------------------------------------------------------------------------
---进度条更新
-------------------------------------------------------------------------------
function InGameInviteCard:ClearTween()
    if self._tween then
        self._tween:cancel()
        self._tween = nil
    end
end

function InGameInviteCard:StartTween(remainTime)
    self:ClearTween()
    self._tween = Facade.TweenManager:CreateTweener(remainTime, {X = remainTime}, {X = 0}, "linear")
    self._tween:OnUpdate(self, self.OnCountdown)
end

function InGameInviteCard:OnCountdown(tween)
    self._wtProgressBar:SetPercent(tonumber(tween.subject.X) / tonumber(Module.Social.Config.DEFAULT_APPOINT_COLD_TIME))

    if tween:IsFinish() then
        self._tween = nil
    end
end
--end
-----------------------------------------------------------------------

return InGameInviteCard