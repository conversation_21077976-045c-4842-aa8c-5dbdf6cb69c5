----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMAuction)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local AuctionBuyView = ui("AuctionBuyView")
local SlotConfig = require "DFM.Business.DataStruct.InventoryStruct.SlotConfig"
local AuctionLogic = require("DFM.Business.Module.AuctionModule.Logic.AuctionLogic")
local CommonWidgetConfig = require("DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig")
local ItemHelperTool     = require("DFM.StandaloneLua.BusinessTool.ItemHelperTool")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponAssemblyTool = require("DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool")
local AuctionSearchBar = require "DFM.Business.Module.AuctionModule.UI.Buy.AuctionSearchBar"
-- local AuctionCollectionPanel = require "DFM.Business.Module.AuctionModule.UI.Buy.AuctionCollectionPanel"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local ETextCommit = import "ETextCommit"
local FVector2D = import("Vector2D")
-- local ULuaSubsystem = import "LuaSubsystem"
local UDFMGameNotch = import "DFMGameNotch"
local DFMGameNotch = UDFMGameNotch.Get(GetGameInstance())
-- local OVERRIDE_SCROLL_AMOUNT_HD = 128

local ESpecialMainTab = {
    Collect = 1,
}

local ESpecialSubTab = {
    All = 0,
}

local EDurabilityLevel = {
    New = 1,
    Good = 2,
    Worn = 3,
}

--- 正在售卖的道具排朝前，其次是高品质的排朝前，其次价格贵的排朝前
local common_sort_mode_1 = function(a, b)
    if a.cur_num > 0 and b.cur_num > 0 then
        if ItemConfigTool.GetItemQuality(a.prop_id) == ItemConfigTool.GetItemQuality(b.prop_id) then
            return b.guide_price < a.guide_price
        else
            return ItemConfigTool.GetItemQuality(b.prop_id) < ItemConfigTool.GetItemQuality(a.prop_id)
        end
    elseif a.cur_num == 0 and b.cur_num == 0 then
        if ItemConfigTool.GetItemQuality(a.prop_id) == ItemConfigTool.GetItemQuality(b.prop_id) then
            return b.guide_price < a.guide_price
        else
            return ItemConfigTool.GetItemQuality(b.prop_id) < ItemConfigTool.GetItemQuality(a.prop_id)
        end
    elseif a.cur_num == 0 then
        return false
    else
        return true
    end
end

--- 高品质的排朝前，其次价格贵的排朝前
local common_sort_mode_2 = function(a, b)
    if ItemConfigTool.GetItemQuality(a.prop_id) == ItemConfigTool.GetItemQuality(b.prop_id) then
        return b.guide_price < a.guide_price
    else        
        return ItemConfigTool.GetItemQuality(b.prop_id) < ItemConfigTool.GetItemQuality(a.prop_id)
    end
end

--- 正在售卖的道具排朝前，其次是高品质的排朝前，其次价格贵的排朝前
local collection_sort_mode_1 = function(a, b)
    if a.cur_num > 0 and b.cur_num > 0 then
        if ItemConfigTool.GetItemSubTagID(a.prop_id) == ItemConfigTool.GetItemSubTagID(b.prop_id) then
            if ItemConfigTool.GetItemQuality(a.prop_id) == ItemConfigTool.GetItemQuality(b.prop_id) then
                return b.guide_price < a.guide_price
            else        
                return ItemConfigTool.GetItemQuality(b.prop_id) < ItemConfigTool.GetItemQuality(a.prop_id)
            end
        else
            return ItemConfigTool.GetItemSubTagID(a.prop_id) < ItemConfigTool.GetItemSubTagID(b.prop_id)
        end
    elseif a.cur_num == 0 and b.cur_num == 0 then
        if ItemConfigTool.GetItemSubTagID(a.prop_id) == ItemConfigTool.GetItemSubTagID(b.prop_id) then
            if ItemConfigTool.GetItemQuality(a.prop_id) == ItemConfigTool.GetItemQuality(b.prop_id) then
                return b.guide_price < a.guide_price
            else        
                return ItemConfigTool.GetItemQuality(b.prop_id) < ItemConfigTool.GetItemQuality(a.prop_id)
            end
        else
            return ItemConfigTool.GetItemSubTagID(a.prop_id) < ItemConfigTool.GetItemSubTagID(b.prop_id)
        end
    elseif a.cur_num == 0 then
        return false
    else
        return true
    end
end

--- 高品质的排朝前，其次价格贵的排朝前
local collection_sort_mode_2 = function(a, b)
    if ItemConfigTool.GetItemSubTagID(a.prop_id) == ItemConfigTool.GetItemSubTagID(b.prop_id) then
        if ItemConfigTool.GetItemQuality(a.prop_id) == ItemConfigTool.GetItemQuality(b.prop_id) then
            return b.guide_price < a.guide_price
        else        
            return ItemConfigTool.GetItemQuality(b.prop_id) < ItemConfigTool.GetItemQuality(a.prop_id)
        end
    else
        return ItemConfigTool.GetItemSubTagID(a.prop_id) < ItemConfigTool.GetItemSubTagID(b.prop_id)
    end
end

function AuctionBuyView:Ctor()
    ---@param list pb_CSSaleType[]
    self._itemInfoList = {}
    self._itemWidgets = {}
    self._CollectionList = {}
    self._curMainIdx = nil
    self._curSubIdx = nil
    self._isSearching = false
    self._bAutoSortMode = nil
    self._durabilityLvl = 0
    -- self._GetGeometryTimerHandle = nil

    self._wtDurabilityVerticalBox = self:Wnd("VerticalBox_0", UIWidgetBase)
    self._wtEmptyBgSlot = self:Wnd("EmptySlot", UIWidgetBase)
    if IsHD() then
        self._wtDFTabV3GroupBox = self:Wnd("WBP_DFTabV3GroupBox", UIWidgetBase)
    else
        self._wtDFTabV3GroupBox = self:Wnd("WBP_DFTabV3GroupBox2", UIWidgetBase)
    end

	self._wtDFTabV3GroupBox:Event("OnTabIndexChanged", self._OnGroupBoxClicked, self)
    self._wtItemViewScrollGridBox = UIUtil.WndWaterfallScrollBox(self, "ScrollGridBox", self._OnGetItemViewRowCount, self._OnProcessItemViewRowWidget)
    self._wtItemViewScrollGridBox:Event('OnProcessItemsUpdateFinished', self._OnProcessItemsUpdateFinished, self)
    -- self._wtCollectScrollBox = self:Wnd("DFScrollBox", UIScrollBox)
    -- 改为在蓝图设置
    self._wtItemClassifyTabs = self:Wnd("WBP_DFCommonSecondTab", UIWidgetBase)
    self._wtSearchBarMobile = self:Wnd("WBP_AuctionSearchBar", AuctionSearchBar)
    self._wtSearchBarHDInsId = 0

    self._wtRelatedSearchTextPanel = self:Wnd("DFCanvasPanel_Search", UIWidgetBase)

    self._CELL_ROW_COUNT = 3
    self._PLAY_SHOW_ANIM_DELTA_TIME = 0.02
    self._showAnimFinishList = {}
end

function AuctionBuyView:OnOpen()
    if IsHD() then
        self._wtSearchBarMobile:Collapsed()

        self._wtPCSearchBarSlot = self:Wnd("PCSearchBar", UIWidgetBase)
        -- 搜索栏初始化
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.AuctionSearchBar, self._wtPCSearchBarSlot)
        self._wtSearchBarHDInsId = instanceId
        local wtSearchBar = getfromweak(weakUIIns)
        if wtSearchBar then
            self:InitSearchBar(wtSearchBar)
        end
    else
        self:InitSearchBar(self._wtSearchBarMobile)
    end
    self:GenLiveTabList()
    self:AddLuaEvent(Server.AuctionServer.Events.evtTypeListChanged, self._OnTypeListChanged, self)
    self:AddLuaEvent(Module.Auction.Config.Events.evtCollectListChanged, self._OnFetchCollectList, self)
    self:AddLuaEvent(Server.AuctionServer.Events.evtPlayerInfoChanged, self._OnFetchCollectList,self)
    self:AddLuaEvent(Module.Auction.Config.Events.evtSearchRecordsChanged, self._OnSearchRecordsChanged, self)
    self:AddLuaEvent(Server.AuctionServer.Events.evtSaleListChanged,self._OnSaleListChanged, self)
    self:AddLuaEvent(Module.Auction.Config.Events.evtAuctionOpenFinish, self._OnTabClicked, self)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连
end


function AuctionBuyView:TryGetSearchBarPlatform()
    if IsHD() then
        local weakUIIns, instanceId = Facade.UIManager:GetSubUI(self, UIName2ID.AuctionSearchBar, self._wtSearchBarHDInsId)
        local wtSearchBar = getfromweak(weakUIIns)
        if wtSearchBar then
           return wtSearchBar
        end
    else
        return self._wtSearchBarMobile
    end
end

function AuctionBuyView:OnClose()
    self:RemoveAllLuaEvent()
    -- self:ReleaseRefreshScrollGridBoxTimer()
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)
    Facade.UIManager:ClearSubUIByParent(self, self._wtPCSearchBarSlot)
    -- Facade.UIManager:ClearSubUIByParent(self, self._wtCollectScrollBox)
end

function AuctionBuyView:OnActivate()
    if self._bAutoSortMode ~= nil and self._bAutoSortMode ~= Module.Auction:GetAutoSortMode() then
        self:_OnTabClicked(self._curMainIdx, self._curSubIdx)
    end
end

function AuctionBuyView:OnDeactivate()
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function AuctionBuyView:OnShowBegin()
    -- 折叠屏适配
    if DFMGameNotch:IsFoldDevice() and (not self.statusHandle) then
        self.statusHandle = DFMGameNotch.OnFoldStatusChanged:Add(CreateCPlusCallBack(self.OnFoldStatusChanged, self))
    end
    -- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(self._OnNotifyResolutionResized, self)
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end
-- END MODIFICATION

function AuctionBuyView:OnHideBegin()
    -- ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(self._OnNotifyResolutionResized, self)
    if self._curHoverItemView and self._curHoverItemView.HideGivenInstruction then
        self._curHoverItemView:HideGivenInstruction()
        self._curHoverItemView = nil
    end
    -- 折叠屏适配
    if self.statusHandle then
        DFMGameNotch.OnFoldStatusChanged:Remove(self.statusHandle)
        self.statusHandle = nil
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    -- END MODIFICATION
end

function AuctionBuyView:OnFoldStatusChanged()
    self._wtItemViewScrollGridBox:TriggerViewportSizeChange()
end

function AuctionBuyView:OnShow()
    if Module.Auction.Field:GetMainTabStartIndex() and Module.Auction.Field:GetSubTabStartIndex() then
        if self._wtItemClassifyTabs then
            self._wtItemClassifyTabs:SetTabIndex(Module.Auction.Field:GetMainTabStartIndex(), Module.Auction.Field:GetSubTabStartIndex(), true)
            Module.Auction.Field:ResetTabStartIndex()
        end
    end
    -- 注册一下新手引导中会引导到的拍卖行购买界面对应的ui
    Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyAuctionBuyView, self._wtItemViewScrollGridBox)
    if not IsHD() then
        self._scrollStopHandle = UIUtil.AddScrollBoxClickStopScroll(self._wtItemViewScrollGridBox, self)
    end
end

function AuctionBuyView:OnHide()
    if not IsHD() then
        if self._scrollStopHandle then
            UIUtil.RemoveScrollBoxClickStopScroll(self._scrollStopHandle)
            self._scrollStopHandle = nil
        end
    end
end

function AuctionBuyView:GenLiveTabList()
    self:PreInitSubTabList()
    self:InitTabFilter()
end

function AuctionBuyView:PreInitSubTabList()
    --- 把策划同学配表的数据自己整理成容易理解的格式，提前存到Field里
    local mapMainId2PageInfoList = Module.Auction.Field:GetPageInfoMap()
    local sortTabList = {}
    for mainId, pageInfoRowList in pairs(mapMainId2PageInfoList) do
        table.insert(sortTabList, {
            mainOrder = pageInfoRowList[1].MainPageOrder,
            mainId = mainId,
            pageInfoRowList = pageInfoRowList,
        })
    end
    table.sort(sortTabList, function(a, b)
        return a.mainOrder < b.mainOrder
    end)
    self._sortTabList = sortTabList
end

function AuctionBuyView:InitTabFilter()
    self._mainTabList = {}
    for mainIdx, sortTabInfo in ipairs(self._sortTabList) do
        local pageInfoRowList = sortTabInfo.pageInfoRowList
        local mainTable = { 
            mainTabText = pageInfoRowList[1].LabelNo1Name,
            subTabDataList = {},
        }
        for subIdx, pageInfoRow in ipairs(pageInfoRowList) do
            if pageInfoRow.LabelNo2Name ~= "" then
                table.insert(mainTable.subTabDataList , {subTabText = pageInfoRow.LabelNo2Name})
            end
        end
        table.insert(self._mainTabList, mainTable) 
    end
    if self._wtItemClassifyTabs then
        self._wtItemClassifyTabs:FreshMainTabByDataList(self._mainTabList)
        self._wtItemClassifyTabs:SetIndexChangeEvent(self._OnTabChanged, self)
        self._wtItemClassifyTabs:RefreshTabItem()
    end
end

function AuctionBuyView:_OnTabChanged(mainIndex, subIndex)
    self:_OnTabClicked(mainIndex, subIndex)
end

function AuctionBuyView:InitSearchBar(wtSearchBar)
    local function _OnRecordsCellClicked(ItemId)
        if ItemId then
            self._isSearching = true
            local item = ItemBase:NewIns(ItemId)
            if item then
                local curText = wtSearchBar:GetTextBox()
                wtSearchBar:SetTextBox(item.name)
                if tostring(curText) == tostring(item.name) then
                    self:SearchProcess(item.name)
                end
            end
        end
    end
    local function _OnLeaveSearch()
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._SubNavGroup_TabScroll)
        self:_RemoveShortcutsSerarching()
        self:_InitShortcutsCommon()
    end
    wtSearchBar:Init(_OnRecordsCellClicked, CreateCallBack(self._OnCancelSearchBtnClicked, self), _OnLeaveSearch)
    local InputMaxLength = 14
    local tipStr = Module.Auction.Config.Loc.TextMaxTip
    wtSearchBar:InitEditTextBlock(InputMaxLength, tipStr, 
    CreateCallBack(self._OnSearchTextChanged, self),
    CreateCallBack(self._OnSearchTextCommitted, self),
    CreateCallBack(self._GetPreSavedInputText, self),
    CreateCallBack(self._OnInputBoxTextSaved, self))
    wtSearchBar:SetBtnDeleteVisible(false)
end

---@param res pb_CSAuctionGetTypeListRes
function AuctionBuyView:_OnTypeListChanged(res)
    if res and res.result == 0 and self._curMainIdx and self._curSubIdx then
        local bRelatingSearching = Module.Auction.Field:GetRelatedSearchIds() and not table.isempty(Module.Auction.Field:GetRelatedSearchIds())
        if bRelatingSearching then
            if self._wtRelatedSearchTextPanel then
                self._wtRelatedSearchTextPanel:SelfHitTestInvisible()
            end
            self._isSearching = false
            self._durabilityLvl = 0
            if self._wtItemClassifyTabs then
                self._wtItemClassifyTabs:SetTabIndex(-1, 1) -- 关联搜索时收起全部页签
            end
            if self._wtDurabilityVerticalBox and self._wtDFTabV3GroupBox then
                self._wtDurabilityVerticalBox:Collapsed()
                self._wtDFTabV3GroupBox:Collapsed()
            end
            Module.Auction.Field:SetRelatedSearchIds()
        else
            if self._wtRelatedSearchTextPanel then
                self._wtRelatedSearchTextPanel:Collapsed()
            end
            local mainSlotType, subSlotType, allPrefixs = self:_GetTabInfo(self._curMainIdx, self._curSubIdx)
            if mainSlotType == ESlotType.GoodsEquipment and subSlotType ~= 3 and subSlotType ~= 4 then
                self._durabilityLvl = self._wtDFTabV3GroupBox:GetCurSelectedIndex() + 1
            else
                self._durabilityLvl = 0
            end
            if self._curMainIdx == ESpecialMainTab.Collect then
                self:_OnFetchCollectList()
                return
            end
        end
        local typeList = {}
        if not table.isempty(res.type_lists) then
            for _, v in ipairs(res.type_lists) do
                if self._isSearching or bRelatingSearching then -- 搜索/关联搜索状态防具三档耐久度全显示
                    local itemMainType = ItemHelperTool.GetMainTypeById(v.prop_id)
                    local itemSubType = ItemHelperTool.GetSubTypeById(v.prop_id)
                    if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
                        if v.durability_lvl == 1 then
                            table.insert(typeList, v)
                        end
                    else
                        if v.durability_lvl == 0 then
                            table.insert(typeList, v)
                        end
                    end
                else
                    if v.durability_lvl == self._durabilityLvl then
                        table.insert(typeList, v)
                    end
                end
            end
        end
        self:_UpdateView(typeList)
    else
        self:_UpdateView({})
    end
end

function AuctionBuyView:_OnFetchCollectList()
    if self._curMainIdx ~= ESpecialMainTab.Collect or self._isSearching then
        return
    end
    ---@param list pb_CSSaleType[]
    local list = {}
    for _, markProp in ipairs(Module.Auction.Field:GetCollectionProps()) do
        local saleInfo = Server.AuctionServer:GetPropSaleInfo(markProp.prop.id)
        if saleInfo then
            list[#list + 1] = saleInfo
        else
            local fakeSaleInfo = {
                price_change = 0,
                min_price = 0,
                prop_id = markProp.prop.id,
                cur_num = 0,
                show_prop_id = 0,
                guide_currency = 0,
                average_price = 0,
                guide_price = 0,
                max_stack_num = 0,
            }
            list[#list + 1] = fakeSaleInfo
        end
    end
    -- self:_UpdateView(list)
    self:_UpdateCollectionView(list)
end

function AuctionBuyView:_OnTabClicked(mainTabIdx, subTabIdx, component)
    loginfo("AuctionBuyView:_OnTabClicked mainTabIdx=", mainTabIdx, " subTabIdx=", subTabIdx)
    self._curMainIdx = mainTabIdx or self._curMainIdx
    self._curSubIdx = subTabIdx or self._curSubIdx
    if self._curMainIdx <= 0 then
        return
    end
    self._isSearching = false
    if Module.Auction.Field:GetRelatedSearchIds() and not table.isempty(Module.Auction.Field:GetRelatedSearchIds()) then
        return
    end
    if self._wtRelatedSearchTextPanel then
        self._wtRelatedSearchTextPanel:Collapsed()
    end
    local fetchIdsList = {}
    if self._curMainIdx == ESpecialMainTab.Collect then
        self._durabilityLvl = 0
        for _, markProp in ipairs(Module.Auction.Field:GetCollectionProps()) do
            table.insert(fetchIdsList, markProp.prop.id)
        end
        for _, v in ipairs(AuctionLogic.GetRecentBougthRecord()) do
            table.insert(fetchIdsList, v.prop_id)
        end
        self._wtDurabilityVerticalBox:Collapsed()
        self._wtDFTabV3GroupBox:Collapsed()
    else
        -- self._wtCollectScrollBox:Collapsed()
        local mainSlotType, subSlotType, allPrefixs = self:_GetTabInfo(self._curMainIdx, self._curSubIdx)
        -- 如果是武器，需要过滤掉10开头的预设
        if mainSlotType == ESlotType.GoodsWeapon then
            table.filter(allPrefixs, function (v, _)
                local type = tonumber(string.sub(v,1,2))
                return type == EItemType.Receiver
            end)
            allPrefixs = table.tolist(allPrefixs)
        end
        fetchIdsList = allPrefixs
        if mainSlotType == ESlotType.GoodsEquipment and subSlotType ~= 3 and subSlotType ~= 4 then
            self._wtDurabilityVerticalBox:SelfHitTestInvisible()
            self._wtDFTabV3GroupBox:SelfHitTestInvisible()
            self._wtDFTabV3GroupBox:SetTabIndex(EDurabilityLevel.New - 1, true)
        else
            self._wtDurabilityVerticalBox:Collapsed()
            self._wtDFTabV3GroupBox:Collapsed()
        end
    end
    if table.isempty(fetchIdsList) then
        self:_UpdateView({})
    else
        Server.AuctionServer:FetchTypeList(fetchIdsList, true)
    end
end

function AuctionBuyView:_GetTabInfo(mainTabIdx, subTabIdx)
    if self._sortTabList and self._sortTabList[self._curMainIdx] and self._sortTabList[self._curMainIdx].pageInfoRowList then
        local mainSlotType = self._sortTabList[self._curMainIdx].pageInfoRowList[self._curSubIdx].LabelNo1
        local subSlotType = self._sortTabList[self._curMainIdx].pageInfoRowList[self._curSubIdx].LabelNo2
        local slotConfig = SlotConfig.GetSlotConfigByType(mainSlotType)
        local allPrefixs = slotConfig:GetItemIdPrefixs(subSlotType)
        return mainSlotType, subSlotType, allPrefixs
    end
end

function AuctionBuyView:_OnGetItemViewRowCount()
    local curRowCount = 0
    if self._curMainIdx == ESpecialMainTab.Collect then
        local transactionList = AuctionLogic.GetRecentBougthRecord()
        local curCollectionRowCount = (self._CollectionList and #self._CollectionList > 0) and math.ceil(#self._CollectionList / self._CELL_ROW_COUNT) or 1
        local curTransactionRowCount = (transactionList and #transactionList > 0) and math.ceil(#transactionList / self._CELL_ROW_COUNT) or 1
        curRowCount = curCollectionRowCount + curTransactionRowCount
        loginfo("AuctionBuyView:_OnGetItemViewRowCount self._curMainIdx == ESpecialMainTab.Collect curRowCount:", curRowCount)
    else
        curRowCount = self._itemInfoList and math.ceil(#self._itemInfoList / self._CELL_ROW_COUNT) or 0
        loginfo("AuctionBuyView:_OnGetItemViewRowCount self._curMainIdx ~= ESpecialMainTab.Collect curRowCount:", curRowCount)
    end
    return curRowCount
end

function AuctionBuyView:_OnProcessItemViewRowWidget(position, itemWidget)
    local itemViewRowList = {}
    local animDelayTime
    if not self._showAnimFinishList[position] and self._wtItemViewScrollGridBox:GetVisibleItemCount() >= position - 1 then
        animDelayTime = (position - 1) * self._PLAY_SHOW_ANIM_DELTA_TIME
    end
    self._showAnimFinishList[position] = true
    if self._curMainIdx == ESpecialMainTab.Collect then
        local curCollectionRowCount = (self._CollectionList and #self._CollectionList > 0) and math.ceil(#self._CollectionList / self._CELL_ROW_COUNT) or 1
        if position <= curCollectionRowCount then
            local titleText = (position == 1) and Module.Auction.Config.Loc.MyCollection or nil
            itemWidget:InitRowList(self:GetItemViewRowList(position, self._CollectionList), CreateCallBack(self._OnBuyCellClicked, self),
             titleText, Module.Auction.Config.Loc.TypeListSubView_NoCollection, self._durabilityLvl, animDelayTime, true, CreateCallBack(self._OnItemViewHover, self))
        else
            local transactionList = AuctionLogic.GetRecentBougthRecord()
            local titleText = (position == (curCollectionRowCount + 1)) and Module.Auction.Config.Loc.BuyTransaction or nil
            itemWidget:InitRowList(self:GetItemViewRowList(position - curCollectionRowCount, transactionList), CreateCallBack(self._OnBuyCellClicked, self),
             titleText, Module.Auction.Config.Loc.TypeListSubView_NoRecord, self._durabilityLvl, animDelayTime, true, CreateCallBack(self._OnItemViewHover, self))
        end
    else
        for i = 1, self._CELL_ROW_COUNT do
            if self._itemInfoList[(position - 1) * self._CELL_ROW_COUNT + i] then
                table.insert(itemViewRowList, self._itemInfoList[(position - 1) * self._CELL_ROW_COUNT + i])
            end
        end
        itemWidget:InitRowList(self:GetItemViewRowList(position, self._itemInfoList), CreateCallBack(self._OnBuyCellClicked, self),
         nil, nil, self._durabilityLvl, animDelayTime, false, CreateCallBack(self._OnItemViewHover, self))
    end
end

function AuctionBuyView:GetItemViewRowList(position, itemInfoList)
    local itemViewRowList = {}
    for i = 1, self._CELL_ROW_COUNT do
        if itemInfoList[(position - 1) * self._CELL_ROW_COUNT + i] then
            table.insert(itemViewRowList, itemInfoList[(position - 1) * self._CELL_ROW_COUNT + i])
        end
    end
    return itemViewRowList
end

function AuctionBuyView:_OnItemViewHover(itemWidget)
    if itemWidget then
        self._curHoverItemView = itemWidget
    end
end

function AuctionBuyView:_OnProcessItemsUpdateFinished()
    if IsHD() and WidgetUtil.IsGamepad() then
        if self._isSearching then
            if self._SubNavGroup_Grid then
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._SubNavGroup_Grid)
            end
        end
    end
end

function AuctionBuyView:_OnGetCollectItemCount()
    return 2
end

function AuctionBuyView:_OnProcessCollectItemWidget(position, itemWidget)
    if position == 0 then -- 我的收藏
        local list = self._CollectionList
        itemWidget:InitCollection(list, Module.Auction.Config.Loc.MyCollection)
        itemWidget:SelfHitTestInvisible()
    elseif position == 1 then -- 购买记录
        local list = Server.AuctionServer:GetBoughtRecords()
        itemWidget:InitTransaction(list, Module.Auction.Config.Loc.BuyTransaction)
        itemWidget:SelfHitTestInvisible()
    end
end

function AuctionBuyView:_OnGroupBoxClicked(curIndex)
    -- 拉取最新数据
    local mainSlotType, subSlotType, allPrefixs = self:_GetTabInfo(self._curMainIdx, self._curSubIdx)
    if table.isempty(allPrefixs) then
        self:_UpdateView({})
    else
        Server.AuctionServer:FetchTypeList(allPrefixs, true)
    end
end

function AuctionBuyView:_OnBuyCellClicked(propId)
    if self._isSearching then
        AuctionLogic.AddSearchRecord(propId)
    end

    Module.Jump:JumpTo(Module.Jump.Config.EJumpToModule.Auction, {propId, nil, nil, false, self._durabilityLvl})
    --AuctionLogic.JumpToBuyDetailViewProcess(propId, nil, nil, false)
end

function AuctionBuyView:OnShopPostRefreshFunc(itemWidget, info)
    self:_FreshSaleInfoShow(itemWidget, info)
    -- self:_FreshMaskShow(itemWidget, info)
end

function AuctionBuyView:_FreshSaleInfoShow(itemWidget, info)
    if itemWidget and info then
        local item = ItemBase:NewIns(info.prop_id)
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        local isWeapon = weaponFeature and weaponFeature:IsWeapon()
        if isWeapon and WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id) then
            item = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
        end
    
        local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
        if equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
            if info.durability_lvl == EDurabilityLevel.New then
                item.name = StringUtil.Key2StrFormat(Module.Auction.Config.Loc.DurabilityLevelNew, {["name"] = item.name})
            elseif info.durability_lvl == EDurabilityLevel.Good then
                item.name = StringUtil.Key2StrFormat(Module.Auction.Config.Loc.DurabilityLevelGood, {["name"] = item.name})
            elseif info.durability_lvl == EDurabilityLevel.Worn then
                item.name = StringUtil.Key2StrFormat(Module.Auction.Config.Loc.DurabilityLevelWorn, {["name"] = item.name})
            end
        end
        local fHoverCallbackIns = CreateCallBack(self._OnItemViewHover, self, itemWidget)
        AuctionLogic.SetHoverdInstruction(self, itemWidget, item, info, nil, fHoverCallbackIns)
        itemWidget:InitAuctionItem(item, info, true)
        itemWidget:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomLeftIconText, false)
        item.id = info.prop_id
        -- if (equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate())) or isWeapon then
        --     itemWidget:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
        -- end
    end
end

function AuctionBuyView:_OnCancelSearchBtnClicked()
    local wtSearchBar = self:TryGetSearchBarPlatform()
    if wtSearchBar then
        wtSearchBar:SetTextBox("")
    end
        -- self:_OnFetchRecordList()
end

function AuctionBuyView:SearchProcess(text)
    self._wtDurabilityVerticalBox:Collapsed()
    self._wtDFTabV3GroupBox:Collapsed()
    self._isSearching = true
    local wtSearchBar = self:TryGetSearchBarPlatform()
    if wtSearchBar then
        wtSearchBar:_OnCheckBoxStateChanged(false)
    end
    if self._wtItemClassifyTabs then
        self._wtItemClassifyTabs:SetTabIndex(-1, 1) -- 搜索时收起全部页签
    end
    self._allMatchItemId = {}
    if not string.isempty(text) then
        if wtSearchBar then
            wtSearchBar:SetBtnDeleteVisible(true)
        end
        local itemTable = Module.Auction.Config.ItemTable
        local saleIds = Server.AuctionServer:GetPropSaleIds()
        for _, itemId in pairs(saleIds) do
            local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
            if itemMainType ~= EItemType.Receiver then
                local itemInfo = itemTable[itemId]
                if itemInfo then
                    local itemNameUpper = string.upper(tostring(itemInfo.Name))
                    local textUpper = string.upper(tostring(text))
                    if string.find(itemNameUpper, textUpper, nil, true) then
                        table.insert(self._allMatchItemId, itemId)
                    end
                end
            end
        end
    else
        if wtSearchBar then
            wtSearchBar:SetBtnDeleteVisible(false)
        end
    end
    loginfo("AuctionBuyView:_OnSearchTextChanged #_allMatchItemId:", #self._allMatchItemId)
    if table.isempty(self._allMatchItemId) then
        self:_UpdateView(self._allMatchItemId)
    else
        Server.AuctionServer:FetchTypeList(self._allMatchItemId, true)
    end
end

function AuctionBuyView:_OnSearchTextChanged(text)
    loginfo("AuctionBuyView:_OnSearchTextChanged text:", text)
    self:SearchProcess(text)
end

function AuctionBuyView:_OnSearchTextCommitted(text, commitType)

end

function AuctionBuyView:_GetPreSavedInputText(text, commitType)
    local customName = ""
    return customName
end

function AuctionBuyView:_OnInputBoxTextSaved(fixText, commitType)

end

function AuctionBuyView:_OnSearchRecordsChanged()
    -- if self._curMainIdx == ESpecialMainTab.Search and not self._isSearching then
    --     self:_OnFetchRecordList()
    -- end
end

---@param list pb_CSSaleType[]
function AuctionBuyView:_UpdateView(list)
    if self._isSearching then -- todo以此屏蔽搜狗输入法的重复输入问题，待MS24搜索逻辑优化
        if #list ~= #self._allMatchItemId then
            return
        end
    end
    -- 去除不在交易开放时间的道具
    self._itemInfoList = AuctionLogic.RemoveTransactionNoOpenSaleInfo(list) or {}
    -- 如果是搜索页签中，不需要排序，按照搜索顺序显示
    if not self._isSearching then
        if Module.Auction:GetAutoSortMode() then
            table.sort(self._itemInfoList, common_sort_mode_2)
        else
            table.sort(self._itemInfoList, common_sort_mode_1)
        end
        self._bAutoSortMode = Module.Auction:GetAutoSortMode()
    end
    if self._wtEmptyBgSlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot)
        self._wtEmptyBgSlot:Collapsed()
    end
    loginfo("AuctionBuyView:_UpdateView #self._itemInfoList = ", #self._itemInfoList)
    if #self._itemInfoList == 0 then
        -- self._wtCollectScrollBox:Collapsed()
        self._wtItemViewScrollGridBox:Collapsed()
        if self._wtEmptyBgSlot then
            if table.isempty(self._itemInfoList) then
                self._wtEmptyBgSlot:SelfHitTestInvisible()
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyBgSlot)
                local emptyBg = getfromweak(weakUIIns)
                if emptyBg then
                    emptyBg:Visible()
                    emptyBg:BP_SetText(Module.Auction.Config.Loc.TypeListSubView_NoCollection)
                    if self._isSearching then
                        emptyBg:BP_SetText(Module.Auction.Config.Loc.TypeListSubView_InvalidKey)
                    else
                        if self._curMainIdx == ESpecialMainTab.Collect then
                            emptyBg:BP_SetText(Module.Auction.Config.Loc.TypeListSubView_NoCollection)
                        else
                            emptyBg:BP_SetText(Module.Auction.Config.Loc.TypeListSubView_Nothing)
                        end
                    end
                    emptyBg:BP_SetTypeWithParam(1)
                end
            end
        end
    else
        -- self._wtCollectScrollBox:Collapsed()
        self._showAnimFinishList = {}
        self:RefreshScrollGridBox()
    end
end

---@param list pb_CSSaleType[]
function AuctionBuyView:_UpdateCollectionView(list)
    -- 去除不在交易开放时间的道具
    self._CollectionList = AuctionLogic.RemoveTransactionNoOpenSaleInfo(list)
    -- 如果是搜索页签中，不需要排序，按照搜索顺序显示
    if not self._isSearching then
        if Module.Auction:GetAutoSortMode() then
            table.sort(self._CollectionList, collection_sort_mode_2)
        else
            table.sort(self._CollectionList, collection_sort_mode_1)
        end
        self._bAutoSortMode = Module.Auction:GetAutoSortMode()
    end
    Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptyBgSlot)
    self:RefreshScrollGridBox()

    -- self._wtCollectScrollBox:Visible()
    -- for _, v in ipairs(self._wtCollectScrollBox:GetAllChildren()) do
    --     v:RemoveSubUI()
    --     v:Collapsed()
    -- end
    -- Facade.UIManager:RemoveSubUIByParent(self, self._wtCollectScrollBox)
    -- self._wtCollectScrollBox:SetScrollOffset(0)
    -- for i = 1, 2 do
    --     local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.AuctionCollectionPanel, self._wtCollectScrollBox)
    --     local CollectionPanel = getfromweak(weakUIIns)
    --     if CollectionPanel then
    --         if i == 1 then -- 我的收藏
    --             CollectionPanel:InitCollection(self._CollectionList, Module.Auction.Config.Loc.MyCollection)
    --             CollectionPanel:SelfHitTestInvisible()
    --         elseif i == 2 then -- 购买记录
    --             Timer.DelayCall(Module.Auction.Config.ItemViewDelayLoadInterval * #self._CollectionList, function()
    --                 CollectionPanel:InitTransaction(Server.AuctionServer:GetBoughtRecords(), Module.Auction.Config.Loc.BuyTransaction)
    --                 CollectionPanel:SelfHitTestInvisible()
    --             end, self)
    --         end
    --     end
    -- end
end

function AuctionBuyView:_OnSaleListChanged(saleList)
    local propId = nil
    for itemId, saleInfo in pairs(saleList) do -- 此处只有一个键值对，拿第一个id即可
        propId = itemId
        break
    end
    if not propId then
        return
    end
    local durabilityLvl = self._durabilityLvl or 0
    local itemMainType = ItemHelperTool.GetMainTypeById(propId)
    local itemSubType = ItemHelperTool.GetSubTypeById(propId)
    if itemMainType == EItemType.Receiver then -- 改装枪不需要及时更新在售页信息
        return
    end
    if self._curMainIdx == ESpecialMainTab.Collect and
        itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
        durabilityLvl = 1 -- 收藏页护甲只为全新
    end
    local itemViewWidgetList = {}
    for i = 1, self._wtItemViewScrollGridBox:GetLastVisibleItemIndex() do
        local itemWidgetRowList = self._wtItemViewScrollGridBox:GetItemByIndex(i)
        if itemWidgetRowList then
            for _, itemWidget in ipairs(itemWidgetRowList:GetItemViewWidgetRowList()) do
                if itemWidget and itemWidget.item and saleList and saleList[durabilityLvl] and
                 itemWidget.item.id == saleList[durabilityLvl].prop_id then
                    self:OnShopPostRefreshFunc(itemWidget, saleList[durabilityLvl])
                    return
                end
            end
        end
    end
end

function AuctionBuyView:_OnRelayConnected()
    logwarning("AuctionBuyView:_OnRelayConnected")
    self:_OnTabClicked(self._curMainIdx, self._curSubIdx)
end

function AuctionBuyView:RefreshScrollGridBox()
    loginfo("AuctionBuyView:RefreshScrollGridBox")
    self._wtItemViewScrollGridBox:Visible()
    self._wtItemViewScrollGridBox:RefreshAllItems()
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function AuctionBuyView:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._SubNavGroup_SearchBar == nil then
        -- -- 搜索框
        -- local SearchBar = self:TryGetSearchBarPlatform()
        -- self._SubNavGroup_SearchBar = WidgetUtil.RegisterNavigationGroup(SearchBar, self, "Hittest")
        -- if self._SubNavGroup_SearchBar then
        --     self._SubNavGroup_SearchBar:AddNavWidgetToArray(SearchBar)
        -- end
        -- 右侧筛选Tab
        if self._wtDFTabV3GroupBox.CustomTabContainer and self._wtDFTabV3GroupBox.CustomTabContainer:GetAllChildren() then
            for _, tab in ipairs(self._wtDFTabV3GroupBox.CustomTabContainer:GetAllChildren()) do
                tab:SetCppValue("bIsFocusable", true)
            end
        end
        self._SubNavGroup_TabGroup = WidgetUtil.RegisterNavigationGroup(self._wtDFTabV3GroupBox, self, "Hittest")
        if self._SubNavGroup_TabGroup then
            self._SubNavGroup_TabGroup:AddNavWidgetToArray(self._wtDFTabV3GroupBox)
        end
        -- 左侧筛选下拉框
        local TabScrollView = self._wtItemClassifyTabs:Wnd("DFCommonSecondTab", UIWidgetBase):Wnd("MainTabList", UIWidgetBase)
        self._SubNavGroup_TabScroll = WidgetUtil.RegisterNavigationGroup(TabScrollView, self, "Hittest")
        if self._SubNavGroup_TabScroll then
            self._SubNavGroup_TabScroll:AddNavWidgetToArray(TabScrollView)
            self._SubNavGroup_TabScroll:SetScrollRecipient(TabScrollView)
            WidgetUtil.BindCustomBoundaryNavRule(self._SubNavGroup_TabScroll, self._CustomBoundaryRule, self)
        end
        -- 右侧显示具体Item的Box
        self._SubNavGroup_Grid = WidgetUtil.RegisterNavigationGroup(self._wtItemViewScrollGridBox, self, "Hittest")
        if self._SubNavGroup_Grid then
            self._SubNavGroup_Grid:AddNavWidgetToArray(self._wtItemViewScrollGridBox)
            self._SubNavGroup_Grid:SetScrollRecipient(self._wtItemViewScrollGridBox)

            WidgetUtil.BindCustomFocusProxy(self._SubNavGroup_Grid, self._GirdBoxFocusProxyMaker, self._GirdBoxFocusProxyResolver, self)

        end

        if not WidgetUtil.TryFocusLastGroupByOwner(self) then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._SubNavGroup_TabScroll)
        end

        self._SearchHandle = self:AddInputActionBinding("AuctionSearch", EInputEvent.IE_Pressed, self._StartSearch,self, EDisplayInputActionPriority.UI_Stack)
        self:_InitShortcutsCommon()
    end
end

function AuctionBuyView:_InitShortcutsCommon()
    -- 设置图标显示：确认（A）返回（B）
    local summaryList = {}
    table.insert(summaryList, {actionName = "Back_Gamepad",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    table.insert(summaryList, {actionName = "AuctionSearch",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    table.insert(summaryList, {actionName = "Select_Gamepad",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function AuctionBuyView:_RemoveShortcutsCommon()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function AuctionBuyView:_InitShortcutsSearching()
    -- 设置图标显示：确认（A）返回（B）
    local summaryList = {}
    table.insert(summaryList, {actionName = "Back_Gamepad",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    table.insert(summaryList, {actionName = "AuctionSearchConfirm",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    table.insert(summaryList, {actionName = "AuctionSearchCancel",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function AuctionBuyView:_RemoveShortcutsSerarching()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function AuctionBuyView:_CustomBoundaryRule(direction)
    local TabScrollView = self._wtItemClassifyTabs:Wnd("DFCommonSecondTab", UIWidgetBase):Wnd("MainTabList", UIWidgetBase)
    if direction == EUINavigation.Down then
        TabScrollView:ScrollToIndex(1)
        local item = TabScrollView:GetItemByIndex(1)
        return item
    elseif direction == EUINavigation.Up then
        local lastIdx = TabScrollView:GetItemCount()
        TabScrollView:ScrollToIndex(lastIdx)
        local item = TabScrollView:GetItemByIndex(lastIdx)
        return item
    else
        return nil
    end
end

function AuctionBuyView:_GirdBoxFocusProxyMaker(inWidget)
    -- 找到所在行
    local row = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_Auction_ItemViewRowList_C")
    local rowIndex = self._wtItemViewScrollGridBox:GetIndexByItem(row)
    -- 找到所在列
    local template = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_ShopItemTemplate_C")
    local colIndex = row:GetIndexByItem(template)
    return {rowIndex, colIndex}
end

function AuctionBuyView:_GirdBoxFocusProxyResolver(inProxyHandle)
    local rowIndex = inProxyHandle[1]
    local colIndex = inProxyHandle[2]

    -- 可能item会在屏幕外，先执行滚动
    -- self._wtItemViewScrollGridBox:ScrollToIndexToScreen(rowIndex, 0.5, 0.5)
    local row = self._wtItemViewScrollGridBox:GetItemByIndex(rowIndex)
    if row then
        return row:GetItemByIndex(colIndex)
    end

    -- 可能会找不到，返回空，自动使用Gorup的默认逻辑查找聚焦
    return nil
end

function AuctionBuyView:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    WidgetUtil.RemoveNavigationGroup(self)
    -- self._SubNavGroup_SearchBar = nil
    self._SubNavGroup_TabGroup = nil
    self._SubNavGroup_TabScroll = nil
    self._SubNavGroup_Grid = nil
    self:_RemoveShortcutsCommon()
end

function AuctionBuyView:_StartSearch()
    local SearchBar = self:TryGetSearchBarPlatform()
    if SearchBar then
        SearchBar:EnterSearch()
        self:_RemoveShortcutsCommon()
        self:_InitShortcutsSearching()
    end
end
-- END MODIFICATION

return AuctionBuyView