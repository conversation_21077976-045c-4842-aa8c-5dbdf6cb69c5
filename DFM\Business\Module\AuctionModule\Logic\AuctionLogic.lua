----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMAuction)
----- LOG FUNCTION AUTO GENERATE END -----------



local AuctionLogic = {}
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local AuctionConfig  = require "DFM.Business.Module.AuctionModule.AuctionConfig"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local SEARCH_RECORDS_KEY = "AuctionSearchRecords"
local SEARCH_RECORDS_MAX_NUM = 30
local function log(...)
    print("Lzw", "[AuctionLogic]", ...)
end
AuctionLogic.AddListeners = function()
    Server.AuctionServer.Events.evtBatchBuyGoodPromiseAllProcess:AddListener(AuctionLogic._BatchBuyGoodPromiseAllProcess)
    Server.GunsmithServer.Events.evtSplitSaleWeaponPostProcess:AddListener(AuctionLogic.SplitSaleWeaponPostProcess)
    Server.AuctionServer.Events.evtGenCollectionList:AddListener(AuctionLogic._GenCollectionList)
    Server.InventoryServer.Events.evtItemMarkStatusChanged:AddListener(AuctionLogic._GenCollectionList)
    Server.InventoryServer.Events.evtItemMove:AddListener(AuctionLogic.OnItemMove)
end

AuctionLogic.RemoveListeners = function()
    if Server.AuctionServer then
        Server.AuctionServer.Events.evtBatchBuyGoodPromiseAllProcess:RemoveListener(AuctionLogic._BatchBuyGoodPromiseAllProcess)
        Server.GunsmithServer.Events.evtSplitSaleWeaponPostProcess:RemoveListener(AuctionLogic.SplitSaleWeaponPostProcess)
        Server.AuctionServer.Events.evtGenCollectionList:RemoveListener(AuctionLogic._GenCollectionList)
    end
    if Server.InventoryServer then
        Server.InventoryServer.Events.evtItemMarkStatusChanged:RemoveListener(AuctionLogic._GenCollectionList)
        Server.InventoryServer.Events.evtItemMove:RemoveListener(AuctionLogic.OnItemMove)
    end
end

AuctionLogic._OnDeviceUpgradeEvent = function(deviceIds)
    if table.contains(deviceIds,1001) then --星链信号塔
        Server.AuctionServer:FetchPlayerInfo()
    end
end

--------------------------------------------------------------------------
--- 单价格点购买拍卖行商品
--------------------------------------------------------------------------
---@param propInfo pb_PropInfo 单价格点的物品PropInfo
---@param buyNum number
---@param equipPos number 购买后装备的位置
function AuctionLogic.DoBuyGoodProcess(propInfo, buyNum, equipPos)
    loginfo("AuctionLogic.DoBuyGoodProcess")
    local totalPrice = propInfo.price * buyNum
    local currencyClientType = MapCurrencyId2ClientType[propInfo.price_currency]
    local playerMoney = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
    if playerMoney < totalPrice then
        -- ShopHelperTool.ShowNotEnoughTipByCurrencyType(propInfo.price_currency)
        ShopHelperTool.ShowNotEnoughTipByCurrencyId(propInfo.price_currency)
        return
    end

    Server.AuctionServer:DoBuyGoodReq(propInfo, buyNum, equipPos)
end

--------------------------------------------------------------------------
--- 跨价格点购买拍卖行商品
--------------------------------------------------------------------------
---@param propInfo pb_PropInfo 单价格点的物品PropInfo
---@param buyNum number
---@param equipPos number 购买后装备的位置
function AuctionLogic.DoMultiPriceBuyProcess(itemId, buyNum, preProps, preTotalPrice, fCallback)
    loginfo("AuctionLogic.DoMultiPriceBuyProcess")
    if not buyNum or (buyNum and buyNum <= 0) then
        return
    end
    Server.AuctionServer:DoMultiPriceBuyReq(preProps, buyNum, preProps, preTotalPrice, fCallback)
    -- Long2去除前台购买前新价格拉取
    -- local function DoMultiPriceBuyCallback()
    --     local latestSaleList = {}
    --     local saleInfo = Server.AuctionServer:GetPropSaleInfo(itemId, preProps[1][1].prop.durability_lvl)
    --     if saleInfo then
    --         latestSaleList = saleInfo and saleInfo.sale_lists or {}
    --         -- 直接对server缓存进行升序排序
    --         local function ASCSort(a, b)
    --             local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    --             local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    --             if itemMainType == EItemType.Equipment and (itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate) then
    --                 local aDurability = a.prop.health / (a.prop.health_max + 1e-6)
    --                 local bDurability = b.prop.health / (b.prop.health_max + 1e-6)
    --                 if aDurability ~= bDurability then
    --                     return aDurability < bDurability
    --                 end
    --             else
    --                 return a.price < b.price
    --             end
    --         end
    --         table.insertionSort(latestSaleList, ASCSort)
    
    --         local props
    --         if preProps and preTotalPrice then
    --             props = {}
    --             local latestSaleNum = {}
    --             for key, value in pairs(latestSaleList) do
    --                 latestSaleNum[key] = latestSaleNum[key] or {}
    --                 latestSaleNum[key].price = value.price
    --                 latestSaleNum[key].prop = latestSaleNum[key].prop or {}
    --                 latestSaleNum[key].prop.num = value.prop.num
    --             end
    --             local buyNumForCount = {}
    --             for key, value in pairs(preProps) do
    --                 local curPropNum = value[2]
    --                 local curPropPrice = value[1].price
    --                 for k, v in pairs(latestSaleNum) do
    --                     if curPropNum == 0 then
    --                         break
    --                     end
    --                     if v.prop.num ~= 0 and v.price <= curPropPrice then
    --                         if v.prop.num <= curPropNum then
    --                             buyNumForCount[k] = buyNumForCount[k] or 0
    --                             buyNumForCount[k] = buyNumForCount[k] + v.prop.num
    --                             curPropNum = curPropNum - v.prop.num
    --                             v.prop.num = 0
    --                         else
    --                             buyNumForCount[k] = buyNumForCount[k] or 0
    --                             buyNumForCount[k] = buyNumForCount[k] + curPropNum
    --                             v.prop.num = v.prop.num - curPropNum
    --                             break
    --                         end
    --                     end
    --                 end
    --             end
    --             for key, value in pairs(latestSaleList) do
    --                 if buyNumForCount[key] and buyNumForCount[key] > 0 then
    --                     local data = {}
    --                     data[1] = value
    --                     data[2] = buyNumForCount[key]
    --                     table.insert(props, data)
    --                 end
    --             end
    --         else -- 供拍卖行外部调用直接购买
    --             local totalPrice
    --             props, totalPrice = AuctionLogic.checkSaleListToBuy(latestSaleList, buyNum)
    --             if #props == 0 then
    --                 local itemInfo = ItemConfigTool.GetItemConfigById(itemId)
    --                 --标准预设枪处理
    --                 local type = ItemHelperTool.GetMainTypeById(itemId)
    --                 if type == EItemType.Weapon then
    --                     local functionId = WeaponAssemblyTool.GetFunctionIdByPresetID(itemId, EArmedForceMode.SOL)
    --                     itemInfo = ItemConfigTool.GetItemConfigById(functionId)
    --                 end
    --                 Module.CommonTips:ShowSimpleTip(string.format(AuctionConfig.Loc.ItemHasBeenSoldOut, itemInfo.Name))
    --                 return
    --             else
    --                 local currencyClientType = MapCurrencyId2ClientType[props[1][1].price_currency]
    --                 local playerMoney = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
    --                 if playerMoney < totalPrice then
    --                     -- ShopHelperTool.ShowNotEnoughTipByCurrencyType(props[1][1].price_currency)
    --                     ShopHelperTool.ShowNotEnoughTipByCurrencyId(props[1][1].price_currency)
    --                     return
    --                 end
    --             end
    --         end
    --         Server.AuctionServer:DoMultiPriceBuyReq(props, buyNum, preProps, preTotalPrice, fCallback)
    --     end
    -- end
    -- -- 拉取拍卖行最新数据再进行购买
    -- Server.AuctionServer:FetchSaleList(itemId, nil, DoMultiPriceBuyCallback)
end

function AuctionLogic.checkSaleListToBuy(saleList, buyNum)
    if saleList and buyNum then
        local props = {}
        local numInProps = 0
        local saleListsNum = 0
        local totalPrice = 0
        for index, value in ipairs(saleList) do
            numInProps = numInProps + value.prop.num
            saleListsNum = saleListsNum + 1
            if numInProps >= buyNum then
                local data = {}
                data[1] = value
                data[2] = buyNum - numInProps + value.prop.num
                props[index] = data
                totalPrice = totalPrice + value.price * (buyNum - numInProps + value.prop.num)
                break
            else
                local data = {}
                data[1] = value
                data[2] = value.prop.num
                props[index] = data
                totalPrice = totalPrice + value.price * value.prop.num
            end
        end
        return props, totalPrice
    end
end

--------------------------------------------------------------------------
--- 批量购买拍卖行商品
--------------------------------------------------------------------------
---@param itemList table<number, number> id和数量
function AuctionLogic.DoBatchBuyGoodReq(itemList)
    if table.isempty(itemList) then
        return
    end
    local propIds = table.keys(itemList)
    Module.Auction.Field:SetBatchBuyList(itemList)
    local function fDoBatchBuyGoodReq()
        local props = {}
        local originalSaleListInfo = Module.Auction.Field:GetOriginalSaleListInfo()
        if originalSaleListInfo then
            --根据道具id和数量，分批购买道具
            for itemId, neednum in pairs(itemList) do
                --(此处需要注意，originalSaleListInfo的数据对应的是安全屋pop显示界面中核对过的商品信息，需要购买次对应的prop道具与数量)
                local Sale = table.find(originalSaleListInfo, function(data, _) return data[1] == itemId end)
                if Sale then
                    for _, propInfo in pairs(Sale[2].props) do
                        table.insert(props,propInfo)
                    end
                end
            end
            --请求购买
            Server.AuctionServer:DoBatchBuyGoodReq(props, propIds)
        end
    end
    --先批量拉取最新道具列表表的出售信息
    Server.AuctionServer:FetchSaleListBatch(propIds,fDoBatchBuyGoodReq)
end

--------------------------------------------------------------------------
--- 在拍卖行出售道具
--------------------------------------------------------------------------
---@param target ItemBase
function AuctionLogic.DoSellGoodProcess(target, num, price, sellFee, managementFee, durationIndex, portion, currencyId)
    loginfo("AuctionLogic.DoSellGoodProcess")
    local props = {}
    local remainNum = num

    local weaponFeature = target:GetFeature(EFeatureType.Weapon)
    local equipmentFeature = target:GetFeature(EFeatureType.Equipment)
    local batchItems = {}
    if weaponFeature and weaponFeature:IsWeapon() then --枪械
        batchItems = {target}
    elseif equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then --护具
        batchItems = Server.AuctionServer:GetSameArmorItems(target)
    else --其他
        batchItems = Server.AuctionServer:GetSameTypeItems(target)
    end

    for _, item in ipairs(batchItems) do
        local propInfo = clone(item:GetRawPropInfo())
        if not propInfo then
            return false
        end

        -- 想要出售的剩余的数量，少于这一组物品的数量
        if item.num > remainNum then
            propInfo.num = remainNum
        end
        table.insert(props, propInfo)
        remainNum = remainNum - propInfo.num
        
        -- 想要出售的物品数量，已经盘点完毕
        if remainNum == 0 then
            break
        end
    end

    -- 上架管理费够不够
    local currencyClientType = MapCurrencyId2ClientType[currencyId]
    local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
    if currencyNum and managementFee and currencyNum < managementFee then
        ShopHelperTool.ConfirmExchangeBankNote(managementFee - currencyNum)
        return false
    end

    Server.AuctionServer:DoSellGoodReq(props, currencyId, price, sellFee, managementFee, durationIndex, portion)
    return true
end

--------------------------------------------------------------------------
--- 在拍卖行拆分出售改装枪械
--------------------------------------------------------------------------
---@param target ItemBase
function AuctionLogic.DoSplitSellWeaponProcess(item, partInfos)
    local socketGUIDMap = {}
    loginfo("AuctionLogic.DoSplitSellWeaponProcess")
    local bAuctionUnlock = Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionSell)
    if not bAuctionUnlock then
        Module.CommonTips:ShowSimpleTip(ServerTipCode.AuctionSellLocked)
        return
    end
    -- 上架管理费够不够
    local saleInfo = Server.AuctionServer:GetPropSaleInfo(item.id)
    local currencyClientType = MapCurrencyId2ClientType[saleInfo.guide_currency]
    local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType)
    local totalManagementFee = 0
    for _, partInfo in ipairs(partInfos) do
        totalManagementFee = totalManagementFee + partInfo.insurance_fee
    end
    if currencyNum and totalManagementFee and currencyNum < totalManagementFee then
        ShopHelperTool.ConfirmExchangeBankNote(totalManagementFee - currencyNum)
        return false
    end

    local rootPropInfo = clone(item:GetRawPropInfo())
    local weapDesc = WeaponAssemblyTool.PropInfo_To_Desc(rootPropInfo)
    local removeSocketGUIDs = {}
    local bSplitAll = false
    for _, partInfo in ipairs(partInfos) do
        local itemMainType = ItemHelperTool.GetMainTypeById(partInfo.id)
        if itemMainType and itemMainType == EItemType.Weapon then
            bSplitAll = true
            break
        end
    end
    if bSplitAll then -- 配件全部卸下
        local itemComponents = {}
        Server.AuctionServer:GetComponents(item.components, itemComponents)
        for _, component in ipairs(itemComponents) do
            declare_if_nil(socketGUIDMap, component.prop_data.id, {})
            local socketGUID = AuctionLogic.GetSocketGUID(weapDesc, component.prop_data.id, socketGUIDMap[component.prop_data.id])
            socketGUIDMap[component.prop_data.id][socketGUID] = true
            if socketGUID then
                table.insert(removeSocketGUIDs, socketGUID)
            end
        end
    else
        for _, partInfo in ipairs(partInfos) do
            local sellNum = partInfo.sellNum or 1
            for i = 1, sellNum do
                declare_if_nil(socketGUIDMap, partInfo.id, {})
                local socketGUID = AuctionLogic.GetSocketGUID(weapDesc, partInfo.id, socketGUIDMap[partInfo.id])
                socketGUIDMap[partInfo.id][socketGUID] = true
                if socketGUID then
                    table.insert(removeSocketGUIDs, socketGUID)
                end
            end
        end
    end
    if not table.isempty(removeSocketGUIDs) then
        table.sort(removeSocketGUIDs, function(a, b) return a < b end)
        for _, socketGUID in ipairs(removeSocketGUIDs) do
            UAssembleWeaponDataLibrary.AutoRemoveNodeFromDescWithSocketGUID(weapDesc, socketGUID, false)
        end
    end
    local presetWeaponPropInfo = WeaponAssemblyTool.Desc_To_PropInfo(weapDesc)
    local function fOnResCallback(result, newGun)
        if result ~= 0 then
            Module.Auction.Field:SetSplitSaleInfo() -- 拆分失败，清空，中断上架
        end
    end
    Module.Auction.Field:SetSplitSaleInfo(partInfos)
    Server.GunsmithServer:CSWAssemblyDepositPropUpdateReq(presetWeaponPropInfo, fOnResCallback, nil, nil, 0, nil, nil, nil, true)
end

function AuctionLogic.SplitSaleWeaponPostProcess(res, newGun)
    loginfo("AuctionLogic.SplitSaleWeaponPostProcess")
    if res.result == 0 then
        -- 根据partInfos找出拆出待售的物品并计算相应税费
        local partInfos = Module.Auction.Field:GetSplitSaleInfo()
        if not partInfos then
            return
        end
        for index, partInfo in ipairs(partInfos) do
            local props = {}
            for _, prop in pairs(partInfo.props) do
                local curGid = prop.gid
                local itemMainType = ItemHelperTool.GetMainTypeById(partInfo.id)
                if itemMainType == EItemType.Weapon then
                    curGid = newGun.gid
                end
                local playerItems = Server.InventoryServer:GetItemsIterator(ESlotGroup.Player)
                for _, eachItem in playerItems do
                    if eachItem.gid == curGid and Server.AuctionServer:CheckPropCanSell(eachItem) then
                        local propInfo = clone(eachItem:GetRawPropInfo())
                        if propInfo then
                            table.insert(props, propInfo)
                            break
                        else
                            logerror("AuctionLogic.SplitSaleWeaponPostProcess No such propInfo in Inventory item id:", eachItem.id ,"item gid:",eachItem.gid)
                        end
                    end
                end
            end
            partInfo.props = props
        end
        Server.AuctionServer:DoBatchSellReq(partInfos)
        Module.Auction.Field:SetSplitSaleInfo()
    end
end

function AuctionLogic.GetSocketGUID(desc, itemId, socketGUIDMap)
    local nodes = desc.WeaponParts
    for _, value in pairs(nodes) do
        local socketGUID = value.SocketGUID
        local itemID = value.ItemId
        if itemID == itemId and not socketGUIDMap[socketGUID] then
            return socketGUID
        end
    end
end

function AuctionLogic.DoReSplitSellWeaponProcess(item, partInfos, order)
    local function fReSplitSellWeaponCallback()
        local realItem = Module.Auction.Field:GetSplitSaleItem()
        AuctionLogic.DoSplitSellWeaponProcess(realItem, partInfos)
        Module.Auction.Field:SetSplitSaleItem()
    end
    Server.AuctionServer:DoPullOffGoodReq(order, order.prop.num, fReSplitSellWeaponCallback)
end

function AuctionLogic.OnItemMove(moveItemInfo)
    if moveItemInfo then
        if moveItemInfo.ChangeReason == ePropChangeReason.AuctionPulloff then
            Module.Auction.Field:SetSplitSaleItem(moveItemInfo.item)
        end
    end
end

-- 卸下武器子弹操作
function AuctionLogic.WeaponBulletRemoveOperation(playerWeapon, fCallback)
    local lastCommonds = {}
    if playerWeapon then
        local propInfo = playerWeapon:GetRawPropInfo()
        local bullets = WeaponAssemblyTool.GetWeaponBullets(propInfo)
        if not table.isempty(bullets) then
            for _, bulletPropInfo in ipairs(bullets) do
                local id = bulletPropInfo.id
                local gid = bulletPropInfo.gid
                local operateNum = bulletPropInfo.num
                logwarning(string.format("AuctionLogic.WeaponBulletRemoveOperation 武器卸下子弹 playerWeapon.id = %s, playerWeapon.name= %s , id = %s, operateNum = %s", playerWeapon.id, playerWeapon.name, id, operateNum))
                Module.Gunsmith:AddBulletOperateCmd(lastCommonds, BulletOperateType.eBulletOperate_UnLoad, id, gid, operateNum, playerWeapon.id, playerWeapon.gid)
            end
        end
    end
    local function fCSDepositOperateBulletRes(res)
        if fCallback then
            fCallback(res)
        end
    end
    -- 发送子弹操作协议
    if not table.isempty(lastCommonds) then
        Server.GunsmithServer:CSDepositOperateBulletReq(lastCommonds, fCSDepositOperateBulletRes, false)
    else
        if fCallback then
            fCallback()
        end
    end
end

function AuctionLogic.OffSelfOrder(order)
    if order then
        if order.state == OrderState.OrderRacked then
            local itemInfo = ItemConfigTool.GetItemConfigById(order.prop.id)
            local itemName = itemInfo.Name
            local type = ItemHelperTool.GetMainTypeById(order.prop.id)
            if type == EItemType.Weapon then
                local functionId = WeaponAssemblyTool.GetFunctionIdByPresetID(order.prop.id, EArmedForceMode.SOL)
                local functionItem = ItemBase:NewIns(functionId)
                itemName = functionItem.name
            end
            local title = AuctionConfig.Loc.PullOffWindowTitle
            local text =  string.format(AuctionConfig.Loc.PullOffWindowText, itemName)
            local cancelTxt = AuctionConfig.Loc.PullOffCancel
            local confirmTxt = AuctionConfig.Loc.PullOffConfirm
            local confirmSound = DFMAudioRes.UIAHOffShelfConfirm
            local confirmHandle = function(num)
                num = num or 1
                Server.AuctionServer:DoPullOffGoodReq(order, num)
            end
            if order.prop.num > 1 then --聚合的道具可以选择下架数量
                Facade.UIManager:AsyncShowUI(UIName2ID.AuctionOffShelfCountableWindow, nil, nil,
                    text, Module.Auction.Config.Loc.TheShelvesNumber, nil, order.prop.num, confirmHandle, nil, cancelTxt, confirmTxt, confirmSound)
            else
                Module.CommonTips:ShowConfirmWindow(text, confirmHandle, nil, cancelTxt, confirmTxt, confirmSound)
            end
            return
        end
        Server.AuctionServer:DoPullOffGoodReq(order, order.prop.num)
    end
end

--------------------------------------------------------------------------
--- 搜索记录相关
--------------------------------------------------------------------------
function AuctionLogic.GetSearchRecords()
    loginfo("AuctionLogic.GetSearchRecords")
    return Facade.ConfigManager:GetUserArray(SEARCH_RECORDS_KEY, {})
end

function AuctionLogic.RemoveSearchRecord(propId)
    loginfo("AuctionLogic.RemoveSearchRecord")
    local tmpRecords = Facade.ConfigManager:GetUserArray(SEARCH_RECORDS_KEY, {})
    table.removebyvalue(tmpRecords, propId)
    Facade.ConfigManager:SetUserArray(SEARCH_RECORDS_KEY, tmpRecords)
    Module.Auction.Config.Events.evtSearchRecordsChanged:Invoke(tmpRecords)
end

function AuctionLogic.AddSearchRecord(propId)
    loginfo("AuctionLogic.AddSearchRecord")
    local tmpRecords = Facade.ConfigManager:GetUserArray(SEARCH_RECORDS_KEY, {})
    if not table.contains(tmpRecords, propId) then
        table.insert(tmpRecords, 1, propId)
    else
        table.removebyvalue(tmpRecords, propId)
        table.insert(tmpRecords, 1, propId)
    end
    
    if #tmpRecords > SEARCH_RECORDS_MAX_NUM then
        table.remove(tmpRecords, #tmpRecords)
    end
    Facade.ConfigManager:SetUserArray(SEARCH_RECORDS_KEY, tmpRecords)
    Module.Auction.Config.Events.evtSearchRecordsChanged:Invoke(tmpRecords)
end


--------------------------------------------------------------------------
--- 获取道具初始建议售卖价格
--------------------------------------------------------------------------
function AuctionLogic.GetInputGuidePrice(item, saleInfo)
    if not item then
        return 0
    end
    if not saleInfo then
        return 0
    end
    local guidePrice = table.isempty(saleInfo.sale_lists) and saleInfo.guide_price or saleInfo.sale_lists[1].price
    local shopBuyPrice = AuctionLogic.CheckItemPurchasePriceInShop(item.id)
    guidePrice = (guidePrice and shopBuyPrice) and math.min(shopBuyPrice - 1, guidePrice) or guidePrice
    return guidePrice or 0
end

--------------------------------------------------------------------------
--- 获取武器，以及其组件的回收价
--------------------------------------------------------------------------
---@param item ItemBase
function AuctionLogic.GetWeaponGuidePrice(item)
    local sellPrice = 0
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsWeapon() then
        local function GetSellPriceFromPropInfo(propInfo)
            if not propInfo.num then
                loginfo("propInfo num should not be zero, id = ", propInfo.id)
                return 0
            end
            local num = math.max(propInfo.num, 1)
            local comTotalSellPrice = Server.AuctionServer:GetSimpleAuctionPrice(propInfo.id) * num
            for _, component in ipairs(propInfo.components) do
                comTotalSellPrice = comTotalSellPrice + GetSellPriceFromPropInfo(component.prop_data)
            end
            return comTotalSellPrice
        end
        
        -- 注：Components里面是没有机匣的数据的
        if item.rawPropInfo then
            sellPrice = sellPrice + GetSellPriceFromPropInfo(item.rawPropInfo)
        elseif item.RawDescObj then
            sellPrice = 0
            for key, part in pairs(item.RawDescObj:GetAllParts()) do
                sellPrice = sellPrice + Server.AuctionServer:GetSimpleAuctionPrice(part.ItemId) * part.Num
            end
        end
    else
        sellPrice = Server.AuctionServer:GetSimpleAuctionPrice(item.id)
    end
    return sellPrice
end

--------------------------------------------------------------------------
--- 获取护具回收价（不同档次）（交易行新内存设计下废弃）
--------------------------------------------------------------------------
---@param item ItemBase
function AuctionLogic.GetArmorGuidePrice(item)
    if not item then
        return 0
    end
    local durabilitylvl = Server.AuctionServer:CheckEquipmentLevel(item)
    local saleInfo = Server.AuctionServer:GetPropSaleInfo(item.id, durabilitylvl)
    if not saleInfo then
        return 0
    end
    local guidePrice = table.isempty(saleInfo.sale_lists) and saleInfo.guide_price or saleInfo.sale_lists[1].price
    local shopBuyPrice = AuctionLogic.CheckItemPurchasePriceInShop(item.id)
    guidePrice = (guidePrice and shopBuyPrice) and math.min(shopBuyPrice - 1, guidePrice) or guidePrice
    return guidePrice or 0
end

-----------------------------------------------View-----------------------------------------------

--------------------------------------------------------------------------
--- 跳转至拍卖行主页面(AuctionMainView)
--------------------------------------------------------------------------
---@param subViewType EAuctionSubViewType
function AuctionLogic.JumpToMainPanelProcess(subViewType)
    Facade.UIManager:AsyncShowUI(UIName2ID.AuctionMainPanel, nil, nil, subViewType)
end

--------------------------------------------------------------------------
--- 跳转至拍卖行购买页指定页签
--------------------------------------------------------------------------
---@param pageSubId string
function AuctionLogic.JumpToPagePanelProcess(pageSubId)
    local mapMainId2PageInfoList = Module.Auction.Field:GetPageInfoMap()
    if pageSubId and not table.isempty(mapMainId2PageInfoList) then
        for mainId, pageInfoList in pairs(mapMainId2PageInfoList) do
            if not table.isempty(pageInfoList) then
                for _, pageInfo in ipairs(pageInfoList) do
                    if pageInfo.PageSubId == pageSubId then
                        Module.Auction.Field:SetMainTabStartIndex(pageInfo.MainPageOrder)
                        Module.Auction.Field:SetSubTabStartIndex(pageInfo.SubPageOrder)
                        Facade.UIManager:AsyncShowUI(UIName2ID.AuctionMainPanel, nil, nil, EAuctionSubViewType.TypeList)
                        return
                    end
                end
            end
        end
    end
    Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.NoSuchItemForSale)
    return
end

--------------------------------------------------------------------------
--- 跳转至拍卖行关联搜索页面
--------------------------------------------------------------------------
---@param itemId number
function AuctionLogic.JumpToRelatedSearchPanelProcess(itemId)
    local allMatchItemIds = AuctionLogic.SetRelatedSearchData(itemId) or {}
    if table.isempty(allMatchItemIds) then
        return
    end
    local function onUILoaded(ui)
        if Module.Auction.Field:GetRelatedSearchIds() and not table.isempty(Module.Auction.Field:GetRelatedSearchIds()) then
            Server.AuctionServer:FetchTypeList(Module.Auction.Field:GetRelatedSearchIds(), true)
        end
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.AuctionMainPanel, onUILoaded, nil, subViewType)
end

--------------------------------------------------------------------------
--- 跳转至购买详情页面
--------------------------------------------------------------------------
function AuctionLogic.JumpToBuyDetailViewProcess(itemId, equipPos, callback, bExternalCall, durabilityLvl)
    bExternalCall = setdefault(bExternalCall, true)
    durabilityLvl = setdefault(durabilityLvl, 0)
    local openUINavIdList = {}  -- 跳转接口返回 按顺序实际需要打开/重新显示的所有界面id
    if itemId then
        local item = ItemBase:NewIns(itemId, 1, 0, false, ESlotGroup.None)
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
        local type = ItemHelperTool.GetMainTypeById(item.id)
        local function onUILoaded(ui)
            if Server.AuctionServer:CheckIsInSaleList(itemId) then
                local param = {
                    skip = nil,
                    limit = nil
                }
                Server.AuctionServer:FetchSaleList(itemId, param)
            else
                ui:Init()
            end
            if bExternalCall then
                LogAnalysisTool.DoSendAuctionEnterCountFlowLog()
            end
        end
        -- 非标准预设枪械且是模块内部调用
        if weaponFeature and weaponFeature:IsWeapon() and type ~= EItemType.Weapon and not bExternalCall then
            Facade.UIManager:AsyncShowUI(UIName2ID.SaleListViewWeapon, onUILoaded, nil, Module.Auction.Config.ESaleType.Weapon, item, equipPos, callback, durabilityLvl)
            table.insert(openUINavIdList, UIName2ID.SaleListViewWeapon)
            -- Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowBuy, onUILoaded, nil, Module.Auction.Config.ESaleListWindowType.Buy, Module.Auction.Config.ESaleType.Weapon, item, equipPos, callback, durabilityLvl)
            -- table.insert(openUINavIdList, UIName2ID.SaleListWindowBuy)
        -- 防具
        elseif equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
            if durabilityLvl == 0 then
                durabilityLvl = 1 -- 拍卖行外部调用默认购买全新防具
            end
            Facade.UIManager:AsyncShowUI(UIName2ID.SaleListViewArmor, onUILoaded, nil, Module.Auction.Config.ESaleType.Armor, item, equipPos, callback, durabilityLvl)
            table.insert(openUINavIdList, UIName2ID.SaleListViewArmor)
            -- Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowBuy, onUILoaded, nil, Module.Auction.Config.ESaleListWindowType.Buy, Module.Auction.Config.ESaleType.Armor, item, equipPos, callback, durabilityLvl)
            -- table.insert(openUINavIdList, UIName2ID.SaleListWindowBuy)
        -- 通用
        else
            if weaponFeature and weaponFeature:IsWeapon() and type ~= EItemType.Weapon and bExternalCall then --模块外部买枪只能购买标准预设
                item = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(itemId)
            end
            Facade.UIManager:AsyncShowUI(UIName2ID.SaleListViewOther, onUILoaded, nil, Module.Auction.Config.ESaleType.Other, item, equipPos, callback, durabilityLvl)
            table.insert(openUINavIdList, UIName2ID.SaleListViewOther)
            -- Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowBuy, onUILoaded, nil, Module.Auction.Config.ESaleListWindowType.Buy, Module.Auction.Config.ESaleType.Other, item, equipPos, callback, durabilityLvl)
            -- table.insert(openUINavIdList, UIName2ID.SaleListWindowBuy)
        end
    else
        Module.Auction:ShowMainPanel()
        table.insert(openUINavIdList, UIName2ID.AuctionMainPanel)
    end
    return openUINavIdList
end

--------------------------------------------------------------------------
--- 跳转至出售弹窗页面
--------------------------------------------------------------------------
function AuctionLogic.OpenSellPopViewProcess(item, bShowSales, bExternalCall)
    if item then
        if Server.AuctionServer:GetPropSaleInfo(item.id) then
            LogAnalysisTool.DoSendAuctionPlayerSearchPropFlowLog(item.id)
            local weaponFeature = item:GetFeature(EFeatureType.Weapon)
            local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
            local durabilityLvl = Server.AuctionServer:CheckEquipmentLevel(item)
            local function onUILoadedNonPresetWeapon(ui)
                Server.BlackSiteServer:BlackSiteGetAuctionTaxDiscountReq()
                ui:Init(item, false, bShowSales)
                local fetchCallback = CreateCallBack(function(ui, saleInfos)
                    loginfo("AuctionLogic.OpenSellPopViewProcess onUILoadedNonPresetWeapon fetchCallback")
                    ui:InitBySaleInfos(saleInfos)
                end, ui)
                local propIds = AuctionLogic.GetWeaponAllPartsIds(item)
                local itemBase = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
                if #propIds == 1 then --标准预设
                    if itemBase and itemBase.id then
                        Server.AuctionServer:FetchSaleList(itemBase.id, nil, fetchCallback)
                    end
                else
                    if not table.isempty(propIds) then
                        if itemBase and itemBase.id then
                            table.insert(propIds, itemBase.id) -- 改装武器还需拉取标准预设数据
                        end
                        Server.AuctionServer:FetchSaleListBatch(propIds, fetchCallback)
                    end
                end
            end
            local function onUILoadedCommon(ui)
                Server.BlackSiteServer:BlackSiteGetAuctionTaxDiscountReq()
                ui:Init()
                local fetchCallback = CreateCallBack(function(ui, saleInfos)
                    loginfo("AuctionLogic.OpenSellPopViewProcess onUILoadedCommon fetchCallback")
                    ui:Init(saleInfos)
                end, ui)
                if weaponFeature and weaponFeature:IsWeapon() and AuctionLogic.JudgeItemTypeIsWeapon(item) then
                    local presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(item.id)
                    Server.AuctionServer:FetchSaleList(presetId, nil, fetchCallback)
                else
                    Server.AuctionServer:FetchSaleList(item.id, nil, fetchCallback)
                end
            end
            --非标准预设枪械
            if weaponFeature and weaponFeature:IsWeapon() and not AuctionLogic.JudgeItemTypeIsWeapon(item) then
                Facade.UIManager:AsyncShowUI(UIName2ID.ShelveWindowWeapon, onUILoadedNonPresetWeapon, nil,
                 Module.Auction.Config.ESaleType.Weapon)
            -- 防具
            elseif equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
                Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowSell, onUILoadedCommon, nil,
                 Module.Auction.Config.ESaleListWindowType.Sell, Module.Auction.Config.ESaleType.Armor, item, nil, nil, durabilityLvl)
            -- 通用
            else
                -- if weaponFeature and weaponFeature:IsWeapon() and AuctionLogic.JudgeItemTypeIsWeapon(item) then
                --     item = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
                -- end
                Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowSell, onUILoadedCommon, nil,
                Module.Auction.Config.ESaleListWindowType.Sell, Module.Auction.Config.ESaleType.Other, item, nil, nil, durabilityLvl)
            end
        else
            logerror("AuctionLogic.OpenSellPopViewProcess PropSaleInfo is nil")
        end
    end
end

--------------------------------------------------------------------------
--- 跳转至重新出售弹窗页面
--------------------------------------------------------------------------
function AuctionLogic.OpenResellPopViewProcess(item, onUILoaded, onUILoadedCommon)
    LogAnalysisTool.DoSendAuctionPlayerSearchPropFlowLog(item.id)
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
    local durabilityLvl = Server.AuctionServer:CheckEquipmentLevel(item)
    --非标准预设枪械
    if weaponFeature and weaponFeature:IsWeapon() and not AuctionLogic.JudgeItemTypeIsWeapon(item) then
        Facade.UIManager:AsyncShowUI(UIName2ID.ShelveWindowWeapon, onUILoaded, nil,
            Module.Auction.Config.ESaleType.Weapon)
    -- 防具
    elseif equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
        Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowSell, onUILoadedCommon, nil,
            Module.Auction.Config.ESaleListWindowType.Resell, Module.Auction.Config.ESaleType.Armor, item, nil, nil, durabilityLvl)
    -- 通用
    else
        -- if weaponFeature and weaponFeature:IsWeapon() and AuctionLogic.JudgeItemTypeIsWeapon(item) then
        --     item = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
        -- end
        Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowSell, onUILoadedCommon, nil,
        Module.Auction.Config.ESaleListWindowType.Resell, Module.Auction.Config.ESaleType.Other, item, nil, nil, durabilityLvl)
    end
end

--------------------------------------------------------------------------
--- 跳转至查看订单页面
--------------------------------------------------------------------------
function AuctionLogic.OpenViewOrderPopProcess(item, onUILoaded)
    if item then
        if Server.AuctionServer:GetPropSaleInfo(item.id) then
            local weaponFeature = item:GetFeature(EFeatureType.Weapon)
            local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
            local durabilityLvl = Server.AuctionServer:CheckEquipmentLevel(item)
            LogAnalysisTool.DoSendAuctionPlayerSearchPropFlowLog(item.id)
            --非标准预设枪械
            if weaponFeature and weaponFeature:IsWeapon() and not AuctionLogic.JudgeItemTypeIsWeapon(item) then
                Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowView, onUILoaded, nil, Module.Auction.Config.ESaleListWindowType.View, Module.Auction.Config.ESaleType.Weapon, item, nil, nil, durabilityLvl)
            -- 防具
            elseif equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
                Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowView, onUILoaded, nil, Module.Auction.Config.ESaleListWindowType.View, Module.Auction.Config.ESaleType.Armor, item, nil, nil, durabilityLvl)
            -- 通用
            else
                if weaponFeature and weaponFeature:IsWeapon() and AuctionLogic.JudgeItemTypeIsWeapon(item) then
                    item = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id) or item
                end
                Facade.UIManager:AsyncShowUI(UIName2ID.SaleListWindowView, onUILoaded, nil, Module.Auction.Config.ESaleListWindowType.View, Module.Auction.Config.ESaleType.Other, item, nil, nil, durabilityLvl)
            end
        else
            logerror("AuctionLogic.OpenViewOrderPopProcess PropSaleInfo is nil")
        end
    end
end

-- 判断物品是否为标准预设枪
function AuctionLogic.JudgeItemTypeIsWeapon(item)
    local bItemBase = true
    local type = ItemHelperTool.GetMainTypeById(item.id)
    if type == EItemType.Weapon then
        return bItemBase
    end
    local itemBase = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
    local BaseWeaponFeature = itemBase:GetFeature(EFeatureType.Weapon)
    local RawPropInfo = item:GetRawPropInfo()
    local BaseRawPropInfo = BaseWeaponFeature and BaseWeaponFeature._rawPropInfo or nil
    if RawPropInfo and BaseRawPropInfo then
        local itemComponents = {}
        local baseComponents = {}
        Server.AuctionServer:GetComponents(RawPropInfo.components, itemComponents)
        Server.AuctionServer:GetComponents(BaseRawPropInfo.components, baseComponents)
        if #itemComponents == #baseComponents then
            for _, component in ipairs(itemComponents) do
                if not table.find(baseComponents, function (v, k)
                    return v.prop_data.id == component.prop_data.id
                end) then
                    bItemBase = false
                    break
                end
            end
        else
            bItemBase = false
        end
    else
        bItemBase = false
    end
    return bItemBase
end

function AuctionLogic.GetJumpInfoByItemId(itemId, durabilityLvl)
    if itemId and Server.AuctionServer:CheckIsInSaleList(itemId) then
        local saleInfo = Server.AuctionServer:GetPropSaleInfo(itemId, durabilityLvl)
        if saleInfo then
            local dataArray = {}
            local data ={
                bUnlock = Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionBuy),
                curNum = saleInfo.cur_num,
                guideCurrency = saleInfo.guide_currency,
                averagePriceStr = (saleInfo.average_price and saleInfo.average_price >0) and MathUtil.GetNumberFormatStr(saleInfo.average_price) or "--",
                averagePrice = saleInfo.average_price or 0,
                guidePrice = saleInfo.guide_price or 0,
                jumpArgs = itemId
            }
            table.insert(dataArray, data)
            return dataArray
        else
            logwarning("AuctionLogic.GetJumpInfoByItemId PropSaleInfo is nil or Auction Buy is Locked!")
        end
    else
        logwarning("AuctionLogic.GetJumpInfoByItemId itemId is nil")
    end
    return nil
end

function AuctionLogic.GetWeaponAllPartsIds(item)
    local ids = {}
    local function GetWeaponAllPartsIdsFromPropInfo(propInfo)
        local itemRow = ItemConfigTool.GetItemConfigById(propInfo.id)
        if itemRow and not itemRow.IsModelOnly then
            table.insert(ids, propInfo.id)
        end
        for _, component in ipairs(propInfo.components) do
            GetWeaponAllPartsIdsFromPropInfo(component.prop_data)
        end
    end
    
    -- 注：Components里面是没有机匣的数据的
    if item.rawPropInfo then
        GetWeaponAllPartsIdsFromPropInfo(item.rawPropInfo)
    elseif item.RawDescObj then
        for key, part in pairs(item.RawDescObj:GetAllParts()) do
            table.insert(ids, part.ItemId)
        end
    end
    return ids
end

function AuctionLogic.CheckItemInSaleList(item)
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature:IsWeapon() then
        local propIds = AuctionLogic.GetWeaponAllPartsIds(item)
        for _, propId in ipairs(propIds) do
            if not Server.AuctionServer:CheckIsInSaleList(propId) then
                return false
            end
        end
    else
        if not Server.AuctionServer:CheckIsInSaleList(item.id) then
            return false
        end
    end
    return true
end

--------------------------------------------------------------------------
--- 打开上架弹窗前处理
--------------------------------------------------------------------------
---@param order pb_CSAuctionOrder
function AuctionLogic.SellGoodBeforeProcess(item, bShowSales, bExternalCall)
    if not item then
        return
    end
    if not Server.AuctionServer:CheckPropCanSell(item, true) then
        return
    end
    -- 不在道具交易时间直接拦截上架
    if not Server.AuctionServer:CheckTransactionSellIsOpenById(item.id) then
        Module.CommonTips:ShowSimpleTip(AuctionConfig.Loc.TransactionSellClose)
        return
    end
    local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) and equipmentFeature:GetDurabilityPercent() < 1 then -- 当前耐久度非满
        -- 初始化完成再弹出窗口
        local function onConfirm(bIsChecked)
            AuctionLogic.OpenRepairPanel(item)
        end
        local function fLoadFinCallback(uiIns)
    
        end
        local text = Module.Auction.Config.Loc.AuctionSaleLowDurabilityIsNotFull
        -- local checkBoxText = Module.Auction.Config.Loc.GoodSellWindow_DoNotRemindMe
        local cancelText = "-1"
        Facade.UIManager:AsyncShowUI(UIName2ID.ConfirmWindows, fLoadFinCallback, nil, text, noticeText, onConfirm, onCancel, cancelText, 
        confirmText, confirmSound, cancelSound, stateInGuide, nil, ignoreGc)
    else
        Module.Auction.Config.Events.evtAuctionSellAuctionClicked:Invoke()
        AuctionLogic.ShowSellGoodPopupProcess(item, false, true)
    end
end

--------------------------------------------------------------------------
--- 显示上架弹窗(SaleListWindowSell)
--------------------------------------------------------------------------
---@param item ItemBase
---@param bShowDetail bool
function AuctionLogic.ShowSellGoodPopupProcess(item, bShowSales, bExternalCall)
    bExternalCall = setdefault(bExternalCall, false)
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    --- 先拉取数据
    if weaponFeature and weaponFeature:IsWeapon() then -- 武器
        local function fBulletRemoveCallback(res)
            if res == nil or (res and res.result == 0) then
                if res and res.result and res.result == 0 then
                    Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.WeaponBulletRemove)
                end
                AuctionLogic.OpenSellPopViewProcess(item, bShowSales, bExternalCall)
            elseif res and res.result and res.result ~= 0 then
                -- 子弹拆卸失败
                if res.result == Err.DepositSpaceNotEnough then
                    Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.BulletRemoveDepositSpaceNotEnough)
                else
                    Facade.ProtoManager:ManuelHandleErrCode(res)
                end
            end
        end
        -- 卸下武器子弹
        AuctionLogic.WeaponBulletRemoveOperation(item, fBulletRemoveCallback)
    else
        -- Server.AuctionServer:FetchSaleList(item.id)
        -- local function onUILoaded(ui)
        --     Server.BlackSiteServer:BlackSiteGetAuctionTaxDiscountReq()
        --     -- ui:InitItem(item, false, bShowDetail)
        --     ui:Init(item, false, bShowSales, bExternalCall)
        -- end
        AuctionLogic.OpenSellPopViewProcess(item, bShowSales, bExternalCall)
    end
end

--------------------------------------------------------------------------
--- 显示再次上架弹窗(SaleListWindowSell)
--------------------------------------------------------------------------
---@param order pb_CSAuctionOrder
function AuctionLogic.ShowResellGoodPopupProcess(order)
    if not (order and order.prop) then
        return
    end
    -- 不在道具交易时间直接拦截重新上架
    if not Server.AuctionServer:CheckTransactionSellIsOpenById(order.prop.id) then
        Module.CommonTips:ShowSimpleTip(AuctionConfig.Loc.TransactionSellClose)
        return
    end
    local propInfo = order.prop
    local item = ItemBase:NewIns(propInfo.id, propInfo.num, propInfo.gid, false)
    if not item then
        return
    end
    item:SetRawPropInfo(propInfo)
    if not Server.AuctionServer:GetPropSaleInfo(item.id) then
        logerror("AuctionLogic.OpenResellPopViewProcess PropSaleInfo is nil")
        return
    end
    local function FetchSaleList(fetchCallback)
        -- 拉数据
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        if weaponFeature and weaponFeature:IsWeapon() then
            local propIds = AuctionLogic.GetWeaponAllPartsIds(item)
            if #propIds == 1 then --标准预设
                if item and item.id then
                    Server.AuctionServer:FetchSaleList(item.id, nil, fetchCallback)
                end
            else
                local itemBase = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
                if not table.isempty(propIds) then
                    if itemBase and itemBase.id then
                        table.insert(propIds, itemBase.id) -- 改装武器还需拉取标准预设数据
                    end
                    Server.AuctionServer:FetchSaleListBatch(propIds, fetchCallback)
                end
            end
        else
            Server.AuctionServer:FetchSaleList(item.id, nil, fetchCallback)
        end
    end
    local function onUILoaded(ui)
        Server.BlackSiteServer:BlackSiteGetAuctionTaxDiscountReq()
        ui:InitFromOrder(order)
        local fetchCallback = CreateCallBack(function(ui, saleInfos)
            loginfo("AuctionLogic.ShowResellGoodPopupProcess onUILoaded fetchCallback")
                ui:InitBySaleInfos(saleInfos)
        end, ui)
        FetchSaleList(fetchCallback)
    end
    local function onUILoadedCommon(ui)
        Server.BlackSiteServer:BlackSiteGetAuctionTaxDiscountReq()
        ui:Init(order)
        local fetchCallback = CreateCallBack(function(ui, saleInfos)
            loginfo("AuctionLogic.ShowResellGoodPopupProcess onUILoadedCommon fetchCallback")
            ui:Init(saleInfos)
        end, ui)
        FetchSaleList(fetchCallback)
    end
    AuctionLogic.OpenResellPopViewProcess(item, onUILoaded, onUILoadedCommon)
end

--------------------------------------------------------------------------
--- 显示查看订单弹窗(SaleListWindowView)
--------------------------------------------------------------------------
---@param order pb_CSAuctionOrder
function AuctionLogic.ShowViewOrderPopupProcess(order)
    if not order then
        return
    end
    local propInfo = order.prop
    local item = ItemBase:NewIns(propInfo.id, propInfo.num, propInfo.gid, false)
    if not item then
        return
    end
    item:SetRawPropInfo(propInfo)
    local function onUILoaded(ui)
        Server.BlackSiteServer:BlackSiteGetAuctionTaxDiscountReq()
        ui:Init(order)
        local fetchCallback = CreateCallBack(function(ui, saleInfos)
            loginfo("AuctionLogic.ShowViewOrderPopupProcess onUILoaded fetchCallback")
            ui:Init(saleInfos)
        end, ui)
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        if weaponFeature and weaponFeature:IsWeapon() then
            local propIds = AuctionLogic.GetWeaponAllPartsIds(item)
            if #propIds == 1 then --标准预设
                if item and item.id then
                    Server.AuctionServer:FetchSaleList(item.id, nil, fetchCallback)
                end
            else
                local itemBase = WeaponAssemblyTool.GetPreviewGunItemBaseFromRecId(item.id)
                if not table.isempty(propIds) then
                    if itemBase and itemBase.id then
                        table.insert(propIds, itemBase.id) -- 改装武器还需拉取标准预设数据
                    end
                    Server.AuctionServer:FetchSaleListBatch(propIds, fetchCallback)
                end
            end
        else
            Server.AuctionServer:FetchSaleList(item.id, nil, fetchCallback)
        end
    end
    AuctionLogic.OpenViewOrderPopProcess(item, onUILoaded)
end

--------------------------------------------------------------------------
--- 显示增加上架槽位弹窗
--------------------------------------------------------------------------
function AuctionLogic.ShowAddSlotPopupProcess()
    local function onUILoaded(ui)
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.AuctionAddSlotPop, onUILoaded, nil)
end

--------------------------------------------------------------------------
--- 在拍卖行中查询指定的物品是否在售
--------------------------------------------------------------------------
---@class SaleInfo
---@field bNumEnough boolean 数量是否足够
---@field prices table<number, number> 购买需要花费的货币，货币id和数量
---@field props table<pb_PropInfo, number> 物品对应的待购买商品，以及购买数量，因为一个物品可能需要购买多个商品，所以是个表

---@param itemList table<number, number> id和数量
---@param fCallback function<table<number, SaleInfo> > 回调，参数是每个物品id和它对应的数据
function AuctionLogic.DoCheckSaleList(itemList, fCallback)
    if table.isempty(itemList) then
        return
    end
    Module.Auction.Field:SetCheckSaleListInfo({itemList, fCallback})
    Server.AuctionServer:FetchSaleListBatch(table.keys(itemList),AuctionLogic._OnSaleListBatchFetched)
end

---@param res pb_CSAuctionGetSaleListRes
function AuctionLogic._OnSaleListBatchFetched(res)
    Module.Auction.Field:SetOriginalSaleListInfo(nil)
    local checkSaleListInfo = Module.Auction.Field:GetCheckSaleListInfo()
    Module.Auction.Field:SetCheckSaleListInfo(nil)
    if checkSaleListInfo then
        local itemList = checkSaleListInfo[1]
        local fCallback = checkSaleListInfo[2]
        local callbackParam = {}
        if res and res.result == 0 then
            for id, num in pairs(itemList) do
                local foundInfo = table.find(res.sale_lists, function(data, _) return data.prop_id == id end)
                ---@param foundInfo pb_CSAuctionSaleListInfo
                if foundInfo then

                    -- 记录要购买的商品，与对应的数量
                    local remainNum = num
                    local props = {}
                    for idx, prop in pairs(foundInfo.sale_lists) do
                        local optNum = math.min(remainNum, prop.selling_num)
                        local propInfo = {
                            [1] = prop,
                            [2] = optNum, 
                        }
                        table.insert(props, propInfo)
                        remainNum = remainNum - optNum
                        if remainNum == 0 then
                            break
                        end
                    end

                    -- 判断拍卖行是否有足够的数量
                    local bNumEnough = true
                    if remainNum > 0 then
                        bNumEnough = false
                    end

                    -- 统计需要购买的商品价格
                    local prices = {}
                    for idx, propInfo in pairs(props) do
                        local currencyId = propInfo[1].price_currency
                        prices[currencyId] = prices[currencyId] or 0
                        prices[currencyId] = prices[currencyId] + propInfo[1].price * propInfo[2]
                    end

                    local saleInfo = {
                        bNumEnough = bNumEnough,
                        prices = prices,
                        props = props
                    }
                    table.insert(callbackParam, {id, saleInfo})
                else
                    local saleInfo = {
                        bNumEnough = false,
                    }
                    table.insert(callbackParam, {id, saleInfo})
                end
            end
            Module.Auction.Field:SetOriginalSaleListInfo(callbackParam) --方便用于某些商品购买失败后校验后续补齐价格
            fCallback(callbackParam)
        end
    end
end

--批量购买PromiseALL回调
---@param resList 回包列表
function AuctionLogic._BatchBuyGoodPromiseAllProcess(resList)
    local errorList = {}
    for key, resinfo in pairs(resList) do
        if resinfo.res.result ~= 0 then --分类处理错误信息
            local errorType = resinfo.res.result
            if errorList[errorType] then
                table.insert(errorList[errorType],resinfo)
            else
                errorList[errorType] = {resinfo}
            end
        end
    end

    if not table.isempty(errorList) then --批量购买部分问题
        -- 仓库空间已经不足，购买不成功的都不应该再购买了
        if errorList[Err.DepositSpaceNotEnough] then
            -- azhengzheng:id映射数量
            local failedTable = {}
            for _, value in pairs(errorList[Err.DepositSpaceNotEnough]) do
                failedTable[value.data[1].prop.id] = failedTable[value.data[1].prop.id] and failedTable[value.data[1].prop.id] + 1 or 1
            end

            Module.Auction.Field:SetOriginalSaleListInfo(nil)
            Module.Auction.Field:SetBatchBuyList(nil)

            local errorTip = ""
            for key, value in pairs(failedTable) do
                if errorTip ~= "" then
                    errorTip = errorTip .. ","
                end
                local itemName = ItemConfigTool.GetItemConfigById(key).Name
                errorTip = errorTip .. itemName
                if value ~= 1 then
                    errorTip = errorTip .. "*" .. value
                end
            end

            Module.CommonTips:ShowSimpleTip(string.format("【%s】%s", errorTip, ServerTipCode.BuyAuctionFailedNotEnough))
            log("_BatchBuyGoodPromiseAllProcess:Err.DepositSpaceNotEnough   ", string.format("【%s】%s",errStr,ServerTipCode.BuyAuctionFailedNotEnough))
        else --其他问题
            for type, errorTypeList in pairs(errorList) do
                if type == Err.AuctionPropNotEnough then --价格波动需要调整补齐
                    log("_BatchBuyGoodPromiseAllProcess:Err.AuctionPropNotEnough")
                    local errorProps = {}
                    for index, resinfo in ipairs(errorTypeList) do
                        if errorProps[resinfo.data[1].prop.id] then
                            table.insert(errorProps[resinfo.data[1].prop.id],resinfo)
                        else
                            errorProps[resinfo.data[1].prop.id] = {resinfo}
                        end
                    end
                    local function fFetchSaleListBatchCallback()
                        AuctionLogic._DoTotalPriceProcess(errorProps)
                    end
                    --重新拉取价格不对的商品
                    Server.AuctionServer:FetchSaleListBatch(table.keys(errorProps),fFetchSaleListBatchCallback)
                elseif type ~= 0 or #res.order == 0 then --部分批量商品购买失败
                    local errStrTable = {}
                    local batchBuyList = Module.Auction.Field:GetBatchBuyList()
                    for index, resinfo in ipairs(errorTypeList) do
                        local porpName = ItemConfigTool.GetItemConfigById(resinfo.data[1].prop.id).Name
                        table.insert(errStrTable,porpName)
                        batchBuyList[resinfo.data[1].prop.id] = nil
                    end
                    local failedTips = ServerTipCode.ChangesInItemTransactionInformation
                    if type == Err.DepositCurrencyNotEnough then
                        failedTips = ServerTipCode.CurrencyNotEnoughBuyAuctionFailed
                        Module.CommonTips:ShowSimpleTip(string.format("【%s】%s",table.concat(errStrTable,","),failedTips))
                    else
                        Module.CommonTips:ShowSimpleTip(failedTips)
                    end
                    
                    log("_BatchBuyGoodPromiseAllProcess:Err.type ~= 0 or #res.order == 0   ", string.format("【%s】%s,%s",table.concat(errStrTable,","),ServerTipCode.BuyAuctionFailed,type))
                    

                    -- Module.CommonTips:ShowSimpleTip(string.format("【%s】%s",table.concat(errStrTable,","),ServerTipCode.BuyAuctionFailed))
                    -- log("_BatchBuyGoodPromiseAllProcess:Err.type ~= 0 or #res.order == 0   ", string.format("【%s】%s",table.concat(errStrTable,","),ServerTipCode.BuyAuctionFailed))
                end
            end
        end
    elseif table.isempty(errorList) and not table.isempty(resList) then --批量购买【全部】成功
        Module.Auction.Field:SetOriginalSaleListInfo(nil)
        Module.Auction.Field:SetBatchBuyList(nil)
        Module.CommonTips:ShowSimpleTip(Module.Auction.Config.Loc.MissingItemsHaveBeenFilled)
    end
    Module.Auction.Config.Events.evtBatchBuyGoodFinished:Invoke()
end

--重新计算批量购买商品的价格
function AuctionLogic._DoTotalPriceProcess(errorProps)
    --获取之前请求批量购买商品的价格数据
    local buyProps = {}
    --售罄道具名单
    local soldOutPropNameList = {}
    local underSoldPropNameList = {}
    local originalSaleListInfo = Module.Auction.Field:GetOriginalSaleListInfo()
    for id, errList in pairs(errorProps) do --对应购买失败id的出售品
        for _, propInfo in pairs(errList) do --购买失败的道具，根据不同gid的
            local oldSale = table.find(originalSaleListInfo, function(data, _) return data[1] == propInfo.data[1].prop.id end)
            local oldProp = table.find(oldSale[2].props, function(data, _) return data[1].prop.gid == propInfo.data[1].prop.gid end)
            local newSale = Server.AuctionServer:GetPropSaleInfo(oldSale[1])
            local newProp = table.find(newSale.sale_lists, function(data, _) return data.prop.gid == propInfo.data[1].prop.gid end)
            if newProp and newProp.selling_num >= propInfo.data[2] then --=如果拉新后出售数量还是比所需的大，直接替换prop即可(大概率进不来)
                --有风险
                oldProp = newProp
                local propInfo = {
                    [1] = newProp,
                    [2] = propInfo.data[2],
                }
                table.insert(buyProps, propInfo)
            elseif #newSale.sale_lists > 0 then
                local remainNum = propInfo.data[2]
                table.removebyvalue(oldSale[2].props,oldProp)
                -- oldProp = nil --直接重新计算，不要之前那个
                for idx, prop in pairs(newSale.sale_lists) do
                    local optNum = math.min(remainNum, prop.selling_num)
                    local propInfo = {
                        [1] = prop,
                        [2] = optNum, 
                    }
                    table.insert(oldSale[2].props, propInfo)
                    table.insert(buyProps, propInfo)
                    remainNum = remainNum - optNum
                    if remainNum == 0 then
                        break
                    end
                end
                if remainNum ~= 0 then --出售的还是不够
                    local porpName = ItemConfigTool.GetItemConfigById(oldSale[1]).Name
                    table.insert(underSoldPropNameList, porpName)
                    log("_DoTotalPriceProcess:UnderSoldFailed ", porpName,remainNum)
                end
            else
                table.removebyvalue(oldSale[2].props,oldProp)
                local porpName = ItemConfigTool.GetItemConfigById(oldSale[1]).Name
                table.insert(soldOutPropNameList, porpName)
                log("_DoTotalPriceProcess:SoldOut ", porpName)
            end

            -- 统计需要购买的商品价格
            local prices = {}
            for idx, propInfo in pairs(oldSale[2].props) do
                local currencyId = propInfo[1].price_currency
                prices[currencyId] = prices[currencyId] or 0
                prices[currencyId] = prices[currencyId] + propInfo[1].price * propInfo[2]
            end
            oldSale[2].prices = prices
        end
    end

    local function fOnConfirmCallbackIns()
        Server.AuctionServer:DoBatchBuyGoodReq(buyProps, table.keys(errorProps))
    end

    if #buyProps > 0 then
        --重新计算总价格
        local totalPrice = 0
        local batchBuyList = Module.Auction.Field:GetBatchBuyList()
        for _, eachItem in pairs(originalSaleListInfo) do
            local id = eachItem[1]
            local bNumEnough = eachItem[2].bNumEnough
            for priceType, priceValue in pairs(eachItem[2].prices) do
                if batchBuyList[id] ~= nil then
                    totalPrice = totalPrice + priceValue
                end
            end
        end

        local title = string.format(Module.Auction.Config.Loc.PriceFluctuationAdjustment,MathUtil.GetNumberFormatStr(totalPrice))
        Module.CommonTips:ShowConfirmWindow(title, fOnConfirmCallbackIns)
    end

    if #soldOutPropNameList > 0 then
        local soldOutPropNamestr = table.concat(soldOutPropNameList,",")
        Module.CommonTips:ShowSimpleTip(string.format(ServerTipCode.BatchBuyAuctionFailed,soldOutPropNamestr))
    end
    if #underSoldPropNameList > 0 then
        local underSoldPropNamestr = table.concat(underSoldPropNameList,",")
        Module.CommonTips:ShowSimpleTip(string.format(Module.Auction.Config.Loc.UnderSoldFailed,underSoldPropNamestr))
    end
end

--------------------------------------------------------------------------
--- 获取某物品在交易所的最低价
--------------------------------------------------------------------------
function AuctionLogic.GetMinPrice(itemId, fResultCallback)
    local function fOnFetchFinished(saleInfos)
        if saleInfos then
            local saleInfo = Server.AuctionServer:GetPropSaleInfo(itemId, nil, saleInfos)
            if saleInfo and saleInfo.sale_lists and not table.isempty(saleInfo.sale_lists) then
                if fResultCallback then
                    fResultCallback(saleInfo.sale_lists[1].price)
                    return
                end
            end
        end
        if fResultCallback then
            fResultCallback(nil)
            return
        end
    end
    Server.AuctionServer:FetchSaleList(itemId, nil, fOnFetchFinished)
end

--------------------------------------------------------------------------
--- 获取某些物品在交易所的最低价
--------------------------------------------------------------------------
function AuctionLogic.GetMinPriceBatch(itemIds, fResultCallback)
    if not itemIds or type(itemIds) ~= "table" or table.isempty(itemIds) then
        if fResultCallback then
            fResultCallback({})
            return
        end
    end
    local function fOnFetchFinished(saleInfos)
        local mapMinPrice = {}
        for _, itemId in ipairs(itemIds) do
            if saleInfos then
                local saleInfo = Server.AuctionServer:GetPropSaleInfo(itemId, nil, saleInfos)
                if saleInfo and saleInfo.sale_lists and not table.isempty(saleInfo.sale_lists) then
                    mapMinPrice[itemId] = saleInfo.sale_lists[1].price
                else
                    mapMinPrice[itemId] = nil
                end
            else
                mapMinPrice[itemId] = nil
            end
        end
        if fResultCallback then
            fResultCallback(mapMinPrice)
            return
        end
    end
    Server.AuctionServer:FetchSaleListBatch(itemIds, fOnFetchFinished)
end

function AuctionLogic._GenCollectionList(id)
    local inventoryServer = Server.InventoryServer
    local collectionList = {}
    if id then
        collectionList = Module.Auction.Field:GetCollectionProps()
        local findMarkProp = table.find(collectionList, function (v, k)
            return v.prop.id == id
        end)
        local itemMarkProp = inventoryServer:GetItemMarkProp(id)
        if itemMarkProp then
            -- 移除旧的
            table.removebyvalue(collectionList ,findMarkProp)
            -- 插入新的
            table.insert(collectionList, itemMarkProp)
        else
            table.removebyvalue(collectionList, findMarkProp)
        end
    else
        local allItemMarkProps = Server.InventoryServer:GetAllItemMarkProps()
        if allItemMarkProps then
            allItemMarkProps = table.tolist(allItemMarkProps)
            Module.Auction.Field:SetCollectionProps(allItemMarkProps)
        end
        -- local saleList = Server.AuctionServer:GetPropSaleList()
        -- for propId, saleInfo in pairs(saleList) do
        --     local bMarked = inventoryServer:CheckItemMarked(propId)
        --     if bMarked then
        --         table.insert(collectionList, propId)
        --     end
        -- end
        -- table.sort(collectionList, function (a, b)
        --     return a < b
        -- end)
        
    end
    Module.Auction.Config.Events.evtCollectListChanged:Invoke()
end

function AuctionLogic.CheckItemIsCollected(itemId)
    local bCollected = false
    for _, markProp in ipairs(Module.Auction.Field:GetCollectionProps()) do
        if itemId and itemId == markProp.prop.id then
            bCollected = true
            break
        end
    end
    return bCollected
end

--------------------------------------------------------------------------
--- 获取SlotType符合的所有拍卖行道具类型
--------------------------------------------------------------------------
---@return
function AuctionLogic.GetSaleListByPrefixs(prefixs)
    local matchmap = {}
    local saleList = Server.AuctionServer:GetPropSaleList()
    for itemId, saleInfo in pairs(saleList) do
        local idStr = tostring(itemId)
        for _, prefix in ipairs(prefixs) do
            local prefixStr = tostring(prefix)
            if string.sub(idStr, 1, string.len(prefixStr)) == prefixStr then
                matchmap[itemId] = saleInfo
            end
        end
    end
    return matchmap
end

function AuctionLogic.OpenRepairPanel(item)
    local function loadFinCall(uiIns)
        local function repairSuccessCallback()
            Module.Auction.Config.Events.evtAuctionSellAuctionClicked:Invoke()
            Module.Auction:JumpToSellPage(item, false, true)
        end
        uiIns:BindRepairCallBack(repairSuccessCallback)
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseRepairWindow, loadFinCall, nil, item)
end

function AuctionLogic.OpenSellPage(item)
    if not item then
        return
    end
    if Server.AuctionServer:CheckPropCanSell(item, true) then
        Module.Auction:JumpToSellPage(item, false, false)
    end
end

function AuctionLogic.GetPriceAfterTax(item, singlePrice, sellNum, saleInfo, discount)
    if item and singlePrice and sellNum and saleInfo and saleInfo.fee_args then
        local durationIndex = 1 -- 默认上架12小时
        local fee = Server.AuctionServer:GetShelveFee(item, singlePrice, sellNum, durationIndex, discount, saleInfo.fee_args)
        if fee["tax"] and fee["management"] then
            local taxFee = fee["tax"]
            local managementFee = fee["management"]
            local priceAfterTax = singlePrice * sellNum - taxFee - managementFee
            return priceAfterTax
        end
    end
end

function AuctionLogic.SetHoverdInstruction(caller, itemWidget, item, info, forceChangeState, fHoverCallbackIns)
    if IsHD() then
        local summaryList = {}
        local weaponFeature = item:GetFeature(EFeatureType.Weapon)
        if weaponFeature and weaponFeature:IsWeapon() then
            summaryList = {
                {
                    actionName = "SearchRelated",
                    func = function() AuctionLogic.RelatedSearch(item.id) end,
                    caller = caller,
                    optionalStr = nil
                }
            }
        else
            summaryList = {
                {
                    actionName = AuctionLogic.CheckItemIsCollected(item.id) and "AuctionCancelCollect" or "AuctionCollect",
                    func = function() AuctionLogic.CollectWidgetItem(caller, itemWidget, item, info, forceChangeState, fHoverCallbackIns) end,
                    caller = caller,
                    optionalStr = nil
                },
                {
                    actionName = "SearchRelated",
                    func = function() AuctionLogic.RelatedSearch(item.id) end,
                    caller = caller,
                    optionalStr = nil
                }
            }
        end
        local contents = {
            {
                id = UIName2ID.Assembled_CommonMessageTips_V1,
                data = {
                    textContent = tostring(item.name),
                }
            },
            {
                id = UIName2ID.Assembled_CommonMessageTips_V2,
                data = {
                    textContent = AuctionLogic.GetSimpleDecription(item.id)
                }
            },
            {
                id = UIName2ID.Assembled_CommonKeyTips_V2,
                data = {
                    summaryList = summaryList
                }
            },
        }
        itemWidget:SetGivenInstructionContents(contents)
        itemWidget:BindHoverCallback(fHoverCallbackIns)
    end
end

function AuctionLogic.CollectWidgetItem(caller, itemWidget, item, info, forceChangeState, fHoverCallbackIns)
    if itemWidget and item then
        if Module.Auction.Field:GetbDelayChangeCollection() then
            return
        end
        local fOnCallback = function(res)
            if res and res.result == 0 then
                forceChangeState = setdefault(forceChangeState, true)
                if forceChangeState then
                    itemWidget:PlayAnimationForward(itemWidget.Hightlight, 1.0, true)
                    AuctionLogic.SetHoverdInstruction(caller, itemWidget, item, info, nil, fHoverCallbackIns)
                end
                itemWidget:ShowGivenInstruction()
            end
        end
        Server.InventoryServer:ToggleItemMarkStatus(item.id, fOnCallback)
        Module.Auction.Field:SetbDelayChangeCollection(true)
        Timer.DelayCall(1, function()
            Module.Auction.Field:SetbDelayChangeCollection(false)
        end, caller)
    end
end

function AuctionLogic.GetSimpleDecription(itemId)
    local auctionRelatedSearchTable = AuctionConfig.AuctionRelatedSearchTable
    if auctionRelatedSearchTable then
        for _, relatedSearchMap in pairs(auctionRelatedSearchTable) do
            if relatedSearchMap and relatedSearchMap.ItemID and relatedSearchMap.ItemID == itemId then
                if relatedSearchMap and relatedSearchMap.SimpleDescription and type(relatedSearchMap.SimpleDescription) == "userdata" then
                    return relatedSearchMap.SimpleDescription
                end
            end
        end
    end
    return ""
end

function AuctionLogic.RelatedSearch(itemId)
    AuctionLogic.SetRelatedSearchData(itemId)
    if Module.Auction.Field:GetRelatedSearchIds() and not table.isempty(Module.Auction.Field:GetRelatedSearchIds()) then
        Server.AuctionServer:FetchTypeList(Module.Auction.Field:GetRelatedSearchIds(), true)
    end
end

function AuctionLogic.SetRelatedSearchData(itemId)
    local relatedIds = AuctionLogic.SearchRelatedItemIds(itemId)
    local allMatchItemIds = relatedIds or {}
    if table.isempty(allMatchItemIds) then
        Module.CommonTips:ShowSimpleTip(AuctionConfig.Loc.NoRelatedItem)
        Module.Auction.Field:SetRelatedSearchIds()
    else
        Module.Auction.Field:SetRelatedSearchIds(allMatchItemIds)
    end
    return allMatchItemIds
end

function AuctionLogic.SearchRelatedItemIds(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    if itemMainType == EItemType.CollectableItem then -- 包括收集品和钥匙
        if itemSubType == ECollectableType.Key then
            local tabIds = AuctionLogic.FindAuctionTabIds(itemId)
            return tabIds
        else
            local ids = {itemId}
            return ids
        end
    elseif itemMainType == EItemType.Equipment then
        local ids = {}
        local tabIds = AuctionLogic.FindAuctionTabIds(itemId)
        if tabIds then
            for _, tabId in ipairs(tabIds) do
                if ItemConfigTool.GetItemQuality(tabId) == ItemConfigTool.GetItemQuality(itemId) then
                    table.insert(ids, tabId)
                end
            end
        end
        return ids
        -- if itemSubType == EEquipmentType.Helmet or itemSubType == EEquipmentType.BreastPlate then
        -- elseif itemSubType == EEquipmentType.ChestHanging or itemSubType == EEquipmentType.Bag then
        -- else
        -- end
    elseif itemMainType == EItemType.Medicine then
        local tabIds = AuctionLogic.FindAuctionTabIds(itemId)
        return tabIds
        -- if itemSubType == EMedicineType.UseMedicine or itemSubType == EMedicineType.HPMedicine or itemSubType == EMedicineType.SurgeryMedicine
        --  or itemSubType == EMedicineType.HemostasisMedicine or itemSubType == EMedicineType.TimeMedicine then
        -- elseif itemSubType == EMedicineType.RepairMedicine then
        -- elseif itemSubType == EMedicineType.InjectionMedicine then
        -- else
        -- end
    elseif itemMainType == EItemType.Weapon or itemMainType == EItemType.Receiver then
        local ids = {}
        local presetId = itemId
        local resId = itemId
        if itemMainType == EItemType.Weapon then
            table.insert(ids, presetId)
            resId = WeaponAssemblyTool.GetFunctionIdByPresetID(itemId) -- 10转18
        end
        if itemMainType == EItemType.Receiver then
            presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(itemId)
            table.insert(ids, presetId)
        end
        local bulletItems = WeaponHelperTool.GetAmmoListForWeapon(resId)
        local tabIds = AuctionLogic.FindAuctionTabIds(bulletItems[1])
        if tabIds then
            for _, tabId in ipairs(tabIds) do
                table.insert(ids, tabId)
            end
        end
        local AdaptersIds = UAssembleWeaponDataLibrary.GetReceiverIDValidInstallList(resId, {})
        if AdaptersIds then
            for _, adapterId in ipairs(AdaptersIds) do
                if Server.AuctionServer:CheckIsInSaleList(adapterId) then
                    table.insert(ids, adapterId)
                end
            end
        end
        return ids
    elseif itemMainType == EItemType.Adapter then
        local ids = {}
        local weaponIds = UAssembleWeaponDataLibrary.GetPartInstallReceiverList(itemId)
        if weaponIds then
            for _, weaponId in ipairs(weaponIds) do
                local presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(weaponId)
                if Server.AuctionServer:CheckIsInSaleList(presetId) then
                    table.insert(ids, presetId)
                end
            end
        end
        local adapterIds = UAssembleWeaponDataLibrary.GetReceiverIDValidInstallList(itemId, {})
        if adapterIds then
            for _, adapterId in ipairs(adapterIds) do
                if Server.AuctionServer:CheckIsInSaleList(adapterId) then
                    table.insert(ids, adapterId)
                end
            end
        end
        return ids
    elseif itemMainType == EItemType.Bullet then
        local ids = {}
        local tabIds = AuctionLogic.FindAuctionTabIds(itemId)
        ids = tabIds
        local weaponIds = WeaponHelperTool.GetBulletApplyWeapon(itemId)
        if weaponIds then
            for _, weaponId in ipairs(weaponIds) do
                local presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(weaponId)
                if Server.AuctionServer:CheckIsInSaleList(presetId) then
                    table.insert(ids, presetId)
                end
            end
        end
        return ids
    else
        return {}
    end
end

function AuctionLogic.FindAuctionTabIds(itemId)
    if not itemId then
        return {}
    end
    local function splitToNumbers(str)
        if not str or type(str) ~= "string" then
            return {}
        end
        local t = {}
        -- 使用模式匹配提取所有数字
        for num in string.gmatch(str, "%d+") do
            table.insert(t, tonumber(num))
        end
        return t
    end
    local propSlotConfig = Facade.TableManager:GetTable("PropSlotSubConfig")
    for tabIndex, slotInfo in pairs(propSlotConfig) do
        if string.find(slotInfo.PagePropTypes, tostring(itemId)) then
            return splitToNumbers(slotInfo.PagePropTypes)
        end
    end
    return {}
end

function AuctionLogic.GetSlotInstructionContent()
    local contents = {}
    table.insert(contents, {
        id = UIName2ID.Assembled_CommonMessageTips_V1,
        data = {
            textContent = AuctionConfig.Loc.SlotNum
        }
    })
    local initRackCnt = Server.AuctionServer:GetInitRackCnt()
    local InitialSlotText = string.format(AuctionConfig.Loc.InitialSlot, initRackCnt)
    if InitialSlotText then
        table.insert(contents, {
            id = UIName2ID.Assembled_CommonMessageTips_V1,
            data = {
                textContent = InitialSlotText
            }
        })
    end
    -- local SlotNumByEquityCard = Server.AuctionServer:GetRightCardRackCnt()
    -- if SlotNumByEquityCard then
    --     local equityCardRemainTimeText = ""
    --     local equityCardExpireTime = Server.AuctionServer:GetRightCardExpireTime()
    --     if equityCardExpireTime and equityCardExpireTime > 0 then
    --         local remainTime = equityCardExpireTime - Facade.ClockManager:GetLocalTimestamp()
    --         if remainTime > 0 then
    --             local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remainTime)
    --             if day == 0 and hour == 0 and min == 0 and sec > 0 then
    --                 equityCardRemainTimeText = string.format(AuctionConfig.Loc.Brackets, string.format(AuctionConfig.Loc.EquityCardRemainSec, sec))
    --             elseif day == 0 and hour == 0 and min > 0 then
    --                 equityCardRemainTimeText = string.format(AuctionConfig.Loc.Brackets, string.format(AuctionConfig.Loc.EquityCardRemainMin, min))
    --             elseif day == 0 and hour > 0 then
    --                 equityCardRemainTimeText = string.format(AuctionConfig.Loc.Brackets, string.format(AuctionConfig.Loc.EquityCardRemainHour, hour))
    --             elseif day > 0 then
    --                 equityCardRemainTimeText = string.format(AuctionConfig.Loc.Brackets, string.format(AuctionConfig.Loc.EquityCardRemainDay, day))
    --             end
    --         else
    --             equityCardRemainTimeText = ""
    --         end
    --     else
    --         equityCardRemainTimeText = ""
    --     end
    --     local SlotByEquityCardText = string.format(AuctionConfig.Loc.SlotByEquityCard, SlotNumByEquityCard, equityCardRemainTimeText)
    --     if SlotByEquityCardText then
    --         table.insert(contents, {
    --             id = UIName2ID.Assembled_CommonMessageTips_V1,
    --             data = {
    --                 textContent = SlotByEquityCardText
    --             }
    --         })
    --     end
    -- end
    local SlotNumBySafeHouse = Server.AuctionServer:GetSafehouseRackCnt()
    if SlotNumBySafeHouse then
        local SlotBySafeHouseText = string.format(AuctionConfig.Loc.SlotBySafeHouse, SlotNumBySafeHouse)
        if SlotBySafeHouseText then
            table.insert(contents, {
                id = UIName2ID.Assembled_CommonMessageTips_V1,
                data = {
                    textContent = SlotBySafeHouseText
                }
            })
        end
    end
    return contents
end

function AuctionLogic.MaxAndMinValueYProcess(maxValue, minValue, lineAdaptationParam)
    local adjustParam
    if math.abs(maxValue - minValue) > 1e-10 and maxValue / minValue < lineAdaptationParam then
        local tempParam = (lineAdaptationParam * minValue - maxValue) / (maxValue + minValue)
        adjustParam = tempParam * (maxValue - minValue) / (lineAdaptationParam - 1 - 2 * tempParam)
    end
    if adjustParam then
        maxValue = maxValue + adjustParam
        minValue = minValue - adjustParam
    end
    maxValue, minValue = AuctionLogic.SameCellsValueYProcess(maxValue, minValue)
    return maxValue, minValue
end

function AuctionLogic.SameCellsValueYProcess(maxValue, minValue)
    local bSame = maxValue == minValue
    if bSame then
        maxValue = maxValue + 1
        minValue = minValue - 1
    end
    return maxValue, minValue
end

function AuctionLogic.GetRecentBougthRecord()
    local boughtRecords = Server.AuctionServer:GetBoughtRecords()
    local recentBoughtIds = {}
    for _, v in ipairs(boughtRecords) do
        if not table.contains(recentBoughtIds, v.prop.id) and #recentBoughtIds < AuctionConfig.RecordReservedNum then
            table.insert(recentBoughtIds, v.prop.id)
        end
    end
    local recentBoughtInfo = {}
    for _, id in ipairs(recentBoughtIds) do
        if Server.AuctionServer:CheckIsInSaleList(id) then
            table.insert(recentBoughtInfo, Server.AuctionServer:GetPropSaleInfo(id))
        end
    end
    recentBoughtInfo = AuctionLogic.RemoveTransactionNoOpenSaleInfo(recentBoughtInfo)
    return recentBoughtInfo
end

function AuctionLogic.RemoveTransactionNoOpenSaleInfo(saleInfoList)
    local finalSaleInfoList = {}
    if saleInfoList and type(saleInfoList) == "table" then
        for _, saleInfo in ipairs(saleInfoList) do
            if Server.AuctionServer:CheckTransactionSellIsOpenById(saleInfo.prop_id, saleInfo.durability_lvl) then
                table.insert(finalSaleInfoList, saleInfo)
            end
        end
    end
    return finalSaleInfoList
end

function AuctionLogic.CheckItemPurchasePriceInShop(itemId)
    if not itemId then
        return
    end
    local item = ItemBase:NewIns(itemId)
    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
    local type = ItemHelperTool.GetMainTypeById(item.id)
    if weaponFeature and weaponFeature:IsWeapon() and type == EItemType.Weapon then
        local functionId = WeaponAssemblyTool.GetFunctionIdByPresetID(itemId, EArmedForceMode.SOL)
        if functionId then
            return Server.ShopServer:CheckItemPurchasePriceById(functionId)
        end
    end
    return Server.ShopServer:CheckItemPurchasePriceById(itemId)
end

return AuctionLogic