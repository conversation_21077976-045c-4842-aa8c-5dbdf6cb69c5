----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local CollectionRoomField = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomField"
local EDescendantScrollDestination  = import "EDescendantScrollDestination"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"


---@class CollectionRoomDIYCabinetPanel : LuaUIBaseView
local CollectionRoomDIYCabinetPanel = ui("CollectionRoomDIYCabinetPanel")

function CollectionRoomDIYCabinetPanel:Ctor()
    InventoryNavManager.Reset()
    self._wtRootCanvas = self:Wnd("DFCanvasPanel_19", UIWidgetBase)
    self._wtValueTxt = self:Wnd("wtValueTxt", UITextBlock)
    self._wtValue = self:Wnd("wtValue", UITextBlock)
    self._wtTipsBtn = self:Wnd("wtTipsBtn", UIWidgetBase)
    self._wtIVWaterfallScroll = UIUtil.WndWaterfallScrollBox(self, "wtIVWaterfallScroll", self._OnGetListCount, self._OnProcessCabinetListWidget)
    self._wtGotoCollectionRoom_1 = self:Wnd("wtGotoCollectionRoom_1", UIWidgetBase)
    self._wtGotoCollectionRoom_2 = self:Wnd("wtGotoCollectionRoom_2", UIWidgetBase)
    self._wtWarehousePanel = self:Wnd("wtWarehousePanel", UIWidgetBase)
    self._wtDIYCabinetValueTxt = self:Wnd("wtDIYCabinetValueTxt", UITextBlock)


    self._wtSwitchToLeftCameraBtn = self:Wnd("DFButton_LeftBtn", DFButtonOnly)
    self._wtSwitchToLeftCameraBtn:Event("OnClicked", self._OnLeftBtnClicked, self)
    self._wtSwitchToRightCameraBtn = self:Wnd("DFButton_RightBtn", DFButtonOnly)
    self._wtSwitchToRightCameraBtn:Event("OnClicked", self._OnRightBtnClicked, self)
    self._wtCameraBtnList = self:MultiWnd("WBP_DFCommonButtonCarousel", DFButtonOnly)
    for index, btn in ipairs(self._wtCameraBtnList) do
        btn:Event("OnClicked", CreateCallBack(self._OnCameraBtnClicked, self, index))
    end
    self._wtSwitchCabinetBtn = self:Wnd("WBP_CommonButtonV3S1_1", DFButtonOnly)
    self._wtSwitchCabinetBtn:Event("OnClicked", self._OnSwitchCabinetBtnClicked, self)
    if IsHD() then
        InventoryNavManager.SetSwitchCabinetBtn(self._wtSwitchCabinetBtn)
    end
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtTipsAnchor", self._OnShowTips, self._OnHideTips)
    
    self._DIYCabinetId = 1
    self._cameraId = 0
    self._eventList = {}
    self._bDepositVisible = false
end

---@param DIYCabinetId number 展示台编号
function CollectionRoomDIYCabinetPanel:OnInitExtraData(DIYCabinetId)
    self._DIYCabinetId = DIYCabinetId
    self:_SetViewTargetToCameraId(2)
end

function CollectionRoomDIYCabinetPanel:OnShowBegin()
    self:StopWidgetAnim(self.WBP_CollectionHome_DIYCabinet_PC_close)
    self:PlayAnimation(self.WBP_CollectionHome_DIYCabinet_PC_open, self.WBP_CollectionHome_DIYCabinet_PC_open:GetEndTime(), 1, EUMGSequencePlayMode.Forward, 1.0, false)
    self:_RefreshBottomBtn(true)
    self._wtWarehousePanel:SetCollectionBtnPanel(true)
    Module.Inventory:RegisterCommonClickBehavior()
    -- self:_InitAutoScrollWidget()
    self:_EnableGamepadFeature()
    self:_SetViewTargetToCameraId(self._cameraId)
end

function CollectionRoomDIYCabinetPanel:OnShow()
    self:AddLuaEvent(CollectionRoomConfig.Events.evtFocusDIYCabinetGrid, self._OnFocusDIYCabinetGrid, self)
    self:AddLuaEvent(Server.CollectionRoomServer.Events.evtCollectionRoomCabinetGridChange, self._OnCabinetGridChange, self)
    self:AddLuaEvent(Module.CommonWidget.Config.Events.evtItemDoubleClicked, self._OnItemDoubleClicked, self)
    self:AddLuaEvent(CollectionRoomConfig.Events.evtDragEnterDIYGrid, self._OnDragEnterDIYGrid, self)
    local gameInst = GetGameInstance()
    self._buttonownHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtBottomBarSetting, self._RefreshBottomBtnGamepad, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEnterExtArrangeMode, self._OnEnterExtArrangeMode, self)
    self:AddLuaEvent(InventoryConfig.Events.evtShouldUseItemOnDraggingSummary, self._OnGlobalSwapItem, self)

	self:RefreshCabinetPanel(self._DIYCabinetId)
    self:InitPriceTxt()
    InventoryNavManager.FocusToDefaultFocusPoint()
end

function CollectionRoomDIYCabinetPanel:OnHideBegin()
    self:_OnHideTips()
    self:_DisableGamepadFeature()
end

function CollectionRoomDIYCabinetPanel:OnHide()
    self:RemoveAllLuaEvent()
    if self._buttonownHandle then
		local gamelnst = GetGameInstance()
		UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._buttonownHandle)
		self._buttonownHandle = nil
	end
    Module.Inventory:UnregisterCommonClickBehavior()
    self:_HideSwitchCabinetSide()
    LuaTickController:Get():RemoveTick(self)
    self._eventList = {}
    Module.CollectionRoom.Field.lastDragItemGid = nil
    InventoryNavManager.Reset()
    Module.CollectionRoom:SetCurCabinetInfo(nil)
end

function CollectionRoomDIYCabinetPanel:Update()
    local targetGridId
    local lastDragItemGid = Module.CollectionRoom.Field.lastDragItemGid
    for _, event in ipairs(self._eventList) do
        local changeType, cabinetType, cabinetId, gridId = table.unpack(event)
        if cabinetId == self._DIYCabinetId then
            if lastDragItemGid then
                local cabinetItemData = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, cabinetId, gridId)
                if cabinetItemData and lastDragItemGid == cabinetItemData.gid then
                    targetGridId = gridId
                    break
                end
            end
            targetGridId = gridId
        end
    end
    Module.CollectionRoom.Field.lastDragItemGid = nil
    if not self._bDepositVisible then
        local bOnlyRemove = #self._eventList == 1 and self._eventList[1][1] == ECabinetGridDiffType.Remove
        if bOnlyRemove then
            Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.Loc.ItemPutInDeposit)
        end
    end
    self._eventList = {}
    if targetGridId then
        self:_OnFocusDIYCabinetGrid(targetGridId)
    end
    LuaTickController:Get():RemoveTick(self)
end

function CollectionRoomDIYCabinetPanel:_InitAutoScrollWidget()
	self._wtUpMask = self:Wnd("wtUpMask", CommonDragDropMask)
	self._wtUpMask:Visible()
	self._wtDownMask = self:Wnd("wtDownMask", CommonDragDropMask)
	self._wtDownMask:Visible()

	---@type AutoScrollParam
	local param = {}
	param.upMask = self._wtUpMask
	param.downMask = self._wtDownMask
    param.scrollSpeed = DFMGlobalConst.GetDepositConstNumber("AutoScrollSpeed", Module.Inventory.Config.AUTO_SCROLL_SPEED)
	param.bindScrollBox = self._wtIVWaterfallScroll
	param.scrollCaller = self
    param.bEnableByHD = false
	self._autoScrollWidget = Module.CommonWidget:CreateScrollComponent(param)
	local LootingConfig = Module.Looting.Config
	self._autoScrollWidget:SetConstantAccelerationMode(LootingConfig.SCROLL_ACCELERATION, LootingConfig.SCROLL_MIN_SPEED, LootingConfig.SCROLL_MAX_SPEED)

	self._autoScrollWidget:EnableComponent(true)
end

function CollectionRoomDIYCabinetPanel:RefreshCabinetPanel(cabinetID)
    self._DIYCabinetId = cabinetID
    self._wtIVWaterfallScroll:RefreshAllItems()
end

-----------------------------------------------------------------------
--region InitWidget

function CollectionRoomDIYCabinetPanel:_OnGetListCount()
    if not self._DIYCabinetId then
        return
    end
    return CollectionRoomLogic.GetDIYCabinetBoothNum(self._DIYCabinetId)
end

---@param itemWidget CollectionRoomDIYBoothCanbinetList
function CollectionRoomDIYCabinetPanel:_OnProcessCabinetListWidget(position, itemWidget)
    local diyCabinetTable = Module.CollectionRoom.Field.collectionDIYCabinetData[self._DIYCabinetId]
    local sortID = CollectionRoomConfig.DIYPosToSortID[position]
    itemWidget:RefreshItemList(diyCabinetTable[sortID], self._DIYCabinetId, position)
end

function CollectionRoomDIYCabinetPanel:InitPriceTxt()
    local price = Server.CollectionRoomServer:GetDIYCabinetPrice(self._DIYCabinetId) and Server.CollectionRoomServer:GetDIYCabinetPrice(self._DIYCabinetId) or 0
    local priceTxt = MathUtil.GetNumberFormatStr(price)
    self._wtValue:SetText(priceTxt)
    self._wtValueTxt:SetText(CollectionRoomConfig.Loc.DIYCabinetValue)
end

function CollectionRoomDIYCabinetPanel:ToggleTips()
    if not self._wtDFTipsAnchor then return end
    if self.hoverHandle then
        self:_OnHideTips()
    else
        self:_OnShowTips()
    end
end

function CollectionRoomDIYCabinetPanel:_OnShowTips()
    if not self._wtDFTipsAnchor then return end
    if self.hoverHandle then
        return
    end

    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = CollectionRoomConfig.Loc.DIYCabinetTips}
    }},self._wtDFTipsAnchor)
end

function CollectionRoomDIYCabinetPanel:_OnHideTips()
    if self.hoverHandle and self._wtDFTipsAnchor then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

function CollectionRoomDIYCabinetPanel:Destroy()
    if self._autoScrollWidget then
        self._autoScrollWidget:Release()
        self._autoScrollWidget = nil
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 

function CollectionRoomDIYCabinetPanel:_OnLeftBtnClicked()
    if self._cameraId > 1 then
        self:_SetViewTargetToCameraId(self._cameraId - 1)
    end
end

function CollectionRoomDIYCabinetPanel:_OnRightBtnClicked()
    if self._cameraId < #CollectionRoomConfig.CameraConfig.DIYCabinet[self._DIYCabinetId] then
        self:_SetViewTargetToCameraId(self._cameraId + 1)
    end
end

function CollectionRoomDIYCabinetPanel:_OnCameraBtnClicked(index)
    self:_SetViewTargetToCameraId(index)
end

function CollectionRoomDIYCabinetPanel:_SetViewTargetToCameraId(cameraId)
    self._cameraId = cameraId
    for index, btn in ipairs(self._wtCameraBtnList) do
        if index == cameraId then
            btn:SetStyle(1)
        else
            btn:SetStyle(0)
        end
    end
    if cameraId == 1 then
        self._wtSwitchToLeftCameraBtn:SetIsEnabled(false)
        self._wtSwitchToRightCameraBtn:SetIsEnabled(true)
    elseif cameraId == #self._wtCameraBtnList then
        self._wtSwitchToLeftCameraBtn:SetIsEnabled(true)
        self._wtSwitchToRightCameraBtn:SetIsEnabled(false)
    else
        self._wtSwitchToLeftCameraBtn:SetIsEnabled(true)
        self._wtSwitchToRightCameraBtn:SetIsEnabled(true)
    end
    CollectionRoomLogic.SetViewTargetToNamedCamera(CollectionRoomConfig.CameraConfig.DIYCabinet[self._DIYCabinetId][cameraId])
end

function CollectionRoomDIYCabinetPanel:_OnFocusDIYCabinetGrid(gridId)
    local config = CollectionRoomLogic.GetDIYCabinetGridConfig(self._DIYCabinetId, gridId)
    if config and CollectionRoomConfig.CameraConfig.DIYCabinet[self._DIYCabinetId][config.CameraID] then
        self:_SetViewTargetToCameraId(config.CameraID)
    end
end

function CollectionRoomDIYCabinetPanel:_OnCabinetGridChange(cabinetGridDiffType, cabinetType, cabinetId, gridId)
    if cabinetType == EShowCabinetType.DIY and cabinetId == self._DIYCabinetId then
        table.insert(self._eventList, {cabinetGridDiffType, cabinetType, cabinetId, gridId})
        LuaTickController:Get():RegisterTick(self)
    end
    if cabinetType == EShowCabinetType.DIY and (cabinetGridDiffType == ECabinetGridDiffType.Add or cabinetGridDiffType == ECabinetGridDiffType.Remove) then
        if cabinetGridDiffType == ECabinetGridDiffType.Add then
            self:_ScrollToIndexByGridId(gridId)
        end
        self:InitPriceTxt()
    end
end

function CollectionRoomDIYCabinetPanel:_ScrollToIndexByGridId(gridId)
    local diyCabinetTable = Module.CollectionRoom.Field.collectionDIYCabinetData[self._DIYCabinetId]
    for index, rackSlotLists in pairs(diyCabinetTable) do
        for idx, rackSlotInfo in pairs(rackSlotLists) do
            if rackSlotInfo.RackSlotID == gridId then
                -- 获取widget
                local listWidget = self._wtIVWaterfallScroll:GetItemByIndex(index == 2 and 2 or 1)
                local itemWidget = listWidget and listWidget._wtItemWaterfallScroll:GetItemByIndex(idx - 1)
                if itemWidget then
                    self._wtIVWaterfallScroll:ScrollWidgetIntoView(itemWidget, true, EDescendantScrollDestination.Center)
                end
                return
            end
        end
    end
end

---@desc 切换展示台的不同柜子
function CollectionRoomDIYCabinetPanel:SwitchCabinetId(toCabinetId)
    self:OnInitExtraData(toCabinetId)
end

function CollectionRoomDIYCabinetPanel:_OnSwitchCabinetBtnClicked()
    if not self._switchCabinetHandle then
        ---@param uiIns CollectionRoomSwitchCabinet
        local function fLoadFinCallback(uiIns)
            self._wtSwitchCabinetBtn:Collapsed()
            uiIns:SetParentAndCurrentCabinet(self, EShowCabinetType.DIY, self._DIYCabinetId)
            uiIns:RefreshEntrancesList()
        end
        self._switchCabinetHandle = Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomSwitchCabinet, fLoadFinCallback)
    end
    self._wtSwitchCabinetBtn:Collapsed()
end

function CollectionRoomDIYCabinetPanel:_HideSwitchCabinetSide()
    if self._switchCabinetHandle then
        Facade.UIManager:CloseUIByHandle(self._switchCabinetHandle)
        self._switchCabinetHandle = nil
    end
end

function CollectionRoomDIYCabinetPanel:OnSwitchCabinetHide()
    self._wtSwitchCabinetBtn:SelfHitTestInvisible()
    self._switchCabinetHandle = nil
end

--endregion
-----------------------------------------------------------------------

function CollectionRoomDIYCabinetPanel:_OnItemDoubleClicked(itemview)
    local item = itemview.item

    -- 双击分为卸下和放置
    if item:IsCollectionCabinetItem() then
        CollectionRoomLogic.UnShelveDIYCabinet(item)
    else
        local DIYCabinetInfo = Module.CollectionRoom.Field.collectionDIYCabinetData[self._DIYCabinetId]
        CollectionRoomLogic.ShevleDIYCabinet(item, self._DIYCabinetId, DIYCabinetInfo)
    end
end

function CollectionRoomDIYCabinetPanel:_RefreshBottomBtnGamepad(inputSummaryList)
    if WidgetUtil.IsGamepad() then
        local bIsInExtArrangeMode = self._wtWarehousePanel._bInExtArrangeMode == true
        local bIsInSwapingItem = Module.CommonWidget:IsInSwapingItem() == true
        if bIsInExtArrangeMode or bIsInSwapingItem then
            return
        end
    end
    self:_RefreshBottomBtn(self._bDepositVisible , inputSummaryList)
end

function CollectionRoomDIYCabinetPanel:_RefreshBottomBtn(bVisible, inputSummaryList)
    if IsHD() and WidgetUtil.IsGamepad() and (Module.CommonWidget:IsInSwapingItem() or Module.Inventory:GetIsYMode()) then
        return
    end
    self._bDepositVisible = bVisible
    local summaryList = {}

    if bVisible then
        if WidgetUtil.IsGamepad() then
            table.insert(summaryList, {actionName = "FoldDeposit_Gamepad", func = CreateCallBack(self._SwitchDepositVisibility, self, false)})
        else
            table.insert(summaryList, {actionName = "FoldDeposit", func = CreateCallBack(self._SwitchDepositVisibility, self, false)})
        end

        -- Module.CommonBar:RecoverBottomBarInputSummaryList()
        if inputSummaryList then
            for _, summary in pairs(inputSummaryList) do
                table.insert(summaryList, summary)
            end
        end

        if WidgetUtil.IsGamepad() then
            table.insert(summaryList, {actionName = "Common_ToggleTip", func = self.ToggleTips, caller = self,bUIOnly = false, bHideIcon = false})
            table.insert(summaryList, {actionName = "ConfirmArrange_Gamepad", func = self._wtWarehousePanel._OnExtArrangeBtnPress, caller = self._wtWarehousePanel ,bUIOnly = false, bHideIcon = false})
        end
        -- Module.CommonBar:RegStackUIInputSummary(self, summaryList)
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
    else
        table.insert(summaryList, {actionName = "Back", func = CreateCallBack(self._CloseSelf, self)})
        -- table.insert(summaryList, {actionName = "UnfoldDeposit", func = CreateCallBack(self._SwitchDepositVisibility, self, true)})
        if WidgetUtil.IsGamepad() then
            table.insert(summaryList, {actionName = "UnFoldDeposit_Gamepad", func = CreateCallBack(self._SwitchDepositVisibility, self, true)})
        else
            table.insert(summaryList, {actionName = "UnfoldDeposit", func = CreateCallBack(self._SwitchDepositVisibility, self, true)})
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
    end
end

function CollectionRoomDIYCabinetPanel:_CloseSelf()
    Facade.UIManager:CloseUI(self)
end

function CollectionRoomDIYCabinetPanel:_SwitchDepositVisibility(bVisible)
    bVisible = setdefault(bVisible, false)
    if bVisible then
        self:StopWidgetAnim(self.WBP_CollectionHome_DIYCabinet_PC_close)
        self:PlayWidgetAnim(self.WBP_CollectionHome_DIYCabinet_PC_open)
    else
        self:StopWidgetAnim(self.WBP_CollectionHome_DIYCabinet_PC_open)
        self:PlayWidgetAnim(self.WBP_CollectionHome_DIYCabinet_PC_close)
    end
    self:_RefreshBottomBtn(bVisible)
    LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.SwitchDepositFoldState)
    self:_OnHideTips()
end

function CollectionRoomDIYCabinetPanel:_OnDragEnterDIYGrid(cabinetId, gridId)
    if cabinetId == self._DIYCabinetId then
        self:_OnFocusDIYCabinetGrid(gridId)
    end
end
--region 手柄
function CollectionRoomDIYCabinetPanel:_EnableGamepadFeature()
    if not IsHD() then
        return
    end
    self:_RegisterNavGroup()
    Module.CommonWidget:BindCommonIVInputLogic()
    Module.CommonWidget:EnableGamepadSwapItem(true)
    Module.CommonWidget:EnableGamepadCarryItemFromPop(true)
    Module.CommonWidget:EnableGamepadItemShortcutKey(true)
end

function CollectionRoomDIYCabinetPanel:_DisableGamepadFeature()
    if not IsHD() then
        return
    end

    self:_RemoveNavGroup()
    Module.CommonWidget:EnableGamepadSwapItem(false)
    Module.CommonWidget:EnableGamepadCarryItemFromPop(false)
    Module.CommonWidget:EnableGamepadItemShortcutKey(false)
end

function CollectionRoomDIYCabinetPanel:_RegisterNavGroup()
    if not IsHD() then
        return
    end

    if not self._navGroup0 then
        self._navGroup0 = WidgetUtil.RegisterNavigationGroup(self._wtSwitchCabinetBtn, self, "Hittest")
        if self._navGroup0 then
            self._navGroup0:AddNavWidgetToArray(self._wtSwitchCabinetBtn)
        end
    end

    if not self._navGroup1 then
        self._navGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtIVWaterfallScroll, self, "Hittest")
        local WareHouseNavStrategy = self._navGroup1:GetOwnerNavStrategy()
        if WareHouseNavStrategy then
            WareHouseNavStrategy:SetHitPadding(5.0)
        end
        if self._navGroup1 then
            self._navGroup1:AddNavWidgetToArray(self._wtIVWaterfallScroll)
            self._navGroup1:SetScrollRecipient(self._wtIVWaterfallScroll)
        end
    end

    InventoryNavManager.DIYPanel = self._wtIVWaterfallScroll
    InventoryNavManager.DIYPanelNavGroup = self._navGroup1

    if not self._navGroup2 then
        ---@type Warehouse
        local wareHouse = self._wtWarehousePanel:GetWarehouseWidget()
        self._navGroup2 = WidgetUtil.RegisterNavigationGroup(wareHouse, self, "LuaExtension")
        -- self._navGroup2 = WidgetUtil.RegisterNavigationGroup(wareHouse, self, "Hittest")
        if self._navGroup2 then
            self._navGroup2:AddNavWidgetToArray(wareHouse)
            self._navGroup2:SetScrollRecipient(wareHouse._wtDepositScrollBox)
        end
        local WareHouseNavStrategy = self._navGroup2:GetOwnerNavStrategy()
        if WareHouseNavStrategy then
            WareHouseNavStrategy:SetHitPadding(5.0)
        end
        -- WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroupWareHouse)
        -- InventoryNavManager.FocusToDefaultFocusPoint()
    end

    InventoryNavManager.warehousePanel = self._wtWarehousePanel

    if not self._navGroup3 then
        local wtWarehouseTab = self._wtWarehousePanel:Wnd("wtWarehouseTab", UIWidgetBase)
        self._navGroup3 = WidgetUtil.RegisterNavigationGroup(wtWarehouseTab, self, "Hittest")
        if self._navGroup3 then
            self._navGroup3:AddNavWidgetToArray(wtWarehouseTab)
        end
        local warehouseTabNavStrategy = self._navGroup3:GetOwnerNavStrategy()
        if warehouseTabNavStrategy then
            warehouseTabNavStrategy:SetHitPadding(5.0)
            warehouseTabNavStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.HittestOrDistance)
        end
    end

    -- if self._navGroup2 then
    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup2)
    -- end
end

function CollectionRoomDIYCabinetPanel:_RemoveNavGroup()
    if not IsHD() then
        return
    end

    if self._navGroup0 or self._navGroup1 or self._navGroup2 or self._navGroup3 then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup0 = nil
        self._navGroup1 = nil
        self._navGroup2 = nil
        self._navGroup3 = nil
    end
end

function CollectionRoomDIYCabinetPanel:_OnEnterExtArrangeMode(bEnter)
    --BEGIN MODIFICATION @ VIRTUOS : Reset the UI Navigation Group
    if bEnter == true then
        --移除基础导航组
        self:_RemoveNavGroup()
        --创建整理扩容箱导航组
        if self._wtWarehousePanel.RegisterArrangeNavGroup then
            self._wtWarehousePanel:RegisterArrangeNavGroup()
        end
    else
        --移除管理扩容箱导航组
        if self._wtWarehousePanel.RemoveArrangeNavGroup then
            self._wtWarehousePanel:RemoveArrangeNavGroup()
        end
        --创建基础导航组

        if self:IsVisible() then
            self:_RegisterNavGroup()
        end
    end
    --END MODIFICATION
end

function CollectionRoomDIYCabinetPanel:_OnGlobalSwapItem(bIsSwapItem)
    if not IsHD() then
        return 
    end
    InventoryNavManager.UpdateInputSummaryOnDrag(true)

    UDFNavigationSelectorBase.SetForceHideSelectorRoot(bIsSwapItem)
end

--endregion

function CollectionRoomDIYCabinetPanel:_OnMouseButtonDown(mouseEvent)
    if isvalid(self._wtIVWaterfallScroll) and type(self._wtIVWaterfallScroll.GetCachedGeometry) == "function" then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = self._wtIVWaterfallScroll:GetCachedGeometry()
        local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
        if not isUnder then
            CollectionRoomConfig.Events.evtOnClickedDIYGrid:Invoke(nil)
            Module.CollectionRoom:SetCurCabinetInfo(nil)
        end
    end
end

return CollectionRoomDIYCabinetPanel