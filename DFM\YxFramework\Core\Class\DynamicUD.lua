local function log(...)
    loginfo("[DynamicUD]", ...)
end

local gEnableDynamicUD = true
local gEnableOverrideClassByDynamicUD = true
local backupUDClass = {
    new = function()
        return {}
    end,
    allow_undefined_field = function() end
}

local overrideClassMeta = {
    __newindex = function(t, k, v)
        if type(k) == "string" and type(v) == "function" then
            local dynamicUDDefine = t.__dynamicUDDefine
            if dynamicUDDefine then
                dynamicUDDefine:add_func(k, v)
            end
        end
        rawset(t, k, v) 
    end
}

local t = {}
t.Integer = FieldType.Integer
t.Number = FieldType.Number
t.String = FieldType.String
t.Boolean = FieldType.Boolean
t.Function = FieldType.Function
t.Table = FieldType.Table
t.Userdata = FieldType.Userdata
t.RESERVER = FieldType.RESERVER
FieldType = t

function EnableDynamicUD(bEnable)
    log("EnableDynamicUD", gEnableDynamicUD, bEnable)
    gEnableDynamicUD = bEnable
end

function MakeDynamicUDClass(...)
    if not gEnableDynamicUD then
        log("MakeDynamicUDClass not enabled")
        return backupUDClass
    end
    local dynamicUDDefine = NewDynamicUD(...)
    return dynamicUDDefine
end

function OverrideClassByDynamicUD(class2Override, allowUndefinedField, ...)
    if not gEnableDynamicUD or not gEnableOverrideClassByDynamicUD then
        log("OverrideClassByDynamicUD not enabled")
        return
    end

    assert(type(class2Override) == "table")
    assert(getmetatable(class2Override) == nil)

    local parentClass = class2Override._parentclass
    assert(parentClass == Object or parentClass == LuaObject)

    local dynamicUDDefine = NewDynamicUD(
        {"_has_destroy_", FieldType.Boolean},
        {"_meta_", FieldType.RESERVER},
        {"_cname", FieldType.RESERVER},
        ...
    )
    dynamicUDDefine:set_reserve_field("_meta_", 1, class2Override)
    dynamicUDDefine:set_reserve_field("_cname", 2, class2Override._cname)
    dynamicUDDefine:add_func_from_table(class2Override)
    dynamicUDDefine:mark_as_dfclass(true)
    dynamicUDDefine:allow_undefined_field(allowUndefinedField)

    class2Override.__dynamicUDDefine = dynamicUDDefine
    setmetatable(class2Override, overrideClassMeta)
    class2Override.New = function(self, ...)
        local ins = dynamicUDDefine:new(...)
        return ins
    end
    class2Override.NewIns = class2Override.New

    log(string.format("OverrideClassByDynamicUD UDDefine=%s Class2Override=%s", dynamicUDDefine, class2Override._cname))

    return class2Override
end