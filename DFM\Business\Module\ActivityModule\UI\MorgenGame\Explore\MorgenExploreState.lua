---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

---对应蓝图:WBP_Example
---@class MorgenExploreState : LuaUIBaseView
local MorgenExploreState = ui("MorgenExploreState")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"

local MogenStateColor = {
	[1] = ColorUtil.GetLinearColorByHex("676767FF"),
	[2] = ColorUtil.GetLinearColorByHex("214D76FF"),
	[3]  = ColorUtil.GetLinearColorByHex("AE4A07FF"),
}

function MorgenExploreState:Ctor()

	-- 暂时使用盾兵的spine
	-- local Atalas = "SpineAtlasAsset'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Spine/Spine_UI_Asa_Shield/Spine_UI_Asa_Shield.Spine_UI_Asa_Shield-atlas'"
	-- local Skeleton = "SpineSkeletonDataAsset'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Spine/Spine_UI_Asa_Shield/Spine_UI_Asa_Shield.Spine_UI_Asa_Shield-data'"
	self._wtSpine = self:Wnd("DFCommonSpine", UIWidgetBase)
	-- if self._wtSpine.SetData then
	-- 	self._wtSpine.__cppinst:SetData(Atalas, Skeleton)
	-- end
	

    self._wtStatePanel = self:Wnd("DFHorizontalBox_0", UIWidgetBase)
    self._wtStateBorder = self:Wnd("DFBorder_75", UIWidgetBase)
	self._wtStateText = self:Wnd("DFTextBlock_21", UITextBlock)
    self._wtIncomeText = self:Wnd("DFTextBlock_85", UITextBlock)
	self._wtIncomeText:SetText(ActivityConfig.Loc.MogenExploreText[2])
	
    self._wtLocationText = self:Wnd("DFCommonTextBlock_0", UITextBlock)
	
    self._wtSanityText = self:Wnd("DFTextBlock_33", UITextBlock)
    self._wtBringText = self:Wnd("DFRichTextBlock_61", UITextBlock)
end

function MorgenExploreState:RefreshInfo(position, san_num, money_num)
	self._wtLocationText:SetText(ActivityLogic.HandleLocalizeText(position))
	
	self._wtSanityText:SetText(string.format(ActivityConfig.Loc.MorgenSanity, san_num))

	local StoneImg = "ArknightsCurrenc2"
	local MoneyText = StringUtil.Key2StrFormat(Module.Activity.Config.Loc.CurrencyStr, {img = StoneImg, num = money_num})
	self._wtBringText:SetText(MoneyText)
end

--- 1未开始，2探索中，3探索完成，4中止
function MorgenExploreState:RefreshState(i)

	local animTable = {
		[1] = "idle",
		[2] = "fight",
		[3] = "idle",
		[4] = "idle",
	}

	local animName = animTable[i]

	self._wtSpine:PlaySpineAnimation(0, animName, true)

	if i == 1 then
		self._wtStatePanel:Collapsed()
	else
		self._wtStatePanel:Visible()
		self._wtStateBorder:SetBrushColor(MogenStateColor[i - 1])
		self._wtStateText:SetText(ActivityConfig.Loc.MogenExploreStateText[i - 1])
	end
end

return MorgenExploreState