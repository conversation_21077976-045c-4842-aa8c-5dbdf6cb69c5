----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityRadioMainPanel : LuaUIBaseView
local ActivityRadioMainPanel = ui("ActivityRadioMainPanel")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ActivityJumpBtn = require "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityJumpBtn"
local ActivityRadioScreen = require "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioScreen"
local NavigationAgent = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local InputBindingAgent = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local UKismetInputLibrary = import "KismetInputLibrary"
local ADJUST_TIMES_KEY = "ACT_SBC_ADJUST_TIMES_"
local ACT_RADIO_TYPE_BROWSE = "ACT_RADIO_TYPE_BROWSE"
local ACT_RADIO_INS_BROWSE_ = "ACT_RADIO_INS_BROWSE_"
local AUTO_ANIM_TIME = 2
local ONE_BYTE_TIME = 0.15

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local function testLog(...)
    loginfo("[SBC_TEST] ", ...)
end

local function testWarningLog(...)
    logwarning("[SBC_TEST] ", ...)
end

local function testErrorLog(...)
    logerror("[SBC_TEST] ", ...)
end

ActivityRadioMainPanel.EChannel = {
    GTI   = 1,
    Asara = 2,
    Hafk  = 3,
}

ActivityRadioMainPanel.AUDIO_NAME = {
    [1] = "UI_Events_SBCRadio_Music_Ahsarah",        -- 播放/切换到阿萨拉电台主题曲
    [2] = "UI_Events_SBCRadio_Music_GTI",            -- 播放/切换到GTI电台主题曲
    [3] = "UI_Events_SBCRadio_Music_Hawk",           -- 播放/切换到哈夫克电台主题曲
    [4] = "UI_Events_SBCRadio_Music_Stop",           -- 停止所有音乐
    [5] = "UI_Events_SBCRadio_Glitch",               -- 调台音效，点击调台时播放
    [6] = "UI_Events_SBCRadio_Telegram",             -- 字幕出现时播放
    [7] = "UI_Events_SBCRadio_Telegram_Stop",        -- 字幕停止时播放

    [8] = "UI_Events_SBCRadio_VO_Ahsarah_She3_01",   -- 阿萨拉阶段一引导
    [9] = "UI_Events_SBCRadio_VO_Ahsarah_She3_02",   -- 阿萨拉阶段二引导
    [10] = "UI_Events_SBCRadio_VO_Ahsarah_She3_03",  -- 阿萨拉阶段三引导
    [11] = "UI_Events_SBCRadio_VO_GTI_She3_01",      -- GTI阶段一引导
    [12] = "UI_Events_SBCRadio_VO_GTI_She3_02",      -- GTI阶段二引导
    [13] = "UI_Events_SBCRadio_VO_GTI_She3_03",      -- GTI阶段三引导
    [14] = "UI_Events_SBCRadio_VO_Haavk_She3_01",    -- 哈夫克阶段一引导
    [15] = "UI_Events_SBCRadio_VO_Haavk_She3_02",    -- 哈夫克阶段二引导
    [16] = "UI_Events_SBCRadio_VO_Haavk_She3_03",    -- 哈夫克阶段三引导
    [17] = "UI_Events_SBCRadio_VO_Stop",             -- 停止所有AI音频引导
}

local SKIP_BTN_INFO = {
    title = {
        [1] = ActivityConfig.Loc.HotProducts,
        [2] = ActivityConfig.Loc.TuningObtain,
        [3] = ActivityConfig.Loc.NewsRecord,
    },
    picture = {
        [1] = ActivityConfig.RadioNewsIcon[1],
        [2] = ActivityConfig.RadioNewsIcon[2],
        [3] = ActivityConfig.RadioNewsIcon[3],
    }
}

local POPUP_TYPE = {
    NONE = 0,
    RULE_DESCRIPTION = 1,
    HOT_POPWINDOW = 2
}

function ActivityRadioMainPanel:Ctor()
    self._navMgr = NavigationAgent:NewIns(self)
    self._inputMgr = InputBindingAgent:NewIns(self)
    self._inputMgr:AddBinding(
        "TuneRadio_X",
        {
            actionName = "RadioOnAdjust_X",
            callback = self.OnAdjustBtnClicked,
            caller = self
        },
        true
    )

    self._activityID = 0
    self._activityInfo = {}
    self._taskList = {}
    self._sbcInfo = {}
    self._tuningTimes = 0
    self._awardList = {}
    self._selectedAwardList = {}
    self._awardTimeList = {}
    self._newsIDList = {}
    self._exchangePropList = {}
    self._radioConfig = {}

    self._curSequence = 0
    self._maxSequence = 0
    self._curChannel = 0
	self._childIdx = 0
    self._curContentIdx = 1

    self._fGetCommonCtrl = nil
    self._currentChildPage = nil
    self._btnAnimTimerHandle = nil
    self._screenAnimTimerHandle = nil
    self._captionAudioTimerHandle = nil

    self._bIntro = false
	
    -- 子UI画布
    self._wtCanvasPage = self:Wnd("DFCanvasPanel_22", UIWidgetBase)
    self._wtCanvasPage:Visible()

    -- 主面板屏幕
	self._wtRadioScreen = self:Wnd("WBP_RadioNews_Screen", ActivityRadioScreen)

    -- 可调节次数
    self._wtAdjustTimesPanel = self:Wnd("PlatformSizeBox_2", UIWidgetBase)
    self._wtAdjustTimesTxt = self:Wnd("DFTextBlock_161", UITextBlock)

    -- 调节按钮
    self._wtAdjustBtn = self:Wnd("DFButton_85", UIButton)
    self._wtAdjustBtn:Event("OnClicked", self.OnAdjustBtnClicked, self)

    -- 调节按钮颜色
    self._wtAdjustGreenPanel = self:Wnd("DFCanvas_Green", UIWidgetBase)
    self._wtAdjustGrayPanel = self:Wnd("DFCanvas_Gray", UIWidgetBase)

    -- 底部字幕
    self._wtCaptionPanel = self:Wnd("DFCanvasPanel_59", UIWidgetBase)
    self._wtCaptionTxt = self:Wnd("RichText", UITextBlock)

    -- 右侧信息栏
    self._wtInfoPanel = self:Wnd("DFVerticalBox_0", UIWidgetBase)
    self._wtSkipBtnList = {
        [1] = self:Wnd("WBP_Event_SkipBtn", ActivityJumpBtn),
        [2] = self:Wnd("WBP_Event_SkipBtn_1", ActivityJumpBtn),
        [3] = self:Wnd("WBP_Event_SkipBtn_2", ActivityJumpBtn),
    }

    -- 子UI面板
    self._wtChildPanel = self:Wnd("DFCanvasPanel_66", UIWidgetBase)

    -- 禁用滚动控件集合
    self._wtHotzone = {
        self._wtInfoPanel,
        self._wtChildPanel,
    }

    -- 禁用点击控件集合
    self._wtGeometryTable = {
        self._wtChildPanel
    }
    
    self:InjectLua()
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityRadioMainPanel:_InitEvent()
    self:_RegisterBottomBar()
    self:_AddMouseButtonDownEvent()

    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityInfoUpdate, self._OnDataChanged, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTaskChange, self._OnTaskRewardChanged, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityRadioAdjustChange, self._OnDataChanged, self)

    self:AddLuaEvent(ActivityConfig.evtRadioPopWindowShow, self.OnPopShow, self)
    self:AddLuaEvent(ActivityConfig.evtRadioPopWindowHide, self.OnPopHide, self)
end

function ActivityRadioMainPanel:OnInitExtraData(activityID, fGetCommonCtrl)
    self._activityID = activityID
    self._fGetCommonCtrl = fGetCommonCtrl
end

function ActivityRadioMainPanel:OnOpen()
    math.randomseed(os.time())
end

function ActivityRadioMainPanel:OnShowBegin()
    if self:_JudgeIsOnHide() then return end

    self:_InitEvent()
    self:_InitLogTime()
    
    self:_InitData()
    self._newsID = nil
    
    self:_RecordActViewStatus()

    self:RestoreSpecialLayout()
    self:ResetToIntroContent()
    self:RefreshUIAndTimerOnFirst()
    self:_PlayAdjustBtnLoopAnim()

    self:_InitGamepadInputs()
end

function ActivityRadioMainPanel:OnShow()
    self._navMgr:FocusGroup("SideBar")
    self:_AddInputSummary()
end

function ActivityRadioMainPanel:OnHideBegin()
    self:_SendLogTime(2)
    self:_ClearLogTime()

    self:_OnRemoveSubUI()
    self:_OnRefreshScreenPanel(nil, nil, false)
    
    self:_CancelDelayAndAutoPlay()
    self:ReleaseTimer()

    self:_RemoveMouseButtonDownEvent()
    self:RemoveAllLuaEvent()

    -- self:ProcessSpecialLayout()
    Module.CommonBar:BindPersistentBackHandler()
    ActivityConfig.evtBackBtnChanged:Invoke()
    self:_DisableGamepadInputs()
end

function ActivityRadioMainPanel:OnClose()
    self._wtHotzone = nil
    self._wtGeometryTable = nil
    self._wtSkipBtnList = nil
end

function ActivityRadioMainPanel:_JudgeIsOnHide()
    if self._fGetCommonCtrl then
        local mainPanel = self._fGetCommonCtrl(EActivityCommCtrl.ActivityMainPanel)
        local activityID = mainPanel and mainPanel._activityID
        if activityID ~= self._activityID then
            return true
        end
    end

    return false
end

function ActivityRadioMainPanel:OnPopShow()
    self:_RemoveMouseButtonDownEvent()
end

function ActivityRadioMainPanel:OnPopHide()
    self:_AddMouseButtonDownEvent()
end

function ActivityRadioMainPanel:_AddMouseButtonDownEvent()
    if not self._buttonownHandle then
        local gamelnst = GetGameInstance()
        self._buttonownHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
    end
end

function ActivityRadioMainPanel:_RemoveMouseButtonDownEvent()
    if self._buttonownHandle then
        local gamelnst = GetGameInstance()
        UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._buttonownHandle)
        self._buttonownHandle = nil
    end
end

function ActivityRadioMainPanel:_OnMouseButtonDown(mouseEvent)
    if isinvalid(self._currentChildPage) then return end

    if not ActivityLogic.CheckClickInterval() then return end

    local detailIns = Module.ItemDetail:GetItemDetailPanelUIIns()
    local bRewardIns = Module.Reward:IsShowingPopUI()
    local sceenPos = mouseEvent:GetScreenSpacePosition()
    local isUnder = false
    for _, widget in ipairs(self._wtGeometryTable or {}) do
        local geometry = widget:GetCachedGeometry()
        isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
        if isUnder then
            break
        end
    end
    
    if not isUnder and not detailIns and not bRewardIns then
        self._buttonDownTimeHandle = Timer.DelayCall(0.1, function()
            if self._currentChildPage then
                self:BackToMainPanel()
            end
        end)
    end
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityRadioMainPanel:_InitData()
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)

    if activityInfo == nil or next(activityInfo) == nil then return end

    -- 基础数据
    self._activityInfo = activityInfo
    self._sbcInfo = activityInfo.sbc_info

    if self._sbcInfo == nil or next(self._sbcInfo) == nil then return end

    -- 调台获取任务列表
    self._taskList = self._activityInfo.task_info or {}

    -- 当前剩余调台次数
    self._tuningTimes = self._sbcInfo.current_adjust_time or 0

    -- 已调台次数
    self._totalTuningTimes = self._sbcInfo.total_adjust_time or 0

    -- 可选奖励列表 ---@type SBCAward[]
    self._awardList = self._sbcInfo.awards or {}

    -- 已选奖励列表 ---@type uint64[]
    self._selectedAwardList = self._sbcInfo.chosen_reward_ids or {}

    -- 选奖次数里程碑列表 ---@type int32[]
    self._awardTimeList = self._sbcInfo.award_adjust_time or {}
    
    -- 已播放的新闻id列表 ---@type uint64[]
    self._newsIDList = self._sbcInfo.news_id or {}

    -- 本期热品 ---@type PropInfo[]
    self._exchangePropList = self._sbcInfo.exchange_props or {}

    -- 本期军需处可兑换物资 ---@type uint64[]
    self._shopExchangeIDList = self._sbcInfo.shop_exchange_ids or {}

    -- 新闻配置 ---@type table<int64, ActivitySBCNews>
    self._radioConfig = self:GetRadioConfigTable()

    -- 活动周期 ---@type string
    self._version = self._sbcInfo.version or ""

    -- 当前批次
    self._curSequence = self:_InitCurSequence()

    -- 最大批次
    self._maxSequence = self:_InitMaxSequence()

    -- 当前频道
    self._curChannel = self:_InitCurChannnel()

    -- 切换皮肤
    self:SetBG(activityInfo.sbc_info.back_image_id or 0)

    self:_HandleLogicTxt()
end

function ActivityRadioMainPanel:_InitCurSequence()
    local curSequence = 1

    for _, newsID in ipairs(self._newsIDList) do
        local radioConfig = self._radioConfig[newsID]
        if radioConfig and radioConfig.sequence > curSequence then
            curSequence = radioConfig.sequence
        end
    end

    return curSequence
end

function ActivityRadioMainPanel:_InitMaxSequence()
    local maxSequence = 1

    for _, value in pairs(self._radioConfig) do
        if value.sequence > maxSequence then
            maxSequence = value.sequence
        end
    end

    return maxSequence
end

function ActivityRadioMainPanel:_InitCurChannnel()
    local curChannel = 2
    local lastID = self._newsIDList[#self._newsIDList]

    for key, value in pairs(self._radioConfig) do
        if key == lastID then
            curChannel = value.channel
            break
        end
    end

    return curChannel
end

function ActivityRadioMainPanel:_HandleLogicTxt()
    self._sbcInfo.exchange_doc = ActivityLogic.HandleLocalizeText(self._sbcInfo.exchange_doc)
end

--- 查找可用的NewsID
---@param sequence number 指定批次
---@param channel number 指定频道
---@param newsID number 当前NewsID
---@param bIsIntro boolean 是否仅限引导类型
---@return number|nil newsID 返回可用的NewsID，无可用时返回nil
---@return boolean isNew 是否为新增的NewsID
function ActivityRadioMainPanel:_OnGetNewsID(sequence, channel, newsID, bIsIntro)
    local newsIDList_set = {}
    for _, newsID in ipairs(self._newsIDList) do
        newsIDList_set[newsID] = true
    end

    local function isIntroValid(value)
        if bIsIntro then
            return value.isIntro == 1
        else
            return value.isIntro ~= 1
        end
    end

    local findList = {}
    for key, value in pairs(self._radioConfig) do
        if not newsIDList_set[key] and value.sequence == sequence and value.channel == channel and isIntroValid(value) then
            table.insert(findList, key)
        end
    end

    -- 如果不为新增的newsID，需要尽量和之前的newsID不重复（除非满足条件的仅有一个）
    if #findList == 0 then
        local validItems = {}
        for key, value in pairs(self._radioConfig) do
            if value.sequence == sequence and value.channel == channel and isIntroValid(value) then
                table.insert(validItems, key)
            end
        end
        if #validItems > 0 then
            if #validItems == 1 then
                testWarningLog(string.format("检测到数据库中只有一个可用的newsID：%s, 当前批次：%s, 当前频道：%s", tostring(validItems[1]), tostring(sequence), tostring(channel)))
                return validItems[1], false
            end

            for i = #validItems, 1, -1 do
                if validItems[i] == newsID then
                    table.remove(validItems, i)
                    break
                end
            end

            local randomIndex = math.random(1, #validItems)
            return validItems[randomIndex], false
        else
            testErrorLog(string.format("数据库中没有可用的newsID, 当前批次：%s, 当前频道：%s, 当前NewsID：%s", tostring(sequence), tostring(channel), tostring(newsID)))
            return nil
        end
    end 

    local randomIndex = math.random(1, #findList)
    return findList[randomIndex], true
end

--- 判断当前批次是否已完成（是否可递增）
---@param sequence number 要检查的批次号
---@return boolean
function ActivityRadioMainPanel:_IsSequenceCompleted(sequence)
    if sequence >= self._maxSequence then
        return false
    end

    -- 构建已使用ID的快速查询表
    local usedSet = {}
    for _, id in ipairs(self._newsIDList) do
        usedSet[id] = true
    end

    -- 收集该批次所有配置的newsID，按频道分类
    local channelNewsIDs = {}
    for key, value in pairs(self._radioConfig) do
        if value.sequence == sequence then
            if not channelNewsIDs[value.channel] then
                channelNewsIDs[value.channel] = {}
            end
            table.insert(channelNewsIDs[value.channel], key)
        end
    end

    if not next(channelNewsIDs) then
        return false
    end

    -- 检查每个频道是否至少有一个新闻被听过
    for _, newsIDs in pairs(channelNewsIDs) do
        local channelCompleted = false
        for _, newsID in ipairs(newsIDs) do
            if usedSet[newsID] then
                channelCompleted = true
                break
            end
        end
        if not channelCompleted then
            return false
        end
    end

    return true
end

--- 循环获取正确的最新批次
---@param sequence number 起点判断批次，一般为当前批次
---@return number
function ActivityRadioMainPanel:_OnGetCorrectSequence(sequence)
    while self:_IsSequenceCompleted(sequence) do
        sequence = sequence + 1
    end
    return sequence
end

--- 获取最近最久未使用的其它频道（LRU策略）
---@param currentChannel number 当前频道
---@return number|nil channel 返回频道号，无可用时返回nil
function ActivityRadioMainPanel:_OnGetOtherChannel(currentChannel)
    local allChannels = {}
    for _, value in pairs(self._radioConfig) do
        allChannels[value.channel] = true
    end

    -- 生成候选频道列表（排除当前频道）
    local candidates = {}
    for channel, _ in pairs(allChannels) do
        if channel ~= currentChannel then
            table.insert(candidates, channel)
        end
    end

    if #candidates == 0 then return nil end

    -- 构建频道最后使用时间映射表
    -- key:频道号 value:最后出现的正序索引（越大表示越近使用）
    local lastUsedMap = {}
    for newsIndex, newsID in ipairs(self._newsIDList) do
        local config = self._radioConfig[newsID]
        if config then
            lastUsedMap[config.channel] = newsIndex
        end
    end

    local lruChannels = {}
    local minWeight = math.huge
    
    for _, channel in ipairs(candidates) do
        -- 未使用过的频道权重设为0（最优先）
        local weight = lastUsedMap[channel] or 0
        
        if weight < minWeight then
            minWeight = weight
            lruChannels = {channel}
        elseif weight == minWeight then
            table.insert(lruChannels, channel)
        end
    end

    -- 从候选中随机选择
    return lruChannels[math.random(1, #lruChannels)]
end

function ActivityRadioMainPanel:GetRadioConfigTable()
    local configTable = Facade.TableManager:GetTable("ActivitySBCNews")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        if value.ActivityID == self._activityID then
            local delimiter = "；"
            
            local content = string.split(value.Content, delimiter)
            for idx, str in ipairs(content) do
                content[idx] = string.gsub(str, "%$", "")
            end
            
            local lines = string.gsub(value.Content, "；", "")
            lines = string.gsub(lines, "%$", "\n")

            local subtitleDuration = string.split(value.SubtitleDuration, delimiter)
            local converted = {
                newsID              = value.NewsID,
                content             = content,
                lines               = lines,
                sequence            = value.Sequence,
                channel             = value.Channel,
                isIntro             = value.IsIntro,
                audio               = value.Audio,
                subtitleDuration    = subtitleDuration
            }
    
            allConfig[value.NewsID] = converted
        end
    end
    
    return allConfig
end

--- 获取对应任务下的调台次数
---@param taskID number|nil 不填则遍历
---@return number|nil 返回nil表示数据有误
function ActivityRadioMainPanel:GetTaskTunningTimes(taskID)
    if not taskID or type(taskID) ~= "number" then 
        local times = 0
        for _, taskInfo in ipairs(self._taskList) do
            if taskInfo.state == ActivityTaskState.ActivityTaskStateCompleted then
                local awardInfo = taskInfo.awards and taskInfo.awards[1] or {}
                local num = awardInfo.prop and awardInfo.prop.num or 0
                times = times + num
            end
        end
        return times
    end
    
    for _, taskInfo in ipairs(self._taskList) do
        if taskInfo.task_id == taskID then
            if taskInfo.state == ActivityTaskState.ActivityTaskStateCompleted then
                local awardInfo = taskInfo.awards and taskInfo.awards[1] or {}
                return awardInfo.prop and awardInfo.prop.num or 0
            end
        end
    end

    testErrorLog(string.format("调台次数有误，taskID：%s", tostring(taskID)))
    return nil
end

function ActivityRadioMainPanel:_OnTaskRewardChanged(activityID, taskID)
    if activityID ~= self._activityID then return end

    local curTuningTimes = self._tuningTimes
    local AddTuningTimes = self:GetTaskTunningTimes(taskID)
    if not AddTuningTimes then return end
    Module.CommonTips:ShowSimpleTip(string.format(ActivityConfig.Loc.AddAdjustTimes, AddTuningTimes))

    -- 先刷数据
    self:_OnDataChanged(self._activityID)
    
    -- 后刷表现
    if curTuningTimes == 0 and self._tuningTimes > 0 then -- 如果用 AddTuningTimes > 0 也可以，但是在当前界面使用GM会因为NTF导致错误显示Green
        self:_PlayAdjustBtnGrayToGreenAnim()
    end
end

function ActivityRadioMainPanel:_OnDataChanged(activityID)
    if activityID ~= self._activityID then return end

    self:_InitData()
    self:_RefreshSkinBtnReddot()
    self:_RefreshChildMainPanel()
end

function ActivityRadioMainPanel:_RecordActViewStatus()
    local showType = self:_OnShowPopWindow()
    if showType == POPUP_TYPE.HOT_POPWINDOW then
        self:_RecordActInsViewStatus()
    end
end

function ActivityRadioMainPanel:_RecordActTypeViewStatus()
    ActivityLogic.SetUserBoolean(ACT_RADIO_TYPE_BROWSE, true)
end

function ActivityRadioMainPanel:_RecordActInsViewStatus()
    local key = ACT_RADIO_INS_BROWSE_ .. self._activityID

    ActivityLogic.SetUserBoolean(key, true)
end

function ActivityRadioMainPanel:_GetActTypeViewStatus()
    return ActivityLogic.GetUserBoolean(ACT_RADIO_TYPE_BROWSE, false)
end

function ActivityRadioMainPanel:_GetActInsViewStatus()
    local key = ACT_RADIO_INS_BROWSE_ .. self._activityID

    return ActivityLogic.GetUserBoolean(key, false)
end

function ActivityRadioMainPanel:_InitLogTime()
    self._logTime = Facade.ClockManager:GetLocalTimestamp()
end

function ActivityRadioMainPanel:_ClearLogTime()
    self._logTime = nil
end

--- 埋点上报
---@param sType number 上报时机 [0:调台(default) 1:打开按钮 2:退出活动 3：关闭新闻记录]
function ActivityRadioMainPanel:_SendLogTime(sType)
    if self._logTime == nil then return end

    sType = setdefault(sType, 0)

    local curTime = Facade.ClockManager:GetLocalTimestamp()
    local stayTime = curTime - self._logTime
    self._logTime = curTime

    LogAnalysisTool.DoSendActivityClientSBCReportLog(self._activityID, self._newsID, self._curSequence, self._curChannel, stayTime, sType)
end

function ActivityRadioMainPanel:_AddInputSummary()
    ---@type ActivityMainPanel
    local mainPanel = self._fGetCommonCtrl(EActivityCommCtrl.ActivityMainPanel)
    if not mainPanel then return end

    if IsHD() then
        ---@type UIInputSummary
        local buttonX_TuneRadio = {
            actionName  = "RadioOnAdjust_X",
            bOnlyUI     = true,
        }
        mainPanel:UpdateCommonPanelSummaries()
        mainPanel:AddInputSummary(buttonX_TuneRadio)
        self._inputMgr:Activate("TuneRadio_X")
    end
end

function ActivityRadioMainPanel:_RemoveInputSummary()
    ---@type ActivityMainPanel
    local mainPanel = self._fGetCommonCtrl(EActivityCommCtrl.ActivityMainPanel)
    if not mainPanel then return end

    if IsHD() then
        mainPanel:RemoveInputSummaries()
        mainPanel:UpdateCommonPanelSummaries()
        self._inputMgr:Deactivate("TuneRadio_X")
    end
end

function ActivityRadioMainPanel:_InitGamepadInputs()
    self._navMgr:CreateGroup({
        id              = "SideBar",
        members         = {self._wtInfoPanel},
        rootWidget      = self,
        bSimClick       = false,
        bShowSelector   = false,
        bStack          = true,
    })
end

function ActivityRadioMainPanel:_DisableGamepadInputs()
    self._navMgr:RemoveAllGroups()
    self._inputMgr:DeactivateAll()
    
    ---@type ActivityMainPanel
    local mainPanel = self._fGetCommonCtrl(EActivityCommCtrl.ActivityMainPanel)
    if not mainPanel then return end
    if IsHD() then
        mainPanel:RemoveInputSummaries()
        mainPanel:UpdateCommonPanelSummaries()
    end
end

-- --- 手柄适配
-- function ActivityRadioMainPanel:_InitGamepadInputs()
--     if not self:IsVisible() then return end

--     loginfo("[heimuu] 111")

--     if not self._navGroup then
--         self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtInfoPanel, self, "Hittest")
--         if self._navGroup then
--             self._navGroup:AddNavWidgetToArray(self._wtInfoPanel)
--             self._navGroup:SetScrollRecipient(self)
--         end
--     end

--     if not self._gamepadSummaries then
--         self._gamepadSummaries = {
--             {actionName = "RadioOnAdjust_X", func = self.OnAdjustBtnClicked, caller = self, bOnlyUI = false},
--         }
--         Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaries, false, false)
--     end
-- end

-- function ActivityRadioMainPanel:_DisableGamepadInputs()
--     loginfo("[heimuu] 222")

--     if self._navGroup then
--         WidgetUtil.RemoveNavigationGroup(self)
--         self._navGroup = nil
--     end
    
--     if self._gamepadSummaries then
--         Module.CommonBar:RecoverBottomBarInputSummaryList()
--         self._gamepadSummaries = nil
--     end
-- end

-----------------------------------------------------UI刷新-----------------------------------------------------
function ActivityRadioMainPanel:RefreshUIAndTimerAgain()
    self:_DelayAndAutoPlay(0)
    self:_OnRefreshScreenPanel(self._curChannel, self._curChannel)
    self:RefreshUI(false)
end

function ActivityRadioMainPanel:RefreshUIAndTimerOnFirst()
    self:_DelayAndAutoPlay()
    self:_OnRefreshScreenPanel(self._curChannel)
    self:RefreshUI()
end

function ActivityRadioMainPanel:RefreshUI(bRefreshScreen)
    if self._activityInfo == nil or next(self._activityInfo) == nil or self._sbcInfo == nil or next(self._sbcInfo) == nil then return end

    bRefreshScreen = setdefault(bRefreshScreen, true)
    if bRefreshScreen then self:_OnRefreshScreenPanel() end

    self:_InitWidgetVisibility()
    self:_InitSkinBtn()
    self:_RefreshSkinBtnReddot()
    self:_OnSetCaptionTxt()
end

function ActivityRadioMainPanel:_InitWidgetVisibility()
    local bShow = not self._currentChildPage
    self._wtAdjustBtn:SetIsEnabled(bShow)
    
    self:_SetColorPanelVisibilityOnly(self._tuningTimes == 0)
end

--- 只显示一种颜色面板，适用于进入界面及刷新
function ActivityRadioMainPanel:_SetColorPanelVisibilityOnly(bGray)
    if bGray then
        self._wtAdjustGrayPanel:HitTestInvisible()
        self._wtAdjustGreenPanel:Collapsed()
        
        self:PlayAnimation(self.WBP_RadioNews_Main_Green_to_Gray_in, 0.2, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self._wtAdjustGreenPanel:HitTestInvisible()
        self._wtAdjustGrayPanel:Collapsed()
        self:PlayAnimation(self.WBP_RadioNews_Main_Gray_to_Green_in, 0.93, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

--- 所有颜色面板均显示，适用于动画开始前
function ActivityRadioMainPanel:_SetColorPanelVisibility(bShow)
    if bShow then
        self._wtAdjustGrayPanel:HitTestInvisible()
        self._wtAdjustGreenPanel:HitTestInvisible()
    else
        self._wtAdjustGrayPanel:Collapsed()
        self._wtAdjustGreenPanel:Collapsed()
    end
end

function ActivityRadioMainPanel:GetAnimTime(animName)
    return self[animName]:GetEndTime() - self[animName]:GetStartTime()
end

function ActivityRadioMainPanel:_InitSkinBtn()
    for index, widget in ipairs(self._wtSkipBtnList) do
        widget:SetJumpData(self._activityID, index, SKIP_BTN_INFO.title[index], SKIP_BTN_INFO.picture[index])
        widget:SetClickCallBack(self.OpenChildMainPanel, self)
    end
end

--- [she1.0] 只有调台获取有红点，有任务可领取或调台次数到达每一个里程碑节点时
function ActivityRadioMainPanel:_RefreshSkinBtnReddot()
    local skinBtn = self._wtSkipBtnList and self._wtSkipBtnList[2]
    if not skinBtn then return end

    local bShowReddot = self:_OnGetTaskReddot() or self:_OnGetAdjustTimesReddot()
    skinBtn:AddReddot(bShowReddot, 2)
end

function ActivityRadioMainPanel:_OnGetTaskReddot()
    local bShowReddot = false
    local taskList = self._taskList

    for _, task in ipairs(taskList) do
        if task.state == ActivityTaskState.ActivityTaskStateCompleted then
            bShowReddot = true
            break
        end
    end

    return bShowReddot
end

function ActivityRadioMainPanel:_OnGetAdjustTimesReddot()
    if self._totalTuningTimes == 0 then return end

    for index, value in ipairs(self._awardTimeList) do
        if self._totalTuningTimes >= value then
            local key = ADJUST_TIMES_KEY .. self._activityID .. "_" .. index
            local isReached = ActivityLogic.GetUserBoolean(key, false)
            if not isReached then
                return true
            end
        end
    end

    return false
end

function ActivityRadioMainPanel:_RefreshAdjustTimesPanel(bShow)
    bShow = setdefault(bShow, true)

    ActivityLogic.SetUIVisibility(self._wtAdjustTimesPanel, bShow)

    if bShow then
        local strTxt = string.format(ActivityConfig.Loc.TuningTimes, tostring(self._tuningTimes))                                            
        self._wtAdjustTimesTxt:SetText(strTxt)
    end
end

-- 暂停所有调台新增动画 [deprecated = true]
function ActivityRadioMainPanel:_StopAllAdjustBtnAnim()
    -- logwarning("[SBC_ADJUST_TEST]  ----- 暂停调台所有动画 -----")
    self:_StopAdjustBtnLoopAnim()
    self:_StopAdjustBtnGrayToGreenAnim()
    self:_StopAdjustBtnGreenToGrayAnim()
end

-- 调台按钮呼吸动画
function ActivityRadioMainPanel:_PlayAdjustBtnLoopAnim()
    if self._tuningTimes == 0 then return end
    self:PlayAnimation(self.WBP_RadioNews_Main_Green_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
    -- loginfo("[SBC_ADJUST_TEST]  播放调台呼吸循环动画")
end

function ActivityRadioMainPanel:_StopAdjustBtnLoopAnim()
    self:StopAnimation(self.WBP_RadioNews_Main_Green_loop)
    self:SkipAnimation(self.WBP_RadioNews_Main_Green_loop)

    -- TODO 以后把按钮状态动画理一下
    if not (self._tuningTimes and self._tuningTimes > 0) then
        self._wtAdjustGreenPanel:Collapsed()
    else
        self._wtAdjustGreenPanel:SelfHitTestInvisible()
    end

    -- logwarning("[SBC_ADJUST_TEST] -- 暂停调台呼吸循环动画 --")
end

-- 调台次数 0 -> 1 动画
function ActivityRadioMainPanel:_PlayAdjustBtnGrayToGreenAnim()
    self:_SetColorPanelVisibility(true)
    self:PlayAnimation(self.WBP_RadioNews_Main_Gray_to_Green_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    -- loginfo("[SBC_ADJUST_TEST]  播放调台变绿动画")
end

function ActivityRadioMainPanel:_StopAdjustBtnGrayToGreenAnim()
    self:StopAnimation(self.WBP_RadioNews_Main_Gray_to_Green_in)
    -- logwarning("[SBC_ADJUST_TEST] -- 暂停调台变绿动画 --")
end

-- 调台次数 1 -> 0 动画
function ActivityRadioMainPanel:_PlayAdjustBtnGreenToGrayAnim()
    self:_SetColorPanelVisibility(true)
    self:PlayAnimation(self.WBP_RadioNews_Main_Green_to_Gray_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    -- loginfo("[SBC_ADJUST_TEST]  播放调台变灰动画")
end

function ActivityRadioMainPanel:_StopAdjustBtnGreenToGrayAnim()
    self:StopAnimation(self.WBP_RadioNews_Main_Green_to_Gray_in)
    -- logwarning("[SBC_ADJUST_TEST] -- 暂停调台变灰动画 --")
end

function ActivityRadioMainPanel:ProcessSpecialLayout()
    if not self._fGetCommonCtrl then return end

    local mainInfoPaddingBox = self._fGetCommonCtrl(EActivityCommCtrl.MainInfoPaddingBox)

    if not mainInfoPaddingBox then return end

    mainInfoPaddingBox:Collapsed()
end

function ActivityRadioMainPanel:RestoreSpecialLayout()
    if not self._fGetCommonCtrl then return end

    local mainInfoPaddingBox = self._fGetCommonCtrl(EActivityCommCtrl.MainInfoPaddingBox)

    if not mainInfoPaddingBox then return end

    mainInfoPaddingBox:SelfHitTestInvisible()
end

--- 刷新调台界面
---@param curChannel number 当前频道
---@param oldChannel number 旧频道
---@param bPlay boolean 播放[default] & 暂停
function ActivityRadioMainPanel:_OnRefreshScreenPanel(curChannel, oldChannel, bPlay)
    bPlay = setdefault(bPlay, true)

    local btnAnim = self.WBP_RadioNews_Main_btn_once

    -- 切换音频映射表 [当前音频] = 音频索引
    local SWITCH_AUDIO_MAP = {
        [self.EChannel.GTI  ] = 2,
        [self.EChannel.Asara] = 1,
        [self.EChannel.Hafk ] = 3,
    }

    local ANIM_STATES = {
        IDLE = { -- 空闲状态
            condition = not curChannel,
            action = function()
                testLog("_OnRefreshScreenPanel [空闲状态], 是否播放进度条动画：" .. tostring(bPlay))

                self:StopAnimation(btnAnim)

                self:_RefreshAdjustTimesPanel(true)
                if bPlay then
                    self._wtRadioScreen:OnPlayProgressLoopAnimation()
                else
                    self:_PlayAudio(4)
                    self:_PlayAudio(17)
                    self:_StopAdjustBtnLoopAnim()
                    self._wtRadioScreen:OnStopProgressLoopAnimation()
                end
            end
        },
        CHANNEL_IN = { -- 频道入场
            condition = curChannel and not oldChannel,
            action = function()
                testLog(string.format("_OnRefreshScreenPanel [频道入场], 当前频道：%s", tostring(curChannel)))

                self:_PlayAudio(5)
                self:_PlayAudio(SWITCH_AUDIO_MAP[curChannel])

                self:StopAnimation(btnAnim)

                self:_RefreshAdjustTimesPanel(true)
                self._wtRadioScreen:OnPlayChannelInAnimation(curChannel)
            end
        },
        CHANNEL_LOOP = { -- 频道循环
            condition = curChannel and oldChannel and curChannel == oldChannel,
            action = function()
                testLog(string.format("_OnRefreshScreenPanel [频道循环], 当前频道：%s", tostring(curChannel)))

                self:StopAnimation(btnAnim)

                self:_RefreshAdjustTimesPanel(true)
                self._wtRadioScreen:OnPlayChannelLoopAnimation(curChannel)
            end
        },
        CHANNEL_SWITCH = { -- 频道切换
            condition = curChannel and oldChannel and curChannel ~= oldChannel,
            action = function()
                testLog(string.format("_OnRefreshScreenPanel [频道切换], 当前频道：%s, 旧频道：%s", tostring(curChannel), tostring(oldChannel)))
                -- 切换动画映射表 [旧频道][当前频道] = 动画索引
                local SWITCH_ANIM_MAP = {
                    [self.EChannel.GTI  ] = { [self.EChannel.Asara] = 1, [self.EChannel.Hafk ] = 2 },
                    [self.EChannel.Asara] = { [self.EChannel.GTI  ] = 3, [self.EChannel.Hafk ] = 4 },
                    [self.EChannel.Hafk ] = { [self.EChannel.GTI  ] = 5, [self.EChannel.Asara] = 6 },
                }

                self:_PlayAudio(SWITCH_AUDIO_MAP[curChannel])

                self:StopAnimation(btnAnim)
                
                local animIndex = SWITCH_ANIM_MAP[oldChannel] and SWITCH_ANIM_MAP[oldChannel][curChannel]
                self._wtRadioScreen:OnPlayChannelSwitchAnimation(animIndex)

                self:_RefreshAdjustTimesPanel(false)
                self:_StopAdjustBtnLoopAnim()
                self:PlayAnimation(btnAnim, 0, 1, EUMGSequencePlayMode.Forward, 1, false)

                self:_CancelBtnAnimTimerHandle()

                local animTime = btnAnim:GetEndTime() - btnAnim:GetStartTime()
                self._btnAnimTimerHandle = Timer.DelayCall(animTime, function()
                    self:_RefreshAdjustTimesPanel(true)
                    if self._tuningTimes == 0 then
                        -- comment：可能不需要这个动画，因为调台已经有旋转动画了
                        self:_PlayAdjustBtnGreenToGrayAnim()
                    else
                        self:_PlayAdjustBtnLoopAnim()
                    end
                end)
            end
        }
    }

    for _, stateConfig in pairs(ANIM_STATES) do
        if stateConfig.condition then
            stateConfig.action()
            break
        end
    end
end

function ActivityRadioMainPanel:ReleaseTimer()
    self:_CancelBtnAnimTimerHandle()
    self:_CancelScreenAnimTimerHandle()
    self:_CancelCaptionAudioTimerHandle()
    self:_CancelButtonDownTimerHandle()
end

function ActivityRadioMainPanel:_CancelBtnAnimTimerHandle()
    if self._btnAnimTimerHandle then
        Timer.CancelDelay(self._btnAnimTimerHandle)
        self._btnAnimTimerHandle = nil
    end
end

function ActivityRadioMainPanel:_CancelScreenAnimTimerHandle()
    if self._screenAnimTimerHandle then
        Timer.CancelDelay(self._screenAnimTimerHandle)
        self._screenAnimTimerHandle = nil
    end
end

function ActivityRadioMainPanel:_CancelCaptionAudioTimerHandle()
    if self._captionAudioTimerHandle then
        Timer.CancelDelay(self._captionAudioTimerHandle)
        self._captionAudioTimerHandle = nil
    end
end

function ActivityRadioMainPanel:_CancelButtonDownTimerHandle()
    if self._buttonDownTimeHandle then
        Timer.CancelDelay(self._buttonDownTimeHandle)
        self._buttonDownTimeHandle = nil
    end
end

---@param newsID number 新闻ID
---@param curContentIdx number 当前内容索引
---@param bIntro boolean 是否为引导词第一句
---@return number|nil nextContentIdx 下一索引，若为nil则表示非法或已播完
---@return number delayTime 应该延迟的播放时间
function ActivityRadioMainPanel:_OnSetCaptionTxt(newsID, curContentIdx, bIntro)
    local radioConfigItem = newsID and self._radioConfig[newsID] or {}
    local contentList = radioConfigItem.content or {}
    local content = curContentIdx and contentList[curContentIdx] or ""
    local channel = radioConfigItem.channel
    local sequence = radioConfigItem.sequence

    -- AI引导音频映射表 [当前频道][当前批次] = 音频索引
    local SWITCH_AUDIO_MAP = {
        [self.EChannel.GTI  ] = { [1] = 11, [2] = 12, [3] = 13 },
        [self.EChannel.Asara] = { [1] =  8, [2] =  9, [3] = 10 },
        [self.EChannel.Hafk ] = { [1] = 14, [2] = 15, [3] = 16 },
    }

    -- 计算同一content的下一索引
    local nextContentIdx = nil
    if curContentIdx and curContentIdx < #contentList then
        nextContentIdx = curContentIdx + 1
    end

    -- 计算常规内容延迟方法
    local delayTime
    local calculateDelayTimeFunc = function(txtStr)
        local strLen = string.len(txtStr)
        delayTime = strLen / 3 * ONE_BYTE_TIME
    end

    -- 计算延迟时间
    local bAppointDelayTime = true
    local delayTimeList = radioConfigItem.subtitleDuration or {}
    delayTime = tonumber(curContentIdx and delayTimeList[curContentIdx])
    if not delayTime then
        calculateDelayTimeFunc(content) 
        bAppointDelayTime = false
    end

    -- 判断是否需要启用音频
    if bIntro and channel and sequence then
        local audioIndex = SWITCH_AUDIO_MAP[channel] and SWITCH_AUDIO_MAP[channel][sequence]
        self:_PlayAudio(17)
        self:_PlayAudio(audioIndex)
    end

    testLog(string.format("_OnSetCaptionTxt, 新闻ID：%s, 当前内容索引：%s, 是否指定延迟时间：%s", tostring(newsID), tostring(curContentIdx), tostring(bAppointDelayTime)))

    self._wtCaptionTxt:SetText(content)
    
    if content ~= "" then
        self._bCaptionAudioPlaying = true
        self:_PlayAudio(7)
        self:_PlayAudio(6)

        self:_CancelCaptionAudioTimerHandle()

        self._captionAudioTimerHandle = Timer.DelayCall(delayTime, function()
            if not self._bCaptionAudioPlaying then
                self:_PlayAudio(7)
                self._bCaptionAudioPlaying = false
            end
        end)
    end

    return nextContentIdx, delayTime 
end

--// 调台定时器
function ActivityRadioMainPanel:_DelayAndAutoPlay(delayTime)
    if self._delayAndAutoPlay then
        Timer.FastRemoveObjTimer(self)
    end
    self._delayAndAutoPlay = true
    delayTime = setdefault(delayTime, AUTO_ANIM_TIME)
    testWarningLog("----- _DelayAndAutoPlay ----- 延迟时间：" .. delayTime)
    Timer.FastAddObjTimer(self, delayTime, 0, CreateCPlusCallBack(
        function()
            self._delayAndAutoPlay = false
            Timer.FastRemoveObjTimer(self)
            self:_OnAutoRadioPlay()
        end, self)
    )
end

function ActivityRadioMainPanel:_CancelDelayAndAutoPlay()
    if self._delayAndAutoPlay then
        testLog("----- _CancelDelayAndAutoPlay true -----")
        Timer.FastRemoveObjTimer(self)
        self._delayAndAutoPlay = false
    end
    testLog("----- _CancelDelayAndAutoPlay false -----")
end



--- 自动播放
function ActivityRadioMainPanel:_OnAutoRadioPlay()
    local newsID = self._newsID
    local isNew = false

    if not newsID or not self._curContentIdx then
        testLog("[自动播放] 未初始化或索引超出当前content的边界值, 正在寻找新的newsID ……")
        newsID, isNew = self:_OnGetNewsID(self._curSequence, self._curChannel, self._newsID, self._bIntro)
    
        if not newsID then
            self:_CancelDelayAndAutoPlay()
            testErrorLog(string.format("[自动播放] 找不到对应的newsID：%s, 当前批次：%s, 当前频道：%s,", tostring(newsID), tostring(self._curSequence), tostring(self._curChannel)))
            return
        else
            self._newsID = newsID
            self._curContentIdx = 1
        end
    end
    
    testLog(string.format("[自动播放] 当前批次：%s, 当前频道：%s, newsID：%s, IsNew：%s", tostring(self._curSequence), tostring(self._curChannel), tostring(newsID), tostring(isNew)))
    
    self:_OnRefreshScreenPanel(self._curChannel, self._curChannel)
    
    local delayTime = 0
    self._curContentIdx, delayTime = self:_OnSetCaptionTxt(newsID, self._curContentIdx, self._bIntro)
    self._bIntro = false
    self:_DelayAndAutoPlay(delayTime)
    
    if isNew then
        self:_OnSendAdjustReq(newsID, true)
    end
end
--// end

--- 子主面板无法盖住主面板的UI，因此由主面板自行控制显隐
---@param bShow boolean 是否显示
function ActivityRadioMainPanel:_OnSwitchWidgetInvisibility(bShow)
    bShow = setdefault(bShow, true)

    self._wtAdjustBtn:SetIsEnabled(bShow)
    self:_RefreshAdjustTimesPanel(bShow)
    ActivityLogic.SetUIVisibility(self._wtInfoPanel, bShow)
    ActivityLogic.SetUIVisibility(self._wtCaptionPanel, true)
    ActivityLogic.SetUIVisible(self._wtCanvasPage, true, bShow and 1 or 666)
end

function ActivityRadioMainPanel:OpenChildMainPanel(index)
    self._childIdx = index

    self:_SendLogTime(1)

    self:ProcessSpecialLayout()
    self:_OnSwitchWidgetInvisibility(false)
    self:_RemoveInputSummary()
    self:_StopAdjustBtnLoopAnim()

    if self._currentChildPage then
        self._currentChildPage:RefreshData(self._activityID, self._activityInfo, self._radioConfig, self._childIdx, self._curChannel)
    else
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.ActivityRadionChildMainPanel, self._wtCanvasPage, nil, self._activityID, self._activityInfo, self._radioConfig, self._childIdx, self._curChannel)
        self._instanceId = instanceId
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            self._currentChildPage = uiIns
            UIUtil.SetWidgetToParent_Full(self._currentChildPage, self._wtCanvasPage)
        end
    end
end

function ActivityRadioMainPanel:_RefreshChildMainPanel()
    if isinvalid(self._currentChildPage) then return end

    self._currentChildPage:RefreshData(self._activityID, self._activityInfo, self._radioConfig, self._childIdx, self._curChannel)
end

function ActivityRadioMainPanel:_OnRemoveSubUI()
    self:_OnSwitchWidgetInvisibility(true)

    if self._currentChildPage then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.ActivityRadionChildMainPanel, self._instanceId)
        self._currentChildPage = nil
        self._childIdx = 0
    end
end

--- 重写返回逻辑
function ActivityRadioMainPanel:_RegisterBottomBar()
    Module.CommonBar:BindPersistentBackHandler(self._ShowModeCard, self)
end

function ActivityRadioMainPanel:_ShowModeCard()
    local detailIns = Module.ItemDetail:GetItemDetailPanelUIIns()
    local bRewardIns = Module.Reward:IsShowingPopUI()
    if detailIns or bRewardIns then
        return
    end

    if self._currentChildPage then
        self:BackToMainPanel()
    else
        ActivityConfig.evtActivityBack:Invoke()
    end
end

function ActivityRadioMainPanel:ResetToIntroContent()
    self._bIntro = true
    self._curContentIdx = 1
end

function ActivityRadioMainPanel:BackToMainPanel()
    self:_InitLogTime()
    self:_OnRemoveSubUI()
    self:RestoreSpecialLayout()
    self:_PlayAdjustBtnLoopAnim()
    self:_AddInputSummary()
end

function ActivityRadioMainPanel:_OnSendAdjustReq(newsID, bAuto)
    bAuto = setdefault(bAuto, true)
    Server.ActivityServer:SendActivitySBCAdjustReq(self._activityID, newsID, bAuto)
end

--- 手动调台
function ActivityRadioMainPanel:OnAdjustBtnClicked()
    -- 注意：当其子UI打开其它PopUI再关闭时，会使X意外绑定到OnAdjust；并且因为是BottomBar触发，无法严格管控其执行过程，需要做强防御编程
    if self._currentChildPage then
        logwarning("[heimuu] OnAdjustBtnClicked error, currentChildPage is not nil")
        return
    end

    -- if not ActivityLogic.CheckClickInterval(1.0) then
    --     testWarningLog("OnAdjustBtnClicked click too quickly, local timestamp = ", Facade.ClockManager:GetLocalTimestamp())
    --     Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.ClickedQuickly)
    --     return
    -- end

    loginfo("[heimuu] OnAdjust!!!")

    if self._tuningTimes <= 0 then
        Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.NoTuningTimes)
        return
    end
    
    self:_SendLogTime()

    self:_PlayAudio(5)

    --// [deprecated]
    -- 需要暂停自动播放的定时器
    -- self:_OnStopTimer()
    --// end

    self:ResetToIntroContent()

    local sequence = self:_OnGetCorrectSequence(self._curSequence)
    local channel = self:_OnGetOtherChannel(self._curChannel)

    testLog("[手动调台] 正在寻找新的newsID ……")
    local newsID, isNew = self:_OnGetNewsID(sequence, channel, self._newsID, self._bIntro)

    if not newsID then
        self:_CancelDelayAndAutoPlay()
        testErrorLog(string.format("[手动调台] 找不到对应的newsID：%s, 当前批次：%s, 当前频道：%s,", tostring(newsID), tostring(sequence), tostring(channel)))
        return
    else
        self._newsID = newsID
    end
    
    testLog(string.format("[手动调台] 批次变更：%s -> %s, 频道变更：%s -> %s, newsID：%s, IsNew：%s", tostring(self._curSequence), tostring(sequence), tostring(self._curChannel), tostring(channel), tostring(newsID), tostring(isNew)))
    
    self:_OnRefreshScreenPanel(channel, self._curChannel)
    
    self:_CancelScreenAnimTimerHandle()
    
    self._wtAdjustBtn:SetIsEnabled(false)
    self._screenAnimTimerHandle = Timer.DelayCall(0.8, function()
        self:_OnRefreshScreenPanel(channel, channel)
        self._wtAdjustBtn:SetIsEnabled(true)
    end)
    
    local delayTime = 0
    self._curContentIdx, delayTime = self:_OnSetCaptionTxt(newsID, 1, self._bIntro)
    self._bIntro = false
    self:_DelayAndAutoPlay(delayTime)

    -- 更新本地部分数据
    self._curSequence = sequence
    self._curChannel = channel
    self._tuningTimes = self._tuningTimes - 1 -- 调台次数提前刷新，防止连点
    
    -- 调台次数改变，涉及预选奖励和解锁槽位，必须要发送调台请求
    self:_OnSendAdjustReq(newsID, false)
end

function ActivityRadioMainPanel:_OnShowPopWindow()
    local showType = POPUP_TYPE.NONE
    local bActInsView = self:_GetActInsViewStatus()

    if not bActInsView then
        showType = POPUP_TYPE.HOT_POPWINDOW
        Facade.UIManager:AsyncShowUI(
            UIName2ID.ActivityRadioHotPopWindow,
            nil,
            nil,
            self._activityID,
            self._activityInfo
        )
    end

    return showType
end

-- 播放音效
function ActivityRadioMainPanel:_PlayAudio(index, isBool)
    testLog("[播放音效] ", index, tostring(ActivityRadioMainPanel.AUDIO_NAME[index]))
    
    if index == nil or ActivityRadioMainPanel.AUDIO_NAME == nil or ActivityRadioMainPanel.AUDIO_NAME[index] == nil then return end
    
    isBool = setdefault(isBool, true)
    
    if isBool then
        Facade.SoundManager:PlayUIAudioEvent(ActivityRadioMainPanel.AUDIO_NAME[index])
    else
        Facade.SoundManager:StopUIAudioEvent(ActivityRadioMainPanel.AUDIO_NAME[index])
    end
end

-- TODO: 若出现音频互斥，则考虑将 _PlayAudio 替换成该函数
function ActivityRadioMainPanel:_PlayAudioOnly(index, isBool)
    if isBool then
        self:_PlayAudio(self._curAudioIdx, false)
        self._curAudioIdx = index
    else
        self._curAudioIdx = 0
    end

    self:_PlayAudio(index, isBool)
end

return ActivityRadioMainPanel