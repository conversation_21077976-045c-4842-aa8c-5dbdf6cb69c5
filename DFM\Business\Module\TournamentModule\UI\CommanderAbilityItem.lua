----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRanking)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CommanderAbilityItem : LuaUIBaseView
local CommanderAbilityItem = ui("CommanderAbilityItem")
local ETipsTriggerReason = import("ETipsTriggerReason")

function CommanderAbilityItem:Ctor()
    self._wtAbilityIcon=self:Wnd("DFImage_Icon",UIImage)
    self._wtAbilityText=self:Wnd("DFTextBlock_50",UITextBlock)
    self._wtTipsAnchor=UIUtil.WndTipsAnchor(self, "DFTipsAnchor_114", self.OnShowTips, self.OnHideTips)

end

function CommanderAbilityItem:OnClose()
    self:HideTips()
end

function CommanderAbilityItem:SetAbilityInfo(info)
    self._info=info
    self._wtAbilityText:SetText(info.desc)
    self._wtAbilityIcon:AsyncSetImagePath(info.icon)
end

function CommanderAbilityItem:OnShowTips(triggerReason)
    if triggerReason==ETipsTriggerReason.Hover then
        self:ShowTips()
    elseif triggerReason==ETipsTriggerReason.Click then
        self:ShowTips()
    end
end

function CommanderAbilityItem:OnHideTips(triggerReason)
    if triggerReason==ETipsTriggerReason.Hover then
        self:HideTips()
    elseif triggerReason==ETipsTriggerReason.Click then
        self:HideTips()
    elseif triggerReason==ETipsTriggerReason.ClickBlankSpace then
        self:HideTips()
    elseif triggerReason==ETipsTriggerReason.Close then
        self:HideTips()
    end
    
end

function CommanderAbilityItem:ShowTips()
    self:HideTips()
    local desc=self._info and self._info.tipDesc or "?"
    self._tipsHandle=Module.CommonTips:ShowAssembledTips({{id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = desc}}},self._wtTipsAnchor)
end

function CommanderAbilityItem:HideTips()
    if self._tipsHandle then
        Module.CommonTips:RemoveAssembledTips(self._tipsHandle)
        self._tipsHandle=nil
    end
end

return CommanderAbilityItem    
