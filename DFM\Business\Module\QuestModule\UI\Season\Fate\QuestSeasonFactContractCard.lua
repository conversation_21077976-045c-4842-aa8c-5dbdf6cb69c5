----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestSeasonFactContractCard : LuaUIBaseView
local QuestSeasonFactContractCard = ui("QuestSeasonFactContractCard")
local TournamentConfig = Module.Tournament.Config

function QuestSeasonFactContractCard:Ctor()
    loginfo("QuestSeasonFactContractCard:Ctor")
    self._wtStageTB = self:Wnd("DFTextBlock_52", UITextBlock)
    self._wtName = self:Wnd("DFTextBlock", UITextBlock)
    self._wtImage = self:Wnd("DFImage_0", UIImage)
    self._wtBtn = self:Wnd("DFBtn", DFCommonButtonOnly)
    self._wtBtn:Event("OnClicked", self._OnQuestBtnClicked, self)
    self._wtBtn:Collapsed()
    self._timerHandle = nil
    self.fateInfo = nil
    self.factId = 0
    self.questId = 0
    self.questInfo = nil
end

function QuestSeasonFactContractCard:OnInitExtraData()
    loginfo("QuestSeasonFactContractCard:OnInitExtraData")
end

function QuestSeasonFactContractCard:OnOpen()
    loginfo("QuestSeasonFactContractCard:OnOpen")
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
end

function QuestSeasonFactContractCard:OnClose()
    loginfo("QuestSeasonFactContractCard:OnClose")
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    self:RemoveAllLuaEvent()
    if self._wtRedDot then
        self._wtRedDot:DestroyReddotWidget()
        self._wtRedDot = nil
    end
end

function QuestSeasonFactContractCard:_OnQuestBtnClicked()
    Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonFactContractDetail, nil, nil, self.factId, self.questInfo)
end

function QuestSeasonFactContractCard:_OnQuestStateUpdate(questId)
    if self.questId and self.questId == questId then
        self:RefreshView()
        self:CheckRedPoint()
    end
end

function QuestSeasonFactContractCard:SetInfo(fateQuestInfo)
    loginfo("QuestSeasonFactContractCard:SetInfo")
    self.fateInfo = fateQuestInfo
    self.factId = self.fateInfo.FateQuestId
    self.questId = self.fateInfo.Quest
    self.questInfo = Server.QuestServer:GetQuestInfoById(self.questId)
    self.unlockTime = self.fateInfo.FateQuestUnlockTime
    self.img = self.fateInfo.FateQuestImg

    if not string.isempty(self.img) then
        self._wtImage:AsyncSetImagePath(self.img)
    end
    if self.questInfo then
        self:RefreshView()
        self:CheckRedPoint()
    else
        logerror("no has questInfo  questid = ", self.questId)
    end
end

function QuestSeasonFactContractCard:CheckRedPoint()
    if self._wtRedPoint == nil then
        return
    end

    if self._wtRedDot == nil then
        self._wtRedDot = Module.ReddotTrie:CreateReddotIns(self._wtBtn)
    end

    local bShowRed = self.questInfo.state == QuestState.Completed or not Server.QuestServer._questFactData:IsHasFactUnderWay()
    self._wtRedDot:SetReddotVisible(bShowRed, EReddotType.Normal)
end

function QuestSeasonFactContractCard:RefreshView()
    self._wtName:SetText(self.questInfo.name)

    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end

    if self.questInfo.state == QuestState.Rewarded then
        self:SetType(2)
        self._wtStageTB:SetText(Module.Quest.Config.Loc.objectiveFinish)
        self:PlayAnimation(self.WBP_SeasonalTasks_ContractCard_loop_type2, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
    else
        if self.questInfo.state < QuestState.Accepted or self.questInfo.state == QuestState.Unaccepted then
            if Server.QuestServer._questFactData:GetRemainToAcceptTime(self.factId) < 0 then
                self:SetType(0)
            else
                self:SetType(3)
                self:_UpdateSeasonCountTime()
                self._timerHandle = Timer:NewIns(30, 0)
                self._timerHandle:AddListener(self._UpdateSeasonCountTime, self)
                self._timerHandle:Start()

                self:PlayAnimation(self.WBP_SeasonalTasks_ContractCard_loop_type3, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
            end
        else
            self:SetType(1)
            self._wtStageTB:SetText(Module.Quest.Config.Loc.underWay)
        end
    end
end

function QuestSeasonFactContractCard:_UpdateSeasonCountTime()
    local desc = ""
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(Server.QuestServer._questFactData:GetRemainToAcceptTime(self.factId))
    if day > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif hour > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif min > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min) ..
            Module.Quest.Config.Loc.CanAccept
    else
        desc = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1) .. Module.Quest.Config.Loc.CanAccept
        if sec <= 0 then
            if self._timerHandle then
                self._timerHandle:Release()
                self._timerHandle = nil
            end
            self:SetType(0)
        end
    end
    self._wtStageTB:SetText(desc)
end

return QuestSeasonFactContractCard
