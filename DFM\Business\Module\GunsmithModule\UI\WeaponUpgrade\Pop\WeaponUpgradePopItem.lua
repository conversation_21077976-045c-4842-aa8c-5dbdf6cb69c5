----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class WeaponUpgradePopItem : LuaUIBaseView
--武器升级界面
local WeaponUpgradePopItem = ui("WeaponUpgradePopItem")
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local Config = Module.Gunsmith.Config

function WeaponUpgradePopItem:Ctor()
    self._wtItemView = self:Wnd("wItemView",      IVCommonItemTemplate)
    self._wtDFAddNum = self:Wnd("DFTextBlock_77", UITextBlock)
    self._wtDFAdLine = self:Wnd("DFImage_45",     UIImage)
    self._wtDecPrice = self:Wnd("wtDFCommonAddDecSliderV1", DFCommonAddDecSlider)
    self._wtDecPrice:Event("OnAddDecSliderCurNumChanged", self._OnAddCurNumChanged, self)
end

function WeaponUpgradePopItem:_OnAddCurNumChanged(curNum, changeNum)
    --是否满级处理
    if self._curNum == curNum then
        return
    end 
    local data = self._data
    if data and data.isBanAdd then
        if curNum and self._curNum then
            if curNum >= self._curNum then
                self._wtDecPrice:InitNum(self._curNum or 0, 1, 0, data.num or 0)
                Module.Gunsmith.Config.Events.evtWeaponNotAddExped:Invoke()
                return
            end
        end
    end
    self._curNum = curNum
    ------>刷新
    self:_SetUsedNum(curNum)
    Module.Gunsmith.Config.Events.evtWeaponUpgradePop:Invoke(self._index, curNum)
end

function WeaponUpgradePopItem:InitData(index, data, count)
    self._data = data
    self._index = index
    if data and data.num then
        self._curNum = data.cardNum
        self._wtDecPrice:InitNum(data.cardNum or 0, 1, 0, data.num or 0)
        self._wtItemView:InitItem(ItemBase:New(data.id))
        local compId, slotPos, uiNavId, addWidget
        compId  = EComp.BottomRightIconText
        slotPos = EIVSlotPos.BottomRight
        uiNavId = UIName2ID.IVTextIconComponent
        addWidget = self._wtItemView:FindOrAdd(compId, uiNavId, slotPos, EIVCompOrder.Order1)
        if addWidget then
            addWidget:ShowTextOnly(data.num or 0)
            self._wtItemView:EnableComponent(compId, data.num ~= 0)
        end
        compId  = EComp.ExpireMask
        slotPos = EIVSlotPos.MaskLayer
        uiNavId = UIName2ID.IVGreyMask
        addWidget = self._wtItemView:FindOrAdd(compId, uiNavId, slotPos, EIVCompOrder.Order1)
        if addWidget then
            self._wtItemView:EnableComponent(compId, data.num == 0)
        end
        if data.num ~= 0 then
            self._wtDFAddNum:SelfHitTestInvisible()
        else
            self._wtDFAddNum:Collapsed()
        end
        if index ~= count then
            self._wtDFAdLine:SelfHitTestInvisible()
        else
            self._wtDFAdLine:Collapsed()
        end
        self:_SetUsedNum(data.cardNum)
    end
end

function WeaponUpgradePopItem:_SetUsedNum(num)
    self._wtDFAddNum:SetText(string.format(Config.Loc.GunsmithUse, num or ""))
end

function WeaponUpgradePopItem:OnShowBegin()
end

function WeaponUpgradePopItem:OnHideBegin()
end

function WeaponUpgradePopItem:OnClose()
end

return WeaponUpgradePopItem