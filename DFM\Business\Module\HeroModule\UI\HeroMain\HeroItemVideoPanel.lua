----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class HeroItemVideoPanel : LuaUIBaseWindow
local HeroItemVideoPanel = ui("HeroItemVideoPanel", require("DFM.YxFramework.Managers.UI.LuaUIBaseWindow"))
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local HeroConfig      = require "DFM.Business.Module.HeroModule.HeroConfig"
local MediaResTable = Facade.TableManager:GetTable("MediaResTable")--视频资源表
local ItemHelperTool  = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CommonVideoComponent = require "DFM.Business.Module.CommonWidgetModule.UI.CommonVideoView.CommonVideoComponent"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EUINavigation 			   = import "EUINavigation"
--- END MODIFICATION


function HeroItemVideoPanel:Ctor()
    self._wtSkinEffectGridBox = UIUtil.WndScrollGridBox(self, "wtSkinEffectGridBox", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtVideoPlayer = self:Wnd("wtVideoPlayer", CommonVideoComponent)
    self._wtEffectNameTxt = self:Wnd("wtEffectNameTxt", UITextBlock)
    self._wtEffectDesTxt = self:Wnd("wtEffectDesTxt", UITextBlock)
    self._wtVideoPlayer:InitComponent(false)
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    Module.CommonBar:RegStackUITopBarTitle(self, HeroConfig.Loc.HeroItemPreview)
    self._selectedPos = -1
    self._selectedCell = nil
    self._itemList = {}
    self._isMystical = false
    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self._isPlay = true
    end
    -- END MODIFICATION
end


function HeroItemVideoPanel:OnInitExtraData(heroItem, ItemList, isMystical)
    if isMystical == nil then
        self._isMystical = false
    else
        self._isMystical = isMystical
    end
    
    if ItemList == nil then
        ItemList = setdefault(ItemList, {})
    end
    self._item = heroItem
    self._itemList = ItemList

    local list = {}
    for index, value in ipairs(ItemList) do
        local itemType = ItemHelperTool.GetSubTypeById(tonumber(value))
        if itemType == 11 then
            list[index] = value
        end
    end
    self._itemList = list

    for index, value in ipairs(self._itemList) do
        if self._item == value then
            self._selectedPos = index
            return
        end
    end

    local isMystical, MysticalItemVideoNames = self:IsMysticalFunc(heroItem)
    --判断是否为玄学
    if isMystical then
        self._itemList = MysticalItemVideoNames
        self._selectedPos = 1
    end
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function HeroItemVideoPanel:OnOpen()
    self:_AddListeners()
    self:_OnRefreshItemDetail()
end


-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function HeroItemVideoPanel:OnClose()
    self:RemoveAllLuaEvent()
    self._wtVideoPlayer:Stop()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function HeroItemVideoPanel:OnShow()

end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function HeroItemVideoPanel:OnHide()
    self._wtVideoPlayer:Stop()
end


function HeroItemVideoPanel:OnShowBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtSkinEffectGridBox, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:SetScrollRecipient(self._wtSkinEffectGridBox)
                self._wtNavGroup:AddNavWidgetToArray(self._wtSkinEffectGridBox)
                self._wtNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
                WidgetUtil.WrapNavBoundary(self._wtNavGroup, {EUINavigation.Up, EUINavigation.Down})
                self:_InitShortcuts()
            end
        end
    end
    -- END MODIFICATION
end


function HeroItemVideoPanel:OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self:_RemoveShortcuts()
        if self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end
    end
    -- END MODIFICATION
end


-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function HeroItemVideoPanel:OnAnimFinished(anim)
end



function HeroItemVideoPanel:_AddListeners()
end

function HeroItemVideoPanel:IsMysticalFunc(curId)
    local Info = HeroHelperTool.GetHeroItemData(curId)
    local isMystical = false
    if Info then
        isMystical = #Info.MysticalHeroItemVideoNames > 0 and self._isMystical
    end
    return isMystical, Info and Info.MysticalHeroItemVideoNames or {}
end


function HeroItemVideoPanel:_OnRefreshItemDetail()
    self._wtEffectNameTxt:SetText(HeroConfig.Loc.HeroItemPreview)
    self._wtEffectDesTxt:SetText(HeroConfig.Loc.HeroItemPreview)
    self._wtVideoPlayer:Stop()
    self._wtSkinEffectGridBox:RefreshAllItems()
end

function HeroItemVideoPanel:_OnGetItemsCount()
    if self._itemList then
        return #self._itemList
    else
        return 0
    end
end

function HeroItemVideoPanel:GetItemVideoInfo(curId)
    local MediaResData = MediaResTable[curId]
    local videoThumbnail = MediaResData and FLuaHelper.SoftObjectPtrToString(MediaResData.MediaResList[1].MediaTexRes) or ""
    local titleName = MediaResData and MediaResData.MediaTitle or ""
    local descName = MediaResData and MediaResData.MediaDesc or ""
    return videoThumbnail, titleName, descName
end

function HeroItemVideoPanel:_OnProcessItemWidget(position, itemWidget)
    local index = position + 1
    local videoThumbnail, effectName, descName = self:GetItemVideoInfo(self._itemList[index])
    local fClickCb = CreateCallBack(self._OnSkinEffectItemClick, self,itemWidget, index)
    itemWidget:InitData({buttonIndex = index, fOnSkillButtonClicked = fClickCb})
    itemWidget:RefreshButtonInfo(videoThumbnail or "", effectName or "")
    if self._selectedPos == -1 or self._selectedPos == index then
        self:_OnSkinEffectItemClick(itemWidget, index)
    else
        itemWidget:_OnSelected(false)
    end
end

function HeroItemVideoPanel:_OnSkinEffectItemClick(itemCell, position)
    local index = position + 1
    if isvalid(self._selectedCell) then
        if self._selectedPos ~= position then
            self._selectedCell:_OnSelected(false)
        end
    end
    self._selectedPos = position
    self._selectedCell = itemCell
    self._selectedCell:_OnSelected(true)
    self._wtVideoPlayer:Stop()
    local videoThumbnail, effectName, descName = self:GetItemVideoInfo(self._itemList[position])
    self._wtEffectNameTxt:SetText(effectName or HeroConfig.Loc.HeroItemPreview)
    self._wtEffectDesTxt:SetText(descName or HeroConfig.Loc.HeroItemPreview)
    if self:IsMysticalFunc(self._item) then
        self._wtVideoPlayer:Play(self._itemList[position])
    else
        local skinInfo = HeroHelperTool.GetHeroItemData(self._itemList[position])
        if skinInfo then
            self._wtVideoPlayer:Play(skinInfo.HeroItemVideoName)
        end
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
function HeroItemVideoPanel:_InitShortcuts()
    if not IsHD() then
        return
    end
    local summaryList = {}
    table.insert(summaryList, {actionName = "HeroVideo_Play_Gamepad",func = self._PlayOrPauseVideo, caller = self ,bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
    self._navConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

    CollectionLogic.RegStackUIInputSummary({
        {actionName = "Collection_PlayOrPause_Gamepad", func = self._PlayOrPauseVideo, caller = self, bUIOnly = false, bHideIcon = false},
    }, false)
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function HeroItemVideoPanel:_RemoveShortcuts()
    if not IsHD() then
        return
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
    if self._navConfigHandler then
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self._navConfigHandler = nil
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function HeroItemVideoPanel:_PlayOrPauseVideo()
    if not IsHD() then
        return
    end
    self._wtVideoPlayer:PlayOrPauseVideo()
    -- if self._isPlay then
    --     self._wtVideoPlayer:Stop()
    --     self._isPlay = false
    -- else
    --     if self:IsMysticalFunc(self._item) then
    --         self._wtVideoPlayer:Play(self._itemList[self._selectedPos])
    --     else
    --         local skinInfo = HeroHelperTool.GetHeroItemData(self._itemList[self._selectedPos])
    --         if skinInfo then
    --             self._wtVideoPlayer:Play(skinInfo.HeroItemVideoName)
    --         end
    --     end
    --     self._isPlay = true
    -- end
end

function HeroItemVideoPanel:_OnAdjustProgress()
end
-- END MODIFICATION

return HeroItemVideoPanel
