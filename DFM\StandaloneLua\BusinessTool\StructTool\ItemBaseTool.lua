local ItemBaseTool = {}
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local UFMysticalSkinColorMaterialManager = import "FMysticalSkinColorMaterialManager"

local ASOLPlayerQuestSystem = import "SOLPlayerQuestSystem"
local FItemSpaceRepData = import "ItemSpaceRepData"
local UInventoryUtil = import "InventoryUtil"

ItemBaseTool.ItemSpeicalTypeMap = nil
ItemBaseTool.EItemSpeicalType = {
    NightVision = 1,            -- 属于夜视仪
    ThermalImagine = 2,         -- 属于热成像
    Flashlight = 3,             --属于手电
}
---------------------------------------------------------------------------------
--- 此处放置Server、Module都可能用得到的道具基础方法
---------------------------------------------------------------------------------

function ItemBaseTool.BuildPropInfoByItem(item, bUseNewGid)
    bUseNewGid = setdefault(bUseNewGid, false)
    ---@type pb_PropInfo
    local propInfo = {}
    propInfo.id = item.id
    if bUseNewGid then
        propInfo.gid = GetGid()
    else
        propInfo.gid = item.instanceId
    end
    local curDurability, maxDurability = 0, 0
    local itemFeature = item:GetFeature()
    if itemFeature and itemFeature.GetDurabilityValue then
        curDurability, maxDurability = itemFeature:GetDurabilityValue()
    end
    propInfo.num = item.num
    propInfo.position = item.InSlot and item.InSlot.SlotType
    propInfo.source = 0
    propInfo.reason = 0
    propInfo.length = item.length
    propInfo.width = item.width
    propInfo.health = curDurability
    propInfo.health_max = maxDurability

    propInfo.aging_type = item.aginType
    propInfo.aging_begin_time = item.aginBeginTime
    propInfo.aging_duration = item.aginDuration
    propInfo.use_cnt = item.useCnt

    propInfo.components = clone(item.components) or {}
    propInfo.loc = {
        space_id = 1,
        x = 1,
        y = 1,
        start_x = 0,
        start_y = 0,
        rotate = false,
        pos = item.InSlot and item.InSlot.SlotType
    }

    return propInfo
end

function ItemBaseTool.SetQualityColor(imageWidget, item)
    ItemConfigTool.SetQualityColorByQuality(imageWidget, item.quality)
end

--- 比较两个道具是否相同（仅比较Id和配件Id）
---@param aItem ItemBase
---@param bItem ItemBase
function ItemBaseTool.CompareItem(aItem, bItem)
    if (aItem and bItem == nil) or (bItem and aItem == nil) then
        return false
    elseif (aItem == nil and bItem == nil) then
        return true
    else
        if aItem:IsAssemblyItem() and bItem:IsAssemblyItem() then
            local bIsSame = aItem.id == bItem.id
            if not bIsSame then
                return false
            else
                return ItemBaseTool.CompareComponent(aItem.components, bItem.components)
            end
        else
            return aItem.id == bItem.id
        end
    end
end

--- 检查两颗配件树是否相同
---@param aComponent pb_Component[]
---@param bComponent pb_Component[]
function ItemBaseTool.CompareComponent(aComponent, bComponent)
    local mapPos2Id = {}
    if aComponent then
        for _, component in ipairs(aComponent) do
            mapPos2Id[component.slot] = component.prop_data.id
        end
    end

    if bComponent then
        for _, component in ipairs(bComponent) do
            local compareId = mapPos2Id[component.slot]
            if not compareId then
                -- A没有B的配件
                return false
            elseif compareId ~= component.prop_data.id then
                -- A和B在某个位置装的配件不同
                return false
            else
                mapPos2Id[component.slot] = nil
            end
        end
    end

    if table.isempty(mapPos2Id) then
        -- 完全匹配
        return true
    else
        -- B没有A的配件
        return false
    end
end

--- 检查两个道具是否能绑定（这里的实现需要与【UInventoryUtil::CheckTwoItemsCanCombine】保持一致）
---@param srcItem ItemBase
---@param tarItem ItemBase
function ItemBaseTool.CheckItemCanCombine(srcItem, tarItem)
    if srcItem.id ~= tarItem.id then return false end
    if srcItem.maxStackCount == 1 or tarItem.maxStackCount == 1 then return false end
    if tarItem.num == tarItem.maxStackCount then return false end

    local bToBulletSpace = tarItem.InSlot:IsBulletSlot()
    -- 子弹槽不关心具体的绑定类型
    if bToBulletSpace then return true end

    local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
    if ItemOperaTool.CheckRunWarehouseLogic() then
        -- 绑定类型不同，不能合并
        if srcItem.bindType ~= tarItem.bindType then return false end

        -- 任一为真-无绑均可以合并
        if srcItem.bindPlayerId == 0 or tarItem.bindPlayerId == 0 then return true end
        -- 绑定类型相同，需要检查绑定的PlayerId是否相同（子弹槽除外，有特殊规则）
        if srcItem.bindPlayerId ~= tarItem.bindPlayerId then return false end
    else
        -- 两个道具都需要已搜索
        if srcItem:NeedSearchByLocalPlayer() or tarItem:NeedSearchByLocalPlayer() then
            return false
        end
        -- 非子弹物品绑定类型不同，不能合并
        if not srcItem:IsBullet() and srcItem.bindType ~= tarItem.bindType then
            return false
        end
        local newBindPlayerId = 0
        local character = Facade.GameFlowManager:GetCharacter()
        return UInventoryUtil.CheckTwoAmmoCanCombine(srcItem.bindPlayerId, tarItem.bindPlayerId, character, newBindPlayerId)
    end
    
    return true
end

---@param perkInfo WeaponPerkFeature
function ItemBaseTool.BuildItemFromPerkInfo(perkInfo)
    local perkId = perkInfo:GetItemId()
    local perkGid = perkInfo:GetGid()

    local newPerkItem = ItemBase:NewIns(perkId, 1, perkGid)
    -- newPerkItem._feature = perkInfo
    
    -- if perk
    -- newPerkItem:

    return newPerkItem
end

---@param weaponItem ItemBase
function ItemBaseTool.BuildPerkItemFromWeapon(weaponItem)
    ---@type WeaponFeature
    local weaponFeature = weaponItem:GetFeature(EFeatureType.Weapon)
    if weaponFeature then
        local perkInfo = weaponFeature:GetPerkInfo()
        if perkInfo then
            local perkItem = ItemBaseTool.BuildItemFromPerkInfo(perkInfo)
            local perkFeature = perkItem:GetFeature(EFeatureType.WeaponPerk)
            if perkFeature then
                perkFeature:BindWeapon(weaponItem.gid)
            end

            return perkItem
        end
    end

    return nil
end

---@param item ItemBase
function ItemBaseTool.IsExtendItem(item)
    local isExtendItem = false
    local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
    if equipmentFeature then
        isExtendItem = equipmentFeature:IsExtendItem()
    end
    return isExtendItem
end

function ItemBaseTool.IsContractItem(item)
    if not item then
        return false
    end

    local questSystem = ASOLPlayerQuestSystem.GetInstance(GetWorld())
    local playerQuestComp = questSystem:GetPlayerQuestComponent()

    if playerQuestComp then
        return playerQuestComp:CheckIsContractPropID(item.id)
        -- return true
    else
        return false
    end
end

---@param item ItemBase
function ItemBaseTool.GetBulletDefaultEquipSlot(item)
    if not item:IsBullet() then
        return nil
    end

    local defaultEquipSlot, canStackNum
    -- 可堆叠槽位 > 可用的空槽位 > 背包
    local leftBulletSlot = Server.InventoryServer:GetSlot(ESlotType.BulletLeft)
    local rightBulletSlot = Server.InventoryServer:GetSlot(ESlotType.BulletRight)
    local lEquipItem = leftBulletSlot:GetEquipItem()
    local rEquipItem = rightBulletSlot:GetEquipItem()
    local bLeftFit = leftBulletSlot:CheckItemFitSlot(item)
    local bRightFit = rightBulletSlot:CheckItemFitSlot(item)
    bLeftFit = bLeftFit and (not lEquipItem or ItemBaseTool.CheckItemCanCombine(item, lEquipItem))
    bRightFit = bRightFit and (not rEquipItem or ItemBaseTool.CheckItemCanCombine(item, rEquipItem))
    if bLeftFit and not lEquipItem then
        defaultEquipSlot = leftBulletSlot
        canStackNum = item.num
    elseif bLeftFit then
        defaultEquipSlot = leftBulletSlot
        canStackNum = lEquipItem.maxStackCount - lEquipItem.num
    elseif bRightFit and not rEquipItem then
        defaultEquipSlot = rightBulletSlot
        canStackNum = item.num
    elseif bRightFit then
        defaultEquipSlot = rightBulletSlot
        canStackNum = rEquipItem.maxStackCount - rEquipItem.num
    end

    return defaultEquipSlot, canStackNum
end

function ItemBaseTool.GetBulletEquipSlotAndMoveNum(item)
    if not item and not item:IsBullet() then
        return nil
    end
    local targetSlot = Server.InventoryServer:GetItemAvaiableCarrySlot(item)
    local moveParams = {}
    local moveNum
    if targetSlot then
        moveNum = not targetSlot:TryFindLocationForItem(item) and targetSlot:GetStackCount(item) or nil
        moveParams.bAutoMerge = true
        moveParams.allow_partly = true
    end

    return targetSlot, moveNum, moveParams
end

local mapEquipmentId2SpaceData = {}
local pocketSpaceData = nil
local bSpaceDataInit = false

function ItemBaseTool.PrepareSpaceDatas()
    local targetTableNames = {"ChestHangingFunc", "BagFunc", "SafeBoxFunc"}
    for _, name in ipairs(targetTableNames) do
        local table = Facade.TableManager:GetTable(name)
        for k, v in pairs(table) do
            local id = tonumber(k)
            local spaceData = FItemSpaceRepData()
            local bSuccess = false
            bSuccess, spaceData = UInventoryUtil.GenerateSpaceDataByEquipmentId(id, spaceData)
            assert(bSuccess)
            mapEquipmentId2SpaceData[id] = spaceData
        end
    end

    pocketSpaceData = FItemSpaceRepData()
    local bSuccess = false
    bSuccess, pocketSpaceData = UInventoryUtil.GenerateSpaceDataForPocket(pocketSpaceData)
    assert(bSuccess)

    bSpaceDataInit = true
end

function ItemBaseTool.GetSpaceDataByEquipmentId(id)
    if not bSpaceDataInit then
        ItemBaseTool.PrepareSpaceDatas()
    end

    return mapEquipmentId2SpaceData[id]
end

function ItemBaseTool.GetCapacityByEquipmentId(id)
    local spaceData = ItemBaseTool.GetSpaceDataByEquipmentId(id)
    if spaceData then
        return spaceData.TotalCapacity
    end

    return 0
end

function ItemBaseTool.GetPocketSpaceData()
    if not bSpaceDataInit then
        ItemBaseTool.PrepareSpaceDatas()
    end

    return pocketSpaceData
end

function ItemBaseTool.GetItemExpiredState(item)
    -- 每个道具都有时间戳(目前只用于vip扩容箱的判断，其他道具慎用)
    -- 1. 0表示未激活
    -- 2. TimeUtil.GetServerRemainTime2Seconds(item.expireTimesTamp)表示剩余时间
    if item then
        return item.expireTimesTamp == 0 or TimeUtil.GetServerRemainTime2Seconds(item.expireTimesTamp) == 0
    end
end

---comment 检测item是否支持夜战（夜视仪/手电/热成像）功能
---@param item ItemBase
---@param speicalTypes EItemSpeicalType[]
---@return boolean 是否支持夜战
function ItemBaseTool.CheckSupportNightBattleBySpeicalTypes(item, speicalTypes)
    if not item then
        logerror("ItemBaseTool.CheckSupportNightBattleBySpeicalType item is nil!!!")
        return false
    end
    if not speicalTypes or table.isempty(speicalTypes) then
        logerror("ItemBaseTool.CheckSupportNightBattleBySpeicalType speicalTypes is nil!!!")
        return false
    end
    if not ItemBaseTool.ItemSpeicalTypeMap then
        ItemBaseTool.InitItemSpeicalTypeMap()
    end

    if ItemBaseTool.ItemSpeicalTypeMap then
        local id = item.id
        local itemMainType = ItemHelperTool.GetMainTypeById(id)
        if itemMainType == EItemType.Receiver then
            local propInfo = item:GetRawPropInfo()
            if propInfo then
                local adapterIds = WeaponAssemblyTool.GetItemIDsByPropInfo(propInfo, false, false, true)
                if not table.isempty(adapterIds) then
                    for _, adapterId in ipairs(adapterIds) do
                        for _, speicalType in ipairs(speicalTypes) do
                            if ItemBaseTool.ItemSpeicalTypeMap[speicalType][adapterId] then
                                return true
                            end
                        end
                    end
                end
            end
        else
            for _, speicalType in ipairs(speicalTypes) do
                if ItemBaseTool.ItemSpeicalTypeMap[speicalType][id] then
                    return true
                end
            end
        end

    else
        logerror("ItemBaseTool.CheckSupportNightBattleBySpeicalType ItemSpeicalTypeMap or ItemSpeicalTypeMap[speicalType] is nil!!!", speicalType)
        return false
    end

    return false
end

---comment 清空ItemSpeicalTypeMap
function ItemBaseTool.ClearItemSpeicalTypeMap()
    ItemBaseTool.ItemSpeicalTypeMap = nil
end

---comment 初始化ItemSpeicalTypeMap
function ItemBaseTool.InitItemSpeicalTypeMap()
    logwarning("ItemBaseTool.InitItemSpeicalTypeMap 初始化ItemSpeicalTypeMap")
    ItemBaseTool.ItemSpeicalTypeMap = {}
    declare_if_nil(ItemBaseTool.ItemSpeicalTypeMap, ItemBaseTool.EItemSpeicalType.NightVision, {})
    declare_if_nil(ItemBaseTool.ItemSpeicalTypeMap, ItemBaseTool.EItemSpeicalType.ThermalImagine, {})
    declare_if_nil(ItemBaseTool.ItemSpeicalTypeMap, ItemBaseTool.EItemSpeicalType.Flashlight, {})
    local itemSpeicalTypeTable = Facade.TableManager:GetTable("ItemSpeicalTypeTable")
    if itemSpeicalTypeTable then
        for _, data in pairs(itemSpeicalTypeTable) do
            local id = data.ItemID
            local bNightVision = data.NightVision 
            local bThermalImagine = data.ThermalImagine 
            local bFlashlight = data.Flashlight
            if bNightVision then
                ItemBaseTool.ItemSpeicalTypeMap[ItemBaseTool.EItemSpeicalType.NightVision][id] = true
            end
            if bThermalImagine then
                ItemBaseTool.ItemSpeicalTypeMap[ItemBaseTool.EItemSpeicalType.ThermalImagine][id] = true
            end
            if bFlashlight then
                ItemBaseTool.ItemSpeicalTypeMap[ItemBaseTool.EItemSpeicalType.Flashlight][id] = true
            end
        end
    end
end

function ItemBaseTool.SetMysticalSkinFloorAppearance(mysticalSkinItem)
    if not mysticalSkinItem then
        return
    end
    if not mysticalSkinItem:GetRawPropInfo() then
        return
    end
    local floorAppearanceId = UFMysticalSkinColorMaterialManager:GetFloorAppearance(mysticalSkinItem.id)
    if not floorAppearanceId then
        return
    end
    local mysticalSkinInfo = pb.MysticalSkinInfo:New()
    mysticalSkinInfo.appearance = pb.MysticalAppearanceInfo:New()
    mysticalSkinInfo.appearance.id = floorAppearanceId
    mysticalSkinItem:SetWeaponMysticalSkinInfo2PropInfo(mysticalSkinItem:GetRawPropInfo(), mysticalSkinInfo)
end

return ItemBaseTool