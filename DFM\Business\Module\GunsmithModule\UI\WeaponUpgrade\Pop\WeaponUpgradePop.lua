----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class WeaponUpgradePop : LuaUIBaseView
--武器升级界面
local WeaponUpgradePop = ui("WeaponUpgradePop")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local Config = Module.Gunsmith.Config
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

function WeaponUpgradePop:Ctor()
    self._wtRootWindows = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtRootWindows:BindCloseCallBack(function()
        Facade.UIManager:CloseUI(self)
        Module.Gunsmith.Config.Events.evtWeaponUpgradePopOnClose:Invoke()
    end)
    self._wtItemView = self:Wnd("WBP_ShopItemTemplate", IVCommonItemTemplate)
    self._wtCheckBox = self:Wnd("wSelectBtn",          UICheckBox) --全选
    self._wtDFGrade1 = self:Wnd("wCurLvlText",         UITextBlock)--等级1
    self._wtDFGrade2 = self:Wnd("wTargetLvlText",      UITextBlock)--等级2
    self._wtArrowImg = self:Wnd("wToImage",            UIImage)    --箭头
    self._wtLeveExp1 = self:Wnd("wExpText",            UITextBlock)--经验1
    self._wtLeveExp2 = self:Wnd("wExpText_2",          UITextBlock)--经验2
    self._wtAddLeExp = self:Wnd("wExpText_1",          UITextBlock)--新增经验
    self._wtProgBar1 = self:Wnd("wCurLvlProgressBar",  UIProgressBar)--进度条白
    self._wtProgBar2 = self:Wnd("wTargetProgressBar",  UIProgressBar)--进度条录
    self._wtGradeTip = self:Wnd("DFRichTextBlock_113", UITextBlock)  --预安装提示
    self._wtWaterBox = UIUtil.WndWaterfallScrollBox(self, "wExpItemBox",  self._OnWaterfallCount, self._OnWaterfallWidget)
    local btn = {
        btnText    = Config.Loc.GunsmithUpgrade or "",
        caller     = self,
        bNeedClose = false,
        bNeedDeClose = false,
        fClickCallback   = self._OnbtnClicked,
        fDeClickCallback = nil,
    }
    self._wtCommonButtons = self._wtRootWindows:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {btn}, false)
    self._wtCheckBox:Event("OnCheckStateChanged", self._OnBtnOnClicked, self)
    --挡板
    self:Wnd("DFCanvasPanel_57", UITextBlock):Visible()
    self:Wnd("DFCanvasPanel_150", UITextBlock):Visible()
end

function WeaponUpgradePop:_OnBtnOnClicked(isBool)
    self._isUpdate = isBool
    if isBool then
        self:_GetAddExpSum()
    else
        for index, value in ipairs(self._list or {}) do
            value.cardNum = 0
        end
        self._addExp = 0
    end
    self:_SetExpCards()
    self:_SetExpLevel()
    self:_UpToLevelMaxTips()
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.WeaponUpgradePopSelectAllClicked, isBool)
end

function WeaponUpgradePop:_OnbtnClicked()
    local items, nums, targetIds, targetGIds = {}, {}, {}, {}
    for _, card in pairs(self._list or {}) do
        if card.cardNum and card.cardNum > 0 then
            table.insert(items, {
                id  = card.id,
                gid = card.gid,
            })
            table.insert(nums, card.cardNum)
            if self._weapon then
                table.insert(targetIds,  self._weapon.id  or 0)
                table.insert(targetGIds, self._weapon.gid or 0)
            end
        end
    end
    if #items > 0 then
        Module.Gunsmith.Field:GetOldWeaponExp(self._curExp)
        Server.CollectionServer:DoUseMultiItems(items, nums, targetIds, targetGIds, function()end)
    end
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.WeaponUpgradePopUpgradeClicked, self._isUpdate)
end


function WeaponUpgradePop:OnInitExtraData(weapon, bLevel)
    self._weapon = weapon
    self._bLevel = bLevel
end

function WeaponUpgradePop:OnOpen()
end

function WeaponUpgradePop:OnShowBegin()
    self:_AddEventListener()
    self:_InitHandle()
    self:_InitData()
    --是否预安装
    if self._bLevel and self._curLevel and self._bLevel > self._curLevel then
        self:_GetAddExpSum(self._bLevel)
    end
    self:_InitPanel()
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.WeaponUpgradePopOnShowBegin)
end

function WeaponUpgradePop:OnHideBegin()
    self:RemoveAllLuaEvent()
    Module.ItemDetail:CloseItemDetailPanel()
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.WeaponUpgradePopOnHideBegin)
end

function WeaponUpgradePop:OnNavBack()
    return false
end

function WeaponUpgradePop:OnClose()
    self.btns = nil
end

--------------------------------------------------事件监听与刷新处理--------------------------------------------------
function WeaponUpgradePop:_AddEventListener()
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionUsePropSucess, self._OnRefreshUpgrade, self)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtWeaponNotAddExped, self._OnWeaponNotAddExped, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtWeaponUpgradePop, self._OnWeaponUpgradePop, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtMPWeaponUpMainOnClose, self._OnMPWeaponUpMainOnClose, self)
end

function WeaponUpgradePop:_OnWeaponNotAddExped()
    self:_UpToLevelMaxTips()
end

function WeaponUpgradePop:_OnWeaponUpgradePop(index, curNum)
    local data = nil
    if self._list then
        data = self._list[index]
    end
    if data then
        data.cardNum = curNum or 0
    end
    self:_GetAddCardNum()
    self:_GetCurCardNum(data)
    self:_SetExpLevel()
    self:_UpToLevelMaxTips()
    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.WeaponUpgradePopExpCardChanged, index)
end

--断线重连
function WeaponUpgradePop:_OnRelayConnected()
    GunsmithUIContextLogic.ProcessContext()
	local gid = GunsmithUIContextLogic.GetGUID()
	local group = GunsmithUIContextLogic.GetGroupID()
    if gid and group then
        self._weapon = Server.InventoryServer:GetItemByGid(gid, group)
        self:_InitData()
        self:_InitPanel()
    end
end

function WeaponUpgradePop:_OnRefreshUpgrade()
    --满级处理
    local Exp = nil
    local weaponFeature = self._weapon:GetFeature(EFeatureType.Weapon)
    if weaponFeature then
        local level = weaponFeature:GetWeaponLevel()
        if level and self._levels then
            local data = self._levels[level]
            if data then
                Exp = data.Exp
            end
        end
    end

    if Exp == 0 then
        Facade.UIManager:CloseUI(self)
        return
    end
    self:_InitData()
    if self._isAnim then
        if self._progress2 and self._progress1 then
            local length = self._progress2 - self._progress1
            if length > 0 then
                self._count = 0.5/0.02
                self._leng = length/self._count
                local func = function()
                    if self._progress1 and self._leng then
                        self._progress1 = self._progress1 + self._leng
                        self._wtProgBar1:SetPercent(self._progress1 or 0)
                    end
                    if self._count then
                        self._count = self._count - 1
                    end
                    if self._count and 0 >= self._count then
                        self:_RemoveTimer()
                        self:_InitPanel()
                    end
                end
                self:_RunTimer(func)
                return
            end
        end
    end
    self:_RemoveHandle()
    self:_InitPanel()
end


function WeaponUpgradePop:_RunTimer(func)
    if self._dfTimer == nil then
        self._dfTimer = Timer:NewIns(0.02, 0)
    end
    if self._dfTimer then
        if func then
            self._dfTimer:AddListener(func, self)
        end
        self._dfTimer:Start()
    end
end

function WeaponUpgradePop:_RemoveTimer()
    if self._dfTimer then
        self._dfTimer:Stop()
        self._dfTimer:Release()
        self._dfTimer = nil
    end
end

function WeaponUpgradePop:_OnMPWeaponUpMainOnClose()
    self:_InitHandle()
    self:_InitData()
    self:_InitPanel()
end

function WeaponUpgradePop:_GetCurCardNum(data)
    local ExpSum = nil
    for index, value in pairs(self._levels or {}) do
        if value and value.Exp == 0 then
            local data = self._levels[value.Level]
            if data then
                ExpSum = data.ExpSum
            end
            break
        end
    end

    if self._addExp and self._curExp then
        local curExp = self._addExp + self._curExp
        if curExp and ExpSum and data and data.cardNum then
            if curExp > ExpSum and data.exp then
                local num = (curExp - ExpSum)/data.exp
                data.cardNum = data.cardNum - math.floor(num)
                --非正检查
                if 0 > data.cardNum then
                    data.cardNum = 0
                end
                self:_GetAddCardNum()
                --如果超出最大经验,刷新一下经验卡显示
                if self._list and #self._list > 0 then
                    self._wtWaterBox:RefreshAllItems()
                end
            end
        end
    end
end

function WeaponUpgradePop:_GetAddCardNum()
    local addExp = 0
    for _, value in ipairs(self._list or {}) do
        if value.cardNum and value.exp then
            addExp = addExp + value.cardNum*value.exp
        end
    end
    self._addExp = addExp
end

function WeaponUpgradePop:_GetAddExpSum(curLevel)
    if curLevel == nil then
        local data = nil
        for key, value in pairs(self._levels or {}) do
            if value and value.Exp == 0 then
                data = value
            end
        end
        if data then
            curLevel = data.Level
        end
    end
    local ExpSum = nil
    if self._levels then
        local data = self._levels[curLevel]
        if data then
            ExpSum = data.ExpSum
        end
    end
    local CardExpSum = 0
    for index, value in ipairs(self._list or {}) do
        if value.exp and value.num then
            CardExpSum = CardExpSum + value.exp*value.num
        end
    end
    if ExpSum and self._curExp then
        ExpSum = ExpSum - self._curExp
    end
    if ExpSum and ExpSum > CardExpSum then
        self._addExp = CardExpSum or 0
    else
        self._addExp = ExpSum or 0
    end
end

--------------------------------------------------数据初始化与界面刷新--------------------------------------------------
function WeaponUpgradePop:_InitData(addExp)
    if self._weapon then
        local weaponFeature = self._weapon:GetFeature(EFeatureType.Weapon)
        if weaponFeature then
            self._curLevel = weaponFeature:GetWeaponLevel()
            self._curExp   = weaponFeature:GetWeaponExp()
        end
        self._addExp = addExp or 0
        self._levels = Server.WeaponAssemblyServer:GetWeaponLevelUnLockData(self._weapon.id)
    end
    local cards = Server.WeaponAssemblyServer:GetWeaponExpItems()
    self._list = {}
    for key, card in ipairs(cards or {}) do
        local itemData = Server.CollectionServer:GetCollectionItemById(card.ItemID)
        local num = itemData and itemData.num or 0
        table.insert(self._list, {
            id  = tonumber(card.ItemID),
            num = num,
            exp = card.AddWeaponExp,
            cardNum = 0,
            gid = card.gid,
        })
    end
end

function WeaponUpgradePop:_InitPanel()
    self:_SetExpCards()
    self:_SetExpLevel()
    if self._weapon then
        self._wtRootWindows:SetTitle(string.format(Config.Loc.GunsmithUpgradeWeapon, self._weapon.name or ""))
    end
end

function WeaponUpgradePop:_SetExpLevel()
    if self._weapon and self._levels and self._curExp and self._addExp then
        local addSumExp = self._curExp + self._addExp
        local addLevel = Server.WeaponAssemblyServer:GetWeaponLevelByExp(self._weapon.id, addSumExp)
        --ExpSum当前等级总经验,Exp下一级经验
        if self._curLevel then
            local data = self._levels[self._curLevel]
            if data and data.ExpSum and data.Exp then
                self._progress1 = (self._curExp - data.ExpSum)/data.Exp
                self._wtProgBar1:SetPercent(self._progress1)
                self._wtDFGrade1:SetText(string.format(Config.Loc.GunsmithGrade, self._curLevel))
            end
        end
        if addLevel then
            local data = self._levels[addLevel]
            if data and addSumExp and data.ExpSum and data.Exp then
                local num = addSumExp - data.ExpSum
                self._progress2 = num/data.Exp
                self._wtProgBar2:SetPercent(self._progress2)
                self._wtDFGrade2:SetText(string.format(Config.Loc.GunsmithGrade, addLevel))
                self._wtLeveExp1:SetText(num)
                self._wtLeveExp2:SetText(string.format("/%s", data.Exp == 0 and num or data.Exp))
                self._wtAddLeExp:SetText(string.format("+%s", self._addExp or 0))
                --经验卡禁止继续添加
                for index, card in ipairs(self._list or {}) do
                    if data.Exp == 0 then
                        card.isBanAdd = true
                    else
                        card.isBanAdd = false
                    end
                end
            end
        end
        self._isAnim = self._curLevel == addLevel
        local isUpdate = addSumExp > self._curExp
        self._isUpdate = isUpdate
        self._wtCheckBox:SetMainTitle(isUpdate and Config.Loc.GunsmithUnSelected or Config.Loc.GunsmithSelectAll)
        self._wtCheckBox:SetIsChecked(isUpdate)
        self._wtItemView:InitItem(self._weapon)
        self._wtItemView:SelfHitTestInvisible()
        for index, btn in pairs(self._wtCommonButtons or {}) do
            if btn then
                btn:SetBtnEnable(isUpdate)
            end
        end
        --新增经验信息
        if isUpdate then
            self._wtArrowImg:SelfHitTestInvisible()
            self._wtProgBar2:SelfHitTestInvisible()
            self._wtDFGrade2:SelfHitTestInvisible()
            self._wtAddLeExp:SelfHitTestInvisible()
        else
            self._wtArrowImg:Collapsed()
            self._wtProgBar2:Collapsed()
            self._wtDFGrade2:Collapsed()
            self._wtAddLeExp:Collapsed()
        end
        if self._curLevel == addLevel then
            self._wtProgBar1:SelfHitTestInvisible()
        else
            self._wtProgBar1:Collapsed()
        end
        --预安装提示
        self._wtGradeTip:SetText(StringUtil.Key2StrFormat(Config.Loc.PreInstallationUnlocking, {level = string.format(Config.Loc.GunsmithGrade2, self._bLevel)}))
        if self._bLevel then
            if self._bLevel > self._curLevel then
                self._wtGradeTip:SelfHitTestInvisible()
            else
                self._wtGradeTip:Collapsed()
            end
        end
        --手柄适配
        self:_AddHandleAdaptation(true)
    end
end

function WeaponUpgradePop:_SetExpCards()
    if self._list and self._addExp and self._addExp > 0 then
        --深度优先算法
        local SumExp = 0
        for index, card in ipairs(self._list or {}) do
            if card.num and card.exp then
                SumExp = SumExp + card.num * card.exp
                card.cardNum = card.num
            end
            if SumExp >= self._addExp then
                break
            end
        end
        local Exp = SumExp - self._addExp
        for index = #self._list, 1, -1 do
            local card = self._list[index]
            if card and Exp and card.exp and card.cardNum then
                --这里要向下取整
                local num = math.floor(Exp/card.exp)
                if card.cardNum > 0 and Exp > 0 then
                    num = card.cardNum > num and num or card.cardNum
                    Exp = Exp - num/card.exp
                    card.cardNum = card.cardNum - num
                end
            end
        end
    end
    self:_GetAddCardNum()
    if self._list and #self._list > 0 then
        self._wtWaterBox:RefreshAllItems()
    end
end

function WeaponUpgradePop:_AddHandleAdaptation(isBool)
    self:AddBtnHandle()
    if isBool then
        --添加Pop底部按钮(这里只是显示,没有实际功能->额外AddBtnHandle添加)
        self._wtRootWindows:AddSummaries({self._isUpdate and "Gunsmith_Cancel_Gamepad" or "Gunsmith_SelectAll_Gamepad"})
        local func = function()
            if self._wtCheckBox then
                self._wtCheckBox:SelfClick()
            end
        end
        self:AddBtnHandle("Gunsmith_SelectAll_Gamepad", func, self._wtCheckBox, true)
    end
end

--------------------------------------------------WaterfallScrollBox逻辑处理--------------------------------------------------
function WeaponUpgradePop:_OnWaterfallCount()
    if self._list then
        return #self._list
    else
        return 0
    end
end

function WeaponUpgradePop:_OnWaterfallWidget(position, itemWidget)
    local index = position
    if self._list and index and itemWidget then
        local data = self._list[index]
        local count = #self._list
        itemWidget:InitData(index, data, count)
        --手柄适配
        self:AddWidgetHandle(itemWidget.wtDFCommonAddDecSliderV1)
    end
end

-----------------------------------------------------按钮回调事件-------------------------------------------------------
function WeaponUpgradePop:_UpToLevelMaxTips()
    if self._weapon and self._curExp and self._addExp then
        if self._btime then
            return
        end
        self._btime = true
        Timer.DelayCall(0.2, function()
            self._btime = false
        end, self)
        local level = Server.WeaponAssemblyServer:GetWeaponLevelByExp(self._weapon.id, self._curExp + self._addExp)
        local data
        if self._levels then
            data = self._levels[level]
        end
        if data and data.Exp == 0 then
            Module.CommonTips:ShowSimpleTip(string.format(Module.Gunsmith.Config.Loc.GunsmithUpToLevelMax, data.Level or 0))
        end
    end
end

function WeaponUpgradePop:_InitHandle()
    if isvalid(self._dfNavGroup) then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._dfNavGroup)
    else
        self._dfNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtWaterBox, self, "Hittest")
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._dfNavGroup)
    end
end

function WeaponUpgradePop:_RemoveHandle()
    self._dfNavGroup = nil
    WidgetUtil.RemoveNavigationGroup(self)
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

--*********************************************************************************[通用函数]***********************************************************
function WeaponUpgradePop:AddWidgetHandle(widget)
    if IsHD() and widget then
        if isvalid(self._dfNavGroup) then
            local controller = nil
            if widget.wtDFCommonAddDecSlider and widget.wtDFCommonAddDecSlider.DFCommonAddDecHolder then
                controller = widget.wtDFCommonAddDecSlider.DFCommonAddDecHolder.SlotContainer
            end
            if controller then
                self._dfNavGroup:AddNavWidgetToArray(controller)
            else
                self._dfNavGroup:AddNavWidgetToArray(widget)
            end
        end
    end
end

function WeaponUpgradePop:_OnCallBackTimer()
    for key, func in pairs(self._dfCallBacks or {}) do
        if type(func) == "function" then
            func()
        end
    end
end

function WeaponUpgradePop:AddTarHandle(actionName, func)
    if not IsHD() then
        return
    end
    if self._dfTabBarInputs == nil then
        self._dfTabBarInputs = {}
    end
    if actionName then
        local callback = nil
        --没有func只做显示
        if func then
            callback = CreateCallBack(func, self)
        end
        --注册导航栏按钮
        if self._dfTabBarInputs then
            table.insert(self._dfTabBarInputs, {actionName = actionName, func = callback, caller = self, bUIOnly = false})
            Module.CommonBar:SetBottomBarTempInputSummaryList(self._dfTabBarInputs)
        end
    else
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._dfTabBarInputs = nil
    end
end

function WeaponUpgradePop:AddBtnHandle(actionName, func, btn, isLong, level, isAixs)
    if not IsHD() then
        return
    end
    if self._dfHandles == nil then
        self._dfHandles = {}
    end
    if actionName then
        --默认Pop级别
        local priority = level == false and EDisplayInputActionPriority.UI_Stack or EDisplayInputActionPriority.UI_Pop
        local handle = nil
        --默认非轴映射
        if isAixs then
            handle = self:AddAxisInputActionBinding(actionName, func, self, priority)
        else
            handle = self:AddInputActionBinding(actionName, EInputEvent.IE_Pressed, CreateCallBack(func, self), self, priority)
        end
        if handle then
            table.insert(self._dfHandles, handle)
        end
        --是否长按(按钮特有:[btn:ButtonClick()/btn:SelfClick()])
        if btn and handle then
            if isLong then
                if btn.SetDisplayInputActionWithLongPress then
                    btn:SetDisplayInputActionWithLongPress(handle, self, actionName, true, nil, true)
                end
            else
                if btn.SetDisplayInputAction then
                    btn:SetDisplayInputAction(actionName, true, nil, true)
                end
            end
        end
    else
        --清除手柄响应
        for index, handle in ipairs(self._dfHandles or {}) do
            self:RemoveInputActionBinding(handle)
        end
        self._dfHandles = nil
    end
end

return WeaponUpgradePop