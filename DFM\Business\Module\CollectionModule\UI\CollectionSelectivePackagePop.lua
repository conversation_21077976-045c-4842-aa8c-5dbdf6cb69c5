----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPUINavHittestFallbackType = import "EGPUINavHittestFallbackType"
local EGPUINavigationDistanceType = import "EGPUINavigationDistanceType"
-- END MODIFICATION

---@class CollectionSelectivePackagePop : LuaUIBaseView
local CollectionSelectivePackagePop = ui("CollectionSelectivePackagePop")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local CollectionLogic  = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local ItemDetailView = require "DFM.Business.Module.ItemDetailModule.UI.ItemDetailView"
local CollectionConfig = Module.Collection.Config

function CollectionSelectivePackagePop:Ctor()
    self._wtPropScrollBox = UIUtil.WndWaterfallScrollBox(self, "wtPropScrollBox", self._OnGetItemsCount, self._OnProcessItemWidget)
    self._wtInfoText = self:Wnd("wtInfoText", UITextBlock)
    self._wtMainPanel = self:Wnd("wtMainPanel", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked,self)
    self._wtMainPanel:BindCloseCallBack(fCallbackIns)
    self._wtMainPanel:SetBackgroudClickable(true)
    self._wtItemDetailView = self:Wnd("wtItemDetailView", ItemDetailView)
    self._wtItemDetailView:SetIsOutBtnSlot(true)
    self._wtItemDetailView:SetStatePlayAnimOnShow(false)
    self._wtItemDetailView:SetCloseBtnVisible(false)
    self._wtItemDetailView:IsShowIconItem(false)
    self._wtConfirmBtn = self:Wnd("wtConfirmBtn", DFCommonButtonOnly)
    self._wtConfirmBtn:Event("OnClicked", self._OnConfirmBtnClicked, self)
    self._wtEmptySlot = self:Wnd("wtEmptySlot", UIWidgetBase)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot, nil, nil)
        self._wtEmptyHint = getfromweak(weakUIIns)
    end
    self._wtEmptyHint:BP_SetText(CollectionConfig.Loc.PleaseSelectAnyPrize)
    self._wtEmptyHint:SetCppValue("Set_Type", 1)
    self._wtEmptyHint:BP_Set_Type()
    self._prizeItems = {}
    self._boxItem = nil
    self._selectedPos = 0
    self._selectedPrizes = {}
end

function CollectionSelectivePackagePop:OnInitExtraData(boxItem, prizes)
    self._boxItem = boxItem
    self._maxNum = math.min(CollectionConfig.BoxOpenMaxNum, self._boxItem.num)
    self._prizeItems = prizes or self._prizeItems
    self._selectedPos = 0
    self._selectedPrizes = {}
end

function CollectionSelectivePackagePop:OnOpen()
    self:AddListeners()
    self:RefreshView()
end

function CollectionSelectivePackagePop:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtItemDetailView)
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptySlot)
end

function CollectionSelectivePackagePop:OnShowBegin()
    if not self._inputTypeChangedHandle then 
        self._inputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
    end
    self:_EnableGamepadFeature()
end

function CollectionSelectivePackagePop:OnHideBegin()
    if self._inputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._inputTypeChangedHandle)
        self._inputTypeChangedHandle = nil
    end
    self:_DisableGamepadFeature()
end

function CollectionSelectivePackagePop:AddListeners()
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self)
end


function CollectionSelectivePackagePop:_EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._wtMainPanel:ActiveDynamicType(true)
    self._wtConfirmBtn:SetDisplayInputAction("Collection_PopConfirm_Gamepad", true, nil, true)
    if self._confirmHandle == nil then
        self._confirmHandle = self:AddInputActionBinding("Collection_PopConfirm_Gamepad", EInputEvent.IE_Pressed, self._OnConfirmBtnClicked, self, EDisplayInputActionPriority.UI_Pop)
    end
    if not self._wtSliderDecHandle then
        self._wtSliderDecHandle = self:AddInputActionBinding("Collection_Reduce_Gamepad", EInputEvent.IE_Pressed, self._OnClickSliderDec,self, EDisplayInputActionPriority.UI_Pop)
    end
    if not self._wtSliderIncHandle then
        self._wtSliderIncHandle = self:AddInputActionBinding("Collection_Increase_Gamepad", EInputEvent.IE_Pressed, self._OnClickSliderInc,self, EDisplayInputActionPriority.UI_Pop)
    end
    if not self._scrollActionHandler then
        self._scrollActionHandler = self:AddAxisInputActionBinding("Collection_Scroll_Gamepad",self._OnScrollItemDetail,self,EDisplayInputActionPriority.UI_Pop)
    end
    if not self._wtNavGroupPropScrollBox then 
        self._wtNavGroupPropScrollBox = WidgetUtil.RegisterNavigationGroup(self._wtPropScrollBox, self, "Hittest")
        if self._wtNavGroupPropScrollBox then
            local navStrategy = self._wtNavGroupPropScrollBox:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetHittestFallbackDistanceType(EGPUINavHittestFallbackType.Horizental, EGPUINavigationDistanceType.Vertical)
            end
            self._wtNavGroupPropScrollBox:AddNavWidgetToArray(self._wtPropScrollBox)
            --self._wtNavGroupPropScrollBox:AddNavWidgetToArray(self._wtItemDetailView._wtScrollBoxContent)
            if self._wtItemDetailView._wtScrollBoxContent then
                --self._wtNavGroupPropScrollBox:SetScrollRecipient(self._wtItemDetailView._wtScrollBoxContent)
            end
            self._wtNavGroupPropScrollBox:MarkIsStackControlGroup() 
            self._wtNavGroupPropScrollBox:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end
    --[[
    self._wtMainPanel:AddSummaries({
        "Collection_Reduce_Gamepad",
        "Collection_Increase_Gamepad"
    })
    --]]
    self._wtMainPanel:OverrideGamepadSetting("Collection_PopConfirm_Gamepad", nil, WidgetUtil.EUINavDynamicType.Default)
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    if not hasdestroy(self._selectedCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
    else
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroupPropScrollBox)
    end
end

function CollectionSelectivePackagePop:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._confirmHandle then
        self:RemoveInputActionBinding(self._confirmHandle)
    end
    if self._wtSliderDecHandle then
        self:RemoveInputActionBinding(self._wtSliderDecHandle)
    end
    if self._wtSliderIncHandle then
        self:RemoveInputActionBinding(self._wtSliderIncHandle)
    end
    if self._scrollActionHandler then
        self:RemoveInputActionBinding(self._scrollActionHandler)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._confirmHandle = nil
    self._wtSliderDecHandle = nil
    self._wtSliderIncHandle = nil
    self._scrollActionHandler = nil
    self._wtNavGroupPropScrollBox = nil 
    self._NavConfigHandler = nil
    self._wtMainPanel:RemoveSummaries()
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionSelectivePackagePop:_OnInputTypeChanged(inputType)
    if IsHD() and inputType == EGPInputType.Gamepad then
        self._bShouldIgnoreClick = true
        self:_EnableGamepadFeature()
    else
        self:_DisableGamepadFeature()
    end
end

function CollectionSelectivePackagePop:_OnClickSliderDec()
    if self._sliderSubUIIns and self._selectedPrizes[self._selectedPos] then
        self._selectedPrizes[self._selectedPos] = math.max(self._selectedPrizes[self._selectedPos]-1, 0)
        self._sliderSubUIIns._wtDFCommonAddDecSliderV1:ChangeCurNum(self._selectedPrizes[self._selectedPos], 1)
        --self:_OnValueChanged(self._selectedPrizes[self._selectedPos])
    end
end

function CollectionSelectivePackagePop:_OnClickSliderInc()
    if self._sliderSubUIIns and self._selectedPrizes then
        local consumeNum = 0
        for key, num in pairs(self._selectedPrizes) do
            consumeNum = consumeNum + num
        end
        if consumeNum < self._maxNum then
            if not self._selectedPrizes[self._selectedPos] then
                self._selectedPrizes[self._selectedPos] = 0
            end
            self._selectedPrizes[self._selectedPos] = self._selectedPrizes[self._selectedPos] + math.min(math.max(self._maxNum-consumeNum, 0), 1)
            self._sliderSubUIIns._wtDFCommonAddDecSliderV1:ChangeCurNum(self._selectedPrizes[self._selectedPos], 1)
        else
            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.NoMoreSelectivePackage)
        end
        --self:_OnValueChanged(self._selectedPrizes[self._selectedPos])
    end
end

function CollectionSelectivePackagePop:_OnScrollItemDetail(axis)
    if not IsHD() or not WidgetUtil.IsGamepad() or WidgetUtil.IsEquipZero(axis) then
        return
    end
    WidgetUtil.ScrollByAxis(self._wtItemDetailView._wtScrollBoxContent, axis)
end

function CollectionSelectivePackagePop:_OnRelayConnected()
    self:RefreshView()
end

function CollectionSelectivePackagePop:RefreshView()
    if self._boxItem.itemSubType == ECollectableType.SelectivePackage then
        self._wtMainPanel:SetTitle(CollectionConfig.Loc.OpenSelectivePackage)
    elseif self._boxItem.itemSubType == ECollectableType.SuitPackage then
        self._wtMainPanel:SetTitle(CollectionConfig.Loc.OpenSuitPackage)
    end
    if not IsInEditor() then
        self._wtPropScrollBox:RefreshAllItems()
    end
    local consumeNum = 0
    for key, num in pairs(self._selectedPrizes) do
        consumeNum = consumeNum + num
    end
    self._wtInfoText:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.ConsumePackage,
    {
        ["Quality"] = self._boxItem.quality-1,
        ["PackageName"] = self._boxItem.name,
        ["ConsumeNum"] = math.min(consumeNum, self._maxNum),
        ["TotalNum"] = self._maxNum, 
    }))
    self._wtConfirmBtn:SetIsEnabled(consumeNum > 0)
    if self._selectedPos > 0 then
        self._wtItemDetailView:SelfHitTestInvisible()
        self:_UpdateDetailView()
        self._wtEmptySlot:Collapsed()
    else
        self._wtItemDetailView:Collapsed()
        self._wtEmptySlot:SelfHitTestInvisible()
    end
end


function CollectionSelectivePackagePop:_OnGetItemsCount()
    return #self._prizeItems or 0
end

function CollectionSelectivePackagePop:_OnProcessItemWidget(position, itemWidget)
    local item = self._prizeItems[position]
    local nameComp = itemWidget:FindOrAdd(EComp.TopLeftTwoLineText, UIName2ID.IVTwoLinesTitleComponent, EIVSlotPos.Top)
    if nameComp and item then
        nameComp:SetTwoLineText(item.name, item.shortNameSec)
        nameComp:EnableComponent(true)
    end
    local bindingComp = itemWidget:FindOrAdd(EComp.BottomLeftIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomLeft)
    bindingComp:SetStyle(CommonWidgetConfig.EIVIconTextStyle.Binding)
    itemWidget:EnableComponent(false)
    local dispensingComp = itemWidget:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVDispensingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order1)
    if dispensingComp then
        local function fDispensingBtnClicked()
            if self._selectedPrizes[position] then
                self._selectedPrizes[position] = math.max(self._selectedPrizes[position]-1, 0)
                dispensingComp:SetNum(self._selectedPrizes[position])
                if self._selectedPrizes[position] == 0 then
                    self._selectedPrizes[position] = nil
                end
                itemWidget:EnableComponent(EComp.TopRightIconText, self._selectedPrizes[position] and self._selectedPrizes[position] > 0 or false)
                local consumeNum = 0
                for key, num in pairs(self._selectedPrizes) do
                    consumeNum = consumeNum + num
                end
                self._wtInfoText:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.ConsumePackage,
                {
                    ["Quality"] = self._boxItem.quality-1,
                    ["PackageName"] = self._boxItem.name,
                    ["ConsumeNum"] = math.min(consumeNum, self._maxNum),
                    ["TotalNum"] = self._maxNum, 
                }))
                self._wtConfirmBtn:SetIsEnabled(consumeNum > 0)
                self:_UpdateSlider()
            end
        end
        dispensingComp:BindDispensingBtnClickedCallback(fDispensingBtnClicked)
    end
    itemWidget:EnableComponent(EComp.TopRightIconText, self._selectedPrizes[position] and self._selectedPrizes[position] > 0 or false)
    itemWidget:InitItem(item)
    local fClickCb = CreateCallBack(self._OnPrizeItemClick, self,itemWidget, position)
    itemWidget:BindCustomOnClicked(fClickCb)
    itemWidget:SetSelected(item, false)
end


function CollectionSelectivePackagePop:_OnPrizeItemClick(prizeCell, position)
    if self._bShouldIgnoreClick then
        self._bShouldIgnoreClick = false
        return
    end
    if self._selectedPos ~= position then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._prizeItems[self._selectedPos], false)
        end
        self._selectedCell = prizeCell
        self._selectedPos = position
        self._selectedCell:SetSelected(self._prizeItems[self._selectedPos], true)
        local consumeNum = 0
        for key, num in pairs(self._selectedPrizes) do
            consumeNum = consumeNum + num
        end
        self._wtInfoText:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.ConsumePackage,
        {
            ["Quality"] = self._boxItem.quality-1,
            ["PackageName"] = self._boxItem.name,
            ["ConsumeNum"] = math.min(consumeNum, self._maxNum),
            ["TotalNum"] = self._maxNum, 
        }))
        if self._prizeItems[self._selectedPos] then   
            self._wtItemDetailView:SelfHitTestInvisible()        
            self:_UpdateDetailView()
            self._wtEmptySlot:Collapsed()
        end
    else
        local consumeNum = 0
        for key, num in pairs(self._selectedPrizes) do
            consumeNum = consumeNum + num
        end
        if consumeNum < self._maxNum then
            if self._selectedPrizes[self._selectedPos] then
                self._selectedPrizes[self._selectedPos] = self._selectedPrizes[self._selectedPos] + 1
            else
                self._selectedPrizes[self._selectedPos] = 1
            end
            self:_UpdateSlider()
            self:_OnValueChanged(self._selectedPrizes[self._selectedPos])
        else
            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.NoMoreSelectivePackage)
        end
    end
end

function CollectionSelectivePackagePop:_OnValueChanged(curNum)
    logerror("[v_dzhanshen] CollectionSelectivePackagePop:_OnValueChanged curNum="..tostring(curNum))
    logerror("[v_dzhanshen] CollectionSelectivePackagePop:_OnValueChanged wtDFCommonAddDecSliderV1 curNum="..tostring(self._sliderSubUIIns._wtDFCommonAddDecSliderV1:GetCurNum()).." minNum="..tostring(self._sliderSubUIIns._wtDFCommonAddDecSliderV1:GetCurMinNum()).." maxNum="..tostring(self._sliderSubUIIns._wtDFCommonAddDecSliderV1:GetCurMaxNum()))
    self._selectedPrizes[self._selectedPos] = curNum
    local dispensingComp = self._selectedCell:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVDispensingComponent, EIVSlotPos.TopRight, EIVCompOrder.Order1)
    if dispensingComp then
        dispensingComp:SetNum(curNum)
    end
    self._selectedCell:EnableComponent(EComp.TopRightIconText, self._selectedPrizes[self._selectedPos] > 0)
    local consumeNum = 0
    for key, num in pairs(self._selectedPrizes) do
        consumeNum = consumeNum + num
    end
    self._wtInfoText:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.ConsumePackage,
    {
        ["Quality"] = self._boxItem.quality-1,
        ["PackageName"] = self._boxItem.name,
        ["ConsumeNum"] = math.min(consumeNum, self._maxNum),
        ["TotalNum"] = self._maxNum, 
    }))
    self._wtConfirmBtn:SetIsEnabled(consumeNum > 0)
end

function CollectionSelectivePackagePop:_UpdateDetailView()
    local consumeNum = 0
    for key, num in pairs(self._selectedPrizes) do
        consumeNum = consumeNum + num
    end
    local curNum
    local maxNum
    if self._selectedPrizes[self._selectedPos] and self._selectedPrizes[self._selectedPos] > 0 then
        curNum = self._selectedPrizes[self._selectedPos]
        maxNum = self._maxNum-consumeNum+self._selectedPrizes[self._selectedPos]
    else
        curNum = 0
        maxNum = self._maxNum-consumeNum
    end
    local loadFinishCallback = function (sliderSubUIIns)
        if hasdestroy(self._sliderSubUIIns) then
            self._sliderSubUIIns = sliderSubUIIns
            self._sliderSubUIIns:RemoveEvent("OnAddDecSliderCurNumChanged", self._OnValueChanged, self)
            self._sliderSubUIIns:Event("OnAddDecSliderCurNumChanged", self._OnValueChanged, self)
            self._sliderSubUIIns:InitDecAddInputActionName("Collection_Reduce_Gamepad", "Collection_Increase_Gamepad", true)
            self._sliderSubUIIns:SetCppValue("bIsFocusable", false)
            self._sliderSubUIIns:SetBtnVisible(false)
            self._sliderSubUIIns:SetSliderTitleText(CollectionConfig.Loc.AmountSelected)
            --self._sliderSubUIIns._wtDFCommonAddDecSliderV1:BindCustomAddOnClicked(self._OnClickSliderInc, self)
        end
        --[[
        if self._sliderSubUIIns._wtDFCommonAddDecSliderV1._wtDFCommonAddDecHolder then
            self._sliderSubUIIns._wtDFCommonAddDecSliderV1._wtDFCommonAddDecHolder:SetCppValue("MinNum", 0)
            self._sliderSubUIIns._wtDFCommonAddDecSliderV1._wtDFCommonAddDecHolder:SetCppValue("MaxNum", maxNum)
        end
        --]]
        self._sliderSubUIIns:InitSliderNum(curNum, 1, 0, maxNum)
    end
    if not hasdestroy(self._sliderSubUIIns) then
        loadFinishCallback(self._sliderSubUIIns)
    end
    self._wtItemDetailView:SetAdjustBtnPosWhenIconHide(false)
    self._wtItemDetailView:UpdateItem(
        self._prizeItems[self._selectedPos], 
        true,
        {{bKeepSlotContentBtn = not hasdestroy(self._sliderSubUIIns)}}, 
        UIName2ID.ItemDetailViewBtnSliderBtn, 
        nil, 
        loadFinishCallback,
        curNum,
        1,
        maxNum,
        0
    )
end

function CollectionSelectivePackagePop:_UpdateSlider()
    if self._sliderSubUIIns then
        local consumeNum = 0
        for key, num in pairs(self._selectedPrizes) do
            consumeNum = consumeNum + num
        end
        if self._selectedPrizes[self._selectedPos] and self._selectedPrizes[self._selectedPos] > 0 then
            self._sliderSubUIIns:InitSliderNum(self._selectedPrizes[self._selectedPos], 1, 0, self._maxNum-consumeNum+self._selectedPrizes[self._selectedPos]) 
        else
            self._sliderSubUIIns:InitSliderNum(0, 1, 0, self._maxNum-consumeNum) 
        end
    end
end

function CollectionSelectivePackagePop:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function CollectionSelectivePackagePop:_OnConfirmBtnClicked()
    self:_RequestOpenBox()
end


function CollectionSelectivePackagePop:_RequestOpenBox()
    if table.nums(self._selectedPrizes) > 0 and table.nums(self._prizeItems) > 0 then
        if self._bOpeningBox == true then
            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.PackageUnboxingTip)
            return
        end
        self._bOpeningBox = true
        local fCallbackIns = CreateCallBack(function(self, res)
            self._bOpeningBox = false
            Facade.UIManager:CloseUI(self)
        end,self)
        local items = {}
        local nums = {}
        local group_ids = {}
        local prop_ids = {}
        for key, num in pairs(self._selectedPrizes) do
            if num > 0 and self._prizeItems[key] then
                table.insert(items, self._boxItem)
                table.insert(nums, num)
                table.insert(group_ids, self._prizeItems[key].group_id)
                table.insert(prop_ids, self._prizeItems[key].id)  
            end
        end
        CollectionLogic.RequestOpenBox(items, nums, group_ids, prop_ids, false, fCallbackIns)
    end
end



function CollectionSelectivePackagePop:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end


return CollectionSelectivePackagePop
