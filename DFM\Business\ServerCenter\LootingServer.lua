----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSLooting)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class LootingServer : ServerBase
local LootingServer = class("LootingServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

local UInventoryManager = import "InventoryManager"
local FInventoryItemInfo = import "InventoryItemInfo"
local UItemIDUtil = import "ItemIDUtil"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local FItemMoveCmd = import "ItemMoveCmd"
local EItemState = import "EItemState"
local EPlayerLootingState = import "EPlayerLootingState"
local ULootingGlobalDefine = import "LootingGlobalDefine"
local EInteractorType = import "EInteractorType"
local EItemChangeType = import "EItemChangeType"
local UGPGameplayGlobalDelegates = import "GPGameplayGlobalDelegates"
local FLootingStateParam = import "LootingStateParam"
local EItemInfoUpdatedReason = import "EItemInfoUpdatedReason"
local ULuaSubsystem = import "LuaSubsystem"
local UInventoryUtil = import "InventoryUtil"
local ELootObjType = import "ELootObjType"
local EEventType = import "EEventType"

local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"
local ObjectPool = require "DFM.YxFramework.Managers.Coroutine.ObjectPool"
local URoleLoadComponent = import "RoleLoadComponent"
local UHardwareParamHelper = import "HardwareParamHelper"

local EEquipmentTypeToSlotType = {
    [EEquipmentType.Helmet] = ESlotType.Helmet,
    [EEquipmentType.BreastPlate] = ESlotType.BreastPlate,
    [EEquipmentType.ChestHanging] = ESlotType.ChestHanging,
    [EEquipmentType.Bag] = ESlotType.Bag,
    [EEquipmentType.SafeBox] = ESlotType.SafeBox,
    [EEquipmentType.KeyChain] = ESlotType.KeyChain
}

local function PropChangeTypeToName(inType)
    for name, type in pairs(PropChangeType) do
        if type == inType then
            return "PropChangeType." .. name
        end
    end
end

---@class EItemInfoModifyState
local EItemInfoModifyState = {
    NotModified = 0, -- 未修改
    OutOfDate = 1, -- 过时的
    Advanced = 2, -- 先进的
}

local function log(...)
    loginfo("[LootingServer]", ...)
end


---@class PropChange
---@field change_type PropChangeType
---@field OldItemData ItemBase
---@field NewItemInfo FInventoryItemInfo
---@field group ESlotGroup
---@field bIncreaseOrDecrease boolean 数量增加或减少了（nil表示不增不减）

function LootingServer:Ctor()
    self.NEARBY_PICKUP_LENGTH = 5
    self.NEARBY_PICKUP_MIN_HEIGHT = 5
	self.NEARBY_PICKUP_MAX_HEIGHT = 999
    self.NEARBY_PICKUP_HEIGHT_COMPLEMENTATION = 0
    self.DEADBODY_MIN_HEIGHT = ULootingGlobalDefine.DEADBODY_MIN_HEIGHT
    self.DEADBODY_HEIGHT_COMPLEMENTATION = ULootingGlobalDefine.DEADBODY_HEIGHT_COMPLEMENTATION
    self.MAX_LOOTING_DISTANCE = ULootingGlobalDefine.MAX_LOOTING_DISTANCE
    ---@type boolean 是否使用FastArray的同步功能进行增量同步
    self.USE_FAST_ARRAY_REPLICATION = true
    ---@type boolean 是否等Controller SetPawn以及PlayerState同步下来之后再FetchPlayerItems（有风险，加开关控制）
    self.DELAY_FETCH_PLAYER_ITEMS = true
    ---@type boolean 是否使用Diff回退预表现
    self.USE_DIFF_REVERT_PREBEHAVE = true
    ---@type boolean 是否使用RPC替代属性同步
    self.USE_RPC_REPLICATION = UHardwareParamHelper.GetConsoleVariableRefBoolValue("loot.UseRPCReplication4ItemArray")
    ---@type boolean
    self.DELAY_DATA_UPDATE = false
    ---@type boolean 发送移动物品请求后未收到回包超过多少秒认为超时，小于等于0表示不启用该功能
    self.ITEM_MOVE_TIMEOUT_SECONDS = 3
    ---@type boolean 是否启用移动物品超时回退
    self.ITEM_MOVE_TIMEOUT_REVERT = true
    ---@type boolean 是否启用移动物品超时刷新UI
    self.ITEM_MOVE_TIMEOUT_REFRESH_UI = true
    ---@type boolean 是否启用移动物品超时移除缓存
    self.ITEM_MOVE_TIMEOUT_REMOVE_CACHE = true
    ---@type boolean 是否打开容器后延迟再加载物品
    self.USE_DELAY_RELOAD_CONTAINER = true
    ---@type number 固定单帧时长（Debug用）
    self.FORCE_FRAME_DURATION = 0
    self:ResetLootingServerData()
end

function LootingServer:ResetLootingServerData()
    log("ResetLootingServerData")

    self._bAutoSortSpace = false

    self._slotItemMap = {}
    ---@type ItemMoveCmd[]
    self._cacheRecentMoveCmds = {}
    self._interactorType = nil
    ---@type table<number, boolean>
    self._startupItemGIDs = {}

    ---@type LootSelectorData
    self._currentSelectorData = nil

    self._nearbyPickupMapping = {}

    ---@type table<ItemBase, itemMoveInfo>
    self._itemMoveEvents = {}
    ---@type table<number>
    self._itemMoveFrameTaskIds = {}
    self._updateFrame = -1

    self.introDelayHandle = 0
    self.currentFocus = nil

    ---@type ItemSlot
    self.dropEquipContainerSlot = nil
    ---@type ItemSlot
    self.dropContainerSlot = nil
    ---@type boolean
    self._bHasFetchedPlayerItems = false
    ---@type PropChange[]
    self._propChangeList = {}
    self._propChangePool = ObjectPool.Create(self._CreatePropChange, self, 10)
    self._cachedData = {}
    self.highValueItemGid = 0
    self.bSpecialInspect = false
    self._currentCmdGid = 0

    self._openBagTimeoutTimer = nil
    ---@type table<number,number>
    self.gid2LastMoveTime = {}

    self._bNeedReloadAllItems = false

    self._needReloadContainerGid = nil

    self._hasSendNightInfo = false

    self._nextTickTime = 0
end

function LootingServer:IsUsingFastArrayReplication()
    return self.USE_FAST_ARRAY_REPLICATION and not IsNetModeStandalone()
end

-----------------------------------------------------------------------
--region Optimization

function LootingServer:MarkItemDirty(gid)
    -- 已废弃
end

---@param itemMoveInfo itemMoveInfo
function LootingServer:AddPendingItemMoveEvent(itemMoveInfo)
    log("LootingServer:AddPendingItemMoveEvent", itemMoveInfo.item.name, itemMoveInfo.item.gid, "PropChangeType." .. enum2string(PropChangeType, itemMoveInfo.Reason))
    self._itemMoveEvents[itemMoveInfo.item] = itemMoveInfo
    local frameTaskId = Facade.LuaFramingManager:RegisterFrameTask(self._ItemMoveImpl, self, {itemMoveInfo})
    if frameTaskId then
        self._itemMoveFrameTaskIds[itemMoveInfo] = frameTaskId
    end
end

---@param itemMoveInfos itemMoveInfo[]
function LootingServer:AddPendingItemMoveEvents(itemMoveInfos)
    local itemGidsNeedScrollTo = ItemOperaTool.GetItemGidsNeedScrollTo(itemMoveInfos)
    for _, itemMoveInfo in ipairs(itemMoveInfos) do
        itemMoveInfo.bNeedScrollTo = itemMoveInfo.item and (itemGidsNeedScrollTo[itemMoveInfo.item.gid] or false) or false
        self:AddPendingItemMoveEvent(itemMoveInfo)
    end
end

---@param itemMoveInfo itemMoveInfo
function LootingServer:_ItemMoveImpl(itemMoveInfo)
    log("LootingServer:_ItemMoveImpl", itemMoveInfo.item.name, itemMoveInfo.item.gid, "PropChangeType." .. enum2string(PropChangeType, itemMoveInfo.Reason))
    self._itemMoveEvents[itemMoveInfo.item] = nil
    self._itemMoveFrameTaskIds[itemMoveInfo] = nil
    self.Events.evtLootingItemMove:Invoke(itemMoveInfo)
end

function LootingServer:CheckItemPendingMove(item)
    return self._itemMoveEvents[item] ~= nil
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Life
function LootingServer:OnInitServer()
    local gameInst = GetGameInstance()
    UDFMGameplayGlobalDelegates.Get(gameInst).OnGameInventoryPickupContainerSubPickupsChanged:Add(self._OnPickupContainerSubPickupsChanged, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnGameInventoryItemListChanged:Add(self._OnInventoryItemListChanged, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnGameInventoryClientInitialize:Add(self._OnInventoryClientInitialize, self)
    -- UDFMGameplayGlobalDelegates.Get(gameInst).OnGameCurrentPotentialInteractorsChange:Add(self._OnGameCurrentPotentialInteractorsChange, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnGameInventoryPickupChanged:Add(self._OnGameCurrentPotentialInteractorsChange, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnRepUseItemInfo:Add(self._OnRepUseItemInfo, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnClientPickupChanged:Add(self._OnClientPickupChanged, self)
    -- ULuaSubsystem.Get().OnLuaClientSeamlessTravelEnd:Add(self._OnLuaClientSeamlessTravelEnd, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnItemArrayPostReplicatedAdd:Add(self._OnItemArrayPostReplicatedAdd, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnItemArrayPreReplicatedRemove:Add(self._OnItemArrayPreReplicatedRemove, self)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnItemArrayPostReplicatedChange:Add(self._OnItemArrayPostReplicatedChange, self)

    UDFMGameplayGlobalDelegates.Get(gameInst).OnNotifyItemSourceInvalid:Bind(self._OnNotifyItemSourceInvalid, self)
    ULuaSubsystem.Get().OnActorProxyUpdate:Add(self._OnActorProxyUpdate, self)

    self.Events = {
        evtSmallBag2BigBag = LuaEvent:NewIns("LootingServer.evtSmallBag2BigBag"),
        evtLootingItemMove = LuaEvent:NewIns("LootingServer.evtLootingItemMove"),

        evtPlayerItemsFetched = LuaEvent:NewIns("LootingServer.evtPlayerItemsFetched"),

        evtItemMoveFail = LuaEvent:NewIns("LootingServer.evtItemMoveFail"),
        
        evtBoxDataChange = LuaEvent:NewIns("LootingServer.evtBoxDataChange"),

        evtItemMoveTimeOutRefreshUI = LuaEvent:NewIns("LootingServer.evtItemMoveTimeOutRefreshUI"),

        evtLootObjHasUnSearchedItemStateChange = LuaEvent:NewIns("LootingServer.evtLootObjHasUnSearchedItemStateChange"),

        evtLootObjHidePreviousInventory = LuaEvent:NewIns("LootingServer.evtLootObjHidePreviousInventory"),
    }
end

function LootingServer:OnDestroyServer()
    
end

function LootingServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.Game then
        if ItemOperaTool.IsInTestMap() then
            self:ResetLootingServerData()
            self:FetchPlayerItems()
        end
    end
end

function LootingServer:LateUpdate(dt)
    if self.FORCE_FRAME_DURATION > 0 then
        local now = TimeUtil.GetCurrentTimeMillis()
        if now < self._nextTickTime then
            return
        end
        self._nextTickTime = now + self.FORCE_FRAME_DURATION
    end
    if self._bNeedReloadAllItems then
        self._bNeedReloadAllItems = false
        self:ReloadAllItems()
    end
    if #self._propChangeList > 0 then
        self:_PreProcessPropChangeList()
        self:_ProcessPropChangeList(self._propChangeList)
        for _, propChange in ipairs(self._propChangeList) do
            self:_FreePropChange(propChange)
        end
        self._propChangeList = {}
        self.Events.evtLootObjHasUnSearchedItemStateChange:Invoke(self:CheckLootObjHasUnSearchedItem())
    end
    if self._bHasFetchedPlayerItems or not self.DELAY_FETCH_PLAYER_ITEMS then
        loginfo("LootingServer:LateUpdate RemoveTick")
        LuaTickController:Get():RemoveTick(self)
    else
        local character = Facade.GameFlowManager:GetCharacter()
        if isvalid(character) then
            if UInventoryManager.Get(character) == self._cachedData.InvMgr then
                if character:GetUin() ~= 0 then
                    self:FetchPlayerItems()
                    LuaTickController:Get():RemoveTick(self)
                else
                    loginfo("LootingServer:LateUpdate character:GetUin() == 0")
                end
            else
                loginfo("LootingServer:LateUpdate UInventoryManager.Get(character) ~= self._cachedData.InvMgr", UInventoryManager.Get(character), "~=", self._cachedData.InvMgr)
            end
        else
            loginfo("LootingServer:LateUpdate character is invalid")
        end
    end
end

function LootingServer:MarkNeedReloadAllItems()
    self._bNeedReloadAllItems = true
    self:MarkAllCmdReverted()
    LuaTickController:Get():RegisterTick(self)
end

function LootingServer:HasFetchPlayerItems()
    return self._bHasFetchedPlayerItems
end

-- 仅局内SOL模式和局外收藏室需要LootingServer的功能
function LootingServer:CheckServerEnabled()
    if ItemOperaTool.bIsInCollectionRoom then
        return true
    end

    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow ~= EGameFlowStageType.Game and currentGameFlow ~= EGameFlowStageType.LobbyToGame then
        return false
    end

    if InGameController:Get():IsTDMMode() or Server.MatchServer:IsInTDMGameMode() then
        return false
    end

    return true
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public
---@param cmd ItemMoveCmd
function LootingServer:AddMoveCmdCache(cmd)
    self._cacheRecentMoveCmds[cmd.gid] = cmd
end

---@return ItemMoveCmd
function LootingServer:GetMoveCmdCache(cmdGid)
    return self._cacheRecentMoveCmds[cmdGid]
end

---@return ItemMoveCmd
function LootingServer:RemoveMoveCmdCache(cmdGid)
    if self._cacheRecentMoveCmds[cmdGid] then
        local ret = self._cacheRecentMoveCmds[cmdGid]
        Timer.CancelDelay(ret.timeoutTimer)
        self._cacheRecentMoveCmds[cmdGid] = nil
        return ret
    else
        return nil
    end
end

function LootingServer:RemoveAllMoveCmdCaches()
    local ret = self._cacheRecentMoveCmds
    self._cacheRecentMoveCmds = {}

    return ret
end

function LootingServer:CheckItemIsWaitingDS(item)
    for cmdGid, moveCmd in pairs(self._cacheRecentMoveCmds) do
        if moveCmd.item.gid == item.gid and not moveCmd.bIsTimeOut then
            return true
        end
    end

    return false
end

---@return ItemBase
function LootingServer:GetItemDataByGid(gid)
    -- return self._slotItemMap[gid]
    local groups2Check = {
        self:GetSelfSlotGroup(),
        ESlotGroup.Nearby,
        ESlotGroup.DeadBody,
    }
    local item
    for _, group in ipairs(groups2Check) do
        item = Server.InventoryServer:GetItemByGid(gid, group)
        if item then
            return item
        end
    end
    return nil
end

local function groupSortItemInfoList(itemInfoArray)
	local itemInfoGroupDict = {}
	for _, itemInfo in pairs(itemInfoArray) do
		local slotType = itemInfo.AttachPosition

		itemInfoGroupDict[slotType] = itemInfoGroupDict[slotType] or {}
		table.insert(itemInfoGroupDict[slotType], itemInfo)
	end

	local allSlotType = {}
	for SlotType, _ in pairs(itemInfoGroupDict) do
		table.insert(allSlotType, SlotType)
	end
	table.sort(allSlotType)

	return itemInfoGroupDict, allSlotType
end

function LootingServer:InitDeadbodyData(deadbody)
    local itemInfoArray = {}
    itemInfoArray = deadbody:GetSubPickups(itemInfoArray)

    for _, slotType in ipairs(DeadbodySlotTypes) do
        local slot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.DeadBody)
        -- slot:ResetSlot()
        slot:ClearItems()

        if #deadbody.LootBoxRepData.SpaceRepData == 0 then
            if slotType == ESlotType.ChestHangingContainer or slotType == ESlotType.BagContainer then
                self:_HideSlot(slot)
            end
        end
    end

    if #deadbody.LootBoxRepData.SpaceRepData == 0 and not IsHD() and not IsNetModeStandalone() then
        self.Events.evtLootObjHidePreviousInventory:Invoke(EGamePickupType.NearbyDeadBody)
    end

    self:_InitTargetSpace(deadbody.LootBoxRepData.SpaceRepData, ESlotGroup.DeadBody)

    local items = self:FetchItemsInDeadbody(deadbody)
    for _, item in ipairs(items) do
        RuntimeIconTool.PreLoadItemIcon(item)
    end
end

function LootingServer:InitNearbyPickupData(nearbyPickups)
    local nearBySlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    nearBySlot:ResetSlot()

    self:_PreProcessNearbySlot()

    self._nearbyPickupMapping = {}
    local items = {}
	if nearbyPickups then
		items = Server.LootingServer:FetchItemsFromNearbyPickups(nearbyPickups)
        for _, item in pairs(items) do
            RuntimeIconTool.PreLoadItemIcon(item)
        end
        for _, pickup in pairs(nearbyPickups) do
            if isvalid(pickup) then
                local itemInfo = pickup:GetPickupItemInfo()
                if itemInfo:GetGID() ~= 0 then
                    self._nearbyPickupMapping[itemInfo:GetGID()] = pickup
                end
            end
        end
	end

    self:_PostProcessNearbySlot()

    return items
end

function LootingServer:InitPickupBoxData_SceneBox(pickupBox)
	--清空容器数据
	local nearbyContainerSlotData = Server.InventoryServer:GetSlot(ESlotType.NearbyContainer, ESlotGroup.Nearby)
	nearbyContainerSlotData:ClearItems()

    if #pickupBox.LootBoxRepData.SpaceRepData == 0 then
        self:_HideSlot(nearbyContainerSlotData)
        if not IsHD() and not IsNetModeStandalone() then
            self.Events.evtLootObjHidePreviousInventory:Invoke(EGamePickupType.SceneBox)
        end
    end

    local spaceRepData = pickupBox.LootBoxRepData.SpaceRepData
    self:_InitTargetSpace(spaceRepData, ESlotGroup.Nearby)

    local items = self:FetchItemsInPickupBox(pickupBox, nearbyContainerSlotData)
    for _, item in ipairs(items) do
        RuntimeIconTool.PreLoadItemIcon(item)
    end
end

function LootingServer:InitPickupBoxData_DropContainer(pickupBox)
	local slotType, containerSlotType = 0, 0
	local itemID = tonumber(pickupBox.InventoryIdName)

	local itemMainType = ItemHelperTool.GetMainTypeById(itemID)
	local itemSubType = ItemHelperTool.GetSubTypeById(itemID)

	slotType = EEquipmentTypeToSlotType[itemSubType]
	containerSlotType = slotType * 1000 + 1

    -- 容器本身
    local targetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Nearby)
    targetSlot:ClearItems()
    self.dropEquipContainerSlot = targetSlot

    -- 容器里的东西
    local targetContainerSlotData = Server.InventoryServer:GetSlot(containerSlotType, ESlotGroup.Nearby)
	targetContainerSlotData:ClearItems()
    self.dropContainerSlot = targetContainerSlotData

    if #pickupBox.LootBoxRepData.SpaceRepData == 0 then
        self:_HideSlot(targetContainerSlotData)
        if not IsHD() and not IsNetModeStandalone() then
            self.Events.evtLootObjHidePreviousInventory:Invoke(EGamePickupType.DropContainer)
        end
    end

    local spaceRepData = pickupBox.LootBoxRepData.SpaceRepData
    self:_InitTargetSpace(spaceRepData, ESlotGroup.Nearby)

    local items = self:FetchItemsInPickupBox(pickupBox, targetSlot, true)
    for _, item in ipairs(items) do
        RuntimeIconTool.PreLoadItemIcon(item)
    end
end

function LootingServer:FetchItemsInDeadbody(deadbody)
    local slotItemDatas = {}
    if isvalid(deadbody) then
        local itemInfoArray = {}
        itemInfoArray = deadbody:GetSubPickups(itemInfoArray)
        local newItemInfoArray = {}
        for _, itemInfo in pairs(itemInfoArray) do
            if table.contains(DeadbodySlotTypes, itemInfo.AttachPosition) then
                table.insert(newItemInfoArray, itemInfo)
            end
        end
        itemInfoArray = newItemInfoArray
        if self:IsUsingFastArrayReplication() then
            self:SyncItemsBeforeFetch(itemInfoArray, ESlotGroup.DeadBody)
        end
        for _, itemInfo in pairs(itemInfoArray) do
            local slot = Server.InventoryServer:GetSlot(itemInfo.AttachPosition, ESlotGroup.DeadBody)
            local newItemData = self:CreateItemData(itemInfo, slot)
            if newItemData then
                table.insert(slotItemDatas, newItemData)
            end
        end
    end
    return slotItemDatas
end

---@param targetSlot ItemSlot
function LootingServer:FetchItemsInPickupBox(pickupBox, targetSlot, bIsDropContainer)
    bIsDropContainer = setdefault(bIsDropContainer, false)
    local slotItemDatas = {}
    if isvalid(pickupBox) then
        local equipItemInfo = nil
        local itemInfoArray = {}
        if bIsDropContainer then
            equipItemInfo = pickupBox:GetPickupItemInfo()
            if equipItemInfo then
                table.insert(itemInfoArray, equipItemInfo)
            end
        end
        itemInfoArray = pickupBox:GetSubPickups(itemInfoArray)

        if self:IsUsingFastArrayReplication() then
            self:SyncItemsBeforeFetch(itemInfoArray, ESlotGroup.Nearby)
        end

        if bIsDropContainer then
            if equipItemInfo then
                local newItemData = self:CreateItemData(equipItemInfo, targetSlot)
                if newItemData then
                    table.insert(slotItemDatas, newItemData)
                end
            end
            targetSlot = targetSlot:GetContainerSlot()
        end

        for _, itemInfo in pairs(itemInfoArray) do
            if equipItemInfo == nil or itemInfo.ItemGid ~= equipItemInfo.ItemGid then
                local newItemData = self:CreateItemData(itemInfo, targetSlot)
                if newItemData then
                    table.insert(slotItemDatas, newItemData)
                end
            end
        end
    end
    return slotItemDatas
end

---@param bShouldCloseBagPanel boolean
function LootingServer:FetchPlayerItems(bShouldCloseBagPanel)
    bShouldCloseBagPanel = setdefault(bShouldCloseBagPanel, true)
    if not self:CheckServerEnabled() then
        return
    end

    logerror("FetchPlayerItems")
    local inventoryMgr = self:GetCharacterInventoryMgr()
    if not inventoryMgr or not isvalid(inventoryMgr) then
        logerror("LootingServer:FetchPlayerItems inventoryMgr is not valid")
        return
    end

    self._bHasFetchedPlayerItems = true

    -- update startup item gids
    self._startupItemGIDs = {}
    for _, gid in ipairs(inventoryMgr.StartupItemGIDs) do
        self._startupItemGIDs[gid] = true
    end

    -- 先清空每一个Slot
    for _, slotType in pairs(self:GetInGameSlotTypes()) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType, self:GetSelfSlotGroup())
        if itemSlot then itemSlot:ResetSlot() end
    end

    local itemInfoArray = {}
    itemInfoArray = inventoryMgr:GetAllInventory(itemInfoArray, false)
    log(string.format("FetchPlayerItems num=%d", #itemInfoArray))

    local spaceRepData = inventoryMgr.RepData.SpaceRepData
    self:_InitTargetSpace(spaceRepData, self:GetSelfSlotGroup())

    local itemInfoGroupDict, allSlotType = groupSortItemInfoList(itemInfoArray)
    for _, slotType in pairs(allSlotType) do
        local itemInfoList = itemInfoGroupDict[slotType]
        if (itemInfoList) then
            local targetSlot = Server.InventoryServer:GetSlot(slotType, self:GetSelfSlotGroup())
            for _, itemInfo in ipairs(itemInfoList) do
                local newItemData = self:CreateItemData(itemInfo, targetSlot)
                if newItemData then
                    RuntimeIconTool.PreLoadItemIcon(newItemData)
                end

                -- Special rule for keychain
                if slotType == ESlotType.KeyChain then
                    local keyChainContSlot = Server.InventoryServer:GetRefContainerSlot(targetSlot)
                    keyChainContSlot:InitKeyChainStructFromItemInfo(itemInfo)
                end
            end
        end
    end

    if bShouldCloseBagPanel then
        self.Events.evtPlayerItemsFetched:Invoke()
    end
end

function LootingServer:SetHasFetchedPlayerItems(bHasFetchedPlayerItems)
    self._bHasFetchedPlayerItems = bHasFetchedPlayerItems or false
end

function LootingServer:ServerCheckFrequentDataUpdated()
    local inventoryMgr = self:GetCharacterInventoryMgr()

    if not inventoryMgr or not isvalid(inventoryMgr) then
        return
    end

    inventoryMgr:ServerCheckFrequentDataUpdated()
end

-- ---@param weaponSlotType ESlotType
-- local function fUpdateWeaponAmmo(weaponSlotType)
--     local slot = Server.InventoryServer:GetSlot(weaponSlotType)
--     if not slot:IsMainWeaponSlot() then
--         return
--     end

--     local weaponItem = slot:GetEquipItem()
--     local weaponFeature = weaponItem and weaponItem:GetFeature(EFeatureType.Weapon) or nil
--     if not weaponFeature then
--         return
--     end

--     local character = Facade.GameFlowManager:GetCharacter()
--     local weapon = character:GetWeaponByGuid(weaponItem.gid)
--     local ammoDataComponent = weapon:GetWeaponDataComponentAmmo()
--     local newCurAmmo = ammoDataComponent:GetCurrentAmmoCount()

--     local oldAmmo = weaponFeature
-- end

-- function LootingServer:LocalUpdateWeaponAmmo()
--     local character = Facade.GameFlowManager:GetCharacter()
    
-- end

function LootingServer:SyncItemsBeforeFetch(itemInfoList, slotGroup)
    local propChangeList = {}
    for _, itemInfo in ipairs(itemInfoList) do
        local item = self:GetItemDataByGid(itemInfo.ItemGid)
        if itemInfo.ItemGid ~= 0 and item and item.InSlot and item.InSlot:GetSlotGroup() ~= slotGroup then
            if self:GetItemInfoModifyState(itemInfo, item) ~= EItemInfoModifyState.OutOfDate then
                local changeType = self:GetChangeType(item, itemInfo, slotGroup)
                if changeType ~= PropChangeType.PropChangeNone then
                    local propChange = self:_ObtainPropChange(changeType, item, itemInfo, slotGroup)
                    table.insert(propChangeList, propChange)
                end
            else
                loginfo(string.format("LootingServer:SyncItemsBeforeFetch forbidden because item=(gid=%d name=%s) has more advanced version in slotGroup=%d than slotGroup=%d",
                        item.gid, item.name, item.InSlot:GetSlotGroup(), slotGroup))
            end
        end
    end
    if #propChangeList > 0 then
        self:_ProcessPropChangeList(propChangeList)
        for _, dataChange in ipairs(propChangeList) do
            self:_FreePropChange(dataChange)
        end
    end
end

function LootingServer:FetchItemsFromNearbyPickups(pickups)
    local itemInfoList = {}
    for _, pickup in pairs(pickups) do
        if isvalid(pickup) then
            local itemInfo = pickup:GetPickupItemInfo()
            if itemInfo:GetGID() ~= 0 then
                table.insert(itemInfoList, itemInfo)
            end
        end
    end
    if self:IsUsingFastArrayReplication() then
        self:SyncItemsBeforeFetch(itemInfoList, ESlotGroup.Nearby)
    end

    local items = {}
    local nearBySlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    for _, itemInfo in pairs(itemInfoList) do
        local newItem = self:CreateItemData(itemInfo, nearBySlot)
        if newItem then
            table.insert(items, newItem)
        end
    end

    return items
end

-- 获取玩家的物资价值总价
---@return number
function LootingServer:GetTotalPrice()
    local totalPrice = 0
    for _, slotType in pairs(self:GetVisibleSlotTypes()) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType)
        if itemSlot then
            for _, itemData in ipairs(itemSlot:GetItems_RefConst()) do
                totalPrice = totalPrice + itemData:GetTotalSellPrice()
            end
        end
    end
    return totalPrice
end

--- 获取玩家的增量物资总价
---@return number
function LootingServer:GetIncrementPrice()
    local totalPrice = 0
    for _, slotType in pairs(self:GetVisibleSlotTypes()) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType)
        if itemSlot then
            for _, itemData in ipairs(itemSlot:GetItems_RefConst()) do
                if not self._startupItemGIDs[itemData.gid] then
                    totalPrice = totalPrice + itemData:GetTotalSellPrice()
                end
            end
        end
    end
    return totalPrice
end

function LootingServer:GetAutoSortSpace()
    return self._bAutoSortSpace
end
function LootingServer:SetAutoSortSpace(bAutoSortSpace)
    self._bAutoSortSpace = bAutoSortSpace
end

function LootingServer:SetPlayerLootingState(lootingState)
    local character = Facade.GameFlowManager:GetCharacter()
    if character then
        local lootParam = FLootingStateParam()
        self._currentCmdGid = self._currentCmdGid + 1
        lootParam.CmdGid = self._currentCmdGid
        lootParam.LootingState = lootingState

        local box = Server.LootingServer:GetCurrentInteractingBox()
        if isvalid(box) then
            lootParam.LootContainer = box
        else
            lootParam.LootContainer = InGameController:Get():GetGPCharacterInventoryMgr()
        end
        if self:GetCurrentInteractorType() == EGamePickupType.NearbyPickups then
            lootParam.bIsInteractingWithPickups = true
        else
            lootParam.bIsInteractingWithPickups = false
        end
        lootParam.HighValueItemGid = self.highValueItemGid
        lootParam.bIsSpecialInspect = self.bSpecialInspect
        character:RequestSetIsLooting(lootParam)
        self:CancelOpenBagTimeoutTimer()
        if isvalid(box) and lootingState ~= EPlayerLootingState.EPlayerLootingState_None then
            self._openBagTimeoutTimer = Timer.DelayCall(3, function()
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.LootBadNetwork)
            end)
        end
        if self.USE_DELAY_RELOAD_CONTAINER then
            if lootingState == EPlayerLootingState.EPlayerLootingState_None then
                self._needReloadContainerGid = nil
            end
        end
    end
end

function LootingServer:ServerNotifySwitchFromLoot2BagView()
    local invMgr = self:GetCharacterInventoryMgr()
    if isvalid(invMgr) then
        invMgr:ServerNotifySwitchFromLoot2BagView()
    end
end

function LootingServer:CancelOpenBagTimeoutTimer()
    Timer.CancelDelay(self._openBagTimeoutTimer)
    self._openBagTimeoutTimer = nil
end

function LootingServer:OnNotifyLootingState(cmdGid, lootingState)
    if cmdGid == self._currentCmdGid then
        if lootingState ~= EPlayerLootingState.EPlayerLootingState_None then
            self:CancelOpenBagTimeoutTimer()
        end
    end
end
-----------------------------------------------------------------------
--region Interactor

function LootingServer:GetCurrentNearbyPickupByGID(gid)
    if gid and self._nearbyPickupMapping and isvalid(self._nearbyPickupMapping[gid]) then
        return self._nearbyPickupMapping[gid]
    end
end

function LootingServer:CheckIsCurrencyGID(gid)
    local deadbody = self:GetCurrentInteractingBox()
    return deadbody and (deadbody.CurrencyGID == gid) or false
end

function LootingServer:GetCurrentInteractingBox()
    local data = self:GetCurrentSelectorData()
    if not data then return nil end

    if data.type == EGamePickupType.SceneBox
    or data.type == EGamePickupType.NearbyDeadBody
    or data.type == EGamePickupType.DropContainer then
        if #data.interactors >= 1 and isvalid(data.interactors[1]) then
            return data.interactors[1]
        end
    end

    return nil
end

function LootingServer:GetCurrentInteractorType()
    local data = self:GetCurrentSelectorData()
    if not data then return nil end
    return data.type
end

---@return LootSelectorData
function LootingServer:GetCurrentSelectorData()
    return self._currentSelectorData
end

function LootingServer:SetCurrentSelectorData(currentSelectorData)
    log(string.format("SetCurrentSelectorData type=%d", currentSelectorData and currentSelectorData.type or -1))
    self._currentSelectorData = currentSelectorData
end

---@param item ItemBase
function LootingServer:GetSourceLootObj(item)
    local ret = self:GetTargetLootObjs(nil, {item})
    if ret and ret[1] then
        return ret[1]
    end
end

---@param overlapItems ItemBase[]
function LootingServer:GetTargetLootObjs(targetSlot, overlapItems)
    local ret = {}

    if not targetSlot and #overlapItems == 1 then
        local targetItem = self:GetItemDataByGid(overlapItems[1].gid)
        if targetItem then
            targetSlot = targetItem.InSlot
        end 
    end

    if targetSlot then
        local slotGroup = targetSlot:GetSlotGroup()
        if slotGroup == self:GetSelfSlotGroup() then
            local invMgr = self:GetCharacterInventoryMgr()
            if isvalid(invMgr) then
                table.insert(ret, invMgr)
            end
        elseif slotGroup == ESlotGroup.Nearby then
            local toSlotType = targetSlot.SlotType
            if toSlotType == ESlotType.NearbyPickups then
                if #overlapItems > 0 then
                    for _, overlapItem in ipairs(overlapItems) do
                        table.insert(ret, self:GetCurrentNearbyPickupByGID(overlapItem.gid))
                    end
                else
                    -- drop item, target loot objs should be empty
                end
            else
                table.insert(ret, self:GetCurrentInteractingBox())
            end
        elseif slotGroup == ESlotGroup.DeadBody then
            table.insert(ret, self:GetCurrentInteractingBox())
        end
    end

    return ret
end

function LootingServer:GetSlotGroupByLootObj(lootObj, bIncludePickups)
    bIncludePickups = setdefault(bIncludePickups, false)
    if not isvalid(lootObj) then
        return ESlotGroup.None
    end
    local invMgr = self:GetCharacterInventoryMgr()
    if isvalid(invMgr) and lootObj == invMgr then
        return self:GetSelfSlotGroup()
    end
    local currentInteractingBox = self:GetCurrentInteractingBox()
    if currentInteractingBox == lootObj then
        local currentInteractorType = self:GetCurrentInteractorType()
        if currentInteractorType == EGamePickupType.SceneBox or currentInteractorType == EGamePickupType.DropContainer then
            return ESlotGroup.Nearby
        elseif self:GetCurrentInteractorType() == EGamePickupType.NearbyDeadBody then
            return ESlotGroup.DeadBody
        end
    end
    if bIncludePickups then
        for _, pickUp in pairs(self._nearbyPickupMapping) do
            if isvalid(pickUp) and pickUp == lootObj then
                return ESlotGroup.Nearby
            end
        end
    end
    return ESlotGroup.None
end

---@param PickupItem ItemBase
function LootingServer:EquipPveWeapon(PickupItem)
    local playerInvMgr = self:GetCharacterInventoryMgr()
    playerInvMgr:ServerEquipPveWeapon(PickupItem.gid)
end

---@param itemMoveCmd ItemMoveCmd
function LootingServer:ProcessItemMoveCmd(itemMoveCmd)
    ---@type ItemBase
    local itemData = itemMoveCmd.item
    local originLoc = itemMoveCmd.originLoc
    ---@type ItemLocation
    local targetLoc = itemMoveCmd.targetLoc
    ---@type ItemSlot
    local srcSlot = itemData.InSlot
    ---@type ItemSlot
    local targetSlot = targetLoc.ItemSlot

    local fromSlotType = srcSlot and srcSlot.SlotType or 0
    local toSlotType = targetSlot and targetSlot.SlotType or 0
    local fromGroupID = srcSlot and srcSlot:GetSlotGroup() or 0
    local toGroupID = targetSlot and targetSlot:GetSlotGroup() or 0

    if itemData and itemData.gid then
        local nowTime = TimeUtil.GetCurrentTimeMillis()
        local itemLastMoveTime = self.gid2LastMoveTime[itemData.gid]
        if itemLastMoveTime and nowTime - itemLastMoveTime < 0.3 then
            loginfo("LootingServer:ProcessItemMoveCmd too frequent move", itemData.name, itemData.gid, itemLastMoveTime, nowTime)
            return false
        end
        self.gid2LastMoveTime[itemData.gid] = nowTime
    end

    -- 检查道具对应的View是否还在等待ItemMove的事件
    -- 如果是，说明这个道具的View可能还没刷新，这个时候触发的交互事件可能不是玩家本意
    -- 因为延迟ItemMove只会延迟一帧，所以直接Discard掉这次操作应该是可以接受的
    if self:CheckItemPendingMove(itemData) then
        logwarning("LootingServer:ProcessItemMoveCmd block because CheckItemPendingMove=true", itemData.name, itemData.gid)
        return false
    end

    -- -- 大包换小包检查
    -- if #itemMoveCmd.targetItems == 1 then
    --     if self:_CheckSwitchBagWarning(itemData, itemMoveCmd.targetItems[1], itemMoveCmd) then
    --         return false
    --     end
    -- end

    if self:ProcessNearbyPickupLocalMoveCmd(itemMoveCmd) then return true end

    -- -- 自动排序下，不允许内部移动
    -- local bAutoSortSpace = self:GetAutoSortSpace()
    -- if bAutoSortSpace then
    --     if fromSlotType == toSlotType and fromGroupID == toGroupID then
    --         return
    --     end
    -- end
    


    -- 通过所有客户端前置校验，开始请求RPC
    log(string.format("ProcessItemMoveCmd,ID=[%s],GID=[%s],from Group=[%s],Slot=[%s],X=[%s],Y=[%s],Index=[%s],bRotated=[%s] to Group=[%s],Slot=[%s],X=[%s],Y=[%s],Index=[%s],bRotated=[%s],bAllowContainerReplaceFailBackup=[%s],bDropItemsFromContainer=[%s],bForceReplaceShell=[%s]",
        itemData.name,
        itemData.gid,
        fromGroupID,
        fromSlotType,
        originLoc.X,
        originLoc.Y,
        originLoc.SubIndex,
        originLoc.bRotated and "true" or "false",
        toGroupID,
        toSlotType,
        targetLoc.X,
        targetLoc.Y,
        targetLoc.SubIndex,
        targetLoc.bRotated and "true" or "false",
        itemMoveCmd:GetAllowContainerReplaceFailBackup() and "true" or "false",
        itemMoveCmd:IsDropItemsFromContainer() and "true" or "false",
        itemMoveCmd:IsForceReplaceShell() and "true" or "false"
    ))

    -- 添加到缓存
    Server.LootingServer:AddMoveCmdCache(itemMoveCmd)

    local playerInvMgr = self:GetCharacterInventoryMgr()
    -- try use refractor code
    local cppMoveCmd = FItemMoveCmd()
    cppMoveCmd.SourceGid = itemData.gid
    local targetGids = cppMoveCmd.TargetGids
    for _, item in ipairs(itemMoveCmd.targetItems) do
        targetGids:Add(item.gid)
    end
    cppMoveCmd.TargetGids = targetGids
    cppMoveCmd.CmdGid = itemMoveCmd.gid
    cppMoveCmd.SourcePos = fromSlotType
    cppMoveCmd.TargetPos = toSlotType
    cppMoveCmd.TargetX = targetLoc.X
    cppMoveCmd.TargetY = targetLoc.Y
    cppMoveCmd.bRotated = targetLoc.bRotated
    cppMoveCmd.moveNum = itemMoveCmd.moveNum
    cppMoveCmd.TargetIndex = targetLoc.SubIndex
    cppMoveCmd.bAllowContainerReplaceFailBackup = itemMoveCmd:GetAllowContainerReplaceFailBackup()
    cppMoveCmd.bDropItemsFromContainer = itemMoveCmd:IsDropItemsFromContainer()
    cppMoveCmd.bForceReplaceShell = itemMoveCmd:IsForceReplaceShell()
    itemMoveCmd.SourceLootObj = self:GetSourceLootObj(itemMoveCmd.item)
    itemMoveCmd.TargetLootObjs = self:GetTargetLootObjs(targetSlot, itemMoveCmd.targetItems)
    cppMoveCmd.SourceLootObj = itemMoveCmd.SourceLootObj
    cppMoveCmd.TargetLootObjs = itemMoveCmd.TargetLootObjs

    local character = Facade.GameFlowManager:GetCharacter()
    local interatorComponent = character and character.InteractorComponent
    if interatorComponent then
        local originPos, originForward, bHitInteractor = interatorComponent:Get1PInteractOrginPosAndForwardForDSByLSI(cppMoveCmd.SourceLootObj, FVector.ZeroVector, FVector.ZeroVector, false)
        cppMoveCmd.OriginPos1P = originPos
        cppMoveCmd.OriginForward1P = originForward
        cppMoveCmd.bHitInteractor1P = bHitInteractor
    end

    playerInvMgr:ClientSendItemMoveCmd(cppMoveCmd, true)
    -- Standalone不需要预表现
    if not UKismetSystemLibrary.IsStandalone(GetWorld()) then
        itemMoveCmd:ProcessLocally(true)

        self:AddTimeoutTimer(itemMoveCmd)
    end

    return true
end

---@param itemMoveCmd ItemMoveCmd
function LootingServer:AddTimeoutTimer(itemMoveCmd)
    if self.ITEM_MOVE_TIMEOUT_SECONDS > 0 then
        local cmdGid = itemMoveCmd.gid
        if cmdGid then
            itemMoveCmd.timeoutTimer = Timer.DelayCall(self.ITEM_MOVE_TIMEOUT_SECONDS, function()
                ---@type ItemMoveCmd
                local cmd = self:GetMoveCmdCache(cmdGid)
                if cmd then
                    if self.ITEM_MOVE_TIMEOUT_REVERT and cmd.bPrebehave then
                        cmd:RevertLocalProcess(true, true)
                    end
                    if self.ITEM_MOVE_TIMEOUT_REFRESH_UI then
                        self.Events.evtItemMoveTimeOutRefreshUI:Invoke(cmd)
                    end
                    if self.ITEM_MOVE_TIMEOUT_REMOVE_CACHE then
                        self:RemoveMoveCmdCache(cmdGid)
                    end
                    cmd.bIsTimeOut = true
                end
            end, self)
        end
    end
end

function LootingServer:MarkAllCmdReverted()
    for _, cmd in pairs(self._cacheRecentMoveCmds) do
        if cmd.bPrebehave and not cmd.bReverted then
            cmd.bReverted = true
        end
    end
end

---@param itemMoveCmd ItemMoveCmd
function LootingServer:ReloadAllItems()
    logerror("LootingServer:ReloadAllItems")
    for _, propChange in ipairs(self._propChangeList) do
        self:_FreePropChange(propChange)
    end
    self._propChangeList = {}
    for _, frameTaskId in pairs(self._itemMoveFrameTaskIds) do
        Facade.LuaFramingManager:CancelFrameTask(frameTaskId)
    end
    self._itemMoveFrameTaskIds = {}
    self._itemMoveEvents = {}

    local allLootObjs = {}
    allLootObjs[self:GetSelfSlotGroup()] = self:GetCharacterInventoryMgr()
    local currentLootObj = self:GetCurrentInteractingBox()
    if currentLootObj then
        local group = self:GetSlotGroupByLootObj(currentLootObj)
        if group ~= ESlotGroup.None then
            allLootObjs[group] = currentLootObj
        end
    end

    local itemInfoMap = {}
    local itemInfoGroup = {}
    for group, lootObj in pairs(allLootObjs) do
        local itemInfoArray = {}
        if group == ESlotGroup.Nearby then
            if self:GetCurrentInteractorType() == EGamePickupType.DropContainer then
                local itemInfo = lootObj:GetPickupItemInfo()
                if itemInfo then
                    table.insert(itemInfoArray, itemInfo)
                end
            end
        end
        if group == self:GetSelfSlotGroup() then
            itemInfoArray = lootObj:GetAllInventory(itemInfoArray, false)
        else
            itemInfoArray = lootObj:GetSubPickups(itemInfoArray)
        end
        for _, itemInfo in pairs(itemInfoArray) do
            if itemInfoMap[itemInfo.ItemGid] then
                if itemInfo.ModifyTimeStamp > itemInfoMap[itemInfo.ItemGid].ModifyTimeStamp
                        or itemInfo:GetDescCount() > itemInfoMap[itemInfo.ItemGid]:GetDescCount()
                        or itemInfo:GetAttrDescCount() > itemInfoMap[itemInfo.ItemGid]:GetAttrDescCount() then
                    itemInfoMap[itemInfo.ItemGid] = itemInfo
                    itemInfoGroup[itemInfo.ItemGid] = group
                end
            else
                itemInfoMap[itemInfo.ItemGid] = itemInfo
                itemInfoGroup[itemInfo.ItemGid] = group
            end
        end
    end
    for group, _ in pairs(allLootObjs) do
        local newItemInfoDict = {}
        for itemGid, groupId in pairs(itemInfoGroup) do
            if groupId == group then
                newItemInfoDict[itemGid] = itemInfoMap[itemGid]
            end
        end

        -- <uint64, ItemBase>
        local itemBaseDict = {}
        local slotTypes = self:GetSlotTypesByGroup(group, self:GetCurrentInteractorType())
        for _, slotType in pairs(slotTypes) do
            local itemSlot = Server.InventoryServer:GetSlot(slotType, group)
            for _, item in pairs(itemSlot:GetItems_RefConst()) do
                itemBaseDict[item.gid] = item
            end
        end

        local dataChanges = self:_diff(itemBaseDict, newItemInfoDict, group, true)

        self:_ProcessPropChangeList(dataChanges)

        for _, dataChange in ipairs(dataChanges) do
            self:_FreePropChange(dataChange)
        end
    end
end

---@param itemMoveCmd ItemMoveCmd
function LootingServer:ProcessNearbyPickupLocalMoveCmd(itemMoveCmd)
    local itemData = itemMoveCmd.item
    local bFromNearbyPickups = itemMoveCmd.originLoc.ItemSlot.SlotType == ESlotType.NearbyPickups
    local bToNearbyPickups = itemMoveCmd.targetLoc.ItemSlot.SlotType == ESlotType.NearbyPickups
    local nearbySlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    local bMoveAnywhere = itemMoveCmd.targetLoc:IsAnywhere()
    local bSplit = itemMoveCmd.moveNum ~= itemData.num

    -- 拆分逻辑，直接走server rpc
    if bSplit then
        return false
    end

    if bFromNearbyPickups and bToNearbyPickups and bMoveAnywhere then
        -- 移动一个散落物到散落的任意位置，不需要处理
        return true
    end

    if bFromNearbyPickups then
        nearbySlot:SetCachedLoc(itemData.gid, nil)
    end
    if bToNearbyPickups and not bMoveAnywhere then
        -- 指定位置放置，需要在客户端记录下位置
        nearbySlot:SetCachedLoc(itemData.gid, itemMoveCmd.targetLoc)
    end

    if bFromNearbyPickups and bToNearbyPickups then
        if #itemMoveCmd.targetItems > 0 then
            -- TODO: 支持交换
            return true
        else
            -- local move, simulate item move cmd and dont' do server rpc
            nearbySlot:SetItemPosFromLoc(itemData, itemMoveCmd.targetLoc)
            ---@type itemMoveInfo
            local itemMoveInfo = {
                item = itemData,
                OldLoc = itemMoveCmd.originLoc,
                NewLoc = itemMoveCmd.targetLoc,
                bInternalMove = true,
                Reason = PropChangeType.Move
            }
            self:AddPendingItemMoveEvent(itemMoveInfo)
            return true
        end
    end

    return false
end

---@return EItemInfoModifyState
function LootingServer:GetItemInfoModifyState(itemInfo, newItem)
    if itemInfo and newItem then
        local bItemInfoOutOfDate = itemInfo.ModifyTimeStamp < newItem.modifyTimeStamp
                or itemInfo:GetDescCount() < newItem.descCount
                or itemInfo:GetAttrDescCount() < newItem.descAttrCount
        if bItemInfoOutOfDate then
            return EItemInfoModifyState.OutOfDate
        end
        local bItemInfoNotModified = itemInfo.ModifyTimeStamp == newItem.modifyTimeStamp
                and itemInfo:GetDescCount() == newItem.descCount
                and itemInfo:GetAttrDescCount() == newItem.descAttrCount
        if bItemInfoNotModified then
            return EItemInfoModifyState.NotModified
        end
        return EItemInfoModifyState.Advanced
    end
    return EItemInfoModifyState.NotModified
end

---@param targetSlot ItemSlot
function LootingServer:CreateItemData(itemInfo, targetSlot)
    local gid = itemInfo.ItemGid

    ---@type ItemBase
    local newItem = nil
    if not self._slotItemMap[gid] then
        local id = UItemIDUtil.ToUint64(itemInfo.ItemId)
        local num = itemInfo.ItemCount
        newItem = ItemBase:New(id, num, gid)

        self._slotItemMap[gid] = newItem
    else
        newItem = self._slotItemMap[gid]
        if newItem.InSlot and newItem.InSlot ~= targetSlot then
            local bItemInfoOutOfDate = self:GetItemInfoModifyState(itemInfo, newItem) == EItemInfoModifyState.OutOfDate
            if bItemInfoOutOfDate then
                -- 如果Lua在别的槽有这个Item的更先进版本，那么这里就不把这个Item加到targetSlot里了
                loginfo(string.format("LootingServer:CreateItemData forbidden because item=(gid=%d name=%s) has more advanced version in slot=(group=%d type=%d) than slot=(group=%d type=%d)",
                        newItem.gid, newItem.name, newItem.InSlot:GetSlotGroup(), newItem.InSlot.SlotType, targetSlot:GetSlotGroup(), targetSlot.SlotType))
                return
            end
        end
    end

    newItem:SetRawCppInfo(itemInfo)
    newItem:AddToSlot(targetSlot, false)
    local newLoc = targetSlot:SetItemPosFromCppItemLocation(newItem, itemInfo.ItemSpaceLocation)
    Server.InventoryServer:AddItemToList(newItem, targetSlot)

    return newItem, newLoc
end

function LootingServer:CheckWeaponCanUnequipAmmo(weaponGID)
    local weapon = self:GetItemDataByGid(weaponGID)
    if not weapon then
        return false
    end

    local descObj = weapon:GetRawDescObj()
    if not descObj then
        return false
    end

    return self:CheckCanUnequipAmmo(descObj:GetAmmoDataItems())
end

function LootingServer:CheckCanUnequipAmmo(ammoDataItems)
    local playerAmmoMapping = {}
    for _, item in Server.InventoryServer:GetItemsIterator() do
        if item:IsBullet() then
            playerAmmoMapping[item.gid] = item.num
        end
    end

    local allAvailableSlotTypes = {
        ESlotType.ChestHangingContainer,
        ESlotType.Pocket,
        ESlotType.BagContainer,
        ESlotType.SafeBoxContainer
    }
    local emptyGrid = 0
    for _, slotType in ipairs(allAvailableSlotTypes) do
        local slot = Server.InventoryServer:GetSlot(slotType)
        emptyGrid = emptyGrid + (slot:GetTotalCapacity() - slot:GetUsedCapacity())
    end

    local character = Facade.GameFlowManager:GetCharacter()
    local availableNum = 0;
    for _, ammoItem in ipairs(ammoDataItems) do
        for gid, num in pairs(playerAmmoMapping) do
            local targetItem = self:GetItemDataByGid(gid)
            local targetAmmoItemNum = targetItem.num
            local targetAmmoItemNumMax = targetItem.maxStackCount
            if (targetItem.id == ammoItem.ItemId and targetAmmoItemNum < targetAmmoItemNumMax) then
                local newBindPlayerId = 0
                if UInventoryUtil.CheckTwoAmmoCanCombine(ammoItem.BindPlayerId, targetItem.bindPlayerId, character, newBindPlayerId) then
                    local combineNum = math.min(targetAmmoItemNumMax - targetAmmoItemNum, ammoItem.Num)
                    playerAmmoMapping[gid] = playerAmmoMapping[gid] + combineNum
                    ammoItem.Num = ammoItem.Num - combineNum
                    availableNum = availableNum + combineNum

                    if ammoItem.Num <= 0 then
                        break
                    end
                end
            end
        end

        local sourceItemCfg = ItemConfigTool.GetItemConfigById(ammoItem.ItemId)
        if sourceItemCfg ~= nil then
            while(ammoItem.Num > 0 and emptyGrid > 0) do
                local consumeNum = math.min(ammoItem.Num, sourceItemCfg.MaxStackCount)
                ammoItem.Num = ammoItem.Num - consumeNum
                availableNum = availableNum + consumeNum
                emptyGrid = emptyGrid - 1
            end
        else
            logerror("LootingServer:CheckCanUnequipAmmo ItemId =", ammoItem.ItemId, "sourceItemCfg is nil")
        end
    end

    return availableNum
end

function LootingServer:CheckToUnequipAmmoHasEnoughSpace(GunItemGid, ToEquipAmmoGid, ToEquipAmmoNum, ToUnEquipAmmoArray, UnEquipAmmoParamStruct)
    logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace", GunItemGid, #ToEquipAmmoGid, #ToUnEquipAmmoArray,
            UnEquipAmmoParamStruct.bAllowToDropAmmo, UnEquipAmmoParamStruct.TargetQuality)
    if UnEquipAmmoParamStruct.bAllowToDropAmmo then
        return true
    end
    if #ToUnEquipAmmoArray == 0 then
        return true
    end
    local character = Facade.GameFlowManager:GetCharacter()
    if not isvalid(character) then
        logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace unable to find InvMgr")
        return false
    end
    local gunItem = self:GetItemDataByGid(GunItemGid)
    if not gunItem or not gunItem.InSlot then
        logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace unable to find gun item")
        return false
    end
    if gunItem.InSlot.SlotType == ESlotType.NearbyPickups then
        logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace gun is pickup")
        return true
    end
    local itemGid2Item = {}
    ---@return ItemBase
    local function GetItemDataByGid(itemGid)
        if not itemGid then
            return
        end
        if not itemGid2Item[itemGid] then
            itemGid2Item[itemGid] = self:GetItemDataByGid(itemGid)
        end
        return itemGid2Item[itemGid]
    end
    for index = 1, #ToEquipAmmoGid do
        local item = GetItemDataByGid(ToEquipAmmoGid[index])
        if not item or not item.InSlot then
            logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace unable to find item for ammo to equip, gid=", ToEquipAmmoGid[index])
            return false
        end
        if not item:IsSearched() then
            logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace ammo to equip is not searched, gid=", ToEquipAmmoGid[index])
            return false
        end
    end
    for index = 1, #ToUnEquipAmmoArray do
        local itemConfig = ItemConfigTool.GetItemConfigById(ToUnEquipAmmoArray[index].ItemId)
        if not itemConfig then
            logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace ammo to unequip is not valid, gid=", ToUnEquipAmmoArray[index].Guid, "itemId=", ToUnEquipAmmoArray[index].ItemId)
            return false
        end
    end
    local itemSlot2ItemGid = {}
    local itemSlot2LeftCapacity = {}
    local itemGid2Id = {}
    local itemGid2Count = {}
    local itemGid2BindPlayer = {}
    ---@param itemSlot ItemSlot
    local function InitItemSlotDataFunc(itemSlot)
        if itemSlot2ItemGid[itemSlot] then return end
        itemSlot2ItemGid[itemSlot] = {}
        itemSlot2LeftCapacity[itemSlot] = itemSlot:GetTotalCapacity() - itemSlot:GetUsedCapacity()
        for _, item in pairs(itemSlot:GetItems_RefConst()) do
            itemSlot2ItemGid[itemSlot][item.gid] = true
            itemGid2Id[item.gid] = item.id
            itemGid2Count[item.gid] = item.num
            itemGid2BindPlayer[item.gid] = item.bindPlayerId
        end
    end
    for index = 1, #ToEquipAmmoGid do
        local itemGid = ToEquipAmmoGid[index]
        local toEquipNum = ToEquipAmmoNum[index]
        local item = GetItemDataByGid(itemGid)
        InitItemSlotDataFunc(item.InSlot)
        if toEquipNum <= 0 or itemGid2Count[itemGid] <= toEquipNum then
            itemSlot2ItemGid[item.InSlot][item.gid] = nil
        else
            itemGid2Count[itemGid] = itemGid2Count[itemGid] - toEquipNum
        end
    end
    local bResult = true
    for index = 1,#ToUnEquipAmmoArray do
        local ammoItem = ToUnEquipAmmoArray[index]
        local ammoItemId = ammoItem.ItemId
        local itemConfig = ItemConfigTool.GetItemConfigById(ammoItemId)
        local itemMaxNum = math.max(itemConfig.MaxStackCount, 1)
        local itemSlots = {}
        if gunItem.InSlot.SlotType ~= ESlotType.NearbyPickups and not gunItem.InSlot:IsEquipableSlot() then
            table.insert(itemSlots, gunItem.InSlot)
        end
        local NEW_UNEQUIPAMMO_SLOTS_ORDER = {
            ESlotType.Pocket,
            ESlotType.ChestHangingContainer,
            ESlotType.BagContainer,
        }
        local slotOrders = NEW_UNEQUIPAMMO_SLOTS_ORDER
        if gunItem.InSlot:GetSlotGroup() == ESlotGroup.DeadBody then
            for _, slotType in ipairs(slotOrders) do
                local itemSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.DeadBody)
                table.insert(itemSlots, itemSlot)
            end
        end
        local bSafeBoxFirst = itemConfig.Quality > UnEquipAmmoParamStruct.TargetQuality
        if bSafeBoxFirst then
            local itemSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBoxContainer)
            table.insert(itemSlots, itemSlot)
        end
        for _, slotType in ipairs(slotOrders) do
            local itemSlot = Server.InventoryServer:GetSlot(slotType)
            table.insert(itemSlots, itemSlot)
        end
        local currentType = self:GetCurrentInteractorType()
        if currentType == EGamePickupType.NearbyDeadBody then
            for _, slotType in ipairs(slotOrders) do
                local itemSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.DeadBody)
                table.insert(itemSlots, itemSlot)
            end
        elseif currentType == EGamePickupType.SceneBox then
            local itemSlot = Server.InventoryServer:GetSlot(ESlotType.NearbyContainer, ESlotGroup.Nearby)
            table.insert(itemSlots, itemSlot)
        elseif currentType == EGamePickupType.DropContainer then
            local obj = self:GetCurrentInteractingBox()
            for _, spaceData in ipairs(obj.LootBoxRepData.SpaceRepData) do
                local itemSlot = Server.InventoryServer:GetSlot(spaceData.SpaceType, ESlotGroup.Nearby)
                if not itemSlot:IsEquipableSlot() then
                    table.insert(itemSlots, itemSlot)
                end
            end
        end
        itemSlots = table.unique(itemSlots, true)
        local fakeItemGid = 1
        local ammoItemNum = ammoItem.Num
        for _, itemSlot in ipairs(itemSlots) do
            InitItemSlotDataFunc(itemSlot)
            while ammoItemNum > 0 do
                local toCombineItemGid = 0
                local toCombineItemCount = 0
                for itemGid, _ in pairs(itemSlot2ItemGid[itemSlot]) do
                    if itemGid2Id[itemGid] == ammoItemId and itemGid2Count[itemGid] < itemMaxNum and itemGid2Count[itemGid] > toCombineItemCount then
                        local newBindPlayerId = 0
                        if UInventoryUtil.CheckTwoAmmoCanCombine(ammoItem.BindPlayerId, itemGid2BindPlayer[itemGid], character, newBindPlayerId) then
                            toCombineItemGid = itemGid
                            toCombineItemCount = itemGid2Count[itemGid]
                        end
                    end
                end
                if toCombineItemGid == 0 then
                    break
                end
                local combineNum = math.min(itemMaxNum - toCombineItemCount, ammoItemNum)
                if combineNum <= 0 then
                    logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace combineNum <= 0, combineNum=", combineNum, "gid=", ToUnEquipAmmoArray[index].Guid)
                    break
                end
                itemGid2Count[toCombineItemGid] = itemGid2Count[toCombineItemGid] + combineNum
                ammoItemNum = ammoItemNum - combineNum
            end
            while ammoItemNum > 0 and itemSlot2LeftCapacity[itemSlot] > 0 do
                local newItemNum = math.min(itemMaxNum, ammoItemNum)
                if newItemNum <= 0 then
                    logerror("LootingServer:CheckToUnequipAmmoHasEnoughSpace newItemNum <= 0, newItemNum=", newItemNum, "gid=", ToUnEquipAmmoArray[index].Guid)
                    break
                end
                local itemGid = fakeItemGid
                fakeItemGid = fakeItemGid + 1
                itemSlot2ItemGid[itemSlot][itemGid] = true
                itemSlot2LeftCapacity[itemSlot] = itemSlot2LeftCapacity[itemSlot] - 1
                itemGid2Id[itemGid] = ammoItemId
                itemGid2Count[itemGid] = newItemNum
                itemGid2BindPlayer[itemGid] = ammoItem.BindPlayerId
                ammoItemNum = ammoItemNum - newItemNum
            end
            if ammoItemNum <= 0 then
                break
            end
        end
        if ammoItemNum > 0 then
            loginfo("LootingServer:CheckToUnequipAmmoHasEnoughSpace ammoItemNum > 0, ammoItemNum=", ammoItemNum, "gid=", ToUnEquipAmmoArray[index].Guid)
            bResult = false
            break
        end
    end
    return bResult
end

--endregion
-----------------------------------------------------------------------


--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private

function LootingServer:_OnNotifyItemSourceInvalid(itemGID)
    logerror("LootingServer:_OnNotifyItemSourceInvalid", itemGID)

    local itemBase = self:GetItemDataByGid(itemGID)
    if (not itemBase or not itemBase.InSlot) then
        return false
    end

    local oldItemSlot = itemBase.InSlot
    local oldLoc = oldItemSlot:GetItemLocation(itemBase)
    oldItemSlot:RemoveItem(itemBase)

    local itemMoveInfo = {
        item = itemBase,
        OldLoc = oldLoc,
        NewLoc = nil,
        bInternal = false,
        Reason = PropChangeType.Del
    }

    self:AddPendingItemMoveEvent(itemMoveInfo)
end

function LootingServer:_OnClientInventoryManagerInit(inventoryMgr)
    if isvalid(inventoryMgr) then
        inventoryMgr.CheckCanUnequipAmmoDelegate:Bind(self.CheckCanUnequipAmmo, self)
        inventoryMgr.CheckToUnequipAmmoHasEnoughSpaceDelegate:Bind(self.CheckToUnequipAmmoHasEnoughSpace, self)

        -- 局内监听负重阈值变化事件
        local character = inventoryMgr.OwnerCharacter
        if character then
            local roleLoadComp =  character:GetComponentByClass(URoleLoadComponent)
            if isvalid(roleLoadComp) then
                self._onOverloadValueChangeDeleagteHandle = roleLoadComp.OnOverloadValueChange:Add(CreateCPlusCallBack(self._OnRoleLoadChanged, self))
                self._onSuperOverloadValueChangeHandle = roleLoadComp.OnSuperOverloadValueChange:Add(CreateCPlusCallBack(self._OnRoleLoadChanged, self))
            end
        end
    end
end

local function subtract_keys(a, b)
    local result = {}
    for key, _ in pairs(a) do
        if (not b[key]) then
            table.insert(result, key)
        end
    end
    return result
end

local function intersection_keys(a, b)
    local result = {}
    for key, _ in pairs(a) do
        if (b[key]) then
            table.insert(result, key)
        end
    end
    return result
end

---@param oldItemBase ItemBase
---@param newItemInfo FInventoryItemInfo
---@param slotGroup ESlotGroup
function LootingServer:GetChangeType(oldItemBase, newItemInfo, slotGroup, bForRevertPrebehave)
    bForRevertPrebehave = setdefault(bForRevertPrebehave, false)
    if oldItemBase == nil or oldItemBase.InSlot == nil then
        return PropChangeType.Add
    end
    if bForRevertPrebehave then
        -- 因为预表现的关系，位置数据的更新有可能已经提前刷新，因此这里还是需要比较实际的位置信息
        local itemLocation = oldItemBase.InSlot:GetItemLocation(oldItemBase)
        local bPosChagnge = newItemInfo.AttachPosition ~= oldItemBase.InSlot.SlotType
        or (slotGroup ~= nil and slotGroup ~= oldItemBase.InSlot:GetSlotGroup())
        or newItemInfo.ItemSpaceLocation.X ~= itemLocation.X 
        or newItemInfo.ItemSpaceLocation.Y ~= itemLocation.Y
        or newItemInfo.ItemSpaceLocation.GridIndex ~= itemLocation.SubIndex
        or newItemInfo.ItemSpaceLocation.bRotated ~= itemLocation.bRotated
        if bPosChagnge then
            return PropChangeType.Move
        end
        local bNotModified = self:GetItemInfoModifyState(newItemInfo, oldItemBase) == EItemInfoModifyState.NotModified
        if bNotModified then
            return PropChangeType.PropChangeNone
        end
    else
        local bNotModified = self:GetItemInfoModifyState(newItemInfo, oldItemBase) == EItemInfoModifyState.NotModified
        if bNotModified then
            return PropChangeType.PropChangeNone
        end
        -- 因为预表现的关系，位置数据的更新有可能已经提前刷新，因此这里还是需要比较实际的位置信息
        local itemLocation = oldItemBase.InSlot:GetItemLocation(oldItemBase)
        local bPosChagnge = newItemInfo.AttachPosition ~= oldItemBase.InSlot.SlotType
        or (slotGroup ~= nil and slotGroup ~= oldItemBase.InSlot:GetSlotGroup())
        or newItemInfo.ItemSpaceLocation.X ~= itemLocation.X 
        or newItemInfo.ItemSpaceLocation.Y ~= itemLocation.Y
        or newItemInfo.ItemSpaceLocation.GridIndex ~= itemLocation.SubIndex
        or newItemInfo.ItemSpaceLocation.bRotated ~= itemLocation.bRotated
        if bPosChagnge then
            return PropChangeType.Move
        end
    end

    return PropChangeType.Modify
end

function LootingServer:_diff(itemBaseDict, itemInfoDict, groupId, bForRevertPrebehave)
    bForRevertPrebehave = setdefault(bForRevertPrebehave, false)
    --add a - b
    local addItems = subtract_keys(itemInfoDict, itemBaseDict)
    --remove b - a
    local removeItems = subtract_keys(itemBaseDict, itemInfoDict)
    --modify a & b
    local intersectionItems = intersection_keys(itemInfoDict, itemBaseDict)

    local dataChanges = {}

    for _, itemGid in ipairs(addItems) do
        local itemData = self:GetItemDataByGid(itemGid)
        local itemInfo = itemInfoDict[itemGid]
        ---@type PropChange
        local dataChange = self:_ObtainPropChange(PropChangeType.Add, itemData, itemInfo, groupId)
        table.insert(dataChanges, dataChange)
    end

    for _, itemGid in ipairs(removeItems) do
        local itemData = itemBaseDict[itemGid]
        ---@type PropChange
        local dataChange = self:_ObtainPropChange(PropChangeType.Del, itemData, nil, groupId)
        table.insert(dataChanges, dataChange)
    end

    for _, itemGid in ipairs(intersectionItems) do
        local itemData = itemBaseDict[itemGid]
        local itemInfo = itemInfoDict[itemGid]

        local changeType = self:GetChangeType(itemData, itemInfo, groupId, bForRevertPrebehave)
        if (changeType ~= PropChangeType.PropChangeNone) then
            ---@type PropChange
            local dataChange = self:_ObtainPropChange(changeType, itemData, itemInfo, groupId)
            table.insert(dataChanges, dataChange)
        end
    end

    return dataChanges
end

function LootingServer:_ProcessPropChange_Delete(propChange, cacheItemsInfo)
    local itemBase = propChange.OldItemData

    if (not itemBase or not itemBase.InSlot) then
        return false
    end

    local oldItemSlot = itemBase.InSlot
    local oldLoc = oldItemSlot:GetItemLocation(itemBase)
    oldItemSlot:RemoveItem(itemBase)

    local itemMoveInfo = {
        item = itemBase,
        OldLoc = oldLoc,
        NewLoc = nil,
        bInternal = false,
        Reason = PropChangeType.Del
    }

    table.insert(cacheItemsInfo, itemMoveInfo)
end

function LootingServer:_ProcessPropChange_Add(propChange, cacheItemsInfo)
    local itemInfo = propChange.NewItemInfo
    local pos = itemInfo.AttachPosition

    local groupId = propChange.group
    ---@type ItemSlot
    local newItemSlot = Server.InventoryServer:GetSlot(pos, groupId)

    local bNewPickup = Server.InventoryServer:GetItemByGid(itemInfo.ItemGid, self:GetSelfSlotGroup()) == nil
    local newItem, newLoc = self:CreateItemData(itemInfo, newItemSlot)
    if newItem and bNewPickup then
        newItem:SetLocalPickupFrame(LuaTickController:Get():GetFrameIndex())
    end

    if newItem then
        local itemMoveInfo = {
            item = newItem,
            OldLoc = nil,
            NewLoc = newLoc,
            bInternal = false,
            Reason = PropChangeType.Add
        }
        table.insert(cacheItemsInfo, itemMoveInfo)
    end
end

function LootingServer:_ProcessPropChange_Modify(propChange, cacheItemsInfo)
    local itemData = propChange.OldItemData
    local newItemInfo = propChange.NewItemInfo
    itemData:SetRawCppInfo(newItemInfo)
    if not itemData.InSlot then
        return
    end
    local newLoc = itemData.InSlot:GetItemLocation(itemData)

    local itemMoveInfo = {
        item = itemData,
        OldLoc = nil,
        NewLoc = newLoc,
        bInternal = false,
        bIncreaseOrDecrease = propChange.bIncreaseOrDecrease,
        Reason = PropChangeType.Modify
    }
    -- CacheItemsInfo[Item] = ItemMoveInfo
    table.insert(cacheItemsInfo, itemMoveInfo)
end


function LootingServer:_IncrementalUpdateItems(itemBaseDict, newItemInfoDict, groupID, spaceRepData)
    log("LootingServer:IncrementalUpdateItems")
    local cacheCapacity = {}
    local cacheLength = {}
    local cacheHeight = {}
    local targetGroups = {self:GetSelfSlotGroup(), ESlotGroup.DeadBody, ESlotGroup.Nearby}
    local targetSlots = {
        ESlotType.BagSpaceContainer,
        ESlotType.SafeBoxContainer,
        ESlotType.KeyChainContainer,
    }

    for _, targetGroup in ipairs(targetGroups) do
        for _, slotType in ipairs(targetSlots) do
            local slot = Server.InventoryServer:GetSlot(slotType, targetGroup)

            cacheCapacity[slot] = 0
            cacheLength[slot] = slot.Length
            cacheHeight[slot] = slot.Width
            for _, item in ipairs(slot:GetItems_RefConst()) do
                cacheCapacity[slot] = cacheCapacity[slot] + item.num
            end
        end
    end


    local dataChanges = self:_diff(itemBaseDict, newItemInfoDict, groupID)

    --logerror("Num of DataChange: ", #DataChanges)
    self:_InitTargetSpace(spaceRepData, groupID)
    -- 如果使用了ItemArray的回调去同步道具，那么dataChanges都应该在Tick里集中处理
    -- 避免因为部分走的这里部分走的Tick导致的增删顺序问题
    if self:IsUsingFastArrayReplication() then
        if #dataChanges > 0 then
            table.append(self._propChangeList, dataChanges)
            LuaTickController:Get():RegisterTick(self)
        end
    else
        self:_ProcessPropChangeList(dataChanges)

        for _, dataChange in ipairs(dataChanges) do
            self:_FreePropChange(dataChange)
        end
    end
    -- for _, moveItemInfo in pairs(cacheItemsInfo) do

    --     --logerror("MoveItemInfo: ", MoveItemInfo.Reason, MoveItemInfo.item.Name)
    --     -- self.Events.evtLootingItemMove:Invoke(moveItemInfo)

    --     ---@type ItemBase
    --     local item = moveItemInfo.item
    --     local oldLoc = moveItemInfo.OldLoc
    --     local newLoc = moveItemInfo.NewLoc
    --     local oldType = oldLoc and oldLoc.ItemSlot.SlotType
    --     local newType = newLoc and newLoc.ItemSlot.SlotType

    --     if oldLoc then
    --         if oldType == ESlotType.Bag or oldType == ESlotType.ChestHanging then

    --         elseif oldType == ESlotType.KeyChain or oldType == ESlotType.SafeBox then
    --             local refContSlot = Server.InventoryServer:GetRefContainerSlot(oldLoc.ItemSlot)
    --             refContSlot:InitSlotSize()
    --             Server.InventoryServer.Events.evtSlotSpaceChange:Invoke(refContSlot)
    --         end
    --     end
    --     if newLoc and newLoc.ItemSlot:IsEquipContainerSlot() then
    --         if newType == ESlotType.Bag or newType == ESlotType.ChestHanging then

    --         elseif newType == ESlotType.KeyChain or newType == ESlotType.SafeBox then
    --             local refContSlot = Server.InventoryServer:GetRefContainerSlot(newLoc.ItemSlot)
    --             if refContSlot.SlotType == ESlotType.KeyChainContainer then
    --                 refContSlot:InitKeyChainStructFromItemInfo(item:GetRawCppInfo())
    --             else
    --                 refContSlot:InitSlotSize(item.id)
    --             end
                
    --             Server.InventoryServer.Events.evtSlotSpaceChange:Invoke(refContSlot)
    --         end
    --     end
    -- end

    for _, targetGroup in ipairs(targetGroups) do
        for _, slotType in ipairs(targetSlots) do
            local tmp = 0
            
            local slot = Server.InventoryServer:GetSlot(slotType, targetGroup)
            local length = slot.Length
            local height = slot.Width
            for _, item in ipairs(slot:GetItems_RefConst()) do
                tmp = tmp + item.num
            end
    
            if tmp ~= cacheCapacity[slot] or length ~= cacheLength[slot] or height ~= cacheHeight[slot] then
                Server.InventoryServer.Events.evtUpdateItemCapacity:Invoke(slot, tmp > cacheCapacity[slot])
            end
        end
    end
    -- 局内looting计算增量负重
    Server.InventoryServer:CalculateLootingAllCarryItemsWeight()
end

--- 预处理propChangeList，是因为：
--- 1. 弱网条件下有概率出现单帧内同一物品在同一SlotGroup中触发多次PropChange，容易出现错误
--- 例如：先Add再Remove，会被处理为Add；先Modify再Remove，会被处理为Remove
--- 因此，需要仅保留该物品在每个SlotGroup中最后收到的PropChange（依赖同一Object上同一属性多次同步下来是保序的）
--- 2. 如果同一物品在多个SlotGroup中都有PropChange，也会发生错误
--- 例如：在Deadbody上Remove，又在Player上Modify，会被处理为Remove
--- 因此，需要仅处理该物品ModifyTimeStamp最大的PropChange（因为不同Object之间的同步顺序可能错乱，但ModifyTimeStamp是单增的）
function LootingServer:_PreProcessPropChangeList()
    for _, propChange in ipairs(self._propChangeList) do
        self:_PrintPropChange(propChange, "LootingServer:_PreProcessPropChangeList [before]")
    end
    --- 1. 保留单帧内同一物品在同一SlotGroup中的最后一个PropChange
    ---@type table<number,table<number,PropChange>>
    local propChangeGroupMap = {}
    for _, propChange in ipairs(self._propChangeList) do
        if not propChangeGroupMap[propChange.group] then
            propChangeGroupMap[propChange.group] = {}
        end
        local itemGid = (propChange.NewItemInfo and propChange.NewItemInfo.ItemGid) or (propChange.OldItemData and propChange.OldItemData.gid)
        if itemGid then
            propChangeGroupMap[propChange.group][itemGid] = propChange
        end
    end
    --- 2. 如果单帧内同一物品在多个SlotGroup中都有PropChange，使用ModifyTimeStamp大的PropChange
    ---@type table<number,PropChange>
    local propChangeMap = {}
    for _, propChangeGroup in pairs(propChangeGroupMap) do
        for _, propChange in pairs(propChangeGroup) do
            local itemGid = (propChange.NewItemInfo and propChange.NewItemInfo.ItemGid) or (propChange.OldItemData and propChange.OldItemData.gid)
            if itemGid then
                if not propChangeMap[itemGid] then
                    propChangeMap[itemGid] = propChange
                else
                    local NewModifyTimeStamp = (propChange.NewItemInfo and propChange.NewItemInfo.ModifyTimeStamp)
                            or (propChange.OldItemData and propChange.OldItemData.modifyTimeStamp)
                    local OldModifyTimeStamp = (propChangeMap[itemGid].NewItemInfo and propChangeMap[itemGid].NewItemInfo.ModifyTimeStamp)
                            or (propChangeMap[itemGid].OldItemData and propChangeMap[itemGid].OldItemData.modifyTimeStamp)
                    if OldModifyTimeStamp and NewModifyTimeStamp and NewModifyTimeStamp > OldModifyTimeStamp then
                        propChangeMap[itemGid] = propChange
                    end
                end
            end
        end
    end
    --- 3. 把筛选到propChangeMap的PropChange还原到propChangeList，注意要删除的PropChange应当Free
    for i = 1, #self._propChangeList do
        local itemGid = (self._propChangeList[i].OldItemData and self._propChangeList[i].OldItemData.gid)
                or (self._propChangeList[i].NewItemInfo and self._propChangeList[i].NewItemInfo.ItemGid)
        if itemGid and propChangeMap[itemGid] ~= self._propChangeList[i] then
            self:_FreePropChange(self._propChangeList[i])
            self._propChangeList[i] = nil
        end
    end
    self._propChangeList = table.values(propChangeMap)
    for _, propChange in ipairs(self._propChangeList) do
        self:_PrintPropChange(propChange, "LootingServer:_PreProcessPropChangeList [after]")
    end
end

---@param propChangeList PropChange[]
function LootingServer:_ProcessPropChangeList(propChangeList)
    local cacheItemsInfo = {}
    for i, propChange in ipairs(propChangeList) do
        if propChange.change_type == PropChangeType.Del then
            self:_ProcessPropChange_Delete(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Move then
            self:_ProcessPropChange_Delete(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Add then
            self:_ProcessPropChange_Delete(propChange, cacheItemsInfo)
        end
    end

    for i, propChange in ipairs(propChangeList) do
        if propChange.change_type == PropChangeType.Modify then
            self:_ProcessPropChange_Modify(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Move then
            self:_ProcessPropChange_Add(propChange, cacheItemsInfo)
        elseif propChange.change_type == PropChangeType.Add then
            self:_ProcessPropChange_Add(propChange, cacheItemsInfo)
        end
    end

    self:AddPendingItemMoveEvents(cacheItemsInfo)
end

function LootingServer:_OnInventoryClientInitialize(invMgr)
    self:_OnClientInventoryManagerInit(invMgr)
    if not self:CheckServerEnabled() then
        return
    end
    logerror("_OnInventoryClientInitialize", invMgr)

    self:ResetLootingServerData()
    self._cachedData.InvMgr = invMgr

    if self.DELAY_FETCH_PLAYER_ITEMS then
        LuaTickController:Get():RegisterTick(self)
    else
        self:FetchPlayerItems()
    end
    self:_TrySendNightVisionInfo()
end

function LootingServer:_OnInventoryItemListChanged(bInitialize)
    if not self:CheckServerEnabled() then
        return
    end

    log("_OnInventoryItemListChanged", bInitialize and "true" or "false")
    if bInitialize then
        return
    elseif self.DELAY_DATA_UPDATE or InGameController:Get():IsIntro() then
        if not self:IsUsingFastArrayReplication() then
            if self.introDelayHandle == 0 then
                self.introDelayHandle = Facade.LuaFramingManager:RegisterFrameTask(self.IncrementalFetchPlayerItems, self)
                -- Timer.DelayCall(0.1, self.IncrementalFetchPlayerItems, self)
            end
        end
    else
        if not self:IsUsingFastArrayReplication() then
            self:IncrementalFetchPlayerItems()
        else
            --- 更新空间数据和负重还是得靠OnRepData
            local inventoryMgr = self:GetCharacterInventoryMgr()
            if isvalid(inventoryMgr) then
                self:_InitTargetSpace(inventoryMgr.RepData.SpaceRepData, self:GetSelfSlotGroup())
            end
            -- 局内looting计算增量负重
            Server.InventoryServer:CalculateLootingAllCarryItemsWeight()
        end
    end
end

function LootingServer:IncrementalFetchPlayerItems()
    local inventoryMgr = self:GetCharacterInventoryMgr()
    if not isvalid(inventoryMgr) then
        logwarning("LootingServer:IncrementalFetchPlayerItems inventoryMgr is not valid")
        return
    end

    local inventoryItems = {}
    inventoryItems = inventoryMgr:GetAllInventory(inventoryItems, false)

    -- <uint64, FInventoryItemInfo>
    local newItemInfoDict = {}
    for _, itemInfo in pairs(inventoryItems) do
        -- body
        local gid = itemInfo.ItemGid
        newItemInfoDict[gid] = itemInfo
    end

    -- <uint64, ItemBase>
    local itemBaseDict = {}
    for _, slotType in pairs(self:GetInGameSlotTypes()) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType, self:GetSelfSlotGroup())
        for _, item in pairs(itemSlot:GetItems_RefConst()) do
            itemBaseDict[item.gid] = item
        end
    end

    local spaceRepData = inventoryMgr.RepData.SpaceRepData
    self:_IncrementalUpdateItems(itemBaseDict, newItemInfoDict, self:GetSelfSlotGroup(), spaceRepData)

    self.introDelayHandle = 0
end

function LootingServer:_OnClientPickupChanged(itemChangeType, gid, pickup)
    if not self:CheckServerEnabled() then
        return
    end

    if gid == 0 then
        return
    end

    log("_OnClientPickupChanged", itemChangeType, gid, pickup)

    local currentType = self:GetCurrentInteractorType()
    if currentType ~= EGamePickupType.NearbyPickups then
        log("_OnClientPickupChanged discard if not in NearbyPickups view.")
        return
    end

    local item = self:GetItemDataByGid(gid)
    local bCurrentItemIsNearbyPickup = false
    if item and item.InSlot and item.InSlot.SlotType == ESlotType.NearbyPickups then
        bCurrentItemIsNearbyPickup = true
    end
    local nearbyPickupSlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    local itemMoveInfo = {
        bInternal = false
    }

    local cacheItemsInfo = {}
    if itemChangeType == EItemChangeType.Delete then
        if bCurrentItemIsNearbyPickup then
            self._nearbyPickupMapping[gid] = nil

            itemMoveInfo.item = item
            itemMoveInfo.OldLoc = nearbyPickupSlot:GetItemLocation(item)
            itemMoveInfo.Reason = PropChangeType.Del
            nearbyPickupSlot:RemoveItem(item)
            table.insert(cacheItemsInfo, itemMoveInfo)
        end
    elseif itemChangeType == EItemChangeType.Add then
        if not bCurrentItemIsNearbyPickup then
            self:_PreProcessNearbySlot()
            self._nearbyPickupMapping[gid] = pickup

            local itemInfo = pickup:GetPickupItemInfo()

            ---@type PropChange
            local propChange = self:_ObtainPropChange(PropChangeType.Add, item, itemInfo, ESlotGroup.Nearby)
            self:_ProcessPropChange_Delete(propChange, cacheItemsInfo)
            self:_ProcessPropChange_Add(propChange, cacheItemsInfo)
            self:_FreePropChange(propChange)

            self:_PostProcessNearbySlot()
        end
    elseif itemChangeType == EItemChangeType.Modified then
        if bCurrentItemIsNearbyPickup then
            self:_PreProcessNearbySlot()
            self._nearbyPickupMapping[gid] = pickup

            local itemInfo = pickup:GetPickupItemInfo()
            item:SetRawCppInfo(itemInfo)
            itemMoveInfo.item = item
            itemMoveInfo.NewLoc = nearbyPickupSlot:GetItemLocation(item)
            itemMoveInfo.Reason = PropChangeType.Modify
            table.insert(cacheItemsInfo, itemMoveInfo)

            self:_PostProcessNearbySlot()
        end
    end

    if #cacheItemsInfo > 0 then
        log("_OnClientPickupChanged do refresh", itemChangeType, gid, pickup)

        self:AddPendingItemMoveEvents(cacheItemsInfo)
    end
end

function LootingServer:_OnGameCurrentPotentialInteractorsChange(pickup)
    if not isvalid(pickup) then
        return
    end
    if self:GetCurrentInteractorType() == nil then
        local itemInfo = pickup:GetPickupItemInfo()
        local gid = itemInfo.ItemGid
        local item = self:GetItemDataByGid(gid)
        if item and item.InSlot and item.InSlot.SlotType == ESlotType.NearbyPickups then
            local changeType = self:GetChangeType(item, itemInfo, ESlotGroup.Nearby)
            if changeType ~= PropChangeType.PropChangeNone then
                local propChange = self:_ObtainPropChange(changeType, item, itemInfo, ESlotGroup.Nearby)
                self:_ProcessPropChangeList({propChange})
                self:_FreePropChange(propChange)
            end
        end
    end
end

-----------------------------------------------------------------------

function LootingServer:_OnNearbyPickupsDirty(nearPickups)
    local newItemInfoDict = {}
    self._nearbyPickupMapping = {}
    for _, pickup in pairs(nearPickups) do
        if isvalid(pickup) then
            local itemInfo = pickup:GetPickupItemInfo()
            local gid = itemInfo.ItemGid
            
            newItemInfoDict[gid] = itemInfo
    
            self._nearbyPickupMapping[gid] = pickup
        end
    end

    --<uint64,ItemBase>
    local itemBaseDict = {}
    local loopTarget = {ESlotType.NearbyPickups}

    for _, slotType in ipairs(loopTarget) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Nearby)
        for _, item in pairs(itemSlot:GetItems_RefConst()) do
            itemBaseDict[item.gid] = item
        end
    end

    self:_PreProcessNearbySlot()

    self:_IncrementalUpdateItems(itemBaseDict, newItemInfoDict, ESlotGroup.Nearby, nil)

    self:_PostProcessNearbySlot()
end

function LootingServer:_OnPickupContainerDirty(container, groupId)
    local itemInfoArray = {}
    itemInfoArray = container:GetSubPickups(itemInfoArray)

    if self.USE_DELAY_RELOAD_CONTAINER then
        if container.InventoryGID ~= nil then
            local itemInfoSize = ULootingGlobalDefine.FInventoryItemInfoSize
            local itemInfoMaxNum = math.floor(UHardwareParamHelper.GetConsoleVariableRefIntValue("net.MaxRepArrayMemory") / itemInfoSize)
            if #itemInfoArray >= itemInfoMaxNum then
                self._needReloadContainerGid = container.InventoryGID
            end
        end
    end

    local newItemInfoDict = {}
    for _, itemInfo in pairs(itemInfoArray) do
        -- body
        local gid = itemInfo.ItemGid

        newItemInfoDict[gid] = itemInfo
    end

    --<uint64,ItemBase>
    local itemBaseDict = {}
    local loopTarget
    if groupId == ESlotGroup.DeadBody then
        loopTarget = DeadbodySlotTypes
    elseif groupId == ESlotGroup.Nearby then
        loopTarget = NearbySlotTypes
    else
        return
    end

    for _, slotType in ipairs(loopTarget) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType, groupId)
        for _, item in pairs(itemSlot:GetItems_RefConst()) do
            itemBaseDict[item.gid] = item
        end
    end

    self:_IncrementalUpdateItems(itemBaseDict, newItemInfoDict, groupId, container.LootBoxRepData.SpaceRepData)
end

function LootingServer:_OnPickupContainerSubPickupsChanged(pickupContainerGid, bOnlySpace)
    if not self:CheckServerEnabled() then
        return
    end

    local box = self:GetCurrentInteractingBox()
    if not isvalid(box) then return end
    if box.InventoryGID ~= pickupContainerGid then return end
    
    local container = self:GetCurrentInteractingBox()
    if isvalid(container) then
        local slotGroup = self:GetSlotGroupByLootObj(container)
        self:_InitTargetSpace(container.LootBoxRepData.SpaceRepData, slotGroup)
    end

    if bOnlySpace or (self:IsUsingFastArrayReplication() and not self.USE_RPC_REPLICATION) then
        --- 正常不应该走进来
        return
    end

    local interactorType = self:GetCurrentInteractorType()
    if interactorType == EGamePickupType.SceneBox then
        self:_OnPickupContainerDirty(box, ESlotGroup.Nearby)
        self.Events.evtBoxDataChange:Invoke(box)
    elseif interactorType == EGamePickupType.DropContainer then
        self:_OnPickupContainerDirty(box, ESlotGroup.Nearby)
        self.Events.evtBoxDataChange:Invoke(box)
    elseif interactorType == EGamePickupType.NearbyDeadBody then
        self:_OnPickupContainerDirty(box, ESlotGroup.DeadBody)

        self.Events.evtBoxDataChange:Invoke(box)
    end
end

local allRefSlotTypes_RepUseItemInfo = table.append({}, PlayerSlotTypes)
table.append(allRefSlotTypes_RepUseItemInfo, NearbySlotTypes)
allRefSlotTypes_RepUseItemInfo = table.unique(allRefSlotTypes_RepUseItemInfo, true)

function LootingServer:_OnRepUseItemInfo(preUseItemInfo)
    if not self:CheckServerEnabled() then
        return
    end

    local targetGroups = {self:GetSelfSlotGroup(), ESlotGroup.DeadBody, ESlotGroup.Nearby}
    local useItemGids = preUseItemInfo.UseItemGids
    for _, targetGroup in ipairs(targetGroups) do
        for _, slotType in pairs(allRefSlotTypes_RepUseItemInfo) do
            local slot = Server.InventoryServer:GetSlot(slotType, targetGroup)
            local items = slot:GetItems_RefConst()

            for _, item in ipairs(items) do
                for key, itemGid in pairs(useItemGids) do
                    if item.gid == itemGid then
                        item:SetItemUseInfo(preUseItemInfo)
                    end
                end
                
            end
        end
    end
end

---@param srcItem ItemBase
---@param tarItem ItemBase
function LootingServer:_CheckSwitchBagWarning(srcItem, tarItem, moveCmd)
    if moveCmd.bHasCheckedBagSwitch then
        return false
    end
    moveCmd.bHasCheckedBagSwitch = true

    local srcSlot = srcItem.InSlot
    local tarSlot = tarItem.InSlot

    if not srcSlot or not tarSlot then
        return false
    end

    if srcSlot.SlotType ~= tarSlot.SlotType or not srcSlot:IsEquipContainerSlot() then
        return false
    end

    local srcGroup = srcSlot:GetSlotGroup()
    local tarGroup = tarSlot:GetSlotGroup()

    local myContCapacity = 0
    local otherContCapacity = 0
    if srcGroup == self:GetSelfSlotGroup() then
        myContCapacity = Server.LootingServer.GetGainEquipCapacityFunc(tarItem)
        otherContCapacity = Server.LootingServer.GetGainEquipCapacityFunc(srcItem)
    elseif tarGroup == self:GetSelfSlotGroup() then
        myContCapacity = Server.LootingServer.GetGainEquipCapacityFunc(srcItem)
        otherContCapacity = Server.LootingServer.GetGainEquipCapacityFunc(tarItem)
    end

    if myContCapacity < otherContCapacity then
        Server.LootingServer.Events.evtSmallBag2BigBag:Invoke(moveCmd)

        return true
    end

    return false
end

LootingServer.GetGainEquipCapacityFunc = function(item)
    local product = 0
    local itemFeature = item:GetFeature()
    if itemFeature and itemFeature.GetGainEquipCapacity then
        product = itemFeature:GetGainEquipCapacity()
    end
    return product
end

--endregion
-----------------------------------------------------------------------

local inGameSlotTypes = {}
local inGameSlotTypes_MP = {}

for k, v in pairs(ESlotType) do
    if (v > ESlotType.EquipmentStart and v < ESlotType.EquipmentEnd)
    or (v > ESlotType.ContainerStart and v < ESlotType.ContainerEnd)
    -- or v == ESlotType.BulletLeft
    -- or v == ESlotType.BulletRight 
    then
        table.insert(inGameSlotTypes, v)
        table.insert(inGameSlotTypes_MP, v)
    elseif v > ESlotType.MP_Begin and v < ESlotType.MP_End then
        table.insert(inGameSlotTypes_MP, v)
    end
end

function LootingServer:GetInGameSlotTypes()
    if InGameController:Get():IsMPMode() then
        return inGameSlotTypes_MP
    else
        return inGameSlotTypes
    end
end

function LootingServer:GetSlotTypesByGroup(group, gamePickupType)
    if group == self:GetSelfSlotGroup() then
        return self:GetInGameSlotTypes()
    elseif group == ESlotGroup.DeadBody then
        return DeadbodySlotTypes
    elseif group == ESlotGroup.Nearby then
        if gamePickupType == EGamePickupType.SceneBox then
            return {ESlotType.NearbyContainer}
        elseif gamePickupType == EGamePickupType.DropContainer then
            return {ESlotType.ChestHanging, ESlotType.Bag, ESlotType.ChestHangingContainer, ESlotType.BagContainer}
        elseif gamePickupType == EGamePickupType.NearbyPickups then
            return {ESlotType.NearbyPickups}
        end
    end
    return {}
end

local visibleSlotTypes = {
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.MeleeWeapon,
    ESlotType.Pistrol,

    ESlotType.Helmet,
    ESlotType.BreastPlate,
    ESlotType.ChestHanging,
    ESlotType.Bag,

    -- ESlotType.KeyChain,
    -- ESlotType.SafeBox,
    -- ESlotType.BagSpaceContainer,

    ESlotType.BagContainer,
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.SafeBoxContainer,
    ESlotType.KeyChainContainer
}

function LootingServer:GetVisibleSlotTypes()
    return visibleSlotTypes
end


function LootingServer:GetSelfSlotGroup()
    if ItemOperaTool.bIsInCollectionRoom then
        return ESlotGroup.CollectionRoom
    end
    return ESlotGroup.Player
end


function LootingServer:_InitTargetSpace(spaceRepData, slotGroup)
    for _, spaceData in ipairs(spaceRepData) do
        local pos = spaceData.SpaceType
        local slot = Server.InventoryServer:GetSlot(pos, slotGroup)
        if slot.serverUpdateCount ~= spaceData.ServerUpdateCount then
            loginfo("LootingServer:_InitTargetSpace", slotGroup, pos)
            slot.serverUpdateCount = spaceData.ServerUpdateCount
            slot:InitSpaceBySpaceRepData(spaceData)

            Server.InventoryServer.Events.evtSlotSpaceChange:Invoke(slot)
        end
    end
end

function LootingServer:_HideSlot(slot)
    slot:InitSingleSpaceStructure(0, 0)
    slot.serverUpdateCount = 0
end

local oldHeight = 0

function LootingServer:_PreProcessNearbySlot()
    local nearbySlotData = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    oldHeight = nearbySlotData.Width
    nearbySlotData:ManualySetSlotSize(nearbySlotData.Length, self.NEARBY_PICKUP_MAX_HEIGHT)
end

function LootingServer:_PostProcessNearbySlot()
    local nearbySlotData = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    local bottom = nearbySlotData:GetBottomOfAllItems()    -- bottom start form 0
    local targetHeight = bottom + self.NEARBY_PICKUP_HEIGHT_COMPLEMENTATION + 1
    targetHeight = math.max(targetHeight, self.NEARBY_PICKUP_MIN_HEIGHT)
    if nearbySlotData.Width >= self.NEARBY_PICKUP_MAX_HEIGHT or targetHeight > nearbySlotData.Width then
        nearbySlotData:ManualySetSlotSize(nearbySlotData.Length, targetHeight)

        if oldHeight ~= targetHeight then
            oldHeight = targetHeight

            Server.InventoryServer.Events.evtSlotSpaceChange:Invoke(nearbySlotData)
        end
    end
end

-----------------------------------------------------------------------
--region rep logic

-- function LootingServer:_GetSlotGroupByLootObj(lootObj)
--     return self:GetSelfSlotGroup()
-- end

function LootingServer:_InitConfigForItemInfo(itemInfo)
    itemInfo:InitConfig()
    itemInfo:RefreshHealthPackageInfo(false)
end

function LootingServer:_CreatePropChange()
    local propChange = table.create(8)
    return propChange
end

---@param oldItemData ItemBase
---@return PropChange
function LootingServer:_ObtainPropChange(changeType, oldItemData, itemInfo, slotGroup)
    ---@type PropChange
    local propChange = self._propChangePool:Obtain()
    propChange.change_type = changeType
    propChange.OldItemData = oldItemData
    propChange.NewItemInfo = itemInfo
    propChange.group = slotGroup
    if oldItemData and itemInfo then
        if itemInfo:GetCount() > oldItemData.num then
            propChange.bIncreaseOrDecrease = true
        elseif itemInfo:GetCount() < oldItemData.num then
            propChange.bIncreaseOrDecrease = false
        end
    end
    return propChange
end

function LootingServer:_FreePropChange(propChange)
    self._propChangePool:Free(propChange)
end

---@param propChange PropChange
function LootingServer:_AddPropChangeToList(propChange)
    if propChange then
        self:_PrintPropChange(propChange, "LootingServer:_AddPropChangeToList")
        table.insert(self._propChangeList, propChange)
    end
end

function LootingServer:_PrintPropChange(propChange, prefix)
    if propChange.NewItemInfo then
        local itemName = tostring(ItemConfigTool.GetItemName(tonumber(propChange.NewItemInfo:GetIdName())))
        loginfo(prefix, PropChangeTypeToName(propChange.change_type), itemName, propChange.NewItemInfo.ItemGid or 0)
    elseif propChange.OldItemData ~= nil then
        loginfo(prefix, PropChangeTypeToName(propChange.change_type), propChange.OldItemData.name or "none", propChange.OldItemData.gid or 0)
    else
        loginfo(prefix, PropChangeTypeToName(propChange.change_type))
    end
end

function LootingServer:_OnItemArrayPostReplicatedAdd(lootObj, itemInfo)
    if not self:IsUsingFastArrayReplication() then
        return
    end
    local itemName = tostring(ItemConfigTool.GetItemName(tonumber(itemInfo:GetIdName())))
    loginfo("LootingServer:_OnItemArrayPostReplicatedAdd", lootObj, itemName, itemInfo.ItemGid)
    if not self:CheckServerEnabled() then
        logwarning("_OnItemArrayPostReplicatedAdd blocked because sever not enabled")
        return
    end
    if not isvalid(lootObj) then
        logwarning("_OnItemArrayPostReplicatedAdd lootObj is invalid")
        return
    end
    local slotGroup = self:GetSlotGroupByLootObj(lootObj)
    if slotGroup == ESlotGroup.None then
        logwarning("_OnItemArrayPostReplicatedAdd fail to find slotGroup for lootObj")
        return
    end
    if slotGroup == self:GetSelfSlotGroup() then
        if not self._bHasFetchedPlayerItems then
            logwarning("_OnItemArrayPostReplicatedAdd has not ever fetched player items")
            return
        end
    end
    local oldItemData = self:GetItemDataByGid(itemInfo.ItemGid)
    if oldItemData and oldItemData.modifyTimeStamp > itemInfo.ModifyTimeStamp then
        logwarning("_OnItemArrayPostReplicatedAdd oldItemData is more advanced than itemInfo")
        return
    end
    self:_InitConfigForItemInfo(itemInfo)
    local changeType = self:GetChangeType(oldItemData, itemInfo, slotGroup)
    ---@type PropChange
    local propChange = self:_ObtainPropChange(changeType, oldItemData, itemInfo, slotGroup)
    self:_AddPropChangeToList(propChange)
    LuaTickController:Get():RegisterTick(self)
    if self.USE_DELAY_RELOAD_CONTAINER then
        -- 同一个Actor的Reliable RPC是保序的，因而第二批道具肯定是接收到全量后的首次接收到增量
        if self._needReloadContainerGid and lootObj.InventoryGID and lootObj.InventoryGID == self._needReloadContainerGid then
            self._needReloadContainerGid = nil
            self:MarkNeedReloadAllItems()
        end
    end
end

function LootingServer:_OnItemArrayPreReplicatedRemove(lootObj, itemInfo)
    if not self:IsUsingFastArrayReplication() then
        return
    end
    local itemName = tostring(ItemConfigTool.GetItemName(tonumber(itemInfo:GetIdName())))
    loginfo("LootingServer:_OnItemArrayPreReplicatedRemove", lootObj, itemName, itemInfo.ItemGid)
    if not self:CheckServerEnabled() then
        logwarning("_OnItemArrayPreReplicatedRemove blocked because sever not enabled")
        return
    end
    if not isvalid(lootObj) then
        logwarning("_OnItemArrayPreReplicatedRemove lootObj is invalid")
        return
    end
    local slotGroup = self:GetSlotGroupByLootObj(lootObj)
    if slotGroup == ESlotGroup.None then
        logwarning("_OnItemArrayPreReplicatedRemove fail to find slotGroup for lootObj")
        return
    end
    if slotGroup == self:GetSelfSlotGroup() then
        if not self._bHasFetchedPlayerItems then
            logwarning("_OnItemArrayPreReplicatedRemove has not ever fetched player items")
            return
        end
    end
    local bLootObjSlotHasItem = false
    for _, slot in pairs(Server.InventoryServer:GetAllSlots(slotGroup)) do
        if slot and slot:HasItemByGid(itemInfo.ItemGid) then
            bLootObjSlotHasItem = true
            break
        end
    end
    if not bLootObjSlotHasItem then
        loginfo("_OnItemArrayPreReplicatedRemove lootObj has no this item")
        return
    end
    local oldItemData = self:GetItemDataByGid(itemInfo.ItemGid)
    if oldItemData and oldItemData.modifyTimeStamp > itemInfo.ModifyTimeStamp then
        logwarning("_OnItemArrayPreReplicatedRemove oldItemData is more advanced than itemInfo")
        return
    end
    self:_InitConfigForItemInfo(itemInfo)
    ---@type PropChange
    local propChange = self:_ObtainPropChange(PropChangeType.Del, self:GetItemDataByGid(itemInfo.ItemGid), nil, slotGroup)
    self:_AddPropChangeToList(propChange)
    LuaTickController:Get():RegisterTick(self)
end

function LootingServer:_OnItemArrayPostReplicatedChange(lootObj, itemInfo)
    if not self:IsUsingFastArrayReplication() then
        return
    end
    local itemName = tostring(ItemConfigTool.GetItemName(tonumber(itemInfo:GetIdName())))
    loginfo("LootingServer:_OnItemArrayPostReplicatedChange", lootObj, itemName, itemInfo.ItemGid)
    if not self:CheckServerEnabled() then
        logwarning("_OnItemArrayPostReplicatedChange blocked because sever not enabled")
        return
    end
    if not isvalid(lootObj) then
        logwarning("_OnItemArrayPostReplicatedChange lootObj is invalid")
        return
    end
    local slotGroup = self:GetSlotGroupByLootObj(lootObj)
    if slotGroup == ESlotGroup.None then
        logwarning("_OnItemArrayPostReplicatedChange fail to find slotGroup for lootObj")
        return
    end
    if slotGroup == self:GetSelfSlotGroup() then
        if not self._bHasFetchedPlayerItems then
            logwarning("_OnItemArrayPostReplicatedChange has not ever fetched player items")
            return
        end
    end
    local oldItemData = self:GetItemDataByGid(itemInfo.ItemGid)
    if oldItemData and oldItemData.modifyTimeStamp > itemInfo.ModifyTimeStamp then
        logwarning("_OnItemArrayPostReplicatedChange oldItemData is more advanced than itemInfo")
        return
    end
    self:_InitConfigForItemInfo(itemInfo)
    local changeType = self:GetChangeType(oldItemData, itemInfo, slotGroup)
    ---@type PropChange
    local propChange = self:_ObtainPropChange(changeType, oldItemData, itemInfo, slotGroup)
    self:_AddPropChangeToList(propChange)
    LuaTickController:Get():RegisterTick(self)
end

function LootingServer:GetCharacterInventoryMgr()
    local inventoryMgr = self._cachedData.InvMgr
    if not isvalid(inventoryMgr) then
        inventoryMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    end
    return inventoryMgr
end

function LootingServer:_OnRoleLoadChanged()
    local allItemsWeight = Server.InventoryServer:GetAllCarryItemsWeight()
    Server.InventoryServer:CheckLoadState(allItemsWeight)
end

function LootingServer:CheckLootObjHasUnSearchedItem()
    local currentLootObj = self:GetCurrentInteractingBox()
    if isvalid(currentLootObj) then
        local slotGroup = self:GetSlotGroupByLootObj(currentLootObj)
        local slotTyps = self:GetSlotTypesByGroup(slotGroup, self:GetCurrentInteractorType())
        for _, slotType in pairs(slotTyps) do
            if table.contains(ItemBase.SlotsNeedSearch, slotType) then
                local itemSlot = Server.InventoryServer:GetSlot(slotType, slotGroup)
                for _, item in pairs(itemSlot:GetItems_RefConst()) do
                    if item and not item:IsSearched() then
                        return true
                    end
                end
            end
        end
    end
    return false
end

function LootingServer:IncrementalUpdateOBPlayerData()
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if not isvalid(obInvMgr) then
        return
    end
    local newItemInfoDict = {}
    for _, itemInfo in pairs(obInvMgr.InventoryItemArray.Items) do
        -- body
        local gid = itemInfo.ItemGid
        newItemInfoDict[gid] = itemInfo
    end

    -- <uint64, ItemBase>
    local itemBaseDict = {}
    for _, slotType in pairs(self:GetInGameSlotTypes()) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType, self:GetSelfSlotGroup())
        for _, item in pairs(itemSlot:GetItems_RefConst()) do
            itemBaseDict[item.gid] = item
        end
    end

    self:_IncrementalUpdateItems(itemBaseDict, newItemInfoDict, self:GetSelfSlotGroup(), obInvMgr.InventoryRepData.SpaceRepData)
end

function LootingServer:FullUpdateOBPlayerData(bOnlyKeyChain)
    bOnlyKeyChain = setdefault(bOnlyKeyChain, false)
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if not isvalid(obInvMgr) then
        return
    end
    for _, slotType in pairs(self:GetInGameSlotTypes()) do
        local itemSlot = Server.InventoryServer:GetSlot(slotType, self:GetSelfSlotGroup())
        if itemSlot then itemSlot:ResetSlot() end
    end

    local itemInfoArray = obInvMgr.InventoryItemArray.Items
    log(string.format("FullUpdateOBData num=%d bOnlyKeyChain=%d", #itemInfoArray, bOnlyKeyChain and 1 or 0))

    if bOnlyKeyChain then
        for _, itemInfo in pairs(itemInfoArray) do
            if itemInfo.AttachPosition == ESlotType.KeyChain then
                local targetSlot = Server.InventoryServer:GetSlot(ESlotType.KeyChain, self:GetSelfSlotGroup())
                self:CreateItemData(itemInfo, targetSlot)
                local keyChainContSlot = Server.InventoryServer:GetRefContainerSlot(targetSlot)
                keyChainContSlot:InitKeyChainStructFromItemInfo(itemInfo)
                break
            end
        end
        return
    end

    local spaceRepData = obInvMgr.InventoryRepData.SpaceRepData
    self:_InitTargetSpace(spaceRepData, self:GetSelfSlotGroup())

    local itemInfoGroupDict, allSlotType = groupSortItemInfoList(itemInfoArray)
    for _, slotType in pairs(allSlotType) do
        local itemInfoList = itemInfoGroupDict[slotType]
        if (itemInfoList) then
            local targetSlot = Server.InventoryServer:GetSlot(slotType, self:GetSelfSlotGroup())
            for _, itemInfo in ipairs(itemInfoList) do
                local newItemData = self:CreateItemData(itemInfo, targetSlot)
                if newItemData then
                    RuntimeIconTool.PreLoadItemIcon(newItemData)
                end

                -- Special rule for keychain
                if slotType == ESlotType.KeyChain then
                    local keyChainContSlot = Server.InventoryServer:GetRefContainerSlot(targetSlot)
                    keyChainContSlot:InitKeyChainStructFromItemInfo(itemInfo)
                end
            end
        end
    end
end

function LootingServer:FullUpdateOBLootingData()
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if not isvalid(obInvMgr) then
        return
    end
    local gamePickupType = self:GetCurrentOBPickupType()
    local lootObj = self:GetCurrentOBLootObj()
    if gamePickupType and isvalid(lootObj) then
        if gamePickupType == EGamePickupType.NearbyDeadBody then
            self:InitDeadbodyData(lootObj)
        elseif gamePickupType == EGamePickupType.DropContainer then
            self:InitPickupBoxData_DropContainer(lootObj)
        elseif gamePickupType == EGamePickupType.SceneBox then
            self:InitPickupBoxData_SceneBox(lootObj)
        end
    end
end

function LootingServer:GetCurrentOBPickupType()
    local lootObj = self:GetCurrentOBLootObj()
    local gamePickupType
    if isvalid(lootObj) and lootObj.GetLootObjType then
        local lootObjType = lootObj:GetLootObjType()
        if lootObjType == ELootObjType.ELootObjType_OpenBox then
            gamePickupType = EGamePickupType.SceneBox
        elseif lootObjType == ELootObjType.ELootObjType_SceneContainer then
            gamePickupType = EGamePickupType.DropContainer
        elseif lootObjType == ELootObjType.ELootObjType_DeadBody then
            gamePickupType = EGamePickupType.NearbyDeadBody
        end
    end
    return gamePickupType
end

function LootingServer:GetCurrentOBLootObj()
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    if isvalid(obInvMgr) and isvalid(obInvMgr.LootingObj) then
        return obInvMgr.LootingObj
    end
end

function LootingServer:IsArena()
    if InGameController:Get():IsArena() then
        return true
    end
    -- 担心GameState还没同步下来，这里再判断一下
    local matchModeInfo = Server.MatchServer:GetMatchModeInfo()
    if matchModeInfo and matchModeInfo.game_rule == MatchGameRule.ArenaGameRule then
        return true
    end
    return false
end

function LootingServer:_OnActorProxyUpdate()
    self:_TrySendNightVisionInfo()
end

function LootingServer:_TrySendNightVisionInfo()
    if self._hasSendNightInfo then
        return
    end
    local matchModeInfo = Server.MatchServer:GetMatchModeInfo()
    --MatchSubMode.HighYieldSOLNight废弃 fireicelin
    if matchModeInfo and (matchModeInfo.sub_mode ~= MatchSubMode.SOLNight and matchModeInfo.sub_mode ~= MatchSubMode.HighYieldSOLNight
    and matchModeInfo.sub_mode ~= MatchSubMode.VeryHighYieldSOLNight and matchModeInfo.sub_mode ~= MatchSubMode.ExtremeHighYieldSOLNight) then
        return
    end
    local invMgr = self._cachedData.InvMgr
    if isvalid(invMgr) then
        local PlayerController = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
        if isvalid(PlayerController) then
            local character = PlayerController:GetPawn()
            if isvalid(character) then
                Timer.DelayCall(1, self._SendNightVisionInfo, self)
                self._hasSendNightInfo = true
            end
        end
    end
end

function LootingServer:_SendNightVisionInfo()
    local invMgr = self._cachedData.InvMgr
    if isvalid(invMgr) then
        local PlayerSettlementComponent = Facade.GameFlowManager:GetPlayerSettlementComponent()
        if isvalid(PlayerSettlementComponent) then
            PlayerSettlementComponent:ClientSendEventState(EEventType.CarryInEquipmentValue, invMgr.RepData.StartupEquipmentValue)
        end
    end
end

--endregion
-----------------------------------------------------------------------

return LootingServer
