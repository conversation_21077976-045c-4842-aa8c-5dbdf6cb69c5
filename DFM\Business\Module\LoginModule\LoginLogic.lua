----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------

local ULuaSubsystem          = import "LuaSubsystem"
local LuaSubsystem           = ULuaSubsystem.Get()



local function log(...)
    loginfo("[LoginLogic]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

local LoginLogic = {}
local LoginConfig = require("DFM.Business.Module.LoginModule.LoginConfig")
local SDKInfoServer = Server.SDKInfoServer
local UGameLogin = import "DFMGameLogin"
local UGameLoginIns = UGameLogin.Get(GetGameInstance())
local UGameFriend = import "DFMGameFriend"
local UDFMLocalizationManager = import "DFMLocalizationManager"
local DFMLocalizationManager = UDFMLocalizationManager.Get(GetGameInstance())
local this = LoginLogic
local UGameVersionUtils = import "GameVersionUtils"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local UAppSetting = import "AppSetting"

local UWeGameManager = nil
local WeGameManager = nil
local UWeGameSDKDelegates = nil
local FWeGameConnectSvrInfo = nil
local DFSteamServiceAgent = nil
if PLATFORM_WINDOWS then
    UWeGameManager = import "WeGameSDKManager"
    WeGameManager = UWeGameManager.Get(GetGameInstance())
    UWeGameSDKDelegates = import "WeGameSDKDelegates"
    FWeGameConnectSvrInfo = import "WeGameConnectSvrInfo"
    local UDFSteamServiceAgent = import "DFSteamServiceAgent"
    DFSteamServiceAgent = UDFSteamServiceAgent.Get(GetGameInstance())
end

this.AddListeners = function()
    -- Facade.ProtoManager.Events.evtOnConnectSuccess:AddListener(this._OnLoginConncetSuccess)
    -- Facade.ProtoManager.Events.evtOnTmpRelayConnected:AddListener(this._OnConncetSuccess)
    -- Facade.ProtoManager.Events.evtOnCancelReconnect:AddListener(this._ReturnToLoginScene)
    -- Facade.ProtoManager:AddNtfListener("CSGatewayKickPlayerNtf", this._OnCSGatewayKickPlayerNtf)
end
this.RemoveListens = function()
    -- Facade.ProtoManager.Events.evtOnConnectSuccess:RemoveListener(this._OnLoginConncetSuccess)
    -- Facade.ProtoManager.Events.evtOnTmpRelayConnected:RemoveListener(this._OnConncetSuccess)
    -- Facade.ProtoManager.Events.evtOnCancelReconnect:RemoveListener(this._ReturnToLoginScene)
    -- Facade.ProtoManager:RemoveNtfListener("CSGatewayKickPlayerNtf", this._OnCSGatewayKickPlayerNtf)
end

-- this._OnCSGatewayKickPlayerNtf = function(ntf)
--     log("LoginLogic:_OnCSGatewayKickPlayerNtf",ntf.reason)
--     if(ntf.reason == 3) then
--         log("登录前发了奇怪的协议, 直接跳过kick")
--         return
--     end
--     Facade.ProtoManager:TryDisConnectServer(true)
--     Facade.ProtoManager:StopHeartbeatTimer()
    
--     Module.CommonTips:ShowConfirmWindowWithSingleBtn(
--         CommonConfig.Loc.SomeOneLoginThisAccount,
--         SafeCallBack(this._ReturnToLoginScene, nil, true),
--         Module.Login.Config.Loc.BackToLogin
--         )
-- end

-- this._ReturnToLoginScene = function()
--     -- 返回登录场景
--     -- Module.CommonTips:ShowSimpleTip(LoginConfig.Loc.ReturnToLoginScene)
--     Module.Login:BackToLogin()
-- end

-- this._OnLoginConncetSuccess = function()
--     Facade.ProtoManager:SetReconnectState(false)
--     Facade.ProtoManager:SetQuickReconnectState(false)
--     LoginLogic.AccountLogin(Server.SDKInfoServer:GetUserName())
-- end

-- this._FetchNeccesaryData = function()
--     log("获取断线后所有数据")
--     local function RelayConnectedFail()
--         -- check necesary data received
--         if not Facade.ProtoManager:GetProtoConnectionState() then
--             Facade.ProtoManager:TryStartReconnecting()
--         end
--         Facade.ProtoManager:SetProtoConnectionState(true)
--     end
--     -- set protoconnection State
--     -- every time received proto then check cachedtable
--     -- when cachedtable over then invoke
--     -- DoFetchServerBusiness()
--     Facade.ProtoManager:GetOnRelayConnectedData()
--     Facade.ProtoManager:SetProtoConnectionState(false) -- now every other proto cant be send
--     -- set timer for in case of disturbed
--     Timer.DelayCall(10, RelayConnectedFail)
-- end

-- this._OnConncetSuccess = function()
--     log("this._OnConncetSuccess")
--     Server.AccountServer:AccountLoginReq(this._FetchNeccesaryData)
-- end

-- LoginLogic.ParseLoginRet = function(ret)
--     local tab = {}
--     tab["openId"] = ret.openID -- string
--     tab["token"] = ret.token -- string
--     tab["tokenExpire"] = ret.tokenExpire -- int64
--     tab["firstLogin"] = ret.firstLogin -- int
--     tab["regChannelDis"] = ret.regChannelDis -- string;
--     tab["userName"] = ret.userName -- string;
--     tab["gender"] = ret.gender -- int;
--     tab["birthdate"] = ret.birthdate -- string;
--     tab["pictureUrl"] = ret.pictureUrl -- string;
--     tab["pf"] = ret.pf -- string;
--     tab["pfKey"] = ret.pfKey -- string;
--     tab["realNameAuth"] = ret.realNameAuth -- bool;
--     tab["channelId"] = ret.channelID -- int;
--     tab["channel"] = ret.channel -- string;
--     tab["channelInfo"] = ret.channelInfo -- string;
--     tab["confirmCode"] = ret.confirmCode -- string;
--     tab["confirmCodeExpireTime"] = ret.confirmCodeExpireTime -- int;
--     tab["bindList"] = ret.bindList -- string;
--     printtable(tab)
--     return tab
-- end

-- LoginLogic.ParseBaseRet = function(ret)
--     local tab = {}
--     -- 标记是从哪个方法过来
--     tab.methodNameID = ret.methodNameID
--     -- MSDK 返回码，详情可参考 MSDKError.h
--     tab.retCode = ret.retCode
--     -- MSDK 描述信息
--     tab.retMsg = ret.retMsg
--     -- 第三方渠道返回码
--     tab.thirdCode = ret.thirdCode
--     -- 第三方渠道描述信息
--     tab.thirdMsg = ret.thirdMsg
--     -- 扩展字段，保留
--     tab.extraJson = ret.extraJson
--     logtable(tab)
--     return tab
-- end

-- 登录回调，包括 login、bind、autologin、switchuser 等
-- LoginLogic._OnLoginRet = function(ret, ...)
--     log("LoginLogic._OnLoginRet", ret, ...)
--     local tab = this.ParseLoginRet(ret)
--     SDKInfoServer:SetLoginRelatedInfo(tab)
--     if Module.Login.Field:GetAutoLogin() then
--         local bAutorLoginSuccess = SDKInfoServer.openId and SDKInfoServer.openId ~= ""
--         loginfo("LoginLogic:_OnLoginRet", bAutorLoginSuccess, SDKInfoServer.openId, SDKInfoServer.openId ~= "")
--         if bAutorLoginSuccess then
--         else
--             Module.GCloudSDK:LuaReportLoginEvent({Login = "GetThirdPartInfo1"})
--             Module.Login.Field:SetAutoLogin(false)
--         end
--     else
--         if Server.SDKInfoServer:GetOpenId() == "" then            
--             Module.Login.Field:SetAutoLogin(false)
--             Module.CommonTips:ShowSimpleTip(Module.Login.Config.Loc.FailGetThirdPartAccountInfoTip)
--             return
--         end
--         if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Login then
--         else
--             log("FlowToEnterLogin",Facade.GameFlowManager:GetCurrentGameFlow())
--             Module.Login:BackToLogin()
--         end
--         -- SDKInfoServer:TrySDKConnect()
--     end
-- end

-- 登出回调、应用唤醒回调 -- 暂时只用作登出
LoginLogic._OnBaseRet = function(ret)
    log("LoginModule:_OnBaseRet", ret.methodNameID,ret.retCode)
    logtable(ret,true)
    Module.Login:BackToLogin()
    if ret.methodNameID == Module.Login.Config.MethodName.kMethodNameLogout then
    end
end

function LoginLogic.ResetPlayerState()
    log("LoginLogic.ResetPlayerState")
    Server.AccountServer:ResetPlayerStateCode()
end

LoginLogic.CallGameLoginFunc = function(funcName, params)
    log("LoginModule:_CallGameLoginFunc")
    -- local fCallOnLoginRet = function(ret)
    --     log("fCallOnLoginRet")
    --     this._OnLoginRet(ret)
    -- end

    if UGameLoginIns then
        -- log("UGameLoginIns Ins Clear OnLoginRetDelegate")
        -- UGameLoginIns.OnLoginRetDelegate:Clear()
        -- log("UGameLoginIns Ins Add OnLoginRetDelegate")
        -- UGameLoginIns.OnLoginRetDelegate:Add(fCallOnLoginRet)
        local func = UGameLoginIns[funcName]
        if func then
            func(UGameLoginIns, params)
        else
            log(funcName, "找不到方法")
        end
    else
        log("LoginModule:Login Error NoUGameLoginIns")
    end
end

-- LoginLogic.CallLoginFunc = function(funcName, ...)
-- end

LoginLogic.CallGameLogoutFunc = function(funcName, params)
    log("LoginModule:CallGameLogoutFunc")
    -- local fCallOnBaseRet = function(ret)
    --     this:_OnBaseRet(ret)
    -- end
    if UGameLoginIns then
        -- UGameLoginIns.OnBaseRetDelegate:Clear()
        -- UGameLoginIns.OnBaseRetDelegate:Add(fCallOnBaseRet)
        local func = UGameLoginIns[funcName]
        if func then
            func(UGameLoginIns, params)
        else
            log(funcName, "找不到方法")
        end
    else
        log("LoginModule:Logout Error NoUGameLoginIns")
    end
end

LoginLogic.CallGameToolsFunc = function(funcName, ...)
    log("LoginLogic.CallGameToolsFunc")
    if UGameLoginIns then
        local func = UGameLoginIns[funcName]
        if func then
            func(UGameLoginIns,...)
        else
            log(funcName, "找不到方法")
        end
    else
        log("LoginModule:Logout Error NoUGameLoginIns")
    end
end

function LoginLogic._InvokeStartLogin()
    log("LoginLogic._InvokeStartLogin")
    LoginConfig.Events.evtOnLoginStartLoginReq:Invoke()
end
function LoginLogic._InvokeLoginFailed()
    log("LoginLogic._InvokeLoginFailed")
    LoginConfig.flowEvtLoginFailed:Invoke()
    LoginConfig.Events.evtOnLoginFailed:Invoke()
end

-- function LoginLogic._InvokeLoginSuccess()
--     log("LoginLogic._InvokeLoginSuccess")
--     log("InvokeLoginSuccess", SDKInfoServer.PlayerId, SDKInfoServer.UserName)
--     GFLogin_InitLobbyBusiness()
--     LoginConfig.Events.evtOnLoginSuccess:Invoke()
--     Module.Login.Field:SetAutoLogin(false)
--     LoginLogic._InitHearbeatConnect()
--     --需要根据当前的情况，选择是否跳过模式大厅
--     Module.Login:GetStateInfo(function()
--         local TeamSvr = Server.TeamServer
--         if TeamSvr:IsInTeam()  then
--             TeamSvr.Events.evtTeamInfosUpdated:AddListener(LoginLogic._OnTeamInfoUpdated)
--         --正常进入模式大厅
--         else
--             LoginConfig.flowEvtLoginSuccess:Invoke("ModeHall")
--         end
--     end)
-- end

-- function LoginLogic._OnTeamInfoUpdated()
--     local TeamSvr = Server.TeamServer
--     if TeamSvr:GetTeamNum() > 1 then
--         if TeamSvr:GetTeamMatchMode().game_mode == MatchGameMode.TDMGameMode then
--             Module.Hall:SetEnabledHallModule(false)
--             Module.BattlefieldEntry:SetEnableNewHallModule(true)
--             LoginConfig.flowEvtLoginSuccess:Invoke("BattleField")
--         else
--             LoginConfig.flowEvtLoginSuccess:Invoke("SafeHouse")
--         end
--     else
--         LoginConfig.flowEvtLoginSuccess:Invoke("ModeHall")
--     end
--     TeamSvr.Events.evtTeamInfosUpdated:RemoveListener(LoginLogic._OnTeamInfoUpdated)
-- end

-- function LoginLogic._InitHearbeatConnect()
--     Facade.ProtoManager:InitHeartBeatThread()
--     Facade.ProtoManager:StartHeartbeatTimer()
--     Module.GCloudSDK:InitWhenLoginSuccess()
-- end

-- function LoginLogic.AccountLogin(userName)
--     log("LoginLogic.AccountLogin", userName)
--     local fOnCSAccountLoginRes = function(res)
--         if res.result and res.result == 0 then
--             if not Facade.GameFlowManager:CheckIsInFrontEnd() then
--                 return
--             end
--             this._InvokeLoginSuccess()
--         else
--             this._InvokeLoginFailed()
--             if not res.result then
--                 Module.CommonTips:ShowSimpleTip(LoginConfig.Loc.LoginFail)
--             end
--             Facade.ProtoManager:TryDisConnectServer(true)
--         end
--     end

--     this._InvokeStartLogin()
--     Server.AccountServer:AccountLoginReq(fOnCSAccountLoginRes)
-- end

function LoginLogic.QrLogin()
    if UGameLoginIns then
        UGameLoginIns:DebugLogin()
    end
end


function LoginLogic.IsQQInstalled()
    log("LoginLogic.IsQQInstalled")
    if UGameLoginIns then
        return UGameLoginIns:IsQQInstalled()
    end
    return false
end

function LoginLogic.IsWechatInstalled()
    log("LoginLogic.IsWechatInstalled")
    if UGameLoginIns then
        return UGameLoginIns:IsWechatInstalled()
    end
    return false
end

-- TestAutomationlogin
function LoginLogic.CheckMatchId(matchId)
    local poolTable = Facade.TableManager:GetTable("MatchCommonConfig")
    local mapInfo = poolTable[tostring(matchId)]
    if mapInfo then
        return true
    end
    return false
end

function LoginLogic.DefaultMatchId()
    local poolTable = Facade.TableManager:GetTable("MatchCommonConfig")
    for key, value in pairs(poolTable) do
        if key then
            return value.MatchId
        end
    end
    return 0
end

function LoginLogic.GetServerGroup()
    local groupName = Module.Login.Field:GetCurrentServerGroup()
    if(groupName == nil) then
        local serverAddr = Server.SDKInfoServer:GetServerAddr()
        if(serverAddr) then
            groupName = serverAddr.groupName
        end
    end
    return groupName
end

function LoginLogic.SaveServerGroup(groupName)
    Module.Login.Field:SetCurrentServerGroup(groupName)
    LoginConfig.Events.evtServerGroupSelect:Invoke(groupName)
end

---@param bAutoMatch boolean        是否自动入局
---@param ip string         服务器ip
---@param port string         服务器端口
---@param matchGameMode MatchGameMode WorldGameMode=1，TDMGameMode=2
---@param param table   大世界：{EntranceIdx,MatchSubMode}对应WorldEntranceConfig中的EntranceIdx，对应的地图,MatchMode.csv中对应的MatchSubMode,一般SOL中PMC=10，SCAV=20
---                     Raid：{EntranceIdx,MatchSubMode}对应WorldEntranceConfig中的EntranceIdx，对应的地图,MatchMode.csv中对应的MatchSubMode,简单21,中等22,困难23
---                     大战场: {groupId,mapID} groupId:CLASSIC = 1-- 经典,OFFDEF  = 2-- 攻防
---@param defaultPlayerId number    玩家id
function LoginLogic.TestAutomationLogin(bAutoMatch, ip, port, matchGameMode, param, defaultPlayerId)
    logerror("TestAutomationLogin【param】:",bAutoMatch, ip, port, matchGameMode, param[1], param[2], defaultPlayerId)
    if Module.Login.Field then
        Module.Login.Field:SetTestAutoLoginMatch(bAutoMatch)
        Module.Login.Field:SetTestAutoLoginMatchGameMode(matchGameMode)
        Module.Login.Field:SetTestAutoLoginParam(param)
        local defaultId = setdefault(defaultPlayerId, "111111111")
        defaultId = tostring(defaultId)
        local serverKey = "customize"
        Server.SDKInfoServer:SetSelectAddrKey(serverKey,string.format("%s:%s",ip,port))
        Server.SDKInfoServer:SetChannel(EChannelType.kChannelNone)
        Server.SDKInfoServer:SetUserName(defaultId)
        Server.SDKInfoServer:SetOpenId(defaultId)
        if IsHD() or _WITH_EDITOR == 1 then
            -- 支持编辑器下也可检测网吧特权。
            local UGameNetBar = import "DFMGameNetBar"
            local UGameNetBarIns = UGameNetBar.Get(GetGameInstance())
            local UAppSetting = import "AppSetting"
            local AppSetting = UAppSetting.Get()
            if IsBuildRegionCN() then
                UGameNetBarIns:ReqNetBarLevel(0, 10180 , 0, 0, false)
            elseif Server.SDKInfoServer:IsRegionKorea() then
                UGameNetBarIns:ReqNetBarLevel(0, 20010 , 0, 0, true)
            end
        end
        Server.SDKInfoServer:TrySDKConnect()
    else
        logerror("TestAutomationLogin Module.Login.Field is nil, Not Init！！！")
    end
end

function LoginLogic.SetCurrentCulture(culture)
--     if culture and DFMLocalizationManager then
--         return DFMLocalizationManager:SetCurrentCulture(culture)
--     end
-- 
--     return false
    return LocalizeTool.SetCurrentCulture(culture)
end

function LoginLogic.GetCurrentCulture()
    return LocalizeTool.GetCurrentCulture()
--     if DFMLocalizationManager then
--         return DFMLocalizationManager:GetCurrentCulture()
--     end
-- 
--     return "en"
end
-- END

--friend 模块 临时放置
function LoginLogic.FriendSendMessage(channel, type, user, title, desc, link, thumbPath, extraJson)
    UGameFriend.SendMessage(channel, type, user, title, desc, link, thumbPath, extraJson)
end
--friend END

function LoginLogic.GameAutoLogin()
    loginfo("LoginInterface:GameAutoLogin")

    --海外包使用LI登录
    if IsBuildRegionGlobal() then
        loginfo("LoginInterface:GameAutoLogin IsBuildRegionGlobal")
        --跑一遍登录面板OnOpen的逻辑
        if IsWeGameEnabled() then
             --跑一遍登录面板OnOpen的逻辑
             this.InitWeGameLoginState()
             --自动登录
             local UDFMGameLogin = import "DFMGameLogin"
             local loginIns = UDFMGameLogin.Get(GetGameInstance())
             if loginIns then
                 loginIns:RegisterWeGameCallBacks()
             end
             WeGameManager:TryAutoLogin()
             return
        end
        --海外界面需要挂载到游戏中
        -- @akihikofeng 主机平台仅绑INTL, 直接显示INTL登录界面
        if PLATFORM_GEN9 == 1 then
            Module.Login:ShowINTLPanel(Module.Login.Config.EINTLPanelType.Login)
        else
            if VersionUtil.IsShipping() then
                logerror("LoginInterface:GameAutoLogin IsShipping so autologin")
                Module.Login:ShowINTLPanel(Module.Login.Config.EINTLPanelType.AutoLogin)
            else
                logerror("LoginInterface:GameAutoLogin Is not Shipping, only show login panel")
            end
        end

        return
    elseif IsBuildRegionGA() then
        loginfo("LoginInterface:GameAutoLogin IsBuildRegionGA")
        if IsWeGameEnabled() then
            --跑一遍登录面板OnOpen的逻辑
            this.InitWeGameLoginState()
            --自动登录
            local UDFMGameLogin = import "DFMGameLogin"
            local loginIns = UDFMGameLogin.Get(GetGameInstance())
            if loginIns then
                loginIns:RegisterWeGameCallBacks()
            end
            WeGameManager:TryAutoLogin()
            return
        end
    else
        loginfo("LoginInterface:GameAutoLogin IsBuildRegionCN")
        if IsWeGameEnabled() then
            --跑一遍登录面板OnOpen的逻辑
            this.InitWeGameLoginState()
            --自动登录
            local UDFMGameLogin = import "DFMGameLogin"
            local loginIns = UDFMGameLogin.Get(GetGameInstance())
            if loginIns then
                loginIns:RegisterWeGameCallBacks()
            end
            LoginLogic._SetInstallChannel()
            WeGameManager:TryAutoLogin()
            return
        end
    end

    --获取上次登录的渠道
    local lastChannelId = Server.SDKInfoServer:GetLastConnectChannelID()
    local lastSubChannelId = Server.SDKInfoServer:GetLastSubChannelID()
    if lastChannelId ~= 0 then
        Server.SDKInfoServer:SetLoginSubChannelID(lastSubChannelId)
    end
    log("AutoLogin channelId, lastSubChannelId",lastChannelId, lastSubChannelId)
    if lastChannelId == 0 then
        return
    end

    if not VersionUtil.IsTestClientType() then
        if Module.Login.Field:GetAutoLogin() and Module.Login.Field:GetIsFirstEnterLogin() then
            Module.Login:AutoLogin(lastChannelId)
            Module.Login.Field:AlreadyEnteredLogin()
            Module.Login.Field:SetLoginState(true)
        end
    end
end

function LoginLogic.InitWeGameLoginState()
    local hotUpdateVersion = tostring(UGameVersionUtils.GetVersion()) .. "." .. tostring(LuaSubsystem:GetMoLiVersion())
    --Facade.ConfigManager:GetString("LastHotUpdateVersion", "")

    local versionApp = UGameVersionUtils.GetAppProductVersion()
    local versionRes = UGameVersionUtils.GetResVersion()

    -- if WeGameManager:GetAppVersion(versionApp) == 1 then
    -- end
    
    --写入crashsight版本号
    Module.GCloudSDK:SetHotfixData("CodeHotUpdateVersion",hotUpdateVersion)
    Module.GCloudSDK:SetHotfixData("ResHotUpdateVersion", versionRes)

    Facade.ProtoManager:SetReconnectState(false)
    Facade.ProtoManager:SetQuickReconnectState(false)
    
    -- Maple修改服务器地址
    Module.GCloudSDK:InitMapleServerUrl()
    -- 关闭Loading背景，放到login成功后再关闭
    -- Module.GCloudSDK:CloseLoadingBackground()
    -- 获取大区配置信息
    this.ConfigureWeGameServerInfo()
end

function LoginLogic.ConfigureWeGameServerInfo()
    local info = FWeGameConnectSvrInfo()
    info = WeGameManager:GetServerInfo()
    logwarning("WeGame TryAutoLogin With ZoneID:", info.ZoneID, " ZoneName_en:", info.ZoneName_en, " ZoneName_zh:", info.ZoneName_zh, " Addr:", info.Address)
    if info.ZoneID ~= -1 then
        -- Server.SDKInfoServer:SetLauncherServerInfo(info.ZoneName_en, info.ZoneName_zh, info.Address)
        Server.SDKInfoServer:SetSelectAddrKey(info.ZoneName_en,info.Address)
    else
        logwarning("WeGame Get ServerInfo Failed,set addr by channel")
        logerror("gamechannel:", VersionUtil.GetGameChannel())
        local addrKey = "zone_36"
        if IsBuildRegionGlobal() then
            addrKey = "zone_66"
        elseif IsBuildRegionGA() then
            addrKey = "zone_501"
        end
        if VersionUtil.IsGameChannelSteam() then
            local branchName = ""
            local isBetaBranch, branchName = DFSteamServiceAgent:GetCurrentBetaName(branchName)
            logerror("Steam isBetaBranch ",isBetaBranch)
            if isBetaBranch then
                addrKey = branchName
            end
            if addrKey == "zone_206" or addrKey == "zone_327" then
                addrKey = "zone_206"
                if IsBuildRegionGlobal() then
                    addrKey = "zone_327"
                end
            end
            logerror("Steam branchName:",branchName)
        elseif VersionUtil.IsGameChannelEpic() or VersionUtil.IsGameChannelOfficial() or VersionUtil.IsGameChannelWeGame() or VersionUtil.IsGameChannelGoogle() then
            if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
                local branchID = WeGameManager:GetCurrentBranchName()
                logerror("WeGameGL branchName:",branchID)
                local serverkey = this.GetServerKeyByBranchID(branchID)
                if serverkey ~= nil and string.len(serverkey) > 0 then
                    addrKey = serverkey
                end
            end
        end

        if not VersionUtil.IsRelease() then
            local command = string.lower(UKismetSystemLibrary.GetCommandLine())
            local pos = string.find(command, "-zone")
            if pos ~= nil then
                local zone_command = string.sub(command, pos, #command)
                local CMDs = StringUtil.StringSplit(zone_command, " ")
                if #CMDs > 1 then
                    addrKey = CMDs[2]
                    logerror("Force Set AddrServer")
                end
            else
                local cpos = string.find(command, "-customzone")
                if cpos ~= nil then
                    local zone_command = string.sub(command, cpos, #command)
                    local CMDs = StringUtil.StringSplit(zone_command, " ")
                    if #CMDs > 1 then
                        local url = CMDs[2]
                        url = string.gsub(url, "_", ".")
                        Server.SDKInfoServer:SetSelectAddrKey("customize", url)
                        logerror("Force Set AddrServer:", url)
                        return
                    end
                end
            end
        end

        logerror("WeGame Global final zoneid:",addrKey)
        Server.SDKInfoServer:SetSelectAddrKey(addrKey)
    end
end

function LoginLogic.IsInReview()
    local command = string.lower(UKismetSystemLibrary.GetCommandLine())
    if string.find(command, "-review") ~= nil then
        logerror("Steam is in review ")
        return true
    end
    return false
end

function LoginLogic.IsReturnFromBHD()
    local command = string.lower(UKismetSystemLibrary.GetCommandLine())
    return string.find(command, "-returnfrombhd") ~= nil
end

--LI PASS登录，需要设置组件UI根节点
function LoginLogic.SetLIUIRoot(uiRoot)
    if UGameLoginIns then
        UGameLoginIns:SetLIUIRoot(uiRoot)
    end
end

function LoginLogic.GetServerKeyByBranchID(branchID)
    if string.len(branchID) > 0 then
        local serverAddrs = Server.SDKInfoServer:GetServerAddrList()
        for key, value in pairs(serverAddrs) do
            if(value) then
                local bCurRowIsShippingCfg = VersionUtil.IsBuildConfigShipping(value.TargetBuildConfiguration)
                loginfo("value.TargetBuildConfiguration:", value.TargetBuildConfiguration)
                loginfo("bCurRowIsShippingCfg:", bCurRowIsShippingCfg)
                loginfo("VersionUtil.IsShipping():", VersionUtil.IsShipping())
                loginfo("value.BranchID:", value.BranchID)
                if VersionUtil.IsShipping() == bCurRowIsShippingCfg then
                    local branchIDs = string.split(value.BranchID, "|")
                    if #branchIDs > 0 then
                        logerror("size of branchIDs:", #branchIDs)
                        for _, IDValue in pairs(branchIDs) do
                            logerror("IDValue", IDValue)
                            if IDValue == branchID then
                                return value.KeyName
                            end
                        end
                    end
                end
            end
        end
    end
    return nil
end

function LoginLogic.ReportToWeGame(Action, Status)
     if PLATFORM_WINDOWS and IsWeGameEnabled() then
         local prefix = "sail_"
         if IsBuildRegionCN() then
             prefix = "rail_"
         end
         local curRegionAction = prefix..tostring(Action)
         local ExtStr =  string.format("{\"gopenid\":\"%s\"}", Server.SDKInfoServer:GetOpenIdStr())
         --logerror("LoginLogic.ReportToWeGame ",Action ,Status, ExtStr)
         WeGameManager:ReportToWeGame(curRegionAction, Status, ExtStr)
     end
end

function LoginLogic.DeleteAccount()
    local webBrowserData = {useSDKWebBrowser = true}
    if IsBuildRegionCN() then
        local url = "https://gacc-account-web-test.odp.qq.com/writeoff.html"
        if VersionUtil.IsShipping() then
            url = "https://gacc-account-web.odp.qq.com/writeoff.html"
        end
        --固定传值为：client
        local ADTAG = "client"
        --根据客户端MSDK版本取值，可取枚举值："V3"或者"V5"，默认为V5，例如：msdkVersion=V5
        local msdkVersion = "V5"
        --根据业务账号体系来取值，可取枚举值："gopenid"或者"openid"，默认为gopenid，例如：idType=openid（如不清楚业务使用的账号类型，可联系leiyzhang确认）
        local idType = "gopenid"
        --MSDK V5版本加密登录态
        local itopencodeparam = ""
        --操作系统 1 - Android, 2 - iOS, 3 - Web，4 - Linux, 5 - windows
        local os = ""
        if PLATFORM_ANDROID then
            os = "1"
        --todo PLATFORM_OPENHARMONY
        elseif PLATFORM_OPENHARMONY then
            os = "1"
        elseif PLATFORM_IOS then
            os = "2"
        elseif PLATFORM_WINDOWS then
            os = "5"
        end
        --MSDK分配的游戏id
        local gameid = UAppSetting.Get().SdkGameId
        --登录渠道id
        local channelid = Server.SDKInfoServer:GetChannel()
        --客户端ip，（隐私合规问题不用采集了，默认传127.0.0.1）
        local outerIp = "127.0.0.1"
        --根据客户端使用的webview内核传递，可取枚举值："ue"或者"msdk"，默认为msdk，例如：webview=msdk
        local webview = "msdk"
        local extraParam = "?ADTAG=client&os={0}&gameid={1}&channelid={2}&outerIp={3}&webview={4}&msdkVersion={5}&idType={6}"
        extraParam = StringUtil.SequentialFormat(extraParam, os, gameid, channelid, outerIp, webview, msdkVersion, idType)
        local full_url = url .. extraParam
        loginfo("LoginLogic.DeleteAccount url:", full_url)
        if not IsHD() then
            Module.GCloudSDK:OpenUrl(full_url, 2, nil, nil, nil, nil, webBrowserData)
            --Module.Login:BackToLogin()
        else
            local extraJson = '{\"webview_window_scale\":0.8}'
            Module.GCloudSDK:OpenUrl(url, 2, false, true, extraJson, nil, webBrowserData)
        end
    elseif IsBuildRegionGlobal() then
        local url = "https://test-common-web.intlgame.com/account-deletion/index.html"
        if VersionUtil.IsShipping() then
            url = "https://common-web.intlgame.com/account-deletion/index.html"
        end

        local intl_url = "https://test.intlgame.com"
        if VersionUtil.IsShipping() then
            intl_url = "https://sg.intlgame.com"
        end

        --进入不同账号注销页面 0: 账号注销 2: 撤销同意隐私协议 3: 撤销同意用户协议
        local pageIndex = 0
        --游戏接入 Player Network SDK INTLConfig.ini 中 INTL_URL 的 base64URL 编码
        local intl_cluster = StringUtil.Base64Encode(intl_url)
        --用户名（建议使用游戏内角色名字，如果传空字符串会导致角色名字在删号邮件里显示为空白）
        local user_name = string.urlencode(Server.SDKInfoServer:GetUserName())
        --游戏区域 ID（不存在时传空字符串）
        local area_id = Server.SDKInfoServer:GetZoneId()
        --游戏区 ID（不存在时传空字符串）
        local zone_id = "0"
        --语言类型（RFC 4646），例如 "en"。详情，请参见 语言类型定义
        local lang_type = LocalizeTool.GetCurrentCulture()

        local extraParam = "?pageIndex={0}&intl_cluster={1}&user_name={2}&area_id={3}&zone_id={4}&lang_type={5}"
        extraParam = StringUtil.SequentialFormat(extraParam, pageIndex, intl_cluster, user_name, area_id, zone_id, lang_type)
        local full_url = url .. extraParam
        loginfo("LoginLogic.DeleteAccount url:", full_url)
        Module.GCloudSDK:OpenUrl(full_url, nil, nil, nil, nil, nil, webBrowserData)
        if not IsHD() then
            --Module.Login:BackToLogin()
        end
    elseif IsBuildRegionGA() then
        --- GA根据国家地区选择注销协议
        -- local GarenaCode2Region = Facade.TableManager:GetTable("GarenaCode2Region")
        -- local GarenaRegion2Url = Facade.TableManager:GetTable("GarenaRegion2Url")
        -- local countryCode = string.format("%03d", Server.PayServer:GetRegistrationnumericCode())
        -- loginfo("LoginLogic.GADeleteAccount CountryCode:", countryCode)
        -- local region = "Other"
        -- for k, v in pairs(GarenaCode2Region) do
        --     if countryCode == v.CountryCode then
        --         region = v.Region
        --     end
        -- end

        -- local url = "https://csredirect.sea.df.garena.sg"
        -- for k, v in pairs(GarenaRegion2Url) do
        --     if region == v.Region then
        --         url = v.AccountDeletionUrl
        --     end
        -- end
        -- loginfo("LoginLogic.GADeleteAccount url:", url)

        --- She3.0 更新链接并增加参数
        local url = "https://delacc.garena.tw/deltaforce"

        Module.GCloudSDK:OpenUrl(url, nil, false, false, "", true, {
            useSDKWebBrowser = true,
            additionalParams = "access_token#area_id"
        })
        -- if PLATFORM_WINDOWS then
        --     Module.GCloudSDK:LaunchURL(url)
        -- else
        --     Module.GCloudSDK:OpenUrl(url)
        -- end
    end
end

function LoginLogic.ShowINTLPanel(intlPanelType)
    if IsBuildRegionGlobal() then
        local OnCreateCallback = function(uiIns)
            Module.Login.Field:SetINTLPanel(uiIns)
        end
        local panel = Module.Login.Field:GetINTLPanel()
        if panel and panel:IsInViewport() then
            panel:OnInitExtraData(intlPanelType, true)
        else
            Facade.UIManager:AsyncShowUI(UIName2ID.LoginInterfaceINTL, OnCreateCallback, nil, intlPanelType, false)
        end
    end
end

function LoginLogic.NativeLoginINTL(channel)
    if IsWeGameEnabled() then
        LoginLogic.InitWeGameLoginState()
    end
    if not channel or channel == EChannelType.kChannelNone then
        Facade.UIManager:AsyncShowUI(UIName2ID.MoreChiocesPopView)
    elseif not channel or channel == EChannelType.kChannelLevelInfinite then
        LoginLogic.ShowINTLPanel(Module.Login.Config.EINTLPanelType.Login)
    else
        LoginLogic.ShowINTLPanel(Module.Login.Config.EINTLPanelType.NativeLogin)
        UE.INTLSDKAPI.LoginForLI(channel, "", "")
    end
end

function LoginLogic.CloseINTLPanel()
    local panel = Module.Login.Field:GetINTLPanel()
    if panel then
        Facade.UIManager:CloseUI(panel)
        Module.Login.Field:SetINTLPanel(nil)
    end
end

function LoginLogic._SetInstallChannel()
    if IsHD() then
        if UGameLoginIns then
            local channelId = tostring(Server.SDKInfoServer:GetConfigChannelIdNumber())
            loginfo("LoginLogic._SetInstallChannel channelId:", channelId)
            UGameLoginIns:SetInstallChannel(channelId)
        end
    end
end

function LoginLogic.BindFirstLoginReward()
    if IsBuildRegionGlobal() then
        if UGameLoginIns then
            local areaIdStr = tostring(Server.SDKInfoServer:GetZoneId())
            local jsonStr = string.format('{"area_id":%s,"role_id":"%s","plat_id":%d,"zone_id":0}', areaIdStr, Server.SDKInfoServer:GetOpenIdStr(), Server.AccountServer:GetPlatIdType())
            UGameLoginIns:SetRewardExtraJson(jsonStr)
            UGameLoginIns:SendBindReward("110001", jsonStr)
        end
    end
end

return LoginLogic
