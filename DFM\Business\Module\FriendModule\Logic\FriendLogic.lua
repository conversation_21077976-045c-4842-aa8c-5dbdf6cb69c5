----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
----- LOG FUNCTION AUTO GENERATE END -----------



local FriendLogic = {}

FriendLogic.AddFriend = function(playerID)
    Server.FriendServer:AddFriend(playerID)
end

FriendLogic.DeleteFriend = function(playerID)
    Server.FriendServer:DeleteFriend(playerID)
end

FriendLogic.ProcessNewFriendReq = function(playerID,bAccept)
    Server.FriendServer:ProcessNewFriendReq(playerID,bAccept)
end

FriendLogic.IsPlayerInMatch = function(info)
    local playerState = Module.Social:GetOnlinePlayerStateFromStateCode(info.state)
    if playerState == GlobalPlayerStateEnums.EPlayerState_InMatch then
        return true
    end

    return false
end

-- 判断某好友是否离线或隐身
FriendLogic.IsFriendOffLineOrInvisible = function(state, playerId)
    if state == GlobalPlayerStateEnums.EPlayerState_Offline then
        return true
    elseif playerId and Server.FriendServer:GetFriendLoginInvisible(playerId) then
        return true
    end

    return false
end

FriendLogic.SetPlayerStateCode = function(widget, info, platWidget)
    if info.state == nil then
        return
    end
    local playerState = Module.Social:GetOnlinePlayerStateFromStateCode(info.state)
    --隐身下这里等同于离线
    if Server.FriendServer:GetFriendLoginInvisible(info.player_id) then
        playerState = GlobalPlayerStateEnums.EPlayerState_Offline
    end
    loginfo("FriendBox:SetPlayerStateCode" .. info.state)
    if playerState == GlobalPlayerStateEnums.EPlayerState_InTeam and info.member_num < 2 then
        playerState = GlobalPlayerStateEnums.EPlayerState_Online
    end
    if playerState == GlobalPlayerStateEnums.EPlayerState_InMatch then
        -- local duration = math.ceil((Facade.ClockManager:GetServerTimestamp() - info.fighting_time)/60.0)
        -- local modeName = Module.GameMode:GetStandardMapNameByMatchModeId(info.mode_info.match_mode_id)
        -- widget:SetText(string.format(Module.Social.Config.Loc.PlayerState_InMatch, modeName,duration))

        -- azhengzheng:覆写旧逻辑，目前不是单排的情况下需要显示队伍人数
        local mapName = Module.GameMode:GetStandardMapNameByMatchModeId(info.mode_info.match_mode_id)
        local playTime = math.ceil((Facade.ClockManager:GetServerTimestamp() - info.fighting_time) / 60.0)

        if not info.member_num then
            logerror("azhengzheng:not info member num")
            info.member_num = 1
        end

        if info.member_num == 0 then
            logerror("azhengzheng:info member num 0")
            info.member_num = 1
        end

        if info.member_num == 1 then
            widget:SetText(StringUtil.Key2StrFormat(Module.Friend.Config.FriendMatchInfo, {["mapName"] = mapName, ["playTime"] = playTime}))
        else
            local maxMember = info.mode_info.team_mode

            if info.mode_info_array then
                for _,mode in pairs(info.mode_info_array) do
                    maxMember = math.max(maxMember, mode.team_mode)
                end
            end

            widget:SetText(StringUtil.Key2StrFormat(Module.Friend.Config.FriendMatchInfoWithTeamMember,
            {["mapName"] = mapName, ["playTime"] = playTime, ["curMember"] = info.member_num, ["maxMember"] = maxMember}))
        end
    elseif playerState == GlobalPlayerStateEnums.EPlayerState_InTeam then
        local teamMax = info.mode_info.team_mode
        if info.mode_info_array then
            for _,mode in pairs(info.mode_info_array) do
                teamMax = math.max(teamMax, mode.team_mode)
            end
        end
        local teamNum = string.format("(%d/%d)", info.member_num, teamMax)
        widget:SetText(Module.Social:GetPlayerStateTxt(playerState) .. teamNum)
    elseif playerState == GlobalPlayerStateEnums.EPlayerState_Offline then
        widget:SetText(Module.Social:GetPlayerStateTxt(playerState))
        platWidget:Collapsed()
    else
        widget:SetText(Module.Social:GetPlayerStateTxt(playerState))
    end
    widget:SetColorAndOpacity(Module.Social:GetPlayerStateColor(playerState))
    platWidget:SetColorAndOpacity(Module.Social:GetPlayerStateIconColor(playerState))
    --- BEGIN MODIFICATION @ VIRTUOS: TRC
    if IsPS5() and info.plat == PlatIDType.Plat_Playstation then
        -- Override the color of platform icon as white if this friend is from ps5 platform too.
        platWidget:SetColorAndOpacity(FLinearColor(1,1,1,1))
    end
    --- END MODIFICATION
end

--- BEGIN MODIFICATION @ VIRTUOS
FriendLogic.IsConsolePlayer = function(platID)
    return platID == PlatIDType.Plat_Playstation or platID == PlatIDType.Plat_XBox
end

FriendLogic.GetPlatformIconPath = function(platID, isBlackVersion, checkFake)
    if not platID then
        return nil
    end

    local collection = {}
    if isBlackVersion then
        collection.platIcon = Module.Friend.Config.PlatIconBlack
        collection.gamepadIcon = Module.Friend.Config.PlatCommonImage.GamepadIconBlack
    else
        collection.platIcon = Module.Friend.Config.PlatIcon
        collection.gamepadIcon = Module.Friend.Config.PlatCommonImage.GamepadIcon
    end

    local isConsolePlayer = FriendLogic.IsConsolePlayer(platID)
    local localPlayerPlatID = Server.AccountServer.GetPlatIdType()
    local isPlayerFromDifferentPlatform = localPlayerPlatID ~= platID

    if checkFake == nil and isPlayerFromDifferentPlatform and Server.AccountServer:IsOpenFakePlat() then 
        return Module.Friend.Config.PlatIcon[localPlayerPlatID]
    end

    -- if this player is from different console platform, using common gamepad icon instead.
    if isConsolePlayer and isPlayerFromDifferentPlatform then
        return collection.gamepadIcon
    else
        return collection.platIcon[platID]
    end
end
--- END MODIFICATION


FriendLogic.GetOffLineTime = function(offLineTime)
    local nowTime = TimeUtil.GetCurrentTime()
    local timeNum = nowTime - offLineTime

    local MinuteNum = math.ceil(timeNum/60.0)
    local HourNum = math.ceil(timeNum/3600.0)
    local DayNum = math.ceil(timeNum/86400.0)
    
    if MinuteNum < 60 then
        return string.format(Module.Friend.Config.OfflineTime, string.format( Module.Friend.Config.minuteTime , MinuteNum ) )
    elseif HourNum < 24 then
        return string.format(Module.Friend.Config.OfflineTime,string.format( Module.Friend.Config.hourTime, HourNum)) 
    end
    DayNum = MathUtil.Clamp(DayNum, 1, 7)
    return string.format(Module.Friend.Config.OfflineTime, string.format( Module.Friend.Config.dayTime, DayNum)) 
end

FriendLogic.GetFriendRecentTime = function(game_time)
    local nowTime = TimeUtil.GetCurrentTime()

    nowDay = math.floor(nowTime/86400.0)
    gameDay = math.floor(game_time/86400.0)
    local diffDay = nowDay - gameDay
    if diffDay == 0 then
        return Module.Friend.Config.Today
    elseif diffDay == 1 then
        return Module.Friend.Config.Yesterday
    else
        diffDay = MathUtil.Clamp(diffDay, 2, 7)
        return string.format(Module.Friend.Config.ManyDaysAgo, math.ceil(diffDay))
    end
end

FriendLogic.SortFriendList = function(friendList)
    local function SortList(friendInfo1, friendInfo2)
        local state1 = Module.Social:GetOnlinePlayerStateFromStateCode(friendInfo1.state) > 0 and 1 or 0
        local state2 = Module.Social:GetOnlinePlayerStateFromStateCode(friendInfo2.state) > 0 and 1 or 0
        if state1 ~= state2 then
            return state1 > state2
        elseif friendInfo1.favorability ~= friendInfo2.favorability then
            return friendInfo1.favorability > friendInfo2.favorability
        --- BEGIN MODIFICATION @ VIRTUOS :
        elseif friendInfo1.PlatformId ~= nil and friendInfo2.PlatformId ~= nil then
            return friendInfo1.PlatformId > friendInfo2.PlatformId
        --- END MODIFICATION
        else
            return friendInfo1.last_logout_time > friendInfo2.last_logout_time
        end
    end
    table.sort(friendList, SortList)
end

-- FriendLogic.InitFriendRank = function(IconWidget, TextWidget, FriendInfo)
--     if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
--         if FriendInfo.sol_attended then
--             local rankInfo = Module.Ranking:GetMinorDataByScore(FriendInfo.sol_rank_score)
--             TextWidget:SetText(rankInfo.RankName)
--             IconWidget:SetRankingIconByScore(FriendInfo.sol_rank_score)
--         else
--             IconWidget:SetRankIconNone()
--             TextWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
--         end
--     else
--         if FriendInfo.mp_attended then
--             local rankInfo = Module.Tournament:GetRankDataByScore(FriendInfo.mp_rank_score)
--             TextWidget:SetText(rankInfo.Name)
--             IconWidget:SetTournamentIconByScore(FriendInfo.mp_rank_score)
--         else
--             IconWidget:SetRankIconNone()
--             TextWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
--         end
--     end
-- end

-- azhengzheng:覆写InitFriendRank,支持胜者为王段位显示
FriendLogic.InitFriendRank = function(rankIconWidget, rankNameWidget, friendInfo)
    -- azhengzheng:处于特勤处，显示SOL模式段位信息
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        if friendInfo.sol_attended then
            local minorData = Module.Ranking:GetMinorDataByScore(friendInfo.sol_rank_score)
            rankIconWidget:SetRankingIconByScore(friendInfo.sol_rank_score)
            rankNameWidget:SetText(minorData and minorData.RankName or "--")
        else
            rankIconWidget:SetRankIconNone()
            rankNameWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        end
    else
        if friendInfo.show_commander_rank_points == 0 then
            if friendInfo.mp_attended then
                local rankData = Module.Tournament:GetRankDataByScore(friendInfo.mp_rank_score)
                rankIconWidget:SetTournamentIconByScore(friendInfo.mp_rank_score)
                rankNameWidget:SetText(rankData and rankData.Name or "--")
            else
                rankIconWidget:SetRankIconNone()
                rankNameWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
            end
        else
            local commanderRankData = Module.Tournament:GetCommanderRankDataByScore(friendInfo.mp_commander_score)
            rankIconWidget:SetCommanderIconByScore(friendInfo.mp_commander_score)
            rankNameWidget:SetText(commanderRankData and commanderRankData.Name or "--")
        end
    end
end

-- FriendLogic.InitRankByIconInfo = function(IconWidget, TextWidget, PlayerIconInfo)
--     if PlayerIconInfo.rank_attended then
--         if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
--             local rankInfo = Module.Ranking:GetMinorDataByScore(PlayerIconInfo.rank_score)
--             IconWidget:SetRankingIconByScore(PlayerIconInfo.rank_score)
--             if rankInfo then
--                 TextWidget:SetText(rankInfo.RankName)
--             end
--         else
--             local rankInfo = Module.Tournament:GetRankDataByScore(PlayerIconInfo.rank_score)
--             IconWidget:SetTournamentIconByScore(PlayerIconInfo.rank_score)
--             if rankInfo then
--                 TextWidget:SetText(rankInfo.Name)
--             end
--         end
--     else
--         IconWidget:SetRankIconNone()
--         TextWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
--     end
-- end

-- azhengzheng:覆写InitRankByIconInfo，支持胜者为王段位显示
FriendLogic.InitRankByIconInfo = function(rankIconWidget, rankNameWidget, rankInfo)
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        if rankInfo.rank_attended then
            local minorData = Module.Ranking:GetMinorDataByScore(rankInfo.rank_score)
            rankIconWidget:SetRankingIconByScore(rankInfo.rank_score)
            rankNameWidget:SetText(minorData and minorData.RankName or "--")
        else
            rankIconWidget:SetRankIconNone()
            rankNameWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        end
    else
        if rankInfo.show_commander_rank_points and rankInfo.show_commander_rank_points == 1 then
            local commanderRankData = Module.Tournament:GetCommanderRankDataByScore(rankInfo.mp_commander_score or 0)
            rankIconWidget:SetCommanderIconByScore(friendInfo.mp_commander_score or 0)
            rankNameWidget:SetText(commanderRankData and commanderRankData.Name or "--")
        else
            if rankInfo.rank_attended then
                local rankData = Module.Tournament:GetRankDataByScore(rankInfo.rank_score)
                rankIconWidget:SetTournamentIconByScore(rankInfo.rank_score)
                rankNameWidget:SetText(rankData and rankData.Name or "--")
            else
                rankIconWidget:SetRankIconNone()
                rankNameWidget:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
            end
        end
    end
end

FriendLogic.CheckCanReservation = function(friendInfo)
    -- 首先检查对方队伍是否已满
end

return FriendLogic
