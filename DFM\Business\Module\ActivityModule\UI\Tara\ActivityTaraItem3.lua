----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--评论
---@class ActivityTaraItem3 : LuaUIBaseView
local ActivityTaraItem3 = ui("ActivityTaraItem3")
local ActivityTaraHead  = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraHead"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local Config = Module.Activity.Config
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"

function ActivityTaraItem3:Ctor()
    self._wtFHead = self:Wnd("NewWidgetBlueprint", ActivityTaraHead)
    self._wtFNews = self:Wnd("DFRichTextBlock_0" , UITextBlock)
    self._wtFName = self:Wnd("DFTextBlock_82"    , UITextBlock)
    self._wtDTime = self:Wnd("DFTextBlock_2"     , UITextBlock)
    self._wtDLine = self:Wnd("DFImage_265"       , UIImage)
    self._wtDFBox = UIUtil.WndWaterfallScrollBox(self, "DFScrollGridBox_66", self._OnWaterfallCount, self._OnWaterfallWidget)
    self._wtDFBox:SetVisibility(ESlateVisibility.Collapsed)
    --领奖
    self._wtDFCanvasPanel = self:Wnd("DFCanvasPanel_7", UIWidgetBase)
    self._wtDFButton = self:Wnd("DFButton", UIButton)
    self._wtDFButton:Event("OnClicked"    , self._OnBtnRewardClicked, self)

    self._wtDFButton:Event("OnHovered"        , self._OnHovered, self)
    self._wtDFButton:Event("OnUnHovered"      , self._OnUnHovered, self)

    self._wtNeBtn = self:Wnd("DFButton_0"  , UIButton)
    self._wtNeBtn:Event("OnClicked"        , self._OnClicked, self)

    self._wtNeBtn:Event("OnHovered"        , self._OnHovered, self)
    self._wtNeBtn:Event("OnUnHovered"      , self._OnUnHovered, self)

    self._wtDFBtn = self:Wnd("DFButton_176", UIButton)
    self._wtDFBtn:Event("OnClicked"        , self._OnClicked, self)

    self._wtDFBtn:Event("OnHovered"        , self._OnHovered, self)
    self._wtDFBtn:Event("OnUnHovered"      , self._OnUnHovered, self)
end

--是否手柄
function ActivityTaraItem3:IsGamepad()
    return IsHD() and WidgetUtil.IsGamepad()
end

function ActivityTaraItem3:IsReward()
    local data = self._data
    if data then
        return not data.received and self._rewards and #self._rewards > 0
    end
    return false
end

function ActivityTaraItem3:IsRecoverable()
    local data = self._data
    if data then
        return not data.replied and data.contents and #data.contents > 0
    end
    return false
end

function ActivityTaraItem3:IsUnLock()
    return false
end

function ActivityTaraItem3:_OnBtnRewardClicked()
    local atc_id = self._activityID
    local data = self._data
    if atc_id and data and self:IsReward() then
        Server.ActivityServer:SendTaraMinRewardReq(atc_id, data.id)
    end
end

function ActivityTaraItem3:_OnUnHovered()
end

function ActivityTaraItem3:_OnHovered()
    if self:IsGamepad() then
        local data = nil
        local index = nil
        if self:IsRecoverable() then
            data  = self._data
        end
        if self:IsReward() then
            index = self._index
        end
        Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Comment, self, data, index, self._index)
    end
end

function ActivityTaraItem3:_OnClicked()
    local data = self._data
    if data then
        --手柄领取奖励
        if self:IsReward() then
            self:_OnBtnRewardClicked()
            return
        end
        --回复干员
        if self:IsRecoverable() then
            Config.evtOpenTaraPanel:Invoke(self._activityID, data, 2)
        end
    end
end

function ActivityTaraItem3:_GetHeroHead(heroId)
    local data = self._data
    if data and data.heroHeads then
        local head = data.heroHeads[heroId]
        if head then
            return head
        end
    end
    return nil
end

function ActivityTaraItem3:InitData(activityID, data, index, list, count)
    self._activityID = activityID
    self._data = data
    self._index = index
    self._list = list
    if data then
        local hero = ActivityLogic.GetListData(list, "heroId", data.heroId)
        local path, name
        local head = self:_GetHeroHead(data.heroId)
        if hero.path == nil and head then
            hero.path = head.image
            hero.name = head.hero_name
        end
        if hero then
            path = hero.path or data.playerPath
            name = hero.name or data.playerName
        end
        self._wtFHead:InitData(activityID, {path = path}, index)
        self._wtFHead:SetHeadBtn(false)
        self._wtFName:SetText(ActivityLogic.GetText(name))
        self._wtFNews:SetText(ActivityLogic.LocalizeText(data.comment))
        if data.atId ~= 0 then
            local nickName = nil
            local atName = ActivityLogic.GetListData(list, "heroId", data.atId).name
            if atName then
                nickName = atName
            end
            if data.atId == data.playerId then
                nickName = data.playerName
            end
            if nickName then
                self._wtFNews:SetText(string.format("<customstyle color=\"Color_Highlight02\">@%s:</> %s", ActivityLogic.LocalizeText(nickName) or "", ActivityLogic.LocalizeText(data.comment) or ""))
            end
        end
        self._wtDLine:SetVisibility(index ~= count and ESlateVisibility.HitTestSelfOnly or ESlateVisibility.Collapsed)
        if self.SetType then
            --是否可回复
            self:SetType(self:IsRecoverable() and 0 or 1)
            --悬浮透明度
            self._wtDFBtn:SetRenderOpacity((self:IsRecoverable() or self:IsReward()) and 1 or 0)
        end
        self._wtDTime:SetText(data.time or "")
        self._rewards = data.rewards
        --没有奖励需要隐藏
        self:_IsReceiveReward()
        Timer.CancelDelay(self._timerIns)
    end
end

function ActivityTaraItem3:_IsReceiveReward()
    local data = self._data
    if data and data.rewards then
        if self:IsReward() then
            self._wtDFCanvasPanel:SelfHitTestInvisible()
            return
        end
    end
    self._wtDFCanvasPanel:Collapsed()
end

function ActivityTaraItem3:OnShowBegin()
    self:_AddEventListener()
end

function ActivityTaraItem3:OnHideBegin()
    self:RemoveAllLuaEvent()
end

function ActivityTaraItem3:OnClose()
    self._timerIns = nil
end

function ActivityTaraItem3:SetIndex(index)
    self._index = index
end

function ActivityTaraItem3:SetIsPlayAnim(isAnim)

end

function ActivityTaraItem3:_AddEventListener()
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
end

--发起器
function ActivityTaraItem3:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Anim then
        self:_OnPlayInAnim(...)
    elseif handleType == ETaraHandleType.Receive then
        self:_ReceiveAReward(...)
    end
end

function ActivityTaraItem3:_ReceiveAReward(index)
    if self._index == index then
        self:_OnBtnRewardClicked()
    end
end

function ActivityTaraItem3:_OnPlayInAnim(time, count)
    local index = self._index
    if index and time and count and index <= count then
        local func = function()
            --播放音效
            Facade.SoundManager:PlayUIAudioEvent("UI_FriendCircl_Info_Popup")
            self:Visible()
            self:PlayAnimation(self.WBP_PatrolAsala_CommentItem_Q1ZNK_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
        self:Hidden()
        self._timerIns = Timer.DelayCall(time * index, func, self)
    end
end

function ActivityTaraItem3:_OnWaterfallCount()
    if self._rewards then
        return #self._rewards
    end
    return 0
end

function ActivityTaraItem3:_OnWaterfallWidget(position, itemWidget)
    local index = position + 1
    if self._rewards and itemWidget then
        local data = self._data
        local itemData = ActivityLogic.GetWeaponSkinItem(self._rewards[index])
        if itemData and data then
            itemWidget:InitItem(itemData)
            --有奖励默认可领取
            local CompId, SlotPos, uiNavId
            if data.received then
                uiNavId = UIName2ID.IVGetMaskComponent
                CompId  = EComp.ExpireMask
                SlotPos = EIVSlotPos.MaskLayer
            else
                uiNavId = UIName2ID.IVCommonItemAnimComp
                CompId  = EComp.UseAnim
                SlotPos = EIVSlotPos.MaskLayer
            end
            local addWidget = itemWidget:FindOrAdd(CompId, uiNavId, SlotPos, EIVCompOrder.Order1)
            if addWidget then
                local animName = addWidget.WBP_CommonItemTemplate_in_special_01
                if animName then
                    addWidget:PlayAnimation(animName, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
                end
            end
            itemWidget:EnableComponent(EComp.ExpireMask, CompId == EComp.ExpireMask)
            itemWidget:EnableComponent(EComp.UseAnim   , CompId == EComp.UseAnim)
            itemWidget:BindCustomOnClicked(SafeCallBack(self._OnRewardClicked, self, data.received and itemData or data, itemWidget))
        end
    end
end

function ActivityTaraItem3:_OnRewardClicked(itemData, btn)
    if itemData then
        if itemData.itemMainType then
            --打开详情页
            if itemData.itemMainType == EItemType.WeaponSkin then
                Module.Collection:ShowWeaponSkinDetailPage(itemData, nil)--武器皮肤(包括刀皮)
            elseif itemData.itemMainType == EItemType.Adapter then
                Module.Collection:ShowHangingDetailPage(itemData   , nil)--配件(挂饰)
            elseif itemData.itemMainType == EItemType.Fashion then
                Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, itemData.id)--干员皮肤
            else
                Module.ItemDetail:OpenItemDetailPanel(itemData, btn)--详情页
            end
        else
            --请求领奖
            if self._activityID and self._data then
                Server.ActivityServer:SendTaraMinRewardReq(self._activityID, self._data.id)
            end
        end
    end
end

return ActivityTaraItem3