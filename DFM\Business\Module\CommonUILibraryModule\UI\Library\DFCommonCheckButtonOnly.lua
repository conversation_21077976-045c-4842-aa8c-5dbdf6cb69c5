----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonUILibrary)
local orilogerror = logerror
logerror = function(...)
    if not VersionUtil.IsShipping() then
        orilogerror(...)
    end
end
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- Library : DFCommonCheckButtonOnly
--- 复杂基类控件
--- *必须包含名称为DFCommonCheckButton的复杂基类按钮
--------------------------------------------------------------------------
---@class DFCommonCheckButtonOnly : LuaUIBaseView
DFCommonCheckButtonOnly = ui('DFCommonCheckButtonOnly')
ECheckButtonState = import"ECheckButtonState"
require("DFM.YxFramework.Managers.Resource.Util.ResImageUtil")

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UIThemeUtil = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"

function DFCommonCheckButtonOnly:Ctor()
    if DFHD_LUA == 1 then
        self.wtCommonCheckBtn = self:Wnd("DFCommonCheckButton_PCOnly", UIWidgetBase)
    end
    if self.wtCommonCheckBtn == nil then
        self.wtCommonCheckBtn = self:Wnd("DFCommonCheckButton", UIWidgetBase)
    end

    self._wtReddotParent = self:Wnd("DFCanvasPanel_0",UIWidgetBase)

    self:Event("OnGetCanBeChecked", self.IsCanBeChecked, self)
    self:Event("OnCheckFailed", self._OnCheckFailed, self)
    self:Event("OnCheckedHovered", self._OnBtnItemHover,self)
    self:Event("OnUncheckedHovered", self._OnBtnItemHover,self)
    UIThemeUtil.CheckIfAutoApplyTheme(self.wtCommonCheckBtn)

    -- self.wtShadowImg = self.wtCommonCheckBtn:Wnd("DFThemeShadowImage", UIImage)
    -- if self.wtShadowImg then
    --     UIThemeUtil.CheckIfAutoApplyTheme(self.wtShadowImg)
    -- end
end

function DFCommonCheckButtonOnly:OnInitExtraData(tabUIContentData)
    self.tabUIContentData = tabUIContentData
end

function DFCommonCheckButtonOnly:OnOpen()
    if self.tabUIContentData then
        self.tabUIContentData:UpdateTabByLuaTabUIContent(self)
    end
end

function DFCommonCheckButtonOnly:Event(delegateName, fCallback, caller, ...)
    if self.wtCommonCheckBtn then
        logframe('Start binding!!! DFCommonCheckButtonOnly:Event(delegateName, fCallback, caller, ...)', delegateName, fCallback, caller, " self:", self)
        self.wtCommonCheckBtn:Event(delegateName, fCallback, caller,  ...)
    else
        logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:Event(delegateName, fCallback, caller, ...)', " self:", self)
    end
end

function DFCommonCheckButtonOnly:RemoveEvent(delegateName)
    if self.wtCommonCheckBtn then
        logframe('Start Removing!!! DFCommonCheckButtonOnly:RemoveEvent(delegateName)', delegateName, " self:", self)
        self.wtCommonCheckBtn:RemoveEvent(delegateName)
    else
        logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:RemoveEvent(delegateName)', " self:", self)
    end
end

function DFCommonCheckButtonOnly:SetMainTitle(text)
    if text then
        self.btnMainTitle = text
        if self.BP_SetMainTitle then
            self:BP_SetMainTitle(text)
        else
            if self.wtCommonCheckBtn then
                logframe('self.wtCommonCheckBtn:SetMainTitleText4AllState(text)', text)
                self.wtCommonCheckBtn:SetMainTitleText4AllState(text)
            else
                logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:SetMainTitleText4AllState(text)')
            end
        end
    end
end

function DFCommonCheckButtonOnly:SetIsChecked(bChecked)
    if self.wtCommonCheckBtn then
        logframe('self.wtCommonCheckBtn:SetIsChecked(bChecked)', bChecked)
        if bChecked then
            self.wtCommonCheckBtn:ManuelSetCheckButtonState(ECheckButtonState.Checked)
        else
            self.wtCommonCheckBtn:ManuelSetCheckButtonState(ECheckButtonState.Unchecked)
        end
    else
        logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:SetIsChecked(bChecked)', bChecked)
    end
end

function DFCommonCheckButtonOnly:IsChecked()
    if self.wtCommonCheckBtn then
        logframe('self.wtCommonCheckBtn:IsChecked()', self.wtCommonCheckBtn:IsChecked())
        return self.wtCommonCheckBtn:IsChecked()
    else
        logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:IsChecked()')
        return false
    end
end

function DFCommonCheckButtonOnly:SetIsEnabledStyle(bEnableStyle)
    if self.wtCommonCheckBtn then
        self.wtCommonCheckBtn:SetIsEnabledStyle(bEnableStyle)
    end
    
    --BEGIN MODIFICATION @ VIRTUOS : Input extension
    if IsHD() then
        if self.wtKeyIconBox then
            self.wtKeyIconBox:SetKeyStyleEnable(bEnableStyle)
        end
    end
    --END MODIFICATION
end

function DFCommonCheckButtonOnly:SetIsLocked(bLocked)
    if self.wtCommonCheckBtn and self.wtCommonCheckBtn.SetIsLocked ~= nil then
        logframe('self.wtCommonCheckBtn:SetIsLocked(bLocked)', bLocked)
        self.wtCommonCheckBtn:SetIsLocked(bLocked)
        self.wtCommonCheckBtn:SetIsEnabledStyle(not bLocked)
    else
        logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:SetIsLocked(bLocked)', bLocked)
    end
end

function DFCommonCheckButtonOnly:SetIsShowHighlight(bShowHighlight)
    if self.wtCommonCheckBtn and self.wtCommonCheckBtn.SetIsShowHighlight ~= nil then
        logframe('self.wtCommonCheckBtn:SetIsShowHighlight(bShowHighlight)', bShowHighlight)
        self.wtCommonCheckBtn:SetIsShowHighlight(bShowHighlight)
    else
        logerror('Cant find Btn named [DFCommonCheckButton], Failed to DFCommonCheckButtonOnly:SetIsShowHighlight(bShowHighlight)', bShowHighlight)
    end
end

function DFCommonCheckButtonOnly:ShowReddot(bShow)
    if bShow then
        if self._wtReddot==nil then
            self._wtReddot = Module.ReddotTrie:CreateReddotIns(self._wtReddotParent)
            self._wtReddot:SetPlaceMode(EReddotPlaceMode.RightTop)
            self._wtReddot:SetReddotVisible(true,EReddotType.Normal)
        else
            self._wtReddot:SetReddotVisible(true,EReddotType.Normal)
        end
    else
        if self._wtReddot then
            self._wtReddot:DestroyReddotWidget()
            -- Module.Reddot:UnRegister(self._wtReddot)
            self._wtReddot=nil
        end
    end
end

function DFCommonCheckButtonOnly:AsyncSetImageIconPathAllState(imagePath, bAutoResize)
    return ResImageUtil.AsyncLoadImgObjByPath(imagePath, bAutoResize, self.SetImageIconAllState, self)
end

function DFCommonCheckButtonOnly:SetImageIconAllState(imageAsset, bAutoResize)
    bAutoResize = setdefault(bAutoResize, true)
    -- if self.bSetImgByBP then
    --     self:SetCppValue("bSetImgByBP", false)
    -- end
    if self.wtCommonCheckBtn then
        if imageAsset then
            local classname = LAI.GetObjectClassName(imageAsset)
            if classname == "PaperSprite" then
                self.wtCommonCheckBtn:SetImageIconFromAtlasInterface4AllState(imageAsset, bAutoResize)
            elseif classname == "Texture2D" then
                self.wtCommonCheckBtn:SetImageIcon4AllState(imageAsset, bAutoResize)
            else
                if isvalid(UIImage.IconWaitingObj) and LAI.GetObjectClassName(imageAsset) == "Texture2D" then
                    self.wtCommonCheckBtn:SetImageIcon4AllState(imageAsset, bAutoResize)
                end
                logbox('DFCommonCheckButtonOnly:SetImageIconAllState 请检查资源格式是否正确———— classname:', classname, 'imageAsset:', imageAsset, debug.traceback())
            end
        end
    end
end

function DFCommonCheckButtonOnly:SetImageIconState(paperSpriteRes, state)
    if self.wtCommonCheckBtn then
        self.wtCommonCheckBtn:SetImageIconFromAtlasInterface4ButtonState(paperSpriteRes, state, true)
    end
end

--- 设置CheckBox开启状态
---@generic T
---@param fConditionFunc function inst:T, groupIdx:number
---@param inst T
function DFCommonCheckButtonOnly:SetCanBeChecked(fConditionFunc, caller)
    if fConditionFunc then
	    self._canBeCheckHandler = SafeCallBack(fConditionFunc, caller)
    else
        self._canBeCheckHandler = nil
    end
end

--- 设置Check开启状态
function DFCommonCheckButtonOnly:IsCanBeChecked()
	if self._canBeCheckHandler then
		return self._canBeCheckHandler()
	end
    if self.wtCommonCheckBtn.bBPLockState then
        return false
    end
	return true
end

function DFCommonCheckButtonOnly:SetCheckFailedHandler(fHandler, caller)
    if fHandler then
	    self._CheckFailedHandler = SafeCallBack(fHandler, caller)
    else
        self._CheckFailedHandler = nil
    end
end


function DFCommonCheckButtonOnly:_OnCheckFailed()
    if self._CheckFailedHandler then
        self._CheckFailedHandler()
    end
end

function DFCommonCheckButtonOnly:OnClose()
end

function DFCommonCheckButtonOnly:OnShowBegin()
    if self.wtCommonCheckBtn and self.wtCommonCheckBtn.OnShowBegin then
        self.wtCommonCheckBtn:OnShowBegin()
    end
end

function DFCommonCheckButtonOnly:OnShow()
    if self.wtCommonCheckBtn and self.wtCommonCheckBtn.OnShowBegin then
        self.wtCommonCheckBtn:OnShow()
    end

    if IsHD() then
        self.wtKeyIconBox = self.wtCommonCheckBtn:Wnd("KeyIcon", HDKeyIconBox)
        -- if self.wtKeyIconBox then
        --     if WidgetUtil.IsGamepad() then
        --         self.wtKeyIconBox:SelfHitTestInvisible()
        --     else
        --         self.wtKeyIconBox:Collapsed()
        --     end
        -- end
    end
end

function DFCommonCheckButtonOnly:OnHideBegin()
    if self.wtCommonCheckBtn and self.wtCommonCheckBtn.OnShowBegin then
        self.wtCommonCheckBtn:OnHideBegin()
    end
end

function DFCommonCheckButtonOnly:OnHide()
    if self.wtCommonCheckBtn and self.wtCommonCheckBtn.OnShowBegin then
        self.wtCommonCheckBtn:OnHide()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
-- 手柄快捷键模拟鼠标点击
function DFCommonCheckButtonOnly:SelfClick()
    if IsHD() and self.wtCommonCheckBtn and self.wtCommonCheckBtn:IsVisible() then
        self.wtCommonCheckBtn:NavigationClick()
    end
end

-- Input extension
function DFCommonCheckButtonOnly:SetDisplayInputAction(displayInputAction, bShowNoneKey, keyIconHeight, bOnlyDisplayOnGamepad)
    if IsHD() then
        if not self.wtKeyIconBox then
            self.wtKeyIconBox = self.wtCommonCheckBtn:Wnd("KeyIcon", HDKeyIconBox)
        end
        
        if self.wtKeyIconBox then
            --设置是否只在Gamepad上显示
            self.wtKeyIconBox:SetOnlyDisplayOnGamepad(bOnlyDisplayOnGamepad)
            --设置当前KeyIcon绑定的Action
            self.wtKeyIconBox:InitByDisplayInputActionName(displayInputAction, bShowNoneKey, keyIconHeight, true)
        else
            logerror('Cant find KeyIcon named [KeyIcon], Failed to DFCommonButtonOnly:SetDisplayInputAction(displayInputAction,bShowNoneKey, keyIconHeight)', " self:", self)
        end
    end
end

-- 自动检测是否是长按并显示长按图标
function DFCommonCheckButtonOnly:SetDisplayInputActionWithLongPress(actionHandle, caller, displayInputAction, bShowNoneKey, keyIconHeight, bOnlyDisplayOnGamepad)
    if IsHD() then
        self:SetDisplayInputAction(displayInputAction, bShowNoneKey, keyIconHeight, bOnlyDisplayOnGamepad)
        self:SetLongPressBinding(actionHandle, caller, displayInputAction)
    end
end

--长按显示绑定
function DFCommonCheckButtonOnly:SetLongPressBinding(ActionHandle, caller, actionName)
    if not actionName then
        return
    end
    -- 处理非常按按键提示
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    local mapping = inputMonitor:GetBestKeyMappingForDisplayAction(actionName)
    if not mapping.bHold then
        if self.wtKeyIconBox then
            self.wtKeyIconBox:BP_ShowHoldProgressBarTips(false)
        end

        if self._HoldInputHandle ~= -1 then
            self:RemoveHoldInputActionBinding(self._HoldInputHandle)
            self._HoldInputHandle = nil
        end
        return
    end
    -- 处理常按按键提示
    if ActionHandle then
        self._HoldInputHandle = ActionHandle
        if self.wtKeyIconBox then
            self.wtKeyIconBox:BP_ShowHoldProgressBarTips(true)
            caller:AddHoldInputActionProgressedBinding(ActionHandle, self.OnConfirmBtnInLongPressing, self)
            caller:AddHoldInputActionReleaseBinding(ActionHandle, self.OnConfirmBtnLongPressFinished, self)
        end
    end
end

function DFCommonCheckButtonOnly:OnConfirmBtnInLongPressing(Percent)
    if self.wtKeyIconBox then
        self.wtKeyIconBox:BP_UpdateProgressBar(Percent) 
    end
end

function DFCommonCheckButtonOnly:OnConfirmBtnLongPressFinished()
    if self.wtKeyIconBox then
        self.wtKeyIconBox:BP_UpdateProgressBar(0)
    end
end

function DFCommonCheckButtonOnly:_OnBtnItemHover()
    if self._fHoverCallbackIns then
        self._fHoverCallbackIns()
    end
end

function DFCommonCheckButtonOnly:BindHoverCallback(fHoverCallbackIns)
    self._fHoverCallbackIns = fHoverCallbackIns
end
--- END MODIFICATION

return DFCommonCheckButtonOnly