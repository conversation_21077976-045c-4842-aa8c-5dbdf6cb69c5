----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------



---------------------------------------------------------------------------------
--- SOL
---------------------------------------------------------------------------------
UITable[UIName2ID.AbilityIconView] =
{
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.AbilityIconView",
    BPKey = "WBP_AbilityIconView",
}

--干员解锁panel
UITable[UIName2ID.HeroUnLockPanel] =
{
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroUnLock.HeroUnLockPanel",
    BPKey = "WBP_Hero_BuyHeroPop",
    Anim = {
        FlowInAni = "WBP_Hero_BuyHeroPop_in",
        FlowOutAni = "WBP_Hero_BuyHeroPop_out",
    },
    IsModal = true,
}

--干员按钮控件
UITable[UIName2ID.HeroApproach] =
{
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroUnLock.HeroApproach",
    BPKey = "WBP_Hero_Approach",
}

--干员提示控件
UITable[UIName2ID.AssemblyTips] =
{
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroUnLock.AssemblyTips",
    BPKey = "WBP_AssemblyMapNeed",
}

local HeroConfig = {
    Events = {
        --干员展示界面关闭事件
        evtHeroShowOnClose = LuaEvent:NewIns("evtHeroShowOnClose"),
        --返回键是否启用事件
        evtChangedBackESC = LuaEvent:NewIns("evtChangedBackESC"),
        evtSelectHeroIdForShowChanged = LuaEvent:NewIns("evtSelectHeroIdForShowChanged"),

        evtMainContentPanelShowChanged = LuaEvent:NewIns("evtMainContentPanelShowChanged"),

        evtOnHeroItemClicked = LuaEvent:NewIns("evtOnHeroItemClicked"),

        evtOnBadgeClicked = LuaEvent:NewIns("evtOnBadgeClicked"),
        evtOnCareerBadgeClicked = LuaEvent:NewIns("evtCareerOnBadgeClicked"),

        evtOnTitleClicked = LuaEvent:NewIns("evtOnTitleClicked"),
        evtOnDeSelectAllItem = LuaEvent:NewIns("evtOnDeSelectAllItem"),

        -- 干员名片点击事件
        evtOnHeroCardItemClicked = LuaEvent:NewIns("evtOnHeroCardItemClicked"),

        -- 乾員商業化輪盤事件
        evtOnEmotIconSelected = LuaEvent:NewIns("evtOnEmotIconSelected"),
        evtOnEmotIconHovered = LuaEvent:NewIns("evtOnEmotIconHovered"),
        evtOnEmotIconUnHovered = LuaEvent:NewIns("evtOnEmotIconUnHovered"),

        evtOnEmotIconClickedInRoulette = LuaEvent:NewIns("evtOnEmotIconClickedInRoulette"),

        -- 名片动画播放结束
        evtOnCardAnimFinsh = LuaEvent:NewIns("evtOnCardAnimFinsh"),

        evtOnHeroViewSkillBtnClicked = LuaEvent:NewIns("evtOnHeroViewSkillBtnClicked"),
        evtOnHeroTopSkillPanelShow = LuaEvent:NewIns("evtOnHeroTopSkillPanelShow"),
        evtOnHeroTopSkillPanelClose = LuaEvent:NewIns("evtOnHeroTopSkillPanelClose"),
        evtOnHeroShowSkillBtnClicked = LuaEvent:NewIns("evtOnHeroShowSkillBtnClicked"),
	    evtHeroSkillVideoPanelShow= LuaEvent:NewIns("evtHeroSkillVideoPanelShow"),

        -- hover干员改变事件
        evtOnHoverHeroChanged = LuaEvent:NewIns("evtOnHoverHeroChanged"),

        evtOnChangedShowRecruitmentHero = LuaEvent:NewIns("evtOnShowRecruitmentHeroChange"), --当前展示的招募干员id发生改变
        
        -- BEGIN MODIFICATION @ VIRTUOS : 用来保存当前选中勋章的事件
        evtOnBadgeSelected = LuaEvent:NewIns("evtOnBadgeSelected"),
        evtOnBadgeSlotSelected = LuaEvent:NewIns("evtOnBadgeSlotSelected"), -- 在名片勋章槽位focus时的事件
        evtOnPersonalBadgeSelected = LuaEvent:NewIns("evtOnPersonalBadgeSelected"), --在成就勋章自定义界面被选中的事件
        evtOnCareerBadgeSelected = LuaEvent:NewIns("evtOnCareerBadgeSelected"), --在职业勋章界面被focus的事件
        -- END MODIFICATION

        -- BEGIN MODIFICATION @ VIRTUOS : 用来通知干员表情界面的事件通知
        evtOnEmotRouletteOpened = LuaEvent:NewIns("evtOnEmotRouletteOpened"), -- 干员表情转盘界面打开时的事件
        evtOnEmotRouletteClosed = LuaEvent:NewIns("evtOnEmotRouletteClosed"), -- 干员表情转盘界面关闭时的事件
        -- END MODIFICATION

        evtOnChangedSelectedHeroPropsScheme = LuaEvent:NewIns("evtOnChangedSelectedHeroPropsScheme"), -- 所选的兵种道具玄学方案变更
        evtHeroListTopPanelOnShowBegin = LuaEvent:NewIns("evtHeroListTopPanelOnShowBegin"),
        evtOnSelectedArchiveTypeChanged = LuaEvent:NewIns("evtOnSelectedArchiveTypeChanged")
    },

    --商业化道具品阶图标
    QualityIconMapping = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0402.Common_ItemProp_Icon_0402'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0403.Common_ItemProp_Icon_0403'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0404.Common_ItemProp_Icon_0404'",
        [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0405.Common_ItemProp_Icon_0405'",
        [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0406.Common_ItemProp_Icon_0406'",
    },

    BottomBarState = {
        Common = 1,
        Hovering = 2,
        Lock = 3,
        Quit = 4,
        HideSkill = 5
    },

    DefaultCardPath = "Texture2D'/Game/UI/UIAtlas/Icon/HeroBusinessCard/Card_Default.Card_Default'",
}


HeroConfig.Loc = {
    ContinueToBuy = NSLOCTEXT("HeroModule", "Lua_ContinueToBuy", "当前干员可以通过<customstyle color=\"Color_Highlight01\">招募任务</>获取，是否继续购买？"),
    HeroNotObtained = NSLOCTEXT("HeroModule", "Lua_HeroNotObtained", "获得干员后可使用"),
    HeroGoToObtain = NSLOCTEXT("HeroModule", "Lua_HeroGoToObtain", "前往获取"),

    HeroSOLUnlock = NSLOCTEXT("HeroModule", "Lua_HeroSOLUnlock", "行动等级%s级解锁通行证"),
    HeroMPUnlock = NSLOCTEXT("HeroModule", "Lua_HeroMPUnlock", "战场等级%s级解锁通行证"),

    HeroLimitedTimeOpening = NSLOCTEXT("HeroModule", "Lua_HeroLimitedTimeOpening", "限时"),
    HeroFashionContent = NSLOCTEXT("HeroModule", "Lua_HeroFashionContent", "时装内容"),
    HeroPainting = NSLOCTEXT("HeroModule", "Lua_HeroPainting", "包含以下涂装"),
    HeroPaintingTips = NSLOCTEXT("HeroModule", "Lua_HeroPaintingTips", "配置外观后以下涂装同时生效"),
    HeroFashionTips = NSLOCTEXT("HeroModule", "Lua_HeroFashionTips", "时装效果在不同模式下存在表现差异：\n危险行动模式下，当前外观效果仅小队成员可见。\n全面战场模式下，当前外观效果全局可见。"),

    GoToRecharge = NSLOCTEXT("HeroModule", "Lua_GoToRecharge", "充值"),
    RechargeTips = NSLOCTEXT("HeroModule", "Lua_RechargeTips", "您当前的三角币不足，是否前往充值？"),
    HeroSpace = NSLOCTEXT("HeroModule", "Lua_HeroJumpAnim", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/>%s"),
    HeroSkipAnim = NSLOCTEXT("HeroModule", "Lua_HeroSkipAnim", "跳过动画"),
    HeroContinue = NSLOCTEXT("HeroModule", "Lua_HeroContinue", "继续"),
    HeroUnLock = NSLOCTEXT("HeroModule", "Lua_HeroUnLock", "干员解锁"),
    HeroNewHero = NSLOCTEXT("HeroModule", "Lua_HeroNewHero", "获得新干员"),
    HeroNewAction = NSLOCTEXT("HeroModule", "Lua_HeroNewAction", "获得动作"),
    HeroNewSkin = NSLOCTEXT("HeroModule", "Lua_HeroNewSkin", "获得新外观"),
    HeroNewWatch = NSLOCTEXT("HeroModule", "Lua_HeroNewWatch", "获得手表"),
    HeroIsLock = NSLOCTEXT("HeroModule", "Lua_HeroIsLock", "未解锁"),
    HeroCode = NSLOCTEXT("HeroModule", "Lua_HeroCode", "代号：%s"),
    HeroPrice = NSLOCTEXT("HeroModule", "Lua_HeroPrice", "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"%s\" align=\"0\"/> %s"),
    HeroPayPrice = NSLOCTEXT("HeroModule", "Lua_HeroPayPrice", "将支付：<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"%s\" align=\"0\"/> <customstyle color=\"C001\">%s</> <dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"%s\" align=\"0\"/> <customstyle color=\"C001\">%s</>"),
    HeroGapPrice = NSLOCTEXT("HeroModule", "Lua_HeroGapPrice", "缺少：<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"%s\" align=\"0\"/> <customstyle color=\"Color_CampEnemy\">%s</>"),

    HeroTitle = NSLOCTEXT("HeroModule", "Lua_HeroTitle", "特战干员"),
    HeroSkillTitle = NSLOCTEXT("HeroModule", "Lua_HeroSkillTitle", "干员特长"),
    HeroAppearanceTitle = NSLOCTEXT("HeroModule", "Lua_HeroAppearanceTitle", "自定义"),
    HeroUnlockConfirmTitleText = NSLOCTEXT("HeroModule", "Lua_Hero_Text_UnlockConfirmTitle", "解锁英雄"),
    HeroRecruitment = NSLOCTEXT("HeroModule", "Lua_HeroRecruitment", "干员招募"),
    HeroFailToUnlockText = NSLOCTEXT("HeroModule", "Lua_Hero_Text_FailToUnlock", "解锁英雄失败!"),
    HeroUnlockConfirmContentText = NSLOCTEXT("HeroModule", "Lua_Hero_Text_UnlockConfirmContent", "是否使用 %s 解锁 %s"),
    Using = NSLOCTEXT("HeroModule", "Lua_Hero_Text_Using", "正在使用"),
    ConfirmChange = NSLOCTEXT("HeroModule", "Lua_Hero_Text_ConfirmChange", "确认更换"),

    HeroMPApplyBtnText = NSLOCTEXT("HeroModule", "Lua_Hero_HeroMPApplyBtnText", "确认更换"),
    HeroMPUsingBtnText = NSLOCTEXT("HeroModule", "Lua_Hero_HeroMPUsingBtnText", "正在使用"),

    BFModeTitle = NSLOCTEXT("HeroModule", "Lua_HeroModule_BFModeTitle", "全面战场"),
    IrisModeTitle = NSLOCTEXT("HeroModule", "Lua_HeroModule_IrisModeTitle", "烽火地带"),

    HeroUseBtnText = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroUseBtnText", "设为出战"),
    HeroGetBtnText = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroGetBtnText", "购买"),

    HeroSkillType1Text = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroSkillType1Text", "战术装备/道具"),
    HeroSkillType2Text = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroSkillType2Text", "干员特长"),
    

    
    HeroSound = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroSoundText", "声音"),
    HeroAnimation = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroAnimationText", "动作"),

    HeroFeatureLock = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroFeatureLockText", "功能不开放"),

    HeroFashionSuit = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroFashionSuit", "套装"),
    HeroFashionHead = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroFashionHead", "头饰"),
    HeroFashionBag = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroFashionBag", "背包"),

    HeroAppearanceTitileName = NSLOCTEXT("HeroModule","Lua_HeroAppearanceTitileName","时装"),
    HeroSelectTitileName = NSLOCTEXT("HeroModule","Lua_HeroSelectTitileName","干员选择"),

    HeroExpertInfoTitle = NSLOCTEXT("HeroModule","Lua_HeroExpertInfoTitle","特战干员简介"),
    SkillTitleName = NSLOCTEXT("HeroModule","Lua_SkillTitleName","特战干员道具"),
    TalentName = NSLOCTEXT("HeroModule","Lua_TalentName","特战干员天赋"),

    TipHeroLock = NSLOCTEXT("HeroModule", "Lua_TipHeroLock", "此干员未解锁"),

    PleaseSelectHero = NSLOCTEXT("HeroModule", "Lua_HeroModule_PleaseSelectHero", "请选择干员"),
    WaitForJoin = NSLOCTEXT("HeroModule", "Lua_HeroModule_WaitForJoin", "等待入局"),
    ViewSkills = NSLOCTEXT("HeroModule", "Lua_HeroModule_ViewSkills", "查看能力"),
    OpenHeroProfile = NSLOCTEXT("HeroModule", "Lua_HeroModule_OpenHeroProfile", "干员档案"),
    ShowSkills = NSLOCTEXT("HeroModule", "Lua_HeroModule_ShowSkills", "能力演示"),
    ChartTitle = NSLOCTEXT("HeroModule", "Lua_HeroModule_ChartTitle", "兵种特征"),

    --商业化
    HeroFashion = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroFashionText", "外观"),
    HeroCardTitle = NSLOCTEXT("HeroModule", "Lua_HeroCardTitle", "名片"),
    HeroActionShow = NSLOCTEXT("HeroModule", "Lua_HeroActionShow", "表情"),
    HeroFinisher = NSLOCTEXT("HeroModule", "Lua_HeroFinisher", "处决"),
    HeroCombatAssistant = NSLOCTEXT("HeroModule", "Lua_HeroCombatAssistant", "手表"),
    HeroCardAppearance = NSLOCTEXT("HeroModule", "Lua_HeroCardAppearance", "肖像"),
    HeroKillaLines = NSLOCTEXT("HeroModule", "Lua_HeroKillaLines", "击杀台词"),
    HeroBadge = NSLOCTEXT("HeroModule", "Lua_HeroBadge", "徽章"),
    HeroCardRank = NSLOCTEXT("HeroModule", "Lua_HeroCardRank", "头衔"),
    HeroGesture = NSLOCTEXT("HeroModule", "Lua_HeroGesture", "手势"),
    HeroAction = NSLOCTEXT("HeroModule", "Lua_HeroAction", "动作"),
    HeroSparyPaint = NSLOCTEXT("HeroModule", "Lua_HeroSparyPaint", "喷漆"),
    HeroLines = NSLOCTEXT("HeroModule", "Lua_HeroLines", "台词"),
    HeroUse = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroUse", "使用"),
    HeroInUse = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroInUse", "使用中"),
    HeroUnequip = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroUnequip", "卸下"),
    HeroUnequipByEmotionMenuTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroGestureUnequipTips", "已将%s从表情菜单配置中移除"),
    HeroPrivacyProofTipTitle = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPrivacyProofTipTitle", "反窥视"),
    HeroPrivacyProofTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPrivacyProofTips", "你的角色第一人称视角中不可见的敌人不会出现在第三人称视角中"),
    HeroUseSpecialExcutionTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroUseSpecialExcutionTips", "需要装备%s外观使用该处决，是否跳转前往干员外观界面配置？"),
    HeroEquipExecutionCondition1 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroEquipExecutionCondition1", "需要装备%s外观使用"),
    --商业化默认
    HeroCommercialDefault = NSLOCTEXT("HeroModule", "Lua_HeroCommercialDefault", "默认"),
    --临时
    HeroCannonName = NSLOCTEXT("HeroModule", "Lua_HeroHeroCannonName", "虎蹲炮战术涂装"),
    HeroC4Name = NSLOCTEXT("HeroModule", "Lua_HeroC4Name", "磁吸炸弹战术涂装"),

    --兵种道具效果预览
    HeroItemPreview = NSLOCTEXT("HeroModule", "Lua_HeroItemPreview", "效果预览"),

    --干员成长线
    HeroLineTitle = NSLOCTEXT("HeroModule", "Lua_Hero_HeroLineTitle", "干员档案"),
    HeroCurStage = NSLOCTEXT("HeroModule", "Lua_Hero_HeroCurStage", "当前阶段（{curLevel}/{maxLevel}）"),
    HeroOptionalTask = NSLOCTEXT("HeroModule", "Lua_Hero_HeroOptionalTask", "自选任务（{completedNum}/{needNum}）"),
    HeroBirthDayTitle = NSLOCTEXT("HeroModule", "Lua_Hero_HeroBirthDayTitle", "出生日期"),
    HeroHeightTitle = NSLOCTEXT("HeroModule", "Lua_Hero_HeroHeightTitle", "身高"),
    HeroWeightTitle = NSLOCTEXT("HeroModule", "Lua_Hero_HeroWeightTitle", "体重"),
    HeroArchiveLocking = NSLOCTEXT("HeroModule", "Lua_Hero_HeroArchiveLocking", "暂未开放"),
    HeroLineLevelUpRewards = NSLOCTEXT("HeroModule", "Lua_Hero_HeroLineLevelUpRewards", "英雄升级奖励"),
    HeroOptionalTaskTipTitle = NSLOCTEXT("HeroModule", "Lua_Hero_HeroOptionalTaskTipTitle", "自选任务"),
    HeroOptionalTaskTipContent = NSLOCTEXT("HeroModule", "Lua_Hero_HeroOptionalTaskTipContent", "可从任务列表中任意完成3个任务，即可达成升级条件。"),
    HeroArchiveNothingTxt = NSLOCTEXT("HeroModule", "Lua_Hero_HeroArchiveNothingTxt", "干员达到熟练度后解锁"),
    HeroArchiveLockTip = NSLOCTEXT("HeroModule", "Lua_Hero_HeroArchiveLockTip", "干员达到{rank}解锁"),


    --干员名片
    HeroCardUnlock = NSLOCTEXT("HeroModule", "Lua_Hero_HeroCardUnlock", "当前干员未解锁"),
    HeroCardItemCommon = NSLOCTEXT("HeroModule", "Lua_Hero_HeroCardItemCommon", "通用"),
    HeroCardItemCareer = NSLOCTEXT("HeroModule", "Lua_Hero_HeroCardItemCareer", "生涯"),
    HeroCardItemHerotic = NSLOCTEXT("HeroModule", "Lua_Hero_HeroCardItemHerotic", "干员专属"),
    HeroUnlockCardItemHint = NSLOCTEXT("HeroModule", "Lua_Hero_HeroUnlockCardItemHint", "完成任务解锁"),
    HeroUnlockTime = NSLOCTEXT("HeroModule", "Lua_Hero_HeroUnlockTime", "解锁时间: %s"),
    HeroSOLCondition = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroSOLCondition", "烽火地带条件"),
    HeroMPCondition = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroMPCondition", "全面战场条件"),
    HeroCondition = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroCondition", "解锁条件"),
    HeroBadgeLevel = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroBadgeLevel", "lv.{Level}"),
    AchievementDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_AchievementDesc", "累计获得单局评价\"{AchievementName}\"{count}次"),
    AchievementCountDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_AchievementCountDesc", "{count}"),
    AchievementPopDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_AchievementPopDesc", "({Percent}%) {Desc}"),

    AchievementTaskProgress = NSLOCTEXT("HeroModule", "Lua_HeroModule_AchievementTaskProgress", "{taskprogress}%"),

    HeroRecruitmentTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitmentTips", "在任务列表中任意完成限定数量的任务，即可达成升级条件。"),
    HeroRecruitmentOptionalTask = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitmentOptionalTask", "自选任务"),
    HeroRecruiting = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruiting", "招募中"),
    Ongoing = NSLOCTEXT("HeroModule", "Lua_HeroModule_Ongoing", "进行中"),
    Reboot = NSLOCTEXT("HeroModule", "Lua_HeroModule_Reboot", "重启"),
    Receive = NSLOCTEXT("HeroModule", "Lua_HeroModule_Receive", "接取"),
    HeroRecruitSwitchTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitSwitchTips", "接取后将暂停挑战中的干员进度，切换为当前干员。干员进度不会清空，且不会转移，可以随时重启挑战，是否确认切换？"),
    HeroRecruitTaskNumber1 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitTaskNumber1", "任务 · 一"),
    HeroRecruitTaskNumber2 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitTaskNumber2", "任务 · 二"),
    HeroRecruitTaskNumber3 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitTaskNumber3", "任务 · 三"),
    SelectHero = NSLOCTEXT("HeroModule", "Lua_HeroModule_SelectHero", "选择干员"),

    HeroTaskProgress = NSLOCTEXT("HeroModule", "Lua_Hero_HeroTaskProgress", "<customstyle color=\"C001\">{curNum}</>/{maxNum}"),


    --玄学兵种道具抽奖
    UpgradeConditionTips = NSLOCTEXT("HeroModule", "Lua_Hero_UpgradeConditionTips","当前阶段已达 <customstyle color=\"Color_Highlight01\">{curQuality}</> ，再重塑 <customstyle color=\"Color_Highlight01\">{upgradCount}</> 次必进阶 <customstyle color=\"Color_Highlight01\">{nextQuality}</>"),
    SaveScheme = NSLOCTEXT("HeroModule", "Lua_Hero_SaveScheme","保存方案"),
    Reshape = NSLOCTEXT("HeroModule", "Lua_Hero_Reshape","重塑"),
    Apply = NSLOCTEXT("HeroModule", "Lua_Hero_Apply","应用"),
    InApply = NSLOCTEXT("HeroModule", "Lua_Hero_InApply","应用中"),
    PreviewTips1 = NSLOCTEXT("HeroModule", "Lua_Hero_PreviewTips1","局内长按【%s】检视"),
    PreviewTips2 = NSLOCTEXT("HeroModule", "Lua_Hero_PreviewTips2","局内【%s】检视"),
    ReshapeTips = NSLOCTEXT("HeroModule", "Lua_Hero_ReshapeTips","重塑将刷新道具涂装，而当前涂装方案将 <customstyle color=\"Color_Highlight01\">自动保存</> 至 <customstyle color=\"Color_Highlight01\">上次方案</> 中，确认进行重塑？"),
    ApllyTips = NSLOCTEXT("HeroModule", "Lua_Hero_ApllyTips","应用方案后，当前涂装方案将 <customstyle color=\"Color_Highlight01\">自动保存</> 至 <customstyle color=\"Color_Highlight01\">上次方案</>，是否应用？"),
    OverrideTips = NSLOCTEXT("HeroModule", "Lua_Hero_OverrideTips","是否将当前涂装方案覆盖  <customstyle color=\"Color_Highlight01\">{SchemeSlotName}</> ？"),
    SaveTips = NSLOCTEXT("HeroModule", "Lua_Hero_SaveTips","是否将当前涂装方案保存  <customstyle color=\"Color_Highlight01\">{SchemeSlotName}</> ？"),
    NotTipsAgain = NSLOCTEXT("HeroModule", "Lua_Hero_NotTipsAgain","不再提示"),
    CurScheme = NSLOCTEXT("HeroModule", "Lua_Hero_CurScheme","当前方案"),
    LastScheme = NSLOCTEXT("HeroModule", "Lua_Hero_LastScheme","上个方案"),
    SavedScheme = NSLOCTEXT("HeroModule", "Lua_Hero_SavedScheme","保存方案"),
    InitScheme = NSLOCTEXT("HeroModule", "Lua_Hero_InitScheme","初始方案"),
    Scheme1 = NSLOCTEXT("HeroModule", "Lua_Hero_Scheme1","方案一"),
    Scheme2 = NSLOCTEXT("HeroModule", "Lua_Hero_Scheme2","方案二"),
    Scheme3 = NSLOCTEXT("HeroModule", "Lua_Hero_Scheme3","方案三"),
    Scheme4 = NSLOCTEXT("HeroModule", "Lua_Hero_Scheme4","方案四"),
    Cover = NSLOCTEXT("HeroModule", "Lua_Hero_Cover","覆盖"),
    Save = NSLOCTEXT("HeroModule", "Lua_Hero_Save","保存"),
    SchemeStorage = NSLOCTEXT("HeroModule", "Lua_Hero_SchemeStorage","方案库"),
    RedCurrencyRichText = NSLOCTEXT("HeroModule", "Lua_HeroModule_RedCurrencyRichText", "<dfmrichtext type=\"img\" id=\"%s\"/><customstyle color=\"Color_DarkNegative\">%s</>"),
    NormalCurrencyRichText = NSLOCTEXT("HeroModule", "Lua_HeroModule_NormalCurrencyRichText", "<dfmrichtext type=\"img\" id=\"%s\"/>%s"),
    ProbabilityDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_ProbabilityDesc", "概率说明"),
    QualityDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_QualityDesc", "品阶说明"),
    SynthesisProbability = NSLOCTEXT("HeroModule", "Lua_HeroModule_SynthesisProbability", "综合概率"),
    GuaranteedAdvancedCount = NSLOCTEXT("HeroModule", "Lua_HeroModule_GuaranteedAdvancedCount", "保底进阶抽数"),
    LotteryFormat1=  NSLOCTEXT("HeroModule", "Lua_HeroModule_LotteryFormat1", "{text1}：{text2}"),
    LotteryFormat2=  NSLOCTEXT("HeroModule", "Lua_HeroModule_LotteryFormat2", "{text1}→{text2}：{text3}"),
    AdvanceDesc=  NSLOCTEXT("HeroModule", "Lua_HeroModule_AdvanceDesc", "随着重塑次数提升，品阶将持续提升，且不会降低，如果重塑出喜欢的涂装，推荐 <customstyle color=\"Color_Highlight01\">保存方案</>"),
    PrivateRewardsDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_PrivateRewardsDesc", "首次进阶至顶尖模组时，将附赠以下专属奖励"),
    CurClass = NSLOCTEXT("HeroModule", "Lua_HeroModule_CurClass", "<customstyle color=\"Color_Highlight01\">{curClassName}（当前）</>"),
    ClassDSortName = NSLOCTEXT("HeroModule", "Lua_HeroModule_ClassDSortName", "基础"),
    ClassCSortName = NSLOCTEXT("HeroModule", "Lua_HeroModule_ClassCSortName", "标准"),
    ClassBSortName = NSLOCTEXT("HeroModule", "Lua_HeroModule_ClassBSortName", "高级"),
    ClassASortName = NSLOCTEXT("HeroModule", "Lua_HeroModule_ClassASortName", "精英"),
    ClassSSortName = NSLOCTEXT("HeroModule", "Lua_HeroModule_ClassSSortName", "顶尖"),
    NoneScheme = NSLOCTEXT("HeroModule", "Lua_HeroModule_NoneScheme", "无方案"),
    HeroPropsInspectDesc = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsInspectDesc", "局内长按【{KeyNames}】检视"),
    HeroPropsInspectDesc2 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsInspectDesc2", "局内【{KeyNames}】检视"),
    HeroPropsInspectDesc3 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsInspectDesc3", "局内长按 [检视键]"),
    HeroPropsBtnTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsBtnTips", "解锁{FashionName}外观后可进行涂装定制"),
    HeroPropsBtnTips1 = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsBtnTips1", "解锁该干员外观后可进行定制"),
    HeroPropsCustomize = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsCustomize", "涂装定制"),
    ApplySchemeSuccess = NSLOCTEXT("HeroModule", "Lua_HeroModule_ApplySchemeSuccess", "已应用方案"),
    OverrideSchemeSuccess = NSLOCTEXT("HeroModule", "Lua_HeroModule_OverrideSchemeSuccess", "已覆盖方案"),
    SaveSchemeSuccess = NSLOCTEXT("HeroModule", "Lua_HeroModule_SaveSchemeSuccess", "已保存方案"),
    HeroPropsMysticalSkinLotteryPanelName = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsMysticalSkinLotteryPanelName", "兵种道具皮肤定制"),
    HeroPropsMystical = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsMystical", "玄学涂装"),
    HighFrequencyTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HighFrequencyTips", "点击频率太快，请稍后"),
    HeroPropsLotteryNone = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroPropsLotteryNone", "奖池未开放"),
    HeroRecruitmentPanelOpenFailReason = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroRecruitmentPanelOpenFailReason", "该干员未开放招募"),
    Unlock = NSLOCTEXT("HeroModule", "Lua_HeroModule_Unlock", "解锁"),
    HeroBadgeTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroBadgeTips", "解锁时间：{time}\n{desc}"),
    HeroSkillPanelTitleName = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroSkillPanelTitleName", "干员能力"),
    HeroBadgeNew = NSLOCTEXT("HeroModule", "Lua_HeroModule_HeroBadgeNew", "干员徽章"),
    UseHeroChangedTips = NSLOCTEXT("HeroModule", "Lua_HeroModule_UseHeroChangedTips", "出战干员切换为{heroName}"),
}
                                         
HeroConfig.ESubViewType =
{
    -- HeroMain = 0,       -- 专家主界面
    -- AssemblyMain = 1,   -- 配装主界面
    HeroAppearance = 1,  -- 皮肤界面
    HeroCard = 2,
    HeroActionShow = 3,
    HeroFinisher = 4,
    HeroCombatAssistant = 5,
}

HeroConfig.ECardSubViewType = 
{
    Unknown = 0,
    HeroCardAppearance = 1, --肖像
    HeroKillaLines = 2,     --击杀台词
    HeroBadge = 3,          --徽章
    HeroCardRank = 4        --头衔
}

HeroConfig.EActionShowSubViewType = 
{
    Unknown = 0,
    HeroGesture = 1,        --手势
    HeroActionAnim = 2,     --动作
    HeroSparyPaint = 3,     --喷漆
    HeroLines = 4           --台词
}

--轮盘按钮展示样式
HeroConfig.EShowType = {
    ToAdd = 0,      --0：加号，表示待添加
    OnlyImg = 1,    --1：仅图片
    ImgAndTxt = 2   --2：图片+文字
}

--徽章种类
HeroConfig.EBadgeType = {
    General = 1,      --1：通用
    Career = 2,    --2：生涯
    Exclusive = 3   --3：干员专属
}

--徽章槽位类型
HeroConfig.EBadgeSlotType = {
    [1] = 0,
    [2] = 1,
    [3] = 2,
}

--干员成就类型
HeroConfig.EAchievementType = {
    Badge = 1,    --1：徽章
    Title = 2,    --2：头衔
}

--头衔种类
HeroConfig.ETitleType = {
    General = 1,      --1：通用
    Exclusive = 3     --3：干员专属
}

---region ！！！！！！！！！！！！！！！！页签变动请联系jobsjunlin，这里关系到红点判断，因为未建表控制现在全依赖index

--名片顶bar标题
HeroConfig.CardSubTabTxtList = {
    HeroConfig.Loc.HeroCardAppearance,
    -- HeroConfig.Loc.HeroBadge,
    -- HeroConfig.Loc.HeroCardRank,
    -- HeroConfig.Loc.HeroKillaLines,
}

--表情菜单顶bar标题
HeroConfig.AnimShowSubTabTxtList = {
    HeroConfig.Loc.HeroGesture,
    HeroConfig.Loc.HeroAction,
    HeroConfig.Loc.HeroSparyPaint,
    HeroConfig.Loc.HeroLines,
}

--干员商业化一级TopBar页签项文字列表
HeroConfig.HeroFirstTabTextList =
{
    [1] = HeroConfig.Loc.HeroFashion, 
    [2] = HeroConfig.Loc.HeroCardTitle, 
    [3] = HeroConfig.Loc.HeroActionShow,
    [4] = HeroConfig.Loc.HeroFinisher,
    [5] = HeroConfig.Loc.HeroCombatAssistant,
}

---endregion

--顶bar图片
HeroConfig.HeroTabImgPathList =
{
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_12.CommonHall_Hero_Icon_12'",             --外观
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_07.CommonHall_Hero_Icon_07'",             --名片
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_03.CommonHall_Hero_Icon_03'",             --表情彩蛋
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_05.CommonHall_Hero_Icon_05'",             --处决
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_13.CommonHall_Hero_Icon_13'",             --手表
    -- "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0103.CommonHall_Merchant_Icon_0103'",
}
--表情菜单顶bar图片
HeroConfig.ActionShowBarImgPathList =
{
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_02.CommonHall_Hero_Icon_02'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_11.CommonHall_Hero_Icon_11'",--long1屏蔽动作
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Vehicles_Icon_0002.CommonHall_Vehicles_Icon_0002'",
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_09.CommonHall_Hero_Icon_09'",
}

--名片顶bar图片
HeroConfig.CardBarImgPathList =
{
    "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_04.CommonHall_Hero_Icon_04'",--肖像
    -- "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_08.CommonHall_Hero_Icon_08'",--徽章
    -- "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_10.CommonHall_Hero_Icon_10'",--头衔
    -- "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_01.CommonHall_Hero_Icon_01'",--击杀台词
}

HeroConfig.HeroAppearanceTabGroupInfo =
{
    Fashion =
    {
        {
            GroupId = 1,
            Name    = HeroConfig.Loc.HeroFashionSuit,
            Type    = eHeroFashionPosition.FashionSuit,
            -- SubUI   = UIName2ID.HeroAppearanceFashionGroup,
            State   = 1,
        },
        -- {
        --     GroupId = 2,
        --     Name    = HeroConfig.Loc.HeroFashionSuit,
        --     Type    = eHeroFashionPosition.FashionHead,
        --     SubUI   = UIName2ID.HeroAppearanceGoodsGroup,
        --     State   = 2,
        -- },
        -- {
        --     GroupId = 3,
        --     Name    = HeroConfig.Loc.HeroFashionSuit,
        --     Type    = eHeroFashionPosition.FashionBag,
        --     SubUI   = UIName2ID.HeroAppearanceGoodsGroup,
        --     State   = 2
        -- },
    },
    Sound = {},
    Animation = {},
}

HeroConfig.ArmedForceIconMapping = {
    [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0004.Common_Arms_Icon_0004'",
    [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0002.Common_Arms_Icon_0002'",
    [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0003.Common_Arms_Icon_0003'",
    [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0001.Common_Arms_Icon_0001'",
}

-- 专家Icon
HeroConfig.ExpertIconMapping = {
    [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0002.Common_Arms_Icon_0002'",
    [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0003.Common_Arms_Icon_0003'",
    [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0004.Common_Arms_Icon_0004'",
    [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0001.Common_Arms_Icon_0001'"
}

-- 专家背景图手游
HeroConfig.ExpertBackgroundMapping = {
    [1] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_03.Hero_Bg_03'",
    [2] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_04.Hero_Bg_04'",
    [3] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_06.Hero_Bg_06'",
    [4] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_05.Hero_Bg_05'"
}

-- 专家背景图HD
HeroConfig.ExpertBackgroundMappingHD = {
    [1] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_03_HD.Hero_Bg_03_HD'",
    [2] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_04_HD.Hero_Bg_04_HD'",
    [3] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_06_HD.Hero_Bg_06_HD'",
    [4] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Bg_05_HD.Hero_Bg_05_HD'"
}

-- 专家名字颜色
HeroConfig.ExpertNameColorMapping = {
    [1] = Facade.ColorManager:GetSlateColor("Arm_Red"),
    [2] = Facade.ColorManager:GetSlateColor("LightPositive"),
    [3] = Facade.ColorManager:GetSlateColor("Arm_Yellow"),
    [4] = Facade.ColorManager:GetSlateColor("Arm_Blue")
}

--干员商业化道具品阶颜色
--6：红色品质
--5：橙色品质
--4：紫色品质
--3：蓝色品质
--2：绿色品质
HeroConfig.OperatorItemColor = {
    [1] = Facade.ColorManager:GetLinerColor("Quality_Light_White"), --白
    [2] = Facade.ColorManager:GetLinerColor("Quality_Light_Green"), --绿
    [3] = Facade.ColorManager:GetLinerColor("Quality_Light_Blue"), --蓝
    [4] = Facade.ColorManager:GetLinerColor("Quality_Light_Purple"), --紫
    [5] = Facade.ColorManager:GetLinerColor("Quality_Light_Orange"), --橙
    [6] = Facade.ColorManager:GetLinerColor("Quality_Light_Red"), --红
    --[6] = ColorUtil.GetSlateColorByHex("#F83B3C4C"), --未知
}

--干员皮肤蒙版品阶颜色
HeroConfig.FashionMaskLevelColor = {
    [1] = Facade.ColorManager:GetLinerColor("Quality_Dark_Black"), --灰
    [2] = Facade.ColorManager:GetLinerColor("Quality_Dark_Green"), --绿
    [3] = Facade.ColorManager:GetLinerColor("Quality_Dark_Blue"), --蓝
    [4] = Facade.ColorManager:GetLinerColor("Quality_Dark_Purple"), --紫
    [5] = Facade.ColorManager:GetLinerColor("Quality_Dark_Orange"), --橙
    [6] = Facade.ColorManager:GetLinerColor("Quality_Dark_Red"), --红
}

-- 专家图标颜色()
HeroConfig.ExpertIconColorMapping = {
    [1] = Facade.ColorManager:GetLinerColor("Arm_Red"),--FLinearColor(1.0,0.035294,0.082353,1.0)
    [2] = Facade.ColorManager:GetLinerColor("LightPositive"),--FLinearColor(0.003922,0.941177,0.32549,1.0)
    [3] = Facade.ColorManager:GetLinerColor("Arm_Yellow"),--FLinearColor(1.0,0.666667,0.109804,1.0),
    [4] = Facade.ColorManager:GetLinerColor("Arm_Blue")--FLinearColor(0.003922,0.890196,0.933333,1.0)--ColorUtil.GetLinearColorByHex
}

-- 专家场景灯光组名
HeroConfig.ExpertLightGroupNameMapping = {
    [1] = "ExpertDisplay_Red",
    [5] = "ExpertDisplay_Red_HD",
    [2] = "ExpertDisplay_Green",
    [6] = "ExpertDisplay_Green_HD",
    [3] = "ExpertDisplay_Yellow",
    [7] = "ExpertDisplay_Yellow_HD",
    [4] = "ExpertDisplay_Blue",
    [8] = "ExpertDisplay_Blue_HD",
}

-- 专家场景特效颜色（无用字段）
-- HeroConfig.ExpertSceneEffectColor = {
--     [1] = ColorUtil.GetLinearColorByHex("#FF0915FF"),
--     [2] = ColorUtil.GetLinearColorByHex("#01F053FF"),
--     [3] = ColorUtil.GetLinearColorByHex("#FFAA1CFF"),
--     [4] = ColorUtil.GetLinearColorByHex("#01E3EEFF")
-- }

-- 专家皮肤背景
HeroConfig.ExpertAppearanceBGImage = {
    [1] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Sp_08.Hero_Sp_08'",
    [2] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Sp_10.Hero_Sp_10'",
    [3] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Sp_09.Hero_Sp_09'",
    [4] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Sp_07.Hero_Sp_07'"
}

-- 临时
HeroConfig.LockHeroIcon = {
    [1] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Sp_11.Hero_Sp_11'",
    [2] = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Sp_12.Hero_Sp_12'"
}

-- 技能快捷键图片
HeroConfig.SkillShortCutImage = {
    [1] = "PaperSprite'/Game/UI_HD/UIAtlas/System/KeyIcon/BakedSprite/KeyIcon_Icon_0024.KeyIcon_Icon_0024'",
    [2] = "PaperSprite'/Game/UI_HD/UIAtlas/System/KeyIcon/BakedSprite/KeyIcon_Icon_0007.KeyIcon_Icon_0007'",
    [3] = "PaperSprite'/Game/UI_HD/UIAtlas/System/KeyIcon/BakedSprite/KeyIcon_Icon_0022.KeyIcon_Icon_0022'"
}

--玄学方案列表类型标题icon（ 1当前方案 2上个方案 3保存的方案 4初始方案）
HeroConfig.HeroPropsSchemeTypeTitleIcon = {
    [1] = "PaperSprite'/Game/UI/UIAtlas/System/Hero/BakedSprite/Hero_Icon_13.Hero_Icon_13'",
    [2] = "PaperSprite'/Game/UI/UIAtlas/System/Hero/BakedSprite/Hero_Icon_14.Hero_Icon_14'",
    [3] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_Save.CommonHall_Icon_Save'",
    [4] = "PaperSprite'/Game/UI/UIAtlas/System/Hero/BakedSprite/Hero_Icon_12.Hero_Icon_12'",
}

HeroConfig.DefaultExecutionIcon = "Texture2D'/Game/UI/UIAtlas/Texture/HeadAvatar/HeadAvatar_0000.HeadAvatar_0000'"

HeroConfig.HeroPropsSchemeTypeTitle = {
    [1] = HeroConfig.Loc.CurScheme,
    [2] = HeroConfig.Loc.LastScheme,
    [3] = HeroConfig.Loc.SaveScheme,
    [4] = HeroConfig.Loc.InitScheme,
}

-- BEGIN MODIFICATION @ VIRTUOS : 技能快捷键图片(Console)
HeroConfig.SkillShortCutImage_Console = {
    [1] = "PaperSprite'/Game/UI_HD/UIAtlas/System/KeyIcon_XSX/BakedSprite/XboxIcon_Icon_0013.XboxIcon_Icon_0013'",
    [2] = "PaperSprite'/Game/UI_HD/UIAtlas/System/KeyIcon_XSX/BakedSprite/XboxIcon_Icon_0014.XboxIcon_Icon_0014'"
}

--干员商业化类型
HeroConfig.CommercialType = {
    Card = 1,       --名片
    SprayPaint = 2, --喷漆
    AnimShow = 3,   --表演动画3p
    Gesture = 4,    --表演手势1p
    Lines = 5,      --轮盘台词
    Execution = 6,  --处决
    Watch = 7,      --手表
    Badge = 8,      --徽章
    Title = 9,      --头衔
    KillaLines = 10, --击杀台词           
    HeroItem = 11,  --干员道具
}

--轮盘装备状态
HeroConfig.RouletteEquipState = {
    None = 0,        --未穿戴
    Equipped = 1,     --已穿戴
}

----干员成长线
HeroConfig.EProfileClass = {
    TacticEquipmentInfo = 1,
    PsychologicalStateAssessment = 2,
    ActionArchive = 3
}

HeroConfig.ProfileLocList = {
    [1] = NSLOCTEXT("HeroModule", "Lua_Hero_TacticEquipmentInfo", "战术装备信息"),
    [2] = NSLOCTEXT("HeroModule", "Lua_Hero_PsychologicalStateAssessment", "干员心理状态评估"),
    [3] = NSLOCTEXT("HeroModule", "Lua_Hero_ActionArchive", "行动档案"),
}


HeroConfig.ExpertSceneMatParamColl = "MaterialParameterCollection'/Game/UI/UIAtlas/System/Hero/Sp/MiniWorldExpertMaterialCollection.MiniWorldExpertMaterialCollection'"

--台词图标
HeroConfig.LinesImage = "PaperSprite'/Game/UI/UIAtlas/System/Hero/BakedSprite/Hero_Icon_07.Hero_Icon_07'"

--默认轮盘图标
HeroConfig.RouletteIcon = "Texture2D'/Game/UI/UIAtlas/System/Hero/Sp/Hero_Execution_Sp_01.Hero_Execution_Sp_01'"

--干员sequence状态
---@enum HeroConfig.EHeroSequenceState
HeroConfig.EHeroSequenceState = {
    Idle = "State2",        --空闲
    OnStage = "None",   --登场
}

UITable[UIName2ID.HeroMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroMainPanel",
    BPKey = "WBP_Hero_MainPanel",
    SubUIs = {
        UIName2ID.HeroMainViewHeroItem,
        -- UIName2ID.HeroAppearancePanel,
        UIName2ID.HeroApproach,
        UIName2ID.AssemblyTips,
        UIName2ID.HeroMainViewHeroListItem,
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    Anim = {
        FlowOutAni = "WBP_Hero_MainPanel_out",
        --.需要手动管理动画播放
        bManuelAnim = true
    },
    LinkSubStage = ESubStage.HallHero,
}

UITable[UIName2ID.HeroMainTabGroupView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroMainTabGroupView", 
    BPKey = "WBP_HeroMain_TabGroup_Hero",
    SubUIs = {
        UIName2ID.HeroMainTabView
    }
}

UITable[UIName2ID.HeroMainTabView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroMainTabView",
    BPKey = "WBP_Hero_ArmsTab1_Hero"
}

UITable[UIName2ID.HeroMainViewHeroItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroMainViewHeroItem",
    BPKey = "WBP_Hero_HeroItem01",
}

UITable[UIName2ID.HeroMainViewHeroListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroMainViewHeroListItem",
    BPKey = "WBP_Hero_HeroItem02",
    SubUIs={
        UIName2ID.HeroMainViewHeroItem
    }
}

UITable[UIName2ID.HeroSelectTips] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSelectTips",
    BPKey = "WBP_Hero_SelectTips",
    
}

UITable[UIName2ID.HeroChartPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroChartPanel",
    BPKey = "WBP_Hero_CharPop",
    SubUIs = {
        UIName2ID.HeroChartItem
    },
    IsModal = true,
}

UITable[UIName2ID.HeroListTopPanel] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroPop.HeroListTopPanel",
    BPKey = "WBP_Hero_ListPop",
    SubUIs = {
        UIName2ID.HeroMainViewHeroItem
    },
    IsModal = true,
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 1,
    },
    ZOrderOffset = 801,
}

UITable[UIName2ID.HeroSkillTopPanel] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroPop.HeroSkillTopPanel",
    BPKey = "WBP_Hero_SkillInfoPop",
    SubUIs = {
        UIName2ID.HeroSkillItem
    },
    IsModal = true,
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 1,
    },
    ZOrderOffset = 700,
}

UITable[UIName2ID.HeroChartItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroChartItem",
    BPKey = "WBP_Hero_CharItem"
}

UITable[UIName2ID.HeroSkillVideoPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillVideoPanelNew",
    BPKey = "WBP_Hero_SkillsVideos",
    SubUIs = {
        UIName2ID.HeroSkillVideoButton1
    },
    IsModal = true,
    bEnableWorldRendering = true,
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.HeroSkillVideoButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillVideoButton",
    BPKey = "WBP_Hero_SkillsVideosBtn"
}

UITable[UIName2ID.HeroSkillVideoButton1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillVideoButton1",
    BPKey = "WBP_Hero_SkillsVideosBtn1"
}


UITable[UIName2ID.HeroSkillItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillItem",
    BPKey = "WBP_Hero_SkillInfo",
}

UITable[UIName2ID.HeroSkillDetailView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillDetailView",
    BPKey = "WBP_Hero_SkllDetail_Hero",
    LinkSubStage = ESubStage.HallMain,
}

UITable[UIName2ID.HeroSkillInfoPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillInfoPanel",
    BPKey = "WBP_Hero_SkllDetailItem_Hero"
}

UITable[UIName2ID.HeroSkillIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroSkillIcon",
    BPKey = "WBP_Hero_Talent"
}


UITable[UIName2ID.BattleHeroSkillIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.BattleHeroSkillIcon",
    BPKey = "WBP_Battle_Hero_Talent",
}


UITable[UIName2ID.HeroPropItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroPropItem",
    BPKey = "WBP_Hero_Props"
}

--时装panel
UITable[UIName2ID.HeroAppearancePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearancePanel",
    BPKey = "WBP_Hero_Appearance",
    SubUIs = {
        UIName2ID.HeroAppearancePaintingItem,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVCommercializeItemTemplate,
    },
}

--道具panel
UITable[UIName2ID.HeroAppearancePropPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearancePropPanel",
    BPKey = "WBP_Hero_Appearance_Prop",
    SubUIs = {
        UIName2ID.HeroAppearancePaintingItem,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVCommercializeItemTemplate,
    },
    LinkSubStage = ESubStage.OperatorItem,
}

--涂装item
UITable[UIName2ID.HeroAppearancePaintingItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearancePaintingItem",
    BPKey = "WBP_Painting_ItemView",
}


-- UITable[UIName2ID.HeroAppearanceFashionGroup] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearanceFashionGroup",
--     BPKey = "WBP_Hero_Appearance_FashionList"
-- }

UITable[UIName2ID.HeroAppearanceGoodsItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearanceGoodsItem",
    BPKey = "WBP_Hero_Appearance_GoodsItem_Hero"
}

UITable[UIName2ID.HeroAppearanceFashionItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearanceFashionItem",
    BPKey = "WBP_Hero_Appearance_FashionItem"
}

UITable[UIName2ID.HeroAppearanceGoodsGroup] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroAppearance.HeroAppearanceGoodsGroup",
    BPKey = "WBP_Hero_Appearance_GoodsList_Hero"
}

--商业化Start
UITable[UIName2ID.HeroCommercialMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCommercialMainPanel",
    BPKey = "WBP_HeroCommercialMain",
    SubUIs = {
        UIName2ID.HeroAppearancePanel,
        UIName2ID.HeroCustomBussinessCardAppearance,
        UIName2ID.HeroExcutionActionMainPanel,
        UIName2ID.HeroGestureMainPanel,
        UIName2ID.HeroActionShowPanel,
        UIName2ID.HeroSparyPaintPanel,
        UIName2ID.HeroLinesShowPanel,
        UIName2ID.HeroCustomBussinessCardBadgePanel,
        UIName2ID.HeroBadgeList,
        UIName2ID.HeroJobTitleList,
        UIName2ID.HeroCustomBussinessCardKillLine,
        -- UIName2ID.HeroCustomBussinessCardTitle,
        UIName2ID.HeroWatchMainPanel,
    },
    LinkSubStage = ESubStage.HallHero,
    IsModal = true,
}

-- 通用干员名片图标
UITable[UIName2ID.HeroCommonCardIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCommonCard.HeroCommonCardIcon",
    BPKey = "WBP_Common_CardIcon",
    SubUIs = {
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.DragBadgeItemPreview,
    }
}

-- 干员名片S2
UITable[UIName2ID.HeroBussinessCard] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroBussinessCard",
    BPKey = "WBP_Common_CardS2",
    Anim = {
        bManuelAnim = true
    },
    SubUIs = {
        UIName2ID.CommonSocialTitleItem_Big
    },
}

UITable[UIName2ID.HeroCustomBussinessCardAppearance] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCustomBussinessCardAppearance",
    BPKey = "WBP_Hero_CustomBusinessCardMain",
    Anim = {
        bManuelAnim = true
    },
}

UITable[UIName2ID.HeroCustomBussinessCardKillLine] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCustomBussinessCardKillLine",
    BPKey = "WBP_Hero_CustomBusinessCardMain",
    Anim = {
        bManuelAnim = true
    },
}

UITable[UIName2ID.HeroCustomBussinessCardBadgePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCustomBussinessCardBadgePanel",
    BPKey = "WBP_Hero_CustomBusinessCardMain",
    SubUIs = {
        UIName2ID.HeroCareerBadgeList,
        UIName2ID.HeroUnlockConditionItem,
    },
    Anim = {
        bManuelAnim = true
    },
}

-- UITable[UIName2ID.HeroCustomBussinessCardTitle] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCustomBussinessCardTitle",
--     BPKey = "WBP_Hero_CustomBusinessCardMain",
--     SubUIs = {
--         UIName2ID.HeroUnlockConditionItem,
--     },
--     Anim = {
--         bManuelAnim = true
--     },
-- }


UITable[UIName2ID.HeroBussinessCardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroBussinessCardItem",
    BPKey = "WBP_Hero_BusinessCard",
}

UITable[UIName2ID.HeroCustomBussinessCard] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCustomBussinessCard",
    BPKey = "WBP_Hero_CustomBusinessCardMain",
}

UITable[UIName2ID.HeroBadgeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroBadgeItem",
    BPKey = "WBP_Hero_Badge",
    SubUIs = {
        UIName2ID.DragBadgeItemPreview,
    },
    Anim = {
        bManuelAnim = true
    },
}

UITable[UIName2ID.HeroCareerBadgeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCareerBadgeItem",
    BPKey = "WBP_Hero_BadgeLeve_ltem",
    Anim = {
        bManuelAnim = true
    },
}

UITable[UIName2ID.HeroBadgeList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroBadgeList",
    BPKey = "WBP_Hero_BadgeList",
    SubUIs = {
        UIName2ID.HeroBadgeItem
    },
}

UITable[UIName2ID.HeroCareerBadgeList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroCareerBadgeList",
    BPKey = "WBP_Hero_Badge_Level",
    SubUIs = {
        UIName2ID.HeroCareerBadgeItem
    },
}

-- 生涯成就弹窗
UITable[UIName2ID.HeroBadgeAchievementPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroBadgeAchievementPop",
    BPKey = "WBP_Hero_ConditionDescription",
    SubUIs = {
        UIName2ID.HeroAchievementItem
    },
    Anim = {
		bManuelAnim = true,
	},
    IsModal = true,
}

-- 生涯成就条目
UITable[UIName2ID.HeroAchievementItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroAchievementItem",
    BPKey = "WBP_Hero_DescriptionItem",
}

UITable[UIName2ID.HeroExcutionActionMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroExcutionActionMainPanel",
    BPKey = "WBP_Hero_ExecutionMain",
    Anim = {
        bManuelAnim = true,
    },
}

UITable[UIName2ID.HeroExecutionActionItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroExecutionActionItem",
    BPKey = "WBP_Hero_ExecutionAction",
}

UITable[UIName2ID.HeroJobTitle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroJobTitle",
    BPKey = "WBP_Hero_JobTitle",
}

UITable[UIName2ID.HeroJobTitleList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroJobTitleList",
    BPKey = "WBP_Hero_JobTitleList",
    SubUIs = {
        UIName2ID.HeroJobTitle
    },
}

UITable[UIName2ID.HeroUnlockConditionItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroUnlockConditionItem",
    BPKey = "WBP_Hero_UnlockConditions",
}

UITable[UIName2ID.HeroEmoticonItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroEmoticonItem",
    BPKey = "WBP_EmoticonRouletteItem",
}

UITable[UIName2ID.HeroGestureMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroGestureMainPanel",
    BPKey = "WBP_Hero_EmoticonMain",
    LinkSubStage = ESubStage.HallHero,
}

UITable[UIName2ID.HeroActionShowPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroActionShowPanel",
    BPKey = "WBP_Hero_EmoticonMain",
    LinkSubStage = ESubStage.HallHero,
}

UITable[UIName2ID.HeroSparyPaintPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroSparyPaintPanel",
    BPKey = "WBP_Hero_EmoticonMain",
    LinkSubStage = ESubStage.HallHero,
    -- Anim = {
    --     bManuelAnim = true,
    -- },
}

UITable[UIName2ID.HeroLinesShowPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroLinesShowPanel",
    BPKey = "WBP_Hero_EmoticonMain",
    LinkSubStage = ESubStage.HallHero,
}

UITable[UIName2ID.HeroEmotionRoulette] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroEmotionRoulette",
    BPKey = "WBP_EmoticonRoulette",
    SubUIs = {
        UIName2ID.HeroRoulettePanel,
    },
    IsModal = true,
}

UITable[UIName2ID.HeroRoulettePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.Roulette.HeroRoulettePanel",
    BPKey = "WBP_CommonRoulette_Outsider",
    SubUIs = {
        UIName2ID.HeroRouletteLine,
        UIName2ID.HeroRouletteItem,
    },
    Anim = {
        bManuelAnim = true,
    },
}

UITable[UIName2ID.HeroRouletteItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.Roulette.HeroRouletteItem",
    BPKey = "WBP_CommonRouletteItem_Outsider",
    SubUIs = {
        UIName2ID.HeroEmoticonItem,
    }
}

UITable[UIName2ID.HeroRouletteLine] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.Roulette.HeroRouletteLine",
    BPKey = "WBP_CommonRouletteLine_Outsider",
}

--手表主界面
UITable[UIName2ID.HeroWatchMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroWatchMainPanel",
    BPKey = "WBP_Hero_CollaborationAidesMain",
    LinkSubStage = ESubStage.HeroWatchMain,
}

--干员招募主界面
UITable[UIName2ID.HeroRecruitmentPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroRecruitmentPanel",
    BPKey = "WBP_Hero_RcruitmentChallenge",
    SubUIs = {
        UIName2ID.HeroRecruitmentTaskItem,
    },
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 1,
    },
}

UITable[UIName2ID.HeroRecruitmentTaskItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroRecruitmentTaskItem",
    BPKey = "WBP_Hero_TaskBox",
    ReConfig = {
        IsPoolEnable = true,
    },
}


UITable[UIName2ID.HeroPropsMysticalSkinLotteryPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroPropsMysticalSkinLotteryPanel",
    BPKey = "WBP_Hero_CustomizedPainting",
    SubUIs = {
        UIName2ID.HeroPaintSchemeList,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.OperatorItem,
}

UITable[UIName2ID.HeroPaintSchemeList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroPaintSchemeList",
    BPKey = "WBP_Hero_PaintSchemeList",
    SubUIs = {
        UIName2ID.HeroPaintSchemeListItem,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.HeroPaintSchemeListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroPaintSchemeListItem",
    BPKey = "WBP_Hero_PaintScheme",
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.HeroPaintSchemeSavePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroPaintSchemeSavePop",
    BPKey = "WBP_Hero_SavePaintScheme_Pop",
    SubUIs = {
        UIName2ID.HeroPaintSchemeSavePopItem,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
    IsModal = true,
}

UITable[UIName2ID.HeroPaintSchemeSavePopItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroPaintSchemeSavePopItem",
    BPKey = "WBP_Hero_PaintSchemeItem",
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.HeroPaintSchemeBricks] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroCustomSystem.HeroPaintSchemeBricks",
    BPKey = "WBP_Hero_PaintingBricks",
    ReConfig = {
        IsPoolEnable = true,
    },
}


UITable[UIName2ID.HeroItemVideoPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroMain.HeroItemVideoPanel",
    BPKey = "WBP_CollectionsVideos_01",
    SubUIs = {
        UIName2ID.HeroSkillVideoButton
    },
    Anim = {
        FlowInAni = "WBP_Hero_SkillsVideos_in",
        FlowOutAni = "WBP_Hero_SkillsVideos_out",
    },
    bEnableWorldRendering = true,
}


--商业化End

--- Instruction
--------------------------------------------------------------------------
UITable[UIName2ID.HeroAppearanceInstruction] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ShopModule.UI.Main.ShopPopInstruction",
    BPKey = "WBP_Shop_Instructions",
    Anim = {
        FlowOutAni = "WBP_Shop_Instructions_out",
        FlowInAni = "WBP_Shop_Instructions_in",
    }
}

--- 干员成长线
UITable[UIName2ID.HeroFile] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroFile",
    BPKey = "WBP_Hero_File",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.HeroFileTask
    },
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 1,
    },
}

UITable[UIName2ID.HeroFileDataView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroFileDataView",
    BPKey = "WBP_Hero_FileDate",
}

UITable[UIName2ID.HeroOperatorProfile] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroOperatorProfile",
    BPKey = "WBP_HeroOperatorProfile",
}

UITable[UIName2ID.HeroFileLevel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroFileLevel",
    BPKey = "WBP_Hero_FileLevel",
    Anim = {
        bManuelAnim = true,
    },
}

UITable[UIName2ID.HeroRewardsOverview] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroRewardsOverview",
    BPKey = "WBP_Hero_RewardsOverview",
    IsModal = true,
}

UITable[UIName2ID.HeroRewardsOverviewLevel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroRewardsOverviewLevel",
    BPKey = "WBP_Hero_RewardsOverview_Level",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVGetMaskComponent,
        UIName2ID.IVCommercializeShadingComponent,
    }
}

UITable[UIName2ID.HeroFileTask] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Growth.HeroFileTask",
    BPKey = "WBP_Hero_FileTask",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
        UIName2ID.IVGetMaskComponent
    }
}

----干员档案
UITable[UIName2ID.HeroFileListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Profile.HeroFileListItem",
    BPKey = "WBP_Hero_FileList",
    Anim = {
        bManuelAnim = true,
    },
}

UITable[UIName2ID.HeroTacticalEquipment] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Profile.HeroTacticalEquipment",
    BPKey = "WBP_Hero_TacticalEquipment",
    -- ReConfig = {
    --     IsPoolEnable = true,
    --     MaxPoolLength = 1,
    -- },
    SubUIs = {
        UIName2ID.HeroArchiveInformation,
    }
}

UITable[UIName2ID.HeroArchiveInformation] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.HeroModule.UI.HeroGrowth.Profile.HeroArchiveInformation",
    BPKey = "WBP_HeroArchiveInformation",
    SubUIs = {
        UIName2ID.CommonEmptyContent,
    }
}

return HeroConfig

