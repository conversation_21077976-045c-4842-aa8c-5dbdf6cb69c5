----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------
local MemoryUtil = require "DFM.YxFramework.Util.MemoryUtil"
local MemoryTool = require("DFM.StandaloneLua.BusinessTool.MemoryTool")

local CommonBarInputLogic = {}

CommonBarInputLogic.SetAndroidInputEnableProcess = function(bEnable)
    if DFHD_LUA == 0 then
        Facade.UIManager:SetFlagEnableAndroidBack(bEnable)
    end
end

CommonBarInputLogic.SetUseStarAppStateProcess = function(bUse, bNeedCloseAnim)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if localCtrl then
        local dfmCharacter = localCtrl:GetPawn()
        if dfmCharacter and dfmCharacter.UseStarApp then
            dfmCharacter:UseStarApp(bUse, bNeedCloseAnim)
        end
        if bUse then
            --针对BHD模式下TopBar音效做特殊处理
            if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.ModeHallToLobbyBHD then
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBarEnterBHD)
            else
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBarEnter)
            end
        else
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBarExit)
        end
        Module.CommonBar.Field:SetIsUseStarAppState(bUse, bNeedCloseAnim)
    end
end

local lastPopAllOperationTime = 0
local bBackConfirmWindowShow = true
function gBackConfirmWindowShow(bBackConfirmWindowShow)
    bBackConfirmWindowShow = bBackConfirmWindowShow
end

local memoryGuardedMBForceSet = 0
function gForceSetMemoryGuardedMB(memoryGuardedMB)
    memoryGuardedMBForceSet = tonumber(memoryGuardedMB)
end
CommonBarInputLogic.CheckMemoryWhenBackStackUI = function(curStackCount)
    if not bBackConfirmWindowShow then
        return
    end

    local bLowMemoryCVarState = Facade.UIManager:GetIsLowMemoryState()
    if not bLowMemoryCVarState then
        return
    end

    if curStackCount <= 1 then
        --- TODO 局外异常恢复机制
        -- if curStackCount == 1 then
        --     local stackController = Facade.UIManager:GetLayerControllerByType(EUILayer.Stack)
        --     if stackController then
        --         stackController:SaveBlackBoxForPlane(gameFlowType)
        --     end
        -- end
        return
    end

    --- 防止频繁检查内存
    local currentTime = os.time()
    local remainTime = currentTime - lastPopAllOperationTime
    if remainTime < 30  and lastPopAllOperationTime ~= 0 then
        logwarning("[CommonBar] CheckMemoryWhenBackStackUI return because last check memory time is too close, remainTime:", remainTime)
        return
    end

    --- 检查内存
    local availableMemoryMB = MemoryUtil.GetCurrentAvailableMemory()
    local memoryGuardedMB_poptobottom = memoryGuardedMBForceSet == 0 and Facade.UIManager:GetMemoryGuardedMB_PopToBottom() or memoryGuardedMBForceSet

    if availableMemoryMB < memoryGuardedMB_poptobottom then
        logwarning("[CommonBar] CheckMemoryWhenBackStackUI show confirm window, availableMemoryMB vs memoryGuardedMB_poptobottom: ", availableMemoryMB, " < ", memoryGuardedMB_poptobottom, "curStackCount:", curStackCount)
        Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
            Module.CommonBar.Config.Loc.ReturnToLobbyConfirm,
            function()
                --- 回到栈底大厅
                Facade.UIManager:PopAllUI(false, true, MapPopAllUIReason2Str.BackActionToLobby)
                --- 释放内存
                MemoryTool.ProcessReleaseResourceOnForceBackConfirm()
                lastPopAllOperationTime = os.time()
                logwarning("[CommonBar] heckMemoryWhenBackStackUI BackActionToLobby 回到栈底大厅，释放内存")
            end,
            Module.CommonBar.Config.Loc.ReturnToLobby
        )
    else
        if not VersionUtil.IsShipping() then
            logwarning("[CommonBar] CheckMemoryWhenBackStackUI not show confirm window, availableMemoryMB vs memoryGuardedMB_poptobottom: ", availableMemoryMB, " > ", memoryGuardedMB_poptobottom, "curStackCount:", curStackCount)
        end
    end
end


return CommonBarInputLogic
