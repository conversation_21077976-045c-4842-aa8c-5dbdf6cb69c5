----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local LootingConfig = require "DFM.Business.Module.LootingModule.LootingConfig"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local CommonWidgetLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonWidgetLogic"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"

local UGPGameplayDelegates = import "GPGameplayDelegates"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local EItemState = import "EItemState"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"

--BEGIN MODIFICATION @ VIRTUOS : 新增手柄操作相关依赖
local UGPInputHelper = import "GPInputHelper"
local EGPInputType = import "EGPInputType"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"
--END MODIFICATION

local CommonIVInputLogic = {}

CommonIVInputLogic.CurrentHoverView = setmetatable({}, weakmeta)
CommonIVInputLogic.LastHoverView = setmetatable({}, weakmeta)
CommonIVInputLogic.DisplayInputHandles = {}
CommonIVInputLogic.HoverTipHandle = nil
CommonIVInputLogic.HoverTipAnchor = setmetatable({}, weakmeta)
CommonIVInputLogic.IVImmediateClickTriggerFrame = 0
CommonIVInputLogic.bBindDisplayAction = false
CommonIVInputLogic.HoverTimer = nil
CommonIVInputLogic.CurrEnterTextIcon = nil
CommonIVInputLogic.NavConfigHandler = nil

--BEGIN MODIFICATION @ VIRTUOS : 长按输入
CommonIVInputLogic.LongPressDisplayInputHandles = {}
--END MODIFICATION

local function fTriggerIVImmediateClick()
    CommonIVInputLogic.IVImmediateClickTriggerFrame = LuaTickController:Get():GetFrameIndex()
end

local function fCheckIVImmediateClickThisFrame()
    return CommonIVInputLogic.IVImmediateClickTriggerFrame == LuaTickController:Get():GetFrameIndex()
end

local function fCheckEnableInput()
    local bEnable = not Module.CommonWidget.Field.bDisableHover
    return bEnable
end

-----------------------------------------------------------------------
--region Events Binding

function CommonIVInputLogic.BindEvents()
    CommonWidgetConfig.Events.evtIVMouseEnter:AddListener(CommonIVInputLogic.OnIVMouseEnter)
    CommonWidgetConfig.Events.evtIVMouseLeave:AddListener(CommonIVInputLogic.OnIVMouseLeave)
    CommonWidgetConfig.Events.evtIVShowHoverTips:AddListener(CommonIVInputLogic.OnShowHoverTips)
    CommonWidgetConfig.Events.evtIVHideHoverTips:AddListener(CommonIVInputLogic.OnHideHoverTips)
    CommonWidgetConfig.Events.evtIVTextIconMouseEnter:AddListener(CommonIVInputLogic.OnIVTextIconMouseEnter)
    CommonWidgetConfig.Events.evtIVTextIconMouseLeave:AddListener(CommonIVInputLogic.OnIVTextIconMouseLeave)
    CommonWidgetConfig.Events.evtIVImmediateClicked:Bind(CommonIVInputLogic.OnIVImmediateClicked)
    

    Server.InventoryServer.Events.evtItemMove:AddListener(CommonIVInputLogic._OnItemMove)
    Module.Inventory.Config.Events.evtProcessItemMoveResultFrontEnd:AddListener(CommonIVInputLogic._OnProcessItemMoveResult)
    Server.LootingServer.Events.evtLootingItemMove:AddListener(CommonIVInputLogic._OnItemMove)

    UGPGameplayDelegates.Get(GetWorld()).OnMarkLogicInputKey:Add(CommonIVInputLogic.ProcessMark)

    -- CommonIVInputLogic.BindDisplayInputs()
end

function CommonIVInputLogic.UnbindEvents()
    CommonWidgetConfig.Events.evtIVMouseEnter:RemoveListener(CommonIVInputLogic.OnIVMouseEnter)
    CommonWidgetConfig.Events.evtIVMouseLeave:RemoveListener(CommonIVInputLogic.OnIVMouseLeave)
    CommonWidgetConfig.Events.evtIVShowHoverTips:RemoveListener(CommonIVInputLogic.OnShowHoverTips)
    CommonWidgetConfig.Events.evtIVHideHoverTips:RemoveListener(CommonIVInputLogic.OnHideHoverTips)
    CommonWidgetConfig.Events.evtIVTextIconMouseEnter:RemoveListener(CommonIVInputLogic.OnIVTextIconMouseEnter)
    CommonWidgetConfig.Events.evtIVTextIconMouseLeave:RemoveListener(CommonIVInputLogic.OnIVTextIconMouseLeave)
    CommonWidgetConfig.Events.evtIVImmediateClicked:Unbind(CommonIVInputLogic.OnIVImmediateClicked)

    Server.InventoryServer.Events.evtItemMove:RemoveListener(CommonIVInputLogic._OnItemMove)
    Server.LootingServer.Events.evtLootingItemMove:RemoveListener(CommonIVInputLogic._OnItemMove)

    UGPGameplayDelegates.Get(GetWorld()).OnMarkLogicInputKey:Remove(CommonIVInputLogic.ProcessMark)

    -- CommonIVInputLogic.UnbindDisplayInputs()
    -- 确保不发生泄露
    if CommonIVInputLogic.bBindDisplayAction then
        CommonIVInputLogic.UnbindDisplayInputs()
    end

    CommonIVInputLogic.SetHoverView(nil)
    CommonIVInputLogic.SetLastHoverView(nil)
    CommonIVInputLogic.CloseCommonHoverTipsHD()
    CommonIVInputLogic.ReleaseHoverTimer()
    CommonIVInputLogic.CurrEnterTextIcon = nil
end


function CommonIVInputLogic.BindDisplayInputs(itemView)
    if CommonIVInputLogic.bBindDisplayAction then
        CommonIVInputLogic.UnbindDisplayInputs()
    end
    CommonIVInputLogic.bBindDisplayAction = true
    -- 如果有Popui，那么悬停itemview不更新快捷键
    -- 这个接口有问题，先屏蔽
    if Module.CommonWidget:GetOpenedPopItemView() ~= nil or Module.ItemDetail:GetIsSplitPanelOpened() then
    -- if Module.CommonWidget:GetOpenedPopItemView() ~= nil then
        return
    end
    -- CommonIVInputLogic.NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, itemView)

    local inputMonitor = Facade.UIManager:GetInputMonitor()
    local handle
     handle = inputMonitor:AddDisplayActionBinding("LootingPanel_Operate", EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessOperate,
         nil, EDisplayInputActionPriority.UI_Stack)
     table.insert(CommonIVInputLogic.DisplayInputHandles, handle)

    handle = inputMonitor:AddDisplayActionBinding("LootingPanel_Discard", EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessDiscard, 
        nil, EDisplayInputActionPriority.UI_Stack)
    table.insert(CommonIVInputLogic.DisplayInputHandles, handle)

    --BEGIN MODIFICATION @ VIRTUOS : 
    -- 因为需求变更，将部分仓库和背包独有的UI手柄输入移到此。
    -- 因为收藏室和仓库Looting差异较大，不在这里一同处理
    if IsHD() and Module.CommonWidget.IsEnableGamepadItemShortcutKey() and WidgetUtil.IsStopSimulatedMouseDrag() and WidgetUtil.IsGamepad() and not Module.CommonWidget:IsInMultiSelectedMode() and itemView and not ItemOperaTool.CheckIsDIYCabinetPanel() then

        local InvSlotViewType = Module.Inventory:GetInvSlotViewType()

        -- 仓库出售和背包丢弃
        if ItemOperaTool.CheckRunWarehouseLogic() == true and InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Default then
            local gamepadAction_Discard = "WarehouseSellItem_Gamepad" 
            handle = inputMonitor:AddDisplayActionBinding(gamepadAction_Discard, EInputEvent.IE_Pressed,CommonIVInputLogic.ProcessDiscard,itemView, EDisplayInputActionPriority.UI_Stack)
            table.insert(CommonIVInputLogic.DisplayInputHandles,handle)
           
        else
            if not (InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_Preset or InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_QuickOperation ) then
                local gamepadAction_Discard = "LootingDropItem_Gamepad"
                handle = inputMonitor:AddDisplayActionBinding(gamepadAction_Discard, EInputEvent.IE_Pressed,CommonIVInputLogic.ProcessDiscard,itemView, EDisplayInputActionPriority.UI_Stack)
                loginfo("CommonIVInputLogic.BindDisplayInputs: " .. gamepadAction_Discard)
                -- if handle then
                --     -- 将绑定信息转到LootingKeyMapping_HD里处理
                --     CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction:Invoke(gamepadAction_Discard, true, handle)
                -- end
                table.insert(CommonIVInputLogic.DisplayInputHandles,handle)
                end
        end 

        -- 点击
        handle = inputMonitor:AddDisplayActionBinding("SelectAndCheckItem", EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessSimClick, nil, EDisplayInputActionPriority.UI_Stack)
        table.insert(CommonIVInputLogic.DisplayInputHandles, handle)

        -- 快捷转移
        if CommonIVInputLogic.IsExtendItem() == false and InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Default then
        handle = inputMonitor:AddDisplayActionBinding("CarryItem", EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessOperate, 
        nil, EDisplayInputActionPriority.UI_Stack)
        table.insert(CommonIVInputLogic.DisplayInputHandles, handle)
        end

        -- 使用消耗品
        if ItemOperaTool.CheckRunWarehouseLogic() == false and CommonIVInputLogic.IsItemEnableUse() == true then
            local actionName_UseItem = "LootingUseItem_Gamepad"
            handle = inputMonitor:AddDisplayActionBinding(actionName_UseItem, EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessUseItem, 
            nil, EDisplayInputActionPriority.UI_Stack)

            if handle then
                -- 将绑定信息转到LootingKeyMapping_HD里处理
                CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction:Invoke(actionName_UseItem, true, handle)
            end

            table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle, actionName = actionName_UseItem})

            local viewPanel = itemView:Wnd("ItemViewPanel",UIWidgetBase)
            -- 添加悬浮在控件上的使用提示
            local currentItem =  CommonIVInputLogic.GetHoverItem()
            local weakUIIns, instanceID = Facade.UIManager:AddSubUI(itemView, UIName2ID.CommonKeyIconTipsItem, viewPanel)
            local subUI = getfromweak(weakUIIns)
            if subUI then
                -- 需要走文本CCB，先给个固定文本
                local locText = CommonWidgetConfig.Loc.UseStr
                subUI:Init(itemView,actionName_UseItem,locText,handle)
                subUI.Slot:SetZOrder(6)
            end
        end

        -- 扩容箱
        -- if CommonIVInputLogic.IsExtendItem() == true and InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Default then
        --     local actionName_InstallExtension = "InstallExtension_Gamepad"
        --     handle = inputMonitor:AddHoldDisplayActionBinding(actionName_InstallExtension, CommonIVInputLogic.FastDoEquipItem,itemView,EDisplayInputActionPriority.UI_Stack)
        --     loginfo("CommonIVInputLogic.BindDisplayInputs: " .. actionName_InstallExtension)

        --     table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle, actionName = actionName_InstallExtension})

        -- end

        -- 安全箱
        if CommonIVInputLogic:IsSafeBox() and InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Default then
            local bChecked = Facade.ConfigManager:GetUserBoolean("SafeBoxbFixed", true)
            
            local actionName_SafeBoxPin = bChecked and "WarehousUnPinSafebox_Gamepad" or "WarehousPinSafebox_Gamepad"
            handle = inputMonitor:AddHoldDisplayActionBinding(actionName_SafeBoxPin, CommonIVInputLogic.FlipFlopSafeBoxPin,itemView,EDisplayInputActionPriority.UI_Stack)
            table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle, actionName = actionName_SafeBoxPin})
        end

        -- Looting用：标记散点物资
        if ItemOperaTool.CheckRunWarehouseLogic() == false then
            handle = inputMonitor:AddHoldDisplayActionBinding("LootingMarkItem_Gamepad", CommonIVInputLogic.SimProcessMark,itemView,EDisplayInputActionPriority.UI_Stack)
            if handle then
                -- 将绑定信息转到LootingKeyMapping_HD里处理
                CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction:Invoke("LootingMarkItem_Gamepad", true, handle)
            end

            table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle, actionName = "LootingMarkItem_Gamepad"})
        end

        -- if ItemOperaTool.CheckRunWarehouseLogic() == false  then
        --     CommonWidgetConfig.Events.evtRefreshSummaryAfterInputBinded:Invoke()
        -- end
        -- if true then
        --     local actionName_Transfer = "Transfer_Gamepad"
        --     handle = inputMonitor:AddHoldDisplayActionBinding(actionName_Transfer, CommonIVInputLogic.ProcessSplitAmmo,itemView,EDisplayInputActionPriority.UI_Stack)
        --     loginfo("CommonIVInputLogic.BindDisplayInputs: " .. actionName_Transfer)
        --     if handle then
        --         CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction:Invoke(actionName_Transfer, true, handle)
        --     end

        --     table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle, actionName = actionName_Transfer})
        -- end e

        -- 精确转移物品
        -- if ItemOperaTool.CheckRunWarehouseLogic() == false then
        -- if true then
        --     local curHoverItem = CommonIVInputLogic.GetHoverItem()
        --     if curHoverItem then
        --         local itemNeedTransferType = Module.CommonWidget:GetItemNeedTransferType(curHoverItem)
        --         if itemNeedTransferType == CommonWidgetConfig.EItemTransferType.FastEquipOrReplace then
        --             local actionName_FastEquipItem = "FastEquipItem_Gamepad"
        --             local handle_FastEquipItem = inputMonitor:AddDisplayActionBinding(actionName_FastEquipItem, EInputEvent.IE_Pressed, CommonIVInputLogic.FastDoEquipItem, 
        --             nil, EDisplayInputActionPriority.UI_Stack)
        
        --             if handle_FastEquipItem then
        --                 -- 将绑定信息转到LootingKeyMapping_HD里处理
        --                 CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction:Invoke(actionName_FastEquipItem, true, handle_FastEquipItem)
        --                 table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle_FastEquipItem, actionName = actionName_FastEquipItem})
        --             end

        --         elseif itemNeedTransferType == CommonWidgetConfig.EItemTransferType.TransferItemPanel then
        --             local actionName_PlaceItemToSlot = "PlaceItemToSlot_Gamepad"
        --             -- local handle_PlaceItemToSlot = inputMonitor:AddDisplayActionBinding(actionName_PlaceItemToSlot, EInputEvent.IE_Pressed, CommonIVInputLogic.OpenTransferItemBtnListPanel, 
        --             -- nil, EDisplayInputActionPriority.UI_Stack)
        --             handle = inputMonitor:AddHoldDisplayActionBinding(actionName_PlaceItemToSlot, CommonIVInputLogic.OpenTransferItemBtnListPanel,itemView,EDisplayInputActionPriority.UI_Stack)
        
        --             if handle_PlaceItemToSlot then
        --                 -- 将绑定信息转到LootingKeyMapping_HD里处理
        --                 CommonWidgetConfig.Events.evtGlobalItemBindLongPressedAction:Invoke(actionName_PlaceItemToSlot, true, handle_PlaceItemToSlot)
        --                 table.insert(CommonIVInputLogic.LongPressDisplayInputHandles, {inputHandle = handle_PlaceItemToSlot, actionName = actionName_PlaceItemToSlot})
        --             end
        --         end
        --     end
        -- end
    end

    if IsHD() and Module.CommonWidget.IsEnableGamepadItemShortcutKey() and WidgetUtil.IsStopSimulatedMouseDrag() and WidgetUtil.IsGamepad() and not Module.CommonWidget:IsInMultiSelectedMode() and itemView and ItemOperaTool.CheckIsDIYCabinetPanel() then
        -- 快捷转移
        if CommonIVInputLogic.IsExtendItem() == false then
        handle = inputMonitor:AddDisplayActionBinding("CarryItem", EInputEvent.IE_Pressed, CommonIVInputLogic.ProcessOperate, nil, EDisplayInputActionPriority.UI_Stack)
        table.insert(CommonIVInputLogic.DisplayInputHandles, handle)
        end
    end
    --END MODIFICATION

end

function CommonIVInputLogic.UnbindDisplayInputs()
    CommonIVInputLogic.bBindDisplayAction = false
    -- if CommonIVInputLogic.NavConfigHandler then
    --     WidgetUtil.DisableDynamicNavConfig(CommonIVInputLogic.NavConfigHandler )
    --     CommonIVInputLogic.NavConfigHandler = nil
    -- end
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    for _, handle in ipairs(CommonIVInputLogic.DisplayInputHandles) do
        inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
    end
    CommonIVInputLogic.DisplayInputHandles = {}

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        for _, LongPressHandle in ipairs(CommonIVInputLogic.LongPressDisplayInputHandles) do
            if LongPressHandle.inputHandle then
                inputMonitor:RemoveHoldDisplayActoinBingingForHandle(LongPressHandle.inputHandle)

                if LongPressHandle.actionName then
                    CommonWidgetConfig.Events.evtGlobalItemUnBindLongPressedAction:Invoke(LongPressHandle.actionName, true)
                end
            end
        end

        CommonIVInputLogic.LongPressDisplayInputHandles = {}
    end
    --END MODIFICATION
end

function CommonIVInputLogic._OnItemMove(moveItemInfo)
    local bHideTips
    local hoverItem = CommonIVInputLogic.GetHoverItem()
    if not hoverItem then
        bHideTips = true
    elseif moveItemInfo.item == hoverItem and moveItemInfo.Reason ~= PropChangeType.Modify then
        bHideTips = true
    end

    -- local item = moveItemInfo.item
    if bHideTips then
        CommonIVInputLogic.OnIVMouseLeave(CommonIVInputLogic.GetHoverView())
    end
end

function CommonIVInputLogic.SetInputSummary(bAdd, itemview)
    -- inputSummarySet
    -- 出售模式的左下角快捷键和整理模式的左下角快捷键会与悬停itemview时左下角快捷键有冲突，所以需要处理
    -- 如果有Popui，那么悬停itemview不更新快捷键
    -- Module.ItemDetail:GetIsSplitPanelOpened()  这个接口有问题
    if Module.CommonWidget:GetOpenedPopItemView() ~= nil or Module.ItemDetail:GetIsSplitPanelOpened() then
    -- if Module.CommonWidget:GetOpenedPopItemView() ~= nil then
        if not bAdd then
            Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
        end
        return
    end
    -- if Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode or Module.Inventory:GetWHExtMode() then
    if Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode then
        Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
        return
    end
    local item = itemview and itemview.item
    local itemSlot = item and item.InSlot
    local itemFeature = item and item:GetFeature(EFeatureType.Equipment)
    local isExtItem = itemFeature and itemFeature:IsExtendItem()
    -- 缓存的当前仓库模块工作状态
    if bAdd and ((itemSlot and not Module.Inventory.Config.SlotsNotResponseR[itemSlot.SlotType]) or not itemSlot) then
        local summaryList = {}
        local InvSlotViewType = Module.Inventory:GetInvSlotViewType()
        local bIsCHDIY = ItemOperaTool.CheckIsDIYCabinetPanel()
        --BEGIN MODIFICATION @ VIRTUOS : 根据输入类型调整Item按键提示
        local curInputType = UGPInputHelper.GetCurrentInputType(GetGameInstance())

        if InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Default then
            if curInputType == EGPInputType.MouseAndKeyboard then
                table.insert(summaryList, {actionName = "TransferItem", func = CommonIVInputLogic.ProcessOperate, caller = nil ,bUIOnly = false, bHideIcon = false})
            else
                if curInputType == EGPInputType.Gamepad then
                    -- 通用提示（不生效情况：扩容箱管理，整理，批量出售）
                    local bUseDefaultSettings = not (Module.CommonWidget:IsInMultiSelectedMode() or Module.Inventory:GetWHExtManageMode() or Module.Inventory:GetWHExtManageMode())
                    if bUseDefaultSettings then
                        table.insert(summaryList, {actionName = "SelectAndCheckItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
                        -- 快捷转移（不生效情况：当前焦点道具类型为扩容箱）
                        if not  isExtItem  then
                        table.insert(summaryList, {actionName = "CarryItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
                        end
                        if not bIsCHDIY then
                        table.insert(summaryList, {actionName = "WarehouseSellItem_Gamepad", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
                        end
                        -- she3 临时，处理收藏室DIY展台上物品
                        if itenSlot then
                            table.insert(summaryList, {actionName = "BeginSwapItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
                        end
                        
                        -- if isExtItem  then
                        --     table.insert(summaryList, {actionName = "InstallExtension_Gamepad", func = nil, caller = itemview ,bUIOnly = true, bHideIcon = false})
                        -- end
                    end
                end

                -- 出售模式配置
                -- if Module.CommonWidget:IsInMultiSelectedMode() then
                --     -- table.insert(summaryList, {actionName = "SellItem", func = CommonIVInputLogic.ProcessOperate, caller = nil ,bUIOnly = false, bHideIcon = false})
                -- end
                -- 整理模式配置
                if Module.Inventory:GetWHExtManageMode() then
                end

                -- 扩容箱管理模式配置
                if  Module.Inventory:GetWHExtManageMode() then

                end

                -- table.insert(summaryList, {actionName = "Transfer_Gamepad", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            
                -- local itemNeedTransferType = Module.CommonWidget:GetItemNeedTransferType(item)
                -- if itemNeedTransferType == CommonWidgetConfig.EItemTransferType.FastEquipOrReplace then
                --     table.insert(summaryList, {actionName = "FastEquipItem_Gamepad", func = CommonIVInputLogic.FastDoEquipItem, caller = nil ,bUIOnly = false, bHideIcon = false})
                -- elseif itemNeedTransferType == CommonWidgetConfig.EItemTransferType.TransferItemPanel then
                --     -- table.insert(summaryList, {actionName = "PlaceItemToSlot_Gamepad", func = CommonIVInputLogic.OpenTransferItemBtnListPanel, caller = nil ,bUIOnly = false, bHideIcon = false})
                -- end
            end
        end
        
        if InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Auciton then
            if curInputType == EGPInputType.Gamepad then
                -- //TODO 换为转移至占位
                table.insert(summaryList, {actionName = "Transfer_Gamepad", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
                table.insert(summaryList, {actionName = "BeginSwapItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            end
        end

        if InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_QuickOperation then
            if curInputType == EGPInputType.Gamepad then
                table.insert(summaryList, {actionName = "BeginSwapItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            end
        end

        if InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_Preset then
            if curInputType == EGPInputType.Gamepad then
                table.insert(summaryList, {actionName = "BeginSwapItem", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            end
        end
        --END MODIFICATION
        
        if InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_QuickOperation or InvSlotViewType == Module.Inventory.Config.EInvSlotViewType.Assembly_Preset then
            Module.Inventory.Config.Events.evtBottomBarSetting:Invoke(summaryList,itemview)
        else
            Module.Inventory.Config.Events.evtBottomBarSetting:Invoke(summaryList)
        end
    else
        -- 先特例处理安全箱
        if CommonIVInputLogic:IsSafeBox() then
            local summaryList = {}
            local bChecked = Facade.ConfigManager:GetUserBoolean("SafeBoxbFixed", true)
            local actionName_SafeBoxPin = bChecked and "WarehousUnPinSafebox_Gamepad" or "WarehousPinSafebox_Gamepad"
            table.insert(summaryList, {actionName = actionName_SafeBoxPin, func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            table.insert(summaryList, {actionName = "WarehouseCommonChange_Gamepad", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            Module.Inventory.Config.Events.evtBottomBarSetting:Invoke(summaryList)
            return
        end
        -- 在特例处理近战武器/卡包
        if itemSlot and (itemSlot.SlotType == ESlotType.MeleeWeapon or itemSlot.SlotType == ESlotType.KeyChain) then
            local summaryList = {}
            table.insert(summaryList, {actionName = "WarehouseCommonChange_Gamepad", func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
            Module.Inventory.Config.Events.evtBottomBarSetting:Invoke(summaryList)
            return
        end
        Module.Inventory.Config.Events.evtBottomBarSetting:Invoke()
    end
end
--endregion
-----------------------------------------------------------------------

function CommonIVInputLogic.GetHoverView()
    return CommonIVInputLogic.CurrentHoverView[1]
end

function CommonIVInputLogic.GetLastHoverView()
    return CommonIVInputLogic.LastHoverView[1]
end

function CommonIVInputLogic.SetHoverView(view)
    local lastHoverView = CommonIVInputLogic.GetHoverView()
    if lastHoverView then
        CommonIVInputLogic.SetLastHoverView(lastHoverView)
    end
    CommonIVInputLogic.CurrentHoverView[1] = view
end

function CommonIVInputLogic.SetLastHoverView(view)
    CommonIVInputLogic.LastHoverView[1] = view
end

function CommonIVInputLogic.GetHoverItem()
    local hoverView =  CommonIVInputLogic.GetHoverView()
    if hoverView then
        return hoverView.item
    end

    return nil
end

function CommonIVInputLogic.SetAndStartHoverTimer(Time, Callback)
    CommonIVInputLogic.ReleaseHoverTimer()
    if Time <= 0 then
        Callback()
        return
    end
    CommonIVInputLogic.HoverTimer = Timer:NewIns(Time)
    local function NewCallback()
        Callback()
        CommonIVInputLogic.ReleaseHoverTimer()
    end
    CommonIVInputLogic.HoverTimer:AddListener(NewCallback, CommonIVInputLogic)
    CommonIVInputLogic.HoverTimer:Start()
end

function CommonIVInputLogic.ReleaseHoverTimer()
    if CommonIVInputLogic.HoverTimer then
        CommonIVInputLogic.HoverTimer:Release()
        CommonIVInputLogic.HoverTimer = nil
    end
end

function CommonIVInputLogic.OpenHoverTips(itemView)
    local data
    if itemView and itemView.item then
        local item = itemView.item
        local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
        local bIsInSolTrophyView = (Facade.UIManager:GetCurrentStackUIId() == UIName2ID.EvacuatePrivateTrophyInfoViewHD)
        if item then
            if item:CheckItemBeingUsed() then
                return
            end
            if CommonIVInputLogic.CurrEnterTextIcon then
                local IconImagePath = CommonIVInputLogic.CurrEnterTextIcon:GetIconImagePath()
                if IconImagePath == CommonWidgetConfig.ITEM_VIEW_LOCK_ICON_PATH then
                    data = {{tips = LootingConfig.Loc.BindItemHoverTip}}
                end
            end
            if not data then
                -- 再这里处理不同类型道具的HoverTip
                local inSlot = item.InSlot
                local price = item:GetTotalSellPrice()
                if price == 0 and IsInEditor() and item.GetSingleSellPrice then
                    -- price = item.originValue --Long1替换为动态指导价
                    price = item:GetSingleSellPrice()
                end
                local txt = ShopHelperTool.GetDynamicGuidePriceRichTextByItemV3(item, item.num,
                    false, false, nil, false)
                if inSlot then
                    if inSlot.SlotType == ESlotType.KeyChain then
                        if bRunWarehouseLogic then
                            if bIsInSolTrophyView then return end
                            data = {{tips = Module.Inventory.Config.Loc.KeyChainHoverTips, styleID = "C001"},
                                    {tips = Module.Inventory.Config.Loc.KeyChainHoverTipsDesc}}
                        else
                            data = {{tips = LootingConfig.Loc.KeyChainHoverTip}}
                        end
                    elseif inSlot.SlotType == ESlotType.SafeBox then
                        if bRunWarehouseLogic then
                            data = {{tips = Module.Inventory.Config.Loc.SafeBoxHoverTips, styleID = "C001"},
                                    {tips = Module.Inventory.Config.Loc.SafeBoxHoverTipsDesc}}
                        else
                            data = {{tips = LootingConfig.Loc.SafeBoxHoverTip}}
                        end
                    elseif inSlot.SlotType == ESlotType.MeleeWeapon then
                        data = {{tips = item.name}}
                    elseif price > 0 then
                        data = {{tips = item.name}, {tips = txt}}
                    end
                elseif price > 0 then
                    data = {{tips = item.name}, {tips = txt}}
                end
            end
        end
    end

    if data then
        CommonIVInputLogic.ShowCommonHoverTipsHD(data, itemView)
    else
        CommonIVInputLogic.CloseCommonHoverTipsHD()
    end
end

---@param itemView IVWarehouseTemplate
function CommonIVInputLogic.OnShowHoverTips(itemView)
    -- Show mouse hover
    local item = itemView.item
    if not item or not item:CheckShouldTriggerHoverLogic() then
        return
    end

    -- 多选模式下也不响应
    if Module.CommonWidget:IsInMultiSelectedMode() then
        return
    end

    CommonIVInputLogic.SetAndStartHoverTimer(CommonWidgetConfig.MOUSE_ENTER_SHOW_TIPS_INTERVAL, function() CommonIVInputLogic.OpenHoverTips(itemView) end)
end

function CommonIVInputLogic.OnHideHoverTips(itemView)
    CommonIVInputLogic.ReleaseHoverTimer()
    CommonIVInputLogic.CloseCommonHoverTipsHD()
end

function CommonIVInputLogic.OnIVMouseEnter(itemView)
    CommonIVInputLogic.SetHoverView(itemView)
    -- 多选模式下也不响应
    if Module.CommonWidget:IsInMultiSelectedMode() then
        return
    end
    
    -- dynamic bind display action
    CommonIVInputLogic.BindDisplayInputs(itemView)
    CommonIVInputLogic.SetInputSummary(true, itemView)
end

function CommonIVInputLogic.OnIVMouseLeave(itemView)
    CommonIVInputLogic.UnbindDisplayInputs()
    CommonIVInputLogic.SetHoverView(nil)
    CommonIVInputLogic.SetInputSummary(false)
    if itemView then
        itemView:ClearGamepadKeyTip()
    end
end

---@param itemTextIcon IVTextIconComponent
function CommonIVInputLogic.OnIVTextIconMouseEnter(itemTextIcon)
    CommonIVInputLogic.CurrEnterTextIcon = itemTextIcon
    if not itemTextIcon then
        return
    end
    local itemView = itemTextIcon:GetAssignedItemView()
    if CommonIVInputLogic.HoverTipHandle then
        CommonIVInputLogic.OpenHoverTips(itemView)
    -- elseif not CommonIVInputLogic.HoverTimer then
    --     CommonIVInputLogic.SetAndStartHoverTimer(CommonWidgetConfig.MOUSE_ENTER_SHOW_TIPS_INTERVAL, function() CommonIVInputLogic.OpenHoverTips(itemView) end)
    end
end

---@param itemTextIcon IVTextIconComponent
function CommonIVInputLogic.OnIVTextIconMouseLeave(itemTextIcon)
    CommonIVInputLogic.CurrEnterTextIcon = nil
    if CommonIVInputLogic.HoverTipHandle then
        CommonIVInputLogic.OpenHoverTips(itemTextIcon:GetAssignedItemView())
    end
end

---@param moveItemInfo itemMoveInfo
function CommonIVInputLogic.OnItemMove(moveItemInfo)
    local hoverItem = CommonIVInputLogic.GetHoverItem()
    if not hoverItem then
        return false
    end

    local item = moveItemInfo.item
    if hoverItem == item then
        CommonIVInputLogic.OnIVMouseLeave(CommonIVInputLogic.GetHoverView())
    end
end

function CommonIVInputLogic.OnIVImmediateClicked(itemView)
    if not fCheckEnableInput() then return true end
    -- if itemView == nil then
    --     itemView = CommonIVInputLogic.GetHoverView()
    -- end
    -- if itemView == nil then
    --     return
    -- end

    local item = itemView.item
    if not item then
        return true
    end

    -- 多选模式下也不响应
    if Module.CommonWidget:IsInMultiSelectedMode() then
        return true
    end

    if Facade.GameFlowManager:IsInOBMode() then
        return true
    end

    Module.ItemDetail:CloseAllPopUI()

    if item and item.InSlot then
        local slotType = item.InSlot.SlotType
        local SlotsNotAllowOperate = ItemOperaTool.CheckRunWarehouseLogic() and Module.Inventory.Config.SlotsNotAllowOperate
                or Module.Looting.Config.SlotsNotAllowOperate
        if SlotsNotAllowOperate[slotType] then
            logwarning("CommonIVInputLogic:OnImmediateClicked blocked", item.id, slotType)
            return true
        end
    end

    local inputMonitor = Facade.UIManager:GetInputMonitor()
    local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
    if inputMonitor:IsKeyPressedByName("LeftAlt") then
        loginfo("CommonIVInputLogic.OnIVImmediateClicked", "LeftAlt", item.id)
        LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_AltClickL)
        -- 记录道具操作来源
        ItemMoveCmd.RecordMoveOperation(ItemMoveCmd.EFromOperation.ItemViewAltClickL)
        if bRunWarehouseLogic then
            ItemOperaTool.DoEquipItem(item)
        else
            if item and item.InSlot and (item.InSlot:GetSlotGroup() ~= ESlotGroup.Player
                    or item.InSlot.SlotType ~= ESlotType.MainWeaponLeft) then
                Module.Looting:DoEquipItem(item, true)
            end
        end
        return false
        -- fTriggerIVImmediateClick()
    elseif inputMonitor:IsKeyPressedByName("LeftControl") then
        loginfo("CommonIVInputLogic.OnIVImmediateClicked", "LeftControl", item.id)
        LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_CtrlClickL)
        -- 记录道具操作来源
        ItemMoveCmd.RecordMoveOperation(ItemMoveCmd.EFromOperation.ItemViewCtrlClickL)
        if bRunWarehouseLogic then
            if item:IsInDepository() then
                ItemOperaTool.DoCarryItem(item)
            else
                CommonWidgetConfig.Events.evtItemDoubleClicked:Invoke(itemView)
            end
        else
            if item and item.InSlot then
                ---@type ItemSlot
                local itemSlot = item.InSlot
                if itemSlot:GetSlotGroup() == Server.LootingServer:GetSelfSlotGroup() then
                    if Server.LootingServer:GetCurrentInteractingBox() then
                        local targetInteractingSlot = Module.Looting:GetCurrentInteractingSlot(item)
                        if targetInteractingSlot then
                            ItemOperaTool.DoPlaceItem(item, targetInteractingSlot, true)
                        end
                    elseif Server.LootingServer:GetCurrentInteractorType() == EGamePickupType.NearbyPickups then
                        local discardSlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
                        if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, discardSlot) then
                            ItemOperaTool.DoPlaceContainerItems(item, discardSlot, true)
                        else
                            ItemOperaTool.DoPlaceItem(item, discardSlot, true)
                        end
                    else
                        Module.Looting:DoQuickLootItem(item, ESlotGroup.Player, true, false, true)
                    end
                else
                    Module.Looting:DoQuickLootItem(item, ESlotGroup.Player, true, false, true)
                end
            end
        end
        return false
    elseif inputMonitor:IsKeyPressedByName("LeftShift") then
        loginfo("CommonIVInputLogic.OnIVImmediateClicked", "LeftShift", item.id, item.num)
        if ItemOperaTool.CheckRunWarehouseLogic() then
            Module.Inventory:DoSelectItemInWH(itemView, true)
        else
            Module.Looting:DoSelectItem(itemView, true, false)
        end
        return false
    end

    return true
end

-- 快速操作
function CommonIVInputLogic.ProcessOperate()
    if not fCheckEnableInput() then return false end

    local hoverView =  CommonIVInputLogic.GetHoverView()
    if not hoverView then
        return false
    end

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        local openBagTime = Module.Looting:GetOpenBagTime()
        local nowTime = UGameplayBlueprintHelper.GetServerTimeSeconds(GetGameInstance())
        local openBagFCoolDown = Module.Looting.Config.F_COOL_DOWN
        if openBagFCoolDown > 0 and nowTime - openBagTime < openBagFCoolDown  then
            logwarning("CommonIVInputLogic.ProcessOperate F not cool down", openBagTime, nowTime, openBagFCoolDown)
            return false
        end
    end

    ItemMoveCmd.RecordMoveOperation(ItemMoveCmd.EFromOperation.ShortKey)

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        local item = hoverView and hoverView.item
        local slot = item and item.InSlot
        if slot and slot.SlotType  then
            if Module.Looting.Config.SlotsNotAllowOperate[slot.SlotType] then
                loginfo("CommonIVInputLogic.ProcessOperate F blocked because item is in slot not allowed to operate", item.name, item.gid)
                return
            end
        end
    end

    if ItemOperaTool.CheckRunCollectionRoomLootingLogic() then
        local item = hoverView and hoverView.item
        local slot = item and item.InSlot
        if slot and slot.SlotType  then
            local slotType = slot.SlotType
            if not Module.Inventory.Config.SlotsAllowOperateInCollectionRoom[slotType] then
                return
            end
        end
    end

    -- 模拟双击
    if not ItemOperaTool.CheckIsDIYCabinetPanel() then
        if IsHD() and WidgetUtil.IsGamepad() then
            local item = hoverView and hoverView.item
            InventoryNavManager.nextTargetItem = Module.Inventory:GetNavNextTarget(item)
            InventoryNavManager.bIsQuickCarry = true
            end
    end 
    
    CommonWidgetConfig.Events.evtItemDoubleClicked:Invoke(hoverView)

    return true
end

-- 丢弃
function CommonIVInputLogic.ProcessDiscard()
    loginfo("CommonIVInputLogic.ProcessDiscard")
    LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_AltD)
    if not fCheckEnableInput() then return false end

    local item = CommonIVInputLogic.GetHoverItem()
    if not item then
        return false
    end

    local inSlot = item and item.InSlot
    if not inSlot then
        return false
    end
    loginfo("CommonIVInputLogic.ProcessDiscard", item.id)
    local bOpera = Module.Inventory.Config.SlotsNotAllowOperate[inSlot.SlotType]

    InventoryNavManager.bIsQuickCarry = true
    if not item then
        logerror("CommonIVInputLogic.Process Discard Hovered Item is Empty")
    end
    InventoryNavManager.nextTargetItem = Module.Inventory:GetNavNextTarget(item)
    if InventoryNavManager.nextTargetItem then
        loginfo("CommonIVInputLogic.Process Discard GetNavNextTarget Got Result", InventoryNavManager.nextTargetItem.shortName, InventoryNavManager.nextTargetItem.gid)
    else
        logwarning("CommonIVInputLogic.Process Discard GetNavNextTarget Got Empty")
    end
    
    -- 租赁道具
    if ItemOperaTool.CheckRunWarehouseLogic() then
        if Server.ArmedForceServer:CheckIsRentalStatus() then
            if inSlot and Server.ArmedForceServer:IsRentalReferenceSlot(inSlot.SlotType) then
                bOpera = true
            end
        end
    end
    
    if bOpera then
        return false
    end
    if ItemOperaTool.CheckRunWarehouseLogic() then
        if not ItemOperaTool.bInSettlement then
            ItemOperaTool.DoSellItems({item})
        end
    else
        Module.Looting:DoDiscardItem(item)
    end

    if ItemOperaTool.CheckRunCollectionRoomLootingLogic() then
        if inSlot and inSlot.SlotType  then
            local slotType = inSlot.SlotType
            if not Module.Inventory.Config.SlotsAllowOperateInCollectionRoom[slotType] then
                return
            end
        end
    end

    return true
end
-- 点击
function CommonIVInputLogic.ProcessSimClick()
    loginfo("CommonIVInputLogic.ProcessProcessSimClick")
    local itemView = CommonIVInputLogic.GetHoverView()
    if not itemView then
        return false
    end
    if itemView.bHandleClick then
        -- itemView:OnClicked()
        WidgetUtil.TrySimulateMouseClick()
    else
        return false
    end
    return true
end

function CommonIVInputLogic.SimProcessMark()
    -- 暂时只接入标记，不接取消标记
    CommonIVInputLogic.ProcessMark(true)
end

-- 标记
function CommonIVInputLogic.ProcessMark(bDown)
    if not fCheckEnableInput() then return false end
    if not bDown then return end

    local item = CommonIVInputLogic.GetHoverItem()
    if not item then
        local lastHoverItemView = CommonIVInputLogic.GetLastHoverView()
        if lastHoverItemView and lastHoverItemView:IsVisible() then
            -- local viewportScale = UWidgetLayoutLibrary.GetViewportScale(GetWorld())
            -- local mousePos = UWidgetLayoutLibrary.GetMousePositionOnPlatform(GetGameInstance()) * viewportScale
            local mousePos = UWidgetLayoutLibrary.GetMousePositionOnPlatform()
            if UIUtil.CheckAbsolutePointInsideWidget(lastHoverItemView, mousePos) then
                item = lastHoverItemView.item
            end
        end
    end
    if not item then
        return false
    end

    if Module.Looting:CheckItemMarkable(item) then
        Module.Looting:ToggleItemMarkedByLooting(item)
    end

    return true
end

function CommonIVInputLogic.ShowCommonHoverTipsHD(data, caller)
    local contents = {}
    for _, dataInfo in ipairs(data) do
        -- table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = tip, styleRowId = "C002"}})
        dataInfo.styleID = setdefault(dataInfo.styleID, "C002")
        table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = dataInfo.tips, styleRowId = dataInfo.styleID}})
    end
    if #contents > 0 then
        local tipsAnchor = nil
        if caller.GetTipsAnchor then
            tipsAnchor = caller:GetTipsAnchor()
        end
        CommonIVInputLogic.CloseCommonHoverTipsHD()
        local handle = Module.CommonTips:ShowAssembledTips(contents, tipsAnchor)
        CommonIVInputLogic.HoverTipHandle = handle
        CommonIVInputLogic.HoverTipAnchor.Anchor = tipsAnchor
    end
end

function CommonIVInputLogic.CloseCommonHoverTipsHD()
    if CommonIVInputLogic.HoverTipHandle then
        if CommonIVInputLogic.HoverTipAnchor.Anchor then
            Module.CommonTips:RemoveAssembledTips(CommonIVInputLogic.HoverTipHandle, CommonIVInputLogic.HoverTipAnchor.Anchor)
        else
            Facade.UIManager:CloseUIByHandle(CommonIVInputLogic.HoverTipHandle)
        end
        CommonIVInputLogic.HoverTipHandle = nil
        CommonIVInputLogic.HoverTipAnchor.Anchor = nil
    end
end

function CommonIVInputLogic.SetBottomBarInput()
    local summaryList = {}
    table.insert(summaryList, {actionName = "TransferAll", func = nil, caller = nil ,bUIOnly = false, bHideIcon = false})
    table.insert(summaryList, {actionName = "TransferItem", func = nil, caller = nil, bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)
end

--BEGIN MODIFICATION @ VIRTUOS : 
function CommonIVInputLogic.BindHoverViewDisplayInput()
    local curHoverView = CommonIVInputLogic.GetHoverView()
    if curHoverView then
        if CommonIVInputLogic.bBindDisplayAction == false then
            CommonIVInputLogic.BindDisplayInputs(itemView)
        end

        CommonIVInputLogic.SetInputSummary(true, curHoverView)
    else
        CommonIVInputLogic.SetInputSummary(false)
    end
end

function CommonIVInputLogic.UnBindHoverViewDisplayInput()
    local curHoverView = CommonIVInputLogic.GetHoverView()
    if curHoverView and CommonIVInputLogic.bBindDisplayAction == true then
        CommonIVInputLogic.UnbindDisplayInputs()
    end

    CommonIVInputLogic.SetInputSummary(false)
end

function CommonIVInputLogic.IsItemEnableUse()
    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    if curHoverItem == nil then
        return false
    end

    return Module.CommonWidget:IsItemEnableUse(curHoverItem)
end

function CommonIVInputLogic.IsItemAmmo()
    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    if curHoverItem == nil then
        return false
    end
    return curHoverItem:IsBullet()
end


function CommonIVInputLogic.IsExtendItem()
    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    if curHoverItem == nil then
        return false
    end
    local equipmentFeature = curHoverItem:GetFeature(EFeatureType.Equipment)
    if equipmentFeature~= nil and equipmentFeature:IsExtendItem() then
        return true
    end
    return false
           
end

function CommonIVInputLogic.IsSafeBox()
    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    if curHoverItem == nil then
        return false
    end
    local equipmentFeature = curHoverItem:GetFeature(EFeatureType.Equipment)
    if equipmentFeature~= nil and equipmentFeature:IsSafeBox() then
        return true
    end
    return false
           
end

function CommonIVInputLogic.ProcessUseItem()
    if not IsHD() then
        return 
    end

    if CommonIVInputLogic.IsItemEnableUse() == false then
        return
    end

    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    if curHoverItem then
        local bFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
        local bClientUseResult = true
        if bFrontEnd then
            -- 局外使用
            bClientUseResult = Server.InventoryServer:DoUseItem(curHoverItem)
            if not bClientUseResult then
                Module.CommonTips:ShowSimpleTip(Module.ItemDetail.Config.Loc.errItemUsedFailed) -- 道具使用失败
            end
        else
            -- 局内使用
            bClientUseResult = Module.Looting:DoReqUseItem(curHoverItem)
        end
    end
end

function CommonIVInputLogic.ProcessSplitAmmo()
    loginfo("CommonIVInputLogic.ProcessSplitAmmo")
    if not IsHD() then
        return
    end
    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    local curHoverView = CommonIVInputLogic.GetHoverView()
    if curHoverItem and curHoverView then
        local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
        local bSplittedSuccess = false

        if bRunWarehouseLogic == true then
            bSplittedSuccess = Module.Inventory:OpenItemSplitPanel(curHoverItem,curHoverView)
        else
            bSplittedSuccess = Module.Looting:OpenItemSplitPanel(curHoverItem,curHoverView)
        end

        if bSplittedSuccess then
            Module.CommonWidget:BeginToFocusItemView(curHoverItem)
        end
    end
end


function CommonIVInputLogic.FastDoEquipItem()
    if not IsHD() then
        return
    end

    local curHoverItem = CommonIVInputLogic.GetHoverItem()
    if curHoverItem then
        local bRunWarehouseLogic = ItemOperaTool.CheckRunWarehouseLogic()
        local bEquippedSuccess = false

        if bRunWarehouseLogic == true then
            bEquippedSuccess = ItemOperaTool.DoEquipItem(curHoverItem)
        else
            bEquippedSuccess = Module.Looting:DoEquipItem(curHoverItem, true)
        end

        if bEquippedSuccess then
            Module.CommonWidget:BeginToFocusItemView(curHoverItem)
        end
    end
end

function CommonIVInputLogic.OpenTransferItemBtnListPanel()
    if not IsHD() then
        return
    end

    if not WidgetUtil.IsGamepad() then
        return
    end

    -- 打开转移详细页面
    local curHoverView = CommonIVInputLogic.GetHoverView()
    if curHoverView then
        local widgetGeo = curHoverView:GetCachedGeometry()
        local curPos = widgetGeo:GetAbsolutePosition()
        local widgetSize = widgetGeo:GetAbsoluteSize()
        curPos.X = curPos.X + widgetSize.X * 0.5
        curPos.Y = curPos.Y + widgetSize.Y * 0.5

        Module.CommonWidget:SetOpenedPopItemView(curHoverView, curHoverView.item)

        if ItemOperaTool.CheckRunWarehouseLogic() then
            local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
            InventoryLogic.OpenTransferItemBtnListPanelInWH(curHoverView, curPos)
        else
            local LootingLogic = require "DFM.Business.Module.LootingModule.LootingLogic"
            LootingLogic.OpenTransferItemBtnListPanel(curHoverView, curPos)
        end
    end
end

function CommonIVInputLogic.FlipFlopSafeBoxPin()
    Module.Inventory.Config.Events.evtFlipFlopSafeBoxPinState:Invoke()
end

function CommonIVInputLogic._OnProcessItemMoveResult(bSuccess,item)
    if not (IsHD() and WidgetUtil.IsGamepad()) then
        return
    end
    
    if bSuccess and item and InventoryNavManager.bIsQuickCarry then
        -- InventoryNavManager.nextTargetItem 可以为空值，会触发保底焦点逻辑
        InventoryNavManager.FocusByItem(InventoryNavManager.nextTargetItem)
        InventoryNavManager.nextTargetItem = nil
        InventoryNavManager.bIsQuickCarry = false
    end
end

--END MODIFICATION

return CommonIVInputLogic
