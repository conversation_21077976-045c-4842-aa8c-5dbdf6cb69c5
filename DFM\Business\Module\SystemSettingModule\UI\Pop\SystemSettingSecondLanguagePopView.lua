----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------



local SystemSettingSecondLanguagePopView = ui("SystemSettingSecondLanguagePopView")

local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local SettingConfig = require "DFM.Business.Module.SystemSettingModule.SystemSettingConfig"
local GameSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.GameSettingLogicHD"
local ComminicateSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.ComminicateSettingLogic"
local UGPAudioStatics = import"GPAudioStatics"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local EVersionType= import"EVersionType"
local function log(...)
    loginfo("[SystemSettingSecondLanguagePopView]", ...)
end

function SystemSettingSecondLanguagePopView:Ctor()
    self._checkBoxMap = {} -- 以语言码保存checkBox对象

    self._cultureKeyList = {} -- 保存当前语言的配置信息的Key，存在先后顺序

    self._wtCommonPopWinV2 = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self.CloseSelf,self)
    self._wtCommonPopWinV2:BindCloseCallBack(fCallbackIns)

    self:InitCultureList()
    self:SetBtnType()
end

function SystemSettingSecondLanguagePopView:InitExtraData()
    if IsHD() then
        self._Setting = import("ClientGameSettingHD").Get()
    else
        self._Setting = import("ClientComminicateSetting").Get()
    end
    if self._Setting then
        self._secondLanguage = self._Setting.SecondLanguage
        self._preLanguage = self._secondLanguage
    end
end

function SystemSettingSecondLanguagePopView:SetBtnType()
    local btnIns = self._wtCommonPopWinV2:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm,
    {
        { btnText = SettingConfig.Loc.confirmBtnText, fClickCallback = self._OnConfirmBtnClick, caller = self,
            bNeedClose = false },
    }, true)
    if #btnIns > 0 then
        self._wtCancelBtn = btnIns[1]
    end

    -- 重载按钮音频
    if self._wtConfirmBtn and self._wtConfirmBtn.Button_Common then
        self._wtConfirmBtn.Button_Common:OverLoadSound(DFMAudioRes.UIConfirm)
    end

end


function SystemSettingSecondLanguagePopView:InitCultureList()
    log("InitCultureList()")
    self._cultureKeyList = {} -- 先置空
    self._cultureConfig = Facade.TableManager:GetTable("Localize/LocalizeCultureConfig")
    for cultureKey, value in pairs(self._cultureConfig) do
        if not self._cultureKeyList[cultureKey] then
            if VersionUtil.IsShipping() then
                if table.contains(value.Versions, EVersionType.Shipping) and LocalizeTool.CheckPlatform(value.Platforms) then
                    table.insert(self._cultureKeyList, cultureKey)
                end
            else
                if table.contains(value.Versions, EVersionType.Test) and LocalizeTool.CheckPlatform(value.Platforms) then
                    table.insert(self._cultureKeyList, cultureKey)
                end
            end
        end
        -- table.insert(self._cultureKeyList, cultureKey)
    end
    -- 排序，目前是乱序
    table.sort(self._cultureKeyList, function(a, b)
        return a < b
    end)
end

function SystemSettingSecondLanguagePopView:OnOpen()
    self:_InitScrollGridBox()
end

function SystemSettingSecondLanguagePopView:_InitScrollGridBox()
    log("SystemSettingSecondLanguagePopView:_InitScrollGridBox()")
    self._wtCultureList = UIUtil.WndScrollGridBox(self, "CultureList", self.OnGetItemCount, self.OnProcessItemWidget)
    self._wtCultureList:RefreshAllItems()
end

function SystemSettingSecondLanguagePopView:OnGetItemCount()
    log("SystemSettingSecondLanguagePopView:OnGetItemCount()", #self._cultureKeyList)
    return #self._cultureKeyList
end

---@param itemWidget SystemSettingLanguageCell
function SystemSettingSecondLanguagePopView:OnProcessItemWidget(position, itemWidget)
    log("SystemSettingSecondLanguagePopView:OnProcessItemWidget()", position, itemWidget)
    local cultureInfo = self._cultureConfig[self._cultureKeyList[position]]
    if cultureInfo then
        local cultureSign = cultureInfo.CultureSign -- 获取语言码
        local function OnWidgetClicked()
            self._secondLanguage = cultureSign
            self:RefrshView(cultureSign, self._checkBoxMap)--刷新当前选中效果
        end

        -- 替换为图片
        local displayLanguageText = string.format("<dfmrichtext type=\"img\" id=\"SetUpText_%s\"/>", cultureSign)
        if cultureSign == "zh-Hans" then
            displayLanguageText = "<dfmrichtext type=\"img\" id=\"SetUpText_zh\"/>" -- 海外主机单独支持简体中文
        end
        local downloadInfo = {
            cultureWwise = cultureSign,
            downloadKey = "",
            displayName = displayLanguageText
        }

        itemWidget:InitItem(cultureSign, downloadInfo, OnWidgetClicked, nil)
        if string.lower(cultureSign) == string.lower(self._secondLanguage) then
            itemWidget:SetSelected(true)
        else
            itemWidget:SetSelected(false)
        end
        self._checkBoxMap[cultureSign] = itemWidget
    end
end

function SystemSettingSecondLanguagePopView:RefrshView(selectedCulture)
    for culture, itemWidget in pairs(self._checkBoxMap) do
        if string.lower(culture) == string.lower(selectedCulture) then
            itemWidget:SetSelected(true)
        else
            itemWidget:SetSelected(false)
        end
    end
end

function SystemSettingSecondLanguagePopView:CloseSelf()
    -- 由于从按钮关闭UI时不播放动画，该问题等待框架层统一处理，暂时先手动播放
    self._wtCommonPopWinV2:PlayAnimation(self._wtCommonPopWinV2.Ani_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    Timer.DelayCall(0.4,function()
        Facade.UIManager:CloseUI(self)
    end)
end

function SystemSettingSecondLanguagePopView:_GetTextByCultureSign(culture)
    log("GetTextByCultureSign", culture)
    for _, cultureKey in pairs(self._cultureKeyList) do
        if self._cultureConfig[cultureKey].CultureSign == culture then
            return self._cultureConfig[cultureKey].CultureDisplayName
        end
    end
    return "Miss Culture Text"
end

---点击确定后要么切换成功要么直接关闭弹窗
function SystemSettingSecondLanguagePopView:_OnConfirmBtnClick()
    log("SystemSettingSecondLanguagePopView:_OnConfirmBtnClick()")
    if self._secondLanguage == self._preLanguage then
        self:CloseSelf()
    end
    if IsHD() then
        GameSettingLogicHD.ProcessSecondLanguage(self._secondLanguage)
    else
        ComminicateSettingLogic.ProcessSecondLanguage(self._secondLanguage)
    end
    local displayLanguageText = string.format("<dfmrichtext type=\"img\" id=\"SetUpText_%s\"/>", self._secondLanguage)
    if self._secondLanguage == "zh-Hans" then
        displayLanguageText = "<dfmrichtext type=\"img\" id=\"SetUpText_zh\"/>" -- 海外主机单独支持简体中文
    end
    Module.SystemSetting:SendSecondLanguage()
    Module.SystemSetting.Config.Event.evtSetSecondLanguage:Invoke(displayLanguageText)
    self:CloseSelf()
end

function SystemSettingSecondLanguagePopView:OnNavBack()
    return false
end

return SystemSettingSecondLanguagePopView