----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
---@class QuestSeasonMainPanel : LuaUIBaseView

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local QuestSeasonMainPanel = ui("QuestSeasonMainPanel")

function QuestSeasonMainPanel:Ctor()

    self._wtSeasonNameText = self:Wnd("DFTextBlock_156", UITextBlock)
    self._wtSeasonRemainTime = self:Wnd("DFTextBlock_52", UITextBlock)

    self._wtEntranceMain = self:Wnd("SeasonalTasks_Entrance", UIWidgetBase)
    self._wtEntranceCollection = self:Wnd("SeasonalTasks_Entrance_01", UIWidgetBase)
    self._wtEntranceFactContract = self:Wnd("SeasonalTasks_Entrance_02", UIWidgetBase)

    -- 赛季目标
    self._wtCheckBtn = self:Wnd("wtTipsBtn", DFCheckBoxOnly)
    if self._wtCheckBtn then 
        -- self._wtCheckBtn:Event("OnClicked", self._ShowTutorial, self)
        self._wtCheckBtn:SetCallback(self._ShowTutorial, self)
    end

    self._wtGoalItem = self:Wnd("WBP_CommonItemTemplate_1", IVCommonItemTemplate)
    self._wtGoalSelected = self:Wnd("DFCanvas_Selected", UIWidgetBase)
    self._wtGoalItemOvertime = self:Wnd("WBP_CommonItemTemplate_2", IVCommonItemTemplate)
    self._wtGoalSelectedOvertime = self:Wnd("DFCanvas_Selected_1", UIWidgetBase)

    
    self._wtUnlockFinal = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    self._wtConditionList = self:Wnd("DFVerticalBox_316", UIWidgetBase)
    self._finalQuestTitle = self:Wnd("DFCanvasPanel", UIWidgetBase)
    self._finalQuestCondition = self:Wnd("DFVerticalBox", UIWidgetBase)

    self._wtCommonBg = self:Wnd("DFImage_265", UIWidgetBase)
    self._wtOvertimeBg = self:Wnd("WBP_SeasonalTasks_BG_01", UIWidgetBase)

    self._wtNormalCanvas = self:Wnd("DFCanvas_Normal", UIWidgetBase)
    self._wtEmptyCanvas = self:Wnd("DFCanvas_Empty", UIWidgetBase)
    self._wtEmptySlot = self:Wnd("EmptySlot",UIWidgetBase)

    self._lineInfo = nil
    self._canTakeReward = false
    self._bIsFinalRewarded = false

    if IsHD() then
        self._wtRightPanel = self:Wnd("DFCanvasPanel_66", UIWidgetBase)
        self._wtCanvasPanel = self:Wnd("DFCanvasPanel_87", UIWidgetBase)
    end

end

function QuestSeasonMainPanel:OnShowBegin()
 
    Server.QuestServer:GetAllQuest()
    
    Server.QuestServer:GetQuestLineSeasonDataReq()

    Module.Quest:OpenSeasonalQuestTutorial(Module.Quest.Config.EQuestSeasonGuideType.Main, Server.TipsRecordServer.keys.QuestSeasonalTutorial)

    self._lineInfo = Server.QuestServer:GetCurrentSeasonLine()

    if self._lineInfo == nil then 
		logerror("Invalid Current Season Questline")
        self._wtNormalCanvas:Collapsed()
        local weak, ins = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot)
        local widget = getfromweak(weak)
        if widget then
            local text = widget:Wnd("RichTextBlock_50", UITextBlock)
            text:SetText(Module.Quest.Config.Loc.QuestSeasonEmpty)
        end
        self._wtEmptyCanvas:Visible()
		return
	end

    self._wtSeasonNameText:SetText(self._lineInfo.name)

    -- 赛季剩余时间
    local remianTime = self._lineInfo:GetSeasonTimeDelta()
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(remianTime)
    self._wtSeasonRemainTime:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonRemainTime, day))

    -- 功能入口
    self._wtEntranceMain:SetBgImg(self._lineInfo._seasonQuestGuideImg1.AssetPathName)
    self._wtEntranceCollection:SetBgImg(self._lineInfo._seasonQuestGuideImg2.AssetPathName)
    self._wtEntranceFactContract:SetBgImg(self._lineInfo._seasonQuestGuideImg3.AssetPathName)

    local seasonLevel = Server.RoleInfoServer.seasonLevel
    local openLevel = self._lineInfo.openLevel
    if seasonLevel >= openLevel then
        self:_SetIsLocked(false,openLevel)
        self._wtEntranceMain:InitInfo(self._lineInfo)
        self._wtEntranceCollection:InitInfo(true, self._lineInfo)
        self._wtEntranceFactContract:InitInfo(false, self._lineInfo)
    else
        self:_SetIsLocked(true,openLevel)
    end

    -- 赛季目标
    local finalQuest = Server.QuestServer:GetQuestInfoById(self._lineInfo._finalQuestID)
    self._bIsFinalRewarded = false
    if finalQuest then
        self._wtGoalItem:InitItem(finalQuest:GetRewardList()[1])
        if Server.QuestServer:IsQuestRewarded({self._lineInfo._finalQuestID}) then
            self._wtGoalItem:SetAlreadyGetState()
            self._bIsFinalRewarded = true
        elseif Server.QuestServer:IsQuestCompleted({self._lineInfo._finalQuestID}) then
            self._wtGoalItem:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01", 0, EUMGSequencePlayMode.Forward, 1, true)
        end
    end

    local overtimeReward = Module.Quest.Field:GetRewardInfoById(self._lineInfo._overTimeRewardID)
    if overtimeReward then
        self._wtGoalItemOvertime:InitItem(ItemBase:New(overtimeReward.id, overtimeReward.num))
        if Server.QuestServer._bIsTakeOvertimeReward then
            self._wtGoalItemOvertime:SetAlreadyGetState()   
        elseif QuestLogic:IsAllConditionCompeleted(self._lineInfo._overConditionList) and self._bIsFinalRewarded == true then
            self._wtGoalItemOvertime:PlayIVAnimation("WBP_CommonItemTemplate_in_special_01", 0, EUMGSequencePlayMode.Forward, 1, true)
            self._canTakeReward = true
        end
    else
        logerror("[QuestSeasonMainPanel] Can't Find Overtime Reward Info")
    end

    self._wtGoalItem:BindCustomOnClicked(self._OnGoalClicked, self)
    self._wtGoalItemOvertime:BindCustomOnClicked(self._OnOvertimeClicked, self)

    self._bIsShowOvertime = Module.Quest.Field:IsInSeasonOvertime() and 
                Server.QuestServer:IsQuestRewarded({self._lineInfo._finalQuestID})
    self:_UpdateShowOvertime(self._bIsShowOvertime)
    if self._bIsShowOvertime then
        self._wtCommonBg:Collapsed()
        self._wtOvertimeBg:Visible()
    else
        self._wtCommonBg:Visible()
        self._wtOvertimeBg:Collapsed()
    end

    self._wtEmptyCanvas:Collapsed()
    self._wtNormalCanvas:Visible()

    self:_AddListeners()

    if IsHD() then
        self:_EnableGamepad()
    end

end

function QuestSeasonMainPanel:OnShow()
    if IsHD() then
        -- WidgetUtil.SetUserFocusToWidget(self._wtEntranceMain,true)
    end
end

function QuestSeasonMainPanel:_AddListeners()
end

function QuestSeasonMainPanel:OnHide()
    self:RemoveAllLuaEvent()
    self._wtGoalItem:BindCustomOnClicked(nil, nil)
    self._wtGoalItemOvertime:BindCustomOnClicked(nil, nil)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
    if IsHD() then
        self:_DisableGamepad()
    end
end

function QuestSeasonMainPanel:_UpdateConditionItems(conditionList, bIsShowOvertime)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtConditionList)
    if bIsShowOvertime then
        local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonConditionItem,
        self._wtConditionList, nil, Module.Quest.Config.Loc.QuestSeasonFinalMissionConditionOvertime)
        local condWidget = getfromweak(uiIns)
        if condWidget then
            if Server.QuestServer:IsQuestRewarded({self._lineInfo._finalQuestID}) then
                condWidget:SetProgress(1,1)    
            else
                condWidget:SetProgress(0,1)
            end
            condWidget:SetHideDecoLine(false)
        end
    end
    for index, value in ipairs(conditionList) do
        if not bIsShowOvertime or index ~= Server.QuestServer.EQuestSeasonConditionType.Star then
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonConditionItem,self._wtConditionList,nil, value.desc)
            local condWidget = getfromweak(uiIns)
            if condWidget then
                condWidget:SetProgressByType(index, value.param)
                if index == #conditionList then
                    condWidget:SetHideDecoLine(true)
                else
                    condWidget:SetHideDecoLine(false)
                end
            end
        end
    end
end

function QuestSeasonMainPanel:_SetIsLocked(bisLocked, openLevel)
    self._wtEntranceMain:SetIsLocked(bisLocked, openLevel)
    self._wtEntranceCollection:SetIsLocked(bisLocked, openLevel)
    self._wtEntranceFactContract:SetIsLocked(bisLocked, openLevel)
end

function QuestSeasonMainPanel:_ShowTutorial(bIsChecked)
    Module.Quest:OpenSeasonalQuestTutorial(Module.Quest.Config.EQuestSeasonGuideType.Main, nil)
    self._wtCheckBtn:SetIsChecked(false, false)
end

function QuestSeasonMainPanel:_UpdateShowOvertime(bIsShowOvertime)
    if bIsShowOvertime then
        -- 加时赛阶段
        self._wtGoalSelected:Collapsed()
        self._wtGoalSelectedOvertime:SelfHitTestInvisible()
        self._finalQuestTitle:Collapsed()
        self._finalQuestCondition:Collapsed()
        self._wtUnlockFinal:Collapsed()
        self:_UpdateConditionItems(self._lineInfo._overConditionList, bIsShowOvertime)
    else
        self._wtGoalSelected:SelfHitTestInvisible()
        self._wtGoalSelectedOvertime:Collapsed()

        local finalQuest = Server.QuestServer:GetQuestInfoById(self._lineInfo._finalQuestID)

        if finalQuest then
            Facade.UIManager:RemoveSubUIByParent(self, self._finalQuestCondition)
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonConditionItem,
            self._finalQuestCondition, nil, string.format(Module.Quest.Config.Loc.QuestSeasonFinalMissionCondition, finalQuest.name))
            local condWidget = getfromweak(uiIns)
            if condWidget then
                if Server.QuestServer:IsQuestRewarded({self._lineInfo._finalQuestID}) then
                    condWidget:SetProgress(1,1)    
                else
                    condWidget:SetProgress(0,1)
                end
                condWidget:SetHideDecoLine(true)
            end
            self._finalQuestTitle:SelfHitTestInvisible()
            self._finalQuestCondition:SelfHitTestInvisible()
            self._wtUnlockFinal:SelfHitTestInvisible()
        end
        self:_UpdateConditionItems(self._lineInfo._unLockConditionList, bIsShowOvertime)
    end
end

function QuestSeasonMainPanel:_OnGoalClicked()
    if self._bIsShowOvertime then
        self._bIsShowOvertime = false
        self:_UpdateShowOvertime(self._bIsShowOvertime)
    else
        self._wtGoalItem:_DefaultOnClicked(false)
        if IsHD() then
            WidgetUtil.SetUserFocusToWidget(self._wtGoalItem, false)
        end
    end
end

function QuestSeasonMainPanel:_OnOvertimeClicked()

    if QuestLogic:IsAllConditionCompeleted(self._lineInfo._overConditionList) and 
    not Server.QuestServer._bIsTakeOvertimeReward and self._bIsFinalRewarded then

        local fcallback = function ()
            local overtimeReward = Module.Quest.Field:GetRewardInfoById(self._lineInfo._overTimeRewardID)
            local item = ItemBase:New(overtimeReward.id, overtimeReward.num)
            Module.Reward:OpenRewardPanel(
                Module.Quest.Config.Loc.QuestSeasonOvertimeReward,
                nil,
                {item}
            )
            self._wtGoalItemOvertime:SetAlreadyGetState()   
            self._wtGoalItemOvertime:StopIVAnimation("WBP_CommonItemTemplate_in_special_01")
        end

        Server.QuestServer:GetOvertimeReward(fcallback)
        if self._PressTakeReward then
            self:RemoveInputActionBinding(self._PressTakeReward)
            self._PressTakeReward= nil
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "QuestSeason_Tips",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
        }, false, true)
        return
    end

    if self._bIsShowOvertime then
        self._wtGoalItemOvertime:_DefaultOnClicked(false)
        if IsHD() then
            WidgetUtil.SetUserFocusToWidget(self._wtGoalItemOvertime, false)
        end
    else
        self._bIsShowOvertime = true
        self:_UpdateShowOvertime(self._bIsShowOvertime)
    end
end

-------- Gamepad ------------

function QuestSeasonMainPanel:_EnableGamepad()
    self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtCanvasPanel, self, "Hittest")
    if self._wtNavGroup1 then
        self._wtNavGroup1:AddNavWidgetToArray(self._wtEntranceMain)
        self._wtNavGroup1:AddNavWidgetToArray(self._wtEntranceCollection)
        self._wtNavGroup1:AddNavWidgetToArray(self._wtEntranceFactContract)
        self._wtNavGroup1:SetAnalogCursorStickySlowdownFactor(1.0) 
    end

    self._wtNavGroup2 = WidgetUtil.RegisterNavigationGroup(self._wtRightPanel, self, "Hittest")
    if self._wtNavGroup2 then
        self._wtNavGroup2:AddNavWidgetToArray(self._wtGoalItem)
        self._wtNavGroup2:AddNavWidgetToArray(self._wtGoalItemOvertime)
    end
    self:_InitShortcuts()
    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup1)
end

function QuestSeasonMainPanel:_DisableGamepad()
    WidgetUtil.RemoveNavigationGroup(self)
    self._wtNavGroup1 = nil
    self._wtNavGroup2 = nil
    self:_RemoveShortcuts()
end

function QuestSeasonMainPanel:_InitShortcuts()
    if self._PressTipsButton == nil then 
        self._PressTipsButton = self:AddInputActionBinding("QuestSeason_Tips", 
        EInputEvent.IE_Pressed, self._ShowTutorial, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._canTakeReward then 
        if self._PressTakeReward == nil then 
            self._PressTakeReward = self:AddInputActionBinding("QuestSeason_GetOvertimeReward", 
            EInputEvent.IE_Pressed, self._OnOvertimeClicked, self, EDisplayInputActionPriority.UI_Stack)
        end
        
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "QuestSeason_Tips",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
            {actionName = "QuestSeason_GetOvertimeReward",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
        }, false, true)
    else        
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "QuestSeason_Tips",func = nil, caller = self ,bUIOnly = true, bHideIcon = false},
        }, false, true)
    end

end

function QuestSeasonMainPanel:_RemoveShortcuts()
    if self._PressTipsButton then
        self:RemoveInputActionBinding(self._PressTipsButton)
        self._PressTipsButton= nil
    end
	if self._PressTakeReward then
        self:RemoveInputActionBinding(self._PressTakeReward)
        self._PressTakeReward= nil
    end
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

return QuestSeasonMainPanel