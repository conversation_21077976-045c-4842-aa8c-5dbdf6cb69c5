----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local GPUINavigationStrategy_Hittest = import("GPUINavigationStrategy_Hittest")
local EGPInputType = import("EGPInputType")
local UGPUINavigationUtils = import("GPUINavigationUtils")

local UDFNavigationSelectorBase = import("DFNavigationSelectorBase")
local UGPInputDelegates = import "GPInputDelegates"

-- END MODIFICATION

local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local ItemHelperTool     = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CurrencyHelperTool    = require "DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
local WeaponAssemblyTool  = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local WarehouseContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD"
local QuickOperationLogic         = require "DFM.Business.Module.ArmedForceModule.Logic.QuickOperation.QuickOperationLogic"
local ItemConfigTool             = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local ArmedForceField = Module.ArmedForce.Field
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EMouseCursor = import("EMouseCursor")


---@class ******************************** : LuaUIBaseView
local ******************************** = ui("********************************")
function ********************************:Ctor()
    self._bCanOperate = false
    self._fOnConfirm = nil
	self._wtBtnConfirm = self:Wnd("WBP_CommonButtonV1S1", DFCommonButtonOnly)
    self._wtBtnConfirm:Event("OnClicked", self._OnBtnConfirmClicked, self)
    self._wtBtnConfirm:Event("OnDeClicked", self._OnBtnConfirmClicked, self)

    self._wtExchangeBtn = self:Wnd("WBP_DFCommonButtonV1S2", DFCommonButtonOnly)
    self._wtExchangeBtn:Event("OnClicked", self._OnExchangeClicked, self)
    self:_CanceProcessing()
    self:_InitContainerSlotViews()
    self:_DisabelHoverTipsFocus()

	self._wtMedicineScrollBox = UIUtil.WndWaterfallScrollBox(self, "wtWaterFallList", self._OnGetItemCount, self._OnProcessItemWidget)
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)

    self._waitInputSummaryList = nil
end


--==================================================
--region Life function
function ********************************:OnOpen()
end

function ********************************:OnClose()
    Server.InventoryServer:ClearSlotGroup(ESlotGroup.Preset)
end

function ********************************:OnActivate()
end

function ********************************:OnDeactivate()
    Server.InventoryServer:ClearSlotGroup(ESlotGroup.Preset)
end


local EHallMainDisplayType = import "EHallMainDisplayType"
function ********************************:OnShowBegin()
    Module.CommonBar:BindBackHandler(self.CloseMainPanel, self)
    Module.Inventory:SetInvSlotViewType(Module.Inventory.Config.EInvSlotViewType.Assembly_QuickOperation)
    self:_ResetContainerSlotViewsContainerSlot()
    self:_AddListeners()
    ArmedForceField:SetShowQuickOperationTipsed(false)
    LogAnalysisTool.SetPresetModuleUseValueByStr("TakeMedicine", 1)
    Module.Inventory:RegisterCommonClickBehavior()
    Module.ArmedForce:ClearQuickOperationDataInfo()
    Module.ArmedForce:GenQuickOperationDataInfo(self._myCurSubViewType)
    self:_InitView()
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMain,"SetDisplayType",EHallMainDisplayType.EquipMedicine)
    
    Module.ArmedForce.Config.evtArmedForceQuickOperationOpen:Invoke(self._myCurSubViewType)

    -- BEGIN MODIFICATION @ VIRTUOS : 注册导航数据、初始化手柄快捷键
    if IsHD() then
        self:_EnableNavigation()
        Module.CommonWidget:BindCommonIVInputLogic()
        -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
        -- local InvSlotViewType = Module.Inventory:GetInvSlotViewType()
        Module.CommonWidget:SetGamepadSwapItem(false)
        self:_EnableInputTypeChangedHandle(true)

        -- 开启手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(true)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(true)
        Module.CommonWidget:EnableGamepadItemShortcutKey(true)
    end
    -- END MODIFICATION
    self._curShowTipsIndex = 0
end


function ********************************:OnShow()
    Server.ShopServer:SetLimitClicker(true)
end

function ********************************:OnHide()
    Server.ShopServer:SetLimitClicker(false)
end

function ********************************:OnHideBegin()
    Module.Inventory:SetInvSlotViewType(Module.Inventory.Config.EInvSlotViewType.Default)
    self:RemoveAllLuaEvent()
    self:ReleaseTimer()

    self:_SetAllSlotViewOnHide()

    Module.Inventory:UnregisterCommonClickBehavior()

    -- BEGIN MODIFICATION @ VIRTUOS : 移除导航数据、手柄快捷键
    if IsHD() then
        self:_DisableNavigation()
        Module.CommonWidget:UnbindCommonIVInputLogic()
        -- 页面关闭时清除物品交换的记录 （每一个需要物品交换功能的主页面都需要）
        Module.CommonWidget:SetGamepadSwapItem(false)
        Module.CommonWidget:StopFocusItemView()
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self:_EnableInputTypeChangedHandle(false)
        
        -- 关闭手柄移动物品功能
        Module.CommonWidget:EnableGamepadSwapItem(false)
        Module.CommonWidget:EnableGamepadCarryItemFromPop(false)
        Module.CommonWidget:EnableGamepadItemShortcutKey(false)

    end
    -- END MODIFICATION
    self:CloseHoverTips()
end

function ********************************:OnInitExtraData(subViewType, fOnConfirm)
    self:_CanceProcessing()
    self._bCanOperate = false
    self._myCurSubViewType = subViewType
	self._fOnConfirm = fOnConfirm
    -- 只要发生过各种操作 就显示
    self._bShowBtn = false
    if self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Bullet then
        LogAnalysisTool.SignButtonClicked(10050004)
        Module.CommonBar:RegStackUITopBarTitle(self, Module.ArmedForce.Config.Loc.ConfigureBulletTitle)
    elseif self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Medicine then
        LogAnalysisTool.SignButtonClicked(10050010)
        Module.CommonBar:RegStackUITopBarTitle(self, Module.ArmedForce.Config.Loc.ConfigureMedicineTitle)
    end
end

--endregion
--==================================================


--==================================================
--region Public API

--endregion
--==================================================


--==================================================
--region Private API
function ********************************:_InitContainerSlotViews()
    local fPostItemRefresh1 = function (itemView)
        local function fOnItemViewClickedRCallback()
            self:_OnLeftDecClicked(itemView)
        end
        self:PostItemRefresh2(itemView, fOnItemViewClickedRCallback)
    end
    local fPostEquipItemRefresh = function (itemView)
        if itemView.item then
            itemView:SetCustomInteractivity(true)
            itemView:EnableComponent(EComp.BottomLeftIconText, false)
            itemView:EnableComponent(EComp.BottomRightIconText, false)
            itemView:EnableComponent(EComp.TopRightIconText, false)
            itemView:SetHandleDrag(false)
            itemView:SetHandleDoubleClick(false)
            itemView:SetCppValue("bHandleClick", false)
            itemView:SetCppValue("bHandleClickR", false)
            local maskComponent = itemView:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
            if maskComponent then
                itemView:EnableComponent(EComp.GreyMask, true)
                itemView:SetCursor(EMouseCursor.Default)
            end
        end
    end
    logerror("【********************************】管理warehouse对象池 InitContainerSlotViews")
    local fObtainCustomWarehouseIVFromPool = function ()
        return Module.ArmedForce.Field:ObtainWarehouseIV(Module.ArmedForce.Config.EAssemblyWarehouseIVPoolType.QuickOperation)
    end

    local fFreeWarehouseIVToPool = function (warehouseIV)
        Module.ArmedForce.Field:FreeWarehouseIV(Module.ArmedForce.Config.EAssemblyWarehouseIVPoolType.QuickOperation, warehouseIV)
    end

    self._wtAssemblyBackpack = self:Wnd("WBP_AssemblyBackpack_PC", UIWidgetBase)
    self._wtMainScrollBox = self._wtAssemblyBackpack:Wnd("wtMainScrollBox", UIScrollBox)
    -- 胸挂
    self._wtCHContainerView = self._wtAssemblyBackpack:Wnd("wtCHContainerView", WarehouseContainerPanel_HD)
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, ESlotGroup.Preset)
    self._wtCHContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtChestHangingCapacity = self._wtCHContainerView:GetCapacityText()
    self._wtChestHangingSlotView = self._wtCHContainerView:GetContainerSlotView()
    self._wtChestHangingSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtChestHangingSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 口袋
    self._wtPocketContainerView = self._wtAssemblyBackpack:Wnd("wtPocketContainerView", WarehouseContainerPanel_HD)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, ESlotGroup.Preset)
    self._wtPocketCapacity = self._wtPocketContainerView:GetCapacityText()
    self._wtPocketSlotView = self._wtPocketContainerView:GetContainerSlotView()
    self._wtPocketSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtPocketSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 背包
    self._wtBagContainerView = self._wtAssemblyBackpack:Wnd("wtBagContainerView", WarehouseContainerPanel_HD)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, ESlotGroup.Preset)
    self._wtBagContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtBagCapacity = self._wtBagContainerView:GetCapacityText()
    self._wtBagSlotView = self._wtBagContainerView:GetContainerSlotView()
    self._wtBagSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtBagSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)

    -- 安全箱
    self._wtSafeBoxContainerView = self._wtAssemblyBackpack:Wnd("wtSafeBoxContainerView", WarehouseContainerPanel_HD)
    self._wtSafeBoxContainerView:InitContainerSlot(ESlotType.SafeBoxContainer, ESlotGroup.Preset)
    self._wtSafeBoxContainerView:GetEquipSlotView():BindPostRefreshFunc(fPostEquipItemRefresh)
    self._wtSafeBoxCapacity = self._wtSafeBoxContainerView:GetCapacityText()
    self._wtSafeBoxSlotView = self._wtSafeBoxContainerView:GetContainerSlotView()
    self._wtSafeBoxSlotView:SetSlotMaxPreviewSize(5, 0)
    self._wtSafeBoxSlotView:BindItemViewPostRefreshFunc(fPostItemRefresh1)
    self._wtSafeBoxSlotView:SetCustomWarehouseIVFromPool(true, fObtainCustomWarehouseIVFromPool, fFreeWarehouseIVToPool)
end

function ********************************:_ResetContainerSlotViewsContainerSlot()
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer, ESlotGroup.Preset)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket, ESlotGroup.Preset)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer, ESlotGroup.Preset)
    self._wtSafeBoxContainerView:InitContainerSlot(ESlotType.SafeBoxContainer, ESlotGroup.Preset)
end

-- UI监听事件、协议
function ********************************:_AddListeners()
    self:AddLuaEvent(Server.InventoryServer.Events.evtLocalMoveResult, self._OnLocalMoveResult, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtRemoveWeaponBullet, self._OnRemoveBulletForWeapon, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtFinishQuickOperationData, self._OnFinishQuickOperationData, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtOnOperationItemAddClicked, self._OnOperationItemAddClicked, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtOnOperationItemViewDecreaseClicked, self._OnOperationItemViewDecreaseClicked, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtOnOperationItemViewUpDatePropInfo, self._OnOnOperationItemViewUpDatePropInfo, self)
    self:AddLuaEvent(Server.AuctionServer.Events.evtOnCSAuctionGetSaleListBatchRes, self._RefreshConfirmBtn, self)
    self:AddLuaEvent(Module.ArmedForce.Config.evtInputSummaryListChanged, self._InternalSetInputSummary, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtBottomBarSetting, self._BottomBarSummaryListChangedByItemView, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDrop, self._OnGlobalItemDrop, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragCancelled, self._OnGlobalItemDragCancelled, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtShouldUseItemOnDraggingSummary, self._OnGlobalSwapItem, self)
    
end

-- 初始化界面
function ********************************:_InitView()
    self._wtBtnConfirm:Collapsed()
    self._wtExchangeBtn:Collapsed()

    self:InitContainerItems()
    self:_RefreshContainerItems()
    -- 刷新右边
    self._wtMedicineScrollBox:RefreshAllItems()
    -- 刷新容量
    self:_RefreshCapacity()
end

-- 设置slotview的itemviewonhide（为了隐藏悬浮tips）
function ********************************:_SetAllSlotViewOnHide()
    local function fSetItemViewOnHide(slotType)
        local slotView = self:_GetSlotView(slotType)
        if slotView then
            local allItemView = slotView:GetAllItemViews()
            for index, itemView in pairs(allItemView) do
                if itemView.OnHide then
                    itemView:OnHide()
                end
            end
        end
    end
    fSetItemViewOnHide(ESlotType.ChestHangingContainer)
    fSetItemViewOnHide(ESlotType.Pocket)
    fSetItemViewOnHide(ESlotType.BagContainer)
    fSetItemViewOnHide(ESlotType.SafeBoxContainer)
end

-- 根据slotType获取slotView
function ********************************:_GetSlotView(slotType)
    local slotView
    if slotType == ESlotType.BagContainer then
        slotView = self._wtBagSlotView
    elseif slotType == ESlotType.ChestHangingContainer then
        slotView = self._wtChestHangingSlotView
    elseif slotType == ESlotType.Pocket then
        slotView = self._wtPocketSlotView
    elseif slotType == ESlotType.SafeBoxContainer then
        slotView = self._wtSafeBoxSlotView
    end
    -- todo 多个容器
    return slotView
end

-- 根据slotType获取ContainerView
function ********************************:_GetContainerView(slotType)
    local containerView
    if slotType == ESlotType.BagContainer then
        containerView = self._wtBagContainerView
    elseif slotType == ESlotType.ChestHangingContainer then
        containerView = self._wtCHContainerView
    elseif slotType == ESlotType.Pocket then
        containerView = self._wtPocketContainerView
    elseif slotType == ESlotType.SafeBoxContainer then
        containerView = self._wtSafeBoxContainerView
    end
    return containerView
end

function ********************************:SetTipsLogic(itemView, bCurOperationItem, fOnItemViewClickedRCallback)
    local function ShowCommonHoverTipsHD(bHovered)
        if bHovered then
            if itemView.item then
                if bCurOperationItem and itemView.item:IsMedicine() then
                    local contents = {}
                    local healthFeature = itemView.item:GetFeature(EFeatureType.Health)
                    local drugEffectStr = nil
                    local curDurability, maxDurability = healthFeature:GetDurabilityValue()
                    if healthFeature:IsArmorMedicine() then
                        drugEffectStr = string.format(Module.ArmedForce.Config.Loc.DrugEffectStr[EDispensingMedicineType.Armor], curDurability, maxDurability)
                    elseif healthFeature:IsDragMedicine() then
                        drugEffectStr = string.format(Module.ArmedForce.Config.Loc.DrugEffectStr[EDispensingMedicineType.HP], curDurability, maxDurability)
                    elseif healthFeature:IsBUFFMedicine() then
                        drugEffectStr = string.format(Module.ArmedForce.Config.Loc.DrugEffectStr[EDispensingMedicineType.BUFF], curDurability, maxDurability)
                    end

                    -- 标题
                    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = itemView.item.name, styleRowId = "C001"}})
                    -- 占格数
                    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_DrugOccupancy, data = {textContent = string.format(Module.ArmedForce.Config.Loc.DrugOccupancyStr, itemView.item.width, itemView.item.length), item = itemView.item}})
                    if drugEffectStr then
                        -- 药品效果
                        table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = drugEffectStr, styleRowId = "C002"}})
                    end
                    -- 药品描述
                    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = itemView.item.description, styleRowId = "C002"}})
                    if not WidgetUtil.IsGamepad() then
                        table.insert(contents,{id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = itemView.item.bNeedBuy and "AssemblyQuickOperation_Remove" or "AssemblyQuickOperation_RemoveToInventory"}}}})
                    end
                    Module.ArmedForce:ShowAssembledTips(contents, itemView)
                elseif bCurOperationItem and itemView.item:IsBullet() then
                    local weapon = Module.ArmedForce:GetPlayerMatchWeaponByBulletId(itemView.item.id)
                    local contents = QuickOperationLogic.GetBulletHoverTipsContents(weapon, itemView.item)
                    if not table.isempty(contents) then
                        local tipsContents = {}
                        -- 标题
                        table.insert(tipsContents, {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = itemView.item.name, styleRowId = "C001"}})
                        -- 子弹详情
                        for index, str in ipairs(contents) do
                            table.insert(tipsContents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = str, styleRowId = "C002"}})
                        end
                        if not WidgetUtil.IsGamepad() then
                            table.insert(tipsContents,{id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = itemView.item.bNeedBuy and "AssemblyQuickOperation_Remove" or "AssemblyQuickOperation_RemoveToInventory"}}}})
                        end
                        Module.ArmedForce:ShowAssembledTips(tipsContents, itemView)
                    end
                else
                    if not WidgetUtil.IsGamepad() then
                        Module.ArmedForce:ShowAssembledTips({{
                            id = UIName2ID.Assembled_CommonHDKeyTips_V1,
                            data = {actionName = "AssemblyQuickOperation_RemoveToInventory"}
                        }}, itemView)
                    end
                end
            end         
        else
            Module.ArmedForce:RemoveAssembledTips(itemView)
        end
    end

    -- 重载OnHide
    itemView.OnHide = function(itemView)
        Module.ArmedForce:RemoveAssembledTips(itemView)
        if itemView:Super().OnHide then
            itemView:Super().OnHide(itemView)
        end
    end
    -- 重载OnShow
    itemView.OnShow = function(itemView)
        if itemView:Super().OnShow then
            itemView:Super().OnShow(itemView)
        end
        local mousePos = UWidgetLayoutLibrary.GetMousePositionOnPlatform()
        local bInside = UIUtil.CheckAbsolutePointInsideWidget(itemView, mousePos)
        if bInside then
            ShowCommonHoverTipsHD(true)
        else
            ShowCommonHoverTipsHD(false)
        end
    end
    
    -- -- 重载OnShow
    -- itemView._OnHovered = function (itemView)
    --     -- local healthFeature = itemView.item:GetFeature(EFeatureType.Health)
    --     -- local bulletFeature = itemView.item:GetFeature(EFeatureType.Bullet)
    --     -- if (healthFeature and self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Medicine) or (bulletFeature and self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Bullet) then
    --         if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
    --             return
    --         end
    --         ShowCommonHoverTipsHD(true)
    --     -- end
    -- end
    -- itemView._OnUnhovered = function (itemView)
    --     if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
    --         return
    --     end
    --     ShowCommonHoverTipsHD(false)
    -- end

    itemView.ShowTip = function(itemView)
        if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
            return
        end
        ShowCommonHoverTipsHD(true)
    end
    
    itemView.HideTip = function(itemView)
        if itemView:IsInHideBeginState() or itemView:IsInHideState() or itemView:IsInShowBeginState() then
            return
        end
        ShowCommonHoverTipsHD(false)
    end


    local function fOnClickCallback()
        CommonWidgetConfig.Events.evtItemSingleSelected:Invoke(itemView)
    end
    -- 需要拖拽
    itemView:SetCppValue("bHandleClick", true)
    itemView:BindCustomOnClicked(fOnClickCallback)
    itemView:SetCppValue("bHandleHover", true)
    -- itemView:Event("OnHovered", itemView._OnHovered, itemView)
    -- itemView:Event("OnUnhovered", itemView._OnUnhovered, itemView)
    itemView:SetCppValue("bHandleClickR", true)
    -- 重载OnShow
    itemView.OnClickedR = function (itemView, InGeometry, InMouseEvent)
        --BEGIN MODIFICATION @ VIRTUOS : 不在手柄交换物品时，用手柄模拟鼠标右键按下事件。
        if WidgetUtil.IsGamepad() then
            return
        end
        --END MODIFICATION
        fOnItemViewClickedRCallback()
        ShowCommonHoverTipsHD(false)
    end
end

function ********************************:PostItemRefresh2(itemView, fOnItemViewClickedRCallback)
    if itemView.item then
        -- local component = itemView:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTwoLineTitleComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
        -- itemView:EnableComponent(EComp.TopLeftTwoLineText, false) -- 隐藏所有名字

        itemView:SetCustomInteractivity(true)
        itemView:SetCppValue("bHandleClickR", false)
        local healthFeature = itemView.item:GetFeature(EFeatureType.Health)
        local bulletFeature = itemView.item:GetFeature(EFeatureType.Bullet)

        local bHealthOperationItem = healthFeature and self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Medicine
        local bBulletOperationItem = bulletFeature and self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Bullet

        local bCurOperationItem = bHealthOperationItem or bBulletOperationItem

        -- 设置tips逻辑
        self:SetTipsLogic(itemView, bCurOperationItem, fOnItemViewClickedRCallback)

        -- 可拖动
        itemView:SetHandleDrag(true)
        -- 不可双击
        itemView:SetHandleDoubleClick(false)
        if bCurOperationItem then
            itemView:EnableComponent(EComp.GreyMask, false)
            itemView:SetCursor(EMouseCursor.Hand)
            if itemView.item.bNeedBuy then
                local component = itemView:FindOrAdd(EComp.NeedBuyIcon, UIName2ID.IVShoppingCartComponent, EIVSlotPos.MaskLayer, EIVCompOrder.MaskLayerOrder)
                itemView:EnableComponent(EComp.NeedBuyIcon, true)
            else
                itemView:EnableComponent(EComp.NeedBuyIcon, false)
            end
        else
            logerror("热更EComp.NeedBuyIcon false")
            itemView:EnableComponent(EComp.NeedBuyIcon, false)
            local maskComponent = itemView:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
            if maskComponent then
                itemView:EnableComponent(EComp.GreyMask, true)
                itemView:SetCursor(EMouseCursor.Default)
            end

            local component = itemView:GetComponent(EComp.TopRightIconText)
            if component and component.UINavID == UIName2ID.IVDecBtnComponent then
                itemView:EnableComponent(EComp.TopRightIconText, false)
            end
        end
    end
end

function ********************************:_OnExchangeClicked()
    local itemDataList, itemIdList = self:_GetExchangeItemDataList()
    if not table.isempty(itemIdList) then
        local bHasExchangeChannel = Module.Shop:CheckBatchExchangeChannel(itemIdList)
        if bHasExchangeChannel then
            local function fCallback(bIsFullySucceed)
                local redistributionIds = {}
                for _, itemData in ipairs(itemDataList) do
                    redistributionIds[itemData.id] = true
                end
                logwarning("配装配置物资兑换结束", bIsFullySucceed)
                QuickOperationLogic.RedistributionQuickOperationData(redistributionIds)
                self:RefreshAllQuickOperationDataNeedBuyItemView()
                self:_RefreshAllBtn()
            end
            Module.Shop:ShowShopPopWindow(itemDataList, fCallback)
        else
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.NoExchangeChannel)
        end
    else
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.NoExchangeChannel)
    end
end

function ********************************:_GetExchangeItemDataList()
    local itemDataList = {}
    local itemIdList = {}
    local allQuickOperationDataInfo = Module.ArmedForce.Field:GetAllQuickOperationDataInfo()
    for id, quickOperationDataInfo in pairs(allQuickOperationDataInfo) do
        local buyNum = quickOperationDataInfo:GetBuyNum()
        local allBuyNum = 0
        if quickOperationDataInfo.itemMainType == EItemType.Bullet then
            local weaponBulletBuyNum = quickOperationDataInfo:GetWeaponBulletBuyNum()
            allBuyNum = buyNum + weaponBulletBuyNum
        elseif quickOperationDataInfo.itemMainType == EItemType.Medicine then
            allBuyNum = buyNum
        end
        if allBuyNum > 0 then
            local itemData = {
                id = id,
                num = allBuyNum,
                targetSlotType = ESlotType.None
            } 
            table.insert(itemDataList, itemData)
            table.insert(itemIdList, id)
        end
    end
    return itemDataList, itemIdList
end

function ********************************:_OnBtnConfirmClicked()
    if self._bProcessing then
        Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.ClickTooSoonPleaseWait)
        return
    end

	if self._fOnConfirm then
        self:ReleaseTimer()
        self._bProcessing = true
        self._wtBtnConfirm:SetIsEnabledStyle(false)
        self._processingTimerHandle = Timer.DelayCall(15, function ()
            if self._bProcessing then
                logerror("********************************:OnBtnProcessClicked 耗时15秒还未处理完配子弹或者配药")
                self._bProcessing = false
                self._wtBtnConfirm:SetIsEnabledStyle(true)
            end
        end, self)
        local bBuyLimited = false
        local curPriceValues = QuickOperationLogic.GetQuickOperationDataCost()
        local currencyStringTable = {}
        if curPriceValues and next(curPriceValues) then
            for currencyType, currencyValue in pairs(curPriceValues) do
                local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyType)
                if currencyValue > 0 then
                    if currencyNum < currencyValue then
                        bBuyLimited = true
                    end
                end
            end
            if bBuyLimited then
                self:_CanceProcessing()
                Module.CommonTips:ShowSimpleTip(ServerTipCode.MaintainanceFail)
                return
            else
                self._fOnConfirm()
            end
        else
            self._fOnConfirm()
        end
	end
end

function ********************************:ReleaseTimer()
    if self._processingTimerHandle then
        Timer.CancelDelay(self._processingTimerHandle)
        self._processingTimerHandle = nil
    end
end


function ********************************:_RefreshCapacity(inSlotType)
    local function fRefreshCapacity(slotType)
        local containerView = self:_GetContainerView(slotType)
        if containerView then
            containerView:RefreshCapacity()
        end
    end

    if inSlotType then
        fRefreshCapacity(inSlotType)
    else
        -- 设置胸挂
        fRefreshCapacity(ESlotType.ChestHangingContainer)
        -- 设置口袋
        fRefreshCapacity(ESlotType.Pocket)
        -- 设置背包
        fRefreshCapacity(ESlotType.BagContainer)
        -- 设置安全箱
        fRefreshCapacity(ESlotType.SafeBoxContainer)
    end

end

function ********************************:_RefreshExchangeBtn()
    local itemDataList, itemIdList = self:_GetExchangeItemDataList()
    if not table.isempty(itemIdList) then
        local bHasExchangeChannel = Module.Shop:CheckBatchExchangeChannel(itemIdList)
        self:_CheckExchangeChannelCallback(bHasExchangeChannel)
    else
        self._wtExchangeBtn:Collapsed()
    end
end

function ********************************:_CheckExchangeChannelCallback(bCanExchange)
    if bCanExchange then
        self._wtExchangeBtn:SelfHitTestInvisible()
    else
        self._wtExchangeBtn:Collapsed()
    end
end

function ********************************:_RefreshConfirmBtn()
    if self._bShowBtn then
        self._wtBtnConfirm:SelfHitTestInvisible()
        local curPriceValues = QuickOperationLogic.GetQuickOperationDataCost()
        local textContent = ""
        local bBuyLimited = false
        if curPriceValues and next(curPriceValues) then
            for currencyType, currencyValue in pairs(curPriceValues) do
                local currencyNum = Server.InventoryServer:GetPlayerCurrencyNum(currencyType) 
                if currencyValue > 0 then
                    if currencyNum < currencyValue then
                        bBuyLimited = true
                    end
                    if textContent ~= "" then
                        textContent = textContent..","
                    end
                    local priceIconText = ECurrencyClientType2RichIconTxtV2[currencyType]
                    local priceValueText = CurrencyHelperTool.GetCurrencyNumFormatStr(currencyValue, CurrencyHelperTool.EKMThousandsType.None)
                    local priceText = ""
                    if bBuyLimited then
                        priceText = priceIconText .. string.format(Module.ArmedForce.Config.Loc.RedColorText, priceValueText)
                    else
                        priceText = priceIconText .. priceValueText
                    end

                    if textContent then
                        textContent = textContent..priceText
                    else
                        textContent = priceText
                    end
                end
            end
        else
            textContent = Module.ArmedForce.Config.Loc.Confirm
        end
        self._wtBtnConfirm:SetMainTitle(textContent)
        -- BEGIN MODIFICATION @ VIRTUOS : 
        self:_InitShortcuts()
        -- END MODIFICATION
    else
        self._wtBtnConfirm:Collapsed()
    end
end

function ********************************:_RefreshAllBtn()
    self:_RefreshExchangeBtn() -- 兑换按钮刷新
    self:_RefreshConfirmBtn()
end

function ********************************:_OnLocalMoveResult(moveCmd)
    self._bShowBtn = true
    -- todo 根据操作gid，重新分配购买和移动数量
    self:_ReDistribute(moveCmd)
    self:_RefreshCapacity()
    self:_RefreshAllBtn()
end

function ********************************:_ReDistribute(moveCmd)
    local itemId = moveCmd.item.id
    -- 先检查是否走道具堆叠的逻辑
    if #moveCmd.targetItems == 1 then
        local targetItem = moveCmd.targetItems[1]
        if moveCmd.item.bindType == targetItem.bindType and moveCmd.item.id == targetItem.id then
            local moveItemGid = moveCmd.item.gid
            local targetItemGid = targetItem.gid
            local quickOperationDataInfo = ArmedForceField:GetQuickOperationDataInfo(itemId)
            if quickOperationDataInfo then
                local quickOperationItemData_move = quickOperationDataInfo:GetItemDataByGid(moveItemGid)
                local quickOperationItemData_target = quickOperationDataInfo:GetItemDataByGid(targetItemGid)
                if quickOperationItemData_move and quickOperationItemData_target then
                    -- 如果本地移动，移动前后道具发生堆叠数量不对时才需要重新分配
                    if (moveCmd.item.num ~= quickOperationItemData_move.buyNum + quickOperationItemData_move.moveNum) or (targetItem.num ~= quickOperationItemData_target.buyNum + quickOperationItemData_target.moveNum) then
                        local tatalBuyNum = quickOperationItemData_move.buyNum + quickOperationItemData_target.buyNum
                        local tatalMoveNum = quickOperationItemData_move.moveNum + quickOperationItemData_target.moveNum
                        assert(tatalBuyNum + tatalMoveNum == moveCmd.item.num + targetItem.num, string.format("本地挪动移动、购买总数量不一致，==> [quickOperationItemData_1 buyNum = %s, moveNum = %s], [quickOperationItemData_2 buyNum = %s, moveNum = %s], [moveCmd.item.num = %s, targetItem.num = %s]", 
                            quickOperationItemData_move.buyNum, quickOperationItemData_move.moveNum,
                            quickOperationItemData_target.buyNum, quickOperationItemData_target.moveNum,
                            moveCmd.item.num, targetItem.num))

                        -- 疑惑，需要将quickOperationDataInfo的数据都先取下来吗？
                        -- 先不取下，默认本地拖拽移动不会改变总数量

                        -- move剩下的数量与购买总数比较
                        if moveCmd.item.num > tatalBuyNum then
                            local moveNum = moveCmd.item.num - tatalBuyNum
                            quickOperationDataInfo:SetItemData(moveCmd.item, moveNum, tatalBuyNum)
                            quickOperationDataInfo:SetItemData(targetItem, targetItem.num, 0)
                        else
                            quickOperationDataInfo:SetItemData(moveCmd.item, 0, moveCmd.item.num)
                            local buyNum = tatalBuyNum - moveCmd.item.num
                            local moveNum = targetItem.num - buyNum
                            quickOperationDataInfo:SetItemData(targetItem, moveNum, buyNum)
                        end
                        self:_RefreshNeedBuyItemView(itemId)
                    end
                else
                    logerror(string.format("********************************:_ReDistribute 根据gid找不到数据 id = %s, moveItemGid = %s, targetItemGid = %s, itemNum = %s, targetItemNum = %s, quickOperationItemData_move = %s, quickOperationItemData_target = %s", itemId, moveItemGid, targetItemGid, moveCmd.item.num, targetItem.num, quickOperationItemData_move or "nil", quickOperationItemData_target or "nil"))
                    -- 查找问题遍历全部quickOperationDataInfo数据
                    local allItemDatas = quickOperationDataInfo:GetAllItemDatas()
                    for gid, data in pairs(allItemDatas) do
                        logerror(string.format("********************************:_ReDistribute 遍历==》 key=>gid = %s, gid = %s, id = %s, buyNum = %s, moveNum = %s", gid, data.gid, data.id, data.buyNum, data.moveNum))
                    end
                end
            end

        end
    end
end

function ********************************:_OnGetItemCount()
    local count = 0
    if self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Medicine then
        count = (table.nums(Module.ArmedForce.Config.EAssemblyDispensingMedicineType) or 0)
    elseif self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Bullet then
        local weaponDatas = self:_GetWeaponDatas()
        count = #weaponDatas
    end
    return count
end

function ********************************:_OnProcessItemWidget(position, itemWidget)
    if self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Medicine then
        local dispensingMedicineType = Module.ArmedForce.Config.EAssemblyDispensingMedicineType[position]
        if not dispensingMedicineType then
            return
        end
        itemWidget:SetData(dispensingMedicineType, self._myCurSubViewType)
    elseif self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Bullet then
        local weaponDatas = self:_GetWeaponDatas()
        local weapon = weaponDatas[position]
        if weapon then
            itemWidget:SetData(weapon.InSlot.SlotType, self._myCurSubViewType)
        end
    end
end

-- 重构点击事件
-- 点击右边itemview添加
function ********************************:_OnOperationItemAddClicked(type, operateItemView, specifyNum)
	Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
    local operateItem = operateItemView.item
    local itemId = operateItem.id
    if not operateItem or not self._bCanOperate then
        return
    end
    
    local result = self:_IncreaseItemViewProcess(type, itemId, specifyNum)
    if result then
        self._bShowBtn = true
        self:_RefreshNeedBuyItemView(itemId)
        self:_RefreshCapacity()
        self:_RefreshAllBtn()
        Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(itemId)
    end
end

function ********************************:OnOperationItemAdd_DepositoryNum(type, itemId, specifyNum)
    local quickOperationDataInfo = ArmedForceField:GetQuickOperationDataInfo(itemId)
    if not quickOperationDataInfo then
        logerror("********************************:OnOperationItemAdd_DepositoryNum  'quickOperationDataInfo' is nil", itemId)
        return
    end

    if not self._bCanOperate then
        return
    end
    local remainDepositoryNum = quickOperationDataInfo:GetDepositoryNum()
    local curSpecifyNum = math.min(remainDepositoryNum, specifyNum)
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
    
    local result = self:_IncreaseItemViewProcess(type, itemId, curSpecifyNum)
    if result then
        self._bShowBtn = true
        self:_RefreshNeedBuyItemView(itemId)
        self:_RefreshCapacity()
        self:_RefreshAllBtn()
        Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(itemId)
    end
end

-- 重构点击事件
-- 点击左边itemview移除
function ********************************:_OnLeftDecClicked(itemView)
	Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
    local operateItem = itemView.item
    local itemId = operateItem.id
    if not operateItem or not self._bCanOperate then
        return
    end
    -- 这里的写法是直接参照仓库快速转移写的
    InventoryNavManager.bIsQuickCarry = true
    InventoryNavManager.nextTargetItem = Module.Inventory:GetNavNextTarget(operateItem)
    --
    local decreaseNum = operateItem.num
    local result = self:_DecreaseItemProcess(operateItem, decreaseNum)
    if result then
        self._bShowBtn = true
        self:_RefreshNeedBuyItemView(itemId)
        self:_RefreshCapacity()
        self:_RefreshAllBtn()
        Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(itemId)
    end

    if result and operateItem and InventoryNavManager.bIsQuickCarry then
        InventoryNavManager.FocusByItem(InventoryNavManager.nextTargetItem)
        InventoryNavManager.bIsQuickCarry = false
        InventoryNavManager.nextTargetItem = nil
    end
end

function ********************************:_OnOperationItemViewDecreaseClicked(operateItemView)
    local operateItem = operateItemView.item
    if not operateItem or not self._bCanOperate then
        return
    end

    local itemId = operateItemView.item.id
	-- 商品
	local goods = Module.ArmedForce.Field:GetMapArmedForceGoods(itemId)
	local quickOperationDataInfo = Module.ArmedForce:GetQuickOperationDataInfo(itemId)
	-- 操作数量
	local operateNum = quickOperationDataInfo:GetOperationNum()
	if operateNum > 0 then
        local allItemDatasMap = quickOperationDataInfo:GetAllItemDatas()
        local allItemDatasList = table.tolist(allItemDatasMap)
        table.sort(allItemDatasList, function (a, b)
            -- 如果a和b的gid都大于1e18，buyNum大的排在前面
            if a.gid > 1e18 and b.gid > 1e18 then
                return a.buyNum > b.buyNum
            end

            -- 如果a的gid大于1e18，放在后面
            if a.gid > 1e18 then
                return false
            end

            -- 如果b的gid大于1e18，放在前面
            if b.gid > 1e18 then
                return true
            end

            -- 否则，gid大的排在前面
            return a.gid > b.gid
        end)
        local decreaseNum = quickOperationDataInfo:GetAddNum()
        for _, itemData in ipairs(allItemDatasList) do
            if decreaseNum > 0 then
                local item = Server.InventoryServer:GetItemByGid(itemData.gid, ESlotGroup.Preset)
                if item and item.id == itemId then -- 排除掉武器上的子弹
                    local operationDecreaseNum = math.min(decreaseNum, item.num)
                    decreaseNum = decreaseNum - operationDecreaseNum
                    local result = self:_DecreaseItemProcess(item, operationDecreaseNum)
                    if result then
                        self._bShowBtn = true
                    end
                end
            end
        end

        self:_RefreshNeedBuyItemView(itemId)
        self:_RefreshCapacity()
        self:_RefreshAllBtn()
        Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(itemId)
	else
        local tips = ""
        local unlockInfos = goods:GetUnlockInfos()
        local unlockShop = unlockInfos.shopInfo == nil and true or unlockInfos.shopInfo.bUnlock
        local unlockAuction = unlockInfos.auctionInfo.bUnlock
        local depositoryTotalNum = quickOperationDataInfo:GetDepositoryNum()
        if depositoryTotalNum > 0 then
            --BEGIN MODIFICATION @ VIRTUOS : SOL配装弹药：减少数量为0的商品时，直接返回，不弹提示（贴合鼠标点击减少数量的逻辑：数量为0时，减少数量按钮被隐藏）
            if WidgetUtil.IsGamepad() then
                return
            end
            --END MODIFICATION
            tips = Module.ArmedForce.Config.Loc.TheItemHasNotBeenAdded
        elseif not unlockShop and not unlockAuction then
            tips = Module.ArmedForce.Config.Loc.IsLock
        elseif unlockShop and not unlockAuction then
            tips = unlockInfos.auctionInfo.unlockTips
        elseif not unlockShop and unlockAuction then
            tips = unlockInfos.shopInfo.unlockTips
        --BEGIN MODIFICATION @ VIRTUOS : SOL配装弹药：减少数量为0的商品时，直接返回，不弹提示（贴合鼠标点击减少数量的逻辑：数量为0时，减少数量按钮被隐藏）
        elseif tips == "" and WidgetUtil.IsGamepad() then
            return
        end
        --END MODIFICATION
        Module.CommonTips:ShowSimpleTip(tips)
	end
end

-- 刷新itemview（刷新购物车图标）
function ********************************:_RefreshNeedBuyItemView(itemId)
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    if slotGroupId ~= ESlotGroup.OutFit then
        -- 方案2
        local quickOperationDataInfo = ArmedForceField:GetQuickOperationDataInfo(itemId)
        if not quickOperationDataInfo then
            return
        end
        -- 重新分配购物车图标
        local allMatchItems = Server.InventoryServer:GetItemsById(itemId, ESlotGroup.Preset)

        for index, item in ipairs(allMatchItems) do
            local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(item.gid)
            if quickOperationItemData then -- 有关联的
                local buyNum = quickOperationItemData.buyNum
                local bNeedBuy = buyNum > 0
                local bChanged = item.bNeedBuy ~= bNeedBuy
                if bChanged then
                    local slot = item.InSlot
                    local slotView = self:_GetSlotView(slot.SlotType)
                    local itemView = slotView:GetViewByItem(item)
                    if itemView then
                        item:GetRawPropInfo().bNeedBuy = bNeedBuy
                        item.bNeedBuy = bNeedBuy
                        itemView:RefreshView()
                    end
                end
            end
        end
    end
end

-- 尝试添加
function ********************************:_TryIncreaseProcess(type, quickOperationDataInfo, specifyNum)
    local itemId = quickOperationDataInfo:GetItemId()
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    -- 所需数
    local totalNeedNum = quickOperationDataInfo:GetAddNum()
    local needNum = specifyNum or totalNeedNum
    local maxStackCount = quickOperationDataInfo:GetMaxStackCount()
    local slotIncreaseDataMap = {}
    ArmedForceField:SetShowQuickOperationTipsed(false)
    -- 当前指定枪内>胸挂>口袋>背包>保险箱
    -- todo 如果是子弹武器优先填充
    -- 指定数量的话，不理会武器弹夹
    if self._myCurSubViewType == Module.ArmedForce.Config.ESubViewType.Bullet and not specifyNum then
        local fCalculateOperateNum, fTryGetOperateNum
        if slotGroupId == ESlotGroup.OutFit then
            fTryGetOperateNum = QuickOperationLogic.TryGetOperateNum_Outfit
        else
            fTryGetOperateNum = QuickOperationLogic.TryGetOperateNum
        end
        local bulletToWeaponNeedNum = self:_CheckBulletToWeaponFilled(type)
        while bulletToWeaponNeedNum > 0 do
            -- 先处理填充武器
            local operateNum = fTryGetOperateNum(quickOperationDataInfo, bulletToWeaponNeedNum, maxStackCount)
            if operateNum ~= 0 then
                self:_OnAddBulletToWeapon(type, quickOperationDataInfo, operateNum, slotIncreaseDataMap)
                bulletToWeaponNeedNum = bulletToWeaponNeedNum - operateNum
            else
                break
            end
        end
    end

    local fCalculateOperateNum, fTryGetOperateNum
    if slotGroupId == ESlotGroup.OutFit then
        fCalculateOperateNum = QuickOperationLogic.CalculateOperateNum_Outfit
        fTryGetOperateNum = QuickOperationLogic.TryGetOperateNum_Outfit
    else
        fCalculateOperateNum = QuickOperationLogic.CalculateOperateNum
        fTryGetOperateNum = QuickOperationLogic.TryGetOperateNum
    end
    

    needNum = QuickOperationLogic.TryIncreaseToSlot(quickOperationDataInfo, needNum, slotIncreaseDataMap, fCalculateOperateNum, fTryGetOperateNum)

    return slotIncreaseDataMap, needNum
end

-- 处理从往slotView里添加itemView
function ********************************:_IncreaseItemViewProcess(type, itemId, specifyNum)
    local quickOperationDataInfo = ArmedForceField:GetQuickOperationDataInfo(itemId)
    if not quickOperationDataInfo then
        logerror("********************************:_IncreaseItemProcess  'quickOperationDataInfo' is nil", itemId)
        return
    end

    local slotIncreaseDataMap, needNum = self:_TryIncreaseProcess(type, quickOperationDataInfo, specifyNum)

    for slotType, slotIncreaseData in pairs(slotIncreaseDataMap) do
        for index, itemViewData in ipairs(slotIncreaseData) do
            local item = itemViewData.item
            local operateNum = itemViewData.operateNum
            if item then
                local slotView = self:_GetSlotView(item.InSlot.SlotType)
                local itemView = slotView:GetViewByItem(item)
                if not itemView then -- 如果没有itemview的，则是刚添加的，需要添加到slotview中
                    local targetLoc = item.InSlot:GetItemLocation(item)
                    itemView = slotView:AddItem(item, targetLoc, targetLoc.bRotated)
                end
                if itemView then
                    itemView:RefreshView()
                    Timer.DelayCall(0, function ()
                        itemView:HighlightItem()
                    end)
                    self._wtMainScrollBox:ScrollWidgetIntoView(itemView, true, EDescendantScrollDestination.IntoView)
                end
            end
        end
    end

    --BEGIN MODIFICATION @ VIRTUOS : 刷新空白导航对象
    if IsHD() then
        self:_RefreshSlotViewBlankItem()
    end
    --END MODIFICATION

    return not table.isempty(slotIncreaseDataMap)
end

function ********************************:_CheckRemoveItem(operateItem)
    local presetMainContainerSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
    local targetLoc = presetMainContainerSlot:TryFindLocationForItem(operateItem)
    return targetLoc
end

function ********************************:_OnOnOperationItemViewUpDatePropInfo(oldItem, newPropInfo)
    if oldItem and newPropInfo then
        local slot = oldItem.InSlot
        if slot then
            local slotView = self:_GetSlotView(slot.SlotType)
            local newLoc = slot:GetItemLocation(oldItem)
            if newLoc then

                slotView:RemoveItem(oldItem)
                Server.InventoryServer:RemoveItemFromList(oldItem.gid, ESlotGroup.Preset)
                oldItem:RemoveSelf()


                local operateItem = ItemBase:NewIns(newPropInfo.id)
                operateItem:SetRawPropInfo(newPropInfo)
                operateItem:AddToSlot(slot)
                Server.InventoryServer:AddItemToList(operateItem, slot)
                local pbLocation = newLoc:ToPbLocation()
                local targetLoc = slot:SetItemPosFromPb(operateItem, pbLocation)
                operateItem:FreshRawPropInfoByLoc(targetLoc)
                local operateItemView = slotView:AddItem(operateItem, targetLoc, targetLoc.bRotated)
            end
            --BEGIN MODIFICATION @ VIRTUOS : 移除Item后，需要刷新一下BlankItem
            if slotView._RefreshBlankItem then
                slotView:_RefreshBlankItem()
            end
            --END MODIFICATION
        end
    end
end

-- 处理从往slotView里减少itemView
function ********************************:_DecreaseItemProcess(operateItem, decreaseNum)
    if decreaseNum > operateItem.num then
        logerror("********************************:_DecreaseItemProcess 减少的数量大于当前item的数量", operateItem.name, operateItem.id, operateItem.gid, operateItem.num, decreaseNum)
        return false
    end
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    local bRemove = decreaseNum == operateItem.num
    local itemId = operateItem.id
    local quickOperationDataInfo = ArmedForceField:GetQuickOperationDataInfo(itemId)
    if quickOperationDataInfo then
        local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(operateItem.gid)
        if quickOperationItemData then -- 有关联的
            if bRemove and not operateItem.bNeedBuy and slotGroupId ~= ESlotGroup.OutFit then -- 如果该item是有需要仓库移动数量的部分，则需要检查放回时仓库是否能接受
                local targetLoc = self:_CheckRemoveItem(operateItem)
                if not targetLoc then -- 每次放回都是只检查一个道具，不会累积的
                    Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.PutBackFailedByInventorySpaceLacking)
                    return false
                end
            end
        end
    elseif bRemove then -- 其他同类型的道具，非关联右边也可以移除
        local targetLoc = self:_CheckRemoveItem(operateItem)
        if not targetLoc and slotGroupId ~= ESlotGroup.OutFit then -- 每次放回都是只检查一个道具，不会累积的
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.PutBackFailedByInventorySpaceLacking)
            return false
        end
        Module.CommonTips:ShowSimpleTip(string.format(Module.ArmedForce.Config.Loc.AlreadyReturnedToWarehouse, operateItem.name))
    end

    if quickOperationDataInfo then
        QuickOperationLogic.CalculateOperateNum(quickOperationDataInfo, operateItem, -decreaseNum) -- 计算当前操作数
        if quickOperationDataInfo.itemMainType == EItemType.Medicine then
            if bRemove and not operateItem.bNeedBuy then
                quickOperationDataInfo:AddDepositoryPropInfo(operateItem:GetRawPropInfo())
            end
        end
    end

    if bRemove then -- 移除
        local slot = operateItem.InSlot
        if slot then
            local slotView = self:_GetSlotView(slot.SlotType)
            local operateNum = operateItem.num
            --BEGIN MODIFICATION @ VIRTUOS : 移除Item前，记录一下Item的位置来恢复聚焦
            if IsHD() then
                local blanklocation = slot:GetItemLocation(operateItem)
            end
            --END MODIFICATION
            slotView:RemoveItem(operateItem)
            Server.InventoryServer:RemoveItemFromList(operateItem.gid, ESlotGroup.Preset)
            operateItem:RemoveSelf()
            
            --BEGIN MODIFICATION @ VIRTUOS : 移除Item后，需要刷新一下BlankItem
            if IsHD() then
                if slotView._RefreshBlankItem then
                    slotView:_RefreshBlankItem()
                    -- 聚焦在背包/安全箱的时候才恢复聚焦到blankitem
                    if UGPUINavigationUtils.IsWidgetInFocusPath(0, self._wtMainScrollBox) or 
                    UGPUINavigationUtils.IsWidgetInFocusPath(0, self._wtSafeBoxContainerView) then
                        slotView:TrySetFocusToBlankItem(blanklocation)
                    end
                end
            end
            --END MODIFICATION
        else
            local loc
            logerror("********************************:_DecreaseItemProcess slot is nil",bRemove, operateItem.name, operateItem.id, operateItem.gid, operateItem.num, decreaseNum)
        end
    else -- 刷新 -- todo刷新购物车
        operateItem:ClientSetNum(operateItem.num - decreaseNum)
        if quickOperationDataInfo then
            local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(operateItem.gid)
            if quickOperationItemData then -- 有关联的
                local buyNum = quickOperationItemData.buyNum
                local bNeedBuy = buyNum > 0
                local slot = operateItem.InSlot
                local slotView = self:_GetSlotView(slot.SlotType)
                local itemView = slotView:GetViewByItem(operateItem)
                if itemView then
                    operateItem.bNeedBuy = bNeedBuy
                    itemView:RefreshView()
                    Timer.DelayCall(0, function ()
                        itemView:HighlightItem()
                    end)
                end
            end
        end
    end

    return true
end

local function fCheckAllItemsValidity(t)
    local gid2ItemMap = {}
    local length = #t
    for i = length, 1, -1 do
        local item = t[i]
        if not gid2ItemMap[item.gid] and item.InSlot and item.InSlot:GetItemLocation(item) then
            gid2ItemMap[item.gid] = item
        else
            table.remove(t, i)
        end
    end
end

local function fSplitTable(t, n)
    local result = {}
    local group = {}
    for i, value in ipairs(t) do
        table.insert(group, value)
        if #group == n or i == #t then
            table.insert(result, group)
            group = {}
        end
    end
    return result
end

-- 设置左边InSlotView预览数据
function ********************************:InitContainerItems()
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    if slotGroupId == ESlotGroup.OutFit then
        -- 初始化容器道具
        self:InitOutfitContainerItems()
    else
        -- 初始化容器道具
        self:InitPlayerContainerItems()
    end
end
function ********************************:InitPlayerContainerItems()
    self._bCanOperate = false
    self._allItems = {}
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()

    local function fSetSlotView(slotType)
        local bInit = true
        local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
        presetSlot:ResetSlot()

        local playerContainerSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)

        if slotType == ESlotType.Pocket then
            presetSlot:InitPocketContainer()
        else
            local containerType = playerContainerSlot:GetContainerType()
            local playerEquipContainerSlot = Server.InventoryServer:GetSlot(containerType, slotGroupId)

            local presetBindEquipSlot = Server.InventoryServer:GetSlot(containerType, ESlotGroup.Preset)
            presetBindEquipSlot:ResetSlot()
            local equipment = playerEquipContainerSlot:GetEquipItem()
            if equipment then
                
                if containerType == ESlotType.SafeBox then
                    local itemFeature = equipment:GetFeature(EFeatureType.Equipment)
                    if itemFeature:GetExpiredStatus() then
                        bInit = false
                    end
                end
                if bInit then
                    presetSlot:InitContainerByEquipmentId(equipment.id)

                    local presetEquiment = ItemBase:NewIns(equipment.id)
                    local propInfo = {}
                    local rawPropInfo = equipment:GetRawPropInfo()
                    if rawPropInfo then -- 这里后续要注意局内拾取到的道具是否没有rawpropinfo
                        deepcopy(propInfo, rawPropInfo)
                        presetEquiment:SetRawPropInfo(propInfo)
                    end
                    presetEquiment:AddToSlot(presetBindEquipSlot)
                    Server.InventoryServer:AddItemToList(presetEquiment, presetBindEquipSlot)
                end
            end
        end
        if bInit then
            local items = playerContainerSlot:GetItems()
            table.append(self._allItems, items)
        end
        local containerView = self:_GetContainerView(slotType)
        containerView:RefreshView() -- 必须放在这里，后面都是单个itemview刷新的，保留itemview缓存信息
        containerView:_CheckContainerSlotVisibility()
    end

    fSetSlotView(ESlotType.ChestHangingContainer) -- 胸挂
    fSetSlotView(ESlotType.Pocket) -- 口袋
    fSetSlotView(ESlotType.BagContainer)
    fSetSlotView(ESlotType.SafeBoxContainer)
end

function ********************************:InitOutfitContainerItems()
    self._bCanOperate = false
    self._allItems = {}
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()

    local function fSetSlotView(slotType)
        local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
        presetSlot:ResetSlot()

        local equipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(slotType)
        if equipPosition and not table.isempty(equipPosition.load_props) then
            for _, propInfo in ipairs(equipPosition.load_props) do
                local newPropInfo = {}
                deepcopy(newPropInfo, propInfo)
                newPropInfo.bNeedBuy = false
                local item = ItemBase:New(newPropInfo.id)
                item:SetRawPropInfo(newPropInfo)
                table.insert(self._allItems, item)
            end
        end


        if slotType == ESlotType.Pocket then
            presetSlot:InitPocketContainer()
        else
            local containerType = presetSlot:GetContainerType()
            local presetBindEquipSlot = Server.InventoryServer:GetSlot(containerType, ESlotGroup.Preset)
            presetBindEquipSlot:ResetSlot()
            local containerEquipPosition = Module.ArmedForce.Field:GetCurOperationOutfitEquipPositionBySlotType(containerType)
            if containerEquipPosition and not table.isempty(containerEquipPosition.load_props) then
                local equipPropInfo = containerEquipPosition.load_props[1]
                if equipPropInfo then
                    presetSlot:InitContainerByEquipmentId(equipPropInfo.id)

                    local presetEquiment = ItemBase:NewIns(equipPropInfo.id)
                    local propInfo = {}
                    if equipPropInfo then
                        deepcopy(propInfo, equipPropInfo)
                        presetEquiment:SetRawPropInfo(propInfo)
                    end
                    
                    presetEquiment:AddToSlot(presetBindEquipSlot)
                    Server.InventoryServer:AddItemToList(presetEquiment, presetBindEquipSlot)
                end
            end
        end
        local containerView = self:_GetContainerView(slotType)
        containerView:RefreshView() -- 必须放在这里，后面都是单个itemview刷新的，保留itemview缓存信息
        containerView:_CheckContainerSlotVisibility()
    end
    fSetSlotView(ESlotType.ChestHangingContainer) -- 胸挂
    fSetSlotView(ESlotType.Pocket) -- 口袋
    fSetSlotView(ESlotType.BagContainer)
    fSetSlotView(ESlotType.SafeBoxContainer)
end

function ********************************:_RefreshContainerItems()
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    if slotGroupId == ESlotGroup.OutFit then
        -- 初始化容器道具
        self:_RefreshOutfitContainerItems()
    else
        -- 初始化容器道具
        self:_RefreshPlayerContainerItems()
    end
end

function ********************************:_RefreshPlayerContainerItems()
    local interval = 0.2
    local maxNum = 100
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    local allQuickOperationDataInfo = ArmedForceField:GetAllQuickOperationDataInfo()
    -- Module.ArmedForce.Config.evtArmedForceQuickOperationOpenFinish:Invoke()
    fCheckAllItemsValidity(self._allItems)
    local groups = fSplitTable(self._allItems, maxNum)
    if #groups > 0 then
        -- 放置原先的道具(分批)
        for i, group in ipairs(groups) do
            loginfo("********************************:InitContainerItems Groups ======================================> group:", i)
            Timer.DelayCall(i * interval, function ()
                for index, item in ipairs(group) do
                    local slotType = item.InSlot.SlotType
                    local slotView = self:_GetSlotView(slotType)
                    local newLoc = item.InSlot:GetItemLocation(item)
                    if newLoc then
                        local itemMainType = ItemHelperTool.GetMainTypeById(item.id)
                        local  quickOperationDataInfo = allQuickOperationDataInfo[item.id]
                        local operateItem = ItemBase:NewIns(item.id)
                        local gid = item.gid

                        local propInfo = {}
                        local rawPropInfo = item:GetRawPropInfo()
                        if rawPropInfo then
                            if slotGroupId == ESlotGroup.Player and quickOperationDataInfo and quickOperationDataInfo.itemMainType == EItemType.Medicine then
                                local depositoryPropInfo = quickOperationDataInfo:GetDepositoryPropInfo(item.gid)
                                if depositoryPropInfo then
                                    rawPropInfo = depositoryPropInfo
                                end
                            end
                            if rawPropInfo then
                                deepcopy(propInfo, rawPropInfo)
                                propInfo.gid = gid
                                operateItem:SetRawPropInfo(propInfo)
                            end
                        end
                        -- end
                        if operateItem then
                            local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
                            operateItem:AddToSlot(presetSlot)
                            Server.InventoryServer:AddItemToList(operateItem, presetSlot)
                            local pbLocation = newLoc:ToPbLocation()
                            local targetLoc = presetSlot:SetItemPosFromPb(operateItem, pbLocation)
                            operateItem:FreshRawPropInfoByLoc(targetLoc)
                            local operateItemView = slotView:AddItem(operateItem, targetLoc, targetLoc.bRotated)
                            if quickOperationDataInfo then
                                quickOperationDataInfo:CalculateMoveNum(operateItem, operateItem.num)
                                Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(item.id)
                            end
                        end
                    end
                end
                if #groups == i then
                    self._bCanOperate = true
                end
                self:_RefreshCapacity()
                --BEGIN MODIFICATION @ VIRTUOS : 刷新空白导航对象
                if IsHD() then
                    self:_RefreshSlotViewBlankItem()
                end
                --END MODIFICATION
            end, self)
        end
    else
        self._bCanOperate = true
    end
end

function ********************************:_RefreshOutfitContainerItems()
    local interval = 0.2
    local maxNum = 100
    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
    local allQuickOperationDataInfo = ArmedForceField:GetAllQuickOperationDataInfo()
    -- Module.ArmedForce.Config.evtArmedForceQuickOperationOpenFinish:Invoke()
    local groups = fSplitTable(self._allItems, maxNum)
    if #groups > 0 then
        -- 放置原先的道具(分批)
        for i, group in ipairs(groups) do
            loginfo("********************************:InitContainerItems Groups ======================================> group:", i)
            Timer.DelayCall(i * interval, function ()
                for index, item in ipairs(group) do
                    local propInfo = item:GetRawPropInfo()
                    local slotType = propInfo.position
                    local slotView = self:_GetSlotView(slotType)
                    -- local newLoc = item.InSlot:GetItemLocation(item)
                    -- if newLoc then
                    --     local itemMainType = ItemHelperTool.GetMainTypeById(item.id)
                        local  quickOperationDataInfo = allQuickOperationDataInfo[item.id]
                        -- local operateItem = ItemBase:NewIns(item.id)
                        -- -- if quickOperationDataInfo then
                        -- --     gid =  GetGid()
                        -- -- end

                        -- local propInfo = {}
                        -- local rawPropInfo = item:GetRawPropInfo()
                        -- if rawPropInfo then
                        --     if slotGroupId == ESlotGroup.Player and quickOperationDataInfo and quickOperationDataInfo.itemMainType == EItemType.Medicine then
                        --         rawPropInfo = quickOperationDataInfo:GetDepositoryPropInfo(item.gid)
                        --     end
                        --     if rawPropInfo then
                        --         deepcopy(propInfo, rawPropInfo)
                        --         propInfo.gid = gid
                        --         operateItem:SetRawPropInfo(propInfo)
                        --     end
                        -- end
                        -- end
                        if item then
                            local presetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
                            item:AddToSlot(presetSlot)
                            Server.InventoryServer:AddItemToList(item, presetSlot)
                            local targetLoc = presetSlot:SetItemPosFromPb(item, propInfo.loc)
                            local operateItemView = slotView:AddItem(item, targetLoc, targetLoc.bRotated)
                        end
                        if quickOperationDataInfo then
                            quickOperationDataInfo:CalculateNum_Outfit(item, item.num)
                            Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(item.id)
                        end
                    end
                if #groups == i then
                    self._bCanOperate = true
                end
                self:_RefreshCapacity()
            end, self)
        end
    else
        self._bCanOperate = true
    end
end


function ********************************:OnAnimFinished(anim)
    if anim == self.WBP_AssemblyStorageDefault_in then
        Module.ArmedForce.Config.evtArmedForceQuickOperationOpenFinish:Invoke()
    end
end

--region ====================配药==============================


--endregion ====================配药==============================

--region ====================配子弹==============================
function ********************************:_GetWeaponDatas()
    local weaponDatas = {}
    local function fCheckWeaponBySlotType(slotType)
        local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
        local weaponSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)
        local weapon = weaponSlot:GetEquipItem()
        table.insert(weaponDatas, weapon)
    end

    fCheckWeaponBySlotType(ESlotType.MainWeaponLeft)
    fCheckWeaponBySlotType(ESlotType.MainWeaponRight)
    fCheckWeaponBySlotType(ESlotType.Pistrol)

    return weaponDatas
end


function ********************************:_CheckBulletToWeaponFilled(type)
    local needNum = 0
    local slot = Server.InventoryServer:GetSlot(type, ESlotGroup.Preset)
	local equipItem = slot:GetEquipItem()
	if equipItem then
		-- todo 获得子弹容量
		local currentBulletNum, capacity = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(equipItem:GetRawPropInfo())
		needNum = capacity - currentBulletNum
	end
    return needNum
end

-- 装填子弹
function ********************************:_OnAddBulletToWeapon(type, quickOperationDataInfo, num, slotIncreaseDataMap)
    if num > 0 and self._bCanOperate then
        local operateNum = num
        if quickOperationDataInfo then
            local slot = Server.InventoryServer:GetSlot(type, ESlotGroup.Preset)
            local equipItem = slot:GetEquipItem()
            if equipItem then
                declare_if_nil(slotIncreaseDataMap, type, {})
                table.insert(slotIncreaseDataMap[type], {
                    operateNum = operateNum
                })

                local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
                if slotGroupId == ESlotGroup.OutFit then
                    quickOperationDataInfo:CalculateWeaponBulletMoveNum_Outfit(equipItem, operateNum)
                    operateNum = 0
                else
                    local remainDepositoryNum = quickOperationDataInfo:GetDepositoryNum()
                    if remainDepositoryNum > 0 then
                        local operateDepositoryNum = math.min(remainDepositoryNum, operateNum)
                        quickOperationDataInfo:CalculateWeaponBulletMoveNum(equipItem, operateDepositoryNum)
                        operateNum = operateNum - operateDepositoryNum
                    end
                    if operateNum > 0 then
                        quickOperationDataInfo:CalculateWeaponBulletBuyNum(equipItem, operateNum)
                    end
                end

                -- todo遍历武器子弹
                local bullets = WeaponAssemblyTool.GetWeaponBullets(equipItem:GetRawPropInfo())
                local bAdd = false
                if not table.isempty(bullets) then
                    for _, bulletPropInfo in ipairs(bullets) do
                        -- 如果原来的子弹上有则添加数量
                        if bulletPropInfo.id == quickOperationDataInfo:GetItemId() then
                            bulletPropInfo.num = bulletPropInfo.num + num
                            bAdd = true
                        end
                    end
                end
                if not bAdd then
                    local bulletPropInfo = pb.PropInfo:New()
                    bulletPropInfo.id = quickOperationDataInfo:GetItemId()
                    bulletPropInfo.gid = GetGid()
                    bulletPropInfo.num = num
                    local load_bullets = equipItem:GetRawPropInfo().weapon.load_bullets
                    table.insert(load_bullets, bulletPropInfo)
                end
                Module.CommonTips:ShowSimpleTip(StringUtil.PluralTextFormat(Module.ArmedForce.Config.Loc.AutoLoadBulletToWeapon,{["num"] = num, ["name"] = equipItem.name}))
                local desc = WeaponAssemblyTool.PropInfo_To_Desc(equipItem:GetRawPropInfo())
                equipItem:SetRawDescObj(desc)
            end
        end
        Module.ArmedForce.Config.evtWeaponBulletDataChanged:Invoke(type, true)
    end
end

-- 卸下子弹
function ********************************:_OnRemoveBulletForWeapon(type)
    ArmedForceField:SetShowQuickOperationTipsed(true) -- 设置为true已提示，防止提示空间不足无法添加
    local slot = Server.InventoryServer:GetSlot(type, ESlotGroup.Preset)
	local equipItem = slot:GetEquipItem()
	if equipItem and self._bCanOperate then
        local weaponGid = equipItem.gid
		local bullets = WeaponAssemblyTool.GetWeaponBullets(equipItem:GetRawPropInfo())
        if not table.isempty(bullets) then
            local result = true
            local itemviewMap = {}
            for _, bulletPropInfo in ipairs(bullets) do
                local quickOperationDataInfo = Module.ArmedForce:GetQuickOperationDataInfo(bulletPropInfo.id)
                if quickOperationDataInfo then
                    local itemId = quickOperationDataInfo:GetItemId()
                    local maxStackCount = quickOperationDataInfo:GetMaxStackCount()
                    local quickOperationItemData = quickOperationDataInfo:GetItemDataByGid(weaponGid)
                    if quickOperationItemData then
                        local buyNum = quickOperationItemData.buyNum
                        local moveNum = quickOperationItemData.moveNum
                        local needNum = buyNum + moveNum
                        declare_if_nil(itemviewMap, bulletPropInfo.id, {
                            weaponGid = weaponGid,
                            originalBuyNum = buyNum,
                            originalMoveNum = moveNum,
                            slotIncreaseDataMap = {}
                        })
                        local function fTryGetOperateNum(quickOperationDataInfo, inNeedNum, maxStackCount)
                            return inNeedNum
                        end
                        needNum = QuickOperationLogic.TryIncreaseToSlot(quickOperationDataInfo, needNum, itemviewMap[bulletPropInfo.id].slotIncreaseDataMap, nil, fTryGetOperateNum)
                        if needNum > 0 then
                            result = false
                        end
                    end
                end
            end
            loginfo("******************************** [子弹右键] 卸下到身上" , result and "成功" or "失败")
            if result then
                -- 刷新itemview
                for bulletId, data in pairs(itemviewMap) do
                    -- local weaponGid = data.weaponGid
                    local originalBuyNum = data.originalBuyNum
                    local originalMoveNum = data.originalMoveNum
                    local quickOperationDataInfo = Module.ArmedForce:GetQuickOperationDataInfo(bulletId)
                    local slotIncreaseDataMap = data.slotIncreaseDataMap
                    for slotType, slotIncreaseData in pairs(slotIncreaseDataMap) do
                        for index, itemViewData in ipairs(slotIncreaseData) do
                            local item = itemViewData.item
                            local operateNum = itemViewData.operateNum
                            if item then
                                local slotView = self:_GetSlotView(item.InSlot.SlotType)
                                local itemView = slotView:GetViewByItem(item)
                                if not itemView then -- 如果没有itemview的，则是刚添加的，需要添加到slotview中
                                    local targetLoc = item.InSlot:GetItemLocation(item)
                                    itemView = slotView:AddItem(item, targetLoc, targetLoc.bRotated)
                                end
                                if itemView then
                                    local slotGroupId = Server.ArmedForceServer:GetCurSlotGroupId()
                                    if slotGroupId == ESlotGroup.OutFit then
                                        if operateNum > 0 and originalMoveNum > 0 then
                                            local curMoveNum = math.min(operateNum, originalMoveNum)
                                            originalMoveNum = originalMoveNum - curMoveNum
                                            operateNum = operateNum - curMoveNum
                                            quickOperationDataInfo:RemoveWeaponBulletMoveNum_Outfit(equipItem, item, curMoveNum)
                                        end
                                    else
                                        if operateNum > 0 and originalMoveNum > 0 then
                                            local curMoveNum = math.min(operateNum, originalMoveNum)
                                            originalMoveNum = originalMoveNum - curMoveNum
                                            operateNum = operateNum - curMoveNum
                                            quickOperationDataInfo:RemoveWeaponBulletMoveNum(equipItem, item, curMoveNum)
                                        end
    
                                        if operateNum > 0 and originalBuyNum > 0 then
                                            local curBuyNum = math.min(operateNum, originalBuyNum)
                                            originalBuyNum = originalBuyNum - curBuyNum
                                            operateNum = operateNum - curBuyNum
                                            quickOperationDataInfo:RemoveWeaponBulletBuyNum(equipItem, item, curBuyNum)
                                        end
                                    end
                                    Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(quickOperationDataInfo:GetItemId())
                                    itemView:RefreshView()
                                    self:_RefreshNeedBuyItemView(bulletId)
                                    Timer.DelayCall(0, function ()
                                        itemView:HighlightItem()
                                    end)
                                    self._wtMainScrollBox:ScrollWidgetIntoView(itemView, true, EDescendantScrollDestination.IntoView)
                                end
                            end
                        end
                    end
                end

                -- todo将武器子弹置空
                equipItem:GetRawPropInfo().weapon.load_bullets = {}
                local desc = WeaponAssemblyTool.PropInfo_To_Desc(equipItem:GetRawPropInfo())
                equipItem:SetRawDescObj(desc)
                self._bShowBtn = true
                self:_RefreshAllBtn()
                Module.ArmedForce.Config.evtWeaponBulletDataChanged:Invoke(type, true)
            else -- 如果添加失败则还原添加的数量
                Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.SpaceInsufficientUnableToUnloadBullets)
                -- 需要还原数据
                for bulletId, data in pairs(itemviewMap) do
                    -- local weaponGid = data.weaponGid
                    local originalBuyNum = data.originalBuyNum
                    local originalMoveNum = data.originalMoveNum
                    local quickOperationDataInfo = Module.ArmedForce:GetQuickOperationDataInfo(bulletId)
                    local slotIncreaseDataMap = data.slotIncreaseDataMap
                    for slotType, slotIncreaseData in pairs(slotIncreaseDataMap) do
                        for index, itemViewData in ipairs(slotIncreaseData) do
                            local item = itemViewData.item
                            local operateNum = itemViewData.operateNum
                            logwarning(string.format("******************************** [子弹右键] 还原 ==> id = %s, 原购买数 = %s, 原移动数 = %s, item数 = %s, 操作数 = %s", bulletId, originalBuyNum, originalMoveNum, item.num, operateNum))
                            if item then
                                item:ClientSetNum(item.num - operateNum)
                                local slotView = self:_GetSlotView(item.InSlot.SlotType)
                                local itemView = slotView:GetViewByItem(item)
                                if item.num > 0 then
                                    logwarning(string.format("******************************** [子弹右键] 还原成功 ==> item数 = %s", item.num))
                                    if itemView then
                                        itemView:RefreshView()
                                        -- self:_RefreshNeedBuyItemView(bulletId)
                                    end
                                else
                                    logwarning(string.format("******************************** [子弹右键] 还原成功 ==> item数 = %s, 并移除！！！", item.num))
                                    if itemView then
                                        slotView:RemoveItem(item)
                                    end
                                    Server.InventoryServer:RemoveItemFromList(item.gid, ESlotGroup.Preset)
                                    item:RemoveSelf()
                                end
                                Module.ArmedForce.Config.evtQuickOperationDataInfoChanged:Invoke(quickOperationDataInfo:GetItemId())
                            end
                        end
                    end
                end
            end
        end
    end
end


function ********************************:_OnFinishQuickOperationData()
    self:_CanceProcessing()
    Module.ArmedForce.Config.evtArmedForceQuickOperationConfirmClicked:Invoke()
	Facade.UIManager:CloseUI(self)
end

function ********************************:_CanceProcessing()
    self._bProcessing = false 
    self._wtBtnConfirm:SetIsEnabledStyle(true)
    self:ReleaseTimer()
end

function ********************************:RefreshAllQuickOperationDataNeedBuyItemView()
    local allQuickOperationDataInfo = Module.ArmedForce.Field:GetAllQuickOperationDataInfo()
    for id, quickOperationDataInfo in pairs(allQuickOperationDataInfo) do
        self:_RefreshNeedBuyItemView(id)
    end
    
end

function ********************************:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end
    Module.ArmedForce:RemoveAssembledTips(self, true)
    self:_InternalSetInputSummary(self._waitInputSummaryList)
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function ********************************:_EnableNavigation()
    if not IsHD() then
       return
    end

    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
    end

    -- 导航组
    if not self._wtNavGroup then
        self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtMedicineScrollBox, self, "Hittest")
        if self._wtNavGroup then
            self._wtNavGroup:AddNavWidgetToArray(self._wtMedicineScrollBox)
            self._wtNavGroup:SetNavSelectorWidgetVisibility(true)
            self._wtNavGroup:SetScrollRecipient(self._wtMedicineScrollBox)    
        end
    end
    
    -- 背包导航组
    if not self._wtNavGroupBackPack then
        self._wtNavGroupBackPack = WidgetUtil.RegisterNavigationGroup(self._wtAssemblyBackpack, self, "Hittest")
        self._wtNavGroupBackPack.OnNavGroupFocusReceivedEvent:Add(self._OnEquipWareHouseFocusReceived, self)
        
        if self._wtNavGroupBackPack then
            self._wtNavGroupBackPack:AddNavWidgetToArray(self._wtMainScrollBox)
            self._wtNavGroupBackPack:SetScrollRecipient(self._wtMainScrollBox)
            self._wtNavGroupBackPack:AddNavWidgetToArray(self._wtSafeBoxContainerView)
            self._wtNavGroupBackPack:SetNavSelectorWidgetVisibility(true)
            -- self._wtNavGroupBackPack:SetCustomSimulateMouseKeyConfigName("QuickOperationKeyConfig")
            local _BackPackNavStratrgy = self._wtNavGroupBackPack:GetOwnerNavStrategy()
            if _BackPackNavStratrgy then
                _BackPackNavStratrgy:SetHitPadding(4.0)
            end
        end
    end
    
    -- 安全箱导航组
    -- if not self._wtNavGroupSafeBox then
    --     self._wtNavGroupSafeBox = WidgetUtil.RegisterNavigationGroup(self._wtSafeBoxContainerView, self, "Hittest")
    --     if self._wtNavGroupSafeBox then
    --         self._wtNavGroupSafeBox:AddNavWidgetToArray(self._wtSafeBoxContainerView)
    --         self._wtNavGroupSafeBox:SetNavSelectorWidgetVisibility(true)
    --         -- self._wtNavGroupSafeBox:SetCustomSimulateMouseKeyConfigName("QuickOperationKeyConfig")
    --         local _SafeBoxNavStratrgy = self._wtNavGroupSafeBox:GetOwnerNavStrategy()
    --         if _SafeBoxNavStratrgy then
    --             _SafeBoxNavStratrgy:SetHitPadding(4.0)
    --         end
    --     end
    -- end
    -- -- 启用没有手柄十字键和A键的导航配置
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
    
    -- -- 显示按键提示
    -- self:_InternalSetInputSummary()   
end

function ********************************:_DisableNavigation()
    if not IsHD() then
        return
     end
     if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
     if self._wtNavGroup or self._wtNavGroupBackPack then
        WidgetUtil.RemoveNavigationGroup(self)
        -- 恢复默认导航配置
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self._wtNavGroup = nil
        self._wtNavGroupBackPack = nil
        -- self._wtNavGroupSafeBox = nil
        self._waitInputSummaryList = nil
     end

    self:_RemoveShortcuts()
end

-- 关闭页面中 “问号” 提示的可聚焦
function ********************************:_DisabelHoverTipsFocus()
    if not IsHD() then
        return 
    end

    local containerSlotType = {
        ESlotType.ChestHangingContainer, -- 胸挂
        ESlotType.Pocket, -- 口袋
        ESlotType.BagContainer,
        ESlotType.SafeBoxContainer,
    }
    for _, slotType in ipairs(containerSlotType) do
        local containerView = self:_GetContainerView(slotType)
        if containerView then
            local wtTipsCheckBox = containerView:Wnd("wtHoverBtn", UIWidgetBase)
            local checkBtn = wtTipsCheckBox:Wnd("DFCheckBox_Icon", UICheckBox)
            if checkBtn then
                checkBtn:SetCppValue("IsFocusable", false)
            end
        end
    end
end

-- 新增手柄快捷键（页面中显示的按钮：确认）
function ********************************:_ShortcutEventOnConfirmBtnClicked()
    if self._wtBtnConfirm:IsVisible() and self._wtBtnConfirm.ButtonClick then
        self._wtBtnConfirm:ButtonClick()
    end
end

-- 兑换
function ********************************:_ShortcutEventOnExchangeBtnClicked()
    if self._wtExchangeBtn:IsVisible() and self._wtExchangeBtn.ButtonClick then
        self._wtExchangeBtn:ButtonClick()
    end
end

function ********************************:_InitShortcuts()
    self:_RemoveShortcuts()

    -- 确认
    self._Confirm = self:AddInputActionBinding("Assembly_QuickOperation_Confirm_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventOnConfirmBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
    self._wtBtnConfirm:SetDisplayInputActionWithLongPress(self._Confirm, self, "Assembly_QuickOperation_Confirm_Gamepad", true, nil, true)

    -- 兑换
    if self._wtExchangeBtn:IsVisible() then
        self._Exchange = self:AddInputActionBinding("Assembly_Exchange_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventOnExchangeBtnClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtExchangeBtn:SetDisplayInputAction("Assembly_Exchange_Gamepad", true, nil, true)
    end
    self:_InternalSetInputSummary(self._waitInputSummaryList)
end

function ********************************:_RemoveShortcuts()
    if not IsHD() then
        return
    end

    if self._Confirm then
        self:RemoveInputActionBinding(self._Confirm)
        self._Confirm= nil
    end

    if self._Exchange then
        self:RemoveInputActionBinding(self._Exchange)
        self._Exchange= nil
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function ********************************:_CarouselDetails()

end

function ********************************:_InternalSetInputSummary(inputSummaryList, itemView)
    if not IsHD() then
        return
    end
    -- 是否悬浮到仓库的itemView上
    local bHoverWarehouseItemView = itemView ~= nil
    local bHoverWarehouseItemViewIsvalid = self:_CheckItemViewCanShowSummaryList(itemView)
    self._waitInputSummaryList = inputSummaryList
    local summaryList = {}

    if not bHoverWarehouseItemView or (bHoverWarehouseItemView and not table.isempty(inputSummaryList)) then
        -- 详情
        table.insert(summaryList, {actionName = "Assembly_Details_Gamepad",func = self.ShowHoverTips, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    if bHoverWarehouseItemView and bHoverWarehouseItemViewIsvalid then
        local function fRemovefunc()
            self:_OnLeftDecClicked(itemView)
        end
        -- 放回
        table.insert(summaryList, {actionName = itemView.item.bNeedBuy and "Assembly_RemoveItemView_Gamepad" or "Assembly_RemoveItemViewToInventory_Gamepad",func = fRemovefunc, caller = self ,bUIOnly = false, bHideIcon = false})
    end

    if not bHoverWarehouseItemView or bHoverWarehouseItemViewIsvalid then
        if inputSummaryList then
            for _, summary in pairs(inputSummaryList) do
                table.insert(summaryList, summary)
            end
        end
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false)
end

function ********************************:_CheckItemViewCanShowSummaryList(itemView)
    local bcanShowSummaryList = false
    if itemView and itemView.item and itemView.item.InSlot:IsContainerSlot() then
        bcanShowSummaryList = true
    end
    return bcanShowSummaryList
end

function ********************************:_BottomBarSummaryListChangedByItemView(inputSummaryList, itemView)
    self:_InternalSetInputSummary(inputSummaryList, itemView)
end

function ********************************:_RefreshSlotViewBlankItem()
    if not IsHD() then
        return
    end

    self._wtBagSlotView:_RefreshBlankItem()
    self._wtChestHangingSlotView:_RefreshBlankItem()
    self._wtPocketSlotView:_RefreshBlankItem()
    self._wtSafeBoxSlotView:_RefreshBlankItem()
end

-- END MODIFICATION
local ContainerSlotList = 
{
    ESlotType.ChestHangingContainer,
    ESlotType.Pocket,
    ESlotType.BagContainer,
    ESlotType.SafeBoxContainer
}

function ********************************:ShowHoverTips()
    if not IsHD() then
        return 
    end
    -- 通过按键逐个显示HoverTips
    if self._curShowTipsIndex < 4 then
        local lastHoveTip = self:_GetContainerView(ContainerSlotList[self._curShowTipsIndex])
        if lastHoveTip and lastHoveTip.OnUnHoverTipByGamepad then
            lastHoveTip:OnUnHoverTipByGamepad()
        end
        self._curShowTipsIndex = self._curShowTipsIndex + 1
        local hoverTip = self:_GetContainerView(ContainerSlotList[self._curShowTipsIndex])
        if hoverTip and hoverTip.OnHoverTipByGamepad then
            hoverTip:OnHoverTipByGamepad()
            if self._wtMainScrollBox then
                self._wtMainScrollBox:ScrollWidgetIntoView(hoverTip, true, EDescendantScrollDestination.IntoView)
            end
        end
    elseif self._curShowTipsIndex == 4 then
        self:CloseHoverTips()
    end
end

function ********************************:CloseHoverTips()
    if not IsHD() then
        return 
    end
    
    if self._curShowTipsIndex ~= 0 then
        local hoverTip = self:_GetContainerView(ContainerSlotList[self._curShowTipsIndex])
        if hoverTip and hoverTip.OnUnHoverTipByGamepad then
            hoverTip:OnUnHoverTipByGamepad()
        end
        self._curShowTipsIndex = 0
    end
end

function ********************************:_ExitSwapItemMode()
    if Module.CommonWidget:IsInSwapingItem() then
        Module.CommonWidget:SetGamepadSwapItem(false)
    end
    -- she3移入InventoryNavManager统一控制
    Module.Inventory.Config.Events.evtRemoveYModeBlankItemVisibility:Invoke()
    Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(false)
end

-- 绑定输入类型切换事件
function ********************************:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end
    if bEnable then
        if not self._onNotifyInputTypeChangedHandle then
            self._onNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end
    else
        if self._onNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._onNotifyInputTypeChangedHandle)
            self._onNotifyInputTypeChangedHandle = nil
        end
    end
end

-- 切换到非手柄时直接隐藏Tip
function ********************************:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end
    -- 从手柄切到键鼠
    if not (inputType == EGPInputType.Gamepad) then
        -- 退出手柄移动物品状态
        self:_ExitSwapItemMode()
        -- -- 关闭手柄打开的HoverTips
        -- if self._wtEquipPanel and self._wtEquipPanel.CloseHoverTips then
        --     self._wtEquipPanel:CloseHoverTips()
        -- end
    end
    -- WarehousePanel
    -- self._wtAssemblyBackpack:OnInputTypeChanged(inputType)
end

function ********************************:_OnGlobalSwapItem(bIsSwapItem)
    if not IsHD() then
        return 
    end
    InventoryNavManager.UpdateInputSummaryOnDrag(true)
    UDFNavigationSelectorBase.SetForceHideSelectorRoot(bIsSwapItem)
    -- if WidgetUtil.IsGamepad() and bIsSwapItem == true then
    --     -- 在手柄拖拽时不希望导航到这些按钮
    --     self:_SetEnableHeroInfoBtns(false)
    -- else
    --     self:_SetEnableHeroInfoBtns(true)
    -- end
end

function ********************************:_DisableDynamicNavConfig()
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

---@param dragDropInfo ItemDragDropInfo
function ********************************:_OnGlobalItemDragStart(dragDropInfo, operation, pointerEvent)
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        InventoryNavManager.UpdateInputSummaryOnDrag(true)
        Module.CommonWidget:SetShouldTryFocusPostDrag(true)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    end
    --END MODIFICATION
end

function ********************************:_OnGlobalItemDrop()
    Module.CommonWidget:UnregisterAutoSnapArea(self._wtAutoSnapArea)
    local swapItem = Module.CommonWidget:GetSelectedSwapItem()
    if swapItem then
        InventoryNavManager.FocusByItem(swapItem,0.1)
    end
    
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        Module.CommonWidget:SetShouldTryFocusPostDrag(false)
    end
    --END MODIFICATION
end

function ********************************:_OnGlobalItemDragCancelled()
    Module.CommonWidget:UnregisterAutoSnapArea(self._wtAutoSnapArea)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        Module.CommonWidget:SetShouldTryFocusPostDrag(false)
    end
    --END MODIFICATION
end

function  ********************************:_OnEquipWareHouseFocusReceived()
    self:_DisableDynamicNavConfig()
    
    -- NoA导航影响拖拽放下，不好通过调用OnClicked模拟
    if Module.CommonWidget:GetIsDragging() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default,self)
    else
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction,self)
    end

end

function ********************************:CloseMainPanel()
    if self._bProcessing then
        logerror("********************************:CloseMainPanel 快速配置应用中，返回失败！！！")
    else
        Facade.UIManager:CloseUI(self)
    end
end

return ********************************