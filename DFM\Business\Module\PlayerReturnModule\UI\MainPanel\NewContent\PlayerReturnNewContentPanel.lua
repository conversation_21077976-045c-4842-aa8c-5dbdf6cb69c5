----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

local IPlayerReturnSubActivityPanel = require "DFM.Business.Module.PlayerReturnModule.UI.SubPanels.IPlayerReturnSubActivityPanel"
local IPlayerReturnSubActivity      = require "DFM.Business.Module.PlayerReturnModule.SubActivities.IPlayerReturnSubActivity"
local NavigationAgent               = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local PlayerReturnConfig            = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"
local PlayerReturnStatistics        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnStatistics"
local SubUIInfo                     = require "DFM.Business.DataStruct.UIDataStruct.SubUIInfo"
local WidgetUtil                    = require "DFM.YxFramework.Util.WidgetUtil"
local UDFNavigationSelectorBase     = import("DFNavigationSelectorBase")
local AnimManager                   = require "DFM.Business.DataStruct.Common.Base.DFUtil.AnimManager"
local InputBindingAgent             = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local CallFilter                    = require "DFM.Business.DataStruct.Common.Base.CallFilter"

---@class PlayerReturnNewContentPanel: IPlayerReturnSubActivityPanel, HasAnimManager
local PlayerReturnNewContentPanel = ui("PlayerReturnNewContentPanel", IPlayerReturnSubActivityPanel)

local function SetLastExpandedTaskGroupIdx(i)
    Module.PlayerReturn.Field._newContentPanel.LastExpandedTaskGroup = i
end

local function GetLastExpandedTaskGroup()
    return Module.PlayerReturn.Field._newContentPanel.LastExpandedTaskGroup
end

function PlayerReturnNewContentPanel:UpdateDisplay()
    ---@type PlayerReturnNewContentImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeNewContent, self._armedForceMode)
    local activityInfo = actv:GetActivityInfo(self._armedForceMode)
    if not activityInfo then return end

    local groupInfos = actv:GetTaskGroupInfo(self._armedForceMode)
    local activityID = activityInfo.actv_id
    self._activityID = activityID
    self._cachedGroupInfos = groupInfos
    self._wtTitle:SetText(actv:GetActivityInfo().name)

    -- 调整全新内容分组控件数量
    while #groupInfos > #self._wtTaskGroupListWidgets do
        table.insert(self._wtTaskGroupListWidgets, SubUIInfo.AddSubUI(self, UIName2ID.PlayerReturnNewContentItem, self._wtTaskGroupList))
    end
    while #groupInfos < #self._wtTaskGroupListWidgets do
        table.remove(self._wtTaskGroupListWidgets):Remove()
    end

    -- 刷新内容
    self._actv_id = activityID
    for groupIdx, groupInfo in ipairs(groupInfos) do 
        self._wtTaskGroupListWidgets[groupIdx]:GetIns()._playerReturnNewContentPanel = self
        self._wtTaskGroupListWidgets[groupIdx]:GetIns():SetData(activityID, groupIdx, groupInfo, false, actv:GetRedDotKeyForGroup(groupIdx))
    end

    -- 之前有展开的任务组，保留展开状态
    local groupIdx = GetLastExpandedTaskGroup()
    if (groupIdx) and
       (groupIdx > 0) and 
       (groupIdx <= #self._wtTaskGroupListWidgets) then 
        self._wtTaskGroupListWidgets[groupIdx]:GetIns():SetExpanded(true)
        self:SetNewContentDisplay(groupIdx)
    else
        --默认展开第一个
        self._wtTaskGroupListWidgets[1]:GetIns():SetExpanded(true)
        self:SetNewContentDisplay(1)
    end

    -- 全部领取按钮启用/禁用
    self._wtCollectAllBtn:SetBtnEnable(actv:HasUnclaimedReward())
end

function PlayerReturnNewContentPanel:OnCarouselDrag(_, dragDelta)
    -- ?
end

function PlayerReturnNewContentPanel:OnCarouselFlick(angle)
    if angle < 45 or angle > 315 then
        -- flick left to right, show previous picture
        self:_OnRightStickLeft_CarouselPrev()
    end

    if 135 < angle and angle < 225 then
        -- flick right to left, show next picture
        self:_OnRightStickRight_CarouselNext()
    end
end

function PlayerReturnNewContentPanel:OnCarouselWheel(delta, _)
    if delta > 0 then
        -- wheel up, show previous picture
        self:_OnRightStickLeft_CarouselPrev()
    end

    if delta < 0 then
        -- wheel down, show next picture
        self:_OnRightStickRight_CarouselNext()
    end
end


function PlayerReturnNewContentPanel:Ctor()
    self._cachedGroupInfos = {}
    
    self._switchImageCooldown = CallFilter.Interval(0.4)

    self._wtGesture = self:Wnd("WBP_Gesture", UIWidgetBase)
    self._wtGesture:Event("OnFlick", self.OnCarouselFlick, self)
    self._wtGesture:Event("OnDrag", self.OnCarouselDrag, self)
    self._wtGesture:Event("OnWheel", self.OnCarouselWheel, self)

    self._animMgr = AnimManager.Create(self)
    self._armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    self._navMgr = NavigationAgent.Create(self)
    self._inputMgr = InputBindingAgent.New(self)

    self._wtFrontImage = self:Wnd("CDNImage_Front", DFCDNImage)
    self._wtBackImage = self:Wnd("CDNImage_Back", DFCDNImage)
    self._wtActiveImage = self._wtFrontImage
    self._activeImageURL = nil

    -- 绑定和私有变量
    self._wtTextScroll = self:Wnd("DFScrollBox_0"    , UIWidgetBase)
    self._wtTaskGroupList = self:Wnd("DFScrollBox_1"    , UIWidgetBase)
    self._wtCollectAllBtn = self:Wnd("WBP_DFCommonButtonV2S2"   , DFCommonButtonOnly)
    self._wtNewContentTitleText = self:Wnd("NewContentTitleText", UIWidgetBase)
    self._wtNewContentDescText = self:Wnd("NewContentDescText", UIWidgetBase)
    self._wtTaskGroupListWidgets = {} ---@type SubUIInfo[]

    -- 这个按钮仅在 HD 端用于诱导焦点到文本框，从而允许右摇杆滚动
    -- Mobile 端该按钮会截获触摸操作，使拖动滚动失效
    self._wtTextFocusEnableBtn = self:Wnd("TextFocusEnableBtn", UIWidgetBase)
    if not IsHD() then
        self._wtTextFocusEnableBtn:HitTestInvisible()
    end

    self._wtCollectAllBtn:Event("OnClicked", self.OnCollectAllBtn, self)

    self._wtCarouselKeyIcon_HD           = self:Wnd("CarouselKeyIcon_2"      , HDKeyIconBox)       
    self._wtCarouselButtonsRoot          = self:Wnd("wtTabBox_1"             , UIWidgetBase)       --显示器里面图片切换按钮根节点 (默认关闭)
    -- self._wtMonitorButton                = self:Wnd("MonitorButton"          , UIButton)           --显示器内图片按钮
    self._wtMonitorKeyIcon_HD            = self:Wnd("CarouselKeyIcon_1"      , InputSummaryItemHD) --显示器右下按键提示
    self._carouselImages = {} ---@type string[]
    self._currCarouselIndex = 0

    self._fMonitorKeyIconCallback        = nil
    self._carouselButtons                = {} ---@type SubUIInfo[]
    if IsHD() then
        self._wtCarouselKeyIcon_HD:Collapsed()
        self._wtCarouselButtonsRoot:Collapsed()
    end
    -- self._wtMonitorButton:Event("OnClicked", self.OnMonitorButton, self)

    
    self._inputMgr:AddBinding(
        "CarouselPrev",
        {
            actionName = "Common_RightStickLeft_Gamepad",
            callback   = self._OnRightStickLeft_CarouselPrev,
            caller     = self,
        },
        true -- initially disabled
    )

    self._inputMgr:AddBinding(
        "CarouselNext",
        {
            actionName = "Common_RightStickRight_Gamepad",
            callback   = self._OnRightStickRight_CarouselNext,
            caller     = self,
        },
        true -- initially disabled
    )

    self._wtCollectAllBtn:SetDisplayInputAction("Common_ButtonLeft", nil, nil, true)
    self._inputMgr:AddBinding(
        "CollectAll",
        {
            actionName = "Common_ButtonLeft",
            callback   = self.OnCollectAllBtn,
            caller     = self,
        },
        true -- initially disabled
    )

    self._inputMgr:AddBinding(
        "RightStickY",
        {
            actionName = "Common_RightStickY_Gamepad",
            callback = self.OnRightStickYAxis,
            caller = self,
            isAxis = true,
        },
        true
    )
end

function PlayerReturnNewContentPanel:OnRightStickYAxis(axisValue)
    if axisValue == 0 then return end

    local SCROLL_SPEED = 20

    local offCurr = self._wtTextScroll:GetScrollOffset()
    local offEnd = self._wtTextScroll:GetScrollOffsetOfEnd()
    local offDesired = math.clamp(0, offCurr + axisValue*SCROLL_SPEED, offEnd)

    self._wtTextScroll:SetScrollOffset(offDesired)
end

function PlayerReturnNewContentPanel:OnOpen()
    -- 初始化界面
    self._wtCollectAllBtn:SetMainTitle(PlayerReturnConfig.Localization.ClaimAllRewards)
    self:UpdateDisplay()

    -- 监听任务变化
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTaskChange, self.OnActivityChange, self)
end


function PlayerReturnNewContentPanel:OnActivityChange(activityID)
    if activityID ~= self._activityID then return end
    LogUtil.LogInfo("PlayerReturn: NewContentPanel OnActivityUpdate")
    self:UpdateDisplay()
end

function PlayerReturnNewContentPanel:OnClose()
    -- 清空
    while #self._wtTaskGroupListWidgets > 0 do
        table.remove(self._wtTaskGroupListWidgets):Remove()
    end

    while #self._carouselButtons > 0 do
        local button = table.remove(self._carouselButtons)
        button:Remove()
    end
    
    self:RemoveAllLuaEvent()
end

function PlayerReturnNewContentPanel:OnCollectAllBtn()
    Server.ActivityServer:SendOneClickRewardClaimReq(self._actv_id)
end

-- 任务组展开互斥，以及中间详情页显示，由PlayerReturnNewContentItem处理完来自用户操作的展开事件后调用
function PlayerReturnNewContentPanel:OnTaskListActivated(expandedSubUI)
    for idx, wtTaskGroup in ipairs(self._wtTaskGroupListWidgets) do

        -- 互斥
        if not (wtTaskGroup:GetIns() == expandedSubUI) then
            wtTaskGroup:GetIns():SetExpanded(false)
        end

        -- 顺便记录激活的任务组索引，查找详情页展示信息要用
        if expandedSubUI == wtTaskGroup:GetIns() then
            SetLastExpandedTaskGroupIdx(idx)
            self:SetNewContentDisplay(idx)
        end
    end
    -- self:PlayAnimThen(self.WBP_Reflow_Task02_switch_02)
end

function PlayerReturnNewContentPanel:UpdateLastExpandedTaskGroupWhenInvalid()
    local groupIdx = GetLastExpandedTaskGroup() or 1
    if groupIdx == 0 then
        groupIdx = 1
    end
    SetLastExpandedTaskGroupIdx(groupIdx)
    return groupIdx
end

function PlayerReturnNewContentPanel:OnPlayerReturnEnter(from)
    IPlayerReturnSubActivityPanel.OnPlayerReturnEnter(self, from) -- call super

    self:UpdateDisplay()
    
    local groupIdx = self:UpdateLastExpandedTaskGroupWhenInvalid()
    self:SetNewContentDisplay(groupIdx)

    -- -- 直接跳转进入而非面板切换进入，则不播放动画
    -- if from then
    --     self:GetParent():PlayMonitorGlitchAnim()
    -- end

    if IsHD() then
        self._navMgr:CreateGroup({
            id              = "ContentList",
            rootWidget      = self._wtTaskGroupList,
            members         = {self._wtTaskGroupList},
            bStack          = true,
            bShowSelector   = true,
        })
        self._deferredOnShow = function()
            self._navMgr:FocusWidget(self._wtTaskGroupListWidgets[groupIdx]:GetIns()._wtExpandToggle, true)
        end

        self._inputMgr:Activate("CarouselPrev")
        self._inputMgr:Activate("CarouselNext")
        self._inputMgr:Activate("CollectAll")
        self._inputMgr:Activate("RightStickY")
    end
end

function PlayerReturnNewContentPanel:OnShow()
    if self._deferredOnShow then self._deferredOnShow() end
    self._deferredOnShow = nil
end

function PlayerReturnNewContentPanel:OnPlayerReturnLeave(to)
    IPlayerReturnSubActivityPanel.OnPlayerReturnLeave(self, to) -- call super

    self:SetCarouselImages({})
    
    if IsHD() then
        self._navMgr:RemoveAllGroups()
    end
    self._inputMgr:DeactivateAll()
end

-- 设置当前展示的全新内容项目
function PlayerReturnNewContentPanel:SetNewContentDisplay(taskgroupIdx)
    local groupInfo = self._cachedGroupInfos[taskgroupIdx]
    if not groupInfo then return end

    -- Title
    local title   = groupInfo.category
    self._wtNewContentTitleText:SetText(title)

    -- Message
    local desc = self:_PreprocessText(groupInfo.descList[1])
    -- if not VersionUtil.IsShipping() then
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    --     desc = desc.."\n非Shipping测试滚动"
    -- end
    self._wtNewContentDescText:SetText(desc)

    -- Image and video poster
    if self:IsUIActive() then
        -- local parent = self:GetParent()
        -- parent:SetMonitorGroupVisibility(true)
        -- parent:SetMonitorButtonVisibility(true)
        -- parent:SetCarouselEnable(true)
        
        if groupInfo.video and groupInfo.video ~= "" then
            self:SetCarouselImages({groupInfo.imageurl})
        else
            self:SetCarouselImages(groupInfo.images)
            local images = groupInfo.images
        end
    end
end


function PlayerReturnNewContentPanel:_PreprocessText(text)
    local text = text or ""
    local messageProcessed = text
    messageProcessed = string.gsub(messageProcessed, "\\n", "\n") --换行特殊处理，后台不支持需要前台替换，"\\n"表示换行
    messageProcessed = string.gsub(messageProcessed, "^%$", "    ") --字符串首的空格处理，字符串"$"开头表示需要首段空格
    return messageProcessed
end

function PlayerReturnNewContentPanel:_OnCarouselChanged(idx)
    self._carouselIndex = idx
    local groupIdx = GetLastExpandedTaskGroup()
    if groupIdx then
        local groupInfo = self._cachedGroupInfos
        if groupInfo and groupInfo[groupIdx] then
            self._wtNewContentDescText:SetText(self:_PreprocessText(groupInfo[groupIdx].descList[idx]))
        end
    end
end

function PlayerReturnNewContentPanel:OnTaskListDeactivated(collapsedSubUI)
    SetLastExpandedTaskGroupIdx(0)
    self._wtTaskGroupList:ScrollToOffset(0)
end

-- 展示相关
function PlayerReturnNewContentPanel:OnMonitorButton()
    local groupIdx = GetLastExpandedTaskGroup()
    local taskGroupInfo = self._cachedGroupInfos[groupIdx]
    if not taskGroupInfo then return end

    -- 配置的是视频则启动全屏播放器
    local videoKey = taskGroupInfo.video
    if videoKey ~= "" then
        LogUtil.LogInfo("PlayerReturn: Play NewContent video, key = ", videoKey)

        -- 这个统计只要播放器打开就触发
        LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetNewContentDetailsStatID(groupIdx))

        Module.CommonWidget:ShowFullScreenVideoView(videoKey, false, true, nil, nil, 0, 0, true, nil, 
        function()
            -- 这个统计只有视频开始播放才触发
            LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetNewContentVideoPlayStatID(groupIdx))
        end)

        return
    else
        -- 配置的是图片
        -- 不再做处理，已删除全屏查看大图功能
    end
end

function PlayerReturnNewContentPanel:ChangeImageDisplay(imageURL, bImmediately, ignorePreviousState)
    if self._activeImageURL == imageURL and (not ignorePreviousState) then return end

    local url = string.format("Resource/Texture/Activity/%s", imageURL)
    self._activeImageURL = imageURL

    local anim
    local inactiveImage
    if self._wtActiveImage == self._wtFrontImage then
        anim = self.WBP_Reflow_Task02_switch_back
        inactiveImage = self._wtBackImage
    else
        anim = self.WBP_Reflow_Task02_switch_front
        inactiveImage = self._wtFrontImage
    end
    self._wtActiveImage, inactiveImage = inactiveImage, self._wtActiveImage
    self._wtActiveImage:SetCDNImage(url, false, Module.CDNIcon.Config.ECdnTagEnum.Activity)

    if bImmediately then
        self._wtActiveImage:SelfHitTestInvisible()
        self._wtActiveImage:SetRenderOpacity(1.00)
        inactiveImage:Collapsed()
    else
        self:PlayAnimThen(anim)
    end
end

---@param images string[] URLs
function PlayerReturnNewContentPanel:SetCarouselImages(images)
    self._carouselImages = images
    self:SwitchImage(1, true, true)

    local count = #images
    for _, button in pairs(self._carouselButtons) do
        button:GetIns():RemoveEvent("OnHovered")
    end

    if count < 2 then
        self._wtCarouselButtonsRoot:Collapsed()
        if IsHD() then self._wtCarouselKeyIcon_HD:Collapsed() end
        return
    end

    self._wtCarouselButtonsRoot:SelfHitTestInvisible()
    
    if IsHD() then
        self._wtCarouselKeyIcon_HD:SelfHitTestInvisible()
        self._wtCarouselKeyIcon_HD:SetOnlyDisplayOnGamepad(true)
        self._wtCarouselKeyIcon_HD:InitByDisplayInputActionName("Common_RightStickX_Gamepad", true, 0, false)
        self._wtCarouselKeyIcon_HD:Visible()
    end

    -- 匹配按钮控件数量，绑定事件
    while #self._carouselButtons < count do
        local button = SubUIInfo.AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtCarouselButtonsRoot)
        table.insert(self._carouselButtons, button)
    end
    while #self._carouselButtons > count do
        local button = table.remove(self._carouselButtons)
        button:GetIns():Remove()
    end
    for i = 1, count do
        local carouselIdx = i - 1 -- Lua index to C index
        self._carouselButtons[i]:GetIns():Event("OnHovered", CreateCallBack(self._OnCarouselButtonHovered, self, carouselIdx))
    end

    self:SwitchImage(1, true)
end

function PlayerReturnNewContentPanel:SwitchImage(index, bImmediately, ignorePreviousState)
    if (not self._switchImageCooldown:Check(true)) and (not bImmediately) and (not ignorePreviousState) then return end

    self:ChangeImageDisplay(self._carouselImages[index], bImmediately, ignorePreviousState)
    self._currCarouselIndex = index
    self:_OnCarouselChanged(index)
    for i, widget in pairs(self._carouselButtons) do
        if i == index then
            widget:GetIns():SetStyle(1)
        else
            widget:GetIns():SetStyle(0)
        end
    end
end

function PlayerReturnNewContentPanel:_OnCarouselButtonHovered(index)
    index = index + 1
    self:SwitchImage(index, true)
end

function PlayerReturnNewContentPanel:_OnRightStickLeft_CarouselPrev()
    if self._currCarouselIndex - 1 > 0 then
        self:SwitchImage(self._currCarouselIndex -1, true)
    else
        self:SwitchImage(#self._carouselImages, true)
    end
end

function PlayerReturnNewContentPanel:_OnRightStickRight_CarouselNext()
    if self._currCarouselIndex + 1 <= #self._carouselImages then
        self:SwitchImage(self._currCarouselIndex + 1, true)
    else
        self:SwitchImage(1, true)
    end
end

return PlayerReturnNewContentPanel
