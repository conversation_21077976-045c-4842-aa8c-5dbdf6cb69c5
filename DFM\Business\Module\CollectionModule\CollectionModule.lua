----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class CollectionModule : ModuleBase
local CollectionModule = class("CollectionModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CollectionEventLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionEventLogic"
local CollectionLoadLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLoadLogic"
function CollectionModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function CollectionModule:OnInitModule()
    CollectionEventLogic.AddListeners()
    Module.Collection.Field:GenTabInfo()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function CollectionModule:OnLoadModule()
end
--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function CollectionModule:OnUnloadModule()
end
--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function CollectionModule:OnDestroyModule()
    CollectionEventLogic.RemoveListeners()
    self:CloseMainPanel()
end
---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function CollectionModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- CollectionEventLogic.RemoveLobbyListeners()
    end
end
---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function CollectionModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- CollectionEventLogic.AddLobbyListeners()
    end
    if gameFlowType == EGameFlowStageType.ModeHallToSafeHouse or gameFlowType == EGameFlowStageType.ModeHallToLobby then
        Server.CollectionServer:FetchCamouflageStatusInfo() 
    end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function CollectionModule:OnLoadingLogin2Frontend(gameFlowType)
    CollectionLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function CollectionModule:OnLoadingGame2Frontend(gameFlowType)
    CollectionLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function CollectionModule:OnLoadingFrontend2Game(gameFlowType)
    CollectionLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------
function CollectionModule:ShowMainPanel(tabID)
    return CollectionLogic.ShowMainPanelProcess(tabID)
end
function CollectionModule:CloseMainPanel()
    return CollectionLogic.CloseMainPanelProcess()
end
function CollectionModule:DoSomeThing(...)
    return CollectionLogic.DoSomeThingProcess(...)
end
function CollectionModule:Jump()
    local openUINavIdList = {} -- 跳转接口返回 按顺序实际需要打开/重新显示的所有界面id
    self:ShowMainPanel()
    table.insert(openUINavIdList, UIName2ID.CollectionMainPanel)
    return openUINavIdList
end

-- 获得已拥有蓝图
function CollectionModule:GetOwnedBlueprintItems()
    return CollectionLogic.GetOwnedBlueprintItems()
end

function CollectionModule:ShowWeaponSkinDetailPage(item, hidePopUpCallback, bAutoShowPresetParts, extraSkinItem)
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item, hidePopUpCallback, bAutoShowPresetParts, extraSkinItem)
end

function CollectionModule:ShowHangingDetailPage(item, hidePopUpCallback, bAutoShowPresetParts, extraSkinItem)
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionHangingDetailPage, nil, nil, item, hidePopUpCallback, bAutoShowPresetParts, extraSkinItem)
end

function CollectionModule:ShowCommonVideoListView(videoNames, videoDescs, videoPaths)
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionCommonVideoListView, nil, nil, videoNames, videoDescs, videoPaths)
end

function CollectionModule:ApplyWeaponSkin(itemStruct)
    local skinDataRow = CollectionLogic.GetWeaponSkinDataRow(itemStruct.id)
    if skinDataRow and skinDataRow.BaseWeaponId then
        CollectionLogic.ApplyWeaponSkin(skinDataRow.BaseWeaponId, itemStruct.id, itemStruct.gid)
    end
end

function CollectionModule:EquipPendant(itemStruct)
    --CollectionLogic.EquipPendant(weaponId, itemStruct.id, itemStruct.gid)
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionHangingDefaultPanel, nil,self, itemStruct.id, itemStruct.gid)
end

-- 添加皮肤到合成池按钮
function CollectionModule:AddWorkshopItemToTargetPool(itemStruct)
    Module.Collection.Config.Events.evtOnAddWorkshopItemToTargetPool:Invoke(itemStruct)
end

-- 从合成池移出皮肤按钮
function CollectionModule:RemoveWorkshopItemFromTargetPool(itemStruct)
    Module.Collection.Config.Events.evtOnRemoveWorkshopItemFromTargetPool:Invoke(itemStruct)
end

function CollectionModule:RemoveRedDots(items)
    CollectionLogic.RemoveRedDots(items)
end


--打开改名卡界面(正常打开不传isJump)
function CollectionModule:OpenReNameCard(isJump)
    if isJump then
        local nick = Module.Collection.Field:JumpBuyRecharge()
        if nick then
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionChangeNameView, nil, nil, nick)
            Facade.UIManager:AsyncShowUI(UIName2ID.BuyReNameCardView)
        end
        return
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionChangeNameView)
end

function CollectionModule:GetMandelBrickIdByPendantId(pendantId)
    return CollectionLogic.GetMandelBrickIdByPendantId(pendantId)
end

function CollectionModule:GetMandelBrickIdBySkinId(skinID)
    return CollectionLogic.GetMandelBrickIdBySkinId(skinID)
end

function CollectionModule:ShowMandelBrickPageByPendantID(pendantId)
    local manderBrick = CollectionLogic.GetMandelBrickIdByPendantId(pendantId)
    CollectionLogic.ShowMandelBrickPage(manderBrick, 1)
end

function CollectionModule:ShowMandelBrickPageBySkinID(skinID)
    local manderBrick = CollectionLogic.GetMandelBrickIdBySkinId(skinID)
    CollectionLogic.ShowMandelBrickPage(manderBrick)
end

function CollectionModule:ShowMandelBrickPage(mandelBrickItemID)
    CollectionLogic.ShowMandelBrickPage(mandelBrickItemID)
end

function CollectionModule:ShowMarketPage()
    CollectionLogic.ShowMarketPage()
end

function CollectionModule:ShowMysticalWorkshopPage()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionMysticalSkinWorkshopPage, nil, nil)
end

function CollectionModule:GetWeaponSkinsCarring()
    return CollectionLogic.GetWeaponSkinsCarring()
end

function CollectionModule:OpenRandomSkinUI(weaponID)
    CollectionLogic.OpenRandomSkinUI(weaponID)
end

function CollectionModule:CheckAreAnySkinsInRandomPool(weaponID)
    return CollectionLogic.CheckAreAnySkinsInRandomPool(weaponID)
end

function CollectionModule:CheckIsRandomPoolEnabled(weaponID)
    return CollectionLogic.CheckIsRandomPoolEnabled(weaponID)
end

function CollectionModule:EnableRandomSkin(weaponID, bEnable, fCustomCallback)
    return CollectionLogic.EnableRandomSkin(weaponID, bEnable, fCustomCallback)
end

function CollectionModule:GetExpireTimeTextByTimestamp(expireTimestamp)
    return CollectionLogic.GetExpireTimeTextByTimestamp(expireTimestamp)
end

function CollectionModule:GetMandelBrickTopPrizeItemId(mandelBrickId)
    return CollectionLogic.GetMandelBrickTopPrizeItemId(mandelBrickId)
end

function CollectionModule:GetProcessedPropExpireInfo(propId)
    return CollectionLogic.GetProcessedPropExpireInfo(propId)
end

-- bShow 是否显示所有图层
-- eCdnTagEnum CDN内容枚举值
-- CDNPathPrefix CDN资源路径
-- CDNImagePaths CDN图片ID(路径)数组
-- imageWidgets 蓝图中的CDN图片组件数组
-- transformBoxWidget 用于设置特定CDN图片组件的偏移量的容器
-- imageTransforms CDN图片transform参数(目前只用到一个)
function CollectionModule:SetBackgroundImg(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, CDNImagePaths, transformBoxWidget, imageTransforms)
    CollectionLogic.SetBackgroundImg(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, CDNImagePaths, transformBoxWidget, imageTransforms)
end

-- bShow 是否显示所有图层
-- eCdnTagEnum CDN内容枚举值
-- CDNPathPrefix CDN资源路径
-- propId 道具ID
-- imageWidgets 蓝图中的CDN图片组件数组
-- transformBoxWidget 用于设置特定CDN图片组件的偏移量的容器
-- imageTransforms CDN图片transform参数(目前只用到一个)， 不填则尝试从Gameitem表对应字段获取
function CollectionModule:SetBackgroundImgByPropId(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, propId, transformBoxWidget, imageTransforms)
    CollectionLogic.SetBackgroundImgByPropId(imageWidgets, bShow, eCdnTagEnum, CDNPathPrefix, propId, transformBoxWidget, imageTransforms)
end

return CollectionModule
