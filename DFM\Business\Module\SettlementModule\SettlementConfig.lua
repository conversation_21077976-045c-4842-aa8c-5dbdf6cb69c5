----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------

local SettlementDefine = require "DFM.Business.DataStruct.SettlementStruct.SettlementDefine"

--- RaidSettlement Start-----------------
--- RaidSettlement Start-----------------

UITable[UIName2ID.FailTipRaid] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidGame.SettlementFailTipRaid",
    BPKey = "WBP_RaidFail"
}

UITable[UIName2ID.WinTipRaid] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidGame.SettlementWinTipRaid",
    BPKey = "WBP_RaidSuccess"
}

UITable[UIName2ID.EvaluateRaid] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidGame.SettlementEvaluateRaid",
    BPKey = "WBP_SettlementInfoWin_Raid"
}

--结算结果
UITable[UIName2ID.EvacuateResultTip] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateResultTip",
    BPKey = "WBP_SettlementTips",
    SubUIs = {
        UIName2ID.EvacuateResultTipItem
    }
}

UITable[UIName2ID.EvacuateResultTipItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateResultTipItem",
    BPKey = "WBP_SettlementTipsItem",
    
}

-- azhengzheng:新版结算
UITable[UIName2ID.EvacuationResultInfo] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuationResultInfo",
    BPKey = "WBP_EvacuationResultInfo",
    SubUIs = {
        UIName2ID.EvacuationDecipher,
        UIName2ID.SettlementExpAddTips,
        UIName2ID.SettlementHighLightKillItem
    }
}

UITable[UIName2ID.EvacuationDecipher] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuationDecipher",
    BPKey = "WBP_EvacuationDecipher"
}

--结算信息(资产/经验)
UITable[UIName2ID.EvacuateInfoDetail] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateInfoDetail",
    BPKey = "WBP_SettlementExperience",
    SubUIs = {
        UIName2ID.SettlementExpAddTips
    }
}

UITable[UIName2ID.SettlementKillTips] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementKillTips",
    BPKey = "WBP_SettlementTipsV2",
    SubUIs = {
        UIName2ID.SettlementKillTipsItem
    },
    IsModal = true,
}

UITable[UIName2ID.SettlementKillTipsItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementKillTipsItem",
    BPKey = "WBP_SettlementTipsV2_Item",
}

UITable[UIName2ID.SettlementExpAddTips] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementExpAddTips",
    BPKey = "WBP_SettlementTipsV1",
}

UITable[UIName2ID.EvacuateWinTip] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateWinTip",
    BPKey = "WBP_SettlementTipsWin"
}
UITable[UIName2ID.EvacuateFailTip] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateFailTip",
    BPKey = "WBP_SettlementTipsFail"
}
UITable[UIName2ID.SettlementCutscene2] = {
    UILayer = EUILayer.Root,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementCutscene2",
    BPKey = "WBP_RootSettlementCutscene"
}

UITable[UIName2ID.EvacuateInfoFailViewAdd] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateInfoFailViewAdd",
    BPKey = "WBP_SettlementInfoFail_Add",
    SubUIs = {
        UIName2ID.InvSlotView
    }
}
UITable[UIName2ID.EvacuateInfoWinViewAdd] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateInfoWinViewAdd",
    BPKey = "WBP_SettlementInfoWin_Add"
}

UITable[UIName2ID.EvacuateExpItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateExpItem",
    BPKey = "WBP_SettlementExperienceItemNew"
}

UITable[UIName2ID.EvacuateExpView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateExpView",
    BPKey = "WBP_SettlementExperienceNew",
    SubUIs = {
        UIName2ID.EvacuateExpItem
    }
}

UITable[UIName2ID.EvacuateTeamTrophyInfoView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateTeamTrophyInfoView",
    BPKey = "F_Settlement_TeamBag",
    -- BPKey = "WBP_SettlementEquipmentTeammate",
}

UITable[UIName2ID.EvacuateTeamInfoDetailItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateTeamInfoDetailItem",
    BPKey = "WBP_SettlementTeamDetailItem"
}

UITable[UIName2ID.EvacuateTeamInfoDetailView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateTeamInfoDetailView",
    BPKey = "WBP_SettlementTeamDetail_New",
    SubUIs = {
        UIName2ID.EvacuateTeamInfoDetailItem
    }
}

-- 干员名片S1
UITable[UIName2ID.EvacuateTeamInfoItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateTeamInfoItem",
    BPKey = "WBP_Common_CardS1"
}

UITable[UIName2ID.EvacuateTeamInfoView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuateTeamInfoView",
    BPKey = "WBP_SettlementTeamOver",
    SubUIs = {
        UIName2ID.EvacuateTeamInfoItem
    }
    --[[SubUIs = {
        UIName2ID.EvacuateTeamInfoItem,
        UIName2ID.EvacuateTeamInfoDetailView,
        UIName2ID.EvacuateTeamTrophyInfoView
    }]]
}

UITable[UIName2ID.SettlementGroupTopHD] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementGroupTopHD",
    BPKey = "WBP_SettlementGroupTop_PC"
}

UITable[UIName2ID.EvacuatePrivateTrophyInfoView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuatePrivateTrophyInfoView",
    BPKey = "WBP_SettlementEquipmentSelf",
    SubUIs = {
        UIName2ID.WarehouseEquipPanel,
        UIName2ID.WarehouseWithTab,
        UIName2ID.IVWarehouseTemplate
    }
}

UITable[UIName2ID.EvacuatePrivateTrophyInfoViewHD] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EvacuatePrivateTrophyInfoViewHD",
    BPKey = "WBP_SettlementEquipmentSelf_Pc",
    SubUIs = {
        UIName2ID.InputSummaryItemHD,
        UIName2ID.WarehouseEquipPanel_HD,
        UIName2ID.WarehouseWithTab_HD,
        UIName2ID.IVWarehouseTemplate
    }
}

-- MP武器经验结算界面
--UITable[UIName2ID.WeaponUpgradeDetailPanel] = {
--    UILayer = EUILayer.Pop,
--    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.WeaponUpgradeDetailPanel",
--    BPKey = "WBP_Settlement_WeaponMain"
--}
--
--UITable[UIName2ID.WeaponUpgradeDetailItem] = {
--    UILayer = EUILayer.Sub,
--    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.WeaponUpgradeDetailItem",
--    BPKey = "WBP_Settlement_WeaponDetails",
--    Anim = {
--        FlowInAni = "In_Ani",
--        FlowOutAni = "Out_Ani"
--    }
--}
--
--UITable[UIName2ID.WeaponUpgradeGainItem] = {
--    UILayer = EUILayer.Sub,
--    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.WeaponUpgradeGainItem",
--    BPKey = "WBP_Settlement_WeaponGet"
--}
--
--UITable[UIName2ID.WeaponUpgradeExpItem] = {
--    UILayer = EUILayer.Sub,
--    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.WeaponUpgradeExpItem",
--    BPKey = "WBP_Settlement_WeaponScoreItem"
--}

-- UITable[UIName2ID.WBP_Settlement_Battle_ExperienceDetails] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.ExperienceSettlementPop",
--     BPKey = "WBP_Settlement_Battle_ExperienceDetails",
--     Anim = {
--         FlowInAni = "WBP_Settlement_Battle_ExperienceDetails_in",
--         FlowOutAni = "WBP_Settlement_Battle_ExperienceDetails_out"
--     }
-- }

UITable[UIName2ID.WBP_Settlement_Battle_WeaponsUnlocked] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.UpgradeUnlockOrRewardsPop",
    BPKey = "WBP_Settlement_Battle_WeaponsUnlocked",
}

UITable[UIName2ID.WBP_Settlement_Battle_WeaponsUnlockedltem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.UpgradeRewardItem",
    BPKey = "WBP_Settlement_Battle_WeaponsUnlockedltem",
}

UITable[UIName2ID.WBP_Settlement_Battle_WeaponUpgradesltem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.UpgradeRewardItem",
    BPKey = "WBP_Settlement_Battle_WeaponUpgradesltem",
}

-- UITable[UIName2ID.WBP_Settlement_Battle_WeaponUpgrades] = {
--     UILayer = EUILayer.Pop,
--     LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.WeaponsUpgradePop",
--     BPKey = "WBP_Settlement_Battle_WeaponUpgrades",
--     Anim = {
--         FlowInAni = "WBP_Settlement_Battle_WeaponUpgrades_in",
--         FlowOutAni = "WBP_Settlement_Battle_WeaponUpgrades_out"
--     },
--     IsModal = true,
-- }

UITable[UIName2ID.SolSettlementDetailPanelHD] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SolSettlementDetailPanel",
    BPKey = "WBP_Settlement_Detail_MainUI",
    SubUIs = {
        UIName2ID.SolSettlementDetailKillList,
        UIName2ID.SolSettlementDetailRewardHD,
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.SolSettlementDetailPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SolSettlementDetailPanel",
    BPKey = "WBP_Settlement_Detail_MainUI",
    SubUIs = {
        UIName2ID.SolSettlementDetailReward,
        UIName2ID.SolSettlementDetailKillList,
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.SolSettlementDetailKillList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SolSettlementDetailKillList",
    BPKey = "WBP_SettlementTipsV2_BeatList",
    SubUIs = {
        UIName2ID.CommonEmptyContent,
    },
}


UITable[UIName2ID.SolSettlementDetailRewardHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SolSettlementDetailReward",
    BPKey = "WBP_Settlement_Reward_PC",
    SubUIs = {
        UIName2ID.IVWarehouseTemplate,
    },
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.SolSettlementDetailReward] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SolSettlementDetailReward",
    BPKey = "WBP_Settlement_Reward",
    SubUIs = {
        UIName2ID.IVWarehouseTemplate,
    },
    Anim = {
        bManuelAnim = true
    }
}

------------------------------------------------------------------------大战场 begin--------------------------------------

UITable[UIName2ID.BF_Tips] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BattleFiledTips",
    BPKey = "WBP_Settlement_Battle_Tips"
}

UITable[UIName2ID.BF_Role] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BattleFiledRole",
    BPKey = "WBP_Settlement_Battle_MVPd"
}

UITable[UIName2ID.BF_TeamCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BattleFiledTeamCell",
    BPKey = "WBP_Settlement_Battle_Roled"
}

UITable[UIName2ID.BF_All] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BattleFiledAll",
    BPKey = "WBP_Settlement_Battle_Details_New"
}

UITable[UIName2ID.BF_AllCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BattleFiledAllCell",
    BPKey = "WBP_Settlement_Battle_DetailsItemd"
}

UITable[UIName2ID.SettlementReturnConfirmWindow] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementReturnConfirmWindow",
    BPKey = "WBP_SettlementTipsReturn",
    Anim = {
        bManuelAnim = true
    }
}

UITable[UIName2ID.SettlementReturnBindingTeammateTips] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementReturnBindingTeammateTips",
    BPKey = "WBP_WarehouseDepositSelectAllPanel",
    Anim = {
        FlowInAni = "WBP_WarehouseDepositSelectAllPanel_in",
        FlowOutAni = "WBP_WarehouseDepositSelectAllPanel_out"
    }
}

UITable[UIName2ID.SettlementHighLightKillItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementHighLightKillItem",
    BPKey = "WBP_EvacuationBloom"
}

UITable[UIName2ID.SettlementHighLightAchievement] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.SettlementHighLightAchievement",
    BPKey = "WBP_EvacuationAchievement"
}

------------------------------------------------------------------------大战场 end ----------------------------------------
------------------------------------------------------------------------攻防 begin ----------------------------------------
-- 大战场结算运镜前的黑屏
UITable[UIName2ID.GF_Tips] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BreakthroughTips",
    BPKey = "WBP_Settlement_Battle_Tips"
}
-- 大战场结算运镜过程中
UITable[UIName2ID.GF_CaptureTips] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BreakthroughCaptureTips",
    BPKey = "WBP_BreakthroughSettlementd_Battle"
}
-- 大战场结算运镜后的小队展示
UITable[UIName2ID.BF_Team] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.BattleFiledTeam",
    BPKey = "WBP_Settlement_Battle_Maind"
}
-- 大战场指挥官模式滚动字幕
UITable[UIName2ID.TopTournament] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.TopTournament",
    BPKey = "WBP_Settlement_TopTournament_Main",
    Anim = {
        bManuelAnim = true,
    },
}
UITable[UIName2ID.TopTournamentTeam] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.TopTournamentTeam",
    BPKey = "WBP_Settlement_TopTournament_Team"
}
UITable[UIName2ID.TopTournamentTeamItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.TopTournamentTeamItem",
    BPKey = "WBP_Settlement_TopTournament_TeamItem"
}


------------------------------------------------------------------------攻防 end ----------------------------------------


------------------------------------------------------------------------死亡详情 begin ---------------------------------------------------
--主界面
UITable[UIName2ID.DeathDamageInfoViewNew] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoViewNew",
    BPKey = "WBP_SettlementTipsFailDetail",
    -- SubUIs = {
    --     UIName2ID.EvacuateTeamInfoDetailItem
    -- }
    Anim = {
        
    }
}

UITable[UIName2ID.DeathDamageInfoDetailView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoDetailView",
    BPKey = "WBP_SettlementTipsFailDetailWatch",
    -- SubUIs = {
    --     UIName2ID.EvacuateTeamInfoDetailItem
    -- }
}

UITable[UIName2ID.DeathDamageInfoDetailListView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoDetailListView",
    BPKey = "WBP_SettlementDetail_S1",
    -- SubUIs = {
    --     UIName2ID.EvacuateTeamInfoDetailItem
    -- }
}

UITable[UIName2ID.DeathDamageInfoDetailListViewItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoDetailListViewItem",
    BPKey = "WBP_SettlementDetail_S1Item",
    -- SubUIs = {
    --     UIName2ID.EvacuateTeamInfoDetailItem
    -- }
}

-- long 1.0 迭代
UITable[UIName2ID.DeathDamageInfoDetailListViewItem_V1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoDetailListViewItem_V1",
    BPKey = "WBP_SettlementUnderAttack_V1",
}

UITable[UIName2ID.DeathDamageInfoDetailListViewItem_V2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoDetailListViewItem_V2",
    BPKey = "WBP_SettlementUnderAttack_V2",
}

UITable[UIName2ID.DeathDamageInfoDetailView_Critical] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoDetailView_Critical",
    BPKey = "WBP_SettlementDetail_S2",
    -- SubUIs = {
    --     UIName2ID.EvacuateTeamInfoDetailItem
    -- }
}

UITable[UIName2ID.DeathDamageInfoBodyWidgetItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.DeathDetail.DeathDamageInfoBodyWidgetItem",
    BPKey = "WBP_SettlementFailDetailItem",
    -- SubUIs = {
    --     UIName2ID.EvacuateTeamInfoDetailItem
    -- }
}
------------------------------------------------------------------------死亡详情 end ---------------------------------------------------

---------------------------------------------------------------- 排位赛 ----------------------------------------------------------------
UITable[UIName2ID.SOLRankSettlement] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.Rank.SOLRankSettlement",
    BPKey = "WBP_SettlementSection"
}

UITable[UIName2ID.RankStar] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.Rank.RankStar",
    BPKey = "WBP_SettlementSectionStarBar"
}
---------------------------------------------------------------- 排位赛 ----------------------------------------------------------------

---------------------------------------------------------------- SOL局外结算 ----------------------------------------------------------------
-- 武器解锁
UITable[UIName2ID.SOLUnlockWeapons] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.SOLOutsideSettlement.SOLUnlockWeapons",
    BPKey = "WBP_SettlementUnlocked",
    SubUIs = {
        UIName2ID.SOLUnlockWeaponsBtn
    }
}

UITable[UIName2ID.SOLUnlockWeaponsBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.SOLOutsideSettlement.SOLUnlockWeaponsBtn",
    BPKey = "WBP_SettlementUnlockedBtn"
}
---------------------------------------------------------------- SOL局外结算 ----------------------------------------------------------------

---------------------------------------------------------------- MP局外结算 ----------------------------------------------------------------
UITable[UIName2ID.MPExpSettlement] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPExpSettlement",
    BPKey = "WBP_Settlement_Battle_ExperienceDetails",
    SubUIs = {
        UIName2ID.MPExpTaskItem
    }
}

UITable[UIName2ID.MPExpTaskItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPExpTaskItem",
    BPKey = "WBP_Settlement_Battle_ExperienceDetailsItem",
    SubUIs = {
        UIName2ID.MPExpTaskItem2
    }
}

UITable[UIName2ID.MPExpTaskItem2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPExpTaskItem2",
    BPKey = "WBP_Settlement_Battle_ExperienceDetailsItem2",
}

UITable[UIName2ID.MPExpAndWeaponMain] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPExpAndWeaponMainPanel",
    BPKey = "WBP_Settlement_Battle_MainPanel",
    SubUIs = {
        UIName2ID.MPExpSettlement,
        UIName2ID.MPWeaponsUpgrade,
        UIName2ID.MPVehicleUpgrade,
    },
    IsModal = true,
}

UITable[UIName2ID.MPWeaponsUpgrade] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPWeaponsUpgrade",
    BPKey = "WBP_Settlement_Battle_WeaponUpgrades",
    SubUIs = {
        UIName2ID.SOLUnlockWeaponsBtn
    }
}

UITable[UIName2ID.MPVehicleUpgrade] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPVehicleUpgrade",
    BPKey = "WBP_Settlement_Battle_WeaponUpgrades",
    SubUIs = {
        UIName2ID.SOLUnlockWeaponsBtn
    }
}

-- 武器升级弹窗修复 2025-3-12
-------------------------------------------------
UITable[UIName2ID.MPWeaponUpMain] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPWeapon.MPWeaponUpMain",
    BPKey = "WBP_Settlement_Battle_MainPanel",
    SubUIs = {
        UIName2ID.MPWeaponUpItem,
    },
    IsModal = true,
}

UITable[UIName2ID.MPWeaponUpItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPWeapon.MPWeaponUpItem",
    BPKey = "WBP_Settlement_Battle_WeaponUpgrades",
    SubUIs = {
        UIName2ID.SOLUnlockWeaponsBtn
    }
}
-------------------------------------------------


UITable[UIName2ID.MPGameMedal] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPGameMedal",
    BPKey = "WBP_SettlementBattleScoreMedal"
}

UITable[UIName2ID.MPRankSettlement] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPRankSettlement",
    BPKey = "WBP_SettlementBattle_Integral",
    Anim = {
        bManuelAnim = true
    },
    IsModal = true,
}

UITable[UIName2ID.MPRankSettlementItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPRankSettlementItem",
    BPKey = "WBP_SettlementBattle_IntegralItem"
}

UITable[UIName2ID.MPBigRankUp] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPBigRankUp",
    BPKey = "WBP_BattlePointMatch_RankUpgrade",
    IsModal = true,
}
---------------------------------------------------------------- MP局外结算 ----------------------------------------------------------------

---------------------------------------------------------------- 巅峰赛局外结算 -----------------------------------------------------------------------

UITable[UIName2ID.MPTopTournamentSettlement] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPTopTournamentSettlement",
    BPKey = "WBP_Settlement_TopTournament_Integral",
    SubUIs = {
        UIName2ID.MPTopTournamentSettlementTiTle
    },
    Anim = {
        bManuelAnim = true
    },
    IsModal = true,
}

UITable[UIName2ID.MPTopTournamentSettlementItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPTopTournamentSettlementItem",
    BPKey = "WBP_Settlement_TopTournament_IntegralItem"
}

UITable[UIName2ID.MPTopTournamentSettlementTiTle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPTopTournamentSettlementTiTle",
    BPKey = "WBP_Settlement_TopTournament_IntegralTiTle",
    SubUIs = {
        UIName2ID.MPTopTournamentSettlementItem
    }
}

UITable[UIName2ID.MPCammanderBigRankUp] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.MPOutsideSettlement.MPCammanderBigRankUp",
    BPKey = "WBP_CommanderUpgrades_Main",
    IsModal = true,
}
---------------------------------------------------------------- 巅峰赛局外结算 -------------------------------------------------------------------------------------

---------------------------------------------------------------- Raid新版局内结算 ----------------------------------------------------------------
UITable[UIName2ID.RaidSuccess] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidSuccess",
    BPKey = "WBP_RaidSuccess",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.RaidFail] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidFail",
    BPKey = "WBP_RaidFail",
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.RaidTeamInfo] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidTeamInfo",
    BPKey = "WBP_RaidTeamInfo",
    SubUIs = {
        UIName2ID.RaidTeamInfoItem
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.RaidTeamInfoItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidTeamInfoItem",
    BPKey = "WBP_RaidTeamInfoItem",
}

UITable[UIName2ID.RaidTeamDetailInfo] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidTeamDetailInfo",
    BPKey = "WBP_RaidTeamDetailInfo",
    SubUIs = {
        UIName2ID.RaidTeamInfoItem,
        UIName2ID.RaidTeamDetailInfoItem,
        UIName2ID.RaidTeamDetailInfoLabel
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.RaidTeamDetailInfoItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidTeamDetailInfoItem",
    BPKey = "WBP_RaidTeamDetailInfoItem",
}

UITable[UIName2ID.RaidTeamDetailInfoLabel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.RaidInside.RaidTeamDetailInfoLabel",
    BPKey = "WBP_SettlementInfoTeam_Label",
}
---------------------------------------------------------------- Raid新版局内结算 ----------------------------------------------------------------

---------------------------------------------------------------- Arena(long3) ----------------------------------------------------------------
UITable[UIName2ID.ArenaResult] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaResult",
    BPKey = "WBP_ArenaResult",
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.ArenaResultDetail] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaResultDetail",
    BPKey = "WBP_ArenaResultDetail",
    SubUIs = {
        UIName2ID.ArenaResultDetailItem
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.ArenaResultDetailItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaResultDetailItem",
    BPKey = "WBP_ArenaResultDetailItem",
    SubUIs = {
        UIName2ID.ArenaResultDetailItemGrid
    }
}

UITable[UIName2ID.ArenaResultDetailItemGrid] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaResultDetailItemGrid",
    BPKey = "WBP_ArenaResultDetailItemGrid",
}

UITable[UIName2ID.ArenaTeamInfo] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaTeamInfo",
    BPKey = "WBP_ArenaTeamInfo",
    SubUIs = {
        UIName2ID.ArenaTeamInfoLable
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.ArenaTeamInfoLable] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaTeamInfoLable",
    BPKey = "WBP_ArenaTeamInfoLable"
}

UITable[UIName2ID.ArenaTeamDetailInfo] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaTeamDetailInfo",
    BPKey = "WBP_ArenaTeamDetailInfo",
    SubUIs = {
        UIName2ID.ArenaTeamDetailInfoItem,
        UIName2ID.ArenaTeamDetailInfoLable
    },
    ReConfig = {
        IsPoolEnable = true
    }
}

UITable[UIName2ID.ArenaTeamDetailInfoItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaTeamDetailInfoItem",
    BPKey = "WBP_ArenaTeamDetailInfoItem"
}

UITable[UIName2ID.ArenaTeamDetailInfoLable] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.ArenaInside.ArenaTeamDetailInfoLable",
    BPKey = "WBP_ArenaTeamDetailInfoLable"
}
---------------------------------------------------------------- Arena(long3) ----------------------------------------------------------------

UITable[UIName2ID.EmptyStackUI] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SettlementModule.UI.UpdateSettlement.EmptyStackUI",
    BPKey = "WBP_EmptyStackUI"
}

local SettlementConfig = {


    EObjectiveSource = {
        ObjectiveSource_INVALID     = 0, -- 这个短期内也当做活动
        ObjectiveSource_Activity    = 1, -- 活动
        ObjectiveSource_BattlePass  = 2, -- bp
        ObjectiveSource_Hero        = 3, -- 干员
        ObjectiveSource_Achievement = 4, -- 成就
        ObjectiveSource_Collection  = 5, -- 藏品刷枪皮
        ObjectiveSource_MPDeposit   = 6, -- MP仓库
        ObjectiveSource_Quest   = 7      -- 军械库
    },
    MapPlayerIndex2Color = {
        FLinearColor("80ADD87F"),
        FLinearColor("C2A0747F"),
        FLinearColor("C293F87F"),
        FLinearColor("80AF837F")
    },
    MapMetalId2Icon = {
        "PaperSprite'/Game/UI/UIAtlas/Global/G_Icon/Medal/G_Medal/BakedSprite/G_Icon_Medal_001.G_Icon_Medal_001'",
        "PaperSprite'/Game/UI/UIAtlas/Global/G_Icon/Medal/G_Medal/BakedSprite/G_Icon_Medal_001.G_Icon_Medal_002'",
        "PaperSprite'/Game/UI/UIAtlas/Global/G_Icon/Medal/G_Medal/BakedSprite/G_Icon_Medal_001.G_Icon_Medal_003'",
        "PaperSprite'/Game/UI/UIAtlas/Global/G_Icon/Medal/G_Medal/BakedSprite/G_Icon_Medal_001.G_Icon_Medal_004'"
    },
    EvacuatePropertyTypeIcons = {
        Contract="PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0304.CommonHud_MapMarker_Icon_0304'",
        Props="PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0048.Common_ItemClass_Icon_0048'",
        Cost="PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0001.Common_Card_0001'",

    },
    EvacuateTeamArmedForceIcons = {
        [1]="Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_101.Settlement_Sp_101'",
        [2]="Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_103.Settlement_Sp_103'",
        [3]="Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_104.Settlement_Sp_104'",
        [4]="Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_102.Settlement_Sp_102'",

    },
    AccidentType2AccidentName = {
        [EGspAccidentType.kGspAccidentFall] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentFall", "高处坠落"),
        [EGspAccidentType.kGspAccidentBleeding] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentBleeding", "流血致死"),
        [EGspAccidentType.kGspAccidentTraffic] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentTraffic", "交通事故"),
        [EGspAccidentType.kGspAccidentExplosive] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentExplosive", "爆炸物致死"),
        [EGspAccidentType.kGspAccidentBurn] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentBurn", "燃烧"),
        [EGspAccidentType.kGspAccidentDrowning] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentDrowning", "溺水"),
        [EGspAccidentType.kGspAccidentSkill] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentSkill", "战术装备/道具致死"),
        [EGspAccidentType.kGspAccidentMissing] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentMissing", "意外失踪"),
        [EGspAccidentType.kGspAccidentUnKnown] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspAccidentUnKnown", "意外身亡"),
    },
    --CharacterDamageParts_Leg
    --CharacterDamageParts_LegUpper
    --CharacterDamageParts_Arm
    --CharacterDamageParts_ArmUpper
    --CharacterDamageParts_Head
    --CharacterDamageParts_ThorxUpper
    --CharacterDamageParts_Thorx
    EPartEnum2Name = {
        [1] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart1", "小腿中弹"),
        [2] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart2", "大腿中弹"),
        [3] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart3", "下臂中弹"),
        [4] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart4", "上臂中弹"),
        [5] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart5", "头部中弹"),
        [6] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart6", "腹部中弹"),
        [7] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart7", "胸部中弹"),
        [8] = NSLOCTEXT("SettlementModule", "Lua_Settlement_kGspBodyPart8", "近战淘汰"),
    },
    Loc = {
        Fail = NSLOCTEXT("SettlementModule", "Lua_Settlement_Fail", "撤离失败"),
        KillByPlayer = NSLOCTEXT("SettlementModule", "Lua_Settlement_KillByPlayer", "被%s淘汰"),
        KillBySelf = NSLOCTEXT("SettlementModule", "Lua_Settlement_KillBySelf", "%s误伤淘汰"),
        Suicide = NSLOCTEXT("SettlementModule", "Lua_Settlement_Suicide", "%s误伤淘汰"),
        ClickToSell = NSLOCTEXT("SettlementModule", "Lua_Settlement_ClickToSell", "点选物品出售"),
        CurSellPrice = NSLOCTEXT("SettlementModule", "Lua_Settlement_CurSellPrice", "收益 %d"),
        ContainerCannotBeSoldInSettlement = NSLOCTEXT("SettlementModule", "Lua_Settlement_ContainerCannotBeSoldInSettlement", "容器类道具不允许结算时出售"),
        QuickSellHint = NSLOCTEXT("SettlementModule", "Lua_Settlement_QuickSellHint", "所选物品中包含%s，确认出售吗？"),
        QuickSellHintEtc = NSLOCTEXT("SettlementModule", "Lua_Settlement_QuickSellHintEtc", "所选物品中包含%s等道具，确认出售吗？"),
        DepositoryIsFull = NSLOCTEXT("SettlementModule", "Lua_Settlement_DepositoryIsFull", "仓库已满，部分道具未转运。"),
        RemainingItemsTitle = NSLOCTEXT("SettlementModule", "Lua_Settlement_RemainingItemsTitle", "剩余物品未转移"),
        TeammateProp = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateProp", "队友道具，无法进行操作"),
        RemainingItemsWillMiss = NSLOCTEXT("SettlementModule", "Lua_Settlement_RemainingItemsWillMiss", "剩余总价值为<dfmrichtext type=\"img\" id=\"allBankNote\"/>%d的物品尚未转移到仓库, 如果离开将无法找回!"),
        TeammatesItemShouldBeGive = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammatesItemShouldBeGive", "<dfmrichtext type=\"img\" id=\"TeamMateBind\"/>队友的物品将通过邮件返还给队友"),
        EscapeStillInGameTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapeStillInGameTXT", "游戏中"),
        EscapeSuccessTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapeSuccessTXT", "撤离成功"),
        EscapeFailTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapeFailTXT", "撤离失败"),
        EscapeTimeOutTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapeTimeOutTXT", "行动超时"),
        EscapeMissingTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapeMissingTXT", "行动失踪"),
        ExtractionPoint = NSLOCTEXT("SettlementModule", "Lua_Settlement_ExtractionPoint", "撤离点：%s"),
        
        Killer = NSLOCTEXT("SettlementModule", "Lua_Settlement_Killer", "淘汰者：%s"),
        DeathReason = NSLOCTEXT("SettlementModule", "Lua_Settlement_DeathReason", "淘汰原因："),
        EquipLoss = NSLOCTEXT("SettlementModule", "Lua_Settlement_EquipLoss", "对局结束前未成功撤离，损失身上全部道具"),
        GameDuration = NSLOCTEXT("SettlementModule", "Lua_Settlement_GameDuration", "时长：%s"),
        FunctionIsMakingTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_FunctionIsMakingTXT", "点赞成功"),
        ReqFriendTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_ReqFriendTXT", "已发送好友请求"),
        ItemTransferSuccess = NSLOCTEXT("SettlementModule", "Lua_Settlement_ItemTransferSuccess", "战利品已成功转移到仓库"),
        ComminSoon = NSLOCTEXT("SettlementModule", "Lua_Settlement_ComminSoon", "功能尚未开发哈"),
        BFSuccess = NSLOCTEXT("SettlementModule", "Lua_Settlement_BFSuccess", "胜利"),
        BFFail = NSLOCTEXT("SettlementModule", "Lua_Settlement_BFFail", "失败"),
        BFTie = NSLOCTEXT("SettlementModule", "Lua_Settlement_BFTie", "平局"),
        BFDraw = NSLOCTEXT("SettlementModule", "Lua_Settlement_BFDraw", "中止"),
        DifficultySimple = NSLOCTEXT("SettlementModule", "Lua_Settlement_DifficultySimple", "低倍场"),
        DifficultyHard = NSLOCTEXT("SettlementModule", "Lua_Settlement_DifficultyHard", "高倍场"),
        RaidSimple = NSLOCTEXT("SettlementModule", "Lua_Settlement_RaidSimple", "[简单难度]"),
        RaidNormal = NSLOCTEXT("SettlementModule", "Lua_Settlement_RaidNormal", "[中等难度]"),
        RaidHard = NSLOCTEXT("SettlementModule", "Lua_Settlement_RaidHard", "[困难难度]"),
        -- BEGIN MODIFICATION - VIRTUOS
        SettlementFail2Retry = NSLOCTEXT("SettlementModule", "Console_Lua_Settlement_SettlementFail2Retry", "三角洲网络繁忙，退出后可在历史战绩查看本局详情，点击重试？"),
        SettlementFail2Retry_Gen9 = NSLOCTEXT("SettlementModule", "Console_Lua_Settlement_SettlementFail2Retry_Sony", "三角洲网络繁忙，退出后可在历史战绩查看本局详情，将返回大厅"),
        -- END MODIFICATION - VIRTUOS
        TeammateItemReturnTips = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemReturnTips", "队友的道具将通过邮件返还给队友"),
        SettlementRetry = NSLOCTEXT("SettlementModule", "Lua_Settlement_SettlementRetry", "重试"),
        Settlement2SafeHouse = NSLOCTEXT("SettlementModule", "Lua_Settlement_Settlement2SafeHouse", "返回特勤处"),
        SOLSettlementTimer = NSLOCTEXT("SettlementModule", "Lua_Settlement_SOLSettlementTimer", "下一步（%d）"),
        Back2SafeHouse = NSLOCTEXT("SettlementModule", "Lua_Settlement_Back2SafeHouse", "回到特勤处（%d）"),
        Back2Lobby = NSLOCTEXT("SettlementModule", "Lua_Settlement_Back2Lobby", "回到大厅（%d）"),
        Back2SafeHouseNoTime = NSLOCTEXT("SettlementModule", "Lua_Settlement_Back2SafeHouseNoTime", "回到特勤处"),
        SettlementTimeRemain = NSLOCTEXT("SettlementModule", "Lua_Settlement_SettlementTimeRemain", "（%d）秒后回到特勤处"),
        Space2NextStep = NSLOCTEXT("SettlementModule", "Lua_Settlement_Space2NextStep", "空格至下一步（%d）"),
        Space2NextStepText = NSLOCTEXT("SettlementModule", "Lua_Settlement_Space2NextStepText", "空格至下一步"),
        Space2JumpAnim = NSLOCTEXT("SettlementModule", "Lua_Settlement_Space2JumpAnim", "空格跳过动画"),
        TabNamePersonal=NSLOCTEXT("SettlementModule", "Lua_Settlement_TabNamePersonal", "个人"),
        KilledDetail=NSLOCTEXT("SettlementModule", "Lua_Settlement_KilledDetail", "淘汰详情"),
        TabNameTeam=NSLOCTEXT("SettlementModule", "Lua_Settlement_TabNameTeam", "队伍"),
        TabNameTrophy=NSLOCTEXT("SettlementModule", "Lua_Settlement_TabNameTrophy", "战利品转移"),
        SettlementException = NSLOCTEXT("SettlementModule", "Lua_Settlement_SettlementException", "上局游戏已经结束，是否查看结果？"),
        SettlementExceptionQuit= NSLOCTEXT("SettlementModule", "Lua_Settlement_SettlementExceptionQuit", "本局游戏刚刚结束，点击确认退出"),
        LongKill = NSLOCTEXT("SettlementModule", "Lua_Settlement_LongKill", "淘汰距离"),
        UseWeapon = NSLOCTEXT("SettlementModule", "Lua_Settlement_UseWeapon", "使用武器"),
        PutTheHigherPriceIntoTheInvFirst = NSLOCTEXT("SettlementModule", "Lua_Settlement_PutTheHigherPriceIntoTheInvFirst", "注：会优先将高价值的道具放入仓库"),
        ReturnToSortOut = NSLOCTEXT("SettlementModule", "Lua_Settlement_ReturnToSortOut", "返回整理"),
        plusSignText = NSLOCTEXT("SettlementModule", "Lua_Settlement_PlusSignText", "+%s"),
        TotalAddText = NSLOCTEXT("SettlementModule", "Lua_Settlement_TotalAddText", "共计 +%s"),
        SymbolColon = NSLOCTEXT("SettlementModule", "Lua_Settlement_SymbolColon", "："),
        ExpAddPercent = NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpAddPercent", "（%d%%）"),
        levelText = NSLOCTEXT("SettlementModule", "Lua_Settlement_LevelText", "LV.%s"),
        expProgressText = NSLOCTEXT("SettlementModule", "Lua_Settlement_expProgressText", "%s/%s"),
        successExp = NSLOCTEXT("SettlementModule", "Lua_Settlement_SuccessExp", "胜利经验"),
        failedExp = NSLOCTEXT("SettlementModule", "Lua_Settlement_FailedExp", "失败经验"),
        dogfallExp = NSLOCTEXT("SettlementModule", "Lua_Settlement_dogfallExp", "平局经验"),
        FirstDeathReturnTips = NSLOCTEXT("SettlementModule", "Lua_Settlement_FirstDeathReturnTips", "你仍然有一次被复活机会，是否退出战斗？"),
        RaidDeathReturnTips = NSLOCTEXT("SettlementModule", "Lua_Settlement_RaidDeathReturnTips", "队友通关后，你仍可以回到战场，是否退出战斗？"),
        KilledHero=NSLOCTEXT("SettlementModule", "Lua_Settlement_KilledHero", "特战干员"),
        KilledEnemy=NSLOCTEXT("SettlementModule", "Lua_Settlement_KilledEnemy", "敌人"),
        KilledEnemyDeltaPMC=NSLOCTEXT("SettlementModule", "Lua_Settlement_KilledEnemyDeltaPMC", "三角洲"),
        KilledEnemyDarkTide=NSLOCTEXT("SettlementModule", "Lua_Settlement_KilledEnemyDarkTide", "阿萨拉"),
        KilledEnemyHudSon=NSLOCTEXT("SettlementModule", "Lua_Settlement_KilledEnemyHudSon", "HAAVK"),
        AddExpByEscapeSuccess=NSLOCTEXT("SettlementModule", "Lua_Settlement_AddExpByEscapeSuccess", "撤离成功：%d"),
        AddExpByEscapeFail=NSLOCTEXT("SettlementModule", "Lua_Settlement_AddExpByEscapeFail", "撤离失败：%d"),
        AddExpByEscapeMissing=NSLOCTEXT("SettlementModule", "Lua_Settlement_AddExpByEscapeMissing", "行动失踪：%d"),
        ExpBattle=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpBattle", "战斗：%d"),
        ExpSearch=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpSearch", "搜索：%d"),
        ExpContract=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpContract", "合同：%d"),
        ExpAction=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpAction", "行动：%d"),
        ExpMandel=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpMandel", "破译曼德尔砖：%d"),
        KAddExpReasonFix=NSLOCTEXT("SettlementModule", "Lua_Settlement_KAddExpReasonFix", "初入战局：%d"),
        ExpTeam=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpTeam", "好友组队加成（%d%%)"),
        ExpSafeHouse=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpSafeHouse", "特勤处加成（%d%%)"),
        TeamAddPercent=NSLOCTEXT("SettlementModule", "Lua_Settlement_TeamAddPercent", "好友组队+%d%%"),
        SafeHouseAddPercent=NSLOCTEXT("SettlementModule", "Lua_Settlement_SafeHouseAddPercent", "特勤处+%d%%"),
        WechatAccountAddPercent=NSLOCTEXT("SettlementModule", "Lua_Settlement_WechatAccountAddPercent", "微信游戏特权+%d%%"),
        CybercafeBuff = NSLOCTEXT("SettlementModule", "CybercafeBuff", "网吧游戏特权+%s%%"),
        QQAccountAddPercent=NSLOCTEXT("SettlementModule", "Lua_Settlement_QQAccountAddPercent", "QQ游戏特权+%d%%"),
        ExpSafeHouseAndTeam=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExpSafeHouseAndTeam", "特勤处+%d%%，好友组队+%d%%"),
        TitleFinishContract=NSLOCTEXT("SettlementModule", "Lua_Settlement_TitleFinishContract", "完成行动"),
        TitleGetProps=NSLOCTEXT("SettlementModule", "Lua_Settlement_TitleGetProps", "带出物资"),
        TitleSafeBoxProps=NSLOCTEXT("SettlementModule", "Lua_Settlement_TitleSafeBoxProps", "保险物资"),
        TitleCost=NSLOCTEXT("SettlementModule", "Lua_Settlement_TitleCost", "本局消耗"),
        LevelNameText=NSLOCTEXT("SettlementModule", "Lua_Settlement_LevelNameText", "等级 %d"),
        PersonalPerformance=NSLOCTEXT("SettlementModule", "Lua_Settlement_PersonalPerformance", "%.0f%%"),
        UpgradeRewards=NSLOCTEXT("SettlementModule", "Lua_Settlement_UpgradeRewards", "升级奖励"),
        AddWeaponExp=NSLOCTEXT("SettlementModule", "Lua_Settlement_AddWeaponExp", "武器经验+%d"),

        -- MP武器经验结算界面
        ColorQuality = NSLOCTEXT("SettlementModule", "Lua_Settlement_ColorQuality", "%s<customstyle color=\"Color_Quality00\">/%s</>"),
        NumberOfVictories = NSLOCTEXT("SettlementModule", "Lua_Settlement_NumberOfVictories", "本局击败数: +{num}"),
        FirearmsStarRating = NSLOCTEXT("SettlementModule", "Lua_Settlement_FirearmsStarRating", "枪械星级: {num}"),
        weaponUnLockParts = NSLOCTEXT("SettlementModule", "Lua_Settlement_WeaponUnLockParts", "解锁配件{Num}个"),
        weaponExpSource = NSLOCTEXT("SettlementModule", "Lua_Settlement_WeaponExpSource", "经验来源"),
        weaponConvertedExpCard = NSLOCTEXT("SettlementModule", "Lua_Settlement_WeaponConvertedExpCard", "经验卡折算"),
        weaponExpText = NSLOCTEXT("SettlementModule", "Lua_Settlement_WeaponExpText", "EXP+%s"),
        EnemyNumNameTextA = NSLOCTEXT("SettlementModule", "Lua_Settlement_EnemyNumNameTextA", "剩余增援"),
        PointNumNameTextA = NSLOCTEXT("SettlementModule", "Lua_Settlement_PointNumNameTextA", "占领区域"),
        EnemyNumNameTextB = NSLOCTEXT("SettlementModule", "Lua_Settlement_EnemyNumNameTextB", "消灭敌军"),
        PointNumNameTextB = NSLOCTEXT("SettlementModule", "Lua_Settlement_PointNumNameTextB", "保卫区域"),
        ScoreRatio = NSLOCTEXT("SettlementModule", "Lua_Settlement_ScoreRatio", "双方票数比"),
        ScoreName = NSLOCTEXT("SettlementModule", "Lua_Settlement_ScoreName", "救援/占领"),
        CaptainName = NSLOCTEXT("SettlementModule", "Lua_Settlement_CaptainName", "军衔"),
        SeasonLvName = NSLOCTEXT("SettlementModule", "Lua_Settlement_SeasonLvName", "行动等级 %d"),
        MandelTips=NSLOCTEXT("SettlementModule", "Lua_Settlement_MandelTips", "你在行动中完成了破译任务经过破译的<customstyle color=\"Color_Highlight01\">%s</>已添加到您的仓库"),
        MaxLevel=NSLOCTEXT("SettlementModule", "Lua_Settlement_MaxLevel", "已满级"),
        Attack=NSLOCTEXT("SettlementModule", "Lua_Settlement_Attack", "进攻"),
        Defend=NSLOCTEXT("SettlementModule", "Lua_Settlement_Defend", "防守"),
        DoneTransfer=NSLOCTEXT("SettlementModule", "Lua_Settlement_DoneTransfer", "已全部转移"),
        DeathDamageInfoView_AttackerTitle01=NSLOCTEXT("SettlementModule", "Lua_DeathDamageInfoView_AttackerTitle01", "命中"),
        DeathDamageInfoView_AttackerTitle02=NSLOCTEXT("SettlementModule", "Lua_DeathDamageInfoView_AttackerTitle02", "来自"),
        DeathDamageInfoView_Bullet=NSLOCTEXT("SettlementModule", "Lua_DeathDamageInfoView_Bullet", "%s级子弹"),
        DeathDamageInfoView_DeathTitle=NSLOCTEXT("SettlementModule", "Lua_DeathDamageInfoView_DeathTitle", "淘汰原因"),
        BreakthroughCaptureTipsA=NSLOCTEXT("SettlementModule", "Lua_BreakthroughCaptureTipsA_DeathTitle", "已占领敌方总部"),
        BreakthroughCaptureTipsB=NSLOCTEXT("SettlementModule", "Lua_BreakthroughCaptureTipsB_DeathTitle", "未占领敌方总部"),
        BreakthroughCaptureTipsC=NSLOCTEXT("SettlementModule", "Lua_BreakthroughCaptureTipsC_DeathTitle", "已保卫我方区域"),
        BreakthroughCaptureTipsD=NSLOCTEXT("SettlementModule", "Lua_BreakthroughCaptureTipsD_DeathTitle", "已沦陷全部区域"),
        ConquestCaptureTipsW=NSLOCTEXT("SettlementModule", "Lua_ConquestCaptureTipsW_DeathTitle", "已控制所有区域"),
        ConquestCaptureTipsF=NSLOCTEXT("SettlementModule", "Lua_ConquestCaptureTipsF_DeathTitle", "已丢失所有区域"),
        FlagBattleFailTips=NSLOCTEXT("SettlementModule", "Lua_FlagBattleFailTips", "夺旗失败"),
        FlagBattleSucceedTips=NSLOCTEXT("SettlementModule", "Lua_FlagBattleSucceedTips", "夺旗胜利"),
        FlagBattleTieTips=NSLOCTEXT("SettlementModule", "Lua_FlagBattleTieTips", "平局"),
        FlagBattleScoreTips=NSLOCTEXT("SettlementModule", "Lua_FlagBattleScoreTips", "比分"),
        TeamMVP=NSLOCTEXT("SettlementModule", "Lua_TeamMVP_DeathTitle", "我的小队"),
        -- azhengzheng:MS23新增
        LowMission = NSLOCTEXT("SettlementModule", "LowMission", "常规行动"),
        MiddleMission = NSLOCTEXT("SettlementModule", "MiddleMission", "机密行动"),
        HighMission = NSLOCTEXT("SettlementModule", "HighMission", "绝密行动"),
        BattlefieldLevel = NSLOCTEXT("SettlementModule", "BattlefieldLevel", "战场等级 %d"),
        CurLevelExpSchedule = NSLOCTEXT("SettlementModule", "CurLevelExpSchedule", "<customstyle Color=\"C001\">%d</><customstyle Size=\"Size_BoldTitle01\">/%d</>"),
        CloseText = NSLOCTEXT("SettlementModule", "CloseText", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/> 关闭"),
        NewContinueText = NSLOCTEXT("SettlementModule", "NewContinueText", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/> 下一步"),
        CloseTextWithTime = NSLOCTEXT("SettlementModule", "CloseTextWithTime", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/> 关闭（%d）"),
        NewContinueTextWithTime = NSLOCTEXT("SettlementModule", "NewContinueTextWithTime", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/> 下一步（%d）"),
        JumpAnimationText = NSLOCTEXT("SettlementModule", "JumpAnimationText", "<dfmrichtext type=\"img\" id=\"KeyIcon_SpaceBar\" align=\"0\"/> 跳过动画"),
        -- azhengzheng:MS24新增
        FinishMission = NSLOCTEXT("SettlementModule", "FinishMission", "完成行动：%s"),
        CarryOutNewPropsPrice = NSLOCTEXT("SettlementModule", "CarryOutNewPropsPrice", "带出物资：{Price}"),
        CostPrice = NSLOCTEXT("SettlementModule", "CostPrice", "本局消耗：%d"),
        RecoveryAmount = NSLOCTEXT("SettlementModule", "RecoveryAmount", "追缴罚款：-%d"),
        BringOutMandelSuccess = NSLOCTEXT("SettlementModule", "BringOutMandelSuccess", "成功破译曼德尔砖"),
        JumpToNextFlow = NSLOCTEXT("SettlementModule", "JumpToNextFlow", "下一步"),
        JumpAnim = NSLOCTEXT("SettlementModule", "JumpAnim", "跳过动画"),
        QuitGame = NSLOCTEXT("SettlementModule", "QuitGame", "中途退出"),
        Quit = NSLOCTEXT("SettlementModule", "Quit", "退出"),
        ReviewSettlement = NSLOCTEXT("SettlementModule", "ReviewSettlement", "查看结果"),
        MakeAppointment = NSLOCTEXT("SettlementModule", "MakeAppointment", "成功发送队友预约"),

---------------------------------------------------------------- 排位赛 ----------------------------------------------------------------
        TabNameRank = NSLOCTEXT("SettlementModule", "TabNameRank", "排位"),
        CurRankStarNum = NSLOCTEXT("SettlementModule", "CurRankStarNum", "×%d"),
        KillAI = NSLOCTEXT("SettlementModule", "KillAI", "击败AI：+%d"),
        KillPlayer = NSLOCTEXT("SettlementModule", "KillPlayer", "击败玩家：+%d"),
        LootingBox = NSLOCTEXT("SettlementModule", "LootingBox", "搜索：+%d"),
        EscapeSuccess = NSLOCTEXT("SettlementModule", "EscapeSuccess", "撤离成功：+%d"),
        EscapeFailScoreUnchanged = NSLOCTEXT("SettlementModule", "EscapeFailScoreUnchanged", "撤离失败：-%d"),
        EscapeMissScoreUnchanged = NSLOCTEXT("SettlementModule", "EscapeMissScoreUnchanged", "行动失踪：-%d"),
        EscapeQuitScoreUnchanged = NSLOCTEXT("SettlementModule", "EscapeQuitScoreUnchanged", "中途退出：-%d"),
        AddExpForQuitGame = NSLOCTEXT("SettlementModule", "AddExpForQuitGame", "中途退出：%d"),
        SOLRankAddExpByContract = NSLOCTEXT("SettlementModule", "SOLRankAddExpByContract", "合同：+%d"),
        SOLRankAddExpByBluePrint = NSLOCTEXT("SettlementModule", "SOLRankAddExpByBluePrint", "曼德尔砖：+%d"),
        SOLRankProtectTip = NSLOCTEXT("SettlementModule", "SOLRankProtectTip", "本次结算不掉分（%s/%s）"),
        SOLRankKillAI = NSLOCTEXT("SettlementModule", "SOLRankKillAI", "击败敌方势力：%s/%s"),
        SOLRankKillPlayer = NSLOCTEXT("SettlementModule", "SOLRankKillPlayer", "击败特战干员：%s/%s"),
        SOLRankLootingBox = NSLOCTEXT("SettlementModule", "SOLRankLootingBox", "搜索：%s/%s"),
        SOLRankContract = NSLOCTEXT("SettlementModule", "SOLRankContract", "合同：%s/%s"),
        SOLRankBluePrint = NSLOCTEXT("SettlementModule", "SOLRankBluePrint", "曼德尔砖：%s/%s"),
        SOLRankCarryOutPrice = NSLOCTEXT("SettlementModule", "SOLRankCarryOutPrice", "带出资产：%s/%s"),
        SOLRankHighQualityPrice = NSLOCTEXT("SettlementModule", "SOLRankHighQualityPrice", "带出高价值道具：%s/%s"),
        SOLRankAssistKillPlayer = NSLOCTEXT("SettlementModule", "SOLRankAssistKillPlayer", "助攻击杀：%s/%s"),
---------------------------------------------------------------- 排位赛 ----------------------------------------------------------------

---------------------------------------------------------------- SOL局外结算 ----------------------------------------------------------------
        SOLUnlockWeaponsTip = NSLOCTEXT("SettlementModule", "SOLUnlockWeaponsTip", "<customstyle color=\"C000\">成功撤离并带出，已在全面战场模式解锁 </>%s"),

        -- Long 1.0
        SOLRankScoreProtect = NSLOCTEXT("SettlementModule", "SOLRankScoreProtect", "段位保护(%s/%s)"),
---------------------------------------------------------------- SOL局外结算 ----------------------------------------------------------------

---------------------------------------------------------------- SOL局内结算 ----------------------------------------------------------------
        ReturnToTheLobby = NSLOCTEXT("SettlementModule", "ReturnToTheLobby", "返回大厅"),
        ReturnToTheLobbyWithTime = NSLOCTEXT("SettlementModule", "ReturnToTheLobbyWithTime", "返回大厅（%d）"),
        Crocodile = NSLOCTEXT("SettlementModule", "Crocodile", "鳄鱼"),
        WildAnimals = NSLOCTEXT("SettlementModule", "WildAnimals", "野外动物"),
---------------------------------------------------------------- SOL局内结算 ----------------------------------------------------------------

---------------------------------------------------------------- MP局外结算 ----------------------------------------------------------------
        MPLevel = NSLOCTEXT("SettlementModule", "MPLevel", "战场等级：%s"),
        MPTotalExp = NSLOCTEXT("SettlementModule", "MPTotalExp", "合计：+%s"),
        WeaponLevel = NSLOCTEXT("SettlementModule", "WeaponLevel", "武器等级：%s"),
        RewardItemTip = NSLOCTEXT("SettlementModule", "RewardItemTip", "获得物品"),
        AbilityScoreAdd = NSLOCTEXT("SettlementModule", "AbilityScoreAdd", "(+%s)"),
        AbilityScoreDec = NSLOCTEXT("SettlementModule", "AbilityScoreDec", "(%s)"),
        MPRankScoreDec = NSLOCTEXT("SettlementModule", "MPRankScoreDec", "合计：%s"),
        MPAlreadyHighestRank = NSLOCTEXT("SettlementModule", "MPAlreadyHighestRank", "已达最高段位"),
        FirstScoreAdd = NSLOCTEXT("SettlementModule", "FirstScoreAdd", "+%s分"),
        FirstScoreDec = NSLOCTEXT("SettlementModule", "FirstScoreDec", "%s分"),
        MPScoreDetailTip = NSLOCTEXT("SettlementModule", "MPScoreDetailTip", "表现分与得分的关系\n局内各个大类下的行为分越高，超出了整体水平时将会加分，超出的越多加分越多。"),
        MPScoreProtectTip = NSLOCTEXT("SettlementModule", "MPScoreProtectTip", "保护（%s/%s）"),
        MPScoreProtectTipNoneNum = NSLOCTEXT("SettlementModule", "MPScoreProtectTipNoneNum", "保护"),
        MPScoreRowDescription = {
            [1001] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc1001", "击败"),
            [1002] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc1002", "伤害"),
            [1003] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc1003", "助攻"),

            [2001] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc2001", "载具击败助攻"),
            [2004] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc2004", "反载击败助攻"),
            [2005] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc2005", "反载伤害"),

            [3101] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3101", "救援"),
            [3102] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3102", "维修"),
            [3104] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3104", "重新部署"),

            [3201] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3201", "夺取据点"),
            [3202] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3202", "占领据点"),
            [3203] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3203", "防守据点"),

            [3301] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3301", "技能战斗"),
            [3302] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3302", "技能侦查"),
            [3303] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPScoreRowDesc3303", "技能后勤"),

        },
---------------------------------------------------------------- MP局外结算 ----------------------------------------------------------------

---------------------------------------------------------------- long 1.0 ----------------------------------------------------------------
        AddScorePercent = NSLOCTEXT("SettlementModule", "AddScorePercent", "积分加成：<customstyle color=\"Color_LightPositive\">%s%%</>"),
        RankedScoreLeavePenalty = NSLOCTEXT("SettlementModule", "RankedScoreLeavePenalty", "中退各能力分值"),
        WeChatAddExpPercentWithIcon = NSLOCTEXT("SettlementModule", "WeChatAddExpPercentWithIcon", "<dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"Wechat\" align=\"0\"/>微信游戏特权+%s%%"),
        DoubleRankScoreRate = NSLOCTEXT("SettlementModule", "DoubleRankScoreRate", "双倍"),
        HighLightKill = NSLOCTEXT("SettlementModule", "HighLightKill", "本局高光击败"),
        -- BEGIN MODIFICATION - VIRTUOS
        HighLightAchievements = NSLOCTEXT("SettlementModule", "Console_HighLightAchievements", "本局解锁成就"),
        -- END MODIFICATION - VIRTUOS
        HighLightKillMsgTB = NSLOCTEXT("SettlementModule", "HighLightKillMsgTB", "%s<customstyle Color=\"C002\"> [%s]</>"),
        HighLightKillMsgTBWithStars = NSLOCTEXT("SettlementModule", "HighLightKillMsgTBWithStars", "%s<customstyle Color=\"C002\"> [%s %s星]</>"),
        BackToLobbyRemainTimeTip = NSLOCTEXT("SettlementModule", "BackToLobbyWithTime", "回到大厅：%s"),
        SOLShareTip = NSLOCTEXT("SettlementModule", "SOLShareTip", "每日首次分享奖励 <dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"allBankNote\"/>%s"),
        MPShareTip = NSLOCTEXT("SettlementModule", "MPShareTip", "每日首次分享奖励 <dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"ExperienceCoins_Law\"/>%s"),
        Ranking = NSLOCTEXT("SettlementModule", "Ranking", "排名"),
        RankingPerMinute = NSLOCTEXT("SettlementModule", "RankingPerMinute", "单位时间排名"),
        DurationCoefficientTip = NSLOCTEXT("SettlementModule", "DurationCoefficientTip", "游玩时间越长，该系数越大，可获得的军功加成越多"),
        LeaveTip = NSLOCTEXT("SettlementModule", "LeaveTip", "如果中途退出对局，将不会得到排名得分"),
        PercentNum = NSLOCTEXT("SettlementModule", "PercentNum", "%s%%"),
        WatchBattle = NSLOCTEXT("SettlementModule", "WatchBattle", "观战"),
---------------------------------------------------------------- long 1.0 ----------------------------------------------------------------

---------------------------------------------------------------- long 2.0 ----------------------------------------------------------------
        Settlement = NSLOCTEXT("SettlementModule", "Settlement", "结算"),
        RaidReturnToTheLobby = NSLOCTEXT("SettlementModule", "RaidReturnToTheLobby", "回到大厅"),
        RaidBattleEvaluation_1 = NSLOCTEXT("SettlementModule", "RaidBattleEvaluation_1", "全队被击败次数不高于%s次"),
        RaidBattleEvaluation_2 = NSLOCTEXT("SettlementModule", "RaidBattleEvaluation_2", "全队带入价值（包含携带空间内物品）不高于%s"),
        RaidBattleEvaluation_3 = NSLOCTEXT("SettlementModule", "RaidBattleEvaluation_3", "通关时长低于{minute}分{second}秒"),
        RaidNoneWorldRecords = NSLOCTEXT("SettlementModule", "RaidNoneWorldRecords", "--:--:--"),
        UniversityBuff = NSLOCTEXT("SettlementModule", "UniversityBuff", "高校特权+%s%%"),
        RaidEvaluateMascot = NSLOCTEXT("SettlementModule", "RaidEvalueteMascot", "吉祥物"),
        RaidFirstPassReward = NSLOCTEXT("SettlementModule", "RaidFirstPassReward", "首通奖励"),
        RaidFirstPassRewardsAndAchievements = NSLOCTEXT("SettlementModule", "RaidFirstPassRewardsAndAchievements", "首通奖励与成就"),
        RaidDailyRewards = NSLOCTEXT("SettlementModule", "RaidDailyRewards", "每日奖励（{curRewardTimes}/{maxRewardTimes}）"),
        RaidDailyRewardsLimit = NSLOCTEXT("SettlementModule", "RaidDailyRewardsLimit", "每日奖励已达上限（{curRewardTimes}/{maxRewardTimes}）"),
        PerformanceScoreTip = NSLOCTEXT("SettlementModule", "PerformanceScoreTip", "根据局内得分/分钟的表现获得军功"),
        MapWithCampBattleResult = NSLOCTEXT("SettlementModule", "MapWithCampBattleResult", "{map}-{camp}{result}"),
        Attacker = NSLOCTEXT("SettlementModule", "Attacker", "进攻方"),
        Defender = NSLOCTEXT("SettlementModule", "Defender", "防守方"),
        MapWithCampBattleResultTip = NSLOCTEXT("SettlementModule", "MapWithCampBattleResultTip", "{map}-{camp}{result}：根据攻防模式的不同地图的不同阵营的获胜难易度，有额外军功分"),
        MoralVictoryTip = NSLOCTEXT("SettlementModule", "MoralVictoryTip", "虽败犹荣：虽然对局失败但个人表现优异，可获得额外军功"),
        SignificantDiffTip = NSLOCTEXT("SettlementModule", "SignificantDiffTip", "艰苦奋战：双方水平差异较大时仍然坚持战斗，可获得额外军功"),
        NumFormat = NSLOCTEXT("SettlementModule", "Lua_Settlement_NumFormat", "{countNum}/{maxNum}"),
---------------------------------------------------------------- long 2.0 ----------------------------------------------------------------

---------------------------------------------------------------- long 3.0 ----------------------------------------------------------------
        RankCoefficientMarkup = NSLOCTEXT("SettlementModule", "RankCoefficientMarkup", "段位系数加成({coefficient}%)：+{addRankCurrency}"),
        DailyFirstEscape = NSLOCTEXT("SettlementModule", "DailyFirstEscape", "今日首次成功撤离：+%s"),
        UpperLimitOverflow = NSLOCTEXT("SettlementModule", "UpperLimitOverflow", "上限溢出：%s"),
        RankCurrencyWeeklyLimit = NSLOCTEXT("SettlementModule", "RankCurrencyWeeklyLimit", "周上限：{curValue}/{weeklyLimit}"),
        WinVictory = NSLOCTEXT("SettlementModule", "WinVictory", "获胜"),
        Defeat = NSLOCTEXT("SettlementModule", "Defeat", "战败"),
        ArenaWinVictoryTip = NSLOCTEXT("SettlementModule", "ArenaWinVictoryTip", "已达到分数上限"),
        ArenaDefeatTip = NSLOCTEXT("SettlementModule", "ArenaDefeatTip", "敌人夺取了物资"),
        ArenaItEndsInADrawTip = NSLOCTEXT("SettlementModule", "ArenaItEndsInADrawTip", "祝你下次好运"),
        ExpOverflowIntoCard = NSLOCTEXT("SettlementModule", "ExpOverflowIntoCard", "溢出经验转化为武器经验卡"),
        ItemNameWithNum = NSLOCTEXT("SettlementModule", "ItemNameWithNum", "{itemName}×{num}"),
        EliminationReplay = NSLOCTEXT("SettlementModule", "EliminationReplay", "淘汰回放"),
        EliminationReplayWithTime = NSLOCTEXT("SettlementModule", "EliminationReplayWithTime", "淘汰回放(%s)"),
        EliminationReplayWithProgress = NSLOCTEXT("SettlementModule", "EliminationReplayWithProgress", "淘汰回放(%s%%)"),
        EliminationReplayTip = NSLOCTEXT("SettlementModule", "EliminationReplayTip", "观看淘汰回放后将不能进入观战，是否观看淘汰回放？\n\n<dfmrichtext type=\"css\" font=\"Font'/Game/UI/Fonts/FZLTZHJW_ZH_Font.FZLTZHJW_ZH_Font'\">*回放功能为测试版本，持续优化中，敬请期待</>"),
        EliminationReplayRemainTimeTip = NSLOCTEXT("SettlementModule", "EliminationReplayRemainTimeTip", "仍有队友存活，%s秒后可查看淘汰回放"),
        EliminationReplayReadyTip = NSLOCTEXT("SettlementModule", "EliminationReplayReadyTip", "淘汰回放在准备中，请稍后再试"),
        ReputationAward = NSLOCTEXT("SettlementModule", "ReputationAward", "信誉非凡每日首胜：+%s"),
        EliminationReplayDownloading = NSLOCTEXT("SettlementModule", "EliminationReplayDownloading", "淘汰回放正在下载(%s%%)"),
---------------------------------------------------------------- long 3.0 ----------------------------------------------------------------
---------------------------------------------------------------- she 1.0 ----------------------------------------------------------------
        CommanderGameTeamName = NSLOCTEXT("SettlementModule", "CommanderGameTeamName", "%s小队"),
        CommanderGameSucceedCampName = NSLOCTEXT("SettlementModule", "CommanderGameSucceedCampName", "胜利团队"),
        CommanderGameFailCampName = NSLOCTEXT("SettlementModule", "CommanderGameFailCampName", "己方团队"),
        Activity =  NSLOCTEXT("SettlementModule", "Activity", "活动"),
        Challenge =  NSLOCTEXT("SettlementModule", "Challenge", "挑战"),
        JumpToPreFlow = NSLOCTEXT("SettlementModule", "JumpToPreFlow", "上一步"),

        MPTopTournamentScoreRowDescription = {
            [1] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1", "结果积分"),
            [2] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore2", "补偿积分"),
            [3] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore3", "特殊积分"),

            [1001] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1001", "进攻方胜利分"),
            [1002] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1002", "进攻方失败基础分"),
            [1003] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1003", "防守方胜利基础分"),
            [1004] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1004", "防守方失败分"),
            [1005] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1005", "进攻方阶段分"),
            [1006] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1006", "防守方阶段分"),

            [1007] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1007", "胜利基础分"),
            [1008] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1008", "失败基础分"),
            [1009] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore1009", "阶段分"),

            [2001] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore2001", "弱势阵营补偿"),
            [2002] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore2002", "中补补偿"),
            [2003] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore2003", "人数补偿"),
            [2004] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore2004", "匹配补偿"),

            [3001] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore3001", "特殊贡献"),
            [3002] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore3002", "挂机处罚"),
            [3003] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore3003", "消极作战"),
            [3004] = NSLOCTEXT("SettlementModule", "Lua_Settlement_MPTopTournamentScore3004", "中退处罚"),
        },

        DoubleRankScore = NSLOCTEXT("SettlementModule", "DoubleRankScore", "双倍积分卡"),
        DoubleRankActScore = NSLOCTEXT("SettlementModule", "DoubleRankActScore", "双倍活动"),

        MPRankProtectScore = NSLOCTEXT("SettlementModule", "MPRankProtectScore", "段位保护-保分"),
        MPRankProtectCard = NSLOCTEXT("SettlementModule", "MPRankProtectCard", "段位保护-保分卡"),
        MPRankProtectAct = NSLOCTEXT("SettlementModule", "MPRankProtectAct", "段位保护-保分活动"),
        MPRankScoreProtect = NSLOCTEXT("SettlementModule", "MPRankScoreProtect", "段位保护"),

        NightLowMission = NSLOCTEXT("SettlementModule", "NightLowMission", "长夜行动"),
        NightMiddleMission = NSLOCTEXT("SettlementModule", "NightMiddleMission", "前夜行动"),
        NightHighMission = NSLOCTEXT("SettlementModule", "NightHighMission", "终夜行动"),
        SkipScroll = NSLOCTEXT("SettlementModule", "SkipScroll", "跳过"),
---------------------------------------------------------------- she 1.0 ----------------------------------------------------------------
---------------------------------------------------------------- she 2.0 ----------------------------------------------------------------
        EscapePrisonSuccessTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapePrisonSuccessTXT", "撤离成功"),
        EscapePrisonFailTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapePrisonFailTXT", "越狱失败"),
        EscapePrisonTimeOutTXT = NSLOCTEXT("SettlementModule", "Lua_Settlement_EscapePrisonTimeOutTXT", "错失良机"),

        SolSettlementTip1 = NSLOCTEXT("SettlementModule", "Lua_Settlement_SolSettlementTip1", "属于队友的物资会在结算时自动返还。"),
        SolSettlementTip2 = NSLOCTEXT("SettlementModule", "Lua_Settlement_SolSettlementTip2", "若包含背包等容器类道具，归还后容器内道具会自动转移至仓库，仓库位置不足则邮件返还。"),
    
----------------------------------------------------------------- she3.0--------------------------------------------------------------------------------
        TopTournamentQuit = NSLOCTEXT("SettlementModule", "Lua_Settlement_TopTournamentQuit", "牺牲战友"),
        TopTournamentMiddleJoin = NSLOCTEXT("SettlementModule", "Lua_Settlement_TopTournamentMiddleJoin", "中补新兵"),
        VehicleLevel = NSLOCTEXT("SettlementModule", "VehicleLevel", "载具等级：%s"),

        MpShowExp = NSLOCTEXT("SettlementModule", "Lua_Settlement_MpShowExp", "战场表现：%s"),
        WechatAccountAddPercentTwo=NSLOCTEXT("SettlementModule", "Lua_Settlement_WechatAccountAddPercentTwo", "<dfmrichtext type=\"img\" width=\"48\" height=\"48\" id=\"Wechat\" align=\"0\"/>微信游戏特权（%d%%）:%s"),
        CybercafeBuffTwo = NSLOCTEXT("SettlementModule", "CybercafeBuffTwo", "<dfmrichtext type=\"img\" id=\"Cybercafe\"/>网吧游戏特权（%s%%）:%s"),
        QQAccountAddPercentTwo =NSLOCTEXT("SettlementModule", "Lua_Settlement_QQAccountAddPercentTwo", "<dfmrichtext type=\"img\" id=\"QQ\"/>QQ游戏特权（%d%%）:%s"),
        UniversityBuffTwo = NSLOCTEXT("SettlementModule", "UniversityBuffTwo", "<dfmrichtext type=\"img\" id=\"School\"/>高校特权（%s%%）:%s"),

        MPOtherBuffTwo = NSLOCTEXT("SettlementModule", "MPOtherBuffTwo", "<dfmrichtext type=\"img\" id=\"Other\"/>其他加成+%s%%"),
        MPDoubleExpCardTwo = NSLOCTEXT("SettlementModule", "MPDoubleExpCardTwo", "双倍卡:%s"),

        MPRankScoreDiscountRateTip = NSLOCTEXT("SettlementModule", "MPRankScoreDiscountRateTip", "活动模式军功折扣  %s%%"),
        MPRankScoreDiscountTitle = NSLOCTEXT("SettlementModule", "MPRankScoreDiscountTitle", "活动模式军功折扣"),
        MPRankScoreDiscountDesc = NSLOCTEXT("SettlementModule", "MPRankScoreDiscountDesc", "通过活动模式获得的军功将按照%s%%的折扣比例计入最终得分。"),
        MPRankScoreDiscountCal = NSLOCTEXT("SettlementModule", "MPRankScoreDiscountCal", "原军功得分 x 折扣比例=最终军功得分"),
        MPRankScoreCeilingTip = NSLOCTEXT("SettlementModule", "MPRankScoreCeilingTip", "超过%s分后活动模式不获得军功"),
    },

    MPTopTournamentScoreKind = {
        OverScore = 1,
        ExbackScore = 2,
        SpecialScore = 3
    },

    --结果分
    MPTopTournamentScoreType = {
        AttackerSuccessScore = 1001,
        AttackerFailedBaseScore = 1002,
        DefenderSuccessBaseScore = 1003,
        DefenderFailedScore = 1004,
        AttackerFlowScore = 1005,
        DefenderFlowScore = 1006,
        ConquestVicScore = 1007,
        ConquestDefeatScore = 1008,
        ConquestProgressScore = 1009,

        AwakerCampBackScore = 2001,
        MiddleJoinBackScore = 2002,
        PlayerNumBackScore = 2003,
        MatchBackScore = 2004,

        SpecialContributorScore = 3001,
        ShortHandupTriggerScore = 3002,
        PassiveCombatRateScore = 3003,
        MiddleLeave = 3004
    },

---------------------------------------------------------------- long 3.0 ----------------------------------------------------------------
    EAddRankCurrencyReason2Txt = {
        [EAddRankScoreReason.EAddRankScoreReason_KillAI] = NSLOCTEXT("SettlementModule", "RankCurrencyKillAI", "击败敌方势力：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_KillPlayer] = NSLOCTEXT("SettlementModule", "RankCurrencyKillPlayer", "击败特战干员：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_LootingBox] = NSLOCTEXT("SettlementModule", "RankCurrencyLootingBox", "搜索：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_Contract] = NSLOCTEXT("SettlementModule", "RankCurrencyContract", "合同：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_BluePrint] = NSLOCTEXT("SettlementModule", "RankCurrencyBluePrint", "曼德尔砖：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_CarryOutPrice] = NSLOCTEXT("SettlementModule", "RankCurrencyCarryOutPrice", "带出资产：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_AssistKillPlayer] = NSLOCTEXT("SettlementModule", "RankCurrencyAssistKillPlayer", "助攻击杀：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_HighQualityPrice] = NSLOCTEXT("SettlementModule", "RankCurrencyHighQualityPrice", "带出高价值道具：+%s"),
        [EAddRankScoreReason.EAddRankScoreReason_ReputationAward] = NSLOCTEXT("SettlementModule", "RankCurrencyReputationAward", "信誉非凡每日首胜：+%s")
    },

    ETeamIdx2Name = {
        [1] = NSLOCTEXT("SettlementModule", "TeamA", "小队 A"),
        [2] = NSLOCTEXT("SettlementModule", "TeamB", "小队 B"),
        [3] = NSLOCTEXT("SettlementModule", "TeamC", "小队 C"),
        [4] = NSLOCTEXT("SettlementModule", "TeamD", "小队 D"),
        [5] = NSLOCTEXT("SettlementModule", "TeamE", "小队 E"),
    },

    ESettlementInfoSource2Step = {
        [SettlementDefine.ESettlementInfoSource.MP] = {"settlementStart", UIName2ID.MPExpAndWeaponMain, UIName2ID.PathOfGrowthLevelUpPanel,
            UIName2ID.MPRankSettlement, UIName2ID.MPTopTournamentSettlement, UIName2ID.BattlePassSettlement, UIName2ID.MandelBrickGainPop},
        [SettlementDefine.ESettlementInfoSource.SOL] = {"settlementStart", UIName2ID.SOLRankSettlement, UIName2ID.PathOfGrowthLevelUpPanel,
            UIName2ID.BattlePassSettlement, UIName2ID.SOLUnlockWeapons},
        [SettlementDefine.ESettlementInfoSource.Arena] = {"settlementStart", UIName2ID.PathOfGrowthLevelUpPanel, UIName2ID.BattlePassSettlement}
    },
---------------------------------------------------------------- long 3.0 ----------------------------------------------------------------

    EEnemyTag = EEnemyTag,
    GameStateWS2BPIndex = {
        Playing = 0,
        Success = 1,
        Fail = 2,
        Quit = 3,
        Missing = 3,
        Error = 4
    },
    EnumSettlementMode = {
        SingleEscape = 1,
        MultiEscape = 2,
        SingleNotEscape = 3,
        MultiNotEscape = 4,
        RaidSettlement = 5
    },
    TeammateReturnMode = {
        None = 1,
        Bag = 2,
        ChestHanging = 3,
        Both = 4,
    },
    TeammateReturnLoc = {
        [1] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemWillBeReturn", "队友的物品<dfmrichtext type=\"img\" id=\"bind\"/> 将通过邮件返还给队友"),
        [2] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemWillBeReturnForBag", "队友的物品<dfmrichtext type=\"img\" id=\"bind\"/> 将通过邮件返还给队友，队友<customstyle color=\"Color_DarkPositive\">背包</>中的道具已转移至邮箱"),
        [3] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemWillBeReturnForChestHanging", "队友的物品<dfmrichtext type=\"img\" id=\"bind\"/> 将通过邮件返还给队友，队友<customstyle color=\"Color_DarkPositive\">胸挂</>中的道具已转移至邮箱"),
        [4] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemWillBeReturnForBoth", "队友的物品<dfmrichtext type=\"img\" id=\"bind\"/> 将通过邮件返还给队友，队友<customstyle color=\"Color_DarkPositive\">背包</>和<customstyle color=\"Color_DarkPositive\">胸挂</>中的道具已转移至邮箱"),
    },
    TeammateReturnFailLoc = {
        [2] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemFailReturnForBag", "<dfmrichtext type=\"img\" id=\"bind\"/>队友<customstyle color=\"Color_DarkNegative\">背包</>中的道具<customstyle color=\"Color_DarkNegative\">无法放入仓库</>,已转移至邮箱"),
        [3] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemFailBeReturnForChestHanging", "<dfmrichtext type=\"img\" id=\"bind\"/>队友<customstyle color=\"Color_DarkNegative\">胸挂</>中的道具<customstyle color=\"Color_DarkNegative\">无法放入仓库</>,已转移至邮箱"),
        [4] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemFailBeReturnForBoth", "<dfmrichtext type=\"img\" id=\"bind\"/>队友<customstyle color=\"Color_DarkNegative\">背包</>和<customstyle color=\"Color_DarkNegative\">胸挂</>中的道具<customstyle color=\"Color_DarkNegative\">无法放入仓库</>,已转移至邮箱"),
    },
    TeammateItemsSell = {
        [2] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemsSellForBag", "<dfmrichtext type=\"img\" id=\"bind\"/>队友的背包将返回给队友，其中无法放进仓库的道具将被<customstyle color=\"Color_DarkPositive\">自动丢弃</>。\n确定要继续吗？"),
        [3] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemsSellForChestHanging", "<dfmrichtext type=\"img\" id=\"bind\"/>队友的胸挂将返回给队友，其中无法放进仓库的道具将被<customstyle color=\"Color_DarkPositive\">自动丢弃</>。\n确定要继续吗？"),
        [4] = NSLOCTEXT("SettlementModule", "Lua_Settlement_TeammateItemsSellForBoth", "<dfmrichtext type=\"img\" id=\"bind\"/>队友的背包和胸挂将返回给队友，其中无法放进仓库的道具将被<customstyle color=\"Color_DarkPositive\">自动丢弃</>。\n确定要继续吗？"),
    },
    Events = {
        evtSettlementStart = LuaEvent:NewIns("evtSettlementStart"),
        evtSettlementEnd = LuaEvent:NewIns("evtSettlementEnd"),
        evtOpenRootCutscene = LuaEvent:NewIns("evtOpenRootCutscene"),
        evtCloseRootCutscene = LuaEvent:NewIns("evtCloseRootCutscene"),
        evtSettlementDelayShowTeam = LuaEvent:NewIns("evtSettlementDelayShowTeam"),
        evtSettlementEnterSpectateMode = LuaEvent:NewIns("evtSettlementEnterSpectateMode"),
        evtSettlementExitSpectateMode = LuaEvent:NewIns("evtSettlementExitSpectateMode"),
        evtCancelTeammateItemsTips = LuaEvent:NewIns("evtCancelTeammateItemsTips"),
        evtEvacuateWinViewLoadFinish = LuaEvent:NewIns("evtEvacuateWinViewLoadFinish"),
        evtEvacuateTrophyViewLoadFinish = LuaEvent:NewIns("evtEvacuateTrophyViewLoadFinish"),
        evtSettlementUILoadFinished = LuaEvent:NewIns("evtSettlementUILoadFinished"),
        evtNotSeeExceptionSettlement = LuaEvent:NewIns("evtNotSeeExceptionSettlement"),
        evtContinueClicked = LuaEvent:NewIns("evtContinueClicked"),
        evtSkillTutorialClicked = LuaEvent:NewIns("evtSkillTutorialClicked"),
        evtEvacuateExpViewOpen = LuaEvent:NewIns("evtEvacuateExpViewOpen"),
        evtChangeSettlementUI = LuaEvent:NewIns("evtChangeSettlementUI"),
        evtChangeSettlementTab = LuaEvent:NewIns("evtChangeSettlementTab"),
        evtSpace2NextStep = LuaEvent:NewIns("evtSpace2NextStep"),
        evtShowAnimPlaying = LuaEvent:NewIns("evtShowAnimPlaying"),
        evtReplayBackToGameplay = LuaEvent:NewIns("evtReplayBackToGameplay"),
        evtExpUpShow = LuaEvent:NewIns("evtExpUpShow"),
        evtWeaponExpShow = LuaEvent:NewIns("evtWeaponExpShow"),
        evtBeginMpSettlementPop = LuaEvent:NewIns("evtBeginSettlementPop"),   -- mp局外结算弹窗开始播放
        evtEndMpSettlementPop = LuaEvent:NewIns("evtEndMpSettlementPop"),   -- mp局外结算弹窗结束播放
        evtEndWeaponUpgradePop = LuaEvent:NewIns("evtEndWeaponUpgradePop"),   -- mp局外结算 武器升级弹窗结束/武器升级弹窗不需要弹出

        -- BEGIN MODIFICATION @ VIRTUOS
        evtReadyToShowSettlement = LuaEvent:NewIns("evtReadyToShowSettlement"), -- 准备显示MP结算页面
        evtStartToShowSettlement = LuaEvent:NewIns("evtStartToShowSettlement"), -- 开始显示MP结算页面
        -- END MODIFICATION
                
        evtBeginSolSettlementPop = LuaEvent:NewIns("evtBeginSolSettlementPop"),   -- sol局外结算弹窗开始播放
        evtEndSolSettlementPop = LuaEvent:NewIns("evtEndSolSettlementPop"),   -- sol局外结算弹窗结束播放

        evtMpRankSettlementShow = LuaEvent:NewIns("evtMpRankSettlementShow"),   -- mp积分结算弹窗打开
        evtMpRankSettlementClose = LuaEvent:NewIns("evtMpRankSettlementClose"),   -- mp积分结算弹窗关闭
        evtSolRankSettlementShow = LuaEvent:NewIns("evtSolRankSettlementShow"),   -- sol积分结算弹窗打开
        evtSolRankSettlementClose = LuaEvent:NewIns("evtSolRankSettlementClose"),   -- sol积分结算弹窗关闭
        evtMpRankSettlementInAnimFin = LuaEvent:NewIns("evtMpRankSettlementInAnimFin"),   -- mp积分结算弹窗进入动画结束

---------------------------------------------------------------- long 2.0 ----------------------------------------------------------------
        evtRaidTeamDetailInfoCheckBoxStateChanged = LuaEvent:NewIns("evtRaidTeamDetailInfoCheckBoxStateChanged"),
---------------------------------------------------------------- long 2.0 ----------------------------------------------------------------

---------------------------------------------------------------- long 3.0 ----------------------------------------------------------------
        evtArenaSelectTeamInfoLabel = LuaEvent:NewIns("evtArenaSelectTeamInfoLabel"),
        evtEliminationReplayEnd = LuaEvent:NewIns("evtEliminationReplayEnd"),
        evtEvacuationResultInfoOnShowBegin = LuaEvent:NewIns("evtEvacuationResultInfoOnShowBegin"),
---------------------------------------------------------------- long 3.0 ----------------------------------------------------------------
---------------------------------------------------------------- she 1.0 ----------------------------------------------------------------
        evtFinishLoadCutSceneLevel = LuaEvent:NewIns("evtFinishLoadCutSceneLevel"),
        evtGMSetShowPlayTime = LuaEvent:NewIns("evtGMSetShowPlayTime"),
---------------------------------------------------------------- she 1.0 ----------------------------------------------------------------
---------------------------------------------------------------- she 2.0 ----------------------------------------------------------------
        evtEndAllSettlementPop = LuaEvent:NewIns("evtEndAllSettlementPop"),   -- 局外结算弹窗结束
---------------------------------------------------------------- she 2.0 ----------------------------------------------------------------
        evtTopTournamentItemClick = LuaEvent:NewIns("evtTopTournamentItemClick"), 
    },
    BGMediaRowName = "Settlement_CutScene1",
    SettlementSuccessWaitTime = 60,
    -- Military2Image = {
    --     [1] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0101.HUD_Rank_Icon_0101'",
    --     [2] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0102.HUD_Rank_Icon_0102'",
    --     [3] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0103.HUD_Rank_Icon_0103'",
    --     [4] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0104.HUD_Rank_Icon_0104'",
    --     [5] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0105.HUD_Rank_Icon_0105'",
    --     [6] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0106.HUD_Rank_Icon_0106'",
    --     [7] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0107.HUD_Rank_Icon_0107'",
    --     [8] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0108.HUD_Rank_Icon_0108'",
    --     [9] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0109.HUD_Rank_Icon_0109'",
    --     [10] = "PaperSprite'/Game/UI/UIAtlas/System/HUD/BakedSprite/HUD_Rank_Icon_0110.HUD_Rank_Icon_0110'"
    -- },
    ArmedForce2Image = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_Arm_Icon_0201.CommonHud_Arm_Icon_0201'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_Arm_Icon_0202.CommonHud_Arm_Icon_0202'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_Arm_Icon_0203.CommonHud_Arm_Icon_0203'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_Arm_Icon_0204.CommonHud_Arm_Icon_0204'",
    },
    TeamEnemyImage = {
        [1]="PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0401.CommonHud_MapMarker_Icon_0401'",
        [2]="PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0402.CommonHud_MapMarker_Icon_0402'",
        [3]="PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0406.CommonHud_MapMarker_Icon_0406'",
        [4]="PaperSprite'/Game/UI/UIAtlas/CommonHud/BakedSprite/CommonHud_MapMarker_Icon_0407.CommonHud_MapMarker_Icon_0407'",
    },
    EscapeInfoType2Image = {
        [1]="PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_Icon_0017.Settlement_Icon_0017'",
        [2]="PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'",
        [3]="PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_Icon_0016.Settlement_Icon_0016'",
        [4]="PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_Icon_0009.Settlement_Icon_0009'",

    },
    CaptureFlagImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0006.Common_Card_0006'",
    EscapeInfoType2Text = {
        [1]=NSLOCTEXT("SettlementModule", "Lua_Settlement_ExtractionPointText", "撤离点"),
        [2]=NSLOCTEXT("SettlementModule", "Lua_Settlement_DurationText", "时长"),
        [3]=NSLOCTEXT("SettlementModule", "Lua_Settlement_KillerText", "淘汰者"),
        [4]=NSLOCTEXT("SettlementModule", "Lua_Settlement_DeathReasonText", "淘汰原因"),
    },
    EscapeInfoType={
        ExtractPoint=1,
        Duration=2,
        Killer=3,
        AccidentDeath=4,
    },
    AbilityType2Name = {
        [1] = NSLOCTEXT("SettlementModule", "ShootAbility", "射击能力"),
        [2] = NSLOCTEXT("SettlementModule", "TacticsAbility", "战术能力"),
        [3] = NSLOCTEXT("SettlementModule", "VehicleAbility", "载具能力")
    },
    AbilityType2Path = {
        [1] = "Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_59.Settlement_Sp_59'",
        [2] = "Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_58.Settlement_Sp_58'",
        [3] = "Texture2D'/Game/UI/UIAtlas/System/Settlement/Sp/Settlement_Sp_57.Settlement_Sp_57'"
    },
    RaidEvaluateType2Color = {
        [1] = "C012",
        [2] = "C013",
        [3] = "C014"
    }
}

SettlementConfig.ACTIVE_UNLOAD_MAP = true
SettlementConfig.MAX_SHOW_ACHIEVEMENT_NUM = 4

SettlementConfig.TDMQuitTimerSeconds = 60

SettlementConfig.bUseEarlyLoadPlan = true

SettlementConfig.CommanderGameConfig = {
    ClosingCreditsStableSeconds = 5,
    ClosingCreditsRollSpeed = 1,
    SkipBtnHideSeconds = 5,
    SkipBtnLongPressSeconds = 1,
}

SettlementConfig.EMPExpMainType = {
    EXP = 0,
    Vehicle = 1,
    Weapon = 2,
}

SettlementConfig.bUseWorldNameToSelectCutScene = true

-- 0表示在S几结算就用S几的运镜
-- 大于0表示固定使用某个运镜Sequence
SettlementConfig.Map2CutScene = {
    [33]    =   0,
    [34]    =   3,
    [54]    =   0,
    [75]    =   0,
    [103]   =   2,
    [105]   =   2,
    [107]   =   0,
    [108]   =   5,
    [109]   =   2,
    [111]   =   0,
    [112]   =   3,
    [113]   =   0,
    [114]   =   2,
    [116]   =   4,
    [117]   =   1,
    [118]   =   1,
    [119]   =   1,
    [121]   =   0,
    [122]   =   2,
    [123]   =   2,
    [124]   =   2,
    [125]   =   1,
    [126]   =   1,
    [127]   =   2,
    [128]   =   3,
    [132]   =   0,
    [133]   =   0,
    [134]   =   3,
    [136]   =   3,
    [137]   =   3,
    [210]   =   2,
    [213]   =   2,
    [227]   =   5,
    [232]   =   0,
    [233]   =   0,
    [234]   =   0,
    [235]   =   0,
    [236]   =   0,
    [237]   =   0,
    [238]   =   0,
    [401]   =   2,
    [402]   =   3,
    [403]   =   2,
    [501]   =   2,
    [502]   =   2,
    [503]   =   2,
    [504]   =   3,
    [505]   =   3,
    [506]   =   1,
    [507]   =   2,
    [508]   =   3,
    [509]   =   3,
    [510]   =   3,
    [511]   =   2,
    [512]   =   2,
    [513]   =   2,
    [514]   =   3,
    [515]   =   2,
    [516]   =   3,
    [517]   =   4,
    [526]   =   3,
    [551]   =   2,
    [553]   =   4,
    [554]   =   3,
    [555]   =   4,
    [556]   =   4,
    [557]   =   3,
    [558]   =   1,
    [559]   =   4,
    [560]   =   3,
    [561]   =   4,
    [562]   =   4,
    [563]   =   3,
    [564]   =   1,
    [565]   =   4,
    [566]   =   3,
    [567]   =   4,
    [568]   =   4,
    [569]   =   3,
    [570]   =   1,
    [571]   =   4,
    [572]   =   3,
    [573]   =   4,
    [574]   =   4,
    [575]   =   3,
    [576]   =   1,
    [577]   =   4,
    [578]   =   3,
    [579]   =   4,
    [580]   =   4,
    [581]   =   3,
    [582]   =   1,
    [583]   =   4,
    [584]   =   3,
    [585]   =   4,
    [586]   =   4,
    [587]   =   3,
    [588]   =   1,
    [601]   =   3,
    [602]   =   3,
    [603]   =   2,
    [604]   =   1,
    [605]   =   5,
    [606]   =   0,
    [607]   =   0,
    [621]   =   0,
    [622]   =   0,
    [623]   =   0,
    [624]   =   0,
    [625]   =   0,
    [626]   =   0,
    [627]   =   0,
    [801]   =   0,
    [802]   =   5,
    [803]   =   2,
    [804]   =   2,
    [805]   =   3,
    [806]   =   5,
    [901]   =   4,
    [902]   =   4,
    [903]   =   3,
    [904]   =   4,
    [906]   =   1,
    [2401]  =   5,
    [2402]  =   2,
    [2403]  =   3,
    [2404]  =   3,
    [2405]  =   2,
    [2406]  =   2,
    [2407]  =   2,
}

SettlementConfig.MapName2CutSceneFVector = {
    cracked = {
        -- 烬区
        [1]=FVector(82584.0, -85236.0, -1374.0),
        [2]=FVector(102258.0, -89729.0, -962.0),
        [3]=FVector(103312.0, -97862.0, -904.0),
    },
    iceland = {
        -- 攀升
        [1]=FVector(186308.0, -150083.0, -21868.0),
        [2]=FVector(206160.0, -182826.0, -21198.0),
        [3]=FVector(205564.0, -230127.0, 1790.0),
        [4]=FVector(232905.0, -243805.0, 6277.0),
    },
    gibraltar = {
        -- 临界点
        [1]=FVector(247745.0, -126535.0, -9466.0),
        [2]=FVector(252287.0, -144392.0, -9465.0),
        [3]=FVector(246078.0, -166144.0, -904.0),
    },
    retrowar = {
        -- 堑壕战
        [1]=FVector(81408.2,-110579.5,-481.7),
        [2]=FVector(97343.2, -117245.6, -171.1),
        [3]=FVector(120823.4, -109600.3, 1163.8),
        [4]=FVector(118755.3, -85550.5, 2724.1),
        [5]=FVector(93044.3, -80794.2, 3567.1),
    },
    shafted = {
        -- 贯穿
        [1]=FVector(43818.0, -47138.0, 274.0),
        [2]=FVector(43046.0, -70620.0, -4355.0),
        [3]=FVector(47134.0, -84240.0, -5023.0),
    },
    derail = {
        -- 断轨
        [1]=FVector(110825.2,-103905.1,916.5),
        [2]=FVector(84705.1,-96422.4,6090.2),
        [3]=FVector(69763.2,-90540.0,4784.9),
        [4]=FVector(98701.9,-55340.9,888.5),
    },
    crest = {
        -- 刀锋
        [1]=FVector(221028.8, -204140.6, 245.3),
        [2]=FVector(219321.671875, -203392.796875, -4317.496582),
        [3]=FVector(209411.812500, -201597.937500, -3102.542480),
    },
}

SettlementConfig.MapID2CutSceneFVector = {
    [33]    =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [34]    =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [54]    =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [75]    =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [103]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [105]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [107]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [108]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [109]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [111]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [112]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [113]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [114]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [116]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [117]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [118]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [119]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [121]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [122]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [123]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [124]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [125]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [126]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [127]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [128]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [132]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [133]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [134]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [136]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [137]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [210]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [213]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [227]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [232]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [233]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [234]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [236]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [237]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [235]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [238]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [401]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [402]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [403]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [501]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [502]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [503]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [504]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [505]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [506]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [507]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [508]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [509]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [510]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [511]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [512]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [513]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [514]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [515]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [516]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [517]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [526]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [551]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [553]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [554]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [555]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [556]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [557]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [558]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [559]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [560]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [561]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [562]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [563]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [564]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [565]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [566]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [567]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [568]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [569]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [570]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [571]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [572]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [573]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [574]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [575]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [576]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [577]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [578]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [579]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [580]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [581]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [582]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [583]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [584]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [585]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [586]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [587]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [588]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [601]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [602]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [603]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [604]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [605]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [606]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [607]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [621]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [622]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [623]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [624]   =   SettlementConfig.MapName2CutSceneFVector.shafted,
    [625]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [626]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [627]   =   SettlementConfig.MapName2CutSceneFVector.crest,
    [801]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [802]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [803]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [804]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [805]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [806]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [901]   =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [902]   =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [903]   =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [904]   =   SettlementConfig.MapName2CutSceneFVector.derail,
    [906]   =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [2401]  =   SettlementConfig.MapName2CutSceneFVector.retrowar,
    [2402]  =   SettlementConfig.MapName2CutSceneFVector.iceland,
    [2403]  =   SettlementConfig.MapName2CutSceneFVector.cracked,
    [2404]  =   SettlementConfig.MapName2CutSceneFVector.derail,
    [2405]  =   SettlementConfig.MapName2CutSceneFVector.gibraltar,
    [2406]  =   SettlementConfig.MapName2CutSceneFVector.crest,
    [2407]  =   SettlementConfig.MapName2CutSceneFVector.shafted,
}
return SettlementConfig
