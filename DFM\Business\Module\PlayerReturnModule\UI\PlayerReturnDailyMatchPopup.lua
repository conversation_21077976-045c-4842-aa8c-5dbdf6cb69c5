----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------



local InputSummaryItemHD            = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local IPlayerReturnSubActivity      = require "DFM.Business.Module.PlayerReturnModule.SubActivities.IPlayerReturnSubActivity"
local Promise                       = require "DFM.Business.DataStruct.Common.Base.Promise"
local Filter                        = require "DFM.Business.DataStruct.Common.Base.Filter"
local F                             = require "DFM.Business.DataStruct.Common.Base.Functional"
local InputBindingAgent             = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local PlayerReturnConfig            = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnConfig"
local PlayerReturnStatistics        = require "DFM.Business.Module.PlayerReturnModule.PlayerReturnStatistics"
local WeaponAssemblyTool            = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local UGunPresetTableManager        = import "GunPresetTableManager"
local UGunPresetTableManager        = import "GunPresetTableManager"
local UAssembleWeaponDataLibrary    = import "AssembleWeaponDataLibrary"
local CollectionLogic               = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local WeaponHelperTool              = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"

local function LoadAndEnterSubstage(eSubstage)
    local p = Promise.New()

    local fEnterSubstage = function()
        Facade.GameFlowManager:EnterSubStage(eSubstage)
        p:Resolve()
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(eSubstage, true, nil, fEnterSubstage, false, 30)
    return p
end

---@class PlayerReturnDailyMatchPopup: LuaUIBaseView
local PlayerReturnDailyMatchPopup = ui("PlayerReturnDailyMatchPopup", LuaUIBaseView)

function PlayerReturnDailyMatchPopup:Ctor()
    self._inputMgr = InputBindingAgent.New(self)

    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)

    self._wtMainTitle    = self:Wnd("DFTextBlock_Title", UITextBlock)
    self._wtDescText     = self:Wnd("DFTextBlock_92", UITextBlock)
    self._wtDayLabel     = self:Wnd("DFTextBlock_7", UITextBlock)
    self._wtItemsBox     = self:Wnd("DFHorizontalBox_151")
    self._wtBtnConfirm   = self:Wnd("WBP_DFCommonButtonV1S3", DFCommonButtonOnly)
    self._wtBtnCancel    = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtSkipLabel_HD = self:Wnd("WBP_TopBarHD_InputSummary", InputSummaryItemHD)
    self._wtBgImage      = self:Wnd("DFCDNImage_60", DFCDNImage)
    self._wtBuffLabel    = self:Wnd("DFHorizontalBox_93", UIWidgetBase)

    self._wtPropImg_1    = self:Wnd("wtPropImg_1", UIImage)
    self._wtPropImg_2    = self:Wnd("wtPropImg_2", UIImage)
    self._wtPropImg_3    = self:Wnd("wtPropImg_3", UIImage)
    self._wtPropImgBox   = self:Wnd("wtPropImgBox", UIImage)

    self._wtBgImageGroup = self:Wnd("WBP_Common_ScaleBg", UIWidgetBase)
    self._wtBgStackedImageGroup = self:Wnd("WBP_Common_ScaleBg_1", UIWidgetBase)

    self._cachedImageURL = nil

    if IsHD() then
        self._wtSkipLabel_HD:SelfHitTestInvisible()
    else
        self._wtBtnCancel:Visible()
        self._wtBtnCancel:Event("OnClicked", self._OnPlayerSkip, self)
    end
    self._wtBtnConfirm:Event("OnClicked", self._OnPlayerConfirm, self)

    Module.CommonBar:RegStackUIHideBar(self)
end

---@param popupPromise      Promise2
---@param interruptPromise  Promise2
function PlayerReturnDailyMatchPopup:SetPopupPromises(popupPromise, interruptPromise)
    self._popupPromise     = popupPromise
    self._interruptPromise = interruptPromise
    if self._interruptPromise then
        self._interruptPromise:Then(CreateCPlusCallBack(self._OnInterrupt, self))
    end
end

-- 注意跳过逻辑和打断逻辑不同
-- 对局爽打弹窗比较强制，即使取消也有跳转，弹窗被打断时才是直接关闭
function PlayerReturnDailyMatchPopup:_OnInterrupt()
    -- 打断弹窗
    Facade.UIManager:CloseUI(self)
    if self._popupPromise then
        self._popupPromise:Resolve({reply = EPopupReply.Continue})
    end
end

function PlayerReturnDailyMatchPopup:_OnPlayerSkip()
    -- SOL首日弹窗是强制参与，空格跳过和点击确认走同一个逻辑
    if self._taskIdx == 1 and Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.SOL then
        self:_OnPlayerConfirm()
        return
    end

    -- 非首日/非SOL，空格跳过进入回流主界面
    Facade.UIManager:CloseUI(self)
    if self._popupPromise then
        Module.PlayerReturn:ShowMainPanel()
        -- 弹窗结算为跳转
        self._popupPromise:Resolve({reply = EPopupReply.Jump})
    end
end

function PlayerReturnDailyMatchPopup:_OnPlayerConfirm()    
    Facade.UIManager:CloseUI(self)
    if self._popupPromise then
        self._popupPromise:Resolve({reply = EPopupReply.Jump})
    end

    Module.PlayerReturn:JumpToDailyMatch(self._activityInfo, self._taskIdx)
end

function PlayerReturnDailyMatchPopup:SetUpInputBinding()
    -- 初始化输入绑定

    -- 确认
    self._inputMgr:AddBinding(
        "ConfirmBtn", -- id
        {
            actionName      = "Common_ButtonLeft_Gamepad", -- X键
            callback        = self._OnPlayerConfirm,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop
        }
    )
    self._wtBtnConfirm:SetDisplayInputAction("Common_ButtonLeft_Gamepad", nil, nil, true)

    -- 跳过
    self._inputMgr:AddBinding(
        "Skip", -- id
        {
            actionName      = "JumpOver", -- A键
            callback        = self._OnPlayerSkip,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop,
        }
    )

    -- 跳过
    self._inputMgr:AddBinding(
        "Back", -- id
        {
            actionName      = "Back", -- Esc
            callback        = self._OnPlayerSkip,
            caller          = self,
            displayPriority = EDisplayInputActionPriority.UI_Pop,
        }
    )
end

function PlayerReturnDailyMatchPopup:OnShowBegin()
    Module.PlayerReturn.Field._lastDailyMatchPopupTime = os.time()

    if IsHD() then
        local cb = CreateCallBack(self._OnPlayerSkip, self)
        self._wtSkipLabel_HD:SetData("JumpOver", cb)
    end

    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeFight, Server.ArmedForceServer:GetCurArmedForceMode())
    local activityInfo = actv:GetActivityInfo()
    if not activityInfo then
        Facade.UIManager:CloseUI(self)
        if self._popupPromise then 
            self._popupPromise:Resolve()
        end
    end
    
    local taskIdx, takInfo = Filter.FirstMatch(activityInfo.task_info, F.Get("state"):Eq(F.V(ActivityTaskState.ActivityTaskStateAccepted)))
    self:SetState(activityInfo, taskIdx)
    LogAnalysisTool.SignButtonClicked(PlayerReturnStatistics:GetDailyMatchPopupStatID(taskIdx))

    self:SetUpInputBinding()
end

function PlayerReturnDailyMatchPopup:OnHideBegin()
    self._inputMgr:ClearAll()
end

function PlayerReturnDailyMatchPopup:OnHide()
    Facade.UIManager:RemoveAllSubUI(self)
end

---@param activityInfo pb_ActivityInfo
---@param taskIdx integer
function PlayerReturnDailyMatchPopup:SetState(activityInfo, taskIdx)
    local taskInfo = activityInfo.task_info[taskIdx]
    
    self._wtMainTitle:SetText(activityInfo.name)
    self._wtDayLabel:SetText(Module.PlayerReturn:GetDayText(taskIdx))
    
    self._wtDescText:SetText(
        (Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.SOL)
        and PlayerReturnConfig.Localization.DailyMatch.DescSOL
        or PlayerReturnConfig.Localization.DailyMatch.DescMP
    )

    ---@type PlayerReturnDailyMatchImpl
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeFight)
    local doubleExp, doubleScore = actv:GetMPBuffState()

    if doubleExp>0 and doubleScore>0 and Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
        self._wtBuffLabel:SelfHitTestInvisible()
    else
        self._wtBuffLabel:Collapsed()
    end

    Facade.UIManager:RemoveAllSubUI(self)
    for idx, award in ipairs(taskInfo.awards) do
        local itemWidget = getfromweak(Facade.UIManager:AddSubUI(self, UIName2ID.PlayerReturnBlurBehindItemView, self._wtItemsBox))

        ---@type IVCommonItemTemplate
        local itemIconWidget = itemWidget:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
        itemIconWidget:InitItem(ItemBase:NewIns(award.prop.id, award.prop.num))
    end

    assert(taskInfo.awards[1], "对局爽打 "..tostring(activityInfo.actv_id).." 第"..tostring(taskIdx).."天 未读取到任何奖励信息")

    local lastRewardItemID = taskInfo.awards[#taskInfo.awards].prop.id
    --lastRewardItemID = 28010150004
    local item = ItemBase:NewIns(lastRewardItemID)
    
    -- 查找CDN图片链接
    self._cachedImageURL = nil
    local imageUrl
    for _, pair in pairs(activityInfo.return_info.images) do
        if pair.task_id == taskInfo.task_id then
            imageUrl = pair.image
        end
    end
    if imageUrl and imageUrl ~= "" then
        -- 优先使用配置CDN图片链接
        self._wtBgImageGroup:SelfHitTestInvisible()
        self._wtBgStackedImageGroup:Collapsed()
        self._cachedImageURL = "Resource/Texture/Activity/"..imageUrl
        self._wtBgImage:SetCDNImage(self._cachedImageURL, false, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    else
        if item.itemMainType == EItemType.WeaponSkin then
            -- 武器皮肤调用3D展示
            self._wtBgImageGroup:Collapsed()
            self._wtBgStackedImageGroup:Collapsed()
            LoadAndEnterSubstage(ESubStage.HallCollectionNew):Then(function()
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetDisplayItem")
                Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "ResetWeapon")
                
                local weaponDesc, partIndexs
                weaponDesc, partIndexs = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                if isvalid(weaponDesc) then
                    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallCollectionNew, "SetDisplayWeaponAutoBoundAdapter", weaponDesc, true, item.itemSubType == ItemConfig.EWeaponItemType.Melee)
                end
            end)
        else
            -- 其他物品调用2D展示
            self._wtBgImageGroup:Collapsed()
            self._wtBgStackedImageGroup:SelfHitTestInvisible()
            Module.Collection:SetBackgroundImgByPropId({self._wtPropImg_1, self._wtPropImg_2, self._wtPropImg_3}, true, Module.CDNIcon.Config.ECdnTagEnum.Collection, nil, lastRewardItemID, self._wtPropImgBox)
        end
    end
    self._activityInfo = activityInfo
    self._taskIdx = taskIdx
end

---返回：视为取消
function PlayerReturnDailyMatchPopup:OnNavBack()
    self:_OnPlayerSkip()
end

return PlayerReturnDailyMatchPopup