----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGM)
----- LOG FUNCTION AUTO GENERATE END -----------



local UKismetSystemLibrary = import "KismetSystemLibrary"
local UGameplayStatics = import "GameplayStatics"
local EquipmentType = import "EEquipmentType"
local CharacterPart = import "ECharacterPart"
local UDFMGameplayBlueprintHelper = import "DFMGameplayBlueprintHelper"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local UWidgetBlueprintLibrary = import "WidgetBlueprintLibrary"
local ULogVisualizerInGame = import "LogVisualizerInGame"
local UUploadToolsModuleBPTools = import "UploadToolsModuleBPTools"
local EQuitPreference = import "EQuitPreference"
local EPropertyClass = import "EPropertyClass"
local UGPGlobalUtil = import "GPGlobalUtil"
local EAttachPosition = import "EAttachPosition"
local UWeaponAssembleSubsystem = import "WeaponAssembleSubsystem"
local DepositoryItemView = require "DFM.Business.Module.InventoryModule.UI.Main.DepositoryItemView"
local ItemView = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemView"
local UDFMLocalizationManager = import "DFMLocalizationManager"
local EGameHUDState = import "GameHUDSate"
local UWeaponAssembleSubsystem = import "WeaponAssembleSubsystem"
local DFMLocalizationManager = UDFMLocalizationManager.Get(GetGameInstance())
local ULuaExtension = import("LuaExtension")
local LuaPerfTool = require "DFM.YxFramework.Plugin.Performance.LuaPerfTool"
local AnalysisUtil = require "DFM.YxFramework.Util.AnalysisUtil"
local UDFMAudioMastering = import "DFMAudioMastering"
local UDFUICostMonitor = import "DFUICostMonitor"
local PreMatchChooseArmDisplayLogic = require "DFM.Business.Module.HUDModule.Logic.PreMatchChooseArmDisplayLogic"
local LuaSymbolTool = require "DFM.YxFramework.Plugin.Performance.LuaSymbolTool"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UGPAudioStatics = import "GPAudioStatics"
local UGPGameplayStatics = import "GPGameplayStatics"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
local UDFMLocalizationCrashsightManager = import "DFMLocalizationCrashsightManager"
local PerfGearPipeline = import "PerfGearPipeline"
local perfGearPipeline = PerfGearPipeline.Get()
local GameletLogic = require "DFM.Business.Module.GameletModule.Logic.GameletLogic"
local UIReleaseUtil = require "DFM.YxFramework.Managers.UI.Util.UIReleaseUtil"

---用于QADebug的Reset
local needCollectCurrentRenderParams = true
local renderParams = 
{
    enableVolumetricFog = 0;
    volumetricFogGridSizeZ = 0;
    bloomQuality = 0;
}

---InGameController
---@return InGameController
local function GetInGameController()
    return InGameController:Get()
end


local GMHandleLogic = {}
--------------------------------------------------------------------------
--- 通用GMServer接口
--------------------------------------------------------------------------

---用于QADebug的Reset
---有一些看起来很丑的应急用hardcode,等引擎修改提交后会去掉
function GMHandleLogic.CollectCurrentParams()
    if needCollectCurrentRenderParams then
        -- renderParams.enableVolumetricFog = UKismetSystemLibrary.GetConsoleVariableIntValue("r.VolumetricFog")
        -- renderParams.volumetricFogGridSizeZ = UKismetSystemLibrary.GetConsoleVariableIntValue("r.VolumetricFog.GridSizeZ")
        -- renderParams.bloomQuality = UKismetSystemLibrary.GetConsoleVariableIntValue("r.BloomQuality")
        local imageQualityLevel = perfGearPipeline:GetQualityLevel()
        if IsHD() then
            if imageQualityLevel==5 then
                renderParams.enableVolumetricFog = 1
                renderParams.volumetricFogGridSizeZ = 128
                renderParams.bloomQuality = 5
            elseif imageQualityLevel==4 then
                renderParams.enableVolumetricFog = 1
                renderParams.volumetricFogGridSizeZ = 128
                renderParams.bloomQuality = 5
            elseif imageQualityLevel==3 then
                renderParams.enableVolumetricFog = 1
                renderParams.volumetricFogGridSizeZ = 64
                renderParams.bloomQuality = 5
            elseif imageQualityLevel==2 then
                renderParams.enableVolumetricFog = 1
                renderParams.volumetricFogGridSizeZ = 32
                renderParams.bloomQuality = 5
            elseif imageQualityLevel==1 then
                renderParams.enableVolumetricFog = 1
                renderParams.volumetricFogGridSizeZ = 32
                renderParams.bloomQuality = 3
            elseif imageQualityLevel==0 then
                renderParams.enableVolumetricFog = 0
                renderParams.volumetricFogGridSizeZ = 32
                renderParams.bloomQuality = 3
            end
        else
            renderParams.enableVolumetricFog = 0
            renderParams.volumetricFogGridSizeZ = 0
            if imageQualityLevel==4 then
                renderParams.bloomQuality = 3
            elseif imageQualityLevel==3 then
                renderParams.bloomQuality = 2
            elseif imageQualityLevel==2 then
                renderParams.bloomQuality = 0
            elseif imageQualityLevel==1 then
                renderParams.bloomQuality = 0
            elseif imageQualityLevel==0 then
                renderParams.bloomQuality = 0
            end
        end
        needCollectCurrentRenderParams = false;
    end
end

function GMHandleLogic._ShowMeesage(message)
    Module.GM.Config.evtShowMessage:Invoke(message)
end

GMHandleLogic.AllGraduate = function()

    GMHandleLogic.SafeHouseGraduate()
    GMHandleLogic.RoleQualityGod()
    GMHandleLogic.AddFiveBox()
    GMHandleLogic.GMMPUnlockWeaponAll()
    GMHandleLogic.HeroUnlockAll()

    GMHandleLogic.AddCurrency(***********, *********)
    GMHandleLogic.AddCurrency(***********, *********)
    GMHandleLogic.AddSeasonExp(*********)
    GMHandleLogic.AddAccountExp(*********)
end

--关闭声音和屏幕调试信息
GMHandleLogic.SwitchPureMode = function(flag)
    if flag then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "DisableAllScreenMessages")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.DisableAudio")
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "EnableAllScreenMessages")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.EnableAudio")
    end
end

--设置阿萨拉训旅里程碑进度
GMHandleLogic.SetTaraMilestone = function(actId, heroId, progress)
    local gmType = EGMType.kGMTypeActivityAhsarahTravelSetProgress
    Server.GMServer:DoGMReq(gmType, {actId, heroId, progress})
end

--活动页面透视开关
GMHandleLogic.ShowActPerspective = function(flag)
    Module.Activity:ShowActPerspective(flag)
end

--直接打开活动页面
GMHandleLogic.ShowActPanel = function(Tab1, Tab2, bShowCurMode)
    Module.Activity:ShowActivityPanel(Tab1, Tab2, false, bShowCurMode)
end

--重置活动本地文件缓存
GMHandleLogic.ResetActUserConfig = function()
    Module.Activity:ClearUserConfigKey()
end

--活动系统完成任务
GMHandleLogic.FinishTask = function(activityID, taskID, flag)
    local gmType = EGMType.kGMTypeActivityCompleteTask

    -- 通用结果处理
    local function handleResult(success)
        if success then
            Server.ActivityServer:InitActivityInfo()
        end
        return success
    end

    -- 单个任务处理
    local function processSingleTask(aid, tid)
        if not aid or not tid then
            return false
        end
        return Server.GMServer:DoGMReq(gmType, {aid, tid})
    end

    -- 批量任务处理
    local function processBatchTasks(taskList)
        if not taskList or #taskList == 0 then
            return false
        end

        for _, task in ipairs(taskList) do
            processSingleTask(task.activityID, task.taskID)
        end
        
        return true
    end

    -- [heimu]批量测试
    if flag then
        if flag == 1 then -- 完成所有活动任务
            local taskBatch = {}
            local activityGroups = Server.ActivityServer:GetActivityGroups()
            
            for _, groupID in ipairs(activityGroups or {}) do
                local activityInfos = Server.ActivityServer:GetActivityInfosByGroupID(groupID)
                for _, activity in ipairs(activityInfos or {}) do
                    for _, task in ipairs(activity.task_info or {}) do
                        table.insert(taskBatch, {
                            activityID = activity.actv_id,
                            taskID = task.task_id
                        })
                    end
                end
            end
            
            return handleResult(#taskBatch > 0 and processBatchTasks(taskBatch))

        elseif flag == 2 then -- 完成指定活动所有任务
            local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(activityID)
            if not activityInfo then return handleResult(false) end

            local taskBatch = {}
            for _, task in ipairs(activityInfo.task_info or {}) do
                table.insert(taskBatch, {
                    activityID = activityID,
                    taskID = task.task_id
                })
            end
            
            return handleResult(processBatchTasks(taskBatch))
        else
            return handleResult(false)
        end
    end

    -- 默认处理单个任务
    return handleResult(processSingleTask(activityID, taskID))
end

--SOL仓库自动装上了5个顶级扩容箱：高级材料箱（20030030001
GMHandleLogic.AddFiveBox = function()
    local gmType = EGMType.kGMTypeEquipTopExtensionBox
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.HeroUnlockAll = function()
    local gmType = EGMType.kGMTypeHeroUnlockAll
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

--皮肤全解锁
GMHandleLogic.SkinFraduate = function()
    local gmType = EGMType.kGMTypeWeaponAllSkin
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

--炫彩全解锁
GMHandleLogic.ColorSkinFraduate = function()
    local gmType = EGMType.kGMTypeWeaponAllFancyColorSkin
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ResetAccount = function()
    local gmType = EGMType.kGMTypeDelDB
    return Server.GMServer:DoGMReq(gmType)
end

GMHandleLogic.AdjustTime = function(min, date, time)
    local gmType = EGMType.kGMTypeTimeAdjust
    local args = {min, date, time}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ClearDepositItems = function()
    local gmType = EGMType.kGMTypeClearDeposit
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddDepositCapacity = function(addY)
    local gmType = EGMType.kGMTypeAddDepositCapacity
    local args = {
        addY,
        0,
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddDepositExtNum = function(addExtNum)
    local gmType = EGMType.kGMTypeAddDepositCapacity
    local args = {
        0,
        addExtNum,
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ClearDepositCapacity = function()
    local gmType = EGMType.kGMTypeClearDepositCapacity
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddItem = function(itemId, num, properties)
    if type(properties) == "number" then    
        properties = {properties, 1}
    elseif type(properties) ~= "table" then
        properties = {0, 1, 0}
    end
    local durability = properties[1]
    local bindType = properties[2]
    local limitedtime = properties[3] --时效时间。0是永久，非0，表示距离当前时间+-分钟数。

    local gmType = EGMType.kGMTypeAddItem
    -- if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
    --     gmType = EGMType.kGMTypeMPAddItem
    -- end
    local args = {
        itemId,
        num,
        durability,
        bindType,
        0,
        limitedtime
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.JoinRoom = function(roomid)
    local gmType = EGMType.kGMTypeChatJoinSpecWorldChatRoom
    local args = {
        roomid
    }
    local function roomCallBack(res)
        Module.Chat:SetWorldChatRoomIdAndKey(res.tip)
    end
    return Server.GMServer:DoGMReq(gmType, args, roomCallBack)
end

GMHandleLogic.ShowELOData = function()
    local gmType = EGMType.kGMTypeSettlementGetEloPointInfo
    return Server.GMServer:DoGMReq(gmType)
end

GMHandleLogic.AddRoomRobot = function(roomid, robotNum, robotName)
    local gmType = EGMType.kGMTypeChatAddWoldChatRobot
    local args = {
        roomid,
        robotNum,
        robotName
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.PassCourse = function()
    local gmType = EGMType.kGMTypePassCourse
    return Server.GMServer:DoGMReq(gmType, {})
end

GMHandleLogic.SetCourse = function(CourseId, forceId)
    CourseId = setdefault(CourseId, 0)
    forceId = setdefault(forceId, 0)
    local gmType = EGMType.kGMTypeSetCourse
    local args = {
        CourseId,
        forceId,
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddInsuranceItem = function(itemId, num)
    local gmType = EGMType.kGMTypeAddInsurance
    local args = {
        itemId,
        num
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddTime = function(svrname, offset)
    local gmType = EGMType.kGMTypeTimeAdjust
    local args = {
        svrname,
        offset
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddMatchRobot = function(playerNum, score)
    local matchID = Server.TeamServer:GetMatchId()
    local gmType = EGMType.kGMTypeAddMatchRobot
    local args = {
        matchID,
        playerNum,
        score
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddGlobalMail = function(mailInfo, playerID)
    local gmType = EGMType.kGMTypeAddGlobalMail
    local args = {mailInfo}
    if not playerID then
        local Id = Server.AccountServer:GetPlayerId()
        table.insert(args, Id)
    else
        table.insert(args, playerID)
    end
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddSystemMail = function(mailID, itemInfo)
    local gmType = EGMType.kGMTypeAddSystemMail
    local args = {mailID}
    if type(itemInfo) ~= nil then
        for _, info in ipairs(itemInfo) do
            table.insert(args, info)
        end
    end
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddCurrency = function(currencyId, num)
    local gmType = EGMType.kGMTypeAddItem
    local args = {
        currencyId,
        num,
        0,
        0,
        0
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ExchangeCurrency = function(srcId, srcNum, dstId)
    -- local gmType = EGMType.kGMTypeAddItem
    -- local args = {
    --     srcId,
    --     srcNum,
    --     dstId,
    --     0,
    --     0
    -- }
    -- return Server.GMServer:DoGMReq(gmType, args)
    -- local callback = function (bEnough,lackNum)
    --     loginfo("[Currency] GMHandleLogic ExchangeCurrency bEnough:%s lackNum:%d",tostring(bEnough), lackNum)
    -- end
    -- Module.Currency:CheckEnoughMoney(srcId, srcNum, callback, true)
    Module.Currency:ExchangeByItemId(srcId, srcNum, dstId)
end

GMHandleLogic.MidasBuyGoods = function(ProductId, Num)
    local goodsInfo = {
        product_id = ProductId,
        num = 1,
        business_id = Module.Pay:GetBusinessId(),
    }
    Module.Pay:ReqBuyGoods(goodsInfo)
end

GMHandleLogic.AddMsgMail = function(inviterNick, matchID)
    local time = os.time()
    local inviter = {
        player_id = 0,
        nick_name = inviterNick,
        pic_url = "",
        level = 2,
        gender = EGspGender.kGspGenderMale,
        safehouse_degree = 0,
    }

    local clientSaveInviteInfo = {
        invite_type = 1,
        team_id     = 0,
        match_id    = matchID,
        invite_time = time,
        inviter     = inviter,
        state       = GlobalPlayerStateEnums.EPlayerState_Online,
        flag        = 1,
        GM = true
    }
    -- Module.Mail:NotifyNewInvite(clientSaveInviteInfo)
end

GMHandleLogic.AddSeasonExp = function(exp)
    local gmType = EGMType.kGMTypeAddExp
    local args = {
        exp
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 添加账号等级经验
GMHandleLogic.AddAccountExp = function(exp)
    local gmType = EGMType.kGMTypeAddAccountExp
    local args = {
        exp
    }
    return Server.GMServer:DoGMReq(gmType, args)
end
-------------------shop-----------------
GMHandleLogic.AdjustShopTime = function(p1)
    local gmType = EGMType.kGMTypeTimeAdjust
    local args = {
        p1
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GetCurrentShopTime = function()
    local gmType = EGMType.kGMTypeGetCurTime
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.MallAddCredit = function(p1, p2)
    local gmType = EGMType.kGMTypeMallAddCredit
    local args = {
        p1,
        p2
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.MallAddFavorability = function(p1, p2)
    local gmType = EGMType.kGMTypeMallAddFavorability
    local args = {
        p1,
        p2
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.MallTriggerGift = function(p1, p2)
    local gmType = EGMType.kGMTypeMallTriggerGift
    local args = {
        p1,
        p2
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ResetScavCD = function()
    local gmType = EGMType.kGMTypeSCAVCD
    return Server.GMServer:DoGMReq(gmType, args)
end

--- GM指令：设置玩家能力分数
GMHandleLogic.SetPlayerAbilityScore = function(playerAbilityScore)
    playerAbilityScore = tostring(playerAbilityScore or 0)
    local gmType = 101
    local args = {
        playerAbilityScore
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

--- GM指令：AI填充房间位置
GMHandleLogic.InviteAIToRoom = function()
    local roomID = tostring(Server.RoomServer:GetRoomId() or 0)
    local gmType = 800
    local args = {
        roomID
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.UninstallTalent = function(heroId, slot)
end

for Key, Value in pairs(GMHandleLogic) do
    if type(Value) == "function" then
        GMHandleLogic[Key] = function(...)
            loginfo("[GMHandleLogic] ", Key)
            Value(...)
        end
    end
end

GMHandleLogic.SafeHouseProduce = function()
    local gmType = EGMType.kGMTypeSafehouseProduce
    local args = {}
    local callback = function()
        Module.BlackSite:BlackSiteGetInfoReq()
    end
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

GMHandleLogic.SafeHouseUpgrade = function()
    local gmType = EGMType.kGMTypeSafehouseUpgrade
    local args = {}
    local callback = function()
        Module.BlackSite:BlackSiteGetInfoReq()
    end
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

GMHandleLogic.ScavRevengeEnterTest = function(roomID)
    local gmType = EGMType.kGMTypeMatchRevenge
    local args = {
        roomID
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.SigleSafeHouseUpgrade = function(Id, Level)
    local gmType = EGMType.kGMTypeSafehouseForceUpgrade
    local args = {Id, Level}
    local callback = function()
        Module.BlackSite:BlackSiteGetInfoReq()
    end
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

GMHandleLogic.SafeHouseGraduate = function()
    local gmType = EGMType.kGMTypeSafehouseUpgradeAll
    local args = {}
    local callback = function()
        Module.BlackSite:BlackSiteGetInfoReq()
    end
    return Server.GMServer:DoGMReq(gmType, args, callback)
end
------------- 任务系统GM -------------
-- 任务系统删除玩家db数据
GMHandleLogic.QuestDeleteDB = function()
    local function callback(res)
        if res.result == 0 then
            Server.QuestServer:ResetServerData()
        end
    end
    local gmType = EGMType.kGMTypeQuestDeleteDB
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

-- 任务系统强制接取任务
-- 参数1：任务ID
GMHandleLogic.QuestForceAccept = function(quest_id)
    local gmType = EGMType.kGMTypeQuestForceAccept
    local args = {
        quest_id
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 任务系统强制完成任务
-- 参数1：任务ID
GMHandleLogic.QuestForceFinish = function(quest_id)
    local gmType = EGMType.kGMTypeQuestForceFinish
    local args = {
        quest_id
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 任务系统领取奖励
-- 参数1：任务ID
GMHandleLogic.QuestGetReward = function(quest_id)
    local gmType = EGMType.kGMTypeQuestGetReward
    local args = {
        quest_id
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 任务系统删除玩家指定任务db数据
-- 参数1：任务ID
GMHandleLogic.QuestDeleteQuest = function(quest_id)
    local gmType = EGMType.kGMTypeQuestDeleteQuest
    local args = {
        quest_id
    }
    local function callback(res)
        print(LogUtil.LogTable(res))
        if res.result == 0 then
            local questInfo = Server.QuestServer:GetQuestInfoById(quest_id)
            if questInfo then
                questInfo:ResetQuest()
            end
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

-- 任务系统在后台日志中输出玩家任务db
GMHandleLogic.QuestDumpDBInServerLog = function(player_id)
    if player_id == nil then
        player_id = Server.AccountServer:GetPlayerId()
    end
    local gmType = EGMType.kGMTypeQuestDumpDBInServerLog
    local args = {
        player_id
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 任务模块强制设置目标进度值
-- 参数1：任务ID
-- 参数2：任务目标ID
-- 参数3：新的进度值
GMHandleLogic.QuestForceSetQuestObjection = function(quest_id, obj_id, value)
    local gmType = EGMType.kGMTypeQuestForceSetQuestObjectiveValue
    local args = {
        quest_id,
        obj_id,
        value
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 完成所有已接受任务
GMHandleLogic.QuestForceFinishAllAccepted = function()
    local gmType = EGMType.kGMTypeQuestForceFinishAllAccepted
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 完成所有任务
GMHandleLogic.QuestForceAcceptAndFinishAll = function()
    local gmType = EGMType.kGMTypeQuestForceAcceptAndFinishAll
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

-- 设置任务消耗时间
-- 参数1：任务ID
-- 参数2：任务目标ID
-- 参数3：时间
GMHandleLogic.QuestForceSetQuestConsumetime = function(questId, objId, consumeTime)
    local gmType = EGMType.kGMTypeQuestForceSetObjectiveSpentSeconds
    local args = {
        questId,
        objId,
        consumeTime
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

------------- 任务系统GM -------------
GMHandleLogic.UnlockAllSytsem = function()
    local gmType = EGMType.kGMTypeSafehouseUnlockSystem
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

------------- 一键放入全部15开头的道具(每种一件)GM-------------
-- index 开头的数字
GMHandleLogic.GetFifteenBeginId = function(index)
    local allIetmInfo = {}

    for itemIdStr, itemInfo in pairs(Module.Auction.Config.ItemTable) do
        local newid = string.sub(itemIdStr, 1, 2)
        if newid == tostring(index) then
            table.insert(allIetmInfo, {itemIdStr, itemInfo})
        end
    end

    local itemType
    local filtertable = {}

    for _, v in pairs(allIetmInfo) do
        if v[2].Quality ~= itemType then
            itemType = v.Quality
            table.insert(filtertable, tonumber(v[1]))
        end
    end

    for _, itemId in pairs(filtertable) do
        local gmType = EGMType.kGMTypeAddItem

        local args = {
            itemId,
            1,
            0,
            0,
            0
        }
        Server.GMServer:DoGMReq(gmType, args)
    end
    return
end

GMHandleLogic.GetFifteenBeginId4 = function(index, num, properties)
    local allIetmInfo = {}
    if type(properties) == "number" then    
        properties = {properties, 1}
    elseif type(properties) ~= "table" then
        properties = {0, 1, 0}
    end
    local durability = properties[1]
    local bindType = properties[2]

    for itemIdStr, itemInfo in pairs(Module.Auction.Config.ItemTable) do
        local newid = string.sub(itemIdStr, 1, 4)
        loginfo("[xxww] newid ", newid)
        if newid == tostring(index) then
            table.insert(allIetmInfo, {itemIdStr, itemInfo})
        end
    end
    local itemType
    local filtertable = {}
    for _, v in pairs(allIetmInfo) do
        if v[2].Quality ~= itemType then
            itemType = v.Quality
            table.insert(filtertable, tonumber(v[1]))
        end
    end
    for _, itemId in pairs(filtertable) do
        local gmType = EGMType.kGMTypeAddItem

        local args = {
            itemId,
            num,
            durability,
            bindType,
            0
        }
        Server.GMServer:DoGMReq(gmType, args)
    end
    return
end

------------- 局外下发钥匙到钥匙包GM -------------
GMHandleLogic.KeyGiveKeybag = function()
    local allIetm = {}
    local testTable = Facade.TableManager:GetTable("GameItem")
    for itemIdStr, itemInfo in pairs(testTable) do
        local newid = string.sub(itemIdStr, 1, 4)

        if newid == tostring(1505) then
            table.insert(allIetm, tonumber(itemIdStr))
        end
    end

    for _, item in pairs(allIetm) do
        local gmType = EGMType.kGMTypeAddItem
        local args = {
            item,
            1,
            0,
            0,
            ESlotType.KeyChainContainer
        }
        Server.GMServer:DoGMReq(gmType, args)
    end
    return
end

-----------------------------------------------------------------------
--region 拍卖行相关

GMHandleLogic.ClearAuction = function()
    local gmType = EGMType.kGMTypeDelAuctionTable
    return Server.GMServer:DoGMReq(gmType)
end

---本玩家在拍卖行出售中的道具全部成交（系统购买）
GMHandleLogic.AuctionAutoBuyAllItems = function()
    local gmType = EGMType.kGMTypeAuctionAutoBuyAllItems
    return Server.GMServer:DoGMReq(gmType)
end

---本玩家在拍卖行出售中的道具全部到期
GMHandleLogic.AuctionAllItemsExpired = function()
    local gmType = EGMType.kGMTypeAuctionAllItemsExpired
    return Server.GMServer:DoGMReq(gmType)
end

---拍卖行增加N个出售槽位
GMHandleLogic.AuctionUnlockOneSlot = function(num)
    local gmType = EGMType.kGMTypeAuctionUnlockOneSlot
    local args = {
        num
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

---设定道具的24h成交均价
GMHandleLogic.AuctionSetAveragePrice = function(itemId, averagePrice)
    local gmType = EGMType.kGMTypeAuctionSetAveragePrice
    local args = {
        itemId,
        averagePrice
    }
    return Server.GMServer:DoGMReq(gmType,args)
end

--endregion

--#region 等级解锁
---解锁所有功能
GMHandleLogic.SwitchUnlockALLModule = function()
    local gmType = EGMType.kGMTypeSwitchUnlockModule
    local args = {
        0
    }
    return Server.GMServer:DoGMReq(gmType,args)
end
---解锁对应moduleID的功能
GMHandleLogic.SwitchUnlockModuleByModuleID = function(moduleID)
    local gmType = EGMType.kGMTypeSwitchUnlockModule
    local args = {
        moduleID
    }
    return Server.GMServer:DoGMReq(gmType,args)
end
--#endregion

--#region 藏品仓库
-- 添加藏品道具
GMHandleLogic.AddCollectionItem = function(itemId, num)
    local gmType = EGMType.kGMTypeCollectionAddProp
    local args = {
        itemId,
        num
    }
    return Server.GMServer:DoGMReq(gmType,args)
end
-- 清空藏品仓库
GMHandleLogic.ClearCollections = function()
    local gmType = EGMType.kGMTypeCollectionClear
    return Server.GMServer:DoGMReq(gmType)
end
--#endregion

---设置ds启动的客户端参数--clientdebug
GMHandleLogic.DSClientDebug = function(arg)
    local gmType = EGMType.kGMTypeDsagentDSDebugArg
    local args = {
        arg
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

---显示GPT窗口
GMHandleLogic.GPTShow = function()
    local gmType = EGMType.kGMTypeGPTShow
    local GPT = import "GPToolManager"
    GPT.Get(GetGameInstance()):Show()
    return
end

-- 邮件系统 删除功能
-- ClearType: 邮件类型
-- isClearRedPoint: 是否清除红点
GMHandleLogic.EmailHandle = function(clearType, isClearRedPoint)
    if not isClearRedPoint then
        local gmType = EGMType.kGMTypeClearMail
        local args = {
            clearType
        }
        return Server.GMServer:DoGMReq(gmType, args)
    else
        --红点和已读挂钩，清理即已读
    end
end

--------------------------------------------------------------------------
--- 其他
--------------------------------------------------------------------------
GMHandleLogic.ChangeVideoState = function()
    loginfo(GMHandleLogic.SetVideoState)
    Module.Hall:SetVideoState(not Module.Hall:GetVideoState())
end

GMHandleLogic.GoToSafeHouseIris = function()
    UGameplayBlueprintHelper.DFMOpenLevel(GetGameInstance(), "/Game/Maps/Iris_Entry/Iris_Entry", true, "")
end

GMHandleLogic.ChangeDepositDragConfig = function(angle, distance)
    DepositoryItemView.SetDragConfig(angle, distance)
end

GMHandleLogic.QuitGame = function()
    UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
end

local bDebugInfoVisibility = 1
GMHandleLogic.ToggleDebugInfo = function()
    bDebugInfoVisibility = bDebugInfoVisibility ~ 1
    Module.GM.Config.evtToggleDebugInfo:Invoke(bDebugInfoVisibility)
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        bDebugInfoVisibility == 1 and "EnableAllScreenMessages" or "DisableAllScreenMessages",
        nil
    )
end

GMHandleLogic.ToggleMuteRelease = function(bToggle)
    UIReleaseUtil.ToggleMuteRelease(bToggle)
end

GMHandleLogic.ToggleForceMemoryRemoveRef = function(bToggle)
    UIReleaseUtil.ToggleForceMemoryRemoveRef(bToggle)
end

GMHandleLogic.ToggleLowMemoryRemoveRef = function(bToggle)
    UIReleaseUtil.ToggleLowMemoryRemoveRef(bToggle)
end

GMHandleLogic.ToggleLowMemoryRemoveChildRef = function(bToggle)
    UIReleaseUtil.ToggleLowMemoryRemoveChildRef(bToggle)
end

GMHandleLogic.ToggleHighMemoryRemoveRef = function(bToggle)
    UIReleaseUtil.ToggleHighMemoryRemoveRef(bToggle)
end

GMHandleLogic.ToggleRemoveRefToDefault = function()
    UIReleaseUtil.ToggleRemoveRefToDefault()
end

GMHandleLogic.ToggleAutoClearSubUI = function(bToggle)
    UIReleaseUtil.ToggleAutoClearSubUI(bToggle)
end

GMHandleLogic.FixPlayerStart = function(locX, locY)
    -- RoomDataServer:Get().GMFixPlayerStartLoc:Invoke(locX, locY)
end

GMHandleLogic.ShowHeroUI = function()
end

GMHandleLogic.ShowFashionUI = function()
end

GMHandleLogic.AddFashionSuit = function(suitId)
end

GMHandleLogic.AddMerit = function(merit)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMAddMerit(merit)
    end
end

GMHandleLogic.SetGameAIDifficulty = function(ailevel)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSetGameAIDifficulty(ailevel)
    end
end

GMHandleLogic.GMRestartAISpawners = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMRestartAISpawners()
    end
end

GMHandleLogic.GMResetAISpawnTemplate = function(templateLevel)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local TemplateName = tostring(templateLevel)
        localCtrl.PlayerGMComponent:GMResetAISpawnTemplate(TemplateName)
    end
end

GMHandleLogic.SpawnVehicle = function(vehicle)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local vehicleName = tostring(vehicle)
        localCtrl.PlayerGMComponent:GMSpawnVehicle(vehicleName)
    end
end

GMHandleLogic.ForceSetVehicleAimAssistConfig = function(ID)
    local cmd = "Vehicle.ForceUseAssistAimConfig "..ID
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), cmd, nil)
end

GMHandleLogic.VehicleRollOver90 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMRotateCurrentDriveVehicle(90,0,0)
    end
end

GMHandleLogic.VehicleRollOver180 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMRotateCurrentDriveVehicle(180,0,0)
    end
end

GMHandleLogic.EnableVehicleAnimOptimization = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Vehicle.UseOptimizedCharacterAnimInVehicle 1", nil)
end

GMHandleLogic.DisableVehicleAnimOptimization = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Vehicle.UseOptimizedCharacterAnimInVehicle 0", nil)
end

GMHandleLogic.EnableDebugVehicleHealth = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMEnableDebugVehicleHealth()
    end
end

GMHandleLogic.SpawnTotalVehicles = function(dist)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSpawnTotalVehicles(dist)
    end
end

GMHandleLogic.SpawnProfileVehicles = function(dist)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSpawnProfileVehicles(dist)
    end
end

GMHandleLogic.ToggleVehicleDebugHUD = function(HUDState)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:EnableVehicleDebug(HUDState ~= 0)
    end
end

GMHandleLogic.OpenScavengers = function()
    Module.Insurance:OpenInsuranceMainPanel()
end

GMHandleLogic.SendExec = function(param1, param2, param3)
    if param1 ~= nil then
        local command = tostring(param1)

        if param2 ~= nil then
            command = command .. " " .. tostring(param2)
        end
        if param3 ~= nil then
            command = command .. " " .. tostring(param3)
        end

        loginfo("GMConsoleCommand：" .. command)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), command, nil)
    end
end

GMHandleLogic.GiveItem = function(param1, param2, param3)
    if param1 ~= nil then
        local command = tostring(param1)

        if param2 ~= nil then
            command = command .. " " .. tostring(param2)
        end
        if param3 ~= nil then
            local num = param3[1]
            local AttachPos = EAttachPosition.Attach_None
            local bindType = param3[2]

            -- command = command .. " " .. tostring(num) .. " " ..tostring(bindType)
            command = string.format("%s %s %s %s", command, num, 0, bindType)
        end

        -- command = command .. " 0"

        loginfo("GMConsoleCommand：" .. command)
        local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
        if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
            localCtrl.PlayerGMComponent.ProcessExecFunc(localCtrl.PlayerGMComponent, command)
        end
    end
end

local function ForeachCharacterEquipComp(itemId, func)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter ~= nil and localCharacter.CharacterEquip ~= nil then
        for _, i in pairs(EquipmentType) do
            localCharacter.CharacterEquip[func](localCharacter.CharacterEquip, i, itemId)
        end
    end
end

GMHandleLogic.Equip = function(itemId)
    ForeachCharacterEquipComp(itemId, "TestServerEquip")
end

GMHandleLogic.Unequip = function(itemId)
    ForeachCharacterEquipComp(itemId, "TestServerUnequip")
end

local function CallGMCompFunc(func)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent[func](localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.TeleportToMark = function()
    CallGMCompFunc("GMGotoMark")
end

GMHandleLogic.TeleportToMarkNav = function()
    CallGMCompFunc("GMGotoMarkWithNavAdjust")
end

GMHandleLogic.TeleportToLoc = function(locX, locY, locZ)
    locX = setdefault(locX, 0)
    locY = setdefault(locY, 0)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        if locZ == nil then
            local loc2D = FVector2D(locX, locY)
            localCtrl.PlayerGMComponent.GMGoto2D(localCtrl.PlayerGMComponent, loc2D)
        else
            if type(locZ) ~= "number" then
                locZ = tonumber(locZ)
            end
            local loc = FVector(locX, locY, locZ)
            localCtrl.PlayerGMComponent.GMGoto(localCtrl.PlayerGMComponent, loc, 10, false)
        end
    end
end

GMHandleLogic.SwitchFTPP = function()
    CallGMCompFunc("ToggleXPP")
end

GMHandleLogic.KillAI = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMKillAI(localCtrl.PlayerGMComponent, 100)
    end
end

GMHandleLogic.KillAllAI = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMKillAI(localCtrl.PlayerGMComponent, 1000000)
    end
end

GMHandleLogic.GMMPFulfillAI = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMMPFulfillAI(localCtrl.PlayerGMComponent, param1)
    end
end

GMHandleLogic.GMMPRemoveAllAI = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMMPRemoveAllAI(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMMPResetDifficultPool = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMMPResetDifficultPool(localCtrl.PlayerGMComponent, param1)
    end
end

GMHandleLogic.GMMPResetDifficultName = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMMPResetDifficultName(localCtrl.PlayerGMComponent, param1)
    end
end

GMHandleLogic.EnablePvpAIDebug = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.EnablePvpAIDebug(localCtrl.PlayerGMComponent, param1)
    end
end

GMHandleLogic.DisablePvpAIDebug = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.DisablePvpAIDebug(localCtrl.PlayerGMComponent, param1)
    end
end

GMHandleLogic.SpawnPvpAiAround = function(param1, param2, param3)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.SpawnPvpAiAround(localCtrl.PlayerGMComponent, param1, param2, param3)
    end
end

---大战场PreMatch兵种选择阶段显示队友模型
---@param param1 any
GMHandleLogic.GMMPPreMatchShowTeammemberCharacterModel = function(param1)
    PreMatchChooseArmDisplayLogic.ShowTeammemberCharacterModel=true
end

---大战场PreMatch跳过兵种选择阶段
---@param param1 any
GMHandleLogic.GMMPPreMatchDisableChooseArm = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMBreakthroughClosePreMatchChooseArm(true)
    end
end

---大战场PreMatch显示兵种选择阶段
---@param param1 any
GMHandleLogic.GMMPPreMatchEnableChooseArm = function(param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMBreakthroughClosePreMatchChooseArm(false)
    end
end

GMHandleLogic.GiveEquipments = function()
    local params = {}
    table.insert(params, "giveitem 11080006001 1 108")
    table.insert(params, "giveitem 11070004001 1 107")
    table.insert(params, "giveitem 11050002001 1 105")
    table.insert(params, "giveitem 14010000003 10")
    table.insert(params, "giveitem 19010050002 5")
    table.insert(params, "giveitem 15020060002 1")
    table.insert(params, "giveitem 15030080001 1")
    table.insert(params, "giveitem 10010000002 1 111")
    table.insert(params, "giveitem 10010000015 1 112")
    table.insert(params, "giveitem 13990120000 300")
    table.insert(params, "giveitem 13990220000 300")
    table.insert(params, "giveitem 13990520000 300")
    table.insert(params, "giveitem 11010001001 1 101")

    for _, command in ipairs(params) do
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), command, nil)
    end
end

GMHandleLogic.CallCharacterFunc = function(func)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter ~= nil then
        if localCharacter[func] then
            localCharacter[func](localCharacter)
        end
    end
end

GMHandleLogic.SendGMCompCommand = function(param1, param2, param3)
    if param1 ~= nil then
        local command = tostring(param1)

        if param2 ~= nil then
            command = command .. " " .. tostring(param2)
        end
        if param3 ~= nil then
            command = command .. " " .. tostring(param3)
        end

        command = command .. " 0"

        loginfo("GMConsoleCommand：" .. command)
        local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
        if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
            localCtrl.PlayerGMComponent.ProcessExecFunc(localCtrl.PlayerGMComponent, command)
        end
    end
end

GMHandleLogic.OpenGMPanel = function(panelName)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.ShowGMPanel(localCtrl.PlayerGMComponent, panelName)
    end
end

GMHandleLogic.GMShowFps = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMShowFps(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMAllowFourFingersConsole = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMAllowFourFingersConsole(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMNotAllowFourFingersConsole = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMNotAllowFourFingersConsole(localCtrl.PlayerGMComponent)
    end
end

local toggleShowDamageText = false
GMHandleLogic.GMToggleShowDamageText = function()
    -- local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    -- if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
    --     localCtrl.PlayerGMComponent.GMToggleShowDamageText(localCtrl.PlayerGMComponent)
    -- end
    if toggleShowDamageText then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "EnableDamageHud 0", nil)
        toggleShowDamageText = false
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "EnableDamageHud 1", nil)
        toggleShowDamageText = true
    end
   
end

GMHandleLogic.UpdateVisiable = function()
    UGameplayBlueprintHelper.OnAllLevelLoaded(GetGameInstance())
end

GMHandleLogic.UseSkeletonAnim = function()
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter ~= nil and localCharacter.GPAnimProxy ~= nil then
        localCharacter.GPAnimProxy.CameraAnimationUseAddMethod = false
    end
end

GMHandleLogic.LegBroke = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:AddBuff(4004,CharacterPart.RightLeg)
        localCtrl.PlayerGMComponent:AddBuff(4004,CharacterPart.LeftLeg)
    end
end

GMHandleLogic.ShowWeaponLog = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil then
        localCtrl.ShowWeaponPanel(localCtrl, 2)
    end
end

GMHandleLogic.ShowAnimLog = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil then
        localCtrl.ShowAnimPanel(localCtrl, 0)
    end
end

GMHandleLogic.WeaponAssistedAiming = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "weapon.FireAssistedAimingDebugEnable 1", nil)

    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil then
        localCtrl.ShowWeaponPanel(localCtrl, 3)
    end
end

GMHandleLogic.ToggleOwnerGOD = function()
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter ~= nil then
        local newGod = setdefault(newGod, true)
        if localCharacter.OwnerIsGod then
            newGod = false
        else
            newGod = true
        end
        localCharacter.GMSetOwnerGOD(localCharacter, newGod)
    end
end

GMHandleLogic.CleanBuff = function()
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter ~= nil and localCharacter.BuffSystemComponent ~= nil then
        localCharacter.BuffSystemComponent.TestRemoveAllBuffs(
            localCharacter.BuffSystemComponent
        )
    end
end

GMHandleLogic.TestAddBuff = function(buffID, part)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter ~= nil and localCharacter.BuffSystemComponent ~= nil then
        addPart = CharacterPart.None
        if part ~= nil and part > 0 then
            addPart = part % 1000
        end
        localCharacter.BuffSystemComponent.TestAddBuff(
            localCharacter.BuffSystemComponent,
            tonumber(buffID),
            addPart,
            nil
        )
    end
end


GMHandleLogic.CommitLog = function()
    UUploadToolsModuleBPTools.UploadLog(GetGameInstance())
end

GMHandleLogic.CreateLogWidget = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil then
        ULogVisualizerInGame.CreateLogWidget(localCtrl)
    end
end

GMHandleLogic.DestroyLogWidget = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil then
        ULogVisualizerInGame.DestroyLogWidget(localCtrl)
    end
end

GMHandleLogic.SwitchCharacterAvatar = function(avatarId)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local Comp = localCharacter.CharacterFashionComponent
        if Comp and avatarId then
            Comp:DebugSpecificCharacterAvatar(avatarId)
        end
    end
end

GMHandleLogic.SwitchCharacterAvatar_DS = function(avatarId)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local Comp = localCharacter.CharacterFashionComponent
        if Comp and avatarId then
            Comp:SwitchCharacterAvatarIDOnDS(avatarId)
        end
    end
end
GMHandleLogic.SetCharacterIsPrisoner_DS = function()
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local Comp = localCharacter.CharacterFashionComponent
        if Comp then
            Comp:SetPrisonerStateOnDS(true)
        end
    end
end

GMHandleLogic.SetCharacterIsNotPrisoner_DS = function()
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local Comp = localCharacter.CharacterFashionComponent
        if Comp then
            Comp:SetPrisonerStateOnDS(false)
        end
    end
end

GMHandleLogic.SwitchCharacterWatch_DS = function(watchId)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local Comp = localCharacter.CharacterFashionComponent
        if Comp and watchId then
            Comp:SwitchCharacterWatchID(watchId)
        end
    end
end

GMHandleLogic.SetPilotState = function(bUsePilot)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        localCharacter:SetPilotState(bUsePilot)
    end
end

GMHandleLogic.GMSpawnDFMCharacterInLocation = function(avatarId)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local position = localCharacter.GetActorLocation(localCharacter)
        local Cmd = string.format("GMSpawnDFMCharacterInLocation %s 0 0 0 0 0 %f %f %f", avatarId, position.X, position.Y + 100, position.Z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), Cmd)
    end
end


GMHandleLogic.SetCharacterEquip = function(equipId, isRemove)
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then  
        local Comp = localCharacter.CharacterFashionComponent
        if Comp and equipId then
            Comp:DebugSetEquipAppearance(equipId, isRemove)
        end
    end
end

------------- 修改对局时长 GM -------------
GMHandleLogic.SetMatchTime = function(time)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetMatchTime(localCtrl.PlayerGMComponent, time)
    end
end
------------- SOL时间线事件触发 GM-------------
GMHandleLogic.TriggleSOLTimeLineEvent = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggleSOLTimeLineEvent(localCtrl.PlayerGMComponent)
    end
end

------------- 设置蓝图武器箱SpawnID GM-------------
GMHandleLogic.GMRefreshGenerator = function(SpawnID)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMRefreshGenerator(localCtrl.PlayerGMComponent,SpawnID)
    end
end

------------- SOL触发事件POI标记 GM-------------
GMHandleLogic.GMTriggerSOLEventPOI = function(eventType,enable)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
		local bEnable = enable ~= 0
        localCtrl.PlayerGMComponent.GMTriggerSOLEventPOI(localCtrl.PlayerGMComponent,eventType,bEnable)
    end
end

------------- 时间线控制 GM -------------
GMHandleLogic.SetSOLTimelineConfig = function(enable, interval)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local bEnable = enable ~= 0
        localCtrl.PlayerGMComponent.GMSetSOLTimelineConfig(localCtrl.PlayerGMComponent, bEnable, interval)
    end
end

GMHandleLogic.SetSOLParatrooperEnable = function(enable)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local bEnable = enable ~= 0
        localCtrl.PlayerGMComponent.GMSetSOLParatrooperEnable(localCtrl.PlayerGMComponent, bEnable)
    end
end

------------- 大战场播放角色音频 GM-------------
GMHandleLogic.TriggleBattleFieldPlayerAudio = function(InType)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggleBattleFieldPlayerAudio(localCtrl.PlayerGMComponent,InType)
    end
end

------------- 大战场结束本局 GM--U-----------
GMHandleLogic.BattleFieldEndMatch = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBattleFieldEndMatch(localCtrl.PlayerGMComponent)
    end
end
------------- 大战场攻防直接胜利 GM-------------
GMHandleLogic.BattleFieldEndMatchCamp = function(camp)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBattleFieldEndMatchCamp(localCtrl.PlayerGMComponent,camp)
    end
end

------------- SOL获得超级负重装备 GM-------------
GMHandleLogic.GMGetOverLoadEquipment = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMGetOverLoadEquipment(localCtrl.PlayerGMComponent,2)
    end
end

------------- 隐藏小地图 GM-------------
GMHandleLogic.ToggleHideMiniMap = function(InHide)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMToggleHideMiniMap(localCtrl.PlayerGMComponent, InHide)
    end
end


GMHandleLogic.SetSOLParatrooperRealNumLimit = function(TotalNum, NumPerZone)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetSOLParatrooperRealNumLimit(localCtrl.PlayerGMComponent, TotalNum, NumPerZone)
    end
end

GMHandleLogic.SetSOLParatrooperFakeNumLimit = function(TotalNum, NumPerZone)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetSOLParatrooperFakeNumLimit(localCtrl.PlayerGMComponent, TotalNum, NumPerZone)
    end
end


GMHandleLogic.CallAKuroshioShell = function(Delay)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggerAShellSpawn(localCtrl.PlayerGMComponent, Delay)
    end
end

GMHandleLogic.GetSOLEventType = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local id = localCtrl.PlayerGMComponent.GMGetSOLTimelineEventID(localCtrl.PlayerGMComponent)
        if id == 0 then
            Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMBigEventEmpty)
        elseif id == 1 then
            Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMBigEventBoom)
        elseif id == 2 then
            Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMBigEventArm)
        end
    end
end


GMHandleLogic.SetShellingFX = function(enable)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local bEnable = enable ~= 0
        localCtrl.PlayerGMComponent.GMSetShellingFX(localCtrl.PlayerGMComponent, bEnable)
    end
end

GMHandleLogic.TriggerSOLTrain = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggerSOLTrain(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.HackSOLTrain = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMHackSOLTrain(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMReducePlayerExitCountDownTime = function(ReduceCountDownTime)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMReducePlayerExitCountDownTime(localCtrl.PlayerGMComponent, ReduceCountDownTime)
    end
end

GMHandleLogic.GMReducePlayerExitEscapeCount = function(ReduceEscapeCount)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMReducePlayerExitEscapeCount(localCtrl.PlayerGMComponent, ReduceEscapeCount)
    end
end

GMHandleLogic.GMTriggerFirework = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggerFirework(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.SetSOLIntroEventState = function(EventIds)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetSOLIntroEventState(localCtrl.PlayerGMComponent, EventIds)
    end
end

GMHandleLogic.ToggleTrainSOL = function(enable)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        Enable = enable ~= 0
        localCtrl.PlayerGMComponent.GMToggleTrainSOL(localCtrl.PlayerGMComponent, Enable)
    end
end

GMHandleLogic.ForceSOLMajorEvent = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMForceSOLMajorEvent(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.ForceGMEvacuationEvent = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMForceSOLEvacuationEvent(localCtrl.PlayerGMComponent)
    end
end

------------- 破损物 GM -------------

GMHandleLogic.GMServerBreakableItemSetHugeDamage = function(isOpen)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakableItemSetHugeDamage(localCtrl.PlayerGMComponent, isOpen)
    end
end

GMHandleLogic.GMServerBreakableBreakAll = function ()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMServerBreakableBreakAll(localCtrl.PlayerGMComponent)
    end
end



GMHandleLogic.GMChangeBreakableItemMatrial = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMChangeBreakableItemMatrial(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMSwitchBreakableItemSkeletalMeshLodControl = function(isOpen)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSwitchBreakableItemSkeletalMeshLodControl(isOpen)
    end
end

GMHandleLogic.GMSwitchBreakableItemDestoryTime = function(isNeverDestroy)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSwitchBreakableItemDestoryTime(isNeverDestroy)
    end
end


------------- 改变TOD地图类型 GM -------------
GMHandleLogic.GMChangeTODType = function(type,param2,param1)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMChangeTODType(localCtrl.PlayerGMComponent, type)
    end
end

------------- 白点测距 GM-------------
GMHandleLogic.OpenWhiteDotDistance = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggleInteractorWhiteDotDistance(localCtrl.PlayerGMComponent,true)
    end
end
GMHandleLogic.CloseWhiteDotDistance = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMTriggleInteractorWhiteDotDistance(localCtrl.PlayerGMComponent,false)
    end
end

GMHandleLogic.OpenAllDoor = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMOpenAllDoor(localCtrl.PlayerGMComponent)
    end
end

------------- SOL CutScene GM -------------
GMHandleLogic.PlaySOLCutScene = function(SOLExitPointId)
    local gameInst = GetGameInstance()
    UDFMGameplayBlueprintHelper.GMPlayExitCutScene(gameInst, SOLExitPointId)
end

-----------------------------------------------------------------------
--region 大世界玩法
GMHandleLogic.EnterSafeHouse = function()
    Module.IrisSafeHouse:EnterSafeHouse()
end

GMHandleLogic.ExitSafeHouse = function()
    Module.IrisSafeHouse:ExitSafeHouse()
end

GMHandleLogic.EnterIrisWorld = function(spotGroup, bNewLocalDS, bReqDSA)
    Module.IrisSafeHouse:EnterIrisWorld(spotGroup, bNewLocalDS, bReqDSA)
end

GMHandleLogic.ActivateMission = function(missionID, _temp, _temp2)
    Module.IrisSafeHouse:ActivateMission(missionID)
end
--endregion
-----------------------------------------------------------------------

---重置当前赛季人物等级
GMHandleLogic.ResetCurSeasonLevel = function()
    local gmType = EGMType.kGMTypeResetLevel
    return Server.GMServer:DoGMReq(gmType)
end

-----------------------------------------------------------------------
--- 匹配相关
GMHandleLogic.QuickMatch3C = function(matchId)
end

GMHandleLogic.QuickScavGame = function()
end

GMHandleLogic.ShowQuickTestBtn = function(_)
    Module.GM.Config.evtShowQuickTest:Invoke()
end

GMHandleLogic.QuickInGame = function(matchModeId)
    local gmType = EGMType.kGMTypeMatchAlone
    local args = {
        matchModeId
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

--开启匹配入局检查
GMHandleLogic.EnableMatchClientCheck = function(num)
    Module.IrisSafeHouse:EnableMatchClientCheck(num~=0)
end

------------- 商人好感度增加 GM -------------
GMHandleLogic.MallSeachFavorability = function(p1)
    local _total = 0
    local OnGetMerchantsRes = function(res)
        if res.result == 0 then
            for _, merchant in ipairs(res.merchants) do
                if merchant.id == p1 then
                    _total = merchant.intimacy
                    Module.CommonTips:ShowSimpleTip("与id为" .. p1 .. "商人好友度：" .. _total)
                    return
                end
            end
        end
    end

    local req = pb.CSMallGetMerchantsReq:New()
    req:Request(OnGetMerchantsRes)
end

GMHandleLogic.LoginByOpenId = function(ip, port, openId)
    local serverKey = "customize"
    Facade.ConfigManager:SetString("customizeIpPort", string.format("%s:%s",ip,port))
    Server.SDKInfoServer:SaveServerAddr(serverKey)
    -- local name = ip .. ":" .. port
    -- Server.SDKInfoServer:SetServerAddr(ip, port, name)
    Server.SDKInfoServer:SetOpenId(openId)
    Server.SDKInfoServer:TrySDKConnect()
end

GMHandleLogic.SettleClientSucceed = function(gmInfo)
    local s = require "DFM.Business.Module.GMModule.Content.SettlementContentSucceed"
    Server.SettlementServer:ParseSettlementInfo(s)
end

GMHandleLogic.SettleClientFail = function(gmInfo)
    logerror("GMHandleLogic.SettleClientFail")
    local s = "testCrashSightLyl"
    Module.GCloudSDK:ReportLuaErrTraceBack(s)
    -- Server.SettlementServer:ParseSettlementInfo(s)
    -- Module.Settlement:ShowSettlementPanel()
end

GMHandleLogic.SettleClientSucceedTeam = function(gmInfo)
    local s = require "DFM.Business.Module.GMModule.Content.SettlementContentSucceedTeam"
    Server.SettlementServer:ParseSettlementInfo(s)
    Module.Settlement:ShowSettlementPanel()
end

GMHandleLogic.SettleClientFailTeam = function(gmInfo)
    local s = require "DFM.Business.Module.GMModule.Content.SettlementContentFailTeam"
    Server.SettlementServer:ParseSettlementInfo(s)
    Module.Settlement:ShowSettlementPanel()
end

GMHandleLogic.ActivateTalent = function(talentId, heroId, slot)
    local talents = {{id = talentId, equiped_hero = heroId, slot = slot}}
end

GMHandleLogic.AddTeammate = function(teamID)
    teamID = teamID or Server.TeamServer:GetTeamID()
    local gmType = EGMType.kGMTypeTeamAddMem
    local args = {
        tostring(teamID or 0)
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

------------- 改装台 GM -------------
GMHandleLogic.EnterAssembleEdit = function()
    Module.Gunsmith:OpenCameraEditor()
end

GMHandleLogic.InstallTalent = function(talentId, heroId, slot)
end

GMHandleLogic.ChangeRoleQualityValue = function(cardId, value)
    local gmType = EGMType.kGMTypeChangeQuality
    local args = {
        cardId,
        value
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.RoleQualityGod = function()
    local gmType = EGMType.kGMTypeSetMaxAllQuality
    local args = {}
    return Server.GMServer:DoGMReq(gmType, args)
end

------------- 兵种GM -------------
GMHandleLogic.SetBattleClass = function(BattleClassId)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        local AbilityIdArr = {}
        localCtrl.PlayerGMComponent:GMSetBattleClass(BattleClassId, AbilityIdArr)
    end
end

GMHandleLogic.AddS3AmoredCarHealth = function(addedHealth)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:TestGMS3ArmoredCarAddHP(addedHealth)
    end
end

GMHandleLogic.PrintIntelligentPreset = function(armId, planLevel)
    Module.ArmedForce:DebugShowSingleRecommendation(armId, planLevel)
end

GMHandleLogic.OpenDolbyAudio = function()
    loginfo("[GMHandleLogic.OpenDolbyAudio] ")
    -- ULuaExtension.OpenDolbyAudio()
    Facade.SoundManager:SetDolbyPluginEnable(true)
end

GMHandleLogic.CloseDolbyAudio = function()
    loginfo("[GMHandleLogic.CloseDolbyAudio] ")
    -- ULuaExtension.CloseDolbyAudio()
    Facade.SoundManager:SetDolbyPluginEnable(false)
end

GMHandleLogic.BeginRecordWwiseProf = function()
    loginfo("[GMHandleLogic.BeginRecordWwiseProf] ")
    UGPAudioStatics.StartMonitorRecord()
end

GMHandleLogic.StopRecordWwiseProf = function()
    loginfo("[GMHandleLogic.StopRecordWwiseProf] ")
    UGPAudioStatics.StopMonitorRecord()
end

GMHandleLogic.Close3PPriorityNumFunctionLimit = function()
    loginfo("[GMHandleLogic.Close3PPriorityNumFunctionLimit] ")
    ULuaExtension.Close3PPriorityNumFunctionLimit()
end

GMHandleLogic.Open3PPriorityNumFunctionLimit = function()
    loginfo("[GMHandleLogic.Open3PPriorityNumFunctionLimit] ")
    ULuaExtension.Open3PPriorityNumFunctionLimit()
end

GMHandleLogic.ShowAllHUD = function()
    ULuaExtension.ShowInGameHud(GetWorld(), true)
end

GMHandleLogic.HideAllHUD = function()
    ULuaExtension.ShowInGameHud(GetWorld(), false)
end

GMHandleLogic.ShowCurrUI = function()
    local currView = Facade.UIManager:GetCurrentStackUI()
    if not currView then
        return nil
    end
    currView:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
end

GMHandleLogic.HideCurrUI = function()
    local currView = Facade.UIManager:GetCurrentStackUI()
    if not currView then
        return nil
    end
    currView:SetVisibility(ESlateVisibility.Hidden)
end

GMHandleLogic.ToggleGrabMode = function()
    ItemView.TOGGLE_GRAB_MODE = true
end

GMHandleLogic.LocalizationTextCheck = function(sourceString, newString, namespace, key)
    return DFMLocalizationManager:UpdateDisplayString(sourceString, newString, namespace, key)
end

GMHandleLogic.LocalizationUpdate = function(namespace, key, sourceSting, cultureStrings)
    return DFMLocalizationManager:AddPolyglotData(namespace, key, sourceSting, cultureStrings)
end

GMHandleLogic.GetDisplayString = function(namespace, key)
    return DFMLocalizationManager:GetDisplayString(namespace, key)
end

GMHandleLogic.ResetLocalPlayerState = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMResetPlayerState()
    end
end   

GMHandleLogic.SetSceneRenderStatus = function(bool)
    if bool == 0 then
        ULuaExtension.LuaSetWorldRenderingStatus(GetWorld(), false)
    elseif bool == 1 then
        ULuaExtension.LuaSetWorldRenderingStatus(GetWorld(), true)
    end
end   

-- 模拟开箱
GMHandleLogic.GMSimulateOpenBox = function(Times)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSimulateOpenBox(Times)
    end
end

-- 局内SOL任务
GMHandleLogic.GMAcceptNewQuest = function(QuestId)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMAcceptNewQuest(QuestId)
    end
end

-- 局内合同
GMHandleLogic.GMAcceptNewContractQuest = function(QuestId)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMAcceptNewContractQuest(QuestId)
    end
end

GMHandleLogic.GMFinishQuest = function(QuestId, QuestObjectiveId)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMFinishQuest(QuestId, QuestObjectiveId)
    end
end

-- 拉起并连接ds 文档：https://iwiki.woa.com/pages/viewpage.action?pageId=**********
-- otherInfos: ["",0,0] = [DS版本号,PlayerId,RoomId]
GMHandleLogic.DSStartAndConnect = function(mapName, dcServer, otherInfos)
    --直连不需要无缝
    GMHandleLogic.GMDisableSeamlessEntry()
    local server = dcServer and dcServer ~= "" and dcServer or "g.dfm.qq.com:35400"
    local version = VersionUtil.GetVersionFull()
    if otherInfos ~= nil and otherInfos[1] ~= "" then
        version = otherInfos[1]
    end

    local playerId = Server.AccountServer:GetPlayerIdStr()
    if otherInfos ~= nil and otherInfos[2] ~= 0 then
        loginfo('playerid '..otherInfos[2])
        playerId = otherInfos[2]
    end
    local roomId = "0"
    if otherInfos ~= nil and otherInfos[3] ~= 0 then
        roomId = otherInfos[3]
    end
    local randSeed = nil
    if otherInfos ~= nil and otherInfos[4] ~= 0 then
        randSeed = otherInfos[4]
    end
    
    local fetchDSRoomUrl = ""
    if nil ~= randSeed then
        fetchDSRoomUrl = string.format("http://%s/launch?mapname=%s&playerid=%s&version=%s&roomid=%s&randseed=%s", 
        server, mapName, playerId, version, roomId, randSeed)
    else
        fetchDSRoomUrl = string.format("http://%s/launch?mapname=%s&playerid=%s&version=%s&roomid=%s", 
        server, mapName, playerId, version, roomId)
    end


    Module.CommonTips:ShowDelayWindow(5.0)
    loginfo("[DSDirectConnect] connect to dc:", fetchDSRoomUrl)
    Module.CommonTips:ShowSimpleTip("[DSDirectConnect] connecting to dc " .. fetchDSRoomUrl, 5.0)
    Facade.ResourceManager:HttpLoadResources(nil, {fetchDSRoomUrl}, function(mapUrl2ResIns)
        -- 回包格式{"code":13001,"ip":"","port":0,"playerId":0}
        Module.CommonTips:CloseDelayWindow()
        Module.CommonTips:CloseSimpleTip()
        local jsonData = mapUrl2ResIns[fetchDSRoomUrl]
        if not jsonData or jsonData == "" then
            Module.CommonTips:ShowSimpleTip("[DSDirectConnect] server response empty data")
            return
        end
        local Json = require("DFM.YxFramework.Plugin.Json.Json").createJson()
        local loginInfo = Json.decode(jsonData)
        if not loginInfo then
            Module.CommonTips:ShowSimpleTip("[DSDirectConnect] server response data decode fail")
            return
        end
        if not loginInfo.code or loginInfo.code ~= 0 then
            local tips = "[DSDirectConnect] server response error code: " .. loginInfo.code
            if loginInfo.code == 130001 then
                tips = "[DSDirectConnect] player id not in white list, please contact @zhangyudeng" 
            end
            Module.CommonTips:ShowSimpleTip(tips)
            return
        end

        GMHandleLogic.DSDirectConnect(loginInfo.ip, loginInfo.port, loginInfo.playerId, loginInfo.mapId, loginInfo.roomid, loginInfo.randSeed)
    end,
    nil, nil, EHttpContentType.String)
end

-- 直接连DS
GMHandleLogic.DSDirectConnect = function(ip, port, playerId, mapId, roomId, randSeed)
    if not playerId or playerId == "" then
        playerId = Server.AccountServer:GetPlayerId()
    end
    if not mapId then
        mapId = 22 -- iris
    end
    if not roomId then
        roomId = 0
    end
    local command = ""
    if nil ~= randSeed then
        command = string.format("%s:%s?PlayerId=%s?MapId=%s?DSRoomId=%s?RandSeed=%s", ip, port, playerId, mapId, roomId, randSeed)
    else
        command = string.format("%s:%s?PlayerId=%s?MapId=%s?DSRoomId=%s", ip, port, playerId, mapId, roomId)
    end

    --直连不需要无缝
    GMHandleLogic.GMDisableSeamlessEntry()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"Game.EnableClientSeamlessTravel 0",nil)
    Module.CommonTips:ShowSimpleTip("[DSDirectConnect] begin to connect " .. command)
    Module.CommonTips:ShowDelayWindow(10.0)
    loginfo("DSDirectConnect logininfo:", command)
    Server.MatchServer.Events.flowEvtRoomStartMatchSuccess:Invoke(command)
    --UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), command, nil)
end

GMHandleLogic.EnterMPHallEquipment = function()
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.ModeHall then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMPleaseOpenHall1)
    else
        Module.ArmedForce:ShowMainMPPanel()
        Module.GM.OnCloseGM()
    end
end

GMHandleLogic.EnterMPPreparationEquipment = function()
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.ModeHall then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMPleaseOpenHall2)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.PrepareMPMainPanel)
        Module.GM.OnCloseGM()
    end
end

GMHandleLogic.OpenCultureResLoad = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "r.bIsLoadCultureRes 1",
        nil)
    Module.CommonTips:ShowSimpleTip(NSLOCTEXT("GMModule", "Lua_GM_OpenLoadCultureRes","语言包加载已开启"))
end

GMHandleLogic.ManualReportLog = function()
    Module.Tgpa:ReportLog()
end

GMHandleLogic.RedeployConfig = function()
    local PanelConfigName = "BattleFieldDeployView"
    local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
    logwarning("[killydu] ABaseHUDWrapper:Imp_OnShowPanel ", PanelConfigName, " ", hudLayerController)
    if hudLayerController then
        hudLayerController:AddState(EGameHUDState.GHS_Redeploy, true)
        hudLayerController:ShowHudByName(PanelConfigName)
    end
    Timer.DelayCall(1, function(PanelConfigName)
        loginfo("[killydu] SaveRedeployConfig")
        local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
        local RedeployView = hudLayerController:GetHudByName(PanelConfigName)
        RedeployView:ChangeCameraLocation()
    end, self, PanelConfigName)
    
    Timer.DelayCall(3, function(PanelConfigName)
        loginfo("[killydu] SaveRedeployConfig")
        local hudLayerController = Facade.UIManager:GetLayerControllerByType(EUILayer.HUD)
        local RedeployView = hudLayerController:GetHudByName(PanelConfigName)
        RedeployView:SaveRedeployConfig()
    end, self, PanelConfigName)
end

GMHandleLogic.ExitAccount = function()
    loginfo("GMHandleLogic.ExitAccount",_WITH_EDITOR == 1)
    if _WITH_EDITOR == 1 then
        Module.Login:BackToLogin()
    else
        Module.Login:Logout()
    end
end

GMHandleLogic.DebugWeaponSceneObject = function(itemID)
    Module.ArmedForce:DebugWeaponSceneObject(itemID)
end

GMHandleLogic.GMGiveWeaponOutGame = function(ids)
    Module.ArmedForce:GMGiveWeaponOutGame(ids)
end

GMHandleLogic.GMModifyWPOutGame = function(ids, socketGUIDs)
    Module.ArmedForce:GMModifyWPOutGame(ids, socketGUIDs)
end

GMHandleLogic.DebugItemInfomation = function()
    local src = CloseModuleType.bIsCloseDebugItemInfomation
    CloseModuleType.bIsCloseDebugItemInfomation = not src
    GMHandleLogic._ShowMeesage(string.format("DEBUG-ItemInfo:%s", src))
end

GMHandleLogic.GoToRescueMission = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMGoToQuest ********", nil)
end

GMHandleLogic.BreakTrain = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMBreakTrain", nil)
end

GMHandleLogic.RailRideSpeedUp = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMRailRideSpeedUp 0.1", nil)
end

GMHandleLogic.RailRideSlowDown = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMRailRideSpeedUp -0.1", nil)
end

GMHandleLogic.PlayerVehicleAddHP = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "PlayerVehicleAddHP 1000000", nil)
end

GMHandleLogic.GMMPUnlockWeaponAll = function()
    local gmType = EGMType.kGMTypeMPUnlockWeaponAll
    local function fOnGMTypeMPUnlockWeaponAllCallback(res)
        local code = res == nil and -1 or res.result
        GMHandleLogic._ShowMeesage(string.format("GMMPUnlockWeaponAll:%d", code))
    end
    Server.GMServer:DoGMReq(gmType, nil, fOnGMTypeMPUnlockWeaponAllCallback)
end

-----------------------------------------------------------------------
--region Lua 性能

local bHasStart = false

GMHandleLogic.StartLuaSample = function()
    if not bHasStart then
        bHasStart = true

        LuaPerfTool.StartLuaSample(true)
    end
end

GMHandleLogic.StopLuaSample = function()
    if bHasStart then
        bHasStart = false
        
        LuaPerfTool.StopLuaSample(true)
    end
end

GMHandleLogic.StartUIAnalysis = function()
    AnalysisUtil.bTraceUI = true
    Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMAnalysisUtilBegin)
end

GMHandleLogic.StopUIAnalysis = function()
    AnalysisUtil.bTraceUI = false
    Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMAnalysisUtilEnd)
end

GMHandleLogic.ToggleLuaSymbol = function()
    LuaSymbolTool.EnableLuaSymbol()
end

GMHandleLogic.ReleaseFrontendMemory = function()
    logerror("GMHandleLogic.ReleaseFrontendMemory, Start")
    Facade.UIManager:UltimateClearAllPoolWithRes(true)

    -- 清理RTI相关
    local RuntimeIconManager = UWeaponAssembleSubsystem.Get():GetRuntimeIconManager()
    RuntimeIconManager:ClearAllAtlas()

    -- 进行GC
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "slua.fullgc", nil)

    logerror("GMHandleLogic.ReleaseFrontendMemory, End")
end

GMHandleLogic.GMPromotQuality = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.ForceLOD 0",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"foliage.ForceLOD 0",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.CullScreenSizeRatio 0",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.DynamicCullScreenSizeRatio 0",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"OpenWorld.PendingUnloadTimeLimit 1000000",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"OpenWorld.MaxPendingUnloadLevelNum 1000000",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.Shadow.MaxCSMResolution 4096",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.Shadow.DistanceScale 2",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"t.maxFPS 120",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.screenpercentage 150",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.iblcullingdistance 50000",nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(),"r.VT.Flush",nil)
end

GMHandleLogic.GMSetPCHomeCinema = function()
    loginfo("[GMHandleLogic.GMSetPCHomeCinema] ")
    UDFMAudioMastering.SetPCHomeCinema()
end

GMHandleLogic.GMSetPCHeadphones = function()
    loginfo("[GMHandleLogic.GMSetPCHeadphones] ")
    UDFMAudioMastering.SetPCHeadphones()
end

GMHandleLogic.GMSetPCNightMode = function()
    loginfo("[GMHandleLogic.GMSetPCNightMode] ")
    UDFMAudioMastering.SetPCNightMode()
end

GMHandleLogic.GMSetMobileHeadphones = function()
    loginfo("[GMHandleLogic.GMSetMobileHeadphones] ")
    UDFMAudioMastering.SetMobileHeadphones()
end

GMHandleLogic.GMSetMobileMobileSpeakers = function()
    loginfo("[GMHandleLogic.GMSetMobileMobileSpeakers] ")
    UDFMAudioMastering.SetMobileMobileSpeakers()
end

GMHandleLogic.GMShow1080Test = function()
    loginfo("[GMHandleLogic.GMShow1080Test] ")
    Facade.UIManager:AsyncShowUI(UIName2ID.GM1080Test)
end

GMHandleLogic.GMShow2KTest = function()
    loginfo("[GMHandleLogic.GMShow2KTest] ")
    Facade.UIManager:AsyncShowUI(UIName2ID.GM2KTest)
end

GMHandleLogic.GMShow2K1080Test = function()
    loginfo("[GMHandleLogic.GMShow2K1080Test] ")
    Facade.UIManager:AsyncShowUI(UIName2ID.GM2K1080Test)
end

GMHandleLogic.GMChatForbidden = function(forbid_type,forbid_time,playerId)
    local gmType = EGMType.kGMTypeChatForbidden
    local args = {
        playerId ~= 0 and playerId or Server.AccountServer:GetPlayerId(),
        forbid_type or 1,
        forbid_time or 3600
    }
 
    local function fChatForbiddenRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.GMChatForbidden] ChatForbidden %s Success",playerId)
        end
    end
    Server.GMServer:DoGMReq(gmType, args, fChatForbiddenRes)
end

GMHandleLogic.GMChatRemoveSpeach = function(playerId)
    local gmType = EGMType.kGMTypeChatRemoveSpeach
    local args = {
        playerId ~= 0 and playerId or Server.AccountServer:GetPlayerId(),
    }
 
    local function fChatForbiddenRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.GMChatRemoveSpeach] ChatRemoveSpeach %s Success",playerId)
        end
    end
    Server.GMServer:DoGMReq(gmType, args, fChatForbiddenRes)
end

local _bRegisterPush = false
GMHandleLogic.AddLocalNotification = function(title, content, time)
    local DFMGamePush = import("DFMGamePush")
    local _push = DFMGamePush.Get(GetGameInstance())
    
    --_push.Channel = "XG"
    if not _bRegisterPush then
        _push:RegisterPush(Server.SDKInfoServer:GetOpenId())
        _bRegisterPush = true
        loginfo("[GMHandleLogic.AddLocalNotification] RegisterPush")
    end
    if type(time) ~= "string" then
        _push:ClearLocalNotifications()
    else
        local times = string.split(time,"-")
        local timeCount = #times
        
        --iOS
        _push.LocalNotification_IOS.AlertAction = tostring(title)
        _push.LocalNotification_IOS.AlertBody = tostring(content)
        _push.LocalNotification_IOS.FireTime = TimeUtil.GetCurrentTime()
        _push.LocalNotification_IOS.Badge = 1

        --Android
        _push.LocalNotification_Android.Title = tostring(title)
        _push.LocalNotification_Android.Content = tostring(content)
        _push.LocalNotification_Android.Type = 1
        _push.LocalNotification_Android.ActionType = 1
        --_push.LocalNotification_Android.Url = "http://www.qq.com"
        if timeCount > 0 then
            _push.LocalNotification_Android.Date = times[1]
        end
        if timeCount > 1 then
            _push.LocalNotification_Android.Hour = times[2]
        end
        if timeCount > 2 then
            _push.LocalNotification_Android.Min = times[3]
        end

        Timer.DelayCall(1, function ()
            _push:AddLocalNotification()
            loginfo("[GMHandleLogic.AddLocalNotification] AddLocalNotification")
        end)
    
    end
end

--endregion
-----------------------------------------------------------------------

--region 攻防玩法规则相关GM
--时间设置
GMHandleLogic.GMBreakthroughSetTime = function(TimeValue)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSetTime(localCtrl.PlayerGMComponent, TimeValue)
    end

end

--region 攻防玩法规则相关GM
--自救
GMHandleLogic.GMBreakthroughSelfReborn = function(TimeValue)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSelfReborn(localCtrl.PlayerGMComponent)
    end

end

--角色lod信息显示
GMHandleLogic.GMBreakthrougShowLodDebug = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthrougShowLodDebug(localCtrl.PlayerGMComponent)
    end
end

--军衔分设置
GMHandleLogic.GMBreakthroughSetRankScore = function(ScoreValue)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSetRankScore(localCtrl.PlayerGMComponent, ScoreValue)
    end
end

--兵力设置
GMHandleLogic.GMBreakthroughSetLeftVotes = function(LeftVotesValue)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSetLeftVotes(localCtrl.PlayerGMComponent, LeftVotesValue)
    end
end

--消耗票数设置
GMHandleLogic.GMBreakthroughSetBalanceVotes = function(CostNum)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSetBalanceVotes(localCtrl.PlayerGMComponent, CostNum)
    end
end

--关闭占点
GMHandleLogic.GMBreakthroughSectorClose = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSectorClose(localCtrl.PlayerGMComponent)
    end
end

--阶段区域推进
GMHandleLogic.GMBreakthroughSectorChange = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSectorChange(localCtrl.PlayerGMComponent)
    end
end

--非游玩区倒计时时间
GMHandleLogic.GMBreakthroughSetCountingTime = function(TimeValue)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughSetCountingTime(localCtrl.PlayerGMComponent, TimeValue)
    end
end

GMHandleLogic.GMBreakthroughWinMatchEnd = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughWinMatchEnd(localCtrl.PlayerGMComponent)
    end
end

--巨塔激活
GMHandleLogic.GMBreakthroughTowerActive = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughTowerActive(localCtrl.PlayerGMComponent)
    end
end

--巨塔切换位置
GMHandleLogic.GMBreakthroughTowerSwitchSeat = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughTowerSwitchSeat(localCtrl.PlayerGMComponent)
    end
end

--播报触发GM
GMHandleLogic.GMBreakthroughTipsBroadCast = function(TipsType)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMBreakthroughTipsBroadCast(localCtrl.PlayerGMComponent, TipsType)
    end
end

--开始录制局外协议
GMHandleLogic.GMBeginRecordOutGameNetworkProtocol = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "net.EnableOutGameNetworkRecorder 1", nil)
    Module.CommonTips:ShowSimpleTip('BeginRecordOutGameNetworkProtocol', 3)
end

--停止录制局外协议
GMHandleLogic.GMEndRecordOutGameNetworkProtocol = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "net.EnableOutGameNetworkRecorder 0", nil)
    Module.CommonTips:ShowSimpleTip('EndRecordOutGameNetworkProtocol', 3)
end

--上传协议录制文件
GMHandleLogic.GMUploadOnr = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "net.UploadOnr", nil)
    Module.CommonTips:ShowSimpleTip('Upload Onr', 3)
end

--关闭无操作检测
GMHandleLogic.GMSetbIsEnableHangUpDetecteFalse = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMSetbIsEnableHangUpDetecte(false)
    end
end

--endregion 攻防玩法规则相关GM
-----------------------------------------------------------------------

GMHandleLogic.GMChangeMPItem = function(itemid)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMChangeMPItem(itemid)
    end
end

GMHandleLogic.GMMatchMaking = function(MatchMode)
    if MatchMode ~= nil then
        Module.CommonTips:ShowSimpleTip('start matching...'..MatchMode, 5)
        local req = pb.CSGMReq:New()
        req.gm_type = EGMType.kGMTypeMatchQuick
        req.args = {matchModeID}
        req:Request(function(res)
            if res.result == 0 then
                loginfo("GMHandleLogic.GMMatchMaking Waiting Join Match!")
                Server.MatchServer:StartMatching()
                -- self:_SetIsMatching(true)
                local myInfo = Server.TeamServer:GetMyInfo()
                Server.TeamServer.Events.evtReadyStateChanged:Invoke(myInfo.PlayerID)
            else
                if not res.result then
                    LuaGlobalEvents.evtServerShowTip:Invoke(Module.GM.Config.Loc.GMMatchNetError, 3)
                elseif res.result == Err.MatchServiceClose then
                    LuaGlobalEvents.evtServerShowTip:Invoke(Module.GM.Config.Loc.GMGameModeLocked, 3)
                else
                    LuaGlobalEvents.evtServerShowTip:Invoke(Module.GM.Config.Loc.GMMatchFail..res.result, 3)
                end
                Server.MatchServer:EndMatching()
            end
        end)
       
    end
    
end

GMHandleLogic.GMEnableMPFashion = function()
    -- Server.HeroServer:DoChangeFashionPriorReq(true, MatchGameMode.TDMGameMode)
end

GMHandleLogic.GMDisableMPFashion = function(typeId)
    -- typeId = setdefault(typeId, 1)
    -- ensure(false, 'GMHandleLogic.GMDisableMPFashion')
    -- Server.HeroServer:DoChangeFashionPriorReq(false, MatchGameMode.WorldGameMode)

    -- local AHallMainDisplayCtrl = import "HallMainDisplayCtrl"

    -- local displayCtrl = AHallMainDisplayCtrl.Get(GetGameInstance())
    -- if not displayCtrl then
    --     return
    -- end
    -- local func = displayCtrl["OnSetEnsureFailed"]
    -- if func then
    --     func(displayCtrl, typeId)
    -- end

    ULuautils.TestEnsureFailed()
end

GMHandleLogic.GMTest2DScene = function()
    Module.LobbyDisplay:SetGMTestSceneType(1)
end

GMHandleLogic.GMTest3DScene = function()
    Module.LobbyDisplay:SetGMTestSceneType(2)
end

GMHandleLogic.GMEnableCharacterAvatarSuit = function()
    local EMainFlowCtrlType = import "EMainFlowCtrlType"
    for i = 1, 4 do
        local character = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MainFlowLobby,"GetCharacterBySlotId", EMainFlowCtrlType.MainFlowCtrl_SOL, i)
        if character then
            local func = character["SetUIHairIngoreRender"]
            if func then
                func(character, true)
            end
        end
    end
    -- Module.LobbyDisplay:SetGMTestEnableCharacterAvatarSuit(true)
end

GMHandleLogic.GMDisableCharacterAvatarSuit = function()
    local EMainFlowCtrlType = import "EMainFlowCtrlType"
    for i = 1, 4 do
        local character = Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.MainFlowLobby,"GetCharacterBySlotId", EMainFlowCtrlType.MainFlowCtrl_SOL, i)
        if character then
            local func = character["SetUIHairIngoreRender"]
            if func then
                func(character, false)
            end
        end
    end
end



GMHandleLogic.CreateMobileAudioProfilerWidget = function()
    loginfo("[GMHandleLogic]CreateMobileAudioProfilerWidget ")
    local UTAProfilerBPFuncLibrary = import"TAProfilerBPFuncLibrary"
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if UTAProfilerBPFuncLibrary ~= nil then
        UTAProfilerBPFuncLibrary.CreateProfilerWidget(localCtrl)
    end
end

GMHandleLogic.ChangeAudioProfilerMode = function()
    loginfo("[GMHandleLogic]ChangeAudioProfilerMode ")
    local UTAProfilerBPFuncLibrary = import"TAProfilerBPFuncLibrary"
    if UTAProfilerBPFuncLibrary ~= nil then
        UTAProfilerBPFuncLibrary.ChangeMode()
    end
end
--匹配分页
GMHandleLogic.GMChangeMatchAIType = function(TreeBoxAI, AILAB)
    local gmType = EGMType.kGMTypeMatch
    local args = {1 , TreeBoxAI, AILAB}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMChangeMPELO = function(arg1)
    local gmType = EGMType.kGMTypeMatch
    local args = {2 , arg1}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMChangePlayQuitDay = function(arg1)
    local gmType = EGMType.kGMTypeMatch
    local args = {3 , arg1}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMChangeRealMPELO = function(arg1, arg2)
    local gmType = EGMType.kGMTypeMatch
    local args = {4 , arg1, arg2}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMSetT3IsOpen = function(arg1)
    local gmType = EGMType.kGMTypeMatch
    local args = {5 , arg1}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMSetFullBot = function(arg1)
    local gmType = EGMType.kGMTypeMatch
    local args = {6 , arg1}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMStopUseMP = function(arg1)
    local gmType = EGMType.kGMTypeMatch
    local args = {7 , arg1}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMSetSkillScore = function(arg1, arg2)
    local gmType = EGMType.kGMTypeSetSkillScore
    local args = {arg1 , arg2}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMSetLootScore = function(arg1, arg2)
    local gmType = EGMType.kGMTypeSetLootScore
    local args = {arg1 , arg2}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMResetSkillScore = function()
    local gmType = EGMType.kGMTypeSetSkillScore
    local args = {0 , 0}
    return Server.GMServer:DoGMReq(gmType, args)
end

-------------------------------

GMHandleLogic.GMDisableSeamlessEntry = function()
    local world = GetWorld()
    if world then
        local worldSettings = world:K2_GetWorldSettings()
        if worldSettings then
            local EnableSeamlessTravel = worldSettings.EnableSeamlessTravel
            worldSettings.EnableSeamlessTravel = false
        else
            Module.CommonTips:ShowSimpleTip("更改失败，无法找到WorldSetting", 3)
        end
    else
        Module.CommonTips:ShowSimpleTip("更改失败，无法找到World", 3)
    end
end

GMHandleLogic.GMToggleSeamlessEntry = function()
    local world = GetWorld()
    if world then
        local worldSettings = world:K2_GetWorldSettings()
        if worldSettings then
            local EnableSeamlessTravel = worldSettings.EnableSeamlessTravel
            worldSettings.EnableSeamlessTravel = not EnableSeamlessTravel
            if worldSettings.EnableSeamlessTravel then
                Module.CommonTips:ShowSimpleTip("更改成功，当前无缝入局为开", 3)
            else
                Module.CommonTips:ShowSimpleTip("更改成功，当前无缝入局为关", 3)
            end
        else
            Module.CommonTips:ShowSimpleTip("更改失败，无法找到WorldSetting", 3)
        end
    else
        Module.CommonTips:ShowSimpleTip("更改失败，无法找到World", 3)
    end
end

GMHandleLogic.GMToggleAILabDebug = function(mode)
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:ToggleAILabDebugMode(mode)
    end
end


GMHandleLogic.EnterMatchDisplayScene = function(time)
    
    
end

GMHandleLogic.SwitchMatchDisplayCharacter = function(slotId, fashionSuitId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMatch, "SetCharacterAvatarWithSlot", slotId, fashionSuitId)
end


--endregion

-----------------------------------------------------------------------
-- data collection region

GMHandleLogic.SaveCollectedData = function()
    loginfo("GMHandleLogic:SaveCollectedData")
    local UICostMonitor = AnalysisUtil.GetUICostMonitor()
    if UICostMonitor then
        UICostMonitor:SaveData("")
    end
    Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMAnalysisUtilSave)
end

-- endregion
-----------------------------------------------------------------------

GMHandleLogic.AddItemBatch = function()
    local gmType = EGMType.kGMTypeAddItemBatch
    local args = {}
    Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ChangeMatchLootStyle = function(style)
    local gmType = EGMType.kGMTypeMatchLootStyle
    local args = {style}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.ResetMatchLootStyle = function()
    local gmType = EGMType.kGMTypeMatchLootStyle
    local args = {0}
    return Server.GMServer:DoGMReq(gmType, args)
end


GMHandleLogic.ChangeOrRevertSol2Mp = function(changeOrRevert, respawnSamePos)
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:GMChangeSol2Mp(changeOrRevert, respawnSamePos)
    end
end

GMHandleLogic.GetWorldName = function()
    local MapName = tostring(UGPGameplayStatics.GetWorldName(GetWorld()))
    loginfo("GMHandleLogic.GetWorldName: ", MapName)
    return MapName
end

GMHandleLogic.RunMetaTest = function()
    local UMetaDorianManager = import "MetaDorianManager"
    local UMetaTestManager = import "MetaTestManager" 
    UMetaTestManager.Init()
    print("[MetaDorian] Run MetaTest scripts at MetaDorian/MetaManualTest")
    UMetaDorianManager.RunHotfixScripts("MetaDorian/MetaManualTest/MTRTTIArgPassTest.lua")
    UMetaDorianManager.RunHotfixScripts("MetaDorian/MetaManualTest/MTRTTIIOTest.lua")
    UMetaDorianManager.RunHotfixScripts("MetaDorian/MetaManualTest/MTRTTITypeCastTest.lua")
    UMetaDorianManager.RunHotfixScripts("MetaDorian/MetaManualTest/MTRTTITypeCtorTest.lua")
    print("[MetaDorian] Run MetaTest scripts at MetaDorian/MetaAutoTest")
    UMetaDorianManager.RunAllHotfixScriptsUnderDir("MetaDorian/MetaAutoTest")
end

GMHandleLogic.GameletTest = function()
    GameletLogic.ShowEntrance()
end

--region Guide/引导

-- 跳过所有引导
GMHandleLogic.SkipAllGuide = function()
    Module.CommonTips:ShowSimpleTip(Module.Guide.Config.Loc.guideSkipAllGuide)
    Module.Guide:StopAllGuide(true)
    Server.GuideServer:SkipNewPlayerGuide()
    Module.Guide.Field.waitPlayGuide = nil
    Module.Guide.Field:ClearCurShowUIState()
    Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideSkipAllGuide, true)
    Module.Guide:SkipAllGuide()
    Module.Guide:StopLoadingViewControl()
end

-- 跳转到指定引导阶段
GMHandleLogic.JumpToGuideStage = function(targetStage, callback)
    if not targetStage then return end
    if not table.contains(Module.Guide.Config.NewPlayerGuideStageList, targetStage) then
        return false
    end
    local gmType = EGMType.kGMTypeSetGuideStage
    local args = {
        targetStage,
    }
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

-- 快速拉起新手关
GMHandleLogic.FastBeginNewPlayerMatch = function()
    local targetStage = Module.Guide.Config.EGuideStage.newPlayerGuideStage1
    local afterJump = function ()
        if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then return end

        local gameModeServer = Server.GameModeServer
        local ret, targetMatchMode = Server.GameModeServer:GenMatchModeByWorldEntrancCfg(2, MatchSubMode.SOLPMC)
        gameModeServer:SetMatchID(0)
        gameModeServer:SetMatchMode(targetMatchMode)
        gameModeServer:SetMatchModes({ targetMatchMode })
        Module.ArmedForce:MarkOutfitFlowFlag()

        Module.Guide:ResetNewPlayerMatchGuideVariables()
        Server.GuideServer:BeginNewPlayerMatch(nil, true)
    end
    GMHandleLogic.JumpToGuideStage(targetStage, afterJump)
end

-- 快速拉起尖叫小关
GMHandleLogic.FastBeginMiniGameMatch = function()
    local targetStage = Module.Guide.Config.EGuideStage.newPlayerGuideStage3
    local afterJump = function ()
        if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then return end

        local gameModeServer = Server.GameModeServer
        local ret, targetMatchMode = Server.GameModeServer:GenMatchModeByWorldEntrancCfg(2, MatchSubMode.SOLPMC)
        gameModeServer:SetMatchID(0)
        gameModeServer:SetMatchMode(targetMatchMode)
        gameModeServer:SetMatchModes({ targetMatchMode })
        Module.ArmedForce:MarkOutfitFlowFlag()

        Server.GuideServer:BeginMiniGameMatch()
    end
    GMHandleLogic.JumpToGuideStage(targetStage, afterJump)
end

-- 跳过intro
GMHandleLogic.SkipIntro = function()
    if Module.Guide:IsIntroFinished() then return end
    local targetStage = Module.Guide.Config.EGuideStage.newPlayerGuideStage1
    GMHandleLogic.JumpToGuideStage(targetStage)
end

function GMHandleLogic.BeginGuideByStep(mobileGuideStepId,pcGuideStepId)
    Module.Guide:ForceStartGuide(IsHD() and pcGuideStepId or mobileGuideStepId)
    Module.GM:OnCloseGM()
end

function GMHandleLogic.BeginGuideByStage(guideStageId)
    Module.Guide:StartStageGuide(guideStageId)
    Module.GM:OnCloseGM()
end

-- 修改账号批次
function GMHandleLogic.ChangeAccountBatch(batch)
    local gmType = EGMType.kGMTypeSetPlayerAccountBatch
    local args = {
        batch,
    }
    return Server.GMServer:DoGMReq(gmType, args,function(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.ChangeAccountBatch] ChangeAccountBatch Success: ", batch)
            Server.GuideServer.playerAccountBatch = batch
        else 
            loginfo("[GMHandleLogic.ChangeAccountBatch] ChangeAccountBatch Failed: ", res.result)
        end
    end)

end

-- 重置引导阶段完成/记录记录
function GMHandleLogic.ResetGuideStageRecord(guideStageId)
    Module.Guide:ResetStage(guideStageId)
end

--endregion


GMHandleLogic.OpenTestManyImageUI = function()
    --Facade.UIManager:AsyncShowUI(UIName2ID.ManyImageTest, nil, nil)
end

-- 进入新大战场大厅
GMHandleLogic.SwitchToNewMPLobbyStage = function(bUseOld)
    local uiSettings = UITable[UIName2ID.BattlefieldEntryMainPanel]
    if uiSettings then
        uiSettings.LinkSubStage = ESubStage.HallMpLobby
    end
    Module.BattlefieldEntry.Config.bUseOld = bUseOld
    loginfo("Enter New MP Lobby Stage")
end

-- 进入安全屋休闲状态
GMHandleLogic.EnterIdleState = function()
    local localPlayerCharacter = GetInGameController():GetGPCharacter()
    if localPlayerCharacter == nil then
        return
    end
    localPlayerCharacter.Blackboard.bIsRelaxIdleState = true
    localPlayerCharacter.Blackboard:SetIsInSafeHouse(true)
end

-- 退出安全屋休闲状态
GMHandleLogic.LeaveIdleState = function()
    local localPlayerCharacter = GetInGameController():GetGPCharacter()
    if localPlayerCharacter == nil then
        return
    end
    localPlayerCharacter.Blackboard.bIsRelaxIdleState = false
    localPlayerCharacter.Blackboard:SetIsInSafeHouse(false)
end


GMHandleLogic.GMSetPlayerName = function(teamIndex, inName)
    teamIndex = setdefault(teamIndex, -1)
    inName = setdefault(inName, "")
    UDFMGameplayDelegates.Get(GetWorld()):TryGMSetPlayerNameHD(teamIndex, inName)
end

-- 本地化
GMHandleLogic.OpenLanguagePanel = function()
    -- 打开语言切换界面，直接弹窗形式打开就行
    Facade.UIManager:AsyncShowUI(UIName2ID.LanguagePopView, nil, nil, true)
end

--QADebug
GMHandleLogic.QADebugReset = function()
    GMHandleLogic.CollectCurrentParams()
    local cmdList = {
        "r.prtlighting 1",
        "r.enableLocalReflection 1",
        "r.IBLCullingDistance 1000000",
        "r.EyeAdaptation.ManualLuminLimit 0",
        "r.UseDefaultColorGrading 0",
        "r.directionLightRed 0",
        "r.LightCheckOption 0",
        "r.Tet.Enable 1",
        "r.Shadow.ScrollingCSMC.Enable 1",
        "r.Mobile.AllowSoftwareOcclusion 1",
        "r.Shadow.UsePreBakeShadowDepth.Enable 1",
        string.format("r.volumetricfog %s", tostring(renderParams.enableVolumetricFog)),
        "r.skyatmosphere 1",
        "r.volumetricfog.exclude 1",
        string.format("r.volumetricfog.gridsizeZ %s", tostring(renderParams.volumetricFogGridSizeZ)),
        "r.fog 1",
        "r.cdlod.landscapetype 1",
        string.format("r.BloomQuality %s", tostring(renderParams.bloomQuality))
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug关闭prt（动态GI）
GMHandleLogic.QADebugPRT = function()
    local cmdList = {
        "r.prtlighting 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug下午天气
GMHandleLogic.QADebugAfternoon = function()
    local cmdList = {
        "r.GMChangeTODType 701"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QA 改为日出天气
GMHandleLogic.QADebugSunrise = function()
    local cmdList = {
        "r.GMChangeTODType 703"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QA 改为上午天气
GMHandleLogic.QADebugMorning = function()
    local cmdList = {
        "r.GMChangeTODType 702"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--改为中午天气
GMHandleLogic.QADebugMidday = function()
    local cmdList = {
        "r.GMChangeTODType 705"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--改为黄昏天气
GMHandleLogic.QADebugTwilight = function()
    local cmdList = {
        "r.GMChangeTODType 700"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--改为夜间天气
GMHandleLogic.QADebugNight = function()
    local cmdList = {
        "r.GMChangeTODType 704"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end


--QADebug关闭室内反射球
--GMHandleLogic.QADebugLocalRef = function()
--   local cmdList = {
--        "r.enableLocalReflection 0"
--    }
--    local worldContext = GetGameInstance()
--    for _,cmd in ipairs(cmdList) do 
--        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
--    end
--end

--QANew 打开/关闭室内反射球
GMHandleLogic.QADebugLocalRef = function(LocalRefIndex)
	local LocalRefLevel = tonumber(LocalRefIndex) or 1
	local cmd = string.format("r.enableLocalReflection %d", localRefLevel)
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QADebug关闭所有反射球
--GMHandleLogic.QADebugAllRef = function()
--    local cmdList = {
--        "r.IBLCullingDistance 0"
--    }
--    local worldContext = GetGameInstance()
--    for _,cmd in ipairs(cmdList) do 
--        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
--    end
--end

--QANew 打开/关闭所有反射球
GMHandleLogic.QADebugAllRef = function(AllRefIndex)
	local AllRefLevel = tonumber(AllRefIndex) or 1
	local cmd = string.format("r.IBLCullingDistance %d", AllRefLevel)
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QADebug解除曝光上下限
GMHandleLogic.QADebugAutoExp = function()
    local cmdList = {
        "r.EyeAdaptation.ManualLuminLimit 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug关闭后效调色
GMHandleLogic.QADebugPost = function()
    local cmdList = {
        "r.UseDefaultColorGrading 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug关闭中低配动态物体光照
GMHandleLogic.QADebugTetlight = function()
    local cmdList = {
        "r.Tet.Enable 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug关闭中低配动态物体光照
GMHandleLogic.QADebugDirRed = function()
    local cmdList = {
        "r.directionLightRed 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 显示/关闭碰撞
GMHandleLogic.QADebugPhysXDrawing = function()
    local cmdList = {
        "PhysXDrawing"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QANew 绘制模式-线框模式/实心光照/实心无光照
GMHandleLogic.QANewPhysXDrawingMode = function(ModeIndex)
	local ModeLevel= tonumber(ModeIndex) or 1
	local cmd = string.format("PhysXDrawing.Mode %d", ModeLevel)
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 显示/隐藏模型
GMHandleLogic.QANewPhysXDrawingOthers = function()
	local cmd = "PhysXDrawing.Others"
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 对比客户端和服务端物理的差异
GMHandleLogic.QANewPhysXDrawingDiff = function()
	local cmd = "PhysXDrawing.Diff"
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 显示三种碰撞-角色/子弹/两种都显示
GMHandleLogic.QANewPhysXDrawingCondition = function(ConditionIndex)
	local ConditionLevel= tonumber(ConditionIndex) or 1
	local cmd = string.format("PhysXDrawing.Condition %d", ConditionLevel)
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 绘制的不透明度
GMHandleLogic.QANewPhysXDrawingOpacity = function(OpacityIndex)
	local OpacityLevel= tonumber(OpacityIndex) or 0.1
	local cmd = string.format("PhysXDrawing.Opacity %f", OpacityLevel)
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 显示服务端物理/客户端物理
GMHandleLogic.QANewPhysXDrawingNet = function(NetIndex)
	local NetLevel = tonumber(NetIndex) or 1
	local cmd = string.format("PhysXDrawing.Net %d", NetLevel)
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 是否使用双面绘制
GMHandleLogic.QANewPhysXDrawingDoubleSided = function(DoubleSidedIndex)
	local DoubleSidedLevel= tonumber(DoubleSidedIndex) or 1
	local cmd = string.format("PhysXDrawing.DoubleSided %d", DoubleSidedLevel)
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 隐藏选中的物理
GMHandleLogic.QANewPhysXDrawingHideSelected = function()
	local cmd = "PhysXDrawing.HideSelected"
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 清除HideSelected隐藏的物体
GMHandleLogic.QANewPhysXDrawingClearHidden = function()
	local cmd = "PhysXDrawing.ClearHidden"
    local worldContext = GetGameInstance()
	UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QANew 切换查看角色脚步声碰撞
GMHandleLogic.QANewPhysXDrawingFootsteps = function()
    local cmdList = {
        "PhysXDrawing 1",
		"PhysXDrawing.Inclusion 2",
		"PhysXDrawing.Exclude Decoration"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QANew 隐藏灰色线框（触发器）
GMHandleLogic.QANewPhysXDrawingExcludeTrigger = function()
    local cmdList = {
        "PhysXDrawing.Exclude Trigger"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QANew 开启快照-单独在右上角渲染场景碰撞
GMHandleLogic.QANewPhysXDrawingResolve = function(ResolveIndex)
	local ResolveLevel = tonumber(ResolveIndex) or 1
	local cmd = string.format("PhysXDrawing.Resolve %d", ResolveLevel)
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

--QADebug 显示服务器人物碰撞
GMHandleLogic.QADebugPhysXDrawingDSCharacter = function()
    local cmdList = {
        "PhysXDrawing",
        "PhysDrawing.Condition 1",
        "PhysDrawing.Mode 1",
        "PhysDrawing.Net 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 显示客户端人物碰撞
GMHandleLogic.QADebugPhysXDrawingClientCharacter = function()
    local cmdList = {
        "PhysXDrawing",
        "PhysDrawing.Condition 1",
        "PhysDrawing.Mode 1",
        "PhysDrawing.Net 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 显示服务器子弹碰撞
GMHandleLogic.QADebugPhysXDrawingDSBullet = function()
    local cmdList = {
        "PhysXDrawing",
        "PhysDrawing.Condition 2",
        "PhysDrawing.Mode 0",
        "PhysDrawing.Net 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 显示客户端子弹碰撞
GMHandleLogic.QADebugPhysXDrawingClientBullet = function()
    local cmdList = {
        "PhysXDrawing",
        "PhysDrawing.Condition 2",
        "PhysDrawing.Mode 0",
        "PhysDrawing.Net 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 刷新ShadowCache
GMHandleLogic.QADebugRefreshShadowCache = function()
    local cmdList = {
        "r.Shadow.ScrollingCSMC.Enable 1",
        "r.Shadow.ScrollingCSMC.ForceGlobalRefresh"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭ShadowCache
GMHandleLogic.QADebugDisableShadowCache = function()
    local cmdList = {
        "r.Shadow.ScrollingCSMC.Enable 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭Occlusion
GMHandleLogic.QADebugDisableSoftwareOcclusion = function()
    local cmdList = {
        "r.Mobile.AllowSoftwareOcclusion 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭预烘焙阴影
GMHandleLogic.QADebugDisablePreBakeShadow = function()
    local cmdList = {
        "r.Shadow.UsePreBakeShadowDepth.Enable 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭室外雾
GMHandleLogic.QADebugDisableAtmosphereFog = function()
    GMHandleLogic.CollectCurrentParams()
    local cmdList = {
        "r.volumetricfog 0",
        "r.skyatmosphere 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭室内雾
GMHandleLogic.QADebugDisableInteriorFog = function()
    GMHandleLogic.CollectCurrentParams()
    local cmdList = {
        "r.volumetricfog 0",
        "r.volumetricfog.exclude 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 提高体积雾细节
GMHandleLogic.QADebugIncreaseVolumetricFogDensity = function()
    GMHandleLogic.CollectCurrentParams()
    local cmdList = {
        "r.volumetricfog 0",
        "r.volumetricfog.gridsizeZ 64"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭雾
GMHandleLogic.QADebugDisableFog = function()
    local cmdList = {
        "r.fog 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭体积雾
GMHandleLogic.QADebugDisableVolumetricFog = function()
    GMHandleLogic.CollectCurrentParams()
    local cmdList = {
        "r.fog 0",
        "r.volumetricfog 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭地形渲染
GMHandleLogic.QADebugDisableTerrain = function()
    local cmdList = {
        "r.cdlod.landscapetype 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--QADebug 关闭Bloom
GMHandleLogic.QADebugDisableBloom = function()
    GMHandleLogic.CollectCurrentParams()
    local cmdList = {
        "r.BloomQuality 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 太阳漫反射
GMHandleLogic.LightingDebugDirDiffuse = function()
    local cmdList = {
        "r.LightCheckOption 2"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 太阳高光
GMHandleLogic.LightingDebugDirSpec = function()
    local cmdList = {
        "r.LightCheckOption 4"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 天光
GMHandleLogic.LightingDebugSkylightSH = function()
    local cmdList = {
        "r.LightCheckOption 256"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug IBL环境高光
GMHandleLogic.LightingDebugIBLSpec = function()
    local cmdList = {
        "r.LightCheckOption 8"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 自然光GI
GMHandleLogic.LightingDebugPRTBounce = function()
    local cmdList = {
        "r.LightCheckOption 16"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 天光AO
GMHandleLogic.LightingDebugSkyAO = function()
    local cmdList = {
        "r.LightCheckOption 320"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 烘焙AO
GMHandleLogic.LightingDebugAO = function()
    local cmdList = {
        "r.LightCheckOption 833"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 雾效
GMHandleLogic.LightingDebugFog = function()
    local cmdList = {
        "r.LightCheckOption 1024"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug Lightmap
GMHandleLogic.LightingDebugLightmap = function()
    local cmdList = {
        "r.LightCheckOption 32"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--光照成分Debug 复位
GMHandleLogic.LightingDebugReset = function()
    local cmdList = {
        "r.LightCheckOption 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--图集测试
GMHandleLogic.TestAtlas = function()
    Facade.UIManager:AsyncShowUI(UIName2ID.GMAtlasTest)
end

-- LQA
GMHandleLogic.OpenLQARemoteTool = function(serverUrl)
    -- 开启LQA远程工具，直接发起登录
    local LQARemoteToolManager = import("LQARemoteToolManager").Get(GetGameInstance())
    if LQARemoteToolManager then
        local OnLQALogin = function(bIsSuccess)
            loginfo("LQA Login:", bIsSuccess)
            if bIsSuccess then
                Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMLQARemoteConnected)
            else
                Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMLQAConnectionWarning)
            end
        end
        LQARemoteToolManager.OnLQALogin:Clear()
        LQARemoteToolManager.OnLQALogin:Add(OnLQALogin)
        if serverUrl and serverUrl ~= "" then
            LQARemoteToolManager:InitLQARemoteTool(serverUrl)
        else
            LQARemoteToolManager:InitLQARemoteTool("")
        end
    end
end

-- 获取远程对应的LQAID
GMHandleLogic.GetLQAServerID = function()
    local LQARemoteToolManager = import("LQARemoteToolManager").Get(GetGameInstance())
    if not LQARemoteToolManager then
        logerror("Get LQARemoteToolManager Failed!")
        Module.CommonTips:ShowSimpleTip("Error")
        return
    end

    if not LQARemoteToolManager:IsLQAConnected() then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMLQAConnectionWarning)
        return
    end

    local serverId = LQARemoteToolManager:GetServerID()
    Module.CommonTips:ShowSimpleTip(serverId, 5)
end

-- 主动上报
GMHandleLogic.LQASendSnapshot = function()
    local LQARemoteToolManager = import("LQARemoteToolManager").Get(GetGameInstance())
    if not LQARemoteToolManager then
        logerror("Get LQARemoteToolManager Failed!")
        Module.CommonTips:ShowSimpleTip("Error")
        return
    end

    if not LQARemoteToolManager:IsLQAConnected() then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMLQAConnectionWarning)
        return
    end

    -- 关掉GM界面
    Module.GM:CloseGMMainPanel()
    -- 需要延迟一点，不然GM界面还在
    Timer.DelayCall(0.2, function()
        -- 触发截图上报
        LQARemoteToolManager:SendSnapshotData()
    end)
end

local LQAHeartbeatReuse = true
GMHandleLogic.SwitchLQAHeartbeatReqReuse = function()
    if LQAHeartbeatReuse then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.LQA.EnableHeartbeatRequestReuse 0")
        LQAHeartbeatReuse = false
    else
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.LQA.EnableHeartbeatRequestReuse 1")
        LQAHeartbeatReuse = true
    end
end

-- L10N InvalidKey检查
GMHandleLogic.L10NToggleInvalidKeyCheck = function()
    if _WITH_EDITOR == 1 or UE_BUILD_SHIPPING then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GML10NInvalidKeyCheckFailed)
        return
    end
    local bIsOn = UDFMLocalizationCrashsightManager:ToggleInvalidKeyReport()
    if bIsOn then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GML10NInvalidKeyCheckOn)
    else
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GML10NInvalidKeyCheckOff)
    end
end

-- 载具解锁
GMHandleLogic.MPUnlockVehicle = function(vehicleItemID)
    local gmType = EGMType.kGMTypeMPAddVehicle
    local args = {
        vehicleItemID,
    }

    local function fMPUnlockVehicleRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.MPUnlockVehicle] unlock Success: ", vehicleItemID)
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPUnlockVehicleRes)
end

-- 所有载具解锁
GMHandleLogic.MPUnlockAllVehicle = function()
    local gmType = EGMType.kGMTypeMPAddVehicle
    local args = VehicleHelperTool.GetAllVehicleItemIDs()

    local function fMPUnlockAllVehicleRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPUnlockAllVehicleRes] unlock Success: ", logtable(args))
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPUnlockAllVehicleRes)
end

-- 载具配件解锁
GMHandleLogic.MPUnlockVehiclePart = function(vehiclePartItemID)
    local gmType = EGMType.kGMTypeMPAddVehicle
    local args = {
        vehiclePartItemID,
    }
    local function fMPUnlockVehiclePartRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPUnlockVehiclePartRes] unlock Success: ", vehiclePartItemID)
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPUnlockVehiclePartRes)
end

-- 所有载具配件解锁
GMHandleLogic.MPUnlockAllVehiclePart = function()
    local gmType = EGMType.kGMTypeMPAddVehicle
    local args = VehicleHelperTool.GetAllVehiclePartItemIDs()

    local function fMPUnlockAllVehiclePartRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPUnlockAllVehiclePartRes] unlock Success: ", logtable(args))
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPUnlockAllVehiclePartRes)
end

-- 所有载具和配件解锁
GMHandleLogic.MPUnlockAllVehicleAndVehiclePart = function()
    local gmType = EGMType.kGMTypeMPAddVehicle
    local args = VehicleHelperTool.GetAllVehicleItemIDsAndVehiclePartItemIDs()

    local function fMPUnlockAllVehicleAndVehiclePartRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPUnlockAllVehicleAndVehiclePartRes] unlock Success: ", logtable(args))
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPUnlockAllVehicleAndVehiclePartRes)
end

-- 载具添加经验
GMHandleLogic.MPAddVehicleExp = function(vehicleItemID, addExpNum)
    local gmType = EGMType.kGMTypeMPVehicleExp
    local args = {
        vehicleItemID,
        addExpNum
    }

    local function fMPAddVehicleExpRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPAddVehicleExpRes] vehicleItemID: ", vehicleItemID, ", addExpNum: ", addExpNum)
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPAddVehicleExpRes)
end

-- 载具解锁任务进度
GMHandleLogic.MPSetVehicleUnlockTaskValue = function(vehicleTaskID, value)
    local gmType = EGMType.kGMTypeMPSetVehicleUnlockTaskValue
    local args = {
        vehicleTaskID,
        value
    }

    local function fMPSetVehicleUnlockTaskValueRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPSetVehicleUnlockTaskValueRes] vehicleTaskID: ", vehicleTaskID, ", value: ", value)
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPSetVehicleUnlockTaskValueRes)
end

-- 载具解锁所有载具皮肤
GMHandleLogic.MPUnlockAllVehicleSkin = function()
    local gmType = EGMType.kGMTypeUnlockAllVehicleSkin
    local args = {
    }
    local function fMPUnlockAllVehicleSkinRes(res)
        if res and res.result == 0 then
            loginfo("[GMHandleLogic.fMPUnlockAllVehicleSkinRes] Success!")
        end
    end
    return Server.GMServer:DoGMReq(gmType, args, fMPUnlockAllVehicleSkinRes)
end

GMHandleLogic.CreateUIOutLinerPanel = function ()
    Module.GM:CloseGMMainPanel()
    Facade.UIManager:AsyncShowUI(UIName2ID.GMUIOutLinerTest)
end

GMHandleLogic.JumpByID = function (jumpID)
    Module.GM:CloseGMMainPanel()
    Module.Jump:JumpByID(jumpID)
end

-- 模拟打开ui的卡顿
GMHandleLogic.OpenEmptyTestUI = function(loadTime, oneFrameTime, totalOpenTime, openEndCB)
    local field = Module.GM.Field
    loadTime = setdefault(loadTime, 0)
    oneFrameTime = setdefault(oneFrameTime, 0)
    totalOpenTime = setdefault(totalOpenTime, 0)
    
    local delay = function()
        local beginTime = TimeUtil.GetCurrentTimeMillis()
        Facade.UIManager:AsyncShowUI(UIName2ID.GMEmptyTestOpen, nil, nil, beginTime, oneFrameTime, totalOpenTime, openEndCB)
        local openEndTime = TimeUtil.GetCurrentTimeMillis()
        --loginfo("OpenEmptyTestUI, beginTime", openEndTime - beginTime)
    end
    if loadTime > 0 then
        Timer.DelayCall(loadTime, delay)
    else
        delay()
    end
end

GMHandleLogic.OpenEmptyTestUIWithCutScene = function(CutSceneInTime, CutSceneOutTime)
    local field = Module.GM.Field
    local cutSceneUI = field.cutSceneHandel and field.cutSceneHandel:GetUIIns()
    if cutSceneUI then
        local afterOpenRealUI = function()
            cutSceneUI:TryHide()
        end
        local delay = function()
            GMHandleLogic.OpenEmptyTestUI(field.simulateLoadTime, field.simulateOpenTime, field.simulateTotalTime, afterOpenRealUI)
        end
        if CutSceneInTime > 0 then
            cutSceneUI:SetData(CutSceneInTime, CutSceneOutTime)
            cutSceneUI:StopAllAnimations()
            cutSceneUI:Show()
            Timer.DelayCall(CutSceneInTime, delay)
        else
            delay()
        end
    else
        local onLoadFinish = function(uiIns)
            uiIns:TryHide()
        end
        field.cutSceneHandel = Facade.UIManager:AsyncShowUI(UIName2ID.GMCutSecenUI, onLoadFinish, nil)
    end
end

GMHandleLogic.SetSimulateUIOpenTime = function(loadTime, oneFrameTime, totalOpenTime)
    local field = Module.GM.Field
    field.simulateLoadTime = loadTime or 0
    field.simulateOpenTime = oneFrameTime or 0
    field.simulateTotalTime = totalOpenTime or 0
    --logerror("SetSimulateUIOpenTime", field.simulateLoadTime, field.simulateOpenTime, field.simulateTotalTime)
    Module.CommonTips:ShowSimpleTip(string.format("%f %f %f", field.simulateLoadTime, field.simulateOpenTime, field.simulateTotalTime))
end

GMHandleLogic.SetSimulateTimeLevel1 = function(loadTime, oneFrameTime, totalOpenTime)
    local field = Module.GM.Field
    local levelDict = field.simulateTimeByLevel[1]
    if not levelDict then return end
    levelDict.simulateLoadTime = loadTime or 0
    levelDict.simulateOpenTime = oneFrameTime or 0
    levelDict.simulateTotalTime = totalOpenTime or 0
    Module.CommonTips:ShowSimpleTip(string.format("set level 1: %f %f %f", levelDict.simulateLoadTime, levelDict.simulateOpenTime, levelDict.simulateTotalTime))
end

GMHandleLogic.SetSimulateTimeLevel2 = function(loadTime, oneFrameTime, totalOpenTime)
    local field = Module.GM.Field
    local levelDict = field.simulateTimeByLevel[2]
    if not levelDict then return end
    levelDict.simulateLoadTime = loadTime or 0
    levelDict.simulateOpenTime = oneFrameTime or 0
    levelDict.simulateTotalTime = totalOpenTime or 0
    Module.CommonTips:ShowSimpleTip(string.format("set level 2: %f %f %f", levelDict.simulateLoadTime, levelDict.simulateOpenTime, levelDict.simulateTotalTime))
end

GMHandleLogic.SetSimulateTimeLevel3 = function(loadTime, oneFrameTime, totalOpenTime)
    local field = Module.GM.Field
    local levelDict = field.simulateTimeByLevel[3]
    if not levelDict then return end
    levelDict.simulateLoadTime = loadTime or 0
    levelDict.simulateOpenTime = oneFrameTime or 0
    levelDict.simulateTotalTime = totalOpenTime or 0
    Module.CommonTips:ShowSimpleTip(string.format("set level 3: %f %f %f", levelDict.simulateLoadTime, levelDict.simulateOpenTime, levelDict.simulateTotalTime))
end

GMHandleLogic.OpenEmptyTestUIWithCutSceneByLevel = function(CutSceneInTime, CutSceneOutTime, level)
    local field = Module.GM.Field
    local cutSceneUI = field.cutSceneHandel and field.cutSceneHandel:GetUIIns()
    if cutSceneUI then
        local afterOpenRealUI = function()
            cutSceneUI:TryHide()
        end
        local delay = function()
            local dict = field.simulateTimeByLevel[level]
            logerror("OpenEmptyTestUIWithCutSceneByLevel", level, dict.simulateLoadTime, dict.simulateOpenTime, dict.simulateTotalTime)
            GMHandleLogic.OpenEmptyTestUI(dict.simulateLoadTime, dict.simulateOpenTime, dict.simulateTotalTime, afterOpenRealUI)
        end
        if CutSceneInTime > 0 then
            cutSceneUI:SetData(CutSceneInTime, CutSceneOutTime)
            cutSceneUI:StopAllAnimations()
            cutSceneUI:Show()
            Timer.DelayCall(CutSceneInTime, delay)
        else
            delay()
        end
    else
        local onLoadFinish = function(uiIns)
            uiIns:TryHide()
        end
        field.cutSceneHandel = Facade.UIManager:AsyncShowUI(UIName2ID.GMCutSecenUI, onLoadFinish, nil)
    end
end

GMHandleLogic.OpenEmptyTestUIByLevel = function(level)
    local field = Module.GM.Field

    local delay = function()
        local dict = field.simulateTimeByLevel[level]
        logerror("OpenEmptyTestUIWithCutSceneByLevel", level, dict.simulateLoadTime, dict.simulateOpenTime, dict.simulateTotalTime)
        GMHandleLogic.OpenEmptyTestUI(dict.simulateLoadTime, dict.simulateOpenTime, dict.simulateTotalTime)
    end

    delay()

end

--region 战略板
--预加载战略版
GMHandleLogic.PreloadSandBoxMap=function(preloadNum)
    Module.IrisSafeHouse:SetPreloadSandBoxMap(preloadNum)
end

--隐藏战略版3dui
GMHandleLogic.HideSandBoxMap3DUI=function(hideNum)
    Module.SandBoxMap:GMHide3DUI(hideNum==1)
end

--是否播放入场动画
GMHandleLogic.GMHideGrow=function(num)
    Module.SandBoxMap:GMHideGrow(num==1)
end

GMHandleLogic.GMHideBuildings=function(num)
    Module.SandBoxMap:GMHideBuildings(num==1)
end

GMHandleLogic.GMHideLine=function(num)
    Module.SandBoxMap:GMHideLine(num==1)
end

GMHandleLogic.GMHideMap=function(num)
    Module.SandBoxMap:GMHideMap(num==1)
end

GMHandleLogic.GMHideParticles=function(num)
    Module.SandBoxMap:GMHideParticles(num==1)
end

GMHandleLogic.SandBoxSetPlayMapInSeq=function(num)
    Module.SandBoxMap:GMSetPlayMapInSeq(num==1)
end

GMHandleLogic.GMHideSelect=function(num)
    Module.SandBoxMap:GMHideSelect(num==1)
end

GMHandleLogic.GMHideGrowth=function(num)
    Module.SandBoxMap:GMHideGrowth(num==1)
end

GMHandleLogic.GMHideBGPlane=function(num)
    Module.SandBoxMap:GMHideBGPlane(num==1)
end

GMHandleLogic.GMHideFog=function(num)
    Module.SandBoxMap:GMHideFog(num==1)
end
--endregion

--region 成就相关
--完成单局成就
GMHandleLogic.CompleteAchievements = function(properties, properties2)
    if type(properties) == "number" then    
        properties = {properties, 1000}
    elseif type(properties) ~= "table" then
        properties = {1001, 1000}
    end

    if type(properties2) == "number" then    
        properties2 = {properties2, 1000}
    elseif type(properties2) ~= "table" then
        properties2 = {1006, 1000}
    end

    local gmType = EGMType.kGMTypeCompleteAchievements
    local args = {
        -- 单局成就id
        properties[1],
        -- 完成次数
        properties[2],
        -- 单局成就id2
        properties2[1],
        -- 完成次数2
        properties2[2],
    }
    return Server.GMServer:DoGMReq(gmType, args)
end

--完成生涯成就
GMHandleLogic.CompleteCareerAchievements = function(id)
    local gmType = EGMType.kGMTypeCompleteCareerAchievements
    local args = {
        -- 生涯成就id
        id
    }
    return Server.GMServer:DoGMReq(gmType, args)
end
--endregion

--修改排位分
GMHandleLogic.SetRankScore=function(score)
    loginfo("GMHandleLogic.SetRankScore",score)
    local gmType = EGMType.kGMTypeSeasonSetRankScore
    local args = {
        score
    }

    local function fSetRankScoreRes(res)
        loginfo("fSetRankScoreRes",res.result)
    end
    return Server.GMServer:DoGMReq(gmType, args, fSetRankScoreRes)
end

--修改大战场积分
---@param subScore table
GMHandleLogic.SetTournamentScore=function(subScore)
    loginfo("GMHandleLogic.SetTournamentScore")
    if type(subScore) ~= "table" then
        Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMParamInValid)
        return
    end
    logtable(subScore,true)
    local scoreShoot=subScore[1] or 0
    local scoreTactics=subScore[2] or 0
    local scoreVehicle=subScore[3] or 0

    local gmType = EGMType.kGMTypeSeasonSetMPRankScore
    local args = {scoreShoot,scoreTactics,scoreVehicle}

    local function fSetTournamentScoreRes(res)
        loginfo("fSetTournamentScoreRes",res.result)
    end
    return Server.GMServer:DoGMReq(gmType, args, fSetTournamentScoreRes)
end

--修改胜者为王分
---@param subScore table
GMHandleLogic.SetCommanderScore=function(score)
    loginfo("GMHandleLogic.SetCommanderScore")
    
    local gmType = EGMType.kGMTypeSeasonSetMPCommanderScore
    local args = {
        score
    }

    local function fSetCommanderScoreRes(res)
        loginfo("fSetCommanderScoreRes",res.result)
    end
    return Server.GMServer:DoGMReq(gmType, args, fSetCommanderScoreRes)
end

--修改排位币
GMHandleLogic.SetRankCoin=function(coinNum)
    loginfo("GMHandleLogic.SetRankCoin",coinNum)
    local gmType = EGMType.kGMTypeSeasonSetRankCoins
    local args = {
        coinNum
    }

    local function fSetRankCoinRes(res)
        loginfo("fSetRankCoinRes",res.result)
    end
    return Server.GMServer:DoGMReq(gmType, args, fSetRankCoinRes)
end

GMHandleLogic.OpenLoadingMovie = function()
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "r.Loading.EnableLoadingMovie 1",
        nil)
end

--------BHD
GMHandleLogic.SetBHDEnabled=function(enabled)
    CloseModuleType.bIsCloseBHDSwitch = not enabled
    loginfo('GMHandleLogic.SetBHDEnabled 设置BHD入口开启 = ', enabled)
    Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMPromptResultSucceeded)
end

GMHandleLogic.GMBHDUnlockChapter = function(chapterId)
    if not chapterId then return end
    local gmType = EGMType.kGMTypeBhdSettlement
    -- local args = {Server.AccountServer:GetPlayerId(), chapterId}
    local args = {Server.AccountServer:GetPlayerIdStr(), chapterId}
    local callback = function()
        Server.BHDServer:ReqBHDGetChapterList()
    end
    return Server.GMServer:DoGMReq(gmType, args, callback)
end

GMHandleLogic.GMSetBHDPreload = function(bPreload)
    bPreload = setdefault(bPreload, false)
    if DFHD_LUA == 1 then
        local bhdGameController = Facade.GameFlowManager:GetBHDGameController()
        if bhdGameController.SetIsPreloadBHD then
            bhdGameController:SetIsPreloadBHD(bPreload)
            Module.CommonTips:ShowSimpleTip(Module.GM.Config.Loc.GMPromptResultSucceeded)
        end
    else
        logerror('GMHandleLogic.GMSetBHDPreload DFHD_LUA == 1 ', DFHD_LUA == 1)
    end
end


GMHandleLogic.MainFullFillSelfCharacter = function(bFullfill)
    if bFullfill == 0 then
        Module.LobbyDisplay:SetMainFlowFullfillSelfCharacter(false)
    elseif bFullfill == 1 then
        Module.LobbyDisplay:SetMainFlowFullfillSelfCharacter(true)
    end
end

GMHandleLogic.OpenRoomSelectPanel = function(isOpen)
    isOpen = setdefault(isOpen, true)
    local targetUIInst = Facade.UIManager:GetStackUIByUINavId(UIName2ID.RoomSelectPanel)
    Module.Room:SetGMFlag(isOpen)
    if not targetUIInst and isOpen then
        Module.Room:ShowSelectRoomPanel()
        loginfo('GMHandleLogic.OpenRoomSelectPanel 打开自建房界面')
    end
end

GMHandleLogic.SetDisplayWatchItemId = function(inWatchItemId)
    Module.LobbyDisplay:SetDisplayWatchItemId(inWatchItemId,false)
end

-- 宣发录制
GMHandleLogic.GameExtremeQualityMode = function()
    local cmdList = {
        "g.Significance.FreezeTick 1",
        "r.Streaming.PoolSize 100000",
        "r.ForceLOD 0",
        "r.SkeletalMeshLODBias -2",
        "r.StaticMeshLODDistanceScale 0.1",
        "foliage.LODDistanceScale 100",
        "m.sil HLOD",
        "r.PRCDiv 1",
        "g.Significance.DynamicParticleSignificane 0",
        "m.sld landmark=120000,,120000",
        "m.sld bigmeshgroup=120000,,120000",
        "m.sld midmeshgroup=120000,,120000",
        "m.sld Grass=120000,,120000",
        "m.sld Tree=120000,,120000",
        "m.sld Bushes=120000,,120000",
        "r.ViewDistanceScale 10",
        "r.CullScreenSizeRatio 0",
        "foliage.DisableCull 1",
        "openworld.allowprecomputedvisibility 0",
        "r.AntiAliasingMethod 2",
        "r.NGX.DLSS.Enable 2",
        "r.SSRTAO.HalfRes 1",
        "r.SSRTAO.Denoise.Enable 1",
        "r.Shadow.ScrollingCSMC.Enable 0",
        "r.Shadow.DistanceScale 100",
        "r.Shadow.MaxCSMResolution 8192",
        "r.Shadow.RadiusThreshold 0",
        "r.Shadow.EnableDirectionalLightPreBakeShadow 0",
        "r.raytracing.reflections 1",
        "r.raytracing.reflections.roughnessmultiplier 0.8",
        "r.RayTracing.Reflections.MaxRoughness 0.25",
        "r.RayTracing.Reflections.Hybrid 1",
        "r.ShadowQualityFPP 5",
        "r.Shadow.MaxFirstPersonShadowResolution 4096",
        "r.TPPMeshCastShadowUnderFPP 1",
        "r.Shadow.AllowLocalLightCastsFPPShadow 1",
        "r.Shadow.FPPTransitionScale 1000"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeAnimationMode = function()
    local cmdList = {
        "g.Significance.FreezeTick 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeAssetMode = function()
    local cmdList = {
        "g.Significance.FreezeTick 1",
        "r.Streaming.PoolSize 100000",
        "r.ForceLOD 0",
        "r.SkeletalMeshLODBias -2",
        "r.StaticMeshLODDistanceScale 0.1",
        "foliage.LODDistanceScale 100",
        "m.sil HLOD",
        "r.PRCDiv 1",
        "g.Significance.DynamicParticleSignificane 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeLoadDistanceMode = function()
    local cmdList = {
        "m.sld landmark=120000,,120000",
        "m.sld bigmeshgroup=120000,,120000",
        "m.sld midmeshgroup=120000,,120000",
        "m.sld Grass=120000,,120000",
        "m.sld Tree=120000,,120000",
        "m.sld Bushes=120000,,120000",
        "r.ViewDistanceScale 10",
        "r.CullScreenSizeRatio 0",
        "foliage.DisableCull 1",
        "openworld.allowprecomputedvisibility 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeDLSSMode = function()
    local cmdList = {
        "r.AntiAliasingMethod 2",
        "r.NGX.DLSS.Enable 2"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeShadowMode = function()
    local cmdList = {
        "r.SSRTAO.HalfRes 1",
        "r.SSRTAO.Denoise.Enable 1",
        "r.Shadow.ScrollingCSMC.Enable 0",
        "r.Shadow.DistanceScale 100",
        "r.Shadow.MaxCSMResolution 8192",
        "r.Shadow.RadiusThreshold 0",
        "r.Shadow.EnableDirectionalLightPreBakeShadow 0"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeRayTracingMode = function()
    local cmdList = {
        "r.raytracing.reflections 1",
        "r.raytracing.reflections.roughnessmultiplier 0.8",
        "r.RayTracing.Reflections.MaxRoughness 0.25",
        "r.RayTracing.Reflections.Hybrid 1"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

GMHandleLogic.GameExtremeFPPMode = function()
    local cmdList = {
        "r.ShadowQualityFPP 5",
        "r.Shadow.MaxFirstPersonShadowResolution 4096",
        "r.TPPMeshCastShadowUnderFPP 1",
        "r.Shadow.AllowLocalLightCastsFPPShadow 1",
        "r.Shadow.FPPTransitionScale 1000"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

-- 宣发录制-手游
GMHandleLogic.GameExtremeQualityModeMobile = function()
    local cmdList = {
        "r.MobileMSAA 8",
        "r.showflag.bloom 0",
        "r.OverrideMobileContentScaleFactor 1440",
        "r.ScreenPercentage 100",
        "r.Streaming.PoolSize 10000",
        "r.ForceLOD 0",
        "r.SkeletalMeshLODBias -2",
        "r.StaticMeshLODDistanceScale 0.1",
        "foliage.LODDistanceScale 1",
        "m.sil HLOD",
        "r.PRCDiv 1",
        "g.Significance.DynamicParticleSignificane 0",
        "m.sld landmark 120000",
        "m.sld bigmeshgroup 120000",
        "m.sld midmeshgroup 120000",
        "m.sld Grass 100000",
        "m.sld Tree 1200000",
        "m.sld Bushes 1200000",
        "r.ViewDistanceScale 10",
        "r.CullScreenSizeRatio 0",
        "foliage.DisableCull 1",
        "openworld.allowprecomputedvisibility 0",
        "r.Shadow.ScrollingCSMC.Enable 0",
        "r.Shadow.DistanceScale 2",
        "r.Shadow.MaxCSMResolution 2048",
        "r.Shadow.RadiusThreshold 0",
        "r.ShadowQualityFPP 5",
        "r.Shadow.MaxFirstPersonShadowResolution 2048",
        "r.TPPMeshCastShadowUnderFPP 1",
        "r.Shadow.AllowLocalLightCastsFPPShadow 1",
        "r.Shadow.FPPTransitionScale 1000"
    }
    local worldContext = GetGameInstance()
    for _,cmd in ipairs(cmdList) do 
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
end

--一键毕业
GMHandleLogic.GMHeroGrowLineQuickGraduation = function(heroId)
    local gmType = EGMType.kGMTypeHeroGrowLineQuickGraduation
    local args = {tonumber(heroId)}
    return Server.GMServer:DoGMReq(gmType, args)
end

--完成指定目标
GMHandleLogic.GMHeroGrowLineFinishGoal = function(heroId, goalId)
    local gmType = EGMType.kGMTypeHeroGrowLineFinishGoal
    local args = {tonumber(heroId), tonumber(goalId)}
    return Server.GMServer:DoGMReq(gmType, args)
end

--跳转到指定等级
GMHandleLogic.GMHeroGrowLineJumpLevel = function(heroId, level)
    local gmType = EGMType.kGMTypeHeroGrowLineJumpLevel
    local args = {tonumber(heroId), tonumber(level)}
    return Server.GMServer:DoGMReq(gmType, args)
end

--重置指定目标任务进度
GMHandleLogic.GMHeroGrowLineResetGoal = function(heroId, goalId)
    local gmType = EGMType.kGMTypeHeroGrowLineResetGoal
    local args = {tonumber(heroId), tonumber(goalId)}
    return Server.GMServer:DoGMReq(gmType, args)
end

--重置当前干员成长线所有等级进度
GMHandleLogic.GMHeroGrowLineResetLevel = function(heroId)
    local gmType = EGMType.kGMTypeHeroGrowLineResetLevel
    local args = {tonumber(heroId)}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMDebugUnlockPoolSize = function()
    local cmd = "r.Streaming.PoolSizeForMeshes 1000"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugForceLOD = function(LODIndex)
    local LodLevel = tonumber(LODIndex) or 0
    local cmd = string.format("r.forcelod %d", LodLevel)
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugRevertLOD = function()
    local cmd = "r.forcelod -1"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

-- 恢复/关闭Level PVS
GMHandleLogic.GMDebugLevelPVS = function()
    local cmd = "OpenWorld.EnableLevelPVS"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

-- 隐藏HLOD（PC）
GMHandleLogic.GMDebugHideHLOD_PC = function()
    local cmd = "m.sld  bigmeshgroup=102400"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

-- 隐藏HLOD（mobile精致以上）
GMHandleLogic.GMDebugHideHLOD_MobileH = function()
    local cmd = "m.sld batch_hq_bigmeshgroup=102400"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

-- 隐藏HLOD（mobile流畅标准）
GMHandleLogic.GMDebugHideHLOD_MobileL = function()
    local cmd = "m.sld batch_lq_bigmeshgroup=102400"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugHideHLOD = function()
    local cmd = "m.sil hlod"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugHideHLOD_Mobile = function()
    local cmd = "m.sil batch_hq_bigMeshgroup_h1"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugRevertSIL = function()
    local cmd = "m.sil reset"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOffCullScreenSize = function()
    local cmd = "r.cullscreensizeratio 0"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOffPVS = function()
    local cmd = "OpenWorld.AllowPrecomputedVisibility 0"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOnPVS = function()
    local cmd = "OpenWorld.AllowPrecomputedVisibility 1"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugHideRoom = function()
    local cmd = "m.sil room"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugHideRoom_Mobile = function()
    local cmd = "m.sil batch_hq_room"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOffSOC = function()
    local cmd = "r.Mobile.AllowSoftwareOcclusion 0"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOnSOC = function()
    local cmd = "r.Mobile.AllowSoftwareOcclusion 1"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOffHOC = function()
    local cmd = "r.AllowOcclusionQueries 0"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMDebugTurnOnHOC = function()
    local cmd = "r.AllowOcclusionQueries 1"
    local worldContext = GetGameInstance()
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMResetExitTrigger = function()
    Module.GM.OnCloseGM()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMResetExitTrigger(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMDebugBackToLogin = function()
    Module.Login:BackToLogin()
end

GMHandleLogic.SetAINonPerceptual = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.SetAINonPerceptual(localCtrl.PlayerGMComponent, true)
    end
end
GMHandleLogic.GhostWalk = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.GPInputManager ~= nil then
        localCtrl.GPInputManager:ProcessGMToggleGhostWalk(localCtrl.PlayerGMComponent)
    end
end
GMHandleLogic.FlyWalk = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.GPInputManager ~= nil then
        localCtrl.GPInputManager:ProcessGMToggleFlyWalk(localCtrl.PlayerGMComponent)
    end
end
GMHandleLogic.AddSpeed100 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.GPInputManager ~= nil then
        localCtrl.GPInputManager:ProcessGMAddSpeed100(localCtrl.PlayerGMComponent)
    end
end
GMHandleLogic.MinusSpeed100 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.GPInputManager ~= nil then
        localCtrl.GPInputManager:ProcessGMMinusSpeed100(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.PhysXDrawing = function()
    local worldContext = GetGameInstance()

    if Module.GM._EnablePhysXDrawing then 
        Module.GM._EnablePhysXDrawing = false
        local cmd = "PhysXDrawing 0"
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    else 
        Module.GM._EnablePhysXDrawing = true
        local cmd = "PhysXDrawing 1"
        UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    end
    
end

GMHandleLogic.ChangeTOD2703 = function(type)
    local worldContext = GetGameInstance()
    local cmd = "r.SeamlessTODld".. " " .. tostring(type)
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
    cmd = "r.DSTODId".. " " .. tostring(type)
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)

end

GMHandleLogic.VideoClamp = function(type)
    local worldContext = GetGameInstance()
    local cmd = "r.EnableVideoSettingClamp".. " " .. tostring(type)
    UKismetSystemLibrary.ExecuteConsoleCommand(worldContext, cmd)
end

GMHandleLogic.GMOpenInteractSystemDetailLog = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMOpenInteractSystemDetailLog(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMOpenDSInteractSystemDetailLog = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMOpenDSInteractSystemDetailLog(localCtrl.PlayerGMComponent)
    end
end

GMHandleLogic.GMOpenInteractComponentTick = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMOpenInteractComponentTick(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.GMCloseInteractComponentTick = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMOpenInteractComponentTick(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenCheckAndRefresh = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetNeedCheckAndRefresh(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseCheckAndRefresh = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetNeedCheckAndRefresh(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenInteractSystemDSCheck = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetInteractSystemDSCheck(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseInteractSystemDSCheck = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetInteractSystemDSCheck(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenForwardAndCheckHitFirstly = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseForwardAndCheckHitFirstly(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseForwardAndCheckHitFirstly = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseForwardAndCheckHitFirstly(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenDSCheckNearly = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseDSCheckNearly(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseDSCheckNearly = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseDSCheckNearly(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.IOSOpenCheckCollSweep = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCollSweep(localCtrl.PlayerGMComponent, 1, true, 1)
    end
end

GMHandleLogic.IOSCloseCheckCollSweep = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCollSweep(localCtrl.PlayerGMComponent, 1, false, 1)
    end
end

GMHandleLogic.AndroidOpenCheckCollSweep = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCollSweep(localCtrl.PlayerGMComponent, 2, true, 1)
    end
end

GMHandleLogic.AndroidCloseCheckCollSweep = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCollSweep(localCtrl.PlayerGMComponent, 2, false, 1)
    end
end

GMHandleLogic.PCOpenCheckCollSweep = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCollSweep(localCtrl.PlayerGMComponent, 3, true, 1)
    end
end

GMHandleLogic.PCCloseCheckCollSweep = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCollSweep(localCtrl.PlayerGMComponent, 3, false, 1)
    end
end

GMHandleLogic.OpenCheckHack = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckHack(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseCheckHack = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckHack(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenCheckCapture = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCapture(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseCheckCapture = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckCapture(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenCheckUnlock = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckUnlock(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseCheckUnlock = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseCheckUnlock(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.OpenUseClientWhenCheckPre = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseClientWhenCheckPreInMaxDis(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.CloseUseClientWhenCheckPre = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseClientWhenCheckPreInMaxDis(localCtrl.PlayerGMComponent, false)
    end
end

GMHandleLogic.UseClientOverlapWhenCheckPre100 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseClientOverlapWhenCheckPreInMaxDis(localCtrl.PlayerGMComponent, true, 100)
    end
end

GMHandleLogic.UseClientOverlapWhenCheckPre200 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseClientOverlapWhenCheckPreInMaxDis(localCtrl.PlayerGMComponent, true, 200)
    end
end

GMHandleLogic.UseClientOverlapWhenCheckPre300 = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseClientOverlapWhenCheckPreInMaxDis(localCtrl.PlayerGMComponent, true, 300)
    end
end

GMHandleLogic.CloseUseClientOverlapWhenCheckPre = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMUseClientOverlapWhenCheckPreInMaxDis(localCtrl.PlayerGMComponent, false, 200)
    end
end

GMHandleLogic.SetInteractorCheckCollisionChannelPawn = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetInteractorCheckCollisionChannel(localCtrl.PlayerGMComponent, true)
    end
end

GMHandleLogic.SetInteractorCheckCollisionChannelNotPawn = function()
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent.GMSetInteractorCheckCollisionChannel(localCtrl.PlayerGMComponent, false)
    end
end

local function _GMArmorySetPlayerDataImpl(armoryID, goalID, progressCnt)
    local gmType = EGMType.kGMTypeCSArmorySetPlayerData
    local args = {armoryID, goalID, progressCnt}
    local function OnRes(res)
        Server.ArmoryServer:FetchServerData()
    end
    local isSuccessful = Server.GMServer:DoGMReq(gmType, args, OnRes)
    return isSuccessful
end

-- 军械库设置当前任务进度
function GMHandleLogic.GMArmorySetPlayerData(progress)
    for _, item_ in ipairs(Server.ArmoryServer.armoryItems) do
        local item = item_ ---@type ArmoryItemData
        if item.bActivated then
            _GMArmorySetPlayerDataImpl(item.itemID, item:GetCurrentTask().goalID, progress)
            return
        end
    end
    return isSuccessful
end

-- 军械库完成本阶段任务
function GMHandleLogic.GMArmoryCompleteStage()
    for _, item_ in ipairs(Server.ArmoryServer.armoryItems) do
        local item = item_ ---@type ArmoryItemData
        if item.bActivated then
            _GMArmorySetPlayerDataImpl(item.itemID, item:GetCurrentTask().goalID, item:GetCurrentTask().taskGoal)
            return
        end
    end
end

GMHandleLogic.GMExportHeroAppearanceInfo = function()
    local EHeroShapeShiftType = import "EHeroShapeShiftType"
    local avatarIdList = {
        "30000020002",
        "30000020003",
        "30000020004",
        "30000020005",
        "30000020006",
        "30000020007",
        "30000020008",
        "30000020009",
        "30000020010",
        "30000020011",
        "30000020012",

        "30000040004",
        "30000040005",
        "30000040006",
        "30000040007",

        "30000050002",
        "30000050008",
        "30000050009",
        "30000050010",
        "30000050011",
        "30000050012",
        "30000050013",
        "30000050014",

        "30000060001",
        "30000060002",
        "30000060004",
        "30000060005",

        "88000000025",
        "88000000026",
        "88000000027",
        "88000000028",
        "88000000029",
        "88000000030",
        "88000000035",
        "88000000036",
        "88000000037",
        "88000000038",
        "88000000039",
    }

    local allShapeShift
    if IsHD() then
        allShapeShift = {
            EHeroShapeShiftType.Zero,
            EHeroShapeShiftType.One,
            EHeroShapeShiftType.MpGTIZero,
            EHeroShapeShiftType.MpGTIOne,
            EHeroShapeShiftType.MpHavvkZero,
            EHeroShapeShiftType.MpHavvkOne,
            EHeroShapeShiftType.BodyOne,
            EHeroShapeShiftType.Prisoner,
        }
    else
        allShapeShift = {
            EHeroShapeShiftType.Zero,
            EHeroShapeShiftType.One,
            EHeroShapeShiftType.MpGTIZero,
            EHeroShapeShiftType.MpGTIOne,
            EHeroShapeShiftType.BodyOne,
            EHeroShapeShiftType.Prisoner,
        }
    end
    local ChageShapeShift = function(shapeShift)
        local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
        if localCharacter then
            local Comp = localCharacter.DFMCharacterAppearanceTPP
            if Comp and shapeShift then
                logwarning("GMExportHeroAppearanceInfo ------------ ChageShapeShift:", shapeShift)
                --Comp:SetForceShapeType(shapeShift, true)
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), string.format("character.ForceShapeShift %d", shapeShift), nil)
            end
        end
    end

    local ChangeHero =function(avatarId)
        GMHandleLogic.SwitchCharacterAvatar(avatarId)
        loginfo("GMExportHeroAppearanceInfo ----- ChangeHero:", avatarId)
    end

    local idx = 0
    for __, shape in pairs(allShapeShift) do
        Timer.DelayCall(5* idx, ChageShapeShift, nil, shape)
        for _, id in pairs(avatarIdList) do
            Timer.DelayCall(1 + 5* idx, ChangeHero, nil, id)
            local delay = function()
                loginfo("GMExportHeroAppearanceInfoChangeHero record")
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMGetCurAllCharacterAppearanceInfo", nil)
            end
            Timer.DelayCall(5*idx + 3, delay)
            idx = idx + 1
        end
    end

    local delay1 = function()
        logerror("GMExportHeroAppearanceInfo ChangeHero end")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMExportAllCharacterAppearanceInfo", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "character.ForceShapeShift -1", nil)
    end
    Timer.DelayCall(5* (idx + 1), delay1)
end

GMHandleLogic.GMExportHeroAppearanceInfoCommon = function(avatarIdList)
    
    local EHeroShapeShiftType = import "EHeroShapeShiftType"
    local avatarTable = Facade.TableManager:GetTable("CharacterAvatarData")

    local needCheckList = {}
    for _, avatarId in pairs(avatarIdList) do
        local avatarCfg = avatarTable[tostring(avatarId)]
        if avatarCfg then
            loginfo("[ExportHeroAppearance] add config:", avatarId, "  shape:", EHeroShapeShiftType.Zero)
            table.insert(needCheckList, {id=avatarId, shape=EHeroShapeShiftType.Zero})
            for __, characterShapeShift in ipairs(avatarCfg.TPPShapeShift) do
                loginfo("[ExportHeroAppearance] add config:", avatarId, "  shape:", characterShapeShift.ShapeType)
                table.insert(needCheckList, {id=avatarId, shape=characterShapeShift.ShapeType})
            end
        end
    end

    logwarning("[ExportHeroAppearance] check count: ", #needCheckList)

    local ChageShapeShift = function(shapeShift)
        local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
        if localCharacter then
            local Comp = localCharacter.DFMCharacterAppearanceTPP
            if Comp and shapeShift then
                logwarning("GMExportHeroAppearanceInfo ------------ ChageShapeShift:", shapeShift)
                --Comp:SetForceShapeType(shapeShift, true)
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), string.format("character.ForceShapeShift %d", shapeShift), nil)
            end
        end
    end

    local ChangeHero =function(avatarId)
        GMHandleLogic.SwitchCharacterAvatar(avatarId)
        loginfo("GMExportHeroAppearanceInfo ----- ChangeHero:", avatarId)
    end

    local idx = 0
    for _, cfg in ipairs(needCheckList) do
        Timer.DelayCall(5* idx, ChageShapeShift, nil, cfg.shape)
        Timer.DelayCall(1 + 5* idx, ChangeHero, nil, cfg.id)
        local delay = function()
            loginfo("GMExportHeroAppearanceInfoChangeHero record")
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMGetCurAllCharacterAppearanceInfo", nil)
        end
        Timer.DelayCall(5*idx + 3, delay)
        idx = idx + 1
    end

    local delay1 = function()
        logerror("GMExportHeroAppearanceInfo ChangeHero end")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GMExportAllCharacterAppearanceInfo", nil)
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "character.ForceShapeShift -1", nil)
    end
    Timer.DelayCall(5* (idx + 1), delay1)
end

GMHandleLogic.GMExportHeroAppearanceInfoMp = function()
    local avatarIdList = {
        "30000020002",
        "30000020003",
        "30000020004",
        "30000020005",
        "30000020006",
        "30000020007",
        "30000020008",
        "30000020009",
        "30000020010",
        "30000020011",
        "30000020012",

        "30000040004",
        "30000040005",
        "30000040006",
        "30000040007",

        "30000050002",
        "30000050008",
        "30000050009",
        "30000050010",
        "30000050011",
        "30000050012",
        "30000050013",
        "30000050014",

        "30000060001",
        "30000060002",
        "30000060004",
        "30000060005",
    }
    GMHandleLogic.GMExportHeroAppearanceInfoCommon(avatarIdList)
end

GMHandleLogic.GMExportHeroAppearanceInfoSol = function()
    local avatarIdList = {
        "30000040004",
        "30000040005",
        "30000040006",
        "30000040007",

        "30000050002",
        "30000050008",
        "30000050009",
        "30000050010",
        "30000050011",
        "30000050012",
        "30000050013",
        "30000050014",

        "30000060001",
        "30000060002",
        "30000060004",
        "30000060005",

        "88000000025",
        "88000000026",
        "88000000027",
        "88000000028",
        "88000000029",
        "88000000030",
        "88000000035",
        "88000000036",
        "88000000037",
        "88000000038",
        "88000000039",
    }
    GMHandleLogic.GMExportHeroAppearanceInfoCommon(avatarIdList)
end

function GMHandleLogic.StartCharacterProfiler()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "EnableCharacterProfiler 1", nil)
end

function GMHandleLogic.StopCharacterProfiler()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "EnableCharacterProfiler 0", nil)
end

function GMHandleLogic.DumpCharacterProfiler()
    local UDFCharacterProfilerManager = import "DFCharacterProfilerManager"
    if UDFCharacterProfilerManager.Get(GetWorld()) then
        UDFCharacterProfilerManager.Get(GetWorld()):DumpResults()
    end
end

function GMHandleLogic.ClearCharacterProfiler()
    local UDFCharacterProfilerManager = import "DFCharacterProfilerManager"
    if UDFCharacterProfilerManager.Get(GetWorld()) then
        UDFCharacterProfilerManager.Get(GetWorld()):ClearCurData()
    end
end

--卸载关卡
function GMHandleLogic.UnLoadLevelByStage(substage)
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(substage, false)
end
--卸载所有关卡
function GMHandleLogic.UnLoadAllLevel()
    for k,substage in pairs(ESubStage) do 
        if substage > 0 and substage ~= ESubStage.MainFlowLobby then
            Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(substage, false)
        end
    end
end

function GMHandleLogic.ShowPlayTime(bShow)
    bShow = bShow > 0 and true or false
    Module.Settlement:GMShowPlayTime(bShow)
end

function GMHandleLogic.SetPlayerContributorID(contributorID)
    if contributorID == nil then
        return
    end
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        localCtrl.PlayerGMComponent:ServerSetPlayerContributorID(contributorID)
    end
end

function GMHandleLogic.TryToSampleWaterData(SampledCount, MaxDistance)
    loginfo("TryToSampleWaterData Begin", SampledCount, MaxDistance)
    if SampledCount == nil then
        return
    end
    
    local localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)

    loginfo("TryToSampleWaterData", SampledCount, MaxDistance, localCtrl)

    if localCtrl ~= nil and localCtrl.PlayerGMComponent ~= nil then
        loginfo("TryToSampleWaterData")
        localCtrl.PlayerGMComponent:TryToSampleWaterData(SampledCount, MaxDistance)
    end

    loginfo("TryToSampleWaterData End")
end

GMHandleLogic.AddMPWeaponStarNum = function(weaponId, starNum, killCountNum)
    local gmType = EGMType.kGMTypeMPSetWeaponStar
    local args = {weaponId, starNum, killCountNum}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.AddMPWeaponKillCountNum = function(weaponId, killCountNum)
    local gmType = EGMType.kGMTypeMPAddWeaponKillCount
    local args = {weaponId, killCountNum}
    return Server.GMServer:DoGMReq(gmType, args)
end

GMHandleLogic.GMToggleActivityIdDisplay = function(bEnable)
    Server.ActivityServer:OnSetActPersonalized(bEnable)
end

GMHandleLogic.GMSetPickHeroExtraInfo = function(extraTime,repeatedHeroNum)
    loginfo("GMSetPickHeroExtraInfo",extraTime,repeatedHeroNum)
    extraTime=extraTime or 0
    repeatedHeroNum=repeatedHeroNum or 0
    if extraTime and extraTime >= 0 then
        Module.ArmedForce.Config.GMPickHeroExtraTime = extraTime
        Server.MatchServer:SetGMPickHeroExtraTime(extraTime)
        loginfo("GMSetPickHeroExtraTime:", extraTime)
    end
    Module.ArmedForce.Config.GMPickHeroRepeatedHeroNum = repeatedHeroNum

end

GMHandleLogic.GMCopyPlayerPositionToClipBoard = function()
    local localCharacter = UGameplayStatics.GetPlayerCharacter(GetGameInstance(), 0)
    if localCharacter then
        local position = localCharacter.GetActorLocation(localCharacter)
        local rotation = localCharacter.GetActorRotation(localCharacter)
        if ULuaExtension.RoleInfoCopy2Clipboard then
            local posStr = string.format("位置: (%f %f %f) \n角度：(%f, %f, %f)", position.X, position.Y, position.Z, rotation.Roll, rotation.Pitch, rotation.Yaw)
            ULuaExtension.RoleInfoCopy2Clipboard(tostring(posStr))
            GMHandleLogic._ShowMeesage(posStr)
        end
    end
end

GMHandleLogic.GMSetCommanderAbilityPercent = function(percentStr)--指挥，载具，步战，救援（0-1之间）
    loginfo("GMSetCommanderAbilityPercent",percentStr)
    if not percentStr or percentStr=="" then
        Server.TournamentServer:SetGMAbilityPercentInfo(nil)
    else
        Server.TournamentServer:SetGMAbilityPercentInfo(percentStr)
    end

end

--GameSDK初始化执行检查
GMHandleLogic.GMGameSDKExec = function()  
    local UGameSDKInitUtil = import "GameSDKInitUtil"
    local EGameSDKType = import "EGameSDKType"

    local GetExecResult = function(SDKType)
        local Rule = UGameSDKInitUtil.GetRule(SDKType)
        local RuleEffective = UGameSDKInitUtil.IsRuleEffective(Rule)
        local RuleInfo = UGameSDKInitUtil.GetRuleInfo(Rule)
        local Tips = string.format("SDK: %s, Effective: %s, Info: %s", SDKType, RuleEffective, RuleInfo)
        return Tips
    end
    Module.CommonTips:ShowSimpleTip(GetExecResult(EGameSDKType.Crashsight) .. "\r\n" .. GetExecResult(EGameSDKType.Perfsight), 30) 
end

--GameSDK初始化参数修改
GMHandleLogic.GMGameSDKParamDebug = function(region, xwid, enable)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.GameSDKTestRegion " .. region, nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.GameSDKTestXid " .. xwid, nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.GameSDKInitRule " .. enable, nil)
end

return GMHandleLogic
