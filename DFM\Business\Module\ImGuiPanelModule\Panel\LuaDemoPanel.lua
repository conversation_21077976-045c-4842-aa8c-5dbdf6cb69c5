----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMImGuiPanel)
----- LOG FUNCTION AUTO GENERATE END -----------



---@diagnostic disable: missing-parameter

local LuaDemoPanel = ImGuiPanelCls("LuaDemoPanel")

LuaDemoPanel.PanelWindowTitle = "LuaImGuiPanel的示例窗口"
LuaDemoPanel.bWithMenu = true


function LuaDemoPanel:OnGUI()
    self:MenuDemo() --- 显示菜单栏

    --- 设定Child块的尺寸
    if self.childSize == nil then
        self.childSize = ImVec2(400, 300)
    end

    ImGui.BeginChild("TextDemo", self.childSize, true)
    ImGui.TextColored(ImVec4(1,1,0,1),"文本显示Demo")
    self:TextDemo()
    ImGui.EndChild()

    ImGui.SameLine()  -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("InputDemo", self.childSize, true)
    ImGui.TextColored(ImVec4(1,1,0,1),"属性编辑Demo")
    self:InputDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("ButtonDemo", self.childSize, true)
    ImGui.TextColored(ImVec4(1,1,0,1),"按钮控件Demo")
    self:ButtonDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("TabBarDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"Tab Bar Demo")
    self:TabBarDemo()
    ImGui.EndChild()

    -----------------------------------------------------------------------------------------------
    
    ImGui.BeginChild("PlotLineDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"画线Demo")
    self:PlotLineDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("ColumnDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"表格Demo")
    self:ColumnDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("PopupDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"弹窗Demo")
    self:PopupDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("OtherDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"其他属性输入Demo")
    self:OtherInputDemo()
    ImGui.EndChild()

    -----------------------------------------------------------------------------------------------
    
    ImGui.BeginChild("ConsoleVarDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"控制台变量编辑器Demo")
    self:ConsoleVarDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("UObjectDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"UObject查看器Demo")
    self:ObjectDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("ColorEditDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"颜色编辑Demo")
    self:ColorEditDemo()
    ImGui.EndChild()

    ImGui.SameLine() -- 和前一个Child Block保持同一行布局

    ImGui.BeginChild("LayoutDemo",self.childSize,true)
    ImGui.TextColored(ImVec4(1,1,0,1),"布局与风格化控制Demo")
    self:LayoutDemo()
    ImGui.EndChild()
end

function LuaDemoPanel:TextDemo()
    ImGui.Text("Hello World")

    ImGui.Text("%s",self.PanelWindowTitle)


    ImGui.Text("这是一行无法自动换行的长文本，这是一行无法自动换行的长文本")

    ImGui.TextWrapped("这是一行可以自动换行的长文本，这是一行可以自动换行的长文本")

    ImGui.TextColored(ImVec4(1,0,0,1),"这是一行红色文本")
    ImGui.TextDisabled("这是一行标灰的文本")
    ImGui.BulletText("这是一行带弹孔标记的文本")
    ImGui.LabelText("Label","这是一行带lable的文本")
    
end

function LuaDemoPanel:InputDemo()
    if self.textBuf == nil then
        self.textBuf=ImGuiTextBuf(512)
    end

    ImGui.InputText("InputText",self.textBuf)
    ImGui.InputTextMultiline("InputTextMultiline",self.textBuf,ImVec2(ImGui.GetContentRegionAvail().x,50))
    ImGui.InputTextWithHint("InputTextWithHint","This is hint",self.textBuf)

    local changed = false
    if self.numberVal == nil then
        self.numberVal = 0.0
    end
    if self.integerVal == nil then
        self.integerVal = 0
    end
    if self.boolVal == nil then
        self.boolVal = false
    end
    changed,self.numberVal = ImGui.InputFloat("InputFloat",self.numberVal)
    changed,self.integerVal = ImGui.InputInt("InputInt",self.integerVal)
    changed,self.boolVal = ImGui.Checkbox("Checkbox", self.boolVal)
    

    local fruits = {"Apple","Orange","Banana"}
    if self.fruit == nil then
        self.fruit = "Apple"
    end
    if ImGui.BeginCombo("Combo", self.fruit) then
        for i, v in ipairs(fruits) do
            if ImGui.Selectable(v,self.fruit == v) then
                self.fruit = v
            end
        end
        ImGui.EndCombo()
    end

    if self.fv1 == nil then
        self.fv1 = 0.0
        self.fv2 = 0.0
        self.fv3 = 0.0
        self.fv4 = 0.0
    end

    changed,self.fv1,self.fv2 = ImGui.InputFloat2("InputFloat2",self.fv1,self.fv2)
    changed,self.fv1,self.fv2,self.fv3 = ImGui.InputFloat3("InputFloat3",self.fv1,self.fv2,self.fv3)
    changed,self.fv1,self.fv2,self.fv3,self.fv4 = ImGui.InputFloat4("InputFloat4",self.fv1,self.fv2,self.fv3,self.fv4)

    if self.iv1 == nil then
        self.iv1 = 0
        self.iv2 = 0
        self.iv3 = 0
        self.iv4 = 0
    end

    changed,self.iv1,self.iv2 = ImGui.InputInt2("InputInt2",self.iv1,self.iv2)
    changed,self.iv1,self.iv2,self.iv3 = ImGui.InputInt3("InputInt3",self.iv1,self.iv2,self.iv3)
    changed,self.iv1,self.iv2,self.iv3,self.iv4 = ImGui.InputInt4("InputInt4",self.iv1,self.iv2,self.iv3,self.iv4)

end

function LuaDemoPanel:ButtonDemo()
    if ImGui.Button("Button") then
        ImGui.ToastInfo("点击了Button")
    end

    if ImGui.Button("Big Button",ImVec2(ImGui.GetContentRegionAvail().x,50)) then
        ImGui.ToastInfo("点击了Big Button")
    end

    if ImGui.SmallButton("Small Button") then
        ImGui.ToastInfo("点击了small button")
    end

    ImGui.PushStyleColor(ImGui.ImGuiCol_Button, ImVec4(1,0,0,1))  --- 临时设置按钮的颜色为红色
    if ImGui.Button("Red Button",ImVec2(ImGui.GetContentRegionAvail().x,50)) then
        ImGui.ToastInfo("点击了Red Button")
    end
    ImGui.PopStyleColor()  --- 恢复按钮颜色设置

    if ImGui.ArrowButton("ArrowButton",ImGui.ImGuiDir_Up) then
        ImGui.ToastInfo("点击了ArrowButton")
    end

    if ImGui.InvisibleButton("InvisibleButton",ImVec2(ImGui.GetContentRegionAvail().x,10)) then
        ImGui.ToastInfo("点击了InvisibleButton")
    end

    if self.radioChecked == nil then
        self.radioChecked = false
    end
    if ImGui.RadioButton("RadioButton",self.radioChecked) then
        self.radioChecked = not self.radioChecked
    end

    ImGui.ProgressBar(0.5,ImVec2(ImGui.GetContentRegionAvail().x,20),"50%")

    ImGui.Bullet()
end

function LuaDemoPanel:MenuDemo()
    if ImGui.BeginMainMenuBar() then
        if ImGui.BeginMenu("Main Menu1") then
            if ImGui.MenuItem("Main Menu Item1") then
                ImGui.ToastInfo("点击了Main Menu Item1")
            end

            if ImGui.MenuItem("Main Menu Item2") then
                ImGui.ToastInfo("点击了Main Menu Item2")
            end
            ImGui.EndMenu()
        end

        if ImGui.BeginMenu("Main Menu2") then
            if ImGui.MenuItem("Main Menu Item1") then
                ImGui.ToastInfo("点击了Main Menu Item1")
            end

            if ImGui.MenuItem("Main Menu Item2") then
                ImGui.ToastInfo("点击了Main Menu Item2")
            end
            ImGui.EndMenu()
        end
        ImGui.EndMainMenuBar()
    end

    if ImGui.BeginMenuBar() then
        if ImGui.BeginMenu("Menu1") then
            if ImGui.MenuItem("Menu Item1") then
                ImGui.ToastInfo("点击了Menu Item1")
            end

            if ImGui.MenuItem("Menu Item2") then
                ImGui.ToastInfo("点击了Menu Item2")
            end
            ImGui.EndMenu()
        end

        if ImGui.BeginMenu("Menu2") then
            if ImGui.MenuItem("Menu Item1") then
                ImGui.ToastInfo("点击了Menu Item1")
            end

            if ImGui.MenuItem("Menu Item2") then
                ImGui.ToastInfo("点击了Menu Item2")
            end
            ImGui.EndMenu()
        end
        ImGui.EndMenuBar()
    end
end

function LuaDemoPanel:TabBarDemo()
    if ImGui.BeginTabBar("tab bar") then
        if ImGui.BeginTabItem("Tab1") then
            ImGui.Text("Hello World")
            ImGui.EndTabItem()
        end

        if ImGui.BeginTabItem("Tab2") then
            ImGui.Text("你好世界")
            ImGui.EndTabItem()
        end
        ImGui.EndTabBar()
    end
end

function LuaDemoPanel:PlotLineDemo()
    if self.floatBuf == nil then
        self.floatBuf = ImGuiFloatBuf(100)
        for i=1,100 do
            self.floatBuf:Set(i-1,i)
        end
    end
    ImGui.PlotLines("PlotLines",self.floatBuf)
    ImGui.PlotHistogram("PlotHistogram",self.floatBuf)
    ImGui.PlotLines("PlotLines Big",self.floatBuf,0,"Overlay",ImGui.ImGuiMaxFloat,ImGui.ImGuiMaxFloat,ImVec2(ImGui.GetContentRegionAvail().x,50))
    ImGui.PlotHistogram("PlotHistogram Big",self.floatBuf,0,"Overlay",ImGui.ImGuiMaxFloat,ImGui.ImGuiMaxFloat,ImVec2(ImGui.GetContentRegionAvail().x,50))
end

function LuaDemoPanel:ColumnDemo()
    local columnData = {
        -- name, gender, age
        {"Bob","male",16},
        {"Alice","female",20},
        {"Chris","male",18},
    }

    ImGui.Columns(3,"demo", true) --- Begin a sheet with 3 columns

    --- DataSheet header
    ImGui.Text("Name")
    ImGui.NextColumn() --- Move to next column
    ImGui.Text("Gender")
    ImGui.NextColumn() --- Move to next column
    ImGui.Text("Age")
    ImGui.NextColumn() --- Move to next column

    ImGui.Separator() --- Draw a separator line

    for i,item in ipairs(columnData) do
        for i=1,3 do
            ImGui.Text("%s",item[i])
            ImGui.NextColumn()
        end

        ImGui.Separator()  --- Draw a separator line for each row
    end

    ImGui.Columns() -- Finish this column

    ImGui.Text("Column Finished")
end

function LuaDemoPanel:PopupDemo()
    ImGui.TextWrapped("鼠标放这里，可以看到Tooltip")
    if ImGui.IsItemHovered() then -- 如果鼠标选服在前面绘制的控件上，也就是TextWrapped控件
        ImGui.SetTooltip("这是一条tooltip")
    end

    ImGui.TextWrapped("鼠标放这里，可以看到复杂Tooltip")
    if ImGui.IsItemHovered() then -- 如果鼠标选服在前面绘制的控件上，也就是TextWrapped控件
        ImGui.BeginTooltip()
        ImGui.Text("这是一个复杂的Tooltip")
        ImGui.Text("可以显示任意复杂的内容")
        ImGui.TextColored(ImVec4(1,0,0,1),"比如这个红色的文本")
        ImGui.Text("以及下面的表格")
        self:ColumnDemo()
        ImGui.EndTooltip()
    end

    ImGui.TextWrapped("鼠标放这里，可以看到复杂弹窗")
    if ImGui.IsItemHovered() then-- 如果鼠标选服在前面绘制的控件上，也就是TextWrapped控件
        ImGui.OpenPopup("MyPopup") -- 显示MyPopup弹窗
    end
    if ImGui.BeginPopup("MyPopup") then --- 只有前文调用了OpenPopup("MyPopup")，这里才会返回true，if里面填写弹窗的布局，并调用EndPopup收尾
        ImGui.TextWrapped("这是一个复杂的弹窗，打开后只有鼠标点击弹窗外的空白处才会消失，可以显示任何内容")
        ImGui.TextColored(ImVec4(1,0,0,1),"比如这个红色的文本")
        ImGui.Text("以及下面的表格")
        self:ColumnDemo()
        if ImGui.Button("点我关闭") then
            ImGui.CloseCurrentPopup() --- 关闭Popup必须在BeginPopup与EndPopup之间
        end
        ImGui.EndPopup()
    end
end

function LuaDemoPanel:OtherInputDemo()
    local changed = false
    if self.dragFloat == nil then
        self.dragFloat = 0.0
    end

    if self.dragInt == nil then
        self.dragInt = 0
    end

    if self.dragF1 == nil then
        self.dragF1 = 0.0
        self.dragF2 = 0.0
        self.dragF3 = 0.0
        self.dragF4 = 0.0
    end

    if self.dragI1 == nil then
        self.dragI1 = 0.0
        self.dragI2 = 0.0
        self.dragI3 = 0.0
        self.dragI4 = 0.0
    end

    ImGui.Text("提示：Ctrl+鼠标左键可以手动输入数值")
    changed,self.dragFloat = ImGui.DragFloat("DragFloat",self.dragFloat)
    changed,self.dragInt = ImGui.DragInt("DragInt",self.dragInt)
    changed,self.dragFloat = ImGui.SliderFloat("SliderFloat",self.dragFloat,0,100)
    changed,self.dragInt = ImGui.SliderInt("SliderInt",self.dragInt,0,100)

    changed, self.dragF1, self.dragF2 = ImGui.DragFloat2("DragFloat2", self.dragF1, self.dragF2)
    changed, self.dragF1, self.dragF2, self.dragF3 = ImGui.DragFloat3("DragFloat3", self.dragF1, self.dragF2, self.dragF3)
    changed, self.dragF1, self.dragF2, self.dragF3, self.dragF4 = ImGui.DragFloat4("DragFloat4", self.dragF1, self.dragF2, self.dragF3, self.dragF4)
    changed, self.dragI1, self.dragI2 = ImGui.DragInt2("DragInt2", self.dragI1, self.dragI2)
    changed, self.dragI1, self.dragI2, self.dragI3 = ImGui.DragInt3("DragInt3", self.dragI1, self.dragI2, self.dragI3)
    changed, self.dragI1, self.dragI2, self.dragI3, self.dragI4 = ImGui.DragInt4("DragInt4", self.dragI1, self.dragI2, self.dragI3, self.dragI4)

    changed, self.dragF1, self.dragF2 = ImGui.SliderFloat2("SliderFloat2", self.dragF1, self.dragF2, 0, 100)
    changed, self.dragF1, self.dragF2, self.dragF3 = ImGui.SliderFloat3("SliderFloat3", self.dragF1, self.dragF2, self.dragF3, 0, 100)
    changed, self.dragF1, self.dragF2, self.dragF3, self.dragF4 = ImGui.SliderFloat4("SliderFloat4", self.dragF1, self.dragF2, self.dragF3, self.dragF4, 0, 100)
    changed, self.dragI1, self.dragI2 = ImGui.SliderInt2("Slidernt2", self.dragI1, self.dragI2, 0, 100)
    changed, self.dragI1, self.dragI2, self.dragI3 = ImGui.SliderInt3("SliderInt3", self.dragI1, self.dragI2, self.dragI3, 0, 100)
    changed, self.dragI1, self.dragI2, self.dragI3, self.dragI4 = ImGui.SliderInt4("SliderInt4", self.dragI1, self.dragI2, self.dragI3, self.dragI4, 0, 100)

    changed,self.dragFloat = ImGui.VSliderFloat("VSliderFloat",ImVec2(50,200),self.dragFloat,0,100)
    ImGui.SameLine()
    changed,self.dragInt = ImGui.VSliderInt("VSliderInt",ImVec2(50,200),self.dragInt,0,100)
end


function LuaDemoPanel:ConsoleVarDemo()
    ImGui.Text("只读控制台变量")
    ImGui.InspectConsoleVar("t.MaxFPS","最大FPS (t.MaxFPS)")
    ImGui.Separator()
    ImGui.Text("可编辑控制台变量")
    ImGui.EditConsoleVar("t.MaxFPS","最大FPS (t.MaxFPS)")
    ImGui.EditConsoleVarCombo("t.MaxFPS","最大FPS (t.MaxFPS)##EditConsoleVarCombo",{0.0,30.0,60.0,90.0,120.0})
end

function LuaDemoPanel:ObjectDemo()
    local localPlayer = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if self.propertySearchBuf == nil then
        self.propertySearchBuf = ImGuiTextBuf(512)
    end
    if localPlayer ~= nil then
        ImGui.Text("查看PlayerController全部反射属性")
        if ImGui.TreeNode("点我展开") then 
            ImGui.InspectUObject(localPlayer, self.propertySearchBuf, true)
            ImGui.TreePop()
        end
        
        ImGui.NewLine()
        ImGui.Text("查看PlayerController指定反射属性")
        if ImGui.TreeNode("点我展开##InspectUObjectFor") then 
            ImGui.InspectUObjectFor(localPlayer, false, "InputYawScale","InputPitchScale","InputRollScale")
            ImGui.TreePop()
        end
    else
        ImGui.Text("Player Controller不存在")
    end
end

function LuaDemoPanel:ColorEditDemo()
    local changed = false
    if self.colorR == nil then
        self.colorR = 0
        self.colorG = 1
        self.colorB = 1
        self.colorA = 1
    end
    changed,
    self.colorR,
    self.colorG,
    self.colorB = ImGui.ColorEdit3("ColorEdit3",
                                self.colorR,
                                self.colorG,
                                self.colorB)

    changed,
    self.colorR,
    self.colorG,
    self.colorB,
    self.colorA = ImGui.ColorEdit4("ColorEdit4",
                                self.colorR,
                                self.colorG,
                                self.colorB,
                                self.colorA)

    changed,
    self.colorR,
    self.colorG,
    self.colorB = ImGui.ColorPicker3("ColorPicker3",
                                self.colorR,
                                self.colorG,
                                self.colorB)

    changed,
    self.colorR,
    self.colorG,
    self.colorB,
    self.colorA = ImGui.ColorPicker4("ColorPicker4",
                                self.colorR,
                                self.colorG,
                                self.colorB,
                                self.colorA)

end

function LuaDemoPanel:LayoutDemo()
    ImGui.Text("同一行布局，下面的按钮都在一行")

    ImGui.SmallButton("button1")
    ImGui.SameLine()
    ImGui.SmallButton("button2")
    ImGui.SameLine()
    ImGui.SmallButton("button3")

    ImGui.Text("下面的按钮固定间隔")

    ImGui.SmallButton("button4")
    ImGui.SameLine(100)
    ImGui.SmallButton("button5")
    ImGui.SameLine(200)
    ImGui.SmallButton("button6")

    ImGui.Separator() --- 显示一个分隔线


    ImGui.Text("下面的文本有缩进")
    ImGui.Indent(50)  --- 缩进50个像素
    ImGui.Text("Hello World")
    ImGui.Text("你好世界")
    ImGui.Unindent(50)  --- 恢复缩进

    ImGui.Separator()
    ImGui.Text("风格化控制示例")

    ImGui.PushStyleVar(ImGui.ImGuiStyleVar_Alpha, 0.2)  --- 设置所有控件的透明度为0.5，风格化设置的每个设置都有一个栈，绘制控件时使用栈顶的设置
    ImGui.SmallButton("透明度为0.5的按钮")
    ImGui.SameLine()
    ImGui.Text("透明度为0.5的文本")
    ImGui.PopStyleVar() --- 弹出（取消）刚才的设置

    ImGui.PushStyleColor(ImGui.ImGuiCol_Text, ImVec4(1,0,0,1)) --- 设置所有文本的颜色为红色
    ImGui.SmallButton("红色按钮")
    ImGui.SameLine()
    ImGui.Text("红色文本")
    ImGui.PopStyleColor()

    ImGui.PushStyleColor(ImGui.ImGuiCol_Text, ImVec4(0,1,0,1)) --- 设置所有文本的颜色为绿色
    ImGui.SmallButton("绿色按钮")
    ImGui.SameLine()
    ImGui.Text("绿色文本")
    ImGui.PopStyleColor()

    ImGui.PushStyleColor(ImGui.ImGuiCol_Text, ImVec4(0,0,1,1)) --- 设置所有文本的颜色为蓝色
    ImGui.SmallButton("蓝色按钮")
    ImGui.SameLine()
    ImGui.Text("蓝色文本")
    ImGui.PopStyleColor()

    local _, fractional = math.modf(TimeUtil.GetCurrentTimeMillis()/2.0)
    local color = ImVec4(fractional,1-fractional,0,1)
    ImGui.PushStyleColor(ImGui.ImGuiCol_Text, color) --- 设置动态的颜色
    ImGui.SmallButton("变色按钮")
    ImGui.SameLine()
    ImGui.Text("变色文本")
    ImGui.PopStyleColor()
end


return LuaDemoPanel