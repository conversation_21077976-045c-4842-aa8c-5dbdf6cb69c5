----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------


local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"


---@class RoleInfoTitleItem_Big : LuaUIBaseView
local RoleInfoTitleItem_Big = ui("RoleInfoTitleItem_Big")


function RoleInfoTitleItem_Big:Ctor()
    self._wtSelectBtn = self:Wnd("wtSeclectBtn", UIWidgetBase)
    self._wtSelectBtn:Event("OnClicked", self._OnSelectBtnClicked, self)

    self._wtRoot = self:Wnd("wtRoot", UIWidgetBase)

    self._wtTitleImage = self:Wnd("wtTitleBg", UIImage)
    self._wtTitleIcon = self:Wnd("wtTitleIcon", UIImage)
    self._wtTitleName = self:Wnd("DFTextBlock_194", UITextBlock)
    self._rankingRichText = self:Wnd("wtTitleIconRichText", UIRichTextBlock)

    self._wtDownTimeBox = self:Wnd("DFHorizontalBox_DownTime", UIWidgetBase)
    self._wtText_DownTime = self:Wnd("wtText_DownTime", UITextBlock)

    self._selectId = nil
    self._lockIns = nil
    self._selectIns = nil
    self._usingIns = nil
end

function RoleInfoTitleItem_Big:OnOpen()
end

function RoleInfoTitleItem_Big:OnInitExtraData()
end

---@param info SocialInfoMisc
function RoleInfoTitleItem_Big:OnInitAvatarData(info)
    self._info = info
    self._bShowLock = false

    local now = Facade.ClockManager:GetLocalTimestamp()
    local bUnlocked = info.Islock;
    local bHasExpireDate = (info.SocialType ~= LimitSocialTypeDef.NONE) and
        (info.StartTime < now and info.EndTime > now
        )

    -- if bUnlocked and not bHasExpireDate then
    --     logerror("title unlocked but reamin time is invalid")
    -- end

    -- text and icon
    local titleName = RoleInfoLogic.TranslateTitleName(info.AvatarName, info.AdCode, info.RankNo)
    self._wtTitleName:SetText(titleName)
    self:_SetImageFromInfo(info)

    -- ranking
    if info.RankNo and info.RankNo ~= 0 then
        self._rankingRichText:HitTestInvisible()
        self._rankingRichText:SetText(string.format(Module.RoleInfo.Config.TitleRankingTextListItem, info.RankNo))
    else
        self._rankingRichText:Collapsed()
    end

    local reddotHashTbl = Server.RoleInfoServer:GetSocialReddotHash()
    if reddotHashTbl and reddotHashTbl[info.AvatarID] then
        self:OnLockItem()
        self:PlayLociAnim()
    else
        if bUnlocked then
            self:RemoveLockItem()
        else
            self:OnLockItem()
        end
    end

    if bUnlocked and bHasExpireDate then
        self._wtDownTimeBox:SelfHitTestInvisible()
        local gap = info.EndTime - now
        if gap > 60 * 60 * 24 then
            self._wtText_DownTime:SetText(string.format(Module.RoleInfo.Config.Loc.SocialLeftDay,
                math.floor(gap / (60 * 60 * 24))))
        elseif gap > 60 * 60 then
            self._wtText_DownTime:SetText(string.format(Module.RoleInfo.Config.Loc.SocialLeftHour,
                math.floor(gap / (60 * 60))))
        else
            self._wtText_DownTime:SetText(Module.RoleInfo.Config.Loc.SocialLeftOneHour)
        end
    else
        self._wtDownTimeBox:Collapsed()
    end
end

function RoleInfoTitleItem_Big:GetInfo()
    return self._info
end

function RoleInfoTitleItem_Big:_SetImageFromInfo(info)
    self._wtTitleIcon:AsyncSetImagePath(info.ResourceName, true)
    self._wtTitleImage:AsyncSetImagePath(info.OptionalResourceNames[1], true)
    -- self._wtTitleBg:HitTestInvisible()
end

function RoleInfoTitleItem_Big:SetParentAndIndex(parent, index)
    ---@type SocialChangeTitle
    self._parent = parent
    self._index = index
end

function RoleInfoTitleItem_Big:GetIndex()
    return self._index
end

function RoleInfoTitleItem_Big:_OnSelectBtnClicked()
    Module.RoleInfo.Config.Event.evtOnTitleItemBigSelectedBtnClicked:Invoke(self, self._index)
end

--region switch select,using,lock widget

-- select
function RoleInfoTitleItem_Big:OnSelectAvatar()
    if self._selectId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.SelectedComponent)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:SelfHitTestInvisible()
            return
        end
    end

    -- fallback
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.SelectedComponent, self._wtRoot)
    self._selectId = instanceId
    local newCell = getfromweak(weakUIIns)
    if newCell then
        UIUtil.SetWidgetToParent_Full(newCell, self._wtRoot)
        newCell:SelfHitTestInvisible()
    end
end

function RoleInfoTitleItem_Big:RemoveSelectUI()
    if self._selectId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.SelectedComponent, self._selectId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:Collapsed()
        end
    end
end

-- using
function RoleInfoTitleItem_Big:OnUsingAvatar()
    if self._usingId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.UsingComponent, self._usingId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:SelfHitTestInvisible()
            return
        end
    end

    -- fallback
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.UsingComponent, self._wtRoot)
    self._usingId = instanceId
    local newCell = getfromweak(weakUIIns)
    if newCell then
        UIUtil.SetWidgetToParent_Full(newCell, self._wtRoot)
        newCell:SelfHitTestInvisible()
    end
end

function RoleInfoTitleItem_Big:RemoveUsingUI()
    if self._usingId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.UsingComponent, self._usingId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:Collapsed()
        end
    end
end

function RoleInfoTitleItem_Big:PlayLociAnim()
    if self._lockId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.LockComponent, self._lockId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            local ani = newCell.WBP_SlotCompMaskSmallLock_Unlock
            newCell:StopAnimation(ani)
            newCell:PlayAnimation(ani, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
    end
end

function RoleInfoTitleItem_Big:OnLockItem(bPlayAnimation)
    if self._lockId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.LockComponent, self._lockId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:SelfHitTestInvisible()
            return
        end
    end

    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.LockComponent, self._wtRoot)
    self._lockId = instanceId
    local newCell = getfromweak(weakUIIns)
    if newCell then
        UIUtil.SetWidgetToParent_Full(newCell, self._wtRoot)
        newCell:SelfHitTestInvisible()
    end
end

function RoleInfoTitleItem_Big:RemoveLockItem()
    if self._lockId then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.LockComponent, self._lockId)
        self._lockId = nil
    end
    self._bShowLock = false
end

--endregion

return RoleInfoTitleItem_Big
