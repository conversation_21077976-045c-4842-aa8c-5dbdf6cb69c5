------------------------------------
-- Create By Tan<PERSON>iaoliang@VIRTUOS
------------------------------------

----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDInputPanel_Gamepad
local SystemSettingHDInputPanel_Gamepad = ui("SystemSettingHDInputPanel_Gamepad")
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local KeyRebindingTips_Gamepad = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.KeyRebindingTips_Gamepad"
local UDFMGameplayInputManager = import("DFMGameplayInputManager")
local EConsumeMouseWheel = import "EConsumeMouseWheel"

local Config = require "DFM.Business.Module.SystemSettingModule.SystemSettingConfig"
local ESystemSettingHDPanel = Config.ESystemSettingHDPanel
local ESystemSettingHDInputPanel_Gamepad = Config.ESystemSettingHDInputPanel_Gamepad

-- UI Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

local GameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())


function SystemSettingHDInputPanel_Gamepad:Ctor()
    loginfo("[george]<InputPanel_Gamepad> Ctor()")
    self:_BindWidget()

    -- 接框架设置Bar按键提示
    self.BottomBarState = {
        Common = 1,
        Hovering = 2,
        Selecting = 3,
        Quit = 4,
    }

    self._SubUIIDlist = {}
    self._NavGroups = {}
end

function SystemSettingHDInputPanel_Gamepad:_BindWidget()
    self._wtScrollBox = self:Wnd("InputPanelScrollBox", UIWidgetBase)
    self._wtFunctionGroupList = self:Wnd("FunctionGroupList", UIWidgetBase)
    self._wtFunctionKeyPoolList = self:Wnd("FunctionKeyPoolList", UIWidgetBase)
    self._wtTipsBar = self:Wnd("_wtKeyRebindingTips", KeyRebindingTips_Gamepad)
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
end

function SystemSettingHDInputPanel_Gamepad:OnOpen()
    -- loginfo("[george]<InputPanel_Gamepad> OnOpen()")
    GameplayInputManager.NotifyKeyRebindingApplied:Add(self._OnKeyRebindingApplied, self)
    self.NotifyKeySelectingState:Bind(self._HandleKeySelectingState, self)
end

function SystemSettingHDInputPanel_Gamepad:OnShow()
    -- loginfo("[george]<InputPanel_Gamepad> OnShow()")
    self._wtTipsBar:Hide(true, true)
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.InputSetting_Gamepad then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
        self:SwitchBottomBarState(self.BottomBarState.Common)
    end
end

function SystemSettingHDInputPanel_Gamepad:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function SystemSettingHDInputPanel_Gamepad:OnHide()
    -- loginfo("[george]<InputPanel_Gamepad> OnHide()")
    self:SwitchBottomBarState(self.BottomBarState.Quit)
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)

    self:SaveAppliedKeyRebindings()
    self:_RemoveNavGroup()
    self._SubUIIDlist = {}
    Facade.UIManager:RemoveSubUIByParent(self.SettingMainView, self._wtFunctionGroupList)
end

function SystemSettingHDInputPanel_Gamepad:OnClose()
    -- loginfo("[george]<InputPanel> OnClose()")
    GameplayInputManager.NotifyKeyRebindingApplied:Remove(self._OnKeyRebindingApplied, self)
    self:SaveAppliedKeyRebindings()
    self:_RemoveNavGroup()
    self._SubUIIDlist = {}
    Facade.UIManager:RemoveSubUIByParent(self.SettingMainView, self._wtFunctionGroupList)
end

function SystemSettingHDInputPanel_Gamepad:InitFunctionGroupList()
    loginfo("[george]<InputPanel_Gamepad> InitFunctionGroupList()")
    -- 注意：清理数据统一放入OnHide中
    self.KeyFunctionTypeDataTable = setdefault(self.KeyFunctionTypeDataTable, Facade.TableManager:GetTable("KeyMappings/Gamepad/KeyFunctionType_Gamepad"))
    -- 一个Tab子页面，拥有多个Group
    -- 一个Group对应一个KeyFunctionType值，并显示KeyFunctionType对应的所有按键输入配置
    for i = 1, #self.__cppinst.KeyFunctionTypeListInThisPanel do
        for k, v in pairs(self.KeyFunctionTypeDataTable) do
            -- loginfo("[george]<InputPanel> InitFunctionGroupList(), "..self.__cppinst.KeyFunctionTypeListInThisPanel[i].." "..v.keyFunctionType)
            if (not v.bHide) and (self.__cppinst.KeyFunctionTypeListInThisPanel[i] == v.keyFunctionType) then
                local functionType = v.keyFunctionType
                local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self.SettingMainView, UIName2ID.InputKeyFunctionGroup_Gamepad, self._wtFunctionGroupList)
                local uiIns = getfromweak(weakUIIns)
                if uiIns then
                    uiIns:InitFunctionGroup(functionType, i, self)
                end
                self._SubUIIDlist[functionType] = instanceID
                break
            end
        end
    end

    -- 界面刷新，重新初始化导航
    self:_RemoveNavGroup()
    self:_RegisterNavGroup()
end

-- 刷新Group的显示
function SystemSettingHDInputPanel_Gamepad:UpdateFunctionGroupList()
    for functionType, groupInsID in pairs(self._SubUIIDlist) do
        local weakUIIns, instanceID = Facade.UIManager:GetSubUI(self.SettingMainView, UIName2ID.InputKeyFunctionGroup_Gamepad, groupInsID)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            uiIns:UpdateFunctionGroup()
        end
    end
end

function SystemSettingHDInputPanel_Gamepad:_OnClickAnyKeyButton(functionKeyItem, keyBtn)
    if self.__cppinst.bJustPressedCursurMouseButtonInSelectingKey then
        return false
    end

    self.SelectingRowKeyBtn = keyBtn
    self:CancelKeySelecting()
    self:StartKeySelecting(functionKeyItem._inputTrigger)
    self._wtScrollBox:SetConsumeMouseWheel(EConsumeMouseWheel.Never)

    -- Update BottomBar Btn
    self:SwitchBottomBarState(self.BottomBarState.Selecting)
    self._wtTipsBar:Hide(true, true)

    -- 当正在进行按键重绑定时，禁用导航按键 DPad+A
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoNav_KeyRebinding, self)

    return true
end

function SystemSettingHDInputPanel_Gamepad:_HandleKeySelectingState(state)
    self._wtScrollBox:SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)
    -- Update BottomBar Btn
    self:SwitchBottomBarState(self.BottomBarState.Common)
    -- 刷新显示
    self:UpdateFunctionGroupList()
    -- 恢复导航
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function SystemSettingHDInputPanel_Gamepad:_OnHoverAnyKeyButton(functionKeyItem, keyBtn)
    if self:IsSelectingKey() then
        return
    end

    self:SetCppValue("bIsHoveringKey", true)
    self:SetCppValue("HoveringInputTrigger", functionKeyItem._inputTrigger)

    self.HoveringRowFunctionKeyItem = functionKeyItem
    self.HoveringRowKeyBtn = keyBtn

    -- Update BottomBar Btn
    if self.HoveringRowKeyBtn.KeyName ~= "None" then
        self:SwitchBottomBarState(self.BottomBarState.Hovering)
    end
end

function SystemSettingHDInputPanel_Gamepad:_OnUnhoverAnyKeyButton(functionKeyItem, keyBtn)
    if self:IsSelectingKey() then
        return
    end

    self:SetCppValue("bIsHoveringKey", false)
    self:SetCppValue("HoveringInputTrigger", nil)

    -- Update BottomBar Btn
    self:SwitchBottomBarState(self.BottomBarState.Common)
end

-- UI提示按键交换
function SystemSettingHDInputPanel_Gamepad:_OnKeyRebindingApplied(RebindInputTriggers, SwapInputTriggers)
    if (RebindInputTriggers:Num() > 0 and SwapInputTriggers:Num() > 0) and self.SelectingInputTrigger then
        local selectingItemFunctionName = GameplayInputManager:GetInputMappingRowDisplayName(self.SelectingInputTrigger:GetDefaultInputMappingData(), false)

        -- 仅显示最后一个元素的提示
        local RebindInputTrigger = RebindInputTriggers:Get(RebindInputTriggers:Num() - 1)
        local SwapInputTrigger = SwapInputTriggers:Get(SwapInputTriggers:Num() - 1)
        local SwapSettingTableRow = SwapInputTrigger:GetDefaultInputMappingData()
        local SwapSettingTableRowFunctionName = GameplayInputManager:GetInputMappingRowDisplayName(SwapSettingTableRow, false)
        local tipsContent = string.format(Module.SystemSetting.Config.Loc.HDSetting.KeyBindChangeTxt, SwapSettingTableRowFunctionName, selectingItemFunctionName)
        self._wtTipsBar:InitTips(self, RebindInputTrigger, tipsContent)
        self._wtTipsBar:Show(false, true)
    end

    -- 刷新显示
    self:UpdateFunctionGroupList()
end

function SystemSettingHDInputPanel_Gamepad:SwitchBottomBarState(state)
    if self.curBottomBarState == state then
        return
    end
    self.curBottomBarState = state

    if state == self.BottomBarState.Common or state == self.BottomBarState.Hovering then
        local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
        local list = {
            {actionName = "Bind", func = nil, caller = self},
            {actionName = "Unbind", func = self._OnUnbindHoveringKey, caller = self},
            {actionName = "Reset", func = self._OnClickResetBtn, caller = self}
        }
        for _, v in ipairs(globalList) do
            table.insert(list, v)
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
    elseif state == self.BottomBarState.Selecting then
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "CancelBind", func = self._OnCancelBind, caller = self}
        }, true, true)
    end

    if state == self.BottomBarState.Quit then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end

function SystemSettingHDInputPanel_Gamepad:_OnUnbindHoveringKey()
    self.__cppinst:UnbindHoveringKey()

    self._wtTipsBar:Hide(true, true)
end

function SystemSettingHDInputPanel_Gamepad:_OnClickResetBtn()
    local fReset = function()
        self._wtTipsBar:Hide(true, true)
        self:ResetKeyRebindings()
        self:UpdateFunctionGroupList()
    end

    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    self.confirmWindowHandle = Module.CommonTips:ShowConfirmWindow(self.resetContentTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

function SystemSettingHDInputPanel_Gamepad:_OnCancelBind()
    self:CancelKeySelecting()
end

function SystemSettingHDInputPanel_Gamepad:_NotifySubTabType(inSettingMainView, inType)
    -- 切换Tab时，保存刚刚应用的存档
    self:SaveAppliedKeyRebindings()

    self.SettingMainView = inSettingMainView
    if inType == ESystemSettingHDInputPanel_Gamepad.CCC then
        -- 3C
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInput3CTxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {1, 2}
    elseif inType == ESystemSettingHDInputPanel_Gamepad.Aiming then
        -- Aiming
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInputAimingTxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {3}
    elseif inType == ESystemSettingHDInputPanel_Gamepad.Vehicle then
        -- Vehicle
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInputVehicleTxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {4, 7}
    end

    self:InitFunctionGroupList()
    -- 如果导航被重绑定禁用，那么需要恢复
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

-- UI导航
function SystemSettingHDInputPanel_Gamepad:_RegisterNavGroup()
    local defaultFocusGroup = nil
    -- 滑动条导航
    local wtScrollBox = self:Wnd("InputPanelScrollBox", UIWidgetBase)
    if wtScrollBox then
        local navGroup = WidgetUtil.RegisterNavigationGroup(wtScrollBox, self, "Hittest")
        if navGroup then
            navGroup:AddNavWidgetToArray(wtScrollBox)
            navGroup:SetScrollRecipient(wtScrollBox)
            table.insert(self._NavGroups, navGroup)
            defaultFocusGroup = navGroup
        end
    end

    -- 为每个FunctionGroup创建NavGroup，然后默认Focus第一个FunctionGroup
    for functionType, groupInsID in pairs(self._SubUIIDlist) do
        local navGroup = nil
        local weakUIIns, instanceID = Facade.UIManager:GetSubUI(self.SettingMainView, UIName2ID.InputKeyFunctionGroup_Gamepad, groupInsID)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            navGroup = WidgetUtil.RegisterNavigationGroup(uiIns, self, "Hittest")
        end

        if navGroup then
            navGroup:AddNavWidgetToArray(uiIns)
            table.insert(self._NavGroups, navGroup)
            if functionType == 1 or functionType == 4 then
                defaultFocusGroup = navGroup
            end
        end
    end

    if defaultFocusGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(defaultFocusGroup)
    end
end

function SystemSettingHDInputPanel_Gamepad:_RemoveNavGroup()
    self._NavGroups = {}
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    WidgetUtil.RemoveNavigationGroup(self)
end

return SystemSettingHDInputPanel_Gamepad
