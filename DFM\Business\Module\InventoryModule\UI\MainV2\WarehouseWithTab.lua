----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local Warehouse = require "DFM.Business.Module.InventoryModule.UI.MainV2.Warehouse"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local WarehouseExtSubPanel = require "DFM.Business.Module.InventoryModule.UI.Extension.WarehouseExtSubPanel"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ExtReplaceConfirmWindowItem = require "DFM.Business.Module.InventoryModule.UI.Extension.ExtReplaceConfirmWindowItem"
local ButtonIdConfig = require("DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig")
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local EDescendantScrollDestination = import "EDescendantScrollDestination"

---@class WarehouseWithTab : LuaUIBaseView
local WarehouseWithTab = ui("WarehouseWithTab")

WarehouseWithTab.FILTER_TAB_FOLD_THRESHOLD = 80

local function log(...)
    loginfo("[WarehouseWithTab]", ...)
end

local MAX_SAME_ITEM_CLICK_NUM = 2
local MIN_SAME_ITEM_IN_DEPOSIT = 3
local SELECT_ALL_PANEL_SHOW_SECOND = 4
WarehouseWithTab.SUB_CELL_DRAG_DELAY = 0.1


WarehouseWithTab.ARRANGE_BTN_ON_IMG = "PaperSprite'/Game/UI/UIAtlas/System/Warehouse/BakedSprite/Warehouse_Btn_08.Warehouse_Btn_08'"
WarehouseWithTab.ARRANGE_BTN_OFF_IMG = "PaperSprite'/Game/UI/UIAtlas/System/Warehouse/BakedSprite/Warehouse_Btn_01.Warehouse_Btn_01'"
WarehouseWithTab.LAST_SELECT_INDEXS_KEY = "WAREHOUSE_LAST_SELECT_TABS_TO_SORT"
WarehouseWithTab.LAST_SELECT_INDEXS_KEY_NOT_FIRST = "LAST_SELECT_INDEXS_KEY_NOT_FIRST"

function WarehouseWithTab:Ctor()
    self._bInExtArrangeMode = false
    self._bInExtManagementMode = false
    self._bIsCanInExtManagementMode = true
    self._bShowSellEntrance = true
    self._bShowExtManageExtrance = true
    self._curFilterMode = eDepositSortClass.none
    self._mapCurrency2SellPrice = {}
    self._sameItemNum = {} -- id : num
    ---@type ItemBase[]
    self._otherSameItems = {}
    ---@type WarehouseTopHint
    self._topHint = nil
    self._mapSortClassId2Pos = {}
    ---@type WarehouseFilterTab[]
    self._filterTabs = {}
    self._mapSortClassId2Pos = {}
    self._bFilterBtnToggle = false
    self._bExtManageBtnToggle = false
    self._delayScrollToItemHandle = nil
    self._filterAnimHandle = nil
    self._depositNum = {}
    self._curDepositIndex = nil
    self._isNomoreTip = false
    self._cacheDepositTabChildren = nil
    self._bInReplaceExtMode = false

    -----------------------------------------------------------------------
    --region Main panel
    self._wtMainCanvas = self:Wnd("wtMainCanvas", UIWidgetBase)
    self._wtWarehouse = self:Wnd("wtWarehouse", Warehouse)
    self._wtMaskPanel = self:Wnd("wtMaskPanel", UIWidgetBase)
    --endregion
    -----------------------------------------------------------------------

    -----------------------------------------------------------------------
    --region Tab panel
    self._wtFilterPanel = self:Wnd("wtFilterPanel", UIWidgetBase)
    self._wtExtPanel = self:Wnd("wtExtPanel", UIWidgetBase)
    self._wtDepositTabScrollBox = self:Wnd("wtDepositTabScrollBox", UIWidgetBase)
    self._wtExtVerticalBox = self:Wnd("wtExtVerticalBox", UIWidgetBase)

    -- 常驻顶部的主仓库图标
    self._wtMainDepositTab = self:Wnd("ExpansionButton_Freeze", UIWidgetBase)
    -- self._wtFilterScrollBox = self:Wnd("wtFilterScrollBox", UIWidgetBase)
    -- self._wtFilterScrollBox:Event("OnUserScrolled", self._OnFilterScrolled, self)

    --[[
    self._wtFilterBtnBp = self:Wnd("wtFilterBtnBp", UIWidgetBase)
    self._wtFilterBtnBp:Collapsed()
    self._wtFilterBtn = self._wtFilterBtnBp:Wnd("wtFilterBtn", UIButton)
    self._wtFilterBtn:Event("OnClicked", self._OnFilterBtnClicked, self)
    ]]--

    self._wtSubBtnsPanel = self:Wnd("wtSubBtnsPanel", UIWidgetBase)
    self._wtExtArrangeBtn = self:Wnd("wtExtArrangeBtn", DFCommonButtonOnly)
    -- self._wtExtArrangeBtn:Event("OnCheckedBoxStateChangedNative", self._OnExtArrangeBtnPress, self)
    self._wtExtArrangeBtn:Event("OnClicked", self._OnExtArrangeBtnPress, self)
    self._wtSellBtn = self:Wnd("wtSellBtn", DFCommonButtonOnly)
    -- self._wtSellBtn:Event("OnCheckedBoxStateChangedNative", self._OnSellBtnClicked, self)
    self._wtSellBtn:Event("OnClicked", self._OnSellBtnClicked, self)

    --endregion
    -----------------------------------------------------------------------

    -----------------------------------------------------------------------
    --region Other panel

    -- 出售按钮点击弹出ui(需要拆出)
    self._sellSubPanel = nil
    self._slotSellSubSlot = self:Wnd("Slot_Warehouse_SellSub", UIWidgetBase)
    self._slotSellSubSlot:Collapsed()

    self._wtExtManageBtn = self:Wnd("wtExtManageBtn", UIButton)
    self._wtExtManageBtn:Event("OnClicked", self._OnExtManageBtnClick, self)
    self._wtExtLvlUpHint = self:Wnd("wtExtLvlUpHint", UIWidgetBase)

    -- 整理按钮点击弹出ui(需要拆出)
    self._extArrangePanel = nil
    self._slotExtArrange = self:Wnd("Slot_Warehouse_ExtArrange", UIWidgetBase)
    self._slotExtArrange:Collapsed()

    self._wtMaskImg = self:Wnd("wtMaskImg", UIImage)
    self._wtMaskImg:Event("OnMouseButtonDownEvent", self._OnMaskImgClicked, self)

    -- 愿望单按钮改为收藏室按钮
    self._wtWishListBtn = self:Wnd("wtWishListBtn", DFCommonButtonOnly)
    self._wtWishListBtn:Event("OnClicked", self._OnCollectionHallBtnClicked, self)
    self._wtWishListBtn:Hidden()

    self._wtExtManagePanel = self:Wnd("wtExtManagePanel", UIWidgetBase)
    self.wtDFImage = self:Wnd("wtBgImage", UIImage)
    --endregion
    -----------------------------------------------------------------------
end

--==================================================
--region Life function

function WarehouseWithTab:OnOpen()
    log("OnOpen")
    -- self:_CheckExtLvlUpHintShouldShow()
end

function WarehouseWithTab:OnClose()
    Timer.CancelDelay(self._filterAnimHandle)
    self._filterAnimHandle = nil
    Timer.CancelDelay(self._delayScrollToItemHandle)
    self._delayScrollToItemHandle = nil
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonUpEvent:Remove(self._OnGlobalTouchUp, self)
end

function WarehouseWithTab:OnShowBegin()
    self:_OnCollectionHallBtnVisible()
end

function WarehouseWithTab:OnShow()
    logerror("WarehouseWithTab:OnShow")

    self:_InitDepositTabList()

    -- 防止极限操作卡掉subpanel，onshow内部直接显示
    self._wtSubBtnsPanel:SelfHitTestInvisible()

    -- self:_OnFilterBtnClicked(false)

    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMoveBatch, self._OnItemMoveBatch, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostSortMultiPos, self._OnPostSortMultiPos, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtNotifyExtOrderChanged, self._OnNotifyExtOrderChanged, self)
    self:AddLuaEvent(Module.CommonWidget.Config.Events.evtPostItemMultiSelected, self._OnPostItemMultiSelected, self)
    -- self:AddLuaEvent(InventoryConfig.Events.evtExtManageDragSortStart, self._OnExtManageDragSortStart, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtInventoryFetchFinished, self._OnCancelSellBtnClick, self)

    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragStart, self._OnGlobalDragStart, self)
    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDrop, self._OnGlobalDragStop, self)
    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragCancelled, self._OnGlobalDragStop, self)
    self:AddLuaEvent(InventoryConfig.Events.evtJumpToExtSlot, self._SelectDepositTab, self)
    self:AddLuaEvent(InventoryConfig.Events.evtSaveExpansionNum, self._SaveExpansionNum, self)
    self:AddLuaEvent(InventoryConfig.Events.evtCompareExpansionNum, self._CompareExpansionNum, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtArrangeFaild, self._WarehouseArrangeFaild, self)
    self:AddLuaEvent(InventoryConfig.Events.evtPopWindowStageChange, self.OnPopWindowStageChange, self)
    self:AddLuaEvent(Server.BlackSiteServer.Events.evtBlackSiteUpgradeSuccess2Warehouse, self._BlackSiteUpgradeSuccess, self)
    self:AddLuaEvent(InventoryConfig.Events.evtSaleCompleted, self._CancelSell, self)
    self:AddLuaEvent(InventoryConfig.Events.evtEnterReplaceExtPanel, self._SetExtReplaceMode, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtExtensionBoxRes, self._OnExtensionBoxRes, self)

    if Server.InventoryServer:GetSortEveryEnter() then
        self:AutoArrangeWarehouse()
    end
    if DFHD_LUA == 1 then
    else
        self:AddLuaEvent(InventoryConfig.Events.evtAutoScrollVisible, self._AutoScrollVisible, self)
    end

    -- self:_OnExtManageDragSortStart(false)
    self:SetLonger(false)
    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonUpEvent:Add(self._OnGlobalTouchUp, self)
end

function WarehouseWithTab:OnHide()
    logerror("WarehouseWithTab:OnHide")

    self:_CancelSell()
    self:_CancelExtArrange()
    self:_EndExtManagement()
    self:_HideTopHint()
    self:_EndExtReplace()

    self:RemoveAllLuaEvent()

    UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonUpEvent:Remove(self._OnGlobalTouchUp, self)
end

function WarehouseWithTab:OnInitExtraData()
end

function WarehouseWithTab:_OnGlobalDragStart()
    self._wtSubBtnsPanel:Collapsed()
end

function WarehouseWithTab:_OnGlobalDragStop()
    if not self._bInExtManagementMode and Module.CommonWidget:GetItemOperMode() ~= EItemOperaMode.SellMode then
        self._wtSubBtnsPanel:SelfHitTestInvisible()
    end
end

function WarehouseWithTab:_AutoScrollVisible(bDirection, bVisible)
    if ItemOperaTool.bInSettlement then
        return
    end
    if not bDirection then
        if bVisible then
            self._wtSubBtnsPanel:SelfHitTestInvisible()
        else
            self._wtSubBtnsPanel:Collapsed()
        end
    end
end

function WarehouseWithTab:_OnGlobalTouchUp()
    if self._bShouldFoldFilterTab then
        self._bShouldFoldFilterTab = false

        -- self._wtFilterBtn:SetIsChecked(false, true)
        -- self:_OnFilterBtnClicked(false)
    end
end
--endregion
--==================================================

--==================================================
--region Public API

function WarehouseWithTab:PlayInAnim()
    -- self:PlayWidgetAnim(self.In_Anim)
    -- self._wtWarehouse:PlayInAnim()
end

function WarehouseWithTab:GetCurrentShowDepositId()
    return self._wtWarehouse:GetCurrentShowDepositId()
end

function WarehouseWithTab:GetWarehouseWidget()
    return self._wtWarehouse
end

function WarehouseWithTab:ShowFilterBtn(bShow)
    if bShow then
        self._wtFilterBtn:Visible()
    else
        self._wtFilterBtn:Collapsed()
    end
end
function WarehouseWithTab:ShowSellExtrance(bShow)
    self._bShowSellEntrance = bShow

    self:_UpdateSellBtnVisibility(bShow)
end

function WarehouseWithTab:ShowExtManageExtrance(bShow)
    self._bShowExtManageExtrance = bShow

    if bShow then
        self._wtExtManageBtn:Visible()
    else
        self._wtExtManageBtn:Collapsed()
    end
end

function WarehouseWithTab:ShowExtArrangeBtn(bShow)
    if bShow and self._wtWarehouse:IsInGridMode() then
        self._wtExtArrangeBtn:Visible()
    else
        self._wtExtArrangeBtn:Collapsed()
    end
end

function WarehouseWithTab:ShowCollectionInBtn(bShow)
    if bShow then
        self._wtCollectionInBtn:Visible()
    else
        self._wtCollectionInBtn:Collapsed()
    end
end

function WarehouseWithTab:ShowWishListBtn(bShow)
    if bShow then
        self._wtWishListBtn:Visible()
    else
        self._wtWishListBtn:Collapsed()
    end
end

function WarehouseWithTab:_UpdateSellBtnVisibility(bShow)
    if bShow and self._bShowSellEntrance and self._wtWarehouse:IsInGridMode() then
        self._wtSellBtn:Visible()
    else
        self._wtSellBtn:Collapsed()
    end
end

function WarehouseWithTab:SetShowCurNumDeposit(bShowCurNum)
    self._bShowCurNumDeposit = bShowCurNum
    self:_InitDepositTabList()
end

function WarehouseWithTab:SetExtManagerCellInfo(extSubPanel, bShow, index)
    extSubPanel:BindCellSelectionChangedCallback(self._OnExtCellSelectionChanged, self)
    extSubPanel:BindEndManageCallback(self._OnEndManage, self)
    extSubPanel:MarkAsOpen(bShow)
    if bShow then
        extSubPanel:InitWarehouseExtList(index, false)
    end
end

function WarehouseWithTab:BackToWarehouse()
    local extSubPanelParam = Module.Inventory.Field:GetIsInExtSubPanel()
    if extSubPanelParam.bInExtSubPanel then
        self:_StartExtManagement()
        Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, extSubPanelParam.index)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, extSubPanelParam.index)
    end
end

function WarehouseWithTab:_BlackSiteUpgradeSuccess()
    self:_InitDepositTabList()
end

-- 收藏室界面的显隐
function WarehouseWithTab:SetCollectionBtnPanel(bInCollectionRoom)
    if bInCollectionRoom then
        self._wtWishListBtn:Collapsed()
        self._wtExtManageBtn:Collapsed()
        self._wtSellBtn:Collapsed()
    end
end

function WarehouseWithTab:SetSettlementBtnPanel(bInSettlement)
    if bInSettlement then
        self._wtWishListBtn:Collapsed()
        self._wtExtManageBtn:Collapsed()
        self._wtSellBtn:Collapsed()
        self._wtExtArrangeBtn:Collapsed()
    end
end

-- 整理按钮面板显隐
function WarehouseWithTab:ShowExtArrangePanel(bShow)
    loginfo("WarehouseWithTab:ShowExtArrangePanel")
    self._wtSubBtnsPanel:SetVisibility(bShow and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
end

--endregion
--==================================================

--==================================================
--region Private API

function WarehouseWithTab:_ResetDepositTabScrollBox()
    -- 用lua table加速查询
    if not self._cacheDepositTabChildren then
        self._cacheDepositTabChildren = {}
        -- local childWidgets = self._wtDepositTabScrollBox:GetAllChildren()
        local childWidgets = self._wtExtVerticalBox:GetAllChildren()
        if childWidgets and #childWidgets ~= 0 then
            for i, childWidget in pairs(childWidgets) do
                self._cacheDepositTabChildren[i+1] = childWidget
            end
        end
    end

    -- 隐藏子节点
    for i, childWidget in pairs(self._cacheDepositTabChildren) do
        -- childWidget:Hide(nil, true)
        childWidget:Collapsed()
    end

    --self._wtDepositTabScrollBox:ClearChildren()
end

-- 调用必须连续
function WarehouseWithTab:_GetOrCreateDepositTabItem(idx)
    -- 走节点缓存
    if idx <= #self._cacheDepositTabChildren then
        local childWidget = self._cacheDepositTabChildren[idx]
        -- childWidget:Show(nil, true)
        childWidget:Visible()
        return childWidget
    end

    local childWidget = nil
    if InventoryConfig.bEnableWarehouseDepositTabPool then
        childWidget = Module.Inventory:ObtainWarehouseDepositTabFromPool()
        self._wtExtVerticalBox:AddChild(childWidget)
    else
        local newTabWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseDepositTab, self._wtExtVerticalBox)
        childWidget = getfromweak(newTabWeakIns)
    end

    if childWidget then
        self._cacheDepositTabChildren[idx] = childWidget
    end
    return childWidget
end

function WarehouseWithTab:_InitDepositTabList(targetSelectTabIndex)
    targetSelectTabIndex = setdefault(targetSelectTabIndex, 1)

    self._curDepositIndex = targetSelectTabIndex
    local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
    local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    local lockSlotNum = Server.InventoryServer:GetLockSlotNum()
    local bHaveVipExt = Server.InventoryServer:IsHaveVipExt()

    ---@type WarehouseDepositTab[]
    self._allDepositTabs = {}
    --self._wtDepositTabScrollBox:ClearChildren()
    self:_ResetDepositTabScrollBox()

    local targetShowTabNum = curDepositNum -- Don't show locked
    if self._bShowExtManageExtrance then
        local defaultTagetNum = curDepositNum + availableDepositNum + 1                     -- 仓库常驻锁定图标 + VIP扩容箱槽位
        targetShowTabNum = defaultTagetNum + 1
    end
    targetShowTabNum = math.min(targetShowTabNum, maxDepositNum)

    -- 如果是拍卖行界面就不用显示availableDepositNum
    if self._bShowCurNumDeposit or ItemOperaTool.CheckIsDIYCabinetPanel() then
        targetShowTabNum = curDepositNum
    end

    for i = 1, targetShowTabNum do
        ---@type WarehouseDepositTab 
        local newTab
        if i == 1 then
            newTab = self._wtMainDepositTab
        else
            newTab = self:_GetOrCreateDepositTabItem(i - 1)
        end
        if newTab then
            newTab:InitDepositTab(i)
            newTab:BindBtnCallback(self._OnDepositTabClicked, self)
            newTab:BindDragStayCallback(self._OnDragStayCallback, self)
            newTab:BindLongPressCallback(self._OnTabLongPressedCallback, self)
            newTab:SetCppValue("bPreciseClick", true)
            self._allDepositTabs[i] = newTab
        end
    end

    Module.Guide:RemoveGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyWarehouseExpansion)
    -- 注册一下新手引导中会引导到的扩容槽位对应的ui
    if self._allDepositTabs[curDepositNum + 1] then
        Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget.guideProxyWarehouseExpansion, self._allDepositTabs[curDepositNum + 1])
    end

    if not self._bInExtArrangeMode then
        self._curDepositTabIndex = 0
        self._curMultiTabIndexs = {}

        self:_SelectDepositTab(targetSelectTabIndex)
    else
        -- 可能是从其他设置（例如设置页面）返回的
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            warehouseTab:SetInMultiMode(true)

            if self._curMultiTabIndexs[i] then
                self:_SetMultiSelected(i, true)
            end
        end
    end
end

function WarehouseWithTab:_OnDepositTabClicked(index)
    if self._bInExtArrangeMode then
        self:_OnDepositTabClicked_ExtArrangeMode(index)
    elseif self._bInReplaceExtMode then
        self:_OnDepositTabClicked_ReplaceExtMode(index)
    else
        self:_OnDepositTabClicked_Normal(index)
    end
end

function WarehouseWithTab:_OnDepositTabClicked_Normal(index)
    local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
    local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    local bHaveVipExt = Server.InventoryServer:IsHaveVipExt()
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHTab)

    if index <= 0 or index > maxDepositNum then
        return
    end

    if index <= curDepositNum then
        -- Cur exist, selet tab
        self:_SelectDepositTab(index)
        self._curDepositIndex = index
    elseif index <= curDepositNum + availableDepositNum then
        -- Available, open ext selection window
        Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, index)
    elseif not bHaveVipExt and index == curDepositNum + availableDepositNum + 1 then
        self:_SelectDepositTab(index)
        self._curDepositIndex = index
    else
        -- Lock, show lock tip
        -- Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseExtSlotLock)
        local title = InventoryConfig.Loc.LevelupWarehouseToUnlockMoreExtSlots
        local confirmBtnText = InventoryConfig.Loc.LevelupWarehouseToUnlockMoreExtSlots_ConfirmBtnText
        local function fOnConfirm()
            Module.BlackSite:Jump(1010, true)
        end
        Module.CommonTips:ShowConfirmWindow(title, fOnConfirm, nil, nil, confirmBtnText)
    end
end

function WarehouseWithTab:_OnDepositTabClicked_ExtArrangeMode(index)
    local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
    local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()

    if index <= 0 or index > maxDepositNum then
        return
    end

    if index <= curDepositNum then
        self:_SetMultiSelected(index)
        self:_PostRefreshTabMultiSelected()
    end
end

function WarehouseWithTab:_OnDepositTabClicked_ReplaceExtMode(index)
    if index == 1 then
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.MainExtCantReplace)
        return
    end
	local extSlot = Server.InventoryServer:GetExtSlotByIndex(index)
	if InventoryLogic.StartExtItemReplacementInvalidation(self._extItem2Replace, extSlot, true) then
        InventoryConfig.Events.evtEnterReplaceExtPanel:Invoke(false)
    end
end

function WarehouseWithTab:_SelectDepositTab(index, item)
    if self._curDepositTabIndex == index then
        return
    end

    if self._allDepositTabs[index].onDropState then
        self._allDepositTabs[index]:ChangeDropState()
        return
    end

    self._curDepositTabIndex = index

    for i = 1, #self._allDepositTabs do
        self._allDepositTabs[i]:SetSelected(i == index)
    end

    local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
    if depositId ~= ESlotType.None then
        self._wtWarehouse:ShowInGridMode(depositId, item)
    else
        self._wtWarehouse:ShowInGridMode(ESlotType.VipDepositContainer, item)
    end
end

function WarehouseWithTab:_SetMultiSelected(index, bForceMultiSelected)
    local tab = self._allDepositTabs[index]

    if bForceMultiSelected == nil then
        local bCurMuitlSelected = self._curMultiTabIndexs[index]
        self._curMultiTabIndexs[index] = not bCurMuitlSelected
        tab:SetMultiSelected(not bCurMuitlSelected)
    else
        self._curMultiTabIndexs[index] = bForceMultiSelected
        tab:SetMultiSelected(bForceMultiSelected)
    end
end

function WarehouseWithTab:_PostRefreshTabMultiSelected()
    -- Post update
    local curSelectNum = 0
    for k, v in pairs(self._curMultiTabIndexs) do
        if v == true then
            curSelectNum = curSelectNum + 1
        end
    end

    if self._extArrangePanel then
        self._extArrangePanel:IsSelectedExpansionBox(curSelectNum)
        self._extArrangePanel:SetCheckBoxState(curSelectNum == Server.InventoryServer:GetCurDepositSlotNum())
    end
    -- if curSelectNum > 0 then
    --     self._wtExtArrangeTextSelect:SelfHitTestInvisible()
    --     self._wtExtArrangeTextNonSelect:Collapsed()
    --     self._wtConfirmExtArrangeBtn:Visible()

    --     self._wtExtArrangeTextSelect:SetText(StringUtil.PluralTextFormat(InventoryConfig.Loc.WarehouseExtArrangeSelectNum, {["Num"]=curSelectNum}))
    -- else
    --     self._wtExtArrangeTextSelect:Collapsed()
    --     self._wtExtArrangeTextNonSelect:SelfHitTestInvisible()
    --     self._wtConfirmExtArrangeBtn:Collapsed()
    -- end
end


function WarehouseWithTab:_OnDragStayCallback(index)
    local curDragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
    local item = curDragDropInfo and curDragDropInfo.item

    local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
    local refDepositSlot = Server.InventoryServer:GetSlot(depositId)

    if not item or not refDepositSlot then
        return
    end

    ---@type EquipmentFeature
    local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
    local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()
    if bExtendItem and depositId ~= ESlotType.MainContainer then
        -- 箱子拖到已装箱子的槽位后，不用走是否放入判断的逻辑。只走是否能替换的逻辑
        return
    end

    local bTypeFit = ItemOperaTool.VerifyItemForTargetSlot(item, refDepositSlot, false)
    local bHasSpace = refDepositSlot:TryFindLocationForItem(item)

    -- 类型符合就切到对应的Tab
    if bTypeFit then
        self:_SelectDepositTab(index)
    end

    if bTypeFit and bHasSpace then

    else
        local extItem = Server.InventoryServer:GetExtItemByIndex(index)
        if not extItem then
            return
        end
        local txt
        if not bTypeFit then
            txt = string.format(InventoryConfig.Loc.ExtItemDontSupportThisItem, extItem.name)
        elseif not bHasSpace then
            txt = string.format(InventoryConfig.Loc.ExtItemSpaceNotEnough, extItem.name)
        end

        if txt then
            Module.CommonTips:ShowSimpleTip(txt)
        end
    end
end

function WarehouseWithTab:_OnTabLongPressedCallback(index)
    -- 界面处于某些状态时，无法通过长按进入扩容箱管理页
    if self._bIsCanInExtManagementMode and not ItemOperaTool.bInSettlement and not Module.CommonWidget:IsAuctionMode() and not ItemOperaTool.CheckIsDIYCabinetPanel() then
        self:_StartExtManagement(index)
    end
end

---@param component TabComponentParams
function WarehouseWithTab:_OnFilterTabClicked(mainIndex, subIndex, component)
    log("OnFilterTabClicked", mainIndex, subIndex, component)
    ---@type WarehouseFilterTab
    local selectedTab = component:GetMainTab(mainIndex)
    local sortClassId = selectedTab:GetSortClassId()

    self._curFilterMode = sortClassId
    self._wtWarehouse:ShowInListMode(sortClassId)

    if self._filterTabCallback and self._filterTabIns then
        self._filterTabCallback(self._filterTabIns, self._curFilterMode)
    end
end

function WarehouseWithTab:_RefreshFilterTabStatus()
    for index, filterTab in ipairs(self._filterTabs) do
        local sortClassID = filterTab:GetSortClassId()
        local fFilterFunc = InventoryLogic.GetFilterFunctionBySortClassId(sortClassID)
        local bHasItem = false
        for _, item in Server.InventoryServer:GetItemsIterator() do
            local slot = item.InSlot
            if slot then
                local bInDeposit = slot:IsDepositorySlot()
                if bInDeposit and fFilterFunc(item) then
                    bHasItem = true
                    break
                end
            end
        end

        self._tabComponent:SetTabEnabled(
            index,
            bHasItem,
            function()
                Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.NoFilterItems)
            end
        )
    end
end

function WarehouseWithTab:_OnCollectionHallBtnVisible()
    -- azhengzheng:控制收藏室显隐
    -- 先判断是否在局外
    if Facade.GameFlowManager:CheckIsInFrontEnd() and not Module.CommonWidget:IsAuctionMode() and ItemOperaTool.CheckIsWarehouseMainPanel() then
        local deviceData = Module.BlackSite:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)

        -- 再判断收藏室等级
        if deviceData and deviceData:GetLevel() ~= 0 then
            self._wtWishListBtn:Visible()
        else
            self._wtWishListBtn:Hidden()
        end
    else
        self._wtWishListBtn:Hidden()
    end
end

-- 收藏室入口按钮得特殊处理
function WarehouseWithTab:_SetCollectionRoomBtnInteraction(bInteractive)
    local btnSlateVisibility = self._wtWishListBtn.Visibility
    if btnSlateVisibility == ESlateVisibility.Collapsed or btnSlateVisibility == ESlateVisibility.Hidden then
        return
    end
    if bInteractive then
        self._wtWishListBtn:Visible()
    else
        self._wtWishListBtn:HitTestInvisible()
    end
end

-----------------------------------------------------------------------
--region Ext Manage 扩容管理

function WarehouseWithTab:_OnExtManageBtnClick()
    if self._bInExtManagementMode then
        self:_EndExtManagement()
    elseif self._bInReplaceExtMode then
        -- self:_EndExtReplace()
        self:_SetExtReplaceMode(not self._bInReplaceExtMode)
    else
        self:_StartExtManagement()
    end
    Module.ItemDetail:CloseItemDetailPanel()
end

function WarehouseWithTab:_CheckExtLvlUpHintShouldShow()
    local bShowLvlUpHint = false
    local allExtIds = Server.InventoryServer:GetAllExtIds()
    for _, extId in ipairs(allExtIds) do
        local slot = Server.InventoryServer:GetSlot(extId)
        local equipItem = slot:GetEquipItem()
        if equipItem and Server.InventoryServer:CheckExtItemCanLvlUp(equipItem.id) then
            bShowLvlUpHint = true
            break
        end
    end

    if bShowLvlUpHint then
        self._wtExtLvlUpHint:SelfHitTestInvisible()
    else
        self._wtExtLvlUpHint:Collapsed()
    end
end


--[[    -- 废弃功能，看是否有一天能复活
function WarehouseWithTab:_StartExtManagement(targetSelectTabIndex)
    if self._bInExtManagementMode then
        return
    end

    self._bInExtManagementMode = true

    -- 进入一次扩容箱管理页后消失
    self._wtExtLvlUpHint:Collapsed()
    self._wtMainCanvas:HitTestInvisible()
    self:PlayWidgetAnim(self.OpenExpansionManager_out)

    -- 进入扩容箱管理界面时更改顶Bar样式
    Module.CommonBar:ChangeBackBtnText(InventoryConfig.Loc.ExpansionManagement)
    Module.CommonBar:ShowTopBarUpgradeBtn(nil)
    Module.CommonBar:SetCurrencyVisible(false)

    InventoryConfig.Events.evtEnterExtManageMode:Invoke(true, self._curDepositIndex)
end

function WarehouseWithTab:_EndExtManagement()
    if not self._bInExtManagementMode then
        return
    end

    self._bInExtManagementMode = false

    self:_SelectDepositTab(self._curDepositIndex)
    self._wtWarehouse:SelfHitTestInvisible()
    self._wtMainCanvas:SelfHitTestInvisible()
    self:PlayWidgetAnim(self.OpenExpansionManager_in)

    -- 离开扩容箱管理界面时回复顶Bar原样式  
    InventoryConfig.Events.evtChangeTopBarStyle:Invoke()
    Module.CommonBar:SetCurrencyVisible(true)
    InventoryConfig.Events.evtEnterExtManageMode:Invoke(false)
end
]]--

function WarehouseWithTab:_StartExtManagement(targetSelectTabIndex)
    if self._bInExtManagementMode then
        return
    end
    Module.ItemDetail:CloseItemDetailPanel()
    self._bInExtManagementMode = true
    self:SetLonger(true)
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtManagerMode(true)
    end

    -- 手游排序规则不一样，由于主仓库是冻结状态不计入排序，所以后续排序不纳入主仓库参考范围
    local function fOnDragStart(cell)
        logerror("SunBillowsTestLeftIn")
        cell:PlayLeftInAnim()
        Module.CommonBar:SetTopBarVisible(false)
        cell:SetDepositIsOperating(true)
        self._wtExtManageBtn:HitTestInvisible()
        self:_HideAllRemoveBtn(true)
    end
    local function fOnDragStop(cell)
        logerror("SunBillowsTestLeftOut")
        cell:PlayLeftOutAnim()
        Module.CommonBar:SetTopBarVisible(true)
        cell:SetDepositIsOperating(false)
        self._wtExtManageBtn:Visible()
    end
    local function fOnSwapPosEachStep(lastIndex, newIndex)
        log("fOnSwap", lastIndex, newIndex)
    end
    local function fOnShiftPosFinally(lastIndex, newIndex)
        log("fOnShiftPosFinally", lastIndex, newIndex)

        --remapping
        local cellsInOrder = self._wtExtVerticalBox:GetAllChildren()
        for index, cell in ipairs(cellsInOrder) do
            -- 重新排序
            self._cacheDepositTabChildren[index] = cell
        end
        -- 排序后，当前选中由lastIndex变为newIndex
        -- 由于手游主仓库图标为冻结状态，所以主仓库不参与排序，所以在排序成功后需要index+1把主仓库算进去
        self._curDepositTabIndex = newIndex + 1
        Server.InventoryServer:ShiftExtOrder(lastIndex, newIndex)
        Server.InventoryServer:DoChangeSortConfig()
    end
    ---@type ReorderableListParams
    local params = {
        verticalBox = self._wtExtVerticalBox,
        dragDelay = WarehouseWithTab.SUB_CELL_DRAG_DELAY,
        fOnDragStart = fOnDragStart,
        fOnDragStop = fOnDragStop,
        fOnSwapPosEachStep = fOnSwapPosEachStep,
        fOnShiftPosFinally = fOnShiftPosFinally
    }
    self._reorderableListComponent = Module.CommonWidget:InitReorderableList(params)
    local minLimit = 1
    local maxLimit = Server.InventoryServer:GetCurDepositSlotNum() - 1
    self._reorderableListComponent:SetLimit(minLimit, maxLimit)

    -- 扩容箱管理模式下，控制其他按钮的交互
    self._wtExtArrangeBtn:HitTestInvisible()
    self._wtSellBtn:HitTestInvisible()
    self._wtWarehouse:HitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(false)

    InventoryConfig.Events.evtEnterExtManageMode:Invoke(true, true)
    self:OnEnterASMOperation(true)

end

-- 隐藏所有卸下按钮
function WarehouseWithTab:_HideAllRemoveBtn(bHide)
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetRemoveBtnVisible(not bHide)
        warehouseTab:SetVisibility(bHide and ESlateVisibility.HitTestInvisible or ESlateVisibility.Visible)
    end
end

function WarehouseWithTab:_EndExtManagement()
    if not self._bInExtManagementMode then
        return
    end
    self._bInExtManagementMode = false
    Module.CommonBar:SetTopBarVisible(true)
    self._wtWarehouse:SelfHitTestInvisible()
    self:SetLonger(false)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        -- 退出管理时需要保证位置都回正，防止产生位置偏移
        if warehouseTab:GetDepositIsOperating() then
            warehouseTab:SetRenderTranslation(FVector2D.ZeroVector)
            warehouseTab:PlayLeftOutAnim()
            warehouseTab:SetDepositIsOperating(false)
        end
        warehouseTab:SetInExtManagerMode(false)
    end

    self._wtExtArrangeBtn:SelfHitTestInvisible()
    self._wtSellBtn:SelfHitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(true)

    self:_SelectDepositTab(self._curDepositIndex)

    InventoryConfig.Events.evtEnterExtManageMode:Invoke(false)
    self:OnEnterASMOperation(false)
end

function WarehouseWithTab:_SetExtReplaceMode(bInReplaceExtMode, ext2Replace)
    self:SetLonger(bInReplaceExtMode)
    if bInReplaceExtMode then
        self:_StartExtReplace(ext2Replace)
        self._extItem2Replace = ext2Replace
    else
        self:_EndExtReplace()
    end
end

function WarehouseWithTab:_StartExtReplace(ext2Replace)
    if self._bInReplaceExtMode then
        return
    end
    self._bInReplaceExtMode = true
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtReplaceMode(true, ext2Replace)
    end

    -- 扩容箱管理模式下，控制其他按钮的交互
    self._wtExtArrangeBtn:HitTestInvisible()
    self._wtSellBtn:HitTestInvisible()
    self._wtWarehouse:HitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(false)

    InventoryConfig.Events.evtEnterExtManageMode:Invoke(true, false)
end

function WarehouseWithTab:_EndExtReplace()
    if not self._bInReplaceExtMode then
        return
    end
    self._bInReplaceExtMode = false
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInExtReplaceMode(false)
    end

    self._wtExtArrangeBtn:SelfHitTestInvisible()
    self._wtSellBtn:SelfHitTestInvisible()
    self._wtWarehouse:SelfHitTestInvisible()
    self:_SetCollectionRoomBtnInteraction(true)
    self:SetLonger(false)

    self:_SelectDepositTab(self._curDepositIndex)
    InventoryConfig.Events.evtEnterExtManageMode:Invoke(false)
end

function WarehouseWithTab:_OnExtCellSelectionChanged(index)
    if index == 0 then
        self._wtMainCanvas:Hidden()
    else
        self._wtMainCanvas:HitTestInvisible()
        local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
        if depositId ~= ESlotType.None then
            self._wtWarehouse:ShowInGridMode(depositId)
        else
        end
        self._curDepositIndex = index
    end
end

function WarehouseWithTab:_OnEndManage()
    self:_EndExtManagement()
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Ext Arrange 扩容整理

function WarehouseWithTab:_InitStartupMultiSelectIndexs()
    self._curMultiTabIndexs = {}

    local bFirst = Facade.ConfigManager:GetUserBoolean(WarehouseWithTab.LAST_SELECT_INDEXS_KEY_NOT_FIRST, true)
    if bFirst then
        Facade.ConfigManager:SetUserBoolean(WarehouseWithTab.LAST_SELECT_INDEXS_KEY_NOT_FIRST, false)

        -- 默认全选
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            local tabIndex = warehouseTab:GetTabIndex()
            if tabIndex <= curDepositNum then
                self._curMultiTabIndexs[i] = true
            end
        end
    else
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        local lastSelectIndexs = Facade.ConfigManager:GetUserArray(WarehouseWithTab.LAST_SELECT_INDEXS_KEY, {})
        for _, index in ipairs(lastSelectIndexs) do
            if index <= curDepositNum then
                self._curMultiTabIndexs[index] = true
            end
        end
    end
end

function WarehouseWithTab:_SaveMultiSelectIndexs()
    local lastSelectIndexs = {}
    for index, value in pairs(self._curMultiTabIndexs) do
        if value then
            table.insert(lastSelectIndexs, index)
        end
    end
    Facade.ConfigManager:SetUserArray(WarehouseWithTab.LAST_SELECT_INDEXS_KEY, lastSelectIndexs)
    Facade.ConfigManager:Flush()
end

local handle = nil
function WarehouseWithTab:_OnExtArrangeBtnPress()
    Module.Inventory.Config.Events.evtWareHouseWithTabExtArrangeBtnPressed:Invoke()
    if handle then
        -- 双击
        Timer.CancelDelay(handle)
        handle = nil

        self:_OnExtArrangeBtnDoubleClick()
    else
        -- self._wtWarehouse:HitTestInvisible()
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHArrange)
        handle = Timer.DelayCall(0.2, self._OnExtArrangeBtnPress_DelayCheck, self)
    end
end

function WarehouseWithTab:_OnExtArrangeBtnPress_DelayCheck()
    -- 单击
    Timer.CancelDelay(handle)
    handle = nil
    -- self._wtWarehouse:Visible()
    self:_OnExtArrangeBtnClick()
end

-- 双击整理当前页面
function WarehouseWithTab:_OnExtArrangeBtnDoubleClick()
    Server.InventoryServer:DoSortSlots({self:GetCurrentShowDepositId()})
end

-- 单机展开整理选项
function WarehouseWithTab:_OnExtArrangeBtnClick()
    if self._bInExtArrangeMode then
        self:_CancelExtArrange()
    else
        -- 检查仓库是否有道具
        local allDepositIds = Server.InventoryServer:GetAllDepositIds()
        local bHasItem = false
        for _, id in ipairs(allDepositIds) do
            local depositSlot = Server.InventoryServer:GetSlot(id)
            if #depositSlot:GetVisibleItems() > 0 then
                bHasItem = true
                break
            end
        end
        -- 检查是否安装扩容箱
        local bHasExt = Server.InventoryServer:GetCurDepositSlotNum() > 1

        if bHasItem then
            if not bHasExt then
                Server.InventoryServer:DoSortSlots({self:GetCurrentShowDepositId()})
            else
                if Module.CommonWidget:IsAuctionMode() or ItemOperaTool.CheckIsDIYCabinetPanel() then
                    self:_StartExtArrangeInAuction()
                else
                    self:_StartExtArrange()
                end
            end
        else
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseNothing2Manage)
        end
    end
end

function WarehouseWithTab:_StartExtArrange()
    if self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = true
    self._bIsCanInExtManagementMode = false
    self:_CancelSell()
    self:SetLonger(true)

    Module.CommonBar:SetCurrencyVisible(false)

    -- 加载整理面板
    if not self._extArrangePanel then
        local arrangePanelWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseExtSub, self._slotExtArrange)
        local arrangeSubPanel = getfromweak(arrangePanelWeakIns)
        if arrangeSubPanel then
            self._extArrangePanel = arrangeSubPanel
            arrangeSubPanel:BindConfirmBtnCallBack(self._OnConfirmExtArrangeBtnClicked, self)
            arrangeSubPanel:BindCancelBtnCallBack(self._CancelExtArrange, self)
            arrangeSubPanel:BindSelectAllCheckBoxCallBack(self._IsSelectAllDeposit, self)
        end
    end
    self._slotExtArrange:SelfHitTestInvisible()

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(true)
    self:OnEnterASMOperation(true)
    self._wtSellBtn:Collapsed()
    self._wtExtArrangeBtn:Collapsed()
    self._wtExtManageBtn:Collapsed()
    self:_SetCollectionRoomBtnInteraction(false)
    self._wtWarehouse:HitTestInvisible()
    self._wtWarehouse:SetJumpPanelInteraction(false)

    -- self:_UpdateSellBtnVisibility(false)
    self:_InitStartupMultiSelectIndexs()
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(true)

        if self._curMultiTabIndexs[i] then
            self:_SetMultiSelected(i, true)
        end
    end
    self:_PostRefreshTabMultiSelected()
    -- 关闭详情页
    Module.ItemDetail:CloseItemDetailPanel()
end

function WarehouseWithTab:_CancelExtArrange()
    if not self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = false
    self._bIsCanInExtManagementMode = true
    self:_SaveMultiSelectIndexs()
    self:SetLonger(false)

    Module.CommonBar:SetCurrencyVisible(true)

    -- 隐藏整理面板
    self._slotExtArrange:Collapsed()
    self._wtSellBtn:SelfHitTestInvisible()
    self._wtExtArrangeBtn:SelfHitTestInvisible()
    self._wtExtManageBtn:Visible()
    self:_SetCollectionRoomBtnInteraction(true)
    self._wtWarehouse:SelfHitTestInvisible()
    self._wtWarehouse:SetJumpPanelInteraction(true)

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(false)
    self:OnEnterASMOperation(false)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(false)
        if i == self._curDepositTabIndex then
            warehouseTab:SetSelected(true)
        end
    end
end

function WarehouseWithTab:_StartExtArrangeInAuction()
    if self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = true
    self._bIsCanInExtManagementMode = false
    self:_CancelSell()
    self:SetLonger(true)
    self:SetArrange(true)

    -- 加载整理面板
    if not self._extArrangePanel then
        local arrangePanelWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseExtSub, self._slotExtArrange)
        local arrangeSubPanel = getfromweak(arrangePanelWeakIns)
        if arrangeSubPanel then
            self._extArrangePanel = arrangeSubPanel
            arrangeSubPanel:BindConfirmBtnCallBack(self._OnConfirmExtArrangeBtnClicked, self)
            arrangeSubPanel:BindCancelBtnCallBack(self._CancelExtArrangeInAuction, self)
            arrangeSubPanel:BindSelectAllCheckBoxCallBack(self._IsSelectAllDeposit, self)
            arrangeSubPanel:SetTipsPanel(false)
            arrangeSubPanel:SetSettingBtn(false)
        end
    end
    self._slotExtArrange:SelfHitTestInvisible()

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(true)
    self._wtSellBtn:Collapsed()
    self._wtExtArrangeBtn:Collapsed()
    self._wtExtManageBtn:Collapsed()
    self:_SetCollectionRoomBtnInteraction(false)
    self._wtWarehouse:HitTestInvisible()
    self._wtWarehouse:SetJumpPanelInteraction(false)

    -- self:_UpdateSellBtnVisibility(false)
    self:_InitStartupMultiSelectIndexs()
    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(true)

        if self._curMultiTabIndexs[i] then
            self:_SetMultiSelected(i, true)
        end
    end
    self:_PostRefreshTabMultiSelected()
end

function WarehouseWithTab:_CancelExtArrangeInAuction()
    if not self._bInExtArrangeMode then
        return
    end

    self._bInExtArrangeMode = false
    self._bIsCanInExtManagementMode = true
    self:_SaveMultiSelectIndexs()
    self:SetLonger(false)
    self:SetArrange(false)

    -- 隐藏整理面板
    self._slotExtArrange:Collapsed()
    self._wtExtArrangeBtn:Visible()
    self:_SetCollectionRoomBtnInteraction(true)
    self._wtWarehouse:SelfHitTestInvisible()
    self._wtWarehouse:SetJumpPanelInteraction(true)

    InventoryConfig.Events.evtEnterExtArrangeMode:Invoke(false)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInMultiMode(false)
        if i == self._curDepositTabIndex then
            warehouseTab:SetSelected(true)
        end
    end
end

function WarehouseWithTab:_OnConfirmExtArrangeBtnClicked()
    local slots2Sort = {}
    for index, bSelected in pairs(self._curMultiTabIndexs) do
        if bSelected then
            local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
            table.insert(slots2Sort, depositId)
        end
    end
    
    if #slots2Sort > 0 then
        Server.InventoryServer:DoSortSlots(slots2Sort)
    end

    -- if not Server.InventoryServer:GetHasSort() then
    --     -- 如果没有整理过，会弹一个前往设置的TopHint
    --     self:_ShowGoToSettingHint()
    -- end

    if Module.CommonWidget:IsAuctionMode() or ItemOperaTool.CheckIsDIYCabinetPanel() then
        self:_CancelExtArrangeInAuction()
    else
        self:_CancelExtArrange()
    end
end

function WarehouseWithTab:_OpenExtSettingTips()
    local text1 = Module.Inventory.Config.Loc.AutoExtText1
    local text2 = Module.Inventory.Config.Loc.AutoExtText2
    local tipsData = {{contentType = ETipsContentType.OnlyText, desc = text1}, {contentType = ETipsContentType.OnlyText, desc = text2}}
    local title = Module.Inventory.Config.Loc.AutoExtText
    local handle = Module.ItemDetail:OpenItemCommonMiniTipsType2(self._wtShowExtSettingTipsBtn, self._wtShowExtSettingTipsBtn, title, nil ,tipsData, true, nil, nil, nil, nil, true)
    if handle == nil then
        return
    end
end

function WarehouseWithTab:_OnArrangeSettingBtnClicked()
    -- Module.Inventory:ShowExtArrangeWindow()
    Module.SystemSetting:ShowSystemSettingMainView(Module.SystemSetting.Config.ESystemSettingPanel.SystemSetting, true)
end

function WarehouseWithTab:_OnMaskImgClicked()
    self:_CancelExtArrange()
    return {}
end

function WarehouseWithTab:_OnWishListBtnClicked()
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        Facade.UIManager:AsyncShowUI(UIName2ID.WishPopView)
    else
        Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.TheCurrentPageCannotOpenTheFavoritesList)
    end
end

--endregion
-----------------------------------------------------------------------



function WarehouseWithTab:_OnFilterScrolled(finalOffset)
    local firstFilterTab = #self._filterTabs > 0 and self._filterTabs[1] or nil
    if firstFilterTab then
        local firstTabGeometry = firstFilterTab:GetCachedGeometry()
        local topLeftAbsPos = firstTabGeometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.TOP_LEFT_VECTOR)

        local filterScrollBoxGeometry = self._wtFilterScrollBox:GetCachedGeometry()
        local topLeftLocalPos = filterScrollBoxGeometry:AbsoluteToLocal(topLeftAbsPos)

        -- log(topLeftLocalPos.X, topLeftLocalPos.Y)
        if topLeftLocalPos.Y > WarehouseWithTab.FILTER_TAB_FOLD_THRESHOLD then
            self._bShouldFoldFilterTab = true
        end
    end
end

function WarehouseWithTab:_OnFilterBtnClicked(bIsChecked)
    self:_CancelSell()

    if bIsChecked == nil then
        bIsChecked = not self._bFilterBtnToggle
    end

    self._bFilterBtnToggle = bIsChecked
    -- self._wtFilterBtnBp:SetCppValue("BpSelected", bIsChecked)
    -- self._wtFilterBtnBp:BP_SetStyle()

    if bIsChecked then
        self:_PlayFilterAnim(true)
        
        self._wtWarehouse:ShowInListMode()
        
        self:_UpdateSellBtnVisibility(false)
        self:ShowExtArrangeBtn(false)
    else
        self:_PlayFilterAnim(false)

        self._wtWarehouse:ShowInGridMode()

        self:_UpdateSellBtnVisibility(true)
        self:ShowExtArrangeBtn(true)
    end
end

function WarehouseWithTab:_PlayFilterAnim(bForward)
    if not self._filterAimeTime then
        self._filterAnimTime = self.ClassShow_Anim:GetEndTime()
    end
    Timer.CancelDelay(self._filterAnimHandle)
    self._filterAnimHandle = nil

    if bForward then
        self._wtFilterPanel:SelfHitTestInvisible()
        self:PlayWidgetAnim(self.ClassShow_Anim)
    else
        local function fDelayHideFilterPanel()
            self._wtFilterPanel:Collapsed()
        end
        self._filterAnimHandle = Timer.DelayCall(self._filterAnimTime, fDelayHideFilterPanel)
        self:PlayWidgetAnim(self.ClassShow_Anim, 1, EUMGSequencePlayMode.Reverse)
    end
end

function WarehouseWithTab:_OnSellBtnClicked(bForceEnterSellMode)
    local bInSellMode = Module.CommonWidget:GetIsItemSellMode()
    logerror("WarehouseWithTab:_OnSellBtnClicked", bForceEnterSellMode, bInSellMode)
    if bForceEnterSellMode == true or not bInSellMode then
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILobbyWHSell)

        -- 检查是否有道具可以出售
        local slotTypes2Check = Server.InventoryServer:GetAllDepositIds()
        table.append(slotTypes2Check, {
            ESlotType.MainWeaponLeft,
            ESlotType.MainWeaponRight,
            ESlotType.Helmet,
            ESlotType.BreastPlate,
            ESlotType.BagSpaceContainer,
            ESlotType.SafeBoxContainer,
            ESlotType.KeyChainContainer,
        })
        local bHasItem2Sell = false
        for _, id in ipairs(slotTypes2Check) do
            local depositSlot = Server.InventoryServer:GetSlot(id)
            if #depositSlot:GetVisibleItems() > 0 then
                bHasItem2Sell = true
                break
            end
        end
        if not bHasItem2Sell then
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseNothing2Sell)
        else
            self:_StartSell()
        end
    elseif bForceEnterSellMode == false or bInSellMode then
        self:_CancelSell()
    end
end

function WarehouseWithTab:CheckSellBox(bIsChecked)
    self:_OnSellBtnClicked(bIsChecked)
end

function WarehouseWithTab:_StartSell()
    if Module.CommonWidget:GetItemOperMode() == EItemOperaMode.SellMode then
        return
    end
    Module.CommonWidget:SetItemOperMode(EItemOperaMode.SellMode)
    self._bIsCanInExtManagementMode = false

    self._mapCurrency2SellPrice = {}
    self._sameItemNum = {} -- id : num
    ---@type ItemBase[]
    self._otherSameItems = {}
    self:PlayWidgetAnim(self.SellSubPanel_Anim_in)

    InventoryConfig.Events.evtEnterSellMode:Invoke(true)

    if not self._sellSubPanel then
        local sellPanelWeakIns = Facade.UIManager:AddSubUI(self, UIName2ID.WarehouseSellSub, self._slotSellSubSlot)
        local sellSubPanel = getfromweak(sellPanelWeakIns)
        if sellSubPanel then
            self._sellSubPanel = sellSubPanel
            sellSubPanel:BindConfirmBtnCallBack(self._OnConfirmSellBtnClick, self)
            sellSubPanel:BindCancelBtnCallBack(self._OnCancelSellBtnClick, self)
        end
    end
    self._slotSellSubSlot:SelfHitTestInvisible()
    -- 按理说如果_sellSubPanel为空就无法进入正常的出售模式，为了防止没有正常进入出售模式而导致阻塞，所以当sellsubpanel没创建出来时需要return并退出sellmode
    if not self._sellSubPanel then
        self:_CancelSell()
        return
    end
    self._sellSubPanel:IsSelectionItem(false)
    self._sellSubPanel:RefreshSellPrices(self._mapCurrency2SellPrice)

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        if warehouseTab then
            warehouseTab:SetInSellMode(true)
        else
            loginfo("WarehouseDepositTab is nil, please check")
            return
        end
    end

    self._wtWarehouse:SetInMultiSellMode(true)
    self._wtExtArrangeBtn:Collapsed()
    self._wtSellBtn:Collapsed()
    self._wtExtManageBtn:Collapsed()
    self:_SetCollectionRoomBtnInteraction(false)
    self._wtWarehouse:SetJumpPanelInteraction(false)
    Module.ItemDetail:CloseItemDetailPanel()
    -- -- 默认隐藏详情页（仅最开始），并隐藏所有按钮
    -- self._wtItemDetailView:Collapsed()
    -- self._wtItemDetailView:SetBtnVisible(false)
end

function WarehouseWithTab:_CancelSell()
    logerror("WarehouseWithTab:_CancelSell", Module.CommonWidget:GetItemOperMode())

    if Module.CommonWidget:GetItemOperMode() ~= EItemOperaMode.SellMode then
        return
    end
    self._bIsCanInExtManagementMode = true
    Module.CommonWidget:SetItemOperMode(EItemOperaMode.Default)

    self._mapCurrency2SellPrice = {}
    self._sameItemNum = {} -- id : num
    ---@type ItemBase[]
    self._otherSameItems = {}

    InventoryConfig.Events.evtEnterSellMode:Invoke(false)

    self._slotSellSubSlot:Collapsed()

    for i, warehouseTab in ipairs(self._allDepositTabs) do
        warehouseTab:SetInSellMode(false)
    end

    self._wtWarehouse:SetInMultiSellMode(false)
    self._wtExtArrangeBtn:SelfHitTestInvisible()
    self._wtSellBtn:SelfHitTestInvisible()
    self._wtExtManageBtn:Visible()
    self:_SetCollectionRoomBtnInteraction(true)
    self._wtWarehouse:SetJumpPanelInteraction(true)

    self:_HideTopHint()
end

function WarehouseWithTab:_RefreshSellPrices()
    for typeName, type in pairs(ECurrencyClientType) do
        local price = self._mapCurrency2SellPrice[type]
        local icon = self._mapType2PriceIcon[type]
        local textblock = self._mapType2PriceText[type]

        if icon and textblock then
            if price and price > 0 then
                icon:SelfHitTestInvisible()
                textblock:SelfHitTestInvisible()
                textblock:SetText(MathUtil.GetTheThreePositionGradingMethod(price))
            else
                icon:Collapsed()
                textblock:Collapsed()
            end
        end
    end
end

function WarehouseWithTab:_OnCancelSellBtnClick()
    logerror("WarehouseWithTab:_OnCancelSellBtnClick")

    self:_CancelSell()
end

function WarehouseWithTab:_OnConfirmSellBtnClick()
    local itemsToSell = Module.CommonWidget:GetSelectedItems()

    local fOnSellResultCallback =
        CreateCallBack(
        function(ins, result, res)
            if result then
                ins._mapCurrency2SellPrice = {}
                -- ins:_RefreshSellPrices()
                ins:_CancelSell()
            else
            end
        end,
        self
    )

    ItemOperaTool.DoSellItems(itemsToSell, fOnSellResultCallback)
end

function WarehouseWithTab:_ShowSelectAllHint(item)
    local tip = string.format(InventoryConfig.Loc.WarehouseTopHint_SelectAll, item.name)
    local btnText = InventoryConfig.Loc.WarehouseTopHintBtnText_SelectAll
    local fConfirmCallback = function()
        local allMatchItems = InventoryLogic.GetItemCanSellInMultiModeById(item.id)

        for _, matchItem in ipairs(allMatchItems) do
            if not Module.CommonWidget:CheckItemMultiSelected(matchItem) then
                Module.CommonWidget:SetItemMultiSelected(matchItem, true, true)
            end
        end
    end
    self:_ShowTopHint(tip, btnText, fConfirmCallback)
end

function WarehouseWithTab:_ShowGoToSettingHint()
    local tip = InventoryConfig.Loc.WarehouseTopHint_GoToSetting
    local btnText = InventoryConfig.Loc.WarehouseTopHintBtnText_GoToSetting
    local function fOnConfirm()
        Module.SystemSetting:ShowSystemSettingMainView(Module.SystemSetting.Config.ESystemSettingPanel.SystemSetting, true)
    end

    self:_ShowTopHint(tip, btnText, fOnConfirm)
end

function WarehouseWithTab:_ShowAutoSortHint(extItem)
    local extItemId = extItem.id
    local extBoxDesc = ItemConfigTool.GetExtentionBoxDescRowById(extItemId)
    if not extBoxDesc then
        return
    end

    local itemSubType = ItemHelperTool.GetSubTypeById(extItemId)
    local subText = InventoryConfig.MapExtType2FirstTimeEquipTip[itemSubType]
    if not subText then
        return
    end

    local tip = string.format(InventoryConfig.Loc.WarehouseTopHint_AutoSortAfterEquipExtItem, subText)
    local btnText = InventoryConfig.Loc.WarehouseTopHintBtnText_AutoSortAfterEquipExtItem

    local function fOnConfirm()
        local refExtId = extItem.InSlot.SlotType
        local refDepositId = Server.InventoryServer:GetDepositIdByExtId(refExtId)

        local depositSortClassTable = Facade.TableManager:GetTable("DepositSortClass")
        local forceExtFirstCls = {}
        for k, row in pairs(depositSortClassTable) do
            table.insert(forceExtFirstCls, tonumber(k))
        end

        Server.InventoryServer:DoSortSlots({ESlotType.MainContainer, refDepositId}, nil, forceExtFirstCls)
    end

    self:_ShowTopHint(tip, btnText, fOnConfirm)
end

function WarehouseWithTab:_ShowTopHint(hintText, btnText, btnCallback, showDuration)
    local geometry = self._wtMainCanvas:GetCachedGeometry()
    local size = geometry:GetLocalSize()
    local length = size.X
    if self._bInExtManagementMode then
        length = length + 96
    end
    if self._topHint and not hasdestroy(self._topHint) then
        self._topHint:InitTopHint(hintText, btnText, btnCallback, showDuration)
        self._topHint:SetLength(length)
    elseif not self._selectAllPanelHandle or hasdestroy(self._topHint) then
        ---@param selfView WarehouseWithTab
        ---@param selectAllPanel WarehouseTopHint
        local function fOnSelectAllPanelLoaded(selfView, selectAllPanel)
            selfView._topHint = selectAllPanel
            selfView._topHint:InitTopHint(hintText, btnText, btnCallback, showDuration)   
            selfView._topHint:SetLength(length)
        end
        self._selectAllPanelHandle =
            Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseSelectAllHint, fOnSelectAllPanelLoaded, self)
    end
end

function WarehouseWithTab:_HideTopHint()
    if self._topHint then
        self._topHint:AnimHide()
    elseif self._selectAllPanelHandle then
        self._selectAllPanelHandle:Release()
        self._selectAllPanelHandle = nil
    end
end

function WarehouseWithTab:_OnPostItemMultiSelected(item, bSelected, bFromOnClick)
    local bStillHasItem = false
    if item and Server.InventoryServer:GetItemByGid(item.gid) then
        bStillHasItem = true
    end

    if item and item.aginType ~= 0 then
        local curTime = TimeUtil:GetCurrentTime()
        local endTime = item.aginBeginTime + item.aginDuration
        if curTime < endTime then
            return Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.TimeItemCantSell)
        end
    end

    if not Module.CommonWidget:IsInMultiSelectedMode() then
        return
    end

    if not item then
        self._mapCurrency2SellPrice = {}
        for _, selectedItem in ipairs(Module.CommonWidget:GetSelectedItems()) do
            local currencyClientType = Server.ShopServer:GetRecycleCurrencyTypeByItem(selectedItem)
            self:_SetSellPrice(currencyClientType, selectedItem:GetTotalMallSellPrice())
        end
    else
        local currencyClientType = Server.ShopServer:GetRecycleCurrencyTypeByItem(item)
        local bCurrentSelected = Module.CommonWidget:CheckItemMultiSelected(item)
        if bCurrentSelected then
            if not self._sameItemNum[item.id] then
                self._sameItemNum[item.id] = 0
            end
            self._sameItemNum[item.id] = self._sameItemNum[item.id] + 1
            self:_CheckSameItemClick(item)

            self:_SetSellPrice(currencyClientType, item:GetTotalMallSellPrice())
        else
            if not self._sameItemNum[item.id] then
                self._sameItemNum[item.id] = 0
            end
            self._sameItemNum[item.id] = self._sameItemNum[item.id] - 1
            if self._sameItemNum[item.id] == 0 then
                self._sameItemNum[item.id] = nil
            end

            self:_SetSellPrice(currencyClientType, -item:GetTotalMallSellPrice())
        end
    end

    local allSelectedItems = Module.CommonWidget:GetSelectedItems()

    if #allSelectedItems == 0 then
        if self._sellSubPanel then
            self._sellSubPanel:IsSelectionItem(false)
        end
    else
        if self._sellSubPanel then
            self._sellSubPanel:IsSelectionItem(true)
        end
    end
    if self._sellSubPanel then
        self._sellSubPanel:RefreshSellPrices(self._mapCurrency2SellPrice)
    end
    --[[
    if #allSelectedItems == 0 then
        -- self._wtSellHintText:SetText(InventoryConfig.Loc.WarehouseSellTip_NoSelection)
        self._wtSellHintText:Collapsed()
        -- self._wtConfirmSellBtn:SetIsEnabled(false)
        self._wtIsSelectionText:SelfHitTestInvisible()
        self._wtIsSelectionText:SetText(InventoryConfig.Loc.WarehouseSellTip_NoSelection)
        self._wtConfirmSellBtn:Collapsed()
    else
        self._wtSellHintText:SelfHitTestInvisible()
        self._wtSellHintText:SetText(InventoryConfig.Loc.WarehouseSellTip_HasSelection)
        self._wtIsSelectionText:Collapsed()
        self._wtConfirmSellBtn:SelfHitTestInvisible()
        -- self._wtConfirmSellBtn:SetIsEnabled(true)
    end
    self:_RefreshSellPrices()
    ]]--
end

function WarehouseWithTab:_OnExtManageDragSortStart(bStart)
    local txt = ""
    if bStart then
        txt = InventoryConfig.Loc.DragUpAndDownToSort
    else
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        if curDepositNum == 1 then
            txt = InventoryConfig.Loc.ClickRightEmptySlotToEquipExtItem
        else
            txt = InventoryConfig.Loc.SelectExtItemToManage
        end
    end
    self._wtExtSelectNothingPanel:SetCppValue("Text", txt)
    self._wtExtSelectNothingPanel:BP_Set_Type()
end

function WarehouseWithTab:_SetSellPrice(currencyClientType, price)
    if not self._mapCurrency2SellPrice[currencyClientType] then
        self._mapCurrency2SellPrice[currencyClientType] = price
    else
        self._mapCurrency2SellPrice[currencyClientType] = self._mapCurrency2SellPrice[currencyClientType] + price
    end
end

function WarehouseWithTab:_CheckSameItemClick(item)
    if self._sameItemNum[item.id] >= MAX_SAME_ITEM_CLICK_NUM then
        local sameItems = InventoryLogic.GetItemCanSellInMultiModeById(item.id)
        if #sameItems >= MIN_SAME_ITEM_IN_DEPOSIT then
            self._otherSameItems = {}
            for _, sameItem in ipairs(sameItems) do
                if not Module.CommonWidget:CheckItemMultiSelected(sameItem) then
                    table.insert(self._otherSameItems, sameItem)
                end
            end
            if #self._otherSameItems > 0 then
                self:_ShowSelectAllHint(item)
            end
        end
    end
end

function WarehouseWithTab:_OnSellAllBtnClick()
    for _, item in ipairs(self._otherSameItems) do
        Module.CommonWidget:SetItemMultiSelected(item, true, true)
    end
    self:_HideTopHint()
end

function WarehouseWithTab:_OnItemMove(itemMoveInfo, changeNum)
    self:_RefreshFilterTabStatus()

    -- 在仓库扩容箱安装成功后应该切换到打开扩容箱的页面
    -- 在仓库卸下扩容箱后应该切换到主仓库并定位到卸下的扩容箱
    if (itemMoveInfo.OldLoc and itemMoveInfo.OldLoc.ItemSlot:IsExtendSlot()) then
        -- Remove
        self:_InitDepositTabList()
        local warehouse = self:GetWarehouseWidget()
        local warehouseSlotView = warehouse:GetWarehouseSlotView()

        local targetItem = itemMoveInfo.item

        -- 本身当前帧SlotView会刷新，需要延迟一下
        local fDelayScrollToItem = function()
            warehouseSlotView:ScrollToItem(targetItem, true, true)
        end
        self._delayScrollToItemHandle = Timer.DelayCall(0.1, fDelayScrollToItem)
        self:_EndExtManagement()
    elseif (itemMoveInfo.NewLoc and itemMoveInfo.NewLoc.ItemSlot:IsExtendSlot()) then
        -- Add
        local targetExtSlotType = itemMoveInfo.NewLoc.ItemSlot.SlotType
        local targetDepositId = Server.InventoryServer:GetDepositIdByExtId(targetExtSlotType)
        local targetIndex = Server.InventoryServer:GetIndexByDepositId(targetDepositId)
        self:_InitDepositTabList(targetIndex)
        if self._allDepositTabs[targetIndex] then
            self._wtDepositTabScrollBox:ScrollWidgetIntoView(self._allDepositTabs[targetIndex], true, EDescendantScrollDestination.IntoView)
        end
    end

    if itemMoveInfo.OldLoc and itemMoveInfo.NewLoc then
        ---@type ItemSlot
        local oldSlot = itemMoveInfo.OldLoc.ItemSlot
        ---@type ItemSlot
        local newSlot = itemMoveInfo.NewLoc.ItemSlot
        if newSlot ~= oldSlot and newSlot:IsDepositorySlot() and newSlot.SlotType ~= oldSlot.SlotType then
            local index = Server.InventoryServer:GetIndexByDepositId(newSlot.SlotType)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                if not tab then
                    log("depositTab is nil, please check self._allDepositTabs")
                    return
                end
                tab:HighlightTab(1, false)
                -- InventoryConfig.Events.evtJumpToExtSlot:Invoke(index)
                self:_SelectDepositTab(index, itemMoveInfo.item)
            end
        elseif itemMoveInfo.Reason == PropChangeType.Modify and itemMoveInfo.bIncreaseOrDecrease then
            -- 如果是modify，则根据道具数量判断是否为放入
            local index = Server.InventoryServer:GetIndexByDepositId(newSlot.SlotType)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                if not tab then
                    log("depositTab is nil, please check self._allDepositTabs")
                    return
                end
                tab:HighlightTab(1, false)
                self:_SelectDepositTab(index, itemMoveInfo.item)
            end
        end
    elseif itemMoveInfo.NewLoc then
        local newSlot = itemMoveInfo.NewLoc.ItemSlot
        if newSlot:IsDepositorySlot() then
            -- 
            local index = Server.InventoryServer:GetIndexByDepositId(newSlot.SlotType)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                if not tab then
                    log("depositTab is nil, please check self._allDepositTabs")
                    return
                end
                tab:HighlightTab(1, false)
                self:_SelectDepositTab(index, itemMoveInfo.item)
            end
        end
    end
end

-- 存不同扩容箱的数量
function WarehouseWithTab:_SaveExpansionNum()
    local allDepositIds = Server.InventoryServer:GetAllDepositIds()

    for _, id in ipairs(allDepositIds) do
        local slot = Server.InventoryServer:GetSlot(id)
        self._depositNum[id] = slot and slot:GetItemsNum()
    end
end
-- 将不同扩容箱的数量进行对比
function WarehouseWithTab:_CompareExpansionNum()
    local allDepositIds = Server.InventoryServer:GetAllDepositIds()
    local changeNum

    for _, id in ipairs(allDepositIds) do
        local slot = Server.InventoryServer:GetSlot(id)
        if not self._depositNum[id] then
            self._depositNum[id] = 0
        end
        self._depositNum[id] = slot:GetItemsNum() - self._depositNum[id]
    end

    for depositId, num in pairs(self._depositNum) do
        local index = Server.InventoryServer:GetIndexByDepositId(depositId)
        if index > 0 then
            local tab = self._allDepositTabs[index]
            if not tab then
                tab:HighlightTab(num, false)
            end
        end
    end
end

function WarehouseWithTab:_OnItemMoveBatch(itemMoveInfos)
    local bAddNewExtItem = false
	local extItem
	for itemGID, itemMoveInfo in pairs(itemMoveInfos) do
		---@type ItemBase
		local item = itemMoveInfo.item

		---@type ItemLocation
		local newLoc = itemMoveInfo.NewLoc
		local targetSlot = newLoc and newLoc.ItemSlot or nil

		---@type ItemLocation
		local srcLoc = itemMoveInfo.OldLoc
		local sourceSlot = srcLoc and srcLoc.ItemSlot or nil

		if targetSlot and targetSlot:IsExtendSlot() then
			extItem = item
			bAddNewExtItem = true
		elseif sourceSlot and sourceSlot:IsExtendSlot() then
			bAddNewExtItem = false
			break
		end
	end

	if bAddNewExtItem and extItem then
		local itemSubType = ItemHelperTool.GetSubTypeById(extItem.id)
		if itemSubType ~= EExtensionType.Item then
			-- 玩家每次安装一个新的，非物品箱的扩容箱，并且不是替换，都触发一次弹窗提示：
			self:_ShowAutoSortHint(extItem)
		end
	end
end

---@param res pb_CSDepositSortMultiplePosRes
function WarehouseWithTab:_OnPostSortMultiPos(res, capacityChanges)
    if res.result == 0 then
        for depositId, changeNum in pairs(capacityChanges) do
            local index = Server.InventoryServer:GetIndexByDepositId(depositId)
            if index > 0 then
                local tab = self._allDepositTabs[index]
                -- tab:HighlightTab(#res.changes.prop_changes, true)
                if tab then
                    tab:HighlightTab(changeNum, true)
                end
            end
        end
    end

    -- Full refresh
    self._wtWarehouse:ShowInGridMode()
end

function WarehouseWithTab:_OnNotifyExtOrderChanged()
    self:_InitDepositTabList(self._curDepositTabIndex)
end

function WarehouseWithTab:_WarehouseArrangeFaild()
    -- local slots2Sort = {}
    -- for index, bSelected in pairs(self._curMultiTabIndexs) do
    --     if bSelected then
    --         local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
    --         table.insert(slots2Sort, depositId)
    --     end
    -- end
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    local lastSelectIndexs = Facade.ConfigManager:GetUserArray(WarehouseWithTab.LAST_SELECT_INDEXS_KEY, {})

    if curDepositNum == 1 then
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.ArrangeFaildTips)
    elseif #lastSelectIndexs < curDepositNum then
        -- 有可用扩容且未勾选时
        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SortSlotFailTips1)
    else
        -- -- 是否开启了"跨类别换行整理"
        -- if Server.InventoryServer:GetSortStype() == eDepositSortStyle.class_first then
        --     -- 是否勾选弹窗中的不再提醒
        --     if self._isNomoreTip==true then
        --         LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SortSlotFail)
        --     else
        --         -- 跳出弹窗
        --         Facade.UIManager:AsyncShowUI(UIName2ID.ArrangePopWindow, nil, nil)
        --     end
        -- else
        --     -- 提示空间不足
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SortSlotFailTips2)
        -- end
    end
end

-- 进入收藏室按钮点击
function WarehouseWithTab:_OnCollectionHallBtnClicked()
    -- Module.BlackSite.JumpToMainPanelByUIIdx, self, BlackSiteDefine.EBlackSiteEntranceType.CollectionRoom
    local function fOnConfirmCallback()
        Module.BlackSite:JumpToMainPanelByUIIdx(BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement, true)
    end
    local function fOnCancelCallback()
        return
    end
    Module.CommonTips:ShowConfirmWindow(InventoryConfig.Loc.GoToCollectionRoomTips, fOnConfirmCallback, fOnCancelCallback, InventoryConfig.Loc.CancelText, InventoryConfig.Loc.ConfirmText)
end

function WarehouseWithTab:OnPopWindowStageChange()
    self._isNomoreTip=not self._isNomoreTip
end

---@param res pb_CSDepositOperateExtensionBoxRes
function WarehouseWithTab:_OnExtensionBoxRes(res)
    if res.result == 0 then
        -- self:_InternalInitWarehouseExtList(0)

        if res.operation == ExtensionOpType.OpreateUninstall then
            -- 检查物品去向
            local changes = res.changes.prop_changes
            local bToMainWarehouse = false
            local bToExtWarehouse = false
            for _, change in ipairs(changes) do
                local src = change.src.pos
                local target = change.dest.pos

                if src > ESlotType.DepositoryStart and src < ESlotType.DepositoryEnd then
                    if bToMainWarehouse and bToExtWarehouse then
                        break
                    elseif not bToMainWarehouse and target == ESlotType.MainContainer then
                        bToMainWarehouse = true
                    elseif not bToExtWarehouse and target > ESlotType.MainContainer and target < ESlotType.DepositoryEnd then
                        bToExtWarehouse = true
                    end
                end
            end

            local tip
            if bToMainWarehouse and bToExtWarehouse then
                tip = InventoryConfig.Loc.UnequipExtItemSuccessHintSuffix_AllWarehouse
            elseif bToMainWarehouse then
                tip = InventoryConfig.Loc.UnequipExtItemSuccessHintSuffix_MainWarehouse
            elseif bToExtWarehouse then
                tip = InventoryConfig.Loc.UnequipExtItemSuccessHintSuffix_ExtWarehouse
            else

            end
            Module.CommonTips:ShowSimpleTip(tip)
        end

    else
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.UnequipExtItemFailHint)
    end
end

function WarehouseWithTab:SetExtManagerBtnPanels(bShow)
    if bShow then
        self._wtExtManagePanel:Visible()
    else
        self._wtExtManagePanel:Collapsed()
    end
end

-- 全选相关逻辑
function WarehouseWithTab:_IsSelectAllDeposit(bSelected)
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    if bSelected then
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            local tabIndex = warehouseTab:GetTabIndex()
            if tabIndex <= curDepositNum then
                self:_SetMultiSelected(tabIndex, true)
            end
        end
    else
        for i, warehouseTab in ipairs(self._allDepositTabs) do
            local tabIndex = warehouseTab:GetTabIndex()
            if tabIndex <= curDepositNum then
                self:_SetMultiSelected(tabIndex, false)
            end
        end
    end
    self:_PostRefreshTabMultiSelected()
end

function WarehouseWithTab:AutoArrangeWarehouse()
    -- 只有进入仓库时才会自动整理，其他的例如交易行或者结算转移界面都不会触发自动整理
	local curStackUi = Facade.UIManager:GetCurrentStackUI()
    if curStackUi and curStackUi.UINavID == UIName2ID.WarehouseMain and ItemOperaTool.CheckRunWarehouseLogic() then
        local allDepositIds = Server.InventoryServer:GetAllDepositIds()
        Server.InventoryServer:DoSortSlots(allDepositIds, nil, nil, true)
    end
end

-- 进入整理、出售、扩容箱管理界面时对withtab进行某些设置
function WarehouseWithTab:OnEnterASMOperation(bEnter)
    self._wtMaskPanel:SetRenderOpacity(bEnter and 0.1 or 1)
end

--endregion
--==================================================

return WarehouseWithTab
