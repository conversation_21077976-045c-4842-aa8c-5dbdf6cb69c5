----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGVoice)
----- LOG FUNCTION AUTO GENERATE END -----------





--- GVoice 按钮脚本
UITable[UIName2ID.GVoiceMicWidget] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.Widget.GVoiceMicWidget",
    BPKey = "WBP_ControllerFunctionButtonSpeak",
    HUDName = "GVoiceMicWidget",
    ZOrderBase = EHUDBaseZorder.Touch,
    ZOrderOffset = 0
}

UITable[UIName2ID.GVoiceSpeakerWidget] = {
    UILayer = EUILayer.HUD,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.Widget.GVoiceSpeakerWidget",
    BPKey = "WBP_ControllerFunctionButtonSound",
    HUDName = "GVoiceSpeakerWidget",
    ZOrderBase = EHUDBaseZorder.Touch,
    ZOrderOffset = 0
}


--- GVoice弹窗界面
UITable[UIName2ID.SpeakerPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.Widget.SpeakerPanel",
    BPKey = "WBP_Chat_Speaker",
    Anim = {
        --FlowInAni = "WBP_Chat_Speaker_in",
        FlowOutAni = "WBP_Chat_Speaker_out",
    },
    SubUIs = {
        UIName2ID.TeammateSpeakerWidget
    }
}
UITable[UIName2ID.TeammateSpeakerWidget] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.TeammateSpeakerWidget",
    BPKey = "WBP_Chat_Slide"
}
UITable[UIName2ID.MicPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.Widget.MicPanel",
    BPKey = "WBP_Mic_Panel",
    
    Anim = {
        --FlowInAni = "WBP_Mic_Panel_in",
        FlowOutAni = "WBP_Mic_Panel_Out",
    }
}

-- 这个对应的LuaPath是没有的
-- UITable[UIName2ID.MsgPanelWidget] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.GVoiceModule.UI.Widget.MsgPanelWidget",
--     BPKey = "WBP_Chat_Msg_Operation_Widget"
-- }



UITable[UIName2ID.SpeakerListPanel] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.SpeakerListPanel",
    BPKey = "WBP_Speaker_List",
    HUDName = "SpeakerListPanel",
    ZOrderBase = EHUDBaseZorder.Common,
    ZOrderOffset = 0,
    SubUIs = {
        UIName2ID.SpeakerListItem
    }
}

UITable[UIName2ID.SpeakerListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GVoiceModule.UI.SpeakerListItem",
    BPKey = "WBP_Speaker_List_Item"
}

local UDFMGameVoice = import "DFMGameVoice"
local UDFMGameChat = import "DFMGameChat"

local GVoiceConfig = {
    PosEnum = {
        LeftTop = FVector2D(0,0),
        RightTop = FVector2D(1,0),
        LeftBottom = FVector2D(0,1),
        RightBottom = FVector2D(1,1),
        Zero = FVector2D(0,0)
    },

    EDeviceConnectionState = {
        Unconnected = 0,
        WiredHeadSet = 1,
        Bluetooth = 2
    },
    
    EGVoiceRoomType = {
        Lobby = 1,      --局外
        InGame = 2,     --局内
    },


    Events = {
        --进退语音房间的事件（玩家自己）
        --RoomName：房间名称
        --RoomChannel：房间频道
        --bJoin：true-进房 false-退房
        evtGVoiceRoomStateChanage = LuaEvent:NewIns("evtGVoiceRoomStateChanage"),

        --说话状态变化事件（玩家自己）
        --IsSpeaking：是否在说话 true-在说话 false-没说话
        evtGVoiceSpeakStateChange = LuaEvent:NewIns("evtGVoiceSpeakStateChange"),

        --其他玩家房间状态变化事件
        --RoomName：房间名称
        --RoomChannel：房间频道
        --OpenId：玩家ID
        --Action：玩家行为：进房-kCompleteCodeRoomMemberInRoom、退房-kCompleteCodeRoomMemberOutRoom、开麦-kCompleteCodeRoomMemberMicOpen、关麦-kCompleteCodeRoomMemberMicClose
        evtGVoiceRoomMemberRoomStateChange = LuaEvent:NewIns("evtGVoiceRoomMemberRoomStateChange"),

        --其他玩家说话状态变化事件
        --RoomName：房间名称
        --RoomChannel：房间频道
        --OpenId：玩家ID
        --IsSpeaking：是否在说话 true-在说话 false-没说话
        evtGVoiceRoomMemberSpeakStateChange = LuaEvent:NewIns("evtGVoiceRoomMemberSpeakStateChange"),

        --离线语音转文字完成的回调事件
        --Text：转换后的文字内容
        --FileId：GVoice组件使用的录音文件Id
        evtGVoiceSpeechToText = LuaEvent:NewIns("evtGVoiceSpeechToText"),


        --上传录音文件完成的回调事件
        --bSuc：是否成功
        --FilePath：本地录音文件路径
        --FileId：GVoice组件使用的录音文件Id
        evtGVoiceUploadVoiceFile = LuaEvent:NewIns("evtGVoiceUploadVoiceFile"),

        --下载录音文件完成的回调事件
        --bSuc：是否成功
        --FilePath：本地录音文件路径
        --FileId：GVoice组件使用的录音文件Id
        evtGVoiceDownloadVoiceFile = LuaEvent:NewIns("evtGVoiceDownloadVoiceFile"),

        --播放录音文件完成的回调事件
        --FilePath：本地录音文件路径
        evtPlayRecordedFileDelegate = LuaEvent:NewIns("evtPlayRecordedFileDelegate"),

        --麦克风按钮类型变化
        --ButtonType EGVoiceButtonType 按钮类型
        evtMicrophoneButtonTypeChange = LuaEvent:NewIns("evtMicrophoneButtonTypeChange"),

        --麦克风硬件是否打开
        --bOpen：是否开麦
        evtMicrophoneDeviceChange = LuaEvent:NewIns("evtMicrophoneDeviceChange"),

        --扬声器按钮类型变化
        --ButtonType EGVoiceButtonType 按钮类型
        evtSpeakerButtonTypeChange = LuaEvent:NewIns("evtSpeakerButtonTypeChange"),

        --实时录音转文字完成的回调事件
        --Text：转换后的文字内容
        evtGVoiceStreamSpeechToText = LuaEvent:NewIns("evtGVoiceStreamSpeechToText"),

        --举报玩家后的结果
        --Code 返回错误码 GCloudVoiceCompleteCode
        --Info 举报信息
        evtGVoiceReportPlayer = LuaEvent:NewIns("evtGVoiceReportPlayer"),

        --设备变化事件
        --Code 返回错误码 GCloudVoiceCompleteCode
        --DeviceType 硬件类型 硬件类型 参考C++ gcloud_voice::DeviceType
        --DeviceId 硬件Id
        evtOnGVoiceDevice = LuaEvent:NewIns("evtOnGVoiceDevice"),

        --获取设备数量事件
        --DeviceType 硬件类型 参考gcloud_voice::DeviceType
        --DeviceCount 硬件数量
        evtOnGVoiceGetDeviceCount = LuaEvent:NewIns("evtOnGVoiceGetDeviceCount"),

        --按T说话广播事件
        --bPress 是否按T
        evtOnButtonMicroPressed = LuaEvent:NewIns("evtOnButtonMicroPressed"),

        --文本翻译时间
        evtOnTextTranslated = LuaEvent:NewIns("evtOnTextTranslated"),

        --切换前后台的事件
        --bActive：true-前台 false-后台
        evtOnGVoiceApplicationActive = LuaEvent:NewIns("evtOnGVoiceApplicationActive"),

        --点击全员静音
        evtOnMuteAllMember = LuaEvent:NewIns("evtOnMuteAllMember"),
        -- DFHD Start
        -- DFHD End
    },

    --[[ presetChatTable = {
        [0] = Facade.TableManager:GetTable("PresetChat"),
        -- [1] = Facade.TableManager:GetTable("PresetChat"),
        [1] = Facade.TableManager:GetTable("PresetChatSOL"),
        [2] = Facade.TableManager:GetTable("PresetChat"),
        -- [3] = Facade.TableManager:GetTable("PresetChat"),
        -- [4] = Facade.TableManager:GetTable("PresetChat"),
        -- [5] = Facade.TableManager:GetTable("PresetChat"),

        [3] = Facade.TableManager:GetTable("PresetChatRaid"),
        [4] = Facade.TableManager:GetTable("PresetChatBattle"),
        [5] = Facade.TableManager:GetTable("PresetChat"),
    },
    presetChatFunctionTable = Facade.TableManager:GetTable("PresetChatFunction"), ]]
    -- ConvenientMsgConfig = {
    --     msg1 = {content = "前方有危险", fCallbackName = "MarkCoord", param = nil},
    --     msg2 = {content = "我这里有物资", fCallbackName = "MarkItem", param = nil},
    --     msg3 = {content = "血量过低，我需要恢复一下", fCallbackName = nil, param = nil},
    --     msg4 = {content = "弹药不足", fCallbackName = nil, param = nil},
    --     msg5 = {content = "身受重伤，药品不足，需要支援!血量过低，我需要恢复一下", fCallbackName = nil, param = nil},
    --     msg6 = {content = "集合，准备作战", fCallbackName = nil, param = nil},
    --     msg7 = {content = "我老司机，跟我走！", fCallbackName = nil, param = nil},
    --     msg8 = {content = "请打开麦克风交流", fCallbackName = nil, param = nil},
    --     msg9 = {content = "我准备撤离了", fCallbackName = nil, param = nil},
    --     msg10 = {content = "发现男团", fCallbackName = nil, param = nil},
    --     msg11 = {content = "体力不足，等等我！", fCallbackName = nil, param = nil},
    -- },
    MicrophoneButtonImage = {
        [EGVoiceButtonType.Close] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_02.Common_ConstrollerFunction_Btn_02'",
        [EGVoiceButtonType.Team] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_08.Common_ConstrollerFunction_Btn_08'",
        [EGVoiceButtonType.All] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_01.Common_ConstrollerFunction_Btn_01'",
        [EGVoiceButtonType.Camp] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_09.Common_ConstrollerFunction_Btn_09'",
    },

    MicrophonePressButtonImage = {
        [EGVoiceButtonType.ClosePress] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_13.Common_ConstrollerFunction_Btn_13'",
        [EGVoiceButtonType.TeamPress] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_11.Common_ConstrollerFunction_Btn_11'",
        [EGVoiceButtonType.AllPress] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_05.Common_ConstrollerFunction_Btn_05'",
        [EGVoiceButtonType.CampPress] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_12.Common_ConstrollerFunction_Btn_12'",
    },

    SpeakerButtonImage = {
        [EGVoiceButtonType.Close] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_04.Common_ConstrollerFunction_Btn_04'",
        [EGVoiceButtonType.Team] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_06.Common_ConstrollerFunction_Btn_06'",
        [EGVoiceButtonType.All] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_03.Common_ConstrollerFunction_Btn_03'",
        [EGVoiceButtonType.Camp] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ConstrollerFunction_Btn_07.Common_ConstrollerFunction_Btn_07'",
    },

    Loc = {
        NoGVoiceMgr = NSLOCTEXT("GVoiceModule", "Lua_GVoice_NoGVoiceMgr", "获取GVoice控件失败"),

        AcceptTxt = NSLOCTEXT("GVoiceModule", "Lua_GVoice_AcceptTxt", "已接受"),
        RejectTxt = NSLOCTEXT("GVoiceModule", "Lua_GVoice_RejectTxt", "已拒绝"),

        AccountGVoicePunishWindow = NSLOCTEXT("GVoiceModule", "Lua_GVoice_AccountGVoicePunishWindow", "系统检测到你的语音内容违规，已对您做出禁止麦克风功能的处罚，处罚结束时间：%s"),
        AccountGVoicePunishTips = NSLOCTEXT("GVoiceModule", "Lua_GVoice_AccountGVoicePunishTips", "已被禁止使用麦克风功能"),

        GVoiceChangeMicType_Close_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicType_Close_HD", "切换至麦克风禁用状态"),
        GVoiceChangeMicType_Open_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicType_Open_HD", "切换至麦克风常开状态"),
        GVoiceChangeMicType_Press_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicType_Press_HD", "切换至按住说话状态"),

        GVoiceChangeMicChannel_Close_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicChannel_Close_HD", "发言频道已切换至关闭"),
        GVoiceChangeMicChannel_Team_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicChannel_Team_HD", "发言频道已切换至组队"),
        GVoiceChangeMicChannel_All_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicChannel_All_HD", "发言频道已切换至全队"),
        GVoiceChangeMicChannel_Camp_HD = NSLOCTEXT("GVoiceModule", "GVoiceChangeMicChannel_Camp_HD", "发言频道已切换至指挥官"),

        GVoiceButtonText_Close = NSLOCTEXT("GVoiceModule", "GVoiceButtonText_Close", "关闭"),
        GVoiceButtonText_Team = NSLOCTEXT("GVoiceModule", "GVoiceButtonText_Team", "组队"),
        GVoiceButtonText_All = NSLOCTEXT("GVoiceModule", "GVoiceButtonText_All", "全队"),
        GVoiceButtonText_Camp = NSLOCTEXT("GVoiceModule", "GVoiceButtonText_Camp", "阵营"),

        GVoiceTeamIdentity_TeamMember = NSLOCTEXT("GVoiceModule", "GVoiceTeamIdentity_TeamMember", "队员"),
        GVoiceTeamIdentity_TeamLeader = NSLOCTEXT("GVoiceModule", "GVoiceTeamIdentity_TeamLeader", "队长"),
        GVoiceTeamIdentity_Commander = NSLOCTEXT("GVoiceModule", "GVoiceTeamIdentity_Commander", "指挥官"),
        GVoiceTeamIdentity_TeamPreName = NSLOCTEXT("GVoiceModule", "GVoiceTeamIdentity_TeamPreName", "小队"),

        OnToggleMuteAll_Mute_HD = NSLOCTEXT("GVoiceModule", "OnToggleMuteAll_Mute_HD", "队友已被静音"),
        OnToggleMuteAll_Not_Mute_HD = NSLOCTEXT("GVoiceModule", "OnToggleMuteAll_Not_Mute_HD", "解除队友静音"),

        MicrophoneNotAvailable = NSLOCTEXT("GVoiceModule", "MicrophoneNotAvailable", "麦克风不可用"),

        ReportPlayerSuc = NSLOCTEXT("GVoiceModule", "ReportPlayerSuc", "举报成功"),
        ReportPlayerFail = NSLOCTEXT("GVoiceModule", "ReportPlayerFail", "举报失败"),
        ReportPlayerNoThing = NSLOCTEXT("GVoiceModule", "ReportPlayerNoThing", "未发现最近有来自该玩家的语音消息，无法进行举报"),
        ReportPlayerOften = NSLOCTEXT("GVoiceModule", "ReportPlayerOften", "近30s内语音信息已经进行举报，请勿反复操作"),

        ReportPlayerResultSuc = NSLOCTEXT("GVoiceModule", "ReportPlayerResultSuc", "举报语音成功"),
        ReportPlayerResultFail = NSLOCTEXT("GVoiceModule", "ReportPlayerResultFail", "举报语音失败"),

        MicComplianceTitle = NSLOCTEXT("GVoiceModule", "MicComplianceTitle", "ENABLING SOCIAL FEATURES"),
        MicCompliance =  NSLOCTEXT("GVoiceModule","MicCompliance","Would you like to enable voice/text chat?\nEnabling voice chat will access your microphone.Do NOT to share any personal information with other users in this chat window (for example your name,contact number,payment information or other information).\nRemember to be nice to each other when using chats and observe the <dfmrichtext style=\"HyperlinkTask\" link=\"https://www.playdeltaforce.com/terms-of-use.html\">Player Conduct</>."),
        MicComplianceDecline = NSLOCTEXT("GVoiceModule", "MicComplianceDecline", "拒绝"),
        MicComplianceAgree = NSLOCTEXT("GVoiceModule", "MicComplianceAgree", "同意"),

        MicrophonePanelVolumeDesc = NSLOCTEXT("GVoiceModule", "MicrophonePanelVolumeDesc", "麦克风音量"),

        SpeakerChannelChangeTips = NSLOCTEXT("GVoiceModule", "SpeakerChannelChangeTips", "收听频道范围无法小于发言频道"),
    },

    GCloudVoiceErrno = {
        GCLOUD_VOICE_SUCC               = 0,

        GCLOUD_VOICE_NOTHING_TO_REPORT  = 0x300c,   -- 12300, no sound to report

    },

    GCloudVoiceCompleteCode = {
        -- common code
        GV_ON_OK						  = 0x1000,    -- 4096, ok. 

        -- voice report
        GV_ON_REPORT_SUCC                 = 0x6001,    -- 24577，report other player succ
        GV_ON_DATA_ERROR                  = 0x6002,    -- 24578，receive illegal or invalid data from serve
        GV_ON_PUNISHED                    = 0x6003,    -- 24579，the player is punished because of being reported
        GV_ON_NOT_PUNISHED                = 0x6004,    -- 24580，the player
        GV_ON_KEY_DELECTED                = 0x6005,
        GV_ON_REPORT_SUCC_SELF            = 0x6006,

        GV_ON_DEVICE_EVENT_ADD            = 0x8101,    --33025,device event notify
		GV_ON_DEVICE_EVENT_UNUSABLE       = 0x8101,    --33026,device unusable event
		GV_ON_DEVICE_EVENT_DEFAULTCHANGE  = 0x8103,    --33027,default event changed
    }
}

return GVoiceConfig
