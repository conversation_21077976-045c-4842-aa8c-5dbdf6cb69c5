----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaFUIManagerIns)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class UIManager : ManagerBase
local UIManager = {}
local AnalysisUtil = require "DFM.YxFramework.Util.AnalysisUtil"
local UIDebugUtil = require "DFM.YxFramework.Managers.UI.Util.UIDebugUtil"
local UDFMGameGPM = import "DFMGameGPM"
local UIInsBundle = require "DFM.YxFramework.Managers.UI.Part.Data.UIInsBundle"
local ResScalingUtil = require"DFM.YxFramework.Managers.Resource.Util.ResScalingUtil"
local PlatformLogic = require "DFM.YxFramework.Managers.UI.Platform.PlatformLogic"
local UIReleaseUtil = require "DFM.YxFramework.Managers.UI.Util.UIReleaseUtil"

local fRegisterCurrentCreationSetting = import("LuaUIBaseView").RegisterCurrentCreationSetting
local DefaultStackPoolLength = 3
local DefaultUIPoolLRUTime = 180
local DefaultUIPoolLength = 2
local DefaultUICheckDuration = 1800
local DefaultUICheckDurationLow = 1200

local log = function(...)
    -- loginfo('[ UIManager ui复用打点 ] ',...)
end

local logw = function(...)
    logwarning('[ UIManager ui复用打点 ] ',...)
end

---------------------------------------------------------------------------------
--- UIManager关于UMG 对象的复用逻辑拆分
---
--- *UITable开启复用配置:
---     ReConfig = {
---         IsPoolEnable = true,
---         MaxPoolLength = 1;
---     }
---
--- *UI复用类型分为两种：
---     ERePoolType.Unique:唯一缓存，不同UINavID共享一个缓存Map，常见于栈UI
---     ERePoolType.Multiple:多个缓存，同一UINavID独立自己的缓存列表
---
--- *统一入口生命周期
---     _OnLuaUIBaseViewCreated：CreateUIObjectByAsset通知UIManager此生命周期，可打印轮回次数
---     _OnLuaUIBaseViewPaused：_DoRecycleUIIns先触发Deactivate，再通知UIManager此生命周期
---     _OnLuaUIBaseViewClosed：FinalCloseUI先触发OnClose，再通知UIManager此生命周期
---     _OnLuaUIBaseViewMuted: Destruct触发通知UIManager此生命周期，用于不可控制的UI销毁后移出复用池
---
--- *新增创建和关闭逻辑转发到复用：
---     CreateUIObjectByAsset内部分为FinalCreateUI和从复用缓存池里取两种类型，
---         _DoReuseUIIns会触发UI的复用
---         Activate对应Ctor
---
---     各LayerController新增CloseOrRecycle，转发至CloseUIObjectByIns，可能触发FinalCloseUI或回收, 
---         需要根据层级对应的ERePoolType和获取ReConfig中的配置，进行相应的复用池创建和存储，检查上限
---         _DoRecycleUIIns会触发UI的回收
---         Deactivate对应OnClose
---------------------------------------------------------------------------------
function UIManager:Ctor()
    self.bRePoolEnable = true
    --- 单id唯一实例对象池
    self._deUniqueUIList = {}
    --- 单id多个实例对象池
    self._deMapUINavID2UIBundle = {}
    self._deUIBundleOrderList = setmetatable({}, weakmeta_value)
    self._deSubUIBundleOrderList = setmetatable({}, weakmeta_value)

    self._stackPoolLength = DefaultStackPoolLength
    self._stackPoolLengthLow = DefaultStackPoolLength
    self._stackLruDuration = DefaultUIPoolLRUTime
    self._stackLruDurationLow = DefaultUIPoolLRUTime

    self:InitCacheConfig()
    self:InitStubCheckTimer()
end

function UIManager:InitCacheConfig()
    if PLATFORM_IOS then
        self._apmLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        log('GetDeviceLevelByQcc PLATFORM_IOS', self._apmLevel)
    elseif PLATFORM_ANDROID then
        self._apmLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        log('GetDeviceLevelByQcc PLATFORM_ANDROID', self._apmLevel)
    elseif PLATFORM_OPENHARMONY then
        self._apmLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        log('GetDeviceLevelByQcc PLATFORM_OPENHARMONY', self._apmLevel)
    else
        self._apmLevel = 4
        log('GetDeviceLevelByQcc PLATFORM_PC', self._apmLevel)
    end
    local config = MapApmLv2PerformanceConfig[self._apmLevel]
    self._stackPoolLength = config and config.stackPoolLength or DefaultStackPoolLength
    self._stackPoolLengthLow = config and config.stackPoolLengthLow or DefaultStackPoolLength

    self._stackLruDuration = config and config.stackLruDuration or DefaultUIPoolLRUTime
    self._stackLruDurationLow = config and config.stackLruDurationLow or DefaultUIPoolLRUTime

    self._stubSizeRuntimeMb = config and config.stubSizeRuntimeMb or math.huge
    self._stubSizeImageMb = config and config.stubSizeImageMb or math.huge

    if config then
        logw('当前设备等级名称', config.performanceLv)
    end
end

function UIManager:InitStubCheckTimer()
    if self._runtimeCleanHandle then
        Timer.CancelDelay(self._runtimeCleanHandle)
        self._runtimeCleanHandle = nil
    end

    local checkDuration
    if self._bLowMemoryCVarState then
        checkDuration = DefaultUICheckDurationLow or math.huge
    else
        checkDuration = DefaultUICheckDuration or math.huge
    end
    self._runtimeCleanHandle = Timer.DelayCall(checkDuration, self._OnCheckResStubSize, self)
end

function UIManager:_OnCheckResStubSize()
    local curGameFlowType = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlowType ~= EGameFlowStageType.Game then
        local curByteSize = self.stubRuntimeUIRes:GetAllResSize()
        local mbSize = ResScalingUtil.ConvertMbFromByte(curByteSize)
        if mbSize >= self._stubSizeRuntimeMb then
            logw('[ SHE1 StubResChecker - RuntimeStub 资源已达上限，开始清理]-- ', mbSize, '/', self._stubSizeRuntimeMb)
            self.stubRuntimeUIRes:ClearAllResRefs()
        else
            logw('[ SHE1 StubResChecker - RuntimeStub 资源未达上限，继续关注]-- ', mbSize, '/', self._stubSizeRuntimeMb)
        end

        local curImageByteSize = self.stubUIImageRes:GetAllResSize()
        local mbSize = ResScalingUtil.ConvertMbFromByte(curImageByteSize)
        if mbSize >= self._stubSizeImageMb then
            logw('[ SHE1 StubResChecker - UIImageStub 资源已达上限，开始清理]-- ', mbSize, '/', self._stubSizeImageMb)
            self.stubUIImageRes:ClearAllResRefs()
        else
            logw('[ SHE1 StubResChecker - UIImageStub 资源未达上限，继续关注]-- ', mbSize, '/', self._stubSizeImageMb)
        end
    end
    self:InitStubCheckTimer()
end

local function recursiveGetAsyncSubUIList(UINavID, allSubUINavIDList)
    allSubUINavIDList = allSubUINavIDList or {}

    local uiSettings = UITable[UINavID]
    if uiSettings ~= DummyUISettings then
        -- assertlog(uiSettings,"recursiveGetAsyncSubUIList load ui asset failed, UISettings is nil, Please check UITable, UINavID:"..UINavID)
        if uiSettings then
            if uiSettings.UILayer == EUILayer.Sub then
                table.insert(allSubUINavIDList, UINavID)
            end
            if uiSettings.SubUIs then
                for _, subUIId in ipairs(uiSettings.SubUIs) do
                    recursiveGetAsyncSubUIList(subUIId, allSubUINavIDList)
                end
            end
        else
            assertlog(uiSettings,"recursiveGetAsyncSubUIList load ui asset failed, UISettings is nil, Please check UITable, UINavID:"..UINavID)
            logerror('反向依赖了尚未初始化的模块的UI，UISettings is nil 请检查', UIName2ID.GetNameByID(UINavID), debug.traceback())
        end
    end
    return allSubUINavIDList
end

--------------------------------------------------------------------------
--- UIManager Private API: 
--- UI实例 [交付层级管理器的入口] 通用Internal 对象创建、关闭流程
--------------------------------------------------------------------------
function UIManager:_InternalAsyncShowUI(UINavID, fLoadFinCallback, caller, ...)
    UIDebugUtil.RecordAsyncShowUIBeginClock(UINavID)
    if AnalysisUtil.ShouldTraceUI(UINavID) then
        ---@type UISettings
        local uiSettings = UITable[UINavID]
        if uiSettings then
            local cls = require(uiSettings.LuaPath)
            AnalysisUtil.SetCurrentUISession(cls._cname)
            AnalysisUtil.StartUITotalTime(UINavID)
        end
    end

    -- Check is valid world
    if not IsCurrentWorldValid() then return nil end

    --- LONG3 资源计数加载期间修复
    local loadingGid = UIUtil.GetAsyncGid()
    local allSubUINavIDList = {}
    table.insert(allSubUINavIDList, UINavID)
    allSubUINavIDList = recursiveGetAsyncSubUIList(UINavID, allSubUINavIDList)
    self:OnResPathsStartAsyncLoading(allSubUINavIDList, loadingGid)

    local fOnLoadFinCallback = CreateCallBack(function(self, uiIns)
        --- UIIns 已经创建好（非Sub类型的UI此时已经加入根节点）
        self:_OnLuaUIBaseViewCreated(uiIns)

        --- UIIns 注册彻底关闭回调
        local uiSettings = UITable[UINavID]
        if uiSettings then
            local layerType = uiSettings.UILayer
            if layerType ~= EUILayer.Sub then
                uiIns:AddCloseCallBack(self._OnLuaUIBaseViewMuted, self)
            end
        end
        --- UIIns 调用创建完成的业务回调
        safecall(fLoadFinCallback, caller, uiIns)

        --- UIIns UIDebugUtil记录时长log
        UIDebugUtil.RecordAsyncShowUIEndClock(UINavID)
        if AnalysisUtil.ShouldTraceUI(UINavID) then
            AnalysisUtil.StopUIOpenTime(UINavID)
            AnalysisUtil.StopUITotalTime(UINavID)
            AnalysisUtil.ResetCurrentUISession()
        end

        self:OnResPathsFinishAsyncLoading(loadingGid)
        --- SHE3 异步资源使用完后低内存移除
        self:TryLayerTypeClearResRef({UINavID}, self.stubRuntimeUIRes)
    end, self)

    local uiHandle
    --- [UI入口] 创建开始
    local runningHandle = SingleUIHandle.TryGetRunningHandleByKey(UINavID)
    if runningHandle then
        uiHandle = runningHandle
        uiHandle:AddLoadFinCallback(fOnLoadFinCallback, nil)
    else
        ---@type SingleUIHandle
        uiHandle = SingleUIHandle:NewIns(UINavID, fOnLoadFinCallback, nil, ...)
        uiHandle:AsyncLoadRes(false, false)
    end

    return uiHandle
end

--- 关闭流程的入口，交付各层级管理器处理
function UIManager:_InternalCloseUI(uiIns)
    local uiSettings = uiIns:GetUISettings()
    if uiSettings then
        local layerType = uiSettings.UILayer
        local layerController = self.mapLayer2Controller[layerType]
        --- [UI入口] 关闭开始
        if layerController then
            if UILayer2ControllerPath[layerType] == nil then
                layerController:RemoveUIByLayerType(uiIns, layerType)
            else
                if layerType == EUILayer.Stack then
                    --- long1修改 关闭时指定对象
                    --- 修复错误关闭大厅导致栈UI全空的情况
                    local curUINavID = layerController:GetCurrentViewId()
                    if curUINavID ~= nil and curUINavID == uiSettings.UINavID then
                        layerController:PopUI()
                    else
                        if uiSettings.UINavID and curUINavID then
                            logwarning("#[ 统一根节点RootUI Debug ]------------------------ PopStack but uiNavID isnt match:  ui need to close Name:", UIName2ID.GetNameByID(uiSettings.UINavID), '~= cur stack view Name:', UIName2ID.GetNameByID(curUINavID))
                        else
                            logwarning("#[ 统一根节点RootUI Debug ]------------------------ PopStack uiSettings.UINavID or curUINavID is nil:  ui need to close ID:", uiSettings.UINavID, '  cur stack view ID:', curUINavID)
                        end
                    end
                else
                    layerController:RemoveUI(uiIns)
                end
            end
        end
    else
        logwarning("UISettings is nil:", uiIns, UIName2ID.GetNameByID(uiIns.UINavID))
    end
end


--------------------------------------------------------------------------
--- UIManager Public API 打开、关闭UI面板: 
--- 4.栈LuaUI批量操作
--------------------------------------------------------------------------
---@param UICacheList table<i,CacheInfo>
---@param fLoadFinCallback function
---@param caller table
function UIManager:BatchShowStackUI(UICacheList, bInitialHide, fLoadFinCallback, caller, bNeedTransition)
    bNeedTransition = setdefault(bNeedTransition, false)

    --- SHE1 资源计数加载期间修复
    local loadingGid = UIUtil.GetAsyncGid()
    local batchAllSubUINavIDList = {}
    local stackUINavIDList = {}
    for i, cacheInfo in ipairs(UICacheList) do
        local uiNavID = cacheInfo.UINavID
        if uiNavID then
            local uiSettings = UITable[uiNavID]
            assertlog(uiSettings)
            local allSubUINavIDList = {}
            table.insert(allSubUINavIDList, uiNavID)
            allSubUINavIDList = recursiveGetAsyncSubUIList(uiNavID, allSubUINavIDList)
            table.append(batchAllSubUINavIDList, allSubUINavIDList)
            --- 单独记录栈UI的NavID
            table.insert(stackUINavIDList, uiNavID)
        end
    end
    self:OnResPathsStartAsyncLoading(batchAllSubUINavIDList, loadingGid)

    local fOnLoadFinCallback = CreateCallBack(function(self, uiIns)
        self:EnableInput(EInputChangeReason.BatchOperating)
        if bNeedTransition then
            self:CommitTransition(false, nil, nil, ETransitionChangeReason.BatchOperating)
        end
        safecall(fLoadFinCallback, caller, uiIns)

        self:OnResPathsFinishAsyncLoading(loadingGid)
        --- SHE3 异步资源使用完后低内存移除
        self:TryLayerTypeClearResRef(stackUINavIDList, self.stubRuntimeUIRes)
    end, self)

    local uiBatchHandle
    self:DisableInput(EInputChangeReason.BatchOperating)
    if bNeedTransition then
        self:CommitTransition(true, nil, nil, ETransitionChangeReason.BatchOperating)
    end
    local runningHandle = BatchUIHandle.TryGetRunningHandleByKey(UICacheList)
    if runningHandle then
        uiBatchHandle = runningHandle
        uiBatchHandle:AddLoadFinCallback(fOnLoadFinCallback, nil)
    else
        local uiBatchHandle = BatchUIHandle:NewIns(UICacheList, fOnLoadFinCallback, nil)
        uiBatchHandle:AsyncLoadRes(false, bInitialHide)
    end
    
    return uiBatchHandle

    --- 单独Handle的版本
    -- local uiCount = #UICacheList
    -- local batchHandleList = {}
    -- assertlog(uiCount > 0)
    -- for i, CacheInfo in ipairs(UICacheList) do
    --     local UINavID = CacheInfo.UINavID
    --     local params = CacheInfo.params
    --     if i < uiCount then
    --         local uiHandle = SingleUIHandle:NewIns(UINavID, nil, nil, params)
    --         uiHandle:AsyncLoadSilenceUI()
    --         table.insert(batchHandleList, uiHandle)
    --     else
    --         local uiHandle = SingleUIHandle:NewIns(UINavID, nil, nil, params)
    --         uiHandle:AsyncLoadRes(false, bInitialHide)
    --         table.insert(batchHandleList, uiHandle)
    --     end
    -- end
    -- return batchHandleList
end

--------------------------------------------------------------------------
--- UIManager CheckLoading
--------------------------------------------------------------------------
function UIManager:CheckUIIsLoading(UINavID)
    local runningHandle = SingleUIHandle.TryGetRunningHandleByKey(UINavID)
    return runningHandle and runningHandle:CheckIsPendingCreate() or false
end

function UIManager:CheckAnyUIIsLoadingByLayerTypes(typeList)
    local keys = HandleBase.GetRunningKeys()
    local navKeys = {}
    for _, key in ipairs(keys) do
        if type(key) == "table" then
            -- batch
            for _, info in ipairs(key) do
                if info.UINavID then
                    table.insert(navKeys, info.UINavID)
                end
            end
        else
            table.insert(navKeys, key)
        end
    end
    for _, key in ipairs(navKeys) do
        local uiSettings = UITable[key]
        if uiSettings then
            local uiLayer = uiSettings.UILayer
            for _, typ in ipairs(typeList) do
                if uiLayer == typ then
                    return true
                end
            end
        end
    end
    return false
end

function UIManager:CheckAnyBlockUIIsLoading()
    return self:CheckAnyUIIsLoadingByLayerTypes({EUILayer.Stack, EUILayer.Pop})
end

--------------------------------------------------------------------------
--- UIManager Private API: 
--- UI实例 [操作入口与对象池] 被层级管理器和UIHandle调用 对象创建流程
--------------------------------------------------------------------------
--- UIHandle 通过加载好的Asset创建UserWidgetIns接口
function UIManager:CreateUIObjectByAsset(uiNavId, uiResIns)
    local uiIns, deUIList = self:TryGetCacheUIInsByNavId(uiNavId)
    local bReused = false
    if is_ui_invalid(uiIns) then
        uiIns = self:FinalCreateUI(uiNavId, uiResIns)
        if uiIns == nil then
            logerror('CreateUIObjectByAsset FinalCreateUI failed, uiIns is nil', UIName2ID.GetNameByID(uiNavId), debug.traceback())
            return nil, false
        end
        --- 首次生命周期
        if uiIns.OnResInitialized then
            trycall(uiIns.OnResInitialized, uiIns)
        end
        uiIns.lifeTimes = 1
        --- 必要Log
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            local layerType = uiSettings.UILayer
            if layerType == EUILayer.Stack then
                log('CreateUIObjectByAsset [新建] ', UIName2ID.GetBPFullPathByID(uiNavId), uiIns)
            end
        end
    else
        --- 已出栈的非激活UI
        if uiIns.Activate then
            if not rawget(uiIns, "_is_enable_") then
                bReused = self:_DoReuseUIIns(uiIns, deUIList)

                local uiSettings = uiIns:GetUISettings()
                if uiSettings then
                    local layerType = uiSettings.UILayer
                    if layerType == EUILayer.Stack then
                        log('CreateUIObjectByAsset [复用] ', UIName2ID.GetBPFullPathByID(uiNavId), uiIns)
                    end
                end
            --- 栈内的激活UI
            else
                ---直接用，不属于复用
                local uiSettings = uiIns:GetUISettings()
                if uiSettings then
                    local layerType = uiSettings.UILayer
                    if layerType == EUILayer.Stack then
                        log('CreateUIObjectByAsset [栈内调整ZOrder] ', UIName2ID.GetBPFullPathByID(uiNavId), uiIns)
                    end
                end
            end
        end
    end
    return uiIns, bReused
end

function UIManager:_DoReuseUIIns(uiIns, deUIList)
    if uiIns ~= nil and isvalid(uiIns) and uiIns.__cppinst ~= nil then
        local uiSettings = UITable[uiIns.UINavID]
        if uiSettings then
            if deUIList and #deUIList > 0 then
                if deUIList[#deUIList] == uiIns then
                    deUIList[#deUIList] = nil
                else
                    table.removebyvalue(deUIList, uiIns)
                end
            end
            trycall(uiIns.Activate, uiIns)
            declare_if_nil(uiIns, "lifeTimes", 1)
            uiIns.lifeTimes = uiIns.lifeTimes + 1
            log('_DoReuseUIIns bReused!!!!', true, uiIns._cname, '是否active:', rawget(uiIns, "_is_enable_"))
            return true
        end
    end
    return false
end

function UIManager:FinalCreateUI(uiNavId, uiResIns)
    local uiSettings = UITable[uiNavId]
    assertlog(uiSettings, 'FinalCreateUI UISettings is nil, please check', UIName2ID.GetNameByID(uiNavId))
    if uiResIns == nil then
        logerror(uiResIns, 'fail to load ui bp asset:', uiSettings.BPPath)
        if uiSettings.ResSplitConfig ~= ESplitConfig.Common and uiSettings.CommonPlanBConfig then
            trycall(UIName2ID.RevertToPlanB, uiNavId)
        end
        return
    end
    
    -- self.cacheUISettings = uiSettings
    fRegisterCurrentCreationSetting(uiSettings.BPClassName, uiSettings)
    local uiIns
    if AnalysisUtil.ShouldTraceUI(uiNavId) then
        AnalysisUtil.StartUICreateTime(uiNavId)
        uiIns = Facade.LuaCreateUIManager:CreateUIById(uiNavId, uiResIns)
        AnalysisUtil.StopUICreateTime(uiNavId)
    else
        uiIns = Facade.LuaCreateUIManager:CreateUIById(uiNavId, uiResIns)
    end

    if type(uiIns) == GlobalStr.USERDATA then
        -- TODO Get Lua Class && Create it
        -- uiIns = Ctor(uiSettings.LuaPath, uiIns)
        if not uiIns.bIsHud then
            assertlog(
                issubclass(LAI.GetObjectClass(uiIns), ULuaUIBaseView),
                "[HUD 统一接入 Debug] The base class of frontend view should be [ULuaUIBaseView]!",
                uiIns
            )
            -- local ClassStr = LAI.GetObjectClassName(uiIns)
            uiIns = LuaUIBaseView:NewOn(uiIns)
        else
            assertlog(
                issubclass(LAI.GetObjectClass(uiIns), ULuaUIHudBaseView),
                "[HUD 统一接入 Debug] The base class of frontend view should be [ULuaUIHudBaseView]!",
                uiIns
            )
            -- local ClassStr = LAI.GetObjectClassName(uiIns)
            uiIns = LuaUIHUDBaseView:NewOn(uiIns)
        end
    end

    -- self.cacheUISettings = nil
    fRegisterCurrentCreationSetting(nil, nil)
    if uiIns then
        uiIns:InitUISettings(uiNavId)
        if uiIns._cname ~= "LuaUIBaseView" then
            --- 递归绑定子UIHistroy
            self:HandleBindHistory(true, uiIns, uiNavId)

            --- 初始化AliveCount
            local aliveCount = self:InitAliveCount(uiNavId, uiIns.UITokenID)
            if ALIVE_COUNT_CLEAR_SUBUI_LOG then
                if not VersionUtil.IsShipping() then
                    loginfo('[Low Memory Log - AliveCount]-----------InitAliveCount FinalCreateUI', uiIns, uiIns._cname,'当前aliveCount：', aliveCount, uiIns.UITokenID)
                end
            end
        else
            loginfo('[Low Memory Log - AliveCount]-----------InitAliveCount FinalCreateUI(LuaUIBaseView)', uiIns, uiNavId, uiResIns)
        end
        return uiIns
    else
        logerror("fail to create ui object:" .. uiSettings.BPPath)
        return nil
    end
end

function UIManager:HandleBindHistory(bBind, uiIns, uiNavId)
    local uiSettings = UITable[uiNavId]
    local function fRecursiveHistoryProcess(operaUINavId)
        local uiSettings = UITable[operaUINavId]
        if uiSettings and uiSettings.SubUIs then
            for _, operaSubUINavId in ipairs(uiSettings.SubUIs) do
                if bBind then
                    Facade.UIManager:BindOwnerSubHistory(uiIns, operaSubUINavId)
                    -- if ALIVE_COUNT_CLEAR_SUBUI_LOG then
                    --     if not VersionUtil.IsShipping() then
                    --         logwarning('[Low Memory Log - AliveCount] HandleBindHistory 绑定子UIHistory', uiIns, operaSubUINavId)
                    --     end
                    -- end
                else
                    Facade.UIManager:UnbindOwnerSubHistory(uiIns, operaSubUINavId)
                    -- if ALIVE_COUNT_CLEAR_SUBUI_LOG then
                    --     if not VersionUtil.IsShipping() then
                    --         logwarning('[Low Memory Log - AliveCount] HandleBindHistory 解绑子UIHistory', uiIns, operaSubUINavId)
                    --     end
                    -- end
                end
                fRecursiveHistoryProcess(operaSubUINavId)
            end
        end
        return
    end
    if uiSettings and uiSettings.SubUIs then
        fRecursiveHistoryProcess(uiNavId)
    end
end

function UIManager:CheckPoolEnable(reConfig)
    --- Long1 低内存 - 缓存
    if self:GetIsLowMemoryState() then
        return self.bRePoolEnable and reConfig and reConfig.IsPoolEnableLow
    else
        return self.bRePoolEnable and reConfig and reConfig.IsPoolEnable
    end
end

--------------------------------------------------------------------------
--- UIManager Private API: 
--- UI实例 [操作入口与对象池] 被层级管理器调用 对象关闭流程
--------------------------------------------------------------------------
---@param uiIns any
---@param bImmediate boolean
---@return uiIns, bRecycle
function UIManager:CloseUIObjectByIns(uiIns, bImmediate)
    local bRecycle = false
    local bPoolAlreadyRecycled = false
    if not is_ui_invalid(uiIns) then
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            local layerType = uiSettings.UILayer
            local reConfig = uiSettings.ReConfig
            if self:CheckPoolEnable(reConfig) then
                if uiIns.Deactivate then
                    if rawget(uiIns, "_is_enable_") then
                        local reUIType = MapLayer2RePoolType[layerType]
                        if reUIType then
                            if reUIType == ERePoolType.Unique then
                                if layerType == EUILayer.Stack then
                                    local deUIList = self:TryGetDeUIList(uiIns.UINavID)
                                    local stackPoolLength = self._bLowMemoryCVarState and self._stackPoolLengthLow or self._stackPoolLength
                                    bRecycle = self:_DoRecycleUIIns(uiIns, bImmediate, deUIList, stackPoolLength)
                                end
                            elseif reUIType == ERePoolType.Multiple then
                                local deUIList = self:TryGetDeUIList(uiIns.UINavID)
                                bRecycle = self:_DoRecycleUIIns(uiIns, bImmediate, deUIList, reConfig.MaxPoolLength or DefaultUIPoolLength)
                            end
                        end
                    else
                        local reUIType = MapLayer2RePoolType[layerType]
                        if reUIType then
                            if reUIType == ERePoolType.Unique then
                                if layerType == EUILayer.Stack then
                                    local deUIList = self:TryGetDeUIList(uiIns.UINavID)
                                    if table.contains(deUIList, uiIns) then
                                        bPoolAlreadyRecycled = true
                                    end
                                end
                            elseif reUIType == ERePoolType.Multiple then
                                local deUIList = self:TryGetDeUIList(uiIns.UINavID)
                                if table.contains(deUIList, uiIns) then
                                    bPoolAlreadyRecycled = true
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    if not bRecycle and not bPoolAlreadyRecycled then
        self:FinalCloseUI(uiIns, bImmediate)
    end
    return uiIns, bRecycle
end

function UIManager:TryGetDeUIList(uiNavId)
    local uiSettings = UITable[uiNavId]
    if uiSettings ~= nil then
        local layerType = uiSettings.UILayer
        local reConfig = uiSettings.ReConfig
        if self:CheckPoolEnable(reConfig) then
            local reUIType = MapLayer2RePoolType[layerType]
            if reUIType then
                if reUIType == ERePoolType.Unique then
                    if layerType == EUILayer.Stack then
                        return self._deUniqueUIList
                    end
                elseif reUIType == ERePoolType.Multiple then
                    return self:GetDeMultipleUIList(uiNavId)
                end
            end
        end
    end
    return nil
end

function UIManager:_DoRecycleUIIns(uiIns, bImmediate, deUIList, maxPoolLength)
    if deUIList then
        trycall(uiIns.Deactivate, uiIns, bImmediate)
        table.insert(deUIList, uiIns)
        self:_OnLuaUIBaseViewPaused(uiIns)
        log('_DoRecycleUIIns bRecycled!!!!', true, uiIns._cname,  '是否active:', rawget(uiIns, "_is_enable_"))

        if deUIList and #deUIList > maxPoolLength then
            local oldestUIIns = deUIList[1]
            table.removebyvalue(deUIList, oldestUIIns)
            Facade.UIManager:FinalCloseUI(oldestUIIns, true)
        end
        return true
    else
        return false
    end
end

--- 没有对象池的关闭流程
function UIManager:FinalCloseUI(uiInLayer, bImmediate, bLuaPendingKill)
    bLuaPendingKill = setdefault(bLuaPendingKill, false)
    if uiInLayer and not hasdestroy(uiInLayer) and not (FINAL_CLOSE_CLEAR_REF and uiInLayer.bFinalClose) then
        self:_OnLuaUIBaseViewClosed(uiInLayer)

        --- 递归解绑子UIHistroy
        self:HandleBindHistory(false, uiInLayer, uiInLayer.UINavID)

        --- 标记为FinalClose
        if FINAL_CLOSE_CLEAR_REF then
            uiInLayer.bFinalClose = true
        end

        if bLuaPendingKill and LUA_PENDING_KILL_ENABLE then
            UIReleaseUtil.LuaPendingKillRelease(uiInLayer)
        end
        
        if uiInLayer.TryClose then
            uiInLayer:TryClose(bImmediate)
        end
    else
        if not VersionUtil.EnablePerformanceMode() then
            if uiInLayer and uiInLayer._cname and __DebugOnly_UIState then
                if (FINAL_CLOSE_CLEAR_REF and uiInLayer.bFinalClose) then
                    logwarning("[ LuaPendingKill ] UIManager:FinalCloseUI(uiInLayer, bImmediate) the ui needed to close already in bFinalClose Process:", uiInLayer, uiInLayer.UITokenID, uiInLayer._cname, __DebugOnly_UIState(uiInLayer._curUIState))
                else
                    logwarning("UIManager:FinalCloseUI(uiInLayer, bImmediate) the ui needed to close hasdestroy:", uiInLayer, uiInLayer.UITokenID, uiInLayer._cname, __DebugOnly_UIState(uiInLayer._curUIState))
                end
            else
                logwarning("UIManager:FinalCloseUI(uiInLayer, bImmediate) the ui needed to close is nil")
            end
        end
    end
end


function UIManager:FinalClearUInsById(uiNavID)
    local uiSettings = UITable[uiNavID]
    if uiSettings then
        local layerType = uiSettings.UILayer
        local reUIType = MapLayer2RePoolType[layerType]
        if reUIType then
            if reUIType == ERePoolType.Unique then
                if layerType == EUILayer.Stack then
                    local uiInsWeak = self:GetStackUIByUINavId(uiNavID, true)
                    local uiIns = getfromweak(uiInsWeak)
                    if uiIns then
                        self:FinalCloseUI(uiIns, true)
                    end
                end
            elseif reUIType == ERePoolType.Multiple then
                local deUIList = self:TryGetDeUIList(uiNavID)
                if deUIList then
                    for _, uiIns in ipairs(deUIList) do
                        if uiIns and not hasdestroy(uiIns) then
                            self:FinalCloseUI(uiIns, true)
                        end
                    end
                end
                deUIList = {}
            end
        end
    end
end
---------------------------------------------------------------------------------
--- UI实例 [操作入口与对象池] 被层级管理器和UIHandle调用 尝试获取依然存活的实例并复用
---------------------------------------------------------------------------------
function UIManager:TryGetCacheUIInsByNavId(uiNavId, layerType)
    local uiIns = nil
    local deUIList
    local uiSettings = UITable[uiNavId]
    if uiSettings then
        local layerType = uiSettings.UILayer
        local reUIType = MapLayer2RePoolType[layerType]
        if reUIType then
            if reUIType == ERePoolType.Unique then
                --- 注意，这里可能有栈内的已激活UI
                if layerType == EUILayer.Stack then
                    local uiInsWeak = self:GetStackUIByUINavId(uiNavId, true)
                    uiIns = getfromweak(uiInsWeak)
                    deUIList = self._deUniqueUIList
                end
            elseif reUIType == ERePoolType.Multiple then
                local reConfig = uiSettings.ReConfig
                if self:CheckPoolEnable(reConfig) then
                    if layerType == EUILayer.BackRoot then
                        local curBackRootView = self:GetBackRootUIByUINavId(uiNavId, true)
                        if curBackRootView and not hasdestroy(curBackRootView) then
                            uiIns = curBackRootView
                        end
                    end
                    deUIList = self:GetDeMultipleUIList(uiNavId)
                    if uiIns == nil and deUIList and #deUIList > 0 then
                        uiIns = deUIList[#deUIList]
                        log('栈包子 deUIList 从对象池中取出', uiIns._cname, ', 当前对象池长度:', #deUIList)
                    end
                end
            end
        end
    end
    return uiIns, deUIList
end

function UIManager:GetDeMultipleUIList(uiNavId)
    local uiSettings = UITable[uiNavId]
    local reConfig = uiSettings.ReConfig
    local deUIList
    if self:CheckPoolEnable(reConfig) then
        if self._deMapUINavID2UIBundle[uiNavId] == nil then
            local newUIBundle = UIInsBundle:NewIns(uiNavId, reConfig.MaxPoolLength or DefaultUIPoolLength)
            self._deMapUINavID2UIBundle[uiNavId] = newUIBundle

            local layerType = uiSettings.UILayer
            if not layerType == EUILayer.BackRoot or layerType == EUILayer.HUD or not PlatformLogic.IsForceAreaType(layerType) then
                if layerType == EUILayer.Sub then
                    table.insert(self._deSubUIBundleOrderList, newUIBundle)
                else
                    table.insert(self._deUIBundleOrderList, newUIBundle)
                end
            end
        end
        deUIList = self._deMapUINavID2UIBundle[uiNavId]:GetUIList()
    end
    return deUIList
end

function UIManager:ClearReuseUIList(uiNavId)
    if uiNavId == nil then return end
    local uiSettings = UITable[uiNavId]
    local reConfig = uiSettings.ReConfig

    if not self.bRePoolEnable then
        logwarning('[Low Memory Log - GameFlow Debug ] ******* Lua self.bRePoolEnable = false 当前对象池处理中，不可操作 -------------')
        return
    end
    if self:CheckPoolEnable(reConfig) then
        local deUIBundle = self._deMapUINavID2UIBundle[uiNavId]
        if deUIBundle then
            log('deUIBundle:Reset() 开启，清空这个UI对象池:', uiNavId, UIName2ID.GetBPPathByID(uiNavId)," 对象池清空个数:", #deUIBundle:GetUIList())
            if not hasdestroy(deUIBundle) then
                deUIBundle:Reset()
            else
                self._deMapUINavID2UIBundle[uiNavId] = nil
            end
        else
            log('deUIBundle:Reset() 开启，但未找到已经生效的对象池:', uiNavId)
        end
    else
        local deUIBundle = self._deMapUINavID2UIBundle[uiNavId]
        if deUIBundle then
            if not hasdestroy(deUIBundle) then
                deUIBundle:Release()
            end
            self._deMapUINavID2UIBundle[uiNavId] = nil
        end
        -- log('deUIBundle:Reset() 关闭，不存在对象池配置:', uiNavId)
    end
end
--------------------------------------------------------------------------
--- UIManager Private API: 
--- UI实例 [始终回调] UIManager感知UI对象创建、关闭的最后一步
--------------------------------------------------------------------------
---------------------------------------------------------------------------------
--- UI实例 对象被UIManager创建结束回调（已经加入层级管理）
---------------------------------------------------------------------------------
function UIManager:_OnLuaUIBaseViewCreated(uiIns)
    if uiIns then
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            local reConfig = uiSettings.ReConfig
            if uiSettings.UILayer == EUILayer.Stack then
                local stackPoolLength = self._bLowMemoryCVarState and self._stackPoolLengthLow or self._stackPoolLength
                log('_OnLuaUIBaseViewCreated', uiIns, uiIns._cname, '轮回次数：', uiIns.lifeTimes, '栈UI公共对象池最大长度', stackPoolLength)
            elseif reConfig then
                log('_OnLuaUIBaseViewCreated', uiIns, uiIns._cname, '轮回次数：', uiIns.lifeTimes, '对象池最大长度：', reConfig.MaxPoolLength or DefaultUIPoolLength)
            else
                -- log('_OnLuaUIBaseViewCreated', uiIns, uiIns._cname, '轮回次数：', uiIns.lifeTimes, '无对象池')
            end
        end
    end
    --- [UI入口] 创建结束
    -- ---@type UISettings
    -- local uiSettings = uiIns:GetUISettings()
    -- if uiSettings then
    --     local layerType = uiSettings.UILayer
    --     local layerController = self.mapLayer2Controller[layerType]
    -- end
end

---------------------------------------------------------------------------------
--- UI实例 对象被UIManager回收回调
---------------------------------------------------------------------------------
function UIManager:_OnLuaUIBaseViewPaused(uiIns)
    if uiIns then
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            local reConfig = uiSettings.ReConfig
            if uiSettings.UILayer == EUILayer.Stack then
                local stackPoolLength = self._bLowMemoryCVarState and self._stackPoolLengthLow or self._stackPoolLength
                log('_OnLuaUIBaseViewPaused', uiIns, uiIns._cname,'栈UI公共对象池最大长度：', stackPoolLength)
            elseif reConfig then
                log('_OnLuaUIBaseViewPaused', uiIns, uiIns._cname,'对象池最大长度：', reConfig.MaxPoolLength or DefaultUIPoolLength)
            else
                -- log('_OnLuaUIBaseViewPaused', uiIns, uiIns._cname,'无对象池')
            end
        end
    end
end

---------------------------------------------------------------------------------
--- UI实例 对象被UIManager关闭回调
---------------------------------------------------------------------------------
function UIManager:_OnLuaUIBaseViewClosed(uiIns)
    if uiIns then
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            local reConfig = uiSettings.ReConfig
            if uiSettings.UILayer == EUILayer.Stack then
                local stackPoolLength = self._bLowMemoryCVarState and self._stackPoolLengthLow or self._stackPoolLength
                log('_OnLuaUIBaseViewClosed', uiIns, uiIns._cname,'栈UI公共对象池最大长度', stackPoolLength)
            elseif reConfig then
                log('_OnLuaUIBaseViewClosed', uiIns, uiIns._cname,'对象池最大长度：', reConfig.MaxPoolLength or DefaultUIPoolLength)
            else
                -- log('_OnLuaUIBaseViewClosed', uiIns, uiIns._cname,'无对象池')
            end
            
            local layerType = uiSettings.UILayer
            --- 移除UI存活池记录
            if layerType
                and layerType ~= EUILayer.Sub
                and not PlatformLogic.IsForceAreaType(layerType)
                and not PlatformLogic.IsHUDAreaType(layerType) then

                local controller, bDefault = self:GetLayerControllerByType(layerType)
                if controller then
                    if not bDefault then
                        controller:InternalRemoveUI(uiIns)
                    else
                        controller:InternalRemoveUI(uiIns, layerType)
                    end
                    if not VersionUtil.IsShipping() then
                        logwarning("[Low Memory Log - AliveCount]  InternalRemoveUI 移除UI存活池记录", uiIns, uiIns._cname, uiIns.UITokenID)
                    end
                end
            end
        end
    end
end

---------------------------------------------------------------------------------
--- UI实例 对象UIManager主动销毁、其他原因被动销毁 Release入口（已经移除WidgetTree）
--- 注意这里包含UIManager以外的情况造成的关闭
---------------------------------------------------------------------------------
function UIManager:_OnLuaUIBaseViewMuted(uiIns)
    if uiIns then
        --- [UI入口] 关闭结束
        ---@type UISettings
        local uiSettings = uiIns:GetUISettings()
        if uiSettings then
            local layerType = uiSettings.UILayer
            local reConfig = uiSettings.ReConfig

            local aliveCount = self:GetAliveCount(uiIns.UINavID)
            if layerType == EUILayer.Stack then
                local stackPoolLength = self._bLowMemoryCVarState and self._stackPoolLengthLow or self._stackPoolLength
                log('[Low Memory Log - AliveCount] _OnLuaUIBaseViewMuted Stack Unique', uiIns, uiIns._cname,'栈UI公共对象池长度', stackPoolLength, '当前aliveCount：', aliveCount, uiIns.UITokenID)
            else
                if reConfig then
                    log('[Low Memory Log - AliveCount] _OnLuaUIBaseViewMuted Multi', uiIns, uiIns._cname,'对象池长度：', reConfig.MaxPoolLength or DefaultUIPoolLength, '当前aliveCount：', aliveCount, uiIns.UITokenID)
                else
                    log('[Low Memory Log - AliveCount] _OnLuaUIBaseViewMuted Multi', uiIns, uiIns._cname,'无对象池', '当前aliveCount：', aliveCount, uiIns.UITokenID)
                end
            end

            --- 移除UI缓存池记录
            if (layerType ~= EUILayer.Sub) then
                if self:CheckPoolEnable(reConfig) then
                    local reUIType = MapLayer2RePoolType[layerType]
                    if reUIType then
                        if reUIType == ERePoolType.Unique then
                            if layerType == EUILayer.Stack then
                                table.removebyvalue(self._deUniqueUIList, uiIns)
                            end
                        elseif reUIType == ERePoolType.Multiple then
                            local deUIList = self:GetDeMultipleUIList(uiIns.UINavID)
                            table.removebyvalue(deUIList, uiIns)
                        end
                    end
                end
            end

            --- 移除UI资源引用（废弃）
            if not ALIVE_COUNT_CLEAR_SUBUI then
                if self:GetIsLowMemoryState() then
                    --- Long1 低内存 - 消除
                    if uiSettings.FullPath then
                        if (layerType ~= EUILayer.Sub) and aliveCount <= 0 then
                            self.stubRuntimeUIRes:ClearResRefByPath(uiSettings.FullPath)
                            self.stubLoadingUIRes:ClearResRefByPath(uiSettings.FullPath)
                            self.stubPermanentUIRes:ClearResRefByPath(uiSettings.FullPath)
                            if not VersionUtil.IsShipping() then
                                logwarning("[Low Memory Log - AliveCount]  self.stubRuntimeUIRes:ClearResRefByPath aliveCount <= 0 ", uiSettings.FullPath)
                            end
                        end
                    end
                end
            end

            if (layerType ~= EUILayer.Sub) then
                uiIns:RemoveCloseCallBack(self._OnLuaUIBaseViewMuted, self)
            end
        end
    end
end

return UIManager