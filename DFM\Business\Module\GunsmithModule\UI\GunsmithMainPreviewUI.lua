----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local UGPUINavigationUtils = import "GPUINavigationUtils"
local EGPInputType = import"EGPInputType"
-- END MODIFICATION

local GunsmithUI = require "DFM.Business.Module.GunsmithModule.UI.GunsmithUI"
local GunsmithSceneSocketMainUI = require "DFM.Business.Module.GunsmithModule.UI.SceneSocket.GunsmithSceneSocketMainUI"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GunsmithSolutionMainLogic = require "DFM.Business.Module.GunsmithModule.Logic.Solution.GunsmithSolutionMainLogic"
local GunsmithPartUnlockLogic = require "DFM.Business.Module.GunsmithModule.Logic.Unlock.GunsmithPartUnlockLogic"
local GunsmithTLogLogic = require "DFM.Business.Module.GunsmithModule.Logic.TLog.GunsmithTLogLogic"
local GunsmithSimulateStatePropmtUI = require "DFM.Business.Module.GunsmithModule.UI.Simulate.GunsmithSimulateStatePropmtUI"
local GunsmithMissionUI = require "DFM.Business.Module.GunsmithModule.UI.Mission.GunsmithMissionUI"
local GunsmithMainLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithMainLogic"
local GunsmithSkinLogic = require "DFM.Business.Module.GunsmithModule.Logic.Skin.GunsmithSkinLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local GunsmithMissionLogic = require "DFM.Business.Module.GunsmithModule.Logic.Mission.GunsmithMissionLogic"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local EGunsmithMainPreviewUIShortcutType = require "DFM.Business.Module.GunsmithModule.Define.EGunsmithMainPreviewUIShortcutType"

local EAssemblerCameraType = import "EAssemblerCameraType"
local EAssemblerCamPoint = import "EAssemblerCamPoint"
local EGPInputModeType = import "EGPInputModeType"

local SOCKETSUI_SHOW_DELAY_TIME = 0.2

local gameInst = GetGameInstance()

local EGunsmithMainPreviewUIPagination = {
    Default     = 0,
    Detail      = 1,
    Mission     = 2,
}

local EGunsmithMainPreviewUIGamepadFocusWidgetType = {
    SocketUI = 1,
    NavGroup = 2,
}

---@class GunsmithMainPreviewUI : GunsmithUI
local GunsmithMainPreviewUI = ui("GunsmithMainPreviewUI", GunsmithUI)

function GunsmithMainPreviewUI:Ctor()
    self._wtMoveHiddenPanel = self:Wnd("WBP_MoveHiddenPanel", UIWidgetBase)

    self._socketsUI = self:Wnd("wt_WBP_GunStand_GunsmithSceneSocketMainUI", GunsmithSceneSocketMainUI)
    self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI = self:Wnd("wt_WBP_GunStand_GunsmithSimulateStatePropmtUI", GunsmithSimulateStatePropmtUI)

    self._wt_Slot_Detail = self:Wnd("wt_SlotWeaponMainAttribute", UIWidgetBase)
    self._wtWeaponName = self:Wnd("wNameLabel", UITextBlock)

    self._wt_Button_Unlock_SOL = self:Wnd("wt_Button_UnlockPart", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_Unlock_SOL, self._OnButtonUnlockSOLClicked)

    self._wt_Button_Unlock_MP = self:Wnd("WBP_DFCommonButtonV1S1", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_Unlock_MP, self._OnButtonUnlockMPClicked)

    self._wt_Button_Synchronization = self:Wnd("wt_Button_Synchronization", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_Synchronization, self._OnButtonSynchronizationClicked)
    self._wt_Button_Synchronization:SetActive(false)

    self._wt_Button_Solution = self:Wnd("wtButtonPreset", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_Solution, self._OnButtonSolutionClicked)
    self._wt_Button_Solution:SetActive(false)

    self._wt_Button_Skin = self:Wnd("wtButtonSkin", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_Skin, self._OnButtonSkinClicked)
    self._wt_Button_Skin:SetActive(false)

    self._wt_Button_RemoveAllPartMobile = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wt_Button_RemoveAllPartMobile:Event("OnClicked", self._OnButtonRemoveAllPartMobileClicked, self)
    -- self._wt_Button_RemoveAllPartMobile:SetActive(false)

    self._wt_Button_SaveSolution = self:Wnd("wtSaveSolutionButton", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_SaveSolution, self._OnButtonSaveSolutionClicked)
    self._wt_Button_SaveSolution:SetActive(false)

    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor",self._OnSaveBtnHovered, self._OnSaveBtnUnHovered)
    --self._wtDFTipsAnchor_2 = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor_2",self._OnRangeBtnHovered, self._OnRangeBtnUnHovered)

    self._wt_Button_Range = self:Wnd("wt_Button_Range", UIButton)
    self:AddUIButtonClickedEvent(self._wt_Button_Range, self._OnButtonRangeClicked)

    self._wtTxtMPLevel = self:Wnd("DFTextBlock_179", UITextBlock)
    self._wtSharkIcon = self:Wnd("DFImage_120", UITextBlock)

    -- self._wtCheckBoxLookWeaponModel = self:Wnd("wtCheckBoxLookWeaponModel", DFCommonCheckButtonOnly)
    -- self._wtCheckBoxLookWeaponModel:Event("OnCheckedBoxStateChangedNative", self._OnLookWeaponModelClicked, self)

    self._wtWidgetDefaultSkin = self:Wnd("wtCheckBoxWeaponDefaultSkin", DFCommonCheckButtonOnly)
    self._wtWidgetDefaultSkin:Event("OnCheckedBoxStateChangedNative", self._OnWeaponDefaultSkinClicked, self)
    self._focusSkinID = 0
    -- 暂时屏蔽
    self._wtWidgetDefaultSkin:SetActive(false)

    self._wtWeaponDetailCheckButton = self:Wnd("wtCheckBoxLookWeaponModel", DFCommonCheckButtonOnly)
    self._wtWeaponDetailCheckButton:Event("OnCheckedBoxStateChangedNative", self._OnWeaponDetailCheckButtonClicked, self)

    self._wtDFTipsAnchor_1 = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor_1",self._OnDetailCheckBtnHovered, self._OnDetailCheckBtnUnHovered)

    self._wtMissionUI = self:Wnd("WBP_GunStand_Misson", GunsmithMissionUI)
    self._wtCheckBoxMission = self:Wnd("wtCheckBoxMission", DFCommonCheckButtonOnly)
    self._wtCheckBoxMission:Event("OnCheckedBoxStateChangedNative", self._OnMissionCheckedBoxStateChanged, self)

    self._cameraPoint = EAssemblerCamPoint.POINT_GUN_DISPLAY

    self:SetCPPValue("WantedInputMode", EGPInputModeType.GameAndUI)

    self._bIsVisibleFormSockets = true
    self._mainPagination = EGunsmithMainPreviewUIPagination.Default
    self._timerHandle = nil
    
    -- BEGIN MODIFICATION @ VIRTUOS : 绑定进入靶场的图标组件
    if IsHD() then
        self._RangeKeyIcon = self:Wnd("KeyIcon", UIWidgetBase)
        self._RotationVec = FVector2D(0, 0)
    end

    self._gamepadFocusWidgetType = 0
    -- END MODIFICATION    
end

function GunsmithMainPreviewUI:Destroy()
    self:RemoverTimerHandler()
end

function GunsmithMainPreviewUI:OnShowBegin()
    -- self._socketsUI:DisableUI()
    GunsmithUI.OnShowBegin(self)

    self._shortcutType = EGunsmithMainPreviewUIShortcutType.Default
    self:RegisterShortcutEvent()
    
    -- BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        self:_EnableNavigation(true)
    end
    -- END MODIFICATION

    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate, self._OnProcessServerDataUpdated, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostRelayConnected, self.OnProcessRelayConnected, self)
    self:AddLuaEvent(Module.UISceneObject.Config.Events.evtUISceneObjectServiceTaskFinish, self.OnProcessUISceneObjectServiceTaskFinish, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithShortcutPropUpdate, self._OnProcessShortcutPropUpdate, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSceneSocketItemUIFocus, self._OnProcessGunsmithOnSceneSocketItemUIFocus, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSimulateDataUpdated, self._OnProcessGunsmithOnSimulateDataUpdated, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnRangeDataUpdated, self._OnProcessGunsmithOnRangeDataUpdated, self)
end

function GunsmithMainPreviewUI:OnShow()
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSceneSocketItemUIClicked, self._OnProcessGunsmithOnSceneSocketItemUIClicked, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnGunsmithPurchaseSuccessCallback, self._OnProcessGunsmithPurchaseSuccessCallback, self)
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSCheapBuyRes, self._OnCSCheapBuyRes, self)
    self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithUnlockPathSuccessCallback, self._OnProcessGunsmithUnlockPathSuccessCallback, self)

    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)

    self._wtWidgetDefaultSkin:SetIsChecked(false)
    -- self._wtCheckBoxLookWeaponModel:SetIsChecked(false)
    self:_OnRegisterCameraEvent()
end

function GunsmithMainPreviewUI:OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        self:_EnableNavigation(false)
    end
    -- END MODIFICATION
    GunsmithUI.OnHideBegin(self)
    if self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI then
        self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI:SetActive(false)
    end
    self:UnRegisterShortcutEvent()

    self:ResetGamepadFocusWidgetType()
end

function GunsmithMainPreviewUI:OnHide()
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostRelayConnected)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSceneSocketItemUIClicked)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnGunsmithPurchaseSuccessCallback)
    self:RemoveLuaEvent(Server.GunsmithServer.Events.evtCSCheapBuyRes)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithShortcutPropUpdate)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSceneSocketItemUIFocus)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnSimulateDataUpdated)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithOnRangeDataUpdated)
    self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithUnlockPathSuccessCallback)

    UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._OnHandleMouseButtonUpEvent, self)

    self:RemoveLuaEvent(Module.UISceneObject.Config.Events.evtUISceneObjectServiceTaskFinish)

    self:_OnUnRegisterCameraEvent()
    self:RemoverTimerHandler()
end

function GunsmithMainPreviewUI:RemoverTimerHandler()
    if self._timerHandle then
        Timer.CancelDelay(self._timerHandle)
    end
    self._timerHandle = nil
end

function GunsmithMainPreviewUI:GetTitle()
    return Module.Gunsmith.Config.Loc.GunsmithMainPreviewTitle
end

function GunsmithMainPreviewUI:_OnRegisterCameraEvent()
    local cameraActor = GunsmithUIContextLogic.GetCameraActor()
    if isinvalid(cameraActor) then
        return
    end
    self._touchMoveStartHandle = cameraActor.OnTouchMoveStart:Add(CreateCPlusCallBack(self._OnTouchMoveStart, self))
    self._touchMoveEndHandle = cameraActor.OnTouchMoveEnd:Add(CreateCPlusCallBack(self._OnTouchMoveEnd, self))
end

function GunsmithMainPreviewUI:_OnUnRegisterCameraEvent()
    local cameraActor = GunsmithUIContextLogic.GetCameraActor()
    if isinvalid(cameraActor) then
        return
    end

    if self._touchMoveStartHandle then
        cameraActor.OnTouchMoveStart:Remove(self._touchMoveStartHandle)
        self._touchMoveStartHandle = nil
    end
    if self._touchMoveEndHandle then
        cameraActor.OnTouchMoveEnd:Remove(self._touchMoveEndHandle)
        self._touchMoveEndHandle = nil
    end
end

function GunsmithMainPreviewUI:_OnTouchMoveStart()
    self._wtMoveHiddenPanel:SetActive(false)
    self._socketsUI:DisableUI()
    if self.WBP_GunStand_GunsmithMainPreviewUI_hidden then
        self:PlayWidgetAnim(self.WBP_GunStand_GunsmithMainPreviewUI_hidden, 1, EUMGSequencePlayMode.Forward, 1.0, false)
    end
end

function GunsmithMainPreviewUI:_OnTouchMoveEnd()
    self._wtMoveHiddenPanel:SetActive(true)
    if self.WBP_GunStand_GunsmithMainPreviewUI_hidden then
        self:PlayWidgetAnim(self.WBP_GunStand_GunsmithMainPreviewUI_hidden, 1, EUMGSequencePlayMode.Reverse, 1.0, false)
    end
    self:OnProcessUIUpdate(false)
end

function GunsmithMainPreviewUI:OnProcessUISceneObjectServiceTaskFinish()
    self:RemoverTimerHandler()
    self._timerHandle = Timer.DelayCall(0, function()
        self:_InternalOnProcessSceneSockets()
    end)
end

function GunsmithMainPreviewUI:_OnProcessServerDataUpdated()
    self:OnProcessUIUpdate(true)
end

function GunsmithMainPreviewUI:OnProcessRelayConnected()
    self:OnProcessUIUpdate(true)
end

function GunsmithMainPreviewUI:_OnProcessGunsmithOnSceneSocketItemUIClicked(socketData)
    GunsmithUIContextLogic.SetFocusSocket(socketData)
    local param = self:GetUIParam()
    Module.Gunsmith:OpenPartMainUI(param)
end

function GunsmithMainPreviewUI:_OnProcessShortcutPropUpdate()
    self:OnProcessUIUpdate(true)
end

function GunsmithMainPreviewUI:_OnProcessGunsmithPurchaseSuccessCallback()
    GunsmithUIContextLogic.SetUnSycnRangeSolution(false)
    GunsmithUIContextLogic.ProcessSyncContext4PartFromFrontend()
end

function GunsmithMainPreviewUI:_OnCSCheapBuyRes(code)
    GunsmithTLogLogic.TLogBuy(code)
end

function GunsmithMainPreviewUI:_OnProcessGunsmithUnlockPathSuccessCallback()
    self:OnProcessUIUpdate(true)
end

function GunsmithMainPreviewUI:_OnButtonUnlockSOLClicked()
    -- local bUnlock = Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionBuy)
    -- if not bUnlock then
    --     LuaGlobalEvents.evtServerShowTip:Invoke(Module.Gunsmith.Config.Loc.GunsmithPreviewUnlockePartAuctionText)
    --     return
    -- end

    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    GunsmithPartUnlockLogic.OpenPurchaseUI(weaponDescription, true)
end

function GunsmithMainPreviewUI:_OnButtonUnlockMPClicked()
    -- local bUnlock = Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionBuy)
    -- if not bUnlock then
    --     LuaGlobalEvents.evtServerShowTip:Invoke(Module.Gunsmith.Config.Loc.GunsmithPreviewUnlockePartAuctionText)
    --     return
    -- end

    -- local weaponGUID = GunsmithUIContextLogic.GetGUID()
    -- GunsmithPartUnlockLogic.ProcessUnlockMP(weaponGUID)

    GunsmithPartUnlockLogic.OpenUnlockPathUI()
end

function GunsmithMainPreviewUI:_OnButtonSynchronizationClicked()
    GunsmithSolutionMainLogic.OpenSolutionSynchronizationUI()
end

function GunsmithMainPreviewUI:_OnButtonSolutionClicked()
    local uiParam = self:GetUIParam()
    GunsmithSolutionMainLogic.OpenMainUI(uiParam)
end

function GunsmithMainPreviewUI:_OnButtonSkinClicked()
    local uiParam = self:GetUIParam()
    GunsmithSkinLogic.OpenMainUI(uiParam)
end

function GunsmithMainPreviewUI:_OnButtonRemoveAllPartMobileClicked()
    local bIsHD = false -- IsHD()
    if bIsHD then
        self:ProcessShortcutRemoveAllPart()
        return
    end

    local function fOnConfim()
        self:ProcessShortcutRemoveAllPart()
    end

    local function fOnCancel()
    end

    Module.CommonTips:ShowConfirmWindow(Module.Gunsmith.Config.Loc.GunsmithPreviewRemoveAllPartsText, fOnConfim, fOnCancel)
end

function GunsmithMainPreviewUI:_OnButtonSaveSolutionClicked()
    GunsmithSolutionMainLogic.OpenSaveConfirmUI()
end

function GunsmithMainPreviewUI:_Prepare()
    local bShowMissionUI = GunsmithUIContextLogic.IsShowMainPreviewMissionUI()
    self:_UpdateMainPagination(false, bShowMissionUI)
end

function GunsmithMainPreviewUI:_OnButtonRangeClicked()
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        GunsmithUIContextLogic.SetWeaponInRange()
        return
    end
    -- 靶场入口
    local function fOnConfim()
        GunsmithUIContextLogic.OpenRange()
    end

    local function fOnCancel()
    end

    Module.CommonTips:ShowConfirmWindow(Module.Gunsmith.Config.Loc.GunsmithMainPreviewUIRangeText, fOnConfim, fOnCancel)
end

function GunsmithMainPreviewUI:_OnLookWeaponModelClicked()
    GunsmithMainLogic.OpenModelMainUI()
    self._wtCheckBoxLookWeaponModel:SetIsChecked(false)
end

function GunsmithMainPreviewUI:_OnWeaponDefaultSkinClicked(bSelected)
    if bSelected then
        GunsmithUIContextLogic.ProcessSetSkinID(0)
        self:OnProcessUIUpdate(false)
        return
    end

    GunsmithUIContextLogic.ProcessContext()
    self:OnProcessUIUpdate(false)
end

function GunsmithMainPreviewUI:_OnWeaponDetailCheckButtonClicked(bSelected)
    local bShowWeaponDetail = bSelected
    local bShowMissionUI = self._wtCheckBoxMission:IsChecked()
    if bSelected and bShowMissionUI then
        bShowMissionUI = false
    end
    self:_UpdateMainPagination(bShowWeaponDetail, bShowMissionUI)
    self:OnProcessUIUpdate(false)

    -- BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        self:_RefreshPromptOnWeaponDetailStateChanged()
    end
    -- END MODIFICATION
end

function GunsmithMainPreviewUI:_OnMissionCheckedBoxStateChanged(bSelected)
    local bCanShowMission = GunsmithUIContextLogic.CanShowMission()
    if not bCanShowMission then
        LuaGlobalEvents.evtServerShowTip:Invoke(Module.Gunsmith.Config.Loc.GunsmithMissionUINotMissionTips)
    end
    local bShowWeaponDetail = self._wtWeaponDetailCheckButton:IsChecked()
    local bShowMissionUI = bSelected and bCanShowMission
    if bShowWeaponDetail and bShowMissionUI then
        bShowWeaponDetail = false
    end

    self:_UpdateMainPagination(bShowWeaponDetail, bShowMissionUI)
    self:OnProcessUIUpdate(false)
end

function GunsmithMainPreviewUI:_OnHandleMouseButtonUpEvent(mouseEvent)
    local absolutePoint = mouseEvent:GetScreenSpacePosition()
    local detailPanel = self:GetDetailPanel()
	local bInside = UIUtil.CheckAbsolutePointInsideWidget(self._wtWeaponDetailCheckButton, absolutePoint)
    bInside = bInside or UIUtil.CheckAbsolutePointInsideWidget(self._wtCheckBoxMission, absolutePoint)
    bInside = bInside or UIUtil.CheckAbsolutePointInsideWidget(detailPanel, absolutePoint)
    bInside = bInside or (isvalid(self._socketsUI) and self._socketsUI.CheckAbsolutePointInsideSocketItemUIs and self._socketsUI:CheckAbsolutePointInsideSocketItemUIs(absolutePoint))
    if bInside then
        return
    end
    if Module.ItemDetail:GetPopCount() > 0 then
        return
    end
    local bShowWeaponDetail = self._wtWeaponDetailCheckButton:IsChecked()
    local bShowMissionUI = self._wtCheckBoxMission:IsChecked()
    if (not bShowWeaponDetail) and (not bShowMissionUI) then
        return
    end

    self:_UpdateMainPagination(false, false)
    self:OnProcessUIUpdate(false)
end

function GunsmithMainPreviewUI:GetDetailPanel()
    local bIsValid = isvalid(self._wtDetailAttributeView) and self._wtDetailAttributeView.GetDetailPanel
    if not bIsValid then
        return nil
    end
    return self._wtDetailAttributeView:GetDetailPanel()
end

function GunsmithMainPreviewUI:OpenFromMainUI(bFromUserClicked)
    self:_Prepare()
    -- local fCallback = function()
        self:OnProcessUIUpdate(true)
    -- end

    -- self:ProcessRangetoGunsmith(fCallback)
end

function GunsmithMainPreviewUI:ProcessRangetoGunsmith(fCallback)
    local fFinshCallback = function()
        if fCallback then
            fCallback()
        end
    end

    local bIsRangetoGunsmith = GunsmithUIContextLogic.GetIsRangetoGunsmithBytPropInfo()
    if not bIsRangetoGunsmith then
        return fFinshCallback()
    end

    local function fOnConfim()
        GunsmithUIContextLogic.OnProcessRangetoGunsmith()
        GunsmithUIContextLogic.ResetRangetoGunsmith()
        fFinshCallback()
    end

    local function fOnCancel()
        GunsmithUIContextLogic.ResetRangetoGunsmith()
        fFinshCallback()
    end

    Module.CommonTips:ShowConfirmWindow(Module.Gunsmith.Config.Loc.GunsmithMainRangeSolutionSaveTips, fOnConfim, fOnCancel)
end

function GunsmithMainPreviewUI:OnForceProcessUI()
    GunsmithUIContextLogic.ProcessContext()
    -- 暂时屏蔽
    -- self._focusSkinID = GunsmithUIContextLogic.GetSkinIDFromBackup()
end

function GunsmithMainPreviewUI:OnPreProcessUI()
    self._socketsUI:DisableUI()
end

function GunsmithMainPreviewUI:OnProcessUI()
    self:_InternalUpdateSimulateUI()
    -- self:_InternalUpdateSynchronizationButtonUI()
    self:_InternalUpdateSkinDefaultUI()
    self:_ShowWeaponNameUI()
    self:_ShowMissionUI()
    self:_InternalUpdateStatus()
    self:_InternalUpdateMissionUI()
    self:_InternalUpdateWeaponDetailUI()
    self:_InternalUpdateRangeButtonUI()
    self:_InternalUpdateMPLevelUI()
end

function GunsmithMainPreviewUI:OnProcessSceneObject()
    GunsmithUIContextLogic.SetCameraType(EAssemblerCameraType.ROTATE_ROOT)
    GunsmithUIContextLogic.SetCameraDefaultPoint(self._cameraPoint)
    GunsmithUIContextLogic.SetFocusSocket(nil)

    GunsmithUI.OnProcessSceneObject(self)
end

function GunsmithMainPreviewUI:_InternalOnProcessSceneSockets()
    if not self._bIsVisibleFormSockets then
        return
    end

    local weaponActor = GunsmithUIContextLogic.GetWeaponAcator()
    local frontEndSnapshot = GunsmithUIContextLogic.GetWeaponSocketSnapshot4FrontEnd()
    self._socketsUI:UpdateUI(weaponActor, frontEndSnapshot)

    self:SetGamepadFocusWidget(EGunsmithMainPreviewUIGamepadFocusWidgetType.SocketUI)
end

function GunsmithMainPreviewUI:_InternalUpdateSimulateUI()
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    if bIsRange then
        self._wt_Button_Unlock_SOL:SetActive(false, ESlateVisibility.Visible)
        self._wt_Button_Unlock_MP:SetActive(false, ESlateVisibility.Visible)
        self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI:SetActive(false)
        return
    end

    local bSimulate = GunsmithUIContextLogic.GetIsSimulateState()
    local bUnSycnRangeSolution = GunsmithUIContextLogic.GetUnSycnRangeSolution()
    self._wt_WBP_GunStand_GunsmithSimulateStatePropmtUI:UpdateUI()

    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    local bShowSOLButton = (bSimulate or bUnSycnRangeSolution) and (not bIsMP)
    local bShowMPButton = (bSimulate or bUnSycnRangeSolution) and bIsMP
    self._wt_Button_Unlock_SOL:SetActive(bShowSOLButton, ESlateVisibility.Visible)
    self._wt_Button_Unlock_MP:SetActive(bShowMPButton, ESlateVisibility.Visible)

    if bShowSOLButton then
        local SOLPartTotalPriceStr = self:_GetUnlockSOLButtonStr()
        self._wt_Button_Unlock_SOL:SetMainTitle(SOLPartTotalPriceStr)
    end

    if bShowMPButton then
        -- local MPUnlockStr = Module.Gunsmith.Config.Loc.GunsmithPreviewMPButtonUnlockText
        local MPUnlockStr = Module.Gunsmith.Config.Loc.GunsmithUnlockPathUIConfirmBtnText
        self._wt_Button_Unlock_MP:SetMainTitle(MPUnlockStr)
    end

    -- BEGIN MODIFICATION @ VIRTUOS : 快捷键需要在Button的状态刷新后再注册
    if IsHD() then
        self:_RegisterShortcutEventPurchase()
    end
    -- END MODIFICATION   
end

function GunsmithMainPreviewUI:_InternalUpdateSynchronizationButtonUI()
    -- local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    -- local bSimulate = GunsmithUIContextLogic.GetIsSimulateState()
    -- local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    -- local bShow = bIsMP and (not bSimulate) and (not bIsRange)
    -- self._wt_Button_Synchronization:SetActive(bShow, ESlateVisibility.Visible)
end

function GunsmithMainPreviewUI:_GetUnlockSOLButtonStr()
    local bShowTotalPrice = false
    if not bShowTotalPrice then
        return Module.Gunsmith.Config.Loc.GunsmithMainPreviewUIButtonText
    end

    local totalPrice
    local totalPriceStr = MathUtil.GetNumberFormatStr(totalPrice)
    local currencyIconTxt = ECurrencyClientType2RichIconTxt[ECurrencyClientType.OnlyUnBind]
    local param = {
        ["currencyIconTxt"] = currencyIconTxt,
        ["priceStr"] = totalPriceStr,
        -- ["btnTxt"] = Module.Gunsmith.Config.Loc.GunsmithMainPreviewUIButtonPriceText
    }
    return StringUtil.Key2StrFormat(Module.Gunsmith.Config.Loc.GunsmithPriceText, param)
end

function GunsmithMainPreviewUI:_InternalUpdateSkinDefaultUI()
    local bActive = self._focusSkinID > 0
    self._wtWidgetDefaultSkin:SetActive(bActive)
end

function GunsmithMainPreviewUI:_ShowWeaponNameUI()
    local name = self:_GetWeaponName()
    self._wtWeaponName:SetText(name)
end

function GunsmithMainPreviewUI:_GetWeaponName()
    local uiParam = self:GetUIParam()
    if uiParam == nil then
        return CONST_STRING.EMPTY
    end
    local itemID = uiParam:GetItemID()
    local name = ItemConfigTool.GetItemName(itemID)
    return name
end

function GunsmithMainPreviewUI:_ShowMissionUI()
    local bIsMp = GunsmithUIContextLogic.GetGroupIDIsMP()
    local bCanShowMission = GunsmithUIContextLogic.CanShowMission()
    local bShowMissionUI = not bIsMp and bCanShowMission
    self._wtCheckBoxMission:SetActive(bShowMissionUI)
end

function GunsmithMainPreviewUI:_InternalUpdateStatus()
    local bShowDetail = self._mainPagination == EGunsmithMainPreviewUIPagination.Detail
    if bShowDetail then
        self._cameraPoint = EAssemblerCamPoint.POINT_GUN_DISPLAY_FEATURE1
        self._socketsUI:DisableUI()
        self._bIsVisibleFormSockets = false
        return
    end

    self._cameraPoint = EAssemblerCamPoint.POINT_GUN_DISPLAY
    self._bIsVisibleFormSockets = true
end

function GunsmithMainPreviewUI:_InternalUpdateMissionUI()
    local bSelected = self._mainPagination == EGunsmithMainPreviewUIPagination.Mission
    self._wtCheckBoxMission:SetIsChecked(bSelected)
    self._wtMissionUI:SetActive(bSelected)
    GunsmithUIContextLogic.SetShowMainPreviewMissionUI(bSelected)
    if not bSelected then
        return
    end
    self._wtMissionUI:UpdateUI()
end

function GunsmithMainPreviewUI:_InternalUpdateWeaponDetailUI()
    local bSelected = self._mainPagination == EGunsmithMainPreviewUIPagination.Detail
    self._wtWeaponDetailCheckButton:SetIsChecked(bSelected)
    self._wt_Slot_Detail:SetActive(bSelected)
    if not bSelected then
        -- BEGIN MODIFICATION @ VIRTUOS : 在详情页面隐藏时，移除它的导航信息
        if IsHD() and self._wtDetailAttributeView then
            self._wtDetailAttributeView:EnableNavDetailPreviewMainUI(false)
        end
        -- END MODIFICATION
        return
    end
    local ui = self:ShowUIWindow(UIName2ID.GunsmithDetailPreviewMainUI, self._wt_Slot_Detail)
    if ui == nil then
        return
    end
    self._wtDetailAttributeView = ui
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
    ui:UpdateUI(weaponDescription)
end

function GunsmithMainPreviewUI:_InternalUpdateRangeButtonUI()
    -- local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    -- if bIsRange then
    --     self._wt_Button_Range:SetVisibility(ESlateVisibility.Collapsed)
    -- else
        self._wt_Button_Range:SetVisibility(ESlateVisibility.Visible)
    -- end
end

function GunsmithMainPreviewUI:_InternalUpdateMPLevelUI()
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    local bIsRange = GunsmithUIContextLogic.GetGroupIDIsRange()
    local bShowMPLevelUI = bIsMP and not bIsRange
    self._wtTxtMPLevel:SetActive(bShowMPLevelUI)
    if not bShowMPLevelUI then
        self._wtSharkIcon:SetActive(bShowMPLevelUI)
        return
    end
    local weaponLevel = self:GetCurrentWeaponLevelOlnyMP()
    local weaponStarNum = self:GetCurrentWeaponStarNumOlnyMP()

    local bShowSharkIcon = true
    local mpLevelStr = weaponStarNum
    if weaponStarNum == 0 then
        mpLevelStr = string.format(Module.Gunsmith.Config.Loc.GunsmithMainPreviewUIMPLevelText, weaponLevel)
        bShowSharkIcon = false
    end

    self._wtTxtMPLevel:SetText(mpLevelStr)
    self._wtSharkIcon:SetActive(bShowSharkIcon)
end

-- 获取MP模式下的武器等级
function GunsmithMainPreviewUI:GetCurrentWeaponLevelOlnyMP()
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    if not bIsMP then
        return 0
    end
    local weaponItemID = GunsmithUIContextLogic.GetItemID()
    local weaponLevel = WeaponAssemblyTool.GetMPWeaponLevel(weaponItemID)
    return weaponLevel
end

function GunsmithMainPreviewUI:GetCurrentWeaponStarNumOlnyMP()
    local bIsMP = GunsmithUIContextLogic.GetGroupIDIsMP()
    if not bIsMP then
        return 0
    end
    local weaponItemGUID = GunsmithUIContextLogic.GetGUID()
    local weaponItembase = GunsmithUIContextLogic.GetItemByGid(weaponItemGUID)
    local starNum = 0
    if weaponItembase then
        local weaponFeature = weaponItembase:GetFeature(EFeatureType.Weapon)
        if weaponFeature then
            starNum = weaponFeature:GetStarNum()
        end
    end
    return starNum
end

function GunsmithMainPreviewUI:_UpdateMainPagination(bShowDetail, bShowMissionUI)
    if bShowDetail and bShowMissionUI then
        if self._mainPagination == EGunsmithMainPreviewUIPagination.Default
        or self._mainPagination == EGunsmithMainPreviewUIPagination.Detail then
            self._mainPagination = EGunsmithMainPreviewUIPagination.Mission
        elseif self._mainPagination == EGunsmithMainPreviewUIPagination.Mission then
            self._mainPagination = EGunsmithMainPreviewUIPagination.Detail
        end
        return
    end

    if bShowMissionUI then
        self._mainPagination = EGunsmithMainPreviewUIPagination.Mission
        return
    end

    if bShowDetail then
        self._mainPagination = EGunsmithMainPreviewUIPagination.Detail
        return
    end

    self._mainPagination = EGunsmithMainPreviewUIPagination.Default
end

function GunsmithMainPreviewUI:_OnSaveBtnHovered()
    self:_ShowSaveSolutionTip(true)
end

function GunsmithMainPreviewUI:_OnSaveBtnUnHovered()
    self:_ShowSaveSolutionTip(false)
end

function GunsmithMainPreviewUI:_OnDetailCheckBtnHovered()
    self:_ShowDetailCheckTip(true)
end

function GunsmithMainPreviewUI:_OnDetailCheckBtnUnHovered()
    self:_ShowDetailCheckTip(false)
end

function GunsmithMainPreviewUI:_OnRangeBtnHovered()
    self:_ShowRangeTip(true)
end

function GunsmithMainPreviewUI:_OnRangeBtnUnHovered()
    self:_ShowRangeTip(false)
end



function GunsmithMainPreviewUI:_ShowSaveSolutionTip(bShow)
    if bShow then
        self._saveSolutionTipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(Module.Gunsmith.Config.Loc.GunsmithSaveSolutionTip, self._wtDFTipsAnchor)
    else
        if self._saveSolutionTipHandle then
            Module.CommonTips:RemoveCommonMessageWithAnchor(self._saveSolutionTipHandle, self._wtDFTipsAnchor)
            self._saveSolutionTipHandle = nil
        end
    end
end

function GunsmithMainPreviewUI:_ShowDetailCheckTip(bShow)
    if bShow then
        self._detailCheckTipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(Module.Gunsmith.Config.Loc.GunsmithDetailCheckTip, self._wtDFTipsAnchor_1)
    else
        if self._detailCheckTipHandle then
            Module.CommonTips:RemoveCommonMessageWithAnchor(self._detailCheckTipHandle, self._wtDFTipsAnchor_1)
            self._detailCheckTipHandle = nil
        end
    end
end

function GunsmithMainPreviewUI:_ShowRangeTip(bShow)
    if bShow then
        self._rangeTipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(Module.Gunsmith.Config.Loc.GunsmithRangeTip, self._wtDFTipsAnchor_2)
    else
        if self._rangeTipHandle then
            Module.CommonTips:RemoveCommonMessageWithAnchor(self._rangeTipHandle, self._wtDFTipsAnchor_2)
            self._rangeTipHandle = nil
        end
    end
end

function GunsmithMainPreviewUI:RegisterShortcutEvent()
    local bIsHD = IsHD()
    self:SetMobileRemoveAllPartButtonActive(not bIsHD)
    if not bIsHD then
        return
    end
    local shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.RemoveAllPart)
    -- BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.Modify)
        shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.ViewDetail)
        if self._wtCheckBoxMission:IsVisible() then
            shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.ViewMission)
        end

        self:SetShortcutType(shortcutType)

        -- 增加手柄进入靶场快捷键
        if not self._EnterRange then
            self._EnterRange = self:AddInputActionBinding("Gunsmith_EnterRange", EInputEvent.IE_Pressed, self._OnButtonRangeClicked,self, EDisplayInputActionPriority.UI_Stack)
            --设置图标
            local bIsValid = isvalid(self._RangeKeyIcon)
            if bIsValid then
                self._RangeKeyIcon:SetOnlyDisplayOnGamepad(true)
                self._RangeKeyIcon:InitByDisplayInputActionName("Gunsmith_EnterRange", true, 0, true)
            end
        end

        -- 旋转武器
        if not self._RotationX then
            self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
        end
        if not self._RotationY then
            self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
        end
    end
    -- END MODIFICATION    
end

function GunsmithMainPreviewUI:UnRegisterShortcutEvent()
    if not IsHD() then
        return
    end

    local shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.RemoveAllPart)
    shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.RemovePart)
    -- BEGIN MODIFICATION @ VIRTUOS : 移除快捷键
    if IsHD() then
        shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.Modify)
        shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.ViewDetail)
        shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.ViewMission)
        if self._EnterRange then
            self:RemoveInputActionBinding(self._EnterRange)
            self._EnterRange = nil
        end
        if self._Purchase then
            self:RemoveInputActionBinding(self._Purchase)
            self._Purchase = nil
        end
        if self._RotationX then
            self:RemoveInputActionBinding(self._RotationX)
            self._RotationX = nil
        end
        if self._RotationY then
            self:RemoveInputActionBinding(self._RotationY)
            self._RotationY = nil
        end
    end
    -- END MODIFICATION    

    self:SetShortcutType(shortcutType)
end

function GunsmithMainPreviewUI:SetMobileRemoveAllPartButtonActive(bShow)
    self._wt_Button_RemoveAllPartMobile:SetActive(bShow, ESlateVisibility.Visible)
end

function GunsmithMainPreviewUI:_OnProcessGunsmithOnSceneSocketItemUIFocus(bIsFocus)
    local shortcutType = self._shortcutType
    if bIsFocus then
        shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.RemovePart)
    else
        shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.RemovePart)
    end
    
    self:SetShortcutType(shortcutType)
end

function GunsmithMainPreviewUI:_OnProcessGunsmithOnSimulateDataUpdated()
    self:OnProcessUIUpdate(true)
end

function GunsmithMainPreviewUI:SetShortcutType(shortcutType)
    if shortcutType == self._shortcutType then
        return
    end

    self._shortcutType = shortcutType
    self:UpdateShortcutButton()
end

function GunsmithMainPreviewUI:UpdateShortcutButton()
    local summaryList = {}

    -- BEGIN MODIFICATION @ VIRTUOS : 添加手柄改装快捷键
    if IsHD() then
        local bShowModifyBtn = EGunsmithMainPreviewUIShortcutType.HasShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.Modify)
        if bShowModifyBtn then
            table.insert(summaryList, {actionName = "GunsmithModify_Gamepad",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        end
    end
    -- END MODIFICATION

    local bShowRemoveAllPartBtn = EGunsmithMainPreviewUIShortcutType.HasShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.RemoveAllPart)
    if bShowRemoveAllPartBtn then
        table.insert(summaryList, {actionName = "GunsmithRemoveAllPart",func = self.ProcessShortcutRemoveAllPart, caller = self ,bUIOnly = false, bHideIcon = false})
    end

    local bShowRemovePartBtn = EGunsmithMainPreviewUIShortcutType.HasShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.RemovePart)
    if bShowRemovePartBtn then
        table.insert(summaryList, {actionName = "GunsmithRemovePart",func = self.ProcessShortcutRemovePart, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    -- BEGIN MODIFICATION @ VIRTUOS : 查看详情
    if IsHD() then
        local bShowViewDetailBtn = EGunsmithMainPreviewUIShortcutType.HasShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.ViewDetail)
        if bShowViewDetailBtn then
            table.insert(summaryList, {actionName = "Gunsmith_ViewDetails_Gamepad",func = self._ShortcutEventViewDetails, caller = self ,bUIOnly = false, bHideIcon = false})
        end

        -- local bShowViewMissionBtn = EGunsmithMainPreviewUIShortcutType.HasShortcutType(self._shortcutType, EGunsmithMainPreviewUIShortcutType.ViewMission)
        -- if bShowViewMissionBtn then
        --     table.insert(summaryList, {actionName = "Gunsmith_Mission_Gamepad",func = self._ShortcutEventViewDetails, caller = self ,bUIOnly = false, bHideIcon = false})
        -- end
    end
    -- END MODIFICATION
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
end

function GunsmithMainPreviewUI:ProcessShortcutRemoveAllPart()
    GunsmithUIContextLogic.ProcessShortcutRemoveAllPart()
end

function GunsmithMainPreviewUI:ProcessShortcutRemovePart()
    GunsmithUIContextLogic.ProcessShortcutRemovePart()
    self:_OnProcessGunsmithOnSceneSocketItemUIFocus(false)
end

function GunsmithMainPreviewUI:_OnProcessGunsmithOnRangeDataUpdated(uiParam)
    self:SetUIParam(uiParam)
    GunsmithUIContextLogic.ProcessContext()
    self:OnProcessUIUpdate(true)
end

-- BEGIN MODIFICATION @ VIRTUOS : 
function GunsmithMainPreviewUI:_EnableNavigation(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._socketsUI, self, "Gunsmith")
            if self._wtNavGroup then
                self._wtNavGroup:AddNavWidgetToArray(self._socketsUI)

                self:SetGamepadFocusWidget(EGunsmithMainPreviewUIGamepadFocusWidgetType.NavGroup)
                -- 绑定FocusProxy的Method，需要提供一个Maker将控件转换为Proxy，以及一个Resolver将Proxy转换为控件
                WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._FocusProxyMaker, self._FocusProxyResolver, self)
            end
        end
    else
        if  self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end

        -- 处理导航组注销后再次注册，Focus在同一位置时不能导航的问题
        UGPUINavigationUtils.SetUserFocusToGameViewport(GetGameInstance())
    end
end

function GunsmithMainPreviewUI:SetGamepadFocusWidget(GamepadFocusWidgetType)
    local bIsGamepad = self:IsGamepad()
    if not bIsGamepad then
        return
    end

    self._gamepadFocusWidgetType = self:InternalLeftMovementBit(self._gamepadFocusWidgetType, GamepadFocusWidgetType)

    local bIsCompleted = true
    for _, value in ipairs(EGunsmithMainPreviewUIGamepadFocusWidgetType) do
        bIsCompleted = bIsCompleted and self:InternalHasBit(self._gamepadFocusWidgetType, value)
    end

    if not bIsCompleted then
        return
    end

    self:_InternalSetGamepadFocusWidget()
end

function GunsmithMainPreviewUI:ResetGamepadFocusWidgetType()
    self._gamepadFocusWidgetType = 0
end

function GunsmithMainPreviewUI:InternalLeftMovementBit(src, bit)
    src = src | (1 << bit)
    return src
end

function GunsmithMainPreviewUI:InternalHasBit(src, bit)
    return (curType & (1 << bit)) > 0
end

function GunsmithMainPreviewUI:IsGamepad()
    local curInputType = WidgetUtil.GetCurrentInputType()
    return IsHD() and curInputType == EGPInputType.Gamepad
end

function GunsmithMainPreviewUI:_InternalSetGamepadFocusWidget()
    if not IsHD() then
        return
    end
    local focusWidget = self._socketsUI:GetGamepadLastFocusWidget()
    local bIsValid = isvalid(focusWidget)
    if bIsValid then
        WidgetUtil.SetUserFocusToWidget(focusWidget, true)
        return
    end
    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
end

function GunsmithMainPreviewUI:_FocusProxyMaker(inWidget)
    -- Maker会将当前聚焦的UWidget传入
    -- 但聚焦的控件往往是一些button之类的原子控件，不一定是上层带有业务逻辑的Item，因此需要通过GetParentWidgetByClassName找到上层的Item
    local socketItem = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_GunStand_GunsmithSceneSocketItemUI_C")
    -- 通过Item找到当前的guid
    local guid = socketItem:GetSocketData():GetSocketGUID()
    return {guid}
end

function GunsmithMainPreviewUI:_FocusProxyResolver(inProxyHandle)
    local guid = inProxyHandle[1]
    -- 利用guid去查找对应的Item，此时不需要再查找内部的原子控件，框架会自动处理
    return self._socketsUI:GetItemUIBySocketGUID(guid)
end

-- 购买、升级解锁按钮（烽火地带购买按钮和全面战场升级解锁按钮只会显示一个）
function GunsmithMainPreviewUI:_RegisterShortcutEventPurchase()
    if not IsHD() then
        return 
    end

    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end
    
    local fUnlockButtonClicked = nil
    local unlockButton = nil
    if self._wt_Button_Unlock_SOL:IsVisible() then
        fUnlockButtonClicked = CreateCallBack(self._OnButtonUnlockSOLClicked, self)
        unlockButton = self._wt_Button_Unlock_SOL
    elseif self._wt_Button_Unlock_MP:IsVisible() then
        fUnlockButtonClicked = CreateCallBack(self._OnButtonUnlockMPClicked, self)
        unlockButton = self._wt_Button_Unlock_MP
    end

    if unlockButton then
        self._Purchase = self:AddInputActionBinding("Gunsmith_Purchase_Gamepad", EInputEvent.IE_Pressed, fUnlockButtonClicked, self, EDisplayInputActionPriority.UI_Stack)
        unlockButton:SetDisplayInputAction("Gunsmith_Purchase_Gamepad", true, nil, true)
    end
end

-- 查看详情
function GunsmithMainPreviewUI:_ShortcutEventViewDetails()
    if not IsHD() or not self._wtWeaponDetailCheckButton then
        return 
    end

    if self._wtWeaponDetailCheckButton:IsVisible() and not self._wtCheckBoxMission:IsVisible() then
        self._wtWeaponDetailCheckButton:SelfClick()
    end

    -- 这里是策划要求，当能看到mission按钮的时候，按Y是在 detail，mission，关闭这三个状态来回切
    if self._wtWeaponDetailCheckButton:IsVisible() and self._wtCheckBoxMission:IsVisible() then
        if self._mainPagination == EGunsmithMainPreviewUIPagination.Default then
            self._wtWeaponDetailCheckButton:SelfClick()
        elseif  self._mainPagination == EGunsmithMainPreviewUIPagination.Detail then
            self._wtCheckBoxMission:SelfClick()
        elseif self._mainPagination == EGunsmithMainPreviewUIPagination.Mission then
            self._wtCheckBoxMission:SelfClick()
        else
        end
    end
end

-- 武器详情Button状态变更时，刷新按键提示。在ShowBegin时直接返回，防止外面打开这个页面的按键直接一步到位走到这儿。
function GunsmithMainPreviewUI:_RefreshPromptOnWeaponDetailStateChanged()
    if not IsHD() or self:IsInShowBeginState() or self:IsInHideBeginState() then
        return 
    end

    local shortcutType = self._shortcutType
    local bShowWeaponDetail = self._wtWeaponDetailCheckButton:IsChecked()
    if bShowWeaponDetail then
        shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.Modify)
        shortcutType = EGunsmithMainPreviewUIShortcutType.RemoveShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.RemoveAllPart)
    else
        shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.Modify)
        shortcutType = EGunsmithMainPreviewUIShortcutType.AddShortcutType(shortcutType, EGunsmithMainPreviewUIShortcutType.RemoveAllPart)
    end
    self:SetShortcutType(shortcutType)
end

-- 旋转武器 0值作为手柄旋转结束的标志也需要传入C++
function GunsmithMainPreviewUI:_WeaponRotationX(value)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return 
    end

    self._RotationVec.X = value
    local gpPlayerController = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)
    if gpPlayerController then
        gpPlayerController:TouchUpdateByGamepad(self._RotationVec.X, self._RotationVec.Y)
    end
end

function GunsmithMainPreviewUI:_WeaponRotationY(value)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return 
    end

    self._RotationVec.Y = value * -1
    local gpPlayerController = UGameplayBlueprintHelper.GetLocalGPPlayerController(self)
    if gpPlayerController then
        gpPlayerController:TouchUpdateByGamepad(self._RotationVec.X, self._RotationVec.Y)
    end
end
-- END MODIFICATION

return GunsmithMainPreviewUI
