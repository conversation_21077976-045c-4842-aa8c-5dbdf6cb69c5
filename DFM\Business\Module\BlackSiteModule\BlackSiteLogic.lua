----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBlackSite)
----- LOG FUNCTION AUTO GENERATE END -----------



local BlackSiteLogic = {}

local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"

BlackSiteLogic.OutputLog = function(log)
    logerror("azhengzheng:", log)
end

BlackSiteLogic.CheckIsRightReq = function(callBack)
    if Server.BlackSiteServer:GetIsRightInfo() then
        callBack(true)
    else
        callBack()
        Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteRetrieving)
        if Server.BlackSiteServer:GetIsRightConfig() then
            Server.BlackSiteServer:BlackSiteGetInfoReq()
        else
            Server.BlackSiteServer:BlackSiteGetConfigReq(
                function()
                    Server.BlackSiteServer:BlackSiteGetInfoReq()
                end
            )
        end
    end
end

BlackSiteLogic.OpenMainMenu = function()
    Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteEntrance)
end

BlackSiteLogic.Jump = function(id, construct, skipCheck, callBack)
    local BlackSiteConstructUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleSafehouseUpgrade)
    BlackSiteConstructUnlockInfo.bIsUnlocked = BlackSiteConstructUnlockInfo.bIsUnlocked or skipCheck
    local BlackSiteProduceUnlockInfo = Module.ModuleUnlock:GetModuleUnlockInfoById(SwitchModuleID.ModuleSafehouseProduce)
    BlackSiteProduceUnlockInfo.bIsUnlocked = BlackSiteProduceUnlockInfo.bIsUnlocked or skipCheck
    if id then
        if string.len(tostring(id)) == 4 then
            local deviceData = Server.BlackSiteServer:GetDeviceData(id)
            if deviceData then
                if construct then
                    if BlackSiteConstructUnlockInfo.bIsUnlocked then
                        -- azhengzheng:MS24转测周新增
                        if deviceData:GetLevel() ~= 0 or deviceData:IsPreconditionsEnough() then
                            Facade.UIManager:AsyncShowUI(UIName2ID.WBP_BlackSite_Upgrade, nil, nil, deviceData)
                        else
                            local preconditions = deviceData:GetPreconditions()
                            for _, value in pairs(preconditions) do
                                if value.Type == BlackSiteDefine.EBlackSitePreconditionsType.SeasonLevel then
                                    Module.CommonTips:ShowConfirmWindow(
                                        StringUtil.Key2StrFormat(Module.BlackSite.Config.Loc.BlackSiteLockedTipText, {["SeasonLevel"]=value.SeasonLevel, ["DeviceName"]=deviceData:GetName()}),
                                        function()
                                            Facade.UIManager:AsyncShowUI(UIName2ID.WBP_BlackSite_Upgrade, nil, nil, deviceData)
                                            if callBack then
                                                callBack(true)
                                            end
                                        end,
                                        function()
                                            if callBack then
                                                callBack()
                                            end
                                        end,
                                        nil,
                                        Module.BlackSite.Config.Loc.BlackSiteGoToSee
                                    )
                                    return
                                end
                            end
                            Module.CommonTips:ShowSimpleTip(string.format(Module.BlackSite.Config.Loc.BlackSiteDeviceLock, deviceData:GetName()))
                            if callBack then
                                callBack()
                            end
                        end
                        -- azhengzheng:MS24转测周新增
                    else
                        Module.CommonTips:ShowSimpleTip(BlackSiteConstructUnlockInfo.unlocktips)
                        if callBack then
                            callBack()
                        end
                    end
                elseif BlackSiteProduceUnlockInfo.bIsUnlocked then
                    if deviceData:GetLevel() == 0 then
                        Module.CommonTips:ShowConfirmWindow(
                            string.format(Module.BlackSite.Config.Loc.BlackSiteNotUnlockedYet, deviceData:GetName()),
                            function()
                                Facade.UIManager:AsyncShowUI(UIName2ID.WBP_BlackSite_Upgrade, nil, nil, deviceData)
                                if callBack then
                                    callBack(true)
                                end
                            end,
                            function()
                                if callBack then
                                    callBack()
                                end
                            end,
                            nil,
                            Module.BlackSite.Config.Loc.BlackSiteGoToUpgrade
                        )
                    else
                        Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteDeviceProduce, nil, nil, deviceData)
                        if callBack then
                            callBack(true)
                        end
                    end
                else
                    Module.CommonTips:ShowSimpleTip(BlackSiteProduceUnlockInfo.unlocktips)
                    if callBack then
                        callBack()
                    end
                end
            else
                Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteInputIdError)
                if callBack then
                    callBack()
                end
            end
        else
            local deviceData = Server.BlackSiteServer:GetFormulaSourceDeviceData(id)
            if deviceData then
                Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteDeviceProduce, nil, nil, deviceData, id)
            else
                Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteInputIdError)
            end
        end
    elseif construct then
        if BlackSiteConstructUnlockInfo.bIsUnlocked then
            -- Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteConstruct)
            BlackSiteLogic.JumpToMainPanelByUIIdx(BlackSiteDefine.EBlackSiteEntranceType.Construct)
        else
            Module.CommonTips:ShowSimpleTip(BlackSiteConstructUnlockInfo.unlocktips)
        end
    elseif BlackSiteProduceUnlockInfo.bIsUnlocked then
        -- Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteProduce)
        BlackSiteLogic.JumpToMainPanelByUIIdx(BlackSiteDefine.EBlackSiteEntranceType.Produce)
    else
        Module.CommonTips:ShowSimpleTip(BlackSiteProduceUnlockInfo.unlocktips)
    end
end

BlackSiteLogic.BlackSiteGetInfoReq = function()
    Server.BlackSiteServer:BlackSiteGetInfoReq()
end

BlackSiteLogic.GetJumpInfoByItemId = function(itemId)
    Module.BlackSite.Field:ClearItemUsageAndSourceList()
    local itemUsageAndSourceList = Module.BlackSite.Field:GetItemUsageAndSourceList(itemId)
    return itemUsageAndSourceList.SourceList, itemUsageAndSourceList.UsageList, Server.BlackSiteServer:CheckItemCanBeBuild(itemId)
end

BlackSiteLogic.ReceiveAward = function()
    if Server.BlackSiteServer:GetIsRightConfig() then
        Server.BlackSiteServer:BlackSiteGetInfoReq()
    else
        Server.BlackSiteServer:BlackSiteGetConfigReq(
            function()
                Server.BlackSiteServer:BlackSiteGetInfoReq()
            end
        )
    end
end

BlackSiteLogic._GetSubUIBySelf = function(parent, UIID, instanceID)
    local weakUIIns = Facade.UIManager:GetSubUI(parent, UIID, instanceID)

    if weakUIIns then
        local uiIns = getfromweak(weakUIIns)
        return uiIns
    end

    return nil
end

BlackSiteLogic._AddSubUIBySelf = function(parent, UIID, container)
    local weakUIIns, instanceId = Facade.UIManager:AddSubUI(parent, UIID, container)

    if weakUIIns then
        local uiIns = getfromweak(weakUIIns)

        return uiIns, instanceId
    end

    return nil, instanceId
end

BlackSiteLogic.JumpToMainPanelByUIIdx = function(uiIdx, bForceJump)
    if uiIdx and bForceJump then
        Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteMainPanel, nil, nil, uiIdx)
        return
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.BlackSiteMainPanel, nil, nil, BlackSiteDefine.EBlackSiteEntranceType.Construct)
end

BlackSiteLogic.SetCurTabIdx = function(idx)
    Module.BlackSite.Field:SetCurTabIdx(idx)
end

BlackSiteLogic.GetCurTabIdx = function()
    return Module.BlackSite.Field:GetCurTabIdx()
end

BlackSiteLogic.CollectFormulaByID = function(formulaID, itemName)
    if not Module.BlackSite.Field:CheckFormulaCollectBtnCDFinish() then
        Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteClickCollectBtnFastTip)
        return
    end

    local onFormulaFavorite = function(res)
        if res == 0 then
            if Server.BlackSiteServer:GetFormulaCollectStateByID(formulaID) then
                Module.CommonTips:ShowSimpleTip(string.format(Module.BlackSite.Config.Loc.BlackSiteFormulaHasBeenFavorite, itemName or "--"))
            end
        else
            Module.CommonTips:ShowSimpleTip(Module.BlackSite.Config.Loc.BlackSiteCollectFormulaFailTip)
        end
    end

    Server.BlackSiteServer:BlackSiteFormulaFavoriteReq(formulaID, onFormulaFavorite)
end

BlackSiteLogic.SetItemQualityImg = function(img, quality)
    img:Collapsed()

    if not img or not quality or quality == 0 then
        return
    end

    local qualityIcon = Module.Collection.Config.QualityIconMapping[quality]

    if not qualityIcon then
        return
    end

    img:AsyncSetImagePath(qualityIcon)

    local itemQualityLinearColor = ItemConfigTool.GetItemQualityLinearColor(quality)

    if not itemQualityLinearColor then
        return
    end

    img:SetColorAndOpacity(itemQualityLinearColor)
    img:Visible()
end

BlackSiteLogic.CheckDeviceIsOpenByID = function(deviceID, bShowTip)
    if Module.BlackSite.Field:GetDeviceOpenCacheByID(deviceID) then
        return true
    end

    if bShowTip then
        Module.CommonTips:ShowSimpleTip(Module.BattlefieldEntry.Config.Loc.NotOpenNow)
    end

    return false
end

BlackSiteLogic.CheckFormulaCollectBtnCDFinish = function()
    return Module.BlackSite.Field:CheckFormulaCollectBtnCDFinish()
end

BlackSiteLogic.CheckBtnCDFinishByType = function(type, tip)
    return Module.BlackSite.Field:CheckBtnCDFinishByType(type, tip)
end

return BlackSiteLogic