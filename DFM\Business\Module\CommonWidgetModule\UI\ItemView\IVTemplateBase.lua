----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



local IVComponentBase = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVComponentBase"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local CommonWidgetField  = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetField"
local CommonRegisterUIView = require "DFM.Business.Module.CommonWidgetModule.UI.CommonRegisterUIView"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent

local LAI = import "LAI"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local UCanvasPanel = import "CanvasPanel"
local UVerticalBox = import "VerticalBox"
local UHorizontalBox = import "HorizontalBox"
local DFCanvasPanel = import "DFCanvasPanel"
local DFVerticalBox = import "DFVerticalBox"
local DFHorizontalBox = import "DFHorizontalBox"
local FAnchors = import "Anchors"
local EHorizontalAlignment = import "EHorizontalAlignment"
local EVerticalAlignment = import "EVerticalAlignment"

---@class IVTemplateBase : CommonRegisterUIView
local IVTemplateBase = ui("IVTemplateBase", CommonRegisterUIView)

local function log(...)
    loginfo("[IVTemplateBase]", ...)
end

local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder

local commonOffset = FMargin(0, 0, 0, 0)
-- 全铺锚点
local commonAnchor = FAnchors()
local commonAlighment = LuaGlobalConst.TOP_LEFT_VECTOR
commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
-- 左上角锚点
local topLeftAnchor = FAnchors()
topLeftAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
topLeftAnchor.Maximum = LuaGlobalConst.TOP_LEFT_VECTOR
-- 左下角锚点
local bottomLeftAnchor = FAnchors()
bottomLeftAnchor.Minimum = LuaGlobalConst.BOTTOM_LEFT_VECTOR
bottomLeftAnchor.Maximum = LuaGlobalConst.BOTTOM_LEFT_VECTOR
-- 右上角锚点
local topRightAnchor = FAnchors()
topRightAnchor.Minimum = LuaGlobalConst.TOP_RIGHT_VECTOR
topRightAnchor.Maximum = LuaGlobalConst.TOP_RIGHT_VECTOR
-- 右下角锚点
local bottomRightAnchor = FAnchors()
bottomRightAnchor.Minimum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
bottomRightAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
-- 顶部锚点
local topAnchor = FAnchors()
topAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
topAnchor.Maximum = LuaGlobalConst.TOP_RIGHT_VECTOR
-- 底部锚点
local bottomAnchor = FAnchors()
bottomAnchor.Minimum = LuaGlobalConst.BOTTOM_LEFT_VECTOR
bottomAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR

function IVTemplateBase:Ctor()
    ---@type table<number, IVComponentBase>
    self._itemComponents = {}
    ---@type table<number, any>
    self._posMapping = {}
    ---@type ItemBase
    self.item = nil
    ---@type table<position, table>
    self._compNumByPos = {}
    ---@type table<number, component>
    self._components = {}
    self._nameComp = nil

    self:_InitRootSize()
    self:_InitSlotMapping()

    --BEGIN MODIFICATION @ VIRTUOS : 记录当前使用该控件的slotView
    ---@type CommonSlotView
    if IsHD() then
        self.slotView = nil
    end
    --END MODIFICATION

    -- itemview类型
    self.itemviewMode = CommonWidgetConfig.EIVItemViewMode.None
    self.fromIVTemplateBaseClass = true
    
    ---@type FRTIParamData
    self.RTIParamData = nil
end

function IVTemplateBase:Destroy()
    self:_RemoveSlot()
    self:_ReleaseComponents()
    self:_ReleaseTimer()
end

function IVTemplateBase:PreReleaseOnUnBindLuaRefs()
    Facade.UIManager:TryFastReleaseChildren(self)
end

function IVTemplateBase:_ReleaseComponents()
    if self._itemComponents == nil then
        return
    end

    for k, v in pairs(self._itemComponents) do
        releaseobject(v)
    end
end

function IVTemplateBase:_ReleaseTimer()
    if self.timeHandel then
        Timer.CancelDelay(self.timeHandel)
        self.timeHandel = nil
    end
end

-----------------------------------------------------------------------
--region Life cycle

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public

function IVTemplateBase:RegisterComponent(id, component, pos)
    self._itemComponents[id] = component
    if isvalid(component) then
        if component.GetItemViewMode then
            component:GetItemViewMode(self.itemviewMode)
        end
        component:SetParentWidget(self)
        if component._cname == "IVTextQualityComponent" then
            self._nameComp = component
        end
    end
end

function IVTemplateBase:RegisterComponentByName(id, componentName)
    local component = self:Wnd(componentName)
    if component then
        self:RegisterComponent(id, component)
    end
end

---@param component IVComponentBase
function IVTemplateBase:AddComponentAtPosition(id, component, pos, order)
    order = setdefault(order, EIVCompOrder.Order1)

    local slot = self:GetSlotByPos(pos)
    if slot then
        local container = slot:GetContent()
        if container then
            local containerCls = LAI.GetObjectClass(container) 
            if containerCls == UCanvasPanel or containerCls == DFCanvasPanel then
                container:AddChild(component)

                local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(component)
                local zOrder = order
                canvasSlot:SetZOrder(zOrder)
                canvasSlot:SetAutoSize(true)
                -- 设置锚点以及在对齐方式
                if pos == EIVSlotPos.TopLeft then
                    canvasSlot:SetAnchors(topLeftAnchor)
                    canvasSlot:SetPosition(FVector2D(0,0))
                    canvasSlot:SetAlignment(FVector2D(0,0))
                elseif pos == EIVSlotPos.TopRight then
                    canvasSlot:SetAnchors(topRightAnchor)
                    canvasSlot:SetPosition(FVector2D(0,0))
                    canvasSlot:SetAlignment(FVector2D(1,0))
                elseif pos == EIVSlotPos.BottomLeft then
                    canvasSlot:SetAnchors(bottomLeftAnchor)
                    canvasSlot:SetPosition(FVector2D(0,0))
                    canvasSlot:SetAlignment(FVector2D(0,1))
                elseif pos == EIVSlotPos.BottomRight then
                    canvasSlot:SetAnchors(bottomRightAnchor)
                    canvasSlot:SetPosition(FVector2D(0,0))
                    canvasSlot:SetAlignment(FVector2D(1,1))
                else
                    canvasSlot:SetAnchors(commonAnchor)
                    canvasSlot:SetOffsets(commonOffset)
                end
            elseif containerCls == UVerticalBox or containerCls == UHorizontalBox or containerCls == DFHorizontalBox or containerCls == DFVerticalBox then
                -- 由于目前并不支持插入，所以这里的处理会比较麻烦
                -- 先取出所有的Children，重新排序，然后添加回去
                local allChildren = container:GetAllChildren()
                -- container:ClearChildren()
                local tmp = {}
                for _, child in pairs(allChildren) do
                    table.insert(tmp, child)
                end

                component:SetCompOrder(order)
                table.insert(tmp, component)

                local fSortFuc
                if pos == EIVSlotPos.BottomLeft or pos == EIVSlotPos.BottomRight then
                    fSortFuc = function(a, b)
                        return a:CompareOrder(b)
                    end
                else
                    fSortFuc = function(a, b)
                        return not a:CompareOrder(b)
                    end
                end
                table.sort(tmp, fSortFuc)
                local insertIndex = table.indexof(tmp, component)

                -- 计算Alignment
                local horizontalAlignment = EHorizontalAlignment.HAlign_Fill
                if pos == EIVSlotPos.BottomLeft or pos == EIVSlotPos.TopLeft then
                    -- 左对齐
                    horizontalAlignment = EHorizontalAlignment.HAlign_Left
                elseif pos == EIVSlotPos.BottomRight or pos == EIVSlotPos.TopRight then
                    -- 有对齐
                    horizontalAlignment = EHorizontalAlignment.HAlign_Right
                end

                local slot = container:InsertChildAt(insertIndex - 1, component)
                slot:SetHorizontalAlignment(horizontalAlignment)
                -- for _, child in ipairs(tmp) do
                --     container:AddChild(child)

                --     local slot = child.Slot
                --     slot:SetHorizontalAlignment(horizontalAlignment)
                -- end
            end

        else
            -- use nameslot as container
            slot:AddChild(component)
        end

        self:RegisterComponent(id, component)
    end
end

function IVTemplateBase:AddComponentAtPosition2(id, component, pos, order)
    order = setdefault(order, EIVCompOrder.MaskLayerOrder)
    -- 获取该 itemview 层次位置信息
    local compPositionInfo = Module.CommonWidget.Field:_GetCompPositionInfo(self.itemviewMode)

    -- 先将组件加入到根画布中
    -- self.itemviewPanel:AddChild(component)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(component)
    if not canvasSlot then
        log("Check ItemViewPanel is nil or Check Component Add Failed")
        component:Collapsed()
        return
    end
    local TestAnchor = FAnchors()
    local zOrder = order
    canvasSlot:SetAutoSize(true)
    canvasSlot:SetZOrder(zOrder)
    if pos == EIVSlotPos.TopLeft then
        -- 寻找配置
        local topleftInfo = compPositionInfo[1]
        if topleftInfo then
            TestAnchor.Minimum = FVector2D(topleftInfo.Anchors.Minimum.X, topleftInfo.Anchors.Minimum.Y)
            TestAnchor.Maximum = FVector2D(topleftInfo.Anchors.Maximum.X, topleftInfo.Anchors.Maximum.Y)
            canvasSlot:SetAnchors(TestAnchor)
            canvasSlot:SetOffsets(FMargin(topleftInfo.Offset.Left, topleftInfo.Offset.Top, topleftInfo.Offset.Right, topleftInfo.Offset.Bottom))
            canvasSlot:SetAlignment(FVector2D(topleftInfo.Alignment.X, topleftInfo.Alignment.Y))
            -- canvasSlot:SetAnchors(topleftInfo.Anchors)
            -- canvasSlot:SetOffsets(topleftInfo.Offset)
            -- canvasSlot:SetAlignment(topleftInfo.Alignment)
        else
            canvasSlot:SetAnchors(topAnchor)
            canvasSlot:SetOffsets(FMargin(9, 3, 9, 30))
            canvasSlot:SetAlignment(FVector2D(0, 0))
        end
    elseif pos == EIVSlotPos.TopRight then
        local toprightInfo = compPositionInfo[2]
        if toprightInfo then
            TestAnchor.Minimum = FVector2D(toprightInfo.Anchors.Minimum.X, toprightInfo.Anchors.Minimum.Y)
            TestAnchor.Maximum = FVector2D(toprightInfo.Anchors.Maximum.X, toprightInfo.Anchors.Maximum.Y)
            canvasSlot:SetAnchors(TestAnchor)
            canvasSlot:SetOffsets(FMargin(toprightInfo.Offset.Left, toprightInfo.Offset.Top, toprightInfo.Offset.Right, toprightInfo.Offset.Bottom))
            canvasSlot:SetAlignment(FVector2D(toprightInfo.Alignment.X, toprightInfo.Alignment.Y))
            -- canvasSlot:SetAnchors(toprightInfo.Anchors)
            -- canvasSlot:SetOffsets(toprightInfo.Offset)
            -- canvasSlot:SetAlignment(toprightInfo.Alignment)
        else
            canvasSlot:SetAnchors(topRightAnchor)
            canvasSlot:SetPosition(FVector2D(-9, 3))
            canvasSlot:SetAlignment(FVector2D(1, 0))
        end
    elseif pos == EIVSlotPos.BottomLeft then
        local bottomleftInfo = compPositionInfo[3]
        if bottomleftInfo then
            TestAnchor.Minimum = FVector2D(bottomleftInfo.Anchors.Minimum.X, bottomleftInfo.Anchors.Minimum.Y)
            TestAnchor.Maximum = FVector2D(bottomleftInfo.Anchors.Maximum.X, bottomleftInfo.Anchors.Maximum.Y)
            canvasSlot:SetAnchors(TestAnchor)
            canvasSlot:SetOffsets(FMargin(bottomleftInfo.Offset.Left, bottomleftInfo.Offset.Top, bottomleftInfo.Offset.Right, bottomleftInfo.Offset.Bottom))
            canvasSlot:SetAlignment(FVector2D(bottomleftInfo.Alignment.X, bottomleftInfo.Alignment.Y))
            -- canvasSlot:SetAnchors(bottomleftInfo.Anchors)
            -- canvasSlot:SetOffsets(bottomleftInfo.Offset)
            -- canvasSlot:SetAlignment(bottomleftInfo.Alignment)
        else
            canvasSlot:SetAnchors(bottomAnchor)
            canvasSlot:SetOffsets(FMargin(9, -3, 9, 48))
            canvasSlot:SetAlignment(FVector2D(0, 1))
        end
    elseif pos == EIVSlotPos.BottomRight then
        local bottomrightInfo = compPositionInfo[4]
        if bottomrightInfo then
            TestAnchor.Minimum = FVector2D(bottomrightInfo.Anchors.Minimum.X, bottomrightInfo.Anchors.Minimum.Y)
            TestAnchor.Maximum = FVector2D(bottomrightInfo.Anchors.Maximum.X, bottomrightInfo.Anchors.Maximum.Y)
            canvasSlot:SetAnchors(TestAnchor)
            canvasSlot:SetOffsets(FMargin(bottomrightInfo.Offset.Left, bottomrightInfo.Offset.Top, bottomrightInfo.Offset.Right, bottomrightInfo.Offset.Bottom))
            canvasSlot:SetAlignment(FVector2D(bottomrightInfo.Alignment.X, bottomrightInfo.Alignment.Y))
            -- canvasSlot:SetAnchors(bottomrightInfo.Anchors)
            -- canvasSlot:SetOffsets(bottomrightInfo.Offset)
            -- canvasSlot:SetAlignment(bottomrightInfo.Alignment)
        else
            canvasSlot:SetAnchors(bottomRightAnchor)
            canvasSlot:SetPosition(FVector2D(-9, -3))
            canvasSlot:SetAlignment(FVector2D(1, 1))
        end
    elseif pos == EIVSlotPos.Top then
        canvasSlot:SetAnchors(topAnchor)
        canvasSlot:SetOffsets(FMargin(9, 9, 0, 9))
        canvasSlot:SetAlignment(FVector2D(0, 0))
    else
        canvasSlot:SetAnchors(commonAnchor)
        canvasSlot:SetOffsets(commonOffset)
    end
    self:RegisterComponent(id, component)
end

-- 当某个位置的components > 1时，需要加上verticalbox来显示所有components
function IVTemplateBase:SortComponents()
    
end

function IVTemplateBase:PreloadComponent(id, componentNameID, pos, order)
    local component = self:FindOrAdd(id, componentNameID, pos, order)
    if component then
        component:EnableComponent(false)
    end
end

---@return IVComponentBase
function IVTemplateBase:FindOrAdd(id, componentNameID, pos, order, bUseGlobalRegister)
    local component = self:GetComponent(id)
    if not component then
        -- 采用新接口
        if bUseGlobalRegister then
            component = self:AddGlobalRegisterSubUI(id, componentNameID, bUseGlobalRegister, self.itemviewPanel)
        else
            component = Facade.UIManager:CreateSubUIBindOwner(self, componentNameID)
            self.itemviewPanel:AddChild(component)
        end
        if component then
            self:AddComponentAtPosition2(id, component, pos, order)
            component:SetComponentPos(pos)
            if self.item then
                component:BindItem(self.item)
            end
        else
            logwarning("[IVTemplateBase]: uiIns is nil, please check")
        end
    end

    return component
end

---@return IVComponentBase
function IVTemplateBase:GetComponent(id)
    return self._itemComponents[id]
end

---@return IVComponentBase
function IVTemplateBase:ClearComponent(id)
    self._itemComponents[id] = nil
    self:RemoveGlobalRegisterSubUI(id, true)
end

function IVTemplateBase:EnableComponent(id, bEnable, bReturn2GlobalRegisterIfDisable)
    local component = self:GetComponent(id)
    if isinvalid(component) then
        return component
    end

    component:EnableComponent(bEnable)

    bReturn2GlobalRegisterIfDisable = setdefault(bReturn2GlobalRegisterIfDisable, false)
    if not bEnable and bReturn2GlobalRegisterIfDisable then
        self:ClearComponent(id)
    end

    return component
end

function IVTemplateBase:ClearGlobalRegisterComponents()
    for key, value in pairs(self._componentsRefrence) do
        self._itemComponents[key] = nil
    end
    CommonRegisterUIView.ClearGlobalRegisterComponents(self)
end

function IVTemplateBase:RefreshComponent(id)
    local component = self:GetComponent(id)
    if component and component:IsEnabled() then
        component:RefreshComponent(self.RTIParamData)
    end
end

function IVTemplateBase:ItemViewOnHover()
    -- logerror("[IVTemplateBase]itemviewMode:", self.itemviewMode, "OnHovered")
    if self._hoverWidget then
        self._hoverWidget:Visible()
    end
end

function IVTemplateBase:ItemViewOnUnHover()
    -- logerror("[IVTemplateBase]itemviewMode:", self.itemviewMode, "OnUnHovered")
    if self._hoverWidget then
        self._hoverWidget:Collapsed()
    end
end

function IVTemplateBase:InitItem(item)
    self.item = item

    -- 只有有item才需要刷新界面
    if item then
        for id, component in pairs(self._itemComponents) do
            component:BindItem(item)
        end
        self:RefreshView()
    else
        for id, component in pairs(self._itemComponents) do
            if component.OnLoseItem then
                component:OnLoseItem()
            end
        end
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 记录当前使用该控件的slotView
function IVTemplateBase:InitSlotView(slotView)
    if IsHD() then
        self.slotView = slotView
    end
end
--END MODIFICATION

function IVTemplateBase:AddCommercializeShadingComp()
    if self.item and self.item.commercializeType > 0 then
        local commercializeComp = self:FindOrAdd(EComp.CommercializeShading, UIName2ID.IVCommercializeShadingComponent, EIVSlotPos.BgLayer, EIVCompOrder.DefaultOrder)
        commercializeComp:RefreshComponent(self.RTIParamData)
    end
end

function IVTemplateBase:BindPostRefreshFunc(fPostRefreshFunc)
    self._fPostRefreshFunc = fPostRefreshFunc
end

function IVTemplateBase:BindCustomOnClicked(fOnClicked, caller, ...)
    if fOnClicked then
        -- 绑定点击事件会默认可见
        self:Visible()
        local cb = SafeCallBack(fOnClicked, caller, ...)
        self.OnClicked = function()
            LogUtil.LogWarning("[finnywxu] CLICKED ", self.__cppinst)
            cb()
        end
        self:SetCppValue("bHandleClick" , true)
    end
end

function IVTemplateBase:UnBindCustomOnClicked()
    if not hasdestroy(self) then
        self:HitTestInvisible()
        self:SetCppValue("bHandleClick" , false)
    end
end

function IVTemplateBase:RefreshView()
    for id, component in pairs(self._itemComponents) do
        if component:IsEnabled() then
            component:RefreshComponent(self.RTIParamData)
        end
    end

    if self._fPostRefreshFunc then
        self._fPostRefreshFunc(self)
    end
end

function IVTemplateBase:SetAnchorIfOverFlow()
    -- 目前仅针对左上角名字进行超框处理，其他地方以后有需求再说
    if not self._nameComp then
        return
    end
    -- local fSetAnchorFunc = function ()
    --     if hasdestroy(self._nameComp) then
    --         return
    --     end
    --     local compSize = self._nameComp:GetCompSize().X
    --     local compSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._nameComp)
    --     local widgetWidth
    --     if self.GetCachedGeometry then
    --         local widgetGeometry = self:GetCachedGeometry()
    --         widgetWidth = widgetGeometry and widgetGeometry:GetLocalSize().X
    --     end
    --     if (compSize and compSize ~= 0) and (widgetWidth or widgetWidth ~= 0) then
    --         self:SetAnchorIfOverFlowByFrame()
    --     else
    --         Facade.LuaFramingManager:RegisterFrameTask(fSetAnchorFunc)
    --     end
    -- end
    Facade.LuaFramingManager:RegisterFrameTask(self._SetAnchorFunc, self)
end

function IVTemplateBase:_SetAnchorFunc()
    if hasdestroy(self._nameComp) then
        return
    end
    local compSize = self._nameComp:GetCompSize().X
    local compSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._nameComp)
    local widgetWidth
    if self.GetCachedGeometry then
        local widgetGeometry = self:GetCachedGeometry()
        widgetWidth = widgetGeometry and widgetGeometry:GetLocalSize().X
    end
    if (compSize and compSize ~= 0) and (widgetWidth or widgetWidth ~= 0) then
        -- 分帧只能确保文本是否被渲染出来，但是文本经过重设之后是否超框还是得做延时处理
        Timer.DelayCall(0.1, function()
            self:SetAnchorIfOverFlowByFrame()
        end)
    else
        Facade.LuaFramingManager:RegisterFrameTask(self._SetAnchorFunc, self)
    end
end

-- 分帧设置防止误差
function IVTemplateBase:SetAnchorIfOverFlowByFrame()
    if hasdestroy(self._nameComp) then
        return
    end
    local compSize = self._nameComp:GetCompSize().X
    local compSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._nameComp)
    local widgetWidth, widgetLenth
    if self.GetCachedGeometry then
        local widgetGeometry = self:GetCachedGeometry()
        widgetWidth = widgetGeometry and widgetGeometry:GetLocalSize().X
        widgetLenth = widgetGeometry and widgetGeometry:GetLocalSize().Y
    end
    -- 设置不同的锚点
    local topleftInfo = Module.CommonWidget.Field:_GetCompPositionInfo(self.itemviewMode)
    --[[  itemview以前的bug内容
    -- if compSize and compSize > 0 and widgetWidth and widgetWidth > 0 and compSize > widgetWidth then
    --     self._nameComp:SetEllipsis(true)
    --     local anchor = topleftInfo[5] and topleftInfo[5].Anchors or topAnchor
    --     local margin = topleftInfo[5] and topleftInfo[5].Offset or FMargin(0, 0, 0, 0)
    --     compSlot:SetAnchors(anchor)
    --     compSlot:SetOffsets(margin)
    -- else
    --     self._nameComp:SetEllipsis(false)
    --     compSlot:SetAnchors(topleftInfo[1].Anchors)
    --     compSlot:SetOffsets(topleftInfo[1].Offset)
    -- end
    ]]--

    local anchor = FAnchors()
    local margin = FMargin(0, 0, 0, 0)
    -- 为了防止名字和其他组件重叠，需要进行调整
    -- 1. 当超框时，直接采用配置的数据
    -- 2. 当组件长度大于itemview宽度-48时（重构设置的阈值），rightoffset设置为超框后的rightoffset配置。
    if compSize and compSize > 0 and widgetWidth and widgetWidth > 0 and compSize > (widgetWidth - 48) then
        self._nameComp:SetEllipsis(true)
        if topleftInfo[5] then
            anchor.Minimum = FVector2D(topleftInfo[5].Anchors.Minimum.X, topleftInfo[5].Anchors.Minimum.Y)
            anchor.Maximum = FVector2D(topleftInfo[5].Anchors.Maximum.X, topleftInfo[5].Anchors.Maximum.Y)
            margin = FMargin(topleftInfo[5].Offset.Left, topleftInfo[5].Offset.Top, topleftInfo[5].Offset.Right, topleftInfo[5].Offset.Bottom)
        end
        compSlot:SetAnchors(anchor)
        compSlot:SetOffsets(margin)
    else
        self._nameComp:SetEllipsis(false)
        if topleftInfo[1] then
            anchor.Minimum = FVector2D(topleftInfo[1].Anchors.Minimum.X, topleftInfo[1].Anchors.Minimum.Y)
            anchor.Maximum = FVector2D(topleftInfo[1].Anchors.Maximum.X, topleftInfo[1].Anchors.Maximum.Y)
            margin = FMargin(topleftInfo[1].Offset.Left, topleftInfo[1].Offset.Top, topleftInfo[1].Offset.Right, topleftInfo[1].Offset.Bottom)
        end
        compSlot:SetAnchors(anchor)
        compSlot:SetOffsets(margin)
    end
end

function IVTemplateBase:UpdateComponentFromItemID(itemID)
    for id, component in pairs(self._itemComponents) do
        if component:IsEnabled() then
            component:UpdateComponentFromItemID(itemID)
        end
    end
end

-- function IVTemplateBase:SetSelected(bSelected)
--     for id, component in pairs(self._itemComponents) do
--         if component:IsEnabled() then
--             component:SetSelected(bSelected)
--         end
--     end
-- end

function IVTemplateBase:SetHandleDrag(bHandleDrag)
    self:SetCppValue("bHandleDrag", bHandleDrag)
end

function IVTemplateBase:SetHandleDoubleClick(bHandleDoubleClick)
    self:SetCppValue("bHandleDoubleClick", bHandleDoubleClick)
end

function IVTemplateBase:SetHandleClickR(bHandleClickR)
    self:SetCppValue("bHandleClickR", bHandleClickR)
end

function IVTemplateBase:SetHandleClickMid(bHandleClickMid)
    self:SetCppValue("bHandleClickMid", bHandleClickMid)
end

function IVTemplateBase:SetRootSize(width, height)
    if self.wtRootSizeBox then
        self.wtRootSizeBox:SetWidthOverride(width)
        self.wtRootSizeBox:SetHeightOverride(height)
    end
    if self.SetSize then
        self:SetSize(width, height)
    end
end

-- 更改默认组件的itemviewmode（需要将另外的模板的表现套用至此模板上时使用）
function IVTemplateBase:ChangeDefaultMode(itemviewMode)
    -- 若此模板的mode进行改变时，将默认组件得mode进行更新
    self.itemviewMode = setdefault(itemviewMode, self.itemviewMode)
    -- 根据itemview尺寸更改itemviewmode
    for _, component in pairs(self._itemComponents) do
        component:GetItemViewMode(self.itemviewMode)
    end
end

function IVTemplateBase:ChangeDefaultSize(Size)
    self:SetRootSize(Size.X, Size.Y)
end

function IVTemplateBase:PlayCommonAnim(animName, index)
    index = setdefault(index, 0)
    if self[animName] then
        self:Collapsed()
        local delay = self[animName]:GetEndTime()
        Timer.DelayCall(delay * index, function ()
            self:PlayAnimationForward(self[animName], 1.0, true)
            self:Visible()
        end, self)
    end
end

-- 针对未下载枪械皮肤资源的道具，是否采用原皮处理
function IVTemplateBase:UseOriginalSkin()
    local iconComponent = self._itemComponents[EComp.ItemIcon]
    if iconComponent and iconComponent.SetUseOriginalSkin then
        iconComponent:SetUseOriginalSkin()
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private

function IVTemplateBase:GetSlotByPos(pos)
    local slot = self._posMapping[pos]
    return slot
end

---设置pos位置的层级（目前只用于队友绑定时覆盖mask）
---@param pos EIVSlotPos 槽位位置
---@param order number 层级
function IVTemplateBase:SetPosOrder(pos,order)
    local slot = self:GetSlotByPos(pos)
    if slot then
        local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(slot)
        if canvasSlot then
            canvasSlot:SetZOrder(order)
        end
    end
end

function IVTemplateBase:_InitSlotMapping()
    local slotItemView = self.WBP_SlotItemView
    if slotItemView then
        self._posMapping[EIVSlotPos.BgLayer] = slotItemView.Slot_Bg
        self._posMapping[EIVSlotPos.IconLayer] = slotItemView.Slot_Icon
        self._posMapping[EIVSlotPos.BorderLayer] = slotItemView.Slot_Border
        self._posMapping[EIVSlotPos.BottomLeft] = slotItemView.Slot_L0
        self._posMapping[EIVSlotPos.BottomRight] = slotItemView.Slot_R0
        self._posMapping[EIVSlotPos.TopLeft] = slotItemView.Slot_L1
        self._posMapping[EIVSlotPos.TopRight] = slotItemView.Slot_R1
        self._posMapping[EIVSlotPos.MaskLayer] = slotItemView.Slot_Mask
        self._posMapping[EIVSlotPos.StateLayer] = slotItemView.Slot_State
        self._posMapping[EIVSlotPos.Extra] = slotItemView.Slot_Extra
    else
        self.itemviewPanel = self:Wnd("ItemViewPanel", UIWidgetBase)
        self.wtRootSizeBox = self:Wnd("wtRootSizeBox", UISizeBox)
    end
end

function IVTemplateBase:_InitRootSize()
    local slotItemView = self.WBP_SlotItemView
    if slotItemView then
        self._wtRootSizeBox = slotItemView.wtRootSizeBox
    end
end

function IVTemplateBase:_RemoveSlot()
    self._posMapping = nil
    self._wtRootSizeBox = nil
end

function IVTemplateBase:_ChangeModeBySize()
    if self.wtRootSizeBox then
        local width, height = self.wtRootSizeBox.widthOverride, self.wtRootSizeBox.heightOverride
        for style, config in ipairs(CommonWidgetField.itemviewConfig) do
            if width == config.BoxSize.X and height == config.BoxSize.Y then
                return style
            end
        end
        return
    end
end

function IVTemplateBase:_IsCommonItemWidget()
    return self.itemviewMode == CommonWidgetConfig.EIVItemViewMode.CommonViewItemView
        or self.itemviewMode == CommonWidgetConfig.EIVItemViewMode.CommonViewItemViewA
        or self.itemviewMode == CommonWidgetConfig.EIVItemViewMode.CommonViewItemViewB
        or self.itemviewMode == CommonWidgetConfig.EIVItemViewMode.CommonViewItemViewC
end

function IVTemplateBase:_ChangeComponentPos(component)
    local posInfo = component:GetComponentPos()
    local compPositionInfo = Module.CommonWidget.Field:GetCompPositionInfo(self.itemviewMode)

    -- 先将组件加入到根画布中
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(component)
    if posInfo == EIVSlotPos.TopLeft then
        -- 寻找配置
        local topleftInfo = compPositionInfo[1]
        if topleftInfo then
            canvasSlot:SetAnchors(topleftInfo.Anchors)
            canvasSlot:SetOffsets(topleftInfo.Offset)
            canvasSlot:SetAlignment(topleftInfo.Alignment)
        end
    elseif posInfo == EIVSlotPos.TopRight then
        local toprightInfo = compPositionInfo[2]
        if toprightInfo then
            canvasSlot:SetAnchors(toprightInfo.Anchors)
            canvasSlot:SetOffsets(toprightInfo.Offset)
            canvasSlot:SetAlignment(toprightInfo.Alignment)
        end
    elseif posInfo == EIVSlotPos.BottomLeft then
        local bottomleftInfo = compPositionInfo[3]
        if bottomleftInfo then
            canvasSlot:SetAnchors(bottomleftInfo.Anchors)
            canvasSlot:SetOffsets(bottomleftInfo.Offset)
            canvasSlot:SetAlignment(bottomleftInfo.Alignment)
        end
    elseif posInfo == EIVSlotPos.BottomRight then
        local bottomrightInfo = compPositionInfo[4]
        if bottomrightInfo then
            canvasSlot:SetAnchors(bottomrightInfo.Anchors)
            canvasSlot:SetOffsets(bottomrightInfo.Offset)
            canvasSlot:SetAlignment(bottomrightInfo.Alignment)
        end
    end
end

--endregion
-----------------------------------------------------------------------

function IVTemplateBase:SetCommonHoverBgHideFreeAnalogCursorHideFocusContentRoot(bHideFreeAnalogCursorHideFocusContentRoot)
    if isvalid(self) and isvalid(self.WBP_CommonHoverBg) then
        if self.WBP_CommonHoverBg.SetHideFreeAnalogCursorHideFocusContentRoot then
            self.WBP_CommonHoverBg:SetHideFreeAnalogCursorHideFocusContentRoot(bHideFreeAnalogCursorHideFocusContentRoot)
        end
    end
end

return IVTemplateBase