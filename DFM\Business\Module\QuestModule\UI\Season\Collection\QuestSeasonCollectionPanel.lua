----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local QuestSeasonCollectorItem = require "DFM.Business.Module.QuestModule.UI.Season.Collection.QuestSeasonCollectorItem"
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local ECheckButtonState = import "ECheckButtonState"
local EUINavigation = import("EUINavigation")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local UGPInputHelper = import "GPInputHelper"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
-- END MODIFICATION
---@class QuestSeasonCollectionPanel : LuaUIBaseView
local QuestSeasonCollectionPanel = ui("QuestSeasonCollectionPanel")

function QuestSeasonCollectionPanel:Ctor()
    self._wtTipBtn = self:Wnd("wtTipsBtn", DFCheckBoxOnly)
    self._wtTipBtn:SetCallback(self.OnTipButtonClick, self)
    self._wtTimeTB = self:Wnd("TextBlock_1", UITextBlock)

    self._wtCollectGainTB = self:Wnd("DFTextBlock_287", UIWidgetBase)
    self._wtPercentImg = self:Wnd("wtScheduleImg", UIImage)
    self._wtIconItem = self:Wnd("WBP_CommonItemTemplate", IVCommonItemTemplate)
    self._wtGainTipTB = self:Wnd("DFTextBlock", UITextBlock)
    self._wtRewardBtn = self:Wnd("WBP_CommonButtonV2S2_91", DFCommonButtonOnly)
    self._wtRewardBtn:Event("OnClicked", self.OnRewardListBtnClick, self)

    self._wtNormalPanel = self:Wnd("DFCanvas_Normal", UIWidgetBase)
    self._wtItem1 = self:Wnd("WBP_SeasonalTasks_EntrustmentItem", QuestSeasonCollectorItem)
    self._wtItem2 = self:Wnd("WBP_SeasonalTasks_EntrustmentItem_1", QuestSeasonCollectorItem)
    self._wtItem3 = self:Wnd("WBP_SeasonalTasks_EntrustmentItem_2", QuestSeasonCollectorItem)

    self._wtRefrshTB = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtRefreshBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtRefreshBtn:Event("OnClicked", self.OnRefreshBtnClick, self)

    self._wtEmptySlot = self:Wnd("EmptySlot", UIWidgetBase)
    self._wtBg1 = self:Wnd("Bg_1", UIWidgetBase)
    self._wtBg2 = self:Wnd("Bg_4", UIWidgetBase)

    self._collectorIds = {}
    ---@type QuestCollectorStruct
    self.dataInfo = {}
    self.curProgress = 0
    self.targetProgress = 0
    self._timerHandle = nil
    self.nextRewardTable = nil
end

function QuestSeasonCollectionPanel:OnInitExtraData()
    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Quest.Config.Loc.QuestCollectorTitle)
end

------------------------------------ Override function ------------------------------------
-- UI打开时触发
function QuestSeasonCollectionPanel:OnOpen()
    --self:_AddEventListener()
end

function QuestSeasonCollectionPanel:OnShow()
    local tipsRecordServer = Server.TipsRecordServer
    tipsRecordServer:SetBoolean(tipsRecordServer.keys.QuestCollectorChecked, true)
    self.targetProgress = 0
    self:_OnRefreshView()
    self:_AddEventListener()

    self:_InitShortcuts()
    self:PlayAnimation(self.WBP_SeasonalTasks_EntrustmentPanel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    Module.Quest:OpenSeasonalQuestTutorial(
        Module.Quest.Config.EQuestSeasonGuideType.Collection,
        Server.TipsRecordServer.keys.QuestSeasonCollector
    )
end

function QuestSeasonCollectionPanel:OnHide()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    self.targetProgress = 0
    self:_RemoveEventListener()
    self:_RemoveShortcuts()
    self.nextRewardTable = nil
end

-- UI关闭时触发
function QuestSeasonCollectionPanel:OnClose()
    self:_RemoveEventListener()
    self._wtIconItem:StopIVAnimation("WBP_CommonItemTemplate_in_special_01")
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end
------------------------------------ Private function ------------------------------------
function QuestSeasonCollectionPanel:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtSeasonCollectorRefresh, self._OnRefreshCallback, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtQuestSeasonCollectorTimeRefresh, self._OnRefreshTimePanel, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtSeasonCollectorRewardRefresh, self._OnRefreshRewardPanel, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtSeasonCollectorLockRefresh, self._OnRefreshLockTip, self)
end

function QuestSeasonCollectionPanel:_OnRefreshCallback(isRefresh, isSubmit)
    if isRefresh then
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_SeasonMission_Collector_Progress_Refresh)
        Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.QuestSeasonCollectorRefreshSuccess)
    end
    self:_OnRefreshView(isRefresh, isSubmit)
end

function QuestSeasonCollectionPanel:_RemoveEventListener()
    self:RemoveLuaEvent(Server.QuestServer.Events.evtSeasonCollectorRefresh)
    self:RemoveLuaEvent(Server.QuestServer.Events.evtQuestSeasonCollectorTimeRefresh)
    self:RemoveLuaEvent(Server.QuestServer.Events.evtSeasonCollectorRewardRefresh)
    self:RemoveLuaEvent(Server.QuestServer.Events.evtSeasonCollectorLockRefresh)
end

function QuestSeasonCollectionPanel:_OnRefreshView(isRefresh, isSubmit)
    isRefresh = setdefault(isRefresh, false)
    isSubmit = setdefault(isSubmit, false)
    self.dataInfo = Server.QuestServer:GetCollectorDataInfo()
    if self.dataInfo == nil then
        self:SetEmptyState(true)
        return
    end
    local collectorList = self.dataInfo:GetCollectorList()
    if table.isempty(collectorList) then
        self:SetEmptyState(true)
    else
        self:SetEmptyState(false)
        for index = 1, 3 do
            if collectorList[index] then
                local wbp = self["_wtItem" .. tostring(index)]
                if wbp then
                    wbp:SelfHitTestInvisible()
                    wbp:RefreshView(collectorList[index], isRefresh)
                else
                    logerror("QuestSeasonCollectionPanel:_OnRefreshView wbp is nil  index = ", index)
                end
            else
                self["_wtItem" .. tostring(index)]:Collapsed()
            end
        end
    end

    if self.dataInfo.refreshRemaindCount > 0 then
        self._wtRefrshTB:SetText(
            string.format(Module.Quest.Config.Loc.QuestCollectorRefreshCount, self.dataInfo.refreshRemaindCount)
        )
        self._wtRefreshBtn:SetIsEnabled(true)
    else
        self._wtRefreshBtn:SetIsEnabled(false)
    end

    self:_OnRefreshTimePanel()

    self:_OnRefreshRewardPanel(isSubmit)
end

function QuestSeasonCollectionPanel:_OnRefreshRewardPanel(bSubmitSuccess)
    local maxCompleteCount = 0
    self.nextRewardTable = nil
    local maxCollectorRewardID = 0
    local rewardsList = self.dataInfo:GetCollectorRewards(Server.QuestServer.curSeasonId)
    for _, value in ipairs(rewardsList) do
        if value.CollectorCount > maxCompleteCount then
            maxCompleteCount = value.CollectorCount
            maxCollectorRewardID = value.CollectorRewardId
        end

        if not self.dataInfo:IsAlreadyReward(value.CollectorRewardId) and not self.nextRewardTable then
            self.nextRewardTable = value
        end
    end

    self._wtCollectGainTB:SetText(string.format("%s/%s", self.dataInfo.finishCount, maxCompleteCount))
    if maxCompleteCount > 0 then
        if bSubmitSuccess and self.targetProgress >= 0 and self.targetProgress < self.dataInfo.finishCount / maxCompleteCount then
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_SeasonMission_Collector_Progress_Increase)
        end
        --- 百分比
        self.targetProgress = self.dataInfo.finishCount / maxCompleteCount
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end
        if self.targetProgress > 0 then
            self._timerHandle = Timer:NewIns(0.01, 0)
            self._timerHandle:AddListener(self._OnUpdateProgressAnim, self)
            self._timerHandle:Start()
        else
            local mat = self._wtPercentImg:GetDynamicMaterial()
            if mat then
                mat:SetScalarParameterValue("Progress", 0)
            end
        end
    end

    if self.dataInfo.finishCount == maxCompleteCount and self.dataInfo:IsAlreadyReward(maxCollectorRewardID) then
        self.nextRewardTable = Facade.TableManager:GetRowByKey("CollectorRewards", tostring(maxCollectorRewardID))
    end

    if self.nextRewardTable then
        local itemBase = QuestLogic._GenerateRewardItem(self.nextRewardTable.RewardIdArr[1])
        self._wtIconItem:InitItem(itemBase)
        if self.dataInfo:IsAlreadyReward(self.nextRewardTable.CollectorRewardId) then
            self._wtIconItem:SetAlreadyGetState()
            self._wtIconItem:EnableComponent(EComp.GetMask, true)
        else
            self._wtIconItem:EnableComponent(EComp.GetMask, false)
        end

        self._wtIconItem:StopIVAnimation("WBP_CommonItemTemplate_in_special_01")
        if
            self.dataInfo.finishCount >= self.nextRewardTable.CollectorCount and
                not self.dataInfo:IsAlreadyReward(self.nextRewardTable.CollectorRewardId)
         then
            self._wtIconItem:PlayIVAnimation(
                "WBP_CommonItemTemplate_in_special_01",
                0,
                EUMGSequencePlayMode.Forward,
                1,
                true
            )
        end

        -- 绑定奖励点击回调函数
        local function fItemViewClick()
            if
                self.dataInfo.finishCount < self.nextRewardTable.CollectorCount or
                    self.dataInfo.finishCount > maxCompleteCount or
                    self.dataInfo:IsAlreadyReward(self.nextRewardTable.CollectorRewardId)
             then
                self._wtIconItem:_SetSelected(true)
                Module.ItemDetail:OpenItemDetailPanel(
                    itemBase,
                    self._wtIconItem,
                    nil,
                    nil,
                    nil,
                    nil,
                    nil,
                    nil,
                    nil,
                    function()
                        self._wtIconItem:_SetSelected()
                    end
                )
                return
            end
            QuestLogic.DoGetSeasonCollectorRewards(self.nextRewardTable.CollectorRewardId)
        end

        self._wtIconItem:BindCustomOnClicked(fItemViewClick)

        self._wtGainTipTB:SetText(
            string.format(
                Module.Quest.Config.Loc.QuestCollectorRewardProgress,
                self.dataInfo.finishCount,
                self.nextRewardTable.CollectorCount,
                self.nextRewardTable.CollectorCount
            )
        )
    end

    if self.dataInfo.finishCount > 0 and self.dataInfo.finishCount == maxCompleteCount then
        -- Module.Quest:CloseSeasonCollectorTimer()
        self._wtTimeTB:SetText("--")
        if self.dataInfo.refreshRemaindCount <= 0 then
            self._wtRefrshTB:SetText(
                string.format(Module.Quest.Config.Loc.QuestCollectorRefreshCount, self.dataInfo.refreshRemaindCount)
            )
        end
    end

    if IsHD() then
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInputType)
    end
end

function QuestSeasonCollectionPanel:_OnRefreshLockTip(id, ret)
    if ret then
        Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.QuestCollectorLockSuccessTip)
    end
end

function QuestSeasonCollectionPanel:_OnUpdateProgressAnim()
    local mat = self._wtPercentImg:GetDynamicMaterial()
    if mat then
        self.curProgress = math.lerp(self.curProgress, self.targetProgress, 0.1)
        if math.abs(self.curProgress - self.targetProgress) <= 0.01 then
            self.curProgress = self.targetProgress
        end
        mat:SetScalarParameterValue("Progress", self.curProgress)
        if self.curProgress >= self.targetProgress then
            if self._timerHandle then
                self._timerHandle:Release()
                self._timerHandle = nil
            end
        end
    end
end

function QuestSeasonCollectionPanel:_OnRefreshTimePanel()
    if self.dataInfo == nil then
        return
    end
    local timeStr = ""
    local delta = self.dataInfo:GetAutoRefreshTimeDelta()
    if delta > 0 then
        local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(delta)
        if day > 0 then
            timeStr = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min)
        elseif hour > 0 then
            timeStr = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min)
        elseif min > 0 then
            timeStr = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min)
        end
        self._wtTimeTB:SetText(timeStr)
        if self.dataInfo.refreshRemaindCount <= 0 then
            self._wtRefrshTB:SetText(timeStr)
        end
    else
        Server.QuestServer:CollectorTaskRefreshReq()
    end
end

function QuestSeasonCollectionPanel:OnRefreshBtnClick()
    if self.dataInfo.refreshRemaindCount > 0 then
        if self.dataInfo:IsAllTaskBeLocked() then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.QuestCollectorAllLockTip)
            return
        end
        Server.QuestServer:CollectorTaskRefreshReq()
    end
end

function QuestSeasonCollectionPanel:OnTipButtonClick()
    Module.Quest:OpenSeasonalQuestTutorial(Module.Quest.Config.EQuestSeasonGuideType.Collection, nil)
    self._wtTipBtn:SetIsChecked(false, false)
end

function QuestSeasonCollectionPanel:OnRewardListBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonCollectorRewardPanel)
end

function QuestSeasonCollectionPanel:OnCollectorRewardReq()
    if self.nextRewardTable then
        QuestLogic.DoGetSeasonCollectorRewards(self.nextRewardTable.CollectorRewardId)
    end
end

function QuestSeasonCollectionPanel:SetEmptyState(isEmpty)
    if self._wtEmptySlot then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtEmptySlot)
        self._wtEmptySlot:Collapsed()
    end

    if isEmpty then
        self._wtNormalPanel:Collapsed()
        self._wtBg1:SelfHitTestInvisible()
        self._wtBg2:SelfHitTestInvisible()
        if self._wtEmptySlot then
            self._wtEmptySlot:SelfHitTestInvisible()
            local weakUIIns, instanceID =
                Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptySlot)
            local emptyBg = getfromweak(weakUIIns)
            if emptyBg then
                emptyBg:Visible()
                emptyBg:BP_SetText(Module.Quest.Config.Loc.QuestCollectorEmpty)
                emptyBg:BP_SetTypeWithParam(1)
            end
        end
    else
        self._wtNormalPanel:SelfHitTestInvisible()
        self._wtBg1:Collapsed()
        self._wtBg2:Collapsed()
    end
end

function QuestSeasonCollectionPanel:_InitShortcuts()
    if IsHD() then
        --WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

        if not self._navGroups then
            self._navGroups = {
                one = WidgetUtil.RegisterNavigationGroup(self._wtItem1._wtScrollGridBox, self, "Hittest"),
                two = WidgetUtil.RegisterNavigationGroup(self._wtItem2._wtScrollGridBox, self, "Hittest"),
                three = WidgetUtil.RegisterNavigationGroup(self._wtItem3._wtScrollGridBox, self, "Hittest"),
                four = WidgetUtil.RegisterNavigationGroup(self._wtRefreshBtn, self, "Hittest"),
                five = WidgetUtil.RegisterNavigationGroup(self._wtIconItem, self, "Hittest")
            }

            if self._navGroups.one then
                self._navGroups.one:AddNavWidgetToArray(self._wtItem1._wtScrollGridBox)
                self._navGroups.one:SetScrollRecipient(self._wtItem1._wtScrollGridBox)
            end

            if self._navGroups.two then
                self._navGroups.two:AddNavWidgetToArray(self._wtItem2._wtScrollGridBox)
                self._navGroups.two:SetScrollRecipient(self._wtItem2._wtScrollGridBox)
            end
            if self._navGroups.three then
                self._navGroups.three:AddNavWidgetToArray(self._wtItem3._wtScrollGridBox)
                self._navGroups.three:SetScrollRecipient(self._wtItem3._wtScrollGridBox)
            end
            if self._navGroups.four then
                self._navGroups.four:AddNavWidgetToArray(self._wtRefreshBtn)
            end
            if self._navGroups.five then
                self._navGroups.five:AddNavWidgetToArray(self._wtIconItem)
            end

            --self._navGroups:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroups.one)
        end
        if self._PressAllQuestButton == nil then
            self._PressAllQuestButton =
                self:AddInputActionBinding(
                "QuestCollector",
                EInputEvent.IE_Pressed,
                self.OnRewardListBtnClick,
                self,
                EDisplayInputActionPriority.UI_Pop
            )
            self._wtRewardBtn:SetDisplayInputAction("QuestCollector", true, nil, true)
        end

        -- 绑定多输入设备切换事件
        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle =
                UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(
                CreateCPlusCallBack(self._OnInputTypeChanged, self)
            )
        end

        -- 初始化
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInputType)
    -- END MODIFICATION
    end
end

function QuestSeasonCollectionPanel:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return
    end

    if InputType == EGPInputType.Gamepad then
        self:UpdateBottomBar()
    else
        -- 清空底部按键提示
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end

function QuestSeasonCollectionPanel:UpdateBottomBar()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    -- 设置底部按键提示
    local summaryList = {}
    -- BEGIN MODIFICATION @ VIRTUOS
    table.insert(summaryList, {actionName = "Confirm", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
    if
        self.nextRewardTable and self.dataInfo.finishCount >= self.nextRewardTable.CollectorCount and
            not self.dataInfo:IsAlreadyReward(self.nextRewardTable.CollectorRewardId)
     then
        table.insert(
            summaryList,
            {actionName = "QuestCollectorGet", func = CreateCallBack(self.OnCollectorRewardReq, self)}
        )
    end

    table.insert(summaryList, {actionName = "ScorePrompt", func = CreateCallBack(self.OnTipButtonClick, self)})

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, false)
end

function QuestSeasonCollectionPanel:_RemoveShortcuts()
    if IsHD() then
        if self._PressAllQuestButton then
            self:RemoveInputActionBinding(self._PressAllQuestButton)
            self._PressAllQuestButton = nil
        end
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        if self._navGroups then
            WidgetUtil.RemoveNavigationGroup(self)
            self._navGroups = nil
        end
        Module.CommonBar:RecoverBottomBarInputSummaryList()

        if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end
end

return QuestSeasonCollectionPanel
