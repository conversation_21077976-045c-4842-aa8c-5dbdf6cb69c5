----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------
---
---

local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local UGameplayStatics = import "GameplayStatics"
local UDFMGuideManager = import "DFMGuideManager"
local UDFMGameLoadingManager = import("DFMGameLoadingManager")
local UGPSingletonUtils = import "GPSingletonUtils"
local AGPSubtitleLevelSubsystem = import "GPSubtitleLevelSubsystem"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UGPBattleFieldSystem = import "GPBattleFieldSystem"
local UDFHDKeySettingManager = import "DFHDKeySettingManager"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"
local UDFMGameplayInputManager = import("DFMGameplayInputManager")


--@NOTICE 需要 1<<(enum value) 使用, 在 GuideStageClientOnlyConfig 的 ModeType 中
---@source ../../../../../SOURCE/DFMGAMECORE/DFMGLOBALDEFINES/DATATABLE/GUIDECONFIG.H:175
---@enum EGuideStageClientMode
local EGuideStageClientMode = import "EGuideStageClientMode"

local EDFMGamePlayMode = import "EDFMGamePlayMode"
local EDFMGamePlaySubMode = import "EDFMGamePlaySubMode"
local ECharacterLiveStatus = import "ECharacterLiveStatus"



local function log(...) loginfo("[GuideLogic]", ...) end
local function err(...) logerror("[GuideLogic]", ...) end
local function warning(...) logwarning("[GuideLogic]", ...) end

-- 常规log级，但是临时提到error级别，保证任何包日志都输出
local function logTempError(...)
    --logerror("[GuideLogic]", ...)
    loginfo("[GuideLogic]", ...)
end


local GuideLogic = {}

function GuideLogic.switch(defaultInput)
    local context = {
        _input = defaultInput,
        defaultFunction = nil,
        functions = {},
    }

    context.case = function(match, callback)
        context.functions[match] = callback
        return context
    end

    context.default = function(callback)
        context.defaultFunction = callback
        return context
    end

    context.process = function(input, ...)
        context._input = input
        local case = context.functions[input]
        if case then
            return case(...)
        elseif context.defaultFunction then
            return context.defaultFunction(...)
        end
    end

    context.match = function(...)
        local case = context.functions[context._input]
        if case then
            return case(...)
        elseif context.defaultFunction then
            return context.defaultFunction(...)
        end
    end

    context._check = function(input)
        for _, f in pairs(context.functions) do
            assert(type(f) == "function", "GuideLogic.switch: case must be a function")
        end
    end

    return context
end

function GuideLogic.matchbit(bitMask, bit)
     return (bitMask & bit) ~= 0 
end

local function _GetGlobal(path)
    local value = _G -- Start from the global table
    for part in string.gmatch(path, "[^.]+") do
        value = value[part]
        if not value then
            return nil -- Path is invalid, return nil
        end
    end
    return value
end

local function _GetGlobalUnsafe(path)
    local value = _G -- Start from the global table
    for part in string.gmatch(path, "[^.]+") do
        value = value[part]
        -- if not value then
        --     return nil -- Path is invalid, return nil
        -- end
    end
    return value
end
GuideLogic.GetValueByPath = VersionUtil.IsShipping() and _GetGlobal or _GetGlobalUnsafe





---@param guideStageId EGuideStage
---@return boolean
GuideLogic._DoStartStageGuideInternal = function(guideStageId)
    logTempError("_DoStartStageGuideInternal", guideStageId)

    local guideStageCfg = Module.Guide.Field:GetGuideStageCfg(guideStageId)

    if not VersionUtil.IsShipping() then
        GuideLogic.AssertConstexprStartCondition(guideStageId, "GuideLogic.DoStartStageGuide: invalid stage should be filltered before" .. (guideStageId or "nil"))
    end

    -- 是否处于观战
    local bfSystem = UGPBattleFieldSystem.Get(GetWorld())
    local bIsLiveSpectating = bfSystem.bIsLiveSpectating
    local bIsInObserverMode = bfSystem.bIsInObserverMode
    local bIsOBFreeCamera = bfSystem.bIsOBFreeCamera
    if bIsLiveSpectating or bIsInObserverMode or bIsOBFreeCamera then
        loginfo("DoStartStageGuide fail not player self", guideStageId, bIsLiveSpectating, bIsInObserverMode, bIsOBFreeCamera)
        return false
    end

    -- 判断是否可以打断现有引导
    if GuideLogic.IsGuiding() then
        local curGuideStageId = Module.Guide.Field:GetGuideCfg(Module.Guide.Field:GetCurGuideId()).GuideStageId
        local curGuideStageCfg = Module.Guide.Field:GetGuideStageCfg(curGuideStageId)
        if guideStageCfg.Priority <= curGuideStageCfg.Priority then
            warning("DoStartStageGuide fail, cur in guiding, want start stage:", guideStageId, "cur guiding:", Module.Guide.Field:GetCurGuideId())
            return false
        end
    end

    -- 是否当前输入模式允许执行该引导
    -- 目前只在pc侧做gamepad判断
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if table.contains(guideStageCfg.DisableInputType, EGPInputType.Gamepad) then
                GuideLogic.DoForceEndGuideStage(guideStageId)
                loginfo("DoStartStageGuide fail, cur is disable on gamepad inputtype:", guideStageId)
                return false
            end
        end
    end

    -- 判断回滚逻辑，获取真正要执行的guideId
    local guideId
    local stepMap = Server.TipsRecordServer:GetMap(GuideLogic._GetStageStepKey())
    if stepMap[tostring(guideStageId)] == nil then
        guideId = guideStageCfg.FirstStepGuideId
    else
        local lastGuideCfg = Module.Guide.Field:GetGuideCfg(stepMap[tostring(guideStageId)] + guideStageId * 1000)
        if lastGuideCfg then
            guideId = lastGuideCfg.RollBackGuideId
        else
            guideId = guideStageCfg.FirstStepGuideId
        end
    end

    if GuideLogic.IsEndGuideId(guideId) then
        GuideLogic._OnGuideStageEnd(guideStageCfg)
        return false
    end

    GuideLogic._RecordGuideStageInfo(guideStageId, true)

    -- new player level loading state
    if guideStageCfg.IsNewPlayerGuide then
        log("DoStartStageGuide, set waitPlayGuide", guideStageId)
        Module.Guide.Field.bLoadingGuideRes = true
        GuideLogic.SetOnlyShowGuideState(true, GuideConfig.EGuideShowUIFlag.WaitMaskLoading)
    end

    -- 2025/5/27  mainly for android `back` button disable
    GuideLogic.SetAndroidInputEnable(false)
    local ret = GuideLogic._RealStartGuide(guideId)
    -- 通知外界引导开始
    if ret then
        GuideConfig.EGuideEvent.evtGuideStart:Invoke(guideId)
    end
    return ret
end



---@param guideStageId EGuideStage
---@return boolean
GuideLogic.DoStartStageGuide = function(guideStageId)
    logTempError("DoStartStageGuide", guideStageId)
    if GuideLogic.CheckConstexprStartCondition(guideStageId) then
        return GuideLogic._DoStartStageGuideInternal(guideStageId)
    end
    return false
end

function GuideLogic.CheckConstexprStartCondition(guideStageId)
    local stageCfg = GuideConfig.TableGuideStageConfig[guideStageId]
    if not stageCfg or not stageCfg.IsUse then
        warning("CheckConstexprStartCondition: guide stage config is nil or not use, guide stage id:", guideStageId)
        return false
    end

    -- 后端让跳过
    if Server.GuideServer:IsNeedSkipGuide(guideStageId) then
        warning("CheckConstexprStartCondition: server set skip this guide, guide stage id:", guideStageId)
        return false
    end

    -- 批次是否符合
    if not GuideLogic.IsGuideBatchMatch(stageCfg) then
        warning("CheckConstexprStartCondition: guide batch not match, guide stage id:", guideStageId)
        local tbl = Module.Guide.Field.InvalidBatchGuideStageIdss:Get()
        table.insert(tbl, guideStageId)
        return false
    end

    return true
end

function GuideLogic.AssertConstexprStartCondition(guideStageId, msg)
    local stageCfg = GuideConfig.TableGuideStageConfig[guideStageId]
    assert(stageCfg ~= nil, msg)
    assert(stageCfg.IsUse, msg)
    assert(not Server.GuideServer:IsNeedSkipGuide(guideStageId), msg)
    assert(GuideLogic.IsGuideBatchMatch(stageCfg), msg)
end

-- 判断引导是否符合当前模式
---@param guideStageCfg FGuideStageClientOnlyConfig
---@return boolean
function GuideLogic.IsCurrentModeValid(guideStageCfg)
    if not guideStageCfg then return false end

    local modeType         = guideStageCfg.ModeType
    -- this will leak all guides to other modes, so do pre-mode check
    -- no config | select none 
    if modeType == 0 or (modeType & GuideConfig.EGuideStageClientMode.None) ~= 0 then
        return true
    end


    local inGameController = InGameController:Get()
    local curMode    = inGameController:GetGamePlayerMode()
    local curSubMode = inGameController:GetGamePlayerSubMode()
    loginfo("IsCurrentModeValid modeType:", modeType, "cur mode:", curMode, "cur sub mode:", curSubMode, "curGuideStage", guideStageCfg.GuideStageId)

    if not curMode or not curSubMode then
        logerror("curMode or curSubMode is nil")
        return false
    end

    -->>> Check invalid *GameMode* condition first

    --bug=136047327 [【CN】【安卓】【必现】SOL模式，竞技场需要屏蔽所有竞技场内的教学](https://tapd.woa.com/r/t?id=136047327&type=bug)
    if inGameController:IsArena() and (modeType & GuideConfig.EGuideStageClientMode.Arena) == 0 then
        return false
    end
    --<<<<

    --->>> Then check valid guide stage *Configuration*
       


    -- struct tutorial
    if (modeType & GuideConfig.EGuideStageClientMode.StructTutorial) ~= 0 then
        if curMode == EDFMGamePlayMode.GamePlayMode_SafeHouse or
            (curMode == EDFMGamePlayMode.GamePlayMode_SOL and curSubMode ~= EDFMGamePlaySubMode.GamePlaySubMode_Arena)
        then
            return true
        end
    end

    -- SOL
    if (modeType & GuideConfig.EGuideStageClientMode.SOL) ~= 0 then
        if curMode == EDFMGamePlayMode.GamePlayMode_SafeHouse or
            (curMode == EDFMGamePlayMode.GamePlayMode_SOL and curSubMode ~= EDFMGamePlaySubMode.GamePlaySubMode_Arena)
        then
            return true
        end
    end
    -- MP
    if (modeType & GuideConfig.EGuideStageClientMode.MP) ~= 0 then
        -- TODO: 闪击, 夺旗， Breakthrough, 占领?
        if inGameController:IsMPMode() then
            return true
        end
    end
    -- RAID
    if (modeType & GuideConfig.EGuideStageClientMode.Raid) ~= 0 and inGameController:IsRaid() then
        return true
    end
    -- Arena
    if (modeType & GuideConfig.EGuideStageClientMode.Arena) ~= 0 and inGameController:IsArena() then
        return true
    end
    --<<<<


    return false
end


GuideLogic._OnGuideStageEnd = function (guideStageCfg, tryPlayNextStageId)
    local guideStageId = guideStageCfg.GuideStageId

    if guideStageCfg.IsNewPlayerGuide then
        if guideStageId == Module.Guide.Field.waitPlayGuide then
            Module.Guide.Field.waitPlayGuide = nil
        end
        GuideLogic._SendServerPassedGuide(guideStageId)

        if GuideLogic.IsSolFailGuide(guideStageId) then
            Server.GuideServer:SetGuideNotForceGuideEnd(guideStageId)
        end
    end

    GuideLogic.DoStopAllGuide()
    GuideLogic._RecordGuideStageInfo(guideStageId, false)
    -- 任意引导结束的时候，都要判断一下是否当前有新手引导需要继续执行
    GuideConfig.EGuideEvent.evtGuideEnd:Invoke(guideStageId, tryPlayNextStageId)
end

-- 强制从某一步启动引导，跳过回滚逻辑检查，跳过引导阶段是否启用检查
-- 2025/1/8 <dexzhou> 取消编辑器限制, 出现异常请重置账号
GuideLogic.DoForceStartGuide = function(guideId)
    -- if IsInEditor() then
    log("DoForceStartGuide", guideId)

    -- 不去判断当前状态

    -- -- 是否处于观战
    -- local bIsLiveSpectating = UGPBattleFieldSystem.Get(GetWorld()).bIsLiveSpectating
    -- if bIsLiveSpectating then
    --     loginfo("DoForceStartGuide fail, cur is live spectating, guide id:", guideId)
    --     return
    -- end

    local ret = GuideLogic._RealStartGuide(guideId)
    -- 通知外界引导开始
    if ret then
        GuideConfig.EGuideEvent.evtGuideStart:Invoke(guideId)
    end
    -- end
end

-- 尝试启动局外的新手引导, 检测一下是否需要弹出跳过引导的弹窗
GuideLogic.TryStartNewPlayerGuideInFrontEnd = function ()
    if not GuideLogic.GetNextNewPlayerGuideStage() then
        return
    end

    if Module.Guide.Field.bSkipGuidePopWindowShow then
        return
    end

    --[[
    if false and CloseModuleType and CloseModuleType.bCanSkipNewPlayerGuide then
        if Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.IsSetSkipGuideState) then
            GuideLogic._StartNewPlayerGuideInFrontEnd()
        else
            Module.Guide.Field.bSkipGuidePopWindowShow = true
            local confirmCallback = function ()
                local onSkipSuccess = function ()
                    GuideLogic.UpdateGuideInGameData()
                end
                Module.Guide.Field.bSkipGuidePopWindowShow = false
                Server.GuideServer:SkipNewPlayerGuide(onSkipSuccess)
                Module.Guide.Field.waitPlayGuide = nil
            end
            local cancelCallback = function ()
                Module.Guide.Field.bSkipGuidePopWindowShow = false
                Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.IsSetSkipGuideState, true)
                GuideLogic._StartNewPlayerGuideInFrontEnd()
            end
            Module.CommonTips:ShowConfirmWindow(
                GuideConfig.Loc.skipNewPlayerGuide,
                confirmCallback,
                cancelCallback)
        end
    else
        GuideLogic._StartNewPlayerGuideInFrontEnd()
    end
    ]]--

    -- start directly
    GuideLogic._StartNewPlayerGuideInFrontEnd()
end

GuideLogic._StartNewPlayerGuideInFrontEnd = function ()
    if GuideLogic.IsGuiding() then return end -- 是否有其他引导
    if GuideLogic.IsNewPlayerGuideFinished() then return end

    -- 是否在对局中
    if Server.AccountServer:IsPlayerInGame() then
        -- 新手关特殊处理，从对局中出来不考虑前台状态，直接发起新的
        if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game and GuideLogic.GetCurNewPlayerGuideStage() == GuideConfig.EGuideStage.newPlayerGuideStage1 then
        else
            local _afterAccountUpdate = function()
                if not Server.AccountServer:IsPlayerInGame() then
                    GuideLogic.TryStartNewPlayerGuideInFrontEnd() -- retry
                else
                    loginfo("AccountServer:IsPlayerInGame after refresh")
                end
            end
            loginfo("AccountServer:IsPlayerInGame, refresh player state")
            Server.AccountServer:GetStateInfo(_afterAccountUpdate)
            return
        end
    end

    -- 是否在局外
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then
        return
    end

    -- 是否在合法的sub Stage
    if IsHD() and Server.GuideServer.FlagPCEnableGuideIn3dSafeHouse then
        -- 不做拦截
    else
        if GuideLogic.IsInSubStage3dSafehouse() or GuideLogic.IsInSubStageRange() then
            logwarning("_StartNewPlayerGuideInFrontEnd, sub stage cant begin guide")
            return
        end
    end

    if IsHD() then
        -- 是否pc侧 topbar准备完毕
        if Server.GuideServer.FlagPCEnableGuideIn3dSafeHouse then
            -- 不做拦截
        else
            if not GuideLogic.IsTopbarEnable() then
                loginfo("IsTopbarEnable, false")
                return
            end
        end
    else
        -- 是否处于3d安全屋主界面
        if not GuideLogic.CheckSafeHouseIsTop() then
            loginfo("CheckSafeHouseIsTop, false")
            return
        end
    end
    
    local stageList = {}
    do
        -- 是否有可进行引导
        local readyList = GuideLogic.GetCurReadyNewPlayerGuideStageList()
        if #readyList == 0 then
            return
        end

        -- 再走通用逻辑过滤一次
        for _, stageCfg in ipairs(readyList) do
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(stageList, stageCfg.GuideStageId)
        end
    end

    local maxPriority = -1
    ---@class FGuideStageClientOnlyConfig
    local waitStageCfg = nil
    for _, stageCfg in ipairs(stageList) do
        if stageCfg.Priority > maxPriority then
            maxPriority = stageCfg.Priority
            waitStageCfg = stageCfg
        end
    end

    -- 是否在合法的sub Stage
    if IsHD() and   Server.GuideServer.FlagPCEnableGuideIn3dSafeHouse then
        if waitStageCfg and waitStageCfg.GuideStageId == GuideConfig.EGuideStage.newPlayerGuideStage3 then
            local curSubStageId = Facade.GameFlowManager:GetCurrentSubStage()
            loginfo(" newPlayerGuideStage3 precheck curSubStageId: ", curSubStageId)
            if curSubStageId == ESubStage.None then
                return
            end
            if GuideLogic.IsInSubStage3dSafehouse() or GuideLogic.IsInSubStageRange() then
                logwarning("_StartNewPlayerGuideInFrontEnd, sub stage cant begin guide, guide stage is:", waitStageCfg.GuideStageId)
                return
            end
            if not GuideLogic.IsTopbarEnable() then
                loginfo("IsTopbarEnable, false")
                return
            end
        end
    end

    if hasdestroy(waitStageCfg) then
        loginfo("no wait stage cfg, return")
        return
    end
    GuideLogic.DoStartStageGuide(waitStageCfg.GuideStageId)
end

GuideLogic.IsGuiding = function ()
    local guideField = Module.Guide.Field
    return guideField:IsGuiding()
end

GuideLogic.GetMatchCount = function (game_rule)
    return Server.GuideServer:GetMatchCount(game_rule)
end

GuideLogic._RealStartGuide = function (guideId)
    -- 判断引导id是否合法
    local guideCfg = Module.Guide.Field:GetGuideCfg(guideId)
    if not guideCfg then
        err("guide cfg is nil. error guide id:", guideId)
        GuideLogic.DoStopAllGuide(true)
        return false
    end

    -- 如果有其他引导，提前终止
    if GuideLogic.IsGuiding() then
        warning("_RealStartGuide is guiding:", Module.Guide.Field:GetCurGuideId(), " break it")
        GuideLogic.DoStopAllGuide(true)
    end


    -- 开始引导
    local guideType = guideCfg.GuideType
    local guideDataClass = require(string.format("DFM.Business.Module.GuideModule.Data.GuideData%s", guideType))
    if not guideDataClass then
        err("guideDataClass nil. error guide id:", guideId, "error guide Type:", guideType)
        GuideLogic.DoStopAllGuide(true)
        return false
    end


    local guideData = guideDataClass:NewIns(guideId, guideCfg)
    Module.Guide.Field:SetCurGuideInfo(guideId, guideCfg, guideData)
    if guideCfg.GuideStageId == Module.Guide.Field.waitPlayGuide then
        Module.Guide.Field.waitPlayGuide = nil
    end

    -- 处理吞噬层
    Facade.UIManager:DisableInput(EInputChangeReason.BusinessPending)

    local mainUI = Module.Guide.Field:GetMainUI()
    ---@param curMainUI GuideMainUI
    local realStartGuide = function (curMainUI)
        logTempError("_RealStartGuide", guideId, guideType)
        if Module.Guide.Field.bLoadingGuideRes then
            GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.WaitMaskLoading)
            Module.Guide.Field.bLoadingGuideRes = false
        end
        --local curMainUI = Module.Guide.Field:GetMainUI()
        curMainUI:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        curMainUI:Show()
        curMainUI:SetSwallowTouchState(guideCfg.IsSwallowTouch)

        if GuideLogic.IsGuideGMShow() then
            local gmUIHandle = Module.Guide.Field:GetGmUIHandle()
            if gmUIHandle then
                local uiIns = gmUIHandle:GetUIIns()
                if uiIns then
                    uiIns:Show()
                    uiIns:UpdateGuideCfg(guideCfg)
                end
                log("use old gm ui")
            else
                local onFinishLoad = function (uiIns)
                    Facade.UIManager:SetZOrderInFrameRoot(uiIns, UILayer2ZOrder[EUILayer.Mask] + 40) -- 把gm层级提到最高
                    if not GuideLogic.IsGuiding() then
                        uiIns:Hide()
                    end
                end
                local gmHandle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideGMUI, onFinishLoad, nil, guideCfg)
                Module.Guide.Field:SetGmUIHandle(gmHandle)
                log("use new gm ui")
            end
        end

        if guideCfg and guideCfg.IsLogStep then
            LogAnalysisTool.DoSendClientCommonEventLog(GuideLogic._GetStepStartLogId(guideCfg.GuideId))
        end
        guideData:StartGuide()
        GuideLogic.UpdateHDInputState() -- 刷新下这一步是否禁用pc键盘输入
        Facade.UIManager:EnableInput(EInputChangeReason.BusinessPending)
    end

    if hasdestroy(mainUI) then
        log("wait load main ui", guideId, guideType)
        Module.Guide.Field.onMainUILoadFinishCallback = realStartGuide
        GuideLogic._AddGuideMainUI()
    else
        realStartGuide(mainUI)
    end

    return true
end

GuideLogic.DoEndGuide = function (nextGuideId, idx)
    local curGuideId = Module.Guide.Field:GetCurGuideId()
    local stageId = GuideLogic.GetStageIdByGuideId(curGuideId)
    local guideStageCfg = Module.Guide.Field:GetGuideStageCfg(stageId)
    local guideCfg = Module.Guide.Field:GetGuideCfg(curGuideId)

    log("DoEndGuide", curGuideId, nextGuideId, idx, guideCfg and (not VersionUtil.IsRelease()) and guideCfg.RowDescription)

    -- 埋点
    --if guideCfg and guideCfg.LogIdOnStepEnd and guideCfg.LogIdOnStepEnd > 0 then
    --    LogAnalysisTool.DoSendClientCommonEventLog(guideCfg.LogIdOnStepEnd)
    --end
    if guideCfg and guideCfg.IsLogStep then
        LogAnalysisTool.DoSendClientCommonEventLog(GuideLogic._GetStepEndLogId(guideCfg.GuideId))
    end

    -- 清除GM界面
    GuideLogic._HideGmUI()

    if not guideCfg then
        err("DoEndGuide, guide cfg is nil", curGuideId, guideCfg)
        return
    end

    -- 记录当前引导步骤已经执行
    GuideLogic._RecordGuideInfo(curGuideId, nextGuideId)


    -- 判断是否有下一步
    if GuideLogic.IsEndGuideId(nextGuideId) then
        GuideLogic._OnGuideStageEnd(guideStageCfg)
        return
    elseif GuideLogic.IsTempSkipGuideId(nextGuideId) then
        GuideLogic.DoStopAllGuide()

        -- 重新设置一下sol主流程引导的stage，以便重启
        if table.contains(GuideConfig.NewPlayerGuideStageList, stageId) then
            Module.Guide.Field:ResetNewPlayerGuidePassed()
        end

        return
    elseif GuideLogic.IsEndGuideAndRemoveAutoPlayId(nextGuideId) then
        GuideLogic.DoStopAllGuide()
        -- pop and refresh
        local needRemoveInfo = Module.Guide.Field:GetAutoPlayGuideInfoByStageId(stageId, true)
        log("on end guide and remove auto play", stageId, needRemoveInfo)
        GuideLogic._RefreshAutoPlayTimer()
        return
    elseif GuideLogic.IsNextStageId(nextGuideId) then
        local nextStageId = nextGuideId
        GuideLogic._OnGuideStageEnd(guideStageCfg, nextStageId)
        return
    end

    -- 清除上一步引导信息
    local curGuideData = Module.Guide.Field:GetCurGuideData()
    if curGuideData then
        curGuideData:Release()
    end
    Module.Guide.Field:SetCurGuideInfo(nil, nil, nil)
    GuideLogic.SetSwallowMainUIState(false)

    -- 开始下一步引导
    GuideLogic._RealStartGuide(nextGuideId)
end

GuideLogic.DoStopAllGuide = function (bResetWidget)
    logTempError("DoStopAllGuide")
    -- 启用安卓返回输入
    GuideLogic.SetAndroidInputEnable(true)

    -- 清除引导ui
    local curGuideData = Module.Guide.Field:GetCurGuideData()
    if curGuideData then
        curGuideData:Release()
    end
    local mainUI = Module.Guide.Field:GetMainUI()
    if mainUI then
        mainUI:SetVisibility(ESlateVisibility.Collapsed)
        GuideLogic.SetSwallowMainUIState(false)
    end

    local guideField = Module.Guide.Field
    guideField:ResetModifyVisibilityWidgets(bResetWidget)
    -- 清除引导信息
    guideField:SetCurGuideInfo(nil, nil, nil)
    GuideLogic._HideGmUI()
    guideField:SetInMovieTypeState(false)
    guideField:ResetFlagForOtherModule()
    GuideLogic.CloseGuideCutSceneUI()
    GuideLogic.SetCameraNpcTalkEnable(false)

    --GuideLogic.UpdateHDInputState(true) -- 引导结束或中断，重新启用pc键盘输入
    GuideLogic.UpdateHDInputState() -- 引导结束或中断，重新启用pc键盘输入
end

-- 强制结束并完成当前的引导阶段
GuideLogic.DoForceEndGuideStage = function(guideStageId)
    log("DoForceEndGuideStage", guideStageId)
    -- 清除GM界面
    GuideLogic._HideGmUI()
    
    -- GuideLogic.DoEndGuide(-1)
    local guideStageCfg = Module.Guide.Field:GetGuideStageCfg(guideStageId)
    GuideLogic._OnGuideStageEnd(guideStageCfg)
end

-- 清除对loading界面的操控
GuideLogic.StopLoadingViewControl = function ()
    if Module.Guide.Field.bControlLoadingView == true then
        log("DoStopAllGuide, release loading view control")
        local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
        if DFMGameLoadingManager then
            DFMGameLoadingManager:SetCloseLoadingViewAfterLoading(true)
            DFMGameLoadingManager:ShutDownLoadingView()
            Module.Guide.Field:StopFakeLoadingTimer()
            Module.Guide.Field.bControlLoadingView = false
        else
            err("DoStopAllGuide, Reset fail, DFMGameLoadingManager is nil !!!")
        end
    end
end

GuideLogic.SetAndroidInputEnable = function (bEnable)
    Module.CommonBar:SetAndroidInputEnable(bEnable)
end



-- 首位表示是引导的log，第二位表示是 局外引导单步开始(1) 局外单步引导结束(2) 局内引导(3及其他)，三四五位表示阶段id，后三位表示具体单步id
-- 如阶段1的第一步，guideId为1001，开始时上报log为11001001，结束时上报12001001, 被重定向为 21001001(手游) 或 31001001(手柄)

GuideLogic._GetStepStartLogId = function (guideId)
    return guideId and (11000000 + guideId) or 0 -- 10+1
end

GuideLogic._GetStepEndLogId = function (guideId)
    return guideId and (12000000 + guideId) or 0 -- 10+2
end

---@param guideId integer
---@param extType GuideConfig.ELogExType
function GuideLogic.GetStepExLogId (guideId, extType)
    return guideId and ((10 + extType) * 10 ^ 6 + guideId) or 0
end

GuideLogic._AddGuideMainUI = function ()
    local mainUI = Module.Guide.Field:GetMainUI()
    local bDestroy = hasdestroy(mainUI) 
    log("_AddGuideMainUI", bDestroy)
    if not bDestroy then
        return
    end

    local _callback = function (ins)
        Module.Guide.Field:SetMainUI(ins)
        ins:SetVisibility(ESlateVisibility.Collapsed)
        if Module.Guide.Field.onMainUILoadFinishCallback then
            Module.Guide.Field.onMainUILoadFinishCallback(ins)
            Module.Guide.Field.onMainUILoadFinishCallback = nil
        end
    end
    local guideMainUIHandle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideMainUI, _callback, nil)
    Module.Guide.Field:SetMainUIHandle(guideMainUIHandle)
end

function GuideLogic._RecordGuideInfo (guideId, nextGuideId)
    if guideId == nil then
        guideId = Module.Guide.Field:GetCurGuideId()
    end

    local guideCfg = Module.Guide.Field:GetGuideCfg(guideId)
    if not guideCfg then
        err("_RecordGuideInfo, guide cfg is nil", guideId, guideCfg)
        return
    end
    local stageId = guideCfg.GuideStageId
    local strStageId = tostring(stageId)

    if guideCfg.IsKeyStep then
        local tipsKey = GuideLogic._GetStageStepKey()
        local stepMap = Server.TipsRecordServer:GetMap(tipsKey)
        stepMap[strStageId] = guideId % 1000
        Server.TipsRecordServer:SetMap(tipsKey, stepMap)
    end

    log("_RecordGuideInfo:", guideId)
end


function GuideLogic._RecordGuideStageInfo(stageId, isStageStart)
    assert(isStageStart ~= nil)
    if stageId == nil then
        logerror("RecordGuideStageInfo: stageId is nil")
        return
    end

    local stageConfig = GuideConfig.TableGuideStageConfig[stageId]
    local strStageId = tostring(stageId)

    -- 记录引导阶段的结束
    if not isStageStart then
        local finishMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageFinished)
        if finishMap[strStageId] == nil then
            finishMap[strStageId] = 1
        else
            finishMap[strStageId] = finishMap[strStageId] + 1
        end
        Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideStageFinished, finishMap)
    end

    local curElapsedTime = math.floor(UDFMGuideManager.Get(GetWorld()):GetElapsedTime())   --  start from 0 from client running
    local timeStamp = Facade.ClockManager:GetLocalTimestamp() -- in sec 

    -- AutoPlay
    -- Q: 需要一个引导不停地播放吗?
    -- A: Deprecated, 现在没有对应的需求，保留该feature
    if stageConfig.AutoPlayAgainCD > 0 then
        Module.Guide.Field:PushAutoPlayGuideInfo(stageId, curElapsedTime, curElapsedTime + stageConfig.AutoPlayAgainCD)
        GuideLogic._RefreshAutoPlayTimer()
    end

    --[[ 记录每一次引导开始/结束时:
        curGameCount sol历史局数
        curElapsedTiime 该次启动游戏的运行时间
        stageEndCount 完成了多少次(阶段结束+1)
        timestamp  // 2025/7/9 she3: 使用timestamp, 支持跨GameSession判断
    ]] 
    local infoMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageInGameInfo)
    local infoStr = infoMap[strStageId]

    -- TODO: 移动  GuideStageDayPlayed 到这里 -> 2025/6/16下个版本 // 2025/7/9 暂时没有这样需求,仅技能对抗教学需要
    local curGameCount = Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule)

    local stageEndCount = 0
    if not infoStr then
        if not isStageStart then
            stageEndCount = 1 -- end once
        end
    else
        local seps = StringUtil.Split(infoStr, "|")
        local lastGameCount = tonumber(seps[1]) or 0
        -- local lastTime = tonumber(seps[2]) or 0
        local lastStageEndCount = tonumber(seps[3]) or 0

        if  curGameCount == lastGameCount then
            stageEndCount =  isStageStart and lastStageEndCount or lastStageEndCount + 1
        else
            if not isStageStart then
                stageEndCount = 1
            end
        end
    end
    infoMap[strStageId] = string.format("%s|%s|%s|%s", curGameCount, curElapsedTime, stageEndCount, timeStamp)
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideStageInGameInfo, infoMap)
end

GuideLogic._GetStageStepKey = function ()
    return IsHD() and Server.TipsRecordServer.keys.GuideStageStepHD or Server.TipsRecordServer.keys.GuideStageStep
end


function GuideLogic.GetStageRecordInfo(guideStageId)
    ---@class GuideStageRecordInfo
    local ret = {
        bInSameMatch = false,
        lastGameCount = 0,
        lastElapsedTime = 0, -- stage start or end
        stageEndCount = 0,
        timeStamp = 0,
    }

    local stageIdStr = tostring(guideStageId)
    local infoMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageInGameInfo)
    local infoStr = infoMap[stageIdStr]
    log("_GetStageLastTimeInGame:", stageIdStr, infoStr)
    if not infoStr then
        return ret
    end

    -- local gameCount, theLastTime, theLastStageEndCount = StringUtil.Split(infoStr, "|")
    local sep =  StringUtil.StringSplit(infoStr, "|")
    -- 是否和上次处于同一局/同一进度
    ret.lastGameCount = tonumber(sep[1]) or 0
    ret.bInSameMatch = ret.lastGameCount == Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule)
    if not ret.bInSameMatch then
        ret.lastElapsedTime = tonumber(sep[2]) or 0
    end
    ret.stageEndCount = tonumber(sep[3]) or 0
    ret.timeStamp = tonumber(sep[4]) or 0

    return  ret
end




---------------------------------------------------------
--#region AutoPlay 

-- 上次该阶段开始/结束时的游戏启动时间
---@return nil|0|number  传入参数错误| 本局无上次记录|上次进行的时间
GuideLogic._GetStageLastElapsedTimeInGame = function (stageId)
    if stageId == nil then
        logerror("_GetStageLastTimeInGame: stageId is nil")
        return
    end
    local infoMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageInGameInfo)
    local strStageId = tostring(stageId)
    local infoStr = infoMap[strStageId]
    log("_GetStageLastTimeInGame:", stageId, infoStr)
    if not infoStr then return 0 end
    local gameCount, lastTime = StringUtil.Split(infoStr, "|")
    -- 是否和上次处于同一局
    if tonumber(gameCount) ~= Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule) then return 0 end
    return tonumber(lastTime)
end

GuideLogic._GetAllStageRecordInfo = function ()
    local infoMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageInGameInfo)
    local ret = {}
    for strStageId, strInfo in pairs(infoMap) do
        local gameCount, lastTime, lastStageEndCount = StringUtil.Split(strInfo, "|")
        -- 是否和上次处于同一局
        if tonumber(gameCount) == Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule) then 
            table.insert(ret, {
                stageId = tonumber(strStageId),
                lastTime = tonumber(lastTime),
                lastStageEndCount = tonumber(lastStageEndCount)

            })
            log("GetAllStageAutoPlayInfoInGame:", strStageId, strInfo, lastTime, lastStageEndCount)
        end
    end
    return ret
end


GuideLogic.InitAutoPlayGuideInfo = function ()
    local recordInfoList = GuideLogic._GetAllStageRecordInfo()
    local guideField = Module.Guide.Field
    for _, recordInfo in ipairs(recordInfoList) do
        local stageConfig = GuideConfig.TableGuideStageConfig[recordInfo.stageId]
        guideField:PushAutoPlayGuideInfo(recordInfo.stageId, recordInfo.lastTime, recordInfo.lastTime + stageConfig.AutoPlayAgainCD)
    end

    GuideLogic._RefreshAutoPlayTimer()
end

GuideLogic.ClearAutoPlayGuideInfo = function ()
    GuideLogic._ClearAutoPlayTimer()
    Module.Guide.Field:CleanAutoPlayInfo()
end

-- 处理定时器，找到重播时间最近的一个引导进行定时
GuideLogic._RefreshAutoPlayTimer = function ()
    GuideLogic._ClearAutoPlayTimer()
    local firstAutoInfo = Module.Guide.Field:GetFirstAutoPlayGuideInfo()
    if firstAutoInfo then
        local guideMgrIns = UDFMGuideManager.Get(GetWorld())
        local curTime = guideMgrIns:GetElapsedTime()
        log("_RefreshAutoPlayTimer, time", math.max(firstAutoInfo.nextTime - curTime, 1), firstAutoInfo.stageId)
        local timer = Timer.DelayCall(math.max(firstAutoInfo.nextTime - curTime, 1), GuideLogic._TryAutoPlayGuideAgain)
        Module.Guide.Field.guideAutoPlayTimer = timer
    end
end

GuideLogic._ClearAutoPlayTimer = function ()
    Module.Guide.Field:CleanAutoPlayTimer()
end

GuideLogic._TryAutoPlayGuideAgain = function ()
    local firstAutoInfo = Module.Guide.Field:GetFirstAutoPlayGuideInfo(true)
    local bPlaySuccess = false
    if firstAutoInfo then
        if GuideLogic.CheckMeetGuideStageStartCondition(firstAutoInfo.stageId) then
            if GuideLogic.DoStartStageGuide(firstAutoInfo.stageId) then
                bPlaySuccess = true
            end
        end
    end

    -- 非战斗状态下播放失败重新塞入队列；战斗状态下播放失败放弃播放，等脱战重启
    if firstAutoInfo and not bPlaySuccess then
        if not Module.Guide.Field.bInCombat then
            local guideMgrIns = UDFMGuideManager.Get(GetWorld())
            local curTime = guideMgrIns:GetElapsedTime()
            if firstAutoInfo.nextTime < curTime then
                local lastTime = GuideLogic._GetStageLastElapsedTimeInGame(firstAutoInfo.stageId)
                firstAutoInfo.nextTime = firstAutoInfo.nextTime - firstAutoInfo.lastTime + lastTime
                firstAutoInfo.lastTime = lastTime
            end
            Module.Guide.Field:PushAutoPlayGuideInfo(firstAutoInfo.stageId, firstAutoInfo.lastTime, firstAutoInfo.nextTime)
        end
    end
    Module.Guide.Field.guideAutoPlayTimer = nil
    GuideLogic._RefreshAutoPlayTimer()
end

--#endregion
-----------------------------------------------------

-- 重启引导
-- reason: ERestartGuideReason
GuideLogic.TryRestartGuide = function (reason)
    logTempError("TryRestartGuide", reason)
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then
        err("TryRestartGuide fail, not in safehouse")
        return
    end
    local needRestartStageId = nil
    local guideField = Module.Guide.Field
    local curGuideId = guideField:GetCurGuideId()
    -- 当前有执行的引导
    if curGuideId then
        local guideCfg = guideField:GetGuideCfg(curGuideId)
        local curStageId = guideCfg.GuideStageId
        if table.contains(GuideConfig.NewPlayerGuideStageList, curStageId) then
            needRestartStageId = curStageId
        end
    else
        -- 当前没有执行的引导
    end

    logTempError("TryRestartGuide try restart stage id:", needRestartStageId)
    if needRestartStageId then
        -- 清理当前引导
        Module.Guide:StopAllGuide()
        -- 回到安全屋
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            local syncUIInst = Facade.UIManager:GetStackUIByUINavId(UIName2ID.StarLinkView)
            if syncUIInst then
                Module.CommonBar:SetUseStarAppState(false, false)
            end
        end
        Facade.UIManager:PopAllUI(true, false, MapPopAllUIReason2Str.RestartGuideToLobby)
        Facade.UIManager:CloseAllPopUI()
        -- 清理选图信息
        local gameModeServer = Server.GameModeServer
        gameModeServer:SetMatchMode(gameModeServer:GetUnknownGameMode())
        gameModeServer:SetMatchModes({})
        if Module.ArmedForce:HasOutfitFlowFlag() then
            Module.ArmedForce:ClearOutfitFlowFlag()
        end

        -- 尝试重启引导
        Module.Guide:StartStageGuide(needRestartStageId)
    end
end

GuideLogic.SetSwallowMainUIState = function (bShow, bImmediate)
    local mainUI = Module.Guide.Field:GetMainUI()
    if mainUI then
        if mainUI.SetSwallowTouchState then
            mainUI:SetSwallowTouchState(bShow, bImmediate)
        else
            err("mainUI.SetSwallowTouchState is nill!")
        end
    end
end

-- 新手引导完成一个阶段的时候主动上报，上报期间屏蔽玩家输入
GuideLogic._SendServerPassedGuide = function (stageId)
    Server.GuideServer:SetGuidePassed(stageId)
end

-- 获取下一个新手引导阶段
GuideLogic.GetNextNewPlayerGuideStage = function ()
    return Module.Guide.Field.waitPlayGuide
end

GuideLogic.GetStageIdByGuideId = function (guideId)
    return guideId // 1000
end

-- 表示结束引导，且引导完成
GuideLogic.IsEndGuideId = function (guideId)
    return guideId == -1 or guideId == 0
end

-- 表示暂时结束引导，但引导不完成，下次可以重启
GuideLogic.IsTempSkipGuideId = function (guideId)
    return guideId == -2
end

-- 表示结束引导，且引导完成。如果该引导在自动重播队列，则停止其自动重播
GuideLogic.IsEndGuideAndRemoveAutoPlayId = function (guideId)
    return guideId == -3
end

-- 表示结束引导，且引导完成。并且重新将该阶段插入自动重播队列
GuideLogic.IsEndGuideAndRepushAutoPlayId = function (guideId)
    return guideId == -4
end

-- 表示自己引导结束，且尝试启动下一个阶段引导
GuideLogic.IsNextStageId = function (guideId)
    return guideId and GuideLogic.GetStageIdByGuideId(guideId) == 0
end

-- 当前引导是否处于暂停状态
GuideLogic.IsInPauseState = function ()
    return Module.Guide.Field:IsStatePause()
end

GuideLogic.GetGuideStageFinishCount = function (guideStageId)
    local finishMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageFinished)
    return finishMap[tostring(guideStageId)] or 0
end

GuideLogic.AddGuideWidgetProxy = function (name, targetWidget)
    Module.Guide.Field:AddProxyWidget(name, targetWidget)
end

GuideLogic.RemoveGuideWidgetProxy = function (name)
    Module.Guide.Field:RemoveProxyWidget(name)
end

GuideLogic._HideGmUI = function ()
    local gmHandle = Module.Guide.Field:GetGmUIHandle()
    if gmHandle and gmHandle:GetUIIns() then
        gmHandle:GetUIIns():Hide()
    end
end

GuideLogic.CloseGmUI = function ()
    local gmHandle = Module.Guide.Field:GetGmUIHandle()
    if gmHandle then
        Facade.UIManager:CloseUIByHandle(gmHandle)
        Module.Guide.Field:SetGmUIHandle(nil)
    end
end

GuideLogic.ShowGuideCutSceneUI = function ()
    if Module.Guide.Field:IsStatePause() then return end
    local handle = Module.Guide.Field:GetCutSceneUIHandle()
    if not handle then
        -- 去掉黑边
        --handle = Facade.UIManager:AsyncShowUI(UIName2ID.GuideCutSceneUI, nil, nil)
        --Module.Guide.Field:SetCutSceneUIHandle(handle)
        GuideLogic.SetOnlyShowGuideState(true, GuideConfig.EGuideShowUIFlag.OnlyGuiding)
    end
end

GuideLogic.CloseGuideCutSceneUI = function ()
    local handle = Module.Guide.Field:GetCutSceneUIHandle()
    if handle then
        Facade.UIManager:CloseUIByHandle(handle)
    end
    Module.Guide.Field:SetCutSceneUIHandle(nil)
    GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.OnlyGuiding)
end

GuideLogic.SetCameraNpcTalkEnable = function (bEnable)
    local LocalPlayerCharacter = UGameplayStatics.GetPlayerCharacter(GetWorld(), 0)
    if not LocalPlayerCharacter then return end
    LocalPlayerCharacter.Blackboard.CameraMode.TPPSpringArmComponent:SetCameraModeToNpcTalk(bEnable)
end

---@param bOnlyGuideUI boolean
---@param flag EGuideShowUIFlag
GuideLogic.SetOnlyShowGuideState = function (bOnlyGuideUI, flag)
    flag = setdefault(flag, GuideConfig.EGuideShowUIFlag.OnlyGuiding)
    if bOnlyGuideUI then
        Module.Guide.Field:AddFlagShowUI(flag)
    else
        Module.Guide.Field:RemoveFlagShowUI(flag)
    end
end

GuideLogic.SetWaitSettlementState = function (bWaitSettlement)
    --log("SetWaitSettlementState", bWaitSettlement, GuideLogic.IsNewPlayerGuideFinished())
    local bCurNotHaveSettlement = true -- 现在游戏没有局外结算
    Module.Guide.Field.bWaitSettlementInFrontEnd = bWaitSettlement
    if GuideLogic.IsNewPlayerGuideFinished() then return end

    if bCurNotHaveSettlement then return end
    if bWaitSettlement then
        Module.Guide.Field:AddFlagShowUI(GuideConfig.EGuideShowUIFlag.WaitSettlement)
    else
        Module.Guide.Field:RemoveFlagShowUI(GuideConfig.EGuideShowUIFlag.WaitSettlement)
    end
end

GuideLogic.IsCurInMovieType = function ()
    local handle = Module.Guide.Field:GetCutSceneUIHandle()
    return handle ~= nil
end

GuideLogic.GetCurTime = function ()
    local guideMgrIns = UDFMGuideManager.Get(GetWorld())
    return guideMgrIns:GetElapsedTime()
end

-- 局内定时触发的引导，设定定时器触发
GuideLogic.TryStartTimerForGuideInGame = function ()
    GuideLogic.StopTimerForGuideInGame()

    -- pc现在不需要启动定时启动引导
    if true then return end

    local matchCount = Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule)
    local newTimer = {}
    local leftTime = -1

    if DFHD_LUA == 1 then
        if matchCount == 1 then
            leftTime = 60 * 25 - GuideLogic.GetCurTime()
            local GuideEvtLogic = require "DFM.Business.Module.GuideModule.GuideEvtLogic"
            if leftTime < 0 then
                leftTime = 1
            end
            if leftTime > 10 then
                table.insert(newTimer, Timer.DelayCall(leftTime, GuideEvtLogic._OnGuideRemindEscapeSimple))
            end
        end
    else
        if matchCount == 1 then
            leftTime = 60 * 15 - GuideLogic.GetCurTime()
            local GuideEvtLogic = require "DFM.Business.Module.GuideModule.GuideEvtLogic"
            if leftTime < 0 then
                leftTime = 1
            end
            if leftTime > 10 then
                table.insert(newTimer, Timer.DelayCall(leftTime, GuideEvtLogic._OnGuideRemindEscape))
            end
        elseif matchCount == 2 then
            leftTime = 60 * 20 - GuideLogic.GetCurTime()
            local GuideEvtLogic = require "DFM.Business.Module.GuideModule.GuideEvtLogic"
            if leftTime < 0 then
                leftTime = 1
            end
            if leftTime > 10 then
                table.insert(newTimer, Timer.DelayCall(leftTime, GuideEvtLogic._OnGuideRemindEscapeSimple))
            end
        end
    end

    if #newTimer > 0 then
        Module.Guide.Field.timerForGuideInGameList = newTimer
    end
    logTempError("TryStartTimerForGuideInGame", #newTimer, leftTime)
end

GuideLogic.StopTimerForGuideInGame = function ()
    log("StopTimerForGuideInGame")
    if Module.Guide.Field.timerForGuideInGameList then
        for _, timerHandle in pairs(Module.Guide.Field.timerForGuideInGameList) do
            Timer.CancelDelay(timerHandle)
        end
        Module.Guide.Field.timerForGuideInGameList = nil
    end
end

-- 获取新手引导流程中当前可以开始执行的引导
GuideLogic.GetCurReadyNewPlayerGuideStageList = function ()
    ---@type FGuideStageClientOnlyConfig[]
    local ret = {}
    -- local defaultCondition = {
        -- [GuideConfig.ECondition.MaxGuideTimes] = 1,
    -- }
    for _, stageCfg in ipairs(GuideConfig.TableGuideStageConfig) do
        -- 新手引导能否执行，以服务器通知的值为准
        if stageCfg.IsNewPlayerGuide then
            if stageCfg.GuideStageId == GuideLogic.GetNextNewPlayerGuideStage() then
                table.insert(ret, stageCfg)
            end
        end
    end
    return ret
end



local function _IsInTestMap()
    if IsInEditor() then
        local worldName = UE.GPGameplayStatics.GetWorldName(GetWorld())
        if string.starts_with(worldName, "TestMap") then
            return true
        end
    end
    return false
end

-- cached functions
local _commonGuideStageStartConditions = GuideLogic
    .switch()
    .default(function(params, stageCfg)
        assert(false, "GuideLogic.CheckMeetGuideStageStartCondition: conditionId is not exist", stageCfg.GuideStageId)
    end)
    .case(GuideConfig.ECondition.FinishNewPlayerGuide, function(params)
        return GuideLogic.IsNewPlayerGuideFinished()
    end)
    .case(GuideConfig.ECondition.MaxGuideTimes, function(params, stageCfg)
        local curFinishCount = GuideLogic.GetGuideStageFinishCount(stageCfg.GuideStageId)
        if curFinishCount >= params[1] then
            --log(conditionId, curFinishCount, params[1])
            return false
        end
        return true
    end)
    .case(GuideConfig.ECondition.PreGuideStage, function(params) ---@param params EGuideStage[]
        for _, preGuideStageId in ipairs(params) do
            if GuideLogic.GetGuideStageFinishCount(preGuideStageId) == 0 then
                --log(conditionId, GuideLogic.GetGuideStageFinishCount(preGuideStageId))
                return false
            end
        end
        return true
    end)
    .case(GuideConfig.ECondition.MinFinishGame, function(params) ---@param params [ MatchGameRule , integer]
        return Server.GuideServer:GetMatchCount(params[1]) >= params[2]
    end)
    .case(GuideConfig.ECondition.MaxFinishGame, function(params) ---@param params [MatchGameRule, integer]
        if _IsInTestMap() then
            warning("GuideLogic.CheckMeetGuideStageStartCondition: MaxFinishGame condition is ignored  in test map")
            return true
        end
        return Server.GuideServer:GetMatchCount(params[1]) < params[2]
    end)
    .case(GuideConfig.ECondition.CurNotHasMapMarkExit, function()
        local UDFMHudHelper = import "DFMHudHelper"
        if UDFMHudHelper.HasMapSignPlayerExit(GetWorld()) then
            return false
        end
        return true
    end)
    .case(GuideConfig.ECondition.InCombatState, function(params)
        local targetCombatState = params[1]
        local guideField = Module.Guide.Field
        if targetCombatState == 0 and guideField.bInCombat == true then
            return false
        elseif targetCombatState == 1 and guideField.bInCombat == false then
            return false
        end
        return true
    end)
    .case(GuideConfig.ECondition.LastSolGameIsFail, function(params)
        --log(conditionId, Server.GuideServer:IsLastSolGameResultFail())
        return Server.GuideServer:IsLastSolGameResultFail()
    end)
    .case(GuideConfig.ECondition.MinLevel, function(params)
        return Server.RoleInfoServer.seasonLevel >= params[1]
    end)
    .case(GuideConfig.ECondition.MaxLevel, function(params)
        return Server.RoleInfoServer.seasonLevel <= params[1]
    end)
    .case(GuideConfig.ECondition.NotInMathing, function()
        return not Server.MatchServer:GetIsReadytoGo()
    end)
    -- 为了兼容不同平台不同的触发条件 (如技能对抗教学的弹窗， 手游自动触发，每一局打完后只能触发一次; 而PC分为了两个引导，一个是点击引导仅触发一次。而弹窗在结算时触发，可以触发无数次)
        ---@param params [GuideConfig.EPlayCDType]
        ---@param stageCfg FGuideStageClientOnlyConfig
        ---@param stageInfo GuideStageRecordInfo
        ---@return boolean
    .case(GuideConfig.ECondition.PlayCD, function(params, stageCfg, stageInfo)
        assert(stageInfo, "GuideLogic.CheckMeetGuideStageStartCondition: stageInfo is nil, stageId:", stageCfg.GuideStageId)
        local cdType = params[1]

        if cdType == GuideConfig.EPlayCDType.BySec then
            local dur = params[1]
            local now = Facade.ClockManager:GetLocalTimestamp()
            if now - stageInfo.timeStamp < dur then
                log("CheckMeetGuideStageStartCondition In CD", stageCfg.GuideStageId, now, stageInfo.timeStamp)
                return false
            end
            return true
        elseif cdType == GuideConfig.EPlayCDType.ByDay then
            local dur = params[1] * 24 * 60 * 60     -- 秒
            local now = Facade.ClockManager:GetLocalTimestamp()
            if now - stageInfo.timeStamp < dur then
                log("CheckMeetGuideStageStartCondition In CD", stageCfg.GuideStageId, now, stageInfo.timeStamp)
                return false
            end
            return true
        elseif cdType == GuideConfig.EPlayCDType.ByMatch then
            local dur = params[1]
            local lastGameCount = stageInfo.lastGameCount
            local curGameCount = Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule)
            if curGameCount - lastGameCount < dur then
                log("CheckMeetGuideStageStartCondition In CD, curGameCount < lastGameCount", stageCfg.GuideStageId, lastGameCount, curGameCount)
                return false
            end
            return true
        elseif cdType == GuideConfig.EPlayCDType.EachMath then
            local maxTimePerGame = #params >= 2 and params[2] or 0 -- 一局最多x次
            local minSec = #params >= 3 and params[3] or 0         -- 每次间隔最小y秒

            if not stageInfo.bInSameMatch then
                return true
            end

            if stageInfo.stageEndCount >= maxTimePerGame then
                log("stage meet MaxTimesPerGame ", stageCfg.GuideStageId, stageInfo.stageEndCount, maxTimePerGame)
                return false
            end
            if minSec > 0 then
                local curTime = GuideLogic.GetCurTime()
                if stageInfo.lastElapsedTime > 0 and curTime - stageInfo.lastElapsedTime < maxTimePerGame then
                    log("In CD", stageCfg.GuideStageId, curTime, stageInfo.lastElapsedTime, curTime - stageInfo.lastElapsedTime)
                    return false
                end
            end
            return true
        end
        assert(false, "unimplemented for", cdType, "in GuideLogic.CheckMeetGuideStageStartCondition")
        return false
    end)

---@param readyListToAdd table
---@param guideStageId EGuideStage
---@return boolean
GuideLogic.TryInsertIfMeetGuideStageStartCondition = function(readyListToAdd, guideStageId)
    assert(readyListToAdd)
    if GuideLogic.CheckMeetGuideStageStartCondition(guideStageId) then
        table.insert(readyListToAdd,GuideConfig.TableGuideStageConfig[guideStageId])
        return true
    end
    return false
end



-- 检查某个引导是否满足开启条件
function GuideLogic.CheckMeetGuideStageStartCondition(guideStageId)
    local stageCfg = GuideConfig.TableGuideStageConfig[guideStageId]

    if not GuideLogic.CheckConstexprStartCondition(guideStageId) then
        return false
    end

    -- 前端让跳过
    if Module.Guide.Field.ClientSkipGuides:Get(guideStageId) == true then
        warning("CheckMeetGuideStageStartCondition: client set skip this guide, guide stage id:", guideStageId)
        return false
    end

    -- 过滤掉SOL击倒状态下的引导
    if Module.Guide.Field.InGameIsAliveState:Get() == ECharacterLiveStatus.ECharacterLiveStatus_ImpendingDeath then
        loginfo("DoStartStageGuide fail, cur is dead, guide stage id:", guideStageId)
        return false
    end

    -- 模式是否有限制
    if not GuideLogic.IsCurrentModeValid(stageCfg) then
        loginfo("CheckMeetGuideStageStartCondition , cur mode not valid, stage id:", guideStageId)
        return false
    end

    local stageInfo = Module.Guide.Field:IsGuideStageNeedCD(stageCfg) and GuideLogic.GetStageRecordInfo(guideStageId) -- or nil
    local startCondition = GuideLogic.GetRealStartCondition(stageCfg)

    for _, cond in pairs(startCondition) do
        if not _commonGuideStageStartConditions.process(cond.ConditionID, cond.Params, stageCfg, stageInfo) then
            return false
        end
    end

    return true
end

---@param stageCfg FGuideStageClientOnlyConfig
GuideLogic.GetRealStartCondition = function(stageCfg)
    if IsConsole() and stageCfg.OverrideStartCondition.Gamepad and #stageCfg.OverrideStartCondition.Gamepad > 0 then
      return stageCfg.OverrideStartCondition.Gamepad
    end
    if IsHD() and stageCfg.OverrideStartCondition.HD and #stageCfg.OverrideStartCondition.HD > 0 then
        return stageCfg.OverrideStartCondition.HD
    end
    if IsMobile() and stageCfg.OverrideStartCondition.Mobile and #stageCfg.OverrideStartCondition.Mobile > 0 then
        return stageCfg.OverrideStartCondition.Mobile
    end
    return stageCfg.StartCondition
end

-- 检查某个引导是否需要避开战斗状态
GuideLogic.CheckGuideNeedNotInCombat = function (guideStageId)
    local stageCfg = GuideConfig.TableGuideStageConfig[guideStageId]
    local startCondition = GuideLogic.GetRealStartCondition(stageCfg)
    for _, condition in pairs(startCondition) do
        local conditionId = condition.ConditionID
        local params = condition.Params
        if conditionId == GuideConfig.ECondition.InCombatState then
            local targetCambatState = params[1]
            local guideField = Module.Guide.Field
            return targetCambatState == 0
        end
    end
    return false
end


---@param guideStageIds number[]
---@return FGuideStageClientOnlyConfig[]
function GuideLogic._GuideStageIds2GuideStageCfgs(guideStageIds)
    local guideStageCfgs = {}
    for _, stageId in ipairs(guideStageIds) do
        local stageCfg = Module.Guide.Field:GetGuideStageCfg(stageId)
        if stageCfg then
            table.insert(guideStageCfgs, stageCfg)
        end
    end
    return guideStageCfgs
end

---@param guideStageCfgs FGuideStageClientOnlyConfig[]
function GuideLogic._OnGuideSkipByAccountBatchNotMatch(guideStageCfgs)
    if VersionUtil.IsShipping() then return end
    if #guideStageCfgs == 0 then return end

    local friendlyStr = ""
    for _, stageCfg in ipairs(guideStageCfgs) do
        friendlyStr = friendlyStr .. " " .. (tostring(stageCfg.GuideStageId) or "nil") .. "-" .. stageCfg.RowDescription .. ", "
    end
    log("OnGuideSkipByAccountBatchNotMatch", friendlyStr)
    Module.CommonTips:ShowSimpleTip(
        StringUtil.SequentialFormat(Module.Guide.Config.Loc.guideTriggerFailedByAccountBatch, Server.GuideServer.playerAccountBatch, friendlyStr),
        5)
end


---@param structGuideCfgs FGuideStageClientOnlyConfig[]
function GuideLogic._OnStructTutorialSkippedByNewPlyerGuide(structGuideCfgs, newPlayerGuideCfgs)
    if VersionUtil.IsShipping() then return end
    if #structGuideCfgs == 0 then return end

    local friendlyStr = ""
    for _, stageCfg in ipairs(structGuideCfgs) do
        friendlyStr = friendlyStr .. " " .. (tostring(stageCfg.GuideStageId) or "nil") .. "-" .. stageCfg.RowDescription .. ", "
    end
    friendlyStr = friendlyStr .. ". New Player Guide Stage Ids: " 
    for _, stageCfg in ipairs(newPlayerGuideCfgs) do
        friendlyStr = friendlyStr .. " " .. (tostring(stageCfg.GuideStageId) or "nil") .. "-" .. stageCfg.RowDescription .. ", "
    end

    log("OnStructTutorialSkippedByNewPlyerGuide", friendlyStr)
    Module.CommonTips:ShowSimpleTip(
        StringUtil.SequentialFormat(Module.Guide.Config.Loc.structGuideTriggerFailedByNewPlayerGuide, friendlyStr),
        5)
end


---@param guideStageCfg FGuideStageClientOnlyConfig
function GuideLogic.IsGuideBatchMatch(guideStageCfg)
    if  guideStageCfg.GuideBatch > Server.GuideServer.playerAccountBatch then
        log(string.format(" guideBatch %d > playerAccountBatch %d, GuideStageId: %d",
            guideStageCfg.GuideBatch,
            Server.GuideServer.playerAccountBatch,
            guideStageCfg.GuideStageId)
        )
        return false
    end
    return true
end


local function _LogSpecialStartFailedGuide()
    if VersionUtil.IsShipping() then return end
    local stageIds = Module.Guide.Field.InvalidBatchGuideStageIdss:Get()
    if #stageIds > 0 then
        local newIds = {}
        -- record that has been notified
        local map = Module.Guide.Field.ShowedInvlaidBatchGuideStageIds
        for _, stageId in ipairs(stageIds) do
            if map:Get(stageId) ~= true then
                map:Set(stageId, true)
                table.insert(newIds, stageId)
            end
        end
        if #newIds > 0 then
            GuideLogic._OnGuideSkipByAccountBatchNotMatch(
                GuideLogic._GuideStageIds2GuideStageCfgs(stageIds)
            )
        end
    end
    Module.Guide.Field.InvalidBatchGuideStageIdss:Reset()
end

-- 尝试启动满足条件的引导，如果有多个，选择优先级高的
-- 对于体系教学，新手教学优先
---@param guideStageList FGuideStageClientOnlyConfig[] 引导阶段列表
GuideLogic.TryStartGuideStage = function (guideStageList)
    if #guideStageList == 0 then
        _LogSpecialStartFailedGuide()
        return
    end

    local debugStr = ""
    local newPlayerGuideConfigs = {} ---@type FGuideStageClientOnlyConfig[] 
    local structureGuideConfigs = {} ---@type FGuideStageClientOnlyConfig[] default batch is 0 (valid all time) 

    --- classify to new player guide/struct guideIds
    for _, stageCfg in ipairs(guideStageList) do
        debugStr = debugStr .. stageCfg.GuideStageId .. ","
        if GuideLogic.IsStructTutorial(stageCfg) then
            table.insert(structureGuideConfigs, stageCfg)
        else
            table.insert(newPlayerGuideConfigs, stageCfg)
        end
    end
    loginfo("TryStartGuideStage, input guides: ", debugStr)

    -- 1. get the max priority in stages
    -- 2. new player guide first
    local configs = nil
    if #newPlayerGuideConfigs > 0 then
        configs = newPlayerGuideConfigs
        GuideLogic._OnStructTutorialSkippedByNewPlyerGuide(structureGuideConfigs, newPlayerGuideConfigs)
    else
        configs = structureGuideConfigs
    end



    ---@type table
    local waitStageCfg = nil
    local maxPriority = -1
    for _, stageCfg in ipairs(configs) do
        if stageCfg.Priority > maxPriority then
            maxPriority = stageCfg.Priority
            waitStageCfg = stageCfg
        end
    end

    if not waitStageCfg then
        return
    end

    local result = GuideLogic._DoStartStageGuideInternal(waitStageCfg.GuideStageId)
    log("TryStart guide stage list, DoStartStageGuide result:", result)

    -- handle struct tutorial skip & report logics
    do 
        local structTutorialIds = {}
        local mapGuideStageId2StructTutorialId = Server.GuideServer.mapGuideStageId2StructTutorialId
        for _, stageCfg in ipairs(structureGuideConfigs) do
            local bInsert = true
            if stageCfg.GuideStageId == waitStageCfg.GuideStageId then
                if result == true then
                    bInsert = false
                else
                    log("this struct tutorial start failed, id", stageCfg.GuideStageId)
                end
            end

            if bInsert then
                local structTutorialId = mapGuideStageId2StructTutorialId[stageCfg.GuideStageId]
                if nil ~= structTutorialId then
                    table.insert(structTutorialIds, structTutorialId)
                end
            end
        end
        if #structTutorialIds > 0 then
            log("TryStart guide stage list, report struct tutorial skip")
            Server.GuideServer:ReportStructGuideSkip(structTutorialIds)
        end
    end

    -- handle in game replay logics
    do 
        local guideField = Module.Guide.Field
        for _, stageCfg in ipairs(newPlayerGuideConfigs) do
            if result == true and stageCfg == waitStageCfg then
            elseif stageCfg.IsNeedReplayOnOtherGuideEnd then
                if not table.contains(guideField.inGameWaitPlayQueue, stageCfg) then
                    table.insert(guideField.inGameWaitPlayQueue, stageCfg)
                end
            end
        end
        for _, stageCfg in ipairs(structureGuideConfigs) do
            if result == true and stageCfg == waitStageCfg then
            elseif stageCfg.IsNeedReplayOnOtherGuideEnd then
                if not table.contains(guideField.inGameWaitPlayQueue, stageCfg) then
                    table.insert(guideField.inGameWaitPlayQueue, stageCfg)
                end
            end
        end
        for _, stageCfg in ipairs(guideField.inGameWaitPlayQueue) do
            log("inGameWaitPlayQueue cur have", stageCfg.GuideStageId)
        end
    end
end

-- 尝试打断当前执行的单步引导，如果满足打断条件的话
GuideLogic.TryStopGuideStep = function (reason)
    if GuideLogic.IsGuiding() then
        local guideField = Module.Guide.Field
        local curGuideId = guideField:GetCurGuideId()
        local guideConfig = guideField:GetGuideCfg(curGuideId)
        if guideConfig.StopStepEvent then
            for _, conditionId in pairs(guideConfig.StopStepEvent) do
                if reason == conditionId then
                    -- 打断单步引导的条件
                    log("GuideLogic.TryStopGuideStep reason:", reason, guideConfig.GuideStageId)
                    GuideLogic.DoStopAllGuide()
                    GuideConfig.EGuideEvent.evtGuideEnd:Invoke(guideConfig.GuideStageId)
                    return
                end
            end
        end
    end
end

GuideLogic.IsNewPlayerGuideFinished = function ()
    local newPlayerGuideStage = GuideLogic.GetCurNewPlayerGuideStage()
    return newPlayerGuideStage == nil or newPlayerGuideStage < 1
end

GuideLogic.IsNewPlayerGuiding = function ()
    local guideField = Module.Guide.Field
    if GuideLogic.IsGuiding() then
        local curGuideId = guideField:GetCurGuideId()
        local curGuideStageConfig = guideField:GetGuideStageCfg(guideField:GetGuideCfg(curGuideId).GuideStageId)
        return curGuideStageConfig and curGuideStageConfig.IsNewPlayerGuide or false
    end
    return false
end

GuideLogic.IsNewPlayerMatchGuiding = function ()
    if GuideLogic.IsGuiding() then
        -- local newPlayerGuideStage = GuideLogic.GetCurNewPlayerGuideStage()
        local newPlayerGuideStage = Module.Guide.Field:GetCurGuideStage()
        return newPlayerGuideStage == GuideConfig.EGuideStage.newPlayerGuideStage1
    end
    return false
end

function GuideLogic.IsNewPlayerMatchFinish()
    local newPlayerGuideStage = GuideLogic.GetCurNewPlayerGuideStage()
    return newPlayerGuideStage == nil or newPlayerGuideStage ~= 1
end


GuideLogic.IsIntroFinished = function ()
    local ret = true
    -- 引导自己拉到数据了，就以引导的为准；没拉到以登录协议返回的为准
    if Server.GuideServer:IsInitFinish() then
        log("IsIntroFinished? cur stage id", GuideLogic.GetCurNewPlayerGuideStage())
        ret = GuideLogic.GetCurNewPlayerGuideStage() ~= GuideConfig.EGuideStage.newPlayerGuideStageIntro
    else
        --local curStageList = Module.Login:GetCurGuideStage()
        local curStageList = Server.GuideServer.curGuideStageFromLogin
        if curStageList then
            for _, stageId in pairs(curStageList) do
                if stageId == GuideConfig.EGuideStage.newPlayerGuideStageIntro then
                    ret = false
                    break
                end
            end
        end
    end
    log("IsIntroFinished", ret)
    return ret
end

GuideLogic.SetIntroFinished = function (callback)
    log("SetIntroFinished")
    Server.GuideServer:SetGuidePassed(GuideConfig.EGuideStage.newPlayerGuideStageIntro, callback)
end

GuideLogic.GetCurNewPlayerGuideStage = function ()
    if not Server.GuideServer.newPlayerGuideStage then return end
    local newPlayerGuideStageList = Server.GuideServer.newPlayerGuideStage
    for _, stageId in ipairs(newPlayerGuideStageList) do
        if table.contains(GuideConfig.NewPlayerGuideStageList, stageId) then
            return stageId
        end
    end
end

GuideLogic.GetCurNewPlayerGuideStageNotForce = function ()
    if not Server.GuideServer.newPlayerGuideStage then 
        return {}
    end
    local newPlayerGuideStageList = Server.GuideServer.newPlayerGuideStage
    local ret = {}
    for _, stageId in ipairs(newPlayerGuideStageList) do
        if not table.contains(GuideConfig.NewPlayerGuideStageList, stageId) then
            table.insert(ret, stageId)
        end
    end
    return ret
end

GuideLogic.ShowPopWindowGuide = function (explainId)
    if not explainId then return end
    local guideExplainConfig = GuideConfig.TableGuideExplainConfig[explainId]
    if not guideExplainConfig then
        log("guideExplainConfig is nil", explainId)
        return
    end
    local showData = {}
    for _, popWindowCfgId in ipairs(guideExplainConfig.PopWindowConfigId) do
        local guidePopWindowConfig = GuideConfig.TableGuidePopWindowConfig[popWindowCfgId]
        local onePageInfo = {}
        onePageInfo.descInfoList = GuideLogic.GetRealPopWindowDesc(guidePopWindowConfig)
        onePageInfo.title = guidePopWindowConfig.Title
        table.insert(showData, onePageInfo)
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.GuideExplainUI, nil, nil, showData)
end

GuideLogic.OnNotifyResolutionResized = function (newSizeX, newSizeY)
    log("Screen size: ", newSizeX, newSizeY)
	if GuideLogic.IsGuiding() then
        local guideData = Module.Guide.Field:GetCurGuideData()
        if guideData then
            guideData:Resize()
        end
    end
end

-- 如果uiName == nil，则匹配(uiPath, widgetName), 否则匹配（uiName, widgetName）
-- return targetWidget, targetUI
---comment
---@param uiPath string
---@param widgetName string
---@param uiName string
---@param bIsHudUI boolean
---@param bFuzzy boolean
---@return UIWidgetBase|nil, any|nil, UIWidgetBase[]|nil
GuideLogic._GetWidgetByPathInternal = function (uiPath, widgetName, uiName, bIsHudUI, bFuzzy)

    -- PROXY
    if string.find(widgetName, "guideProxy") then
        if string.find(widgetName, ".", 1, true) then
            local widgetNamePath = StringUtil.StringSplit(widgetName, ".")
            local root = Module.Guide.Field:GetProxyWidget(widgetNamePath[1])
            for idx, name in ipairs(widgetNamePath) do
                if idx~= 1 then
                    if root == nil then
                        break
                    end
                    if not string.isempty(name) then
                        local next = root:Wnd(name, UIWidgetBase)
                        root = next
                    end
                end
            end
            return root
        else
            return Module.Guide.Field:GetProxyWidget(widgetName)
        end
    end

    -- FUNCTIONAL: which may return targetWidgetList by functions
    if string.find(widgetName, "getWidgetFunc") then
        if string.find(widgetName, ".", 1, true) then
            local widgetNamePath = StringUtil.StringSplit(widgetName, ".")
            local root  = GuideLogic[widgetNamePath[1]]()
            for idx, name in ipairs(widgetNamePath) do
                if idx~= 1 then
                    if root == nil then
                        break
                    end
                    if not string.isempty(name) then
                        local next = root:Wnd(name, UIWidgetBase)
                        root = next
                    end
                end
            end
            return root
        else
            return GuideLogic[widgetName]()
        end
    end



    bFuzzy = setdefault(bFuzzy, true)

    -- HUD
    if bIsHudUI then
        local baseHud = UE.BaseHUD.GetHUD(GetWorld())
        if not baseHud then
            err("baseHud nil", uiName)
            return nil
        end
        local uiList = baseHud:GetPanel(uiName)
        if not uiList or #uiList < 1 then
            err("GetWidgetByPath hud uiName invalid", uiName)
            return nil
        end
        local targetUI = uiList[#uiList]
        local targetWidget
        if string.find(widgetName, ".", 1, true) then
            local widgetNamePath = StringUtil.StringSplit(widgetName, ".")
            local root = targetUI
            for _, name in ipairs(widgetNamePath) do
                if root == nil then
                    break
                end
                if hasdestroy(root) then
                    err("GetWidgetByPath root node destroyed", uiName, widgetName, name)
                    return nil
                end

                local next = ULuautils.GetWidgetByName(root, name)
                -- 模糊匹配一下所有子节点
                if not next and bFuzzy then
                    for _, node in pairs(ULuautils.PyGetAllWidgets(root)) do
                        if string.find(ULuautils.GetName(node), name) then
                            next = node
                            break
                        end
                    end
                end
                root = next
            end
            targetWidget = root
        else
            targetWidget = ULuautils.GetWidgetByName(targetUI, widgetName)
        end
        return targetWidget, targetUI
    end

    -- LUA UI
    local uiNavId
    if uiName and uiName ~= 'None' then
        uiNavId = UIName2ID[uiName]
    else
        local uiCls = UIBPPath2Cls[uiPath]
        uiNavId = UIBPCls2ID[uiCls]
        if not uiNavId then
            err("GetWidgetByPath uiPath invalid", uiPath)
            return nil
        end
    end

    local uiSetting = UITable[uiNavId]
    if uiSetting == nil then
        err("GetWidgetByPath uiPath invalid", uiPath)
        return nil
    end

    local targetWidget, targetUI
    if uiSetting.UILayer == EUILayer.Stack then
        targetUI = Facade.UIManager:GetStackUIByUINavId(uiNavId)
        if targetUI then
            targetUI = getfromweak(targetUI)
        end
    elseif uiSetting.UILayer == EUILayer.HUD then
        local controller = Facade.UIManager:GetHUDLayerController()
        targetUI = controller:GetUIByID(uiNavId)
    elseif uiSetting.UILayer == EUILayer.Pop then
        local controller = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
        targetUI = controller:TryGetLastPopUI()
    elseif uiSetting.UILayer == EUILayer.Top then
        local controller = Facade.UIManager:GetLayerControllerByType(EUILayer.Top)
        local targetUIList = controller:GetLayerList(EUILayer.Top)
        for _, uiIns in pairs(targetUIList) do
            if uiIns.UINavID == uiNavId then
                targetUI = uiIns
                break
            end
        end
    elseif uiSetting.UILayer == EUILayer.Root then
        local controller = Facade.UIManager:GetLayerControllerByType(EUILayer.Root)
        targetUI = controller:GetNewestUI()
    else
        local controller = Facade.UIManager:GetLayerControllerByType(uiSetting.UILayer)
        local targetUIList = controller:GetLayerList(uiSetting.UILayer)
        for _, uiIns in pairs(targetUIList) do
            if uiIns.UINavID == uiNavId then
                targetUI = uiIns
                break
            end
        end
    end

    if targetUI then
        if string.find(widgetName, ".", 1, true) then
            local widgetNamePath = StringUtil.StringSplit(widgetName, ".")
            local root = targetUI
            for _, name in ipairs(widgetNamePath) do
                if root == nil then
                    break
                end
                if not string.isempty(name) then
                    local next = root:Wnd(name, UIWidgetBase)
                    if not next then
                        --next = root:FindWidgetByName(name)
                        if bFuzzy then
                            for _, node in pairs(ULuautils.PyGetAllWidgets(root)) do
                                if string.find(ULuautils.GetName(node), name) then
                                    next = node
                                    break
                                end
                            end
                        else
                            next = ULuautils.GetWidgetByName(root, name)
                        end
                    end
                    root = next
                end
            end
            targetWidget = root
        else
            targetWidget = targetUI:Wnd(widgetName, UIWidgetBase)
            if not targetWidget then
                if bFuzzy then
                    for _, node in pairs(ULuautils.PyGetAllWidgets(targetUI)) do
                        if string.find(ULuautils.GetName(node), widgetName) then
                            targetWidget = node
                            break
                        end
                    end
                else
                    --targetWidget = targetUI:FindWidgetByName(widgetName)
                    targetWidget = ULuautils.GetWidgetByName(targetUI, widgetName)
                end
            end
        end
    end

    if targetWidget == nil then
        err("GetWidgetByPath not find", uiPath, uiNavId, widgetName)
    end
    return targetWidget, targetUI
end


---@param uiPath  string bp path
---@param widgetName string widget in UI Outline
---@param uiName string name in LuaBpAssetConfig/hud 
---@param bIsHudUI boolean
---@param bFuzzy boolean
---@return unknown|nil
---@return unknown|nil
---@return unknown|nil
function GuideLogic.GetWidgetByPath(uiPath, widgetName, uiName, bIsHudUI, bFuzzy)
    local targetWidget, targetUI , targetWidgetList= GuideLogic._GetWidgetByPathInternal(uiPath, widgetName, uiName, bIsHudUI, bFuzzy)
    if not targetWidget and not targetWidgetList then
        logerror("GetWidgetByPath fail", uiPath, widgetName, uiName)
    end
    return targetWidget, targetUI, targetWidgetList
end

function GuideLogic.IsValidWidget(widget)
    if not widget or not isvalid(widget) then return false end
    if type(widget) == "table" and widget.__cppinst and not slua.isValid(widget.__cppinst) then
        return false
    end
    if hasdestroy(widget) then
        return false
    end
    return true
end

GuideLogic.GetGlobalPosAndSizeByWidget = function (widget)
    if not GuideLogic.IsValidWidget(widget) then
        err("GetGlobalPosAndSizeByWidget fail, nil widget", Module.Guide.Field:GetCurGuideId())
        return FVector2D(0, 0), FVector2D(0, 0)
    end


    if not widget.GetCachedGeometry then
        err("widget.GetCachedGeometry fail, nil func", widget, Module.Guide.Field:GetCurGuideId())
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local alignWidgetGeometry = widget:GetCachedGeometry()
	local itemScreenPosLT = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    local itemScreenPosRB = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(1, 1))

    return itemScreenPosLT,  FVector2D(itemScreenPosRB.X - itemScreenPosLT.X, itemScreenPosRB.Y- itemScreenPosLT.Y)
end

GuideLogic.GetLocalPosAndSize = function (parentWidget, globalPos, globalSize)
    if parentWidget == nil then
        err("GetLocalPosAndSize fail, nil parentWidget")
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local parentWidgetGeometry = parentWidget:GetCachedGeometry()
    if parentWidgetGeometry == nil then
        err("GetLocalPosAndSize fail, nil parentWidgetGeometry")
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local localPos = parentWidgetGeometry:AbsoluteToLocal(globalPos)
    local localSize = parentWidgetGeometry:AbsoluteToLocal(globalPos + globalSize) - localPos
    return localPos,  localSize
end

---@param clickCfg FGuideClickConfig
GuideLogic.GetRealClickTipsText = function(clickCfg)
    local ret = clickCfg.TipsText -- 默认使用手游的文本

    local bHDNoFallbackText = (clickCfg.Options & GuideConfig.EGuideClickConfigOption.HDNoFallbackText) ~= 0

    if IsHD() then
        if not string.isempty(clickCfg.TipsTextForHD) then
            ret = clickCfg.TipsTextForHD
        else
            if bHDNoFallbackText then
                -- 2025/8/7 为了兼容之前的文本配置，PC会以默认（mobile）文本作为fallback
                ret = ""
            else
                --- ret  already set to mobile text
            end
        end
    end

    if IsHD() and WidgetUtil.IsGamepad() then

        -- 手柄默认不采用手游/default文本
        -- if bHDNoFallbackText then
        ret = ""

        local tipsTextForGamepad = clickCfg.TipsTextForGamePad
        if WidgetUtil.IsXBoxGamepad() and #tipsTextForGamepad >= 1 then
            local text = tipsTextForGamepad[1]
            if text and not string.isempty(text) then
                ret = text
            end
        end
        if WidgetUtil.IsPSGamepad() and #tipsTextForGamepad >= 2 then
            local text = tipsTextForGamepad[2]
            if text and not string.isempty(text) then
                ret = text
            end
        end
    end
    -- END MODIFICATION

    return ret
end

GuideLogic.GetRealClickTipsAudio = function (clickCfg)
    if not clickCfg then return end

    --BEGIN MODIFICATION @ VIRTUOS : 引导Console扩展
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if clickCfg.TipsAudioForConsole and FLuaHelper.SoftObjectPtrToString(clickCfg.TipsAudioForConsole) ~= "" then
                return clickCfg.TipsAudioForConsole
            end
        end
    end
    -- END MODIFICATION

    --BEGIN MODIFICATION @ VIRTUOS : 引导GamePad扩展
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if clickCfg.TipsAudioForGamePad and FLuaHelper.SoftObjectPtrToString(clickCfg.TipsAudioForGamePad) ~= "" then
                return clickCfg.TipsAudioForGamePad
            end
        end
    end    
    -- END MODIFICATION

    if DFHD_LUA == 1 then
        if clickCfg.TipsAudioForHD and FLuaHelper.SoftObjectPtrToString(clickCfg.TipsAudioForHD) ~= "" then
            return clickCfg.TipsAudioForHD
        end
    end
    return clickCfg.TipsAudio
end

GuideLogic.GetRealPopWindowDesc = function (popWindowCfg)

    --BEGIN MODIFICATION @ VIRTUOS : 引导Console扩展
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if popWindowCfg.DescInfoForHDWithGamepad and #popWindowCfg.DescInfoForHDWithGamepad > 0 then
                return popWindowCfg.DescInfoForHDWithGamepad
            end
        end
    end    
    -- END MODIFICATION

    --BEGIN MODIFICATION @ VIRTUOS : 引导GamePad扩展
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if popWindowCfg.DescInfoForGamePad and #popWindowCfg.DescInfoForGamePad > 0 then
                return popWindowCfg.DescInfoForGamePad
            end
        end
    end    
    -- END MODIFICATION

    if DFHD_LUA == 1 then
        if popWindowCfg.DescInfoForHD and #popWindowCfg.DescInfoForHD > 0 then
            return popWindowCfg.DescInfoForHD
        end
    end
    return popWindowCfg.DescInfo
end

GuideLogic.GetRealPopWindowAudio = function (popWindowCfg)

    --BEGIN MODIFICATION @ VIRTUOS : 引导Console扩展
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if popWindowCfg.AudioForHDWithGamepad ~= nil then
                return popWindowCfg.AudioForHDWithGamepad
            end
        end
    end    
    -- END MODIFICATION

    --BEGIN MODIFICATION @ VIRTUOS : 引导GamePad扩展
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            if popWindowCfg.AudioForGamePad ~= nil then
                return popWindowCfg.AudioForGamePad
            end
        end
    end    
    -- END MODIFICATION

    if IsHD() then
        return  popWindowCfg.AudioForHD
    end
    return  popWindowCfg.Audio
end

GuideLogic.StaticAsyncSetImagePath = function (imagePath, callback)
    if imagePath == nil then
        err("GuideLogicStaticAsyncSetImagePath = ", imagePath)
        return
    end

    local function OnImageLoadFinished(resList)
        local imageAsset = nil
        if type(imagePath) == "string" then
            imageAsset = resList[imagePath]
        else
            imageAsset = resList[imagePath.AssetPathName]
        end

        callback(imageAsset)
    end
    Facade.ResourceManager:AsyncLoadResource(Facade.UIManager.UIImagePaperSpriteStub, imagePath, OnImageLoadFinished)
end

GuideLogic.CheckSafeHouseIsTop = function ()
    if Module.Guide.Field.loadingUINumUpSafeHouse > 0 then
        return
    end
    if Facade.UIManager:GetStackUICount() ~= 1 then
        return false
    end
    return true
end

GuideLogic.IsGuideGMShow = function ()
    --return not CloseModuleType.bIsCloseGM or IsInEditor() or Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideGmShowState)
    if VersionUtil.IsShipping() then
        return false
    end
    return not CloseModuleType.bIsCloseGM or IsInEditor()
end

-- 当前步骤是否为强引导
-- 强引导为：有全屏遮罩，玩家不能自由操作(IsSwallowTouch)
--
GuideLogic.IsForceGuide = function ()
    local curGuideData= Module.Guide.Field:GetCurGuideData()
    if not curGuideData then
        return false
    end
    local t = curGuideData:GetGuideType()
    local ts = GuideConfig.EGuideData
    if t == ts.WeakClick   then
        return false
    elseif t == ts.HDWeakClick then
        return false
    elseif t == ts.HDPopDescAndWeakClick then
        return false
    elseif t == ts.Event then
        return curGuideData:GetGuideCfg().IsSwallowTouch
    elseif t == ts.HDPopTips then
        return curGuideData:GetGuideCfg().IsSwallowTouch
    elseif t == ts.Delay then
        return curGuideData:GetGuideCfg().IsSwallowTouch
    end

    return true
end

-- 2025/8/5 dexzhou: GHD_GuideDisableInput deprecated, no cpp reference existent
-- 刷新一下当前步骤是否禁用pc端键盘输入
GuideLogic.UpdateHDInputState = function (newState)
    if DFHD_LUA == 1 then
        --屏蔽输入
        newState = setdefault(newState, not GuideLogic.IsForceGuide() and Module.Guide.Field:GetInputStateInGame())
        -- local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        -- if isvalid(localCtrl) and localCtrl.PlayerStopRequest then -- 提审分支win包不关闭输入
        --     log("UpdateHDInputState", newState)
        --     if not newState then
        --         localCtrl:PlayerStopRequest()
        --     end
        --     localCtrl:SetClientEnableProcessPlayerInput(newState)
        -- end

        log("UpdateHDInputState", newState)
        local hudStateManager = UE.HUDStateManager.Get(GetWorld())
        if isvalid(hudStateManager) then
            if newState then
                hudStateManager:RemoveState(UE.GameHUDSate.GHS_GuideDisableInput, true)
            else
                hudStateManager:AddState(UE.GameHUDSate.GHS_GuideDisableInput, true)
            end
        end
    end
end

-- 设置游戏输入响应的状态。在引导数据没有正确获取的情况下，禁止玩家自由操作。
GuideLogic.SetInputStateAllPlatform = function (bEnableInput)
    logTempError("SetInputStateAllPlatform", bEnableInput, Server.GuideServer:IsWaitServerUpdateGuideData(), GuideLogic.IsInPauseState())
    bEnableInput = setdefault(bEnableInput, not Server.GuideServer:IsWaitServerUpdateGuideData() or GuideLogic.IsInPauseState())

    -- pc 键盘
    --GuideLogic.UpdateHDInputState(bEnableInput)

    -- 移动端 点击
    if bEnableInput then
        Facade.UIManager:EnableInput(EInputChangeReason.BusinessPending)
        Module.Guide.Field:RemoveFlagShowUI(GuideConfig.EGuideShowUIFlag.WaitGuideData)
    else
        Facade.UIManager:DisableInput(EInputChangeReason.BusinessPending)
        Module.Guide.Field:AddFlagShowUI(GuideConfig.EGuideShowUIFlag.WaitGuideData)
    end
end

-- 设置药品轮盘
GuideLogic.SetCustomRooleteeMedItem = function (itemId, bForceUseCustom)
    bForceUseCustom = setdefault(bForceUseCustom, itemId ~= nil)
    Module.HUDToolBar:SetFlagDisableCommendInGuide(bForceUseCustom)
    Module.Guide.Field.bChangeRouletteState = bForceUseCustom

    if itemId == nil and bForceUseCustom then
        return false
    end

    local curMedItemsInOrder = Module.HUDToolBar:GetMedItemsInOrder()
    if not curMedItemsInOrder then
        return false
    end

    local medItemWrap
    for _, medItem in ipairs(curMedItemsInOrder) do
        if itemId == medItem.id then
            medItemWrap = medItem
        end
    end
    if medItemWrap then
        Module.HUDToolBar:SetCurSelectMedkitItemWrap(medItemWrap)
        return true
    end
    
    return false
end

GuideLogic.DelayOpenUIInGame = function()
    local guideField = Module.Guide.Field
    log("DelayOpenUIInGame", #guideField._delayShowDataInGame)
    for _, data in ipairs(guideField._delayShowDataInGame) do
        log("DelayOpenUIInGame", data[1], data[2], data[3])
        GuideLogic.CommonCloseCacheGuideUI(data[1], data[2])
        GuideLogic.CommonOpenCacheGuideUI(data[1], data[2], data[3])
    end
    guideField._delayShowDataInGame = {}
end

GuideLogic.AddDelayOpenUIInGame = function(data)
    local guideField = Module.Guide.Field
    log("AddDelayOpenUIInGame", #guideField._delayShowDataInGame)
    table.insert(guideField._delayShowDataInGame, data)
end

GuideLogic.CommonOpenCacheGuideUI = function (uiId, cfgId, showData)
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.LobbyToGame or curGameFlow == EGameFlowStageType.SafeHouseToGame then
        log("CommonOpenCacheGuideUI, need delay", curGameFlow, uiId, cfgId)
        GuideLogic.AddDelayOpenUIInGame({uiId, cfgId, showData})
        return
    end

    local uiIns = Module.Guide.Field:GetCacheUIIns(uiId)
    local OnUILoadFinish = function(newUIIns)
        local uiDict = Module.Guide.Field.curUseUIDict[uiId]
        if not uiDict then
            uiDict = {}
            Module.Guide.Field.curUseUIDict[uiId] = uiDict
        end
        uiDict[cfgId] = newUIIns
        newUIIns:SetData(showData)
    end
    -- TODO: check gc
    if uiIns then
        uiIns:StopAllAnimations()
        OnUILoadFinish(uiIns)
        uiIns:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        uiIns:Show()
    else
        Facade.UIManager:AsyncShowUI(uiId, OnUILoadFinish, nil, showData)
    end
end

GuideLogic.CommonCloseCacheGuideUI = function (uiId, cfgId)
    local uiDict = Module.Guide.Field.curUseUIDict[uiId]
    if not uiDict then return end
    if cfgId and cfgId > 0 then
        if uiDict[cfgId] then
            uiDict[cfgId]:RecycleSelf()
            uiDict[cfgId] = nil
            return true
        end
    else
        for _, uiIns in pairs(uiDict) do
            uiIns:RecycleSelf()
        end
        Module.Guide.Field.curUseUIDict[uiId] = {}
    end
    return false
end

GuideLogic.CommonGetCacheGuideUI = function (uiId, cfgId)
    local uiDict = Module.Guide.Field.curUseUIDict[uiId]
    if not uiDict then return end
    if cfgId and cfgId > 0 then
        return uiDict[cfgId]
    end
end

GuideLogic.CommonSetGuideUIValidState = function (uiId, cfgId, bValid)
    local uiDict = Module.Guide.Field.curUseUIDict[uiId]
    if not uiDict then return end
    if cfgId and cfgId > 0 then
        if uiDict[cfgId] then
            local uiIns = uiDict[cfgId]
            if bValid then
                if uiIns.Restart then
                    uiIns:Restart()
                else
                    uiIns:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                end
            else
                if uiIns.Pause then
                    uiIns:Pause()
                else
                    uiIns:SetVisibility(ESlateVisibility.Collapsed)
                end
            end
            return true
        end
    end
    return false
end

-------------------------------
--region 获取特殊控件 getWidgetFunc

GuideLogic.getWidgetFuncMiniMapExit = function (widget)
    local UDFMHudHelper = import "DFMHudHelper"
    local ret = {}
    local filed = Module.Guide.Field
    filed.miniMapMarkItemListInGame = {}
    for _, targetWidget in pairs(UDFMHudHelper.GetActiveExitIconInBigMap(GetWorld())) do
        local markerWidget = ULuautils.GetWidgetByName(targetWidget, "DFButton_Select")
        --local exitImg = ULuautils.GetWidgetByName(markerWidget, "PublicItem")
        local exitImg = markerWidget
        table.insert(ret, exitImg)
        table.insert(filed.miniMapMarkItemListInGame, targetWidget)
    end
    return nil, nil, ret
end

GuideLogic.getWidgetFuncMiniMapHighValueBox = function (widget)
    local UDFMHudHelper = import "DFMHudHelper"
    local ret = {}
    for _, targetWidget in pairs(UDFMHudHelper.GetHighValueBoxIconInBigMap(GetWorld())) do
        local markerWidget = ULuautils.GetWidgetByName(targetWidget, "DFButton_Select")
        local exitImg = markerWidget
        table.insert(ret, exitImg)
    end
    return nil, nil, ret
end

GuideLogic.getWidgetFuncQuestObject = function (widget)
    local UDFMHudHelper = import "DFMHudHelper"
    local ret = {}
    local specGuideItemInfo = Module.Guide:GetNewPlayerGuideItemInfo(GuideConfig.NewPlayerGuideSpecItem.questObjectIdInGame)
    local questId
    if specGuideItemInfo then
        questId = tonumber(specGuideItemInfo[1])
    else
        return nil
    end
    local targetWidget = UDFMHudHelper.GetQuestObjectionIconInBigMap(GetWorld(), questId)
    return targetWidget
end

GuideLogic.getWidgetFuncMiniMapExitMark = function ()
    local filed = Module.Guide.Field
    for _, targetWidget in pairs(filed.miniMapMarkItemListInGame) do
        if targetWidget.bIsSelected then
            local markerWidget = targetWidget.MarkBtn
            return markerWidget, nil, nil
        end
    end
    return nil, nil, nil
end

GuideLogic.getWidgetFuncWarehouseSlotView = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.WarehouseMain)
    if not targetUI then return nil end
    -- type(targetWidget): InvSlotView
    local targetWidget = targetUI:GetWarehousePanel():GetWarehouseWidget():GetWarehouseSlotView()
    return targetWidget
end

GuideLogic.getWidgetFuncWarehouseWidget = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.WarehouseMain)
    if not targetUI then return nil end
    local targetWidget = targetUI:GetWarehousePanel():GetWarehouseWidget()
    return targetWidget
end

GuideLogic.getWidgetFuncWarehouseEquipPanel = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.WarehouseMain)
    if not targetUI then return nil end
    -- type(targetWidget): InvSlotView
    local targetWidget = targetUI:GetWarehouseEquipPanel()
    return targetWidget
end

GuideLogic.getWidgetFuncLootingDeadBody = function()
    local lootingUI = Module.Looting:GetMainPanel()
    if not lootingUI then
        err("looting ui is nil")
        return nil
    end
    local targetUI
    if IsHD() then
        ---@type LootingMainView_HD
        local subUI = lootingUI
        if subUI.UINavID ~= UIName2ID.LootingMainView_HD then
            err("subUI.UINavID ~= UIName2ID.LootingMainView_HD")
            return nil
        end
        ---@type LootDeadbodyPanel_HD
        local deadBodyEquipPnael = subUI._wtLootDeadbodyPanel
        return deadBodyEquipPnael
    else
        targetUI = lootingUI:GetCurrentSubView()
        if not targetUI then
            err("_currentSubView ui is nil")
            return nil
        end
        targetUI = targetUI._wtLootDeadbodyPanel
        if not targetUI then
            err("_wtLootDeadbodyPanel ui is nil")
            return nil
        end
    end

    return targetUI
end

GuideLogic.getWidgetFuncCurShopList = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.ShopMainPanel)
    if not targetUI then return nil end
    local targetWidget = targetUI:Wnd("NamedSlot_ShopContent", UINamedSlot)
    if not targetWidget then return nil end
    local bSearched = false
    for _, widget in pairs(targetWidget:GetAllChildren()) do
        if widget.UINavID == UIName2ID.ShopMerchantContent then
            targetUI = widget
            bSearched = true
            break
        end
    end
    if not bSearched then return nil end
    bSearched = false
    targetWidget = targetUI._wtScrollGridShopItems
    local ret = {}
    for _, widget in pairs(targetWidget:GetAllChildren()) do
        table.insert(ret, widget)
    end
    return ret
end

GuideLogic.getWidgetFuncAssemblySelectionMain = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.AssemblySelectionMain)
    return targetUI
end

GuideLogic.getWidgetFuncAssemblyBulletView = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.AssemblyQuickOperationMainView)
    return targetUI
end

GuideLogic.getWidgetFuncAssemblyMedicineViewUI = function ()
    local targetUI = Facade.UIManager:GetStackUIByUINavId(UIName2ID.AssemblyQuickOperationMainView) 
        or Facade.UIManager:GetStackUIByUINavId(UIName2ID.AssemblyHDQuickOperationMainView)
    return targetUI
end

---部署界面新手引导：找到符合条件的船的选择框
GuideLogic.getWidgetFuncRedeployFindSuitableVehicleSelectWidget = function (widget)
    local UBreakthroughUtil = import "BreakthroughUtil"

    local targetWidget = UBreakthroughUtil.FindSuitableVehicleSelectWidget(GetWorld())
    return targetWidget
end

---部署界面新手引导：找到符合条件的船的选择框 用于辅助计算尺寸和位置
GuideLogic.getWidgetFuncRedeployFindSuitableVehicleSelectWidgetForCalSizeAndPos = function (widget)
    local UBreakthroughUtil = import "BreakthroughUtil"

    local targetWidget = UBreakthroughUtil.FindSuitableVehicleSelectWidgetForCalSizeAndPos(GetWorld())
    return targetWidget
end

---部署界面新手引导：找到符合条件的据点的选择框
GuideLogic.getWidgetFuncRedeployFindSuitableCampPointSelectWidget = function (widget)
    local UBreakthroughUtil = import "BreakthroughUtil"

    local targetWidget = UBreakthroughUtil.FindSuitableCampPointSelectWidget(GetWorld())
    return targetWidget
end

---部署界面新手引导：找到符合条件的据点 或 船 的选择框
GuideLogic.getWidgetFuncRedeployFindSuitableCampPointOrBoatSelectWidget = function (widget)
    local UBreakthroughUtil = import "BreakthroughUtil"

    local targetWidget = UBreakthroughUtil.FindSuitableCampPointSelectWidget(GetWorld())

    if(targetWidget == nil) then
        targetWidget = UBreakthroughUtil.FindSuitableVehicleSelectWidget(GetWorld())
    end
    return targetWidget
end

---部署界面长时间停留引导：找到符合条件的未添加指引的基地点添加指引
GuideLogic.getWidgetFuncLongStayRedeployFindCampPointSelectWidget = function (widget)
    local UBreakthroughUtil = import "BreakthroughUtil"
    local targetWidget = UBreakthroughUtil.FindLongStayRedeployCampPointSelectWidget(GetWorld())
    return targetWidget
end


GuideLogic.getWidgetFuncHeroViewSkills = function ()
    return Module.CommonBar:GetBottomBarItemByActionName("ViewSkills")
end

-- AKA get Looting Backpack Panel
function GuideLogic.getWidgetFuncLootingWarehouse()
    local lootingUI = Module.Looting:GetMainPanel()

    if not lootingUI then
        err("looting ui is nil")
        return nil
    end
    if IsHD() then
        return lootingUI._wtLootBackpackPanel ---@type LootBackpackPanelV2_HD
    end

    local curSubView= lootingUI:GetCurrentSubView()
    if not curSubView or curSubView.UINavID ~= UIName2ID.LootingMainView then
        err("_currentSubView ui is nil or not LootingMainView")
        return nil
    end

    return curSubView._wtLootingEquipPanelV1  ---@type LootDeadbodyPanel
end

function GuideLogic.getWidgetFuncLootingMainUI()
    return Module.Looting:GetMainPanel()
end

--- 获取looting 界面不同Slot SlotView
---@param slotType ESlotType
---@param bPlayer boolean self or deadbody
function GuideLogic.GetLootingContainerView(slotType, bPlayer)
    bPlayer = setdefault(bPlayer, true)

    local lootingUI = Module.Looting:GetMainPanel()
    if not lootingUI then
        err("looting ui is nil")
        return nil
    end

    if IsHD() then
        ---@type LootingMainView_HD
        local subUI = lootingUI
        if subUI.UINavID ~= UIName2ID.LootingMainView_HD then
            err("subUI.UINavID ~= UIName2ID.LootingMainView_HD")
            return nil
        end
        if bPlayer then
            ---@type LootBackpackPanelV2_HD
            equipmentView = subUI._wtLootBackpackPanel
            if equipmentView.UINavID ~= UIName2ID.LootBackpackPanelV2_HD then
                err("equipmentView.UINavID ~= UIName2ID.LootBackpackPanelV2_HD")
                return nil
            end
            if not equipmentView then
                err("_wtLootEquipPanel ui is nil")
                return nil
            end

            if slotType == ESlotType.ChestHangingContainer then
                return equipmentView._wtCHContainerView
            elseif slotType == ESlotType.Pocket then
                return equipmentView._wtPocketContainerView
            elseif slotType == ESlotType.BagContainer then
                return equipmentView._wtBagContainerView
            elseif slotType == ESlotType.KeyChainContainer then
                return equipmentView._wtKeyContainerView
            elseif slotType == ESlotType.SafeBoxContainer then
                return equipmentView._wtSafeBoxContainerView
            else
                assert(false, "unimplemented")
            end
        else
            if slotType == ESlotType.NearbyContainer then
                return subUI._wtLootingLitter._wtContainerPanel
            else
                assert(false, "unimplemented")
            end
        end
    else
        ---@type LootingMainView
        local subUI = lootingUI:GetCurrentSubView()
        if subUI.UINavID ~= UIName2ID.LootingMainView then
            err("subUI.UINavID ~= UIName2ID.LootingMainView")
            return nil
        end
        if not subUI then
            err("_currentSubView ui is nil")
            return nil
        end

        -- 新手关获取安全箱
        if bPlayer == false and slotType == ESlotType.NearbyContainer then
            slotView = subUI._wtLootingLitter ---@type LootingLitter
            return slotView._wtContainerPanel
        end


        local slotView = nil
        if bPlayer then
            slotView = subUI._wtLootingEquipPanelV1
        else
            slotView = subUI._wtLootDeadbodyPanel
        end

        if not slotView then
            err("_wtLootingEquipPanelV1 ui is nil")
            return nil
        end

        return slotView:GetSlotViewByType(slotType)
        -- this functionn only want to get container-view not the container-slot-view
        -- local containerUI = slotView:GetSlotViewByType(slotType)
        -- return containerUI:GetContainerSlotView()
    end

    return nil
end




--endregion
-------------------------------

-------------------------------
--region 局内3c相关

-- 废弃接口
GuideLogic.Is3CFinished = function ()
    if true then return true end
    local bFinished = Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideInGame3CFinished)
    -- 新手引导结束也认为3c结束
    if not bFinished then
        bFinished = GuideLogic.IsNewPlayerGuideFinished()
    end
    -- 首局结束也认为3c结束
    if not bFinished then
        if Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule) > 0 then
            GuideLogic.Set3CFinishedState(true)
            bFinished = true
        end
    end
    return bFinished
end

-- 废弃接口
GuideLogic.Set3CFinishedState = function (state)
    if true then return end
    Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideInGame3CFinished, state)
    log("Set3CFinishedState", state)
end

GuideLogic.UpdateGuideInGameData = function (state)
    local UGuideUtils = import "GuideUtils"
    --local b3cFinished = GuideLogic.Is3CFinished()
    -- 设置3C状态
    --log("InitFinished3CGuideState", b3cFinished)
    --UGuideUtils.InitFinished3CGuideState(b3cFinished)
    -- 设置当前局内引导剩余数量
    UGuideUtils.ClientSetNotFinishGuideCount(GetWorld(), GuideLogic.GetNotFinishGuideCount())
end

-- 废弃接口
GuideLogic.Set3CFinished = function (state)
    if true then return end
    local b3cFinished = GuideLogic.Is3CFinished()
    log("Set3CFinished", b3cFinished)
    local UGuideUtils = import "GuideUtils"
    UGuideUtils.SetFinished3CGuideState(GetWorld(), b3cFinished)
end

-- 获取本局局内可能会执行的引导数量
-- TODO 需要补充动态计算逻辑
GuideLogic.GetNotFinishGuideCount = function ()
    return 1
end

--endregion
-------------------------------

--[[
inputMonitor:SetActionsPriorityGate(...)
"用SetActionsPriorityGate 接口设置一个PriorityGate。当一个Gate打开时，优先级比当前Gate低的输入，都会被屏蔽。
当多个Gate存在时，以优先级最大的Gate为准。"
]]
GuideLogic.SetGuideInputGate = function (name, bEnable)
    loginfo("SetGuideInputGate", name, bEnable)
    local guideField = Module.Guide.Field
    if bEnable then
        guideField.useInputGateList[name] = true
    else
        guideField.useInputGateList[name] = false
    end

    local inputMonitor = Facade.UIManager:GetInputMonitor()

    if guideField.useInputGateList["GuidePause"] then
        loginfo("real setGuideInputGate false, because GudiePause")
        inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, false)
        return
    end

    for _, bEnable in pairs(guideField.useInputGateList) do
        if bEnable then
            loginfo("real setGuideInputGate true", _)
            inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, true)
            return
        end
    end
    loginfo("real setGuideInputGate false")
    inputMonitor:SetActionsPriorityGate(EDisplayInputActionPriority.UI_Guide, false)
end

GuideLogic.SafeHouseNpcSpeak = function (key)
    local subtitleLevel = UGPSingletonUtils.GetLevelSubsystem(GetWorld(), AGPSubtitleLevelSubsystem)
    subtitleLevel:ServerRequestSubtitleSequence(key, 3, nil, {})
end

GuideLogic.IsMpMode = function ()
    return InGameController:Get():IsMPMode()
end

GuideLogic.IsSOLMode = function ()
    return InGameController:Get():IsSOLMode()
end

GuideLogic.IsGameAndSolMode = function (gameFlow)
    gameFlow = gameFlow or Facade.GameFlowManager:GetCurrentGameFlow()
    -- logTempError("IsGameAndSolMode", gameFlow == EGameFlowStageType.Game, GuideLogic.IsSOLMode(), gameFlow)
    if gameFlow == EGameFlowStageType.Game
        -- or gameFlow == EGameFlowStageType.LobbyToGame -- reconnect from modehall to sol game
        -- or gameFlow == EGameFlowStageType.SafeHouseToGame -- reconnect from safehouse to sol game
    then
        if GuideLogic.IsSOLMode() then
            return true
        end
    end
    return false
end

GuideLogic.DoSendGuideSpecLog = function(logId, bOnlyFirst)
    if not logId then
        err("DoSendGuideSpecLog id is nil")
    end

    if bOnlyFirst then
        local key = logId % 1000
        local tipsServer = Server.TipsRecordServer
        local records = tipsServer:GetMap(tipsServer.keys.GuideSpecLogSend)
        if records[tostring(key)] then
            log("DoSendGuideSpecLog log was send, only send once, key:", key)
            return
        else
            LogAnalysisTool.DoSendClientCommonEventLog(logId)
            records[tostring(key)] = 1
            tipsServer:SetMap(tipsServer.keys.GuideSpecLogSend, records)
        end
    else
        LogAnalysisTool.DoSendClientCommonEventLog(logId)
    end
end

GuideLogic.SetPopTipsInGameVisibility = function (key, bVisible)
    log("SetPopTipsInGameVisibility", key, bVisible)
    Module.Guide.Field:SetGuideUIState(UIName2ID.GuideHDPopTipsUI, bVisible, key)
end

GuideLogic.SetPopWindowInGameVisibility = function (key, bVisible)
    log("SetPopWindowInGameVisibility", key, bVisible)
    local uiId = IsHD() and UIName2ID.GuideHDPopWindowUI or UIName2ID.GuidePopWindowUI
    Module.Guide.Field:SetGuideUIState(uiId, bVisible, key)
end

GuideLogic.SetPopButtonInGameVisibility = function (key, bVisible)
    log("SetPopTipsInGameVisibility", key, bVisible)
    Module.Guide.Field:SetGuideUIState(UIName2ID.GuideHDPopButtonUI, bVisible, key)
end

GuideLogic.SetForceClickInGameVisibility = function (key, bVisible)
    log("SetClickInGameVisibility", key, bVisible)
    local forceClickID =  IsHD() and UIName2ID.GuideHDClickUI or UIName2ID.GuideClickUI
    Module.Guide.Field:SetGuideUIState(forceClickID, bVisible, key)
end

-- GuideLogic.

-- 停止非列表里的引导
GuideLogic.StopGuideNotInList = function (whiteList)
    log("StopNotForceGuide", GuideLogic.IsGuiding())
    local guideField = Module.Guide.Field
    if GuideLogic.IsGuiding() then
        for _, stageId in pairs(whiteList) do
            log("StopNotForceGuide whiteList have:", stageId)
        end
        local curGuideId = guideField:GetCurGuideId()
        local stageId = guideField:GetGuideCfg(curGuideId).GuideStageId
        if table.contains(whiteList, stageId) then
            log("StopNotForceGuide cur in whitelist", stageId)
        else
            log("StopNotForceGuide stop", stageId, curGuideId)
            Module.Guide:StopAllGuide()
        end
    end
end

-- 停止非进入新手关的引导
GuideLogic.StopGuideNotFirst = function ()
    local whiteList = {GuideConfig.EGuideStage.newPlayerGuideStage1}
    GuideLogic.StopGuideNotInList(whiteList)
end

GuideLogic.IsAuctionUnlockSell = function ()
    local bAuctionUnlock = Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionSell)
    log("GuideLogic.IsAuctionUnlockSell", bAuctionUnlock)
    return bAuctionUnlock
end

GuideLogic.IsAuctionUnlockBuy = function ()
    local bAuctionUnlock = Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleAuctionBuy)
    log("GuideLogic.IsAuctionUnlockBuy", bAuctionUnlock)
    return bAuctionUnlock
end

GuideLogic.IsInSubStage3dSafehouse = function ()
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    loginfo("IsInSubStage3dSafehouse cur substage", curSubStage)
    return curSubStage == ESubStage.SafeHouse3D
end


GuideLogic.IsInSubStageRange = function ()
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    loginfo("IsInSubStageRange cur substage", curSubStage)
    return curSubStage == ESubStage.Range
end

GuideLogic.IsTopbarEnable = function ()
    local ret = Module.CommonBar:GetCurrentTopTabIndex(1)
    loginfo("GuideLogic.IsTopbarEnable", ret)
    return ret > 0
end

GuideLogic.IsTopbarVisible = function ()
    ---@type LuaUIBaseView
    local ret = Module.CommonBar:GetTopBarUIInst(1)
    return ret and ret:IsVisible()
end

-- 是否打开过仓库界面
GuideLogic.IsWarehouseOpened = function ()
    return Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideWarehouseOpened)
end

-- 是否打开过部门界面
GuideLogic.IsDepartmentOpened = function ()
    return Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideDepartmentOpened)
end

-- 是否标记了保险箱
GuideLogic.IsMarkHighValueBox = function ()
    local UDFMHudHelper = import "DFMHudHelper"
    if UDFMHudHelper.HasMapSignHighValueBox(GetWorld()) then
        return true
    end

    return false
end

GuideLogic.TryAutoSetBulletSelectNum = function (slotType, quality, num)
    local uiIns = GuideLogic.getWidgetFuncAssemblyBulletView()
    if not uiIns then return false end


    local allData = Module.ArmedForce:GetAllQuickOperationDataInfo()
    local slot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
    if not slot then
		logerror("TryAutoSetBulletSelectNum() ==> slot is nil!", slotType)
		return false
	end

    local UAmmoDataManager = import "AmmoDataManager"
    local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"

    local ammoMgr = UAmmoDataManager.Get()
    local equipItem = slot:GetEquipItem()
    for id, data in pairs(allData) do
        if ammoMgr:IsMatchWeapon(equipItem.id, id) then
            --local item = data:GetItem()
            if ItemConfigTool.GetItemQuality(id) == quality and data:GetDepositoryNum() > 0 then
                local realNum = math.min(data:GetDepositoryNum(), num - data:GetOperationNum())
                realNum = math.max(0, realNum)
                uiIns:OnOperationItemAdd_DepositoryNum(slotType, id, realNum)
                return true
            end
        end
    end

    return false
end

GuideLogic.TryAutoSetMedicineSelectNum = function (slotType, id, num)
    local uiIns = GuideLogic.getWidgetFuncAssemblyBulletView()
    if not uiIns then return false end


    local allData = Module.ArmedForce:GetAllQuickOperationDataInfo()
    -- local slot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Preset)
    -- if not slot then
	-- 	logerror("TryAutoSetBulletSelectNum() ==> slot is nil!", slotType)
	-- 	return false
	-- end

    local data = allData[id]
    if not data then
        logerror("TryAutoSetMedicineSelectNum  data is nil, id:", id)
        return false 
    end
    if data:GetDepositoryNum() > 0 then
        local realNum = math.min(data:GetDepositoryNum(), num - data:GetOperationNum())
        realNum = math.max(0, realNum)
        uiIns:OnOperationItemAdd_DepositoryNum(slotType, id, realNum)
        return true
    else
        if uiIns.OnOperationItemAdd_DepositoryZeroNum then
            uiIns:OnOperationItemAdd_DepositoryZeroNum()
        end
    end

    return false
end

GuideLogic.CheckCanGetBullet = function (slotType, quality)
    local slot = Server.InventoryServer:GetSlot(slotType)
    if not slot then
		logerror("CheckCanGetBullet() ==> slot is nil!", slotType)
		return false
	end

    -- 是否装了枪
    local equipItem = slot:GetEquipItem()
    if not equipItem then
        logerror("not equip item", slotType)
        return false
    end

    -- 是否有能装2级子弹
    local UAmmoDataManager = import "AmmoDataManager"
    local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
    local ammoMgr = UAmmoDataManager.Get()
    local matchIds = {} -- 拍卖行全图鉴
    local matchQualityIds = {}
    local typeInfo = Module.ArmedForce.Config.SlotType2Type[ESlotType.BulletLeft] -- 用哪个子弹类型都会获得相同的拍卖行数据
    if typeInfo then
        matchIds = Server.AuctionServer:GetSaleIdListFromType(typeInfo.mainType, typeInfo.subType)
    end
    for _, id in ipairs(matchIds) do
        if ammoMgr:IsMatchWeapon(equipItem.id, id) and ItemConfigTool.GetItemQuality(id) == quality then
            table.insert(matchQualityIds, id)
        end
    end
    if #matchQualityIds == 0 then
        logerror("matchQualityIds num is 0! weapon id", equipItem.id)
        return false
    end

    -- 是否仓库有可装的2级子弹
    for _, id in pairs(matchQualityIds) do
        local items = Server.InventoryServer:GetItemsById(id)
        if #items > 0 then
            -- 身上是否有空位
            local slot = Server.InventoryServer:GetSlot(ESlotType.ChestHangingContainer)
            if slot and slot:TryFindLocationForItem(items[1]) then
                return true
            end
            slot = Server.InventoryServer:GetSlot(ESlotType.Pocket)
            if slot and slot:TryFindLocationForItem(items[1]) then
                return true
            end
            slot = Server.InventoryServer:GetSlot(ESlotType.BagSpaceContainer)
            if slot and slot:TryFindLocationForItem(items[1]) then
                return true
            end
            -- slot = Server.InventoryServer:GetSlot(ESlotType.BagContainer)
            -- if slot and slot:TryFindLocationForItem(items[0]) then
            --     return true
            -- end
            slot = Server.InventoryServer:GetSlot(ESlotType.SafeBoxContainer)
            if slot and slot:TryFindLocationForItem(items[1]) then
                return true
            end
            
            logerror("TryFindLocationForItem fail!", id)
            return false
        end
    end

    return false
end

GuideLogic.IsGuidingOrWaitGuide = function ()
    if not Server.GuideServer.FlagEnableGuide then return false end
    
    local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()

    if gameFlow == EGameFlowStageType.SafeHouse then
        -- sol模式下
        if GuideLogic.IsGuiding() then
            loginfo("IsGuidingOrWaitGuide, cur is sol guiding")
            return true
        end

        local curGuideStage = GuideLogic.GetCurNewPlayerGuideStage()
        if curGuideStage then
            loginfo("IsGuidingOrWaitGuide, cur new player guide not finish", curGuideStage)
            return true
        end
        
    elseif gameFlow == EGameFlowStageType.Lobby then
        -- mp模式下
        if IsHD() then
            -- pc目前都是弱引导，先不屏蔽拍脸图
        else
            if GuideLogic.IsGuiding() then
                loginfo("IsGuidingOrWaitGuide, cur is mp guiding")
                return true
            end
--[[
            local curMpCount = Server.GuideServer:GetMatchCount(MatchGameRule.TDMClassGameRule)
            if curMpCount < 1 then
                loginfo("IsGuidingOrWaitGuide, mp match count is less 1, cur count:", curMpCount)
                return true
            end
]]--
            for k,v in pairs(GuideConfig.MpNewPlayerGuideStageList or {}) do
                if GuideLogic.CheckMeetGuideStageStartCondition(v) then
                    loginfo("IsGuidingOrWaitGuide, mp guide not finish: ", k)
                    return true
                end
            end
        end
    end

    -- mode hall?


    return false
end

-- mp排位赛是否解锁
GuideLogic.IsMpRankUnlock = function()
    local moduleInfo = Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleScoreMP)
    if moduleInfo then
       return moduleInfo.bIsUnlocked
    end

    logwarning("GuideLogic.IsMpRankUnlock, moduleInfo is nil")
    return false
end

-- 是否撤离失败相关的引导
GuideLogic.IsSolFailGuide = function(guideStageId)
    if not guideStageId then return false end
    if guideStageId == GuideConfig.EGuideStage.solFailGiveNewEquip then
        return true
    end

    if guideStageId >= GuideConfig.EGuideStage.solFailGiveNewEquipBegin and guideStageId < GuideConfig.EGuideStage.solFailGiveNewEquipEnd then
        return true
    end

    return false
end


---@param slotType ESlotType
---@return EquipmentFeature|nil
function GuideLogic.GetEquipmentFeature (slotType)
    local slot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
    if slot then
        local equip = slot:GetEquipItem()
        if equip then
            ---@type EquipmentFeature
            return equip:GetFeature(EFeatureType.Equipment)
        end
    end
    return nil
end


---@param slotType ESlotType
---@return number|nil, boolean|nil
function GuideLogic.GetPlayerEquipSlotDurabilityPercent(slotType)
    local feature = GuideLogic.GetEquipmentFeature(slotType)
    if feature then
        log("GetPlayerEquipSlotDurabilityPercent", slotType, feature.ArmorLevel, feature.curDurability, feature.maxDurability,
            feature.maxDurabilityOriginal, feature.MIN_CAN_REPAIR_DURABILITY)

        local curdurability = feature.curDurability or 0
        local maxdurability = feature.maxDurability or 0
        local bCanRepair = maxdurability > feature.MIN_CAN_REPAIR_DURABILITY
        return curdurability / maxdurability, bCanRepair
    end
    warning("GetPlayerEquipSlotDurabilityPercent, feature is nil, slotType:", slotType)
    return nil, nil
end


-- 特勤处 tab 高亮
function GuideLogic.ToggleTopbarBlackSiteAnim(bOpen)
    if bOpen then
        Module.Guide:OpenGuideHDWeakClickUI(222)
    else
        Module.Guide:CloseGuideHDWeakClickUI(222)
    end
end

----------------------------------------
--region 版本判断

function GuideLogic.IsBuildExper()
    -- return BUILD_REGION_CN or BUILD_REGION_CN_EXPER
    return BUILD_REGION_CN_EXPER 
end

function GuideLogic.IsRegionCN()
    return REGION_CN 
end

function GuideLogic.IsRegionGA()
    return REGION_GA
end

function GuideLogic.IsRegionGlobal()
    return BUILD_REGION_GLOBAL
end
--endregion 版本判断
---------------------------------------


------------------------------------------
--region 体系引导 structured tutorial

---@param guideStageCfg FGuideStageClientOnlyConfig
---@param structStage EGuideStructTutorialStage|nil
---@return boolean
function GuideLogic.IsStructTutorial(guideStageCfg, structStage)
    if not guideStageCfg then return false end

    -- check modeType flag
    local bStructTutorial = ((1<< EGuideStageClientMode.StructTutorial )& guideStageCfg.ModeType) ~= 0
    if bStructTutorial then
        if structStage == nil then
            return true
        end

        local structuredTable = GuideConfig.TableGuideStructuredConfig
        local structTutorialId = Server.GuideServer.mapGuideStageId2StructTutorialId[guideStageCfg.GuideStageId]
        local structuredCfg = structuredTable[structTutorialId]

        loginfo("GuideLogic.IsStructTutorial, ", structStage, "structGuideType:", structuredCfg.GuideType)
        return structStage == structuredCfg.GuideType

        -- if structStage == GuideConfig.EGuideStructTutorialStage.InGameHud then
        --     return structuredCfg.GuideType == 1
        -- elseif structStage == GuideConfig.EGuideStructTutorialStage.SettlementView then
        --     return structuredCfg.GuideType == 2
        -- elseif structStage == GuideConfig.EGuideStructTutorialStage.AssemblyView then
        --     return structuredCfg.GuideType == 3
        -- elseif structStage == GuideConfig.EGuideStructTutorialStage.HallView then
        --     return structuredCfg.GuideType == 4
        -- else
        --     return false
        -- end
    end

    return false
end


function GuideLogic._TryInsertStructTutorial(readyStageList, structTutorialId, targetStructTutorialStage, extArgs)
    -- if not readyStageList then return false end

    local stageId = Server.GuideServer.mapStructTutorialId2GuideStageId[structTutorialId]
    local stageCfg = GuideConfig.TableGuideStageConfig[stageId]

    if not stageCfg then
        logerror("_TryInsertStructTutorial, stageCfg is nil, stageId:", stageId, "structTutorialId:", structTutorialId)
        return false
    end

    -- does match the conditions to show?
    if not GuideLogic.IsStructTutorial(stageCfg, targetStructTutorialStage) then
        loginfo("_TryInsertStructTutorial not a struct tutorial", structTutorialId)
        return false
    end

    -- business validation
    if targetStructTutorialStage == GuideConfig.EGuideStructTutorialStage.InGameHud then
        if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game then
            loginfo("_TryInsertStructTutorial current not in `game` flow", structTutorialId)
            return false
        end
    elseif targetStructTutorialStage == GuideConfig.EGuideStructTutorialStage.SettlementView then
        -- do another validation to check win/failed at settlement view

        ---@type pb_GspEndGamePlayerStatus
        local endGameInfo = extArgs.endGameInfo

        local bEscaped = endGameInfo.type == EGspPlayerResultType.kGspPlayerResultEscaped
        local bFailedGuide = table.contains(GuideConfig.EStructTutorialStageHallView_Failed, stageId)
        log("bWin:", bEscaped, "bFailedGuide:", bFailedGuide)

        if bFailedGuide then
            if bEscaped then
                log("not this condition to show struct tutorial")
                return false
            end
        end
    end

    if not GuideLogic.CheckMeetGuideStageStartCondition(stageId) then
        loginfo("_TryInsertStructTutorial not meet start condition", stageId)
        return false
    end

    log("add structTutorial stage", stageId, "structTutorialId:", structTutorialId)
    table.insert(readyStageList, stageCfg)
end

---comment
---@param readyStageList table
---@param targetStructTutorialStage EGuideStructTutorialStage
---@param extArgs table
function GuideLogic.HandleStructTutorialStart(readyStageList, targetStructTutorialStage, extArgs)
    if not readyStageList then return end
    assert(targetStructTutorialStage ~= nil, "the structTutorialStage is nil")

    local structTutorialIds = Server.GuideServer:GetTraceStructTutorialStageIds()
    log("cur structTutorialIds:", table.concat(structTutorialIds, ","))

    for _, structTutorialId in pairs(structTutorialIds) do
        --- TODO: handle pc/mobile
        -- local structTutorialIdStr =  tostring(structTutorialId)
        -- string.starts_with(structTutorialIdStr, "1")
        local bSucceed =  GuideLogic._TryInsertStructTutorial(readyStageList, structTutorialId, targetStructTutorialStage, extArgs) 
    end
end


function GuideLogic.HandleStructTutorialEnd(endGuideStageId, tryPlayNextStageId)
    local endStructTutorialId = Server.GuideServer.mapGuideStageId2StructTutorialId[endGuideStageId]
    if not endStructTutorialId then
        return
    end

    -- gather other struct guideId which in same `GuideType`(same trigger scene)
    local sameSceneStructGuideIds = {}
    local otherStructGuideIds = {}
    do
        local structGuideTable   = GuideConfig.TableGuideStructuredConfig
        local endStructGuideType = structGuideTable[endStructTutorialId].GuideType

        -- 局内 hud 不互斥
        if endStructGuideType ~= GuideConfig.EGuideStructTutorialStage.InGameHud then
            local curStructGuideIds = Server.GuideServer:GetTraceStructTutorialStageIds()
            for _, curStructGuideId in pairs(curStructGuideIds) do
                local tbl = structGuideTable[curStructGuideId]
                if tbl.GuideType == endStructGuideType then
                    table.insert(sameSceneStructGuideIds, curStructGuideId)
                end
            end
            -- to skips
            otherStructGuideIds = simpleclone(sameSceneStructGuideIds, false)
            table.removebyvalue(otherStructGuideIds, endStructTutorialId)
        end
    end

    Server.GuideServer:ReportStructGuideTrigger(endStructTutorialId, {
        onPreReport = function()
            -- remove this and other struct tutorial stages at same `EGuideStructTutorialStage`
            -- because report was a async action with networks issues
            -- we want those do not trigger again at current scene/guide type
            Server.GuideServer:RemoveStructTutorialIds(sameSceneStructGuideIds)
        end,
        onReportSuccess = function()
            Server.GuideServer:ReportStructGuideSkip(otherStructGuideIds)
        end,
        onReportFailed = function()
            -- we need to restore that was removed?
            Server.GuideServer:AddStructTutorialIds(sameSceneStructGuideIds)
        end
    })
end


function GuideLogic.GetActionKeyMapping(actionName)
    local DFHDKeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    local settingTableRow = DFHDKeySettingMgr:GetRowByActionOrAxisName(actionName, 0, true)
    if not settingTableRow then
        logerror("no such row in DFHDKeySettingMgr, actionName:", actionName)
        return nil
    end
    -- for more field, assign to table
    local fakeKeyMapping = {}
    fakeKeyMapping.Key = settingTableRow.KeyInfo.Key
    if not settingTableRow.IsAxis then
        fakeKeyMapping.bCtrl = settingTableRow.KeyInfo.NeedModifierKeys[1]
        fakeKeyMapping.bShift = settingTableRow.KeyInfo.NeedModifierKeys[2]
        fakeKeyMapping.bAlt = settingTableRow.KeyInfo.NeedModifierKeys[3]
        fakeKeyMapping.bCmd = settingTableRow.KeyInfo.NeedModifierKeys[4]
    end

    return fakeKeyMapping
end

--endregion
------------------------------------------



---@param evacuationReason EGspPlayerResultType
function GuideLogic.RecordSettlementVideoGuidePlayed(evacuationReason)
    log("GuideLogic.RecordSettlementVideoGuidePlayed", evacuationReason)
    local map = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideSettlementVideoPlayed)
    local strEvacuationReason = tostring(evacuationReason)
    if map[strEvacuationReason] == true then
        log("GuideLogic.RecordSettlementVideoGuidePlayed, already played", strEvacuationReason)
    end
    map[strEvacuationReason] = true
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideSettlementVideoPlayed, map)
end


function GuideLogic.TryPlaySettlementVideoGuide(playType, evacuationResultType)
    local evacuationReason =nil 
    -- get from settlement server
    if evacuationResultType == nil then
        if Server.SettlementServer.playerInfo ~= nil then
            evacuationReason = Server.SettlementServer:GetEscapeGameType()
            log("TryPlaySettlementVideoGuide", playType, evacuationReason)
        end
    else
        evacuationReason = evacuationResultType
    end

    if not evacuationReason then
        logerror("evacuationReason is nil")
        return false
    end

    targetVideo = GuideConfig.EPlayerEscapeType2VideoId[evacuationReason]
    if targetVideo == nil then
        logwarning("target settlement reason's video is nil", evacuationReason)
        return false
    end

    ------------------------------------------

    local videoLen = nil
    local bReported = false
    local startTime = nil

    local recordPlayed = function()
        GuideLogic.RecordSettlementVideoGuidePlayed(evacuationReason)
    end

    -- dexzhou 这里包含没有成功播视频, 就直接退出了的情况, 应该是手游流媒体
    local doReport = function(videoView)
        if bReported then
            log("doReport, already reported")
            return
        end

        bReported = true
        local bFullPlayed = not videoView.bWasSkip or false
        local gap = os.clock() - startTime
        if videoLen == nil or gap < videoLen or gap < 0 then
            warning("videoLen == nil or gap < videoLen or gap < 0", gap, videoLen)
        end
        Module.Guide:OnPlayEvacuateTeachingFinish(targetVideo, playType, gap, bFullPlayed)
    end

    local bPlaySuccess = Module.CommonWidget:ShowFullScreenVideoView(
        targetVideo,
        false,
        true,
        function (uiIns) ---@param uiIns CommonVideoFullScreenView
            log("TryPlaySettlementVideoGuide onMediaPlayEnd", targetVideo, bReported)
            doReport(uiIns)
            recordPlayed()
            Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.SettlementVideoEnd)
        end,
        nil,
        3,
        6,
        true,
        function(len)
            videoLen = len
        end,
        ---@param uiIns CommonVideoFullScreenView
        function(uiIns)
            loginfo("GuideLogic.TryPlaySettlementVideoGuide onMediaPlayBegin", targetVideo)
            startTime = os.clock()
            assert(not hasdestroy(uiIns), "TryPlaySettlementVideoGuide uiIns is destroyed, targetVideo:" .. tostring(targetVideo))

            --TODO: handle pause and resume event?
            -- uiIns._progressBar
            ---@param videoView CommonVideoFullScreenView
            local _onCloseOrRecycle = function(videoView)
                doReport(videoView)
                recordPlayed()
                Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.SettlementVideoEnd)
            end
            uiIns:AddCloseOrRecycleCallback(_onCloseOrRecycle)
        end
    )

    return bPlaySuccess
end

--#region 引导阶段每日播放次数记录

function GuideLogic.RecordStageDayplayInfo(guideStageId)
    local tipsKey = Server.TipsRecordServer.keys.GuideStageDayPlayed
    local recoreds = Server.TipsRecordServer:GetDailyMap(tipsKey)
    local strStageId = tostring(guideStageId)

    local old            = recoreds[strStageId]
    local new =  (old or 0)  +1
    recoreds[strStageId] =  new
    log("RecordStageDayplayInfo", strStageId, old,  new)

    Server.TipsRecordServer:SetMap(tipsKey, recoreds)
    return  new
end



-- TODO Movie into GuideLogic.CheckMeetGuideStageStartCondition
function GuideLogic.GetStageDayPlayCount(guideStageId)
    local mp = Server.TipsRecordServer:GetDailyMap(Server.TipsRecordServer.keys.GuideStageDayPlayed)
    local strStageId = tostring(guideStageId)
    log("GetStageDayPlayCount", strStageId, mp[strStageId])
    return mp[strStageId] or 0
end

--#endregion


--#region  局内技能数据流

--- 在玩家死亡后会被ds同步下来
function  GuideLogic.GetKilledSavedDataFlow()
    local controller =   InGameController:Get()
    if hasdestroy(controller) then
        err("GetSkillDataFlow in game controller is nil")
        return nil
    end
    local gpCharacter = controller:GetGPCharacter()
    if hasdestroy(gpCharacter) then
        err("GetSkillDataFlow in gpCharacter is nil")
        return nil
    end
    if hasdestroy( gpCharacter.Blackboard )then
        err("GetSkillDataFlow in gpCharacter.Blackboard is nil")
        return nil
    end
    if hasdestroy(gpCharacter.Blackboard.GPSkill) then
        err("GetSkillDataFlow in gpCharacter.Blackboard.GPSkill is nil")
        return nil
    end
    -- if hasdestroy(gpCharacter.Blackboard.GPSkill.SkillDataFlow) then
    --     err("GetSkillDataFlow in gpCharacter.Blackboard.GPSkill.SkillDataFlow is nil")
    --     return nil
    -- end
    -- return gpCharacter.Blackboard.GPSkill.SkillDataFlow:GetKilledSavedDataFlow()
    return gpCharacter.Blackboard.GPSkill.KilledSavedDataFlow
end

function GuideLogic.GetFirstAvaliableSkillAgainstEntry()
    local ret = nil
    local records = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideSkillDataFlowInfo)
    
    local output = ""
    for _, entry in ipairs(GuideConfig.AvaliableHeroAgainstSkill) do
        local count = records[entry.name]
        output = output .. entry.name .. ":" .. tostring(count) .. ", "
        if count and count >= 3 then --对局失败且来自同一技能达成触发条件3次
            ret = entry
            break
        end
    end
    log("GetFirstAvaliableSkillAgainstEntry", output)

    if not ret then
        if GuideConfig.bMockSkillDataFlow then
            -- random index
            ret = GuideConfig.AvaliableHeroAgainstSkill[math.random(1, #GuideConfig.AvaliableHeroAgainstSkill)]
        end
    end

    return ret
end
--#endregion




---@param actionName string
---@return string|unknown|nil, string|unknown|nil
function GuideLogic.ActionNam2RichIconText(actionName)
    local DFHDKeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    local settingTableRow = DFHDKeySettingMgr:GetRowByActionOrAxisName(actionName, 0, true) -- table: KeyMappingDefaultSettings gamepad-> GamepadInputMappings
    if not settingTableRow then
        err("ActionNam2RichIconText no such row in DFHDKeySettingMgr, actionName:", actionName)
        return nil
    end

    -- EGPInputType
    local fakeKeyMapping = {}
    fakeKeyMapping.Key = settingTableRow.KeyInfo.Key
    if not settingTableRow.IsAxis then
        -- TODO: with ctrl
        fakeKeyMapping.bCtrl = settingTableRow.KeyInfo.NeedModifierKeys[1]
        fakeKeyMapping.bShift = settingTableRow.KeyInfo.NeedModifierKeys[2]
        fakeKeyMapping.bAlt = settingTableRow.KeyInfo.NeedModifierKeys[3]
        fakeKeyMapping.bCmd = settingTableRow.KeyInfo.NeedModifierKeys[4]
    end

    local ret = string.format(" <dfmrichtext type=\"img\" id=\"KeyIcon_%s\"/> ", fakeKeyMapping.Key.KeyName)

    -- 手柄会有两个键位一起
    if IsHD() then
        if WidgetUtil.IsGamepad() then
            local GameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())
            local InputTrigger = GameplayInputManager:GetBestInputTriggerByName(actionName, true)
            if InputTrigger then
                fakeKeyMapping.Key = InputTrigger:GetKey1()
                fakeKeyMapping.Key2 = InputTrigger:GetKey2()
            end
            local KeyText = nil
            local Key2Text = nil

            if not WidgetUtil.IsXBoxGamepad() then
                if fakeKeyMapping.Key then
                    KeyText = string.format("<dfmrichtext type=\"img\" id=\"KeyIcon_%s_Sony\"/>", fakeKeyMapping.Key.KeyName)
                end
                if fakeKeyMapping.Key2 then
                    Key2Text = string.format("<dfmrichtext type=\"img\" id=\"KeyIcon_%s_Sony\"/>", fakeKeyMapping.Key2.KeyName)
                end
            else
                if fakeKeyMapping.Key then
                    KeyText = string.format("<dfmrichtext type=\"img\" id=\"KeyIcon_%s\"/>", fakeKeyMapping.Key.KeyName)
                end
                if fakeKeyMapping.Key2 then
                    Key2Text = string.format("<dfmrichtext type=\"img\" id=\"KeyIcon_%s\"/>", fakeKeyMapping.Key2.KeyName)
                end
            end

            if fakeKeyMapping.Key2 and fakeKeyMapping.Key2.KeyName ~= "None" and fakeKeyMapping.Key2.keyname ~= "" then
                ret =  string.format(" %s %s ", KeyText, Key2Text)
            else 
                ret =  string.format(" %s ", KeyText)
            end

        end
    end

    if fakeKeyMapping.Key.KeyName == "None" then
        ret = StringUtil.SequentialFormat(Module.Guide.Config.Loc.guideTipsKeyIsNone, ret)
    end

    return ret
end

function GuideLogic.IsWidgetActualInvisible(widget)
    -- Check if widget is valid
    if hasdestroy(widget) then
        return true
    end

    -- Start with the widget's own visibility
    local currentVisibility = widget.Visibility
    
    -- If already hidden or collapsed, return early
    if currentVisibility == ESlateVisibility.Hidden or 
       currentVisibility == ESlateVisibility.Collapsed then
        return true
    end
    
    -- Check parent chain
    local currentWidget = widget:GetParent()
    while  currentWidget do
        if hasdestroy(currentWidget) then
            return true
        end

        if currentWidget.Visibility == ESlateVisibility.Collapsed or 
           currentWidget.Visibility == ESlateVisibility.Hidden then
            return true
        end
        currentWidget = currentWidget:GetParent()
    end
    
    -- If we get here, the widget and all its parents are visible
    return false
end

function GuideLogic.OnSkillAgainstVideoStepEnd()
    local entry = Module.Guide.Field.lastPlayedSkillAgainstEntry
    Module.Guide.Field.lastPlayedSkillAgainstEntry = nil -- 防止重入
    if not entry then
        err("OnSkillAgainstVideoStepEnd, lastPlayedSkillAgainstEntry is nil")
        return
    end

    --- 记录是否已经播放过该视频 @TODO: move into startCondition framework
    GuideLogic.RecordStageDayplayInfo(GuideConfig.EGuideStage.solSkillAgainstPopWindow)

    --- 2025/6/11 dexzhou&rogerdong 重置该entry的记录为0
    local records = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideSkillDataFlowInfo)
    records[entry.name] = 0
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideSkillDataFlowInfo, records)
    log("reset GuideSkillDataFlowDayInfo to 0  for entry: ", entry.name)
end


function GuideLogic.GetGuidePopWindowShowData(guidePopWindowId)
    local guidePopWindowConfig = GuideConfig.TableGuidePopWindowConfig[guidePopWindowId]

    ---@class GuidePopWindowUIShowData
    local showData = {}
    showData.popWindowId = guidePopWindowId
    showData.showType = guidePopWindowConfig.ShowType
    showData.bHideHudUI = guidePopWindowConfig.bHideHudUI

    local cfgDescInfoList = GuideLogic.GetRealPopWindowDesc(guidePopWindowConfig)
    showData.title = cfgDescInfoList[1].Title
    showData.descInfo = {} ---@type GuidePopWindowShowData_DescInfo[]
    for _, cfgDescInfo in ipairs(cfgDescInfoList) do
        ---@class GuidePopWindowShowData_DescInfo
        local descInfo = {}
        descInfo.title = cfgDescInfo.Title
        descInfo.descText = cfgDescInfo.DescText
        descInfo.exContentType = cfgDescInfo.ExContentType
        descInfo.exContentParams = {}
        for _, param in ipairs(cfgDescInfo.ExContentParams) do
            table.insert(descInfo.exContentParams, param)
        end
        descInfo.Image = cfgDescInfo.Image
        table.insert(showData.descInfo, descInfo)
    end

    showData.callbackOnEnd = nil
    return showData
end


-- must be called in the settlement stage
function GuideLogic.ProcessCurrentMatchSkillDataFlow()
    local bRecordUpdate = false

    local lastDsRoomId = Module.Guide.Field.LastDsRoomIdOnDeathDamageInfoView:Get()
    local curDsRoomId =  Server.MatchServer:GetDsRoomId()

    log("_ProcessCurrentMatchSkillDataFlow lastDsRoomId = ", lastDsRoomId, " curDsRoomId = ", curDsRoomId)
    assert(curDsRoomId, "curDsRoomId should not be nil")

    if curDsRoomId == lastDsRoomId then
        warning("_OnDeathDamageInfoViewOnOpen curDsRoomId == lastDsRoomId, no need to process skill data flow")
        return bRecordUpdate
    end
    Module.Guide.Field.LastDsRoomIdOnDeathDamageInfoView:Set(curDsRoomId)
    local records  = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideSkillDataFlowInfo)

    local skillDataFlow = GuideLogic.GetKilledSavedDataFlow()
    if hasdestroy(skillDataFlow) then
        err("_OnDeathDamageInfoViewOnOpen skillDataFlow  is destroyed!!!")
        return bRecordUpdate
    end

    for _, cfg in ipairs(GuideConfig.AvaliableHeroAgainstSkill) do
        for _, match in ipairs(cfg.matches) do
            local val = skillDataFlow[match]
            log("_ProcesssCurrentMatchSkillDataFlow ", cfg.name, match, val, records[cfg.name])
            if val and val > 0 then 
                records[cfg.name] = (records[cfg.name] or 0) + 1 -- 每局最多累计计算一次
                bRecordUpdate = true
            end
        end
    end

    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.GuideSkillDataFlowInfo, records)
    return bRecordUpdate

end


return GuideLogic
