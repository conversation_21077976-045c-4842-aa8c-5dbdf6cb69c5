----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonSlotView = require "DFM.Business.Module.CommonWidgetModule.UI.CommonSlotView"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemSpaceAlgorithm = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemSpaceAlgorithm"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local DragItemPreview = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.DragItemPreview"
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local CommonItemHighlight = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.CommonItemHighlight"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local CommonDragDropLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonDragDropLogic"
local Queue = require("DFM.YxFramework.Core.Library.deque")

local UKismetMathLibrary = import "KismetMathLibrary"
local UGPInputHelper = import "GPInputHelper"
local UDFImage = import "DFImage"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local EItemInfoUpdatedReason = import "EItemInfoUpdatedReason"
local ULuaExtension = import "LuaExtension"
local UInventoryUtil = import "InventoryUtil"

--BEGIN MODIFICATION @ VIRTUOS : 依赖空白可导航控件
local IVCommonBlankItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.HD.IVCommonBlankItemTemplate"
local CommonIVInputLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonIVInputLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- local UGPUINavigationManager = import("GPUINavigationManager")
--END MODIFICATION

---@class InvSlotView : CommonSlotView
local InvSlotView = ui("InvSlotView", CommonSlotView)

InvSlotView.EHighlightMode = {
    None = 0,
    AvailableSlot = 1,
    DefaultTargetSlot = 2,
    InvalidSlot = 3
}

InvSlotView.bPreCreateUIsInEditor = false
InvSlotView.bShouldCheckShouldClearItemViews = false
InvSlotView.waitingForScrollSeconds = 0.1

local function log(...)
    loginfo("[InvSlotView]", ...)
end

function InvSlotView:Ctor()
    self:SetCppValue("bHandleClick", true)

    ---@type ItemSlot
    self.itemSlot = Server.InventoryServer:GetSlot(ESlotType.None)
    self._subSlotId = ItemConfig.DEFAULT_SLOT_SPACE_INDEX

    self._highlightMode = InvSlotView.EHighlightMode.None
    self._filterMode = eDepositSortClass.none

    self._bPlayGuideHighlightForItemMove = false
    self._bHasPrecreateDone = false
    self.bConditionalHide = false
    self.slotSpaceUpdateCount = -1

    ---@type CommonItemHighlight[]
    self._wtHighlights = {}
    --self:_CreateHighlightComponent()    -- Create default highlight component (only one)
    --self:_HideHighlights()

    self._DoubleHighLightItemViews = setmetatable({}, weakmeta)
    self._hNavigationChangedFocus = nil

    --BEGIN MODIFICATION @ VIRTUOS : 缓存Blank Item
    if IsHD() then
        self._allBlankItem = {}
        self._allBlankItemInsID = {}
        self._firstRowBlankItem = {}
        self._firstRowBlankItemInsID = {}
        -- 四向导航空格
        -- she3更新，中间空心的地方也要加个导航格
        self._LastNavBlankItem = {}
        self._LastNavBlankItemInsID = {}
        self._NavBlankItem = {}
        self._NavBlankItemInsID = {}
        -- 记录Slot是否开启Mask
        self.bShownMask = false
    end
    --END MODIFICATION

    self:AddLuaEvent(Server.LootingServer.Events.evtLootObjHidePreviousInventory, self._OnHidePreviousInventory, self)
end

function InvSlotView:OnShow()
    if self.itemSlot then
        loginfo("InvSlotView:OnShow", self.itemSlot:GetSlotGroup(), self.itemSlot.SlotType)
    else
        loginfo("InvSlotView:OnShow", self)
    end
    CommonSlotView.OnShow(self)

    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self.OnItemMove, self)
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self.OnItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtSlotSpaceChange, self.OnRefreshGrid, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtInventoryFetchFinished, self.OnInventoryFetchFinished, self)
    self:AddLuaEvent(DragItemPreview.EPostDragItemSpin, self.OnPostDragItemSpin, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtUpdateItemUnitSize, self._OnUnitSizeUpdate, self)
    -- self:AddLuaEvent(Module.Looting.Config.Events.evtOnShowAutoSnapHighlight, self._OnShowAutoSnapHighlight, self)
    -- self:AddLuaEvent(Module.CommonWidget.Config.Events.evtItemOperaModeChanged, self._OnItemOperaModeChanged, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtLocalMoveResult, self._OnLocalMoveResult, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtChangeSafeBoxState, self._OnChangeSafeBoxState, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility, self._OnUpdateAllBlankItemVisibility, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility, self._OnUpdateFirstRowBlankItemVisibility, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtRemoveYModeBlankItemVisibility, self.RemoveNavBlankItemOnDragEnd, self)
    
    self:AddLuaEvent(Server.LootingServer.Events.evtItemMoveTimeOutRefreshUI, self._OnItemMoveTimeOutRefreshUI, self)

    -- self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtGlobalItemDragCancelled, self._OnGlobalItemDragCancelled, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtIVMouseEnter, self._OnIVMouseEnter, self)
    self:AddLuaEvent(CommonWidgetConfig.Events.evtIVMouseLeave, self._OnIVMouseLeave, self)
    
    
    -- self:AddLuaEvent(CommonWidgetConfig.Events.evtItemViewAdded, self._OnItemViewAdded, self)


    Module.CommonWidget:RegisterCommonDragDrop(self)

    if self._itemUnitLength ~= ItemConfig.DefaultItemViewSize then
        -- 如果缓存的格子大小与当前设置的格子大小不一致，那么重刷布局
        self:_OnUnitSizeUpdate(ItemConfig.DefaultItemViewSize)
    end
end

function InvSlotView:OnHide()
    if self.itemSlot then
        loginfo("InvSlotView:OnHide", self.itemSlot:GetSlotGroup(), self.itemSlot.SlotType)
    else
        loginfo("InvSlotView:OnHide", self)
    end
    CommonSlotView.OnHide(self)

    self:_CheckShouldClearItemViews()

    self:RemoveAllLuaEvent()

    self:ResetConditionHide()

    Module.CommonWidget:UnregisterCommonDragDrop(self)

    self:HideDragHint()
end

function InvSlotView:OnClose()
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_FreeBlankItem()
        self:_FreeFirstRowBlankItem()
    end
    --END MODIFICATION

    CommonSlotView.OnClose(self)
    Facade.UIManager:ClearAllSubUI(self)
end

function InvSlotView:_OnShowAutoSnapHighlight(itemSlot)
    if itemSlot == nil or self.itemSlot ~= itemSlot then
        self:SetHighlightMode(InvSlotView.EHighlightMode.None)
    else
        self:SetHighlightMode(InvSlotView.EHighlightMode.AvailableSlot)
    end
end

--- 初始化普通的槽（单一槽）
function InvSlotView:InitServerSlot(slotType, groupId)
    if groupId == ESlotGroup.Player then
        if Module.Looting:IsCreatingUIForCollectionRoom() then
            groupId = ESlotGroup.CollectionRoom
        end
    end
    self._bUseCustomData = false
    self.itemSlot = Server.InventoryServer:GetSlot(slotType, groupId)
    self.slotSpaceUpdateCount = -1
    if slotType == ESlotType.ArchiveContainer then
        local curLength, _ = self:_GetViewSize()
        self:SetExtraPadding(CommonSlotView.ARCHIVE_MAX_LENGTH - curLength, 0)
    end
end

function InvSlotView:PrecreateItemViews(precreateNum)
    -- Don't precreate in editor maybe
    if IsInEditor() and not InvSlotView.bPreCreateUIsInEditor then return end
    if self.bUseGlobalPool then return end

    if self._bHasPrecreateDone then return end  -- ItemView的预创建只做一次
    self._bHasPrecreateDone = true

    log("PrecreateItemViews", precreateNum)

    if precreateNum then
        self._innerItemViewPool:Preload(precreateNum)
    end
end

--- 设置槽的高亮
---@param bIsSnapHint boolean 是吸附高亮
function InvSlotView:SetHighlightMode(highlightMode, bIsSnapHint)
    self._highlightMode = highlightMode

    local fullLength, fullHeight = self.itemSlot.Length, self.itemSlot.Width
    if highlightMode == InvSlotView.EHighlightMode.None then
        self:ShowDragHint(false)
    elseif highlightMode == InvSlotView.EHighlightMode.AvailableSlot
        or highlightMode == InvSlotView.EHighlightMode.DefaultTargetSlot then
        self:ShowDragHint(true, true, 0, 0, -1, -1, nil, nil, bIsSnapHint)
    elseif highlightMode == InvSlotView.EHighlightMode.InvalidSlot then
        self:ShowDragHint(true, false, 0, 0, -1, -1, nil, nil, bIsSnapHint)
    end
end

function InvSlotView:PlayWarningAnim()
    self:SetHighlightMode(InvSlotView.EHighlightMode.InvalidSlot)
    self._wtHighlight:PlayWarningAnim()
end

---@param dragDropInfo ItemDragDropInfo
---@param bIsSnapHint boolean 是吸附高亮
function InvSlotView:RefreshDragHint(dragDropInfo, bIsDragEnter, bIsSnapHint)
    local item = dragDropInfo.item
    local allAvailableSlots = dragDropInfo.allAvailableSlots
    local bValid = table.contains(allAvailableSlots, self.itemSlot)
    if self.itemSlot:IsKeyContainerSlot() then
        -- 如果是卡包，需要额外检查View的SubSlotID是否匹配对应的钥匙卡
        bValid = bValid and ItemOperaTool.CheckKeyMatchIndex(item, self:GetSubSlotId())
    end

    local bShouldCheckEquippedNotEmptyContainer = ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, self.itemSlot)
    local bShowSnapHint = bIsSnapHint or (not IsHD() and bShouldCheckEquippedNotEmptyContainer and self.itemSlot == dragDropInfo.defaultTargetSlot)
    if bValid then
        if bIsSnapHint or bShouldCheckEquippedNotEmptyContainer then
            self:SetHighlightMode(InvSlotView.EHighlightMode.AvailableSlot, bShowSnapHint)
        end
        -- 针对装备区自动吸附
        if self.itemSlot:IsContainerSlot() then
            return self.itemSlot
        end
    else
        if bShowSnapHint then
            self:SetHighlightMode(InvSlotView.EHighlightMode.InvalidSlot, true)
        elseif bShouldCheckEquippedNotEmptyContainer then
            self:SetHighlightMode(InvSlotView.EHighlightMode.InvalidSlot)
        else
            self:SetHighlightMode(InvSlotView.EHighlightMode.None)
        end
    end
    if bIsDragEnter and Module.CommonWidget:IsUseCustomDragEnterTrigger() and not bShouldCheckEquippedNotEmptyContainer then
        self:HideDragHint()
    end
    -- 如果VIP扩容箱槽位所在的容器槽位道具过期或者未激活，则也显示一片红色
    if ItemOperaTool.CheckRunWarehouseLogic() and self.itemSlot and self.itemSlot:CheckVipContainerIsExpired() then
        self:SetHighlightMode(InvSlotView.EHighlightMode.InvalidSlot)
        return
    end
end

function InvSlotView:HideDragHint()
    -- 重置DragHint
    self:ShowDragHint(false)
end

function InvSlotView:HideEquipPanelNabHL()
    -- 隐藏装备区侧边页签高亮
    if self.itemSlot:IsContainerSlot() then
        Module.Inventory.Config.Events.evtNavTabHightLight:Invoke(false, self.itemSlot)
    end
end

function InvSlotView:CDDL_GetSlot()
    return self.itemSlot
end

---@param item ItemBase
function InvSlotView:ConditionalUpdateWhenItemDrag(item, bDragStart)
    CommonSlotView.ConditionalUpdateWhenItemDrag(self, item, bDragStart)
    
    local inSlot = item.InSlot
    if inSlot and self.itemSlot:GetSlotGroup() == inSlot:GetSlotGroup() then
        if self.itemSlot:IsContainerSlot() and self.itemSlot:GetContainerType() == inSlot.SlotType then
            if bDragStart then
                -- self:Collapsed()
                self.bConditionalHide = true
                self:PlayWidgetAnim(self.Anima_ItemSlotView_Fadeout)
            else
                -- self:Visible()
                if self.itemSlot then
                    if self.bConditionalHide then
                        log("ConditionalUpdateWhenItemDrag succeed to show grids", self.itemSlot:GetSlotGroup(), self.itemSlot.SlotType)
                    else
                        log("ConditionalUpdateWhenItemDrag fail to show grids", self.itemSlot:GetSlotGroup(), self.itemSlot.SlotType, self:GetRenderOpacity())
                    end
                end
                self:ResetConditionHide()
            end
        end
    end
end

function InvSlotView:ResetConditionHide()
    if self.bConditionalHide then
        self.bConditionalHide = false
        self:StopWidgetAnim(self.Anima_ItemSlotView_Fadeout)
        self:SetRenderOpacity(1)
    end
end

function InvSlotView:_OnUnitSizeUpdate(newSize)
    if self.itemSlot then
        loginfo("InvSlotView:UpdateItemUnitSize", self.itemSlot:GetSlotGroup(), self.itemSlot.SlotType, newSize)
    end
    self._itemUnitLength = newSize
    self:SetSlotSize()
    self:_UpdateBgTexData(true)
end

---@param moveCmd ItemMoveCmd
function InvSlotView:_OnLocalMoveResult(moveCmd)
    if moveCmd:CheckLocalMove() or moveCmd.bPrebehave then
        log("_OnLocalMoveResult", moveCmd.item.name)

        ---@type ItemBase[]
        local allMoveItems = {}

        table.insert(allMoveItems, moveCmd.item)
        self:RemoveItem(moveCmd.item)
        for _, item in ipairs(moveCmd.targetItems) do
            table.insert(allMoveItems, item)
            self:RemoveItem(item)
        end

        for _, item in ipairs(allMoveItems) do
            local slot = item.InSlot
            if slot == self.itemSlot then
                local location = slot:GetItemLocation(item)
                local itemView = self:AddItem(item, location, location.bRotated)
                if moveCmd.bPrebehave then
                    self:PlayHighlightForItem(itemView)
                end
            end
        end

        --BEGIN MODIFICATION @ VIRTUOS : 刷新空白对象的显示情况
        if IsHD() then
            self:_RefreshBlankItem()
        end
        --END MODIFICATION
    end
end

function InvSlotView:_OnChangeSafeBoxState(bChecked)
    if self:IsInRecycleMode() and ItemOperaTool.CheckRunWarehouseLogic() then
        if self.itemSlot and not self.itemSlot:IsDepositorySlot() then
            self:TryGetScrollBoxHeight(true)
        end
    end
end

function InvSlotView:_OnItemMoveTimeOutRefreshUI(itemMoveCmd)
    self:ConditionalUpdateWhenItemDrag(itemMoveCmd.item, false)
end

-- function InvSlotView:_OnGlobalItemDragStart(dragDropInfo)
--     local slot = dragDropInfo.item.InSlot
    
--     if self.itemSlot.SlotType > ESlotType.DepositoryStart and self.itemSlot.SlotType < ESlotType.DepositoryEnd  and slot.SlotType > ESlotType.DepositoryStart and slot.SlotType < ESlotType.DepositoryEnd then
--         self:_OnUpdateFirstRowBlankItemVisibility(true)
--     end
-- end

function InvSlotView:_OnGlobalItemDragCancelled()
    Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(false)
    self:RemoveNavBlankItemOnDragEnd()
end


local cacheLastLogicX, cacheLastLogicY, cacheLastValid, bGeometryValid = -1, -1, false, false
local lastTouchPos
local cacheLastIndex = nil
function InvSlotView:_ResetDragDropParams()
    cacheLastLogicX, cacheLastLogicY, cacheLastValid, bGeometryValid = -1, -1, false, false
    lastTouchPos = nil
    cacheLastIndex = nil
    self._bItemRotated = false
    self._bItemShowRotated = false
    self._curDragItemView = nil
end

function InvSlotView:ShouldShowGridDragHint()
    local length, height = self:_GetSlotSize()
    local bShouldShow = not self:IsInAutoLootMode()

    return bShouldShow
end

function InvSlotView:IsInAutoLootMode()
    if ItemOperaTool.CheckRunWarehouseLogic() then
        return Module.Inventory:GetAutoSort()
    else
        return Module.Looting:GetAutoLoot()
    end
end

function InvSlotView:OnDragEnter(inGeometry, inDragDropEvent, operation)
    self:_ResetDragDropParams()

    ---@type DragItemPreview
    local dragItemView = operation.DefaultDragVisual
    if not dragItemView then
        return
    end

        -- 如果不���仓库页，则要激活仓库页面第一列的导航
    if self.itemSlot.SlotType > ESlotType.DepositoryStart and self.itemSlot.SlotType < ESlotType.DepositoryEnd then
        Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    else
        Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(true)
    end

    if not IsHD() then
        Module.CommonWidget:SetUseCustomDragEnterTrigger(true)
    end

    local item = dragItemView.item
    self._bItemRotated = dragItemView.bRotated

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        if item and item:GetFeature(EFeatureType.Key) and self.itemSlot.SlotType == ESlotType.KeyChainContainer then
            local contents = {{id = UIName2ID.Assembled_CommonMessageTips_V1,
                               data = {textContent = Module.Looting.Config.Loc.KeyChainNotAllowedToPutInTip, styleRowId = "C001"}}}
            dragItemView:ShowTips(contents)
        end
    end
end

function InvSlotView:OnDragLeave(inDragDropEvent, operation)
    self:_ResetDragDropParams()
    self:RemoveNavBlankItemOnDragEnd()
    if Module.CommonWidget:IsUseCustomDragEnterTrigger() then
        if Module.CommonWidget:IsCustomDragEnterTrigger() then
            Module.CommonWidget:SetCustomDragEnterTrigger(false)
        end
    else
        self:HideDragHint()
    end

    ---@type DragItemPreview
    local dragItemView = operation.DefaultDragVisual
    if not dragItemView then
        return
    end

    local item = dragItemView.item
    if item then
        dragItemView:SetRotation(item:IsRotated())
    end

    dragItemView:HideTips()
end

-- do some cache

---@type ItemLocation
local tarLocation = ItemLocation:NewIns()
---@type ItemMoveCmd
local moveCmd = ItemMoveCmd:NewIns()

local TryEquipItem = function(bDoHint, slotView, touchLogicX, touchLogicY, index, item)

    -- 先检查是否可以快速安装
    local curItem = slotView.itemSlot:GetItemAtPos(touchLogicX, touchLogicY, index)
    local weaponFeature
    if curItem then
        weaponFeature = curItem:GetFeature(EFeatureType.Weapon)
    end
    local adapterFeature = item:GetFeature(EFeatureType.Adapter)
    if curItem and curItem ~= item and (adapterFeature and adapterFeature:IsAdapter()) and
            (weaponFeature and weaponFeature:IsWeapon())
    then
        -- 进入快拆的判断，不走槽位高亮的通用逻辑
        -- local actualLength, actualHeight = item:GetSizeInSlot(slotView.itemSlot.SlotType)
        -- if slotView._bItemShowRotated then
        --     actualLength, actualHeight = actualHeight, actualLength
        -- end
        local loc = slotView.itemSlot:GetItemLocation(curItem)
        local x, y = loc.X, loc.Y
        local actualLength, actualHeight = loc:GetActualSize()
        if item:GetRootGid() == curItem.gid then
            -- 配件就是这把枪上的
            if bDoHint then
                slotView:HideDragHint()
            end
            return true, false
        elseif Module.FastEquip:CheckFastEquip(item, curItem) then
            -- -- ItemView上本身有个高亮，这里就不再高亮了
            -- slotView:HideDragHint()
            --- 重叠时需要在ItemView高亮的基础上叠加反馈高亮，因而把高亮恢复回来
            if bDoHint then
                slotView:ShowDragHint(true, true, x, y, actualLength, actualHeight, loc.SubIndex, true)
            end
            return true, true
        else
            if bDoHint then
                slotView:ShowDragHint(true, false, x, y, actualLength, actualHeight, loc.SubIndex, true)
            end
            return true, false
        end
    end

    -- 再检查子弹装填
    local loadAmmoResult = curItem and ItemOperaTool.CheckLoadingAmmoAvailable(item, curItem) or ELoadAmmoResult.NoLoadAmmoLogic
    if loadAmmoResult ~= ELoadAmmoResult.NoLoadAmmoLogic then
        local loc = slotView.itemSlot:GetItemLocation(curItem)
        local x, y = loc.X, loc.Y
        local actualLength, actualHeight = loc:GetActualSize()
        if loadAmmoResult == ELoadAmmoResult.Success then
            -- -- ItemView上本身有个高亮，这里就不再高亮了
            -- slotView:HideDragHint()
            --- 重叠时需要在ItemView高亮的基础上叠加反馈高亮，因而把高亮恢复回来
            if bDoHint then
                slotView:ShowDragHint(true, true, x, y, actualLength, actualHeight, loc.SubIndex, true)
            end
            return true, false
        else
            if bDoHint then
                slotView:ShowDragHint(true, false, x, y, actualLength, actualHeight, loc.SubIndex, true)
            end
            return true, false
        end
    end
    return false, false
end

---@param item ItemBase
local TryPlaceItem = function(slotView, touchLogicX, touchLogicY, index, item, bRotated, bShowTip, bAllowReplace)
    if not item then return false end
    -- MS23修改：不支持任意位置（吸附）
    if touchLogicX < 0 or touchLogicY < 0 then return false end

    local bApplyEquip, bIsValid = TryEquipItem(false, slotView, touchLogicX, touchLogicY, index, item)
    if bApplyEquip and not bIsValid then
        return false
    end

    ---@type ItemSlot
    local slot = slotView.itemSlot
    index = slotView:GetSubSlotId() or index

    local actualLength, actualHeight = item:GetSizeInSlot(slot.SlotType)
    if bRotated then
        actualLength, actualHeight = actualHeight, actualLength
    end

    -- 枪上地配件没有Source Location，需要特殊处理一下
    local adapterFeature = item:GetFeature(EFeatureType.Adapter)
    if adapterFeature and adapterFeature:IsAdapterInGun() then
        tarLocation:Init(slot, touchLogicX, touchLogicY, actualLength, actualHeight, index)
        local ignoreSelfList = {}
        ignoreSelfList[item.gid] = true
        return tarLocation:CheckLocationValidInSlot() and not tarLocation:CheckLocationUsed(ignoreSelfList) and ItemOperaTool.VerifyItemForTargetSlot(item, slot, bShowTip)
    elseif item.InSlot then
        ---@type ItemLocation
        local srcLocation = item.InSlot:GetItemLocation(item)
        tarLocation:Init(slot, touchLogicX, touchLogicY, actualLength, actualHeight, index)
        moveCmd:InitCmd(item, srcLocation, tarLocation, item.num, {bAllowReplace = bAllowReplace})
        return moveCmd:CheckIsValid(bShowTip)
    elseif item:IsCollectionCabinetItem() then
        -- 交换逻辑走这
        local canDropIn = false
        tarLocation:Init(slot, touchLogicX, touchLogicY, actualLength, actualHeight, index)
        if not tarLocation:CheckLocationValidInSlot() then
            canDropIn = false
        end

        local bUsed, overlapItems = tarLocation:CheckLocationUsed()
        if bUsed then
            if #overlapItems == 1 then
                local curDragingItem = Module.CommonWidget:GetCurDragingItem()
                -- 进行交换
                if item.length <= overlapItems[1].length and item.width <= overlapItems[1].width then
                    -- Server.CollectionRoomServer:ExchangeShowCabinet(EShowCabinetType.DIY, item.cabinetId, item.gridId, overlapItems[1], item)
                    canDropIn = true
                end
            elseif #overlapItems > 1 then
                canDropIn = false
            end
        else
            canDropIn = true
        end
        return canDropIn
    end

    return false
end

function InvSlotView:OnDragOver(inGeometry, inDragDropEvent, inOperation)
    ---@type BaseItemView
    local dragItemView = inOperation.DefaultDragVisual
    if not dragItemView or not dragItemView.item then
        return
    end

    dragItemView:SetRotation(self._bItemShowRotated)
    self._curDragItemView = dragItemView
    ---@type ItemBase
    local item = dragItemView.item

    --- 被拖拽物如果是已装备且有内容的容器，那么OnDragEnter的时候网格显示一片绿色或者红色，OnDragOver不处理
    if not Module.CommonWidget:IsUseCustomDragEnterTrigger() then
        if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, self.itemSlot) then
            return
        end
    end

    self:_InternalOnDragOver(inGeometry, inDragDropEvent, item)
end

function InvSlotView:_InternalOnDragOver(inGeometry, inDragDropEvent, item)
    -- local touchLogicX, touchLogicY = self:_GetLogicPosition(inGeometry, inDragDropEvent, true)
    -- local touchLogicX, touchLogicY = self:_GetLogicPositionV2(inGeometry, inDragDropEvent, item, self._bItemShowRotated)

    -- 每移动超过半格，需要重新计算一次有效性

    if self:_CheckLogicPosHalfUpdated(inGeometry, inDragDropEvent) then
        -- 按原始的旋转算位置
        log("yanmingjing: _CheckLogicPosHalfUpdated--CallBack")
        self._bItemShowRotated = self._bItemRotated
        local touchLogicX, touchLogicY, index, bInSlot = self:_GetLogicPositionV2(inGeometry, inDragDropEvent, item, self._bItemShowRotated)
        local bRotated = self._bItemShowRotated
        cacheLastLogicX, cacheLastLogicY, cacheLastIndex = touchLogicX, touchLogicY, index
        log("_InternalOnDragOver", self.itemSlot.SlotType, self.itemSlot:GetSlotGroup(), touchLogicX, touchLogicY)

        -- //TODO 每次计算出逻辑新位置的时候，也要更新一下导航格
        if IsHD() then
            self:AddNavBlankItemOnDragging(touchLogicX, touchLogicY, index, bInSlot)
        end

        if Module.CommonWidget:IsUseCustomDragEnterTrigger() then
            if not bInSlot then
                if Module.CommonWidget:IsCustomDragEnterTrigger() then
                    self:HideDragHint()
                    Module.CommonWidget:SetCustomDragEnterTrigger(false, self)
                end
                return
            else
                if not Module.CommonWidget:IsCustomDragEnterTrigger() and not self.bConditionalHide then
                    Module.CommonWidget:SetCustomDragEnterTrigger(true, self)
                    self:HideEquipPanelNabHL()
                end
            end

            --- 被拖拽物如果是已装备且有内容的容器，那么OnDragEnter的时候网格显示一片绿色或者红色，OnDragOver不处理
            if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, self.itemSlot) then
                return
            end
        end

        -- 在过期扩容箱中移动显示一片红
        if ItemOperaTool.CheckRunWarehouseLogic() and self.itemSlot and self.itemSlot:CheckVipContainerIsExpired() then
            self:SetHighlightMode(InvSlotView.EHighlightMode.InvalidSlot)
            return
        end
        -- if self.itemSlot and self.itemSlot.SlotType == ESlotType.VipDepositContainer then
        --     local containerSlotType = self.itemSlot.SlotType
        --     local extSlot = Server.InventoryServer:GetSlot(containerSlotType * 1000 + 1)
        --     local extItem = extSlot:GetEquipItem()
        --     if extItem and ItemBaseTool.GetItemExpiredState(extItem) then

        --     end
        -- end

        -- 不可快速安装的，再检查是否可以放置
        -- Check Type Valid First

        local bKeyValid = true
        local keyTipText = ""
        if self.itemSlot:IsKeyContainerSlot() and not ItemOperaTool.CheckKeyMatchIndex(item, self:GetSubSlotId()) then
            bKeyValid = false
            keyTipText = Module.Inventory.Config.Loc.KeyIndexNotMatch
        end

        if not self:IsInAutoLootMode() then
            local bIsMoveInNearby = item.InSlot and item.InSlot.SlotType == ESlotType.NearbyPickups and self.itemSlot.SlotType == ESlotType.NearbyPickups
            bRotated = self._bItemShowRotated
            -- 尝试直接放入（不允许替换）
            cacheLastValid = TryPlaceItem(self, touchLogicX, touchLogicY, index, item, bRotated, false, false)
            if not cacheLastValid then
                -- 尝试旋转（不允许替换）
                bRotated = not self._bItemShowRotated
                touchLogicX, touchLogicY = self:_GetLogicPositionV2(inGeometry, inDragDropEvent, item, bRotated)
                cacheLastValid = TryPlaceItem(self, touchLogicX, touchLogicY, index, item, bRotated, false, false)
            end
            if not cacheLastValid and not bIsMoveInNearby then
                -- 尝试直接放入（允许替换）
                bRotated = self._bItemShowRotated
                touchLogicX, touchLogicY = self:_GetLogicPositionV2(inGeometry, inDragDropEvent, item, bRotated)
                cacheLastValid = TryPlaceItem(self, touchLogicX, touchLogicY, index, item, bRotated, false, true)
            end
            if not cacheLastValid and not bIsMoveInNearby then
                -- 尝试旋转（允许替换）
                bRotated = not self._bItemShowRotated
                touchLogicX, touchLogicY = self:_GetLogicPositionV2(inGeometry, inDragDropEvent, item, bRotated)
                cacheLastValid = TryPlaceItem(self, touchLogicX, touchLogicY, index, item, bRotated, false, true)
            end
            -- if not cacheLastValid then
            --     -- 尝试任意位置放置
            --     bRotated = self._bItemShowRotated
            --     touchLogicX, touchLogicY = -1, -1
            --     cacheLastValid = TryPlaceItem(self, touchLogicX, touchLogicY, index, item)
            -- end
            if cacheLastValid then
                self._bItemShowRotated = bRotated
                cacheLastLogicX = touchLogicX
                cacheLastLogicY = touchLogicY
            else
                -- cacheLastLogicX = touchLogicX
                -- cacheLastLogicY = touchLogicY
            end

            log("_InternalOnDragOver", self.itemSlot.SlotType, self.itemSlot:GetSlotGroup(), cacheLastValid, cacheLastLogicX, cacheLastLogicY, cacheLastIndex)
        else
            cacheLastValid = true
            cacheLastLogicX = -1
            cacheLastLogicY = -1
        end

        cacheLastValid = cacheLastValid and bKeyValid

        -- 把TryEquipItem挪下来，因为如果不先执行旋转的话，快拆逻辑里如果有个配件在超框了就没旋转快拆就会判断格子位置不合法
        if TryEquipItem(true, self, touchLogicX, touchLogicY, index, item) then
            return
        end

        -- 只有非单格的空间才需要高亮的显示
        local actualLength, actualHeight = item:GetSizeInSlot(self.itemSlot.SlotType)
        if self._bItemShowRotated then
            actualLength, actualHeight = actualHeight, actualLength
        end
        if self:ShouldShowGridDragHint() then
            self:ShowDragHint(true, cacheLastValid, cacheLastLogicX, cacheLastLogicY, actualLength, actualHeight, cacheLastIndex)
            logerror("[InvSlotViewSunBillows]: Check item move in single grid")
            if self.itemSlot:IsContainerSlot() then
                Module.Inventory.Config.Events.evtNavTabHightLight:Invoke(cacheLastValid, self.itemSlot)
            end
        end
    end
end


function InvSlotView:OnDrop(inGeometry, inDragDropEvent, inOperation)
    self:RemoveNavBlankItemOnDragEnd()
    Module.Inventory.Config.Events.evtUpdateFirstRowBlankItemVisibility:Invoke(false)
    Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(false)
    if self.bConditionalHide then
        return
    end
    --- 准备一些必要的数据
    local dragItemView = inOperation.DefaultDragVisual
    if not dragItemView then
        return
    end

    ---@type ItemBase
    local dragItem = dragItemView.item
    if not dragItem then
        return
    end

    self:HideDragHint()
    local fastEquipCheckResult = Module.Inventory:CommonOnDropForFastEquip(inOperation, self.itemSlot, cacheLastLogicX, cacheLastLogicY, cacheLastIndex, self._bItemShowRotated)
    if fastEquipCheckResult ~= Module.Inventory.Config.EFastEquipCheckResult.NoFastEquip then
        -- -- 进到这里，说明需要走快拆的逻辑（快拆本身可能成功或者不合法）
        -- if fastEquipCheckResult == Module.Inventory.Config.EFastEquipCheckResult.Fail then
		-- 	Module.CommonTips:ShowSimpleTip(string.format(Module.Inventory.Config.Loc.ItemNotFitExt, self.itemSlot._slotConfig.name))
        -- end
        return
    end

    local loadAmmoResult = Module.Inventory:CommonOnDropForBullet(inOperation, self.itemSlot, cacheLastLogicX, cacheLastLogicY, cacheLastIndex, self._bItemShowRotated)
    if loadAmmoResult ~= ELoadAmmoResult.NoLoadAmmoLogic then
        -- 进到这里，说明需要走填弹的逻辑
        return
    end

    if not ItemOperaTool.CheckRunWarehouseLogic() then
        if dragItem:GetFeature(EFeatureType.Key) and self.itemSlot.SlotType == ESlotType.KeyChainContainer then
            Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.KeyChainNotAllowedToPutInTip)
            return
        end
    end

    self:_InternalOnDrop(inGeometry, inDragDropEvent, dragItem)
end

function InvSlotView:_InternalOnDrop(inGeometry, inDragDropEvent, dragItem)
    
    -- 记录道具操作来源
    ItemMoveCmd.RecordMoveOperation(ItemMoveCmd.EFromOperation.ItemViewPreciseDrag)
    local subIndex = self:GetSubSlotId() or cacheLastIndex
    local dropItem = self.itemSlot:GetItemAtPos(cacheLastLogicX, cacheLastLogicY, subIndex)
    local weaponFeature, dropItemFeatureType
    if dropItem then
        dropItemFeatureType = dropItem:GetFeatureType()
        weaponFeature = dropItem:GetFeature(EFeatureType.Weapon)
    end

    local fPlaceItem = function()
        local bIsMoveInNearby = dragItem.InSlot and dragItem.InSlot.SlotType == ESlotType.NearbyPickups and self.itemSlot.SlotType == ESlotType.NearbyPickups
        if bIsMoveInNearby then
            ---@type ItemLocation
            local targetLoc = ItemLocation:NewIns()
            local length, height = dragItem:GetSizeInSlot(ESlotType.NearbyPickups)
            if self._bItemShowRotated then
                length, height = height, length
            end
            targetLoc:Init(self.itemSlot, cacheLastLogicX, cacheLastLogicY, length, height, subIndex)
            targetLoc:SetRotation(self._bItemShowRotated)
            if targetLoc:CheckLocationUsed({[dragItem.gid] = true}) then
                Module.CommonTips:ShowSimpleTip(CommonWidgetConfig.Loc.NearbyPickupsNotAllowReplace)
                return
            end

        end
        if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(dragItem, self.itemSlot) then
            ItemOperaTool.DoPlaceContainerItems(dragItem, self.itemSlot, false)
            return
        end

        -- VIP扩容箱过期需要弹tips
        if ItemOperaTool.CheckRunWarehouseLogic() and self.itemSlot and self.itemSlot:CheckVipContainerIsExpired() then
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.ExpiredTips)
            return
        end

        if cacheLastValid then
            log("_InternalOnDrop", self.itemSlot.SlotType, self.itemSlot:GetSlotGroup(), cacheLastLogicX, cacheLastLogicY, self._bItemShowRotated)
            if self:IsInAutoLootMode() then
                ItemOperaTool.DoPlaceItem(dragItem, self.itemSlot)
            elseif cacheLastLogicX >= 0 and cacheLastLogicY >= 0 then
                if dragItem:IsCollectionCabinetItem() then
                    local length, height = dragItem:GetSizeInSlot(ESlotType.NearbyPickups)
                    if self._bItemShowRotated then
                        length, height = height, length
                    end
                    tarLocation:Init(self.itemSlot, cacheLastLogicX, cacheLastLogicY, length, height, subIndex)
                    if tarLocation:CheckLocationValidInSlot() then
                        local bUsed, overlapItems = tarLocation:CheckLocationUsed()
                        if bUsed then
                            if #overlapItems == 1 then
                                local curDragingItem = Module.CommonWidget:GetCurDragingItem()
                                -- 进行交换
                                if dragItem.length <= overlapItems[1].length and dragItem.width <= overlapItems[1].width then
                                    Server.CollectionRoomServer:ExchangeShowCabinet(EShowCabinetType.DIY, dragItem.cabinetId, dragItem.gridId, overlapItems[1], dragItem)
                                end
                            end
                        end
                    end
                end

                ItemOperaTool.DoPlaceItemAt(
                    dragItem,
                    self.itemSlot,
                    cacheLastLogicX,
                    cacheLastLogicY,
                    subIndex,
                    self._bItemShowRotated
                )

                if dragItem:IsCollectionCabinetItem() then
                    local newLocation = ItemLocation:NewIns()
                    if self._bItemShowRotated then
                        dragItem.length, dragItem.width = dragItem.width, dragItem.length
                    end
                    newLocation:Init(self.itemSlot, cacheLastLogicX, cacheLastLogicY, dragItem.length, dragItem.width, subIndex)
                    newLocation:SetRotation(self._bItemShowRotated)
                    Module.CollectionRoom:WithdrawShowCabinet(dragItem.cabinetId, dragItem.gridId, newLocation)
                end

                --BEGIN MODIFICATION @ VIRTUOS :
                if IsHD() then
                    Module.CommonWidget:BeginToFocusItemView(dragItem)
                end
                --END MODIFICATION
            else
                -- ItemOperaTool.DoPlaceItem(dragItem, self.itemSlot)
            end
        else
            -- if self.itemSlot:IsDepositorySlot() then
            --     -- 仓库超级特殊逻辑，目测活不久
            --     ItemOperaTool.DoPlaceItem(dragItem, self.itemSlot)
            -- else
                -- Show failure tip
                TryPlaceItem(self, cacheLastLogicX, cacheLastLogicY, cacheLastIndex, dragItem, self._bItemShowRotated, true)
            -- end
        end

        self:_ResetDragDropParams()
    end

    if ItemOperaTool.CheckRunWarehouseLogic() and dropItem and dropItem.id == dragItem.id and dragItem.canStack and
        dragItem.bindType == PropBindingType.BindingNotBind and
        dropItem.bindType == PropBindingType.BindingBinded and
        dragItem.maxStackCount - dropItem.num > 0
    then
        local fPlaceItem2Bind = function()
            local targetLoc = self.itemSlot:GetItemLocation(dropItem)
            ItemOperaTool.DoMoveItem(dragItem, targetLoc, true, dragItem.maxStackCount - dropItem.num)
        end

        --非绑定堆叠到绑定 弹出确认窗口
        -- Facade.UIManager:AsyncShowUI(UIName2ID.ItemStackPanel, nil, nil, dragItem, dropItem, fPlaceItem2Bind)
    else
        fPlaceItem()
    end
end

function InvSlotView:_GetLogicPosition(inGeometry, inEvent, bIsDrag)
    local touchAbsPos = inEvent:GetScreenSpacePosition()
    return InventoryLogic.CalculateTouchLogicPos(
        self.itemSlot,
        touchAbsPos,
        inGeometry
    )
end

function InvSlotView:_CheckLogicPosHalfUpdated(inGeometry, inEvent)
    local touchAbsPos = inEvent:GetScreenSpacePosition()
    local localPos = inGeometry:AbsoluteToLocal(touchAbsPos)
    if not lastTouchPos then
        lastTouchPos = localPos
        return true
    end

    local x, y = localPos.X, localPos.Y
    local space = self.itemSlot:GetSpaceByAbsPos(x, y)
    local index = space and space.index or -1
    if cacheLastIndex ~= index then
        cacheLastIndex = index

        return true
    end

    local startX = space and space.startX or 0
    local startY = space and space.startY or 0
    local halfX = math.floor((x - startX) / ItemConfig.DefaultItemViewSize * 2)
    local halfY = math.floor((y - startY) / ItemConfig.DefaultItemViewSize * 2)
    local lastHalfX = math.floor((lastTouchPos.X - startX) / ItemConfig.DefaultItemViewSize * 2)
    local lastHalfY = math.floor((lastTouchPos.Y - startY) / ItemConfig.DefaultItemViewSize * 2)
    lastTouchPos = localPos

    return halfX ~= lastHalfX or halfY ~= lastHalfY
end

---@param item ItemBase
function InvSlotView:_GetLogicPositionV2(inGeometry, inEvent, item, bRotated)
    local touchAbsPos = inEvent:GetScreenSpacePosition()
    local localPos = inGeometry:AbsoluteToLocal(touchAbsPos)
    -- 当使用手柄走格子时，使用左上角计算逻辑位置
    if IsHD() and WidgetUtil.IsGamepad() and not WidgetUtil.IsUsingFreeAnalogCursor() then
        return self:_GetLogicPositionV2ByLocalPos(localPos, item, bRotated, true)
    else
        return self:_GetLogicPositionV2ByLocalPos(localPos, item, bRotated)
    end
end

---@param item ItemBase
function InvSlotView:_GetLogicPositionV2ByLocalPos(localPos, item, bRotated, bCenter)
    bCenter = setdefault(bCenter, false)
    if self.itemSlot:IsEquipableSlot() then
        return 0, 0, 1, true
    end

    local posX, posY = localPos.X, localPos.Y
    local space = self.itemSlot:GetSpaceByAbsPos(posX, posY)
    if not space then
        if self.itemSlot:GetSpaceByAbsPos(posX - ItemConfig.DefaultSlotSpaceHalfPadding, posY)
                and self.itemSlot:GetSpaceByAbsPos(posX + ItemConfig.DefaultSlotSpaceHalfPadding, posY) then
            return -1, -1, -1, true
        end
        if self.itemSlot:GetSpaceByAbsPos(posX, posY - ItemConfig.DefaultSlotSpaceHalfPadding)
                and self.itemSlot:GetSpaceByAbsPos(posX, posY + ItemConfig.DefaultSlotSpaceHalfPadding) then
            return -1, -1, -1, true
        end
        return -1, -1, -1, false
    end

    local itemLength, itemHeight = item:GetSizeInSlot(self.itemSlot.SlotType)
    if bRotated then
        itemLength, itemHeight = itemHeight, itemLength
    end

    local x = (posX - space.startX) / ItemConfig.DefaultItemViewSize
    local y = (posY - space.startY) / ItemConfig.DefaultItemViewSize
    if not bCenter then
        x = x - (itemLength - 1) / 2
        y = y - (itemHeight - 1) / 2
    end

    x = math.floor(x)
    y = math.floor(y)
    if self.itemSlot:IsKeyContainerSlot() then
        local gridIndex = space.length * y + x
        if gridIndex >= space.validNum then
            return -1, -1, -1, false
        end
    end

    return x, y, space.index, true
end

local lastTouchPos = nil
local GRAB_DROP_ALLOW_DISTANCE = 5
function InvSlotView:OnNativeOnTouchStarted(inGeometry, inGestureEvent)
    self:_MouseButtonDown(inGeometry, inGestureEvent)
end

function InvSlotView:OnNativeOnTouchEnded(inGeometry, inGestureEvent)
    self:_MouseButtonUp(inGeometry, inGestureEvent)
end

---------------------------Touch改Mouse--------------------------------------

function InvSlotView:_MouseButtonDown(inGeometry, inGestureEvent)
    if Module.CommonWidget:IsInGrabMode() then
        -- 记录下按下的位置
        lastTouchPos = inGestureEvent:GetScreenSpacePosition()
    end
end

local function GenerateKey(x, y)
    return 100 * x + y
end

function InvSlotView:_MouseButtonUp(inGeometry, inGestureEvent)
    if Module.CommonWidget:IsInGrabMode() then
        local grabItem = Module.CommonWidget:GetCurGrabItem()
        if grabItem then
            local curTouchPos = inGestureEvent:GetScreenSpacePosition()
            -- local distance = lastTouchPos:Distance(curTouchPos)
            local distance = UKismetMathLibrary.Distance2D(lastTouchPos, curTouchPos)
            if distance <= GRAB_DROP_ALLOW_DISTANCE then
                -- Do drop

                -- local x, y = self:_GetLogicPosition(inGeometry, inGestureEvent, false)
                local x, y = self:_GetLogicPositionV2(inGeometry, inGestureEvent, grabItem, false)
                self:_InternalOnDragOver(inGeometry, inGestureEvent, grabItem)

                -- Auto shift
                local touchPosItem = self.itemSlot:GetItemAtPos(x, y, self:GetSubSlotId() or cacheLastIndex)
                if touchPosItem then
                    local touchPosItemLength, touchPosItemHeight = touchPosItem:GetSizeInSlot(self.itemSlot.SlotType)
                    local actualLength, actualHeight = grabItem:GetSizeInSlot(self.itemSlot.SlotType)
                    if actualLength == touchPosItemLength and actualLength == touchPosItemHeight then
                        -- Do shift
                        local touchItemLoc = self.itemSlot:GetItemLocation(touchPosItem)
                        cacheLastLogicX, cacheLastLogicY = touchItemLoc.X, touchItemLoc.Y
                        cacheLastValid = true
                    end
                end

                self:_InternalOnDrop(inGeometry, inGestureEvent, grabItem)
            end
        end

        Module.CommonWidget:StopGrab()
    end
end

function InvSlotView:OnNativeOnMouseButtonDown(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
        self:_MouseButtonDown(inGeometry, inGestureEvent)
    end
end

function InvSlotView:OnNativeOnMouseButtonUp(inGeometry, inGestureEvent)
    if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) and IsHD() then
        self:_MouseButtonUp(inGeometry, inGestureEvent)
    end
    local touchAbsPos = inGestureEvent:GetScreenSpacePosition()
    local localPos = inGeometry:AbsoluteToLocal(touchAbsPos)
    self:_TriggerSingleClickMove(localPos)
    Module.Inventory.Config.Events.evtRemoveYModeBlankItemVisibility:Invoke()
end

function InvSlotView:_TriggerSingleClickMove(localPos)
    if Module.ItemDetail:GetUserScrolledInGap() then
        return
    end
    if localPos == nil then
        return
    end
    local item = ItemOperaTool.GetCurrentSingleClickMoveItem()
    if not item then
        return
    end
    loginfo("InvSlotView:_TriggerSingleClickMove", self.itemSlot.SlotType, self.itemSlot:GetSlotGroup(), item.name, item.gid)
    local x, y, index, bInSlot = self:_GetLogicPositionV2ByLocalPos(localPos, item, false, true)
    if not bInSlot then
        ItemOperaTool.DoSelectItem(nil, false)
        return
    end
    LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_SingleClick)

    if not ItemOperaTool.VerifyItemForTargetSlot(item, self.itemSlot, true) then
        ItemOperaTool.DoSelectItem(nil, false)
        return
    end

    if ItemOperaTool.CheckRunWarehouseLogic() then
        if self.itemSlot:IsKeyContainerSlot() or self.itemSlot:IsSafeBoxContainerSlot() then
            local containerType = self.itemSlot:GetContainerType()
            local equipSlot = Server.InventoryServer:GetSlot(containerType)
            local equipItem = equipSlot and equipSlot:GetEquipItem()
            ---@type EquipmentFeature
            local equipmentFeature = equipItem and equipItem:GetFeature()
            if equipmentFeature and equipmentFeature:PermissionNotExpired() then
                Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.ExpiredTips)
                ItemOperaTool.DoSelectItem(nil, false)
                return
            end
        end
    end

    if self.itemSlot:IsKeyContainerSlot() and item:GetFeatureType() == EFeatureType.Key then
        if not ItemOperaTool.CheckKeyMatchIndex(item, self:GetSubSlotId()) then
            ItemOperaTool.DoSelectItem(nil, false)
            return
        end
    end

    if item.InSlot and item.InSlot:GetContainerSlot() == self.itemSlot then
        ItemOperaTool.DoSelectItem(nil, false)
        return
    end

    if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, self.itemSlot) then
        if not ItemOperaTool.DoPlaceContainerItems(item, self.itemSlot, false) then
            Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.SingleClickMove_FindEmptyLocationFail)
        end
        ItemOperaTool.DoSelectItem(nil, false)
        return
    end

    -- 如果恰好点到了小片网格之间的缝隙，在附近找一个空格子
    if x < 0 or y < 0 or index < 0 then
        for _, xBias in ipairs({-1, 0, 1}) do
            for _, yBias in ipairs({-1, 0, 1}) do
                if xBias * yBias == 0 and xBias ~= yBias then
                    local newLocalPos = localPos + FVector2D(xBias, yBias) * ItemConfig.DefaultItemViewSize
                    x, y, index, _ = self:_GetLogicPositionV2ByLocalPos(newLocalPos, item, false, true)
                    if x >= 0 and y >= 0 and index >= 0 then
                        local cellData = self.itemSlot:GetSlotCellData(x, y, index)
                        if not cellData or not cellData.item then
                            break
                        end
                    end
                end
            end
        end
    end

    if x < 0 or y < 0 or index < 0 then
        ItemOperaTool.DoSelectItem(nil, false)
        return
    end

    index = self:GetSubSlotId() or index

    local curItem = self.itemSlot:GetItemAtPos(x, y, index)
    if curItem and curItem.gid == item.gid then
        loginfo("InvSlotView:_TriggerSingleClickMove single click the same item at", x, y, index)
        ItemOperaTool.DoSelectItem(nil, false)
        return
    end

    local spaceLength, spaceHeight = self.itemSlot:GetSlotSize(index)
    -- 偶数格子的物品要以更靠近开头的格子为中心格（比如2格物品以第1格作为中心，4格物品以第2格作为中心），这样更符合直觉
    local itemHalfLength = math.floor((item.length - 1) / 2)
    local itemHalfHeight = math.floor((item.width - 1) / 2)

    local clickedGrid = {x = x, y = y}
    local ignoreGids = {[item.gid] = true}
    -- 检查被点击的格子是否在指定的道具放置的范围内
    ---@param itemLoc ItemLocation
    local function CheckClickedGridInItemLoc(itemLoc)
        if itemLoc then
            if itemLoc.Left <= clickedGrid.x and clickedGrid.x <= itemLoc.Right then
                if itemLoc.Top <= clickedGrid.y and clickedGrid.y <= itemLoc.Bottom then
                    return true
                end
            end
        end
        return false
    end
    -- 检查一个格子是否满足放置道具的需求
    local function CheckGridMeetNeed(loc)
        if loc and loc:CheckLocationValidInSlot() and not loc:CheckLocationUsed(ignoreGids) then
            return true
        end
        return false
    end
    -- 获取以一个格子为中心放置道具时的位置
    local function GetItemLocByGrid(grid, bRotated)
        local leftTopX = grid.x - (bRotated and itemHalfHeight or itemHalfLength)
        local leftTopY = grid.y - (bRotated and itemHalfLength or itemHalfHeight)
        if leftTopX >= 0 and leftTopX < spaceLength then
            if leftTopY >= 0 and leftTopY < spaceHeight then
                ---@type ItemLocation
                local loc = ItemLocation:NewIns()
                if bRotated then
                    loc:Init(self.itemSlot, leftTopX, leftTopY, item.width, item.length, index, bRotated)
                else
                    loc:Init(self.itemSlot, leftTopX, leftTopY, item.length, item.width, index, bRotated)
                end
                return loc
            end
        end
    end
    -- 记录检查过的格子，就不用再检查了，避免无限循环
    local foundGrids = {
        [false] = {},
        [true] = {},
    }
    -- 在附近找一个空格子
    local function SearchNewGrid(grid, queue, bRotated)
        for _, xBias in ipairs({-1, 0, 1}) do
            for _, yBias in ipairs({-1, 0, 1}) do
                if xBias * yBias == 0 and xBias ~= yBias then
                    local newCenterX = grid.x + xBias
                    local newCenterY = grid.y + yBias
                    local key = GenerateKey(newCenterX, newCenterY)
                    if not foundGrids[bRotated][key] then
                        foundGrids[bRotated][key] = true
                        if newCenterX >= 0 and newCenterX < spaceLength then
                            if newCenterY >= 0 and newCenterY < spaceHeight then
                                local cellData = self.itemSlot:GetSlotCellData(newCenterX, newCenterY, index)
                                -- 找空格子时需要把当前要放的物品占的格子也当做空格子，否则有些仅旋转物品就可以放下的地方会被误认为放不下
                                if not cellData or not cellData.item or cellData.item == item then
                                    queue:push_right({x = newCenterX, y = newCenterY})
                                end
                            end
                        end
                    end
                end
            end
        end
    end
    local foundNearestLoc = {
        [false] = nil,
        [true] = nil,
    }
    --- 广度优先搜索
    for _, bRotated in ipairs({false, true}) do
        local queue = Queue.new()
        queue:push_right(clickedGrid)
        while not queue:is_empty() do
            local grid = queue:pop_left()
            foundGrids[bRotated][GenerateKey(grid.x, grid.y)] = true
            if bRotated == false or item.length ~= item.width then
                local loc = GetItemLocByGrid(grid, bRotated)
                if CheckGridMeetNeed(loc) then
                    foundNearestLoc[bRotated] = loc
                    break
                end
            end
            SearchNewGrid(grid, queue, bRotated)
        end
    end
    -- 如果能放进去就放
    local function PutInIfCan(loc)
        if loc and ItemOperaTool.DoPlaceItemAt(item, loc.ItemSlot, loc.X, loc.Y, loc.SubIndex, loc.bRotated) then
            ItemOperaTool.DoSelectItem(nil, false)
            return true
        end
        return false
    end
    --- 优先考虑包含玩家点击格子的位置, 再考虑不包含玩家点击格子的位置
    for _, bNeedIncludeClickedGrid in ipairs({true, false}) do
        for _, bRotated in ipairs({false, true}) do
            ---@type ItemLocation
            local loc = foundNearestLoc[bRotated]
            if loc and (not bNeedIncludeClickedGrid or CheckClickedGridInItemLoc(loc)) and PutInIfCan(loc) then
                ItemOperaTool.DoSelectItem(nil, false)
                return
            end
        end
    end
    Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.SingleClickMove_FindEmptyLocationFail)
    ItemOperaTool.DoSelectItem(nil, false)
end

---------------------------Touch改Mouse--------------------------------------

function InvSlotView:OnPostDragItemSpin()
    if self._curDragItemView then

        self._bItemRotated = self._curDragItemView.bRotated
        self._bItemShowRotated = self._bItemRotated

        cacheLastValid = TryPlaceItem(self, cacheLastLogicX, cacheLastLogicY, cacheLastIndex, self._curDragItemView.item, self._bItemShowRotated)
        
        local curDragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
        if curDragDropInfo and ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(curDragDropInfo.item, self.itemSlot) then
            return
        end

        local actualLength, actualHeight = self._curDragItemView.item:GetSizeInSlot(self.itemSlot.SlotType)
        if self._bItemShowRotated then
            actualLength, actualHeight = actualHeight, actualLength
        end

        self:ShowDragHint(true, cacheLastValid, cacheLastLogicX, cacheLastLogicY, actualLength, actualHeight, cacheLastIndex)
    end
end

-- 展示拖拽时的占格提示
---@param bIsSnapHint boolean 是吸附高亮
function InvSlotView:ShowDragHint(bShow, bValid, x, y, length, height, index, bFastEquipPutIn, bIsSnapHint)
    bFastEquipPutIn = setdefault(bFastEquipPutIn, false)
    bIsSnapHint = setdefault(bIsSnapHint, false)

    if not bShow then
        self:_HideHighlights()
        return
    end

    -- if not bShow then
    --     self._wtHighlight:Collapsed()
    --     return
    -- else
    --     self._wtHighlight:SelfHitTestInvisible()
    -- end

    local bReplace = false
    if length >= 0 and height >= 0 then
        ---@type ItemLocation
        local tmpLoc = ItemLocation:NewIns()
        tmpLoc:Init(self.itemSlot, x, y, length, height, index)
        local bUsed, overlapItems = tmpLoc:CheckLocationUsed()
        if bUsed then
            if #overlapItems == 1 then
                local curDragingItem = Module.CommonWidget:GetCurDragingItem()
                bReplace = (overlapItems[1].gid ~= curDragingItem.gid) and not ItemBaseTool.CheckItemCanCombine(curDragingItem, overlapItems[1])
            elseif #overlapItems > 1 then
                bReplace = true
            end
        end
    else
        -- if bShow and bValid then
        --     local curDragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
        --     local item = curDragDropInfo.item
        --     if item.InSlot == self.itemSlot then
        --         local curLocation = self.itemSlot:GetItemLocation(item)
        --         x = curLocation.X
        --         y = curLocation.Y
        --         length = curLocation.Length
        --         height = curLocation.Height
        --     else

        --     end
        -- else
            
        -- end
    end

    local fHighlightFunc
    if bValid then
        if bFastEquipPutIn then
            fHighlightFunc = CommonItemHighlight.SetPutInValid
            -- self._wtHighlight:SetPutInValid()
        elseif bReplace then
            -- self._wtHighlight:PlayDropReplaceValidAnim()
            fHighlightFunc = CommonItemHighlight.PlayDropReplaceValidAnim
        elseif bIsSnapHint then
            fHighlightFunc = CommonItemHighlight.SetDoubleValid
        else
            -- self._wtHighlight:PlayDropValidAnim()
            fHighlightFunc = CommonItemHighlight.PlayDropValidAnim
        end
    else
        if bFastEquipPutIn then
            -- self._wtHighlight:SetPutInInvalid()
            fHighlightFunc = CommonItemHighlight.SetPutInInvalid
        elseif bReplace then
            -- self._wtHighlight:PlayDropReplaceInvalidAnim()
            fHighlightFunc = CommonItemHighlight.SetPutInInvalid -- 这里原来的PlayDropReplaceInvalidAnim会造成闪烁
        elseif bIsSnapHint then
            fHighlightFunc = CommonItemHighlight.SetDoubleInvalid
        else
            -- self._wtHighlight:PlayDropInvalidAnim()
            fHighlightFunc = CommonItemHighlight.PlayDropInvalidAnim
        end
    end

    for key, itemView in pairs(self._DoubleHighLightItemViews) do
        if itemView:IsInPermanentHighLight() then
            itemView:ToggleHighlightComponent(true, true)
        end
        self._DoubleHighLightItemViews[key] = nil
    end

    --- 对于快速安装/装填类型，如果枪械ItemView处于常驻高亮，那么SlotView显示的是反馈高亮，需要使用背景层级，否则使用正常高亮层级
    if bFastEquipPutIn then
        local item = self.itemSlot:GetItemAtPos(x, y, index)
        if item then
            local itemView = Module.CommonWidget:GetAssignedItemView(item)
            if itemView and itemView:IsInPermanentHighLight() then
                itemView:ToggleHighlightComponent(true, true, true)
                table.insert(self._DoubleHighLightItemViews, itemView)
                self:HideDragHint()
                return
            end
        end
    end
    --- 对于快速安装/装填类型，高亮使用背景层级
    self:_ShowHighlights(fHighlightFunc, x, y, length, height, index)
    
    -- if length == -1 or height == -1 then
    --     local absLength, absHeight = self.itemSlot:GetSlotLayoutData()
    --     self:SetViewByAbsPos(self._wtHighlight, 0, 0, absLength, absHeight)
    -- else
    --     local space = index and self.itemSlot:GetSpaceByIndex(index) or nil
    --     self:SetViewByLogicPos(self._wtHighlight, x, y, length, height, space)
    -- end
end

function InvSlotView:_CreateHighlightComponent()
    ---@type CommonItemHighlight
    self:_AddOrGetHighlightComponentByIndex( #self._wtHighlights + 1)
end

function InvSlotView:_AddOrGetHighlightComponentByIndex(index)
    -- already exists?
    local highlightComponent = self._wtHighlights[index]
    if highlightComponent then
        highlightComponent:Visible()
        return highlightComponent
    end

    -- allocate
    local highlightComponent = Module.CommonWidget:ObtainIVComp(UIName2ID.CommonItemHighlight)
    self._cellsContainer:AddChild(highlightComponent)
    self:SetComponentZOrder(highlightComponent, CommonSlotView.HighlightZOrder)
    self._wtHighlights[#self._wtHighlights + 1] = highlightComponent
    return highlightComponent
end

function InvSlotView:_PreCheckHighlightsNum()
    if self.itemSlot then
        local num = self.itemSlot:IsKeyContainerSlot() and 1 or self.itemSlot:GetSpacesNum()
        if num > #self._wtHighlights then
            for i = 1, num - #self._wtHighlights do
                self:_CreateHighlightComponent()
            end
        end
    end
end

function InvSlotView:_ShowHighlights(fHighlightFunc, x, y, length, height, index)

    if length == -1 or height == -1 then
        local highLightComponentIndex = 1
        local allSpaces = self.itemSlot:GetSpaceBatch()
        for index, space in pairs(allSpaces) do
            local highlight = self:_AddOrGetHighlightComponentByIndex(highLightComponentIndex)
            if highlight then
                highlight:SelfHitTestInvisible()
                fHighlightFunc(highlight)
                local sizeX = space.sizeX
                if self.itemSlot:IsKeyContainerSlot() then
                    sizeX = math.min(sizeX, space.validNum * ItemConfig.DefaultItemViewSize)
                end
                self:SetViewByAbsPos(highlight, space.startX, space.startY, sizeX, space.sizeY)
            end

            highLightComponentIndex = highLightComponentIndex + 1
            -- 如果是钥匙包只需要高亮一个
            if self.itemSlot:IsKeyContainerSlot() then
                return
            end
        end
    else
        -- Only show one highlight
        self:_HideHighlights(true)
        
        local spaceLength, spaceHeight = self.itemSlot:GetSlotSize(index)
        local left = math.max(x, 0)
        local right = math.min(x + length, spaceLength)
        local top = math.max(y, 0)
        local bottom = math.min(y + height, spaceHeight)
        local length = right - left
        local height = bottom - top

        if length <= 0 or height <= 0 then
            self:_HideHighlights(false)
            return
        end
        x = left
        y = top

        local highlight = self:_AddOrGetHighlightComponentByIndex(1)
        if highlight then
            highlight:SelfHitTestInvisible()
            fHighlightFunc(highlight)
        end

        local space = index and self.itemSlot:GetSpaceByIndex(index) or nil

        self:SetViewByLogicPos(highlight, x, y, length, height, space)
    end
end

function InvSlotView:SetComponentZOrder(Component, ZOrder)
    local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(Component)
    if canvasSlot then
        canvasSlot:SetZOrder(ZOrder)
    end
end

---@param bKeepOne boolean 是否要留下一个
function InvSlotView:_HideHighlights(bKeepOne)
    bKeepOne = setdefault(bKeepOne, false)
    local componentToKeep
    for i, component in ipairs(self._wtHighlights) do
        if not bKeepOne or i ~= 1 then
            self._cellsContainer:RemoveChild(component)
            Module.CommonWidget:FreeIVComp(component, UIName2ID.CommonItemHighlight)
        else
            componentToKeep = component
        end
    end
    self._wtHighlights = {componentToKeep}
end

function InvSlotView:SetFilter(filterMode)
    self._filterMode = filterMode
end

function InvSlotView:GetFilter()
    return self._filterMode
end

function InvSlotView:GetFilterFunc()
    if self._filterMode then
        return Module.Inventory.Config.FilterFuncs[self._filterMode]
    else
        return nil
    end
end

---@param location ItemLocation
function InvSlotView:_CheckLocIndexMatch(location)
    return not self._bShowSub or location.SubIndex == self:GetSubSlotId() or self._subSlotId == ItemConfig.DEFAULT_SLOT_SPACE_INDEX
end

---@param moveItemInfo itemMoveInfo
function InvSlotView:OnItemMove(moveItemInfo)
    if self._bUseCustomData or self:IsInAutoLootMode() then
        self:RefreshView(false)
        return
    end

    local reason = moveItemInfo.Reason
    ---@type ItemBase
    local item = moveItemInfo.item
    -- 不可见道具不需要展示
    if item:IsInvisibleItem() then
        logerror("InvSlotView:OnItemMove add invisible item", item.id)
        return
    end

    --BEGIN MODIFICATION @ VIRTUOS : 刷新空白对象的显示情况
    if IsHD() then
        self:_RefreshBlankItem()
    end
    --END MODIFICATION

    ---@type ItemLocation
    local oldLoc
    ---@type ItemLocation
    local newLoc
    
    if reason == PropChangeType.Add then
        newLoc = moveItemInfo.NewLoc
        if self:_CheckLocIndexMatch(newLoc) and newLoc.ItemSlot == self.itemSlot then
            -- local newItemWidget = self:AddItem(item, newLoc, false, false)
            -- self:PlayHighlightForItem(newItemWidget)
            -- Facade.LuaFramingManager:RegisterFrameTask(self.SingleFrameTask_AddItem, self, {item, true})
            self:DispatchIVFrameTask({item}, true, true, moveItemInfo.bNeedScrollTo)
        end
    elseif reason == PropChangeType.Del then
        oldLoc = moveItemInfo.OldLoc
        if self:_CheckLocIndexMatch(oldLoc) and oldLoc and oldLoc.ItemSlot == self.itemSlot then
            -- self:RemoveItem(item)
            -- Facade.LuaFramingManager:RegisterFrameTask(self.SingleFrameTask_RemoveItem, self, {item})
            self:DispatchIVFrameTask({item}, false)

            --BEGIN MODIFICATION @ VIRTUOS : 原Item被移除后，尝试聚焦到对应的BlankItem
            if IsHD() then
                self:TrySetFocusToBlankItem(oldLoc)
            end
            --END MODIFICATION
        end
    elseif reason == PropChangeType.Modify then
        newLoc = moveItemInfo.NewLoc
        if newLoc and self:_CheckLocIndexMatch(newLoc) and newLoc.ItemSlot == self.itemSlot then
            -- targetItemView = self:GetViewByItem(item)
            -- if targetItemView then
            --     targetItemView:Visible()
            --     targetItemView:InitItem(item, self)
            --     self:_CheckItemCustomRefresh(targetItemView)
            --     self:PlayHighlightForItem(targetItemView)
            -- end

            -- 判断是否只是切换搜索状态
            if item:GetLastModifyReason() == EItemInfoUpdatedReason.SearchState then
                local bHasScrollToSearchedItem = Module.Looting:HasScrollToSearchedItem()
                if not bHasScrollToSearchedItem then
                    Module.Looting:SetHasScrollToSearchedItem(true)
                end
                local bScrollToItem
                if Module.Looting:GetDisableScrollSearchedItem() then
                    bScrollToItem = false
                else
                    -- 如果这个道具已经被判断为滚动目标，那么继续滚动，防止以下case：
                    -- 道具开始搜索，被指定为滚动目标；过一会儿道具完成搜索，但还没开始滚动，道具又不再指定为滚动目标，导致滚动被取消
                    if self._itemsToScroll[item] then
                        bScrollToItem = true
                    else
                        bScrollToItem = not bHasScrollToSearchedItem
                    end
                end
                logerror("InvSlotView:OnItemMove search state change", item.name, bScrollToItem)
                self:DispatchIVFrameTask({item}, true, false, bScrollToItem)
            else
                self:DispatchIVFrameTask({item}, true, true, moveItemInfo.bNeedScrollTo)
            end
        end
    elseif reason == PropChangeType.Move then
        -- end
        oldLoc = moveItemInfo.OldLoc
        newLoc = moveItemInfo.NewLoc
        
        local bShouldRemove = oldLoc and self:_CheckLocIndexMatch(oldLoc) and oldLoc.ItemSlot == self.itemSlot
        if bShouldRemove then
            -- self:RemoveItem(item)
            -- loginfo("[InvSlotViewRemoveItem]itemId:" .. item.id .. " OldSlot:" .. oldLoc.ItemSlot.SlotType)
            -- Facade.LuaFramingManager:RegisterFrameTask(self.SingleFrameTask_RemoveItem, self, {item})
            self:DispatchIVFrameTask({item}, false)

            --BEGIN MODIFICATION @ VIRTUOS : 原Item被快速转移到其他Slot后，尝试Focus到BlankItem
            if IsHD() and Module.CommonWidget:IsInTryFocusItemView() == false then
                self:TrySetFocusToBlankItem(oldLoc)
            end
            --END MODIFICATION
        end

        local bShouldAdd = newLoc and self:_CheckLocIndexMatch(newLoc) and newLoc.ItemSlot == self.itemSlot
        if bShouldAdd then
            self:DispatchIVFrameTask({item}, true, true, moveItemInfo.bNeedScrollTo)
        end
    else
        error("Impossible")
    end
end

function InvSlotView:_OnProcItemsDuringScroll(item2Show, item2Recycle)
    if #item2Show > 1 then
        table.sort(item2Show, function(a, b)
            if a == nil or b == nil or a == b then
                return false
            end
            ---@type ItemLocation
            local aLoc = self.itemSlot:GetItemLocation(a)
            ---@type ItemLocation
            local bLoc = self.itemSlot:GetItemLocation(b)
            if aLoc and bLoc then
                return self.itemSlot:CompareLocation(aLoc.SubIndex, aLoc.X, aLoc.Y, bLoc.SubIndex, bLoc.X, bLoc.Y)
            end
            return false
        end)
    end
    if InventoryConfig.bUseMaxFrameOrItem then
        local ScrollDispatchMaxFrameNum = self:GetScrollDispatchMaxFrameNum()
        local item2ProcBatch = {}
        local procNumPerFrame = #item2Recycle / math.max(ScrollDispatchMaxFrameNum, 1)
        for index, item in ipairs(item2Recycle) do
            table.insert(item2ProcBatch, item)
            if #item2ProcBatch >= procNumPerFrame or index == #item2Recycle then
                self:DispatchIVFrameTask(item2ProcBatch, false)
                item2ProcBatch = {}
            end
        end
        item2ProcBatch = {}
        procNumPerFrame = #item2Show / math.max(ScrollDispatchMaxFrameNum, 1)
        for index, item in ipairs(item2Show) do
            table.insert(item2ProcBatch, item)
            if #item2ProcBatch >= procNumPerFrame or index == #item2Show then
                self:DispatchIVFrameTask(item2ProcBatch, true)
                item2ProcBatch = {}
            end
        end
    else
        local ScrollDispatchMaxItemNum = self:GetScrollDispatchMaxItemNum()
        local item2ProcBatch = {}
        for index, item in ipairs(item2Recycle) do
            table.insert(item2ProcBatch, item)
            if #item2ProcBatch >= ScrollDispatchMaxItemNum or index == #item2Recycle then
                self:DispatchIVFrameTask(item2ProcBatch, false)
                item2ProcBatch = {}
            end
        end
        item2ProcBatch = {}
        for index, item in ipairs(item2Show) do
            table.insert(item2ProcBatch, item)
            if #item2ProcBatch >= ScrollDispatchMaxItemNum or index == #item2Show then
                self:DispatchIVFrameTask(item2ProcBatch, true)
                item2ProcBatch = {}
            end
        end
    end
end

function InvSlotView:GetScrollDispatchMaxFrameNum()
    local ScrollDispatchMaxFrameNum
    if self.itemSlot:IsDepositorySlot() then
        ScrollDispatchMaxFrameNum = InventoryConfig.ScrollDispatchMaxFrameNumForDepository
    end
    if ScrollDispatchMaxFrameNum == nil then
        if ItemOperaTool.CheckRunWarehouseLogic() then
            ScrollDispatchMaxFrameNum = InventoryConfig.ScrollDispatchMaxFrameNumInWH
        else
            ScrollDispatchMaxFrameNum = InventoryConfig.ScrollDispatchMaxFrameNumInLooting
        end
    end
    return math.max(ScrollDispatchMaxFrameNum, 0)
end

function InvSlotView:GetScrollDispatchMaxItemNum()
    local ScrollDispatchMaxItemNum
    if self.itemSlot:IsDepositorySlot() then
        ScrollDispatchMaxItemNum = InventoryConfig.ScrollDispatchMaxItemNumForDepository
    end
    if ScrollDispatchMaxItemNum == nil then
        if ItemOperaTool.CheckRunWarehouseLogic() then
            ScrollDispatchMaxItemNum = InventoryConfig.ScrollDispatchMaxItemNumInWH
        else
            ScrollDispatchMaxItemNum = InventoryConfig.ScrollDispatchMaxItemNumInLooting
        end
    end
    return math.max(ScrollDispatchMaxItemNum, 1)
end

function InvSlotView:BindScrollBox(scrollBox)
    Module.ItemDetail:AddConcernedScrollBox(scrollBox)
    CommonSlotView.BindScrollBox(self, scrollBox)
end

function InvSlotView:OnRefreshGrid(targetSlot)
    if self.itemSlot == targetSlot then
        self:RefreshView()
    end
end

function InvSlotView:OnInventoryFetchFinished()
    self:RefreshView()
end

---@param item ItemBase
local function checkSize(item)
    return item.length ~= 0 and item.width ~= 0
end

---@param slot ItemSlot
function InvSlotView:OnRefreshSlot(slot)
    if not slot or (self.itemSlot and self.itemSlot == slot) then
        self:RefreshView()
    end
end

-----------------------------------------------------------------------
--region Override functions

function InvSlotView:_GetSlotSize()
    if self._bUseCustomData then
        return self._slotLength, self._slotHeight
    elseif self.itemSlot then
        return self.itemSlot:GetSlotSize(self:GetSubSlotId() or ItemConfig.DEFAULT_SLOT_SPACE_INDEX)
    else
        return 0, 0
    end
end

function InvSlotView:_GetViewSize(length, height)
    local finalLength, finalHeight = 0, 0
    if self.itemSlot then
        finalLength, finalHeight = self.itemSlot:GetSlotLayoutData()
        --- 手游安全箱特殊处理：当格子不足2行时，补足两行的高度
        if not IsHD() and not ItemOperaTool.CheckRunWarehouseLogic() and self.itemSlot.SlotType == ESlotType.SafeBoxContainer then
            local MobileSafeBoxMinRows = CommonSlotView.MOBILE_SAFEBOX_MIN_ROWS
            if self.itemSlot.Width <= MobileSafeBoxMinRows then
                finalHeight = finalHeight + (MobileSafeBoxMinRows - self.itemSlot.Width) * ItemConfig.DefaultItemViewSize
            end
        end
    else
        finalLength, finalHeight = CommonSlotView._GetViewSize(self, length, height)
    end

    if self._showContainerGridLength then
        local defaultLength, defaultHeight = CommonSlotView._GetViewSize(self)
        finalLength = math.max(finalLength, defaultLength)
        finalHeight = math.max(finalHeight, defaultHeight)
    end

    return finalLength, finalHeight
end

function InvSlotView:GetViewSize(length, height)
    return self:_GetViewSize(length, height)
end

function InvSlotView:_CalSlotItemLocations()
    self._allItemLocations = {}
    self._allSlotCells = {}

    if self._bUseCustomData then
        if self._customItemLocGetter then
            self:_CalSlotItemLocationsFromCustom()
        else
            local items = self:GetAllItems()

            local length, height = self:_GetSlotSize()
            local result, mapItem2ActualPos, unfitItems =
            ItemSpaceAlgorithm.Fit(items, ESlotType.MainContainer, length, 999, false)
            local key
            for item, Pos in pairs(mapItem2ActualPos) do
                ---@type ItemLocation
                local newLoc = ItemLocation:NewIns()
                local length, height = item:GetSizeInSlot()
                newLoc:Init(self.itemSlot, Pos.PosX, Pos.PosY, length, height, 1)
                newLoc:SetRotation(Pos.bRotated)
                self._allItemLocations[item.instanceId] = newLoc

                for i, j in newLoc:GetPointIter() do
                    key = ItemSlot.GetKeyByPos(i, j, newLoc.SubIndex)
                    self._allSlotCells[key] = { loc = newLoc, item = item }
                end
            end
        end

        return
    end

    if self:IsInAutoLootMode() then
        local result, mapItem2ActualPos, unfitItems = ItemOperaTool.TryPlaceItems({}, self.itemSlot)
        if not result then
            self._allItemLocations = self.itemSlot:GetItemsPosition()
            self._allSlotCells = self.itemSlot:GetSlotCells()
        else
            local key
            for item, Pos in pairs(mapItem2ActualPos) do
                ---@type ItemLocation
                local newLoc = ItemLocation:NewIns()
                local length, height = item:GetSizeInSlot(self.itemSlot.SlotType)
                newLoc:Init(self.itemSlot, Pos.PosX, Pos.PosY, length, height, 1)
                newLoc:SetRotation(Pos.bRotated)
                self._allItemLocations[item.instanceId] = newLoc

                for i, j in newLoc:GetPointIter() do
                    key = ItemSlot.GetKeyByPos(i, j, newLoc.SubIndex)
                    self._allSlotCells[key] = { loc = newLoc, Item = item }
                end
            end
        end
    else
        self._allItemLocations = self.itemSlot:GetItemsPosition()
        self._allSlotCells = self.itemSlot:GetSlotCells()
    end
end

function InvSlotView:GetAllItems(outRet)
    if self._bUseCustomData and self._customItemListGetter then
        return self._customItemListGetter()
    end

    local ret = outRet or {}
    ---@type ItemLocation
    local loc
    for _, item in ipairs(self.itemSlot:GetItems_RefConst()) do
        if not item:IsInvisibleItem() then
            loc = self.itemSlot:GetItemLocation(item)
            if not self._bShowSub or loc.SubIndex == self:GetSubSlotId() then
                table.insert(ret, item)
            end
        end
    end

    return ret
end

function InvSlotView:ShowMask(bShow)
    CommonSlotView.ShowMask(self, bShow)

    --BEGIN MODIFICATION @ VIRTUOS : 刷新空白对象的显示情况
    if IsHD() then
        self.bShownMask = bShow
        self:_RefreshBlankItem()
    end
    --END MODIFICATION

    if not bShow then
        return
    end

    if self.itemSlot then
        local allSpaces = self.itemSlot:GetSpaceBatch()
        if self.itemSlot:GetSpacesNum() > 1 then
            local i = 1
            for index, space in pairs(allSpaces) do
                local maskImg = self._multiMaskImgs[i]
                if not maskImg then
                    maskImg = self:_CreateMaskImg()
                end
                
                self:_SetMaskImg(maskImg, space.startX, space.startY, space.length, space.height)

                i = i + 1
            end
        end
    end
end

function InvSlotView:_UpdateBgTexData(bForce)
    bForce = setdefault(bForce, false)
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        if self.itemSlot then
            if self.itemSlot.SlotType ~= ESlotType.NearbyPickups 
                and self.slotSpaceUpdateCount == self.itemSlot.serverUpdateCount and not bForce then
                return
            end
            self.slotSpaceUpdateCount = self.itemSlot.serverUpdateCount
        end
    end

    CommonSlotView._UpdateBgTexData(self)

    self:ResetConditionHide()

    if self.itemSlot then
        local allSpaces = self.itemSlot:GetSpaceBatch()
        if self.itemSlot:IsKeyContainerSlot() then
            loginfo("InvSlotView:_UpdateBgTexData ------ KeyContainerSlot")
            local curSpace = allSpaces[self:GetSubSlotId()]
            local tilingImg = self._multiTilingImgs[1]
            local tilingMaterial = tilingImg:GetDynamicMaterial()
            if curSpace then
                local validNum = curSpace.validNum
                local length = curSpace.length
                local height = curSpace.height
                local h = math.floor(validNum / length)
                local l = validNum - h * length
                local tolerance = 0.003
    
                tilingMaterial:SetScalarParameterValue("MaskX", l / length + tolerance)
                tilingMaterial:SetScalarParameterValue("MaskY", h / height + tolerance)

                local outlineImg = self._multilOutlineImgs[1]

                local maxLength
                if ItemOperaTool.CheckRunWarehouseLogic() then
                    maxLength = UInventoryUtil.GetDepositoryKeyChainContainerMaxLength()
                else
                    maxLength = UInventoryUtil.GetLootingKeyChainContainerMaxLength()
                end
                maxLength = math.min(maxLength, length)
                ULuaExtension.InitSplineAreaWidgetForKeySlotView(outlineImg, validNum, maxLength, CommonSlotView.SPLINE_THICKNESS, CommonSlotView.SPLINE_COLOR)
            else
                tilingMaterial:SetScalarParameterValue("MaskX", 1)
                tilingMaterial:SetScalarParameterValue("MaskY", 1)
            end
        elseif self.itemSlot:GetSpacesNum() > 1 then
            local i = 1
            for index, space in pairs(allSpaces) do

                loginfo("InvSlotView:_UpdateBgTexData", self.itemSlot:GetSlotGroup(), self.itemSlot.SlotType, index, space.startX, space.startY, space.length, space.height)

                local tilingImg = self._multiTilingImgs[i]
                if not tilingImg then
                    tilingImg = self:_CreateTilingImg()
                end
                
                self:_SetTilingImg(tilingImg, space.startX, space.startY, space.length, space.height)

                local outlineImg = self._multilOutlineImgs[i]
                if not outlineImg then
                    outlineImg = self:_CreateOutlineImg()
                end
                self:_SetOutlineImg(outlineImg, space.startX, space.startY, space.length, space.height)

                i = i + 1
            end
        end

        --BEGIN MODIFICATION @ VIRTUOS : 初始化空白可导航控件
        -- she1 SOL不上手柄，先屏蔽（性能问题）
        if IsHD() then
            self:_InitBlankItem()
        end
        --END MODIFICATION

    end
end

--endregion
-----------------------------------------------------------------------

function InvSlotView:_OnItemOperaModeChanged(operaMode)
    self:RefreshView(false)
end

--region SubSlot的一些支持
function InvSlotView:SetSubSlotId(subSlotId, bRefreshView)
    -- Check valid
    local spaceCell = self.itemSlot:GetSpaceByIndex(subSlotId)
    if not spaceCell then
        logwarning(string.format("Not valid sub slot id [%d] for slot [%d]", subSlotId, self.itemSlot.SlotType))
        return
    end

    self._subSlotId = subSlotId
    self._bShowSub = true

    if bRefreshView then
        self:RefreshView()
    end
end

function InvSlotView:GetSubSlotId()
    return self._bShowSub and self._subSlotId or nil
end

--endregion

function InvSlotView:GetHighlightView()
    return self._wtHighlight
end

---@param itemWidget IVWarehouseTemplate
function InvSlotView:PlayHighlightForItem(itemWidget)
    if itemWidget then
        if self._bPlayGuideHighlightForItemMove then
            itemWidget:GuideHighlightItem()
        else
            itemWidget:HighlightItem()
        end
    end
end

-- 【新手引导】设置使用新手引导的高亮替代常规高亮
function InvSlotView:SetPlayGuideHighlightForItemMove(bPlayGuideHighlightForItemMove)
    self._bPlayGuideHighlightForItemMove = bPlayGuideHighlightForItemMove
end

-- 【新手引导】停止当前所有道具的高亮
function InvSlotView:StopAllItemsHighlight()
    for item, itemView in pairs(self._allItemViews) do
        itemView:StopHighlightItem()
    end
end

function InvSlotView:_CheckShouldClearItemViews()
    if not InvSlotView.bShouldCheckShouldClearItemViews then
        return false
    end

    if not self.itemSlot then
        return false
    end

    local slotGroup = self.itemSlot:GetSlotGroup()
    if slotGroup == ESlotGroup.Player then
        return false
    end

    self:ClearChildren()

    return true
end
--BEGIN MODIFICATION @ VIRTUOS : 手柄导航相关���助方法
function InvSlotView:_InitBlankItem()
    if not IsHD() then
        return
    end

    self:_FreeBlankItem()
    self:_FreeFirstRowBlankItem()

    local allSpaces = self.itemSlot:GetSpaceBatch()
    
    if not allSpaces then
        return
    end

    for index, space in pairs(allSpaces) do
        -- 仓库页只在第一列生成导航格
        if self.itemSlot.SlotType > ESlotType.DepositoryStart and self.itemSlot.SlotType < ESlotType.DepositoryEnd then
            self:_CreateTilingBlankItemInInvTab(space, index)
        else
            self:_CreateTilingBlankItem(space, index)
        end
        
    end
end

function InvSlotView:_CreateTilingBlankItem(space, spaceIndex)
    if not IsHD() then
        return
    end

    local spaceLength = space.length
    local spaceHeight = space.height

    -- 剔除8*200这个特异数
    if spaceLength == 8 and spaceHeight == 200 then
        loginfo("InvSlotView:_CreateTilingBlankItem: Culling Speical Num 8*200")
        return
    end

    for i = 1, spaceLength do
        for j = 1, spaceHeight do

            local spaceX = i - 1
            local spaceY = j - 1

            local textureSize = CommonSlotView.DEFAULT_TEXTURE_SIZE

            local locX = space.startX + spaceX * textureSize
            local locY = space.startY + spaceY * textureSize

            local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonBlankItemTemplate, self._cellsContainer)
            
            if uiIns then
                local commonBlankItem = getfromweak(uiIns)

                if commonBlankItem then
                    commonBlankItem:Init(self.itemSlot, spaceIndex, spaceX, spaceY, self, self.bShownMask)
                    self:SetViewByAbsPos(commonBlankItem, locX, locY, textureSize, textureSize, true)
        
                    -- 缓存初始创建的Blank Item
                    local key = self.itemSlot.GetKeyByPos(spaceX, spaceY, spaceIndex)
                    if self._allBlankItemInsID[key] == nil then
                        self._allBlankItemInsID[key] = instanceID
                    end

                    if self._allBlankItem[key] == nil then
                        self._allBlankItem[key] = commonBlankItem
                    end
                end
            end

        end
    end

    self:_DisableBlankItem()
end

function InvSlotView:_CreateTilingBlankItemInInvTab(space, spaceIndex)
    if not IsHD() then
        return
    end

    local spaceLength = space.length
    local spaceHeight = space.height

    -- 剔除8*200这个特异数
    if spaceLength == 8 and spaceHeight == 200 then
        loginfo("InvSlotView:_CreateTilingBlankItem: Culling Speical Num 8*200")
        return
    end

    -- 只在每列的第一列生成格子
    for j = 1, spaceHeight do
        local spaceX = 0
        local spaceY = j - 1  -- 只生成第一列

        local textureSize = CommonSlotView.DEFAULT_TEXTURE_SIZE

        local locX = space.startX + spaceX * textureSize
        local locY = space.startY + spaceY * textureSize

        local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonBlankItemTemplate, self._cellsContainer)

        if uiIns then
            local commonBlankItem = getfromweak(uiIns)

            if commonBlankItem then
                -- loginfo("yanmingjing:_CreateTilingBlankItem: Create Blank Item: spaceIndex: ",spaceIndex)
                commonBlankItem:Init(self.itemSlot, spaceIndex, spaceX, spaceY, self, self.bShownMask)
                self:SetViewByAbsPos(commonBlankItem, locX, locY, textureSize, textureSize, true)
                logwarning("yanmingjing:_CreateTilingBlankItem: Create Blank Item: spaceIndex: ",spaceIndex," spaceX: ",spaceX," spaceY: ",spaceY," instanceID: ",instanceID)
                logwarning("yanmingjing:_CreateTilingBlankItem: Create Blank Item: locX: ",locX," locY: ",locY," textureSize: ",textureSize)
                -- 缓存初始创建的Blank Item
                local key = self.itemSlot.GetKeyByPos(spaceX, spaceY, spaceIndex)
                if self._firstRowBlankItemInsID[key] == nil then
                    self._firstRowBlankItemInsID[key] = instanceID
                end

                if self._firstRowBlankItem[key] == nil then
                    self._firstRowBlankItem[key] = commonBlankItem
                end
            end
        end
    end

    self:_DisableFirstRowBlankItem()
end

local navStartSpaceIndex = nil
-- 导航开始时创建空白导航格，便于功能开发先和拖拽过程中的分开写
-- 这个函数意味着实际上的Dpad拖拽导航开始
function InvSlotView:AddNavBlankItem(itemView)
    -- 如果不在仓库中，则不创建四向导航格
    -- //TODO 替换成全局事件，通知所有slot
    Module.Inventory.Config.Events.evtUpdateAllBlankItemVisibility:Invoke(true)
    if self.itemSlot.SlotType < ESlotType.DepositoryStart or self.itemSlot.SlotType > ESlotType.DepositoryEnd then
        return
    end
    if not WidgetUtil.IsGamepad() then
        return
    end
    if type(itemView) ~= "table" then
        return
    end

    local item = itemView.item
    ---@type ItemSlot
    local slot = item.InSlot
    local loc = slot:GetItemLocation(item)
    local x, y, index = loc.X, loc.Y, loc.SubIndex
    local textureSize = CommonSlotView.DEFAULT_TEXTURE_SIZE

    -- Define directions to process: left, right, top, bottom
    local directions = {
        {name = "left", dx = -1, dy = 0},
        {name = "right", dx = 1, dy = 0},
        {name = "top", dx = 0, dy = -1},
        {name = "bottom", dx = 0, dy = 1}
    }

    -- 聚焦到medium对应的位置，放置按下Y后走焦歪掉
    local originalFocusPositionItem = nil
    for _, dir in ipairs(directions) do
        local absPosX, absPosY = slot:GetAbsLocation(index, x + dir.dx, y + dir.dy)
        loginfo(string.format("yanmingjing:AddBlankNavTarget %s: locX: %d, locY: %d", 
            dir.name, absPosX, absPosY))

        local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonBlankItemTemplate, self._cellsContainer)

        if uiIns then
            local commonBlankItem = getfromweak(uiIns)
            if commonBlankItem then
                commonBlankItem:Init(self.itemSlot, index, x + dir.dx, y + dir.dy, self, false)
                self:SetViewByAbsPos(commonBlankItem, absPosX, absPosY, textureSize, textureSize, false)
                commonBlankItem:SetPositionZ(9999)
                logwarning(string.format("yanmingjing:_CreateTilingBlankItem: Create %s Blank Item: spaceIndex: %d, spaceX: %d, spaceY: %d, instanceID: %d",
                    dir.name, index, x + dir.dx, y + dir.dy, instanceID))
                logwarning(string.format("yanmingjing:_CreateTilingBlankItem: Create %s Blank Item: locX: %d, locY: %d, textureSize: %d",
                    dir.name, absPosX, absPosY, textureSize))

                -- Cache created Blank Item
                local key = self.itemSlot.GetKeyByPos(x + dir.dx, y + dir.dy, index)
                if self._NavBlankItemInsID[key] == nil then
                    self._NavBlankItemInsID[key] = instanceID
                end

                if self._NavBlankItem[key] == nil then
                    self._NavBlankItem[key] = commonBlankItem
                end

                -- if dir.name == "medium" then
                --     originalFocusPositionItem = commonBlankItem
                -- end
                commonBlankItem:UpdateVisibility(false)
            end
        end
    end

    -- WidgetUtil.SetUserFocusToWidget(originalFocusPositionItem, true)

    -- for _,localItemView in pairs(self._allItemViews) do
    --     -- localItemView:SetIsFocusable(false)
    --     localItemView:SetCppValue("bIsFocusable", false)
    -- end 

    -- local key = self.itemSlot.GetKeyByPos(x + 1, y, index)
    -- WidgetUtil.SetUserFocusToWidget(self._NavBlankItem[key], true) 
    -- Initialize navigation manager if needed
    -- if self.navMgr == nil then
    --     self.navMgr = UGPUINavigationManager.Get(GetGameInstance())
    -- end
    -- if self.navMgr then
    --     self._hNavigationChangedFocus = self.navMgr.OnNavigationChangedFocusEvent:Add(self._OnNavigationChangedFocus, self)
    -- end
end

-- 手柄拖拽过程中添加导航格
function InvSlotView:AddNavBlankItemOnDragging(touchLogicX, touchLogicY, index)
    self:RemoveNavBlankItemOnDragging()
    if not WidgetUtil.IsGamepad() then
        return
    end
    -- 如果不在仓库中，则不创建四向导航格
    if self.itemSlot.SlotType < ESlotType.DepositoryStart or self.itemSlot.SlotType > ESlotType.DepositoryEnd then
        return
    end
    local textureSize = CommonSlotView.DEFAULT_TEXTURE_SIZE

    -- Define directions to process: left, right, top, bottom
    local directions = {
        {name = "left", dx = -1, dy = 0},
        {name = "right", dx = 1, dy = 0},
        {name = "top", dx = 0, dy = -1},
        {name = "bottom", dx = 0, dy = 1}
    }

    local x = touchLogicX
    local y = touchLogicY

    for _, dir in ipairs(directions) do
        if x + dir.dx >= 0 and x + dir.dx < self.itemSlot.Length and y + dir.dy >= 0 and y + dir.dy < self.itemSlot.Width then
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.IVCommonBlankItemTemplate, self._cellsContainer)
            
            if uiIns then
                local commonBlankItem = getfromweak(uiIns)
            
                local absPosX, absPosY = self.itemSlot:GetAbsLocation(index, x + dir.dx, y + dir.dy)
                
                
                    if commonBlankItem then
                        commonBlankItem:Init(self.itemSlot, index, x + dir.dx, y + dir.dy, self, false)
                        self:SetViewByAbsPos(commonBlankItem, absPosX, absPosY, textureSize, textureSize, false)
                        commonBlankItem:SetPositionZ(9999)
                        
                        logwarning(string.format("yanmingjing:_CreateTilingBlankItem: Create %s Blank Item: spaceIndex: %d, spaceX: %d, spaceY: %d, instanceID: %d",
                            dir.name, index, x + dir.dx, y + dir.dy, instanceID))
                        logwarning(string.format("yanmingjing:_CreateTilingBlankItem: Create %s Blank Item: locX: %d, locY: %d, textureSize: %d",
                            dir.name, absPosX, absPosY, textureSize))
        
                        -- Cache created Blank Item
                        local key = self.itemSlot.GetKeyByPos(x + dir.dx, y + dir.dy, index)
                        if self._NavBlankItemInsID[key] == nil then
                            self._NavBlankItemInsID[key] = instanceID
                        end
        
                        if self._NavBlankItem[key] == nil then
                            self._NavBlankItem[key] = commonBlankItem
                        end
        
                        commonBlankItem:UpdateVisibility(false)
                    end

            end
        end
    end
end

-- 拖拽更新时，移除旧的导航格
-- 实际导航完成的时机比焦点态转移完成要晚，目前没有导航完成的回调。不多加一层buffer的话，会导致无限向同个方向导航。
function InvSlotView:RemoveNavBlankItemOnDragging()
    if self._LastNavBlankItemInsID ~=nil then
        for key, value in pairs(self._LastNavBlankItemInsID) do
            local weakUIIns = Facade.UIManager:RemoveSubUI(self, UIName2ID.IVCommonBlankItemTemplate, value)
            local blankItemIns = getfromweak(weakUIIns)
            if blankItemIns and blankItemIns.ResetItemInfo then
                blankItemIns:ResetItemInfo()
            end
        end
        self._LastNavBlankItemInsID = self._NavBlankItemInsID
        self._LastNavBlankItem = self._NavBlankItem
        self._NavBlankItemInsID = {}
        self._NavBlankItem = {}
    end
end

function InvSlotView:RemoveNavBlankItemOnDragEnd()
    loginfo("yanmingjing:RemoveNavBlankItemOnDragEnd")
    -- 设置离开Y模式
    if self._LastNavBlankItemInsID ~=nil then
        for key, value in pairs(self._LastNavBlankItemInsID) do
            local weakUIIns = Facade.UIManager:RemoveSubUI(self, UIName2ID.IVCommonBlankItemTemplate, value)
            local blankItemIns = getfromweak(weakUIIns)
            if blankItemIns and blankItemIns.ResetItemInfo then
                blankItemIns:ResetItemInfo()
            end
        end
    end
    if self._NavBlankItemInsID ~=nil then
        for key, value in pairs(self._NavBlankItemInsID) do
            local weakUIIns = Facade.UIManager:RemoveSubUI(self, UIName2ID.IVCommonBlankItemTemplate, value)
            local blankItemIns = getfromweak(weakUIIns)
            if blankItemIns and blankItemIns.ResetItemInfo then
                blankItemIns:ResetItemInfo()
            end
        end
    end

    self._LastNavBlankItemInsID = {}
    self._LastNavBlankItem = {}
    self._NavBlankItemInsID = {}
    self._NavBlankItem = {}
end

function InvSlotView:_OnIVMouseEnter(itemView)
    if itemView == nil then
        return
    end
    local item = itemView.item
    local slot = item.InSlot
    if slot == nil then
        return
    end
    Module.Inventory.Field:SetNavStartItemview(itemView)
end

function InvSlotView:_OnIVMouseLeave(itemView)
    Module.Inventory.Field:SetNavStartItemview(nil)
end

-- function InvSlotView:_OnItemViewAdded(itemView)
--     local navTarget = Module.Inventory.Field:GetNavTargetItem()
--     if navTarget == nil then
--         return
--     end
--     local item = itemView.item
--     if navTarget == item then
--         WidgetUtil.SetUserFocusToWidget(itemView, true)
--     end
-- end

--END MODIFICATION
function InvSlotView:_RefreshBlankItem()
    if not IsHD() then
        return
    end

    if not self._allBlankItem then
        return
    end

    -- for key, value in pairs(self._allBlankItem) do
    --     value:UpdateVisibility(self.bShownMask)
    -- end
end

function InvSlotView:_FreeBlankItem()
    if not IsHD() then
        return
    end

    -- 移除已有空白对象
    if self._allBlankItemInsID ~= nil then
        for key, value in pairs(self._allBlankItemInsID) do
            local weakUIIns = Facade.UIManager:RemoveSubUI(self, UIName2ID.IVCommonBlankItemTemplate, value)
            local blankItemIns = getfromweak(weakUIIns)
            if blankItemIns and blankItemIns.ResetItemInfo then
                blankItemIns:ResetItemInfo()
            end
        end
        self._allBlankItemInsID = {}
        self._allBlankItem = {}
    end
end

function InvSlotView:_FreeFirstRowBlankItem()
    if not IsHD() then
        return
    end
    self._firstRowBlankItem = {}
        self._firstRowBlankItemInsID = {}

    -- 移除第一列空白对象
    if self._firstRowBlankItemInsID ~= nil then
        for key, value in pairs(self._firstRowBlankItemInsID) do
            local weakUIIns = Facade.UIManager:RemoveSubUI(self, UIName2ID.IVCommonBlankItemTemplate, value)
            local blankItemIns = getfromweak(weakUIIns)
            if blankItemIns and blankItemIns.ResetItemInfo then
                blankItemIns:ResetItemInfo()
            end
        end
        self._firstRowBlankItemInsID = {}
        self._firstRowBlankItem = {}
    end
end

-- 启用统一填满的空白格的导航性
function InvSlotView:_EnableBlankItem()
    if not IsHD() then
        return
    end
    if self._allBlankItem ~= nil then
        for key, value in pairs(self._allBlankItem) do
            value:UpdateVisibility(false)
        end
    end
end
-- 去除统一填满的空白格的导航性
function InvSlotView:_DisableBlankItem()
    if not IsHD() then
        return
    end
    if self._allBlankItem ~= nil then
        for key, value in pairs(self._allBlankItem) do
            value:UpdateVisibility(true)
        end
    end
end
-- 启用第一列空白格的导航性
function InvSlotView:_EnableFirstRowBlankItem()
    if not IsHD() then
        return
    end
    if self._firstRowBlankItem ~= nil then
        for key, value in pairs(self._firstRowBlankItem) do
            value:UpdateVisibility(false)
        end
    end
end
-- 去除第一列空白格的导航性
function InvSlotView:_DisableFirstRowBlankItem()
    if not IsHD() then
        return
    end
    if self._firstRowBlankItem ~= nil then
        for key, value in pairs(self._firstRowBlankItem) do
            value:UpdateVisibility(true)
        end
    end
end

function InvSlotView:_OnUpdateAllBlankItemVisibility(bShown,itemView)
    if not IsHD() then
        return
    end
    if bShown then
        if itemView then
            if itemView.item.InSlot:GetContainerSlotType() == self.itemSlot.SlotType and itemView.item.InSlot:GetSlotGroup() == self.itemSlot:GetSlotGroup()  then
                self:_DisableBlankItem()
            else
                self:_EnableBlankItem()           
            end
        else
        self:_EnableBlankItem()
        end
    else
        self:_DisableBlankItem()
    end
end

function InvSlotView:_OnUpdateFirstRowBlankItemVisibility(bShown)
    if not IsHD() then
        return
    end
    if bShown then
        self:_EnableFirstRowBlankItem()
    else
        self:_DisableFirstRowBlankItem()
    end
end

function InvSlotView:TrySetFocusToBlankItem(ItemLoc)
    if not IsHD() then
        return 
    end

    if ItemLoc == nil or WidgetUtil.IsGamepad() == false then
        return 
    end

    if ItemLoc.X and ItemLoc.Y and ItemLoc.SubIndex then
        local key = self.itemSlot.GetKeyByPos(ItemLoc.X, ItemLoc.Y, ItemLoc.SubIndex)
        if self._allBlankItem[key] and self._allBlankItem[key].SetFocus then
            WidgetUtil.SetUserFocusToWidget(self._allBlankItem[key], true)
        end
    end
end

function InvSlotView:GetFirstBlankItem()
    local key = self.itemSlot.GetKeyByPos(0, 0, 1)
    return self._allBlankItem[key]
end

-- 直接拾取物品后再打开背包需要刷新空白格子
function InvSlotView:RefreshBlankItem()
    if IsHD() then
        self:_RefreshBlankItem()
    end
end
--END MODIFICATION

function InvSlotView:_OnHidePreviousInventory(GamePickupType)
    if self.itemSlot then
        if GamePickupType == EGamePickupType.NearbyDeadBody then
            if self.itemSlot:GetSlotGroup() == ESlotGroup.DeadBody then
                if self.itemSlot.SlotType == ESlotType.ChestHangingContainer or  self.itemSlot.SlotType == ESlotType.BagContainer then
                    self:RefreshBgGrid()
                else
                    self:RefreshView()
                end
            end
        elseif GamePickupType == EGamePickupType.DropContainer or GamePickupType == EGamePickupType.SceneBox then
            if self.itemSlot:GetSlotGroup() == ESlotGroup.Nearby and self.itemSlot.SlotType ~= ESlotType.NearbyPickups then
                self:RefreshBgGrid()
            end
        end
    end
end


return InvSlotView
