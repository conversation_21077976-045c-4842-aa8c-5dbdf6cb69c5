--cs_matchroom.protoencode&decode functions.
function pb.pb_CSPrepareJoinMatchNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPrepareJoinMatchNtf) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __time_stamp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __time_stamp ~= 0 then tb.time_stamp = __time_stamp end
    local __player_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __team_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __random_seed = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __random_seed ~= 0 then tb.random_seed = __random_seed end
    local __player_idx = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __player_idx ~= 0 then tb.player_idx = __player_idx end
    local __game_mode = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __iris_opening_event_id = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __iris_opening_event_id ~= 0 then tb.iris_opening_event_id = __iris_opening_event_id end
    local __iris_event_course_id = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __iris_event_course_id ~= 0 then tb.iris_event_course_id = __iris_event_course_id end
    tb.room_member_infos = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.room_member_infos[k] = pb.pb_MatchRoomTeamMemberInfoDecode(v)
    end
    local __match_mode_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    tb.box_event = decoder:geti32ary(12)
    local __spawn_point_config_id = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __spawn_point_config_id ~= 0 then tb.spawn_point_config_id = __spawn_point_config_id end
    local __tod_weather_id = decoder:getu64(14)
    if not PB_USE_DEFAULT_TABLE or __tod_weather_id ~= 0 then tb.tod_weather_id = __tod_weather_id end
    tb.iris_event_ids = decoder:getu64ary(15)
    local __is_observer = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __is_observer ~= false then tb.is_observer = __is_observer end
    local __observer_id = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __observer_id ~= 0 then tb.observer_id = __observer_id end
    return tb
end

function pb.pb_CSPrepareJoinMatchNtfEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.time_stamp) then    encoder:addi64(2, tb.time_stamp)    end
    if(tb.player_id) then    encoder:addu64(3, tb.player_id)    end
    if(tb.team_id) then    encoder:addu64(4, tb.team_id)    end
    if(tb.random_seed) then    encoder:addi64(5, tb.random_seed)    end
    if(tb.player_idx) then    encoder:addi32(6, tb.player_idx)    end
    if(tb.game_mode) then    encoder:addu32(7, tb.game_mode)    end
    if(tb.iris_opening_event_id) then    encoder:addu32(8, tb.iris_opening_event_id)    end
    if(tb.iris_event_course_id) then    encoder:addu32(9, tb.iris_event_course_id)    end
    if(tb.room_member_infos) then
        for i=1,#(tb.room_member_infos) do
            pb.pb_MatchRoomTeamMemberInfoEncode(tb.room_member_infos[i], encoder:addsubmsg(10))
        end
    end
    if(tb.match_mode_id) then    encoder:addu64(11, tb.match_mode_id)    end
    if(tb.box_event) then    encoder:addi32(12, tb.box_event)    end
    if(tb.spawn_point_config_id) then    encoder:addi32(13, tb.spawn_point_config_id)    end
    if(tb.tod_weather_id) then    encoder:addu64(14, tb.tod_weather_id)    end
    if(tb.iris_event_ids) then    encoder:addu64(15, tb.iris_event_ids)    end
    if(tb.is_observer) then    encoder:addbool(16, tb.is_observer)    end
    if(tb.observer_id) then    encoder:addu32(17, tb.observer_id)    end
end

function pb.pb_PlayerJoinMatchNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerJoinMatchNtf) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __ds_textual_ip = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    local __ds_port = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __map_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __result = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __is_half_join = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_half_join ~= false then tb.is_half_join = __is_half_join end
    tb.player = pb.pb_MatchPlayerInfoDecode(decoder:getsubmsg(8))
    local __ds_domain = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __ds_domain ~= "" then tb.ds_domain = __ds_domain end
    tb.ds_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.ds_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    return tb
end

function pb.pb_PlayerJoinMatchNtfEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.ds_textual_ip) then    encoder:addstr(10, tb.ds_textual_ip)    end
    if(tb.ds_port) then    encoder:addu32(3, tb.ds_port)    end
    if(tb.map_id) then    encoder:addu32(5, tb.map_id)    end
    if(tb.result) then    encoder:addi32(6, tb.result)    end
    if(tb.is_half_join) then    encoder:addbool(7, tb.is_half_join)    end
    if(tb.player) then    pb.pb_MatchPlayerInfoEncode(tb.player, encoder:addsubmsg(8))    end
    if(tb.ds_domain) then    encoder:addstr(9, tb.ds_domain)    end
    if(tb.ds_ip_list) then
        for i=1,#(tb.ds_ip_list) do
            pb.pb_HostInfoEncode(tb.ds_ip_list[i], encoder:addsubmsg(11))
        end
    end
end

function pb.pb_CSMatchRoomCallNumeralNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomCallNumeralNtf) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __match_mode_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    local __check_gap = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __check_gap ~= 0 then tb.check_gap = __check_gap end
    return tb
end

function pb.pb_CSMatchRoomCallNumeralNtfEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.match_mode_id) then    encoder:addu64(3, tb.match_mode_id)    end
    if(tb.check_gap) then    encoder:addi64(4, tb.check_gap)    end
end

function pb.pb_CSMatchRoomCheckCallNumeralReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomCheckCallNumeralReq) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __game_mode = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __match_mode_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    return tb
end

function pb.pb_CSMatchRoomCheckCallNumeralReqEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.game_mode) then    encoder:addu32(3, tb.game_mode)    end
    if(tb.match_mode_id) then    encoder:addu64(4, tb.match_mode_id)    end
end

function pb.pb_CSMatchRoomCheckCallNumeralResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomCheckCallNumeralRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.ntf = pb.pb_CSPlayerJoinMatchNtfDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSMatchRoomCheckCallNumeralResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.ntf) then    pb.pb_CSPlayerJoinMatchNtfEncode(tb.ntf, encoder:addsubmsg(2))    end
end

function pb.pb_CSPlayerJoinMatchNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPlayerJoinMatchNtf) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __ds_textual_ip = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    local __ds_port = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __map_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __result = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __is_half_join = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_half_join ~= false then tb.is_half_join = __is_half_join end
    tb.player = pb.pb_MatchPlayerInfoDecode(decoder:getsubmsg(8))
    local __ds_domain = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __ds_domain ~= "" then tb.ds_domain = __ds_domain end
    local __match_mode_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    tb.ds_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.ds_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    tb.xunyou_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.xunyou_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    local __enc_flag = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __enc_flag ~= 0 then tb.enc_flag = __enc_flag end
    return tb
end

function pb.pb_CSPlayerJoinMatchNtfEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.ds_textual_ip) then    encoder:addstr(10, tb.ds_textual_ip)    end
    if(tb.ds_port) then    encoder:addu32(3, tb.ds_port)    end
    if(tb.map_id) then    encoder:addu32(5, tb.map_id)    end
    if(tb.result) then    encoder:addi32(6, tb.result)    end
    if(tb.is_half_join) then    encoder:addbool(7, tb.is_half_join)    end
    if(tb.player) then    pb.pb_MatchPlayerInfoEncode(tb.player, encoder:addsubmsg(8))    end
    if(tb.ds_domain) then    encoder:addstr(9, tb.ds_domain)    end
    if(tb.match_mode_id) then    encoder:addu64(11, tb.match_mode_id)    end
    if(tb.ds_ip_list) then
        for i=1,#(tb.ds_ip_list) do
            pb.pb_HostInfoEncode(tb.ds_ip_list[i], encoder:addsubmsg(12))
        end
    end
    if(tb.xunyou_ip_list) then
        for i=1,#(tb.xunyou_ip_list) do
            pb.pb_HostInfoEncode(tb.xunyou_ip_list[i], encoder:addsubmsg(13))
        end
    end
    if(tb.enc_flag) then    encoder:addi64(14, tb.enc_flag)    end
end

function pb.pb_CSPlayerJoinSafehouseNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPlayerJoinSafehouseNtf) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __ds_textual_ip = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    local __map_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __ds_domain = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __ds_domain ~= "" then tb.ds_domain = __ds_domain end
    local __ds_token = decoder:getstr(24)
    if not PB_USE_DEFAULT_TABLE or __ds_token ~= "" then tb.ds_token = __ds_token end
    local __ds_port = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __sys_team_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __sys_team_id ~= 0 then tb.sys_team_id = __sys_team_id end
    tb.ds_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.ds_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    return tb
end

function pb.pb_CSPlayerJoinSafehouseNtfEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.ds_textual_ip) then    encoder:addstr(10, tb.ds_textual_ip)    end
    if(tb.map_id) then    encoder:addu32(5, tb.map_id)    end
    if(tb.ds_domain) then    encoder:addstr(9, tb.ds_domain)    end
    if(tb.ds_token) then    encoder:addstr(24, tb.ds_token)    end
    if(tb.ds_port) then    encoder:addu32(31, tb.ds_port)    end
    if(tb.sys_team_id) then    encoder:addu64(2, tb.sys_team_id)    end
    if(tb.ds_ip_list) then
        for i=1,#(tb.ds_ip_list) do
            pb.pb_HostInfoEncode(tb.ds_ip_list[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSMatchRoomMatchEndNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomMatchEndNtf) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __reason = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    return tb
end

function pb.pb_CSMatchRoomMatchEndNtfEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.reason) then    encoder:addu32(2, tb.reason)    end
end

function pb.pb_CSMatchRoomReconnectReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomReconnectReq) or {} 
    local __is_reconnect = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __is_reconnect ~= false then tb.is_reconnect = __is_reconnect end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSMatchRoomReconnectReqEncode(tb, encoder)
    if(tb.is_reconnect) then    encoder:addbool(1, tb.is_reconnect)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
end

function pb.pb_CSMatchRoomReconnectResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomReconnectRes) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __ds_textual_ip = decoder:getstr(16)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    local __ds_ip = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ds_ip ~= 0 then tb.ds_ip = __ds_ip end
    local __ds_port = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __result = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __ds_token = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __ds_token ~= "" then tb.ds_token = __ds_token end
    local __map_id = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __ds_room_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __ds_team_id = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __ds_team_id ~= 0 then tb.ds_team_id = __ds_team_id end
    local __ds_domian = decoder:getstr(15)
    if not PB_USE_DEFAULT_TABLE or __ds_domian ~= "" then tb.ds_domian = __ds_domian end
    local __ds_version = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __ds_version ~= "" then tb.ds_version = __ds_version end
    tb.ds_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(17)) do
        tb.ds_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    local __is_observer = decoder:getbool(18)
    if not PB_USE_DEFAULT_TABLE or __is_observer ~= false then tb.is_observer = __is_observer end
    tb.xunyou_ip_list = {}
    for k,v in pairs(decoder:getsubmsgary(19)) do
        tb.xunyou_ip_list[k] = pb.pb_HostInfoDecode(v)
    end
    local __enc_flag = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __enc_flag ~= 0 then tb.enc_flag = __enc_flag end
    return tb
end

function pb.pb_CSMatchRoomReconnectResEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.ds_textual_ip) then    encoder:addstr(16, tb.ds_textual_ip)    end
    if(tb.ds_ip) then    encoder:addu32(3, tb.ds_ip)    end
    if(tb.ds_port) then    encoder:addu32(4, tb.ds_port)    end
    if(tb.result) then    encoder:addi32(10, tb.result)    end
    if(tb.ds_token) then    encoder:addstr(11, tb.ds_token)    end
    if(tb.map_id) then    encoder:addu32(12, tb.map_id)    end
    if(tb.ds_room_id) then    encoder:addu64(13, tb.ds_room_id)    end
    if(tb.ds_team_id) then    encoder:addu32(14, tb.ds_team_id)    end
    if(tb.ds_domian) then    encoder:addstr(15, tb.ds_domian)    end
    if(tb.ds_version) then    encoder:addstr(2, tb.ds_version)    end
    if(tb.ds_ip_list) then
        for i=1,#(tb.ds_ip_list) do
            pb.pb_HostInfoEncode(tb.ds_ip_list[i], encoder:addsubmsg(17))
        end
    end
    if(tb.is_observer) then    encoder:addbool(18, tb.is_observer)    end
    if(tb.xunyou_ip_list) then
        for i=1,#(tb.xunyou_ip_list) do
            pb.pb_HostInfoEncode(tb.xunyou_ip_list[i], encoder:addsubmsg(19))
        end
    end
    if(tb.enc_flag) then    encoder:addi64(20, tb.enc_flag)    end
end

function pb.pb_CSMatchRoomGetTdmRoomArmedForceTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomGetTdmRoomArmedForceTReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSMatchRoomGetTdmRoomArmedForceTReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
end

function pb.pb_CSMatchRoomGetTdmRoomArmedForceTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomGetTdmRoomArmedForceTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __room_start_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __room_start_time ~= 0 then tb.room_start_time = __room_start_time end
    local __stage_end_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __stage_end_time ~= 0 then tb.stage_end_time = __stage_end_time end
    tb.player_armed_force_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.player_armed_force_array[k] = pb.pb_CSMatchRoomTdmArmedForceShowInfoDecode(v)
    end
    return tb
end

function pb.pb_CSMatchRoomGetTdmRoomArmedForceTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.room_start_time) then    encoder:addi64(3, tb.room_start_time)    end
    if(tb.stage_end_time) then    encoder:addi64(4, tb.stage_end_time)    end
    if(tb.player_armed_force_array) then
        for i=1,#(tb.player_armed_force_array) do
            pb.pb_CSMatchRoomTdmArmedForceShowInfoEncode(tb.player_armed_force_array[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_CSMatchRoomTdmArmedForceShowInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomTdmArmedForceShowInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __armedforce_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    tb.profile = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(3))
    local __hero_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    return tb
end

function pb.pb_CSMatchRoomTdmArmedForceShowInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.armedforce_id) then    encoder:addu64(2, tb.armedforce_id)    end
    if(tb.profile) then    pb.pb_PlayerSimpleInfoEncode(tb.profile, encoder:addsubmsg(3))    end
    if(tb.hero_id) then    encoder:addu64(4, tb.hero_id)    end
end

function pb.pb_CSMatchRoomSetTdmRoomArmedForceTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSetTdmRoomArmedForceTReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __select_bag_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __select_bag_id ~= 0 then tb.select_bag_id = __select_bag_id end
    return tb
end

function pb.pb_CSMatchRoomSetTdmRoomArmedForceTReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.select_bag_id) then    encoder:addu32(2, tb.select_bag_id)    end
end

function pb.pb_CSMatchRoomSetTdmRoomArmedForceTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSetTdmRoomArmedForceTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSMatchRoomSetTdmRoomArmedForceTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSMatchRoomTdmChangeArmedForceNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomTdmChangeArmedForceNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    tb.player_armed_force = pb.pb_CSMatchRoomTdmArmedForceShowInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSMatchRoomTdmChangeArmedForceNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_armed_force) then    pb.pb_CSMatchRoomTdmArmedForceShowInfoEncode(tb.player_armed_force, encoder:addsubmsg(2))    end
end

function pb.pb_CSMatchRoomGetSolRoomTeamTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomGetSolRoomTeamTReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSMatchRoomGetSolRoomTeamTReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
end

function pb.pb_CSMatchRoomGetSolRoomTeamTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomGetSolRoomTeamTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __room_start_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __room_start_time ~= 0 then tb.room_start_time = __room_start_time end
    local __stage_end_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __stage_end_time ~= 0 then tb.stage_end_time = __stage_end_time end
    tb.player_info_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.player_info_array[k] = pb.pb_SolRoomTeamShowInfoDecode(v)
    end
    return tb
end

function pb.pb_CSMatchRoomGetSolRoomTeamTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.room_start_time) then    encoder:addi64(3, tb.room_start_time)    end
    if(tb.stage_end_time) then    encoder:addi64(4, tb.stage_end_time)    end
    if(tb.player_info_array) then
        for i=1,#(tb.player_info_array) do
            pb.pb_SolRoomTeamShowInfoEncode(tb.player_info_array[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_CSMatchRoomSetSolRoomHeroTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSetSolRoomHeroTReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __hero_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    tb.new_fashions = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.new_fashions[k] = pb.pb_HeroFashionDecode(v)
    end
    return tb
end

function pb.pb_CSMatchRoomSetSolRoomHeroTReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.hero_id) then    encoder:addu64(2, tb.hero_id)    end
    if(tb.new_fashions) then
        for i=1,#(tb.new_fashions) do
            pb.pb_HeroFashionEncode(tb.new_fashions[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSMatchRoomSetSolRoomHeroTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSetSolRoomHeroTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSMatchRoomSetSolRoomHeroTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSMatchRoomSolChangeHeroNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSolChangeHeroNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.hero_info = pb.pb_CSHeroDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_CSMatchRoomSolChangeHeroNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.hero_info) then    pb.pb_CSHeroEncode(tb.hero_info, encoder:addsubmsg(3))    end
end

function pb.pb_SolRoomTeamShowInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SolRoomTeamShowInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.hero_info = pb.pb_CSHeroDecode(decoder:getsubmsg(2))
    local __is_ready = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_ready ~= false then tb.is_ready = __is_ready end
    local __nick_name = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __nick_name ~= "" then tb.nick_name = __nick_name end
    local __player_idx = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __player_idx ~= 0 then tb.player_idx = __player_idx end
    local __title = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __title ~= 0 then tb.title = __title end
    local __rank_title_adcode = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __rank_title_adcode ~= 0 then tb.rank_title_adcode = __rank_title_adcode end
    local __rank_title_rank_no = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __rank_title_rank_no ~= 0 then tb.rank_title_rank_no = __rank_title_rank_no end
    local __plat_id = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __is_bot = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __is_bot ~= false then tb.is_bot = __is_bot end
    local __rand_hero_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __rand_hero_time ~= 0 then tb.rand_hero_time = __rand_hero_time end
    tb.rand_hero_info = pb.pb_CSHeroDecode(decoder:getsubmsg(8))
    return tb
end

function pb.pb_SolRoomTeamShowInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.hero_info) then    pb.pb_CSHeroEncode(tb.hero_info, encoder:addsubmsg(2))    end
    if(tb.is_ready) then    encoder:addbool(3, tb.is_ready)    end
    if(tb.nick_name) then    encoder:addstr(4, tb.nick_name)    end
    if(tb.player_idx) then    encoder:addi32(5, tb.player_idx)    end
    if(tb.title) then    encoder:addu64(9, tb.title)    end
    if(tb.rank_title_adcode) then    encoder:addu32(10, tb.rank_title_adcode)    end
    if(tb.rank_title_rank_no) then    encoder:addi64(11, tb.rank_title_rank_no)    end
    if(tb.plat_id) then    encoder:addi32(12, tb.plat_id)    end
    if(tb.is_bot) then    encoder:addbool(6, tb.is_bot)    end
    if(tb.rand_hero_time) then    encoder:addi64(7, tb.rand_hero_time)    end
    if(tb.rand_hero_info) then    pb.pb_CSHeroEncode(tb.rand_hero_info, encoder:addsubmsg(8))    end
end

function pb.pb_CSMatchRoomSolReadyTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSolReadyTReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSMatchRoomSolReadyTReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
end

function pb.pb_CSMatchRoomSolReadyTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSolReadyTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSMatchRoomSolReadyTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSMatchRoomSolReadyNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSolReadyNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    return tb
end

function pb.pb_CSMatchRoomSolReadyNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
end

function pb.pb_CSMatchRoomStartMatchTglogTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomStartMatchTglogTReq) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __sec_report_data = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __sec_report_data ~= "" then tb.sec_report_data = __sec_report_data end
    local __client_start_time = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __client_start_time ~= "" then tb.client_start_time = __client_start_time end
    return tb
end

function pb.pb_CSMatchRoomStartMatchTglogTReqEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.sec_report_data) then    encoder:addstr(2, tb.sec_report_data)    end
    if(tb.client_start_time) then    encoder:addstr(3, tb.client_start_time)    end
end

function pb.pb_CSMatchRoomStartMatchTglogTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomStartMatchTglogTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSMatchRoomStartMatchTglogTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSPlayerJoinDsStatusReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPlayerJoinDsStatusReq) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __match_mode_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    local __status = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __status ~= 0 then tb.status = __status end
    local __fail_reason = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __fail_reason ~= 0 then tb.fail_reason = __fail_reason end
    local __desc = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __desc ~= "" then tb.desc = __desc end
    return tb
end

function pb.pb_CSPlayerJoinDsStatusReqEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.match_mode_id) then    encoder:addu64(2, tb.match_mode_id)    end
    if(tb.status) then    encoder:addi64(3, tb.status)    end
    if(tb.fail_reason) then    encoder:addi64(4, tb.fail_reason)    end
    if(tb.desc) then    encoder:addstr(5, tb.desc)    end
end

function pb.pb_CSPlayerJoinDsStatusResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPlayerJoinDsStatusRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSPlayerJoinDsStatusResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSMatchRoomSyncMemberOptionReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSyncMemberOptionReq) or {} 
    local __option_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __option_type ~= 0 then tb.option_type = __option_type end
    local __ds_room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __data = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __data ~= "" then tb.data = __data end
    return tb
end

function pb.pb_CSMatchRoomSyncMemberOptionReqEncode(tb, encoder)
    if(tb.option_type) then    encoder:addi32(1, tb.option_type)    end
    if(tb.ds_room_id) then    encoder:addu64(2, tb.ds_room_id)    end
    if(tb.data) then    encoder:addbuffer(3, tb.data)    end
end

function pb.pb_CSMatchRoomSyncMemberOptionResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSyncMemberOptionRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSMatchRoomSyncMemberOptionResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CSMatchRoomSyncMemberOptionNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSyncMemberOptionNtf) or {} 
    local __option_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __option_type ~= 0 then tb.option_type = __option_type end
    local __ds_room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __member_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __member_id ~= 0 then tb.member_id = __member_id end
    local __data = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __data ~= "" then tb.data = __data end
    return tb
end

function pb.pb_CSMatchRoomSyncMemberOptionNtfEncode(tb, encoder)
    if(tb.option_type) then    encoder:addi32(1, tb.option_type)    end
    if(tb.ds_room_id) then    encoder:addu64(2, tb.ds_room_id)    end
    if(tb.member_id) then    encoder:addu64(3, tb.member_id)    end
    if(tb.data) then    encoder:addbuffer(4, tb.data)    end
end

function pb.pb_CSMatchRoomPickStageReconnectReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomPickStageReconnectReq) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    return tb
end

function pb.pb_CSMatchRoomPickStageReconnectReqEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
end

function pb.pb_CSMatchRoomPickStageReconnectResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomPickStageReconnectRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __ds_room_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __time_stamp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __time_stamp ~= 0 then tb.time_stamp = __time_stamp end
    local __player_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __team_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __random_seed = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __random_seed ~= 0 then tb.random_seed = __random_seed end
    local __player_idx = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __player_idx ~= 0 then tb.player_idx = __player_idx end
    local __game_mode = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __iris_opening_event_id = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __iris_opening_event_id ~= 0 then tb.iris_opening_event_id = __iris_opening_event_id end
    local __iris_event_course_id = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __iris_event_course_id ~= 0 then tb.iris_event_course_id = __iris_event_course_id end
    tb.room_member_infos = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.room_member_infos[k] = pb.pb_MatchRoomTeamMemberInfoDecode(v)
    end
    local __match_mode_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    tb.box_event = decoder:geti32ary(12)
    local __spawn_point_config_id = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __spawn_point_config_id ~= 0 then tb.spawn_point_config_id = __spawn_point_config_id end
    tb.iris_event_ids = decoder:getu64ary(15)
    return tb
end

function pb.pb_CSMatchRoomPickStageReconnectResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.ds_room_id) then    encoder:addu64(13, tb.ds_room_id)    end
    if(tb.time_stamp) then    encoder:addi64(2, tb.time_stamp)    end
    if(tb.player_id) then    encoder:addu64(3, tb.player_id)    end
    if(tb.team_id) then    encoder:addu64(4, tb.team_id)    end
    if(tb.random_seed) then    encoder:addi64(5, tb.random_seed)    end
    if(tb.player_idx) then    encoder:addi32(6, tb.player_idx)    end
    if(tb.game_mode) then    encoder:addu32(7, tb.game_mode)    end
    if(tb.iris_opening_event_id) then    encoder:addu32(8, tb.iris_opening_event_id)    end
    if(tb.iris_event_course_id) then    encoder:addu32(9, tb.iris_event_course_id)    end
    if(tb.room_member_infos) then
        for i=1,#(tb.room_member_infos) do
            pb.pb_MatchRoomTeamMemberInfoEncode(tb.room_member_infos[i], encoder:addsubmsg(10))
        end
    end
    if(tb.match_mode_id) then    encoder:addu64(11, tb.match_mode_id)    end
    if(tb.box_event) then    encoder:addi32(12, tb.box_event)    end
    if(tb.spawn_point_config_id) then    encoder:addi32(14, tb.spawn_point_config_id)    end
    if(tb.iris_event_ids) then    encoder:addu64(15, tb.iris_event_ids)    end
end

function pb.pb_CSMatchRoomGetPlayStationMatchIdTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomGetPlayStationMatchIdTReq) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    return tb
end

function pb.pb_CSMatchRoomGetPlayStationMatchIdTReqEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
end

function pb.pb_CSMatchRoomGetPlayStationMatchIdTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomGetPlayStationMatchIdTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __ds_room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __match_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __match_id ~= "" then tb.match_id = __match_id end
    tb.players = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.players[k] = pb.pb_MatchRoomPlayerPlatInfoDecode(v)
    end
    return tb
end

function pb.pb_CSMatchRoomGetPlayStationMatchIdTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.ds_room_id) then    encoder:addu64(2, tb.ds_room_id)    end
    if(tb.match_id) then    encoder:addstr(3, tb.match_id)    end
    if(tb.players) then
        for i=1,#(tb.players) do
            pb.pb_MatchRoomPlayerPlatInfoEncode(tb.players[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_MatchRoomPlayerPlatInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchRoomPlayerPlatInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __player_nick = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __player_nick ~= "" then tb.player_nick = __player_nick end
    local __plat_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __camp = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __camp ~= 0 then tb.camp = __camp end
    return tb
end

function pb.pb_MatchRoomPlayerPlatInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.player_nick) then    encoder:addstr(2, tb.player_nick)    end
    if(tb.plat_id) then    encoder:addu32(3, tb.plat_id)    end
    if(tb.camp) then    encoder:addu32(4, tb.camp)    end
end

function pb.pb_CSMatchRoomSetPlayStationMatchIdTReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSetPlayStationMatchIdTReq) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    local __match_id = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __match_id ~= "" then tb.match_id = __match_id end
    return tb
end

function pb.pb_CSMatchRoomSetPlayStationMatchIdTReqEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.match_id) then    encoder:addstr(2, tb.match_id)    end
end

function pb.pb_CSMatchRoomSetPlayStationMatchIdTResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSMatchRoomSetPlayStationMatchIdTRes) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSMatchRoomSetPlayStationMatchIdTResEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

