----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class MandelBrickGainPop : LuaUIBaseView
local RewardBaseView = require "DFM.Business.Module.RewardModule.UI.RewardBaseView"
local MandelBrickGainPop = ui("MandelBrickGainPop", RewardBaseView)
local EGPInputModeType = import "EGPInputModeType"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local RewardDetail = require "DFM.Business.Module.RewardModule.UI.RewardDetail"

function MandelBrickGainPop:Ctor()
    self._wtRewardDetail = self:Wnd("wtRewardDetail", RewardDetail)
    self._wtRewardDetail:BindJumpClick(self._OnSkipBtnClick, self)
    self._wtRewardDetail:SetShowSkipTxt(true)
    self._wtExpertIcon = self:Wnd("wtExpertIcon", UIImage)
    self._wtNicknameTxt = self:Wnd("wtNicknameTxt", UITextBlock)
    self._wtGainSourceTxt = self:Wnd("wtGainSourceTxt", UITextBlock)
    self._wtGainCountTxt = self:Wnd("wtGainCountTxt", UITextBlock)
    self._closeClickCount = -1
end


function MandelBrickGainPop:OnInitExtraData(mandelBrickId)
    self._mandelBrickId = mandelBrickId
end

function MandelBrickGainPop:OnOpen()
    self:HandleTransition(false)
    self:_AddListeners()
end

function MandelBrickGainPop:OnShowBegin()
    self:_RefreshWidget()
    Facade.SoundManager:PlayUIAudioEvent("UI_Common_Popup")
    self._wtRewardDetail:AddJumpInputAction()
end

function MandelBrickGainPop:OnClose()
    self:RemoveAllLuaEvent()
    Module.Settlement:OpenSettlementUI(self.UINavID)
    if self._bExecuteClose ~= true then
        self._bExecuteClose = true
        Module.Reward:ShowNextRewards()
    end
end

function MandelBrickGainPop:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

function MandelBrickGainPop:OnHide()
end

function MandelBrickGainPop:OnAnimFinished(anim)
    if anim == self.WBP_ObtaintMandel_in then
        if self._closeClickCount < 1 then
            self._closeClickCount = self._closeClickCount + 1
        end
    elseif anim == self.WBP_ObtaintMandel_out then
        if self._bExecuteClose ~= true then
            self._bExecuteClose = true
            Module.Reward:ShowNextRewards(self._bTabPressed == true)
        end
    end
end

function MandelBrickGainPop:_AddListeners()
    self:AddLuaEvent(Module.IrisSafeHouse.Config.evtTabBackToSafeHouseHD, self._OnTabPressed, self)
end

function MandelBrickGainPop:_OnSkipBtnClick()
    if self._closeClickCount == 1 then
        self._closeClickCount = 2
        self:HandleTransition(true)
		Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
		self:PlayAnimation(self.WBP_ObtaintMandel_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    elseif self._closeClickCount == 0 then
        self._closeClickCount = 1
        self:SkipAnimation(self.WBP_ObtaintMandel_in)
        self._wtRewardDetail:SkipInAnimation()
    end
end

function MandelBrickGainPop:_RefreshWidget()
    if self._closeClickCount < 1 then
        self._closeClickCount = self._closeClickCount + 1
    end
    self._wtRewardDetail:SetType(2)
    self._wtRewardDetail:SetDesc(nil)
    self._wtRewardDetail:SetMainTitle(Module.Reward.Config.Loc.ObtainMandelBrick)
    self._wtNicknameTxt:SetText(Server.RoleInfoServer.nickName or Module.Reward.Config.Loc.Unknown)
    self._wtGainCountTxt:Collapsed()
    local fCallbackIns = CreateCallBack(function(self, mandelBrickLimitData)
        if mandelBrickLimitData then
            local MPDropLimitInfoDataRow = Facade.TableManager:GetRowByKey("MPDropLimitDataTable", tostring(self._mandelBrickId))
            local maxNum = 2
            if isvalid(MPDropLimitInfoDataRow) and MPDropLimitInfoDataRow.WeekLimit > 0 then
                maxNum = MPDropLimitInfoDataRow.WeekLimit
            end
            self._wtGainCountTxt:SetText(StringUtil.Key2StrFormat(Module.Reward.Config.Loc.NumCanObatinInCurrentWeek,{["currentNum"] = mandelBrickLimitData.weekTakeTimes,["maxNum"] = maxNum}))
            self._wtGainCountTxt:SelfHitTestInvisible()
        end
    end,self)
    Server.RewardServer:FetchMandelBrickLimitData(161100, fCallbackIns)
    if isvalid(self._mandelBrickId) then
        self._wtRewardDetail:SetQuality(ItemConfigTool.GetItemQuality(self._mandelBrickId))
        self._wtRewardDetail:SetName(ItemConfigTool.GetItemName(self._mandelBrickId))
    end
    if self._closeClickCount < 1 then
        self:PlayAnimation(self.WBP_ObtaintMandel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end
end

function MandelBrickGainPop:OnNavBack()
    self:_OnSkipBtnClick()
    return true
end

function MandelBrickGainPop:_OnTabPressed()
    self._bTabPressed = true
    self:SkipAnimation(self.WBP_ObtaintMandel_in)
    Facade.SoundManager:StopUIAudioEvent("UI_Common_Popup")
    self:PlayAnimation(self.WBP_ObtaintMandel_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

return MandelBrickGainPop