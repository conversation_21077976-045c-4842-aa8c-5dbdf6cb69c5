----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local SettingLogicHD = {}
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local SettingDataTableLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingDataTableLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local GameSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.GameSettingLogicHD"
local AudioSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.AudioSettingLogicHD"
local CameraSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CameraSettingLogicHD"
local DisplaySettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.DisplaySettingLogicHD"
local InputSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.InputSettingLogicHD"
local VideoSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VideoSettingLogicHD"
local LanguageSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.LanguageSettingLogicHD"
local PrivacySettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.PrivacySettingLogicHD"
local VRamSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VRamSettingLogicHD"
local MiscSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.MiscSettingLogicHD"
local BHDSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.HD.BHDSettingLogic"
local UpgradeSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.UpgradeSettingLogicHD"
local UClientVideoSettingHD = import("ClientVideoSettingHD")
local ESystemSettingInputTypeHD = import("ESystemSettingInputTypeHD")
local UPerfGearFuncLib = import "PerfGearFuncLib"
local UDataTableSystemManagerLite = import("DataTableSystemManagerLite")
local _bhdSetting = import("ClientBHDSetting").Get()
local _baseSettingHD = import("ClientBaseSettingHD").Get()
local _sensitivitySetting = import("ClientSensitivitySettingHD").Get()
local _videoSetting = import("ClientVideoSettingHD").Get()
local _audioSetting = import("ClientAudioSettingHD").Get()
local _cameraSetting = import("ClientCameraSettingHD").Get()
local _displaySetting = import("ClientDisplaySettingHD").Get()
local _gameSetting = import("ClientGameSettingHD").Get()
local _vehicleSetting = import("ClientVehicleSettingHD").Get()
local _privacySetting = import("ClientPrivacySettingHD").Get()
local UClientSettingHelperHD = import("ClientSettingHelperHD")
local UDFHDKeySettingManager = import "DFHDKeySettingManager"
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local UDFMGameGPM = import "DFMGameGPM"
local ESettingGameType = require("DFM.Business.Module.SystemSettingModule.SystemSettingConfig").ESettingGameType
local EMouseSensitivityMode = import("EMouseSensitivityMode")
local ULuaExtension = import("LuaExtension")

local _allSettingOjbs = {
    _sensitivitySetting,
    _videoSetting,
    _audioSetting,
    _cameraSetting,
    _displaySetting,
    _gameSetting,
    _vehicleSetting,
    _privacySetting
}

local VideoSettingRecommandVersion = "6"

local function _IntToBool(value)
    if value == 0 then
        return false
    else
        return true
    end
end

function SettingLogicHD.ApplyAllSettingData()
    local applyMappings = SettingRegLogicHD.GetApplyMapping()
    local mobileMappings = SettingRegLogicHD.GetMobileMapping()

    local idList = table.getkeys(applyMappings)

    for mobileKey, _ in pairs(mobileMappings) do
        if not applyMappings[mobileKey] then
            table.insert(idList, mobileKey)
        end
    end

    local ordered = SettingRegLogicHD.SortPrerequisiteIDs(idList)

    for _, id in ipairs(ordered) do
        trycall(
            function()
                local applyMapping = applyMappings[id]
                if applyMapping then
                    local value = SettingRegLogicHD.CallGetterByID(id)
                    local settingObj = applyMapping.settingObj
                    applyMapping.applyFunc(settingObj, applyMapping.key, value, table.unpack(applyMapping.applyParams))
                    loginfo("Apply Setting:", id, value)
                end

                local mobileMapping = mobileMappings[id]
                if mobileMapping then
                    local value = SettingRegLogicHD.CallGetterByID(id)
                    mobileMapping.mobileFunc(
                        mobileMapping.mobileSettingObj,
                        value,
                        table.unpack(mobileMapping.mobileParams)
                    )
                    loginfo("Apply Mobile Setting:", id, value)
                end
            end
        )
    end

    VideoSettingLogicHD.RecordRealViewportResolution(true)
end

function SettingLogicHD.ApplyOutOfGameSettingData()
    local idArr = UClientSettingHelperHD.GetOutOfGameApplySettings()
    -- array to table
    local idList = {}
    for _, id in pairs(idArr) do
        table.insert(idList, id)
    end

    local applyMappings = SettingRegLogicHD.GetApplyMapping()

    local ordered = SettingRegLogicHD.SortPrerequisiteIDs(idList)

    for _, id in ipairs(ordered) do
        local applyMapping = applyMappings[id]
        if applyMapping then
            local settingObj = applyMapping.settingObj
            local value = SettingRegLogicHD.CallGetterByID(id)
            applyMapping.applyFunc(settingObj, applyMapping.key, value, table.unpack(applyMapping.applyParams))

            loginfo("Apply Setting:", id, value)
        end
    end
    UClientSettingHelperHD.ClearOutOfGameApplySettings()
end

local function _LoadDefaultsFromDataTable()
    local resetMappings = SettingRegLogicHD.GetResetMapping()
    local setMappings = SettingRegLogicHD.GetSetMapping()
    for id, mapping in pairs(setMappings) do
        -- 优先使用resetMapping
        -- local row = SettingDataTableLogicHD.GetDataTableRow(id)
        -- local tableKey = _inputTypeToTableKey[row.InputType]
        -- local tableValue = row[tableKey]
        -- if row.InputType == ESystemSettingInputTypeHD.Switcher then
        --     tableValue = _IntToBool(tableValue)
        -- end
        local tableValue = CommonSettingLogicHD.GetDefaultDataByID(id)
        local resetMapping = resetMappings[id]
        if resetMapping then
            local settingObj = resetMapping.settingObj
            resetMapping.resetter(settingObj, resetMapping.key, tableValue, table.unpack(resetMapping.resetParams))
        else
            local settingObj = mapping.settingObj
            mapping.setter(settingObj, mapping.key, tableValue, table.unpack(mapping.setParams))
        end
    end
    -- 这里单纯覆盖ini值，不需要apply
end

local function _DoResetFromToDefaultIni()
    for _, setting in ipairs(_allSettingOjbs) do
        setting:ResetToDefault()
    end
end

function SettingLogicHD.CheckDataTable()
    if UClientSettingHelperHD.IsEditor() then
        local originTable =
            Facade.TableManager:GetDataTableManager():GetDataTable(
            SettingDataTableLogicHD.GetDataTableName(ESettingGameType.DeltaForce)
        )
        local guid = UDataTableSystemManagerLite.GetTableAssetGuid(originTable)
        local baseSetting = _baseSettingHD
        if guid ~= baseSetting.DataTableGUID then
            _LoadDefaultsFromDataTable()

            VideoSettingLogicHD.ReloadExtraDefaults()

            baseSetting.DataTableGUID = guid
            baseSetting:SaveToDefault()

            for _, setting in ipairs(_allSettingOjbs) do
                setting:SaveToDefault()
            end
        end
    end

    InputSettingLogicHD.CheckDataTable()
end

--需要修改，按照逻辑分类，而不是Setting class分类
-- function SettingLogicHD.DoReset()
--     _DoResetFromToDefaultIni()
--     SettingLogicHD.ApplyAllSettingData()
-- end

function SettingLogicHD.ResetCurrentSettings()
    SettingRegLogicHD.FlushPendingSaveObjs()

    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()

    for id, item in pairs(curItems) do
        if CommonSettingLogicHD.IsValidID(id) then
            if CommonSettingLogicHD.IsValueChanged(id, CommonSettingLogicHD.GetDataByID(id), true) then
                CommonSettingLogicHD.ResetDataByID(id, CommonSettingLogicHD.GetDefaultDataByID(id))
                if not hasdestroy(item) then
                    item:ReloadSetting()
                end
            end
        end
    end
end

function SettingLogicHD.Register()
    GameSettingLogicHD.Register()
    AudioSettingLogicHD.Register()
    CameraSettingLogicHD.Register()
    DisplaySettingLogicHD.Register()
    InputSettingLogicHD.Register()
    VideoSettingLogicHD.Register()
    PrivacySettingLogicHD.Register()
    LanguageSettingLogicHD.Register()
    BHDSettingLogic.Register()
end

local consoleHandle = nil
local _hDisplayMetricsChanged = nil
local onLevelLoadHandle = nil

function SettingLogicHD.UploadAPM()
    local function IsIDInAPMTable(targetID, array)
        for _, id in ipairs(array) do
            if id == targetID then
                return true
            end
        end
        return false
    end

    local function IsIDInSettingMapple(targetID, Mapping)
        for id, _ in pairs(Mapping) do
            if id == targetID then
                return true
            end
        end
        return false
    end

    loginfo("SettingLogicHD.UploadAPM()")

    if _videoSetting then
        local ResolutionStrValue = _videoSetting.ResolutionX .. "," .. _videoSetting.ResolutionY
        UDFMGameGPM.ReportStringParam("VideoSetting", "Resolution", ResolutionStrValue, 0)

        local RealViewportResolution =
            _videoSetting.RealViewportResolution.X .. "," .. _videoSetting.RealViewportResolution.Y
        UDFMGameGPM.ReportStringParam("VideoSetting", "RealViewportResolution", RealViewportResolution, 0)
    end

    local SetMapping = SettingRegLogicHD.GetSetMapping()

    for _, id in ipairs(VideoSettingLogicHD.APMUploadIDTable) do
        if IsIDInSettingMapple(id, SetMapping) then
            local value = CommonSettingLogicHD.GetDataByID(id)
            local category = "VideoSetting"
            CommonSettingLogicHD.GPMReportParam(category, id, value)
        end
    end

    if SetMapping["bScopeUseRT"] ~= nil then
        local value = CommonSettingLogicHD.GetDataByID("bScopeUseRT")
        local category = "GameSetting"
        CommonSettingLogicHD.GPMReportParam(category, "bScopeUseRT", value)
    end
end

function SettingLogicHD.Init()
    if false and IsInEditor() then
        local TestBenchMarkLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.Debug.TestBenchMarkLogicHD"
        TestBenchMarkLogicHD.Test()
    end

    trycall(SettingLogicHD.Register)
    trycall(SettingLogicHD.CheckDataTable)
    trycall(SettingLogicHD.VerifySettings)

    local preset = UClientVideoSettingHD.ParsePresetCommandLine()
    if preset ~= -1 then
        local level = VideoSettingLogicHD.PresetIndexToLevel(preset)
        local mappings = VideoSettingLogicHD.GetGraphicsQualityByLevel(level)
        SettingLogicHD.SetGraphicsQualityList(mappings, true)
    end

    -- 初始化默认配置前需要确保PerfGearTable加载完成
    local PerfGearInst = UPerfGearFuncLib.GetPerfGearInst()
    PerfGearInst:Apply(false, false, false)
    ---@todo 端游声音设置未全部开发完毕，因此只是在手游流程之后覆盖部分设置
    -- 开发完成后，两个平台设置会隔离
    CommonSettingLogicHD.SetIsInitialApplying(true)
    trycall(SettingLogicHD.ApplyAllSettingData)
    CommonSettingLogicHD.SetIsInitialApplying(false)

    consoleHandle = _baseSettingHD.OnSettingHD:Add(SettingLogicHD._OnConsole)

    VideoSettingLogicHD.Init()
    VRamSettingLogicHD.Init()
    AudioSettingLogicHD.Init()
    LanguageSettingLogicHD.Init()
    MiscSettingLogicHD.Init()
    BHDSettingLogic.Init()
    local lastVersion = _videoSetting.RecommandVersion
    if lastVersion ~= VideoSettingRecommandVersion then
        -- SettingLogicHD.ApplyRecommandGraphicsQulitySetting(true)
        -- 超分被纠正过，但是纠正成关闭的
        -- if lastVersion ==  "5" then
        --     if CommonSettingLogicHD.GetDataByID("SuperResolutionMethod") ==  0 then
        -- logerror("SettingLogicHD.Init->RecommandSuperResolutionSetting")
        -- VideoSettingLogicHD.RecommandSuperResolutionSetting(true)
        --     end
        -- end

        -- -- 关闭GPU调试模式. 5的版本纠正过一次，不再纠正
        -- if (lastVersion ~= "5") and (lastVersion ~= "6")  then
        --     logerror("SettingLogicHD.Init SetGPUCrashDebuggingEnable(false)")
        --     VideoSettingLogicHD.SetGPUCrashDebuggingEnable(false)
        -- end

        _videoSetting.RecommandVersion = VideoSettingRecommandVersion
        _videoSetting:SaveToSaved()
    end
    SettingLogicHD.UploadAPM()

    local UDFMGameGPMManager = import("DFMGameGPMManager").GetDFMGameGPMManager()
    onLevelLoadHandle = UDFMGameGPMManager.OnLevelLoad:Add(SettingLogicHD.UploadAPM)
end

function SettingLogicHD.Uninit()
    SettingRegLogicHD.FlushPendingSaveObjs()
    if consoleHandle and isvalid(consoleHandle) then
        _baseSettingHD.OnSettingHD:Remove(consoleHandle)
        consoleHandle = nil
    end

    VideoSettingLogicHD.Uninit()
    AudioSettingLogicHD.Uninit()
    LanguageSettingLogicHD.Uninit()
    MiscSettingLogicHD.Uninit()
    BHDSettingLogic.Uninit()

    local UDFMGameGPMManager = import("DFMGameGPMManager").GetDFMGameGPMManager()

    if onLevelLoadHandle and isvalid(onLevelLoadHandle) then
        UDFMGameGPMManager.OnLevelLoad:Remove(onLevelLoadHandle)
        onLevelLoadHandle = nil
    end
end

local _SpecSettingConsole = {
    GraphicsPreset = function(settingValue)
        settingValue = tonumber(settingValue)
        if settingValue == 7 then
            Timer.DelayCall(
                0,
                function()
                    SettingLogicHD.ApplyRecommandGraphicsQulitySetting(false)
                end
            )
            return
        end
        -- 正常设置档位流程
        CommonSettingLogicHD.SetDataByID("GraphicsPreset", settingValue)
        if settingValue ~= 0 then
            local level = VideoSettingLogicHD.PresetIndexToLevel(settingValue)
            local mappings = VideoSettingLogicHD.GetGraphicsQualityByLevel(level)
            SettingLogicHD.SetGraphicsQualityList(mappings, false)
        end
        CommonSettingLogicHD.ApplyPendingSettings()
        SettingRegLogicHD.FlushPendingSaveObjs()
    end
}

function SettingLogicHD._OnConsole(settingID, settingValue)
    local lowerID = string.lower(settingID)
    local setMappings = SettingRegLogicHD.GetSetMapping()

    for id, func in pairs(_SpecSettingConsole) do
        if lowerID == string.lower(id) then
            func(settingValue)
            return
        end
    end

    for id, mapping in pairs(setMappings) do
        if lowerID == string.lower(id) then
            local row = SettingDataTableLogicHD.GetDataTableRow(id)
            settingValue = tonumber(settingValue)
            local inputType = row.InputType
            if inputType == ESystemSettingInputTypeHD.Switcher then
                settingValue = _IntToBool(settingValue)
            elseif inputType == ESystemSettingInputTypeHD.FloatValue or inputType == ESystemSettingInputTypeHD.Volume then
                settingValue = settingValue
            else
                settingValue = math.floor(settingValue)
            end

            SettingRegLogicHD.CallSetterByID(id, settingValue)
            CommonSettingLogicHD.RefreshPrerequisite(id)

            local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
            local item = curItems[id]
            if not hasdestroy(item) then
                item:ReloadSetting()
            end
            return
        end
    end
end

function SettingLogicHD.RefreshSettingUIByID(settingID)
    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()
    local item = curItems[settingID]
    if not hasdestroy(item) then
        item:ReloadSetting()
    end
end

function SettingLogicHD.SetGraphicsQualityList(mappings, immediately)
    Module.SystemSetting.Field:SetIsChangingGraphicsPresetHD(true)
    local curItems = Module.SystemSetting.Field:GetAllSettingItemHD()

    for id, value in pairs(mappings) do
        local row = SettingDataTableLogicHD.GetDataTableRow(id)
        if row.InputType == ESystemSettingInputTypeHD.Switcher then
            if value == 0 then
                value = false
            else
                value = true
            end
        elseif row.InputType == ESystemSettingInputTypeHD.DropDown then
            if row.Min ~= row.Max then
                value = math.floor(math.clamp(value, row.Min, row.Max - 1))
            end
        end
        if immediately then
            CommonSettingLogicHD.SetDataByIDImmediately(id, value)
        else
            CommonSettingLogicHD.SetDataByID(id, value)
        end
        local item = curItems[id]
        if not hasdestroy(item) then
            item:ReloadSetting()
        end
    end
    Module.SystemSetting.Field:SetIsChangingGraphicsPresetHD(false)
end

function SettingLogicHD.ApplyRecommandGraphicsQulitySetting(immediately)
    trycall(
        function()
            local preset = UClientVideoSettingHD.ParsePresetCommandLine()
            if preset ~= -1 then
                return
            end
            local mappings = VideoSettingLogicHD.GetRecommandGraphicsQuality()
            SettingLogicHD.SetGraphicsQualityList(mappings, immediately)

            VideoSettingLogicHD.RecommandDisplayMode(immediately)

            VideoSettingLogicHD.RecommandSuperResolutionSetting(immediately)

            VideoSettingLogicHD.RecommandMisc(immediately)

            VideoSettingLogicHD.RecordBenchmarkLevels()

            if immediately then
                VideoSettingLogicHD.RecordRealViewportResolution(false)
            end

            if immediately then
                SettingRegLogicHD.FlushPendingSaveObjs()
            end
        end
    )
end

function SettingLogicHD.ApplyRecommandMaxFPSSettingHD(immediately)
    VideoSettingLogicHD.RecommandMaxFPSSetting(immediately)

    if immediately then
        SettingRegLogicHD.FlushPendingSaveObjs()
    end
end

function SettingLogicHD.ApplyRecommandGraphicsSettingMisc(bForce)
    -- local lastVersion = _videoSetting.RecommandVersion
    -- if lastVersion ~= VideoSettingRecommandVersion or bForce then
    --     --根据档位，推荐分辨率和最高帧率
    --     VideoSettingLogicHD.RecommandMisc(true)
    --     _videoSetting.RecommandVersion = VideoSettingRecommandVersion
    --     _videoSetting:SaveToSaved()
    -- end
end

function SettingLogicHD.ApplyLateSettings()
    AudioSettingLogicHD.ApplyGVoiceSettings()
    GameSettingLogicHD.ApplyLateSettings()
end

function SettingLogicHD.VerifySettings()
    VideoSettingLogicHD.VerifySettings()
    -- LanguageSettingLogicHD.VerifySettings()
    InputSettingLogicHD.VerifySettings()

    local setMappings = SettingRegLogicHD.GetSetMapping()
    for id, mapping in pairs(setMappings) do
        local row = SettingDataTableLogicHD.GetDataTableRow(id)
        if row then
            if row.InputType == ESystemSettingInputTypeHD.DropDown then
                if row.Min ~= row.Max then
                    local rawIndex = CommonSettingLogicHD.GetDataByID(id)
                    local newIndex = math.floor(math.clamp(rawIndex, row.Min, row.Max - 1))
                    if rawIndex ~= newIndex then
                        CommonSettingLogicHD.SetDataByIDImmediately(id, newIndex, false)
                    end
                end
            end
        end
    end
    SettingRegLogicHD.FlushPendingSaveObjs()
end

local _CommonSettingInfoIDs = {
    -- "bSeparateVault",
    "bVaultTriggerMode",
    "bCanMouseTurnMelee",
    "bSceneFOVCalcOpenCameraFOV",
    "bScopeUseRT",
    "CannonCamFollow",
    "FOVRatioTPP",
    "DriverWeaponSensitivityTPP",
    "DriverWeaponSensitivityFPP",
    -- "HelicopterDriverWeaponSensitivityFPP",
    -- "HelicopterDriverWeaponSensitivityTPP",
    "PassengerWeaponSensitivityFPP",
    "PassengerWeaponSensitivityTPP",
    "Sensitivity",
    "ADS_Sensitivty_Ratio",
    "MDV",
    "MDV_Driver",
    "MDV_HelicopterDriver",
    "MDV_Passenger",
    "MDVSwitchingMode",
    "SideAimingControlModes",
    "VerticalSensitivity",
    "HorizontalSensitivity",
    "AimHorizontalSensitivity",
    "AimVerticalSensitivity",
    "SensitivityMode",
    "SensitivityMode_Driver",
    "SensitivityMode_HelicopterDriver",
    "SensitivityMode_Passenger",
    "bAllVerticalMMReversed",
    "bInfantryVerticalMMReversed",
    "bVehicleVerticalMMReversed",
    "bHelicopterVerticalMMReversed",
    "bGunnerVerticalMMReversed",
    -- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
    "ADS_Sensitivity_Ratio_Gamepad",
    "VerticalGamepadSensitivity",
    "HorizontalGamepadSensitivity",
    "AimHorizontalGamepadSensitivity",
    "AimVerticalGameSensitivity",
    "GamepadSensitivityMode",
    "bAllVerticalMMReversedGamepad",
    "bInfantryVerticalMMReversedGamepad",
    "bVehicleVerticalMMReversedGamepad",
    "bHelicopterVerticalMMReversedGamepad",
    "bGunnerVerticalMMReversedGamepad",
    -- END MODIFICATION
    "FPP_FOV",
    "bScopeUseRT",
    "bEyesProtection"
}

local function _truncate(num)
    local mult = 100
    return math.floor(num * mult) / mult
end

function SettingLogicHD.GetASDAndMDVDataList(bNeedTruncate, bGamepad)
    local truncate
    if bNeedTruncate then
        truncate = _truncate
    else
        truncate = function(v)
            return v
        end
    end

    local ADSList = {}
    local MDVList = {}
    for mode = EMouseSensitivityMode.ENormalWeapon, EMouseSensitivityMode.EJet do
        local ads = {}
        local mdvList = {}
        local zoomData = InputSettingLogicHD.GetSortedZoomData(mode)
        for i, _ in ipairs(zoomData) do
            local sensitivity0 = InputSettingLogicHD.GetADSSensitivity(mode, i, 0, bGamepad)
            local sensitivity1 = InputSettingLogicHD.GetADSSensitivity(mode, i, 1, bGamepad)
            table.insert(ads, {truncate(sensitivity0), truncate(sensitivity1)})
        end
        for i, _ in ipairs(zoomData) do
            local mdv = InputSettingLogicHD.GetZoomratedMDV(mode, i)
            table.insert(mdvList, truncate(mdv))
        end
        ADSList[mode] = ads
        MDVList[mode] = mdvList
    end
    return ADSList, MDVList
end

function SettingLogicHD.SetASDAndMDVDataList(ADSList, MDVList, bGamepad)
    local anyError = false
    if ADSList == nil or MDVList == nil then
        return true
    end

    for mode = EMouseSensitivityMode.ENormalWeapon, EMouseSensitivityMode.EJet do
        local ads = ADSList[mode]
        local mdv = MDVList[mode]
        -- 因为转json的时候出现了0 index的数组变成了key字符串
        if not ads then
            ads = ADSList[tostring(mode)]
        end
        if not mdv then
            mdv = MDVList[tostring(mode)]
        end

        local zoomData = InputSettingLogicHD.GetSortedZoomData(mode)
        local localNum = #zoomData
        if ads then
            if localNum == #ads then
                for i, v in ipairs(ads) do
                    InputSettingLogicHD.SetADSSensitivity(mode, i, 0, v[1], bGamepad)
                    InputSettingLogicHD.SetADSSensitivity(mode, i, 1, v[2], bGamepad)
                end
            else
                anyError = true
            end
        else
            anyError = true
        end

        if mdv then
            if localNum == #mdv then
                for i, v in ipairs(mdv) do
                    InputSettingLogicHD.SetZoomratedMDV(mode, i, v)
                end
            else
                anyError = true
            end
        else
            anyError = true
        end
    end
    return anyError
end

SettingLogicHD.GetHDSettingInfo_CommonSetting = function()
    local rst = {}
    for _, id in ipairs(_CommonSettingInfoIDs) do
        if CommonSettingLogicHD.IsValidID(id) then
            local value = CommonSettingLogicHD.GetDataByID(id)
            if type(value) == "number" and math.type(value) == "float" then
                value = _truncate(value)
            end
            rst[id] = value
        else
            logerror("GetHDSettingInfo_CommonSetting Cannot find ID:", id)
        end
    end

    local ADSList, MDVList = SettingLogicHD.GetASDAndMDVDataList(true)
    rst.ADS = ADSList
    rst.MDVList = MDVList

    local Json = JsonFactory.createJson()

    return Json.encode(rst)
end

SettingLogicHD.GetHDSettingInfo_KeySetting = function()
    local keySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    if isvalid(keySettingMgr) and keySettingMgr.SerializeCustomKeySetting ~= nil then
        return keySettingMgr:SerializeCustomKeySetting()
    end
    return ""
end

SettingLogicHD.GetHDSettingInfoJsonStr = function()
    local info = {
        ["CommonSetting"] = SettingLogicHD.GetHDSettingInfo_CommonSetting(),
        ["KeySetting"] = SettingLogicHD.GetHDSettingInfo_KeySetting()
    }
    local jsonStr = UClientSettingHelperHD.MergeJson(info)
    return jsonStr
end

function SettingLogicHD.GetLocalCommonSettingDataAndHash()
    local values = {}
    local simpleSettingData = {}

    -- 先收集简单的kv数据，
    local GetDataByID = CommonSettingLogicHD.GetDataByID
    local insert = table.insert
    for _, id in ipairs(Module.SystemSetting.Config.SimpleCloudSettingIDs) do
        local value = GetDataByID(id)
        simpleSettingData[id] = value
        insert(values, tostring(value))
    end

    --再收集特殊数据
    local adsList, mdvList = SettingLogicHD.GetASDAndMDVDataList(false, false)
    local adsList_gamepad, mdvList_gamepad = SettingLogicHD.GetASDAndMDVDataList(false, true)

    for mode = EMouseSensitivityMode.ENormalWeapon, EMouseSensitivityMode.EJet do
        local adsByMode = adsList[mode]
        local mdvByMode = mdvList[mode]
        for _, ads in ipairs(adsByMode) do
            insert(values, tostring(ads[1]))
            insert(values, tostring(ads[2]))
        end
        for _, mdv in ipairs(mdvByMode) do
            insert(values, tostring(mdv))
        end
        local adsByMode_gamepad = adsList_gamepad[mode]
        local mdvByMode_gamepad = mdvList_gamepad[mode]
        for _, ads in ipairs(adsByMode_gamepad) do
            insert(values, tostring(ads[1]))
            insert(values, tostring(ads[2]))
        end
        for _, mdv in ipairs(mdvByMode_gamepad) do
            insert(values, tostring(mdv))
        end
    end

    local valueStr = table.concat(values)
    local hash = ULuaExtension.String2UpperMD5(valueStr)

    local commonSettingData = {
        SimpleSettingData = simpleSettingData,
        ADSList = adsList,
        MDVList = mdvList,
        ADSListGamepad = adsList_gamepad,
        MDVListGamepad = mdvList_gamepad
    }

    return commonSettingData, hash
end

function SettingLogicHD.DoSendCommonSetting(commonSettingData, hash)
    loginfo("[SettingLogicHD.Cloud] DoSendCommonSetting")
    -- 先发数据，再发Hash
    local function OnSendHashRes(res)
        if res.result == 0 then
            Module.SystemSetting.Field:SetFetchedCommonSettingHashHD(hash)
            loginfo("[SettingLogicHD.Cloud] Hash Succeed")
        else
            logerror("[SettingLogicHD.Cloud] Hash Failed", res.result)
        end
    end

    local function OnSendDataRes(res)
        if res.result == 0 then
            -- end)
            loginfo("[SettingLogicHD.Cloud] Data Succeed")
            -- 否则会被高频调用给拦截
            -- Timer.DelayCall(0.1, function()
            local reqHash = pb.CSSettingPutKeyValueReq:New()
            reqHash.kv = {type = "CommonSettingHD", key = "Hash", value = hash}
            reqHash:Request(OnSendHashRes, {bEnableHighFrequency = true})
        else
            logerror("[SettingLogicHD.Cloud] Data Failed", res.result)
        end
    end

    -- 转json
    local Json = JsonFactory.createJson()
    commonSettingData.SimpleSettingData = UpgradeSettingLogicHD.FillSaveVersion(commonSettingData.SimpleSettingData)
    local commonSettingJson = Json.encode(commonSettingData)

    local reqData = pb.CSSettingPutKeyValueReq:New()
    reqData.kv = {type = "CommonSettingHD", key = "Data", value = commonSettingJson}
    reqData:Request(OnSendDataRes, {bEnableHighFrequency = true})

    loginfo("[SettingLogicHD.Cloud] DoSendCommonSetting data ".. table.dump(commonSettingData))

end

function SettingLogicHD.DoSendKeySetting(keySettingJsonStr)
    loginfo("[SettingLogicHD.Cloud] DoSendKeySetting")
    local function OnSendDataKeySettingRes(res)
        if res.result == 0 then
            loginfo("[SettingLogicHD.Cloud] DataKeySetting Succeed")
        else
            logerror("[SettingLogicHD.Cloud] DataKeySetting Failed", res.result)
        end
    end
    if keySettingJsonStr ~= nil then
        local reqDataKeySetting = pb.CSSettingPutKeyValueReq:New()
        reqDataKeySetting.kv = {type = "CommonSettingHD", key = "DataKeySetting", value = keySettingJsonStr}
        reqDataKeySetting:Request(OnSendDataKeySettingRes, {bEnableHighFrequency = true})
    end
end

function SettingLogicHD.TrySendSetting(bForce)
    bForce = not not bForce
    loginfo("[SettingLogicHD.Cloud] TrySendSetting")
    -- 检查云端的Hash是否和本地有差异，有差异时再上传

    local commonSettingData, hash = SettingLogicHD.GetLocalCommonSettingDataAndHash()
    local fetchedHash = Module.SystemSetting.Field:GetFetchedCommonSettingHashHD()

    if hash and fetchedHash then
        loginfo("[SettingLogicHD.Cloud] localHash " .. hash)
        loginfo("[SettingLogicHD.Cloud] fetchedHash " .. fetchedHash)
    end

    if (hash ~= fetchedHash) or bForce then
        SettingLogicHD.DoSendCommonSetting(commonSettingData, hash)
    end

    -- 按键绑定本地与云端比较
    local keySettingJsonStr = SettingLogicHD.GetHDSettingInfo_KeySetting()
    local fetchedKeySettingJsonStr = Module.SystemSetting.Field:GetFetchedKeySettingStr()
    local bKeySettingChanged = keySettingJsonStr ~= fetchedKeySettingJsonStr
    if bKeySettingChanged or bForce then
        if bForce then
            loginfo("[SettingLogicHD.Cloud] Force sendSetting")
        end
        SettingLogicHD.DoSendKeySetting(keySettingJsonStr)
    end
end

-- 拉取云端设置成功后，可能遇到场景切换，因此要等到大厅再把弹窗弹出来
function SettingLogicHD.AddPendingOverwriteTask(
    remoteData,
    remoteHash,
    localData,
    localHash,
    remoteDataKeySetting,
    localDataKeySetting)
    loginfo("[SettingLogicHD.Cloud] AddPendingOverwriteTask")
    local newTask = {
        remoteData = remoteData,
        remoteHash = remoteHash,
        localData = localData,
        localHash = localHash,
        remoteDataKeySetting = remoteDataKeySetting,
        localDataKeySetting = localDataKeySetting
    }
    Module.SystemSetting.Field:SetPendingOverwriteTaskHD(newTask)
end

function SettingLogicHD.TryHandlePendingOverwriteTask()
    loginfo("[SettingLogicHD.Cloud] TryHandlePendingOverwriteTask")
    local pendingTask = Module.SystemSetting.Field:GetPendingOverwriteTaskHD()

    if not pendingTask then
        return
    end

    Module.SystemSetting.Field:SetPendingOverwriteTaskHD(nil)
    local remoteData = pendingTask.remoteData
    local remoteHash = pendingTask.remoteHash
    local localData = pendingTask.localData
    local localHash = pendingTask.localHash
    local remoteDataKeySetting = pendingTask.remoteDataKeySetting
    local localDataKeySetting = pendingTask.localDataKeySetting

    -- 云端覆盖本地
    local function OnRemoteOverwriteLocal()
        loginfo("[SettingLogicHD.Cloud] OnRemoteOverwriteLocal")
        local anyError = false

        local ret =
            trycall(
            function()
                local SetDataByID = CommonSettingLogicHD.SetDataByIDImmediately

                local simpleSettingData = UpgradeSettingLogicHD.UpgradeForCloud(remoteData.SimpleSettingData)

                -- 为了兼容性，需要根据本地的id列表来获取数值，而不是云端的
                for _, id in ipairs(Module.SystemSetting.Config.SimpleCloudSettingIDs) do
                    local settingValue = simpleSettingData[id]
                    if settingValue ~= nil then
                        SetDataByID(id, settingValue)
                    else
                        if id then
                            logerror("[SettingLogicHD.Cloud] "..id.." value not found in cloud data")
                        else
                            logerror("[SettingLogicHD.Cloud] id nil")
                        end
                        anyError = true
                    end
                end

                local ADSList = remoteData.ADSList
                local MDVList = remoteData.MDVList
                local anyError_SetASDAndMDV = SettingLogicHD.SetASDAndMDVDataList(ADSList, MDVList, false)
                local ADSListGamepad = remoteData.ADSListGamepad
                local MDVListGamepad = remoteData.MDVListGamepad
                local anyError_SetASDAndMDV_Gamepad = SettingLogicHD.SetASDAndMDVDataList(ADSListGamepad, MDVListGamepad, true)

                anyError = anyError or anyError_SetASDAndMDV or anyError_SetASDAndMDV_Gamepad
            end
        )

        Module.SystemSetting.Field:SetFetchedCommonSettingHashHD(remoteHash)
        SettingRegLogicHD.FlushPendingSaveObjs()

        if remoteDataKeySetting then
            Module.SystemSetting.Field:SetFetchedKeySettingStr(remoteDataKeySetting)
            SettingLogicHD.ApplyKeySettingJsonStr(remoteDataKeySetting)
        else
            Module.SystemSetting.Field:SetFetchedKeySettingStr("")
        end

        if not ret then
            anyError = true
        end

        -- if anyError then
            -- 本地与远端数据格式不匹配，重新上传本地数据
            -- 需要重新获取数据，并计算hash
            -- SettingLogicHD.TrySendSetting()
        -- end
        -- 不论写入是否发生异常，都要再次上传云端
        SettingLogicHD.TrySendSetting(true)
    end

    -- 本地覆盖云端
    local function OnLocalOverwriteRemote()
        loginfo("[SettingLogicHD.Cloud] OnLocalOverwriteRemote")
        if remoteHash ~= localHash then
            SettingLogicHD.DoSendCommonSetting(localData, localHash)
        end
        if remoteDataKeySetting ~= localDataKeySetting then
            -- 慢一点避免高频req阻塞
            Timer.DelayCall(
                1,
                function()
                    SettingLogicHD.DoSendKeySetting(localDataKeySetting)
                end
            )
        end
    end

    -- She2 6.7
    if _gameSetting.bConfirmSettingDiff then
        -- 留个口子恢复原样，默认false
        loginfo("[SettingLogicHD.Cloud] Want manually confirm")
    else
        -- 不再弹窗，默认云端覆盖本地
        OnRemoteOverwriteLocal()
        loginfo("[SettingLogicHD.Cloud] Default use remote")
        return
    end

    -- 弹窗
    local contentTxt = Module.SystemSetting.Config.Loc.HDSetting.SettingConflictTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDSetting.SettingConflictUseLocalTxt
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.SettingConflictUseRemoteTxt
    Module.CommonTips:ShowConfirmWindow(
        contentTxt,
        OnRemoteOverwriteLocal,
        OnLocalOverwriteRemote,
        cancelTxt,
        confirmTxt
    )
end

function SettingLogicHD.FetchSetting()
    loginfo("[SettingLogicHD.Cloud] FetchSetting")

    -- 先对本地存档兼容性升级
    UpgradeSettingLogicHD.UpgradeForIni()

    local CloudSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CloudSettingLogicHD"
    CloudSettingLogicHD.ReqAllUploadedSettingData()

    local function OnFetchRes(res)
        if res.result == 0 then
            loginfo("[SettingLogicHD.Cloud] FetchSetting Succ")
            local remoteData = nil
            local remoteHash = nil
            local remoteDataKeySetting = nil
            for _, v in ipairs(res.kv_array) do
                if v.type == "CommonSettingHD" then
                    if v.key == "Data" then
                        local commonSettingJson = v.value
                        local Json = JsonFactory.createJson()
                        remoteData = Json.decode(commonSettingJson)
                    elseif v.key == "Hash" then
                        remoteHash = v.value
                    elseif v.key == "DataKeySetting" then
                        remoteDataKeySetting = v.value
                    end
                end
            end

            -- 如果云端有数据
            if remoteData then
                loginfo("[SettingLogicHD.Cloud] remoteData ".. table.dump(remoteData))
            end
            if remoteHash then
                loginfo("[SettingLogicHD.Cloud] remoteHash " .. remoteHash)
            end
            if remoteDataKeySetting then
                loginfo("[SettingLogicHD.Cloud] remoteDataKeySetting " .. remoteDataKeySetting)
            end
            if remoteData and remoteHash and remoteDataKeySetting then
                local localSaveVersion = UpgradeSettingLogicHD.GetLocalSaveVersion()
                local remoteSaveVersion = UpgradeSettingLogicHD.GetCloudSettingVersion(remoteData.SimpleSettingData)

                -- 先计算本地的设置Hash进行对比
                local localData, localHash = SettingLogicHD.GetLocalCommonSettingDataAndHash()
                local localDataKeySetting = SettingLogicHD.GetHDSettingInfo_KeySetting()
                if localData then
                    loginfo("[SettingLogicHD.Cloud] localData "..table.dump(localData))
                end
                if localHash then
                    loginfo("[SettingLogicHD.Cloud] localHash " .. localHash)
                end
                if localDataKeySetting then
                    loginfo("[SettingLogicHD.Cloud] localDataKeySetting " .. localDataKeySetting)
                end
                if localHash ~= remoteHash or localDataKeySetting ~= remoteDataKeySetting or localSaveVersion ~= remoteSaveVersion then
                    -- 发现云端数据不一致，询问玩家是否覆盖
                    logwarning("[SettingLogicHD.Cloud] FetchSetting LocalDiff")
                    SettingLogicHD.AddPendingOverwriteTask(
                        remoteData,
                        remoteHash,
                        localData,
                        localHash,
                        remoteDataKeySetting,
                        localDataKeySetting
                    )
                end
            end
        else
            logerror("[SettingLogicHD.Cloud] FetchSetting Failed", res.result)
        end
    end

    -- local req = pb.CSSettingGetValuesByTypeReq:New()
    -- req.type = "CommonSettingHD"
    -- req:Request(OnFetchRes, {bEnableHighFrequency = true})
end

function SettingLogicHD.ApplyKeySettingJsonStr(keySettingJsonStr)
    loginfo("[SettingLogicHD.Cloud] ApplyKeySettingJsonStr keySettingJsonStr:" .. keySettingJsonStr)
    local DFHDKeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    if DFHDKeySettingMgr then
        DFHDKeySettingMgr:ReceiveCustomKeySetting(keySettingJsonStr)
    else
        logerror("[SettingLogicHD.Cloud] ApplyKeySettingJsonStr DFHDKeySettingMgr nil")
    end
end

return SettingLogicHD
