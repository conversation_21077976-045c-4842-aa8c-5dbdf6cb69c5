----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

--- 对应蓝图：WBP_ArknightsRecruitment_Main
--- @class MTSafeItem : LuaUIBaseView

local MTSafeItem = ui("MTSafeItem")
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local IVCommonItemTemplate = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.IVCommonItemTemplate"
local IVQualityComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVQualityComponent"
local IVMainIconComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVMainIconComponent"
local IVSearchAnimComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVSearchAnimComponent"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"

--- 结束音效
local itemQuality2ItemShowEvt = {
    [0] = DFMAudioRes.UILootShowNormal,
    [1] = DFMAudioRes.UILootShowNormal,
    [2] = DFMAudioRes.UILootShowNormal,
    [3] = DFMAudioRes.UILootShowEpic,
    [4] = DFMAudioRes.UILootShowEpic,
    [5] = DFMAudioRes.UILootShowMythic,
    [6] = DFMAudioRes.UILootShowMythic,
}

MTSafeItem.evtItemPickup = LuaEvent:NewIns("MTSafeItem.evtIconClicked")

function MTSafeItem:Ctor()
    self.itemInfo = {}

    self._wtBtn = self:Wnd("DFButton_27", UIButton)
    self._wtNumTxt = self:Wnd("DFTextBlock", UITextBlock)
    self._wtNameTxt = self:Wnd("DFTextBlock_55", UITextBlock)

    self._wtIconComp = self:Wnd("wtItemIcon", IVMainIconComponent)
    self._wtIcon = self._wtIconComp:Wnd("wtMainIcon", UIImage)
    self._wtQualityComp = self:Wnd("wtItemQuality", IVQualityComponent)
    self._wtSearchAnimComp = self:Wnd("WBP_SlotComp_SearchAnim", IVSearchAnimComponent)

    self._wtBtn:Event("OnClicked", self.OnClicked, self)

    if not IsHD() then return end
    self:SetCppValue("bIsFocusable", true)
end
----------------------------------------------------- 干员图标时 -----------------------------------------------------
function MTSafeItem:SetInfo(itemInfo, i)
    self.itemInfo = itemInfo
    self.index = i

    local itemData = ItemBase:New(tonumber(itemInfo.connectedItem), itemInfo.showNumber)
    self._wtIconComp:BindItem(itemData)
    self._wtQualityComp:BindItem(itemData)

    self._wtQualityComp:SetCppValue("BpType", itemInfo.showRarity - 1)
    self._wtQualityComp:BP_SetType()

    local itemAsset = ItemConfigTool.GetItemAssetById(itemInfo.connectedItem)
    if itemAsset then
        self._wtIcon:AsyncSetImagePath(itemAsset.ItemIconPath)
    end

    if itemInfo.showNumber ~= 0 then
        self._wtNumTxt:SetText(itemInfo.showNumber)
    else
        self._wtNumTxt:Collapsed()
    end
    self._wtNameTxt:SetText(ActivityLogic.HandleLocalizeText(itemInfo.name))

    if itemInfo.bIsSearched then
        self._wtSearchAnimComp:Collapsed()
    else
        self._wtSearchAnimComp:SelfHitTestInvisible()
        self._wtSearchAnimComp:SetWaitingStatus()
    end
    
end

function MTSafeItem:OnClicked()
    if self.itemInfo.bIsSearched then
        self:Collapsed()
        MTSafeItem.evtItemPickup:Invoke(self.itemInfo.attributeChange, self.index)
        Module.CommonTips:ShowSimpleTip(ActivityLogic.HandleLocalizeText(self.itemInfo.claimNote))
    else
        Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.LootSearchTxt)
        return
    end
end

function MTSafeItem:SearchBegin()
    self._wtSearchAnimComp:SetSearchingStatus()
    self._wtSearchAnimComp:PlayWidgetAnim(self._wtSearchAnimComp.Ani_looting_loop, 0)
    self._wtSearchAnimComp._wtTeamInfoRoot:Collapsed()

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILootItemSearch)
end

function MTSafeItem:SearchEnd()

    self.itemInfo.bIsSearched = true
    
    self._wtIconComp:PlaySearchDoneAnim()
    self._wtQualityComp:PlaySearchDoneAnim()
    self._wtSearchAnimComp:FinishSearchAnim()
    self._wtSearchAnimComp:Collapsed()

    -- 播放结束的2D音效
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UILootItemSearch)
    local evt = itemQuality2ItemShowEvt[self.itemInfo.showRarity]
    Facade.SoundManager:PlayUIAudioEvent(evt)

end

return MTSafeItem