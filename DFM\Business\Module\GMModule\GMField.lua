----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGM)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class GMField : FieldBase
local GMField = class("GMField", require "DFM.YxFramework.Managers.Module.FieldBase")
local GMSaveLogic = require "DFM.Business.Module.GMModule.Logic.GMSaveLogic"

local function xxww_(...)
    loginfo("[xxww] ", ...)
end

function GMField:Ctor()
    self._gmBtnPanelHandle = nil
    self._gmMainPanelHandle = nil
    self._watermarkHandle = nil
    self._infoHintHandle = nil
    self._saveData = {}

    --ABTest实验标签ID表，每次回到模式大厅按间隔拉取协议更新，全局有效不可删除
    self._abt_exp_tag_ids = {}

    self.cutSceneHandel = nil
    self.simulateLoadTime = 0
    self.simulateOpenTime = 0
    self.simulateTotalTime = 0

    self.simulateTimeByLevel = {
        [1] = { ["simulateLoadTime"] = 0.1, ["simulateOpenTime"] = 0.1, ["simulateTotalTime"] = 0.1},
        [2] = { ["simulateLoadTime"] = 0.2, ["simulateOpenTime"] = 0.25, ["simulateTotalTime"] = 0.25},
        [3] = { ["simulateLoadTime"] = 0.5, ["simulateOpenTime"] = 0.5, ["simulateTotalTime"] = 0.5},
    }
end

function GMField:OnClearField()
    self._gmBtnPanelHandle = nil
    self._gmMainPanelHandle = nil
    self._watermarkHandle = nil
    self._infoHintHandle = nil

    self.cutSceneHandel = nil
end

function GMField:IsDataRepeat(data1 , data2)
    for index, value in ipairs(data1) do
        if data2[index] ~= value then
            return false
        end
    end
    return true
end

function GMField:SaveData(gmId, param1, param2, param3)
    local data = {
        [1] = param1,
        [2] = param2,
        [3] = param3,
    }
    if self._saveData[gmId] then
        for index, info in ipairs(self._saveData[gmId]) do
            if self:IsDataRepeat(info, data) then
                table.remove(self._saveData[gmId], index)
                break
            end
        end
        if #self._saveData[gmId] >= 10 then
            table.remove(self._saveData[gmId], 10)
        end
    else
        self._saveData[gmId] = {}
    end
    table.insert(self._saveData[gmId], 1, data)
end

function GMField:GetSaveData(gmId)
    return self._saveData[gmId] and self._saveData[gmId] or {}
end

function GMField:SetGmMainPanelHandle(gmMainPanelHandle)
    self._gmMainPanelHandle = gmMainPanelHandle
end

function GMField:GetGmMainPanelHandle()
    return self._gmMainPanelHandle
end

function GMField:SetGmBtnPanelHandle(gmBtnPanelHandle)
    self._gmBtnPanelHandle = gmBtnPanelHandle
end

function GMField:GetGmBtnPanelHandle()
    return self._gmBtnPanelHandle
end

function GMField:SetWatermarkHandle(watermarkHandle)
    self._watermarkHandle = watermarkHandle
end

function GMField:GetWatermarkHandle()
    return self._watermarkHandle
end

function GMField:SetInfoHintHandle(infoHintHandle)
    self._infoHintHandle = infoHintHandle
end

function GMField:GetInfoHintHandle()
    return self._infoHintHandle
end

function GMField:GetTabList(configPath)
    local gmConfigGroupDataTable = Facade.TableManager:GetTable(configPath)
    local dataTableArray = table.tolist(gmConfigGroupDataTable)
    table.sort(
        dataTableArray,
        function(lhs, rhs)
            return lhs["Priority"] < rhs["Priority"]
        end
    )
    return dataTableArray
end
function GMField:GetFilteredBtnList(groupId, configPath)
    local gmConfigDataTable = Facade.TableManager:GetTable(configPath)
    local filteredDataTableArray = {}
    if groupId then
        for _, dataRow in gmConfigDataTable:FullPairs() do
            if dataRow["Group"] == groupId then
                table.insert(filteredDataTableArray, dataRow)
            end
        end
        table.sort(
            filteredDataTableArray,
            function(lhs, rhs)
                return tonumber(lhs["GMId"]) < tonumber(rhs["GMId"])
            end
        )
    else
        local gmRecentlyUsedList = GMSaveLogic.GetGMRecentlyUsed()
        for _, gmId in ipairs(gmRecentlyUsedList) do
            local dataRow = gmConfigDataTable[gmId]
            if dataRow then
                table.insert(filteredDataTableArray, 1, dataRow)
            end
        end
    end
    return filteredDataTableArray
end
function GMField:GetFilteredBtnByName(prefixStrs, descDataRowName)
    local gmConfigDataTable = Facade.TableManager:GetTable(configPath)
    local filteredDataTableArray = {}
    if descDataRowName then
        for _, dataRow in gmConfigDataTable:FullPairs() do
            for _, prefixStr in ipairs(prefixStrs) do
                if string.find(tostring(dataRow[descDataRowName]), prefixStrs) then
                    table.insert(filteredDataTableArray, dataRow)
                end
            end
        end
        table.sort(
            filteredDataTableArray,
            function(lhs, rhs)
                return tonumber(lhs["GMId"]) < tonumber(rhs["GMId"])
            end
        )
    end
    return filteredDataTableArray
end

function GMField:GetCandidateArgList(dataTableName, prefixStrs, argDataRowName, descDataRowName, isFind)
    isFind = setdefault(isFind, true)
    local dataTbl = Facade.TableManager:GetTable(dataTableName)
    if not dataTbl then
        return {}
    end
    local filteredDataTableArray = {}
    if #prefixStrs > 0 then
        for key, dataRow in dataTbl:FullPairs() do
            for _, prefixStr in ipairs(prefixStrs) do
                if string.len(prefixStr) <= 0 or string.starts_with(tostring(dataRow[argDataRowName]), prefixStr) then
                    table.insert(filteredDataTableArray, dataRow)
                    break
                else
                    local bResult, start, finish = pcall(function()
                        return string.find(tostring(dataRow[descDataRowName]), prefixStr)
                    end)
                    if bResult and start then
                        if isFind then
                            table.insert(filteredDataTableArray, dataRow)
                            break
                        end
                    else
                        if not bResult then
                            logerror("string.find Error in pattern matching: ", tostring(start), "dataRow[descDataRowName]:", dataRow[descDataRowName], "prefixStr:", prefixStr)
                        end
                    end
                end
            end
        end
    end
    table.sort(
        filteredDataTableArray,
        function(lhs, rhs)
            local lnum = tonumber(lhs[argDataRowName])
            local rnum = tonumber(rhs[argDataRowName])
            if lnum and rnum then
                return lnum < rnum
            else
                return false
            end
        end
    )
    return filteredDataTableArray
end

function GMField:GetPreDescName(id, dataTableName, argDataRowName, descDataRowName) --通过物品id获取描述
    local datableInfo = Facade.TableManager:GetTable(dataTableName)
    if not datableInfo then
        return {}
    end
    if #id > 0 then
        for _, dataRow in pairs(datableInfo) do
            if dataRow[argDataRowName] == id then
                return dataRow[descDataRowName]
            end
        end

    end
end

function GMField:GetAbtExpTagIds()
    return self._abt_exp_tag_ids
end

function GMField:SetAbtExpTagIds(abt_exp_tag_ids)
    self._abt_exp_tag_ids = abt_exp_tag_ids
end

return GMField
