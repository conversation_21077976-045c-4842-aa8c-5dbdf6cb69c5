----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMBattlefieldEntry)
----- LOG FUNCTION AUTO GENERATE END -----------



local Config = require "DFM.Business.Module.IrisSafeHouseModule.IrisSafeHouseConfig"
local EntryLogic = require("DFM.Business.Module.BattlefieldEntryModule.Logic.BattlefieldEntryLogic")
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local Field = Module.BattlefieldEntry.Field
local Config = Module.BattlefieldEntry.Config

local BattlefieldPrepareLogic = {}

local _allEvents = {}

local _bMainTabViewOpened = false

local _stateMap = {
    [Config.EPrepareType.UnInited] = {
        fStartClicked = function()
        end,
        fGetStartButtonText = function()
            return Config.Loc.Prepare[Config.EPrepareType.UnInited]
        end
    },
    [Config.EPrepareType.NotReady] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIMatchEnterGame)
            --检测需不需要下载全部地图
            if Server.TeamServer:GetEnableMPRankMatch() and not Module.BattlefieldEntry:CheckIsCommanderMode() then
                if BattlefieldPrepareLogic.CheckIsNeedDownloadAllMap() then
                    return
                end
            end
            --检测是否有足够的地图
            if not BattlefieldPrepareLogic.CheckIsMapEnough() then
                return 
            end
            --检测有没有下载地图
            if not BattlefieldPrepareLogic.CheckMapIsDownload() then
                return
            end
            local hasValidHero,heroNames=Module.BattlefieldEntry:CheckHasValidHero(Module.BattlefieldEntry:GetGroupId())--检查一下是否有可用英雄
            if not hasValidHero then
                Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.BattlefieldEntry.Config.Loc.NoAvailableHero,{heroNames=heroNames}),3)
                return
            end
            if Server.TeamServer:GetEnableMPRankMatch() and not Module.BattlefieldEntry:CheckIsCommanderMode() then
                local bIsLocked,needLevel=BattlefieldPrepareLogic.CheckRankLockedBySeasonLevel()
                if bIsLocked then
                    Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.CantMatchScoreReadyTipText)
                    return
                end
            end
            local isLockedByPlayerLevel,unlockLevel=BattlefieldPrepareLogic.CheckIsModeLockedByPlayerLevel()
            if isLockedByPlayerLevel then
                Module.CommonTips:ShowSimpleTip(string.format(Module.BattlefieldEntry.Config.Loc.OpenLevelTips,unlockLevel))
                return
            end

            local isLockedByRankScore,unlockScore=BattlefieldPrepareLogic.CheckIsModeLockedByRankScore()
            if isLockedByRankScore then
                local rankData=Module.Tournament:GetRankDataByScore(unlockScore)
                Module.CommonTips:ShowSimpleTip(string.format(Module.BattlefieldEntry.Config.Loc.OpenRankLevelTips,rankData and rankData.Name or "?"))
                return
            end
            
            if Module.BattlefieldEntry:CheckIsCommanderMode(Module.BattlefieldEntry:GetGroupId()) then
                local isLocked,unlockDesc=BattlefieldPrepareLogic.CheckIsModeLockedByCommanderCondition()
                if isLocked then
                    Module.CommonTips:ShowSimpleTip(unlockDesc)
                    return
                end
            end
            
            local fCallback = SafeCallBack(BattlefieldPrepareLogic.SetPrepareState, nil, Config.EPrepareType.Ready)
            Server.TeamServer:SendMatchReady(true, fCallback)

        end,
        fEnter = function()
        end,
        fLeave = function()
        end,
        fGetStartButtonText = function()
            local readyNum, totalNum = BattlefieldPrepareLogic.GetReadyAndTotalNum()
            return StringUtil.SequentialFormat('{0}[{1}/{2}]',Config.Loc.Prepare[Config.EPrepareType.NotReady],readyNum, totalNum)
        end
    },
    [Config.EPrepareType.Ready] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)
            local fCallback = SafeCallBack(BattlefieldPrepareLogic.SetPrepareState, nil, Config.EPrepareType.NotReady)
            Server.TeamServer:SendMatchReady(false, fCallback)
        end,
        fEnter = function()
        end,
        fLeave = function()
        end,
        fGetStartButtonText = function()
            local readyNum, totalNum = BattlefieldPrepareLogic.GetReadyAndTotalNum()
            return StringUtil.SequentialFormat('{0}[{1}/{2}]',Config.Loc.Prepare[Config.EPrepareType.Ready],readyNum, totalNum)
        end
    },
    
    [Config.EPrepareType.Captain] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIMatchEnterGame)
            --赛季刚重置的时候有一段保护时间
            local bInProtectTime,sec=Module.Tournament:IsInSeasonInitProtectTime()
            if bInProtectTime then
                Module.CommonTips:ShowSimpleTip(string.format(Module.Tournament.Config.Loc.SeasonInInitProtectTime,sec))
                return
            end

            --检测需不需要下载全部地图
            if Server.TeamServer:GetEnableMPRankMatch() and not Module.BattlefieldEntry:CheckIsCommanderMode() then
                if BattlefieldPrepareLogic.CheckIsNeedDownloadAllMap() then
                    return
                end
            end
            --检测是否有足够的地图
            if not BattlefieldPrepareLogic.CheckIsMapEnough() then
                return 
            end
            --检测有没有下载地图
            if not BattlefieldPrepareLogic.CheckMapIsDownload() then
                return
            end
            
            local hasValidHero,heroNames=Module.BattlefieldEntry:CheckHasValidHero(Module.BattlefieldEntry:GetGroupId())--检查一下是否有可用英雄
            if not hasValidHero then
                Module.CommonTips:ShowSimpleTip(StringUtil.Key2StrFormat(Module.BattlefieldEntry.Config.Loc.NoAvailableHero,{heroNames=heroNames}),3)
                return
            end

            local isLockedByPlayerLevel,unlockLevel=BattlefieldPrepareLogic.CheckIsModeLockedByPlayerLevel()
            if isLockedByPlayerLevel then
                Module.CommonTips:ShowSimpleTip(string.format(Module.BattlefieldEntry.Config.Loc.OpenLevelTips,unlockLevel))
                return
            end
            
            local isLockedByRankScore,unlockScore=BattlefieldPrepareLogic.CheckIsModeLockedByRankScore()
            if isLockedByRankScore then
                local rankData=Module.Tournament:GetRankDataByScore(unlockScore)
                Module.CommonTips:ShowSimpleTip(string.format(Module.BattlefieldEntry.Config.Loc.OpenRankLevelTips,rankData and rankData.Name or "?"))
                return
            end

            if Module.BattlefieldEntry:CheckIsCommanderMode(Module.BattlefieldEntry:GetGroupId()) then
                local isLocked,unlockDesc=BattlefieldPrepareLogic.CheckIsModeLockedByCommanderCondition()
                if isLocked then
                    Module.CommonTips:ShowSimpleTip(unlockDesc)
                    return
                end
            end

            --针对限时开放的地图，匹配之前需要先验证一下
            if not Server.GameModeServer:GetIsModeOpen(Module.BattlefieldEntry:GetGroupId()) then
                logwarning("BattlefieldPrepareLogic:OnStartBtnClick, mode not open!")
                Field:ResetToDefaultMaps()
                return
            else
                for k,v in pairs(Module.BattlefieldEntry:GetSelectedMapList() or {}) do
                    if not Server.GameModeServer:GetIsMapOpenByMapInfo(v) then
                        logwarning("BattlefieldPrepareLogic:OnStartBtnClick, map not open!")
                        Field:ResetToDefaultMaps()
                        return
                    end
                end
            end

            if not BattlefieldPrepareLogic.CheckTeammateIsReady() then
                return
            end
            BattlefieldPrepareLogic.CaptainStartMatch()
        end,
        fEnter = function()
        end,
        fLeave = function()
        end,
        fGetStartButtonText = function(self)
            if Server.TeamServer:IsCaptial() and Server.TeamServer:IsInTeam() and Server.TeamServer:GetTeamNum() > 1 then
                local readyNum, totalNum = BattlefieldPrepareLogic.GetReadyAndTotalNum()
                local text = string.format(Config.Loc.StartMatch, readyNum, totalNum)
                return text
            else
                return Config.Loc.Prepare[Config.EPrepareType.Captain]
            end
        end
    },
    [Config.EPrepareType.Matching] = {
        fStartClicked = function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIClick)
            if Server.TeamServer:IsCaptial() or Server.TeamServer:GetTeamNum() <= 1 then
                Server.TeamServer:SendCancelMatching()
            else
                Module.CommonTips:ShowSimpleTip(Config.Loc.OnlyCaptain)
            end
        end,
        fEnter = function()
        end,
        fLeave = function()
        end,
        fGetStartButtonText = function(self)
            if Server.TeamServer:IsCaptial() or not Server.TeamServer:IsInTeam() or Server.TeamServer:GetTeamNum() <= 1 then
                return Config.Loc.CancelMatch
            else
                return Config.Loc.Matching
            end
        end
    },

}

function BattlefieldPrepareLogic.InitEvents()
    _allEvents.hMatchGatePunish =
    Server.TeamServer.Events.evtMatchGatePunishFailed:AddListener(BattlefieldPrepareLogic._OnMatchGatePunishFailed)
    _allEvents.hReadyStateChanged =
    Server.TeamServer.Events.evtReadyStateChanged:AddListener(BattlefieldPrepareLogic._OnReadyStateChanged)
    _allEvents.hJoinTeam =
    Server.TeamServer.Events.evtJoinTeam:AddListener(BattlefieldPrepareLogic._OnJoinTeam)
    _allEvents.hAlreadyEnterMatch =
    Server.TeamServer.Events.evtAlreadyEnterMatch:AddListener(BattlefieldPrepareLogic.OnPrepareStateRefreshed)
    _allEvents.hTeamMemberUpdated =
    Config.evtBattlefieldEntryTeamMemberUpdated:AddListener(BattlefieldPrepareLogic._OnTeamMemberChanged)
    _allEvents.hEndMatching =
    Server.MatchServer.Events.evtEndMatching:AddListener(BattlefieldPrepareLogic._OnEndMatching)
    _allEvents.hStartMatching =
    Server.MatchServer.Events.evtStartMatching:AddListener(BattlefieldPrepareLogic._OnStartMatching)
    _allEvents.hNewSeasonComing =
    Server.TournamentServer.Events.evtNewSeasonComing:AddListener(BattlefieldPrepareLogic._OnNewSeasonComing)
    _allEvents.hTeammateStateChanged =
    Server.TeamServer.Events.evtTeammateStateChanged:AddListener(BattlefieldPrepareLogic._OnTeammateStateChanged)
end

function BattlefieldPrepareLogic.RemoveEvents()
    Server.TeamServer.Events.evtMatchGatePunishFailed:RemoveListenerByHandle(_allEvents.hMatchGatePunish)
    Server.TeamServer.Events.evtReadyStateChanged:RemoveListenerByHandle(_allEvents.hReadyStateChanged)
    Server.TeamServer.Events.evtJoinTeam:RemoveListenerByHandle(_allEvents.hJoinTeam)
    Server.TeamServer.Events.evtAlreadyEnterMatch:RemoveListenerByHandle(_allEvents.hAlreadyEnterMatch)
    Server.MatchServer.Events.evtStartMatching:RemoveListenerByHandle(_allEvents.hStartMatching)
    Config.evtBattlefieldEntryTeamMemberUpdated:RemoveListenerByHandle(_allEvents.hTeamMemberUpdated)
    Server.MatchServer.Events.evtEndMatching:RemoveListenerByHandle(_allEvents.hEndMatching)
    Server.TournamentServer.Events.evtNewSeasonComing:RemoveListenerByHandle(_allEvents.hNewSeasonComing)
    Server.TeamServer.Events.evtTeammateStateChanged:RemoveListenerByHandle(_allEvents.hTeammateStateChanged)
    _allEvents = {}
end

function BattlefieldPrepareLogic.SetPrepareState(newState)
    logwarning("[BattlefieldPrepareLogic]setpreparestate:",newState)
    local preState = BattlefieldPrepareLogic.GetPrepareState()
    Module.BattlefieldEntry.Field:SetPrepareState(newState)
    Module.BattlefieldEntry.Config.evtPrepareStateChanged:Invoke(newState, preState)
end

function BattlefieldPrepareLogic.GetPrepareState()
    return Module.BattlefieldEntry.Field:GetPrepareState()
end

function BattlefieldPrepareLogic.GetStateMap()
    return _stateMap
end

function BattlefieldPrepareLogic.GetReadyAndTotalNum()
    local members = Server.TeamServer:GetMembers()
    local readyNum = 0
    local totalNum = Server.TeamServer:GetTeamNum()
    for _, v in pairs(members) do
        if Server.TeamServer:IsCaptial(v.PlayerID) or
            (v.State & TeamMemberState.MemReady ~= 0 and v.State & TeamMemberState.MemInMatch == 0) then
            readyNum = readyNum + 1
        end
    end
    return readyNum, totalNum
end

function BattlefieldPrepareLogic.CheckTeammateIsReady()
    --检查队友是否未准备
    if Server.TeamServer:GetTeamNum()>1 and Server.TeamServer:IsCaptial() then
        local readyNum, totalNum = BattlefieldPrepareLogic.GetReadyAndTotalNum()
        loginfo("readyNum",readyNum,"totalNum",totalNum)
        if readyNum~=totalNum then
            local otherMembers=Server.TeamServer:GetOtherMembers()
            local notReadyMembers={}
            for k,v in pairs(otherMembers)do
                if not Server.TeamServer:IsReady(v.PlayerID) or Server.TeamServer:IsInMatch(v.PlayerID) then
                    table.insert(notReadyMembers,v)
                end
            end
            local tips=""
            local length=#notReadyMembers
            for k,v in pairs(notReadyMembers)do
                tips=tips..v.PlayerName
                if k~=length then
                    tips=tips..","
                end
            end
            Module.CommonTips:ShowSimpleTip(string.format(Server.TeamServer.Loc.TeammateCantStart,tips))
            return false
        else
            return true
        end
    end
    return true
end

function BattlefieldPrepareLogic.CaptainStartMatch()
    -- 判断地图列表是否为空
    local modeList = Field:GetModeList()
    if #modeList == 0 then
        Module.CommonTips:ShowSimpleTip(Config.Loc.MapListShouldNotEmpty)
        return
    end
    -- 注意组队入局也需要刷新PreparationModule里面的PrepareMPMainPanel的队友列表
    if Server.TeamServer:IsInTeam() then
        -- 需要检查队友是否都准备好了
        if not Server.TeamServer:CheckCanStartMatch() then
            loginfo("Could not start match with team currently.")
            -- 照发匹配请求 会有错误ntf
            Server.TeamServer:SendTeamMatching()
            return
        end
        Server.TeamServer:SendTeamMatching()
    else
        local fSendMatch = function ()
            Server.TeamServer:SendPersonMatchingWithParams(modeList, false, 0, Field:GetGroupId())
        end
        if Server.RoomServer:GetIsInRoom() then
            Module.Room:ExitRoom(fSendMatch)
        else
            fSendMatch()
        end
    end
end

function BattlefieldPrepareLogic.OnMapBtnClicked()
    if DFHD_LUA == 1 then
        LogAnalysisTool.SignButtonClicked(10120007)
        loginfo("[BattleField] mp hd click map")
    else
        LogAnalysisTool.SignButtonClicked(20120005)
    end
    if Server.MatchServer:GetIsMatching() then
        Module.CommonTips:ShowSimpleTip(Config.Loc.CantChangeMapWhenMatching)
        return
    end
    EntryLogic.OpenModeSelector()
end

function BattlefieldPrepareLogic.OnStartBtnClicked()
    local prepareState = BattlefieldPrepareLogic.GetPrepareState()
    logwarning("[BattlefieldPrepare] View:_OnStartBtnClicked", prepareState)
    local stateInfo = _stateMap[prepareState]
    if stateInfo ~= nil then
        stateInfo.fStartClicked()
        Module.BattlefieldEntry.Config.evtStartClicked:Invoke()
    end
end

function BattlefieldPrepareLogic.GenerateMatchModeInfoByMatchId(matchId)
    matchId = matchId or 0
    local matchModeConfig = Facade.TableManager:GetTable("MatchModeDataConfig")
    local matchModeRow = matchModeConfig[matchId]
    matchModeRow = matchModeRow or {}
    local matchModeInfo = {}
    matchModeInfo.team_mode = matchModeRow.TeamMode
    matchModeInfo.map_id = matchModeRow.MapID
    matchModeInfo.game_rule = matchModeRow.GameRule
    matchModeInfo.raid_id = matchModeRow.RaidID
    matchModeInfo.sub_mode = matchModeRow.MatchSubMode
    matchModeInfo.match_mode_id = matchModeRow.MatchModeID
    matchModeInfo.game_mode = matchModeRow.GameMode
    matchModeInfo.add_member_type = 1
    matchModeInfo.equip_price = 0
    matchModeInfo.spawn_point = 0

    return matchModeInfo
end

function BattlefieldPrepareLogic.InitMapInfo()
    loginfo("BattlefieldPrepareLogic.InitMapInfo")
    local fRequestCallback=function()
        local mapInfoList={}
        for k,v in pairs(Server.GameModeServer:GetAvailableTDMGroup() or {}) do
            for i,j in pairs(Server.GameModeServer:GetTDMMapInGroup(v) or {}) do
                local mapData=Module.BattlefieldEntry:GetMapDataByMatchId(j.match_id,v)
                table.insert(mapInfoList,{
                    groupId=j.group_id,
                    matchId=j.match_id,
                    mapId=j.mode and j.mode.map_id,
                    modeName=mapData and mapData.ModeName,
                    mapName=mapData and mapData.MapName,
                    modeLabel=mapData and mapData.ModeLabel,
                    modeLabelNeedReddot=mapData and mapData.ModeLabelNeedReddot,
                    mapLabel=mapData and mapData.MapLabel,
                    mapLabelNeedReddot=mapData and mapData.MapLabelNeedReddot,
                    groupMapSelectType=j.group_map_select_type,
                    groupMapSelectTypeParam=j.group_map_select_type_param,
                    isTournement=j.is_tournement,
                    modePlayerLv=j.mode_player_lv,
                    modePlayerRank=j.mode_player_rank,
                    lockReason=j.lock_reason,
                    startType=j.start_type,
                    loopCloseSec=j.loop_close_sec,
                    curStartStamp=j.cur_start_stamp,
                    curEndStamp=j.cur_end_stamp,
                    nextStartStamp=j.next_start_stamp,
                    nextEndStamp=j.next_end_stamp
                })
                
            end
        end
        loginfo("logTDMMapBoardDesc")
        for k,v in pairs(mapInfoList)do
            loginfo(v.groupId,v.matchId,v.mapId,v.modeName,v.mapName,v.modeLabel,v.modeLabelNeedReddot,v.mapLabel,v.mapLabelNeedReddot,
            v.groupMapSelectType,v.groupMapSelectTypeParam,v.isTournement,v.modePlayerLv,v.modePlayerRank,v.lockReason,v.startType,v.loopCloseSec,v.curStartStamp,v.curEndStamp,v.nextStartStamp,v.nextEndStamp)
            
        end
        -- 设置默认地图
        Field:ResetToDefaultMaps()
    end
    Server.GameModeServer:RequestTDMMapBoard(fRequestCallback)
end

function BattlefieldPrepareLogic._OnMatchGatePunishFailed(ntf)
    local mode = Server.ArmedForceServer:GetCurArmedForceMode()
    if mode == EArmedForceMode.SOL then
        return
    end

    if ntf then
        --队伍中有人被禁赛
        local playerName = ntf.punish_nick_name
        local punishInfo = ntf.punish_info
        loginfo("BattlefieldPrepareLogic:OnMatchGatePunishFailed", playerName)
        if playerName and punishInfo then
            local punishMsg = Server.AccountServer:SerializePunishNtfMessage(punishInfo)
            -- 直接解析封禁原因，如果解析失败就走旧有逻辑
            if punishMsg then
                Module.CommonTips:ShowSimpleTip(punishMsg)
                return
            end
            
            local puishReasonStr = ""
            if punishInfo.reason then
                local accountPunishData = Facade.TableManager:GetTable("AccountPunishReason")
                local findedMsgData = table.find(accountPunishData,
                    function(v, k) return k == tostring(punishInfo.reason) end)
                if findedMsgData then
                    puishReasonStr = findedMsgData.Message
                else
                    puishReasonStr = punishInfo.reason
                end
            end

            local limitTime = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(punishInfo.over_time)
            local tips = string.format(Module.Preparation.Config.Tips.MatchGatePunishFailed, playerName, puishReasonStr,
                limitTime)
            Module.CommonTips:ShowSimpleTip(tips)
        end
    end
end

function BattlefieldPrepareLogic._OnReadyStateChanged(playerID)
    if Server.MatchServer:GetIsMatching() then
        BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Matching)
        return
    end

    if playerID == Server.AccountServer:GetPlayerId() then
        BattlefieldPrepareLogic.SetPrepareState(Server.TeamServer:IsInTeam() and Server.TeamServer:IsCaptial() and
            Config.EPrepareType.Captain or Server.TeamServer:IsReady() and Config.EPrepareType.Ready or
            Config.EPrepareType.NotReady)
    end
end

function BattlefieldPrepareLogic._OnJoinTeam(teamID)
    if not Server.TeamServer:IsInTeam() or Server.TeamServer:IsCaptial() then
        BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Captain)
    else
        BattlefieldPrepareLogic.SetPrepareState(Server.TeamServer:IsReady() and Config.EPrepareType.Ready or
            Config.EPrepareType.NotReady)
    end
end

function BattlefieldPrepareLogic._OnTeamMemberChanged()
    BattlefieldPrepareLogic.OnPrepareStateRefreshed()
end

function BattlefieldPrepareLogic._OnTeammateStateChanged()
    BattlefieldPrepareLogic.OnPrepareStateRefreshed()
end

function BattlefieldPrepareLogic.OnPrepareStateRefreshed()
    -- 更新状态
    if Server.MatchServer:GetIsWaitForGotoGame() then
        loginfo("[BattlefieldPrepareLogic] GetIsWaitForGotoGame no need change startText")
        return
    end
    if Server.MatchServer:GetIsMatching() then
        -- 匹配中
        BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Matching)
        return
    end

    if Server.TeamServer:IsInTeam() and Server.TeamServer:GetTeamNum() > 1 then
        -- 在队伍中
        if Server.TeamServer:IsCaptial() then
            -- 且是队长
            BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Captain)
        elseif Server.TeamServer:IsReady() then
            -- 不是队长但已准备
            BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Ready)
        else
            -- 未准备
            BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.NotReady)
        end
    else
        BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Captain)
    end
end

function BattlefieldPrepareLogic._OnStartMatching()
    loginfo("[BattlefieldPrepareLogic] _OnStartMatching")
    BattlefieldPrepareLogic.SetPrepareState(Config.EPrepareType.Matching)
end

function BattlefieldPrepareLogic._OnEndMatching()
    loginfo("[BattlefieldPrepareLogic] _OnEndMatching")
    BattlefieldPrepareLogic.OnPrepareStateRefreshed()
    if Server.MatchServer:GetIsIdle() then
        Module.LitePackage:CheckWIFIDownloadQuests(2)
    end
end

function BattlefieldPrepareLogic._OnNewSeasonComing()
    loginfo("BattlefieldPrepareLogic._OnNewSeasonComing")
    local prepareState=BattlefieldPrepareLogic.GetPrepareState()
    loginfo("BattlefieldPrepareLogic._OnNewSeasonComing", prepareState,Server.TeamServer:IsMember())
    if prepareState==Config.EPrepareType.Matching and not Server.TeamServer:IsMember() then
        Server.TeamServer:SendCancelMatching()
    elseif prepareState==Config.EPrepareType.Ready then
        Server.TeamServer:SendMatchReady(false)
    end
end

function BattlefieldPrepareLogic.GetMapName()
    local modeList = Module.BattlefieldEntry.Field:GetModeList()
    local groupId = Module.BattlefieldEntry.Field:GetGroupId()
    local mapName = Module.BattlefieldEntry:Modes2String(modeList)

    logwarning("PrepareRegion:_RefreshMapName", mapName)
    return mapName
end

function BattlefieldPrepareLogic.GetConfig()
    return Config
end

function BattlefieldPrepareLogic.GetMatchId()
    return Field:GetMatchId()
end

function BattlefieldPrepareLogic.SetMatchId(matchId)
    return Field:SetMatchId(matchId)
end

function BattlefieldPrepareLogic.CheckRankLockedBySeasonLevel()
    local data=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleScoreMP)
    if data then
        return not data.bIsUnlocked,data.unlock1Condition,data.unlocktips
    end
    return false,0,''
end

function BattlefieldPrepareLogic.CheckIsModeLockedByPlayerLevel()
    loginfo("BattlefieldPrepareLogic.CheckIsModeLockedByPlayerLevel")
    local mapList=Module.BattlefieldEntry:GetSelectedMapList()
    for k,v in pairs(mapList or {})do
        if Server.RoleInfoServer.accountLevel<v.mode_player_lv then
            return true,v.mode_player_lv
        end
    end
    return false
end

function BattlefieldPrepareLogic.CheckIsModeLockedByRankScore()
    loginfo("BattlefieldPrepareLogic.CheckIsModeLockedByRankScore")
    local mapList=Module.BattlefieldEntry:GetSelectedMapList()
    local maxRankScore=Server.TournamentServer:GetMaxRankScore()
    for k,v in pairs(mapList or {})do
        if maxRankScore<v.mode_player_rank then
            return true,v.mode_player_rank
        end
    end
    return false
end

function BattlefieldPrepareLogic.CheckIsModeLockedByCommanderCondition()
    loginfo("BattlefieldPrepareLogic.CheckIsModeLockedByCommanderCondition")
    local isUnlock,unlockDesc=Server.TeamServer:GetTeamNum()>=2 and true or Module.BattlefieldEntry:CheckIsCommanderUnlock()
    return not isUnlock,unlockDesc
end

function BattlefieldPrepareLogic.CheckIsSupportTournament()
    for k,v in pairs(Module.BattlefieldEntry:GetSelectedMapList() or {}) do
        if not v.is_tournement then
            return false
        end
    end
    return true
end

function BattlefieldPrepareLogic.CheckMapIsDownload()--全选模式需要全部下载，其他模式只下载一个就行了
    --- BEGIN Virtuos Modification @ PengBoFeng
    loginfo("BattlefieldPrepareLogic.CheckMapIsDownload",Module.ExpansionPackCoordinator:IsSupportLitePackage(),IsPS5Family())
    if Module.ExpansionPackCoordinator:IsSupportLitePackage() or IsPS5Family() then --PS5 For PlayGO
    --- END Modification
        local modeList=Module.BattlefieldEntry:GetSelectedModeList()
        if modeList and modeList[1] then
            local groupId=Module.BattlefieldEntry:GetGroupId()
            local groupMapSelectType=Server.GameModeServer:GetTDMGroupMapSelectType(groupId)
            local downloadedNum=0
            for key, value in pairs(modeList or {}) do
                local mapId=value.map_id
                local moduleName = Module.ExpansionPackCoordinator:GetModuleNameByMapId(mapId)
                local isDownload = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleName)
                if isDownload then
                    downloadedNum=downloadedNum+1
                end
            end
            loginfo("BattlefieldPrepareLogic.CheckMapIsDownload",groupMapSelectType,downloadedNum,#modeList)
            if groupMapSelectType==1 then
                if downloadedNum==#modeList then
                    return true
                else
                    Module.CommonTips:ShowSimpleTip(Module.BattlefieldEntry.Config.Loc.Type1NeedDownloadAllMap)--全选模式需要全部下载
                    return false
                end
            else
                if downloadedNum>0 then
                    return true
                else
                    --- BEGIN Virtuos Modification @ PengBoFeng
                    if IsPS5Family() then
                        Module.CommonTips:ShowSimpleTip(Module.Preparation.Config.Tips.MapNotReady) --For PlayGo Miss Map Tips
                    else
                        Module.Social:ShowNotDownloadTips(modeList[1] and modeList[1].map_id,Module.BattlefieldEntry:GetGroupId())
                    end
                    --- END Modification
                    return false
                end
            end
        else
            logerror("BattlefieldPrepareLogic.CheckMapIsDownload, modeList not valid!!!")
            return true
        end
    else
        return true
    end

    
end

function BattlefieldPrepareLogic.CheckIsNeedDownloadAllMap()--单人/队伍中有一人段位分达到指定分数，全队需要下载所有地图
    loginfo("BattlefieldPrepareLogic.CheckIsNeedDownloadAllMap")
    local allMapLimitScore=Server.GameModeServer:GetTDMAllMapLimitScore()
    if Server.TeamServer:GetTeamNum()>1 then
        local isMemberOverLimitScore=false
        for k,v in pairs(Server.TeamServer:GetMembers()) do
            if v.PlayerSimpleInfo and v.PlayerSimpleInfo.mp_rank_attended and v.PlayerSimpleInfo.mp_rank_score>=allMapLimitScore then
                isMemberOverLimitScore=true
                break
            end
        end
        if isMemberOverLimitScore then
            for k,v in pairs(Module.BattlefieldEntry:GetSelectedModeList()) do
                local moduleName=Module.ExpansionPackCoordinator:GetModuleNameByMapId(v.map_id)
                if moduleName then
                    if not Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleName) then
                        local rankData=Module.Tournament:GetRankDataByScore(allMapLimitScore)
                        local wordParameterTable=Facade.TableManager:GetTable("TournamentWordParameter")
                        local parameterRow=wordParameterTable and wordParameterTable["TournamentPlayConditionTips"]
                        local playConditionTips=parameterRow and parameterRow.Value
                        local tipsStr=string.format(playConditionTips or Module.BattlefieldEntry.Config.Loc.TeamNeedDownloadAllMapTips,rankData and rankData.Type or "?")
                        Module.CommonTips:ShowConfirmWindow(tipsStr,function()Module.ExpansionPackCoordinator:ShowExpansionPackDownloadPanel()end,nil,nil,Module.BattlefieldEntry.Config.Loc.Download)
                        return true
                    end
                else
                    logwarning("BattlefieldPrepareLogic.CheckIsNeedDownloadAllMap, moduleName is nil!")--对于进首包的地图，moduleName为nil
                end
            end
        end
        return false
    else
        if Server.TournamentServer:GetHasAttended() and Server.TournamentServer:GetRankScore()>=allMapLimitScore then
            for k,v in pairs(Module.BattlefieldEntry:GetSelectedModeList()) do
                local moduleName=Module.ExpansionPackCoordinator:GetModuleNameByMapId(v.map_id)
                if moduleName then
                    if not Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleName) then
                        local rankData=Module.Tournament:GetRankDataByScore(allMapLimitScore)
                        local wordParameterTable=Facade.TableManager:GetTable("TournamentWordParameter")
                        local parameterRow=wordParameterTable and wordParameterTable["TournamentPlayConditionTips"]
                        local playConditionTips=parameterRow and parameterRow.Value
                        local tipsStr=string.format(playConditionTips or Module.BattlefieldEntry.Config.Loc.NeedDownloadAllMapTips,rankData and rankData.Type or "?")
                        Module.CommonTips:ShowConfirmWindow(tipsStr,function()Module.ExpansionPackCoordinator:ShowExpansionPackDownloadPanel()end,nil,nil,Module.BattlefieldEntry.Config.Loc.Download)
                        return true
                    end
                else
                    logwarning("BattlefieldPrepareLogic.CheckIsNeedDownloadAllMap, moduleName is nil!")--对于进首包的地图，moduleName为nil
                end
            end
        end
        return false
    end
end

--ban图数量=未选择地图+未下载地图
--ban图数量不能大于可ban数量
function BattlefieldPrepareLogic.CheckIsMapEnough()
    loginfo("BattlefieldPrepareLogic.CheckIsMapEnough")
    if Server.GameModeServer:GetTDMGroupMapSelectType(Module.BattlefieldEntry:GetGroupId())~=4 then return true end

    local groupId=Module.BattlefieldEntry:GetGroupId()
    local openedMaps=Server.GameModeServer:GetOpenedMapInGroup(groupId)
    local selectedModeList=Module.BattlefieldEntry:GetSelectedModeList(groupId)
    local groupMapSelectParam=Server.GameModeServer:GetTDMGroupMapSelectParam(groupId)
    
    local selectedMatchIds={}
    for k,v in pairs(selectedModeList or {})do
        table.insert(selectedMatchIds,v.match_mode_id)
    end
    local banNum=0
    for k,v in pairs(openedMaps or {})do
        if not table.contains(selectedMatchIds,v.match_id) then
            banNum=banNum+1
        else
            local moduleName=Module.ExpansionPackCoordinator:GetModuleNameByMapId(v.mode and v.mode.map_id)
            if not Module.ExpansionPackCoordinator:IsDownloadedByModuleName(moduleName) then
                banNum=banNum+1
            end
        end
    end
    logerror("BattlefieldPrepareLogic.CheckIsMapEnough",groupMapSelectParam,banNum)
    if banNum>groupMapSelectParam then
        local opendMapNum=openedMaps and #openedMaps or 0
        local tipStr=string.format(Module.BattlefieldEntry.Config.Loc.MapRequiredNum,opendMapNum-groupMapSelectParam)
        Module.CommonTips:ShowSimpleTip(tipStr)
    end
    return banNum<=groupMapSelectParam
end

return BattlefieldPrepareLogic
