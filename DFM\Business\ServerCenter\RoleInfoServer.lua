----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RoleInfoServer : ServerBase
local RoleInfoServer                             = class("RoleInfoServer",
    require("DFM.YxFramework.Managers.Server.ServerBase"))
local WeaponAssemblyTool                         = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool                             = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
-- BEGIN MODIFICATION - VIRTUOS
local UDFMOnlineIdentityManager                  = import "DFMOnlineIdentityManager"
local DFMOnlineIdentityManager                   = UDFMOnlineIdentityManager.Get(GetWorld())
local UDFMOnlineIdentityProxy_CheckUserPrivilege = import "DFMOnlineIdentityProxy_CheckUserPrivilege"
-- END MODIFICATION - VIRTUOS
local function xxww_(...)
    loginfo("", ...)
end

local SocialType = {
    Avatar = 1,
    Military = 2,
    Title = 3,
}

function RoleInfoServer:Ctor()
    --角色品质
    ---@type table<idx, pb_RoleQualityCard>
    self.qualityCardList = {}
    --战绩
    self.recordList = {}
    self.seasonLevel = 0
    self.seasonName = "" --- 预留
    self.selfSeasonLevel = 0

    --角色品质声望相关配置
    ---@type table<idx, pb_RoleQualityConfig>
    self.qualityConfigs = {}

    --角色品质声望升级相关配置
    ---@type table<idx, pb_RoleLoadValue>
    self.loadConfigs = {}
    ---@type table<idx, pb_RepLevelConfig>
    self.repLevelConfigs = {}
    self.mapInfo = {}

    -- 账号等级
    self.accountLevel = 0
    -- 账号等级当前经验
    self.accountExp = 0
    -- -- 账号等级下一个等级所需经验
    self.accountNextLevelExp = 0
    -- 账号当前等级相对经验
    self.accountRelativeExp = 0

    self.deltaExp = 0

    self.seasonExpDetail = {}

    self.accountDeltaExp = 0

    -- 账号等级解锁奖励
    self.accountUnlockItems = {}
    -- 最大账号等级
    self.accountMaxLevel = 0
    -- sol战斗次数
    self.solTotalFight = 0
    -- 自己装配的军牌id
    self.militaryId = 0
    -- 装备的称号id
    self.titleId = 0

    self.netbarPriv = 0

    self.stuPriv = 0

    self._playerBasicInfo = nil -- 当前打开的玩家的个人信息页的 basicinfo

    -- BEGIN MODIFICATION - VIRTUOS
    -- 是否可以跨平台游玩
    self.can_cross_plat_play = true
    self.hasCheckedPlatformCrossPlayPrivilege = false
    -- END MODIFICATION - VIRTUOS

    -- 回流活动标签和回流结束时间
    self.return_user_tag = 0 --此处tag是服务器enum格式！！！
    self.return_user_expire_time = 0

    self.Events = {
        --战区经验发生改变事件
        evtRoleDataChange = LuaEvent:NewIns("RoleInfoServer.evtRoleDataChange"),

        evtRefreshRoleInfoTab = LuaEvent:NewIns("RoleInfoServer.evtRefreshRoleInfoTab"),
        evtRefreshRoleQualityCardTab = LuaEvent:NewIns("RoleInfoServer.evtRefreshRoleQualityCardTab"),
        evtRoleQualityConfigFetched = LuaEvent:NewIns("RoleInfoServer.evtRoleQualityConfigFetched"),
        evtAttributeConfigFetched = LuaEvent:NewIns("RoleInfoServer.evtAttributeConfigFetched"),
        evtRefreshRecordListTab = LuaEvent:NewIns("RoleInfoServer.evtRefreshRecordListTab"),
        evtRefreshSeasonGetTotalData = LuaEvent:NewIns("RoleInfoServer.evtRefreshSeasonGetTotalData"),
        evtRefreshSeasonData = LuaEvent:NewIns("RoleInfoServer.evtRefreshSeasonData"),
        evtRefreshSingleHistoryData = LuaEvent:NewIns("RoleInfoServer.evtRefreshSingleHistoryData"),
        evtRoleQualityUpdate = LuaEvent:NewIns("RoleInfoServer.evtRoleQualityUpdate"),
        evtRefreshSolDate = LuaEvent:NewIns("RoleInfoServer.evtRefreshSolDate"),
        evtRefreshRaidDate = LuaEvent:NewIns("RoleInfoServer.evtRefreshRaidDate"),
        evtRefreshMPDate = LuaEvent:NewIns("RoleInfoServer.evtRefreshMPDate"),
        evtRefreshSolRecord = LuaEvent:NewIns("RoleInfoServer.evtRefreshSolRecord"),
        evtRefreshRaidRecord = LuaEvent:NewIns("RoleInfoServer.evtRefreshRaidRecord"),
        evtRefreshMPRecord = LuaEvent:NewIns("RoleInfoServer.evtRefreshMPRecord"),
        evtRefreshDetailRecord = LuaEvent:NewIns("RoleInfoServer.evtRefreshDetailRecord"),
        evtRefreshMPDetailRecord = LuaEvent:NewIns("RoleInfoServer.evtRefreshMPDetailRecord"),
        evtRefreshPlayerCard = LuaEvent:NewIns("RoleInfoServer.evtRefreshPlayerCard"),

        evtPrestigeLvUpgraded = LuaEvent:NewIns("RoleInfoServer.evtPrestigeLvUpgraded"),

        evtRefreshRoleInfoPanel = LuaEvent:NewIns("RoleInfoServer.evtRefreshRoleInfoPanel"), --个人信息界面专用事件

        --赛季等级变更
        evtSeasonLevelUpdate = LuaEvent:NewIns("RoleInfoServer.evtSeasonLevelUpdate"),
        --账号等级变更
        evtAccountLevelUpdate = LuaEvent:NewIns("RoleInfoServer.evtAccountLevelUpdate"),

        evtGetBadgeShow = LuaEvent:NewIns("RoleInfoServer.evtGetBadgeShow"),

        evtRolePanelBadgeShow = LuaEvent:NewIns("RoleInfoServer.evtRolePanelBadgeShow"),

        evtMilitaryChange = LuaEvent:NewIns("RoleInfoServer.evtMilitaryChange"),
        evtTitleChange = LuaEvent:NewIns("RoleInfoServer.evtTitleChanged"),

        evtPlayerDeregister = LuaEvent:NewIns("RoleInfoServer.evtPlayerDeregister"),

        evtSocialAvatarChange = LuaEvent:NewIns("RoleInfoServer.evtSocialAvatarChange"),

        evtGetSocialAvatarDataEnd = LuaEvent:NewIns("RoleInfoServer.evtGetSocialAvatarDataEnd"),
        evtBHDPurchasedSignUpdate = LuaEvent:NewIns("RoleInfoServer.evtBHDPurchasedSignUpdate"),
        evtCommanderRankModeChanged = LuaEvent:NewIns("RoleInfoServer.evtCommanderRankModeChanged"),
        evtCommanderRankModeSelect = LuaEvent:NewIns("RoleInfoServer.evtCommanderRankModeSelect"),
        evtPlayerBasicInfoUpdate = LuaEvent:NewIns("RoleInfoServer.evtPlayerBasicInfoUpdate"),

        -- BEGIN MODIFICATION - VIRTUOS
        evtCrossPlayChanged = LuaEvent:NewIns("RoleInfoServer.evtCrossPlayChanged"), --跨平台游玩开关变更
        -- END MODIFICATION - VIRTUOS

        -- crashsight report start
        evtRoleNetbarStateChanged = LuaEvent:NewIns("RoleInfoServer.evtRoleNetbarStateChanged"),
        -- crashsight report end
    }

    self.Tables = {
        cardTable = Facade.TableManager:GetTable("RoleQualityConfig"),
    }
    self.SocialTables = Facade.TableManager:GetTable("SocialAvatarDataTable") ---@type FSocialAvatarDataTable[]
    self.id2SocialInfo = {} ---@type table<number, SocialInfoMisc>
    self.AvatarTbl = {} ---@type SocialInfoMisc
    self.MilitaryTbl = {} ---@type SocialInfoMisc
    self.TitleTbl = {} ---@type SocialInfoMisc
    self._BadgeShowTbl = {}

    self._socialDataTbl = {
        [SocialType.Avatar] = "AvatarTbl",
        [SocialType.Military] = "MilitaryTbl",
        [SocialType.Title] = "TitleTbl",
    }

    self._socialRedPoint = {}

    self._socialTypeRedPoint = {}
    for _, value in pairs(SocialType) do
        self._socialTypeRedPoint[value] = {}
    end
end

function RoleInfoServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSAttrQualityValueChangeNtf", self.OnAttrQualityValueChangeNtf, self)
    Facade.ProtoManager:AddNtfListener("CSAccountExpChangeNtf", self.OnAccountExpChangeNtf, self)
    Facade.ProtoManager:AddNtfListener("CSCollectionPropChangeNtf", self.FetchCollectionUnlockAvatars, self)
    Facade.ProtoManager:AddNtfListener("CSGetNetbarPrivNtf", self.OnFetchAccountProfile, self)
    Facade.ProtoManager:AddNtfListener("CSGetStuPrivNtf", self.OnGetCSGetStuPrivNtf, self)
    Facade.ProtoManager:AddNtfListener("CSUpdateReputationNtf", self.OnUpdateReputationNtf, self)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() and DFMOnlineIdentityManager then
        Server.AccountServer.Events.evtOnAccountLoginSuccess:AddListener(self.ReqGetPlatformCrossplayPrivilege, self)
        DFMOnlineIdentityManager.OnPlatformPrivilegesResetDelegate:Add(self.ReqGetPlatformCrossplayPrivilege, self)
    end
    -- END MODIFICATION - VIRTUOS
    -- 断线重连
    self:AddLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected, self)
end

function RoleInfoServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() and DFMOnlineIdentityManager then
        Server.AccountServer.Events.evtOnAccountLoginSuccess:RemoveListener(self.ReqGetPlatformCrossplayPrivilege, self)
        DFMOnlineIdentityManager.OnPlatformPrivilegesResetDelegate:Remove(self.ReqGetPlatformCrossplayPrivilege, self)
    end
    -- END MODIFICATION - VIRTUOS
    -- 断线重连
    self:RemoveLuaEvent(Facade.ProtoManager.Events.evtOnRelayConnected, self._OnRelayConnected)
end

function RoleInfoServer:OnAccountExpChangeNtf(ntf)
    logwarning(string.format("RoleInfoServer:OnAccountExpChangeNtf is InFrontEnd%s, CurrentGameFlow:%s",
        Facade.GameFlowManager:CheckIsInFrontEnd(), Facade.GameFlowManager:GetCurrentGameFlow()))
    self.seasonExpDetail = {}
    -- 赛季等级变化
    if ntf.source == ExpNtfChangeSource.season then
        local seasonChange = ntf.change
        self.seasonLevel = seasonChange.level -- 赛季等级
        self.seasonExp = seasonChange.exp -- 当前赛季经验
        self.seasonNextExp = seasonChange.next_level_exp -- 下一赛季等级所需经验
        if seasonChange then
            loginfo("CSAccountExpChangeNtf: exp==", seasonChange.exp, "; next_level_exp==", seasonChange.next_level_exp)
        end
        self.preSeasonLevel = seasonChange.old_level -- = 4; // 旧等级
        self.preSeasonExp = seasonChange.old_exp -- = 5; // 旧经验
        self.preSeasonDisExp = seasonChange.old_next_level_exp -- = 6; // 旧升级所需经验
        self.deltaExp = seasonChange.delta_exp -- = 7; // 经验变化量
        self.seasonExpDetail = seasonChange.add_exps
        if self.preSeasonLevel ~= 0 then
            self.Events.evtSeasonLevelUpdate:Invoke(self.seasonLevel, self.preSeasonLevel, true)
            self.Events.evtRoleDataChange:Invoke()
        end
    elseif ntf.source == ExpNtfChangeSource.account then
        self.preAccountLevel = self.accountLevel -- = 4; // 旧等级
        self.preAccountExp = self.accountExp -- = 5; // 旧经验
        self.preAccountRelativeExp = self.accountRelativeExp --// 旧相对经验
        self.preAccountDisExp = self.accountNextLevelExp -- = 6; // 旧升级所需经验

        local accountChange = ntf.account_change
        -- 账号等级
        self.accountLevel = accountChange.level
        LogAnalysisTool.SetPlayerInfoLevel(self.accountLevel)
        -- 账号等级当前经验
        self.accountExp = accountChange.relative_exp
        --账号等级相对经验
        self.accountRelativeExp = accountChange.relative_exp
        -- -- 账号等级下一个等级所需经验
        self.accountNextLevelExp = accountChange.next_level_exp
        self.accountDeltaExp = accountChange.add_exp
        if self.preAccountLevel ~= 0 then
            self.Events.evtAccountLevelUpdate:Invoke(self.accountLevel, self.preAccountLevel)
        end
    end
end

function RoleInfoServer:FetchServerData()

end

function RoleInfoServer:OnLoadingLogin2Frontend(gameFlowType)
    logerror("RoleInfoServer:OnLoadingLogin2Frontend  ddh ")
    self:FetchAllRoleInfoData()
end

function RoleInfoServer:OnLoadingGame2Frontend(gameFlowType)
    logerror("RoleInfoServer:OnLoadingGame2Frontend  ddh ")
    self:FetchAllRoleInfoData()
end

--局外到局内
function RoleInfoServer:OnLoadingFrontend2Game(gameFlowType)
    self.accountUnlockItems = {}
    self.AvatarTbl = {}
    self.MilitaryTbl = {}
    self.TitleTbl = {}
    self._BadgeShowTbl = {}
    self.id2SocialInfo = {}
end

function RoleInfoServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse or
        gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.LobbyBHD then
        self:FetchAllRoleInfo()
        -- BEGIN MODIFICATION - VIRTUOS
        if IsXSX() then
            self:ReqSetCrossplay(self.hasCrossPlayPrivilege)
        end
        -- END MODIFICATION - VIRTUOS
    end
end

-- 重启之后需要重新拉角色信息
function RoleInfoServer:OnPostRestartSystem()
    logerror("RoleInfoServer:OnPostRestartSystem  ddh ")
    self:FetchAllRoleInfoData()
end

function RoleInfoServer:FetchAllRoleInfoData()
    self:FetchAllRoleInfo()
    self:FetchAllRoleQualityCard()
    self:FetchAllRoleQualityConfig()
    self:FetchAllAttributeConfig()
    self:FetchRaidGameMapDataInit()
    self:FetchSelfRoleInfo()
    self:FetchAccountUnlockItems()
    self:FetchCollectionUnlockAvatars()
    self:FetchGetSelfBadgeShow()
    self:FetchAllMatchInfo()
    self:FetchReputationRankAward()
end

function RoleInfoServer:OnDestoryServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self:RemoveAllLuaEvent()
end

--此条接口只用于查看自己的个人信息，并存储到本server的变量中
function RoleInfoServer:FetchAllRoleInfo()
    logerror("RoleInfoServer seasonLevel FetchAllRoleInfo")
    local req = pb.CSAccountGetPlayerProfileReq:New()
    req.client_flag = 0
    req:Request(
        function(res)
            ---@field public account_exp number
            ---@field public account_level number
            ---@field public account_next_level_exp number
            if res.result == 0 then
                self.Events.evtSeasonLevelUpdate:Invoke(res.level, self.seasonLevel, false)
                logerror("RoleInfoServer seasonLevel =", res.level)
                self.seasonLevel = res.level
                self.seasonExp = res.exp
                self.seasonNextExp = res.next_level_exp
                self.tickCount = res.tick_count
                self.nickName = res.nick_name
                if IsBuildRegionCN() then
                    self.picUrl = res.pic_url ~= "" and res.pic_url or Server.SDKInfoServer:GetPictureUrl()
                else
                    self.picUrl = res.pic_url
                end
                self.gender = res.gender
                self.headFrame = res.head_frame
                self.registerTime = res.register_time
                self.playerId = Server.AccountServer:GetPlayerId()
                self.praisedNum = res.praised_num
                self.accountExp = res.account_exp
                logerror("RoleInfoServer accountLevel =", res.account_level)
                self.accountLevel = res.account_level
                self.accountNextExp = res.account_next_level_exp
                -- azhengzheng:缓存之前的等级信息防止NTF没下发导致结算获取到空
                self:_CachePreLevelMsg()
                self.accountRelativeExp = res.account_exp
                self.militaryId = res.military_tag
                self.titleId = res.title and res.title or 0
                self.titleAdcode = res.rank_title_adcode and res.rank_title_adcode or 0
                self.titleRankNo = res.rank_title_rank_no and res.rank_title_rank_no or 0
                --改名卡/cd时间
                self.used_rename_card = res.used_rename_card
                self.rename_cd_time = res.rename_cd_time

                self.netbarPriv = res.netbar_priv
                self.stuPriv = res.stu_priv
                self.game_center = res.game_center
                -- BEGIN MODIFICATION - VIRTUOS
                -- 是否可以跨平台游玩
                self.can_cross_plat_play = res.can_cross_plat_play
                -- 使用服务器值覆盖本地值
                if IsConsole() then
                    local _gameSetting = import("ClientGameSettingHD").Get()
                    local crossPlayFlag = self.can_cross_plat_play
                    if crossPlayFlag ~= _gameSetting.bCrossplay then
                        _gameSetting.bCrossplay = crossPlayFlag
                        _gameSetting:SaveToSaved()
                    end
                    if IsPS5() then
                        self.hasCrossPlayPrivilege = self.can_cross_plat_play
                        self._isPlayerProfileRetrieved = true
                        DFMOnlineIdentityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.CrossPlatform
                            , self.hasCrossPlayPrivilege)
                    end
                end
                -- END MODIFICATION - VIRTUOS

                --回流活动相关
                self.return_user_tag = res.return_user_tag
                self.return_user_expire_time = res.return_user_expire_time
                self.bhd_is_purchased = res.bhd_is_purchased

                self.Events.evtRefreshRoleInfoPanel:Invoke(res)
                LogAnalysisTool.SetPlayerInfoLevel(self.accountLevel)
                -- netbar状态写入crashsight
                self.Events.evtRoleNetbarStateChanged:Invoke(self.netbarPriv)
            else
                logerror("CSAccountGetPlayerProfileReq fail res.result =", res.result)
            end
            self.Events.evtRefreshRoleInfoTab:Invoke()
        end
        , { bNeedResendAfterReconnected = true }
    )
end

-- 获取BHD购买状态
function RoleInfoServer:FetchBHDPurchasedSign()
    local req = pb.CSAccountGetPlayerProfileReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                if self.bhd_is_purchased ~= res.bhd_is_purchased then
                    self.Events.evtBHDPurchasedSignUpdate:Invoke(res.bhd_is_purchased)
                    self.bhd_is_purchased = res.bhd_is_purchased
                end
            end
        end
    )
end

--获取改名卡时间
function RoleInfoServer:GetReNameCardInfo()
    local list = {
        used_rename_card = self.used_rename_card,
        rename_cd_time = self.rename_cd_time,
    }
    return list
end

function RoleInfoServer:_CachePreLevelMsg()
    if not self.preAccountLevel then
        self.preAccountLevel = self.accountLevel
        self.preAccountExp = self.accountExp
        self.preAccountRelativeExp = self.accountRelativeExp
    end
end

function RoleInfoServer:ReSetAccountLevelInfo()
    self.preAccountLevel = self.accountLevel
    self.preAccountExp = self.accountExp
    self.preAccountRelativeExp = self.accountRelativeExp
    self.preAccountDisExp = self.accountNextLevelExp
end

function RoleInfoServer:SetMilitaryId(militaryId)
    local req = pb.CSPlayerUpdateMilitaryTagReq:New()
    req.military_tag = militaryId
    req:Request(
        function(res)
            if res.result == 0 then
                self.militaryId = militaryId
                self.Events.evtMilitaryChange:Invoke(militaryId)
            end
        end
    )
end

function RoleInfoServer:SetTitleId(titleId)
    -- logerror("RoleInfoServer:SetMilitaryId ----> Unimplementd")
    local req = pb.CSPlayerUpdateTitleReq:New()
    req.title = titleId
    req:Request(---@param res pb_CSPlayerUpdateTitleRes
        function(res)
            if res.result == 0 then
                self.titleId = titleId

                local titleInfo = self.id2SocialInfo[titleId]
                if titleInfo then
                    self.titleAdcode = titleInfo.AdCode
                    self.titleRankNo = titleInfo.RankNo
                else
                    logerror("RoleInfoServer:SetTitleId titleInfo is nil", titleId)
                end

                self.Events.evtTitleChange:Invoke(titleId)
            else
                logerror("RoleInfoServer:SetTitleId fail res.result =", res.result)
            end
        end
    )
end

function RoleInfoServer:FetchCollectionUnlockAvatars(onSuccess)
    loginfo("RoleInfoServer:FetchCollectionUnlockAvatars")
    local req = pb.CSCollectionUnlockAvatarsReq:New()
    req:Request(
        function(res)
            ---@type pb_CSCollectionUnlockAvatarsRes
            if res.result == 0 then
                loginfo("RoleInfoServer:FetchCollectionUnlockAvatars OnRes")

                -- reset
                self.id2SocialInfo = {}
                self.AvatarTbl = {}
                self.MilitaryTbl = {}
                self.TitleTbl = {}

                -- flag
                local lockTbl = {}
                local limitTbl = {}
                local ShowTimeTable = {}
                ---@type table<any, pb_CollectionRankTitleInfo>
                local rankTitleInfoTbl = {}

                self._socialRedPoint = {}
                self._socialTypeRedPoint = {}

                for _, value in pairs(SocialType) do
                    self._socialTypeRedPoint[value] = {}
                end
                for index, info in ipairs(res.props) do
                    lockTbl[info.id] = true
                end
                for _, info in ipairs(res.limit_social_props) do
                    limitTbl[info.prop_id] = info
                end
                for _, info in ipairs(res.show_time_info) do
                    ShowTimeTable[info.prop_id] = info
                end
                for index, info in ipairs(res.rank_title_info) do
                    rankTitleInfoTbl[info.prop_id] = info
                end
                for i, info in pairs(self.SocialTables) do
                    local avatarId = info.AvatarID

                    ---@class SocialInfoMisc
                    local SocialInfo = {
                        AvatarType = info.AvatarType,
                        Islock = lockTbl[avatarId] and true or false,
                        AvatarID = avatarId,
                        AvatarDescription = info.AvatarDescription,
                        ResourceName = info.ResourceName,
                        OpenCollection = info.OpenCollection,
                        AvatarName = info.AvatarName,
                        OptionalResourceNames = info.OptionalResourceNames,
                        DisplayResource = info.DisplayResource,
                        JumpID = info.JumpID,
                        JumpIDMP = info.JumpIDMP,
                        ButtonDesc = info.ButtonDesc,
                        ButtonDescMP = info.ButtonDescMP,
                        ShopId = info.ShopId,
                        OrderWeight = info.OrderWeight,
                        UnlockPopType = info.UnlockPopType,
                        SocialType = LimitSocialTypeDef.NONE,
                        StartTime = 0,
                        EndTime = 0,
                    }
                    if limitTbl[avatarId] then
                        SocialInfo.SocialType = limitTbl[avatarId].type
                        SocialInfo.StartTime = limitTbl[avatarId].effective_start_time
                        SocialInfo.EndTime = limitTbl[avatarId].expired_time
                    end
                    if rankTitleInfoTbl[avatarId] then
                        SocialInfo.AdCode = rankTitleInfoTbl[avatarId].adcode
                        SocialInfo.RankNo = rankTitleInfoTbl[avatarId].rank_no
                    end

                    local bValid = false
                    if info.OpenCollection == 1 then
                        if SocialInfo.Islock == true then
                            bValid = true
                        else
                            if info.DisplayResource ~= 0 then
                                if (not ShowTimeTable[avatarId]) or self:_CheckTimeCanShow(ShowTimeTable[avatarId]) then
                                    bValid = true
                                end
                            end
                        end
                    end
                    if bValid then
                        table.insert(self[self._socialDataTbl[info.AvatarType]], SocialInfo)
                        -- index
                        self.id2SocialInfo[avatarId] = SocialInfo
                    end

                end

                for key, value in pairs(SocialType) do
                    self:SortSocialTbl(self[self._socialDataTbl[value]])
                end
                self:RefreshSocialReddotData(res.redpoint_list)
                if onSuccess and type(onSuccess) == "function" then
                    onSuccess()
                end
                self.Events.evtGetSocialAvatarDataEnd:Invoke()
            end
        end
        , { bEnableHighFrequency = true }
    )
end

function RoleInfoServer:_CheckTimeCanShow(timeInfo)
    local NowTime = Facade.ClockManager:GetServerTimestamp()
    if timeInfo.start_time > 0 and timeInfo.end_time > 0 then
        if NowTime > timeInfo.start_time and NowTime < timeInfo.end_time then
            return true
        else
            return false
        end
    elseif timeInfo.start_time > 0 then
        if NowTime > timeInfo.start_time then
            return true
        else
            return false
        end
    elseif timeInfo.end_time > 0 then
        if NowTime < timeInfo.end_time then
            return true
        else
            return false
        end
    end
end

function RoleInfoServer:SortSocialTbl(socialTbl)
    table.sort(socialTbl, function(a, b)
        local a1 = a.Islock and 1 or 0
        local b1 = b.Islock and 1 or 0
        if a1 ~= b1 then
            return a1 > b1
        end
        return a.AvatarID < b.AvatarID
    end)
end

function RoleInfoServer:FetchCollectionRedPoint()
    loginfo("RoleInfoServer:FetchCollectionUnlockAvatars")
    local req = pb.CSCollectionUnlockAvatarsReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                self:RefreshSocialReddotData(res.redpoint_list)
            end
        end
        , { bEnableHighFrequency = true })
end

function RoleInfoServer:RefreshSocialReddotData(reddotList)
    self._socialRedPoint = {}
    self._socialTypeRedPoint = {}
    self._socialReddotHash = {}
    for _, value in pairs(SocialType) do
        self._socialTypeRedPoint[value] = {}
    end
    if #reddotList > 0 then
        self._socialRedPoint = reddotList
        for _, info in ipairs(reddotList) do
            if self.SocialTables[info.prop_id] and info.num > 0 then
                table.insert(self._socialTypeRedPoint[self.SocialTables[info.prop_id].AvatarType], info)
                self._socialReddotHash[info.prop_id] = info
            end
        end
        self.Events.evtSocialAvatarChange:Invoke()
    else
        self.Events.evtSocialAvatarChange:Invoke()
    end
end

function RoleInfoServer:UpdateRedDot(items)
    local fOnCSCollectionUpdateRedDotRes = function(res)
        if res.result == 0 then
            self:FetchCollectionRedPoint()
        end
    end
    ---@field public prop_ids 道具id列表
    local req = pb.CSCollectionUpdateRedPointReq:New()

    local props = {}
    for key, item in pairs(items) do
        table.insert(props, { id = item.prop_id, gid = item.prop_gid })
    end

    loginfo("RoleInfoServer:UpdateRedDot")
    req.props = props
    req:Request(fOnCSCollectionUpdateRedDotRes, { bEnableHighFrequency = true })
    return true
end

function RoleInfoServer:GetSocialRedPoint()
    return self._socialRedPoint
end

function RoleInfoServer:GetSocialTypeRedPoint(socialType)
    return self._socialTypeRedPoint[socialType]
end

function RoleInfoServer:GetSocialReddotHash()
    return self._socialReddotHash
end

function RoleInfoServer:GetAvatarTbl()
    return self.AvatarTbl
end

function RoleInfoServer:GetMilitaryTbl()
    return self.MilitaryTbl
end

function RoleInfoServer:GetTitleTbl()
    return self.TitleTbl
end

function RoleInfoServer:SetPicUrl(url)
    self.picUrl = url
end

function RoleInfoServer:FetchRoleInfoByPlayerId(playerId)
    playerId = setdefault(playerId, Server.AccountServer:GetPlayerId())
    local req = pb.CSAccountGetPlayerProfileReq:New()
    req.client_flag = 0
    req.player_id = playerId

    req:Request(
        function(res)
            loginfo("jianwendeng " .. playerId .. "result :" .. res.result)

            if res.result == 0 then
                --如果是拉的自己的个人信息则更新
                if playerId == Server.AccountServer:GetPlayerId() then
                    self.militaryId = res.military_tag
                    self.picUrl = res.pic_url
                    self.titleId = res.title and res.title or 0
                end
                self.Events.evtRefreshRoleInfoPanel:Invoke(res)
            elseif res.result == 15109 then
                self.Events.evtPlayerDeregister:Invoke()
            end
        end
        , { bNeedResendAfterReconnected = true })
end

function RoleInfoServer:FetchSelfRoleInfo(playerId)
    playerId = setdefault(playerId, Server.AccountServer:GetPlayerId())
    local req = pb.CSAccountGetPlayerProfileReq:New()
    req.client_flag = 0
    req:Request(
        function(res)
            if res.result == 0 then
                self.selfSeasonLevel = res.level
            else
                logerror("CSAccountGetPlayerProfileReq fail res.result =", res.result)
            end
        end
    )
end

function RoleInfoServer:GetSeasonLevel()
    return self.selfSeasonLevel
end

function RoleInfoServer:FetchSolSeasonTotalDataByMatchInfo(mode, playerId, isCareer)
    isCareer = setdefault(isCareer, false)
    local req = pb.CSSeasonGetTotalDataReq:New()
    req.mode = mode
    req.player_id = playerId
    req.is_career = isCareer
    req:Request(
        function(res)
            if res.result == 0 then
                if res.sol_data then
                    local solData = res.sol_data
                    self.solTotalFight = solData.total_fight --总战局数
                    self.solTotalEscape = solData.total_escape --撤离成功数
                    self.solTotalGameTime = solData.total_game_time --游戏时长
                    self.solTotalKill = solData.total_kill --总击败数
                    self.solTotalKilled = solData.total_killed --总被击败数
                    self.solHelpMoneyCount = solData.carry_teammate_assets --累计帮带资产
                    self.solTotalPrice = solData.total_price
                    self.solTotalMiss = solData.total_miss --总被击败数
                    self.Events.evtRefreshSolDate:Invoke(res.sol_data)
                end
            end
        end, { bNeedResendAfterReconnected = true })
end

function RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo(mode, playerId, callback, isCareer)
    isCareer = setdefault(isCareer, false)
    local req = pb.CSSeasonGetTotalDataReq:New()
    req.mode = mode
    req.player_id = playerId
    req.is_career = isCareer
    -- logerror("RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo request", mode.game_mode, mode.game_rule, playerId)
    req:Request(
        function(res)
            -- logerror("RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo res")
            if res.result == 0 then
                if res.mp_data then
                    -- logerror("RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo res mpdata:", res.mp_data.total_fight)
                    local mpData = res.mp_data
                    self.mpTotalFight = mpData.total_fight --总战局数
                    self.mpTotalWin = mpData.total_win --撤离成功数
                    self.mpTotalKill = mpData.total_kill --游戏时长
                    self.mpTotalMvp = mpData.total_mvp --总击败数
                    self.mpTotalKilled = mpData.total_killed --总被击败数
                    self.mpTotalTime = mpData.total_game_time --游戏时间
                    self.Events.evtRefreshMPDate:Invoke(res.mp_data)
                end
            end
            if callback then
                callback(res.result)
            end
        end, { bNeedResendAfterReconnected = true, iDefaultSendNum = 100, bEnableHighFrequency = true })
end

function RoleInfoServer:FetchSolRecord(playerId, page)
    local req = pb.CSSeasonGetRecordListReq:New()
    req.mode = {}
    req.mode.game_mode = MatchGameMode.WorldGameMode
    --req.mode.game_rule = MatchGameRule.SOLGameRule
    loginfo("RoleInfoServer:FetchSolRecord" .. playerId)
    req.player_id = playerId
    req.page = 0
    req.num = 50
    req:Request(
        function(res)
            if res.result == 0 then
                self.Events.evtRefreshSolRecord:Invoke(res.record_list, res.is_hidden)
            end
        end
        , { bNeedResendAfterReconnected = true, bEnableHighFrequency = true })
end

function RoleInfoServer:FetchRaidRecord(playerId, page)
    local req = pb.CSSeasonGetRecordListReq:New()
    req.mode = {
        ["game_mode"] = MatchGameMode.WorldGameMode,
        ["game_rule"] = MatchGameRule.RaidGameRule
    }
    req.player_id = playerId
    req.page = 1
    req.num = 50
    req:Request(
        function(res)
            if res.result == 0 then
                self.raidRecordList = res.record_list
                self.raidPage = res.page
                self.raidNum = res.num
                self.raidMaxPage = res.max_page
                self.Events.evtRefreshRaidRecord:Invoke()
            end
        end
        , { bNeedResendAfterReconnected = true, bEnableHighFrequency = true })
end

function RoleInfoServer:FetchMPRecord(playerId, page)
    local req = pb.CSSeasonGetRecordListReq:New()
    req.mode = {
        ["game_mode"] = MatchGameMode.TDMGameMode
    }
    req.player_id = playerId
    req.page = 1
    req.num = 50
    req:Request(
        function(res)
            if res.result == 0 then
                self.Events.evtRefreshMPRecord:Invoke(res.record_list, res.is_hidden)
            end
        end
        , { bNeedResendAfterReconnected = true, bEnableHighFrequency = true })
end

function RoleInfoServer:FetchRaidGameMapDataInit()
    local req = pb.CSSeasonRaidMapListReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                self.mapInfo = {}
                for _, map in ipairs(res.maps) do
                    table.insert(self.mapInfo, map)
                end
            end
        end
    )
end

function RoleInfoServer:FetchSeasonTotalData()
    local req = pb.CSSeasonGetTotalDataReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                if res.sol_data then
                    self.solTotalFight = res.sol_data.total_fight
                end
                self.totalFight = res.total_fight
                self.kd = res.kd - res.kd % 0.01
                self.totalEscape = res.total_escaped
                self.avgSurvivalTime = res.avg_survival_time
                self.totalGameTime = res.total_game_time
            else
                logerror("CSSeasonGetTotalDataReq fail res.result =", res.result)
            end
            self.Events.evtRefreshSeasonGetTotalData:Invoke()
        end
    )
end

--SOL 详细数据
function RoleInfoServer:FetchSolSeasonDetailData(playerId, season, isRank, delayCall)
    local req = pb.CSSeasonGetDetailDataReq:New()
    local mode = {
        game_mode = MatchGameMode.WorldGameMode,
        game_rule = MatchGameRule.SOLGameRule,
    }
    req.mode = mode
    req.player_id = playerId
    req.season_no = season
    req.is_rank = isRank
    req:Request(
        function(res)
            if res.result == 0 or res.result == 137030 then
                if delayCall then
                    delayCall(res.sol_data)
                end
            end
        end
        , { bEnableHighFrequency = true, bNeedResendAfterReconnected = true })
end

--MP 详细数据
function RoleInfoServer:FetchMPSeasonDetailData(playerId, season, isRank, is_victory_unite, is_victory_unite_commander,
                                                delayCall)
    local req = pb.CSSeasonGetDetailDataReq:New()
    local mode = {
        game_mode = MatchGameMode.TDMGameMode,
    }
    req.mode = mode
    req.player_id = playerId
    req.season_no = season
    req.is_rank = isRank
    req.is_victory_unite = is_victory_unite -- 胜者为王模式
    req.is_victory_unite_commander = is_victory_unite_commander -- 胜者为王模式并担任指挥官
    req:Request(
        function(res)
            if res.result == 0 or res.result == 137030 then
                if delayCall then
                    delayCall(res.mp_data)
                end
            end
        end
        , { bEnableHighFrequency = true, bNeedResendAfterReconnected = true })
end

--sol战绩详细

--击杀列表
function RoleInfoServer:GetSolRecordKillList(playerId, roomId, callback)
    local req = pb.CSSeasonGetMatchKillDataReq:New()
    req.player_id = playerId
    req.room_id = roomId
    req:Request(
        function(res)
            if res.result == 0 then
                if callback then
                    callback(res.kill_array)
                end
            end
        end
    )
end

--淘汰详情
function RoleInfoServer:GetSolRecordDamageData(playerId, roomId, callback)
    local req = pb.CSSeasonGetMatchDeathHurtDataReq:New()
    req.player_id = playerId
    req.room_id = roomId
    req:Request(
        function(res)
            if res.result == 0 then
                if callback then
                    callback(res.death_hurt_data)
                end
            end
        end
    )
end

--行动收获
function RoleInfoServer:GetSolRewardData(playerId, roomId, callback)
    local req = pb.CSSeasonGetMatchGainedDataReq:New()
    req.player_id = playerId
    req.room_id = roomId
    req:Request(
        function(res)
            if res.result == 0 then
                if callback then
                    callback(res)
                end
            end
        end
    )
end

---@class pb_RoleQualityCard : ProtoBase
---@field public card_id number
---@field public quality_value number
---@field public is_unlock boolean
---@field public unlock_time number
---@field public is_disabled boolean
---@field public level number

--- 获取角色品质当前数据
function RoleInfoServer:FetchAllRoleQualityCard()
    local req = pb.CSAttrGetAllQualityReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                if res.all_quality then
                    ---@type table<idx, pb_RoleQualityCard>
                    self.qualityCardList = res.all_quality
                    self.Events.evtRoleQualityUpdate:Invoke()
                    self.Events.evtRefreshRoleQualityCardTab:Invoke()
                end
            else
                logerror("CSAttrGetQualityCardBatchReq fail res.result =", res.result)
            end
        end
        , { bNeedResendAfterReconnected = true })
end

function RoleInfoServer:OnAttrQualityValueChangeNtf(ntf)
    if ntf.change_list then
        local mapPid2LvChange = {}
        local bLvUp = false
        for k, v in ipairs(ntf.change_list) do
            local qualityData = self:GetRoleQualityDataByCardId(v.quality_id)
            qualityData.old_level = qualityData.level or v.old_rep_lvl or 1
            qualityData.level = v.new_rep_lvl
            qualityData.quality_value = v.new_rep_val
            if v.new_rep_lvl > v.old_rep_lvl then
                mapPid2LvChange[v.quality_id] = {
                    newLv = v.new_rep_lvl,
                    oldLv = v.old_rep_lvl,
                }
                bLvUp = true
            end
        end
        if bLvUp then
            self.Events.evtPrestigeLvUpgraded:Invoke(mapPid2LvChange)
        end
        self.Events.evtRoleQualityUpdate:Invoke()
        self.Events.evtRefreshRoleQualityCardTab:Invoke()
    end
end

---@class pb_RoleQualityConfig : ProtoBase
---@field public quality_id number
---@field public quest_id number
---@field public name string (客户端表)
---@field public img_path string (客户端表)
---@field public sprite_path string (客户端表)
---@field public quality_usages pb_RoleQualityUsage[]

--- 获取角色品质静态配置
function RoleInfoServer:FetchAllRoleQualityConfig()
    local req = pb.CSGetRoleQualityConfigReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                if res.configs then
                    ---@type table<idx, pb_RoleQualityConfig>
                    self.qualityConfigs = res.configs
                    local tab = self.Tables.cardTable
                    for idx, qualityConfig in ipairs(self.qualityConfigs) do
                        local row = tab[qualityConfig.quality_id]
                        qualityConfig.name = row.Name or ""
                        qualityConfig.img_path = row.ImgPath
                        qualityConfig.sprite_path = row.SpritePath
                        for i, usageConfig in ipairs(qualityConfig.quality_usages) do
                            usageConfig.desc = row['UsageList' .. i .. 'Desc'] or ""
                        end
                    end
                    xxww_("FetchAllRoleQualityConfig res.configs", #res.configs)
                    self.Events.evtRoleQualityConfigFetched:Invoke(res.configs)
                end
            else
                logerror("CSAttrGetQualityCardBatchReq fail res.result =", res.result)
            end
        end
    )
end

--- 获取声望升级信息
function RoleInfoServer:FetchAllAttributeConfig()
    local req = pb.CSAttributeGetConfigReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                if res.load_configs then
                    ---@type table<idx, pb_RoleLoadValue>
                    self.loadConfigs = res.load_configs
                end
                if res.rep_level_configs then
                    ---@type table<idx, pb_RepLevelConfig>
                    self.repLevelConfigs = res.rep_level_configs
                    -- xxww_("FetchAllAttributeConfig res.load_configs", #res.load_configs, "res.repLevelConfigs", #res.repLevelConfigs)
                    self.Events.evtAttributeConfigFetched:Invoke()
                end
            else
                logerror("FetchAllAttributeConfig fail res.result =", res.result)
            end
        end
        , { bEnableHighFrequency = true })
end

function RoleInfoServer:FetchRecordList()
    local req = pb.CSSeasonGetRecordListReq:New()
    req.page = 1
    req.num = 20
    req:Request(
        function(res)
            if res.result == 0 then
                if res.record_list then
                    self.recordList = res.record_list
                    xxww_(" RoleInfoServer:FetchRecordList #self.recordList ", #res.record_list)
                end
            else
                logerror("CSSeasonGetRecordListReq fail res.result =", res.result)
            end
            self.Events.evtRefreshRecordListTab:Invoke()
        end
    )
end

function RoleInfoServer:FetchSingleHistoryData(key, matchInfo, isShowTeam, playerId)
    isShowTeam = setdefault(isShowTeam, false)
    local req = pb.CSSeasonGetRecordReq:New()
    req.key = key
    req.mode = matchInfo
    req.show_teammates_only = isShowTeam
    req.show_player_id = playerId
    req:Request(
        function(res)
            if res.result == 0 then
                self.Events.evtRefreshDetailRecord:Invoke(res.player_record, res.game_result, res.client_group)
            end
        end
        , { bEnableHighFrequency = true })
end

function RoleInfoServer:FetchMatchScoreData(playerid, roomId, callback)
    local req = pb.CSSeasonGetMatchScoreDataReq:New()
    req.player_id = playerid
    req.room_id = roomId
    req:Request(
        function(res)
            if res.result == 0 then
                if callback then
                    callback(res)
                end
            end
        end
        , { bEnableHighFrequency = true })
end

function RoleInfoServer:FetchMpDetailRecordData(key, matchInfo, playerId)
    local req = pb.CSSeasonGetRecordReq:New()
    req.key = key
    req.mode = matchInfo
    req.show_teammates_only = true
    req.show_player_id = playerId
    req:Request(
        function(res)
            if res.result == 0 then
                self.Events.evtRefreshMPDetailRecord:Invoke(res.player_record, res.game_result)
            end
        end
        , { bEnableHighFrequency = true })
end

---@return pb_RoleQualityCard
function RoleInfoServer:GetRoleQualityDataByCardId(cardId)
    if self.qualityCardList then
        for idx, v in ipairs(self.qualityCardList) do
            if v.card_id == tonumber(cardId) then
                return v
            end
        end
        return {}
    end
end

---@return pb_RoleQualityConfig
function RoleInfoServer:GetRoleQualityConfigByCardId(cardId)
    if self.qualityConfigs then
        for idx, v in ipairs(self.qualityConfigs) do
            if v.quality_id == tonumber(cardId) then
                return v
            end
        end
    end
    return {}
end

function RoleInfoServer:GetAttrbuteLevelConfigByLevel(level)
    if self.repLevelConfigs then
        for idx, v in ipairs(self.repLevelConfigs) do
            if v.level == tonumber(level) then
                return v
            end
        end
    end
    return {}
end

function RoleInfoServer:GetPrestigeNameById(cardId)
    local name = ""
    if self.Tables.cardTable then
        for i, row in pairs(self.Tables.cardTable) do
            if row.QualityID == cardId then
                name = row.Name
            end
        end
    end
    return name
end

function RoleInfoServer:GetCardCurValueById(cardId)
    local prestigeData = self:GetRoleQualityDataByCardId(cardId)
    return prestigeData.quality_value
end

function RoleInfoServer:GetPrestigeCurValueAndScoreById(cardId)
    local prestigeData = self:GetRoleQualityDataByCardId(cardId)
    local curValue = prestigeData.quality_value
    local curLevelConfig = self:GetAttrbuteLevelConfigByLevel(prestigeData.level)
    local curScore = curLevelConfig.score and curLevelConfig.score or curValue
    prestigeData.level = prestigeData.level or 1
    if prestigeData.level > 1 then
        local addUpValue = self:GetPrestigeAddUpValueByLevel(prestigeData.level - 1)
        curValue = curValue - addUpValue
    end
    if curValue > curScore then
        logwarning('RoleInfoServer:GetPrestigeCurValueAndScoreById(cardId) curValue > curScore 请检查', curValue,
            curScore)
    end
    curScore = math.clamp(curScore, 0, curScore)
    curValue = math.clamp(curValue, 0, curScore)
    return curValue, curScore
end

function RoleInfoServer:GetPrestigeMaxLv()
    return #self.repLevelConfigs
end

function RoleInfoServer:GetPrestigeAddUpValueByLevel(level)
    local addUpValue = 0
    if self.repLevelConfigs then
        for idx, v in ipairs(self.repLevelConfigs) do
            if v.level <= tonumber(level) then
                addUpValue = addUpValue + v.score
            end
        end
    end
    return addUpValue
end

function RoleInfoServer:GetMapPid2CurPrestigeLv()
    local mapPid2PrestigeLv = {}
    if self.qualityCardList then
        for idx, v in ipairs(self.qualityCardList) do
            mapPid2PrestigeLv[v.card_id] = v.level
        end
    end
    return mapPid2PrestigeLv
end

function RoleInfoServer:GetPlayerCardById(playerId)
    local req = pb.CSFriendGetFriendCardReq:New()
    req.player_id = playerId
    req:Request(
        function(res)
            if res.result == 0 then
                self.Events.evtRefreshPlayerCard:Invoke(res.info)
            end
        end
    )
end

function RoleInfoServer:SetPlayerCard(playerId, cardInfo)
    local req = pb.CSFriendUpdateFriendCardReq:New()
    req.player_id = playerId
    req.info = cardInfo
    req:Request(
        function(res)
            if res.result == 0 then
            end
        end
    )
end

function RoleInfoServer:GetMPPlayerShowAvatarId(playerId, callBack)
    local req = pb.CSPlayerInfoGetAppearanceReq:New()
    req.player_id = playerId
    req.mode = MatchGameMode.TDMGameMode
    req:Request(
        function(res)
            if res.result == 0 then
                if callBack then
                    callBack(res)
                end
            end
        end
        , { bNeedResendAfterReconnected = true, bEnableHighFrequency = true })
end

function RoleInfoServer:GetSolPlayerShowAvatarId(playerId, callBack)
    local req = pb.CSPlayerInfoGetAppearanceReq:New()
    req.player_id = playerId
    req.mode = MatchGameMode.WorldGameMode
    req:Request(
        function(res)
            if res.result == 0 then
                if callBack then
                    callBack(res)
                end
            end
        end
        , { bNeedResendAfterReconnected = true, bEnableHighFrequency = true })
end

function RoleInfoServer:SavePlayerAppearance(mode)
    local req = pb.CSPlayerInfoSaveAppearanceReq:New()
    req.mode = mode
    req:Request(
        function(res)
            if res.result == 0 then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.SaveAppearanceSuccess)
            end
        end
        , { bNeedResendAfterReconnected = true })
end

function RoleInfoServer:FetchSelfBadgeShow(itemId, slot)
    local req = pb.CSHeroSetBadgeShowReq:New()
    req.badge = {
        prop_id = itemId,
        slot = slot,
    }
    req:Request(
        function(res)
            if res.result == 0 then
                self._BadgeShowTbl = {}
                for _, badge in ipairs(res.badges) do
                    self._BadgeShowTbl[badge.prop_id] = true
                end
                self.Events.evtGetBadgeShow:Invoke(res.badges)
            end
        end
        , { bEnableHighFrequency = true })
end

function RoleInfoServer:FetchGetSelfBadgeShow()
    local req = pb.CSHeroGetBadgeShowReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                self._BadgeShowTbl = {}
                for _, badge in ipairs(res.badges) do
                    self._BadgeShowTbl[badge.prop_id] = true
                end
                self.Events.evtGetBadgeShow:Invoke(res.badges)

            end
        end
        , { bEnableHighFrequency = true }
    )
end

function RoleInfoServer:FetchAllMatchInfo()
    --- 向服务器请求 Mp 与 Sol 对局数据, 阻止在主动打开Personal页面的脏数据被其他模块访问
    local playerId = Server.AccountServer:GetPlayerId()
    local isCareer = false -- WTF?

    local matchInfo1 = {}
    matchInfo1.game_mode = MatchGameMode.TDMGameMode
    matchInfo1.game_rule = 0
    Server.RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo(matchInfo1, playerId, nil, isCareer)

    local matchInfo2 = {}
    matchInfo2.game_mode = MatchGameMode.WorldGameMode
    matchInfo2.game_rule = MatchGameRule.SOLGameRule
    matchInfo2.sub_mode = 0
    Server.RoleInfoServer:FetchSolSeasonTotalDataByMatchInfo(matchInfo2, playerId, isCareer)
end

--排位/积分赛信誉分加成
function RoleInfoServer:FetchReputationRankAward()
    loginfo("RoleInfoServer:FetchReputationRankAward")
    local fGetReputationRankAwardRes = function(res)
        loginfo("RoleInfoServer:FetchReputationRankAward, fGetReputationRankAwardRes", res.result)
        if res.result == 0 then
            self.hasTDMReputationAward = res.IsTdmCanAward
            self.hasSOLReputationAward = res.IsSolCanAward
        end
    end
    local req = pb.CSGetReputationRankAwardReq:New()
    req:Request(fGetReputationRankAwardRes)
end

--获取排位赛信誉分加成
function RoleInfoServer:GetSOLReputationRankAward()
    return self.hasSOLReputationAward
end

--获取积分赛信誉分加成
function RoleInfoServer:GetTDMReputationRankAward()
    return self.hasTDMReputationAward
end

function RoleInfoServer:GetBadgeShowTbl()
    return self._BadgeShowTbl
end

function RoleInfoServer:FetchGetBadgeRolePanelShow(playerId)
    local req = pb.CSHeroGetBadgeShowReq:New()
    req.player_id = playerId
    req:Request(
        function(res)
            if res.result == 0 then
                self.Events.evtRolePanelBadgeShow:Invoke(res.badges)
            end
        end
        , { bEnableHighFrequency = true }
    )
end

function RoleInfoServer:FetchAccountUnlockItems()
    self.accountUnlockItems = {}
    self.accountMaxLevel = 0
    local req = pb.CSSwitchGetAccountUnlockItemsReq:New()
    req:Request(
        function(res)
            if res.result == 0 then
                -- self.accountUnlockItems
                for _, levelInfo in ipairs(res.items) do
                    -- 取得最大等级
                    if levelInfo.level > self.accountMaxLevel then
                        self.accountMaxLevel = levelInfo.level
                    end
                    declare_if_nil(self.accountUnlockItems, levelInfo.level, {})
                    for _, propInfo in ipairs(levelInfo.rewards) do
                        --解锁功能屏蔽reward_type
                        if propInfo.prop_id ~= 0 and propInfo.reward_type ~= 0 and propInfo.prop_id then
                            --构建ItemBase
                            local itemData = ItemBase:New(propInfo.prop_id, propInfo.num)
                            if itemData then
                                --武器(添加皮肤信息)
                                if itemData.GetFeature and itemData.SetRawPropInfo then
                                    local weaponFeature = itemData:GetFeature(EFeatureType.Weapon)
                                    if weaponFeature and weaponFeature.IsWeapon and weaponFeature:IsWeapon() then
                                        --设置武器皮肤数据
                                        local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(itemData.id
                                            , EArmedForceMode.MP)
                                        if itemPresetId then
                                            local rawPropInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId)
                                            if rawPropInfo then
                                                itemData:SetRawPropInfo(rawPropInfo)
                                            end
                                            loginfo("RoleInfoServer:FetchAccountUnlockItems name = ", itemData.name,
                                                " itemPresetId = ", itemPresetId)
                                        end
                                    end
                                end
                                --设置是否奖励
                                itemData.reward_type = propInfo.reward_type
                                table.insert(self.accountUnlockItems[levelInfo.level], itemData)
                            end
                        end
                    end
                end
            end
        end
    )
end

---获取最大账号等级
---@return integer
function RoleInfoServer:GetAccountMaxLevel()
    return self.accountMaxLevel or 0
end

---根据level获取奖励
---@param level number  等级
---@return table
function RoleInfoServer:GetAccountUnlockItemsByLevel(level)
    return self.accountUnlockItems[level] or {}
end

---获取all奖励
---@return table
function RoleInfoServer:GetAllAccountUnlockItems()
    return self.accountUnlockItems
end

---根据level区间获取奖励
---@param preaccountLv number  等级
---@param accountLv number  等级
---@return table
function RoleInfoServer:GetAccountUnlockweaponList(preaccountLv, accountLv)
    local weaponList = {}
    if preaccountLv > 0 and preaccountLv <= accountLv then
        for level = preaccountLv + 1, accountLv do
            local rewards = self:GetAccountUnlockItemsByLevel(level)
            for index, item in ipairs(rewards) do
                local weaponFeature = item:GetFeature(EFeatureType.Weapon)
                local isWeapon = weaponFeature and weaponFeature:IsWeapon()
                if isWeapon then
                    table.insert(weaponList, item)
                end
            end
        end
    end
    return weaponList
end

-- 获取网吧特权
function RoleInfoServer:IsNetBarPrivilegeValid()
    logerror("[RoleInfoServer] IsNetBarPrivilegeValid self.netbarPriv =", self.netbarPriv)
    return self.netbarPriv > 0
end

function RoleInfoServer:GetNetBarPrivilegeLevel()
    logerror("[RoleInfoServer] GetNetBarPrivilegeLevel self.netbarPriv =", self.netbarPriv)
    return self.netbarPriv
end

-- 获取高校特权
function RoleInfoServer:IsCollegePrivilegeValid()
    logerror("[RoleInfoServer] self.stu_priv =", self.stuPriv)
    return self.stuPriv == StuPrivType.StuPriv_Certified
end

function RoleInfoServer:OnFetchAccountProfile(ntf)
    if ntf == nil then
        logerror("[RoleInfoServer] ntf is nil")
        self:FetchAllRoleInfo()
    else
        logerror("[RoleInfoServer] ntf.netbar_priv:", ntf.netbar_priv)
        self.netbarPriv = ntf.netbar_priv
        self.Events.evtRoleNetbarStateChanged:Invoke(self.netbarPriv)
    end
end

function RoleInfoServer:OnGetCSGetStuPrivNtf(ntf)
    if ntf then
        logerror("[RoleInfoServer] ntf.stuPriv =", ntf.stuPriv)
        self.stuPriv = ntf.stu_priv
    else
        logerror("[RoleInfoServer] ntf is nil")
    end
end

function RoleInfoServer:OnUpdateReputationNtf(ntf)
    loginfo("RoleInfoServer:OnUpdateReputationNtf")
    self:FetchReputationRankAward()
end

function RoleInfoServer:GetIsBHDPurchased()
    return self.bhd_is_purchased
end

function RoleInfoServer:DoAchieveBatchQuery(fCallback)
    local req = pb.CSAchieveBatchQueryProgressReq:New()
    req:Request(function(res)
        if res.result == 0 then
            fCallback(res.states)
        else
            logerror("DoAchieveBatchQuery failed")
        end
    end, { bEnableHighFrequency = true })
end

function RoleInfoServer:GetRankCheckPopUpWindow(fCallback)
    local function f(res)
        logerror("RoleInfoLogic.OpenTitleUnlockPop CSRankCheckPopUpWindowReq", res.result, res.last_weekly_reward_time,
            Facade.ClockManager:GetLocalTimestamp())
        if res.result == 0 then
            if fCallback then
                fCallback(res.last_weekly_reward_time)
            end
        end
    end

    local req = pb.CSRankCheckPopUpWindowReq:New()
    req:Request(f)
end

function RoleInfoServer:GetAllStarWeapon(playerId, fCallback)
    local req = pb.CSMPGetAllStarWeaponReq:New()
    req.player_id = playerId

    local function f(res)
        logerror("RoleInfoLogic.GetAllStarWeapon CSMPGetAllStarWeaponReq", res.result)
        if res.result == 0 then
            fCallback(res.star_weapons) -- pb_PropInfo[]
        end
    end

    req:Request(f, { bEnableHighFrequency = true })
end

-- 0 晋升之路 1 胜者为王
function RoleInfoServer:ChangeCommanderRankMode(mode)
    local req = pb.CSPlayerUpdateDisplayMpRankPointsReq:New()
    req.show_commander_rank_points = mode

    local function f(res)
        logerror("RoleInfoLogic.ChangeWinnerMode CSMPGetAllStarWeaponReq", res.result)
        if res.result == 0 then
            self.Events.evtCommanderRankModeChanged:Invoke()
        end
    end

    req:Request(f)
end

function RoleInfoServer:GetPlayerBasicInfo()
    return self._playerBasicInfo
end

function RoleInfoServer:SetPlayerBasicInfo(info)
    self._playerBasicInfo = info
end

function RoleInfoServer:ReqPlayerBasicInfo(playerId)
    local f = function(res)
        if res and res.result == 0 then
            local playerInfo = res.info
            if playerInfo then
                self._playerBasicInfo = playerInfo
                self.Events.evtPlayerBasicInfoUpdate:Invoke()
            end
        end
    end

    local req = pb.CSPlayerGetBasicInfoReq:New()
    req.player_id = playerId
    req:Request(f)
end

function RoleInfoServer:ReqQueryShowRoom(playerId, fCallback)
    local req = pb.CSDepositQueryShowRoomReq:New()
    req.player_id = playerId

    local function f(res)
        logerror("RoleInfoServer.ReqQueryShowRoom ", res.result)
        if res.result == 0 then
            fCallback(res)
        end
    end

    req:Request(f)
end

-- BEGIN MODIFICATION - VIRTUOS
function RoleInfoServer:ReqSetCrossplay(value)
    if not IsConsole() then
        return
    end
    loginfo("RoleInfoServer:ReqSetCrossplay value: ", value)

    if IsXSX() and self:HasCrossPlayPrivilegeChanged() == false then
        return
    end

    local fSetCrossplayRes = function(res)
        loginfo("RoleInfoServer:ReqSetCrossplay fSetCrossplayRes: ", res.result)
        if res.result == 0 then
            self:FetchAllRoleInfo()
            self.can_cross_plat_play = value
            if IsPS5() then
                -- PS5的跨平台权限设置保存在游戏服务器端
                self.hasCrossPlayPrivilege = value
            end
            self.Events.evtCrossPlayChanged:Invoke(value)
        end
    end
    local req = pb.CSAccountSetCrossPlatPlayReq:New()
    req.can_cross_plat_play = value
    req:Request(fSetCrossplayRes, { bEnableHighFrequency = true })
end

function RoleInfoServer:ReqGetPlatformCrossplayPrivilege()
    loginfo("RoleInfoServer:ReqGetPlatformCrossplayPrivilege")
    local fOnGetPlatformCrossplayPrivilege = function(res)
        loginfo("On get platform cross play privilge completed, result:", res)
        self.hasCrossPlayPrivilege = res
        self.hasCheckedPlatformCrossPlayPrivilege = true
        self:ReqSetCrossplay(res)
    end
    if IsXSX() then
        -- Xbox的跨平台权限设置保存在Xbox服务器端
        local bShowPlatformUIToResolvePrivilege = not self.hasCheckedPlatformCrossPlayPrivilege
        Server.ChatServer:CheckSinglePlatformPrivilege(EPlatformUserPrivileges.CanUserCrossPlay,
            bShowPlatformUIToResolvePrivilege, fOnGetPlatformCrossplayPrivilege)
        -- 在CSAccountGetPlayerProfileReq结果返回之前无法发送proto
    elseif IsPS5() and self._isPlayerProfileRetrieved then
        -- PS5的跨平台权限设置保存在游戏服务器端
        local req = pb.CSAccountGetPlayerProfileReq:New()
        req.player_id = Server.AccountServer:GetPlayerId()
        req.client_flag = 0
        req:Request(function(res)
            if res.result == 0 then
                loginfo("On get platform cross play privilge completed, can user cross play:", res.can_cross_plat_play)
                self.hasCrossPlayPrivilege = res.can_cross_plat_play
                DFMOnlineIdentityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.CrossPlatform,
                    self.hasCrossPlayPrivilege)
            end
        end)
    end
end

function RoleInfoServer:HasCrossPlayPrivilegeChanged()
    -- can_cross_plat_play在PS5平台表示客户端本地值，在Xbox平台表示游戏服务器保存值
    -- hasCrossPlayPrivilege在PS5平台表示游戏服务器保存值，在Xbox平台表示Xbox服务器端的值
    return self.can_cross_plat_play ~= self.hasCrossPlayPrivilege
end

function RoleInfoServer:_OnRelayConnected()
    logerror('RoleInfoServer:_OnRelayConnected FetchAllRoleQualityCard')
    self:FetchAllRoleQualityCard()
end

-- END MODIFICATION - VIRTUOS

return RoleInfoServer
