local UWeaponDisplayUtil = import "WeaponDisplayUtil"
-- local FWeaponAttributeCalculator = import "FWeaponAttributeCalculator"
local UWeaponAttributeDetailMethods = import "WeaponAttributeDetailMethods"
local UWeaponAttributeDetailManager = import "WeaponAttributeDetailManager"
local EWeaponAttributeValueType = import "EWeaponAttributeValueType"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"

local UGunPresetTableManager = import "GunPresetTableManager"
local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local UPartsDataTableManager = import "PartsDataTableManager"
local UPartsRuleTableManager = import "PartsRuleTableManager"
local EModularPartNodeType = import "EModularPartNodeType"
local FWeaponMeleeDataAttribute = import "WeaponMeleeDataAttribute"
local UAmmoDataManager = import "AmmoDataManager"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local RefUtil = require "DFM.YxFramework.Util.RefUtil"

local EModularWeaponDescCompare = import "EModularWeaponDescCompare"
local USuggestionPresetTableManager = import "SuggestionPresetTableManager"
local UPartSocketsUIAudioTableManager = import "PartSocketsUIAudioTableManager"
local UPartsFilterTableManager = import "PartsFilterTableManager"

local WeaponHelperTool = {}
---------------------------------------------------------------------------------
--- 此处放置Server、Module都可能用得到的武器工具方法
---------------------------------------------------------------------------------
local GetWeaponDataTable = function()
    return Facade.TableManager:GetTable("WeaponBase/WeaponAttributeTable")
end

local GetGunDataTable = function()
    return Facade.TableManager:GetTable("WeaponPart/GunPresetTable")
end

local GetAmmoDataTable = function()
    return Facade.TableManager:GetTable("WeaponPart/AmmoDataTable")
end

local GetAttackLevelCorrectionTable = function()
    return Facade.TableManager:GetTable("Damage/AttackLevelCorrectionTable")
end

local GetPoorWeaponDataTable = function()
    return Facade.TableManager:GetTable("PoorWeapon/PoorWeaponTable")
end

local GetPoorFunctionDataTable = function()
    return Facade.TableManager:GetTable("PoorWeapon/PoorFunctionTable")
end

local GetAttackerValueTable = function()
    return Facade.TableManager:GetTable("Damage/AttackerValueTable")
end

local GetDamagePointValue = function()
    return Facade.TableManager:GetTable("Damage/DamagePointValue")
end

local function GetCharacter()
    return Facade.GameFlowManager:GetCharacter()
end

-------------------------- Start WeaponWrapper ------------------
local WeaponWrapper = class('WeaponWrapper', LuaObject)

function WeaponWrapper:Ctor(WeaponGid)
    self._weapon = GetCharacter():GetWeaponByGuid(WeaponGid)
end

function WeaponWrapper:Reload()
    return self._weapon:WeaponReload()
end

function WeaponWrapper:SetWeaponGid(WeaponGid)
    self._weapon = GetCharacter():GetWeaponByGuid(WeaponGid)
end

function WeaponWrapper:GetChangeClipTime()
    return self._weapon:GetChangeClipTime()
end

function WeaponWrapper:GetWeaponObject()
    return self._weapon
end

function WeaponWrapper:GetGid()
    return self._weapon.WeaponGuid
end

function WeaponWrapper:CanChangeClip()
    if not self._weapon then
        return false
    end
    return self._weapon:CanChangeClip()
end

function WeaponWrapper:GetCurAmmoCount()
    return self._weapon:GetCurAmmoCount()
end

function WeaponWrapper:GetCarriedAmmoCount()
    return self._weapon:GetCarriedAmmoCount()
end

-------------------------- Start WeaponHelperTool ------------------
WeaponHelperTool.GetWeaponAttributeConfig = function(WeaponItemId)
    return GetWeaponDataTable():GetRowByColumn("recId", WeaponItemId)
end

WeaponHelperTool.GetAmmoConfig = function(AmmoItemId)
    return GetAmmoDataTable():GetRowByColumn("ItemId", AmmoItemId)
end

WeaponHelperTool.GetAttackLevelConfig = function(GroupId)
    return GetAttackLevelCorrectionTable():GetRowByColumn("GroupId", GroupId)
end

WeaponHelperTool.GetAttackerValueConfig = function(AttackerValueId)
    return GetAttackerValueTable():GetRowByColumn("AttackerValueId", AttackerValueId)
end

WeaponHelperTool.GetDamagePointValueConfig = function(AttackerValueId)
    return GetDamagePointValue():GetRowByColumn("AttackerValueId", AttackerValueId)
end

WeaponHelperTool.GetWeaponWrapper = function(WeaponOrGid)
    if type(WeaponOrGid) == "table" then
        return WeaponOrGid
    end
    local Wrapper = WeaponWrapper:New(WeaponOrGid)
    Wrapper:SetWeaponGid(WeaponOrGid)
    return Wrapper
end

WeaponHelperTool.GetWeaponWrapperByItem = function(WeaponItem)
    local WeaponGid = WeaponHelperTool.GetWeaponGid(WeaponItem)
    if WeaponGid then
        return WeaponHelperTool.GetWeaponWrapper(WeaponGid)
    end
    return nil
end

WeaponHelperTool.GetAllEquipedWeapons = function()
    local Character = GetCharacter()
end

WeaponHelperTool.GetWeaponGid = function(WeaponItem)
    local RawPropInfo = WeaponItem.rawPropInfo
    if not RawPropInfo then
        return
    end
    return RawPropInfo.gid
end

WeaponHelperTool.GetWeaponItemId = function(WeaponItem)
    local RawPropInfo = WeaponItem.rawPropInfo
    if not RawPropInfo then
        return
    end
    return RawPropInfo.id
end

-- 返回描述对应的武器属性
WeaponHelperTool.GetWeaponDataAttribute = function(WeaponDataAttr,  WeaponDesc )
    return UWeaponDisplayUtil.GetWeaponDataAttrByWeaponDesc(WeaponDataAttr, WeaponDesc)
end

-- 返回对应展示属性的评价值
WeaponHelperTool.GetWeaponDisplayAttributeValues = function(DisplayIds, WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcDisplayValues(DisplayIds, WeaponDataAttribute)
end

-- 返回对应展示的 详细属性的 <FName,float>
WeaponHelperTool.GetWeaponAttributeDetailValues = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcAllAttributeDetailValues(WeaponDataAttribute)
end


-- 返回对应展示的 TArray<FVector2D>
WeaponHelperTool.GetWeaponRecoliArray = function(WeaponDataAttribute, num)
    num = setdefault(num, 5)
    return UWeaponDisplayUtil.GetWeaponRecoliArray(WeaponDataAttribute, num)
end

-- 返回对应射程衰减
WeaponHelperTool.GetWeaponDamageAtten = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.GetWeaponDamageAtten(WeaponDataAttribute)
end

-- 返回对应展示的子弹信息
WeaponHelperTool.GetWeaponDataBullet = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.GetWeaponDataBullet(WeaponDataAttribute)
end



-- 返回弹夹容量
WeaponHelperTool.GetWeaponClipSize = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcAttributeValue("WeaponClipAmmoCount", WeaponDataAttribute)
end

-- 返回当前弹夹内的子弹数量
WeaponHelperTool.GetCurAmmoCountInClip = function(WeaponDesc)
    if WeaponDesc then
        return WeaponDesc:GetAmmoCountInClip()
    else
        return 0
    end
end

-- 返回备弹上限
WeaponHelperTool.GetMaxCarriedAmmoCount = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcAttributeValue("MaxCarriedAmmoCount", WeaponDataAttribute)
end

-- 返回当前备弹数量
WeaponHelperTool.GetCarriedAmmoCount = function(WeaponDesc)
    return WeaponDesc:GetCarriedAmmoCount()
end

-- 返回射速
WeaponHelperTool.GetWeaponFireSpeed = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcAttributeValue("FireSpeed", WeaponDataAttribute)
end

-- 返回弹药类型
WeaponHelperTool.GetWeaponAmmoType = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcAttributeValue("ammoType", WeaponDataAttribute)
end



WeaponHelperTool.GetAmmoTypeByAmmoId = function(id)
    local ammoDataTable = GetAmmoDataTable()
    if ammoDataTable[id] then
        return ammoDataTable[id].type
    else
        return nil
    end
end

WeaponHelperTool.GetAmmoTypeByItemId = function(id)
    return GetAmmoDataTable():GetRowFieldByColumn("ItemId", id, "Type")
end

WeaponHelperTool.GetAmmoCaliberByAmmoType = function(ammoType)
    return GetAmmoDataTable():GetRowFieldByColumn("Type", ammoType, "AmmoCaliber")
end

WeaponHelperTool.GetWeaponDetailValueStrById = function(value,attributeId)
    local attributeDetailRow  = UWeaponAttributeDetailManager:Get():GetRow(attributeId)
    return WeaponHelperTool.GetWeaponDetailValueStr(value,attributeDetailRow)
end

WeaponHelperTool.GetWeaponDetailValueStr=function(value,attributeDetailRow)
    local suffix = ""
    if attributeDetailRow.ValueType == EWeaponAttributeValueType.Percentage then
        suffix = "%"
        value = value * 100
     
    elseif attributeDetailRow.ValueType == EWeaponAttributeValueType.Normal then
       
    end
    if attributeDetailRow.RoundNum == 0 then
        value = math.round( value ) 
    else
        local round = 10 ^ attributeDetailRow.RoundNum
        value = math.round( value * round) /round
    end
    
  
    return tostring(value)..attributeDetailRow.UnitName..suffix
end

-- 武器伤害
-- local weaponAttribute = FWeaponDataAttribute()
-- weaponAttribute = WeaponHelperTool.GetWeaponDataAttribute(weaponAttribute,weaponDesc)
-- local caculator = FWeaponAttributeCalculator(WeaponAttribute)

WeaponHelperTool.GetWeaponAttDamge = function(WeaponDataAttribute)
    local damage =  UWeaponDisplayUtil.CalcAttributeDetailValue("Damage", WeaponDataAttribute)
    if Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.MP then
        local damageOfMP = damage
        if WeaponDataAttribute.RecId and WeaponDataAttribute.RecId ~= 0 then
            local weaponAttributeConfig = WeaponHelperTool.GetWeaponAttributeConfig(WeaponDataAttribute.RecId)
            if weaponAttributeConfig then
                local attackerValueConfig = WeaponHelperTool.GetAttackerValueConfig(weaponAttributeConfig.AttackerValueId)
                if attackerValueConfig then
                    local damagePointValueConfig = WeaponHelperTool.GetDamagePointValueConfig(attackerValueConfig.DamageIdGameMode01)
                    if damagePointValueConfig then
                        damageOfMP = damagePointValueConfig.Damage
                    end
                end
            end
            damage = damageOfMP
        else
            loginfo("WeaponHelperTool.GetWeaponAttDamge WeaponDataAttribute.RecId error! WeaponDataAttribute.RecId:", WeaponDataAttribute.RecId)
        end
	end
    local damageStr = WeaponHelperTool.GetWeaponDetailValueStrById(damage,"Damage")
    return damage,damageStr
end

WeaponHelperTool.GetWeaponArmorDamge = function(WeaponDataAttribute)
    local armorDamage =  UWeaponDisplayUtil.CalcAttributeDetailValue("ArmorDamage", WeaponDataAttribute)
    return armorDamage
end

WeaponHelperTool.GetPoorWeaponAttDamge = function(WeaponDataAttribute, poorWeaponId)
    local damage =  UWeaponDisplayUtil.CalcAttributeDetailValue("Damage", WeaponDataAttribute)
    local poorWeaponTable = GetPoorWeaponDataTable()
    local poorWeaponCfg = poorWeaponTable[poorWeaponId]
    local damageRate = 1
    if poorWeaponCfg then
        local poorFunctionTable = GetPoorFunctionDataTable()
        damageRate = poorFunctionTable[poorWeaponCfg.PoorFunctionId].DamageRate
    else
        logwarning("WeaponHelperTool.GetPoorWeaponAttDamge poorWeaponCfg is nil, poorWeaponId:", poorWeaponId)
    end
    damage = damage * damageRate
    local damageStr = WeaponHelperTool.GetWeaponDetailValueStrById(damage,"Damage")
    return damage,damageStr
end

-- 射速
WeaponHelperTool.GetWeaponAttFireRate = function(WeaponDataAttribute)
    local fireRate =  UWeaponDisplayUtil.CalcAttributeDetailValue("FireRate", WeaponDataAttribute)
    -- local fireRateStr = WeaponHelperTool.GetWeaponDetailValueStrById(fireRate,"FireRate")
    local fireRateStr = string.format(Module.ItemDetail.Config.Loc.weaponFireSpeed, math.round(fireRate))
    return fireRate,fireRateStr
end

-- 子弹数
WeaponHelperTool.GetWeaponAttCarriedAmmoCount = function(WeaponDataAttribute)
    local ClipAmmo =  UWeaponDisplayUtil.CalcAttributeDetailValue("ClipAmmo", WeaponDataAttribute)
    local ClipAmmoStr = WeaponHelperTool.GetWeaponDetailValueStrById(ClipAmmo,"ClipAmmo")
    return ClipAmmo,ClipAmmoStr
end

-- 备弹数
WeaponHelperTool.GetWeaponAttMaxCarriedAmmo = function(WeaponDataAttribute)
    local MaxCarriedAmmo =  UWeaponDisplayUtil.CalcAttributeDetailValue("MaxCarriedAmmo", WeaponDataAttribute)
    local MaxCarriedAmmoStr = WeaponHelperTool.GetWeaponDetailValueStrById(MaxCarriedAmmo,"MaxCarriedAmmo")
    return MaxCarriedAmmo,MaxCarriedAmmoStr
end

-- 穿甲等级
WeaponHelperTool.GetPenetrateLevel = function(WeaponDataAttribute)
    return UWeaponDisplayUtil.CalcAttributeDetailValue("PenetrateLevel", WeaponDataAttribute)
end

-- 枪口初速
WeaponHelperTool.GetFlySpeed = function (WeaponDataAttribute)
    local initSpeed = UWeaponDisplayUtil.CalcAttributeDetailValue("BulletFlySpeed", WeaponDataAttribute)
    local initSpeedValue = MathUtil.GetRoundingNum(initSpeed/100)
    local initSpeedStr = string.format(Module.ItemDetail.Config.Loc.weaponInitSpeed, initSpeedValue)
    return initSpeedValue, initSpeedStr
end

-- 开火模式
WeaponHelperTool.GetFireMode = function (WeaponDataAttribute)
    return UWeaponDisplayUtil.GetFireMode(WeaponDataAttribute)
end

-- 返回弹药类型
WeaponHelperTool.GetWeaponAmmoTypeStr = function(ammoType)
    local ret = GetAmmoDataTable():GetRowFieldByColumn("Type", ammoType, "ShortTypeName")
    if ret then
        return ret
    else
        loginfo('cant find ammoId by ammoType'..ammoType)
        return NSLOCTEXT("GlobalText", "WeaponHelperTool", "无效弹药类型")
    end
end

WeaponHelperTool.GetRecIdByWeaponPresetId = function(presetId)
    if not presetId then 
        return nil
    end

    local row = UGunPresetTableManager.Get():GetRow(presetId)
    local recId = row and row.recId or 0
    return recId
end

WeaponHelperTool.GetProsAndConsByPartsId = function(id)
    local info = UPartsDataTableManager.Get():GetPartDataRow(id)
    local outInfo = {}
    local buffInfo={}
    local debuffInfo={}
    if info then
        for idx, value in ipairs(info.ProsList) do
            local resultStr
            local params = {}
            if value.Params then
                -- 容错处理
                for _, val in pairs(value.Params) do
                    table.insert(params, val)
                end
                for i = #value.Params, #string.split(value.BuffText, "%") - 2 do
                    table.insert(params, " ")
                end
                resultStr = string.format(value.BuffText, unpack(params))
            else
                resultStr = value.BuffText
            end
            table.insert(buffInfo,resultStr)
        end
        for idx, value in ipairs(info.ConsList) do
            local resultStr
            local params = {}
            if value.Params then
                -- 容错处理
                for _, val in pairs(value.Params) do
                    table.insert(params, val)
                end
                for i = #value.Params, #string.split(value.BuffText, "%") - 2 do
                    table.insert(params, " ")
                end
                resultStr = string.format(value.BuffText, unpack(params))
            else
                resultStr = value.BuffText
            end
            table.insert(debuffInfo,resultStr)
        end
        outInfo.buffInfo=buffInfo
        outInfo.debuffInfo=debuffInfo
    end
	return outInfo
end

WeaponHelperTool.GetProsAndConsByPartsIdType2 = function(id)
    local info = UPartsDataTableManager.Get():GetPartDataRow(id)
    local outInfo = {}
    local buffInfo={}
    local debuffInfo={}
    if info then
        for idx, value in ipairs(info.ProsList) do
            local resultStr
            local params = {}
            if value.Params then
                -- 判断使用哪种拼接方式
                for _, val in pairs(value.Params) do
                    -- 根据ID找到属性名
                    if string.sub(val, 1, 2) == "10" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        --10%字符串转换nil,获取不到nil的数据,调方法报错
                        local partsAttri = PartsAttriNameTable[tonumber(val)]
                        if partsAttri then
                            val = partsAttri.AttributesName
                        end
                    end
                    if val then
                        table.insert(params, val)
                    else
                        log("val is nil, please check it!")
                    end
                end
                -- 容错处理
                for i = #value.Params, #string.split(value.BuffText, "%") - 2 do
                    table.insert(params, " ")
                end
                resultStr = string.format(value.BuffText, unpack(params))
            else
                resultStr = value.BuffText
            end
            table.insert(buffInfo,resultStr)
        end
        for idx, value in ipairs(info.ConsList) do
            local resultStr
            local params = {}
            if value.Params then
                for _, val in pairs(value.Params) do
                    -- 根据ID找到属性名
                    if string.sub(val, 1, 2) == "10" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        val = PartsAttriNameTable[tonumber(val)].AttributesName
                    end
                    if val then
                        table.insert(params, val)
                    else
                        log("val is nil, please check it!")
                    end
                end
                for i = #value.Params, #string.split(value.BuffText, "%") - 2 do
                    table.insert(params, " ")
                end
                resultStr = string.format(value.BuffText, unpack(params))
            else
                resultStr = value.BuffText
            end
            table.insert(debuffInfo,resultStr)
        end
        outInfo.buffInfo=buffInfo
        outInfo.debuffInfo=debuffInfo
    end
	return outInfo
end

WeaponHelperTool.GetProsAndConsByPartsIdType3 = function(id)
    local info = UPartsDataTableManager.Get():GetPartDataRow(id)
    local outInfo = {}
    local buffInfo={}
    local debuffInfo={}
    if info then
        for idx, value in ipairs(info.ProsList) do
            local resultStr
            local params = {}
            if value.Params then
                local attrDesc
                local showStr = ""
                local param = {}
                local i = 1
                -- 判断使用哪种拼接方式
                for _, val in pairs(value.Params) do
                    --[[
                    -- 根据ID找到属性名
                    -- if attrDesc then
                    --     local key = "value"..i
                    --     param[key] = val
                    --     i = i + 1
                    -- end
                    if string.sub(val, 1, 2) == "16" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                    end
                    ]]--
                    if string.sub(val, 1, 3) == "100" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        if PartsAttriNameTable[tonumber(val)] then
                            attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                        else
                            logerror(string.format("[WeaponHelperTool.GetProsAndConsByPartsIdType3]:PartsDataTable is there %s", val))
                        end
                    else
                        showStr = showStr .. val
                    end
                end
                param["value1"] = showStr
                if attrDesc and param then
                    resultStr = StringUtil.Key2StrFormat(attrDesc, param)
                end
            else
                resultStr = ""
            end
            table.insert(buffInfo,resultStr)
        end
        for idx, value in ipairs(info.ConsList) do
            local resultStr
            local params = {}
            if value.Params then
                local attrDesc
                local param = {}
                local showStr = ""
                local i = 1
                -- 判断使用哪种拼接方式
                for _, val in pairs(value.Params) do
                    --[[
                    -- 根据ID找到属性名
                    if attrDesc then
                        local key = "value"..i
                        param[key] = val
                        i = i + 1
                    end
                    if string.sub(val, 1, 2) == "16" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                    end
                    ]]--
                    if string.sub(val, 1, 3) == "100" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        if PartsAttriNameTable[tonumber(val)] then
                            attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                        else
                            logerror(string.format("[WeaponHelperTool.GetProsAndConsByPartsIdType3]:PartsDataTable is there %s", val))
                        end
                    else
                        showStr = showStr .. val
                    end
                end
                param["value1"] = showStr
                if attrDesc and param then
                    resultStr = StringUtil.Key2StrFormat(attrDesc, param)
                end
            else
                resultStr = ""
            end
            table.insert(debuffInfo,resultStr)
        end
        outInfo.buffInfo=buffInfo
        outInfo.debuffInfo=debuffInfo
    end
	return outInfo
end

-- +-号不显示，后面配置数字，为电量的格数
WeaponHelperTool.GetProsAndConsByPartsIdType4 = function(id)
    local info = UPartsDataTableManager.Get():GetPartDataRow(id)
    local outInfo = {}
    local buffInfo={}
    local debuffInfo={}
    if info then
        for idx, value in ipairs(info.ProsList) do
            local resultStr
            local params = {}
            local num = 0
            if value.Params then
                local attrDesc
                local showStr = ""
                local param = {}
                local i = 1
                -- 判断使用哪种拼接方式
                for index, val in pairs(value.Params) do
                    --[[
                    -- 根据ID找到属性名
                    -- if attrDesc then
                    --     local key = "value"..i
                    --     param[key] = val
                    --     i = i + 1
                    -- end
                    if string.sub(val, 1, 2) == "16" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                    end
                    ]]--
                    if string.sub(val, 1, 3) == "100" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        if PartsAttriNameTable[tonumber(val)] then
                            attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                        else
                            logerror(string.format("[WeaponHelperTool.GetProsAndConsByPartsIdType3]:PartsDataTable is there %s", val))
                        end
                    else
                        if index == 0 then
                            -- 第一位配置固定为"+x"，x为电量个数
                            local tmpNum = string.sub(val, 2, 2)
                            if #tmpNum ~= 0 then
                                num = tonumber(tmpNum) or 0
                            end
                        else
                            showStr = showStr .. val
                        end
                    end
                end
                param["value1"] = showStr
                if attrDesc and param then
                    resultStr = StringUtil.Key2StrFormat(attrDesc, param)
                end
            else
                resultStr = ""
            end
            table.insert(buffInfo, {txt = resultStr, num = num})
        end
        for idx, value in ipairs(info.ConsList) do
            local resultStr
            local params = {}
            local num = 0
            if value.Params then
                local attrDesc
                local param = {}
                local showStr = ""
                local i = 1
                -- 判断使用哪种拼接方式
                for index, val in pairs(value.Params) do
                    --[[
                    -- 根据ID找到属性名
                    if attrDesc then
                        local key = "value"..i
                        param[key] = val
                        i = i + 1
                    end
                    if string.sub(val, 1, 2) == "16" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                    end
                    ]]--
                    if string.sub(val, 1, 3) == "100" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        if PartsAttriNameTable[tonumber(val)] then
                            attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                        else
                            logerror(string.format("[WeaponHelperTool.GetProsAndConsByPartsIdType3]:PartsDataTable is there %s", val))
                        end
                    else
                        if index == 0 then
                            -- 第一位配置固定为"+x"，x为电量个数
                            local tmpNum = string.sub(val, 2, 2)
                            if #tmpNum ~= 0 then
                                num = tonumber(tmpNum) or 0
                            end
                        else
                            showStr = showStr .. val
                        end
                    end
                end
                param["value1"] = showStr
                if attrDesc and param then
                    resultStr = StringUtil.Key2StrFormat(attrDesc, param)
                end
            else
                resultStr = ""
            end
            table.insert(debuffInfo, {txt = resultStr, num = num})
        end
        outInfo.buffInfo=buffInfo
        outInfo.debuffInfo=debuffInfo
    end
	return outInfo
end

-- 根据子弹id获取子弹效果
WeaponHelperTool.GetProsAndConsByBulletId = function(id)
    local outInfo = {}
    local buffInfo = {}
    local debuffInfo = {}
    local prosListSOL
    local consListSOL

    local ammoDataTable = Facade.TableManager:GetTable("WeaponPart/AmmoDataTable")
	if ammoDataTable then
        local row = ammoDataTable:GetRowByColumn("ItemId", id)
        if row then
            prosListSOL = row.ProsListSOL
            consListSOL = row.ConsListSOL
        end

        if prosListSOL and consListSOL then
            for idx, value in ipairs(prosListSOL) do
                local resultStr = ""
                local num = 0
                local attrDesc
                local showStr = ""
                local param = {}
                local numStr = ""
                -- 判断使用哪种拼接方式
                for index, val in pairs(value.Params) do
                    if string.sub(val, 1, 3) == "100" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        if PartsAttriNameTable[tonumber(val)] then
                            attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                        else
                            logerror(string.format("[WeaponHelperTool.GetProsAndConsByPartsIdType3]:PartsDataTable is there %s", val))
                        end
                    elseif string.sub(val, 1, 3) == "200" then
                        numStr = string.sub(val, 4, string.len(val))
                    else
                        if index == 0 then
                            -- 第一位配置固定为"+x"，x为电量个数
                            local tmpNum = string.sub(val, 2, 2)
                            if #tmpNum ~= 0 then
                                num = tonumber(tmpNum) or 0
                            end
                        else
                            showStr = showStr .. val
                        end
                    end
                end
                param["value1"] = showStr
                if attrDesc and param then
                    resultStr = StringUtil.Key2StrFormat(attrDesc, param)
                end
                if numStr ~= "" then
                    resultStr = StringUtil.SequentialFormat(Module.ItemDetail.Config.Loc.BuffEffectText, numStr, resultStr)
                    
                end
                table.insert(buffInfo, {txt = resultStr, num = num})
            end
            for idx, value in ipairs(consListSOL) do
                local resultStr = ""
                local num = 0
                local attrDesc
                local showStr = ""
                local param = {}
                local numStr = ""
                -- 判断使用哪种拼接方式
                for index, val in pairs(value.Params) do
                    if string.sub(val, 1, 3) == "100" then
                        local PartsAttriNameTable = Facade.TableManager:GetTable("WeaponPart/PartAttribute")
                        if PartsAttriNameTable[tonumber(val)] then
                            attrDesc = PartsAttriNameTable[tonumber(val)].AttributesName
                        else
                            logerror(string.format("[WeaponHelperTool.GetProsAndConsByPartsIdType3]:PartsDataTable is there %s", val))
                        end
                    elseif string.sub(val, 1, 3) == "200" then
                        numStr = string.sub(val, 4, string.len(val))
                    else
                        if index == 0 then
                            -- 第一位配置固定为"+x"，x为电量个数
                            local tmpNum = string.sub(val, 2, 2)
                            if #tmpNum ~= 0 then
                                num = tonumber(tmpNum) or 0
                            end
                        else
                            showStr = showStr .. val
                        end
                    end
                end
                param["value1"] = showStr
                if attrDesc and param then
                    resultStr = StringUtil.Key2StrFormat(attrDesc, param)
                end
                if numStr ~= "" then
                    resultStr = StringUtil.SequentialFormat(Module.ItemDetail.Config.Loc.BuffEffectText, numStr, resultStr)
                    
                end
                table.insert(debuffInfo, {txt = resultStr, num = num})
            end
        else
            logerror("WeaponHelperTool.GetProsAndConsByBulletId: id not in AmmoDataTable! id: ", id)
        end
	end
    outInfo.buffInfo = buffInfo
    outInfo.debuffInfo = debuffInfo
	return outInfo
end

WeaponHelperTool.GetDebugInfoByPartsId = function(id)
    local info = UPartsDataTableManager.Get():GetPartDataRow(id)
    local funcList = {}
    funcList = UPartsFunctionTableManager.Get():GetFuncRows(info.FunctionId, funcList)
    local groupFuncList = {}
    for key, value in pairs(funcList) do
        local Str = string.format("%d,%s,%s,%s",value.PartFunctionType,value.Param1,value.Param2,value.Param3)
        if groupFuncList[value.RowDescription] then
        groupFuncList[value.RowDescription] = groupFuncList[value.RowDescription].."\n"..Str
        else
            groupFuncList[value.RowDescription] = Str
        end
    end        
    --outInfo =  table.tolist(groupFuncList)
    local fullStr = ""
    for index, value in ipairs( table.tolist(groupFuncList)) do
        fullStr = fullStr..value.."\n"
    end
    return fullStr
end

WeaponHelperTool.GetExpSourceDesc = function(factorType)
    local t = Facade.TableManager:GetTable("WeaponSystem/WeaponLevelFormula")
    local ret = t:GetRowFieldByColumn("FactorType", factorType, "FactorDesc")
    if ret then
        return ret
    else
        return ""
    end
end

---@param skinID number
---@return GPModularWeaponDesc, Table<PartIndex>
function WeaponHelperTool.GetWeaponDescriptionFromSkinID(skinID)
    local skinConfig
    skinConfig = Facade.TableManager:GetRowByKey("WeaponSkin/WeaponSkinDataTable", tostring(skinID))
    if skinConfig == nil then
        skinConfig = Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(skinID))
    end
    if skinConfig == nil then
        return nil
    end

    local presetID = skinConfig.BlueprintId or skinConfig.SkinApperaranceId
    if presetID == nil then
        return nil
    end
    local presetConfig = UGunPresetTableManager.Get():GetGunByItemId(presetID)
    if presetConfig == nil then
        return nil
    end
    local partIndexs = {}
    local weaponDescription
    weaponDescription, partIndexs = UAssembleWeaponDataLibrary.GetWeaponDescAndPartIndexsFromPreset(presetConfig, partIndexs)
    if not isvalid(weaponDescription) then
        weaponDescription = UAssembleWeaponDataLibrary.GetWeaponDescFromWeaponID(skinConfig.MeleeWeaponID)
    end
    if isvalid(weaponDescription) then
        weaponDescription:SetSkin(skinID, 0)
    end
    RefUtil.AddRef(weaponDescription)
    return weaponDescription, partIndexs
end

---@param propinfo pb_PropInfo
---@return boolean
function WeaponHelperTool.GetIsBlueprintSolutionFromPropinfo(propinfo)
    local skinID =  ItemBase.GetWeaponSkinIDFromPropInfo(propinfo)
    if skinID == 0 then
        return false
    end

    local blueprintWeaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(skinID)
    local inWeaponDescription = WeaponAssemblyTool.PropInfo_To_Desc(propinfo)

    local bEqual = WeaponHelperTool.CompareWeaponDescription(blueprintWeaponDescription, inWeaponDescription)
    return bEqual
end

---@param presetID number
---@param desctiption UModularWeaponDesc
function WeaponHelperTool.CompareWeaponDescriptionWithPresetID(presetID, inDesctiption)
    local presetConfig = UGunPresetTableManager.Get():GetGunByItemId(presetID)
    if presetConfig == nil then
        return nil
    end

    local presetDescription = UAssembleWeaponDataLibrary.GetWeaponDescFromPreset(presetConfig)
    local bEqual = WeaponHelperTool.CompareWeaponDescription(presetDescription, inDesctiption)
    return bEqual
end

function WeaponHelperTool.CompareWeaponPropInfo(lhs, rhs)
    local function fCompareWeaponPropInfo(lPropInfo, rPropInfo)
        if lPropInfo.id ~= rPropInfo.id then -- 判断机匣是否一致
            return false
        end
        -- local lSkin_id = lPropInfo.weapon.skin_id or 0
        -- local rSkin_id = rPropInfo.weapon.skin_id or 0
        -- if lSkin_id ~= rSkin_id then -- 判断皮肤是否一致
        --     return false
        -- end
        local lComponents = lPropInfo.components
        local rComponents = rPropInfo.components
        if #lComponents ~= #rComponents then
            return false
        end
        local bEqual = true
        for _, lComponent in ipairs(lComponents) do
            local lSlot = lComponent.slot
            local lProp = lComponent.prop_data
            local rComponent = table.find(rComponents, function (v, k)
                return v.slot == lSlot
            end)
            if not rComponent then
                return false
            end
            local rProp = rComponent.prop_data
            bEqual = fCompareWeaponPropInfo(lProp, rProp)
            if not bEqual then
                return false
            end
        end
        return bEqual
    end
    local bEqual = true
    bEqual = fCompareWeaponPropInfo(lhs, rhs)
    return bEqual
end

---@param lhs UModularWeaponDesc
---@param rhs UModularWeaponDesc
function WeaponHelperTool.CompareWeaponDescription(lhs, rhs, compareType)
    if isinvalid(lhs) or isinvalid(rhs) then
        return false
    end

    compareType = setdefault(compareType, EModularWeaponDescCompare.AllCompare)

    local bResult = lhs:CompareRenderResult(rhs, compareType)
    return bResult

    -- local function dump(desc, message)
    --     logformat("-------------%s-----------------", message)
    --     local nodes = desc.WeaponParts
    --     for _, value in ipairs(nodes) do
    --        logerror(message, value.ItemId, value.SocketGUID)
    --     end
    
    -- end
    -- dump(lhs, "lhs")
    -- dump(rhs, "rhs")

    -- local invalidSocketGUID = G_GUNSMITH.SOCKET_GUID_INVALID
    -- local function fMatchNode(inNode, description)
    --     if inNode == nil or inNode.SocketGUID == invalidSocketGUID or inNode.NodeType ~= EModularPartNodeType.Adpater  then
    --         return true
    --     end

    --     local inGUID = inNode.SocketGUID
    --     local inItemID = inNode.ItemId
        
    --     local matchNode = description:GetWeaponPartNodeFromSocketGUID(inGUID)
    --     local matchItemID = matchNode.ItemId
    --     local bMatched = inItemID == matchItemID
    --     return bMatched
    -- end
    
    -- local inPartNodes = lhs.WeaponParts
    -- for _, value in ipairs(inPartNodes) do
    --     local bMatched = fMatchNode(value, rhs)
    --     if not bMatched then
    --         return false
    --     end
    -- end

    -- return true
end

---@param lhs UModularWeaponDesc
---@param rhs UModularWeaponDesc
function WeaponHelperTool.CompareWeaponDescriptionSkin(lhs, rhs)
    if isinvalid(lhs) or isinvalid(rhs) then
        return false
    end
    local lSkinID = lhs:GetSkinInfo().SkinId
    local rSkinID = rhs:GetSkinInfo().SkinId
    local lFancyColorID = lhs:GetSkinInfo().FancyColorID
    local rFancyColorID = rhs:GetSkinInfo().FancyColorID
    local bEqualSkinID = (lSkinID == rSkinID)
    local bEqualColorID = (lFancyColorID == rFancyColorID)
    if bEqualSkinID and bEqualColorID then
        return true
    end
    return false
end

---@param baseWeaponID number
---@return table<number>
function WeaponHelperTool.GetWeaponSkinIDs(baseWeaponID)
    local datas = nil
    local dataStore = Server.GunsmithServer:GetDataStore()
    if dataStore ~= nil then
        datas = dataStore:GetWeaponSkinIDsFromBaseWeaponID(baseWeaponID)
    end

    local bContains = false
    if datas ~= nil then
        bContains = #datas > 0
    end
    return bContains, datas
end

function WeaponHelperTool.GetWeaponSkinBaseWeaponID(skinID)
    local dataTable = WeaponHelperTool.GetWeaponSkinDataTable()
    local key = tostring(skinID)
    local config = dataTable[key]
    if config == nil then
        return 0
    end

    return config.BaseWeaponId
end

function WeaponHelperTool.GetWeaponSkinDataTable()
    return Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
end

function WeaponHelperTool.GetMeleeWeaponSkinDataTable()
    return Facade.TableManager:GetTable("WeaponSkin/MeleeWeaponSkinDataTable")
end

function WeaponHelperTool.GetWeaponBluePrintDataTable()
    return Facade.TableManager:GetTable("WeaponBlueprintDesc")
end

function WeaponHelperTool.GetRecFunctionTableDataTable()
    return Facade.TableManager:GetTable("WeaponPart/RecFunctionTable")
end

function WeaponHelperTool.GetTunePartFunctionTable()
    return Facade.TableManager:GetTable("WeaponPart/TunePartFunctionTable")
end

function WeaponHelperTool.GetTunePartFunctionTableRow(id)
    local dataTable = WeaponHelperTool.GetTunePartFunctionTable()
    local row = dataTable[id]
    return row
end

function WeaponHelperTool.GetTunePartFunctionLocIDTable()
    return Facade.TableManager:GetTable("WeaponPart/TunePartFunctionLocIDTable")
end

function WeaponHelperTool.GetTunePartFunctionLocIDTableRow(id)
    local dataTable = WeaponHelperTool.GetTunePartFunctionLocIDTable()
    local row = dataTable[id]
    return row
end

function WeaponHelperTool.GetTunePartFunctionLocIDTableRowLocation(id)
    local row = WeaponHelperTool.GetTunePartFunctionLocIDTableRow(id)
    if isinvalid(row) then
        return nil
    end
    return row.Loc
end

function WeaponHelperTool.GetWeaponPartsDataTableRow(id)
    local row = UPartsDataTableManager.Get():GetPartDataRow(id)
    return row
end

function WeaponHelperTool.GetPendantOpenCollection(pendantID)
    local pendantConfig = WeaponHelperTool.GetPendantTable()
    local row = pendantConfig[pendantID]
    if isinvalid(row) then
        return false
    end
    return row.OpenCollection
end

function WeaponHelperTool.GetPendantDisplayResources(pendantID)
    local pendantConfig = WeaponHelperTool.GetPendantTable()
    local row = pendantConfig[pendantID]
    if isinvalid(row) then
        return false
    end
    return row.DisplayResources
end

function WeaponHelperTool.GetPendantMandelBrickID(pendantID)
    local pendantConfig = WeaponHelperTool.GetPendantTable()
    local row = pendantConfig[pendantID]
    if isinvalid(row) then
        return false
    end
    return row.MandelBrickID
end

function WeaponHelperTool.GetPartsFunctionDataTable()
    return Facade.TableManager:GetTable("WeaponPart/PartsFunctionDataTable")
end

function WeaponHelperTool.GetWeaponFunctionIdByItemId(id)
    local row = WeaponHelperTool.GetWeaponPartsDataTableRow(id)
    if isinvalid(row) then
        return nil
    end
    return row.FunctionId
end

local functionID2RulesID = {}
for key, part in pairs(WeaponHelperTool.GetPartsFunctionDataTable() or {}) do
    local functionId = part and part.FunctionId
    if functionId then
        if functionID2RulesID[functionId] then
            table.insert(functionID2RulesID[functionId], part.RuleKey)
        else
            functionID2RulesID[functionId] = {}
            table.insert(functionID2RulesID[functionId], part.RuleKey)
        end
    end
end

function WeaponHelperTool.GetWeaponFunctionID2RulesIDTab(functionId)
    if functionID2RulesID[functionId] then
        return functionID2RulesID[functionId]
    end
    return nil
end

function WeaponHelperTool.GetWeaponpFunctionTableRowByFunctionId(functionId)
    return WeaponHelperTool.GetPartsFunctionDataTable():GetRowByColumn("FunctionId", functionId)
end

-- 获取蓝图数据
function WeaponHelperTool.GetWeaponBluePrintData(skinID)
    local dataTable = WeaponHelperTool.GetWeaponBluePrintDataTable()
    local key = tostring(skinID)
    local skinData = dataTable[key]
    if skinData == nil then
        logerror("未获取到该蓝图数据")
        return nil
    end
    return skinData
end

function WeaponHelperTool. GetPendantTable()
    return Facade.TableManager:GetTable("PendantDataTable")
end

---小刀显示属性
-- 返回对应展示的 详细属性的 <FName,float>
WeaponHelperTool.GetMeleeWeaponDataAttrByWeaponDesc = function( WeaponDesc )
    local WeaponMeleeDataAttribute = FWeaponMeleeDataAttribute()
    return UWeaponDisplayUtil.GetMeleeWeaponDataAttrByWeaponDesc(WeaponMeleeDataAttribute, WeaponDesc)
end

--小刀连招伤害ID
WeaponHelperTool.GetMeleeComboAttackID = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeComboAttackID(WeaponMeleeDataAttribute)
end

--小刀肉伤
WeaponHelperTool.GetMeleeDamage = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeDamage(WeaponMeleeDataAttribute)
end

--小刀连招肉伤
WeaponHelperTool.GetMeleeComboDamages = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeComboDamages(WeaponMeleeDataAttribute)
end

--小刀甲伤
WeaponHelperTool.GetMeleeArmorDamage = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeArmorDamage(WeaponMeleeDataAttribute)
end

--小刀连击甲伤
WeaponHelperTool.GetMeleeArmorDamages = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeArmorDamages(WeaponMeleeDataAttribute)
end

--小刀基础移速
WeaponHelperTool.GetMeleeStandRunSpeed = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeStandRunSpeed(WeaponMeleeDataAttribute)
end

--小刀攻击距离
WeaponHelperTool.GetMeleeAttackDistance = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeAttackDistance(WeaponMeleeDataAttribute)
end

--小刀攻击频率
WeaponHelperTool.GetMeleeAttackSpeed = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeAttackSpeed(WeaponMeleeDataAttribute)
end

--小刀攻击速度
WeaponHelperTool.GetMeleeAtkspeed = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeAtkspeed(WeaponMeleeDataAttribute)
end

--小刀疾跑移速
WeaponHelperTool.GetMeleeStandSprintSpeed = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeStandSprintSpeed(WeaponMeleeDataAttribute)
end

--小刀穿甲等级
WeaponHelperTool.GetMeleeArmorLevel = function( WeaponMeleeDataAttribute )
    return UWeaponAttributeDetailMethods.GetMeleeArmorLevel(WeaponMeleeDataAttribute)
end

-- 获取枪械所需的子弹类型
WeaponHelperTool.GetAmmoListForWeapon = function (weaponItemId)
    local ammoList = {}
    local ammoMgr = UAmmoDataManager.Get()
    local ammoDataTable = Facade.TableManager:GetTable("WeaponPart/AmmoDataTable")

    for _, row in pairs(ammoDataTable) do
        local itemSubType = ItemHelperTool.GetSubTypeById(row.ItemId)
        if itemSubType > 9 and ammoMgr:IsMatchWeapon(weaponItemId, row.ItemId) then
            table.insert(ammoList, row.ItemId)
        end
    end

    -- sorted by quality
    table.sort(ammoList, function(lhs, rhs)
        local lConfig = ItemConfigTool.GetItemConfigById(lhs)
        local rConfig = ItemConfigTool.GetItemConfigById(rhs)
        if not lConfig or not rConfig then
            logerror("[WeaponHelperTool] GetAmmoListForWeapon error, lhs-itemID = %s", tostring(lhs))
            logerror("[WeaponHelperTool] GetAmmoListForWeapon error, rhs-itemID = %s", tostring(rhs))
        end
        if lConfig.Quality ~= rConfig.Quality then
            return lConfig.Quality < rConfig.Quality
        end

        return lhs < rhs
    end)

    return ammoList
end

-- 获取子弹对应的全部枪械
WeaponHelperTool.GetBulletApplyWeapon = function(bulletId)
    local weaponIds = {}
	local AmmoRow = WeaponHelperTool.GetAmmoConfig(bulletId)
	if AmmoRow then
		local WeaponAttributeTable = Facade.TableManager:GetTable("WeaponBase/WeaponAttributeTable")
		for k, Row in pairs(WeaponAttributeTable) do
			if Row.AmmoType == AmmoRow.Type then
                table.insert(weaponIds, Row.RecId)
			end
		end
	end
    return weaponIds
end

--根据武器描述对象获取排序好的配件数组 已挪至ItemDetailLogic.GetSocketArrayByWeaponDesc
-- WeaponHelperTool.GetSocketArrayByWeaponDesc = function( WeaponDesc )
--     local allNodeTree = UAssembleWeaponDataLibrary.GetAssemblePartNodesFromeDesc(WeaponDesc)
-- 	local allWeaponPartNodes = allNodeTree.AllNodes ---@type TArray<FGPAssemblePartNode>
-- 	local socketArray = {}
-- 	local rootNodeSocketList = nil

-- 	for key, node in pairs(allWeaponPartNodes) do
-- 		if node.NodeType == EModularPartNodeType.Adpater then
-- 			if node.AllSocketList:Num() > 0 then
-- 				if rootNodeSocketList == nil then
-- 					rootNodeSocketList = node.AllSocketList
-- 				end
-- 				for _, socket in pairs(node.AllSocketList) do
-- 					local luaSocket = PartSocket:Create(socket, allWeaponPartNodes)
-- 					local bShowable=Module.Gunsmith:GetIsShowSocketNode(luaSocket.validPartItemList)
-- 					if luaSocket:CanBeInteraction() and bShowable  then
-- 						table.insert(socketArray, luaSocket)
-- 					end
-- 				end
-- 			end
-- 		end
-- 	end
-- 	if #socketArray ~= 0 then
-- 		local function getSocketIdInRootNode(nodeIdx)
-- 			for idx, socket in ipairs(rootNodeSocketList) do
-- 				if socket.AttachPartIndex == nodeIdx then
-- 					return socket.SocketId
-- 				end
-- 			end
-- 			return 0
-- 		end
-- 		local sortFunc = function(socket1, socket2)
-- 			-- 判断强化套件
-- 			if socket1:GetSocketPartType() == ItemConfig.EAdapterItemType.Attacker then
-- 				return true
-- 			end
-- 			if socket2:GetSocketPartType() == ItemConfig.EAdapterItemType.Attacker then
-- 				return false
-- 			end
-- 			-- 判断是否子配件，子配件的话找父节点排序
-- 			if socket1:GetParentPartIndex() == socket2:GetParentPartIndex() then
-- 				return socket1.SocketId < socket2.SocketId
-- 			end
-- 			if socket1:GetParentPartIndex() == 0 then
-- 				-- 父配件排到子配件前面
-- 				if socket2:GetParentPartIndex() == socket1.AttachPartIndex then
-- 					return true
-- 				end
-- 				return socket1.SocketId < getSocketIdInRootNode(socket2:GetParentPartIndex())
-- 			end
-- 			if socket2:GetParentPartIndex() == 0 then
-- 				-- 父配件排到子配件前面
-- 				if socket1:GetParentPartIndex() == socket2.AttachPartIndex then
-- 					return false
-- 				end
-- 				return getSocketIdInRootNode(socket1:GetParentPartIndex()) < socket2.SocketId
-- 			end
-- 			return getSocketIdInRootNode(socket1:GetParentPartIndex()) < getSocketIdInRootNode(socket2:GetParentPartIndex())
-- 		end
-- 		table.sort(socketArray, sortFunc)
--     end
--     return socketArray
-- end

function WeaponHelperTool.GetAllPendantPartIDs()
    return UPartsDataTableManager.Get():GetAllPendantPartIDs()
end

function WeaponHelperTool.GetSuggestionPresetConfigByItemID(weaponItemID)
    return USuggestionPresetTableManager.Get():GetRow(weaponItemID)
end

function WeaponHelperTool.GetPartSocketsUIAudioConfigBySocketID(socketID)
    return UPartSocketsUIAudioTableManager.Get():GetRow(socketID)
end

function WeaponHelperTool.GetUIAudioBySocketID(socketID, bInstall)
    local partSocketsUIAudioConfig = WeaponHelperTool.GetPartSocketsUIAudioConfigBySocketID(socketID)
    if isinvalid(partSocketsUIAudioConfig) then
        return nil
    end
    if bInstall then
        return partSocketsUIAudioConfig.InstallUIAudio
    end
    return partSocketsUIAudioConfig.UninstallUIAudio
end

function WeaponHelperTool.GetPartsFilterConfigByID(ID)
    return UPartsFilterTableManager.Get():GetRow(ID)
end

function WeaponHelperTool.GetFilterDisplayNameByID(ID)
    local partFilterConfig = WeaponHelperTool.GetPartsFilterConfigByID(ID)
    if isinvalid(partFilterConfig) then
        return nil
    end
    return partFilterConfig.FilterDisplayName
end

return WeaponHelperTool