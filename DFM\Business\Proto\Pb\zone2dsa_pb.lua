require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.zone2dsa_editor_pb"
end

EAbandonMatchReason = {
None = 0,
Disconnect = 1,
IDIP_Kick = 2,
}
MetricType = {
MetricTypeNone = 0,
MetricTypeSum = 1,
MetricTypeVal = 2,
MetricTypeEmergency = 3,
}
pb.__pb_DsPkgHead = {
    dsPID = 0,
    player_id = 0,
    room_id = 0,
    seq = 0,
    ack = 0,
    name = "",
    plat_id = 0,
    account_type = 0,
    service_id = "",
    context = "",
    dest_service_name = "",
    dest_service_id = "",
    map_name = "",
    version = 0,
    is_return = false,
    zoneID = 0,
    ds_version = "",
    compress = false,
    origin_body_size = 0,
}
pb.__pb_DsPkgHead.__name = "DsPkgHead"
pb.__pb_DsPkgHead.__index = pb.__pb_DsPkgHead
pb.__pb_DsPkgHead.__pairs = __pb_pairs

pb.DsPkgHead = { __name = "DsPkgHead", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPkgHead : ProtoBase
---@field public dsPID number
---@field public player_id number
---@field public room_id number
---@field public seq number
---@field public ack number
---@field public name string
---@field public plat_id number
---@field public account_type number
---@field public service_id string
---@field public context string
---@field public dest_service_name string
---@field public dest_service_id string
---@field public map_name string
---@field public version number
---@field public is_return boolean
---@field public zoneID number
---@field public ds_version string
---@field public compress boolean
---@field public origin_body_size number

---@return pb_DsPkgHead
function pb.DsPkgHead:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPkg = {
    body = "",
}
pb.__pb_DsPkg.__name = "DsPkg"
pb.__pb_DsPkg.__index = pb.__pb_DsPkg
pb.__pb_DsPkg.__pairs = __pb_pairs

pb.DsPkg = { __name = "DsPkg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPkg : ProtoBase
---@field public head pb_DsPkgHead
---@field public body string

---@return pb_DsPkg
function pb.DsPkg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsaRouterMeta = {
    room_id = 0,
    zone_id = 0,
    dest_service_id = "",
}
pb.__pb_DsaRouterMeta.__name = "DsaRouterMeta"
pb.__pb_DsaRouterMeta.__index = pb.__pb_DsaRouterMeta
pb.__pb_DsaRouterMeta.__pairs = __pb_pairs

pb.DsaRouterMeta = { __name = "DsaRouterMeta", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsaRouterMeta : ProtoBase
---@field public room_id number
---@field public zone_id number
---@field public dest_service_id string

---@return pb_DsaRouterMeta
function pb.DsaRouterMeta:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFather2AnyHeartbeatNtf = {
}
pb.__pb_DsFather2AnyHeartbeatNtf.__name = "DsFather2AnyHeartbeatNtf"
pb.__pb_DsFather2AnyHeartbeatNtf.__index = pb.__pb_DsFather2AnyHeartbeatNtf
pb.__pb_DsFather2AnyHeartbeatNtf.__pairs = __pb_pairs

pb.DsFather2AnyHeartbeatNtf = { __name = "DsFather2AnyHeartbeatNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFather2AnyHeartbeatNtf : ProtoBase

---@return pb_DsFather2AnyHeartbeatNtf
function pb.DsFather2AnyHeartbeatNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFather2AnyChildExitNtf = {
    pid = 0,
    status = 0,
}
pb.__pb_DsFather2AnyChildExitNtf.__name = "DsFather2AnyChildExitNtf"
pb.__pb_DsFather2AnyChildExitNtf.__index = pb.__pb_DsFather2AnyChildExitNtf
pb.__pb_DsFather2AnyChildExitNtf.__pairs = __pb_pairs

pb.DsFather2AnyChildExitNtf = { __name = "DsFather2AnyChildExitNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFather2AnyChildExitNtf : ProtoBase
---@field public pid number
---@field public status number

---@return pb_DsFather2AnyChildExitNtf
function pb.DsFather2AnyChildExitNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyExitNtf = {
    pid = 0,
    status = 0,
    room_id = 0,
}
pb.__pb_Ds2AnyExitNtf.__name = "Ds2AnyExitNtf"
pb.__pb_Ds2AnyExitNtf.__index = pb.__pb_Ds2AnyExitNtf
pb.__pb_Ds2AnyExitNtf.__pairs = __pb_pairs

pb.Ds2AnyExitNtf = { __name = "Ds2AnyExitNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyExitNtf : ProtoBase
---@field public pid number
---@field public status number
---@field public room_id number

---@return pb_Ds2AnyExitNtf
function pb.Ds2AnyExitNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsFatherForkChildReq = {
    room_id = 0,
    cmdline = "",
    env_id = "",
}
pb.__pb_Any2DsFatherForkChildReq.__name = "Any2DsFatherForkChildReq"
pb.__pb_Any2DsFatherForkChildReq.__index = pb.__pb_Any2DsFatherForkChildReq
pb.__pb_Any2DsFatherForkChildReq.__pairs = __pb_pairs

pb.Any2DsFatherForkChildReq = { __name = "Any2DsFatherForkChildReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsFatherForkChildReq : ProtoBase
---@field public room_id number
---@field public cmdline string
---@field public env_id string

---@return pb_Any2DsFatherForkChildReq
function pb.Any2DsFatherForkChildReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFather2AnyForkChildRes = {
    room_id = 0,
    pid = 0,
}
pb.__pb_DsFather2AnyForkChildRes.__name = "DsFather2AnyForkChildRes"
pb.__pb_DsFather2AnyForkChildRes.__index = pb.__pb_DsFather2AnyForkChildRes
pb.__pb_DsFather2AnyForkChildRes.__pairs = __pb_pairs

pb.DsFather2AnyForkChildRes = { __name = "DsFather2AnyForkChildRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFather2AnyForkChildRes : ProtoBase
---@field public room_id number
---@field public pid number

---@return pb_DsFather2AnyForkChildRes
function pb.DsFather2AnyForkChildRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsFatherQuitNtf = {
}
pb.__pb_Any2DsFatherQuitNtf.__name = "Any2DsFatherQuitNtf"
pb.__pb_Any2DsFatherQuitNtf.__index = pb.__pb_Any2DsFatherQuitNtf
pb.__pb_Any2DsFatherQuitNtf.__pairs = __pb_pairs

pb.Any2DsFatherQuitNtf = { __name = "Any2DsFatherQuitNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsFatherQuitNtf : ProtoBase

---@return pb_Any2DsFatherQuitNtf
function pb.Any2DsFatherQuitNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyRoomHeartbeat = {
    room_id = 0,
}
pb.__pb_Ds2AnyRoomHeartbeat.__name = "Ds2AnyRoomHeartbeat"
pb.__pb_Ds2AnyRoomHeartbeat.__index = pb.__pb_Ds2AnyRoomHeartbeat
pb.__pb_Ds2AnyRoomHeartbeat.__pairs = __pb_pairs

pb.Ds2AnyRoomHeartbeat = { __name = "Ds2AnyRoomHeartbeat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyRoomHeartbeat : ProtoBase
---@field public room_id number
---@field public ds_stat pb_DsStat

---@return pb_Ds2AnyRoomHeartbeat
function pb.Ds2AnyRoomHeartbeat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyHeartbeatNtf = {
    ds_pid = 0,
    multi_ds = false,
}
pb.__pb_Ds2AnyHeartbeatNtf.__name = "Ds2AnyHeartbeatNtf"
pb.__pb_Ds2AnyHeartbeatNtf.__index = pb.__pb_Ds2AnyHeartbeatNtf
pb.__pb_Ds2AnyHeartbeatNtf.__pairs = __pb_pairs

pb.Ds2AnyHeartbeatNtf = { __name = "Ds2AnyHeartbeatNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyHeartbeatNtf : ProtoBase
---@field public ds_pid number
---@field public ds_stat pb_DsStat
---@field public room_list pb_Ds2AnyRoomHeartbeat[]
---@field public multi_ds boolean

---@return pb_Ds2AnyHeartbeatNtf
function pb.Ds2AnyHeartbeatNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyMultiRoomExitNtf = {
    ds_pid = 0,
    room_id = 0,
}
pb.__pb_Ds2AnyMultiRoomExitNtf.__name = "Ds2AnyMultiRoomExitNtf"
pb.__pb_Ds2AnyMultiRoomExitNtf.__index = pb.__pb_Ds2AnyMultiRoomExitNtf
pb.__pb_Ds2AnyMultiRoomExitNtf.__pairs = __pb_pairs

pb.Ds2AnyMultiRoomExitNtf = { __name = "Ds2AnyMultiRoomExitNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyMultiRoomExitNtf : ProtoBase
---@field public ds_pid number
---@field public room_id number

---@return pb_Ds2AnyMultiRoomExitNtf
function pb.Ds2AnyMultiRoomExitNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsAckNtf = {
    now = 0,
}
pb.__pb_Any2DsAckNtf.__name = "Any2DsAckNtf"
pb.__pb_Any2DsAckNtf.__index = pb.__pb_Any2DsAckNtf
pb.__pb_Any2DsAckNtf.__pairs = __pb_pairs

pb.Any2DsAckNtf = { __name = "Any2DsAckNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsAckNtf : ProtoBase
---@field public now number

---@return pb_Any2DsAckNtf
function pb.Any2DsAckNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsTODInfo = {
    weather_id = 0,
    event_id = 0,
}
pb.__pb_DsTODInfo.__name = "DsTODInfo"
pb.__pb_DsTODInfo.__index = pb.__pb_DsTODInfo
pb.__pb_DsTODInfo.__pairs = __pb_pairs

pb.DsTODInfo = { __name = "DsTODInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsTODInfo : ProtoBase
---@field public weather_id number
---@field public event_id number

---@return pb_DsTODInfo
function pb.DsTODInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AfkPunishInfo = {
    id = 0,
    type = 0,
    warning_time = 0,
    kick_time = 0,
}
pb.__pb_AfkPunishInfo.__name = "AfkPunishInfo"
pb.__pb_AfkPunishInfo.__index = pb.__pb_AfkPunishInfo
pb.__pb_AfkPunishInfo.__pairs = __pb_pairs

pb.AfkPunishInfo = { __name = "AfkPunishInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AfkPunishInfo : ProtoBase
---@field public id number
---@field public type number
---@field public warning_time number
---@field public kick_time number

---@return pb_AfkPunishInfo
function pb.AfkPunishInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsBeginMatchReq = {
    room_id = 0,
    match_type = 0,
    map_id = 0,
    zone_id = 0,
    open_replay = false,
    max_match_player_count = 0,
    min_match_player_count = 0,
    max_team_count = 0,
    map_name = "",
    map_full_name = "",
    sub_world_name = "",
    map_level = 0,
    match_open_svc = "",
    ai_level = 0,
    ailab_ai_level = 0,
    open_boss = 0,
    ds_version = "",
    close_ai_hint = false,
    ai_bt_config = 0,
    bot_difficulty = "",
    ai_drop_id = 0,
    enemy_fashion_visible = false,
    tdm_scheme_index = 0,
    map_box_config = 0,
    spawn_point_config_id = 0,
    is_open_ai_type_dynamic_switching = false,
    course_id = 0,
    scheme_id = 0,
    first_lose_id = 0,
    idc = "",
    client_group = 0,
    sol_mandel_brick_id = 0,
    player_robot_num = 0,
    match_open_svc_id = "",
    outer_port = 0,
    dstlog_udp_addr = "",
    enable_replay = false,
    match_sequence = 0,
    idc_score_load_enable = false,
    is_gray_match = false,
    random_seed = 0,
    spawn_point_seed = 0,
    open_evacuation_point = false,
    iris_event_course_id = 0,
    iris_opening_event_id = 0,
    iris_course_id = 0,
    isolate_type = 0,
    evacuation_point_config_id = 0,
    room_skill_level = 0,
    lobby_server_time = 0,
    tdm_begin_vote_ratio = 0,
    tdm_conquest_sector_vote_ratio = 0,
    tdm_game_time_ratio = 0,
    tdm_revive_time_ratio = 0,
    mode_type = 0,
    submode_type = 0,
    version = 0,
    match_subtype = 0,
    pve_mission_level = 0,
    match_halfway_start_id = 0,
    rule = 0,
    player_scheme_id = 0,
}
pb.__pb_Any2DsBeginMatchReq.__name = "Any2DsBeginMatchReq"
pb.__pb_Any2DsBeginMatchReq.__index = pb.__pb_Any2DsBeginMatchReq
pb.__pb_Any2DsBeginMatchReq.__pairs = __pb_pairs

pb.Any2DsBeginMatchReq = { __name = "Any2DsBeginMatchReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsBeginMatchReq : ProtoBase
---@field public room_id number
---@field public match_type number
---@field public map_id number
---@field public zone_id number
---@field public open_replay boolean
---@field public max_match_player_count number
---@field public min_match_player_count number
---@field public max_team_count number
---@field public map_name string
---@field public map_full_name string
---@field public sub_world_name string
---@field public map_level number
---@field public join_config pb_HalfJoinConfig
---@field public match_open_svc string
---@field public ai_level number
---@field public ailab_sdk_info pb_AILabSDKInfo
---@field public ailab_ai_level number
---@field public open_boss number
---@field public mode_info pb_MatchModeInfo
---@field public tod_info pb_DsTODInfo
---@field public ds_version string
---@field public player_info_list pb_MatchRoomTeamMemberInfo[]
---@field public close_ai_hint boolean
---@field public ai_bt_config number
---@field public bot_difficulty string
---@field public ai_drop_id number
---@field public box_event number[]
---@field public enemy_fashion_visible boolean
---@field public tdm_scheme_index number
---@field public map_box_config number
---@field public attr pb_RoomAttr
---@field public spawn_point_config_id number
---@field public is_open_ai_type_dynamic_switching boolean
---@field public course_id number
---@field public scheme_id number
---@field public first_lose_id number
---@field public idc string
---@field public client_group number
---@field public sol_mandel_brick_id number
---@field public player_robot_num number
---@field public match_open_svc_id string
---@field public outer_port number
---@field public dstlog_udp_addr string
---@field public enable_replay boolean
---@field public drop_ds_tglog_names string[]
---@field public match_sequence number
---@field public idc_score_load_enable boolean
---@field public is_gray_match boolean
---@field public trace_white_player_ids number[]
---@field public random_seed number
---@field public spawn_point_seed number
---@field public open_evacuation_point boolean
---@field public iris_event_course_id number
---@field public iris_opening_event_id number
---@field public iris_course_id number
---@field public iris_event_ids number[]
---@field public isolate_type number
---@field public holiday_array number[]
---@field public afk_punish_list pb_AfkPunishInfo[]
---@field public evacuation_point_config_id number
---@field public room_skill_level number
---@field public best_idc pb_RttBestIdc
---@field public lobby_server_time number
---@field public tdm_begin_vote_ratio number
---@field public tdm_conquest_sector_vote_ratio number
---@field public tdm_game_time_ratio number
---@field public tdm_revive_time_ratio number
---@field public roomsvr_param pb_RoomsvrParam
---@field public mode_type number
---@field public submode_type number
---@field public player_id_list number[]
---@field public version number
---@field public match_subtype number
---@field public pve_mission_level number
---@field public match_halfway_start_id number
---@field public rule number
---@field public player_scheme_id number

---@return pb_Any2DsBeginMatchReq
function pb.Any2DsBeginMatchReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerAddr = {
    player_id = 0,
    ds_ip = 0,
    ds_textual_ip = "",
    ds_port = 0,
    ds_domain = "",
}
pb.__pb_DsPlayerAddr.__name = "DsPlayerAddr"
pb.__pb_DsPlayerAddr.__index = pb.__pb_DsPlayerAddr
pb.__pb_DsPlayerAddr.__pairs = __pb_pairs

pb.DsPlayerAddr = { __name = "DsPlayerAddr", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerAddr : ProtoBase
---@field public player_id number
---@field public ds_ip number
---@field public ds_textual_ip string
---@field public ds_port number
---@field public ds_domain string

---@return pb_DsPlayerAddr
function pb.DsPlayerAddr:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyBeginMatchRes = {
    room_id = 0,
    result = 0,
    ds_ip = 0,
    ds_port = 0,
    during_time = 0,
    dsa_id = "",
    ds_domain = "",
    ds_textual_ip = "",
}
pb.__pb_Ds2AnyBeginMatchRes.__name = "Ds2AnyBeginMatchRes"
pb.__pb_Ds2AnyBeginMatchRes.__index = pb.__pb_Ds2AnyBeginMatchRes
pb.__pb_Ds2AnyBeginMatchRes.__pairs = __pb_pairs

pb.Ds2AnyBeginMatchRes = { __name = "Ds2AnyBeginMatchRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyBeginMatchRes : ProtoBase
---@field public room_id number
---@field public result number
---@field public ds_ip number
---@field public ds_port number
---@field public during_time number
---@field public dsa_id string
---@field public ds_domain string
---@field public ds_textual_ip string
---@field public player_addr_list pb_DsPlayerAddr[]
---@field public machine_load pb_DSMachineLoad
---@field public idc_load pb_IDCDSLoad

---@return pb_Ds2AnyBeginMatchRes
function pb.Ds2AnyBeginMatchRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerInfo = {
    player_id = 0,
    open_id = "",
    plat_id = 0,
    account_type = 0,
    nick_name = "",
    ds_token = "",
    is_robot = false,
    robot_level = 0,
    camp = 0,
    is_revenge = false,
    match_pool_id = 0,
    mode_id = 0,
    team_id = 0,
    leader_id = 0,
    player_idx = 0,
    match_tactics = 0,
    skill_score = 0,
    course_id = 0,
    drop_logic_id = 0,
    drop_buff_id = 0,
    lobby_server_time = 0,
    quests_get_time = 0,
    spawn_point = 0,
    is_fashion_prior = false,
    robot_type = 0,
    half_join = false,
    replace_bot_id = 0,
    pic_url = "",
    difficulty = 0,
    ai_score = 0,
    ai_style = 0,
    is_ds_guide_passed = false,
    is_guide = false,
    best_idc = "",
    best_access_point = "",
    best_idc_rtt = 0,
    game_appid = "",
    glicko_rating = 0,
    glicko_rating_dev = 0,
    fashion_id = 0,
    level = 0,
    cur_exp = 0,
    match_uuid = "",
    sys_team_id = 0,
    safebox_skin_id = 0,
    isRankedMatch = false,
    rank_match_score = 0,
    ranked_score_shoot = 0,
    ranked_score_tactics = 0,
    ranked_score_vehicle = 0,
    sol_double_rank_multiple_value = 0,
    is_observer = false,
    observer_id = 0,
    is_single_player = false,
    hidden_score = 0,
    old_glicko_rating = 0,
    old_glicko_rating_dev = 0,
    title = 0,
    rank_title_adcode = 0,
    rank_title_rank_no = 0,
    use_rental_props = false,
    is_bought_bhd = false,
    ailab_diffculty = "",
    deposit_price = 0,
    mandel_brick_num = 0,
    dogtag_custom_id = 0,
    country_code = 0,
    is_victory_unite_match = false,
    has_trigger_gold_egg = false,
}
pb.__pb_DsPlayerInfo.__name = "DsPlayerInfo"
pb.__pb_DsPlayerInfo.__index = pb.__pb_DsPlayerInfo
pb.__pb_DsPlayerInfo.__pairs = __pb_pairs

pb.DsPlayerInfo = { __name = "DsPlayerInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerInfo : ProtoBase
---@field public player_id number
---@field public open_id string
---@field public plat_id number
---@field public account_type number
---@field public nick_name string
---@field public ds_token string
---@field public is_robot boolean
---@field public robot_level number
---@field public camp number
---@field public is_revenge boolean
---@field public match_pool_id number
---@field public mode_id number
---@field public team_id number
---@field public leader_id number
---@field public player_idx number
---@field public match_tactics number
---@field public skill_score number
---@field public ai_rand_prop_id number[]
---@field public course_id number
---@field public drop_logic_id number
---@field public drop_buff_id number
---@field public drop_counters pb_MatchSOLDropCounter[]
---@field public equiped_props pb_EquipPosition[]
---@field public role_attr_array pb_DSAttrValue[]
---@field public role_buff_array pb_AttrBuff[]
---@field public s_price_values number[]
---@field public general_skills pb_GeneralSkill[]
---@field public hero pb_Hero
---@field public safehouse_info pb_SafehouseNumeralInfo
---@field public player_accepted_quests pb_PlayerQuestData[]
---@field public ds_quests_desc_list pb_DSQuestDesc[]
---@field public player_completed_quests pb_PlayerQuestData[]
---@field public quest_world_game_state pb_QuestDSGameState[]
---@field public player_unaccept_quests pb_PlayerQuestData[]
---@field public lobby_server_time number
---@field public quests_get_time number
---@field public marked_props pb_MarkPropInfo[]
---@field public strongholds pb_StrongholdInfo[]
---@field public world_actors pb_WorldActorInfo[]
---@field public spawn_point number
---@field public season_area_array number[]
---@field public season_prop_array number[]
---@field public role_load pb_RoleLoadValue
---@field public tdm_numeral_data pb_TDMNumeral
---@field public armedforce_data pb_ArmedForceNumeralData
---@field public heros pb_DSHero[]
---@field public is_fashion_prior boolean
---@field public weapon_skin_setup pb_WeaponSkinSetup[]
---@field public weapon_skin_info pb_WeaponSkinInfo[]
---@field public robot_type number
---@field public half_join boolean
---@field public replace_bot_id number
---@field public pic_url string
---@field public difficulty number
---@field public ai_score number
---@field public ai_style number
---@field public is_ds_guide_passed boolean
---@field public is_guide boolean
---@field public best_idc string
---@field public best_access_point string
---@field public best_idc_rtt number
---@field public activity_data pb_NumeralActivityData
---@field public game_appid string
---@field public tss_tlog_common_info pb_TssTLogHeader
---@field public glicko_rating number
---@field public glicko_rating_dev number
---@field public fashion_id number
---@field public level number
---@field public cur_exp number
---@field public match_uuid string
---@field public sys_team_id number
---@field public player_rtt_data pb_PlayerRttData
---@field public equip_skin_props pb_PropInfo[]
---@field public safebox_skin_id number
---@field public isRankedMatch boolean
---@field public rank_match_score number
---@field public ranked_score_shoot number
---@field public ranked_score_tactics number
---@field public ranked_score_vehicle number
---@field public sol_double_rank_multiple_value number
---@field public tdm_detail pb_TDMDsDetail
---@field public is_observer boolean
---@field public observer_id number
---@field public is_single_player boolean
---@field public hidden_score number
---@field public old_glicko_rating number
---@field public old_glicko_rating_dev number
---@field public guide_price_pkg pb_Any2DsPropGuidePriceNtf
---@field public title number
---@field public rank_title_adcode number
---@field public rank_title_rank_no number
---@field public use_rental_props boolean
---@field public equip_pendant_props pb_PropInfo[]
---@field public is_bought_bhd boolean
---@field public bhd_special_data pb_BhdSpecData
---@field public mp_rank_numeral pb_MpRankNumeral
---@field public commander_numeral pb_CommanderNumeral
---@field public ailab_diffculty string
---@field public deposit_price number
---@field public mandel_brick_num number
---@field public roomsvr_numeral pb_RoomsvrNumeral
---@field public dogtag_custom_id number
---@field public country_code number
---@field public is_victory_unite_match boolean
---@field public has_trigger_gold_egg boolean

---@return pb_DsPlayerInfo
function pb.DsPlayerInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsSyncPlayerInfoReq = {
    room_id = 0,
    dsa_id = "",
    call_id = 0,
    numeral_svr = "",
}
pb.__pb_Any2DsSyncPlayerInfoReq.__name = "Any2DsSyncPlayerInfoReq"
pb.__pb_Any2DsSyncPlayerInfoReq.__index = pb.__pb_Any2DsSyncPlayerInfoReq
pb.__pb_Any2DsSyncPlayerInfoReq.__pairs = __pb_pairs

pb.Any2DsSyncPlayerInfoReq = { __name = "Any2DsSyncPlayerInfoReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsSyncPlayerInfoReq : ProtoBase
---@field public room_id number
---@field public player_info pb_DsPlayerInfo[]
---@field public dsa_id string
---@field public call_id number
---@field public numeral_svr string

---@return pb_Any2DsSyncPlayerInfoReq
function pb.Any2DsSyncPlayerInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsSyncPlayerInfoRes = {
    result = 0,
}
pb.__pb_Any2DsSyncPlayerInfoRes.__name = "Any2DsSyncPlayerInfoRes"
pb.__pb_Any2DsSyncPlayerInfoRes.__index = pb.__pb_Any2DsSyncPlayerInfoRes
pb.__pb_Any2DsSyncPlayerInfoRes.__pairs = __pb_pairs

pb.Any2DsSyncPlayerInfoRes = { __name = "Any2DsSyncPlayerInfoRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsSyncPlayerInfoRes : ProtoBase
---@field public result number

---@return pb_Any2DsSyncPlayerInfoRes
function pb.Any2DsSyncPlayerInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsSyncTDMWeaponStoreReq = {
    room_id = 0,
    player_id = 0,
    seq = 0,
    total_seq = 0,
}
pb.__pb_Any2DsSyncTDMWeaponStoreReq.__name = "Any2DsSyncTDMWeaponStoreReq"
pb.__pb_Any2DsSyncTDMWeaponStoreReq.__index = pb.__pb_Any2DsSyncTDMWeaponStoreReq
pb.__pb_Any2DsSyncTDMWeaponStoreReq.__pairs = __pb_pairs

pb.Any2DsSyncTDMWeaponStoreReq = { __name = "Any2DsSyncTDMWeaponStoreReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsSyncTDMWeaponStoreReq : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public tdm_prop_list pb_TDMNumeralProp[]
---@field public seq number
---@field public total_seq number
---@field public weapon_store_bytes string[]
---@field public weapon_store pb_PropInfo[]

---@return pb_Any2DsSyncTDMWeaponStoreReq
function pb.Any2DsSyncTDMWeaponStoreReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsSyncTDMWeaponDesignReq = {
    room_id = 0,
    player_id = 0,
    seq = 0,
    total_seq = 0,
}
pb.__pb_Any2DsSyncTDMWeaponDesignReq.__name = "Any2DsSyncTDMWeaponDesignReq"
pb.__pb_Any2DsSyncTDMWeaponDesignReq.__index = pb.__pb_Any2DsSyncTDMWeaponDesignReq
pb.__pb_Any2DsSyncTDMWeaponDesignReq.__pairs = __pb_pairs

pb.Any2DsSyncTDMWeaponDesignReq = { __name = "Any2DsSyncTDMWeaponDesignReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsSyncTDMWeaponDesignReq : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public seq number
---@field public total_seq number
---@field public weapon_designs_bytes string[]
---@field public weapon_designs pb_TDMWeaponDesign[]

---@return pb_Any2DsSyncTDMWeaponDesignReq
function pb.Any2DsSyncTDMWeaponDesignReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsPlayerCsvTableReq = {
    room_id = 0,
    dsa_id = "",
    player_id = 0,
}
pb.__pb_Any2DsPlayerCsvTableReq.__name = "Any2DsPlayerCsvTableReq"
pb.__pb_Any2DsPlayerCsvTableReq.__index = pb.__pb_Any2DsPlayerCsvTableReq
pb.__pb_Any2DsPlayerCsvTableReq.__pairs = __pb_pairs

pb.Any2DsPlayerCsvTableReq = { __name = "Any2DsPlayerCsvTableReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsPlayerCsvTableReq : ProtoBase
---@field public room_id number
---@field public dsa_id string
---@field public player_id number
---@field public pkg pb_CsvZipPkg

---@return pb_Any2DsPlayerCsvTableReq
function pb.Any2DsPlayerCsvTableReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnySyncPlayerInfoRes = {
    result = 0,
    call_id = 0,
    room_id = 0,
    numeral_svr = "",
    player_id = 0,
    is_robot = false,
}
pb.__pb_Ds2AnySyncPlayerInfoRes.__name = "Ds2AnySyncPlayerInfoRes"
pb.__pb_Ds2AnySyncPlayerInfoRes.__index = pb.__pb_Ds2AnySyncPlayerInfoRes
pb.__pb_Ds2AnySyncPlayerInfoRes.__pairs = __pb_pairs

pb.Ds2AnySyncPlayerInfoRes = { __name = "Ds2AnySyncPlayerInfoRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnySyncPlayerInfoRes : ProtoBase
---@field public result number
---@field public call_id number
---@field public room_id number
---@field public numeral_svr string
---@field public player_id number
---@field public is_robot boolean
---@field public player_addr_list pb_DsPlayerAddr[]

---@return pb_Ds2AnySyncPlayerInfoRes
function pb.Ds2AnySyncPlayerInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsPlayerAbandonMatchNtf = {
    room_id = 0,
    player_id = 0,
    reason = 0,
    dsa_id = "",
}
pb.__pb_Any2DsPlayerAbandonMatchNtf.__name = "Any2DsPlayerAbandonMatchNtf"
pb.__pb_Any2DsPlayerAbandonMatchNtf.__index = pb.__pb_Any2DsPlayerAbandonMatchNtf
pb.__pb_Any2DsPlayerAbandonMatchNtf.__pairs = __pb_pairs

pb.Any2DsPlayerAbandonMatchNtf = { __name = "Any2DsPlayerAbandonMatchNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsPlayerAbandonMatchNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public reason number
---@field public dsa_id string

---@return pb_Any2DsPlayerAbandonMatchNtf
function pb.Any2DsPlayerAbandonMatchNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyPlayerLoginDsNtf = {
    room_id = 0,
    player_id = 0,
}
pb.__pb_Ds2AnyPlayerLoginDsNtf.__name = "Ds2AnyPlayerLoginDsNtf"
pb.__pb_Ds2AnyPlayerLoginDsNtf.__index = pb.__pb_Ds2AnyPlayerLoginDsNtf
pb.__pb_Ds2AnyPlayerLoginDsNtf.__pairs = __pb_pairs

pb.Ds2AnyPlayerLoginDsNtf = { __name = "Ds2AnyPlayerLoginDsNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyPlayerLoginDsNtf : ProtoBase
---@field public room_id number
---@field public player_id number

---@return pb_Ds2AnyPlayerLoginDsNtf
function pb.Ds2AnyPlayerLoginDsNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyPlayerLogoutDsNtf = {
    room_id = 0,
    player_id = 0,
    logout_reason = 0,
    quit_team_id = 0,
}
pb.__pb_Ds2AnyPlayerLogoutDsNtf.__name = "Ds2AnyPlayerLogoutDsNtf"
pb.__pb_Ds2AnyPlayerLogoutDsNtf.__index = pb.__pb_Ds2AnyPlayerLogoutDsNtf
pb.__pb_Ds2AnyPlayerLogoutDsNtf.__pairs = __pb_pairs

pb.Ds2AnyPlayerLogoutDsNtf = { __name = "Ds2AnyPlayerLogoutDsNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyPlayerLogoutDsNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public logout_reason number
---@field public quit_team_id number

---@return pb_Ds2AnyPlayerLogoutDsNtf
function pb.Ds2AnyPlayerLogoutDsNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSimplePlayerInfo = {
    player_id = 0,
    name = "",
    team_id = 0,
    team_name = "",
    player_idx = 0,
    camp_id = 0,
}
pb.__pb_DsSimplePlayerInfo.__name = "DsSimplePlayerInfo"
pb.__pb_DsSimplePlayerInfo.__index = pb.__pb_DsSimplePlayerInfo
pb.__pb_DsSimplePlayerInfo.__pairs = __pb_pairs

pb.DsSimplePlayerInfo = { __name = "DsSimplePlayerInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSimplePlayerInfo : ProtoBase
---@field public player_id number
---@field public name string
---@field public team_id number
---@field public team_name string
---@field public player_idx number
---@field public camp_id number

---@return pb_DsSimplePlayerInfo
function pb.DsSimplePlayerInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyRoomInfoNtf = {
    room_id = 0,
    enable_half_join = false,
}
pb.__pb_Ds2AnyRoomInfoNtf.__name = "Ds2AnyRoomInfoNtf"
pb.__pb_Ds2AnyRoomInfoNtf.__index = pb.__pb_Ds2AnyRoomInfoNtf
pb.__pb_Ds2AnyRoomInfoNtf.__pairs = __pb_pairs

pb.Ds2AnyRoomInfoNtf = { __name = "Ds2AnyRoomInfoNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyRoomInfoNtf : ProtoBase
---@field public room_id number
---@field public enable_half_join boolean
---@field public player_infos pb_DsSimplePlayerInfo[]

---@return pb_Ds2AnyRoomInfoNtf
function pb.Ds2AnyRoomInfoNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFather2AnyLaunchNtf = {
    elapsed_time = 0,
}
pb.__pb_DsFather2AnyLaunchNtf.__name = "DsFather2AnyLaunchNtf"
pb.__pb_DsFather2AnyLaunchNtf.__index = pb.__pb_DsFather2AnyLaunchNtf
pb.__pb_DsFather2AnyLaunchNtf.__pairs = __pb_pairs

pb.DsFather2AnyLaunchNtf = { __name = "DsFather2AnyLaunchNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFather2AnyLaunchNtf : ProtoBase
---@field public elapsed_time number

---@return pb_DsFather2AnyLaunchNtf
function pb.DsFather2AnyLaunchNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyFramerateStatNtf = {
    total_frame = 0,
    total_time = 0,
    total_cpu_time = 0,
    max_cpu_time = 0,
    fps_count_0_30 = 0,
    fps_count_30_50 = 0,
    fps_count_50_200 = 0,
    fps_count_200_500 = 0,
    fps_count_500_2000 = 0,
    fps_count_2000 = 0,
}
pb.__pb_Ds2AnyFramerateStatNtf.__name = "Ds2AnyFramerateStatNtf"
pb.__pb_Ds2AnyFramerateStatNtf.__index = pb.__pb_Ds2AnyFramerateStatNtf
pb.__pb_Ds2AnyFramerateStatNtf.__pairs = __pb_pairs

pb.Ds2AnyFramerateStatNtf = { __name = "Ds2AnyFramerateStatNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyFramerateStatNtf : ProtoBase
---@field public total_frame number
---@field public total_time number
---@field public total_cpu_time number
---@field public max_cpu_time number
---@field public fps_count_0_30 number
---@field public fps_count_30_50 number
---@field public fps_count_50_200 number
---@field public fps_count_200_500 number
---@field public fps_count_500_2000 number
---@field public fps_count_2000 number

---@return pb_Ds2AnyFramerateStatNtf
function pb.Ds2AnyFramerateStatNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyMatchInfoNtf = {
    room_id = 0,
    player_num = 0,
    pmc_num = 0,
    scav_num = 0,
    boss_killed = false,
    property = 0,
}
pb.__pb_Ds2AnyMatchInfoNtf.__name = "Ds2AnyMatchInfoNtf"
pb.__pb_Ds2AnyMatchInfoNtf.__index = pb.__pb_Ds2AnyMatchInfoNtf
pb.__pb_Ds2AnyMatchInfoNtf.__pairs = __pb_pairs

pb.Ds2AnyMatchInfoNtf = { __name = "Ds2AnyMatchInfoNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyMatchInfoNtf : ProtoBase
---@field public room_id number
---@field public player_num number
---@field public pmc_num number
---@field public scav_num number
---@field public boss_killed boolean
---@field public property number

---@return pb_Ds2AnyMatchInfoNtf
function pb.Ds2AnyMatchInfoNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyLogTgLogNtf = {
    player_id = 0,
    is_tss_log = false,
}
pb.__pb_Ds2AnyLogTgLogNtf.__name = "Ds2AnyLogTgLogNtf"
pb.__pb_Ds2AnyLogTgLogNtf.__index = pb.__pb_Ds2AnyLogTgLogNtf
pb.__pb_Ds2AnyLogTgLogNtf.__pairs = __pb_pairs

pb.Ds2AnyLogTgLogNtf = { __name = "Ds2AnyLogTgLogNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyLogTgLogNtf : ProtoBase
---@field public player_id number
---@field public entry_array pb_TgLogEntry[]
---@field public is_tss_log boolean

---@return pb_Ds2AnyLogTgLogNtf
function pb.Ds2AnyLogTgLogNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2RawLogTglogNtf = {
}
pb.__pb_Ds2RawLogTglogNtf.__name = "Ds2RawLogTglogNtf"
pb.__pb_Ds2RawLogTglogNtf.__index = pb.__pb_Ds2RawLogTglogNtf
pb.__pb_Ds2RawLogTglogNtf.__pairs = __pb_pairs

pb.Ds2RawLogTglogNtf = { __name = "Ds2RawLogTglogNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2RawLogTglogNtf : ProtoBase
---@field public entry_array string[]

---@return pb_Ds2RawLogTglogNtf
function pb.Ds2RawLogTglogNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ReportMonitorLabel = {
    label = "",
    value = "",
}
pb.__pb_ReportMonitorLabel.__name = "ReportMonitorLabel"
pb.__pb_ReportMonitorLabel.__index = pb.__pb_ReportMonitorLabel
pb.__pb_ReportMonitorLabel.__pairs = __pb_pairs

pb.ReportMonitorLabel = { __name = "ReportMonitorLabel", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ReportMonitorLabel : ProtoBase
---@field public label string
---@field public value string

---@return pb_ReportMonitorLabel
function pb.ReportMonitorLabel:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ReportMonitorMetric = {
    id = "",
    type = 0,
    val = 0,
    inc = 0,
    emergency = "",
}
pb.__pb_ReportMonitorMetric.__name = "ReportMonitorMetric"
pb.__pb_ReportMonitorMetric.__index = pb.__pb_ReportMonitorMetric
pb.__pb_ReportMonitorMetric.__pairs = __pb_pairs

pb.ReportMonitorMetric = { __name = "ReportMonitorMetric", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ReportMonitorMetric : ProtoBase
---@field public id string
---@field public type number
---@field public val number
---@field public inc number
---@field public emergency string
---@field public labels pb_ReportMonitorLabel[]

---@return pb_ReportMonitorMetric
function pb.ReportMonitorMetric:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DS2AnyReportMonitorNtf = {
}
pb.__pb_DS2AnyReportMonitorNtf.__name = "DS2AnyReportMonitorNtf"
pb.__pb_DS2AnyReportMonitorNtf.__index = pb.__pb_DS2AnyReportMonitorNtf
pb.__pb_DS2AnyReportMonitorNtf.__pairs = __pb_pairs

pb.DS2AnyReportMonitorNtf = { __name = "DS2AnyReportMonitorNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DS2AnyReportMonitorNtf : ProtoBase
---@field public metrics pb_ReportMonitorMetric[]

---@return pb_DS2AnyReportMonitorNtf
function pb.DS2AnyReportMonitorNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsagentSendToZoneReq = {
    service_name = "",
    service_id = "",
    name = "",
    payload = "",
    room_id = 0,
}
pb.__pb_Any2DsagentSendToZoneReq.__name = "Any2DsagentSendToZoneReq"
pb.__pb_Any2DsagentSendToZoneReq.__index = pb.__pb_Any2DsagentSendToZoneReq
pb.__pb_Any2DsagentSendToZoneReq.__pairs = __pb_pairs

pb.Any2DsagentSendToZoneReq = { __name = "Any2DsagentSendToZoneReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsagentSendToZoneReq : ProtoBase
---@field public service_name string
---@field public service_id string
---@field public name string
---@field public payload string
---@field public room_id number

---@return pb_Any2DsagentSendToZoneReq
function pb.Any2DsagentSendToZoneReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Dsagent2AnySendToZoneRes = {
    result = 0,
}
pb.__pb_Dsagent2AnySendToZoneRes.__name = "Dsagent2AnySendToZoneRes"
pb.__pb_Dsagent2AnySendToZoneRes.__index = pb.__pb_Dsagent2AnySendToZoneRes
pb.__pb_Dsagent2AnySendToZoneRes.__pairs = __pb_pairs

pb.Dsagent2AnySendToZoneRes = { __name = "Dsagent2AnySendToZoneRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Dsagent2AnySendToZoneRes : ProtoBase
---@field public result number

---@return pb_Dsagent2AnySendToZoneRes
function pb.Dsagent2AnySendToZoneRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyMatchEndNtf = {
    room_id = 0,
    reason = 0,
    MatchType = 0,
}
pb.__pb_Ds2AnyMatchEndNtf.__name = "Ds2AnyMatchEndNtf"
pb.__pb_Ds2AnyMatchEndNtf.__index = pb.__pb_Ds2AnyMatchEndNtf
pb.__pb_Ds2AnyMatchEndNtf.__pairs = __pb_pairs

pb.Ds2AnyMatchEndNtf = { __name = "Ds2AnyMatchEndNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyMatchEndNtf : ProtoBase
---@field public room_id number
---@field public reason number
---@field public not_settlement_players number[]
---@field public mode_info pb_MatchModeInfo
---@field public MatchType number

---@return pb_Ds2AnyMatchEndNtf
function pb.Ds2AnyMatchEndNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyTkvSettlementReq = {
    player_id = 0,
    match_tactics = 0,
    dps = 0,
    last_time_props_price = 0,
    loot_total_price = 0,
    loot_expected_price = 0,
    contract_num = 0,
    fighting_with_player_num = 0,
    fighting_with_ai_num = 0,
}
pb.__pb_Ds2AnyTkvSettlementReq.__name = "Ds2AnyTkvSettlementReq"
pb.__pb_Ds2AnyTkvSettlementReq.__index = pb.__pb_Ds2AnyTkvSettlementReq
pb.__pb_Ds2AnyTkvSettlementReq.__pairs = __pb_pairs

pb.Ds2AnyTkvSettlementReq = { __name = "Ds2AnyTkvSettlementReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyTkvSettlementReq : ProtoBase
---@field public player_id number
---@field public match_info pb_DsMatchInfoNew
---@field public team_info pb_DsTeamInfo
---@field public status_array pb_DsPlayerGameStatus[]
---@field public player_quests pb_DsQuestData
---@field public marked_props pb_MarkPropInfo[]
---@field public match_tactics number
---@field public dps number
---@field public last_time_props_price number
---@field public loot_total_price number
---@field public loot_expected_price number
---@field public contract_num number
---@field public fighting_with_player_num number
---@field public fighting_with_ai_num number

---@return pb_Ds2AnyTkvSettlementReq
function pb.Ds2AnyTkvSettlementReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsTkvSettlementRes = {
    result = 0,
}
pb.__pb_Any2DsTkvSettlementRes.__name = "Any2DsTkvSettlementRes"
pb.__pb_Any2DsTkvSettlementRes.__index = pb.__pb_Any2DsTkvSettlementRes
pb.__pb_Any2DsTkvSettlementRes.__pairs = __pb_pairs

pb.Any2DsTkvSettlementRes = { __name = "Any2DsTkvSettlementRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsTkvSettlementRes : ProtoBase
---@field public result number

---@return pb_Any2DsTkvSettlementRes
function pb.Any2DsTkvSettlementRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyPVESettlementReq = {
    player_id = 0,
}
pb.__pb_Ds2AnyPVESettlementReq.__name = "Ds2AnyPVESettlementReq"
pb.__pb_Ds2AnyPVESettlementReq.__index = pb.__pb_Ds2AnyPVESettlementReq
pb.__pb_Ds2AnyPVESettlementReq.__pairs = __pb_pairs

pb.Ds2AnyPVESettlementReq = { __name = "Ds2AnyPVESettlementReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyPVESettlementReq : ProtoBase
---@field public player_id number
---@field public mission_info pb_DsPVEMissionInfo
---@field public status_array pb_DsPVEPlayerStatus[]

---@return pb_Ds2AnyPVESettlementReq
function pb.Ds2AnyPVESettlementReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsPVESettlementRes = {
    result = 0,
}
pb.__pb_Any2DsPVESettlementRes.__name = "Any2DsPVESettlementRes"
pb.__pb_Any2DsPVESettlementRes.__index = pb.__pb_Any2DsPVESettlementRes
pb.__pb_Any2DsPVESettlementRes.__pairs = __pb_pairs

pb.Any2DsPVESettlementRes = { __name = "Any2DsPVESettlementRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsPVESettlementRes : ProtoBase
---@field public result number

---@return pb_Any2DsPVESettlementRes
function pb.Any2DsPVESettlementRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyPlayerSettlementReq = {
    match_result = 0,
    outfit_price = 0,
}
pb.__pb_Ds2AnyPlayerSettlementReq.__name = "Ds2AnyPlayerSettlementReq"
pb.__pb_Ds2AnyPlayerSettlementReq.__index = pb.__pb_Ds2AnyPlayerSettlementReq
pb.__pb_Ds2AnyPlayerSettlementReq.__pairs = __pb_pairs

pb.Ds2AnyPlayerSettlementReq = { __name = "Ds2AnyPlayerSettlementReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyPlayerSettlementReq : ProtoBase
---@field public match_result number
---@field public outfit_price number
---@field public outfit pb_EquipPosition[]

---@return pb_Ds2AnyPlayerSettlementReq
function pb.Ds2AnyPlayerSettlementReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsPlayerSettlementRes = {
    result = 0,
}
pb.__pb_Any2DsPlayerSettlementRes.__name = "Any2DsPlayerSettlementRes"
pb.__pb_Any2DsPlayerSettlementRes.__index = pb.__pb_Any2DsPlayerSettlementRes
pb.__pb_Any2DsPlayerSettlementRes.__pairs = __pb_pairs

pb.Any2DsPlayerSettlementRes = { __name = "Any2DsPlayerSettlementRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsPlayerSettlementRes : ProtoBase
---@field public result number

---@return pb_Any2DsPlayerSettlementRes
function pb.Any2DsPlayerSettlementRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerLostItems = {
    player_id = 0,
    status = 0,
    result = 0,
}
pb.__pb_PlayerLostItems.__name = "PlayerLostItems"
pb.__pb_PlayerLostItems.__index = pb.__pb_PlayerLostItems
pb.__pb_PlayerLostItems.__pairs = __pb_pairs

pb.PlayerLostItems = { __name = "PlayerLostItems", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerLostItems : ProtoBase
---@field public player_id number
---@field public status number
---@field public result number
---@field public item_array pb_PropInfo[]
---@field public looting_items_array pb_PropInfo[]

---@return pb_PlayerLostItems
function pb.PlayerLostItems:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyMatchSettlementReq = {
    match_id = 0,
    map_id = 0,
    match_type = 0,
    match_subtype = 0,
    game_mode = 0,
    game_submode = 0,
    end_time = 0,
}
pb.__pb_Ds2AnyMatchSettlementReq.__name = "Ds2AnyMatchSettlementReq"
pb.__pb_Ds2AnyMatchSettlementReq.__index = pb.__pb_Ds2AnyMatchSettlementReq
pb.__pb_Ds2AnyMatchSettlementReq.__pairs = __pb_pairs

pb.Ds2AnyMatchSettlementReq = { __name = "Ds2AnyMatchSettlementReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyMatchSettlementReq : ProtoBase
---@field public match_id number
---@field public map_id number
---@field public match_type number
---@field public match_subtype number
---@field public game_mode number
---@field public game_submode number
---@field public end_time number
---@field public lost_items_array pb_PlayerLostItems[]

---@return pb_Ds2AnyMatchSettlementReq
function pb.Ds2AnyMatchSettlementReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsMatchSettlementRes = {
    result = 0,
}
pb.__pb_Any2DsMatchSettlementRes.__name = "Any2DsMatchSettlementRes"
pb.__pb_Any2DsMatchSettlementRes.__index = pb.__pb_Any2DsMatchSettlementRes
pb.__pb_Any2DsMatchSettlementRes.__pairs = __pb_pairs

pb.Any2DsMatchSettlementRes = { __name = "Any2DsMatchSettlementRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsMatchSettlementRes : ProtoBase
---@field public result number

---@return pb_Any2DsMatchSettlementRes
function pb.Any2DsMatchSettlementRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsBeginRaidReq = {
    room_id = 0,
    raid_id = 0,
}
pb.__pb_Any2DsBeginRaidReq.__name = "Any2DsBeginRaidReq"
pb.__pb_Any2DsBeginRaidReq.__index = pb.__pb_Any2DsBeginRaidReq
pb.__pb_Any2DsBeginRaidReq.__pairs = __pb_pairs

pb.Any2DsBeginRaidReq = { __name = "Any2DsBeginRaidReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsBeginRaidReq : ProtoBase
---@field public room_id number
---@field public raid_id number
---@field public player_array number[]

---@return pb_Any2DsBeginRaidReq
function pb.Any2DsBeginRaidReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyBeginRaidRes = {
    room_id = 0,
    raid_id = 0,
    result = 0,
}
pb.__pb_Ds2AnyBeginRaidRes.__name = "Ds2AnyBeginRaidRes"
pb.__pb_Ds2AnyBeginRaidRes.__index = pb.__pb_Ds2AnyBeginRaidRes
pb.__pb_Ds2AnyBeginRaidRes.__pairs = __pb_pairs

pb.Ds2AnyBeginRaidRes = { __name = "Ds2AnyBeginRaidRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyBeginRaidRes : ProtoBase
---@field public room_id number
---@field public raid_id number
---@field public result number

---@return pb_Ds2AnyBeginRaidRes
function pb.Ds2AnyBeginRaidRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsCanHalfJoinReq = {
    dsa_id = "",
    room_id = 0,
    camp = 0,
    team_id = 0,
    caller = "",
}
pb.__pb_Any2DsCanHalfJoinReq.__name = "Any2DsCanHalfJoinReq"
pb.__pb_Any2DsCanHalfJoinReq.__index = pb.__pb_Any2DsCanHalfJoinReq
pb.__pb_Any2DsCanHalfJoinReq.__pairs = __pb_pairs

pb.Any2DsCanHalfJoinReq = { __name = "Any2DsCanHalfJoinReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsCanHalfJoinReq : ProtoBase
---@field public dsa_id string
---@field public room_id number
---@field public player_ids number[]
---@field public camp number
---@field public team_id number
---@field public caller string

---@return pb_Any2DsCanHalfJoinReq
function pb.Any2DsCanHalfJoinReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsCanHalfJoinRes = {
    result = 0,
    can_half_join = false,
}
pb.__pb_Any2DsCanHalfJoinRes.__name = "Any2DsCanHalfJoinRes"
pb.__pb_Any2DsCanHalfJoinRes.__index = pb.__pb_Any2DsCanHalfJoinRes
pb.__pb_Any2DsCanHalfJoinRes.__pairs = __pb_pairs

pb.Any2DsCanHalfJoinRes = { __name = "Any2DsCanHalfJoinRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsCanHalfJoinRes : ProtoBase
---@field public result number
---@field public can_half_join boolean

---@return pb_Any2DsCanHalfJoinRes
function pb.Any2DsCanHalfJoinRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSEmptySettlementNtf = {
    result = 0,
    mode = 0,
    room_id = 0,
}
pb.__pb_CSEmptySettlementNtf.__name = "CSEmptySettlementNtf"
pb.__pb_CSEmptySettlementNtf.__index = pb.__pb_CSEmptySettlementNtf
pb.__pb_CSEmptySettlementNtf.__pairs = __pb_pairs

pb.CSEmptySettlementNtf = { __name = "CSEmptySettlementNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSEmptySettlementNtf : ProtoBase
---@field public result number
---@field public mode number
---@field public room_id number

---@return pb_CSEmptySettlementNtf
function pb.CSEmptySettlementNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSSettlementEmptyNtf = {
    result = 0,
    mode = 0,
    room_id = 0,
}
pb.__pb_CSSettlementEmptyNtf.__name = "CSSettlementEmptyNtf"
pb.__pb_CSSettlementEmptyNtf.__index = pb.__pb_CSSettlementEmptyNtf
pb.__pb_CSSettlementEmptyNtf.__pairs = __pb_pairs

pb.CSSettlementEmptyNtf = { __name = "CSSettlementEmptyNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSSettlementEmptyNtf : ProtoBase
---@field public result number
---@field public mode number
---@field public room_id number

---@return pb_CSSettlementEmptyNtf
function pb.CSSettlementEmptyNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFatherExitNtf = {
    pid = 0,
    status = 0,
    reason = 0,
}
pb.__pb_DsFatherExitNtf.__name = "DsFatherExitNtf"
pb.__pb_DsFatherExitNtf.__index = pb.__pb_DsFatherExitNtf
pb.__pb_DsFatherExitNtf.__pairs = __pb_pairs

pb.DsFatherExitNtf = { __name = "DsFatherExitNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFatherExitNtf : ProtoBase
---@field public pid number
---@field public status number
---@field public reason number

---@return pb_DsFatherExitNtf
function pb.DsFatherExitNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsPropGuidePriceNtf = {
}
pb.__pb_Any2DsPropGuidePriceNtf.__name = "Any2DsPropGuidePriceNtf"
pb.__pb_Any2DsPropGuidePriceNtf.__index = pb.__pb_Any2DsPropGuidePriceNtf
pb.__pb_Any2DsPropGuidePriceNtf.__pairs = __pb_pairs

pb.Any2DsPropGuidePriceNtf = { __name = "Any2DsPropGuidePriceNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsPropGuidePriceNtf : ProtoBase
---@field public price_list pb_PropGuidePrice[]

---@return pb_Any2DsPropGuidePriceNtf
function pb.Any2DsPropGuidePriceNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSafeHousePlayerInfo = {
    player_id = 0,
}
pb.__pb_DsSafeHousePlayerInfo.__name = "DsSafeHousePlayerInfo"
pb.__pb_DsSafeHousePlayerInfo.__index = pb.__pb_DsSafeHousePlayerInfo
pb.__pb_DsSafeHousePlayerInfo.__pairs = __pb_pairs

pb.DsSafeHousePlayerInfo = { __name = "DsSafeHousePlayerInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSafeHousePlayerInfo : ProtoBase
---@field public player_id number
---@field public equiped_props pb_EquipPosition[]
---@field public heros pb_DSHero

---@return pb_DsSafeHousePlayerInfo
function pb.DsSafeHousePlayerInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsSyncSafeHousePlayerInfoReq = {
    room_id = 0,
    dsa_id = "",
    call_id = 0,
    call_svr = "",
}
pb.__pb_Any2DsSyncSafeHousePlayerInfoReq.__name = "Any2DsSyncSafeHousePlayerInfoReq"
pb.__pb_Any2DsSyncSafeHousePlayerInfoReq.__index = pb.__pb_Any2DsSyncSafeHousePlayerInfoReq
pb.__pb_Any2DsSyncSafeHousePlayerInfoReq.__pairs = __pb_pairs

pb.Any2DsSyncSafeHousePlayerInfoReq = { __name = "Any2DsSyncSafeHousePlayerInfoReq", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsSyncSafeHousePlayerInfoReq : ProtoBase
---@field public room_id number
---@field public player_info pb_DsSafeHousePlayerInfo[]
---@field public dsa_id string
---@field public call_id number
---@field public call_svr string

---@return pb_Any2DsSyncSafeHousePlayerInfoReq
function pb.Any2DsSyncSafeHousePlayerInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnySyncSafeHousePlayerInfoRes = {
    result = 0,
    call_id = 0,
    room_id = 0,
    call_svr = "",
}
pb.__pb_Ds2AnySyncSafeHousePlayerInfoRes.__name = "Ds2AnySyncSafeHousePlayerInfoRes"
pb.__pb_Ds2AnySyncSafeHousePlayerInfoRes.__index = pb.__pb_Ds2AnySyncSafeHousePlayerInfoRes
pb.__pb_Ds2AnySyncSafeHousePlayerInfoRes.__pairs = __pb_pairs

pb.Ds2AnySyncSafeHousePlayerInfoRes = { __name = "Ds2AnySyncSafeHousePlayerInfoRes", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnySyncSafeHousePlayerInfoRes : ProtoBase
---@field public result number
---@field public call_id number
---@field public room_id number
---@field public call_svr string

---@return pb_Ds2AnySyncSafeHousePlayerInfoRes
function pb.Ds2AnySyncSafeHousePlayerInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Any2DsPlayerLeaveSafeHouseNtf = {
    room_id = 0,
    player_id = 0,
    reason = 0,
    dsa_id = "",
}
pb.__pb_Any2DsPlayerLeaveSafeHouseNtf.__name = "Any2DsPlayerLeaveSafeHouseNtf"
pb.__pb_Any2DsPlayerLeaveSafeHouseNtf.__index = pb.__pb_Any2DsPlayerLeaveSafeHouseNtf
pb.__pb_Any2DsPlayerLeaveSafeHouseNtf.__pairs = __pb_pairs

pb.Any2DsPlayerLeaveSafeHouseNtf = { __name = "Any2DsPlayerLeaveSafeHouseNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Any2DsPlayerLeaveSafeHouseNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public reason number
---@field public dsa_id string

---@return pb_Any2DsPlayerLeaveSafeHouseNtf
function pb.Any2DsPlayerLeaveSafeHouseNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyDeathAndHurtDetailInfoNtf = {
    room_id = 0,
    player_id = 0,
}
pb.__pb_Ds2AnyDeathAndHurtDetailInfoNtf.__name = "Ds2AnyDeathAndHurtDetailInfoNtf"
pb.__pb_Ds2AnyDeathAndHurtDetailInfoNtf.__index = pb.__pb_Ds2AnyDeathAndHurtDetailInfoNtf
pb.__pb_Ds2AnyDeathAndHurtDetailInfoNtf.__pairs = __pb_pairs

pb.Ds2AnyDeathAndHurtDetailInfoNtf = { __name = "Ds2AnyDeathAndHurtDetailInfoNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyDeathAndHurtDetailInfoNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public death_detail_info pb_DsDeathDetailedInfo
---@field public hurt_detail_info pb_DsHurtDetailedInfo[]

---@return pb_Ds2AnyDeathAndHurtDetailInfoNtf
function pb.Ds2AnyDeathAndHurtDetailInfoNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyRoomStatisticsNtf = {
    room_id = 0,
    player_id = 0,
    drop_counters_cnt = 0,
}
pb.__pb_Ds2AnyRoomStatisticsNtf.__name = "Ds2AnyRoomStatisticsNtf"
pb.__pb_Ds2AnyRoomStatisticsNtf.__index = pb.__pb_Ds2AnyRoomStatisticsNtf
pb.__pb_Ds2AnyRoomStatisticsNtf.__pairs = __pb_pairs

pb.Ds2AnyRoomStatisticsNtf = { __name = "Ds2AnyRoomStatisticsNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyRoomStatisticsNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public match_info pb_MatchModeInfo
---@field public sol_guide_info pb_SOLGuideInstructionInfo
---@field public drop_counters pb_MatchSOLDropCounter[]
---@field public drop_counters_cnt number
---@field public key_chain_usage_info pb_KeyChainUsageInfo[]

---@return pb_Ds2AnyRoomStatisticsNtf
function pb.Ds2AnyRoomStatisticsNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_KeyChainUsageInfo = {
    item_id = 0,
    use_durability = 0,
}
pb.__pb_KeyChainUsageInfo.__name = "KeyChainUsageInfo"
pb.__pb_KeyChainUsageInfo.__index = pb.__pb_KeyChainUsageInfo
pb.__pb_KeyChainUsageInfo.__pairs = __pb_pairs

pb.KeyChainUsageInfo = { __name = "KeyChainUsageInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_KeyChainUsageInfo : ProtoBase
---@field public item_id number
---@field public use_durability number

---@return pb_KeyChainUsageInfo
function pb.KeyChainUsageInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2RoomRecommendFriendNtf = {
    room_id = 0,
    player_id = 0,
    escape_time_point = 0,
    extraction_location = "",
}
pb.__pb_Ds2RoomRecommendFriendNtf.__name = "Ds2RoomRecommendFriendNtf"
pb.__pb_Ds2RoomRecommendFriendNtf.__index = pb.__pb_Ds2RoomRecommendFriendNtf
pb.__pb_Ds2RoomRecommendFriendNtf.__pairs = __pb_pairs

pb.Ds2RoomRecommendFriendNtf = { __name = "Ds2RoomRecommendFriendNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2RoomRecommendFriendNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public rescue_info pb_RescueInfo[]
---@field public help_teammate_carry_out_info pb_HelpTeammateCarryOutInfo[]
---@field public kill_assistant_info pb_KillAssistantInfo[]
---@field public drop_pickup_item_info pb_DropPickupItemInfo[]
---@field public escape_time_point number
---@field public extraction_location string

---@return pb_Ds2RoomRecommendFriendNtf
function pb.Ds2RoomRecommendFriendNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSeasonMatchSettlementAINtf = {
    player_id = 0,
    nickname = "",
    room_id = 0,
    team_id = 0,
    team_rank = 0,
    start_time = 0,
    plat_id = 0,
    account_type = 0,
}
pb.__pb_DsSeasonMatchSettlementAINtf.__name = "DsSeasonMatchSettlementAINtf"
pb.__pb_DsSeasonMatchSettlementAINtf.__index = pb.__pb_DsSeasonMatchSettlementAINtf
pb.__pb_DsSeasonMatchSettlementAINtf.__pairs = __pb_pairs

pb.DsSeasonMatchSettlementAINtf = { __name = "DsSeasonMatchSettlementAINtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSeasonMatchSettlementAINtf : ProtoBase
---@field public player_id number
---@field public nickname string
---@field public room_id number
---@field public team_id number
---@field public team_rank number
---@field public start_time number
---@field public team_members number[]
---@field public record pb_DsMatchBaseRecord
---@field public plat_id number
---@field public account_type number

---@return pb_DsSeasonMatchSettlementAINtf
function pb.DsSeasonMatchSettlementAINtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSMPLabAILogNtf = {
    room_id = 0,
    player_id = 0,
    game_duration = 0,
    kill_cnt = 0,
    kill_player_cnt = 0,
    kill_player_ai_cnt = 0,
    kill_ai_cnt = 0,
    dead_cnt = 0,
    mp_level = 0,
    rescue_cnt = 0,
    damage_dealt = 0,
    be_headshot_cnt = 0,
    assist_cnt = 0,
    breatheheal_times = 0,
    breatheheal_amount = 0,
    total_mileage = 0,
    headshot_cnt = 0,
    kill_cnt_nocarrier = 0,
    kill_cnt_withcarrier = 0,
    kill_player_nocarrier = 0,
    kill_player_withcarrier = 0,
    kill_ai_nocarrier = 0,
    kill_ai_withcarrier = 0,
    kill_player_ai_nocarrier = 0,
    kill_player_ai_withcarrier = 0,
    lives = 0,
    skill_use_cnt = "",
    skill_kill_player_cnt = "",
    near_kill_cnt = 0,
    medium_kill_cnt = 0,
    far_kill_cnt = 0,
    mp_break_count = 0,
    occupy = 0,
    mp_ammo_box_use_count = 0,
    mp_repari_count = 0,
    mvp = false,
    score_rank = 0,
    scores = "",
    is_win = false,
    game_mode = 0,
    map_id = 0,
    match_type = 0,
    team_num = 0,
    max_playernum = 0,
    campid = 0,
    hidden_score = 0,
    match_score = 0,
    rank_match_score = 0,
    match_use_time = 0,
    pre_team_friend_num = 0,
    team_player_num = 0,
    max_camp_real_player_num = 0,
    round_type = 0,
    early_quit_flag = 0,
    shot_cnt = 0,
    hit_target_cnt = 0,
    ai_level = "",
    total_player_real_num = 0,
    ailab_total_num = 0,
    total_human_team_num = 0,
    scheme_pool_id = 0,
    armed_force_id = 0,
}
pb.__pb_DSMPLabAILogNtf.__name = "DSMPLabAILogNtf"
pb.__pb_DSMPLabAILogNtf.__index = pb.__pb_DSMPLabAILogNtf
pb.__pb_DSMPLabAILogNtf.__pairs = __pb_pairs

pb.DSMPLabAILogNtf = { __name = "DSMPLabAILogNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSMPLabAILogNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public game_duration number
---@field public kill_cnt number
---@field public kill_player_cnt number
---@field public kill_player_ai_cnt number
---@field public kill_ai_cnt number
---@field public dead_cnt number
---@field public mp_level number
---@field public rescue_cnt number
---@field public damage_dealt number
---@field public be_headshot_cnt number
---@field public assist_cnt number
---@field public breatheheal_times number
---@field public breatheheal_amount number
---@field public total_mileage number
---@field public headshot_cnt number
---@field public kill_cnt_nocarrier number
---@field public kill_cnt_withcarrier number
---@field public kill_player_nocarrier number
---@field public kill_player_withcarrier number
---@field public kill_ai_nocarrier number
---@field public kill_ai_withcarrier number
---@field public kill_player_ai_nocarrier number
---@field public kill_player_ai_withcarrier number
---@field public lives number
---@field public skill_use_cnt string
---@field public skill_kill_player_cnt string
---@field public near_kill_cnt number
---@field public medium_kill_cnt number
---@field public far_kill_cnt number
---@field public mp_break_count number
---@field public occupy number
---@field public mp_ammo_box_use_count number
---@field public mp_repari_count number
---@field public mvp boolean
---@field public score_rank number
---@field public scores string
---@field public is_win boolean
---@field public game_mode number
---@field public map_id number
---@field public match_type number
---@field public team_num number
---@field public max_playernum number
---@field public campid number
---@field public hidden_score number
---@field public match_score number
---@field public rank_match_score number
---@field public match_use_time number
---@field public pre_team_friend_num number
---@field public team_player_num number
---@field public max_camp_real_player_num number
---@field public round_type number
---@field public early_quit_flag number
---@field public shot_cnt number
---@field public hit_target_cnt number
---@field public ai_level string
---@field public total_player_real_num number
---@field public ailab_total_num number
---@field public total_human_team_num number
---@field public scheme_pool_id number
---@field public armed_force_id number

---@return pb_DSMPLabAILogNtf
function pb.DSMPLabAILogNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2AnyTssReportNtf = {
    room_id = 0,
    player_id = 0,
}
pb.__pb_Ds2AnyTssReportNtf.__name = "Ds2AnyTssReportNtf"
pb.__pb_Ds2AnyTssReportNtf.__index = pb.__pb_Ds2AnyTssReportNtf
pb.__pb_Ds2AnyTssReportNtf.__pairs = __pb_pairs

pb.Ds2AnyTssReportNtf = { __name = "Ds2AnyTssReportNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2AnyTssReportNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public report_data pb_DsTssReportData

---@return pb_Ds2AnyTssReportNtf
function pb.Ds2AnyTssReportNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


