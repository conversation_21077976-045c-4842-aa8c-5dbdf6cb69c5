----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestSeasonFactContractDetail : LuaUIBaseView
local QuestSeasonFactContractDetail = ui("QuestSeasonFactContractDetail")
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local ECheckButtonState = import "ECheckButtonState"
local EUINavigation = import("EUINavigation")
-- END MODIFICATION

function QuestSeasonFactContractDetail:Ctor()
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)

    self._wtRewardScrollBox = self:Wnd("wRewardWrapBox", UIScrollBox)

    self._wtQuestDetailPanel = self:Wnd("WBP_TaskSystem_DetailPanel", UIWidgetBase)

    self._wtActionBtn = self:Wnd("wtCommonButtonV1S1_1", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnClickActionBtn, self)
    self._wtActionBtn:Event("OnDeClicked", self._OnClickActionBtn, self)

    self._wtAssemblyMapNeed = self:Wnd("WBP_AssemblyMapNeed", UIWidgetBase)
    self._fateQuestId = 0
    self._questInfo = nil
    self._timerHandle = nil
end

------------------------------------ Override function ------------------------------------
function QuestSeasonFactContractDetail:OnInitExtraData(fateQuestId, questInfo)
    if questInfo == nil then
        logerror(" QuestSeasonFactContractDetail OnInitExtraData : questInfo is nil ")
        return
    end
    self._fateQuestId = fateQuestId
    self._questInfo = questInfo
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Quest.Config.Loc.QuestDeatilView)
end

function QuestSeasonFactContractDetail:OnShow()
    self:_InitQuestDetailInfo()
    self:PlayAnimation(self.WBP_SeasonalTasks_ContractDetail_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    self:FreshBGImage()
end

function QuestSeasonFactContractDetail:OnOpen()
end

function QuestSeasonFactContractDetail:OnClose()
    self:_RemoveEventListener()
    Facade.UIManager:ClearSubUIByParent(self, self._wtRewardScrollBox)
end

function QuestSeasonFactContractDetail:OnShowBegin()
    self:_AddEventListener()
end

function QuestSeasonFactContractDetail:OnHide()
    self:_RemoveEventListener()
end

-- BEGIN MODIFICATION @ VIRTUOS : Navigation and input
function QuestSeasonFactContractDetail:OnHideBegin()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
end
-- END MODIFICATION

------------------------------------ Private function ------------------------------------
function QuestSeasonFactContractDetail:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateObjectiveState, self._UpdateDetailActionBtn, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._UpdateDetailActionBtn, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtQuestSubmitItemsSuccess, self._OnQuestSubmitItemsSuccess, self)

    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateSeasonLevel, self._UpdateDetailActionBtn, self)
end

function QuestSeasonFactContractDetail:_RemoveEventListener()
    self:RemoveAllLuaEvent()
end

--- 刷背景图
function QuestSeasonFactContractDetail:FreshBGImage()
    local faceCfg = Facade.TableManager:GetRowByKey("FateQuest", tostring(self._fateQuestId))
    if faceCfg then
        local bg = (faceCfg and faceCfg.FateQuestCover) and faceCfg.FateQuestCover or "PaperSprite'/Game/UI/UIAtlas/System/TaskSystem/BakedSprite/TaskSystem_Chapter_Bg_06.TaskSystem_Chapter_Bg_06'"
        Facade.UIManager:ManuelRefreshBackgroundUI(self.UINavID, bg)
    else
        logerror("QuestSeasonFactContractDetail:FreshBGImage()")
    end
end

--刷新选择的任务相关内容 右侧
function QuestSeasonFactContractDetail:_InitQuestDetailInfo()
    if self._questInfo then
        Module.ItemDetail:CloseAllPopUI()
        self._wtQuestDetailPanel:UpdateInfo(self._questInfo, true)
        self:_UpdateDetailActionBtn()

        Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardScrollBox)

        self:_UpdateRewardList()
    end
end

function QuestSeasonFactContractDetail:_OnQuestStateUpdate(questId)
    if self._questInfo then
        if questId == self._questInfo.id then
            self:_UpdateDetailActionBtn()
            if self._questInfo.state >= QuestState.Rewarded then
                if self._wtRewardScrollBox:GetChildrenCount() > 0 then
                    local allRewardItems = self._wtRewardScrollBox:GetAllChildren()
                    for _, itemView in ipairs(allRewardItems) do
                        itemView:SetAlreadyGetState()
                    end
                end
            end
        end
    end
end

function QuestSeasonFactContractDetail:_UpdateDetailActionBtn()
    if self._questInfo == nil then
        return
    end

    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end

    self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Collapsed)
    if self._questInfo.state <= QuestState.Unaccepted then
        self._questInfo.state = QuestState.Unaccepted
        if Server.QuestServer:IsQuestAcceptable(self._questInfo) then
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.accept)
            if Server.QuestServer._questFactData:IsHasFactUnderWay() then
                self._wtActionBtn:SetIsEnabledStyle(false)
                self._wtAssemblyMapNeed:SetType(0)
                self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
                self._wtAssemblyMapNeed:SetMainTitle(Module.Quest.Config.Loc.QuestSeasonContractAcceptedTip)
            else
                self._wtActionBtn:SetIsEnabledStyle(true)
            end
        else
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.NotUnLock)
            self._wtActionBtn:SetIsEnabledStyle(false)
            local time = Server.QuestServer._questFactData:GetRemainToAcceptTime(self._fateQuestId)
            if time > 0 then
                self:_UpdateRemainCountTime()

                self._timerHandle = Timer:NewIns(5, 0)
                self._timerHandle:AddListener(self._UpdateRemainCountTime, self)
                self._timerHandle:Start()
            end
        end
    elseif self._questInfo.state == QuestState.Accepted then
        -- 没有道具目标或提交已完成
        if self._questInfo:IsFinishSubmitObjectives() then
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.underWay)
            self._wtActionBtn:SetIsEnabledStyle(false)
        else
            local itemStructList, submitDatas = QuestLogic.Get3ItemSubmitInfos(self._questInfo)
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.submit)
            self._wtActionBtn:SetIsEnabledStyle(true)
        end
    elseif self._questInfo.state == QuestState.Completed then
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.receive)
        self._wtActionBtn:SetIsEnabledStyle(true)
    elseif self._questInfo.state == QuestState.Rewarded then
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.objectiveFinish)
        self._wtActionBtn:SetIsEnabledStyle(false)
    end
end

function QuestSeasonFactContractDetail:_OnClickActionBtn()
    if self._questInfo == nil then
        return
    end
    if self._questInfo.state == QuestState.Unaccepted then
        if Server.QuestServer._questFactData:IsHasFactUnderWay() then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.QuestSeasonContractAcceptedTip)
            return
        end
        QuestLogic.DoAcceptQuest(self._questInfo)
    elseif self._questInfo.state == QuestState.Completed then
        QuestLogic.DoGetQuestRewards(self._questInfo) --前往领取奖励物品
    elseif self._questInfo.state == QuestState.Failed then
        -- 提交道具
        QuestLogic.DoAcceptQuest(self._questInfo)
    elseif self._questInfo.state == QuestState.Accepted and self._questInfo:IsFinishSubmitObjectives() == false then
        local itemStructList, submitDatas, zeroStoredItemList = QuestLogic.Get3ItemSubmitInfos(self._questInfo)
        if table.isempty(itemStructList) then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.noItemsToSubmit) -- 暂时没有可提交的物品
        else
            -- 点击提交物品记录当前打开任务详情页，返回任务界面时候打开
            -- Module.Quest.Field:SetCurQuestDetailInfo(self._questInfo)
            Module.Quest.Field:SetSubmitQuestId(self._questInfo.id)
            Module.Inventory:OpenItemSubmitPanel(
                itemStructList,
                submitDatas,
                zeroStoredItemList,
                QuestLogic.ProcessItemSubmit
            )
        end
    end
end

function QuestSeasonFactContractDetail:_OnQuestSubmitItemsSuccess(questId)
    if questId == self._questInfo.id then
        -- 提交物品成功时刷新提交界面
        local submitPanelHandle = Module.Inventory:GetItemSubmitPanel()
        if submitPanelHandle then
            submitPanelHandle:GetUIIns():ItemSubmitedSuccessUpdate(QuestLogic.Get3ItemSubmitInfos(self._questInfo))
        end
    end
end

function QuestSeasonFactContractDetail:_UpdateRewardList()
    local allRewardList = self._questInfo:GetRewardList()
    local newList = {}
    for index, value in ipairs(allRewardList) do
        table.insert(newList, value)
    end

    if #newList > 0 then
        for _, rewardItem in ipairs(newList) do
            local weakUIIns, instanceId =
                Module.CommonWidget:CreateIVCommonItemTemplateBySize(self, self._wtRewardScrollBox, nil, 168, 168)
            local rewardItemView = getfromweak(weakUIIns)
            rewardItemView:InitItem(rewardItem)
            rewardItemView:SetItemDetailFromUIID(LogAnalysisTool.EItemDetailFromUIType.TaskQuest)
            if self._questInfo.state >= QuestState.Rewarded then
                rewardItemView:SetAlreadyGetState()
                rewardItemView:EnableComponent(EComp.GetMask, true)
            else
                rewardItemView:EnableComponent(EComp.GetMask, false)
            end
            rewardItemView:ShowBindingComp(rewardItem.bindType ~= PropBindingType.BindingNotBind)
            rewardItemView.Slot:SetPadding(FMargin(0, 0, 20, 0))
        end
    end
end

function QuestSeasonFactContractDetail:OnNavBack()
    return true
end

function QuestSeasonFactContractDetail:_UpdateRemainCountTime()
    local day, hour, min, sec =
        TimeUtil.GetSecondsFormatDDHHMMSS(Server.QuestServer._questFactData:GetRemainToAcceptTime(self._fateQuestId))
    local desc = ""
    if day > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif hour > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif min > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min) ..
            Module.Quest.Config.Loc.CanAccept
    else
        desc = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1) .. Module.Quest.Config.Loc.CanAccept
        if sec <= 0 then
            if self._timerHandle then
                self._timerHandle:Release()
                self._timerHandle = nil
            end
            self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Collapsed)
            return
        end
    end

    self._wtAssemblyMapNeed:SetType(0)
    self._wtAssemblyMapNeed:SetMainTitle(desc)
    self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Visible)
end

return QuestSeasonFactContractDetail
