require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require("DFM.Business.Proto.Pb.errcode_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cscpp_online_editor_pb"
end

pb.__pb_CSOnlineHeartbeatReq = {
    padding = 0,
}
pb.__pb_CSOnlineHeartbeatReq.__name = "CSOnlineHeartbeatReq"
pb.__pb_CSOnlineHeartbeatReq.__index = pb.__pb_CSOnlineHeartbeatReq
pb.__pb_CSOnlineHeartbeatReq.__pairs = __pb_pairs

pb.CSOnlineHeartbeatReq = { __name = "CSOnlineHeartbeatReq", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSOnlineHeartbeatReq : ProtoBase
---@field public padding number

---@return pb_CSOnlineHeartbeatReq
function pb.CSOnlineHeartbeatReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSOnlineHeartbeatRes = {
    padding = 0,
    tick_count = 0,
}
pb.__pb_CSOnlineHeartbeatRes.__name = "CSOnlineHeartbeatRes"
pb.__pb_CSOnlineHeartbeatRes.__index = pb.__pb_CSOnlineHeartbeatRes
pb.__pb_CSOnlineHeartbeatRes.__pairs = __pb_pairs

pb.CSOnlineHeartbeatRes = { __name = "CSOnlineHeartbeatRes", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSOnlineHeartbeatRes : ProtoBase
---@field public padding number
---@field public tick_count number

---@return pb_CSOnlineHeartbeatRes
function pb.CSOnlineHeartbeatRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSStateGetInfoReq = {
}
pb.__pb_CSStateGetInfoReq.__name = "CSStateGetInfoReq"
pb.__pb_CSStateGetInfoReq.__index = pb.__pb_CSStateGetInfoReq
pb.__pb_CSStateGetInfoReq.__pairs = __pb_pairs

pb.CSStateGetInfoReq = { __name = "CSStateGetInfoReq", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSStateGetInfoReq : ProtoBase

---@return pb_CSStateGetInfoReq
function pb.CSStateGetInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSStateGetInfoRes = {
    PlayerID = 0,
    State = 0,
    TeamID = 0,
    RoomID = 0,
    result = 0,
    DsRoomID = 0,
    PlatID = 0,
}
pb.__pb_CSStateGetInfoRes.__name = "CSStateGetInfoRes"
pb.__pb_CSStateGetInfoRes.__index = pb.__pb_CSStateGetInfoRes
pb.__pb_CSStateGetInfoRes.__pairs = __pb_pairs

pb.CSStateGetInfoRes = { __name = "CSStateGetInfoRes", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSStateGetInfoRes : ProtoBase
---@field public PlayerID number
---@field public State number
---@field public TeamID number
---@field public RoomID number
---@field public result number
---@field public mode pb_MatchModeInfo
---@field public DsRoomID number
---@field public PlatID number

---@return pb_CSStateGetInfoRes
function pb.CSStateGetInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSStateBatchGetInfoReq = {
}
pb.__pb_CSStateBatchGetInfoReq.__name = "CSStateBatchGetInfoReq"
pb.__pb_CSStateBatchGetInfoReq.__index = pb.__pb_CSStateBatchGetInfoReq
pb.__pb_CSStateBatchGetInfoReq.__pairs = __pb_pairs

pb.CSStateBatchGetInfoReq = { __name = "CSStateBatchGetInfoReq", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSStateBatchGetInfoReq : ProtoBase
---@field public player_id number[]

---@return pb_CSStateBatchGetInfoReq
function pb.CSStateBatchGetInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSStateBatchGetInfoRes = {
    result = 0,
}
pb.__pb_CSStateBatchGetInfoRes.__name = "CSStateBatchGetInfoRes"
pb.__pb_CSStateBatchGetInfoRes.__index = pb.__pb_CSStateBatchGetInfoRes
pb.__pb_CSStateBatchGetInfoRes.__pairs = __pb_pairs

pb.CSStateBatchGetInfoRes = { __name = "CSStateBatchGetInfoRes", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSStateBatchGetInfoRes : ProtoBase
---@field public result number
---@field public player_info pb_PlayerStateSimpleInfo[]

---@return pb_CSStateBatchGetInfoRes
function pb.CSStateBatchGetInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerStateSimpleInfo = {
    player_id = 0,
    state = 0,
    fighting_time = 0,
    member_num = 0,
    team_id = 0,
    PlatID = 0,
    invisible = false,
}
pb.__pb_PlayerStateSimpleInfo.__name = "PlayerStateSimpleInfo"
pb.__pb_PlayerStateSimpleInfo.__index = pb.__pb_PlayerStateSimpleInfo
pb.__pb_PlayerStateSimpleInfo.__pairs = __pb_pairs

pb.PlayerStateSimpleInfo = { __name = "PlayerStateSimpleInfo", __service="online", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerStateSimpleInfo : ProtoBase
---@field public player_id number
---@field public state number
---@field public fighting_time number
---@field public member_num number
---@field public team_id number
---@field public mode_info pb_MatchModeInfo
---@field public mode_info_array pb_MatchModeInfo[]
---@field public PlatID number
---@field public invisible boolean

---@return pb_PlayerStateSimpleInfo
function pb.PlayerStateSimpleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


