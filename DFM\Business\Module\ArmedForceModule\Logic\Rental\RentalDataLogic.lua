local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemConfigTool     = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemMatchConditionTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemMatchConditionTool"
local UAmmoDataManager = import "AmmoDataManager"
local ammoMgr = UAmmoDataManager.Get()
----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------




local RentalDataLogic = {}

RentalDataLogic.EquipmentTypeList = {
    ESlotType.Helmet,
    ESlotType.BreastPlate,
    ESlotType.ChestHanging,
    ESlotType.Bag,
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    -- ESlotType.MeleeWeapon, -- 可能读仓库的
    ESlotType.Pistrol,
}

RentalDataLogic.ContainerTypeList = {
    ESlotType.Pocket,
    ESlotType.ChestHangingContainer,
    ESlotType.BagContainer
}

RentalDataLogic.WeaponTypeList = {
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.Pistrol,
}
--#region ==============================================请求所有租借数据========================================================
RentalDataLogic.ReqRentalDatas = function(consumableId, bTimeIsUp, fCallback)
    if consumableId then
        Server.ArmedForceServer:ReqArmedForceGetRental(consumableId, bTimeIsUp, fCallback)
    end
end
--#endregion ==============================================请求所有租借数据========================================================

--#region ==============================================取消租借========================================================
RentalDataLogic.CancelRental = function(fCallback)
    local bIsReady2Go, readyState = Server.MatchServer:GetIsReadytoGo()
    if bIsReady2Go then
        if readyState == 1 then
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InMatching)
        else
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InReadying)
        end
        return
    end
    local fConfirmCallback = function()
        Server.ArmedForceServer:ReqArmedForceCancelEquipmentRental(fCallback)
    end
    local curRentalPlan_ConsumableID = Server.ArmedForceServer:GetCurRentalPlan_ConsumableID()
    local name1 = ItemConfigTool.GetItemName(curRentalPlan_ConsumableID)
    local text = StringUtil.Key2StrFormat(Module.ArmedForce.Config.Loc.RentalVocherExpirationCancelConfirmation,{["Name1"] = name1})

    RentalDataLogic.CheckUsedRentalPlanExpirationConfirmation(fConfirmCallback, text)

end
--#endregion ==============================================取消租借========================================================

--#region ==============================================应用租借========================================================
RentalDataLogic.ApplyRental = function(rentalData, fCallback)
    local bIsReady2Go, readyState = Server.MatchServer:GetIsReadytoGo()
    if bIsReady2Go then
        if readyState == 1 then
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InMatching)
        else
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InReadying)
        end
        Module.ArmedForce.Field:SetApplyRentalProcessing(false)
        return
    end
    Server.ArmedForceServer:ReqArmedForceEquipmentRental(rentalData, fCallback)
end
--#endregion ==============================================应用租借========================================================

--#region ==============================================刷新租借方案========================================================
RentalDataLogic.ReqRefreshRental = function(inRentalData, fCallback)
    local bIsReady2Go, readyState = Server.MatchServer:GetIsReadytoGo()
    if bIsReady2Go then
        if readyState == 1 then
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InMatching)
        else
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InReadying)
        end
        return
    end
    if inRentalData then
        Server.ArmedForceServer:ReqArmedForceRefreshRental(inRentalData, fCallback)
    else
        logerror("RentalDataLogic.ReqRefreshRental inRentalData is nil!!!")
    end
end
--#endregion ==============================================刷新租借方案========================================================

--#region ==============================================根据配置生成SlotGroup数据========================================================
local EquipTimerHandle = {
    [ESlotGroup.Rental_1] = nil,
    [ESlotGroup.Rental_2] = nil,
    [ESlotGroup.Rental_3] = nil,
    [ESlotGroup.MainRental] = nil,
}
local ContainerTimerHandle = {
    [ESlotGroup.Rental_1] = {},
    [ESlotGroup.Rental_2] = {},
    [ESlotGroup.Rental_3] = {},
    [ESlotGroup.MainRental] = {},
}
RentalDataLogic.GenSlotGroupData = function(slotGroupId, rentalData, fRefreshEquipSlotView, fRefreshContainerView, bImmediately, bOverridedSkin)
    bImmediately = setdefault(bImmediately, false)
    logwarning(string.format("[RentalDataLogic] GenSlotGroupData ========================> slotGroupId = %s, typeID = %s, presetID = %s, bImmediately = %s", slotGroupId or "nil", rentalData.type_id or "nil", rentalData.preset_id or "nil", bImmediately))
    if not slotGroupId or not rentalData then
        logerror("[RentalDataLogic] GenSlotGroupData slotGroupId or rentalData is nil !!! ")
    end
    local function fContainerProcess(slotType)
        local rentalSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)
        -- 先重置一下数据
        rentalSlot:ResetSlot()

        -- 初始化容器空间
        local containerType = rentalSlot:GetContainerType()
        if slotType == ESlotType.Pocket then
            rentalSlot:InitPocketContainer()
        else
            local rentalContainerEquipSlot = Server.InventoryServer:GetSlot(containerType, slotGroupId)
            if rentalContainerEquipSlot then
                local equipItem = rentalContainerEquipSlot:GetEquipItem()
                if equipItem then
                    local equipId = equipItem.id
                    rentalSlot:InitContainerByEquipmentId(equipId)
                end
            end
        end

        local itemList = rentalData.itemDataList[slotType]
        RentalDataLogic.GenContainerItems(slotGroupId, slotType, itemList, fRefreshContainerView)
    end

    local function fEquipmentProcess()
        -- 优先推荐装备
        for _, slotType in ipairs(RentalDataLogic.EquipmentTypeList) do
            local rentalSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)
            -- 先重置一下数据
            rentalSlot:ResetSlot()
            local itemList = rentalData.itemDataList[slotType]
            RentalDataLogic.GenEquipmentItem(slotGroupId, slotType, itemList, fRefreshEquipSlotView, bOverridedSkin)
        end
        -- 推荐容器道具
        for index, slotType in ipairs(RentalDataLogic.ContainerTypeList) do
            if bImmediately then
                fContainerProcess(slotType)
            else
                ContainerTimerHandle[slotGroupId][slotType] = Timer.DelayCall(index * 0.02, fContainerProcess, nil, slotType)
            end
        end
    end

    -- 取消定时器，否则会生成上一次的装备
    if EquipTimerHandle[slotGroupId] then
        Timer.CancelDelay(EquipTimerHandle[slotGroupId])
        EquipTimerHandle[slotGroupId] = nil
    end

    for index, slotType in ipairs(RentalDataLogic.ContainerTypeList) do
        if ContainerTimerHandle[slotGroupId][slotType] then
            Timer.CancelDelay(ContainerTimerHandle[slotGroupId][slotType])
            ContainerTimerHandle[slotGroupId][slotType] = nil
        end
    end


    if bImmediately then
        fEquipmentProcess()
    else
        EquipTimerHandle[slotGroupId] = Timer.DelayCall(0, fEquipmentProcess)
    end
end

RentalDataLogic.GenEquipmentItem = function(slotGroupId, slotType, itemList, fRefreshEquipSlotView, bOverridedSkin)
    local rentalSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)
    if itemList and not table.isempty(itemList) then
        for _, itemData in ipairs(itemList) do
            local id = itemData.id
            local num = itemData.num
            local pos = itemData.position
            local rentalEquipItem
            if ItemHelperTool.IsMainWeaponSlotType(pos) or pos == ESlotType.Pistrol then
                local propInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(id)
                if bOverridedSkin then
                    local bOverrided = WeaponAssemblyTool.OverrideDefaultAppearanceInfo(propInfo)
                end
                -- local bOverrided = WeaponAssemblyTool.OverrideDefaultAppearanceInfo(propInfo)
                -- if bOverrided and propInfo then
                    rentalEquipItem = ItemBase:New(propInfo.id, num)
                    rentalEquipItem:SetRawPropInfo(propInfo)
                -- end
            else
                rentalEquipItem = ItemBase:New(id, num)
            end
            loginfo(string.format("[RentalDataLogic] GenEquipmentItem 推荐的装备 ==> id = %s, num = %s, pos = %s, name = %s", id, num, pos, rentalEquipItem and rentalEquipItem.name))
            if rentalEquipItem then
                if ItemMatchConditionTool.CheckItemMatchSOLCondition(rentalEquipItem, rentalSlot) then
                    rentalEquipItem:AddToSlot(rentalSlot)
                    Server.InventoryServer:AddItemToList(rentalEquipItem, rentalSlot)
                else
                    logerror(string.format("[RentalDataLogic] GenEquipmentItem 推荐的装备不符合槽位 ==> id = %s, num = %s, pos = %s, name = %s", id, num, pos, rentalEquipItem and rentalEquipItem.name))
                end
            else
                logerror(string.format("[RentalDataLogic] GenEquipmentItem 推荐的装备没item生成 ==> id = %s, num = %s, pos = %s", id, num, pos))
            end
        end
    end
    if fRefreshEquipSlotView then
        fRefreshEquipSlotView()
    end
end

RentalDataLogic.GenContainerItems = function(slotGroupId, slotType, itemList, fRefreshContainerView)
    local rentalSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)
    if itemList and not table.isempty(itemList) then
        for _, itemData in ipairs(itemList) do
            local id = itemData.id
            local needNum = itemData.num
            local pos = itemData.position
            local itemMainType = ItemHelperTool.GetMainTypeById(id)
            -- 如果是子弹需要优先处理武器弹夹内的
            if itemMainType == EItemType.Bullet then
                for _, weaponSlotType in ipairs(RentalDataLogic.WeaponTypeList) do
                    local weaponNeedNum, weaponItem = RentalDataLogic._CheckBulletToWeaponFilled(id, weaponSlotType, slotGroupId)
                    if weaponNeedNum > 0 and weaponItem then
                        local operateNum = math.min(weaponNeedNum, needNum)
                        if operateNum > 0 then
                            local bulletPropInfo = pb.PropInfo:New()
                            bulletPropInfo.id = id
                            bulletPropInfo.gid = GetGid()
                            bulletPropInfo.num = operateNum
                            local load_bullets = weaponItem:GetRawPropInfo().weapon.load_bullets
                            table.insert(load_bullets, bulletPropInfo)
                            local desc = WeaponAssemblyTool.PropInfo_To_Desc(weaponItem:GetRawPropInfo())
                            weaponItem:SetRawDescObj(desc)
                            Module.ArmedForce.Config.evtOnRentalWeaponBulletChanged:Invoke(weaponItem)
                            needNum = needNum - operateNum
                        end
                    end
                end
            end
            -- 再处理容器空间下的
            needNum = RentalDataLogic.TryIncreaseToSlot(id, needNum, pos, slotGroupId)
            local itemName = ItemConfigTool.GetItemName(id)
            loginfo(string.format("[RentalDataLogic] GenContainerItems 推荐的容器道具 ==> id = %s, num = %s, pos = %s, name = %s, neednum = %s", id, itemData.num, pos, itemName, needNum))
        end
    end
    if fRefreshContainerView then
        fRefreshContainerView(slotType)
    end
end

RentalDataLogic._CheckBulletToWeaponFilled = function (bulletId, weaponSlotType, slotGroupId)
    local needNum = 0
    local slot = Server.InventoryServer:GetSlot(weaponSlotType, slotGroupId)
	local weaponItem = slot:GetEquipItem()
	if weaponItem and ammoMgr:IsMatchWeapon(weaponItem.id, bulletId)then
		-- todo 获得子弹容量
		local currentBulletNum, capacity = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(weaponItem:GetRawPropInfo())
		needNum = capacity - currentBulletNum
	end
    return needNum, weaponItem
end

-- 在preset容器中放临时道具
RentalDataLogic.TryIncreaseToSlot = function (itemId, needNum, pos, slotGroupId)
    -- -- 尝试位置
    -- local function fTryIncreaseToSlot(slotType)
        if needNum > 0 then
            needNum = RentalDataLogic._TryIncreaseToSlotProcess(itemId, needNum, pos, slotGroupId)
        end
    -- end
    -- for _, slotType in ipairs(RentalDataLogic.ContainerTypeList) do
    --     fTryIncreaseToSlot(pos)
    -- end

    return needNum
end

-- 从临时的容器槽中找位置
RentalDataLogic._TryIncreaseToSlotProcess = function (itemId, needNum, slotType, slotGroupId)
    local maxStackCount = ItemConfigTool.GetMaxStacksNumById(itemId)
    local rentalSlot = Server.InventoryServer:GetSlot(slotType, slotGroupId)
    while needNum > 0 do
        local operateNum = math.min(needNum, maxStackCount)
        if operateNum == 0 then
            break
        end
        local operateItem = ItemBase:NewIns(itemId)
        local targetLoc = rentalSlot:TryFindLocationForItem(operateItem)
        if targetLoc then --如果能添加的话
            local prop = pb.PropInfo:New()
            prop.id = itemId
            prop.gid = GetGid()
            prop.num = operateNum
            operateItem:SetRawPropInfo(prop)
            operateItem:FreshRawPropInfoByLoc(targetLoc)
            operateItem:AddToSlot(rentalSlot)
            Server.InventoryServer:AddItemToList(operateItem, rentalSlot)
            rentalSlot:SetItemPosFromLoc(operateItem, targetLoc)
            needNum = needNum - operateNum
        else
            logerror("找不到位置", itemId, operateNum, slotType)
            break
        end
    end
    return needNum
end

--#endregion ==============================================刷新租借方案========================================================


function RentalDataLogic.OnCSWAssemblyApplySkinRes(res)
    if Server.ArmedForceServer:CheckIsRentalStatus() then
        local groupId =  Server.ArmedForceServer:GetCurSlotGroupId()
        for _, slotType in ipairs(RentalDataLogic.WeaponTypeList) do
            local slot = Server.InventoryServer:GetSlot(slotType, groupId)
            if slot then
                local equipItem = slot:GetEquipItem();
                if equipItem then
                    local propInfo = equipItem:GetRawPropInfo()
                    if propInfo then
                        local bOverrided = WeaponAssemblyTool.OverrideDefaultAppearanceInfo(propInfo)
                        if bOverrided and propInfo then
                            equipItem:SetRawPropInfo(propInfo)
                        end
                    end
                end
            end
        end
    end
end

function RentalDataLogic.RentalApplyProcess(rentalData)
    -- local i = typeId - (math.ceil(typeId / 3) - 1) * 3
    -- local slotGroupID = ESlotGroup.MainRental + i
    -- local allItemSlots = Server.InventoryServer:GetAllSlots(slotGroupID)

    -- if true then
    --     RentalDataLogic.RentalApplyProcess_Equip(slotGroupID)
    --     RentalDataLogic.RentalApplyProcess_Container(slotGroupID)
    --     Server.InventoryServer:CalculateAllCarryItemsWeight(ESlotGroup.MainRental)
    -- else
        if Server.ArmedForceServer:CheckIsRentalStatus() then
            -- local rentalData = Server.ArmedForceServer:GetCurRentalPlan()
            -- if rentalData then
            --     local planKey = string.format("%s_%s", tostring(rentalData.type_id), tostring(rentalData.preset_id))
            --     local rentalData = Module.ArmedForce.Field:GetRentalDataTableByKey(planKey)
                if rentalData then
                    RentalDataLogic.GenSlotGroupData(ESlotGroup.MainRental, rentalData, nil, nil, true, true)
                    Server.InventoryServer:CalculateAllCarryItemsWeight(ESlotGroup.MainRental)
                end
            -- end
        end

    -- end
    Server.ArmedForceServer.Events.evtRentalApplySuccess:Invoke(rentalData)
end

function RentalDataLogic.CheckAndGenerateRentalData()
    if Server.ArmedForceServer:CheckIsRentalStatus() then
        local mainWeaponSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.MainRental)
        if not mainWeaponSlot or (mainWeaponSlot and not mainWeaponSlot:GetEquipItem()) then
            local curRentalPlan = Server.ArmedForceServer:GetCurRentalPlan()
            RentalDataLogic.RentalApplyProcess(curRentalPlan)
        end
    end
end

-- function RentalDataLogic.RentalApplyProcess_Equip(slotGroupID)
--     local equipmentTypeList = {
--         ESlotType.Helmet,
--         ESlotType.BreastPlate,
--         ESlotType.ChestHanging,
--         ESlotType.Bag,
--         ESlotType.MainWeaponLeft,
--         ESlotType.MainWeaponRight,
--         ESlotType.Pistrol,
--     }
--     -- 优先推荐装备
--     for _, slotType in ipairs(equipmentTypeList) do
--         local mainRentalSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.MainRental)
--         -- 先重置一下数据
--         mainRentalSlot:ResetSlot()
--         local slot = Server.InventoryServer:GetSlot(slotType, slotGroupID)
--         local items = slot:GetItems()
--         if not table.isempty(items) then
--             for _, item in ipairs(items) do
--                 -- 如果是武器，需要设置默认皮肤和挂饰
--                 if ItemHelperTool.IsMainWeaponSlotType(slotType) or slotType == ESlotType.Pistrol then
--                     local propInfo = item:GetRawPropInfo()
--                     if propInfo then
--                         local bOverrided = WeaponAssemblyTool.OverrideDefaultAppearanceInfo(propInfo)
--                         if bOverrided and propInfo then
--                             item:SetRawPropInfo(propInfo)
--                         end
--                     end
--                 end

--                 -- 直接将数据添加到槽位上，不再做假数据
--                 item:AddToSlot(mainRentalSlot)
--                 Server.InventoryServer:AddItemToList(item, mainRentalSlot)
--             end
--         end
--     end
-- end

-- function RentalDataLogic.RentalApplyProcess_Container(slotGroupID)
--     local containerTypeList = {
--         ESlotType.ChestHangingContainer,
--         ESlotType.Pocket,
--         ESlotType.BagContainer
--     }
--     -- 优先推荐装备
--     for _, slotType in ipairs(containerTypeList) do
--         local mainRentalSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.MainRental)
--         -- 先重置一下数据
--         mainRentalSlot:ResetSlot()
--         -- 初始化容器空间
--         local containerType = mainRentalSlot:GetContainerType()
--         if slotType == ESlotType.Pocket then
--             mainRentalSlot:InitPocketContainer()
--         else
--             local rentalContainerEquipSlot = Server.InventoryServer:GetSlot(containerType, ESlotGroup.MainRental)
--             if rentalContainerEquipSlot then
--                 local equipItem = rentalContainerEquipSlot:GetEquipItem()
--                 if equipItem then
--                     local equipId = equipItem.id
--                     mainRentalSlot:InitContainerByEquipmentId(equipId)
--                 end
--             end
--         end

--         local slot = Server.InventoryServer:GetSlot(slotType, slotGroupID)
--         local items = slot:GetItems()
--         if not table.isempty(items) then
--             for _, item in ipairs(items) do
--                 local newLoc = item.InSlot:GetItemLocation(item)
--                 item:AddToSlot(mainRentalSlot)
--                 Server.InventoryServer:AddItemToList(item, mainRentalSlot)
--                 local pbLocation = newLoc:ToPbLocation()
--                 local targetLoc = mainRentalSlot:SetItemPosFromPb(item, pbLocation)
--                 item:FreshRawPropInfoByLoc(targetLoc)
--             end
--         end
--     end
-- end

-- 配装回流支持 --- 被重构过了，typeId要要映射出consumableid和对应的typeid
function RentalDataLogic.ReflowSupport(typeId)
    local index = math.ceil(typeId / 3)
    local type_id = (typeId - 1) % 3 + 1
    local consumableId = Module.ArmedForce.Config.RentalConsumableID[index] or nil
    logerror(string.format("[RentalDataLogic.ReflowSupport] 回流 传入的typeid = %s, 解析后的页签 = %s, 对应方案的下标 = %s, consumableId = %s", typeId, index, type_id, consumableId))
    if consumableId then
        local fCallback = function(res)
            if res.result == 0 then
                local fShowMainPanel = function ()
                    if IsHD() then
                        Module.CommonBar:ChangeStackUITab(UIName2ID.AssemblyHDMainPanel,function()
                            Module.ArmedForce:ShowMainPanel(Module.ArmedForce.Config.EEnterFrom.PrepareFlow)
                        end)
                    else
                        Module.ArmedForce:ShowMainPanel(Module.ArmedForce.Config.EEnterFrom.PrepareFlow)
                    end
                end
            
                local rentalData = Server.ArmedForceServer:GetCurRentalInfo_RentalData(consumableId, type_id)
                if rentalData then
                    local consumableID = rentalData.consumable_id
                    local rentalVoucherNum = Server.CollectionServer:GetCollectionItemsNumById(consumableID)
                    if rentalVoucherNum > 0 then
                        RentalDataLogic.ApplyRental(rentalData, fShowMainPanel)
                    else
                        fShowMainPanel()
                    end
                else
                    fShowMainPanel()
                end
            end
        end
        Server.ArmedForceServer:ReqArmedForceGetRental(consumableId, false, fCallback)
    end
end


-- 根据func获得藏品物资券
function RentalDataLogic.GetRentalVoucherCollectionItemsByFunction(func)
    local rentalVoucherCollectionItems = {}
    local collectionItems = Server.CollectionServer:GetCollectionItemsByMainTypeOrSubType(EItemType.CollectionProp, ECollectableType.RentalVoucher)
    table.walk(collectionItems,
        function (v, k)
            if func(v, k) then
                table.insert(rentalVoucherCollectionItems, v)
            end
    end)
    return rentalVoucherCollectionItems
end

-- 根据func获得藏品物资券
function RentalDataLogic.CheckConsumableIDCanBeApply(consumableId)
    local commonEquipmentPreset, uniqueEquipmentPreset = Server.GameModeServer:GetModeEquipTicket()
    local bCanBeApply = table.contains(commonEquipmentPreset, consumableId) or table.contains(uniqueEquipmentPreset, consumableId)
    return bCanBeApply
end

-- 检查当前使用的物资券过期确认提醒
function RentalDataLogic.CheckUsedRentalPlanExpirationConfirmation(fConfirmCallback, text)
    local curRentalPlan_ConsumableID = Server.ArmedForceServer:GetCurRentalPlan_ConsumableID()
    if curRentalPlan_ConsumableID > 0 then
        local propExpireInfo = Server.CollectionServer:GetPropExpireInfo(curRentalPlan_ConsumableID)
        if propExpireInfo then
            local remainTime = TimeUtil.GetLocalRemainTime2Seconds(propExpireInfo[1].expireTime)
            if remainTime == 0 then -- 当前使用的物资券已经过期需要弹二次弹窗确认
                Module.CommonTips:ShowConfirmWindow(
                    text,
                    fConfirmCallback,
                    nil)
                return
            end
        end
    end
    fConfirmCallback()
end

function RentalDataLogic.CheckCommonConsumableId(consumableId)
    return table.contains(Module.ArmedForce.Config.RentalConsumableID, consumableId)
end
return RentalDataLogic