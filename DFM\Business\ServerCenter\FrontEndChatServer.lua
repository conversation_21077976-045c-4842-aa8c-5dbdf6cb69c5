----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSFrontEndChat)
----- LOG FUNCTION AUTO GENERATE END -----------



local FrontEndChatServer = class("FrontEndChatServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local PrivateSummyStruct = require "DFM.Business.DataStruct.ChatStruct.PrivateSummyStruct"
local PrivateChatStruct = require "DFM.Business.DataStruct.ChatStruct.PrivateChatStruct"
local PrivatePlayerStruct = require "DFM.Business.DataStruct.ChatStruct.PrivatePlayerStruct"
local GroupCacheStruct = require "DFM.Business.DataStruct.ChatStruct.GroupCacheStruct"
local CommonChatCacheStruct = require "DFM.Business.DataStruct.ChatStruct.CommonChatCacheStruct"
local EFilterType = {
    BlackList = 0,
}

-- BEGIN MODIFICATION
local UClientComminicateSetting = import "ClientComminicateSetting"
local ClientComminicateSetting = UClientComminicateSetting.Get(GetWorld())
-- 平台通信权限检测相关引用
local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetWorld())
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
local UDFMOnlineIdentityProxy_CheckUsersCommunicationPermissions = import "DFMOnlineIdentityProxy_CheckUsersCommunicationPermissions"
-- END MODIFICATION

function FrontEndChatServer:Ctor()
    self.Events = {
       evtReceiveNewChatPrivate     = LuaEvent:NewIns("FrontEndChatServer.evtReceiveNewChatPrivate"),       ---收到新的私聊内容
       evtReceiveNewChatGroup       = LuaEvent:NewIns("FrontEndChatServer.evtReceiveNewChatGroup"),         ---收到新的群聊内容
       evtRefreshPrivateList        = LuaEvent:NewIns("FrontEndChatServer.evtRefreshPrivateList"),          ---刷新私聊聊天列表
       evtUpdateWorldInterval       = LuaEvent:NewIns("FrontEndChatServer.evtUpdateWorldInterval"),         ---刷新世界频道拉取间隔
       evtUpdateCollegeInterval      = LuaEvent:NewIns("FrontEndChatServer.evtUpdateCollegeInterval"),      ---刷新高校频道拉取间隔
       evtReceiveNewChat            = LuaEvent:NewIns("FrontEndChatServer.evtReceiveNewChat"),              ---收到新消息
       evtPrivateUnreadNumChange    = LuaEvent:NewIns("FrontEndChatServer.evtPrivateUnreadNumChange"),      ---私聊未读消息数量改变
       evtTeamUnreadNumChange       = LuaEvent:NewIns("FrontEndChatServer.evtTeamUnreadNumChange"),         ---队伍未读消息数量改变
       evtWorldUnreadNumChange      = LuaEvent:NewIns("FrontEndChatServer.evtWorldUnreadNumChange"),        ---世界未读消息数量改变
       evtCollegeUnreadNumChange     = LuaEvent:NewIns("FrontEndChatServer.evtCollegeUnreadNumChange"),     ---高校未读消息数量改变
       evtReceiveNewAt              = LuaEvent:NewIns("FrontEndChatServer.evtReceiveNewAt"),                ---收到新的At
       evtAtNumChange               = LuaEvent:NewIns("FrontEndChatServer.evtAtNumChange"),                 ---At数量改变
       evtChatFriendChange          = LuaEvent:NewIns("FrontEndChatServer.evtChatFriendChange"),            ---私聊对象改变
       evtChatRemoveSpeech          = LuaEvent:NewIns("FrontEndChatServer.evtChatRemoveSpeech"),            ---清除某些玩家发言记录(私聊和队伍)
       evtWorldChatRemoveSpeech     = LuaEvent:NewIns("FrontEndChatServer.evtWorldChatRemoveSpeech"),       ---清除某些玩家发言记录（世界）
       evtWorldChatRoomIdInvalid    = LuaEvent:NewIns("FrontEndChatServer.evtWorldChatRoomIdInvalid"),      ---世界频道房间ID失效
       evtCollegeChatRoomIdInvalid   = LuaEvent:NewIns("FrontEndChatServer.evtCollegeChatRoomIdInvalid"),   ---高校频道房间ID失效
       evtWorldChatRoomIdGetSuccess = LuaEvent:NewIns("FrontEndChatServer.evtWorldChatRoomIdGetSuccess"),   ---世界频道房间ID获取成功
       evtCollegeChatRoomIdGetSuccess= LuaEvent:NewIns("FrontEndChatServer.evtCollegeChatRoomIdGetSuccess"),---高校频道房间ID获取成功
       evtFriendListReqSucceed      = LuaEvent:NewIns("FrontEndChatServer.evtFriendListReqSucceed"),        --- 拉取好友列表请求成功
       evtStrangerListReqSucceed    = LuaEvent:NewIns("FrontEndChatServer.evtStrangerListReqSucceed"),      --- 拉取陌生人列表请求成功
       evtPrivateMsgUpdateSucceed   = LuaEvent:NewIns("FrontEndChatServer.evtPrivateMsgUpdateSucceed"),     --- 成功拉取或更新和某一玩家的私聊消息
       evtPrivateMsgRemove          = LuaEvent:NewIns("FrontEndChatServer.evtPrivateMsgRemove"),            --- 移除某一条私聊消息缓存
       evtFetchWorldMsgSucceed      = LuaEvent:NewIns("FrontEndChatServer.evtFetchWorldMsgSucceed"),        --- 成功拉取世界消息
       evtFetchCollegeMsgSucceed     = LuaEvent:NewIns("FrontEndChatServer.evtFetchCollegeMsgSucceed"),     --- 成功拉取高校消息
       evtFetchTeamMsgSucceed       = LuaEvent:NewIns("FrontEndChatServer.evtFetchTeamMsgSucceed"),         --- 成功拉取队伍消息
       evtPrivatePCFirstGetSucceed  = LuaEvent:NewIns("FrontEndChatServer.evtPrivatePCFirstGetSucceed"),    --- pc通过摘要拿私聊历史消息
       evtShowAnnouncement          = LuaEvent:NewIns("FrontEndChatServer.evtShowAnnouncement"),            --- 显示跑马灯公告
       evtHideAnnouncement          = LuaEvent:NewIns("FrontEndChatServer.evtHideAnnouncement"),            --- 隐藏跑马灯公告
       evtStudentPrivilegeChange    = LuaEvent:NewIns("FrontEndChatServer.evtStudentPrivilegeChange"),      --- 学生特权变动
       evtRemoveCurAppointPlayer    = LuaEvent:NewIns("FrontEndChatServer.evtRemoveCurAppointPlayer"),      --- 移除当前预约玩家信息

       -- BEGIN MODIFICATION - VIRTUOS
       -- 从消息摘要获得新的玩家信息(当前只处理私有消息，以解决陌生人发送私聊信息时无法获得其平台的问题)
       evtReceivePlayerInfoFromChat = LuaEvent:NewIns("FrontEndChatServer.evtReceivePlayerInfoFromChat"),
       -- End MODIFICATION - VIRTUOS
    }
    -- 常量
    self.EChatMsgUpdateType = {
        Init    = 1,
        Reset   = 2,
        Add     = 3,
        Remove  = 4,
        Update  = 5,
    }

    --世界频道ID、已拉取消息的索引
    self._roomId = 0
    self._worldMsgIndex = 0

    --高校频道ID、已拉取消息的索引
    self._collegeRoomId = 0
    self._collegeMsgIndex = 0
    self._isStudent = false

    --私聊的玩家列表
    self._chatPlayerInfo = nil

    --上一次拉取世界频道的时间戳
    self._lastReqWorldTime = 0
    --上一次拉取高校频道的时间戳
    self._lastReqCollegeTime = 0

    --是否将新消息统计进 _unReadNum
    self._bCountTeamUnreadNum = true
    self._bCountWorldUnreadNum = false
    self._bCountCollegeUnrealNum = false

    self._bIsInLobby=false
    self._bCanOpenAnouncement=true

    --被清除世界发言的玩家id
    self._worldRemoveSpeechList = {}

    self._privateSummyStruct = PrivateSummyStruct:NewIns()
    self._privateChatStruct = PrivateChatStruct:NewIns()
    self._privatePlayerStruct = PrivatePlayerStruct:NewIns()
    self._groupCacheStruct = GroupCacheStruct:NewIns()
    self._commonChatCacheStruct = CommonChatCacheStruct:NewIns()
end

function FrontEndChatServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSChatMsgNtf",self._OnCSChatMsgNtf, self)
    Facade.ProtoManager:AddNtfListener("CSChatRemoveSpeechNtf",self._OnCSChatRemoveSpeechNtf, self)
end
-----------------------------------------------------------------------
--进入大厅后拉取聊天的初始数据
function FrontEndChatServer:ReqInitServerData()
    self:ReqWorldRoomId()
    self:ReqCollegeRoomId()
    self:ReqAllChannelMsgSumy()
    self:ResetWorldMsgIndex()
    self:ResetCollegeMsgIndex()
    self:ReqTeamChannelChatList()
end

-- 断线重连后应该做的数据清理和重拉过程
function FrontEndChatServer:AfterReconnectSucceed()
    self._privateSummyStruct:ClearAllData()
    self._privateChatStruct:ClearAllData()
    self._privatePlayerStruct:ClearAllData()
    self._groupCacheStruct:ClearAllData()
    self._commonChatCacheStruct:ClearAllData()
end

function FrontEndChatServer:ReqWorldRoomId(fCallBack)
    local oldRoomId = self._roomId
    local OnCSWorldChatGetRoomIDRes = function(res)
        if res and res.result == 0 then
            if self._roomId ~= res.room_id then
                logwarning(string.format("horis ChatModule: world roomId change from %d to %d",oldRoomId,res.room_id))
            end
            self._roomId = res.room_id
            if fCallBack then
                fCallBack()
            end
            self.Events.evtWorldChatRoomIdGetSuccess:Invoke()
        elseif res and res.result then
            logerror(string.format("horis ChatModule: ReqWorldRoomId failed! errid is %d",res.result))
            self.Events.evtWorldChatRoomIdInvalid:Invoke()
            self._roomId = 0
        end
    end
    local req = pb.CSWorldChatGetRoomIDReq:New()
    req:Request(OnCSWorldChatGetRoomIDRes,{bShowErrTip = false, bEnableHighFrequency = true})
end

function FrontEndChatServer:ReqCollegeRoomId(fCallBack)
    if self.mForbidGetCollegeRoomId then
        return
    end
    self.mForbidGetCollegeRoomId = true

    local oldRoomId = self._collegeRoomId
    local OnCSCollegeChatGetRoomIDRes = function(res)
        if res and res.result == 0 then
            if res.is_student then

                if self._collegeRoomId ~= res.college_room_id then
                    logwarning(string.format("horis ChatModule: college roomId change from %d to %d",oldRoomId,res.college_room_id))
                end
                self._collegeRoomId = res.college_room_id
                if fCallBack then
                    fCallBack()
                end
                self.Events.evtCollegeChatRoomIdGetSuccess:Invoke()
            else
                self.Events.evtCollegeChatRoomIdInvalid:Invoke()
            end
            self:TriggerStudentPrivilegeChange(res.is_student)
        elseif res and res.result then
            logerror(string.format("horis ChatModule: ReqCollegeRoomId failed! errid is %d",res.result))
            self.Events.evtCollegeChatRoomIdInvalid:Invoke()
            self._collegeRoomId = 0
        end
    end
    local req = pb.CSWorldChatGetRoomIDReq:New()
    req.only_ask_college_room = true
    req:Request(OnCSCollegeChatGetRoomIDRes,{bShowErrTip = false, bEnableHighFrequency = true})
end

function FrontEndChatServer:GetRoomId()
    return self._roomId
end

function FrontEndChatServer:GetCollegeId()
    return self._collegeRoomId
end

function FrontEndChatServer:IsRoomIdValid()
    return self._roomId ~= 0
end

function FrontEndChatServer:IsCollegeRoomIdValid()
    return self._collegeRoomId ~= 0
end

function FrontEndChatServer:TriggerStudentPrivilegeChange(isStudent)
    self._isStudent = isStudent
    self.Events.evtStudentPrivilegeChange:Invoke()
end

function FrontEndChatServer:IsStudentPrivilege()
    return self._isStudent
end

function FrontEndChatServer:ResetRoomId()
    self._roomId = 0
    self._collegeRoomId = 0
end

function FrontEndChatServer:ResetUnredChat()
    self._privateSummyStruct:ResetPrivateUnreadData()
end

-- 初始化或重置摘要数据
function FrontEndChatServer:SummyCacheInitOrReset(isReset, res)
    self._privateSummyStruct:ResetPrivateUnreadData()
    if isReset then
        -- 重置单独清理摘要缓存？
        self._privateSummyStruct:ResetAllLatestData()
    end

    local ClearInvalidMsgCache = function(playerId, isFriend)
        local channel = isFriend and ChatChannelType.PrivateChatStanger or ChatChannelType.PrivateChatFriend
        if self._privateChatStruct:GetSingleChatCache(channel, playerId) then
            self._privateChatStruct:ResetSingleChatCache(channel, playerId)
        end
    end

    -- BEGIN MODIFICATION - VIRTUOS
    local function handleSummyCacheInitOrReset()
        --私聊摘要
        for _, chatInfo in ipairs(res.private_chat_sumys) do
            local channel = chatInfo.is_friend and ChatChannelType.PrivateChatFriend or
            ChatChannelType.PrivateChatStanger
            self._privateSummyStruct:AddPrivateLatestData(channel, chatInfo.player_info.player_id, chatInfo)
            if Server.FriendServer:CheckIsBlack(chatInfo.player_info.player_id) then
                chatInfo.unread_msg_num = 0
            end
            -- PC端且最后一条消息是自己发的 理应是默认已读的不该出现红点
            if IsHD() and (chatInfo.latest_msg.sender_id == Server.AccountServer:GetPlayerId()) then
                chatInfo.unread_msg_num = 0
            end
            self._privateSummyStruct:AdjustUnreadNumByChannel(channel, chatInfo.unread_msg_num)
            if isReset then
                -- 在未杀端退出前，有概率存在self._privateChatCache好友和陌生人频道都有某个玩家id，这里单独清一下
                ClearInvalidMsgCache(chatInfo.player_info.player_id, chatInfo.is_friend)
            end
        end
        self.Events.evtPrivateUnreadNumChange:Invoke()

        --群聊摘要
        for _, chatInfo in ipairs(res.group_chat_sumys) do
            if chatInfo.channel_type == ChatChannelType.TeamChat then
                self._privateSummyStruct:AddTeamLatestData(chatInfo.latest_msg.content)
                if self._bCountTeamUnreadNum then
                    self._privateSummyStruct:SetUnreadByChannel(ChatChannelType.TeamChat, chatInfo.unread_msg_num)
                end
            end
        end
        self.Events.evtTeamUnreadNumChange:Invoke()

        local result, extraCache = self._privateChatStruct:CacheOfflinePrivateMsg_PC(res.private_chat_msgs_unread)
        if result then
            self.Events.evtPrivatePCFirstGetSucceed:Invoke(extraCache)
        end

        local teamResult, teamExtraCache = self._groupCacheStruct:CacheOfflineTeamMsg_PC(res.team_chat_msgs_unread)
        if teamResult then
            self.Events.evtFetchTeamMsgSucceed:Invoke(teamExtraCache)
        end
    end

    if PLATFORM_GEN9 ~= 1 then
        handleSummyCacheInitOrReset()
    else
        -- Xbox平台要对所有消息进行平台权限过滤
        if Server.ChatServer.hasCommunicationPrivilege == false then
            -- 如果没有通信权限，不作任何处理
            return
        end

        local privateChatSumys = {}
        local groupChatSumys = {}
        local privateChatMsgsUnread = {}
        local teamChatMsgsUnread = {}
        local privateChatSumysFromCurrentPlatform = {}
        local groupChatSumysFromCurrentPlatform = {}
        local privateChatMsgsUnreadFromCurrentPlatform = {}
        local teamChatMsgsUnreadFromCurrentPlatform = {}
        local senderIdListFromCurrentPlatform = {}

        local function filterChatInfo(senderId, senderPlat, fAnonymousUserDo, fCurrentPlatformUserDo)
            if senderPlat ~= Server.AccountServer:GetPlatIdType() then
                -- 如果消息发送者来自其它平台
                Server.ChatServer:CheckAnonymousUserCommunicationPermissions(senderId, function(isAllowed)
                    if isAllowed then
                        fAnonymousUserDo()
                    end
                end)
            else
                fCurrentPlatformUserDo()
            end
            fCurrentPlatformUserDo()
            if table.find(senderIdListFromCurrentPlatform, function(v, k) return v == senderId end) == nil then
                table.insert(senderIdListFromCurrentPlatform, senderId)
            end
        end
        -- 遍历私聊摘要筛选来自当前平台的消息
        for _, chatInfo in ipairs(res.private_chat_sumys) do
            filterChatInfo(chatInfo.player_info.player_id, chatInfo.player_info.plat,
                function() table.insert(privateChatSumys, chatInfo) end, function()
                    table.insert(privateChatSumysFromCurrentPlatform, chatInfo)
                end)
        end
        -- 遍历群聊摘要筛选来自当前平台的消息
        for _, chatInfo in ipairs(res.group_chat_sumys) do
            filterChatInfo(chatInfo.latest_msg.sender.player_id, chatInfo.latest_msg.sender.plat,
                function() table.insert(groupChatSumys, chatInfo) end, function()
                    table.insert(groupChatSumysFromCurrentPlatform, chatInfo)
                end)
        end
        -- 遍历私聊未读筛选来自当前平台的消息
        for _, chatInfo in ipairs(res.private_chat_msgs_unread) do
            filterChatInfo(chatInfo.sender.player_id, chatInfo.sender.plat,
                function() table.insert(privateChatMsgsUnread, chatInfo) end, function()
                    table.insert(privateChatMsgsUnreadFromCurrentPlatform, chatInfo)
                end)
        end
        -- 遍历队伍未读筛选来自当前平台的消息
        for _, chatInfo in ipairs(res.team_chat_msgs_unread) do
            filterChatInfo(chatInfo.sender.player_id, chatInfo.sender.plat,
                function() table.insert(teamChatMsgsUnread, chatInfo) end, function()
                    table.insert(teamChatMsgsUnreadFromCurrentPlatform, chatInfo)
                end)
        end

        local function filterXboxChatInfoCallback(allowedUserList)
            for _, chatInfo in ipairs(privateChatSumysFromCurrentPlatform) do
                local allowedId = table.find(allowedUserList, function(v,k) return v == ULuautils.GetUInt64String(chatInfo.player_info.player_id) end) 
                if allowedId ~= nil then
                    table.insert(privateChatSumys, chatInfo)
                end
            end

            for _, chatInfo in ipairs(groupChatSumysFromCurrentPlatform) do
                local allowedId = table.find(allowedUserList, function(v,k) return v == ULuautils.GetUInt64String(chatInfo.latest_msg.sender.player_id) end) 
                if allowedId ~= nil then
                    table.insert(groupChatSumys, chatInfo)
                end
            end

            for _, chatInfo in ipairs(privateChatMsgsUnreadFromCurrentPlatform) do
                local allowedId = table.find(allowedUserList, function(v,k) return v == ULuautils.GetUInt64String(chatInfo.sender.player_id) end) 
                if allowedId ~= nil then
                    table.insert(privateChatMsgsUnread, chatInfo)
                end
            end

            for _, chatInfo in ipairs(teamChatMsgsUnreadFromCurrentPlatform) do
                local allowedId = table.find(allowedUserList, function(v,k) return v == ULuautils.GetUInt64String(chatInfo.sender.player_id) end) 
                if allowedId ~= nil then
                    table.insert(teamChatMsgsUnread, chatInfo)
                end
            end

            res.private_chat_sumys = privateChatSumys
            res.group_chat_sumys = groupChatSumys
            res.private_chat_msgs_unread = privateChatMsgsUnread
            res.team_chat_msgs_unread = teamChatMsgsUnread
            handleSummyCacheInitOrReset()
        end

        if #senderIdListFromCurrentPlatform == 0 then
            --没有来自当前平台的信息
            res.private_chat_sumys = privateChatSumys
            res.group_chat_sumys = groupChatSumys
            res.private_chat_msgs_unread = privateChatMsgsUnread
            res.team_chat_msgs_unread = teamChatMsgsUnread
            handleSummyCacheInitOrReset()
        else
            Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText, false, senderIdListFromCurrentPlatform, filterXboxChatInfoCallback)
        end
        -- END MODIFICATION - VIRTUOS
    end
end

function FrontEndChatServer:ReqAllChannelMsgSumy()
    local OnCSChatGetMsgSumyAllChannelRes = function(res)
        if res and res.result == 0 then
            self:SummyCacheInitOrReset(false, res)
        end
    end
    local req = pb.CSChatGetMsgSumyAllChannelReq:New()
    req.team_id = Server.TeamServer:GetTeamID()
    req:Request(OnCSChatGetMsgSumyAllChannelRes, {bShowErrTip = false})
end

function FrontEndChatServer:SyncServerData()
    local OnCSChatGetMsgSumyAllChannelRes = function(res)
        if res and res.result == 0 then
            self:SummyCacheInitOrReset(true, res)
        end
    end
    local req = pb.CSChatGetMsgSumyAllChannelReq:New()
    req.team_id = Server.TeamServer:GetTeamID()
    req:Request(OnCSChatGetMsgSumyAllChannelRes, {bShowErrTip = false})
end

--end
-----------------------------------------------------------------------


-------------------------------------------------私聊---------------------------------------------------
--拉取私聊频道的玩家聊天记录
function FrontEndChatServer:ReqPrivateChannelChatList(playerId,fCallback,bFriend)
    local judgeResult, curIndex = self:HandleDataConflictPrivate(playerId, bFriend)
    local channel = bFriend and ChatChannelType.PrivateChatFriend or ChatChannelType.PrivateChatStanger
    local chatCache = self._privateChatStruct:GetSingleChatCache(channel, playerId)
    if (not judgeResult) and chatCache then
        self.Events.evtPrivateMsgUpdateSucceed:Invoke(playerId, chatCache)
        -- 补一个处理
        local tempRes = {}
        tempRes.msg_list = chatCache
        if fCallback then
            fCallback(tempRes)
        end

        return
    end

    local OnCSChatPrivateLoadRes = function(res)
        if res and res.result == 0 and fCallback then
            self._privateChatStruct:OnCSChatPrivateLoadReq(channel, playerId, curIndex, res.msg_list)
            chatCache = self._privateChatStruct:GetSingleChatCache(channel, playerId)
            self.Events.evtPrivateMsgUpdateSucceed:Invoke(playerId, chatCache)
        end
        if fCallback then
            fCallback(res)
        end
    end
    local req = pb.CSChatPrivateLoadReq:New()
    req.target_player_id = playerId
    req.last_msg_index = curIndex ~= nil and curIndex or 0
    req.is_friend_chat = bFriend
    req:Request(OnCSChatPrivateLoadRes,{bEnableHighFrequency = true})
end

--获取第一个未读私聊玩家信息
function FrontEndChatServer:GetPrivateUnreadTopPlayer()
    return self._privateSummyStruct:GetPrivateUnreadTopPlayer()
end

-- 借由传入的旧的好友列表来判断好友列表中哪些好友发生了变更(包括新加的和被删的)
function FrontEndChatServer:GetChangedFriendListByCompare(oldFriendList)
    if not IsHD() then
        return false, nil, nil
    end
    local newFriendList = Server.FriendServer:GetFriendPanelList()
    local addedList, removedList = self._privatePlayerStruct:GetChangedFriendListByCompare(oldFriendList, newFriendList)
    return true, addedList, removedList
end

--拉取好友列表
function FrontEndChatServer:ReqFriendInfoList()

    local friendList = Server.FriendServer:GetFriendPanelList()
    if friendList then
        self._privatePlayerStruct:SetFriendListData(friendList)
        friendList = self._privatePlayerStruct:GetFriendList()
        self.Events.evtFriendListReqSucceed:Invoke(friendList)
    end

    -- local fOnCSFriendPlayerListRes = function(res)
    --     if res and res.result == 0 then
    --         self._privatePlayerStruct:SetFriendListData(res["player_list"])
    --         local friendList = self._privatePlayerStruct:GetFriendList()
    --         self.Events.evtFriendListReqSucceed:Invoke(friendList)
    --     end
    -- end
    -- Server.SocialServer:ReqFriendList(fOnCSFriendPlayerListRes,{bEnableHighFrequency = true})
end

--拉取陌生人列表
function FrontEndChatServer:ReqStrangerInfoList()
    local OnCSChatGetStangerInfoRes = function(res)
        if not res or res.result ~= 0 then
            return
        end

        self._privatePlayerStruct:SetStrangerListData(res.stanger_info_list)

        local addList = self._privatePlayerStruct:GetSortedNewStrangerList()
        if table.isempty(addList) then
            local strangerList = self._privatePlayerStruct:GetStrangerList()
            self.Events.evtStrangerListReqSucceed:Invoke(strangerList)
        else
            self:UpdateLocalStrangerState()
        end
    end

    local req = pb.CSChatGetStangerInfoReq:New()
    req:Request(OnCSChatGetStangerInfoRes,{bEnableHighFrequency = true})
end

--更新客户端本地存的陌生人状态
function FrontEndChatServer:UpdateLocalStrangerState()
    local addList = self._privatePlayerStruct:GetSortedNewStrangerList()

    local onCSStateBatchGetInfoReq = function(res)
        if res and res.result == 0 then
            self._privatePlayerStruct:UpdateLocalStrangerState(res.player_info)
            local strangerList = self._privatePlayerStruct:GetStrangerList()
            self.Events.evtStrangerListReqSucceed:Invoke(strangerList)
        end
    end

    local playerList = {}
    for _,info in ipairs(addList) do
        table.insert(playerList, info.player_id)
    end

    local req = pb.CSStateBatchGetInfoReq:New()
    req.player_id = playerList
    req:Request(onCSStateBatchGetInfoReq,{bEnableHighFrequency = true})
end

function FrontEndChatServer:AddStrangerInfo(playerInfo) --PlayerSimpleInfo
    self._privatePlayerStruct:AddSingleNewStranger(playerInfo)
end

function FrontEndChatServer:ClearNewStrangers()
    self._privatePlayerStruct:ClearAllNewStranger()
end

--置顶某个玩家，一般是收到这个玩家的新消息之后置顶
function FrontEndChatServer:TopPlayer(playerId)
    local playerList = self._privatePlayerStruct:GetFriendList()
    if not Server.FriendServer:CheckIsFriend(playerId) then
        playerList = self._privatePlayerStruct:GetStrangerList()
    end
    if table.isempty(playerList) then
        return
    end
    local index = 1
    for i,info in ipairs(playerList) do
        if info.player_id == playerId then
            index = i
        end
    end
    local info = table.remove(playerList,index)
    table.insert(playerList,1,info)
    self.Events.evtRefreshPrivateList:Invoke(playerId)
end

--当前正在聊天对象的PlayerSimpleInfo
function FrontEndChatServer:SetChatPlayerInfo(newInfo, isClickChat)
    isClickChat = setdefault(isClickChat, false)
    self._chatPlayerInfo = newInfo
    self.Events.evtChatFriendChange:Invoke(newInfo, isClickChat)
end

function FrontEndChatServer:GetChatPlayerInfo()
    return self._chatPlayerInfo
end

local function BuildChatMsgSummaryPlayer(playerId, content, bFriend)
    local chatMsgSummaryPlayer = {}
    chatMsgSummaryPlayer.player_info =  {player_id = playerId}
    chatMsgSummaryPlayer.unread_msg_num = 0
    chatMsgSummaryPlayer.read_msg_index = content.index
    chatMsgSummaryPlayer.latest_msg = content
    chatMsgSummaryPlayer.is_friend = bFriend
    return chatMsgSummaryPlayer
end

local function OnConstructSendContent(text, emojiList, chatMsgType, voice_key, duration, translated_text)
    local contentValue = {
        timestamp = os.time(),
        msg_type = chatMsgType or ChatMsgType.Str,
        text = text,
        emoji_list = emojiList,
        sender_id = Server.AccountServer:GetPlayerId(),
        voice_data = chatMsgType == ChatMsgType.Voice and {
            voice_key = voice_key or "",
            duration = duration or 0,
            translated_text = translated_text or ""
        } or {}
    }
    return contentValue
end

--发送私聊消息
function FrontEndChatServer:ChatPrivateSend(playerId,text,emojiList,chatMsgType,voice_key,duration,translated_text)
    local bFriend = Server.FriendServer:CheckIsFriend(playerId)
    local sendContent = OnConstructSendContent(text, emojiList, chatMsgType, voice_key, duration, translated_text)

    local OnCSChatPrivateSendRes = function(res)
        if res and res.result == 0 or (res.result == Err.ChatForbidden and res.forbid_info.forbid_type & ChatForbidType.ChatSilent ~= 0) then
            local channel = bFriend and ChatChannelType.PrivateChatFriend or ChatChannelType.PrivateChatStanger
            local chatMsgSummary = BuildChatMsgSummaryPlayer(playerId, res.content, bFriend)
            self._privateChatStruct:AppendMsgForSingleCache(channel, playerId, res.content)
            self._privateSummyStruct:AddPrivateLatestData(channel, playerId, chatMsgSummary)
            self:TopPlayer(playerId)
            if IsHD() then
                res.content.receiver_id = playerId
                self.Events.evtReceiveNewChatPrivate:Invoke(playerId, res.content, res.receiver_player_info)
            else
                self.Events.evtReceiveNewChatPrivate:Invoke(playerId, res.content, res.receiver_player_info)
            end

            local tmpChat = {
                channel = ChatChannelType.PrivateChatFriend,
                sender = {nick_name = Server.RoleInfoServer.nickName,player_id = res.content.sender_id},
                content = res.content
            }
            self:AddNewestChat(tmpChat)
        elseif res and res.result == Err.ChatForbidden then
            local free_time = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(res.forbid_info.free_time)
            if sendContent.msg_type == ChatMsgType.Voice then
                local tip = string.format(ServerTipCode.ChatForbidVoice,free_time)
                LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
            else
                local tip = string.format(ServerTipCode.ChatForbidMsg,free_time)
                LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
            end
        elseif res and res.result == Err.ChatForbiddenByCreditCheck then
            LuaGlobalEvents.evtServerShowCreditTip:Invoke(Server.FriendServer.EXinyongType.ChatLimit)
        else
            Facade.ProtoManager:ManuelHandleErrCode(res)
        end
    end

    local req = pb.CSChatPrivateSendReq:New()
    req.receiver_id = playerId
    req.content = sendContent
    req:Request(OnCSChatPrivateSendRes,{bShowErrTip = false, bEnableHighFrequency = true})
end

--发送预约消息
function FrontEndChatServer:ChatAppointSend(playerId,text)
    local sendContent = {
        timestamp = os.time(),
        msg_type = ChatMsgType.Appointment,
        text = text,
        emoji_list = {},
        sender_id = Server.AccountServer:GetPlayerId(),
        voice_data = {}
    }

    local OnCSChatAppointmentRes = function(res)
        if res and res.result == 0 then
            local channel = ChatChannelType.PrivateChatFriend
            local chatMsgSummary = BuildChatMsgSummaryPlayer(playerId, res.content, true)
            self._privateChatStruct:AppendMsgForSingleCache(channel, playerId, res.content)
            self._privateSummyStruct:AddPrivateLatestData(channel, playerId, chatMsgSummary)
            self:TopPlayer(playerId)
            self.Events.evtReceiveNewChatPrivate:Invoke(playerId, res.content)

            local tmpChat = {
                channel = ChatChannelType.PrivateChatFriend,
                sender = {nick_name = Server.RoleInfoServer.nickName,player_id = res.content.sender_id},
                content = res.content
            }
            self:AddNewestChat(tmpChat)
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.AppointSuccess, 2)
        else
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.AppointFailed, 2)
            self.Events.evtRemoveCurAppointPlayer:Invoke(playerId)
        end
    end

    local req = pb.CSChatAppointmentReq:New()
    req.receiver_id = playerId
    req.content = sendContent
    req:Request(OnCSChatAppointmentRes)
end

--通知服务器最新的私聊已读序号
function FrontEndChatServer:SendPrivateReadIndex(playerId)
    local channel = Server.FriendServer:CheckIsFriend(playerId) and ChatChannelType.PrivateChatFriend or ChatChannelType.PrivateChatStanger
    local chatMsgSummary = self._privateSummyStruct:GetLatestDataById(channel, playerId)
    local index = chatMsgSummary and chatMsgSummary.latest_msg.index or 0
    if index ~= 0 then
        local req = pb.CSChatPrivateReadStatUpdateReq:New()
        req.last_msg_index = index
        req.target_player_id = playerId
        req:Request({bEnableHighFrequency = true})
        local unreadValue = math.max(0, self:GetUnreadNumByChannel(channel) - chatMsgSummary.unread_msg_num)
        self._privateSummyStruct:SetUnreadByChannel(channel, unreadValue)
        chatMsgSummary.unread_msg_num = 0
        self.Events.evtPrivateUnreadNumChange:Invoke()
    end
end

--通知服务器一键已读全部私聊
function FrontEndChatServer:SendPrivateReadIndex_PC()
    local req = pb.CSChatPrivateReadStatUpdateReq:New()
    req.last_msg_index = 0  -- 这个参数废弃的
    req.target_player_id = 0  -- 传0服务端默认会当作私聊全部已读
    req:Request()
    self._privateSummyStruct:OnClickPrivateRead_PC()
    local unreadValue = 0  -- pc一键已读全部私聊
    self._privateSummyStruct:SetUnreadByChannel(ChatChannelType.PrivateChatFriend, unreadValue)
    self._privateSummyStruct:SetUnreadByChannel(ChatChannelType.PrivateChatStanger, unreadValue)
    self.Events.evtPrivateUnreadNumChange:Invoke()
end

--添加一条好友预约
function FrontEndChatServer:AddNewAppointment(playerId,fCallBack)
    if not Server.FriendServer:CheckIsFriend(playerId) then
        logwarning("%s is not Friend,Can not Appoint",playerId)
        return
    end

    local onChatListRes = function(res)
        local chatList = res.msg_list
        local _index = 0
        if not table.isempty(chatList) then
            _index = chatList[#chatList].index
        end
        local ChatMsg  = {
            timestamp = os.time() * 1000,
            bHandle = false
        }
        table.insert(chatList,ChatMsg)

        if fCallBack then
            fCallBack(res)
        end
    end

    Server.FrontEndChatServer:ReqPrivateChannelChatList(playerId, onChatListRes, true)
end

--处理某玩家聊天记录同时存在于陌生人和好友缓存内（即存在动态添加好友和删除的操作）
function FrontEndChatServer:HandleDataConflictPrivate(playerId, bFriend)
    local channel = bFriend and ChatChannelType.PrivateChatFriend or ChatChannelType.PrivateChatStanger

    local chatCache = self._privateChatStruct:GetSingleChatCache(channel, playerId)
    if not chatCache then
        return false, nil
    end

    local latestValidMsgIndex = self._privateChatStruct:GetSingleLatestChatCacheFiltered(channel, playerId)
    local chatMsg = self._privateSummyStruct:GetLatestDataById(channel, playerId)
    local index = chatMsg and chatMsg.latest_msg.index or 0

    if (index == 0) or latestValidMsgIndex == index then
        return false, nil
    end

    return true, math.min(#chatCache, index)
end
-------------------------------------------------End---------------------------------------------------


-------------------------------------------------获取未读消息数量---------------------------------------------------
function FrontEndChatServer:GetUnreadNumByChannel(channel)
    return self._privateSummyStruct:GetUnreadNumByChannel(channel)
end

function FrontEndChatServer:GetPrivateTotalUnreadNum()
    local friendUnreadNum = self:GetUnreadNumByChannel(ChatChannelType.PrivateChatFriend)
    local strangerUnreadNum = self:GetUnreadNumByChannel(ChatChannelType.PrivateChatStanger)
    return friendUnreadNum + strangerUnreadNum
end

--队伍频道未读统计
function FrontEndChatServer:StartCountTeamUnreadNum()
    self._bCountTeamUnreadNum = true
end

function FrontEndChatServer:StopCountTeamUnreadNum()
    self._bCountTeamUnreadNum = false
    self._privateSummyStruct:SetUnreadByChannel(ChatChannelType.TeamChat, 0)
    self.Events.evtTeamUnreadNumChange:Invoke()
end

--世界频道未读统计
function FrontEndChatServer:StartCountWorldUnreadNum()
    self._bCountWorldUnreadNum = true

    local channel = ChatChannelType.WorldChat
    if self._privateSummyStruct:GetUnreadNumByChannel(channel) == 0 then
        self._privateSummyStruct:SetUnreadByChannel(channel, self:GetAtNumByChannel(channel))
    end
end

function FrontEndChatServer:StopCountWorldUnreadNum()
    self._bCountWorldUnreadNum = false
    self._privateSummyStruct:ResetWorldUnreadData()
    self.Events.evtWorldUnreadNumChange:Invoke()
end

--高校频道未读统计
function FrontEndChatServer:StartCountCollegeUnreadNum()
    self._bCountCollegeUnreadNum = true

    local channel = ChatChannelType.CollegeChat
    if self._privateSummyStruct:GetUnreadNumByChannel(channel) == 0 then
        self._privateSummyStruct:SetUnreadByChannel(channel, self:GetAtNumByChannel(channel))
    end
end

function FrontEndChatServer:StopCountCollegeUnreadNum()
    self._bCountCollegeUnreadNum = false
    local channelValue = ChatChannelType.CollegeChat
    self._privateSummyStruct:SetUnreadByChannel(channelValue, 0)
    self.Events.evtCollegeUnreadNumChange:Invoke()
end
-------------------------------------------------End---------------------------------------------------


------------------------------------------------队伍聊天---------------------------------------------------
--拉取Team频道的聊天list
function FrontEndChatServer:ReqTeamChannelChatList(fCallback)
    -- pc现在这套和手游差异很大 不需要单独拉取数据（请求摘要会拉离线消息）
    if IsHD() then
        return
    end

    local groupChatCache = self._groupCacheStruct:GetTeamChatCache()
    if groupChatCache then
        self.Events.evtFetchTeamMsgSucceed:Invoke(groupChatCache)
        if fCallback then
            fCallback(groupChatCache)
        end
        return
    end
    if not Server.TeamServer:IsInTeam() or Server.TeamServer:GetTeamMatesNum() < 2 then
        self._groupCacheStruct:SetTeamChatCache({})
        self.Events.evtFetchTeamMsgSucceed:Invoke(self._groupCacheStruct:GetTeamChatCache())
        if fCallback then
            fCallback(self._groupCacheStruct:GetTeamChatCache())
        end
        return
    end
    local OnCSChatTeamLoadTRes = function(res)
        if res and res.result == 0 then
            self._groupCacheStruct:SetTeamChatCache(res.msg_list)
            self.Events.evtFetchTeamMsgSucceed:Invoke(self._groupCacheStruct:GetTeamChatCache())
            if fCallback then
                fCallback(self._groupCacheStruct:GetTeamChatCache())
            end
        end
    end
    local req = pb.CSChatTeamLoadTReq:New()
    req.team_id = Server.TeamServer:GetTeamID()
    req.last_msg_index = 0
    req:Request(OnCSChatTeamLoadTRes,{bShowErrTip = false})
end

--发送队伍消息
function FrontEndChatServer:ChatTeamSend(text,emojiList,chatMsgType,voice_key,duration,translated_text)
    local content = OnConstructSendContent(text, emojiList, chatMsgType, voice_key, duration, translated_text)

    local onCSChatTeamSendTRes = function(res)
        --静默禁言，显示回包里的消息，其他显示CSChatMsgNtf里的消息
        if res and res.result == Err.ChatForbidden and res.forbid_info.forbid_type & ChatForbidType.ChatSilent ~= 0 then
            self._privateSummyStruct:AddTeamLatestData(res.content)
            self._groupCacheStruct:AddTeamChatCache(res.content)
            self.Events.evtReceiveNewChatGroup:Invoke(ChatChannelType.TeamChat, res.content)

            local tmpChat = {
                channel = ChatChannelType.TeamChat,
                sender = res.content.sender,
                content = res.content.content
            }
            self:AddNewestChat(tmpChat)
        elseif res and res.result == Err.ChatForbidden then
            local free_time = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(res.forbid_info.free_time)
            if content.msg_type == ChatMsgType.Voice then
                local tip = string.format(ServerTipCode.ChatForbidVoice,free_time)
                LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
            else
                local tip = string.format(ServerTipCode.ChatForbidMsg,free_time)
                LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
            end
        elseif res and res.result == Err.ChatForbiddenByCreditCheck then
            LuaGlobalEvents.evtServerShowCreditTip:Invoke(Server.FriendServer.EXinyongType.ChatLimit)
        else
            Facade.ProtoManager:ManuelHandleErrCode(res)
        end
    end

    local req = pb.CSChatTeamSendTReq:New()
    req.team_id = Server.TeamServer:GetTeamID()
    req.content = content
    req:Request(onCSChatTeamSendTRes,{bShowErrTip = false})
end

function FrontEndChatServer:TeammateChange(playerId, text, nickName)
    local content = {
        timestamp = os.time() * 1000,
        msg_type = ChatMsgType.Str,
        text = text,
        sender_id = playerId,
        flag = 1, --这是个提示信息，不是聊天文本
        nickName = nickName,
    }
    local function BuildGroupChatMsg()
        local groupChatMsg = {}
        groupChatMsg.sender = {player_id = playerId}
        groupChatMsg.content = content
        return groupChatMsg
    end
    local onChatListRes = function()
        self._groupCacheStruct:AddTeamChatCache(BuildGroupChatMsg())
        self.Events.evtReceiveNewChatGroup:Invoke(ChatChannelType.TeamChat, BuildGroupChatMsg())
    end

    if (not IsHD()) and (not self._groupCacheStruct:GetTeamChatCache()) then
        self:ReqTeamChannelChatList(onChatListRes)
    else
        onChatListRes()
    end
end

--通知服务器最新的队伍已读序号
function FrontEndChatServer:SendTeamReadIndex()
    local chatMsgSummary = self._privateSummyStruct:GetLatestDataByChannel(ChatChannelType.TeamChat)
    local index = chatMsgSummary and chatMsgSummary.index or 0
    if index ~= 0 and Server.TeamServer:IsInTeam() and Server.TeamServer:GetTeamMatesNum() > 1 then
        local req = pb.CSChatTeamReadStatUpdateTReq:New()
        req.last_msg_index = index
        req.team_chat_id = Server.TeamServer:GetTeamID()
        req:Request()
    end
end
-------------------------------------------------End---------------------------------------------------

-------------------------------------------------世界频道-----------------------------------------------
-- BEGIN MODIFICATION - VIRTUOS
-- 根据平台权限过滤消息列表
function FrontEndChatServer:FilterWorldMsgListByPlatformPrivilege(res, fCallback)
    if PLATFORM_GEN9 ~= 1 then
        return
    end
    
    if res and res.result == 0 then
        local filterMsgList = {}
        local msgListFromCurPlatform = {}
        local senderIdListFromCurPlatform = {}
        for _, msg in pairs(res.msg_list) do
            local senderId = msg.sender.player_id
            local senderPlat = msg.sender.plat
            if senderPlat ~= Server.AccountServer:GetPlatIdType() then
                Server.ChatServer:CheckAnonymousUserCommunicationPermissions(senderId, function(isAllowed)
                    if isAllowed then
                        table.insert(filterMsgList, msg)
                    end
                end)
            else
                if table.find(senderIdListFromCurPlatform, function(v, k) return v == senderId end) == nil then
                    table.insert(senderIdListFromCurPlatform, senderId)
                    table.insert(msgListFromCurPlatform, msg)
                end
            end
        end
        local function filterPlatformMsgCallback(allowedUserList)
            for _, msg in pairs(msgListFromCurPlatform) do
                local allowedId = table.find(allowedUserList,function(v, k) return v == ULuautils.GetUInt64String(msg.sender.player_id) end)
                if allowedId ~= nil then
                   table.insert(filterMsgList, msg)
                end
            end
            res.msg_list = filterMsgList
            fCallback(res)
        end
        if #msgListFromCurPlatform == 0 then
            -- 没有来自当前平台的信息
            res.msg_list = filterMsgList
            fCallback(res)
        else
            Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText, false, senderIdListFromCurPlatform, filterPlatformMsgCallback)
        end
    end
end
-- END MODIFICATION - VIRTUOS

--拉取世界频道的聊天list
function FrontEndChatServer:ReqWorldChannelChatList(fCallback)
    self._lastReqWorldTime = os.time()
    local function f()
        local OnCSChatWorldLoadTRes = function(res)
            if res and res.result == 0 then
                -- BEGIN MODIFICATION - VIRTUOS
                local function filterWorldMsgListByPlatformPrivilegeCallback(res)
                -- END MODIFICATION - VIRTUOS
                    res.msg_list = self:FilterMsgList(res.msg_list, EFilterType.BlackList)
                    local insertedMsgList = {}
                    self:AppendWorldChatList(res.msg_list, insertedMsgList)

                    self:ParseAnnouncement(res.rolling_notice)

                    local curWorldCacheData, isRemoveExtra = self:GetWorldChatList()
                    if not table.isempty(res.msg_list) then
                        -- 只有超上限了清理了部分数据才这么干
                        if isRemoveExtra then
                            local num = math.min(#curWorldCacheData, #res.msg_list)
                            num = math.max(0, num)
                            self.Events.evtFetchWorldMsgSucceed:Invoke(
                                self.EChatMsgUpdateType.Add, curWorldCacheData, num, true)
                        elseif (insertedMsgList) and (not table.isempty(insertedMsgList)) then
                            self.Events.evtFetchWorldMsgSucceed:Invoke(
                                self.EChatMsgUpdateType.Add, insertedMsgList, #insertedMsgList, true, true)
                        end
                    end

                    if fCallback then
                        fCallback(res.msg_list, res.load_msg_interval)
                    end

                    if not table.isempty(res.msg_list) then
                        self._worldMsgIndex = res.msg_list[#res.msg_list].content.index
                        local chatInfo = res.msg_list[#res.msg_list]
                        if IsHD() then
                            self:AddWorldNewChat2NewestChat(res.msg_list)
                        else
                            self:AddWorldNewChat2NewestChat(chatInfo)
                        end
                        self:AddNewToAtList(ChatChannelType.WorldChat, res.msg_list)
                        if self._bCountWorldUnreadNum then
                            local worldChannel = ChatChannelType.WorldChat
                            self._privateSummyStruct:SetUnreadByChannel(
                                worldChannel, self:GetUnreadNumByChannel(worldChannel) + #res.msg_list)
                            self.Events.evtWorldUnreadNumChange:Invoke()
                        end
                    end

                    if res.new_room_id ~= 0 then
                        if self._roomId ~= res.new_room_id then
                            logerror(string.format("horis ChatModule: world roomId change from %d to %d", self._roomId,res.new_room_id))
                        end
                        self._roomId = res.new_room_id
                    end
                    --清除发言
                    if not table.isempty(res.rm_speech_playerid_list) then
                        self:WorldChatRemoveSpeech(res.rm_speech_playerid_list, self._worldMsgIndex)
                    end
                end
                -- BEGIN MODIFICATION - VIRTUOS
                if PLATFORM_GEN9 == 1 then
                    self:FilterWorldMsgListByPlatformPrivilege(res, filterWorldMsgListByPlatformPrivilegeCallback)
                else
                    filterWorldMsgListByPlatformPrivilegeCallback(res)
                end
                -- END MODIFICATION - VIRTUOS

            elseif res and res.result == Err.ChatWorldInvaildRoomID then
                self._roomId = 0
                self:ReqWorldRoomId()
                self.Events.evtWorldChatRoomIdInvalid:Invoke()
            end
        end
        local req = pb.CSChatWorldLoadTReq:New()
        req.worldchat_room_id = self._roomId
        req.read_msg_index = self._worldMsgIndex
        req:Request(OnCSChatWorldLoadTRes,{bShowErrTip = false, bEnableHighFrequency = true})

        self._lastTimeStamp = os.time()
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if PLATFORM_GEN9 == 1 then
        if Server.ChatServer.hasCommunicationPrivilege == false then
            --没有通信权限，不拉取世界消息
            return
        end
    end
    -- END MODIFICATION - VIRTUOS

    if self._roomId == 0 then
        self:ReqWorldRoomId(f)
    else
        f()
    end
end

--拉取高校频道的聊天list
function FrontEndChatServer:ReqCollegeChannelChatList(fCallback)
    self._lastReqCollegeTime = os.time()
    local function f()
        local OnCSChatCollegeLoadTRes = function(res)
            if res and res.result == 0 then
                -- BEGIN MODIFICATION - VIRTUOS
                local function filterCollegeMsgListByPlatformPrivilegeCallback(res)
                -- END MODIFICATION - VIRTUOS
                    res.msg_list = self:FilterMsgList(res.msg_list, EFilterType.BlackList)
                    local insertedMsgList = {}
                    self:AppendCollegeChatList(res.msg_list, insertedMsgList)

                    self:ParseAnnouncement(res.rolling_notice)

                    local curCollegeCacheData, isRemoveExtra = self:GetCollegeChatList()
                    if not table.isempty(res.msg_list) then
                        -- 只有超上限了清理了部分数据才这么干
                        if isRemoveExtra then
                            local num = math.min(#curCollegeCacheData, #res.msg_list)
                            num = math.max(0, num)
                            self.Events.evtFetchCollegeMsgSucceed:Invoke(
                                self.EChatMsgUpdateType.Add, curCollegeCacheData, num, true)
                        elseif (insertedMsgList) and (not table.isempty(insertedMsgList)) then
                            self.Events.evtFetchCollegeMsgSucceed:Invoke(
                                self.EChatMsgUpdateType.Add, insertedMsgList, #insertedMsgList, true, true)
                        end
                    end

                    if fCallback then
                        fCallback(res.msg_list, res.load_msg_interval)
                    end

                    if not table.isempty(res.msg_list) then
                        self._collegeMsgIndex = res.msg_list[#res.msg_list].content.index
                        local chatInfo = res.msg_list[#res.msg_list]
                        if IsHD() then
                            self:AddCollegeNewChat2NewestChat(res.msg_list)
                        else
                            self:AddCollegeNewChat2NewestChat(chatInfo)
                        end
                        self:AddNewToAtList(ChatChannelType.CollegeChat, res.msg_list)
                        if self._bCountCollegeUnreadNum then
                            local collegeChannel = ChatChannelType.CollegeChat
                            self._privateSummyStruct:SetUnreadByChannel(
                                collegeChannel, self:GetUnreadNumByChannel(collegeChannel) + #res.msg_list)
                            self.Events.evtCollegeUnreadNumChange:Invoke()
                        end
                    end

                    if res.new_room_id ~= 0 then
                        if self._collegeRoomId ~= res.new_room_id then
                            logerror(string.format("horis ChatModule: college roomId change from %d to %d", self._collegeRoomId,res.new_room_id))
                        end
                        self._collegeRoomId = res.new_room_id
                    end
                    --清除发言
                    if not table.isempty(res.rm_speech_playerid_list) then
                        self:WorldChatRemoveSpeech(res.rm_speech_playerid_list, self._collegeMsgIndex)
                    end
                end
                -- BEGIN MODIFICATION - VIRTUOS
                if PLATFORM_GEN9 == 1 then
                    self:FilterWorldMsgListByPlatformPrivilege(res, filterCollegeMsgListByPlatformPrivilegeCallback)
                else
                    filterCollegeMsgListByPlatformPrivilegeCallback(res)
                end
                -- END MODIFICATION - VIRTUOS

            elseif res and res.result == Err.ChatWorldInvaildRoomID then
                self._collegeRoomId = 0
                self:ReqCollegeRoomId()
                self.Events.evtCollegeChatRoomIdInvalid:Invoke()
            end
        end
        local req = pb.CSChatWorldLoadTReq:New()
        req.worldchat_room_id = self._collegeRoomId
        req.read_msg_index = self._collegeMsgIndex
        req:Request(OnCSChatCollegeLoadTRes,{bShowErrTip = false, bEnableHighFrequency = true})

    end
    -- BEGIN MODIFICATION - VIRTUOS
    if PLATFORM_GEN9 == 1 then
        if Server.ChatServer.hasCommunicationPrivilege == false then
            --没有通信权限，不拉取高校消息
            return
        end
    end
    -- END MODIFICATION - VIRTUOS

    if self._collegeRoomId == 0 then
        self:ReqCollegeRoomId(f)
    else
        f()
    end
end

--跑马灯公告
function FrontEndChatServer:ParseAnnouncement(rollingNotice)
    loginfo("[FrontEndChatServer] ParseAnnouncement")
    -- if self._rollingNotice then
    --     return
    -- end
    self._rollingNotice=rollingNotice
    logtable(self._rollingNotice)
    if self._bCanOpenAnouncement then
        self.Events.evtShowAnnouncement:Invoke()
    end
end

function FrontEndChatServer:CheckShowAnnouncement()
    if self._bCanOpenAnouncement and isvalid(self._rollingNotice) and self._rollingNotice.begin_timestamp then
        local currentTime=Facade.ClockManager:GetLocalTimestamp()
        if self._rollingNotice.begin_timestamp<=currentTime and currentTime<=self._rollingNotice.end_timestamp then
            return true,not self._rollingNotice.is_multiline,self._rollingNotice.content
        end
    end
    return false,false,''
end

function FrontEndChatServer:SetChatInLobby(bInLobby)
    self._bIsInLobby=bInLobby
end

function FrontEndChatServer:CheckChatIsInLobby()
    return self._bIsInLobby
end

function FrontEndChatServer:SetCanOpenAnouncement(bCanOpenAnouncement)
    self._bCanOpenAnouncement=bCanOpenAnouncement
end

function FrontEndChatServer:GetCanOpenAnouncement()
    return self._bCanOpenAnouncement
end

function FrontEndChatServer:RemovePlayerChatList(playerId)
    if self._groupCacheStruct:RemoveWorldCacheById(playerId) then
        if not IsHD() then
            self.Events.evtFetchWorldMsgSucceed:Invoke(
                self.EChatMsgUpdateType.Remove, self:GetWorldChatList(), 1)  -- 1为默认参数，划到底部
        end
    end

    --移除世界@记录
    if self._privateSummyStruct:RemoveWorldAtMsgById(playerId) then
        self.Events.evtAtNumChange:Invoke()
    end

    --移除最新一条消息记录
    if self._commonChatCacheStruct:CanRemoveNewChat(playerId) then
        self:AddNewestChat(self._commonChatCacheStruct:BuildEmptyNewChat(), true)
    end

    self:RemoveCollegePlayerChatList(playerId)
end

function FrontEndChatServer:RemoveCollegePlayerChatList(playerId)
    if self._groupCacheStruct:RemoveWorldCacheById(playerId, ChatChannelType.CollegeChat) then
        if not IsHD() then
            self.Events.evtFetchCollegeMsgSucceed:Invoke(
                self.EChatMsgUpdateType.Remove, self:GetCollegeChatList(), 1)  -- 1为默认参数，划到底部
        end
    end

    --移除高校@记录
    if self._privateSummyStruct:RemoveWorldAtMsgById(playerId, ChatChannelType.CollegeChat) then
        self.Events.evtAtNumChange:Invoke()
    end

    --移除最新一条消息记录
    if self._commonChatCacheStruct:CanRemoveNewChat(playerId) then
        self:AddNewestChat(self._commonChatCacheStruct:BuildEmptyNewChat(), true)
    end
end

function FrontEndChatServer:ChangeRoomIdAndKey(roomId,key)
    logerror(string.format("horis ChatModule: world roomId change from %s to %s",tostring(self._roomId),tostring(roomId)))
    self._roomId = roomId
end

function FrontEndChatServer:GetLastReqWorldTime()
    return self._lastReqWorldTime
end

function FrontEndChatServer:GetLastReqCollegeTime()
    return self._lastReqCollegeTime
end

function FrontEndChatServer:AddWorldNewChat2NewestChat(chatInfo, otherChannel)
    if IsHD() then
        self:AddWorldNewChatList2NewestChat(chatInfo, otherChannel)
    elseif self._commonChatCacheStruct:CanAddWorldNewChat(chatInfo) then
        self:AddNewestChat(self._commonChatCacheStruct:BuildWorldNewChat(chatInfo, otherChannel))
    end
end

function FrontEndChatServer:AddWorldNewChatList2NewestChat(msg_list, otherChannel)
    for _,chatInfo in ipairs(msg_list) do
        if self._commonChatCacheStruct:CanAddWorldNewChat2List(chatInfo) then
            self:AddNewestChat(self._commonChatCacheStruct:BuildWorldNewChat(chatInfo, otherChannel))
        end

    end
end

function FrontEndChatServer:AddCollegeNewChat2NewestChat(chatInfo)
    self:AddWorldNewChat2NewestChat(chatInfo, ChatChannelType.CollegeChat)
end

-- 世界频道发送是否受限制
function FrontEndChatServer:WorldSendLimited()
    -- 判断是否符合世界频道发言等级限制
    local moduleId = SwitchModuleID.ModuleWorldChat
    local checkResult = Server.ModuleUnlockServer:IsModuleUnlock(moduleId)
    if not checkResult then
        local tip = Server.ModuleUnlockServer:GetModuleUnlockInfoById(moduleId).unlocktips
        LuaGlobalEvents.evtServerShowTip:Invoke(tip)
        return true
    end

    -- 判断冷却
    if self._commonChatCacheStruct:IsWorldTalkCool() then
        local remainderTime = self._commonChatCacheStruct:GetRemainderCoolTime()
        remainderTime = math.max(0, self._commonChatCacheStruct._worldCoolDefault - remainderTime)
        local tip = string.format(NSLOCTEXT("ServerTipCode", "Lua_Chat_TooQuickSend", "世界发言频率过高，请%d秒后再试"), remainderTime)
        LuaGlobalEvents.evtServerShowTip:Invoke(tip)
        return true
    end

    return false
end

-- 高校频道发送是否受限制
function FrontEndChatServer:CollegeSendLimited()
    -- 判断是否符合高校频道发言等级限制
    local moduleId = SwitchModuleID.ModuleWorldChat
    local checkResult = Server.ModuleUnlockServer:IsModuleUnlock(moduleId)
    if not checkResult then
        local tip = Server.ModuleUnlockServer:GetModuleUnlockInfoById(moduleId).unlocktips
        LuaGlobalEvents.evtServerShowTip:Invoke(tip)
        return true
    end

    -- 判断冷却
    if self._commonChatCacheStruct:IsWorldTalkCool() then
        local remainderTime = self._commonChatCacheStruct:GetRemainderCoolTime()
        remainderTime = math.max(0, self._commonChatCacheStruct._worldCoolDefault - remainderTime)
        local tip = string.format(NSLOCTEXT("ServerTipCode", "Lua_Chat_CollegeTooQuickSend", "高校发言频率过高，请%d秒后再试"), remainderTime)
        LuaGlobalEvents.evtServerShowTip:Invoke(tip)
        return true
    end

    return false
end

--发送世界频道消息
function FrontEndChatServer:ChatWorldSend(text,emojiList,chatMsgType,voice_key,duration,translated_text)
    if self:WorldSendLimited() then
        return
    end

    self._commonChatCacheStruct:EnterWorldTalkCool()

    local function f()
        local content = {
            timestamp = os.time(),
            msg_type = chatMsgType or ChatMsgType.Str,
            text = text,
            emoji_list = emojiList,
            sender_id = Server.AccountServer:GetPlayerId(),
            voice_data = chatMsgType == ChatMsgType.Voice and {
                voice_key = voice_key or "",
                duration = duration or 0,
                translated_text = translated_text or ""
            } or {}
        }

        local OnCSChatWorldSendTRes = function(res)
            local canRetCooling = true
            if res and res.result == 0 or (res.result == Err.ChatForbidden and res.forbid_info.forbid_type & ChatForbidType.ChatSilent ~= 0) then
                -- BEGIN MODIFICATION - VIRTUOS
                local function filterWorldMsgListByPlatformPrivilegeCallback(res)
                -- END MODIFICATION - VIRTUOS
                    if not table.isempty(res.msg_list) then
                        res.msg_list = self:FilterMsgList(res.msg_list, EFilterType.BlackList)
                        self:AppendWorldChatList(res.msg_list)

                        local chatInfo = res.msg_list[#res.msg_list]
                        if IsHD() then
                            self:AddWorldNewChat2NewestChat(res.msg_list)
                        else
                            self:AddWorldNewChat2NewestChat(chatInfo)
                        end
                        self:AddNewToAtList(ChatChannelType.WorldChat, res.msg_list)
                        self.Events.evtFetchWorldMsgSucceed:Invoke(
                            self.EChatMsgUpdateType.Add, self:GetWorldChatList(), #res.msg_list)
                    end

                    self.Events.evtUpdateWorldInterval:Invoke(res.load_msg_interval)
                    if res.new_room_id ~= 0 then
                        if self._roomId ~= res.new_room_id then
                            logerror(string.format("horis ChatModule: world roomId change from %d to %d", self._roomId,
                                res.new_room_id))
                        end
                        self._roomId = res.new_room_id
                        self._roomKey = res.new_room_key
                    end
                    canRetCooling = false
                end
                if PLATFORM_GEN9 == 1 then
                    self:FilterWorldMsgListByPlatformPrivilege(res, filterWorldMsgListByPlatformPrivilegeCallback)
                else
                    filterWorldMsgListByPlatformPrivilegeCallback(res)
                end
            elseif res and res.result == Err.ChatForbidden then
                local free_time = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(res.forbid_info.free_time)
                if content.msg_type == ChatMsgType.Voice then
                    local tip = string.format(ServerTipCode.ChatForbidVoice,free_time)
                    LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
                else
                    local tip = string.format(ServerTipCode.ChatForbidMsg,free_time)
                    LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
                end
            elseif res and res.result == Err.ChatWorldInvaildRoomID then
                self._roomId = 0
                self:ReqWorldRoomId()
                self.Events.evtWorldChatRoomIdInvalid:Invoke()
                Facade.ProtoManager:ManuelHandleErrCode(res)
            elseif res and res.result == Err.ChatForbiddenByCreditCheck then
                LuaGlobalEvents.evtServerShowCreditTip:Invoke(Server.FriendServer.EXinyongType.ChatLimit)
            else
                Facade.ProtoManager:ManuelHandleErrCode(res)
            end

            --清除发言
            if res.rm_speech_playerid_list and not table.isempty(res.rm_speech_playerid_list) then
                self:WorldChatRemoveSpeech(res.rm_speech_playerid_list,self._worldMsgIndex)
            end

            -- 这里非正常回包 都刷新世界聊天冷却
            if canRetCooling then
                self._commonChatCacheStruct:ClearWorldTimer()
            end
        end
        local req = pb.CSChatWorldSendTReq:New()
        req.worldchat_room_id = self._roomId
        req.key = self._roomKey
        req.content = content
        req.read_msg_index = self._worldMsgIndex
        req:Request(OnCSChatWorldSendTRes,{bShowErrTip = false})

        self._lastReqWorldTime = os.time()
    end
    if self._roomId == 0 then
        self:ReqWorldRoomId(f)
    else
        f()
    end
end

--发送高校频道消息
function FrontEndChatServer:ChatCollegeSend(text,emojiList,chatMsgType,voice_key,duration,translated_text)
    if self:CollegeSendLimited() then
        return
    end

    self._commonChatCacheStruct:EnterWorldTalkCool()

    local function f()
        local content = {
            timestamp = os.time(),
            msg_type = chatMsgType or ChatMsgType.Str,
            text = text,
            emoji_list = emojiList,
            sender_id = Server.AccountServer:GetPlayerId(),
            voice_data = chatMsgType == ChatMsgType.Voice and {
                voice_key = voice_key or "",
                duration = duration or 0,
                translated_text = translated_text or ""
            } or {}
        }

        local OnCSChatCollegeSendTRes = function(res)
            local canRetCooling = true
            if res and res.result == 0 or (res.result == Err.ChatForbidden and res.forbid_info.forbid_type & ChatForbidType.ChatSilent ~= 0) then
                -- BEGIN MODIFICATION - VIRTUOS
                local function filterCollegeMsgListByPlatformPrivilegeCallback(res)
                -- END MODIFICATION - VIRTUOS
                    if not table.isempty(res.msg_list) then
                        res.msg_list = self:FilterMsgList(res.msg_list, EFilterType.BlackList)
                        self:AppendCollegeChatList(res.msg_list)

                        local chatInfo = res.msg_list[#res.msg_list]
                        if IsHD() then
                            self:AddCollegeNewChat2NewestChat(res.msg_list)
                        else
                            self:AddCollegeNewChat2NewestChat(chatInfo)
                        end
                        self:AddNewToAtList(ChatChannelType.CollegeChat, res.msg_list)
                        self.Events.evtFetchCollegeMsgSucceed:Invoke(
                            self.EChatMsgUpdateType.Add, self:GetCollegeChatList(), #res.msg_list)
                    end

                    self.Events.evtUpdateCollegeInterval:Invoke(res.load_msg_interval)
                    if res.new_room_id ~= 0 then
                        if self._collegeRoomId ~= res.new_room_id then
                            logerror(string.format("horis ChatModule: college roomId change from %d to %d", self._collegeRoomId,
                                res.new_room_id))
                        end
                        self._collegeRoomId = res.new_room_id
                        self._collegeRoomKey = res.new_room_key
                    end
                    canRetCooling = false
                end
                if PLATFORM_GEN9 == 1 then
                    self:FilterWorldMsgListByPlatformPrivilege(res, filterCollegeMsgListByPlatformPrivilegeCallback)
                else
                    filterCollegeMsgListByPlatformPrivilegeCallback(res)
                end
            elseif res and res.result == Err.ChatForbidden then
                local free_time = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(res.forbid_info.free_time)
                if content.msg_type == ChatMsgType.Voice then
                    local tip = string.format(ServerTipCode.ChatForbidVoice,free_time)
                    LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
                else
                    local tip = string.format(ServerTipCode.ChatForbidMsg,free_time)
                    LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
                end
            elseif res and res.result == Err.ChatWorldInvaildRoomID then
                self._collegeRoomId = 0
                self:ReqCollegeRoomId()
                self.Events.evtCollegeChatRoomIdInvalid:Invoke()
                Facade.ProtoManager:ManuelHandleErrCode(res)
            elseif res and res.result == Err.ChatForbiddenByCreditCheck then
                LuaGlobalEvents.evtServerShowCreditTip:Invoke(Server.FriendServer.EXinyongType.ChatLimit)
            else
                Facade.ProtoManager:ManuelHandleErrCode(res)
            end

            --清除发言
            if res.rm_speech_playerid_list and not table.isempty(res.rm_speech_playerid_list) then
                self:WorldChatRemoveSpeech(res.rm_speech_playerid_list,self._collegeMsgIndex)
            end

            -- 这里非正常回包 都刷新高校聊天冷却
            if canRetCooling then
                self._commonChatCacheStruct:ClearWorldTimer()
            end
        end
        local req = pb.CSChatWorldSendTReq:New()
        req.worldchat_room_id = self._collegeRoomId
        req.key = self._collegeRoomKey
        req.content = content
        req.read_msg_index = self._collegeMsgIndex
        req:Request(OnCSChatCollegeSendTRes,{bShowErrTip = false})

        self._lastReqCollegeTime = os.time()
    end
    if self._collegeRoomId == 0 then
        self:ReqCollegeRoomId(f)
    else
        f()
    end
end

function FrontEndChatServer:FilterMsgList(InMsgList,FilterType)
    if FilterType == EFilterType.BlackList then
        local FilterList = {}
        for _,msg in pairs(InMsgList) do
            if not Server.FriendServer:CheckIsBlack(msg.sender.player_id) then
                table.insert(FilterList,msg)
            end
        end
        return FilterList
    end
    return InMsgList
end

function FrontEndChatServer:TriggerOpenChatWorldUI()
    local chatList = self:GetWorldChatList()
    self.Events.evtFetchWorldMsgSucceed:Invoke(
        self.EChatMsgUpdateType.Init, chatList, #chatList, true)
end

function FrontEndChatServer:TriggerOpenChatCollegeUI()
    local chatList = self:GetCollegeChatList()
    self.Events.evtFetchCollegeMsgSucceed:Invoke(
        self.EChatMsgUpdateType.Init, chatList, #chatList, true)
end
function FrontEndChatServer:GetWorldChatList()
    return self._groupCacheStruct:GetWorldChatCacheByLimit()
end

function FrontEndChatServer:GetCollegeChatList()
    return self._groupCacheStruct:GetWorldChatCacheByLimit(ChatChannelType.CollegeChat)
end

function FrontEndChatServer:ResetWorldMsgIndex()
    --重置为0，表示只拉最近的几条消息
    self._worldMsgIndex = 0
end

function FrontEndChatServer:ResetCollegeMsgIndex()
    --重置为0，表示只拉最近的几条消息
    self._collegeMsgIndex = 0
end

function FrontEndChatServer:AppendWorldChatList(msg_list, insertedMsgList, otherChannel) --repeated GroupChatMsg
    if table.isempty(msg_list) then
        return
    end

    insertedMsgList = setdefault(insertedMsgList, nil)
    otherChannel = setdefault(otherChannel, nil)
    local channelValue = (otherChannel == nil) and ChatChannelType.WorldChat or otherChannel

    local chatList = self._groupCacheStruct:GetWorldChatCache(channelValue)

    local function AddNewMsg(msgValue)
        if msgValue.content.index == 0 then --静默禁言下，收到的回报index会是0
            msgValue.content.index = (otherChannel == ChatChannelType.CollegeChat) and self._collegeMsgIndex or self._worldMsgIndex
        elseif otherChannel == ChatChannelType.CollegeChat then
            self._collegeMsgIndex = msgValue.content.index
        else
            self._worldMsgIndex = msgValue.content.index
        end
        table.insert(chatList, msgValue)
    end

    if table.isempty(chatList) then
        for _,groupChatMsg in ipairs(msg_list) do
            AddNewMsg(groupChatMsg)
            if insertedMsgList then
                table.insert(insertedMsgList, groupChatMsg)
            end
        end
    else
        for _,groupChatMsg in ipairs(msg_list) do
            if chatList[#chatList].content.timestamp < groupChatMsg.content.timestamp
                or chatList[#chatList].content.index < groupChatMsg.content.index then
                AddNewMsg(groupChatMsg)
                if insertedMsgList then
                    table.insert(insertedMsgList, groupChatMsg)
                end
            end
        end
    end
end

function FrontEndChatServer:AppendCollegeChatList(msg_list, insertedMsgList)
    insertedMsgList = setdefault(insertedMsgList, nil)
    self:AppendWorldChatList(msg_list, insertedMsgList, ChatChannelType.CollegeChat)
end

--------------------------------------------------------------------------------------------------------


----------------------------------------新消息ntf------------------------------

function FrontEndChatServer:SetChatChanelEnable(bEnable)
    if not IsHD() then
        return
    end

    if  self._commonChatCacheStruct then
        self._commonChatCacheStruct:SetChatChanelEnable(bEnable)
    end
end

function FrontEndChatServer:SetCurSelectedChannel_PC(channel)
    if not IsHD() then
        return
    end

    if  self._commonChatCacheStruct then
        self._commonChatCacheStruct:SetCurSelectedChannel_PC(channel)
    end
end

function FrontEndChatServer:ShouldResetLastPrivateUnread(curPlayer, playerId)
    if IsHD() and self._commonChatCacheStruct:GetCurSelectedChannel_PC() == ChatChannelType.PrivateChatFriend and self._commonChatCacheStruct:GetChatChanelEnable() then
        return true
    end

    if not IsHD() and curPlayer and curPlayer.player_id == playerId then
        return true
    end

    return false
end

--收到新消息
function FrontEndChatServer:_OnCSChatMsgNtf(ntf) --CSChatMsgNtf
    local selfPlayerId = Server.AccountServer:GetPlayerId()
    loginfo("收到新消息！！！")
    if PLATFORM_GEN9 == 1 and Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
        loginfo("局内收到新消息！！！")
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if PLATFORM_GEN9 == 1 and Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game and
        ClientComminicateSetting.bOpenTextChatInGame == false then
        -- 局内文字聊天关闭
        return
    end
    local function handleCSChatMsgNtf()
    -- END MODIFICATION - VIRTUOS
        for _, chatInfo in ipairs(ntf.private_chat_sumys) do
            local playerId = chatInfo.player_info.player_id
            local channel = chatInfo.is_friend and ChatChannelType.PrivateChatFriend or ChatChannelType.PrivateChatStanger
            if playerId ~= selfPlayerId then
                -- BEGIN MODIFICATION - VIRTUOS
                -- 广播一条 PlayerInfo 消息
                if playerId ~= Server.AccountServer:GetPlayerId() then
                    self.Events.evtReceivePlayerInfoFromChat:Invoke(chatInfo.player_info)
                end
                -- END MODIFICATION - VIRTUOS
                if IsHD() then
                    self._privateChatStruct:AppendMsgForSingleCache_PC(channel, playerId, chatInfo.latest_msg)
                    self.Events.evtReceiveNewChatPrivate:Invoke(playerId, chatInfo.latest_msg, chatInfo.player_info)
                --没拉取聊天记录前，不缓存新消息
                elseif self._privateChatStruct:GetSingleChatCache(channel, playerId) then
                    self._privateChatStruct:AppendMsgForSingleCache(channel, playerId, chatInfo.latest_msg)
                    self.Events.evtReceiveNewChatPrivate:Invoke(playerId, chatInfo.latest_msg)
                end
            end
            self._privateSummyStruct:AddPrivateLatestData(channel, playerId, chatInfo)
            self:TopPlayer(playerId)
            local curPlayer = self._chatPlayerInfo
            -- if curPlayer and curPlayer.player_id == playerId then
            if self:ShouldResetLastPrivateUnread(curPlayer, playerId) then
                self._privateSummyStruct:ResetLatestUnreadById(channel, playerId)
            else
                self._privateSummyStruct:AdjustUnreadNumByChannel(channel, 1)
				if  self._commonChatCacheStruct then
                	local curChannel = self._commonChatCacheStruct:GetCurSelectedChannel_PC()
                	if curChannel == ChatChannelType.MultiChat then
                    	self:ResetUnredChat()
                	end
            	end
                self.Events.evtPrivateUnreadNumChange:Invoke()
            end
        end

        for _, chatInfo in ipairs(ntf.group_chat_sumys) do
            if self._groupCacheStruct:AddGroupCacheByChannel(
                    chatInfo.channel_type, chatInfo.latest_msg) then
                self.Events.evtReceiveNewChatGroup:Invoke(ChatChannelType.TeamChat, chatInfo.latest_msg)
            end

            if chatInfo.channel_type == ChatChannelType.TeamChat then
                self._privateSummyStruct:AddTeamLatestData(chatInfo.latest_msg.content)
                if self._bCountTeamUnreadNum then
                    self._privateSummyStruct:AdjustUnreadNumByChannel(ChatChannelType.TeamChat, 1)
                    self.Events.evtTeamUnreadNumChange:Invoke()
                end
            end
        end
        self:AddNewToAtList(ChatChannelType.WorldChat, ntf.group_chat_sumys)

        local newestChat = {}
        --私聊摘要
        for _, chatInfo in ipairs(ntf.private_chat_sumys) do
            local channel = chatInfo.is_friend and ChatChannelType.PrivateChatFriend or ChatChannelType.PrivateChatStanger
            if table.isempty(newestChat) then
                newestChat.channel = channel
                newestChat.sender = chatInfo.player_info
                newestChat.content = chatInfo.latest_msg
            else
                if newestChat.content.timestamp < chatInfo.latest_msg.timestamp then
                    newestChat.channel = channel
                    newestChat.sender = chatInfo.player_info
                    newestChat.content = chatInfo.latest_msg
                end
            end
        end
        --群聊摘要
        for _, chatInfo in ipairs(ntf.group_chat_sumys) do
            if table.isempty(newestChat) then
                newestChat.channel = chatInfo.channel_type
                newestChat.sender = chatInfo.latest_msg.sender
                newestChat.content = chatInfo.latest_msg.content
            else
                if newestChat.content.timestamp < chatInfo.latest_msg.content.timestamp then
                    newestChat.channel = chatInfo.channel_type
                    newestChat.sender = chatInfo.latest_msg.sender
                    newestChat.content = chatInfo.latest_msg.content
                end
            end
        end
        if not table.isempty(newestChat) then
            self:AddNewestChat(newestChat)
        end
    end
    
    -- BEGIN MODIFICATION - VIRTUOS
    -- 对ntf进行过滤
    if PLATFORM_GEN9 == 1 then
        if Server.ChatServer.hasCommunicationPrivilege == false then
            -- 没有通信权限，屏蔽所有消息
            return
        end
        local privateChatSumys = {}
        local groupChatSumys = {}
        local privateChatSumysFromCurPlatform = {}
        local groupChatSumysFromCurPlatform = {}
        local senderIdListFromCurPlatform = {}
        for _, privateChatInfo in ipairs(ntf.private_chat_sumys) do
            local senderId = privateChatInfo.player_info.player_id
            local senderPlat = privateChatInfo.player_info.plat
            if senderPlat ~= Server.AccountServer:GetPlatIdType() then
                Server.ChatServer:CheckAnonymousUserCommunicationPermissions(senderId, function(isAllowed)
                    if isAllowed then
                        table.insert(privateChatSumys, privateChatInfo)
                    end
                end)
            else
                table.insert(privateChatSumysFromCurPlatform, privateChatInfo)
                if table.find(senderIdListFromCurPlatform, function(v, k) return v == senderId end) == nil then
                    table.insert(senderIdListFromCurPlatform, senderId)
                end
            end
        end

        for _, groupChatInfo in ipairs(ntf.group_chat_sumys) do
            local senderId = groupChatInfo.latest_msg.sender.player_id
            local senderPlat = groupChatInfo.latest_msg.sender.plat
            if senderPlat ~= Server.AccountServer:GetPlatIdType() then
                Server.ChatServer:CheckAnonymousUserCommunicationPermissions(senderId, function(isAllowed)
                    if isAllowed then
                        table.insert(groupChatSumys, groupChatInfo)
                    end
                end)
            else
                table.insert(groupChatSumysFromCurPlatform, groupChatInfo)
                if table.find(senderIdListFromCurPlatform, function(v, k) return v == senderId end) == nil then
                    table.insert(senderIdListFromCurPlatform, senderId)
                end
            end
        end

        local function filterPlatformMsgCallback(allowedUserList)
            for _, privateChatInfo in ipairs(privateChatSumysFromCurPlatform) do
                local allowedId = table.find(allowedUserList, function(v,k) return v == ULuautils.GetUInt64String(privateChatInfo.player_info.player_id) end)
                if allowedId ~=nil then
                    table.insert(privateChatSumys, privateChatInfo)
                end
            end

            for _, groupChatInfo in ipairs(groupChatSumysFromCurPlatform) do
                local allowedId = table.find(allowedUserList, function(v,k) return v == ULuautils.GetUInt64String(groupChatInfo.latest_msg.sender.player_id) end)
                if allowedId then
                    table.insert(groupChatSumys, groupChatInfo)
                end
            end

            ntf.private_chat_sumys = privateChatSumys
            ntf.group_chat_sumys = groupChatSumys
            handleCSChatMsgNtf()
        end

        if #senderIdListFromCurPlatform == 0 then
            ntf.private_chat_sumys = privateChatSumys
            ntf.group_chat_sumys = groupChatSumys
            handleCSChatMsgNtf()
        else
            Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText, true, senderIdListFromCurPlatform, filterPlatformMsgCallback)
        end
    else
        handleCSChatMsgNtf()
    end
    -- END MODIFICATION - VIRTUOS
end

--私聊频道排序用
function FrontEndChatServer:GetLastestChatTime(playerId,bFriend)
    return self._privateSummyStruct:GetLastestChatTime(playerId, bFriend)
end

--获取对应玩家的私聊摘要
function FrontEndChatServer:GetPrivateChatSummary(playerId,bFriend)
    return self._privateSummyStruct:GetPrivateChatSummary(playerId, bFriend)
end

--获取最新的一条聊天记录
function FrontEndChatServer:GetNewestChat()
    return self._commonChatCacheStruct:GetNewestChat()
end

--获取聊天记录,仅限PC
function FrontEndChatServer:GetChatHistory()
    return self._commonChatCacheStruct:GetChatHistory()
end

--------------------------------------- 依赖信用系统的逻辑 ------------------------------------
-- 查询本地玩家语音权限是否允许
function FrontEndChatServer:ReqTSSPlayerQueryScnLimitReq(outCallback)
    local OnCSTSSPlayerQueryScnLimitRes = function(res)
        local callbackResult = false
        if res and res.result == Err.TSSScnLimitQueryFail then
            -- 这种情况下意味着权限是被信用系统毙了的？
            callbackResult = false
        else
            callbackResult = true
        end

        if outCallback then
            outCallback(callbackResult)
        end
    end

    local req = pb.CSTSSPlayerQueryScnLimitReq:New()
    req.player_id = Server.AccountServer:GetPlayerId()
    req.credit_scn_type = EnumTSSCreditScnLimitType.Chat
    req:Request(OnCSTSSPlayerQueryScnLimitRes, {bShowErrTip = false})
end
--end
-----------------------------------------------------------------------

--------------------------------------- 一些玩家基本信息的拉取 ------------------------------------
-- 拉取玩家的军牌数据
function FrontEndChatServer:ReqPlayerMilitaryTag(outCallback, playerId)
    local OnCSGetMilitaryTag = function(res)
        if res and res.result == 0 then
            local playerInfo = res.info
            if outCallback and playerInfo then
                outCallback(playerInfo.military_tag)
            end
        end
    end

    local req = pb.CSPlayerGetBasicInfoReq:New()
    req.player_id = playerId or Server.AccountServer:GetPlayerId()
    req:Request(OnCSGetMilitaryTag, {bShowErrTip = false})
end
--end
-----------------------------------------------------------------------

--------------------------------------- 分享模块的一些协议 ------------------------------------

-- 判断是否可以领取首次分享奖励
function FrontEndChatServer:CanTriggerFirstRewardGet(outCallback, rewardId)
    local OnCSShareRewardTakeStatus = function(res)
        if res and res.result == 0 then

            if not res.left_take_cnt then
                return
            end
            local canGet = (res.left_take_cnt > 0)
            if outCallback then
                outCallback(canGet)
            end
        end
    end

    local req = pb.CSShareRewardTakeStatusReq:New()
    req.share_reward_id = rewardId
    logerror("FrontEndChatServer:CanTriggerFirstRewardGet", req)
    req:Request(OnCSShareRewardTakeStatus, {bShowErrTip = false})
end

-- 请求领取首次分享奖励
function FrontEndChatServer:ReqGetFirstShareReward(outCallback, rewardId)
    local OnCSShareRewardTakeRes = function(res)
        if res and res.result == 0 then
            if outCallback and res.reward_list then
                outCallback(res.reward_list)
            end
        end
    end

    -- -- sol为1 全面战场为2
    -- local rewardId = 1
    -- local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    -- if curGameFlow ~= EGameFlowStageType.SafeHouse then
    --     rewardId = 2
    -- end

    local req = pb.CSShareRewardTakeReq:New()
    req.share_reward_id = rewardId
    logerror("FrontEndChatServer:ReqGetFirstShareReward", req)
    req:Request(OnCSShareRewardTakeRes, {bShowErrTip = false})
end

-- 分享经分上报
function FrontEndChatServer:ReqReportShareLog(shareTypeId, buttonType, extraInfo)
    if extraInfo then
        extraInfo = tostring(extraInfo)
    else
        extraInfo = ""
    end

    local OnClientShareReportRes = function(res)
        local a = 1
    end


    local req = pb.CSClientShareReportNtf:New()
    req.share_type_id = shareTypeId
    req.button_type = buttonType
    req.extra_info = extraInfo
    req:Request(OnClientShareReportRes, {bShowErrTip = false, iDefaultSendNum = 0})
end


--end
-----------------------------------------------------------------------

------------------------------------------特殊功能@------------------------------
function FrontEndChatServer:AddNewToAtList(channel,newList)
    local hasNewAt = false
    for _,new in ipairs(newList) do
        local chatMsg = nil
        if new.channel_type then    --new为ChatMsgSummaryGroup
            channel = new.channel_type
            chatMsg = new.latest_msg
        else                        --new为GroupChatMsg
            chatMsg = new
        end
        if chatMsg then
            local bAted = false
            string.gsub(chatMsg.content.text, '<Chat_At2>%[@.-%]</>', function (word)
                local nick = string.sub(word,13, string.len(word) - 4)
                if nick == Server.RoleInfoServer.nickName then
                    bAted = true
                end
            end)
            if bAted then
                local index = self:FindIndexInChannel(channel,chatMsg)
                chatMsg.index = index
                self._privateSummyStruct:InsertMsgByChannel(channel, chatMsg)
                hasNewAt = true
            end
        end
    end
    if hasNewAt then
        local atMsg = self._privateSummyStruct:GetMsgByChannel(channel)
        self.Events.evtReceiveNewAt:Invoke(channel, atMsg)
        self.Events.evtAtNumChange:Invoke()
    end
end

function FrontEndChatServer:FindIndexInChannel(channel,chatMsg)
    return self._groupCacheStruct:FindIndexInChannel(channel,chatMsg)
end

function FrontEndChatServer:GetAtChatMsgByChannel(channel)
    return self._privateSummyStruct:GetMsgByChannel(channel)
end

function FrontEndChatServer:RemoveFirstAt(channel)
    if self._privateSummyStruct:RemoveFirstAt(channel) then
        self.Events.evtAtNumChange:Invoke()
    end
end

function FrontEndChatServer:GetAtNumByChannel(channel)
    return self._privateSummyStruct:GetAtNumByChannel(channel)
end

function FrontEndChatServer:ClearAtChatMsg(channel)
    self._privateSummyStruct:ClearAtChatMsg(channel)
    self.Events.evtAtNumChange:Invoke()
end

function FrontEndChatServer:HasNewAtChatMsg()
    return self._privateSummyStruct:HasNewAtChatMsg()
end
-----------------------------------------------------------------------

--添加消息到 _newestChat
function FrontEndChatServer:AddNewestChat(chatInfo, bReset)
    bReset = setdefault(bReset,false)
    self._commonChatCacheStruct:AddNewestChat(chatInfo)
    self.Events.evtReceiveNewChat:Invoke(bReset)
end

--聊天设置
function FrontEndChatServer:ForbiddenStrangerChat(forbidden)
    local req = pb.CSChatStrangerChatSwitchReq:New()
    req.is_ban_stranger_chat = forbidden
    req:Request()
end

function FrontEndChatServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self._commonChatCacheStruct:Destroy()
end

--聊天禁言
function FrontEndChatServer:ResolveForbidInfo(ForbidInfo)
    local forbid_type = {
        ["NotForbid"] = ForbidInfo.forbid_type == 0,
        ["Silent"] = ForbidInfo.forbid_type & ChatForbidType.ChatSilent,
        ["ForbidMsg"] = ForbidInfo.forbid_type & ChatForbidType.ChatForbidMsg,
        ["ForbidVoice"] = ForbidInfo.forbid_type & ChatForbidType.ChatForbidVoice,
    }
    ForbidInfo.forbid_type = forbid_type
    return ForbidInfo
end

--清除玩家发言（私聊和队伍）
function FrontEndChatServer:_OnCSChatRemoveSpeechNtf(ntf) --CSChatRemoveSpeechNtf
    local OnClearAndReqChatList = function(channelValue, IdValue, isFriend)
        if self._privateChatStruct:ResetSingleChatCache(channelValue, IdValue) then
            self:ReqPrivateChannelChatList(IdValue, nil, isFriend)
        end
    end

    local OnBatchClearAndReqChatList = function(channelValue, isFriend)
        for Id,_ in pairs(self._privateChatStruct:GetChannelChatCache(channelValue)) do
            if self._privateChatStruct:ResetSingleChatCache(channelValue, Id) then
                self:ReqPrivateChannelChatList(Id, nil, isFriend)
            end
        end
    end

    for _,playerId in pairs(ntf.player_id_list) do
        --清除并重拉私聊记录
        if playerId == Server.AccountServer:GetPlayerId() then
            local channel = ChatChannelType.PrivateChatFriend
            OnBatchClearAndReqChatList(channel, true)
            channel = ChatChannelType.PrivateChatStanger
            OnBatchClearAndReqChatList(channel, false)
        else
            local channel = ChatChannelType.PrivateChatFriend
            OnClearAndReqChatList(channel, true)
            channel = ChatChannelType.PrivateChatStanger
            OnClearAndReqChatList(channel, false)
        end

        --清除并重拉队伍聊天记录
        self._groupCacheStruct:ResetTeamCache()
        self:ReqTeamChannelChatList()

        --清除最新一条私聊/队伍记录
        self._privateSummyStruct:ResetAllLatestData()

        --清除最新的一条消息
        if self._commonChatCacheStruct:CanRemoveNewChatPrivateAndTeam(playerId) then
            self:AddNewestChat(self._commonChatCacheStruct:BuildEmptyNewChat(), true)
        end

        --清除队伍频道At记录
        self._privateSummyStruct:RemoveTeamAtMsgById(playerId)
    end
    --重新请求摘要
    self:ReqAllChannelMsgSumy()

    self.Events.evtChatRemoveSpeech:Invoke(ntf.player_id_list)
end

--清除玩家发言（世界）
function FrontEndChatServer:WorldChatRemoveSpeech(playerIdList,msgIndex)
    local bNeedUpdate = false
    for _,playerId in pairs(playerIdList) do
        if not self._worldRemoveSpeechList[playerId] or self._worldRemoveSpeechList[playerId] < msgIndex then
            if self._groupCacheStruct:RemoveWorldCacheById(playerId) then
                self.Events.evtFetchWorldMsgSucceed:Invoke(
                    self.EChatMsgUpdateType.Remove, self:GetWorldChatList(), 1)  -- 1为默认参数，划到底部
            end

            --清除最新的一条消息
            if self._commonChatCacheStruct:CanRemoveNewChatWorld(playerId) then
                self:AddNewestChat(self._commonChatCacheStruct:BuildEmptyNewChat(), true)
            end

            --清除世界频道At记录
            self._privateSummyStruct:RemoveWorldAtMsgById(playerId)

            bNeedUpdate = true
            self._worldRemoveSpeechList[playerId] = msgIndex
        end
    end

    if bNeedUpdate then
        self._privateSummyStruct:ResetWorldUnreadData()
        self.Events.evtWorldUnreadNumChange:Invoke()
        self.Events.evtWorldChatRemoveSpeech:Invoke()
    end
end

return FrontEndChatServer
