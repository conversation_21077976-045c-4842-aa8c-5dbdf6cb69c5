----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class TeamMemberInfoItem_Gamepad : LuaUIBaseView
local TeamMemberInfoItem_Gamepad = ui("TeamMemberInfoItem_Gamepad")
local SocialLogic = require "DFM.Business.Module.SocialModule.Logic.SocialLogic"
local RankIconAbbr = require "DFM.Business.Module.RankingModule.UI.RankIconAbbr"

function TeamMemberInfoItem_Gamepad:Ctor()
    self._wtBtn = self:Wnd("wtBtn", UIButton)
    self._wtBtn:Event("OnClicked", self._ShowInfo, self)
    --self._wtBtn:Event("OnFocusReceivedEvent", self._OnReceivedFocus, self)
    --self._wtBtn:Event("OnFocusLostEvent", self._OnLostFocus, self)
    self:SetCppValue("bIsFocusable", true)
    self:Event("OnFocusReceivedEvent", self._OnReceivedFocus, self)
    self:Event("OnFocusLostEvent", self._OnLostFocus, self)
    self._wtPlayerNameTxt = self:Wnd("wtPlayerNameTxt", UITextBlock)
    self._wtRankDivisionIcon = self:Wnd("wtRankDivisionIcon", RankIconAbbr)
    self._wtTeamMemberInfoPanel = self:Wnd("wtTeamMemberInfoPanel", UIWidgetBase)
    self._wtPlayerHeadIcon = self:Wnd("wtPlayerHeadIcon", CommonHeadIcon)
    self._wtMilitaryTagImg = self:Wnd("wtMilitaryTagImg", UIImage)
    self._wtImage_Invisibility = self:Wnd("DFImage_Invisibility", UIImage)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function TeamMemberInfoItem_Gamepad:OnInitExtraData(params)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function TeamMemberInfoItem_Gamepad:OnOpen()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function TeamMemberInfoItem_Gamepad:OnClose()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function TeamMemberInfoItem_Gamepad:OnAnimFinished(anim)
end

function TeamMemberInfoItem_Gamepad:InitPlayerInfo(playerSimpleInfo)
    self._playerSimpleInfo = playerSimpleInfo
    self._wtRankDivisionIcon:SetRankIconNone()
    self._wtRankDivisionIcon:SelfHitTestInvisible()
    if playerSimpleInfo ~= nil then
        self._wtTeamMemberInfoPanel:SelfHitTestInvisible()
        local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if curGameFlow == EGameFlowStageType.SafeHouse then
            playerSimpleInfo.level = playerSimpleInfo.season_lvl
        else
            playerSimpleInfo.level = playerSimpleInfo.level
        end
        if Server.AccountServer:GetPlayerId() == playerSimpleInfo.player_id then
            self._wtPlayerHeadIcon:InitPortrait(playerSimpleInfo, HeadIconType.HeadPerInformat)

            -- 我自己是隐身状态，显示隐身状态图标
            if Module.Friend:GetSelfLoginInvisible() then
                self._wtImage_Invisibility:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            else
                self._wtImage_Invisibility:Collapsed()
            end
        else
            btnTbl =  {
                HeadButtonType.PlayerInformat,
                HeadButtonType.AddFriend,
            }
            if Server.TeamServer:IsCaptial() == true and Server.TeamServer:IsMember(playerSimpleInfo.player_id) ~= nil then
                table.insert(btnTbl, HeadButtonType.KickTeam)
                table.insert(btnTbl, HeadButtonType.PromotTeam)
            end
            if Server.TeamServer:FindMember(playerSimpleInfo.player_id) ~= nil then
                table.insert(btnTbl, HeadButtonType.ReportVoice)
            end
            self._wtPlayerHeadIcon:InitPortrait(playerSimpleInfo, HeadIconType.HeadList, btnTbl, Server.TeamServer:FindMember(playerSimpleInfo.player_id) and FriendApplySource.TeamRelationshipApply or FriendApplySource.OtherApply)
        end 
        self._wtMilitaryTagImg:AsyncSetImagePath(Module.RoleInfo:GetMilitary(playerSimpleInfo.military_tag), true)
        local playerName = playerSimpleInfo.nick_name
        if playerSimpleInfo.nick_name then
            local remarkName = Server.FriendServer:GetFriendRemarkById(playerSimpleInfo.player_id)
            if remarkName ~= "" then
                playerName = string.format(Module.Friend.Config.QQFriend, remarkName, playerSimpleInfo.nick_name)
            else
                playerName = playerSimpleInfo.nick_name
            end
        end
        self._wtPlayerNameTxt:SelfHitTestInvisible()
        self._wtPlayerNameTxt:SetText(playerName)
        --- BEGIN MODIFICATION @ VIRTUOS
        -- TRC: using Platform Online ID as player name.
        if IsPS5() then
            local callback = function(onlineID)
                self._wtPlayerNameTxt:SetText(onlineID)
            end
            Module.Social:AsyncGetPS5OnlineIdByUID(playerSimpleInfo.player_id, callback, self)
        end

        if IsConsole() and self._wtPlatformIcon then
            local platIconPath = Module.Friend:GetPlatformIconPath(playerSimpleInfo.plat)
            if platIconPath then
                self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
                self._wtPlatformIcon:SelfHitTestInvisible()
            else
                self._wtPlatformIcon:Collapsed()
            end
        end
        --- END MODIFICATION
        if SocialLogic.IsInMp() == true then
            if playerSimpleInfo.mp_rank_attended == true then
                local targetScore = (playerSimpleInfo.show_commander_rank_points and playerSimpleInfo.show_commander_rank_points > 0) and playerSimpleInfo.mp_commander_score or playerSimpleInfo.mp_rank_score
                self._wtRankDivisionIcon:SetTournamentIconByScore(targetScore)
            else
                self._wtRankDivisionIcon:SetRankIconNone()
            end
        else
            if playerSimpleInfo.sol_rank_attended == true then
                self._wtRankDivisionIcon:SetRankingIconByScore(playerSimpleInfo.sol_rank_score)
            else
                self._wtRankDivisionIcon:SetRankIconNone()
            end
        end
    else
        self._wtTeamMemberInfoPanel:Collapsed()
        self._wtPlayerNameTxt:SetText(Module.Social.Config.Loc.NotInvite)
        self._wtRankDivisionIcon:Collapsed()
        self._wtTeamMemberInfoPanel:Collapsed()
        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() and self._wtPlatformIcon then
            self._wtPlatformIcon:Collapsed()
        end
        --- END MODIFICATION
    end 
end

function TeamMemberInfoItem_Gamepad:_ShowInfo()
    if self._playerSimpleInfo then
        if Server.AccountServer:GetPlayerId() == self._playerSimpleInfo.player_id then
            Module.RoleInfo:ShowMainPanel(self._playerSimpleInfo.player_id)
        else
            self._wtPlayerHeadIcon:ActiveClick() 
        end
    elseif self._clickedCallback then
        self._clickedCallback()
    end
end

function TeamMemberInfoItem_Gamepad:_OnReceivedFocus()
    if self._receivedFocusCallback then
        self._receivedFocusCallback(self._playerSimpleInfo)
    end
end

function TeamMemberInfoItem_Gamepad:_OnLostFocus()
    if self._lostFocusCallback then
        self._lostFocusCallback(self._playerSimpleInfo)
    end
end

function TeamMemberInfoItem_Gamepad:BindClickCallback(fCallback)
    self._clickedCallback = fCallback
end

function TeamMemberInfoItem_Gamepad:BindReceivedFocusCallback(fCallback)
    self._receivedFocusCallback = fCallback
end

function TeamMemberInfoItem_Gamepad:BindLostFocusCallback(fCallback)
    self._lostFocusCallback = fCallback
end

return TeamMemberInfoItem_Gamepad
