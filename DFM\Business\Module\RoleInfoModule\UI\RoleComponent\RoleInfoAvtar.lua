----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------



local RoleInfoAvtar = ui("RoleInfoAvtar")

function RoleInfoAvtar:Ctor()
    self._wtSocialImage = self:Wnd("wtSocialImage", UIImage)
    self._wtSeclectBtn = self:Wnd("wtSeclectBtn", UIWidgetBase)
    self._wtSeclectBtn:Event("OnClicked", self._SelectOnClicked, self)
    self._wtRoot = self:Wnd("wtRoot", UIWidgetBase)
    self._wtDownTime = self:Wnd("wtDownTime", UIWidgetBase)
    self._wtDownTimePanel = self:Wnd("wtDownTimePanel", UIWidgetBase)
    self._selectId = nil
    self._lockIns = nil
    self._selectIns = nil
    self._usingIns = nil
end

function RoleInfoAvtar:OnInitAvatarData(info)
    local NowTime = Facade.ClockManager:GetServerTimestamp()
    self._info = info
    if info.ResourceName then
        self._wtSocialImage:AsyncSetImagePath(info.ResourceName, true)
        if info.AvatarType == 2 then
            local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName("UICustomized")
            if not bDownloaded then
                self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,
                    self._OnDownloadStateChange, self)
                self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,
                    self._ModuleDownloadResult, self)
            end
        end
    end

    local reddotHashTbl = Server.RoleInfoServer:GetSocialReddotHash()

    if info.Islock then
        if reddotHashTbl and reddotHashTbl[info.AvatarID] then
            self:OnLockItem(true)
        elseif info.SocialType ~= LimitSocialTypeDef.NONE and info.EndTime < NowTime then
            self:OnLockItem()
        else
            self:RemoveLockItem()
        end
    else
        self:OnLockItem()
    end

    if info.SocialType ~= LimitSocialTypeDef.NONE and info.EndTime > NowTime and info.Islock then
        self._wtDownTimePanel:Visible()
        self:SetTextEndTime(info.EndTime)
    else
        self._wtDownTimePanel:Collapsed()
    end
end

function RoleInfoAvtar:_OnDownloadStateChange(moduleName, bDownloaded)
    if moduleName == "UICustomized" and bDownloaded then
        self._wtSocialImage:AsyncSetImagePath(self._info.ResourceName, true)
        self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged)
    end
end

function RoleInfoAvtar:_ModuleDownloadResult(moduleName, bSuccess, errorCode, bShowTips)
    if moduleName == "UICustomized" and bSuccess then
        self._wtSocialImage:AsyncSetImagePath(self._info.ResourceName, true)
        self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    end
end

function RoleInfoAvtar:SetTextEndTime(EndTime)
    local NowTime = Facade.ClockManager:GetServerTimestamp()
    local TimeLeft = EndTime - NowTime
    local Loc = Module.RoleInfo.Config.Loc

    if TimeLeft > 86400 then
        self._wtDownTime:SetText(string.format(Loc.SocialLeftDay, math.floor(TimeLeft / 86400)))
    elseif TimeLeft > 3600 then
        self._wtDownTime:SetText(string.format(Loc.SocialLeftHour, math.floor(TimeLeft / 3600)))
    else
        self._wtDownTime:SetText(Loc.SocialLeftOneHour)
    end
end

function RoleInfoAvtar:SetParent(parent, index)
    self._parent = parent
    self._index = index
end

function RoleInfoAvtar:GetIndex()
    return self._index
end

function RoleInfoAvtar:SetUrlImage()
    if self._info.url ~= "" then
        local callback = CreateCallBack(self._SetUrlImage, self)
        Module.Social:SetProfileFromUrl(self._info.url, callback, true, true)
    else
        self._wtSocialImage:AsyncSetImagePath(self._info.ResourceName, true)
    end
end

function RoleInfoAvtar:_SetUrlImage(ProfileImg)
    if not ResConvertUtil.IsEmptyImage(ProfileImg) then
        if self._wtSocialImage and self._wtSocialImage.SetBrush then
            self._wtSocialImage:SetBrush(ProfileImg.Brush)
        end
    end
end

function RoleInfoAvtar:_SelectOnClicked()
    self._parent:SetSelectWidget(self, self._index)
    self:OnSelectAvatar()
end

function RoleInfoAvtar:OnSelectAvatar()
    if not self._selectId then
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.SelectedComponent, self._wtRoot)
        self._selectId = instanceId
        local newCell = getfromweak(weakUIIns)
        if newCell then
            UIUtil.SetWidgetToParent_Full(newCell, self._wtRoot)
            newCell:SelfHitTestInvisible()
        end
    else
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.SelectedComponent)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:SelfHitTestInvisible()
        end
    end
end

function RoleInfoAvtar:RemoveSelectUI()
    if self._selectId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.SelectedComponent, self._selectId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:Collapsed()
        end
    end
end

function RoleInfoAvtar:OnUsingAvatar()
    if not self._usingId then
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.UsingComponent, self._wtRoot)
        self._usingId = instanceId
        local newCell = getfromweak(weakUIIns)
        if newCell then
            UIUtil.SetWidgetToParent_Full(newCell, self._wtRoot)
            newCell:SelfHitTestInvisible()
        end
    else
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.UsingComponent)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:SelfHitTestInvisible()
        end
    end
end

function RoleInfoAvtar:RemoveUsingUI()
    if self._usingId then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.UsingComponent, self._usingId)
        local newCell = getfromweak(weakUIIns)
        if newCell then
            newCell:Collapsed()
        end
    end
end

function RoleInfoAvtar:OnLockItem(bPlayAnimation)
    local wtLockComp
    if not self._lockId then
        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.LockComponent, self._wtRoot)
        self._lockId = instanceId
        wtLockComp = getfromweak(weakUIIns)
        if wtLockComp then
            UIUtil.SetWidgetToParent_Full(wtLockComp, self._wtRoot)
            wtLockComp:SelfHitTestInvisible()
        end
    end

    if bPlayAnimation and self._lockId ~= nil then
        local weakUIIns = Facade.UIManager:GetSubUI(self, UIName2ID.LockComponent, self._lockId)
        wtLockComp = getfromweak(weakUIIns)
        if wtLockComp then
            local ani = wtLockComp.WBP_SlotCompMaskSmallLock_Unlock
            wtLockComp:StopAnimation(ani)
            wtLockComp:PlayAnimation(ani, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
    end
end

function RoleInfoAvtar:RemoveLockItem()
    if self._lockId then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.LockComponent, self._lockId)
        self._lockId = nil
    end
end

return RoleInfoAvtar
