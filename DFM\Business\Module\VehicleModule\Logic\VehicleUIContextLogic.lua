----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMVehicle)
----- LOG FUNCTION AUTO GENERATE END -----------



local VehicleUIParam = require "DFM.Business.DataStruct.VehicleStruct.VehicleUIParam"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"

local UVehicleFrontendSubsystem = import "VehicleFrontendSubsystem"

local VehicleUIContextLogic = {}

VehicleUIContextLogic.ENABLE_DEBUG_CONTEXT = false

function VehicleUIContextLogic.ProcessContextFromUIParam(uiParam, bProcessContext)
    if uiParam == nil then
        uiParam = VehicleUIParam:NewIns()
    end

    local uiContext = VehicleUIContextLogic.GetUIContext()
    uiContext:SetUIParam(uiParam)

    bProcessContext = setdefault(bProcessContext, true)
    if not bProcessContext then
        return
    end
    VehicleUIContextLogic.ProcessContext()
end

function VehicleUIContextLogic.ProcessContext()
    local processor = VehicleUIContextLogic.GetContextProcessor()
    processor:OverrideContext()
end

function VehicleUIContextLogic.ProcessRenderSceneObject()
    local vehicleContext = VehicleUIContextLogic.GetVehicleFrontendContextFromFrontend()
    local bIsValid = isvalid(vehicleContext)
    if not bIsValid then
        logerror("VehicleUIContextLogic.ProcessRenderSceneObject vehicleContext is nil")
        return
    end
    Module.Vehicle:SetQualityEnv(vehicleContext:GetSkinID())
    local vehicleFrontendSubsystemInst = VehicleUIContextLogic.GetUVehicleFrontendSubsystem()
    local bIsValid = isvalid(vehicleFrontendSubsystemInst)
    if not bIsValid then
        logerror("VehicleUIContextLogic.ProcessRenderSceneObject vehicleFrontendSubsystemInst is nil")
        return
    end
    vehicleFrontendSubsystemInst:LoadVehicle(vehicleContext)
end

function VehicleUIContextLogic.AddPartNode(itemID, itemGUID, bUnlock)
    local processor = VehicleUIContextLogic.GetContextProcessor()
    processor:SyncAddPartNode(itemID, itemGUID, bUnlock)
end

function VehicleUIContextLogic.RemovePartNode(itemID, bUnlock)
    local processor = VehicleUIContextLogic.GetContextProcessor()
    processor:SyncRemovePartNode(itemID, itemGUID, bUnlock)
end

function VehicleUIContextLogic.LoadVehicleFromSkinID(iItemID, iActorId)
    iActorId = setdefault(iActorId, 0)
    local vehicleFrontendSubsystemInst = VehicleUIContextLogic.GetUVehicleFrontendSubsystem()
    local bIsValid = isvalid(vehicleFrontendSubsystemInst)
    if not bIsValid then
        logerror("VehicleUIContextLogic.LoadVehicleFromSkinID vehicleFrontendSubsystemInst is nil")
        return
    end
    vehicleFrontendSubsystemInst:LoadVehicleFromSkinID(iItemID, iActorId)
end

function VehicleUIContextLogic.LoadStandardVehicleFromVehicleID(iVehicleID, iActorId)
    iActorId = setdefault(iActorId, 0)
    local vehicleFrontendSubsystemInst = VehicleUIContextLogic.GetUVehicleFrontendSubsystem()
    local bIsValid = isvalid(vehicleFrontendSubsystemInst)
    if not bIsValid then
        logerror("VehicleUIContextLogic.LoadStandardVehicleFromVehicleID vehicleFrontendSubsystemInst is nil")
        return
    end
    vehicleFrontendSubsystemInst:LoadStandardVehicle(iVehicleID, iActorId)
end

function VehicleUIContextLogic.ProcessDisposeByActorID(iActorId)
    iActorId = setdefault(iActorId, 0)
    local vehicleFrontendSubsystemInst = VehicleUIContextLogic.GetUVehicleFrontendSubsystem()
    local bIsValid = isvalid(vehicleFrontendSubsystemInst)
    if not bIsValid then
        return
    end
    vehicleFrontendSubsystemInst:ProcessDisposeByActorID(iActorId)
end

------------------------------------------------------------------------
--------------------------------  debug  -------------------------------
------------------------------------------------------------------------



------------------------------------------------------------------------
--------------------------------  data  --------------------------------
------------------------------------------------------------------------

function VehicleUIContextLogic.ResetUIContext()
end

function VehicleUIContextLogic.SetItemID(itemID)
    local context = VehicleUIContextLogic.GetUIContext()
    context:SetItemID(itemID)
end

function VehicleUIContextLogic.SetGUID(guid)
    local context = VehicleUIContextLogic.GetUIContext()
    context:SetGUID(guid)
end

function VehicleUIContextLogic.SetVehicleInfo(vehicleInfo)
    local context = VehicleUIContextLogic.GetUIContext()
    context:SetVehicleInfo(vehicleInfo)
end

function VehicleUIContextLogic.SetFocusSlotID(focusSlotID)
    local context = VehicleUIContextLogic.GetUIContext()
    context:SetFocusSlotID(focusSlotID)
end

function VehicleUIContextLogic.GetUVehicleFrontendSubsystem()
    return UVehicleFrontendSubsystem.Get()
end

function VehicleUIContextLogic.GetAVehicleFrontendActor(id)
    local vehicleFrontendSubsystemInst = VehicleUIContextLogic.GetUVehicleFrontendSubsystem()
    local bIsValid = isvalid(vehicleFrontendSubsystemInst)
    if not bIsValid then
        logerror("VehicleUIContextLogic.GetAVehicleFrontendActor vehicleFrontendSubsystemInst is nil")
        return
    end
    id = setdefault(id, 0)
    return vehicleFrontendSubsystemInst:GetActor(id)
end

function VehicleUIContextLogic.GetVehicleRootID()
    local vehicleFrontendActor = VehicleUIContextLogic.GetAVehicleFrontendActor()
    local bIsValid = isvalid(vehicleFrontendActor)
    if not bIsValid then
        logerror("VehicleUIContextLogic.GetVehicleRootID vehicleFrontendActor is nil")
        return nil
    end

    return vehicleFrontendActor:GetVehicleRootID()
end

function VehicleUIContextLogic.GetContextProcessor()
    local processor = Module.Vehicle.Field:GetUIContextProcessor()
    return processor
end

function VehicleUIContextLogic.GetUIContext()
    local context = Module.Vehicle.Field:GetUIContext()
    return context
end

function VehicleUIContextLogic.GetModificationTabList()
    local modificationTabList = Module.Vehicle.Field:GetModificationTabList()
    return modificationTabList
end

function VehicleUIContextLogic.GetModificationSlotIDListByTabID(inTabID)
    local vehicleSlotIDs = Module.Vehicle.Field:GetModificationSlotIDListByTabID(inTabID)
    return vehicleSlotIDs
end

function VehicleUIContextLogic.GetItemID()
    local context = VehicleUIContextLogic.GetUIContext()
    return context:GetItemID()
end

function VehicleUIContextLogic.GetGUID()
    local context = VehicleUIContextLogic.GetUIContext()
    return context:GetGUID()
end

function VehicleUIContextLogic.GetFrontend()
    local context =  VehicleUIContextLogic.GetUIContext()
    return context:GetFrontend()
end

function VehicleUIContextLogic.GetVehicleInfo()
    local context =  VehicleUIContextLogic.GetUIContext()
    return context:GetVehicleInfo()
end

function VehicleUIContextLogic.GetFocusSlotID()
    local context =  VehicleUIContextLogic.GetUIContext()
    return context:GetFocusSlotID()
end

function VehicleUIContextLogic.GetVehicleFrontendContextFromFrontend()
    local context =  VehicleUIContextLogic.GetUIContext()
    return context:GetVehicleFrontendContextFromFrontend()
end

function VehicleUIContextLogic.ProcessSetSkinID(skinID)
    local processor = VehicleUIContextLogic.GetContextProcessor()
    processor:ProcessSetSkinID(skinID)
end




------------------------------------------------------------------------
-----------------------------   private  -------------------------------
------------------------------------------------------------------------

-- 是否可以进入载具改装
function VehicleUIContextLogic.IsCouldChangeVehicle(uiParam)
    -- long1.0 修改载具资源包下载流程,首包确定有部分载具资源包,但是没有全部载具资源包,可以不用未下载不能进入
    return true
    -- return VehicleUIContextLogic.IsCouldChangeVehicleByPak()
end

function VehicleUIContextLogic.IsCouldChangeVehicleByPak(bDownloadPak)
    -- PC暂时没有资源检查
    local bIsHD = IsHD()
    if bIsHD then
        return true
    end

    return VehicleUIContextLogic._InternalIsCouldChangeVehicleByMobilePak(bDownloadPak)
end

-- 手游检查小包是否下载
function VehicleUIContextLogic._InternalIsCouldChangeVehicleByMobilePak(bDownloadPak)
    bDownloadPak = setdefault(bDownloadPak, true)
    -- 不支持下载包的默认为整包
    local bIsSupportLitePak = LiteDownloadManager:IsSupportLitePackage()
    if not bIsSupportLitePak then
        return true
    end

    -- 是否已经下载载具资源
    local mobilePakName = Module.Vehicle.Config.Const.VehicleMobilePakName
    local bIsDownLoaded = LiteDownloadManager:IsDownloadedByModuleName(mobilePakName)
    if bIsDownLoaded then
        return true
    end

    if bDownloadPak then
        local function fOnConfim()
            LiteDownloadManager:DownloadByModuleName(mobilePakName)
            Module.LitePackage:ShowMainPanel()
        end

        local function fOnCancel()
        end

        Module.CommonTips:ShowConfirmWindow(Module.Vehicle.Config.Loc.VehiclePakDownloadTitle, fOnConfim, fOnCancel)
    end
    return false
end

return VehicleUIContextLogic