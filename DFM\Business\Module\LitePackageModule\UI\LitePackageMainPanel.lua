----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLitePackage)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LitePackageMainPanel : LuaUIBaseView
local LitePackageMainPanel = ui("LitePackageMainPanel")
local LitePackageConfig = Module.LitePackage.Config
local LitePackageLogic = require "DFM.Business.Module.LitePackageModule.Logic.LitePackageLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local LiteDownloadNormalItem = require "DFM.Business.Module.LitePackageModule.UI.LiteDownloadNormalItem"
local LiteDownloadDataTable = nil
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LitePackageDownloadTableItem = require "DFM.Business.DataStruct.LitePackageStruct.LitePackageDownloadTableItem"
local FAnchors = import "Anchors"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

function LitePackageMainPanel:Ctor()
    self._rootWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local fCallbackIns = CreateCallBack(self.OnCloseBtnClick, self)
    if self._rootWindow then
        self._rootWindow:BindCloseCallBack(fCallbackIns)
        self._rootWindow:SetIsNeedBlock(true)
    end
    self._wtSGBMailList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1", self.OnGetCategoryCount, self._OnProcessCategoryWidget)
    self._wtAllQuestDownloadedTip = self:Wnd("WBP_Common_NoAnything", UITextBlock)
    self._wtCanvasPanel = self:Wnd("CanvasPanel_61", UIWidgetBase)
    self.SortedDownloadTable = {}
    self.CategoryTable = {}
    self.CategoryPaksItem = {}
end

function LitePackageMainPanel:OnNavBack()
    Facade.UIManager:CloseUI(self)
    return true
end

function LitePackageMainPanel:ResetDownloadAllBtn()
    local isDownlaodedAll = LiteDownloadManager:IsAllQuestDownloaded()
    local txtDownloadAll = LitePackageConfig.Loc.LitePackageMainPanel_Download_All
    if isDownlaodedAll then
        txtDownloadAll = LitePackageConfig.Loc.LitePackageMainPanel_Download_ALL_DOWNLOADED
    end

    if self.bShowDownloadCenter == false then
        local btnIns = self._rootWindow:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm,
            {
                { btnText = txtDownloadAll, fClickCallback = self.OnCommonButtonS1Clicked, caller = self,
                    bNeedClose = false },
            }, true)

        if btnIns ~= nil then
            self.downloadAllBtn = btnIns[2]
        end
    else
        local btnIns = self._rootWindow:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterCancelConfirm,
            {
                { btnText = LitePackageConfig.Loc.LitePackageMainPanel_Download_SKIP_TO_SETTING,
                    fClickCallback = self.OnBtnSkipToSettingClicked, caller = self, bNeedClose = false },
                { btnText = txtDownloadAll, fClickCallback = self.OnCommonButtonS1Clicked, caller = self,
                    bNeedClose = false },
            }, true)

        if #btnIns > 1 then
            self.downloadAllBtn = btnIns[2]
        end
    end

    if self.downloadAllBtn ~= nil then
        if isDownlaodedAll then
            self.downloadAllBtn:SetIsEnabledStyle(false)
        else
            self.downloadAllBtn:SetIsEnabledStyle(true)
        end
    end
end

function LitePackageMainPanel:OnCloseBtnClick()
    Facade.UIManager:CloseUI(self)
end


function LitePackageMainPanel:OnInitExtraData(bShowDownloadCenter,autoDownloadCategoryId,autoDownloadModuleKey,bshowReddot)
    loginfo("LitePackageMainPanel:OnInitExtraData",bShowDownloadCenter,autoDownloadCategoryId,autoDownloadModuleKey)
    self.bShowDownloadCenter = bShowDownloadCenter
    self._autoDownloadCategoryId = autoDownloadCategoryId
    self._autoDownloadModuleKey = autoDownloadModuleKey
    self.bshowReddot = bshowReddot
    self:ResetDownloadAllBtn()
end


function LitePackageMainPanel:OnOpen()
    self:AddListeners()
    self:RefreshData()
    self:RefreshUI()

    if self._wtAllQuestDownloadedTip~= nil then
        self._wtAllQuestDownloadedTip:Hidden()
    end

    if (PLATFORM_IOS or PLATFORM_ANDROID or _WITH_EDITOR) and self.bShowDownloadCenter == false then
        LiteDownloadManager:StartCheckModuleInfosAsync()
    end
end

function LitePackageMainPanel:RefreshData()
    if not LiteDownloadDataTable then
        LiteDownloadDataTable = Facade.TableManager:GetTable("LitePackageDownload")
    end

    if not LitePackCategoryTable then
        LitePackCategoryTable = Facade.TableManager:GetTable("LitePackageCategory")
    end

    for _, v in pairs(LitePackCategoryTable) do
        local categoryTable = {}
        categoryTable.CategoryTitle = v.CategoryTitle
        categoryTable.ShowCategoryID = v.ShowCategoryID
        categoryTable.Priority = v.Priority
        categoryTable.Paks = {}
        for _, pak in pairs(LiteDownloadDataTable) do
             local pakTableItem = LitePackageDownloadTableItem:New(pak)
             pakTableItem.ModuleKey = self:SetPakModuleKey(pakTableItem.ModuleKey)
            if pakTableItem.ShowCategory == v.ShowCategoryID and  pakTableItem.IsShow >0 then
                if not LiteDownloadManager:IsDownloadedByModuleName(pakTableItem.ModuleKey) then -- 若已下载则不显示在下载页面
                    table.insert(categoryTable.Paks,pakTableItem)
                    if VersionUtil.IsShipping() then --若shipping包，过滤掉testmap
                        if pakTableItem.MapOrModeDownload == 3 then -- testMap
                            table.remove(categoryTable.Paks)
                        end
                    end
                end
            end
            table.sort(categoryTable.Paks, function (a,b)
                return a.Priority > b.Priority
            end)
        end

        if categoryTable.Paks and (#categoryTable.Paks >0) then
            table.insert(self.CategoryTable,categoryTable)
        end

    end

    table.sort(self.CategoryTable, function (a,b)
        return a.Priority > b.Priority
    end)
end

function LitePackageMainPanel:RefreshUI()

end

function LitePackageMainPanel:OnGetCategoryCount()
    self._categoryCount = 0
    for _,category in pairs(self.CategoryTable) do
        if #category.Paks > 0 then
            self._categoryCount = self._categoryCount + 1
        end
    end
    return self._categoryCount
end

function LitePackageMainPanel:_OnProcessCategoryWidget(position,widget)
    if self.CategoryTable[position] then
        widget:RefreshItem(self.CategoryTable[position],self.CategoryTable[position].ShowCategoryID,self._autoDownloadCategoryId,self._autoDownloadModuleKey,self.bshowReddot)
        widget:SetPanelType(1)
        -- if LiteDownloadManager:IsDownloadingByModuleName("HDRuntimeCollection") then --若高清包正在下载，则资源下载置灰
        --     widget:SetIsEnabled(false)
        -- else
        --     widget:SetIsEnabled(true)
        -- end
        self.CategoryPaksItem[position] = widget
    end
end

function LitePackageMainPanel:OnBtnSkipToSettingClicked()--跳转至下载中心

    if self.bShowDownloadCenter == false then
        Module.CommonTips:ShowSimpleTip("could not skip to download center")
    else
        Module.SystemSetting:ShowSystemSettingMainView(Module.SystemSetting.Config.ESystemSettingPanel.PufferDownloadSetting
            , false)
        Facade.UIManager:CloseUI(self)
    end

end

function LitePackageMainPanel:OnCommonButtonS1Clicked()
    -- if LiteDownloadManager:IsDownloadingByModuleName("HDRuntimeCollection") then --若高清资源下载，则弹出提示并返回
    --     Module.CommonTips:ShowSimpleTip(LitePackageConfig.Loc.LitePackageMainPanel_Download_WaitForHDDownloading)
    --     return
    -- end
    if LiteDownloadManager:IsQuestDownloading() then
        LiteDownloadManager:CancelAll()
        self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Download_All)
    else
        local moduleKeys = {}
        for _, categoryPaks in pairs (self.CategoryTable) do 
            for _, pak in pairs(categoryPaks.Paks)  do
                if  not LiteDownloadManager:IsDownloadedByModuleName(pak.ModuleKey) then
                    table.insert(moduleKeys, pak.ModuleKey)
                end
            end
        end

        -- 计算资源大小，超过3G则弹窗提醒
        local WIFI_DOWNLOAD_SPACE_GB = 3
        local LiteDownloadGBNum = 1024 * 1024 * 1024
        local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
        local spaceGB = freeSpace / LiteDownloadGBNum
        local allNowSize = 0
        local allTotalSize = 0
        for index, moduleName in ipairs(moduleKeys) do
            local nowSize = LiteDownloadManager:GetNowSizeByModuleName(moduleName)
            local totalSize = LiteDownloadManager:GetTotalSizeByModuleName(moduleName)
            allNowSize = allNowSize + nowSize
            allTotalSize = allTotalSize + totalSize
        end

        local questSize = (allTotalSize - allNowSize) / LiteDownloadGBNum
        if spaceGB < WIFI_DOWNLOAD_SPACE_GB + questSize then
            local confirmHandle = function()

            end

            LogAnalysisTool.SignButtonClicked(10200001)
            local content = LitePackageConfig.Loc.LITE_DOWNLOAD_CHECK_SPACE_NOT_ENOUGH
            Module.CommonTips:ShowConfirmWindowWithSingleBtn(content, confirmHandle, nil)
            return
        else
            LiteDownloadManager:CancelAll()
            LiteDownloadManager:CheckAndDownloadAll(moduleKeys)
            self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Cancel_All)
        end
    end
end

-- UI监听事件、协议
function LitePackageMainPanel:AddListeners()
    self:AddLuaEvent(LitePackageConfig.evtHDDownloadCollectionFinish, self.OnHDDownloadCollectionFinish, self)
    self:AddLuaEvent(LitePackageConfig.evtDownloadCategoryOverlaped, self.OnDownloadCategoryOverlaped, self)
    self:AddLuaEvent(LitePackageConfig.evtPakDownloadTrigged, self.ChangeDownloadAllType, self)
    --self:AddLuaEvent(LitePackageConfig.evtPakDownloadFinish, self.OnDownloadCategoryOverlaped, self)
end


function LitePackageMainPanel:OnClose()
    self:RemoveAllLuaEvent()
    self.CategoryPaksItem = {}
    Timer.CancelDelay(self._scrollToCategoryDelayHandle)
end



function LitePackageMainPanel:OnHide()
end


function LitePackageMainPanel:OnShowBegin()
    self._wtSGBMailList:RefreshAllItems()
    self:TryScrollToTargetCategory()
    self:BindBackAction(self._OnBackBtnClicked, self)
    if LiteDownloadManager:IsQuestDownloading() then
        self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Cancel_All)
    end
    -- if LiteDownloadManager:IsDownloadingByModuleName("HDRuntimeCollection") then
    --     self.downloadAllBtn:SetBtnEnable(false)
    -- end

end

function LitePackageMainPanel:TryScrollToTargetCategory()
    loginfo("LitePackageMainPanel:TryScrollToTargetCategory")
    if self._autoDownloadCategoryId then
        local categoryIndex=1
        for k,v in pairs(self.CategoryTable or {}) do
            if v.ShowCategoryID == self._autoDownloadCategoryId then
                break
            end
            categoryIndex=categoryIndex+1
        end
        Timer.CancelDelay(self._scrollToCategoryDelayHandle)
        self._scrollToCategoryDelayHandle=Timer.DelayCall(0.1, function(self)
            self._wtSGBMailList:ScrollToIndex(categoryIndex)
        end,self)
    end
end

function LitePackageMainPanel:_OnBackBtnClicked()
    -- @yixiaoguan 实现封装暴露到Module
    local fCancelLeaveGameHandle = CreateCallBack(function(self)
    end, self)

    local fConfirmLeaveGameHandle = CreateCallBack(function(self)
        Facade.UIManager:CloseUI(self)
    end, self)
    Module.CommonBar:FlowBackQuitGame(fCancelLeaveGameHandle, fConfirmLeaveGameHandle)
end

function LitePackageMainPanel:OnHideBegin()
end

function LitePackageMainPanel:SetPakModuleKey(moduleKey) --设置modulekey，使其在高清与低清包下都适用
    if LiteDownloadManager:IsHDDownloadStatus() then
        return LiteDownloadManager:GetHDRuntimeNameByModuleName(moduleKey)
    end
        return moduleKey
end

function LitePackageMainPanel:OnHDDownloadCollectionFinish()
    self._wtSGBMailList:RefreshAllItems()
    self.downloadAllBtn:SetBtnEnable(true)
end

function LitePackageMainPanel:OnDownloadCategoryOverlaped()
    if self._RefreshItemHandler then
        Timer.CancelDelay(self._RefreshItemHandler)
        self._RefreshItemHandler = nil
    end
    local function f()
        self._wtSGBMailList:RefreshVisibleItemSizeAndPos()
    end
    self._RefreshItemHandler =  Timer.DelayCall(0, f, self)
end

function LitePackageMainPanel:ChangeDownloadAllType() --修改下载全部按钮的文本
    for _, categoryPaks in pairs (self.CategoryTable) do 
        for _, pak in pairs(categoryPaks.Paks)  do
            if  LiteDownloadManager:IsDownloadingByModuleName(pak.ModuleKey) then
                self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Cancel_All)
                return
            end
        end
    end
    self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Download_All)
end

function LitePackageMainPanel:OnPakDOwnloadFinish()
   local hasDownloading = LiteDownloadManager:IsQuestDownloading()
   if hasDownloading then
    self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Cancel_All)
   else
    self.downloadAllBtn:SetMainTitle(LitePackageConfig.Loc.LitePackageMainPanel_Download_All)
   end
end



return LitePackageMainPanel
