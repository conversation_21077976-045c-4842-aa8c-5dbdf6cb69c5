----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local GuideLogic = require "DFM.Business.Module.GuideModule.GuideLogic"
local GuideLogic_CheckCondition = require "DFM.Business.Module.GuideModule.GuideLogic_CheckCondition"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local GuideConfig= require "DFM.Business.Module.GuideModule.GuideConfig"
local EGameHUDState = import "GameHUDSate"
local ULuaSubsystem = import "LuaSubsystem"
local UDFMGuideManager = import "DFMGuideManager"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local ULAI = import "LAI"
local UDFMHealthDataComponent = import "DFMHealthDataComponent"
local ADFMCharacter = import "DFMCharacter"
local UGPGameplayGlobalDelegates = import "GPGameplayGlobalDelegates"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local UDFMGameLoadingManager = import("DFMGameLoadingManager")
local EIrisEnterStageType = import("EIrisEnterStageType")
local EItemInfoUpdatedReason = import "EItemInfoUpdatedReason"
local UDFMIrisEnterSubsystem = import "DFMIrisEnterSubsystem"
local InGameController= require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"

local EMapLocationPOILevelType = import "EMapLocationPOILevelType"
local ECharacterLiveStatus = import "ECharacterLiveStatus"
local UDFMGameNotch = import "DFMGameNotch"




---@source ../../../../../Source/DFMGameCore/DFMGameplay/Gamemode/Components/DFMBroardcastDefine.h:20
---@enum EGameBrocastType
local EGameBrocastType = import "EGameBrocastType"



local GuideEvtLogic = {}

GuideEvtLogic.bHasBindInGameEvents_Generic = false
GuideEvtLogic.bHasBindInGameEvents_SOL = false
GuideEvtLogic.bHasBindInGameEvents_MP = false

GuideEvtLogic.bHasBindGlobalEvents = false
GuideEvtLogic.bHasRegisterSolGameGuideDelegate = false

GuideEvtLogic.bLocalFocus = false
GuideEvtLogic.cacheHealthCom = nil
GuideEvtLogic.guideArmorPercent = 0.7
GuideEvtLogic.guideHealthPercent = 0.75

GuideEvtLogic._hActionGuideSkip = nil


local function log(...) loginfo("[GuideEvtLogic]", ...) end
local function warning(...) logwarning("[GuideEvtLogic]", ...) end
local function err(...) logerror("[GuideEvtLogic]", ...) end

-----------------------------
--region Bus

function GuideEvtLogic.OnGameFlowChangeLeave(gameflow)
    -- TODO: GuideClick check target ui is valid...
end

---@param gameflow EGameFlowStageType
function GuideEvtLogic.OnGameFlowChangeEnter(gameflow)
    if gameflow == EGameFlowStageType.Game then
        GuideEvtLogic._DoGuideSolPlayerFreeOperate()
    end
end

function GuideEvtLogic.BindInGameEvents()
    local gf = Facade.GameFlowManager:GetCurrentGameFlow()
    if gf ~= EGameFlowStageType.Game then
        warning("BindInGameEvents -> try bind not in game stage")
        return
    end

    GuideEvtLogic.BindInGameEvents_Generic()

    if GuideLogic.IsSOLMode() then
        log("BindInGameEvents -> SOL")
        GuideEvtLogic.BindInGameEvents_SOL()
    elseif GuideLogic.IsMpMode() then
        log("BindInGameEvents -> MP")
        GuideEvtLogic.BindInGameEvents_MP()
    end
end


function GuideEvtLogic.UnBindInGameEvents()
    GuideEvtLogic.UnBindInGameEvents_Generic()
    GuideEvtLogic.UnBindInGameEvents_SOL()
    GuideEvtLogic.UnBindInGameEvents_MP()
end



function GuideEvtLogic.BindInGameEvents_Generic()
    if bHasBindInGameEvents_Generic then
        return
    end
    bHasBindInGameEvents_Generic = true
    log("BindInGameEvents_Generic")

    local gi = GetGameInstance()

    UDFMGameplayGlobalDelegates.Get(gi).OnGuidePlayerDied:Add(GuideEvtLogic._OnPlayerDied)

    UDFMGameplayDelegates.Get(gi).OnClientPlayerMatchOver:Add(GuideEvtLogic._OnPlayerDied)
end

function GuideEvtLogic.UnBindInGameEvents_Generic()
    if not bHasBindInGameEvents_Generic then
        return
    end
    bHasBindInGameEvents_Generic = false
    log("UnBindInGameEvents_Generic")

    local gi = GetGameInstance()

    UDFMGameplayGlobalDelegates.Get(gi).OnGuidePlayerDied:Remove(GuideEvtLogic._OnPlayerDied)
    UDFMGameplayDelegates.Get(gi).OnClientPlayerMatchOver:Remove(GuideEvtLogic._OnPlayerDied)
end

function GuideEvtLogic.BindInGameEvents_SOL()
    if GuideEvtLogic.bHasBindInGameEvents_SOL then
        return
    end
    GuideEvtLogic.bHasBindInGameEvents_SOL = true

    log("BindInGameEvents_SOL")

    local gameInst = GetGameInstance()


    UDFMGameplayGlobalDelegates.Get(gameInst).OnGuideRemindEscape:Add(GuideEvtLogic._OnGuideRemindEscape) -- Tutorial delegate

    Module.Looting.Config.Events.evtCarryItemFail:AddListener(GuideEvtLogic._OnLootingCarryItemFail)
    Module.Looting.Config.Events.evtOnInvMgrRepData:AddListener(GuideEvtLogic._OnInventoryManagerRepData)

    UDFMGameplayDelegates.Get(gameInst).SOLMatchUrgenDisplayBegin:Add(GuideEvtLogic._OnGameCutDownShow) -- escape time hud delegate
    UDFMGameplayDelegates.Get(gameInst).OnLocalPlayerEnterExitPOI:Add(GuideEvtLogic._OnLocalPlayerEnterExitPOI)
    UDFMGameplayDelegates.get(gameInst).OnSOLMapExitInfoShow:Add(GuideEvtLogic._OnSOLMapExitInfoShow)


    UDFMGameHudDelegates.Get(gameInst).OnSelectMapTarget:Add(GuideEvtLogic._OnTryMarkMap)
    UDFMGameHudDelegates.Get(gameInst).OnInteractionPanelInteractorsChanged:Add(GuideEvtLogic._OnInteractionPanelInteractorsChanged)
    -- TODO: optimize this when there is not guide want this event?
    UDFMGameHudDelegates.Get(gameInst).OnInteractionPanelVisibilityChanged:Add(GuideEvtLogic._OnInteractionPanelVisibilityChanged)
    GuideEvtLogic._Unregister_OnBroadcastHudConsumeMsg = function() UDFMGameHudDelegates.Get(gameInst).OnBroadcastHudConsumeMsg:Remove(GuideEvtLogic._OnBroadcastHudConsumeMsg) end
    UDFMGameHudDelegates.Get(gameInst).OnBroadcastHudConsumeMsg:Add(GuideEvtLogic._OnBroadcastHudConsumeMsg)
    UDFMGameHudDelegates.Get(gameInst).OnGuideHUDMsg:Add(GuideEvtLogic._OnGuideHUDMsg)


    -- max sol match: 1 (max finished times 1)
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solNewPlayerLevel_FreeSafeBoxLooting) then
        log("BindInGameEvents_SOL -> register OnCommonSlotViewItemAdded for solNewPlayerLevel_FreeSafeBoxLooting")
        Module.CommonWidget.Config.Events.evtOnCommonSlotViewItemAdded:AddListener(GuideEvtLogic._OnCommonSlotViewItemAdded)
    end

    if DFHD_LUA == 1 then
        -- pass        
    else
        UDFMGameplayGlobalDelegates.Get(gameInst).OnGuideRaidPlayerFreeOperate:Add(GuideEvtLogic._OnGuideRaidPlayerFreeOperate)
        UDFMGameplayDelegates.Get(gameInst).OnTryMarkMap:Add(GuideEvtLogic._OnTryMarkMap)
        UDFMGameplayDelegates.Get(gameInst).OnMarkDetailOpen:Add(GuideEvtLogic._OnMarkDetailOpen)
        UDFMGameplayDelegates.Get(gameInst).PlayerCombatStateNotify:Add(GuideEvtLogic._OnCombatStateUpdate)
        UDFMGameplayDelegates.Get(gameInst).OnTaskSelected:Add(GuideEvtLogic._OnTaskSelected)
        --UDFMGameplayDelegates.Get(gameInst).OnClientQuestObjectionUpdate:Add(GuideEvtLogic._OnQuestObjectionStateChanged)
        UDFMGameplayGlobalDelegates.Get(gameInst).OnGamePickupBoxOperationBegin:Add(GuideEvtLogic._OnBeginLootSearch)
        UDFMGameplayGlobalDelegates.Get(gameInst).OnGamePickupDeadBodyOperationBegin:Add(GuideEvtLogic._OnBeginLootSearch)



        GuideConfig.EGuideEvent.evtLootBtnShow:AddListener(GuideEvtLogic._OnLootBtnShow)
        Module.Looting.Config.Events.evtUseItemInGame:AddListener(GuideEvtLogic._OnItemUseInGame)
        Module.Looting.Config.Events.evtLootEachItemSearchDone:AddListener(GuideEvtLogic._OnLootEachDone)

        UDFMGameHudDelegates.Get(gameInst).OnAllMapIconCreatedCompeted:Add(GuideEvtLogic._OnBigMapIconLoadFinished)
    end
end

function GuideEvtLogic.UnBindInGameEvents_SOL()
    if not GuideEvtLogic.bHasBindInGameEvents_SOL then
        return
    end
    GuideEvtLogic.bHasBindInGameEvents_SOL = false
    log("UnbindInGameEvents_SOL")
    local gameInst = GetGameInstance()
    
    
    GuideEvtLogic.RemoveInGameGuideDelegate_SOL() -- also unregister the high frequency delegates

    UDFMGameplayGlobalDelegates.Get(gameInst).OnGuideRemindEscape:Remove(GuideEvtLogic._OnGuideRemindEscape)
    UDFMGameplayDelegates.Get(gameInst).OnLocalPlayerEnterExitPOI:Remove(GuideEvtLogic._OnLocalPlayerEnterExitPOI)
    UDFMGameplayDelegates.get(gameInst).OnSOLMapExitInfoShow:Remove(GuideEvtLogic._OnSOLMapExitInfoShow)

    Module.Looting.Config.Events.evtCarryItemFail:RemoveListener(GuideEvtLogic._OnLootingCarryItemFail)
    Module.Looting.Config.Events.evtOnInvMgrRepData:RemoveListener(GuideEvtLogic._OnInventoryManagerRepData)

    UDFMGameplayDelegates.Get(gameInst).SOLMatchUrgenDisplayBegin:Remove(GuideEvtLogic._OnGameCutDownShow)
    UDFMGameHudDelegates.Get(gameInst).OnSelectMapTarget:Remove(GuideEvtLogic._OnTryMarkMap)
    UDFMGameHudDelegates.Get(gameInst).OnInteractionPanelVisibilityChanged:Remove(GuideEvtLogic._OnInteractionPanelVisibilityChanged)
    UDFMGameHudDelegates.Get(gameInst).OnBroadcastHudConsumeMsg:Remove(GuideEvtLogic._OnBroadcastHudConsumeMsg)
    UDFMGameHudDelegates.Get(gameInst).OnInteractionPanelInteractorsChanged:Remove(GuideEvtLogic._OnInteractionPanelInteractorsChanged)
    UDFMGameHudDelegates.Get(gameInst).OnGuideHUDMsg:Remove(GuideEvtLogic._OnGuideHUDMsg)

    UDFMGameplayDelegates.get(gameInst).OnSOLMapExitInfoShow:Remove(GuideEvtLogic._OnSOLMapExitInfoShow)

    Module.CommonWidget.Config.Events.evtOnCommonSlotViewItemAdded:RemoveListener(GuideEvtLogic._OnCommonSlotViewItemAdded)


    if DFHD_LUA == 1 then
        -- pass
        
    else
        UDFMGameplayGlobalDelegates.Get(gameInst).OnGuideRaidPlayerFreeOperate:Remove(GuideEvtLogic._OnGuideRaidPlayerFreeOperate)
        UDFMGameplayDelegates.Get(gameInst).OnTryMarkMap:Remove(GuideEvtLogic._OnTryMarkMap)
        UDFMGameplayDelegates.Get(gameInst).OnMarkDetailOpen:Remove(GuideEvtLogic._OnMarkDetailOpen)
        UDFMGameplayDelegates.Get(gameInst).PlayerCombatStateNotify:Remove(GuideEvtLogic._OnCombatStateUpdate)
        UDFMGameplayDelegates.Get(gameInst).OnTaskSelected:Remove(GuideEvtLogic._OnTaskSelected)
        --UDFMGameplayDelegates.Get(gameInst).OnClientQuestObjectionUpdate:Remove(GuideEvtLogic._OnQuestObjectionStateChanged)
        UDFMGameplayGlobalDelegates.Get(gameInst).OnGamePickupBoxOperationBegin:Remove(GuideEvtLogic._OnBeginLootSearch)
        UDFMGameplayGlobalDelegates.Get(gameInst).OnGamePickupDeadBodyOperationBegin:Remove(GuideEvtLogic._OnBeginLootSearch)


        GuideConfig.EGuideEvent.evtLootBtnShow:RemoveListener(GuideEvtLogic._OnLootBtnShow)
        Module.Looting.Config.Events.evtUseItemInGame:RemoveListener(GuideEvtLogic._OnItemUseInGame)
        Module.Looting.Config.Events.evtLootEachItemSearchDone:RemoveListener(GuideEvtLogic._OnLootEachDone)

        UDFMGameHudDelegates.Get(gameInst).OnAllMapIconCreatedCompeted:Remove(GuideEvtLogic._OnBigMapIconLoadFinished)
    end
end

function GuideEvtLogic.BindInGameEvents_MP()
    if GuideEvtLogic.bHasBindInGameEvents_MP then
        return
    end
    GuideEvtLogic.bHasBindInGameEvents_MP = true
    log("BindInGameEvents_MP")
end

function GuideEvtLogic.UnBindInGameEvents_MP()
    if not GuideEvtLogic.bHasBindInGameEvents_MP then
        return
    end
    GuideEvtLogic.bHasBindInGameEvents_MP = false
    log("UnBindInGameEvents_MP")
end

-- 全局监听的事件
function GuideEvtLogic.BindLuaEventsGlobal()
    if GuideEvtLogic.bHasBindGlobalEvents then
        log("BindLuaEventsGlobal -> already bind")
        return
    end
    log("BindLuaEventsGlobal -> start binding")
    GuideEvtLogic.bHasBindGlobalEvents = true
    local gameInst = GetGameInstance()

    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    hudLayerController.evtOnBeforeHUDStateChanged:AddListener(GuideEvtLogic._OnHudStateChanged)

    local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
    DFMGameLoadingManager.OnLoadingViewVisible:Add(GuideEvtLogic._OnLoadingViewVisible)
    DFMGameLoadingManager.OnLoadingViewClosed:Add(GuideEvtLogic._OnLoadingViewClosed)
    -------- 启动新手引导的事件

    GuideConfig.EGuideEvent.evtGuideEnd:AddListener(GuideEvtLogic._OnGuideEnd)
    GuideConfig.EGuideEvent.evtGuideEnd:AddListener(GuideEvtLogic._OnGuideEndReport) -------- INTL上报事件

    GuideConfig.EGuideEvent.evtGuideNewPlayerStageUpdate:AddListener(GuideEvtLogic._OnGuideUpdate)
    GuideConfig.EGuideEvent.evtGuideClick:AddListener(GuideEvtLogic._OnGuideClick)
    GuideConfig.EGuideEvent.evtOnGuideMsg:AddListener(GuideEvtLogic.OnGuideMsg)

    GuideConfig.EGuideEvent.evtLootingAiTagChanged:AddListener(GuideEvtLogic._OnLootingAiTagChanged)

    Module.Reconnect.Config.Events.evtOnCancelReconnectMatchBtnClicked:AddListener(GuideEvtLogic._OnCancelReconnetBtnClick)
    Server.SettlementServer.Events.evtNoExceptionSettlement:AddListener(GuideEvtLogic._OnNoExceptionSettlement)
    Module.Settlement.Config.Events.evtNotSeeExceptionSettlement:AddListener(GuideEvtLogic._OnNoExceptionSettlement)
    Module.Settlement.Config.Events.evtSettlementUILoadFinished:AddListener(GuideEvtLogic._OnSettlementUIOpenFinished)
    Module.Settlement.Config.Events.evtSkillTutorialClicked:AddListener(GuideEvtLogic._OnSettlementSkillTutorialClicked)



    Module.CommonBar.Config.evtTabIndexChanged:AddListener(GuideEvtLogic._OnCommonBarTabIndexChanged)

    Module.IrisSafeHouse.Config.evtSafeHouseHUDOpenAnimFinish:AddListener(GuideEvtLogic._OnSafeHouseHUDOpenFinished)
    Module.IrisSafeHouse.Config.evtIrisWorldEntryOnHideBegin:AddListener(GuideEvtLogic._OnIrisWorldEntryOnHideBegin)
    Module.IrisSafeHouse.Config.evtGoBtnClicked:AddListener(GuideEvtLogic._OnGoSolBtnClicked)
    Module.IrisSafeHouse.Config.evtReadyBtnClicked:AddListener(GuideEvtLogic._OnReadySolBtnClicked)
    

    Module.Inventory.Config.Events.evtWareHouseMainOpenFinish:AddListener(GuideEvtLogic._OnWarehouseMainShow)
    Module.Inventory.Config.Events.evtWareHouseMainOnHideBegin:AddListener(GuideEvtLogic._OnWareHouseMainHideBegin)
    Module.Inventory.Config.Events.evtRepairWindowRepairBtnClick:AddListener(GuideEvtLogic._OnRepairWindowRepairBtnClick)
    Module.Inventory.Config.Events.evtRepairWindowRepairFailedByNoEnoughCurrency:AddListener(GuideEvtLogic._OnRepairWindowRepairFailedByNoEnoughCurrency)
    Module.Inventory.Config.Events.evtRepairWindowShowBegin:AddListener(GuideEvtLogic._OnRepairWindowShowBegin)
    Module.Inventory.Config.Events.evtRepairWindowHideBegin:AddListener(GuideEvtLogic._OnRepairWindowHideBegin)
    Module.Inventory.Config.Events.evtWareHouseWithTabExtArrangeBtnPressed:AddListener(GuideEvtLogic._OnWareHouseWithTabExtArrangeBtnPressed)


    Module.ItemDetail.Config.evtItemDetailBtnCreateFinish:AddListener(GuideEvtLogic._OnItemDetailBtnCreateFinish)
    Module.ItemDetail.Config.evtItemDetailPanelTryOpen:AddListener(GuideEvtLogic._OnItemDetailPanelTryOpen)
    Module.ItemDetail.Config.evtItemDetailSellClicked:AddListener(GuideEvtLogic._OnItemDetailSellClicked)
    Module.Auction.Config.Events.evtAuctionOnShelfUncountableSellOpen:AddListener(GuideEvtLogic._OnAuctionOnShelfUncountableSellOpen)
    Module.Looting.Config.Events.evtOpenLootingUI:AddListener(GuideEvtLogic._OnGuideLooting)
    Module.Looting.Config.Events.evtCloseLootingUI:AddListener(GuideEvtLogic._OnLootingClose)

    Module.Settlement.Config.Events.evtEvacuateTrophyViewLoadFinish:AddListener(GuideEvtLogic._OnEvacuateTrophyShow)
    Module.SandBoxMap.Config.evtSandBoxShowBegin:AddListener(GuideEvtLogic._OnSandBoxMapShowBegin)
    Module.SandBoxMap.Config.evtSandBoxHideBegin:AddListener(GuideEvtLogic._OnSandBoxMapHideBegin)
    Module.Quest.Config.evtQuestChapterLoadFinish:AddListener(GuideEvtLogic._OnQuestChapterShow)
    Module.Shop.Config.evtShopChooseOpenFinish:AddListener(GuideEvtLogic._OnShopChooseShow)
    Module.Auction.Config.Events.evtAuctionSellAuctionFail:AddListener(GuideEvtLogic._OnAuctionSellAuctionFail)
    Module.Auction.Config.Events.evtAuctionSellAuctionClicked:AddListener(GuideEvtLogic._OnAuctionSellAuctionClicked)
    Module.Auction.Config.Events.evtAuctionSellOpen:AddListener(GuideEvtLogic._OnAuctionSellOpen)
    Module.Auction.Config.Events.evtAuctionSellHide:AddListener(GuideEvtLogic._OnAuctionSellHide)
    Module.Auction.Config.Events.evtAuctionRealSellAuctionClicked:AddListener(GuideEvtLogic._OnAuctionRealSellAuctionClicked)
    Module.Auction.Config.Events.evtAuctionShelveClose:AddListener(GuideEvtLogic._OnAuctionShelveClose)
    -- Module.Reward.Config.Events.evtShowModuleUnlock:AddListener(GuideEvtLogic._OnShowModuleUnlock)
    Module.Reward.Config.Events.evtPathOfGrowthLevelUpPanelOpen:AddListener(GuideEvtLogic._OnPathOfGrowthLevelUpPanelOpen)
    Server.RoleInfoServer.Events.evtSeasonLevelUpdate:AddListener(GuideEvtLogic._OnLevelChanged)
    Module.BattlefieldEntry.Config.evtMainPanelShowBegin:AddListener(GuideEvtLogic._OnMpMainPanelShowBegin)
    Module.BattlefieldEntry.Config.evtMainPanelOpen:AddListener(GuideEvtLogic._OnMpMainUIOpen)
    Module.BattlefieldEntry.Config.evtMainPanelHide:AddListener(GuideEvtLogic._OnMpMainUIHide)
    Module.BattlefieldEntry.Config.evtMainPanelShowAnimFinish:AddListener(GuideEvtLogic._OnMpMainUIOpenFinished)
    Module.BattlefieldEntry.Config.evtStartClicked:AddListener(GuideEvtLogic._OnMpStartClicked)
    Module.BattlefieldEntry.Config.evtModeSelectorOnShowBegin:AddListener(GuideEvtLogic._OnBattlefieldEntryModeSelectorShowBegin)

    Module.Auction.Config.Events.evtAuctionOpenFinishHD:AddListener(GuideEvtLogic._OnAuctionShow)
    Server.TipsRecordServer.events.evtTipsRecordFetchDataFinished:AddListener(GuideEvtLogic._OnTipsRecordInitFinished)
    Server.GuideServer.Events.evtWaitServerGuideDataStateUpdate:AddListener(GuideEvtLogic._SetInputStateAllPlatform)
    Server.GuideServer.Events.evtSkipAllGuide:AddListener(GuideEvtLogic._OnServerSkipAllGuide)
    Server.GuideServer.Events.evtStopAllGuide:AddListener(GuideEvtLogic._OnServerStopAllGuide)

    Module.SandBoxMap.Config.evtClickSandBoxTarget:AddListener(GuideEvtLogic._OnClickSandBoxTarget)
    Module.SandBoxMap.Config.evtSOLConfirmBtnClicked:AddListener(GuideEvtLogic._OnSandBoxConfirmBtnClicked)
    Module.SandBoxMap.Config.evtStrategicSelectionSOLMainPanelRefresh:AddListener(GuideEvtLogic._OnStrategicSelectionSOLMainPanelRefresh)
    Module.SandBoxMap.Config.evtPostRefreshPanel:AddListener(GuideEvtLogic._OnSandBoxPostRefreshPanel)

    Module.SandBoxMap.Config.evtStrategicSelectionSOLSubModeChange:AddListener(GuideEvtLogic._OnSandBoxStrategicSelectionSOLSubModeChange)
    Module.SandBoxMap.Config.evtStrategicSelectionSOLActionFileOnShowBegin:AddListener(GuideEvtLogic._OnSandBoxStrategicSelectionSOLActionFileOnShowBegin)
    Module.SandBoxMap.Config.evtStrategicSelectionSOLActionFileOnHideBegin:AddListener(GuideEvtLogic._OnSandBoxStrategicSelectionSOLActionFileOnHideBegin)
    Module.SandBoxMap.Config.evtOnStrategicSelectionMapDownloadBtnClicked:AddListener(GuideEvtLogic._OnStrategicSelectionMapDownloadBtnClicked)


    Module.CommonTips.Config.Events.evtCommonWindowOpenNeedPauseGuide:AddListener(GuideEvtLogic._OnOpenPauseGuideUI)
    Module.CommonTips.Config.Events.evtCommonWindowCloseNeedPauseGuide:AddListener(GuideEvtLogic._OnClosePauseGuideUI)
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntraceOnShowBegin:AddListener(GuideEvtLogic._OnSystemSettingHDEntranceOnShowBegin)
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntranceOnHideBegin:AddListener(GuideEvtLogic._OnSystemSettingHDEntranceOnHideBegin)
    Module.SystemSetting.Config.Event.evtSettingMainShowBeginHD:AddListener(GuideEvtLogic._OnSettingMainShowBeginHD)
    Module.SystemSetting.Config.Event.evtSettingMainHideHD:AddListener(GuideEvtLogic._OnSettingMainHideHD)
    Module.SystemSetting.Config.Event.evtSettingBrightnessShowHD:AddListener(GuideEvtLogic._OnSettingBrightnessShowHD)
    Module.SystemSetting.Config.Event.evtSettingBrightnessHideHD:AddListener(GuideEvtLogic._OnSettingBrightnessHideHD)
    Module.SystemSetting.Config.Event.evtSystemSettingMainUIOpen:AddListener(GuideEvtLogic._OnSystemSettingMainUIOpen)
    Module.SystemSetting.Config.Event.evtSystemSettingMainUIClose:AddListener(GuideEvtLogic._OnSystemSettingMainUIClose)

    Module.ArmedForce.Config.evtArmedForceMainShow:AddListener(GuideEvtLogic._OnArmedForceMainBeginShow)
    Module.ArmedForce.Config.evtArmedForceMainHide:AddListener(GuideEvtLogic._OnArmedForceMainHide)
    Module.ArmedForce.Config.evtArmedForceQuickOperationOpen:AddListener(GuideEvtLogic._OnArmedForceQuickOperationOpenBegin)
    Module.ArmedForce.Config.evtArmedForceQuickOperationTryOpen:AddListener(GuideEvtLogic._OnAssemblyQuickOperationTryOpen)
    Module.ArmedForce.Config.evtAssemblySquadPickOpenFinish:AddListener(GuideEvtLogic._OnAssemblySquadPickOpenFinish)
    Module.ArmedForce.Config.evtAssemblySquadPickHeroClicked:AddListener(GuideEvtLogic._OnAssemblySquadPickHeroClicked)
    Module.Login.Config.Events.evtOnGatewayKickPlayer:AddListener(GuideEvtLogic._OnGatewayKickPlayer)
    Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock:AddListener(GuideEvtLogic._OnEvtSwitchMoudleUnlock)
    Module.Settlement.Config.Events.evtContinueClicked:AddListener(GuideEvtLogic._OnEvacuateContinueClicked)
    Server.LootingServer.Events.evtLootingItemMove:AddListener(GuideEvtLogic._OnLootingItemMove)
    Module.CommonWidget.Config.Events.evtGlobalItemDragStart:AddListener(GuideEvtLogic._OnItemDragStart)
    Module.CommonWidget.Config.Events.evtGlobalItemDragCancelled:AddListener(GuideEvtLogic._OnItemDragCancelled)
    Server.MatchServer.Events.evtBeginSeamless:AddListener(GuideEvtLogic._OnBeginSeamless) -- 无缝入局开始
    Module.CommonWidget.Config.Events.evtGlobalItemDrop:AddListener(GuideEvtLogic._OnItemDrop)

    DFMGlobalEvents.evtBusinessShutDown:AddListener(GuideEvtLogic._OnLuaBusinessShutDown)
    
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(GuideEvtLogic._OnConnectSuccess)


    UDFMIrisEnterSubsystem.Get(GetWorld()).OnSeamlessSequencePreSpawn:Add(GuideEvtLogic._OnSeamlessSequencePreSpawn)
    Facade.UIManager.Events.evtStackUIChanged:AddListener(GuideEvtLogic._OnStackUIChanged)
    Module.Inventory.Config.Events.evtOnBatchSellWindowShow:AddListener(GuideEvtLogic._OnBatchSellWindowShow)
    Module.Inventory.Config.Events.evtTrySale:AddListener(GuideEvtLogic._OnTrySale)
    Module.Inventory.Config.Events.evtWarehouseEquipSlotViewOnDrop:AddListener(GuideEvtLogic._OnWarehouseEquipSlotViewOnDrop)
    Module.Settlement.Config.Events.evtExpUpShow:AddListener(GuideEvtLogic._OnMpExpUpShow)
    Module.Settlement.Config.Events.evtWeaponExpShow:AddListener(GuideEvtLogic._OnMpWeaponExpUpShow)
    Module.ArmedForce.Config.evtOnMpPresetPanelShow:AddListener(GuideEvtLogic._OnMpPresetPanelShow)
    Module.ArmedForce.Config.evtOnMpPresetPanelHide:AddListener(GuideEvtLogic._OnMpPresetPanelHide)
 
    Module.ArmedForce.Config.evtAssemblyMainPanelRentalBtnClicked:AddListener(GuideEvtLogic._OnAssemblyMainPanelRentalBtnClicked)
    Module.ArmedForce.Config.evtOnAssemblySelectionMPMainShow:AddListener(GuideEvtLogic._OnAssemblySelectionMPMainShow)
    Module.ArmedForce.Config.evtOnAssemblySelectionMPMainHide:AddListener(GuideEvtLogic._OnAssemblySelectionMPMainHide)
    Module.ArmedForce.Config.evtAssemblySelectionMainShow:AddListener(GuideEvtLogic._OnAssemblySelectionMainShow)
    Module.ArmedForce.Config.evtPostAssemblySelectionRefreshEquipmentBtnState:AddListener(GuideEvtLogic._OnPostAssemblySelectionRefreshEquipmentBtnState)
    Module.ArmedForce.Config.evtOnAssemblySelectionMainSelectFirstItemDone:AddListener(GuideEvtLogic._OnAssemblySelectionMainSelectFirstItemDone)
    Module.ArmedForce.Config.evtAssemblyRentalMainPanelOnShowBegin:AddListener(GuideEvtLogic._OnAssemblyRentalMainPanelOnShowBegin)
    Module.ArmedForce.Config.evtAssemblyRentalMainPanelOnHideBegin:AddListener(GuideEvtLogic._OnAssemblyRentalMainPanelOnHideBegin)


    Module.ArmedForce.Config.evtOnWeaponUpgradeBtnClicked:AddListener(GuideEvtLogic._OnWeaponUpgradeBtnClicked)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIShow:AddListener(GuideEvtLogic._OnWeaponUpgradeUIShow)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIHide:AddListener(GuideEvtLogic._OnWeaponUpgradeUIHide)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradePanelBtnClicked:AddListener(GuideEvtLogic._OnWeaponUpgradePanelBtnClicked)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeSuccessShow:AddListener(GuideEvtLogic._OnWeaponUpgradeSuccessShow)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeSuccessHide:AddListener(GuideEvtLogic._OnWeaponUpgradeSuccessHide)
    Module.Gunsmith.Config.Events.evtGunsmithUpgradeItemOperated:AddListener(GuideEvtLogic._OnGunsmithUpgradeItemOperated)
    Module.Gunsmith.Config.Events.evtOnUseItemBtnClicked:AddListener(GuideEvtLogic._OnUseItemBtnClicked)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeSuccessPanelSelectAllClicked:AddListener(GuideEvtLogic._OnWeaponUpgradeSuccessPanelBtnClicked)

    Module.ArmedForce.Config.evtOnAssemblyWeaponClicked:AddListener(GuideEvtLogic._OnAssemblyWeaponClicked)
    Module.ArmedForce.Config.evtAssemblySelectionRepairBtnClicked:AddListener(GuideEvtLogic._OnAssemblySelectionRepairBtnClicked)
    Module.ArmedForce.Config.evtArmedForceSureBtnClicked:AddListener(GuideEvtLogic._OnArmedForceSureBtnClicked)


    -- Settlement
    Module.Settlement.Config.Events.evtBeginMpSettlementPop:AddListener(GuideEvtLogic._OnBeginMpSettlementPop)
    Module.Settlement.Config.Events.evtEndMpSettlementPop:AddListener(GuideEvtLogic._OnEndMpSettlementPop)
    Module.Settlement.Config.Events.evtBeginSolSettlementPop:AddListener(GuideEvtLogic._OnBeginSolSettlementPop)
    Module.Settlement.Config.Events.evtEndSolSettlementPop:AddListener(GuideEvtLogic._OnEndSolSettlementPop)
    Module.Settlement.Config.Events.evtEndWeaponUpgradePop:AddListener(GuideEvtLogic._OnEndWeaponUpgradePop)
    Module.Reward.Config.Events.evtOpenLevelUpUI:AddListener(GuideEvtLogic._OnOpenLevelUpUI)
    Module.Reward.Config.Events.evtCloseLevelUpUI:AddListener(GuideEvtLogic._OnCloseLevelUpUI)
    Module.GCloudSDK.Config.Events.evtSDKCommonTipOpen:AddListener(GuideEvtLogic._OnSDKCommonTipOpen)
    Module.GCloudSDK.Config.Events.evtSDKCommonTipClose:AddListener(GuideEvtLogic._OnSDKCommonTipClose)
    Module.Tournament.Config.evtTournamentMainUIShow:AddListener(GuideEvtLogic._OnTournamentMainUIShow)
    Module.Tournament.Config.evtOpenRankRewardPanelBtnClicked:AddListener(GuideEvtLogic._OnOpenRankRewardPanelBtnClicked)
    Module.Settlement.Config.Events.evtMpRankSettlementShow:AddListener(GuideEvtLogic._OnMpRankSettlementShow)
    Module.Settlement.Config.Events.evtMpRankSettlementInAnimFin:AddListener(GuideEvtLogic._OnMpRankSettlementInAnimFin)
    Module.Settlement.Config.Events.evtMpRankSettlementClose:AddListener(GuideEvtLogic._OnMpRankSettlementClose)
    Module.Settlement.Config.Events.evtSolRankSettlementShow:AddListener(GuideEvtLogic._OnSolRankSettlementShow)
    Module.Settlement.Config.Events.evtSolRankSettlementClose:AddListener(GuideEvtLogic._OnSolRankSettlementClose)
    Module.Settlement.Config.Events.evtEvacuationResultInfoOnShowBegin:AddListener(GuideEvtLogic._OnEvacuationResultInfoOnShowBegin)

    Module.Hero.Config.Events.evtOnHeroItemClicked:AddListener(GuideEvtLogic._OnHeroItemClicked)
    Module.Hero.Config.Events.evtOnHeroViewSkillBtnClicked:AddListener(GuideEvtLogic._OnHeroViewSkillBtnClicked)
    Module.Hero.Config.Events.evtOnHeroTopSkillPanelShow:AddListener(GuideEvtLogic._OnHeroTopSkillPanelShow)
    Module.Hero.Config.Events.evtOnHeroTopSkillPanelClose:AddListener(GuideEvtLogic._OnHeroTopSkillPanelClose)
    Module.Hero.Config.Events.evtOnHeroShowSkillBtnClicked:AddListener(GuideEvtLogic._OnHeroShowSkillBtnClicked)
    Server.HeroServer.Events.evtSOLUsedHeroIdChanged:AddListener(GuideEvtLogic._OnSOLUsedHeroIdChanged)
    -- Server.HeroServer.Events.evtMPUsedHeroIdChanged:AddListener(GuideEvtLogic._OnUsedHeroIdChanged)
    Module.Hero.Config.Events.evtHeroSkillVideoPanelShow:AddListener(GuideEvtLogic._OnHeroSkillVideoPanelShow)
    Module.Hero.Config.Events.evtHeroListTopPanelOnShowBegin:AddListener(GuideEvtLogic._OnHeroListTopPanelOnShowBegin)


    Module.Ranking.Config.evtRankMainPanelShow:AddListener(GuideEvtLogic._OnRankMainPanelShow)
    Module.Ranking.Config.evtRankMainPanelClose:AddListener(GuideEvtLogic._OnRankMainPanelClose)
    Module.Ranking.Config.evtRankRewardBtnClicked:AddListener(GuideEvtLogic._OnRankRewardBtnClicked)
    Module.BlackSite.Config.Events.evtBlackSiteMainUIOpen:AddListener(GuideEvtLogic._OnBlackSiteMainUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteMainUIClose:AddListener(GuideEvtLogic._OnBlackSiteMainUIClose)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeUIOpen:AddListener(GuideEvtLogic._OnBlackSiteUpgradeUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeConditionsUIOnShowBegin:AddListener(GuideEvtLogic._OnBlackSiteUpgradeConditionsUIOnShowBegin)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeUIHideBegin:AddListener(GuideEvtLogic._OnBlackSiteUpgradeHideBegin)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeItemClicked:AddListener(GuideEvtLogic._OnBlackSiteUpgradeItemClicked)
    Module.BlackSite.Config.Events.evtBlackSiteConstructUIOpen:AddListener(GuideEvtLogic._OnBlackSiteConstructUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteProduceUIOpen:AddListener(GuideEvtLogic._OnBlackSiteProduceUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteProduceUIHideBegin:AddListener(GuideEvtLogic._OnBlackSiteProduceUIHideBegin)
    Module.BlackSite.Config.Events.evtBlackSiteProduceOpenDevice:AddListener(GuideEvtLogic._OnBlackSiteProduceOpenDevice)
    Module.BlackSite.Config.Events.evtBlackSiteEntranceItemClicked:AddListener(GuideEvtLogic._OnBlackSiteEntranceItemClicked)
    Module.BlackSite.Config.Events.evtBlackSiteConditionsUpgradeBtnClicked:AddListener(GuideEvtLogic._OnBlackSiteConditionsUpgradeBtnClicked)


    Module.Market.Config.Events.evtMarketMainPanelTabChanged:AddListener(GuideEvtLogic._OnMarketMainPanelTabChanged)
    Module.Market.Config.Events.evtMarketSkinPageOnShowBegin:AddListener(GuideEvtLogic._OnMarketSkinPageOnShowBegin)
    Module.Market.Config.Events.evtMarketSkinPageOnHideBegin:AddListener(GuideEvtLogic._OnMarketSkinPageOnHideBegin)

    Module.Quest.Config.evtQuestMainPanelOnHideBegin:AddListener(GuideEvtLogic._OnQuestMainPanelOnHideBegin)

    Server.InventoryServer.Events.evtItemMove:AddListener(GuideEvtLogic._OnInventoryItemMove)
    Server.InventoryServer.Events.evtItemRepaireSuccess:AddListener(GuideEvtLogic._OnItemRepaireSuccess)

    Module.Collection.Config.Events.evtOnCollectionMainPanelTabIndexChanged:AddListener(GuideEvtLogic._OnCollectionMainPanelTabIndexChanged)
    Module.Collection.Config.Events.evtOnCollectionMainPanelTertiaryTabIndexChanged:AddListener(GuideEvtLogic._OnCollectionMainPanelTertiaryTabIndexChanged)
    Module.Collection.Config.Events.evtHangingPageItemClicked:AddListener(GuideEvtLogic._OnCollectionHangingPageItemClicked)

    Module.GameMode.Config.evtOnPreDoSwitchModeOperation:AddListener(GuideEvtLogic._OnPreDoSwitchModeOperation)

    Module.Social.Config.Events.evtOnInvitePopViewOnShowBegin:AddListener(GuideEvtLogic._OnInvitePopViewOnShowBegin)


    Module.Store.Config.evtActivityMandelDrawOnlyOnShowBegin:AddListener(GuideEvtLogic._OnActivityMandelDrawOnlyOnShowBegin)

    UDFMGameplayGlobalDelegates.Get(gameInst).OnGuideSolPlayerFreeOperate:Add(GuideEvtLogic._OnGuideSolPlayerFreeOperate)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnSeamlessEnterFirstSeqStart:Add(GuideEvtLogic._OnSeamlessEnterFirstSeqStart)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnIrisEnterStageChange:Add(GuideEvtLogic._OnIrisEnterStageChange)
    UGPGameplayGlobalDelegates.Get(gameInst).OnStartLocalFocus:Add(GuideEvtLogic._OnStartLocalFocus)
    UGPGameplayGlobalDelegates.Get(gameInst).OnStopLocalFocus:Add(GuideEvtLogic._OnStopLocalFocus)

    if DFHD_LUA == 1 then
        Server.MatchServer.Events.evtRealBoarding:AddListener(GuideEvtLogic._OnRealBoarding)
        Module.IrisSafeHouse.Config.evtTopBarFinished:AddListener(GuideEvtLogic._OnTopBarFinished)
        Module.BattlefieldEntry.Config.evtTopBarFinished:AddListener(GuideEvtLogic._OnMPTopBarFinished)

        --Module.IrisSafeHouse.Config.evtTopBarSafehouseAfterInit:AddListener(GuideEvtLogic._OnTopBarSafehouseAfterInit)
        Module.IrisSafeHouse.Config.evtTopBarSafehouseBeforeUnInit:AddListener(GuideEvtLogic._OnTopBarSafehouseBeforeUnInit)
        Module.BattlefieldEntry.Config.evtTopBarMpBeforeUnInit:AddListener(GuideEvtLogic._OnTopBarMpBeforeUnInit)
        --Module.IrisSafeHouse.Config.evtTopBarSafehouseClicked:AddListener(GuideEvtLogic._OnTopBarSafehouseClicked)

        --Module.IrisSafeHouse.Config.flowEvtToEnterSafeHouse:AddListener(GuideEvtLogic._OnBeginEnterSafeHouse) -- 准备切换进安全屋

        Module.IrisSafeHouse.Config.evtFinishEnterSafeHouse:AddListener(GuideEvtLogic._RealEnterSafeHouse)

        -- pc输入设备的切换
        UGPInputDelegates.Get(gameInst).OnInputTypeChanged:Add(GuideEvtLogic._OnInputTypeChanged)
    else
        -------- server事件
        
        -------- 启动新手引导的事件

        -------- gameplay 事件
        GuideConfig.EGuideEvent.evtGuideShowInsuranceTips:AddListener(GuideEvtLogic._OnGuideShowInsuranceTips)
        GuideConfig.EGuideEvent.evtCancelGotoInsurance:AddListener(GuideEvtLogic._OnGuideCancelGotoInsurance)

        -- @dexzhou 2025/7/23: 确认 12和15两个引导阶段 deprecated
        -- GuideConfig.EGuideEvent.evtOpenWinTransToInventory:AddListener(GuideEvtLogic._OnGuideTransToInventory)

        -------- 其他事件
        Module.SandBoxMap.Config.evtSandBoxShow:AddListener(GuideEvtLogic._OnSandBoxMapShow)
        Module.ArmedForce.Config.evtArmedForceMainOpenFinish:AddListener(GuideEvtLogic._OnArmedForceMainShow)
        Module.ArmedForce.Config.evtSelectionEquipMainOpenFinish:AddListener(GuideEvtLogic._OnSelectionEquipShow)
        Module.IrisSafeHouse.Config.evtSafeHouseHUDOpenAnimStart:AddListener(GuideEvtLogic._OnSafeHouseHUDOpenStart)
        Module.IrisSafeHouse.Config.evtPrepareBtnClicked:AddListener(GuideEvtLogic._OnPrepareBtnClicked)
        Module.Inventory.Config.Events.evtWarehouseExpansionOpenFinish:AddListener(GuideEvtLogic._OnWarehouseExpansionShow)
        Module.IrisSafeHouse.Config.evtSyncMainOpenFinish:AddListener(GuideEvtLogic._OnevtSyncMainShow)
        Module.IrisSafeHouse.Config.evtSyncMainOpenStart:AddListener(GuideEvtLogic._OnevtSyncMainShowStart)
        Module.ArmedForce.Config.evtSlotViewClicked:AddListener(GuideEvtLogic._OnSlotViewClicked)
        Module.CommonWidget.Config.Events.evtItemDoubleClicked:AddListener(GuideEvtLogic._OnItemDoubleClick)
        Module.IrisSafeHouse.Config.evtSyncWarehouseBtnClicked:AddListener(GuideEvtLogic._OnSyncWarehouseBtnClicked)
        Module.IrisSafeHouse.Config.evtSyncQuestBtnClicked:AddListener(GuideEvtLogic._OnSyncQuestBtnClicked)
        Module.Quest.Config.evtQuestChapterBtnClicked:AddListener(GuideEvtLogic._OnQuestChapterBtnClicked)
        Module.Quest.Config.evtQuestItemLoadFinish:AddListener(GuideEvtLogic._OnQuestItemShow)
        Module.Quest.Config.evtQuestDetailBtnClicked:AddListener(GuideEvtLogic._OnQuestDetailBtnClicked)
        Module.Quest.Config.evtQuestDetailLoadFinish:AddListener(GuideEvtLogic._OnQuestDetailShow)
        Module.Quest.Config.evtQuestStateChange:AddListener(GuideEvtLogic._OnQuestStateChange)
        Module.ArmedForce.Config.evtArmedForceWarehouseBtnClicked:AddListener(GuideEvtLogic._OnArmedForceWarehouseBtnClicked)
        Module.IrisSafeHouse.Config.evtSyncShopBtnClicked:AddListener(GuideEvtLogic._OnSyncShopBtnClicked)
        Module.Shop.Config.evtShopSpecMerchanBtnClicked:AddListener(GuideEvtLogic._OnShopSpecMerchanBtnClicked)
        Module.Shop.Config.evtShopMainOpenFinish:AddListener(GuideEvtLogic._OnShopMainShow)

        Module.Inventory.Config.Events.evtWareHouseTransferEnd:AddListener(GuideEvtLogic._OnWareHouseTransferEnd)

        Module.GCloudSDK.Config.Events.evtHopeMainUIOpen:AddListener(GuideEvtLogic._OnOpenPauseGuideUI)
        Module.GCloudSDK.Config.Events.evtHopeMainUIClose:AddListener(GuideEvtLogic._OnClosePauseGuideUI)
        Module.Shop.Config.evtSelectedSItemChanged:AddListener(GuideEvtLogic._OnShopItemSelected)

        Module.Settlement.Config.Events.evtEvacuateWinViewLoadFinish:AddListener(GuideEvtLogic._OnEvacuateWinViewShow)


        Module.ItemDetail.Config.evtItemDetailInputBoxBtnClicked:AddListener(GuideEvtLogic._OnItemDetailSliderBtnClicked)
        Module.ItemDetail.Config.evtItemDetailPanelIsFoldChange:AddListener(GuideEvtLogic._OnItemDetailPanelIsFoldChange)

        -- Module.Reward.Config.Events.evtChooseInventoryReward:AddListener(GuideEvtLogic._OnRewardClose)
        Module.Reward.Config.Events.evtOpenRewardPanel:AddListener(GuideEvtLogic._OnRewardOpen)
        Module.Reward.Config.Events.evtCloseRewardPanel:AddListener(GuideEvtLogic._OnRewardClose)

        Module.ArmedForce.Config.evtProcessBtnClicked:AddListener(GuideEvtLogic._OnArmedForceProcessBtnClicked)

        Module.Report.Config.evtReportMainPanelOpen:AddListener(GuideEvtLogic._OnReportMainPanelOpen)
        Module.Report.Config.evtReportMainPanelClose:AddListener(GuideEvtLogic._OnReportMainPanelClose)

        Server.ShopServer.Events.evtShopItemReGenerated:AddListener(GuideEvtLogic._OnShopItemReGenerated)
        Server.QuestServer.Events.evtQuestAcceptSuccess:AddListener(GuideEvtLogic._OnQuestAcceptSuccess)
        Server.QuestServer.Events.evtQuestGetReward:AddListener(GuideEvtLogic._OnQuestGetReward)
        Module.ArmedForce.Config.evtAssemblySquadPickClose:AddListener(GuideEvtLogic._OnAssemblySquadPickClose)
        Module.ArmedForce.Config.evtOnAssemblySelectionOutAnimBegin:AddListener(GuideEvtLogic._OnAssemblySelectionOutAnimBegin)
        Module.ArmedForce.Config.evtAssemblySquadPickConfirmClicked:AddListener(GuideEvtLogic._OnAssemblySquadPickConfirmClicked)
        Module.SandBoxMap.Config.evtConfrimTarget:AddListener(GuideEvtLogic._OnSandBoxConfrimTarget)
        Module.SandBoxMap.Config.evtDetailInAniFinished:AddListener(GuideEvtLogic._OnSandBoxDetailShowFinished)

        Module.ArmedForce.Config.evtSubViewChanged:AddListener(GuideEvtLogic._OnAssemblySubViewChanged)
        Module.ArmedForce.Config.evtArmedForceQuickOperationOpenFinish:AddListener(GuideEvtLogic._OnAssemblyMedicineOpenFinish)
        Module.ArmedForce.Config.evtArmedForceQuickOperationConfirmClicked:AddListener(GuideEvtLogic._OnAssemblyMedicineConfirmClicked)
        Module.Settlement.Config.Events.evtEvacuateExpViewOpen:AddListener(GuideEvtLogic._OnEvacuateExpViewOpen)
        Module.ItemDetail.Config.evtItemDetailSellResult:AddListener(GuideEvtLogic._OnItemSellResult)
        Module.CommonTips.Config.Events.evtCommonWindowOpenFinish:AddListener(GuideEvtLogic._OnCommonWindowOpenFinish)
        Server.InventoryServer.Events.evtPostSortMultiPos:AddListener(GuideEvtLogic._OnInventorySorted)


        --Module.IrisSafeHouse.Config.evtModeHallShow:AddListener(GuideEvtLogic._OnModeHallShow)
        Module.IrisSafeHouse.Config.evtModeHallOpenAnimFinish:AddListener(GuideEvtLogic._OnModeHallOpenAnimFinish)
        Module.IrisSafeHouse.Config.evtModeHallClose:AddListener(GuideEvtLogic._OnModeHallClose)
        Module.IrisSafeHouse.Config.evtModeHallChangeMode:AddListener(GuideEvtLogic._OnModeHallChangeMode) 
        Module.Login.Config.Events.evtOnRegisterSuccess:AddListener(GuideEvtLogic._OnRenameFinished)
        Module.Reward.Config.Events.evtOpenUnlockUI:AddListener(GuideEvtLogic._OnUnlockUIOpen)
        Module.Reward.Config.Events.evtCloseUnlockUI:AddListener(GuideEvtLogic._OnUnlockUIClose)
        Module.Login.Config.Events.evtOnRegisterFinish:AddListener(GuideEvtLogic._OnRegisterFinish)
        Module.BattlefieldEntry.Config.evtOnVedioPlayStart:AddListener(GuideEvtLogic._OnMpVedioPlayStart)
        Module.BattlefieldEntry.Config.evtOnVedioPlayEnd:AddListener(GuideEvtLogic._OnMpVedioPlayEnd)
        Module.BattlefieldEntry.Config.evtOnConfirmModeBtnClick:AddListener(GuideEvtLogic._OnMpConfirmModeBtnClick)
        Module.BattlefieldEntry.Config.evtSelectModeUIOpen:AddListener(GuideEvtLogic._OnMpSelectModeUIOpen)
        Module.BattlefieldEntry.Config.evtSelectModeUIShowAnimFinish:AddListener(GuideEvtLogic._OnMpSelectModeUIShowAnimFinish)


        Module.Shop.Config.evtSelectedDepartmentIdChanged:AddListener(GuideEvtLogic._OnSelectedDepartmentIdChanged)

        --- 折叠屏
        UDFMGameNotch.Get(GetGameInstance()).OnFoldStatusChanged:Add(GuideEvtLogic._OnFoldStatusChanged)

    end


    if IsInEditor() then
		ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Add(GuideEvtLogic._OnNotifyResolutionResized)
	end
end

function GuideEvtLogic.UnBindLuaEventsGlobal()
    if not GuideEvtLogic.bHasBindGlobalEvents then
        log("UnBindLuaEventsGlobal: Not bound, skipping.")
        return
    end
    GuideEvtLogic.bHasBindGlobalEvents = false
    log("UnBindLuaEventsGlobal")
    local gameInst = GetGameInstance()

    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    hudLayerController.evtOnBeforeHUDStateChanged:RemoveListener(GuideEvtLogic._OnHudStateChanged)

    local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
    DFMGameLoadingManager.OnLoadingViewVisible:Remove(GuideEvtLogic._OnLoadingViewVisible)
    DFMGameLoadingManager.OnLoadingViewClosed:Remove(GuideEvtLogic._OnLoadingViewClosed)
    -------- 启动新手引导的事件

    GuideConfig.EGuideEvent.evtGuideEnd:RemoveListener(GuideEvtLogic._OnGuideEndReport) -------- INTL上报事件
    GuideConfig.EGuideEvent.evtGuideEnd:RemoveListener(GuideEvtLogic._OnGuideEnd) 

    GuideConfig.EGuideEvent.evtGuideNewPlayerStageUpdate:RemoveListener(GuideEvtLogic._OnGuideUpdate)
    GuideConfig.EGuideEvent.evtGuideClick:RemoveListener(GuideEvtLogic._OnGuideClick)
    GuideConfig.EGuideEvent.evtOnGuideMsg:RemoveListener(GuideEvtLogic.OnGuideMsg)

    GuideConfig.EGuideEvent.evtLootingAiTagChanged:RemoveListener(GuideEvtLogic._OnLootingAiTagChanged)

    Module.Reconnect.Config.Events.evtOnCancelReconnectMatchBtnClicked:RemoveListener(GuideEvtLogic._OnCancelReconnetBtnClick)

    Server.SettlementServer.Events.evtNoExceptionSettlement:RemoveListener(GuideEvtLogic._OnNoExceptionSettlement)
    Module.Settlement.Config.Events.evtNotSeeExceptionSettlement:RemoveListener(GuideEvtLogic._OnNoExceptionSettlement)
    Module.Settlement.Config.Events.evtSettlementUILoadFinished:RemoveListener(GuideEvtLogic._OnSettlementUIOpenFinished)
    Module.Settlement.Config.Events.evtSkillTutorialClicked:RemoveListener(GuideEvtLogic._OnSettlementSkillTutorialClicked)

    Module.CommonBar.Config.evtTabIndexChanged:RemoveListener(GuideEvtLogic._OnCommonBarTabIndexChanged)


    Module.IrisSafeHouse.Config.evtSafeHouseHUDOpenAnimFinish:RemoveListener(GuideEvtLogic._OnSafeHouseHUDOpenFinished)
    Module.IrisSafeHouse.Config.evtIrisWorldEntryOnHideBegin:RemoveListener(GuideEvtLogic._OnIrisWorldEntryOnHideBegin)
    Module.IrisSafeHouse.Config.evtGoBtnClicked:RemoveListener(GuideEvtLogic._OnGoSolBtnClicked)
    Module.IrisSafeHouse.Config.evtReadyBtnClicked:RemoveListener(GuideEvtLogic._OnReadySolBtnClicked)

    Module.Inventory.Config.Events.evtWareHouseMainOpenFinish:RemoveListener(GuideEvtLogic._OnWarehouseMainShow)
    Module.Inventory.Config.Events.evtWareHouseMainOnHideBegin:RemoveListener(GuideEvtLogic._OnWareHouseMainHideBegin)
    Module.Inventory.Config.Events.evtRepairWindowRepairBtnClick:RemoveListener(GuideEvtLogic._OnRepairWindowRepairBtnClick)
    Module.Inventory.Config.Events.evtRepairWindowRepairFailedByNoEnoughCurrency:RemoveListener(GuideEvtLogic._OnRepairWindowRepairFailedByNoEnoughCurrency)
    Module.Inventory.Config.Events.evtRepairWindowShowBegin:RemoveListener(GuideEvtLogic._OnRepairWindowShowBegin)
    Module.Inventory.Config.Events.evtRepairWindowHideBegin:RemoveListener(GuideEvtLogic._OnRepairWindowHideBegin)
    Module.Inventory.Config.Events.evtWareHouseWithTabExtArrangeBtnPressed:RemoveListenerByHandle(GuideEvtLogic._OnWareHouseWithTabExtArrangeBtnPressed)

    Server.InventoryServer.Events.evtItemRepaireSuccess:RemoveListener(GuideEvtLogic._OnItemRepaireSuccess)
    Server.InventoryServer.Events.evtItemMove:RemoveListener(GuideEvtLogic._OnInventoryItemMove)

    Module.ItemDetail.Config.evtItemDetailBtnCreateFinish:RemoveListener(GuideEvtLogic._OnItemDetailBtnCreateFinish)
    Module.ItemDetail.Config.evtItemDetailPanelTryOpen:RemoveListener(GuideEvtLogic._OnItemDetailPanelTryOpen)
    Module.ItemDetail.Config.evtItemDetailSellClicked:RemoveListener(GuideEvtLogic._OnItemDetailSellClicked)
    Module.Auction.Config.Events.evtAuctionOnShelfUncountableSellOpen:RemoveListener(GuideEvtLogic._OnAuctionOnShelfUncountableSellOpen)
    Module.Looting.Config.Events.evtOpenLootingUI:RemoveListener(GuideEvtLogic._OnGuideLooting)
    Module.Looting.Config.Events.evtCloseLootingUI:RemoveListener(GuideEvtLogic._OnLootingClose)

    Module.Settlement.Config.Events.evtEvacuateTrophyViewLoadFinish:RemoveListener(GuideEvtLogic._OnEvacuateTrophyShow)
    Module.SandBoxMap.Config.evtSandBoxShowBegin:RemoveListener(GuideEvtLogic._OnSandBoxMapShowBegin)
    Module.SandBoxMap.Config.evtSandBoxHideBegin:RemoveListener(GuideEvtLogic._OnSandBoxMapHideBegin)
    Module.ArmedForce.Config.evtArmedForceMainHide:RemoveListener(GuideEvtLogic._OnArmedForceMainHide)
    Module.ArmedForce.Config.evtArmedForceMainShow:RemoveListener(GuideEvtLogic._OnArmedForceMainBeginShow)
    Module.Quest.Config.evtQuestChapterLoadFinish:RemoveListener(GuideEvtLogic._OnQuestChapterShow)
    Module.Shop.Config.evtShopChooseOpenFinish:RemoveListener(GuideEvtLogic._OnShopChooseShow)
    Module.ArmedForce.Config.evtArmedForceQuickOperationOpen:RemoveListener(GuideEvtLogic._OnArmedForceQuickOperationOpenBegin)
    Module.Auction.Config.Events.evtAuctionSellAuctionFail:RemoveListener(GuideEvtLogic._OnAuctionSellAuctionFail)
    Module.Auction.Config.Events.evtAuctionSellAuctionClicked:RemoveListener(GuideEvtLogic._OnAuctionSellAuctionClicked)
    Module.Auction.Config.Events.evtAuctionSellOpen:RemoveListener(GuideEvtLogic._OnAuctionSellOpen)
    Module.Auction.Config.Events.evtAuctionSellHide:RemoveListener(GuideEvtLogic._OnAuctionSellHide)
    Module.Auction.Config.Events.evtAuctionRealSellAuctionClicked:RemoveListener(GuideEvtLogic._OnAuctionRealSellAuctionClicked)
    Module.Auction.Config.Events.evtAuctionShelveClose:RemoveListener(GuideEvtLogic._OnAuctionShelveClose)
    -- Module.Reward.Config.Events.evtShowModuleUnlock:RemoveListener(GuideEvtLogic._OnShowModuleUnlock)
    Module.Reward.Config.Events.evtPathOfGrowthLevelUpPanelOpen:RemoveListener(GuideEvtLogic._OnPathOfGrowthLevelUpPanelOpen)
    Server.RoleInfoServer.Events.evtSeasonLevelUpdate:RemoveListener(GuideEvtLogic._OnLevelChanged)
    Module.BattlefieldEntry.Config.evtMainPanelShowBegin:RemoveListener(GuideEvtLogic._OnMpMainPanelShowBegin)
    Module.BattlefieldEntry.Config.evtMainPanelOpen:RemoveListener(GuideEvtLogic._OnMpMainUIOpen)
    Module.BattlefieldEntry.Config.evtMainPanelHide:RemoveListener(GuideEvtLogic._OnMpMainUIHide)
    Module.BattlefieldEntry.Config.evtMainPanelShowAnimFinish:RemoveListener(GuideEvtLogic._OnMpMainUIOpenFinished)
    Module.BattlefieldEntry.Config.evtStartClicked:RemoveListener(GuideEvtLogic._OnMpStartClicked)
    Module.BattlefieldEntry.Config.evtModeSelectorOnShowBegin:RemoveListener(GuideEvtLogic._OnBattlefieldEntryModeSelectorShowBegin)

    Module.Auction.Config.Events.evtAuctionOpenFinishHD:RemoveListener(GuideEvtLogic._OnAuctionShow)
    Server.TipsRecordServer.events.evtTipsRecordFetchDataFinished:RemoveListener(GuideEvtLogic._OnTipsRecordInitFinished)
    Server.GuideServer.Events.evtWaitServerGuideDataStateUpdate:RemoveListener(GuideEvtLogic._SetInputStateAllPlatform)
    Server.GuideServer.Events.evtSkipAllGuide:RemoveListener(GuideEvtLogic._OnServerSkipAllGuide)
    Server.GuideServer.Events.evtStopAllGuide:RemoveListener(GuideEvtLogic._OnServerStopAllGuide)

    Module.SandBoxMap.Config.evtClickSandBoxTarget:RemoveListener(GuideEvtLogic._OnClickSandBoxTarget)
    Module.SandBoxMap.Config.evtSOLConfirmBtnClicked:RemoveListener(GuideEvtLogic._OnSandBoxConfirmBtnClicked)
    Module.SandBoxMap.Config.evtStrategicSelectionSOLMainPanelRefresh:RemoveListener(GuideEvtLogic._OnStrategicSelectionSOLMainPanelRefresh)
    Module.SandBoxMap.Config.evtPostRefreshPanel:RemoveListener(GuideEvtLogic._OnSandBoxPostRefreshPanel)

    Module.SandBoxMap.Config.evtStrategicSelectionSOLSubModeChange:RemoveListener(GuideEvtLogic._OnSandBoxStrategicSelectionSOLSubModeChange)
    Module.SandBoxMap.Config.evtStrategicSelectionSOLActionFileOnShowBegin:RemoveListener(GuideEvtLogic._OnSandBoxStrategicSelectionSOLActionFileOnShowBegin)
    Module.SandBoxMap.Config.evtStrategicSelectionSOLActionFileOnHideBegin:RemoveListener(GuideEvtLogic._OnSandBoxStrategicSelectionSOLActionFileOnHideBegin)
    Module.SandBoxMap.Config.evtOnStrategicSelectionMapDownloadBtnClicked:RemoveListener(GuideEvtLogic._OnStrategicSelectionMapDownloadBtnClicked)


    Module.CommonTips.Config.Events.evtCommonWindowOpenNeedPauseGuide:RemoveListener(GuideEvtLogic._OnOpenPauseGuideUI)
    Module.CommonTips.Config.Events.evtCommonWindowCloseNeedPauseGuide:RemoveListener(GuideEvtLogic._OnClosePauseGuideUI)
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(GuideEvtLogic._OnConnectSuccess)
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntraceOnShowBegin:RemoveListener(GuideEvtLogic._OnSystemSettingHDEntranceOnShowBegin)
    Module.SystemSetting.Config.Event.evtSystemSettingHDEntranceOnHideBegin:RemoveListener(GuideEvtLogic._OnSystemSettingHDEntranceOnHideBegin)
    Module.SystemSetting.Config.Event.evtSettingMainShowBeginHD:RemoveListener(GuideEvtLogic._OnSettingMainShowBeginHD)
    Module.SystemSetting.Config.Event.evtSettingMainHideHD:RemoveListener(GuideEvtLogic._OnSettingMainHideHD)
    Module.SystemSetting.Config.Event.evtSettingBrightnessShowHD:RemoveListener(GuideEvtLogic._OnSettingBrightnessShowHD)
    Module.SystemSetting.Config.Event.evtSettingBrightnessHideHD:RemoveListener(GuideEvtLogic._OnSettingBrightnessHideHD)
    Module.SystemSetting.Config.Event.evtSystemSettingMainUIOpen:RemoveListener(GuideEvtLogic._OnSystemSettingMainUIOpen)
    Module.SystemSetting.Config.Event.evtSystemSettingMainUIClose:RemoveListener(GuideEvtLogic._OnSystemSettingMainUIClose)

    DFMGlobalEvents.evtBusinessShutDown:RemoveListener(GuideEvtLogic._OnLuaBusinessShutDown)
    Module.Login.Config.Events.evtOnGatewayKickPlayer:RemoveListener(GuideEvtLogic._OnGatewayKickPlayer)
    Server.ModuleUnlockServer.Events.evtSwitchMoudleUnlock:RemoveListener(GuideEvtLogic._OnEvtSwitchMoudleUnlock)
    Module.Settlement.Config.Events.evtContinueClicked:RemoveListener(GuideEvtLogic._OnEvacuateContinueClicked)
    Server.LootingServer.Events.evtLootingItemMove:RemoveListener(GuideEvtLogic._OnLootingItemMove)
    Module.CommonWidget.Config.Events.evtGlobalItemDragStart:RemoveListener(GuideEvtLogic._OnItemDragStart)
    Module.CommonWidget.Config.Events.evtGlobalItemDragCancelled:RemoveListener(GuideEvtLogic._OnItemDragCancelled)
    Module.CommonWidget.Config.Events.evtGlobalItemDrop:RemoveListener(GuideEvtLogic._OnItemDrop)

    Server.MatchServer.Events.evtBeginSeamless:RemoveListener(GuideEvtLogic._OnBeginSeamless)
    Facade.UIManager.Events.evtStackUIChanged:RemoveListener(GuideEvtLogic._OnStackUIChanged)
    Module.Inventory.Config.Events.evtOnBatchSellWindowShow:RemoveListener(GuideEvtLogic._OnBatchSellWindowShow)
    Module.Inventory.Config.Events.evtTrySale:RemoveListener(GuideEvtLogic._OnTrySale)
    Module.Inventory.Config.Events.evtWarehouseEquipSlotViewOnDrop:RemoveListener(GuideEvtLogic._OnWarehouseEquipSlotViewOnDrop)

    Module.Shop.Config.evtSelectedDepartmentIdChanged:RemoveListener(GuideEvtLogic._OnSelectedDepartmentIdChanged)
    Module.Settlement.Config.Events.evtExpUpShow:RemoveListener(GuideEvtLogic._OnMpExpUpShow)
    Module.Settlement.Config.Events.evtWeaponExpShow:RemoveListener(GuideEvtLogic._OnMpWeaponExpUpShow)

    Module.ArmedForce.Config.evtArmedForceQuickOperationTryOpen:RemoveListener(GuideEvtLogic._OnAssemblyQuickOperationTryOpen)
    Module.ArmedForce.Config.evtAssemblySquadPickOpenFinish:RemoveListener(GuideEvtLogic._OnAssemblySquadPickOpenFinish)
    Module.ArmedForce.Config.evtAssemblySquadPickHeroClicked:RemoveListener(GuideEvtLogic._OnAssemblySquadPickHeroClicked)
    Module.ArmedForce.Config.evtOnMpPresetPanelShow:RemoveListener(GuideEvtLogic._OnMpPresetPanelShow)
    Module.ArmedForce.Config.evtOnMpPresetPanelHide:RemoveListener(GuideEvtLogic._OnMpPresetPanelHide)
    Module.ArmedForce.Config.evtAssemblyMainPanelRentalBtnClicked:RemoveListener(GuideEvtLogic._OnAssemblyMainPanelRentalBtnClicked)
    Module.ArmedForce.Config.evtOnAssemblySelectionMPMainShow:RemoveListener(GuideEvtLogic._OnAssemblySelectionMPMainShow)
    Module.ArmedForce.Config.evtOnAssemblySelectionMPMainHide:RemoveListener(GuideEvtLogic._OnAssemblySelectionMPMainHide)
    Module.ArmedForce.Config.evtAssemblySelectionMainShow:RemoveListener(GuideEvtLogic._OnAssemblySelectionMainShow)
    Module.ArmedForce.Config.evtPostAssemblySelectionRefreshEquipmentBtnState:RemoveListener(GuideEvtLogic._OnPostAssemblySelectionRefreshEquipmentBtnState)
    Module.ArmedForce.Config.evtOnAssemblySelectionMainSelectFirstItemDone:RemoveListener(GuideEvtLogic._OnAssemblySelectionMainSelectFirstItemDone)
    Module.ArmedForce.Config.evtAssemblyRentalMainPanelOnShowBegin:RemoveListener(GuideEvtLogic._OnAssemblyRentalMainPanelOnShowBegin)
    Module.ArmedForce.Config.evtAssemblyRentalMainPanelOnHideBegin:RemoveListener(GuideEvtLogic._OnAssemblyRentalMainPanelOnHideBegin)


    Module.ArmedForce.Config.evtOnWeaponUpgradeBtnClicked:RemoveListener(GuideEvtLogic._OnWeaponUpgradeBtnClicked)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIShow:RemoveListener(GuideEvtLogic._OnWeaponUpgradeUIShow)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIHide:RemoveListener(GuideEvtLogic._OnWeaponUpgradeUIHide)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradePanelBtnClicked:RemoveListener(GuideEvtLogic._OnWeaponUpgradePanelBtnClicked)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeSuccessShow:RemoveListener(GuideEvtLogic._OnWeaponUpgradeSuccessShow)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeSuccessHide:RemoveListener(GuideEvtLogic._OnWeaponUpgradeSuccessHide)
    Module.Gunsmith.Config.Events.evtGunsmithUpgradeItemOperated:RemoveListener(GuideEvtLogic._OnGunsmithUpgradeItemOperated)
    Module.Gunsmith.Config.Events.evtOnUseItemBtnClicked:RemoveListener(GuideEvtLogic._OnUseItemBtnClicked)
    Module.Gunsmith.Config.Events.evtOnWeaponUpgradeSuccessPanelSelectAllClicked:RemoveListener(GuideEvtLogic._OnWeaponUpgradeSuccessPanelBtnClicked)
    Module.ArmedForce.Config.evtOnAssemblyWeaponClicked:RemoveListener(GuideEvtLogic._OnAssemblyWeaponClicked)
    Module.ArmedForce.Config.evtAssemblySelectionRepairBtnClicked:RemoveListener(GuideEvtLogic._OnAssemblySelectionRepairBtnClicked)
    Module.ArmedForce.Config.evtArmedForceSureBtnClicked:RemoveListener(GuideEvtLogic._OnArmedForceSureBtnClicked)

    Module.Settlement.Config.Events.evtBeginMpSettlementPop:RemoveListener(GuideEvtLogic._OnBeginMpSettlementPop)
    Module.Settlement.Config.Events.evtEndMpSettlementPop:RemoveListener(GuideEvtLogic._OnEndMpSettlementPop)
    Module.Settlement.Config.Events.evtBeginSolSettlementPop:RemoveListener(GuideEvtLogic._OnBeginSolSettlementPop)
    Module.Settlement.Config.Events.evtEndSolSettlementPop:RemoveListener(GuideEvtLogic._OnEndSolSettlementPop)

    Module.Settlement.Config.Events.evtEndWeaponUpgradePop:RemoveListener(GuideEvtLogic._OnEndWeaponUpgradePop)
    Module.Reward.Config.Events.evtOpenLevelUpUI:RemoveListener(GuideEvtLogic._OnOpenLevelUpUI)
    Module.Reward.Config.Events.evtCloseLevelUpUI:RemoveListener(GuideEvtLogic._OnCloseLevelUpUI)
    Module.GCloudSDK.Config.Events.evtSDKCommonTipOpen:RemoveListener(GuideEvtLogic._OnSDKCommonTipOpen)
    Module.GCloudSDK.Config.Events.evtSDKCommonTipClose:RemoveListener(GuideEvtLogic._OnSDKCommonTipClose)
    Module.Tournament.Config.evtTournamentMainUIShow:RemoveListener(GuideEvtLogic._OnTournamentMainUIShow)
    Module.Tournament.Config.evtOpenRankRewardPanelBtnClicked:RemoveListener(GuideEvtLogic._OnOpenRankRewardPanelBtnClicked)
    Module.Settlement.Config.Events.evtMpRankSettlementShow:RemoveListener(GuideEvtLogic._OnMpRankSettlementShow)
    Module.Settlement.Config.Events.evtMpRankSettlementInAnimFin:RemoveListener(GuideEvtLogic._OnMpRankSettlementInAnimFin)

    Module.Settlement.Config.Events.evtMpRankSettlementClose:RemoveListener(GuideEvtLogic._OnMpRankSettlementClose)
    Module.Settlement.Config.Events.evtSolRankSettlementShow:RemoveListener(GuideEvtLogic._OnSolRankSettlementShow)
    Module.Settlement.Config.Events.evtSolRankSettlementClose:RemoveListener(GuideEvtLogic._OnSolRankSettlementClose)

    Module.Settlement.Config.Events.evtEvacuateWinViewLoadFinish:RemoveListener(GuideEvtLogic._OnEvacuateWinViewShow)
    Module.Settlement.Config.Events.evtEvacuationResultInfoOnShowBegin:RemoveListener(GuideEvtLogic._OnEvacuationResultInfoOnShowBegin)

    Module.Hero.Config.Events.evtOnHeroItemClicked:RemoveListener(GuideEvtLogic._OnHeroItemClicked)
    Module.Hero.Config.Events.evtOnHeroViewSkillBtnClicked:RemoveListener(GuideEvtLogic._OnHeroViewSkillBtnClicked)
    Module.Hero.Config.Events.evtOnHeroTopSkillPanelShow:RemoveListener(GuideEvtLogic._OnHeroTopSkillPanelShow)
    Module.Hero.Config.Events.evtOnHeroTopSkillPanelClose:RemoveListener(GuideEvtLogic._OnHeroTopSkillPanelClose)
    Module.Hero.Config.Events.evtOnHeroShowSkillBtnClicked:RemoveListener(GuideEvtLogic._OnHeroShowSkillBtnClicked)
    Server.HeroServer.Events.evtSOLUsedHeroIdChanged:RemoveListener(GuideEvtLogic._OnSOLUsedHeroIdChanged)
    -- Server.HeroServer.Events.evtMPUsedHeroIdChanged:RemoveListener(GuideEvtLogic._OnUsedHeroIdChanged)
    Module.Hero.Config.Events.evtHeroSkillVideoPanelShow:RemoveListener(GuideEvtLogic._OnHeroSkillVideoPanelShow)
    Module.Hero.Config.Events.evtHeroListTopPanelOnShowBegin:RemoveListener(GuideEvtLogic._OnHeroListTopPanelOnShowBegin)

    Module.Ranking.Config.evtRankMainPanelShow:RemoveListener(GuideEvtLogic._OnRankMainPanelShow)
    Module.Ranking.Config.evtRankMainPanelClose:RemoveListener(GuideEvtLogic._OnRankMainPanelClose)
    Module.Ranking.Config.evtRankRewardBtnClicked:RemoveListener(GuideEvtLogic._OnRankRewardBtnClicked)
    Module.BlackSite.Config.Events.evtBlackSiteMainUIOpen:RemoveListener(GuideEvtLogic._OnBlackSiteMainUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteMainUIClose:RemoveListener(GuideEvtLogic._OnBlackSiteMainUIClose)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeUIOpen:RemoveListener(GuideEvtLogic._OnBlackSiteUpgradeUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeConditionsUIOnShowBegin:RemoveListener(GuideEvtLogic._OnBlackSiteUpgradeConditionsUIOnShowBegin)
    Module.BlackSite.Config.Events.evtBlackSiteUpgradeUIHideBegin:RemoveListener(GuideEvtLogic._OnBlackSiteUpgradeHideBegin)


    Module.BlackSite.Config.Events.evtBlackSiteUpgradeItemClicked:RemoveListener(GuideEvtLogic._OnBlackSiteUpgradeItemClicked)
    Module.BlackSite.Config.Events.evtBlackSiteConstructUIOpen:RemoveListener(GuideEvtLogic._OnBlackSiteConstructUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteProduceUIHideBegin:RemoveListener(GuideEvtLogic._OnBlackSiteProduceUIHideBegin)
    Module.BlackSite.Config.Events.evtBlackSiteProduceUIOpen:RemoveListener(GuideEvtLogic._OnBlackSiteProduceUIOpen)
    Module.BlackSite.Config.Events.evtBlackSiteProduceOpenDevice:RemoveListener(GuideEvtLogic._OnBlackSiteProduceOpenDevice)
    Module.BlackSite.Config.Events.evtBlackSiteEntranceItemClicked:RemoveListener(GuideEvtLogic._OnBlackSiteEntranceItemClicked)
    Module.BlackSite.Config.Events.evtBlackSiteConditionsUpgradeBtnClicked:RemoveListener(GuideEvtLogic._OnBlackSiteConditionsUpgradeBtnClicked)

    Module.Market.Config.Events.evtMarketMainPanelTabChanged:RemoveListener(GuideEvtLogic._OnMarketMainPanelTabChanged)
    Module.Market.Config.Events.evtMarketSkinPageOnShowBegin:RemoveListener(GuideEvtLogic._OnMarketSkinPageOnShowBegin)
    Module.Market.Config.Events.evtMarketSkinPageOnHideBegin:RemoveListener(GuideEvtLogic._OnMarketSkinPageOnHideBegin)

    Module.Quest.Config.evtQuestMainPanelOnHideBegin:RemoveListener(GuideEvtLogic._OnQuestMainPanelOnHideBegin)

    -------- 启动新手引导的事件
    UDFMGameplayGlobalDelegates.Get(gameInst).OnGuideSolPlayerFreeOperate:Remove(GuideEvtLogic._OnGuideSolPlayerFreeOperate)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnSeamlessEnterFirstSeqStart:Remove(GuideEvtLogic._OnSeamlessEnterFirstSeqStart)
    UDFMGameplayGlobalDelegates.Get(gameInst).OnIrisEnterStageChange:Remove(GuideEvtLogic._OnIrisEnterStageChange)
    UGPGameplayGlobalDelegates.Get(gameInst).OnStartLocalFocus:Remove(GuideEvtLogic._OnStartLocalFocus)
    UGPGameplayGlobalDelegates.Get(gameInst).OnStopLocalFocus:Remove(GuideEvtLogic._OnStopLocalFocus)

    UDFMIrisEnterSubsystem.Get(GetWorld()).OnSeamlessSequencePreSpawn:Remove(GuideEvtLogic._OnSeamlessSequencePreSpawn)


    Module.Collection.Config.Events.evtOnCollectionMainPanelTabIndexChanged:RemoveListener(GuideEvtLogic._OnCollectionMainPanelTabIndexChanged)
    Module.Collection.Config.Events.evtOnCollectionMainPanelTertiaryTabIndexChanged:RemoveListener(GuideEvtLogic._OnCollectionMainPanelTertiaryTabIndexChanged)
    Module.Collection.Config.Events.evtHangingPageItemClicked:RemoveListener(GuideEvtLogic._OnCollectionHangingPageItemClicked)



    Module.GameMode.Config.evtOnPreDoSwitchModeOperation:RemoveListener(GuideEvtLogic._OnPreDoSwitchModeOperation)

    Module.Social.Config.Events.evtOnInvitePopViewOnShowBegin:RemoveListener(GuideEvtLogic._OnInvitePopViewOnShowBegin)

    Module.Store.Config.evtActivityMandelDrawOnlyOnShowBegin:RemoveListener(GuideEvtLogic._OnActivityMandelDrawOnlyOnShowBegin)

    if DFHD_LUA == 1 then
        Server.MatchServer.Events.evtRealBoarding:RemoveListener(GuideEvtLogic._OnRealBoarding)
        Module.IrisSafeHouse.Config.evtTopBarFinished:RemoveListener(GuideEvtLogic._OnTopBarFinished)
        Module.BattlefieldEntry.Config.evtTopBarFinished:RemoveListener(GuideEvtLogic._OnMPTopBarFinished)

        --Module.IrisSafeHouse.Config.flowEvtToEnterSafeHouse:RemoveListener(GuideEvtLogic._OnBeginEnterSafeHouse) -- 准备切换进安全屋
        --Module.IrisSafeHouse.Config.evtTopBarSafehouseAfterInit:RemoveListener(GuideEvtLogic._OnTopBarSafehouseAfterInit)
        Module.IrisSafeHouse.Config.evtTopBarSafehouseBeforeUnInit:RemoveListener(GuideEvtLogic._OnTopBarSafehouseBeforeUnInit)
        Module.BattlefieldEntry.Config.evtTopBarMpBeforeUnInit:RemoveListener(GuideEvtLogic._OnTopBarMpBeforeUnInit)

        --Module.IrisSafeHouse.Config.evtTopBarSafehouseClicked:RemoveListener(GuideEvtLogic._OnTopBarSafehouseClicked)
        Module.IrisSafeHouse.Config.evtFinishEnterSafeHouse:RemoveListener(GuideEvtLogic._RealEnterSafeHouse)

        -- pc输入设备的切换
        UGPInputDelegates.Get(gameInst).OnInputTypeChanged:Remove(GuideEvtLogic._OnInputTypeChanged)
    else
        -------- server事件

        -------- gameplay 事件
        GuideConfig.EGuideEvent.evtGuideShowInsuranceTips:RemoveListener(GuideEvtLogic._OnGuideShowInsuranceTips)
        GuideConfig.EGuideEvent.evtCancelGotoInsurance:RemoveListener(GuideEvtLogic._OnGuideCancelGotoInsurance)

        -- GuideConfig.EGuideEvent.evtOpenWinTransToInventory:RemoveListener(GuideEvtLogic._OnGuideTransToInventory)

        -------- 其他事件
        Module.SandBoxMap.Config.evtSandBoxShow:RemoveListener(GuideEvtLogic._OnSandBoxMapShow)
        Module.ArmedForce.Config.evtArmedForceMainOpenFinish:RemoveListener(GuideEvtLogic._OnArmedForceMainShow)
        Module.ArmedForce.Config.evtSelectionEquipMainOpenFinish:RemoveListener(GuideEvtLogic._OnSelectionEquipShow)
        Module.IrisSafeHouse.Config.evtSafeHouseHUDOpenAnimStart:RemoveListener(GuideEvtLogic._OnSafeHouseHUDOpenStart)
        Module.IrisSafeHouse.Config.evtPrepareBtnClicked:RemoveListener(GuideEvtLogic._OnPrepareBtnClicked)
        Module.Inventory.Config.Events.evtWarehouseExpansionOpenFinish:RemoveListener(GuideEvtLogic._OnWarehouseExpansionShow)
        Module.IrisSafeHouse.Config.evtSyncMainOpenFinish:RemoveListener(GuideEvtLogic._OnevtSyncMainShow)
        Module.IrisSafeHouse.Config.evtSyncMainOpenStart:RemoveListener(GuideEvtLogic._OnevtSyncMainShowStart)
        Module.ArmedForce.Config.evtSlotViewClicked:RemoveListener(GuideEvtLogic._OnSlotViewClicked)
        Module.IrisSafeHouse.Config.evtSyncWarehouseBtnClicked:RemoveListener(GuideEvtLogic._OnSyncWarehouseBtnClicked)
        Module.IrisSafeHouse.Config.evtSyncQuestBtnClicked:RemoveListener(GuideEvtLogic._OnSyncQuestBtnClicked)
        Module.Quest.Config.evtQuestChapterBtnClicked:RemoveListener(GuideEvtLogic._OnQuestChapterBtnClicked)
        Module.Quest.Config.evtQuestItemLoadFinish:RemoveListener(GuideEvtLogic._OnQuestItemShow)
        Module.Quest.Config.evtQuestDetailBtnClicked:RemoveListener(GuideEvtLogic._OnQuestDetailBtnClicked)
        Module.Quest.Config.evtQuestDetailLoadFinish:RemoveListener(GuideEvtLogic._OnQuestDetailShow)
        Module.Quest.Config.evtQuestStateChange:RemoveListener(GuideEvtLogic._OnQuestStateChange)
        Module.ArmedForce.Config.evtArmedForceWarehouseBtnClicked:RemoveListener(GuideEvtLogic._OnArmedForceWarehouseBtnClicked)
        Module.Inventory.Config.Events.evtWareHouseTransferEnd:RemoveListener(GuideEvtLogic._OnWareHouseTransferEnd)
        Module.IrisSafeHouse.Config.evtSyncShopBtnClicked:RemoveListener(GuideEvtLogic._OnSyncShopBtnClicked)
        Module.Shop.Config.evtShopSpecMerchanBtnClicked:RemoveListener(GuideEvtLogic._OnShopSpecMerchanBtnClicked)
        Module.Shop.Config.evtShopMainOpenFinish:RemoveListener(GuideEvtLogic._OnShopMainShow)

        Module.CommonWidget.Config.Events.evtItemDoubleClicked:RemoveListener(GuideEvtLogic._OnItemDoubleClick)
        Module.GCloudSDK.Config.Events.evtHopeMainUIOpen:RemoveListener(GuideEvtLogic._OnOpenPauseGuideUI)
        Module.GCloudSDK.Config.Events.evtHopeMainUIClose:RemoveListener(GuideEvtLogic._OnClosePauseGuideUI)
        Module.Shop.Config.evtSelectedSItemChanged:RemoveListener(GuideEvtLogic._OnShopItemSelected)


        Module.ItemDetail.Config.evtItemDetailInputBoxBtnClicked:RemoveListener(GuideEvtLogic._OnItemDetailSliderBtnClicked)
        Module.ItemDetail.Config.evtItemDetailPanelIsFoldChange:RemoveListener(GuideEvtLogic._OnItemDetailPanelIsFoldChange)

        -- Module.Reward.Config.Events.evtChooseInventoryReward:RemoveListener(GuideEvtLogic._OnRewardClose)
        Module.Reward.Config.Events.evtOpenRewardPanel:RemoveListener(GuideEvtLogic._OnRewardOpen)
        Module.Reward.Config.Events.evtCloseRewardPanel:RemoveListener(GuideEvtLogic._OnRewardClose)

        Module.ArmedForce.Config.evtProcessBtnClicked:RemoveListener(GuideEvtLogic._OnArmedForceProcessBtnClicked)


        Module.Report.Config.evtReportMainPanelOpen:RemoveListener(GuideEvtLogic._OnReportMainPanelOpen)
        Module.Report.Config.evtReportMainPanelClose:RemoveListener(GuideEvtLogic._OnReportMainPanelClose)

        Server.ShopServer.Events.evtShopItemReGenerated:RemoveListener(GuideEvtLogic._OnShopItemReGenerated)
        Server.QuestServer.Events.evtQuestAcceptSuccess:RemoveListener(GuideEvtLogic._OnQuestAcceptSuccess)
        Server.QuestServer.Events.evtQuestGetReward:RemoveListener(GuideEvtLogic._OnQuestGetReward)
        Module.ArmedForce.Config.evtAssemblySquadPickClose:RemoveListener(GuideEvtLogic._OnAssemblySquadPickClose)
        Module.ArmedForce.Config.evtOnAssemblySelectionOutAnimBegin:RemoveListener(GuideEvtLogic._OnAssemblySelectionOutAnimBegin)
        Module.ArmedForce.Config.evtAssemblySquadPickConfirmClicked:RemoveListener(GuideEvtLogic._OnAssemblySquadPickConfirmClicked)

        Module.SandBoxMap.Config.evtConfrimTarget:RemoveListener(GuideEvtLogic._OnSandBoxConfrimTarget)
        Module.SandBoxMap.Config.evtDetailInAniFinished:RemoveListener(GuideEvtLogic._OnSandBoxDetailShowFinished)


        Module.ArmedForce.Config.evtSubViewChanged:RemoveListener(GuideEvtLogic._OnAssemblySubViewChanged)
        Module.ArmedForce.Config.evtArmedForceQuickOperationOpenFinish:RemoveListener(GuideEvtLogic._OnAssemblyMedicineOpenFinish)
        Module.ArmedForce.Config.evtArmedForceQuickOperationConfirmClicked:RemoveListener(GuideEvtLogic._OnAssemblyMedicineConfirmClicked)
        Module.Settlement.Config.Events.evtEvacuateExpViewOpen:RemoveListener(GuideEvtLogic._OnEvacuateExpViewOpen)
        Module.ItemDetail.Config.evtItemDetailSellResult:RemoveListener(GuideEvtLogic._OnItemSellResult)
        Module.CommonTips.Config.Events.evtCommonWindowOpenFinish:RemoveListener(GuideEvtLogic._OnCommonWindowOpenFinish)
        Server.InventoryServer.Events.evtPostSortMultiPos:RemoveListener(GuideEvtLogic._OnInventorySorted)





        --Module.IrisSafeHouse.Config.evtModeHallShow:RemoveListener(GuideEvtLogic._OnModeHallShow)
        Module.IrisSafeHouse.Config.evtModeHallOpenAnimFinish:RemoveListener(GuideEvtLogic._OnModeHallOpenAnimFinish)
        Module.IrisSafeHouse.Config.evtModeHallClose:RemoveListener(GuideEvtLogic._OnModeHallClose)
        Module.IrisSafeHouse.Config.evtModeHallChangeMode:RemoveListener(GuideEvtLogic._OnModeHallChangeMode) 
        Module.Login.Config.Events.evtOnRegisterSuccess:RemoveListener(GuideEvtLogic._OnRenameFinished)

        Module.Reward.Config.Events.evtOpenUnlockUI:RemoveListener(GuideEvtLogic._OnUnlockUIOpen)
        Module.Reward.Config.Events.evtCloseUnlockUI:RemoveListener(GuideEvtLogic._OnUnlockUIClose)

        Module.Login.Config.Events.evtOnRegisterFinish:RemoveListener(GuideEvtLogic._OnRegisterFinish)
        Module.BattlefieldEntry.Config.evtOnVedioPlayStart:RemoveListener(GuideEvtLogic._OnMpVedioPlayStart)
        Module.BattlefieldEntry.Config.evtOnVedioPlayEnd:RemoveListener(GuideEvtLogic._OnMpVedioPlayEnd)
        Module.BattlefieldEntry.Config.evtOnConfirmModeBtnClick:RemoveListener(GuideEvtLogic._OnMpConfirmModeBtnClick)
        Module.BattlefieldEntry.Config.evtSelectModeUIOpen:RemoveListener(GuideEvtLogic._OnMpSelectModeUIOpen)
        Module.BattlefieldEntry.Config.evtSelectModeUIShowAnimFinish:RemoveListener(GuideEvtLogic._OnMpSelectModeUIShowAnimFinish)

        --- 折叠屏
        UDFMGameNotch.Get(GetGameInstance()).OnFoldStatusChanged:Remove(GuideEvtLogic._OnFoldStatusChanged)

    end

    if IsInEditor() then
		ULuaSubsystem.Get().OnNotifyLuaResolutionResized:Remove(GuideEvtLogic._OnNotifyResolutionResized)
	end
end

function GuideEvtLogic.AddInGameGuideDelegate_SOL(caller)
    if GuideEvtLogic.bHasRegisterSolGameGuideDelegate then return end
    -- log("RegisterSolGameGuideDelegate")
    local character = UGameplayBlueprintHelper.GetLocalFocusCharacterBase(GetWorld())
    if not isvalid(character) then
        logwarning("RegisterSolGameGuideDelegate character is invalid")
        return
    end

    if not ULAI.IsSubClassOf(character,ADFMCharacter) then
        logwarning("RegisterSolGameGuideDelegate character is not ADFMCharacter")
        return
    end

    -- Kill
    -- character.OnPlayerKill:Add(GuideEvtLogic._OnPlayerKill)

    -- Heath attributes
    local healthComp =  character:GetComponentByClass(UDFMHealthDataComponent)
    if isvalid(healthComp) then
        GuideEvtLogic.cacheHealthCom = healthComp
        healthComp.OwnerHelmetArmorChanged:Add(GuideEvtLogic._OnHelmetArmorChanged)
        healthComp.OwnerArmorChanged:Add(GuideEvtLogic._OnArmorInfoChanged)
        healthComp.OwnerHealthChanged:Add(GuideEvtLogic._OnCharacterHealthChange)
        healthComp.OwnerIsAliveStateChanged:Add(GuideEvtLogic._OnCharacterIsAliveStateChanged)
    end

    -- Buff
    character.OnClientNotifyAddBuff:Add(GuideEvtLogic._OnClientNotifyAddBuff)
    character.OnClientNotifyRemoveBuff:Add(GuideEvtLogic._OnClientNotifyRemoveBuff)


    -- Hud
    local hudToolbarEvents = Module.HUDToolBar.Config.Events;
    hudToolbarEvents.evtOnRouletteMain_PC_AnimShow:AddListener(GuideEvtLogic._OnRouletteMain_PC_AnimShow)

    GuideEvtLogic.bHasRegisterSolGameGuideDelegate = true
end

function GuideEvtLogic.RemoveInGameGuideDelegate_SOL(caller)
    if not GuideEvtLogic.bHasRegisterSolGameGuideDelegate then return end
    -- log("RemoveSolGameGuideDelegate")

    local character = UGameplayBlueprintHelper.GetLocalFocusCharacterBase(GetWorld())
    if not isvalid(character) then
        return
    end

    if not ULAI.IsSubClassOf(character,ADFMCharacter) then
        return
    end
    
    -- Kill
    -- character.OnPlayerKill:Remove(GuideEvtLogic._OnPlayerKill)

    -- health attributes
    local healthComp =  character:GetComponentByClass(UDFMHealthDataComponent)
    if isvalid(healthComp) then
        healthComp.OwnerHelmetArmorChanged:Remove(GuideEvtLogic._OnHelmetArmorChanged)
        healthComp.OwnerArmorChanged:Remove(GuideEvtLogic._OnArmorInfoChanged)
        healthComp.OwnerHealthChanged:Remove(GuideEvtLogic._OnCharacterHealthChange)
        healthComp.OwnerIsAliveStateChanged:Remove(GuideEvtLogic._OnCharacterIsAliveStateChanged)
    end

    -- buff
    character.OnClientNotifyAddBuff:Remove(GuideEvtLogic._OnClientNotifyAddBuff)
    character.OnClientNotifyRemoveBuff:Remove(GuideEvtLogic._OnClientNotifyRemoveBuff)

    -- hud
    local hudToolbarEvents = Module.HUDToolBar.Config.Events;
    hudToolbarEvents.evtOnRouletteMain_PC_AnimShow:RemoveListener(GuideEvtLogic._OnRouletteMain_PC_AnimShow)

    GuideEvtLogic.bHasRegisterSolGameGuideDelegate = false
end



--endregion
-------------------------------



-------------------------------
--region Pc输入相关

-- now is handled by  guide widget
-- function GuideEvtLogic.BindPCInputAction()
--     if GuideEvtLogic._hActionGuideSkip then
--         err("_hActionGuideSkip was bound, REPEAT!!!")
--     else
--         local inputMonitor = Facade.UIManager:GetInputMonitor()
--         GuideEvtLogic._hActionGuideSkip = inputMonitor:AddDisplayActionBinding("GuideSkip", 
--                                             EInputEvent.IE_Pressed, 
--                                             self._OnPCSkipGuidePressed,
--                                             self, 
--                                             EDisplayInputActionPriority.Always)
--     end
-- end

-- function GuideEvtLogic.UnbindPCInputAction()
--     if not GuideEvtLogic._hActionGuideSkip then
--         log("_hActionGuideSkip was not bind")
--     else
--         local inputMonitor = Facade.UIManager:GetInputMonitor()
--         inputMonitor:RemoveDisplayActoinBingingForHandle(GuideEvtLogic._hActionGuideSkip)
--         GuideEvtLogic._hActionGuideSkip = nil
--     end
-- end




function GuideEvtLogic._OnPCSkipGuidePressed()
    log("_OnPCSkipGuidePressed")
end

--endregion
-------------------------------



function GuideEvtLogic._OnGuideRaidPlayerFreeOperate()
    log("_OnGuideRaidPlayerFreeOperate")
    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.raidInfoIntro) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.raidInfoIntro])
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._DoGuideSolPlayerFreeOperate() 
    log("_DoGuideSolPlayerFreeOperate")
    -- BUG: the IsGameAndSolMode() will return false when in the LobbyToGame/SafeHouseToGame gameflow
    -- we should do a delay call to avoid that
    local _realTryStartGuide = function()
        local readyStageList = {}
        if GuideLogic.IsGameAndSolMode() then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solScriptMatchMap)
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solResourcePoint)
        end
        GuideLogic.TryStartGuideStage(readyStageList)
    end
    Server.GuideServer:RequestSolMatchCountInfo(_realTryStartGuide)


    Module.Guide:ProcessInGame()

    if GuideLogic.IsGameAndSolMode() then
        GuideLogic.TryStartTimerForGuideInGame() -- deprecated
        GuideEvtLogic.AddInGameGuideDelegate_SOL()
    end
end

function GuideEvtLogic._OnGuideSolPlayerFreeOperate()
    log("_OnGuideSolPlayerFreeOperate")
    GuideEvtLogic._DoGuideSolPlayerFreeOperate()
end



function GuideEvtLogic._OnGuideRemindEscape()
    log("_OnGuideRemindEscape")
    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solEscapeMark) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solEscapeMark])
    end



    GuideLogic.TryStartGuideStage(readyStageList)
    GuideLogic.StopTimerForGuideInGame()
end

function GuideEvtLogic._OnGuideRemindEscapeSimple()
    log("_OnGuideRemindEscapeSimple")
    local readyStageList = {}

    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solEscapeTime) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solEscapeTime])
    end

    -- replace last escape time guide
    local curGuideStage=  Module.Guide.Field:GetCurGuideStage()
    if Module.Guide:IsGuiding() and  curGuideStage and curGuideStage == GuideConfig.EGuideStage.solEscapeTime then
        Module.Guide:StopAllGuide(true)
    end

    GuideLogic.TryStartGuideStage(readyStageList)

    GuideLogic.StopTimerForGuideInGame() -- NONEED
end

function GuideEvtLogic._OnGuideShowInsuranceTips()
    log("_OnGuideShowInsuranceTips")
    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.gotoInsurance) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.gotoInsurance])
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end


-- function GuideEvtLogic._OnGuideTransToInventory()
--     log("_OnGuideTransToInventory")
--     local readyStageList = {}
--     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.firstTransToInventory) then
--         table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.firstTransToInventory])
--     end
--     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.transToInventory) then
--         table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.transToInventory])
--     end
--     GuideLogic.TryStartGuideStage(readyStageList)
-- end

function GuideEvtLogic._OnGuideCancelGotoInsurance()
    log("_OnGuideCancelGotoInsurance")
    GuideEvtLogic.OnGuideOpenSafehouse()
end

function GuideEvtLogic._OnGuideStartMpGame(curGroup)
    log("_OnGuideStartMpGame")
    local readyStageList = {}
    if curGroup == Server.GameModeServer.EnumGroupID.CLASSIC then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpInfoIntro) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpInfoIntro])
        end
    elseif curGroup == Server.GameModeServer.EnumGroupID.OFFDEF then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpOffdefIntro) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpOffdefIntro])
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end


function GuideEvtLogic._OnGuideLooting()
    -- log("_OnGuideLooting")
    GuideConfig.EGuideEvent.evtOpenLootingBagUI:Invoke()

    local curLootData = Server.LootingServer:GetCurrentSelectorData()
    if not curLootData then
        GuideConfig.EGuideEvent.evtOpenBagWithoutLooting:Invoke()

        log("none loot data -> check helth and armor")
        if IsHD() then
        else
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solRepairEquipment) then
                -- 判断是否有装备耐久不足
                local bNeedRepairEquipment = false
                local itemSlot = Server.InventoryServer:GetSlot(ESlotType.Helmet, ESlotGroup.Player)
                if itemSlot then
                    local item = itemSlot:GetEquipItem()
                    if item then
                        ---@type EquipmentFeature
                        local itemFeature = item:GetFeature()
                        if itemFeature then
                            if itemFeature.maxDurability > 0 and (itemFeature.curDurability / itemFeature.maxDurability < GuideEvtLogic.guideArmorPercent) then
                                bNeedRepairEquipment = true
                            end
                        end
                    end
                end
                if not bNeedRepairEquipment then
                    local itemSlot = Server.InventoryServer:GetSlot(ESlotType.BreastPlate, ESlotGroup.Player)
                    if itemSlot then
                        local item = itemSlot:GetEquipItem()
                        if item then
                            ---@type EquipmentFeature
                            local itemFeature = item:GetFeature()
                            if itemFeature then
                                if itemFeature.maxDurability > 0 and (itemFeature.curDurability / itemFeature.maxDurability < GuideEvtLogic.guideArmorPercent) then
                                    bNeedRepairEquipment = true
                                end
                            end
                        end
                    end
                end
                if bNeedRepairEquipment then
                    local readyStageList = {}
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solRepairEquipment])
                    GuideLogic.TryStartGuideStage(readyStageList)
                end
            end
        end

        return
    end

    -- 从第二局起，记录下玩家是否打开过loot界面
    local type = curLootData.type
    if type == EGamePickupType.SceneBox or type == EGamePickupType.NearbyDeadBody or type == EGamePickupType.DropContainer then
        if not Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideLootBtnClickedInGame) then
            if Server.GuideServer:GetMatchCount(MatchGameRule.SOLGameRule) > 0 then
                Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideLootBtnClickedInGame, true)
            end
        end
    end

    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.lootingGuide) or
        GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.lootingDeadBody)
    then
        GuideEvtLogic._TryStartTutorialLevelLootingGuide(1)
    end

    -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solNewPlayerLevel_FreeSafeBoxLooting) then
    --     GuideEvtLogic._TryStartTutorialLevelLootingGuide(2)
    -- end
end



function GuideEvtLogic._TryStartTutorialLevelLootingGuide(cond)
    -- wait for lootingAiIndexIngame replica
    if cond == 1 then
        Timer.DelayCall(0.6, function()
            local curLootData = Server.LootingServer:GetCurrentSelectorData()
            if not curLootData then
                err("looting gudie no loot data")
                return
            end

            if Module.Guide.Field.lootingInGameOpenState == false then
                err("looting guide: lootingInGameOpenState == false, register frame task wait for open")
                return
            end

            ---@type FGuideStageClientOnlyConfig[]
            local readyStageList = {}
            local pickupType = curLootData.type
            local index = Module.Guide.Field.lootingAiIndexInGame
            log("looting guide: pickupType = ", pickupType, " index = ", index)
            if IsInEditor() and index ==0 then
                -- mock
                index = GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.lootingDeadBody) and 1 or 2
            end


            if pickupType == EGamePickupType.NearbyDeadBody then
                if index == 1 then
                    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.lootingDeadBody)
                elseif index == 2 then
                    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.lootingGuide)
                end
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        end)
    elseif cond == 2 then
        -- wait for the looting data sync to views
        Timer.DelayCall(0.1, function()
            local curLootData = Server.LootingServer:GetCurrentSelectorData()
            if not curLootData then
                err("looting gudie no loot data")
                return
            end
            if Module.Guide.Field.lootingInGameOpenState == false then
                err("looting guide: lootingInGameOpenState == false, register frame task wait for open")
                return
            end

            ---@type FGuideStageClientOnlyConfig[]
            local readyStageList = {}
            local pickupType = curLootData.type
            log("looting guide: pickupType = ", pickupType)

            if pickupType == EGamePickupType.SceneBox then
                -- 免保?
                if GuideLogic_CheckCondition.CanStartNewPlayerLevelFreeSafeBoxLootingGuide(curLootData) then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solNewPlayerLevel_FreeSafeBoxLooting])
                end
            end

            GuideLogic.TryStartGuideStage(readyStageList)
        end)
    end
end


GuideEvtLogic._OnStackUIChanged = function (uiNavId, OpType)
    if OpType == EStackAction.Pop then
        GuideConfig.EGuideEvent.evtStackUIClose:Invoke(uiNavId)
    elseif OpType == EStackAction.Push then
        GuideConfig.EGuideEvent.evtStackUIOpen:Invoke(uiNavId)
    end

    -- 是否在对局中
    if Server.AccountServer:IsPlayerInGame() then
        return
    end

    -- 是否在局外
    local gf = Facade.GameFlowManager:GetCurrentGameFlow()
    if gf == EGameFlowStageType.SafeHouse then
        -- 
        GuideEvtLogic.OnGuideOpenSafehouse("_OnStackUIChanged")
        -- 处理下pc的topbar
        if GuideLogic.IsTopbarEnable() and GuideLogic.IsTopbarVisible() then
            if IsHD() then
                if not GuideLogic.IsWarehouseOpened() then
                    Module.Guide:OpenGuideHDWeakClickUI(148)
                else
                    Module.Guide:CloseGuideHDWeakClickUI(148)
                end
                if not GuideLogic.IsDepartmentOpened() then
                    Module.Guide:OpenGuideHDWeakClickUI(149)
                else
                    Module.Guide:CloseGuideHDWeakClickUI(149)
                end
            end
        end
    elseif gf == EGameFlowStageType.Lobby then
    end

end

GuideEvtLogic._OnGuideEnd = function (endGuideStageId, tryPlayNextStageId)
    --体系化教学
    GuideLogic.HandleStructTutorialEnd(endGuideStageId, tryPlayNextStageId)

    --判断是否完成新手教程，解锁平台成就
    --BEGIN MODIFICATION - liuyi_b
    if PLATFORM_GEN9 and endGuideStageId == Module.Guide.Config.EGuideStage.solMarkEarnings then
        local UDFMAchievementsManager = import "DFMAchievementsManager"
        local DFMAchievementsManager = UDFMAchievementsManager.Get(GetWorld())
        if DFMAchievementsManager then
            DFMAchievementsManager:GrantAchievementByConfigIndex(1, 100)
        end
    end
    --END MODIFICATION - liuyi_b

    local guideField = Module.Guide.Field
    -- TODO
    --if tryPlayNextStageId then
    --    if GuideLogic.CheckMeetGuideStageStartCondition(tryPlayNextStageId) then
    --        GuideLogic.TryStartGuideStage({guideField:GetGuideStageCfg(tryPlayNextStageId)})
    --    end
    --end

    if #guideField.inGameWaitPlayQueue > 0 then
        GuideLogic.TryStartGuideStage(guideField.inGameWaitPlayQueue)
        guideField.inGameWaitPlayQueue = {}
    end

    ------ 某些引导结束的时候，有可能需要判断后续是否启动其他引导，在此判断
    -- looting引导结束，有可能启动撤离相关引导
    --if endGuideStageId == GuideConfig.EGuideStage.lootingGuide then
    --    GuideEvtLogic._OnGuideRemindEscape()
    --end

    ------ 判断是否尝试启动局外新手引导
    -- 是否在对局中
    if Server.AccountServer:IsPlayerInGame() then
        return
    end

    -- 是否在局外
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then

        -- 
        GuideEvtLogic.OnGuideOpenSafehouse()

        -- 根据当前显示的最上层栈ui，判断是否需要接着上一个引导继续触发引导
        local controller = Facade.UIManager:GetLayerControllerByType(EUILayer.Stack)
        local topStackViewId = controller:GetCurrentViewId()
        local topStackView = controller:GetCurrentView()
        if DFHD_LUA == 1 then
            if not topStackViewId or not topStackView then return end
            if topStackView:IsInHideState() or topStackView:IsInHideBeginState() then
                return
            end
            if topStackViewId == UIName2ID.WarehouseMain then
                -- 仓库主界面的一些引导
                GuideEvtLogic._TryStartWarehouseGuide("_OnGuideEnd", { endGuideStageId = endGuideStageId })
            elseif topStackViewId == UIName2ID.AssemblyHDMainPanel then
                -- 备战界面的一些引导
                GuideEvtLogic._TryStartArmedForceMainGuide("_OnGuideEnd")
            elseif topStackViewId == UIName2ID.SandBoxMapView then
                -- 战略板界面的一些引导
                GuideEvtLogic._TryStartSandBoxGuide()
            end
        end
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        --[[
        --story=123705207 【手游|PC】【前台】【新手引导】【She2】大战场结算改版阻塞引导处理 https://tapd.woa.com/r/t?id=123705207&type=story
        if DFHD_LUA == 1 then
            if endGuideStageId == GuideConfig.EGuideStage.mpExpUp
            -- or  endGuideStageId == GuideConfig.EGuideStage.mpWeaponExpUp 
            then
                GuideEvtLogic._TryStartMpMainGuide("OnGuideEnd", endGuideStageId)
            end
        else
        ]]--
        if endGuideStageId == GuideConfig.EGuideStage.mpExpUp then
            GuideEvtLogic._TryStartMpMainGuide("OnGuideEnd", endGuideStageId)
        end
        -- end
    end

    Module.Guide.Config.EGuideEvent.evtPostOnGuideEnd:Invoke(endGuideStageId, tryPlayNextStageId)
end



GuideEvtLogic._OnGuideUpdate = function(endGuideStageId, tryPlayNextStageId)
    log("_OnGuideUpdate")
    local guideField = Module.Guide.Field

    ------ 判断是否尝试启动局外新手引导
    -- 是否在对局中
    if Server.AccountServer:IsPlayerInGame() then
        return
    end

    -- 是否在局外
    if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then
        return
    end

    GuideEvtLogic.OnGuideOpenSafehouse()
end

function GuideEvtLogic._OnGuideClick(...)
    log("_OnGuideClicked ", ...)
    GuideConfig.EGuideEvent.gdEvtOnGuideClicked:Invoke(...)
end


function GuideEvtLogic._OnPlayerReturnDailyMatchPopupJumpToMpHall()

    local bInTeam = Server.AccountServer:IsInTeam()
    log("_OnPlayerReturnDailyMatchPopupJumpToMpHall",bInTeam)
    if bInTeam then
        return
    end
    local readyStageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpReflowBeginMatchPulling)
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnCollectionListPanelOnShowBegin()
    log("_OnCollectionListPanelOnShowBegin")
    local readyStageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solBlackSiteGameplay_CollectionBook)
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnEvacuationResultInfoNextFlowClicked()
    local  solMatchcount = GuideLogic.GetMatchCount(MatchGameRule.SOLGameRule)
    local bNewPlayerMatchFinished = GuideLogic.IsNewPlayerMatchFinish()
    log("_OnEvacuationResultInfoNextFlowClicked", solMatchcount, bNewPlayerMatchFinished)
    if bNewPlayerMatchFinished then
        Module.GCloudSDK:OnNewPlayerGuideEndReport()
    end
end




function GuideEvtLogic._OnDeathDamageInfoViewOnOpen(bSettlement)
    log("_OnDeathDamageInfoViewOnOpen", bSettlement, IsHD())

    if not bSettlement then
        return
    end


    local bRecordUpdate = false
    local bNeedSkillAgainstGuide = GuideLogic_CheckCondition.NeedSkillAgainstGuide()

    if bNeedSkillAgainstGuide then
        bRecordUpdate = GuideLogic.ProcessCurrentMatchSkillDataFlow()
    end
    log("bNeedSkillAgainstGuide = ", bNeedSkillAgainstGuide, " bRecordUpdate = ", bRecordUpdate)

    if IsHD() then
        if bNeedSkillAgainstGuide and bRecordUpdate then
            if GuideLogic.GetFirstAvaliableSkillAgainstEntry() then
                local readyStageList = {}
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solSkillAgainst)
                GuideLogic.TryStartGuideStage(readyStageList)
            end
        end
    end
end



function GuideEvtLogic._OnLootingAiTagChanged()
    log("_OnLootingAiTagChanged")
    -- GuideEvtLogic._TryStartTutorialLevelLootingGuide()
end

-- 取消重连游戏时触发，新手关游戏要刷新一下玩家引导数据
function GuideEvtLogic._OnCancelReconnetBtnClick()
    log("_OnCancelReconnetBtnClick")

    -- 这段逻辑暂时看不出来有什么用了，先屏蔽
    if true then return end

    if GuideLogic.GetCurNewPlayerGuideStage() == GuideConfig.EGuideStage.newPlayerGuideStage1 then
        Module.Reconnect.Config.Events.evtOnCancelReconnectMatchRes:AddListener(GuideEvtLogic._OnCancelReconnetRes)
        GuideLogic.SetOnlyShowGuideState(true, GuideConfig.EGuideShowUIFlag.Reconnect)
    end
end

function GuideEvtLogic._OnCancelReconnetRes()
    log("_OnCancelReconnetRes")
    Module.Reconnect.Config.Events.evtOnCancelReconnectMatchRes:RemoveListener(GuideEvtLogic._OnCancelReconnetRes)
    local function _delayReq()
        local callback = function ()
            GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.Reconnect)
            GuideEvtLogic.OnGuideOpenSafehouse()
        end
        Server.GuideServer:RequestSolMatchCountInfo(callback)
    end
    Timer.DelayCall(1, _delayReq)   -- 等1s，防止结算通知时序对不上
end




local _blacksiteSpeficCondition = GuideLogic
    .switch()
    .case(GuideConfig.EGuideStage.solBlackSiteUpgrade_AmorPlatform, GuideLogic_CheckCondition.CanStartSolBlackSiteUpgradeAmorPlatformGuide)
    .case(GuideConfig.EGuideStage.solBlackSiteProduce_Amor, GuideLogic_CheckCondition.CanStartSolBlackSiteProduceAmorGuide)
    .case(GuideConfig.EGuideStage.solBlackSiteUpgrade_CollectionRoom, GuideLogic_CheckCondition.CanStartSolBlackSiteUpgradeCollectionRoomGuide)
    .case(GuideConfig.EGuideStage.solBlackSiteGameplay_CollectionRoom, GuideLogic_CheckCondition.CanStartSolBlackSiteGameplayCollectionRoomGuide)
    .default(function() return true end)

local _tryHighlightBlackSiteTab = function(curStackUIId)
    if not GuideLogic.IsTopbarEnable() then return end
    if curStackUIId == UIName2ID.BlackSiteEntrance or curStackUIId == UIName2ID.BlackSiteMainPanel then return end
    if Facade.GameFlowManager:GetCurrentGameFlow() ~=  EGameFlowStageType.SafeHouse then return end

    local _BlackSiteGuides = {
        Module.Guide.Config.EGuideStage.solBlackSiteUpgrade_WareHouse,
        Module.Guide.Config.EGuideStage.solBlackSiteProduce_Bullet,
        Module.Guide.Config.EGuideStage.solBlackSiteUpgrade_AmorPlatform,
        Module.Guide.Config.EGuideStage.solBlackSiteProduce_Amor,
        Module.Guide.Config.EGuideStage.solBlackSiteUpgrade_CollectionRoom,
        Module.Guide.Config.EGuideStage.solBlackSiteGameplay_CollectionRoom,
    }

    if GuideLogic.IsGuiding() then
        -- do not highlight when already in these guides
        local curGuideStageId = Module.Guide.Field:GetCurGuideStage()
        if table.contains({ -1, GuideConfig.EGuideStage.newPlayerGuideStage1, }, curGuideStageId) then
            return
        end
        if table.contains(_BlackSiteGuides, curGuideStageId) then
            return
        end
    end


    for _, stageId in ipairs(_BlackSiteGuides) do
        if GuideLogic.CheckMeetGuideStageStartCondition(stageId) then
            if _blacksiteSpeficCondition.process(stageId) == true then
                GuideLogic.ToggleTopbarBlackSiteAnim(true)
                return
            end
        end
    end

    GuideLogic.ToggleTopbarBlackSiteAnim(false)
end

-- 打开3d安全屋的时候触发引导
function GuideEvtLogic.OnGuideOpenSafehouse(fromStr)
    log("OnGuideOpenSafehouse ", fromStr)
   
    if IsHD() then
        if Server.GuideServer.FlagPCEnableGuideIn3dSafeHouse then
            -- 不做拦截
            log("OnGuideOpenSafehouse, FlagPCEnableGuideIn3dSafeHouse is true")
        else
            -- 是否pc侧 topbar准备完毕
            if not GuideLogic.IsTopbarEnable() then
                log("IsTopbarEnable, false")
                return
            end
        end
    else
         -- 是否处于3d安全屋主界面
        if not GuideLogic.CheckSafeHouseIsTop() then
            log("CheckSafeHouseIsTop, is not top ui")
            return
        end
    end

    local curGF = Facade.GameFlowManager:GetCurrentGameFlow()
    local lastGF =  Facade.GameFlowManager:GetPreGameFlow()
    log("OnGuideOpenSafehouse curGF, lastGF", curGF, lastGF)

    if curGF ~= EGameFlowStageType.SafeHouse then
        log("OnGuideOpenSafehouse, not in safehouse game flow")
        return
    end

    -- 还未完全进入完毕安全屋，不启动引导
    if Module.IrisSafeHouse:IsEnteringSafeHouse() then
        warning("OnGuideOpenSafehouse Entering SafeHouse, wait next entry")
        return
    end

    -- 赛季重置时，安全屋主界面不做引导
    if Module.Ranking:GetIsShowingSeasonRestartWindow() then
        warning("OnGuideOpenSafehouse, season restart window is showing")
        return
    end

    -- 处于 匹配选人 阶段时，不启动引导
    if curGF == ESubStage.HallMatch then
        warning("OnGuideOpenSafehouse cur stage is match")
        return
    end

    local curStackUIId = Facade.UIManager:GetCurrentStackUIId()
    local lastStackID = Facade.UIManager:GetLastStackUIId()
    local lastStackName = UIName2ID.GetNameByID(lastStackID)
    local bInSafeHouseMainPanel = curStackUIId == UIName2ID.IrisWorldEntryMainPanel
    local bSolSettlementPopEnd = Module.Guide.Field.bSolSettlementPopEnd
    log(string.format("OnGuideOpenSafehouse, curStackUIId = %s, lastStackID = %d, lastStackName = %s, bSolSettlementPopEnd= %s",
        curStackUIId, lastStackID, lastStackName, bSolSettlementPopEnd))

    local readyStageList = {}

    -- 是否有新手*主流程*引导优先于当前新手引导
    local newPlayerGuideNotForces = GuideLogic.GetCurNewPlayerGuideStageNotForce()
    log("notForceStageList", #newPlayerGuideNotForces, newPlayerGuideNotForces and table.concat(newPlayerGuideNotForces, ",") or "")

    -- 撤离失败相关
    if bSolSettlementPopEnd then
        for _, stageId in ipairs(newPlayerGuideNotForces or {}) do
            if GuideLogic.IsSolFailGuide(stageId) then
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, stageId)
            end
        end
    end


    -- 特勤处引导
    if not IsHD() then
        -- all at lobby view
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solBlackSiteUpgrade_WareHouse)
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solBlackSiteProduce_Bullet)
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteUpgrade_AmorPlatform) then
            if GuideLogic_CheckCondition.CanStartSolBlackSiteUpgradeAmorPlatformGuide() then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteUpgrade_AmorPlatform])
            end
        end
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteProduce_Amor) then
            if GuideLogic_CheckCondition.CanStartSolBlackSiteProduceAmorGuide() then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteProduce_Amor])
            end
        end
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteUpgrade_CollectionRoom) then
            if GuideLogic_CheckCondition.CanStartSolBlackSiteUpgradeCollectionRoomGuide() then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteUpgrade_CollectionRoom])
            end
        end
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteGameplay_CollectionRoom) then
            if GuideLogic_CheckCondition.CanStartSolBlackSiteGameplayCollectionRoomGuide() then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteGameplay_CollectionRoom])
            end
        end
    else
        -- HD 高亮 topbar 前置, 引导触发在特勤处主界面打开后
        -- see GuideEvtLogic._OnBlackSiteMainUIOpen
        Timer.DelayCall(0.5, function()
            _tryHighlightBlackSiteTab(curStackUIId)
        end,nil )
    end

    -- 模式引导
    if  bInSafeHouseMainPanel and
        fromStr and  fromStr == "_OnSafeHouseHUDOpenFinished" and 
        (lastStackID == UIName2ID.SandBoxMapView or lastStackID == UIName2ID.AssemblyMainPanel or lastStackID == UIName2ID.AssemblyHDMainPanel) 
    then
        local bOpen = false
        local matchMode = Server.GameModeServer:GetMatchMode()
        if matchMode and matchMode.match_mode_id then
            bOpen = not  Server.GameModeServer:CheckMatchModeIsLocked(matchMode.match_mode_id)
        else
            logwarning("GetCurrentSelectedMapModeLockReason: matchMode.match_mode_id is invalid")
        end
        if bOpen then
            if Server.GameModeServer:IsArena() then
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.arenaIntro)
            elseif Server.GameModeServer:IsRaid() then
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solRaidIntro)
            elseif Server.GameModeServer:CheckGameModeIsNightMode() then
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solNightFight)
            elseif matchMode.map_id == 8802 or  matchMode.map_id == 8803 then
                -- <dexzhou> 2025/5/15 8802: 机密潮汐监狱  8803: 绝密潮汐监狱
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solTidePrison)
            end
        end
    end

    -- 排位赛引导
    if IsHD() then
        if bInSafeHouseMainPanel then
            if GuideLogic.CheckSafeHouseIsTop() then -- loading view
                log("CheckSafeHouseIsTop, is top ui")
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solRank)
            end
        end
    else
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solRank)
    end


    -- 撤离视频教学
    if bInSafeHouseMainPanel and lastGF == EGameFlowStageType.GameToSafeHouse then
        if IsHD() then
            -- PC 在局内结算自动/点击播放了 GuideConfig.EGuideStage.solSettlementVideo
            -- TODO: 下个开发周重构一下结算模块的播放逻辑，采用触发引导的方式播放
        else
            local settlementInfo = Server.SettlementServer:GetSettlementInfo()
            if GuideLogic_CheckCondition.CanStartSolSettlementGuide(settlementInfo) then
                ---@type EGspPlayerGameStatusType
                local endGameType = settlementInfo.player.type

                -- local escapeGameType = endGameType == EGspPlayerGameStatusType.kGspPlayerGameStatusTypeEndGame and settlementInfo.player.end_game.type or EkGpsPlayerResultType.kGspPlayerResultEscaped
                ---@type EGspPlayerResultType|nil
                local escapeGameType = Server.SettlementServer:GetEscapeGameType()
                log("OnGuideOpenSafehouse, endGameType = ", endGameType, " escapeGameType = ", escapeGameType)

                if escapeGameType == EGspPlayerResultType.kGspPlayerResultEscaped then
                    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solFirstEvacuationSuccess)
                elseif escapeGameType == EGspPlayerResultType.kGspPlayerResultKilled then
                    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solFirstEvacuationFailed)
                elseif escapeGameType == EGspPlayerResultType.kGspPlayerResultMissing then
                    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solFirstEvacuationFailedByTimeout)
                end
            end
        end
    end

    -- 手游大厅技能对抗教学
    if bInSafeHouseMainPanel and lastGF == EGameFlowStageType.GameToSafeHouse then
        if IsHD() then
            -- PC 在局内结算时候触发, 并且播放过了, 在这里做清理, 和Moobile的logic放一起
            GuideLogic.OnSkillAgainstVideoStepEnd()
        else
            if GuideLogic_CheckCondition.NeedSkillAgainstGuide() then -- TODO: move into guide start condition (daily max trigger count)
                if GuideLogic.GetFirstAvaliableSkillAgainstEntry() ~= nil then
                    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solSkillAgainstPopWindow)
                end
            end
        end
    end

    -- 体系教学
    if Module.Guide.Field.bSolSettlementPopEnd and bInSafeHouseMainPanel then
        GuideLogic.HandleStructTutorialStart(readyStageList, GuideConfig.EGuideStructTutorialStage.HallView)
    end


    -- 新手关完成后，可以执行其他sol引导
    if Server.GuideServer:IsNewPlayerMatchFinished() then
        --log("TryStartGuideStage", #readyStageList)
        GuideLogic.TryStartGuideStage(readyStageList)
    end


    -- 启动主线新手引导(从服务器下发的)
    if GuideLogic.GetNextNewPlayerGuideStage() then
        Module.Guide:StartNewPlayerGuideInFrontEnd()
    end
end


function GuideEvtLogic._OnTryMarkMap()
    log("_OnTryMarkMap")
    GuideConfig.EGuideEvent.evtGuideTryMarkMap:Invoke()
end

---@param value number (int32) 0 Hide 1 Show
function GuideEvtLogic._OnInteractionPanelVisibilityChanged(value)
    if value ~= Module.Guide.Field.interactionPanelShowState then
        Module.Guide.Field.interactionPanelShowState = value
        log("_OnInteractionPanelVisibilityChanged", value)
    end
    if value == 1 then
        -- do poi search guide
        GuideConfig.EGuideEvent.evtGuideInteractionPanelShow:Invoke()
    elseif value == 0 then
        -- break poi search guide
        GuideConfig.EGuideEvent.evtGuideInteractionPanelHide:Invoke()
    end
end

function GuideEvtLogic._OnInteractionPanelInteractorsChanged()
    log("_OnInteractionPanelInteractorsChanged")
    GuideConfig.EGuideEvent.evtGuideInteractionPanelInteractorsChanged:Invoke()
end


function GuideEvtLogic._OnGuideHUDMsg(sig, args)
    log("_OnGuideHUDMsg",sig, table.concat(args, ", "))

    local readyStageList = {}
    --TODO: optmize the string sig
    if sig == "SOLBigMapHUD_TipsBoxShow" then
        local bValidToGuide = false
        -- and the instruction btn is visible
        if args[1] ~="invalid" then
            local visibility   = tonumber(args[1])
            if visibility == ESlateVisibility.Visible
                or visibility == ESlateVisibility.SelfHitTestInvisible
                or visibility == ESlateVisibility.HitTestInvisible
            then
                bValidToGuide = true
            else 
                bValidToGuide = false
            end
        else
            bValidToGuide = false
        end
        if bValidToGuide then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solPlayerEvacuationPointInstruct)
        else
            local curGuideStage = Module.Guide.Field:GetCurGuideStage()
            if curGuideStage == GuideConfig.EGuideStage.solPlayerEvacuationPointInstruct then
                log("_OnGuideHUDMsg SOLBigMapHUD_TipsBoxShow, end guide")
                Module.Guide:StopAllGuide(true)
            end
        end
    elseif sig == "SOLBigMapHUD_TipsBoxHide" then
        local curGuideStage = Module.Guide.Field:GetCurGuideStage()
        if curGuideStage == GuideConfig.EGuideStage.solPlayerEvacuationPointInstruct then
            log("_OnGuideHUDMsg SOLBigMapHUD_TipsBoxHide, end guide")
            Module.Guide:StopAllGuide(true)
        end
    end
    GuideConfig.EGuideEvent.evtOnGuideMsg:Invoke(sig)
    GuideLogic.TryStartGuideStage(readyStageList)

end

---@param slotView CommonSlotView
---@param item ItemBase
---@param location ItemLocation
function GuideEvtLogic._OnCommonSlotViewItemAdded(slotView, item, location)
    if hasdestroy(slotView) or hasdestroy(item) or hasdestroy(location) then
        warning("_OnCommonSlotViewItemAdded, input arguments invalid", slotView, item, location)
        return
    end
    if hasdestroy(item.InSlot) then
        warning("_OnCommonSlotViewItemAdded, item.InSlot is invalid")
        return
    end

    local ids = Module.Guide:GetNewPlayerGuideItemInfo(Module.Guide.Config.NewPlayerGuideSpecItem.newPlayerLevelSafeBoxLootItem)
    local id = tonumber(ids[1])

    log("_OnCommonSlotViewItemAdded",  item.id, location.ItemSlot.SlotType, id)

    if location.ItemSlot.SlotType == ESlotType.NearbyContainer then -- safebox
        if item.id == id then
            GuideEvtLogic._TryStartTutorialLevelLootingGuide(2)
        end
    end
end

--- APlayerExitBase
function GuideEvtLogic._OnSOLMapExitInfoShow(playerExit)
    if not playerExit then
        logerror("_OnSOLMapExitInfoShow playerExit or playerExit.ExitOpenConfig is nil")
        Module.Guide.Field.LastEvacuationPointGuideType:Set(nil)
        return
    end

    GuideConfig.EGuideEvent.evtGuideClick:Invoke("BigMapPlayerExitInstructionBtn")

    local EExitConditionType = import "EExitConditionType"
    local EExitOpenType = import "EExitOpenType"

    local openType = playerExit.ExitOpenConfig.ExitOpenType
    local conditionType = playerExit.ExitConditionConfig.ExitConditionType
    log("_OnSOLMapExitInfoShow", conditionType, openType)

    ---@type EGuideEvacuationPointType
    local guideEvacuationType = GuideConfig.EEvacuationPointType.None
    for _ = 1, 1 do
        --  付费
        if conditionType == EExitConditionType.Payment then
            guideEvacuationType = GuideConfig.EEvacuationPointType.Pay
            break
        end
        -- 随机
        if openType == EExitOpenType.Random then
            guideEvacuationType = GuideConfig.EEvacuationPointType.Random
            break
        end
        -- 丢包
        if conditionType == EExitConditionType.EquipmentLimit then
            local EExitConditionEquipmentType = import "EExitConditionEquipmentType"
            local EExitConditionEquipmentState = import "EExitConditionEquipmentState"

            local equipmentEntry =  playerExit.ExitConditionConfig.ExitConditionEquipmentEntry.ExitConditionEquipment
            local stateEntry = playerExit.ExitConditionConfig.ExitConditionEquipmentEntry.ExitConditionEquipmentState
            log("_OnSOLMapExitInfoShow equipment limit", equipmentEntry, stateEntry)

            if stateEntry == EExitConditionEquipmentState.Empty then
                if equipmentEntry == EExitConditionEquipmentType.Bag then
                    guideEvacuationType = GuideConfig.EEvacuationPointType.DropBag
                    break
                end
            end
        end
        -- 负重
        if conditionType == EExitConditionType.RoleLoadLimit then
            guideEvacuationType = GuideConfig.EEvacuationPointType.WeightLimit
            break
        end
        -- 拉闸
        if openType == EExitOpenType.Switch then
            guideEvacuationType = GuideConfig.EEvacuationPointType.Switcher
            break
        end
    end


    log("_OnSOLMapExitInfoShow guide evacuationType", guideEvacuationType)
    local popWindowId = GuideConfig.EEvacuationPointType2PopWindowId[guideEvacuationType]
    if not popWindowId then
        logerror("_OnSOLMapExitInfoShow popWindowId is nil")
        return
    end

    if IsHD() then
        -- PC 需要引导来控制输入
        Module.Guide.Field.LastEvacuationPointGuideType:Set(guideEvacuationType)
        local stageList = {}
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(stageList, GuideConfig.EGuideStage.solPlayerEvacuationPointInstructPopWindow)
        GuideLogic.TryStartGuideStage(stageList)
    else
        -- Mobile 直接打开弹窗
        local report = function()
            LogAnalysisTool.DoSendGuideEvacuationPointInstructReportLog(guideEvacuationType)
        end
        Module.Guide:OpenGuidePopWindowUI(popWindowId, report)
    end
end

---@param msg any
function GuideEvtLogic._OnBroadcastHudConsumeMsg(msg)
    
    local tutorialId = Server.GuideServer.mapGuideStageId2StructTutorialId[GuideConfig.EGuideStage.tSolLootDeadBodyTips]
    if not tutorialId then
        -- GuideEvtLogic._Unregister_OnBroadcastHudConsumeMsg()
        return 
    end

    local readyStageList = {}

    if msg.BrocastType == EGameBrocastType.KillInfo then
        log("_OnReceiveBroadcastMsgs KillInfo", msg.Name1, msg.Name1Uin, msg.Name2, msg.Name2Uin)
        log("__OnReceiveBroadcastMsgs temp: ", msg.KillInfo.VictimIsPlayer, msg.KillInfo.VictimIsRobotAI, msg.KillInfo.VictimIsBossAI, msg.KillInfo.VictimIsAILAB)
        if msg.KillInfo.KillerUin == Facade.GameFlowManager:GetPlayerUin() then
            if msg.KillInfo.VictimIsPlayer == false and msg.KillInfo.VictimIsRobotAI == false then
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.tSolLootDeadBodyTips)
            end
        end
    end


    GuideLogic.TryStartGuideStage(readyStageList)
end


---@param bEnter boolean
---@param POITriggerActorPtr any
function GuideEvtLogic._OnLocalPlayerEnterExitPOI(bEnter, POITriggerActorPtr)
    local readyStageList = {}

    if not POITriggerActorPtr then
        logerror("_OnLocalPlayerEnterExitPOI POITriggerActorPtr is nil")
        return
    end
    local curIrisEnterState = Module.Guide.Field.IrisEnterStageState:Get().current
    log("_OnLocalPlayerEnterExitPOI", bEnter, POITriggerActorPtr.POILevelType, curIrisEnterState)

    if bEnter and POITriggerActorPtr.POILevelType > EMapLocationPOILevelType.POILevel1 then
        if curIrisEnterState == EIrisEnterStageType.Finish then
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solPOI) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solPOI])
            end
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnMarkDetailOpen()
    log("_OnMarkDetailOpen")
    GuideConfig.EGuideEvent.evtGuideMarkDetailOpen:Invoke()
end

function GuideEvtLogic._OnPlayerDied()
    log("_OnPlayerDie")
    GuideLogic.DoStopAllGuide()
    GuideLogic.StopTimerForGuideInGame()
    Module.Guide.Field:ClearInGameInfo()
    Module.Guide:CloseGuidePopWindowUI()
    Module.Guide:CloseGuideHDPopTipsUI()
end


function GuideEvtLogic._OnFoldStatusChanged(foldState, expandScreenCount)
    log("_OnFoldStatusChanged", foldState, expandScreenCount)
    local guideData = Module.Guide.Field:GetCurGuideData()
    if guideData and guideData.OnFoldStatusChanged then
        guideData:OnFoldStatusChanged(foldState, expandScreenCount)
    end
end

function GuideEvtLogic._OnNotifyResolutionResized(newSizeX, newSizeY)
    log("_OnNotifyResolutionResized")
    GuideLogic.OnNotifyResolutionResized(newSizeX, newSizeY)
end


local bDebugState = false
function GuideEvtLogic._OnHudStateChanged(state)
    if bDebugState then
        local t = {}
        for k, v in ipairs(state.states) do
            t[k] = math.log(v, 2)  -- Changed to log base 2 of v
        end
        print("State log values:", table.concat(t, ", "))
        -- TODO: find what state when be knocked down
    end

    -- log("_OnHudStateChanged")
    local guideField = Module.Guide.Field
    if state:HasState(EGameHUDState.GHS_OpenMap) then
        GuideConfig.EGuideEvent.evtGuideMiniMapOpen:Invoke()
        GuideLogic.SetPopTipsInGameVisibility("miniMap", false)
        GuideLogic.SetPopButtonInGameVisibility("miniMap", false)
        guideField.miniMapInGameOpenState = true
    elseif guideField.miniMapInGameOpenState then
        GuideConfig.EGuideEvent.evtGuideMiniMapClose:Invoke()
        GuideLogic.SetPopTipsInGameVisibility("miniMap", true)
        GuideLogic.SetPopButtonInGameVisibility("miniMap", true)
        guideField.miniMapInGameOpenState = false
    end

    if state:HasState(EGameHUDState.GHS_RealOpenBag) then
        GuideLogic.SetPopTipsInGameVisibility("looting",false)
        GuideLogic.SetPopButtonInGameVisibility("looting", false)
        guideField.lootingInGameOpenState = true
    elseif guideField.lootingInGameOpenState then
        GuideLogic.SetPopTipsInGameVisibility("looting",true)
        GuideLogic.SetPopButtonInGameVisibility("looting", true)
        guideField.lootingInGameOpenState = false
    end

    if state:HasState(EGameHUDState.GHS_SOLCaptureView) then
        GuideLogic.SetPopTipsInGameVisibility("solCaptureView",false)
        GuideLogic.SetPopButtonInGameVisibility("solCaptureView", false)
        guideField.solCaptureInGameOpenState = true
    elseif guideField.solCaptureInGameOpenState then
        GuideLogic.SetPopTipsInGameVisibility("solCaptureView",true)
        GuideLogic.SetPopButtonInGameVisibility("solCaptureView", true)
        guideField.solCaptureInGameOpenState = false
    end

    if state:HasState(EGameHUDState.GHS_TeamInfoDetail) then
        GuideLogic.SetPopTipsInGameVisibility("teamInfoDetail", false)
        GuideLogic.SetPopButtonInGameVisibility("teamInfoDetail", false)
        guideField.teamInfoDetailInGameOpenState = true
    elseif guideField.teamInfoDetailInGameOpenState then
        GuideLogic.SetPopTipsInGameVisibility("teamInfoDetail", true)
        GuideLogic.SetPopButtonInGameVisibility("teamInfoDetail", true)
        guideField.teamInfoDetailInGameOpenState = false
    end

    local bHaveState = state:HasState(EGameHUDState.GHS_LoadingHideAllHUD)
    if bHaveState and not guideField.loadingHideAllState then
        GuideLogic.SetPopTipsInGameVisibility("loading",false)
        GuideLogic.SetPopWindowInGameVisibility("loading",false)
        GuideLogic.SetPopButtonInGameVisibility("loading", false)
        guideField.loadingHideAllState = true
    elseif not bHaveState and guideField.loadingHideAllState then
        GuideLogic.SetPopTipsInGameVisibility("loading",true)
        GuideLogic.SetPopWindowInGameVisibility("loading",true)
        GuideLogic.SetPopButtonInGameVisibility("loading", true)
        guideField.loadingHideAllState = false
    end

    if GuideLogic.IsSOLMode() then
        if state:HasState(EGameHUDState.GHS_LiveSpectate) then
            if not guideField.solDyingViewState then
                guideField.solDyingViewState = true
                local readyStageList = {}
                if DFHD_LUA == 1 then
                    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solFirstDying) then
                        local character = InGameController:Get():GetGPCharacter()
                        if character and character:IsCanBeRescue(false, true, false, false) then
                            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solFirstDying])
                        end
                    end
                else
                    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solFirstDying) then
                        local character = InGameController:Get():GetGPCharacter()
                        if character and character:IsCanBeRescue(false, true, false, false) then
                            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solFirstDying])
                        end
                        -- log("IsCanBeRescue", character and character:IsCanBeRescue(false, true, false, false))
                    end
                end
                GuideLogic.TryStartGuideStage(readyStageList)
            end
        elseif guideField.solDyingViewState then
            guideField.solDyingViewState = false
        end
    end
end

function GuideEvtLogic._OnLoadingViewVisible()
    log("_OnLoadingViewVisible")
    if not Module.Guide.Field.loadingHideAllState then
        GuideLogic.SetPopTipsInGameVisibility("loading",false)
        GuideLogic.SetPopWindowInGameVisibility("loading",false)
        GuideLogic.SetPopButtonInGameVisibility("loading", false)
        Module.Guide.Field.loadingHideAllState = true
    end
end

function GuideEvtLogic._OnLoadingViewClosed()
    log("_OnLoadingViewClosed")
    if Module.Guide.Field.loadingHideAllState then
        GuideLogic.SetPopTipsInGameVisibility("loading",true)
        GuideLogic.SetPopWindowInGameVisibility("loading",true)
        GuideLogic.SetPopButtonInGameVisibility("loading", true)
        Module.Guide.Field.loadingHideAllState = false
    end
end

function GuideEvtLogic._OnSystemSettingHDEntranceOnShowBegin()
    log("_OnEscShowBeginHD")

    local key = EPauseGuideUIKey.HDSystemSettingEntrance

    GuideLogic.SetPopTipsInGameVisibility(key,false)
    GuideLogic.SetPopButtonInGameVisibility(key,false)

    Module.Guide.Field:AddPauseGuideUINum(key)
end

function GuideEvtLogic._OnSystemSettingHDEntranceOnHideBegin()
    log("_OnEscHideHD")
    local key = EPauseGuideUIKey.HDSystemSettingEntrance
    GuideLogic.SetPopTipsInGameVisibility(key,true)
    GuideLogic.SetPopButtonInGameVisibility(key,true)

    Module.Guide.Field:ReducePauseGuideUINum(key)
end

function GuideEvtLogic._OnSettingMainShowBeginHD()
    log("_OnSettingMainShowBeginHD")

    local key = EPauseGuideUIKey.HDSystemSettingMainPanel
    GuideLogic.SetPopTipsInGameVisibility(key, false)
    GuideLogic.SetPopButtonInGameVisibility(key, false)

    Module.Guide.Field:AddPauseGuideUINum(key)
end

function GuideEvtLogic._OnSettingMainHideHD()
    log("_OnSettingMainHideHD")
    local key = EPauseGuideUIKey.HDSystemSettingMainPanel
    GuideLogic.SetPopTipsInGameVisibility(key,true)
    GuideLogic.SetPopButtonInGameVisibility(key,true)

    Module.Guide.Field:ReducePauseGuideUINum(key)
end

function GuideEvtLogic._OnSettingBrightnessShowHD()
    log("_OnSettingBrightnessShowHD")

    local key = EPauseGuideUIKey.HDSystemSettingBrightness
    GuideLogic.SetPopTipsInGameVisibility(key, false)
    GuideLogic.SetPopButtonInGameVisibility(key, false)

    Module.Guide.Field:AddPauseGuideUINum(key)
end

function GuideEvtLogic._OnSettingBrightnessHideHD()
    log("_OnSettingBrightnessHideHD")
    local key = EPauseGuideUIKey.HDSystemSettingBrightness
    GuideLogic.SetPopTipsInGameVisibility(key, true)
    GuideLogic.SetPopButtonInGameVisibility(key, true)

    Module.Guide.Field:ReducePauseGuideUINum(key)
end

function GuideEvtLogic._OnItemDetailOpen()
    log("_OnItemDetailOpen")
    GuideConfig.EGuideEvent.evtGuideItemDetailOpen:Invoke()
end

function GuideEvtLogic._OnLootingClose()
    -- log("_OnLootingClose")
    GuideConfig.EGuideEvent.evtCloseLootingBagUI:Invoke()

    -- loot界面关闭的时候，如果还在进行新手关loot引导，直接停掉引导
    -- 多见于断线重连等loot界面自行关闭的情景
    local curGuideId = Module.Guide.Field:GetCurGuideId()
    if curGuideId then
        local guideCfg = Module.Guide.Field:GetGuideCfg(curGuideId)
        if guideCfg and (guideCfg.GuideStageId == GuideConfig.EGuideStage.lootingGuide or guideCfg.GuideStageId == GuideConfig.EGuideStage.lootingDeadBody) then
            Module.Guide.StopAllGuide()
        end
    end
end


function GuideEvtLogic._OnRewardOpen()
    log("_OnRewardOpen")
    Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.InventoryReward)
    GuideConfig.EGuideEvent.evtOpenInventoryReward:Invoke()
end

function GuideEvtLogic._OnRewardClose()
    log("_OnRewardClose")
    Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.InventoryReward)
    GuideConfig.EGuideEvent.evtCloseInventoryReward:Invoke()
end

function GuideEvtLogic._OnOpenLevelUpUI(key)
    log("_OnOpenLevelUpUI ", key)
    Module.Guide.Field:AddPauseGuideUINum(key)
end

function GuideEvtLogic._OnReportMainPanelOpen()
    log("_OnReportMainPanelOpen")
    Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.ReportMainPanel)
end

function GuideEvtLogic._OnReportMainPanelClose()
    log("_OnReportMainPanelClose")
    Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.ReportMainPanel)
end

function GuideEvtLogic._OnCloseLevelUpUI(key)
    log("_OnCloseLevelUpUI", key)
    Module.Guide.Field:ReducePauseGuideUINum(key)
end

function GuideEvtLogic._OnOpenPauseGuideUI(key)
    log("_OnOpenPauseGuideUI ", key)
    Module.Guide.Field:AddPauseGuideUINum(key)
end

function GuideEvtLogic._OnClosePauseGuideUI(key)
    log("_OnClosePauseGuideUI", key)
    Module.Guide.Field:ReducePauseGuideUINum(key)
end

function GuideEvtLogic._OnShopItemReGenerated()
    log("_OnShopItemReGenerated")
    GuideConfig.EGuideEvent.evtShopItemRefresh:Invoke()
end

function GuideEvtLogic._OnArmedForceProcessBtnClicked()
    log("_OnArmedForceProcessBtnClicked")
    GuideConfig.EGuideEvent.evtOnArmedForceProcessBtnClicked:Invoke()
end

function GuideEvtLogic._OnUIOpenFinished(uiIns)
    log("_OnUIOpenFinish")
    GuideConfig.EGuideEvent.evtGuideUIOpenFinish:Invoke(uiIns)
end

function GuideEvtLogic._OnTipsRecordInitFinished()
    log("_OnTipsRecordInitFinished")
    if Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideSkipAllGuide) then
        Module.Guide:SkipAllGuide()
    end
    if Server.GuideServer.ForceSkipGuide then
        Server.GuideServer:SkipNewPlayerGuide()
        Module.Guide.Field.waitPlayGuide = nil
        Module.Guide:SkipAllGuide()
        Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideSkipAllGuide, true)
    end

    log("All GuideStageFinished") 
    local finishMap = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.GuideStageFinished)
    for k, v in pairs(finishMap) do
        log(k, v)
    end

    GuideLogic.UpdateGuideInGameData()
end

function GuideEvtLogic._OnServerSkipAllGuide()
    err("_OnServerSkipAllGuide")
    if IsInEditor() then
        Module.Guide:StopAllGuide(true)
        Server.GuideServer:SkipNewPlayerGuide()
        Module.Guide.Field.waitPlayGuide = nil
        Module.Guide:SkipAllGuide()
        Module.Guide.Field:ClearCurShowUIState()
        Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.GuideSkipAllGuide, true)
        Module.Guide:StopLoadingViewControl()
    else
        err("_OnServerSkipAllGuide NOT EDITOR!!!")
    end
end

function GuideEvtLogic._OnServerStopAllGuide()
    err("_OnServerStopAllGuide")
    Module.Guide:StopAllGuide(true)
end

function GuideEvtLogic._SetInputStateAllPlatform()
    log("_SetInputStateAllPlatform")
    GuideLogic.SetInputStateAllPlatform()
end

function GuideEvtLogic._OnSandBoxMapShow()
    log("_OnSandBoxMapShow")
    GuideConfig.EGuideEvent.evtSandBoxOpenFinish:Invoke()
end

function GuideEvtLogic._OnSandBoxMapShowBegin()
    log("_OnSandBoxMapShowBegin")
    GuideConfig.EGuideEvent.evtSandBoxOpenBegin:Invoke()
    GuideEvtLogic._TryStartSandBoxGuide()
end

function GuideEvtLogic._TryStartSandBoxGuide()
    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.sandBox) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.sandBox])
    end

    if false then
        local tipsServer = Server.TipsRecordServer
        local firstMap = tipsServer:GetNumber(tipsServer.keys.GuideFirsetUnlockMap)
        local mapForrestId = 1901
        local mapBigBaId = 2201
        if firstMap <= 0 then
            -- 记录下首次打开解锁的首图
            local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()
            local _CheckMap = function(mapId)
                local mapCfg = Server.GameModeServer:GetWorldEntranceInfoByMapID(mapId)
                if mapCfg then
                    local bLock = sandBoxMapUtil.IsTargetPointLocked(mapCfg)
                    if not bLock then
                        tipsServer:SetNumber(tipsServer.keys.GuideFirsetUnlockMap, mapId)
                        return true
                    end
                end
                return false
            end
            -- 先看森林是否解锁
            if not _CheckMap(mapForrestId) then
                _CheckMap(mapBigBaId)
            end
        else
            if firstMap == mapForrestId then
                if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mapSecondBigBaUnlock) then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mapSecondBigBaUnlock])
                end
            elseif firstMap == mapBigBaId then
                if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mapSecondForrestUnlock) then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mapSecondForrestUnlock])
                end
            end
        end

        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mapHighUnlock) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mapHighUnlock])
        end

        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mapSkyCityUnlock) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mapSkyCityUnlock])
        end
    end

    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnSandBoxMapHideBegin()
    log("_OnSandBoxMapHideBegin")
    GuideConfig.EGuideEvent.evtSandBoxHideBegin:Invoke()
end

function GuideEvtLogic._OnArmedForceMainShow()
    log("_OnArmedForceMainShow")
    GuideConfig.EGuideEvent.evtArmedForceOpenFinish:Invoke()

    -- local readyStageList = {}
    -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solFailUseNewEquip) then
    --     table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solFailUseNewEquip])
    -- end
    -- GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnSelectionEquipShow()
    log("_OnSelectionEquipShow")
    GuideConfig.EGuideEvent.evtSelectionEquipOpenFinish:Invoke()
end

function GuideEvtLogic._OnSafeHouseHUDOpenFinished()
    log("_OnSafeHouseHUDOpenFinished")
    GuideConfig.EGuideEvent.evtSafeHouseHUDOpenFinish:Invoke()
    GuideEvtLogic.OnGuideOpenSafehouse("_OnSafeHouseHUDOpenFinished")
    if IsHD() then
        if not GuideLogic.IsWarehouseOpened() then
            Module.Guide:OpenGuideHDWeakClickUI(148)
        end
        if not GuideLogic.IsDepartmentOpened() then
            Module.Guide:OpenGuideHDWeakClickUI(149)
        end

        -- todo 有可能按钮尺寸不对，需要延迟一下重新高亮
        local _delay = function()
            -- 是否在局外
            if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.SafeHouse then
                return
            end

            -- 是否在合法的sub Stage
            if GuideLogic.IsInSubStage3dSafehouse() or GuideLogic.IsInSubStageRange() then
                logwarning("_OnSafeHouseHUDOpenFinished delay, sub stage invalid")
                return
            end

            if not GuideLogic.IsWarehouseOpened() then
                Module.Guide:OpenGuideHDWeakClickUI(148)
            end
            if not GuideLogic.IsDepartmentOpened() then
                Module.Guide:OpenGuideHDWeakClickUI(149)
            end
        end
        Timer.DelayCall(1, _delay)
    end
end

function GuideEvtLogic._OnIrisWorldEntryOnHideBegin()
    log("_OnIrisWorldEntryOnHideBegin")
    Module.Guide.Config.EGuideEvent.evtIrisWorldEntryOnHideBegin:Invoke()
end

function GuideEvtLogic._OnSafeHouseHUDOpenStart()
    log("_OnSafeHouseHUDOpenStart")
    GuideConfig.EGuideEvent.evtSafeHouseHUDOpenStart:Invoke()
end

function GuideEvtLogic._OnPrepareBtnClicked()
    log("_OnPrepareBtnClicked")
    GuideConfig.EGuideEvent.evtNewPlayerGuidePrepare:Invoke()
end

function GuideEvtLogic._OnWarehouseExpansionShow()
    log("_OnWarehouseExpansionShow")
    GuideConfig.EGuideEvent.evtGuideWarehouseExpansionOpenFinish:Invoke()
end

function GuideEvtLogic._OnevtSyncMainShow()
    log("_OnevtSyncMainShow")
    GuideConfig.EGuideEvent.evtSyncMainOpen:Invoke()
end

function GuideEvtLogic._OnevtSyncMainShowStart()
    log("_OnevtSyncMainShowStart")
    GuideConfig.EGuideEvent.evtSyncMainOpenStart:Invoke()
end

function GuideEvtLogic._OnAuctionShow()
    log("_OnAuctionShow")
    GuideConfig.EGuideEvent.evtAuctionOpen:Invoke()

    local readyStageList = {}
    if DFHD_LUA == 1 then
        if GuideLogic.IsAuctionUnlockBuy() and GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.auctionUnlockBuy) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.auctionUnlockBuy])
        elseif GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.auctionLockBuy) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.auctionLockBuy])
        end
    else
        -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.auctionInfo) then
        --     table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.auctionInfo])
        -- end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnSlotViewClicked()
    log("_OnSlotViewClicked")
    GuideConfig.EGuideEvent.evtGuideSlotViewClicked:Invoke()
end

function GuideEvtLogic._OnItemDoubleClick()
    -- log("_OnItemDoubleClick")
    GuideConfig.EGuideEvent.evtGuideItemDoubleClick:Invoke()
end

function GuideEvtLogic._OnItemDragStart()
    log("_OnItemDragStart")
    GuideConfig.EGuideEvent.evtGuideItemDragStart:Invoke()
end

function GuideEvtLogic._OnItemDragCancelled()
    log("_OnItemDragCancelled")
    GuideConfig.EGuideEvent.evtGuideItemDragCancelled:Invoke()
end

function GuideEvtLogic._OnItemDrop()
    -- log("_OnItemDrop")
    GuideConfig.EGuideEvent.evtGuideItemDragCancelled:Invoke()
end

function GuideEvtLogic._OnWarehouseMainShow()
    log("_OnWarehouseMainShow")
    GuideConfig.EGuideEvent.evtGuideWareHouseMainOpenFinish:Invoke()

    -- newplayer's inventory tab highlight
    if IsHD() then
        Module.Guide:CloseGuideHDWeakClickUI(148)
    end
    GuideEvtLogic._TryStartWarehouseGuide("_OnWarehouseMainShow")
end

function GuideEvtLogic._OnWareHouseMainHideBegin()
    log("_OnWareHouseMainHideBegin")
    GuideConfig.EGuideEvent.evtWareHouseMainHideBegin:Invoke()
end

function  GuideEvtLogic._OnRepairWindowRepairBtnClick(item)
    log("_OnItemRepairPanelRepairBtnClicked")
    GuideConfig.EGuideEvent.evtRepairWindowRepairBtnClick:Invoke()
end

function GuideEvtLogic._OnRepairWindowRepairFailedByNoEnoughCurrency()
    log("_OnRepairWindowRepairFailedByNoEnoughCurrency")
    GuideConfig.EGuideEvent.evtRepairWindowRepairFailedByNoEnoughCurrency:Invoke()
end

function GuideEvtLogic._OnRepairWindowShowBegin()
    log("_OnRepairWindowShowBegin")
    GuideConfig.EGuideEvent.evtRepairWindowShowBegin:Invoke()
end

function GuideEvtLogic._OnRepairWindowHideBegin ()
    log("_OnRepairWindowHideBegin")
    GuideConfig.EGuideEvent.evtRepairWindowHideBegin:Invoke()
end

function GuideEvtLogic._OnWareHouseWithTabExtArrangeBtnPressed()
    log("_OnWareHouseWithTabExtArrangeBtnPressed")
    GuideConfig.EGuideEvent.evtWareHouseWithTabExtArrangeBtnPressed:Invoke()
end


function GuideEvtLogic._OnCollectionMainPanelTabIndexChanged(idx, lastIdx)
    log("_OnCollectionMainPanelTabIndexChanged", idx, lastIdx)
    -- see datatable: collection_tab

    local collectionTabInfos = Module.Collection.Field:GetTabInfo()
    local tabId = collectionTabInfos[idx].mainTabId
    local CollectionTabId2Info = Module.Collection.Config.CollectionTabId2Info
    local tabConfig = CollectionTabId2Info[tabId]

    local guideStages = {}
    if  tabConfig.uiNavID == UIName2ID.CollectionMandelBrickPagePanel then
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(guideStages, GuideConfig.EGuideStage.mandelBrick)
    -- elseif tabConfig.uiNavID == UIName2ID.CollectionMysticalSkinWorkshopPagePanel then
        -- GuideLogic.TryInsertIfMeetGuideStageStartCondition(guideStages, GuideConfig.EGuideStage.mysticalWorkshop)
    end
    GuideLogic.TryStartGuideStage(guideStages)
end

function GuideEvtLogic._OnCollectionMainPanelTertiaryTabIndexChanged(mainTabIdx, tertiaryTabIdx, lastTertiaryTabIndex )
    log("_OnCollectionMainPanelTertiaryTabIndexChanged", mainTabIdx, tertiaryTabIdx, lastTertiaryTabIndex )

    local collectionTabInfos = Module.Collection.Field:GetTabInfo()
    local realTabId = collectionTabInfos[mainTabIdx].mainTabId

    local CollectionTabId2Info = Module.Collection.Config.CollectionTabId2Info
    local tabConfig = CollectionTabId2Info[realTabId]

    local guideStages = {}
    if tabConfig.uiNavID == UIName2ID.CollectionsPageContainer and tabConfig.tertiaryUiNavIDs then
        local subTabUINavId = tabConfig.tertiaryUiNavIDs[tertiaryTabIdx]
        if subTabUINavId == UIName2ID.CollectionMysticalSkinWorkshopPagePanel then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(guideStages, GuideConfig.EGuideStage.mysticalWorkshop)
        elseif subTabUINavId == UIName2ID.CollectionMysticalPendantWorkshopPagePanel then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(guideStages, GuideConfig.EGuideStage.mysticalWorkShopHanging)
        end
    end
    GuideLogic.TryStartGuideStage(guideStages)

end

function GuideEvtLogic._OnCollectionHangingPageItemClicked(idx)
    log("_OnCollectionHangingPageItemClicked", idx)

    local guideStages = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(guideStages, GuideConfig.EGuideStage.mysticalHanging)
    GuideLogic.TryStartGuideStage(guideStages)
end

function  GuideEvtLogic._OnPreDoSwitchModeOperation(eSwitchMode, bIgnoredUnlockState)
    log("_OnPreDoSwitchModeOperation", eSwitchMode, bIgnoredUnlockState)
    GuideConfig.EGuideEvent.evtOnPreDoSwitchModeOperation:Invoke()
    -- local curGuideCfg = Module.Guide.Field._curGuideCfg;
    -- local curGuideData = Module.Guide.Field:GetCurGuideData()
    -- if curGuideCfg and curGuideData then
    --     if curGuideCfg.GuideType == "HDWeakClick" then
    --         GuideLogic.DoEndGuide(-2)
    --     end
    -- end
    Module.Guide:StopAllGuide(true)
    -------------
end
function GuideEvtLogic._OnInvitePopViewOnShowBegin()
    log("_OnInvitePopViewOnShowBegin")

    -- local curGuideStage = Module.Guide.Field:GetCurGuideStage()

    -- if curGuideStage ~= -1 then
    --     local bNewPlayerGuide = false
    --     for _, stage in pairs(GuideConfig.NewPlayerGuideStageList) do
    --         if stage == curGuideStage then
    --             bNewPlayerGuide = true
    --             break
    --         end
    --     end
    --     if not bNewPlayerGuide then
    --         GuideLogic.DoEndGuide(-2)
    --     end
    -- end

    Module.Guide:StopAllGuide(true)
end

function GuideEvtLogic._OnActivityMandelDrawOnlyOnShowBegin()
    log("_OnActivityMandelDrawOnlyOnShowBegin")

    local readyStageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.turingBrickIntro)
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnItemRepaireSuccess(item)
    log("_OnItemRepaireSuccess")
    GuideConfig.EGuideEvent.evtItemRepaireSuccess:Invoke()
end

function GuideEvtLogic._TryStartWarehouseGuide(fromStr, extArgs)
    local readyStageList = {}
    if IsHD() then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.firstOpenInventory) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.firstOpenInventory])
        end
    end

    local mainWareHousePercent = Server.InventoryServer:GetMainWarehousePercent()
    log("_TryStartWarehouseGuide, mainWarehousePercent: ", mainWareHousePercent)
    if mainWareHousePercent > 0.9 then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solQuickSort) then
            -- first time full
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solQuickSort])
        elseif GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solWarehouseFull) then
            -- second time full
            if fromStr == "_OnGuideEnd" and extArgs.endGuideStageId == GuideConfig.EGuideStage.solQuickSort then
                log("wait next time full")
            else
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solWarehouseFull])
            end
        end
    end
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solDoorKey)

    GuideLogic.TryStartGuideStage(readyStageList)
end


function GuideEvtLogic._TryStartArmedForceMainGuide(fromStr, extArgs)
    local readyStageList = {}

    if IsHD() then
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.armedForceTakeItem)

        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.armedForceTwoGun) then
            if GuideLogic_CheckCondition.CheckHaveTwoWeapon() then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.armedForceTwoGun])
            end
        end
        -- 护甲低耐久 (mobile in stage 2 main flow)
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solArmedForceArmorLowDurability) then
            if GuideLogic_CheckCondition.NeedLowAmorDurabilityGuide(GuideConfig.guideArmedForceArmorDurabilityPercent) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solArmedForceArmorLowDurability])
            end
        end
    end

    -- 物资券
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solSupplyVoucher) then
        if GuideLogic_CheckCondition.CanStartSolSupplyVoucherGuide() then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solSupplyVoucher])
        end
    end
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.armedForceAuctionUnlock)


    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solArmedForceArmorLowMaxDurability) then
        if GuideLogic_CheckCondition.NeedAmorLowMaxPriorityGuide() then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solArmedForceArmorLowMaxDurability])
        end
    end

    -- 体系教学
    GuideLogic.HandleStructTutorialStart(readyStageList, GuideConfig.EGuideStructTutorialStage.AssemblyView)

    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnInventorySorted()
    log("_OnInventorySorted")

    -- local readyStageList = {}
    -- if Server.InventoryServer:CheckMainWarehousePercent(0.9) then
    --     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.inventoryFull) then
    --         -- 是否在局外
    --         if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
    --             table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.inventoryFull])
    --         end
    --     end
    -- end
    -- GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnInventoryItemMove(moveItemInfo)
    if not moveItemInfo or not moveItemInfo.item then
        return
    end
   
    local oldLoc = moveItemInfo.OldLoc
    local newLoc = moveItemInfo.NewLoc

    if not oldLoc
        or not newLoc
        or not newLoc.ItemSlot or
        not newLoc.ItemSlot.SlotType
        or not oldLoc.ItemSlot.SlotType then
        return
    end

    local newGroup = newLoc.ItemSlot:GetSlotGroup()
    local oldGroup = oldLoc.ItemSlot:GetSlotGroup()

    if newGroup == ESlotGroup.Player and oldGroup == ESlotGroup.Player then
        if oldLoc.ItemSlot.SlotType == ESlotType.BreastPlate then
            GuideConfig.EGuideEvent.evtUnquipBreastPlate:Invoke()
        elseif oldLoc.ItemSlot.SlotType == ESlotType.Helmet then
            GuideConfig.EGuideEvent.evtUnEquipHelmet:Invoke()
        end
    end

end

function GuideEvtLogic._OnSyncWarehouseBtnClicked()
    log("_OnSyncWarehouseBtnClicked")
    GuideConfig.EGuideEvent.evtGuideSyncWarehouseBtnClicked:Invoke()
end

function GuideEvtLogic._OnQuestChapterShow()
    log("_OnQuestChapterShow")
    GuideConfig.EGuideEvent.evtGuideQuestChapterLoadFinish:Invoke()

    if IsHD() then
        Module.Guide:CloseGuideHDWeakClickUI(149)

        local readyStageList = {}
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.firstOpenQuest) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.firstOpenQuest])
        end
        GuideLogic.TryStartGuideStage(readyStageList)
    else
        -- local readyStageList = {}
        -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.firstOpenQuest) then
        --     table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.firstOpenQuest])
        -- end
        -- GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnSyncQuestBtnClicked()
    log("_OnSyncQuestBtnClicked")
    GuideConfig.EGuideEvent.evtGuideSyncQuestBtnClicked:Invoke()
end

function GuideEvtLogic._OnQuestChapterBtnClicked()
    log("_OnQuestChapterBtnClicked")
    GuideConfig.EGuideEvent.evtGuideQuestChapterBtnClicked:Invoke()
end

function GuideEvtLogic._OnQuestItemShow()
    log("_OnQuestItemShow")
    GuideConfig.EGuideEvent.evtGuideQuestItemLoadFinish:Invoke()
end

function GuideEvtLogic._OnQuestDetailBtnClicked()
    log("_OnQuestDetailBtnClicked")
    GuideConfig.EGuideEvent.evtGuideQuestDetailBtnClicked:Invoke()
end

function GuideEvtLogic._OnQuestDetailShow()
    log("_OnQuestDetailShow")
    GuideConfig.EGuideEvent.evtGuideQuestDetailLoadFinish:Invoke()
end

function GuideEvtLogic._OnQuestStateChange()
    log("_OnQuestStateChange")
    GuideConfig.EGuideEvent.evtGuideQuestStateChange:Invoke()
end

function GuideEvtLogic._OnArmedForceSureBtnClicked()
    log("_OnArmedForceSureBtnClicked")
    GuideConfig.EGuideEvent.evtGuideArmedForceSureBtnClicked:Invoke()
end

function GuideEvtLogic._OnWareHouseTransferEnd(res)
    log("_OnWareHouseTransferEnd")
    GuideConfig.EGuideEvent.evtGuideWareHouseTransferEnd:Invoke()

    GuideConfig.EGuideEvent.evtContinueClicked:Invoke() -- 结算迭代导致的按钮没有发相应事件。在这个新的接口中重新转发一下
    -- local readyStageList = {}
    -- if (res and res.result > 0) or Server.InventoryServer:CheckMainWarehouseFull() then
    --     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.inventoryFull) then
    --         -- 是否在局外
    --         if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
    --             table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.inventoryFull])
    --         end
    --     end
    -- end
    -- GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnArmedForceWarehouseBtnClicked()
    log("_OnArmedForceWarehouseBtnClicked")
    GuideConfig.EGuideEvent.evtGuideArmedForceWarehouseBtnClicked:Invoke()
end

function GuideEvtLogic._OnSyncShopBtnClicked()
    log("_OnSyncShopBtnClicked")
    GuideConfig.EGuideEvent.evtGuideSyncShopBtnClicked:Invoke()
end

function GuideEvtLogic._OnShopChooseShow()
    log("_OnShopChooseShow")
    GuideConfig.EGuideEvent.evtGuideShopChooseOpenFinish:Invoke()

    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.firstOpenShop) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.firstOpenShop])
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnShopSpecMerchanBtnClicked()
    log("_OnShopSpecMerchanBtnClicked")
    GuideConfig.EGuideEvent.evtGuideShopSpecMerchanBtnClicked:Invoke()
end

function GuideEvtLogic._OnShopMainShow()
    log("_OnShopMainShow")
    GuideConfig.EGuideEvent.evtGuideShopMainOpenFinish:Invoke()
end

function GuideEvtLogic._OnGoSolBtnClicked()
    log("_OnGoSolBtnClicked")
    GuideConfig.EGuideEvent.evtGuideGoSolBtnClicked:Invoke()
end

function GuideEvtLogic._OnReadySolBtnClicked()
    log("_OnReadySolBtnClicked")
    GuideConfig.EGuideEvent.evtGuideGoSolBtnClicked:Invoke()
end

function GuideEvtLogic._OnAuctionSellOpen()
    log("_OnAuctionSellOpen")
    GuideConfig.EGuideEvent.evtAuctionSellOpen:Invoke()
    if DFHD_LUA == 1 then
    else
        -- local readyStageList = {}
        -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.auctionSell) then
        --     table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.auctionSell])
        -- end
        -- GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnAuctionSellHide()
    log("_OnAuctionSellHide")
    GuideConfig.EGuideEvent.evtAuctionSellHide:Invoke()
end

function GuideEvtLogic._OnShopItemSelected(shopItem)
    log("_OnShopItemSelected")
    if shopItem then
        GuideConfig.EGuideEvent.evtGuideShopItemSelect:Invoke()
    end
end

function GuideEvtLogic._OnItemDetailSliderBtnClicked()
    log("_OnItemDetailSliderBtnClicked")
    GuideConfig.EGuideEvent.evtItemDetailSliderBtnClicked:Invoke()
end

function GuideEvtLogic._OnItemDetailPanelIsFoldChange()
    log("_OnItemDetailPanelIsFoldChange")
    GuideConfig.EGuideEvent.evtItemDetailPanelIsFoldChange:Invoke()
end

--region evacuation view 撤离
function GuideEvtLogic._OnEvacuateTrophyShow()
    log("_OnEvacuateTrophyShow")
    GuideConfig.EGuideEvent.evtEvacuateTrophyViewLoadFinish:Invoke()
    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solMarkEarnings) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solMarkEarnings])
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end


-- 撤离成功 页面
function GuideEvtLogic._OnEvacuateWinViewShow()
    log("_OnEvacuateWinViewShow")
    GuideConfig.EGuideEvent.evtOnEvacuateWinViewOpenFinish:Invoke()
end


function GuideEvtLogic._OnEvacuationResultInfoOnShowBegin()
    log("_OnEvacuationResultInfoOnShowBegin")

    local readyStageList = {}

    local settlementInfo = Server.SettlementServer:GetSettlementInfo()
    local endGameInfo = Server.SettlementServer:GetEndGameInfo()
    if not settlementInfo or not endGameInfo then
        logerror("_OnEvacuationResultInfoOnShowBegin, settlementInfo or endGameInfo is nil")
        return
    end

    -- --story=122775322 [【手游】【父需求】【新手引导】手游屏蔽结算界面的撤离视频](https://tapd.woa.com/r/t?id=122775322&type=story) 
    if IsHD() then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solSettlementVideoHD) then
            if GuideLogic_CheckCondition.CanStartSolSettlementGuide(settlementInfo) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solSettlementVideoHD])
            end
        end
    else
        -- Mobile 在返回大厅后才会执行
        -- mobile in GuideEvtLogic.OnGuideOpenSafehouse()
    end

    GuideLogic.HandleStructTutorialStart(
        readyStageList,
        GuideConfig.EGuideStructTutorialStage.SettlementView,
        { endGameInfo = endGameInfo })
    

    GuideLogic.TryStartGuideStage(readyStageList)
end

--endregion

function GuideEvtLogic._OnLootBtnShow()
    log("_OnLootBtnShow")
    local readyStageList = {}
    -- 没有点击过按钮，尝试启动引导点击loot按钮
    if not Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.GuideLootBtnClickedInGame) then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.lootBtnGuide) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.lootBtnGuide])
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end


function GuideEvtLogic._OnStartLocalFocus()
    -- log("_OnStartLocalFocus")
    GuideEvtLogic.bLocalFocus = true
    if not GuideLogic.IsGameAndSolMode() then return end
    GuideEvtLogic.RemoveInGameGuideDelegate_SOL()
    GuideEvtLogic.AddInGameGuideDelegate_SOL()
end

function GuideEvtLogic._OnStopLocalFocus()
    -- log("_OnStartLocalFocus")
    GuideEvtLogic.bLocalFocus = false
    if not GuideLogic.IsGameAndSolMode() then return end
    GuideEvtLogic.RemoveInGameGuideDelegate_SOL()
end

function GuideEvtLogic._OnCharacterHealthChange(owner, curHealth, maxHealth)
    -- log("_OnCharacterHealthChange", curHealth, maxHealth)
    if not isvalid(GuideEvtLogic.cacheHealthCom) then
        return
    end

    -- sol对局内才生效，intro不要生效
    --local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if not GuideLogic.IsGameAndSolMode() then return end

    local guideField = Module.Guide.Field
    local lastHealth = guideField.inGamePlayerHealth
    guideField.inGamePlayerHealth = curHealth
    guideField.inGamePlayerMaxHealth = maxHealth
    -- 血量在下降且低于某个值
    if lastHealth > curHealth and (curHealth / maxHealth) <= GuideEvtLogic.guideHealthPercent then
        local readyStageList = {}
        if IsHD() then
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solScriptMatchPlayerStateHealthHD) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solScriptMatchPlayerStateHealthHD])
            end
        else
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.playerStateHealth) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.playerStateHealth])
            end
        end
        GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnCharacterIsAliveStateChanged(owner, newState, oldState)
    log("_OnCharacterIsAliveStateChanged", newState, oldState)

    -- if MP Mode?

    if not isvalid(GuideEvtLogic.cacheHealthCom) then
        error("_OnCharacterIsAliveStateChanged, cacheHealthCom is invalid")
        return
    end

    -- sol对局内才生效，intro不要生效
    if not GuideLogic.IsGameAndSolMode() then return end

    Module.Guide.Field.InGameIsAliveState:Set(newState)
    
    -- Knock Down
    if newState == ECharacterLiveStatus.ECharacterLiveStatus_ImpendingDeath then
        GuideLogic.DoStopAllGuide(true)
    end
end

function GuideEvtLogic._OnArmorInfoChanged(owner, curArmor, maxArmor)
    -- log("_OnArmorInfoChanged", curArmor, maxArmor)
    if not isvalid(GuideEvtLogic.cacheHealthCom) then
        return
    end
    local Armor = curArmor
    local ArmorMax = maxArmor
    local guideField = Module.Guide.Field
    local lastArmor = guideField.inGamePlayerArmor
    guideField.inGamePlayerArmor = Armor
    guideField.inGamePlayerMaxArmor = maxArmor
    -- 护甲在下降且低于某个值
    if lastArmor > Armor and (Armor / ArmorMax) <= GuideEvtLogic.guideArmorPercent then
        local readyStageList = {}
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.playerStateArmor) then
            local itemSlot = Server.InventoryServer:GetSlot(ESlotType.BreastPlate, ESlotGroup.Player)
            if itemSlot then
                local item = itemSlot:GetEquipItem()
                if item then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.playerStateArmor])
                end
            end
        end
        GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnPlayerKill(killInfo)
    log("_OnPlayerKill", killInfo.TakerIsPlayer, killInfo.TakerIsAILAB, killInfo.TakerIsRobotAI, killInfo.TakerIsAIBOSS)
    local readyStageList = {}
    -- 玩家击杀AI
    if not killInfo.TakerIsPlayer then
        local tutorialId = Server.GuideServer.mapGuideStageId2StructTutorialId[GuideConfig.EGuideStage.tSolLootDeadBodyTips]
        if tutorialId then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.tSolLootDeadBodyTips])
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnHelmetArmorChanged(owner, curArmor, maxArmor)
    log("_OnHelmetArmorInfoChanged", curArmor, maxArmor)
    if not isvalid(GuideEvtLogic.cacheHealthCom) then
        return
    end
    local Armor = curArmor
    local ArmorMax = maxArmor
    local guideField = Module.Guide.Field
    local lastArmor = guideField.inGamePlayerHelmetArmor
    guideField.inGamePlayerHelmetArmor = Armor
    guideField.inGamePlayerHelmetMaxArmor = maxArmor
    -- 头盔护甲在下降且低于某个值
    if lastArmor > Armor and (Armor / ArmorMax) <= GuideEvtLogic.guideArmorPercent then
        local readyStageList = {}
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.playerStateArmor) then
            local itemSlot = Server.InventoryServer:GetSlot(ESlotType.Helmet, ESlotGroup.Player)
            if itemSlot then
                local item = itemSlot:GetEquipItem()
                if item then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.playerStateArmor])
                end
            end
        end
        GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnItemUseInGame(itemId)
    log("_OnItemUseInGame", itemId)
    if table.contains(GuideConfig.MedOperationItems, itemId) then
        GuideConfig.EGuideEvent.evtUseMedOperationSetItems:Invoke()
    end

    if table.contains(GuideConfig.RouletteAddHpMedList, itemId) then
        GuideConfig.EGuideEvent.evtUseItemAddHp:Invoke()
    end
    if IsHD() then
        if table.contains(GuideConfig.RouletteAddDEFMedList, itemId) then
            GuideConfig.EGuideEvent.evtUseItemAddArmor:Invoke()
        end
    else
        if table.contains(GuideConfig.OnlyAmorRepairItemIds, itemId) then
            GuideConfig.EGuideEvent.evtUseItemAddArmor:Invoke()
        end
        if table.contains(GuideConfig.OnlyHelmetAmorRepairItemIds, itemId) then
            GuideConfig.EGuideEvent.evtUseItemAddArmor:Invoke()
        end
    end
    if table.contains(GuideConfig.RouletteLegAddHpMedList, itemId) then
        GuideConfig.EGuideEvent.evtUseItemCureHurtLeg:Invoke()
    end
    if table.contains(GuideConfig.RouletteHandAddHpMedList, itemId) then
        GuideConfig.EGuideEvent.evtUseItemCureHurtArm:Invoke()
    end
    if table.contains(GuideConfig.RouletteHeadAddHpMedList, itemId) then
        GuideConfig.EGuideEvent.evtUseItemCureHurtHead:Invoke()
    end
    if table.contains(GuideConfig.RouletteChestAddHpMedList, itemId) then
        GuideConfig.EGuideEvent.evtUseItemCureHurtChest:Invoke()
    end
end

function GuideEvtLogic._OnLootEachDone()
    log("_OnLootEachDone")
    GuideConfig.EGuideEvent.evtLootSearchDone:Invoke()
end

-- ClientBuff: FCharacterBuff
function GuideEvtLogic._OnClientNotifyAddBuff(ClientBuff)
    -- log("_OnClientNotifyAddBuff", ClientBuff.BuffID)
    if not GuideLogic.IsGameAndSolMode() then return end

    local guideField = Module.Guide.Field
   table.insert(guideField.inGameBuffList, ClientBuff.BuffID)

    local buffToGuideDict = GuideConfig.BuffIdToGuideStageId
    local readyStageList = {}
    local curBuffGuide = buffToGuideDict[ClientBuff.BuffID]
    if curBuffGuide and GuideLogic.CheckMeetGuideStageStartCondition(curBuffGuide) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[curBuffGuide])
    end

    local defuffIds = GuideConfig.PartDeBuffIds or {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solPhysicalNegativeState) then 
        if table.contains(defuffIds, ClientBuff.BuffID) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solPhysicalNegativeState])
        end
    end

    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnClientNotifyRemoveBuff(BuffId, IsPauseBuff, CharacterPart)
    -- log("_OnClientNotifyRemoveBuff", BuffId)
    if not GuideLogic.IsGameAndSolMode() then return end

    local guideField = Module.Guide.Field
    table.removebyvalue(guideField.inGameBuffList, BuffId)
end

function GuideEvtLogic._OnCombatStateUpdate(bInCombat)
    log("_OnCombatStateUpdate", bInCombat)
    --Module.CommonTips:ShowSimpleTip(string.format(GuideConfig.Loc.guideDialogNpcName, tostring(bInCombat)))
    Module.Guide.Field.bInCombat = bInCombat
    if bInCombat then
        GuideEvtLogic._InCombatState()
    else
        GuideEvtLogic._OutCombatState()
    end
end

function GuideEvtLogic._InCombatState()
    -- 需要停止要避开战斗状态的引导
    if GuideLogic.IsGuiding() then
        local guideField = Module.Guide.Field
        local curGuideId = guideField:GetCurGuideId()
        local guideConfig = guideField:GetGuideCfg(curGuideId)
        if GuideLogic.CheckGuideNeedNotInCombat(guideConfig.GuideStageId) then
            GuideLogic.DoStopAllGuide()
        end
        GuideLogic.TryStopGuideStep(GuideConfig.EStopStepCondition.InCombat)
    end
end

function GuideEvtLogic._OutCombatState()
    -- 重启状态引导
    if not isvalid(GuideEvtLogic.cacheHealthCom) then
        return
    end
    local readyStageList = {}

    -- -- 血量
    -- local guideField = Module.Guide.Field
    -- local lastHealth = guideField.inGamePlayerHealth
    -- if guideField.inGamePlayerMaxHealth > 0 and (guideField.inGamePlayerHealth / guideField.inGamePlayerMaxHealth) <= GuideEvtLogic.guideHealthPercent then
    --     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.playerStateHealth) then
    --         table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.playerStateHealth])
    --     end
    -- end

    -- -- 护甲
    -- if guideField.inGamePlayerMaxArmor > 0 and (guideField.inGamePlayerArmor / guideField.inGamePlayerMaxArmor) <= GuideEvtLogic.guideArmorPercent then
    --     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.playerStateArmor) then
    --         table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.playerStateArmor])
    --     end
    -- end

    -- -- Buff
    -- local buffToGuideDict = GuideConfig.BuffIdToGuideStageId
    -- for _, buffId in ipairs(guideField.inGameBuffList) do
    --     local curBuffGuide = buffToGuideDict[buffId]
    --     if curBuffGuide and GuideLogic.CheckMeetGuideStageStartCondition(curBuffGuide) then
    --         table.insert(readyStageList, GuideConfig.TableGuideStageConfig[curBuffGuide])
    --     end
    -- end

    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnNoExceptionSettlement()
    log("_OnNoExceptionSettlement")
    GuideLogic.SetWaitSettlementState(false)
    if not GuideLogic.GetNextNewPlayerGuideStage() and #GuideLogic.GetCurNewPlayerGuideStageNotForce() <= 0 then
        return
    end
    -- 当前没有异常结算，尝试启动新手引导
    GuideEvtLogic.OnGuideOpenSafehouse()
end

function GuideEvtLogic._OnSettlementUIOpenFinished()
    log("_OnSettlementUIOpenFinished")
    GuideLogic.SetWaitSettlementState(false)

    if not GuideLogic.GetNextNewPlayerGuideStage() and #GuideLogic.GetCurNewPlayerGuideStageNotForce() <= 0 then
        return
    end
    GuideEvtLogic.OnGuideOpenSafehouse()
end

function GuideEvtLogic._OnSettlementSkillTutorialClicked()
    log("_OnSettlementSkillTutorialClicked")
    GuideConfig.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.DeathDamageSkillAgainstGuideClicked)
    local readyStageList = {}
    if GuideLogic_CheckCondition.NeedSkillAgainstGuide() then
        if GuideLogic.GetFirstAvaliableSkillAgainstEntry() then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solSkillAgainstPopWindow)
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnCommonBarTabIndexChanged()
    log("_OnCommonBarTabIndexChanged")
    GuideConfig.EGuideEvent.evtCommonBarTabIndexChanged:Invoke()
end


function GuideEvtLogic._OnClickSandBoxTarget(targetIdx)
    log("_OnClickSandBoxTarget", targetIdx)

    local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()
    local _checkMap = function(mapIds)
        for _, mapId in ipairs(mapIds) do
            local mapCfg = Server.GameModeServer:GetWorldEntranceInfoByMapID(mapId)
            if mapCfg then
                local bLock = sandBoxMapUtil.IsTargetPointLocked(mapCfg)
                if not bLock then
                    GuideConfig.EGuideEvent.evtOnSandboxItemClicked:Invoke()
                    -- Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.FirstForrestMapClicked, true)
                    Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.FirstMapItemClicked, true)
                    return true
                end
            end
        end
        return false
    end

    -- mapId source: see MapConfig
    if targetIdx == 2 then
        _checkMap({ 1901, 1902, 1911, 1912 })
    elseif targetIdx == 3 then
        _checkMap({ 2201, 2202, 2211, 2212 })
    elseif targetIdx == 6 then
        _checkMap({ 8101, 8102 })
    elseif targetIdx == 4 then
        _checkMap({ 3901, 3902 })
    end
end

function GuideEvtLogic._OnSystemSettingMainUIOpen()
    log("_OnSystemSettingMainUIOpen")
    GuideConfig.EGuideEvent.evtOnSystemSettingMainUIOpen:Invoke()
end
function GuideEvtLogic._OnSystemSettingMainUIClose()
    log("_OnSystemSettingMainUIClose")
    GuideConfig.EGuideEvent.evtOnSystemSettingMainUIClose:Invoke()
end


-- 开始loot搜索就可以停止tick了
function GuideEvtLogic._OnBeginLootSearch()
    log("_OnBeginLootSearch")
    Module.Guide:RemoveSelfTick()
end

function GuideEvtLogic._OnArmedForceMainBeginShow()
    log("_OnArmedForceMainBeginShow")
    GuideConfig.EGuideEvent.evtArmedForceMainShow:Invoke()

    GuideEvtLogic._TryStartArmedForceMainGuide("_OnArmedForceMainBeginShow")
end

function GuideEvtLogic._OnArmedForceMainHide()
    log("_OnArmedForceMainHide")
    GuideConfig.EGuideEvent.evtArmedForceMainHide:Invoke()
end

function GuideEvtLogic._OnQuestAcceptSuccess()
    log("_OnQuestAcceptSuccess")
    GuideConfig.EGuideEvent.evtQuestAcceptSuccess:Invoke()
end

function GuideEvtLogic._OnQuestGetReward()
    log("_OnQuestGetReward")
    GuideConfig.EGuideEvent.evtQuestGetReward:Invoke()
end

function GuideEvtLogic._OnAssemblySquadPickOpenFinish()
    log("_OnAssemblySquadPickOpenFinish")
    GuideConfig.EGuideEvent.evtAssemblySquadPickOpenFinish:Invoke()

    GuideLogic.StopGuideNotFirst()

    if IsHD() then
    else
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
            local readyStageList = {}
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpSelectHero) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpSelectHero])
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        end
    end    
end

function GuideEvtLogic._OnAssemblySquadPickHeroClicked()
    log("_OnAssemblySquadPickHeroClicked")
    GuideConfig.EGuideEvent.evtAssemblySquadPickHeroClicked:Invoke()
end

function GuideEvtLogic._OnAssemblySquadPickClose()
    log("_OnAssemblySquadPickClose")
    GuideConfig.EGuideEvent.evtAssemblySquadPickClose:Invoke()
end

function GuideEvtLogic._OnAssemblySquadPickConfirmClicked()
    log("_OnAssemblySquadPickConfirmClicked")
    GuideConfig.EGuideEvent.evtAssemblySquadPickConfirmClicked:Invoke()
end

function GuideEvtLogic._OnSandBoxConfrimTarget()
    log("_OnSandBoxConfrimTarget")
    GuideConfig.EGuideEvent.evtConfrimTarget:Invoke()
end

function GuideEvtLogic._OnAssemblySubViewChanged(subType)
    log("_OnAssemblySubViewChanged", subType)
    -- if subType == Module.ArmedForce.Config.ESubViewType.Medicine then
    --     GuideConfig.EGuideEvent.evtAssemblyMedicineState:Invoke()

    -- elseif subType == Module.ArmedForce.Config.ESubViewType.Bullet then
    --     GuideConfig.EGuideEvent.evtAssemblyBulletState:Invoke()
    -- end
end

function GuideEvtLogic._OnAssemblyMedicineOpenFinish()
    log("_OnAssemblyMedicineOpenFinish")
    GuideConfig.EGuideEvent.evtArmedForceQuickOperationOpenFinish:Invoke()
end

function GuideEvtLogic._OnAssemblyQuickOperationTryOpen()
    log("_OnAssemblyQuickOperationTryOpen")
    GuideConfig.EGuideEvent.evtArmedForceQuickOperationTryOpen:Invoke()
end

function GuideEvtLogic._OnAssemblyMedicineConfirmClicked()
    log("_OnAssemblyMedicineConfirmClicked")
    GuideConfig.EGuideEvent.evtArmedForceQuickOperationConfirmClicked:Invoke()
end

function GuideEvtLogic._OnEvacuateContinueClicked()
    log("_OnEvacuateContinueClicked")
    GuideConfig.EGuideEvent.evtContinueClicked:Invoke()
end

function GuideEvtLogic._OnEvacuateExpViewOpen()
    log("_OnEvacuateExpViewOpen")
    GuideConfig.EGuideEvent.evtEvacuateExpViewOpen:Invoke()
end

function GuideEvtLogic._OnItemDetailPanelTryOpen()
    -- log("_OnItemDetailPanelTryOpen")
    GuideConfig.EGuideEvent.evtItemDetailPanelTryOpen:Invoke()

    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow ~= EGameFlowStageType.Game then
        local readyStageList = {}
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solItemDetailFold)
        GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnItemDetailBtnCreateFinish()
    -- log("_OnItemDetailBtnCreateFinish")
    GuideConfig.EGuideEvent.evtItemDetailBtnCreateFinish:Invoke()
end

function GuideEvtLogic._OnItemDetailSellClicked()
    log("_OnItemDetailSellClicked")
    GuideConfig.EGuideEvent.evtItemDetailSellClicked:Invoke()
end

function GuideEvtLogic._OnCommonWindowOpenFinish()
    log("_OnCommonWindowOpenFinish")
    GuideConfig.EGuideEvent.evtCommonWindowOpenFinish:Invoke()
end

function GuideEvtLogic._OnItemSellResult()
    log("_OnItemSellResult")
    GuideConfig.EGuideEvent.evtItemDetailSellResult:Invoke()
end

function GuideEvtLogic._OnQuestObjectionStateChanged(questId, objectionId, newState)
    log("_OnQuestObjectionStateChanged", questId, objectionId, newState)
    GuideConfig.EGuideEvent.evtQuestObjectionFinished:Invoke()
end

function GuideEvtLogic._OnTaskSelected()
    log("_OnTaskSelected")
    GuideConfig.EGuideEvent.evtOnTaskSelected:Invoke()
end

function GuideEvtLogic._OnBigMapIconLoadFinished()
    log("_OnBigMapIconLoadFinished")
    GuideConfig.EGuideEvent.evtOnBigMapItemLoadFinished:Invoke()
end

-- function GuideEvtLogic._OnModeHallShow()
    -- log("_OnModeHallShow")
    -- if true then return end
    
    -- local readyStageList = {}
    -- if Module.Guide.Field.bBackFromSafeHouse == false then
    --     if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.enterHall) then
    --         table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.enterHall])
    --     end
    -- end
    -- GuideLogic.TryStartGuideStage(readyStageList)
-- end

function GuideEvtLogic._OnModeHallOpenAnimFinish()
    log("_OnModeHallOpenAnimFinish")
    GuideConfig.EGuideEvent.evtModeHallOpenAnimFinish:Invoke()
end

function GuideEvtLogic._OnModeHallClose()
    log("_OnModeHallClose")
    GuideConfig.EGuideEvent.evtModeHallClose:Invoke()
end

function GuideEvtLogic._OnModeHallChangeMode()
    log("_OnModeHallChangeMode")
    GuideConfig.EGuideEvent.evtModeHallChangeMode:Invoke()
end

function GuideEvtLogic._OnRenameFinished()
    log("_OnRenameFinished")
    GuideConfig.EGuideEvent.evtRenameFinished:Invoke()
end

function GuideEvtLogic._OnUnlockUIOpen()
    log("_OnUnlockUIOpen")
    Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.LevelUpUnlockUI)
    GuideConfig.EGuideEvent.evtUnlockUIOpen:Invoke()
end

function GuideEvtLogic._OnUnlockUIClose()
    log("_OnUnlockUIClose")
    Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.LevelUpUnlockUI)
    GuideConfig.EGuideEvent.evtUnlockUIClose:Invoke()
end

function GuideEvtLogic._OnRegisterFinish()
    log("_OnRegisterFinish")
    GuideConfig.EGuideEvent.evtOnRegisterFinish:Invoke()
end


-- ---  @slafterwang: She1 用 group id 判断夜战 
-- local nightBattleGroupId =
--     GuideLogic.IsRegionCN() and { 10120, 120 } or
--     GuideLogic.IsRegionGA() and { 20120, 10120, 120 } or
--     GuideLogic.IsRegionGlobal() and { 20120, 10120, 120 } or
--     {}


function GuideEvtLogic._OnMpMainUIOpenFinished()
    log("_OnMpMainUIOpenFinished")
    GuideConfig.EGuideEvent.evtMpMainPanelShowAnimFinish:Invoke()

    local readyStageList = {}
    local lastStackID = Facade.UIManager:GetLastStackUIId()
    if lastStackID == UIName2ID.BattlefieldModeSelector then

        -- assert(MatchSubMode.TDMReserve5 == 70, "MatchSubMode.TDMReserve5 is not 70")

        local groupId = Module.BattlefieldEntry:GetGroupId()
        local groupType = Module.BattlefieldEntry:GetGroupType()
        log("_OnMpMainUIOpenFinished", groupId, groupType)

        ---  She1 用 group id 判断夜战
        -- if table.contains(nightBattleGroupId, groupId) then
            -- GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpNightMap)
        -- if groupId == 120 then --2025/5/21 she2 -> 120  海盗战
        if groupType == 120 then
            --<dexzhou> 2025/6/17 用 GroupType 判断风暴眼攻防
            -- GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpIsland)
            --<dexzhou> 2025/7/23 she3 替换为断层
            --bug=146405327 [【AtBug】屏蔽断层新手引导配置](https://tapd.woa.com/r/t?id=146405327&type=bug) 
            -- GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpMapFault)

        else
            -- determine by MatchMode
            for _, v in pairs(Server.GameModeServer:GetTDMMapInGroup(groupId) or {}) do
                local matchMode = Module.GameMode:GetMatchModeByMatchModeId(v.match_id)
                if matchMode ~= nil then
                    log("_OnMpMainUIOpenFinished", matchMode.MatchModeID, matchMode.MapID, matchMode.MatchSubMode)
                    local subMode = matchMode.MatchSubMode
                    if subMode == MatchSubMode.TDMReserve5 then -- mp 指挥官 aka 巅峰赛
                        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpPeakLeague)
                        break
                        -- 2025/3/27 <dexzhou> 夜战并入主模式
                        -- elseif subMode == MatchSubMode.TDMReserve6 then -- mp夜战
                        --     GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpNightMap)
                        --     break
                    elseif subMode == MatchSubMode.TDMFlagBattle then -- mp 夺旗
                        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpFlagIntro)
                        break
                    end
                end
            end
        end
    end

    log("plat", IsBuildRegionGA(), IsBuildRegionGlobal(), "IsHD", IsHD())
    --story=124518312 [【手游|PC】【前台】【新手引导】【She3】GA大战场新增中退视频](https://tapd.woa.com/r/t?id=124518312&type=story) 
    -- 需求上是 GA ?
    if not IsHD() and (IsBuildRegionGA() or IsBuildRegionGlobal()) then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpFailedOrQuit) then
            if GuideLogic_CheckCondition.NeedMpQuitOrFailedGuide() then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpFailedOrQuit])
            end
        end
    end

    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnMpStartClicked()
    log("_OnMpStartClicked")
    GuideConfig.EGuideEvent.evtStartClicked:Invoke()
end

function GuideEvtLogic._OnBattlefieldEntryModeSelectorShowBegin()
    log("_OnBattlefieldEntryModeSelectorShowBegin")
end

function GuideEvtLogic._OnMpMainUIOpen()
    log("_OnMpMainUIOpen")
    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpDesc) then
        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpDesc])
    end

    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnMpMainPanelShowBegin()
    local curMpCount = Server.GuideServer:GetMatchCount(MatchGameRule.TDMClassGameRule)
    log("_OnMpMainPanelShowBegin curMpCount", curMpCount)


    local _afterUpdateMp = function(result)
        log("_OnMpMainPanelShowBegin _afterUpdateMp", result)
        if result == 0 then
            GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.MpWaitGameNum)
            if Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Lobby then
                logwarning("_OnMpMainPanelShowBegin _afterUpdateMp not in lobby, cur game flow", Facade.GameFlowManager:GetCurrentGameFlow())
                return
            end

            GuideEvtLogic._TryStartMpMainGuide("OnMainPanelShowBegin")
        elseif result > 0 then
            GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.MpWaitGameNum)
            logerror("_OnMpMainPanelShowBegin _afterUpdateMp, res not valid", result)      
        else
            -- disconnected
            -- GuideLogic.SetOnlyShowGuideState(false, GuideConfig.EGuideShowUIFlag.MpWaitGameNum)
            logwarning("_OnMpMainPanelShowBegin _afterUpdateMp, res not valid", result)      
        end
    end

    if curMpCount == 0 then
        GuideEvtLogic._bFetchingMpData = true
        GuideLogic.SetOnlyShowGuideState(true, GuideConfig.EGuideShowUIFlag.MpWaitGameNum)
        local mpMatchInfo = {}
        mpMatchInfo.game_mode = MatchGameMode.TDMGameMode
        mpMatchInfo.game_rule = 0
        Server.RoleInfoServer:FetchMPSeasonTotalDataByMatchInfo(mpMatchInfo, nil, _afterUpdateMp, true)
    else
        GuideEvtLogic._TryStartMpMainGuide("OnMpMainPanelShowBegin", curMpCount)
    end
end

function GuideEvtLogic._OnMpMainUIHide()
    log("_OnMpMainUIHide")
    GuideConfig.EGuideEvent.evtMainPanelHide:Invoke()
end

function GuideEvtLogic._OnMpSelectModeUIOpen()
    log("_OnMpSelectModeUIOpen")
    GuideConfig.EGuideEvent.evtMpSelectModeUIOpen:Invoke()
end

function GuideEvtLogic._OnMpSelectModeUIShowAnimFinish()
    log("_OnMpSelectModeUIShowAnimFinish")
    GuideConfig.EGuideEvent.evtMpSelectModeUIShowAnimFinish:Invoke()
end

function GuideEvtLogic._OnMpVedioPlayStart()
    log("_OnMpVedioPlayStart")
    GuideConfig.EGuideEvent.evtOnMpVedioPlayStart:Invoke()
end

function GuideEvtLogic._OnMpVedioPlayEnd()
    log("evtOnMpVedioPlayEnd")
    GuideConfig.EGuideEvent.evtOnMpVedioPlayEnd:Invoke()
end

function GuideEvtLogic._OnMpConfirmModeBtnClick()
    log("_OnMpConfirmModeBtnClick")
    GuideConfig.EGuideEvent.evtOnMpConfirmModeBtnClick:Invoke()
end

function GuideEvtLogic._OnSandBoxDetailShowFinished()
    log("_OnSandBoxDetailShowFinished")
    GuideConfig.EGuideEvent.evtDetailInAniFinished:Invoke()
end

--@type FWorldEntranceConfig
--@field group 
function GuideEvtLogic._OnStrategicSelectionSOLMainPanelRefresh(worldEntranceCfg, group, multiType, bNight)
    --[[ 2024/8/1
        修改为：只有森林，大坝，巴克什中倍场解锁时有机会触发，航天城解锁中倍场不触发
        world entrance config
        森林(长弓溪谷)--2
        大坝-1
        巴克什-13 
        2024/10/31 巴克什普通被移除
    ]]--
    if bNight then
        return
    end

    local bHighYieldGuideEntranceIdx = false
    for _, v in pairs({ 2, 1 }) do
        if v == worldEntranceCfg.EntranceIdx then
            bHighYieldGuideEntranceIdx = true
            break
        end
    end
    local lockReason = Server.GameModeServer:GetTargetPointLockReason(worldEntranceCfg.EntranceIdx, MatchSubMode.HighYieldSOLPMC, multiType)
    local bMapOpen = lockReason == MapBoardLockReason.MapOpen 
    log("Guide HighYieldSOLPMC, bHighYieldGuideEntranceIdx:", bHighYieldGuideEntranceIdx, " bMapOpen:", bMapOpen, " lockReason:", lockReason)

    local readyStageList = {}
    if bHighYieldGuideEntranceIdx and bMapOpen then
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solStrategySecretOperation)
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnSandBoxPostRefreshPanel(worldEntranceCfg, group, multiType)
    
end

function GuideEvtLogic._OnSandBoxStrategicSelectionSOLSubModeChange(bHighMode, ESubMode)
    if not bHighMode  then
        if ESubMode == MatchSubMode.HighYieldSOLPMC then
            GuideConfig.EGuideEvent.evtStrategicSelectionSOLChange2HighYield:Invoke()
        elseif ESubMode == MatchSubMode.SOLPMC then
            GuideConfig.EGuideEvent.evtStrategicSelectionSOLChange2LowYield:Invoke()
        end
    else 
        if ESubMode == MatchSubMode.HighYieldSOLPMC then
            GuideConfig.EGuideEvent.evtStrategicSelectionSOLChange2HighYield:Invoke()
        elseif ESubMode == MatchSubMode.ExtremeHighYieldSOLPMC then
            GuideConfig.EGuideEvent.evtStrategicSelectionSOLChange2ExtremeHighYield:Invoke()
        end
    end
end

function GuideEvtLogic._OnSandBoxStrategicSelectionSOLActionFileOnShowBegin()
    log("_OnSandBoxStrategicSelectionSOLActionFileOnShowBegin")
    GuideConfig.EGuideEvent.evtSandBoxStrategicSelectionSOLActionFileOnShowBegin:Invoke()
end

function  GuideEvtLogic._OnSandBoxStrategicSelectionSOLActionFileOnHideBegin()
    log("_OnSandBoxStrategicSelectionSOLActionFileOnHideBegin")
    GuideConfig.EGuideEvent.evtSandBoxStrategicSelectionSOLActionFileOnHideBegin:Invoke()
end

function GuideEvtLogic. _OnStrategicSelectionMapDownloadBtnClicked()
    GuideConfig.EGuideEvent.evtStrategicSelectionMapDownloadBtnClicked:Invoke()
end



function GuideEvtLogic._OnSandBoxConfirmBtnClicked()
    log("_OnSandBoxConfirmBtnClicked")
    GuideConfig.EGuideEvent.evtSOLConfirmBtnClicked:Invoke()
    Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.FirstForrestSelected, true)
end

function GuideEvtLogic._OnGuideEndReport(endGuideStageId, tryPlayNextStageId)
    log("_OnGuideEndReport")
    Module.GCloudSDK.OnGuideEndReport(endGuideStageId, tryPlayNextStageId)
end

function GuideEvtLogic._OnBeginSeamless()
    log("_OnBeginSeamless")
    GuideConfig.EGuideEvent.evtBeginSeamless:Invoke()

    GuideLogic.StopGuideNotFirst()
end

function GuideEvtLogic._OnRealBoarding()
    log("_OnRealBoarding")
    if true then return end

    -- GuideConfig.EGuideEvent.evtRealBoarding:Invoke()
    -- if Module.Guide.Field.bControlLoadingView then
    --     log("_OnRealBoarding")
    --     local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
    --     if DFMGameLoadingManager then
    --         DFMGameLoadingManager:SetCloseLoadingViewAfterLoading(true)
    --         DFMGameLoadingManager:ShutDownLoadingView()
    --         Module.Guide.Field:StopFakeLoadingTimer()
    --         Module.Guide.Field.bControlLoadingView = false
    --     else
    --         err("_OnBeginSeamless, DFMGameLoadingManager is nil !!!")
    --     end
    -- end
end

function GuideEvtLogic._OnSeamlessEnterFirstSeqStart()
    log("_OnSeamlessEnterFirstSeqStart")
    GuideConfig.EGuideEvent.evtOnSeamlessEnterFirstSeqStart:Invoke()
    GuideLogic.StopLoadingViewControl()
end

function GuideEvtLogic._OnIrisEnterStageChange(NewStage, OldStage)
    log("_OnIrisEnterStageChange",NewStage, OldStage)
    Module.Guide.Field.IrisEnterStageState:Set({ current = NewStage, last = OldStage })

    if OldStage~= EIrisEnterStageType.None and NewStage == EIrisEnterStageType.Fail then
        GuideConfig.EGuideEvent.evtOnIrisEnterStageChange:Invoke()
        GuideLogic.StopLoadingViewControl()
    end
end

function GuideEvtLogic._OnAuctionOnShelfUncountableSellOpen()
    log("_OnAuctionOnShelfUncountableSellOpen")
    GuideConfig.EGuideEvent.evtAuctionOnShelfUncountableSellOpen:Invoke()
end

function GuideEvtLogic._OnArmedForceQuickOperationOpenBegin(subView)
    log("_OnArmedForceQuickOperationOpenBegin", subView)
    local readyStageList = {}
    if subView == Module.ArmedForce.Config.ESubViewType.Medicine then
        if IsHD() then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.armedForceMedicineOpen)
        end
        GuideConfig.EGuideEvent.evtAssemblyMedicineState:Invoke()

    elseif subView == Module.ArmedForce.Config.ESubViewType.Bullet then
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.bulletAndArmor)
        if IsHD() then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.armedForceBulletOpen)
        end
        GuideConfig.EGuideEvent.evtAssemblyBulletState:Invoke()
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnAuctionSellAuctionFail()
    log("_OnAuctionSellAuctionFail")
    GuideConfig.EGuideEvent.evtAuctionSellAuctionFail:Invoke()
end

function GuideEvtLogic._OnAuctionSellAuctionClicked()
    log("_OnAuctionSellAuctionClicked")
    GuideConfig.EGuideEvent.evtAuctionSellAuctionClicked:Invoke()
end

function GuideEvtLogic._OnAuctionRealSellAuctionClicked()
    log("_OnAuctionRealSellAuctionClicked")
    GuideConfig.EGuideEvent.evtAuctionRealSellAuctionClicked:Invoke()
end

function GuideEvtLogic._OnAuctionShelveClose()
    log("_OnAuctionShelveClose")
    GuideConfig.EGuideEvent.evtAuctionShelveClose:Invoke()
end

function GuideEvtLogic._OnEvtSwitchMoudleUnlock(module_id, isUnlock)
    log("_OnEvtSwitchMoudleUnlock", module_id)
    if DFHD_LUA == 1 then
        if module_id == SwitchModuleID.ModuleAuctionSell then
            if GuideLogic.IsAuctionUnlockSell() and Server.GuideServer:IsWaitAuctionUnlock() then
                Server.GuideServer:SetGuidePassed(GuideConfig.WaitAuctionUnlockStage)
            end
        end
    else
        if module_id == SwitchModuleID.ModuleAuctionSell then
            if GuideLogic.IsAuctionUnlockSell() and Server.GuideServer:IsWaitAuctionUnlock() then
                Server.GuideServer:SetGuidePassed(GuideConfig.WaitAuctionUnlockStage)
            end
        end
    end
end

function GuideEvtLogic._OnLevelChanged(newLevel, oldLevel)
    log("_OnLevelChanged",newLevel, oldLevel)
    if DFHD_LUA == 1 then
        if GuideLogic.IsAuctionUnlockSell() and Server.GuideServer:IsWaitAuctionUnlock() then
            Server.GuideServer:SetGuidePassed(GuideConfig.WaitAuctionUnlockStage)
        end
    else
        if GuideLogic.IsAuctionUnlockSell() and Server.GuideServer:IsWaitAuctionUnlock() then
            Server.GuideServer:SetGuidePassed(GuideConfig.WaitAuctionUnlockStage)
        end
    end
end

function GuideEvtLogic._OnShowModuleUnlock(unlockId)
    log("_OnShowModuleUnlock",unlockId)
    local delay = function()
        Module.Guide.CloseGuideDialogUI()
    end
    if unlockId == 2000 then
        --GuideLogic.SafeHouseNpcSpeak("SafeHouseUnlockActionBuy")
        Module.Guide:OpenGuideDialogUI(37, nil, IsHD() and -200 or 0)
        Timer.DelayCall(5, delay)
        Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.AuctionVOBuyUnlock)
    elseif unlockId == 2001 then
        --GuideLogic.SafeHouseNpcSpeak("SafeHouseUnlockActionSell")
        Module.Guide:OpenGuideDialogUI(38, nil, IsHD() and -200 or 0)
        Timer.DelayCall(5, delay)
        Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.AuctionVOSellUnlock)
    end
end

function GuideEvtLogic._OnPathOfGrowthLevelUpPanelOpen(preLevel, currentLevel, bIsMp)
    log("_OnPathOfGrowthLevelUpPanelOpen",preLevel, currentLevel, bIsMp)
    if bIsMp then
        return
    end

    local delay = function()
        Module.Guide.CloseGuideDialogUI()
    end
    -- 出售是否解锁
    local unlockInfo = Server.ModuleUnlockServer:GetModuleUnlockInfoById(2001)
    if not unlockInfo or unlockInfo.unlock1Type ~= 1 then
        logerror("_OnPathOfGrowthLevelUpPanelOpen no unlock info, id:2001 type:", unlockInfo and unlockInfo.unlock1Type)
        return
    end
    if currentLevel >= unlockInfo.unlock1Condition and preLevel < unlockInfo.unlock1Condition then
        Module.Guide:OpenGuideDialogUI(38, nil, IsHD() and 0 or 140)
        Timer.DelayCall(5, delay)
        Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.AuctionVOSellUnlock)
        return
    end

    -- 购买是否解锁
    unlockInfo = Server.ModuleUnlockServer:GetModuleUnlockInfoById(2000)
    if not unlockInfo or unlockInfo.unlock1Type ~= 1 then
        logerror("_OnPathOfGrowthLevelUpPanelOpen no unlock info, id:2000 type:", unlockInfo and unlockInfo.unlock1Type)
        return
    end
    if currentLevel >= unlockInfo.unlock1Condition and preLevel < unlockInfo.unlock1Condition then
        Module.Guide:OpenGuideDialogUI(37, nil, IsHD() and 0 or 170)
        Timer.DelayCall(5, delay)
        Module.Guide:SendGuideSpecLog(Module.Guide.Config.ESpecCommonEventLogId.AuctionVOBuyUnlock)
        return
    end
end

function GuideEvtLogic._OnConnectSuccess()
    log("_OnConnectSuccess")
    GuideConfig.EGuideEvent.evtOnGuideMsg:Invoke("ConnectSuccess")

    if not Module.Guide:IsGuiding() then
        return
    end

    local curGuideStage = Module.Guide.Field:GetCurGuideStage()
    local bRestart = false
    for _ = 1, 1 do
        -- if curGuideStage == GuideConfig.EGuideStage.newPlayerGuideStage2 then
        --     bRestart = true
        --     break
        -- end
        if curGuideStage == GuideConfig.EGuideStage.newPlayerGuideStage3 then
            bRestart = true
            break
        end
    end

    if bRestart then
        Module.Guide:StopAllGuide(true)
        GuideLogic.DoStartStageGuide(curGuideStage)
    end
end

function GuideEvtLogic._OnLuaBusinessShutDown()
    log("_OnLuaBusinessShutDown")
    Module.Guide:StopAllGuide()
end

function GuideEvtLogic._OnGatewayKickPlayer()
    log("_OnGatewayKickPlayer")
    Module.Guide:StopAllGuide()
end

---@param itemMoveInfo itemMoveInfo
function GuideEvtLogic._OnLootingItemMove(itemMoveInfo)
    local item = itemMoveInfo.item
    local itemId = item.id
    local itemMainType = item.itemMainType
    if itemMoveInfo.Reason == PropChangeType.Modify then
        if item:GetLastModifyReason() == EItemInfoUpdatedReason.Location then
            -- log("_OnLootingItemMove")
            GuideConfig.EGuideEvent.evtLootingItemMove:Invoke(itemMainType,itemId)
        end
    elseif itemMoveInfo.Reason == PropChangeType.Add then
        -- log("_OnLootingItemMove, add or del")
        GuideConfig.EGuideEvent.evtLootingItemMove:Invoke(itemMainType, itemId)
    elseif itemMoveInfo.Reason == PropChangeType.Del then
        GuideConfig.EGuideEvent.evtLootingItemMove:Invoke(itemMainType, itemId)
        GuideConfig.EGuideEvent.evtLootingItemDiscard:Invoke(itemMainType, itemId)
    elseif itemMoveInfo.Reason == PropChangeType.Move then
        -- log("_OnLootingItemMove, move")

        GuideConfig.EGuideEvent.evtLootingItemMove:Invoke(itemMainType, itemId)

        local newItemSlot = itemMoveInfo.NewLoc.ItemSlot
        local oldItemSlot = itemMoveInfo.OldLoc.ItemSlot
        local newLocSlotGroup = newItemSlot:GetSlotGroup()
        local oldLocSlotGroup = oldItemSlot:GetSlotGroup()

        if newLocSlotGroup == ESlotGroup.Player then
            if oldLocSlotGroup == ESlotGroup.DeadBody  or oldLocSlotGroup == ESlotGroup.Nearby then
                local readyStageList = {}
                -- this first to cut branch?
                if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solSafeBox) then
                    local bCanStart = GuideLogic_CheckCondition.CanStartSafeBoxGuide(itemMoveInfo)
                    if bCanStart then
                        -- store this gid for latter guide widget register and click
                        Module.Guide.Field.SafeBoxGuideData:Set({
                            gid = itemMoveInfo.item.gid,
                            slotType = itemMoveInfo.NewLoc.ItemSlot.SlotType
                        })
                        table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solSafeBox])
                    end
                end
                GuideLogic.TryStartGuideStage(readyStageList)
            end

            if oldLocSlotGroup == ESlotGroup.DeadBody then
                GuideConfig.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.ItemMove2PlayerFromDeadBody, itemMainType, itemId)
            elseif oldLocSlotGroup == ESlotGroup.Player then
                if newItemSlot.SlotType == ESlotType.SafeBoxContainer and oldItemSlot.SlotType ~= ESlotType.SafeBoxContainer then
                    GuideConfig.EGuideEvent.evtOnGuideMsg:Invoke("msgItemMove2SafeBoxFromSelfContainer", itemMoveInfo.item.id, itemMainType)
                end
            end
        end
    end
end


function GuideEvtLogic._OnSeamlessSequencePreSpawn()
    log("_OnSeamlessSequencePreSpawn")
    GuideConfig.EGuideEvent.evtOnSeamlessSequencePreSpawn:Invoke()
end

function GuideEvtLogic._OnTopBarFinished()
    log("_OnTopBarFinished")
    GuideConfig.EGuideEvent.evtTopBarFinished:Invoke()
end

function GuideEvtLogic._OnMPTopBarFinished()
    log("_OnMPTopBarFinished")
    GuideConfig.EGuideEvent.evtTopBarFinished:Invoke()
end

function GuideEvtLogic._OnTopBarSafehouseAfterInit()
    -- log("_OnTopBarSafehouseAfterInit")
    -- if not GuideLogic.IsWarehouseOpened() then
    --     Module.Guide:OpenGuideHDWeakClickUI(148)
    -- end
    -- if not GuideLogic.IsDepartmentOpened() then
    --     Module.Guide:OpenGuideHDWeakClickUI(149)
    -- end
end

function GuideEvtLogic._OnTopBarSafehouseBeforeUnInit()
    log("_OnTopBarSafehouseBeforeUnInit")
    Module.Guide:CloseGuideHDWeakClickUI(148)
    Module.Guide:CloseGuideHDWeakClickUI(149)
end

function GuideEvtLogic._OnTopBarMpBeforeUnInit()
    log("_OnTopBarMpBeforeUnInit")
    Module.Guide:CloseGuideHDWeakClickUI(181)
end

function GuideEvtLogic._OnTopBarSafehouseClicked(clickIdx)
    -- log("_OnTopBarSafehouseClicked")
    -- if clickIdx == 3 then
    --     Module.Guide:CloseGuideHDWeakClickUI(148)
    -- elseif clickIdx == 5 then
    --     Module.Guide:CloseGuideHDWeakClickUI(149)
    -- end
end

function GuideEvtLogic._OnGameCutDownShow()
    log("_OnGameCutDownShow")
    GuideEvtLogic._OnGuideRemindEscapeSimple()
end

function GuideEvtLogic._OnBatchSellWindowShow()
    log("_OnBatchSellWindowShow")
    GuideConfig.EGuideEvent.evtOnBatchSellWindowShow:Invoke()
end

function GuideEvtLogic._OnTrySale()
    log("_OnTrySale")
    GuideConfig.EGuideEvent.evtTrySale:Invoke()
end

function GuideEvtLogic._OnWarehouseEquipSlotViewOnDrop()
    log("_OnWarehouseEquipSlotViewOnDrop")
    GuideConfig.EGuideEvent.evtOnWarehouseEquipSlotViewOnDrop:Invoke()
end

function GuideEvtLogic._OnBackBtnClicked()
    log("_OnBackBtnClicked")
    GuideConfig.EGuideEvent.evtBackBtnClicked:Invoke()
end

function GuideEvtLogic._OnSelectedDepartmentIdChanged(idx)
    log("_OnSelectedDepartmentIdChanged", idx)
    if idx == 2 then
        GuideConfig.EGuideEvent.evtGuideShopChooseOpenFinish:Invoke()
    elseif idx == 1 then
        GuideConfig.EGuideEvent.evtGuideQuestChapterLoadFinish:Invoke()
        if IsHD() then
        else
            local readyStageList = {}
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.firstOpenQuest) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.firstOpenQuest])
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        end
    end
end

function GuideEvtLogic._OnMpExpUpShow()
    log("_OnMpExpUpShow")
    local readyStageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpExpUp)
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnMpWeaponExpUpShow()
    log("_OnMpWeaponExpUpShow")
end

function GuideEvtLogic._OnMpPresetPanelShow()
    log("_OnMpPresetPanelShow")
    GuideConfig.EGuideEvent.evtOnMpPresetPanelShow:Invoke()

    if IsHD() then
        log("[HD] GuideEvtLogic._OnMpPresetPanelShow ->> retrigger the mpAssembly guide stage")
        local readyStageList = {}
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpAssembly)
        GuideLogic.TryStartGuideStage(readyStageList)
    end

end

function GuideEvtLogic._OnMpPresetPanelHide()
    log("_OnMpPresetPanelHide")
    GuideConfig.EGuideEvent.evtOnMpPresetPanelHide:Invoke()
end


function GuideEvtLogic._OnAssemblyMainPanelRentalBtnClicked()
    log("_OnAssemblyMainPanelRentalBtnClicked")
    GuideConfig.EGuideEvent.evtAssemblyMainPanelRentalBtnClicked:Invoke()
end


function GuideEvtLogic._OnAssemblySelectionMPMainShow(slotType)
    log("_OnAssemblySelectionMPMainShow", slotType)
    if IsHD() then
        if slotType == ESlotType.MP_MainWeapon or slotType == ESlotType.MP_SecondaryWeapon then
            GuideConfig.EGuideEvent.evtOnAssemblySelectionMPMainShow:Invoke()
        end
        if slotType == ESlotType.MP_ArmedForceTDMProp then
            GuideConfig.EGuideEvent.evtOnAssemblyMPMainShowArmedforcedItem:Invoke()
        end
    else
        GuideConfig.EGuideEvent.evtOnAssemblySelectionMPMainShow:Invoke()
    end
end

function GuideEvtLogic._OnAssemblySelectionMPMainHide()
    log("_OnAssemblySelectionMPMainHide")
    GuideConfig.EGuideEvent.evtOnAssemblySelectionMPMainHide:Invoke()
end

function GuideEvtLogic._OnAssemblySelectionMainShow()
    GuideConfig.EGuideEvent.evtOnAssemblySelectionMainShow:Invoke()
end

function GuideEvtLogic._OnPostAssemblySelectionRefreshEquipmentBtnState()
    log("_OnPostAssemblySelectionRefreshBtnState")
    GuideConfig.EGuideEvent.evtOnPostAssemblySelectionRefreshEquipmentBtnState:Invoke()
end

function GuideEvtLogic._OnAssemblySelectionMainSelectFirstItemDone()
    log("_OnSelectFirstItemDone")
    GuideConfig.EGuideEvent.evtOnAssemblySelectionMainSelectFirstItemDone:Invoke()
end


function GuideEvtLogic._OnAssemblyRentalMainPanelOnShowBegin()
    log("_OnAssemblyRentalMainPanleOnShowBegin")
    GuideConfig.EGuideEvent.evtAssemblyRentalMainPanelOnShowBegin:Invoke()

    local readyStageList = {}
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solCustomVoucher) then
        if GuideLogic_CheckCondition.CanStartSolCustomVoucherGuide() then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solCustomVoucher])
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end
function GuideEvtLogic._OnAssemblyRentalMainPanelOnHideBegin()
    log("_OnAssemblyRentalMainPanleOnHideBegin")
    GuideConfig.EGuideEvent.evtAssemblyRentalMainPanelOnHideBegin:Invoke()
end

function GuideEvtLogic._OnWeaponUpgradeBtnClicked()
    log("_OnWeaponUpgradeBtnClicked")
    GuideConfig.EGuideEvent.evtOnWeaponUpgradeBtnClicked:Invoke()
end

function GuideEvtLogic._OnWeaponUpgradeUIShow(bMaxLevel)
    log("_OnWeaponUpgradeUIShow", bMaxLevel)
    if bMaxLevel then
        GuideConfig.EGuideEvent.evtOnWeaponUpgradeUIShowLevelMax:Invoke()
    else
        GuideConfig.EGuideEvent.evtOnWeaponUpgradeUIShow:Invoke()
    end
end

function GuideEvtLogic._OnWeaponUpgradeUIHide()
    log("_OnWeaponUpgradeUIHide")
    GuideConfig.EGuideEvent.evtOnWeaponUpgradeUIHide:Invoke()
end

function GuideEvtLogic._OnWeaponUpgradePanelBtnClicked()
    log("_OnWeaponUpgradePanelBtnClicked")
    GuideConfig.EGuideEvent.evtOnWeaponUpgradePanelBtnClicked:Invoke()
end

function GuideEvtLogic._OnWeaponUpgradeSuccessShow()
    log("_OnWeaponUpgradeSuccessShow")
    GuideConfig.EGuideEvent.evtOnWeaponUpgradeSuccessShow:Invoke()
end

function GuideEvtLogic._OnWeaponUpgradeSuccessHide()
    log("_OnWeaponUpgradeSuccessHide")
    GuideConfig.EGuideEvent.evtOnWeaponUpgradeSuccessHide:Invoke()
end

function GuideEvtLogic._OnGunsmithUpgradeItemOperated()
    log("_OnGunsmithUpgradeItemOperated")
    GuideConfig.EGuideEvent.evtGunsmithUpgradeItemOperated:Invoke()
end


function GuideEvtLogic._OnUseItemBtnClicked()
    log("_OnUseItemBtnClicked")
    GuideConfig.EGuideEvent.evtOnUseItemBtnClicked:Invoke()
end

function GuideEvtLogic._OnWeaponUpgradeSuccessPanelBtnClicked()
    log("_OnWeaponUpgradeSuccessPanelBtnClicked")
    GuideConfig.EGuideEvent.evtOnWeaponUpgradeSuccessPanelSelectAllClicked:Invoke()
end

function GuideEvtLogic._OnAssemblySelectionOutAnimBegin()
    log("_OnAssemblySelectionOutAnimBegin")
    GuideConfig.EGuideEvent.evtOnAssemblySelectionOutAnimBegin:Invoke()
end

function GuideEvtLogic._OnAssemblyWeaponClicked(slotType)
    log("_OnAssemblyWeaponClicked" ,slotType)
    if slotType == ESlotType.MP_MainWeapon then
        GuideConfig.EGuideEvent.evtOnAssemblyMainWeaponClicked:Invoke()
    end

    GuideConfig.EGuideEvent.evtOnAssemblyWeaponClicked:Invoke()

end

function GuideEvtLogic._OnAssemblySelectionRepairBtnClicked()
    GuideConfig.EGuideEvent.evtOnAssemblySelectionRepairBtnClicked:Invoke()
end


function GuideEvtLogic._OnBeginMpSettlementPop()
    log("_OnBeginMpSettlementPop")
    if IsHD() then
    else
        -- local readyStageList = {}
        -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpExpUp) then
        --     table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpExpUp])
        -- end
        -- GuideLogic.TryStartGuideStage(readyStageList)
    end
end

function GuideEvtLogic._OnEndMpSettlementPop()
    log("_OnEndMpSettlementPop")
    GuideConfig.EGuideEvent.evtEndMpSettlementPop:Invoke()

    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow == EGameFlowStageType.Lobby then
        -- Reset global variable
        Module.Guide.Field.bMpSettlementPopEnd = true
        GuideEvtLogic._TryStartMpMainGuide("OnEndMpSettlementPop")
    end
end


function GuideEvtLogic._OnBeginSolSettlementPop()
    log("_OnBeginSolSettlementPop")
    Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.SolSettlementPop)
end

function GuideEvtLogic._OnEndSolSettlementPop()
    log("_OnEndSolSettlementPop")
    Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.SolSettlementPop)

    local gameflow = Facade.GameFlowManager:GetCurrentGameFlow()
    if gameflow == EGameFlowStageType.SafeHouse then
        Module.Guide.Field.bSolSettlementPopEnd = true
        GuideEvtLogic.OnGuideOpenSafehouse("OnEndSolSettlementPop")
    end

end


function GuideEvtLogic._OnEndWeaponUpgradePop()
    log("_OnEndWeaponUpgradePop")
    GuideConfig.EGuideEvent.evtEndWeaponUpgradePop:Invoke()
end

function GuideEvtLogic._OnLootingCarryItemFail()
    log("_OnLootingCarryItemFail")
    GuideConfig.EGuideEvent.evtCarryItemFail:Invoke()
end

function GuideEvtLogic._OnInventoryManagerRepData()

    local gain = Module.Looting:GetPlayerGainValue()
    local bLootUIOpen = Module.Guide.Field.lootingInGameOpenState
    log("_OnInvMgrRepData cur gain: ", gain , " bLootUIOpen: ", bLootUIOpen)

    local readyStageList = {}

    if gain > 20 * 10000 then
        if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solEscapePath) then
            table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solEscapePath])
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end


function GuideEvtLogic._OnSDKCommonTipOpen()
    log("_OnSDKCommonTipOpen")
    Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.SDKCommonTip)
end

function GuideEvtLogic._OnSDKCommonTipClose()
    log("_OnSDKCommonTipClose")
    Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.SDKCommonTip)
end


-- 尝试启动当前mp可以进行的主流程引导
function GuideEvtLogic._TryStartMpMainGuide(...)
    log("_TryStartMpMainGuide exInfo:", ...)

    -- 赛季重置时，不做引导
    if Module.Tournament:GetIsShowingSeasonRestartWindow() then
        warning("_TryStartMpMainGuide: Tournament season restart window is showing, skip")
        return
    end

    local readyStageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpArmedForceItem)
    if IsHD() then
        if Module.Guide.Field.bMpSettlementPopEnd then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpAssembly)
            -- why after pop
            if GuideLogic.IsMpRankUnlock() then
                GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpRank)
            end
        else
            log("mpRank: Settlement module processing, skip")
        end
    else
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpInfoIntro)

        if GuideLogic.IsMpRankUnlock() then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpRank)
        end
        if Module.Guide.Field.bMpSettlementPopEnd then
            GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mpAssembly)
        else
            log("mpRank: Settlement module processing, skip")
        end
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end


function GuideEvtLogic._OnTournamentMainUIShow()
    log("_OnTournamentMainUIShow")
    GuideConfig.EGuideEvent.evtTournamentMainUIShow:Invoke()
end

function GuideEvtLogic._OnOpenRankRewardPanelBtnClicked()
    log("_OnOpenRankRewardPanelBtnClicked")
    GuideConfig.EGuideEvent.evtOpenRankRewardPanelBtnClicked:Invoke()
end

function GuideEvtLogic._OnMpRankSettlementShow()
    local bRankShield = Server.SettlementServer:IsMpSettlementRankShield()
    log("_OnMpRankSettlementShow", bRankShield)
    if bRankShield then
        if DFHD_LUA == 1 then
            local readyStageList = {}
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpRankScoreSettlement) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpRankScoreSettlement])
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        else
            local readyStageList = {}
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.mpRankScoreSettlement) then
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.mpRankScoreSettlement])
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        end
    end
end

function GuideEvtLogic._OnMpRankSettlementInAnimFin()
    log("_OnMpRankSettlementInAnimFin")
    GuideConfig.EGuideEvent.evtMpRankSettlementInAnimFin:Invoke()
end

function GuideEvtLogic._OnMpRankSettlementClose()
    log("_OnMpRankSettlementClose")
    GuideConfig.EGuideEvent.evtMpRankSettlementClose:Invoke()
end

function GuideEvtLogic._OnSolRankSettlementShow()
    local bRankShield = Server.SettlementServer:IsSolSettlementRankShield()
    log("_OnSolRankSettlementShow", bRankShield)
    if bRankShield then
        if DFHD_LUA == 1 then
            local readyStageList = {}
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solFirstRankProtection) then
                Module.Guide.Field:ReducePauseGuideUINum("SolSettlementPop")
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solFirstRankProtection])
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        else
            local readyStageList = {}
            if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solFirstRankProtection) then
                Module.Guide.Field:ReducePauseGuideUINum("SolSettlementPop")
                table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solFirstRankProtection])
            end
            GuideLogic.TryStartGuideStage(readyStageList)
        end
    end
end

function GuideEvtLogic._OnSolRankSettlementClose()
    log("_OnSolRankSettlementClose")
    Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.SolSettlementPop)
    GuideConfig.EGuideEvent.evtSolRankSettlementClose:Invoke()
end



function GuideEvtLogic._OnMarketMainPanelTabChanged(curIdx, lastIdx)
    log("_OnMarketMainPanelTabChanged", curIdx, lastIdx)
    local readyStageList = {}
    if curIdx == EMarketSubPageType.MysticalSkin then
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mysticalSkin) 
    elseif curIdx == EMarketSubPageType.MysticalPendant then
        GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.mysticalHangingMarket) 
    end
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnMarketSkinPageOnShowBegin()
    log("_OnMarketSkinPageOnShowBegin")
end

function GuideEvtLogic._OnMarketSkinPageOnHideBegin()
    GuideConfig.EGuideEvent.evtMarketSkinHideBegin:Invoke()
end

function GuideEvtLogic._OnQuestMainPanelOnHideBegin()
    log("_OnQuestMainPanelOnHideBegin")
    GuideConfig.EGuideEvent.evtQuestMainPanelHideBegin:Invoke()
end


function GuideEvtLogic._OnSOLUsedHeroIdChanged(heroId)
    log("OnUsedHeroIdChanged", heroId)
    if DFHD_LUA == 1 then
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
            if curSubStage == ESubStage.HallMatch then
                warning("OnGuideOpenSafehouse cur stage is match")
                return
            end

            if heroId == GuideConfig.HeroSkillGuideHeroId then
                local readyStageList = {}
                if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solHeroFirstSelectShepHerdb) then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solHeroFirstSelectShepHerdb])
                end
                GuideLogic.TryStartGuideStage(readyStageList)
            end
        end
    end
end

function GuideEvtLogic._OnHeroSkillVideoPanelShow()
    log("OnHeroSkillVideoPanelShow")
    GuideConfig.EGuideEvent.evtOnHeroSkillVideoPanelShow:Invoke()
end

function GuideEvtLogic._OnHeroListTopPanelOnShowBegin()
    log("OnHeroListTopPanelOnShowBegin")
    GuideConfig.EGuideEvent.evtHeroListTopPanelOnShowBegin:Invoke()
end

function GuideEvtLogic._OnHeroItemClicked(heroId)
    log("OnHeroItemClicked", heroId)
    GuideConfig.EGuideEvent.evtOnHeroItemClicked:Invoke()
    if DFHD_LUA == 1 then
        if GuideConfig.HeroSkillGuideHeroId ~= heroId then
            GuideConfig.EGuideEvent.evtHeroChangeToNonGuideHeroItem:Invoke()
        else
            -- local readyStageList = {}
            -- if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solHeroFirstSelectShepHerdb) then
            --     table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solHeroFirstSelectShepHerdb])
            -- end
            -- GuideLogic.TryStartGuideStage(readyStageList)
        end
    else
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            if heroId == GuideConfig.HeroSkillGuideHeroId then
                local readyStageList = {}
                if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solHeroFirstSelectShepHerdb) then
                    table.insert(readyStageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solHeroFirstSelectShepHerdb])
                end
                GuideLogic.TryStartGuideStage(readyStageList)
            end
        end
    end
end

function GuideEvtLogic._OnHeroViewSkillBtnClicked()
    log("_OnHeroViewSkillBtnClicked")
    GuideConfig.EGuideEvent.evtOnHeroViewSkillBtnClicked:Invoke()
end

function GuideEvtLogic._OnHeroTopSkillPanelShow()
    log("_OnHeroTopSkillPanelShow")
    GuideConfig.EGuideEvent.evtOnHeroTopSkillPanelShow:Invoke()
end

function GuideEvtLogic._OnHeroTopSkillPanelClose()
    log("_OnHeroTopSkillPanelClose")
    GuideConfig.EGuideEvent.evtOnHeroTopSkillPanelClose:Invoke()
end

function GuideEvtLogic._OnHeroShowSkillBtnClicked()
    log("_OnHeroShowSkillBtnClicked")
    GuideConfig.EGuideEvent.evtOnHeroShowSkillBtnClicked:Invoke()
end

function GuideEvtLogic._OnRouletteMain_PC_AnimShow()
    log("_OnRouletteMain_PC_AnimShow")
    GuideConfig.EGuideEvent.evtOnRouletteMain_PC_AnimShow:Invoke()
end

function GuideEvtLogic._OnRankMainPanelShow()
    log("_OnRankMainPanelShow")
    GuideConfig.EGuideEvent.evtRankMainPanelShow:Invoke()
end

function GuideEvtLogic._OnRankMainPanelClose()
    log("_OnRankMainPanelClose")
    GuideConfig.EGuideEvent.evtRankMainPanelClose:Invoke()
end

function GuideEvtLogic._OnRankRewardBtnClicked()
    log("_OnRankRewardBtnClicked")
    GuideConfig.EGuideEvent.evtRankRewardBtnClicked:Invoke()
end

function GuideEvtLogic._OnBlackSiteMainUIOpen()
    local curStackUIId =  Facade.UIManager:GetCurrentStackUIId()
    local curBlackSiteTabId  = Module.BlackSite:GetCurTabIdx()
    log("_OnBlackSiteMainUIOpen", curStackUIId, curBlackSiteTabId)

    GuideConfig.EGuideEvent.evtBlackSiteMainUIOpen:Invoke()

    if not IsHD() then return end
    if  curStackUIId ~= UIName2ID.BlackSiteMainPanel then return end

    local stageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(stageList, GuideConfig.EGuideStage.solBlackSiteUpgrade_WareHouse)
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteUpgrade_AmorPlatform) then
        if GuideLogic_CheckCondition.CanStartSolBlackSiteUpgradeAmorPlatformGuide() then
            table.insert(stageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteUpgrade_AmorPlatform])
        end
    end
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(stageList, GuideConfig.EGuideStage.solBlackSiteProduce_Bullet)
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteProduce_Amor) then
        if GuideLogic_CheckCondition.CanStartSolBlackSiteProduceAmorGuide() then
            table.insert(stageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteProduce_Amor])
        end
    end
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteUpgrade_CollectionRoom) then
        if GuideLogic_CheckCondition.CanStartSolBlackSiteUpgradeCollectionRoomGuide() then
            table.insert(stageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteUpgrade_CollectionRoom])
        end
    end
    if GuideLogic.CheckMeetGuideStageStartCondition(GuideConfig.EGuideStage.solBlackSiteGameplay_CollectionRoom) then
        if GuideLogic_CheckCondition.CanStartSolBlackSiteGameplayCollectionRoomGuide() then
            table.insert(stageList, GuideConfig.TableGuideStageConfig[GuideConfig.EGuideStage.solBlackSiteGameplay_CollectionRoom])
        end
    end
    GuideLogic.TryStartGuideStage(stageList)
end

function GuideEvtLogic._OnBlackSiteMainUIClose()
    log("_OnBlackSiteMainUIClose")
    GuideConfig.EGuideEvent.evtBlackSiteMainUIClose:Invoke()
end

function GuideEvtLogic._OnBlackSiteUpgradeUIOpen()
    log("_OnBlackSiteUpgradeUIOpen")
    GuideConfig.EGuideEvent.evtBlackSiteUpgradeUIOpen:Invoke()

    if GuideEvtLogic._cbOnBlackSiteUpgradeUIOpen then
        GuideEvtLogic._cbOnBlackSiteUpgradeUIOpen()
    end
end

function GuideEvtLogic._OnBlackSiteUpgradeHideBegin()
    log("_OnBlackSiteUpgradeHideBegin")
    GuideConfig.EGuideEvent.evtBlackSiteUpgradeUIHideBegin:Invoke()
end

---@param deviceId EBlackSiteDeviceName2Id
function GuideEvtLogic._OnBlackSiteUpgradeConditionsUIOnShowBegin(deviceId)
    log("_OnBlackSiteUpgradeConditionsUIOnShowBegin", deviceId)
    GuideConfig.EGuideEvent.evtBlackSiteUpgradeConditionsUIOnShowBegin:Invoke(deviceId)
end

function GuideEvtLogic._OnBlackSiteProduceUIOpen()
    log("_OnBlackSiteProduceUIOpen")
    GuideConfig.EGuideEvent.evtBlackSiteProduceUIOpen:Invoke()
    if GuideEvtLogic._cbOnBlackSiteProduceUIOpen then
        GuideEvtLogic._cbOnBlackSiteProduceUIOpen()
    end
end

function GuideEvtLogic._OnBlackSiteProduceUIHideBegin()
    log("_OnBlackSiteProduceUIHideBegin")
    GuideConfig.EGuideEvent.evtBlackSiteProduceUIHideBegin:Invoke()
end


---@param deviceId  EBlackSiteDeviceName2Id
function GuideEvtLogic._OnBlackSiteUpgradeItemClicked(deviceId)
    log("_OnBlackSiteUpgradeItemClicked", deviceId)
    GuideConfig.EGuideEvent.evtBlackSiteUpgradeItemClicked:Invoke(deviceId)
end

function GuideEvtLogic._OnBlackSiteConstructUIOpen()
    log("_OnBlackSiteConstructUIOpen")
    GuideConfig.EGuideEvent.evtBlackSiteConstructUIOpen:Invoke()
end



---@param deviceId  EBlackSiteDeviceName2Id
function  GuideEvtLogic._OnBlackSiteProduceOpenDevice(deviceId)
    log("_OnBlackSiteProduceOpenDevice", deviceId)
    -- EBlackSiteDeviceName2Id.Warehouse 
    -- EBlackSiteDeviceName2Id.Staging 工作台
    -- EBlackSiteDeviceName2Id.ArmorPlatform 

    GuideConfig.EGuideEvent.evtBlackSiteProduceOpenDevice:Invoke(deviceId)
end

function GuideEvtLogic._OnBlackSiteEntranceItemClicked(entranceType)
    log("_OnBlackSiteEntranceItemClicked", entranceType)
    if  entranceType == BlackSiteDefine.EBlackSiteEntranceType.Construct then
        GuideConfig.EGuideEvent.evtBlackSiteEntranceConstructClicked:Invoke()
    else
        GuideConfig.EGuideEvent.evtBlackSiteEntranceProduceClicked:Invoke()
    end
end

---@param deviceId EBlackSiteDeviceName2Id
function GuideEvtLogic._OnBlackSiteConditionsUpgradeBtnClicked(deviceId)
    log("_OnBlackSiteConditionsUpgradeBtnClicked", deviceId)
    GuideConfig.EGuideEvent.evtBlackSiteConditionsUpgradeBtnClicked:Invoke(deviceId)
end

function GuideEvtLogic._RealEnterSafeHouse()
    log("_RealEnterSafeHouse")
    GuideEvtLogic.OnGuideOpenSafehouse()
end

function GuideEvtLogic._OnInputTypeChanged(inputType)
    log("_OnInputTypeChanged", inputType)
    -- 目前只需要判断 to Gamepad
    if inputType == EGPInputType.Gamepad then
        if Module.Guide:IsGuiding() then
            local curGuideId = Module.Guide.Field:GetCurGuideId()
            if not curGuideId then
                return
            end
            local guideCfg = Module.Guide.Field:GetGuideCfg(curGuideId)
            if not guideCfg then
                return
            end
            local guideStageCfg = Module.Guide.Field:GetGuideStageCfg(guideCfg.GuideStageId)
            log("_OnInputTypeChanged, cur guide data:", curGuideId, guideCfg.GuideStageId, table.contains(guideStageCfg.DisableInputType, inputType))
            if table.contains(guideStageCfg.DisableInputType, inputType) then
                GuideLogic.DoForceEndGuideStage(guideCfg.GuideStageId)
            end
        end
    end
    local data = Module.Guide.Field:GetCurGuideData()
    if data then
        data:OnInputTypeChanged(inputType)
    end
end

function GuideEvtLogic._OnPlayerReturnDailyMatchPopupJumpToSolArmedForce()
    local bInTeam = Server.AccountServer:IsInTeam()
    log("_OnPlayerReturnDailyMatchPopupJumpToSolArmedForce", bInTeam)
    if bInTeam then
        return
    end

    Module.ArmedForce:MarkOutfitFlowFlag()
    -- 高优先级不被其他的打断即可
    local readyStageList = {}
    GuideLogic.TryInsertIfMeetGuideStageStartCondition(readyStageList, GuideConfig.EGuideStage.solReflowBeginMatchPulling)
    GuideLogic.TryStartGuideStage(readyStageList)
end

function GuideEvtLogic._OnRewardHeroStuffUnlockPanelMisc(infoStr)
    log("_OnRewardHeroStuffUnlockPanelMisc", infoStr)
    if infoStr == "OnOpen" then
        Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.RewardHeroStuffUnlockPanel)
    elseif infoStr == "OnClose" then
        Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.RewardHeroStuffUnlockPanel)
    end
end

function  GuideEvtLogic._OnRewardGestureGainPopMisc(infoStr)
    log("_OnRewardGestureGainPopMisc", infoStr)
    if infoStr == "OnOpen" then
        Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.RewardGestureGainPop)
    elseif infoStr == "OnClose" then
        Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.RewardGestureGainPop)
    end
end

function GuideEvtLogic._OnRewardStuffGainPopMisc(infoStr)
    log("_OnRewardStuffGainPopMisc", infoStr)
    if infoStr == "OnOpen" then
        Module.Guide.Field:AddPauseGuideUINum(EPauseGuideUIKey.RewardStuffGainPop)
    elseif infoStr == "OnClose" then
        Module.Guide.Field:ReducePauseGuideUINum(EPauseGuideUIKey.RewardStuffGainPop)
    end
end

local _guideMsgDispatcher = GuideLogic.switch()
    -- .default(function() end)
    -- wrapper all  callbacks or defined them before _guideMsgDispatcher
    .case(EGuideMsgSig.PlayerReturnDailyMatchPopupJumpToSolArmedForce, GuideEvtLogic._OnPlayerReturnDailyMatchPopupJumpToSolArmedForce)
    .case(EGuideMsgSig.PlayerReturnDailyMatchPopupJumpToMpHall, GuideEvtLogic._OnPlayerReturnDailyMatchPopupJumpToMpHall)
    .case(EGuideMsgSig.CollectionListPanelOnShowBegin, GuideEvtLogic._OnCollectionListPanelOnShowBegin)
    .case(EGuideMsgSig.EvacuationResultInfoNextFlowClicked, GuideEvtLogic._OnEvacuationResultInfoNextFlowClicked)
    .case(EGuideMsgSig.DeathDamageInfoViewOnShow, GuideEvtLogic._OnDeathDamageInfoViewOnOpen)
    .case(EGuideMsgSig.RewardHeroStuffUnlockPanel, GuideEvtLogic._OnRewardHeroStuffUnlockPanelMisc)
    .case(EGuideMsgSig.RewardGestureGainPop, GuideEvtLogic._OnRewardGestureGainPopMisc)
    .case(EGuideMsgSig.RewardStuffGainPop, GuideEvtLogic._OnRewardStuffGainPopMisc)


---@param sig EGuideMsgSig
function GuideEvtLogic.OnGuideMsg(sig,...)
    local args = {...}
    log("_OnGuideMsg ", sig, ...)
    _guideMsgDispatcher.process(sig, ...)
    GuideConfig.EGuideEvent.gdEvtOnGuideMsg:Invoke(sig, ...)
end


return GuideEvtLogic