----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local WarehouseEquipSlotView = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipSlotView"
local InvSlotView = require "DFM.Business.Module.InventoryModule.UI.Common.InvSlotView"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"

local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"

local LootingConfig = Module.Looting.Config

---@class WarehouseContainerPanel : LuaUIBaseView
local WarehouseContainerPanel = ui("WarehouseContainerPanel")

local function log(...)
    loginfo("[WarehouseContainerPanel]", ...)
end

---@param itemView IVWarehouseTemplate
local function fPostItemRefresh(itemView)
    -- 装备槽隐藏左上名字
    itemView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.TopLeftIconText, false)
end

function WarehouseContainerPanel:Ctor()
    self.bindContainerSlot = nil
    self.bindEquipSlot = nil
    self.hoverDescription = ""
    self.hoverHandle = nil
    self.equipmentType = 0

    self._wtEquipSlotView = self:Wnd("wtEquipSlotView", WarehouseEquipSlotView)
    self._wtEquipSlotView:BindPostRefreshFunc(fPostItemRefresh)
    self._wtContainerSlotView = self:Wnd("wtContainerSlotView", InvSlotView)
    self._wtContainerSlotView:SetSlotMaxPreviewSize(self.SlotMaxPreviewSize, 0)
    self._wtCapacityText = self:Wnd("wtCapacityText", UITextBlock)
    self._wtColonText = self:Wnd("wtColonText", UITextBlock)

    self._wtHoverBtn = self:Wnd("wtHoverBtn", DFCheckBoxOnly)
    self._wtHoverBtn:SetPreciseTap()
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._OnShowDescriptionTip, self._OnHideDescriptionTip)
end

function WarehouseContainerPanel:OnShow()
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtSlotSpaceChange, self._OnSlotSpaceChange, self)
    self:AddLuaEvent(Facade.UIManager.Events.evtPopUIChanged, self._OnPopUIChanged, self)

    self:RefreshView(true)

    self:_CheckContainerSlotVisibility()
    self:_CheckTipsBtnVisibility()
    self._wtHoverBtn:SetIsChecked(false)
end

function WarehouseContainerPanel:OnHide()
    self:RemoveAllLuaEvent()
    self._wtHoverBtn:SetIsChecked(false)
    self:_OnHideDescriptionTip()
end

function WarehouseContainerPanel:InitContainerSlot(containerSlotType, slotGroup)
    if slotGroup == ESlotGroup.Player then
        if Module.Looting:IsCreatingUIForCollectionRoom() then
            slotGroup = ESlotGroup.CollectionRoom
        end
    end
    self.bindContainerSlot = Server.InventoryServer:GetSlot(containerSlotType, slotGroup)
    self._wtContainerSlotView:InitServerSlot(containerSlotType, slotGroup)

    if slotGroup == ESlotGroup.DeadBody and containerSlotType ~= ESlotType.ArchiveContainer then
        self._wtHoverBtn:Collapsed()
    else
        self._wtHoverBtn:SelfHitTestInvisible()
    end

    local equipmentType = 0
    if containerSlotType == ESlotType.Pocket or containerSlotType == ESlotType.ArchiveContainer then
        equipmentType = LootingConfig.WarehouseContainerPanelTypeIndex.NoEquipSlot
    else
        if self.bindContainerSlot == Server.LootingServer.dropContainerSlot then
            equipmentType = LootingConfig.WarehouseContainerPanelTypeIndex.DropContainer
        elseif slotGroup == ESlotGroup.Nearby then
            equipmentType = LootingConfig.WarehouseContainerPanelTypeIndex.PickupsOrBox
        else
            if slotGroup == ESlotGroup.Rental_1 or slotGroup == ESlotGroup.Rental_2 or slotGroup == ESlotGroup.Rental_3 then
                equipmentType = LootingConfig.WarehouseContainerPanelTypeIndex.MainPickupsOrBox
            else
                equipmentType = LootingConfig.WarehouseContainerPanelTypeIndex.WithEquipSlot
            end
        end
    end
    if self.equipmentType ~= equipmentType then
        self:SetType(equipmentType)
        self.equipmentType = equipmentType
    end

    local equipSlotType = self.bindContainerSlot:GetContainerType()
    if equipSlotType then
        self.bindEquipSlot = Server.InventoryServer:GetSlot(equipSlotType, slotGroup)
        self._wtEquipSlotView:InitServerSlot(equipSlotType, slotGroup)
    end

    self.hoverDescription = InventoryConfig.MapSlotType2HoverDescription[containerSlotType]

    self:_CheckContainerSlotVisibility()
    self:RefreshView()
end

function WarehouseContainerPanel:RefreshView(bScrollToTop)
    -- equipslotview做适配
    local equipSlotViewPanel = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtEquipSlotView)
    local coefficient = 1.4
    local bRentalPanel = false
    if self._wtEquipSlotView:CDDL_GetSlot() then
        local slotGroup = self._wtEquipSlotView:CDDL_GetSlot():GetSlotGroup()
        if slotGroup == ESlotGroup.Rental_1 or slotGroup == ESlotGroup.Rental_2 or slotGroup == ESlotGroup.Rental_3 then
            coefficient = 1
            bRentalPanel = true
        end
    end

    equipSlotViewPanel:SetSize(FVector2D(coefficient * ItemConfig.DefaultItemViewSize, coefficient * ItemConfig.DefaultItemViewSize))
    self._wtEquipSlotView:BP_SetLineColor(bRentalPanel)
    self._wtEquipSlotView:RefreshView()
    self._wtContainerSlotView:RefreshView(bScrollToTop)
    self:_RefreshCapacity()
end

function WarehouseContainerPanel:FindItemViewByGID(gid)
    local item = self._wtEquipSlotView:GetEquipItem()
    if item and item.gid == gid then
        return self._wtEquipSlotView:GetMainItemView()
    end

    return self._wtContainerSlotView:GetViewByGID(gid)
end

-- 公有刷新容量方法
function WarehouseContainerPanel:RefreshCapacity()
    self:_RefreshCapacity()
end

function WarehouseContainerPanel:_RefreshCapacity()
    if self.bindContainerSlot then
        local capacity = self.bindContainerSlot:GetUsedCapacity()
        local totalCapacity = self.bindContainerSlot:GetTotalCapacity()

        if totalCapacity > 0 then
            self._wtCapacityText:SetText(string.format("%d/%d", capacity, totalCapacity))
        end
    end
end

function WarehouseContainerPanel:_OnItemMove(moveItemInfo)
    self:_RefreshCapacity()
end

function WarehouseContainerPanel:_OnSlotSpaceChange(targetSlot)
    if self.bindContainerSlot == targetSlot then
        self:_CheckContainerSlotVisibility()
    end
end

function WarehouseContainerPanel:_CheckContainerSlotVisibility()
    if self.bindContainerSlot then
        local bShowContainerSlotView = self.bindContainerSlot:GetTotalCapacity() > 0
        if bShowContainerSlotView then
            -- self._wtContainerSlotView:Show(false, true)
            self._wtContainerSlotView:Visible()
            if not Module.Looting.Config.SlotsHideCapacityText[self.bindContainerSlot.SlotType] then
                self._wtColonText:SelfHitTestInvisible()
                self._wtCapacityText:SelfHitTestInvisible()
            else
                self._wtColonText:Collapsed()
                self._wtCapacityText:Collapsed()
            end
        else
            -- self._wtContainerSlotView:Hide(true, true)
            self._wtContainerSlotView:Collapsed()
            self._wtColonText:Collapsed()
            self._wtCapacityText:Collapsed()
        end
    end
end

function WarehouseContainerPanel:_CheckTipsBtnVisibility()
    if self.bindContainerSlot then
        local slotGroup = self.bindContainerSlot:GetSlotGroup()
        local slotType = self.bindContainerSlot.SlotType
        if slotGroup == ESlotGroup.DeadBody and slotType ~= ESlotType.ArchiveContainer then
            self._wtHoverBtn:Collapsed()
        else
            self._wtHoverBtn:SelfHitTestInvisible()
        end
    end
end

function WarehouseContainerPanel:_OnShowDescriptionTip()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle)
        self.hoverHandle = nil
    end

    self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
        id = UIName2ID.Assembled_CommonMessageTips_V2,
        data = {textContent = self.hoverDescription}
    }},self._wtDFTipsAnchor)
    self._wtHoverBtn:SetIsChecked(true)
end

function WarehouseContainerPanel:_OnHideDescriptionTip()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle,self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
    self._wtHoverBtn:SetIsChecked(false)
end

function WarehouseContainerPanel:_OnCheckStateChanged(bChecked)
    if bChecked then
        --self.messageHandle = Module.CommonTips:ShowCommonMessageView(self._wtHoverBtn, self.hoverDescription, self._wtHoverBtn)
        self:_OnShowDescriptionTip()
    else
        -- if self.messageHandle then
        --     Facade.UIManager:CloseUIByHandle(self.messageHandle)
        --     self.messageHandle = nil
        -- end
        self:_OnHideDescriptionTip()
    end
end

function WarehouseContainerPanel:GetEquipSlotView()
    return self._wtEquipSlotView
end

function WarehouseContainerPanel:GetContainerSlotView()
    return self._wtContainerSlotView
end

function WarehouseContainerPanel:GetCapacityText()
    return self._wtCapacityText
end

function WarehouseContainerPanel:PrecreateItemViews(precreateNum)
    self._wtContainerSlotView:PrecreateItemViews(precreateNum)
end

function WarehouseContainerPanel:SetHoverBtnVisible(bVisible)
    if bVisible then
        self._wtHoverBtn:SelfHitTestInvisible()
    else
        self._wtHoverBtn:Collapsed()
    end
end

function WarehouseContainerPanel:_OnPopUIChanged()
    self:_OnHideDescriptionTip()
end

-- 战利品显示界面隐藏安全箱时间(Long2暂时这样处理，只有可以优化成通用接口)
function WarehouseContainerPanel:HideBottomRightComp()
    local equipSlotView = self._wtEquipSlotView:GetMainItemView()
    if equipSlotView then
        local function hideBRComp()
            equipSlotView:EnableComponent(Module.CommonWidget.Config.EIVWarehouseTempComponent.BottomRightIconText, false)
        end
        equipSlotView:BindPostRefreshFunc(hideBRComp)
    end
end

function WarehouseContainerPanel:BindScrollBox(scrollBox)
    self._wtEquipSlotView:BindScrollBox(scrollBox)
    self._wtContainerSlotView:BindScrollBox(scrollBox)
end

return WarehouseContainerPanel