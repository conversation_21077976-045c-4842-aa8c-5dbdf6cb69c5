----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestDetailView : LuaUIBaseView
local QuestDetailView = ui("QuestDetailView")
local QuestLogic = require "DFM.Business.Module.QuestModule.QuestLogic"
local QuestDetailTitle = require "DFM.Business.Module.QuestModule.UI.SOL.QuestDetailTitle"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local EHorizontalAlignment = import("EHorizontalAlignment")
local EVerticalAlignment = import "EVerticalAlignment"
local UMapInfoSystem = import "MapInfoSystem"
local DFCommonProgressBarOnly = require "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonProgressBarOnly"
local EComp = Module.CommonWidget.Config.EIVWarehouseTempComponent
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local ECheckButtonState = import "ECheckButtonState"
local EUINavigation = import("EUINavigation")
-- END MODIFICATION

function QuestDetailView:Ctor()
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    self._wtPlayCgBtn = self:Wnd("wPlayCgBtn", UIButton)
    if self._wtPlayCgBtn then
        self._wtPlayCgBtn:Event("OnClicked", self._OnPlayCgBtnClicked, self)
    end


    self._wtQuestBulletinText = self:Wnd("wQuestBulletinText", UITextBlock)
    self._wtRewardScrollBox = self:Wnd("wRewardWrapBox", UIScrollBox)
    self._wtLockedShopItemScrollBox = self:Wnd("wLockedShopItemWrapBox", UIScrollBox)

    self._wtStateCanvas = self:Wnd("wStateCanvas", UIWidgetBase)
    self._wtStateText = self:Wnd("wStateText", UITextBlock)
    -- 行为按钮（领取任务/奖励/提交/重新开始）
    self._wtActionBtn = self:Wnd("wActionBtn", DFCommonButtonOnly)
    self._wtActionBtn:Event("OnClicked", self._OnClickActionBtn, self)
    self._wtActionBtn:Event("OnDeClicked", self._OnClickActionBtn, self)

    self._wtQuestDetailPanel = self:Wnd("WBP_TaskSystem_DetailPanel", UIWidgetBase)
    self._wtQuestDetailPanel._wtParentUI = self
    -- 战术地图
    self._wtQuestMapDecorativePanel = self:Wnd("wQuestMapDecorativePanel", UIWidgetBase)
    self._wtQuestMapRelatedInfoPanel = self:Wnd("wQuestMapRelatedInfoPanel", UIWidgetBase)
    self._wtQuestMapImg = self:Wnd("wQuestMapImg", UIImage) -- 战术地图
    self._wtMapCanvasPanel = self:Wnd("wMapCanvasPanel", UIWidgetBase)
    local canvasSlot = UE.WidgetLayoutLibrary.SlotAsCanvasSlot(self._wtMapCanvasPanel)
    self._mapSize = canvasSlot:GetSize()
    self._wtQuestMapCenterWidget = self:Wnd("wQuestMapCenterWidget", UIWidgetBase)
    self._wtMapDetailWidget = self:Wnd("wMapDetailWidget", UIWidgetBase)
    self._wtMapDetailTargetWidget = self._wtMapDetailWidget:Wnd("wMapDetailTargetWidget", UIWidgetBase) -- 任务目标定位
    self._wtMapDetailContentWidget = self._wtMapDetailWidget:Wnd("wMapDetailContentWidget", UIWidgetBase) -- 任务目标说明点位
    self._wtMapLocationInfoTxt = self:Wnd("wMapLocationInfoTxt", UITextBlock)
    self._wtMapTemperatureTxt = self:Wnd("wMapTemperatureTxt", UITextBlock)
    self._wtMapTimeInfoTxt = self:Wnd("wMapTimeInfoTxt", UITextBlock)

    -- 语音字幕
    self._wtVoiceMessageBtn = self:Wnd("wVoiceMessageButton", UIButton)
    self._wtVoiceMessageBtn:Event("OnClicked", self._OnVoiceMessageClicked, self)
    self._wtVoiceMessageBox = self:Wnd("wVoiceMessageBox", UIWidgetBase)
    self._wtSpeakerImage = self:Wnd("wSpeakerImage", UIImage)
    self._wtPanel_03 = self:Wnd("Panel_03", UIWidgetBase)

    self._questInfo = nil
    self._npcInfo = nil
    self._ownerQuestLineInfo = nil
    self._childQuestObjectives = nil
    self._solQuesDetailtMapInfo = nil

    self._questInfos = {}
    self._LineQuestsDroDownLocs = {}
    self._LineQuestsDroDownTypes = {}
    --滑动列表
    self._wtCustomWaterFallList =
        UIUtil.WndWaterfallScrollBox(
        self,
        "DFWaterfallScrollView_Quest",
        self._OnGetQuestRromptCount,
        self._OnProcessQuestRromptWidget
    )

    self._wDFImag_QuestLineBg = self:Wnd("DFImage_Bg", UIImage)
    self._DFTextBlock_PartmentName = self:Wnd("DFTextBlock_PartmentName", UITextBlock)
    self._DFTextBlock_PartmentName_1 = self:Wnd("DFTextBlock_PartmentName_1", UITextBlock)
    self._wtDFCommonProgressBar = self:Wnd("WBP_DFCommonProgressBarV1", DFCommonProgressBarOnly)
    if self._wtDFCommonProgressBar then
        self._wtProgressBar = self._wtDFCommonProgressBar.wtCommonProgressBar
    end

    self._wtAllQuestBtn = self:Wnd("WBP_CommonButtonV3S1_AllQuestBtn", CommonButton)
    self._wtAllQuestBtn:Event("OnClicked", self._OnClickAllQuestBtn, self)
    self._wtAllQuestBtn:Event("OnDeClicked", self._OnClickAllQuestBtn, self)

    self._wtDFCommonDropDownBox = UIUtil.WndDropDownBox(self, "WBP_DFCommonDropDownBox", self.OnQuestTyepDropChanged)

    self._wtTaskName = self:Wnd("DFTextBlock_302", UITextBlock)

    if self.DFButton_TopBarBtn then
        self._wtPrestigeButton = self:Wnd("DFButton_TopBarBtn", UIButton)
        self._wtPrestigeButton:Event("OnClicked", self._OnPrestigeBtnClicked, self)
    end

    self._wtShopItemPanel = self:Wnd("DFCanvasPanel", UIWidgetBase)

    self._timerHandle = nil

    self.dropTabType = 0

    self._wtAssemblyMapNeed = self:Wnd("WBP_AssemblyMapNeed", UIWidgetBase)
    self._wtAssemblyMapNeed3 = self:Wnd("WBP_AssemblyMapNeed_3", UIWidgetBase)

    self._questTaskDetailItem = nil

    self._showAnimFinishList = {}

    self._showAllAnimation = false

    self._ShowAnimDelayHandle = {}
    self._ClickLinePanel = false

    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)

        self._wtDFCanvasPanel_0 = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
	    self._wtDFScrollBox_151 = self:Wnd("DFScroll_Reward", UIWidgetBase)
        -- self._wtDropDownBox = self._wtDFCommonDropDownBox:Wnd("DFCommonCheckButton", UIWidgetBase)
        -- self._wtDropDownKeyIcon = self._wtDropDownBox:Wnd("wtKeyIcon", UIWidgetBase)
        self._selectedItem = nil
        self._defaultGoto = nil
    end
    -- END MODIFICATION   
end

-----------------------------------------------------------------------
--region MS24
---------------------------------------------------任务滚动框----------------------------------------------------
function QuestDetailView:_OnGetQuestRromptCount()
    return #self._questInfos
end

function QuestDetailView:_OnProcessQuestRromptWidget(position, itemWidget)
    local index = position
    local questInfo = self._questInfos[index]
    --itemWidget:StopAllAnimations()
    if questInfo then
        itemWidget:RefreshItemWidget(index, questInfo, self._questInfo.id == questInfo.id)
    end

    -- 动画相关
    if self._showAllAnimation == false then
        local rowIdx = position
        if not self._showAnimFinishList[position] then
            --itemWidget:Collapsed()
            loginfo("_OnProcessQuestRromptWidget position = ", position)
            local timeHandle =
                Timer.DelayCall(
                rowIdx * 0.05,
                function()
                    --itemWidget:Visible()
                    itemWidget:PlayAnimationForward(itemWidget.WBP_QuestDetailItem_in, 1.0, false)
                    self._ShowAnimDelayHandle[position] = nil
                end,
                itemWidget
            )
            self._ShowAnimDelayHandle[position] = timeHandle
            self._showAnimFinishList[position] = true
        end
    end
end

function QuestDetailView:ClearAllDelayTimeHanle()
    for key, value in pairs(self._ShowAnimDelayHandle) do
        if value then
            Timer.CancelDelay(value)
        end
    end

    self._ShowAnimDelayHandle = {}
end

function QuestDetailView:_OnClickAllQuestBtn()
    LogAnalysisTool.SignButtonClicked(10080030)
    if self._ownerQuestLineInfo then
        self._ClickLinePanel = true
        local lineHandle = Module.Quest:OpenSOLQuestLinePanel(self._ownerQuestLineInfo, self._questInfo)
    end
end

function QuestDetailView:_OnPrestigeBtnClicked()
    LogAnalysisTool.SignButtonClicked(10080029)
    Module.Shop:ShowPrestigeUsagePanel(self._ownerQuestLineInfo.merchantId, false)
end

function QuestDetailView:_InitAllQuestDropDownBox()
    if self._wtDFCommonDropDownBox then
        local list = Server.QuestServer:GetAllQuestsByQuestLine(self._ownerQuestLineInfo)
        self._LineQuestsDroDownLocs = {}
        self._LineQuestsDroDownTypes = {}
        table.insert(self._LineQuestsDroDownLocs, Module.Quest.Config.Loc.LineQuestsDroDownLoc_All)
        for _, questInfo in ipairs(list) do
            local tabName = ""
            if questInfo.type == QuestType.Mission then
                tabName = Module.Quest.Config.Loc.LineQuestsDroDownLoc_Mission
            elseif questInfo.type == QuestType.ImportantQuest then
                tabName = Module.Quest.Config.Loc.LineQuestsDroDownLoc_Important
            elseif questInfo.type == QuestType.Branch then
                tabName = Module.Quest.Config.Loc.LineQuestsDroDownLoc_Branch
            elseif questInfo.type == QuestType.SeasonQuest then
                tabName = Module.Quest.Config.Loc.LineQuestsDroDownLoc_Season
            end
            if tabName ~= "" and table.contains(self._LineQuestsDroDownLocs, tabName) == false then
                table.insert(self._LineQuestsDroDownLocs, tabName)
            end
            if table.contains(self._LineQuestsDroDownTypes, questInfo.type) == false then
                table.insert(self._LineQuestsDroDownTypes, questInfo.type)
            end
        end
        UIUtil.InitDropDownBox(self._wtDFCommonDropDownBox, self._LineQuestsDroDownLocs, {}, 0)
    end
end

function QuestDetailView:OnQuestTyepDropChanged(idx)
    loginfo("QuestDetailView:OnLineQuestsCheckedTabIndexChanged idx =", idx)
    self._wtDFCommonDropDownBox:BP_SetMainTabText(self._LineQuestsDroDownLocs[idx + 1])
    local list = Server.QuestServer:GetAllQuestsByQuestLine(self._ownerQuestLineInfo)
    if idx == 0 then
        self:_SortQuestInfos(list, 0)
        self.dropTabType = 0
    else
        self.dropTabType = self._LineQuestsDroDownTypes[idx]
        self:_SortQuestInfos(list, self.dropTabType)
    end
   
    self._questInfo = self._questInfos[1]
    self:ClearAllDelayTimeHanle()
    self._showAllAnimation = false
    self._wtCustomWaterFallList:RefreshAllItems()
    self._showAllAnimation = true
    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    -- if IsHD() then
    --     WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup1)
    -- end
	-- END MODIFICATION
    self:_InitQuestDetailInfo()
end

function QuestDetailView:_OnQuestLineTabIndexChanged(curIdx, lastIdx)
    local questLineInfos = Server.QuestServer:GetQuestLineInfosByQuestType(self._questLineType)
    if questLineInfos[curIdx] == nil or questLineInfos[curIdx]:IsLineOpend() == false or self._bOnHide then
        return
    end
    self._bOnHide = false
    self._showAnimFinishList = {}
    self.dropTabType = 0
    self._ownerQuestLineInfo = questLineInfos[curIdx]
    self:_SortQuestInfos(Server.QuestServer:GetAllQuestsByQuestLine(self._ownerQuestLineInfo), 0)
    --找到第一个进行中或者可以接取的
    if self._questInfo == nil then
        self._questInfo = self._questInfos[1]
    else
        local curQuestInfoLine = Server.QuestServer:GetQuestLineInfoByQuest(self._questInfo)
        if curQuestInfoLine and curQuestInfoLine.questLineId ~= self._ownerQuestLineInfo.questLineId then
            self._questInfo = self._questInfos[1]
        end
    end

    Module.ItemDetail:CloseAllPopUI()
    self:_InitOwnerQuestLineDetailInfo()
    self:_InitQuestDetailInfo()

    self._wtDFCommonDropDownBox:BP_SetMainTabText(self._LineQuestsDroDownLocs[1])
    self:ClearAllDelayTimeHanle()
    self._showAllAnimation = false
    self._wtCustomWaterFallList:RefreshAllItems()
    self._showAllAnimation = true
    if IsHD() then
        self:PlayWidgetAnim(self.WBP_TaskDetailsMain_in)
        -- BEGIN MODIFICATION @ VIRTUOS : Navigation
	    -- 在页面切换的时候刷新focus
	    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup1)
	    -- END MODIFICATION
    else
        self:PlayWidgetAnim(self.WBP_TaskDetailsMain_in_Mobile)
    end

    -- BEGIN MODIFICATION @ VIRTUOS : Navigation
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    -- END MODIFICATION
end

function QuestDetailView:_SortQuestInfos(questInfos, type)
    if questInfos == nil or #questInfos <= 0 then
        self._questInfos = {}
        return
    end
    local newQuestInfos = QuestLogic.SortQuestList(questInfos,type)
    self._questInfos = newQuestInfos
end

--endregion
-----------------------------------------------------------------------

------------------------------------ Override function ------------------------------------
function QuestDetailView:OnInitExtraData(questLineInfo, questInfo)
    if questLineInfo == nil and questInfo == nil then
        logerror(" QuestDetailView OnInitExtraData : questLineInfo and questInfo is nil ")
        return
    end
    self._questLineType = setdefault(questType, QuestType.Mission)
    self.dropTabType = 0
    if questInfo then
        self._questInfo = questInfo
        self._ownerQuestLineInfo = Server.QuestServer:GetQuestLineInfoByQuest(self._questInfo)
        self:_SortQuestInfos(Server.QuestServer:GetAllQuestsByQuestLine(self._ownerQuestLineInfo), 0)
    else
        if questLineInfo then
            self._ownerQuestLineInfo = questLineInfo
            self:_SortQuestInfos(Server.QuestServer:GetAllQuestsByQuestLine(self._ownerQuestLineInfo), 0)
            --找到第一个进行中或者可以接取的
            self._questInfo = self._questInfos[1]
        end
    end
    
    Module.CommonBar:RegStackUITopBarTitle(self, Module.Quest.Config.Loc.QuestDeatilView)
end

function QuestDetailView:OnOpen()
    -- print("xhz >>>>>>>>>>>>>>>>> QuestDetailView:OnOpen() ")
end

function QuestDetailView:OnClose()
    -- print("xhz >>>>>>>>>>>>>>>>> QuestDetailView:OnClose() ")
    self:_RemoveEventListener()
    self:ClearAllDelayTimeHanle()
    Module.Inventory:CloseItemSubmitPanel()
    -- Module.Quest:CloseSOLQuestDetailView()
    Facade.UIManager:ClearSubUIByParent(self, self._wtLockedShopItemScrollBox)
    Facade.UIManager:ClearSubUIByParent(self, self._wtRewardScrollBox)
    self._questTaskDetailItem = nil
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end

    Server.QuestServer.Events.evtQuestExitDetailView:Invoke()

    if IsHD() then
        self:PlayWidgetAnim(self.WBP_TaskDetailsMain_out)
    else
        self:PlayWidgetAnim(self.WBP_TaskDetailsMain_out_Mobile)
    end
end

function QuestDetailView:OnShowBegin()
    -- print("xhz >>>>>>>>>>>>>>>>> QuestDetailView:OnShowBegin() ")
    --self:_InitOwnerQuestLineDetailInfo()
    --self._wtCustomWaterFallList:RefreshAllItems()
    --self:_InitQuestDetailInfo()

    self._showAnimFinishList = {}
    self:_InitTopTab()
    self._bOnHide = false

    self:_AddEventListener()
   
    LogAnalysisTool.DoSendOpenUILog(LogAnalysisTool.EItemDetailFromUIType.TaskQuest)

    local submitPanelHandle = Module.Inventory:GetItemSubmitPanel()
    if submitPanelHandle and submitPanelHandle:GetUIIns() and not submitPanelHandle:GetUIIns():IsRelease() then
        submitPanelHandle:GetUIIns():UpdateAvailableItems()
        submitPanelHandle:GetUIIns():OnPlayAnimation()
        submitPanelHandle:GetUIIns():Show()
    end

    if IsHD() then
        self:PlayWidgetAnim(self.WBP_TaskDetailsMain_in)
        -- BEGIN MODIFICATION @ VIRTUOS : Navigation
        self:_EnableGamepadFeature()
        -- END MODIFICATION
    else
        self:PlayWidgetAnim(self.WBP_TaskDetailsMain_in_Mobile)
    end
end

function QuestDetailView:OnHide()
    -- print("xhz >>>>>>>>>>>>>>>>> QuestDetailView:OnHide() ")
    self:_RemoveEventListener()
    self._showAllAnimation = false
    local submitPanelHandle = Module.Inventory:GetItemSubmitPanel()
    if submitPanelHandle and submitPanelHandle:GetUIIns() and not submitPanelHandle:GetUIIns():IsRelease() then
        submitPanelHandle:GetUIIns():Hide()
    end


    self:ClearAllDelayTimeHanle()
    Module.Inventory:CloseItemSubmitPanel()
    -- Module.Quest:CloseSOLQuestDetailView()
    --Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
    -- Facade.UIManager:RemoveSubUIByParent(self, self._wtLockedShopItemScrollBox)
    -- Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardScrollBox)

    if Module.Quest:GetToDetailFromSource() == Module.Quest.Config.EEnterToDetailType.Jump and not self._ClickLinePanel then
        Server.QuestServer.Events.evtQuestExitDetailView:Invoke()
        loginfo("QuestDetailView:OnHide Clear")
    end
    self._ClickLinePanel = false
    Module.Quest:SetToDetailFromSource(Module.Quest.Config.EEnterToDetailType.None)
    self._bOnHide = true

end

function QuestDetailView:_InitTopTab()
    local tabTitles = {}
    local reddotTrieList = {}
    local defalutTab = 1
    local lockDataList = {}
    local questLineInfos = Server.QuestServer:GetQuestLineInfosByQuestType(self._questLineType)
    for index, lineInfo in ipairs(questLineInfos) do
        table.insert(tabTitles, lineInfo.lineName)
        if lineInfo.questLineId == self._ownerQuestLineInfo.questLineId then
            defalutTab = index
        end
        table.insert(
            lockDataList,
            {
                fCustomLockChecker = function(a, data)
                    return self:_IsLocked(index, data)
                end,
                customLockCheckerCaller = self
            }
        )
        local key = string.format("TakeAndReward_QuestLine%s", lineInfo.questLineId)
        table.insert(
            reddotTrieList,
            {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Quest, key = key}}
            }
        )
    end
    local imagePath = Module.Quest.Config.TabImgPathListNoSeason
    if IsHD() then
        imagePath = {}
    end
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(
        self,
        {
            tabTxtList = tabTitles,
            imgPathList = imagePath,
            fCallbackIns = SafeCallBack(self._OnQuestLineTabIndexChanged, self),
            defalutIdx = defalutTab,
            bNewReddotTrie = true,
            reddotTrieRegItemList = reddotTrieList,
            lockDataList = lockDataList
        }
    )
end

function QuestDetailView:_IsLocked(index, data)
    local questLineInfos = Server.QuestServer:GetQuestLineInfosByQuestType(self._questLineType)
    if questLineInfos then
        local lineInfo = questLineInfos[index]
        if lineInfo then
            if not lineInfo:IsLineOpend() and data.bShowTip then
                Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.questLineLocked2)
            end
            return not lineInfo:IsLineOpend()
        end
    end
    return false
end

-- BEGIN MODIFICATION @ VIRTUOS : Navigation and input
function QuestDetailView:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end
-- END MODIFICATION 

------------------------------------ Event listener function ------------------------------------
function QuestDetailView:_OnNPCIconClicked()
    -- if self._ownerQuestLineInfo and self._ownerQuestLineInfo.questPublishNPCId > 0 then
    -- 	Module.Quest:OpenNPCRolfInfoPanel(self._ownerQuestLineInfo.questPublishNPCId)
    -- end
    return {}
end

function QuestDetailView:_OnClickDetailBtn()
    Facade.UIManager:AsyncShowUI(UIName2ID.QuestDescView, nil, nil, self._questInfo, self._npcInfo)
end

function QuestDetailView:_OnPlayCgBtnClicked()
    if self._questInfo.state >= QuestState.Accepted then
        Module.Quest:OpenCGPlayPanel(self._questInfo.cgPath, CreateCallBack(self._OnCgPlayEnd, self))
    end
end

function QuestDetailView:_OnCgPlayEnd()
    if self._questInfo.state < QuestState.Completed then
        Server.QuestServer:CGQuestPlayFinish(self._questInfo.id, self._questInfo:GetQusetObjectives()[1].id)
    end
end

function QuestDetailView:_OnVoiceMessageClicked()
    Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.underDevelopment)
end

function QuestDetailView:_OnClickActionBtn()
    if self._questInfo == nil then
        return
    end
    if self._questInfo.state == QuestState.Unaccepted then
        QuestLogic.DoAcceptQuest(self._questInfo)
    elseif self._questInfo.state == QuestState.Completed then
        QuestLogic.DoGetQuestRewards(self._questInfo) --前往领取奖励物品
    elseif self._questInfo.state == QuestState.Failed then
        -- 提交道具
        QuestLogic.DoAcceptQuest(self._questInfo)
    elseif self._questInfo.state == QuestState.Accepted and self._questInfo:IsFinishSubmitObjectives() == false then
        local itemStructList, submitDatas, zeroStoredItemList = QuestLogic.Get3ItemSubmitInfos(self._questInfo)
        if table.isempty(itemStructList) then
            Module.CommonTips:ShowSimpleTip(Module.Quest.Config.Loc.noItemsToSubmit) -- 暂时没有可提交的物品
        else
            -- 点击提交物品记录当前打开任务详情页，返回任务界面时候打开
            -- Module.Quest.Field:SetCurQuestDetailInfo(self._questInfo)
            Module.Quest.Field:SetSubmitQuestId(self._questInfo.id)
            Module.Inventory:OpenItemSubmitPanel(
                itemStructList,
                submitDatas,
                zeroStoredItemList,
                QuestLogic.ProcessItemSubmit
            )
        end
    end
end

function QuestDetailView:_OnShowNewQuestInfo()
    if self._wtCustomWaterFallList then
        self:_SortQuestInfos(Server.QuestServer:GetAllQuestsByQuestLine(self._ownerQuestLineInfo), self.dropTabType)
        self._questInfo = self._questInfos[1]
        self:ClearAllDelayTimeHanle()
        self._showAllAnimation = false
        self._wtCustomWaterFallList:RefreshAllItems()
        self._showAllAnimation = true


        self:_InitQuestDetailInfo()
    end
end

function QuestDetailView:_OnQuestStateUpdate(questId)
    if self._questInfo then
        if questId == self._questInfo.id then
            self:_UpdateDetailActionBtn()
            if self._questInfo.state >= QuestState.Rewarded then
                if self._wtRewardScrollBox:GetChildrenCount() > 0 then
                    local allRewardItems = self._wtRewardScrollBox:GetAllChildren()
                    for _, itemView in ipairs(allRewardItems) do
                        itemView:SetAlreadyGetState()
                    end
                end
                if self._wtLockedShopItemScrollBox:GetChildrenCount() > 0 then
                    local shopItems = self._wtLockedShopItemScrollBox:GetAllChildren()
                    for _, itemView in ipairs(shopItems) do
                        itemView:SetAlreadyGetState()
                    end
                end

                local postQuests = self._questInfo:GetPostQuests()
                local multiPostQusets = self._questInfo:GetMultiPostQuests()
                local hasPostQuest = false
                if #postQuests > 0 then
                    for index, value in ipairs(postQuests) do
                        if value:IsPreQuestAllFinished() then
                            Module.Quest.Field:CachingNewlyOpenQuestItem(value)
                            hasPostQuest = true
                        end
                    end

                    if hasPostQuest then
                        Module.Reward:OpenQuestDetailNewPanel(Module.Quest.Config.Loc.NewQuestOpen, nil, postQuests)
                    end
                elseif #multiPostQusets > 0 then
                    for index, value in ipairs(multiPostQusets) do
                        if value:IsPreQuestAllFinished() then
                            Module.Quest.Field:CachingNewlyOpenQuestItem(value)
                            hasPostQuest = true
                        end
                    end

                    if hasPostQuest then
                        Module.Reward:OpenQuestDetailNewPanel(
                            Module.Quest.Config.Loc.NewQuestOpen,
                            nil,
                            multiPostQusets
                        )
                    end
                end

                if #postQuests <= 0 or hasPostQuest == false or #multiPostQusets <= 0 then
                    self:_OnShowNewQuestInfo()
                end
            end
        end
    end
end

function QuestDetailView:_OnQuestSubmitItemsSuccess(questId)
    if questId == self._questInfo.id then
        -- 提交物品成功时刷新提交界面
        local submitPanelHandle = Module.Inventory:GetItemSubmitPanel()
        if submitPanelHandle then
            submitPanelHandle:GetUIIns():ItemSubmitedSuccessUpdate(QuestLogic.Get3ItemSubmitInfos(self._questInfo))
        end
    end
end

function QuestDetailView:_OnQuestItemClicked(questInfo, widget)
    if self._questInfo.id == questInfo.id then
        return
    end
    self._selectedItem = widget
    self._questInfo = questInfo
    self:_InitQuestDetailInfo()
end

------------------------------------ Private function ------------------------------------
function QuestDetailView:_AddEventListener()
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateQuestState, self._OnQuestStateUpdate, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateObjectiveState, self._UpdateDetailActionBtn, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostUpdateDeposData, self._UpdateDetailActionBtn, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtQuestSubmitItemsSuccess, self._OnQuestSubmitItemsSuccess, self)
    self:AddLuaEvent(Module.Quest.Config.evtQuestItemSelected, self._OnQuestItemClicked, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtShowNewQuestInfo, self._OnShowNewQuestInfo, self)

    self:AddLuaEvent(Server.RoleInfoServer.Events.evtRoleQualityUpdate, self._UpdateQuestLineDetailInfo, self)
    self:AddLuaEvent(Server.QuestServer.Events.evtUpdateSeasonLevel, self._UpdateDetailActionBtn, self)

    self:AddLuaEvent(Server.MatchServer.Events.evtPrepareJoinMatch, self.OnPrepareJoinMatch_Safe, self)

    if IsHD() then
        self:AddLuaEvent(Module.Quest.Config.evtQuestObjectiveClickable, self._OnUpdateNavGroup3, self)
    end
end

function QuestDetailView:_RemoveEventListener()
    self:RemoveAllLuaEvent()
end

function QuestDetailView:OnPrepareJoinMatch_Safe()
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.QuestDetailOpen)
end

function QuestDetailView:_OnGetObjectiveItemCount()
    self._childQuestObjectives = self._questInfo:GetQusetObjectives()
    return #self._childQuestObjectives
end

--刷新选择的任务型相关内容 左侧
function QuestDetailView:_InitOwnerQuestLineDetailInfo()
    self:_InitAllQuestDropDownBox()
    self:_UpdateQuestLineDetailInfo()
end

function QuestDetailView:_UpdateQuestLineDetailInfo()
    if not string.isempty(self._ownerQuestLineInfo.lineCover) then
        self._wDFImag_QuestLineBg:AsyncSetImagePath(self._ownerQuestLineInfo.lineTab)
    end
    local merchantStruct = Server.ShopServer:GetMerchantStructById(self._ownerQuestLineInfo.merchantId)
    if merchantStruct then
        local level = merchantStruct:GetPrestigeData().level or 1
        self._DFTextBlock_PartmentName:SetText(merchantStruct.traderConfig.TraderCenterType)
        self._DFTextBlock_PartmentName_1:SelfHitTestInvisible()
        self._DFTextBlock_PartmentName_1:SetText(string.format(Module.Quest.Config.Loc.levelText, level)) -- 获取声望等级
        self._wtDFCommonProgressBar:SelfHitTestInvisible()
        local maxLv = Server.RoleInfoServer:GetPrestigeMaxLv()
        if level >= maxLv then
            self._wtProgressBar:SetPercent(1)
        else
            local curValue, curScore =
                Server.RoleInfoServer:GetPrestigeCurValueAndScoreById(merchantStruct.prestigeId)
            if curScore > 0 then
                self._wtProgressBar:SetPercent(curValue / curScore)
            else
                self._wtProgressBar:SetPercent(0)
            end
        end
    end
end

--刷新选择的任务相关内容 右侧
function QuestDetailView:_InitQuestDetailInfo()
    if self._questInfo then
        Module.ItemDetail:CloseAllPopUI()

        self._wtQuestDetailPanel:UpdateInfo(self._questInfo)
        self._defaultGoto = nil
        self:_UpdateDetailActionBtn()
        self:_UpdateVoiceMessage()
        self:_UpdateMapImage()

        Facade.UIManager:RemoveSubUIByParent(self, self._wtRewardScrollBox)
        Facade.UIManager:RemoveSubUIByParent(self, self._wtLockedShopItemScrollBox)
        self:_UpdateRewardList()
        self:_UpdateLockedShopItemList()
        self:SetQuestDetailState(true)
    else
        self:SetQuestDetailState(false)
    end
end

---右侧按钮状态
function QuestDetailView:_UpdateDetailActionBtn()
    if self._questInfo == nil then
        return
    end
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end

    self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Collapsed)
    self._wtAssemblyMapNeed3:SetVisibility(ESlateVisibility.Collapsed)
    -- self._wtGiveUpQuestBtn:SetVisibility(ESlateVisibility.Collapsed)

    self._wtStateText:SetText("")
    self._wtActionBtn:SetIsEnabledStyle(true)
    if self._questInfo.state == QuestState.Unaccepted or self._questInfo.state == QuestState.Unread then
        if Server.QuestServer:IsQuestAcceptable(self._questInfo) then
            self:BtnType(0)
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.accept)
        else
            self:BtnType(1)
            self._wtStateText:SetText(Module.Quest.Config.Loc.NotUnLock)
            if self._questInfo:GetRemainToAcceptTime() > 0 then
                local desc = ""
                local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self._questInfo:GetRemainToAcceptTime())
                if day > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min) ..
                        Module.Quest.Config.Loc.CanAccept
                elseif hour > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min) ..
                        Module.Quest.Config.Loc.CanAccept
                elseif min > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min) ..
                        Module.Quest.Config.Loc.CanAccept
                elseif sec > 0 then
                    desc =
                        string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1) ..
                        Module.Quest.Config.Loc.CanAccept
                end
                self._wtAssemblyMapNeed:SetType(0)
                self._wtAssemblyMapNeed:SetMainTitle(desc)
                self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Visible)

                self._timerHandle = Timer:NewIns(5, 0)
                self._timerHandle:AddListener(self._UpdateRemainCountTime, self)
                self._timerHandle:Start()
            end
            if Server.RoleInfoServer.seasonLevel < self._questInfo.levelLimit then
                -- 玩家等级不足导致任务没解锁，等级提示应为纯文本
                self._wtAssemblyMapNeed3:SetType(0)
                self._wtAssemblyMapNeed3:SetMainTitle(
                    string.format(Module.Quest.Config.Loc.LvlLimitPureTxt, self._questInfo.levelLimit)
                )
                self._wtAssemblyMapNeed3:SetVisibility(ESlateVisibility.Visible)
            end
        end
    elseif self._questInfo.state == QuestState.Failed then
        self:BtnType(0)
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.restart)
    elseif self._questInfo.state == QuestState.Accepted then
        -- 没有道具目标或提交已完成
        if self._questInfo:IsFinishSubmitObjectives() then
            self:BtnType(1)
            if self._questInfo.bCgQuest then
                self._wtStateText:SetText(Module.Quest.Config.Loc.watchCgGetReward)
            else
                self._wtStateText:SetText(Module.Quest.Config.Loc.underWay)
            end
        else
            local itemStructList, submitDatas = QuestLogic.Get3ItemSubmitInfos(self._questInfo)
            self:BtnType(0)
            self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.submit)
        end
    elseif self._questInfo.state == QuestState.Completed then
        self:BtnType(0)
        self._wtActionBtn:SetMainTitle(Module.Quest.Config.Loc.receive)
    elseif self._questInfo.state == QuestState.Rewarded then
        self:BtnType(2)
        self._wtStateText:SetText(Module.Quest.Config.Loc.rewardReceived)
    end
end

function QuestDetailView:_UpdateVoiceMessage()
    self._wtVoiceMessageBtn:Collapsed()
    self._wtPanel_03:Collapsed()
end

function QuestDetailView:_UpdateMapImage()
    -- 重置questMapImg
    self._wtQuestMapImg:SetRenderTransformAngle(0)
    self._wtQuestMapImg:SetRenderScale(FVector2D(1, 1))
    self._wtQuestMapImg:SetRenderTranslation(FVector2D(0, 0))
    self:IsMap(false)
    if not self._questInfo.bIsInMapDetail then
        --使用插画
        self._wtQuestMapCenterWidget:Collapsed()
        self._wtMapDetailWidget:Collapsed()
        self._wtQuestMapImg:AsyncSetImagePath(self._questInfo.questImage, false)
        if DFHD_LUA == 1 then
            --self._wtQuestMapImg.Slot:SetSize(FVector2D(1966, 1117))
        else
            --self._wtQuestMapImg.Slot:SetSize(FVector2D(1408, 800))
        end
    else
        self._wtQuestMapCenterWidget:Collapsed()
        local MapInfoSystem = UMapInfoSystem.GetInst(GetGameInstance())
        if not MapInfoSystem then
            return
        end
        local SOLQuestDetailMapCfg = Facade.TableManager:GetTable("SOLQuestDetailMapConfig", false)
        local MapInfoCfg = Module.ArmedForce.Config.mapInfoTable
        local mapInfoConfig = nil
        for _, quesDetailtMapTab in pairs(SOLQuestDetailMapCfg) do
            if quesDetailtMapTab.QuestID == self._questInfo.id then
                mapInfoConfig = MapInfoSystem:GetMapInfoConfig(quesDetailtMapTab.MapName)
                self._solQuesDetailtMapInfo = quesDetailtMapTab
                break
            end
        end
        -- if mapInfo then
        -- 	local mapInfoConfig = mapInfo.MapInfoConfig
        -- 	self._wtQuestMapImg:AsyncSetImagePath(mapInfoConfig:GetMapPath(), false)
        -- 	self._wtQuestMapImg.Slot:SetSize(FVector2D(4096, 4096))
        -- 	-- self._wtQuestMapImg:SetRenderTransformAngle(90)
        -- 	local questMapScale = tonumber(self._solQuesDetailtMapInfo.MapRangeRadius)
        -- 	self._wtQuestMapImg:SetRenderScale(FVector2D(questMapScale, questMapScale))
        -- end

        if mapInfoConfig then
            self._wtMapDetailWidget:SelfHitTestInvisible()

            self:IsMap(true)
            local mapPath = mapInfoConfig:GetMapPath()
            self._wtQuestMapImg:AsyncSetImagePath(mapPath, false)
            local mapWorldExtent = mapInfoConfig.WorldExtent
            local mapWorldCenter = mapInfoConfig.WorldCenter
            local mapRotationType = mapInfoConfig.RotationType
            local questMapCenter = self._solQuesDetailtMapInfo.MapCenterLocation --screenCenterLocation
            local questMapScale = self._solQuesDetailtMapInfo.MapRangeRadius -- 缩放比例
            local questMapCenter2WorldCenter =
                FVector2D(mapWorldCenter.X - questMapCenter.X, mapWorldCenter.Y - questMapCenter.Y)
            self._wtQuestMapImg:SetBrushSize(FVector2D(4096, 4096))

            -- 和旋转有关
            if mapRotationType == 0 then
                -- 不旋转
            elseif mapRotationType == 1 then
                -- 顺时针旋转90°
                self._wtQuestMapImg:SetRenderTransformAngle(90)
                questMapCenter2WorldCenter.X, questMapCenter2WorldCenter.Y =
                    -questMapCenter2WorldCenter.Y,
                    questMapCenter2WorldCenter.X
            elseif mapRotationType == 2 then
                -- 逆时针旋转90°
                self._wtQuestMapImg:SetRenderTransformAngle(-90)
                questMapCenter2WorldCenter.X, questMapCenter2WorldCenter.Y =
                    questMapCenter2WorldCenter.Y,
                    -questMapCenter2WorldCenter.X
            end

            -- Set Map
            local scaleMapSizeX = 4096 * questMapScale
            local scaleMapSizeY = 4096 * questMapScale
            local imageToWorldSizeRatio =
                FVector2D(scaleMapSizeX / mapWorldExtent.X / 2, scaleMapSizeY / mapWorldExtent.Y / 2)
            local imageVector1 =
                FVector2D(
                questMapCenter2WorldCenter.X * imageToWorldSizeRatio.X,
                questMapCenter2WorldCenter.Y * imageToWorldSizeRatio.Y
            )
            self._wtQuestMapImg:SetRenderScale(FVector2D(questMapScale, questMapScale))

            -- 检查是否有未接触的边接, 计算补偿向量移动地图
            -- X方向没有和边界贴合
            local XAlignDiff = 0
            if (imageVector1.X - scaleMapSizeX / 2 > 0) then
                -- loginfo("ruochen:  左未贴合")
                -- 左边未贴合
                XAlignDiff = -(imageVector1.X - scaleMapSizeX / 2)
            elseif (scaleMapSizeX / 2 - self._mapSize.X / 2 + imageVector1.X < 0) then
                --右边未贴合
                XAlignDiff = -(scaleMapSizeX / 2 - self._mapSize.X / 2 + imageVector1.X)
            -- loginfo("ruochen:  右未贴合")
            end

            -- Y方向没有和边界贴合
            local YAlignDiff = 0
            if (imageVector1.Y - scaleMapSizeY / 2 > 0) then
                -- 上边未贴合
                YAlignDiff = -(imageVector1.Y - scaleMapSizeY / 2)
            elseif (scaleMapSizeY / 2 - self._mapSize.Y / 2 + imageVector1.Y < 0) then
                -- 下边未贴合
                YAlignDiff = -(scaleMapSizeY / 2 - self._mapSize.Y / 2 + imageVector1.Y)
            end

            -- 如果地图缩小到尺寸比地图窗口还要小的时候, 需要居中它
            if (scaleMapSizeX < self._mapSize.X) then
                local XSizeDiff = self._mapSize.X - scaleMapSizeX
                XAlignDiff = XAlignDiff - XSizeDiff / 2
            end
            if (scaleMapSizeY < self._mapSize.Y) then
                local YSizeDiff = self._mapSize.Y - scaleMapSizeY
                YAlignDiff = YAlignDiff - YSizeDiff / 2
            end

            imageVector1.X = imageVector1.X + XAlignDiff
            imageVector1.Y = imageVector1.Y + YAlignDiff
            self._wtQuestMapImg:SetRenderTranslation(imageVector1) -- 此时地图居中
            self._wtQuestMapCenterWidget:SetRenderTranslation(FVector2D(XAlignDiff, YAlignDiff)) -- 视为中心点的位置
            self._wtQuestMapCenterWidget:Collapsed()
            self._wtMapDetailWidget:SetRenderTranslation(FVector2D(XAlignDiff, YAlignDiff))

            -- Set MapDetailTarget and MapDetailObjectives
            local questTargetCoord = self._solQuesDetailtMapInfo.QuestTargetLocation
            local questMapDetail2MapCenter =
                FVector2D(questTargetCoord.X - questMapCenter.X, questTargetCoord.Y - questMapCenter.Y)
            if mapRotationType == 0 then
                -- 不旋转
            elseif mapRotationType == 1 then
                questMapDetail2MapCenter.X, questMapDetail2MapCenter.Y =
                    -questMapDetail2MapCenter.Y,
                    questMapDetail2MapCenter.X
            elseif mapRotationType == 2 then
                questMapDetail2MapCenter.X, questMapDetail2MapCenter.Y =
                    questMapDetail2MapCenter.Y,
                    -questMapDetail2MapCenter.X
            end
            local imageVector2 =
                FVector2D(
                questMapDetail2MapCenter.X * imageToWorldSizeRatio.X,
                questMapDetail2MapCenter.Y * imageToWorldSizeRatio.Y
            )
            self._wtMapDetailWidget:SetRenderTranslation(imageVector2)

            local SOLQuestDetailTargetTypeCfg = Facade.TableManager:GetTable("SOLQuestDetailTargetType", false)
            local SOLQuestDetailObjectivesTypeCfg = Facade.TableManager:GetTable("SOLQuestDetailObjectivesType", false)
            local questDetailTargetIconPath = nil
            local questDetailObjectivesIconPath = nil
            for _, quesDetailtTargetTab in pairs(SOLQuestDetailTargetTypeCfg) do
                if
                    quesDetailtTargetTab.TargetID and
                        tonumber(quesDetailtTargetTab.TargetID) == tonumber(self._solQuesDetailtMapInfo.MapDetailTarget)
                 then
                    questDetailTargetIconPath = quesDetailtTargetTab.TargetIcon
                    break
                end
            end
            for _, quesDetailtObjTab in pairs(SOLQuestDetailObjectivesTypeCfg) do
                if
                    quesDetailtObjTab.ObjectiveID and
                        tonumber(quesDetailtObjTab.ObjectiveID) ==
                            tonumber(self._solQuesDetailtMapInfo.MapDetailObjectives)
                 then
                    questDetailObjectivesIconPath = quesDetailtObjTab.ObjectiveIcon
                    break
                end
            end
            self._wtMapDetailWidget:InitMapDetailItemData(
                self._solQuesDetailtMapInfo.MapDetailTarget,
                self._solQuesDetailtMapInfo.MapDetailTargetParam1,
                self._solQuesDetailtMapInfo.MapDetailTargetParam2,
                self._solQuesDetailtMapInfo.MapDetailTargetScale,
                questDetailTargetIconPath,
                self._solQuesDetailtMapInfo.MapDetailObjectives,
                self._solQuesDetailtMapInfo.MapDetailObjectivesDesc,
                questDetailObjectivesIconPath
            )

            -- Set QuestMapRelatedInfoPanel
            self._wtQuestMapRelatedInfoPanel:SelfHitTestInvisible()
            -- self._wtQuestMapDecorativePanel:SelfHitTestInvisible()
            self._wtMapLocationInfoTxt:SetText(self._solQuesDetailtMapInfo.MapLocationInfo)
            self._wtMapTemperatureTxt:SetText(self._solQuesDetailtMapInfo.MapTemperatureInfo)
            self._wtMapTimeInfoTxt:SetText(self._solQuesDetailtMapInfo.MapTimeInfo)
        end
    end
end

function QuestDetailView:_UpdateRewardList()
    local allRewardList = self._questInfo:GetRewardList()
    local newList = {}
    for index, value in ipairs(allRewardList) do
        table.insert(newList, value)
    end

    if #newList > 0 then
        for _, rewardItem in ipairs(newList) do
            local weakUIIns, instanceId =
                Module.CommonWidget:CreateIVCommonItemTemplateBySize(self, self._wtRewardScrollBox, nil, 168, 168)
            local rewardItemView = getfromweak(weakUIIns)
            -- if rewardItem:GetFeature():GetFeatureType() == EFeatureType.Weapon then
            -- 	local weakUIIns, instanceId = Module.CommonWidget:CreateIVCommonItemTemplateBySize(self, self._wtRewardScrollBox, nil, 356,168)
            -- 	rewardItemView = getfromweak(weakUIIns)
            -- else
            -- 	local weakUIIns, instanceId = Module.CommonWidget:CreateIVCommonItemTemplateBySize(self, self._wtRewardScrollBox, nil, 168,168)
            -- 	rewardItemView = getfromweak(weakUIIns)
            -- end
            rewardItemView:InitItem(rewardItem)
            -- 调用IVCommonItemTemplate设置详情页上报数据的界面id
            rewardItemView:SetItemDetailFromUIID(LogAnalysisTool.EItemDetailFromUIType.TaskQuest)
            if self._questInfo.state >= QuestState.Rewarded then
                rewardItemView:SetAlreadyGetState()
                rewardItemView:EnableComponent(EComp.GetMask, true)
            else
                rewardItemView:EnableComponent(EComp.GetMask, false)
            end
            rewardItemView:ShowBindingComp(rewardItem.bindType ~= PropBindingType.BindingNotBind)
            rewardItemView.Slot:SetPadding(FMargin(0, 0, 20, 0))
        end
    end
end

function QuestDetailView:_UpdateLockedShopItemList()
    local lockedShopItemList = Server.ShopServer:GetShopItemByQuestId(self._questInfo.id)
    self._wtShopItemPanel:SelfHitTestInvisible()
    if #lockedShopItemList > 0 then
        for _, shopItem in ipairs(lockedShopItemList) do
            local weakUIIns, instanceId =
                Module.CommonWidget:CreateIVCommonItemTemplateBySize(
                self,
                self._wtLockedShopItemScrollBox,
                nil,
                168,
                168
            )
            local shopItemView = getfromweak(weakUIIns)

            local item = simpleclone(shopItem.item, true)
            item.num = 0
            shopItemView:InitItem(item)
            -- 调用IVCommonItemTemplate设置详情页上报数据的界面id
            shopItemView:SetItemDetailFromUIID(LogAnalysisTool.EItemDetailFromUIType.TaskQuest)
            if shopItem:GetIsUnlock() then
                shopItemView:SetAlreadyGetState()
                shopItemView:EnableComponent(EComp.GetMask, true)
            else
                shopItemView:EnableComponent(EComp.GetMask, false)
            end
            shopItemView:ShowBindingComp(shopItem.item.bindType ~= PropBindingType.BindingNotBind)
            shopItemView.Slot:SetPadding(FMargin(0, 0, 20, 0))
        end
    else
        self._wtShopItemPanel:Collapsed()
    end
end

function QuestDetailView:OnNavBack()
    self:_OnClickCloseBtn()
    return true
end

function QuestDetailView:_OnClickCloseBtn()
    Module.Quest:CloseSOLQuestDetailView()
    Module.Inventory:CloseItemSubmitPanel()
    return {}
end

function QuestDetailView:OnAnimFinished(anim)
    if anim == self.WBP_TaskDetailsMain_in then
        Module.Quest.Config.evtQuestDetailLoadFinish:Invoke()
    end
end

function QuestDetailView:_UpdateRemainCountTime()
    local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self._questInfo:GetRemainToAcceptTime())
    local desc = ""
    if day > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif hour > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min) ..
            Module.Quest.Config.Loc.CanAccept
    elseif min > 0 then
        desc =
            string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min) ..
            Module.Quest.Config.Loc.CanAccept
    else
        desc = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, 1) .. Module.Quest.Config.Loc.CanAccept
        if sec <= 0 then
            if self._timerHandle then
                self._timerHandle:Release()
                self._timerHandle = nil
            end
            self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Collapsed)
            return
        end
    end

    self._wtAssemblyMapNeed:SetType(0)
    self._wtAssemblyMapNeed:SetMainTitle(desc)
    self._wtAssemblyMapNeed:SetVisibility(ESlateVisibility.Visible)
end

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
function QuestDetailView:_EnableGamepadFeature()
    if not self._wtNavGroup1 then
        self._wtNavGroup1 = WidgetUtil.RegisterNavigationGroup(self._wtCustomWaterFallList, self, "Hittest")
    end
    if not self._wtNavGroup2 then
        self._wtNavGroup2 = WidgetUtil.RegisterNavigationGroup(self._wtDFScrollBox_151, self, "Hittest")
    end
    if not self._wtNavGroup3 then
        -- self._wtNavGroup3 = WidgetUtil.RegisterNavigationGroup(self._wtQuestObjectiveBox, self, "Hittest")
    end
    if not self._wtNavGroup4 then
        self._wtNavGroup4 = WidgetUtil.RegisterNavigationGroup(self._wtQuestDetailPanel._wtGiveUpQuestBtn, self, "Hittest")
    end
    if self._wtNavGroup1 then
        self._wtNavGroup1:AddNavWidgetToArray(self._wtCustomWaterFallList)
        self._wtNavGroup1:SetScrollRecipient(self._wtCustomWaterFallList)
        self._wtNavGroup1:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        self._wtNavGroup1:MarkIsStackControlGroup()

        -- local navGroup1Strategy = self._wtNavGroup1:GetOwnerNavStrategy()
        -- if navGroup1Strategy then
        --     navGroup1Strategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocused)
        -- end

        WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup1, self._WaterfallListBoundaryRule, self)
    end

    if self._wtNavGroup2 then
        self._wtNavGroup2:AddNavWidgetToArray(self._wtDFScrollBox_151)
        WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup2, self._ReturnLeftUpBoundaryRule, self)
    end

    if self._wtNavGroup3 then
        -- self._wtNavGroup3:AddNavWidgetToArray(self._wtQuestObjectiveBox)
        -- self._wtNavGroup3:SetScrollRecipient(self._wtQuestObjectiveBox)
        self._wtNavGroup3:SetNavSelectorWidgetVisibility(true)
        WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup3, self._ReturnLeftBoundaryRule, self)
    end

    if self._wtNavGroup4 then
        self._wtNavGroup4:AddNavWidgetToArray(self._wtQuestDetailPanel._wtGiveUpQuestBtn)
        WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup4, self._ReturnLeftDownBoundaryRule, self)
    end
    WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup1)

    -- if  self._wtDropDownBox then
    --     self._wtDropDownBox:Event("OnCheckButtonStateChanged", self.OnAccordionCheckButtonStateChanged, self)
    -- end

    self:_InitShortcuts()
end

function QuestDetailView:_DisableGamepadFeature()
    WidgetUtil.RemoveNavigationGroup(self)
    self._wtNavGroup1 = nil
    self._wtNavGroup2 = nil
    self._wtNavGroup3 = nil
    self._wtNavGroup4 = nil

    self:_RemoveShortcuts()

    self._defaultGoto = nil

end

function QuestDetailView:_WaterfallListBoundaryRule(direction)
    if direction == EUINavigation.Right then
        if self._defaultGoto then
            return self._defaultGoto
        else
            return self._wtRewardScrollBox:GetItemByIndex(1)
        end
    elseif direction == EUINavigation.Up then
        local lastIdx = #self._questInfos
        self._wtCustomWaterFallList:ScrollToIndex(lastIdx)
        local item = self._wtCustomWaterFallList:GetItemByIndex(lastIdx)
        return item
    elseif direction == EUINavigation.Down then
        self._wtCustomWaterFallList:ScrollToIndex(1)
        local item = self._wtCustomWaterFallList:GetItemByIndex(1)
        return item
    else
        return nil
    end
end

function QuestDetailView:_ReturnLeftBoundaryRule(direction)
    if direction == EUINavigation.Left then
        return self._selectedItem
    else
        return nil
    end
end

function QuestDetailView:_ReturnLeftUpBoundaryRule(direction)
    if direction == EUINavigation.Left then
        return self._selectedItem
    elseif direction == EUINavigation.Up then
        return self._defaultGoto
    else
        return nil
    end
end

function QuestDetailView:_ReturnLeftDownBoundaryRule(direction)
    if direction == EUINavigation.Left then
        return self._selectedItem
    elseif direction == EUINavigation.Down then
        return self._defaultGoto
    else
        return nil
    end
end

function QuestDetailView:_OnUpdateNavGroup3(clickable)
    if self._wtNavGroup3 then
        self._wtNavGroup3:AddNavWidgetToArray(clickable)
        if self._defaultGoto == nil then
            self._defaultGoto = clickable
        end
    end
end

function QuestDetailView:_InitShortcuts()
    -- 打开任务总览
    if self._PressAllQuestButton == nil then 
        self._PressAllQuestButton = self:AddInputActionBinding("QuestDetail_OpenAllQuest", EInputEvent.IE_Pressed, self._OnClickAllQuestBtn,self, EDisplayInputActionPriority.UI_Stack)
        self._wtAllQuestBtn:SetDisplayInputAction("QuestDetail_OpenAllQuest", true, nil, true)
    end

	-- 行为按钮（领取任务/奖励/提交/重新开始）
    if self._PressActionButton == nil then
        self._PressActionButton = self:AddInputActionBinding("QuestDetail_Action", EInputEvent.IE_Pressed, self._OnClickActionBtn,self, EDisplayInputActionPriority.UI_Stack)
        self._wtActionBtn:SetDisplayInputAction("QuestDetail_Action", true, nil, true)
    end

    local summaryList = {}
    table.insert(summaryList, {actionName = "Confirm",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, false)

end

function QuestDetailView:_RemoveShortcuts()
    if self._PressAllQuestButton then
        self:RemoveInputActionBinding(self._PressAllQuestButton)
        self._PressAllQuestButton= nil
    end
	if self._PressActionButton then
        self:RemoveInputActionBinding(self._PressActionButton)
        self._PressActionButton= nil
    end
    -- if self._closeDropDownHandler then
    --     self:RemoveInputActionBinding(self._closeDropDownHandler)
    --     self._closeDropDownHandler= nil
    -- end
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function QuestDetailView:_CloseDropDown()
    if self._wtDropDownBox then
        self._wtDropDownBox:ManuelSetCheckButtonState(ECheckButtonState.Unchecked)
    end
end

-- function QuestDetailView:_RegisterDropDownNavGroup()
--     if not self._DropDownNavGroup then
--         if self._wtDFCommonDropDownBox and self._wtDFCommonDropDownBox.ScrollGridBox then
--             self._DropDownNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtDFCommonDropDownBox.ScrollGridBox, self._wtDFCommonDropDownBox, "Hittest")
--         end
 
--         if self._DropDownNavGroup then
--             self._DropDownNavGroup:AddNavWidgetToArray(self._wtDFCommonDropDownBox.ScrollGridBox)
--             WidgetUtil.TryFocusDefaultWidgetByGroup(self._DropDownNavGroup)
--         end
--     end
-- end

-- function QuestDetailView:_InitDropDownShortCuts()
--     if not self._closeDropDownHandler then
--         self._closeDropDownHandler = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
--     end
-- end

-- function QuestDetailView:_RemoveDropDownNavGroup()
--     if self._DropDownNavGroup then
--         self._DropDownNavGroup = nil
--     end
--     WidgetUtil.RemoveNavigationGroup(self._wtDFCommonDropDownBox)
-- end

-- function QuestDetailView:_RemoveDropDownShortCusts()
--     if self._closeDropDownHandler then
--         self:RemoveInputActionBinding(self._closeDropDownHandler)
--         self._closeDropDownHandler= nil
--     end
-- end

-- function QuestDetailView:OnAccordionCheckButtonStateChanged(eCheckButtonState)
--     if eCheckButtonState == ECheckButtonState.UncheckedPressed then
--         self:_RegisterDropDownNavGroup()
--         self:_InitDropDownShortCuts()
--     elseif eCheckButtonState == ECheckButtonState.Unchecked then
--         if self._DropDownNavGroup then
--             self:_RemoveDropDownNavGroup()
--             self:_RemoveDropDownShortCusts()
--             WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup1)
--         end
--     end
-- end
-- END MODIFICATION

------------------------------------ Public function ------------------------------------
return QuestDetailView
