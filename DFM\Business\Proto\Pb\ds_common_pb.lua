require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.ds_common_editor_pb"
end

AIEnvMode = {
AIEnvExp = 0,
AIEnvModeTrain = 1,
AIEnvDebug = 2,
}
HeroArmedPropFashionQuality = {
Quality_None = 0,
Quality_S = 1,
Quality_A = 2,
Quality_B = 3,
Quality_C = 4,
Quality_D = 5,
}
MatchWarmType = {
Non_Warm = 0,
Normal_Warm = 1,
Ailab_Warm = 2,
}
TDMMatchSubType = {
DefaultMatch = 0,
NormalMatch = 1,
RankMatch = 2,
TournamentMatch = 3,
}
TournamentRankShieldType = {
RankShieldType_None = 0,
RankShieldType_Rank = 1,
RankShieldType_Score = 2,
RankShieldType_Act = 3,
RankShieldType_Card = 4,
}
TournamentRankDoubleReason = {
RankDoubleReason_None = 0,
RankDoubleReason_Act = 1,
RankDoubleReason_Card = 2,
}
ESettlementNtf = {
SNTF_NONE = 0,
SNTF_RAID = 1,
SNTF_TDM = 2,
SNTF_SOL = 3,
SNTF_BHD = 4,
SNTF_Arena = 5,
}
DSType = {
DSType_None = 0,
DSType_Any = 1,
DSType_SingleRoom = 2,
DSType_MultiRoom = 3,
DSType_Bhd = 4,
DSType_Max = 5,
}
EGspPlayerResultType = {
kGspPlayerResultEscaped = 0,
kGspPlayerResultKilled = 1,
kGspPlayerResultMissing = 2,
kGspPlayerResultQuit = 3,
kGspPlayerResultGiveUp = 4,
kGspPlayerResultUndetermined = 99,
}
MatchGameMode = {
InvalidGameMode = 0,
WorldGameMode = 1,
TDMGameMode = 2,
SafehouseGameMode = 3,
BlackHawkDown = 5,
}
MatchGameRule = {
InvalidGameRule = 0,
SafehouseGameRule = 1,
DiscoveryGameRule = 2,
PVERaidGameRule = 3,
SOLGameRule = 4,
RaidGameRule = 5,
TDMClassGameRule = 6,
TDMODGameRule = 7,
TDMConquestGameRule = 8,
TDMBlitzGameRule = 9,
TDMActivityGameRule = 10,
TDMHumanMachineGameRule = 11,
ArenaGameRule = 12,
TDMCommanderGameRule = 13,
BHDLevel1 = 91,
BHDLevel2 = 92,
BHDLevel3 = 93,
BHDLevel4 = 94,
BHDLevel5 = 95,
BHDLevel6 = 96,
BHDLevel7 = 97,
}
MatchGameType = {
InvalidGameType = 0,
SOLGameType = 1,
MPGameType = 2,
RaidGameType = 3,
}
MatchSubMode = {
InvalidSubMode = 0,
SOLPMC = 10,
HighYieldSOLPMC = 11,
ExtremeHighYieldSOLPMC = 12,
SOLNight = 13,
HighYieldSOLNight = 14,
ExtremeHighYieldSOLNight = 15,
VeryHighYieldSOLNight = 16,
SOLSCAV = 20,
RaidSimple = 21,
RaidNormal = 22,
RaidHard = 23,
TDMSimple = 41,
TDMNormal = 42,
TDMHard = 51,
TDMSiege = 61,
TDMQuickDefense = 62,
TDMTideOfIron = 63,
TDMFlagBattle = 64,
TDMLimitHero = 65,
TDMReserve1 = 66,
TDMReserve2 = 67,
TDMReserve3 = 68,
TDMReserve4 = 69,
TDMReserve5 = 70,
TDMReserve6 = 71,
TDMReserve7 = 72,
TDMReserve8 = 73,
TDMReserve9 = 74,
TDMReserve10 = 75,
TDMReserve11 = 76,
TDMReserve12 = 77,
TDMReserve13 = 78,
TDMReserve14 = 79,
TDMReserve15 = 80,
}
MatchTeamMode = {
TeamModeNone = 0,
TeamModeSolo = 1,
TeamModeDouble = 2,
TeamModeThreeMember = 3,
TeamModeFourMember = 4,
TeamModeTwentyMember = 7,
}
MysticalSourceType = {
MysticalSysGenerated = 0,
MysticalSysReissue = 1,
MysticalCGenerated = 2,
}
MysticalSkinRarityType = {
RarityNone = 0,
RarityHigh = 1,
RarityRara = 2,
RarityPremium = 3,
RarityExtraordinary = 4,
}
MysticalPendantShowType = {
M_P_ShowTypeNone = 0,
M_P_ShowTypeMaterial = 1,
M_P_ShowTypeEffect = 2,
M_P_ShowTypePattern = 3,
}
MysticalPendantRarityType = {
M_P_RarityNone = 0,
M_P_RarityHigh = 1,
M_P_RarityRara = 2,
M_P_RarityPremium = 3,
M_P_RarityExtraordinary = 4,
}
MysticalPendantSourceType = {
M_P_None = 0,
M_P_Open = 1,
}
ObjectiveSource = {
ObjectiveSource_INVALID = 0,
ObjectiveSource_Activity = 1,
ObjectiveSource_BattlePass = 2,
ObjectiveSource_Hero = 3,
ObjectiveSource_Achievement = 4,
ObjectiveSource_Collection = 5,
ObjectiveSource_MPDeposit = 6,
ObjectiveSource_Quest = 7,
}
TaskGoalType = {
CommonTaskGoal = 0,
HeroGrowLineTaskGoal = 1,
HeroRecruitmentTaskGoal = 2,
}
PropSourceType = {
PropSourceType_Default = 0,
PropSourceType_Other = 1,
PropSourceType_SceneBox = 2,
PropSourceType_SubPickup = 3,
PropSourceType_LabAI = 4,
PropSourceType_BotAI = 5,
PropSourceType_Player = 6,
PropSourceType_Quest = 7,
PropSourceType_HackPC = 8,
PropSourceType_DecipherDevice = 9,
PropSourceType_RaidAmmoBox = 10,
}
PropRecordType = {
PropRecordNone = 0,
PropRecordPlayer = 1,
PropRecordAI = 2,
PropRecordBoss = 3,
PropRecordScav = 4,
}
ActivityPropMode = {
ActivityPropModeOther = 0,
ActivityPropModeMP = 1,
}
TDMAddVehicleExpReason = {
EXP_None = 0,
EXP_KillPlayer = 1,
EXP_DestroyVehicle = 2,
EXP_AssistPlayer = 3,
EXP_AssistVehicle = 4,
EXP_AccumulateDuration = 5,
}
TDMCampColor = {
CampColorInvalid = 0,
CampColorBlue = 1,
CampColorRed = 2,
CampRobot = 106,
}
EOutStandingContributionType = {
EOutStandingContributionType_None = 0,
EOutStandingContributionType_Commander = 1,
EOutStandingContributionType_Kill = 2,
EOutStandingContributionType_Piot = 3,
EOutStandingContributionType_Armor = 4,
EOutStandingContributionType_VehicleKiller = 5,
EOutStandingContributionType_Doctor = 6,
EOutStandingContributionType_Capture = 7,
EOutStandingContributionType_Tactical = 8,
}
VehicleSlotID = {
VS_None = 0,
VS_Main_Weapon = 1,
VS_Secondary_Weapon = 2,
VS_Equipment_Slot1 = 3,
VS_Equipment_Slot2 = 4,
VS_Weapon_Station = 5,
VS_Weapon_Bay = 6,
VS_Commander_Position = 7,
VS_Equipment_Slot3 = 8,
VS_Weapon_Station_2_Postion_1 = 9,
VS_Weapon_Station_2_Postion_2 = 10,
VS_Commander_Position_2 = 11,
VS_Weapon_Station_Secondary_Weapon = 12,
}
EWeaponExpReason = {
EWeaponExpReason_None = 0,
EWeaponExpReason_SOLUseTime = 1,
EWeaponExpReason_SOLKillPlayer = 2,
EWeaponExpReason_SOLAssistsPlayer = 3,
EWeaponExpReason_SOLKillAI = 4,
EWeaponExpReason_SOLAssistsAI = 5,
EWeaponExpReason_MPUseTime = 11,
EWeaponExpReason_MPKillPlayer = 12,
EWeaponExpReason_MPAssistsPlayer = 13,
EWeaponExpReason_RaidUseTime = 21,
}
WorldActorState = {
ActorNone = 0,
ActorLocked = 1,
ActorVisitable = 2,
}
WorldStrongholdState = {
StrongholdLocked = 0,
StrongholdInProgress = 1,
StrongholdOccupied = 2,
}
eHeroFashionPosition = {
FashionSuit = 0,
FashionHead = 1,
FashionBag = 9,
}
BhdMissionRating = {
BhdMissionRatingFailed = 0,
BhdMissionRatingSucc_Outstanding = 1,
BhdMissionRatingSucc_Excellent = 2,
BhdMissionRatingSucc_Good = 3,
BhdMissionRatingSucc_Average = 4,
BhdMissionRatingSucc_Poor = 5,
}
ArenaResult = {
AR_None = 0,
AR_Win = 1,
AR_Loss = 2,
AR_Draw = 3,
}
ArenaRoundResult = {
RR_None = 0,
RR_Wipe_Out = 1,
RR_Upload_mantle_brick = 2,
RR_Timeout = 3,
}
ReportEntrance = {
ReportNone = 0,
ReportMeteriral = 1,
ReportSettlementPVP = 2,
ReportSettlementPVE = 3,
ReportDynamic = 5,
ReportComment = 6,
ReportMessage = 7,
ReportZoneInfo = 8,
ReportBattleRecord = 10,
}
pb.__pb_AILabSDKInfo = {
    enable = false,
    sync_transport_mode = 0,
    sync_frame_state_span_ms = 0,
    mode = 0,
    business_id = 0,
}
pb.__pb_AILabSDKInfo.__name = "AILabSDKInfo"
pb.__pb_AILabSDKInfo.__index = pb.__pb_AILabSDKInfo
pb.__pb_AILabSDKInfo.__pairs = __pb_pairs

pb.AILabSDKInfo = { __name = "AILabSDKInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabSDKInfo : ProtoBase
---@field public enable boolean
---@field public sync_transport_mode number
---@field public route_info pb_AILabSDKRouteInfo
---@field public sync_frame_state_span_ms number
---@field public mode number
---@field public args pb_AIRoomArg
---@field public business_id number

---@return pb_AILabSDKInfo
function pb.AILabSDKInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabSDKRouteInfo = {
    router_mode = 0,
    downstream_ip = "",
    downstream_port = 0,
    downstream_cl5_mod_id = 0,
    downstream_cl5_cmd_id = 0,
    downstream_polaris_name = "",
}
pb.__pb_AILabSDKRouteInfo.__name = "AILabSDKRouteInfo"
pb.__pb_AILabSDKRouteInfo.__index = pb.__pb_AILabSDKRouteInfo
pb.__pb_AILabSDKRouteInfo.__pairs = __pb_pairs

pb.AILabSDKRouteInfo = { __name = "AILabSDKRouteInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabSDKRouteInfo : ProtoBase
---@field public router_mode number
---@field public downstream_ip string
---@field public downstream_port number
---@field public downstream_cl5_mod_id number
---@field public downstream_cl5_cmd_id number
---@field public downstream_polaris_name string

---@return pb_AILabSDKRouteInfo
function pb.AILabSDKRouteInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIRoomArg = {
    min_deliver_distance = 0,
    max_deliver_distance = 0,
    max_nav_distance_gap = 0,
}
pb.__pb_AIRoomArg.__name = "AIRoomArg"
pb.__pb_AIRoomArg.__index = pb.__pb_AIRoomArg
pb.__pb_AIRoomArg.__pairs = __pb_pairs

pb.AIRoomArg = { __name = "AIRoomArg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIRoomArg : ProtoBase
---@field public min_deliver_distance number
---@field public max_deliver_distance number
---@field public max_nav_distance_gap number

---@return pb_AIRoomArg
function pb.AIRoomArg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedForceNumeralData = {
    armedforce_id = 0,
}
pb.__pb_ArmedForceNumeralData.__name = "ArmedForceNumeralData"
pb.__pb_ArmedForceNumeralData.__index = pb.__pb_ArmedForceNumeralData
pb.__pb_ArmedForceNumeralData.__pairs = __pb_pairs

pb.ArmedForceNumeralData = { __name = "ArmedForceNumeralData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedForceNumeralData : ProtoBase
---@field public armedforce_id number
---@field public talent pb_ArmedForceTalent

---@return pb_ArmedForceNumeralData
function pb.ArmedForceNumeralData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedForceTalent = {
    talent_id = 0,
    active_skill_id = 0,
    passive_skill_id = 0,
}
pb.__pb_ArmedForceTalent.__name = "ArmedForceTalent"
pb.__pb_ArmedForceTalent.__index = pb.__pb_ArmedForceTalent
pb.__pb_ArmedForceTalent.__pairs = __pb_pairs

pb.ArmedForceTalent = { __name = "ArmedForceTalent", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedForceTalent : ProtoBase
---@field public talent_id number
---@field public active_skill_id number
---@field public passive_skill_id number

---@return pb_ArmedForceTalent
function pb.ArmedForceTalent:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AttrBuff = {
    type = 0,
    source_id = 0,
    source_gid = 0,
    start_time = 0,
    duration = 0,
    body_part = 0,
}
pb.__pb_AttrBuff.__name = "AttrBuff"
pb.__pb_AttrBuff.__index = pb.__pb_AttrBuff
pb.__pb_AttrBuff.__pairs = __pb_pairs

pb.AttrBuff = { __name = "AttrBuff", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AttrBuff : ProtoBase
---@field public type number
---@field public source_id number
---@field public source_gid number
---@field public start_time number
---@field public duration number
---@field public body_part number

---@return pb_AttrBuff
function pb.AttrBuff:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CombatRoleAbility = {
    ability_id = 0,
    game_mode = 0,
}
pb.__pb_CombatRoleAbility.__name = "CombatRoleAbility"
pb.__pb_CombatRoleAbility.__index = pb.__pb_CombatRoleAbility
pb.__pb_CombatRoleAbility.__pairs = __pb_pairs

pb.CombatRoleAbility = { __name = "CombatRoleAbility", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CombatRoleAbility : ProtoBase
---@field public ability_id number
---@field public game_mode number
---@field public special_items pb_CombatRoleSpecialItem[]

---@return pb_CombatRoleAbility
function pb.CombatRoleAbility:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CombatRoleSpecialItem = {
    id = 0,
    num = 0,
    pos = 0,
}
pb.__pb_CombatRoleSpecialItem.__name = "CombatRoleSpecialItem"
pb.__pb_CombatRoleSpecialItem.__index = pb.__pb_CombatRoleSpecialItem
pb.__pb_CombatRoleSpecialItem.__pairs = __pb_pairs

pb.CombatRoleSpecialItem = { __name = "CombatRoleSpecialItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CombatRoleSpecialItem : ProtoBase
---@field public id number
---@field public num number
---@field public pos number

---@return pb_CombatRoleSpecialItem
function pb.CombatRoleSpecialItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Component = {
    slot = 0,
}
pb.__pb_Component.__name = "Component"
pb.__pb_Component.__index = pb.__pb_Component
pb.__pb_Component.__pairs = __pb_pairs

pb.Component = { __name = "Component", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Component : ProtoBase
---@field public slot number
---@field public prop_data pb_PropInfo

---@return pb_Component
function pb.Component:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSAttrValue = {
    type = 0,
    value = 0,
}
pb.__pb_DSAttrValue.__name = "DSAttrValue"
pb.__pb_DSAttrValue.__index = pb.__pb_DSAttrValue
pb.__pb_DSAttrValue.__pairs = __pb_pairs

pb.DSAttrValue = { __name = "DSAttrValue", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSAttrValue : ProtoBase
---@field public type number
---@field public value number

---@return pb_DSAttrValue
function pb.DSAttrValue:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroArmedPropFashion = {
    fashion_id = 0,
    quality = 0,
}
pb.__pb_HeroArmedPropFashion.__name = "HeroArmedPropFashion"
pb.__pb_HeroArmedPropFashion.__index = pb.__pb_HeroArmedPropFashion
pb.__pb_HeroArmedPropFashion.__pairs = __pb_pairs

pb.HeroArmedPropFashion = { __name = "HeroArmedPropFashion", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroArmedPropFashion : ProtoBase
---@field public fashion_id number
---@field public quality number

---@return pb_HeroArmedPropFashion
function pb.HeroArmedPropFashion:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroArmedPropFashionInfo = {
    armed_prop_id = 0,
}
pb.__pb_HeroArmedPropFashionInfo.__name = "HeroArmedPropFashionInfo"
pb.__pb_HeroArmedPropFashionInfo.__index = pb.__pb_HeroArmedPropFashionInfo
pb.__pb_HeroArmedPropFashionInfo.__pairs = __pb_pairs

pb.HeroArmedPropFashionInfo = { __name = "HeroArmedPropFashionInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroArmedPropFashionInfo : ProtoBase
---@field public armed_prop_id number
---@field public fashion pb_HeroArmedPropFashion

---@return pb_HeroArmedPropFashionInfo
function pb.HeroArmedPropFashionInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSHero = {
    hero_id = 0,
    armed_force_id = 0,
    fashion_teammate_visible = false,
    fashion_enemy_visible = false,
    fashion_safehouse_visible = false,
}
pb.__pb_DSHero.__name = "DSHero"
pb.__pb_DSHero.__index = pb.__pb_DSHero
pb.__pb_DSHero.__pairs = __pb_pairs

pb.DSHero = { __name = "DSHero", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSHero : ProtoBase
---@field public hero_id number
---@field public armed_force_id number
---@field public skill pb_ExpertSkill
---@field public armed_prop1 pb_PropInfo
---@field public armed_prop2 pb_PropInfo
---@field public hero_body pb_PropInfo
---@field public fashion pb_HeroFashion[]
---@field public accessories pb_HeroAccessoryItem[]
---@field public fashion_teammate_visible boolean
---@field public fashion_enemy_visible boolean
---@field public fashion_safehouse_visible boolean
---@field public armed_prop_fashion_info pb_HeroArmedPropFashionInfo[]

---@return pb_DSHero
function pb.DSHero:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSQuestDesc = {
    quest_id = 0,
    quest_type = 0,
    should_auto_accept = false,
    giver_id = "",
    ender_id = "",
    quest_path = "",
    accept_required_level = 0,
}
pb.__pb_DSQuestDesc.__name = "DSQuestDesc"
pb.__pb_DSQuestDesc.__index = pb.__pb_DSQuestDesc
pb.__pb_DSQuestDesc.__pairs = __pb_pairs

pb.DSQuestDesc = { __name = "DSQuestDesc", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSQuestDesc : ProtoBase
---@field public quest_id number
---@field public quest_type number
---@field public should_auto_accept boolean
---@field public giver_id string
---@field public ender_id string
---@field public quest_path string
---@field public accept_required_level number
---@field public objective_list pb_DSQuestObjectiveDesc[]

---@return pb_DSQuestDesc
function pb.DSQuestDesc:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSQuestObjectiveDesc = {
    quest_objective_id = 0,
    should_must_be_completed = false,
    type = 0,
    param1 = "",
    param2 = "",
    required_count = 0,
}
pb.__pb_DSQuestObjectiveDesc.__name = "DSQuestObjectiveDesc"
pb.__pb_DSQuestObjectiveDesc.__index = pb.__pb_DSQuestObjectiveDesc
pb.__pb_DSQuestObjectiveDesc.__pairs = __pb_pairs

pb.DSQuestObjectiveDesc = { __name = "DSQuestObjectiveDesc", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSQuestObjectiveDesc : ProtoBase
---@field public quest_objective_id number
---@field public should_must_be_completed boolean
---@field public type number
---@field public param1 string
---@field public param2 string
---@field public list_param number[]
---@field public required_count number

---@return pb_DSQuestObjectiveDesc
function pb.DSQuestObjectiveDesc:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RaidKillEnemyInfo = {
    raid_enemy_type = "",
    kill_enemy_count = 0,
}
pb.__pb_RaidKillEnemyInfo.__name = "RaidKillEnemyInfo"
pb.__pb_RaidKillEnemyInfo.__index = pb.__pb_RaidKillEnemyInfo
pb.__pb_RaidKillEnemyInfo.__pairs = __pb_pairs

pb.RaidKillEnemyInfo = { __name = "RaidKillEnemyInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RaidKillEnemyInfo : ProtoBase
---@field public raid_enemy_type string
---@field public kill_enemy_count number

---@return pb_RaidKillEnemyInfo
function pb.RaidKillEnemyInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsRaidPlayerGameStatus = {
    player_id = 0,
    passed_life = 0,
    leave = false,
    difficulty = 0,
    fail_quest = 0,
    quit_quest = 0,
    play_time = 0,
    passed_time = 0,
    total_damage = 0,
    damage_to_vehicles = 0,
    total_kill_count = 0,
    special_ai_kill_count = 0,
    normal_ai_kill_count = 0,
    death_count = 0,
    rescue_count = 0,
    heal_value = 0,
    raid_score = 0,
    kill_vehicle_count = 0,
    helicopter_kill = false,
    armored_car_full_blood = false,
    carry_in_props_price = 0,
}
pb.__pb_DsRaidPlayerGameStatus.__name = "DsRaidPlayerGameStatus"
pb.__pb_DsRaidPlayerGameStatus.__index = pb.__pb_DsRaidPlayerGameStatus
pb.__pb_DsRaidPlayerGameStatus.__pairs = __pb_pairs

pb.DsRaidPlayerGameStatus = { __name = "DsRaidPlayerGameStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsRaidPlayerGameStatus : ProtoBase
---@field public player_id number
---@field public passed_life number
---@field public leave boolean
---@field public difficulty number
---@field public fail_quest number
---@field public quit_quest number
---@field public play_time number
---@field public passed_time number
---@field public total_damage number
---@field public damage_to_vehicles number
---@field public total_kill_count number
---@field public special_ai_kill_count number
---@field public normal_ai_kill_count number
---@field public death_count number
---@field public rescue_count number
---@field public heal_value number
---@field public raid_score number
---@field public kill_vehicle_count number
---@field public helicopter_kill boolean
---@field public armored_car_full_blood boolean
---@field public kill_enemy_list pb_RaidKillEnemyInfo[]
---@field public carry_in_props_price number

---@return pb_DsRaidPlayerGameStatus
function pb.DsRaidPlayerGameStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomAttr = {
    WarmType = 0,
    SchemePoolID = 0,
    GroupID = 0,
    match_sub_type = 0,
    dynamic_skill_level = 0,
    dynamic_ratio_low = 0,
    dynamic_ratio_high = 0,
    tdm_device_type = 0,
    max_camp_real_player_num = 0,
    max_player_num = 0,
    ailab_conf_id = 0,
    is_victory_unite_match = false,
}
pb.__pb_RoomAttr.__name = "RoomAttr"
pb.__pb_RoomAttr.__index = pb.__pb_RoomAttr
pb.__pb_RoomAttr.__pairs = __pb_pairs

pb.RoomAttr = { __name = "RoomAttr", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomAttr : ProtoBase
---@field public WarmType number
---@field public SchemePoolID number
---@field public GroupID number
---@field public match_sub_type number
---@field public dynamic_skill_level number
---@field public dynamic_ratio_low number
---@field public dynamic_ratio_high number
---@field public tdm_device_type number
---@field public max_camp_real_player_num number
---@field public max_player_num number
---@field public ailab_conf_id number
---@field public is_victory_unite_match boolean

---@return pb_RoomAttr
function pb.RoomAttr:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMCamp = {
    color = 0,
    is_winner = 0,
    score = 0,
    attacker = false,
    occupied_stronghold = 0,
    fallen_stronghold = 0,
    total_score = 0,
    capture_flag_score = 0,
}
pb.__pb_TDMCamp.__name = "TDMCamp"
pb.__pb_TDMCamp.__index = pb.__pb_TDMCamp
pb.__pb_TDMCamp.__pairs = __pb_pairs

pb.TDMCamp = { __name = "TDMCamp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMCamp : ProtoBase
---@field public color number
---@field public is_winner number
---@field public score number
---@field public attacker boolean
---@field public occupied_stronghold number
---@field public fallen_stronghold number
---@field public team_list pb_TDMTeam[]
---@field public total_score number
---@field public capture_flag_score number
---@field public od_camp pb_ODTdmCamp

---@return pb_TDMCamp
function pb.TDMCamp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSettlementAccountExp = {
    exp = 0,
    victorDefeatExp = 0,
    compensation = 0,
    factor = 0,
    game_exp = 0,
    medal_exp = 0,
    result_exp = 0,
    raw_total_exp = 0,
}
pb.__pb_TDMSettlementAccountExp.__name = "TDMSettlementAccountExp"
pb.__pb_TDMSettlementAccountExp.__index = pb.__pb_TDMSettlementAccountExp
pb.__pb_TDMSettlementAccountExp.__pairs = __pb_pairs

pb.TDMSettlementAccountExp = { __name = "TDMSettlementAccountExp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSettlementAccountExp : ProtoBase
---@field public exp number
---@field public victorDefeatExp number
---@field public compensation number
---@field public factor number
---@field public game_exp number
---@field public medal_exp number
---@field public result_exp number
---@field public raw_total_exp number

---@return pb_TDMSettlementAccountExp
function pb.TDMSettlementAccountExp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TournamentRankShield = {
    id = 0,
    rank_id = 0,
    used_shied = 0,
    total_shied = 0,
    score = 0,
    shield_type = 0,
    left_shield = 0,
}
pb.__pb_TournamentRankShield.__name = "TournamentRankShield"
pb.__pb_TournamentRankShield.__index = pb.__pb_TournamentRankShield
pb.__pb_TournamentRankShield.__pairs = __pb_pairs

pb.TournamentRankShield = { __name = "TournamentRankShield", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TournamentRankShield : ProtoBase
---@field public id number
---@field public rank_id number
---@field public used_shied number
---@field public total_shied number
---@field public score number
---@field public shield_type number
---@field public left_shield number

---@return pb_TournamentRankShield
function pb.TournamentRankShield:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TournamentRankDoubleInfo = {
    reason = 0,
    used_num = 0,
    total_num = 0,
    left_num = 0,
    double_rate = 0,
}
pb.__pb_TournamentRankDoubleInfo.__name = "TournamentRankDoubleInfo"
pb.__pb_TournamentRankDoubleInfo.__index = pb.__pb_TournamentRankDoubleInfo
pb.__pb_TournamentRankDoubleInfo.__pairs = __pb_pairs

pb.TournamentRankDoubleInfo = { __name = "TournamentRankDoubleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TournamentRankDoubleInfo : ProtoBase
---@field public reason number
---@field public used_num number
---@field public total_num number
---@field public left_num number
---@field public double_rate number

---@return pb_TournamentRankDoubleInfo
function pb.TournamentRankDoubleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TdmCommanderScore = {
    attack_defeat_score = 0,
    attack_vic_score = 0,
    defence_vic_score = 0,
    defence_defeat_score = 0,
    attack_stage_score = 0,
    defence_stage_score = 0,
    camp_compestion_score = 0,
    camp_match_score = 0,
    join_at_middle_score = 0,
    short_hangup_trigger_score = 0,
    contributor_title_score = 0,
    passive_combat_rate = 0,
    leave_score = 0,
    total_score = 0,
    weak_camp_score = 0,
    passive_combat_score = 0,
    conquest_vic_score = 0,
    conquest_defeat_score = 0,
    conquest_progress_score = 0,
}
pb.__pb_TdmCommanderScore.__name = "TdmCommanderScore"
pb.__pb_TdmCommanderScore.__index = pb.__pb_TdmCommanderScore
pb.__pb_TdmCommanderScore.__pairs = __pb_pairs

pb.TdmCommanderScore = { __name = "TdmCommanderScore", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TdmCommanderScore : ProtoBase
---@field public attack_defeat_score number
---@field public attack_vic_score number
---@field public defence_vic_score number
---@field public defence_defeat_score number
---@field public attack_stage_score number
---@field public defence_stage_score number
---@field public camp_compestion_score number
---@field public camp_match_score number
---@field public join_at_middle_score number
---@field public short_hangup_trigger_score number
---@field public contributor_title_score number
---@field public passive_combat_rate number
---@field public leave_score number
---@field public total_score number
---@field public weak_camp_score number
---@field public passive_combat_score number
---@field public conquest_vic_score number
---@field public conquest_defeat_score number
---@field public conquest_progress_score number

---@return pb_TdmCommanderScore
function pb.TdmCommanderScore:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSettlementData = {
    my_color = 0,
    game_time = 0,
    drop_mantel_brick_id = 0,
    is_ranked_match = false,
    rank_match_score = 0,
    ranked_score_shoot = 0,
    ranked_score_tactics = 0,
    ranked_score_vehicle = 0,
    ranked_score_shoot_delta = 0,
    ranked_score_tactics_delta = 0,
    ranked_score_vehicle_delta = 0,
    ranked_score_leave_penalty = 0,
    ranked_score_half_join_score = 0,
    ranked_score_rank_score = 0,
    time_factor = 0,
    raw_total_ranked_score = 0,
    real_total_ranked_score = 0,
    self_ranking = 0,
    rank_score_spm = 0,
    rank_score_spm_score = 0,
    rank_score_result_score = 0,
    rank_score_defeated_extra_score = 0,
    rank_score_camps_gap_extra_score = 0,
    rank_score_origin_spm_point = 0,
    rank_score_my_camp_point = 0,
    rank_score_enemy_camp_point = 0,
    rank_score_reputation_extra_score = 0,
    rank_score_discount_rate = 0,
    rank_score_discount_ceiling = 0,
    orgin_commander_score = 0,
    current_serial_number = 0,
    is_victory_unite_match = false,
    use_vehicle = false,
}
pb.__pb_TDMSettlementData.__name = "TDMSettlementData"
pb.__pb_TDMSettlementData.__index = pb.__pb_TDMSettlementData
pb.__pb_TDMSettlementData.__pairs = __pb_pairs

pb.TDMSettlementData = { __name = "TDMSettlementData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSettlementData : ProtoBase
---@field public camp_list pb_TDMCamp[]
---@field public my_color number
---@field public mvp_list pb_TDMMvp[]
---@field public weapon_change pb_WeaponChange[]
---@field public game_time number
---@field public account_exp pb_TDMSettlementAccountExp
---@field public vehicle_exp_list pb_TDMVehicleAddExp[]
---@field public weapon_exp_list pb_WeaponExpAdd[]
---@field public drop_mantel_brick_id number
---@field public mystical_skins pb_PropInfo[]
---@field public is_ranked_match boolean
---@field public rank_match_score number
---@field public ranked_score_shoot number
---@field public ranked_score_tactics number
---@field public ranked_score_vehicle number
---@field public ranked_score_shoot_delta number
---@field public ranked_score_tactics_delta number
---@field public ranked_score_vehicle_delta number
---@field public rank_shields pb_TournamentRankShield[]
---@field public ranked_score_leave_penalty number
---@field public ranked_score_half_join_score number
---@field public ranked_score_rank_score number
---@field public time_factor number
---@field public raw_total_ranked_score number
---@field public real_total_ranked_score number
---@field public self_ranking number
---@field public rank_score_spm number
---@field public rank_score_spm_score number
---@field public rank_score_result_score number
---@field public rank_score_defeated_extra_score number
---@field public rank_score_camps_gap_extra_score number
---@field public rank_score_origin_spm_point number
---@field public rank_score_my_camp_point number
---@field public rank_score_enemy_camp_point number
---@field public rank_score_reputation_extra_score number
---@field public rank_score_discount_rate number
---@field public rank_score_discount_ceiling number
---@field public vehicles pb_Vehicle[]
---@field public tdm_point_weight_list pb_TDMPointWeight[]
---@field public commander_score pb_TdmCommanderScore
---@field public orgin_commander_score number
---@field public achive_tasks pb_DsGameAchievementTask[]
---@field public current_serial_number number
---@field public mp_weapon_statistics pb_MPWeaponStatistics[]
---@field public is_victory_unite_match boolean
---@field public use_vehicle boolean

---@return pb_TDMSettlementData
function pb.TDMSettlementData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMTeam = {
    team_id = 0,
    team_rank = 0,
    team_leader = 0,
}
pb.__pb_TDMTeam.__name = "TDMTeam"
pb.__pb_TDMTeam.__index = pb.__pb_TDMTeam
pb.__pb_TDMTeam.__pairs = __pb_pairs

pb.TDMTeam = { __name = "TDMTeam", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMTeam : ProtoBase
---@field public team_id number
---@field public player_list pb_TDMPlayer[]
---@field public team_rank number
---@field public team_leader number

---@return pb_TDMTeam
function pb.TDMTeam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SettlementNtf = {
    player_id = 0,
    raid_task_succ = false,
    match_tactics = 0,
    dsa_id = "",
    not_cmp_state_room_id = false,
    exactly_leave = 0,
    client_group = 0,
    sys_team_id = 0,
}
pb.__pb_SettlementNtf.__name = "SettlementNtf"
pb.__pb_SettlementNtf.__index = pb.__pb_SettlementNtf
pb.__pb_SettlementNtf.__pairs = __pb_pairs

pb.SettlementNtf = { __name = "SettlementNtf", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SettlementNtf : ProtoBase
---@field public player_id number
---@field public match_info pb_DsMatchInfoNew
---@field public team_info pb_DsTeamInfo
---@field public status_array pb_DsPlayerGameStatus[]
---@field public raid_player_status_array pb_DsRaidPlayerGameStatus[]
---@field public raid_task_succ boolean
---@field public tdm_data pb_TDMSettlementData
---@field public player_quests pb_DsQuestData
---@field public marked_props pb_MarkPropInfo[]
---@field public match_tactics number
---@field public sol_data pb_SolSettlementData
---@field public dsa_id string
---@field public not_cmp_state_room_id boolean
---@field public activity_data pb_NumeralActivityData
---@field public room_attr pb_RoomAttr
---@field public bhd_data pb_BhdSettlementData
---@field public arn_data pb_ArenaSettlementData
---@field public exactly_leave number
---@field public client_group number
---@field public sys_team_id number

---@return pb_SettlementNtf
function pb.SettlementNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SolSettlementData = {
    dps_float = 0,
    dps = 0,
    last_time_props_price = 0,
    loot_total_price = 0,
    loot_expected_price = 0,
    contract_num = 0,
    fighting_with_player_num = 0,
    fighting_with_ai_num = 0,
    loot_cnt = 0,
    ttk = 0,
    hits = 0,
    ai_ttk = 0,
    total_high_quality_loot = 0,
    assist_cnt = 0,
}
pb.__pb_SolSettlementData.__name = "SolSettlementData"
pb.__pb_SolSettlementData.__index = pb.__pb_SolSettlementData
pb.__pb_SolSettlementData.__pairs = __pb_pairs

pb.SolSettlementData = { __name = "SolSettlementData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SolSettlementData : ProtoBase
---@field public dps_float number
---@field public dps number
---@field public last_time_props_price number
---@field public loot_total_price number
---@field public loot_expected_price number
---@field public contract_num number
---@field public fighting_with_player_num number
---@field public fighting_with_ai_num number
---@field public loot_cnt number
---@field public ttk number
---@field public hits number
---@field public ai_ttk number
---@field public total_high_quality_loot number
---@field public assist_cnt number
---@field public drop_counters pb_MatchSOLDropCounter[]

---@return pb_SolSettlementData
function pb.SolSettlementData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsAIBasicInfo = {
    player_id = 0,
    game_nick = "",
    gender = 0,
    level = 0,
    ai_lab_level = 0,
    rank_match_score = 0,
}
pb.__pb_DsAIBasicInfo.__name = "DsAIBasicInfo"
pb.__pb_DsAIBasicInfo.__index = pb.__pb_DsAIBasicInfo
pb.__pb_DsAIBasicInfo.__pairs = __pb_pairs

pb.DsAIBasicInfo = { __name = "DsAIBasicInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsAIBasicInfo : ProtoBase
---@field public player_id number
---@field public game_nick string
---@field public gender number
---@field public tag_array string[]
---@field public level number
---@field public ai_lab_level number
---@field public rank_match_score number

---@return pb_DsAIBasicInfo
function pb.DsAIBasicInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsAccidentInfo = {
    timestamp = 0,
    accident_type = 0,
    body_part = 0,
}
pb.__pb_DsAccidentInfo.__name = "DsAccidentInfo"
pb.__pb_DsAccidentInfo.__index = pb.__pb_DsAccidentInfo
pb.__pb_DsAccidentInfo.__pairs = __pb_pairs

pb.DsAccidentInfo = { __name = "DsAccidentInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsAccidentInfo : ProtoBase
---@field public timestamp number
---@field public position pb_DsPlayerPosition
---@field public accident_type number
---@field public body_part number

---@return pb_DsAccidentInfo
function pb.DsAccidentInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsAchievementInfo = {
    id = 0,
}
pb.__pb_DsAchievementInfo.__name = "DsAchievementInfo"
pb.__pb_DsAchievementInfo.__index = pb.__pb_DsAchievementInfo
pb.__pb_DsAchievementInfo.__pairs = __pb_pairs

pb.DsAchievementInfo = { __name = "DsAchievementInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsAchievementInfo : ProtoBase
---@field public id number
---@field public score_map pb_DsAchievementInfo.ScoreMapEntry[]

---@return pb_DsAchievementInfo
function pb.DsAchievementInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsDamageInfo = {
    timestamp = 0,
    equipment_recorded = false,
    has_weapon = false,
    weapon = 0,
    body_health = 0,
    status = 0,
    receiver_equipment_recorded = false,
    receiver_body_health = 0,
    receiver_status = 0,
    has_ammo = false,
    ammo = 0,
    damage = 0,
    body_part = 0,
    hit_distance = 0,
}
pb.__pb_DsDamageInfo.__name = "DsDamageInfo"
pb.__pb_DsDamageInfo.__index = pb.__pb_DsDamageInfo
pb.__pb_DsDamageInfo.__pairs = __pb_pairs

pb.DsDamageInfo = { __name = "DsDamageInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsDamageInfo : ProtoBase
---@field public timestamp number
---@field public position pb_DsPlayerPosition
---@field public equipment_recorded boolean
---@field public equipment pb_EquipPosition[]
---@field public has_weapon boolean
---@field public weapon number
---@field public weapon_prop pb_PropInfo
---@field public body_health number
---@field public status number
---@field public receiver_equipment_recorded boolean
---@field public receiver_equipment pb_EquipPosition[]
---@field public receiver_body_health number
---@field public receiver_status number
---@field public has_ammo boolean
---@field public ammo number
---@field public damage number
---@field public body_part number
---@field public hit_distance number

---@return pb_DsDamageInfo
function pb.DsDamageInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsEndGamePlayerStatus = {
    type = 0,
    extraction_point = 0,
    extraction_location = "",
    play_time = 0,
    has_main_weapon = false,
    main_weapon = 0,
    individual_rank = 0,
    carry_out_props_price = 0,
    carry_in_props_price = 0,
    blue_print_special_id = 0,
    asset_increment_price = 0,
    blue_print_type = 0,
    blue_print_price = 0,
    contract_quest_price = 0,
    carry_out_new_props_price = 0,
    rescue_count = 0,
    resurgence_count = 0,
    cost_price = 0,
    carry_out_profit_price = 0,
    carry_in_equip_price = 0,
    carry_out_without_teammate_price = 0,
}
pb.__pb_DsEndGamePlayerStatus.__name = "DsEndGamePlayerStatus"
pb.__pb_DsEndGamePlayerStatus.__index = pb.__pb_DsEndGamePlayerStatus
pb.__pb_DsEndGamePlayerStatus.__pairs = __pb_pairs

pb.DsEndGamePlayerStatus = { __name = "DsEndGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsEndGamePlayerStatus : ProtoBase
---@field public type number
---@field public extraction_point number
---@field public death_info pb_DsPlayerDeathInfo
---@field public extraction_location string
---@field public play_time number
---@field public has_main_weapon boolean
---@field public main_weapon number
---@field public match_log pb_DsMatchEvent[]
---@field public match_stat pb_DsPlayerMatchStatNew
---@field public individual_rank number
---@field public attr_array pb_DSAttrValue[]
---@field public buff_array pb_AttrBuff[]
---@field public carry_in_props pb_EquipPosition[]
---@field public final_props pb_EquipPosition[]
---@field public kill_array pb_DsPlayerKillInfo[]
---@field public knock_down_array pb_DsPlayerKillInfo[]
---@field public achievement_array pb_DsAchievementInfo[]
---@field public C_price_values number[]
---@field public carry_out_health_list pb_EquipHealth[]
---@field public carry_out_props_price number
---@field public carry_in_props_price number
---@field public hero pb_DSHero
---@field public blue_print_special_id number
---@field public asset_increment_price number
---@field public blue_print_type number
---@field public blue_print_price number
---@field public contract_quest_price number
---@field public carry_out_new_props_price number
---@field public rescue_count number
---@field public resurgence_count number
---@field public shoot_down_info pb_DsPlayerKillInfo
---@field public cost_price number
---@field public carry_out_profit_price number
---@field public shoot_down_info_list pb_DsPlayerKillInfo[]
---@field public carry_in_equip_price number
---@field public carry_out_without_teammate_price number

---@return pb_DsEndGamePlayerStatus
function pb.DsEndGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMatchEvent = {
    type = 0,
    timestamp = 0,
    kill_count = 0,
    total_price = 0,
    produced_price = 0,
    KM_traveled = 0,
}
pb.__pb_DsMatchEvent.__name = "DsMatchEvent"
pb.__pb_DsMatchEvent.__index = pb.__pb_DsMatchEvent
pb.__pb_DsMatchEvent.__pairs = __pb_pairs

pb.DsMatchEvent = { __name = "DsMatchEvent", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMatchEvent : ProtoBase
---@field public type number
---@field public timestamp number
---@field public position pb_DsPlayerPosition
---@field public param_map pb_DsMatchEvent.ParamMapEntry[]
---@field public kill_count number
---@field public total_price number
---@field public produced_price number
---@field public KM_traveled number

---@return pb_DsMatchEvent
function pb.DsMatchEvent:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMatchInfoNew = {
    room_id = 0,
    map_id = 0,
    match_type = 0,
    match_subtype = 0,
    match_mode = 0,
    team_size = 0,
    match_pool_id = 0,
    start_time = 0,
    player_count = 0,
    ai_count = 0,
    boss_count = 0,
    is_guide = false,
    ai_level = 0,
}
pb.__pb_DsMatchInfoNew.__name = "DsMatchInfoNew"
pb.__pb_DsMatchInfoNew.__index = pb.__pb_DsMatchInfoNew
pb.__pb_DsMatchInfoNew.__pairs = __pb_pairs

pb.DsMatchInfoNew = { __name = "DsMatchInfoNew", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMatchInfoNew : ProtoBase
---@field public room_id number
---@field public map_id number
---@field public match_type number
---@field public match_subtype number
---@field public match_mode number
---@field public team_size number
---@field public match_pool_id number
---@field public start_time number
---@field public player_count number
---@field public ai_count number
---@field public boss_count number
---@field public mode_info pb_MatchModeInfo
---@field public is_guide boolean
---@field public ai_level number

---@return pb_DsMatchInfoNew
function pb.DsMatchInfoNew:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPVEEndGamePlayerStatus = {
    type = 0,
    play_time = 0,
}
pb.__pb_DsPVEEndGamePlayerStatus.__name = "DsPVEEndGamePlayerStatus"
pb.__pb_DsPVEEndGamePlayerStatus.__index = pb.__pb_DsPVEEndGamePlayerStatus
pb.__pb_DsPVEEndGamePlayerStatus.__pairs = __pb_pairs

pb.DsPVEEndGamePlayerStatus = { __name = "DsPVEEndGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPVEEndGamePlayerStatus : ProtoBase
---@field public type number
---@field public play_time number
---@field public kill_array pb_DsPlayerKillInfo[]
---@field public mission_stat pb_DsPVEMissionStat
---@field public achievement_array pb_DsAchievementInfo[]

---@return pb_DsPVEEndGamePlayerStatus
function pb.DsPVEEndGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPVEMissionInfo = {
    room_id = 0,
    match_type = 0,
    match_subtype = 0,
    match_mode = 0,
    team_size = 0,
    map_id = 0,
    start_time = 0,
    player_count = 0,
}
pb.__pb_DsPVEMissionInfo.__name = "DsPVEMissionInfo"
pb.__pb_DsPVEMissionInfo.__index = pb.__pb_DsPVEMissionInfo
pb.__pb_DsPVEMissionInfo.__pairs = __pb_pairs

pb.DsPVEMissionInfo = { __name = "DsPVEMissionInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPVEMissionInfo : ProtoBase
---@field public room_id number
---@field public match_type number
---@field public match_subtype number
---@field public match_mode number
---@field public team_size number
---@field public map_id number
---@field public start_time number
---@field public player_count number

---@return pb_DsPVEMissionInfo
function pb.DsPVEMissionInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPVEMissionStat = {
    death_count = 0,
    task_count = 0,
}
pb.__pb_DsPVEMissionStat.__name = "DsPVEMissionStat"
pb.__pb_DsPVEMissionStat.__index = pb.__pb_DsPVEMissionStat
pb.__pb_DsPVEMissionStat.__pairs = __pb_pairs

pb.DsPVEMissionStat = { __name = "DsPVEMissionStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPVEMissionStat : ProtoBase
---@field public death_count number
---@field public task_count number

---@return pb_DsPVEMissionStat
function pb.DsPVEMissionStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPVEPlayerStatus = {
    type = 0,
}
pb.__pb_DsPVEPlayerStatus.__name = "DsPVEPlayerStatus"
pb.__pb_DsPVEPlayerStatus.__index = pb.__pb_DsPVEPlayerStatus
pb.__pb_DsPVEPlayerStatus.__pairs = __pb_pairs

pb.DsPVEPlayerStatus = { __name = "DsPVEPlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPVEPlayerStatus : ProtoBase
---@field public type number
---@field public basic_info pb_DsPlayerBasicInfo
---@field public equiped_props pb_EquipPosition[]
---@field public end_game pb_DsPVEEndGamePlayerStatus
---@field public quit_game pb_DsPVEQuitGamePlayerStatus
---@field public playing pb_DsPVEPlayingGamePlayerStatus

---@return pb_DsPVEPlayerStatus
function pb.DsPVEPlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPVEPlayingGamePlayerStatus = {
}
pb.__pb_DsPVEPlayingGamePlayerStatus.__name = "DsPVEPlayingGamePlayerStatus"
pb.__pb_DsPVEPlayingGamePlayerStatus.__index = pb.__pb_DsPVEPlayingGamePlayerStatus
pb.__pb_DsPVEPlayingGamePlayerStatus.__pairs = __pb_pairs

pb.DsPVEPlayingGamePlayerStatus = { __name = "DsPVEPlayingGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPVEPlayingGamePlayerStatus : ProtoBase

---@return pb_DsPVEPlayingGamePlayerStatus
function pb.DsPVEPlayingGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPVEQuitGamePlayerStatus = {
    play_time = 0,
}
pb.__pb_DsPVEQuitGamePlayerStatus.__name = "DsPVEQuitGamePlayerStatus"
pb.__pb_DsPVEQuitGamePlayerStatus.__index = pb.__pb_DsPVEQuitGamePlayerStatus
pb.__pb_DsPVEQuitGamePlayerStatus.__pairs = __pb_pairs

pb.DsPVEQuitGamePlayerStatus = { __name = "DsPVEQuitGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPVEQuitGamePlayerStatus : ProtoBase
---@field public play_time number
---@field public kill_array pb_DsPlayerKillInfo[]
---@field public mission_stat pb_DsPVEMissionStat
---@field public achievement_array pb_DsAchievementInfo[]

---@return pb_DsPVEQuitGamePlayerStatus
function pb.DsPVEQuitGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerBasicInfo = {
    player_id = 0,
    game_nick = "",
    gender = 0,
    avatar = "",
    background = "",
    level = 0,
    grade = 0,
    is_scav = false,
    team_seqno = 0,
    skill_score = 0,
    be_praised_num = 0,
    glicko_rating = 0,
    glicko_rating_dev = 0,
    rank_match_score = 0,
    old_glicko_rating = 0,
    old_glicko_rating_dev = 0,
    title = 0,
    hero_id = 0,
    plat_id = 0,
    account_type = 0,
}
pb.__pb_DsPlayerBasicInfo.__name = "DsPlayerBasicInfo"
pb.__pb_DsPlayerBasicInfo.__index = pb.__pb_DsPlayerBasicInfo
pb.__pb_DsPlayerBasicInfo.__pairs = __pb_pairs

pb.DsPlayerBasicInfo = { __name = "DsPlayerBasicInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerBasicInfo : ProtoBase
---@field public player_id number
---@field public game_nick string
---@field public gender number
---@field public avatar string
---@field public background string
---@field public level number
---@field public grade number
---@field public is_scav boolean
---@field public team_seqno number
---@field public skill_score number
---@field public be_praised_num number
---@field public glicko_rating number
---@field public glicko_rating_dev number
---@field public rank_match_score number
---@field public scheme_detail pb_TDMDsDetail
---@field public old_glicko_rating number
---@field public old_glicko_rating_dev number
---@field public title number
---@field public hero_id number
---@field public plat_id number
---@field public account_type number

---@return pb_DsPlayerBasicInfo
function pb.DsPlayerBasicInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerDeathInfo = {
    reason = 0,
    timestamp = 0,
}
pb.__pb_DsPlayerDeathInfo.__name = "DsPlayerDeathInfo"
pb.__pb_DsPlayerDeathInfo.__index = pb.__pb_DsPlayerDeathInfo
pb.__pb_DsPlayerDeathInfo.__pairs = __pb_pairs

pb.DsPlayerDeathInfo = { __name = "DsPlayerDeathInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerDeathInfo : ProtoBase
---@field public reason number
---@field public timestamp number
---@field public position pb_DsPlayerPosition
---@field public damage pb_DsDamageInfo
---@field public accident pb_DsAccidentInfo
---@field public player pb_DsPlayerBasicInfo
---@field public ai pb_DsAIBasicInfo
---@field public boss pb_DsAIBasicInfo

---@return pb_DsPlayerDeathInfo
function pb.DsPlayerDeathInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsGameAchievement = {
    id = 0,
    value = 0,
    required = 0,
}
pb.__pb_DsGameAchievement.__name = "DsGameAchievement"
pb.__pb_DsGameAchievement.__index = pb.__pb_DsGameAchievement
pb.__pb_DsGameAchievement.__pairs = __pb_pairs

pb.DsGameAchievement = { __name = "DsGameAchievement", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsGameAchievement : ProtoBase
---@field public id number
---@field public value number
---@field public required number

---@return pb_DsGameAchievement
function pb.DsGameAchievement:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsGameAchievementTask = {
    task_id = 0,
    badge_id = 0,
}
pb.__pb_DsGameAchievementTask.__name = "DsGameAchievementTask"
pb.__pb_DsGameAchievementTask.__index = pb.__pb_DsGameAchievementTask
pb.__pb_DsGameAchievementTask.__pairs = __pb_pairs

pb.DsGameAchievementTask = { __name = "DsGameAchievementTask", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsGameAchievementTask : ProtoBase
---@field public task_id number
---@field public badge_id number
---@field public achievements pb_DsGameAchievement[]

---@return pb_DsGameAchievementTask
function pb.DsGameAchievementTask:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerGameStatus = {
    type = 0,
    mode_id = 0,
    player_start = 0,
    money_paper = 0,
    rank_match_score = 0,
    rank_match_loot_box = 0,
    result = 0,
    armor_reduce_in_fighting = 0,
    total_mileage = 0,
    sol_double_rank_multiple_value = 0,
    assist_cnt = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_down = 0,
    total_shoot_head_down = 0,
    exactly_leave = 0,
    sys_team_id = 0,
    safebox_skin_id = 0,
    course_id = 0,
    is_ranked_match = false,
    has_trigger_gold_egg = false,
}
pb.__pb_DsPlayerGameStatus.__name = "DsPlayerGameStatus"
pb.__pb_DsPlayerGameStatus.__index = pb.__pb_DsPlayerGameStatus
pb.__pb_DsPlayerGameStatus.__pairs = __pb_pairs

pb.DsPlayerGameStatus = { __name = "DsPlayerGameStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerGameStatus : ProtoBase
---@field public type number
---@field public basic_info pb_DsPlayerBasicInfo
---@field public mode_id number
---@field public player_start number
---@field public playing pb_DsPlayingPlayerStatus
---@field public end_game pb_DsEndGamePlayerStatus
---@field public quit_game pb_DsQuitGamePlayerStatus
---@field public equip_use_time pb_EquipUseTime[]
---@field public money_paper number
---@field public ai_info pb_DsAIBasicInfo
---@field public rank_match_score number
---@field public rank_match_loot_box number
---@field public game_achievements pb_DsGameAchievement[]
---@field public result number
---@field public mystical_skins pb_PropInfo[]
---@field public armor_reduce_in_fighting number
---@field public total_mileage number
---@field public sol_double_rank_multiple_value number
---@field public assist_cnt number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_down number
---@field public total_shoot_head_down number
---@field public exactly_leave number
---@field public sys_team_id number
---@field public safebox_skin_id number
---@field public course_id number
---@field public is_ranked_match boolean
---@field public has_trigger_gold_egg boolean

---@return pb_DsPlayerGameStatus
function pb.DsPlayerGameStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerKillInfo = {
    type = 0,
    timestamp = 0,
    total_damage = 0,
    enemy_type = 0,
    carry_in_props_price = 0,
    carry_in_equip_price = 0,
}
pb.__pb_DsPlayerKillInfo.__name = "DsPlayerKillInfo"
pb.__pb_DsPlayerKillInfo.__index = pb.__pb_DsPlayerKillInfo
pb.__pb_DsPlayerKillInfo.__pairs = __pb_pairs

pb.DsPlayerKillInfo = { __name = "DsPlayerKillInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerKillInfo : ProtoBase
---@field public type number
---@field public timestamp number
---@field public position pb_DsPlayerPosition
---@field public damage pb_DsDamageInfo
---@field public total_damage number
---@field public enemy_type number
---@field public player pb_DsPlayerBasicInfo
---@field public ai pb_DsAIBasicInfo
---@field public boss pb_DsAIBasicInfo
---@field public carry_in_props_price number
---@field public carry_in_equip_price number

---@return pb_DsPlayerKillInfo
function pb.DsPlayerKillInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerMatchStatNew = {
    blood_loss = 0,
    HP_healed = 0,
    dehydration_times = 0,
    drink_consumed = 0,
    food_consumed = 0,
    KM_traveled = 0,
    bodies_looted = 0,
    weapons_found = 0,
    accessories_found = 0,
    provisions_found = 0,
    rescue_count = 0,
    revive_count = 0,
    unlock_times = 0,
    total_damage = 0,
    head_shot_count = 0,
    total_loot = 0,
    total_battle = 0,
    total_sneak = 0,
    first_loot = 0,
    resurgence_count = 0,
}
pb.__pb_DsPlayerMatchStatNew.__name = "DsPlayerMatchStatNew"
pb.__pb_DsPlayerMatchStatNew.__index = pb.__pb_DsPlayerMatchStatNew
pb.__pb_DsPlayerMatchStatNew.__pairs = __pb_pairs

pb.DsPlayerMatchStatNew = { __name = "DsPlayerMatchStatNew", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerMatchStatNew : ProtoBase
---@field public blood_loss number
---@field public HP_healed number
---@field public dehydration_times number
---@field public drink_consumed number
---@field public food_consumed number
---@field public KM_traveled number
---@field public bodies_looted number
---@field public weapons_found number
---@field public accessories_found number
---@field public provisions_found number
---@field public rescue_count number
---@field public revive_count number
---@field public unlock_times number
---@field public total_damage number
---@field public head_shot_count number
---@field public combo_array number[]
---@field public area_array number[]
---@field public prop_array number[]
---@field public total_loot number
---@field public total_battle number
---@field public total_sneak number
---@field public first_loot number
---@field public new_props_gid number[]
---@field public resurgence_count number

---@return pb_DsPlayerMatchStatNew
function pb.DsPlayerMatchStatNew:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerPosition = {
    x = 0,
    y = 0,
    z = 0,
}
pb.__pb_DsPlayerPosition.__name = "DsPlayerPosition"
pb.__pb_DsPlayerPosition.__index = pb.__pb_DsPlayerPosition
pb.__pb_DsPlayerPosition.__pairs = __pb_pairs

pb.DsPlayerPosition = { __name = "DsPlayerPosition", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerPosition : ProtoBase
---@field public x number
---@field public y number
---@field public z number

---@return pb_DsPlayerPosition
function pb.DsPlayerPosition:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayingPlayerStatus = {
    play_time = 0,
}
pb.__pb_DsPlayingPlayerStatus.__name = "DsPlayingPlayerStatus"
pb.__pb_DsPlayingPlayerStatus.__index = pb.__pb_DsPlayingPlayerStatus
pb.__pb_DsPlayingPlayerStatus.__pairs = __pb_pairs

pb.DsPlayingPlayerStatus = { __name = "DsPlayingPlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayingPlayerStatus : ProtoBase
---@field public play_time number
---@field public carry_in_props pb_EquipPosition[]
---@field public current_props pb_EquipPosition[]
---@field public hero pb_DSHero

---@return pb_DsPlayingPlayerStatus
function pb.DsPlayingPlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsQuestData = {
}
pb.__pb_DsQuestData.__name = "DsQuestData"
pb.__pb_DsQuestData.__index = pb.__pb_DsQuestData
pb.__pb_DsQuestData.__pairs = __pb_pairs

pb.DsQuestData = { __name = "DsQuestData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsQuestData : ProtoBase
---@field public player_changed_quests pb_PlayerQuestData[]

---@return pb_DsQuestData
function pb.DsQuestData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsQuitGamePlayerStatus = {
    reason = 0,
    play_time = 0,
    has_main_weapon = false,
    main_weapon = 0,
    individual_rank = 0,
    carry_out_props_price = 0,
    carry_in_props_price = 0,
    asset_increment_price = 0,
    rescue_count = 0,
    resurgence_count = 0,
    carry_out_profit_price = 0,
    cost_price = 0,
    blue_print_special_id = 0,
    blue_print_type = 0,
    carry_in_equip_price = 0,
    carry_out_new_props_price = 0,
    blue_print_price = 0,
    carry_out_without_teammate_price = 0,
}
pb.__pb_DsQuitGamePlayerStatus.__name = "DsQuitGamePlayerStatus"
pb.__pb_DsQuitGamePlayerStatus.__index = pb.__pb_DsQuitGamePlayerStatus
pb.__pb_DsQuitGamePlayerStatus.__pairs = __pb_pairs

pb.DsQuitGamePlayerStatus = { __name = "DsQuitGamePlayerStatus", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsQuitGamePlayerStatus : ProtoBase
---@field public reason number
---@field public play_time number
---@field public has_main_weapon boolean
---@field public main_weapon number
---@field public match_stat pb_DsPlayerMatchStatNew
---@field public individual_rank number
---@field public carry_in_props pb_EquipPosition[]
---@field public final_props pb_EquipPosition[]
---@field public kill_array pb_DsPlayerKillInfo[]
---@field public knock_down_array pb_DsPlayerKillInfo[]
---@field public achievement_array pb_DsAchievementInfo[]
---@field public carry_out_health_list pb_EquipHealth[]
---@field public carry_out_props_price number
---@field public carry_in_props_price number
---@field public hero pb_DSHero
---@field public asset_increment_price number
---@field public rescue_count number
---@field public resurgence_count number
---@field public shoot_down_info pb_DsPlayerKillInfo
---@field public carry_out_profit_price number
---@field public cost_price number
---@field public shoot_down_info_list pb_DsPlayerKillInfo[]
---@field public blue_print_special_id number
---@field public blue_print_type number
---@field public carry_in_equip_price number
---@field public carry_out_new_props_price number
---@field public blue_print_price number
---@field public carry_out_without_teammate_price number

---@return pb_DsQuitGamePlayerStatus
function pb.DsQuitGamePlayerStatus:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsStat = {
    player_num = 0,
    robot_num = 0,
    ai_num = 0,
}
pb.__pb_DsStat.__name = "DsStat"
pb.__pb_DsStat.__index = pb.__pb_DsStat
pb.__pb_DsStat.__pairs = __pb_pairs

pb.DsStat = { __name = "DsStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsStat : ProtoBase
---@field public player_num number
---@field public robot_num number
---@field public ai_num number

---@return pb_DsStat
function pb.DsStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsTeamInfo = {
    team_id = 0,
    teammate_count = 0,
    team_rank = 0,
    team_num = 0,
}
pb.__pb_DsTeamInfo.__name = "DsTeamInfo"
pb.__pb_DsTeamInfo.__index = pb.__pb_DsTeamInfo
pb.__pb_DsTeamInfo.__pairs = __pb_pairs

pb.DsTeamInfo = { __name = "DsTeamInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsTeamInfo : ProtoBase
---@field public team_id number
---@field public teammate_count number
---@field public team_rank number
---@field public team_achievement_array pb_DsAchievementInfo[]
---@field public team_num number

---@return pb_DsTeamInfo
function pb.DsTeamInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipHealth = {
    id = 0,
    gid = 0,
    health = 0,
    origin_health = 0,
}
pb.__pb_EquipHealth.__name = "EquipHealth"
pb.__pb_EquipHealth.__index = pb.__pb_EquipHealth
pb.__pb_EquipHealth.__pairs = __pb_pairs

pb.EquipHealth = { __name = "EquipHealth", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipHealth : ProtoBase
---@field public id number
---@field public gid number
---@field public health number
---@field public origin_health number
---@field public prop pb_PropInfo

---@return pb_EquipHealth
function pb.EquipHealth:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipPosition = {
    position = 0,
    capacity = 0,
    src_prop_id = 0,
}
pb.__pb_EquipPosition.__name = "EquipPosition"
pb.__pb_EquipPosition.__index = pb.__pb_EquipPosition
pb.__pb_EquipPosition.__pairs = __pb_pairs

pb.EquipPosition = { __name = "EquipPosition", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipPosition : ProtoBase
---@field public position number
---@field public load_props pb_PropInfo[]
---@field public capacity number
---@field public grid_space pb_GridSize[]
---@field public src_prop_id number

---@return pb_EquipPosition
function pb.EquipPosition:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EquipUseTime = {
    weapon_id = 0,
    time = 0,
    expert_id = 0,
}
pb.__pb_EquipUseTime.__name = "EquipUseTime"
pb.__pb_EquipUseTime.__index = pb.__pb_EquipUseTime
pb.__pb_EquipUseTime.__pairs = __pb_pairs

pb.EquipUseTime = { __name = "EquipUseTime", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EquipUseTime : ProtoBase
---@field public weapon_id number
---@field public time number
---@field public expert_id number

---@return pb_EquipUseTime
function pb.EquipUseTime:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ExpertSkill = {
    expert_id = 0,
    active_skill_id = 0,
    ultimate_skill_id = 0,
    support_skill_id = 0,
    passive_skill_id = 0,
    common_passive_skill_id = 0,
    support_skill_id2 = 0,
}
pb.__pb_ExpertSkill.__name = "ExpertSkill"
pb.__pb_ExpertSkill.__index = pb.__pb_ExpertSkill
pb.__pb_ExpertSkill.__pairs = __pb_pairs

pb.ExpertSkill = { __name = "ExpertSkill", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ExpertSkill : ProtoBase
---@field public expert_id number
---@field public active_skill_id number
---@field public ultimate_skill_id number
---@field public support_skill_id number
---@field public passive_skill_id number
---@field public common_passive_skill_id number
---@field public support_skill_id2 number

---@return pb_ExpertSkill
function pb.ExpertSkill:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GeneralSkill = {
    skill_id = 0,
    skill_lvl = 0,
}
pb.__pb_GeneralSkill.__name = "GeneralSkill"
pb.__pb_GeneralSkill.__index = pb.__pb_GeneralSkill
pb.__pb_GeneralSkill.__pairs = __pb_pairs

pb.GeneralSkill = { __name = "GeneralSkill", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GeneralSkill : ProtoBase
---@field public skill_id number
---@field public skill_lvl number

---@return pb_GeneralSkill
function pb.GeneralSkill:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GridSize = {
    id = 0,
    length = 0,
    width = 0,
    base_cnt = 0,
    is_map_unlocked = false,
}
pb.__pb_GridSize.__name = "GridSize"
pb.__pb_GridSize.__index = pb.__pb_GridSize
pb.__pb_GridSize.__pairs = __pb_pairs

pb.GridSize = { __name = "GridSize", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GridSize : ProtoBase
---@field public id number
---@field public length number
---@field public width number
---@field public base_cnt number
---@field public total_locked number[]
---@field public unlocked number[]
---@field public is_map_unlocked boolean

---@return pb_GridSize
function pb.GridSize:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HalfJoinConfig = {
    enable_half_join = false,
    forbid_join_time = 0,
    forbid_join_kills = 0,
    forbid_join_round = 0,
    forbid_join_score_diff = 0,
    half_join_on_game_start = false,
    fast_half_join_threshold = 0,
    enable_bot_half_join = false,
    bot_wait_gap = 0,
    specify_camp = 0,
    half_join_intervel = 0,
    half_join_time = 0,
}
pb.__pb_HalfJoinConfig.__name = "HalfJoinConfig"
pb.__pb_HalfJoinConfig.__index = pb.__pb_HalfJoinConfig
pb.__pb_HalfJoinConfig.__pairs = __pb_pairs

pb.HalfJoinConfig = { __name = "HalfJoinConfig", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HalfJoinConfig : ProtoBase
---@field public enable_half_join boolean
---@field public forbid_join_time number
---@field public forbid_join_kills number
---@field public forbid_join_round number
---@field public forbid_join_score_diff number
---@field public half_join_on_game_start boolean
---@field public fast_half_join_threshold number
---@field public enable_bot_half_join boolean
---@field public bot_wait_gap number
---@field public specify_camp number
---@field public half_join_intervel number
---@field public half_join_time number

---@return pb_HalfJoinConfig
function pb.HalfJoinConfig:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Hero = {
    id = 0,
    is_unlock = false,
    is_equipped = false,
    armed_force_id = 0,
}
pb.__pb_Hero.__name = "Hero"
pb.__pb_Hero.__index = pb.__pb_Hero
pb.__pb_Hero.__pairs = __pb_pairs

pb.Hero = { __name = "Hero", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Hero : ProtoBase
---@field public id number
---@field public is_unlock boolean
---@field public equipped_ability pb_CombatRoleAbility[]
---@field public is_equipped boolean
---@field public armed_force_id number

---@return pb_Hero
function pb.Hero:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroAccessoryItem = {
    prop_id = 0,
    slot = 0,
    unlock_time = 0,
}
pb.__pb_HeroAccessoryItem.__name = "HeroAccessoryItem"
pb.__pb_HeroAccessoryItem.__index = pb.__pb_HeroAccessoryItem
pb.__pb_HeroAccessoryItem.__pairs = __pb_pairs

pb.HeroAccessoryItem = { __name = "HeroAccessoryItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroAccessoryItem : ProtoBase
---@field public prop_id number
---@field public slot number
---@field public unlock_time number

---@return pb_HeroAccessoryItem
function pb.HeroAccessoryItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroAttr = {
    base_load_level = 0,
    upgrade_level = 0,
}
pb.__pb_HeroAttr.__name = "HeroAttr"
pb.__pb_HeroAttr.__index = pb.__pb_HeroAttr
pb.__pb_HeroAttr.__pairs = __pb_pairs

pb.HeroAttr = { __name = "HeroAttr", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroAttr : ProtoBase
---@field public base_load_level number
---@field public upgrade_level number
---@field public fashion pb_HeroFashion[]

---@return pb_HeroAttr
function pb.HeroAttr:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroFashion = {
    slot = 0,
    id = 0,
}
pb.__pb_HeroFashion.__name = "HeroFashion"
pb.__pb_HeroFashion.__index = pb.__pb_HeroFashion
pb.__pb_HeroFashion.__pairs = __pb_pairs

pb.HeroFashion = { __name = "HeroFashion", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroFashion : ProtoBase
---@field public slot number
---@field public id number

---@return pb_HeroFashion
function pb.HeroFashion:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchModeInfo = {
    game_mode = 0,
    game_rule = 0,
    sub_mode = 0,
    team_mode = 0,
    raid_id = 0,
    map_id = 0,
    match_mode_id = 0,
    scheme_pool_id = 0,
}
pb.__pb_MatchModeInfo.__name = "MatchModeInfo"
pb.__pb_MatchModeInfo.__index = pb.__pb_MatchModeInfo
pb.__pb_MatchModeInfo.__pairs = __pb_pairs

pb.MatchModeInfo = { __name = "MatchModeInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchModeInfo : ProtoBase
---@field public game_mode number
---@field public game_rule number
---@field public sub_mode number
---@field public team_mode number
---@field public raid_id number
---@field public map_id number
---@field public match_mode_id number
---@field public scheme_pool_id number

---@return pb_MatchModeInfo
function pb.MatchModeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TdmTeamTagInfo = {
    tdm_lvl1_tag = 0,
    tdm_play_type = 0,
}
pb.__pb_TdmTeamTagInfo.__name = "TdmTeamTagInfo"
pb.__pb_TdmTeamTagInfo.__index = pb.__pb_TdmTeamTagInfo
pb.__pb_TdmTeamTagInfo.__pairs = __pb_pairs

pb.TdmTeamTagInfo = { __name = "TdmTeamTagInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TdmTeamTagInfo : ProtoBase
---@field public tdm_lvl1_tag number
---@field public tdm_language_type string[]
---@field public tdm_play_type number
---@field public language_type number[]

---@return pb_TdmTeamTagInfo
function pb.TdmTeamTagInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMDsDetail = {
    scheme_id = 0,
    is_finish_tdm_scheme = false,
    normal_down_shift = 0,
    tournament_down_shift = 0,
    dynamic_skill_level = 0,
    real_hidden_score = 0,
    match_wait_time = 0,
    match_bucket_ori = 0,
    match_bucket_lower = 0,
    ladder_level = 0,
    ladder_score = 0,
    normal_warm_point = 0,
    rank_warm_point = 0,
    ailab_bucket_diff = 0,
}
pb.__pb_TDMDsDetail.__name = "TDMDsDetail"
pb.__pb_TDMDsDetail.__index = pb.__pb_TDMDsDetail
pb.__pb_TDMDsDetail.__pairs = __pb_pairs

pb.TDMDsDetail = { __name = "TDMDsDetail", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMDsDetail : ProtoBase
---@field public scheme_id number
---@field public is_finish_tdm_scheme boolean
---@field public normal_down_shift number
---@field public tournament_down_shift number
---@field public dynamic_skill_level number
---@field public real_hidden_score number
---@field public match_wait_time number
---@field public match_bucket_ori number
---@field public match_bucket_lower number
---@field public tdm_tag pb_TdmTeamTagInfo
---@field public ladder_level number
---@field public ladder_score number
---@field public normal_warm_point number
---@field public rank_warm_point number
---@field public ailab_bucket_diff number

---@return pb_TDMDsDetail
function pb.TDMDsDetail:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchRoomTeamMemberInfo = {
    player_id = 0,
    team_id = 0,
    player_idx = 0,
    camp = 0,
    nick_name = "",
    plat_id = 0,
    is_robot = false,
    warm_ab_type = 0,
    team_name = "",
    is_team_leader = false,
    first_idc = "",
    first_idc_rtt = 0,
    systeam_team_id = 0,
}
pb.__pb_MatchRoomTeamMemberInfo.__name = "MatchRoomTeamMemberInfo"
pb.__pb_MatchRoomTeamMemberInfo.__index = pb.__pb_MatchRoomTeamMemberInfo
pb.__pb_MatchRoomTeamMemberInfo.__pairs = __pb_pairs

pb.MatchRoomTeamMemberInfo = { __name = "MatchRoomTeamMemberInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchRoomTeamMemberInfo : ProtoBase
---@field public player_id number
---@field public team_id number
---@field public player_idx number
---@field public camp number
---@field public nick_name string
---@field public plat_id number
---@field public is_robot boolean
---@field public warm_ab_type number
---@field public team_name string
---@field public is_team_leader boolean
---@field public first_idc string
---@field public first_idc_rtt number
---@field public systeam_team_id number

---@return pb_MatchRoomTeamMemberInfo
function pb.MatchRoomTeamMemberInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchSOLDropCounter = {
    drop_logic_id = 0,
    drop_key = "",
    count = 0,
    today_trigger_cnt = 0,
    start_of_today = 0,
}
pb.__pb_MatchSOLDropCounter.__name = "MatchSOLDropCounter"
pb.__pb_MatchSOLDropCounter.__index = pb.__pb_MatchSOLDropCounter
pb.__pb_MatchSOLDropCounter.__pairs = __pb_pairs

pb.MatchSOLDropCounter = { __name = "MatchSOLDropCounter", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchSOLDropCounter : ProtoBase
---@field public drop_logic_id number
---@field public drop_key string
---@field public count number
---@field public today_trigger_cnt number
---@field public start_of_today number

---@return pb_MatchSOLDropCounter
function pb.MatchSOLDropCounter:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MysticalSkinInfo = {
    rarity = 0,
    color = 0,
    material = 0,
    wear = 0,
    unique_no = 0,
    kill_cnter = 0,
    custom_name = "",
    market_buy_time = 0,
    source_type = 0,
}
pb.__pb_MysticalSkinInfo.__name = "MysticalSkinInfo"
pb.__pb_MysticalSkinInfo.__index = pb.__pb_MysticalSkinInfo
pb.__pb_MysticalSkinInfo.__pairs = __pb_pairs

pb.MysticalSkinInfo = { __name = "MysticalSkinInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MysticalSkinInfo : ProtoBase
---@field public rarity number
---@field public color number
---@field public material number
---@field public wear number
---@field public unique_no number
---@field public kill_cnter number
---@field public custom_name string
---@field public appearance pb_MysticalAppearanceInfo
---@field public market_buy_time number
---@field public source_type number

---@return pb_MysticalSkinInfo
function pb.MysticalSkinInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MysticalAppearanceInfo = {
    id = 0,
    seed = 0,
}
pb.__pb_MysticalAppearanceInfo.__name = "MysticalAppearanceInfo"
pb.__pb_MysticalAppearanceInfo.__index = pb.__pb_MysticalAppearanceInfo
pb.__pb_MysticalAppearanceInfo.__pairs = __pb_pairs

pb.MysticalAppearanceInfo = { __name = "MysticalAppearanceInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MysticalAppearanceInfo : ProtoBase
---@field public id number
---@field public seed number

---@return pb_MysticalAppearanceInfo
function pb.MysticalAppearanceInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MysticalPendantInfo = {
    rarity = 0,
    wear = 0,
    unique_no = 0,
    custom_name = "",
    market_buy_time = 0,
    source_type = 0,
    suit_active = false,
}
pb.__pb_MysticalPendantInfo.__name = "MysticalPendantInfo"
pb.__pb_MysticalPendantInfo.__index = pb.__pb_MysticalPendantInfo
pb.__pb_MysticalPendantInfo.__pairs = __pb_pairs

pb.MysticalPendantInfo = { __name = "MysticalPendantInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MysticalPendantInfo : ProtoBase
---@field public rarity number
---@field public wear number
---@field public unique_no number
---@field public custom_name string
---@field public appearance pb_MysticalAppearanceInfo
---@field public market_buy_time number
---@field public source_type number
---@field public suit_active boolean
---@field public show pb_MysticalPendantShowInfo[]

---@return pb_MysticalPendantInfo
function pb.MysticalPendantInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MysticalPendantShowInfo = {
    show_type = 0,
    rarity = 0,
}
pb.__pb_MysticalPendantShowInfo.__name = "MysticalPendantShowInfo"
pb.__pb_MysticalPendantShowInfo.__index = pb.__pb_MysticalPendantShowInfo
pb.__pb_MysticalPendantShowInfo.__pairs = __pb_pairs

pb.MysticalPendantShowInfo = { __name = "MysticalPendantShowInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MysticalPendantShowInfo : ProtoBase
---@field public show_type number
---@field public rarity number
---@field public appearance pb_MysticalAppearanceInfo

---@return pb_MysticalPendantShowInfo
function pb.MysticalPendantShowInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MysticalPendantSuitInfo = {
    suit_id = 0,
    gathered = false,
    actived = false,
    season_id = 0,
    red_point_exist = false,
}
pb.__pb_MysticalPendantSuitInfo.__name = "MysticalPendantSuitInfo"
pb.__pb_MysticalPendantSuitInfo.__index = pb.__pb_MysticalPendantSuitInfo
pb.__pb_MysticalPendantSuitInfo.__pairs = __pb_pairs

pb.MysticalPendantSuitInfo = { __name = "MysticalPendantSuitInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MysticalPendantSuitInfo : ProtoBase
---@field public suit_id number
---@field public pendant_map pb_MysticalPendantSuitInfo.PendantMapEntry[]
---@field public gathered boolean
---@field public actived boolean
---@field public season_id number
---@field public red_point_exist boolean

---@return pb_MysticalPendantSuitInfo
function pb.MysticalPendantSuitInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_NumeralActivityData = {
    orgin_commander_score = 0,
}
pb.__pb_NumeralActivityData.__name = "NumeralActivityData"
pb.__pb_NumeralActivityData.__index = pb.__pb_NumeralActivityData
pb.__pb_NumeralActivityData.__pairs = __pb_pairs

pb.NumeralActivityData = { __name = "NumeralActivityData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_NumeralActivityData : ProtoBase
---@field public objectives pb_NumeralActivityObjective[]
---@field public contracts number[]
---@field public boxes pb_NumeralActivityPasswordBox[]
---@field public unlocked_boxes number[]
---@field public star_fire_objectives pb_NumeralActivityObjective[]
---@field public quest_objectives pb_NumeralActivityObjective[]
---@field public orgin_commander_score number

---@return pb_NumeralActivityData
function pb.NumeralActivityData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_NumeralActivityPasswordBox = {
    id = 0,
    password = "",
}
pb.__pb_NumeralActivityPasswordBox.__name = "NumeralActivityPasswordBox"
pb.__pb_NumeralActivityPasswordBox.__index = pb.__pb_NumeralActivityPasswordBox
pb.__pb_NumeralActivityPasswordBox.__pairs = __pb_pairs

pb.NumeralActivityPasswordBox = { __name = "NumeralActivityPasswordBox", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_NumeralActivityPasswordBox : ProtoBase
---@field public id number
---@field public password string
---@field public unlocked_digits pb_UnlockedDigit[]

---@return pb_NumeralActivityPasswordBox
function pb.NumeralActivityPasswordBox:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_UnlockedDigit = {
    index = 0,
    digit = 0,
}
pb.__pb_UnlockedDigit.__name = "UnlockedDigit"
pb.__pb_UnlockedDigit.__index = pb.__pb_UnlockedDigit
pb.__pb_UnlockedDigit.__pairs = __pb_pairs

pb.UnlockedDigit = { __name = "UnlockedDigit", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_UnlockedDigit : ProtoBase
---@field public index number
---@field public digit number

---@return pb_UnlockedDigit
function pb.UnlockedDigit:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_NumeralActivityObjective = {
    activity_id = 0,
    task_id = 0,
    id = 0,
    completed = false,
    current_value = 0,
    source = 0,
    type = 0,
    delta = 0,
}
pb.__pb_NumeralActivityObjective.__name = "NumeralActivityObjective"
pb.__pb_NumeralActivityObjective.__index = pb.__pb_NumeralActivityObjective
pb.__pb_NumeralActivityObjective.__pairs = __pb_pairs

pb.NumeralActivityObjective = { __name = "NumeralActivityObjective", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_NumeralActivityObjective : ProtoBase
---@field public activity_id number
---@field public task_id number
---@field public id number
---@field public completed boolean
---@field public current_value number
---@field public source number
---@field public type number
---@field public delta number

---@return pb_NumeralActivityObjective
function pb.NumeralActivityObjective:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ODTdmCamp = {
    offense_stronghold = 0,
    offense_total_stronghold = 0,
    offense_reenforce = 0,
    defense_stronghold = 0,
    defense_total_stronghold = 0,
    defense_kill_enemy = 0,
}
pb.__pb_ODTdmCamp.__name = "ODTdmCamp"
pb.__pb_ODTdmCamp.__index = pb.__pb_ODTdmCamp
pb.__pb_ODTdmCamp.__pairs = __pb_pairs

pb.ODTdmCamp = { __name = "ODTdmCamp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ODTdmCamp : ProtoBase
---@field public offense_stronghold number
---@field public offense_total_stronghold number
---@field public offense_reenforce number
---@field public defense_stronghold number
---@field public defense_total_stronghold number
---@field public defense_kill_enemy number

---@return pb_ODTdmCamp
function pb.ODTdmCamp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_OdTdmPlayer = {
    military_rank = 0,
    rescue_num = 0,
}
pb.__pb_OdTdmPlayer.__name = "OdTdmPlayer"
pb.__pb_OdTdmPlayer.__index = pb.__pb_OdTdmPlayer
pb.__pb_OdTdmPlayer.__pairs = __pb_pairs

pb.OdTdmPlayer = { __name = "OdTdmPlayer", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_OdTdmPlayer : ProtoBase
---@field public military_rank number
---@field public rescue_num number

---@return pb_OdTdmPlayer
function pb.OdTdmPlayer:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PerkInfo = {
    id = 0,
    gid = 0,
    health = 0,
    health_max = 0,
    length = 0,
    width = 0,
}
pb.__pb_PerkInfo.__name = "PerkInfo"
pb.__pb_PerkInfo.__index = pb.__pb_PerkInfo
pb.__pb_PerkInfo.__pairs = __pb_pairs

pb.PerkInfo = { __name = "PerkInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PerkInfo : ProtoBase
---@field public id number
---@field public gid number
---@field public health number
---@field public health_max number
---@field public loc pb_PropLocation
---@field public length number
---@field public width number

---@return pb_PerkInfo
function pb.PerkInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerObjectiveExtraData = {
    check_sum = 0,
    data1 = 0,
    data2 = 0,
    data3 = 0,
    data4 = 0,
}
pb.__pb_PlayerObjectiveExtraData.__name = "PlayerObjectiveExtraData"
pb.__pb_PlayerObjectiveExtraData.__index = pb.__pb_PlayerObjectiveExtraData
pb.__pb_PlayerObjectiveExtraData.__pairs = __pb_pairs

pb.PlayerObjectiveExtraData = { __name = "PlayerObjectiveExtraData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerObjectiveExtraData : ProtoBase
---@field public check_sum number
---@field public data1 number
---@field public data2 number
---@field public data3 number
---@field public data4 number

---@return pb_PlayerObjectiveExtraData
function pb.PlayerObjectiveExtraData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerQuestData = {
    quest_id = 0,
    quest_state = 0,
    quest_accept_time = 0,
    expire_time = 0,
    reward_time = 0,
}
pb.__pb_PlayerQuestData.__name = "PlayerQuestData"
pb.__pb_PlayerQuestData.__index = pb.__pb_PlayerQuestData
pb.__pb_PlayerQuestData.__pairs = __pb_pairs

pb.PlayerQuestData = { __name = "PlayerQuestData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerQuestData : ProtoBase
---@field public quest_id number
---@field public quest_state number
---@field public quest_accept_time number
---@field public quest_var number[]
---@field public quest_objectives pb_PlayerQuestObjectiveData[]
---@field public marked_objective_id number[]
---@field public expire_time number
---@field public enter_map_info pb_QuestVarEntry[]
---@field public event_occur_info pb_QuestVarEntry[]
---@field public reward_time number

---@return pb_PlayerQuestData
function pb.PlayerQuestData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerQuestObjectiveData = {
    quest_objective_id = 0,
    has_completed = false,
    value = 0,
    has_marked = false,
    spent_seconds = 0,
}
pb.__pb_PlayerQuestObjectiveData.__name = "PlayerQuestObjectiveData"
pb.__pb_PlayerQuestObjectiveData.__index = pb.__pb_PlayerQuestObjectiveData
pb.__pb_PlayerQuestObjectiveData.__pairs = __pb_pairs

pb.PlayerQuestObjectiveData = { __name = "PlayerQuestObjectiveData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerQuestObjectiveData : ProtoBase
---@field public quest_objective_id number
---@field public has_completed boolean
---@field public value number
---@field public has_marked boolean
---@field public spent_seconds number
---@field public extra_data pb_PlayerObjectiveExtraData

---@return pb_PlayerQuestObjectiveData
function pb.PlayerQuestObjectiveData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerRttData = {
    player_id = 0,
    rtt = 0,
    access_point = "",
    idc = "",
}
pb.__pb_PlayerRttData.__name = "PlayerRttData"
pb.__pb_PlayerRttData.__index = pb.__pb_PlayerRttData
pb.__pb_PlayerRttData.__pairs = __pb_pairs

pb.PlayerRttData = { __name = "PlayerRttData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerRttData : ProtoBase
---@field public player_id number
---@field public rtt number
---@field public access_point string
---@field public idc string

---@return pb_PlayerRttData
function pb.PlayerRttData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropGoldEgg = {
    owner = "",
    time = 0,
}
pb.__pb_PropGoldEgg.__name = "PropGoldEgg"
pb.__pb_PropGoldEgg.__index = pb.__pb_PropGoldEgg
pb.__pb_PropGoldEgg.__pairs = __pb_pairs

pb.PropGoldEgg = { __name = "PropGoldEgg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropGoldEgg : ProtoBase
---@field public owner string
---@field public time number

---@return pb_PropGoldEgg
function pb.PropGoldEgg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropInfo = {
    id = 0,
    gid = 0,
    num = 0,
    position = 0,
    source = 0,
    reason = 0,
    length = 0,
    width = 0,
    health = 0,
    health_max = 0,
    bind_type = 0,
    bind_player = 0,
    break_tag = 0,
    aging_type = 0,
    aging_begin_time = 0,
    aging_duration = 0,
    use_cnt = 0,
    use_cnt_max = 0,
    tag = 0,
    gained_time = 0,
    drop_ai_gid = 0,
    drop_point_key = "",
    drop_box_key = "",
    auction_armor_map_id = 0,
    durability_lvl = 0,
    src_id = 0,
    src_num = 0,
    expire_timestamp = 0,
    tglog_raw_gid = 0,
    tglog_src_player_id = 0,
    rights_id = 0,
    activity_prop_mode = 0,
}
pb.__pb_PropInfo.__name = "PropInfo"
pb.__pb_PropInfo.__index = pb.__pb_PropInfo
pb.__pb_PropInfo.__pairs = __pb_pairs

pb.PropInfo = { __name = "PropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropInfo : ProtoBase
---@field public id number
---@field public gid number
---@field public num number
---@field public position number
---@field public source number
---@field public reason number
---@field public length number
---@field public width number
---@field public health number
---@field public health_max number
---@field public loc pb_PropLocation
---@field public bind_type number
---@field public bind_player number
---@field public break_tag number
---@field public aging_type number
---@field public aging_begin_time number
---@field public aging_duration number
---@field public use_cnt number
---@field public use_cnt_max number
---@field public tag number
---@field public gained_time number
---@field public components pb_Component[]
---@field public record pb_PropRecord
---@field public weapon pb_WeaponInfo
---@field public hero pb_HeroAttr
---@field public drop_ai_gid number
---@field public drop_point_key string
---@field public drop_box_key string
---@field public auction_armor_map_id number
---@field public durability_lvl number
---@field public src_id number
---@field public src_num number
---@field public expire_timestamp number
---@field public mystical_skin_data pb_MysticalSkinInfo
---@field public mystical_pendant_data pb_MysticalPendantInfo
---@field public tglog_raw_gid number
---@field public tglog_src_player_id number
---@field public sources pb_PropSource[]
---@field public rights_id number
---@field public activity_prop_mode number
---@field public gold_egg_record pb_PropGoldEgg

---@return pb_PropInfo
function pb.PropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropSource = {
    id = 0,
    num = 0,
    type = 0,
    source = "",
}
pb.__pb_PropSource.__name = "PropSource"
pb.__pb_PropSource.__index = pb.__pb_PropSource
pb.__pb_PropSource.__pairs = __pb_pairs

pb.PropSource = { __name = "PropSource", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropSource : ProtoBase
---@field public id number
---@field public num number
---@field public type number
---@field public source string

---@return pb_PropSource
function pb.PropSource:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropLocation = {
    pos = 0,
    space_id = 0,
    start_x = 0,
    start_y = 0,
    x = 0,
    y = 0,
    rotate = false,
    is_newly_get = false,
}
pb.__pb_PropLocation.__name = "PropLocation"
pb.__pb_PropLocation.__index = pb.__pb_PropLocation
pb.__pb_PropLocation.__pairs = __pb_pairs

pb.PropLocation = { __name = "PropLocation", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropLocation : ProtoBase
---@field public pos number
---@field public space_id number
---@field public start_x number
---@field public start_y number
---@field public x number
---@field public y number
---@field public rotate boolean
---@field public is_newly_get boolean

---@return pb_PropLocation
function pb.PropLocation:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropRecord = {
    Owner = "",
    OwnerLevel = 0,
    RecordType = 0,
    DeathReason = 0,
    AvatarID = 0,
    Killer = "",
    KillerWeaponID = 0,
    KillTime = 0,
    KillerBulletID = 0,
    MapID = 0,
    KillTimestamp = 0,
    KillerBulletLevel = 0,
    CustomID = 0,
}
pb.__pb_PropRecord.__name = "PropRecord"
pb.__pb_PropRecord.__index = pb.__pb_PropRecord
pb.__pb_PropRecord.__pairs = __pb_pairs

pb.PropRecord = { __name = "PropRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropRecord : ProtoBase
---@field public Owner string
---@field public OwnerLevel number
---@field public RecordType number
---@field public DeathReason number
---@field public AvatarID number
---@field public Killer string
---@field public KillerWeaponID number
---@field public KillTime number
---@field public KillerBulletID number
---@field public MapID number
---@field public KillTimestamp number
---@field public KillerBulletLevel number
---@field public CustomID number

---@return pb_PropRecord
function pb.PropRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_QuestDSGameState = {
    state_key = 0,
    action_value = 0,
}
pb.__pb_QuestDSGameState.__name = "QuestDSGameState"
pb.__pb_QuestDSGameState.__index = pb.__pb_QuestDSGameState
pb.__pb_QuestDSGameState.__pairs = __pb_pairs

pb.QuestDSGameState = { __name = "QuestDSGameState", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_QuestDSGameState : ProtoBase
---@field public state_key number
---@field public action_value number

---@return pb_QuestDSGameState
function pb.QuestDSGameState:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_QuestVarEntry = {
    key = 0,
    value = 0,
}
pb.__pb_QuestVarEntry.__name = "QuestVarEntry"
pb.__pb_QuestVarEntry.__index = pb.__pb_QuestVarEntry
pb.__pb_QuestVarEntry.__pairs = __pb_pairs

pb.QuestVarEntry = { __name = "QuestVarEntry", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_QuestVarEntry : ProtoBase
---@field public key number
---@field public value number

---@return pb_QuestVarEntry
function pb.QuestVarEntry:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoleLoadValue = {
    load_level = 0,
    over_weight = 0,
    little_weight = 0,
    severe_over_weight = 0,
    middle_weight = 0,
    over_weight_v2 = 0,
}
pb.__pb_RoleLoadValue.__name = "RoleLoadValue"
pb.__pb_RoleLoadValue.__index = pb.__pb_RoleLoadValue
pb.__pb_RoleLoadValue.__pairs = __pb_pairs

pb.RoleLoadValue = { __name = "RoleLoadValue", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoleLoadValue : ProtoBase
---@field public load_level number
---@field public over_weight number
---@field public little_weight number
---@field public severe_over_weight number
---@field public middle_weight number
---@field public over_weight_v2 number
---@field public little_weight_list number[]
---@field public middle_weight_list number[]

---@return pb_RoleLoadValue
function pb.RoleLoadValue:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RttBestIdc = {
    idc = "",
    rtt = 0,
}
pb.__pb_RttBestIdc.__name = "RttBestIdc"
pb.__pb_RttBestIdc.__index = pb.__pb_RttBestIdc
pb.__pb_RttBestIdc.__pairs = __pb_pairs

pb.RttBestIdc = { __name = "RttBestIdc", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RttBestIdc : ProtoBase
---@field public idc string
---@field public rtt number
---@field public player_rtt_list pb_PlayerRttData[]

---@return pb_RttBestIdc
function pb.RttBestIdc:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SafehouseNumeralInfo = {
}
pb.__pb_SafehouseNumeralInfo.__name = "SafehouseNumeralInfo"
pb.__pb_SafehouseNumeralInfo.__index = pb.__pb_SafehouseNumeralInfo
pb.__pb_SafehouseNumeralInfo.__pairs = __pb_pairs

pb.SafehouseNumeralInfo = { __name = "SafehouseNumeralInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SafehouseNumeralInfo : ProtoBase
---@field public attr_operate_ids number[]

---@return pb_SafehouseNumeralInfo
function pb.SafehouseNumeralInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropGuidePrice = {
    prop_id = 0,
    price = 0,
}
pb.__pb_PropGuidePrice.__name = "PropGuidePrice"
pb.__pb_PropGuidePrice.__index = pb.__pb_PropGuidePrice
pb.__pb_PropGuidePrice.__pairs = __pb_pairs

pb.PropGuidePrice = { __name = "PropGuidePrice", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropGuidePrice : ProtoBase
---@field public prop_id number
---@field public price number

---@return pb_PropGuidePrice
function pb.PropGuidePrice:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SecTLogHeadComm = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AreaID = 0,
    ClientVersion = "",
}
pb.__pb_SecTLogHeadComm.__name = "SecTLogHeadComm"
pb.__pb_SecTLogHeadComm.__index = pb.__pb_SecTLogHeadComm
pb.__pb_SecTLogHeadComm.__pairs = __pb_pairs

pb.SecTLogHeadComm = { __name = "SecTLogHeadComm", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SecTLogHeadComm : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AreaID number
---@field public ClientVersion string

---@return pb_SecTLogHeadComm
function pb.SecTLogHeadComm:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SecTLogHeadDevice = {
    SystemSoftware = "",
    SystemHardware = "",
    TelecomOper = "",
    Network = "",
    DeviceId = "",
    RealIMEI = "",
    UserIP = "",
}
pb.__pb_SecTLogHeadDevice.__name = "SecTLogHeadDevice"
pb.__pb_SecTLogHeadDevice.__index = pb.__pb_SecTLogHeadDevice
pb.__pb_SecTLogHeadDevice.__pairs = __pb_pairs

pb.SecTLogHeadDevice = { __name = "SecTLogHeadDevice", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SecTLogHeadDevice : ProtoBase
---@field public SystemSoftware string
---@field public SystemHardware string
---@field public TelecomOper string
---@field public Network string
---@field public DeviceId string
---@field public RealIMEI string
---@field public UserIP string

---@return pb_SecTLogHeadDevice
function pb.SecTLogHeadDevice:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SecTLogHeadRole = {
    RoleName = "",
    RoleId = 0,
    RoleType = 0,
    RoleLevel = 0,
    RoleRankLevel = 0,
    RoleVipLevel = 0,
    RoleBattlePoint = 0,
    RoleGroupID = 0,
    RoleGroupName = "",
    PicUrl = "",
}
pb.__pb_SecTLogHeadRole.__name = "SecTLogHeadRole"
pb.__pb_SecTLogHeadRole.__index = pb.__pb_SecTLogHeadRole
pb.__pb_SecTLogHeadRole.__pairs = __pb_pairs

pb.SecTLogHeadRole = { __name = "SecTLogHeadRole", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SecTLogHeadRole : ProtoBase
---@field public RoleName string
---@field public RoleId number
---@field public RoleType number
---@field public RoleLevel number
---@field public RoleRankLevel number
---@field public RoleVipLevel number
---@field public RoleBattlePoint number
---@field public RoleGroupID number
---@field public RoleGroupName string
---@field public PicUrl string

---@return pb_SecTLogHeadRole
function pb.SecTLogHeadRole:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_StrongholdInfo = {
    stronghold_id = 0,
    state = 0,
    rewarded = false,
}
pb.__pb_StrongholdInfo.__name = "StrongholdInfo"
pb.__pb_StrongholdInfo.__index = pb.__pb_StrongholdInfo
pb.__pb_StrongholdInfo.__pairs = __pb_pairs

pb.StrongholdInfo = { __name = "StrongholdInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_StrongholdInfo : ProtoBase
---@field public stronghold_id number
---@field public state number
---@field public total_collections number[]
---@field public collections number[]
---@field public rewarded boolean
---@field public rewards pb_StrongholdReward[]

---@return pb_StrongholdInfo
function pb.StrongholdInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_StrongholdReward = {
    stronghold_id = 0,
}
pb.__pb_StrongholdReward.__name = "StrongholdReward"
pb.__pb_StrongholdReward.__index = pb.__pb_StrongholdReward
pb.__pb_StrongholdReward.__pairs = __pb_pairs

pb.StrongholdReward = { __name = "StrongholdReward", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_StrongholdReward : ProtoBase
---@field public stronghold_id number
---@field public prop pb_PropInfo

---@return pb_StrongholdReward
function pb.StrongholdReward:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBagArmedPresetDS = {
    armedforce_id = 0,
    default_bag_id = 0,
    max_bag_num = 0,
    expert_id = 0,
    def_bag_id = 0,
}
pb.__pb_TDMBagArmedPresetDS.__name = "TDMBagArmedPresetDS"
pb.__pb_TDMBagArmedPresetDS.__index = pb.__pb_TDMBagArmedPresetDS
pb.__pb_TDMBagArmedPresetDS.__pairs = __pb_pairs

pb.TDMBagArmedPresetDS = { __name = "TDMBagArmedPresetDS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBagArmedPresetDS : ProtoBase
---@field public armedforce_id number
---@field public default_bag_id number
---@field public bags pb_TDMBagDS[]
---@field public max_bag_num number
---@field public expert_id number
---@field public def_bag_id number
---@field public armed_props pb_PropInfo[]

---@return pb_TDMBagArmedPresetDS
function pb.TDMBagArmedPresetDS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBagDS = {
    bag_id = 0,
    armedforce_id = 0,
    talent_id = 0,
    expert_id = 0,
    expert_bag_id = 0,
}
pb.__pb_TDMBagDS.__name = "TDMBagDS"
pb.__pb_TDMBagDS.__index = pb.__pb_TDMBagDS
pb.__pb_TDMBagDS.__pairs = __pb_pairs

pb.TDMBagDS = { __name = "TDMBagDS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBagDS : ProtoBase
---@field public bag_id number
---@field public armedforce_id number
---@field public props pb_EquipPosition[]
---@field public talent_id number
---@field public talent pb_ArmedForceTalent
---@field public expert_id number
---@field public expert_bag_id number
---@field public equip_skin_props pb_PropInfo[]
---@field public equip_pendant_props pb_PropInfo[]

---@return pb_TDMBagDS
function pb.TDMBagDS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPLevelDesc = {
    sum_exp = 0,
}
pb.__pb_MPLevelDesc.__name = "MPLevelDesc"
pb.__pb_MPLevelDesc.__index = pb.__pb_MPLevelDesc
pb.__pb_MPLevelDesc.__pairs = __pb_pairs

pb.MPLevelDesc = { __name = "MPLevelDesc", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPLevelDesc : ProtoBase
---@field public sum_exp number
---@field public unlock_props number[]

---@return pb_MPLevelDesc
function pb.MPLevelDesc:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPLevels = {
    weapon_id = 0,
    cur_lvl = 0,
}
pb.__pb_MPLevels.__name = "MPLevels"
pb.__pb_MPLevels.__index = pb.__pb_MPLevels
pb.__pb_MPLevels.__pairs = __pb_pairs

pb.MPLevels = { __name = "MPLevels", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPLevels : ProtoBase
---@field public weapon_id number
---@field public cur_lvl number
---@field public lvl_descs pb_MPLevelDesc[]

---@return pb_MPLevels
function pb.MPLevels:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMGameMVPEvaluated = {
    dimension = 0,
    value = 0,
}
pb.__pb_TDMGameMVPEvaluated.__name = "TDMGameMVPEvaluated"
pb.__pb_TDMGameMVPEvaluated.__index = pb.__pb_TDMGameMVPEvaluated
pb.__pb_TDMGameMVPEvaluated.__pairs = __pb_pairs

pb.TDMGameMVPEvaluated = { __name = "TDMGameMVPEvaluated", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMGameMVPEvaluated : ProtoBase
---@field public dimension number
---@field public value number

---@return pb_TDMGameMVPEvaluated
function pb.TDMGameMVPEvaluated:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMMvp = {
    player_id = 0,
}
pb.__pb_TDMMvp.__name = "TDMMvp"
pb.__pb_TDMMvp.__index = pb.__pb_TDMMvp
pb.__pb_TDMMvp.__pairs = __pb_pairs

pb.TDMMvp = { __name = "TDMMvp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMMvp : ProtoBase
---@field public player_id number
---@field public evaluated_list pb_TDMGameMVPEvaluated[]

---@return pb_TDMMvp
function pb.TDMMvp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPPropPosCS = {
    pos_id = 0,
    prop_gid = 0,
    prop_id = 0,
}
pb.__pb_MPPropPosCS.__name = "MPPropPosCS"
pb.__pb_MPPropPosCS.__index = pb.__pb_MPPropPosCS
pb.__pb_MPPropPosCS.__pairs = __pb_pairs

pb.MPPropPosCS = { __name = "MPPropPosCS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPPropPosCS : ProtoBase
---@field public pos_id number
---@field public prop_gid number
---@field public prop_id number

---@return pb_MPPropPosCS
function pb.MPPropPosCS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPPresetCS = {
    bag_id = 0,
    expert_id = 0,
}
pb.__pb_MPPresetCS.__name = "MPPresetCS"
pb.__pb_MPPresetCS.__index = pb.__pb_MPPresetCS
pb.__pb_MPPresetCS.__pairs = __pb_pairs

pb.MPPresetCS = { __name = "MPPresetCS", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPPresetCS : ProtoBase
---@field public bag_id number
---@field public mp_props pb_MPPropPosCS[]
---@field public expert_id number

---@return pb_MPPresetCS
function pb.MPPresetCS:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPWeaponSettlement = {
    weapon_id = 0,
    slot_index = 0,
}
pb.__pb_MPWeaponSettlement.__name = "MPWeaponSettlement"
pb.__pb_MPWeaponSettlement.__index = pb.__pb_MPWeaponSettlement
pb.__pb_MPWeaponSettlement.__pairs = __pb_pairs

pb.MPWeaponSettlement = { __name = "MPWeaponSettlement", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPWeaponSettlement : ProtoBase
---@field public weapon_id number
---@field public slot_index number

---@return pb_MPWeaponSettlement
function pb.MPWeaponSettlement:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleSlotPartCfg = {
    part_id = 0,
    unlock_type = 0,
    unlock_para = 0,
}
pb.__pb_VehicleSlotPartCfg.__name = "VehicleSlotPartCfg"
pb.__pb_VehicleSlotPartCfg.__index = pb.__pb_VehicleSlotPartCfg
pb.__pb_VehicleSlotPartCfg.__pairs = __pb_pairs

pb.VehicleSlotPartCfg = { __name = "VehicleSlotPartCfg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleSlotPartCfg : ProtoBase
---@field public part_id number
---@field public unlock_type number
---@field public unlock_para number

---@return pb_VehicleSlotPartCfg
function pb.VehicleSlotPartCfg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleSlotCfg = {
    slot_id = 0,
}
pb.__pb_VehicleSlotCfg.__name = "VehicleSlotCfg"
pb.__pb_VehicleSlotCfg.__index = pb.__pb_VehicleSlotCfg
pb.__pb_VehicleSlotCfg.__pairs = __pb_pairs

pb.VehicleSlotCfg = { __name = "VehicleSlotCfg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleSlotCfg : ProtoBase
---@field public slot_id number
---@field public parts pb_VehicleSlotPartCfg[]

---@return pb_VehicleSlotCfg
function pb.VehicleSlotCfg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleModificationRule = {
    vehicle_id = 0,
}
pb.__pb_VehicleModificationRule.__name = "VehicleModificationRule"
pb.__pb_VehicleModificationRule.__index = pb.__pb_VehicleModificationRule
pb.__pb_VehicleModificationRule.__pairs = __pb_pairs

pb.VehicleModificationRule = { __name = "VehicleModificationRule", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleModificationRule : ProtoBase
---@field public vehicle_id number
---@field public VehicleSlotCfgs pb_VehicleSlotCfg[]

---@return pb_VehicleModificationRule
function pb.VehicleModificationRule:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMNumeral = {
    selected_armedforce_id = 0,
    selected_bag_id = 0,
    selected_expert_bag_id = 0,
    double_account_exp_rate = 0,
    double_rank_score_rate = 0,
    orgin_commander_score = 0,
}
pb.__pb_TDMNumeral.__name = "TDMNumeral"
pb.__pb_TDMNumeral.__index = pb.__pb_TDMNumeral
pb.__pb_TDMNumeral.__pairs = __pb_pairs

pb.TDMNumeral = { __name = "TDMNumeral", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMNumeral : ProtoBase
---@field public armedforce_id_list number[]
---@field public selected_armedforce_id number
---@field public selected_bag_id number
---@field public talent pb_ArmedForceTalent
---@field public bags pb_TDMBagArmedPresetDS[]
---@field public selected_expert_bag_id number
---@field public vehicle_store pb_TDMNumeral.VehicleStoreEntry[]
---@field public part_store pb_TDMNumeral.PartStoreEntry[]
---@field public vehicles pb_Vehicle[]
---@field public parts pb_VehiclePart[]
---@field public weapon_lvls pb_MPLevels[]
---@field public account_lvls pb_MPLevels
---@field public double_account_exp_rate number
---@field public double_rank_score_rate number
---@field public preset_data pb_MPPresetCS[]
---@field public armed_store pb_PropInfo[]
---@field public forbidden_prop number[]
---@field public prop_data pb_TDMNumeralProp
---@field public allow_prop number[]
---@field public vehicle_rules pb_VehicleModificationRule[]
---@field public orgin_commander_score number

---@return pb_TDMNumeral
function pb.TDMNumeral:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMWeaponDesign = {
    design_name = "",
    slot_index = 0,
    available = false,
}
pb.__pb_TDMWeaponDesign.__name = "TDMWeaponDesign"
pb.__pb_TDMWeaponDesign.__index = pb.__pb_TDMWeaponDesign
pb.__pb_TDMWeaponDesign.__pairs = __pb_pairs

pb.TDMWeaponDesign = { __name = "TDMWeaponDesign", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMWeaponDesign : ProtoBase
---@field public designs pb_PropInfo
---@field public design_name string
---@field public slot_index number
---@field public available boolean

---@return pb_TDMWeaponDesign
function pb.TDMWeaponDesign:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMNumeralProp = {
}
pb.__pb_TDMNumeralProp.__name = "TDMNumeralProp"
pb.__pb_TDMNumeralProp.__index = pb.__pb_TDMNumeralProp
pb.__pb_TDMNumeralProp.__pairs = __pb_pairs

pb.TDMNumeralProp = { __name = "TDMNumeralProp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMNumeralProp : ProtoBase
---@field public weapon_store pb_PropInfo[]
---@field public weapon_designs pb_TDMWeaponDesign[]
---@field public weapon_store_bytes string[]
---@field public weapon_designs_bytes string[]

---@return pb_TDMNumeralProp
function pb.TDMNumeralProp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMCarryoutBag = {
    bag_id = 0,
    armed_prop_id = 0,
}
pb.__pb_TDMCarryoutBag.__name = "TDMCarryoutBag"
pb.__pb_TDMCarryoutBag.__index = pb.__pb_TDMCarryoutBag
pb.__pb_TDMCarryoutBag.__pairs = __pb_pairs

pb.TDMCarryoutBag = { __name = "TDMCarryoutBag", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMCarryoutBag : ProtoBase
---@field public bag_id number
---@field public armed_prop_id number

---@return pb_TDMCarryoutBag
function pb.TDMCarryoutBag:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMTournamentScore = {
    id = 0,
    score = 0,
    result = 0,
    value_a = 0,
    value_b = 0,
    value_c = 0,
    value_d = 0,
    value_e = 0,
}
pb.__pb_TDMTournamentScore.__name = "TDMTournamentScore"
pb.__pb_TDMTournamentScore.__index = pb.__pb_TDMTournamentScore
pb.__pb_TDMTournamentScore.__pairs = __pb_pairs

pb.TDMTournamentScore = { __name = "TDMTournamentScore", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMTournamentScore : ProtoBase
---@field public id number
---@field public score number
---@field public result number
---@field public value_a number
---@field public value_b number
---@field public value_c number
---@field public value_d number
---@field public value_e number

---@return pb_TDMTournamentScore
function pb.TDMTournamentScore:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMPointWeight = {
    rule_id = 0,
    weight = 0,
    total_weight = 0,
    drop_item_id = 0,
    is_drop = false,
    val = 0,
    after_weight = 0,
    before_weight = 0,
    is_award_succ = false,
}
pb.__pb_TDMPointWeight.__name = "TDMPointWeight"
pb.__pb_TDMPointWeight.__index = pb.__pb_TDMPointWeight
pb.__pb_TDMPointWeight.__pairs = __pb_pairs

pb.TDMPointWeight = { __name = "TDMPointWeight", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMPointWeight : ProtoBase
---@field public rule_id number
---@field public weight number
---@field public total_weight number
---@field public drop_item_id number
---@field public is_drop boolean
---@field public val number
---@field public after_weight number
---@field public before_weight number
---@field public is_award_succ boolean

---@return pb_TDMPointWeight
function pb.TDMPointWeight:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMPlayer = {
    player_id = 0,
    team_mvp = false,
    score = 0,
    leave = false,
    dead_count = 0,
    killed_by_ai_count = 0,
    killed_by_player_count = 0,
    fire_count = 0,
    break_count = 0,
    capture_count = 0,
    machine_gun_use_count = 0,
    ammo_box_use_count = 0,
    kill_ai_count = 0,
    kill_player_count = 0,
    living_time = 0,
    play_time = 0,
    is_join_at_middle = false,
    assist_count = 0,
    leave_type = 0,
    game_exp = 0,
    vehicle_kill = 0,
    match_before_elo_point = 0,
    double_account_exp_rate = 0,
    double_rank_score_rate = 0,
    account_add_exp_buf = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_head_down = 0,
    total_kill_long_range = 0,
    total_capture_point_time = 0,
    total_vehicle_use_time = 0,
    total_damage_to_vehicle = 0,
    total_vehicle_kill = 0,
    total_vehicle_destroyed = 0,
    total_vehicle_repair = 0,
    cybercafe_settlement_buff = 0,
    score_rank = 0,
    is_half_join = false,
    university_settlement_buff = 0,
    valid_assist_count = 0,
    capture_flag_num = 0,
    rescue_contrib = 0,
    build_and_destroy_contrib = 0,
    capture_contrib = 0,
    tactics_contrib = 0,
    short_hang_up_triggered = 0,
    commander_contributor_title = 0,
    double_rank_act_rate = 0,
    join_at_middle_time = 0,
    sys_team_id = 0,
    vehicle_kill_count = 0,
    vehicle_play_time = 0,
    infantry_kill_count = 0,
    infantry_play_time = 0,
}
pb.__pb_TDMPlayer.__name = "TDMPlayer"
pb.__pb_TDMPlayer.__index = pb.__pb_TDMPlayer
pb.__pb_TDMPlayer.__pairs = __pb_pairs

pb.TDMPlayer = { __name = "TDMPlayer", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMPlayer : ProtoBase
---@field public player_id number
---@field public basic_info pb_DsPlayerBasicInfo
---@field public team_mvp boolean
---@field public kill_array pb_DsPlayerKillInfo[]
---@field public death_info pb_DsPlayerDeathInfo[]
---@field public stronghold_list pb_TDMStrongholdInfo[]
---@field public score number
---@field public props pb_EquipPosition[]
---@field public leave boolean
---@field public equip_use_time pb_EquipUseTime[]
---@field public dead_count number
---@field public killed_by_ai_count number
---@field public killed_by_player_count number
---@field public fire_count number
---@field public break_count number
---@field public capture_count number
---@field public machine_gun_use_count number
---@field public ammo_box_use_count number
---@field public kill_ai_count number
---@field public kill_player_count number
---@field public living_time number
---@field public play_time number
---@field public is_join_at_middle boolean
---@field public ai_info pb_DsAIBasicInfo
---@field public assist_count number
---@field public leave_type number
---@field public hero pb_DSHero
---@field public bags pb_TDMCarryoutBag[]
---@field public game_achievements pb_DsGameAchievement[]
---@field public game_medal pb_DsGameMedal[]
---@field public game_exp number
---@field public tournament_scores pb_TDMTournamentScore[]
---@field public vehicle_kill number
---@field public match_before_elo_point number
---@field public double_account_exp_rate number
---@field public double_rank_score_rate number
---@field public account_add_exp_buf number
---@field public preset_cs_list pb_MPPresetCS[]
---@field public weapon_list pb_MPWeaponSettlement[]
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_head_down number
---@field public total_kill_long_range number
---@field public total_capture_point_time number
---@field public total_vehicle_use_time number
---@field public total_damage_to_vehicle number
---@field public total_vehicle_kill number
---@field public total_vehicle_destroyed number
---@field public total_vehicle_repair number
---@field public cybercafe_settlement_buff number
---@field public score_rank number
---@field public is_half_join boolean
---@field public university_settlement_buff number
---@field public valid_assist_count number
---@field public capture_flag_num number
---@field public rescue_contrib number
---@field public build_and_destroy_contrib number
---@field public capture_contrib number
---@field public tactics_contrib number
---@field public short_hang_up_triggered number
---@field public commander_contributor_title number
---@field public double_rank_act_rate number
---@field public join_at_middle_time number
---@field public double_rank_info pb_TournamentRankDoubleInfo
---@field public sys_team_id number
---@field public vehicle_kill_count number
---@field public vehicle_play_time number
---@field public infantry_kill_count number
---@field public infantry_play_time number
---@field public od_player pb_OdTdmPlayer

---@return pb_TDMPlayer
function pb.TDMPlayer:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsGameMedal = {
    id = 0,
    level = 0,
    score = 0,
}
pb.__pb_DsGameMedal.__name = "DsGameMedal"
pb.__pb_DsGameMedal.__index = pb.__pb_DsGameMedal
pb.__pb_DsGameMedal.__pairs = __pb_pairs

pb.DsGameMedal = { __name = "DsGameMedal", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsGameMedal : ProtoBase
---@field public id number
---@field public level number
---@field public score number

---@return pb_DsGameMedal
function pb.DsGameMedal:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMPlayerMVPEvaluated = {
    dimension = 0,
    value = 0,
}
pb.__pb_TDMPlayerMVPEvaluated.__name = "TDMPlayerMVPEvaluated"
pb.__pb_TDMPlayerMVPEvaluated.__index = pb.__pb_TDMPlayerMVPEvaluated
pb.__pb_TDMPlayerMVPEvaluated.__pairs = __pb_pairs

pb.TDMPlayerMVPEvaluated = { __name = "TDMPlayerMVPEvaluated", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMPlayerMVPEvaluated : ProtoBase
---@field public dimension number
---@field public value number

---@return pb_TDMPlayerMVPEvaluated
function pb.TDMPlayerMVPEvaluated:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMStrongholdInfo = {
    stronghold_id = 0,
}
pb.__pb_TDMStrongholdInfo.__name = "TDMStrongholdInfo"
pb.__pb_TDMStrongholdInfo.__index = pb.__pb_TDMStrongholdInfo
pb.__pb_TDMStrongholdInfo.__pairs = __pb_pairs

pb.TDMStrongholdInfo = { __name = "TDMStrongholdInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMStrongholdInfo : ProtoBase
---@field public stronghold_id number

---@return pb_TDMStrongholdInfo
function pb.TDMStrongholdInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMVehicleAddExp = {
    vehicle_id = 0,
}
pb.__pb_TDMVehicleAddExp.__name = "TDMVehicleAddExp"
pb.__pb_TDMVehicleAddExp.__index = pb.__pb_TDMVehicleAddExp
pb.__pb_TDMVehicleAddExp.__pairs = __pb_pairs

pb.TDMVehicleAddExp = { __name = "TDMVehicleAddExp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMVehicleAddExp : ProtoBase
---@field public vehicle_id number
---@field public exp_list pb_TDMVehicleExp[]
---@field public unlock_part_list number[]

---@return pb_TDMVehicleAddExp
function pb.TDMVehicleAddExp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMVehicleExp = {
    reason = 0,
    exp = 0,
}
pb.__pb_TDMVehicleExp.__name = "TDMVehicleExp"
pb.__pb_TDMVehicleExp.__index = pb.__pb_TDMVehicleExp
pb.__pb_TDMVehicleExp.__pairs = __pb_pairs

pb.TDMVehicleExp = { __name = "TDMVehicleExp", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMVehicleExp : ProtoBase
---@field public reason number
---@field public exp number

---@return pb_TDMVehicleExp
function pb.TDMVehicleExp:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TgLogEntry = {
    name = "",
    pbtlog = "",
    no_autofill = false,
}
pb.__pb_TgLogEntry.__name = "TgLogEntry"
pb.__pb_TgLogEntry.__index = pb.__pb_TgLogEntry
pb.__pb_TgLogEntry.__pairs = __pb_pairs

pb.TgLogEntry = { __name = "TgLogEntry", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TgLogEntry : ProtoBase
---@field public name string
---@field public pbtlog string
---@field public no_autofill boolean

---@return pb_TgLogEntry
function pb.TgLogEntry:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TssTLogHeader = {
}
pb.__pb_TssTLogHeader.__name = "TssTLogHeader"
pb.__pb_TssTLogHeader.__index = pb.__pb_TssTLogHeader
pb.__pb_TssTLogHeader.__pairs = __pb_pairs

pb.TssTLogHeader = { __name = "TssTLogHeader", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TssTLogHeader : ProtoBase
---@field public secTLogHeadComm pb_SecTLogHeadComm
---@field public secTLogHeadDevice pb_SecTLogHeadDevice
---@field public secTLogHeadRole pb_SecTLogHeadRole

---@return pb_TssTLogHeader
function pb.TssTLogHeader:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Vehicle = {
    id = 0,
    exp = 0,
    level = 0,
    skin = 0,
}
pb.__pb_Vehicle.__name = "Vehicle"
pb.__pb_Vehicle.__index = pb.__pb_Vehicle
pb.__pb_Vehicle.__pairs = __pb_pairs

pb.Vehicle = { __name = "Vehicle", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Vehicle : ProtoBase
---@field public id number
---@field public exp number
---@field public level number
---@field public slots pb_VehicleSlot[]
---@field public skin number

---@return pb_Vehicle
function pb.Vehicle:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehiclePart = {
    id = 0,
}
pb.__pb_VehiclePart.__name = "VehiclePart"
pb.__pb_VehiclePart.__index = pb.__pb_VehiclePart
pb.__pb_VehiclePart.__pairs = __pb_pairs

pb.VehiclePart = { __name = "VehiclePart", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehiclePart : ProtoBase
---@field public id number

---@return pb_VehiclePart
function pb.VehiclePart:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MarkPropInfo = {
    mark_kind = 0,
    mark_num = 0,
    change_time = 0,
}
pb.__pb_MarkPropInfo.__name = "MarkPropInfo"
pb.__pb_MarkPropInfo.__index = pb.__pb_MarkPropInfo
pb.__pb_MarkPropInfo.__pairs = __pb_pairs

pb.MarkPropInfo = { __name = "MarkPropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MarkPropInfo : ProtoBase
---@field public prop pb_PropInfo
---@field public mark_kind number
---@field public mark_num number
---@field public change_time number

---@return pb_MarkPropInfo
function pb.MarkPropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleSlot = {
    slot = 0,
    part_id = 0,
}
pb.__pb_VehicleSlot.__name = "VehicleSlot"
pb.__pb_VehicleSlot.__index = pb.__pb_VehicleSlot
pb.__pb_VehicleSlot.__pairs = __pb_pairs

pb.VehicleSlot = { __name = "VehicleSlot", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleSlot : ProtoBase
---@field public slot number
---@field public part pb_VehiclePart
---@field public part_id number

---@return pb_VehicleSlot
function pb.VehicleSlot:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponAttrInfo = {
    tune_id = 0,
    value = 0,
}
pb.__pb_WeaponAttrInfo.__name = "WeaponAttrInfo"
pb.__pb_WeaponAttrInfo.__index = pb.__pb_WeaponAttrInfo
pb.__pb_WeaponAttrInfo.__pairs = __pb_pairs

pb.WeaponAttrInfo = { __name = "WeaponAttrInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponAttrInfo : ProtoBase
---@field public tune_id number
---@field public value number

---@return pb_WeaponAttrInfo
function pb.WeaponAttrInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPSkinComponents = {
    skin_id = 0,
}
pb.__pb_MPSkinComponents.__name = "MPSkinComponents"
pb.__pb_MPSkinComponents.__index = pb.__pb_MPSkinComponents
pb.__pb_MPSkinComponents.__pairs = __pb_pairs

pb.MPSkinComponents = { __name = "MPSkinComponents", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPSkinComponents : ProtoBase
---@field public skin_id number
---@field public components pb_PropInfo[]
---@field public component_ids number[]

---@return pb_MPSkinComponents
function pb.MPSkinComponents:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponChange = {
    ID = 0,
    old_exp = 0,
    exp = 0,
    is_reach_add_max = false,
    is_level_up = false,
    new_skin = 0,
    star_num = 0,
    cur_kill_count = 0,
    old_star_num = 0,
    old_kill_count = 0,
}
pb.__pb_WeaponChange.__name = "WeaponChange"
pb.__pb_WeaponChange.__index = pb.__pb_WeaponChange
pb.__pb_WeaponChange.__pairs = __pb_pairs

pb.WeaponChange = { __name = "WeaponChange", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponChange : ProtoBase
---@field public ID number
---@field public old_exp number
---@field public exp number
---@field public unlock_componets number[]
---@field public detail pb_WeaponExpAdd[]
---@field public is_reach_add_max boolean
---@field public is_level_up boolean
---@field public converted_expcard pb_PropInfo[]
---@field public new_skin number
---@field public unlock_componets_prop pb_PropInfo[]
---@field public skip_comps pb_MPSkinComponents[]
---@field public global_comps number[]
---@field public star_num number
---@field public cur_kill_count number
---@field public old_star_num number
---@field public old_kill_count number

---@return pb_WeaponChange
function pb.WeaponChange:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponExpAdd = {
    ID = 0,
    add_exp = 0,
    source = 0,
}
pb.__pb_WeaponExpAdd.__name = "WeaponExpAdd"
pb.__pb_WeaponExpAdd.__index = pb.__pb_WeaponExpAdd
pb.__pb_WeaponExpAdd.__pairs = __pb_pairs

pb.WeaponExpAdd = { __name = "WeaponExpAdd", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponExpAdd : ProtoBase
---@field public ID number
---@field public add_exp number
---@field public source number

---@return pb_WeaponExpAdd
function pb.WeaponExpAdd:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponStarInfo = {
    star_num = 0,
    cur_kill_count = 0,
    total_kill_count = 0,
}
pb.__pb_WeaponStarInfo.__name = "WeaponStarInfo"
pb.__pb_WeaponStarInfo.__index = pb.__pb_WeaponStarInfo
pb.__pb_WeaponStarInfo.__pairs = __pb_pairs

pb.WeaponStarInfo = { __name = "WeaponStarInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponStarInfo : ProtoBase
---@field public star_num number
---@field public cur_kill_count number
---@field public total_kill_count number

---@return pb_WeaponStarInfo
function pb.WeaponStarInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponInfo = {
    exp = 0,
    sub_preset_id = 0,
    skin_id = 0,
    magazine_capacity = 0,
    skin_gid = 0,
    pendant_id = 0,
    pendant_gid = 0,
}
pb.__pb_WeaponInfo.__name = "WeaponInfo"
pb.__pb_WeaponInfo.__index = pb.__pb_WeaponInfo
pb.__pb_WeaponInfo.__pairs = __pb_pairs

pb.WeaponInfo = { __name = "WeaponInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponInfo : ProtoBase
---@field public equipped_styles pb_WeaponStyleInfo[]
---@field public equipped_perk pb_PerkInfo
---@field public exp number
---@field public sub_preset_id number
---@field public skin_id number
---@field public attr_set pb_WeaponAttrInfo[]
---@field public magazine_capacity number
---@field public load_bullets pb_PropInfo[]
---@field public skin_gid number
---@field public pendant_id number
---@field public pendant_gid number
---@field public weapon_star_info pb_WeaponStarInfo

---@return pb_WeaponInfo
function pb.WeaponInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponSkinInfo = {
    skin_id = 0,
    color = 0,
    transmog = false,
    skin_gid = 0,
}
pb.__pb_WeaponSkinInfo.__name = "WeaponSkinInfo"
pb.__pb_WeaponSkinInfo.__index = pb.__pb_WeaponSkinInfo
pb.__pb_WeaponSkinInfo.__pairs = __pb_pairs

pb.WeaponSkinInfo = { __name = "WeaponSkinInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponSkinInfo : ProtoBase
---@field public skin_id number
---@field public color number
---@field public funcs number[]
---@field public unlocked_funcs number[]
---@field public transmog boolean
---@field public skin_gid number

---@return pb_WeaponSkinInfo
function pb.WeaponSkinInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponSkinSetup = {
    weapon_id = 0,
    skin_id = 0,
    skin_gid = 0,
    pendant_id = 0,
    pendant_gid = 0,
}
pb.__pb_WeaponSkinSetup.__name = "WeaponSkinSetup"
pb.__pb_WeaponSkinSetup.__index = pb.__pb_WeaponSkinSetup
pb.__pb_WeaponSkinSetup.__pairs = __pb_pairs

pb.WeaponSkinSetup = { __name = "WeaponSkinSetup", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponSkinSetup : ProtoBase
---@field public weapon_id number
---@field public skin_id number
---@field public skin_gid number
---@field public pendant_id number
---@field public pendant_gid number

---@return pb_WeaponSkinSetup
function pb.WeaponSkinSetup:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponStyleInfo = {
    style_id = 0,
    slot_id = 0,
}
pb.__pb_WeaponStyleInfo.__name = "WeaponStyleInfo"
pb.__pb_WeaponStyleInfo.__index = pb.__pb_WeaponStyleInfo
pb.__pb_WeaponStyleInfo.__pairs = __pb_pairs

pb.WeaponStyleInfo = { __name = "WeaponStyleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponStyleInfo : ProtoBase
---@field public style_id number
---@field public slot_id number

---@return pb_WeaponStyleInfo
function pb.WeaponStyleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WorldActorContent = {
    content_id = "",
}
pb.__pb_WorldActorContent.__name = "WorldActorContent"
pb.__pb_WorldActorContent.__index = pb.__pb_WorldActorContent
pb.__pb_WorldActorContent.__pairs = __pb_pairs

pb.WorldActorContent = { __name = "WorldActorContent", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WorldActorContent : ProtoBase
---@field public content_id string
---@field public prop pb_PropInfo

---@return pb_WorldActorContent
function pb.WorldActorContent:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WorldActorInfo = {
    actor_id = 0,
    state = 0,
}
pb.__pb_WorldActorInfo.__name = "WorldActorInfo"
pb.__pb_WorldActorInfo.__index = pb.__pb_WorldActorInfo
pb.__pb_WorldActorInfo.__pairs = __pb_pairs

pb.WorldActorInfo = { __name = "WorldActorInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WorldActorInfo : ProtoBase
---@field public actor_id number
---@field public state number
---@field public contents pb_WorldActorContent[]

---@return pb_WorldActorInfo
function pb.WorldActorInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CsvTableRow = {
}
pb.__pb_CsvTableRow.__name = "CsvTableRow"
pb.__pb_CsvTableRow.__index = pb.__pb_CsvTableRow
pb.__pb_CsvTableRow.__pairs = __pb_pairs

pb.CsvTableRow = { __name = "CsvTableRow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CsvTableRow : ProtoBase
---@field public values string[]

---@return pb_CsvTableRow
function pb.CsvTableRow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CsvTableData = {
    csv_name = "",
}
pb.__pb_CsvTableData.__name = "CsvTableData"
pb.__pb_CsvTableData.__index = pb.__pb_CsvTableData
pb.__pb_CsvTableData.__pairs = __pb_pairs

pb.CsvTableData = { __name = "CsvTableData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CsvTableData : ProtoBase
---@field public csv_name string
---@field public fields string[]
---@field public lines pb_CsvTableRow[]

---@return pb_CsvTableData
function pb.CsvTableData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CsvZipTable = {
    compress = false,
    csv = "",
    csv_name = "",
    hash = "",
    origin_csv_size = 0,
}
pb.__pb_CsvZipTable.__name = "CsvZipTable"
pb.__pb_CsvZipTable.__index = pb.__pb_CsvZipTable
pb.__pb_CsvZipTable.__pairs = __pb_pairs

pb.CsvZipTable = { __name = "CsvZipTable", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CsvZipTable : ProtoBase
---@field public compress boolean
---@field public csv string
---@field public csv_name string
---@field public hash string
---@field public origin_csv_size number

---@return pb_CsvZipTable
function pb.CsvZipTable:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CsvZipPkg = {
    seq = 0,
    idx = 0,
    total = 0,
}
pb.__pb_CsvZipPkg.__name = "CsvZipPkg"
pb.__pb_CsvZipPkg.__index = pb.__pb_CsvZipPkg
pb.__pb_CsvZipPkg.__pairs = __pb_pairs

pb.CsvZipPkg = { __name = "CsvZipPkg", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CsvZipPkg : ProtoBase
---@field public tables pb_CsvZipTable[]
---@field public seq number
---@field public idx number
---@field public total number

---@return pb_CsvZipPkg
function pb.CsvZipPkg:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CsvNumeralData = {
}
pb.__pb_CsvNumeralData.__name = "CsvNumeralData"
pb.__pb_CsvNumeralData.__index = pb.__pb_CsvNumeralData
pb.__pb_CsvNumeralData.__pairs = __pb_pairs

pb.CsvNumeralData = { __name = "CsvNumeralData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CsvNumeralData : ProtoBase
---@field public tables pb_CsvTableData[]
---@field public zip_tables pb_CsvZipTable[]

---@return pb_CsvNumeralData
function pb.CsvNumeralData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsDeathDetailedInfo = {
    helmet_item_id = 0,
    helmet_health = 0,
    helmet_health_max = 0,
    helmet_src_health_max = 0,
    breastplate_item_id = 0,
    breastplate_health = 0,
    breastplate_health_max = 0,
    breastplate_src_health_max = 0,
    maker_is_robot_ai = false,
    maker_is_ailab_ai = false,
    maker_rank_match_score = 0,
    last_bullet_hp_damage = 0,
    last_bullet_armor_damage = 0,
    damage_part = 0,
    hero_id = 0,
}
pb.__pb_DsDeathDetailedInfo.__name = "DsDeathDetailedInfo"
pb.__pb_DsDeathDetailedInfo.__index = pb.__pb_DsDeathDetailedInfo
pb.__pb_DsDeathDetailedInfo.__pairs = __pb_pairs

pb.DsDeathDetailedInfo = { __name = "DsDeathDetailedInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsDeathDetailedInfo : ProtoBase
---@field public helmet_item_id number
---@field public helmet_health number
---@field public helmet_health_max number
---@field public helmet_src_health_max number
---@field public breastplate_item_id number
---@field public breastplate_health number
---@field public breastplate_health_max number
---@field public breastplate_src_health_max number
---@field public maker_is_robot_ai boolean
---@field public maker_is_ailab_ai boolean
---@field public maker_rank_match_score number
---@field public last_weapon_info pb_PropInfo
---@field public last_bullet_hp_damage number
---@field public last_bullet_armor_damage number
---@field public damage_part number
---@field public hero_id number

---@return pb_DsDeathDetailedInfo
function pb.DsDeathDetailedInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsHurtDetailedInfo = {
    maker_player_uin = 0,
    maker_player_name = "",
    hp_damage = 0,
    armor_damage = 0,
    armor_item_id = 0,
    charater_part = 0,
    distance = 0,
    skill_id = 0,
    weapon_id = 0,
    ammo_id = 0,
    attacker_level = 0,
    buff_id = 0,
    attacker_value_id = 0,
    bAttacker = false,
    taker_hero_id = 0,
    taker_player_name = "",
    taker_health_percent = 0,
    hp_damage_percent = 0,
    taker_max_health = 0,
    taker_base_max_health = 0,
    is_penetrating_damage = false,
    penetration_level_decrease = 0,
    taker_health_after = 0,
}
pb.__pb_DsHurtDetailedInfo.__name = "DsHurtDetailedInfo"
pb.__pb_DsHurtDetailedInfo.__index = pb.__pb_DsHurtDetailedInfo
pb.__pb_DsHurtDetailedInfo.__pairs = __pb_pairs

pb.DsHurtDetailedInfo = { __name = "DsHurtDetailedInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsHurtDetailedInfo : ProtoBase
---@field public maker_player_uin number
---@field public maker_player_name string
---@field public hp_damage number
---@field public armor_damage number
---@field public armor_item_id number
---@field public charater_part number
---@field public character_parts number[]
---@field public distance number
---@field public skill_id number
---@field public weapon_id number
---@field public ammo_id number
---@field public attacker_level number
---@field public buff_id number
---@field public attacker_value_id number
---@field public bAttacker boolean
---@field public taker_hero_id number
---@field public taker_player_name string
---@field public taker_health_percent number
---@field public hp_damage_percent number
---@field public taker_max_health number
---@field public taker_base_max_health number
---@field public is_penetrating_damage boolean
---@field public penetration_level_decrease number
---@field public taker_health_after number

---@return pb_DsHurtDetailedInfo
function pb.DsHurtDetailedInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_BhdMissionRecord = {
    chapter_id = 0,
    rating = 0,
    time_cost = 0,
    progress = 0,
    member_num = 0,
    finish_time = 0,
    rating_level = 0,
    kill_count = 0,
    damage = 0,
    rescue_count = 0,
    is_dropout = 0,
    loadout_id = 0,
    role_id = 0,
}
pb.__pb_BhdMissionRecord.__name = "BhdMissionRecord"
pb.__pb_BhdMissionRecord.__index = pb.__pb_BhdMissionRecord
pb.__pb_BhdMissionRecord.__pairs = __pb_pairs

pb.BhdMissionRecord = { __name = "BhdMissionRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_BhdMissionRecord : ProtoBase
---@field public chapter_id number
---@field public rating number
---@field public time_cost number
---@field public progress number
---@field public member_num number
---@field public finish_time number
---@field public rating_level number
---@field public kill_count number
---@field public damage number
---@field public rescue_count number
---@field public is_dropout number
---@field public achievements number[]
---@field public loadout_id number
---@field public role_id number

---@return pb_BhdMissionRecord
function pb.BhdMissionRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_BhdSettlementData = {
    team_kill = 0,
    team_damage = 0,
    team_rescue = 0,
    team_alive_count = 0,
    team_id = 0,
}
pb.__pb_BhdSettlementData.__name = "BhdSettlementData"
pb.__pb_BhdSettlementData.__index = pb.__pb_BhdSettlementData
pb.__pb_BhdSettlementData.__pairs = __pb_pairs

pb.BhdSettlementData = { __name = "BhdSettlementData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_BhdSettlementData : ProtoBase
---@field public mission_record pb_BhdMissionRecord
---@field public team_kill number
---@field public team_damage number
---@field public team_rescue number
---@field public team_alive_count number
---@field public team_id number

---@return pb_BhdSettlementData
function pb.BhdSettlementData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArenaSettlementData = {
    result = 0,
    my_team_id = 0,
}
pb.__pb_ArenaSettlementData.__name = "ArenaSettlementData"
pb.__pb_ArenaSettlementData.__index = pb.__pb_ArenaSettlementData
pb.__pb_ArenaSettlementData.__pairs = __pb_pairs

pb.ArenaSettlementData = { __name = "ArenaSettlementData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArenaSettlementData : ProtoBase
---@field public result number
---@field public teams pb_ArenaTeam[]
---@field public my_team_id number
---@field public rounds pb_ArenaRound[]
---@field public knock_down_array pb_DsPlayerKillInfo[]
---@field public shoot_down_info_list pb_DsPlayerKillInfo[]

---@return pb_ArenaSettlementData
function pb.ArenaSettlementData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArenaRound = {
    round_id = 0,
    result = 0,
    win_team = 0,
    round_time = 0,
}
pb.__pb_ArenaRound.__name = "ArenaRound"
pb.__pb_ArenaRound.__index = pb.__pb_ArenaRound
pb.__pb_ArenaRound.__pairs = __pb_pairs

pb.ArenaRound = { __name = "ArenaRound", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArenaRound : ProtoBase
---@field public round_id number
---@field public result number
---@field public win_team number
---@field public round_time number

---@return pb_ArenaRound
function pb.ArenaRound:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArenaTeam = {
    team_id = 0,
    score = 0,
}
pb.__pb_ArenaTeam.__name = "ArenaTeam"
pb.__pb_ArenaTeam.__index = pb.__pb_ArenaTeam
pb.__pb_ArenaTeam.__pairs = __pb_pairs

pb.ArenaTeam = { __name = "ArenaTeam", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArenaTeam : ProtoBase
---@field public team_id number
---@field public players pb_ArenaPlayer[]
---@field public score number

---@return pb_ArenaTeam
function pb.ArenaTeam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArenaPlayer = {
    is_leavel = false,
    kill_player_count = 0,
    kill_ai_count = 0,
    dead_count = 0,
    assist_count = 0,
    rank = 0,
    play_time = 0,
}
pb.__pb_ArenaPlayer.__name = "ArenaPlayer"
pb.__pb_ArenaPlayer.__index = pb.__pb_ArenaPlayer
pb.__pb_ArenaPlayer.__pairs = __pb_pairs

pb.ArenaPlayer = { __name = "ArenaPlayer", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArenaPlayer : ProtoBase
---@field public basic_info pb_DsPlayerBasicInfo
---@field public is_leavel boolean
---@field public kill_player_count number
---@field public kill_ai_count number
---@field public dead_count number
---@field public assist_count number
---@field public final_props pb_EquipPosition[]
---@field public hero pb_DSHero
---@field public rank number
---@field public play_time number

---@return pb_ArenaPlayer
function pb.ArenaPlayer:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SOLGuideInstructionInfo = {
    ds_room_id = 0,
    original_equipment_price_without_keychain = 0,
    carry_out_ai_price = 0,
    carry_out_ai_slot = 0,
    carry_out_boss_price = 0,
    carry_out_boss_slot = 0,
    carry_out_contain_price = 0,
    carry_out_contain_slot = 0,
    carry_out_loot_price = 0,
    carry_out_loot_slot = 0,
    slot_num = 0,
    escape_fail_reason = 0,
    carry_out_profit_price = 0,
    looting_cnt = 0,
    looting_cnt_pve = 0,
    looting_cnt_pvp = 0,
    looting_cnt_container = 0,
    looting_cnt_drop_point = 0,
    loot_combat_supplies_slot = 0,
    loot_consumables_slot = 0,
    loot_collections_slot = 0,
    use_rental_props = false,
    carry_in_armor_level = 0,
    carry_in_ammo_weighted_level = 0,
    contract_num = 0,
    rescue_count = 0,
    revive_count = 0,
    carry_in_helmet_level = 0,
    carry_in_armor_repair = 0,
    carry_in_helmet_repair = 0,
    has_teammate_need_rescue = false,
    team_type = 0,
    carry_out_enemy_price = 0,
}
pb.__pb_SOLGuideInstructionInfo.__name = "SOLGuideInstructionInfo"
pb.__pb_SOLGuideInstructionInfo.__index = pb.__pb_SOLGuideInstructionInfo
pb.__pb_SOLGuideInstructionInfo.__pairs = __pb_pairs

pb.SOLGuideInstructionInfo = { __name = "SOLGuideInstructionInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SOLGuideInstructionInfo : ProtoBase
---@field public ds_room_id number
---@field public match_info pb_MatchModeInfo
---@field public original_equipment_price_without_keychain number
---@field public carry_out_ai_price number
---@field public carry_out_ai_slot number
---@field public carry_out_boss_price number
---@field public carry_out_boss_slot number
---@field public carry_out_contain_price number
---@field public carry_out_contain_slot number
---@field public carry_out_loot_price number
---@field public carry_out_loot_slot number
---@field public slot_num number
---@field public escape_fail_reason number
---@field public carry_out_profit_price number
---@field public poi_info pb_PoiInfo[]
---@field public looting_cnt number
---@field public looting_cnt_pve number
---@field public looting_cnt_pvp number
---@field public looting_cnt_container number
---@field public looting_cnt_drop_point number
---@field public loot_combat_supplies_slot number
---@field public loot_consumables_slot number
---@field public loot_collections_slot number
---@field public poi_stay_info pb_PoiStayInfo[]
---@field public use_rental_props boolean
---@field public kick_down_info pb_KickDownInfo[]
---@field public carry_in_armor_level number
---@field public carry_in_ammo_weighted_level number
---@field public contract_num number
---@field public rescue_count number
---@field public revive_count number
---@field public carry_in_helmet_level number
---@field public carry_in_armor_repair number
---@field public carry_in_helmet_repair number
---@field public has_teammate_need_rescue boolean
---@field public team_type number
---@field public carry_out_enemy_price number

---@return pb_SOLGuideInstructionInfo
function pb.SOLGuideInstructionInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_KickDownInfo = {
    ammo_level = 0,
    armor_level = 0,
}
pb.__pb_KickDownInfo.__name = "KickDownInfo"
pb.__pb_KickDownInfo.__index = pb.__pb_KickDownInfo
pb.__pb_KickDownInfo.__pairs = __pb_pairs

pb.KickDownInfo = { __name = "KickDownInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_KickDownInfo : ProtoBase
---@field public ammo_level number
---@field public armor_level number

---@return pb_KickDownInfo
function pb.KickDownInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PoiInfo = {
    poi_name = "",
    cur_server_time = 0,
    is_enter = false,
}
pb.__pb_PoiInfo.__name = "PoiInfo"
pb.__pb_PoiInfo.__index = pb.__pb_PoiInfo
pb.__pb_PoiInfo.__pairs = __pb_pairs

pb.PoiInfo = { __name = "PoiInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PoiInfo : ProtoBase
---@field public poi_name string
---@field public cur_server_time number
---@field public is_enter boolean

---@return pb_PoiInfo
function pb.PoiInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PoiStayInfo = {
    poi_name = "",
    stay_time = 0,
    enter_cnt = 0,
}
pb.__pb_PoiStayInfo.__name = "PoiStayInfo"
pb.__pb_PoiStayInfo.__index = pb.__pb_PoiStayInfo
pb.__pb_PoiStayInfo.__pairs = __pb_pairs

pb.PoiStayInfo = { __name = "PoiStayInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PoiStayInfo : ProtoBase
---@field public poi_name string
---@field public stay_time number
---@field public enter_cnt number

---@return pb_PoiStayInfo
function pb.PoiStayInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomsvrNumeral = {
    deposit_price = 0,
    mandel_brick_num = 0,
    spawn_point_type = 0,
    mandel_brick_win_num = 0,
    match_time_scale = 0,
    is_open_match_retreat = 0,
}
pb.__pb_RoomsvrNumeral.__name = "RoomsvrNumeral"
pb.__pb_RoomsvrNumeral.__index = pb.__pb_RoomsvrNumeral
pb.__pb_RoomsvrNumeral.__pairs = __pb_pairs

pb.RoomsvrNumeral = { __name = "RoomsvrNumeral", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomsvrNumeral : ProtoBase
---@field public deposit_price number
---@field public mandel_brick_num number
---@field public spawn_point_type number
---@field public mandel_brick_win_num number
---@field public match_time_scale number
---@field public is_open_match_retreat number

---@return pb_RoomsvrNumeral
function pb.RoomsvrNumeral:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomsvrParam = {
    match_sequence = 0,
    open_boss = 0,
    mandel_brick_win_num = 0,
    match_time_scale = 0,
    is_open_match_retreat = 0,
}
pb.__pb_RoomsvrParam.__name = "RoomsvrParam"
pb.__pb_RoomsvrParam.__index = pb.__pb_RoomsvrParam
pb.__pb_RoomsvrParam.__pairs = __pb_pairs

pb.RoomsvrParam = { __name = "RoomsvrParam", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomsvrParam : ProtoBase
---@field public match_sequence number
---@field public open_boss number
---@field public iris_event_ids number[]
---@field public mandel_brick_win_num number
---@field public match_time_scale number
---@field public is_open_match_retreat number

---@return pb_RoomsvrParam
function pb.RoomsvrParam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPWeaponStatistics = {
    kill_count = 0,
    weapon_id = 0,
}
pb.__pb_MPWeaponStatistics.__name = "MPWeaponStatistics"
pb.__pb_MPWeaponStatistics.__index = pb.__pb_MPWeaponStatistics
pb.__pb_MPWeaponStatistics.__pairs = __pb_pairs

pb.MPWeaponStatistics = { __name = "MPWeaponStatistics", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPWeaponStatistics : ProtoBase
---@field public kill_count number
---@field public weapon_id number

---@return pb_MPWeaponStatistics
function pb.MPWeaponStatistics:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMatchBaseRecord = {
    room_id = 0,
    team_id = 0,
    game_time = 0,
    match_time = 0,
    match_start_time = 0,
    game_result = 0,
    hero_id = 0,
    military_rank = 0,
    armedforce_id = 0,
    is_ranked_match = false,
    ranked_score_delta = 0,
    ranked_score = 0,
    ranked_level = 0,
    ranked_level_old = 0,
    ranked_score_season_no = 0,
    ranked_coins = 0,
    client_group = 0,
}
pb.__pb_DsMatchBaseRecord.__name = "DsMatchBaseRecord"
pb.__pb_DsMatchBaseRecord.__index = pb.__pb_DsMatchBaseRecord
pb.__pb_DsMatchBaseRecord.__pairs = __pb_pairs

pb.DsMatchBaseRecord = { __name = "DsMatchBaseRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMatchBaseRecord : ProtoBase
---@field public room_id number
---@field public team_id number
---@field public game_time number
---@field public match_time number
---@field public match_start_time number
---@field public game_result number
---@field public match_mode pb_MatchModeInfo
---@field public achievements pb_DsGameAchievement[]
---@field public sol pb_DsMatchBaseRecordIrisSOL
---@field public hero_id number
---@field public military_rank number
---@field public armedforce_id number
---@field public is_ranked_match boolean
---@field public ranked_score_delta number
---@field public ranked_score number
---@field public ranked_level number
---@field public ranked_level_old number
---@field public ranked_score_season_no number
---@field public ranked_coins number
---@field public client_group number

---@return pb_DsMatchBaseRecord
function pb.DsMatchBaseRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMatchBaseRecordIrisSOL = {
    collection_price = 0,
    damage = 0,
    rescue = 0,
    healing = 0,
    killPlayer = 0,
    killAI = 0,
    killBoss = 0,
    team_mate_price = 0,
    leave = false,
    total_price = 0,
    gained_price = 0,
    hero_id = 0,
    killer_type = 0,
    weapon = 0,
    revive = 0,
    assist_cnt = 0,
    total_shoot = 0,
    total_shoot_hit = 0,
    total_shoot_down = 0,
    total_shoot_head_down = 0,
    total_contract_price = 0,
    total_bring_mandel_brick = 0,
    total_bring_gold_sku = 0,
    total_bring_red_sku = 0,
    total_search_cnt = 0,
    total_mileage = 0,
    begin_game_price = 0,
}
pb.__pb_DsMatchBaseRecordIrisSOL.__name = "DsMatchBaseRecordIrisSOL"
pb.__pb_DsMatchBaseRecordIrisSOL.__index = pb.__pb_DsMatchBaseRecordIrisSOL
pb.__pb_DsMatchBaseRecordIrisSOL.__pairs = __pb_pairs

pb.DsMatchBaseRecordIrisSOL = { __name = "DsMatchBaseRecordIrisSOL", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMatchBaseRecordIrisSOL : ProtoBase
---@field public collection_price number
---@field public damage number
---@field public rescue number
---@field public healing number
---@field public killPlayer number
---@field public killAI number
---@field public killBoss number
---@field public team_mate_price number
---@field public leave boolean
---@field public total_price number
---@field public gained_price number
---@field public hero_id number
---@field public killer_type number
---@field public player pb_DsPlayerBasicInfo
---@field public ai pb_DsAIBasicInfo
---@field public boss pb_DsAIBasicInfo
---@field public weapon number
---@field public revive number
---@field public assist_cnt number
---@field public total_shoot number
---@field public total_shoot_hit number
---@field public total_shoot_down number
---@field public total_shoot_head_down number
---@field public total_contract_price number
---@field public total_bring_mandel_brick number
---@field public total_bring_gold_sku number
---@field public total_bring_red_sku number
---@field public total_search_cnt number
---@field public total_mileage number
---@field public begin_game_price number

---@return pb_DsMatchBaseRecordIrisSOL
function pb.DsMatchBaseRecordIrisSOL:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RescueInfo = {
    type = 0,
    time_point = 0,
    rescued_player_id = 0,
}
pb.__pb_RescueInfo.__name = "RescueInfo"
pb.__pb_RescueInfo.__index = pb.__pb_RescueInfo
pb.__pb_RescueInfo.__pairs = __pb_pairs

pb.RescueInfo = { __name = "RescueInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RescueInfo : ProtoBase
---@field public type number
---@field public time_point number
---@field public rescued_player_id number

---@return pb_RescueInfo
function pb.RescueInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HelpTeammateCarryOutInfo = {
    helped_player_id = 0,
    help_carryout_price = 0,
}
pb.__pb_HelpTeammateCarryOutInfo.__name = "HelpTeammateCarryOutInfo"
pb.__pb_HelpTeammateCarryOutInfo.__index = pb.__pb_HelpTeammateCarryOutInfo
pb.__pb_HelpTeammateCarryOutInfo.__pairs = __pb_pairs

pb.HelpTeammateCarryOutInfo = { __name = "HelpTeammateCarryOutInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HelpTeammateCarryOutInfo : ProtoBase
---@field public helped_player_id number
---@field public help_carryout_price number

---@return pb_HelpTeammateCarryOutInfo
function pb.HelpTeammateCarryOutInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_KillAssistantInfo = {
    type = 0,
    time_point = 0,
    killed_player_id = 0,
    killed_team_id = 0,
    is_kill_team = false,
    is_knock_down = false,
    direct_dead = false,
}
pb.__pb_KillAssistantInfo.__name = "KillAssistantInfo"
pb.__pb_KillAssistantInfo.__index = pb.__pb_KillAssistantInfo
pb.__pb_KillAssistantInfo.__pairs = __pb_pairs

pb.KillAssistantInfo = { __name = "KillAssistantInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_KillAssistantInfo : ProtoBase
---@field public type number
---@field public time_point number
---@field public killed_player_id number
---@field public killed_team_id number
---@field public killed_position pb_DsPlayerPosition
---@field public is_kill_team boolean
---@field public is_knock_down boolean
---@field public direct_dead boolean

---@return pb_KillAssistantInfo
function pb.KillAssistantInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DropPickupItemInfo = {
    type = 0,
    time_point = 0,
    item_id = 0,
    item_num = 0,
    item_total_price = 0,
    interaction_teammate_id = 0,
}
pb.__pb_DropPickupItemInfo.__name = "DropPickupItemInfo"
pb.__pb_DropPickupItemInfo.__index = pb.__pb_DropPickupItemInfo
pb.__pb_DropPickupItemInfo.__pairs = __pb_pairs

pb.DropPickupItemInfo = { __name = "DropPickupItemInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DropPickupItemInfo : ProtoBase
---@field public type number
---@field public time_point number
---@field public item_id number
---@field public item_num number
---@field public item_total_price number
---@field public interaction_teammate_id number

---@return pb_DropPickupItemInfo
function pb.DropPickupItemInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsTssReportData = {
    report_scence = 0,
    report_category = 0,
    reported_player_id = 0,
    battle_id = 0,
    battle_time = 0,
    reported_profile_url = "",
    report_desc = "",
    report_content = "",
    entrance = 0,
    nick_name = "",
    language_id = 0,
    report_relationship = 0,
}
pb.__pb_DsTssReportData.__name = "DsTssReportData"
pb.__pb_DsTssReportData.__index = pb.__pb_DsTssReportData
pb.__pb_DsTssReportData.__pairs = __pb_pairs

pb.DsTssReportData = { __name = "DsTssReportData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsTssReportData : ProtoBase
---@field public report_scence number
---@field public report_category number
---@field public report_reason number[]
---@field public reported_player_id number
---@field public battle_id number
---@field public battle_time number
---@field public reported_profile_url string
---@field public report_desc string
---@field public report_content string
---@field public entrance number
---@field public nick_name string
---@field public language_id number
---@field public report_relationship number

---@return pb_DsTssReportData
function pb.DsTssReportData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


