----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityTaraPop1 : LuaUIBaseView
local ActivityTaraPop1 = ui("ActivityTaraPop1")
local ActivityTaraItem5 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem5"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local Config = Module.Activity.Config

--选择
function ActivityTaraPop1:Ctor()
    self._wtItems = {
        [1]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_C_73" , ActivityTaraItem5),
        [2]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_1",     ActivityTaraItem5),
        [3]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_C_71",  ActivityTaraItem5),
        [4]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_C_69",  ActivityTaraItem5),
        [5]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_3",     ActivityTaraItem5),
        [6]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_4",     ActivityTaraItem5),
        [7]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_5",     ActivityTaraItem5),
        [8]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_6",     ActivityTaraItem5),
        [9]  = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_7",     ActivityTaraItem5),
        [10] = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_8",     ActivityTaraItem5),
        [11] = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_9",     ActivityTaraItem5),
        [12] = self:Wnd("WBP_PatrolAsala_BloggersItem_Q1ZNK_10",    ActivityTaraItem5),
    }
    self._wtTitle = self:Wnd("DFTextBlock_48",    UITextBlock)
    self._wtFName = self:Wnd("DFTextBlock",       UITextBlock)
    self._wtFDesc = self:Wnd("DFRichTextBlock_0", UITextBlock)

    self._wtDFVerticalBox = self:Wnd("DFVerticalBox_1", UIWidgetBase)

    self._wtBgImg = self:Wnd("DFCDNImage_0"     , DFCDNImage)
    --禁用ESC返回
    Module.CommonBar:RegStackUIDefaultEsc(self, false)
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
end

function ActivityTaraPop1:OnInitExtraData(activityID, items)
    self._activityID = activityID
    self._items = items
end

function ActivityTaraPop1:_InitPanel()
    if self._items then
        self._wtTitle:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self._wtFName:SetText(Config.Loc.Asala)
        self._wtFDesc:SetText(Config.Loc.ChooseBlogger)
        self:_RemoveHandle()
        if IsHD() then
            self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtDFVerticalBox, self, "Hittest")
        end
        for index, item in ipairs(self._wtItems or {}) do
            local hero = self._items[index]
            if hero and hero.state ~= 0 then
                self._wtTitle:SetVisibility(ESlateVisibility.Collapsed)
                if hero.rawState == 1 then
                    self._wtFName:SetText(ActivityLogic.GetText(hero.name))
                    self._wtFDesc:SetText(Config.Loc.CompletedByPeers)
                end
            end
            if hero then
                item:InitData(self._activityID, hero, index, self._items)
                item:IsShowProgressBar(false)
                item:SetVisibility(ESlateVisibility.Visible)
                if isvalid(self._navGroup) then
                    self._navGroup:AddNavWidgetToArray(item)
                end
            else
                item:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
        if isvalid(self._navGroup) then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        end
        --cdn
        local path = "C=026DB08ABE849E24E65D70E201AFF16C701A0DAD26D31864EBA4D3876123AA54501A82B787A6D89F6218F312981CC45B9711668FA865990DD92CA5DDBF5D94AD.jpg"
        if path then
            local url = string.format("Resource/Texture/Activity/%s", path)
            self._wtBgImg:SetCDNImage(url, true, Module.CDNIcon.Config.ECdnTagEnum.Activity)
        end
    end
end

function ActivityTaraPop1:_RemoveHandle()
    if IsHD() then
        self._navGroup = nil
        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
end

function ActivityTaraPop1:OnShowBegin()
    self:_AddEventListener()
    self:_InitPanel()
end

function ActivityTaraPop1:_AddEventListener()
    --手柄适配
    self:AddLuaEvent(Config.evtRefreshTaraPanel, self._OnReqSwitchHero, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActTaraTrainingTravel, self._OnActTaraTrainingTravel, self)
end

function ActivityTaraPop1:_OnReqSwitchHero(activityID, data, index)
    if activityID == self._activityID and data then
        if data.state == 0 then
            Server.ActivityServer:SendTaraSwitchReq(activityID, data.heroId)
        end
    end
end

function ActivityTaraPop1:_OnActTaraTrainingTravel()
    Facade.UIManager:CloseUI(self)
end

function ActivityTaraPop1:OnHideBegin()
    self:RemoveAllLuaEvent()
    self:_RemoveHandle()
    Module.ItemDetail:CloseItemDetailPanel()
end

function ActivityTaraPop1:OnClose()
    self._wtItems = nil
end

function ActivityTaraPop1:OnNavBack()
    local a =1
    return false
end

--初始化手柄适配🎮
function ActivityTaraPop1:_InitHandleAdaptation()
    if IsHD() then
        self:_RemoveHandleAdaptation()
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if isvalid(self._navGroup) then
            self._navGroup:AddNavWidgetToArray(self)
            -- self._navGroup:SetScrollRecipient(self._wtDFBox)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        end
    end
end

--添加手柄适配🎮
function ActivityTaraPop1:_AddHandleAdaptation(widget)
    if IsHD() then
        if isvalid(self._navGroup) and widget then
            self._navGroup:AddNavWidgetToArray(widget)
        end
    end
end

--移除手柄适配🎮
function ActivityTaraPop1:_RemoveHandleAdaptation()
    if IsHD() then
        self._navGroup = nil
        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        --清空导航栏适配
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end

return ActivityTaraPop1