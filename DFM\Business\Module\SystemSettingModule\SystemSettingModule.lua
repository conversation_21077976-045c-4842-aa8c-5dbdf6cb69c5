----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingModule : ModuleBase
local SystemSettingModule = class("SystemSettingModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"
local DisplaySettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.DisplaySettingLogic"
local SystemSettingLoadLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLoadLogic"
local SensitivitySettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SensitivitySettingLogic"
local VolumeSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.VolumeSettingLogic"

local SystemSettingEvtLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingEvtLogic"
local BaseSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.BaseSettingLogic"
local solSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SolSettingLogic"
local SettingDataTableLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingDataTableLogicHD"
local EGameBuildConfiguration = import "EGameBuildConfiguration"

--local EDisplayStyleType = import "EDisplayStyleType"

local UClientBaseSetting = import "ClientBaseSetting"
local UClientControlSetting = import "ClientControlSetting"
local UClientVehicleSetting = import "ClientVehicleSetting"
local UClientSensitivitySetting = import "ClientSensitivitySetting"
local UClientSolSetting = import "ClientSolSetting"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local ESensitivityMode = import "ESensitivityMode"

local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local MiscSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.MiscSettingLogicHD"
local VideoSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VideoSettingLogicHD"
local EPerfGearQualityLevel = import "EPerfGearQualityLevel"
local UWeaponAssembleSubsystem = import "WeaponAssembleSubsystem"

local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"

local UMobileCustomLayoutDataCenter = import "MobileCustomLayoutDataCenter"
local MobileCustomLayoutDataCenter = UMobileCustomLayoutDataCenter.Get(GetGameInstance())

local UDFMSystemSettingHelper = import "DFMSystemSettingHelper"
local DFMSystemSettingHelper = UDFMSystemSettingHelper.Get(GetWorld())

local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local Json = JsonFactory.createJson()

local UGPGameHudDelegates = import "GPGameHudDelegates"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local EGameHUDState = import "GameHUDSate"
local BHDSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.HD.BHDSettingLogic"
local UGPInputHelper = import("GPInputHelper")
local EPrivacyDataIndex = {
    HistoryVisibility = 1,
    InvisibleState = 8,
}


function SystemSettingModule:Ctor()
	-- 获取当前玩家的设置信息
	loginfo("SystemSettingModule:Ctor()")
	self._clientBaseSetting = UClientBaseSetting.Get()
    self._clientControlSetting = UClientControlSetting.Get()
	self._clientVehicleSetting = UClientVehicleSetting.Get()
	self._clientSensitivitySetting = UClientSensitivitySetting.Get()
    self._clientSolSetting = UClientSolSetting.Get()

    self.gameHudState_cutscene_existed = false
end

function SystemSettingModule:ShowSystemSettingHDEntrance()
	return SystemSettingLogic.ShowSystemSettingHDEntrance()
end

function SystemSettingModule:HideSystemSettingHDEntrance()
	return SystemSettingLogic.HideSystemSettingHDEntrance()
end

function SystemSettingModule:ShowSystemSettingMainView(openTabType, isOffset)
    isOffset = setdefault(isOffset, false)
	if DFHD_LUA == 1 then
		SystemSettingLogic.OpenSystemSettingHDMainView(openTabType)
	else
		SystemSettingLogic.OpenSystemSettingMainView(openTabType, isOffset)
	end
end

function SystemSettingModule:OnInitModule()
	print("SystemSettingModule:OnInitModule")
    if DFHD_LUA == 0 then
        self:AddLuaEvent(Server.SystemSettingServer.Events.evtRecvShareLayoutGUID, self.OpenShareLayoutPanel, self)

        self:AddLuaEvent(Server.SystemSettingServer.Events.evtInitPlayerSensititySetting, self._SetSensititySetting, self)
        if self:ShouldResetSensitity() then
            DFMSystemSettingHelper:SettingPanelReset({"Sensitivity"})
            logerror("SensitityDataError Don't load Server Data")
        end
        self:AddLuaEvent(Server.SystemSettingServer.Events.evtInitPlayerBaseSetting, self._SetBaseSetting, self)
        self:AddLuaEvent(Server.SystemSettingServer.Events.evtInventoryAutoLine, self._SetInventoryAutoNewLine, self)
        self:SendAnalysisData()
        self:SendAnalysisDataMobile()
        VolumeSettingLogic.SetVolumeSettingData()
        BaseSettingLogic.InitFPPViewRange()
        if BUILD_REGION_CN_EXPER then
            self._clientBaseSetting:SetOpenFlutterVal(true)
        else
            self._clientBaseSetting:SetOpenFlutterVal(false)
        end
	else
        self:AddLuaEvent(LuaGlobalEvents.evtDFConnectBHDStageChanged, self.OnDFConnectBHDStageChanged, self)
        local bhdGameController = Facade.GameFlowManager:GetBHDGameController()
        if bhdGameController then
            bhdGameController:RegJsonProtoHandler("BHD_SYNC_SETTINGS", self.ApplyBHDSettingData, self)
        end
        SettingLogicHD.Init()

        -- 一个神奇的问题，shipping包响应了控制台
        --bug=144055627 【ALL】【PC】【偶现】设置绑健~键，会出现小鼠标，导致视角无法转动（在she2预发布环境下，暂未复现） https://tapd.woa.com/r/t?id=144055627&type=bug
        --bug=143511397 【CN|WW】【PC】【必现】290赛事服按~ 鼠标居中影响操作，再按一次恢复正常 https://tapd.woa.com/r/t?id=143511397&type=bug

        if VersionUtil.IsShipping() and VersionUtil.Data.gameBuildConfiguration ~= EGameBuildConfiguration.ShippingTest then
            logwarning("VersionUtil.Data.gameBuildConfiguration: " .. tostring(VersionUtil.Data.gameBuildConfiguration))
            logwarning("Publish Shipping Need Clear Console Keys")
            local UDFHDKeySettingManager = import "DFHDKeySettingManager"
            local KeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
            local InputSetting = KeySettingMgr.InputSettings
            InputSetting.ConsoleKeys = {}
        end
	end
    LuaGlobalEvents.evtServerShowCreditTip:AddListener(self.IsXinYongLimit, self)
    LuaGlobalEvents.evtLogUploadResult:AddListener(self._OnGetLogUploadResult, self)
end

function SystemSettingModule:ShouldResetSensitity()
    local Num1 = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomZoomratedMDVFactor:Num()
    local Num2 = self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomZoomratedMDVFactor:Num()
    local Num3 = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomZoomratedMDVFactor:Num()
    local Num4 = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomZoomratedMDVFactor:Num()
    if Num1 <= 0 or Num2 <= 0 or Num3 <= 0 or Num4 <= 0 then
        return true
    end
    return false
end

function SystemSettingModule:OnLoadModule()
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function SystemSettingModule:OnLoadingLogin2Frontend(gameFlowType)
    SystemSettingEvtLogic:AddRewardListeners()
    SystemSettingLoadLogic.OnLoadingLogin2FrontendProcess(gameFlowType)
    Server.SystemSettingServer:InitPlayerBaseSetting()
    Server.SystemSettingServer:InitPlayerSensititySetting()
    Server.SystemSettingServer:InitInventoryAutoLine()
    if DFHD_LUA == 1 then
        SettingLogicHD.FetchSetting()
        Server.SystemSettingServer:InitPlayerPrivacyData()
        --- BEGIN MODIFICATION @ VIRTUOS
        if IsConsole() then
            BHDSettingLogic.FetchSetting()
        end
        --- END MODIFICATION
    end
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function SystemSettingModule:OnLoadingGame2Frontend(gameFlowType)
    SystemSettingLoadLogic.OnLoadingGame2FrontendProcess(gameFlowType)
    if DFHD_LUA == 1 then
        SettingLogicHD.ApplyOutOfGameSettingData()
    end
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function SystemSettingModule:OnLoadingFrontend2Game(gameFlowType)
    SystemSettingLoadLogic.OnLoadingFrontend2GameProcess(gameFlowType)
end

function SystemSettingModule:SendAnalysisData()
	loginfo("SystemSettingModule:SendAnalysisData()")
	if self._clientBaseSetting and self._clientControlSetting then
		local param = {}
		param.LeftJoyStickMode = self._clientControlSetting.LeftJoyStickMode
		param.RightFireMode = self._clientControlSetting.RightFireMode
		param.bScopeOpenMode = self._clientBaseSetting.ScopeOpenMode
		param.FunctionBtnRotationMode = self._clientControlSetting.FunctionBtnRotationMode
        param.IsLeanPeek = self._clientControlSetting.bCanLeanPeek
        param.LeanPeekMode = self._clientControlSetting.PeekOpenMode
        param.IsPeekAutoScope = self._clientControlSetting.bPeekAutoScopeOpen
        param.SilentWalkMode = self._clientControlSetting.SilentWalkInputMode
        param.VaultTriggerMode = self._clientControlSetting.bVaultTriggerMode
        param.SensitivityMode = self._clientSensitivitySetting.RotationSensitivityMode
        param.SettingGyroOpenMode = self._clientControlSetting.GyroScopeOpenMode

        param.IsAimAssistOpen = self._clientBaseSetting.bIsAimAssistOpen
        param.EnableReloadOnAiming = self._clientBaseSetting.bEnableReloadOnAiming
        param.EnableAutoFire = self._clientBaseSetting.bEnableAutoFire
        param.CanFireOnQuickScopeOpen = self._clientBaseSetting.bCanFireOnQuickScopeOpen
        param.AbilityItemFireMode = self._clientBaseSetting.AbilityItemFireMode
        param.ShotGunFireMode = self._clientBaseSetting.ShotGunFireMode
        param.SRFireMode = self._clientBaseSetting.SRFireMode
        param.IsSRInstantFire = self._clientBaseSetting.bSRInstantFire
        param.CanSwitchXPP = self._clientBaseSetting.bCanSwitchXPP
        param.FireBreakReload = self._clientBaseSetting.FireBreakReload
        param.IsAutoUpRun = self._clientControlSetting.bAutoUpRun
        param.IsFireBtnRotated = self._clientControlSetting.bFireBtnRotated
        param.IsAimBtnRotated = self._clientControlSetting.bAimBtnRotated
        param.IsPeekBtnRotated = self._clientControlSetting.bPeekBtnRotated
        param.IsCrouchBtnRotated = self._clientControlSetting.bCrouchBtnRotated
        param.IsGyroScopeReverseX = self._clientControlSetting.bGyroScopeReverseX
        param.IsGyroScopeReverseY = self._clientControlSetting.bGyroScopeReverseY
        param.CarrierVehicleMode = self._clientVehicleSetting.VehicleMode
        param.FireWithOpenSights = self._clientBaseSetting.QuickScopeOpen
        param.MoveRunModeSOL = self._clientControlSetting.MoveRunModeMap:Get(0)
        param.MoveRunModeRaid = self._clientControlSetting.MoveRunModeMap:Get(1)
        param.MoveRunModeBattleField = self._clientControlSetting.MoveRunModeMap:Get(3)
        param.MoveRunModeBreakthrought = self._clientControlSetting.MoveRunModeMap:Get(4)
        param.QuickSPCustomPistol = self._clientBaseSetting.quickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Pistol)
        param.QuickSPCustomLightMachine = self._clientBaseSetting.quickScopeOpenMap:Get(ItemConfig.EWeaponItemType.LightMachine)
        param.QuickSPCustomSubmachine = self._clientBaseSetting.quickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Submachine)
        param.QuickSPCustomPrecisionShootingRifle = self._clientBaseSetting.quickScopeOpenMap:Get(ItemConfig.EWeaponItemType.PrecisionShootingRifle)
        param.QuickSPCustomShotgun = self._clientBaseSetting.quickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Shotgun)
        param.QuickSPCustomRifle = self._clientBaseSetting.quickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Rifle)
        param.QuickSPCustomAbilityItem = self._clientBaseSetting.QuickScopeOpenAbilityItem
        param.SensitivityChangeMode = self._clientControlSetting.SensitivityChangeMode
		SystemSettingLogic.SendAnalysisData(param)

        local sensitityTbl = {}
        sensitityTbl.normalDefaultValue = self._clientSensitivitySetting:GetCurrentCustomDefaultSensitivity(false)
        sensitityTbl.fireDefaultValue = self._clientSensitivitySetting:GetCurrentCustomDefaultSensitivity(true)
        sensitityTbl.fireValueList = self._clientSensitivitySetting:GetCurrentCustomSensitivityArray(true)
        sensitityTbl.normalValueList = self._clientSensitivitySetting:GetCurrentCustomSensitivityArray(false)

        sensitityTbl.GroyNormalValue = self._clientSensitivitySetting:GetCurrentGyroCustomDefaultSensitivity(false)
        sensitityTbl.GroyFireValue = self._clientSensitivitySetting:GetCurrentGyroCustomDefaultSensitivity(true)
        sensitityTbl.GroyFireList = self._clientSensitivitySetting:GetCurrentGyroCustomSensitivityArray(true)
        sensitityTbl.GroyNormalList = self._clientSensitivitySetting:GetCurrentGyroCustomSensitivityArray(false)
        SystemSettingLogic.SendSensityData(sensitityTbl)
	else
		logerror("LogAnalysisTool Data is nil!")
	end
end

function SystemSettingModule:SendAnalysisDataMobile()
    if self._clientBaseSetting and self._clientControlSetting and self._clientSensitivitySetting then
        local params = {}
        params.MoveRunMode = self._clientControlSetting.MoveRunModeMap[0]
        params.MoveRunModeMP = self._clientControlSetting.MoveRunModeMap[1]
        params.bIsAimAssistOpen = self:Bool2Num(self._clientBaseSetting.bIsAimAssistOpen)
        params.bIsAimMagnify = self:Bool2Num(self._clientBaseSetting.bIsAimMagnify)
        params.QuickScopeOpen = self._clientBaseSetting.QuickScopeOpen
        params.QuickScope_Rifle = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Rifle))
        params.QuickScope_Shotgun = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Shotgun))
        params.QuickScope_Sniper = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Sniper))
        params.QuickScope_PrecisionRifle = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.PrecisionShootingRifle))
        params.QuickScope_Submachine = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Submachine))
        params.QuickScope_LightMachine = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.LightMachine))
        params.QuickScope_Pistol = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap:Get(ItemConfig.EWeaponItemType.Pistol))
        params.QuickScope_ArmoryProp = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenAbilityItem)
        params.bCanFireOnQuickScopeOpen = self:Bool2Num(self._clientBaseSetting.bCanFireOnQuickScopeOpen)
        params.ShotGunFireMode = self._clientBaseSetting.ShotGunFireMode
        params.SRFireMode = self._clientBaseSetting.SRFireMode

        params.QuickScopeOpen_MP = self._clientBaseSetting.QuickScopeOpen_MP
        params.QuickScope_Rifle_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.Rifle))
        params.QuickScope_Shotgun_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.Shotgun))
        params.QuickScope_Sniper_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.Sniper))
        params.QuickScope_PrecisionRifle_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.PrecisionShootingRifle))
        params.QuickScope_Submachine_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.Submachine))
        params.QuickScope_LightMachine_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.LightMachine))
        params.QuickScope_Pistol_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenMap_MP:Get(ItemConfig.EWeaponItemType.Pistol))
        params.QuickScope_ArmoryProp_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenAbilityItem_MP)
        params.FixedWeaponQuickScope_MP = self:Bool2Num(self._clientBaseSetting.QuickScopeOpenFixedWeapon)
        params.bCanFireOnQuickScopeOpen_MP = self:Bool2Num(self._clientBaseSetting.bCanFireOnQuickScopeOpen_MP)
        params.ShotGunFireMode_MP = self._clientBaseSetting.ShotGunFireMode_MP
        params.SRFireMode_MP = self._clientBaseSetting.SRFireMode_MP
        params.RollCameraMode = self._clientBaseSetting.RollCameraMode
        params.bReloadBreakMirror = self:Bool2Num(self._clientBaseSetting.bReloadBreakMirror)
        params.bCanSprintBreakZoom = self:Bool2Num(self._clientBaseSetting.bCanSprintBreakZoom)
        params.bCanSprintBreakZoom_MP = self:Bool2Num(self._clientBaseSetting.bCanSprintBreakZoomMP)
        params.bCanSprintBreakUsingItem = self:Bool2Num(self._clientBaseSetting.bCanSprintBreakUsingItem)
        params.FireBreakReload = self._clientBaseSetting.FireBreakReload
        params.bAutoHoldBreathOnZoom = self:Bool2Num(self._clientBaseSetting.bAutoHoldBreathOnZoom)
        params.bCanLeanPeek = self:Bool2Num(self._clientControlSetting.bCanLeanPeek)
        params.PeekOpenMode = self._clientControlSetting.PeekOpenMode
        params.bPeekAutoScopeOpen = self:Bool2Num(self._clientControlSetting.bPeekAutoScopeOpen)
        params.GyroScopeOpenMode = self._clientControlSetting.GyroScopeOpenMode
        params.bGyroScopeReverseX = self:Bool2Num(self._clientControlSetting.bGyroScopeReverseX)
        params.bGyroScopeReverseY = self:Bool2Num(self._clientControlSetting.bGyroScopeReverseY)
        params.bFireBtnRotated = self:Bool2Num(self._clientControlSetting.bFireBtnRotated)
        params.bAimBtnRotated = self:Bool2Num(self._clientControlSetting.bAimBtnRotated)
        params.bPeekBtnRotated = self:Bool2Num(self._clientControlSetting.bPeekBtnRotated)
        params.bCrouchBtnRotated = self:Bool2Num(self._clientControlSetting.bCrouchBtnRotated)
        params.bAutoUpRun = self:Bool2Num(self._clientControlSetting.bAutoUpRun)
        params.bAutoUpRun_MP = self:Bool2Num(self._clientControlSetting.bAutoUpRunMP)
        params.SilentWalkInputMode = self._clientControlSetting.SilentWalkInputMode
        params.bVaultTriggerMode = self:Bool2Num(self._clientControlSetting.bVaultTriggerMode)
        params.SensitivityChangeMode = self._clientControlSetting.SensitivityChangeMode
        params.OpenQuicklySwitchButton = self:Bool2Num(self._clientBaseSetting.OpenQuicklySwitchButton)
        params.bAutoParachute = self:Bool2Num(self._clientControlSetting.bAutoParachute)
        params.OpenFlutter = self:Bool2Num(self._clientBaseSetting.OpenFlutter)

        params.RotationSensitivityMode = self._clientSensitivitySetting.RotationSensitivityMode
        params.RotationSpeedAccelerationSensitivity = self._clientSensitivitySetting:GetCustomSpeedAccExtraAngle(false)
        params.FireSpeedAccelerationSensitivity = self._clientSensitivitySetting:GetCustomSpeedAccExtraAngle(true)
        params.CustomSensitivityFactor = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomSensitivityFactor
        params.CustomZoomratedMDVFactor = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomZoomratedMDVFactor:Get(0)
        params.CustomVerticalSensitivityFactor = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomVerticalSensitivityFactor
        params.CustomHorizontalSensitivityFactor = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.CustomHorizontalSensitivityFactor

        params.FireCustomSensitivityFactor = self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomSensitivityFactor
        params.FireCustomZoomratedMDVFactor = self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomZoomratedMDVFactor:Get(0)
        params.FireCustomVerticalSensitivityFactor = self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomVerticalSensitivityFactor
        params.FireCustomHorizontalSensitivityFactor = self._clientSensitivitySetting.FireSetting.SensitivitySetting.CustomHorizontalSensitivityFactor

        params.RotationSensitivityArray0 = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.RotationSensitivityArray_Custom:Get(0)
        params.RotationSensitivityArray1 = self._clientSensitivitySetting.NormalSetting.SensitivitySetting.RotationSensitivityArray_Custom:Get(1)
        params.RotationSensitivityArray = self:Array2Str(self._clientSensitivitySetting.NormalSetting.SensitivitySetting.RotationSensitivityArray_Custom,2,table.nums(self._clientSensitivitySetting.NormalSetting.SensitivitySetting.RotationSensitivityArray_Custom))

        params.FireRotationSensitivityArray0 = self._clientSensitivitySetting.FireSetting.SensitivitySetting.RotationSensitivityArray_Custom:Get(0)
        params.FireRotationSensitivityArray1 = self._clientSensitivitySetting.FireSetting.SensitivitySetting.RotationSensitivityArray_Custom:Get(1)
        params.FireRotationSensitivityArray = self:Array2Str(self._clientSensitivitySetting.FireSetting.SensitivitySetting.RotationSensitivityArray_Custom,2,table.nums(self._clientSensitivitySetting.FireSetting.SensitivitySetting.RotationSensitivityArray_Custom))

        params.GyroCustomSensitivityFactor = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomSensitivityFactor
        params.GyroFireCustomZoomratedMDVFactor = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomZoomratedMDVFactor:Get(0)
        params.GyroFireCustomVerticalSensitivityFactor = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomVerticalSensitivityFactor
        params.GyroFireCustomHorizontalSensitivityFactor = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.CustomHorizontalSensitivityFactor

        params.GyroFireCustomSensitivityFactor = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomSensitivityFactor
        params.GyroFireCustomZoomratedMDVFactor = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomZoomratedMDVFactor:Get(0)
        params.GyroFireCustomVerticalSensitivityFactor = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomVerticalSensitivityFactor
        params.GyroFireCustomHorizontalSensitivityFactor = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.CustomHorizontalSensitivityFactor

        params.GyroRotationSensitivityArray0 = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom:Get(0)
        params.GyroRotationSensitivityArray1 = self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom:Get(1)
        params.GyroRotationSensitivityArray = self:Array2Str(self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom,2,table.nums(self._clientSensitivitySetting.NormalSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom))

        params.GyroFireRotationSensitivityArray0 = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom:Get(0)
        params.GyroFireRotationSensitivityArray1 = self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom:Get(1)
        params.GyroFireRotationSensitivityArray = self:Array2Str(self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom,2,table.nums(self._clientSensitivitySetting.FireSetting.GyroSensitivitySetting.RotationSensitivityArray_Custom))
        LogAnalysisTool.DoSendMobileSensitityLog(params)
    end

end

function SystemSettingModule:Bool2Num(param)
    if param == true then
        return 1
    elseif param == false then
        return 0
    else-- -1 为错误，便于排查
        return -1
    end
end

function SystemSettingModule:Array2Str(params,head,tail)
local str = string.format("%s",params[head])
    for index = head+1 ,tail do
        str = string.format("%s,%s",str,params[index])
    end
    return str
end

function SystemSettingModule:OnGameFlowChangeEnter(gameFlowType)

    if DFHD_LUA == 0 then
        local mode = Server.ArmedForceServer:GetCurArmedForceMode()
        if mode == EArmedForceMode.MP then
            Server.SystemSettingServer:SendCurrentUsingLayout("BaseLayout", "BattleLayout",false)
            --MobileCustomLayoutDataCenter:SetCurrentBaseLayout("BattleLayout")
        else
            Server.SystemSettingServer:SendCurrentUsingLayout("BaseLayout", "SOLLayout",false)
            --MobileCustomLayoutDataCenter:SetCurrentBaseLayout("SOLLayout")
        end
    end

    if DFHD_LUA == 1 then
		UGPGameplayDelegates.Get(GetGameInstance()).OnOpenSystemSettingHDEntrance:Clear()
		UGPGameplayDelegates.Get(GetGameInstance()).OnOpenSystemSettingHDEntrance:Bind(CreateCallBack(self.ShowSystemSettingHDEntrance, self))
        if gameFlowType == EGameFlowStageType.ModeHall then
            SettingLogicHD.ApplyLateSettings()
            SettingLogicHD.TryHandlePendingOverwriteTask()
        end
        if (not IsConsole()) and Facade.ModuleManager:IsModuleValid("LobbyDisplay") then
            if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
                if not self._hOrthoProjectionStateChanged then
                    self._hOrthoProjectionStateChanged = self:AddLuaEvent(Module.LobbyDisplay.Config.Events.evtHDSwitchSuperResolution, self.OnOrthoProjectionStateChanged, self)
                end
            else
                self:RemoveLuaEvent(Module.LobbyDisplay.Config.Events.evtHDSwitchSuperResolution, self)
                self._hOrthoProjectionStateChanged = nil
            end
        end
	end

    -- if DFHD_LUA == 1 then
    --     if gameFlowType == EGameFlowStageType.ModeHall then
    --         self:_BindSystemSettingHDEntranceAction()
    --     else
    --         self:_UnbindSystemSettingHDEntranceAction()
    --     end
    -- end

    if DFHD_LUA == 1 then
        VideoSettingLogicHD.RefreshSyncAndFPS(true)
    end
    
    if DFHD_LUA == 1 then
        if gameFlowType == EGameFlowStageType.Game then
            self._OnPostGameHUDStateChangedHandler = UGPGameHudDelegates.Get(GetGameInstance()).PostOnGameHudStateChanged:Add(CreateCallBack(self._OnPostGameHUDStateChanged, self))
        end
    else
        self._OnPostGameHUDStateChangedHandler = UGPGameHudDelegates.Get(GetGameInstance()).PostOnGameHudStateChanged:Add(CreateCallBack(self._OnMobilePostGameHUDStateChanged, self))
    end

    --- 更新动态图标配置
    RuntimeIconTool.UpdateRTIConfig()
    --- 更新动态图标配置
    
end

function SystemSettingModule:OnGameFlowChangeLeave(gameFlowType)
    if DFHD_LUA == 1 then
        if gameFlowType == EGameFlowStageType.ModeHall then
            self:_UnbindSystemSettingHDEntranceAction()
        end
    end
    if gameFlowType == EGameFlowStageType.Game then
        if isvalid(self._OnPostGameHUDStateChangedHandler) then
            UGPGameHudDelegates.Get(GetGameInstance()).PostOnGameHudStateChanged:Remove(self._OnPostGameHUDStateChangedHandler)
        end
    end
end
--------------------------------
---C++需要的一些设置
--------------------------------
--打开仓库自动整理
function SystemSettingModule:OpenAutoSortCheck()
	Module.Inventory:SwitchAutoSortMode(true)
end

--关闭仓库自动整理
function SystemSettingModule:CloseAutoSortCheck()
	Module.Inventory:SwitchAutoSortMode(false)
end

-- 打开拍卖行自动排序
function SystemSettingModule:OpenAuctionAutoSort()
	Module.Auction:SwitchAutoSortMode(true)
end

-- 关闭拍卖行自动排序
function SystemSettingModule:CloseAuctionAutoSort()
	Module.Auction:SwitchAutoSortMode(false)
end


----------------------------------
---自定义布局
----------------------------------
function SystemSettingModule:IsPlayerHasShared()
    return Server.SystemSettingServer:GetHasShared() ~= nil
end

function SystemSettingModule:CopyComplete()
    Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.shareCodeCopyComplete, 5)
end

function SystemSettingModule:RemindPlayerSearchError()
    Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.checkShareCode, 5)
end

function SystemSettingModule:OpenShareLayoutPanel(shareCode,layoutName, title)
    Facade.UIManager:AsyncShowUI(UIName2ID.CustomLayoutShareLayout, nil, nil, shareCode,layoutName, title)
end

------------------------------

function SystemSettingModule:OnSubStageChangeEnter(curSubStageType)
    local bIsHallMain = curSubStageType == ESubStage.HallMain
    local bIsSendBoxMap = curSubStageType == ESubStage.SandBoxMap
    if bIsHallMain or bIsSendBoxMap then
		local curDisplayStyle = DisplaySettingLogic.GetDisplayStyle()
		self.Field:SetSavedForMiniWorldDisplayStyle(curDisplayStyle)
		--DisplaySettingLogic.ProcessDisplayStyle(EDisplayStyleType.Realistic)
	end
end

function SystemSettingModule:OnSubStageChangeLeave(leaveSubStageType)
    local bIsHallMain = leaveSubStageType == ESubStage.HallMain
    local bIsSendBoxMap = leaveSubStageType == ESubStage.SandBoxMap
    if bIsHallMain or bIsSendBoxMap then
		local savedDisplayStyle = self.Field:GetSavedForMiniWorldDisplayStyle()
		self.Field:SetSavedForMiniWorldDisplayStyle(nil)
		if savedDisplayStyle then
			--DisplaySettingLogic.ProcessDisplayStyle(savedDisplayStyle)
		end
	end
end

function SystemSettingModule:OnDestroyModule()
    self:RemoveAllLuaEvent()
    SystemSettingEvtLogic:RemoveRewardListeners()
    if DFHD_LUA == 1 then
        local bhdGameController = Facade.GameFlowManager:GetBHDGameController()
        if bhdGameController then
            bhdGameController:RemoveAllJsonProtoListenerByCaller(self)
        end
        
        SettingLogicHD.Uninit()
        self:_UnbindSystemSettingHDEntranceAction()
	end

    self:CloseMainPanel()
end

function SystemSettingModule:GetDataByIDHD(id)
    return CommonSettingLogicHD.GetDataByID(id)
end

function SystemSettingModule:SetDataByIDHD(id, value, bSkipApply)
    CommonSettingLogicHD.SetDataByIDImmediately(id, value, bSkipApply)
    SettingRegLogicHD.FlushPendingSaveObjs()
end

function SystemSettingModule:ResetDataByIDHD(id)
    CommonSettingLogicHD.ResetDataByIDImmediately(id)
    SettingRegLogicHD.FlushPendingSaveObjs()
end

function SystemSettingModule:GetSettingDataRowHD(id)
    return SettingDataTableLogicHD.GetDataTableRow(id)
end

function SystemSettingModule:IsValidIDHD(id)
    return CommonSettingLogicHD.IsValidID(id)
end

function SystemSettingModule:GetDropDownOptionsByIDHD(id)
    return CommonSettingLogicHD.GetDropDownOptionsByID(id)
end

function SystemSettingModule:GetSliderContrainByIDHD(id)
    return CommonSettingLogicHD.GetSliderContrainByID(id)
end

function SystemSettingModule:ApplyRecommandGraphicsQulitySettingHD()
    --- BEGIN MODIFICATION @ VIRTUOS
    if PLATFORM_GEN9 ~= 1 then
        SettingLogicHD.ApplyRecommandGraphicsQulitySetting(true)
    end
    --- END MODIFICATION
end

function SystemSettingModule:ApplyRecommandMaxFPSSettingHD()
    SettingLogicHD.ApplyRecommandMaxFPSSettingHD(true)
end

function SystemSettingModule:ApplyRecommandGraphicsSettingMiscHD(bForce)
    SettingLogicHD.ApplyRecommandGraphicsSettingMisc(bForce)
end

function SystemSettingModule:CheckAndShowResizableBarWarningHD(fOnFinished)
    MiscSettingLogicHD.CheckAndShowResizableBarWarningHD(fOnFinished)
end

function SystemSettingModule:_BindSystemSettingHDEntranceAction()
    if self._hSystemSettingHDEntrance then
        self:_UnbindSystemSettingHDEntranceAction()
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    self._hSystemSettingHDEntrance =
        inputMonitor:AddDisplayActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.HideSystemSettingHDEntrance,
        self,
        EDisplayInputActionPriority.UI_HUD,
        false
    )
end

function SystemSettingModule:_UnbindSystemSettingHDEntranceAction()
    if self._hSystemSettingHDEntrance then
        local inputMonitor = Facade.UIManager:GetInputMonitor()
        inputMonitor:RemoveDisplayActoinBingingForHandle(self._hSystemSettingHDEntrance)
        self._hSystemSettingHDEntrance = nil
    end
end

function SystemSettingModule:_SetSettingByName(settingClass, settingTbl, settingNameTbl)
    for i, name in ipairs(settingNameTbl) do
        local setting = settingClass[name]
        if settingClass[name] ~= nil then
            if type(settingClass[name]) == "userdata" then
                if settingClass[name].__name == "LuaMap" then
                    local tbl = Json.decode(settingTbl[i])
                    for index, _ in pairs(settingClass[name]) do
                        if tbl[index] then
                            settingClass[name]:Add(index, tbl[index])
                        end
                    end
                end
            else
                if type(settingClass[name]) == type(settingTbl[i]) then
                    settingClass[name] = settingTbl[i]
                else
                    break
                end
            end
        end
    end
    settingClass:SaveDataConfig()
end

function SystemSettingModule:_SetBaseSetting(cloudTbl)
    if (not cloudTbl) or cloudTbl == "" then
        return
    end
    local baseTbl = Json.decode(cloudTbl[1])
    local controlTbl = Json.decode(cloudTbl[2])

    self:_SetSettingByName(self._clientBaseSetting, baseTbl, Server.SystemSettingServer.BaseSettingName)
    self:_SetSettingByName(self._clientControlSetting, controlTbl, Server.SystemSettingServer.ControlSettingName)
end

function SystemSettingModule:_SetSensititySetting(sensitityInfo)
    if #sensitityInfo < 25 then
        return
    end
    SensitivitySettingLogic.ProcessRotationSensitityMode(sensitityInfo[1])
    SensitivitySettingLogic.ProcessSensitityLevel(sensitityInfo[2])
    SensitivitySettingLogic.ProcessGroyLevel(sensitityInfo[3])


    for i = 1, table.nums(sensitityInfo[4]) - 1 do
        SensitivitySettingLogic.Process_SenslibilityValueChanged(sensitityInfo[4][i], i - 1, false)
        SensitivitySettingLogic.Process_SenslibilityValueChanged(sensitityInfo[5][i], i - 1, true)
    end

    for i = 1, table.nums(sensitityInfo[6]) - 1 do
        SensitivitySettingLogic.Process_GroySenslibilityValueChanged(sensitityInfo[6][i], i - 1, false)
        SensitivitySettingLogic.Process_GroySenslibilityValueChanged(sensitityInfo[7][i], i - 1, true)
    end


    if sensitityInfo[1] == ESensitivityMode.SpeedAcc then
        SensitivitySettingLogic.Process_AccValueChanged(sensitityInfo[8], true)
        SensitivitySettingLogic.Process_AccValueChanged(sensitityInfo[9], false)
    end
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[10]*100,1,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[11]*100,2,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[12]*100,3,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[13]*100,4,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[14]*100,5,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[15]*100,6,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[16]*100,7,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[17]*100,8,false)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[18]*100,1,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[19]*100,2,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[20]*100,3,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[21]*100,4,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[22]*100,5,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[23]*100,6,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[24]*100,7,true)
    SensitivitySettingLogic.ProcessGlobalSensitityLevel(sensitityInfo[25]*100,8,true)

end

-- 端游客户端上报设置内容
function SystemSettingModule:ClientAddTgLog_HDSetting()
    local jsonStr = SettingLogicHD.GetHDSettingInfoJsonStr()
    local tglogData = pb.PcSettingsInfo:New()
    tglogData.Content = jsonStr
    LogAnalysisTool.AddTglog(tglogData)
end

-- End

function SystemSettingModule:ProcessMicrophoneVolume(value)
    return VolumeSettingLogic.ProcessMicrophoneVolume(value)
end

function SystemSettingModule:GetMicrophoneVolume()
    return VolumeSettingLogic.GetMicrophoneVolume()
end

function SystemSettingModule:GetSpeakerVolume()
    return VolumeSettingLogic.GetSpeakerVolume()
end

function SystemSettingModule:IsXinYongLimit(limitType)
    if DFHD_LUA == 1 then
        Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDCreditSystem, nil, nil, limitType)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingCreditSystem, nil, nil, limitType)
    end
end

function SystemSettingModule:_OnGetLogUploadResult(res)
    if res == true then
        -- 弹窗提示
        loginfo("SystemSettingModule:_OnGetLogUploadResult = true")
        local UploadFinishTips = Module.SystemSetting.Config.Loc.HDSetting.UploadSuccessTips
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            UploadFinishTips,
            nil,
            Module.Login.Config.Loc.ConfirmBtnText
        )
    else
        loginfo("SystemSettingModule:_OnGetLogUploadResult = false")
        local UploadFinishTips = Module.SystemSetting.Config.Loc.HDSetting.UploadFailTips
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            UploadFinishTips,
            nil,
            Module.Login.Config.Loc.ConfirmBtnText
        )
    end
end

function SystemSettingModule:RequestQuitGame(bImmediate)
    if bImmediate then
        self:_QuitGame()
    else
        local quitGameTxt = Module.SystemSetting.Config.Loc.HDEntrance.quitGame
        local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
        local confirmTxt = Module.SystemSetting.Config.Loc.HDEntrance.confirm
        self.confirmWindowHandle = Module.CommonTips:ShowConfirmWindow(quitGameTxt, CreateCallBack(self._QuitGame, self), nil, cancelTxt, confirmTxt)
    end
end

function SystemSettingModule:_QuitGame()
    loginfo("[george] _QuitGame()")
    -- self:Hide(true, false)
    ---@todo this is a temp solution
    Module.IrisSafeHouse:SendModeHallLogOnQuit()
    SystemSettingLogic.HideSystemSettingHDEntrance()
    local UKismetSystemLibrary = import "KismetSystemLibrary"
    local EQuitPreference = import "EQuitPreference"
    UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
end

function SystemSettingModule:GetDrugDuration() --获取药品耐久度限制值，0为无限制
    local drugDuration = 0
    if IsHD() then
        drugDuration = CommonSettingLogicHD.GetDataByID("DrugDuration")
    else
        drugDuration = solSettingLogic.GetDataByID("DrugDuration")
    end

    return drugDuration
end

--打开个人信息界面(正常打开不传isJump)
function SystemSettingModule:OpenRolesNews(isJump)
    if isJump then
        local isBool = Module.SystemSetting.Field:JumpBuyRecharge()
        if isBool then
            if IsHD() then
                Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDPersonalInformation)
            else
                Facade.UIManager:AsyncShowUI(UIName2ID.SetUpPop_Management_V1)
            end
            --尝试打开界面改名卡
            Module.Collection:OpenReNameCard(true)
        end
        return
    end
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDPersonalInformation)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.SetUpPop_Management_V1)
    end
end

function SystemSettingModule:_OnPostGameHUDStateChanged()
    --同步HUDStateManager的state过来
    if GetWorld() == nil then
        return
    end
    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    local hudState = hudLayerController and hudLayerController.state or nil
    if hudState and hudState:HasState(EGameHUDState.GHS_EscPanel) then
        local entranceUiIns = Module.SystemSetting.Field:GetHDEntranceUiIns()
        if not isvalid(entranceUiIns) or hasdestroy(entranceUiIns) then
            -- 设置界面开着时
            if not self.gameHudState_cutscene_existed and hudState:HasState(EGameHUDState.GHS_CutScene) then
                self.gameHudState_cutscene_existed = true
                local mainViewIns = nil
                if IsHD() then
                    mainViewIns = Module.SystemSetting.Field:GetHDSettingsUiIns()
                end
                if isvalid(mainViewIns) then
                    Facade.UIManager:CloseUI(mainViewIns)
                end
            end
            if self.gameHudState_cutscene_existed and not hudState:HasState(EGameHUDState.GHS_CutScene)  then
                self.gameHudState_cutscene_existed = false
            end
        end
    end
end

function SystemSettingModule:_OnMobilePostGameHUDStateChanged()
    --同步HUDStateManager的state过来
    if GetWorld() == nil then
        return
    end
    local hudLayerController = Facade.UIManager:GetHUDLayerController()
    local hudState = hudLayerController and hudLayerController.state or nil
    if hudState and hudState:HasState(EGameHUDState.GHS_EscPanel) then
        local mainViewIns = Module.SystemSetting.Field:GetMobileRootPanel()
        -- 设置界面开着时
        if not self.gameHudState_cutscene_existed and hudState:HasState(EGameHUDState.GHS_CutScene) then
            self.gameHudState_cutscene_existed = true
            if isvalid(mainViewIns) then
                Facade.UIManager:CloseUI(mainViewIns)
            end
        end
        if self.gameHudState_cutscene_existed and not hudState:HasState(EGameHUDState.GHS_CutScene)  then
            self.gameHudState_cutscene_existed = false
        end
    end
end



function SystemSettingModule:ApplyBHDSettingData(inSettingData)
    BHDSettingLogic.ApplyBHDSettingData(inSettingData)
end

function SystemSettingModule:SendBHDSettingData()
    BHDSettingLogic.SendBHDSettingData()
end

function SystemSettingModule:OnDFConnectBHDStageChanged(state)
    --- 送设置数据
    if state == EDFConnectBHDState.BHDStartSuccess then
        self:SendBHDSettingData()
    end
end

function SystemSettingModule:CloseMainPanel()
    SystemSettingLogic.CloseSystemSettingMainView()
end


function SystemSettingModule:_InitInvisible(privacyDataList,invisibleData)
    Module.SystemSetting.Field:SetPrivacyData(privacyDataList)
    if invisibleData then
        if IsHD() then
            local ClientPrivacySettingHD = import("ClientPrivacySettingHD").Get()
            self:SetDataByIDHD("bInvisible",invisibleData.is_open)
        else
            local ClientPrivacySetting = import("ClientPrivacySetting").Get()
            ClientPrivacySetting.bInvisible = invisibleData.is_open
            ClientPrivacySetting:SaveDataConfig()
        end
    end

end

function SystemSettingModule:GetInvisibleState()
    local bInvisible = false
    if IsHD() then
        local ClientPrivacySettingHD = import("ClientPrivacySettingHD").Get()
        bInvisible = ClientPrivacySettingHD.bInvisible
    else
        local ClientPrivacySetting = import("ClientPrivacySetting").Get()
        bInvisible = ClientPrivacySetting.bInvisible
    end
    return bInvisible 
end

function SystemSettingModule._RoundValueByPrecision(rawValue, precision)
    local bMinus = rawValue < 0
    rawValue = math.abs(rawValue)
    if precision == 0 then
        local value = math.floor(rawValue)
        if bMinus then
            value = value * -1
        end
        return value
    end
    -- local mod = 10 ^ (-precision)
    -- rawValue = rawValue + 10^-5
    -- local value = rawValue - rawValue % mod

    local mult = 10 ^ precision
    local value = math.floor(rawValue * mult + 10 ^ -5) / mult

    if bMinus then
        value = value * -1
    end
    return value
end

function SystemSettingModule:GetSecondLanguage()
    SystemSettingLogic.GetSecondLanguage()
end

function SystemSettingModule:GetCurrentCulture()
    LocalizeTool.GetCurrentCulture()
end

function SystemSettingModule:SendSecondLanguage()
    local gameLanguage = LocalizeTool.GetCurrentCulture()
    local secondLanguage = SystemSettingLogic.GetSecondLanguage()
    if secondLanguage == "" then -- 若无第二语言，则第二语言处上报第一语言
        secondLanguage = gameLanguage
    end
    local tglog = pb.DownloadSettingInfo:New()
    tglog.GameLanguage = gameLanguage
    tglog.ChoosenLanguage = secondLanguage
    LogAnalysisTool.AddTglog(tglog)
end

function SystemSettingModule:OnEvtSendSecondLanguage()
    Module.SystemSetting:SendSecondLanguage(Module.SystemSetting:GetCurrentCulture(),Module.SystemSetting:GetSecondLanguage())--上报第一语言与第二语言，匹配用
end

function SystemSettingModule:ProcessVolumeMusic_temp(value)
    VolumeSettingLogic.ProcessVolumeMusic_temp(value)
end

function SystemSettingModule:GetVolumeMusic(value)
    return self.Field:GetVolumeMusic()
end

function SystemSettingModule:SendPlayerCustomInfo(name,customValue)
    local CustomName = "CustomSettingValue_"..name
    Server.SystemSettingServer:SendPlayerCustomInfo(name,customValue)
end

function SystemSettingModule:OnOrthoProjectionStateChanged(isOrtho)
    isOrtho = not isOrtho
    if IsHD() and not IsConsole() then
        VideoSettingLogicHD.OnOrthoProjectionStateChanged(isOrtho)
    end
end

function SystemSettingModule:_SetInventoryAutoNewLine(cloudTbl)
    if (not cloudTbl) or cloudTbl == "" then
        return
    end
    local Tbl = Json.decode(cloudTbl)
    self._clientSolSetting.bInventoryAutoNewline = Tbl[1]
end

return SystemSettingModule

