----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSChat)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION - VIRTUOS
local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetWorld())
local UDFMGameUrlGenerator = import "DFMGameUrlGenerator"
local UDFMGameUrlGeneratorIns = UDFMGameUrlGenerator.Get(GetGameInstance())
local UDFMOnlineIdentityProxy_CheckUsersCommunicationPermissions = import "DFMOnlineIdentityProxy_CheckUsersCommunicationPermissions"
local UDFMOnlineIdentityProxy_CheckUserPrivilege = import "DFMOnlineIdentityProxy_CheckUserPrivilege"
-- END MODIFICATION - VIRTUOS

local function log(...)
    print("", "[ChatServer]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

local ChatServer = class("ChatServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
function ChatServer:Ctor()
    log("ChatServer:Ctor")
    -- self.chatHistory = {}
    self.chatHistory = {}
    self.electionHistory = 0
    self.teamInviteHistory = {}
    self.evtChatDSUpdated = LuaEvent:NewIns("evtChatDSUpdated")
    self.evtChatElectionUpdated = LuaEvent:NewIns("evtChatElectionUpdated")
    self.evtChatAppointChange = LuaEvent:NewIns("evtChatAppointChange")

    self._isMinorChatAllowed = true
    self._updateAppointEnable = true
    self.sendManuaNum=0
    self.sendPresetNum=0
    self.ManualMessageLen=0

    -- BEGIN MODIFICATION - VIRTUOS
    -- 平台权限缓存
    if IsConsole() then
        -- 用户是否拥有通信交流权限
        self.hasCommunicationPrivilege = true
        -- 用户是否拥有跨平台游玩权限
        self.hasCrossPlayPrivilege = true
        -- 用户与跨网络用户之间是否有通信权限
        self.hasCommunicationPermissionsWithCrossNetworkUser = true
        -- 用户与跨网络好友之间是否有通信权限
        self.hasCommunicationPermissionsWithCrossNetworkFriend = true
        -- 用户是否拥有查看用户生成内容权限
        self.hasUseUserGeneratedContentPrivilege = true
        -- END MODIFICATION - VIRTUOS
    end
end

function ChatServer:OnInitServer(...)
    log("ChatServer:OnInitServer")
    Facade.ProtoManager:AddNtfListener("CSChatDSChatNtf", self._CSChatDSChatNtf, self)
    --局内不接受组队邀请
    --self:AddLuaEvent(Server.TeamServer.Events.evtReceiveTeamInvite, self.OnCSTeamToInviteeNtf, self)

    -- BEGIN MODIFICATION - VIRTUOS
    -- 平台权限检测缓存
    if IsConsole() and DFMOnlineIdentityManager then
        Server.AccountServer.Events.evtOnAccountLoginSuccess:AddListener(self.OnPlatformPrivilegesInitOrReset, self)
        DFMOnlineIdentityManager.OnPlatformPrivilegesResetDelegate:Add(self.OnPlatformPrivilegesInitOrReset, self)
        self:CheckSinglePlatformPrivilege(EPlatformUserPrivileges.CanUseUserGeneratedContent, true, function(res)
            self.hasUseUserGeneratedContentPrivilege = res
        end)
    end
    -- END MODIFICATION - VIRTUOS
end

function ChatServer:OnGameFlowChangeEnter(gameFlowType)
    log("ChatServer:OnGameFlowChangeEnter ",gameFlowType)
    self.EnterFlowType = gameFlowType

    if gameFlowType == EGameFlowStageType.ModeHall or (
        gameFlowType == EGameFlowStageType.SafeHouse) or (
            gameFlowType == EGameFlowStageType.Lobby) then

        self:CleanAllChatMsgs()
        self.isInGameAndSettlement = false
    end

    if self.EnterFlowType == EGameFlowStageType.Game then
        -- 进游戏flow清一下chat
        self:CleanAllChatMsgs()
        self.isInGameAndSettlement = true
    end
end

function ChatServer:OnCSTeamToInviteeNtf(ntf)

    -- uint64 TeamID = 1;
    -- uint32 MatchID = 3;
    -- uint64 InviterID = 4;    // 邀请人id
    -- string InviterNick = 6;  // 邀请人名字
    -- uint32 inviterLevel = 7; // 邀请人等级
    -- string inviterPicUrl = 8; // 邀请人头像
    local inviterID = ntf.InviterID
    local inviterNick = ntf.InviterNick
    local inviterLevel = ntf.InviterLevel
    local inviterPicUrl = ntf.InviterPicUrl
    local matchId = ntf.MatchID
    local teamId = ntf.TeamID
    local bIsAgree = false
    log("ChatServer:OnCSTeamToInviteeNtf",inviterID, teamId,bIsAgree)

    local inviteInfo = {
        -- DsRoomId = DsRoomId,
        -- TimeStamp = TimeStamp,
        -- ChatType = ChatType,
        -- MsgType = MsgType,
        senderId = inviterID,
        senderName = inviterNick,
        content = ServerTipCode.InviteYouToStart,
        teamId = teamId
    }
    logtable(inviteInfo,true)
    self.teamInviteHistory[#self.teamInviteHistory +1] = inviteInfo
    self.evtChatDSUpdated:Invoke()
end

function ChatServer:AnsTeamInvite(index)
    log("ChatServer:AnsTeamInvite")
    self.teamInviteHistory[index].bAns = true
end

function ChatServer:CleanAllChatMsgs()
    log("ChatServer:CleanChatMsgs")
    self.chatHistory = {}
    self.teamInviteHistory = {}
    self:UpdateAppointNum(false)
end

function ChatServer:_CSChatDSChatNtf(ntf)
    log("ChatServer:_CSChatDSChatNtf")
    if Server.MatchServer:GetDsRoomId() ~= ntf.ds_room_id then
        logwarning("ChatServer:_CSChatDSChatNtf ds_room_id not match,ntf.ds_room_id:",ntf.ds_room_id,"MatchServer.ds_room_id",Server.MatchServer:GetDsRoomId())
        return
    end

    if not self.isInGameAndSettlement then
        logwarning("ChatServer:_CSChatDSChatNtf can not receive because of not in game or settlement")
        return
    end

    local function handleDSChatNtf()
        local chatStruct = {
            dsRoomId = ntf.ds_room_id,
            timeStamp = ntf.timestamp,
            chatType = ntf.chat_type,
            msgType = ntf.msg.msg_type,
            senderId = ntf.sender,
            senderName = ntf.sender_nick,
            content = ntf.msg.text,
            msg = ntf.msg,
            bHandle = false
        }
        logtable(chatStruct,true)
    
     -- 大战场指挥官竞选宣言事件分流
     if ntf.chat_type == DSChatType.DSChatMPElection then
        electionHistory = chatStruct
        self.evtChatElectionUpdated:Invoke(chatStruct)
        return
    end

    self.chatHistory[#self.chatHistory+1] = chatStruct
    self.evtChatDSUpdated:Invoke(true)

    if self._updateAppointEnable and chatStruct.msgType == ChatMsgType.Appointment then
        self:UpdateAppointNum(true)
        end
    end
    if (not IsXboxSeries() and not IsPS5Family()) then
        handleDSChatNtf()
    else
        -- 平台权限检测
        if self.hasCommunicationPrivilege == false then
            return
        end
        local senderId = ntf.sender
        local senderPlat = ntf.plat_id
        if senderPlat == Server.AccountServer:GetPlatIdType() then
            -- 发送者来自相同平台
            local checkList = {}
            table.insert(checkList, senderId)
            Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingText, false,checkList, function(allowedList)
                if #allowedList > 0 then
                    handleDSChatNtf()
                end
            end)
        else
            -- 发送者来自不同平台
            Server.ChatServer:CheckAnonymousUserCommunicationPermissions(senderId, function(isAllowed)
                if isAllowed then
                    handleDSChatNtf()
                end
            end)
        end
    end
end

function ChatServer:AddTeamInviteMsg(mapIndex)
    local function handleTeamInviteMsg()
        local chatStruct = {
            timeStamp = 1,
            chatType = DSChatType.TeamInvite,
            msgType = ChatMsgType.TeamInvite,
            mapIndex = mapIndex,
            bHandle = false
        }

        self.chatHistory[#self.chatHistory+1] = chatStruct
        self.evtChatDSUpdated:Invoke(true)
    end

    handleTeamInviteMsg()
end

function ChatServer:UpdateAppointNum(bAdd)
    if bAdd then
        self._hasNewAppoint = true
    else
        self._hasNewAppoint = false
    end
    self.evtChatAppointChange:Invoke()
end

function ChatServer:HasNewAppoint()
    return self._hasNewAppoint
end

function ChatServer:UpdateAppointEnable(bEnable)
    self._updateAppointEnable = bEnable
end

function ChatServer:UpdateMinorChatControl(bAllowed)
    self._isMinorChatAllowed = bAllowed
end

function ChatServer:SendChat(dsRoomId, chatType, msgType, content, fCallback, nick,source)
    if not self._isMinorChatAllowed then
        return
    end
    content = tostring(content)
    log("ChatServer:SendChat",dsRoomId, chatType, msgType, content, fCallback)

    chatType = setdefault(chatType, DSChatType.DSChatAll)
    msgType = setdefault(msgType, ChatMsgType.Str)
    local function OnCSChatDSChatTRes(res)
        log("OnCSChatDSChatTRes")
        if res.result == 0 then
            if fCallback then
                fCallback()
            end
        elseif res and res.result == Err.ChatForbidden then
            local free_time = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(res.forbid_info.free_time)
            local tip = string.format(ServerTipCode.ChatForbidMsg,free_time)
            LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
        elseif res and res.result == Err.ChatForbiddenByCreditCheck then
            LuaGlobalEvents.evtServerShowCreditTip:Invoke(Server.FriendServer.EXinyongType.ChatLimit)
        elseif res and res.result == Err.ChatNotInDSRoom then
            -- not do anything
        else
            Facade.ProtoManager:ManuelHandleErrCode(res)
        end
    end

    local msg = {
        timestamp = os.time(),
        msg_type = msgType,
        text = content,
    }
    local req = pb.CSChatDSChatTReq:New()
    req.ds_room_id = dsRoomId
    req.chat_type = chatType
    req.msg = msg
    req.source=source
    --nick不为空，代表是AI借助客户端发消息, Ai ID为8000
    if nick and nick ~= "" then
        req.sender_id = 8000
        req.sender_nick = nick
    end
    req:Request(OnCSChatDSChatTRes,{bShowErrTip = false, bEnableHighFrequency = true})
end

---@param params {} 一个数组，第一个元素表示 tableID，详见presetChatTable, 后续值表示引擎回传的参数如“救援剩余多少%s秒”
function ChatServer:SendChatPreset(dsRoomId, chatType, presetId, nick, params, source)
    log("ChatServer:SendChatPreset", dsRoomId, chatType, nick, presetId)

--     【【iOS】【必现】聊天快捷消息无法使用】
-- https://tapd.woa.com/tapd_fe/20421949/bug/detail/1120421949132551706
    -- local PresetChatAuto = Facade.TableManager:GetTable("PresetChatAuto")
    -- local bFoundId = false
    -- for key, value in pairs(PresetChatAuto) do
    --     if value.PresetChatID == presetId then
    --         bFoundId = true
    --         break
    --     end
    -- end
    -- if not bFoundId then
    --     logwarning("ChatServer:SendChatPreset, Not found coor presetId", presetId)
    --     return
    -- end

    chatType = setdefault(chatType, DSChatType.DSChatAll)
    local function OnCSChatDSChatTRes(res)
        log("OnCSChatDSChatTRes")
        if res and res.result == Err.ChatForbidden then
            local free_time = TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(res.forbid_info.free_time)
            local tip = string.format(ServerTipCode.ChatForbidMsg,free_time)
            LuaGlobalEvents.evtServerShowTip:Invoke(tip,5)
        elseif res and res.result == Err.ChatNotInDSRoom then
            -- not do anything
        else
            Facade.ProtoManager:ManuelHandleErrCode(res)
        end
    end

    local msg = {
        timestamp = os.time(),
        msg_type = ChatMsgType.PresetStr,
        preset_msg = {preset_id = presetId,params = params}
    }
    local req = pb.CSChatDSChatTReq:New()
    req.ds_room_id = dsRoomId
    req.chat_type = chatType
    req.msg = msg
    req.source=source
    --nick不为空，代表是AI借助客户端发消息, Ai ID为8000
    if nick and nick ~= "" then
        req.sender_id = 8000
        req.sender_nick = nick
    end
    req:Request(OnCSChatDSChatTRes,{bShowErrTip = false, bEnableHighFrequency = true})
end

--回复预约
function ChatServer:RespAppoint(receiver_id, txt,rejected)
    local sendContent = {
        timestamp = os.time(),
        msg_type = ChatMsgType.AppointmentResp,
        text = tostring(txt),
        emoji_list = {},
        sender_id = Server.AccountServer:GetPlayerId(),
        voice_data = {}
    }
    local req = pb.CSChatAppointmentRespondReq:New()
    req.sender_id = receiver_id
    req.content = sendContent
    req.is_rejected = rejected
    req:Request()
end

--缓存局内手游聊天临时状态
function ChatServer:CacheTempState(channel)
    self.mCacheSelectedChannel = channel
end

--拿局内手游缓存的临时状态
function ChatServer:GetCacheTempState()
    return self.mCacheSelectedChannel
end

function ChatServer:OnDestroyServer()

    log("ChatServer:OnDestroyServer")
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() and DFMOnlineIdentityManager then
        Server.AccountServer.Events.evtOnAccountLoginSuccess:RemoveListener(self.OnPlatformPrivilegesInitOrReset, self)
        DFMOnlineIdentityManager.OnPlatformPrivilegesResetDelegate:Remove(self.OnPlatformPrivilegesInitOrReset, self)
    end
    -- END MODIFICATION - VIRTUOS
end

-- BEGIN MODIFICATION - VIRTUOS
function ChatServer:OnPlatformPrivilegesInitOrReset()
    if IsConsole() and DFMOnlineIdentityManager then
        local communicationPrivilegeCheckProxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(DFMOnlineIdentityManager)
        communicationPrivilegeCheckProxy.OnGetUserPrivilegeCompleteDelegate:Add(function(res)
            self.hasCommunicationPrivilege = res
            if IsXboxSeries() then
                if res then
                    -- Xbox平台的跨平台权限从Xbox服务器拿到
                    local crossPlayPrivilegeCheckProxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(DFMOnlineIdentityManager)
                    crossPlayPrivilegeCheckProxy.OnGetUserPrivilegeCompleteDelegate:Add(function(res)
                        self.hasCrossPlayPrivilege = res
                        if res then
                            local communicationPermissionsToCrossNetworkUserCheckProxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(DFMOnlineIdentityManager)
                                communicationPermissionsToCrossNetworkUserCheckProxy.OnGetCommunicationPermissionsToAnonymousUserCompleteDelegate:Bind(function(res)
                                self.hasCommunicationPermissionsWithCrossNetworkUser = res
                                local communicationPermissionsToCrossNetWorkFriendCheckProxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(DFMOnlineIdentityManager)
                                communicationPermissionsToCrossNetWorkFriendCheckProxy.OnGetCommunicationPermissionsToAnonymousUserCompleteDelegate:Bind(function(res)
                                    self.hasCommunicationPermissionsWithCrossNetworkFriend = res
                                end)
                                communicationPermissionsToCrossNetWorkFriendCheckProxy:CheckAnonymousUserCommunicationPermissions(EAnonymousUserType.CrossNetWorkFriend)
                            end)
                            communicationPermissionsToCrossNetworkUserCheckProxy:CheckAnonymousUserCommunicationPermissions(EAnonymousUserType.CrossNetworkUser)
                        else
                            self.hasCrossPlayPrivilege = false
                            self.hasCommunicationPermissionsWithCrossNetworkUser = false
                            self.hasCommunicationPermissionsWithCrossNetworkFriend = false
                        end
                    end)
                    crossPlayPrivilegeCheckProxy:CheckUserPrivilege(EPlatformUserPrivileges.CanUserCrossPlay, false)
                else
                    self.hasCommunicationPrivilege = false
                    self.hasCrossPlayPrivilege = false
                    self.hasCommunicationPermissionsWithCrossNetworkUser = false
                    self.hasCommunicationPermissionsWithCrossNetworkFriend = false
                end
            elseif IsPS5Family() then
                -- PS5平台的跨平台权限从游戏服务器拿到
                Server.AccountServer:ReqGetPlayerProfile(Server.AccountServer:GetPlayerId(), function(res)
                    self.hasCrossPlayPrivilege = res.can_cross_plat_play
                    self.hasCommunicationPermissionsWithCrossNetworkUser = res.can_cross_plat_play
                    self.hasCommunicationPermissionsWithCrossNetworkFriend = res.can_cross_plat_play
                end)
            end
        end)
        communicationPrivilegeCheckProxy:CheckUserPrivilege(EPlatformUserPrivileges.CanCommunicateOnline, false)
    end
end

function ChatServer:CheckAnonymousUserCommunicationPermissions(senderId, fCallback)
    if PLATFORM_GEN9 ~= 1 then
        return
    end
    if IsXboxSeries() then
        local isFriend = Server.FriendServer:CheckIsFriend(senderId)
        if Server.ChatServer.hasCrossPlayPrivilege then
            if isFriend and Server.ChatServer.hasCommunicationPermissionsWithCrossNetworkFriend then
                fCallback(true)
            elseif isFriend == false and Server.ChatServer.hasCommunicationPermissionsWithCrossNetworkUser then
                fCallback(true)
            else
                fCallback(false)
            end
        end
    elseif IsPS5Family() then
        if Server.RoleInfoServer.hasCrossPlayPrivilege then
            fCallback(true)
        else
            fCallback(false)
        end
    end
end

function ChatServer:CheckUsersPermissionsByOpenIdList(permissionType, bForceCheck, openIdList, fCallback)
    if PLATFORM_GEN9 ~= 1 then
        return
    end
    if IsXboxSeries() then
        local uidCheckList = DFMOnlineIdentityManager:GetUsersForCheckCommunicationPermissions()
        local openIdListNum = #openIdList
        local uidToOpenIdMap = {}
        local allowedCommunicationList = {}
        for _, openId in pairs(openIdList) do
            local OnReqQueryRes = function(HttpRequest, HttpResponse, bSucceeded)
                queryRes = UDFMGameUrlGeneratorIns:GenerateQueryRes(HttpResponse:GetContentAsString())
                openIdListNum = openIdListNum - 1
                if queryRes.bHasUID then
                    uidCheckList:Add(queryRes.UID)
                    uidToOpenIdMap[queryRes.UID] = queryRes.OpenId
                end
                if openIdListNum == 0 then
                    local proxy = UDFMOnlineIdentityProxy_CheckUsersCommunicationPermissions.CreateInstance(
                    DFMOnlineIdentityManager)
                    proxy:SetCheckUsers(uidCheckList)
                    proxy.OnGetCommunicationPermissionsToUserCompleteDelegate:Bind(function(results)
                        for _, res in pairs(results) do
                            if res.bCommunicationPermissionsAllowed then
                                table.insert(allowedCommunicationList, uidToOpenIdMap[res.UserIdStr])
                            end
                        end
                        fCallback(allowedCommunicationList)
                    end)
                    proxy:CheckUsersPermissions(permissionType, bForceCheck)
                end
            end

            if math.type(openId) == "integer" then
                Server.SocialServer:ReqQueryUID(ULuautils.GetOverInt64String(openId), OnReqQueryRes)
            else
                logerror("OpenId type is not correct:", openId, math.type(openId))
            end
        end
    elseif IsPS5Family() then
        local openIdListNum = #openIdList
        local allowedCommunicationList = {}
        for _, openId in pairs(openIdList) do
            local onGetFriendSpeechJudgmentRes = function(res)
                openIdListNum = openIdListNum - 1
                if res then
                    if math.type(openId) == "integer" then
                        table.insert(allowedCommunicationList, ULuautils.GetOverInt64String(openId))
                    else
                        logerror("OpenId type is not correct:", openId, math.type(openId))
                    end
                end
                if openIdListNum == 0 then
                    fCallback(allowedCommunicationList)
                end
            end
            Server.FriendServer:ReqFriendSpeechJudgment(openId, onGetFriendSpeechJudgmentRes)
        end
    end
end

function ChatServer:CheckSinglePlatformPrivilege(privilegeType, showPlatformUIToResolvePrivilege, fCallback)
    if PLATFORM_GEN9 ~= 1 then
        return
    end
    
    local privilegeCheckProxy = UDFMOnlineIdentityProxy_CheckUserPrivilege.CreateInstance(DFMOnlineIdentityManager)
    privilegeCheckProxy.OnGetUserPrivilegeCompleteDelegate:Add(function(res)
        fCallback(res)
    end)
    privilegeCheckProxy:CheckUserPrivilege(privilegeType, showPlatformUIToResolvePrivilege)
end
-- END MODIFICATION - VIRTUOS

return ChatServer
