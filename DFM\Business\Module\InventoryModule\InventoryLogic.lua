----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local SlotConfig = require "DFM.Business.DataStruct.InventoryStruct.SlotConfig"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"

local UAmmoDataManager = import "AmmoDataManager"

local InventoryLogic = {}

local ammoMgr = UAmmoDataManager.Get()

InventoryLogic.CreateItemView = function(itemStruct, bDraggable, fCallBack, compParentWidget, compWidget)
    -- 资源加载完成回调
    --[[ local function fLoadFinCallback(mapPath2ResIns)
       local weakUiIns = Facade.UIManager:AddSubUI(compParentWidget, UIName2ID.ItemView, compWidget, nil)
        if weakUiIns and getfromweak(weakUiIns) then
            local uiIns = getfromweak(weakUiIns)
            if uiIns then
                uiIns:InitItem(itemStruct, nil, true, bDraggable)
                fCallBack(uiIns)
            end
        else
            logerror("InventoryLogic.CreateItemView:UIManager:AddSubUI: weakUiIns is nil!")
        end
    end

    if Facade.UIManager:CheckUIHasBeenLoaded(UIName2ID.ItemView) then
        local weakUiIns = Facade.UIManager:AddSubUI(compParentWidget, UIName2ID.ItemView, compWidget, nil)
        if weakUiIns and getfromweak(weakUiIns) then
            local uiIns = getfromweak(weakUiIns)
            if uiIns then
                uiIns:InitItem(itemStruct, nil, true, bDraggable)
                fCallBack(uiIns)
            end
        else
            logerror("InventoryLogic.CreateItemView:UIManager:AddSubUI: weakUiIns is nil!")
        end
    else
        Facade.UIManager:AsyncLoadUIResOnly(UIName2ID.ItemView, fLoadFinCallback, nil)
    end--]]
end

---@param refSlot ItemSlot
InventoryLogic.CalculateTouchLogicPos = function(refSlot, inAbsPos, refGeometry)
    if refSlot:IsEquipableSlot() then
        return 0, 0
    end

    local localPos = refGeometry:AbsoluteToLocal(inAbsPos)

    local actualX = math.floor(localPos.X / ItemConfig.DefaultItemViewSize)
    local actualY = math.floor(localPos.Y / ItemConfig.DefaultItemViewSize)
    
    return actualX, actualY
end

---@param markWidget UIWidgetBase
function InventoryLogic.SetItemLabelMark(markWidget, itemId)
    ---@type InventoryServer
    local inventoryServer = Server.InventoryServer
    local bMarked = inventoryServer:CheckItemMarked(itemId)
    if bMarked then
        markWidget:SelfHitTestInvisible()
    else
        markWidget:Collapsed()
    end
end

---@param dragItem ItemBase
---@param slot ItemSlot
function InventoryLogic.CommonOnDropForPerk(dragItem, slot, x, y)
    x = x or 0
    y = y or 0

    local dropItem
    if slot:IsEquipableSlot() then
        dropItem = slot:GetEquipItem()
    else
        dropItem = slot:GetItemAtPos(x, y)
    end
    
    ---@type WeaponFeature
    local weaponFeature
    local dropItemFeatureType 
    if dropItem then
        dropItemFeatureType = dropItem:GetFeatureType()
        weaponFeature = dropItem:GetFeature(EFeatureType.Weapon)
    end
    local dragItemFeatureType = dragItem:GetFeatureType()

    --- Perk特殊装卸逻辑，单独拆个分支处理
    if dragItemFeatureType == EFeatureType.WeaponPerk then
        ---@type WeaponPerkFeature
        local perkFeature = dragItem:GetFeature(EFeatureType.WeaponPerk)
        local bDropOnWeapon = dropItem and dropItem ~= dragItem and dropItemFeatureType == EFeatureType.Weapon
        if perkFeature:IsPerkOnWeapon() then
            if bDropOnWeapon then
                --- Perk from weapon to weapon
                ItemOperaTool.EquipPerk2Weapon(perkFeature, dropItem)
                return true
            else
                --- Perk from weapon to slot
                local refWeaponGid = perkFeature:GetBindWeaponGid()
                local refWeapon
                if ItemOperaTool.CheckRunWarehouseLogic() then
                    refWeapon = Server.InventoryServer:GetItemByGid(refWeaponGid)
                else
                    refWeapon = Server.LootingServer:GetItemDataByGid(refWeaponGid)
                end
                
                if refWeapon then
                    ItemOperaTool.UnequipPerkFromWeapon(refWeapon, slot, x, y)
                end
                
                return true
            end
        else
            if bDropOnWeapon then
                --- Perk from slot to weapon
                ItemOperaTool.EquipPerk2Weapon(perkFeature, dropItem)
                return true
            else
                --- Perk from slot to slot
                --- Do common slot move logic
                return false
            end
        end
    end

    return false
end

---@param slot ItemSlot
function InventoryLogic.CommonOnDropForFastEquip(inOperation, slot, x, y, index, bRotated, bShowTips)
    bShowTips = setdefault(bShowTips, true)
    local dragItemView = inOperation.DefaultDragVisual
    ---@type ItemBase
    local dragItem = dragItemView and dragItemView.item or nil
    local actualLength, actualWidth = dragItem.length, dragItem.width
    -- 判断是不是枪械配件
    local isAdapter = dragItem and dragItem:GetAdapter()
    if not isAdapter then
        return InventoryConfig.EFastEquipCheckResult.NoFastEquip
    end

    -- 所传位置是否合法
    local itemLoc = ItemLocation:NewIns()
    if bRotated then
        actualLength = dragItem.width
        actualWidth = dragItem.length
    end

    if slot and slot:CheckVipContainerIsExpired() then
        return InventoryConfig.EFastEquipCheckResult.Fail
    end

    -- if ItemOperaTool.CheckRunWarehouseLogic() and not slot:IsEquipableSlot() then
    --     return InventoryConfig.EFastEquipCheckResult.Fail
    -- end

    -- 如果是拖到容器槽位，说明是想把配件拆到容器里，位置任意
    if slot:IsEquipContainerSlot() and slot:GetEquipItem() then
        local refContainerSlotType = slot:GetContainerSlotType()
        local refContainerSlot = Server.InventoryServer:GetSlot(refContainerSlotType, slot:GetSlotGroup())
        -- 判断容器有没有足够位置
        if ItemOperaTool.CheckRunWarehouseLogic() and not refContainerSlot:TryFindLocationForItem(dragItem) then
            return InventoryConfig.EFastEquipCheckResult.Fail
        end

        slot = refContainerSlot
        x = -1
        y = -1
        index = 0
        bRotated = false
    end

    -- 如果是拖到装备容器槽，判断是否可装下配件
    -- if slot:IsContainerSlot() then
    --     if ItemOperaTool.CheckRunWarehouseLogic() and not slot:TryFindLocationForItem(dragItem) then
    --         return InventoryConfig.EFastEquipCheckResult.Fail
    --     end
    -- end

    itemLoc:Init(slot, x, y, actualLength, actualWidth, index, bRotated)
    -- 配件拆卸到网格上时，不会判断itemLoc是否合法，所以需要在判断网格上时拆卸的时候是否合法。
    if Module.FastEquip:IsFastEquipItem(inOperation) and (itemLoc:CheckLocationValidInSlot() or not slot:IsGridContainerSlot())  then --快速拆卸
        local widgetRef = inOperation.WidgetReference
        local socketInfo = widgetRef and widgetRef.socketInfo
        if socketInfo then
            local item = inOperation.WidgetReference.item
            local gunItem = inOperation.WidgetReference.rootItem
            local curItem = slot:GetItemAtPos(x, y, index)
            local curWeaponFeature = nil
            local gunWeaponFeature = nil
            local adapterFeature = nil
            if curItem then
                curWeaponFeature = curItem:GetFeature(EFeatureType.Weapon)
            end
            if gunItem then
                gunWeaponFeature = gunItem:GetFeature(EFeatureType.Weapon)
            end
            if item then
                adapterFeature = item:GetFeature(EFeatureType.Adapter)
            end

            local gunItemSlotType = gunItem and gunItem.InSlot and gunItem.InSlot.SlotType
            local curItemSlotType = curItem and curItem.InSlot and curItem.InSlot.SlotType
            local itemSlotType = item and item.InSlot and item.InSlot.SlotType

            -- 匹配过程中不允许快拆装备区中的枪上的配件
            if ItemOperaTool.CheckRunWarehouseLogic() then
                if not InventoryLogic.CheckCanMoveInMatching(gunItemSlotType) or not InventoryLogic.CheckCanMoveInMatching(curItemSlotType)
                or not InventoryLogic.CheckCanMoveInMatching(itemSlotType) then
                    return InventoryConfig.EFastEquipCheckResult.InMatching
                end
            end

            local bDropOnGun = false
            if curItem and curItem ~= item and 
                (adapterFeature and adapterFeature:IsAdapter()) and
                (curWeaponFeature and curWeaponFeature:IsWeapon()) then

                bDropOnGun= true
            end

            if bDropOnGun then
                --1.直接把A枪配件拖到B枪上
                if gunItem and gunItem ~= curItem then
                    local bCanUnequip = Module.FastEquip:CheckFastUnEquip(item, gunItem, false)
                    local bCanEquip = Module.FastEquip:CheckFastEquip(item, curItem, bShowTips)
                    if bCanUnequip and bCanEquip then
                        Module.FastEquip:FastUnEquip(
                            gunItem,
                            slot,
                            item,
                            socketInfo,
                            x,
                            y,
                            index,
                            bRotated,
                            bShowTips
                        )
                        return InventoryConfig.EFastEquipCheckResult.Success
                    end
                elseif gunItem ~= curItem then
                    if Module.FastEquip:CheckFastEquip(item, curItem, bShowTips) then
                        Module.FastEquip:FastEquip(item, curItem, bShowTips)
                        return InventoryConfig.EFastEquipCheckResult.Success
                    else
                        return InventoryConfig.EFastEquipCheckResult.Fail
                    end
                end
            elseif gunItem and gunItem ~= item and 
                    (adapterFeature and adapterFeature:IsAdapter()) and 
                    (gunWeaponFeature and gunWeaponFeature:IsWeapon()) then
                        
                    if Module.FastEquip:CheckFastUnEquip(item, gunItem, bShowTips) and
                        slot:CheckItemFitSlot(item) then
                        Module.FastEquip:FastUnEquip(
                            gunItem,
                            slot,
                            item,
                            socketInfo,
                            x,
                            y,
                            index,
                            bRotated,
                            bShowTips
                        )

                    return InventoryConfig.EFastEquipCheckResult.Success 
                else
                    return InventoryConfig.EFastEquipCheckResult.Fail
                end
            end

            return InventoryConfig.EFastEquipCheckResult.NoFastEquip
        end
    end

    local dropItem = slot:GetItemAtPos(x, y, index)
    local weaponFeature, dropItemFeatureType
    if dropItem then
        dropItemFeatureType = dropItem:GetFeatureType()
        weaponFeature = dropItem:GetFeature(EFeatureType.Weapon)
    end

    if dragItem and dropItem and dragItem ~= dropItem then

        -- 队友绑定的不不走逻辑
        local bDropItemHaveTeammateBindCondition, dropItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(dropItem)
        local bDragItemHaveTeammateBindCondition, dragItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(dragItem)
        if bDropItemHaveTeammateBindCondition or bDragItemHaveTeammateBindCondition then
            return InventoryConfig.EFastEquipCheckResult.NoFastEquip
        end

        local weaponFeature = dropItem:GetFeature(EFeatureType.Weapon)
        local adapterFeature = dragItem:GetFeature(EFeatureType.Adapter)
        local dragItemSlotType = dragItem.InSlot and dragItem.InSlot.SlotType
        if ItemOperaTool.CheckRunWarehouseLogic() and not InventoryLogic.CheckCanMoveInMatching(dragItemSlotType) then
            return InventoryConfig.EFastEquipCheckResult.InMatching
        end
        if (adapterFeature and adapterFeature:IsAdapter()) and (weaponFeature and weaponFeature:IsWeapon()) then
            if Module.FastEquip:CheckFastEquip(dragItem, dropItem, bShowTips) then
                Module.FastEquip:FastEquip(dragItem, dropItem)
                return InventoryConfig.EFastEquipCheckResult.Success
            else
                return InventoryConfig.EFastEquipCheckResult.Fail
            end
        else
            return InventoryConfig.EFastEquipCheckResult.NoFastEquip
        end
    end

    return InventoryConfig.EFastEquipCheckResult.NoFastEquip
end

---@param slot ItemSlot
function InventoryLogic.CommonOnDropForBullet(inOperation, slot, x, y, index, bRotated)
    if slot and slot:CheckVipContainerIsExpired() then
        return ELoadAmmoResult.Fail
    end
    ---@type DragItemPreview
    local dragItemView = inOperation.DefaultDragVisual
    if not dragItemView then return ELoadAmmoResult.NoLoadAmmoLogic end

    local dragItem = dragItemView.item
    if not dragItem then return ELoadAmmoResult.NoLoadAmmoLogic end

    local dropItem = slot:GetItemAtPos(x, y, index)
    if not dropItem then return ELoadAmmoResult.NoLoadAmmoLogic end

    -- 队友绑定的不不走逻辑
    local bDropItemHaveTeammateBindCondition, dropItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(dropItem)
    local bDragItemHaveTeammateBindCondition, dragItemTeammateBindConditionReason = ItemOperaTool.CheckItemHaveTeammateBindCondition(dragItem)
    if bDropItemHaveTeammateBindCondition or bDragItemHaveTeammateBindCondition then
        return ELoadAmmoResult.NoLoadAmmoLogic
    end

    local result
    if ItemOperaTool.CheckRunWarehouseLogic() then
        result = ItemOperaTool.DoLoadingAmmo(dragItem, dropItem)
    else
        result = Module.Looting:DoLoadingAmmo(dragItem, dropItem)
    end

    return result
end

---@param item ItemBase
function InventoryLogic.ShowLevelUpItemConfirmWindow(item)
    local materials = ItemConfigTool.GetExtItemLevelUpMaterialList(item.id)
    if not table.isempty(materials) then
        local extDesRow = ItemConfigTool.GetExtentionBoxDescRowById(item.id)
        local nextLvItemId = extDesRow.NextLvItemID
        local nextLvItemConfig = ItemConfigTool.GetItemConfigById(nextLvItemId)
        ensure(nextLvItemConfig, string.format("Next level item [%s] has no config", tostring(nextLvItemId)))

        local confirmText = string.format(InventoryConfig.Loc.WarehouseExtItemLevelUpConfirmText,
            item.name, nextLvItemConfig.name)
        local fOnConfirmBtnClick = function()
            Server.InventoryServer:DoLevelUpExtItem(item)
        end
        local materialIdsWithNum = {}
        for id, num in pairs(materials) do
            table.insert(materialIdsWithNum, {
                itemId = id,
                num = num
            })
        end

        Module.CommonTips:ShowConfirmItemsWindow(
            confirmText,
            fOnConfirmBtnClick,
            nil,
            nil,
            InventoryConfig.Loc.WarehouseExtItemLevelUpBtnText,
            materialIdsWithNum
        )
    end
end

function InventoryLogic.GetItemCanSellInMultiModeById(id)
    local allMatchItems = Server.InventoryServer:GetItemsById(id)
    local ret = {}
    for gid, item in pairs(allMatchItems) do
        -- if item:IsInDepository() or item:IsInUserContainer() then
        if item:IsInDepository() then
            table.insert(ret, item)
        end
    end

    return ret
end

function InventoryLogic.ShowRepairConfirmWindow(item)
    -- 获取标题
    local confirmText = string.format(InventoryConfig.Loc.WarehouseExtItemRepairConfirmText, item.name)
    -- 确认按钮处理逻辑
    local fOnConfirmBtnClick = function ()
        Server.InventoryServer:DoRepairItem(item)
        -- Facade.UIManager:CloseUI(self)
    end
    Module.CommonTips:ShowConfirmItemsWindow(
        confirmText,
        fOnConfirmBtnClick,
        nil,
        nil,
        InventoryConfig.Loc.WarehouseExtItemRepairBtnText,
        nil
    )
end

function InventoryLogic.ShowSellMissionItemConfirmWindow(item, sellNum)
    local itemName = item.name
    local itemNamesStr = nil
    itemName = string.format(Module.Inventory.Config.Loc.QuickSellHint_QuestItemName, itemName)
    local appendStr
    if sellNum > 0 then
        appendStr = string.format("%s x %d", itemName, sellNum)
    else
        appendStr = itemName
    end
    if not itemNamesStr then
        itemNamesStr = appendStr
    else
        itemNamesStr = itemNamesStr .. CommonConfig.Loc.Comma .. appendStr
    end
    -- 计算价格和获取金币图标
    local totalPrice = math.round(item:GetSingleMallSellPrice() * sellNum)
    local currencyIconText = ECurrencyClientType2RichIconTxt[Server.ShopServer:GetRecycleCurrencyTypeByItem(item)]
    local totalPriceStr = currencyIconText .. tostring(totalPrice)
    itemNamesStr = string.format(Module.Inventory.Config.Loc.QuickSellHint, itemNamesStr, totalPriceStr)
    local fConfirmbackIns = SafeCallBack(function()
        Server.ShopServer:DoSellItemReq(item, sellNum)
    end,nil)
    Module.CommonTips:ShowConfirmWindow(itemNamesStr, fConfirmbackIns, nil, Module.Inventory.Config.Loc.InvSellCancelText, Module.Inventory.Config.Loc.InvSellConfirmText)
end

-----------------------------------------------------------------------
--region Warehouse 2.0

---@param item ItemBase
function InventoryLogic.CommonItemDoubleClickedInWH(item, curSelectDepositId)
    curSelectDepositId = setdefault(curSelectDepositId, ESlotType.MainContainer)

    if not item then
        return false
    end

    local slot = item.InSlot
    if not slot then
        return false
    end

    if item:IsInDepository() then
        if Module.CommonWidget:CheckRunWeaponSelection(item)then
            return false
        end
    end

    local itemFeatureType = item:GetFeatureType()
    if slot:IsDepositorySlot() then
        local isExtendItem = ItemBaseTool.IsExtendItem(item)
        if isExtendItem then
            -- 装备扩容道具
            ItemOperaTool.DoEquipExtItem(item)
        elseif itemFeatureType == EFeatureType.KeyBoxUnlockItem then
            -- 使用钥匙包解锁道具
            Server.InventoryServer:DoUseItem(item)
        elseif item:IsEquipableItem() then
            if item:IsBullet() then
                local targetSlot = ItemBaseTool.GetBulletDefaultEquipSlot(item)
                if targetSlot then
                    ItemOperaTool.DoPlaceItem(item, targetSlot, true)
                else
                    ItemOperaTool.DoCarryItem(item)
                end
            else
                ItemOperaTool.DoEquipItem(item)
            end
        else
            ItemOperaTool.DoCarryItem(item)
        end
    else
        local targetDepositSlot = Server.InventoryServer:GetSlot(curSelectDepositId)
        ItemOperaTool.DoPlaceItem(item, targetDepositSlot, true)
    end
end

function InventoryLogic.CommonItemDoubleClickedInWH2Slot(item, curSelectDepositId, depositIds)
    curSelectDepositId = setdefault(curSelectDepositId, ESlotType.MainContainer)

    if not item then
        return false
    end

    local slot = item.InSlot
    if not slot then
        return false
    end

    if item:IsInDepository() then
        if Module.CommonWidget:CheckRunWeaponSelection(item)then
            return false
        end
    end

    local itemFeatureType = item:GetFeatureType()
    if slot:IsDepositorySlot() then
        local isExtendItem = ItemBaseTool.IsExtendItem(item)
        if isExtendItem then
            -- 装备扩容道具
            ItemOperaTool.DoEquipExtItem(item)
        elseif itemFeatureType == EFeatureType.KeyBoxUnlockItem then
            -- 使用钥匙包解锁道具
            Server.InventoryServer:DoUseItem(item)
        elseif item:IsEquipableItem() then
            ItemOperaTool.DoEquipItem(item)
        else
            ItemOperaTool.DoCarryItem(item)
        end
    elseif slot.SlotType == ESlotType.SafeBox or slot.SlotType == ESlotType.KeyChain or slot.SlotType == ESlotType.MeleeWeapon then

    else
        local allDepositIds = Server.InventoryServer:GetAllDepositIds()
        table.insert(allDepositIds, 1, curSelectDepositId)
        allDepositIds = table.unique(allDepositIds, true)
        for _, depositId in ipairs(allDepositIds) do
            local depositSlot = Server.InventoryServer:GetSlot(depositId)
            -- 是否满足放入条件
            if not ItemOperaTool.CheckItemCanMove(item, depositSlot) then
                return
            elseif depositSlot and depositSlot:CheckItemFitSlot(item) then
                if ItemOperaTool.DoCarryItem(item, depositSlot, false) then
                    return
                end
            end
        end
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.ExtNoEnoughSpace)

        -- -- 若当前的slot无法放入道具则寻找其他的扩容箱
        -- local targetDepositSlot = Server.InventoryServer:GetSlot(curSelectDepositId)
        -- local bInSlot = ItemOperaTool.DoPlaceItem(item, targetDepositSlot, false)
        -- if not bInSlot then
        --     for _, depositId in pairs(depositIds) do
        --         local otherDepositSlot = Server.InventoryServer:GetSlot(depositId)
        --         if ItemOperaTool.DoPlaceItem(item, otherDepositSlot, false) then
        --             return
        --         end
        --     end

        --     Module.CommonTips:ShowSimpleTip(string.format(InventoryConfig.Loc.ExtNoEnoughSpace, targetDepositSlot:GetSlotName()))
        -- end
    end
end

---@param item ItemBasef
function InventoryLogic.CommonItemDoubleClickedInWHForCarry(item, curSelectDepositId)
    curSelectDepositId = setdefault(curSelectDepositId, ESlotType.MainContainer)
    if not item then
        return false
    end

    local slot = item.InSlot
    if not slot then
        return false
    end

    if slot:IsDepositorySlot() then
        if item:IsEquipableItem() then
            ItemOperaTool.DoEquipItem(item)
        else
            ItemOperaTool.DoCarryItem(item)
        end
    else
        local targetDepositSlot = Server.InventoryServer:GetSlot(curSelectDepositId)
        ItemOperaTool.DoPlaceItem(item, targetDepositSlot)
    end
end

---@param item ItemBase
function InventoryLogic.GetAutoSnapSlotInWH(item)
    local autoSnapSlot = nil
    ---@type AdapterFeature
    local adapterFeature = item:GetFeature(EFeatureType.Adapter)
    if adapterFeature and adapterFeature:IsAdapterInGun() then
        -- 枪上的配件，吸附有特殊的规则
        local gid = adapterFeature:GetRootGid()
        local parentItem = Server.InventoryServer:GetItemByGid(gid)
        if parentItem and parentItem:IsInDepository() then
            autoSnapSlot = Server.InventoryServer:GetItemAvaiableCarrySlot(item)
        else
            local curDepositId = Module.Inventory.Field:GetCurDepositId()
            autoSnapSlot = Server.InventoryServer:GetSlot(curDepositId)
        end

        return autoSnapSlot
    end

    if item:IsInDepository() then
        if item:IsEquipableItem() then
            if item:IsBullet() then
                autoSnapSlot = ItemBaseTool.GetBulletDefaultEquipSlot(item)
                if not autoSnapSlot then
                    autoSnapSlot = Server.InventoryServer:GetItemAvaiableCarrySlot(item)
                end
            else
                autoSnapSlot = Server.InventoryServer:GetEquipmentAvailableSlot(item)
            end
        else
            autoSnapSlot = Server.InventoryServer:GetItemAvaiableCarrySlot(item)
        end
    else
        local curDepositId = Module.Inventory.Field:GetCurDepositId()
        autoSnapSlot = Server.InventoryServer:GetSlot(curDepositId)
    end

    return autoSnapSlot
end

---@param item ItemBase
function InventoryLogic.CommonBuildDragDropInfoInWH(item, itemPreview)
    ---@type ItemDragDropInfo
    local itemDragDropInfo = {
        item = item,
        itemPreview = itemPreview,
        allAvailableSlots = {},
        defaultTargetSlot = nil,
        curDepositId = Module.Inventory.Field:GetCurDepositId(),
    }

    if not item then
        return itemDragDropInfo
    end

    ItemOperaTool.EnableUseCacheToSpeedUp(true)

    local allAvailableSlots = {}
    local allPlayerSlots = Server.InventoryServer:GetAllSlots(ESlotGroup.Player)
    for slotType, slot in pairs(allPlayerSlots) do
        if slot:IsEquipableSlot() or slot:IsContainerSlot() or slot:IsDepositorySlot() then
            ---@type ItemLocation
            local targetLoc
            if slot:IsEquipableSlot() then
                targetLoc = ItemLocation:NewIns()
                targetLoc:InitEquipLoc(slot)
            else
                targetLoc = slot:TryFindLocationForItem(item)
                if targetLoc == nil and not slot:IsDepositorySlot() and ItemOperaTool.TryPlaceItems({item}, slot) then
                    targetLoc = ItemLocation:NewIns()
                    targetLoc:Init(slot, -1, -1)
                end
            end
            if targetLoc and ItemOperaTool.VerifyItemForLocation(item, targetLoc, false) then
                -- 对非空的容器进行再判断
                if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, slot) then
                    if ItemOperaTool.TryPlaceContainerItem(item, slot) then
                        table.insert(allAvailableSlots, slot)
                    end
                else
                    table.insert(allAvailableSlots, slot)
                end
            end
        end
    end

    itemDragDropInfo.allAvailableSlots = allAvailableSlots
    ItemOperaTool.SetCacheAvailableSlotsForItem(item, allAvailableSlots)
    itemDragDropInfo.defaultTargetSlot = InventoryLogic.GetAutoSnapSlotInWH(item)

    ItemOperaTool.EnableUseCacheToSpeedUp(false)

    return itemDragDropInfo
end

---@param detailIns ItemDetailPanel
local function fCustomCloseCallback(detailIns)
    local refItemWidget = detailIns:GetParentWidget()
    if refItemWidget then
        Module.Inventory:DoSelectItemInWH(refItemWidget, false)
    end
end

---@param item ItemBase
function InventoryLogic.CreateCommonDetailBtnsInWH(item, bSimplePanel)
    local btnTypeList = {}

    -- 根据道具类型设定仓库按钮
    local isContainerItem = false
    local isPlayerEquipment = false
    local isAdapter = false
    local isExtendItem = false
    local isBlindBox = false
    local isNarrativeProps = false
    local isTreasureMap = false
    local isWeapon = false
    local isPoorWeapon = false
    local isRandomGift = false
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local isKeyBoxUnlockItem = false
    local inSlotType = item.InSlot and item.InSlot.SlotType or ESlotType.None
    local isSafeBox = false
    local isKeyChain = false
    local isBullet = false
    if featureType == EFeatureType.Equipment then
        isPlayerEquipment = itemFeature:IsPlayerEquipment()
        isContainerItem = itemFeature:IsContainerItem()
        isExtendItem = itemFeature:IsExtendItem()
        isSafeBox = itemFeature:IsSafeBox()
        isKeyChain = itemFeature:IsKeyChain()
    elseif featureType == EFeatureType.Reward then
        isBlindBox = itemFeature:IsBlindBox()
        isRandomGift = itemFeature:IsRandomGift()
    elseif featureType == EFeatureType.Default then
        isNarrativeProps = itemFeature:IsNarrativeProps()
        isTreasureMap = itemFeature:IsTreasureMap()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
        isPoorWeapon = itemFeature:IsPoorWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.KeyBoxUnlockItem then
        isKeyBoxUnlockItem = true
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    end

    local bVipExtExpire = item and item.InSlot and item.InSlot:CheckVipContainerIsExpired()

    local function AddBtnTypeList(btnType, bSettlement, bVipExtExpire)
        -- 某些按钮在结算转移界面不需要显示
        setdefault(bSettlement, false)
        if not bSettlement and not bVipExtExpire then
            table.insert(btnTypeList, btnType)
        end
    end

    -- 默认应该都有出售
    -- AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell)
    if isSafeBox then
    elseif isWeapon or isPlayerEquipment then
        if item and item.InSlot and (not item.InSlot:IsEquipContainerSlot()) then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        end
        if isWeapon then
            if bSimplePanel then
                local desc = itemFeature:GetRawDescObj()
                local curBulletId = desc:GetCurrentAmmoItemId()
                if curBulletId and curBulletId ~= 0 then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.UnEquipBullet)
                end
            end
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.BuyBullet, ItemOperaTool.bInSettlement, bVipExtExpire)
            if isPoorWeapon then
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Maintain)
            elseif not itemFeature:IsAirBrush() and not ItemOperaTool.bInSettlement and not Server.MatchServer:GetIsReadytoGo() then
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Assembly, bVipExtExpire)
            end
        elseif isPlayerEquipment then
            if itemFeature:IsBreastPlate() or itemFeature:IsHelmet() then
                local maxDurability=itemFeature:GetDurabilityMaxValue()
                local curDurability=itemFeature:GetDurabilityCurValue()
                if (itemFeature:IsBreastPlate() and maxDurability <= itemFeature.MIN_CAN_REPAIR_DURABILITY)
                    or (itemFeature:IsHelmet() and maxDurability <= 5) then
                    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.RepairGrey, bVipExtExpire)
                elseif curDurability < maxDurability then
                    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Repair, bVipExtExpire)
                end
            end
        end
        if item:IsEquipped() then
            local function fUnEquipInWarehouse(item)
                local curSelectDepositId = Module.Inventory.Field:GetCurDepositId()
                local targetDepositSlot = Server.InventoryServer:GetSlot(curSelectDepositId)
                ItemOperaTool.DoPlaceItemByPriority(item, targetDepositSlot, true)
                Module.ItemDetail:CloseAllPopUI()
            end
            --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
            AddBtnTypeList({btnType = "UnEquip", fOnClick = fUnEquipInWarehouse, txt = Module.ItemDetail.Config.Loc.unEquip, pcRightIcon = InventoryLogic.GetIconList().UnEquip})
            --- END MODIFICATION
        else
            -- 需判断该装备所在槽位是否有道具，没有道具则装备，有道具则替换
            local targetSlot = Server.InventoryServer:GetEquipmentAvailableSlot(item)
            if targetSlot and targetSlot:GetEquipItem() then
                local copyBtnType = simpleclone(Module.ItemDetail.Config.ButtonType.Equip)
                copyBtnType.txt = Module.ItemDetail.Config.Loc.replaceExtItem
                copyBtnType.pcRightIcon = Module.ItemDetail.Config.pcRightIconList.Replace
                AddBtnTypeList(copyBtnType, nil, bVipExtExpire)
            else
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip)
            end
            -- 右侧网格携带，左侧网格放回(右键详情页)
            if bSimplePanel then
                --- BEGIN MODIFICATION @ VIRTUOS: 主机需���替换按键图标
                if item:IsInDepository() then
                    local function fCarryBtnCallBack()
                        ItemOperaTool.DoCarryItem(item)
                    end
                    AddBtnTypeList({btnType = "Carry", fOnClick = fCarryBtnCallBack, txt = Module.ItemDetail.Config.Loc.carry, pcRightIcon = InventoryLogic.GetIconList().UnEquip}, ItemOperaTool.bInSettlement)
                elseif item:IsInUserContainer() then
					AddBtnTypeList(InventoryLogic.GetButtonType().PutBack, ItemOperaTool.bInSettlement)
                end
                --- END MODIFICATION
            end
        end
    elseif isExtendItem then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        if curDepositNum > 1 and Facade.GameFlowManager:CheckIsInFrontEnd() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.ReplaceExtItem, bVipExtExpire)
        end
        if availableDepositNum > 0 then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipExtItem)
        end
    elseif isAdapter then
        if item:IsAdapterInGun() then
            --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
            AddBtnTypeList(InventoryLogic.GetButtonType().UnEquip)
            --- END MODIFICATION

        else
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
            if InventoryLogic.CheckIsEquippedWeapon(item) then
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipAdapter, bVipExtExpire)
            else
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipAdapterGrey, bVipExtExpire)
            end
            --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
            if item:IsInUserContainer() then
	    	AddBtnTypeList(InventoryLogic.GetButtonType().PutBack, ItemOperaTool.bInSettlement)
            else
                if IsHD() then
                    AddBtnTypeList(InventoryLogic.GetButtonType().Carry, ItemOperaTool.bInSettlement)
                end
            end
            --- END MODIFICATION
        end
    elseif isRandomGift then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Open, bVipExtExpire)
    elseif isBlindBox then
        if itemFeature:IsUndecipheredBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.PickUp)
        elseif itemFeature:IsUnableDecipherBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        elseif itemFeature:IsDecipheredBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.OpenBlind, bVipExtExpire)
        elseif itemFeature:IsSpecialBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.OpenBlind, bVipExtExpire)
        end
    elseif isKeyBoxUnlockItem then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Use) 
    elseif isBullet then
        if bSimplePanel then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.SimpleSell, ItemOperaTool.bInSettlement)
        else
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        end
        if item.num > 1 then
            --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
            if bSimplePanel then
                AddBtnTypeList(InventoryLogic.GetButtonType().FastSplit, nil, bVipExtExpire)
            else
                AddBtnTypeList(InventoryLogic.GetButtonType().Split, nil, bVipExtExpire)
            end
            --- END MODIFICATION
        end
        --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
        if item:IsInDepository() then
            if IsHD() then
                AddBtnTypeList(InventoryLogic.GetButtonType().Carry, ItemOperaTool.bInSettlement)
            end
        else
            AddBtnTypeList(InventoryLogic.GetButtonType().PutBack, ItemOperaTool.bInSettlement)
        end
        --- END MODIFICATION
    else
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Sell, ItemOperaTool.bInSettlement)
        -- if isNarrativeProps or isTreasureMap then
        --     AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Check)
        -- end
        if item.num > 1 then
            if bSimplePanel then
                AddBtnTypeList(InventoryLogic.GetButtonType().FastSplit, nil, bVipExtExpire)
            else
                --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
                AddBtnTypeList(InventoryLogic.GetButtonType().Split, nil, bVipExtExpire)
                --- END MODIFICATION
            end

        end
        --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
        if item:IsInUserContainer() or inSlotType == ESlotType.Medicine then
            AddBtnTypeList(InventoryLogic.GetButtonType().PutBack, ItemOperaTool.bInSettlement)
        else
            if IsHD() then
                AddBtnTypeList(InventoryLogic.GetButtonType().Carry, ItemOperaTool.bInSettlement)
            end
        end
        --- END MODIFICATION
    end
    --[[if ItemHelperTool.GetIsExchangeCostItemById(item.id) then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Exchange)
    end]]--

    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.RelatedSearch)
    end
    return btnTypeList
end

--- 手游局外(仓库+结算转移)折叠态详情页特有，每种道具显示2个按钮常用按钮
--- 具体参见https://www.figma.com/design/we3CRdYqaOiLk7lcocTD0K/%E4%BB%93%E5%BA%93?node-id=8237-165290&t=l5g4iFCyRq4OTW2I-0
--- 附录1：折叠版详情页按钮汇总
---@param item ItemBase
function InventoryLogic.CreateCommonFoldDetailBtnsInWH(item)
    local btnTypeList = {}

    -- 根据道具类型设定仓库按钮
    local isContainerItem = false
    local isPlayerEquipment = false
    local isAdapter = false
    local isExtendItem = false
    local isBlindBox = false
    local isNarrativeProps = false
    local isTreasureMap = false
    local isWeapon = false
    local isPoorWeapon = false
    local isRandomGift = false
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local isKeyBoxUnlockItem = false
    local inSlotType = item.InSlot and item.InSlot.SlotType or ESlotType.None
    local isSafeBox = false
    local isKeyChain = false
    local isBullet = false
    local isHealth = false
    local isKey = false
    local isCollect = false
    local isArchiveItem = false
    local isPlayerArchiveItem = false
    if featureType == EFeatureType.Equipment then
        isPlayerEquipment = itemFeature:IsPlayerEquipment()
        isContainerItem = itemFeature:IsContainerItem()
        isExtendItem = itemFeature:IsExtendItem()
        isSafeBox = itemFeature:IsSafeBox()
        isKeyChain = itemFeature:IsKeyChain()
    elseif featureType == EFeatureType.Reward then
        isBlindBox = itemFeature:IsBlindBox()
        isRandomGift = itemFeature:IsRandomGift()
    elseif featureType == EFeatureType.Default then
        isNarrativeProps = itemFeature:IsNarrativeProps()
        isTreasureMap = itemFeature:IsTreasureMap()
        isCollect = itemFeature:IsCollectableItem()
        isArchiveItem = itemFeature:IsArchiveItem()
        isPlayerArchiveItem = itemFeature:IsPlayerArchiveItem()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
        isPoorWeapon = itemFeature:IsPoorWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.KeyBoxUnlockItem then
        isKeyBoxUnlockItem = true
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    elseif featureType == EFeatureType.Health then
        isHealth = true
    elseif featureType == EFeatureType.Key then
        isKey = true
    end

    local bVipExtExpire = item and item.InSlot and item.InSlot:CheckVipContainerIsExpired()

    local function AddBtnTypeList(btnType, bSettlement, bVipExtExpire)
        -- 某些按钮在结算转移界面不需要显示
        setdefault(bSettlement, false)
        if not bSettlement and not bVipExtExpire then
            table.insert(btnTypeList, btnType)
        end
    end

    if isSafeBox then
    elseif isWeapon or isPlayerEquipment then
        if item:IsEquipped() then
            local function fUnEquipInWarehouse(item)
                local curSelectDepositId = Module.Inventory.Field:GetCurDepositId()
                local targetDepositSlot = Server.InventoryServer:GetSlot(curSelectDepositId)
                ItemOperaTool.DoPlaceItem(item, targetDepositSlot, true)
                Module.ItemDetail:CloseAllPopUI()
            end
            --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
            AddBtnTypeList({btnType = "UnEquip", fOnClick = fUnEquipInWarehouse, txt = Module.ItemDetail.Config.Loc.unEquip, pcRightIcon = InventoryLogic.GetIconList().UnEquip})
            --- END MODIFICATION
        else
            -- 需判断该装备所在槽位是否有道具，没有道具则装备，有道具则替换
            local targetSlot = Server.InventoryServer:GetEquipmentAvailableSlot(item)
            if targetSlot and targetSlot:GetEquipItem() then
                local copyBtnType = simpleclone(Module.ItemDetail.Config.ButtonType.Equip)
                copyBtnType.txt = Module.ItemDetail.Config.Loc.replaceExtItem
                copyBtnType.pcRightIcon = Module.ItemDetail.Config.pcRightIconList.Replace
                AddBtnTypeList(copyBtnType, nil, bVipExtExpire)
            else
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip)
            end
        end
        if isWeapon then
            -- 枪有卸下子弹按钮
            local desc = itemFeature:GetRawDescObj()
            local curBulletId = desc:GetCurrentAmmoItemId()
            if curBulletId and curBulletId ~= 0 then
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.UnEquipBullet)
            end
            if isPoorWeapon then
                -- TODO:31开头的武器，暂未投放，未对齐显示逻辑，这里是默认逻辑
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Maintain, nil, bVipExtExpire)
            end
        elseif isPlayerEquipment then
            if itemFeature:IsBreastPlate() or itemFeature:IsHelmet() then
                -- TODO:这里改了原来逻辑，耐久度不满能修理显示修理，否则显示出售
                local maxDurability = itemFeature:GetDurabilityMaxValue()
                local curDurability = itemFeature:GetDurabilityCurValue()
                if curDurability < maxDurability and ((itemFeature:IsBreastPlate() and maxDurability > itemFeature.MIN_CAN_REPAIR_DURABILITY) or (itemFeature:IsHelmet() and maxDurability > 5)) then
                    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Repair)
                else
                    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
                end
            end
        end
        if item.InSlot and not item.InSlot:IsEquipContainerSlot() then
            -- 防具只有耐久度满才显示出售，单独处理
            if isPlayerEquipment and (itemFeature:IsBreastPlate() or itemFeature:IsHelmet()) then
            else
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
            end
        else
            loginfo("item.InSlot is nil")
        end
    elseif isExtendItem then
        local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
        local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
        if availableDepositNum > 0 then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipExtItem)
        else
            if curDepositNum > 1 and Facade.GameFlowManager:CheckIsInFrontEnd() then
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.ReplaceExtItem, nil, bVipExtExpire)
            end
        end
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isAdapter then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.RelatedSearch, ItemOperaTool.bInSettlement)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isRandomGift then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Open)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isBlindBox then
        if itemFeature:IsUndecipheredBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.PickUp)
        elseif itemFeature:IsUnableDecipherBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
        elseif itemFeature:IsDecipheredBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.OpenBlind)
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
        elseif itemFeature:IsSpecialBrick() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.OpenBlind)
        end
    elseif isKeyBoxUnlockItem then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Use)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isBullet then
        if item.num > 1 then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldFastSplit, nil, bVipExtExpire)
        end
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isHealth then
        --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
        if item:IsInUserContainer() or inSlotType == ESlotType.Medicine then
            AddBtnTypeList(InventoryLogic.GetButtonType().PutBack, ItemOperaTool.bInSettlement)
        else
            AddBtnTypeList(InventoryLogic.GetButtonType().Carry, ItemOperaTool.bInSettlement)
        end
        --- END MODIFICATION
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isKey then
        if Facade.GameFlowManager:CheckIsInFrontEnd() or Module.Inventory:CheckRunWarehouseLogic() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.PlaceOfUse)
        end
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isCollect then
        if Facade.GameFlowManager:CheckIsInFrontEnd() then
            if item.num > 1 then
                AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldFastSplit, nil, bVipExtExpire)
            else
                local allUsePlaceInfo = {}
                local haveUsePlace = false
                ItemDetailLogic.InitUsePlace(item, allUsePlaceInfo)
                for _, sourceInfo in ipairs(allUsePlaceInfo) do
                    if #sourceInfo.dataList > 0 then
                        haveUsePlace = true
                        break
                    end
                end
                if haveUsePlace then
                    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.UsePlaceOnFold)
                end
            end
        end
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    elseif isArchiveItem or isPlayerArchiveItem then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    else
        --TODO:没列出来的就按下面逻辑了，有问题再拉群，只会显示前2个，下面写的我理解为只会出现1个
        if item.num > 1 then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldFastSplit, nil, bVipExtExpire)
        end
        --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
        if item:IsInUserContainer() or inSlotType == ESlotType.Medicine then
            AddBtnTypeList(InventoryLogic.GetButtonType().PutBack, ItemOperaTool.bInSettlement)
        else
            AddBtnTypeList(InventoryLogic.GetButtonType().Carry, ItemOperaTool.bInSettlement)
        end
        --- END MODIFICATION
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.FoldSell, ItemOperaTool.bInSettlement)
    end
    return btnTypeList
end

---@param item ItemBase
function InventoryLogic.CreateTransItemBtnListInWH(item)
    if IsHD() or not item then return {} end

    local btnTypeList = {}

    -- 根据道具类型设定仓库按钮
    local isAdapter = false
    local isBlindBox = false
    local isWeapon = false
    local isPoorWeapon = false
    local isRandomGift = false
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local isKeyChain = false
    local isBullet = false
    local isKey = false
    local isHealth = false
    local isCollectableItem = false
    local isChest = false
    local isHelmet = false
    local isBreastPlate = false
    local isBag = false
    if featureType == EFeatureType.Equipment then
        isKeyChain = itemFeature:IsKeyChain()
        isChest = itemFeature:IsChestHanging()
        isHelmet = itemFeature:IsHelmet()
        isBreastPlate = itemFeature:IsBreastPlate()
        isBag = itemFeature:IsBag()
    elseif featureType == EFeatureType.Reward then
        isBlindBox = itemFeature:IsBlindBox()
        isRandomGift = itemFeature:IsRandomGift()
    elseif featureType == EFeatureType.Default then
        isCollectableItem = itemFeature:IsCollectableItem()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
        isPoorWeapon = itemFeature:IsPoorWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    elseif featureType == EFeatureType.Health then
        isHealth = true
    elseif featureType == EFeatureType.Key then
        isKey = true
    end

    local function AddBtnTypeList(btnType)
        local data = {}
        data.item = item
        data.transBtnType = btnType
        table.insert(btnTypeList, data)
    end

    local bInChest = item.InSlot and item.InSlot.SlotType == ESlotType.ChestHangingContainer
    local bInPocket = item.InSlot and item.InSlot.SlotType == ESlotType.Pocket
    local bInBag = item.InSlot and item.InSlot.SlotType == ESlotType.BagContainer
    local bInSafeBox = item.InSlot and item.InSlot.SlotType == ESlotType.SafeBoxContainer
    local bHasBag = Server.InventoryServer:GetBagItem()
    -- local bHasChestHanging = Server.InventoryServer:GetChestHangingItem()
    -- 枪械
    if isWeapon or isPoorWeapon then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
    elseif isKey then
        if item.InSlot and item.InSlot.SlotType == ESlotType.KeyChainContainer then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
            if bHasBag then
                AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
            end
        else
            if not bInChest and not bInPocket then
                AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
            end
            if not bInBag and bHasBag then
                AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
            end
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToCardBag)
        end
    elseif isAdapter then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
    elseif isBullet or isHealth or isCollectableItem then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        if not bInSafeBox then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        end
    elseif isChest then
        if item.InSlot and item.InSlot.SlotType ~= ESlotType.ChestHanging and not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        -- if not bInSafeBox then
        --     AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        -- end
    elseif isBag then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if item.InSlot and item.InSlot.SlotType ~= ESlotType.Bag and not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        -- if not bInSafeBox then
        --     AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        -- end
    elseif isHelmet or isBreastPlate then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        -- if not bInSafeBox then
        --     AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        -- end
    end

    return btnTypeList
end


function InventoryLogic.OpenDetailPanelInWH(item, refWidget, bShowItemDetialBtn)
    local btnTypeList
    if Module.ItemDetail:CanOpenFoldDetail() then
        btnTypeList = InventoryLogic.CreateCommonFoldDetailBtnsInWH(item)
    else
        btnTypeList = InventoryLogic.CreateCommonDetailBtnsInWH(item)
    end
    local transBtnList = InventoryLogic.CreateTransItemBtnListInWH(item)
    if bShowItemDetialBtn == false then
        transBtnList = nil
    end
    bShowItemDetialBtn = setdefault(bShowItemDetialBtn, #btnTypeList > 0)

    -- 仓库近战武器详情页不应该有功能按钮
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    if featureType == EFeatureType.Weapon then
        if itemFeature:IsKnife() then
            bShowItemDetialBtn = false
        end
    end

    ---@param detailIns ItemDetailPanel
    local function fOnWarehouseDetailPanelLoaded(detailIns)
        if detailIns then
            -- 共有接口
            detailIns:IgnoreWidgetOnBtnUp(refWidget)
            detailIns:SetCustomClosePanelLogic(fCustomCloseCallback)
            if not Module.ItemDetail:CanOpenFoldDetail() then
                -- 展开态详情页特有接口
                detailIns:SetHideInDraging(true)
                detailIns:SetAdapterDragState(true)
                detailIns:SetAdapterTipsShowButtonState(true)
                detailIns:SetAdapterFastUnequipState(true)
                if bShowItemDetialBtn == false then
                    detailIns:SetAdapterTipsShowButtonState(false)
                    detailIns:SetAdapterFastUnequipState(false)
                    detailIns:SetWeaponBulletUnEquipVisible(false)
                end
            end
        end
    end
    LogAnalysisTool.SetCurHaveItemDetailUI(LogAnalysisTool.EItemDetailFromUIType.Inventory)
    Module.ItemDetail:OpenItemDetailPanel(item, refWidget, false, bShowItemDetialBtn, btnTypeList, nil, fOnWarehouseDetailPanelLoaded, nil, nil, nil, transBtnList, nil, true)
end

function InventoryLogic.CloseDetailPanelInWH(bOnlyTop, bCloseSimpleBtnPanel)
    bOnlyTop = setdefault(bOnlyTop, true)
    Module.ItemDetail:CloseItemDetailPanel(0, bOnlyTop, bCloseSimpleBtnPanel)
end

function InventoryLogic.CreateSimpleBtenList(item)
    local btnTypeList = InventoryLogic.CreateCommonDetailBtnsInWH(item, true)

    local function AddBtnTypeList(btnType, index)
        index = setdefault(index, 1)
        table.insert(btnTypeList, index, btnType)
    end

    -- 1. 所有道具都有详情页和收藏选项
    if Module.ItemDetail:IsShowLabelMarkBtn(item) then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.LabelMark)
    end
    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.OpenDetailPanel)
    -- 2. 局外所有道具都可能有来源和用途，但过期扩容箱内的道具不显示
    local itemSlot = item and item.InSlot
    if Facade.GameFlowManager:CheckIsInFrontEnd() and not itemSlot:CheckVipContainerIsExpired() then
        local allSourceInfo = {}
        local allUsePlaceInfo = {}
        local haveSource = false
        local haveUsePlace = false
	    ItemDetailLogic.InitSource(item, allSourceInfo)
	    ItemDetailLogic.InitUsePlace(item, allUsePlaceInfo)

        for _, sourceInfo in ipairs(allSourceInfo) do
            if #sourceInfo.dataList > 0 then
               haveSource = true
               break
            end
        end

        for _, sourceInfo in ipairs(allUsePlaceInfo) do
            if #sourceInfo.dataList > 0 then
               haveUsePlace = true
               break
            end
        end

        if haveSource then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Source)
        end
        if haveUsePlace then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.UsePlace)
        end
    end
    -- 3. 按钮有使用地点
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    if featureType == EFeatureType.Key then
        if Facade.GameFlowManager:CheckIsInFrontEnd() or Module.Inventory:CheckRunWarehouseLogic() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.PlaceOfUse)
        end
    end

    return btnTypeList
end

function InventoryLogic.OpenSimpleBtnListPanelInWH(refWidget, alignPosition)
    local item = refWidget.item

    local btnTypeList = InventoryLogic.CreateSimpleBtenList(item)
    local customData = {
        ["itemWidget"] = refWidget
    }

    --- 数量大于1时添加快速拆分按钮
    -- if item.num > 1 then
    --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
    --      table.insert(btnTypeList, InventoryLogic.GetButtonType().FastSplit)
    --- END MODIFICATION
    -- end

    local function fCustomCloseCallback()
        if Module.Inventory.Field.bInDetailPanel then
            Module.Inventory.Field:SetIsInDetailPanel(false)
            return
        end
        if refWidget then
            Module.Inventory:DoSelectItemInWH(refWidget, false)
        end
    end
    Module.ItemDetail:OpenSimpleBtnListPanel(item, btnTypeList, alignPosition, fCustomCloseCallback, nil, customData)
end

function InventoryLogic.CloseSimpleBtnListPanelInWH()
    Module.ItemDetail:CloseSimpleBtnListPanel()
end

--BEGIN MODIFICATION @ VIRTUOS : 
---@param item ItemBase
function InventoryLogic.CreateTransferItemBtnList(item)
    if not item.InSlot then
        return
    end
    
    if not IsHD() then
        return 
    end

    -- 根据道具类型设定仓库按钮
    local isAdapter = false
    local isBlindBox = false
    local isWeapon = false
    local isPoorWeapon = false
    local isRandomGift = false
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local isKeyChain = false
    local isBullet = false
    local isKey = false
    local isHealth = false
    local isCollectableItem = false
    local isChest = false
    local isHelmet = false
    local isBreastPlate = false
    local isBag = false
    if featureType == EFeatureType.Equipment then
        isKeyChain = itemFeature:IsKeyChain()
        isChest = itemFeature:IsChestHanging()
        isHelmet = itemFeature:IsHelmet()
        isBreastPlate = itemFeature:IsBreastPlate()
        isBag = itemFeature:IsBag()
    elseif featureType == EFeatureType.Reward then
        isBlindBox = itemFeature:IsBlindBox()
        isRandomGift = itemFeature:IsRandomGift()
    elseif featureType == EFeatureType.Default then
        isCollectableItem = itemFeature:IsCollectableItem()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
        isPoorWeapon = itemFeature:IsPoorWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    elseif featureType == EFeatureType.Health then
        isHealth = true
    elseif featureType == EFeatureType.Key then
        isKey = true
    end

    local btnTypeList = {}
    local function AddBtnTypeList(btnType, isRec)
        -- 避免污染源配置
        isRec = setdefault(isRec, false)
        local copyBtnType = simpleclone(btnType)
        copyBtnType.isRec = isRec
        table.insert(btnTypeList, copyBtnType)
    end

    local itemNeedTransferType = Module.CommonWidget:GetItemNeedTransferType(item)
    if itemNeedTransferType == Module.CommonWidget.Config.EItemTransferType.None then
        return nil
    end

    local MainWeaponTypes = {
        ItemConfig.EWeaponItemType.Rifle,
        ItemConfig.EWeaponItemType.Submachine,
        ItemConfig.EWeaponItemType.Shotgun,
        ItemConfig.EWeaponItemType.LightMachine,
        ItemConfig.EWeaponItemType.PrecisionShootingRifle,
        ItemConfig.EWeaponItemType.Sniper,
    }
    
    local bIsMainWeaponItem = false
    if isWeapon then
        bIsMainWeaponItem = table.contains(MainWeaponTypes, item.itemSubType)
    end

    if bIsMainWeaponItem then
        local mainWeaponLeftSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft)
        local mainWeaponRightSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight)

        if mainWeaponLeftSlot and mainWeaponLeftSlot:GetEquipItem() ~= nil then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.ReplaceMainWeaponLeft)
        else
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipMainWeaponLeft)
        end

        if mainWeaponRightSlot and mainWeaponRightSlot:GetEquipItem() ~= nil then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.ReplaceMainWeaponRight)
        else
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipMainWeaponRight)
        end
    else
        -- 目前总是显示四个选项，没有去判断是否存在对应的容器，后续可能会完善逻辑
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.TransToChestOrPocket)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.TransToBag)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.TransToCardBag)
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.TransToSafeBox)
    end

    return btnTypeList
end

function InventoryLogic.OpenTransferItemBtnListPanelInWH(refWidget, alignPosition)
    ---@type ItemBase
    local item = refWidget.item
    local btnTypeList = InventoryLogic.CreateTransferItemBtnList(item)

    if btnTypeList == nil then
        Module.CommonWidget:SetOpenedPopItemView(nil)
        return
    end

    local function OpenTransferItemBtnListPanel()
        local function fCustomCloseCallback()
            if refWidget then
                Module.Looting:DoSelectItem(refWidget, false)
            end
        end

        local customData = {
            ["itemWidget"] = refWidget
        }
        Module.ItemDetail:OpenTransferItemBtnListPanel(item, btnTypeList, alignPosition, fCustomCloseCallback, nil, customData)
    end

    OpenTransferItemBtnListPanel()
end

function InventoryLogic.CloseTransferItemBtnListPanelInWH()
    Module.ItemDetail:CloseSimpleBtnListPanel()
end
--END MODIFICATION

function InventoryLogic.OpenItemSplitPanel(item, parentWidget)
    if item and item.num > 1 then
        Module.ItemDetail:OpenItemSplitPanel(item, parentWidget)
    end
end

-- 打开收藏室中DIY展柜界面内的道具详情页

function InventoryLogic.CreateInteractionBtn(item)
    -- 在收藏室中可放置和卸下的道具添加，其他的不添加
    local btnTypeList = {}

    -- 满足收藏的道具只需要放置和卸下按钮，其余的都不需要显示
    -- 可放入收藏室的道具
    if ItemOperaTool.ItemCanPutInDIYCabinet(item) then
        -- 卸下和放回
        if item:IsCollectionCabinetItem() then
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.CollectionRoomUnLoad)
        else
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.CollectionRoomPutIn)
        end
    end
    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RelatedSearch)

    return btnTypeList
end

function InventoryLogic.OpenDetailPanelInCollectionRoom(item, refWidget, bShowItemDetialBtn)
    local btnTypeList = InventoryLogic.CreateInteractionBtn(item)
    bShowItemDetialBtn = setdefault(bShowItemDetialBtn, #btnTypeList > 0)

    local function fOnWarehouseDetailPanelLoaded(detailIns)
        if detailIns then
            local function fCallback(detail)
                -- 由于详情页的关闭比点击按钮先执行，所以需要延迟
                Timer.DelayCall(0.1, fCustomCloseCallback, self, detail)
            end
            detailIns:SetCustomClosePanelLogic(fCallback)
        end
    end
    Module.ItemDetail:OpenItemDetailPanel(item, refWidget, false, bShowItemDetialBtn, btnTypeList, nil, fOnWarehouseDetailPanelLoaded, nil, nil, nil, nil, nil, true)
end

function InventoryLogic.CreateSimpleListInCollectionRoom(item)
    local btnTypeList = InventoryLogic.CreateInteractionBtn(item)
  
    local function AddBtnTypeList(btnType, index)
        index = setdefault(index, 1)
        table.insert(btnTypeList, index, btnType)
    end

    -- 1. 所有道具都有详情页和收藏选项
    if Module.ItemDetail:IsShowLabelMarkBtn(item) then
        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.LabelMark)
    end
    AddBtnTypeList(Module.ItemDetail.Config.ButtonType.OpenDetailPanel)
    -- 2. 局外所有道具都可能有来源和用途，但过期扩容箱内的道具不显示
    local itemSlot = item and item.InSlot
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        local allSourceInfo = {}
        local allUsePlaceInfo = {}
        local haveSource = false
        local haveUsePlace = false
	    ItemDetailLogic.InitSource(item, allSourceInfo)
	    ItemDetailLogic.InitUsePlace(item, allUsePlaceInfo)

        for _, sourceInfo in ipairs(allSourceInfo) do
            if #sourceInfo.dataList > 0 then
               haveSource = true
               break
            end
        end

        for _, sourceInfo in ipairs(allUsePlaceInfo) do
            if #sourceInfo.dataList > 0 then
               haveUsePlace = true
               break
            end
        end

        if haveSource then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Source)
        end
        if haveUsePlace then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.UsePlace)
        end
    end
    -- 3. 按钮有使用地点
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    if featureType == EFeatureType.Key then
        if Facade.GameFlowManager:CheckIsInFrontEnd() or Module.Inventory:CheckRunWarehouseLogic() then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.PlaceOfUse)
        end
    end

    return btnTypeList
end

function InventoryLogic.OpenSimpleListPanelInCollectionRoom(refWidget, alignPosition)
    local item = refWidget.item
    -- 满足收藏的道具只需要放置和卸下按钮，其余的都不需要显示
    local btnTypeList = InventoryLogic.CreateSimpleListInCollectionRoom(item)
    local customData = {
        ["itemWidget"] = refWidget
    }

    local function fCustomCloseCallback()
        if Module.Inventory.Field.bInDetailPanel then
            Module.Inventory.Field:SetIsInDetailPanel(false)
            return
        end
        if refWidget then
            Module.Inventory:DoSelectItemInWH(refWidget, false)
        end
    end
    Module.ItemDetail:OpenSimpleBtnListPanel(item, btnTypeList, alignPosition, fCustomCloseCallback, nil, customData)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Ext arrangement 扩容管理

function InventoryLogic.ShowExtArrangeWindow(refExtItemId)
    Facade.UIManager:AsyncShowUI(UIName2ID.WarehouseArrangeWindow, nil, nil, refExtItemId)
end

---@param item ItemBase
function InventoryLogic.ShowFirstTimeEquipExtItemConfirmWindow(item)
    local extItemId = item.id
    local extBoxDesc = ItemConfigTool.GetExtentionBoxDescRowById(extItemId)
    if not extBoxDesc then
        return
    end

    local itemSubType = ItemHelperTool.GetSubTypeById(extItemId)
    local subText = InventoryConfig.MapExtType2FirstTimeEquipTip[itemSubType]
    if not subText then
        return
    end

    local tip = string.format(InventoryConfig.Loc.WarehouseFirstEquipExtBoxTip, subText)

    local function fOnConfirm()
        local refExtId = item.InSlot.SlotType
        local refDepositId = Server.InventoryServer:GetDepositIdByExtId(refExtId)

        local depositSortClassTable = Facade.TableManager:GetTable("DepositSortClass")
        local forceExtFirstCls = {}
        for k, row in pairs(depositSortClassTable) do
            table.insert(forceExtFirstCls, tonumber(k))
        end

        Server.InventoryServer:DoSortSlots({ESlotType.MainContainer, refDepositId}, nil, forceExtFirstCls)
    end
    Module.CommonTips:ShowConfirmWindow(
        tip,
        fOnConfirm
    )
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Key Panel



--endregion
-----------------------------------------------------------------------

function InventoryLogic.GetSortClassRowById(sortClassId)
    if type(sortClassId) == "number" then
        sortClassId = tostring(sortClassId)
    end

    local sortClsTable = Facade.TableManager:GetTable("DepositSortClass")
    return sortClsTable[sortClassId]
end

function InventoryLogic.GetFilterFunctionBySortClassId(sortClassId)
    if sortClassId == eDepositSortClass.none then
        ---@param item ItemBase
        local function fFilterFunc(item)
            return true
        end
        return fFilterFunc
    end

    local row = InventoryLogic.GetSortClassRowById(sortClassId)
    if row then
        local types = row.ItemTypeID
        ---@type SlotConfig
        local tmpSlotConfig = SlotConfig:NewIns(ESlotType.None)
        for _, type in ipairs(types) do
            tmpSlotConfig:AddSingle(type, nil, false)
        end

        ---@param item ItemBase
        local function fFilterFunc(item)
            return tmpSlotConfig:CheckItemFit(item.id)
        end

        return fFilterFunc
    end

    return nil
end

function InventoryLogic.StartExtItemReplacement(extItem)
    local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
    if curDepositNum > 1 then
        -- if curDepositNum == 2 then
        --     -- 只有一个可替换，直接进入替换判定
        --     local allExtIds = Server.InventoryServer:GetAllExtIds()
        --     local targetReplaceSlot = Server.InventoryServer:GetSlot(allExtIds[1])
        --     InventoryLogic.StartExtItemReplacementInvalidation(extItem, targetReplaceSlot)
        -- else
        --     -- 多个可替换，进入替换选择
        --     InventoryLogic.OpenExtReplaceSelectionWindow(extItem)
        -- end

        -- 不判断扩容箱个数
        -- 统一进入替换选择界面
        InventoryLogic.OpenExtReplaceSelectionWindow(extItem)
    end
end

---@param extItem ItemBase
---@param targetExtSlot ItemSlot
function InventoryLogic.StartExtItemReplacementInvalidation(extItem, targetExtSlot, bShowTips)
    local replaceItem = targetExtSlot and targetExtSlot:GetEquipItem()

    local canReplace = InventoryLogic.CheckExtItemReplacable(extItem, replaceItem)
    if not canReplace then
        if bShowTips then
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.OnlySameTypeExtItemCanReplace)
        end
        return false
    end

    -- 请求后台查询是否放得下
    ---@param res pb_CSDepositTestReplaceExtensionBoxRes
    local function fOnServerCheckRes(res)
        if res.result == 0 then
            if res.can_be_replaced then
                -- 判断是否小换大
                local srcConfig = ItemConfigTool.GetExtentionBoxDescRowById(extItem.id)
                local targetConfig = ItemConfigTool.GetExtentionBoxDescRowById(replaceItem.id)
                ensure(srcConfig and targetConfig)

                local srcCapacity = srcConfig.GainPosLength * srcConfig.GainPosWidth
                local targetCapacity = targetConfig.GainPosLength * targetConfig.GainPosWidth
                if srcCapacity > targetCapacity then
                    -- 大换小，直接替换
                    ItemOperaTool.DoEquipExtItem(extItem, targetExtSlot, bShowTips)
                else
                    -- 小换大，弹窗询问
                    -- 匹配中时屏蔽替换
                    local bReady, readyState = Server.MatchServer:GetIsReadytoGo()
                    if bReady then
                        if readyState == 1 then
                            Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.MatchingText)
                        elseif readyState == 2 then
                            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InReadying)
                        end
                    else
                        InventoryLogic.OpenExtReplaceConfirmWindow(extItem, replaceItem)
                    end
                end
            else
                if bShowTips then
                    Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseCapacityNotEnoughAfterReplaceExtItem)
                end
            end
        elseif res.result == -1 then
            
        else
            if bShowTips then
                Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseCapacityNotEnoughAfterReplaceExtItem)
            end
        end
    end

    Server.InventoryServer:ReqServerToCheckExtReplacementValid(extItem, targetExtSlot, fOnServerCheckRes)
    return true
end

---@param srcItem ItemBase
---@param targetItem ItemBase
function InventoryLogic.CheckExtItemReplacable(srcItem, targetItem)
    if not srcItem or not targetItem then
        return false
    end

    ---@type EquipmentFeature
    local srcEquipmentFeature = srcItem:GetFeature(EFeatureType.Equipment)
    ---@type EquipmentFeature
    local targetEquipmentFeature = targetItem:GetFeature(EFeatureType.Equipment)
    local bAllAreExtItems = true
    bAllAreExtItems = bAllAreExtItems and srcEquipmentFeature and srcEquipmentFeature:IsExtendItem()
    bAllAreExtItems = bAllAreExtItems and targetEquipmentFeature and targetEquipmentFeature:IsExtendItem()

    if not bAllAreExtItems then
        return false
    end

    if targetItem.InSlot.SlotType == ESlotType.ExtensionVIP then
        return false
    end

    local subType1 = ItemHelperTool.GetSubTypeById(srcItem.id)
    local subType2 = ItemHelperTool.GetSubTypeById(targetItem.id)
    if subType1 == subType2 or subType1 == InventoryConfig.EExtItemType.AllBox then
        return true
    end

    return false
end

function InventoryLogic.OpenExtReplaceSelectionWindow(extItem)
    if IsHD() then
        Module.Inventory.Config.Events.evtEnterReplaceExtPanel:Invoke(true, extItem)
    else
        -- Facade.UIManager:AsyncShowUI(UIName2ID.ExtReplaceSelectionWindow, nil, nil, extItem)
        Module.Inventory.Config.Events.evtEnterReplaceExtPanel:Invoke(true, extItem)
    end
end

function InventoryLogic.OpenExtReplaceConfirmWindow(extItem, targetExtItem)
    Facade.UIManager:AsyncShowUI(UIName2ID.ExtReplaceConfirmWindow, nil, nil, extItem, targetExtItem)
end

------------------------------------------------------------------------------------------------------------------------------------------------
--#region 愿望单相关

function InventoryLogic.GetWishList(sortIndex)
    local allItemMarkProps = Server.InventoryServer:GetAllItemMarkProps()
    local wishList = table.tolist(allItemMarkProps) or {}
    if sortIndex then
        Module.Inventory.Field:SetWishSortIndex(sortIndex)
        InventoryLogic.SortWishList(wishList, sortIndex)
    end
    return wishList
end

function InventoryLogic.SortWishList(wishList, sortIndex)
    if sortIndex == InventoryConfig.EWishSortType.TimeAsc then
        --时间升序
        table.sort(
            wishList,
            function(a, b)
                if a.change_time ~= b.change_time then
                    return a.change_time < b.change_time
                elseif a.mark_num ~= b.mark_num then
                    local aNum = a.mark_num == -1 and math.huge or a.mark_num
                    local bNum = b.mark_num == -1 and math.huge or b.mark_num
                    return aNum > bNum
                else
                    local aQuality = ItemConfigTool.GetItemConfigById(a.prop.id).Quality
                    local bQuality= ItemConfigTool.GetItemConfigById(b.prop.id).Quality
                    if aQuality ~= bQuality then
                        return aQuality > bQuality
                    end
                end
            end)
    elseif sortIndex == InventoryConfig.EWishSortType.TimeDesc then
        --时间降序
        table.sort(
            wishList,
            function(a, b)
                if a.change_time ~= b.change_time then
                    return a.change_time > b.change_time
                elseif a.mark_num ~= b.mark_num then
                    local aNum = a.mark_num == -1 and math.huge or a.mark_num
                    local bNum = b.mark_num == -1 and math.huge or b.mark_num
                    return aNum > bNum
                else
                    local aQuality = ItemConfigTool.GetItemConfigById(a.prop.id).Quality
                    local bQuality= ItemConfigTool.GetItemConfigById(b.prop.id).Quality
                    if aQuality ~= bQuality then
                        return aQuality > bQuality
                    end
                end
            end)
    elseif sortIndex == InventoryConfig.EWishSortType.NumAsc then
        --所需数升序
        table.sort(
            wishList,
            function(a, b)
                if a.mark_num ~= b.mark_num then
                    local aNum = a.mark_num == -1 and math.huge or a.mark_num
                    local bNum = b.mark_num == -1 and math.huge or b.mark_num
                    return aNum < bNum
                elseif a.change_time ~= b.change_time then
                    return a.change_time > b.change_time
                else
                    local aQuality = ItemConfigTool.GetItemConfigById(a.prop.id).Quality
                    local bQuality= ItemConfigTool.GetItemConfigById(b.prop.id).Quality
                    if aQuality ~= bQuality then
                        return aQuality > bQuality
                    end
                end
            end)
    elseif sortIndex == InventoryConfig.EWishSortType.NumDesc then
        --所需数降序
        table.sort(
            wishList,
            function(a, b)
                if a.mark_num ~= b.mark_num then
                    local aNum = a.mark_num == -1 and math.huge or a.mark_num
                    local bNum = b.mark_num == -1 and math.huge or b.mark_num
                    return aNum > bNum
                elseif a.change_time ~= b.change_time then
                    return a.change_time > b.change_time
                else
                    local aQuality = ItemConfigTool.GetItemConfigById(a.prop.id).Quality
                    local bQuality= ItemConfigTool.GetItemConfigById(b.prop.id).Quality
                    if aQuality ~= bQuality then
                        return aQuality > bQuality
                    end
                end
            end)
    end
end

---@class WishNeedItemInfo
---@field public id number 道具id
---@field public needNum number 所需数量

---检查该场景下的所需道具是否可添加到愿望单
---@param param table {EWishSceneType,id,other} -- 用于标记该按钮是否点击过
---@param wishNeedItemInfos WishNeedItemInfo[]  -- 该场景下所需道具列表
---@return EWishBtnStatus
function InventoryLogic.CheckWishNeedItemInfos(param, wishNeedItemInfos)
    local GetItemNumById = function (id)
        -- 如果是预设，需要转为机匣显示数量，因为server发下来的就是机匣
        local itemMainType = ItemHelperTool.GetMainTypeById(id)
        if itemMainType == EItemType.Weapon then
            local recId = WeaponHelperTool.GetRecIdByWeaponPresetId(id)
            return Server.InventoryServer:GetItemNumById(recId)
        else
            return Server.InventoryServer:GetItemNumById(id)
        end
    end

    local bHasBeenClicked = Server.InventoryServer:CheckAddButtonStatus(param)
    local wishBtnStatus = EWishBtnStatus.Hide
    if bHasBeenClicked then
        for _, wishNeedItemInfo in ipairs(wishNeedItemInfos) do
            local id = wishNeedItemInfo.id
            local needNum = wishNeedItemInfo.needNum
            local ownNum = GetItemNumById(id) or 0

            if ownNum >= needNum then
                wishBtnStatus = (wishBtnStatus > EWishBtnStatus.Hide) and wishBtnStatus or EWishBtnStatus.Hide
            else
                local wishPropInfo = Server.InventoryServer:GetItemMarkProp(id)
                if wishPropInfo then
                    local wishNum = wishPropInfo.mark_num == -1 and math.huge or wishPropInfo.mark_num
                    if wishNum >= needNum then
                        wishBtnStatus = (wishBtnStatus > EWishBtnStatus.Unavailable) and wishBtnStatus or EWishBtnStatus.Unavailable
                    else
                        wishBtnStatus = (wishBtnStatus > EWishBtnStatus.Available) and wishBtnStatus or EWishBtnStatus.Available
                    end
                else
                    wishBtnStatus = (wishBtnStatus > EWishBtnStatus.Available) and wishBtnStatus or EWishBtnStatus.Available
                end
            end
        end
    else
        for _, wishNeedItemInfo in ipairs(wishNeedItemInfos) do
            local id = wishNeedItemInfo.id
            local needNum = wishNeedItemInfo.needNum
            local ownNum = GetItemNumById(id) or 0

            if ownNum >= needNum then
                wishBtnStatus = (wishBtnStatus > EWishBtnStatus.Hide) and wishBtnStatus or EWishBtnStatus.Hide
            else
                wishBtnStatus = (wishBtnStatus > EWishBtnStatus.Available) and wishBtnStatus or EWishBtnStatus.Available
            end
        end
    end

    return wishBtnStatus
end

-- 检查是否可装备配件
function InventoryLogic.CheckIsEquippedWeapon(adapterItem)
    local mainWeaponLeft = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.Player):GetEquipItem()
    local mainWeaponRight = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, ESlotGroup.Player):GetEquipItem()
    local pistol = Server.InventoryServer:GetSlot(ESlotType.Pistrol, ESlotGroup.Player):GetEquipItem()
    -- if (mainWeaponLeft and Module.FastEquip:CheckFastEquip(adapterItem, mainWeaponLeft, false)) or (mainWeaponRight and Module.FastEquip:CheckFastEquip(adapterItem, mainWeaponRight, true)) then
    --     return true
    -- else
    --     return false
    -- end
    local left, right, pistolWeapon
    if mainWeaponLeft and Module.FastEquip:CheckFastEquip(adapterItem, mainWeaponLeft, false) then
        left = mainWeaponLeft
    end
    if mainWeaponRight and Module.FastEquip:CheckFastEquip(adapterItem, mainWeaponRight, false) then
        right = mainWeaponRight
    end
    if pistol and Module.FastEquip:CheckFastEquip(adapterItem, pistol, false) then
        pistolWeapon = pistol
    end
    return left or right or pistolWeapon, left, right, pistolWeapon, mainWeaponLeft or mainWeaponRight or pistol
end

-- 显示对应时间
function InventoryLogic.GetRemainTime(endTimestamp, showInWH)
    showInWH = setdefault(showInWH, false)
    local remainTime = endTimestamp - Facade.ClockManager:GetServerTimestamp()
    if remainTime <= 0 then --避免负数
        remainTime = 0
    end
    local hour, min, second = TimeUtil.GetSecondsFormatHHMMSS(remainTime)
    local timeStr
    local day = math.floor(hour / 24)
    if day > 0 then
        timeStr = StringUtil.Key2StrFormat(InventoryConfig.Loc.DayAndHourText, {["day"] = day, ["hour"] = hour % 24})
    elseif hour > 0 then
        timeStr = StringUtil.Key2StrFormat(InventoryConfig.Loc.HourAndMinText, {["hour"] = hour, ["min"] = min})
    elseif min > 0 then
        timeStr = StringUtil.Key2StrFormat(InventoryConfig.Loc.MinAndSecondText, {["min"] = min, ["second"] = second})
    else
        timeStr = StringUtil.Key2StrFormat(Module.CommonWidget.Config.Loc.SecondText, {["sec"] = second})
    end

    return timeStr
end

function InventoryLogic.GetMysticalRatityInfoBySkinId(mysticalPropInfo, bSpecial)
    setdefault(bSpecial, false)
    local mysticalQuality = ItemConfigTool.GetItemQuality(mysticalPropInfo.SkinId)
    local wearTxt = ItemConfigTool.GetMysticalWearConfig(mysticalPropInfo.Wear) or ""
    if mysticalQuality == ItemConfig.EWeaponSkinQualityType.Orange then
        -- 获取稀有度文本
        local bUseSpecial = bSpecial and not IsBuildRegionCN()
        local txt = bUseSpecial and Module.Inventory.Config.Loc.MysticalPendantTxt1 or Module.Inventory.Config.Loc.MysticalSkinWearAndRarity
        local ratityID = ItemConfigTool.GetItemRatityID(mysticalPropInfo.MysticalId)
        local ratityTxt = ratityID and Module.Inventory.Config.MysticalSkinRarityTxtMapping[ratityID] or "未找到稀有度id"
        local ratityAndWearTxt = string.format(txt, ratityTxt, wearTxt)
        return ratityAndWearTxt
    else
        local mysticalWearTxt = string.format(Module.Inventory.Config.Loc.MysticalSkinWearTxt, wearTxt)
        return mysticalWearTxt
    end
end

function InventoryLogic.GetMysticalPendantInfoByDetailInfos(detailInfos, itemQuality)
	local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
	local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
	local kill_cnter_text = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText

    for index, mysticalPendantShowInfo in ipairs(detailInfos) do
        if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypeMaterial then
            if Module.ItemDetail.Config.EPendantColor[mysticalPendantShowInfo.Level] then
                rateText = Module.ItemDetail.Config.EPendantColor[mysticalPendantShowInfo.Level]
            end
        end
        if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypeEffect then
            if Module.ItemDetail.Config.EPendantEffects[mysticalPendantShowInfo.Level] then
                wearText = Module.ItemDetail.Config.EPendantEffects[mysticalPendantShowInfo.Level]
            end
        end
        if mysticalPendantShowInfo.ShowType == Module.ItemDetail.Config.MysticalPendantShowType.M_P_ShowTypePattern then
            if Module.ItemDetail.Config.EPendantPlaceholderOne[mysticalPendantShowInfo.Level] then
                kill_cnter_text = Module.ItemDetail.Config.EPendantPlaceholderOne[mysticalPendantShowInfo.Level]
            end
        end
    end
    local detailInfoStr = ""
    if itemQuality <= ItemConfig.EWeaponSkinQualityType.Blue then
        detailInfoStr = string.format(Module.Inventory.Config.Loc.MysticalPendantTxt2, rateText)
    elseif itemQuality <= ItemConfig.EWeaponSkinQualityType.Purple then
        detailInfoStr = string.format(Module.Inventory.Config.Loc.MysticalPendantTxt1, rateText, wearText)
    else
        detailInfoStr = string.format(Module.Inventory.Config.Loc.MysticalPendantTxt, rateText, wearText, kill_cnter_text)
    end
    return detailInfoStr
end

function InventoryLogic.CheckCanMoveInMatching(slotType)
    local bReady, readyState = Server.MatchServer:GetIsReadytoGo()
    if bReady then
        if readyState == 1 then
            return not ItemMoveCmd.MatchForbidSlots[slotType]
        end
    end
    return true
end


function InventoryLogic.CheckSafeBoxExpiredStatus(groupId)
    return InventoryLogic._CommonCheckExpiredStatus(groupId, ESlotType.SafeBox)
end

function InventoryLogic.CheckKeyChainExpiredStatus(groupId)
    return InventoryLogic._CommonCheckExpiredStatus(groupId, ESlotType.KeyChain)
end

function InventoryLogic._CommonCheckExpiredStatus(groupId, slotType)
    groupId = setdefault(groupId, ESlotGroup.Player)
    local slot = Server.InventoryServer:GetSlot(slotType, groupId)
    local equipItem = slot:GetEquipItem()
    if equipItem then
        local equipmentFeature = equipItem:GetFeature(EFeatureType.Equipment)
        if equipmentFeature and not equipmentFeature:PermissionCanUse() then
            logerror(string.format("InventoryLogic._CommonCheckExpiredStatus %s--%s过期了", groupId, Module.Inventory.Config.SlotNameMapping[slotType]))
            return true
        end
    end
    return false
end

function InventoryLogic.CheckDragItemType(healthItemId, equipmentItem)
    local dragItemInfo = ItemHelperTool.GetHealthInfoById(healthItemId)
    local equipmentFeature = equipmentItem:GetFeature(EFeatureType.Equipment)
    -- 装备和维修套件是否匹配
    if equipmentFeature and dragItemInfo then
        if equipmentFeature:IsHelmet() and dragItemInfo.ArmorInfo.bCanUseOnHelmet then
            return true
        elseif equipmentFeature:IsBreastPlate() and dragItemInfo.ArmorInfo.bCanUseOnArmor then
            return true
        end
    end
    return false
end

function InventoryLogic.CheckItemDurability(equipmentItem)
    local equipmentFeature = equipmentItem:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate()) then
        if equipmentFeature:GetDurabilityMaxValue() ~= equipmentFeature:GetDurabilityCurValue() then
            return true
        end
    end
    return false
end

--- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
function InventoryLogic.GetIconList()
    if DFCONSOLE_LUA == 1 then
        return Module.ItemDetail.Config.consoleIconList
    else
        return Module.ItemDetail.Config.pcRightIconList
    end
end

function InventoryLogic.GetButtonType()
    if DFCONSOLE_LUA == 1 then
        return Module.ItemDetail.Config.ButtonTypeConsole
    else
        return Module.ItemDetail.Config.ButtonType
    end
end

function InventoryLogic.GetNearestNavTarget(Direction)
    -- 方向错误
    if Direction <0 or Direction > 3 then
        return nil
    end
    logwarning("yanmingjing:InventoryLogic.GetNearestNavTarget()")
    local itemView,overrideX,overrideY,overrideSlot = Module.Inventory.Field:GetNavStartItemview()
    local GID = nil
    if itemView and itemView.item and itemView.item.InSlot then
        local item = itemView.item
        local slot = item.InSlot
        if slot then
            local loc = slot:GetItemLocation(item)
            local x, y, index = loc.X, loc.Y, loc.SubIndex
            GID = slot:GetNearestItemPosInDirection(x,y,index,Direction,false)
        else
            logerror("yanmingjing:No valid slot");
            return nil
        end
        if GID then
            local targetItem = Server.InventoryServer:GetItemByGid(GID)
            if targetItem then
                local slotView = itemView:GetViewParent(true)
                if slotView then
                    local targetItemView = slotView:GetNavigationItemView_cppinst(targetItem)
                    -- slotview:ScrollToItem(targetItem)
                    -- Module.Inventory.Field:SetNavTargetItem(targetItem)
                    return targetItemView
                end
            end
        else
            logerror("yanmingjing:No valid GID,startitemview", itemView)
            return nil
        end
    else
        if overrideX and overrideY and overrideSlot then
            logerror("yanmingjing:No valid itemview,use override x and y,but logic not implemented now")
            GID = overrideSlot:GetNearestItemPosInDirection(overrideX,overrideY,0,Direction,true)
        end
        if GID then
            local targetItem = Server.InventoryServer:GetItemByGid(GID)
            if targetItem then
				local slotView = InventoryNavManager.allInvSlotView[ESlotType.MainContainer]
                if slotView then
                    local targetItemView = slotView:GetNavigationItemView_cppinst(targetItem)
                    slotView:ScrollToItem(targetItem)
                    Module.Inventory.Field:SetNavTargetItem(targetItem)
                    return targetItemView
                end
            end
        else
            logerror("yanmingjing:No valid GID,uoverride x =“,override y =“,overrideSlot =“", overrideX, overrideY, overrideSlot)
            return nil
        end
    end
   
end

local nextItemNavMapping = {
    [ESlotType.ChestHangingContainer] = ESlotType.Pocket,
    [ESlotType.Pocket] = ESlotType.BagContainer,
    [ESlotType.BagContainer] = ESlotType.SafeBoxContainer
}

local invertNextItemNavMapping = {
    [ESlotType.SafeBoxContainer] = ESlotType.BagContainer,
    [ESlotType.BagContainer] = ESlotType.Pocket,
    [ESlotType.Pocket] = ESlotType.ChestHangingContainer
}



function InventoryLogic.GetNavNextTarget(item, bInvert)
    bInvert = setdefault(bInvert, false)

    if not (item and item.InSlot) then
        logerror(string.format("InventoryLogic.GetNavNextTarget item: %s or inSlot: %s is nil !!!!!", item , item and item.InSlot))
        return
    end
    local itemSlot = item.InSlot

    local nextItemGID = itemSlot:GetNavNextTarget(item)
    if nextItemGID then
        return Server.InventoryServer:GetItemByGid(nextItemGID,itemSlot:GetSlotGroup())
    end

    local nextCheckSlotType = nextItemNavMapping[itemSlot.SlotType]
    local targetItem = nil

    while(nextCheckSlotType and targetItem == nil)
    do
        local nextCheckSlot = Server.InventoryServer:GetSlot(nextCheckSlotType,itemSlot:GetSlotGroup())
        nextItemGID = nextCheckSlot:GetFirstItem()
        targetItem = Server.InventoryServer:GetItemByGid(nextItemGID,itemSlot:GetSlotGroup())
        if(not targetItem) then
            nextCheckSlotType = nextItemNavMapping[nextCheckSlotType]
        end
    end
    if targetItem then
        return targetItem
    end

    -- 反向查找
    nextItemGID = itemSlot:GetNavNextTarget(item,true)
    if nextItemGID then
        return Server.InventoryServer:GetItemByGid(nextItemGID,itemSlot:GetSlotGroup())
    end
    nextCheckSlotType = invertNextItemNavMapping[itemSlot.SlotType]
    while(nextCheckSlotType and targetItem == nil)
    do
        local nextCheckSlot = Server.InventoryServer:GetSlot(nextCheckSlotType,itemSlot:GetSlotGroup())
        nextItemGID = nextCheckSlot:GetLastItem()
        targetItem = Server.InventoryServer:GetItemByGid(nextItemGID,itemSlot:GetSlotGroup())
        if(not targetItem) then
            nextCheckSlotType = invertNextItemNavMapping[nextCheckSlotType]
        end
    end



    return targetItem
end

--- END MODIFICATION

--#endregion
------------------------------------------------------------------------------------------------------------------------------------------------

return InventoryLogic
