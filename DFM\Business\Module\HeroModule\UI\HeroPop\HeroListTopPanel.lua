----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHero)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local HeroListTopPanel = ui("HeroListTopPanel")
local UDFMGameHudDelegates = import("DFMGameHudDelegates")
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local HeroConfig=require "DFM.Business.Module.HeroModule.HeroConfig"
local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
local SkillHelperTool = require "DFM.StandaloneLua.BusinessTool.SkillHelperTool"
local EConsumeMouseWheel = import "EConsumeMouseWheel"
local BattleHeroSkillIcon = require"DFM.Business.Module.HeroModule.UI.HeroMain.BattleHeroSkillIcon"
local HeroPropItem = require"DFM.Business.Module.HeroModule.UI.HeroMain.HeroPropItem"

local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local EUINavigation = import "EUINavigation"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"

---@enum EHeroListTopPanelMode 界面复用多用途区分
local EHeroListTopPanelMode  = {
	Normal = 1,
	Recruitment = 2, ---招募专属
	Badge = 3, 		 ---徽章专属
}

function HeroListTopPanel:Ctor()
    -- HeroItem行数
	self._CELL_ROW_COUNT = 1
	    self._PLAY_SHOW_ANIM_DELTA_TIME = 0.05
	self._animStartDelayTime = 0.05
    self._animDeltaTime = 0.03
    self._showAnimFinishList = {}

	self._wtProcessBtn = self:Wnd("WBP_DFCommonButtonV1S1", DFCommonButtonOnly)
    self._wtProcessBtn:Event("OnClicked", self._OnProcessBtnClicked, self)
    self._wtProcessBtn:Event("OnDeClicked", self._OnProcessBtnDeClicked, self)

	self._wtCanvasHeroList = self:Wnd("DFCanvasPanel_HeroList", UIWidgetBase)
	self.heroIdList = {}
	self._mapIdx2ItemWidget = setmetatable({}, weakmeta_value)
    -- self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallBox_Hero", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtScrollGridList = UIUtil.WndScrollGridBox(self, "DFScrollGridBox_Hero", self._OnGetItemCount, self._OnProcessItemWidget)
	self._wtScrollGridList:Event('OnProcessItemsUpdateFinished', self._OnProcessItemsUpdateFinished, self)

	
    self._wtTbHeroName = self:Wnd("DFTextBlock_hName", UITextBlock)
    self._wtTbArmTitle = self:Wnd("DFTextBlock_armTitle", UITextBlock)
    self._wtImgArmIcon = self:Wnd("DFMImage_armIcon", UIImage)

	--- 技能相关
	self._wtSkillIcon1 = self:Wnd("HeroSkill1", BattleHeroSkillIcon)
	self._wtSkillIcon2 = self:Wnd("HeroSkill2", BattleHeroSkillIcon)
	self._wtHeroPropItem1 = self:Wnd("WBP_Hero_PropsItem1", HeroPropItem)
	self._wtHeroPropItem2 = self:Wnd("WBP_Hero_PropsItem2", HeroPropItem)
	self._cachedHeroId = nil

	self._wtDFImage_132 = self:Wnd("DFImage_132", UIImage)
	self._wtDFBorder = self:Wnd("DFBorder_0", UIImage)
	self._wtBadgeChangeBox = self:Wnd("DFHorizontalBox_130", UIWidgetBase)

	self:AddLuaEvent(Server.HeroServer.Events.evtMPUsedHeroIdChanged, self._OnSelectedHeroIdChange, self)
	self:AddLuaEvent(Server.HeroServer.Events.evtSOLUsedHeroIdChanged, self._OnSelectedHeroIdChange, self)
	self:AddLuaEvent(Server.HeroServer.Events.evtHeroUnlockNtf, self._OnHeroUnlockNtf, self)
	self:AddLuaEvent(Module.Hero.Config.Events.evtSelectHeroIdForShowChanged, self._OnShowHeroSelectedChanged, self)
	if IsHD() then
		self:AddLuaEvent(Facade.UIManager.Events.evtStackUIChanged, self._OnStackUIChanged, self)
	end

	-- 使得此UI的进入动画播完之后才能进行隐藏
	self.bInEnd = false

	if DFHD_LUA == 1 then
        --PC端统一禁用掉用户的滚轮滑动
        -- self._wtWaterFallList:SetConsumeMouseWheel(EConsumeMouseWheel.Never)
		-- self._wtScrollGridList :SetConsumeMouseWheel(EConsumeMouseWheel.Never)
    else
        -- self._wtWaterFallList:SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)
		-- self._wtScrollGridList :SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)
    end
end

---@param HeroListTopPanelMode EHeroListTopPanelMode 
function HeroListTopPanel:OnInitExtraData(fOnHeroPanelHideBegin, needFlitArmId , HeroListTopPanelMode)
	self._fOnHeroPanelHideBegin = fOnHeroPanelHideBegin
	self._needFlitArmId = needFlitArmId or 0
	self._curHeroListTopPanelMode = HeroListTopPanelMode and HeroListTopPanelMode or 1

	self:SetHeroDetailBox()
end

function HeroListTopPanel:OnOpen()
    self:_FreshHeroList()
	UDFMGameHudDelegates.Get(GetGameInstance()).OnHandlePostProcessMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)	
end

function HeroListTopPanel:SetHeroDetailBox()
    if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
		self._wtDFBorder:Collapsed()
		self._wtBadgeChangeBox:Visible()
	else
		self._wtDFBorder:SetVisibility(ESlateVisibility.HitTestInvisible)
		self._wtBadgeChangeBox:Collapsed()
	end
end

function HeroListTopPanel:_RegisterHeroPanelShortCuts()
	Module.CommonBar:SetBottomBarTempInputSummaryList({
		{actionName = "Back", func = self._OnCloseHeroListPanel, caller = self ,bUIOnly = false, bHideIcon = false},
		{actionName = self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment or self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge
			 and "SelectHero1" or "SelectHero",func = self._OnSelectHero, caller = self ,bUIOnly = true, bHideIcon = false},
	}, true, true)

	self._TopBar_Prev02 = self:AddInputActionBinding("Prev02", EInputEvent.IE_Pressed, self._OnDoNothing, self, EDisplayInputActionPriority.UI_Pop)
    self._TopBar_Next02 = self:AddInputActionBinding("Next02", EInputEvent.IE_Pressed, self._OnDoNothing, self, EDisplayInputActionPriority.UI_Pop)
end

function HeroListTopPanel:_OnDoNothing()
end


function HeroListTopPanel:_OnCloseHeroListPanel() 
	Facade.UIManager:CloseUI(self)
end

function HeroListTopPanel:_OnStackUIChanged()
	Facade.UIManager:CloseUI(self)
end

function HeroListTopPanel:_OnSelectHero()
	if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then

		local curShowHeroId = Module.Hero:GetCurShowHeroId() --当前列表中悬停选择的干员
		local curShowRecruitHeroId = Module.Hero:GetCurShowRecruitmentHeroId()
		if tonumber(curShowHeroId)  ~= curShowRecruitHeroId then
			Module.Hero:SetCurShowRecruitmentHeroId(curShowHeroId)
		else
			Facade.UIManager:CloseUI(self)
		end

	elseif self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
		local curShowHeroId = Module.Hero:GetCurShowHeroId() 	--当前列表中悬停选择的干员
		local curShowRecruitHeroId = Module.RoleInfo:GetSocialHeroId()
		if tonumber(curShowHeroId) ~= curShowRecruitHeroId then
			Module.RoleInfo:SetSocialHeroId(curShowHeroId)
		end
		Facade.UIManager:CloseUI(self)
	else
		local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
		if armedForceMode == EArmedForceMode.SOL then
			--- TODO 暂无经分
		else
			LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPUseHeroAsBattle)
		end
		local curShowHeroId = Module.Hero:GetCurShowHeroId()
		local curHeroId = Server.HeroServer:GetCurUsedHeroId()
		if curShowHeroId ~= curHeroId then
			Module.Hero:UseHeroById(curShowHeroId)
		else
			Facade.UIManager:CloseUI(self)
		end
	end
end

function HeroListTopPanel:_UnregisterHeroPanelShortCuts()
	Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function HeroListTopPanel:OnShowBegin()
	local curShowHeroId = Module.Hero:GetCurShowHeroId()
    self:_FreshHeroInfoByHID(curShowHeroId)
	self._showAnimFinishList = {}
	self:_RegisterHeroPanelShortCuts()
    self:BindBackAction(self._OnBackBtnClicked, self)

	Module.Hero.Config.Events.evtHeroListTopPanelOnShowBegin:Invoke()

	-- BEGIN MODIFICATION @ VIRTUOS : 添加导航组
	if IsHD() then
    	self:_EnableNavigation(true)
	end
    -- END MODIFICATION
end

function HeroListTopPanel:_OnBackBtnClicked()
	if self.bInEnd then
		Facade.UIManager:CloseUI(self)
		self.bInEnd = false
	end
end

function HeroListTopPanel:OnHideBegin()
	self._cachedHeroId = nil
	self._showAnimFinishList = {}
	self:_UnregisterHeroPanelShortCuts()
	if self._fOnHeroPanelHideBegin then
		self._fOnHeroPanelHideBegin()
	end
	self:UnBindBackAction()

	-- BEGIN MODIFICATION @ VIRTUOS : 
	if IsHD() then
		self:_EnableNavigation(false)
	end
    -- END MODIFICATION
end

function HeroListTopPanel:OnClose()
	-- local curShowHeroId = Module.Hero:GetCurShowHeroId()
	-- local curHeroId = Server.HeroServer:GetCurUsedHeroId()
	-- if curHeroId ~= curShowHeroId then
	-- 	Module.Hero:ShowHeroById(curHeroId)
	-- end
	UDFMGameHudDelegates.Get(GetGameInstance()).OnHandlePostProcessMouseButtonUpEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
	self:RemoveAllLuaEvent()
	self._showAnimFinishList = {}
	-- self._wtWaterFallList:ScrollToStart(false, false, 0)
	-- self._wtWaterFallList:RefreshVisibleItems()

	self._wtScrollGridList:ScrollToStart(false, false, 0)
	self._wtScrollGridList:RefreshVisibleItems()
end


function HeroListTopPanel:OnAnimFinished(anim)
	if anim ==  self.WBP_Hero_ListPop_in then
		self.bInEnd = true
	end
end

function HeroListTopPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
	local sceenPos = mouseEvent:GetScreenSpacePosition()
	local geometry = self._wtDFImage_132:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
    if not isUnder then
		if self.bInEnd then
			Facade.UIManager:CloseUI(self)
			self.bInEnd = false
		end
    end
end

function HeroListTopPanel:_OnSelectedHeroIdChange()
	Facade.UIManager:CloseUI(self)
end

function HeroListTopPanel:_OnHeroUnlockNtf(heroIds)
	self:_FreshHeroList()
end

function HeroListTopPanel:_FreshHeroList()
	self:_BuildHeroIdList()
	local curShowHeroId = Module.Hero:GetCurShowHeroId()
	local curShowIdx = 1
	for idx, v in ipairs(self.heroIdList) do
		if tostring(v) == tostring(curShowHeroId) then
			curShowIdx = idx
		end
	end
	-- self._wtWaterFallList:RefreshAllItems()
	-- self._wtWaterFallList:ScrollToIndex(curShowIdx)

	self._wtScrollGridList:RefreshAllItems()
	self._wtScrollGridList:ScrollToItem(curShowIdx - 1, true, true, 15, 0, false)
end

function HeroListTopPanel:_OnGetItemCount()
	return #self.heroIdList or 0
end

function HeroListTopPanel:_OnShowHeroSelectedChanged(curShowHeroId)
	local curShowIdx = 1
	for idx, v in ipairs(self.heroIdList) do
		if tostring(v) == tostring(curShowHeroId) then
			curShowIdx = idx
			break
		end
	end

	local bNeedArmUpdate = false
	local heroInfo = HeroDataTable[tostring(curShowHeroId)]
	
	if self._needFlitArmId ~= heroInfo.ArmedForceID then
		if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Normal then
			self._needFlitArmId = heroInfo.ArmedForceID
			bNeedArmUpdate = true
		end
	end

	if not bNeedArmUpdate then
		local itemWidget = self._mapIdx2ItemWidget[curShowIdx]
		local preItemWidget = self._mapIdx2ItemWidget[self._curSelectedIdx]
		if itemWidget then
			itemWidget:OnSelect()
		end
		if preItemWidget then
			preItemWidget:OnUnSelect()
		end
		self._curSelectedIdx = curShowIdx
	
		local curShowHeroId = Module.Hero:GetCurShowHeroId()
		self:_FreshHeroInfoByHID(curShowHeroId)
	else
		self:_FreshHeroList()
	end
end

function HeroListTopPanel:_OnProcessItemWidget(position, itemWidget)
	local dataIdx = position + 1
	local heroId = self.heroIdList and self.heroIdList[dataIdx] or nil
	if not heroId then
		return
	end
	-- if DFHD_LUA == 1 then
	-- 	itemWidget:SetType(0)
	-- else
	-- 	itemWidget:SetType(1)
	-- end

	if Server.HeroServer:IsCanUseHero(heroId) then
		itemWidget:ToggleLockMask(false)
	else
		itemWidget:ToggleLockMask(true)
	end

    local curHeroId = Server.HeroServer:GetCurUsedHeroId()
	local curShowHeroId = Module.Hero:GetCurShowHeroId()

	if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
		curShowHeroId = Module.RoleInfo:GetSocialHeroId()
		curHeroId = curShowHeroId
	end

	if tostring(heroId) == tostring(curShowHeroId) then
		itemWidget:OnSelect()
		self._curSelectedIdx = dataIdx
	else
		itemWidget:OnUnSelect()
	end

	if tostring(heroId) == tostring(curHeroId) then
		itemWidget:OnOperated()
	else
		itemWidget:OnUnOperated()
	end

	local function onClickCallback(uins)
		if DFHD_LUA == 1 then
			if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment or self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
				self:_OnSelectHero()
			else
				local curHeroId = Server.HeroServer:GetCurUsedHeroId()
				if tostring(curHeroId) == tostring(heroId) then
					return
				end
				local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
				if armedForceMode == EArmedForceMode.SOL then
					--- TODO 暂无经分
				else
					LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPUseHeroAsBattle)
				end
				Module.Hero:UseHeroById(heroId)
			end
		else
			local curShowHeroId = Module.Hero:GetCurShowHeroId()
			if tostring(curShowHeroId) == tostring(heroId) then
				return
			end
			Module.Hero:ShowHeroById(heroId)
		end
	end
	local function onEnterCallback(uins)
		if DFHD_LUA == 1 then
			if Server.HeroServer:IsCanUseHero(heroId) then
				uins:ToggleLockMask(false)
			else
				uins:ToggleLockMask(true)
			end
			local curShowHeroId = Module.Hero:GetCurShowHeroId()
			if tostring(curShowHeroId) == tostring(heroId) then
				return
			end
			Module.Hero:ShowHeroById(heroId)
		end
	end
	local function onLeaveCallback(uins)
	end
	local function onRightButtonCallback()
		-- pc RMB fashion 
		-- self:_OnFashionBtnClick()
	end
	local function onHoverCallback(uins)
		-- pc HOVER select hero
		-- self:SetSelectedHeroCell(uins)
		-- self.curSeletcHeroId = uins.heroId
	end

	if itemWidget.InitData then
		local isShowReddot = (self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge) or (self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment)
		itemWidget:InitData(
			{
				index = position,
				heroId = heroId,
				fCallback = onClickCallback, 
				fHoverCallback = onHoverCallback,
				fEnterCallback = onEnterCallback,
				fLeaveCallback = onLeaveCallback, 
				fRightButtonCallback = onRightButtonCallback,
				bShowRecruitmentInfo = self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment
			}, isShowReddot
		)
	end
	self._mapIdx2ItemWidget[dataIdx] = itemWidget
    -- 动画相关
    -- local rowIdx = position - 1 / self._CELL_ROW_COUNT
	-- local visibleItemCount = self._wtWaterFallList:GetVisibleItemCount()
    -- local bVisible = visibleItemCount >= position
    -- if bVisible then
    --     if not self._showAnimFinishList[position - 1] then
	-- 		itemWidget:SetVisibility(ESlateVisibility.Collapsed)
    --         Timer.DelayCall(rowIdx * self._PLAY_SHOW_ANIM_DELTA_TIME, function ()
	-- 			itemWidget:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	-- 			itemWidget:PlayShowAnim(rowIdx * self._animDeltaTime + self._animStartDelayTime)
    --         end, itemWidget)
    --         self._showAnimFinishList[position - 1] = true
    --     end
    -- end
end

function HeroListTopPanel:_OnProcessItemsUpdateFinished()
	if IsHD() then
		if self._curSelectedIdx and self._curSelectedIdx > 0 then
			local pos = self._curSelectedIdx - 1
			local itemWidget = self._wtScrollGridList:GetItemByIndex(pos)
			WidgetUtil.SetUserFocusToWidget(itemWidget, true)
		end
	end
end

function HeroListTopPanel:_OnProcessBtnClicked()
	local curShowHeroId = Module.Hero:GetCurShowHeroId() 	--当前列表中悬停选择的干员

	if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then
		local curShowRecruitHeroId = Module.Hero:GetCurShowRecruitmentHeroId()
		Module.Hero:SetCurShowRecruitmentHeroId(curShowHeroId)
		Facade.UIManager:CloseUI(self)
		-- if tonumber(curShowHeroId)  ~= curShowRecruitHeroId then
		-- else
		-- end
	elseif self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
		local curShowRecruitHeroId = Module.RoleInfo:GetSocialHeroId()
		if tonumber(curShowHeroId) ~= curShowRecruitHeroId then
			Module.RoleInfo:SetSocialHeroId(curShowHeroId)
		end
		Facade.UIManager:CloseUI(self)
	else
		local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
		if armedForceMode == EArmedForceMode.SOL then
			--- TODO 暂无经分
		else
			LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPUseHeroAsBattle)
		end
		local curHeroId = Server.HeroServer:GetCurUsedHeroId()
		if curShowHeroId ~= curHeroId then
			Module.Hero:UseHeroById(curShowHeroId)
		else
			Facade.UIManager:CloseUI(self)
		end
	end
	
end

function HeroListTopPanel:_OnProcessBtnDeClicked()
	-- local curShowHeroId = Module.Hero:GetCurShowHeroId()
	-- local curHeroId = Server.HeroServer:GetCurUsedHeroId()
	-- if curShowHeroId ~= curHeroId then
	-- 	Module.Hero:UseHeroById(curShowHeroId)
	-- else
	-- 	Facade.UIManager:CloseUI(self)
	-- end
end

function HeroListTopPanel:_FreshHeroInfoByHID(heroId)
    if self._cachedHeroId ~= heroId then
        local heroData = HeroDataTable[tostring(heroId)]
        local cacheHeroData = self._cachedHeroId ~= nil and HeroDataTable[tostring(self._cachedHeroId)] or nil
        if heroData then
            self._wtTbHeroName:SetText(heroData.Name)
            self._wtTbHeroName:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C001"))
            self._wtTbArmTitle:SetText(heroData.Title)
            self._wtImgArmIcon:AsyncSetImagePath(heroData.ExpertIcon)
            -- self._wtImgHeroIcon:AsyncSetImagePath(heroData.Icon)

            local heroConfig = Module.Hero.Config
            local armedImgColor = heroConfig.ExpertIconColorMapping[heroData.ArmedForceID]
            local armedTextColor = heroConfig.ExpertNameColorMapping[heroData.ArmedForceID]
            self._wtImgArmIcon:SetColorAndOpacity(armedImgColor)
            self._wtTbArmTitle:SetColorAndOpacity(armedTextColor)
        else
            local unkownHeroStr = string.format(Module.ArmedForce.Config.Loc.HeroDataFailed, heroId)
            Module.CommonTips:ShowSimpleTip(unkownHeroStr)
        end

        self:_RefreshSkillAndArmPropInfo(heroId)
    else
    end
    self:_FreshHeroProcessBtn()
	if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then
		Module.Hero:SetCurShowRecruitmentHeroId(heroId)
	end

    self._cachedHeroId = heroId
end


function HeroListTopPanel:_RefreshSkillAndArmPropInfo(heroId)
	local rawData = Server.HeroServer:GetCurModeExpertDataByHeroId(heroId)
	if rawData then
        if SkillHelperTool.CheckIsSkillDataValid(heroId) then
            local fSkillIconClickCallback = function (SkillId,Idx)
            end
            local PassiveSkillId = SkillHelperTool.GetMPPassiveSkillID(heroId)
            self._wtSkillIcon1:SetExtraData(PassiveSkillId, function ()
                fSkillIconClickCallback(PassiveSkillId, 1)
            end)
            self._wtSkillIcon1:SetSkillText(SkillHelperTool.GetSkillNameById(PassiveSkillId),SkillHelperTool.GetSkillBuffNameById(PassiveSkillId))

            local armId = HeroHelperTool.GetHeroCategoryId(heroId)
            self._wtSkillIcon2:SetIconColor(Module.Hero.Config.ExpertIconColorMapping[armId])
            self._wtSkillIcon2:SetTextColor(Module.Hero.Config.ExpertNameColorMapping[armId])

            local UltimateSkillId = SkillHelperTool.GetMPUltimateSkillID(heroId)
            self._wtSkillIcon2:SetExtraData(UltimateSkillId, function ()
                fSkillIconClickCallback(UltimateSkillId, 2)
            end)
            self._wtSkillIcon2:SetSkillText(SkillHelperTool.GetSkillNameById(UltimateSkillId),SkillHelperTool.GetSkillBuffNameById(UltimateSkillId))


            local fHeroPropIconClickCallback = function (SkillId,Idx)
            end
            local SkillId = SkillHelperTool.GetMPSupportSkillID(heroId)
            self._wtHeroPropItem1:SetExtraData(SkillId, function ()
                fHeroPropIconClickCallback(SkillId,3)
            end )
            self._wtHeroPropItem1:SetSkillText(SkillHelperTool.GetSkillNameById(SkillId),SkillHelperTool.GetSkillBuffNameById(SkillId))

            local SkillId2 = SkillHelperTool.GetMPActiveSkillID(heroId)
            self._wtHeroPropItem2:SetExtraData(SkillId2, function ()
                fHeroPropIconClickCallback(SkillId2,4)
            end )
            self._wtHeroPropItem2:SetSkillText(SkillHelperTool.GetSkillNameById(SkillId2),SkillHelperTool.GetSkillBuffNameById(SkillId2))
        else
            logerror('HeroListTopPanel:_RefreshSkillAndArmPropInfo(heroId) - invalid heroId')
        end
	end
end
---------------------------------------------------------------------------------
--- ProcessBtn刷新 - 跟随干员
---------------------------------------------------------------------------------
--- 是不是当前干员刷新
function HeroListTopPanel:_FreshHeroProcessBtn()
	local curShowHeroId = Module.Hero:GetCurShowHeroId() 	--当前列表中悬停选择的干员

	if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then
		self._wtProcessBtn:SetIsEnabledStyle(true)
		self._wtProcessBtn:SetMainTitle(HeroConfig.Loc.SelectHero)

	elseif self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
		self._wtProcessBtn:SetIsEnabledStyle(true)
		local badgeHeroId = Module.RoleInfo:GetSocialHeroId()
		self._wtProcessBtn:SetMainTitle(HeroConfig.Loc.SelectHero)

		if badgeHeroId ~= curShowHeroId then
			self._wtProcessBtn:SetIsEnabledStyle(true)
		else
			self._wtProcessBtn:SetIsEnabledStyle(false)
		end
	else
		local heroId = Server.HeroServer:GetCurUsedHeroId()
		if heroId ~= curShowHeroId then
			if Server.HeroServer:IsCanUseHero(curShowHeroId) then
				self._wtProcessBtn:SetIsEnabledStyle(true)
				self._wtProcessBtn:SetMainTitle(Module.ArmedForce.Config.Loc.ToSetDefaultPageTitle)
				-- self._wtUnlockMask:Collapsed()
				-- self._wtTbUnlockDes:SetText("")
			else
				self._wtProcessBtn:SetIsEnabledStyle(false)
				self._wtProcessBtn:SetMainTitle(Module.ArmedForce.Config.Loc.NotUnlockHeroTitle)
				-- self._wtUnlockMask:SelfHitTestInvisible()
				-- self._wtTbUnlockDes:SetText(Module.ArmedForce.Config.Loc.NotUnlockHeroTitle)
			end
		else
			self._wtProcessBtn:SetIsEnabledStyle(false)
			self._wtProcessBtn:SetMainTitle(Module.ArmedForce.Config.Loc.CurrentDefaultPageTitle)
			-- self._wtUnlockMask:Collapsed()
			-- self._wtTbUnlockDes:SetText("")
		end
	end
    
end

function HeroListTopPanel:_BuildHeroIdList()
	self.heroIdList = {}
	if self._curHeroListTopPanelMode == EHeroListTopPanelMode.Normal then
		for _, v in pairs(Server.HeroServer:GetHeroData()) do
			if self._needFlitArmId == 0 then
				table.insert(self.heroIdList, v.hero_id)
			else
				local vHeroData = HeroDataTable[tostring(v.hero_id)]
				if self._needFlitArmId == vHeroData.ArmedForceID then
					table.insert(self.heroIdList, v.hero_id)
				end
			end
		end

	elseif self._curHeroListTopPanelMode == EHeroListTopPanelMode.Badge then
		for _, v in pairs(Server.HeroServer:GetHeroData()) do
			if  v.is_unlock then
				table.insert(self.heroIdList, v.hero_id)
			end
		end
	elseif self._curHeroListTopPanelMode == EHeroListTopPanelMode.Recruitment then
		for _, v in pairs(Server.HeroServer:GetHeroData()) do
			if self._needFlitArmId == 0 then
				if HeroHelperTool.IsRecruitableHero(v.hero_id) and not v.is_unlock then
					table.insert(self.heroIdList, v.hero_id)
				end
			else
				local vHeroData = HeroDataTable[tostring(v.hero_id)]
				if self._needFlitArmId == vHeroData.ArmedForceID and HeroHelperTool.IsRecruitableHero(v.hero_id) and not not v.is_unlock then
					table.insert(self.heroIdList, v.hero_id)
				end
			end

		end
	end

	table.sort(self.heroIdList, function(a, b)
		local heroInfoA = HeroDataTable[tostring(a)]
		local heroInfoB = HeroDataTable[tostring(b)]
		if heroInfoA.ArmedForceID == heroInfoB.ArmedForceID then
			return heroInfoA.ExpertID < heroInfoB.ExpertID
		else
			return heroInfoA.ArmedForceID < heroInfoB.ArmedForceID
		end
	end)
end

-- BEGIN MODIFICATION @ VIRTUOS : 添加导航组
function HeroListTopPanel:_EnableNavigation(bEnable)
	if not IsHD() then
		return 
	end
	
	if bEnable then
		self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollGridList, self, "Hittest")
		self._wtNavGroup:AddNavWidgetToArray(self._wtScrollGridList)
		self._wtNavGroup:MarkIsStackControlGroup()
		-- WidgetUtil.BindCustomFocusProxy(self._wtNavGroup, self._ScrollGridFocusProxyMaker, self._ScrollGridFocusProxyResolver, self)
		-- WidgetUtil.BindCustomBoundaryNavRule(self._wtNavGroup, self._CustomBoundaryRule, self)
		WidgetUtil.WrapNavBoundary(self._wtNavGroup, {EUINavigation.Left, EUINavigation.Right})

		-- 当前页面需要A键点击
		WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
	else
		WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    	WidgetUtil.RemoveNavigationGroup(self)
	end
end
-- END MODIFICATION

function HeroListTopPanel:_ScrollGridFocusProxyMaker(inWidget)
    -- 找到所在行
    local row = WidgetUtil.GetParentWidgetByClassName(inWidget, "WBP_Hero_HeroItem01_C")
    local rowIndex = self._wtScrollGridList:GetIndexByItem(row)
    return {rowIndex}
end

function HeroListTopPanel:_ScrollGridFocusProxyResolver(inProxyHandle)
    local rowIndex = inProxyHandle[1]
    -- 可能item会在屏幕外，先执行滚动
	self._wtScrollGridList:ScrollToItem(rowIndex, true, true, 15, 0, false)
    local row = self._wtScrollGridList:GetItemByIndex(rowIndex)
    if row then
        return row
    end
    -- 可能会找不到，返回空，自动使用Gorup的默认逻辑查找聚焦
    return nil
end

function HeroListTopPanel:_CustomBoundaryRule(direction)
    if direction ==  EUINavigation.Down then
		self._wtScrollGridList:ScrollToItem(0, true, true, 15, 0, false)
        local item = self._wtScrollGridList:GetItemByIndex(1)
        return item
    elseif direction == EUINavigation.Up then
        local lastIdx = self:_OnGetItemCount()
		self._wtScrollGridList:ScrollToItem(lastIdx - 1, true, true, 15, 0, false)
        local item = self._wtScrollGridList:GetItemByIndex(lastIdx)
        return item
    else
        return nil
    end
end

return HeroListTopPanel