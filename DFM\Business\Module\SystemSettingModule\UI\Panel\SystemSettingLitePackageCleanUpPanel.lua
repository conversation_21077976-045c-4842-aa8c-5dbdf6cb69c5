----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local function log(...)
    loginfo("[SystemSettingLitePackageCleanUpPanel]", ...)
end

local function error(...)
    logerror("[SystemSettingLitePackageCleanUpPanel]", ...)
end

---@class SystemSettingLitePackageCleanUpPanel
local SystemSettingLitePackageCleanUpPanel = ui("SystemSettingLitePackageCleanUpPanel")
local LitePackageConfig = Module.LitePackage.Config
local LitePackageLogic = require "DFM.Business.Module.LitePackageModule.Logic.LitePackageLogic"
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local LiteDownloadNormalItem = require "DFM.Business.Module.LitePackageModule.UI.LiteDownloadNormalItem"
local LiteDownloadDataTable = nil
local LitePackCategoryTable = nil
local LiteDownloadMBNum = 1024 * 1024
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local ECheckBoxState = import"ECheckBoxState"
local EQuitPreference = import "EQuitPreference"
local FAnchors = import "Anchors"
local LitePackageDownloadTableItem = require "DFM.Business.DataStruct.LitePackageStruct.LitePackageDownloadTableItem"

function SystemSettingLitePackageCleanUpPanel:Ctor()
    local fCallbackIns = CreateCallBack(self.OnCloseBtnClick, self)
    self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows):BindCloseCallBack(fCallbackIns)
    self._rootWindow = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    local commonBtns = self._rootWindow:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.RightConfirm, 
        {
            {btnText = LitePackageConfig.Loc.SystemSettingLitePackageCleanUpPanel_DeleteComfirm, fClickCallback = self._OnDeleteComfirm, caller = self, bNeedClose = false, bNeedDeClose = false},
        }, true)

    self._wtSGBMailList = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1", self.OnGetCategoryCount, self._OnProcessCategoryWidget)
    self._delConfirmBtn = commonBtns[CommonPopWindows.EHandleBtnType.Confirm]
    self._delConfirmBtn:Event("OnDeClicked", self._OnDeclickedConfirm, self)
    self._wtCheckBox = self:Wnd("wtDFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtCheckBox:Event("OnCheckStateChanged", self._OnSelectedAll, self)
    self._wtSelectedText = self:Wnd("DFTextBlock_52", UITextBlock)
    -- NoAnything
    self._wtSlotForContent = self:Wnd("EmptySlot", UIWidgetBase)

    self.CategoryTable = {}
    self._deleltePaks = {}
    self._categoryCount = 0
    self._cleanUpSize = 0
    self._paksCount = 0
    self._allPaksNum = 0 --界面中所有可清理的包体数量
end

function SystemSettingLitePackageCleanUpPanel:AddListeners()
    self:AddLuaEvent(LitePackageConfig.evtSelectedAllCategoryPaks, self._OnSelectedCatrgoryPaks, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadItemSelected, self._OnSelectedCleanPak, self)
    self:AddLuaEvent(LitePackageConfig.evtDownloadCategoryOverlaped, self.OnDownloadCategoryOverlaped, self)
end

function SystemSettingLitePackageCleanUpPanel:OnShowBegin()
    Module.LitePackage:SendDownloadSettingInfo("PackageCleanUP",1)
    self._wtSGBMailList:RefreshAllItems()
    if self._rootWindow then
        self._delConfirmBtn:SetIsEnabledStyle(false)
    end
end

function SystemSettingLitePackageCleanUpPanel:OnOpen()
    log("SystemSettingLitePackageCleanUpPanel:OnOpen")
    self:GetAllDeletablePaks()
    self:AddListeners()
end

function SystemSettingLitePackageCleanUpPanel:OnClose()
    self:RemoveAllLuaEvent()
end

---@overload fun(LuaUIBaseView, OnInitExtraData)
function SystemSettingLitePackageCleanUpPanel:OnInitExtraData(bShowDownloadCenter)
    self.bShowDownloadCenter = bShowDownloadCenter
end

function SystemSettingLitePackageCleanUpPanel:GetAllDeletablePaks()
    
    if not LiteDownloadDataTable then
        LiteDownloadDataTable = Facade.TableManager:GetTable("LitePackageDownload")
    end

    if not LitePackCategoryTable then
        LitePackCategoryTable = Facade.TableManager:GetTable("LitePackageCategory")
    end

    for _, v in pairs(LitePackCategoryTable) do
        if v.ShowCategoryID == 6 or v.ShowCategoryID == 8 or v.ShowCategoryID == 9 then --she2只清除地图资源 SHE3可以删场景和视频
            local categoryTable = {}
            categoryTable.CategoryTitle = v.CategoryTitle
            categoryTable.ShowCategoryID = v.ShowCategoryID
            categoryTable.Priority = v.Priority
            categoryTable.Paks = {}
            for _, pak in pairs(LiteDownloadDataTable) do
                local pakTableItem = LitePackageDownloadTableItem:New(pak)
                if pakTableItem.ShowCategory == v.ShowCategoryID and pakTableItem.IsShow >0  then
                    pakTableItem.ModuleKey = self:SetPakModuleKey(pakTableItem.ModuleKey)
                    if self:CheckDownloadedAndDeletable(pakTableItem) then
                        table.insert(categoryTable.Paks,pakTableItem)
                        self._allPaksNum = self._allPaksNum + 1
                    end
                end
                table.sort(categoryTable.Paks, function (a,b)
                    return a.Priority > b.Priority
                end)
            end

            if categoryTable.Paks and (#categoryTable.Paks >0) then
                table.insert(self.CategoryTable,categoryTable)
            end
        end

    end

    table.sort(self.CategoryTable, function (a,b)
        return a.Priority > b.Priority
    end)
end

function SystemSettingLitePackageCleanUpPanel:CheckDownloadedAndDeletable(pak)
    local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(pak.ModuleKey)
    local bDeletable = (pak.IsDeletable > 0)
    return (bDownloaded and bDeletable)
end

function SystemSettingLitePackageCleanUpPanel:OnGetCategoryCount()
    self._categoryCount = 0
    for _,category in pairs(self.CategoryTable) do
        if category.Paks and (#category.Paks > 0) then
            self._categoryCount = self._categoryCount + 1
        end
    end
    self:SetNoAnything(self._categoryCount == 0)
    return self._categoryCount
end

function SystemSettingLitePackageCleanUpPanel:_OnProcessCategoryWidget(position,widget)
    if self.CategoryTable[position] then
        widget:RefreshItem(self.CategoryTable[position],self.CategoryTable[position].ShowCategoryID)
        widget:SetPanelType(2)
    end
end


function SystemSettingLitePackageCleanUpPanel:OnCloseBtnClick()
    Facade.UIManager:CloseUI(self)
end


function SystemSettingLitePackageCleanUpPanel:_OnSelectedAll(bSelected)
    for _, categoryPaks in pairs(self.CategoryTable) do
        categoryPaks.bSelected = bSelected
    end
    self._wtSGBMailList:RefreshAllItems()
    self:_OnSelectedCatrgoryPaks(bSelected, -1)
end

function SystemSettingLitePackageCleanUpPanel:_OnDeclickedConfirm()
    Module.CommonTips:ShowSimpleTip(Module.LitePackage.Config.Loc.SystemSettingLitePackageCleanUpPanel_SelectPak)
end


--- 确认清理按钮
function SystemSettingLitePackageCleanUpPanel:_OnDeleteComfirm()
    local confirmHandle = function ()
        Module.LitePackage:SendDownloadSettingInfo("PackageCleanUP",3)
        local deleteModuleNames = {}
        for _, moduleKey in pairs(self._deleltePaks) do
            table.insert(deleteModuleNames,moduleKey)
        end
        LiteDownloadManager:SetDeletePaksByModuleNames(deleteModuleNames)
        if PLATFORM_IOS then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.GMTestCrash")
        else
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end
    end

    local cancelHandle = function ()
        Module.LitePackage:SendDownloadSettingInfo("PackageCleanUP",2)
    end

    Module.CommonTips:ShowConfirmWindow(string.format(Module.LitePackage.Config.Loc.LitePackageMainPanel_Download_ConfirmCleanUp,self._cleanUpSize/LiteDownloadMBNum),confirmHandle,cancelHandle,nil,Module.Login.Config.Loc.FixPufferConfirmBtnText)
end

--- 选中清理某一类下载包体(若传的参数为 -1，则代表全选)
function SystemSettingLitePackageCleanUpPanel:_OnSelectedCatrgoryPaks(bSelected,showCategoryID)
    local deleteCount = 0
    if bSelected then
        self._cleanUpSize = 0
        for _, categoryPaks in pairs(self.CategoryTable) do
            if categoryPaks.ShowCategoryID == showCategoryID or showCategoryID == -1 then
                for _, pak in pairs(categoryPaks.Paks) do
                    self._deleltePaks[pak.ModuleKey] = pak.ModuleKey
                    self._cleanUpSize = self._cleanUpSize + LiteDownloadManager:GetTotalSizeByModuleName(pak.ModuleKey)
                end
            end
        end
    else
        if showCategoryID == -1 then -- -1代表全部去勾选，否则为选择性去勾选
            self._cleanUpSize = 0
            self._deleltePaks = {}
            deleteCount = self:GetPaksCount(self._deleltePaks)
            self._wtSelectedText:SetText(string.format(Module.LitePackage.Config.Loc.SystemSettingLitePackageCleanUpPanel_SelectedText, deleteCount))
            self:SetCleanUpBtnEnabled(deleteCount > 0)
            return
        end
        for _, categoryPaks in pairs(self.CategoryTable) do
            if categoryPaks.ShowCategoryID == showCategoryID or showCategoryID == -1 then
                for _, pak in pairs(categoryPaks.Paks) do
                    self._deleltePaks[pak.ModuleKey] = nil
                    self._cleanUpSize = self._cleanUpSize - LiteDownloadManager:GetTotalSizeByModuleName(pak.ModuleKey)
                end
            end
        end
    end
    deleteCount = self:GetPaksCount(self._deleltePaks)
    if deleteCount == self._allPaksNum then
        self._wtCheckBox:SetIsChecked(true,false)
    else
        self._wtCheckBox:SetIsChecked(false,false)
    end
    self._wtSelectedText:SetText(string.format(Module.LitePackage.Config.Loc.SystemSettingLitePackageCleanUpPanel_SelectedText, deleteCount))
    self:SetCleanUpBtnEnabled(deleteCount > 0)
end

--- 单独选中清理某一包体
function SystemSettingLitePackageCleanUpPanel:_OnSelectedCleanPak(bSelcted,moduleKey,category)

    if bSelcted then
        self._cleanUpSize = self._cleanUpSize + LiteDownloadManager:GetTotalSizeByModuleName(moduleKey)
        self._deleltePaks[moduleKey] = moduleKey
    else
        self._cleanUpSize = self._cleanUpSize - LiteDownloadManager:GetTotalSizeByModuleName(moduleKey)
        self._deleltePaks[moduleKey] = nil
    end
    local deleteCount = self:GetPaksCount(self._deleltePaks)
    if deleteCount == self._allPaksNum then
        self._wtCheckBox:SetIsChecked(true,false)
    else
        self._wtCheckBox:SetIsChecked(false,false)
    end
    self._wtSelectedText:SetText(string.format(Module.LitePackage.Config.Loc.SystemSettingLitePackageCleanUpPanel_SelectedText, deleteCount))
    self:SetCleanUpBtnEnabled(deleteCount > 0)
end

--- 获取选中删除包的个数
function SystemSettingLitePackageCleanUpPanel:GetPaksCount(deletePaks)
    local count = 0
    for _, pak in pairs(deletePaks) do
        if pak then
            count = count + 1
        end
    end
    return count
end

function SystemSettingLitePackageCleanUpPanel:SetCleanUpBtnEnabled(bEnabled)
    if self._rootWindow then --清理按钮失活或激活
        self._delConfirmBtn:SetIsEnabledStyle(bEnabled)
    end
end

function SystemSettingLitePackageCleanUpPanel:OnDownloadCategoryOverlaped()
    if self._RefreshItemHandler then
        Timer.CancelDelay(self._RefreshItemHandler)
        self._RefreshItemHandler = nil
    end
    local function f()
        self._wtSGBMailList:RefreshVisibleItemSizeAndPos()
    end
    self._RefreshItemHandler =  Timer.DelayCall(0, f, self)

end

function SystemSettingLitePackageCleanUpPanel:SetPakModuleKey(moduleKey) --设置modulekey，使其在高清与低清包下都适用
    if LiteDownloadManager:IsHDDownloadStatus() then
        return LiteDownloadManager:GetHDRuntimeNameByModuleName(moduleKey)
    end
        return moduleKey
end




--------------- 无内容标题显隐

function SystemSettingLitePackageCleanUpPanel:SetNoAnything(state) 
    self:UpdateNoAnythingState(state)
    if state then
        self._wtCheckBox:Collapsed()
        self._wtSelectedText:Collapsed()
        self._delConfirmBtn:Collapsed()
    else
        self._wtCheckBox:SelfHitTestInvisible()
        self._wtSelectedText:SelfHitTestInvisible()
        self._delConfirmBtn:SelfHitTestInvisible()
    end

end


function SystemSettingLitePackageCleanUpPanel:UpdateNoAnythingState(state)
    if state then
        self:_RemoveNoAnythingContent()
        ---创建emptyContent
        self.emptyContent = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtSlotForContent)
        local emptyContent = getfromweak(self.emptyContent)
        if emptyContent then
            emptyContent:BP_SetText(Module.LitePackage.Config.Loc.SystemSettingLitePackageCleanUpPanel_NoDeletable_TIP)
            emptyContent:Visible()
            self.isContentOwner = true
        end

    else
        self:_RemoveNoAnythingContent()
    end
end

function SystemSettingLitePackageCleanUpPanel:_RemoveNoAnythingContent()
    self.emptyContent = nil
    if self.isContentOwner then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtSlotForContent)
    end
end



return SystemSettingLitePackageCleanUpPanel
