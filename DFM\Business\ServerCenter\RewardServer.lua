----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSReward)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

---@class RewardServer : ServerBase
local RewardServer = class("RewardServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

function RewardServer:Ctor()
    self.Events = {
        evtUseGiftSuccess = LuaEvent:NewIns("RewardServer.evtUseGiftSuccess"),
        evtShowRewardMainView = LuaEvent:NewIns("RewardServer.evtShowRewardMainView"),
        evtOpenBlindBoxFinished = LuaEvent:NewIns("RewardServer.evtOpenBlindBoxFinished"),
        evtOpenRewardSceneView = LuaEvent:NewIns("RewardServer.evtOpenRewardSceneView"),
        evtCloseRewardSceneView = LuaEvent:NewIns("RewardServer.evtCloseRewardSceneView"),
        evtSetShowRewardScene = LuaEvent:NewIns("RewardServer.evtSetShowRewardScene"),
        evtOnDrawShowFinished = LuaEvent:NewIns("RewardServer.evtOnDrawShowFinished"),
        evtClearCache = LuaEvent:NewIns("RewardServer.evtClearCache"),
        evtOpenBlindBoxWithItemId = LuaEvent:NewIns("RewardServer.evtOpenBlindBoxWithItemId"),
    }
    self.blindboxInfo = {}
    self.itemConditionInfo = {}
    self.mandelBrickDataId = nil -- 当前所消耗的曼德尔砖ID信息
    -- self:_FreshGiftInfoTable()
    self:_FreshBlindBoxInfoTable()
end

function RewardServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSGatewayKickPlayerNtf",self.OnGatewayKickPlayer, self)
end

---------------------------------------------------------------------------------
--- 礼包 Part
---------------------------------------------------------------------------------
function RewardServer:GetGiftInfoByItemId(id)
    return self._mapItemId2GiftInfo[id]
end

function RewardServer:_FreshGiftInfoTable()
    self._mapItemId2GiftInfo = {}
    self._giftInfoList = {}
    local lotteryTable = Facade.TableManager:GetTable("LotteryProp")
    if lotteryTable then
        for _, v in pairs(lotteryTable) do
            self._mapItemId2GiftInfo[v.ItemId] = {itemId = v.ItemId, action = v.Action, lotteryPoolId = v.LotteryPoolId}
        end
    end
    local poolTable = Facade.TableManager:GetTable("LotteryPoolClient")
    if poolTable then
        for _, v in pairs(poolTable) do
            if not self._giftInfoList[v.PoolId] then
                self._giftInfoList[v.PoolId] = {}
            end
            table.insert(self._giftInfoList[v.PoolId],{itemId = v.ItemId, num = v.Num, isDropDirectly = v.IsDropDirectly})
        end
    end
end

function RewardServer:GetGiftInfoListByLotteryId(lotteryPoolId)
    return self._giftInfoList[tonumber(lotteryPoolId)]
end

function RewardServer:GetGiftInfoByLotteryIdAndItemId(lotteryPoolId, itemId)
    for _, itemInfo in pairs(self._giftInfoList[lotteryPoolId]) do
        if itemInfo.itemId == itemId then
            return itemInfo
        end
    end
end

function RewardServer:GetIsGiftAutoUse(id)
    if not self._mapItemId2GiftInfo[id] then
        return
    end
    return self._mapItemId2GiftInfo[id].action == "1"
end


function RewardServer:FetchMandelBrickLimitData(typeId, fCustomCallback)
    local req = pb.CSPlayerGetMPDropLimitDataReq:New()
    req.type_id = setdefault(typeId, 161100)
    local OnCSPlayerGetMPDropLimitDataRes = function(res)
        if res.result == 0 then
            self._mandelBrickLimitData = {
                dayTakeTimes = res.day_take_times,
                weekTakeTimes = res.week_take_times,
                monthTakeTimes = res.month_take_times
            }
        end
        if fCustomCallback then
            fCustomCallback(self._mandelBrickLimitData)
        end
    end
    req:Request(OnCSPlayerGetMPDropLimitDataRes)
end

function RewardServer:GetMandelBrickLimitData()
    return self._mandelBrickLimitData
end

function RewardServer:UseGift(item)
    local req = pb.CSLotteryPropDrawReq:New()
    self._lastUseItemId = clone(item.id)
    req.prop_id = item.id
    req.prop_gid = item.instanceId
    req.num = 1
    local OnCSLotteryPropDrawRes = function(res)
        if res.result == 0 then
            local itemList = {}
            for _,change in pairs(res.data_change.prop_changes) do
                if change.change_type == PropChangeType.SendBuyMail then
                    return LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardInventoryIsFull)
                end
                if (change.change_type == PropChangeType.Add or change.change_type == PropChangeType.Modify) then
                    if change.prop.num >= 0 and change.prop.id ~= 28100000002 then
                        table.insert(itemList, ItemBase:NewIns(change.prop.id, change.prop.num))
                        itemList[#itemList]:SetRawPropInfo(change.prop)
                        itemList[#itemList].bindType = PropBindingType.BindingNotBind
                    end
                end
            end
            for _, change in pairs(res.data_change.currency_changes) do
                if (change.change_type == PropChangeType.Add or change.change_type == PropChangeType.Modify ) then
                    if change.delta and change.currency_id then
                        table.insert(itemList, ItemBase:NewIns(change.currency_id, change.delta))
                        itemList[#itemList].src_id = change.src_id
                    end
                end
            end
            local rewardData = {
                itemList = itemList,
                itemId = self._lastUseItemId
            }
            self.Events.evtUseGiftSuccess:Invoke(rewardData)
        end
        self._lastUseItemId = nil
    end
    req:Request(OnCSLotteryPropDrawRes)
end

---------------------------------------------------------------------------------
--- 盲盒 Part
---------------------------------------------------------------------------------
---TODO
function RewardServer:GetBlindBoxInfoByItemId(id)
    return self.blindboxInfo[id]
end

function RewardServer:_FreshBlindBoxInfoTable()
    local lotteryTable = Facade.TableManager:GetTable("BlindBox")
    for k, v in pairs(lotteryTable) do
        self.itemConditionInfo[v.ItemId] = self.itemConditionInfo[v.ItemId] or {}
        table.insert(self.itemConditionInfo[v.ItemId], k)
        self.blindboxInfo[k] = {itemId = v.ItemId, conditionId = k, itemName = v.ItemName,
            lotteryPoolId = v.LotteryPoolId,
            conditionType = v.ConditionType,conditionArg = v.ConditionArg,conditionArg2 = v.ConditionArg2,
            conditionArg3 = v.ConditionArg3,conditionArg4 = v.ConditionArg4,conditionArg5 = v.ConditionArg5,
            condition2Type = v.Condition2Type,condition2Arg = v.Condition2Arg,condition2Arg2 = v.Condition2Arg2,
            condition2Arg3 = v.Condition2Arg3,condition2Arg4 = v.Condition2Arg4,condition2Arg5 = v.Condition2Arg5
        }
    end
end

-- 拿到打开盲盒条件一
function RewardServer:_GetOpenConditionOne(conditionId, text)
    local result = true
    local conditionCostType = "condition"..text.."Arg"
    local conditionCost = "condition"..text.."Arg2"
    local itemCost = self.blindboxInfo[conditionId][conditionCost]
    local currencyClientType = MapCurrencyId2ClientType[tonumber(self.blindboxInfo[conditionId][conditionCostType])]
    if itemCost <= Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType) then
        result = result
    else
        result = false
    end
    return result
end

-- 拿到打开盲盒条件二
function RewardServer:_GetOpenConditionTwo(conditionId)
    local result = true
    local conditionItem = "conditionArg"
    local conditionCost = "conditionArg2"
    local deviceId = self.blindboxInfo[conditionId][conditionItem]
    local deviceLevel = self.blindboxInfo[conditionId][conditionCost]
    local deviceInfo = Server.BlackSiteServer:GetDeviceData(tonumber(deviceId))
    if deviceInfo and deviceInfo:GetLevel() < deviceLevel then
        return false
    end
    local conditionCostType = "conditionArg3"
    conditionCost = "conditionArg4"
    local itemCost = self.blindboxInfo[conditionId][conditionCost]
    local currencyClientType = MapCurrencyId2ClientType[tonumber(self.blindboxInfo[conditionId][conditionCostType])]
    if itemCost > Server.InventoryServer:GetPlayerCurrencyNum(currencyClientType) then
        result = false
    end
    return result
end

-- 拿到打开盲盒条件三
function RewardServer:_GetOpenConditionThree(conditionId, text)
    local result = true
    local conditionItem
    local conditionCost
    for i = 1,4,2 do
        if i == 1 then
            conditionItem = "condition"..text.."Arg"
            conditionCost = "condition"..text.."Arg2"
        else
            conditionItem = "condition"..text.."Arg" .. tostring(i)
            conditionCost = "condition"..text.."Arg" .. tostring(i+1)
        end
        local  itemId = self.blindboxInfo[conditionId][conditionItem]
        if itemId == "" then
            break
        end
        local  itemCost = self.blindboxInfo[conditionId][conditionCost]
        local itemList = Server.InventoryServer:GetItemsById(tonumber(itemId))
        if itemCost > #itemList then
            result = false
        end
    end
    return result
end

function RewardServer:GetBlindBoxCanBeOpen(item)
    local itemId = tostring(item.id)
    if not self.itemConditionInfo[itemId] then
        return false
    end
    local condionId = self.itemConditionInfo[itemId][1]
    local ret = false
    local result = true
    for _,conditionId in pairs(self.itemConditionInfo[itemId]) do
        local conditionType = self.blindboxInfo[conditionId]["conditionType"]
        local condition2Type = self.blindboxInfo[conditionId]["condition2Type"]
        if conditionType == 1 then
            result = self:_GetOpenConditionOne(conditionId,"")
        elseif conditionType == 2 then
            result = self:_GetOpenConditionTwo(conditionId)
        elseif conditionType == 3 then
            result = self:_GetOpenConditionThree(conditionId,"")
        end
        if condition2Type == 1 then
            result = self:_GetOpenConditionOne(conditionId,"2")
        elseif condition2Type == 2 then
            result = self:_GetOpenConditionTwo(conditionId)
        elseif condition2Type == 3 then
            result = self:_GetOpenConditionThree(conditionId,"2")
        end
        if result == true then
            ret = true
        end
    end
    if ret == true then
        self.mCondionId = condionId
        return true
    else
        return false
    end
end

function RewardServer:IsActivityMandelBrick(id)
    local numStr = tostring(id)
    if numStr:sub(1, 8) == "16110001" then
        return  true
    end
    return false
end


--开启多个蓝图宝箱
function RewardServer:OpenBlindBox(item, num , fOnResCallback)
    if item == nil or item.num < num then
        return
    end   
    local req = pb.CSLotteryBlindBoxDrawReq:New()
    local boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(item.id)
    if itemInfo then
        boxId = itemInfo.ConnectedPool or 0
    end


    req.box_list = {}
    local lotteryBoxData = pb.LotteryBoxData:New()
    lotteryBoxData.box_id = boxId
    lotteryBoxData.num = num or 1
    lotteryBoxData.opened_prop_id = item.id
    lotteryBoxData.opened_prop_gid = item.gid
    lotteryBoxData.opened_prop_num = num
    table.insert(req.box_list, lotteryBoxData)
    -- req:Request(OnBlindBoxOpenRes, {bEnableHighFrequency = true})

    local bOpenRewardScene = ItemHelperTool.IsDecipheredBrickTypeById(item.id)

    

    local OnBlindBoxOpenRes = function(res) 
        loginfo("[RewardServer][OpenBlindBox]OnBlindBoxOpenRes： res.result = ," .. tostring(res.result).."boxid = " .. tostring(boxId) .. " num = " .. tostring(num) .. " itemId = " .. tostring(item.id))

        if fOnResCallback then
            fOnResCallback(res)
        end
        
        if boxId~= 0 then
            Server.StoreServer:ClearBoxInfoCache(boxId)
        end
        
        --更新藏品曼德尔砖页签红点
        self.Events.evtOpenBlindBoxWithItemId:Invoke(item.id)

        -- 判断是否为曼德尔砖
        local bIsMandel = false
        local bIsActivityMandel = false
        local numStr = tostring(item.id)        
        if numStr:sub(1, 5) == "16110" then -- 检查是否以16110开头            
            if numStr:sub(1, 6) ~= "161109" then -- 进一步检查是否不以161109开头
                bIsMandel = true
                self.mandelBrickDataId = item.id
                if self:IsActivityMandelBrick(item.id) then
                    bIsActivityMandel = true 
                end
            end
        end

        if not res.result or res.result ~= 0 then
            if res.result and res.result == Err.DepositSendMailFailed then
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardInventoryAndEmailIsFull)
            else
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardOpenBlindBoxFail)
            end
            self.Events.evtOpenBlindBoxFinished:Invoke()
            self.Events.evtOnDrawShowFinished:Invoke()
        elseif not res.data_change or not res.data_change.prop_changes then
            LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardOpenBlindBoxNone)
            self.Events.evtOpenBlindBoxFinished:Invoke()
            self.Events.evtOnDrawShowFinished:Invoke()
        else
            local itemList = {}
            for _,change in pairs(res.data_change.prop_changes) do
                if change.change_type == PropChangeType.SendBuyMail then
                    return LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.RewardInventoryIsFull)
                end
                if (change.change_type == PropChangeType.Add or change.change_type == PropChangeType.Modify ) then
                    if change.prop.num >= 0 and change.prop.id ~= 28100000002 then
                        local item = ItemHelperTool.CreateItemByPropInfo(change.prop, change)
                        if item then
                            table.insert(itemList, item)
                        end
                    end
                end
            end
            for _, change in pairs(res.data_change.currency_changes) do
                if change.delta and change.currency_id then
                    table.insert(itemList, ItemBase:NewIns(change.currency_id, change.delta))
                    itemList[#itemList].src_id = change.src_id
                end
            end
            local rewardData = {
                itemList = itemList,
                itemId = item.id
            }
            num = setdefault(num, 1)
            local bTenDraws = num == 10 -- 1-9个砖进一抽，10个砖进十抽
            if bIsMandel then -- 只有曼德尔砖才进场景
                local iType = 0
                if bIsActivityMandel then
                    iType = 1
                end
                self:ProcessRewardItems(rewardData, bOpenRewardScene, bTenDraws, iType)
            end
        end
    end
    self:SendLotteryBlindBoxDraw(req, OnBlindBoxOpenRes, {bEnableHighFrequency = true}, bOpenRewardScene)
end

-- 收束曼德尔砖开箱入口
function RewardServer:SendLotteryBlindBoxDraw(req, resCallback, reqTable, bOpenRewardScene)
    bOpenRewardScene = setdefault(bOpenRewardScene, true)
    -- local bCouldOpenRewardScene = DFHD_LUA == 1
    if bOpenRewardScene then
        self.Events.evtSetShowRewardScene:Invoke(true)
    end
    if reqTable then
        req:Request(resCallback, reqTable)
    else
        req:Request(resCallback)
    end
end

--------------------------------------------------------------------------
--- 奖励处理
--------------------------------------------------------------------------
function RewardServer:ProcessRewardItems(rewardData, bOpenRewardScene, bTenDraws, iType)
    if rewardData.itemList ~= nil and #rewardData.itemList > 0 then

        bOpenRewardScene = setdefault(bOpenRewardScene, true)
        
        -- todo test  28010050272 电玩 28020050001 美杜莎  28010050271 棱镜攻势
        --rewardData.itemList[1] = ItemBase:NewIns(28010050271, 1, 100001)
        --rewardData.itemList[2] = ItemBase:NewIns(28020050001, 1, 100002)

        local rewardTable = {}
        -- self.mandelBrickDataId = nil -- TODO需要飞飞那边提供接口获取砖头的ID
        for _, rewardItem in ipairs(rewardData.itemList) do
            if rewardItem.num > 0 then -- num大于0是为了过滤掉扣除的曼德尔砖
                if ItemHelperTool.IsMysticalSkin(rewardItem.id) and rewardItem.quality == ItemConfig.EWeaponSkinQualityType.Orange then
                    rewardItem.itemList = rewardData.itemList
                end
                table.insert(rewardTable, rewardItem)
            end
        end

        -- 获取曼德尔砖场景需要的数据
        local maxQuality = 0
        local itemTable = {} -- 一抽时，table数量为1；十抽时，table数量为10
        local bSceneShow = true
        self._bShowTenSceneInMobile = false -- 手游是否开放十抽场景
        if IsHD() or self._bShowTenSceneInMobile then
            if  bTenDraws then
                for _, rewardItem in ipairs(rewardTable) do
                    if ItemHelperTool.IsMysticalSkin(rewardItem.id) and rewardItem.quality == ItemConfig.EWeaponSkinQualityType.Orange then
                        if bSceneShow then
                            rewardItem.bSceneShow = true
                            bSceneShow = false
                        end
                    end
                    if rewardItem.num > 0 then -- num大于0是为了过滤掉扣除的曼德尔砖
                        table.insert(itemTable, rewardItem)
                    end
                end
            else -- 一抽
                local reward = nil
                for _, rewardItem in ipairs(rewardTable) do
                    if ItemHelperTool.IsMysticalSkin(rewardItem.id) and rewardItem.quality == ItemConfig.EWeaponSkinQualityType.Orange then
                        if bSceneShow then
                            rewardItem.bSceneShow = true
                            bSceneShow = false
                        end
                    end
                    local quality = ItemConfigTool.GetItemQuality(rewardItem.id)
                    if quality > maxQuality and rewardItem.num > 0 then -- 展示最高品质的奖励；num大于0是为了过滤掉扣除的曼德尔砖
                        maxQuality = quality
                        reward = rewardItem
                    end
                end
                if reward then
                    table.insert(itemTable, reward)
                end
            end
        else
            local reward = nil
            for _, rewardItem in ipairs(rewardTable) do
                if ItemHelperTool.IsMysticalSkin(rewardItem.id) and rewardItem.quality == ItemConfig.EWeaponSkinQualityType.Orange then
                    if bSceneShow then
                        rewardItem.bSceneShow = true
                        bSceneShow = false
                    end
                end
                local quality = ItemConfigTool.GetItemQuality(rewardItem.id)
                if quality > maxQuality and rewardItem.num > 0 then -- 展示最高品质的奖励；num大于0是为了过滤掉扣除的曼德尔砖
                    maxQuality = quality
                    reward = rewardItem
                end
            end
            if reward then
                table.insert(itemTable, reward)
            end
        end

        local fCallback = function()
            Facade.UIManager:CommitTransition(true)
            self.Events.evtCloseRewardSceneView:Invoke()
            local title = ServerTipCode.GetItemTitle
            self.Events.evtShowRewardMainView:Invoke(title, rewardTable)
            self.Events.evtOpenBlindBoxFinished:Invoke()
            self.Events.evtOnDrawShowFinished:Invoke()
        end
        -- -- 暂只在PC开启曼德尔砖场景
        -- local bCouldOpenRewardScene = DFHD_LUA == 1
        -- if not bCouldOpenRewardScene or not bOpenRewardScene then
        --     fCallback()
        --     return
        -- end

        if #itemTable == 0 then
            fCallback()
            return
        end
        self.Events.evtOpenRewardSceneView:Invoke(fCallback, itemTable, bTenDraws, iType)
    end
end


function RewardServer:GetMandelBrickDataID()
    return self.mandelBrickDataId
end

---@param item ItemBase
function RewardServer:IsBlindBoxStatus(item)
    local rewardFeature = item:GetFeature(EFeatureType.Reward)
    if (rewardFeature and rewardFeature:IsBlindBox())  and Server.RewardServer:GetBlindBoxCanBeOpen(item) then
        return true
    end
    return false
end



function RewardServer:OnGatewayKickPlayer(ntf)
    if ntf.reason == 1 then
        self.Events.evtClearCache:Invoke(ntf)
    end
end


return RewardServer