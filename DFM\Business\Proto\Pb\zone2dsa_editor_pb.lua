--zone2dsa.protoencode&decode functions.
function pb.pb_DsPkgHeadDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPkgHead) or {} 
    local __dsPID = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __dsPID ~= 0 then tb.dsPID = __dsPID end
    local __player_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __room_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __seq = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __seq ~= 0 then tb.seq = __seq end
    local __ack = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __ack ~= 0 then tb.ack = __ack end
    local __name = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __plat_id = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    local __service_id = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __service_id ~= "" then tb.service_id = __service_id end
    local __context = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __context ~= "" then tb.context = __context end
    local __dest_service_name = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __dest_service_name ~= "" then tb.dest_service_name = __dest_service_name end
    local __dest_service_id = decoder:getstr(13)
    if not PB_USE_DEFAULT_TABLE or __dest_service_id ~= "" then tb.dest_service_id = __dest_service_id end
    local __map_name = decoder:getstr(14)
    if not PB_USE_DEFAULT_TABLE or __map_name ~= "" then tb.map_name = __map_name end
    local __version = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __version ~= 0 then tb.version = __version end
    local __is_return = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __is_return ~= false then tb.is_return = __is_return end
    local __zoneID = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __zoneID ~= 0 then tb.zoneID = __zoneID end
    local __ds_version = decoder:getstr(18)
    if not PB_USE_DEFAULT_TABLE or __ds_version ~= "" then tb.ds_version = __ds_version end
    local __compress = decoder:getbool(19)
    if not PB_USE_DEFAULT_TABLE or __compress ~= false then tb.compress = __compress end
    local __origin_body_size = decoder:geti32(20)
    if not PB_USE_DEFAULT_TABLE or __origin_body_size ~= 0 then tb.origin_body_size = __origin_body_size end
    return tb
end

function pb.pb_DsPkgHeadEncode(tb, encoder)
    if(tb.dsPID) then    encoder:addi32(1, tb.dsPID)    end
    if(tb.player_id) then    encoder:addu64(3, tb.player_id)    end
    if(tb.room_id) then    encoder:addu64(4, tb.room_id)    end
    if(tb.seq) then    encoder:addu32(5, tb.seq)    end
    if(tb.ack) then    encoder:addu32(6, tb.ack)    end
    if(tb.name) then    encoder:addstr(7, tb.name)    end
    if(tb.plat_id) then    encoder:addi32(8, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(9, tb.account_type)    end
    if(tb.service_id) then    encoder:addstr(10, tb.service_id)    end
    if(tb.context) then    encoder:addstr(11, tb.context)    end
    if(tb.dest_service_name) then    encoder:addstr(12, tb.dest_service_name)    end
    if(tb.dest_service_id) then    encoder:addstr(13, tb.dest_service_id)    end
    if(tb.map_name) then    encoder:addstr(14, tb.map_name)    end
    if(tb.version) then    encoder:addu32(15, tb.version)    end
    if(tb.is_return) then    encoder:addbool(16, tb.is_return)    end
    if(tb.zoneID) then    encoder:addi32(17, tb.zoneID)    end
    if(tb.ds_version) then    encoder:addstr(18, tb.ds_version)    end
    if(tb.compress) then    encoder:addbool(19, tb.compress)    end
    if(tb.origin_body_size) then    encoder:addi32(20, tb.origin_body_size)    end
end

function pb.pb_DsPkgDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPkg) or {} 
    tb.head = pb.pb_DsPkgHeadDecode(decoder:getsubmsg(1))
    local __body = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __body ~= "" then tb.body = __body end
    return tb
end

function pb.pb_DsPkgEncode(tb, encoder)
    if(tb.head) then    pb.pb_DsPkgHeadEncode(tb.head, encoder:addsubmsg(1))    end
    if(tb.body) then    encoder:addbuffer(2, tb.body)    end
end

function pb.pb_DsaRouterMetaDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsaRouterMeta) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __zone_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __zone_id ~= 0 then tb.zone_id = __zone_id end
    local __dest_service_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __dest_service_id ~= "" then tb.dest_service_id = __dest_service_id end
    return tb
end

function pb.pb_DsaRouterMetaEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.zone_id) then    encoder:addi32(2, tb.zone_id)    end
    if(tb.dest_service_id) then    encoder:addstr(3, tb.dest_service_id)    end
end

function pb.pb_DsFather2AnyHeartbeatNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsFather2AnyHeartbeatNtf) or {} 
    return tb
end

function pb.pb_DsFather2AnyHeartbeatNtfEncode(tb, encoder)
end

function pb.pb_DsFather2AnyChildExitNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsFather2AnyChildExitNtf) or {} 
    local __pid = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __pid ~= 0 then tb.pid = __pid end
    local __status = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __status ~= 0 then tb.status = __status end
    return tb
end

function pb.pb_DsFather2AnyChildExitNtfEncode(tb, encoder)
    if(tb.pid) then    encoder:addu32(1, tb.pid)    end
    if(tb.status) then    encoder:addu32(2, tb.status)    end
end

function pb.pb_Ds2AnyExitNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyExitNtf) or {} 
    local __pid = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __pid ~= 0 then tb.pid = __pid end
    local __status = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __status ~= 0 then tb.status = __status end
    local __room_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_Ds2AnyExitNtfEncode(tb, encoder)
    if(tb.pid) then    encoder:addu32(1, tb.pid)    end
    if(tb.status) then    encoder:addi32(2, tb.status)    end
    if(tb.room_id) then    encoder:addu64(3, tb.room_id)    end
end

function pb.pb_Any2DsFatherForkChildReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsFatherForkChildReq) or {} 
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __cmdline = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __cmdline ~= "" then tb.cmdline = __cmdline end
    local __env_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __env_id ~= "" then tb.env_id = __env_id end
    return tb
end

function pb.pb_Any2DsFatherForkChildReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.cmdline) then    encoder:addstr(1, tb.cmdline)    end
    if(tb.env_id) then    encoder:addstr(3, tb.env_id)    end
end

function pb.pb_DsFather2AnyForkChildResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsFather2AnyForkChildRes) or {} 
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __pid = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __pid ~= 0 then tb.pid = __pid end
    return tb
end

function pb.pb_DsFather2AnyForkChildResEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.pid) then    encoder:addu32(1, tb.pid)    end
end

function pb.pb_Any2DsFatherQuitNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsFatherQuitNtf) or {} 
    return tb
end

function pb.pb_Any2DsFatherQuitNtfEncode(tb, encoder)
end

function pb.pb_Ds2AnyRoomHeartbeatDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyRoomHeartbeat) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    tb.ds_stat = pb.pb_DsStatDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_Ds2AnyRoomHeartbeatEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.ds_stat) then    pb.pb_DsStatEncode(tb.ds_stat, encoder:addsubmsg(3))    end
end

function pb.pb_Ds2AnyHeartbeatNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyHeartbeatNtf) or {} 
    local __ds_pid = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __ds_pid ~= 0 then tb.ds_pid = __ds_pid end
    tb.ds_stat = pb.pb_DsStatDecode(decoder:getsubmsg(2))
    tb.room_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.room_list[k] = pb.pb_Ds2AnyRoomHeartbeatDecode(v)
    end
    local __multi_ds = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __multi_ds ~= false then tb.multi_ds = __multi_ds end
    return tb
end

function pb.pb_Ds2AnyHeartbeatNtfEncode(tb, encoder)
    if(tb.ds_pid) then    encoder:addi32(1, tb.ds_pid)    end
    if(tb.ds_stat) then    pb.pb_DsStatEncode(tb.ds_stat, encoder:addsubmsg(2))    end
    if(tb.room_list) then
        for i=1,#(tb.room_list) do
            pb.pb_Ds2AnyRoomHeartbeatEncode(tb.room_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.multi_ds) then    encoder:addbool(4, tb.multi_ds)    end
end

function pb.pb_Ds2AnyMultiRoomExitNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyMultiRoomExitNtf) or {} 
    local __ds_pid = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __ds_pid ~= 0 then tb.ds_pid = __ds_pid end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_Ds2AnyMultiRoomExitNtfEncode(tb, encoder)
    if(tb.ds_pid) then    encoder:addi32(1, tb.ds_pid)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
end

function pb.pb_Any2DsAckNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsAckNtf) or {} 
    local __now = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __now ~= 0 then tb.now = __now end
    return tb
end

function pb.pb_Any2DsAckNtfEncode(tb, encoder)
    if(tb.now) then    encoder:addu64(1, tb.now)    end
end

function pb.pb_DsTODInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsTODInfo) or {} 
    local __weather_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weather_id ~= 0 then tb.weather_id = __weather_id end
    local __event_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __event_id ~= 0 then tb.event_id = __event_id end
    return tb
end

function pb.pb_DsTODInfoEncode(tb, encoder)
    if(tb.weather_id) then    encoder:addu64(1, tb.weather_id)    end
    if(tb.event_id) then    encoder:addu64(2, tb.event_id)    end
end

function pb.pb_AfkPunishInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AfkPunishInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __type = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __warning_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __warning_time ~= 0 then tb.warning_time = __warning_time end
    local __kick_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __kick_time ~= 0 then tb.kick_time = __kick_time end
    return tb
end

function pb.pb_AfkPunishInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.type) then    encoder:addu64(2, tb.type)    end
    if(tb.warning_time) then    encoder:addi64(3, tb.warning_time)    end
    if(tb.kick_time) then    encoder:addi64(4, tb.kick_time)    end
end

function pb.pb_Any2DsBeginMatchReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsBeginMatchReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __match_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __map_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __zone_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __zone_id ~= 0 then tb.zone_id = __zone_id end
    local __open_replay = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __open_replay ~= false then tb.open_replay = __open_replay end
    local __max_match_player_count = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __max_match_player_count ~= 0 then tb.max_match_player_count = __max_match_player_count end
    local __min_match_player_count = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __min_match_player_count ~= 0 then tb.min_match_player_count = __min_match_player_count end
    local __max_team_count = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __max_team_count ~= 0 then tb.max_team_count = __max_team_count end
    local __map_name = decoder:getstr(15)
    if not PB_USE_DEFAULT_TABLE or __map_name ~= "" then tb.map_name = __map_name end
    local __map_full_name = decoder:getstr(16)
    if not PB_USE_DEFAULT_TABLE or __map_full_name ~= "" then tb.map_full_name = __map_full_name end
    local __sub_world_name = decoder:getstr(17)
    if not PB_USE_DEFAULT_TABLE or __sub_world_name ~= "" then tb.sub_world_name = __sub_world_name end
    local __map_level = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __map_level ~= 0 then tb.map_level = __map_level end
    tb.join_config = pb.pb_HalfJoinConfigDecode(decoder:getsubmsg(22))
    local __match_open_svc = decoder:getstr(24)
    if not PB_USE_DEFAULT_TABLE or __match_open_svc ~= "" then tb.match_open_svc = __match_open_svc end
    local __ai_level = decoder:geti32(26)
    if not PB_USE_DEFAULT_TABLE or __ai_level ~= 0 then tb.ai_level = __ai_level end
    tb.ailab_sdk_info = pb.pb_AILabSDKInfoDecode(decoder:getsubmsg(30))
    local __ailab_ai_level = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __ailab_ai_level ~= 0 then tb.ailab_ai_level = __ailab_ai_level end
    local __open_boss = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __open_boss ~= 0 then tb.open_boss = __open_boss end
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(41))
    tb.tod_info = pb.pb_DsTODInfoDecode(decoder:getsubmsg(42))
    local __ds_version = decoder:getstr(43)
    if not PB_USE_DEFAULT_TABLE or __ds_version ~= "" then tb.ds_version = __ds_version end
    tb.player_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(44)) do
        tb.player_info_list[k] = pb.pb_MatchRoomTeamMemberInfoDecode(v)
    end
    local __close_ai_hint = decoder:getbool(45)
    if not PB_USE_DEFAULT_TABLE or __close_ai_hint ~= false then tb.close_ai_hint = __close_ai_hint end
    local __ai_bt_config = decoder:geti32(46)
    if not PB_USE_DEFAULT_TABLE or __ai_bt_config ~= 0 then tb.ai_bt_config = __ai_bt_config end
    local __bot_difficulty = decoder:getstr(47)
    if not PB_USE_DEFAULT_TABLE or __bot_difficulty ~= "" then tb.bot_difficulty = __bot_difficulty end
    local __ai_drop_id = decoder:geti32(48)
    if not PB_USE_DEFAULT_TABLE or __ai_drop_id ~= 0 then tb.ai_drop_id = __ai_drop_id end
    tb.box_event = decoder:geti32ary(49)
    local __enemy_fashion_visible = decoder:getbool(50)
    if not PB_USE_DEFAULT_TABLE or __enemy_fashion_visible ~= false then tb.enemy_fashion_visible = __enemy_fashion_visible end
    local __tdm_scheme_index = decoder:geti64(51)
    if not PB_USE_DEFAULT_TABLE or __tdm_scheme_index ~= 0 then tb.tdm_scheme_index = __tdm_scheme_index end
    local __map_box_config = decoder:geti32(52)
    if not PB_USE_DEFAULT_TABLE or __map_box_config ~= 0 then tb.map_box_config = __map_box_config end
    tb.attr = pb.pb_RoomAttrDecode(decoder:getsubmsg(53))
    local __spawn_point_config_id = decoder:geti32(54)
    if not PB_USE_DEFAULT_TABLE or __spawn_point_config_id ~= 0 then tb.spawn_point_config_id = __spawn_point_config_id end
    local __is_open_ai_type_dynamic_switching = decoder:getbool(55)
    if not PB_USE_DEFAULT_TABLE or __is_open_ai_type_dynamic_switching ~= false then tb.is_open_ai_type_dynamic_switching = __is_open_ai_type_dynamic_switching end
    local __course_id = decoder:getu32(56)
    if not PB_USE_DEFAULT_TABLE or __course_id ~= 0 then tb.course_id = __course_id end
    local __scheme_id = decoder:getu32(57)
    if not PB_USE_DEFAULT_TABLE or __scheme_id ~= 0 then tb.scheme_id = __scheme_id end
    local __first_lose_id = decoder:getu32(58)
    if not PB_USE_DEFAULT_TABLE or __first_lose_id ~= 0 then tb.first_lose_id = __first_lose_id end
    local __idc = decoder:getstr(59)
    if not PB_USE_DEFAULT_TABLE or __idc ~= "" then tb.idc = __idc end
    local __client_group = decoder:geti32(60)
    if not PB_USE_DEFAULT_TABLE or __client_group ~= 0 then tb.client_group = __client_group end
    local __sol_mandel_brick_id = decoder:getu64(61)
    if not PB_USE_DEFAULT_TABLE or __sol_mandel_brick_id ~= 0 then tb.sol_mandel_brick_id = __sol_mandel_brick_id end
    local __player_robot_num = decoder:getu32(101)
    if not PB_USE_DEFAULT_TABLE or __player_robot_num ~= 0 then tb.player_robot_num = __player_robot_num end
    local __match_open_svc_id = decoder:getstr(102)
    if not PB_USE_DEFAULT_TABLE or __match_open_svc_id ~= "" then tb.match_open_svc_id = __match_open_svc_id end
    local __outer_port = decoder:getu32(103)
    if not PB_USE_DEFAULT_TABLE or __outer_port ~= 0 then tb.outer_port = __outer_port end
    local __dstlog_udp_addr = decoder:getstr(104)
    if not PB_USE_DEFAULT_TABLE or __dstlog_udp_addr ~= "" then tb.dstlog_udp_addr = __dstlog_udp_addr end
    local __enable_replay = decoder:getbool(105)
    if not PB_USE_DEFAULT_TABLE or __enable_replay ~= false then tb.enable_replay = __enable_replay end
    tb.drop_ds_tglog_names = decoder:getstrary(106)
    local __match_sequence = decoder:getu32(107)
    if not PB_USE_DEFAULT_TABLE or __match_sequence ~= 0 then tb.match_sequence = __match_sequence end
    local __idc_score_load_enable = decoder:getbool(108)
    if not PB_USE_DEFAULT_TABLE or __idc_score_load_enable ~= false then tb.idc_score_load_enable = __idc_score_load_enable end
    local __is_gray_match = decoder:getbool(109)
    if not PB_USE_DEFAULT_TABLE or __is_gray_match ~= false then tb.is_gray_match = __is_gray_match end
    tb.trace_white_player_ids = decoder:getu64ary(110)
    local __random_seed = decoder:geti64(200)
    if not PB_USE_DEFAULT_TABLE or __random_seed ~= 0 then tb.random_seed = __random_seed end
    local __spawn_point_seed = decoder:geti64(201)
    if not PB_USE_DEFAULT_TABLE or __spawn_point_seed ~= 0 then tb.spawn_point_seed = __spawn_point_seed end
    local __open_evacuation_point = decoder:getbool(202)
    if not PB_USE_DEFAULT_TABLE or __open_evacuation_point ~= false then tb.open_evacuation_point = __open_evacuation_point end
    local __iris_event_course_id = decoder:getu32(203)
    if not PB_USE_DEFAULT_TABLE or __iris_event_course_id ~= 0 then tb.iris_event_course_id = __iris_event_course_id end
    local __iris_opening_event_id = decoder:getu32(204)
    if not PB_USE_DEFAULT_TABLE or __iris_opening_event_id ~= 0 then tb.iris_opening_event_id = __iris_opening_event_id end
    local __iris_course_id = decoder:getu32(205)
    if not PB_USE_DEFAULT_TABLE or __iris_course_id ~= 0 then tb.iris_course_id = __iris_course_id end
    tb.iris_event_ids = decoder:getu64ary(206)
    local __isolate_type = decoder:getu32(207)
    if not PB_USE_DEFAULT_TABLE or __isolate_type ~= 0 then tb.isolate_type = __isolate_type end
    tb.holiday_array = decoder:getu32ary(208)
    tb.afk_punish_list = {}
    for k,v in pairs(decoder:getsubmsgary(209)) do
        tb.afk_punish_list[k] = pb.pb_AfkPunishInfoDecode(v)
    end
    local __evacuation_point_config_id = decoder:geti32(210)
    if not PB_USE_DEFAULT_TABLE or __evacuation_point_config_id ~= 0 then tb.evacuation_point_config_id = __evacuation_point_config_id end
    local __room_skill_level = decoder:geti32(211)
    if not PB_USE_DEFAULT_TABLE or __room_skill_level ~= 0 then tb.room_skill_level = __room_skill_level end
    tb.best_idc = pb.pb_RttBestIdcDecode(decoder:getsubmsg(301))
    local __lobby_server_time = decoder:geti64(302)
    if not PB_USE_DEFAULT_TABLE or __lobby_server_time ~= 0 then tb.lobby_server_time = __lobby_server_time end
    local __tdm_begin_vote_ratio = decoder:getfloat(401)
    if not PB_USE_DEFAULT_TABLE or __tdm_begin_vote_ratio ~= 0 then tb.tdm_begin_vote_ratio = __tdm_begin_vote_ratio end
    local __tdm_conquest_sector_vote_ratio = decoder:getfloat(402)
    if not PB_USE_DEFAULT_TABLE or __tdm_conquest_sector_vote_ratio ~= 0 then tb.tdm_conquest_sector_vote_ratio = __tdm_conquest_sector_vote_ratio end
    local __tdm_game_time_ratio = decoder:getfloat(403)
    if not PB_USE_DEFAULT_TABLE or __tdm_game_time_ratio ~= 0 then tb.tdm_game_time_ratio = __tdm_game_time_ratio end
    local __tdm_revive_time_ratio = decoder:getfloat(404)
    if not PB_USE_DEFAULT_TABLE or __tdm_revive_time_ratio ~= 0 then tb.tdm_revive_time_ratio = __tdm_revive_time_ratio end
    tb.roomsvr_param = pb.pb_RoomsvrParamDecode(decoder:getsubmsg(405))
    local __mode_type = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __mode_type ~= 0 then tb.mode_type = __mode_type end
    local __submode_type = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __submode_type ~= 0 then tb.submode_type = __submode_type end
    tb.player_id_list = decoder:getu64ary(21)
    local __version = decoder:getu64(25)
    if not PB_USE_DEFAULT_TABLE or __version ~= 0 then tb.version = __version end
    local __match_subtype = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __pve_mission_level = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __pve_mission_level ~= 0 then tb.pve_mission_level = __pve_mission_level end
    local __match_halfway_start_id = decoder:getu32(29)
    if not PB_USE_DEFAULT_TABLE or __match_halfway_start_id ~= 0 then tb.match_halfway_start_id = __match_halfway_start_id end
    local __rule = decoder:getu32(40)
    if not PB_USE_DEFAULT_TABLE or __rule ~= 0 then tb.rule = __rule end
    local __player_scheme_id = decoder:getu32(100)
    if not PB_USE_DEFAULT_TABLE or __player_scheme_id ~= 0 then tb.player_scheme_id = __player_scheme_id end
    return tb
end

function pb.pb_Any2DsBeginMatchReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.match_type) then    encoder:addi32(2, tb.match_type)    end
    if(tb.map_id) then    encoder:addu32(3, tb.map_id)    end
    if(tb.zone_id) then    encoder:addi32(6, tb.zone_id)    end
    if(tb.open_replay) then    encoder:addbool(7, tb.open_replay)    end
    if(tb.max_match_player_count) then    encoder:addu32(10, tb.max_match_player_count)    end
    if(tb.min_match_player_count) then    encoder:addi32(11, tb.min_match_player_count)    end
    if(tb.max_team_count) then    encoder:addi32(12, tb.max_team_count)    end
    if(tb.map_name) then    encoder:addstr(15, tb.map_name)    end
    if(tb.map_full_name) then    encoder:addstr(16, tb.map_full_name)    end
    if(tb.sub_world_name) then    encoder:addstr(17, tb.sub_world_name)    end
    if(tb.map_level) then    encoder:addi32(18, tb.map_level)    end
    if(tb.join_config) then    pb.pb_HalfJoinConfigEncode(tb.join_config, encoder:addsubmsg(22))    end
    if(tb.match_open_svc) then    encoder:addstr(24, tb.match_open_svc)    end
    if(tb.ai_level) then    encoder:addi32(26, tb.ai_level)    end
    if(tb.ailab_sdk_info) then    pb.pb_AILabSDKInfoEncode(tb.ailab_sdk_info, encoder:addsubmsg(30))    end
    if(tb.ailab_ai_level) then    encoder:addi32(31, tb.ailab_ai_level)    end
    if(tb.open_boss) then    encoder:addi32(32, tb.open_boss)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(41))    end
    if(tb.tod_info) then    pb.pb_DsTODInfoEncode(tb.tod_info, encoder:addsubmsg(42))    end
    if(tb.ds_version) then    encoder:addstr(43, tb.ds_version)    end
    if(tb.player_info_list) then
        for i=1,#(tb.player_info_list) do
            pb.pb_MatchRoomTeamMemberInfoEncode(tb.player_info_list[i], encoder:addsubmsg(44))
        end
    end
    if(tb.close_ai_hint) then    encoder:addbool(45, tb.close_ai_hint)    end
    if(tb.ai_bt_config) then    encoder:addi32(46, tb.ai_bt_config)    end
    if(tb.bot_difficulty) then    encoder:addstr(47, tb.bot_difficulty)    end
    if(tb.ai_drop_id) then    encoder:addi32(48, tb.ai_drop_id)    end
    if(tb.box_event) then    encoder:addi32(49, tb.box_event)    end
    if(tb.enemy_fashion_visible) then    encoder:addbool(50, tb.enemy_fashion_visible)    end
    if(tb.tdm_scheme_index) then    encoder:addi64(51, tb.tdm_scheme_index)    end
    if(tb.map_box_config) then    encoder:addi32(52, tb.map_box_config)    end
    if(tb.attr) then    pb.pb_RoomAttrEncode(tb.attr, encoder:addsubmsg(53))    end
    if(tb.spawn_point_config_id) then    encoder:addi32(54, tb.spawn_point_config_id)    end
    if(tb.is_open_ai_type_dynamic_switching) then    encoder:addbool(55, tb.is_open_ai_type_dynamic_switching)    end
    if(tb.course_id) then    encoder:addu32(56, tb.course_id)    end
    if(tb.scheme_id) then    encoder:addu32(57, tb.scheme_id)    end
    if(tb.first_lose_id) then    encoder:addu32(58, tb.first_lose_id)    end
    if(tb.idc) then    encoder:addstr(59, tb.idc)    end
    if(tb.client_group) then    encoder:addi32(60, tb.client_group)    end
    if(tb.sol_mandel_brick_id) then    encoder:addu64(61, tb.sol_mandel_brick_id)    end
    if(tb.player_robot_num) then    encoder:addu32(101, tb.player_robot_num)    end
    if(tb.match_open_svc_id) then    encoder:addstr(102, tb.match_open_svc_id)    end
    if(tb.outer_port) then    encoder:addu32(103, tb.outer_port)    end
    if(tb.dstlog_udp_addr) then    encoder:addstr(104, tb.dstlog_udp_addr)    end
    if(tb.enable_replay) then    encoder:addbool(105, tb.enable_replay)    end
    if(tb.drop_ds_tglog_names) then    encoder:addstr(106, tb.drop_ds_tglog_names)    end
    if(tb.match_sequence) then    encoder:addu32(107, tb.match_sequence)    end
    if(tb.idc_score_load_enable) then    encoder:addbool(108, tb.idc_score_load_enable)    end
    if(tb.is_gray_match) then    encoder:addbool(109, tb.is_gray_match)    end
    if(tb.trace_white_player_ids) then    encoder:addu64(110, tb.trace_white_player_ids)    end
    if(tb.random_seed) then    encoder:addi64(200, tb.random_seed)    end
    if(tb.spawn_point_seed) then    encoder:addi64(201, tb.spawn_point_seed)    end
    if(tb.open_evacuation_point) then    encoder:addbool(202, tb.open_evacuation_point)    end
    if(tb.iris_event_course_id) then    encoder:addu32(203, tb.iris_event_course_id)    end
    if(tb.iris_opening_event_id) then    encoder:addu32(204, tb.iris_opening_event_id)    end
    if(tb.iris_course_id) then    encoder:addu32(205, tb.iris_course_id)    end
    if(tb.iris_event_ids) then    encoder:addu64(206, tb.iris_event_ids)    end
    if(tb.isolate_type) then    encoder:addu32(207, tb.isolate_type)    end
    if(tb.holiday_array) then    encoder:addu32(208, tb.holiday_array)    end
    if(tb.afk_punish_list) then
        for i=1,#(tb.afk_punish_list) do
            pb.pb_AfkPunishInfoEncode(tb.afk_punish_list[i], encoder:addsubmsg(209))
        end
    end
    if(tb.evacuation_point_config_id) then    encoder:addi32(210, tb.evacuation_point_config_id)    end
    if(tb.room_skill_level) then    encoder:addi32(211, tb.room_skill_level)    end
    if(tb.best_idc) then    pb.pb_RttBestIdcEncode(tb.best_idc, encoder:addsubmsg(301))    end
    if(tb.lobby_server_time) then    encoder:addi64(302, tb.lobby_server_time)    end
    if(tb.tdm_begin_vote_ratio) then    encoder:addfloat(401, tb.tdm_begin_vote_ratio)    end
    if(tb.tdm_conquest_sector_vote_ratio) then    encoder:addfloat(402, tb.tdm_conquest_sector_vote_ratio)    end
    if(tb.tdm_game_time_ratio) then    encoder:addfloat(403, tb.tdm_game_time_ratio)    end
    if(tb.tdm_revive_time_ratio) then    encoder:addfloat(404, tb.tdm_revive_time_ratio)    end
    if(tb.roomsvr_param) then    pb.pb_RoomsvrParamEncode(tb.roomsvr_param, encoder:addsubmsg(405))    end
    if(tb.mode_type) then    encoder:addu32(4, tb.mode_type)    end
    if(tb.submode_type) then    encoder:addu32(5, tb.submode_type)    end
    if(tb.player_id_list) then    encoder:addu64(21, tb.player_id_list)    end
    if(tb.version) then    encoder:addu64(25, tb.version)    end
    if(tb.match_subtype) then    encoder:addi32(27, tb.match_subtype)    end
    if(tb.pve_mission_level) then    encoder:addi32(28, tb.pve_mission_level)    end
    if(tb.match_halfway_start_id) then    encoder:addu32(29, tb.match_halfway_start_id)    end
    if(tb.rule) then    encoder:addu32(40, tb.rule)    end
    if(tb.player_scheme_id) then    encoder:addu32(100, tb.player_scheme_id)    end
end

function pb.pb_DsPlayerAddrDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerAddr) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __ds_ip = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __ds_ip ~= 0 then tb.ds_ip = __ds_ip end
    local __ds_textual_ip = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    local __ds_port = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __ds_domain = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __ds_domain ~= "" then tb.ds_domain = __ds_domain end
    return tb
end

function pb.pb_DsPlayerAddrEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.ds_ip) then    encoder:addu32(2, tb.ds_ip)    end
    if(tb.ds_textual_ip) then    encoder:addstr(3, tb.ds_textual_ip)    end
    if(tb.ds_port) then    encoder:addu32(4, tb.ds_port)    end
    if(tb.ds_domain) then    encoder:addstr(5, tb.ds_domain)    end
end

function pb.pb_Ds2AnyBeginMatchResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyBeginMatchRes) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __result = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __ds_ip = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ds_ip ~= 0 then tb.ds_ip = __ds_ip end
    local __ds_port = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __during_time = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __during_time ~= 0 then tb.during_time = __during_time end
    local __dsa_id = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    local __ds_domain = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __ds_domain ~= "" then tb.ds_domain = __ds_domain end
    local __ds_textual_ip = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    tb.player_addr_list = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.player_addr_list[k] = pb.pb_DsPlayerAddrDecode(v)
    end
    tb.machine_load = pb.pb_DSMachineLoadDecode(decoder:getsubmsg(100))
    tb.idc_load = pb.pb_IDCDSLoadDecode(decoder:getsubmsg(101))
    return tb
end

function pb.pb_Ds2AnyBeginMatchResEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.result) then    encoder:addi32(2, tb.result)    end
    if(tb.ds_ip) then    encoder:addu32(3, tb.ds_ip)    end
    if(tb.ds_port) then    encoder:addu32(4, tb.ds_port)    end
    if(tb.during_time) then    encoder:addi64(8, tb.during_time)    end
    if(tb.dsa_id) then    encoder:addstr(9, tb.dsa_id)    end
    if(tb.ds_domain) then    encoder:addstr(10, tb.ds_domain)    end
    if(tb.ds_textual_ip) then    encoder:addstr(11, tb.ds_textual_ip)    end
    if(tb.player_addr_list) then
        for i=1,#(tb.player_addr_list) do
            pb.pb_DsPlayerAddrEncode(tb.player_addr_list[i], encoder:addsubmsg(12))
        end
    end
    if(tb.machine_load) then    pb.pb_DSMachineLoadEncode(tb.machine_load, encoder:addsubmsg(100))    end
    if(tb.idc_load) then    pb.pb_IDCDSLoadEncode(tb.idc_load, encoder:addsubmsg(101))    end
end

function pb.pb_DsPlayerInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __open_id = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __open_id ~= "" then tb.open_id = __open_id end
    local __plat_id = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    local __nick_name = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __nick_name ~= "" then tb.nick_name = __nick_name end
    local __ds_token = decoder:getstr(14)
    if not PB_USE_DEFAULT_TABLE or __ds_token ~= "" then tb.ds_token = __ds_token end
    local __is_robot = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __is_robot ~= false then tb.is_robot = __is_robot end
    local __robot_level = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __robot_level ~= 0 then tb.robot_level = __robot_level end
    local __camp = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __camp ~= 0 then tb.camp = __camp end
    local __is_revenge = decoder:getbool(15)
    if not PB_USE_DEFAULT_TABLE or __is_revenge ~= false then tb.is_revenge = __is_revenge end
    local __match_pool_id = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __match_pool_id ~= 0 then tb.match_pool_id = __match_pool_id end
    local __mode_id = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __mode_id ~= 0 then tb.mode_id = __mode_id end
    local __team_id = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __leader_id = decoder:getu64(16)
    if not PB_USE_DEFAULT_TABLE or __leader_id ~= 0 then tb.leader_id = __leader_id end
    local __player_idx = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __player_idx ~= 0 then tb.player_idx = __player_idx end
    local __match_tactics = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __match_tactics ~= 0 then tb.match_tactics = __match_tactics end
    local __skill_score = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __skill_score ~= 0 then tb.skill_score = __skill_score end
    tb.ai_rand_prop_id = decoder:getu32ary(20)
    local __course_id = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __course_id ~= 0 then tb.course_id = __course_id end
    local __drop_logic_id = decoder:geti64(22)
    if not PB_USE_DEFAULT_TABLE or __drop_logic_id ~= 0 then tb.drop_logic_id = __drop_logic_id end
    local __drop_buff_id = decoder:geti64(23)
    if not PB_USE_DEFAULT_TABLE or __drop_buff_id ~= 0 then tb.drop_buff_id = __drop_buff_id end
    tb.drop_counters = {}
    for k,v in pairs(decoder:getsubmsgary(24)) do
        tb.drop_counters[k] = pb.pb_MatchSOLDropCounterDecode(v)
    end
    tb.equiped_props = {}
    for k,v in pairs(decoder:getsubmsgary(30)) do
        tb.equiped_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.role_attr_array = {}
    for k,v in pairs(decoder:getsubmsgary(31)) do
        tb.role_attr_array[k] = pb.pb_DSAttrValueDecode(v)
    end
    tb.role_buff_array = {}
    for k,v in pairs(decoder:getsubmsgary(32)) do
        tb.role_buff_array[k] = pb.pb_AttrBuffDecode(v)
    end
    tb.s_price_values = decoder:geti32ary(33)
    tb.general_skills = {}
    for k,v in pairs(decoder:getsubmsgary(34)) do
        tb.general_skills[k] = pb.pb_GeneralSkillDecode(v)
    end
    tb.hero = pb.pb_HeroDecode(decoder:getsubmsg(35))
    tb.safehouse_info = pb.pb_SafehouseNumeralInfoDecode(decoder:getsubmsg(36))
    tb.player_accepted_quests = {}
    for k,v in pairs(decoder:getsubmsgary(37)) do
        tb.player_accepted_quests[k] = pb.pb_PlayerQuestDataDecode(v)
    end
    tb.ds_quests_desc_list = {}
    for k,v in pairs(decoder:getsubmsgary(39)) do
        tb.ds_quests_desc_list[k] = pb.pb_DSQuestDescDecode(v)
    end
    tb.player_completed_quests = {}
    for k,v in pairs(decoder:getsubmsgary(40)) do
        tb.player_completed_quests[k] = pb.pb_PlayerQuestDataDecode(v)
    end
    tb.quest_world_game_state = {}
    for k,v in pairs(decoder:getsubmsgary(44)) do
        tb.quest_world_game_state[k] = pb.pb_QuestDSGameStateDecode(v)
    end
    tb.player_unaccept_quests = {}
    for k,v in pairs(decoder:getsubmsgary(46)) do
        tb.player_unaccept_quests[k] = pb.pb_PlayerQuestDataDecode(v)
    end
    local __lobby_server_time = decoder:geti64(47)
    if not PB_USE_DEFAULT_TABLE or __lobby_server_time ~= 0 then tb.lobby_server_time = __lobby_server_time end
    local __quests_get_time = decoder:geti64(48)
    if not PB_USE_DEFAULT_TABLE or __quests_get_time ~= 0 then tb.quests_get_time = __quests_get_time end
    tb.marked_props = {}
    for k,v in pairs(decoder:getsubmsgary(41)) do
        tb.marked_props[k] = pb.pb_MarkPropInfoDecode(v)
    end
    tb.strongholds = {}
    for k,v in pairs(decoder:getsubmsgary(42)) do
        tb.strongholds[k] = pb.pb_StrongholdInfoDecode(v)
    end
    tb.world_actors = {}
    for k,v in pairs(decoder:getsubmsgary(43)) do
        tb.world_actors[k] = pb.pb_WorldActorInfoDecode(v)
    end
    local __spawn_point = decoder:getu64(45)
    if not PB_USE_DEFAULT_TABLE or __spawn_point ~= 0 then tb.spawn_point = __spawn_point end
    tb.season_area_array = decoder:geti32ary(49)
    tb.season_prop_array = decoder:getu64ary(50)
    tb.role_load = pb.pb_RoleLoadValueDecode(decoder:getsubmsg(51))
    tb.tdm_numeral_data = pb.pb_TDMNumeralDecode(decoder:getsubmsg(52))
    tb.armedforce_data = pb.pb_ArmedForceNumeralDataDecode(decoder:getsubmsg(53))
    tb.heros = {}
    for k,v in pairs(decoder:getsubmsgary(59)) do
        tb.heros[k] = pb.pb_DSHeroDecode(v)
    end
    local __is_fashion_prior = decoder:getbool(60)
    if not PB_USE_DEFAULT_TABLE or __is_fashion_prior ~= false then tb.is_fashion_prior = __is_fashion_prior end
    tb.weapon_skin_setup = {}
    for k,v in pairs(decoder:getsubmsgary(54)) do
        tb.weapon_skin_setup[k] = pb.pb_WeaponSkinSetupDecode(v)
    end
    tb.weapon_skin_info = {}
    for k,v in pairs(decoder:getsubmsgary(64)) do
        tb.weapon_skin_info[k] = pb.pb_WeaponSkinInfoDecode(v)
    end
    local __robot_type = decoder:getu32(55)
    if not PB_USE_DEFAULT_TABLE or __robot_type ~= 0 then tb.robot_type = __robot_type end
    local __half_join = decoder:getbool(56)
    if not PB_USE_DEFAULT_TABLE or __half_join ~= false then tb.half_join = __half_join end
    local __replace_bot_id = decoder:getu64(66)
    if not PB_USE_DEFAULT_TABLE or __replace_bot_id ~= 0 then tb.replace_bot_id = __replace_bot_id end
    local __pic_url = decoder:getstr(57)
    if not PB_USE_DEFAULT_TABLE or __pic_url ~= "" then tb.pic_url = __pic_url end
    local __difficulty = decoder:getu32(58)
    if not PB_USE_DEFAULT_TABLE or __difficulty ~= 0 then tb.difficulty = __difficulty end
    local __ai_score = decoder:getfloat(61)
    if not PB_USE_DEFAULT_TABLE or __ai_score ~= 0 then tb.ai_score = __ai_score end
    local __ai_style = decoder:geti32(65)
    if not PB_USE_DEFAULT_TABLE or __ai_style ~= 0 then tb.ai_style = __ai_style end
    local __is_ds_guide_passed = decoder:getbool(62)
    if not PB_USE_DEFAULT_TABLE or __is_ds_guide_passed ~= false then tb.is_ds_guide_passed = __is_ds_guide_passed end
    local __is_guide = decoder:getbool(63)
    if not PB_USE_DEFAULT_TABLE or __is_guide ~= false then tb.is_guide = __is_guide end
    local __best_idc = decoder:getstr(67)
    if not PB_USE_DEFAULT_TABLE or __best_idc ~= "" then tb.best_idc = __best_idc end
    local __best_access_point = decoder:getstr(68)
    if not PB_USE_DEFAULT_TABLE or __best_access_point ~= "" then tb.best_access_point = __best_access_point end
    local __best_idc_rtt = decoder:geti32(69)
    if not PB_USE_DEFAULT_TABLE or __best_idc_rtt ~= 0 then tb.best_idc_rtt = __best_idc_rtt end
    tb.activity_data = pb.pb_NumeralActivityDataDecode(decoder:getsubmsg(70))
    local __game_appid = decoder:getstr(71)
    if not PB_USE_DEFAULT_TABLE or __game_appid ~= "" then tb.game_appid = __game_appid end
    tb.tss_tlog_common_info = pb.pb_TssTLogHeaderDecode(decoder:getsubmsg(73))
    local __glicko_rating = decoder:getfloat(74)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating ~= 0 then tb.glicko_rating = __glicko_rating end
    local __glicko_rating_dev = decoder:getfloat(75)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_dev ~= 0 then tb.glicko_rating_dev = __glicko_rating_dev end
    local __fashion_id = decoder:getu64(76)
    if not PB_USE_DEFAULT_TABLE or __fashion_id ~= 0 then tb.fashion_id = __fashion_id end
    local __level = decoder:geti32(77)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __cur_exp = decoder:geti64(84)
    if not PB_USE_DEFAULT_TABLE or __cur_exp ~= 0 then tb.cur_exp = __cur_exp end
    local __match_uuid = decoder:getstr(78)
    if not PB_USE_DEFAULT_TABLE or __match_uuid ~= "" then tb.match_uuid = __match_uuid end
    local __sys_team_id = decoder:getu64(79)
    if not PB_USE_DEFAULT_TABLE or __sys_team_id ~= 0 then tb.sys_team_id = __sys_team_id end
    tb.player_rtt_data = pb.pb_PlayerRttDataDecode(decoder:getsubmsg(80))
    tb.equip_skin_props = {}
    for k,v in pairs(decoder:getsubmsgary(81)) do
        tb.equip_skin_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __safebox_skin_id = decoder:getu64(110)
    if not PB_USE_DEFAULT_TABLE or __safebox_skin_id ~= 0 then tb.safebox_skin_id = __safebox_skin_id end
    local __isRankedMatch = decoder:getbool(82)
    if not PB_USE_DEFAULT_TABLE or __isRankedMatch ~= false then tb.isRankedMatch = __isRankedMatch end
    local __rank_match_score = decoder:geti64(83)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __ranked_score_shoot = decoder:geti64(90)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_shoot ~= 0 then tb.ranked_score_shoot = __ranked_score_shoot end
    local __ranked_score_tactics = decoder:geti64(91)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_tactics ~= 0 then tb.ranked_score_tactics = __ranked_score_tactics end
    local __ranked_score_vehicle = decoder:geti64(92)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_vehicle ~= 0 then tb.ranked_score_vehicle = __ranked_score_vehicle end
    local __sol_double_rank_multiple_value = decoder:getu32(95)
    if not PB_USE_DEFAULT_TABLE or __sol_double_rank_multiple_value ~= 0 then tb.sol_double_rank_multiple_value = __sol_double_rank_multiple_value end
    tb.tdm_detail = pb.pb_TDMDsDetailDecode(decoder:getsubmsg(85))
    local __is_observer = decoder:getbool(86)
    if not PB_USE_DEFAULT_TABLE or __is_observer ~= false then tb.is_observer = __is_observer end
    local __observer_id = decoder:getu32(87)
    if not PB_USE_DEFAULT_TABLE or __observer_id ~= 0 then tb.observer_id = __observer_id end
    local __is_single_player = decoder:getbool(88)
    if not PB_USE_DEFAULT_TABLE or __is_single_player ~= false then tb.is_single_player = __is_single_player end
    local __hidden_score = decoder:geti64(89)
    if not PB_USE_DEFAULT_TABLE or __hidden_score ~= 0 then tb.hidden_score = __hidden_score end
    local __old_glicko_rating = decoder:getfloat(93)
    if not PB_USE_DEFAULT_TABLE or __old_glicko_rating ~= 0 then tb.old_glicko_rating = __old_glicko_rating end
    local __old_glicko_rating_dev = decoder:getfloat(94)
    if not PB_USE_DEFAULT_TABLE or __old_glicko_rating_dev ~= 0 then tb.old_glicko_rating_dev = __old_glicko_rating_dev end
    tb.guide_price_pkg = pb.pb_Any2DsPropGuidePriceNtfDecode(decoder:getsubmsg(96))
    local __title = decoder:getu64(97)
    if not PB_USE_DEFAULT_TABLE or __title ~= 0 then tb.title = __title end
    local __rank_title_adcode = decoder:getu32(105)
    if not PB_USE_DEFAULT_TABLE or __rank_title_adcode ~= 0 then tb.rank_title_adcode = __rank_title_adcode end
    local __rank_title_rank_no = decoder:geti64(106)
    if not PB_USE_DEFAULT_TABLE or __rank_title_rank_no ~= 0 then tb.rank_title_rank_no = __rank_title_rank_no end
    local __use_rental_props = decoder:getbool(98)
    if not PB_USE_DEFAULT_TABLE or __use_rental_props ~= false then tb.use_rental_props = __use_rental_props end
    tb.equip_pendant_props = {}
    for k,v in pairs(decoder:getsubmsgary(99)) do
        tb.equip_pendant_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __is_bought_bhd = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __is_bought_bhd ~= false then tb.is_bought_bhd = __is_bought_bhd end
    tb.bhd_special_data = pb.pb_BhdSpecDataDecode(decoder:getsubmsg(101))
    tb.mp_rank_numeral = pb.pb_MpRankNumeralDecode(decoder:getsubmsg(102))
    tb.commander_numeral = pb.pb_CommanderNumeralDecode(decoder:getsubmsg(103))
    local __ailab_diffculty = decoder:getstr(104)
    if not PB_USE_DEFAULT_TABLE or __ailab_diffculty ~= "" then tb.ailab_diffculty = __ailab_diffculty end
    local __deposit_price = decoder:geti64(107)
    if not PB_USE_DEFAULT_TABLE or __deposit_price ~= 0 then tb.deposit_price = __deposit_price end
    local __mandel_brick_num = decoder:geti64(108)
    if not PB_USE_DEFAULT_TABLE or __mandel_brick_num ~= 0 then tb.mandel_brick_num = __mandel_brick_num end
    tb.roomsvr_numeral = pb.pb_RoomsvrNumeralDecode(decoder:getsubmsg(109))
    local __dogtag_custom_id = decoder:geti32(111)
    if not PB_USE_DEFAULT_TABLE or __dogtag_custom_id ~= 0 then tb.dogtag_custom_id = __dogtag_custom_id end
    local __country_code = decoder:geti32(112)
    if not PB_USE_DEFAULT_TABLE or __country_code ~= 0 then tb.country_code = __country_code end
    local __is_victory_unite_match = decoder:getbool(113)
    if not PB_USE_DEFAULT_TABLE or __is_victory_unite_match ~= false then tb.is_victory_unite_match = __is_victory_unite_match end
    local __has_trigger_gold_egg = decoder:getbool(114)
    if not PB_USE_DEFAULT_TABLE or __has_trigger_gold_egg ~= false then tb.has_trigger_gold_egg = __has_trigger_gold_egg end
    return tb
end

function pb.pb_DsPlayerInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.open_id) then    encoder:addstr(2, tb.open_id)    end
    if(tb.plat_id) then    encoder:addi32(3, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(4, tb.account_type)    end
    if(tb.nick_name) then    encoder:addbuffer(5, tb.nick_name)    end
    if(tb.ds_token) then    encoder:addstr(14, tb.ds_token)    end
    if(tb.is_robot) then    encoder:addbool(8, tb.is_robot)    end
    if(tb.robot_level) then    encoder:addi32(9, tb.robot_level)    end
    if(tb.camp) then    encoder:addi32(10, tb.camp)    end
    if(tb.is_revenge) then    encoder:addbool(15, tb.is_revenge)    end
    if(tb.match_pool_id) then    encoder:addi32(12, tb.match_pool_id)    end
    if(tb.mode_id) then    encoder:addu32(13, tb.mode_id)    end
    if(tb.team_id) then    encoder:addi32(11, tb.team_id)    end
    if(tb.leader_id) then    encoder:addu64(16, tb.leader_id)    end
    if(tb.player_idx) then    encoder:addi32(17, tb.player_idx)    end
    if(tb.match_tactics) then    encoder:addu32(18, tb.match_tactics)    end
    if(tb.skill_score) then    encoder:addi64(19, tb.skill_score)    end
    if(tb.ai_rand_prop_id) then    encoder:addu32(20, tb.ai_rand_prop_id)    end
    if(tb.course_id) then    encoder:addu32(21, tb.course_id)    end
    if(tb.drop_logic_id) then    encoder:addi64(22, tb.drop_logic_id)    end
    if(tb.drop_buff_id) then    encoder:addi64(23, tb.drop_buff_id)    end
    if(tb.drop_counters) then
        for i=1,#(tb.drop_counters) do
            pb.pb_MatchSOLDropCounterEncode(tb.drop_counters[i], encoder:addsubmsg(24))
        end
    end
    if(tb.equiped_props) then
        for i=1,#(tb.equiped_props) do
            pb.pb_EquipPositionEncode(tb.equiped_props[i], encoder:addsubmsg(30))
        end
    end
    if(tb.role_attr_array) then
        for i=1,#(tb.role_attr_array) do
            pb.pb_DSAttrValueEncode(tb.role_attr_array[i], encoder:addsubmsg(31))
        end
    end
    if(tb.role_buff_array) then
        for i=1,#(tb.role_buff_array) do
            pb.pb_AttrBuffEncode(tb.role_buff_array[i], encoder:addsubmsg(32))
        end
    end
    if(tb.s_price_values) then    encoder:addi32(33, tb.s_price_values)    end
    if(tb.general_skills) then
        for i=1,#(tb.general_skills) do
            pb.pb_GeneralSkillEncode(tb.general_skills[i], encoder:addsubmsg(34))
        end
    end
    if(tb.hero) then    pb.pb_HeroEncode(tb.hero, encoder:addsubmsg(35))    end
    if(tb.safehouse_info) then    pb.pb_SafehouseNumeralInfoEncode(tb.safehouse_info, encoder:addsubmsg(36))    end
    if(tb.player_accepted_quests) then
        for i=1,#(tb.player_accepted_quests) do
            pb.pb_PlayerQuestDataEncode(tb.player_accepted_quests[i], encoder:addsubmsg(37))
        end
    end
    if(tb.ds_quests_desc_list) then
        for i=1,#(tb.ds_quests_desc_list) do
            pb.pb_DSQuestDescEncode(tb.ds_quests_desc_list[i], encoder:addsubmsg(39))
        end
    end
    if(tb.player_completed_quests) then
        for i=1,#(tb.player_completed_quests) do
            pb.pb_PlayerQuestDataEncode(tb.player_completed_quests[i], encoder:addsubmsg(40))
        end
    end
    if(tb.quest_world_game_state) then
        for i=1,#(tb.quest_world_game_state) do
            pb.pb_QuestDSGameStateEncode(tb.quest_world_game_state[i], encoder:addsubmsg(44))
        end
    end
    if(tb.player_unaccept_quests) then
        for i=1,#(tb.player_unaccept_quests) do
            pb.pb_PlayerQuestDataEncode(tb.player_unaccept_quests[i], encoder:addsubmsg(46))
        end
    end
    if(tb.lobby_server_time) then    encoder:addi64(47, tb.lobby_server_time)    end
    if(tb.quests_get_time) then    encoder:addi64(48, tb.quests_get_time)    end
    if(tb.marked_props) then
        for i=1,#(tb.marked_props) do
            pb.pb_MarkPropInfoEncode(tb.marked_props[i], encoder:addsubmsg(41))
        end
    end
    if(tb.strongholds) then
        for i=1,#(tb.strongholds) do
            pb.pb_StrongholdInfoEncode(tb.strongholds[i], encoder:addsubmsg(42))
        end
    end
    if(tb.world_actors) then
        for i=1,#(tb.world_actors) do
            pb.pb_WorldActorInfoEncode(tb.world_actors[i], encoder:addsubmsg(43))
        end
    end
    if(tb.spawn_point) then    encoder:addu64(45, tb.spawn_point)    end
    if(tb.season_area_array) then    encoder:addi32(49, tb.season_area_array)    end
    if(tb.season_prop_array) then    encoder:addu64(50, tb.season_prop_array)    end
    if(tb.role_load) then    pb.pb_RoleLoadValueEncode(tb.role_load, encoder:addsubmsg(51))    end
    if(tb.tdm_numeral_data) then    pb.pb_TDMNumeralEncode(tb.tdm_numeral_data, encoder:addsubmsg(52))    end
    if(tb.armedforce_data) then    pb.pb_ArmedForceNumeralDataEncode(tb.armedforce_data, encoder:addsubmsg(53))    end
    if(tb.heros) then
        for i=1,#(tb.heros) do
            pb.pb_DSHeroEncode(tb.heros[i], encoder:addsubmsg(59))
        end
    end
    if(tb.is_fashion_prior) then    encoder:addbool(60, tb.is_fashion_prior)    end
    if(tb.weapon_skin_setup) then
        for i=1,#(tb.weapon_skin_setup) do
            pb.pb_WeaponSkinSetupEncode(tb.weapon_skin_setup[i], encoder:addsubmsg(54))
        end
    end
    if(tb.weapon_skin_info) then
        for i=1,#(tb.weapon_skin_info) do
            pb.pb_WeaponSkinInfoEncode(tb.weapon_skin_info[i], encoder:addsubmsg(64))
        end
    end
    if(tb.robot_type) then    encoder:addu32(55, tb.robot_type)    end
    if(tb.half_join) then    encoder:addbool(56, tb.half_join)    end
    if(tb.replace_bot_id) then    encoder:addu64(66, tb.replace_bot_id)    end
    if(tb.pic_url) then    encoder:addstr(57, tb.pic_url)    end
    if(tb.difficulty) then    encoder:addu32(58, tb.difficulty)    end
    if(tb.ai_score) then    encoder:addfloat(61, tb.ai_score)    end
    if(tb.ai_style) then    encoder:addi32(65, tb.ai_style)    end
    if(tb.is_ds_guide_passed) then    encoder:addbool(62, tb.is_ds_guide_passed)    end
    if(tb.is_guide) then    encoder:addbool(63, tb.is_guide)    end
    if(tb.best_idc) then    encoder:addstr(67, tb.best_idc)    end
    if(tb.best_access_point) then    encoder:addstr(68, tb.best_access_point)    end
    if(tb.best_idc_rtt) then    encoder:addi32(69, tb.best_idc_rtt)    end
    if(tb.activity_data) then    pb.pb_NumeralActivityDataEncode(tb.activity_data, encoder:addsubmsg(70))    end
    if(tb.game_appid) then    encoder:addstr(71, tb.game_appid)    end
    if(tb.tss_tlog_common_info) then    pb.pb_TssTLogHeaderEncode(tb.tss_tlog_common_info, encoder:addsubmsg(73))    end
    if(tb.glicko_rating) then    encoder:addfloat(74, tb.glicko_rating)    end
    if(tb.glicko_rating_dev) then    encoder:addfloat(75, tb.glicko_rating_dev)    end
    if(tb.fashion_id) then    encoder:addu64(76, tb.fashion_id)    end
    if(tb.level) then    encoder:addi32(77, tb.level)    end
    if(tb.cur_exp) then    encoder:addi64(84, tb.cur_exp)    end
    if(tb.match_uuid) then    encoder:addstr(78, tb.match_uuid)    end
    if(tb.sys_team_id) then    encoder:addu64(79, tb.sys_team_id)    end
    if(tb.player_rtt_data) then    pb.pb_PlayerRttDataEncode(tb.player_rtt_data, encoder:addsubmsg(80))    end
    if(tb.equip_skin_props) then
        for i=1,#(tb.equip_skin_props) do
            pb.pb_PropInfoEncode(tb.equip_skin_props[i], encoder:addsubmsg(81))
        end
    end
    if(tb.safebox_skin_id) then    encoder:addu64(110, tb.safebox_skin_id)    end
    if(tb.isRankedMatch) then    encoder:addbool(82, tb.isRankedMatch)    end
    if(tb.rank_match_score) then    encoder:addi64(83, tb.rank_match_score)    end
    if(tb.ranked_score_shoot) then    encoder:addi64(90, tb.ranked_score_shoot)    end
    if(tb.ranked_score_tactics) then    encoder:addi64(91, tb.ranked_score_tactics)    end
    if(tb.ranked_score_vehicle) then    encoder:addi64(92, tb.ranked_score_vehicle)    end
    if(tb.sol_double_rank_multiple_value) then    encoder:addu32(95, tb.sol_double_rank_multiple_value)    end
    if(tb.tdm_detail) then    pb.pb_TDMDsDetailEncode(tb.tdm_detail, encoder:addsubmsg(85))    end
    if(tb.is_observer) then    encoder:addbool(86, tb.is_observer)    end
    if(tb.observer_id) then    encoder:addu32(87, tb.observer_id)    end
    if(tb.is_single_player) then    encoder:addbool(88, tb.is_single_player)    end
    if(tb.hidden_score) then    encoder:addi64(89, tb.hidden_score)    end
    if(tb.old_glicko_rating) then    encoder:addfloat(93, tb.old_glicko_rating)    end
    if(tb.old_glicko_rating_dev) then    encoder:addfloat(94, tb.old_glicko_rating_dev)    end
    if(tb.guide_price_pkg) then    pb.pb_Any2DsPropGuidePriceNtfEncode(tb.guide_price_pkg, encoder:addsubmsg(96))    end
    if(tb.title) then    encoder:addu64(97, tb.title)    end
    if(tb.rank_title_adcode) then    encoder:addu32(105, tb.rank_title_adcode)    end
    if(tb.rank_title_rank_no) then    encoder:addi64(106, tb.rank_title_rank_no)    end
    if(tb.use_rental_props) then    encoder:addbool(98, tb.use_rental_props)    end
    if(tb.equip_pendant_props) then
        for i=1,#(tb.equip_pendant_props) do
            pb.pb_PropInfoEncode(tb.equip_pendant_props[i], encoder:addsubmsg(99))
        end
    end
    if(tb.is_bought_bhd) then    encoder:addbool(100, tb.is_bought_bhd)    end
    if(tb.bhd_special_data) then    pb.pb_BhdSpecDataEncode(tb.bhd_special_data, encoder:addsubmsg(101))    end
    if(tb.mp_rank_numeral) then    pb.pb_MpRankNumeralEncode(tb.mp_rank_numeral, encoder:addsubmsg(102))    end
    if(tb.commander_numeral) then    pb.pb_CommanderNumeralEncode(tb.commander_numeral, encoder:addsubmsg(103))    end
    if(tb.ailab_diffculty) then    encoder:addstr(104, tb.ailab_diffculty)    end
    if(tb.deposit_price) then    encoder:addi64(107, tb.deposit_price)    end
    if(tb.mandel_brick_num) then    encoder:addi64(108, tb.mandel_brick_num)    end
    if(tb.roomsvr_numeral) then    pb.pb_RoomsvrNumeralEncode(tb.roomsvr_numeral, encoder:addsubmsg(109))    end
    if(tb.dogtag_custom_id) then    encoder:addi32(111, tb.dogtag_custom_id)    end
    if(tb.country_code) then    encoder:addi32(112, tb.country_code)    end
    if(tb.is_victory_unite_match) then    encoder:addbool(113, tb.is_victory_unite_match)    end
    if(tb.has_trigger_gold_egg) then    encoder:addbool(114, tb.has_trigger_gold_egg)    end
end

function pb.pb_Any2DsSyncPlayerInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsSyncPlayerInfoReq) or {} 
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    tb.player_info = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.player_info[k] = pb.pb_DsPlayerInfoDecode(v)
    end
    local __dsa_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    local __call_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __call_id ~= 0 then tb.call_id = __call_id end
    local __numeral_svr = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __numeral_svr ~= "" then tb.numeral_svr = __numeral_svr end
    return tb
end

function pb.pb_Any2DsSyncPlayerInfoReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.player_info) then
        for i=1,#(tb.player_info) do
            pb.pb_DsPlayerInfoEncode(tb.player_info[i], encoder:addsubmsg(1))
        end
    end
    if(tb.dsa_id) then    encoder:addstr(3, tb.dsa_id)    end
    if(tb.call_id) then    encoder:addu64(4, tb.call_id)    end
    if(tb.numeral_svr) then    encoder:addstr(5, tb.numeral_svr)    end
end

function pb.pb_Any2DsSyncPlayerInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsSyncPlayerInfoRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Any2DsSyncPlayerInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_Any2DsSyncTDMWeaponStoreReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsSyncTDMWeaponStoreReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.tdm_prop_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.tdm_prop_list[k] = pb.pb_TDMNumeralPropDecode(v)
    end
    local __seq = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __seq ~= 0 then tb.seq = __seq end
    local __total_seq = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_seq ~= 0 then tb.total_seq = __total_seq end
    tb.weapon_store_bytes = decoder:getstrary(6)
    tb.weapon_store = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.weapon_store[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_Any2DsSyncTDMWeaponStoreReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.tdm_prop_list) then
        for i=1,#(tb.tdm_prop_list) do
            pb.pb_TDMNumeralPropEncode(tb.tdm_prop_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.seq) then    encoder:addu32(4, tb.seq)    end
    if(tb.total_seq) then    encoder:addu32(5, tb.total_seq)    end
    if(tb.weapon_store_bytes) then    encoder:addbuffer(6, tb.weapon_store_bytes)    end
    if(tb.weapon_store) then
        for i=1,#(tb.weapon_store) do
            pb.pb_PropInfoEncode(tb.weapon_store[i], encoder:addsubmsg(7))
        end
    end
end

function pb.pb_Any2DsSyncTDMWeaponDesignReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsSyncTDMWeaponDesignReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __seq = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __seq ~= 0 then tb.seq = __seq end
    local __total_seq = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_seq ~= 0 then tb.total_seq = __total_seq end
    tb.weapon_designs_bytes = decoder:getstrary(5)
    tb.weapon_designs = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.weapon_designs[k] = pb.pb_TDMWeaponDesignDecode(v)
    end
    return tb
end

function pb.pb_Any2DsSyncTDMWeaponDesignReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.seq) then    encoder:addu32(3, tb.seq)    end
    if(tb.total_seq) then    encoder:addu32(4, tb.total_seq)    end
    if(tb.weapon_designs_bytes) then    encoder:addbuffer(5, tb.weapon_designs_bytes)    end
    if(tb.weapon_designs) then
        for i=1,#(tb.weapon_designs) do
            pb.pb_TDMWeaponDesignEncode(tb.weapon_designs[i], encoder:addsubmsg(6))
        end
    end
end

function pb.pb_Any2DsPlayerCsvTableReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsPlayerCsvTableReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __dsa_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    local __player_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.pkg = pb.pb_CsvZipPkgDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_Any2DsPlayerCsvTableReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.dsa_id) then    encoder:addstr(3, tb.dsa_id)    end
    if(tb.player_id) then    encoder:addu64(4, tb.player_id)    end
    if(tb.pkg) then    pb.pb_CsvZipPkgEncode(tb.pkg, encoder:addsubmsg(5))    end
end

function pb.pb_Ds2AnySyncPlayerInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnySyncPlayerInfoRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __call_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __call_id ~= 0 then tb.call_id = __call_id end
    local __room_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __numeral_svr = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __numeral_svr ~= "" then tb.numeral_svr = __numeral_svr end
    local __player_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __is_robot = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __is_robot ~= false then tb.is_robot = __is_robot end
    tb.player_addr_list = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.player_addr_list[k] = pb.pb_DsPlayerAddrDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnySyncPlayerInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.call_id) then    encoder:addu64(2, tb.call_id)    end
    if(tb.room_id) then    encoder:addu64(3, tb.room_id)    end
    if(tb.numeral_svr) then    encoder:addstr(4, tb.numeral_svr)    end
    if(tb.player_id) then    encoder:addu64(5, tb.player_id)    end
    if(tb.is_robot) then    encoder:addbool(6, tb.is_robot)    end
    if(tb.player_addr_list) then
        for i=1,#(tb.player_addr_list) do
            pb.pb_DsPlayerAddrEncode(tb.player_addr_list[i], encoder:addsubmsg(8))
        end
    end
end

function pb.pb_Any2DsPlayerAbandonMatchNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsPlayerAbandonMatchNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __reason = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __dsa_id = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    return tb
end

function pb.pb_Any2DsPlayerAbandonMatchNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.reason) then    encoder:addu32(3, tb.reason)    end
    if(tb.dsa_id) then    encoder:addstr(4, tb.dsa_id)    end
end

function pb.pb_Ds2AnyPlayerLoginDsNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyPlayerLoginDsNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    return tb
end

function pb.pb_Ds2AnyPlayerLoginDsNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
end

function pb.pb_Ds2AnyPlayerLogoutDsNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyPlayerLogoutDsNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __logout_reason = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __logout_reason ~= 0 then tb.logout_reason = __logout_reason end
    local __quit_team_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __quit_team_id ~= 0 then tb.quit_team_id = __quit_team_id end
    return tb
end

function pb.pb_Ds2AnyPlayerLogoutDsNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.logout_reason) then    encoder:addi32(3, tb.logout_reason)    end
    if(tb.quit_team_id) then    encoder:addu64(4, tb.quit_team_id)    end
end

function pb.pb_DsSimplePlayerInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsSimplePlayerInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __team_id = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __team_name = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __team_name ~= "" then tb.team_name = __team_name end
    local __player_idx = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __player_idx ~= 0 then tb.player_idx = __player_idx end
    local __camp_id = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __camp_id ~= 0 then tb.camp_id = __camp_id end
    return tb
end

function pb.pb_DsSimplePlayerInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.name) then    encoder:addstr(2, tb.name)    end
    if(tb.team_id) then    encoder:addi64(3, tb.team_id)    end
    if(tb.team_name) then    encoder:addstr(4, tb.team_name)    end
    if(tb.player_idx) then    encoder:addi64(5, tb.player_idx)    end
    if(tb.camp_id) then    encoder:addi64(6, tb.camp_id)    end
end

function pb.pb_Ds2AnyRoomInfoNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyRoomInfoNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __enable_half_join = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __enable_half_join ~= false then tb.enable_half_join = __enable_half_join end
    tb.player_infos = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.player_infos[k] = pb.pb_DsSimplePlayerInfoDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnyRoomInfoNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.enable_half_join) then    encoder:addbool(2, tb.enable_half_join)    end
    if(tb.player_infos) then
        for i=1,#(tb.player_infos) do
            pb.pb_DsSimplePlayerInfoEncode(tb.player_infos[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_DsFather2AnyLaunchNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsFather2AnyLaunchNtf) or {} 
    local __elapsed_time = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __elapsed_time ~= 0 then tb.elapsed_time = __elapsed_time end
    return tb
end

function pb.pb_DsFather2AnyLaunchNtfEncode(tb, encoder)
    if(tb.elapsed_time) then    encoder:addu32(1, tb.elapsed_time)    end
end

function pb.pb_Ds2AnyFramerateStatNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyFramerateStatNtf) or {} 
    local __total_frame = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_frame ~= 0 then tb.total_frame = __total_frame end
    local __total_time = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_time ~= 0 then tb.total_time = __total_time end
    local __total_cpu_time = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_cpu_time ~= 0 then tb.total_cpu_time = __total_cpu_time end
    local __max_cpu_time = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __max_cpu_time ~= 0 then tb.max_cpu_time = __max_cpu_time end
    local __fps_count_0_30 = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __fps_count_0_30 ~= 0 then tb.fps_count_0_30 = __fps_count_0_30 end
    local __fps_count_30_50 = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __fps_count_30_50 ~= 0 then tb.fps_count_30_50 = __fps_count_30_50 end
    local __fps_count_50_200 = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __fps_count_50_200 ~= 0 then tb.fps_count_50_200 = __fps_count_50_200 end
    local __fps_count_200_500 = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __fps_count_200_500 ~= 0 then tb.fps_count_200_500 = __fps_count_200_500 end
    local __fps_count_500_2000 = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __fps_count_500_2000 ~= 0 then tb.fps_count_500_2000 = __fps_count_500_2000 end
    local __fps_count_2000 = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __fps_count_2000 ~= 0 then tb.fps_count_2000 = __fps_count_2000 end
    return tb
end

function pb.pb_Ds2AnyFramerateStatNtfEncode(tb, encoder)
    if(tb.total_frame) then    encoder:addu32(1, tb.total_frame)    end
    if(tb.total_time) then    encoder:addu32(2, tb.total_time)    end
    if(tb.total_cpu_time) then    encoder:addu32(3, tb.total_cpu_time)    end
    if(tb.max_cpu_time) then    encoder:addu32(4, tb.max_cpu_time)    end
    if(tb.fps_count_0_30) then    encoder:addu32(5, tb.fps_count_0_30)    end
    if(tb.fps_count_30_50) then    encoder:addu32(6, tb.fps_count_30_50)    end
    if(tb.fps_count_50_200) then    encoder:addu32(7, tb.fps_count_50_200)    end
    if(tb.fps_count_200_500) then    encoder:addu32(8, tb.fps_count_200_500)    end
    if(tb.fps_count_500_2000) then    encoder:addu32(9, tb.fps_count_500_2000)    end
    if(tb.fps_count_2000) then    encoder:addu32(10, tb.fps_count_2000)    end
end

function pb.pb_Ds2AnyMatchInfoNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyMatchInfoNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_num = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __player_num ~= 0 then tb.player_num = __player_num end
    local __pmc_num = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __pmc_num ~= 0 then tb.pmc_num = __pmc_num end
    local __scav_num = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __scav_num ~= 0 then tb.scav_num = __scav_num end
    local __boss_killed = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __boss_killed ~= false then tb.boss_killed = __boss_killed end
    local __property = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __property ~= 0 then tb.property = __property end
    return tb
end

function pb.pb_Ds2AnyMatchInfoNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_num) then    encoder:addu32(3, tb.player_num)    end
    if(tb.pmc_num) then    encoder:addu32(4, tb.pmc_num)    end
    if(tb.scav_num) then    encoder:addu32(5, tb.scav_num)    end
    if(tb.boss_killed) then    encoder:addbool(2, tb.boss_killed)    end
    if(tb.property) then    encoder:addu64(6, tb.property)    end
end

function pb.pb_Ds2AnyLogTgLogNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyLogTgLogNtf) or {} 
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.entry_array = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.entry_array[k] = pb.pb_TgLogEntryDecode(v)
    end
    local __is_tss_log = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_tss_log ~= false then tb.is_tss_log = __is_tss_log end
    return tb
end

function pb.pb_Ds2AnyLogTgLogNtfEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.entry_array) then
        for i=1,#(tb.entry_array) do
            pb.pb_TgLogEntryEncode(tb.entry_array[i], encoder:addsubmsg(1))
        end
    end
    if(tb.is_tss_log) then    encoder:addbool(3, tb.is_tss_log)    end
end

function pb.pb_Ds2RawLogTglogNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2RawLogTglogNtf) or {} 
    tb.entry_array = decoder:getstrary(1)
    return tb
end

function pb.pb_Ds2RawLogTglogNtfEncode(tb, encoder)
    if(tb.entry_array) then    encoder:addstr(1, tb.entry_array)    end
end

function pb.pb_ReportMonitorLabelDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ReportMonitorLabel) or {} 
    local __label = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __label ~= "" then tb.label = __label end
    local __value = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= "" then tb.value = __value end
    return tb
end

function pb.pb_ReportMonitorLabelEncode(tb, encoder)
    if(tb.label) then    encoder:addstr(1, tb.label)    end
    if(tb.value) then    encoder:addstr(2, tb.value)    end
end

function pb.pb_ReportMonitorMetricDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ReportMonitorMetric) or {} 
    local __id = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= "" then tb.id = __id end
    local __type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __val = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __val ~= 0 then tb.val = __val end
    local __inc = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __inc ~= 0 then tb.inc = __inc end
    local __emergency = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __emergency ~= "" then tb.emergency = __emergency end
    tb.labels = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.labels[k] = pb.pb_ReportMonitorLabelDecode(v)
    end
    return tb
end

function pb.pb_ReportMonitorMetricEncode(tb, encoder)
    if(tb.id) then    encoder:addstr(1, tb.id)    end
    if(tb.type) then    encoder:addi32(2, tb.type)    end
    if(tb.val) then    encoder:addi64(3, tb.val)    end
    if(tb.inc) then    encoder:addi32(4, tb.inc)    end
    if(tb.emergency) then    encoder:addstr(5, tb.emergency)    end
    if(tb.labels) then
        for i=1,#(tb.labels) do
            pb.pb_ReportMonitorLabelEncode(tb.labels[i], encoder:addsubmsg(6))
        end
    end
end

function pb.pb_DS2AnyReportMonitorNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DS2AnyReportMonitorNtf) or {} 
    tb.metrics = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.metrics[k] = pb.pb_ReportMonitorMetricDecode(v)
    end
    return tb
end

function pb.pb_DS2AnyReportMonitorNtfEncode(tb, encoder)
    if(tb.metrics) then
        for i=1,#(tb.metrics) do
            pb.pb_ReportMonitorMetricEncode(tb.metrics[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_Any2DsagentSendToZoneReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsagentSendToZoneReq) or {} 
    local __service_name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __service_name ~= "" then tb.service_name = __service_name end
    local __service_id = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __service_id ~= "" then tb.service_id = __service_id end
    local __name = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __payload = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __payload ~= "" then tb.payload = __payload end
    local __room_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_Any2DsagentSendToZoneReqEncode(tb, encoder)
    if(tb.service_name) then    encoder:addstr(1, tb.service_name)    end
    if(tb.service_id) then    encoder:addstr(2, tb.service_id)    end
    if(tb.name) then    encoder:addstr(3, tb.name)    end
    if(tb.payload) then    encoder:addbuffer(4, tb.payload)    end
    if(tb.room_id) then    encoder:addu64(5, tb.room_id)    end
end

function pb.pb_Dsagent2AnySendToZoneResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Dsagent2AnySendToZoneRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Dsagent2AnySendToZoneResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_Ds2AnyMatchEndNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyMatchEndNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __reason = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    tb.not_settlement_players = decoder:getu64ary(3)
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(4))
    local __MatchType = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __MatchType ~= 0 then tb.MatchType = __MatchType end
    return tb
end

function pb.pb_Ds2AnyMatchEndNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.reason) then    encoder:addi32(2, tb.reason)    end
    if(tb.not_settlement_players) then    encoder:addu64(3, tb.not_settlement_players)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(4))    end
    if(tb.MatchType) then    encoder:addu32(5, tb.MatchType)    end
end

function pb.pb_Ds2AnyTkvSettlementReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyTkvSettlementReq) or {} 
    local __player_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.match_info = pb.pb_DsMatchInfoNewDecode(decoder:getsubmsg(1))
    tb.team_info = pb.pb_DsTeamInfoDecode(decoder:getsubmsg(2))
    tb.status_array = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.status_array[k] = pb.pb_DsPlayerGameStatusDecode(v)
    end
    tb.player_quests = pb.pb_DsQuestDataDecode(decoder:getsubmsg(5))
    tb.marked_props = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.marked_props[k] = pb.pb_MarkPropInfoDecode(v)
    end
    local __match_tactics = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __match_tactics ~= 0 then tb.match_tactics = __match_tactics end
    local __dps = decoder:geti64(100)
    if not PB_USE_DEFAULT_TABLE or __dps ~= 0 then tb.dps = __dps end
    local __last_time_props_price = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __last_time_props_price ~= 0 then tb.last_time_props_price = __last_time_props_price end
    local __loot_total_price = decoder:geti64(102)
    if not PB_USE_DEFAULT_TABLE or __loot_total_price ~= 0 then tb.loot_total_price = __loot_total_price end
    local __loot_expected_price = decoder:geti64(103)
    if not PB_USE_DEFAULT_TABLE or __loot_expected_price ~= 0 then tb.loot_expected_price = __loot_expected_price end
    local __contract_num = decoder:geti32(104)
    if not PB_USE_DEFAULT_TABLE or __contract_num ~= 0 then tb.contract_num = __contract_num end
    local __fighting_with_player_num = decoder:geti32(105)
    if not PB_USE_DEFAULT_TABLE or __fighting_with_player_num ~= 0 then tb.fighting_with_player_num = __fighting_with_player_num end
    local __fighting_with_ai_num = decoder:geti32(106)
    if not PB_USE_DEFAULT_TABLE or __fighting_with_ai_num ~= 0 then tb.fighting_with_ai_num = __fighting_with_ai_num end
    return tb
end

function pb.pb_Ds2AnyTkvSettlementReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(4, tb.player_id)    end
    if(tb.match_info) then    pb.pb_DsMatchInfoNewEncode(tb.match_info, encoder:addsubmsg(1))    end
    if(tb.team_info) then    pb.pb_DsTeamInfoEncode(tb.team_info, encoder:addsubmsg(2))    end
    if(tb.status_array) then
        for i=1,#(tb.status_array) do
            pb.pb_DsPlayerGameStatusEncode(tb.status_array[i], encoder:addsubmsg(3))
        end
    end
    if(tb.player_quests) then    pb.pb_DsQuestDataEncode(tb.player_quests, encoder:addsubmsg(5))    end
    if(tb.marked_props) then
        for i=1,#(tb.marked_props) do
            pb.pb_MarkPropInfoEncode(tb.marked_props[i], encoder:addsubmsg(6))
        end
    end
    if(tb.match_tactics) then    encoder:addu32(7, tb.match_tactics)    end
    if(tb.dps) then    encoder:addi64(100, tb.dps)    end
    if(tb.last_time_props_price) then    encoder:addi64(101, tb.last_time_props_price)    end
    if(tb.loot_total_price) then    encoder:addi64(102, tb.loot_total_price)    end
    if(tb.loot_expected_price) then    encoder:addi64(103, tb.loot_expected_price)    end
    if(tb.contract_num) then    encoder:addi32(104, tb.contract_num)    end
    if(tb.fighting_with_player_num) then    encoder:addi32(105, tb.fighting_with_player_num)    end
    if(tb.fighting_with_ai_num) then    encoder:addi32(106, tb.fighting_with_ai_num)    end
end

function pb.pb_Any2DsTkvSettlementResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsTkvSettlementRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Any2DsTkvSettlementResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_Ds2AnyPVESettlementReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyPVESettlementReq) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.mission_info = pb.pb_DsPVEMissionInfoDecode(decoder:getsubmsg(2))
    tb.status_array = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.status_array[k] = pb.pb_DsPVEPlayerStatusDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnyPVESettlementReqEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.mission_info) then    pb.pb_DsPVEMissionInfoEncode(tb.mission_info, encoder:addsubmsg(2))    end
    if(tb.status_array) then
        for i=1,#(tb.status_array) do
            pb.pb_DsPVEPlayerStatusEncode(tb.status_array[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_Any2DsPVESettlementResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsPVESettlementRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Any2DsPVESettlementResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_Ds2AnyPlayerSettlementReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyPlayerSettlementReq) or {} 
    local __match_result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __match_result ~= 0 then tb.match_result = __match_result end
    local __outfit_price = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __outfit_price ~= 0 then tb.outfit_price = __outfit_price end
    tb.outfit = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.outfit[k] = pb.pb_EquipPositionDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnyPlayerSettlementReqEncode(tb, encoder)
    if(tb.match_result) then    encoder:addi32(1, tb.match_result)    end
    if(tb.outfit_price) then    encoder:adddouble(2, tb.outfit_price)    end
    if(tb.outfit) then
        for i=1,#(tb.outfit) do
            pb.pb_EquipPositionEncode(tb.outfit[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_Any2DsPlayerSettlementResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsPlayerSettlementRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Any2DsPlayerSettlementResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_PlayerLostItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerLostItems) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __status = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __status ~= 0 then tb.status = __status end
    local __result = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.item_array = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.item_array[k] = pb.pb_PropInfoDecode(v)
    end
    tb.looting_items_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.looting_items_array[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_PlayerLostItemsEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.status) then    encoder:addi32(2, tb.status)    end
    if(tb.result) then    encoder:addi32(3, tb.result)    end
    if(tb.item_array) then
        for i=1,#(tb.item_array) do
            pb.pb_PropInfoEncode(tb.item_array[i], encoder:addsubmsg(4))
        end
    end
    if(tb.looting_items_array) then
        for i=1,#(tb.looting_items_array) do
            pb.pb_PropInfoEncode(tb.looting_items_array[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_Ds2AnyMatchSettlementReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyMatchSettlementReq) or {} 
    local __match_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __match_id ~= 0 then tb.match_id = __match_id end
    local __map_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __match_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_subtype = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __game_mode = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __game_submode = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __game_submode ~= 0 then tb.game_submode = __game_submode end
    local __end_time = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    tb.lost_items_array = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.lost_items_array[k] = pb.pb_PlayerLostItemsDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnyMatchSettlementReqEncode(tb, encoder)
    if(tb.match_id) then    encoder:addu64(1, tb.match_id)    end
    if(tb.map_id) then    encoder:addu64(2, tb.map_id)    end
    if(tb.match_type) then    encoder:addi32(3, tb.match_type)    end
    if(tb.match_subtype) then    encoder:addi32(4, tb.match_subtype)    end
    if(tb.game_mode) then    encoder:addi32(5, tb.game_mode)    end
    if(tb.game_submode) then    encoder:addi32(6, tb.game_submode)    end
    if(tb.end_time) then    encoder:addu64(7, tb.end_time)    end
    if(tb.lost_items_array) then
        for i=1,#(tb.lost_items_array) do
            pb.pb_PlayerLostItemsEncode(tb.lost_items_array[i], encoder:addsubmsg(8))
        end
    end
end

function pb.pb_Any2DsMatchSettlementResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsMatchSettlementRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Any2DsMatchSettlementResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_Any2DsBeginRaidReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsBeginRaidReq) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __raid_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __raid_id ~= 0 then tb.raid_id = __raid_id end
    tb.player_array = decoder:getu64ary(3)
    return tb
end

function pb.pb_Any2DsBeginRaidReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.raid_id) then    encoder:addu64(2, tb.raid_id)    end
    if(tb.player_array) then    encoder:addu64(3, tb.player_array)    end
end

function pb.pb_Ds2AnyBeginRaidResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyBeginRaidRes) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __raid_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __raid_id ~= 0 then tb.raid_id = __raid_id end
    local __result = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_Ds2AnyBeginRaidResEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.raid_id) then    encoder:addu64(2, tb.raid_id)    end
    if(tb.result) then    encoder:addi32(3, tb.result)    end
end

function pb.pb_Any2DsCanHalfJoinReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsCanHalfJoinReq) or {} 
    local __dsa_id = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    tb.player_ids = decoder:getu64ary(3)
    local __camp = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __camp ~= 0 then tb.camp = __camp end
    local __team_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __caller = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __caller ~= "" then tb.caller = __caller end
    return tb
end

function pb.pb_Any2DsCanHalfJoinReqEncode(tb, encoder)
    if(tb.dsa_id) then    encoder:addstr(1, tb.dsa_id)    end
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.player_ids) then    encoder:addu64(3, tb.player_ids)    end
    if(tb.camp) then    encoder:addu32(4, tb.camp)    end
    if(tb.team_id) then    encoder:addu32(5, tb.team_id)    end
    if(tb.caller) then    encoder:addstr(6, tb.caller)    end
end

function pb.pb_Any2DsCanHalfJoinResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsCanHalfJoinRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __can_half_join = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __can_half_join ~= false then tb.can_half_join = __can_half_join end
    return tb
end

function pb.pb_Any2DsCanHalfJoinResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.can_half_join) then    encoder:addbool(2, tb.can_half_join)    end
end

function pb.pb_CSEmptySettlementNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSEmptySettlementNtf) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __mode = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    local __room_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSEmptySettlementNtfEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.mode) then    encoder:addu32(2, tb.mode)    end
    if(tb.room_id) then    encoder:addu64(3, tb.room_id)    end
end

function pb.pb_CSSettlementEmptyNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSSettlementEmptyNtf) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __mode = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    local __room_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    return tb
end

function pb.pb_CSSettlementEmptyNtfEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.mode) then    encoder:addu32(2, tb.mode)    end
    if(tb.room_id) then    encoder:addu64(3, tb.room_id)    end
end

function pb.pb_DsFatherExitNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsFatherExitNtf) or {} 
    local __pid = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __pid ~= 0 then tb.pid = __pid end
    local __status = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __status ~= 0 then tb.status = __status end
    local __reason = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    return tb
end

function pb.pb_DsFatherExitNtfEncode(tb, encoder)
    if(tb.pid) then    encoder:addu32(1, tb.pid)    end
    if(tb.status) then    encoder:addu32(2, tb.status)    end
    if(tb.reason) then    encoder:addu32(3, tb.reason)    end
end

function pb.pb_Any2DsPropGuidePriceNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsPropGuidePriceNtf) or {} 
    tb.price_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.price_list[k] = pb.pb_PropGuidePriceDecode(v)
    end
    return tb
end

function pb.pb_Any2DsPropGuidePriceNtfEncode(tb, encoder)
    if(tb.price_list) then
        for i=1,#(tb.price_list) do
            pb.pb_PropGuidePriceEncode(tb.price_list[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_DsSafeHousePlayerInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsSafeHousePlayerInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.equiped_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.equiped_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.heros = pb.pb_DSHeroDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_DsSafeHousePlayerInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.equiped_props) then
        for i=1,#(tb.equiped_props) do
            pb.pb_EquipPositionEncode(tb.equiped_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.heros) then    pb.pb_DSHeroEncode(tb.heros, encoder:addsubmsg(3))    end
end

function pb.pb_Any2DsSyncSafeHousePlayerInfoReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsSyncSafeHousePlayerInfoReq) or {} 
    local __room_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    tb.player_info = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.player_info[k] = pb.pb_DsSafeHousePlayerInfoDecode(v)
    end
    local __dsa_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    local __call_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __call_id ~= 0 then tb.call_id = __call_id end
    local __call_svr = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __call_svr ~= "" then tb.call_svr = __call_svr end
    return tb
end

function pb.pb_Any2DsSyncSafeHousePlayerInfoReqEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(2, tb.room_id)    end
    if(tb.player_info) then
        for i=1,#(tb.player_info) do
            pb.pb_DsSafeHousePlayerInfoEncode(tb.player_info[i], encoder:addsubmsg(1))
        end
    end
    if(tb.dsa_id) then    encoder:addstr(3, tb.dsa_id)    end
    if(tb.call_id) then    encoder:addu64(4, tb.call_id)    end
    if(tb.call_svr) then    encoder:addstr(5, tb.call_svr)    end
end

function pb.pb_Ds2AnySyncSafeHousePlayerInfoResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnySyncSafeHousePlayerInfoRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __call_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __call_id ~= 0 then tb.call_id = __call_id end
    local __room_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __call_svr = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __call_svr ~= "" then tb.call_svr = __call_svr end
    return tb
end

function pb.pb_Ds2AnySyncSafeHousePlayerInfoResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.call_id) then    encoder:addu64(2, tb.call_id)    end
    if(tb.room_id) then    encoder:addu64(3, tb.room_id)    end
    if(tb.call_svr) then    encoder:addstr(4, tb.call_svr)    end
end

function pb.pb_Any2DsPlayerLeaveSafeHouseNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Any2DsPlayerLeaveSafeHouseNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __reason = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __dsa_id = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    return tb
end

function pb.pb_Any2DsPlayerLeaveSafeHouseNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.reason) then    encoder:addu32(3, tb.reason)    end
    if(tb.dsa_id) then    encoder:addstr(4, tb.dsa_id)    end
end

function pb.pb_Ds2AnyDeathAndHurtDetailInfoNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyDeathAndHurtDetailInfoNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.death_detail_info = pb.pb_DsDeathDetailedInfoDecode(decoder:getsubmsg(3))
    tb.hurt_detail_info = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.hurt_detail_info[k] = pb.pb_DsHurtDetailedInfoDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnyDeathAndHurtDetailInfoNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.death_detail_info) then    pb.pb_DsDeathDetailedInfoEncode(tb.death_detail_info, encoder:addsubmsg(3))    end
    if(tb.hurt_detail_info) then
        for i=1,#(tb.hurt_detail_info) do
            pb.pb_DsHurtDetailedInfoEncode(tb.hurt_detail_info[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_Ds2AnyRoomStatisticsNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyRoomStatisticsNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.match_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(5))
    tb.sol_guide_info = pb.pb_SOLGuideInstructionInfoDecode(decoder:getsubmsg(3))
    tb.drop_counters = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.drop_counters[k] = pb.pb_MatchSOLDropCounterDecode(v)
    end
    local __drop_counters_cnt = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __drop_counters_cnt ~= 0 then tb.drop_counters_cnt = __drop_counters_cnt end
    tb.key_chain_usage_info = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.key_chain_usage_info[k] = pb.pb_KeyChainUsageInfoDecode(v)
    end
    return tb
end

function pb.pb_Ds2AnyRoomStatisticsNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.match_info) then    pb.pb_MatchModeInfoEncode(tb.match_info, encoder:addsubmsg(5))    end
    if(tb.sol_guide_info) then    pb.pb_SOLGuideInstructionInfoEncode(tb.sol_guide_info, encoder:addsubmsg(3))    end
    if(tb.drop_counters) then
        for i=1,#(tb.drop_counters) do
            pb.pb_MatchSOLDropCounterEncode(tb.drop_counters[i], encoder:addsubmsg(4))
        end
    end
    if(tb.drop_counters_cnt) then    encoder:addi64(6, tb.drop_counters_cnt)    end
    if(tb.key_chain_usage_info) then
        for i=1,#(tb.key_chain_usage_info) do
            pb.pb_KeyChainUsageInfoEncode(tb.key_chain_usage_info[i], encoder:addsubmsg(7))
        end
    end
end

function pb.pb_KeyChainUsageInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_KeyChainUsageInfo) or {} 
    local __item_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __use_durability = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __use_durability ~= 0 then tb.use_durability = __use_durability end
    return tb
end

function pb.pb_KeyChainUsageInfoEncode(tb, encoder)
    if(tb.item_id) then    encoder:addu64(1, tb.item_id)    end
    if(tb.use_durability) then    encoder:addu64(2, tb.use_durability)    end
end

function pb.pb_Ds2RoomRecommendFriendNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2RoomRecommendFriendNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.rescue_info = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.rescue_info[k] = pb.pb_RescueInfoDecode(v)
    end
    tb.help_teammate_carry_out_info = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.help_teammate_carry_out_info[k] = pb.pb_HelpTeammateCarryOutInfoDecode(v)
    end
    tb.kill_assistant_info = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.kill_assistant_info[k] = pb.pb_KillAssistantInfoDecode(v)
    end
    tb.drop_pickup_item_info = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.drop_pickup_item_info[k] = pb.pb_DropPickupItemInfoDecode(v)
    end
    local __escape_time_point = decoder:getdouble(7)
    if not PB_USE_DEFAULT_TABLE or __escape_time_point ~= 0 then tb.escape_time_point = __escape_time_point end
    local __extraction_location = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __extraction_location ~= "" then tb.extraction_location = __extraction_location end
    return tb
end

function pb.pb_Ds2RoomRecommendFriendNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.rescue_info) then
        for i=1,#(tb.rescue_info) do
            pb.pb_RescueInfoEncode(tb.rescue_info[i], encoder:addsubmsg(3))
        end
    end
    if(tb.help_teammate_carry_out_info) then
        for i=1,#(tb.help_teammate_carry_out_info) do
            pb.pb_HelpTeammateCarryOutInfoEncode(tb.help_teammate_carry_out_info[i], encoder:addsubmsg(4))
        end
    end
    if(tb.kill_assistant_info) then
        for i=1,#(tb.kill_assistant_info) do
            pb.pb_KillAssistantInfoEncode(tb.kill_assistant_info[i], encoder:addsubmsg(5))
        end
    end
    if(tb.drop_pickup_item_info) then
        for i=1,#(tb.drop_pickup_item_info) do
            pb.pb_DropPickupItemInfoEncode(tb.drop_pickup_item_info[i], encoder:addsubmsg(6))
        end
    end
    if(tb.escape_time_point) then    encoder:adddouble(7, tb.escape_time_point)    end
    if(tb.extraction_location) then    encoder:addstr(8, tb.extraction_location)    end
end

function pb.pb_DsSeasonMatchSettlementAINtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsSeasonMatchSettlementAINtf) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __nickname = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __nickname ~= "" then tb.nickname = __nickname end
    local __room_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __team_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __team_rank = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __team_rank ~= 0 then tb.team_rank = __team_rank end
    local __start_time = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    tb.team_members = decoder:getu64ary(7)
    tb.record = pb.pb_DsMatchBaseRecordDecode(decoder:getsubmsg(8))
    local __plat_id = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    return tb
end

function pb.pb_DsSeasonMatchSettlementAINtfEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.nickname) then    encoder:addstr(2, tb.nickname)    end
    if(tb.room_id) then    encoder:addu64(3, tb.room_id)    end
    if(tb.team_id) then    encoder:addu32(4, tb.team_id)    end
    if(tb.team_rank) then    encoder:addu32(5, tb.team_rank)    end
    if(tb.start_time) then    encoder:addu64(6, tb.start_time)    end
    if(tb.team_members) then    encoder:addu64(7, tb.team_members)    end
    if(tb.record) then    pb.pb_DsMatchBaseRecordEncode(tb.record, encoder:addsubmsg(8))    end
    if(tb.plat_id) then    encoder:addi32(10, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(11, tb.account_type)    end
end

function pb.pb_DSMPLabAILogNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DSMPLabAILogNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __game_duration = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __game_duration ~= 0 then tb.game_duration = __game_duration end
    local __kill_cnt = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __kill_cnt ~= 0 then tb.kill_cnt = __kill_cnt end
    local __kill_player_cnt = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __kill_player_cnt ~= 0 then tb.kill_player_cnt = __kill_player_cnt end
    local __kill_player_ai_cnt = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __kill_player_ai_cnt ~= 0 then tb.kill_player_ai_cnt = __kill_player_ai_cnt end
    local __kill_ai_cnt = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_cnt ~= 0 then tb.kill_ai_cnt = __kill_ai_cnt end
    local __dead_cnt = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __dead_cnt ~= 0 then tb.dead_cnt = __dead_cnt end
    local __mp_level = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __mp_level ~= 0 then tb.mp_level = __mp_level end
    local __rescue_cnt = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __rescue_cnt ~= 0 then tb.rescue_cnt = __rescue_cnt end
    local __damage_dealt = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __damage_dealt ~= 0 then tb.damage_dealt = __damage_dealt end
    local __be_headshot_cnt = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __be_headshot_cnt ~= 0 then tb.be_headshot_cnt = __be_headshot_cnt end
    local __assist_cnt = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __assist_cnt ~= 0 then tb.assist_cnt = __assist_cnt end
    local __breatheheal_times = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __breatheheal_times ~= 0 then tb.breatheheal_times = __breatheheal_times end
    local __breatheheal_amount = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __breatheheal_amount ~= 0 then tb.breatheheal_amount = __breatheheal_amount end
    local __total_mileage = decoder:geti32(16)
    if not PB_USE_DEFAULT_TABLE or __total_mileage ~= 0 then tb.total_mileage = __total_mileage end
    local __headshot_cnt = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __headshot_cnt ~= 0 then tb.headshot_cnt = __headshot_cnt end
    local __kill_cnt_nocarrier = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __kill_cnt_nocarrier ~= 0 then tb.kill_cnt_nocarrier = __kill_cnt_nocarrier end
    local __kill_cnt_withcarrier = decoder:geti32(19)
    if not PB_USE_DEFAULT_TABLE or __kill_cnt_withcarrier ~= 0 then tb.kill_cnt_withcarrier = __kill_cnt_withcarrier end
    local __kill_player_nocarrier = decoder:geti32(20)
    if not PB_USE_DEFAULT_TABLE or __kill_player_nocarrier ~= 0 then tb.kill_player_nocarrier = __kill_player_nocarrier end
    local __kill_player_withcarrier = decoder:geti32(21)
    if not PB_USE_DEFAULT_TABLE or __kill_player_withcarrier ~= 0 then tb.kill_player_withcarrier = __kill_player_withcarrier end
    local __kill_ai_nocarrier = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_nocarrier ~= 0 then tb.kill_ai_nocarrier = __kill_ai_nocarrier end
    local __kill_ai_withcarrier = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_withcarrier ~= 0 then tb.kill_ai_withcarrier = __kill_ai_withcarrier end
    local __kill_player_ai_nocarrier = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __kill_player_ai_nocarrier ~= 0 then tb.kill_player_ai_nocarrier = __kill_player_ai_nocarrier end
    local __kill_player_ai_withcarrier = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __kill_player_ai_withcarrier ~= 0 then tb.kill_player_ai_withcarrier = __kill_player_ai_withcarrier end
    local __lives = decoder:geti32(26)
    if not PB_USE_DEFAULT_TABLE or __lives ~= 0 then tb.lives = __lives end
    local __skill_use_cnt = decoder:getstr(27)
    if not PB_USE_DEFAULT_TABLE or __skill_use_cnt ~= "" then tb.skill_use_cnt = __skill_use_cnt end
    local __skill_kill_player_cnt = decoder:getstr(28)
    if not PB_USE_DEFAULT_TABLE or __skill_kill_player_cnt ~= "" then tb.skill_kill_player_cnt = __skill_kill_player_cnt end
    local __near_kill_cnt = decoder:geti32(29)
    if not PB_USE_DEFAULT_TABLE or __near_kill_cnt ~= 0 then tb.near_kill_cnt = __near_kill_cnt end
    local __medium_kill_cnt = decoder:geti32(30)
    if not PB_USE_DEFAULT_TABLE or __medium_kill_cnt ~= 0 then tb.medium_kill_cnt = __medium_kill_cnt end
    local __far_kill_cnt = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __far_kill_cnt ~= 0 then tb.far_kill_cnt = __far_kill_cnt end
    local __mp_break_count = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __mp_break_count ~= 0 then tb.mp_break_count = __mp_break_count end
    local __occupy = decoder:geti32(33)
    if not PB_USE_DEFAULT_TABLE or __occupy ~= 0 then tb.occupy = __occupy end
    local __mp_ammo_box_use_count = decoder:geti32(34)
    if not PB_USE_DEFAULT_TABLE or __mp_ammo_box_use_count ~= 0 then tb.mp_ammo_box_use_count = __mp_ammo_box_use_count end
    local __mp_repari_count = decoder:geti32(35)
    if not PB_USE_DEFAULT_TABLE or __mp_repari_count ~= 0 then tb.mp_repari_count = __mp_repari_count end
    local __mvp = decoder:getbool(36)
    if not PB_USE_DEFAULT_TABLE or __mvp ~= false then tb.mvp = __mvp end
    local __score_rank = decoder:geti32(37)
    if not PB_USE_DEFAULT_TABLE or __score_rank ~= 0 then tb.score_rank = __score_rank end
    local __scores = decoder:getstr(38)
    if not PB_USE_DEFAULT_TABLE or __scores ~= "" then tb.scores = __scores end
    local __is_win = decoder:getbool(39)
    if not PB_USE_DEFAULT_TABLE or __is_win ~= false then tb.is_win = __is_win end
    local __game_mode = decoder:geti32(40)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __map_id = decoder:geti32(41)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __match_type = decoder:geti32(42)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __team_num = decoder:geti32(43)
    if not PB_USE_DEFAULT_TABLE or __team_num ~= 0 then tb.team_num = __team_num end
    local __max_playernum = decoder:geti32(44)
    if not PB_USE_DEFAULT_TABLE or __max_playernum ~= 0 then tb.max_playernum = __max_playernum end
    local __campid = decoder:geti32(45)
    if not PB_USE_DEFAULT_TABLE or __campid ~= 0 then tb.campid = __campid end
    local __hidden_score = decoder:geti32(46)
    if not PB_USE_DEFAULT_TABLE or __hidden_score ~= 0 then tb.hidden_score = __hidden_score end
    local __match_score = decoder:geti32(47)
    if not PB_USE_DEFAULT_TABLE or __match_score ~= 0 then tb.match_score = __match_score end
    local __rank_match_score = decoder:geti32(48)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __match_use_time = decoder:geti32(49)
    if not PB_USE_DEFAULT_TABLE or __match_use_time ~= 0 then tb.match_use_time = __match_use_time end
    local __pre_team_friend_num = decoder:geti32(50)
    if not PB_USE_DEFAULT_TABLE or __pre_team_friend_num ~= 0 then tb.pre_team_friend_num = __pre_team_friend_num end
    local __team_player_num = decoder:geti32(51)
    if not PB_USE_DEFAULT_TABLE or __team_player_num ~= 0 then tb.team_player_num = __team_player_num end
    local __max_camp_real_player_num = decoder:geti32(52)
    if not PB_USE_DEFAULT_TABLE or __max_camp_real_player_num ~= 0 then tb.max_camp_real_player_num = __max_camp_real_player_num end
    local __round_type = decoder:geti32(53)
    if not PB_USE_DEFAULT_TABLE or __round_type ~= 0 then tb.round_type = __round_type end
    local __early_quit_flag = decoder:geti32(54)
    if not PB_USE_DEFAULT_TABLE or __early_quit_flag ~= 0 then tb.early_quit_flag = __early_quit_flag end
    local __shot_cnt = decoder:geti32(55)
    if not PB_USE_DEFAULT_TABLE or __shot_cnt ~= 0 then tb.shot_cnt = __shot_cnt end
    local __hit_target_cnt = decoder:geti32(56)
    if not PB_USE_DEFAULT_TABLE or __hit_target_cnt ~= 0 then tb.hit_target_cnt = __hit_target_cnt end
    local __ai_level = decoder:getstr(57)
    if not PB_USE_DEFAULT_TABLE or __ai_level ~= "" then tb.ai_level = __ai_level end
    local __total_player_real_num = decoder:geti32(58)
    if not PB_USE_DEFAULT_TABLE or __total_player_real_num ~= 0 then tb.total_player_real_num = __total_player_real_num end
    local __ailab_total_num = decoder:geti32(59)
    if not PB_USE_DEFAULT_TABLE or __ailab_total_num ~= 0 then tb.ailab_total_num = __ailab_total_num end
    local __total_human_team_num = decoder:geti32(60)
    if not PB_USE_DEFAULT_TABLE or __total_human_team_num ~= 0 then tb.total_human_team_num = __total_human_team_num end
    local __scheme_pool_id = decoder:geti32(61)
    if not PB_USE_DEFAULT_TABLE or __scheme_pool_id ~= 0 then tb.scheme_pool_id = __scheme_pool_id end
    local __armed_force_id = decoder:geti32(62)
    if not PB_USE_DEFAULT_TABLE or __armed_force_id ~= 0 then tb.armed_force_id = __armed_force_id end
    return tb
end

function pb.pb_DSMPLabAILogNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.game_duration) then    encoder:addfloat(3, tb.game_duration)    end
    if(tb.kill_cnt) then    encoder:addi32(4, tb.kill_cnt)    end
    if(tb.kill_player_cnt) then    encoder:addi32(5, tb.kill_player_cnt)    end
    if(tb.kill_player_ai_cnt) then    encoder:addi32(6, tb.kill_player_ai_cnt)    end
    if(tb.kill_ai_cnt) then    encoder:addi32(7, tb.kill_ai_cnt)    end
    if(tb.dead_cnt) then    encoder:addi32(8, tb.dead_cnt)    end
    if(tb.mp_level) then    encoder:addi32(9, tb.mp_level)    end
    if(tb.rescue_cnt) then    encoder:addi32(10, tb.rescue_cnt)    end
    if(tb.damage_dealt) then    encoder:addi32(11, tb.damage_dealt)    end
    if(tb.be_headshot_cnt) then    encoder:addi32(12, tb.be_headshot_cnt)    end
    if(tb.assist_cnt) then    encoder:addi32(13, tb.assist_cnt)    end
    if(tb.breatheheal_times) then    encoder:addi32(14, tb.breatheheal_times)    end
    if(tb.breatheheal_amount) then    encoder:addi32(15, tb.breatheheal_amount)    end
    if(tb.total_mileage) then    encoder:addi32(16, tb.total_mileage)    end
    if(tb.headshot_cnt) then    encoder:addi32(17, tb.headshot_cnt)    end
    if(tb.kill_cnt_nocarrier) then    encoder:addi32(18, tb.kill_cnt_nocarrier)    end
    if(tb.kill_cnt_withcarrier) then    encoder:addi32(19, tb.kill_cnt_withcarrier)    end
    if(tb.kill_player_nocarrier) then    encoder:addi32(20, tb.kill_player_nocarrier)    end
    if(tb.kill_player_withcarrier) then    encoder:addi32(21, tb.kill_player_withcarrier)    end
    if(tb.kill_ai_nocarrier) then    encoder:addi32(22, tb.kill_ai_nocarrier)    end
    if(tb.kill_ai_withcarrier) then    encoder:addi32(23, tb.kill_ai_withcarrier)    end
    if(tb.kill_player_ai_nocarrier) then    encoder:addi32(24, tb.kill_player_ai_nocarrier)    end
    if(tb.kill_player_ai_withcarrier) then    encoder:addi32(25, tb.kill_player_ai_withcarrier)    end
    if(tb.lives) then    encoder:addi32(26, tb.lives)    end
    if(tb.skill_use_cnt) then    encoder:addstr(27, tb.skill_use_cnt)    end
    if(tb.skill_kill_player_cnt) then    encoder:addstr(28, tb.skill_kill_player_cnt)    end
    if(tb.near_kill_cnt) then    encoder:addi32(29, tb.near_kill_cnt)    end
    if(tb.medium_kill_cnt) then    encoder:addi32(30, tb.medium_kill_cnt)    end
    if(tb.far_kill_cnt) then    encoder:addi32(31, tb.far_kill_cnt)    end
    if(tb.mp_break_count) then    encoder:addi32(32, tb.mp_break_count)    end
    if(tb.occupy) then    encoder:addi32(33, tb.occupy)    end
    if(tb.mp_ammo_box_use_count) then    encoder:addi32(34, tb.mp_ammo_box_use_count)    end
    if(tb.mp_repari_count) then    encoder:addi32(35, tb.mp_repari_count)    end
    if(tb.mvp) then    encoder:addbool(36, tb.mvp)    end
    if(tb.score_rank) then    encoder:addi32(37, tb.score_rank)    end
    if(tb.scores) then    encoder:addstr(38, tb.scores)    end
    if(tb.is_win) then    encoder:addbool(39, tb.is_win)    end
    if(tb.game_mode) then    encoder:addi32(40, tb.game_mode)    end
    if(tb.map_id) then    encoder:addi32(41, tb.map_id)    end
    if(tb.match_type) then    encoder:addi32(42, tb.match_type)    end
    if(tb.team_num) then    encoder:addi32(43, tb.team_num)    end
    if(tb.max_playernum) then    encoder:addi32(44, tb.max_playernum)    end
    if(tb.campid) then    encoder:addi32(45, tb.campid)    end
    if(tb.hidden_score) then    encoder:addi32(46, tb.hidden_score)    end
    if(tb.match_score) then    encoder:addi32(47, tb.match_score)    end
    if(tb.rank_match_score) then    encoder:addi32(48, tb.rank_match_score)    end
    if(tb.match_use_time) then    encoder:addi32(49, tb.match_use_time)    end
    if(tb.pre_team_friend_num) then    encoder:addi32(50, tb.pre_team_friend_num)    end
    if(tb.team_player_num) then    encoder:addi32(51, tb.team_player_num)    end
    if(tb.max_camp_real_player_num) then    encoder:addi32(52, tb.max_camp_real_player_num)    end
    if(tb.round_type) then    encoder:addi32(53, tb.round_type)    end
    if(tb.early_quit_flag) then    encoder:addi32(54, tb.early_quit_flag)    end
    if(tb.shot_cnt) then    encoder:addi32(55, tb.shot_cnt)    end
    if(tb.hit_target_cnt) then    encoder:addi32(56, tb.hit_target_cnt)    end
    if(tb.ai_level) then    encoder:addstr(57, tb.ai_level)    end
    if(tb.total_player_real_num) then    encoder:addi32(58, tb.total_player_real_num)    end
    if(tb.ailab_total_num) then    encoder:addi32(59, tb.ailab_total_num)    end
    if(tb.total_human_team_num) then    encoder:addi32(60, tb.total_human_team_num)    end
    if(tb.scheme_pool_id) then    encoder:addi32(61, tb.scheme_pool_id)    end
    if(tb.armed_force_id) then    encoder:addi32(62, tb.armed_force_id)    end
end

function pb.pb_Ds2AnyTssReportNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Ds2AnyTssReportNtf) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __player_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.report_data = pb.pb_DsTssReportDataDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_Ds2AnyTssReportNtfEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.player_id) then    encoder:addu64(2, tb.player_id)    end
    if(tb.report_data) then    pb.pb_DsTssReportDataEncode(tb.report_data, encoder:addsubmsg(3))    end
end

