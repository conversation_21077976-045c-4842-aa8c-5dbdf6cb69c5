----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------



local IVComponentBase = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVComponentBase"
local EItemState = import "EItemState"

---@class ItemViewUseAnim : IVComponentBase
local ItemViewUseAnim = ui("ItemViewUseAnim", IVComponentBase)

local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"

--BEGIN MODIFICATION @ VIRTUOS : 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

local function log(...)
    loginfo("[ItemViewUseAnim]", ...)
end

---@param item ItemBase
function ItemViewUseAnim.SelfShouldShow(item)
    local itemDuration = item and item:GetPreUseAnimSeconds() or 0
    return itemDuration > 0
end

function ItemViewUseAnim:Ctor()
    self._rootVisiable = true
    self._defaultVisibility = ESlateVisibility.SelfHitTestInvisible
    self._tipsHandle = nil

    self._wtItemUseAnim = self:Wnd("ItemUseAnim", UIImage)
    self._wtItemUseCounter = self:Wnd("ItemUseCounter", UITextBlock)
    self._wtCancelBtn = self:Wnd("wtCancelBtn", UIButton)
    self._wtCancelBtn:Event("OnClicked", self._OnCancelBtnClicked, self)
    self._wtCancelIcon = self:Wnd("wtCancelIcon", UIImage)
    if IsHD() then
        self._wtCancelBtn:Event("OnHovered", self._OnHovered, self)
        self._wtCancelBtn:Event("OnUnHovered", self._OnUnHovered, self)

        --BEGIN MODIFICATION @ VIRTUOS :
        self._bTryRefreshFocusInTimeOut = false
        --END MODIFICATION
    end
end

function ItemViewUseAnim:Destroy()
    self:_ClearTimerHandle()
	self:RemoveAllLuaEvent()
end

function ItemViewUseAnim:_OnHovered()
    if self._item then
        local playerUin = Facade.GameFlowManager:GetPlayerUin()
        if playerUin == self._item:GetItemUsingPlayerUin() then
            self._wtItemUseCounter:Collapsed()
            self._wtCancelIcon:SelfHitTestInvisible()

            --BEGIN MODIFICATION @ VIRTUOS : 
            if IsHD() and WidgetUtil.IsGamepad() then
                Module.CommonWidget.Config.Events.evtGlobalHoverItemViewUseAnim:Invoke(true, self._item, false)
            end
            --END MODIFICATION
        end
    end
    self:ShowUsingTips()
end

function ItemViewUseAnim:_OnUnHovered()
    self._wtItemUseCounter:SelfHitTestInvisible()
    self._wtCancelIcon:Collapsed()
    self:HideUsingTips()

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() and WidgetUtil.IsGamepad() then
        Module.CommonWidget.Config.Events.evtGlobalHoverItemViewUseAnim:Invoke(false, self._item, false)
    end
    --END MODIFICATION
end

function ItemViewUseAnim:ShowUsingTips()
    if Facade.GameFlowManager:IsInOBMode() then
        return
    end
    if self._item then
        local itemView = Module.CommonWidget:GetAssignedItemView(self._item)
        if itemView then
            local playerUin = Facade.GameFlowManager:GetPlayerUin()
            if playerUin == self._item:GetItemUsingPlayerUin() then
                local contents = {{id = UIName2ID.Assembled_CommonHDKeyTips_V1, data = {actionName = "CancelEquipAmmo"}}}
                self._tipsHandle = Module.CommonTips:ShowAssembledTips(contents, itemView:GetTipsAnchor())
            else
                local contents = {{id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = ItemMoveCmd.allItemMoveTip.ItemBeingUsedByOtherPlayer}}}
                self._tipsHandle = Module.CommonTips:ShowAssembledTips(contents, itemView:GetTipsAnchor())
            end
        end
    end
end

function ItemViewUseAnim:HideUsingTips()
    local itemView = Module.CommonWidget:GetAssignedItemView(self._item)
    if self._tipsHandle and itemView then
        Module.CommonTips:RemoveAssembledTips(self._tipsHandle, itemView:GetTipsAnchor())
    end
    self._tipsHandle = nil
end

-----------------------------------------------------------------------
--region Override

function ItemViewUseAnim:EnableComponent(bEnable)
    IVComponentBase.EnableComponent(self, bEnable)

    if IsHD() and (not bEnable or not self._item) then
        self:HideUsingTips()
        self:_ClearTimerHandle()
        return
    end

    if self._item:IsInUsing() then
        -- 使用中
        self:_CreateTimerHandle()
    else

        --BEGIN MODIFICATION @ VIRTUOS : 
        if IsHD() and WidgetUtil.IsGamepad() and self._bTryRefreshFocusInTimeOut == false then
            Module.CommonWidget.Config.Events.evtGlobalHoverItemViewUseAnim:Invoke(false, self._item, true)
        end
        --END MODIFICATION

        self:_ClearTimerHandle()
    end
end

--endregion
-----------------------------------------------------------------------


function ItemViewUseAnim:_CreateTimerHandle()
    self:_ClearTimerHandle()

    self._tickTimer = Timer:NewIns(0.1, 0)
    self._tickTimer:AddListener(self.TickItemUseAnim, self)
    self._tickTimer:Start()
    self:TickItemUseAnim(0.1)  -- 当前帧也调用一下

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() and WidgetUtil.IsGamepad() then
        self._bTryRefreshFocusInTimeOut = false
    end
    --END MODIFICATION
end

function ItemViewUseAnim:_ClearTimerHandle()
    if self._tickTimer then
        self._tickTimer:Release()
        self._tickTimer = nil
    end
end

function ItemViewUseAnim:TickItemUseAnim(delta)
    if not self._item then
        self:_ClearTimerHandle()
        return
    end

    local itemDuration = self._item:GetPreUseAnimSeconds()
    local useFinishTime = self._item:GetUseFinishTime()
    local itemFeature = self._item:GetFeature()

    if useFinishTime <= 0 then
        if self._rootVisiable then
            self._wtCancelBtn:Collapsed()
            self._rootVisiable = false
        end
        return
    end
    if not self._rootVisiable then
        self._wtCancelBtn:Visible()
        self._rootVisiable = true
    end

    local curTime = UGameplayBlueprintHelper.GetServerTimeSeconds(self)
    local dynamicMaterial = self._wtItemUseAnim:GetDynamicMaterial()

    if curTime < useFinishTime then
        local deltaSecond = itemDuration -  math.max(0, useFinishTime - curTime)
        local percent = deltaSecond / itemDuration

        dynamicMaterial:SetScalarParameterValue("Progress", percent)
        self._wtItemUseCounter:SetText(string.format("%0.1fs", math.max(0, useFinishTime - curTime)))
    else
        self:SetVisibility(ESlateVisibility.Collapsed)

        --BEGIN MODIFICATION @ VIRTUOS : 
        if IsHD() and WidgetUtil.IsGamepad() and self._bTryRefreshFocusInTimeOut == false then
            Module.CommonWidget.Config.Events.evtGlobalHoverItemViewUseAnim:Invoke(false, self._item, true)
            self._bTryRefreshFocusInTimeOut = true
        end
        --END MODIFICATION

        self:_ClearTimerHandle()
    end
end

function ItemViewUseAnim:_OnCancelBtnClicked()
    local playerUin = Facade.GameFlowManager:GetPlayerUin()
    if self._item and playerUin == self._item:GetItemUsingPlayerUin() then
        Module.Looting:DoCancelUseItem()
    elseif not IsHD() then
        Module.CommonTips:ShowSimpleTip(ItemMoveCmd.allItemMoveTip.ItemBeingUsedByOtherPlayer)
    end
end

return ItemViewUseAnim