local UKismetSystemLibrary = import "KismetSystemLibrary"
local Locations = require "Automation.PerfTest.Locations"

local IDLE_RATE = 0.2
local MOVING_RATE = 0.6

local function BatchSpawn(Locations, IdleTemplate, MovingTemplate, ShootingTemplate)
    local GameInstance = GetGameInstance()

    for i, Location in ipairs(Locations) do
        local Template = "None"
        if i >= #Locations * (IDLE_RATE + MOVING_RATE) then
            Template = ShootingTemplate
        elseif i >= #Locations * IDLE_RATE then
            Template = MovingTemplate
        else
            Template = IdleTemplate
        end

        local Cmd = string.format("GMSpawnInLocation %s %f %f %f", Template, Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
    end
end

return function()
    BatchSpawn(Locations.SolLocations_InLocation.PlayerLocations, "PerfTest_Bot_Idle", "PerfTest_Bot_MoveAround", "PerfTest_Bot_ShootForward")
    BatchSpawn(Locations.SolLocations_InLocation.EventAILocations, "PerfTest_AI_Idle", "PerfTest_AI_MoveAround", "PerfTest_AI_ShootForward")
    BatchSpawn(Locations.SolLocations_InLocation.POIAILocations, "PerfTest_AI_Idle", "PerfTest_AI_MoveAround", "PerfTest_AI_ShootForward")
end
