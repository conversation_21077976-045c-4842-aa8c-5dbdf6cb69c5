----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local SystemSettingEvtLogic = {}
local EPrivacyDataIndex = {
    HistoryVisibility = 1,
    InvisibleState = 8,
}

SystemSettingEvtLogic.AddRewardListeners = function()

	Server.TeamServer.Events.evtSendSecondLanguage:AddListener(SystemSettingEvtLogic.SendSecondLanguage)
    Server.SystemSettingServer.Events.evtInitPlayerPrivacyDataList:AddListener(SystemSettingEvtLogic._InitInvisible)
end

SystemSettingEvtLogic.RemoveRewardListeners = function()

    Server.TeamServer.Events.evtSendSecondLanguage:RemoveListener(SystemSettingEvtLogic.SendSecondLanguage)
    Server.SystemSettingServer.Events.evtInitPlayerPrivacyDataList:RemoveListener(SystemSettingEvtLogic._InitInvisible)
end

function SystemSettingEvtLogic.SendSecondLanguage()
    Module.SystemSetting:SendSecondLanguage()
end

function SystemSettingEvtLogic._InitInvisible(privacyDataList)
    local invisibleData = nil
    for key, privacyData in pairs(privacyDataList) do
        if privacyData.type == EPrivacyDataIndex.InvisibleState  then
            invisibleData = privacyData
        end
    end
    Module.SystemSetting:_InitInvisible(privacyDataList,invisibleData)
end


return SystemSettingEvtLogic
