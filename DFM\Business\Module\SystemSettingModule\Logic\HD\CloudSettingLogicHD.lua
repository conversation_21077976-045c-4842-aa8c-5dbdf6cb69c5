----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local CloudSettingLogicHD = {}
local UGPGConfigUtils = import "GPGConfigUtils"
local UpgradeSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.UpgradeSettingLogicHD"
local JsonFactory = require "DFM.YxFramework.Plugin.Json.Json"
local DummyServerLogic = require "DFM.Business.Module.SystemSettingModule.Logic.HD.Debug.DummyServerLogic"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local EMouseSensitivityMode = import("EMouseSensitivityMode")

local maxSlotCount = 6
CloudSettingLogicHD.maxSlotCount = maxSlotCount
CloudSettingLogicHD.localIndex = maxSlotCount

local autoNamePrefix = "+++"

local _bBlockSending = nil
local _SendingQueue = {}


-- 判断文本是否合法
function CloudSettingLogicHD.CheckPlayerNameValid(name)
    if name == nil or name == "" then
        logwarning("LoginRegisterLogic CheckPlayerNameValid Error: name empty or nil")
        return ""
    end

    local config = Server.SDKInfoServer:GetPlayerNameConf()
    if config == nil then
        logwarning("LoginRegisterLogic CheckPlayerNameValid Error: No Config")
        CloudSettingLogicHD.ReqAccountUnicodeConf()
        return ""
    end

    local whiteList = config.white_list
    local blackList = config.black_list
    local max_char_num = config.max_char_num
    local default_char_num = config.default_char_num
    local count_chars = config.count_chars
    local totalLength = 0  --计算的是有效的长度
    local validName = ""
    local errorType = 0
    -- 1、先检查名称里面是否包含特殊字符
    for _, char in utf8.codes(tostring(name)) do
        local charLength = CloudSettingLogicHD._calculateCharLength(char, count_chars, default_char_num)

        if not CloudSettingLogicHD._isCharInWhiteList(char, whiteList) or CloudSettingLogicHD._isCharInBlackList(char, blackList) then
            errorType = 2
        else
            totalLength = totalLength + charLength
            if totalLength <= max_char_num then
                validName = validName .. utf8.char(char)
            end
        end
    end

    -- 2、检查名称长度是否超过限制
    if totalLength > max_char_num then
        errorType = 1
    end

    return errorType, validName
end

function CloudSettingLogicHD.ReqAccountUnicodeConf()
    if not Server.SDKInfoServer:GetPlayerNameConf() then
        Server.AccountServer:ReqAccountUnicodeConf(CloudSettingLogicHD._RecvUnicodeConf)
    end
end

function CloudSettingLogicHD._RecvUnicodeConf(res)
    logerror("[CloudSettingLogicHD][RecvUnicodeConf] not error result:", res.result)
    if res.result and res.result == 0 then
        Server.SDKInfoServer:SavePlayerNameConf(res)
    end
end

-- 判断字符是否在白名单内
function CloudSettingLogicHD._isCharInWhiteList(unicode, whiteList)
    for _, range in ipairs(whiteList) do
        if unicode >= range.minRune and unicode <= range.maxRune then
            return true
        end
    end
    return false
end

-- 判断字符是否在黑名单范围内
function CloudSettingLogicHD._isCharInBlackList(unicode, blackList)
    for _, blackChar in ipairs(blackList) do
        if unicode == blackChar then
            return true
        end
    end
    return false
end

-- 计算字符长度
function CloudSettingLogicHD._calculateCharLength(unicode, charCountList, defaultNum)
    for _, countItem in ipairs(charCountList) do
        if unicode >= countItem.minRune and unicode <= countItem.maxRune then
            return countItem.charNum
        end
    end
    return defaultNum
end

function CloudSettingLogicHD._isCharInBlackList(unicode, blackList)
    for _, blackChar in ipairs(blackList) do
        if unicode == blackChar then
            return true
        end
    end
    return false
end

local autoUploadIni = {
    section = "ClientSettingHD",
    key = "bCloudSettingAutoUpload",
    ini = "UserSystemSettingHD",
}

function CloudSettingLogicHD.GetAutoUploadState()
    local bAutoUpload = false
    local result = false
    result, bAutoUpload = UGPGConfigUtils.GetBool(autoUploadIni.section, autoUploadIni.key, nil, autoUploadIni.ini)
    if not result then
        bAutoUpload = true -- 默认值
    end
    return bAutoUpload
end

function CloudSettingLogicHD.SetAutoUploadState(bIsChecked)
    UGPGConfigUtils.SetBool(autoUploadIni.section, autoUploadIni.key, bIsChecked, autoUploadIni.ini)
end

local appliedCloudSettingIndexIni = {
    section = "ClientSettingHD",
    key = "CloudSettingIndex",
    ini = "UserSystemSettingHD",
}

function CloudSettingLogicHD.SetAppliedCloudSettingIndex(index)
    if index >= 1 and index <= maxSlotCount then
        UGPGConfigUtils.SetInt(appliedCloudSettingIndexIni.section, appliedCloudSettingIndexIni.key, index, appliedCloudSettingIndexIni.ini)
        return
    end
    logerror("CloudSettingLogicHD.SetAppliedCloudSettingIndex invalid index", index)
end

function CloudSettingLogicHD.GetLocalSettingData()
    return Module.SystemSetting.Field:GetCloudCommonSetting(maxSlotCount)
end

-- local commonSetting = {}
-- commonSetting["SettingName"] = "SettingValue"
-- local Meta = {
--     Slots = { -- 这个数组的顺序决定显示顺序
--         {
--             name = "xxx", -- 如果使用默认名字，就没有name属性
--             index = 6,
--             timestamp = 123
--         },
--         {
--             name = autoNamePrefix .. tostring(1), --使用默认名字，后跟数字1
--             index = 1,
--             timestamp = 123
--         },
--         {
--             name = autoNamePrefix .. tostring(3), --使用默认名字，后跟数字3
--             index = 3,
--             timestamp = 123
--         },
--         {
--             name = "xxx", --自定义名字，可以重名
--             index = 2,
--             timestamp = 123
--         },
--     },
--     SaveVersion = 123
-- }
-- local kv_array = {
--     {type = "CommonSettingHD", key = "Data", value = "jsonstr"},
--     {type = "CommonSettingHD", key = "Meta", value = "jsonstr"},
--     {type = "CommonSettingHD", key = "DataKeySetting", value = "jsonstr"},
-- }

function CloudSettingLogicHD._ReqUploadedSetting()
    local function OnFetchRes(res)
        loginfo("[CloudSettingLogicHD] Fetched Setting")
        if res.result == 0 then
            loginfo("[CloudSettingLogicHD] Fetch Setting Success")
            local remoteMeta = nil
            local remoteCommonSetting = {}
            local remoteKeySetting = {}
            for _, v in ipairs(res.kv_array) do
                if v.type == "CommonSettingHD" then
                    if string.starts_with(v.key, "DataKeySetting") then
                        for i = 1, maxSlotCount do
                            if v.key == CloudSettingLogicHD._GetKeyForKeySetting(i) then
                                remoteKeySetting[v.key] = v.value
                            end
                        end
                    elseif string.starts_with(v.key, "Data") then
                        for i = 1, maxSlotCount do
                            if v.key == CloudSettingLogicHD._GetKeyForCommonSetting(i) then
                                remoteCommonSetting[v.key] = JsonFactory.createJson().decode(v.value)
                            end
                        end
                    elseif v.key == "Meta" then
                        remoteMeta = JsonFactory.createJson().decode(v.value)
                    end
                end
            end

            -- 如果云端有数据
            if remoteMeta then
                remoteMeta = UpgradeSettingLogicHD.UpgradeForCloudMeta(remoteMeta) -- TODO
                loginfo("[CloudSettingLogicHD] remoteMetaData ".. table.dump(remoteMeta))
                Module.SystemSetting.Field:SetCloudSettingMeta(remoteMeta)
            end

            for k, v in pairs(remoteCommonSetting) do
                remoteCommonSetting[k].SimpleSettingData = UpgradeSettingLogicHD.UpgradeForCloud(remoteCommonSetting[k].SimpleSettingData)
            end
            loginfo("[CloudSettingLogicHD] remoteCommonSettingData ".. table.dump(remoteCommonSetting))
            Module.SystemSetting.Field:SetCloudCommonSetting(remoteCommonSetting)

            loginfo("[CloudSettingLogicHD] remoteKeySetting ".. table.dump(remoteCommonSetting))
            Module.SystemSetting.Field:SetCloudKeySetting(remoteKeySetting)

            CloudSettingLogicHD._ProccessConsistency()
        else
            logerror("[CloudSettingLogicHD] Fetch Setting Failed", res.result)
        end
    end
    CloudSettingLogicHD.Request({type = "CommonSettingHD", callback = OnFetchRes})
    loginfo("[CloudSettingLogicHD] Fetching Setting")
end

function CloudSettingLogicHD.ReqAllUploadedSettingData()
    DummyServerLogic.PrepareCase(DummyServerLogic.Case.OldPlayerWithoutMeta)
    CloudSettingLogicHD._ReqUploadedSetting()
end

function CloudSettingLogicHD._SendCommonSetting(index)
    loginfo("[CloudSettingLogicHD] SendCommonSetting")

    local function OnSendDataRes(res, resIndex)
        if res.result == 0 then
            loginfo("[CloudSettingLogicHD] SendCommonSetting index Succeed", resIndex)
            Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged:Invoke("success")
        else
            logerror("[CloudSettingLogicHD] SendCommonSetting index Failed", resIndex, res.result)
            Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged:Invoke("fail")
        end
    end

    -- 转json
    local Json = JsonFactory.createJson()
    local commonSettingAllData = Module.SystemSetting.Field:GetCloudCommonSetting()
    local commonSettingData = commonSettingAllData[CloudSettingLogicHD._GetKeyForCommonSetting(index)]
    local commonSettingJson = Json.encode(commonSettingData)

    local OnSendRes = function(res) OnSendDataRes(res, index) end
    Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged:Invoke("start")
    CloudSettingLogicHD.Request({type = "CommonSettingHD", key = CloudSettingLogicHD._GetKeyForCommonSetting(index), value = commonSettingJson, callback = OnSendRes})
    loginfo("[CloudSettingLogicHD] SendCommonSetting data " .. table.dump(commonSettingData))

end

function CloudSettingLogicHD._SendKeySetting(index)
    loginfo("[CloudSettingLogicHD] SendKeySetting")
    local function OnSendDataKeySettingRes(res, resIndex)
        if res.result == 0 then
            loginfo("[CloudSettingLogicHD] SendKeySetting index Succeed", resIndex)
            Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged:Invoke("success")
        else
            logerror("[CloudSettingLogicHD] SendKeySetting index Failed", resIndex, res.result)
            Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged:Invoke("fail")
        end
    end
    local keySettingJsonAll = Module.SystemSetting.Field:GetCloudKeySetting()
    local keySettingJsonStr = keySettingJsonAll[CloudSettingLogicHD._GetKeyForKeySetting(index)]

    local OnSendRes = function(res) OnSendDataKeySettingRes(res, index) end
    Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged:Invoke("start")
    CloudSettingLogicHD.Request({type = "CommonSettingHD", key = CloudSettingLogicHD._GetKeyForKeySetting(index), value = keySettingJsonStr, callback = OnSendRes})
    loginfo("[CloudSettingLogicHD] SendKeySetting data " .. keySettingJsonStr)
end

function CloudSettingLogicHD._SendMeta()
    loginfo("[CloudSettingLogicHD] DoSendMeta")
    local function OnSendRes(res)
        if res.result == 0 then
            loginfo("[CloudSettingLogicHD] Send Meta Succeed")
        else
            logerror("[CloudSettingLogicHD] Send Meta Failed", res.result)
        end
    end

    -- 转json
    local Json = JsonFactory.createJson()
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    local metaJson = Json.encode(meta)

    CloudSettingLogicHD.Request({type = "CommonSettingHD", key = "Meta", value = metaJson, callback = OnSendRes})
    loginfo("[CloudSettingLogicHD] Send Meta:" .. table.dump(meta))
end

local localKeyIni = {
    section = "ClientSettingHD",
    key = "CloudSettingLocalKey",
    ini = "UserSystemSettingHD",
}

function CloudSettingLogicHD._GetLocalKey()
    local key = Module.SystemSetting.Field:GetCloudSettingLocalKey()
    if key then
        return key
    end
    local result = false
    result, key = UGPGConfigUtils.GetString(localKeyIni.section, localKeyIni.key, nil, localKeyIni.ini)
    if not result or key == nil or key == "" then
        key = CloudSettingLogicHD.GenerateRandomKey()
        CloudSettingLogicHD._SetLocalKey(key)
    end
    loginfo("[CloudSettingLogicHD] GetLocalKey", result, localKeyIni.section, localKeyIni.key, key, localKeyIni.ini)
    return key
end

function CloudSettingLogicHD._SetLocalKey(key)
    if key == nil or key == "" then
        key = CloudSettingLogicHD.GenerateRandomKey()
    end
    UGPGConfigUtils.SetString(localKeyIni.section, localKeyIni.key, key, localKeyIni.ini)
    Module.SystemSetting.Field:SetCloudSettingLocalKey(key)
    loginfo("[CloudSettingLogicHD] SetLocalKey", localKeyIni.section, localKeyIni.key, key, localKeyIni.ini)
end

function CloudSettingLogicHD._GetKeyForCommonSetting(index)
    if index == 1 then
        return "Data" -- 旧数据直接当做第一个
    elseif index == maxSlotCount then
        local key = CloudSettingLogicHD._GetLocalKey()
        return "Data" .. key
    end
    return "Data" .. tostring(index)
end

function CloudSettingLogicHD._GetKeyForKeySetting(index)
    if index == 1 then
        return "DataKeySetting" -- 旧数据直接当做第一个
    elseif index == maxSlotCount then -- 最后一个是本地
        local key = CloudSettingLogicHD._GetLocalKey()
        return "DataKeySetting" .. key
    end
    return "DataKeySetting" .. tostring(index)
end

function CloudSettingLogicHD.IsLocal(index)
    return index == maxSlotCount
end

-- 获取客户端时区
function CloudSettingLogicHD.GetClientTimeZoneDeltaSeconds()
	local now = os.time()
	local difftime = os.difftime(now, os.time(os.date("!*t", now)))
	return difftime
end

-- 当前应用中的，index是云上的排序
function CloudSettingLogicHD.GetAppliedCloudSettingIndex()
    local index = 1
    local result = false
    result, index = UGPGConfigUtils.GetInt(appliedCloudSettingIndexIni.section, appliedCloudSettingIndexIni.key, nil, appliedCloudSettingIndexIni.ini)
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    if not result or not (index >= 1 and index <= maxSlotCount) then
        index = 1 -- 默认值
        CloudSettingLogicHD.SetAppliedCloudSettingIndex(index)
    else
        if meta.Slots then
            for _, v in ipairs(meta.Slots) do
                if v.index == index then
                    return index
                end
            end
        end
        index = nil
    end
    return index
end

function CloudSettingLogicHD.GenerateNewSlotName(bLocal)
    if bLocal then
        return Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.LocalNameDefault
    end

    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    local newIndex = 1
    local bContinueFinding = meta ~= nil and meta.Slots ~= nil
    while bContinueFinding do
        bContinueFinding = false
        for _, v in pairs(meta.Slots) do
            if string.starts_with(v.name, autoNamePrefix) then
                local index = tonumber(string.match(v.name, "%d+"))
                if index == newIndex then
                    newIndex = newIndex + 1
                    bContinueFinding = true
                    break
                end
            end
        end
    end
    return Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.AutoNamePrefix .. tostring(newIndex)
end

function CloudSettingLogicHD.GetFreeIndex()
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    local index = 1
    if meta.Slots then
        for _, v in ipairs(meta.Slots) do
            if index == v.index and v.index < CloudSettingLogicHD.localIndex then
                index = index + 1
            end
        end
    end
    return index
end

function CloudSettingLogicHD.SetName(index, name)
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    local newSlot = {
        name = name,
        index = index,
        timestamp = Facade.ClockManager:GetLocalTimestamp(),
    }
    if meta.Slots then
        local bFoundOld = false
        for i, v in ipairs(meta.Slots) do
            if v.index == index then
                v.name = name
                meta.Slots[i] = v
                bFoundOld = true
                break
            end
        end
        if not bFoundOld then
            table.insert(meta.Slots, newSlot)
        end
    else
        meta.Slots = {newSlot}
    end
    Module.SystemSetting.Field:SetCloudSettingMeta(meta)
    CloudSettingLogicHD._SendMeta()
    Module.SystemSetting.Config.Event.evtCloudSettingSlotStateChanged:Invoke()
end

function CloudSettingLogicHD.DeleteIndex(index)
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    if meta.Slots then
        for i, v in ipairs(meta.Slots) do
            if v.index == index then
                table.remove(meta.Slots, i)
                Module.SystemSetting.Field:SetCloudSettingMeta(meta)
                CloudSettingLogicHD._SendMeta()
                Module.SystemSetting.Config.Event.evtCloudSettingSlotStateChanged:Invoke()
                return
            end
        end
    end
end

function CloudSettingLogicHD.ApplyIndex(index)
    if index > 0 and index <= maxSlotCount then
        CloudSettingLogicHD._ApplyIndexForCommonSetting(index)
        CloudSettingLogicHD._ApplyIndexForKeySetting(index)
        CloudSettingLogicHD.SetAppliedCloudSettingIndex(index)
        Module.SystemSetting.Config.Event.evtCloudSettingSlotStateChanged:Invoke()
    end
end

function CloudSettingLogicHD.CreateNameWindow(fOnNameConfirmed, bLocal, oldName)
    local newName = oldName or CloudSettingLogicHD.GenerateNewSlotName(bLocal)
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDCloudSettingChangeName, nil, nil, fOnNameConfirmed, newName)
end

-- 一致性处理，新旧兼容
function CloudSettingLogicHD._ProccessConsistency()
    local commonSetting = Module.SystemSetting.Field:GetCloudCommonSetting()
    local keySetting = Module.SystemSetting.Field:GetCloudKeySetting()
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    local bChanged = false
    if (commonSetting == nil) or (keySetting == nil) then -- 是否empty一致
        commonSetting = {}
        keySetting = {}
        logwarning("[CloudSettingLogicHD] detect inconsistency set Setting empty")
        bChanged = true
    elseif commonSetting and keySetting then -- 数量一致
        for i = 1, maxSlotCount do
            local commonSettingKey = CloudSettingLogicHD._GetKeyForCommonSetting(i)
            local keySettingKey = CloudSettingLogicHD._GetKeyForKeySetting(i)
            if commonSetting[commonSettingKey] == nil ~= keySetting[keySettingKey] == nil then
                if commonSetting[commonSettingKey] then
                    commonSetting[commonSettingKey] = nil
                    logwarning("[CloudSettingLogicHD] detect inconsistency commonSetting and remove key:", commonSettingKey)
                end
                if keySetting[keySettingKey] then
                    keySetting[keySettingKey] = nil
                    logwarning("[CloudSettingLogicHD] detect inconsistency DataKeySetting and remove key:", keySettingKey)
                end
                bChanged = true
            end
        end
    end

    if bChanged then -- 没有改变云的内容，不上传
        Module.SystemSetting.Field:SetCloudCommonSetting(commonSetting)
        Module.SystemSetting.Field:SetCloudKeySetting(keySetting)
        logwarning("[CloudSettingLogicHD] detect inconsistency commonSetting:" .. table.dump(commonSetting))
        logwarning("[CloudSettingLogicHD] detect inconsistency keySetting:" .. table.dump(keySetting))
    end

    if meta and meta.Slots then -- meta说有的东西，如果没有，则在meta中去掉
        bChanged = false
        for i = #meta.Slots, 1, -1 do
            local bHasCommonSetting = table.containskey(commonSetting, CloudSettingLogicHD._GetKeyForCommonSetting(meta.Slots[i].index))
            local bHasKeySetting = table.containskey(keySetting, CloudSettingLogicHD._GetKeyForKeySetting(meta.Slots[i].index))
            if not (bHasCommonSetting and bHasKeySetting) then
                table.remove(meta.Slots, i)
            end
        end
        if bChanged then
            Module.SystemSetting.Field:SetCloudSettingMeta(meta)
            CloudSettingLogicHD._SendMeta()
        end
    elseif not meta then
        local defaultCommonKey = CloudSettingLogicHD._GetKeyForCommonSetting(1)
        for k,v in pairs(commonSetting) do
            if k ~= defaultCommonKey then --这不可能发生
                logwarning("[CloudSettingLogicHD] detect bad commonSetting and correct for none meta old player:" .. table.dump(commonSetting))
                local newCommonSetting = {}
                newCommonSetting[defaultCommonKey] = v
                commonSetting = newCommonSetting
                Module.SystemSetting.Field:SetCloudCommonSetting(commonSetting)
                logwarning("[CloudSettingLogicHD] corrected:" .. table.dump(commonSetting))
                break
            end
        end
        if commonSetting[defaultCommonKey] then-- 老玩家，应用云
            CloudSettingLogicHD._ApplyIndexForCommonSetting(1)
        else  -- 新玩家，创建云
            local commonSettingData = CloudSettingLogicHD._GetLocalCommonSettingData()
            commonSettingData.SimpleSettingData = UpgradeSettingLogicHD.FillSaveVersion(commonSettingData.SimpleSettingData)
            commonSetting[defaultCommonKey] = commonSettingData
            Module.SystemSetting.Field:SetCloudCommonSetting(commonSetting)
            CloudSettingLogicHD._SendCommonSetting(1)
        end

        local defaultKeyKey = CloudSettingLogicHD._GetKeyForKeySetting(1)
        for k,v in pairs(keySetting) do
            if k ~= defaultKeyKey then --这不可能发生
                logwarning("[CloudSettingLogicHD] detect bad keySetting and correct for none meta old player:" .. table.dump(keySetting))
                local newKeySetting = {}
                newKeySetting[defaultKeyKey] = v
                keySetting = newKeySetting
                Module.SystemSetting.Field:SetCloudKeySetting(keySetting)
                logwarning("[CloudSettingLogicHD] corrected:" .. table.dump(keySetting))
                break
            end
        end

        if keySetting[defaultKeyKey] then-- 老玩家，应用云
            CloudSettingLogicHD._ApplyIndexForKeySetting(1)
        else  -- 新玩家，创建云
            local keySettingData = SettingLogicHD.GetHDSettingInfo_KeySetting()
            keySetting[defaultKeyKey] = keySettingData
            Module.SystemSetting.Field:SetCloudKeySetting(keySetting)
            CloudSettingLogicHD._SendKeySetting(1)
        end
        CloudSettingLogicHD.SetAppliedCloudSettingIndex(1)
        meta = {
            Slots = {
                {
                    name = autoNamePrefix .. tostring(1),
                    index = 1,
                    timestamp = Facade.ClockManager:GetLocalTimestamp()
                },
            },
            SaveVersion = UpgradeSettingLogicHD.GetSaveVersion(),
        }
        Module.SystemSetting.Field:SetCloudSettingMeta(meta)
        CloudSettingLogicHD._SendMeta()
    end
end

function CloudSettingLogicHD.Request(data)
    if DummyServerLogic.DebugOn then
        DummyServerLogic.Request(data.type, data.key, data.value, data.callback)
    else
        if _bBlockSending then
            table.insert(_SendingQueue, data)
        else
            local req = nil
            if data.key then
                req = pb.CSSettingPutKeyValueReq:New()
                req.kv = {type = data.type, key = data.key, value = data.value}
            else
                req = pb.CSSettingGetValuesByTypeReq:New()
                req.type = data.type
            end
            req:Request(data.callback, {bEnableHighFrequency = true})
            _bBlockSending = true
            Timer.DelayCall(0.2, function()
                _bBlockSending = false
                local queueHead = _SendingQueue[1]
                if queueHead then
                    CloudSettingLogicHD.Request(queueHead)
                    table.remove(_SendingQueue, 1)
                end
            end)
        end
    end
end

-- 随机生成一个32位字符字符串
function CloudSettingLogicHD.GenerateRandomKey()
    math.randomseed(os.time())
    local chars = {}
    for i = 1, 32 do
        local rand = math.random(0, 1)  -- 随机决定大小写
        if rand == 0 then
            chars[i] = string.char(math.random(65, 90))  -- A-Z
        else
            chars[i] = string.char(math.random(97, 122))  -- a-z
        end
    end
    return table.concat(chars)
end

function CloudSettingLogicHD._ApplyIndexForCommonSetting(index)
    loginfo("[CloudSettingLogicHD] _ApplyIndexForCommonSetting", index)
    local anyError = false
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    if meta then
        if not meta.Slots or #meta.Slots <= 0 then
            logerror("[CloudSettingLogicHD] _ApplyIndexForCommonSetting with wrong index", index, table.dump(meta))
            return
        end
    else
        if index ~= 1 then
            logerror("[CloudSettingLogicHD] _ApplyIndexForCommonSetting with nil meta", index)
            return
        end
    end

    local ret = trycall(
            function()
                local SetDataByID = CommonSettingLogicHD.SetDataByIDImmediately

                local commonSetting = Module.SystemSetting.Field:GetCloudCommonSetting()
                local remoteData = commonSetting[CloudSettingLogicHD._GetKeyForCommonSetting(index)]
                if remoteData then
                    -- 为了兼容性，需要根据本地的id列表来获取数值，而不是云端的
                    for _, id in ipairs(Module.SystemSetting.Config.SimpleCloudSettingIDs) do
                        local settingValue = remoteData.SimpleSettingData[id]
                        if settingValue ~= nil then
                            SetDataByID(id, settingValue)
                        else
                            if id then
                                logerror("[CloudSettingLogicHD] "..id.." value not found in cloud data")
                            else
                                logerror("[CloudSettingLogicHD] id nil")
                            end
                            anyError = true
                        end
                    end

                    local ADSList = remoteData.ADSList
                    local MDVList = remoteData.MDVList
                    local anyError_SetASDAndMDV = SettingLogicHD.SetASDAndMDVDataList(ADSList, MDVList, false)
                    local ADSListGamepad = remoteData.ADSListGamepad
                    local MDVListGamepad = remoteData.MDVListGamepad
                    local anyError_SetASDAndMDV_Gamepad = SettingLogicHD.SetASDAndMDVDataList(ADSListGamepad, MDVListGamepad, true)

                    anyError = anyError or anyError_SetASDAndMDV or anyError_SetASDAndMDV_Gamepad
                end
            end
    )

    SettingRegLogicHD.FlushPendingSaveObjs()

    if not ret then
        anyError = true
    end
end

function CloudSettingLogicHD._ApplyIndexForKeySetting(index)
    loginfo("[CloudSettingLogicHD] _ApplyIndexForKeySetting", index)
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    if meta then
        if not meta.Slots or #meta.Slots <= 0 then
            logerror("[CloudSettingLogicHD] _ApplyIndexForKeySetting with wrong index", index, table.dump(meta))
            return
        end
    else
        if index ~= 1 then
            logerror("[CloudSettingLogicHD] _ApplyIndexForKeySetting with nil meta", index)
            return
        end
    end

    local keySetting = Module.SystemSetting.Field:GetCloudKeySetting()
    local remoteDataKeySetting = keySetting[CloudSettingLogicHD._GetKeyForKeySetting(index)]
    if remoteDataKeySetting then
        SettingLogicHD.ApplyKeySettingJsonStr(remoteDataKeySetting)
    end
end

function CloudSettingLogicHD.GetDisplayNameFromMetaName(metaName)
    if string.starts_with(metaName, autoNamePrefix) then
        local index = tonumber(string.match(metaName, "%d+"))
        if index > 0 and index <= maxSlotCount then -- 这是一个自动命名
            return Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.AutoNamePrefix .. tostring(index)
        end
    end
    return metaName
end

function CloudSettingLogicHD.GetDisplayNameFromIndex(index)
    local meta = Module.SystemSetting.Field:GetCloudSettingMeta()
    if meta and meta.Slots then
        for _, v in ipairs(meta.Slots) do
            if v.index == index then
                return CloudSettingLogicHD.GetDisplayNameFromMetaName(v.name)
            end
        end
    end
    return ""
end

function CloudSettingLogicHD.OverWrite(index)
    local commonSetting = Module.SystemSetting.Field:GetCloudCommonSetting()
    local commonSettingData = CloudSettingLogicHD._GetLocalCommonSettingData()
    commonSettingData.SimpleSettingData = UpgradeSettingLogicHD.FillSaveVersion(commonSettingData.SimpleSettingData)
    commonSetting[CloudSettingLogicHD._GetKeyForCommonSetting(index)] = commonSettingData
    Module.SystemSetting.Field:SetCloudCommonSetting(commonSetting)
    CloudSettingLogicHD._SendCommonSetting(index)

    local keySetting = Module.SystemSetting.Field:GetCloudKeySetting()
    local keySettingData = SettingLogicHD.GetHDSettingInfo_KeySetting()
    keySetting[CloudSettingLogicHD._GetKeyForKeySetting(index)] = keySettingData
    Module.SystemSetting.Field:SetCloudKeySetting(keySetting)
    CloudSettingLogicHD._SendKeySetting(index)
end

function CloudSettingLogicHD._GetLocalCommonSettingData()
    local commonSettingData, _ = SettingLogicHD.GetLocalCommonSettingDataAndHash()
    return commonSettingData
end

function CloudSettingLogicHD.CheckAndSendSetting(commonSetingIdChanged, bHasKeySettingChanged)
    -- 是否自动上传
    if not CloudSettingLogicHD.GetAutoUploadState() then
        loginfo("[CloudSettingLogicHD] CheckAndSendSetting not autoupload")
        return
    end
    -- 是否应用任何设置槽
    local appliedIndex = CloudSettingLogicHD.GetAppliedCloudSettingIndex()
    if not appliedIndex then
        loginfo("[CloudSettingLogicHD] CheckAndSendSetting bad appliedIndex", appliedIndex)
        return
    end

    if #commonSetingIdChanged > 0 then
        local commonSetting = Module.SystemSetting.Field:GetCloudCommonSetting()
        local key = CloudSettingLogicHD._GetKeyForCommonSetting(appliedIndex)
        local commonSettingData = commonSetting[key]
        local localCommonSettingData = CloudSettingLogicHD._GetLocalCommonSettingData()
        assert(commonSettingData)
        for _, id in ipairs(commonSetingIdChanged) do
            if table.contains(Module.SystemSetting.Config.SimpleCloudSettingIDs, id) then
                if commonSettingData.SimpleSettingData[id] ~= localCommonSettingData.SimpleSettingData[id] then
                    commonSetting[key] = localCommonSettingData
                    Module.SystemSetting.Field:SetCloudCommonSetting(commonSetting)
                    CloudSettingLogicHD._SendCommonSetting(appliedIndex)
                    break
                end
            end
        end
    end

    if bHasKeySettingChanged then
        local localKeySettingJsonStr = SettingLogicHD.GetHDSettingInfo_KeySetting()
        local keySetting = Module.SystemSetting.Field:GetCloudKeySetting()
        local key = CloudSettingLogicHD._GetKeyForKeySetting(appliedIndex)
        local keySettingJsonStr = keySetting[key]
        if localKeySettingJsonStr ~= keySettingJsonStr then
            keySetting[key] = localKeySettingJsonStr
            Module.SystemSetting.Field:SetCloudKeySetting(keySetting)
            CloudSettingLogicHD._SendKeySetting(appliedIndex)
        end
    end
end

return CloudSettingLogicHD
