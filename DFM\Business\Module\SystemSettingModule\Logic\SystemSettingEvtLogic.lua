----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



local SystemSettingEvtLogic = {}


SystemSettingEvtLogic.AddRewardListeners = function()

	Server.TeamServer.Events.evtSendSecondLanguage:AddListener(SystemSettingEvtLogic.SendSecondLanguage,self)
end

SystemSettingEvtLogic.RemoveRewardListeners = function()

    Server.TeamServer.Events.evtSendSecondLanguage:RemoveListener(SystemSettingEvtLogic.SendSecondLanguage)
end

SystemSettingEvtLogic.SendSecondLanguage = function()
    Module.SystemSetting:SendSecondLanguage()
end


return SystemSettingEvtLogic
