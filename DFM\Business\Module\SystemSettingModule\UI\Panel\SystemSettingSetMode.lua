----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingSetMode
local SystemSettingSetMode = ui("SystemSettingSetMode")

local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum

function SystemSettingSetMode:Ctor()
    self._SOLModeItem = self:Wnd("WBP_SetUp_ModeItem",UIWidgetBase)
    self._SOLModeItemBG = self._SOLModeItem:Wnd("DFImage_bg",UIWidgetBase)
    self._SOLModeItem:Wnd("DFButton_0",UIButton):Event("Onclicked",self._OnSelectSOLMode,self)
    self._SOLDownloadItem = self._SOLModeItem:Wnd("WBP_CommonDownload",LiteCommonDownload)
    self._SOLDownloadItem:Wnd("DFMButton_DownloadBtn",UIButton):RemoveEvent("OnClicked")
    self._SOLDownloadItem:Wnd("DFMButton_DownloadBtn",UIButton):Event("Onclicked",self._OnClickedDownloadBtnSOL,self)

    self._MPModeItem = self:Wnd("WBP_SetUp_ModeItem_1",UIWidgetBase)
    self._MPModeItemBG = self._MPModeItem:Wnd("DFImage_bg",UIWidgetBase)
    self._MPModeItem:Wnd("DFButton_0",UIButton):Event("Onclicked",self._OnSelectMPMode,self)
    self._MPDownloadItem = self._MPModeItem:Wnd("WBP_CommonDownload",LiteCommonDownload)
    self._MPDownloadItem:Wnd("DFMButton_DownloadBtn",UIButton):RemoveEvent("OnClicked")
    self._MPDownloadItem:Wnd("DFMButton_DownloadBtn",UIButton):Event("Onclicked",self._OnClickedDownloadBtnMP,self)

    ---@type MusicPlayerWidget 
    self._wtMusicPlayerWidget = self:Wnd("WBP_MusicPlayer_Main", UIWidgetBase)
    self._wtMusicPlayerWidget:Visible()

    self._wtIdcDesc = self:Wnd("DFRichTextBlock", UITextBlock)
    self._wtSwitchIdcBtn = self:Wnd("SwitchIDCButton", UIButton)
    self._wtSwitchIdcBtn:Event("OnClicked", self.OpenSwitchIDCPanel, self)
end

function SystemSettingSetMode:OnOpen()
   
end

function SystemSettingSetMode:OnShowBegin()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._RefreshDownloadBtn,self)
    self:AddLuaEvent(Module.NetworkBusiness.Config.Events.evtOnIdcConfirmBtnClicked, self._RefreshIdcInfo, self)
    self._gameMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if self._gameMode == EArmedForceMode.SOL then
        self._SOLModeItem:SetType(1)
        self._SOLModeItemBG:Collapsed()
    elseif self._gameMode == EArmedForceMode.MP then
        self._MPModeItem:SetType(1)
        self._MPModeItemBG:Collapsed()
    end
    self._SOLModeItem:SetLocked(not Module.GameMode:IsModeUnlocked(EArmedForceMode.SOL))
    self._MPModeItem:SetLocked(not Module.GameMode:IsModeUnlocked(EArmedForceMode.MP))
    
    self:_RefreshDownloadBtn()


    self._wtMusicPlayerWidget:RefreshView()
    self._wtMusicPlayerWidget:SetParentWidget(self)
    self:_UpdateIdcInfo()
end

function SystemSettingSetMode:OnShow()
end


function SystemSettingSetMode:_OnSelectSOLMode()
    local function fOnConfirmCallBack()
        Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.SOL)
    end

    if self._gameMode == EArmedForceMode.SOL then
        Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.ModeSelect.AlreadyIn)
        return 
    end
    if not Module.GameMode:IsModeUnlocked(EArmedForceMode.SOL) then
        local unlockConfig=Server.ModuleSwitcherServer:GetModuleSwitcherConfigById(SwitchSystemID.SwitchSyetemLobbyEntranceSOL)
        if unlockConfig and unlockConfig.unlock_timestamp~=0 then
            local unlockTimestamp = Module.ModuleSwitcher:GetSystemEnableTime(SwitchSystemID.SwitchSyetemLobbyEntranceSOL)
            if unlockTimestamp then
                Module.CommonTips:ShowSimpleTip(string.format(Module.IrisSafeHouse.Config.Loc.ModeLockedTip, TimeUtil.TransUnixTimestamp2YYMMDDHHMMSSString(unlockTimestamp + TimeUtil.GetTimeOffsetInSeconds())))
            end
        else
            Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.SOLLockReasonForOld)
        end
        return
    end

    local solModuelName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SOLModule)
    local bDownloaded = self._SOLDownloadItem:InitModuleKey(solModuelName)
    if not bDownloaded then
        self:_OnClickedDownloadBtnSOL()
        return
    end

    Module.CommonTips:ShowConfirmWindow(string.format(Module.SystemSetting.Config.Loc.ModeSelect.Confirm,tostring(Module.SystemSetting.Config.Loc.ModeSelect.SOL)),fOnConfirmCallBack)
end

function SystemSettingSetMode:_OnSelectMPMode()
    local function fOnConfirmCallBack()
        Module.GameMode:TrySwitchMode(Module.GameMode.Config.ESwitchMode.MP)
    end
    if self._gameMode == EArmedForceMode.MP then
        Module.CommonTips:ShowSimpleTip(Module.SystemSetting.Config.Loc.ModeSelect.AlreadyIn)
        return 
    end
    
    if not Module.GameMode:IsModeUnlocked(EArmedForceMode.MP) then
        local unlockConfig=Server.ModuleSwitcherServer:GetModuleSwitcherConfigById(SwitchSystemID.SwitchSyetemLobbyEntranceMP)
        if unlockConfig and unlockConfig.unlock_timestamp~=0 then
            local unlockTimestamp = Module.ModuleSwitcher:GetSystemEnableTime(SwitchSystemID.SwitchSyetemLobbyEntranceMP)
            if unlockTimestamp then
                Module.CommonTips:ShowSimpleTip(string.format(Module.IrisSafeHouse.Config.Loc.ModeLockedTip, TimeUtil.TransUnixTimestamp2YYMMDDHHMMSSString(unlockTimestamp + TimeUtil.GetTimeOffsetInSeconds())))
            end
        else
            Module.CommonTips:ShowSimpleTip(Module.IrisSafeHouse.Config.Loc.SOLLockReasonForOld)
        end
        return
    end

    local mpModuelName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MPModule)
    local bDownloaded = self._MPDownloadItem:InitModuleKey(mpModuelName)
    if not bDownloaded then
        self:_OnClickedDownloadBtnMP()
        return
    end

    Module.CommonTips:ShowConfirmWindow(string.format(Module.SystemSetting.Config.Loc.ModeSelect.Confirm,tostring(Module.SystemSetting.Config.Loc.ModeSelect.MP)),fOnConfirmCallBack)
end


function SystemSettingSetMode:_OnClickedDownloadBtnSOL()

    if not self._SOLDownloadItem then
       return
    end
    local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(self._SOLDownloadItem.ModuleKey)
    local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(self._SOLDownloadItem.ModuleKey)
    local isWaiting = LiteDownloadManager:IsWaitingByModuleName(self._SOLDownloadItem.ModuleKey)
    if isDownloading or isWaiting then
        LiteDownloadManager:CancelByModuleName(self._SOLDownloadItem.ModuleKey)
    elseif not bDownloaded then

        local fConfirm = function()
            if self._SOLDownloadItem.ModuleKey == nil or self._SOLDownloadItem.ModuleKey == "" then
                return
            end
            local pufferInitSucceed = LiteDownloadManager:IsPufferInitSucceed()
            if pufferInitSucceed == false then
                logerror("[LiteCommonDownload] OnDownloadBtnClicked pufferInitSucceed == false")
                return
            end
            LiteDownloadManager:DownloadByModuleName(self._SOLDownloadItem.ModuleKey)

            LogAnalysisTool.SignButtonClicked(ButtonIdEnum.LitePOPToTipsDownloadSOL)
        end

        local LiteDownloadMBNum=1024*1024
        local mainTitle=StringUtil.SequentialFormat(Module.LitePackage.Config.Loc.ModeResourceDownloadTip,Module.IrisSafeHouse.Config.Loc.IrisModeTitle)
        local showStr = string.format("%.1fMB", LiteDownloadManager:GetRemainderSizeByModuleName(self._SOLDownloadItem.ModuleKey) / LiteDownloadMBNum)
        local secondTitle=StringUtil.SequentialFormat(Module.LitePackage.Config.Loc.ResourceDownloadDetail,showStr)
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapSOLMoneyLimitWindow, nil, nil, mainTitle, secondTitle,false,fConfirm)
    else
        return
    end
    self._SOLDownloadItem:RefreshState()
end

function SystemSettingSetMode:_OnClickedDownloadBtnMP()

    if not self._MPDownloadItem then
       return
    end
    local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(self._MPDownloadItem.ModuleKey)
    local isDownloading = LiteDownloadManager:IsDownloadingByModuleName(self._MPDownloadItem.ModuleKey)
    local isWaiting = LiteDownloadManager:IsWaitingByModuleName(self._MPDownloadItem.ModuleKey)
    if isDownloading or isWaiting then
        LiteDownloadManager:CancelByModuleName(self._MPDownloadItem.ModuleKey)
    elseif not bDownloaded then

        local fConfirm = function()
            if self._MPDownloadItem.ModuleKey == nil or self._MPDownloadItem.ModuleKey == "" then
                return
            end
            local pufferInitSucceed = LiteDownloadManager:IsPufferInitSucceed()
            if pufferInitSucceed == false then
                logerror("[LiteCommonDownload] OnDownloadBtnClicked pufferInitSucceed == false")
                return
            end
            LiteDownloadManager:DownloadByModuleName(self._MPDownloadItem.ModuleKey)

            LogAnalysisTool.SignButtonClicked(ButtonIdEnum.LitePOPToTipsDownloadMP)
        end

        local LiteDownloadMBNum=1024*1024
        local mainTitle=StringUtil.SequentialFormat(Module.LitePackage.Config.Loc.ModeResourceDownloadTip,Module.IrisSafeHouse.Config.Loc.BFModeTitle)
        local showStr = string.format("%.1fMB", LiteDownloadManager:GetRealSizeTotalByModuleName(self._MPDownloadItem.ModuleKey) / LiteDownloadMBNum)
        local secondTitle=StringUtil.SequentialFormat(Module.LitePackage.Config.Loc.ResourceDownloadDetail,showStr)
        Facade.UIManager:AsyncShowUI(UIName2ID.SandBoxMapSOLMoneyLimitWindow, nil, nil, mainTitle, secondTitle,false,fConfirm)
    else
        return
    end
    self._MPDownloadItem:RefreshState()
end

function SystemSettingSetMode:_RefreshDownloadBtn()
    if self._SOLDownloadItem then
        self._SOLDownloadItem:Collapsed()
        local solModuelName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SOLModule)
        local bDownloadedSOL = self._SOLDownloadItem:InitModuleKey(solModuelName)
        if not bDownloadedSOL then
            self._SOLDownloadItem:Visible()
        end
    end

    if self._MPDownloadItem then
        self._MPDownloadItem:Collapsed()
        local mpModuelName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.MPModule)
        local bDownloadedMP = self._MPDownloadItem:InitModuleKey(mpModuelName)
        if not bDownloadedMP then
            self._MPDownloadItem:Visible()
        end
    end
end
function SystemSettingSetMode:OnHide()
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,self._RefreshDownloadBtn,self)
    self:RemoveLuaEvent(Module.NetworkBusiness.Config.Events.evtOnIdcConfirmBtnClicked, self._RefreshIdcInfo, self)
end


function SystemSettingSetMode:OnClose()
end

function SystemSettingSetMode:OpenSwitchIDCPanel()
    Module.NetworkBusiness:OpenSwitchIDCPanel()
end

function SystemSettingSetMode:_UpdateIdcInfo()
    local bEnableSwitch = Module.NetworkBusiness:IsEnableSwitchIDC()
    if bEnableSwitch then
        self._wtIdcDesc:Visible()
        self._wtSwitchIdcBtn:Visible()
        local callback = CreateCallBack(function(self, res)
            if res and self then
                local desc = Module.NetworkBusiness:GetIdcDesc(res.match_area, res.rtt)
                if self._wtIdcDesc then
                    self._wtIdcDesc:SetText(desc)
                end
            end
        end, self)
        Module.NetworkBusiness:GetSelectIdcInfo(callback)
    else
        self._wtIdcDesc:Collapsed()
        self._wtSwitchIdcBtn:Collapsed()
    end
end

function SystemSettingSetMode:_RefreshIdcInfo()
    logerror("[SystemSettingSetMode] _RefreshIdcInfo")
    Timer.DelayCall(1, function()
        self:_UpdateIdcInfo()
    end)
end

return SystemSettingSetMode