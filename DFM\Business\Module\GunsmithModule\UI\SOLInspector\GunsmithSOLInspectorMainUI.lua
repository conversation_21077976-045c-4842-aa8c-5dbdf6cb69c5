----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local GunsmithInspectorMainBaseUI = require "DFM.Business.Module.GunsmithModule.UI.SOLInspector.GunsmithInspectorMainBaseUI"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local GunsmithUIParam = require "DFM.Business.DataStruct.GunsmithStruct.GunsmithUIParam"

---@class GunsmithSOLInspectorMainUI : GunsmithInspectorMainBaseUI
local GunsmithSOLInspectorMainUI = ui("GunsmithSOLInspectorMainUI", GunsmithInspectorMainBaseUI)

function GunsmithSOLInspectorMainUI:Ctor()
    -- self._wtCanvas_Default = self:Wnd("DFCanvasPanel_110", UIWidgetBase)
    self._wtCanvas_Gunsmith = self:Wnd("wt_DFCanvasPanel_Gunsmith", UIWidgetBase)
    -- self._wtCanvas_Default:SetActive(false)
    self._wtCanvas_Gunsmith:SetActive(true)

    self._wtButtonGunsmith_Default = self:Wnd("wt_WBP_CommonButtonV1S1_Gunsmith_Default", DFCommonButtonOnly)
    self._wtButtonGunsmith_Simulate = self:Wnd("wt_WBP_CommonButtonV1S2_Gunsmith_Simulate", DFCommonButtonOnly)
    self._wtButtonGunsmith_Default:SetMainTitle(Module.Gunsmith.Config.Loc.GunsmithSOLInspectorMainUIDefaultEquipeButtonText)
    self._wtButtonGunsmith_Simulate:SetMainTitle(Module.Gunsmith.Config.Loc.GunsmithSOLInspectorMainUISimulateEquipeButtonText)
    self:AddUIButtonClickedEvent(self._wtButtonGunsmith_Default, self._OnButtonGunsmithDefaultClicked)
    self:AddUIButtonClickedEvent(self._wtButtonGunsmith_Simulate, self._OnButtonGunsmithSimulateClicked)
    self._wtButtonGunsmith_Default:Event("OnDeClicked", self._OnButtonGunsmithDefaultClicked, self)
    self._wtButtonGunsmith_Simulate:Event("OnDeClicked", self._OnButtonGunsmithSimulateClicked, self)
end

function GunsmithSOLInspectorMainUI:Destroy()
end

function GunsmithSOLInspectorMainUI:OnShow()
    GunsmithInspectorMainBaseUI.OnShow(self)
    -- BEGIN MODIFICATION @ VIRTUOS : 禁用A键确认
    if IsHD() then
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    end
    -- END MODIFICATION    
end

function GunsmithSOLInspectorMainUI:OnHide()
    GunsmithInspectorMainBaseUI.OnHide(self)
    -- BEGIN MODIFICATION @ VIRTUOS : 恢复默认导航配置
    if IsHD() then
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self:_RemoveShortcuts()
    end
    -- END MODIFICATION   
end

function GunsmithSOLInspectorMainUI:_OnButtonGunsmithDefaultClicked()
    LogAnalysisTool.SignButtonClicked(Module.Gunsmith.Config.Const.GUNSMITH_SOLINSPECTORMAINUI_DDEFAULT_BUTTON_ID)

    local bIsDownload = self:_InternalCheckDownload()
    if not bIsDownload then
        return
    end

    self:_OnProcessOpenGunsmith()
end

function GunsmithSOLInspectorMainUI:_OnButtonGunsmithSimulateClicked()
    LogAnalysisTool.SignButtonClicked(Module.Gunsmith.Config.Const.GUNSMITH_SOLINSPECTORMAINUI_SIMULATE_BUTTON_ID)

    local bIsDownload = self:_InternalCheckDownload()
    if not bIsDownload then
        return
    end

    self:_OnProcessOpenGunsmith()
end

function GunsmithSOLInspectorMainUI:GetAllRecParts4Inventory()
    return Server.InventoryServer:GetAllRecParts()
end

function GunsmithSOLInspectorMainUI:IsValidPresetData4SlotType(groupID, presetData)
    local presetID = presetData:GetPresetID4SOL()
    if presetID == 0 then
        return false
    end

    if groupID == ItemConfig.EWeaponItemType.Universal then
        local bIsValid = self:IsValidUniversalWeapon(presetID, groupID)
        if not bIsValid then
            return false
        end
    end

    return true
end

function GunsmithSOLInspectorMainUI:GetSlotTypeList()
    return {
        ESlotType.MainWeaponLeft,
        ESlotType.MainWeaponRight,
        ESlotType.Pistrol,
    }
end

function GunsmithSOLInspectorMainUI:_ProcessUpdateUIButtons(itemUIData)
    local bEnable4Deault = false
    local bEnable4Simulate = false

    if itemUIData ~= nil then
        local bUnlock = itemUIData:GetUnlock4Inventory()
        bEnable4Deault = bUnlock
        bEnable4Simulate = not bUnlock
    end

    self._wtButtonGunsmith_Default:SetActive(bEnable4Deault)
    self._wtButtonGunsmith_Simulate:SetActive(bEnable4Simulate)

    -- 进入匹配状态不能对已装备的武器进行改装
    self:_CheckDefaultButtonState(itemUIData, bEnable4Deault)

    -- 资源未下载时不能进行改装
    self:_CheckDefaultButtonStateByPak()

    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self:_InitShortcuts()
    end
    -- END MODIFICATION    
end

 -- 进入匹配状态不能对已装备的武器进行改装
function GunsmithSOLInspectorMainUI:_CheckDefaultButtonState(itemUIData, bEnable4Deault)
    local fcallback = function(bEnableStyle)
        self._wtButtonGunsmith_Default:SetIsEnabledStyle(bEnableStyle)
    end
    local itemBase
    if itemUIData then
        itemBase = itemUIData:GetItemBase()
    end

    local bCouldChangeGun = GunsmithUIContextLogic.IsCouldChangeGun(itemBase)

    return fcallback(bCouldChangeGun)
end

function GunsmithSOLInspectorMainUI:_CheckDefaultButtonStateByPak()
    local bIsDownload = self:_InternalIsDownloaded()
    if bIsDownload then
        return
    end

    self._wtButtonGunsmith_Default:SetIsEnabledStyle(false)
    self._wtButtonGunsmith_Simulate:SetIsEnabledStyle(false)
end

function GunsmithSOLInspectorMainUI:_OnProcessOpenGunsmith()
    local itemUIData = self:_GetFocusItemUIData()
    if itemUIData == nil then
        return
    end

    local itemBase = itemUIData:GetItemBase()
    local param = nil
    local bUnlocked = itemUIData:GetUnlock4Inventory()
    if bUnlocked then
        param = GunsmithUIParam.SetFromItemBase(itemBase)
    else
        local propinfo = itemUIData:PreparePropInfo()
        local bIsValid = isvalid(propinfo)
        if not bIsValid then
            local itemID = itemBase.id
            local errorStr = string.format("GunsmithSOLInspectorMainUI:_OnProcessOpenGunsmith -- itemID: %s modularDesc is nil!!!", itemID)
            local bIsShipping = VersionUtil.IsShipping()
            if not bIsShipping then
                LuaGlobalEvents.evtServerShowTip:Invoke(errorStr)
            end
            logerror(errorStr)
            return
        end
        propinfo.gid = 0
        param = GunsmithUIParam.SetFromUserPropInfo(propinfo)
    end

    local uiParam = self:GetUIParam()
    local bIsValid = isvalid(uiParam)
    if bIsValid then
        local missionID = uiParam:GetMissionID()
        if missionID then
            param:SetMissionID(missionID)
        end
    end

    -- 重新选枪进入需要清除数据
    GunsmithUIContextLogic.SetUnSycnRangeSolution(false)
    Module.Gunsmith:OpenMainUI(param)
end

-- BEGIN MODIFICATION @ VIRTUOS : 新增手柄快捷键（页面中显示的按钮：改装、预改装）
function GunsmithSOLInspectorMainUI:_InitShortcuts()
    self:_RemoveShortcuts()
    -- 改装
    if self._wtButtonGunsmith_Default and self._wtButtonGunsmith_Default:IsVisible() then
        self._DefaultClicked = self:AddInputActionBinding("Gunsmith_Modification", EInputEvent.IE_Pressed, self._OnButtonGunsmithDefaultClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtButtonGunsmith_Default:SetDisplayInputAction("Gunsmith_Modification", true, nil, true)
    end
    -- 预改装
    if self._wtButtonGunsmith_Simulate and self._wtButtonGunsmith_Simulate:IsVisible() then
        self._SimulateClicked = self:AddInputActionBinding("Gunsmith_PreModification", EInputEvent.IE_Pressed, self._OnButtonGunsmithSimulateClicked,self, EDisplayInputActionPriority.UI_Stack)
        self._wtButtonGunsmith_Simulate:SetDisplayInputAction("Gunsmith_PreModification", true, nil, true)
    end
end

function GunsmithSOLInspectorMainUI:_RemoveShortcuts()
    if self._DefaultClicked then
        self:RemoveInputActionBinding(self._DefaultClicked)
        self._DefaultClicked= nil
    end
    if self._SimulateClicked then
        self:RemoveInputActionBinding(self._SimulateClicked)
        self._SimulateClicked= nil
    end
end
-- END MODIFICATION

return GunsmithSOLInspectorMainUI