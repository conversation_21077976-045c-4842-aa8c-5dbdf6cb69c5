----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonWidget)
----- LOG FUNCTION AUTO GENERATE END -----------

local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local IVComponentBase = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVComponentBase"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local CommonWidgetLogic = require "DFM.Business.Module.CommonWidgetModule.Logic.CommonWidgetLogic"
local CommonWidgetModule= require "DFM.Business.Module.CommonWidgetModule.CommonWidgetModule"
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local FAnchors = import "Anchors"

local ETextStyle = {
    Default = 0,
    Equipment = 1,
    ItemName = 2,
    AuctionText = 3,
    NewEquipment = 4
    -- BagCapacityExtend = 4,
}

---@class IVTextIconComponent : IVComponentBase
local IVTextIconComponent = ui("IVTextIconComponent", IVComponentBase)

local function log(...)
    loginfo("[IVTextIconComponent]", ...)
end

IVTextIconComponent.StyleFuncMapping = {}

function IVTextIconComponent:Ctor()
    self._textStyle = CommonWidgetConfig.EIVIconTextStyle.None

    self._wtMainText = self:Wnd("wtMainText", UITextBlock)
    self._wtMainText_1 = self:Wnd("wtMainText_1", UITextBlock)
    self._wtMainIcon = self:Wnd("wtMainIcon", UIImage)

    self._wtMainPanel = self:Wnd("wtMainPanel", UIWidgetBase)
    self._cornerTextStyle = 0

    -- 限制显示堆叠数量
    self._bForceShowStackNum = false
    self:_GenerateStyleFunctionMapping()
    self._IconImagePath = ""
    self._curTweener = nil

    -- cornerstyle优化相关
    self._selfPosition = nil
    self._selfIVMode = nil
end

function IVTextIconComponent:Destroy()
    releaseobject(self.shadingQuality)
    self.shadingQuality = nil
end

-----------------------------------------------------------------------
--region Override

function IVTextIconComponent:RefreshComponent()
    if not IVComponentBase.RefreshComponent(self) then
        return
    end

    -- 设置四角样式
    self:SetCornerStyle()
    self:SetCppValue("bHandleClick", false)
    self:SetCppValue("bHandleClickR", false)
    self:SetCppValue("bHandleClickMid", false)

    local fRefreshFunc = self:_GetStyleRefreshFunction(self._textStyle)
    if not fRefreshFunc then
        if self._textStyle ~= CommonWidgetConfig.EIVIconTextStyle.Custom then
            self:Collapsed()
        end

        return
    end

    -- Default visibility
    -- Control your special visibility in refresh funciton
    self:SelfHitTestInvisible()
    self._wtMainText:SelfHitTestInvisible()
    self._wtMainIcon:SelfHitTestInvisible()
    self._wtMainText_1:Collapsed()

    -- if self._item:IsEquipped() then
    --     self:SetTextStyle(ETextStyle.ItemName)
    -- else
    --     self:SetTextStyle(ETextStyle.NewEquipment)
    -- end

    fRefreshFunc(self)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Public

function IVTextIconComponent:SetStyle(style)
     self._textStyle = style
end

function IVTextIconComponent:ShowIconOnly(iconPath)
    self._wtMainIcon:SelfHitTestInvisible()
    self._wtMainText:Collapsed()
    self._wtMainText_1:Collapsed()
    self._wtMainIcon:AsyncSetImagePath(iconPath, false)
    self._IconImagePath = iconPath
end

function IVTextIconComponent:ShowTextOnly(txt)
    self._wtMainIcon:Collapsed()
    self._wtMainText:SelfHitTestInvisible()
    self._wtMainText_1:Collapsed()

    self._wtMainText:SetText(txt)
end

function IVTextIconComponent:ShowTwoTextOnly(txt1, txt2)
    self._wtMainIcon:Collapsed()
    self._wtMainText:SelfHitTestInvisible()
    self._wtMainText_1:SelfHitTestInvisible()

    self._wtMainText:SetText(txt1)
    self._wtMainText_1:SetText(txt2)
end

function IVTextIconComponent:ShowIconAndText(iconPath, txt)
    self._wtMainText_1:Collapsed()
    self._wtMainIcon:SelfHitTestInvisible()
    self._wtMainText:SelfHitTestInvisible()

    self._wtMainIcon:AsyncSetImagePath(iconPath, false)
    self._wtMainText:SetText(txt)
end

function IVTextIconComponent:ShowSecondText(txt)
    self._wtMainText_1:Visible()
    self._wtMainText_1:SetText(txt)
end

function IVTextIconComponent:bShowIcon(bShow, iconPath)
    if bShow then
        self._wtMainIcon:AsyncSetImagePath(iconPath, false)
        self._IconImagePath = iconPath
    else
        self._wtMainIcon:Collapsed()
    end
end

function IVTextIconComponent:SetTextStyle(style)
    self:SetCppValue("Type", style)
    self:BP_SetStyle()
end

function IVTextIconComponent:SetTextColorAndOpacity(slateColor)
    self._wtMainText:SetColorAndOpacity(slateColor)
end

function IVTextIconComponent:SetSecTextColorAndOpacity(slateColor)
    self._wtMainText_1:SetColorAndOpacity(slateColor)
end

function IVTextIconComponent:ShowStackNum(bForecShow)
    self._bForceShowStackNum = bForecShow
end

function IVTextIconComponent:SetCornerStyle()
    if self._textStyle == CommonWidgetConfig.EIVIconTextStyle.NeedBuy then
        return
    end

    -- 四角文本样式的变更主要与位置和itemview类型有关
    if self._compPos == self._selfPosition and self._itemview == self._selfIVMode then
        -- 由于某些需求会导致部分文本设置为红色并不会设置会原本状态，所以必须调用原本的蓝图方法将某些被更改的样式还原。
        self:SetTextStyle(self._cornerTextStyle)
    else
        self:SetStyleCorner()
    end
end

-- 设置四角文本样式
function IVTextIconComponent:SetStyleCorner()

    --[[
        配表：
            ComponentPositon        TextStyle
                1(左上)             1(typeTest)
                2(右上)             1(typeTest)
                3(左下)             1(typeTest)
                4(右下)             1(typeTest)
    ]]--
    if self._cornerTextStyle == 0 then
        -- 获取配置表信息
        local itemviewConfig = Module.CommonWidget.Field.itemviewConfig[self._itemview]
        -- 获取组件位置信息
        local compPos = self._compPos
        local StyleType = itemviewConfig.TextStyle
        for _, textStyle in ipairs(StyleType) do
            if textStyle.pos == compPos then
                self._cornerTextStyle = textStyle.Style
                break
            end
        end
    end
    self:SetTextStyle(self._cornerTextStyle)
    self._selfPosition = self._compPos
    self._selfIVMode = self._itemview
end

function IVTextIconComponent:SetCurMaxStyle(curNum, maxNum)
    self:ShowTextOnly(string.format("%s/%s", curNum, maxNum))
    if curNum < maxNum then
        self:SetTextColorAndOpacity(Facade.ColorManager:GetSlateColor("LightNegative"))
    else
        self:SetTextColorAndOpacity(Facade.ColorManager:GetSlateColor("Basic_White"))
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private

function IVTextIconComponent:_GenerateStyleFunctionMapping()
    if table.getkeynum(IVTextIconComponent.StyleFuncMapping) > 0 then
        return
    end
    IVTextIconComponent.StyleFuncMapping = {
        [CommonWidgetConfig.EIVIconTextStyle.None] = nil,
        [CommonWidgetConfig.EIVIconTextStyle.ItemName] = IVTextIconComponent._RefreshItemName,
        [CommonWidgetConfig.EIVIconTextStyle.Weight] = IVTextIconComponent._RefreshWeight,
        [CommonWidgetConfig.EIVIconTextStyle.Armor] = IVTextIconComponent._RefreshArmor,
        [CommonWidgetConfig.EIVIconTextStyle.Helmet] = IVTextIconComponent._RefreshHelmet,
        [CommonWidgetConfig.EIVIconTextStyle.Stack] = IVTextIconComponent._RefreshStack,
        [CommonWidgetConfig.EIVIconTextStyle.Binding] = IVTextIconComponent._RefreshBinding,
        [CommonWidgetConfig.EIVIconTextStyle.InSafebox] = IVTextIconComponent._RefreshInSafebox,
        [CommonWidgetConfig.EIVIconTextStyle.WeaponAmmo] = IVTextIconComponent._RefreshWeaponAmmo,
        [CommonWidgetConfig.EIVIconTextStyle.Mark] = IVTextIconComponent._RefreshMark,
        [CommonWidgetConfig.EIVIconTextStyle.IsEquippedSafebox] = IVTextIconComponent._RefreshIsEquippedSafebox,
        [CommonWidgetConfig.EIVIconTextStyle.UseNum] = IVTextIconComponent._RefreshUseNum,
        [CommonWidgetConfig.EIVIconTextStyle.NeedBuy] = IVTextIconComponent._RefreshNeedBuy,
        [CommonWidgetConfig.EIVIconTextStyle.Insurance] = IVTextIconComponent._RefreshInsurance,
        [CommonWidgetConfig.EIVIconTextStyle.InsuranceNum] = IVTextIconComponent._RefreshInsuranceNum,
        [CommonWidgetConfig.EIVIconTextStyle.BagCapacityExtend] = IVTextIconComponent._RefreshCapacityExtend,
        [CommonWidgetConfig.EIVIconTextStyle.Durability] = IVTextIconComponent._RefreshDurability,
        [CommonWidgetConfig.EIVIconTextStyle.BulletHint] = IVTextIconComponent._RefreshBulletHint,
        [CommonWidgetConfig.EIVIconTextStyle.WeaponBlueprintName] = IVTextIconComponent._RefreshWeaponBlueprintName,
        [CommonWidgetConfig.EIVIconTextStyle.DamagedMark] = IVTextIconComponent._ShowDamagedIcon,
        [CommonWidgetConfig.EIVIconTextStyle.LockIcon] = IVTextIconComponent._ShowLockIcon,
        [CommonWidgetConfig.EIVIconTextStyle.Countdown] = IVTextIconComponent._RefreshContainerCutdown,
        [CommonWidgetConfig.EIVIconTextStyle.SkinRarity] = IVTextIconComponent._RefreshMysticalSkin,
        [CommonWidgetConfig.EIVIconTextStyle.SpecialRarity] = IVTextIconComponent._RefreshSpecialMysticalSkin,
        [CommonWidgetConfig.EIVIconTextStyle.GunStar] = IVTextIconComponent._RefreshGunStar,
        -- [CommonWidgetConfig.EIVIconTextStyle.ExpireDate] = IVTextIconComponent._ShowContainerExpireDate,
        [CommonWidgetConfig.EIVIconTextStyle.Custom] = nil,
    }
end

function IVTextIconComponent:_GetStyleRefreshFunction(style)
    return IVTextIconComponent.StyleFuncMapping[style]
end

function IVTextIconComponent:_RefreshInsuranceNum()
    self._wtMainIcon:Collapsed()
    -- self:SetTextStyle(ETextStyle.ItemName)
    if self._item.num > 1 then
        self._wtMainText:Visible()
        self._wtMainText:SetText(Module.Insurance.Config.Loc.MaxStackCount .. self._item.num)
    else
        self._wtMainText:Collapsed()
    end
end

function IVTextIconComponent:_RefreshCapacityExtend()
    -- 已装备时不显示
    local inSlot = self._item and self._item.InSlot
    if inSlot and inSlot:IsEquipChestAndBagSlot() then
        self:Collapsed()
    end
    -- 暂时无图标
    local iconPath

    ---@type EquipmentFeature
    local equipmentFeature = self._item:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and (equipmentFeature:IsBag() or equipmentFeature:IsChestHanging()) then
        if equipmentFeature:IsChestHanging() then
            iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Merchant_Icon_0102.Common_Merchant_Icon_0102'"
        elseif equipmentFeature:IsBag() then
            iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_EquipClass_Icon_0001.Common_EquipClass_Icon_0001'"
        end

        local totalCapacity = ItemBaseTool.GetCapacityByEquipmentId(self._item.id)
        self._wtMainText:SetText(string.format("%d", totalCapacity))
        self._wtMainIcon:AsyncSetImagePath(iconPath, false)
        if not IsHD() then
            self._wtMainIcon:SetBrushSize(FVector2D(34, 34))
        end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshInsurance()
    self._wtMainIcon:Collapsed()
    -- self:SetTextStyle(ETextStyle.ItemName)
    if not string.isempty(self._item.shortName) then
        self._wtMainText:SetText(self._item.shortName)
    else
        self._wtMainText:SetText(self._item.name)
    end
end

-- function IVTextIconComponent:_RefreshItemName()
--     -- 此接口用于全局区分枪械皮肤名字和普通道具名字，后续可根据情况进行添加删除
--     local itemFeature = self._item and self._item:GetFeature(EFeatureType.Weapon)
--     local mysticalPropInfo
--     if itemFeature and itemFeature:IsWeapon() then
--         local weaponDesc = self._item:GetRawDescObj()
--         mysticalPropInfo = weaponDesc:GetSkinInfo()
--     end
--     if mysticalPropInfo and mysticalPropInfo.SkinId ~= 0 then
--         self:_RefreshWeaponBlueprintName()
--     else
--         self:_RefreshCommonItemName()
--     end
-- end

function IVTextIconComponent:_RefreshItemName()
    self._wtMainIcon:Collapsed()
    local bShowShadingQuality = true

    local shortName = self._item and self._item.shortName or ""
    local itemName = self._item and self._item.name or ""
    if not string.isempty(shortName) or not string.isempty(itemName) then
        local showStr = self:IsWHItemView() and shortName or itemName
        self:ShowTextOnly(showStr)
    else
        self:Collapsed()
    end

    local itemFeature = self._item and self._item:GetFeature()
    local isWeapon = itemFeature and itemFeature.IsWeapon and itemFeature:IsWeapon()
    local isAdapter = itemFeature and itemFeature.IsAdapter and itemFeature:IsAdapter()
    local isVehicle = self._item and self._item:GetFeature(EFeatureType.Vehicle)
    local currentGameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    -- mp武器和配件都不显示品阶色，列表类itemview也不显示品阶色
    local currentGameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow == EGameFlowStageType.Lobby and isAdapter and isVehicle then
        self:Collapsed()
        return
    end

    local quality = self._item.quality
    if isWeapon then
        local weaponDesc = self._item:GetRawDescObj()
        local skinid = weaponDesc and weaponDesc:GetSkinInfo() and weaponDesc:GetSkinInfo().SkinId
        bShowShadingQuality = skinid and skinid ~= 0
        quality = skinid ~= 0 and ItemHelperTool.GetQualityTypeById(skinid) or quality
    end
    -- 底部品阶
    if self:IsSpecialQuality() and quality > 0 and bShowShadingQuality then
        if self.shadingQuality then
            self.shadingQuality:SelfHitTestInvisible()
        else
            -- 加入底纹品阶
            local shadingCompWeakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.IVShadingQualityComponent, self._wtMainPanel)
            self.shadingQuality = getfromweak(shadingCompWeakUIIns)
        end
        -- 由于新接口在remove时会
        if self.shadingQuality then
            self._wtMainPanel:AddChild(self.shadingQuality)
            local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self.shadingQuality)
            canvasSlot:SetAutoSize(false)
            local commonAnchor = FAnchors()
            local commonOffset = FMargin(0, 0, 0, 0)
            commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
            commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
            canvasSlot:SetAnchors(commonAnchor)
            canvasSlot:SetOffsets(commonOffset)
            canvasSlot:SetZOrder(1)
            self.shadingQuality:SetCppValue("BpType", self._item.quality)
            self.shadingQuality:BP_SetType()
            self._wtMainText:SetAutoWrapText(false)
        end
    else
        if self.shadingQuality then
            self.shadingQuality:Collapsed()
        end
    end
end

function IVTextIconComponent:_RefreshWeight()
    local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0012.Common_ItemProp_Icon_0012'"
    self._wtMainIcon:AsyncSetImagePath(iconPath, false)

    if Module.CommonWidget:GetIsItemShowWeight() then
        self._wtMainText:SetText(string.format("%.1f", self._item.weight))
        -- if self.WBP_SlotCompIconLabel_in then
        --     self:PlayAnimation(self.WBP_SlotCompIconLabel_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        -- end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshArmor()
    -- 暂时无图标
    local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_ItemProp_Icon_0017.CommonHall_ItemProp_Icon_0017'"
    self._wtMainIcon:AsyncSetImagePath(iconPath, false)

    ---@type EquipmentFeature
    local equipmentFeature = self._item:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and equipmentFeature:IsBreastPlate() then
        -- if equipmentFeature.curDurability <= equipmentFeature.maxDurability * equipmentFeature.damagedDurability then
        if equipmentFeature:IsDamaged() then
            self._wtMainText:SetColorAndOpacity(Facade.ColorManager:GetSlateColor("LightNegative"))
        end
        if ItemOperaTool.CheckRunWarehouseLogic() then
            self._wtMainText:SetText(string.format("%d", math.floor(equipmentFeature.maxArmorHealth)))
        else
            self._wtMainText:SetText(string.format("%d/%d", math.floor(equipmentFeature.curArmorHealth),
                math.floor(equipmentFeature.maxArmorHealth)))
        end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshHelmet()
    -- 暂时无图标
    local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0018.Common_ItemProp_Icon_0018'"
    self._wtMainIcon:AsyncSetImagePath(iconPath, false)

    ---@type EquipmentFeature
    local equipmentFeature = self._item:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and equipmentFeature:IsHelmet() then
        self._wtMainText:SetText(string.format("%d%%", equipmentFeature.headDamagesReduction))
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshStack()
    self._wtMainIcon:Collapsed()
    self:_StopUsingAnim()
    if self._item.previousNum > self._item.num then
        if self._item:CheckItemBeingUsed() then
            self:_SetShowNum(self._item.previousNum)
        else
            self:_PlayUsingItemAnim()
        end
    elseif self._item.num > 1 or self._bForceShowStackNum then
        self:_SetShowNum(self._item.num)
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_SetShowNum(num)
    num = math.floor(num)
    if num <= 1 then
        if not hasdestroy(self) then
            self:Collapsed()
        end
    end
    if num <= 9999 then
        self._wtMainText:SetText(string.format("%s", MathUtil.GetNumberFormatStr(num)))
    elseif num <= 999999 then
        self._wtMainText:SetText(string.format("%sK", MathUtil.GetFristFloatDecimal(num/1000)))
    elseif num <= 99999999 then
        self._wtMainText:SetText(string.format("%sM", MathUtil.GetFristFloatDecimal(num/1000000)))
    else
        self._wtMainText:SetText(CommonWidgetConfig.Loc.CollectItemMaxShowNum)
    end
    -- local itemMainType = ItemHelperTool.GetMainTypeById(self._item.id)
    -- if itemMainType == EItemType.CollectionProp then
    --     if self._item.num > CommonWidgetConfig.CollectItemMaxShowNum then
    --         self._wtMainText:SetText(CommonWidgetConfig.Loc.CollectItemMaxShowNum)
    --     else
    --         self._wtMainText:SetText(string.format("%s", MathUtil.GetNumberFormatStr(num)))
    --     end
    -- else
    --     self._wtMainText:SetText(string.format("%s", MathUtil.GetNumberFormatStr(num)))
    -- end
end

function IVTextIconComponent:_PlayUsingItemAnim()
    self._curTweener = Facade.TweenManager:CreateTweener(Module.Looting.Config.USING_ITEM_STACK_NUM_ANIM_DURATION,
            {showNum = self._item.previousNum}, {showNum = self._item.num}, "linear")
    self._curTweener:OnUpdate(self, self._UpdateShowNum)
end

function IVTextIconComponent:_StopUsingAnim()
    if self._curTweener then
        Facade.TweenManager:RemoveTween(self._curTweener)
        self._curTweener = nil
        self._item.previousNum = 0
    end
end

function IVTextIconComponent:_UpdateShowNum(tween)
    local showNum = math.floor(tween.subject.showNum)
    if showNum <= self._item.num then
        self._item.previousNum = 0
    end
    self:_SetShowNum(showNum)
end

function IVTextIconComponent:_RefreshWeaponAmmo()
    ---@type WeaponFeature
    local weawponFeature = self._item:GetFeature(EFeatureType.Weapon)
    if weawponFeature and not weawponFeature:IsMeleeWeapon() then
        local iconPath, curNum, maxNum
        if ItemOperaTool.CheckRunWarehouseLogic() then
            curNum, maxNum = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(self._item:GetRawPropInfo())
            maxNum = string.format("/%d", maxNum)
        else
            -- showStr = string.format("%d/%d", weawponFeature:GetWeaponCurAmmoNum(),
            --     weawponFeature:GetWeaponClipSize())
            curNum = weawponFeature:GetWeaponCurAmmoNum()
            maxNum = string.format("/%d", weawponFeature:GetWeaponClipSize())
        end
        self:ShowTwoTextOnly(curNum, maxNum)
        -- self._wtMainText:SetText(curNum)
        -- self._wtMainText_1:SetText(maxNum)
        -- self._wtMainText_1:SelfHitTestInvisible()
        -- self._wtMainIcon:AsyncSetImagePath(iconPath, false)
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshDurability()
    local itemFeature = self._item:GetFeature()
    -- 判断该道具类是否存在且该道具是否有耐久度
    if itemFeature and itemFeature.isDurabilityItem then
        local curDurability = itemFeature.curDurability or 0
        local maxDurability = itemFeature.maxDurability or 0
        self._wtMainText:SetText(string.format("%d/%d", curDurability, maxDurability))
    elseif itemFeature:IsHelmet() or itemFeature:IsBreastPlate() then
        local curDurability = itemFeature.curDurability or 0
        local maxDurability = itemFeature.maxDurability or 0
        -- 在这里打个日志，查个bug（需要是error)
        logerror("_RefreshDurability", self._item.name, curDurability, maxDurability)
        self:ShowTwoTextOnly(string.format("%.1f", curDurability), string.format("/%.1f", maxDurability))
        -- 若为破损状态
        if curDurability <= MathUtil.GetRoundingNum(maxDurability / 3) then
            TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtMainText, "C104")
        else
        end
    else
        self:Collapsed()
    end
end

-- 破损角标
function IVTextIconComponent:_ShowDamagedIcon()
    -- local itemFeature = self._item:GetFeature(EFeatureType.Equipment)
    -- if itemFeature and (itemFeature:IsHelmet() or itemFeature:IsBreastPlate()) and itemFeature.curDurability <= 0 then
    --     local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0024.Common_ItemProp_Icon_0024'"
    --     self:ShowIconOnly(iconPath)
    --     self._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("LightNegative"))
    -- else
    --     self:Collapsed()
    -- end
    self:Collapsed()
end

function IVTextIconComponent:_ShowLockIcon()
    local inSlot = self._item and self._item.InSlot
    if inSlot and Module.Inventory.Config.SlotsNotAllowOperate[inSlot.SlotType] then
        local iconPath = CommonWidgetConfig.ITEM_VIEW_LOCK_ICON_PATH
        self:ShowIconOnly(iconPath)
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshContainerCutdown()
    if not ItemOperaTool.CheckRunWarehouseLogic() then
        self:Collapsed()
        return
    end
    local inSlot = self._item and self._item.InSlot
    local itemFeature = self._item and self._item:GetFeature(EFeatureType.Equipment)

    if inSlot and (inSlot.SlotType == ESlotType.SafeBox or inSlot.SlotType == ESlotType.KeyChain) and -- 安全箱或卡包
       itemFeature and itemFeature._expireTimes and itemFeature._expireTimes > 0 and                  -- 有合法的过期时间
       not itemFeature:GetExpiredStatus()                                                             -- 没有已经过期
    then
        -- 显示倒计时
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'"
        local curDown = CommonWidgetLogic.GetRemainTime(itemFeature._expireTimes)
        self:ShowIconAndText(iconPath, curDown)
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_ShowContainerExpireDate()
    -- 到期的时间信息
    --[[
    local itemFeature = self._item:GetFeature(EFeatureType.Equipment)
    if itemFeature and (itemFeature:IsHelmet() or itemFeature:IsBreastPlate()) and itemFeature.curDurability <= 0 then
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0024.Common_ItemProp_Icon_0024'"
        self:ShowIconOnly(iconPath)
        self._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("LightNegative"))
    else
        self:Collapsed()
    end
    ]]--
end

function IVTextIconComponent:_RefreshBulletHint()
    if self._item:IsBullet() then
        local bShowIcon = false
        local slotGroup = Server.ArmedForceServer:CheckIsRentalStatus() and ESlotGroup.MainRental or ESlotGroup.Player
        local weaponSlot2Check = {ESlotType.MainWeaponLeft, ESlotType.MainWeaponRight, ESlotType.Pistrol}
        for _, slotType in ipairs(weaponSlot2Check) do
            local slot = Server.InventoryServer:GetSlot(slotType, slotGroup)
            local weapon = slot:GetEquipItem()
            if weapon and ItemOperaTool.CheckLoadingAmmoAvailable(self._item, weapon) == ELoadAmmoResult.Success then
                bShowIcon = true
                break
            end
        end

        if bShowIcon then
            local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Assembly_Icon_0009.Common_Assembly_Icon_0009'"
            self:ShowIconOnly(iconPath)
            -- self._wtMainIcon:SetColorAndOpacity(ColorUtil.GetLinearColorByHex("#ffffff"))
            self._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Basic_White"))
        else
            self:Collapsed()
        end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshBinding()
    local bShowLockIcon = self._item and self._item.InSlot 
    and Module.Inventory.Config.SlotsNotAllowOperate[self._item.InSlot.SlotType]
    and self._item.InSlot:GetSlotGroup() == ESlotGroup.Player
    if self._item:CheckIsTeammateBind() and not bShowLockIcon then
        -- 设置默认样式
        self:SetTextStyle(8)
        -- 队友绑定
        local teamBindIconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0007.Common_ItemProp_Icon_0007'"
        self:ShowIconOnly(teamBindIconPath)
    elseif self._item:CheckIsBind() and not bShowLockIcon then
        -- 普通绑定
        local bindIconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0001.Common_ItemProp_Icon_0001'"
        self:ShowIconOnly(bindIconPath)
    elseif bShowLockIcon then
        local iconPath = CommonWidgetConfig.ITEM_VIEW_LOCK_ICON_PATH
        self:ShowIconOnly(iconPath)
        if DFHD_LUA == 1 then
            self:RemoveEvent("OnMouseEnterEvent")
            self:RemoveEvent("OnMouseLeaveEvent")
            self:Event("OnMouseEnterEvent",self._OnMouseEnterEvent,self)
            self:Event("OnMouseLeaveEvent",self._OnMouseLeaveEvent,self)
            self:Visible() -- 开启射线碰撞检测，不然无法与鼠标交互
            self:SetCppValue("bHandleClick", true)
            self:SetCppValue("bHandleClickR", true)
            self:SetCppValue("bHandleClickMid", true)
        end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_OnMouseEnterEvent()
    CommonWidgetConfig.Events.evtIVTextIconMouseEnter:Invoke(self)
end

function IVTextIconComponent:_OnMouseLeaveEvent()
    CommonWidgetConfig.Events.evtIVTextIconMouseLeave:Invoke(self)
end

function IVTextIconComponent:GetIconImagePath()
    return self._IconImagePath
end

function IVTextIconComponent:GetAssignedItemView()
    return CommonWidgetModule:GetAssignedItemView(self._item)
end

function IVTextIconComponent:_RefreshInSafebox()
    self._wtMainText:Collapsed()

    local inSlot = self._item.InSlot
    -- if inSlot and inSlot.SlotType == ESlotType.SafeBoxContainer then
    if inSlot and (inSlot:IsEquipSafeBoxAndKeyChain() or inSlot:IsSOLMeleeWeaponSlot()) then
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Switch.Common_De_Switch'"
        self._wtMainIcon:AsyncSetImagePath(iconPath, false)
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshMark()
    local bMark = Server.InventoryServer:CheckItemMarked(self._item.id)
    if not bMark or ItemOperaTool.CheckRunWarehouseLogic()
        or self._item.InSlot:GetSlotGroup() == ESlotGroup.Player then
        self:Collapsed()
        return
    end

    local iconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0003.Common_ItemProp_Icon_0003'"
    self._wtMainIcon:AsyncSetImagePath(iconPath, false)
    self._wtMainText:Collapsed()
end

function IVTextIconComponent:_RefreshIsEquippedSafebox()
    self._wtMainText:Collapsed()
    local slot = self._item and self._item.InSlot or nil
    if slot and slot.SlotType == ESlotType.SafeBox then
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_ItemProp_Icon_0014.CommonHall_ItemProp_Icon_0014'"
        self._wtMainIcon:AsyncSetImagePath(iconPath, false)
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshUseNum()
    -- 使用次数
    local healthFeature = self._item and self._item:GetFeature(EFeatureType.Health) or nil
    if healthFeature then
        local maxDurability = healthFeature:GetDurabilityMaxValue()
        if maxDurability > 0 then
            local curDurability = healthFeature:GetDurabilityCurValue()
            if healthFeature:IsDurabilityMedicine() then
                self._wtMainText:SetText(curDurability)
                if healthFeature:IsArmorMedicine() then
                    self._wtMainIcon:AsyncSetImagePath(CommonWidgetConfig.ArmorMedicineIconPath, false)
                else
                    self._wtMainIcon:AsyncSetImagePath(CommonWidgetConfig.DragMedicineIconPath, false)
                end
                self._wtMainIcon:SelfHitTestInvisible()
            else
                if curDurability > maxDurability then
                    logerror("_RefreshUseNum", self._item.name, self._item.gid, curDurability, maxDurability)
                end
                self:ShowTwoTextOnly(curDurability, string.format("/%d", maxDurability))
            end
        end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshNeedBuy()
    self._wtMainText:Collapsed()
    -- 特殊处理，只有配装中需要购买的才有这个标志
    local bNeedBuy = self._item.bNeedBuy or Server.ShopStationServer:CheckItemIsBuyPreview(self._item)
    if bNeedBuy then
        local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_De_Buy.CommonHall_De_Buy'"
        self._wtMainIcon:AsyncSetImagePath(iconPath, false)
        self._wtMainIcon:SetBrushSize(FVector2D(38, 38))
        self._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Highlight01"))
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshWeaponBlueprintName()
    -- 武器则获取皮肤，皮肤则直接使用
    local itemFeature = self._item and self._item:GetFeature(EFeatureType.Weapon)
    local mysticalPropInfo
    if itemFeature and (itemFeature:IsWeapon() or itemFeature:IsWeaponSkin()) then
        local weaponDesc = self._item:GetRawDescObj()
        mysticalPropInfo = weaponDesc:GetSkinInfo()
    else
        loginfo("当前道具不是武器或皮肤")
        return
    end
    local itemInfo
    local mysticalName
    if mysticalPropInfo and mysticalPropInfo.SkinId ~= 0 then
        itemInfo = ItemConfigTool.GetItemConfigById(mysticalPropInfo.SkinId)
        if not string.isempty(mysticalPropInfo.CustomName) then
            mysticalName = mysticalPropInfo.CustomName
        else
            mysticalName = itemInfo and itemInfo.Name or ""
        end
        self:ShowTextOnly(mysticalName)
    else
        self:Collapsed()
        return
    end

    -- 底部品阶
    if itemInfo and itemInfo.quality > 0 then
        if self.shadingQuality then
            self.shadingQuality:SelfHitTestInvisible()
        else
            -- 加入底纹品阶
            local shadingCompWeakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.IVShadingQualityComponent, self._wtMainPanel)
            self.shadingQuality = getfromweak(shadingCompWeakUIIns)
            local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self.shadingQuality)
            canvasSlot:SetAutoSize(false)
            local commonAnchor = FAnchors()
            local commonOffset = FMargin(0, 0, 0, 0)
            commonAnchor.Minimum = LuaGlobalConst.TOP_LEFT_VECTOR
            commonAnchor.Maximum = LuaGlobalConst.BOTTOM_RIGHT_VECTOR
            canvasSlot:SetAnchors(commonAnchor)
            canvasSlot:SetOffsets(commonOffset)
            canvasSlot:SetZOrder(1)
        end
        self.shadingQuality:SetCppValue("BpType", itemInfo.quality)
        self.shadingQuality:BP_SetType()
        self._wtMainText:SetAutoWrapText(false)
    else
        loginfo("itemInfo is nil, please check")
    end
end

-- 目前玄学有武器玄学皮肤和玄学挂饰，所以得分开判断
function IVTextIconComponent:_RefreshMysticalSkin()
    local itemFeature = self._item and self._item:GetFeatureType()
    if itemFeature and itemFeature == EFeatureType.Weapon then
        self:_RefreshMysticalWeaponSkin()
    elseif itemFeature and itemFeature == EFeatureType.Adapter then
        self:_RefreshMysticalPendant()
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshMysticalWeaponSkin()
    -- 武器则获取皮肤，皮肤则直接使用
    local itemFeature = self._item and self._item:GetFeature(EFeatureType.Weapon)
    local mysticalPropInfo
    -- 近战武器排除
    if itemFeature and itemFeature:IsMeleeWeapon() then
        self:Collapsed()
        return
    end

    if itemFeature and (itemFeature:IsWeapon() or itemFeature:IsWeaponSkin()) then
        local weaponDesc = self._item:GetRawDescObj()
        mysticalPropInfo = weaponDesc and weaponDesc:GetSkinInfo()
    else
        loginfo("当前道具不是武器")
        self:Collapsed()
        return
    end

    if mysticalPropInfo and ItemHelperTool.GetThirdTypeById(mysticalPropInfo.SkinId) == 0 then
        -- 判断是预设玄学皮肤还是实际拥有的玄学皮肤
        if mysticalPropInfo.SkinGid ~= 0 then
            local wearAndRatiryTxt = Module.Inventory:GetMysticalRatityInfoBySkinId(mysticalPropInfo)
            self:ShowTextOnly(wearAndRatiryTxt)
        else
            local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'"
            local txt = Module.Inventory.Config.Loc.MysticalSkinTxt
            self:ShowIconAndText(iconPath, txt)
        end
    else
        self:Collapsed()
    end
end

function IVTextIconComponent:_RefreshMysticalPendant()
    -- 配件中的挂饰子分支
    local mysticalPendantPropInfo
    local itemFeature = self._item and self._item:GetFeature(EFeatureType.Adapter)
    if itemFeature and itemFeature:IsPendant() then
        mysticalPendantPropInfo = self._item and self._item.rawPropInfo and self._item.rawPropInfo.mystical_pendant_data
    else
        loginfo("当前道具不是挂饰")
        self:Collapsed()
        return
    end

    if ItemHelperTool.IsMysticalPendant(self._item.id) then
        if mysticalPendantPropInfo then
            -- 所有数据从mysticalPendantPropInfo中获取
            local mystical_pendant_apperanceInfo = mysticalPendantPropInfo.show and mysticalPendantPropInfo.show[1] and mysticalPendantPropInfo.show[1].appearance
            local pendantMysticalId = mystical_pendant_apperanceInfo and mystical_pendant_apperanceInfo.id
            local pendantMysticalRow = ItemConfigTool.GetPendantMysticalTable(pendantMysticalId)        -- 获取挂饰的方案信息
    
            local detailInfos = {}
            if pendantMysticalRow then
                if pendantMysticalRow.DetailId01 > 0 then
                    local detailInfo01 = ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId01)
                    table.insert(detailInfos, detailInfo01)
                end
                if pendantMysticalRow.DetailId02 > 0 then
                    local detailInfo02 = ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId02)
                    table.insert(detailInfos, detailInfo02)
                end
                if pendantMysticalRow.DetailId03 > 0 then
                    local detailInfo03 = ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId03)
                    table.insert(detailInfos, detailInfo03)
                end
            end
    
            -- 根据玄学挂饰信息组和道具品阶获取对应的品质信息
            local itemQuality = self._item.quality
            local ratityTxt = Module.Inventory:GetMysticalPendantInfoByDetailInfos(detailInfos, itemQuality)
            self:ShowTextOnly(ratityTxt)
        else
            local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'"
            local txt = Module.Inventory.Config.Loc.MysticalSkinTxt
            self:ShowIconAndText(iconPath, txt)
        end
    else
        -- 非典藏挂饰
        self:Collapsed()
    end
    -- if mysticalPendantPropInfo then
    --     -- 所有数据从mysticalPendantPropInfo中获取
    --     local mystical_pendant_apperanceInfo = mysticalPendantPropInfo.show and mysticalPendantPropInfo.show[1] and mysticalPendantPropInfo.show[1].appearance
    --     local pendantMysticalId = mystical_pendant_apperanceInfo and mystical_pendant_apperanceInfo.id
    --     local pendantMysticalRow = ItemConfigTool.GetPendantMysticalTable(pendantMysticalId)        -- 获取挂饰的方案信息

    --     local detailInfos = {}
    --     if pendantMysticalRow then
    --         if pendantMysticalRow.DetailId01 > 0 then
    --             local detailInfo01 = ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId01)
    --             table.insert(detailInfos, detailInfo01)
    --         end
    --         if pendantMysticalRow.DetailId02 > 0 then
    --             local detailInfo02 = ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId02)
    --             table.insert(detailInfos, detailInfo02)
    --         end
    --         if pendantMysticalRow.DetailId03 > 0 then
    --             local detailInfo03 = ItemConfigTool.GetPendantMysticalDetailInfoTable(pendantMysticalRow.DetailId03)
    --             table.insert(detailInfos, detailInfo03)
    --         end
    --     end

    --     -- 根据玄学挂饰信息组获取对应的品质信息
    --     local ratityTxt = Module.Inventory:GetMysticalPendantInfoByDetailInfos(detailInfos)
    --     self:ShowTextOnly(ratityTxt)
    --     --[[
    --     -- 判断是预设玄学皮肤还是实际拥有的玄学皮肤
    --     if mysticalPendantPropInfo.SkinGid ~= 0 then
    --         -- local wearAndRatiryTxt = Module.Inventory:GetMysticalRatityInfoBySkinId(mysticalPropInfo)
    --         -- self:ShowTextOnly(wearAndRatiryTxt)
    --     else
    --         -- local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'"
    --         -- local txt = Module.Inventory.Config.Loc.MysticalSkinTxt
    --         -- self:ShowIconAndText(iconPath, txt)
    --     end
    --     ]]--
    -- else
    --     self:Collapsed()
    -- end
end

-- 展示武器的星级信息
function IVTextIconComponent:_RefreshGunStar()
    -- 只有大战场才需要显示
    local currentGameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow == EGameFlowStageType.Lobby then
        self:Collapsed()
    end
    local weaponFeature = self._item:GetFeature(EFeatureType.Weapon)
    if weaponFeature and weaponFeature.GetStarNum and weaponFeature:GetStarNum() >= 1 then
        self:ShowIconAndText("PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Arms_Icon_0101.Common_Arms_Icon_0101'", weaponFeature:GetStarNum())
        -- self._wtMainIcon:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Highlight01"))
        self:SelfHitTestInvisible()
    else
        self:Collapsed()
    end
end


-- 特供版，用于防止超框去掉文本中的成色两字
function IVTextIconComponent:_RefreshSpecialMysticalSkin()
    -- 武器则获取皮肤，皮肤则直接使用
    local itemFeature = self._item and self._item:GetFeature(EFeatureType.Weapon)
    local mysticalPropInfo
    -- 近战武器排除
    if itemFeature and itemFeature:IsMeleeWeapon() then
        self:Collapsed()
        return
    end

    if itemFeature and (itemFeature:IsWeapon() or itemFeature:IsWeaponSkin()) then
        local weaponDesc = self._item:GetRawDescObj()
        mysticalPropInfo = weaponDesc and weaponDesc:GetSkinInfo()
    else
        loginfo("当前道具不是武器")
        self:Collapsed()
        return
    end

    if mysticalPropInfo and ItemHelperTool.GetThirdTypeById(mysticalPropInfo.SkinId) == 0 then
        -- 判断是预设玄学皮肤还是实际拥有的玄学皮肤
        if mysticalPropInfo.SkinGid ~= 0 then
            local wearAndRatiryTxt = Module.Inventory:GetMysticalRatityInfoBySkinId(mysticalPropInfo, true)
            self:ShowTextOnly(wearAndRatiryTxt)
        else
            local iconPath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Market_Icon_0002.CommonHall_Market_Icon_0002'"
            local txt = Module.Inventory.Config.Loc.MysticalSkinTxt
            self:ShowIconAndText(iconPath, txt)
        end
    else
        self:Collapsed()
    end
end

--endregion
-----------------------------------------------------------------------

return IVTextIconComponent
