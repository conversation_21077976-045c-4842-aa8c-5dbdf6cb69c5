----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
-- END MODIFICATION

---@class FriendLatelyHD : LuaUIBaseView
local FriendLatelyHD = ui("FriendLatelyHD")
local LIST_MAX_NUMBER = 16
function FriendLatelyHD:Ctor()
    loginfo("FriendLatelyHD:Ctor")
    self._wtCoPlayerList = UIUtil.WndScrollGridBox(self, "wtLatelyList", self._OnGetPlayerCount, self._OnProcessItemWidget)
    self._wtEmptyPanel = self:Wnd("EmptySlot", UIWidgetBase)
    self._wtPageText = self:Wnd("wtPageText", UITextBlock)
    self._wtLeftBtn = self:Wnd("wtLeftBtn", UIButton)
    self._wtLeftBtn:Event("OnClicked", self._OnBtnLeftClick, self)
    self._wtRightBtn = self:Wnd("wtRightBtn", UIButton)
    self._wtRightBtn:Event("OnClicked", self._OnBtnRightClick, self)

    self._wtLeftBtn:Collapsed()
    self._wtRightBtn:Collapsed()
    self._wtPageText:Collapsed()

    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._OnHover, self._OnUnhovered)

    self._playerList = {}

    self._index = 1

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtRootPanel = self:Wnd("Panel_Right", UIWidgetBase)
        self._wtCheckInstruction = self:Wnd("wtCommonCheckInstruction", UIWidgetBase)
    end
    --- END MODIFICATION
end

function FriendLatelyHD:OnOpen()
    self:AddListeners()
    Server.FriendServer:FetchCoPlayerList()
end

--- BEGIN MODIFICATION @ VIRTUOS
function FriendLatelyHD:OnShowBegin()
    if IsHD() then
        self:_EnableGamepadFeature()
    end
end
--- END MODIFICATION

-- UI监听事件、协议
function FriendLatelyHD:AddListeners()
    self:AddLuaEvent(Server.FriendServer.Events.evtFriendCoPlayerList, self._OnGetRecentPlayerList, self)
end

function FriendLatelyHD:OnHideBegin()
    self:ReleaseTimer()

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
function FriendLatelyHD:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtEmptyPanel)
end

function FriendLatelyHD:RefreshPageNumber()
    self._maxPage = math.ceil(#self._playerList / LIST_MAX_NUMBER)
    local isOpen = self._maxPage > 1
    if isOpen then
        self._wtLeftBtn:Visible()
        self._wtRightBtn:Visible()
        self._wtPageText:Visible()
        local parms = {
            ["curNum"] = self._index,
            ["maxNum"] = self._maxPage
        }
        self._wtPageText:SetText(StringUtil.Key2StrFormat(Module.Friend.Config.NumFormat, parms))

        if self._index == 1 then
            self._wtLeftBtn:Collapsed()
        end
        if self._index == self._maxPage then
            self._wtRightBtn:Collapsed()
        end
    else
        self._wtLeftBtn:Collapsed()
        self._wtRightBtn:Collapsed()
        self._wtPageText:Collapsed()
    end
end

function FriendLatelyHD:_OnBtnLeftClick()
    if self._index > 1  then
        self._index = self._index - 1
        self:RefreshPageNumber()
        self._wtCoPlayerList:RefreshAllItems()
    end
end

function FriendLatelyHD:_OnBtnRightClick()
    if self._index < self._maxPage  then
        self._index = self._index + 1
        self:RefreshPageNumber()
        self._wtCoPlayerList:RefreshAllItems()
    end
end

function FriendLatelyHD:_OnGetPlayerCount()
    local size = #self._playerList
    local num = size < self._index * LIST_MAX_NUMBER and (size - (self._index - 1) * LIST_MAX_NUMBER) or LIST_MAX_NUMBER
    return num
end

function FriendLatelyHD:_OnProcessItemWidget(i, itemWidget)
    local playerInfo = self._playerList[LIST_MAX_NUMBER * (self._index - 1) + i]
    itemWidget:ShowUI(playerInfo)
end

function FriendLatelyHD:_OnGetRecentPlayerList(player_list)
    if #player_list == 0 then
        self:CreateNothingUIByText()
    else
        self._wtEmptyPanel:Collapsed()
        self._playerList = player_list
        self._playerIdList = {}
        for i, playerInfo in ipairs(player_list) do
            table.insert(self._playerIdList,playerInfo.player_id)
        end
        self._LatelyListRefresh = Timer:NewIns(5, 0)
        self._LatelyListRefresh:AddListener(self._GetLateLyPlayerState, self)
        self._LatelyListRefresh:Start()
        self._wtCoPlayerList:RefreshAllItems()
    end
    self:RefreshPageNumber()
end

function FriendLatelyHD:OnClose()
    self:RemoveAllLuaEvent()
    self:ReleaseTimer()
end

function FriendLatelyHD:ReleaseTimer()
    if self._LatelyListRefresh then
        self._LatelyListRefresh:Release()
        self._LatelyListRefresh = nil
    end
end

function FriendLatelyHD:_GetLateLyPlayerState()
    local CallFunction = CreateCallBack(self._RefreshFriendState, self)
    Server.FriendServer:ReFetchFriendStateList(CallFunction, self._playerIdList)
end

function FriendLatelyHD:_RefreshFriendState(stateList)
    for i, info in ipairs(self._playerList) do
        local stateInfo = stateList[info.player_id]
        if stateInfo then
            self._playerList[i].state = stateInfo.state
            self._playerList[i].fighting_time = stateInfo.fighting_time
            self._playerList[i].member_num = stateInfo.member_num
            self._playerList[i].team_id = stateInfo.team_id
            self._playerList[i].mode_info = stateInfo.mode_info
            Server.FriendServer:UpdateFriendLoginInvisible(info.player_id, stateInfo.invisible)
            --- BEGIN MODIFICATION @ VIRTUOS: do not refresh plat id if player is offline.
            if stateInfo.state ~= GlobalPlayerStateEnums.EPlayerState_Offline then
                self._playerList[i].plat = stateInfo.PlatID
            end
            --- END MODIFICATION
        end
    end
    self._wtCoPlayerList:RefreshAllItems()
end

function FriendLatelyHD:_OnHover()
    if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
    if self._wtDFTipsAnchor then
        self.hoverHandle = Module.CommonTips:ShowAssembledTips({{
            id = UIName2ID.Assembled_CommonMessageTips_V2,
            data = {textContent  = Module.Friend.Config.LatelyTips}
        }},self._wtDFTipsAnchor)
    end
end

function FriendLatelyHD:_OnUnhovered()
    if self.hoverHandle and self._wtDFTipsAnchor then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

function FriendLatelyHD:CreateNothingUIByText()
    if not self._instanceID then
        self._wtEmptyPanel:SelfHitTestInvisible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtEmptyPanel)
        self._instanceID = instanceID
        local emptyBg = getfromweak(weakUIIns)
        if emptyBg then
            emptyBg:BP_SetText(Module.Friend.Config.NothingLately)
            emptyBg:BP_SetTypeWithParam(1)
            emptyBg:Visible()
        end
    else
        self._wtEmptyPanel:SelfHitTestInvisible()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS
function FriendLatelyHD:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 必须要显示的时候才注册gamepad相关feature
    if not self:IsVisible() then
        return
    end

    if self._wtRootPanel and self._wtRootNavGroup == nil then
        self._wtRootNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootPanel, self, "Hittest")

        if self._wtCoPlayerList and self._wtRootNavGroup then
            self._wtRootNavGroup:AddNavWidgetToArray(self._wtCoPlayerList)
        end

        if self._wtCheckInstruction and self._wtRootNavGroup then
            self._wtRootNavGroup:AddNavWidgetToArray(self._wtCheckInstruction)
        end 
    end

    -- 绑定多输入设备切换事件
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
        -- 初始化
        local curInpurtType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInpurtType)
    end
end

function FriendLatelyHD:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 移除导航组
    WidgetUtil.RemoveNavigationGroup(self)
    self._wtRootNavGroup = nil
    -- 注销输入设备切换事件
    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end
end

function FriendLatelyHD:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end
    
    if WidgetUtil.IsGamepad() then
        -- 设置底部按键提示
        local summaryList = {}
        table.insert(summaryList, {actionName = "Select",func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
    else
        -- 清空底部按键提示
        local summaryList = {}
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
    end
end
--- END MODIFICATION

return FriendLatelyHD