----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------



local CommonBarViewLogic = {}
local WidgetUtil                    = require "DFM.YxFramework.Util.WidgetUtil"
--------------------------------------------------------------------------
--- top bar 打开关闭
--------------------------------------------------------------------------
CommonBarViewLogic.ShowCommonBarBasisViewProcess = function(loadCallback)
    local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    if commonBarBasisView and not hasdestroy(commonBarBasisView) then
        loginfo('CommonBarViewLogic.ShowCommonBarBasisViewProcess 已经创建过')
    else
        Module.CommonBar.Field:SetCommonBarBasisView(nil)
        local OnLoadFin = function(uiIns)
            Module.CommonBar.Field:SetCommonBarBasisView(uiIns)
            if loadCallback then
                trycall(loadCallback)
            end
            Module.CommonBar.Config.evtOnCommonBarBasisViewReady:Invoke(uiIns)
        end
    
        if DFHD_LUA == 1 then
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonBarBasisView, OnLoadFin)
        else
            Facade.UIManager:AsyncShowUI(UIName2ID.CommonBarBasisView, OnLoadFin)
        end
    end
end

CommonBarViewLogic.CloseCommonBarBasisViewProcess = function()
    local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    if not hasdestroy(commonBarBasisView) then
        Facade.UIManager:CloseUI(commonBarBasisView)
    end
    Module.CommonBar.Field:SetCommonBarBasisView(nil)
end

CommonBarViewLogic.ShowTopBarProcess = function(loadCallback)
    CommonBarViewLogic.CloseTopBarProcess()
    local OnLoadFin = function(uiIns)
        Module.CommonBar.Field:SetTopBar(uiIns)
        if DFHD_LUA ==  1 then
            -- uiIns:Hide(true, true)
            -- 已内部处理初始化隐藏
        else
            local bFrontEnd = Facade.GameFlowManager:CheckIsInFrontEnd()
            if not bFrontEnd then
                Timer.DelayCall(0.01,function (uiIns)
                    uiIns:Collapsed()
                end, uiIns)
            end
        end
       
        if loadCallback then
            trycall(loadCallback)
        end
    end

    if DFHD_LUA == 1 then
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
            Facade.UIManager:AsyncShowUICustomlized(UIName2ID.TopBarHD, {}, OnLoadFin)
        else
            Facade.UIManager:AsyncShowUICustomlized(
                UIName2ID.TopBarHD,
                {
                    UIName2ID.CurrencyIconNumHD,
                    UIName2ID.TopBarAvatarMainHD,
                    UIName2ID.SideBarGamepad
                }, 
                OnLoadFin)
        end
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.TopBar, OnLoadFin)
    end
end

CommonBarViewLogic.CloseTopBarProcess = function()
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        Facade.UIManager:CloseUI(topBar)
    end
    Module.CommonBar.Field:SetTopBar(nil)
end

CommonBarViewLogic.ShowBottomBarProcess = function(loadCallback)
    CommonBarViewLogic.CloseBottomBarProcess()
    local OnLoadFin = function(uiIns)
        Module.CommonBar.Field:SetBottomBar(uiIns)
        if DFHD_LUA == 1 then
            uiIns:Hide(true, true)
        else
            uiIns:Collapsed()
        end
        if loadCallback then
            trycall(loadCallback)
        end
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.BottomBarHD, OnLoadFin)
end

CommonBarViewLogic.CloseBottomBarProcess = function()
    local bottomBar = Module.CommonBar.Field:GetBottomBar()
    if bottomBar then
        Facade.UIManager:CloseUI(bottomBar)
    end
    Module.CommonBar.Field:SetBottomBar(nil)
end

--------------------------------------------------------------------------
--- switchmode bar
--------------------------------------------------------------------------
CommonBarViewLogic.ShowBarSwitchModeProcess = function(loadCallback)
    if DFHD_LUA == 1 and not CloseModuleType.bIsCloseModeSwitch then
        if Server.MatchServer:GetIsMatching() then
            Module.CommonTips:ShowSimpleTip(Module.ArmedForce.Config.Loc.InMatching)
            return
        end
        local barSwitchMode = Module.CommonBar.Field:GetBarSwitchMode()
        if barSwitchMode and not hasdestroy(barSwitchMode) then
            loginfo('CommonBarViewLogic.ShowBarSwitchModeProcess 已经创建过')
        else
            logwarning("CommonBarViewLogic.ShowBarSwitchModeProcess")
            WidgetUtil.SetUserFocusToGameViewport()
            Module.CommonBar.Field:SetBarSwitchMode(nil)
            local OnLoadFin = function(uiIns)
                Module.CommonBar.Field:SetBarSwitchMode(uiIns)
                if loadCallback then
                    trycall(loadCallback)
                end
                loginfo('CommonBarViewLogic.ShowBarSwitchModeProcess 创建成功')
            end
            Facade.UIManager:AsyncShowUI(UIName2ID.BarSwitchModeTopPanel, OnLoadFin)
            loginfo('CommonBarViewLogic.ShowBarSwitchModeProcess 创建开始')
        end
    end
end

--- 临时针对BHD做的不隐藏UI的抽屉
CommonBarViewLogic.ShowBHDBarSwitchModeProcess = function(loadCallback)
    if DFHD_LUA == 1 and not CloseModuleType.bIsCloseModeSwitch then
        local barSwitchMode = Module.CommonBar.Field:GetBarSwitchMode()
        if barSwitchMode and not hasdestroy(barSwitchMode) then
            loginfo('CommonBarViewLogic.ShowBarSwitchModeProcess 已经创建过')
        else
            logwarning("CommonBarViewLogic.ShowBarSwitchModeProcess")
            
            Module.CommonBar.Field:SetBarSwitchMode(nil)
            local OnLoadFin = function(uiIns)
                Module.CommonBar.Field:SetBarSwitchMode(uiIns)
                if loadCallback then
                    trycall(loadCallback)
                end
                loginfo('CommonBarViewLogic.ShowBarSwitchModeProcess 创建成功')
            end
            Facade.UIManager:AsyncShowUI(UIName2ID.BHDBarSwitchModeTopPanel, OnLoadFin)
            loginfo('CommonBarViewLogic.ShowBarSwitchModeProcess 创建开始')
        end
    end
end

CommonBarViewLogic.HideBarSwitchModeProcess = function()
    if DFHD_LUA == 1 and not CloseModuleType.bIsCloseModeSwitch then
        local barSwitchMode = Module.CommonBar.Field:GetBarSwitchMode()
        if barSwitchMode and not hasdestroy(barSwitchMode) then
            barSwitchMode:Hide(true, true)
        else
            Module.CommonBar.Field:SetBarSwitchMode(nil)
        end
    end
end

CommonBarViewLogic.CloseBarSwitchModeProcess = function()
    if DFHD_LUA == 1 and not CloseModuleType.bIsCloseModeSwitch then
        local barSwitchMode = Module.CommonBar.Field:GetBarSwitchMode()
        if barSwitchMode then
            Facade.UIManager:CloseUI(barSwitchMode)
        end
        Module.CommonBar.Field:SetBarSwitchMode(nil)
    end
end


CommonBarViewLogic.DoBarSwitchModeProcess = function()
    if DFHD_LUA == 1 and not CloseModuleType.bIsCloseModeSwitch then
        local barSwitchMode = Module.CommonBar.Field:GetBarSwitchMode()
        if barSwitchMode then
            barSwitchMode:DoSwitchModePanel()
        end
    end
end

--------------------------------------------------------------------------
--- top/bottom bar 获取操作
--------------------------------------------------------------------------
CommonBarViewLogic.GetTopBarTabGroupWidgetProcess = function(tabLevel)
    local topBar = Module.CommonBar.Field:GetTopBar()
    return (topBar and topBar.GetTabGroupWidget) and topBar:GetTabGroupWidget(tabLevel) or nil
end

CommonBarViewLogic.ForceInvokeBackProcess = function()
    Module.CommonBar.Config.evtBackBtnClicked:Invoke()
end

--------------------------------------------------------------------------
--- top/bottom bar 手动操作
--------------------------------------------------------------------------
--- 手动change非栈UI导致的顶部bar可见性
CommonBarViewLogic.SetTopBarVisibleProcess = function(bVisible)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:SetTopBarVisible(bVisible)
    end

    --- @todo 临时方案，让bottombar 和 topbar visibility 对齐
    -- 后续需要根据需求来定
    CommonBarViewLogic.SetBottomBarVisibleProcess(bVisible)
end

--- 手动change非栈UI导致的顶部bar透明度
CommonBarViewLogic.SetTopRenderOpacityProcess = function(renderOpacity)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:SetTopRenderOpacity(renderOpacity)
    end
end

--- 手动change非栈UI导致的底部bar可见性
CommonBarViewLogic.SetBottomBarVisibleProcess = function(bVisible)
    local bottomBar = Module.CommonBar.Field:GetBottomBar()
    if bottomBar then
        bottomBar:SetBottomBarVisible(bVisible)
    end
end

CommonBarViewLogic.SetBarVisibleProcess = function(bVisible)
    CommonBarViewLogic.SetTopBarVisibleProcess(bVisible)
    CommonBarViewLogic.SetBottomBarVisibleProcess(bVisible)
end

CommonBarViewLogic.SetBarAndSecPanVisibleProcess = function(bVisible)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:SetTopBarAndSecPanVisible(bVisible)
    end

    --- @todo 临时方案，让bottombar 和 topbar visibility 对齐
    -- 后续需要根据需求来定
    CommonBarViewLogic.SetBottomBarVisibleProcess(bVisible)
end

--------------------------------------------------------------------------
--- Back 相关
--------------------------------------------------------------------------
--- 手动change非栈UI导致的顶部bar - 返回按钮可见性
CommonBarViewLogic.SetBackBtnVisibleProcess = function(bVisible)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            topBar:SetBackBtnVisible(bVisible)
        end
    end
    local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    if not hasdestroy(commonBarBasisView) then
        commonBarBasisView:SetBackBtnVisible(bVisible)
    end
end


--- 手动change非栈UI导致的顶部bar - 返回按钮文字
CommonBarViewLogic.ChangeBackBtnTextProcess = function(text)
    -- if DFHD_LUA == 0 then
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:ChangeBackBtnText(text)
    end
    -- end
    -- local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    -- if not hasdestroy(commonBarBasisView) then
    --     commonBarBasisView:ChangeBackBtnText(text)
    -- end
end

--- 手动change非栈UI导致的顶部bar - 返回按钮callback
CommonBarViewLogic.BindBackHandlerProcess = function(callback, caller, ...)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) and topBar.BindBackHandler then
            topBar:BindBackHandler(callback, caller, ...)
        end
    elseif DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar and bottomBar.BindBackHandler then
            bottomBar:BindBackHandler(callback, caller, ...)
        end
    end

    -- local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    -- if not hasdestroy(commonBarBasisView) then
    --     commonBarBasisView:BindBackHandler(callback, caller, ...)
    -- end
end

--- 手动change非栈UI导致的顶部bar - 返回按钮callback, 可多次触发
CommonBarViewLogic.BindPersistentBackHandlerProcess = function(callback, caller, ...)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) and topBar.BindPersistentBackHandler then
            topBar:BindPersistentBackHandler(callback, caller, ...)
        end
    end

    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            bottomBar:BindPersistentBackHandler(callback, caller, ...)
        end
    end

    --- TODO 这个方法可能暂时用不上
    -- local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    -- if not hasdestroy(commonBarBasisView) then
    --     commonBarBasisView:BindPersistentBackHandler(callback, caller, ...)
    -- end
end

--- 手动change非栈UI导致的顶部bar - 信息按钮可见性
CommonBarViewLogic.SetInfoBtnVisibleProcess = function(bVisible)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            topBar:SetInfoBtnVisible(bVisible)
        end
    end
    
    -- local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    -- if not hasdestroy(commonBarBasisView) then
    --     commonBarBasisView:SetInfoBtnVisible(bVisible)
    -- end
end

--- 手动change非栈UI导致的顶部bar - 信息按钮callback
CommonBarViewLogic.BindInfoHandlerProcess = function(callback, caller, ...)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            topBar:BindInfoHandler(callback, caller, ...)
        end
    end

    -- local commonBarBasisView = Module.CommonBar.Field:GetCommonBarBasisView()
    -- if not hasdestroy(commonBarBasisView) then
    --     commonBarBasisView:BindInfoHandler(callback, caller, ...)
    -- end
end

--------------------------------------------------------------------------
--- TabGroup 相关
--------------------------------------------------------------------------
CommonBarViewLogic.SetTopBarGroupTabVisibleProcess = function(bVisible, tabLevel)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:SetTopGroupTabVisible(bVisible, tabLevel)
    end
end

CommonBarViewLogic.GetHDTopTabGroupModeProcess = function()
    if DFHD_LUA == 1 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            return topBar:GetHDTopTabGroupMode()
        end
    else
        return nil
    end
end

CommonBarViewLogic.GetIsLobbyTopBarShow = function()
    return Module.CommonBar.Field:GetIsLobbyTopBarShow()
end

CommonBarViewLogic.SetIsLobbyTopBarShow = function(isLobbyTopBarShowing)
    Module.CommonBar.Field:SetIsLobbyTopBarShow(isLobbyTopBarShowing)
end

---@param tabGroupRegInfo topTabGroupRegInfo
CommonBarViewLogic.AddTopBarTertiaryTabGroupProcess = function(tabGroupRegInfo)
    if DFHD_LUA == 1 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            return topBar:AddTertiaryTabGroup(tabGroupRegInfo)
        end
    end
end

CommonBarViewLogic.RemoveTopBarTertiaryTabGroupProcess = function()
    if DFHD_LUA == 1 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            return topBar:RemoveTertiaryTabGroup()
        end
    end
end

--- 手动change非栈UI导致的顶部bar - 货币信息可见性
CommonBarViewLogic.SetCurrencyVisibleProcess = function(bVisible)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:SetCurrencyVisible(bVisible)
    end
end

CommonBarViewLogic.ShowTopBarLevel = function(level)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            topBar:ShowTopBarLevel(level)
        end
    end
end

CommonBarViewLogic.ShowTopBarUpgradeBtn = function(deviceInfo)
    if DFHD_LUA == 0 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            topBar:ShowTopBarUpgradeBtn(deviceInfo)
        end
    end
end


CommonBarViewLogic.RefreshCurrencyTypeList = function(currencyTypeList)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:RefreshCurrencyTypeList(currencyTypeList)
    end
end

CommonBarViewLogic.SetTopTabGroup = function(topTabGroupRegInfo, tabLevel, overrideIndex, bInvoke)
    -- if DFHD_LUA == 1 then -- 注意手游要调用的时候需要把uiNavId填上，不然会影响布局
        local topBar = Module.CommonBar.Field:GetTopBar()
        if not hasdestroy(topBar) then
            topBar:SetTopTabGroup(topTabGroupRegInfo, tabLevel, overrideIndex, bInvoke)
        end
    -- end
end

CommonBarViewLogic.SetBottomBarInputSummaryList = function(summaryList, bNavigation)
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if not hasdestroy(bottomBar) then
            bottomBar:SetInputSummaryList(summaryList, bNavigation)
        end
    end
end

CommonBarViewLogic.SetBottomBarTempInputSummaryList = function(summaryList, bReplace, bNavIncluded)
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if not hasdestroy(bottomBar) then
            bottomBar:SetTempInputSummaryList(summaryList, bReplace, bNavIncluded)
        end
    end
end

-- BEGIN MODIFICATION @ VIRTUOS : 手动设置BottomBarV2
CommonBarViewLogic.SetBottomBarInputSummaryListV2 = function(summaryListV2)
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if not hasdestroy(bottomBar) then
            bottomBar:SetInputSummaryListV2(summaryListV2)
        end
    end
end

-- 只设置BottonBar的显隐，不创建新的BottonBar
CommonBarViewLogic.ShowBottomBarOnly = function(bShow)
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            if bShow then
                bottomBar:Show()
            else
                bottomBar:Hide()
            end
        end
    end
end

--判断当前BottomBar是否显示或隐藏
CommonBarViewLogic.IsBottomBarVisible = function()
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            return bottomBar:IsVisible()
        end
    end
    return false
end
-- END MODIFICATION

CommonBarViewLogic.RecoverBottomBarInputSummaryList = function()
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            bottomBar:RecoverInputSummaryList()
        end
    end
end

--- BEGIN MODIFICATION @ VIRTUOS: Clear BottomBar input summary.
CommonBarViewLogic.ClearBottomBarStackSummaryList = function()
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            bottomBar:ClearStackSummaryList()
        end
    end
end

CommonBarViewLogic.ClearBottomBarOuterNavSummaryList = function()
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            bottomBar:ClearOuterNavSummaryList()
        end
    end
end

CommonBarViewLogic.ClearBottomBarInputSummaryList = function()
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            bottomBar:ClearInputSummaryList()
        end
    end
end
--- END MODIFICATION

CommonBarViewLogic.TakeBottomBarTempInputSummarySnapshot = function()
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            return bottomBar:TakeBottomBarTempInputSummarySnapshot()
        end
    end
    return nil
end

CommonBarViewLogic.ApplyBottomBarTempInputSummarySnapshot = function(inHandle, bReleaseSnapshot)
    if DFHD_LUA == 1 then
        local bottomBar = Module.CommonBar.Field:GetBottomBar()
        if bottomBar then
            bottomBar:ApplyBottomBarTempInputSummarySnapshot(inHandle, bReleaseSnapshot)
        end
    end
end


CommonBarViewLogic.SetSideBar = function(MainTabInputSummaryV2)
    if DFHD_LUA == 1 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if topBar then
            topBar:SetSideBar(MainTabInputSummaryV2)
        end
    end
end

CommonBarViewLogic.SetIsSideBarVisible = function(bInVisible)
    if DFHD_LUA == 1 then
        local topBar = Module.CommonBar.Field:GetTopBar()
        if topBar then
            topBar:SetIsSideBarVisible(bInVisible)
        end
    end
end

CommonBarViewLogic.NavigationSetTopTabGroup = function(index, tabLevel, bOverrideInitIdx)
    bOverrideInitIdx = setdefault(bOverrideInitIdx, false)
    if bOverrideInitIdx then
        Module.CommonBar:RegTopTabGroupInitIdx(tabLevel, index)
    end
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        if topBar.NavigationSetTopTabGroup then
            topBar:NavigationSetTopTabGroup(index, tabLevel)
        end
    end
end

CommonBarViewLogic.CheckTopTabGroup = function(index, bForceCallback, tabLevel, bOverrideInitIdx)
    bOverrideInitIdx = setdefault(bOverrideInitIdx, false)
    if bOverrideInitIdx then
        Module.CommonBar:RegTopTabGroupInitIdx(tabLevel, index)
    end
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:CheckTopTabGroup(index, bForceCallback, tabLevel)
    end
end

CommonBarViewLogic.CheckTopTabGroupTabLevel23 = function(indexLv2, indexLv3, bForceCallback, bOverrideInitIdx)
    bOverrideInitIdx = setdefault(bOverrideInitIdx, false)
    if bOverrideInitIdx then
        Module.CommonBar:RegTopTabGroupInitIdx(2, indexLv2)
        Module.CommonBar:RegTopTabGroupInitIdx(3, indexLv3)
    end
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        topBar:CheckTopTabGroup(indexLv2, bForceCallback, 2)
    end
end

CommonBarViewLogic.GetTopTabGroupIndex = function(tabLevel)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) then
        return topBar:GetCurrentTopTabIndex(tabLevel)
    end
    return 0
end

CommonBarViewLogic.OnCloseAllPopUIAndStackUI = function(bIncludeBottom, bSilence)
    bIncludeBottom = setdefault(bIncludeBottom, false)
    bSilence = setdefault(bSilence, true)

    -- 之前安全屋不需要保留大厅的情况
    -- local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    -- local bIsInSafeHouse = currentGameFlow == EGameFlowStageType.SafeHouse
    -- local bIsHD = DFHD_LUA == 1
    -- if bIsInSafeHouse and bIsHD then
    --     bIncludeBottom = true
	-- end

    -- MS24 双大厅都需要保留的情况
    Facade.UIManager:PopAllUI(bIncludeBottom, bSilence, MapPopAllUIReason2Str.BackActionToLobby)
	Facade.UIManager:CloseAllPopUI()
end



---@param actionName string
---@return LuaUIBaseView|nil
---@field  bar BottomBarHD
CommonBarViewLogic.GetBottomBarItemByActionName = function(actionName)
    local bar = Module.CommonBar.Field:GetBottomBar()
    if not bar then return nil end
    if type(bar.GetItemByActionName) == "function" then
        return bar:GetItemByActionName(actionName)
    end
    return nil
end

CommonBarViewLogic.RegTopTabGroupInitIdxProcess = function(tabLevel, initIdx)
    local topBar = Module.CommonBar.Field:GetTopBar()
    if not hasdestroy(topBar) and topBar.RegTopTabGroupInitIdx then
        topBar:RegTopTabGroupInitIdx(tabLevel, initIdx)
    end
end

CommonBarViewLogic.CheckAnyBlockUIProcess = function()
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    if inputMonitor.inputDisCount > 0 then
        return true
    end
    return false
end

return CommonBarViewLogic
