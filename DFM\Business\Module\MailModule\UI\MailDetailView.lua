----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMail)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local UGPInputHelper = import("GPInputHelper")
local EGPInputType = import("EGPInputType")
--END MODIFICATION  

local MailLogic = require "DFM.Business.Module.MailModule.Logic.MailLogic"
local MailOparetionLogic = require "DFM.Business.Module.MailModule.Logic.MailOparetionLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local EHorizontalAlignment = import "EHorizontalAlignment"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local MailDetailView = ui("MailDetailView")
local GameItem = Facade.TableManager:GetTable("GameItem")
local DFMGameBrowser = import "DFMGameBrowser"
local MailAvatarTable = Facade.TableManager:GetTable("MailAvatar")
local ActivityTable = Facade.TableManager:GetTable("ActivityRow")
local FAnchors = import "Anchors"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"

function MailDetailView:Ctor()
    self._wtTxtTitle = self:Wnd("TextBlock_77", UITextBlock)
    self._wtTxtContent = self:Wnd("RichTextBlock_0", UITextBlock)
    self._wtScrollBoxContent = self:Wnd("DFScrollBox_1", UITextBlock)
    self._wtTxtAddressor = self:Wnd("TextBlock_1", UITextBlock)
    self._wtTxtSendTime = self:Wnd("TextBlock_2", UITextBlock)
    self._wtTxtRemainTime = self:Wnd("TextBlock",TextBlock)
    self._wtDefaultIcon = self:Wnd("DFImage_1",UIImage)
    self._wtSenderIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtSenderLevel = self._wtSenderIcon:Wnd("TextBlock_65",UITextBlock)
    self._wtItemListText = self:Wnd("DFTextBlock_173",TextBlock)
    self._wtScrollGridBoxItems =
        UIUtil.WndScrollGridBox(
        self,
        "ScrollBox_0",
        self._OnGetItemCount,
        self._OnProcessItemWidget
    )
    self._wtCanvasDetail = self:Wnd("CanvasPanel_Maildetial",UIWidgetBase)

    --部分领取的领取按钮
    self._wtBtnReceive = self:Wnd("WBP_CommonButtonV2S2_C_1", DFCommonButtonOnly)
    self._wtBtnReceive:Event("OnClicked", self._OnReceiveBtnClick, self)
    self._wtBtnReceive:Event("OnDeClicked", self._OnReceivePartBtnDeClick, self)

    --删除
    self._wtBtnDelete = self:Wnd("WBP_CommonButtonV2S2_C_0", DFCommonButtonOnly)
    self._wtBtnDelete:Event("OnClicked", self._OnDeleteBtnClick, self)

    --self._wtHBoxReceive = self:Wnd("DFHorizontalBox_1", UIWidgetBase)
    --领取
    self._wtBtnReceiveALL = self:Wnd("WBP_CommonButtonV2S1_C_2", DFCommonButtonOnly)
    self._wtBtnReceiveALL:Event("OnClicked", self._OnReceiveAllBtnClick, self)

    --部分领取
    self._wtBtnReceivePart = self:Wnd("wtDFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtBtnReceivePart:Event("OnCheckStateChanged", self._OnReceivePartBtnClick, self)
    --self._wtBtnReceivePart:Event("OnClicked", self._OnReceivePartBtnClick, self)

    -- NoAnything
    self._wtSlotForContent = self:Wnd("EmptySlot", UIWidgetBase)

    --附件列表
    self._itemList = {}

    --多选状态
    self._bSelecting = false

    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self._wtBtnReceiveALL:SetDisplayInputAction("Receive_Gamepad", true, nil, true)
        self._wtBtnReceive:SetDisplayInputAction("Receive_Gamepad", true, nil, true)
        self._wtBtnDelete:SetDisplayInputAction("DeleteMail_Gamepad", true, nil, true)
        self._inputMonitor = Facade.UIManager:GetInputMonitor()    
        self._ScrollGridBox = self:Wnd("ScrollBox_0", UIWidgetBase)
    end
    -- END MODIFICATION
 
end


-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation

function MailDetailView:_UpdateInputSummaryList()
    if IsHD() then
        local summaryList={}
        table.insert(summaryList,{actionName="OpenPreModificationTips_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})        
        if self._wtBtnReceivePart:IsVisible() then
            table.insert(summaryList,{actionName="ReceivePart_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false})
        end
        if self._wtBtnReceivePart:GetIsChecked() then 
            table.insert( summaryList,{actionName="Select", func = nil, caller = self ,bUIOnly = true, bHideIcon = false })
            --table.insert( summaryList,{actionName="OpenItemDetail_Gamepad", func = nil, caller = self ,bUIOnly = true, bHideIcon = false })
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList,true,false)
    end
end

function MailDetailView:_OnReceiveBtnClickByPad()
    if IsHD() then
        if self._wtBtnReceivePart:IsVisible() then
            if self._wtBtnReceivePart:GetIsChecked() then
                self:_OnReceiveBtnClick()
            else
                self:_OnReceiveAllBtnClick()
            end
        end
    end
end
function MailDetailView:_OnDeleteBtnClickByPad()
    if IsHD() then
        if  self._wtBtnDelete:IsVisible() then
            self:_OnDeleteBtnClick()
        end
    end
end
function MailDetailView:UnregisterShortCuts()
    if IsHD() then
        if self._ReceivePartHandle then
            self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._ReceivePartHandle)
            self._ReceivePartHandle = nil
        end
        if self._ReceiveHandle then
            self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._ReceiveHandle)
            self._ReceiveHandle = nil
        end
        if self._DeleteHandle then
            self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._DeleteHandle)
            self._DeleteHandle = nil
        end
            Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end
-- END MODIFICATION


function MailDetailView:OnOpen()
    self:AddLuaEvent(Module.Mail.Config.evtOpenMail, self._OnOpenMail, self)
    self:AddLuaEvent(Module.Mail.Config.evtMailDetailPanelRefresh, self.RefreshView, self)
    self:AddLuaEvent(Module.Mail.Config.evtMailItemClick, self.UnlockReceieveBtn, self)
    self:AddLuaEvent(Module.Mail.Config.evtMailReceivingAll, self._OnReceivingAll, self)
    self:AddLuaEvent(Module.Mail.Config.evtReceivedAll,self.CheckAllReceived,self)
    self:AddLuaEvent(Server.MailServer.Events.evtReceiveFailed, self.CheckAllReceived, self)
    -- 断线重连
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self.CheckAllReceived,self)
end

function MailDetailView:OnShowBegin()

    if isvalid(self._wtTxtContent) then
        local decoratorIns = self._wtTxtContent:GetDecoratorByClass(self.BpTextBlockDecorator)
        if decoratorIns then
            decoratorIns.OnClicked:Add(self._OnRichTextClicked, self)
        end
    end


end

function MailDetailView:OnShow()
end

function MailDetailView:OnHideBegin()
    self._itemList = {}
    Module.ItemDetail:CloseAllPopUI()
    if isvalid(self._wtTxtContent) then
		local decoratorIns = self._wtTxtContent:GetDecoratorByClass(self.BpTextBlockDecorator)
		if decoratorIns then
			decoratorIns.OnClicked:Clear()
		end
	end
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:UnregisterShortCuts()
    end 
    -- END MODIFICATION
end

function MailDetailView:OnHide()
end

function MailDetailView:OnClose()
    self:RemoveAllLuaEvent()
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self.CheckAllReceived, self)
    --Facade.UIManager:ClearSubUIByParent(self,self._wtScrollBoxContent)
end

function MailDetailView:UnlockReceieveBtn()--判断部分领取按钮是否置灰
    if table.isempty(Module.Mail.Field._attachmentIdxes) then
        self._wtBtnReceive:SetIsEnabledStyle(false)
        return
    end
    self._wtBtnReceive:SetIsEnabledStyle(true)
end

function MailDetailView:_OnOpenMail(mailID,mailType)
    self._mailInfo = Server.MailServer:GetMailInfo(mailType,mailID)
    if not self._mailInfo then
        self:_ShowEmpty(true)
        return
    end
    if not table.isempty(self._mailInfo.Args) then
--[[         local itemStr = ""
        for i=1,#self._mailInfo.Args,2 do
            local ItemId = self._mailInfo.Args[i]
            local Num = tonumber(self._mailInfo.Args[i+1])
            local ItemName = ItemConfigTool.GetItemName(ItemId)
            local activity = ActivityTable[ItemId]
            if Num and Num > 1 then
                itemStr = itemStr .. string.format("%s x %s",ItemName,Num)
            elseif ItemId and activity then--过期活动
                itemStr = itemStr .. string.format("%s ",activity.Name)
            else
                itemStr = itemStr .. ItemName .. ""
            end
        end
        self._mailInfo.Content = string.format(self._mailInfo.Content,itemStr,"") ]]
        self._mailInfo.Content = self:Convert(self._mailInfo.Content,self._mailInfo.Args)
    end
    self:PlayChangeMailAnim()
    self:RefreshView()
    self:_ShowEmpty(false)

-- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        self:_UpdateInputSummaryList()
    end
-- END MODIFICATION    

end

function MailDetailView:SetSenderIcon(info)
    if info.sender_id ~= 0 then
        self._wtSenderIcon:SelfHitTestInvisible()
        self._wtDefaultIcon:Collapsed()
        if self._wtSenderLevel then
            self._wtSenderLevel:SelfHitTestInvisible()
        end
        self:SetPlayerIcon(info.sender_id)
        return
    end
    self._wtSenderIcon:Collapsed()
    self._wtDefaultIcon:SelfHitTestInvisible()
    local iconID = info.system_avatar
    if iconID == 0 then
        self._wtDefaultIcon:AsyncSetImagePath(Module.CommonWidget.Config.HEAD_ICON_NONE)
        if self._wtSenderLevel then
            self._wtSenderLevel:Collapsed()
        end
        return
    end
    if not MailAvatarTable[iconID] then
        loginfo("IconSetFailed")
        return
    end
    self._wtDefaultIcon:AsyncSetImagePath(MailAvatarTable[iconID].AvatarPath)
    if self._wtSenderLevel then
        self._wtSenderLevel:Collapsed()
    end

end

function MailDetailView:SetPlayerIcon(id)--获取玩家头像
    local function callBack(res)
        if res.result == 0 and self._mailInfo.sender_id ~= 0 then
            local playerInfo = {
                player_id = id,
                pic_url = res.pic_url,
                level = res.level
            }
            self._wtSenderIcon:InitPortrait(playerInfo, HeadIconType.HeadNore)
        end
    end
    local req = pb.CSAccountGetPlayerProfileReq:New()
    req.client_flag = 0
    req.player_id = id
    req:Request(callBack,{bNeedResendAfterReconnected = true,bEnableHighFrequency = true})

end


function MailDetailView:RefreshView()
    if not self._mailInfo then
        loginfo("NoMailInfo")
        return
    end
    self._wtBtnReceivePart:SetIsChecked(false)
    -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
    if IsHD() then
        WidgetUtil.RemoveNavigationGroup(self)
    end
    -- END MODIFICATION
    self:SetSenderIcon(self._mailInfo)
    self._wtTxtTitle:SetText(self._mailInfo.Title)
    --self._wtTxtTitle:PlayAnim_ComputeTextBlock()
    local richText = self:CheckURLInMailInfo(self._mailInfo.Content)
    --local richText = self:CheckURLInMailInfo("这是一个网页链接 https://www.google.com.hk 和另一个链接https://www.baidu.com")
    --local richText = self:CheckURLInMailInfo("尊敬的迅游会员，您的迅游加速服务（移动端）已到期。您可以在手机端点击下方链接，跳转迅游加速服务页面查看详情。[【这里会有一段超链接，点击会拉起迅游H5】](”https://pay.xunyou.mobi/EFC68577-1342-5310-C34F-928C23442614/?user_openid=XXXXXXXX&guid=EFC68577-1342-5310-C34F-928C23442614&go=5“)")
    
    --local richText = self:CheckURLInMailInfo("这是一个网页链接[快点击链接报名](https://www.openai.com)和另一个链接[点击打开问卷](https://www.google.com)")
    
    self._wtTxtContent:SetText(richText)

    self._wtTxtAddressor:SetText(self._mailInfo.Sender)

    local sendTime = TimeUtil.TransTimestamp2AYYMMDDStr(self._mailInfo.SendTimestamp, "YY.MM.DD")
    self._wtTxtSendTime:SetText(sendTime)
    if self._mailInfo.ExpirationTime == 0 then
        self._wtTxtRemainTime:Collapsed()
    else
        self._wtTxtRemainTime:Visible()
        local remainTime = MailLogic.GetRemainTime(self._mailInfo.ExpirationTime)
        if remainTime.day > 0 then
            if remainTime.hour > 0 then
                remainTime.day = remainTime.day + 1
            end
            self._wtTxtRemainTime:SetText(string.format(Module.Mail.Config.Loc.expiredTimeDay,remainTime.day))
        elseif remainTime.hour > 0 then
            self._wtTxtRemainTime:SetText(string.format(Module.Mail.Config.Loc.remainTimeHour,remainTime.hour))
        else
            self._wtTxtRemainTime:SetText(string.format(Module.Mail.Config.Loc.remainTimeMin,remainTime.min))
        end
    end


    self._wtScrollGridBoxItems:Collapsed()

    if self._mailInfo.state.attachmentState then
        self:ShowGifts()
    end

    if self._mailInfo.state.attachmentState and not self._mailInfo.state.receiveState then
        --self._wtHBoxReceive:Visible()
        self._wtBtnReceiveALL:Visible()
        self._wtBtnReceivePart:Visible()
        self._wtBtnReceive:Collapsed()
        self._wtBtnDelete:Collapsed()
        self._wtItemListText:SelfHitTestInvisible()
        -- BEGIN MODIFICATION @ VIRTUOS : UI input  
        if IsHD() then
            if not self._ReceiveHandle  then
                self._ReceiveHandle = self._inputMonitor:AddDisplayActionBinding("Receive_Gamepad", EInputEvent.IE_Pressed, self._OnReceiveBtnClickByPad, self, EDisplayInputActionPriority.UI_Stack)
            end
            if not self._ReceivePartHandle then
                self._ReceivePartHandle = self._inputMonitor:AddDisplayActionBinding("ReceivePart_Gamepad", EInputEvent.IE_Pressed, self._OnReceivePartBtn_GamePadClick, self, EDisplayInputActionPriority.UI_Stack)
            end
            if self._DeleteHandle then
                self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._DeleteHandle)
                self._DeleteHandle = nil
            end
            self:_UpdateInputSummaryList()
        end
        -- END MODIFICATION
    else
        --self._wtHBoxReceive:Collapsed()
        if self._mailInfo.autoDelete then
            self:_OnDeleteBtnClick()
        end
        self._wtBtnReceiveALL:Collapsed()
        self._wtBtnReceive:Collapsed()
        self._wtBtnReceivePart:Collapsed()
        self._wtBtnDelete:Visible()
        -- BEGIN MODIFICATION @ VIRTUOS : UI input
        if IsHD() then
            if self._ReceiveHandle then
                self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._ReceiveHandle)
                self._ReceiveHandle = nil
            end
            if self._ReceivePartHandle then
                self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._ReceivePartHandle)
                self._ReceivePartHandle = nil
            end
            if not self._DeleteHandle then
            self._DeleteHandle = self._inputMonitor:AddDisplayActionBinding("DeleteMail_Gamepad", EInputEvent.IE_Pressed, self._OnDeleteBtnClickByPad, self, EDisplayInputActionPriority.UI_Stack)
            end
            self:_UpdateInputSummaryList()
        end
        -- END MODIFICATION
        self._wtItemListText:Collapsed()
    end
    self._bSelecting = false
end

function MailDetailView:CheckURLInMailInfo(mailInfo)--将邮件内容中的超链接解析出来

    local mailContent = tostring(mailInfo)
    --local index = 1
    for text ,url in string.gmatch(mailInfo, "%[(.-)%]%((.-)%)") do --通过正则表达式解析markdown格式的链接
        url = "<dfmrichtext style=\"HyperlinkTask\" link=\""..url.."\">" ..text.. "</>" --文字转链接富文本
        url = string.gsub(url, "%%", "%%%%")
        mailContent = string.gsub(mailContent, "%[(.-)%]%((.-)%)", url,1) --将原链接替换成超链接
        mailContent = string.gsub(mailContent, "%%%%", "%%")
        --index =index+1
    end
    return mailContent
end



function MailDetailView:_OnRichTextClicked(metadataStruct)
	for key, value in pairs(metadataStruct.Metadata) do
		if key == "link" then
            local bOpenUrl, Url = MailLogic.PreProcess(value)
            if bOpenUrl then
                Module.GCloudSDK:OpenUrl(Url)
            else
                logerror("MailDetailView:_OnRichTextClicked: Failed to open URL:", bOpenUrl, Url)
            end
		end
	end
end

function MailDetailView:ShowGifts()
    self._wtScrollGridBoxItems:Visible()
    self:SplitAttachments(self._mailInfo.mail_attachments)
    self._wtScrollGridBoxItems:RefreshAllItems()
end

function MailDetailView:SplitAttachments(attachments)--将奖品在邮件中分成多个网格显示
    self._itemList = {}

    for _ , attachment in ipairs(attachments) do
        local gameItem = GameItem[attachment.prop_info.id]
        Server.InventoryServer:OverridePropInfoSkinInfosChecked(attachment.prop_info)
        if not gameItem then
            Module.CommonTips:ShowSimpleTip(Module.Mail.Config.Loc.ItemIDError)
            local item = ItemHelperTool.CreateItemByPropInfo(attachment.prop_info, attachment)
            if item then
                item:SetRawPropInfo(attachment.prop_info)
                table.insert(self._itemList,item)
            end
        else
            local maxCount = gameItem.MaxStackCount --奖品最大堆叠数
            local originNum = attachment.origin_prop_num --奖励物品的总数
            local receivedNum = attachment.origin_prop_num - attachment.prop_info.num --已接收的奖励物品个数
            for index = 1, math.ceil(originNum/maxCount) do
                local item = ItemHelperTool.CreateItemByPropInfo(attachment.prop_info, attachment)
                if item then
                    item:SetRawPropInfo(attachment.prop_info)
                    if receivedNum >0 then
                        item.num = math.min(receivedNum, maxCount)--若附件中存在已领取的，则首先显示领取的附件
                    else
                        item.num = math.min(originNum, maxCount)--奖品每格显示的数量
                    end
                    item.attachmentsIndex = attachment.index
                    originNum = originNum - item.num
                    if receivedNum >0 then 
                        item.received =true
                        receivedNum = receivedNum -item.num
                    end
                    --处理货币的绑定类型
                    self:_SetCurrencyBindType(item)
                    table.insert(self._itemList,item)
                end
            end
        end
    end
end

function MailDetailView:_OnGetItemCount()
    return #self._itemList
end

function MailDetailView:_OnProcessItemWidget(position, itemWidget)
    local info = self._itemList[position+1]
    if not info then
        return
    end
    info.index = position+1
    itemWidget:SetInfo(info,self._mailInfo.state.receiveState)
    itemWidget:SetSelectable(self._bSelecting)
end

function MailDetailView:_SetCurrencyBindType(item)
    if item.id == 17020000010 or
    item.id == 17020000012 or item.id == 17020000013 then
        item.bindType = PropBindingType.BindingNotBind
    end

    if item.id == 17020000011 then
        item.bindType = PropBindingType.BindingBinded
    end
end

function MailDetailView:Change2Blank()
    self:_ShowEmpty(true)
end

function MailDetailView:_ShowEmpty(bShow)
    if bShow then
        self._wtCanvasDetail:Collapsed()

    else
        self._wtCanvasDetail:Visible()
    end
    Module.ItemDetail:CloseAllPopUI()
end

function MailDetailView:_OnReceiveAllBtnClick()
    local onReceive = function(remainTime,mail_attachments,data_change)
        self._mailInfo.ExpirationTime = remainTime
        local itemList = {}
        if data_change.prop_changes ~= nil then
            for _, change in pairs(data_change.prop_changes) do
                if change.change_type == PropChangeType.Add or change.change_type == PropChangeType.Modify then
                    if change.prop then
                        Server.InventoryServer:OverridePropInfoSkinInfosChecked(change.prop)
                        local item = ItemHelperTool.CreateItemByPropInfo(change.prop, change)
                        if item then
                            item:SetRawPropInfo(change.prop)
                            table.insert(itemList,item)
                            --itemList[#itemList]:SetRawPropInfo(change.prop)
                        end
                    end
                end
            end
        end
        for _, change in pairs(data_change.currency_changes) do
            if change.delta and change.currency_id then
                table.insert(itemList, ItemBase:NewIns(change.currency_id, change.delta))
                itemList[#itemList].src_id = change.src_id
            end
        end
        Module.Reward:OpenRewardPanel(Module.Mail.Config.Loc.rewardPanelTitle,nil, itemList, nil, false, false, true)
        Module.Reward:EnableNTFCall("Hero", true)
        self:RefreshView()
        -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
        if IsHD() then
            self:_UpdateInputSummaryList()
        end
        -- END MODIFICATION
    end
    local attachments = {}
    local bEquipmentTipOverFlow = false -- 装备券是否过量
    local totalTipNums = {} --玩家目前持有的装备券


    for _,info in ipairs(self._mailInfo.mail_attachments) do
        if not  bEquipmentTipOverFlow and (string.find(info.prop_info.id, "3233")) then
            bEquipmentTipOverFlow = self:CheckEquipmentTipIsOverflow(info.prop_info)
        end
        if info.prop_info.num > 0 then
            local attachment = { index = info.index, prop_id = info.prop_info.id, num = info.prop_info.num }
            table.insert(attachments, attachment)
        end
    end


    if MailOparetionLogic.HasEquipmentTipInList(self._mailInfo.mail_attachments) and bEquipmentTipOverFlow then --若邮件附件列表中有装备券并超限时，弹出确认框（点击确认则走领取逻辑，取消则直接返回不走领取）
        Module.CommonTips:ShowConfirmWindow(
            Module.ArmedForce.Config.Loc.ConfirmResolveTip,
            function ()
                Server.MailServer:ReceiveMail(self._mailInfo.MailID,attachments,self._mailInfo.Type,onReceive)
                return
            end
        )
        return
    end
    Server.MailServer:ReceiveMail(self._mailInfo.MailID,attachments,self._mailInfo.Type,onReceive)

end

function MailDetailView:_OnReceivePartBtn_GamePadClick()
    self._wtBtnReceivePart:SetIsChecked(not self._wtBtnReceivePart:GetIsChecked(),false)--部分领取的选框状态取反,无需调用回调
    self:_OnReceivePartBtnClick()
end



function MailDetailView:_OnReceivePartBtnClick()
    self._bSelecting = not self._bSelecting
    --self._wtHBoxReceive:Collapsed()
    if self._bSelecting then
        self._wtBtnReceive:Visible()
        self._wtBtnReceiveALL:Collapsed()
    else
        self._wtBtnReceiveALL:Visible()
        self._wtBtnReceive:Collapsed()
    end
    self._wtBtnReceive:SetIsEnabledStyle(not self._bSelecting)
    self._wtScrollGridBoxItems:RefreshAllItems()
    Module.Mail.Field:ClearAttachmentIdxes()
end

function MailDetailView:_OnReceiveBtnClick()
    local onReceive = function(remainTime,mail_attachments,data_change)
        self._mailInfo.ExpirationTime = remainTime
        local itemList = {}
        if data_change.prop_changes ~= nil then
            for _, change in pairs(data_change.prop_changes) do
                if change.change_type == PropChangeType.Add or change.change_type == PropChangeType.Modify then
                    if change.prop then
                        Server.InventoryServer:OverridePropInfoSkinInfosChecked(change.prop)
                        local item = ItemHelperTool.CreateItemByPropInfo(change.prop, change)
                        if item then
                            item:SetRawPropInfo(change.prop)
                            table.insert(itemList,item)
                            --itemList[#itemList]:SetRawPropInfo(change.prop)
                        end
                    end
                end
            end
        end
        for _, change in pairs(data_change.currency_changes) do
            if change.delta and change.currency_id then
                table.insert(itemList, ItemBase:NewIns(change.currency_id, change.delta))
                itemList[#itemList].src_id = change.src_id
            end
        end
        Module.Reward:OpenRewardPanel(Module.Mail.Config.Loc.rewardPanelTitle,nil,itemList, nil, false, false, true)
        Module.Reward:EnableNTFCall("Hero", true)
        self:RefreshView()
        self._wtBtnReceivePart:SetIsChecked(false)

        -- BEGIN MODIFICATION @ VIRTUOS : UI input and navidation
        if IsHD() then
            self:_UpdateInputSummaryList()
        end
        -- END MODIFICATION
    end

    local attachment_idxes = Module.Mail.Field:GetAttachmentIdxes()
    local attachments = {}
    local idxsTable={}--存储奖励物品的后台索引值
    local bEquipmentTipOverFlow = false -- 装备券是否过量
    local totalTipNums = {} --玩家目前持有的装备券
    local attachmentsTemp = {}


    for _, idx in ipairs(attachment_idxes)  do
        local item = self._itemList[idx+1]
        if item then
            if not bEquipmentTipOverFlow and (string.find(item.id, "3233")) then
                bEquipmentTipOverFlow = self:CheckEquipmentTipIsOverflow(item)
            end

            -- if table.contains(idxsTable,item.attachmentsIndex) then --若勾选的奖品有重复，则将重复奖品的数量相加
            --     attachments[attachmentsIndex].num = attachments[attachmentsIndex].num + math.min(item.num,GameItem[item.id].MaxStackCount)
            -- else
            --     local attachment = { index = item.attachmentsIndex, prop_id = item.id, num = math.min(item.num,GameItem[item.id].MaxStackCount),prop_info = item}
            --     attachmentsIndex =attachmentsIndex +1
            --     --attachments[attachmentsIndex] = attachment
            --     table.insert(attachments, attachment)
            --     table.insert(idxsTable,item.attachmentsIndex)--通过idx记录领取的奖品种类
            -- end

            if table.contains(idxsTable,item.attachmentsIndex) then --若勾选的奖品有重复，则将重复奖品的数量相加
                attachmentsTemp[item.attachmentsIndex].num = attachmentsTemp[item.attachmentsIndex].num + math.min(item.num,GameItem[item.id].MaxStackCount)
            else
                local attachment = { index = item.attachmentsIndex, prop_id = item.id, num = math.min(item.num,GameItem[item.id].MaxStackCount),prop_info = item}
                attachmentsTemp[item.attachmentsIndex] = attachment
                table.insert(idxsTable,item.attachmentsIndex)--通过idx记录领取的奖品种类
            end
        end
    end


    for _, attachment in pairs(attachmentsTemp) do --将map存成数组的形式
    table.insert(attachments, attachment)
    end

    if table.isempty(attachment_idxes) then
        Module.CommonTips:ShowSimpleTip(Module.Mail.Config.Loc.selectAttachment)
    else
        if MailOparetionLogic.HasEquipmentTipInList(attachments) and bEquipmentTipOverFlow then --若邮件附件列表中有装备券并超限时，弹出确认框（点击确认则走领取逻辑，取消则直接返回不走领取）
            Module.CommonTips:ShowConfirmWindow(
                Module.ArmedForce.Config.Loc.ConfirmResolveTip,
                function ()
                    Server.MailServer:ReceiveMail(self._mailInfo.MailID,attachments,self._mailInfo.Type,onReceive)
                    return
                end
            )
            return
        end
        Server.MailServer:ReceiveMail(self._mailInfo.MailID,attachments,self._mailInfo.Type,onReceive)
    end

    
end

function MailDetailView:CheckEquipmentTipIsOverflow(itemInfo) -- 判断是否存在装备券是否过量
    if not itemInfo then
        return
    end
    local totalTipNum = Server.CollectionServer:GetCollectionItemsNumById(itemInfo.id)
    totalTipNum = totalTipNum + itemInfo.num
    local maxNum = ItemConfigTool.GetMaxStacksNumById(itemInfo.id)
    if  totalTipNum > maxNum then -- 装备券过量
        bEquipmentTipOverFlow = true
        return true
    end
    return false
end



function MailDetailView:_OnReceivePartBtnDeClick()
    Module.CommonTips:ShowSimpleTip(Module.Mail.Config.Loc.selectAttachment)
end

function MailDetailView:_OnDeleteBtnClick()
    local onDelete = function()
        self:PlayDeleteMailAnim()
        self:_ShowEmpty(true)
        if not self._mailInfo.autoDelete then
            Module.CommonTips:ShowSimpleTip(Module.Mail.Config.Loc.deleteMailSucess)
        end
    end
    Server.MailServer:DeleteMail(self._mailInfo.MailID,self._mailInfo.Type,onDelete)
end

function MailDetailView:PlayDeleteMailAnim()
    self:PlayWidgetAnim(self.DeleteMailAnim, 1,EUMGSequencePlayMode.Forward,4.0)
end

function MailDetailView:PlayChangeMailAnim()
    self:PlayWidgetAnim(self.ChangeMailAnim, 1,EUMGSequencePlayMode.Forward,4.0)
end



function MailDetailView:_OnOpenBtnClick(link)
    if not IsHD() then
        Module.GCloudSDK:OpenUrl(link)
    elseif DFMGameBrowser and DFMGameBrowser.LaunchURL then
        local parms = ""
        local error = ""
        DFMGameBrowser.LaunchURL(link,parms,error)
        logframe("Mail open link:",link,"with error:",error)
    end
end

function MailDetailView:Convert(content, args,start_argi)
    content = tostring(content)
    if not start_argi then
		start_argi = 1
	end
	local argi = start_argi
	local segment_start = 1
	local segments = {}
	while true do
		local s, e = string.find(content, "%%.", segment_start)
		if s == nil then
			break
		end
		segments[#segments+1] = string.sub(content, segment_start, s-1)
		local t = string.sub(content, s, e)
		if t == "%s" then -- 通用字符串
			segments[#segments+1] = tostring(args[argi])
		elseif t == "%a" then -- 活动名称
            if ActivityTable[args[argi]] then
                segments[#segments+1] = ActivityTable[args[argi]].Name
            end
		elseif t == "%i" then -- 物品名称
			segments[#segments+1] = ItemConfigTool.GetItemName(args[argi])
        elseif t == "%L" then -- 变长数组，格式：%L{子pattern}{分隔符}。
			-- 传参方式：元素数量, 元素1参数1, 元素2参数2, ...,  元素2参数1, 元素2参数2, ...
			-- lua 不支持 lazy match，只能自己手工先做 lazy match 了
			local first_right_bracket = string.find(content, "}", e+1)
			local second_right_bracket = string.find(content, "}", (first_right_bracket or #content)+1)
			if first_right_bracket ~= nil and second_right_bracket ~= nil then
				local parameters = string.match(string.sub(content, e+1, second_right_bracket), "^{.+}{.*}$")
				if parameters ~= nil then
					e = e + #parameters
					local subpattern, divider = string.match(parameters, "^{(.+)}{(.*)}$")
					local count = args[argi]--count为卖出了几个物品
					argi = argi + 1
					for i = 1,count do
						local subargs = {}
						local subresult, new_argi = self:Convert(subpattern, args, argi)
                        subresult = string.format("<customstyle color=\"Color_Highlight01\">%s</>",subresult)
                        segments[#segments+1] = "\n"
						segments[#segments+1] = subresult
						argi = new_argi
					end
					argi = argi - 1 -- 前面无论如何都会多加 1，先减回去，因为大循环末尾会统一argi+1
				else
					segments[#segments+1] = "%L"
				end
			else
				segments[#segments+1] = "%L"
			end
		elseif t == "%%" then -- 百分号转义
			segments[#segments+1] = "%"
		else -- 缺省直接打出来
			segments[#segments+1] = t
		end
		argi = argi + 1
		segment_start = e+1
        if argi > #args then
            break
        end
	end
	segments[#segments+1] = string.sub(content, segment_start, #content)
	return table.concat(segments, ""),argi
end

---全部领取时
function MailDetailView:_OnReceivingAll(mailType)
    self._wtBtnReceiveALL:SetIsEnabledStyle(false)
    self._wtBtnReceive:SetIsEnabledStyle(false)
end

function MailDetailView:CheckAllReceived()

    self._wtBtnReceive:SetIsEnabledStyle(true)
    self._wtBtnReceiveALL:SetIsEnabledStyle(true)

end

return MailDetailView