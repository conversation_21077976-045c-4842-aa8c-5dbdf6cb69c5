----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



local RecruitLogic = {}
---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
--- 可以仅模块内部调用，也可以在Module中被公开
RecruitLogic.DoSomeThingProcess = function(...)
    -- TODO 业务逻辑、弹窗Tips表现、发送Server Req等等一系列事情
    -- Server.RecruitServer:DoSomeThingReq(...)
    -- return 
end

RecruitLogic.ShowMainPanelProcess = function(tabIndex)
    Module.Recruit:CloseMainPanel()
    local OnCreateCallback = function(uiIns)
        Module.Recruit.Field:SetMainPanel(uiIns)
    end
    if DFHD_LUA == 1 then
        Facade.UIManager:AsyncShowUI(UIName2ID.RecruitSocialPanel, OnCreateCallback, nil, tabIndex)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.RecruitMainPanel_Mobile, OnCreateCallback, nil)
    end 
end

RecruitLogic.OnMainPanelCreateFinished = function(uiIns)
    Module.Recruit.Field:SetMainPanel(uiIns)
end

RecruitLogic.CloseMainPanelProcess = function()
    local mainPanel = Module.Recruit.Field:GetMainPanel()
    if mainPanel then
        Facade.UIManager:CloseUI(mainPanel)
        Module.Recruit.Field:SetMainPanel(nil)
    end
end

RecruitLogic.IsInMp = function()
    return Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby
end

RecruitLogic.GetTeamMemberMaxNum = function()
    return RecruitLogic.IsInMp() and Module.Recruit.Config.MPMaxMemberNum or Module.Recruit.Config.SOLMaxMemberNum
end



--队伍筛选
RecruitLogic.GetRecommendFilterMicChoice = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.micChoice
end

RecruitLogic.SetRecommendFilterMicChoice = function(choice)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.micChoice = setdefault(choice, RecruitmentMicrophoneType.RecruitmentMicrophoneNoLimit)
    Module.Recruit.Config.Events.evtOnRecruitmentMicChoiceChanged:Invoke()
end

RecruitLogic.GetRecommendFilterRankDivisionChoice = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.rankDivisionChoice
end

RecruitLogic.SetRecommendFilterRankDivisionChoice = function(choice)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.rankDivisionChoice = setdefault(choice, RecruitmentRankType.RecruitmentRankNoLimit)
    Module.Recruit.Config.Events.evtOnRecruitmentRankDivisionChoiceChanged:Invoke()
end

RecruitLogic.GetRecommendFilterMaps = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.selectedMapIDs
end   

RecruitLogic.ToggleRecommendFilterMap = function(mapId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    if recommendFilterData.selectedMapIDs[mapId] ~= nil then
        if table.nums(recommendFilterData.selectedMapIDs) <= 1 then
            Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.MinMapHint)
        else
            recommendFilterData.selectedMapIDs[mapId] = nil 
        end
    else
        recommendFilterData.selectedMapIDs[mapId] = true
    end
    Module.Recruit.Config.Events.evtOnRecruitmentMapChanged:Invoke()
end

RecruitLogic.ClearRecommendFilterMap = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.selectedMapIDs = {}
    Module.Recruit.Config.Events.evtOnRecruitmentMapChanged:Invoke()
end

RecruitLogic.IsRecommendFilterMapSelected = function(mapId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.selectedMapIDs[mapId] == true
end

RecruitLogic.GetRecommendFilterTargets = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.selectedTargetIDs
end   

RecruitLogic.ToggleRecommendFilterTarget = function(targetId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    if recommendFilterData.selectedTargetIDs[targetId] ~= nil then
        if table.nums(recommendFilterData.selectedTargetIDs) <= 1 then
            Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.MinTargetHint)
        else
            recommendFilterData.selectedTargetIDs[targetId] = nil 
        end
    else
        recommendFilterData.selectedTargetIDs[targetId] = true
    end
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.ClearRecommendFilterTarget = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.selectedTargetIDs = {}
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.AddRecommendFilterTarget = function(targetId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.selectedTargetIDs[targetId] = true
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.RemoveRecommendFilterTarget = function(targetId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.selectedTargetIDs[targetId] = nil
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.IsRecommendFilterTargetSelected = function(targetId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.selectedTargetIDs[targetId] == true
end

RecruitLogic.GetRecommendFilterLabels = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    return recommendFilterData.selectedLabelIDs
end

RecruitLogic.ToggleRecommendFilterLabel = function(groupId, labelId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    declare_if_nil(recommendFilterData.selectedLabelIDs, groupId, {})
    local currentNum = 0
    for groupId, labels in pairs(recommendFilterData.selectedLabelIDs) do
        currentNum = currentNum + table.nums(labels)
    end
    local bIsMutexGroup = table.contains(Module.Recruit.Config.LabelMutexGroupIds, groupId)
    if currentNum >= Module.Recruit.Config.labelMaxNum then
        if bIsMutexGroup then
            if recommendFilterData.selectedLabelIDs[groupId] == nil or table.isempty(recommendFilterData.selectedLabelIDs[groupId]) then
                Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.MaxNumOfLabelHint)
            else
                if recommendFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                    table.empty(recommendFilterData.selectedLabelIDs[groupId])
                else
                    table.empty(recommendFilterData.selectedLabelIDs[groupId])
                    recommendFilterData.selectedLabelIDs[groupId][labelId] = 1
                end
            end
        else
            if recommendFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                recommendFilterData.selectedLabelIDs[groupId][labelId] = nil
                for key, value in pairs(recommendFilterData.selectedLabelIDs[groupId]) do
                    if recommendFilterData.selectedLabelIDs[groupId][key] > table.nums(recommendFilterData.selectedLabelIDs[groupId]) then
                        recommendFilterData.selectedLabelIDs[groupId][key] = table.nums(recommendFilterData.selectedLabelIDs[groupId])
                    end
                end
            else
                Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.MaxNumOfLabelHint)
            end
        end
    else
        if bIsMutexGroup then
            if recommendFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                table.empty(recommendFilterData.selectedLabelIDs[groupId])
            else
                table.empty(recommendFilterData.selectedLabelIDs[groupId])
                recommendFilterData.selectedLabelIDs[groupId][labelId] = 1
            end
        else
            if recommendFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                recommendFilterData.selectedLabelIDs[groupId][labelId] = nil
                for key, value in pairs(recommendFilterData.selectedLabelIDs[groupId]) do
                    if recommendFilterData.selectedLabelIDs[groupId][key] > table.nums(recommendFilterData.selectedLabelIDs[groupId]) then
                        recommendFilterData.selectedLabelIDs[groupId][key] = table.nums(recommendFilterData.selectedLabelIDs[groupId])
                    end
                end
            else
                recommendFilterData.selectedLabelIDs[groupId][labelId] = table.nums(recommendFilterData.selectedLabelIDs[groupId]) + 1
            end
        end  
    end
    Module.Recruit.Config.Events.evtOnRecruitmentLabelChanged:Invoke()
end

RecruitLogic.ClearRecommendFilterLabel = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    recommendFilterData.selectedLabelIDs = {}
end

RecruitLogic.IsRecommendFilterLabelSelected = function(groupId, labelId)
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    if recommendFilterData.selectedLabelIDs[groupId] ~= nil then
        return recommendFilterData.selectedLabelIDs[groupId][labelId] ~= nil
    else
        return false        
    end
end

RecruitLogic.SetRecommendFilterData = function(data)
    Module.Recruit.Field:SetFilterData("Recommend", data)
end

RecruitLogic.ResetRecommendFilterData = function()
    local recommendFilterData = {}
    local lastRecommendFilterData = RecruitLogic.GetLastRecommendFilterData()
    deepcopy(recommendFilterData, lastRecommendFilterData)
    RecruitLogic.SetRecommendFilterData(recommendFilterData)
end

RecruitLogic.GetRecommendFilterData = function()
    local recommendFilterData = Module.Recruit.Field:GetFilterData("Recommend")
    recommendFilterData = setdefault(recommendFilterData, {})
    recommendFilterData.selectedMapIDs = setdefault(recommendFilterData.selectedMapIDs, {})
    recommendFilterData.selectedTargetIDs = setdefault(recommendFilterData.selectedTargetIDs, {})
    recommendFilterData.micChoice = setdefault(recommendFilterData.micChoice, RecruitmentMicrophoneType.RecruitmentMicrophoneNoLimit)
    recommendFilterData.rankDivisionChoice = setdefault(recommendFilterData.rankDivisionChoice, RecruitmentRankType.RecruitmentRankNoLimit)
    recommendFilterData.selectedLabelIDs = setdefault(recommendFilterData.selectedLabelIDs, {})
    RecruitLogic.SetRecommendFilterData(recommendFilterData)
    return recommendFilterData
end

RecruitLogic.UpdateLastRecommendFilterData = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    local lastRecommendFilterData = {}
    deepcopy(lastRecommendFilterData, recommendFilterData)
    RecruitLogic.SetLastRecommendFilterData(lastRecommendFilterData)
end

RecruitLogic.SetLastRecommendFilterData = function(data)
    Module.Recruit.Field:SetLastFilterData("Recommend", data)
end

RecruitLogic.GetLastRecommendFilterData = function()
    local recommendFilterData = Module.Recruit.Field:GetLastFilterData("Recommend")
    recommendFilterData = setdefault(recommendFilterData, {})
    recommendFilterData.selectedMapIDs = setdefault(recommendFilterData.selectedMapIDs, {})
    recommendFilterData.selectedTargetIDs = setdefault(recommendFilterData.selectedTargetIDs, {})
    recommendFilterData.micChoice = setdefault(recommendFilterData.micChoice, RecruitmentMicrophoneType.RecruitmentMicrophoneNoLimit)
    recommendFilterData.rankDivisionChoice = setdefault(recommendFilterData.rankDivisionChoice, RecruitmentRankType.RecruitmentRankNoLimit)
    recommendFilterData.selectedLabelIDs = setdefault(recommendFilterData.selectedLabelIDs, {})
    if RecruitLogic.CheckHasFilteredRecruitment() == false then
        local mapInfoList = nil
        local targetInfoList = nil
        if RecruitLogic.IsInMp() then
            mapInfoList = RecruitLogic.GetMPMaps()
        else
            mapInfoList = RecruitLogic.GetSOLMaps()
            targetInfoList = RecruitLogic.GetSOLTargets()
        end
        for index, mapInfo in ipairs(mapInfoList) do
            recommendFilterData.selectedMapIDs[mapInfo.id] = true
        end
        if targetInfoList and #targetInfoList > 0 then
            for index, targetInfo in ipairs(targetInfoList) do
                if RecruitLogic.IsTargetAvailable(targetInfo.id, recommendFilterData.selectedMapIDs) == true then
                    recommendFilterData.selectedTargetIDs[targetInfo.id] = true
                end
            end
        end
    end
    RecruitLogic.SetLastRecommendFilterData(recommendFilterData)
    return recommendFilterData
end

RecruitLogic.ClearAllRecommendFilterData = function()
    RecruitLogic.SetRecommendFilterData(nil)
    RecruitLogic.SetLastRecommendFilterData(nil)
end






--发布招募
RecruitLogic.GetPublishFilterMicChoice = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.micChoice
end

RecruitLogic.SetPublishFilterMicChoice = function(choice)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.micChoice = setdefault(choice, RecruitmentMicrophoneType.RecruitmentMicrophoneEnable)
    Module.Recruit.Config.Events.evtOnRecruitmentMicChoiceChanged:Invoke()
end

RecruitLogic.GetPublishFilterRankDivisionChoice = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.rankDivisionChoice
end

RecruitLogic.SetPublishFilterRankDivisionChoice = function(choice)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.rankDivisionChoice = setdefault(choice, RecruitmentRankType.RecruitmentRankEnable)
    Module.Recruit.Config.Events.evtOnRecruitmentRankDivisionChoiceChanged:Invoke()
end

RecruitLogic.GetPublishFilterMaps = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.selectedMapIDs
end   

RecruitLogic.TogglePublishFilterMap = function(mapId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    if publishFilterData.selectedMapIDs[mapId] == nil then
        table.empty(publishFilterData.selectedMapIDs)
        publishFilterData.selectedMapIDs[mapId] = true
    end
    Module.Recruit.Config.Events.evtOnRecruitmentMapChanged:Invoke()
end

RecruitLogic.ClearPublishFilterMap = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.selectedMapIDs = {}
    Module.Recruit.Config.Events.evtOnRecruitmentMapChanged:Invoke()
    RecruitLogic.ClearPublishFilterTarget()
end

RecruitLogic.AddPublishFilterMap = function(mapId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.selectedMapIDs[mapId] = true
    Module.Recruit.Config.Events.evtOnRecruitmentMapChanged:Invoke()
end

RecruitLogic.IsPublishFilterMapSelected = function(mapId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.selectedMapIDs[mapId] == true
end

RecruitLogic.GetPublishFilterTargets = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.selectedTargetIDs
end   

RecruitLogic.TogglePublishFilterTarget = function(targetId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    if publishFilterData.selectedTargetIDs[targetId] == nil then
        table.empty(publishFilterData.selectedTargetIDs)
        publishFilterData.selectedTargetIDs[targetId] = true
    end
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.ClearPublishFilterTarget = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.selectedTargetIDs = {}
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.AddPublishFilterTarget = function(targetId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.selectedTargetIDs[targetId] = true
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.RemovePublishFilterTarget = function(targetId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.selectedTargetIDs[targetId] = nil
    Module.Recruit.Config.Events.evtOnRecruitmentTargetChanged:Invoke()
end

RecruitLogic.IsPublishFilterTargetSelected = function(targetId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.selectedTargetIDs[targetId] == true
end

RecruitLogic.GetPublishFilterLabels = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    return publishFilterData.selectedLabelIDs
end

RecruitLogic.TogglePublishFilterLabel = function(groupId, labelId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    declare_if_nil(publishFilterData.selectedLabelIDs, groupId, {})
    local currentNum = 0
    for groupId, labels in pairs(publishFilterData.selectedLabelIDs) do
        currentNum = currentNum + table.nums(labels)
    end
    local bIsMutexGroup = table.contains(Module.Recruit.Config.LabelMutexGroupIds, groupId)
    if currentNum >= Module.Recruit.Config.labelMaxNum then
        if bIsMutexGroup then
            if publishFilterData.selectedLabelIDs[groupId] == nil or table.isempty(publishFilterData.selectedLabelIDs[groupId]) then
                Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.MaxNumOfLabelHint)
            else
                if publishFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                    table.empty(publishFilterData.selectedLabelIDs[groupId])
                else
                    table.empty(publishFilterData.selectedLabelIDs[groupId])
                    publishFilterData.selectedLabelIDs[groupId][labelId] = 1
                end
            end
        else
            if publishFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                publishFilterData.selectedLabelIDs[groupId][labelId] = nil
                for key, value in pairs(publishFilterData.selectedLabelIDs[groupId]) do
                    if publishFilterData.selectedLabelIDs[groupId][key] > table.nums(publishFilterData.selectedLabelIDs[groupId]) then
                        publishFilterData.selectedLabelIDs[groupId][key] = table.nums(publishFilterData.selectedLabelIDs[groupId])
                    end
                end
            else
                Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.MaxNumOfLabelHint)
            end
        end
    else
        if bIsMutexGroup then
            if publishFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                table.empty(publishFilterData.selectedLabelIDs[groupId])
            else
                table.empty(publishFilterData.selectedLabelIDs[groupId])
                publishFilterData.selectedLabelIDs[groupId][labelId] = 1
            end
        else
            if publishFilterData.selectedLabelIDs[groupId][labelId] ~= nil then
                publishFilterData.selectedLabelIDs[groupId][labelId] = nil
                for key, value in pairs(publishFilterData.selectedLabelIDs[groupId]) do
                    if publishFilterData.selectedLabelIDs[groupId][key] > table.nums(publishFilterData.selectedLabelIDs[groupId]) then
                        publishFilterData.selectedLabelIDs[groupId][key] = table.nums(publishFilterData.selectedLabelIDs[groupId])
                    end
                end
            else
                publishFilterData.selectedLabelIDs[groupId][labelId] = table.nums(publishFilterData.selectedLabelIDs[groupId]) + 1
            end
        end  
    end
    if table.nums(publishFilterData.selectedLabelIDs[groupId]) == 0 then
        publishFilterData.selectedLabelIDs[groupId] = nil
    end
    Module.Recruit.Config.Events.evtOnRecruitmentLabelChanged:Invoke()
end

RecruitLogic.ClearPublishFilterLabel = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    publishFilterData.selectedLabelIDs = {}
end

RecruitLogic.IsPublishFilterLabelSelected = function(groupId, labelId)
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    if publishFilterData.selectedLabelIDs[groupId] ~= nil then
        return publishFilterData.selectedLabelIDs[groupId][labelId] ~= nil
    else
        return false        
    end
end

RecruitLogic.SetPublishFilterData = function(data)
    Module.Recruit.Field:SetFilterData("Publish", data)
end

RecruitLogic.ResetPublishFilterData = function()
    local lastPublishFilterData = RecruitLogic.GetLastPublishFilterData()
    if Module.Recruit:IsRecruiting() == true then
        local publishFilterData = {}
        deepcopy(publishFilterData, lastPublishFilterData)
        RecruitLogic.SetPublishFilterData(publishFilterData)
    else
        RecruitLogic.ClearPublishFilterLabel()
        if RecruitLogic.CheckHasFilteredRecruitment() == true then
            local cachePublishFilterData = {}
            deepcopy(cachePublishFilterData, RecruitLogic.GetPublishFilterData())
            RecruitLogic.ClearPublishFilterMap()
            RecruitLogic.ClearPublishFilterTarget()
            local lastRecommendFilterData = RecruitLogic.GetLastRecommendFilterData()
            for mapId, value in pairs(cachePublishFilterData.selectedMapIDs) do
                if lastRecommendFilterData.selectedMapIDs[mapId] == true then
                    RecruitLogic.TogglePublishFilterMap(mapId)
                end
            end
            if table.isempty(RecruitLogic.GetPublishFilterMaps()) then
                local mapIds = table.keys(lastRecommendFilterData.selectedMapIDs)
                table.sort(mapIds, function (a, b)
                    return a < b
                end)
                for index, mapId in ipairs(mapIds) do
                    RecruitLogic.TogglePublishFilterMap(mapId)
                    break
                end
            end
            for targetId, value in pairs(cachePublishFilterData.selectedTargetIDs) do
                if lastRecommendFilterData.selectedTargetIDs[targetId] == true and RecruitLogic.IsTargetAvailable(targetId, RecruitLogic.GetPublishFilterMaps()) == true then
                    RecruitLogic.TogglePublishFilterTarget(targetId)
                end
            end
            if table.isempty(RecruitLogic.GetPublishFilterTargets()) then
                local targetIds = table.keys(lastRecommendFilterData.selectedTargetIDs)
                table.sort(targetIds, function (a, b)
                    return a < b
                end)
                for index, targetId in ipairs(targetIds) do
                    if RecruitLogic.IsTargetAvailable(targetId, RecruitLogic.GetPublishFilterMaps()) == true then
                        RecruitLogic.TogglePublishFilterTarget(targetId)
                        break
                    end
                end
            end
            if lastRecommendFilterData.micChoice == RecruitmentMicrophoneType.RecruitmentMicrophoneNoLimit then
                RecruitLogic.SetPublishFilterMicChoice(RecruitmentMicrophoneType.RecruitmentMicrophoneEnable)
            else
                RecruitLogic.SetPublishFilterMicChoice(lastRecommendFilterData.micChoice)
            end
            if lastRecommendFilterData.rankDivisionChoice == RecruitmentRankType.RecruitmentRankNoLimit then
                RecruitLogic.SetPublishFilterRankDivisionChoice(RecruitmentRankType.RecruitmentRankEnable)
            else
                RecruitLogic.SetPublishFilterRankDivisionChoice(lastRecommendFilterData.rankDivisionChoice)
            end
        else
            RecruitLogic.SetPublishFilterMicChoice(RecruitmentMicrophoneType.RecruitmentMicrophoneEnable)   
            RecruitLogic.SetPublishFilterRankDivisionChoice(RecruitmentRankType.RecruitmentRankEnable)
        end
        if table.isempty(RecruitLogic.GetPublishFilterMaps()) then
            local mapInfo = nil
            if RecruitLogic.IsInMp() then
                mapInfo = RecruitLogic.GetMPMaps()
            else
                mapInfo = RecruitLogic.GetSOLMaps()
            end
            if #mapInfo > 0 then
                RecruitLogic.TogglePublishFilterMap(mapInfo[1].id)
            end
        end
        if not RecruitLogic.IsInMp() then
            if table.isempty(RecruitLogic.GetPublishFilterTargets()) then
                local targetInfo = RecruitLogic.GetSOLTargets()
                if targetInfo and #targetInfo > 0 then
                    for index, target in ipairs(targetInfo) do
                        if RecruitLogic.IsTargetAvailable(target.id, RecruitLogic.GetPublishFilterMaps()) == true then
                            RecruitLogic.TogglePublishFilterTarget(target.id)
                            break
                        end
                    end 
                end
            end
        end
        for groupId, groupData in pairs(lastPublishFilterData.selectedLabelIDs) do
            for labelId, value in pairs(groupData) do
                if value ~= nil then
                    RecruitLogic.TogglePublishFilterLabel(groupId, labelId)
                end
            end
        end
    end
end

RecruitLogic.GetPublishFilterData = function()
    local publishFilterData = Module.Recruit.Field:GetFilterData("Publish")
    publishFilterData = setdefault(publishFilterData, {})
    publishFilterData.selectedMapIDs = setdefault(publishFilterData.selectedMapIDs, {})
    publishFilterData.selectedTargetIDs = setdefault(publishFilterData.selectedTargetIDs, {})
    publishFilterData.micChoice = setdefault(publishFilterData.micChoice, RecruitmentMicrophoneType.RecruitmentMicrophoneEnable)
    publishFilterData.rankDivisionChoice = setdefault(publishFilterData.rankDivisionChoice, RecruitmentRankType.RecruitmentRankEnable)
    publishFilterData.selectedLabelIDs = setdefault(publishFilterData.selectedLabelIDs, {})
    RecruitLogic.SetPublishFilterData(publishFilterData)
    return publishFilterData
end

RecruitLogic.UpdateLastPublishFilterData = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    local lastPublishFilterData = {}
    deepcopy(lastPublishFilterData, publishFilterData)
    RecruitLogic.SetLastPublishFilterData(lastPublishFilterData)
end

RecruitLogic.SetLastPublishFilterData = function(data)
    Module.Recruit.Field:SetLastFilterData("Publish", data)
end

RecruitLogic.GetLastPublishFilterData = function()
    local publishFilterData = Module.Recruit.Field:GetLastFilterData("Publish")
    publishFilterData = setdefault(publishFilterData, {})
    publishFilterData.selectedMapIDs = setdefault(publishFilterData.selectedMapIDs, {})
    publishFilterData.selectedTargetIDs = setdefault(publishFilterData.selectedTargetIDs, {})
    publishFilterData.micChoice = setdefault(publishFilterData.micChoice, RecruitmentMicrophoneType.RecruitmentMicrophoneUnable)
    publishFilterData.rankDivisionChoice = setdefault(publishFilterData.rankDivisionChoice, RecruitmentRankType.RecruitmentRankUnable)
    publishFilterData.selectedLabelIDs = setdefault(publishFilterData.selectedLabelIDs, {})
    RecruitLogic.SetLastPublishFilterData(publishFilterData)
    return publishFilterData
end

RecruitLogic.ClearAllPublishFilterData = function()
    RecruitLogic.SetPublishFilterData(nil)
    RecruitLogic.SetLastPublishFilterData(nil)
end





RecruitLogic.SetRefreshPendingTime = function(seconds)
    seconds = seconds or 0
    RecruitLogic.StopRefreshTimer()
    if seconds > 0 then   
        Module.Recruit.Field:SetRefreshPendingTime(seconds)
        local timerHandle = Timer:NewIns(1, 0)
        timerHandle:AddListener(RecruitLogic.RefreshPendingTick, self)
        Module.Recruit.Field:SetRefreshTimer(timerHandle)
        timerHandle:Start()
        Module.Recruit.Config.Events.evtOnRecruitmentRefreshPendingChanged:Invoke()
    end
end

RecruitLogic.GetRefreshPendingTime = function()
    return Module.Recruit.Field:GetRefreshPendingTime()
end

RecruitLogic.RefreshPendingTick = function()
    if Module.Recruit.Field:GetRefreshPendingTime() <= 0 then
        RecruitLogic.StopRefreshTimer()  
    else
        Module.Recruit.Field:SetRefreshPendingTime(Module.Recruit.Field:GetRefreshPendingTime() - 1)
    end
    Module.Recruit.Config.Events.evtOnRecruitmentRefreshPendingChanged:Invoke()
end

RecruitLogic.StopRefreshTimer = function()
    local timerHandle = Module.Recruit.Field:GetRefreshTimer()
    if isvalid(timerHandle) then
        timerHandle:Release()
        Module.Recruit.Field:SetRefreshTimer(nil)
    end  
    Module.Recruit.Field:SetRefreshPendingTime(0)
end



RecruitLogic.SetRecruitmentLifeTime = function(seconds)
    seconds = seconds or 0
    RecruitLogic.StopRecruitmentTimer() 
    if seconds > 0 then     
        Module.Recruit.Field:SetRecruitmentLifeTime(seconds)
        local timerHandle = Timer:NewIns(1, 0)
        timerHandle:AddListener(RecruitLogic.RecruitmentPendingTick, self)
        Module.Recruit.Field:SetRecruitmentTimer(timerHandle)
        timerHandle:Start()
        Module.Recruit.Config.Events.evtOnRecruitmentLifeTimeChanged:Invoke()
    end
end

RecruitLogic.GetRecruitmentLifeTime = function()
    return Module.Recruit.Field:GetRecruitmentLifeTime()
end

RecruitLogic.RecruitmentPendingTick = function()
    if Module.Recruit.Field:GetRecruitmentLifeTime() <= 0 then
        RecruitLogic.StopRecruitmentTimer()  
        if Module.Recruit:IsRecruiting() == true then
            RecruitLogic.StopRecruitment()
        end
    else
        Module.Recruit.Field:SetRecruitmentLifeTime(Module.Recruit.Field:GetRecruitmentLifeTime() - 1)
    end
    Module.Recruit.Config.Events.evtOnRecruitmentLifeTimeChanged:Invoke()
end

RecruitLogic.StopRecruitmentTimer = function()
    local timerHandle = Module.Recruit.Field:GetRecruitmentTimer()
    if isvalid(timerHandle) then
        timerHandle:Release()
        Module.Recruit.Field:SetRecruitmentTimer(nil)
        Module.Recruit.Field:SetRecruitmentLifeTime(0)
    end  
end



RecruitLogic.SortMembersBySeat = function(members)
    local memberList={}
    for k,v in pairs(members) do
        table.insert(memberList,v)
    end
    table.sort(memberList, function(a,b) return a.Seat<b.Seat end)
    return memberList
end

RecruitLogic.GetSOLMaps = function()
    local maps = {}
    local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()
    for key, entry in pairs(Module.Recruit.Config.SOLMapConfig) do
        if (entry.GameMode == 1 and entry.GameRuleType == 4 or entry.GameMode ~= 1) and Server.GameModeServer:CheckWorldntranceValid(entry.EntranceIdx) == true and sandBoxMapUtil.IsTargetPointLocked(entry) == false then
            table.insert(maps, {id = entry.EntranceIdx, name = entry.EntranceMainName})
        end
    end
    table.sort(maps, function (a, b)
        return a.id < b.id
    end)
    return maps
end

RecruitLogic.GetSOLTargets = function()
    local targets = Module.Recruit.Field:GetSOLTargets()
    targets = setdefault(targets, {})
    if table.isempty(targets) then
        for targetId, text in pairs(Module.Recruit.Config.TargetId2Text) do
            table.insert(targets, {id = targetId, name = text})
        end
    end
    table.sort(targets, function (a, b)
        return a.id < b.id
    end)
    Module.Recruit.Field:SetSOLTargets(targets)
    return targets
end

RecruitLogic.GetMPMaps = function()
    local mapsDict = {}
    local availableMapGroups = Server.GameModeServer:GetAvailableTDMGroup()
    for index, groupId in ipairs(availableMapGroups) do
        local mapInfoList = Server.GameModeServer:GetOpenedMapInGroup(groupId)
        if mapInfoList and mapInfoList[1] then
            declare_if_nil(mapsDict, groupId , {id = groupId, name = Module.GameMode:GetTDMModeNameByGroupId(groupId)})
        end
    end
    local maps = table.values(mapsDict)
    table.sort(maps, function (a, b)
        return a.id < b.id
    end)
    return maps
end

RecruitLogic.GetSOLLabelGroups = function()
    local labelGroup = Module.Recruit.Field:GetSOLLabelGroups()
    labelGroup = setdefault(labelGroup, {})
    if table.isempty(labelGroup) then
        local labelGroupDict = {}
        local groupId
        for index, labelId in ipairs(Module.Recruit.Config.SOLlabelIds) do
            local labelInfo = {}
            labelInfo.id = labelId
            labelInfo.name = Module.Recruit.Config.LabelId2Text[labelId]
            labelInfo.iconPath = Module.Recruit.Config.LabelId2IconPath[labelId]
            groupId = Module.Recruit.Config.LabelId2LabelGroupId[labelInfo.id]
            declare_if_nil(labelGroupDict, groupId , {groupId=groupId, labels={}})
            table.insert(labelGroupDict[groupId].labels, labelInfo)
        end
        labelGroup = table.values(labelGroupDict)
        table.sort(labelGroup, function (a, b)
            return a.groupId < b.groupId
        end)
    end
    Module.Recruit.Field:SetSOLLabelGroups(labelGroup)
    return labelGroup
end

RecruitLogic.GetMPLabelGroups = function()
    local labelGroup = Module.Recruit.Field:GetMPLabelGroups()
    labelGroup = setdefault(labelGroup, {})
    if table.isempty(labelGroup) then
        if table.isempty(labelGroup) then
            local labelGroupDict = {}
            local groupId
            for index, labelId in ipairs(Module.Recruit.Config.MPlabelIds) do
                local labelInfo = {}
                labelInfo.id = labelId
                labelInfo.name = Module.Recruit.Config.LabelId2Text[labelId]
                labelInfo.iconPath = Module.Recruit.Config.LabelId2IconPath[labelId]
                groupId = Module.Recruit.Config.LabelId2LabelGroupId[labelInfo.id]
                declare_if_nil(labelGroupDict, groupId , {groupId=groupId, labels={}})
                table.insert(labelGroupDict[groupId].labels, labelInfo)
            end
            labelGroup = table.values(labelGroupDict)
            table.sort(labelGroup, function (a, b)
                return a.groupId < b.groupId
            end)
        end
    end
    Module.Recruit.Field:SetMPLabelGroups(labelGroup)
    return labelGroup
end

RecruitLogic.GetFilterBtnTitle = function()
    local recommendFilterData = RecruitLogic.GetLastRecommendFilterData()
    recommendFilterData = setdefault(recommendFilterData, {})
    local title = "" 
    local selectedMapIds = setdefault(recommendFilterData.selectedMapIDs, {})
    local selectedTargetIds = setdefault(recommendFilterData.selectedTargetIDs, {})
    if RecruitLogic.IsInMp() then
        for groupId, value in pairs(selectedMapIds) do
            title = Module.GameMode:GetTDMModeNameByGroupId(groupId) or title
            if title ~= "" then
                break
            end
        end
    else
        for mapId, value in pairs(selectedMapIds) do
            local maps = {}
            maps[mapId] = true
            for targetId, value in pairs(selectedTargetIds) do
                if RecruitLogic.IsTargetAvailable(targetId, maps) == true then
                    matchModeInfo = Server.GameModeServer:GetMatchModeInfoByIDAndSubMode(mapId, Module.Recruit.Config.TargetId2SubMode[targetId], 3)
                    if isvalid(matchModeInfo) then
                        title = Module.GameMode:GetStandardMapNameByMatchModeId(matchModeInfo.mode.match_mode_id) or title
                        break
                    end
                end
            end
            if title ~= "" then
                break
            end
        end
    end
    if table.nums(recommendFilterData.selectedMapIDs) > 1 then
        title = string.format(Module.Recruit.Config.Loc.Etc, title)
    end
    return title
end

RecruitLogic.IsTargetAvailable = function(targetId, selectedMapIDs)
    logerror("[v_dzhanshen] RecruitLogic.IsTargetAvailable targetId="..tostring(targetId))
    for mapId, value in pairs(selectedMapIDs) do
        local options = Server.GameModeServer:GetEntranceDifficulty(mapId)
        for index, difficulty in ipairs(options) do
            logerror("[v_dzhanshen] RecruitLogic.IsTargetAvailable targetId="..tostring(targetId).." difficulty="..tostring(difficulty))
            logerror("[v_dzhanshen] RecruitLogic.IsTargetAvailable targetId="..tostring(targetId).." CheckIfEntranceDifficultyUnlocked="..tostring(Server.GameModeServer:CheckIfEntranceDifficultyUnlocked(mapId, difficulty)))
            if targetId == difficulty and Server.GameModeServer:CheckIfEntranceDifficultyUnlocked(mapId, difficulty) == true then
                return true
            end
        end
    end 
    return false
end

RecruitLogic.CanPublishRecruitment = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    if table.nums(publishFilterData.selectedMapIDs) == 0 then
        return false
    elseif RecruitLogic.IsInMp() == false and table.nums(publishFilterData.selectedTargetIDs) == 0 then
        return false
    elseif Server.AccountServer:IsInRoom() then
        return false
    elseif Module.Recruit:IsRecruiting() == true then
        return RecruitLogic.NeedUpdatePublishFilter()
    end
    return true
end

RecruitLogic.SetFirstTimeOpenRecruitmentPanel = function(bFirstTime)
    Module.Recruit.Field:SetFirstTimeOpenRecruitmentPanel(bFirstTime)
end

RecruitLogic.CheckFirstTimeOpenRecruitmentPanel = function()
    return Module.Recruit.Field:GetFirstTimeOpenRecruitmentPanel() == true
end

RecruitLogic.SetIsRecruiting = function(bHasPublished)
    Module.Recruit.Field:SetIsRecruiting(bHasPublished)
end

RecruitLogic.IsRecruiting = function()
    return Module.Recruit.Field:GetIsRecruiting() == true
end

RecruitLogic.SetHasFilteredRecruitment = function(bHasFiltered)
    Module.Recruit.Field:SetHasFilteredRecruitment(bHasFiltered)
end

RecruitLogic.CheckHasFilteredRecruitment = function()
    return Module.Recruit.Field:GetHasFilteredRecruitment() == true
end

RecruitLogic.PublishRecruitment = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    local serverData = RecruitLogic.localDataToServerData(publishFilterData)
    if serverData == nil then
        return
    end
    if Module.Recruit:IsRecruiting() == true then
        Server.RecruitServer:ReqEditRecruitment(serverData)
    elseif Server.AccountServer:IsInRoom() == false then
        if Server.AccountServer:IsInTeam() then
            --BEGIN VIRTUPOS MODE @Zhang Yingqi
            if PLATFORM_PS5 == 1 then
                logwarning("[RecruitLogic] PublishRecruitment: Player is in team")
                local callback = function(bSuccessful)
                    if bSuccessful then
                        Server.RecruitServer:ReqPublishRecruitment(serverData)
                        
                        local sessionId = Module.Team:GetSessionID()
                        local teamId = Server.TeamServer:GetTeamID()
                        Server.TeamServer:ReqUpdatePlatformSessionID(teamId, sessionId)
                    end
                end
                Module.Social:ProcessPlatformSession(EPlatformSessionRestriction.Public, callback)
                return
            end
            --END VIRTUOS MODE
            Server.RecruitServer:ReqPublishRecruitment(serverData)
        elseif not Server.MatchServer:GetIsMatching() then
            --[[
            if RecruitLogic.IsInMp() then
                for groupId, value in pairs(publishFilterData.selectedMapIDs) do
                    --Module.BattlefieldEntry:ChangeTDMMapGroup(groupId)
                    break
                end
            else
                local worldEntranceId, subMode
                for mapId, value in pairs(publishFilterData.selectedMapIDs) do
                    worldEntranceId = mapId
                    break
                end
                for targetId, value in pairs(publishFilterData.selectedTargetIDs) do
                    subMode = Module.Recruit.Config.TargetId2SubMode[targetId]
                    break
                end
                --Server.GameModeServer:SetMatchModeByWorldEntrancCfg(worldEntranceId, subMode)
            end
            --]]
            --BEGIN VIRTUPOS MODE @Zhang Yingqi
            if PLATFORM_PS5 == 1 then
                logwarning("[RecruitLogic] PublishRecruitment: Player isn't in team")
                local callback = function(bSuccessful)
                    if bSuccessful then
                        Module.Team:CreateTeam(2, function()
                            if Server.AccountServer:IsInTeam() then
                                Server.RecruitServer:ReqPublishRecruitment(serverData)
                            end
                        end)
                    end
                end
                Module.Social:ProcessPlatformSession(EPlatformSessionRestriction.Public, callback)
                return
            end
            --END VIRTUOS MODE
            Module.Team:CreateTeam(2, function()
                if Server.AccountServer:IsInTeam() then
                    Server.RecruitServer:ReqPublishRecruitment(serverData)
                end
            end)
        end
    end
end

RecruitLogic.StopRecruitment = function(fCallback)
    Server.RecruitServer:ReqStopRecruitment(fCallback)
end

RecruitLogic.LoadRecommendRecruitment = function()
    local recommendFilterData = RecruitLogic.GetLastRecommendFilterData()
    if table.nums(recommendFilterData.selectedMapIDs) == 0 then
        return
    end
    if not RecruitLogic.IsInMp() then
        if table.nums(recommendFilterData.selectedTargetIDs) == 0 then
            return
        end
    end
    local lastTeamIds = {}
    local listInfo = RecruitLogic.GetLastRecommendListInfo()
    if listInfo then
        for index, info in ipairs(listInfo) do
            table.insert(lastTeamIds, info.team_id)
        end
    end
    local serverData = RecruitLogic.localDataToServerData(recommendFilterData, true)
    Server.RecruitServer:ReqFetchRecommendRecruitment(serverData, lastTeamIds)
end


RecruitLogic.ReloadRecommendRecruitment = function(fCustomCallback)
    if table.nums(RecruitLogic.GetRecommendFilterMaps()) == 0 then
        return
    end
    if not RecruitLogic.IsInMp() then
        if table.nums(RecruitLogic.GetRecommendFilterTargets()) == 0 then
            return
        end
    end
    local lastTeamIds = {}
    local listInfo = RecruitLogic.GetLastRecommendListInfo()
    if listInfo then
        for index, info in ipairs(listInfo) do
            table.insert(lastTeamIds, info.team_id)
        end
    end
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    local serverData = RecruitLogic.localDataToServerData(recommendFilterData, true)
    Server.RecruitServer:ReqFetchRecommendRecruitment(serverData, lastTeamIds, fCustomCallback)
end


RecruitLogic.LoadMiniChatRecruitment = function(bNeedReset)
    local roomId = Server.FrontEndChatServer:GetRoomId()
    if roomId and roomId > 0 then
        if bNeedReset == true then
            RecruitLogic.SetLastMiniChatIndex(nil)
            RecruitLogic.SetMiniChatRecruitmentList(nil)
            RecruitLogic.SetAppendedMiniChatRecruitmentList(nil)
        end
        Server.RecruitServer:ReqFetchMiniChatRecruitment(RecruitLogic.GetLastMiniChatIndex(), roomId, RecruitLogic.IsInMp())
    end
end

RecruitLogic.UpdateRecommendRecruitment = function()
    local listInfo = RecruitLogic.GetLastRecommendListInfo()
    local recommendFilterData = RecruitLogic.GetLastRecommendFilterData()
    local serverData = RecruitLogic.localDataToServerData(recommendFilterData, true)
    if table.nums(listInfo) > 0 then
        Server.RecruitServer:ReqUpdateRecommendRecruitment(listInfo, RecruitLogic.IsInMp() ,serverData)
    end
end

RecruitLogic.NeedUpdatePublishFilter = function()
    local publishFilterData = RecruitLogic.GetPublishFilterData()
    local lastPublishFilterData = RecruitLogic.GetLastPublishFilterData()
    if table.nums(publishFilterData.selectedMapIDs) ~= table.nums(lastPublishFilterData.selectedMapIDs) then
        return true
    end
    local keys = table.keys(publishFilterData.selectedMapIDs)
    local lastKeys = table.keys(lastPublishFilterData.selectedMapIDs)
    for index, key in pairs(keys) do
        if key ~= lastKeys[index] then
            return true
        end
    end
    if table.nums(publishFilterData.selectedTargetIDs) ~= table.nums(lastPublishFilterData.selectedTargetIDs) then
        return true
    end
    keys = table.keys(publishFilterData.selectedTargetIDs)
    lastKeys = table.keys(lastPublishFilterData.selectedTargetIDs)
    for index, key in pairs(keys) do
        if key ~= lastKeys[index] then
            return true
        end
    end
    if publishFilterData.micChoice ~= lastPublishFilterData.micChoice then
        return true
    end
    if publishFilterData.rankDivisionChoice ~= lastPublishFilterData.rankDivisionChoice then
        return true
    end
    for groupId, groupData in pairs(publishFilterData.selectedLabelIDs) do
        if lastPublishFilterData.selectedLabelIDs[groupId] == nil then
            return true
        end
        for labelId, value in pairs(groupData) do
            if lastPublishFilterData.selectedLabelIDs[groupId][labelId] == nil then
                return true
            end
        end
    end
    for groupId, groupData in pairs(lastPublishFilterData.selectedLabelIDs) do
        if publishFilterData.selectedLabelIDs[groupId] == nil then
            return true
        end
        for labelId, value in pairs(groupData) do
            if publishFilterData.selectedLabelIDs[groupId][labelId] == nil then
                return true
            end
        end
    end
    return false
end

RecruitLogic.NeedUpdateRecommendFilter = function()
    local recommendFilterData = RecruitLogic.GetRecommendFilterData()
    local lastRecommendFilterData = RecruitLogic.GetLastRecommendFilterData()
    local selectedIds = {}
    for groupId, groupData in pairs(recommendFilterData.selectedLabelIDs) do
        for labelId, value in pairs(groupData) do
            if value == true then
                table.insert(selectedIds, labelId)
            end
        end
    end
    local lastSelectedIds = {}
    for groupId, groupData in pairs(lastRecommendFilterData.selectedLabelIDs) do
        for labelId, value in pairs(groupData) do
            if value == true then
                table.insert(lastSelectedIds, labelId)
            end
        end
    end
    if table.nums(selectedIds) ~= table.nums(lastSelectedIds) then
        return true
    else
        for index, labelId in ipairs(selectedIds) do
            if labelId ~= lastSelectedIds[index] then
                return true
            end
        end -- body
    end
    if table.nums(recommendFilterData.selectedTargetIDs) ~= table.nums(lastRecommendFilterData.selectedTargetIDs) then
        return true
    end
    local keys = table.keys(recommendFilterData.selectedTargetIDs)
    local lastKeys = table.keys(lastRecommendFilterData.selectedTargetIDs)
    for index, key in pairs(keys) do
        if key ~= lastKeys[index] then
            return true
        end
    end
    if recommendFilterData.micChoice ~= lastRecommendFilterData.micChoice then
        return true
    end
    if recommendFilterData.rankDivisionChoice ~= lastRecommendFilterData.rankDivisionChoice then
        return true
    end
    if table.nums(recommendFilterData.selectedLabelIDs) ~= table.nums(lastRecommendFilterData.selectedLabelIDs) then
        return true
    end
    local keys = table.keys(recommendFilterData.selectedLabelIDs)
    local lastKeys = table.keys(lastRecommendFilterData.selectedLabelIDs)
    for index, key in pairs(keys) do
        if key ~= lastKeys[index] then
            return true
        end
    end
    return false
end

RecruitLogic.serverDataToLocalData = function(serverData)
    local localData = {}
    localData.bIsMP = false
    localData.selectedMapIDs = {}
    localData.selectedTargetIDs = {}
    localData.micChoice = RecruitmentMicrophoneType.RecruitmentMicrophoneEnable
    localData.rankDivisionChoice = RecruitmentRankType.RecruitmentRankEnable
    localData.selectedLabelIDs = {}
    localData.match_mode_id = serverData.match_mode_id
    localData.difficulty = serverData.difficulty
    localData.game_rule = serverData.game_rule
    localData.group_id = serverData.group_id
    if serverData == nil then
        return localData
    end
    localData.bIsMP = serverData.game_mode == 2
    if localData.bIsMP then
        if serverData.group_id then
            declare_if_nil(localData.selectedMapIDs, serverData.group_id, true) 
        end
    else
        local subModeType = nil
        local matchModeInfo = nil
        local entryConfig
        for index, mapId in ipairs(serverData.point_id) do
            declare_if_nil(localData.selectedMapIDs, mapId, true) 
        end
        for index, option in ipairs(serverData.difficulty) do
            declare_if_nil(localData.selectedTargetIDs, option, true) 
        end
    end
    localData.micChoice = serverData.microphone_type or RecruitmentMicrophoneType.RecruitmentMicrophoneEnable
    localData.rankDivisionChoice = serverData.rank_type or RecruitmentRankType.RecruitmentRankEnable
    localData.selectedLabelIDs = {}
    local labelId
    labelId = Module.Recruit.Config.LabelTacticalType2Id[serverData.tactical_style]
    if labelId ~= nil then
        local groupId = Module.Recruit.Config.LabelId2LabelGroupId[labelId]
        declare_if_nil(localData.selectedLabelIDs, groupId, {}) 
        declare_if_nil(localData.selectedLabelIDs[groupId], labelId, 1) 
    end
    labelId = nil
    labelId = Module.Recruit.Config.LabelEquipType2Id[serverData.equip_style]
    if labelId ~= nil then
        local groupId = Module.Recruit.Config.LabelId2LabelGroupId[labelId]
        declare_if_nil(localData.selectedLabelIDs, groupId, {}) 
        declare_if_nil(localData.selectedLabelIDs[groupId], labelId, 1) 
    end
    if not table.isempty(serverData.armed_force_id_list) then
        for index, labelType in ipairs(serverData.armed_force_id_list) do
            labelId = nil
            labelId = Module.Recruit.Config.LabelArmedForceType2Id[labelType]
            if labelId ~= nil then
                local groupId = Module.Recruit.Config.LabelId2LabelGroupId[labelId]
                declare_if_nil(localData.selectedLabelIDs, groupId, {}) 
                declare_if_nil(localData.selectedLabelIDs[groupId], labelId, table.nums(localData.selectedLabelIDs[groupId])) 
            end
        end
    end
    return localData
end

RecruitLogic.localDataToServerData = function(localData, bKeepValidOption)
    loginfo("RecruitLogic.localDataToServerData")
    local filter = {}
    filter.game_mode = RecruitLogic.IsInMp() and 2 or 1
    local selectedMapIds = localData.selectedMapIDs
    local selectedTargetIds = localData.selectedTargetIDs
    filter.match_mode_id_list = {}
    filter.group_id = 0
    filter.group_id_list = {}
    filter.match_sub_mode = {}
    if RecruitLogic.IsInMp() then
        filter.game_rule = {}
        filter.point_id = {0}
        filter.difficulty = {0}
        local availableMapGroups = Server.GameModeServer:GetAvailableTDMGroup()
        logtable(selectedMapIds,true)
        for groupId, value in pairs(selectedMapIds) do
            if table.contains(availableMapGroups, groupId) then
                filter.group_id = groupId
                table.insert(filter.group_id_list, groupId)
            elseif not bKeepValidOption then
                Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.InvalidMapToPublishRecruitment)
                return nil
            end
            local mapInfoList = Module.BattlefieldEntry:GetValidMapsByGroupId(groupId)
            if mapInfoList then
                for index, mapInfo in ipairs(mapInfoList) do
                    if not table.contains(filter.game_rule, mapInfo.mode.game_rule) then
                        table.insert(filter.game_rule, mapInfo.mode.game_rule)
                    end
                    if not table.contains(filter.match_sub_mode, mapInfo.mode.sub_mode) then
                        table.insert(filter.match_sub_mode, mapInfo.mode.sub_mode)
                    end
                    filter.match_mode_id = mapInfo.mode.match_mode_id
                    table.insert(filter.match_mode_id_list, mapInfo.mode.match_mode_id)
                end
            end
        end
    else
        local matchModeInfo = nil
        filter.point_id = {}
        filter.difficulty = table.keys(selectedTargetIds)
        filter.game_rule = {4}
        local sandBoxMapUtil = Module.SandBoxMap:GetSandBoxMapUtil()
        local bValidMapId
        for mapId, value in pairs(selectedMapIds) do
            bValidMapId = true
            for key, entry in pairs(Module.Recruit.Config.SOLMapConfig) do
                if mapId == entry.EntranceIdx and (Server.GameModeServer:CheckWorldntranceValid(entry.EntranceIdx) ~= true or sandBoxMapUtil.IsTargetPointLocked(entry) == true) then
                    if bKeepValidOption then
                        bValidMapId = false
                        break
                    else
                        Module.CommonTips:ShowSimpleTip(Module.Recruit.Config.Loc.InvalidMapToPublishRecruitment)
                        return nil
                    end
                end
            end
            if bValidMapId then
                table.insert(filter.point_id, mapId)
            end
        end 
        for index, targetId in ipairs(filter.difficulty) do
            if RecruitLogic.IsTargetAvailable(targetId, selectedMapIds) == true then
                table.insert(filter.match_sub_mode, Module.Recruit.Config.TargetId2SubMode[targetId])
                for mapId, value in pairs(selectedMapIds) do
                    matchModeInfo = Server.GameModeServer:GetMatchModeInfoByIDAndSubMode(mapId, Module.Recruit.Config.TargetId2SubMode[targetId], 3)
                    if isvalid(matchModeInfo) then
                        filter.match_mode_id = matchModeInfo.mode.match_mode_id
                        table.insert(filter.match_mode_id_list, matchModeInfo.mode.match_mode_id)
                    end
                end
            end
        end
    end
    filter.microphone_type = localData.micChoice
    filter.rank_type = localData.rankDivisionChoice
    local selectedLabelIDs = localData.selectedLabelIDs
    if selectedLabelIDs[1] ~= nil then
        for labelId, value in pairs(selectedLabelIDs[1]) do
            filter.tactical_style = Module.Recruit.Config.LabelId2Type[labelId]
            break
        end
    elseif selectedLabelIDs[3] ~= nil then
        for labelId, value in pairs(selectedLabelIDs[3]) do
            filter.tactical_style = Module.Recruit.Config.LabelId2Type[labelId]
            break
        end
    end
    if selectedLabelIDs[2] ~= nil then
        for labelId, value in pairs(selectedLabelIDs[2]) do
            filter.equip_style = Module.Recruit.Config.LabelId2Type[labelId]
            break
        end
    end
    filter.armed_force_id_list = {}
    if selectedLabelIDs[4] ~= nil then
        local posToId = {}
        for labelId, value in pairs(selectedLabelIDs[4]) do
            posToId[value] = labelId
        end
        local posList = table.keys(posToId)
        table.sort(posList, function (a, b)
            return a < b
        end)
        for index, pos in ipairs(posList) do
            table.insert(filter.armed_force_id_list, Module.Recruit.Config.LabelId2Type[posToId[pos]])
        end
    end
    return filter
end

RecruitLogic.LabelIDToOrderList = function(LabelIDGroups)
    local labelIdList = {}
    for groupId, labelMap in pairs(LabelIDGroups) do
        local posToId = {}
        for labelId, value in pairs(labelMap) do
            posToId[value] = labelId
        end
        local posList = table.keys(posToId)
        table.sort(posList, function (a, b)
            return a < b
        end)
        for index, pos in ipairs(posList) do
            table.insert(labelIdList, posToId[pos])
        end
    end
    return labelIdList
end

RecruitLogic.SetLastRecommendListInfo = function(listInfo)
    Module.Recruit.Field:SetLastRecommendListInfo(listInfo)
end

RecruitLogic.GetLastRecommendListInfo = function()
    local listInfo = Module.Recruit.Field:GetLastRecommendListInfo()
    listInfo = setdefault(listInfo, {})
    Module.Recruit.Field:SetLastRecommendListInfo(listInfo)
    return listInfo
end

RecruitLogic.SetLastMiniChatIndex = function(index)
    Module.Recruit.Field:SetLastMiniChatIndex(index)
end

RecruitLogic.GetLastMiniChatIndex = function()
    return Module.Recruit.Field:GetLastMiniChatIndex()
end

RecruitLogic.SetRecruitmentList = function(recruitmentList)
    Module.Recruit.Field:SetRecruitmentList(recruitmentList)
end

RecruitLogic.GetRecruitmentList = function()
    local recruitmentList = Module.Recruit.Field:GetRecruitmentList()
    recruitmentList = setdefault(recruitmentList, {})
    Module.Recruit.Field:SetRecruitmentList(recruitmentList)
    return recruitmentList
end

RecruitLogic.SetMiniChatRecruitmentList = function(recruitmentList)
    Module.Recruit.Field:SetMiniChatRecruitmentList(recruitmentList)
end

RecruitLogic.GetMiniChatRecruitmentList = function()
    local recruitmentList = Module.Recruit.Field:GetMiniChatRecruitmentList()
    recruitmentList = setdefault(recruitmentList, {})
    Module.Recruit.Field:SetMiniChatRecruitmentList(recruitmentList)
    return recruitmentList
end

RecruitLogic.SetAppendedMiniChatRecruitmentList = function(recruitmentList)
    Module.Recruit.Field:SetAppendedMiniChatRecruitmentList(recruitmentList)
end

RecruitLogic.GetAppendedMiniChatRecruitmentList = function()
    local recruitmentList = Module.Recruit.Field:GetAppendedMiniChatRecruitmentList()
    recruitmentList = setdefault(recruitmentList, {})
    Module.Recruit.Field:SetMiniChatRecruitmentList(recruitmentList)
    return recruitmentList
end

RecruitLogic.AddTeamIdRequireMic = function(teamId)
    Module.Recruit.Field:AddTeamIdRequireMic(teamId)
end

RecruitLogic.CheckTeamIdRequireMic = function(teamId)
    return Module.Recruit.Field:CheckTeamIdRequireMic(teamId)
end

RecruitLogic.ClearTeamIdRequireMic = function()
    Module.Recruit.Field:ClearTeamIdRequireMic()
end

-- azhengzheng:组队码功能
RecruitLogic.OpenTeamCodePop = function()
    Facade.UIManager:AsyncShowUI(UIName2ID.RecruitTeamCodePop, function() end, {}, nil)
end

return RecruitLogic
