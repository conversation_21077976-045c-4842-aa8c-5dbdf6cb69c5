----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTransition)
----- LOG FUNCTION AUTO GENERATE END -----------



-- 用于处理在关卡切换前准备一些数据供Loading时使用

local TransitionModule = class("TransitionModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local TransitionLogic = require("DFM.Business.Module.TransitionModule.TransitionLogic")
local ULuaExtension = import("LuaExtension")
local UPerfGearFuncLib = import "PerfGearFuncLib"
local PerfGearInst = UPerfGearFuncLib.GetPerfGearInst()
local UGPGConfigUtils = import "GPGConfigUtils"
local ERHIType = import ("ERHIType")
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"

local function log(...)
    loginfo("[TransitionModule]", ...)
end

function TransitionModule:Ctor()
end

function TransitionModule:OnInitModule()
    log("TransitionModule:OnInitModule()")
    TransitionLogic.AddListeners()
    
    if IsHD() then
        if ULuaExtension.CheckIsHDD() then
            -- PC机械硬盘关闭视频入局
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.Loading.EnableMPLoadingVideo 0", nil)
        end
    end
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Slate.EnableIntersectTextByLayoutRect 1", nil)
    if not IsHD() then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.BinkReleaseResourceAfterClose 1", nil) ----bug=146949803 [【安卓|iOS|PC】【AtBug】曼德尔砖 FBinkMediaTextureResource 泄漏](https://tapd.woa.com/r/t?id=146949803&type=bug) 
    end
end

function TransitionModule:OnDestroyModule()
    TransitionLogic.RemoveListeners()
end

local GEnableLeaveGameCloseNetDirver = true
-- 收集Loading所需的数据，保存到LoadingManager中
    -- match mode id
    -- player level
    -- num of games
function TransitionModule:OnGameFlowChangeEnter(gameFlowType)
    -- 处理需要关心的gameflow，在这个时候处理数据
    TransitionLogic.UpdateDestination(gameFlowType)

    -- 引擎自动化检测适配，提供自动进入大厅的能力，只有启动命令行存在-frontendtest的时候才会触发
    if _WITH_FRONTEND_TEST then
        if gameFlowType == EGameFlowStageType.Login then
            loginfo("Frontend Auto Test: LoginToModeHall, Delay 2 secs")
            Timer.DelayCall(2, function()
                -- 关闭Lua报错弹窗，避免影响检测流程
                CloseModuleType.bIsCloseLuaErrTips = true
                ULuaExtension.SetLuaErrTipsFlag(not CloseModuleType.bIsCloseLuaErrTips)
                -- 2秒后自动触发进入模式大厅的流程
                Module.Login.Config.flowEvtLoginSuccess:Invoke("SafeHouse")
            end)
        end

        if gameFlowType == EGameFlowStageType.SafeHouse then
            -- 30秒后打印成功日志
            Timer.DelayCall(30, function()
                loginfo("Frontend Auto Test: Enter SafeHouse Success!")
            end)
        end
    end

    if gameFlowType == EGameFlowStageType.LoginToModeHall then
        if Server.AccountServer:GetPlayerRegisterState() == false then
            if IsHD() then
                -- PC新号会播CG，要预加载一下CG资源
                if IsBuildRegionCN() then
                    Facade.SoundManager:PreLoadAssetByName("CS_Openning_Season")
                else
                    Facade.SoundManager:PreLoadAssetByName("CS_Openning_S1_Oversea")
                end
            end
            
            Facade.SoundManager:PreLoadAssetByName("CS_Battlefield_Tutorial")
        end
    end

    if gameFlowType == EGameFlowStageType.SafeHouse then
        if Server.AccountServer:GetPlayerRegisterState() == false then
            -- 新号无缝进去会加载
            Facade.SoundManager:PreLoadAssetByName("CS_SOL_Tutorial")
        end
    end

    if gameFlowType == EGameFlowStageType.GameToLobby or gameFlowType == EGameFlowStageType.GameToSafeHouse then
        if PerfGearInst:GetRHIType() == ERHIType.D3D12 then
            UGPGConfigUtils.SetInt("WindowsRHIInfo", "GPUCrashCount", 0, "Engine")
            local GConfig = FConfigCacheIni.GetGlobalConfig()
            local EngineIni = FConfigCacheIni.LoadGlobalIni("Engine")
            GConfig:Flush(false, EngineIni)

            local videoSetting = import("ClientVideoSettingHD").Get()
            videoSetting.GPUCrashCount = 0
            videoSetting:SaveToSaved()

            logerror("@jasonscheng reset gpu crash count")
        end

        if PerfGearInst:GetRHIInfo() == 2 then 
            UGPGConfigUtils.SetInt("AndroidRHIPlatformInfo", "VulkanCrashSize", 0, "Engine")
            local GConfig = FConfigCacheIni.GetGlobalConfig()
            local EngineIni = FConfigCacheIni.LoadGlobalIni("Engine")
            GConfig:Flush(false, EngineIni)
            logerror("[oricai] reset crash size 0 !!!")
        end

        -- 结算后切GameFlow时就断开所有NetDriver的通信
        if Facade.ModuleManager:IsModuleValid("Settlement") and GEnableLeaveGameCloseNetDirver then
            Module.Settlement:InvokeGameNotifyCloseNetDirver()
        end
    end

    if gameFlowType == EGameFlowStageType.LobbyToGame then
        TransitionLogic.ReleaseAllStringTables()
        clearlualoctext() --bug=143143794 [【安卓|iOS|PC】【AtBug】luatextcache局内内存释放](https://tapd.woa.com/r/t?id=143143794&type=bug) 
    end

    if gameFlowType == EGameFlowStageType.Game then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Slate.EnableIntersectTextByLayoutRect 0", nil)
    end

    local currentCulture = LocalizeTool.GetCurrentCulture()
    LocalizeTool.SetCurrentCulture(currentCulture)
end

-- 当触发Leave的时候，可以传值给GameLoadingManager
function TransitionModule:OnGameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.ModeHall 
        or gameFlowType == EGameFlowStageType.Lobby
        or gameFLowType == EGameFlowStageType.SafeHouse then
        -- 拉取玩家游玩局数和等级的相关信息
        TransitionLogic.UpdatePlayerLevel()
        TransitionLogic.UpdateGameNum()
    end

    -- she3 临时处理 @aidenliao
    if gameFlowType == EGameFlowStageType.Game then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "Slate.EnableIntersectTextByLayoutRect 1", nil)
    end
end

return TransitionModule