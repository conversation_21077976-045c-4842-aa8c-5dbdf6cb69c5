----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLooting)
----- LOG FUNCTION AUTO GENERATE END -----------



local EDeadType = import "EDeadType"
local FLootSearchInfo = import "LootSearchInfo"
local EItemMoveResult = import "EItemMoveResult"
local EAttachPosition = import "EAttachPosition"
local EInGameBattleClassType = import "EInGameBattleClassType"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local UHUDStateManager = UE.HUDStateManager
local EDFMGamePlayMode = import "EDFMGamePlayMode"
local UCaptureStudioManager = import "CaptureStudioManager"
local UDFMGameGPM = import "DFMGameGPM"
local ABaseHUD = import "BaseHUD"
local EItemInfoUpdatedReason = import "EItemInfoUpdatedReason"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local ULuaExtension = import("LuaExtension")
local UGPBattleFieldSystem = import "GPBattleFieldSystem"
local ULootUtil = import "LootUtil"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"

local LootingConfig = require "DFM.Business.Module.LootingModule.LootingConfig"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponHelperTool = require"DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ItemBaseTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local LootingOperaLogic = require "DFM.Business.Module.LootingModule.LootingOperaLogic"
local LootingVehicleLogic = require "DFM.Business.Module.LootingModule.LootingVehicleLogic"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"

local SSRQuality = 0
local onScrollEndHandle = nil

local LootingLogic = {}

---@return LootingField
local function getField()
    return Module.Looting.Field
end



local function log(...)
    loginfo("[LootingLogic]", ...)
end

-----------------------------------------------------------------------
--region Important logic

---@param task PreloadTask
function LootingLogic.PreCreateUIs(task)
    -- 避免入局狂点背包，这个时候这里还没走到，会导致后续ui打不开
    getField():SetCurrentLootUIState(LootingConfig.ELootUIState.None)

    if IsHD() then
        return LootingLogic.PreCreateUIs_HD(task)
    end

    log("PreCreateUIs")

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        getField().bIsCreatingUIForCollectionRoom = true
    end

    -- 背包界面预热
    ---@param ui MobileBagPanel
    local function fOnPanelMainLoaded(ui)
        getField():SetMainPanel(ui)

        ui:OnSwitchTab(LootingConfig.ESOLSelectPanel.Bag)
        ui:OnSwitchTab(LootingConfig.ESOLSelectPanel.Health)
        Facade.UIManager:ForceCheckPendingUIParents()
        ui:Hide(true, true)
        getField().bIsCreatingUIForCollectionRoom = false
        if task then
            Module.Preload:NotifyAsyncTaskDone(task)
        end
        Timer.DelayCall(0.01, function()
            getField().bHasPreCreateUI = true
        end)
    end

    local mainPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.MobileBagPanel, fOnPanelMainLoaded,nil)
    getField():SetMainPanelHandle(mainPanelHandle)
end

---@param task PreloadTask
function LootingLogic.PreCreateUIs_HD(task)
    log("PreCreateUIs_HD")

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        getField().bIsCreatingUIForCollectionRoom = true
    end

    local function fOnMainPanelHDLoaded(ui)
        getField():SetMainPanel(ui)

        Facade.UIManager:ForceCheckPendingUIParents()
        ui:Hide(true, true)
        getField().bIsCreatingUIForCollectionRoom = false
        if task then
            Module.Preload:NotifyAsyncTaskDone(task)
        end
        Timer.DelayCall(0.01, function()
            getField().bHasPreCreateUI = true
        end)
    end
    local mainPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.LootingMainView_HD, fOnMainPanelHDLoaded,nil)
    getField():SetMainPanelHandle(mainPanelHandle)

    -- Also preinit character capture environment
    LootingLogic.PreloadCharacterCaptureEnv()
end

function LootingLogic.PreloadCharacterCaptureEnv()
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        return
    end

    log("PreloadCharacterCaptureEnv")

    local captureStudioManager = UCaptureStudioManager.Get(GetWorld())
    captureStudioManager:LoadCaptureEnv(Module.CommonWidget.Config.DEFAULT_CAPTURE_ENV_PATH_GAME)
end

-- Mobile特化的ui和逻辑
function LootingLogic.InternalOpenMainPanelMobile(tab, pickupType, interactors)

    ULuaExtension.ReportLuaUIShow("LootingMainPanel")

    -- 打开UI生命周期分帧
    Facade.UIManager:EnableLifeFunctionFraming(true)
    Facade.LuaFramingManager:UpdateFrameBudgetByCfg(EFrameBudgetConfig.LootPanel, true)

    local mainPanel = getField():GetMainPanel()
    if not isvalid(mainPanel) then
        local mainPanelHandle = getField():GetMainPanelHandle()
        if not mainPanelHandle then
            local function fOnMainPanelLoaded(mainPanel)
                getField():SetMainPanel(mainPanel)

                mainPanel:InitView(tab)
                if tab == mainPanel.tabType and tab == LootingConfig.ESOLSelectPanel.Bag then
                    -- 如果当前要打开Looting界面，并且背包的tab已经是Looting界面，那么需要手动播放Looting界面的AnimIn
                    mainPanel:PlayLootingAnimIn()
                end
            end

            mainPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.MobileBagPanel, fOnMainPanelLoaded,nil)
            getField():SetMainPanelHandle(mainPanelHandle)

            -- 关闭UI生命周期分帧
            Facade.UIManager:EnableLifeFunctionFraming(false)
        end
    else
        mainPanel:Show()
        mainPanel:InitView(tab)
        if tab == mainPanel.tabType and tab == LootingConfig.ESOLSelectPanel.Bag then
            -- 如果当前要打开Looting界面，并且背包的tab已经是Looting界面，那么需要手动播放Looting界面的AnimIn
            mainPanel:PlayLootingAnimIn()
        end

        -- 关闭UI生命周期分帧
        Facade.UIManager:EnableLifeFunctionFraming(false)
    end
end

-- PC特化的ui和逻辑
function LootingLogic.InternalOpenMainPanelHD(tab, pickupType, interactors)
    -- Facade.UIManager:EnableLifeFunctionFraming(true)

    ---@type LootingMainView_HD
    local mainPanel = getField():GetMainPanel()

    local hud = ABaseHUD.GetHUD(GetWorld())
    if hud then
        local panel = hud:GetPanel("LocalPlayerInfo")
        if panel and panel[1] and panel[1].SetRenderOpacity then
            panel[1]:SetRenderOpacity(LootingConfig.PLAYER_INFO_IN_BAG_OPACITY)
        end
    end

    if not isvalid(mainPanel) then
        local mainPanelHandle = getField():GetMainPanelHandle()
        if not mainPanelHandle then
            ---@param mainPanel LootingMainView_HD
            local function fOnMainPanelHDLoaded(mainPanel)
                getField():SetMainPanel(mainPanel)

                mainPanel:InitPanel(pickupType, interactors)
                mainPanel:SetTab(tab - 1)
            end

            mainPanelHandle = Facade.UIManager:AsyncShowUI(UIName2ID.LootingMainView_HD, fOnMainPanelHDLoaded,nil)
            getField():SetMainPanelHandle(mainPanelHandle)
        end
    else
        mainPanel:InitPanel(pickupType, interactors)
        mainPanel:SetTab(tab - 1)
        mainPanel:Show()
    end
end

local function fCloseMainPanel()
    getField().hasShowForbiddenToCarryOut = {}
    local mainPanel = getField():GetMainPanel()
    if mainPanel then
        mainPanel:Hide()
    else
        local mainPanelHandle = getField():GetMainPanelHandle()
        if mainPanelHandle then
            mainPanelHandle:CancelLoadProcess()
            getField():SetMainPanelHandle(nil)
        end
    end
end

-- Mobile特化的关闭
function LootingLogic.InternalCloseMainPanelMobile()
    ULuaExtension.ReportLuaUIClose("LootingMainPanel")

    Facade.LuaFramingManager:FlushAll()
    Facade.LuaFramingManager:ResetFrameBudget()

    Module.CommonWidget:CancelSimulateDrag()

    fCloseMainPanel()
end

-- PC特化的关闭
function LootingLogic.InternalCloseMainPanelHD()
    -- Facade.UIManager:EnableLifeFunctionFraming(false)

    fCloseMainPanel()
end

function LootingLogic.OpenMainPanel(tab)
    if not Facade.GameFlowManager:GetIsInGameControlInited() then
        return
    end

    local lootState = getField():GetCurrentLootUIState()
    if lootState ~= LootingConfig.ELootUIState.None then
        return
    end

    local character = InGameController:Get():GetGPCharacter()
    if character and (character:IsDead() or character:IsImpendingDeath() or character:IsRaiseWatch()) then
        return
    end

    local mainPanel = getField():GetMainPanel()
    if not isvalid(mainPanel) then
        local mainPanelHandle = getField():GetMainPanelHandle()
        if mainPanelHandle then
            --- 如果有handle但是没有ui本身，说明正在预加载过程中，禁止打开背包界面
            logerror("LootingLogic.OpenMainPanel blocked because mainPanel is loading")
            return
        end
    end

    if UGPBattleFieldSystem.Get(GetWorld()).bIsLiveSpectating and not Facade.GameFlowManager:IsInOBMode() then
        log("LootingLogic.OpenMainPanel blocked because bIsLiveSpectating = true")
        return
    end

    if Facade.GameFlowManager:IsInOBMode() then
        LootingLogic.OBOpenLootPanel(true)
        return
    end

    getField():SetCurrentLootUIState(LootingConfig.ELootUIState.LootBag)

    if IsHD() then
        SSRQuality = UKismetSystemLibrary.GetConsoleVariableIntValue("r.SSR.Quality")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.SSR.Quality 0")
    end

    getField().openBagTime = UGameplayBlueprintHelper.GetServerTimeSeconds(GetGameInstance())
    -- 保底，在玩家打开Looting界面之前如果还没有FetchPlayerItems无论如何也要Fetch了
    if not Server.LootingServer:HasFetchPlayerItems() then
        Server.LootingServer:FetchPlayerItems(false)
    end

    if IsHD() then
        LootingLogic.InternalOpenMainPanelHD(tab)
    else
        LootingLogic.InternalOpenMainPanelMobile(tab)
    end
end

function LootingLogic.OpenBagView()
    LootingLogic.OpenMainPanel(LootingConfig.ESOLSelectPanel.Bag)
end

function LootingLogic.OpenHealthView()
    LootingLogic.OpenMainPanel(LootingConfig.ESOLSelectPanel.Health)
end

function LootingLogic.OpenScoreBoardView()
    LootingLogic.OpenMainPanel(LootingConfig.ESOLSelectPanel.Score)
end

function LootingLogic.InternalCloseMainPanel()
    getField():SetCurrentLootUIState(LootingConfig.ELootUIState.None)

    local hud = ABaseHUD.GetHUD(GetWorld())
    if hud then
        local panel = hud:GetPanel("LocalPlayerInfo")
        if panel and panel[1] and panel[1].SetRenderOpacity then
            panel[1]:SetRenderOpacity(1)
        end
    end

    getField().bHasScrollToSearchedItem = false

    if IsHD() then
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.SSR.Quality " .. SSRQuality)
    end

    if IsHD() then
        LootingLogic.InternalCloseMainPanelHD()
    else
        LootingLogic.InternalCloseMainPanelMobile()
    end
end

function LootingLogic.CloseBagView()
    Module.Looting:StopAllLoots()
end

function LootingLogic.OBOpenLootPanel(bIsActive)
    bIsActive = setdefault(bIsActive, false)
    log("LootingLogic.OBOpenLootPanel")
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow ~= EGameFlowStageType.Game then
        log(string.format("LootingLogic.OBOpenLootPanel blocked because curGameFlow == %d", curGameFlow))
        return
    end
    if getField():GetCurrentLootUIState() == LootingConfig.ELootUIState.LootBag then
        return
    end
    if UGPBattleFieldSystem.Get(GetWorld()).bIsOBFreeCamera then
        log("LootingLogic.OBOpenLootPanel blocked because is in OBFreeCamera mode")
        return
    end
    local playerState = UGPBattleFieldSystem.Get(GetWorld()):GetCurrentLocalPlayerOrOBPlayerState()
    if isvalid(playerState) and playerState.PlayerMatchInfo.bMatchOver then
        log("LootingLogic.OBOpenLootPanel blocked because player is match over")
        return
    end
    getField():SetCurrentLootUIState(LootingConfig.ELootUIState.LootBag)
    getField().bOBIsActivelyOpenBag = bIsActive
    Server.LootingServer:IncrementalUpdateOBPlayerData()
    Server.LootingServer:FullUpdateOBLootingData()
    local pickupType = Server.LootingServer:GetCurrentOBPickupType()
    local interactors = {Server.LootingServer:GetCurrentOBLootObj()}
    if IsHD() then
        LootingLogic.InternalOpenMainPanelHD(LootingConfig.ESOLSelectPanel.Bag, pickupType, interactors)
    else
        LootingLogic.InternalOpenMainPanelMobile(LootingConfig.ESOLSelectPanel.Bag, pickupType, interactors)
    end
end

function LootingLogic.GPMReportOpenLoot(bOpen)
    if bOpen then
        log("GPMReportOpenLoot true")

        UDFMGameGPM.BeginExtTag("EXCLUDE_OpenBag")
        UDFMGameGPM.BeginExclude()
    else
        log("GPMReportOpenLoot false")

        UDFMGameGPM.EndExtTag("EXCLUDE_OpenBag")
        UDFMGameGPM.EndExclude()
    end
end

local toggleGameModes = {
    [EDFMGamePlayMode.GamePlayMode_SOL] = true,
    [EDFMGamePlayMode.GamePlayMode_Raid] = true,
    [EDFMGamePlayMode.GamePlayMode_Intro] = true,
}
function LootingLogic._OnToggleLootPanelAction(tab)
    log("_OnToggleLootPanelAction")

    local gamePlayerMode = InGameController:Get():GetGamePlayerMode()
    if not toggleGameModes[gamePlayerMode] then return end

    local currentUIState = getField():GetCurrentLootUIState()
    log("_OnToggleLootPanelAction", enum2string(LootingConfig.ELootUIState, currentUIState))

    if UGPBattleFieldSystem.Get(GetWorld()).bIsLiveSpectating and not Facade.GameFlowManager:IsInOBMode() then
        log("_OnToggleLootPanelAction blocked because bIsLiveSpectating = true")
        return
    end

    if currentUIState == LootingConfig.ELootUIState.None then
        LootingLogic.OpenMainPanel(tab)
    elseif currentUIState == LootingConfig.ELootUIState.LootSearch then
        -- Do nothing
    else
        Module.Looting:StopAllLoots()
    end
end

function LootingLogic._OnOpenHealthPanel()
    log("_OnOpenHealthPanel")

    local gamePlayerMode = InGameController:Get():GetGamePlayerMode()
    if not toggleGameModes[gamePlayerMode] then return end

    local currentUIState = getField():GetCurrentLootUIState()
    log("_OnOpenHealthPanel", enum2string(LootingConfig.ELootUIState, currentUIState))

    if currentUIState == LootingConfig.ELootUIState.None then
        LootingLogic.OpenMainPanel(LootingConfig.ESOLSelectPanel.Health)
    end
end

function LootingLogic.OpenPickupPanel(pickupType, interactors)
    if not Facade.GameFlowManager:GetIsInGameControlInited() then
        return
    end

    local lootState = getField():GetCurrentLootUIState()
    if lootState ~= LootingConfig.ELootUIState.None
    and lootState ~= LootingConfig.ELootUIState.LootSearch then
        return
    end

    local character = InGameController:Get():GetGPCharacter()
    if character and (character:IsDead() or character:IsImpendingDeath()) then
        return
    end

    getField():SetCurrentLootUIState(LootingConfig.ELootUIState.LootPanel)
    getField():SetCurSearchingBox(nil)

    if pickupType == EGamePickupType.SceneBox then
        local box = interactors[1]
        local fOnLocalOpenBox = box.OnLocalOpenBox
        if fOnLocalOpenBox then
            fOnLocalOpenBox(box)
        end
    end

    if IsHD() then
        SSRQuality = UKismetSystemLibrary.GetConsoleVariableIntValue("r.SSR.Quality")
        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.SSR.Quality 0")
    end

    getField().openBagTime = UGameplayBlueprintHelper.GetServerTimeSeconds(GetGameInstance())
    -- 保底，在玩家打开Looting界面之前如果还没有FetchPlayerItems无论如何也要Fetch了
    if not Server.LootingServer:HasFetchPlayerItems() then
        Server.LootingServer:FetchPlayerItems(false)
    end
    local slotGroup
    if pickupType == EGamePickupType.NearbyDeadBody then
        slotGroup = ESlotGroup.DeadBody
    elseif pickupType == EGamePickupType.DropContainer or pickupType == EGamePickupType.SceneBox then
        slotGroup = ESlotGroup.Nearby
    end
    if IsHD() then
        LootingLogic.InternalOpenMainPanelHD(LootingConfig.ESOLSelectPanel.Bag, pickupType, interactors)
    else
        LootingLogic.InternalOpenMainPanelMobile(LootingConfig.ESOLSelectPanel.Bag, pickupType, interactors)
    end
end

function LootingLogic.OpenDetailPanel(refWidget)
    -- -- 如果UI正在创建，先直接返回
    -- local detailHandle = getField():GetItemDetailPanelHandle()
    -- if detailHandle and detailHandle:GetHandleState() ~= EHandleState.CreateFinish then
    --     return
    -- end

    -- local detailIns = getField():GetItemDetailPanel()
    -- if detailIns then
    --     detailIns:Update
    --     return
    -- end

    -- Tmp
    local bWarehouse = ItemOperaTool.CheckRunWarehouseLogic()
    if bWarehouse then
        return
    end

    local item = refWidget.item
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local btnTypeList
    if Module.ItemDetail:CanOpenFoldDetail() then
        btnTypeList = LootingLogic.CreateFoldDetailBtns(item)
    else
        btnTypeList = LootingLogic.CreateCommonDetailBtns(item)
    end
    local transBtnList = LootingLogic.CreateTransItemBtnList(item)

    local bShowBtn = true
    ---- 钥匙包不显示按钮
    if featureType == EFeatureType.Equipment and item:IsEquipped()
    and itemFeature:IsKeyChain() then
        bShowBtn = false
    end
    bShowBtn = bShowBtn and #btnTypeList > 0

    bShowBtn = bShowBtn and (not LootingLogic.IsInteractingShopStation())   -- 购物站交互时，详情页不显示操作按钮

    ---@param detailIns ItemDetailPanel
    local function fCustomCloseCallback(detailIns)
        local refItemWidget = detailIns:GetParentWidget()
        if refItemWidget then
            Module.Looting:DoSelectItem(refItemWidget, false)
        end
    end

    local bAdjustAlignY = true
    local bTransBtnForceToRight = false
    local itemviewWidget = refWidget

    -- 枪的详情页，特殊停靠
    -- if DFHD_LUA == 1 then
        if featureType == EFeatureType.Weapon then
            local mainPanel = getField():GetMainPanel()
            if mainPanel then
                local targetWidget = mainPanel.GetBagPanel and mainPanel:GetBagPanel()
                if targetWidget then
                    refWidget = targetWidget
                    bAdjustAlignY = false
                    bTransBtnForceToRight = true
                end
            end
        end
    -- end

    ---@param detailIns ItemDetailPanel
    local function fOnLoadCallback(detailIns)
        -- 共有接口
        if bAdjustAlignY or IsHD() then
            detailIns:IgnoreWidgetOnBtnUp(itemviewWidget)
        end
        detailIns:SetCustomClosePanelLogic(fCustomCloseCallback)
        -- detailIns:SetCustomVisibilityControllLogic(true)
        -- 展开态详情页特有接口
        if not Module.ItemDetail:CanOpenFoldDetail() then
            detailIns:SetHideInDraging(true)
            detailIns:SetAdapterDragState(true)
            detailIns:SetAdapterFastUnequipState(true)
            detailIns:SetAdapterTipsShowButtonState(true)
            detailIns:UpdateDetailViewPosition(refWidget, 20, bAdjustAlignY,Module.ItemDetail.Config.ENewTipsAlignDirect.Right, bTransBtnForceToRight)
        end
    end

    Module.ItemDetail:OpenItemDetailPanel(item, refWidget, true, bShowBtn, btnTypeList, nil, fOnLoadCallback, nil, nil, nil, transBtnList, nil, true)

    -- ---@param detailIns ItemDetailPanel
    -- local function fOnLoadCallback(detailIns)
    --     detailIns:SetCustomVisibilityControllLogic(true)
    -- end
    -- local uihandle = Module.ItemDetail:OpenItemDetailPanel(item, refWidget, true, true, btnTypeList, nil, fOnLoadCallback)
    -- getField():SetItemDetailPanelHandle(uihandle)
end

function LootingLogic.CloseDetailPanel(bIncludeSub)
    if bIncludeSub then
        Module.ItemDetail:CloseAllItemDetailSubDetailPanelHandle()
    end
    Module.ItemDetail:CloseItemDetailPanel(nil, nil, false)
end

---@param item ItemBase
function LootingLogic.CreateCommonDetailBtns(item)
    local inSlot = item.InSlot
    local slotType = inSlot and inSlot.SlotType
    
    if slotType == ESlotType.SafeBox then
        local safeBoxSkinID = LootingLogic.GetSafeBoxSkinID()
        if safeBoxSkinID ~= 0 then
            local itemConf = ItemConfigTool.GetItemConfigById(safeBoxSkinID)
            if itemConf and itemConf.bHighValueItemNeedInspection then
                return {Module.ItemDetail.Config.ButtonType.InspectSafeBox}
            end
        end
    end

    -- 部分槽位局内不可操作，不显示任何详情页按钮
    if LootingConfig.SlotsNotAllowOperate[slotType] then
        return {}
    end

    local btnTypeList = {}
    local function AddBtnTypeList(btnType, isRec)
        -- 避免污染源配置
        isRec = setdefault(isRec, false)
        local copyBtnType = simpleclone(btnType)
        copyBtnType.isRec = isRec
        table.insert(btnTypeList, copyBtnType)
    end

    -- if item.InSlot and item.InSlot.SlotType ~= ESlotType.NearbyPickups then
    --     table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Drop)
    -- end

    -- 手游的丢弃 移到精准操作列表里 废弃，依然挪回详情页
    -- if IsHD() then
        if item.InSlot and item.InSlot.SlotType ~= ESlotType.NearbyPickups then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Drop)
        end
    -- end


    if item.num > 1 then
        --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
        AddBtnTypeList(LootingLogic.GetButtonType().Split)
        --- END MODIFICATION
    end

    local isArchiveItem = false
    local isNarrativeProps = false
    local isKey = false
    local isMedicine = false
    local isWeapon = false
    local isPerk = false
    local isPerkOnWeapon = false
    local isGadgetItem = false
    local isTreasureMap = false
    local isExtendItem = false
    local isBullet = false
    local isSafeBox = false
    local isAdapter = false
    local isKeyChain
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    if featureType == EFeatureType.Default then
        isArchiveItem = itemFeature:IsArchiveItem()
        isNarrativeProps = itemFeature:IsNarrativeProps()
        isTreasureMap = itemFeature:IsTreasureMap()
    elseif featureType == EFeatureType.Key then
        isKey = true
    elseif featureType == EFeatureType.Health then
        isMedicine = itemFeature:IsMedicine()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.WeaponPerk then
        isPerk = true
        isPerkOnWeapon = itemFeature:IsPerkOnWeapon()
    elseif featureType == EFeatureType.GadgetItem then
        isGadgetItem = true
    elseif featureType == EFeatureType.Equipment then
        if itemFeature:IsExtendItem() then
            isExtendItem = true
        end
        isSafeBox = itemFeature:IsSafeBox()
        isKeyChain = itemFeature:IsKeyChain()
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    end

    if inSlot then
        local slotGroup = inSlot:GetSlotGroup()
        if slotGroup == Server.LootingServer:GetSelfSlotGroup() then
            if item:IsEquipped() then
                if isWeapon then
                elseif isBullet then
                elseif item:IsUsableItem() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use)
                end
                
                if inSlot.SlotType ~= ESlotType.MeleeWeapon and inSlot:IsEquipSlot() then
                    --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
					table.insert(btnTypeList, LootingLogic.GetButtonType().UnEquip)
					--- END MODIFICATION

                end
            else
                -- if isNarrativeProps or isTreasureMap then
                --     table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                -- end
                if isExtendItem then
                elseif isBullet then
                elseif isKeyChain then
                elseif isAdapter then
                    if Module.Inventory:CheckIsEquippedWeapon(item) then
                        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.EquipAdapter) -- 安装
                    else
                        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.EquipAdapterGrey) -- 安装
                    end
                elseif item:IsUsableItem() then
                    if isKey then -- 除了钥匙以外显示装备和使用
                    elseif isGadgetItem then
                        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
                    elseif isMedicine then
                        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
                        --table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Equip) -- 装备
                    else
                        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
                    end
                elseif item:IsEquipableItem() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Equip) -- 装备
                elseif isArchiveItem then
                    -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                end
            end
        else
            if isExtendItem then
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            elseif isBullet then
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            elseif isArchiveItem then
                -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            elseif item:IsUsableItem() then
                table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use)
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            elseif isAdapter then
                if Module.Inventory:CheckIsEquippedWeapon(item) then
                    --AddBtnTypeList(Module.ItemDetail.Config.ButtonType.EquipAdapter, true) -- 安装
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.EquipAdapter) -- 安装
                else
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.EquipAdapterGrey) -- 安装
                end
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            elseif item:IsEquipableItem() then
                local function RecIsPickUpOrEquip(isEquip)
                    if isEquip then
                        if IsHD() then
                            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                        end
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip, true)
                    else
                        if IsHD() then
                            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                        end
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip)
                    end
                end

                if item:GetFeatureType() == EFeatureType.Weapon then
                    local targetEquipSlot = Server.InventoryServer:GetEquipmentEmptySlot(item)
                    if targetEquipSlot then
                        RecIsPickUpOrEquip(true)
                    else
                        RecIsPickUpOrEquip(false)
                    end
                elseif item:GetFeatureType() == EFeatureType.Equipment then
                    local equipmentFeature = item:GetFeature()
                    local targetEquipSlot = Server.InventoryServer:GetEquipmentDefaultSlot(item)
                    local targetEquipItem = targetEquipSlot:GetEquipItem()
                    ---@type EquipmentFeature
                    local targetEquipFeature = targetEquipItem and targetEquipItem:GetFeature(EFeatureType.Equipment) or nil
                    
                    if not targetEquipItem or not targetEquipFeature then
                        RecIsPickUpOrEquip(true)
                    elseif equipmentFeature:IsContainerItem() then
                        -- 背包考虑容量
                        RecIsPickUpOrEquip(equipmentFeature:GetContainerSize() > targetEquipFeature:GetContainerSize())
                    elseif equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate() then
                        -- 头盔和护甲考虑当前耐久度和防护等级
                        local bEquip = ItemOperaTool.CheckTriggerReplaceArmor(item, targetEquipSlot)
                        RecIsPickUpOrEquip(bEquip)
                    end
                end

            elseif isNarrativeProps or isTreasureMap then
                -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            else
                if IsHD() then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
                end
            end
            --table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.PickUp) -- 拾取
        end
    else
        if isPerk then
            if isPerkOnWeapon then
                local function fCustomUnEquip(item)
                    ItemOperaTool.UnEquipPerkFromPerkItem(item, LootingLogic.GetSuitableSlot(item))
                    Module.ItemDetail:CloseItemDetailPanel()
                end
            
                local unEquipBtnType = {
                    fOnClick = fCustomUnEquip,
                    txt = Module.Inventory.Config.Loc.SelectionUnEquipPerkBtnText
                }

                table.insert(btnTypeList, unEquipBtnType) -- Perk的卸下
            end
        end
    end

    if LootingLogic.HasInspectButton(item) then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Inspect)
    end

    if LootingLogic.HasSpecialInspectButton(item) then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.SpecialInspect)
    end

    return btnTypeList
end

--- 手游局内(looting)折叠态详情页特有，每种道具显示2个按钮常用按钮
--- 具体参见https://www.figma.com/design/we3CRdYqaOiLk7lcocTD0K/%E4%BB%93%E5%BA%93?node-id=8237-165290&t=l5g4iFCyRq4OTW2I-0
--- 附录1：折叠版详情页按钮汇总
---@param item ItemBase
function LootingLogic.CreateFoldDetailBtns(item)
    local inSlot = item.InSlot
    local slotType = inSlot and inSlot.SlotType

    if slotType == ESlotType.SafeBox then
        local safeBoxSkinID = LootingLogic.GetSafeBoxSkinID()
        if safeBoxSkinID ~= 0 then
            local itemConf = ItemConfigTool.GetItemConfigById(safeBoxSkinID)
            if itemConf and itemConf.bHighValueItemNeedInspection then
                return {Module.ItemDetail.Config.ButtonType.InspectSafeBox}
            end
        end
    end

    -- 部分槽位局内不可操作，不显示任何详情页按钮
    if LootingConfig.SlotsNotAllowOperate[slotType] then
        return {}
    end

    local btnTypeList = {}
    local function AddBtnTypeList(btnType, isRec)
        -- 避免污染源配置
        isRec = setdefault(isRec, false)
        local copyBtnType = simpleclone(btnType)
        copyBtnType.isRec = isRec
        table.insert(btnTypeList, copyBtnType)
    end

    local isArchiveItem = false
    local isNarrativeProps = false
    local isKey = false
    local isMedicine = false
    local isWeapon = false
    local isPerk = false
    local isPerkOnWeapon = false
    local isGadgetItem = false
    local isTreasureMap = false
    local isExtendItem = false
    local isBullet = false
    local isSafeBox = false
    local isAdapter = false
    local isKeyChain = false
    local isHealth = false
    local isCollect = false
    local isPlayerArchiveItem = false
    local isArmor = false
    local isHelmet = false
    local IsBag = false
    local IsChestHanging = false
    local IsUndecipheredBrick = false
    local IsSpecialBrick = false
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    if featureType == EFeatureType.Default then
        isArchiveItem = itemFeature:IsArchiveItem()
        isNarrativeProps = itemFeature:IsNarrativeProps()
        isTreasureMap = itemFeature:IsTreasureMap()
        isPlayerArchiveItem = itemFeature:IsPlayerArchiveItem()
        isCollect = itemFeature:IsCollectableItem()
    elseif featureType == EFeatureType.Reward then
        IsUndecipheredBrick = itemFeature:IsUndecipheredBrick()
        IsSpecialBrick = itemFeature:IsSpecialBrick()
    elseif featureType == EFeatureType.Key then
        isKey = true
    elseif featureType == EFeatureType.Health then
        isMedicine = itemFeature:IsMedicine()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.WeaponPerk then
        isPerk = true
        isPerkOnWeapon = itemFeature:IsPerkOnWeapon()
    elseif featureType == EFeatureType.GadgetItem then
        isGadgetItem = true
    elseif featureType == EFeatureType.Equipment then
        if itemFeature:IsExtendItem() then
            isExtendItem = true
        end
        isSafeBox = itemFeature:IsSafeBox()
        isKeyChain = itemFeature:IsKeyChain()
        isArmor = itemFeature:IsBreastPlate()
        isHelmet = itemFeature:IsHelmet()
        IsBag = itemFeature:IsBag()
        IsChestHanging = itemFeature:IsChestHanging()
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    elseif featureType == EFeatureType.Health then
        isHealth = true
    end

    local bItemIsOnPlayer = item and item.InSlot and item.InSlot:GetSlotGroup() == ESlotGroup.Player
    local bHasLootObj = Server.LootingServer:GetCurrentInteractorType() or false
    local moveOrPickup = bHasLootObj and (bItemIsOnPlayer and Module.ItemDetail.Config.ButtonType.Move or Module.ItemDetail.Config.ButtonType.PickUp) or nil

    if inSlot then
        local slotGroup = inSlot:GetSlotGroup()
        -- 可拾取和不可拾取
        if isAdapter then
            table.insert(btnTypeList, moveOrPickup) -- 移出
        elseif isBullet then
            table.insert(btnTypeList, moveOrPickup) -- 移出
        elseif isKey then
            table.insert(btnTypeList, moveOrPickup) -- 移出
        elseif isCollect then
            table.insert(btnTypeList, moveOrPickup) -- 移出
        elseif IsUndecipheredBrick then
            table.insert(btnTypeList, moveOrPickup) -- 移出
        elseif IsSpecialBrick then
            table.insert(btnTypeList, moveOrPickup) -- 移出
        end

        if slotGroup == ESlotGroup.Player then -- 在玩家身上
            if item:IsEquipped() then -- 已经装备
                if isWeapon then
                elseif isBullet then
                elseif item:IsUsableItem() then
                    -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use)
                end
                if inSlot.SlotType ~= ESlotType.MeleeWeapon and inSlot:IsEquipSlot() then
                    --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
					table.insert(btnTypeList, LootingLogic.GetButtonType().UnEquip)
					--- END MODIFICATION
                end
            else
                -- if isNarrativeProps or isTreasureMap then
                --     table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                -- end
                if isExtendItem then
                elseif isAdapter then
                elseif isBullet then
                elseif isKey then
                elseif isCollect then
                elseif IsUndecipheredBrick then
                elseif IsSpecialBrick then
                elseif isKeyChain then
                elseif item:IsUsableItem() then -- 可以被使用
                    if isKey then -- 除了钥匙以外显示装备和使用
                    elseif isGadgetItem then
                        -- TODO:这个类型没说写要有使用
                        -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
                    elseif isMedicine then
                        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
                        --table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Equip) -- 装备
                    else
                        -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
                    end
                elseif item:IsEquipableItem() then -- 可以装备
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Equip) -- 装备
                end
            end

            if isWeapon or isArmor or isHelmet or IsBag or IsChestHanging then
                -- TODO:枪新增了卸下子弹
                if #btnTypeList == 0 then -- 不能装备不能卸下
                    local targetEquipSlot = Server.InventoryServer:GetEquipmentEmptySlot(item)
                    if targetEquipSlot then
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip, true)
                    else
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Replace, true)
                    end
                end
                if isWeapon then
                    local currentBulletNum, _ = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(item:GetRawPropInfo())
                    if currentBulletNum > 0 then
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.UnEquipBullet)
                    else
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Drop)
                    end
                end
            end
        else
            if isExtendItem then
                -- TODO:这些限定PC才有的取消频闭
                -- if IsHD() then
                    table.insert(btnTypeList, moveOrPickup) -- 拾取
                -- end
            elseif isAdapter then
            elseif isBullet then
            elseif isKey then
            elseif isCollect then
            elseif IsUndecipheredBrick then
            elseif IsSpecialBrick then
            elseif isMedicine then
                    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use) -- 使用
            elseif isArchiveItem then
                -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                -- if IsHD() then
                    table.insert(btnTypeList, moveOrPickup) -- 拾取
                -- end
            elseif item:IsUsableItem() then
                -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Use)
                -- if IsHD() then
                    table.insert(btnTypeList, moveOrPickup) -- 拾取
                -- end
            elseif item:IsEquipableItem() then
                -- local function RecIsPickUpOrEquip(isEquip)
                --     if isEquip then
                --         table.insert(btnTypeList, moveOrPickup) -- 拾取
                --         AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip, true)
                --     else
                --         table.insert(btnTypeList, moveOrPickup) -- 拾取
                --         AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip)
                --     end
                -- end

                if item:GetFeatureType() == EFeatureType.Weapon then
                    local targetEquipSlot = Server.InventoryServer:GetEquipmentEmptySlot(item)
                    if targetEquipSlot then
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip, true)
                    else
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Replace, true)
                    end
                    local currentBulletNum, _ = WeaponAssemblyTool.GetWeaponBulletNumAndCapacity(item:GetRawPropInfo())
                    if currentBulletNum > 0 then
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.UnEquipBullet)
                    else
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Drop)
                    end
                elseif item:GetFeatureType() == EFeatureType.Equipment then
                    local equipmentFeature = item:GetFeature()
                    local targetEquipSlot = Server.InventoryServer:GetEquipmentDefaultSlot(item)
                    local targetEquipItem = targetEquipSlot:GetEquipItem()
                    ---@type EquipmentFeature
                    local targetEquipFeature = targetEquipItem and targetEquipItem:GetFeature(EFeatureType.Equipment) or nil
                    if not targetEquipItem or not targetEquipFeature then
                        AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Equip, true)
                    else equipmentFeature:IsContainerItem()
                        local replaceBtnType = clone(Module.ItemDetail.Config.ButtonType.Equip)
                        replaceBtnType.btnType = Module.ItemDetail.Config.ButtonType.Replace.btnType
                        replaceBtnType.txt = Module.ItemDetail.Config.ButtonType.Replace.txt
                        replaceBtnType.pcRightIcon = Module.ItemDetail.Config.pcRightIconList.Replace
                        AddBtnTypeList(replaceBtnType, true)
                    end
                end

            elseif isNarrativeProps or isTreasureMap then
                -- table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Check) -- 查看/检视
                -- if IsHD() then
                    table.insert(btnTypeList, moveOrPickup) -- 拾取
                -- end
            else
                -- if IsHD() then
                    table.insert(btnTypeList, moveOrPickup) -- 拾取
                -- end
            end
            --table.insert(btnTypeList, moveOrPickup) -- 拾取
        end
    else
        if isPerk then
            if isPerkOnWeapon then
                local function fCustomUnEquip(item)
                    ItemOperaTool.UnEquipPerkFromPerkItem(item, LootingLogic.GetSuitableSlot(item))
                    Module.ItemDetail:CloseItemDetailPanel()
                end
                local unEquipBtnType = {
                    fOnClick = fCustomUnEquip,
                    txt = Module.Inventory.Config.Loc.SelectionUnEquipPerkBtnText
                }

                table.insert(btnTypeList, unEquipBtnType) -- Perk的卸下
            end
        end
    end

    -- 除了枪，其他所有道具第二个按钮都是丢弃
    if item.InSlot and item.InSlot:GetSlotGroup() ~= ESlotGroup.Nearby then
        if not isWeapon then
            AddBtnTypeList(Module.ItemDetail.Config.ButtonType.Drop)
        end
    end

    return btnTypeList
end

---@param item ItemBase
function LootingLogic.CreateTransItemBtnList(item)
    if not item.InSlot then
        return
    end
    if IsHD() then return {} end

    if item.InSlot.SlotType == ESlotType.SafeBox or item.InSlot.SlotType == ESlotType.KeyChain then
        return {}
    end

    local btnTypeList = {}

    -- 根据道具类型设定仓库按钮
    local isAdapter = false
    local isBlindBox = false
    local isWeapon = false
    local isPoorWeapon = false
    local isRandomGift = false
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local isKeyChain = false
    local isBullet = false
    local isKey = false
    local isHealth = false
    local isCollectableItem = false
    local isChest = false
    local isHelmet = false
    local isBreastPlate = false
    local isBag = false
    if featureType == EFeatureType.Equipment then
        isKeyChain = itemFeature:IsKeyChain()
        isChest = itemFeature:IsChestHanging()
        isHelmet = itemFeature:IsHelmet()
        isBreastPlate = itemFeature:IsBreastPlate()
        isBag = itemFeature:IsBag()
    elseif featureType == EFeatureType.Reward then
        isBlindBox = itemFeature:IsBlindBox()
        isRandomGift = itemFeature:IsRandomGift()
    elseif featureType == EFeatureType.Default then
        isCollectableItem = itemFeature:IsCollectableItem()
    elseif featureType == EFeatureType.Weapon then
        isWeapon = itemFeature:IsWeapon()
        isPoorWeapon = itemFeature:IsPoorWeapon()
    elseif featureType == EFeatureType.Adapter then
        isAdapter = itemFeature:IsAdapter()
    elseif featureType == EFeatureType.Bullet then
        isBullet = true
    elseif featureType == EFeatureType.Health then
        isHealth = true
    elseif featureType == EFeatureType.Key then
        isKey = true
    end

    local function AddBtnTypeList(btnType)
        if ItemOperaTool.bIsInCollectionRoom then
            if btnType == Module.ItemDetail.Config.TransBtnType.ToCardBag then
                return
            end
            if btnType == Module.ItemDetail.Config.TransBtnType.ToSafebox then
                return
            end
        end
        local data = {}
        data.item = item
        data.transBtnType = btnType
        table.insert(btnTypeList, data)
    end

    local bPlayerSelf = item.InSlot:GetSlotGroup() == ItemOperaTool.GetSelfSlotGroup()
    local bInChest = item.InSlot.SlotType == ESlotType.ChestHangingContainer and bPlayerSelf
    local bInPocket = item.InSlot.SlotType == ESlotType.Pocket and bPlayerSelf
    local bInBag = item.InSlot.SlotType == ESlotType.BagContainer and bPlayerSelf
    local bInSafeBox = item.InSlot.SlotType == ESlotType.SafeBoxContainer and bPlayerSelf
    local bHasBag = Server.InventoryServer:GetBagItem()
    local bCanDrop = true
    -- local bHasChestHanging = Server.InventoryServer:GetChestHangingItem()
    -- 枪械
    if isWeapon or isPoorWeapon then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
    elseif isKey then
        if item.InSlot.SlotType == ESlotType.KeyChainContainer then
            bCanDrop = false
        else
            if not bInChest and not bInPocket then
                AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
            end
            if not bInBag and bHasBag then
                AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
            end
            if not bInSafeBox then
                AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
            end
        end
    elseif isAdapter then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
    elseif isBullet or isHealth or isCollectableItem then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        if not bInSafeBox then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        end
    elseif isChest then
        if not (item.InSlot.SlotType == ESlotType.ChestHanging and bPlayerSelf) and not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        -- if not bInSafeBox then
        --     AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        -- end
    elseif isBag then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not (item.InSlot.SlotType == ESlotType.Bag and bPlayerSelf) and not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        -- if not bInSafeBox then
        --     AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        -- end
    elseif isHelmet or isBreastPlate then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        -- if not bInSafeBox then
        --     AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        -- end
    elseif isBlindBox or isRandomGift then
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
    else
        if not bInChest and not bInPocket then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToChestOrPocket)
        end
        if not bInBag and bHasBag then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToBag)
        end
        if not bInSafeBox then
            AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToSafebox)
        end
    end

    if bCanDrop and item.InSlot and item.InSlot:GetSlotGroup() ~= ESlotGroup.Nearby then
        AddBtnTypeList(Module.ItemDetail.Config.TransBtnType.ToRemove)
    end

    return btnTypeList
end

function LootingLogic.OpenSimpleBtnListPanel(refWidget, alignPosition)
    ---@type ItemBase
    local item = refWidget.item
    local btnTypeList = LootingLogic.CreateCommonDetailBtns(item)
    
    local tempList = btnTypeList
    btnTypeList = {}
    for _, btnType in ipairs(tempList) do
        table.insert(btnTypeList, 1, btnType)
    end

    local function OpenSimpleBtnListPanel()
        local function fCustomCloseCallback()
            getField().simpleBtnListPanelItem = nil
            if refWidget then
                Module.Looting:DoSelectItem(refWidget, false)
                if Module.Looting.Field.onCloseSimpleBtnListPanelCallback then
                    Module.Looting.Field.onCloseSimpleBtnListPanelCallback()
                    Module.Looting.Field.onCloseSimpleBtnListPanelCallback = nil
                end
            end
        end

        getField().simpleBtnListPanelItem = item
        local customData = {
            ["itemWidget"] = refWidget
        }
        Module.ItemDetail:OpenSimpleBtnListPanel(item, btnTypeList, alignPosition, fCustomCloseCallback, nil, customData)
    end

    --- 在卡包中的钥匙只添加收藏按钮
    if item:GetFeature(EFeatureType.Key) and item.InSlot and item.InSlot:IsKeyContainerSlot() then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.LabelMark)
        OpenSimpleBtnListPanel()
        return
    end

    --- 数量大于1时添加快速拆分按钮
    if item.num > 1 then
        --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
        table.insert(btnTypeList, LootingLogic.GetButtonType().FastSplit)
        --- END MODIFICATION
    end

    --- 对于装备类物品，对应的装备槽已满时，装备的文本变为替换
    for index,btnType in ipairs(btnTypeList) do
        if btnType.btnType == Module.ItemDetail.Config.ButtonType.Equip.btnType then
            local replaceBtnType = clone(Module.ItemDetail.Config.ButtonType.Equip)
            replaceBtnType.btnType = Module.ItemDetail.Config.ButtonType.Replace.btnType
            replaceBtnType.txt = Module.ItemDetail.Config.ButtonType.Replace.txt
            replaceBtnType.pcRightIcon = Module.ItemDetail.Config.pcRightIconList.Replace
            if item:GetFeature(EFeatureType.Weapon) then
                local LeftSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.Player)
                local RightSlot = Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, ESlotGroup.Player)
                if (LeftSlot and LeftSlot:GetEquipItem())
                        and (RightSlot and RightSlot:GetEquipItem()) then
                    btnTypeList[index] = replaceBtnType
                end
            elseif item:IsEquipableItem() then
                local targetEquipSlot = Server.InventoryServer:GetEquipmentDefaultSlot(item, ESlotGroup.Player)
                if targetEquipSlot:GetEquipItem() then
                    btnTypeList[index] = replaceBtnType
                end
            end
        end
    end

    --- 当物品在左侧网格中时，添加移动按钮
    if Server.LootingServer:GetCurrentSelectorData() then
        if item and item.InSlot and item.InSlot:GetSlotGroup() == ESlotGroup.Player and not item.InSlot:IsEquipableSlot() then
            
            --- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
            table.insert(btnTypeList, LootingLogic.GetButtonType().Move)
            --- END MODIFICATION

        end
    end

    -- 容器里的武器加一个卸下子弹按钮
    local itemFeature = item:GetFeature()
    local featureType = itemFeature:GetFeatureType()
    local isWeapon = featureType == EFeatureType.Weapon and itemFeature:IsWeapon()
    if isWeapon then
        --if not item:IsEquipped() then
        local desc = itemFeature:GetRawDescObj()
        local curBulletId = desc:GetCurrentAmmoItemId()
        if curBulletId and curBulletId ~= 0 then
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.UnEquipBullet)
        end
        --end
    end

    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.OpenDetailPanel)
    if Module.ItemDetail:IsShowLabelMarkBtn(item) then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.LabelMark)
    end

    OpenSimpleBtnListPanel()
end

function LootingLogic.CloseSimpleBtnListPanel(item)
    if not item or getField().simpleBtnListPanelItem == item then
        Module.ItemDetail:CloseSimpleBtnListPanel()
    end
end

function LootingLogic.OpenItemSplitPanel(item, parentWidget)
    if item and item.num > 1 then
        Module.ItemDetail:OpenItemSplitPanel(item, parentWidget)
    end
end

function LootingLogic.GetAICharacterNameConfig(aiName)
    local aiCharacterNameTable = Facade.TableManager:GetTable("AICharacterName")
    return aiCharacterNameTable[aiName]
end

function LootingLogic.IsScav()
    local playerCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not playerCtrl then
        return false, false
    end

    local playerState = playerCtrl.PlayerState
    if not playerState then
        return false, false
    end

    local bIsScav = playerState:GetCharacterModeId() == 2
    if bIsScav then
        return bIsScav, playerState:GetCharacterIsWanted()
    else
        return false, false
    end
end

function LootingLogic.CheckHasEquipAny(checkSlotTypes, slotGroup)
    for _, slotType in ipairs(checkSlotTypes) do
        local slot = Server.InventoryServer:GetSlot(slotType, slotGroup)
        if slot:GetEquipItem() ~= nil then
            return true
        end
    end

    return false
end

function LootingLogic.GetDeathReasonTxt(killInfo)
    local killedType = killInfo.BeKilledType
    local keyword = LootingConfig.Loc.DeathKeyword_None
    if killedType == EDeadType.EKilledByWeapon then --被武器杀
        local weaponID = killInfo.KillerWeaponID
        local itemInfo = ItemConfigTool.GetItemConfigById(weaponID)
        if itemInfo then
            keyword = tostring(itemInfo.Name)
        else
            keyword = LootingConfig.Loc.DeathKeyword_Weapon
        end
        return string.format(LootingConfig.Loc.DeathDescriptionText_KillByPlayer, killInfo.KillerName, keyword)
    elseif killedType == EDeadType.EkilledByVehicle then --被车撞死
        keyword = LootingConfig.Loc.DeathKeyword_Vehicle
    elseif killedType == EDeadType.EkilledBySelf then --高空坠落
        keyword = LootingConfig.Loc.DeathKeyword_Self
    elseif killedType == EDeadType.EkilledByPoisonGas then --毒气
        keyword = LootingConfig.Loc.DeathKeyword_PosionGas
    end

    return string.format(LootingConfig.Loc.DeathDescriptionText, keyword)
end

---@param item ItemBase
---@param itemSlot ItemSlot
function LootingLogic.CommonSlotOnDrop(item, itemSlot)
    local featureType = item:GetFeatureType()
    if itemSlot:IsEquipContainerSlot() then
        -- 放入容器有特殊规则
        if item == itemSlot:GetEquipItem() then
            -- 自己放自己，直接返回
            return false
        elseif itemSlot:CheckItemFitSlot(item) then
            return ItemOperaTool.DoPlaceItem(item, itemSlot, true)
        else
            local refContSlot = Server.InventoryServer:GetRefContainerSlot(itemSlot)
            return ItemOperaTool.DoPlaceItem(item, refContSlot, true)
        end
    elseif featureType == EFeatureType.Adapter then
        local adapterFeature = item:GetFeature(EFeatureType.Adapter)
        --Reload 子弹
        if itemSlot:IsWeaponSlot() and (adapterFeature and adapterFeature:IsAmmo()) then
            local equipItem = itemSlot:GetEquipItem()
            if equipItem then
                Module.Looting:DoReloadWeapon(WeaponHelperTool.GetWeaponGid(equipItem), item.id, item.gid)
                return
            end
        elseif itemSlot:IsWeaponSlot() and (adapterFeature and adapterFeature:IsAdapter()) then
            local equipItem = itemSlot:GetEquipItem()
            if Module.FastEquip:CheckFastEquip(item,equipItem,true) then
                Module.FastEquip:PrepareFastEquipCmd(item,equipItem,itemSlot)
            end
            return
        end
    elseif featureType == EFeatureType.WeaponPerk then
        ---@type WeaponPerkFeature
        local perkFeature = item:GetFeature(EFeatureType.WeaponPerk)

        local srcPerkGid = item.gid
        local srcWeaponGid = 100001
        
        if perkFeature:IsPerkOnWeapon() then
            srcWeaponGid = perkFeature:GetBindWeaponGid()
        end

        local targetPerkGid = 0
        local targetWeaponGid = 0
        local targetSlot = nil
        local slotType = itemSlot.SlotType
        if slotType == ESlotType.MainWeaponLeft or slotType == ESlotType.MainWeaponRight then
            local equipWeapon = itemSlot:GetEquipItem()
            if equipWeapon then
                targetWeaponGid = equipWeapon.gid

                ---@type WeaponFeature
                local weaponFeature = equipWeapon:GetFeature(EFeatureType.Weapon)
                if weaponFeature and weaponFeature:HasPerk() then
                    local perkInfo = weaponFeature:GetPerkInfo()
                    targetPerkGid = perkInfo:GetGid()
                end
            end
        else
            targetSlot = itemSlot
        end
        
        ItemOperaTool.DoMovePerk(srcPerkGid, targetPerkGid, srcWeaponGid, targetWeaponGid, targetSlot)

        return
    end

    ItemOperaTool.DoPlaceItem(item, itemSlot, true)
end

---@param moveCmd ItemMoveCmd
function LootingLogic.GetLootResultTip(moveResult, moveCmd)
    local tip
    local item = moveCmd.item
    local targetLoc = moveCmd.targetLoc
    local originLoc = moveCmd.originLoc

    if moveResult == EItemMoveResult.EItemMoveResult_Success then
        if targetLoc and targetLoc.ItemSlot and targetLoc.ItemSlot.SlotType == ESlotType.NearbyPickups then
            if originLoc and originLoc.ItemSlot and originLoc.ItemSlot.SlotType ~= ESlotType.NearbyPickups then
                tip = string.format(LootingConfig.Loc.LootingDropItemSuccess, item.name)
            end
        end
    else
        tip = LootingConfig.MapItemMoveResult2Tip[moveResult]
    end
    
    return tip
end

function LootingLogic.GetInteratorComponent()
    local character = Facade.GameFlowManager:GetCharacter()
    if isvalid(character) then
        return character.InteractorComponent
    end
    return nil
end

function LootingLogic.GetItemSearchSpeedRate()
    local character = Facade.GameFlowManager:GetCharacter()
    if isvalid(character) then
        return character:GetItemSearchSpeedRate()
    end
    return 1
end

---@class LootSelectorData
---@field type EGamePickupType
---@field interactors userdata[]

---@return LootSelectorData[]
function LootingLogic.GetLootSelectorDatas()
    local lootSelectorDatas = {}
    local interatorComponent = LootingLogic.GetInteratorComponent()
    if interatorComponent then
        for index, interactorInfo in pairs(interatorComponent.CurrentPotentialInteractors) do
            local interactorType = interactorInfo.InteractorType
            local interactor = interactorInfo.InteractableProxy.Interactor
            if interactorType == LootingConfig.EInteractorType.PickupBox then --容器                
                if interactor ~= nil then
                    local lootSelectorData =
                    {
                        type = interactor:IsOpenBox() and EGamePickupType.SceneBox or EGamePickupType.DropContainer,
                        interactors = {interactor}
                    }

                    table.insert(lootSelectorDatas, lootSelectorData)
                end
            elseif interactorType == LootingConfig.EInteractorType.Deadbody then --尸体
                if interactor ~= nil then
                    local lootSelectorData =
                    {
                        type = EGamePickupType.NearbyDeadBody,
                        interactors = {interactor}
                    }

                    table.insert(lootSelectorDatas, lootSelectorData)
                end
            elseif interactorType == LootingConfig.EInteractorType.NearbyPickup then --散件
                local interactors = {}
                if interactorInfo.isBrunchofItem == true then
                    for _, interactorItem in pairs(interactorInfo.AllInteractors) do
                        table.insert(interactors, interactorItem)
                    end
                else
                    table.insert(interactors, interactor)
                end
                local lootSelectorData =
                {
                    type = EGamePickupType.NearbyPickups,
                    interactors = interactors
                }
                table.insert(lootSelectorDatas, lootSelectorData)
            elseif interactorType == LootingConfig.EInteractorType.ShopStation then -- 购物站（兼容）
                local lootSelectorData =
                {
                    type = EGamePickupType.ShopStation,
                    interactors = {interactor}
                }
                table.insert(lootSelectorDatas, lootSelectorData)
            end
        end
    end

    return lootSelectorDatas
end

---@return LootSelectorData
function LootingLogic.GetLootSelectorData(type, interactors)
    -- local allSelectorDatas = LootingLogic.GetLootSelectorDatas()
    -- for _, data in ipairs(allSelectorDatas) do
    --     if data.type == type then
    --         if type == EGamePickupType.NearbyPickups then
    --             return data
    --         elseif #data.interactors == 1 and #interactors == 1 and data.interactors[1] == interactors[1] then
    --             return data
    --         end
    --     end
    -- end

    -- return nil

    local lootSelectorData = {
        type = type,
        interactors = interactors
    }
    return lootSelectorData
end

function LootingLogic.IsInteractingShopStation()
    local data = Server.LootingServer:GetCurrentSelectorData()
    return data and data.type == EGamePickupType.ShopStation or false
end

---@param item ItemBase
function LootingLogic.GetSuitableSlot(item, slotGroup, bCheckEquipSlot)
	local targetEquipSlot = nil
    local featureType = item:GetFeatureType()

	if not targetEquipSlot and bCheckEquipSlot and item:IsEquipableItem() then
        if featureType == EFeatureType.Weapon then
            -- local weaponFeature = item:GetFeature(EFeatureType.Weapon)
            targetEquipSlot = Server.InventoryServer:GetEquipmentAvailableSlot(item, slotGroup)
        elseif featureType == EFeatureType.Equipment then
            targetEquipSlot = Server.InventoryServer:GetEquipmentEmptySlot(item, slotGroup)
        elseif featureType == EFeatureType.Health then
            local medicineSlot = Server.InventoryServer:GetSlot(ESlotType.Medicine, slotGroup)
            local equipHealthItem = medicineSlot:GetEquipItem()
            -- 如果未装药品，或者可以合并，则将药品装到槽位上
            if not equipHealthItem or ItemBaseTool.CheckItemCanCombine(equipHealthItem, item) then
                targetEquipSlot = medicineSlot
            else
                targetEquipSlot = nil
            end
        end
	end
	if not targetEquipSlot then
		targetEquipSlot = LootingOperaLogic.GetItemAvaiableCarrySlot(item, slotGroup)
	end

	return targetEquipSlot
end

--快速智能拾取
function LootingLogic.QuickLootItem(itemData, slotGroup, bRefreshView, bCheckEquipSlot, bShowTip)
    bShowTip = setdefault(bShowTip, false)
    local ret = false
    local targetEquipSlot = LootingLogic.GetSuitableSlot(itemData, slotGroup, bCheckEquipSlot)

    if targetEquipSlot then
        ret = ItemOperaTool.DoPlaceItem(itemData, targetEquipSlot, bShowTip)
    elseif bShowTip then
        -- 囚徒状态下拾取武器装备弹“囚徒状态禁止拾取装备和武器”
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if isvalid(playerState) and playerState.bJailBreak and itemData and itemData:IsEquipableItem() then
            Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.LootingFail_JailBreakFobidden)
        else
            Module.CommonTips:ShowSimpleTip(LootingConfig.Loc.LootingFailNoEnoughSpaceTip)
        end
    end
    return ret, targetEquipSlot
end

---@param itemData ItemBase
function LootingLogic.DiscardOperation(itemData)
    -- 先检查是否是武器上配件的丢弃
    local rootGunItem = itemData:TryGetRootGunItem()
    if rootGunItem then
        if Module.FastEquip:CheckFastUnEquip(itemData, rootGunItem, true) then
            local nearbyItemSlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
            return Module.FastEquip:FastUnEquip(
                rootGunItem,
                nil,
                itemData,nil,-1,-1,0,false,nil
            )
        end

        return false
    end

    if itemData.InSlot == nil then
        return false
    end

    if itemData.InSlot.SlotType == ESlotType.NearbyPickups 
    or (itemData.InSlot:GetSlotGroup() == ESlotGroup.Nearby and itemData.InSlot:IsEquipContainerSlot())
    then
        Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.CannotDiscardedRepeatedly, 2)
        return false
    end

    local nearbyItemSlot = Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby)
    return ItemOperaTool.DoPlaceItem(itemData, nearbyItemSlot)
end

function LootingLogic._ProcessItemUseReason(reason)
    local reasonTip = Module.Looting.Config.MapUseItemResult2Tip[reason]
    if reasonTip then
        Module.CommonTips:ShowSimpleTip(reasonTip)
    end
end

function LootingLogic.ReqEquipWeaponItemById(itemId, toSlotType)
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if not invMgr then
        return false
    end

    invMgr:AttemptEquipItemByID(itemId, toSlotType)
    return true
end

function LootingLogic.ReqEquipWeaponItemByGid(itemGid)
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if not invMgr then
        return false
    end

    invMgr:AttemptEquipItem(itemGid, EAttachPosition.Attach_None)
    
    return LootingLogic.Switch2Weapon(itemGid)
end

function LootingLogic.ReqUnEquipAmmoByGunGid(gunGid)
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if not invMgr then
        return false
    end

    local currentTime = TimeUtil.GetCurrentTimeMillis()
    if getField().reqUnEquipAmmoTime > 0 and currentTime - getField().reqUnEquipAmmoTime < 0.3 then
        return false
    end
    getField().reqUnEquipAmmoTime = currentTime

    local PutInSafeBoxFirst = LootingOperaLogic.GetPutInSafeBoxFirstArray(gunGid)

    invMgr:ServerWeaponUnEquipAmmo(gunGid, 0, PutInSafeBoxFirst, 0)
    return true
end

function LootingLogic.Switch2Weapon(weaponGid)
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if invMgr then
        invMgr:AttemptSwitchWeaponByGid(weaponGid)

        return true
    end

    return false
end

function LootingLogic.ReloadWeapon(weaponGid, itemId, itemGid)
    local weaponWrapper = WeaponHelperTool.GetWeaponWrapper(weaponGid)
    
    if not weaponWrapper:CanChangeClip() then
        -- Check reason here
        local carriedAmmoCount = weaponWrapper:GetCarriedAmmoCount()
        if carriedAmmoCount == 0 then
            Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.NoAvailableAmmo)
        else
            Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.NoNeed2Reload)
        end

        return false
    end
    weaponWrapper:Reload()

    LootingConfig.Events.evtPostReloadWeapon:Invoke(weaponGid, itemId, itemGid)

    return true
end

function LootingLogic.GetSearchAudioByQuality(quality)
    return Module.Looting.Config.MapQuality2SearchAudio[quality] or Module.Looting.Config.MapQuality2SearchAudio[0]
end

---@param item ItemBase
function LootingLogic.CheckIsRealWeapon(item)
    local character = Facade.GameFlowManager:GetCharacter()
	local realWeapon = character:GetRealWeapon()

    return realWeapon.WeaponGuid == item.gid
end

---@return ItemSlot[]
local function fGetAllLootingSlots2Check()
    local ret = {
        Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.Pistrol, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.Helmet, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.BreastPlate, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.Bag, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.ChestHanging, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.ChestHangingContainer, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.Pocket, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.BagContainer, Server.LootingServer:GetSelfSlotGroup()),
        Server.InventoryServer:GetSlot(ESlotType.SafeBoxContainer, Server.LootingServer:GetSelfSlotGroup()),
    }
    local currentInteractorType = Server.LootingServer:GetCurrentInteractorType()
    if currentInteractorType == EGamePickupType.NearbyDeadBody then
        table.append(ret,{
            Server.InventoryServer:GetSlot(ESlotType.MainWeaponLeft, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.MainWeaponRight, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.Pistrol, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.Helmet, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.BreastPlate, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.Bag, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.ChestHanging, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.ChestHangingContainer, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.Pocket, ESlotGroup.DeadBody),
            Server.InventoryServer:GetSlot(ESlotType.BagContainer, ESlotGroup.DeadBody),
        })
    elseif currentInteractorType == EGamePickupType.SceneBox then
        table.append(ret,{
            Server.InventoryServer:GetSlot(ESlotType.NearbyContainer, ESlotGroup.Nearby),
        })
    elseif currentInteractorType == EGamePickupType.DropContainer then
        table.append(ret, {
            Server.InventoryServer:GetSlot(ESlotType.Bag, ESlotGroup.Nearby),
            Server.InventoryServer:GetSlot(ESlotType.BagContainer, ESlotGroup.Nearby),
            Server.InventoryServer:GetSlot(ESlotType.ChestHanging, ESlotGroup.Nearby),
            Server.InventoryServer:GetSlot(ESlotType.ChestHangingContainer, ESlotGroup.Nearby),
        })
    elseif currentInteractorType == EGamePickupType.NearbyPickups then
        table.append(ret, {
            Server.InventoryServer:GetSlot(ESlotType.NearbyPickups, ESlotGroup.Nearby),
        })
    end

    return ret
end

---@param item ItemBase
function LootingLogic.CommonBuildDragDropInfoInLooting(item, itemPreview)
    ---@type ItemDragDropInfo
    local itemDragDropInfo = {
        item = item,
        itemPreview = itemPreview,
        allAvailableSlots = {},
        defaultTargetSlot = nil
    }

    if not item then
        return itemDragDropInfo
    end

    ItemOperaTool.EnableUseCacheToSpeedUp(true)

    local allAvailableSlots = {}
    local slots2Check = fGetAllLootingSlots2Check()

    ---@type ItemMoveCmd
    local moveCmd = ItemMoveCmd:NewIns()
    local fromLocation = item.InSlot and item.InSlot:GetItemLocation(item) or nil

    ---@type ItemLocation
    local targetLoc = ItemLocation:PoolObtain()
    for _, slot in pairs(slots2Check) do
        targetLoc:Reset()
        if slot:CheckItemFitSlot(item) then
            if ItemOperaTool.ShouldCheckEquippedNotEmptyContainer(item, slot) then
                targetLoc:Init(slot, -1, -1)
            else
                if slot:IsEquipableSlot() then
                    targetLoc:InitEquipLoc(slot)
                elseif slot.SlotType == ESlotType.NearbyPickups then
                    targetLoc:InitAnywhereLoc(slot, item)
                else
                    local foundLoc = slot:TryFindLocationForItem(item)
                    if foundLoc then
                        targetLoc:InitFromOtherLocation(foundLoc)
                    elseif ItemOperaTool.TryPlaceItems({item}, slot) then
                        targetLoc:Init(slot, -1, -1)
                    end
                end
            end
        end

        if targetLoc and targetLoc.ItemSlot then
            if fromLocation then
                moveCmd:InitCmd(item, fromLocation, targetLoc)
                if moveCmd:CheckIsValid(false) then
                    table.insert(allAvailableSlots, slot)
                end
            elseif item:GetRootGid() ~= 0 then
                table.insert(allAvailableSlots, slot)   -- 兼容配件
            end
        end
    end
    ItemLocation:PoolFree(targetLoc)

    itemDragDropInfo.allAvailableSlots = allAvailableSlots
    ItemOperaTool.SetCacheAvailableSlotsForItem(item, allAvailableSlots)
    if LootingVehicleLogic.IsOperatingVehicle() then
        itemDragDropInfo.defaultTargetSlot = LootingOperaLogic.GetAutoSnapSlotInLooting_Vehicle(item)
    else
        itemDragDropInfo.defaultTargetSlot = LootingOperaLogic.GetAutoSnapSlotInLooting(item)
    end

    ItemOperaTool.EnableUseCacheToSpeedUp(false)

    return itemDragDropInfo
end

function LootingLogic.CheckIsDragFromLeft(operation, bIsLootingMainView)
    local itemView = operation.WidgetReference
    if bIsLootingMainView then
        -- LootingMainView
        local viewportGeometry = UWidgetLayoutLibrary.GetViewportWidgetGeometry(GetWorld())
        local viewportLocalSize = viewportGeometry:GetLocalSize()
        
        local geometry = itemView:GetCachedGeometry()
        local centerScreenPos = geometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.CENTER_CENTER_VECTOR)
        local centerLocalPos = viewportGeometry:AbsoluteToLocal(centerScreenPos)
        
        return centerLocalPos.X <= viewportLocalSize.X * 0.5
    else
        local item = itemView.item
        local inSlot = item and item.InSlot or nil
        if inSlot then
            return inSlot:GetSlotGroup() == ESlotGroup.Player
        else
            return false
        end
    end

end


local function fOnItemDetailForLootPanelLoaded(Panel)
    getField():SetItemDetailForLootPanel(Panel)
end

function LootingLogic.OnInteractDetailNotify(itemId, pickup, btnType)
    local item
    if btnType == 0 or btnType == 2 then
        -- 表示缺少该道具
        item = ItemBase:New(itemId, 1, nil)
        item.bUseRenderTexture = false
    elseif btnType == 1 then
        local items = Server.LootingServer:InitNearbyPickupData({pickup})
        item = items[1]
        if not item then
            return
        end
    else
        return
    end

    -- 加载道具详情面板
    -- Init
    ---@type ItemDetailForLoot
    local panel = getField():GetItemDetailForLootPanel()
    if not isvalid(panel) then
        local panelHandle = getField():GetItemDetailForLootHandle()
        if not panelHandle then
            panelHandle=Facade.UIManager:AsyncShowUI(UIName2ID.ItemDetailForLoot,fOnItemDetailForLootPanelLoaded,nil,item,btnType)
            getField():SetItemDetailForLootHandle(panelHandle)
        end
    else
        panel:Update(item,btnType)
        panel:Show()
    end
end

-- DFHD ShowHideInteractDetailPanel With keyboard
function LootingLogic.OnSwitchInteractDetailNotify(itemId, pickup, btnType)
    local item
    if btnType == 0 or btnType == 2 then
        -- 表示缺少该道具
        item = ItemBase:New(itemId, 1, nil)
        item.bUseRenderTexture = false
    elseif btnType == 1 then
        local items = Server.LootingServer:InitNearbyPickupData({pickup})
        item = items[1]
        if not item then
            return
        end
    else
        return
    end

    -- 加载道具详情面板
    -- Init
    -- panel 不是详情面板而是loot面板 这里要开关详情面板 通过panel间接创造详情面板 ？

    -- 创建详情面板前需要先创建loot面板panel作为父widget
    ---@type ItemDetailForLoot
    local panel = getField():GetItemDetailForLootPanel()
    if not isvalid(panel) then
        Facade.UIManager:AsyncShowUI(UIName2ID.ItemDetailForLoot,fOnItemDetailForLootPanelLoaded,nil,item,btnType)
    else
        if isvalid(panel._itemDetailHandle) then
            local itemDetail = panel._itemDetailHandle:GetUIIns()
            if isvalid(itemDetail) and itemDetail._curUIState == LuaUIBaseView.EUIState.Show then
                Module.ItemDetail:CloseItemDetailPanel()
            else
                panel:Update(item,btnType)
            end
        end
    end
end

function LootingLogic.TryCloseInteractDetailNotify()
    local panel = getField():GetItemDetailForLootPanel()
    if isvalid(panel) and isvalid(panel._itemDetailHandle) then
        local itemDetail = panel._itemDetailHandle:GetUIIns()
        if isvalid(itemDetail) and itemDetail._curUIState == LuaUIBaseView.EUIState.Show then
            Module.ItemDetail:CloseItemDetailPanel()
        end
    end
end

function LootingLogic.CloseItemDetailForLootPanel()
    local field = getField()
    if field then
        local panel = field:GetItemDetailForLootPanel()
        if isvalid(panel) then
            panel:HideView()
        end
    end
end

function LootingLogic.ClientReportLootInteraction(lootObj)
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if invMgr and lootObj then
        invMgr:ClientReportLootInteraction(lootObj)
    end
end

function LootingLogic.CheckFirstPickUpTeamMateItem(moveItemInfo)
    if not moveItemInfo then
        return false
    end
    if moveItemInfo.item:CheckIsTeammateBind() then
        local bFirstPickUpTeammateItem = getField():GetFirstPickUpTeammateItem()
        loginfo("LootingLogic.CheckFirstPickUpTeamMateItem",moveItemInfo.item.name, bFirstPickUpTeammateItem)
        if bFirstPickUpTeammateItem and moveItemInfo.Reason == PropChangeType.Add and moveItemInfo.NewLoc.ItemSlot:GetSlotGroup() == ESlotGroup.Player then
            if not Facade.GameFlowManager:IsInOBMode() then
                Module.CommonTips:ShowSimpleTip(Module.Looting.Config.Loc.LootingFirstPickUpTeammateItemTip)
            end
            getField():SetFirstPickUpTeammateItem(false)
            loginfo("LootingLogic.CheckFirstPickUpTeamMateItem设置了",getField():GetFirstPickUpTeammateItem())
            return true
        end
    end
    return false
end

function LootingLogic.CheckCurrentMapHasKeyChain()
    local keyBoxTable = Facade.TableManager:GetTable("Key/KeyBox")

    for _, cfg in pairs(keyBoxTable) do
        if ItemOperaTool.CheckKeyIndexMatchCurrentMapId(cfg.MapID) then
            return true
        end
    end
    return false
end

function LootingLogic.GetPlayerGainValue()
    local RepData
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    local invMgr = InGameController:Get():GetGPCharacterInventoryMgr()
    if isvalid(obInvMgr) then
        RepData = obInvMgr.InventoryRepData
    elseif invMgr then
        RepData = invMgr.RepData
    end
    if not RepData then
        return 0
    end
    return math.max(RepData.GainValue, 0)
end

function LootingLogic.GetPickupCurrency()
    local RepData
    local obInvMgr = Facade.GameFlowManager:GetOBInvMgr()
    local invMgr = InGameController:Get():GetGPCharacterInventoryMgr()
    if isvalid(obInvMgr) then
        RepData = obInvMgr.InventoryRepData
    elseif invMgr then
        RepData = invMgr.RepData
    end
    if not RepData then
        return false
    end
    return RepData.PickupCurrency
end

function LootingLogic.ScrollToTargetWidget(scrollBox, itemMoveInfo, reason, targetScroll2Widget)
    local function ScrollToItem()
    --    if targetScroll2Widget then
    --        scrollBox:EndInertialScrolling()
    --        scrollBox:ScrollWidgetIntoView(targetScroll2Widget, true, EDescendantScrollDestination.IntoView)
    --    else
    --        LootingLogic.SetItemScrollCmd(item, scrollBox)
    --    end
        if getField().bDisableScrollSearchedItem then
            return
        end
        Module.CommonWidget:SetItemScrollCmd(itemMoveInfo, scrollBox)
    end
    local bLastModifyReasonIsSearch = itemMoveInfo.item:GetLastModifyReason() == EItemInfoUpdatedReason.SearchState
    if reason == PropChangeType.Modify and bLastModifyReasonIsSearch then
        --if not getField().bHasScrollToSearchedItem then
        --    ScrollToItem()
        --    getField().bHasScrollToSearchedItem = true
        --end
    else
        ScrollToItem()
    end
end

function LootingLogic.HasInspectButton(item)
    if item and item.InSlot then
        local slotGroup = item.InSlot:GetSlotGroup()
        if slotGroup == ESlotGroup.Player or slotGroup == ESlotGroup.CollectionRoom then
            local itemConf = ItemConfigTool.GetItemConfigById(item.id)
            if itemConf and itemConf.bHighValueItemNeedInspection then
                return true
            end
        end
    end
    return false
end

function LootingLogic.HasSpecialInspectButton(item)
    if item and item.InSlot then
        local slotGroup = item.InSlot:GetSlotGroup()
        if slotGroup == ESlotGroup.Player or slotGroup == ESlotGroup.CollectionRoom then
            local itemAsset = ItemConfigTool.GetItemAssetById(item.id)
            if itemAsset and itemAsset.bUseInspectPro then
                return true
            end
        end
    end
    return false
end

if DFHD_LUA == 1 then
    extendclass(LootingLogic, require "DFM.Business.Module.LootingModule.LootingLogic_HD")
end

--- BEGIN MODIFICATION @ VIRTUOS: 主机需要替换按键图标
function LootingLogic.GetButtonType()
    if DFCONSOLE_LUA == 1 then
        return Module.ItemDetail.Config.ButtonTypeConsole
    else
        return Module.ItemDetail.Config.ButtonType
    end
end
--- END MODIFICATION

function LootingLogic.GetCurSwimOxygenPercent()
    local character = Facade.GameFlowManager:GetCharacter()
    if isvalid(character) and isvalid(character.Comboard) then
        return character.Comboard:GetCurSwimOxygenPercent()
        --return character.Comboard.SwimOxygenValue / 100
    end
    return 1
end

function LootingLogic.GetSafeBoxSkinID()
    local playerState = UGPBattleFieldSystem.Get(GetWorld()):GetCurrentLocalPlayerOrOBPlayerState()
    if isvalid(playerState) and playerState.SafeBoxSkinID ~= 0 then
        local slot = Server.InventoryServer:GetSlot(ESlotType.SafeBoxContainer)
        -- 目前只有9格安全箱能装配外观，这里再判断一层
        if slot and slot:GetTotalCapacity() == 9 then
            return playerState.SafeBoxSkinID
        end
    end
    return 0
end

return LootingLogic