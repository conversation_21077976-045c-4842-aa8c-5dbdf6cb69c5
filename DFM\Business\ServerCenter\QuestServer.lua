----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

local QuestStruct = require "DFM.Business.DataStruct.QuestStruct.QuestStruct"
local QuestLineStruct = require "DFM.Business.DataStruct.QuestStruct.QuestLineStruct"
local QuestSeasonLineStruct = require "DFM.Business.DataStruct.QuestStruct.QuestSeasonLineStruct"
local QuestCollectorStruct = require "DFM.Business.DataStruct.QuestStruct.QuestCollectorStruct"
local QuestFactStruct = require "DFM.Business.DataStruct.QuestStruct.QuestFactStruct"
local UGPGameplayDelegates = import "GPGameplayDelegates"

local FGPLuaQuestData = import "GPLuaQuestData"
local FGPLuaQuestObjectiveData = import "GPLuaQuestObjectiveData"
-- QuestState = {
-- Locked = 0,
-- Unread = 1,
-- Unaccepted = 2,
-- Accepted = 3,
-- Failed = 4,
-- Paused = 5,
-- Completed = 6,
-- Rewarded = 7,
-- }

--- BEGIN MODIFICATION @ VIRTUOS: 实现PS5 Activity相关内容
local UDFMPlatformActivityManager = import("DFMPlatformActivityManager")
--- END MODIFICATION

---@class QuestServer : ServerBase
local QuestServer = class("QuestServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

function QuestServer:Ctor()
    -- key:QuestId, value:QuestStruct
    self._questId2QuestDict = {} --这个主要是保存任务模块每个任务的信息

    -- key:QuestLineId, value:QuestLineStruct
    self._solQuestLineId2QuestLineDict = {} -- 顾名思义，就是任务线id对应该任务线的信息
    -- key:SeasonQuestLineId, value:QuestSeasonLineStruct
    self._solSeasonQuestLineId2LineDict = {}

    self._questCollectorData = nil
    self.curSeasonId = 0

    self._questFactData = nil

    -- 判断是否初始化任务数据
    self._bInitQuestLineNQuestDataAlready = false

    self.Events = {
        evtUpdateObjectiveState = LuaEvent:NewIns("QuestServer.evtUpdateObjectiveState"),
        evtUpdateQuestState = LuaEvent:NewIns("QuestServer.evtUpdateQuestState"),
        -- 任务道具提交成功
        evtQuestSubmitItemsSuccess = LuaEvent:NewIns("QuestServer.evtQuestSubmitItemsSuccess"),
        -- 任务追踪状态更新
        evtQuestTrackingStateUpdate = LuaEvent:NewIns("QuestServer.evtQuestTrackingStateUpdate"),
        -- 锁定(隐藏)任务线(章节)开启(显示)
        evtLockedQuestLineOpen = LuaEvent:NewIns("QuestServer.evtLockedQuestLineOpen"),
        -- 赛季等级升级显示
        evtUpdateSeasonLevel = LuaEvent:NewIns("QuestServer.evtUpdateSeasonLevel"),
        -- 缓存锁定任务线开启
        evtCacheLockedQuestLineOpen = LuaEvent:NewIns("QuestServer.evtCacheLockedQuestLineOpen"),
        -- 任务成功接取
        evtQuestAcceptSuccess = LuaEvent:NewIns("QuestServer.evtQuestAcceptSuccess"),
        -- 任务成功领取奖励
        evtQuestGetReward = LuaEvent:NewIns("QuestServer.evtQuestGetReward"),
        -- 变化进度提示
        evtQuestObjectiveUpdateTips = LuaEvent:NewIns("QuestServer.evtQuestObjectiveUpdateTips"),
        -- 更新任务红点
        evtUpdateQuestReddots = LuaEvent:NewIns("QuestServer.evtUpdateQuestReddots"),
        evtShowNewQuestInfo = LuaEvent:NewIns("QuestServer.evtShowNewQuestInfo"),
        evtSeasonClose = LuaEvent:NewIns("QuestServer.evtSeasonClose"),
        evtQuestExitDetailView = LuaEvent:NewIns("QuestServer.evtQuestExitDetailView"),
        evtSeasonInfoUpdate = LuaEvent:NewIns("QuestServer.evtSeasonInfoUpdate"),

        evtSeasonCollectorRefresh = LuaEvent:NewIns("QuestServer.evtSeasonCollectorRefresh"),
        evtSeasonCollectorLockRefresh = LuaEvent:NewIns("QuestServer.evtSeasonCollectorLockRefresh"),
        evtSeasonCollectorRewardRefresh = LuaEvent:NewIns("QuestServer.evtSeasonCollectorRewardRefresh"),

        evtSeasonOvertimeRewardTaken = LuaEvent:NewIns("QuestServer.evtSeasonOvertimeRewardTaken")
    }

    self._server_time = 0
    self._client_time_recive_pkg = 0
    self.server_quest_data_req = CreateCPlusCallBack(self.GetLuaQuestDataFunc, self)
    UGPGameplayDelegates.Get(GetWorld()).GetLuaQuestData:Add(self.server_quest_data_req)

    -- DatatableHotfix
    -- Key:QuestObjectiveId, Value:QuestId
    self._questObjectiveId2QuestIdDict = {}

    self._questConditionId2QuestIdDict = {}

    -- Key:QuestRewardId, Value:QuestId
    self._questRewardId2QuestIdDict = {}
    self._questLinenDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("QuestLine", self._OnHotfixQuestLineDT, self)
    self._questDTHotfixHandle = Facade.DataTableHotfixManager:AddHotfixCallback("Quest", self._OnHotfixQuestDT, self)
    self._questObjectivesDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("QuestObjectives", self._OnHotfixQuestObjectivesDT, self)
    self._ComplexPropObjectiveDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("ComplexPropObjective", self._OnComplexPropObjectiveDT, self)
    self._questRewardsDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("QuestRewards", self._OnHotfixQuestRewardsDT, self)
    self._questConditionDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("QuestCondition", self._OnHotfixQuestConditionDT, self)

    self._seasonQuestLineDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("SeasonQuestLine", self._OnHotfixSeasonQuestLineDT, self)
    self._seasonQuestStageDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("SeasonQuestStage", self._OnHotfixSeasonQuestStageDT, self)
    self._seasonQuestGroupDTHotfixHandle =
        Facade.DataTableHotfixManager:AddHotfixCallback("SeasonQuestGroup", self._OnHotfixSeasonQuestGroupDT, self)
end

------------------------------------ Override function ------------------------------------
function QuestServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSQuestDataChangeNtf", self._CSQuestDataChangeNtf, self)
    self:_InitData()
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtSeasonLevelUpdate, self._OpenLevelAchieved, self)
end

function QuestServer:OnDestroyServer()
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._questLinenDTHotfixHandle)
    self._questLinenDTHotfixHandle = nil
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._questDTHotfixHandle)
    self._questDTHotfixHandle = nil
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._questObjectivesDTHotfixHandle)
    self._questObjectivesDTHotfixHandle = nil
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._ComplexPropObjectiveDTHotfixHandle)
    self._ComplexPropObjectiveDTHotfixHandle = nil
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._questRewardsDTHotfixHandle)
    self._questRewardsDTHotfixHandle = nil

    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._seasonQuestLineDTHotfixHandle)
    self._seasonQuestLineDTHotfixHandle = nil
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._seasonQuestStageDTHotfixHandle)
    self._seasonQuestStageDTHotfixHandle = nil
    Facade.DataTableHotfixManager:RemoveHotfixCallback(self._seasonQuestGroupDTHotfixHandle)
    self._seasonQuestGroupDTHotfixHandle = nil


    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self._questId2QuestDict = nil
    self._solQuestLineId2QuestLineDict = nil
    self._solSeasonQuestLineId2LineDict = nil

    self._questObjectiveId2QuestIdDict = nil
    self._questConditionId2QuestIdDict = nil
    self._questRewardId2QuestIdDict = nil
    UGPGameplayDelegates.Get(GetWorld()).GetLuaQuestData:Remove(self.server_quest_data_req)
    self:RemoveLuaEvent(Server.RoleInfoServer.Events.evtSeasonLevelUpdate)
end

function QuestServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function QuestServer:OnLoadingGame2Frontend(gameFlowType)
    self._bInitQuestLineNQuestDataAlready = false
    self:FetchServerData()
    self._solUnderwayQuest = nil
end

function QuestServer:OnLoadingFrontend2Game(gameFlowType)
    -- 暂时不清除，给局内新手使用，后面修改
    self._questId2QuestDict = {}
    self._solQuestLineId2QuestLineDict = {}
    self._solSeasonQuestLineId2LineDict = {}
    self._questObjectiveId2QuestIdDict = {}
    self._questConditionId2QuestIdDict = {}
    self._questRewardId2QuestIdDict = {}
    self._bInitQuestLineNQuestDataAlready = false
    self._solUnderwayQuest = self:_GetAllSOLUnderwayQuestForce()
    self._questCollectorData = nil
    self._questFactData = nil
end

---
function QuestServer:GetLuaQuestDataFunc(QuestData)
    --local data = UGPLuaDataDefine.CreateQuestDataModel(self)
    for id, questInfo in pairs(self._questId2QuestDict) do
        local data = FGPLuaQuestData()
        data.quest_id = id
        data.quest_state = questInfo.state
        data.quest_accept_time = questInfo.acceptTime
        data.expire_time = questInfo.expire_time
        data.reward_time = setdefault(questInfo.reward_time, data.reward_time)
        data.marked = false
        data.ServerTime = self._server_time

        local client_time = TimeUtil.GetTimeSeconds(self)
        local pass_time = client_time - self._client_time_recive_pkg
        data.expire_left_time = data.expire_time - data.ServerTime - pass_time

        for _, TraceObjectiveid in pairs(questInfo.trackingObjectivesId) do
            data.marked_objective_id.Add(TraceObjectiveid)
        end

        for oid, objectiveInfo in pairs(questInfo._objectiveDict) do
            local objectiveData = FGPLuaQuestObjectiveData()
            objectiveData.quest_objective_id = objectiveInfo.id
            objectiveData.has_completed = objectiveInfo.bIsFinsih
            objectiveData.value = objectiveInfo.num
            objectiveData.has_marked = objectiveInfo.bTracking
            if objectiveData.has_marked then
                data.marked = true
            end
            objectiveData.spent_seconds = objectiveInfo.spendTime
            objectiveData.required_count = objectiveInfo.numNeeded
            objectiveData.accepted_state = objectiveInfo.bAcceptedState

            data.quest_objectives:Add(objectiveData)
        end

        QuestData.data:Add(data)
    end

    return {}
end

--- 每个Server用于拉取全量数据的接口
function QuestServer:FetchServerData()
    if not self._bInitQuestLineNQuestDataAlready then
        self:_InitData()
        self:_UpdateQuestLine(Server.RoleInfoServer.seasonLevel, false)
    end
    self:GetAllQuest()
    self:GetQuestLineSeasonDataReq()
end

-- 重置任务数据（GM删除任务专用）
function QuestServer:ResetServerData()
    self._questId2QuestDict = {}
    self._solQuestLineId2QuestLineDict = {}
    self._questCollectorData = nil
    self._questFactData = nil
    self:_InitData()
end

-- function QuestServer:OnGameFlowChangeEnter(gameFlowType)
-- 	if gameFlowType == EGameFlowStageType.SafeHouse or gameFlowType == EGameFlowStageType.ModeHall then
-- 		Server.QuestServer:FetchServerData()
-- 	end
-- end

------------------------------------ Event listener function ------------------------------------

--#region 表格热更新
function QuestServer:_OnHotfixQuestLineDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questLineInfo = self._solQuestLineId2QuestLineDict[dataRow.QuestLineID]
                if questLineInfo then
                    -- Update QuestLineOpenState
                    local bLimitConChanged = #dataRow.OpenLimits ~= #questLineInfo.openLimits
                    if not bLimitConChanged and #questLineInfo.openLimits > 0 then
                        for idx, questId in ipairs(questLineInfo.openLimits) do
                            if questId ~= dataRow.OpenLimits[idx] then
                                bLimitConChanged = true
                                break
                            end
                        end
                    end
                    if bLimitConChanged or dataRow.OpenLevel ~= questLineInfo.openLevel then
                        -- Even if bLineOpened had changed, hotfix no need to pop QuestLineOpen tips
                        local bLineOpened =
                            Server.RoleInfoServer.seasonLevel >= dataRow.OpenLevel and
                            self:IsQuestCompleted(dataRow.OpenLimits)
                        -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT >> Update QuestLine Open State >> questline name" , questLineInfo.lineName, " Is Open Limit Change:", tostring(bLimitConChanged), " cur OpenLevel:", questLineInfo.openLevel, " new OpenLevel:", dataRow.OpenLevel, " Is Line Opened:", questLineInfo.bLineOpened)
                        if questLineInfo.bLineOpened ~= bLineOpened then
                            questLineInfo.bLineOpened = bLineOpened
                        end
                    end
                    -- Update QuestLineRootQuest
                    if dataRow.RootQuestID ~= questLineInfo.rootQuestId then
                        local rootQuestInfo = self._questId2QuestDict[dataRow.RootQuestID]
                        if rootQuestInfo then
                            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT >> Update QuestLine Root Quest >> questline name" , questLineInfo.lineName, " cur rootQuest:", questLineInfo.rootQuestId, " new rootQuest:", dataRow.RootQuestID)
                            rootQuestInfo:SetQuestLine(questLineInfo.questLineId)
                            if rootQuestInfo.state <= QuestState.Unread then
                                rootQuestInfo:UpdateState(QuestState.Unaccepted)
                            end
                            if #rootQuestInfo.preQuestIdList ~= 0 then
                                logwarning(
                                    string.format(
                                        "Hotfix Quest 【%d】 is not a Root quest for Quest Line 【%d】 in QuestLine Table !",
                                        dataRow.RootQuestID,
                                        questLineInfo.questLineId
                                    )
                                )
                            end
                        end
                    end
                    questLineInfo:HotFixQuestLine(dataRow)
                else -- TODO:Add quest line
                end
            else
                logwarning(string.format("Cannot find Quest Line 【%s】 in QuestLine Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnHotfixQuestDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local objectivesCfg = Facade.TableManager:GetTable("QuestObjectives", false)
    local complexPropObjCfg = Facade.TableManager:GetTable("ComplexPropObjective", false)
    local rewardsCfg = Facade.TableManager:GetTable("QuestRewards", false)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    local conditionCfg = Facade.TableManager:GetTable("QuestCondition", false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixQuestDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questInfo = self._questId2QuestDict[dataRow.QuestID]
                if questInfo then
                    -- Update Previous&PostQuestsRelation
                    if dataRow.PreviousIDList then
                        local preQuestNum = #dataRow.PreviousIDList
                        local hasAdd = false
                        for _, preId in pairs(dataRow.PreviousIDList) do
                            local preQuest = self._questId2QuestDict[preId]
                            if preQuest ~= nil then
                                if preQuestNum > 1 then
                                    preQuest:AddMultiPostQuest(questInfo)
                                    if preQuest:IsConnectToPrevious() and not hasAdd then
                                        preQuest:AddPostQuests(questInfo)
                                        hasAdd = true
                                    end
                                else
                                    preQuest:AddPostQuests(questInfo)
                                    hasAdd = true
                                end
                            end
                        end
                    end

                    questInfo:DeleteQuestCondition(-1)

                    if dataRow.ResetAllWhenSettlementFailed then
                        questInfo:AddQuestCondition({QuestConditionID = -1, desc = ""})
                    end

                    questInfo:DeleteQuestCondition(-2)

                    if dataRow.ResetAllWhenFailed then
                        questInfo:AddQuestCondition({QuestConditionID = -2, desc = ""})
                    end

                    if dataRow.QuestConditionIDArray then
                        for _, conditionId in ipairs(dataRow.QuestConditionIDArray) do
                            local conditionTable = conditionCfg[tostring(conditionId)]
                            if conditionTable ~= nil then
                                questInfo:AddQuestCondition(conditionTable)
                            end
                        end
                    end

                    -- Update QuestObjectives
                    if dataRow.ObjectiveList then
                        local qusetObjectives = questInfo:GetObjectIDList()
                        for _, objectId in ipairs(qusetObjectives) do
                            self._questObjectiveId2QuestIdDict[objectId] = nil
                        end
                        questInfo:DeleteAllQuestObjective()
                        for _, oId in ipairs(dataRow.ObjectiveList) do
                            if objectivesCfg then
                                local complexPropObjRow = nil
                                if complexPropObjCfg then
                                    complexPropObjRow = complexPropObjCfg[tostring(oId)]
                                else
                                    logwarning(" DataTable ComplexPropObjective is nil!!!")
                                end
                                local objectiveRow = objectivesCfg[tostring(oId)]
                                if objectiveRow then
                                    local objInfo = questInfo:GetQusetObjectiveById(oId)
                                    if objInfo == nil then
                                        if complexPropObjRow then
                                            questInfo:AddQuestObjective(objectiveRow, complexPropObjRow)
                                        else
                                            questInfo:AddQuestObjective(objectiveRow)
                                            logwarning(
                                                "Can't find Objective ID 【%d】 in ComplexPropObjective Table !",
                                                oId
                                            )
                                        end
                                        -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestDT >> AddQuestObjective: objInfo:", objectiveRow.ObjectiveDesc, objectiveRow.ObjectiveID, " curQuestInfo :",questInfo.name, questInfo.id)
                                        self._questObjectiveId2QuestIdDict[objectiveRow.ObjectiveID] = questInfo.id
                                    else
                                        -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestDT >> HotFixObjective: objInfo:", objectiveRow.ObjectiveDesc, objectiveRow.ObjectiveID, " curQuestInfo :",questInfo.name, questInfo.id, " complexPropObjRow: ", complexPropObjRow)
                                        objInfo:HotFixObjective(objectiveRow, questInfo, complexPropObjRow)
                                    end
                                else
                                    logwarning(
                                        string.format("Can't find Objective ID 【%d】 in QuestObjectives Table !", oId)
                                    )
                                end
                            end
                        end
                    end

                    -- Update QuestRewards
                    if dataRow.RewardList then
                        local questRewards = questInfo:GetAllQuestRewards()
                        for rewardId, rewardInfo in pairs(questRewards) do
                            self._questRewardId2QuestIdDict[rewardId] = nil
                        end
                        questInfo:DeleteAllQuestReward()
                        local rewardList = {}
                        for _, rId in ipairs(table.tolist(dataRow.RewardList)) do
                            local rewardRow = rewardsCfg[rId]
                            if rewardRow ~= nil then
                                questInfo:AddQuestReward(rewardRow)
                                -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestDT >> AddQuestReward: rewardId:", rewardRow.RewardID, " curQuestInfo :",questInfo.name, questInfo.id)
                                self._questRewardId2QuestIdDict[rId] = questInfo.id
                            else
                                logwarning(string.format(" Can't find Reward ID 【%d】 in QuestRewards Table !", rId))
                            end
                        end
                    end
                    questInfo:HotFixQuest(dataRow)
                else -- TODO:Add quest
                    local questInfo =
                        self:_OnCreateQuestData(dataRow, objectivesCfg, rewardsCfg, complexPropObjCfg, conditionCfg)
                    self._questId2QuestDict[questInfo.id] = questInfo
                end
            else
                logwarning(string.format("Cannot find Quest 【%s】 in Quest Table !", key))
            end
        end
    end

    -- 构建任务树前后任务关联关系
    for id, questInfo in pairs(self._questId2QuestDict) do
        local hasAdd = false
        for _, preId in pairs(questInfo.preQuestIdList) do
            local preQuest = self._questId2QuestDict[preId]
            if preQuest ~= nil then
                if #questInfo.preQuestIdList > 1 then
                    preQuest:AddMultiPostQuest(questInfo)
                    if preQuest:IsConnectToPrevious() and not hasAdd then
                        preQuest:AddPostQuests(questInfo)
                        hasAdd = true
                    end
                else
                    preQuest:AddPostQuests(questInfo)
                    hasAdd = true
                end
            end
        end
    end
end

function QuestServer:_OnHotfixQuestObjectivesDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local complexPropObjCfg = Facade.TableManager:GetTable("ComplexPropObjective", false)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixQuestObjectivesDT >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questId = self._questObjectiveId2QuestIdDict[dataRow.ObjectiveID]
                if questId then
                    local questInfo = self._questId2QuestDict[questId]
                    if questInfo then
                        local complexPropObjRow = nil
                        if complexPropObjCfg then
                            complexPropObjRow = complexPropObjCfg[tostring(dataRow.ObjectiveID)]
                        else
                            logwarning(" HotfixQuestObjectives: DataTable ComplexPropObjective is nil!!!")
                        end
                        local objectInfo = questInfo:GetQusetObjectiveById(dataRow.ObjectiveID)
                        if objectInfo then
                            objectInfo:HotFixObjective(dataRow, questInfo, complexPropObjRow)
                        end
                    end
                end
            else
                logwarning(string.format("Cannot find Quest Objective 【%s】 in QuestObjectives Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestObjectivesDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnHotfixQuestConditionDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixQuestObjectivesDT >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questId = self._questConditionId2QuestIdDict[dataRow.QuestConditionID]
                if questId then
                    local questInfo = self._questId2QuestDict[questId]
                    if questInfo then
                        local conditionInfo = questInfo:GetQusetConditionById(dataRow.QuestConditionID)
                        if conditionInfo then
                            conditionInfo:HotFixObjective(dataRow, questInfo)
                        end
                    end
                end
            else
                logwarning(string.format("Cannot find Quest Objective 【%s】 in QuestObjectives Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestObjectivesDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnComplexPropObjectiveDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixQuestRewardsDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questId = self._questObjectiveId2QuestIdDict[dataRow.ObjectiveID]
                if questId then
                    local questInfo = self._questId2QuestDict[questId]
                    if questInfo then
                        local objectInfo = questInfo:GetQusetObjectiveById(dataRow.ObjectiveID)
                        if objectInfo then
                            objectInfo:AddComplexPropObjective(dataRow)
                        end
                    end
                end
            else
                logwarning(string.format("Cannot find Quest Objective 【%s】 in ComplexPropObjective Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestRewardsDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnHotfixQuestRewardsDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixQuestRewardsDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questId = self._questRewardId2QuestIdDict[dataRow.RewardID]
                if questId then
                    local questInfo = self._questId2QuestDict[questId]
                    if questInfo then
                        questInfo:HotFixQuestRewardById(dataRow.RewardID, dataRow)
                    end
                else
                    logwarning(
                        string.format(
                            "Cannot find Quest Reward 【%d】 in RewardId2QuestId map, maybe add new reward line and not be used in Quest Table !",
                            dataRow.RewardID
                        )
                    )
                end
            else
                logwarning(string.format("Cannot find Quest Reward 【%s】 in QuestRewards Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestRewardsDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnHotfixSeasonQuestLineDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixSeasonQuestLineDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                local questLineInfo = self._solSeasonQuestLineId2LineDict[dataRow.LineID]
                if questLineInfo then
                    questLineInfo:HotFixQuestLine(dataRow)
                else -- TODO:Add quest line
                end
            else
                logwarning(string.format("Cannot find Quest Line 【%s】 in QuestLine Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnHotfixSeasonQuestStageDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixSeasonQuestLineDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                for key, value in pairs(self._solQuestLineId2QuestLineDict) do
                    if value:GetStageInfoByID(dataRow.StageID) then
                        value:InitSeasonStageCfg(dataRow)
                    end
                end
            else
                logwarning(string.format("Cannot find Quest Line 【%s】 in QuestLine Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

function QuestServer:_OnHotfixSeasonQuestGroupDT(name)
    local hotfixKeys = Facade.DataTableHotfixManager:GetHotfixedKeys(name)
    local dataCfg = Facade.TableManager:GetTable(name, false)
    if dataCfg then
        for _, key in pairs(hotfixKeys) do
            loginfo(" >>>>>>>>>> xhz >> OnHotfixSeasonQuestLineDT >> >> Table: ", name, " index:", _, " row key:", key)
            local dataRow = dataCfg[key]
            if dataRow then
                for key, value in pairs(self._solQuestLineId2QuestLineDict) do
                    if value:GetGroupInfo(dataRow.GroupID) then
                        value:InitSeasonGroupCfg(dataRow)
                    end
                end
            else
                logwarning(string.format("Cannot find Quest Line 【%s】 in QuestLine Table !", key))
            end
            -- logerror(" >>>>>>>>>> xhz >> OnHotfixQuestLineDT cfg ", dataCfg, dataCfg[key])
        end
    end
end

--#endregion

--#region 初始化任务数据和表格数据
function QuestServer:_InitData()
    logwarning("QuestServer._InitData")
    self:_InitQuestData()
    self:_InitQuestLineData()
    self._bInitQuestLineNQuestDataAlready = true
end

function QuestServer:_OnCreateQuestData(questTable, objectivesCfg, rewardsCfg, complexPropObjCfg, conditionCfg)
    local questInfo = QuestStruct:New(questTable)
    for _, objId in ipairs(table.tolist(questTable.ObjectiveList)) do
        local objectiveTable = objectivesCfg[tostring(objId)]
        if objectiveTable ~= nil then
            if objectiveTable.Type == QuestObjectiveType.Submit and objectiveTable.IsComplexPropObjective then
                local ruleId = 0
                if objectiveTable.Param1 ~= "" then
                    ruleId = tonumber(objectiveTable.Param1)
                else
                    ruleId = tonumber(objId)
                end
                if complexPropObjCfg then
                    local complexPropObjTable = complexPropObjCfg[tostring(ruleId)]
                    if complexPropObjTable ~= nil then
                        questInfo:AddQuestObjective(objectiveTable, complexPropObjTable)
                    else
                        logwarning(
                            string.format(
                                "Can't find ComplexPropObjective's RuleID 【%d】 in ComplexPropObjective Table !",
                                ruleId
                            )
                        )
                    end
                else
                    logwarning("DataTable complexPropObjCfg is nil !!!")
                end
            else
                questInfo:AddQuestObjective(objectiveTable)
            end
        else
            logwarning(string.format("Can't find Objective ID 【%d】 in QuestObjectives Table !", objId))
        end
        self._questObjectiveId2QuestIdDict[objId] = questInfo.id
    end
    questInfo:ResetQuestCondition()
    if questTable.ResetAllWhenSettlementFailed then
        questInfo:AddQuestCondition({QuestConditionID = -1, desc = ""})
    end

    if questTable.ResetAllWhenFailed then
        questInfo:AddQuestCondition({QuestConditionID = -2, desc = ""})
    end
    for _, conditionId in ipairs(questTable.QuestConditionIDArray) do
        local conditionTable = conditionCfg[tostring(conditionId)]
        if conditionTable ~= nil then
            questInfo:AddQuestCondition(conditionTable)
        end
    end
    local rewardList = {}
    for _, rId in ipairs(table.tolist(questTable.RewardList)) do
        local rewardTable = rewardsCfg[tostring(rId)]
        if rewardTable ~= nil then
            questInfo:AddQuestReward(rewardTable)
            self._questRewardId2QuestIdDict[rId] = questInfo.id
        else
            logwarning(string.format("Can't find Reward ID 【%d】 in QuestRewards Table !", rId))
        end
    end

    return questInfo
end

function QuestServer:_InitQuestData()
    -- 暂时在这里做清理，待局内新手依赖修改完成之后放到OnLoadingFrontend2Game
    self._questId2QuestDict = {}

    local questsCfg = Facade.TableManager:GetTable("Quest", false)
    local objectivesCfg = Facade.TableManager:GetTable("QuestObjectives", false)
    local rewardsCfg = Facade.TableManager:GetTable("QuestRewards", false)
    local complexPropObjCfg = Facade.TableManager:GetTable("ComplexPropObjective", false)
    local conditionCfg = Facade.TableManager:GetTable("QuestCondition", false)
    for _, questTable in pairs(questsCfg) do
        if self._questId2QuestDict[questTable.QuestID] == nil then
            local questInfo =
                self:_OnCreateQuestData(questTable, objectivesCfg, rewardsCfg, complexPropObjCfg, conditionCfg)
            self._questId2QuestDict[questInfo.id] = questInfo
        else
            logwarning(string.format("Duplicate Quest ID 【%d】 in Quest Table ！", questTable.QuestID))
        end
    end
    -- 构建任务树前后任务关联关系
    for id, questInfo in pairs(self._questId2QuestDict) do
        local hasAdd = false
        for _, preId in pairs(questInfo.preQuestIdList) do
            local preQuest = self._questId2QuestDict[preId]
            if preQuest ~= nil then
                if #questInfo.preQuestIdList > 1 then
                    preQuest:AddMultiPostQuest(questInfo)
                    if preQuest:IsConnectToPrevious() and not hasAdd then
                        preQuest:AddPostQuests(questInfo)
                        hasAdd = true
                    end
                else
                    preQuest:AddPostQuests(questInfo)
                    hasAdd = true
                end
            end
        end
    end
end

function QuestServer:_InitQuestLineData()
    local questLineCfg = Facade.TableManager:GetTable("QuestLine", false)
    for _, lineTable in pairs(questLineCfg) do
        local questLine = QuestLineStruct:New(lineTable)
        if lineTable.SeasonId > 0 then
            loginfo("questline exist seasonid")
        else
            self._solQuestLineId2QuestLineDict[questLine.questLineId] = questLine
        end
        -- 设置
        local rootQuestInfo = self._questId2QuestDict[questLine.rootQuestId]
        if rootQuestInfo then
            rootQuestInfo:SetQuestLine(questLine.questLineId)
            rootQuestInfo:UpdateState(QuestState.Unaccepted)
            if #rootQuestInfo.preQuestIdList ~= 0 then
                logwarning(
                    string.format(
                        "Quest 【%d】 is not a Root quest for Quest Line 【%d】 in QuestLine Table !",
                        questLine.rootQuestId,
                        questLine.questLineId
                    )
                )
            end
        end
    end

    local seasonlineCfg = Facade.TableManager:GetTable("SeasonQuestLine", false)
    if seasonlineCfg then
        for _, lineTable in pairs(seasonlineCfg) do
            local questLine = QuestSeasonLineStruct:New(lineTable)
            local fCallback = CreateCallBack(function (self, questId, lineId, stageId, groupId)
                local questInfo = self:GetQuestInfoById(questId)
                if questInfo then
                    questInfo:SetSeasonInfo(lineId, stageId, groupId)
                end
            end, self)
            questLine:ForechSetQuestData(fCallback)
            self._solSeasonQuestLineId2LineDict[questLine.seasonLineId] = questLine
        end
    end
end
--#endregion

--#region 单个任务内容

--通知任务刷新
function QuestServer:_CSQuestDataChangeNtf(ntf)
    -- print(" >>>>>>>> _CSQuestDataChangeNtf : ", LogUtil.LogTable(ntf))
    logwarning(string.format("_UpdateQuestObjectState_self._questId2QuestDict_num【%d】", #self._questId2QuestDict))
    for k, quest in ipairs(ntf.player_quests) do
        self:_UpdateQuest(self._questId2QuestDict[quest.quest_id], quest)
    end

    for k, newQuest in ipairs(ntf.player_quests) do
        if ntf.quest_data_pre then
            logwarning("_UpdateQuestObjectState_ntf.quest_data_pre")
            for k, oldQuest in ipairs(ntf.quest_data_pre) do
                if newQuest.quest_id == oldQuest.quest_id then
                    for k, newQuestObj in ipairs(newQuest.quest_objectives) do
                        for k, oldQuestObj in ipairs(oldQuest.quest_objectives) do
                            if newQuestObj.quest_objective_id == oldQuestObj.quest_objective_id then
                                if newQuestObj.value ~= oldQuestObj.value then
                                    logwarning("_UpdateQuestObjectState_QuestObj.quest_objective_id")
                                    logwarning(
                                        string.format(
                                            "_UpdateQuestObjectState_QuestObj.quest_objective_id_【%d】",
                                            newQuestObj.quest_objective_id
                                        )
                                    )
                                    local questInfo = self._questId2QuestDict[newQuest.quest_id]
                                    if questInfo then
                                        self.Events.evtQuestObjectiveUpdateTips:Invoke(
                                            questInfo:GetQuestObjectivesById(newQuestObj.quest_objective_id),
                                            questInfo
                                        )
                                    else
                                        self.Events.evtQuestObjectiveUpdateTips:Invoke(newQuestObj, newQuest)
                                    end
                                end
                            end
                        end
                    end
                    break
                end
            end
        end
    end
end

-- 更新任务
function QuestServer:_UpdateQuest(questInfo, questData, bNotify)
    bNotify = setdefault(bNotify, true)
    if questInfo ~= nil then
        -- print(" >>>>>>>> _UpdateQuest oldState : ", questInfo.state, questInfo.name, bNotify)
        local oldState = questInfo.state
        questInfo:UpdateQuest(questData, bNotify)
        -- 状态变更触发通知
        -- print(" >>>>>>>> _UpdateQuest newState : ", questInfo.state, oldState, questInfo.name)
        if oldState ~= questInfo.state then
            -- 任务完成，判断任务线开启
            if questInfo.state >= QuestState.Completed then
                self:_UpdateQuestLine(Server.RoleInfoServer.seasonLevel, bNotify)
            end
        end
    end
end

-- 请求任务列表
function QuestServer:GetAllQuest()
    logwarning("QuestServer_GetAllQuest_Begin")
    local callback = function(res)
        if res.result == 0 then
            --- print(" >>>>>>>> GetAllQuest : ", LogUtil.LogTable(res, true))
            for k, quest in ipairs(res.player_quests) do
                self:_UpdateQuest(self._questId2QuestDict[quest.quest_id], quest, false)
            end

            for id, questInfo in pairs(self._questId2QuestDict) do
                questInfo:UpdateQuestLockState()
            end

            logwarning("QuestServer_GetAllQuest_End")
            self._server_time = res.lobby_server_time
            self._client_time_recive_pkg = TimeUtil.GetTimeSeconds(GetWorld())

            self._bIsTakeOvertimeReward = res.is_take_overtime_reward

            if self._questCollectorData == nil then
                self._questCollectorData = QuestCollectorStruct:New()
            end
            if res.collector_data then
                self._questCollectorData:UpdateCollectorData(res.collector_data)
            end
            if self._questFactData == nil then
                self._questFactData = QuestFactStruct:New()
            end
            --- BEGIN MODIFICATION @ VIRTUOS: 实现PS5 Activity相关内容
            if IsPS5() then
                self:_SyncPS5Activities()
            end
        --- END MODIFICATION

            self.Events.evtUpdateQuestReddots:Invoke()
        end
    end
    local req = pb.CSQuestGetPlayerDataReq:New()
    req:Request(callback)
end

-- 接受任务
function QuestServer:AcceptQuest(questId, fCallback)
    local callback = function(res)
        -- print(" >>>>>>>> AcceptQuest : ", LogUtil.LogTable(res))
        if res.result == 0 then
            local questInfo = self._questId2QuestDict[res.quest_id]
            if questInfo ~= nil then
                -- self:_UpdateQuest(questInfo, res)
                if fCallback then
                    fCallback(questInfo)
                end
                LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.AcceptingQuest)
                self.Events.evtQuestAcceptSuccess:Invoke()
            end

            --- BEGIN MODIFICATION @ VIRTUOS: 实现PS5 Activity相关内容
            -- 只有PS5需要activity
            if IsPS5() then
                local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
                if DFMPlatformActivityManager then
                    DFMPlatformActivityManager:StartActivity(res.quest_id)
                end
            end
        --- END MODIFICATION
        end
    end
    local req = pb.CSQuestAcceptReq:New()
    req.quest_id = questId
    req:Request(callback)
end

-- 完成任务
function QuestServer:_FinishQuest(questId)
    local callback = function(res)
        -- print(LogUtil.LogTable(res))
        if res.result == 0 then
            local questInfo = self._questId2QuestDict[res.quest_id]
            if questInfo ~= nil then
                questInfo:UpdateState(res.quest_state, true)
            end
        end
    end
    local req = pb.CSQuestCompleteReq:New()
    req.quest_id = questId
    req:Request(callback)
end

-- 获取任务奖励
function QuestServer:GetQuestRewards(questId, fCallback)
    local callback = function(res)
        -- print(" >>>>>>>> GetQuestRewards : ", LogUtil.LogTable(res))
        if res.result == 0 then
            --- END MODIFICATION
            -- 回包处理成功
            self.Events.evtQuestGetReward:Invoke()
            if fCallback then
                fCallback(res.is_send_prop_by_mail, res.is_currency_sent_by_mail, res.deposit_change)
            end

            --- BEGIN MODIFICATION @ VIRTUOS: 实现PS5 Activity相关内容
            -- 只有PS5需要activity
            if IsPS5() then
                local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
                if DFMPlatformActivityManager then
                    DFMPlatformActivityManager:CompleteActivity(res.quest_id)
                end
            end
        elseif res.result == -1 then
            -- 回包超时
            Facade.ProtoManager:ManuelHandleErrCode(res)
        end
    end
    local req = pb.CSQuestGetRewardReq:New()
    req.quest_id = questId
    req:Request(callback)
end

-- 提交道具
function QuestServer:SubmitItems(questId, submitObjectiveId, submitItemInfoList)
    local callback = function(res)
        -- print(" >>>>>>>> SubmitItems : ", LogUtil.LogTable(res))
        if res.result == 0 then
            for _, objectiveData in ipairs(res.player_quest.quest_objectives) do
                if objectiveData.quest_objective_id == submitObjectiveId then
                    self._questId2QuestDict[res.quest_id]:GetQuestObjectivesById(submitObjectiveId):UpdateObjective(
                        objectiveData,
                        true
                    )
                    self.Events.evtQuestSubmitItemsSuccess:Invoke(questId)
                end
            end
        end
    end

    local req = pb.CSQuestSubmitPropReq:New()
    req.quest_id = questId
    req.submit_list = {{obj_id = submitObjectiveId, submit_props = submitItemInfoList}}
    -- req.submit_prop_list = itemList
    req:Request(callback)
end

-- 暂停任务
function QuestServer:PauseQuset(questId)
    local callback = function(res)
        -- print(" >>>>>>>> PauseQuset : ", LogUtil.LogTable(res))
        if res.result == 0 then
            local questInfo = self._questId2QuestDict[res.quest_id]
            if questInfo ~= nil then
                questInfo:UpdateState(res.quest_state, true)
            end
        end
    end
    local req = pb.CSQuestPauseReq:New()
    req.quest_id = questId
    req:Request(callback)
end

-- 放弃任务
function QuestServer:GiveUpQuset(questId)
    local callback = function(res)
        -- print(" >>>>>>>> PauseQuset : ", LogUtil.LogTable(res))
        --print(" >>>>>>>> GiveUpQuset222 : ", LogUtil.LogTable(res))
        if res.remove_quest_id then
            --print(" >>>>>>>> GiveUpQuset444 : ", LogUtil.LogTable(res))
            local questInfo = self._questId2QuestDict[res.remove_quest_id]
            if questInfo ~= nil then
                --print(" >>>>>>>> GiveUpQuset : ", res.remove_quest_id)
                questInfo:GiveUpQuest()
            else
                print(" >>>>>>>> GiveUpQuset failed : ", res.remove_quest_id)
            end
        end
    end
    local req = pb.CSQuestGiveUpReq:New()
    req.remove_quest_id = questId
    --print(" >>>>>>>> GiveUpQuset111 : ", questId)
    req:Request(callback)
end

-- 任务目标追踪
function QuestServer:TraceQuestOjection(questId, objectiveId, bTracking)
    local callback = function(res)
        if res.result == 0 then
            local questInfo = self._questId2QuestDict[res.quest_id]
            if questInfo ~= nil then
                questInfo:UpdateQuestObjectiveTrack(res.quest_objective_id, res.mark_value)
                if res.obj_unmark_by_replace and res.obj_unmark_by_replace.quest_id > 0 then
                    local replaceInfo = self._questId2QuestDict[res.obj_unmark_by_replace.quest_id]
                    if replaceInfo then
                        replaceInfo:UpdateQuestObjectiveTrack(res.obj_unmark_by_replace.obj_id, false)
                        self.Events.evtQuestTrackingStateUpdate:Invoke(res.obj_unmark_by_replace.obj_id, replaceInfo)
                        LuaGlobalEvents.evtServerShowTip:Invoke(ServerTipCode.TrackReplace)
                    end
                else
                    LuaGlobalEvents.evtServerShowTip:Invoke(
                        bTracking and ServerTipCode.TrackSucc or ServerTipCode.TrackFail
                    )
                end
                self.Events.evtQuestTrackingStateUpdate:Invoke(res.quest_objective_id, questInfo)
            end
        end
    end
    local req = pb.CSQuestSetObjectiveMarkValueReq:New()
    req.quest_id = questId
    req.quest_objective_id = objectiveId
    req.mark_value = bTracking
    req:Request(callback)
end

-- CG播放完成
function QuestServer:CGQuestPlayFinish(questId, objectiveId)
    local callback = function(res)
        -- print(" >>>>>>>> CGQuestPlayFinish : ", LogUtil.LogTable(res))
        if res.result == 0 then
        end
    end
    local req = pb.CSQuestPlayCGCompletedReq:New()
    req.quest_id = questId
    req.quest_objective_id = objectiveId
    req:Request(callback)
end

function QuestServer:GetQuestInfoById(questId)
    return self._questId2QuestDict[questId]
end

-- 任务是否可接
function QuestServer:IsQuestAcceptable(questInfo)
    --local limitStr = ""
    if questInfo then
        if
            questInfo:GetRemainToAcceptTime() <= 0 and Server.RoleInfoServer.seasonLevel >= questInfo.levelLimit and
                (questInfo.state == QuestState.Unread or questInfo.state == QuestState.Unaccepted)
         then
            return true
        else
            return false
        end
    else
        return false
    end
end

-- 任务是否完成
-- questIds: 任务id列表
function QuestServer:IsQuestCompleted(questIds)
    if questIds == nil or #questIds <= 0 then
        return true
    end
    for _, id in pairs(questIds) do
        local curQuest = self._questId2QuestDict[id]
        if curQuest and curQuest.state < QuestState.Completed then
            return false
        end
    end
    return true
end

-- 任务是否接取
-- questId: 任务id
function QuestServer:IsQuestAccepted(questId)
    local questInfo = self._questId2QuestDict[questId]
    if questInfo then
        return questInfo.state > QuestState.Unaccepted
    end
    return false
end

-- 任务是否已领取奖励
-- questId: 任务id
function QuestServer:IsQuestRewarded(questId)
    local questInfo = self._questId2QuestDict[questId]
    if questInfo then
        return questInfo.state == QuestState.Rewarded
    end
    return false
end

-- 获取SOL任务进行中任务目标
function QuestServer:GetAllSOLUnderwayQuest()
    if self._solUnderwayQuest and not Facade.GameFlowManager:CheckIsInFrontEnd() then
        return self._solUnderwayQuest
    end
    local underwayQuest, trackingObjectiveIds = self:_GetAllSOLUnderwayQuestForce()
    return underwayQuest, trackingObjectiveIds
end

function QuestServer:_GetAllSOLUnderwayQuestForce()
    local underwayQuest = {}
    local trackingObjectiveIds = {}
    for _, questInfo in pairs(self._questId2QuestDict) do
        if
            (questInfo.state == QuestState.Accepted or questInfo.state == QuestState.Failed) and
                questInfo:IsQuestNeedShow()
         then
            table.insert(underwayQuest, questInfo)
            if #questInfo.trackingObjectivesId > 0 then
                table.append(trackingObjectiveIds, questInfo.trackingObjectivesId)
            end
        end
    end
    return underwayQuest, trackingObjectiveIds
end

-- 获取指定地图的可追踪任务目标
function QuestServer:GetSOLTrackingQuestObjectivesByMapId(mapId)
    local underwayObjectives = {}
    local underwayQuests = self:GetAllSOLUnderwayQuest()

    if #underwayQuests > 0 then
        table.sort(
            underwayQuests,
            function(q1, q2)
                return q1.acceptTime > q2.acceptTime
            end
        )
        local untrackableObjective = {}
        for _, questInfo in ipairs(underwayQuests) do
            for _, objective in ipairs(questInfo:GetQusetObjectives()) do
                if objective.bIsFinsih == false and objective.ownerQuestInfo.state == QuestState.Accepted then
                    for _, id in ipairs(objective.mapId) do
                        if mapId == id then
                            if objective.bShowTracking then
                                table.insert(underwayObjectives, objective)
                            else
                                table.insert(untrackableObjective, objective)
                            end
                            break
                        end
                    end
                end
            end
        end
        for _, objective in ipairs(untrackableObjective) do
            table.insert(underwayObjectives, objective)
        end
    end
    return underwayObjectives
end

-- 获取指定地图的含可追踪任务目标的任务
function QuestServer:GetSOLTrackingQuestByMapId(mapId)
    local trackableQuests = {}
    local underwayQuests = self:GetAllSOLUnderwayQuest()

    if #underwayQuests > 0 then
        table.sort(
            underwayQuests,
            function(q1, q2)
                return q1.acceptTime < q2.acceptTime
            end
        )
        for _, questInfo in ipairs(underwayQuests) do
            local trackableQuest = false
            if questInfo.state == QuestState.Accepted then
                for _, objective in ipairs(questInfo:GetQusetObjectives()) do
                    if objective.bIsFinsih == false then
                        for _, id in ipairs(objective.mapId) do
                            if mapId == id then
                                trackableQuest = true
                                break
                            end
                        end
                    end
                end
                if trackableQuest then
                    table.insert(trackableQuests, questInfo)
                end
            end
        end
    end
    return trackableQuests
end

-- 是否任务道具（进行中的带道具交互的任务目标中的道具）
-- itemId: number
-- bindType: PropBindingType
function QuestServer:IsSOLQuestItem(itemId, bindType)
    if itemId == nil then
        return false
    end
    local underwayQuests = self:GetAllSOLUnderwayQuest()
    bindType = setdefault(bindType, PropBindingType.BindingBinded)
    for _, questInfo in ipairs(underwayQuests) do
        for _, objective in ipairs(questInfo:GetQusetObjectives()) do
            if not objective.bIsFinsih and objective:IsObjectiveItem(itemId, bindType) then
                return true
            end
        end
    end

    return false
end

-- 是否任务道具（除去任务类型19局内拾取道具）
-- itemId: number
-- bindType: PropBindingType
function QuestServer:IsSOLQuestItemButNotDSPickUpProp(itemId, bindType)
    if itemId == nil then
        return false
    end
    bindType = setdefault(bindType, PropBindingType.BindingBinded)
    local underwayQuests = self:GetAllSOLUnderwayQuest()
    for _, questInfo in ipairs(underwayQuests) do
        for _, objective in ipairs(questInfo:GetQusetObjectives()) do
            if
                not objective.bIsFinsih and objective.type ~= QuestObjectiveType.DSPickUpProp and
                    objective:IsObjectiveItem(itemId, bindType)
             then
                return true
            end
        end
    end

    return false
end

--#endregion
-----------------------------------------------------------------------

--- BEGIN MODIFICATION @ VIRTUOS: 实现PS5 Activity相关内容
function QuestServer:_SyncPS5Activities()
    -- 只有PS5需要activity
    if PLATFORM_PS5 ~= 1 then
        return
    end

    -- 目前activity只能使用 start和end来控制状态resume接口已经弃用
    local DFMPlatformActivityManager = UDFMPlatformActivityManager.Get(GetWorld())
    if DFMPlatformActivityManager and DFMPlatformActivityManager.bNeedSyncActivities then
        -- 只需要主线任务
        local questType = setdefault(questType, QuestType.Mission)
        for _, questInfo in pairs(self._questId2QuestDict) do
            if questInfo.type == questType then
                if questInfo.state == QuestState.Accepted then
                    DFMPlatformActivityManager:StartActivity(questInfo.id)
                end

                if questInfo.state == QuestState.Rewarded then
                    DFMPlatformActivityManager:CompleteActivity(questInfo.id)
                end
            end
        end
        DFMPlatformActivityManager.bNeedSyncActivities = false
    end
end
--- END MODIFICATION

-----------------------------------------------------------------------
--region 
function QuestServer:_OpenLevelAchieved(seasonLevel, preSeasonLevel, bNotify)
    self:_UpdateQuestLine(seasonLevel, bNotify)
    self.Events.evtUpdateSeasonLevel:Invoke()
end

function QuestServer:GetAllQuestsByQuestLine(questLineInfo)
    local list = {}
    if questLineInfo and questLineInfo:IsLineOpend() then
        for _, questInfo in pairs(self._questId2QuestDict) do
            if questInfo:GetQuestLine() == questLineInfo.questLineId then
                table.insert(list, questInfo)
            end
        end
        table.sort(
            list,
            function(a, b)
                return a.id < b.id
            end
        )
    end
    return list
end

function QuestServer:_UpdateQuestLine(seasonLevel, bNotify)
    local openLockedQuestLines = {}
    for key, value in pairs(self._solQuestLineId2QuestLineDict) do
        local lastBLineOpend = value.bLineOpened
        local bQuestLineOpen = value:IsLineOpend()
        if bQuestLineOpen and not lastBLineOpend then
            if bNotify then
                self.Events.evtCacheLockedQuestLineOpen:Invoke(value) -- 缓存lockedquestlineopen
            end
            table.insert(openLockedQuestLines, value)
        end
    end

    if #openLockedQuestLines > 0 then
        self.Events.evtLockedQuestLineOpen:Invoke(openLockedQuestLines) -- 触发更新questline
    end
end

function QuestServer:GetQuestLineInfosByQuestType(questType)
    local questLines = {}
    for key, value in pairs(self._solQuestLineId2QuestLineDict) do
        if value.questType == questType then
            table.insert(questLines, value)
        end
    end
    if #questLines > 0 then
        table.sort(
            questLines,
            function(ql1, ql2)
                return ql1.questLineId < ql2.questLineId
            end
        )
    end
    return questLines
end

--获取任务线信息
function QuestServer:GetQuestLineInfoById(questLineId)
    if questLineId then
        return self._solQuestLineId2QuestLineDict[questLineId]
    end
    return nil
end

-- 返回任务所在任务线信息
function QuestServer:GetQuestLineInfoByQuest(questInfo)
    local questLineId = questInfo:GetQuestLine()
    local questLineInfo = self:GetQuestLineInfoById(questLineId)
    return questLineInfo
end

-- 获取特定类型任务中显示Tips（已完成\可接受）的任务
-- questType：任务类型，主线，支线
function QuestServer:GetNotifyQuestByQuestType(questType)
    local questType = setdefault(questType, QuestType.Mission)
    local qustLineInfos = self:GetQuestLineInfosByQuestType(questType)
    for _, lineInfo in ipairs(qustLineInfos) do
        local notifyQuests = self:GetNotifyQuestInQuestLine(lineInfo)
        if type(notifyQuests) == "table" and #notifyQuests > 0 then
            return notifyQuests
        end
    end
    return {}
end


-- 获取任务线中的显示Tips（已完成\可接受）的任务，返回的notifyQuest里最多有一个已完成和最多一个待接取任务
function QuestServer:GetNotifyQuestInQuestLine(questLineInfo)
    if questLineInfo == nil then
        return false
    end
    local ret = false
    if questLineInfo and questLineInfo:IsLineOpend() and questLineInfo.bUnopenShow then
        local list = self:GetAllQuestsByQuestLine(questLineInfo)
        for _, questInfo in ipairs(list) do
            local bQuestAcceptable = self:IsQuestAcceptable(questInfo)
            if QuestState.Completed == questInfo.state or bQuestAcceptable then
                ret = true
                break
            end
        end
    end
    return ret
end
--endregion
-----------------------------------------------------------------------


-----------------------------------------------------------------------
--region 赛季信息
-- 请求赛季信息
function QuestServer:GetQuestLineSeasonDataReq()
    logwarning("GetQuestLineSeasonDataReq")
    local callback = function(res)
        if res.result == 0 then
            logwarning("GetQuestLineSeasonDataReq_End")
            self.curSeasonId = res.cur_season_id
            for key, value in pairs(self._solSeasonQuestLineId2LineDict) do
                if #value.table_SeasonIdAry > 0 then
                    value:UpdateQuestLineData(res.cur_season_id, res.season_info, res.server_time_stamp)
                end
            end
            self.Events.evtSeasonInfoUpdate:Invoke()
            if self._questFactData == nil then
                self._questFactData = QuestFactStruct:New()
            end
            self._questFactData:InitFact()
        end
    end
    local req = pb.CSQuestGetQuestLineExtraInfoReq:New()
    req:Request(callback)
end

function QuestServer:GetSeasonLineData(lineID)
    return self._solSeasonQuestLineId2LineDict[lineID]
end

function QuestServer:GetCurrentSeasonLine()
    for index, value in pairs(self._solSeasonQuestLineId2LineDict) do
        if value:IsCurSeasonLine() then 
            return value
        end
    end
    logerror("No Current Season Line Availiable")
    return nil
end

function QuestServer:GetCurrentSeasonStartTime()
    local curSeasonLine = self:GetCurrentSeasonLine()
    if curSeasonLine then
        return curSeasonLine:GetSeasonStartTime()
    end
    return 0
end

--手动请求刷新收集者
function QuestServer:CollectorTaskRefreshReq()
    local callback = function(res)
        if res.result == 0 then
            logwarning("CollectorTaskRefreshReq_End")
            if self._questCollectorData == nil then
                self._questCollectorData = QuestCollectorStruct:New()
            end
            if res.collector_data then
                self._questCollectorData:UpdateCollectorData(res.collector_data)
            end
            self.Events.evtSeasonCollectorRefresh:Invoke()
        end
    end
    local req = pb.CSQuestCollectorTaskRefreshReq:New()
    req:Request(callback)
end

--收集者锁定
function QuestServer:CollectorTaskSetStatusReq(collectorId, bLock)
    local callback = function(res)
        if res.result == 0 then
            logwarning("CollectorTaskSetStatusReq_End")
            self._questCollectorData:UpdateCollectorTaskLock(res.collector_id, res.status)
        else
            logwarning("CollectorTaskRefreshReq_Failed")
            self.Events.evtSeasonCollectorLockRefresh:Invoke(collectorId, false)
        end
    end
    local req = pb.CSQuestCollectorSetStatusReq:New()
    req.collector_id = collectorId
    req.status = bLock
    req:Request(callback, {bBuildEmptyRecv = false, bEnableHighFrequency = true})
end

--收集者提交请求
function QuestServer:CollectorTaskSubmitReq(collectorId, submitItemInfoList)
    local callback = function(res)
        if res.result == 0 then
            if res.collector_data then
                self._questCollectorData:UpdateCollectorData(res.collector_data)
            end
            self.Events.evtSeasonCollectorRefresh:Invoke()
        end
    end

    local req = pb.CSQuestCollectorSubmitReq:New()
    req.collector_id = collectorId
    req.submit_list = {{obj_id = collectorId, submit_props = submitItemInfoList}}
    req:Request(callback)
end

function QuestServer:CollectorTaskRewardsReq(rewardId, fCallback)
    local callback = function(res)
        if res.result == 0 then
            if fCallback then
                fCallback(res.is_send_prop_by_mail, res.is_currency_sent_by_mail, res.deposit_change)
            end

            self._questCollectorData:UpdateCollectorRewardInfo(res.already_take_rewards)
            self.Events.evtSeasonCollectorRewardRefresh:Invoke()
        end
    end
    local req = pb.CSQuestCollectorTakeRewardReq:New()
    req.reward_id = rewardId
    req:Request(callback)
end

function QuestServer:GetCollectorDataInfo()
    return self._questCollectorData
end
--endregion

--#region 命运契约
function QuestServer:QuestFateContractAcceptReq(contractId, fCallback)
    local callback = function(res)
        if res.result == 0 then
           
        end
    end
    local req = pb.CSQuestFateContractAcceptReq:New()
    req.contract_id = contractId
    req:Request(callback)
end
--#endregion

function QuestServer:GetOvertimeReward(fCallback)
    local callback = function(res)
        if res == 1 then
            self._bIsTakeOvertimeReward = true
            self.Events.evtSeasonOvertimeRewardTaken:Invoke()
            if fCallback then
                fCallback(res.deposit_change)
            end
        end
    end
    local req = pb.CSQuestTakeOverTimeRewardReq:New()
    req:Request(callback)
end

function QuestServer:GetNotifyQuestInSeasonGroup(groupId)
    if groupId == nil then
        return false
    end
    local ret = false
    local lineInfo = self:GetCurrentSeasonLine()
    
    if lineInfo == nil then 
        return false
    end

    if lineInfo:IsUnlockByGroupId(groupId) then
        local questlist = lineInfo:GetAllQuestIdsByGroupID(groupId)
        for _, questID in pairs(questlist) do
            local questInfo = self:GetQuestInfoById(questID)
            if questInfo ~= nil then
                local bQuestAcceptable = self:IsQuestAcceptable(questInfo)
                if QuestState.Completed == questInfo.state or bQuestAcceptable then
                    ret = true
                    break
                end
            end
        end
    end

    return ret
end

function QuestServer:GetNotifyQuestInSeasonStage(stageId)
    
    if stageId == nil then
        return false
    end

    local ret = false
    local lineInfo = self:GetCurrentSeasonLine()

    if lineInfo == nil then
        return false
    end

    if lineInfo:IsUnLockByStageID(stageId) then
        local groupList = lineInfo:GetAllGroupIDsByStageID(stageId)
        for _, groupId in pairs(groupList) do
            if self:GetNotifyQuestInSeasonGroup(groupId) then
                ret = true
                break
            end
        end
    end

    return ret
end

function QuestServer:GetNotifyQuestInSeasonLine(lineInfo)
    local stagelist = lineInfo:GetStageIDList()
    local needRet = false
    if stagelist ~= nil then
        for key, value in pairs(stagelist) do
            if Server.QuestServer:GetNotifyQuestInSeasonStage(value) then
                needRet = true
                break
            end
        end
    end
    return needRet
end

return QuestServer
