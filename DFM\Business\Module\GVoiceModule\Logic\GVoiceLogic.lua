----- LOG FUNCTION AUTO GENERATE -----------
local GVoiceLogInfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGVoice)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class GVoiceLogic : FieldBase
local GVoiceLogic = {}
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local EBreakthroughStage = import("EBreakthroughStage")
local UGameplayStatics = import "GameplayStatics"
local DFMGameVoice = import "DFMGameVoice"
local _gvoiceManager = DFMGameVoice.Get(GetGameInstance())
local GPAudioStatics = import "GPAudioStatics"
local Luautils = import "Luautils"
local DFPermissionType = import "EDFPermissionType"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

local _isGVoiceEnable = true        --功能是否可用
local _isGVoiceSdkEnable = nil      --组件本身是否可用
local _isSdkLogEnable = true        --是否开启sdk日志 （sdk侧的日志，正式版本也要关闭）
local _isDebugLogEnable = true      --是否开启调试日志 （仅调试bug开启）
local _isInfoLogEnable = true       --是否开启普通日志 （仅开发版本开启，正式版本需要关闭）

local _accountPunishEndTime = 0     --文明发言处罚结束时间

local _defaultMemberVolume = 80

local _inGameFirstSpeakTime = 0         --局内首次说话的时间
local _inGameLastSpeakTime = 0          --局内上次说话的时间
local _inGameLastSpeakChannel = 0       --局内上次说话的频道
local _inGameSpeakDurations = {}        --局内各个频道持续的时间
local _inGameSpeakDurationFlag = false  --是否计时
local _bPressMic = false                --是否按住说话状态
local _bLastPressMic = false            --之前是否按住说话状态

local _bCampChannelEnable = false
local _CampChannelEnableCache = nil
local _curStage = 0

local DEFAULT_VOLUME = 100

local EGVoiceEvent = {
    kEventNoDeviceConnected               = 0,                   -- not any device is connected
    kEventHeadsetDisconnected             = 10,                  -- a headset device is connected
    kEventHeadsetConnected                = 11,                  -- a headset device is disconnected
    kEventBluetoothHeadsetDisconnected    = 20,                  -- a bluetooth device is connected
    kEventBluetoothHeadsetConnected       = 21,                  -- a bluetooth device is disconnected
    kEventMicStateOpenSucc                = 30,                  -- open microphone
    kEventMicStateOpenErr                 = 31,                  -- open microphone
    kEventMicStateNoOpen                  = 32,                  -- close micrphone
    kEventMicStateOccupancy               = 33,                  -- indicates the microphone has been occupancyed by others
    kEventSpeakerStateOpenSucc            = 40,                  -- open speaker
    kEventSpeakerStateOpenErr             = 41,                  -- open speaker error
    kEventSpeakerStateNoOpen              = 42,                  -- close speaker
    kEventAudioInterruptBegin             = 50,                  -- audio device begin to be interrupted
    kEventAudioInterruptEnd               = 51,                  -- audio device end to be interrupted
    kEventAudioRecoderException           = 52,                  -- indicates the recorder thread throws a exception, maybe you can resume the audio
    kEventAudioRenderException            = 53,                  -- indicates the render thread throws a exception, maybe you can resume the audio
    kEventPhoneCallPickUp                 = 54,                  -- indicates that you picked up the phone
    kEventPhoneCallHangUp                 = 55,                  -- indicates that you hanged up the phone
}

local function GVoiceLogDebug(...)
    if _isDebugLogEnable then
        loginfo("GVoiceLogic.debug ", ...)
    end
end

local function GVoiceLogInfo(...)
    if _isInfoLogEnable then
        loginfo("GVoiceLogic.info ", ...)
    end
end

function GVoiceLogic.IsEnable()
    if _gvoiceManager == nil then
        return false
    end
    if _isGVoiceSdkEnable == nil then
        _isGVoiceSdkEnable = _gvoiceManager:IsSdkEnable()
    end
    return (_isGVoiceEnable and _isGVoiceSdkEnable)
end

function GVoiceLogic.IsDebugMode()
    return false
end

---InGameController
---@return InGameController
local function GetInGameController()
    return InGameController:Get()
end

-----------------------------------------------------------------------
--region 初始化相关

function GVoiceLogic.OpenGVoicePlayByWwise()
    GVoiceLogic.Invoke(8618, 1, 0)
    GVoiceLogic.OpenAvPlay()
end

function GVoiceLogic.CloseGVoicePlayByWwise()
    GVoiceLogic.Invoke(8618, 0, 0)
    GVoiceLogic.StopAvPlay()
end

function GVoiceLogic.OpenAvPlay()
    -- 开扬声器时 调用 (现在最新接口只用在初始化调用即可)
    GVoiceLogInfo("GVoiceLogic.OpenAvPlay")
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.avPlay)
end

function GVoiceLogic.StopAvPlay()
    -- 关扬声器时 调用
    GVoiceLogInfo("GVoiceLogic.StopAvPlay")
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.avStop)
end

function GVoiceLogic.Invoke(nCmd, nParam1, nParam2, pOutput)
    if _gvoiceManager == nil then
        return
    end
    if pOutput then
        _gvoiceManager:InvokeWithStr(nCmd, nParam1, nParam2, pOutput)
    else
        _gvoiceManager:Invoke(nCmd, nParam1, nParam2)
    end
end

function GVoiceLogic.OnOpenLobby()
    GVoiceLogInfo("GVoiceLogic.OnOpenLobby")
    Server.AccountServer:ReqAccountAllowRealTimeVoice()
    --避免局内残留
    GVoiceLogic.QuitRoomByType(Module.GVoice.Config.EGVoiceRoomType.InGame)
    if Server.AccountServer:IsInTeam() then
        GVoiceLogic._JoinLobbyRoom()
    end
end

function GVoiceLogic.OnQuitGame()
    GVoiceLogInfo("GVoiceLogic.OnQuitGame")
    GVoiceLogic._UnregisterInGameCppEvent()
    --GVoiceLogic.QuitAllRoom()
    GVoiceLogic._QuitInGameRoom()
    GVoiceLogic._InGameSpeakDurationFinish()
    GVoiceLogic.ResetCampButtonTypeToNarmal()
    Module.GVoice.Field:ResetIdentity()
end

function GVoiceLogic.OnEnterGame()
    GVoiceLogInfo("GVoiceLogic.OnEnterGame")
    GVoiceLogic._RegisterInGameCppEvent()
    if GVoiceLogic.IsDebugMode() then
        Timer.DelayCall(
            3,
            function()
                local playerState = Facade.GameFlowManager:GetPlayerState()
                GVoiceLogic.DebugLocalDS(playerState.Uin)
            end
        )
        -- GVoiceLogic.timeHandleTick = Timer:NewIns(0.5, 0)
        -- GVoiceLogic.timeHandleTick:AddListener(GVoiceLogic.OnTick, self)
        -- GVoiceLogic.timeHandleTick:Start()
    end
    --Module.GVoice.Field:ResetRoomMemberVolume()
    GVoiceLogic._JoinInGameRoom()
    GVoiceLogic._InGameSpeakDurationStart()
end

function GVoiceLogic.OnBackToLogin()
    GVoiceLogInfo("GVoiceLogic.OnBackToLogin")
    GVoiceLogic.QuitAllRoom()
end

---初始化GVoice引擎
function GVoiceLogic._InitVoiceEngine()
    GVoiceLogInfo("_InitVoiceEngine:", Server.SDKInfoServer:GetOpenIdStr())
    if _gvoiceManager == nil then
        logwarning("GVoiceLogic._InitVoiceEngine GVoice _gvoiceManager is nil")
        return
    end
    if not GPAudioStatics.bGVoiceInit() then
        logwarning("GVoiceLogic._InitVoiceEngine GVoice bGVoiceInit not enable")
        return
    end
    if not GVoiceLogic.IsEnable() then
        logwarning("GVoiceLogic._InitVoiceEngine GVoice _isGVoiceEnable %s _isGVoiceSdkEnable %s", tostring(_isGVoiceEnable), tostring(_isGVoiceSdkEnable))
        return
    end
    _gvoiceManager.bUseTicker = true
    _gvoiceManager:SetGVoiceURL(Server.SDKInfoServer:GetGVoiceURL())
    _gvoiceManager:InitVoiceEngine(Server.SDKInfoServer:GetOpenIdStr())
    _gvoiceManager:SetVoiceLog(_isSdkLogEnable, _isDebugLogEnable, _isInfoLogEnable)
    _gvoiceManager:EnableMultiRoom(true)
    _gvoiceManager:SetDefaultMemberVolume(_defaultMemberVolume)
    GVoiceLogic.OpenGVoicePlayByWwise()
    --local state = _gvoiceManager:GetAudioDeviceConnectionState()
    --GVoiceLogInfo("GVoiceLogic._InitVoiceEngine GetAudioDeviceConnectionState:", state)
    --如果是蓝牙
    -- if Module.GVoice.Config.EDeviceConnectionState.BlueTooth == state then
    --     _gvoiceManager:EnableBluetoothSCO(false)
    -- end
    GVoiceLogic._SetupSettingButtonType()
    GVoiceLogic._SetupSettingVolume()
    GVoiceLogic._InitAllDeviceCount()
end

function GVoiceLogic.DebugLocalDS(openId)
    GVoiceLogInfo("GVoiceLogic.DebugLocalDS openId:", openId)
    GVoiceLogic._InitVoiceEngine_Debug(openId)
    local roomName = "DF_GVOICE_TEST"
    local roomChannel = EGVoiceRoomChannel.All
    local roomType = Module.GVoice.Config.EGVoiceRoomType.InGame
    GVoiceLogic.JoinRoom(roomName, roomChannel, roomType)
end

function GVoiceLogic._InitVoiceEngine_Debug(id)
    GVoiceLogInfo("GVoiceLogic._InitVoiceEngine_Debug")
    if _gvoiceManager == nil then
        logwarning("GVoiceLogic._InitVoiceEngine_Debug GVoice _gvoiceManager is nil")
        return
    end
    if not GPAudioStatics.bGVoiceInit() then
        logwarning("GVoiceLogic._InitVoiceEngine_Debug GVoice bGVoiceInit not enable")
        return
    end
    if not GVoiceLogic.IsEnable() then
        logwarning("_GVoiceLogic.InitVoiceEngine_Debug GVoice _isGVoiceEnable %s _isGVoiceSdkEnable %s", tostring(_isGVoiceEnable), tostring(_isGVoiceSdkEnable))
        return
    end
    
    _gvoiceManager.bUseTicker = true
    _gvoiceManager:InitVoiceEngine(tostring(id))
    _gvoiceManager:SetVoiceLog(true, true, true)
    _gvoiceManager:EnableMultiRoom(true)
    _gvoiceManager:SetDefaultMemberVolume(_defaultMemberVolume)
    GVoiceLogic._SetupSettingButtonType()
    GVoiceLogic._SetupSettingVolume()
    GVoiceLogic._InitAllDeviceCount()
end

---有的openId是数字，lua中是int64, 直接转字符串会是越界的结果
function GVoiceLogic.OpenIdToString(openId)
    if type(openId) == "number" then
        openId = Luautils.GetOverInt64String(openId)
    end
    return openId
end

--endregion
-----------------------------------------------------------------------

--#region C++相关事件

function GVoiceLogic._RegisterCppEvent()
    if _gvoiceManager == nil then
        return
    end
    GVoiceLogInfo("GVoiceLogic._RegisterCppEvent")
    ---------------------实时语音------------------------
    --_gvoiceManager.OnGVoiceRoomStateChangeDelegate:Clear()
    _gvoiceManager.OnGVoiceRoomStateChangeDelegate:Add(GVoiceLogic._OnGVoiceRoomStateChange)
    --_gvoiceManager.OnGVoiceSpeakStateChangeDelegate:Clear()
    _gvoiceManager.OnGVoiceSpeakStateChangeDelegate:Add(GVoiceLogic._OnGVoiceSpeakStateChange)
    --_gvoiceManager.OnGVoiceRoomMemberRoomStateChangeDelegate:Clear()
    _gvoiceManager.OnGVoiceRoomMemberRoomStateChangeDelegate:Add(GVoiceLogic._OnGVoiceRoomMemberRoomStateChange)
    --_gvoiceManager.OnGVoiceRoomMemberSpeakStateChangeDelegate:Clear()
    _gvoiceManager.OnGVoiceRoomMemberSpeakStateChangeDelegate:Add(GVoiceLogic._OnGVoiceRoomMemberSpeakStateChange)
    _gvoiceManager.OnGVoiceEventDelegate:Clear()
    _gvoiceManager.OnGVoiceEventDelegate:Add(GVoiceLogic._OnGVoiceEvent)
    ---------------------离线语音------------------------
    --_gvoiceManager.OnGVoiceSpeechToTextDelegate:Clear()   --暂时好像没用到
    --_gvoiceManager.OnGVoiceSpeechToTextDelegate:Add(GVoiceLogic._OnGVoiceSpeechToText)
    --_gvoiceManager.OnGVoiceStreamSpeechToTextDelegate:Clear()
    _gvoiceManager.OnGVoiceStreamSpeechToTextDelegate:Add(GVoiceLogic._OnGVoiceStreamSpeechToText)
    --_gvoiceManager.OnGVoiceUploadVoiceFileDelegate:Clear()
    _gvoiceManager.OnGVoiceUploadVoiceFileDelegate:Add(GVoiceLogic._OnGVoiceUploadVoiceFile)
    --_gvoiceManager.OnGVoiceDownloadVoiceFileDelegate:Clear()
    _gvoiceManager.OnGVoiceDownloadVoiceFileDelegate:Add(GVoiceLogic._OnGVoiceDownloadVoiceFile)
    --_gvoiceManager.OnPlayRecordedFileDelegate:Clear()
    _gvoiceManager.OnPlayRecordedFileDelegate:Add(GVoiceLogic._OnPlayRecordedFileDelegate)
    --_gvoiceManager.OnGVoiceReportPlayerDelegate:Clear()
    _gvoiceManager.OnGVoiceReportPlayerDelegate:Add(GVoiceLogic._OnReportPlayer)
    --_gvoiceManager.OnGVoiceDeviceDelegate:Clear()
    _gvoiceManager.OnGVoiceDeviceDelegate:Add(GVoiceLogic._OnGVoiceDevice)
    --_gvoiceManager.OnGVoiceGetDeviceCountDelegate:Clear()
    _gvoiceManager.OnGVoiceGetDeviceCountDelegate:Add(GVoiceLogic._OnGVoiceGetDeviceCount)
    --_gvoiceManager.OnGVoiceTextTranslateDelegate:Clear()
    _gvoiceManager.OnGVoiceTextTranslateDelegate:Add(GVoiceLogic._OnTextTranslated)
    ---------------------系统事件------------------------
    --_gvoiceManager.OnGVoiceApplicationActiveDelegate:Clear()
    _gvoiceManager.OnGVoiceApplicationActiveDelegate:Add(GVoiceLogic._OnGVoiceApplicationActive)
end

function GVoiceLogic._UnregisterCppEvent()
    if _gvoiceManager == nil then
        return
    end
    GVoiceLogInfo("GVoiceLogic._UnregisterCppEvent")
    ---------------------实时语音------------------------
    _gvoiceManager.OnGVoiceRoomStateChangeDelegate:Remove(GVoiceLogic._OnGVoiceRoomStateChange)
    _gvoiceManager.OnGVoiceSpeakStateChangeDelegate:Remove(GVoiceLogic._OnGVoiceSpeakStateChange)
    _gvoiceManager.OnGVoiceRoomMemberRoomStateChangeDelegate:Remove(GVoiceLogic._OnGVoiceRoomMemberRoomStateChange)
    _gvoiceManager.OnGVoiceRoomMemberSpeakStateChangeDelegate:Remove(GVoiceLogic._OnGVoiceRoomMemberSpeakStateChange)
    _gvoiceManager.OnGVoiceEventDelegate:Remove(GVoiceLogic._OnGVoiceEvent)
    ---------------------离线语音------------------------
    _gvoiceManager.OnGVoiceStreamSpeechToTextDelegate:Remove(GVoiceLogic._OnGVoiceStreamSpeechToText)
    _gvoiceManager.OnGVoiceUploadVoiceFileDelegate:Remove(GVoiceLogic._OnGVoiceUploadVoiceFile)
    _gvoiceManager.OnGVoiceDownloadVoiceFileDelegate:Remove(GVoiceLogic._OnGVoiceDownloadVoiceFile)
    _gvoiceManager.OnPlayRecordedFileDelegate:Remove(GVoiceLogic._OnPlayRecordedFileDelegate)
    _gvoiceManager.OnGVoiceReportPlayerDelegate:Remove(GVoiceLogic._OnReportPlayer)
    _gvoiceManager.OnGVoiceDeviceDelegate:Remove(GVoiceLogic._OnGVoiceDevice)
    _gvoiceManager.OnGVoiceGetDeviceCountDelegate:Remove(GVoiceLogic._OnGVoiceGetDeviceCount)
    _gvoiceManager.OnGVoiceTextTranslateDelegate:Remove(GVoiceLogic._OnTextTranslated)
    ---------------------系统事件------------------------
    _gvoiceManager.OnGVoiceApplicationActiveDelegate:Remove(GVoiceLogic._OnGVoiceApplicationActive)
end

function GVoiceLogic._RegisterInGameCppEvent()
    GVoiceLogInfo("GVoiceLogic._RegisterInGameCppEvent")
    UGPGameplayDelegates.Get(GetWorld()).OnPlayerStateMemberInfoListLengthChanged:Add(GVoiceLogic._OnPlayerStateMemberInfoListLengthChanged)
    UGPGameplayDelegates.Get(GetWorld()).OnTeamIdentityReplicate:Add(GVoiceLogic._OnTeamIdentityReplicate)
    UDFMGameplayDelegates.Get(GetWorld()).OnCommanderChannelVoiceAuthorityChanged:Add(GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged)
    local gameState = GetInGameController():GetGameState()
    if gameState ~= nil and gameState.onCurStage ~= nil then
        gameState.onCurStage:Add(GVoiceLogic._OnCurStageChanged)
    end
end

function GVoiceLogic._UnregisterInGameCppEvent()
    GVoiceLogInfo("GVoiceLogic._UnregisterInGameCppEvent")
    UGPGameplayDelegates.Get(GetWorld()).OnPlayerStateMemberInfoListLengthChanged:Remove(GVoiceLogic._OnPlayerStateMemberInfoListLengthChanged)
    UGPGameplayDelegates.Get(GetWorld()).OnTeamIdentityReplicate:Remove(GVoiceLogic._OnTeamIdentityReplicate)
    UDFMGameplayDelegates.Get(GetWorld()).OnCommanderChannelVoiceAuthorityChanged:Remove(GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged)
    local gameState = GetInGameController():GetGameState()
    if gameState ~= nil and gameState.onCurStage ~= nil then
        gameState.onCurStage:Remove(GVoiceLogic._OnCurStageChanged)
    end
end


---进退语音房间的事件（玩家自己）
---@param roomName string 房间名称
---@param roomChannel number 房间频道
---@param bJoin boolean true-进房 false-退房
function GVoiceLogic._OnGVoiceRoomStateChange(roomName, roomChannel, bJoin)
    Module.GVoice.Config.Events.evtGVoiceRoomStateChanage:Invoke(roomName, roomChannel, bJoin)
    if roomChannel ~= EGVoiceRoomChannel.Moss then
        if not bJoin and _gvoiceManager and not _gvoiceManager:IsInAnyRoom() then
            GVoiceLogic.CloseSpeakerListPanel()
            Module.GVoice.Field:ResetSpeakerState()
        end
    end
    GVoiceLogic._OnJoinInGameRoom_All(roomName, roomChannel, bJoin)
end

---说话状态变化事件（玩家自己）
---@param isSpeaking boolean true-在说话 false-没说话
function GVoiceLogic._OnGVoiceSpeakStateChange(isSpeaking)
    Module.GVoice.Config.Events.evtGVoiceSpeakStateChange:Invoke(isSpeaking)
end

---其他玩家房间状态变化事件
---@param roomName string 房间名称
---@param roomChannel number 房间频道
---@param openId string 玩家ID
---@param action number 玩家行为：进房-kCompleteCodeRoomMemberInRoom、退房-kCompleteCodeRoomMemberOutRoom、开麦-kCompleteCodeRoomMemberMicOpen、关麦-kCompleteCodeRoomMemberMicClose
function GVoiceLogic._OnGVoiceRoomMemberRoomStateChange(roomName, roomChannel, openId, action)
    Module.GVoice.Config.Events.evtGVoiceRoomMemberRoomStateChange:Invoke(roomName, roomChannel, openId, action)
    -- BEGIN MODIFICATION - VIRTUOS
    if IsConsole() and action == EGVoiceRoomMemberState.kCompleteCodeRoomMemberInRoom then
        -- 在这里处理玩家在语音房内时，离线后又重新登陆的情况
        if openId ~= Server.AccountServer:GetPlayerId() then
            -- 先将玩家静音，等待权限检查结果
            GVoiceLogic.SetPlayerMuteState(openId, true)
            GVoiceLogic._CheckVoicePermissionWithPlayer(ULuautils.GetUInt64StrToInt64(openId), function(res)
                if res then
                    -- 允许与目标玩家通信，解除静音
                    GVoiceLogic.SetPlayerMuteState(openId, false)
                end
            end)
        end
    end
    -- END MODIFICATION - VIRTUOS
    if action == EGVoiceRoomMemberState.kCompleteCodeRoomMemberOutRoom then
        GVoiceLogic._OnGVoiceRoomMemberSpeakStateChange(roomName, roomChannel, openId, false)
    elseif action == EGVoiceRoomMemberState.kCompleteCodeRoomMemberInRoom then
        GVoiceLogic._CheckForibidRoomMembersVoice(roomName, roomChannel, openId)
    end
    if action == EGVoiceRoomMemberState.kCompleteCodeRoomMemberMicOpen or action == EGVoiceRoomMemberState.kCompleteCodeRoomMemberMicClose then
        Module.GVoice.Field:SetRoomMemberMicroState(openId, action)
    end
end

---其他玩家说话状态变化事件
---@param roomName string 房间名称
---@param roomChannel number 房间频道
---@param openId string 玩家ID
---@param isSpeaking boolean 是否在说话 true-在说话 false-没说话
function GVoiceLogic._OnGVoiceRoomMemberSpeakStateChange(roomName, roomChannel, openId, isSpeaking)
    Module.GVoice.Config.Events.evtGVoiceRoomMemberSpeakStateChange:Invoke(roomName, roomChannel, openId, isSpeaking)
    if roomChannel ~= EGVoiceRoomChannel.Moss then
        GVoiceLogic.UpdateSpeakerListPanel(roomName, roomChannel, openId, isSpeaking)
    end
end

---GVoice硬件事件
---@param eventId any EventId：GCloud::GVoice::Event
---@param info any
function GVoiceLogic._OnGVoiceEvent(eventId, info)
    if eventId == EGVoiceEvent.kEventMicStateOpenSucc then
        --GPAudioStatics.SetDolbyPluginEnable(false)
        Module.GVoice.Config.Events.evtMicrophoneDeviceChange:Invoke(true)
    elseif eventId == EGVoiceEvent.kEventMicStateNoOpen then
        --GPAudioStatics.SetDolbyPluginEnable(true)
        Module.GVoice.Config.Events.evtMicrophoneDeviceChange:Invoke(false)
    elseif eventId == EGVoiceEvent.kEventSpeakerStateOpenSucc then

    elseif eventId == EGVoiceEvent.kEventSpeakerStateNoOpen then

    elseif eventId == EGVoiceEvent.kEventBluetoothHeadsetConnected then
        GVoiceLogInfo("GVoiceLogic._OnGVoiceEvent kEventBluetoothHeadsetConnected")
        GVoiceLogic._RequestBlueToothPermission()
    end
end

---离线语音转文字完成的回调事件
---@param text string 转换后的文字内容
---@param fileId string GVoice组件使用的录音文件Id
function GVoiceLogic._OnGVoiceSpeechToText(text, fileId)
    Module.GVoice.Config.Events.evtGVoiceSpeechToText:Invoke(text, fileId)
end

---实时录音转文字完成的回调事件
---@param text string 转换后的文字内容
function GVoiceLogic._OnGVoiceStreamSpeechToText(text)
    GVoiceLogInfo("GVoiceLogic._OnGVoiceStreamSpeechToText:", text)
    Module.GVoice.Config.Events.evtGVoiceStreamSpeechToText:Invoke(text)
end

---上传录音文件完成的回调事件
---@param bSuc boolean 是否成功
---@param filePath string 本地录音文件路径
---@param fileId string GVoice组件使用的录音文件Id
function GVoiceLogic._OnGVoiceUploadVoiceFile(bSuc, filePath, fileId)
    GVoiceLogInfo("GVoiceLogic._OnGVoiceUploadVoiceFile:", tostring(bSuc), " filePath:", filePath," fileId:", fileId)
    Module.GVoice.Config.Events.evtGVoiceUploadVoiceFile:Invoke(bSuc, filePath, fileId)
end

---下载录音文件完成的回调事件
---@param bSuc boolean 是否成功
---@param filePath string 本地录音文件路径
---@param fileId string GVoice组件使用的录音文件Id
function GVoiceLogic._OnGVoiceDownloadVoiceFile(bSuc, filePath, fileId)
    Module.GVoice.Config.Events.evtGVoiceDownloadVoiceFile:Invoke(bSuc, filePath, fileId)
end

---播放录音文件完成的回调事件
---@param filePath string 本地录音文件路径
function GVoiceLogic._OnPlayRecordedFileDelegate(filePath)
    Module.GVoice.Config.Events.evtPlayRecordedFileDelegate:Invoke(filePath)
end

--endregion

--region Lua相关事件

function GVoiceLogic.AddListener()
    GVoiceLogInfo("GVoiceLogic.AddListener")
    -- 登录成功
    Module.Login.Config.Events.evtOnLoginSuccess:AddListener(GVoiceLogic._OnLoginSuccess)
    -- 组队相关
    Server.TeamServer.Events.evtTeamCreated:AddListener(GVoiceLogic._OnTeamCreated)
    Server.TeamServer.Events.evtTeamInfosUpdated:AddListener(GVoiceLogic._TeamInfosUpdated)
    Server.TeamServer.Events.evtJoinTeam:AddListener(GVoiceLogic._JoinLobbyRoom)
    Server.TeamServer.Events.evtYouAreKicked:AddListener(GVoiceLogic._OnYouAreKicked)
    Server.TeamServer.Events.evtYouLeaveTeam:AddListener(GVoiceLogic._OnYouLeaveTeam)
    Server.TeamServer.Events.evtTeammateJoined:AddListener(GVoiceLogic._OnTeammateJoined)
    Server.TeamServer.Events.evtTeammateLeft:AddListener(GVoiceLogic._OnTeammateLeft)
    -- 局内
    Server.MatchServer.Events.evtPrepareJoinMatch:AddListener(GVoiceLogic._PrepareJoinMatch)
    -- 重连入局
    Server.MatchServer.Events.evtMatchReconnect:AddListener(GVoiceLogic._OnMatchReconnect)
    -- 断线重连
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(GVoiceLogic._OnRelayConnected)
    -- 处罚相关
    Server.AccountServer.Events.evtOnGVoicePunish:AddListener(GVoiceLogic._OnAccountGVoicePunish)
    -- 注册C++事件
    GVoiceLogic._RegisterCppEvent()
end

function GVoiceLogic.RemoveListener()
    GVoiceLogInfo("GVoiceLogic.RemoveListener")
    Module.Login.Config.Events.evtOnLoginSuccess:RemoveListener(GVoiceLogic._OnLoginSuccess)

    Server.TeamServer.Events.evtTeamCreated:RemoveListener(GVoiceLogic._OnTeamCreated)
    Server.TeamServer.Events.evtTeamInfosUpdated:RemoveListener(GVoiceLogic._TeamInfosUpdated)
    Server.TeamServer.Events.evtJoinTeam:RemoveListener(GVoiceLogic._JoinLobbyRoom)
    Server.TeamServer.Events.evtYouAreKicked:RemoveListener(GVoiceLogic._OnYouAreKicked)
    Server.TeamServer.Events.evtYouLeaveTeam:RemoveListener(GVoiceLogic._OnYouLeaveTeam)
    Server.TeamServer.Events.evtTeammateJoined:RemoveListener(GVoiceLogic._OnTeammateJoined)
    Server.TeamServer.Events.evtTeammateLeft:RemoveListener(GVoiceLogic._OnTeammateLeft)
    Server.MatchServer.Events.evtPrepareJoinMatch:RemoveListener(GVoiceLogic._PrepareJoinMatch)
    Server.MatchServer.Events.evtMatchReconnect:RemoveListener(GVoiceLogic._OnMatchReconnect)

    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(GVoiceLogic._OnRelayConnected)
    Server.AccountServer.Events.evtOnGVoicePunish:RemoveListener(GVoiceLogic._OnAccountGVoicePunish)

    GVoiceLogic._UnregisterCppEvent()
end

function GVoiceLogic._OnLoginSuccess()
    GVoiceLogic._InitVoiceEngine()
end

function GVoiceLogic._OnTeamCreated(teamId)
    GVoiceLogInfo("GVoiceLogic._OnTeamCreated teamId:", teamId)
    GVoiceLogic._JoinLobbyRoom()
end

function GVoiceLogic._TeamInfosUpdated(teamId)
    GVoiceLogInfo("GVoiceLogic._TeamInfosUpdated teamId:", teamId)
    GVoiceLogic._JoinLobbyRoom()
end

function GVoiceLogic._OnYouAreKicked(curTeamId, kickedTeamId)
    GVoiceLogInfo("GVoiceLogic._OnYouAreKicked ", "curTeamId:", tostring(curTeamId)," kickedTeamId:", tostring(kickedTeamId))
    GVoiceLogic._QuitLobbyRoom(kickedTeamId)
end

function GVoiceLogic._OnYouLeaveTeam(curTeamId, leaveTeamId)
    GVoiceLogInfo("GVoiceLogic._OnYouLeaveTeam ", "curTeamId:", tostring(curTeamId)," leaveTeamId:", tostring(leaveTeamId))
    GVoiceLogic._QuitLobbyRoom(leaveTeamId)
end

function GVoiceLogic._OnTeammateJoined(playerId)
    GVoiceLogInfo("GVoiceLogic._OnTeammateJoined ", tostring(playerId), " teamNum:", tostring(Server.TeamServer:GetTeamNum()))
    -- BEGIN MODIFICATION - VIRTUOS
    -- 平台权限验证
    if IsConsole() then
        local memberOpenIdStr = ULuautils.GetOverInt64String(playerId)
        if playerId ~= Server.AccountServer:GetPlayerId() then
            -- 在玩家入队时先将其静音，然后在其加入语音房后进行黑名单以及平台通信权限检测
            -- 如果在加入语音房后再静音，会由于延迟造成短暂的语音通信
            -- 根据黑名单以及平台通信权限验证结果决定是否解除静音
            GVoiceLogic.SetPlayerMuteState(memberOpenIdStr, true)
            GVoiceLogic._CheckVoicePermissionWithPlayer(playerId, function(res)
                if res then
                    -- 允许与目标玩家通信，解除静音
                    GVoiceLogic.SetPlayerMuteState(memberOpenIdStr, false)
                end
            end)
        end
    end
    -- END MODIFICATION - VIRTUOS
    -- if Server.TeamServer:GetTeamNum() > 1 then
    --     GVoiceLogic._QuitLobbyRoom()
    -- end
    GVoiceLogic._JoinLobbyRoom()
end

function GVoiceLogic._OnTeammateLeft(playerId)
    GVoiceLogInfo("GVoiceLogic._OnTeammateLeft ", tostring(playerId), " teamNum:", tostring(Server.TeamServer:GetTeamNum()))
    if Server.TeamServer:GetTeamNum() <= 1 then
        GVoiceLogic._QuitLobbyRoom()
        if IsHD() then
            --PC不用重置，有设置
        else
            --移动端退队需要重置按钮
            if _gvoiceManager ~= nil then
                --_gvoiceManager:ResetButtonType()
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.Close)
                GVoiceLogic.SetSpeakerButtonType(EGVoiceButtonType.All)
            end
        end
    end
end

---局内匹配成功的事件
---@param dsRoomId any
---@param teamId any
function GVoiceLogic._PrepareJoinMatch(dsRoomId, teamId)
    GVoiceLogInfo("GVoiceLogic._PrepareJoinMatch dsRoomId:", tostring(dsRoomId), " teamId:", tostring(teamId))
end

---局内重连入局
function GVoiceLogic._OnMatchReconnect()
    GVoiceLogInfo("GVoiceLogic._OnMatchReconnect")
    GVoiceLogic._OnPlayerStateMemberInfoListLengthChanged()
end

function GVoiceLogic._JoinLobbyRoom()
    local teamId = Server.TeamServer:GetTeamId()
    local teamNum = Server.TeamServer:GetTeamNum()
    GVoiceLogInfo("GVoiceLogic._JoinLobbyRoom teamId:", teamId, " teamNum:", teamNum)
    if teamId == 0 or teamNum <= 1 then
        logerror("GVoiceLogic._JoinLobbyRoom teamId is 0:", teamId)
        return
    end
    local zoneId = Server.SDKInfoServer:GetZoneId()
    local roomName = zoneId.."-"..teamId
    local roomChannel = EGVoiceRoomChannel.Team
    local roomType = Module.GVoice.Config.EGVoiceRoomType.Lobby
    GVoiceLogInfo(string.format("GVoiceLogic._JoinLobbyRoom roomName:%s, roomChannel:%d roomType:%d", roomName, roomChannel, roomType))
    GVoiceLogic.JoinRoom(roomName, roomChannel, roomType)
end

---退出局外语音房间
function GVoiceLogic._QuitLobbyRoom(teamId)
    GVoiceLogInfo("GVoiceLogic._QuitLobbyRoom")
    if IsHD() then
        --PC不用重置，有设置
    else
        --移动端退队需要重置按钮
        if _gvoiceManager ~= nil then
            _gvoiceManager:ResetButtonType()
        end
    end
    --现在队伍退出事件有问题，退出的时候广播的teamId是0
    --local roomName = tostring(teamId)
    --GVoiceLogic.QuitRoom(roomName)
    GVoiceLogic.QuitAllRoom()
end

---加入局内语音房
---@param dsRoomId any
---@param teamId any
function GVoiceLogic._JoinInGameRoom()
    if Facade.GameFlowManager:IsInOBMode() then
        logwarning("GVoiceLogic._JoinInGameRoom IsInOBMode")
        return
    end
    if GVoiceLogic.IsCommanderGameMode() then
        GVoiceLogic._JoinInGameRoom_Camp()
    else
        GVoiceLogic._JoinInGameRoom_All()
    end
end

function GVoiceLogic._JoinInGameRoom_All()
    local zoneId = Server.SDKInfoServer:GetZoneId()
    local dsRoomId = Server.MatchServer:GetDsRoomId()
    local dsTeamId = Server.MatchServer:GetDsTeamId()
    local campId = GVoiceLogic.GetCamp()
    if campId < 0 then
        logerror("GVoiceLogic._JoinInGameRoom_All campId < 0")
        return
    end

    GVoiceLogInfo("GVoiceLogic._JoinInGameRoom_All dsRoomId:", tostring(dsRoomId), " dsTeamId:", tostring(dsTeamId), " campId:", campId)

    if dsRoomId == nil or dsTeamId == nil then
        logerror("GVoiceLogic._JoinInGameRoom_All dsRoomId dsTeamId nil")
        return
    end
    
    local roomName = zoneId.."-"..dsRoomId.."-" ..campId.."-"..dsTeamId
    local roomChannel = EGVoiceRoomChannel.All
    local roomType = Module.GVoice.Config.EGVoiceRoomType.InGame

    GVoiceLogic.JoinRoom(roomName, roomChannel, roomType)
end

function GVoiceLogic._OnJoinInGameRoom_All(roomName, roomChannel, bJoin)

end

function GVoiceLogic._JoinInGameRoom_Camp()
    GVoiceLogInfo("GVoiceLogic._JoinInGameRoom_Camp")
    _bCampChannelEnable = false
    _CampChannelEnableCache = nil
    --加入阵营房间
    if GVoiceLogic.IsCommanderGameMode() then
        local zoneId = Server.SDKInfoServer:GetZoneId()
        local dsRoomId = Server.MatchServer:GetDsRoomId()
        local dsTeamId = Server.MatchServer:GetDsTeamId()
        local campId = GVoiceLogic.GetCamp()
        if campId < 0 then
            logerror("GVoiceLogic._JoinInGameRoom_Camp campId < 0")
            return
        end

        GVoiceLogInfo("GVoiceLogic._JoinInGameRoom_Camp dsRoomId:", tostring(dsRoomId), " dsTeamId:", tostring(dsTeamId))

        local roomName = zoneId.."-"..dsRoomId.."-".. campId
        local roomChannel = EGVoiceRoomChannel.Camp
        local roomType = Module.GVoice.Config.EGVoiceRoomType.InGame

        GVoiceLogic.JoinRoom(roomName, roomChannel, roomType)
        --调整麦克风
        if GVoiceLogic.IsMicrophoneChannelAvailable(ESpeakingChanel.Camp) then
            GVoiceLogic.SetSpeakerButtonType(EGVoiceButtonType.Camp)
        else
            if GVoiceLogic.IsPressButtonType(GVoiceLogic.GetMicrophoneButtonType()) then
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.CampPress)
            else
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.Camp)
            end
        end
    end
end

---退出局内语音房间
function GVoiceLogic._QuitInGameRoom(teamId)
    if IsHD() then
        --重置屏蔽状态
        Module.GVoice.Field:ResetMute_PC()
        if _gvoiceManager then
            _gvoiceManager:ResetForbidAllState()
        end
    end
    GVoiceLogInfo("GVoiceLogic._QuitInGameRoom")
    GVoiceLogic.QuitRoomByType(Module.GVoice.Config.EGVoiceRoomType.InGame)
    --GVoiceLogic.QuitAllRoom()
end

---断线重连
function GVoiceLogic._OnRelayConnected()
    GVoiceLogInfo("GVoiceLogic._OnRelayConnected")
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:HandleReconnect()
end

---是否有麦克风权限
function GVoiceLogic.CheckMicrophonePermission(bRequestMicrophonePermission)
    if Module.Permission:IfPermissionGranted(DFPermissionType.RecordAudio) then
        GVoiceLogInfo("GVoiceLogic.CheckMicrophonePermission has RecordAudio permission")
        return true
    else
        GVoiceLogInfo("GVoiceLogic.CheckMicrophonePermission no RecordAudio permission")
        if bRequestMicrophonePermission then
            GVoiceLogInfo("GVoiceLogic.CheckMicrophonePermission no RecordAudio permission and request")
            Module.Permission:AsyncRequestPermissions(DFPermissionType.RecordAudio)
        end
        return false
    end
end

---麦克风是否可用
function GVoiceLogic.IsMicrophoneAvailable(bRequestMicrophonePermission)
    if not Module.Chat:AllowVoiceUse(false) then
        return false
    end
    if GVoiceLogic.CheckAccountGVoicePunish() then
        return false
    end
    if not GVoiceLogic.CheckMicrophonePermission(bRequestMicrophonePermission) then
        return false
    end
    if _gvoiceManager == nil then
        return false
    end
    return _gvoiceManager:IsMicrophoneAvailable()
end

--#endregion

--region 实时语音

---加入语音房间
---@param roomName string 房间名称
---@param roomChannel integer 房间频道
---@param roomType integer 房间类型 局外、局内 目前仅仅用于区分语音使用场景 
function GVoiceLogic.JoinRoom(roomName, roomChannel, roomType)
    if string.isempty(roomName) or roomName == "0" then
        logerror("GVoiceLogic.JoinRoom roomName is nil", roomName)
        return
    end
    -- BEGIN MODIFICATION - VIRTUOS
    local function HandleJoinRoom()
        if _gvoiceManager == nil then
            return
        end
        roomChannel = roomChannel or EGVoiceRoomChannel.Team
        roomType = roomType or 0
        Module.GVoice.Field:SetRoomName(roomChannel, roomName)
        _gvoiceManager:JoinRoom(roomName, roomChannel, roomType)
    end

    if IsXSX() or IsPS5() then
        -- 只有用户开启了平台通信权限，才会加入语音房
        if Server.ChatServer.hasCommunicationPrivilege then
            -- 在尚未进入语音房前将所有成员的语音音量设为0，经过平台通信权限检测后再打开
            local teamMembers = Server.TeamServer:GetMembers()
            for _, memberInfo in pairs(teamMembers) do
                local memberSimpleInfo = memberInfo.PlayerSimpleInfo
                local memberOpenId = memberSimpleInfo.player_id
                local memberOpenIdStr = ULuautils.GetOverInt64String(memberOpenId)
                if memberOpenId ~= Server.AccountServer:GetPlayerId() then
                    -- 处理其他玩家，忽略自己
                    GVoiceLogic.SetPlayerMuteState(memberOpenIdStr, true)
                    GVoiceLogic._CheckVoicePermissionWithPlayer(memberOpenId, function(res)
                        if res then
                            -- 允许与目标玩家通信，解除静音
                            GVoiceLogic.SetPlayerMuteState(memberOpenIdStr, false)
                        end
                    end)
                end
            end
            -- 将所有玩家静音后加入语音房，后续权限结果返回后在回调中根据权限解除静音
            HandleJoinRoom()
        end
    else
        HandleJoinRoom()
    end
    -- END MODIFICATION - VIRTUOS
end

---退出语音房间
---@param roomName string 房间名称
function GVoiceLogic.QuitRoom(roomName)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:QuitRoom(roomName)
end

---退出语音房间
---@param roomType number 房间类型
function GVoiceLogic.QuitRoomByType(roomType)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:QuitRoomByType(roomType)
end

---退出所有语音房间
function GVoiceLogic.QuitAllRoom()
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:QuitAllRoom()
end

---麦克风是否开启
---@return boolean
function GVoiceLogic.IsMicrophoneOpen()
    if _gvoiceManager == nil then
        return false
    end
    return _gvoiceManager:IsMicrophoneOpen()
end

---扬声器是否开启
---@return boolean
function GVoiceLogic.IsSpeakerOpen()
    if _gvoiceManager == nil then
        return false
    end
    return _gvoiceManager:IsSpeakerOpen()
end

---玩家自己是否在说话
---@return boolean
function GVoiceLogic.IsSpeaking()
    if _gvoiceManager == nil then
        return false
    end
    return _gvoiceManager:IsSpeaking()
end

---设置麦克风按钮类型
---@param buttonType number 语音按钮类型
function GVoiceLogic.SetMicrophoneButtonType(buttonType, bFromSettingSystem, bCheckMicrophonePermission)
    if not GVoiceLogic.IsDebugMode() then
        if buttonType ~= EGVoiceButtonType.Close and buttonType ~= EGVoiceButtonType.ClosePress then
            --文明语音
            local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
            local bShowTips = gameFlow == EGameFlowStageType.Game or gameFlow == EGameFlowStageType.SafeHouse or gameFlow == EGameFlowStageType.Lobby
            if not Module.Chat:AllowVoiceUse(bShowTips) then
                logwarning("GVoiceLogic.SetMicrophoneButtonType not AllowVoiceUse")
                return
            end
            --账号禁言
            if GVoiceLogic.CheckAccountGVoicePunish() then
                logwarning("GVoiceLogic.SetMicrophoneButtonType no CheckAccountGVoicePunish")
                return
            end
            --未成年家长授权
            --如果未成年设置全开麦模式，在局内或者队伍中就弹tip，其他就直接无感返回
            if buttonType == EGVoiceButtonType.Team or buttonType == EGVoiceButtonType.All then
                if not Module.Chat:CheckIfMinorChatAllowed(true) then
                    local isInGame = GVoiceLogic.IsInGame()
                    if Server.AccountServer:IsInTeam() or isInGame then
                        Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.MinorGetAuthorized)
                    end
                    return
                end
            end
            --权限相关
            if not GVoiceLogic.CheckMicrophonePermission(bCheckMicrophonePermission) then
                logwarning("GVoiceLogic.SetMicrophoneButtonType no Microphone Permission")
                return
            end
        end
    end

    --普通队员不能开启阵营麦,但是可以开阵营扬声器
    if GVoiceLogic.IsCommanderGameMode() and GVoiceLogic.IsCampButton(buttonType) then
        GVoiceLogic.SyncMicrophoneButtonTypeToSpeaker(buttonType, bFromSettingSystem)
        return
    end

    if _gvoiceManager ~= nil then
        _gvoiceManager:SetMicrophoneButtonType(buttonType)
    end

    local roomChannel = GVoiceLogic.GetRoomChannelFromButtonType(buttonType)
    Module.GVoice.Field:SetSpeakerRoomChannel(roomChannel)

    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if isvalid(localCtrl) then
        local dfmCharacter = localCtrl:GetPawn()
        if isvalid(dfmCharacter) and dfmCharacter.NotifyMicStateChange ~= nil then
            dfmCharacter:NotifyMicStateChange(buttonType)
        end
    end
    
    Module.GVoice.Config.Events.evtMicrophoneButtonTypeChange:Invoke(buttonType)

    GVoiceLogic._OnInGameMicrophoneButtonTypeChange(buttonType)

    if Module.GVoice.Field._microphoneButtonType and Module.GVoice.Field._microphoneButtonType ~= buttonType then
        Module.GVoice._micState = 3
    end
    if IsHD() and not bFromSettingSystem then
        GVoiceLogic.ApplyMicrophoneButtonTypeSetting(buttonType)
    end

    GVoiceLogic.SyncMicrophoneButtonTypeToSpeaker(buttonType, bFromSettingSystem)
end

---同步麦克风频道到扬声器频道
function GVoiceLogic.SyncMicrophoneButtonTypeToSpeaker(buttonType, bFromSettingSystem)
    if buttonType ~= EGVoiceButtonType.MossPress then
        local speakerButtonType = GVoiceLogic.GetSpeakerButtonType()
        buttonType = GVoiceLogic.ConvertPressButtonTypeToNormal(buttonType)
        if buttonType ~= EGVoiceButtonType.Close and speakerButtonType < buttonType then
            --喇叭只有All和Close，麦克风有All\Team\Close，奇葩设计
            if buttonType == EGVoiceButtonType.Team then
                buttonType = EGVoiceButtonType.All
            end
            GVoiceLogInfo("GVoiceLogic.SetMicrophoneButtonType speakerButtonType:", speakerButtonType, " microphoneButtonType:", buttonType)
            GVoiceLogic.SetSpeakerButtonType(buttonType, bFromSettingSystem)
        end
    end
end

---获取麦克风按钮类型
---@return number 语音按钮类型
function GVoiceLogic.GetMicrophoneButtonType()
    if _gvoiceManager == nil then
        return 0
    end
    return _gvoiceManager:GetMicrophoneButtonType()
end

---获取麦克风房间频道
---@return number 语音按钮类型
function GVoiceLogic.GetMicrophoneRoomChannel()
    local roomChannel = Module.GVoice.Field:GetMicrophoneRoomChannel()
    if roomChannel == nil then
        roomChannel = GVoiceLogic.GetRoomChannelFromButtonType(GVoiceLogic.GetMicrophoneButtonType())
        Module.GVoice.Field:SetMicrophoneRoomChannel(roomChannel)
    end
    return roomChannel
end

---设置扬声器按钮类型
---@param buttonType number 语音按钮类型
function GVoiceLogic.SetSpeakerButtonType(buttonType, bFromSettingSystem)
    local microphoneButtonType = GVoiceLogic.GetMicrophoneButtonType()
    local convertMicrophoneButtonType = GVoiceLogic.ConvertPressButtonTypeToNormal(microphoneButtonType)
    if buttonType < convertMicrophoneButtonType then
        GVoiceLogInfo("GVoiceLogic.SetSpeakerButtonType speakerButtonType:", buttonType, " microphoneButtonType:", microphoneButtonType, " convertMicrophoneButtonType:", convertMicrophoneButtonType)
        local micphoneButtonText = Module.GVoice:GetButtonText(microphoneButtonType)
        local speakerButtonText = Module.GVoice:GetButtonText(buttonType)
        local tips = Module.GVoice.Config.Loc.SpeakerChannelChangeTips
        Module.CommonTips:ShowSimpleTip(tips)
        return
    end
    if _gvoiceManager ~= nil then
        _gvoiceManager:SetSpeakerButtonType(buttonType)
    end

    local roomChannel = GVoiceLogic.GetRoomChannelFromButtonType(buttonType)
    Module.GVoice.Field:SetSpeakerRoomChannel(roomChannel)

    Module.GVoice.Config.Events.evtSpeakerButtonTypeChange:Invoke(buttonType)
    if IsHD() and not bFromSettingSystem then
        GVoiceLogic.ApplySpeakereButtonTypeSetting(buttonType)
    end
    --切换收听频道，重置说话列表
    GVoiceLogic.CloseSpeakerListPanel()
end

---获取扬声器按钮类型
---@return number 语音按钮类型
function GVoiceLogic.GetSpeakerButtonType()
    if _gvoiceManager == nil then
        return 0
    end
    return _gvoiceManager:GetSpeakerButtonType()
end

---获取扬声器房间频道
---@return number 语音按钮类型
function GVoiceLogic.GetSpeakerRoomChannel()
    local roomChannel = Module.GVoice.Field:GetSpeakerRoomChannel()
    if roomChannel == nil then
        roomChannel = GVoiceLogic.GetRoomChannelFromButtonType(GVoiceLogic.GetSpeakerButtonType())
        Module.GVoice.Field:SetSpeakerRoomChannel(roomChannel)
    end
    return roomChannel
end

---设置扬声器按钮类型
---@param buttonType number 语音按钮类型
function GVoiceLogic.IsPressButtonType(buttonType)
	if (buttonType == EGVoiceButtonType.ClosePress or
        buttonType == EGVoiceButtonType.MossPress or
        buttonType == EGVoiceButtonType.TeamPress or
        buttonType == EGVoiceButtonType.AllPress or
        buttonType == EGVoiceButtonType.CampPress) then
        return true
    end
	return false
end

--获取按钮类型对应的房间频道
function GVoiceLogic.GetRoomChannelFromButtonType(buttonType)
    if _gvoiceManager == nil then
        return 0
    end
    buttonType = buttonType or 0
    return _gvoiceManager:GetRoomChannelFromButtonType(buttonType)
end

---将按住说话的按钮转成普通按钮
---@param buttonType number 语音按钮类型
function GVoiceLogic.ConvertPressButtonTypeToNormal(buttonType)
    if buttonType == EGVoiceButtonType.ClosePress then
        buttonType = EGVoiceButtonType.Close
    elseif buttonType == EGVoiceButtonType.TeamPress then
        buttonType = EGVoiceButtonType.Team
    elseif buttonType == EGVoiceButtonType.AllPress then
        buttonType = EGVoiceButtonType.All
    elseif buttonType == EGVoiceButtonType.CampPress then
        buttonType = EGVoiceButtonType.Camp
    end
    return buttonType
end

---设置麦克风按钮按住说话状态
---@param bPress boolean 是否按住状态
function GVoiceLogic.SetMicrophoneButtonPress(bPress)
    GVoiceLogInfo("GVoiceLogic.SetMicrophoneButtonPress bPress:", tostring(bPress))
    if not GVoiceLogic.IsPressButtonType(GVoiceLogic.GetMicrophoneButtonType()) then
        GVoiceLogInfo("GVoiceLogic.SetMicrophoneButtonPress not press type bPress:", tostring(bPress))
        return
    end
    --家长认证
    --如果是在局内或队伍中，按住说话就判断是否是未成年
    local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local isInGame = GVoiceLogic.IsInGame()
    if bPress == true and (Server.AccountServer:IsInTeam() or isInGame) then
        if not Module.Chat:CheckIfMinorChatAllowed(true) then
            Module.CommonTips:ShowSimpleTip(Module.Chat.Config.Loc.MinorGetAuthorized)
            return
        end
    end

    if _gvoiceManager then
        _gvoiceManager:SetMicrophoneButtonPress(bPress)
    end

    _bPressMic = bPress

    GVoiceLogic._OnInGameMicrophoneButtonTypeChange(GVoiceLogic.GetMicrophoneButtonType())

    if not bPress then
        GVoiceLogic.UpdateSpeakerListPanel("press_fake", GVoiceLogic.GetMicrophoneRoomChannel(), Server.SDKInfoServer:GetOpenIdStr(), false)
    else
        --GVoiceLogic.UpdateSpeakerListPanel("press_fake", GVoiceLogic.GetMicrophoneRoomChannel(), Server.SDKInfoServer:GetOpenIdStr(), true)
    end

    GVoiceLogic._OnGVoiceSpeakStateChange(bPress)
end

---设置麦克风音量
---@param volume number 0-100
function GVoiceLogic.SetMicrophoneVolume(volume)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:SetMicrophoneVolume(volume)
end

---设置扬声器音量
---@param volume number 0-100
function GVoiceLogic.SetSpeakerVolume(volume)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:SetSpeakerVolume(volume)
end

---设置其他玩家的音量
---@param openId string 玩家openId
---@param volume number 0-100
function GVoiceLogic.SetRoomMemberVolume(openId, volume)
    if _gvoiceManager == nil then
        return
    end
    openId = GVoiceLogic.OpenIdToString(openId)
    _gvoiceManager:SetRoomMemberVolume(openId, volume)
    Module.GVoice.Field:SetRoomMemberVolume(openId, volume)
end

---获取其他玩家的音量
---@param openId string 玩家openId
---@return number 0-100
function GVoiceLogic.GetRoomMemberVolume(openId)
    if _gvoiceManager == nil then
        return 0
    end
    openId = GVoiceLogic.OpenIdToString(openId)
    -- local volume = _gvoiceManager:GetRoomMemberVolume(openId, volume)
    -- --说明该玩家不在语音房间，或者是AI，但是音量调节需要做假表现
    -- if volume < 0 then
    --     volume = Module.GVoice.Field:GetRoomMemberVolume(openId)
    --     if volume < 0 then
    --         Module.GVoice.Field:SetRoomMemberVolume(openId, _defaultMemberVolume)
    --         volume = _defaultMemberVolume
    --     end
    -- end
    local volume = Module.GVoice.Field:GetRoomMemberVolume(openId)
    if volume < 0 then
        Module.GVoice.Field:SetRoomMemberVolume(openId, _defaultMemberVolume)
        volume = _defaultMemberVolume
    end
    return volume
end

---使用设置按钮类型
function GVoiceLogic._SetupSettingButtonType()
    if not IsHD() then
        return
    end
    if _gvoiceManager == nil then
        return
    end
    local settingMicrophoneButtonType = GVoiceLogic.GetSettingMicrophoneButtonType()
    local settingSpeakerButtonType = GVoiceLogic.GetSettingSpeakerButtonType()
    GVoiceLogInfo("GVoiceLogic._SetupSettingButtonType settingMicrophoneButtonType:", settingMicrophoneButtonType, " settingSpeakerButtonType:", settingSpeakerButtonType)
    GVoiceLogic.SetMicrophoneButtonType(settingMicrophoneButtonType)
    GVoiceLogic.SetSpeakerButtonType(settingSpeakerButtonType)
end

--获取设置的麦克风语音按钮类型
function GVoiceLogic.GetSettingMicrophoneButtonType()
    local channel = Module.SystemSetting:GetDataByIDHD("SpeakingChanel")
    local pressType = Module.SystemSetting:GetDataByIDHD("MicrophoneButtonType")
    local buttonType = GVoiceLogic.ConvertSettingTypeToMicrophoneButtonType(channel, pressType)
    return buttonType
end

--应用麦克风语音按钮类型到设置
function GVoiceLogic.ApplyMicrophoneButtonTypeSetting(buttonType)
    --这里的value是设置里的，参考AudioSettingLogicHD._MicrophoneButtonTypApplyFunc
    local channel, pressType = GVoiceLogic.ConvertMicrophoneButtonTypeToSettingType(buttonType)
    GVoiceLogInfo("GVoiceLogic.ApplyMicrophoneButtonTypeSetting channel:", channel, " pressType:", pressType, " buttonType:", buttonType)
    Module.SystemSetting:SetDataByIDHD("SpeakingChanel", channel, true)
    Module.SystemSetting:SetDataByIDHD("MicrophoneButtonType", pressType, true)
end

--麦克风语音按钮类型转设置类型
function GVoiceLogic.ConvertMicrophoneButtonTypeToSettingType(buttonType)
    local channel = ESpeakingChanel.Team
    local pressType = EMicrophoneButtonType.Press
    if buttonType == EGVoiceButtonType.Team then
        channel = ESpeakingChanel.Team
        pressType = EMicrophoneButtonType.Always
    elseif buttonType == EGVoiceButtonType.TeamPress then
        channel = ESpeakingChanel.Team
        pressType = EMicrophoneButtonType.Press
    elseif buttonType == EGVoiceButtonType.All then
        channel = ESpeakingChanel.All
        pressType = EMicrophoneButtonType.Always
    elseif buttonType == EGVoiceButtonType.AllPress then
        channel = ESpeakingChanel.All
        pressType = EMicrophoneButtonType.Press
    elseif buttonType == EGVoiceButtonType.Camp then
        channel = ESpeakingChanel.Camp
        pressType = EMicrophoneButtonType.Always
    elseif buttonType == EGVoiceButtonType.CampPress then
        channel = ESpeakingChanel.Camp
        pressType = EMicrophoneButtonType.Press
    elseif buttonType == EGVoiceButtonType.Close then
        channel = Module.SystemSetting:GetDataByIDHD("SpeakingChanel")
        pressType = EMicrophoneButtonType.Close
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsXSX() or IsPS5() then
        -- Xbox平台移除了按键说话选项
        if buttonType ~= EGVoiceButtonType.Team and buttonType ~= EGVoiceButtonType.Close then
            pressType = EMicrophoneButtonType.Always
        end
    end
    -- END MODIFICATION - VIRTUOS

    return channel, pressType
end

--麦克风设置类型转语音按钮类型
function GVoiceLogic.ConvertSettingTypeToMicrophoneButtonType(channel, pressType)
    local buttonType = EGVoiceButtonType.Close
    if pressType == EMicrophoneButtonType.Always then
        if channel == ESpeakingChanel.Team then
            buttonType = EGVoiceButtonType.Team
        elseif channel == ESpeakingChanel.All then
            buttonType = EGVoiceButtonType.All
        elseif channel == ESpeakingChanel.Camp then
            buttonType = EGVoiceButtonType.Camp
        end
    elseif pressType == EMicrophoneButtonType.Press then
        if channel == ESpeakingChanel.Team then
            buttonType = EGVoiceButtonType.TeamPress
        elseif channel == ESpeakingChanel.All then
            buttonType = EGVoiceButtonType.AllPress
        elseif channel == ESpeakingChanel.Camp then
            buttonType = EGVoiceButtonType.CampPress
        end
    end
    return buttonType
end

--获取设置的扬声器语音按钮类型
function GVoiceLogic.GetSettingSpeakerButtonType()
    local channel = Module.SystemSetting:GetDataByIDHD("ListeningChanel")
    local buttonType = GVoiceLogic.ConvertSettingTypeToSpeakerButtonType(channel)
    return buttonType
end

--应用扬声器语音按钮类型到设置
function GVoiceLogic.ApplySpeakereButtonTypeSetting(buttonType)
    --这里的value是设置里的，参考AudioSettingLogicHD._ListeningChanelApplyFunc
    local channel = GVoiceLogic.ConvertSpeakerButtonTypeToSettingType(buttonType)
    GVoiceLogInfo("GVoiceLogic.ApplySpeakereButtonTypeSetting channel:", channel, " buttonType:", buttonType)
    Module.SystemSetting:SetDataByIDHD("ListeningChanel", channel, true)
end

--扬声器语音按钮类型转设置类型
function GVoiceLogic.ConvertSpeakerButtonTypeToSettingType(buttonType)
    local channel = EListenChanel.Close
    if buttonType == EGVoiceButtonType.Team then
        channel = EListenChanel.Team
    elseif buttonType == EGVoiceButtonType.TeamPress then
        channel = EListenChanel.Team
    elseif buttonType == EGVoiceButtonType.All then
        channel = EListenChanel.All
    elseif buttonType == EGVoiceButtonType.AllPress then
        channel = EListenChanel.All
    elseif buttonType == EGVoiceButtonType.Camp then
        channel = EListenChanel.Camp
    elseif buttonType == EGVoiceButtonType.CampPress then
        channel = EListenChanel.Camp
    end
    return channel
end

--扬声器设置类型转语音按钮类型
function GVoiceLogic.ConvertSettingTypeToSpeakerButtonType(channel)
    local buttonType = EGVoiceButtonType.Close
    if channel == EListenChanel.All then
        buttonType = EGVoiceButtonType.All
    elseif channel == EListenChanel.Camp then
        buttonType = EGVoiceButtonType.Camp
    end
    return buttonType
end

---使用设置音量
function GVoiceLogic._SetupSettingVolume()
    if _gvoiceManager == nil then
        return
    end
    local settingMicrophoneVolume = Module.SystemSetting:GetMicrophoneVolume() or DEFAULT_VOLUME
    local settingSpeakerVolume = Module.SystemSetting:GetSpeakerVolume() or DEFAULT_VOLUME
    _gvoiceManager:SetMicrophoneVolume(settingMicrophoneVolume)
    _gvoiceManager:SetSpeakerVolume(settingSpeakerVolume)
end

--endregion

--region 离线语音

---开始录制离线语音消息
function GVoiceLogic.StartRecording()
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:StartRecording()
end

---停止录制离线语音消息
function GVoiceLogic.StopRecording()
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:StopRecording()
end

---上传离线语音消息
function GVoiceLogic.UploadRecordedFile()
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:UploadRecordedFile()
end

---下载离线语音消息
---@param fileId string GVoice组件使用的录音文件Id
function GVoiceLogic.DownloadRecordedFile(fileId)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:DownloadRecordedFile(fileId)
end

---播放离线语音消息
---@param fileId string GVoice组件使用的录音文件Id
function GVoiceLogic.PlayRecordedFile(fileId)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:PlayRecordedFile(fileId)
end

---停止播放离线语音消息
function GVoiceLogic.StopPlayRecordFile()
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:StopPlayRecordFile()
end

---文本翻译
function GVoiceLogic.TextTranslate(text, srcLang, targetLang, timeoutMS)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:TextTranslate(text, srcLang, targetLang, timeoutMS)
end

--#endregion

--#region PC Events

---切换麦克风状态 按住说话、持续说话、关闭麦克风 循环切换
function GVoiceLogic.OnToggleMicrophone_PC()
    local pressType = Module.SystemSetting:GetDataByIDHD("MicrophoneButtonType") or 0
    local nextPressType = (pressType + 1) % table.nums(EMicrophoneButtonType)
    GVoiceLogInfo("GVoiceLogic.OnToggleMicrophone_PC pressType:", pressType, " nextPressType:", nextPressType)
    Module.SystemSetting:SetDataByIDHD("MicrophoneButtonType", nextPressType)
    
    if nextPressType == EMicrophoneButtonType.Close then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicType_Close_HD)
    elseif nextPressType == EMicrophoneButtonType.Press then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicType_Press_HD)
    elseif nextPressType == EMicrophoneButtonType.Always then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicType_Open_HD)
    end
end

function GVoiceLogic.IsMicrophoneChannelAvailable(speakingChanel)
    if speakingChanel == ESpeakingChanel.Camp then
        return _bCampChannelEnable and GVoiceLogic.IsCommanderGameMode()
    elseif speakingChanel == ESpeakingChanel.All then
        if GVoiceLogic.IsCommanderGameMode() then
            return _curStage and _curStage >= EBreakthroughStage.PreMatch
        end
    end
    return true
end

function GVoiceLogic.IsCampChannelEnable()
    return GVoiceLogic.IsMicrophoneChannelAvailable(ESpeakingChanel.Camp)
end

--设置选项用的枚举
local _SpeakingChanelOrder = {
    ESpeakingChanel.Team, ESpeakingChanel.All, ESpeakingChanel.Camp, ESpeakingChanel.Close
}

---切换麦克风频道 关闭，组队、全队 循环切换
function GVoiceLogic.OnToggleMicrophoneRoomChannel_PC()
    local speakingChanel = Module.SystemSetting:GetDataByIDHD("SpeakingChanel") or 0
    local speakingChanelOrder = 1
    for order, channel in ipairs(_SpeakingChanelOrder) do
        if channel == speakingChanel then
            speakingChanelOrder = order
            break
        end
    end

    local speakingChanelOrderNum = #_SpeakingChanelOrder
    local nextSpeakingChanel = speakingChanel
    
    -- 循环查找下一个可用的频道
    for i = 1, speakingChanelOrderNum do
        local nextOrder = (speakingChanelOrder + i - 1) % speakingChanelOrderNum + 1
        local tempChannel = _SpeakingChanelOrder[nextOrder]
        
        if GVoiceLogic.IsMicrophoneChannelAvailable(tempChannel) then
            nextSpeakingChanel = tempChannel
            break
        else
            GVoiceLogInfo("GVoiceLogic.OnToggleMicrophoneRoomChannel_PC not IsMicrophoneChannelAvailable:", tempChannel)
        end
    end

    GVoiceLogInfo("GVoiceLogic.OnToggleMicrophoneRoomChannel_PC speakingChanel:", speakingChanel, " nextSpeakingChanel:", nextSpeakingChanel)
    Module.SystemSetting:SetDataByIDHD("SpeakingChanel", nextSpeakingChanel)

    if nextSpeakingChanel == ESpeakingChanel.Close then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicChannel_Close_HD)
    elseif nextSpeakingChanel == ESpeakingChanel.Team then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicChannel_Team_HD)
    elseif nextSpeakingChanel == ESpeakingChanel.All then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicChannel_All_HD)
    elseif nextSpeakingChanel == ESpeakingChanel.Camp then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.GVoiceChangeMicChannel_Camp_HD)
    end
    GVoiceLogic.CloseSpeakerListPanel()
end

--PC可以通过设置切换状态
function GVoiceLogic.ApplyMicrophoneButtonTypeBySetting_PC(btnType)
    --这里的value是设置里的，参考AudioSettingLogicHD._MicrophoneButtonTypApplyFunc
    GVoiceLogic.SetMicrophoneButtonType(btnType)
end

---按住说话
---@param bPress boolean 是否按住说话
function GVoiceLogic.OnButtonMicrophonePress_PC(bPress)
    local buttonType = GVoiceLogic.GetMicrophoneButtonType()
    GVoiceLogInfo("GVoiceLogic.OnButtonMicrophonePress_PC:", tostring(bPress), " buttonType:", buttonType)
    GVoiceLogic.SetMicrophoneButtonPress(bPress)
end

---屏蔽其他人的麦克风
function GVoiceLogic.OnToggleMuteAll_PC()
    if _gvoiceManager == nil then
        return
    end
    Module.GVoice.Field:ToggleMuteAll_PC()
    local isMute = Module.GVoice.Field:IsMuteAll_PC() or false
    GVoiceLogInfo("OGVoiceLogic.OnToggleMuteAll_PC isMute:", tostring(isMute))
    _gvoiceManager:ForbidAllRoomMembersVoice(isMute)
    Module.GVoice.Config.Events.evtOnMuteAllMember:Invoke(isMute)
    if isMute then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.OnToggleMuteAll_Mute_HD)
    else
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.OnToggleMuteAll_Not_Mute_HD)
    end
end

--别人进房，检测是否需要屏蔽他
function GVoiceLogic._CheckForibidRoomMembersVoice(roomName, roomChannel, openId)
    if _gvoiceManager == nil then
        return
    end
    local isMute = Module.GVoice.Field:IsMuteAll_PC() or false
    if isMute then
        GVoiceLogInfo("OGVoiceLogic._CheckForibidRoomMembersVoice openId:", openId, roomName, roomChannel)
        _gvoiceManager:ForbidRoomMemberVoice(openId, isMute)
    else
        local volume = GVoiceLogic.GetRoomMemberVolume(openId)
        GVoiceLogic.SetRoomMemberVolume(openId, volume)
         GVoiceLogInfo("OGVoiceLogic._CheckForibidRoomMembersVoice SetRoomMemberVolume", openId, roomName, volume)
    end
end

-- BEGIN MODIFICATION - VIRTUOS
---设置其他玩家是否静音
function GVoiceLogic.SetPlayerMuteState(openId, isMute)
    loginfo("SetPlayerMuteState ismute", isMute)
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:SetPlayerMuteState(openId, isMute)
end
-- END MODIFICATION - VIRTUOS

--endregion

--region speaker list view

--是否显示说话人列表

function GVoiceLogic.UpdateSpeakerListPanel(roomName, roomChannel, openId, isSpeaking)
    if not IsHD() then
        return
    end
    Module.GVoice.Field:SetSpeakerState(roomName, roomChannel, GVoiceLogic.GetSpeakerRoomChannel(), openId, isSpeaking)
    local isInGame = GVoiceLogic.IsInGame()
    local panel = Module.GVoice.Field:GetSpeakerListPanel()
    local isCamp = roomChannel == EGVoiceRoomChannel.Camp
    local identityInfo = nil

    if isCamp then
        identityInfo = GVoiceLogic.GetMemberIdentityInfo(openId)
    end
    -- if GVoiceLogic.IsDebugMode() then
    --     identityInfo = {
    --         identity = 2,
    --         tempId = "["..tostring(os.time() % 10)..openId.."]"
    --     }
    -- end
    if panel then
        GVoiceLogInfo("GVoiceLogic.UpdateSpeakerListPanel IsInViewport", openId, isSpeaking, isInGame, roomChannel)
        if panel:GetUIIns() then
            panel:GetUIIns():OnInitExtraData(openId, isSpeaking, isInGame, roomChannel, identityInfo)
        end
    else
        GVoiceLogInfo("GVoiceLogic.UpdateSpeakerListPanel", openId, isSpeaking, isInGame, roomChannel)
        panel = Facade.UIManager:AsyncShowUI(UIName2ID.SpeakerListPanel, nil, nil, openId, isSpeaking, isInGame, roomChannel, identityInfo)
        Module.GVoice.Field:SetSpeakerListPanel(panel)
    end
end

GVoiceLogic.CloseSpeakerListPanel = function()
    if not IsHD() then
        return
    end
    local panel = Module.GVoice.Field:GetSpeakerListPanel()
    if panel then
        Facade.UIManager:CloseUIByHandle(panel)
        Module.GVoice.Field:SetSpeakerListPanel(nil)
    end
end

--endregion

--region 硬件信息相关

--- 初始化所有硬件数量
function GVoiceLogic._InitAllDeviceCount()
    if _gvoiceManager == nil then
        return
    end
    _gvoiceManager:GetDeviceCountAsync(0)
    _gvoiceManager:GetDeviceCountAsync(1)
end

--- 获取硬件数量
---@param deviceType number 硬件类型 参考C++ gcloud_voice::DeviceType
---@return number 硬件数量
function GVoiceLogic.GetDeviceCount(deviceType)
    return Module.GVoice.Field:GetDeviceCount(deviceType)
end

--- 获取硬件信息
---@param deviceType number 硬件类型 参考C++ gcloud_voice::DeviceType
---@param index number 硬件列表索引 小于GetDeviceCount
---@return string 硬件Id
---@return string 硬件名称
function GVoiceLogic.DescribleDevice(deviceType, index)
    local deviceId = ""
    local deviceName = ""
    if _gvoiceManager ~= nil then
        deviceId, deviceName = _gvoiceManager:DescribleDevice(deviceType, index, deviceId, deviceName)
        GVoiceLogInfo(string.format("GVoiceLogic.DescribleDevice deviceType:%d index:%d deviceId:%s deviceName:%s", deviceType, index, deviceId, deviceName))
    end
    return deviceId, deviceName
end

--- 设置硬件
---@param deviceType number 硬件类型 参考C++ gcloud_voice::DeviceType
---@param deviceId string 硬件Id
function GVoiceLogic.SelectDevice(deviceType, deviceId)
    GVoiceLogInfo("GVoiceLogic.SelectDevice deviceType:", deviceType, " deviceId:", deviceId)
    if _gvoiceManager == nil then
        return
    end
    if deviceType == nil or string.isempty(deviceId) then
        logerror("GVoiceLogic.SelectDevice error deviceType:", deviceType, " deviceId:", deviceId)
        return
    end
    _gvoiceManager:SelectDevice(deviceType, deviceId)
    if IsXSX() or IsPS5() then
        -- 设置硬件后，重新读取语音设置
        GVoiceLogic._SetupSettingButtonType()
    end
end

--- 获取当前设置的硬件索引
---@param deviceType number 硬件类型 参考C++ gcloud_voice::DeviceType
---@return number 硬件Index 如果获取异常，则返回-1; 如果没有执行SelectDevice 则会返回0
function GVoiceLogic.GetSelectDeviceIndex(deviceType)
    if _gvoiceManager == nil then
        return -1
    end
    return _gvoiceManager:GetSelectDeviceIndex(deviceType)
end

--- 硬件变化的事件
---@param code number 返回错误码 GCloudVoiceCompleteCode
---@param deviceType number 硬件类型 参考C++ gcloud_voice::DeviceType
---@param deviceId string 硬件Id
function GVoiceLogic._OnGVoiceDevice(code, deviceType, deviceId)
    Module.GVoice.Config.Events.evtOnGVoiceDevice:Invoke(code, deviceType, deviceId)
    if code == Module.GVoice.Config.GCloudVoiceCompleteCode.GV_ON_DEVICE_EVENT_DEFAULTCHANGE then
        GVoiceLogic.SelectDevice(deviceType, deviceId)
    end
end

--- 硬件数量变化的事件
---@param deviceType number 硬件类型 参考C++ gcloud_voice::DeviceType
---@param deviceCount number 硬件数量
function GVoiceLogic._OnGVoiceGetDeviceCount(deviceType, deviceCount)
    Module.GVoice.Field:SetDeviceCount(deviceType, deviceCount)
    Module.GVoice.Config.Events.evtOnGVoiceGetDeviceCount:Invoke(deviceType, deviceCount)
end

--- 翻译文本
---@param code, this operation's result @enum GCloudVoiceCompleteCode.
---@param srcLang, speech language associated with fileID.
---@param srcText, text that the source speech file translate to.
---@param targetLang, target language that we want to translate to.
---@param targetText, text that the source text translate to.
function GVoiceLogic._OnTextTranslated(code, srcLang, srcText, targetLang, targetText)
    Module.GVoice.Config.Events.evtOnTextTranslated:Invoke(code, srcLang, srcText, targetLang, targetText)
end

--endregion

--region 系统相关

--- 切换前后台的事件
---@param bActive boolean true-前台 false-后台
function GVoiceLogic._OnGVoiceApplicationActive(bActive)
    logerror("GVoiceLogic._OnGVoiceApplicationActive bActive:", tostring(bActive))
    if bActive then
        GVoiceLogic.ResumeVoiceEngine()
    else
        GVoiceLogic.PauseVoiceEngine()
    end
    Module.GVoice.Config.Events.evtOnGVoiceApplicationActive:Invoke(bActive)
    Module.NetworkBusiness:OnApplicationActive(bActive)
end

function GVoiceLogic.PauseVoiceEngine()
    if PLATFORM_IOS then
        GVoiceLogic.OpenIosBackgroundMode()
    else
        if _gvoiceManager then
            _gvoiceManager:PauseVoiceEngine()
        end
    end
end

function GVoiceLogic.ResumeVoiceEngine()
    if PLATFORM_IOS then
        GVoiceLogic.CloseIosBackgroundMode()
    else
        if _gvoiceManager then
            _gvoiceManager:ResumeVoiceEngine()
        end
    end
end

function GVoiceLogic.OpenIosBackgroundMode()
    if PLATFORM_IOS then
        GVoiceLogic.Invoke(9500, 1, 0)
        GVoiceLogic.Invoke(20, 1, 0)
    end
end

function GVoiceLogic.CloseIosBackgroundMode()
    if PLATFORM_IOS then
        GVoiceLogic.Invoke(9500, 0, 0)
        GVoiceLogic.Invoke(20, 0, 0)
    end
end

--endregion

--region 文明语音相关

--账号文明发言被禁言
function GVoiceLogic._OnAccountGVoicePunish(punish_info)
    if punish_info == nil then
        logerror("GVoiceLogic._OnAccountGVoicePunish punish_info is nil")
        return
    end
    _accountPunishEndTime = punish_info["punish_end"] or 0
    local endTimeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(_accountPunishEndTime)
    GVoiceLogInfo("GVoiceLogic._OnAccountGVoicePunish punish_info _end:"..endTimeStr, "_accountPunishEndTime:", _accountPunishEndTime)
    if _accountPunishEndTime > 0 and _accountPunishEndTime >= TimeUtil.GetCurrentTime() then
        local CurGVoicePunishTime = tostring(_accountPunishEndTime)
        local LastGVoicePunishTime = Facade.ConfigManager:GetString("GVoicePunishTime", "")
        if LastGVoicePunishTime == CurGVoicePunishTime then
            GVoiceLogInfo("GVoiceLogic._OnAccountGVoicePunish alreay show window:"..LastGVoicePunishTime)
            return
        end
        Facade.ConfigManager:SetString("GVoicePunishTime", CurGVoicePunishTime)
        local content = string.format(Module.GVoice.Config.Loc.AccountGVoicePunishWindow, endTimeStr)
        if punish_info.reason and punish_info.reason ~= 0 then
            local buildLoginLimitNtfData = {
                ["reason"] = punish_info.reason,
                ["over_time"] = _accountPunishEndTime
            }
            local punishMsg = Server.AccountServer:SerializePunishNtfMessage(buildLoginLimitNtfData)
            if punishMsg then
                content = punishMsg
            else
                -- 解析失败了走原有的读表逻辑
                local accountPunishData = Facade.TableManager:GetTable("AccountPunishReason")
                local findedMsgData = table.find(accountPunishData, function(v, k) return k == tostring(punish_info.reason) end)
                if findedMsgData then
                    content = findedMsgData.Message
                end
            end
        end
        GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.Close)
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            content,
            nil,
            Module.Login.Config.Loc.ConfirmBtnText
        )
    end
end

--是否处于封禁状态
function GVoiceLogic.CheckAccountGVoicePunish()
    if _accountPunishEndTime > 0 and _accountPunishEndTime >= TimeUtil.GetCurrentTime() then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.AccountGVoicePunishTips)
        return true
    end
    return false
end

--- 举报不文明玩家
---@param playerList table 玩家OpenIdList
---@param info string 举报信息
function GVoiceLogic.ReportPlayer(playerList, info)
    if _gvoiceManager == nil then
        return
    end

    local openIdList = {}
    for _, id in ipairs(playerList) do
        local opendId = GVoiceLogic.OpenIdToString(id)
        GVoiceLogDebug("GVoiceLogic.ReportPlayerList opendId num:", tostring(id), " | str:", opendId)
        table.insert(openIdList, opendId)
    end

    if not Module.GVoice.Field:IsPlayerEndCD(openIdList) then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.ReportPlayerOften)
        return
    end
  
    local errorCode = _gvoiceManager:ReportPlayer(openIdList, info)

    GVoiceLogInfo("GVoiceLogic.ReportPlayer info:", info, " code:", errorCode)
    
    if errorCode == Module.GVoice.Config.GCloudVoiceErrno.GCLOUD_VOICE_SUCC then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.ReportPlayerSuc)
        Module.GVoice.Field:RecordReportTime(openIdList)
    elseif errorCode == Module.GVoice.Config.GCloudVoiceErrno.GCLOUD_VOICE_NOTHING_TO_REPORT then
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.ReportPlayerNoThing)
    else
        Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.ReportPlayerFail)
    end
end

--- 举报玩家后的结果
---@param code number 返回错误码 GCloudVoiceCompleteCode
---@param info string 举报信息
function GVoiceLogic._OnReportPlayer(code, info)
    GVoiceLogInfo("GVoiceLogic._OnReportPlayer code:", tostring(code), " info:", tostring(info))
    -- if code == Module.GVoice.Config.GCloudVoiceCompleteCode.GV_ON_REPORT_SUCC then
    --     Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.ReportPlayerResultFail)
    -- else
    --     Module.CommonTips:ShowSimpleTip(Module.GVoice.Config.Loc.ReportPlayerResultSuc)
    -- end
    -- Module.GVoice.Config.Events.evtGVoiceReportPlayer:Invoke(code, info)
end

function GVoiceLogic.GetRoomMemberMicroState(openId)
    return Module.GVoice.Field:GetRoomMemberMicroState(openId)
end

--endregion

function GVoiceLogic._RequestBlueToothPermission()
    if Module.Permission:IfPermissionGranted(DFPermissionType.BlueTooth) then
        GVoiceLogInfo("GVoiceLogic._RequestBlueToothPermission has BlueTooth permission")
    else
        GVoiceLogInfo("GVoiceLogic._RequestBlueToothPermission no BlueTooth permission")
        Module.Permission:AsyncRequestPermissions(DFPermissionType.BlueTooth)
    end
end

--region 队伍相关

function GVoiceLogic.GetTeamMemberList(buttonType)
    local memberList = {}
    local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local isLobby = gameFlow == EGameFlowStageType.SafeHouse or gameFlow == EGameFlowStageType.Lobby or gameFlow == EGameFlowStageType.ModeHall
    --局外只能拿到局外队伍
    if isLobby or buttonType == EGVoiceButtonType.Team or buttonType == EGVoiceButtonType.TeamPress then
        local members = Server.TeamServer:GetOtherMembers()
        for _, info in pairs(members) do
            local memberInfo = {
                playerId = Luautils.GetOverInt64String(info.PlayerID),
                playerName = info.PlayerName,
                teamIndex = info.Seat or 0,
            }
            table.insert(memberList, memberInfo)
            GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.Team openidStr is other:", memberInfo.playerId)
        end
        --local bInSOL = Server.ArmedForceServer:GetCurArmedForceMode() == EArmedForceMode.SOL
        -- table.sort(
        --     memberList,
        --     function(a, b)
        --         return a.Seat < b.Seat
        --     end
        -- )
    elseif buttonType == EGVoiceButtonType.All or buttonType == EGVoiceButtonType.AllPress then
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if playerState ~= nil then
            memberList = GVoiceLogic.ParseTeamMemberList(playerState.MemberInfoList)
        end
    elseif buttonType == EGVoiceButtonType.Camp or buttonType == EGVoiceButtonType.CampPress then
        local playerState = Facade.GameFlowManager:GetPlayerState()
        if playerState ~= nil then
            memberList = GVoiceLogic.ParseMemberIncommander(playerState.CampMemberInfoList)
        end
    end
    return memberList
end

function GVoiceLogic.ParseTeamMemberList(memberInfoList)
    local memberList = {}
    if memberInfoList then
        for _, info in ipairs(memberInfoList) do
            local openidStr = Luautils.GetOverInt64String(info.PlayerUin)
            if openidStr ~= Server.SDKInfoServer:GetOpenIdStr() then
                local innerTeamIndex = info.InnerTeamIndex or 0
                local teamIdentity = 0
                if isvalid(info.PS) then
                    teamIdentity = info.PS:GetTeamIdentity()
                end
                local memberInfo = {
                    playerId = Luautils.GetOverInt64String(info.PlayerUin),
                    playerName = info.PlayerName,
                    teamIndex = innerTeamIndex + 1,
                    teamIdentity = teamIdentity,
                }
                table.insert(memberList, memberInfo)
                GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.All openidStr is other:", openidStr)
            else
                GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.All openidStr is me:", openidStr)
            end
        end
    else
        GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.All no member")
    end
    return memberList
end

function GVoiceLogic.GetMemberHasCommanderChannelVoiceAuthority()
    local memberIdList = {}
    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if not gameState then
        return memberIdList
    end
 
    local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()

    if battleFieldCommanderDataComponent and battleFieldCommanderDataComponent.CommanderChannelVoiceAuthority then
        for _, playerId in ipairs(battleFieldCommanderDataComponent.CommanderChannelVoiceAuthority) do
            local idStr = Luautils.GetOverInt64String(playerId) or ""
            memberIdList[idStr] = true
        end
    end
    return memberIdList
end

function GVoiceLogic.ParseMemberIncommander(memberInfoList)
    local playerState = Facade.GameFlowManager:GetPlayerState()
    local memberList = {}
    if playerState == nil then
        return memberList
    end
    local memberHasCommanderChannelVoiceAuthorityIdList = GVoiceLogic.GetMemberHasCommanderChannelVoiceAuthority()
    if memberInfoList then
        for _, info in ipairs(memberInfoList) do
            local openIdStr = Luautils.GetOverInt64String(info.PlayerUin)
            if openIdStr ~= Server.SDKInfoServer:GetOpenIdStr() then
                local innerTeamIndex = info.InnerTeamIndex or 0
                local teamIdentity = 0
                if isvalid(info.PS) then
                    teamIdentity = info.PS:GetTeamIdentity()
                end
                if memberHasCommanderChannelVoiceAuthorityIdList[openIdStr] then
                    local memberInfo = {
                        playerId = openIdStr,
                        playerName = info.PlayerName,
                        teamIndex = innerTeamIndex + 1,
                        teamIdentity = teamIdentity,
                    }
                    table.insert(memberList, memberInfo)
                    GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.All openidStr is other:", openIdStr)
                end

            else
                GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.All openidStr is me:", openIdStr)
            end
        end
    else
        GVoiceLogInfo("GVoiceLogic.GetTeamMemberList EGVoiceRoomChannel.All no member")
    end
    return memberList
end


--获取队友名字
function GVoiceLogic.GetTeamMemberName(openId)
    -- if GVoiceLogic.IsDebugMode() then
    --     return "DebugName"..openId
    -- end
    --玩家自己
    if openId == Server.SDKInfoServer:GetOpenIdStr() then
        return Server.SDKInfoServer:GetUserName()
    end
    --其他玩家
    local memberName = Module.GVoice.Field:GetRoomMemberName(openId)
    if string.isempty(memberName) then
        local memberInfo = Server.TeamServer:GetMemberByIdStr(openId)
        --先从局外拿
        if memberInfo and not string.isempty(memberInfo.PlayerName) then
            memberName = memberInfo.PlayerName
            Module.GVoice.Field:SetRoomMemberName(openId, memberName)
        --拿不到从局内拿
        else
            memberName = GVoiceLogic._GetPlayerStateMemberName(openId)
            if not string.isempty(memberName) then
                Module.GVoice.Field:SetRoomMemberName(openId, memberName)
            end
        end
    end
    return memberName or ""
end

function GVoiceLogic._GetPlayerStateMemberName(openId)
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if playerState == nil then
        logerror("GVoiceLogic._GetPlayerStateMemberName playerState is nil")
        return nil
    end
    local memberInfoList = playerState.MemberInfoList
    if memberInfoList then
        for _, memberInfo in pairs(memberInfoList) do
            if Luautils.GetOverInt64String(memberInfo.PlayerUin) == openId then
                return memberInfo.PlayerName
            end
        end
    end
    memberInfoList = playerState.CampMemberInfoList
    if memberInfoList then
        for _, memberInfo in pairs(memberInfoList) do
            if Luautils.GetOverInt64String(memberInfo.PlayerUin) == openId then
                return memberInfo.PlayerName
            end
        end
    end
end

-- BEGIN MODIFICATION - VIRTUOS
function GVoiceLogic._CheckVoicePermissionWithPlayer(openId, fCallBack)
    -- 首先判断与对方是否存在游戏内拉黑关系
    local onGetFriendSpeechJudgmentRes = function(res)
        if res then
            -- 获取目标用户平台信息
            local req = pb.CSAccountGetPlayerProfileReq:New()
            req.player_id = openId
            req.client_flag = 0
            req:Request(function(playerProfileRes)
                if playerProfileRes.result == 0 then
                    if playerProfileRes.plat_id == Server.AccountServer:GetPlatIdType() then
                        -- 目标玩家来自相同平台
                        if IsPS5() then
                            if res then
                                fCallBack(true)
                            else
                                fCallBack(false)
                            end
                        else
                            if res then
                                local checkList = {}
                                table.insert(checkList, openId)
                                -- 判断与对方是否存在平台方的通信权限限制
                                Server.ChatServer:CheckUsersPermissionsByOpenIdList(EPlatformUserPermissionType.CommunicateUsingVoice, true, checkList, function(allowedList)
                                    local hasVoicePermissionWithPlayer = #allowedList > 0
                                    if #allowedList <= 0 then
                                        loginfo("Due to platform permission restrictions, there is no voice permission with the target player. openId: ", openId)
                                    end
                                    if fCallBack then
                                        fCallBack(hasVoicePermissionWithPlayer)
                                    end
                                end)
                            else
                                loginfo("Due to the in-game blacklist, there is no voice permission with the target player. openId: ", openId)
                                if fCallBack then
                                    fCallBack(false)
                                end
                            end
                        end
                    else
                        -- 目标玩家来自不同平台
                        Server.ChatServer:CheckAnonymousUserCommunicationPermissions(openId, function(isAllowed)
                            if isAllowed then
                                fCallBack(true)
                            else
                                loginfo("Due to the crossplay permission, there is no voice permission with the target player. openId: ", openId)
                                fCallBack(false)
                            end
                        end)
                    end
                else
                    fCallBack(true)
                end
            end, {bEnableHighFrequency = true})
        else
            loginfo("Due to the in-game blacklist, there is no voice permission with the target player. openId: ", openId)
            if fCallBack then
                fCallBack(false)
            end
        end 
    end
    Server.FriendServer:ReqFriendSpeechJudgment(openId, onGetFriendSpeechJudgmentRes)
end
-- END MODIFICATION - VIRTUOS

function GVoiceLogic.IsInGame()
    local gameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    local isInGame = gameFlow == EGameFlowStageType.Game or gameFlow == EGameFlowStageType.GameSettlement
    return isInGame
end

--endregion

--region 阵营频道相关

--是否阵营模式
function GVoiceLogic.IsCommanderGameMode()
    local isInGame = GVoiceLogic.IsInGame()
    if isInGame then
        local inGameController = InGameController:Get()
        if inGameController then
            --loginfo("GVoiceLogic.IsCommanderGameMode subMode", inGameController:GetGamePlayerSubMode())
            return inGameController:IsCommander()
        end
    else
        --loginfo("GVoiceLogic.IsCommanderGameMode not inGame")
    end
    return false
end

--是否阵营按钮
function GVoiceLogic.IsCampButton(buttonType)
    if buttonType == EGVoiceButtonType.Camp or buttonType == EGVoiceButtonType.CampPress then
        return true
    end
    return false
end

--获取阵营
function GVoiceLogic.GetCamp()
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if playerState ~= nil and playerState.GetCamp then
        return playerState:GetCamp()
    end
    logerror("GVoiceLogic.GetCamp playerState is nil")
    return -1
end

--获取身份信息
function GVoiceLogic.GetMemberIdentityInfo(openId)
    local identityInfo = {
        identity = 0,
        teamId = 0
    }
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if playerState == nil then
        return identityInfo
    end
    local memberInfoList = playerState.CampMemberInfoList
    if memberInfoList then
        for _, memberInfo in pairs(memberInfoList) do
            if Luautils.GetOverInt64String(memberInfo.PlayerUin) == openId then
                identityInfo.teamId = memberInfo.TeamId
                if isvalid(memberInfo.PS) then
                    identityInfo.identity = memberInfo.PS:GetTeamIdentity()
                end
                return identityInfo
            end
        end
    end
    return identityInfo
end

function GVoiceLogic._OnPlayerStateMemberInfoListLengthChanged()
    GVoiceLogInfo("GVoiceLogic._OnPlayerStateMemberInfoListLengthChanged")
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if playerState == nil then
        return
    end
    local memberInfoList = playerState.MemberInfoList
    if memberInfoList then
        local num = memberInfoList:Num()
        GVoiceLogInfo("GVoiceLogic._OnPlayerStateMemberInfoListLengthChanged num:", num)
        if num > 1 then
            GVoiceLogic._JoinInGameRoom()
        else
            --GVoiceLogic.QuitAllRoom()
        end
    end
end

--指挥官权限变化
function GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged(bVoiceAuthority)
    GVoiceLogInfo("GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged _bCampChannelEnable", _bCampChannelEnable, "-new bVoiceAuthority:", bVoiceAuthority)
    _bCampChannelEnable = bVoiceAuthority
    if _bCampChannelEnable then
        local microphoneButtonType = GVoiceLogic.GetMicrophoneButtonType()
        _CampChannelEnableCache = microphoneButtonType
        if IsHD() then
            Module.SystemSetting:SetDataByIDHD("SpeakingChanel", ESpeakingChanel.Camp)
        else
            if microphoneButtonType == EGVoiceButtonType.Close or microphoneButtonType == EGVoiceButtonType.ClosePress then
                GVoiceLogInfo("GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged cur camp close")
                --GVoiceLogic.SetMicrophoneButtonType() 
            elseif GVoiceLogic.IsPressButtonType(microphoneButtonType) then
                GVoiceLogInfo("GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged cur camp press")
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.CampPress)
            else
                GVoiceLogInfo("GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged cur camp open")
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.Camp)
            end
        end
    else
        if _CampChannelEnableCache then
            GVoiceLogInfo("GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged _CampChannelEnableCache:", _CampChannelEnableCache)
            GVoiceLogic.SetMicrophoneButtonType(_CampChannelEnableCache)
        else
            GVoiceLogInfo("GVoiceLogic._OnCommanderChannelVoiceAuthorityChanged no _CampChannelEnableCache")
            GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.Close)
        end
        _CampChannelEnableCache = nil
    end
end

function GVoiceLogic._OnCurStageChanged(stage)
    GVoiceLogInfo("GVoiceLogic._OnCurStageChanged _curStage", _curStage, "-new stage:", stage)
    _curStage = stage
    if stage == EBreakthroughStage.PreMatch then
        GVoiceLogic._JoinInGameRoom_All()
    end
end

--身份变化
function GVoiceLogic._OnTeamIdentityReplicate()
    --GVoiceLogic.OnIdentityChange()
end

function GVoiceLogic.OnIdentityChange()
    if not GVoiceLogic.IsCommanderGameMode() then
        GVoiceLogInfo("GVoiceLogic.OnIdentityChange not IsCommanderGameMode")
        return
    end
    local playerState = Facade.GameFlowManager:GetPlayerState()
    --local playerState = GetInGameController():GetPlayerState()
    local identity = 0
    if playerState ~= nil then
        identity = playerState:GetTeamIdentity()
    end
    if identity == 0 then
        logerror("GVoiceLogic.OnIdentityChange identity == 0")
        return
    end
    local cacheIdentity = Module.GVoice.Field:GetIdentity()
    if cacheIdentity == identity then
        return
    end
    Module.GVoice.Field:SetIdentity(identity)
    local buttonType = GVoiceLogic.GetMicrophoneButtonType()
    GVoiceLogInfo("GVoiceLogic.OnIdentityChange identify:", cacheIdentity, identity, "buttonType:", buttonType)
    if identity <= 1 then
        if GVoiceLogic.IsCampButton(buttonType) then
            if GVoiceLogic.IsPressButtonType(buttonType) then
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.AllPress)
            else
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.All)
            end
        end
    else
        if not GVoiceLogic.IsCampButton(buttonType) then
            if GVoiceLogic.IsPressButtonType(buttonType) then
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.CampPress)
            else
                GVoiceLogic.SetMicrophoneButtonType(EGVoiceButtonType.Camp)
            end
            if IsMobile() then
                GVoiceLogic.SetSpeakerButtonType(EGVoiceButtonType.Camp)
            end
        end
    end
end

function GVoiceLogic.ResetCampButtonTypeToNarmal()
    local microphoneButtonType = GVoiceLogic.GetMicrophoneButtonType()
    local speakerButtonType = GVoiceLogic.GetSpeakerButtonType()
    if microphoneButtonType == EGVoiceButtonType.Camp then
        microphoneButtonType = EGVoiceButtonType.All
        GVoiceLogic.SetMicrophoneButtonType(microphoneButtonType)
    elseif microphoneButtonType == EGVoiceButtonType.CampPress then
        microphoneButtonType = EGVoiceButtonType.AllPress
        GVoiceLogic.SetMicrophoneButtonType(microphoneButtonType)
    end
    if speakerButtonType == EGVoiceButtonType.Camp then
        speakerButtonType = EGVoiceButtonType.All
        GVoiceLogic.SetSpeakerButtonType(speakerButtonType)
    end
end

--endregion

--region InGameSpeakDuration
function GVoiceLogic._InGameSpeakDurationStart()
    GVoiceLogInfo("GVoiceLogic._InGameSpeakDurationStart")
    _inGameFirstSpeakTime = os.time()
    _inGameLastSpeakTime = os.time()
    _inGameLastSpeakChannel = GVoiceLogic.GetMicrophoneButtonType()
    GVoiceLogic._OnInGameMicrophoneButtonTypeChange(_inGameLastSpeakChannel)
    _inGameSpeakDurations = {}
    _inGameSpeakDurationFlag = true
end

function GVoiceLogic._InGameSpeakDurationFinish()
    GVoiceLogInfo("GVoiceLogic._InGameSpeakDurationFinish")
    GVoiceLogic._OnInGameMicrophoneButtonTypeChange(_inGameLastSpeakChannel)
    _inGameSpeakDurationFlag = false
    GVoiceLogic._OnTlogReportGameSpeakDurations()
    _inGameFirstSpeakTime = 0
    _inGameLastSpeakTime = 0
    _inGameLastSpeakChannel = 0
    _inGameSpeakDurations = {}
end

function GVoiceLogic._OnInGameMicrophoneButtonTypeChange(buttonType)
    GVoiceLogInfo("GVoiceLogic._OnTlogReportGameSpeakDurations buttonType", buttonType)
    if not _inGameSpeakDurationFlag then
        GVoiceLogInfo("GVoiceLogic._OnTlogReportGameSpeakDurations not _inGameSpeakDurationFlag")
        return
    end

    local bIsPressButtonType = GVoiceLogic.IsPressButtonType(buttonType)

    local now = os.time()
    local interval = now - _inGameLastSpeakTime

    -- 更新上一个状态的持续时间
    if not _inGameSpeakDurations[_inGameLastSpeakChannel] then
        _inGameSpeakDurations[_inGameLastSpeakChannel] = 0
    end

    --按住说话只统计真正按下的时长
    if bIsPressButtonType then
        if _bLastPressMic then
            _inGameSpeakDurations[_inGameLastSpeakChannel] = _inGameSpeakDurations[_inGameLastSpeakChannel] + interval
        end
    else
        _inGameSpeakDurations[_inGameLastSpeakChannel] = _inGameSpeakDurations[_inGameLastSpeakChannel] + interval
    end


    -- 更新当前状态和时间戳
    _inGameLastSpeakTime = now
    _inGameLastSpeakChannel = buttonType
    _bLastPressMic = _bPressMic
end

function GVoiceLogic._OnTlogReportGameSpeakDurations()
    GVoiceLogInfo("GVoiceLogic._OnTlogReportGameSpeakDurations")
    local tglog = pb.DSChatRealTimeVoiceFlow:New()
    tglog.RoomID = Server.MatchServer:GetDsRoomId()
    tglog.TeamID = Server.MatchServer:GetDsTeamId()
    tglog.FirstStartTime = _inGameFirstSpeakTime
    tglog.MicState = _inGameLastSpeakChannel
    tglog.VoiceDuration = 0
    tglog.GroupVoiceDuration = 0
    tglog.TeamVoiceDuration = 0
    tglog.SideVoiceDuration = 0
    for buttonType, duration in pairs(_inGameSpeakDurations) do
        if buttonType == EGVoiceButtonType.Close or buttonType == EGVoiceButtonType.ClosePress then

        elseif buttonType == EGVoiceButtonType.Team or buttonType == EGVoiceButtonType.TeamPress then
            tglog.GroupVoiceDuration = tglog.GroupVoiceDuration + duration
        elseif buttonType == EGVoiceButtonType.All or buttonType == EGVoiceButtonType.AllPress then
            tglog.TeamVoiceDuration = tglog.TeamVoiceDuration + duration
        elseif buttonType == EGVoiceButtonType.Camp or buttonType == EGVoiceButtonType.CampPress then
            tglog.SideVoiceDuration = tglog.SideVoiceDuration + duration
        end
    end

    tglog.VoiceDuration = tglog.GroupVoiceDuration + tglog.TeamVoiceDuration + tglog.SideVoiceDuration

    --CSFriendVoiceDurationReportReq
    local req = pb.CSFriendVoiceDurationReportReq:New()
    local fCSFriendVoiceDurationReportRes = function(res)
        loginfo("GVoiceLogic._OnTlogReportGameSpeakDurations CSFriendVoiceDurationReportReq res.result:" .. tostring(res.result))
    end
    req.group_voice_duration = tglog.GroupVoiceDuration
    req.team_voice_duration = tglog.TeamVoiceDuration
    req.side_voice_duration = tglog.SideVoiceDuration
    req:Request(fCSFriendVoiceDurationReportRes)
    --tlog send
    logtable(tglog)
    LogAnalysisTool.AddTglog(tglog)
end

--endregion

return GVoiceLogic