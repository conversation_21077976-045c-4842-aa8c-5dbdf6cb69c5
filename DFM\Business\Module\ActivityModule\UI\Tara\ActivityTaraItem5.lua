----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--干员
---@class ActivityTaraItem5 : LuaUIBaseView
local ActivityTaraItem5 = ui("ActivityTaraItem5")
local ActivityTaraHead  = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraHead"
local Config = Module.Activity.Config

function ActivityTaraItem5:Ctor()
    self._wtFHead = self:Wnd("WBP_PatrolAsala_HeadShot", ActivityTaraHead)
    self._wtSelec = self:Wnd("WBP_SlotCompSelected_1"  , UIWidgetBase)
    self._wtFProg = self:Wnd("DFProgressBar_184"       , UIWidgetBase)
    self._wtFName = self:Wnd("DFTextBlock_120"         , UITextBlock)
    self._wtFDesc = self:Wnd("DFTextBlock_42"          , UITextBlock)
    self._wtState = self:Wnd("DFTextBlock_213"         , UITextBlock)
    self._wtFDBtn = self:Wnd("DFButton_53"             , UIButton)
    self._wtFDBtn:Event("OnClicked"  , self._OnClicked  , self)
    self._wtFDBtn:Event("OnHovered"  , self._OnHovered  , self)
    self._wtFDBtn:Event("OnUnhovered", self._OnUnhovered, self)
end

function ActivityTaraItem5:_OnHovered()
    if self._data and self._data.state ~= 2 then
        self._wtSelec:SetVisibility(ESlateVisibility.HitTestSelfOnly)
    end
end

function ActivityTaraItem5:_OnUnhovered()
    self._wtSelec:Collapsed()
end

function ActivityTaraItem5:_OnClicked()
    --请求切换博主
    if self._data then
        --选择查看博主
        Config.evtRefreshTaraPanel:Invoke(self._activityID, self._data, self._index)

        -- if self._data.state == 0 then
        --     --请求切换博主(确认切换博主)
        --     local confirmHandle = function ()
        --         Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Switch)
        --         Server.ActivityServer:SendTaraSwitchReq(self._activityID, self._data.heroId)
        --     end
        --     local hero = nil
        --     for index, item in ipairs(self._items or {}) do
        --         if item.state == 1 then
        --             local count = 0
        --             for _, wechatMoment in ipairs(item.items or {}) do
        --                 if wechatMoment.count and wechatMoment.count > count then
        --                     count = wechatMoment.count
        --                 end
        --             end
        --             if item.count and item.count < count then
        --                 hero = item
        --             end
        --             break
        --         end
        --     end
        --     if hero then
        --         Module.CommonTips:ShowConfirmWindow(General.GetText(Config.Loc.ThisWillInterrupt, hero.name), confirmHandle)
        --     else
        --         confirmHandle()
        --     end
        -- else
        --     --选择查看博主
        --     Config.evtRefreshTaraPanel:Invoke(self._activityID, self._data, self._index)
        -- end
    end
end

function ActivityTaraItem5:InitData(activityID, data, index, items)
    self._activityID = activityID
    self._data  = data
    self._index = index
    self._items = items
    if data then
        self._wtFHead:InitData(activityID, data, index)
        self._wtFName:SetText(General.GetText(data.name))
        self._wtFDesc:SetText(General.GetText(data.title))
        if data.state == 1 then
            self._wtState:SetText(Config.Loc.ActUITextBlock[7])
            self._wtState:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        else
            self._wtState:SetVisibility(ESlateVisibility.Collapsed)
        end
        if data.state == 2 then
            self._wtFProg:SetVisibility(ESlateVisibility.Collapsed)
        else
            self._wtFProg:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        end
        local count = 0
        for _, item in ipairs(data.items or {}) do
            if item.count and item.count >= count then
                count = item.count
            end
        end
        if self.SetStyle then
            self:SetStyle(data.state == 2 and 1 or 0)
        end
        self._wtFProg:SetPercent((data.count or 0)/count)
        self._wtSelec:Collapsed()
    end
    Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Add, self, 2)
end

function ActivityTaraItem5:OnShowBegin()
end

function ActivityTaraItem5:OnHideBegin()
end

function ActivityTaraItem5:OnClose()
end

return ActivityTaraItem5