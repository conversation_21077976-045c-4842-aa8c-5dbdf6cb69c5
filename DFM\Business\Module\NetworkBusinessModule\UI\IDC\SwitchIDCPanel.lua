----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGCloudSDK)
----- LOG FUNCTION AUTO GENERATE END -----------


local SwitchIDCPanel = ui("SwitchIDCPanel")
local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local DFCommonButtonV1S2 = require "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonButtonOnly"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction   = import    ("EGPUINavWidgetFocusedAction")
-- 输入设备相关
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import"EGPInputType"
function SwitchIDCPanel:Ctor()
    self._wtCommonPopWindows = self:Wnd("WBP_CommonPopWindowsV2", CommonPopWindows)
    self._wtCommonPopWindows:SetConfirmBtnType(CommonPopWindows.EConfirmBtnType.CenterConfirm, {
        {btnText = Module.NetworkBusiness.Config.Loc.ConfirmText, fClickCallback = self._OnConfirmBtnClicked, caller = self, bNeedClose = true}
    }, false)
    local fCallbackIns = CreateCallBack(self.CloseSelf,self)
    self._wtCommonPopWindows:BindCloseCallBack(fCallbackIns)

    self._wtScrollBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1", self._OnGetItemCount, self._OnCreateItemWidget)
    self._idcList = {}
    self._selectedIdc = ""
    self:AddLuaEvent(Module.NetworkBusiness.Config.Events.evtOnIdcSelectorChanged, self._OnIdcSelectorChanged, self)
end

function SwitchIDCPanel:OnOpen()
    local curSelectedIdc = Module.NetworkBusiness:GetCurSelectedIdc()
    if curSelectedIdc and curSelectedIdc ~= "" then
        self._selectedIdc = curSelectedIdc
        logerror("[SwitchIDCPanel] OnOpen, curSelectedIdc: %s", curSelectedIdc)
    else
        Module.NetworkBusiness:GetSelectIdcInfo(function(res)
            if res and res.result == 0 then
                self._selectedIdc = res.match_area
                self._wtScrollBox:RefreshVisibleItems()
                logerror("[SwitchIDCPanel] OnOpen, GetSelectIdcInfo, area: %s", self._selectedIdc)
            end
        end)
    end

    Module.NetworkBusiness:GetIdcRtt(function(res)
        if res and res.result == 0 then
            self._idcList = res.area_list or {}
            if self._wtScrollBox then
                self._wtScrollBox:RefreshAllItems()
            end
        else
            logerror("[SwitchIDCPanel] GetIdcRtt failed, result: %d", res.result)
        end
    end)
end

function SwitchIDCPanel:_OnConfirmBtnClicked()
    Server.GameModeServer:SendPrepareMapBoard()
    Server.GameModeServer:RequestTDMMapBoard()
    if self._selectedIdc and self._selectedIdc ~= "" then
        logerror("[SwitchIDCPanel] _OnConfirmBtnClicked, selectedIdc: %s", self._selectedIdc)
        Module.NetworkBusiness:SetCurSelectedIdc(self._selectedIdc)
        Module.NetworkBusiness.Config.Events.evtOnIdcConfirmBtnClicked:Invoke()
    end
    Facade.UIManager:CloseUI(self)
end

function SwitchIDCPanel:_OnGetItemCount()
    if self._idcList and #self._idcList > 0 then
        return #self._idcList
    end
    return 0
end

function SwitchIDCPanel:_OnCreateItemWidget(position, itemWidget)
    local index = position
    local idcItem = self._idcList[index]
    if itemWidget and index and idcItem then
        itemWidget:RefreshItemWidget(idcItem.area,idcItem.rtt, idcItem.area == self._selectedIdc)
    end
end

function SwitchIDCPanel:_OnCreateItemSize()
    
end

function SwitchIDCPanel:OnNavBack()
    return false
end

function SwitchIDCPanel:_OnIdcSelectorChanged(area)
    if area and area ~= "" then
        self._selectedIdc = area
    end
end

-- BEGIN MODIFICATION @ VIRTUOS :
function SwitchIDCPanel:OnShowBegin()
    logwarning("[SwitchIDCPanel][OnShowBegin]...")

    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
end


function SwitchIDCPanel:OnHideBegin()
    logwarning("[SwitchIDCPanel][OnHideBegin]...")

    if IsHD() then 
        self:_EnableGamepadFeature(false)
    end
end
-- END MODIFICATIO

function SwitchIDCPanel:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end

    if bEnable then
        -- 绑定多输入设备切换事件
        if not self._OnNotifyInputTypeChangedHandle then
            self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._OnInputTypeChanged, self))
            local curInputType = WidgetUtil.GetCurrentInputType()
            self:_OnInputTypeChanged(curInputType)
        end

        -- 导航组
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollBox, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:SetScrollRecipient(self._wtScrollBox)
                self._wtNavGroup:AddNavWidgetToArray(self._wtScrollBox)
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end
    else
        if self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end

        self._wtCommonPopWindows:RemoveSummaries()

	    if self._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
            self._OnNotifyInputTypeChangedHandle = nil
        end
    end
   
end


function SwitchIDCPanel:_OnInputTypeChanged(InputType)
    if not IsHD() then
        return 
    end

    if not self._wtCommonPopWindows then
        return
    end

    if InputType == EGPInputType.Gamepad then
        -- 手柄模式下显示额外的底部按键提示
        self._wtCommonPopWindows:AddSummaries({"Select_GamePad"})
        self._wtCommonPopWindows:OverrideGamepadSetting("Common_ButtonLeft_Gamepad", nil, WidgetUtil.EUINavDynamicType.Default, false)
    else
        -- 非手柄模式下，清空不需要的底部栏
        self._wtCommonPopWindows:RemoveSummaries()
    end
end

function SwitchIDCPanel:CloseSelf()
    Facade.UIManager:CloseUI(self)
end

return SwitchIDCPanel