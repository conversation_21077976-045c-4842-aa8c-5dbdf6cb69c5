----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerLevelLoading : LuaUIBaseView
local ActivityMagicTowerLevelLoading = ui("ActivityMagicTowerLevelLoading")
local ActivityMagicTowerTextItem = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerTextItem"
-- local ActivityMagicTowerAStarNode = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarNode"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"

local function testLog(...)
    loginfo("[Magic_Tower] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower] ", ...)
end

function ActivityMagicTowerLevelLoading:Ctor()
    self._wtDesPanel = self:Wnd("wt_DesPanel", UIWidgetBase)
    self._wtDescTxt = self:Wnd("DFComputeTextBlock_67", UITextBlock)
    self._wtDescSecondTxt = self:Wnd("DFTextBlockSecond", UITextBlock)
    self._wtScrollGridBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1", self._OnGetDialogeCount, self._OnProcessDialog)
end

-----------------------------------------------------生命周期-----------------------------------------------------
function ActivityMagicTowerLevelLoading:OnOpen()
    -- Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.MagicTower, true, nil, nil, false, 30)
end

function ActivityMagicTowerLevelLoading:_InitEvent()

end

function ActivityMagicTowerLevelLoading:OnInitExtraData(activityID,sceneID)
    self._activityID = activityID
    self._sceneID = sceneID
end

function ActivityMagicTowerLevelLoading:OnShowBegin()
    self:BindBackAction()
    self:_InitEvent()

    self:_InitData()
    self:_RefreshUI()
end

function ActivityMagicTowerLevelLoading:OnHideBegin()
    self:RemoveAllActions()
    self:RemoveAllLuaEvent()

end

function ActivityMagicTowerLevelLoading:OnClose()
end

function ActivityMagicTowerLevelLoading:OnNavBack()
    Facade.UIManager:CloseUI(self)
end

function ActivityMagicTowerLevelLoading:BindBackAction()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    if self._JumpOverHandle then
        self:RemoveInputActionBinding(self._JumpOverHandle)
        self._JumpOverHandle = nil
    end

    self._backActionHandle = self:AddInputActionBinding(
        "Back",
        EInputEvent.IE_Pressed,
        self.OnNavBack,
        self,
        EDisplayInputActionPriority.UI_Pop
    )

    self._JumpOverHandle = self:AddInputActionBinding(
        "JumpOver", 
        EInputEvent.IE_Pressed, 
        self._OnSpaceBtnClicked, 
        self, EDisplayInputActionPriority.UI_Pop
    )
end

function ActivityMagicTowerLevelLoading:RemoveAllActions()
    if self._backActionHandle then
        self:RemoveInputActionBinding(self._backActionHandle)
        self._backActionHandle = nil
    end

    if self._JumpOverHandle then
        self:RemoveInputActionBinding(self._JumpOverHandle)
        self._JumpOverHandle = nil
    end
end

function ActivityMagicTowerLevelLoading:_RefreshUI()
    self:PlayWidgetAnim(self.WBP_DoctorGames_LevelLoading_in)
    if not self._sceneID then 
        --先播放Desc
        self:_ShowDesc()
    else
        self:_ShowTransition(self._sceneID)
    end
end

function ActivityMagicTowerLevelLoading:_ShowDesc()
    self._wtDesPanel:SelfHitTestInvisible()
    self._wtDescSecondTxt:SelfHitTestInvisible()
    self._wtScrollGridBox:Collapsed()
    local data
    if self.curCycle >= #self._constId then 
        data = self._constId[#self._constId]
    else
        data = self._constId[self.curCycle]
    end
    self._wtDescTxt:SetText(self._constantConfig[data.title].constantValue)
    self._wtDescSecondTxt:SetText(self._constantConfig[data.desc].constantValue)
    --5S后播放专场
    Timer.DelayCall(
        5,
        function()
            self:_ShowTransition(data.trans)
        end
    )
end

function ActivityMagicTowerLevelLoading:_ShowTransition(sceneID)
    self._wtDesPanel:Collapsed()
    self._wtDescSecondTxt:Collapsed()
    self._wtScrollGridBox:SelfHitTestInvisible()
    self._wtScrollGridBox:RefreshAllItems()
end

function ActivityMagicTowerLevelLoading:_OnGetDialogeCount()
    return self._pageData and #self._pageData or 0
end

function ActivityMagicTowerLevelLoading:_OnProcessDialog(position, itemWidget)
    itemWidget:Collapsed()
    local time = self._playTime
    Timer.DelayCall(
        time,
        function()
            itemWidget:SelfHitTestInvisible()
            itemWidget:InitData(self._pageData[position].content)
            itemWidget:PlayAnim()
            if position == #self._pageData then --播放到本页最后一个了
                self._playTime = 0
                if self._page == #self._dialogData then
                    --展示游戏界面
                    Module.Activity:OpenMagicTowerPanel()
                    Facade.UIManager:CloseUI(self)
                    return
                else
                    self._page = self._page + 1
                    self._pageData = self._dialogData[self._page]
                end
                Timer.DelayCall(
                    self._pageData[position].endurance,
                    function()
                        self._wtScrollGridBox:RefreshAllItems()
                    end
                )
            end
        end
    )
    self._playTime = self._playTime + self._pageData[position].endurance
end

-----------------------------------------------------数据区-----------------------------------------------------
function ActivityMagicTowerLevelLoading:_InitData()
    --常量ID写死对应,key对应几周目
    self._constId = {
        [1] = 
        {
            title = 3,
            desc  = 4,
            trans = 9,
        },
        [2] =
        {
            title = 5,
            desc  = 6,
            trans = 11,
        },
        [3] =
        {
            title = 7,
            desc  = 8, 
            trans = 13,
        }
    }
    -- 所有数据先从本地表获取
    self:_InitDataConfig()
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if activityInfo == nil or next(activityInfo) == nil then return end

    -- 基础数据
    self.allInfo = activityInfo.arknights_game_info or {}
    self.curCycle = 1
    if self.allInfo.best_match_record.result and self.allInfo.best_match_record.result == 1 then
        self.curCycle = self.allInfo.best_match_record.cycle + 1
    elseif self.allInfo.best_match_record.time ~= 0 then
        self.curCycle = self.allInfo.best_match_record.cycle
    end

    self._dialogData = {}
    self._page = 1
    self._playTime = 0
    for k,v in ipairs(self._cutSceneConfig) do
        if v.sceneID == (self._sceneID or 1) then
            if not self._dialogData[v.paragraphID] then 
                self._dialogData[v.paragraphID] = {}
            end
            table.insert(self._dialogData[v.paragraphID],v)
        end
    end
    self._pageData = self._dialogData[self._page]
end


function ActivityMagicTowerLevelLoading:_InitDataConfig()
    -- 转场文字配置 ---@type table<int64, MorgenTowerCutSceneConfig>
    self._cutSceneConfig = ConfigManager.GetCutSceneConfigTable()
    -- 其它常量配置 ---@type table<int64, MorgenTowerConstantConfig>
    self._constantConfig = ConfigManager.GetConstantConfigTable()
end

function ActivityMagicTowerLevelLoading:_OnSpaceBtnClicked()
    Module.Activity:OpenMagicTowerPanel()
    Facade.UIManager:CloseUI(self)
end

return ActivityMagicTowerLevelLoading