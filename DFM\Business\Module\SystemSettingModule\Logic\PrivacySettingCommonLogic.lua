----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local PrivacySettingCommonLogic = {}
local SystemUtil = require "DFM.YxFramework.Managers.UI.Util.UISystemUtil"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local UClientPrivacySetting = import "ClientPrivacySetting"
local ClientPrivacySetting = UClientPrivacySetting.Get(GetWorld())

function PrivacySettingCommonLogic.OpenPrivacyPolicy()
    local url = "https://www.playdeltaforce.com/privacy-policy.html"

    -- steam 国区特殊处理
    if DFHD_LUA == 1 then
        if IsBuildRegionCN() and VersionUtil.IsGameChannelSteam() then
            url = "https://df.qq.com/cp/a20240125main/tencent_other_privacy_Steam.shtml"
        end
    end

    if IsBuildRegionGA() then
        --- 根据语言选择协议
        local GarenaLang2Url = Facade.TableManager:GetTable("GarenaLang2Url")
        local langCode = LocalizeTool.GetCurrentCulture()
        loginfo("PrivacySettingCommonLogic.OpenPrivacyPolicy langCode:", langCode)
        
        url = "https://contentgarena-a.akamaihd.net/legal/pp/pp_en.html"
        for k, v in pairs(GarenaLang2Url) do
            if langCode == v.Language then
                url = v.PrivacyPolicyUrl
            end
        end
    end
    loginfo("PrivacySettingCommonLogic.OpenPrivacyPolicy url:", url)
    if PLATFORM_WINDOWS then
		Module.GCloudSDK:LaunchURL(url)
	else
		Module.GCloudSDK:OpenUrl(url)
	end
end

function PrivacySettingCommonLogic.OpenTermsOfUse()
    local url = "https://www.playdeltaforce.com/terms-of-use.html"
    
    -- steam 国区特殊处理
    if DFHD_LUA == 1 then
        if IsBuildRegionCN() and VersionUtil.IsGameChannelSteam() then
            url = "https://df.qq.com/cp/a20240125main/tencent_other_contract_Steam.shtml"
        end
    end
    
    if IsBuildRegionGA() then
        --- 根据语言选择协议
        local GarenaLang2Url = Facade.TableManager:GetTable("GarenaLang2Url")
        local langCode = LocalizeTool.GetCurrentCulture()
        loginfo("PrivacySettingCommonLogic.OpenTermsOfUse langCode:", langCode)
        
        url = "https://contentgarena-a.akamaihd.net/legal/tos/tos_en.html"
        for k, v in pairs(GarenaLang2Url) do
            if langCode == v.Language then
                url = v.TermsConditionsUrl
            end
        end
    end

    if IsPS5() then
        url = "https://www.playdeltaforce.com/sony/terms-of-use.html"
    end

    loginfo("PrivacySettingCommonLogic.OpenTermsOfUse url:", url)
    if PLATFORM_WINDOWS then
		Module.GCloudSDK:LaunchURL(url)
	else
		Module.GCloudSDK:OpenUrl(url)
	end
end

function PrivacySettingCommonLogic.IsInvisible(bInvisible,privacyDataList)
    if ClientPrivacySetting then
        ClientPrivacySetting.bInvisible = bInvisible
        Module.SystemSetting.Events.evtChangeInvisibleState:Invoke()
    end
end

return PrivacySettingCommonLogic
