----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityMagicTowerCombatLogic
local ActivityMagicTowerCombatLogic = {}

local function testLog(...)
    loginfo("[Magic_Tower_Combat_Logic] ", ...)
end

local function testWarningLog(...)
    logwarning("[Magic_Tower_Combat_Logic] ", ...)
end

local function testErrorLog(...)
    logerror("[Magic_Tower_Combat_Logic] ", ...)
end

ActivityMagicTowerCombatLogic.MAX_ROUNDS = 300
ActivityMagicTowerCombatLogic.PREDICT_MAX_ROUNDS = 300
ActivityMagicTowerCombatLogic.MAX_PREDICT_HEALTH = 9999

ActivityMagicTowerCombatLogic.COMBAT_RESULT = {
    WIN = "win",
    LOSE = "lose",
    TIMEOUT = "timeout"
}

-- 计算完整战斗结果（快速战斗模式）
---@param playerData table 玩家数据 {health, attack, defense, hitProb}
---@param enemyConfig table 敌人配置 {health, attack, defense, hitProb}
---@param isNoCritMode boolean 是否使用无暴击模式
---@return table 战斗结果 { result = "win"/"lose"/"timeout", playerFinalHealth = number, battleLog = table }
ActivityMagicTowerCombatLogic.CalculateFullCombatResult = function(playerData, enemyConfig, isNoCritMode)
    isNoCritMode = setdefault(isNoCritMode, false)

    local battleLog = {}
    local currentRound = 1
    local playerHP, enemyHP = playerData.health, enemyConfig.health
    
    while currentRound <= ActivityMagicTowerCombatLogic.MAX_ROUNDS do
        ----------------------------------
        -- 玩家回合
        ----------------------------------
        local playerRound = ActivityMagicTowerCombatLogic.CalculateRoundDamage(
            "player", 
            playerData, 
            enemyConfig, 
            playerHP, 
            enemyHP,
            isNoCritMode
        )
        enemyHP = playerRound.targetHP
        table.insert(battleLog, playerRound)
        
        if enemyHP <= 0 then
            return {
                result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.WIN,
                playerFinalHealth = playerHP,
                battleLog = battleLog
            }
        end
        
        ----------------------------------
        -- 敌人回合
        ----------------------------------
        local enemyRound = ActivityMagicTowerCombatLogic.CalculateRoundDamage(
            "enemy", 
            enemyConfig, 
            playerData, 
            enemyHP, 
            playerHP,
            isNoCritMode
        )
        playerHP = enemyRound.targetHP
        table.insert(battleLog, enemyRound)
        
        if playerHP <= 0 then
            return {
                result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.LOSE,
                playerFinalHealth = playerHP,
                battleLog = battleLog
            }
        end
        
        currentRound = currentRound + 1
    end

    -- 达到最大回合数判负
    return {
        result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.TIMEOUT,
        playerFinalHealth = playerHP,
        battleLog = battleLog
    }
end

ActivityMagicTowerCombatLogic.CalculateRoundDamage = function(attacker, attackerStats, defenderStats, attackerHP, defenderHP, isNoCritMode)
    isNoCritMode = setdefault(isNoCritMode, false)
    
    if isNoCritMode then
        return ActivityMagicTowerCombatLogic._CalculateRoundDamageNoCritical(attacker, attackerStats, defenderStats, attackerHP, defenderHP)
    else
        return ActivityMagicTowerCombatLogic._CalculateRoundDamage(attacker, attackerStats, defenderStats, attackerHP, defenderHP)
    end
end

-- 计算单回合伤害（回合模式）
---@param attacker string attacker = "player"/"enemy"
---@param attackerStats table 攻击方属性 {attack, defense, hitProb}
---@param defenderStats table 防御方属性 {attack, defense, hitProb}
---@param attackerHP number 攻击方当前HP
---@param defenderHP number 防御方当前HP
---@return table 回合结果 { attacker = string, isCrit = boolean, damage = number, targetHP = number }
ActivityMagicTowerCombatLogic._CalculateRoundDamage = function(attacker, attackerStats, defenderStats, attackerHP, defenderHP)
    local isCrit = math.random(1, 100) <= attackerStats.hitProb
    local damage = attackerStats.attack * (isCrit and 3 or 1)
    local finalDamage = math.max(1, damage - defenderStats.defense)
    
    return {
        attacker = attacker,
        isCrit = isCrit,
        damage = finalDamage,
        targetHP = math.max(0, defenderHP - finalDamage)
    }
end

-- 无暴击计算单回合伤害（回合模式）
ActivityMagicTowerCombatLogic._CalculateRoundDamageNoCritical = function(attacker, attackerStats, defenderStats, attackerHP, defenderHP)
    local adjustedAttack = attackerStats.attack * (1 + attackerStats.hitProb * 1.5 / 100)
    local damage = adjustedAttack
    local finalDamage = math.max(1, damage - defenderStats.defense)
    
    return {
        attacker = attacker,
        isCrit = false,
        damage = finalDamage,
        targetHP = math.max(0, defenderHP - finalDamage)
    }
end

-- 记录战斗日志
---@param log table 战斗日志
ActivityMagicTowerCombatLogic.RecordBattleLog = function(log)
    if not log then return end
    testLog("---------- BATTLE LOG ----------")
    for i, round in ipairs(log.battleLog or {}) do
        local critMsg = round.isCrit and "[CRIT!] " or ""
        testLog(string.format("回合 %s: %s 造成 %s 伤害 (%s对方生命剩余: %s)", 
            i, 
            tostring(round.attacker), 
            tostring(round.damage), 
            critMsg,
            tostring(round.targetHP)))
    end
    if log.result then
        testLog("Result: " .. log.result)
    end
end

-- 计算预测战斗结果
---@param playerData table 玩家数据 {health, attack, defense, hitProb}
---@param enemyConfig table 敌人配置 {health, attack, defense, hitProb}
---@return table 战斗结果 { result = "win"/"lose"/"timeout", playerChangeHealth = number }
ActivityMagicTowerCombatLogic._CalculateCombatPrediction = function(playerData, enemyConfig)
    local enemyStatus = {
        health = enemyConfig.health,
        attack = enemyConfig.attack,
        defense = enemyConfig.defense,
        hitProb = enemyConfig.hitProb
    }
    
    local playerPredictAttack = playerData.attack * (1 + 2 * (playerData.hitProb / 100))
    local enemyPredictAttack = enemyStatus.attack * (1 + 2 * (enemyStatus.hitProb / 100))
    
    local playerHP = playerData.health
    local enemyHP = enemyStatus.health
    local currentRound = 0
    local outFlag = 0

    while currentRound < ActivityMagicTowerCombatLogic.MAX_ROUNDS do
        local playerDamage = math.max(1, playerPredictAttack - enemyStatus.defense)
        enemyHP = enemyHP - playerDamage
        if enemyHP <= 0 then
            outFlag = 1
            break
        end
        
        local enemyDamage = math.max(1, enemyPredictAttack - playerData.defense)
        playerHP = playerHP - enemyDamage
        -- if playerHP <= 0 then break end

        currentRound = currentRound + 1
    end
    
    local playerChangeHealth = playerHP - playerData.health
    local result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.TIMEOUT

    if playerHP <= 0 then
        result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.LOSE
    else
        if outFlag == 1 then
            result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.WIN
        else
            result = ActivityMagicTowerCombatLogic.COMBAT_RESULT.TIMEOUT
        end
    end

    loginfo(string.format("[neomtzhang] 敌人名字：%s,   预测结果: %s,   扣除生命: %s   回合：%s", tostring(enemyConfig.name), result, playerChangeHealth, currentRound))
    
    return {
        result = result,
        playerChangeHealth = playerChangeHealth,
        rounds = currentRound,
        playerHitProb = playerData.hitProb,
        enemyHitProb = enemyStatus.hitProb
    }
end

-- 判断预测结果的类型
---@return number 类型[0: 其它情况 1：高度不确定 2：必然失败]
ActivityMagicTowerCombatLogic.GetCombatResultType = function(prediction, playerData, enemyConfig, isNoCritMode)
    if not prediction or not enemyConfig then return 0 end
    
    -- 首先检查是否是必然失败
    if prediction.result == ActivityMagicTowerCombatLogic.COMBAT_RESULT.LOSE or prediction.result == ActivityMagicTowerCombatLogic.COMBAT_RESULT.TIMEOUT then
        return 2
    end

    -- 无暴击模式下不会有高度不确定的情况
    if isNoCritMode then return 0 end
    
    -- 然后检查是否是高度不确定
    -- 条件1: 计算的掉血量超过玩家当前生命的40%
    local healthThreshold = playerData.health * 0.4
    local condition1 = math.abs(prediction.playerChangeHealth) > healthThreshold
    
    -- 条件2: 在规定的预测战斗回合数
    local condition2 = prediction.rounds < ActivityMagicTowerCombatLogic.PREDICT_MAX_ROUNDS
    
    -- 条件3: min(玩家暴击率，abs(玩家的暴击率 - 100%)) + min(敌人暴击率，abs(敌人暴击率 - 100%)) >= 10%
    local playerDiff = math.min(prediction.playerHitProb or 0, math.abs((prediction.playerHitProb or 0) - 100))
    local enemyDiff = math.min(prediction.enemyHitProb or 0, math.abs((prediction.enemyHitProb or 0) - 100))
    local condition3 = (playerDiff + enemyDiff) >= 10

    if condition1 and condition2 and condition3 then
        return 1
    end
    
    -- 其它情况
    return 0
end

return ActivityMagicTowerCombatLogic