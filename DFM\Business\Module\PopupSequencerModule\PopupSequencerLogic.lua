----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPopupSequencer)
----- LOG FUNCTION AUTO GENERATE END -----------



-- local Promise   = require "DFM.Business.DataStruct.Common.Base.Promise"
local Promise   = require "DFM.YxFramework.Plugin.Promise.Promise"
local Deep      = require "DFM.Business.DataStruct.Common.Base.Deep"
local Table     = require "DFM.Business.DataStruct.Common.Base.Table"
local Sort      = require "DFM.Business.DataStruct.Common.Base.Sort"
local TokenGen  = require "DFM.Business.DataStruct.Common.Base.TokenGen":New()

local PopupSequencerLogic = {}

local PopupReplyStr = {
    [0] = "Continue",
    [1] = "Stop",
    [2] = "Jump",
}

---@param timingID           any
---@param extraParam         any
---@param sequence           PopupProvider[]
---@param sequenceEndPromise Promise
function PopupSequencerLogic._AdvanceSequence(timingID, extraParam, sequence, sequenceEndPromise)

    local itemToShow ---@type PopupProvider
    while #sequence > 0 do
        local item = table.remove(sequence, 1)
        if PopupSequencerLogic._IsCurrentlyAllowed(item) then
            itemToShow = item
            break
        end
    end
    if not itemToShow then
        sequenceEndPromise:Resolve({reply = EPopupReply.Continue})
        return
    end

    local token = TokenGen:Get()

    local interruptPromise = Promise:NewIns(function()end)
    Module.PopupSequencer._pendingInterruptPromises[token] = interruptPromise
    interruptPromise.__interruptEvents       = Table.Exchange(itemToShow.interruptEvents)
    interruptPromise.__popupProviderID       = itemToShow.id

    local popupPromise = itemToShow.fPopup(timingID, extraParam, Module.PopupSequencer._pendingInterruptPromises[token])

    if not popupPromise then
        popupPromise = Promise:NewIns(function()end)
        popupPromise:Resolve({reply = EPopupReply.Continue})
    end
    Module.PopupSequencer._pendingPopupPromises[token] = popupPromise

    -- Add pause state in guide module when requested
    if itemToShow.pauseGuide then
        Module.Guide.Field:AddPauseGuideUINum("PauseByPopupSequencer")
    end

    popupPromise:Then(
        ---@param result PopupResult
        function(result)
            Module.Guide.Field:ReducePauseGuideUINum("PauseByPopupSequencer")

            result = result or {reply = EPopupReply.Continue}
            logwarning("[PopupSequencer] ", itemToShow.id, " reply = ", PopupReplyStr[result.reply])

            Module.PopupSequencer._pendingPopupPromises[token]     = nil
            Module.PopupSequencer._pendingInterruptPromises[token] = nil
            
            if result.reply == EPopupReply.Continue then
                PopupSequencerLogic._AdvanceSequence(timingID, extraParam, sequence, sequenceEndPromise)
            elseif result.reply == EPopupReply.Jump then
                sequenceEndPromise:Resolve({reply = EPopupReply.Jump})
            elseif result.reply == EPopupReply.Stop then
                sequenceEndPromise:Resolve({reply = EPopupReply.Stop})
            end
        end
    )
end


---@param popupProvider PopupProvider
function PopupSequencerLogic._IsCurrentlyAllowed(popupProvider)
    logwarning("[PopupSequencer] _IsCurrentlyAllowed - ", popupProvider.id)

    for _, conditionID in pairs(popupProvider.cancelConditions) do
        local shouldCancel = false
        if Module.PopupSequencer._conditionProvider[conditionID] and
        Module.PopupSequencer._conditionProvider[conditionID]() then
            shouldCancel = true
        end
        logwarning("[PopupSequencer]      ", conditionID, " = ", shouldCancel)

        if shouldCancel then
            return false
        end
    end
    return true
end

return PopupSequencerLogic