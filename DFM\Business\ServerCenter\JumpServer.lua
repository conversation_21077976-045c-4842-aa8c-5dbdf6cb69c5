----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSJump)
----- LOG FUNCTION AUTO GENERATE END -----------

local UDFMGameLogin = import "DFMGameLogin"
---@class JumpServer : ServerBase
local JumpServer = class("JumpServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local function log(...)
    print("lzw", "[JumpServer]", ...)
end

local JumpInfoDefine = MakeDynamicUDClass(
    {"JumpId", FieldType.Integer},
    {"JumpFirstEnum", FieldType.Integer},
    {"JumpSecendEnum", FieldType.Integer},
    {"JumpThridEnum", FieldType.Integer},
    {"JumpFourthEnum", FieldType.Integer},
    {"ExParams", FieldType.String},
    {"StartTime", FieldType.Integer},
    {"EndTime", FieldType.Integer},
    {"Deeplink", FieldType.String}
)

function JumpServer:Ctor()
    loginfo("JumpServer:Ctor")
    --跳转表数据
    self._JumpInfo = {}
end

function JumpServer:OnDestroyServer()
end

function JumpServer:OnInitServer()
    self:_InitData()
end

function JumpServer:FetchServerData()
    self:FetchJumpInfoData()
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
---------------------------------------------------------------------------------
function JumpServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function JumpServer:OnLoadingGame2Frontend(gameFlowType)
    self:_InitData()
    self:FetchServerData()
end

function JumpServer:OnLoadingFrontend2Game(gameFlowType)
    self._JumpInfo = {}
end

function JumpServer:_InitData()
    local jumpTable = Facade.TableManager:GetTable("Module/JumpConfig")
    for _, info in pairs(jumpTable) do
        if not self._JumpInfo[info.JumpId] then
            local newJumpInfo = JumpInfoDefine:new()
            newJumpInfo.JumpId = info.JumpId
            newJumpInfo.JumpFirstEnum = info.JumpFirstEnum
            newJumpInfo.JumpSecendEnum = info.JumpSecendEnum
            newJumpInfo.JumpThridEnum = info.JumpThridEnum
            newJumpInfo.JumpFourthEnum = info.JumpFourthEnum
            newJumpInfo.ExParams = info.ExParams
            newJumpInfo.StartTime = info.StartTime ~= "" and TimeUtil.GetTimeStame(info.StartTime) or 0
            newJumpInfo.EndTime = info.EndTime ~= "" and TimeUtil.GetTimeStame(info.EndTime) or 0
            newJumpInfo.Deeplink = info.Deeplink or ""
            self._JumpInfo[info.JumpId] = newJumpInfo
        end
    end
end

---@param JumpId
---@return any
function JumpServer:GetJumpConfigById(JumpId)
    if JumpId == nil then
        return nil
    end
    local info = self._JumpInfo[JumpId]
    if info then
        return info
    end
    return nil
end

function JumpServer:GetJumpIdByDeepline(deeplink)
    local jumpId = 0
    for key, value in pairs(self._JumpInfo) do
        if value.Deeplink == deeplink  then
            jumpId = key
            break
        end
    end
    return jumpId
end

function JumpServer:FetchJumpInfoData()
    local fOnPlayerGetJumpConfigDataRes = function(res)
        if res.result == 0 then
            local Infos = res.jump_config_datas
            for index, info in ipairs(Infos) do
                ---@class pb_JumpConfigData : ProtoBase
                ---@field public jump_id number
                ---@field public desc string
                ---@field public jump_first_enum number
                ---@field public jump_secend_enum number
                ---@field public jump_thrid_enum number
                ---@field public jump_fourth_enum number
                ---@field public ex_params string
                ---@field public row_description string
                ---@field public start_time string
                ---@field public end_time string
                ---@field public parsed_start_time number
                ---@field public parsed_end_time number
                local jumpInfo = self._JumpInfo[info.jump_id]
                if jumpInfo then
                    jumpInfo.JumpId = info.jump_id
                    jumpInfo.JumpFirstEnum = info.jump_first_enum
                    jumpInfo.JumpSecendEnum = info.jump_secend_enum
                    jumpInfo.JumpThridEnum = info.jump_thrid_enum
                    jumpInfo.JumpFourthEnum = info.jump_fourth_enum
                    jumpInfo.ExParams = info.ex_params
                    jumpInfo.StartTime = 0
                    jumpInfo.EndTime = 0
                    if info.parsed_start_time and info.parsed_start_time > 0 then
                        jumpInfo.StartTime = info.parsed_start_time
                    end
                    if info.parsed_end_time and info.parsed_end_time > 0 then
                        jumpInfo.EndTime = info.parsed_end_time
                    end
                    jumpInfo.Deeplink = info.deeplink
                else
                    local newJumpInfo = JumpInfoDefine:new()
                    newJumpInfo.JumpId = info.jump_id
                    newJumpInfo.JumpFirstEnum = info.jump_first_enum
                    newJumpInfo.JumpSecendEnum = info.jump_secend_enum
                    newJumpInfo.JumpThridEnum = info.jump_thrid_enum
                    newJumpInfo.JumpFourthEnum = info.jump_fourth_enum
                    newJumpInfo.ExParams = info.ex_params
                    newJumpInfo.StartTime = 0
                    newJumpInfo.EndTime = 0
                    newJumpInfo.Deeplink = info.deeplink
                    self._JumpInfo[info.jump_id] = newJumpInfo
                    if info.parsed_start_time and info.parsed_start_time > 0 then
                        self._JumpInfo[info.jump_id].StartTime = info.parsed_start_time
                    end
                    if info.parsed_end_time and info.parsed_end_time > 0 then
                        self._JumpInfo[info.jump_id].EndTime = info.parsed_end_time
                    end
                end
                --log("[weixinhuang] CSPlayerGetJumpConfigDataReq JumpID=" .. tostring(info.JumpID))
            end
        end
    end

    local req = pb.CSPlayerGetJumpConfigDataReq:New()
    req:Request(fOnPlayerGetJumpConfigDataRes)
end

return JumpServer
