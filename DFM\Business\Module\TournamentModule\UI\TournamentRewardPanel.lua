----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRanking)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class TournamentRewardPanel : LuaUIBaseView
local TournamentRewardPanel = ui("TournamentRewardPanel")
local TournamentConfig = Module.Tournament.Config
local TournamentLogic = require "DFM.Business.Module.TournamentModule.Logic.TournamentLogic"
local UKismetInputLibrary = import "KismetInputLibrary"
local ECarouselV2ScrollDirection = import "ECarouselV2ScrollDirection"
local CommonWidgetConfig=Module.CommonWidget.Config

local RankModeType=Module.Ranking.Config.RankModeType
local itemSizeHD=FVector2D(256,256)
local itemSizeMobile=FVector2D(192,192)
local itemSizeSeasonalHD=FVector2D(155,155)
local itemSizeSeasonalMobile=FVector2D(128,128)
local rewardRegion={levelReward=10141001,seasonalReward=10141002}
function TournamentRewardPanel:Ctor()
    loginfo("TournamentRewardPanel:Ctor")
    self._wtRightBtn=self:Wnd("DFButton_RightBtn",UIButton)
    self._wtLeftBtn=self:Wnd("DFButton_LeftBtn",UIButton)
    self._wtRewardBox=self:Wnd("DFHorizontalBox_RewardBox",UIWidgetBase)
    self._wtRewardUnlockText=self:Wnd("DFTextBlock_RankGainTips",UITextBlock)
    self._wtLevelTextMid=self:Wnd("DFTextBlock_Mid",UITextBlock)
    self._wtLevelTextLeft=self:Wnd("DFTextBlock_Left",UITextBlock)
    self._wtLevelTextRight=self:Wnd("DFTextBlock_Right",UITextBlock)
    self._wtSeasonRewardBox=self:Wnd("DFHorizontalBox_UnlockBox",UIWidgetBase)
    self._wtSeasonRewardUnlockText=self:Wnd("DFTextBlock",UITextBlock)
    self._wtSeasonRewardInfoBox=self:Wnd("DFVerticalBox_0",UIWidgetBase)
    self._wtRewardSeparateLine=self:Wnd("DFImage_De3",UIWidgetBase)
    self._wtRewardBGImage=self:Wnd("DFImage_274",UIImage)
    self._wtRewardCarouselProV2=UIUtil.WndCarouselProV2_Scroll(self,"DFExCommonCarouselProV2_34",self._OnGetCarouselV2ItemCount,self._OnProcessCarouselV2ItemWidget,self._OnCarouselV2IndexChanged)

    --self._wtRewardBGImage:Collapsed()
    self._wtRightBtn:Event("OnClicked",self._OnSwitchBtnClicked,self,1)
    self._wtLeftBtn:Event("OnClicked",self._OnSwitchBtnClicked,self,-1)
    self._rankingRankRewardsTable=Module.Ranking:GetSortedRewardsTable()
    self._tournamentRewardsTable=Module.Tournament:GetSortedRewardsTable()
    self._commanderRewardsTable=Module.Tournament:GetCommanderSortedRewardsTable()
    self._rewardType2RewardTable={
        [RankModeType.Ranking]=self._rankingRankRewardsTable,
        [RankModeType.Tournament]=self._tournamentRewardsTable,
        [RankModeType.Commander]=self._commanderRewardsTable
    }
    
    Module.CommonBar:RegStackUITopBarStyle(self,Module.CommonBar.Config.TopBarStyle.HideInfo)
    
end

function TournamentRewardPanel:OnInitExtraData(selectedMajorLevel,rankMode)
    loginfo("TournamentRewardPanel:OnInitExtraData",selectedMajorLevel,rankMode)
    local majorLevel=0
    if rankMode==RankModeType.Ranking then
        majorLevel=Server.RankingServer:GetMajorLevel()
    elseif rankMode==RankModeType.Tournament then
        majorLevel=Server.TournamentServer:GetMajorLevel()
    else
        majorLevel=Server.TournamentServer:GetCommanderMajorLevel()
    end
    selectedMajorLevel=selectedMajorLevel or majorLevel
    local selectedIndex=self:RewardLeveltoIndex(selectedMajorLevel,rankMode)
    self._targetIndex=math.max(selectedIndex,1)
    self._rankMode=rankMode

end

function TournamentRewardPanel:OnOpen()
    loginfo("TournamentRewardPanel:OnOpen")
    self._wtRewardCarouselProV2:RefreshAllItems()
    if self._targetIndex~=1 then
        self._switchToTargetIndexDelayHandle=Timer.DelayCall(0.1,function(self)
            self._wtRewardCarouselProV2:TurnPageOneByOne(self._targetIndex-1, ECarouselV2ScrollDirection.Carousel_Left, 0)
        end,self)
    end
    
    if self._rankMode==RankModeType.Ranking then
        self:AddLuaEvent(Server.RankingServer.Events.evtSeasonRankInfoUpdated,self._OnSeasonInfoUpdated,self)
    else
        self:AddLuaEvent(Server.TournamentServer.Events.evtTournamentInfoUpdated,self._OnSeasonInfoUpdated,self)
    end
    if self._rankMode==RankModeType.Ranking then
        Module.CommonBar:RegStackUITopBarTitle(self,Module.Ranking.Config.Loc.RewardTitle)
        Module.CommonBar:ChangeBackBtnText(Module.Ranking.Config.Loc.RewardTitle)
    else
        Module.CommonBar:RegStackUITopBarTitle(self,Module.Tournament.Config.Loc.RewardTitle)
        Module.CommonBar:ChangeBackBtnText(Module.Tournament.Config.Loc.RewardTitle)
    end
end

function TournamentRewardPanel:OnShowBegin()

    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    -- END MODIFICATION
end

function TournamentRewardPanel:OnShow()
    loginfo("TournamentRewardPanel:OnShow")

end

-- BEGIN MODIFICATION @ VIRTUOS
function TournamentRewardPanel:OnHideBegin()

    if IsHD() then
        self:_DisableGamepadInputs()
    end
end
-- END MODIFICATION

function TournamentRewardPanel:OnClose()
    loginfo("TournamentRewardPanel:OnClose")

    Facade.UIManager:ClearSubUIByParent(self,self._wtRewardBox)
    Facade.UIManager:ClearSubUIByParent(self,self._wtSeasonRewardBox)
    self:RemoveAllLuaEvent()
    Module.ItemDetail:CloseAllPopUI()
    Timer.CancelDelay(self._switchToTargetIndexDelayHandle)
end

function TournamentRewardPanel:_CloseSelf()
    loginfo("TournamentRewardPanel:_CloseSelf")

    Facade.UIManager:CloseUI(self)

end

function TournamentRewardPanel:_OnGetCarouselV2ItemCount()
    local itemCount=0
    if self._rankMode then
        itemCount=table.nums(self._rewardType2RewardTable[self._rankMode] or {})
    end
    return itemCount
end

--index从0开始
function TournamentRewardPanel:_OnProcessCarouselV2ItemWidget(carouselProWidget, itemWidget, index)
    loginfo("TournamentRewardPanel:_OnProcessCarouselV2ItemWidget",index)
    local rewardTable=self._rewardType2RewardTable[self._rankMode]
    itemWidget:SetInfo(rewardTable and rewardTable[index+1],self._rankMode)
end

--index从0开始
function TournamentRewardPanel:_OnCarouselV2IndexChanged(carouselProWidget, itemWidget, preIdx, curIdx)
    loginfo("TournamentRewardPanel:_OnCarouselV2IndexChanged",preIdx,curIdx)
    self._selectedIndex=curIdx+1
    self:_RefreshMajorNames(self._selectedIndex)
    self:_RefreshRewardItems(self._selectedIndex)
end

function TournamentRewardPanel:_OnSwitchBtnClicked(indexOffset)
    if indexOffset>0 then
        self._wtRewardCarouselProV2:NextPage()
    else
        self._wtRewardCarouselProV2:PreviousPage()
    end

end

function TournamentRewardPanel:RewardLeveltoIndex(level,rankMode)
    loginfo("TournamentRewardPanel:RewardLeveltoIndex",level,rankMode)
    if rankMode==RankModeType.Ranking then
        local index=1
        for k,v in pairs(Module.Ranking:GetSortedRewardsTable() or {})do
            if v.ConnectedMajorRank==level then
                return index
            end
            index=index+1
        end
    elseif rankMode==RankModeType.Tournament then
        local index=1
        for k,v in pairs(Module.Tournament:GetSortedRewardsTable() or {})do
            if v.TierID==level then
                return index
            end
            index=index+1
        end
    else
        local index=1
        for k,v in pairs(Module.Tournament:GetCommanderSortedRewardsTable() or {})do
            if v.TierID==level then
                return index
            end
            index=index+1
        end
    end
    return 1
end

function TournamentRewardPanel:RewardIndextoLevel(index,rankMode)
    loginfo("TournamentRewardPanel:RewardIndextoLevel",index,rankMode)
    index=math.floor(index)
    local level=1
    if rankMode==RankModeType.Ranking then
        local rewardInfo=self._rankingRankRewardsTable[index]
        if rewardInfo then
            level=rewardInfo.ConnectedMajorRank
        else
            logerror("TournamentRewardPanel:RewardIndextoLevel rewardInfo is nil!!!")
            level=self._rankingRankRewardsTable[1] and self._rankingRankRewardsTable[1].ConnectedMajorRank
        end 
    elseif rankMode==RankModeType.Tournament then
        local rewardInfo=self._tournamentRewardsTable[index]
        if rewardInfo then
            level=rewardInfo.TierID
        else
            logerror("TournamentRewardPanel:RewardIndextoLevel rewardInfo is nil!!!")
            level=self._tournamentRewardsTable[1] and self._tournamentRewardsTable[1].TierID

        end
    else
        local rewardInfo=self._commanderRewardsTable[index]
        if rewardInfo then
            level=rewardInfo.TierID
        else
            logerror("TournamentRewardPanel:RewardIndextoLevel rewardInfo is nil!!!")
            level=self._commanderRewardsTable[1] and self._commanderRewardsTable[1].TierID
        end
    end
    return level
end

function TournamentRewardPanel:_OnSeasonInfoUpdated()
    loginfo("TournamentRewardPanel:_OnSeasonInfoUpdated")
    self:_RefreshRewardItems(self._selectedIndex)
end

function TournamentRewardPanel:_RefreshRewardItems(selectedIndex)
    if self._rankMode==RankModeType.Ranking then
        self:_RefreshRewardItemsRanking(selectedIndex)
    elseif self._rankMode==RankModeType.Tournament then
        self:_RefreshRewardItemsTournament(selectedIndex)
    else
        self:_RefreshRewardItemsCommander(selectedIndex)
    end

    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() and self._navGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
    end
    --END MODIFICATION
end

function TournamentRewardPanel:_RefreshMajorNames(selectedIndex)
    local maxIndex=table.nums(self._rewardType2RewardTable[self._rankMode] or {})
    local leftIndex=selectedIndex-1
    local rightIndex=selectedIndex+1
    if leftIndex<1 then
        leftIndex=maxIndex
        self._wtLevelTextLeft:Collapsed()
        self._wtLeftBtn:Collapsed()
    else
        self._wtLevelTextLeft:SelfHitTestInvisible()
        self._wtLeftBtn:Visible()
    end
    if rightIndex>maxIndex then
        rightIndex=1
        self._wtLevelTextRight:Collapsed()
        self._wtRightBtn:Collapsed()
    else
        self._wtLevelTextRight:SelfHitTestInvisible()
        self._wtRightBtn:Visible()
    end
    local fSetMajorItemInfo=CreateCallBack(function(self,majorText,majorIndex,rankMode)
        if rankMode==RankModeType.Ranking then
            local majorLevel=self:RewardIndextoLevel(majorIndex,RankModeType.Ranking)
            local majorData=Module.Ranking:GetMajorRankData(majorLevel) or {}
            majorText:SetText(majorData.MajorRankName or "?")
        elseif rankMode==RankModeType.Tournament then
            local majorLevel=self:RewardIndextoLevel(majorIndex,RankModeType.Tournament)
            local rankData=Module.Tournament:GetRankDataByMajor(majorLevel) or {}
            majorText:SetText(rankData.Type or "?")
        else
            local majorLevel=self:RewardIndextoLevel(majorIndex,RankModeType.Commander)
            local rankData=Module.Tournament:GetCommanderRankDataByMajor(majorLevel) or {}
            majorText:SetText(rankData.Type or "?")
        end
    end,self)
    
    fSetMajorItemInfo(self._wtLevelTextMid,selectedIndex,self._rankMode)
    fSetMajorItemInfo(self._wtLevelTextLeft,leftIndex,self._rankMode)
    fSetMajorItemInfo(self._wtLevelTextRight,rightIndex,self._rankMode)
    
end

function TournamentRewardPanel:_RefreshRewardItemsRanking(selectedIndex)
    selectedIndex=math.floor(selectedIndex)
    local rewardInfo=self._rankingRankRewardsTable[selectedIndex]
    local maxMajorLevel=Server.RankingServer:GetMaxMajorLevel() 
    local selectedMajorLevel=rewardInfo and rewardInfo.ConnectedMajorRank or Server.RankingServer:GetMajorLevel()
    if rewardInfo then
        Module.ItemDetail:CloseAllPopUI()
        local rewardUnlockInfo=self:_GetRewardUnlockInfoByLevel(selectedMajorLevel,RankModeType.Ranking)
        local unlockDesc="?"
        if rewardUnlockInfo and rewardUnlockInfo.needGameCount==0 then
            unlockDesc=StringUtil.PluralTextFormat(Module.Ranking.Config.Loc.RewardUnlockDescLevelOnly,
            {levelName=rewardUnlockInfo.levelText})
        else
            unlockDesc=StringUtil.PluralTextFormat(Module.Ranking.Config.Loc.RewardUnlockDesc,
            {levelName=rewardUnlockInfo.levelText,curCount=rewardUnlockInfo.curGameCount,needCount=rewardUnlockInfo.needGameCount,gameCount=rewardUnlockInfo.needGameCount})
        end
        self._wtRewardUnlockText:SetText(unlockDesc)

        self._rewardIdList=self._rewardIdList or {}
        for k,v in pairs(self._rewardIdList)do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,v)
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:Collapsed()
            end
        end
        local index=1
        for k,v in pairs(rewardInfo.Rewards or {})do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,self._rewardIdList[index] or -1)
            if not weakIns then
                weakIns,InsId=Module.CommonWidget:CreateIVCommonItemTemplate(self,self._wtRewardBox,index)
                self._rewardIdList[index]=InsId
            end
            index=index+1
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SelfHitTestInvisible()
                local id,num=Module.Ranking:ParseRewardStr(v)
                local item=ItemBase:New(id,num)
                uiIns:InitItem(item)
                local mode=IsHD() and Module.CommonWidget.Config.EIVItemViewMode.CommonViewItemViewB or Module.CommonWidget.Config.EIVItemViewMode.CommonViewItemViewD
                uiIns:ChangeDefaultMode(mode)
                local itemSize=IsHD() and itemSizeHD or itemSizeMobile
                uiIns:ChangeDefaultSize(itemSize)
                uiIns:SetCppValue("bPreciseClick", false)--手游滑动点击会导致点击不响应的问题，这里屏蔽掉PreciseClick
                
                uiIns:StopIVAnimation("WBP_Itemview_LightEffect_01_Loop")
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,false)
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.GetMask,false)
                uiIns:SetSelected(uiIns.item,false)

                if selectedMajorLevel<=maxMajorLevel and rewardUnlockInfo.curGameCount>=rewardUnlockInfo.needGameCount then
                    if table.contains(Server.RankingServer:GetNotReceivedLevels(),rewardInfo.ConnectedMajorRank) then--未领取
                        local itemList={}
                        for i,j in pairs(rewardInfo.Rewards)do
                            local id,num=Module.Ranking:ParseRewardStr(j)
                            local item=ItemBase:New(id,num)
                            table.insert(itemList,item)
                        end
                        self:BindNotReceivedClick(uiIns, selectedMajorLevel, itemList, RankModeType.Ranking,rewardRegion.levelReward)
                    else--已领取
                        self:BindReceivedClick(uiIns, selectedMajorLevel,rewardRegion.levelReward)
                    end
                else--未达成
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, true,rewardRegion.levelReward)
                end
            end
        end

    end
    local seasonalRewardInfo=Module.Ranking:GetSeasonalRewardsInfo(selectedMajorLevel)
    if seasonalRewardInfo then
        if not seasonalRewardInfo.Rewards or table.nums(seasonalRewardInfo.Rewards)==0 then
            self._wtSeasonRewardInfoBox:Collapsed()
            self._wtRewardSeparateLine:Collapsed()
            return
        end
        self._wtSeasonRewardInfoBox:SelfHitTestInvisible()
        self._wtRewardSeparateLine:SelfHitTestInvisible()
        self._seasonalRewardIdList=self._seasonalRewardIdList or {}
        for k,v in pairs(self._seasonalRewardIdList)do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,v)
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:Collapsed()
            end
        end
        local index=1
        for k,v in pairs(seasonalRewardInfo.Rewards or {})do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,self._seasonalRewardIdList[index] or -1)
            if not weakIns then
                weakIns,InsId=Facade.UIManager:AddSubUI(self,UIName2ID.IVCommonItemTemplate,self._wtSeasonRewardBox,index+10)
                self._seasonalRewardIdList[index]=InsId
            end
            index=index+1
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SelfHitTestInvisible()
                local id,num=Module.Ranking:ParseRewardStr(v)
                local item=ItemBase:New(id,num)
                uiIns:InitItem(item)
                local itemSize=IsHD() and itemSizeSeasonalHD or itemSizeSeasonalMobile
                uiIns:ChangeDefaultSize(itemSize)
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,false)
                if maxMajorLevel<selectedMajorLevel then
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, true,rewardRegion.seasonalReward)
                    self._wtSeasonRewardUnlockText:Collapsed()
                else
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, false,rewardRegion.seasonalReward)
                    self._wtSeasonRewardUnlockText:SelfHitTestInvisible()
                end
            end
        end
    else
        logwarning("TournamentRewardPanel:_RefreshRewardItemsRanking, seasonalRewardInfo is nil!",selectedMajorLevel)
        self._wtSeasonRewardInfoBox:Collapsed()
        self._wtRewardSeparateLine:Collapsed()
    end
        
end

function TournamentRewardPanel:_RefreshRewardItemsTournament(selectedIndex)
    local rewardInfo=self._tournamentRewardsTable[selectedIndex]
    local maxMajorLevel=Server.TournamentServer:GetMaxMajorLevel() 
    local selectedMajorLevel=rewardInfo and rewardInfo.TierID or Server.TournamentServer:GetMajorLevel()
    if rewardInfo then
        Module.ItemDetail:CloseAllPopUI()
        local rewardUnlockInfo=self:_GetRewardUnlockInfoByLevel(selectedMajorLevel,RankModeType.Tournament)
        local unlockDesc="?"
        if rewardUnlockInfo and rewardUnlockInfo.needGameCount==0 then
            unlockDesc=StringUtil.PluralTextFormat(TournamentConfig.Loc.RewardUnlockDescLevelOnly,
            {levelName=rewardUnlockInfo.levelText})
        else
            unlockDesc=StringUtil.PluralTextFormat(TournamentConfig.Loc.RewardUnlockDesc,
            {levelName=rewardUnlockInfo.levelText,curCount=rewardUnlockInfo.curGameCount,needCount=rewardUnlockInfo.needGameCount,gameCount=rewardUnlockInfo.needGameCount})
        end
        self._wtRewardUnlockText:SetText(unlockDesc)

        self._rewardIdList=self._rewardIdList or {}
        for k,v in pairs(self._rewardIdList)do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,v)
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:Collapsed()
            end
        end
        local index=1
        for k,v in pairs(rewardInfo.Rewards or {})do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,self._rewardIdList[index] or -1)
            if not weakIns then
                weakIns,InsId=Module.CommonWidget:CreateIVCommonItemTemplate(self,self._wtRewardBox,index)
                self._rewardIdList[index]=InsId
            end
            index=index+1
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SelfHitTestInvisible()
                local id,num=Module.Tournament:ParseRewardStr(v)
                local item=ItemBase:New(id,num)
                uiIns:InitItem(item)
                local mode=IsHD() and Module.CommonWidget.Config.EIVItemViewMode.CommonViewItemViewB or Module.CommonWidget.Config.EIVItemViewMode.CommonViewItemViewD
                uiIns:ChangeDefaultMode(mode)
                local itemSize=IsHD() and itemSizeHD or itemSizeMobile
                uiIns:ChangeDefaultSize(itemSize)
                uiIns:SetCppValue("bPreciseClick", false)--手游滑动点击会导致点击不响应的问题，这里屏蔽掉PreciseClick
                
                uiIns:StopIVAnimation("WBP_Itemview_LightEffect_01_Loop")
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,false)
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.GetMask,false)
                uiIns:SetSelected(uiIns.item,false)

                if selectedMajorLevel<=maxMajorLevel and rewardUnlockInfo.curGameCount>=rewardUnlockInfo.needGameCount then
                    if table.contains(Server.TournamentServer:GetNotReceivedLevels(),selectedMajorLevel) then--未领取
                        local itemList={}
                        for i,j in pairs(rewardInfo.Rewards)do
                            local id,num=Module.Tournament:ParseRewardStr(j)
                            local item=ItemBase:New(id,num)
                            table.insert(itemList,item)
                        end
                        self:BindNotReceivedClick(uiIns, selectedMajorLevel, itemList, RankModeType.Tournament,rewardRegion.levelReward)
                    else--已领取
                        self:BindReceivedClick(uiIns, selectedMajorLevel,rewardRegion.levelReward)
                    end
                else--未达成
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, true,rewardRegion.levelReward)
                end
            end
        end

    end
    local seasonalRewardInfo=Module.Tournament:GetLevelRewardsInfo(selectedMajorLevel)
    if seasonalRewardInfo then
        if not seasonalRewardInfo.SeasonRewards or table.nums(seasonalRewardInfo.SeasonRewards)==0 then
            self._wtSeasonRewardInfoBox:Collapsed()
            self._wtRewardSeparateLine:Collapsed()
            return
        end
        self._wtSeasonRewardInfoBox:SelfHitTestInvisible()
        self._wtRewardSeparateLine:SelfHitTestInvisible()
        self._seasonalRewardIdList=self._seasonalRewardIdList or {}
        for k,v in pairs(self._seasonalRewardIdList)do
            local weakInst=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,v)
            local uiIns=getfromweak(weakInst)
            if uiIns then
                uiIns:Collapsed()
            end
        end
        local index=1
        for k,v in pairs(seasonalRewardInfo.SeasonRewards or {})do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,self._seasonalRewardIdList[index] or -1)
            if not weakIns then
                weakIns,InsId=Facade.UIManager:AddSubUI(self,UIName2ID.IVCommonItemTemplate,self._wtSeasonRewardBox,index+10)
                self._seasonalRewardIdList[index]=InsId
            end
            index=index+1
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SelfHitTestInvisible()
                local id,num=Module.Tournament:ParseRewardStr(v)
                local item=ItemBase:New(id,num)
                uiIns:InitItem(item)
                local itemSize=IsHD() and itemSizeSeasonalHD or itemSizeSeasonalMobile
                uiIns:ChangeDefaultSize(itemSize)
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,false)
                if maxMajorLevel<selectedMajorLevel then
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, true,rewardRegion.seasonalReward)
                    self._wtSeasonRewardUnlockText:Collapsed()
                else
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, false,rewardRegion.seasonalReward)
                    self._wtSeasonRewardUnlockText:SelfHitTestInvisible()
                end
            end
        end
    else
        logwarning("TournamentRewardPanel:_RefreshRewardItemsTournament, seasonalRewardInfo is nil!",selectedMajorLevel)
        self._wtSeasonRewardInfoBox:Collapsed()
        self._wtRewardSeparateLine:Collapsed()
    end
    
end

function TournamentRewardPanel:_RefreshRewardItemsCommander(selectedIndex)
    local rewardInfo=self._commanderRewardsTable[selectedIndex]
    local maxMajorLevel=Server.TournamentServer:GetCommanderMaxMajorLevel() 
    local selectedMajorLevel=rewardInfo and rewardInfo.TierID or Server.TournamentServer:GetCommanderMajorLevel()
    if rewardInfo then
        Module.ItemDetail:CloseAllPopUI()
        local rewardUnlockInfo=self:_GetRewardUnlockInfoByLevel(selectedMajorLevel,RankModeType.Commander)
        local unlockDesc="?"
        if rewardUnlockInfo and rewardUnlockInfo.needGameCount==0 then
            unlockDesc=StringUtil.PluralTextFormat(TournamentConfig.Loc.RewardUnlockDescLevelOnly,
            {levelName=rewardUnlockInfo.levelText})
        else
            unlockDesc=StringUtil.PluralTextFormat(TournamentConfig.Loc.RewardUnlockDesc,
            {levelName=rewardUnlockInfo.levelText,curCount=rewardUnlockInfo.curGameCount,needCount=rewardUnlockInfo.needGameCount,gameCount=rewardUnlockInfo.needGameCount})
        end
        self._wtRewardUnlockText:SetText(unlockDesc)

        self._rewardIdList=self._rewardIdList or {}
        for k,v in pairs(self._rewardIdList)do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,v)
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:Collapsed()
            end
        end
        local index=1
        for k,v in pairs(rewardInfo.Rewards or {})do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,self._rewardIdList[index] or -1)
            if not weakIns then
                weakIns,InsId=Module.CommonWidget:CreateIVCommonItemTemplate(self,self._wtRewardBox,index)
                self._rewardIdList[index]=InsId
            end
            index=index+1
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SelfHitTestInvisible()
                local id,num=Module.Tournament:ParseRewardStr(v)
                local item=ItemBase:New(id,num)
                uiIns:InitItem(item)
                local mode=IsHD() and Module.CommonWidget.Config.EIVItemViewMode.CommonViewItemViewB or Module.CommonWidget.Config.EIVItemViewMode.CommonViewItemViewD
                uiIns:ChangeDefaultMode(mode)
                local itemSize=IsHD() and itemSizeHD or itemSizeMobile
                uiIns:ChangeDefaultSize(itemSize)
                uiIns:SetCppValue("bPreciseClick", false)--手游滑动点击会导致点击不响应的问题，这里屏蔽掉PreciseClick
                
                uiIns:StopIVAnimation("WBP_Itemview_LightEffect_01_Loop")
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,false)
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.GetMask,false)
                uiIns:SetSelected(uiIns.item,false)

                if selectedMajorLevel<=maxMajorLevel and rewardUnlockInfo.curGameCount>=rewardUnlockInfo.needGameCount then
                    if table.contains(Server.TournamentServer:GetCommanderNotReceivedLevels(),selectedMajorLevel) then--未领取
                        local itemList={}
                        for i,j in pairs(rewardInfo.Rewards)do
                            local id,num=Module.Tournament:ParseRewardStr(j)
                            local item=ItemBase:New(id,num)
                            table.insert(itemList,item)
                        end
                        self:BindNotReceivedClick(uiIns, selectedMajorLevel, itemList, RankModeType.Commander,rewardRegion.levelReward)
                    else--已领取
                        self:BindReceivedClick(uiIns, selectedMajorLevel,rewardRegion.levelReward)
                    end
                else--未达成
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, true,rewardRegion.levelReward)
                end
            end
        end

    end
    local seasonalRewardInfo=Module.Tournament:GetCommanderLevelRewardsInfo(selectedMajorLevel)
    if seasonalRewardInfo then
        if not seasonalRewardInfo.SeasonRewards or table.nums(seasonalRewardInfo.SeasonRewards)==0 then
            self._wtSeasonRewardInfoBox:Collapsed()
            self._wtRewardSeparateLine:Collapsed()
            return
        end
        self._wtSeasonRewardInfoBox:SelfHitTestInvisible()
        self._wtRewardSeparateLine:SelfHitTestInvisible()
        self._seasonalRewardIdList=self._seasonalRewardIdList or {}
        for k,v in pairs(self._seasonalRewardIdList)do
            local weakInst=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,v)
            local uiIns=getfromweak(weakInst)
            if uiIns then
                uiIns:Collapsed()
            end
        end
        local index=1
        for k,v in pairs(seasonalRewardInfo.SeasonRewards or {})do
            local weakIns=Facade.UIManager:GetSubUI(self,UIName2ID.IVCommonItemTemplate,self._seasonalRewardIdList[index] or -1)
            if not weakIns then
                weakIns,InsId=Facade.UIManager:AddSubUI(self,UIName2ID.IVCommonItemTemplate,self._wtSeasonRewardBox,index+10)
                self._seasonalRewardIdList[index]=InsId
            end
            index=index+1
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SelfHitTestInvisible()
                local id,num=Module.Tournament:ParseRewardStr(v)
                local item=ItemBase:New(id,num)
                uiIns:InitItem(item)
                local itemSize=IsHD() and itemSizeSeasonalHD or itemSizeSeasonalMobile
                uiIns:ChangeDefaultSize(itemSize)
                uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,false)
                if maxMajorLevel<selectedMajorLevel then
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, true,rewardRegion.seasonalReward)
                    self._wtSeasonRewardUnlockText:Collapsed()
                else
                    self:BindNotAchiveClick(uiIns, selectedMajorLevel, false,rewardRegion.seasonalReward)
                    self._wtSeasonRewardUnlockText:SelfHitTestInvisible()
                end
            end
        end
    else
        logwarning("TournamentRewardPanel:_RefreshRewardItemsTournament, seasonalRewardInfo is nil!",selectedMajorLevel)
        self._wtSeasonRewardInfoBox:Collapsed()
        self._wtRewardSeparateLine:Collapsed()
    end
    
end

function TournamentRewardPanel:BindReceivedClick(uiIns,majorLevel,rewardRegion)
    uiIns:FindOrAdd(CommonWidgetConfig.EIVWarehouseTempComponent.GetMask, UIName2ID.IVGetMaskComponent, CommonWidgetConfig.EIVSlotPos.MaskLayer,CommonWidgetConfig.EIVCompOrder.MaskLayerOrder)
    uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.GetMask,true)
    uiIns:BindCustomOnClicked(
        function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            Module.CommonTips:ShowSimpleTip(Module.Tournament.Config.Loc.RewardsAlreadyRewarded)
            local item=uiIns.item or {}
            self:SendRewardClickLog(majorLevel,item.id or 0,self._rankMode,rewardRegion)
            uiIns:SetSelected(uiIns.item,true)
            Module.ItemDetail:OpenItemDetailPanel(
                uiIns.item,
                uiIns,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                nil,
                function()
                    uiIns:SetSelected(uiIns.item,false)
                end
            )
        end
    )
end

function TournamentRewardPanel:BindNotReceivedClick(uiIns,majorLevel,itemList,rankMode,rewardRegion)
    uiIns:PlayIVAnimation("WBP_Itemview_LightEffect_01_Loop",0)
    uiIns:BindCustomOnClicked(
        function()
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            if rankMode==RankModeType.Ranking then
                Server.RankingServer:ReqSeasonRankAward({majorLevel},
                function()
                    local majorInfo=Module.Ranking:GetMajorRankData(majorLevel)
                    local title=""
                    if majorInfo then
                        title=string.format(Module.Ranking.Config.Loc.ReceiveSingleSuccess,majorInfo.MajorRankName)
                    else
                        logerror("TournamentRewardPanel:BindNotReceivedClick,on reward click, majorInfo is nil!!!")
                    end
                    Module.Reward:OpenRewardPanel(title, nil, itemList,nil,nil,true)
                    Server.RankingServer:ReqSeasonRankInfo()
                end)
            elseif rankMode==RankModeType.Tournament then
                Server.TournamentServer:ReqSeasonRankAward({majorLevel},
                function()
                    local majorInfo=Module.Tournament:GetRankDataByMajor(majorLevel)
                    local title=""
                    if majorInfo then
                        title=string.format(Module.Tournament.Config.Loc.ReceiveSingleSuccess,majorInfo.Type)
                    else
                        logerror("TournamentRewardPanel:BindNotReceivedClick,on reward click, majorInfo is nil!!!")
                    end
                    Module.Reward:OpenRewardPanel(title, nil, itemList,nil,nil,true)
                    Server.TournamentServer:ReqSeasonRankInfo()
                end)
            else
                Server.TournamentServer:ReqCommanderSeasonRankAward({majorLevel},
                function()
                    local majorInfo=Module.Tournament:GetCommanderRankDataByMajor(majorLevel)
                    local title=""
                    if majorInfo then
                        title=string.format(Module.Tournament.Config.Loc.ReceiveSingleSuccess,majorInfo.Type)
                    else
                        logerror("TournamentRewardPanel:BindNotReceivedClick,on reward click, majorInfo is nil!!!")
                    end
                    Module.Reward:OpenRewardPanel(title, nil, itemList,nil,nil,true)
                    Server.TournamentServer:ReqSeasonRankInfo()
                end)
            end
            local item=uiIns.item or {}
            self:SendRewardClickLog(majorLevel,item.id or 0,rankMode,rewardRegion)
        end
    )
                        
end

function TournamentRewardPanel:BindNotAchiveClick(uiIns,majorLevel,needLock,rewardRegion)
    if needLock then
        uiIns:FindOrAdd(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark, UIName2ID.IVMaskSmallLockComponent, CommonWidgetConfig.EIVSlotPos.MaskLayer,CommonWidgetConfig.EIVCompOrder.MaskLayerOrder)
        uiIns:EnableComponent(CommonWidgetConfig.EIVWarehouseTempComponent.ItemMark,true)
    end
    
    uiIns:BindCustomOnClicked(
        function()
            local item=uiIns.item or {}
            self:SendRewardClickLog(majorLevel,item.id or 0,self._rankMode,rewardRegion)
            Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
            uiIns:SetSelected(uiIns.item,true)
            Module.ItemDetail:OpenItemDetailPanel(
            uiIns.item,
            uiIns,
            nil,
            nil,
            nil,
            nil,
            nil,
            nil,
            nil,
            function()
                uiIns:SetSelected(uiIns.item,false)
            end)
        end
    )
end

function TournamentRewardPanel:SendRewardClickLog(selectedMajorLevel,itemId,rankMode,rewardRegion)
    loginfo("TournamentRewardPanel:SendRewardClickLog","selectedMajorLevel",selectedMajorLevel,"itemId",itemId,"rankMode",rankMode,"rewardRegion",rewardRegion)
    if rankMode==RankModeType.Ranking then
        local clickData={}
        clickData.SeasonNo=Server.RankingServer:GetCurSerial()
        clickData.MajorLevel=selectedMajorLevel
        clickData.ItemID=itemId
        clickData.RewardType=rewardRegion
        LogAnalysisTool.DoSendRankingRewardClickReport(clickData)
    elseif rankMode==RankModeType.Tournament then
        local clickData={}
        clickData.SeasonNo=Server.TournamentServer:GetCurSerial()
        clickData.MajorLevel=selectedMajorLevel
        clickData.ItemID=itemId
        clickData.RewardType=rewardRegion
        LogAnalysisTool.DoSendTournamentRewardClickReport(clickData)
    end
    
end

function TournamentRewardPanel:_GetRewardUnlockInfoByLevel(majorLevel,rankMode)
    loginfo("TournamentRewardPanel:_GetRewardUnlockInfoByLevel",majorLevel,rankMode)
    local levelText=""
    local curGameCount=0
    local needGameCount=0
    if rankMode==RankModeType.Ranking then
        local rankDataMajor=Module.Ranking:GetMajorRankData(majorLevel)
        local majorSummaries=Server.RankingServer:GetMajorSummaries()
        local rewardInfo=Module.Ranking:GetLevelRewardsInfo(majorLevel)
        if rankDataMajor then
            levelText=rankDataMajor.MajorRankName
            needGameCount=rewardInfo and rewardInfo.GamesRequired or 0
            for k,v in pairs(majorSummaries or {})do
                if v.majorid>=majorLevel then
                    curGameCount=curGameCount+v.total_game_cnt
                end
            end
            curGameCount=math.clamp(curGameCount,0,needGameCount)
        end
    elseif rankMode==RankModeType.Tournament then
        local rankData=Module.Tournament:GetRankDataByMajor(majorLevel)
        local majorSummaries=Server.TournamentServer:GetMajorSummaries()
        local rewardInfo=Module.Tournament:GetLevelRewardsInfo(majorLevel)
        if rankData then
            levelText=rankData.Type
            needGameCount=rewardInfo and rewardInfo.RewardConditionPar or 0
            for k,v in pairs(majorSummaries or {})do
                if v.majorid>=majorLevel then
                    curGameCount=curGameCount+v.total_game_cnt
                end
            end
            curGameCount=math.clamp(curGameCount,0,needGameCount)

        end
    else
        local rankData=Module.Tournament:GetCommanderRankDataByMajor(majorLevel)
        local majorSummaries=Server.TournamentServer:GetCommanderMajorSummaries()
        local rewardInfo=Module.Tournament:GetCommanderLevelRewardsInfo(majorLevel)
        if rankData then
            levelText=rankData.Type
            needGameCount=rewardInfo and rewardInfo.RewardConditionPar or 0
            for k,v in pairs(majorSummaries or {})do
                if v.majorid>=majorLevel then
                    curGameCount=curGameCount+v.total_game_cnt
                end
            end
            curGameCount=math.clamp(curGameCount,0,needGameCount)

        end
    end
    
    return {levelText=levelText,curGameCount=curGameCount,needGameCount=needGameCount}
end

-- BEGIN MODIFICATION @ VIRTUOS
function TournamentRewardPanel:_InitGamepadInputs()
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtRewardBox, self, "Hittest")
        self._navGroup:AddNavWidgetToArray(self._wtRewardBox)
        self._navGroup:MarkIsStackControlGroup()
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end

    if not self._gamepadSummaryList then
        local actionName=self._rankMode==RankModeType.Ranking and "SwitchRankLevel" or "SwitchTournamentLevel"
        self._gamepadSummaryList = {
            {actionName = actionName, func = nil, caller = nil, bUIOnly = true},
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)

        self._toggleNextTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab", EInputEvent.IE_Pressed, self._SwitchNextTab, self, EDisplayInputActionPriority.UI_Stack)
        self._togglePrevTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab", EInputEvent.IE_Pressed, self._SwitchPrevTab, self, EDisplayInputActionPriority.UI_Stack)  
    end
end

function TournamentRewardPanel:_DisableGamepadInputs()
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end

    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil

        self:RemoveInputActionBinding(self._toggleNextTabHandle)
        self._toggleNextTabHandle = nil
        self:RemoveInputActionBinding(self._togglePrevTabHandle)
        self._togglePrevTabHandle = nil
    end
end

function TournamentRewardPanel:_SwitchNextTab()
    if self._wtRightBtn:IsVisible() then
        self:_OnSwitchBtnClicked(1)
    end
end

function TournamentRewardPanel:_SwitchPrevTab()
    if self._wtLeftBtn:IsVisible() then
        self:_OnSwitchBtnClicked(-1)
    end
end
-- END MODIFICATION

return TournamentRewardPanel    
