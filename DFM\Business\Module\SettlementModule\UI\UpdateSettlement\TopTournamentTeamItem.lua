----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSettlement)
----- LOG FUNCTION AUTO GENERATE END -----------
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local SettlementLogic = require "DFM.Business.Module.SettlementModule.SettlementLogic"
local MPWeaponLevelPartsTable = Facade.TableManager:GetTable("WeaponSystem/MPWeaponLevelParts")
local TextStyleBlueprintLib = import "TextStyleBlueprintLib"

---@class TopTournamentTeamItem : LuaUIBaseView
local TopTournamentTeamItem = ui("TopTournamentTeamItem")

local function xxwwinfo(...)
    loginfo("[xxww] ", ...)
end

local function xxwwwarning(...)
    logwarning("[xxww] @warning@", ...)
end

local function xxwwerror(...)
    logerror("[xxww] @error@", ...)
end

function TopTournamentTeamItem:Ctor()
    self._wtPlayerName = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtPlayerTitle = self:Wnd("DFTextBlock_2", UITextBlock)
    self._wtSelectImg = self:Wnd("WBP_SlotCompSelected_1", UIWidgetBase)

    self._wtTipsBtn = self:Wnd("DFButton_2", UIWidgetBase)
    self._wtDFScaleBox = self:Wnd("DFScaleBox_0", UIWidgetBase)
    
    self._wtQuitImg = self:Wnd("DFImage_339", UIWidgetBase)

    self._wtLeaderImg1 = self:Wnd("Commander_Bg_01", UIWidgetBase)
    self._wtLeaderImg2 = self:Wnd("Commander_Icon_01", UIWidgetBase)
    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
end

function TopTournamentTeamItem:Show()
    self:AddLuaEvent(Server.SettlementServer.Events.evtPlayerInfoBePraisedNtf, self._OnPlayerInfoBePraisedNtf, self)
end

function TopTournamentTeamItem:Hide()
    self:RemoveAllLuaEvent()
end

---@param player pb_TDMPlayer
function TopTournamentTeamItem:RefreshView(player, teamLeaderId)
    self._wtLeaderImg1:Collapsed()
    self._wtLeaderImg2:Collapsed()

    self._wtQuitImg:Collapsed()

    self.playerId = player.player_id
    if teamLeaderId == self.playerId then
        self._wtLeaderImg1:SelfHitTestInvisible()
        self._wtLeaderImg2:SelfHitTestInvisible()
    end

    TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtPlayerName, "C001")

    self.name = ""
    if player.basic_info.game_nick ~= "" then
        self._wtPlayerName:SetText(player.basic_info.game_nick)
        self.name = player.basic_info.game_nick
    else
        self._wtPlayerName:SetText(player.ai_info.game_nick)
        self.name = player.ai_info.game_nick
    end

    local commanderGameTitleName = SettlementLogic.GetCommanderGameContributorName(player.commander_contributor_title)
    if commanderGameTitleName then
        self._wtPlayerTitle:SetText(commanderGameTitleName)
    end

    if player.commander_contributor_title <= 0 and player.is_join_at_middle then
        self._wtPlayerTitle:SetText(Module.Settlement.Config.Loc.TopTournamentMiddleJoin)
    end

    if player.leave then
        self._wtQuitImg:SelfHitTestInvisible()
        self._wtPlayerTitle:SetText(Module.Settlement.Config.Loc.TopTournamentQuit)
        TextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtPlayerName, "C000")
    end

    self:SetType(player.player_id == Server.AccountServer:GetPlayerId(), player.commander_contributor_title > 0)

    if player.player_id ~= Server.AccountServer:GetPlayerId() then
        self._wtTipsBtn:RemoveEvent("OnClicked")
        self._wtTipsBtn:Event("OnClicked", self._OnBtnClick, self)
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and self._wtPlatformIcon then
        -- collapsed as default
        self._wtPlatformIcon:Collapsed()

        local plat_id = player.basic_info.plat_id
        if plat_id then
            local platIconPath = Module.Friend:GetPlatformIconPath(plat_id)
            if platIconPath then
                self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
                self._wtPlatformIcon:SelfHitTestInvisible()
            end
        end
    end

    --Replace player name with OnlineId 
    if IsPS5() then
        local plat_id = player.basic_info.plat_id
        if plat_id ~= nil and plat_id == PlatIDType.Plat_Playstation then
            local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
            local OnlineIdManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

            if OnlineIdManager then
                local PS5OnlineId = OnlineIdManager:GetPlayerPlatformIdByOpenId(player.player_id)
                if not string.isempty(PS5OnlineId) then
                    self._wtPlayerName:SetText(PS5OnlineId)
                    self.name = PS5OnlineId
                end
            end
        end
    end
    --- END MODIFICATION
    local cantPraise = false
    if self.playerId == Server.AccountServer:GetPlayerId() then
        cantPraise = true
    end
    self.info = {
        player_id = self.playerId,
        nick_name = self.name,
        report_type = 3,
        bIsPraise = cantPraise,
    }
end

function TopTournamentTeamItem:_OnBtnClick()
    local btnTbl = {
        HeadButtonType.Tribute,
        HeadButtonType.AddFriend,
        HeadButtonType.Report,
    }
    local pos=FVector2D(0, 0)
    if DFHD_LUA==1 then
        pos = UWidgetLayoutLibrary.GetMousePositionOnViewport(GetWorld())
    else
        pos = self:GetCachedGeometry():GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    end
    self._wtSelectImg:SelfHitTestInvisible()
    local CallBack = SafeCallBack(function()
        self._wtSelectImg:Collapsed()
    end, self)
    if IsHD() then
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, self.info, self, btnTbl,
                FriendApplySource.MpSettlementApply, TeamInviteSource.FromAll, nil, true, pos, CallBack)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.CommonTipsPlayerSimple, nil, nil, self.info, self._wtDFScaleBox, btnTbl,
                FriendApplySource.MpSettlementApply, TeamInviteSource.FromAll, nil, true, nil, CallBack)
    end
end

--#region 点赞
function TopTournamentTeamItem:_OnPlayerInfoBePraisedNtf(playerId, num)
    if self.playerId == playerId then
        --展示小兵被点赞的数量和动效
    end
end

--#endregion

return TopTournamentTeamItem
