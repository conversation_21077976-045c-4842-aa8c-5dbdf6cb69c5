----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLobbyBHD)
----- LOG FUNCTION AUTO GENERATE END -----------

---------------------------------------------------------------------------------
--- 全局通用逻辑🔗 2025-4-16 ssy
---------------------------------------------------------------------------------

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local VehicleHelperTool = require "DFM.StandaloneLua.BusinessTool.VehicleHelperTool"
local EGPInputType = import"EGPInputType"

GeneralHelperTool = {}
General = GeneralHelperTool

---判断是否手柄模式
---@return boolean
function GeneralHelperTool.IsGamepad()
    return WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad
end

---判断是否是货币
---@param currencyId any 货币ID
---@return boolean
function GeneralHelperTool.IsCurrency(currencyId)
    for key, value in pairs(ECurrencyItemId or {}) do
        if value == currencyId then
            return true
        end
    end
    return false
end

---是否可以兑换
---@param currencyIdOrProps any 货币ID或者道具数组
---@param exchangeNum       any 兑换数量
---@return boolean
function GeneralHelperTool.IsCanExchange(currencyIdOrProps, exchangeNum)
    -- if currencyIdOrProps is nil direct return false
    if currencyIdOrProps == nil then
        return false
    end
    -- default set exchange num to one
    exchangeNum = exchangeNum or 1
    -- if is currency exchange
    if type(currencyIdOrProps) == "number" then
        local num = GeneralHelperTool.GetPropNumByPropId(currencyIdOrProps)
        if num then
            return num >= exchangeNum
        else
            return false
        end
    end
    -- if is prop exchange
    if type(currencyIdOrProps) == "table" then
        for index, prop in ipairs(currencyIdOrProps or {}) do
            if prop and prop.id and prop.num then
                local num = GeneralHelperTool.GetPropNumByPropId(prop.id)
                local max = prop.num * exchangeNum
                if num < max then
                    return false
                end
            end
        end
        return true
    end
    return false
end

---通过道具id获取道具数量(包括货币/仓库/藏品/活动)
---@param propId any 道具ID
---@return integer
function GeneralHelperTool.GetPropNumByPropId(propId)
    if propId == nil then
        --如果参数错误,就返回0吧
        return 0
    end
    --数据检查
    propId = tonumber(propId)
    
    if GeneralHelperTool.IsCurrency(propId) then
        --获取货币
        return Server.CurrencyServer:GetNumByItemId(propId)
    else
        --获取仓库道具
        local num = Server.InventoryServer:GetItemNumById(propId)
        if num == 0 then
            --获取藏品道具
            local itemData = Server.CollectionServer:GetCollectionItemById(propId)
            if itemData and itemData.num and itemData.num > 0 then
                return itemData.num
            end
            --获取活动代币
            num = Server.ActivityServer:GetCurrenNum(propId)
            if num then
                return num
            else
                return 0
            end
        else
            return num
        end
    end
end


---获取武器相关信息
---@param weaponData any 武器item
---@param funcName   any 函数名称
---@param ... unknown 参数
function GeneralHelperTool.GetWeaponData(weaponData, funcName, ...)
    if weaponData and funcName then
        if weaponData.GetFeature then
            local weaponFeature = weaponData:GetFeature(EFeatureType.Weapon)
            if weaponFeature then
                local func = weaponFeature[funcName]
                if func and type(func) == "function" then
                    return func(weaponFeature, ...)
                else
                    return func or 0
                end
            end
        end
    end
    logerror("ssy: get weapon data error !")
    return 0
end

---获取本地化文本
---@param txt any 本地化key
---@return string 本地化文本
function GeneralHelperTool.LocalizeText(txt)
    if txt and type(txt) == "string" then
        if string.starts_with(txt, "LOC_") then
            return LocalizeTool.GetTransStr(txt)
        end
    end
    return txt
end

---文本颜色
---@param list any 文本/颜色(RichTextStyleTable)
---@param txt  any 文本
---@return string|LuaLocText
function GeneralHelperTool.GetTextColor(list, txt)
    local colors = {
        [1] = "<customstyle color=\"Color_DarkNegative\">{data}</>" ,--红色
        [2] = "<customstyle color=\"Color_Highlight01\">{data}</>"  ,--黄色
        [3] = "<customstyle color=\"Color_Highlight02\">{data}</>"  ,--绿色
        [4] = "<customstyle color=\"Color_Quality00\">{data}</>"    ,--灰色
    } 
    local montage = "{data1}{data2}" --拼接
    --获取本地化(解决服务端文本key)
    local GetLocalize = function(comTxt)
        return GeneralHelperTool.LocalizeText(comTxt)
    end

    local GetColorFunc = function(data)
        if data and data.txt then
            local comTxt = GetLocalize(data.txt)
            if data.color then
                local color = colors[data.color]
                if color then
                    return StringUtil.Key2StrFormat(color, {data = comTxt})
                end
            end
            return comTxt
        end
        return ""
    end

    local GetMontageFunc = function(str, data)
        str  = GetLocalize(str)  or ""
        data = GetLocalize(data) or ""
        return StringUtil.Key2StrFormat(montage, {data1 = str, data2 = data})
    end

    local str = nil

    if list and type(list) == "table" then
        for index, value in ipairs(list or {}) do
            if str then
                str = GetMontageFunc(str, GetColorFunc(value))
            else
                str = GetColorFunc(value)
            end
        end
    elseif txt then
        str = GetMontageFunc(list, txt)
    elseif list then
        str = GetLocalize(list)
    end

    return str or ""
end

---获取货币描述
---@param currencyId  any 货币ID
---@param currencyNum any 货币数量
---@param color       any 数量颜色
function GeneralHelperTool.GetCurrencyStr(currencyId, currencyNum, color)
    if currencyId == nil or currencyNum == nil then
        return ""
    end
    local num = nil
    local currencyStr = "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"{img}\" align=\"0\"/> {num}"--货币描述
    local img = Server.CurrencyServer:GetRichTxtImgId(Server.CurrencyServer:ConvertCurrencyIdByItemId(currencyId))
    num = MathUtil.GetNumberFormatStr(currencyNum)

    --调用颜色处理函数
    if num and color then
        num = GeneralHelperTool.GetTextColor({{txt = num, color = color}})
    end

    if currencyStr and img and num then
        return StringUtil.Key2StrFormat(currencyStr, {img = img, num = num})
    else
        return ""
    end
end

---获取玄学武器皮肤
---@param prop any 道具
function GeneralHelperTool.GetSkinItemData(prop)
    if prop == nil or prop.id == nil then
        return
    end
    if prop.num == nil then
        prop.num = 1
    end
    --(需要赋值:解决皮肤不显示问题)
    if prop.gid == nil then
        prop.gid = 0
    end
    --ItemBase:401迭代，宽度计算报错
    if prop.components == nil then
        prop.components = {}
    end
    local itemData = ItemBase:New(prop.id, prop.num, prop.gid)
    if itemData == nil then
        return
    end
    --类型设置
    itemData.bindType = PropBindingType.BindingNotBind
    -- itemData.exceedPercent = prop.exceed_percent or 0
    --武器皮肤(玄学皮肤/普通皮肤)
    if itemData.SetRawPropInfo then
        local isWeapon = GeneralHelperTool.GetWeaponData(itemData, "IsWeapon")
        if isWeapon then
            --设置武器皮肤信息(模型/图标)
            local itemPresetId = WeaponAssemblyTool.GetPreviewGunItemIdFromRecId(prop.id, EArmedForceMode.SOL)
            if itemPresetId then
                local rawPropInfo = WeaponAssemblyTool.PresetRow_to_PropInfo(itemPresetId)
                if rawPropInfo then
                    itemData:SetRawPropInfo(rawPropInfo)
                end
            end
        else
            --标准预设(图标皮肤处理)
            itemData:SetRawPropInfo(prop)
        end
    end
    return itemData
end

---数量转换
---@param num any 数量
---@return any 
function GeneralHelperTool.GetNumStr(num)
    if num == nil or type(num) ~= "number" then
        return num
    end
    local flag
    --货币加k、m
	if num >= 1000000 then
		num = math.floor(num / 1000000)
		flag = 'M'
	elseif num >= 1000 then
		num = math.floor(num / 1000)
		flag = 'K'
	end
	local numStr = MathUtil.GetNumberFormatStr(num)
	if flag then
		numStr = numStr .. flag
	end
    return numStr
end

---获取服务器时间
---@return integer
function GeneralHelperTool.GetCurTime()
    return Facade.ClockManager:GetServerTimestamp()
end

---c获取剩余时间描述(服务器时间为准)
---@param time   any 时间戳
---@param isBool any 是否取差
function GeneralHelperTool.GetTimeStr(time, isBool)
    if time and type(time) == "number" then
        --服务器时间
        local curTime = GeneralHelperTool.GetCurTime()
        if isBool then
            time = time - curTime
        end
        --不同时间取描述
        if time >= 24*3600 then
            return TimeUtil.TransTimestamp2DDHH_CNStr(time)
        elseif time >= 3600 then
            return TimeUtil.TransTimestamp2HHMM_CNStr(time)
        elseif time >= 0 then
            return TimeUtil.TransTimestamp2MMSS_CNStr(time)
        end
    end
    return ""
end

---判断是否足够
---@param max any 最大值
---@param min any 最小值
---@return boolean
---@return unknown 
function GeneralHelperTool.IsHave(max, min)
    if max and min then
        if type(max) == "number" and type(min) == "number" then
            local num = max - min
            return num > 0, num
        end
    end
    return false, 0
end

---判断时间是否到期
---@param time any 时间
---@return boolean
function GeneralHelperTool.IsTimeExpired(time)
    if type(time) == "number" then
        return time <= GeneralHelperTool.GetCurTime()
    else
        return true
    end
end

---持久化key
---@param key any 
---@param value any
function GeneralHelperTool.SetUserData(key, value)
    --保存添加的key
    local RecordUserConfigKey = function (key)
        local keys = Facade.ConfigManager:GetArray("USER_KEY", {})
        for _, _key in ipairs(keys or {}) do
            if _key == key then
                return
            end
        end
        table.insert(keys, key)
        Facade.ConfigManager:SetArray("USER_KEY", keys)
    end
    --清空key
    local ClearUserConfigKey = function()
        local keys = Facade.ConfigManager:GetArray("USER_KEY", {})
        for _, _key in pairs(keys or {}) do
            Facade.ConfigManager:SetUserString(_key, "USER_NIL")
        end
    end

    if key and value ~= nil then
        --记录key
        RecordUserConfigKey(key)
        --保存value
        if type(value) == "string" then
            if value == "USER_NIL" then
                return
            end
            Facade.ConfigManager:SetUserString(key, value)
        end
        if type(value) == "boolean" then
            Facade.ConfigManager:SetUserBoolean(key, value)
        end
        if type(value) == "number" then
            Facade.ConfigManager:SetUserInt(key, value)
        end
    end
    --如果没有传视为清空
    if key == nil then
        ClearUserConfigKey()
    end
end

---获取持久化key的值
---@param key any
---@param default any
---@return any
function GeneralHelperTool.GetUserData(key, default)
    if key and default ~= nil then
        if type(default) == "string" then
            local str = Facade.ConfigManager:GetUserString(key, default)
            if str == "USER_NIL" then
                return default
            else
                return str
            end
        end
        if type(default) == "boolean" then
            return Facade.ConfigManager:GetUserBoolean(key, default)
        end
        if type(default) == "number" then
            return Facade.ConfigManager:GetUserInt(key, default)
        end
    end
end

---长度
---@param list any 表
---@return integer
function GeneralHelperTool.Count(list)
    if list and type(list) == "table" then
        return #list
    end
    return 0
end

---获取一个数
---@param num any 数字
---@return number
function GeneralHelperTool.GetNumber(num)
    num = tonumber(num)
    if num then
        return num
    end
    return 0
end

---大于
---@param param1 any
---@param param2 any
---@return boolean
function GeneralHelperTool.Greater(param1, param2)
    if param1 and param2 then
        if type(param1) == "number" and type(param2) == "number" then
            return param1 > param2
        end
    end
    return false
end

---大于等于
---@param param1 any
---@param param2 any
---@return boolean
function GeneralHelperTool.GreaterEqual(param1, param2)
    if param1 and param2 then
        if type(param1) == "number" and type(param2) == "number" then
            return param1 >= param2
        end
    end
    return false
end

---求和
---@param ... integer 数字
---@return integer
function GeneralHelperTool.Sum(...)
    local sum = 0
    for index, value in ipairs(table.pack(...) or {}) do
        local num = tonumber(value)
        if num then
            sum = sum + num
        end
    end
    return sum
end

---求差
---@param max any 被减数
---@param min any 减数
---@return unknown
function GeneralHelperTool.Distance(max, min)
    if max and min then
        if type(max) == "number" and type(min) == "number" then
            return max - min
        end
    end
    return 0
end

---除法
---@param numerator any 分子
---@param denominator any 分母
---@return unknown
function GeneralHelperTool.Division(numerator, denominator)
    if numerator and denominator then
        if type(numerator) == "number" and type(denominator) == "number" then
            --如果除数为零归一化
            if denominator == 0 then
                return 1
            end
            return numerator / denominator
        end
    end
    return 0
end

---求余
---@param numerator any 分子
---@param denominator any 分母
---@return unknown
function GeneralHelperTool.Modulus(numerator, denominator)
    if numerator and denominator then
        if type(numerator) == "number" and type(denominator) == "number" then
            --如果除数为零归一化
            if denominator == 0 then
                return 1
            end
            return numerator % denominator
        end
    end
    return 0
end

---求积
---@param ... unknown 数字
---@return number
function GeneralHelperTool.Amass(...)
    local sum = nil
    for index, value in ipairs(table.pack(...) or {}) do
        if type(value) == "number" then
            if sum then
                sum = sum * value
            else
                sum = value
            end
        end
    end
    if sum then
        return sum
    else
        return 0
    end
end

---获得理想的key
---@param ... unknown 任意字符/数字
---@return unknown
function GeneralHelperTool.GetKey(...)
    local str = "KEY_"
    for index, value in ipairs(table.pack(...) or {}) do
        str = str..tostring(value)
    end
    return str
end

---临时数据驿站
---@param caller any Server对象
---@param key    any GeneralHelperTool.GetKey(...)
---@param data   any 数据
function GeneralHelperTool.TempDataStage(caller, key, data)
    if caller then
        if caller.tempDatas == nil then
            caller.tempDatas = {}
        end
        if key and data ~= nil then
            caller.tempDatas[key] = data
            return key
        elseif key then
            return caller.tempDatas[key]
        else
            caller.tempDatas = nil
        end
    end
end

---活动data(活动专属)
---@param id   any 奖励id
---@param num  any 奖励数量
---@param max  any 奖励限购
---@param min  any 奖励已购
---@param uuid any 奖励唯一ID
---@param currencyId  any 奖励货币id
---@param currencyNum any 奖励货币数量
---@param refreshTime any 奖励刷新时间
---@param releaseTime any 奖励开放时间
---@return table
function GeneralHelperTool.GetData(id, num, max, min, uuid, currencyId, currencyNum, refreshTime, releaseTime)
    local data = {
        id   = id   or 0, --ID
        num  = num  or 0, --数量
        max  = max  or 0, --限购
        min  = min  or 0, --已购
        uuid = uuid or "",--唯一ID
        currencyId  = currencyId  or 0,--货币ID
        currencyNum = currencyNum or 0,--货币数量
        refreshTime = refreshTime or 0,--刷新时间
        releaseTime = releaseTime or 0,--开放时间
    }
    --唯一识别ID
    if data.uuid == "" then
        data.uuid = GeneralHelperTool.GetKey(data.id, data.num, data.max, data.currencyId, data.currencyNum)
    end
    return data
end

---获取CDN地址(活动专属)
---@param data any
function GeneralHelperTool.GetCDN(data)
    if data then
        local url = data.cdn
        if string.len(url or "") > 0 then
            return url
        end
    end
end

---data数据扩展(data专属)
---@param data  any data表
---@param key   any 扩展key
---@param value any 扩展值
function GeneralHelperTool.ExtendData(data, key, value)
    if data then
        data[key] = value
    end
end

---获得奖励兑换状态(活动专属)
---@param data any 奖励GetData(...)
---@param num  any 奖励兑换个数
---@return integer
function GeneralHelperTool.GetExchange(data, num)
    if data then
        --可能兑换好几个
        num = num or 1
        --未开放
        if not GeneralHelperTool.IsTimeExpired(data.releaseTime) then
            return 0
        end
        --未刷新
        if not GeneralHelperTool.IsTimeExpired(data.refreshTime) then
            return 1
        end
        --已购完
        if not GeneralHelperTool.IsHave(data.max, data.min) then
            return 2
        end
        --是否可兑换
        if GeneralHelperTool.IsCanExchange(data.currencyId, data.currencyNum * num) then
            return 4
        else
            return 3
        end
    end
    return -1
end

---查询玩家是否已拥有该道具(武器皮肤、头像、军牌、名片...)
---@param id any 道具ID
---@return boolean
function GeneralHelperTool.IsHaved(id)
    --查询武器皮肤
    if Server.CollectionServer:IsOwnedWeaponSkin(id) then
        return true
    end
    --查询玩家头像
    local heads = Server.RoleInfoServer:GetAvatarTbl()
    for index, head in ipairs(heads or {}) do
        if head.Islock and head.AvatarID == id then
            return true
        end
    end
    --查询玩家军牌
    local brands = Server.RoleInfoServer:GetMilitaryTbl()
    for index, brand in ipairs(brands or {}) do
        if brand.Islock and brand.AvatarID == id then
            return true
        end
    end
    --查询干员周边道具(名片...)
    local heros = Server.HeroServer:GetHeroData()
    for key, hero in pairs(heros or {}) do
        if hero and hero.hero_id then
            if Server.HeroServer:IsAccessoryUnlocked(tostring(hero.hero_id), id) then
                return true
            end
        end
    end

    --查询是否拥有挂饰
    if Server.CollectionServer:IsOwnedHanging(id) then
        return true
    end
    return false
end

---获取货币(转化ID/Index)
---@param id any 货币ID(WBP_TopBarHD_Tips)
---@return integer (货币ID/货币下标)
function GeneralHelperTool.GetCurrencyID(id)
    if id == nil then
        return 0
    end
    local CurrencyId = function(data)
        for key, value in pairs(ECurrencyItemId or {}) do
            if data == key then
                return value
            elseif data == value then
                return key
            end
        end
    end
    local CurrencyIndex = function(data)
        for key, value in pairs(ECurrencyClientId or {}) do
            if data == key then
                return value
            elseif data == value then
                return key
            end
        end
    end
    local ID = nil
    if CurrencyId(id) then
        ID = CurrencyIndex(CurrencyId(id))
    elseif CurrencyIndex(id) then
        ID = CurrencyId(CurrencyIndex(id))
    end
    ID = tonumber(ID)
    --哈夫币可能有点问题(哈夫币的key不一样)
    return ID or 0
end

---获取对象相关数据
---@param object any 对象
---@param funcName any 函数名称
---@param ... unknown 参数
function GeneralHelperTool.GetObjectData(object, funcName, ...)
    if object and funcName then
        local func = object[funcName]
        if func and type(func) == "function" then
            return func(object, ...)
        else
            return func
        end
    end
end

---获取itemData描述
---@param itemData any itemData
function GeneralHelperTool.GetItemDataDesc(itemData)
    if itemData then
        local featureType = GeneralHelperTool.GetObjectData(GeneralHelperTool.GetObjectData(itemData, "GetFeature"), "GetFeatureType")
        local desc = nil
        if ItemHelperTool.IsArmedForceUniquePropByItem(itemData) then
            --非武器兵种道具
            for _, propSubInfo in pairs(Facade.TableManager:GetTable("PresetArmedProps") or {}) do
                if propSubInfo then
                    if propSubInfo.PropID == tostring(itemData.id) then
                        desc = propSubInfo.Desc
                        break
                    end
                end
            end
        elseif featureType == EFeatureType.Vehicle then
            --载具
            if ItemHelperTool.GetMainTypeById(itemData.id) == EItemType.VehicleSkin then
                desc = VehicleHelperTool.GetVehicleDescByID(itemData.id)
            end
        elseif ItemHelperTool.GetMainTypeById(itemData.id) == EItemType.WeaponSkin then
            --武器皮肤
            local data = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
            if data then
                data = data[itemData.id]
            end
            if data == nil then
                data = Facade.TableManager:GetRowByKey("WeaponSkin/MeleeWeaponSkinDataTable", tostring(itemData.id))
            end
            if data then
                desc = data.SkinDescription
            end
        elseif itemData.itemMainType ==  EItemType.SocialAppearance then
            --头像
            for key, value in pairs(Facade.TableManager:GetTable("SocialAvatarDataTable") or {}) do
                if value and value.AvatarID == tostring(itemData.id) then
                    desc = value.AvatarDescription
                    break
                end
            end
        elseif itemData.itemMainType ==  EItemType.HeroAccessory then
            --名片
            for key, value in pairs(Facade.TableManager:GetTable("HeroCardData") or {}) do
                if value and value.HeroCardId == tostring(itemData.id) then
                    desc = value.CardDescription
                    break
                end
            end
        end
        return desc or itemData.description
    end
    return ""
end

---通过皮肤获取皮肤所属武器
---@param itemData any itemData
---@return ItemBase
function GeneralHelperTool.GetWeaponBySkin(itemData)
    if itemData then
        local skins = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
        for key, skin in pairs(skins or {}) do
            if skin and skin.SkinId == itemData.id then
                local item = GeneralHelperTool.GetSkinItemData({id = skin.BaseWeaponId})
                if item then
                    return item
                end
                break
            end
        end
    else
        return {}
    end
    return itemData
end

---获取数组里满足条件的数据
---@param list      any 数组
---@param valueName any data的值名称
---@param value     any 值
function GeneralHelperTool.GetListData(list, valueName, value)
    if valueName == nil then
        return {}
    end
    for key, data in pairs(list or {}) do
        if GeneralHelperTool.GetObjectData(data, valueName) == value then
            return data or {}
        end
    end
    return {}
end

---获取本地化文本{data}/%s
---@param Loc_Key any 本地化key
---@param ... unknown 参数
---@return string|LuaLocText
function GeneralHelperTool.GetText(Loc_Key, ...)
    --区分:服务端key/本地key
    if Loc_Key then
        local Split = function(str, r, index)
            if str and r and string.len(str) > 0 and string.len(r) > 0 then
                local arr = string.split(str, r)
                if arr then
                    if index then
                        return arr[index]
                    end
                    return arr
                end
            end
        end
        local keys = {}
        for index, value in ipairs(Split(Loc_Key, "}") or {}) do
            local key = Split(value, "{", 2)
            if key then
                table.insert(keys, key)
            end
        end
        local values = table.pack(...)
        local data = {}
        local isAdd = false
        for index, key in ipairs(keys or {}) do
            isAdd = true
            if values and values[index] then
                data[key] = GeneralHelperTool.LocalizeText(values[index])
            else
                data[key] = ""
            end
        end
        if data and isAdd then
            return StringUtil.Key2StrFormat(GeneralHelperTool.LocalizeText(Loc_Key), data)
        end
        --获取本地化参数%s
        local count = 0
        for word in string.gmatch(Loc_Key, "%%s") do
            count = count + 1
        end
        --添加参数
        for index = 1, count, 1 do
            if values and values[index] then
                table.insert(data, GeneralHelperTool.LocalizeText(values[index]))
            else
                table.insert(data, "")
            end
        end
        return string.format(GeneralHelperTool.LocalizeText(Loc_Key), unpack(data))
    end
    return ""
end

return GeneralHelperTool