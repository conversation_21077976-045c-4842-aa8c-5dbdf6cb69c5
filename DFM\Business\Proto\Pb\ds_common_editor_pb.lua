--ds_common.protoencode&decode functions.
function pb.pb_AILabSDKInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AILabSDKInfo) or {} 
    local __enable = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __enable ~= false then tb.enable = __enable end
    local __sync_transport_mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __sync_transport_mode ~= 0 then tb.sync_transport_mode = __sync_transport_mode end
    tb.route_info = pb.pb_AILabSDKRouteInfoDecode(decoder:getsubmsg(3))
    local __sync_frame_state_span_ms = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __sync_frame_state_span_ms ~= 0 then tb.sync_frame_state_span_ms = __sync_frame_state_span_ms end
    local __mode = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __mode ~= 0 then tb.mode = __mode end
    tb.args = pb.pb_AIRoomArgDecode(decoder:getsubmsg(6))
    local __business_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __business_id ~= 0 then tb.business_id = __business_id end
    return tb
end

function pb.pb_AILabSDKInfoEncode(tb, encoder)
    if(tb.enable) then    encoder:addbool(4, tb.enable)    end
    if(tb.sync_transport_mode) then    encoder:addu32(1, tb.sync_transport_mode)    end
    if(tb.route_info) then    pb.pb_AILabSDKRouteInfoEncode(tb.route_info, encoder:addsubmsg(3))    end
    if(tb.sync_frame_state_span_ms) then    encoder:addu32(2, tb.sync_frame_state_span_ms)    end
    if(tb.mode) then    encoder:addu32(5, tb.mode)    end
    if(tb.args) then    pb.pb_AIRoomArgEncode(tb.args, encoder:addsubmsg(6))    end
    if(tb.business_id) then    encoder:addu64(7, tb.business_id)    end
end

function pb.pb_AILabSDKRouteInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AILabSDKRouteInfo) or {} 
    local __router_mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __router_mode ~= 0 then tb.router_mode = __router_mode end
    local __downstream_ip = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __downstream_ip ~= "" then tb.downstream_ip = __downstream_ip end
    local __downstream_port = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __downstream_port ~= 0 then tb.downstream_port = __downstream_port end
    local __downstream_cl5_mod_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __downstream_cl5_mod_id ~= 0 then tb.downstream_cl5_mod_id = __downstream_cl5_mod_id end
    local __downstream_cl5_cmd_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __downstream_cl5_cmd_id ~= 0 then tb.downstream_cl5_cmd_id = __downstream_cl5_cmd_id end
    local __downstream_polaris_name = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __downstream_polaris_name ~= "" then tb.downstream_polaris_name = __downstream_polaris_name end
    return tb
end

function pb.pb_AILabSDKRouteInfoEncode(tb, encoder)
    if(tb.router_mode) then    encoder:addu32(1, tb.router_mode)    end
    if(tb.downstream_ip) then    encoder:addstr(2, tb.downstream_ip)    end
    if(tb.downstream_port) then    encoder:addi32(3, tb.downstream_port)    end
    if(tb.downstream_cl5_mod_id) then    encoder:addu64(4, tb.downstream_cl5_mod_id)    end
    if(tb.downstream_cl5_cmd_id) then    encoder:addu64(5, tb.downstream_cl5_cmd_id)    end
    if(tb.downstream_polaris_name) then    encoder:addstr(6, tb.downstream_polaris_name)    end
end

function pb.pb_AIRoomArgDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AIRoomArg) or {} 
    local __min_deliver_distance = decoder:getfloat(1)
    if not PB_USE_DEFAULT_TABLE or __min_deliver_distance ~= 0 then tb.min_deliver_distance = __min_deliver_distance end
    local __max_deliver_distance = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __max_deliver_distance ~= 0 then tb.max_deliver_distance = __max_deliver_distance end
    local __max_nav_distance_gap = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __max_nav_distance_gap ~= 0 then tb.max_nav_distance_gap = __max_nav_distance_gap end
    return tb
end

function pb.pb_AIRoomArgEncode(tb, encoder)
    if(tb.min_deliver_distance) then    encoder:addfloat(1, tb.min_deliver_distance)    end
    if(tb.max_deliver_distance) then    encoder:addfloat(2, tb.max_deliver_distance)    end
    if(tb.max_nav_distance_gap) then    encoder:addfloat(3, tb.max_nav_distance_gap)    end
end

function pb.pb_ArmedForceNumeralDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArmedForceNumeralData) or {} 
    local __armedforce_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    tb.talent = pb.pb_ArmedForceTalentDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_ArmedForceNumeralDataEncode(tb, encoder)
    if(tb.armedforce_id) then    encoder:addu32(1, tb.armedforce_id)    end
    if(tb.talent) then    pb.pb_ArmedForceTalentEncode(tb.talent, encoder:addsubmsg(2))    end
end

function pb.pb_ArmedForceTalentDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArmedForceTalent) or {} 
    local __talent_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __talent_id ~= 0 then tb.talent_id = __talent_id end
    local __active_skill_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __active_skill_id ~= 0 then tb.active_skill_id = __active_skill_id end
    local __passive_skill_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __passive_skill_id ~= 0 then tb.passive_skill_id = __passive_skill_id end
    return tb
end

function pb.pb_ArmedForceTalentEncode(tb, encoder)
    if(tb.talent_id) then    encoder:addu32(1, tb.talent_id)    end
    if(tb.active_skill_id) then    encoder:addu32(2, tb.active_skill_id)    end
    if(tb.passive_skill_id) then    encoder:addu32(3, tb.passive_skill_id)    end
end

function pb.pb_AttrBuffDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AttrBuff) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __source_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __source_id ~= 0 then tb.source_id = __source_id end
    local __source_gid = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __source_gid ~= 0 then tb.source_gid = __source_gid end
    local __start_time = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __duration = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __duration ~= 0 then tb.duration = __duration end
    local __body_part = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __body_part ~= 0 then tb.body_part = __body_part end
    return tb
end

function pb.pb_AttrBuffEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.source_id) then    encoder:addi32(2, tb.source_id)    end
    if(tb.source_gid) then    encoder:addi32(3, tb.source_gid)    end
    if(tb.start_time) then    encoder:addu64(4, tb.start_time)    end
    if(tb.duration) then    encoder:addu32(5, tb.duration)    end
    if(tb.body_part) then    encoder:addu32(6, tb.body_part)    end
end

function pb.pb_CombatRoleAbilityDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CombatRoleAbility) or {} 
    local __ability_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __ability_id ~= 0 then tb.ability_id = __ability_id end
    local __game_mode = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    tb.special_items = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.special_items[k] = pb.pb_CombatRoleSpecialItemDecode(v)
    end
    return tb
end

function pb.pb_CombatRoleAbilityEncode(tb, encoder)
    if(tb.ability_id) then    encoder:addu32(1, tb.ability_id)    end
    if(tb.game_mode) then    encoder:addu32(2, tb.game_mode)    end
    if(tb.special_items) then
        for i=1,#(tb.special_items) do
            pb.pb_CombatRoleSpecialItemEncode(tb.special_items[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CombatRoleSpecialItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CombatRoleSpecialItem) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __pos = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __pos ~= 0 then tb.pos = __pos end
    return tb
end

function pb.pb_CombatRoleSpecialItemEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.num) then    encoder:addu32(2, tb.num)    end
    if(tb.pos) then    encoder:addu32(3, tb.pos)    end
end

function pb.pb_ComponentDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Component) or {} 
    local __slot = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __slot ~= 0 then tb.slot = __slot end
    tb.prop_data = pb.pb_PropInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_ComponentEncode(tb, encoder)
    if(tb.slot) then    encoder:addi32(1, tb.slot)    end
    if(tb.prop_data) then    pb.pb_PropInfoEncode(tb.prop_data, encoder:addsubmsg(2))    end
end

function pb.pb_DSAttrValueDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DSAttrValue) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __value = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_DSAttrValueEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.value) then    encoder:addfloat(2, tb.value)    end
end

function pb.pb_HeroArmedPropFashionDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroArmedPropFashion) or {} 
    local __fashion_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __fashion_id ~= 0 then tb.fashion_id = __fashion_id end
    local __quality = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __quality ~= 0 then tb.quality = __quality end
    return tb
end

function pb.pb_HeroArmedPropFashionEncode(tb, encoder)
    if(tb.fashion_id) then    encoder:addu64(1, tb.fashion_id)    end
    if(tb.quality) then    encoder:addu32(2, tb.quality)    end
end

function pb.pb_HeroArmedPropFashionInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroArmedPropFashionInfo) or {} 
    local __armed_prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __armed_prop_id ~= 0 then tb.armed_prop_id = __armed_prop_id end
    tb.fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_HeroArmedPropFashionInfoEncode(tb, encoder)
    if(tb.armed_prop_id) then    encoder:addu64(1, tb.armed_prop_id)    end
    if(tb.fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.fashion, encoder:addsubmsg(2))    end
end

function pb.pb_DSHeroDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DSHero) or {} 
    local __hero_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __armed_force_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __armed_force_id ~= 0 then tb.armed_force_id = __armed_force_id end
    tb.skill = pb.pb_ExpertSkillDecode(decoder:getsubmsg(4))
    tb.armed_prop1 = pb.pb_PropInfoDecode(decoder:getsubmsg(5))
    tb.armed_prop2 = pb.pb_PropInfoDecode(decoder:getsubmsg(6))
    tb.hero_body = pb.pb_PropInfoDecode(decoder:getsubmsg(7))
    tb.fashion = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.fashion[k] = pb.pb_HeroFashionDecode(v)
    end
    tb.accessories = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.accessories[k] = pb.pb_HeroAccessoryItemDecode(v)
    end
    local __fashion_teammate_visible = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __fashion_teammate_visible ~= false then tb.fashion_teammate_visible = __fashion_teammate_visible end
    local __fashion_enemy_visible = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __fashion_enemy_visible ~= false then tb.fashion_enemy_visible = __fashion_enemy_visible end
    local __fashion_safehouse_visible = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __fashion_safehouse_visible ~= false then tb.fashion_safehouse_visible = __fashion_safehouse_visible end
    tb.armed_prop_fashion_info = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.armed_prop_fashion_info[k] = pb.pb_HeroArmedPropFashionInfoDecode(v)
    end
    return tb
end

function pb.pb_DSHeroEncode(tb, encoder)
    if(tb.hero_id) then    encoder:addu64(1, tb.hero_id)    end
    if(tb.armed_force_id) then    encoder:addu32(2, tb.armed_force_id)    end
    if(tb.skill) then    pb.pb_ExpertSkillEncode(tb.skill, encoder:addsubmsg(4))    end
    if(tb.armed_prop1) then    pb.pb_PropInfoEncode(tb.armed_prop1, encoder:addsubmsg(5))    end
    if(tb.armed_prop2) then    pb.pb_PropInfoEncode(tb.armed_prop2, encoder:addsubmsg(6))    end
    if(tb.hero_body) then    pb.pb_PropInfoEncode(tb.hero_body, encoder:addsubmsg(7))    end
    if(tb.fashion) then
        for i=1,#(tb.fashion) do
            pb.pb_HeroFashionEncode(tb.fashion[i], encoder:addsubmsg(8))
        end
    end
    if(tb.accessories) then
        for i=1,#(tb.accessories) do
            pb.pb_HeroAccessoryItemEncode(tb.accessories[i], encoder:addsubmsg(9))
        end
    end
    if(tb.fashion_teammate_visible) then    encoder:addbool(10, tb.fashion_teammate_visible)    end
    if(tb.fashion_enemy_visible) then    encoder:addbool(11, tb.fashion_enemy_visible)    end
    if(tb.fashion_safehouse_visible) then    encoder:addbool(12, tb.fashion_safehouse_visible)    end
    if(tb.armed_prop_fashion_info) then
        for i=1,#(tb.armed_prop_fashion_info) do
            pb.pb_HeroArmedPropFashionInfoEncode(tb.armed_prop_fashion_info[i], encoder:addsubmsg(13))
        end
    end
end

function pb.pb_DSQuestDescDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DSQuestDesc) or {} 
    local __quest_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __quest_id ~= 0 then tb.quest_id = __quest_id end
    local __quest_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __quest_type ~= 0 then tb.quest_type = __quest_type end
    local __should_auto_accept = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __should_auto_accept ~= false then tb.should_auto_accept = __should_auto_accept end
    local __giver_id = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __giver_id ~= "" then tb.giver_id = __giver_id end
    local __ender_id = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __ender_id ~= "" then tb.ender_id = __ender_id end
    local __quest_path = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __quest_path ~= "" then tb.quest_path = __quest_path end
    local __accept_required_level = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __accept_required_level ~= 0 then tb.accept_required_level = __accept_required_level end
    tb.objective_list = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.objective_list[k] = pb.pb_DSQuestObjectiveDescDecode(v)
    end
    return tb
end

function pb.pb_DSQuestDescEncode(tb, encoder)
    if(tb.quest_id) then    encoder:addu64(1, tb.quest_id)    end
    if(tb.quest_type) then    encoder:addi32(2, tb.quest_type)    end
    if(tb.should_auto_accept) then    encoder:addbool(3, tb.should_auto_accept)    end
    if(tb.giver_id) then    encoder:addstr(4, tb.giver_id)    end
    if(tb.ender_id) then    encoder:addstr(5, tb.ender_id)    end
    if(tb.quest_path) then    encoder:addstr(6, tb.quest_path)    end
    if(tb.accept_required_level) then    encoder:addi32(7, tb.accept_required_level)    end
    if(tb.objective_list) then
        for i=1,#(tb.objective_list) do
            pb.pb_DSQuestObjectiveDescEncode(tb.objective_list[i], encoder:addsubmsg(9))
        end
    end
end

function pb.pb_DSQuestObjectiveDescDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DSQuestObjectiveDesc) or {} 
    local __quest_objective_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __quest_objective_id ~= 0 then tb.quest_objective_id = __quest_objective_id end
    local __should_must_be_completed = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __should_must_be_completed ~= false then tb.should_must_be_completed = __should_must_be_completed end
    local __type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __param1 = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __param1 ~= "" then tb.param1 = __param1 end
    local __param2 = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __param2 ~= "" then tb.param2 = __param2 end
    tb.list_param = decoder:geti64ary(6)
    local __required_count = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __required_count ~= 0 then tb.required_count = __required_count end
    return tb
end

function pb.pb_DSQuestObjectiveDescEncode(tb, encoder)
    if(tb.quest_objective_id) then    encoder:addu64(1, tb.quest_objective_id)    end
    if(tb.should_must_be_completed) then    encoder:addbool(2, tb.should_must_be_completed)    end
    if(tb.type) then    encoder:addi32(3, tb.type)    end
    if(tb.param1) then    encoder:addstr(4, tb.param1)    end
    if(tb.param2) then    encoder:addstr(5, tb.param2)    end
    if(tb.list_param) then    encoder:addi64(6, tb.list_param)    end
    if(tb.required_count) then    encoder:addi64(7, tb.required_count)    end
end

function pb.pb_RaidKillEnemyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RaidKillEnemyInfo) or {} 
    local __raid_enemy_type = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __raid_enemy_type ~= "" then tb.raid_enemy_type = __raid_enemy_type end
    local __kill_enemy_count = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __kill_enemy_count ~= 0 then tb.kill_enemy_count = __kill_enemy_count end
    return tb
end

function pb.pb_RaidKillEnemyInfoEncode(tb, encoder)
    if(tb.raid_enemy_type) then    encoder:addstr(1, tb.raid_enemy_type)    end
    if(tb.kill_enemy_count) then    encoder:addu32(2, tb.kill_enemy_count)    end
end

function pb.pb_DsRaidPlayerGameStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsRaidPlayerGameStatus) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __passed_life = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __passed_life ~= 0 then tb.passed_life = __passed_life end
    local __leave = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __leave ~= false then tb.leave = __leave end
    local __difficulty = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __difficulty ~= 0 then tb.difficulty = __difficulty end
    local __fail_quest = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __fail_quest ~= 0 then tb.fail_quest = __fail_quest end
    local __quit_quest = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __quit_quest ~= 0 then tb.quit_quest = __quit_quest end
    local __play_time = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __passed_time = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __passed_time ~= 0 then tb.passed_time = __passed_time end
    local __total_damage = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __damage_to_vehicles = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __damage_to_vehicles ~= 0 then tb.damage_to_vehicles = __damage_to_vehicles end
    local __total_kill_count = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __total_kill_count ~= 0 then tb.total_kill_count = __total_kill_count end
    local __special_ai_kill_count = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __special_ai_kill_count ~= 0 then tb.special_ai_kill_count = __special_ai_kill_count end
    local __normal_ai_kill_count = decoder:getu32(19)
    if not PB_USE_DEFAULT_TABLE or __normal_ai_kill_count ~= 0 then tb.normal_ai_kill_count = __normal_ai_kill_count end
    local __death_count = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __death_count ~= 0 then tb.death_count = __death_count end
    local __rescue_count = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __heal_value = decoder:getu32(24)
    if not PB_USE_DEFAULT_TABLE or __heal_value ~= 0 then tb.heal_value = __heal_value end
    local __raid_score = decoder:getu32(25)
    if not PB_USE_DEFAULT_TABLE or __raid_score ~= 0 then tb.raid_score = __raid_score end
    local __kill_vehicle_count = decoder:getu32(26)
    if not PB_USE_DEFAULT_TABLE or __kill_vehicle_count ~= 0 then tb.kill_vehicle_count = __kill_vehicle_count end
    local __helicopter_kill = decoder:getbool(27)
    if not PB_USE_DEFAULT_TABLE or __helicopter_kill ~= false then tb.helicopter_kill = __helicopter_kill end
    local __armored_car_full_blood = decoder:getbool(28)
    if not PB_USE_DEFAULT_TABLE or __armored_car_full_blood ~= false then tb.armored_car_full_blood = __armored_car_full_blood end
    tb.kill_enemy_list = {}
    for k,v in pairs(decoder:getsubmsgary(29)) do
        tb.kill_enemy_list[k] = pb.pb_RaidKillEnemyInfoDecode(v)
    end
    local __carry_in_props_price = decoder:geti32(30)
    if not PB_USE_DEFAULT_TABLE or __carry_in_props_price ~= 0 then tb.carry_in_props_price = __carry_in_props_price end
    return tb
end

function pb.pb_DsRaidPlayerGameStatusEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.passed_life) then    encoder:addi32(4, tb.passed_life)    end
    if(tb.leave) then    encoder:addbool(6, tb.leave)    end
    if(tb.difficulty) then    encoder:addi32(10, tb.difficulty)    end
    if(tb.fail_quest) then    encoder:addi32(11, tb.fail_quest)    end
    if(tb.quit_quest) then    encoder:addi32(12, tb.quit_quest)    end
    if(tb.play_time) then    encoder:addu32(13, tb.play_time)    end
    if(tb.passed_time) then    encoder:addu32(14, tb.passed_time)    end
    if(tb.total_damage) then    encoder:addu32(15, tb.total_damage)    end
    if(tb.damage_to_vehicles) then    encoder:addu32(16, tb.damage_to_vehicles)    end
    if(tb.total_kill_count) then    encoder:addu32(17, tb.total_kill_count)    end
    if(tb.special_ai_kill_count) then    encoder:addu32(18, tb.special_ai_kill_count)    end
    if(tb.normal_ai_kill_count) then    encoder:addu32(19, tb.normal_ai_kill_count)    end
    if(tb.death_count) then    encoder:addu32(22, tb.death_count)    end
    if(tb.rescue_count) then    encoder:addu32(23, tb.rescue_count)    end
    if(tb.heal_value) then    encoder:addu32(24, tb.heal_value)    end
    if(tb.raid_score) then    encoder:addu32(25, tb.raid_score)    end
    if(tb.kill_vehicle_count) then    encoder:addu32(26, tb.kill_vehicle_count)    end
    if(tb.helicopter_kill) then    encoder:addbool(27, tb.helicopter_kill)    end
    if(tb.armored_car_full_blood) then    encoder:addbool(28, tb.armored_car_full_blood)    end
    if(tb.kill_enemy_list) then
        for i=1,#(tb.kill_enemy_list) do
            pb.pb_RaidKillEnemyInfoEncode(tb.kill_enemy_list[i], encoder:addsubmsg(29))
        end
    end
    if(tb.carry_in_props_price) then    encoder:addi32(30, tb.carry_in_props_price)    end
end

function pb.pb_RoomAttrDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomAttr) or {} 
    local __WarmType = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __WarmType ~= 0 then tb.WarmType = __WarmType end
    local __SchemePoolID = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __SchemePoolID ~= 0 then tb.SchemePoolID = __SchemePoolID end
    local __GroupID = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __GroupID ~= 0 then tb.GroupID = __GroupID end
    local __match_sub_type = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __match_sub_type ~= 0 then tb.match_sub_type = __match_sub_type end
    local __dynamic_skill_level = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __dynamic_skill_level ~= 0 then tb.dynamic_skill_level = __dynamic_skill_level end
    local __dynamic_ratio_low = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __dynamic_ratio_low ~= 0 then tb.dynamic_ratio_low = __dynamic_ratio_low end
    local __dynamic_ratio_high = decoder:getfloat(7)
    if not PB_USE_DEFAULT_TABLE or __dynamic_ratio_high ~= 0 then tb.dynamic_ratio_high = __dynamic_ratio_high end
    local __tdm_device_type = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __tdm_device_type ~= 0 then tb.tdm_device_type = __tdm_device_type end
    local __max_camp_real_player_num = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __max_camp_real_player_num ~= 0 then tb.max_camp_real_player_num = __max_camp_real_player_num end
    local __max_player_num = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __max_player_num ~= 0 then tb.max_player_num = __max_player_num end
    local __ailab_conf_id = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __ailab_conf_id ~= 0 then tb.ailab_conf_id = __ailab_conf_id end
    local __is_victory_unite_match = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __is_victory_unite_match ~= false then tb.is_victory_unite_match = __is_victory_unite_match end
    return tb
end

function pb.pb_RoomAttrEncode(tb, encoder)
    if(tb.WarmType) then    encoder:addu32(1, tb.WarmType)    end
    if(tb.SchemePoolID) then    encoder:addi64(2, tb.SchemePoolID)    end
    if(tb.GroupID) then    encoder:addu64(3, tb.GroupID)    end
    if(tb.match_sub_type) then    encoder:addu32(4, tb.match_sub_type)    end
    if(tb.dynamic_skill_level) then    encoder:addi64(5, tb.dynamic_skill_level)    end
    if(tb.dynamic_ratio_low) then    encoder:addfloat(6, tb.dynamic_ratio_low)    end
    if(tb.dynamic_ratio_high) then    encoder:addfloat(7, tb.dynamic_ratio_high)    end
    if(tb.tdm_device_type) then    encoder:addi64(8, tb.tdm_device_type)    end
    if(tb.max_camp_real_player_num) then    encoder:addi64(9, tb.max_camp_real_player_num)    end
    if(tb.max_player_num) then    encoder:addi64(10, tb.max_player_num)    end
    if(tb.ailab_conf_id) then    encoder:addi64(11, tb.ailab_conf_id)    end
    if(tb.is_victory_unite_match) then    encoder:addbool(12, tb.is_victory_unite_match)    end
end

function pb.pb_TDMCampDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMCamp) or {} 
    local __color = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __color ~= 0 then tb.color = __color end
    local __is_winner = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __is_winner ~= 0 then tb.is_winner = __is_winner end
    local __score = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __attacker = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __attacker ~= false then tb.attacker = __attacker end
    local __occupied_stronghold = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __occupied_stronghold ~= 0 then tb.occupied_stronghold = __occupied_stronghold end
    local __fallen_stronghold = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __fallen_stronghold ~= 0 then tb.fallen_stronghold = __fallen_stronghold end
    tb.team_list = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.team_list[k] = pb.pb_TDMTeamDecode(v)
    end
    local __total_score = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __total_score ~= 0 then tb.total_score = __total_score end
    local __capture_flag_score = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __capture_flag_score ~= 0 then tb.capture_flag_score = __capture_flag_score end
    tb.od_camp = pb.pb_ODTdmCampDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_TDMCampEncode(tb, encoder)
    if(tb.color) then    encoder:addu32(1, tb.color)    end
    if(tb.is_winner) then    encoder:addi32(2, tb.is_winner)    end
    if(tb.score) then    encoder:addi32(3, tb.score)    end
    if(tb.attacker) then    encoder:addbool(4, tb.attacker)    end
    if(tb.occupied_stronghold) then    encoder:addi32(5, tb.occupied_stronghold)    end
    if(tb.fallen_stronghold) then    encoder:addi32(6, tb.fallen_stronghold)    end
    if(tb.team_list) then
        for i=1,#(tb.team_list) do
            pb.pb_TDMTeamEncode(tb.team_list[i], encoder:addsubmsg(7))
        end
    end
    if(tb.total_score) then    encoder:addi32(8, tb.total_score)    end
    if(tb.capture_flag_score) then    encoder:addi32(9, tb.capture_flag_score)    end
    if(tb.od_camp) then    pb.pb_ODTdmCampEncode(tb.od_camp, encoder:addsubmsg(100))    end
end

function pb.pb_TDMSettlementAccountExpDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMSettlementAccountExp) or {} 
    local __exp = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __victorDefeatExp = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __victorDefeatExp ~= 0 then tb.victorDefeatExp = __victorDefeatExp end
    local __compensation = decoder:getdouble(3)
    if not PB_USE_DEFAULT_TABLE or __compensation ~= 0 then tb.compensation = __compensation end
    local __factor = decoder:getdouble(4)
    if not PB_USE_DEFAULT_TABLE or __factor ~= 0 then tb.factor = __factor end
    local __game_exp = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __game_exp ~= 0 then tb.game_exp = __game_exp end
    local __medal_exp = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __medal_exp ~= 0 then tb.medal_exp = __medal_exp end
    local __result_exp = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __result_exp ~= 0 then tb.result_exp = __result_exp end
    local __raw_total_exp = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __raw_total_exp ~= 0 then tb.raw_total_exp = __raw_total_exp end
    return tb
end

function pb.pb_TDMSettlementAccountExpEncode(tb, encoder)
    if(tb.exp) then    encoder:addi64(1, tb.exp)    end
    if(tb.victorDefeatExp) then    encoder:addu32(2, tb.victorDefeatExp)    end
    if(tb.compensation) then    encoder:adddouble(3, tb.compensation)    end
    if(tb.factor) then    encoder:adddouble(4, tb.factor)    end
    if(tb.game_exp) then    encoder:addi64(5, tb.game_exp)    end
    if(tb.medal_exp) then    encoder:addi64(6, tb.medal_exp)    end
    if(tb.result_exp) then    encoder:addi64(7, tb.result_exp)    end
    if(tb.raw_total_exp) then    encoder:addi64(8, tb.raw_total_exp)    end
end

function pb.pb_TournamentRankShieldDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TournamentRankShield) or {} 
    local __id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __rank_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __rank_id ~= 0 then tb.rank_id = __rank_id end
    local __used_shied = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __used_shied ~= 0 then tb.used_shied = __used_shied end
    local __total_shied = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __total_shied ~= 0 then tb.total_shied = __total_shied end
    local __score = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __shield_type = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __shield_type ~= 0 then tb.shield_type = __shield_type end
    local __left_shield = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __left_shield ~= 0 then tb.left_shield = __left_shield end
    return tb
end

function pb.pb_TournamentRankShieldEncode(tb, encoder)
    if(tb.id) then    encoder:addi32(1, tb.id)    end
    if(tb.rank_id) then    encoder:addu32(2, tb.rank_id)    end
    if(tb.used_shied) then    encoder:addi32(3, tb.used_shied)    end
    if(tb.total_shied) then    encoder:addi32(4, tb.total_shied)    end
    if(tb.score) then    encoder:addi64(5, tb.score)    end
    if(tb.shield_type) then    encoder:addu32(6, tb.shield_type)    end
    if(tb.left_shield) then    encoder:addi32(7, tb.left_shield)    end
end

function pb.pb_TournamentRankDoubleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TournamentRankDoubleInfo) or {} 
    local __reason = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __used_num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __used_num ~= 0 then tb.used_num = __used_num end
    local __total_num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __total_num ~= 0 then tb.total_num = __total_num end
    local __left_num = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __left_num ~= 0 then tb.left_num = __left_num end
    local __double_rate = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __double_rate ~= 0 then tb.double_rate = __double_rate end
    return tb
end

function pb.pb_TournamentRankDoubleInfoEncode(tb, encoder)
    if(tb.reason) then    encoder:addu32(1, tb.reason)    end
    if(tb.used_num) then    encoder:addi32(2, tb.used_num)    end
    if(tb.total_num) then    encoder:addi32(3, tb.total_num)    end
    if(tb.left_num) then    encoder:addi32(4, tb.left_num)    end
    if(tb.double_rate) then    encoder:addi32(5, tb.double_rate)    end
end

function pb.pb_TdmCommanderScoreDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TdmCommanderScore) or {} 
    local __attack_defeat_score = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __attack_defeat_score ~= 0 then tb.attack_defeat_score = __attack_defeat_score end
    local __attack_vic_score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __attack_vic_score ~= 0 then tb.attack_vic_score = __attack_vic_score end
    local __defence_vic_score = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __defence_vic_score ~= 0 then tb.defence_vic_score = __defence_vic_score end
    local __defence_defeat_score = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __defence_defeat_score ~= 0 then tb.defence_defeat_score = __defence_defeat_score end
    local __attack_stage_score = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __attack_stage_score ~= 0 then tb.attack_stage_score = __attack_stage_score end
    local __defence_stage_score = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __defence_stage_score ~= 0 then tb.defence_stage_score = __defence_stage_score end
    local __camp_compestion_score = decoder:getdouble(7)
    if not PB_USE_DEFAULT_TABLE or __camp_compestion_score ~= 0 then tb.camp_compestion_score = __camp_compestion_score end
    local __camp_match_score = decoder:getdouble(8)
    if not PB_USE_DEFAULT_TABLE or __camp_match_score ~= 0 then tb.camp_match_score = __camp_match_score end
    local __join_at_middle_score = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __join_at_middle_score ~= 0 then tb.join_at_middle_score = __join_at_middle_score end
    local __short_hangup_trigger_score = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __short_hangup_trigger_score ~= 0 then tb.short_hangup_trigger_score = __short_hangup_trigger_score end
    local __contributor_title_score = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __contributor_title_score ~= 0 then tb.contributor_title_score = __contributor_title_score end
    local __passive_combat_rate = decoder:getdouble(12)
    if not PB_USE_DEFAULT_TABLE or __passive_combat_rate ~= 0 then tb.passive_combat_rate = __passive_combat_rate end
    local __leave_score = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __leave_score ~= 0 then tb.leave_score = __leave_score end
    local __total_score = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __total_score ~= 0 then tb.total_score = __total_score end
    local __weak_camp_score = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __weak_camp_score ~= 0 then tb.weak_camp_score = __weak_camp_score end
    local __passive_combat_score = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __passive_combat_score ~= 0 then tb.passive_combat_score = __passive_combat_score end
    local __conquest_vic_score = decoder:geti64(17)
    if not PB_USE_DEFAULT_TABLE or __conquest_vic_score ~= 0 then tb.conquest_vic_score = __conquest_vic_score end
    local __conquest_defeat_score = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __conquest_defeat_score ~= 0 then tb.conquest_defeat_score = __conquest_defeat_score end
    local __conquest_progress_score = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __conquest_progress_score ~= 0 then tb.conquest_progress_score = __conquest_progress_score end
    return tb
end

function pb.pb_TdmCommanderScoreEncode(tb, encoder)
    if(tb.attack_defeat_score) then    encoder:addi64(1, tb.attack_defeat_score)    end
    if(tb.attack_vic_score) then    encoder:addi64(2, tb.attack_vic_score)    end
    if(tb.defence_vic_score) then    encoder:addi64(3, tb.defence_vic_score)    end
    if(tb.defence_defeat_score) then    encoder:addi64(4, tb.defence_defeat_score)    end
    if(tb.attack_stage_score) then    encoder:addi64(5, tb.attack_stage_score)    end
    if(tb.defence_stage_score) then    encoder:addi64(6, tb.defence_stage_score)    end
    if(tb.camp_compestion_score) then    encoder:adddouble(7, tb.camp_compestion_score)    end
    if(tb.camp_match_score) then    encoder:adddouble(8, tb.camp_match_score)    end
    if(tb.join_at_middle_score) then    encoder:addi64(9, tb.join_at_middle_score)    end
    if(tb.short_hangup_trigger_score) then    encoder:addi64(10, tb.short_hangup_trigger_score)    end
    if(tb.contributor_title_score) then    encoder:addi64(11, tb.contributor_title_score)    end
    if(tb.passive_combat_rate) then    encoder:adddouble(12, tb.passive_combat_rate)    end
    if(tb.leave_score) then    encoder:addi64(13, tb.leave_score)    end
    if(tb.total_score) then    encoder:addi64(14, tb.total_score)    end
    if(tb.weak_camp_score) then    encoder:addi64(15, tb.weak_camp_score)    end
    if(tb.passive_combat_score) then    encoder:addi64(16, tb.passive_combat_score)    end
    if(tb.conquest_vic_score) then    encoder:addi64(17, tb.conquest_vic_score)    end
    if(tb.conquest_defeat_score) then    encoder:addi64(18, tb.conquest_defeat_score)    end
    if(tb.conquest_progress_score) then    encoder:addi64(19, tb.conquest_progress_score)    end
end

function pb.pb_TDMSettlementDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMSettlementData) or {} 
    tb.camp_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.camp_list[k] = pb.pb_TDMCampDecode(v)
    end
    local __my_color = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __my_color ~= 0 then tb.my_color = __my_color end
    tb.mvp_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.mvp_list[k] = pb.pb_TDMMvpDecode(v)
    end
    tb.weapon_change = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.weapon_change[k] = pb.pb_WeaponChangeDecode(v)
    end
    local __game_time = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __game_time ~= 0 then tb.game_time = __game_time end
    tb.account_exp = pb.pb_TDMSettlementAccountExpDecode(decoder:getsubmsg(6))
    tb.vehicle_exp_list = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.vehicle_exp_list[k] = pb.pb_TDMVehicleAddExpDecode(v)
    end
    tb.weapon_exp_list = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.weapon_exp_list[k] = pb.pb_WeaponExpAddDecode(v)
    end
    local __drop_mantel_brick_id = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __drop_mantel_brick_id ~= 0 then tb.drop_mantel_brick_id = __drop_mantel_brick_id end
    tb.mystical_skins = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.mystical_skins[k] = pb.pb_PropInfoDecode(v)
    end
    local __is_ranked_match = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __rank_match_score = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __ranked_score_shoot = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_shoot ~= 0 then tb.ranked_score_shoot = __ranked_score_shoot end
    local __ranked_score_tactics = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_tactics ~= 0 then tb.ranked_score_tactics = __ranked_score_tactics end
    local __ranked_score_vehicle = decoder:geti64(21)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_vehicle ~= 0 then tb.ranked_score_vehicle = __ranked_score_vehicle end
    local __ranked_score_shoot_delta = decoder:geti64(22)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_shoot_delta ~= 0 then tb.ranked_score_shoot_delta = __ranked_score_shoot_delta end
    local __ranked_score_tactics_delta = decoder:geti64(23)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_tactics_delta ~= 0 then tb.ranked_score_tactics_delta = __ranked_score_tactics_delta end
    local __ranked_score_vehicle_delta = decoder:geti64(24)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_vehicle_delta ~= 0 then tb.ranked_score_vehicle_delta = __ranked_score_vehicle_delta end
    tb.rank_shields = {}
    for k,v in pairs(decoder:getsubmsgary(25)) do
        tb.rank_shields[k] = pb.pb_TournamentRankShieldDecode(v)
    end
    local __ranked_score_leave_penalty = decoder:geti64(26)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_leave_penalty ~= 0 then tb.ranked_score_leave_penalty = __ranked_score_leave_penalty end
    local __ranked_score_half_join_score = decoder:geti64(27)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_half_join_score ~= 0 then tb.ranked_score_half_join_score = __ranked_score_half_join_score end
    local __ranked_score_rank_score = decoder:geti64(28)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_rank_score ~= 0 then tb.ranked_score_rank_score = __ranked_score_rank_score end
    local __time_factor = decoder:getdouble(29)
    if not PB_USE_DEFAULT_TABLE or __time_factor ~= 0 then tb.time_factor = __time_factor end
    local __raw_total_ranked_score = decoder:geti64(30)
    if not PB_USE_DEFAULT_TABLE or __raw_total_ranked_score ~= 0 then tb.raw_total_ranked_score = __raw_total_ranked_score end
    local __real_total_ranked_score = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __real_total_ranked_score ~= 0 then tb.real_total_ranked_score = __real_total_ranked_score end
    local __self_ranking = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __self_ranking ~= 0 then tb.self_ranking = __self_ranking end
    local __rank_score_spm = decoder:geti64(33)
    if not PB_USE_DEFAULT_TABLE or __rank_score_spm ~= 0 then tb.rank_score_spm = __rank_score_spm end
    local __rank_score_spm_score = decoder:geti64(34)
    if not PB_USE_DEFAULT_TABLE or __rank_score_spm_score ~= 0 then tb.rank_score_spm_score = __rank_score_spm_score end
    local __rank_score_result_score = decoder:geti64(35)
    if not PB_USE_DEFAULT_TABLE or __rank_score_result_score ~= 0 then tb.rank_score_result_score = __rank_score_result_score end
    local __rank_score_defeated_extra_score = decoder:geti64(36)
    if not PB_USE_DEFAULT_TABLE or __rank_score_defeated_extra_score ~= 0 then tb.rank_score_defeated_extra_score = __rank_score_defeated_extra_score end
    local __rank_score_camps_gap_extra_score = decoder:geti64(37)
    if not PB_USE_DEFAULT_TABLE or __rank_score_camps_gap_extra_score ~= 0 then tb.rank_score_camps_gap_extra_score = __rank_score_camps_gap_extra_score end
    local __rank_score_origin_spm_point = decoder:geti64(38)
    if not PB_USE_DEFAULT_TABLE or __rank_score_origin_spm_point ~= 0 then tb.rank_score_origin_spm_point = __rank_score_origin_spm_point end
    local __rank_score_my_camp_point = decoder:getdouble(39)
    if not PB_USE_DEFAULT_TABLE or __rank_score_my_camp_point ~= 0 then tb.rank_score_my_camp_point = __rank_score_my_camp_point end
    local __rank_score_enemy_camp_point = decoder:getdouble(40)
    if not PB_USE_DEFAULT_TABLE or __rank_score_enemy_camp_point ~= 0 then tb.rank_score_enemy_camp_point = __rank_score_enemy_camp_point end
    local __rank_score_reputation_extra_score = decoder:geti64(41)
    if not PB_USE_DEFAULT_TABLE or __rank_score_reputation_extra_score ~= 0 then tb.rank_score_reputation_extra_score = __rank_score_reputation_extra_score end
    local __rank_score_discount_rate = decoder:getdouble(50)
    if not PB_USE_DEFAULT_TABLE or __rank_score_discount_rate ~= 0 then tb.rank_score_discount_rate = __rank_score_discount_rate end
    local __rank_score_discount_ceiling = decoder:geti64(51)
    if not PB_USE_DEFAULT_TABLE or __rank_score_discount_ceiling ~= 0 then tb.rank_score_discount_ceiling = __rank_score_discount_ceiling end
    tb.vehicles = {}
    for k,v in pairs(decoder:getsubmsgary(42)) do
        tb.vehicles[k] = pb.pb_VehicleDecode(v)
    end
    tb.tdm_point_weight_list = {}
    for k,v in pairs(decoder:getsubmsgary(43)) do
        tb.tdm_point_weight_list[k] = pb.pb_TDMPointWeightDecode(v)
    end
    tb.commander_score = pb.pb_TdmCommanderScoreDecode(decoder:getsubmsg(44))
    local __orgin_commander_score = decoder:geti64(45)
    if not PB_USE_DEFAULT_TABLE or __orgin_commander_score ~= 0 then tb.orgin_commander_score = __orgin_commander_score end
    tb.achive_tasks = {}
    for k,v in pairs(decoder:getsubmsgary(46)) do
        tb.achive_tasks[k] = pb.pb_DsGameAchievementTaskDecode(v)
    end
    local __current_serial_number = decoder:geti32(47)
    if not PB_USE_DEFAULT_TABLE or __current_serial_number ~= 0 then tb.current_serial_number = __current_serial_number end
    tb.mp_weapon_statistics = {}
    for k,v in pairs(decoder:getsubmsgary(48)) do
        tb.mp_weapon_statistics[k] = pb.pb_MPWeaponStatisticsDecode(v)
    end
    local __is_victory_unite_match = decoder:getbool(49)
    if not PB_USE_DEFAULT_TABLE or __is_victory_unite_match ~= false then tb.is_victory_unite_match = __is_victory_unite_match end
    local __use_vehicle = decoder:getbool(10000)
    if not PB_USE_DEFAULT_TABLE or __use_vehicle ~= false then tb.use_vehicle = __use_vehicle end
    return tb
end

function pb.pb_TDMSettlementDataEncode(tb, encoder)
    if(tb.camp_list) then
        for i=1,#(tb.camp_list) do
            pb.pb_TDMCampEncode(tb.camp_list[i], encoder:addsubmsg(1))
        end
    end
    if(tb.my_color) then    encoder:addu32(2, tb.my_color)    end
    if(tb.mvp_list) then
        for i=1,#(tb.mvp_list) do
            pb.pb_TDMMvpEncode(tb.mvp_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.weapon_change) then
        for i=1,#(tb.weapon_change) do
            pb.pb_WeaponChangeEncode(tb.weapon_change[i], encoder:addsubmsg(4))
        end
    end
    if(tb.game_time) then    encoder:addi32(5, tb.game_time)    end
    if(tb.account_exp) then    pb.pb_TDMSettlementAccountExpEncode(tb.account_exp, encoder:addsubmsg(6))    end
    if(tb.vehicle_exp_list) then
        for i=1,#(tb.vehicle_exp_list) do
            pb.pb_TDMVehicleAddExpEncode(tb.vehicle_exp_list[i], encoder:addsubmsg(7))
        end
    end
    if(tb.weapon_exp_list) then
        for i=1,#(tb.weapon_exp_list) do
            pb.pb_WeaponExpAddEncode(tb.weapon_exp_list[i], encoder:addsubmsg(8))
        end
    end
    if(tb.drop_mantel_brick_id) then    encoder:addu64(9, tb.drop_mantel_brick_id)    end
    if(tb.mystical_skins) then
        for i=1,#(tb.mystical_skins) do
            pb.pb_PropInfoEncode(tb.mystical_skins[i], encoder:addsubmsg(15))
        end
    end
    if(tb.is_ranked_match) then    encoder:addbool(16, tb.is_ranked_match)    end
    if(tb.rank_match_score) then    encoder:addi64(18, tb.rank_match_score)    end
    if(tb.ranked_score_shoot) then    encoder:addi64(19, tb.ranked_score_shoot)    end
    if(tb.ranked_score_tactics) then    encoder:addi64(20, tb.ranked_score_tactics)    end
    if(tb.ranked_score_vehicle) then    encoder:addi64(21, tb.ranked_score_vehicle)    end
    if(tb.ranked_score_shoot_delta) then    encoder:addi64(22, tb.ranked_score_shoot_delta)    end
    if(tb.ranked_score_tactics_delta) then    encoder:addi64(23, tb.ranked_score_tactics_delta)    end
    if(tb.ranked_score_vehicle_delta) then    encoder:addi64(24, tb.ranked_score_vehicle_delta)    end
    if(tb.rank_shields) then
        for i=1,#(tb.rank_shields) do
            pb.pb_TournamentRankShieldEncode(tb.rank_shields[i], encoder:addsubmsg(25))
        end
    end
    if(tb.ranked_score_leave_penalty) then    encoder:addi64(26, tb.ranked_score_leave_penalty)    end
    if(tb.ranked_score_half_join_score) then    encoder:addi64(27, tb.ranked_score_half_join_score)    end
    if(tb.ranked_score_rank_score) then    encoder:addi64(28, tb.ranked_score_rank_score)    end
    if(tb.time_factor) then    encoder:adddouble(29, tb.time_factor)    end
    if(tb.raw_total_ranked_score) then    encoder:addi64(30, tb.raw_total_ranked_score)    end
    if(tb.real_total_ranked_score) then    encoder:addi64(31, tb.real_total_ranked_score)    end
    if(tb.self_ranking) then    encoder:addi32(32, tb.self_ranking)    end
    if(tb.rank_score_spm) then    encoder:addi64(33, tb.rank_score_spm)    end
    if(tb.rank_score_spm_score) then    encoder:addi64(34, tb.rank_score_spm_score)    end
    if(tb.rank_score_result_score) then    encoder:addi64(35, tb.rank_score_result_score)    end
    if(tb.rank_score_defeated_extra_score) then    encoder:addi64(36, tb.rank_score_defeated_extra_score)    end
    if(tb.rank_score_camps_gap_extra_score) then    encoder:addi64(37, tb.rank_score_camps_gap_extra_score)    end
    if(tb.rank_score_origin_spm_point) then    encoder:addi64(38, tb.rank_score_origin_spm_point)    end
    if(tb.rank_score_my_camp_point) then    encoder:adddouble(39, tb.rank_score_my_camp_point)    end
    if(tb.rank_score_enemy_camp_point) then    encoder:adddouble(40, tb.rank_score_enemy_camp_point)    end
    if(tb.rank_score_reputation_extra_score) then    encoder:addi64(41, tb.rank_score_reputation_extra_score)    end
    if(tb.rank_score_discount_rate) then    encoder:adddouble(50, tb.rank_score_discount_rate)    end
    if(tb.rank_score_discount_ceiling) then    encoder:addi64(51, tb.rank_score_discount_ceiling)    end
    if(tb.vehicles) then
        for i=1,#(tb.vehicles) do
            pb.pb_VehicleEncode(tb.vehicles[i], encoder:addsubmsg(42))
        end
    end
    if(tb.tdm_point_weight_list) then
        for i=1,#(tb.tdm_point_weight_list) do
            pb.pb_TDMPointWeightEncode(tb.tdm_point_weight_list[i], encoder:addsubmsg(43))
        end
    end
    if(tb.commander_score) then    pb.pb_TdmCommanderScoreEncode(tb.commander_score, encoder:addsubmsg(44))    end
    if(tb.orgin_commander_score) then    encoder:addi64(45, tb.orgin_commander_score)    end
    if(tb.achive_tasks) then
        for i=1,#(tb.achive_tasks) do
            pb.pb_DsGameAchievementTaskEncode(tb.achive_tasks[i], encoder:addsubmsg(46))
        end
    end
    if(tb.current_serial_number) then    encoder:addi32(47, tb.current_serial_number)    end
    if(tb.mp_weapon_statistics) then
        for i=1,#(tb.mp_weapon_statistics) do
            pb.pb_MPWeaponStatisticsEncode(tb.mp_weapon_statistics[i], encoder:addsubmsg(48))
        end
    end
    if(tb.is_victory_unite_match) then    encoder:addbool(49, tb.is_victory_unite_match)    end
    if(tb.use_vehicle) then    encoder:addbool(10000, tb.use_vehicle)    end
end

function pb.pb_TDMTeamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMTeam) or {} 
    local __team_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.player_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.player_list[k] = pb.pb_TDMPlayerDecode(v)
    end
    local __team_rank = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __team_rank ~= 0 then tb.team_rank = __team_rank end
    local __team_leader = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __team_leader ~= 0 then tb.team_leader = __team_leader end
    return tb
end

function pb.pb_TDMTeamEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu32(1, tb.team_id)    end
    if(tb.player_list) then
        for i=1,#(tb.player_list) do
            pb.pb_TDMPlayerEncode(tb.player_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.team_rank) then    encoder:addi32(3, tb.team_rank)    end
    if(tb.team_leader) then    encoder:addu64(4, tb.team_leader)    end
end

function pb.pb_SettlementNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SettlementNtf) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.match_info = pb.pb_DsMatchInfoNewDecode(decoder:getsubmsg(2))
    tb.team_info = pb.pb_DsTeamInfoDecode(decoder:getsubmsg(3))
    tb.status_array = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.status_array[k] = pb.pb_DsPlayerGameStatusDecode(v)
    end
    tb.raid_player_status_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.raid_player_status_array[k] = pb.pb_DsRaidPlayerGameStatusDecode(v)
    end
    local __raid_task_succ = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __raid_task_succ ~= false then tb.raid_task_succ = __raid_task_succ end
    tb.tdm_data = pb.pb_TDMSettlementDataDecode(decoder:getsubmsg(7))
    tb.player_quests = pb.pb_DsQuestDataDecode(decoder:getsubmsg(8))
    tb.marked_props = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.marked_props[k] = pb.pb_MarkPropInfoDecode(v)
    end
    local __match_tactics = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __match_tactics ~= 0 then tb.match_tactics = __match_tactics end
    tb.sol_data = pb.pb_SolSettlementDataDecode(decoder:getsubmsg(13))
    local __dsa_id = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __dsa_id ~= "" then tb.dsa_id = __dsa_id end
    local __not_cmp_state_room_id = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __not_cmp_state_room_id ~= false then tb.not_cmp_state_room_id = __not_cmp_state_room_id end
    tb.activity_data = pb.pb_NumeralActivityDataDecode(decoder:getsubmsg(20))
    tb.room_attr = pb.pb_RoomAttrDecode(decoder:getsubmsg(21))
    tb.bhd_data = pb.pb_BhdSettlementDataDecode(decoder:getsubmsg(22))
    tb.arn_data = pb.pb_ArenaSettlementDataDecode(decoder:getsubmsg(23))
    local __exactly_leave = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __exactly_leave ~= 0 then tb.exactly_leave = __exactly_leave end
    local __client_group = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __client_group ~= 0 then tb.client_group = __client_group end
    local __sys_team_id = decoder:getu64(26)
    if not PB_USE_DEFAULT_TABLE or __sys_team_id ~= 0 then tb.sys_team_id = __sys_team_id end
    return tb
end

function pb.pb_SettlementNtfEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.match_info) then    pb.pb_DsMatchInfoNewEncode(tb.match_info, encoder:addsubmsg(2))    end
    if(tb.team_info) then    pb.pb_DsTeamInfoEncode(tb.team_info, encoder:addsubmsg(3))    end
    if(tb.status_array) then
        for i=1,#(tb.status_array) do
            pb.pb_DsPlayerGameStatusEncode(tb.status_array[i], encoder:addsubmsg(4))
        end
    end
    if(tb.raid_player_status_array) then
        for i=1,#(tb.raid_player_status_array) do
            pb.pb_DsRaidPlayerGameStatusEncode(tb.raid_player_status_array[i], encoder:addsubmsg(5))
        end
    end
    if(tb.raid_task_succ) then    encoder:addbool(6, tb.raid_task_succ)    end
    if(tb.tdm_data) then    pb.pb_TDMSettlementDataEncode(tb.tdm_data, encoder:addsubmsg(7))    end
    if(tb.player_quests) then    pb.pb_DsQuestDataEncode(tb.player_quests, encoder:addsubmsg(8))    end
    if(tb.marked_props) then
        for i=1,#(tb.marked_props) do
            pb.pb_MarkPropInfoEncode(tb.marked_props[i], encoder:addsubmsg(9))
        end
    end
    if(tb.match_tactics) then    encoder:addu32(10, tb.match_tactics)    end
    if(tb.sol_data) then    pb.pb_SolSettlementDataEncode(tb.sol_data, encoder:addsubmsg(13))    end
    if(tb.dsa_id) then    encoder:addstr(11, tb.dsa_id)    end
    if(tb.not_cmp_state_room_id) then    encoder:addbool(12, tb.not_cmp_state_room_id)    end
    if(tb.activity_data) then    pb.pb_NumeralActivityDataEncode(tb.activity_data, encoder:addsubmsg(20))    end
    if(tb.room_attr) then    pb.pb_RoomAttrEncode(tb.room_attr, encoder:addsubmsg(21))    end
    if(tb.bhd_data) then    pb.pb_BhdSettlementDataEncode(tb.bhd_data, encoder:addsubmsg(22))    end
    if(tb.arn_data) then    pb.pb_ArenaSettlementDataEncode(tb.arn_data, encoder:addsubmsg(23))    end
    if(tb.exactly_leave) then    encoder:addi32(24, tb.exactly_leave)    end
    if(tb.client_group) then    encoder:addi32(25, tb.client_group)    end
    if(tb.sys_team_id) then    encoder:addu64(26, tb.sys_team_id)    end
end

function pb.pb_SolSettlementDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SolSettlementData) or {} 
    local __dps_float = decoder:getfloat(99)
    if not PB_USE_DEFAULT_TABLE or __dps_float ~= 0 then tb.dps_float = __dps_float end
    local __dps = decoder:geti64(100)
    if not PB_USE_DEFAULT_TABLE or __dps ~= 0 then tb.dps = __dps end
    local __last_time_props_price = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __last_time_props_price ~= 0 then tb.last_time_props_price = __last_time_props_price end
    local __loot_total_price = decoder:geti64(102)
    if not PB_USE_DEFAULT_TABLE or __loot_total_price ~= 0 then tb.loot_total_price = __loot_total_price end
    local __loot_expected_price = decoder:geti64(103)
    if not PB_USE_DEFAULT_TABLE or __loot_expected_price ~= 0 then tb.loot_expected_price = __loot_expected_price end
    local __contract_num = decoder:geti32(104)
    if not PB_USE_DEFAULT_TABLE or __contract_num ~= 0 then tb.contract_num = __contract_num end
    local __fighting_with_player_num = decoder:geti32(105)
    if not PB_USE_DEFAULT_TABLE or __fighting_with_player_num ~= 0 then tb.fighting_with_player_num = __fighting_with_player_num end
    local __fighting_with_ai_num = decoder:geti32(106)
    if not PB_USE_DEFAULT_TABLE or __fighting_with_ai_num ~= 0 then tb.fighting_with_ai_num = __fighting_with_ai_num end
    local __loot_cnt = decoder:geti32(107)
    if not PB_USE_DEFAULT_TABLE or __loot_cnt ~= 0 then tb.loot_cnt = __loot_cnt end
    local __ttk = decoder:getfloat(108)
    if not PB_USE_DEFAULT_TABLE or __ttk ~= 0 then tb.ttk = __ttk end
    local __hits = decoder:getfloat(109)
    if not PB_USE_DEFAULT_TABLE or __hits ~= 0 then tb.hits = __hits end
    local __ai_ttk = decoder:getfloat(110)
    if not PB_USE_DEFAULT_TABLE or __ai_ttk ~= 0 then tb.ai_ttk = __ai_ttk end
    local __total_high_quality_loot = decoder:geti32(111)
    if not PB_USE_DEFAULT_TABLE or __total_high_quality_loot ~= 0 then tb.total_high_quality_loot = __total_high_quality_loot end
    local __assist_cnt = decoder:geti32(112)
    if not PB_USE_DEFAULT_TABLE or __assist_cnt ~= 0 then tb.assist_cnt = __assist_cnt end
    tb.drop_counters = {}
    for k,v in pairs(decoder:getsubmsgary(113)) do
        tb.drop_counters[k] = pb.pb_MatchSOLDropCounterDecode(v)
    end
    return tb
end

function pb.pb_SolSettlementDataEncode(tb, encoder)
    if(tb.dps_float) then    encoder:addfloat(99, tb.dps_float)    end
    if(tb.dps) then    encoder:addi64(100, tb.dps)    end
    if(tb.last_time_props_price) then    encoder:addi64(101, tb.last_time_props_price)    end
    if(tb.loot_total_price) then    encoder:addi64(102, tb.loot_total_price)    end
    if(tb.loot_expected_price) then    encoder:addi64(103, tb.loot_expected_price)    end
    if(tb.contract_num) then    encoder:addi32(104, tb.contract_num)    end
    if(tb.fighting_with_player_num) then    encoder:addi32(105, tb.fighting_with_player_num)    end
    if(tb.fighting_with_ai_num) then    encoder:addi32(106, tb.fighting_with_ai_num)    end
    if(tb.loot_cnt) then    encoder:addi32(107, tb.loot_cnt)    end
    if(tb.ttk) then    encoder:addfloat(108, tb.ttk)    end
    if(tb.hits) then    encoder:addfloat(109, tb.hits)    end
    if(tb.ai_ttk) then    encoder:addfloat(110, tb.ai_ttk)    end
    if(tb.total_high_quality_loot) then    encoder:addi32(111, tb.total_high_quality_loot)    end
    if(tb.assist_cnt) then    encoder:addi32(112, tb.assist_cnt)    end
    if(tb.drop_counters) then
        for i=1,#(tb.drop_counters) do
            pb.pb_MatchSOLDropCounterEncode(tb.drop_counters[i], encoder:addsubmsg(113))
        end
    end
end

function pb.pb_DsAIBasicInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsAIBasicInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __game_nick = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __game_nick ~= "" then tb.game_nick = __game_nick end
    local __gender = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __gender ~= 0 then tb.gender = __gender end
    tb.tag_array = decoder:getstrary(5)
    local __level = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __ai_lab_level = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __ai_lab_level ~= 0 then tb.ai_lab_level = __ai_lab_level end
    local __rank_match_score = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    return tb
end

function pb.pb_DsAIBasicInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.game_nick) then    encoder:addstr(2, tb.game_nick)    end
    if(tb.gender) then    encoder:addi32(3, tb.gender)    end
    if(tb.tag_array) then    encoder:addstr(5, tb.tag_array)    end
    if(tb.level) then    encoder:addi32(6, tb.level)    end
    if(tb.ai_lab_level) then    encoder:addi32(7, tb.ai_lab_level)    end
    if(tb.rank_match_score) then    encoder:addi64(8, tb.rank_match_score)    end
end

function pb.pb_DsAccidentInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsAccidentInfo) or {} 
    local __timestamp = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_DsPlayerPositionDecode(decoder:getsubmsg(2))
    local __accident_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __accident_type ~= 0 then tb.accident_type = __accident_type end
    local __body_part = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __body_part ~= 0 then tb.body_part = __body_part end
    return tb
end

function pb.pb_DsAccidentInfoEncode(tb, encoder)
    if(tb.timestamp) then    encoder:addi32(1, tb.timestamp)    end
    if(tb.position) then    pb.pb_DsPlayerPositionEncode(tb.position, encoder:addsubmsg(2))    end
    if(tb.accident_type) then    encoder:addi32(3, tb.accident_type)    end
    if(tb.body_part) then    encoder:addi32(4, tb.body_part)    end
end

function pb.pb_DsAchievementInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsAchievementInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    tb.score_map = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.score_map[k] = pb.pb_DsAchievementInfo_ScoreMapEntryDecode(v)
    end
    return tb
end

function pb.pb_DsAchievementInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.score_map) then
        for i=1,#(tb.score_map) do
            pb.pb_DsAchievementInfo_ScoreMapEntryEncode(tb.score_map[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_DsDamageInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsDamageInfo) or {} 
    local __timestamp = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_DsPlayerPositionDecode(decoder:getsubmsg(8))
    local __equipment_recorded = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __equipment_recorded ~= false then tb.equipment_recorded = __equipment_recorded end
    tb.equipment = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.equipment[k] = pb.pb_EquipPositionDecode(v)
    end
    local __has_weapon = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __has_weapon ~= false then tb.has_weapon = __has_weapon end
    local __weapon = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __weapon ~= 0 then tb.weapon = __weapon end
    tb.weapon_prop = pb.pb_PropInfoDecode(decoder:getsubmsg(11))
    local __body_health = decoder:getfloat(14)
    if not PB_USE_DEFAULT_TABLE or __body_health ~= 0 then tb.body_health = __body_health end
    local __status = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __status ~= 0 then tb.status = __status end
    local __receiver_equipment_recorded = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __receiver_equipment_recorded ~= false then tb.receiver_equipment_recorded = __receiver_equipment_recorded end
    tb.receiver_equipment = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.receiver_equipment[k] = pb.pb_EquipPositionDecode(v)
    end
    local __receiver_body_health = decoder:getfloat(15)
    if not PB_USE_DEFAULT_TABLE or __receiver_body_health ~= 0 then tb.receiver_body_health = __receiver_body_health end
    local __receiver_status = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __receiver_status ~= 0 then tb.receiver_status = __receiver_status end
    local __has_ammo = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __has_ammo ~= false then tb.has_ammo = __has_ammo end
    local __ammo = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __ammo ~= 0 then tb.ammo = __ammo end
    local __damage = decoder:getdouble(5)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __body_part = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __body_part ~= 0 then tb.body_part = __body_part end
    local __hit_distance = decoder:getfloat(12)
    if not PB_USE_DEFAULT_TABLE or __hit_distance ~= 0 then tb.hit_distance = __hit_distance end
    return tb
end

function pb.pb_DsDamageInfoEncode(tb, encoder)
    if(tb.timestamp) then    encoder:addi32(7, tb.timestamp)    end
    if(tb.position) then    pb.pb_DsPlayerPositionEncode(tb.position, encoder:addsubmsg(8))    end
    if(tb.equipment_recorded) then    encoder:addbool(9, tb.equipment_recorded)    end
    if(tb.equipment) then
        for i=1,#(tb.equipment) do
            pb.pb_EquipPositionEncode(tb.equipment[i], encoder:addsubmsg(1))
        end
    end
    if(tb.has_weapon) then    encoder:addbool(10, tb.has_weapon)    end
    if(tb.weapon) then    encoder:addu64(2, tb.weapon)    end
    if(tb.weapon_prop) then    pb.pb_PropInfoEncode(tb.weapon_prop, encoder:addsubmsg(11))    end
    if(tb.body_health) then    encoder:addfloat(14, tb.body_health)    end
    if(tb.status) then    encoder:addu32(17, tb.status)    end
    if(tb.receiver_equipment_recorded) then    encoder:addbool(16, tb.receiver_equipment_recorded)    end
    if(tb.receiver_equipment) then
        for i=1,#(tb.receiver_equipment) do
            pb.pb_EquipPositionEncode(tb.receiver_equipment[i], encoder:addsubmsg(13))
        end
    end
    if(tb.receiver_body_health) then    encoder:addfloat(15, tb.receiver_body_health)    end
    if(tb.receiver_status) then    encoder:addu32(18, tb.receiver_status)    end
    if(tb.has_ammo) then    encoder:addbool(3, tb.has_ammo)    end
    if(tb.ammo) then    encoder:addu64(4, tb.ammo)    end
    if(tb.damage) then    encoder:adddouble(5, tb.damage)    end
    if(tb.body_part) then    encoder:addi32(6, tb.body_part)    end
    if(tb.hit_distance) then    encoder:addfloat(12, tb.hit_distance)    end
end

function pb.pb_DsEndGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsEndGamePlayerStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __extraction_point = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __extraction_point ~= 0 then tb.extraction_point = __extraction_point end
    tb.death_info = pb.pb_DsPlayerDeathInfoDecode(decoder:getsubmsg(3))
    local __extraction_location = decoder:getstr(24)
    if not PB_USE_DEFAULT_TABLE or __extraction_location ~= "" then tb.extraction_location = __extraction_location end
    local __play_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __has_main_weapon = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __has_main_weapon ~= false then tb.has_main_weapon = __has_main_weapon end
    local __main_weapon = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __main_weapon ~= 0 then tb.main_weapon = __main_weapon end
    tb.match_log = {}
    for k,v in pairs(decoder:getsubmsgary(18)) do
        tb.match_log[k] = pb.pb_DsMatchEventDecode(v)
    end
    tb.match_stat = pb.pb_DsPlayerMatchStatNewDecode(decoder:getsubmsg(19))
    local __individual_rank = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.attr_array = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.attr_array[k] = pb.pb_DSAttrValueDecode(v)
    end
    tb.buff_array = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.buff_array[k] = pb.pb_AttrBuffDecode(v)
    end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.final_props = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.final_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.kill_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.knock_down_array = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.knock_down_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.achievement_array[k] = pb.pb_DsAchievementInfoDecode(v)
    end
    tb.C_price_values = decoder:geti32ary(16)
    tb.carry_out_health_list = {}
    for k,v in pairs(decoder:getsubmsgary(17)) do
        tb.carry_out_health_list[k] = pb.pb_EquipHealthDecode(v)
    end
    local __carry_out_props_price = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __carry_out_props_price ~= 0 then tb.carry_out_props_price = __carry_out_props_price end
    local __carry_in_props_price = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __carry_in_props_price ~= 0 then tb.carry_in_props_price = __carry_in_props_price end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(25))
    local __blue_print_special_id = decoder:getu64(26)
    if not PB_USE_DEFAULT_TABLE or __blue_print_special_id ~= 0 then tb.blue_print_special_id = __blue_print_special_id end
    local __asset_increment_price = decoder:geti64(27)
    if not PB_USE_DEFAULT_TABLE or __asset_increment_price ~= 0 then tb.asset_increment_price = __asset_increment_price end
    local __blue_print_type = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __blue_print_type ~= 0 then tb.blue_print_type = __blue_print_type end
    local __blue_print_price = decoder:geti64(29)
    if not PB_USE_DEFAULT_TABLE or __blue_print_price ~= 0 then tb.blue_print_price = __blue_print_price end
    local __contract_quest_price = decoder:geti64(30)
    if not PB_USE_DEFAULT_TABLE or __contract_quest_price ~= 0 then tb.contract_quest_price = __contract_quest_price end
    local __carry_out_new_props_price = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __carry_out_new_props_price ~= 0 then tb.carry_out_new_props_price = __carry_out_new_props_price end
    local __rescue_count = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __resurgence_count = decoder:geti32(33)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    tb.shoot_down_info = pb.pb_DsPlayerKillInfoDecode(decoder:getsubmsg(34))
    local __cost_price = decoder:geti32(35)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    local __carry_out_profit_price = decoder:geti32(36)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    tb.shoot_down_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(37)) do
        tb.shoot_down_info_list[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    local __carry_in_equip_price = decoder:geti32(38)
    if not PB_USE_DEFAULT_TABLE or __carry_in_equip_price ~= 0 then tb.carry_in_equip_price = __carry_in_equip_price end
    local __carry_out_without_teammate_price = decoder:geti64(39)
    if not PB_USE_DEFAULT_TABLE or __carry_out_without_teammate_price ~= 0 then tb.carry_out_without_teammate_price = __carry_out_without_teammate_price end
    return tb
end

function pb.pb_DsEndGamePlayerStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.extraction_point) then    encoder:addi32(2, tb.extraction_point)    end
    if(tb.death_info) then    pb.pb_DsPlayerDeathInfoEncode(tb.death_info, encoder:addsubmsg(3))    end
    if(tb.extraction_location) then    encoder:addstr(24, tb.extraction_location)    end
    if(tb.play_time) then    encoder:addi32(4, tb.play_time)    end
    if(tb.has_main_weapon) then    encoder:addbool(20, tb.has_main_weapon)    end
    if(tb.main_weapon) then    encoder:addu64(5, tb.main_weapon)    end
    if(tb.match_log) then
        for i=1,#(tb.match_log) do
            pb.pb_DsMatchEventEncode(tb.match_log[i], encoder:addsubmsg(18))
        end
    end
    if(tb.match_stat) then    pb.pb_DsPlayerMatchStatNewEncode(tb.match_stat, encoder:addsubmsg(19))    end
    if(tb.individual_rank) then    encoder:addi32(9, tb.individual_rank)    end
    if(tb.attr_array) then
        for i=1,#(tb.attr_array) do
            pb.pb_DSAttrValueEncode(tb.attr_array[i], encoder:addsubmsg(10))
        end
    end
    if(tb.buff_array) then
        for i=1,#(tb.buff_array) do
            pb.pb_AttrBuffEncode(tb.buff_array[i], encoder:addsubmsg(11))
        end
    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(12))
        end
    end
    if(tb.final_props) then
        for i=1,#(tb.final_props) do
            pb.pb_EquipPositionEncode(tb.final_props[i], encoder:addsubmsg(13))
        end
    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(14))
        end
    end
    if(tb.knock_down_array) then
        for i=1,#(tb.knock_down_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.knock_down_array[i], encoder:addsubmsg(21))
        end
    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_DsAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(15))
        end
    end
    if(tb.C_price_values) then    encoder:addi32(16, tb.C_price_values)    end
    if(tb.carry_out_health_list) then
        for i=1,#(tb.carry_out_health_list) do
            pb.pb_EquipHealthEncode(tb.carry_out_health_list[i], encoder:addsubmsg(17))
        end
    end
    if(tb.carry_out_props_price) then    encoder:addi32(22, tb.carry_out_props_price)    end
    if(tb.carry_in_props_price) then    encoder:addi32(23, tb.carry_in_props_price)    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(25))    end
    if(tb.blue_print_special_id) then    encoder:addu64(26, tb.blue_print_special_id)    end
    if(tb.asset_increment_price) then    encoder:addi64(27, tb.asset_increment_price)    end
    if(tb.blue_print_type) then    encoder:addi32(28, tb.blue_print_type)    end
    if(tb.blue_print_price) then    encoder:addi64(29, tb.blue_print_price)    end
    if(tb.contract_quest_price) then    encoder:addi64(30, tb.contract_quest_price)    end
    if(tb.carry_out_new_props_price) then    encoder:addi64(31, tb.carry_out_new_props_price)    end
    if(tb.rescue_count) then    encoder:addi32(32, tb.rescue_count)    end
    if(tb.resurgence_count) then    encoder:addi32(33, tb.resurgence_count)    end
    if(tb.shoot_down_info) then    pb.pb_DsPlayerKillInfoEncode(tb.shoot_down_info, encoder:addsubmsg(34))    end
    if(tb.cost_price) then    encoder:addi32(35, tb.cost_price)    end
    if(tb.carry_out_profit_price) then    encoder:addi32(36, tb.carry_out_profit_price)    end
    if(tb.shoot_down_info_list) then
        for i=1,#(tb.shoot_down_info_list) do
            pb.pb_DsPlayerKillInfoEncode(tb.shoot_down_info_list[i], encoder:addsubmsg(37))
        end
    end
    if(tb.carry_in_equip_price) then    encoder:addi32(38, tb.carry_in_equip_price)    end
    if(tb.carry_out_without_teammate_price) then    encoder:addi64(39, tb.carry_out_without_teammate_price)    end
end

function pb.pb_DsMatchEventDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsMatchEvent) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __timestamp = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_DsPlayerPositionDecode(decoder:getsubmsg(3))
    tb.param_map = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.param_map[k] = pb.pb_DsMatchEvent_ParamMapEntryDecode(v)
    end
    local __kill_count = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __kill_count ~= 0 then tb.kill_count = __kill_count end
    local __total_price = decoder:getdouble(6)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __produced_price = decoder:getdouble(7)
    if not PB_USE_DEFAULT_TABLE or __produced_price ~= 0 then tb.produced_price = __produced_price end
    local __KM_traveled = decoder:getfloat(8)
    if not PB_USE_DEFAULT_TABLE or __KM_traveled ~= 0 then tb.KM_traveled = __KM_traveled end
    return tb
end

function pb.pb_DsMatchEventEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.timestamp) then    encoder:addi32(2, tb.timestamp)    end
    if(tb.position) then    pb.pb_DsPlayerPositionEncode(tb.position, encoder:addsubmsg(3))    end
    if(tb.param_map) then
        for i=1,#(tb.param_map) do
            pb.pb_DsMatchEvent_ParamMapEntryEncode(tb.param_map[i], encoder:addsubmsg(4))
        end
    end
    if(tb.kill_count) then    encoder:addi32(5, tb.kill_count)    end
    if(tb.total_price) then    encoder:adddouble(6, tb.total_price)    end
    if(tb.produced_price) then    encoder:adddouble(7, tb.produced_price)    end
    if(tb.KM_traveled) then    encoder:addfloat(8, tb.KM_traveled)    end
end

function pb.pb_DsMatchInfoNewDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsMatchInfoNew) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __map_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __match_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_subtype = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __match_mode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    local __team_size = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __team_size ~= 0 then tb.team_size = __team_size end
    local __match_pool_id = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __match_pool_id ~= 0 then tb.match_pool_id = __match_pool_id end
    local __start_time = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __player_count = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __player_count ~= 0 then tb.player_count = __player_count end
    local __ai_count = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __ai_count ~= 0 then tb.ai_count = __ai_count end
    local __boss_count = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __boss_count ~= 0 then tb.boss_count = __boss_count end
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(12))
    local __is_guide = decoder:getbool(13)
    if not PB_USE_DEFAULT_TABLE or __is_guide ~= false then tb.is_guide = __is_guide end
    local __ai_level = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __ai_level ~= 0 then tb.ai_level = __ai_level end
    return tb
end

function pb.pb_DsMatchInfoNewEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.map_id) then    encoder:addi32(2, tb.map_id)    end
    if(tb.match_type) then    encoder:addi32(3, tb.match_type)    end
    if(tb.match_subtype) then    encoder:addi32(9, tb.match_subtype)    end
    if(tb.match_mode) then    encoder:addi32(4, tb.match_mode)    end
    if(tb.team_size) then    encoder:addi32(8, tb.team_size)    end
    if(tb.match_pool_id) then    encoder:addi32(11, tb.match_pool_id)    end
    if(tb.start_time) then    encoder:addu64(5, tb.start_time)    end
    if(tb.player_count) then    encoder:addi32(6, tb.player_count)    end
    if(tb.ai_count) then    encoder:addi32(7, tb.ai_count)    end
    if(tb.boss_count) then    encoder:addi32(10, tb.boss_count)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(12))    end
    if(tb.is_guide) then    encoder:addbool(13, tb.is_guide)    end
    if(tb.ai_level) then    encoder:addi32(14, tb.ai_level)    end
end

function pb.pb_DsPVEEndGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPVEEndGamePlayerStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __play_time = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.kill_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.mission_stat = pb.pb_DsPVEMissionStatDecode(decoder:getsubmsg(4))
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.achievement_array[k] = pb.pb_DsAchievementInfoDecode(v)
    end
    return tb
end

function pb.pb_DsPVEEndGamePlayerStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.play_time) then    encoder:addi32(2, tb.play_time)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(3))
        end
    end
    if(tb.mission_stat) then    pb.pb_DsPVEMissionStatEncode(tb.mission_stat, encoder:addsubmsg(4))    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_DsAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_DsPVEMissionInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPVEMissionInfo) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __match_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_subtype = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __match_mode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    local __team_size = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __team_size ~= 0 then tb.team_size = __team_size end
    local __map_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __start_time = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __player_count = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __player_count ~= 0 then tb.player_count = __player_count end
    return tb
end

function pb.pb_DsPVEMissionInfoEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.match_type) then    encoder:addi32(2, tb.match_type)    end
    if(tb.match_subtype) then    encoder:addi32(3, tb.match_subtype)    end
    if(tb.match_mode) then    encoder:addi32(4, tb.match_mode)    end
    if(tb.team_size) then    encoder:addi32(5, tb.team_size)    end
    if(tb.map_id) then    encoder:addu64(6, tb.map_id)    end
    if(tb.start_time) then    encoder:addu64(7, tb.start_time)    end
    if(tb.player_count) then    encoder:addi32(8, tb.player_count)    end
end

function pb.pb_DsPVEMissionStatDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPVEMissionStat) or {} 
    local __death_count = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __death_count ~= 0 then tb.death_count = __death_count end
    local __task_count = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __task_count ~= 0 then tb.task_count = __task_count end
    return tb
end

function pb.pb_DsPVEMissionStatEncode(tb, encoder)
    if(tb.death_count) then    encoder:addi64(1, tb.death_count)    end
    if(tb.task_count) then    encoder:addi64(2, tb.task_count)    end
end

function pb.pb_DsPVEPlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPVEPlayerStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.basic_info = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(2))
    tb.equiped_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.equiped_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.end_game = pb.pb_DsPVEEndGamePlayerStatusDecode(decoder:getsubmsg(4))
    tb.quit_game = pb.pb_DsPVEQuitGamePlayerStatusDecode(decoder:getsubmsg(5))
    tb.playing = pb.pb_DsPVEPlayingGamePlayerStatusDecode(decoder:getsubmsg(6))
    return tb
end

function pb.pb_DsPVEPlayerStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.basic_info) then    pb.pb_DsPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.equiped_props) then
        for i=1,#(tb.equiped_props) do
            pb.pb_EquipPositionEncode(tb.equiped_props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.end_game) then    pb.pb_DsPVEEndGamePlayerStatusEncode(tb.end_game, encoder:addsubmsg(4))    end
    if(tb.quit_game) then    pb.pb_DsPVEQuitGamePlayerStatusEncode(tb.quit_game, encoder:addsubmsg(5))    end
    if(tb.playing) then    pb.pb_DsPVEPlayingGamePlayerStatusEncode(tb.playing, encoder:addsubmsg(6))    end
end

function pb.pb_DsPVEPlayingGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPVEPlayingGamePlayerStatus) or {} 
    return tb
end

function pb.pb_DsPVEPlayingGamePlayerStatusEncode(tb, encoder)
end

function pb.pb_DsPVEQuitGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPVEQuitGamePlayerStatus) or {} 
    local __play_time = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.kill_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.mission_stat = pb.pb_DsPVEMissionStatDecode(decoder:getsubmsg(3))
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.achievement_array[k] = pb.pb_DsAchievementInfoDecode(v)
    end
    return tb
end

function pb.pb_DsPVEQuitGamePlayerStatusEncode(tb, encoder)
    if(tb.play_time) then    encoder:addi32(1, tb.play_time)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(2))
        end
    end
    if(tb.mission_stat) then    pb.pb_DsPVEMissionStatEncode(tb.mission_stat, encoder:addsubmsg(3))    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_DsAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_DsPlayerBasicInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerBasicInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __game_nick = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __game_nick ~= "" then tb.game_nick = __game_nick end
    local __gender = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __gender ~= 0 then tb.gender = __gender end
    local __avatar = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __avatar ~= "" then tb.avatar = __avatar end
    local __background = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __background ~= "" then tb.background = __background end
    local __level = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __grade = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __grade ~= 0 then tb.grade = __grade end
    local __is_scav = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __is_scav ~= false then tb.is_scav = __is_scav end
    local __team_seqno = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __team_seqno ~= 0 then tb.team_seqno = __team_seqno end
    local __skill_score = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __skill_score ~= 0 then tb.skill_score = __skill_score end
    local __be_praised_num = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __be_praised_num ~= 0 then tb.be_praised_num = __be_praised_num end
    local __glicko_rating = decoder:getfloat(12)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating ~= 0 then tb.glicko_rating = __glicko_rating end
    local __glicko_rating_dev = decoder:getfloat(13)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_dev ~= 0 then tb.glicko_rating_dev = __glicko_rating_dev end
    local __rank_match_score = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    tb.scheme_detail = pb.pb_TDMDsDetailDecode(decoder:getsubmsg(15))
    local __old_glicko_rating = decoder:getfloat(16)
    if not PB_USE_DEFAULT_TABLE or __old_glicko_rating ~= 0 then tb.old_glicko_rating = __old_glicko_rating end
    local __old_glicko_rating_dev = decoder:getfloat(17)
    if not PB_USE_DEFAULT_TABLE or __old_glicko_rating_dev ~= 0 then tb.old_glicko_rating_dev = __old_glicko_rating_dev end
    local __title = decoder:getu64(18)
    if not PB_USE_DEFAULT_TABLE or __title ~= 0 then tb.title = __title end
    local __hero_id = decoder:getu64(19)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __plat_id = decoder:geti32(20)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(21)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    return tb
end

function pb.pb_DsPlayerBasicInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.game_nick) then    encoder:addstr(2, tb.game_nick)    end
    if(tb.gender) then    encoder:addi32(3, tb.gender)    end
    if(tb.avatar) then    encoder:addstr(4, tb.avatar)    end
    if(tb.background) then    encoder:addstr(5, tb.background)    end
    if(tb.level) then    encoder:addi32(6, tb.level)    end
    if(tb.grade) then    encoder:addi32(7, tb.grade)    end
    if(tb.is_scav) then    encoder:addbool(9, tb.is_scav)    end
    if(tb.team_seqno) then    encoder:addi32(8, tb.team_seqno)    end
    if(tb.skill_score) then    encoder:addi64(10, tb.skill_score)    end
    if(tb.be_praised_num) then    encoder:addi32(11, tb.be_praised_num)    end
    if(tb.glicko_rating) then    encoder:addfloat(12, tb.glicko_rating)    end
    if(tb.glicko_rating_dev) then    encoder:addfloat(13, tb.glicko_rating_dev)    end
    if(tb.rank_match_score) then    encoder:addi64(14, tb.rank_match_score)    end
    if(tb.scheme_detail) then    pb.pb_TDMDsDetailEncode(tb.scheme_detail, encoder:addsubmsg(15))    end
    if(tb.old_glicko_rating) then    encoder:addfloat(16, tb.old_glicko_rating)    end
    if(tb.old_glicko_rating_dev) then    encoder:addfloat(17, tb.old_glicko_rating_dev)    end
    if(tb.title) then    encoder:addu64(18, tb.title)    end
    if(tb.hero_id) then    encoder:addu64(19, tb.hero_id)    end
    if(tb.plat_id) then    encoder:addi32(20, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(21, tb.account_type)    end
end

function pb.pb_DsPlayerDeathInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerDeathInfo) or {} 
    local __reason = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __timestamp = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_DsPlayerPositionDecode(decoder:getsubmsg(15))
    tb.damage = pb.pb_DsDamageInfoDecode(decoder:getsubmsg(9))
    tb.accident = pb.pb_DsAccidentInfoDecode(decoder:getsubmsg(13))
    tb.player = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(10))
    tb.ai = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(11))
    tb.boss = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(12))
    return tb
end

function pb.pb_DsPlayerDeathInfoEncode(tb, encoder)
    if(tb.reason) then    encoder:addi32(1, tb.reason)    end
    if(tb.timestamp) then    encoder:addi32(14, tb.timestamp)    end
    if(tb.position) then    pb.pb_DsPlayerPositionEncode(tb.position, encoder:addsubmsg(15))    end
    if(tb.damage) then    pb.pb_DsDamageInfoEncode(tb.damage, encoder:addsubmsg(9))    end
    if(tb.accident) then    pb.pb_DsAccidentInfoEncode(tb.accident, encoder:addsubmsg(13))    end
    if(tb.player) then    pb.pb_DsPlayerBasicInfoEncode(tb.player, encoder:addsubmsg(10))    end
    if(tb.ai) then    pb.pb_DsAIBasicInfoEncode(tb.ai, encoder:addsubmsg(11))    end
    if(tb.boss) then    pb.pb_DsAIBasicInfoEncode(tb.boss, encoder:addsubmsg(12))    end
end

function pb.pb_DsGameAchievementDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsGameAchievement) or {} 
    local __id = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __value = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    local __required = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __required ~= 0 then tb.required = __required end
    return tb
end

function pb.pb_DsGameAchievementEncode(tb, encoder)
    if(tb.id) then    encoder:addi64(1, tb.id)    end
    if(tb.value) then    encoder:addi64(2, tb.value)    end
    if(tb.required) then    encoder:addi64(3, tb.required)    end
end

function pb.pb_DsGameAchievementTaskDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsGameAchievementTask) or {} 
    local __task_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __badge_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __badge_id ~= 0 then tb.badge_id = __badge_id end
    tb.achievements = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    return tb
end

function pb.pb_DsGameAchievementTaskEncode(tb, encoder)
    if(tb.task_id) then    encoder:addu64(1, tb.task_id)    end
    if(tb.badge_id) then    encoder:addu64(2, tb.badge_id)    end
    if(tb.achievements) then
        for i=1,#(tb.achievements) do
            pb.pb_DsGameAchievementEncode(tb.achievements[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_DsPlayerGameStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerGameStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.basic_info = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(2))
    local __mode_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __mode_id ~= 0 then tb.mode_id = __mode_id end
    local __player_start = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __player_start ~= 0 then tb.player_start = __player_start end
    tb.playing = pb.pb_DsPlayingPlayerStatusDecode(decoder:getsubmsg(5))
    tb.end_game = pb.pb_DsEndGamePlayerStatusDecode(decoder:getsubmsg(3))
    tb.quit_game = pb.pb_DsQuitGamePlayerStatusDecode(decoder:getsubmsg(4))
    tb.equip_use_time = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.equip_use_time[k] = pb.pb_EquipUseTimeDecode(v)
    end
    local __money_paper = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __money_paper ~= 0 then tb.money_paper = __money_paper end
    tb.ai_info = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(10))
    local __rank_match_score = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __rank_match_loot_box = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __rank_match_loot_box ~= 0 then tb.rank_match_loot_box = __rank_match_loot_box end
    tb.game_achievements = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.game_achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    local __result = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.mystical_skins = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.mystical_skins[k] = pb.pb_PropInfoDecode(v)
    end
    local __armor_reduce_in_fighting = decoder:geti32(16)
    if not PB_USE_DEFAULT_TABLE or __armor_reduce_in_fighting ~= 0 then tb.armor_reduce_in_fighting = __armor_reduce_in_fighting end
    local __total_mileage = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __total_mileage ~= 0 then tb.total_mileage = __total_mileage end
    local __sol_double_rank_multiple_value = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __sol_double_rank_multiple_value ~= 0 then tb.sol_double_rank_multiple_value = __sol_double_rank_multiple_value end
    local __assist_cnt = decoder:getu32(19)
    if not PB_USE_DEFAULT_TABLE or __assist_cnt ~= 0 then tb.assist_cnt = __assist_cnt end
    local __total_shoot = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_down = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_down ~= 0 then tb.total_shoot_down = __total_shoot_down end
    local __total_shoot_head_down = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __exactly_leave = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __exactly_leave ~= 0 then tb.exactly_leave = __exactly_leave end
    local __sys_team_id = decoder:getu64(25)
    if not PB_USE_DEFAULT_TABLE or __sys_team_id ~= 0 then tb.sys_team_id = __sys_team_id end
    local __safebox_skin_id = decoder:getu64(26)
    if not PB_USE_DEFAULT_TABLE or __safebox_skin_id ~= 0 then tb.safebox_skin_id = __safebox_skin_id end
    local __course_id = decoder:getu32(100)
    if not PB_USE_DEFAULT_TABLE or __course_id ~= 0 then tb.course_id = __course_id end
    local __is_ranked_match = decoder:getbool(101)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __has_trigger_gold_egg = decoder:getbool(102)
    if not PB_USE_DEFAULT_TABLE or __has_trigger_gold_egg ~= false then tb.has_trigger_gold_egg = __has_trigger_gold_egg end
    return tb
end

function pb.pb_DsPlayerGameStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.basic_info) then    pb.pb_DsPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.mode_id) then    encoder:addi32(6, tb.mode_id)    end
    if(tb.player_start) then    encoder:addi32(7, tb.player_start)    end
    if(tb.playing) then    pb.pb_DsPlayingPlayerStatusEncode(tb.playing, encoder:addsubmsg(5))    end
    if(tb.end_game) then    pb.pb_DsEndGamePlayerStatusEncode(tb.end_game, encoder:addsubmsg(3))    end
    if(tb.quit_game) then    pb.pb_DsQuitGamePlayerStatusEncode(tb.quit_game, encoder:addsubmsg(4))    end
    if(tb.equip_use_time) then
        for i=1,#(tb.equip_use_time) do
            pb.pb_EquipUseTimeEncode(tb.equip_use_time[i], encoder:addsubmsg(8))
        end
    end
    if(tb.money_paper) then    encoder:addi64(9, tb.money_paper)    end
    if(tb.ai_info) then    pb.pb_DsAIBasicInfoEncode(tb.ai_info, encoder:addsubmsg(10))    end
    if(tb.rank_match_score) then    encoder:addi64(11, tb.rank_match_score)    end
    if(tb.rank_match_loot_box) then    encoder:addi64(12, tb.rank_match_loot_box)    end
    if(tb.game_achievements) then
        for i=1,#(tb.game_achievements) do
            pb.pb_DsGameAchievementEncode(tb.game_achievements[i], encoder:addsubmsg(13))
        end
    end
    if(tb.result) then    encoder:addu32(14, tb.result)    end
    if(tb.mystical_skins) then
        for i=1,#(tb.mystical_skins) do
            pb.pb_PropInfoEncode(tb.mystical_skins[i], encoder:addsubmsg(15))
        end
    end
    if(tb.armor_reduce_in_fighting) then    encoder:addi32(16, tb.armor_reduce_in_fighting)    end
    if(tb.total_mileage) then    encoder:addi32(17, tb.total_mileage)    end
    if(tb.sol_double_rank_multiple_value) then    encoder:addu32(18, tb.sol_double_rank_multiple_value)    end
    if(tb.assist_cnt) then    encoder:addu32(19, tb.assist_cnt)    end
    if(tb.total_shoot) then    encoder:addu32(20, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu32(21, tb.total_shoot_hit)    end
    if(tb.total_shoot_down) then    encoder:addu32(22, tb.total_shoot_down)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(23, tb.total_shoot_head_down)    end
    if(tb.exactly_leave) then    encoder:addi32(24, tb.exactly_leave)    end
    if(tb.sys_team_id) then    encoder:addu64(25, tb.sys_team_id)    end
    if(tb.safebox_skin_id) then    encoder:addu64(26, tb.safebox_skin_id)    end
    if(tb.course_id) then    encoder:addu32(100, tb.course_id)    end
    if(tb.is_ranked_match) then    encoder:addbool(101, tb.is_ranked_match)    end
    if(tb.has_trigger_gold_egg) then    encoder:addbool(102, tb.has_trigger_gold_egg)    end
end

function pb.pb_DsPlayerKillInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerKillInfo) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __timestamp = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_DsPlayerPositionDecode(decoder:getsubmsg(3))
    tb.damage = pb.pb_DsDamageInfoDecode(decoder:getsubmsg(14))
    local __total_damage = decoder:getdouble(15)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __enemy_type = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __enemy_type ~= 0 then tb.enemy_type = __enemy_type end
    tb.player = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(10))
    tb.ai = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(11))
    tb.boss = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(12))
    local __carry_in_props_price = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __carry_in_props_price ~= 0 then tb.carry_in_props_price = __carry_in_props_price end
    local __carry_in_equip_price = decoder:geti64(17)
    if not PB_USE_DEFAULT_TABLE or __carry_in_equip_price ~= 0 then tb.carry_in_equip_price = __carry_in_equip_price end
    return tb
end

function pb.pb_DsPlayerKillInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.timestamp) then    encoder:addi32(2, tb.timestamp)    end
    if(tb.position) then    pb.pb_DsPlayerPositionEncode(tb.position, encoder:addsubmsg(3))    end
    if(tb.damage) then    pb.pb_DsDamageInfoEncode(tb.damage, encoder:addsubmsg(14))    end
    if(tb.total_damage) then    encoder:adddouble(15, tb.total_damage)    end
    if(tb.enemy_type) then    encoder:addi32(7, tb.enemy_type)    end
    if(tb.player) then    pb.pb_DsPlayerBasicInfoEncode(tb.player, encoder:addsubmsg(10))    end
    if(tb.ai) then    pb.pb_DsAIBasicInfoEncode(tb.ai, encoder:addsubmsg(11))    end
    if(tb.boss) then    pb.pb_DsAIBasicInfoEncode(tb.boss, encoder:addsubmsg(12))    end
    if(tb.carry_in_props_price) then    encoder:addi64(16, tb.carry_in_props_price)    end
    if(tb.carry_in_equip_price) then    encoder:addi64(17, tb.carry_in_equip_price)    end
end

function pb.pb_DsPlayerMatchStatNewDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerMatchStatNew) or {} 
    local __blood_loss = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __blood_loss ~= 0 then tb.blood_loss = __blood_loss end
    local __HP_healed = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __HP_healed ~= 0 then tb.HP_healed = __HP_healed end
    local __dehydration_times = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __dehydration_times ~= 0 then tb.dehydration_times = __dehydration_times end
    local __drink_consumed = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __drink_consumed ~= 0 then tb.drink_consumed = __drink_consumed end
    local __food_consumed = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __food_consumed ~= 0 then tb.food_consumed = __food_consumed end
    local __KM_traveled = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __KM_traveled ~= 0 then tb.KM_traveled = __KM_traveled end
    local __bodies_looted = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __bodies_looted ~= 0 then tb.bodies_looted = __bodies_looted end
    local __weapons_found = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __weapons_found ~= 0 then tb.weapons_found = __weapons_found end
    local __accessories_found = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __accessories_found ~= 0 then tb.accessories_found = __accessories_found end
    local __provisions_found = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __provisions_found ~= 0 then tb.provisions_found = __provisions_found end
    local __rescue_count = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __revive_count = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __revive_count ~= 0 then tb.revive_count = __revive_count end
    local __unlock_times = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __unlock_times ~= 0 then tb.unlock_times = __unlock_times end
    local __total_damage = decoder:getdouble(13)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __head_shot_count = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __head_shot_count ~= 0 then tb.head_shot_count = __head_shot_count end
    tb.combo_array = decoder:geti32ary(19)
    tb.area_array = decoder:geti32ary(20)
    tb.prop_array = decoder:getu64ary(21)
    local __total_loot = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __total_loot ~= 0 then tb.total_loot = __total_loot end
    local __total_battle = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __total_battle ~= 0 then tb.total_battle = __total_battle end
    local __total_sneak = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __total_sneak ~= 0 then tb.total_sneak = __total_sneak end
    local __first_loot = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __first_loot ~= 0 then tb.first_loot = __first_loot end
    tb.new_props_gid = decoder:getu64ary(26)
    local __resurgence_count = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    return tb
end

function pb.pb_DsPlayerMatchStatNewEncode(tb, encoder)
    if(tb.blood_loss) then    encoder:addi32(1, tb.blood_loss)    end
    if(tb.HP_healed) then    encoder:addi32(2, tb.HP_healed)    end
    if(tb.dehydration_times) then    encoder:addi32(3, tb.dehydration_times)    end
    if(tb.drink_consumed) then    encoder:addi32(4, tb.drink_consumed)    end
    if(tb.food_consumed) then    encoder:addi32(5, tb.food_consumed)    end
    if(tb.KM_traveled) then    encoder:addfloat(6, tb.KM_traveled)    end
    if(tb.bodies_looted) then    encoder:addi32(7, tb.bodies_looted)    end
    if(tb.weapons_found) then    encoder:addi32(8, tb.weapons_found)    end
    if(tb.accessories_found) then    encoder:addi32(9, tb.accessories_found)    end
    if(tb.provisions_found) then    encoder:addi32(10, tb.provisions_found)    end
    if(tb.rescue_count) then    encoder:addi32(11, tb.rescue_count)    end
    if(tb.revive_count) then    encoder:addi32(12, tb.revive_count)    end
    if(tb.unlock_times) then    encoder:addi32(17, tb.unlock_times)    end
    if(tb.total_damage) then    encoder:adddouble(13, tb.total_damage)    end
    if(tb.head_shot_count) then    encoder:addi32(18, tb.head_shot_count)    end
    if(tb.combo_array) then    encoder:addi32(19, tb.combo_array)    end
    if(tb.area_array) then    encoder:addi32(20, tb.area_array)    end
    if(tb.prop_array) then    encoder:addu64(21, tb.prop_array)    end
    if(tb.total_loot) then    encoder:addi32(22, tb.total_loot)    end
    if(tb.total_battle) then    encoder:addi32(23, tb.total_battle)    end
    if(tb.total_sneak) then    encoder:addi32(24, tb.total_sneak)    end
    if(tb.first_loot) then    encoder:addi32(25, tb.first_loot)    end
    if(tb.new_props_gid) then    encoder:addu64(26, tb.new_props_gid)    end
    if(tb.resurgence_count) then    encoder:addi32(27, tb.resurgence_count)    end
end

function pb.pb_DsPlayerPositionDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayerPosition) or {} 
    local __x = decoder:getdouble(1)
    if not PB_USE_DEFAULT_TABLE or __x ~= 0 then tb.x = __x end
    local __y = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __y ~= 0 then tb.y = __y end
    local __z = decoder:getdouble(3)
    if not PB_USE_DEFAULT_TABLE or __z ~= 0 then tb.z = __z end
    return tb
end

function pb.pb_DsPlayerPositionEncode(tb, encoder)
    if(tb.x) then    encoder:adddouble(1, tb.x)    end
    if(tb.y) then    encoder:adddouble(2, tb.y)    end
    if(tb.z) then    encoder:adddouble(3, tb.z)    end
end

function pb.pb_DsPlayingPlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsPlayingPlayerStatus) or {} 
    local __play_time = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.current_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.current_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(4))
    return tb
end

function pb.pb_DsPlayingPlayerStatusEncode(tb, encoder)
    if(tb.play_time) then    encoder:addi32(1, tb.play_time)    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.current_props) then
        for i=1,#(tb.current_props) do
            pb.pb_EquipPositionEncode(tb.current_props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(4))    end
end

function pb.pb_DsQuestDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsQuestData) or {} 
    tb.player_changed_quests = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.player_changed_quests[k] = pb.pb_PlayerQuestDataDecode(v)
    end
    return tb
end

function pb.pb_DsQuestDataEncode(tb, encoder)
    if(tb.player_changed_quests) then
        for i=1,#(tb.player_changed_quests) do
            pb.pb_PlayerQuestDataEncode(tb.player_changed_quests[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_DsQuitGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsQuitGamePlayerStatus) or {} 
    local __reason = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __play_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __has_main_weapon = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __has_main_weapon ~= false then tb.has_main_weapon = __has_main_weapon end
    local __main_weapon = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __main_weapon ~= 0 then tb.main_weapon = __main_weapon end
    tb.match_stat = pb.pb_DsPlayerMatchStatNewDecode(decoder:getsubmsg(19))
    local __individual_rank = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.final_props = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.final_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.kill_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.knock_down_array = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.knock_down_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.achievement_array[k] = pb.pb_DsAchievementInfoDecode(v)
    end
    tb.carry_out_health_list = {}
    for k,v in pairs(decoder:getsubmsgary(16)) do
        tb.carry_out_health_list[k] = pb.pb_EquipHealthDecode(v)
    end
    local __carry_out_props_price = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __carry_out_props_price ~= 0 then tb.carry_out_props_price = __carry_out_props_price end
    local __carry_in_props_price = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __carry_in_props_price ~= 0 then tb.carry_in_props_price = __carry_in_props_price end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(25))
    local __asset_increment_price = decoder:geti64(27)
    if not PB_USE_DEFAULT_TABLE or __asset_increment_price ~= 0 then tb.asset_increment_price = __asset_increment_price end
    local __rescue_count = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __resurgence_count = decoder:geti32(29)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    tb.shoot_down_info = pb.pb_DsPlayerKillInfoDecode(decoder:getsubmsg(30))
    local __carry_out_profit_price = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    local __cost_price = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    tb.shoot_down_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(33)) do
        tb.shoot_down_info_list[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    local __blue_print_special_id = decoder:getu64(34)
    if not PB_USE_DEFAULT_TABLE or __blue_print_special_id ~= 0 then tb.blue_print_special_id = __blue_print_special_id end
    local __blue_print_type = decoder:geti32(35)
    if not PB_USE_DEFAULT_TABLE or __blue_print_type ~= 0 then tb.blue_print_type = __blue_print_type end
    local __carry_in_equip_price = decoder:geti32(36)
    if not PB_USE_DEFAULT_TABLE or __carry_in_equip_price ~= 0 then tb.carry_in_equip_price = __carry_in_equip_price end
    local __carry_out_new_props_price = decoder:geti64(37)
    if not PB_USE_DEFAULT_TABLE or __carry_out_new_props_price ~= 0 then tb.carry_out_new_props_price = __carry_out_new_props_price end
    local __blue_print_price = decoder:geti64(38)
    if not PB_USE_DEFAULT_TABLE or __blue_print_price ~= 0 then tb.blue_print_price = __blue_print_price end
    local __carry_out_without_teammate_price = decoder:geti64(39)
    if not PB_USE_DEFAULT_TABLE or __carry_out_without_teammate_price ~= 0 then tb.carry_out_without_teammate_price = __carry_out_without_teammate_price end
    return tb
end

function pb.pb_DsQuitGamePlayerStatusEncode(tb, encoder)
    if(tb.reason) then    encoder:addi32(1, tb.reason)    end
    if(tb.play_time) then    encoder:addi32(4, tb.play_time)    end
    if(tb.has_main_weapon) then    encoder:addbool(20, tb.has_main_weapon)    end
    if(tb.main_weapon) then    encoder:addu64(5, tb.main_weapon)    end
    if(tb.match_stat) then    pb.pb_DsPlayerMatchStatNewEncode(tb.match_stat, encoder:addsubmsg(19))    end
    if(tb.individual_rank) then    encoder:addi32(9, tb.individual_rank)    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(12))
        end
    end
    if(tb.final_props) then
        for i=1,#(tb.final_props) do
            pb.pb_EquipPositionEncode(tb.final_props[i], encoder:addsubmsg(13))
        end
    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(14))
        end
    end
    if(tb.knock_down_array) then
        for i=1,#(tb.knock_down_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.knock_down_array[i], encoder:addsubmsg(21))
        end
    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_DsAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(15))
        end
    end
    if(tb.carry_out_health_list) then
        for i=1,#(tb.carry_out_health_list) do
            pb.pb_EquipHealthEncode(tb.carry_out_health_list[i], encoder:addsubmsg(16))
        end
    end
    if(tb.carry_out_props_price) then    encoder:addi32(17, tb.carry_out_props_price)    end
    if(tb.carry_in_props_price) then    encoder:addi32(18, tb.carry_in_props_price)    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(25))    end
    if(tb.asset_increment_price) then    encoder:addi64(27, tb.asset_increment_price)    end
    if(tb.rescue_count) then    encoder:addi32(28, tb.rescue_count)    end
    if(tb.resurgence_count) then    encoder:addi32(29, tb.resurgence_count)    end
    if(tb.shoot_down_info) then    pb.pb_DsPlayerKillInfoEncode(tb.shoot_down_info, encoder:addsubmsg(30))    end
    if(tb.carry_out_profit_price) then    encoder:addi32(31, tb.carry_out_profit_price)    end
    if(tb.cost_price) then    encoder:addi32(32, tb.cost_price)    end
    if(tb.shoot_down_info_list) then
        for i=1,#(tb.shoot_down_info_list) do
            pb.pb_DsPlayerKillInfoEncode(tb.shoot_down_info_list[i], encoder:addsubmsg(33))
        end
    end
    if(tb.blue_print_special_id) then    encoder:addu64(34, tb.blue_print_special_id)    end
    if(tb.blue_print_type) then    encoder:addi32(35, tb.blue_print_type)    end
    if(tb.carry_in_equip_price) then    encoder:addi32(36, tb.carry_in_equip_price)    end
    if(tb.carry_out_new_props_price) then    encoder:addi64(37, tb.carry_out_new_props_price)    end
    if(tb.blue_print_price) then    encoder:addi64(38, tb.blue_print_price)    end
    if(tb.carry_out_without_teammate_price) then    encoder:addi64(39, tb.carry_out_without_teammate_price)    end
end

function pb.pb_DsStatDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsStat) or {} 
    local __player_num = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __player_num ~= 0 then tb.player_num = __player_num end
    local __robot_num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __robot_num ~= 0 then tb.robot_num = __robot_num end
    local __ai_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __ai_num ~= 0 then tb.ai_num = __ai_num end
    return tb
end

function pb.pb_DsStatEncode(tb, encoder)
    if(tb.player_num) then    encoder:addi64(1, tb.player_num)    end
    if(tb.robot_num) then    encoder:addi64(2, tb.robot_num)    end
    if(tb.ai_num) then    encoder:addi64(3, tb.ai_num)    end
end

function pb.pb_DsTeamInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsTeamInfo) or {} 
    local __team_id = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __teammate_count = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __teammate_count ~= 0 then tb.teammate_count = __teammate_count end
    local __team_rank = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __team_rank ~= 0 then tb.team_rank = __team_rank end
    tb.team_achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.team_achievement_array[k] = pb.pb_DsAchievementInfoDecode(v)
    end
    local __team_num = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __team_num ~= 0 then tb.team_num = __team_num end
    return tb
end

function pb.pb_DsTeamInfoEncode(tb, encoder)
    if(tb.team_id) then    encoder:addi32(4, tb.team_id)    end
    if(tb.teammate_count) then    encoder:addi32(1, tb.teammate_count)    end
    if(tb.team_rank) then    encoder:addi32(2, tb.team_rank)    end
    if(tb.team_achievement_array) then
        for i=1,#(tb.team_achievement_array) do
            pb.pb_DsAchievementInfoEncode(tb.team_achievement_array[i], encoder:addsubmsg(3))
        end
    end
    if(tb.team_num) then    encoder:addi32(5, tb.team_num)    end
end

function pb.pb_EquipHealthDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipHealth) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __gid ~= 0 then tb.gid = __gid end
    local __health = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __health ~= 0 then tb.health = __health end
    local __origin_health = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __origin_health ~= 0 then tb.origin_health = __origin_health end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(5))
    return tb
end

function pb.pb_EquipHealthEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.gid) then    encoder:addu64(2, tb.gid)    end
    if(tb.health) then    encoder:addi32(3, tb.health)    end
    if(tb.origin_health) then    encoder:addi32(4, tb.origin_health)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(5))    end
end

function pb.pb_EquipPositionDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipPosition) or {} 
    local __position = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __position ~= 0 then tb.position = __position end
    tb.load_props = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.load_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __capacity = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __capacity ~= 0 then tb.capacity = __capacity end
    tb.grid_space = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.grid_space[k] = pb.pb_GridSizeDecode(v)
    end
    local __src_prop_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __src_prop_id ~= 0 then tb.src_prop_id = __src_prop_id end
    return tb
end

function pb.pb_EquipPositionEncode(tb, encoder)
    if(tb.position) then    encoder:addu32(1, tb.position)    end
    if(tb.load_props) then
        for i=1,#(tb.load_props) do
            pb.pb_PropInfoEncode(tb.load_props[i], encoder:addsubmsg(4))
        end
    end
    if(tb.capacity) then    encoder:addi32(5, tb.capacity)    end
    if(tb.grid_space) then
        for i=1,#(tb.grid_space) do
            pb.pb_GridSizeEncode(tb.grid_space[i], encoder:addsubmsg(6))
        end
    end
    if(tb.src_prop_id) then    encoder:addu64(7, tb.src_prop_id)    end
end

function pb.pb_EquipUseTimeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipUseTime) or {} 
    local __weapon_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    local __time = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    local __expert_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    return tb
end

function pb.pb_EquipUseTimeEncode(tb, encoder)
    if(tb.weapon_id) then    encoder:addu64(1, tb.weapon_id)    end
    if(tb.time) then    encoder:addu32(3, tb.time)    end
    if(tb.expert_id) then    encoder:addu32(4, tb.expert_id)    end
end

function pb.pb_ExpertSkillDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ExpertSkill) or {} 
    local __expert_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    local __active_skill_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __active_skill_id ~= 0 then tb.active_skill_id = __active_skill_id end
    local __ultimate_skill_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __ultimate_skill_id ~= 0 then tb.ultimate_skill_id = __ultimate_skill_id end
    local __support_skill_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __support_skill_id ~= 0 then tb.support_skill_id = __support_skill_id end
    local __passive_skill_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __passive_skill_id ~= 0 then tb.passive_skill_id = __passive_skill_id end
    local __common_passive_skill_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __common_passive_skill_id ~= 0 then tb.common_passive_skill_id = __common_passive_skill_id end
    local __support_skill_id2 = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __support_skill_id2 ~= 0 then tb.support_skill_id2 = __support_skill_id2 end
    return tb
end

function pb.pb_ExpertSkillEncode(tb, encoder)
    if(tb.expert_id) then    encoder:addu32(3, tb.expert_id)    end
    if(tb.active_skill_id) then    encoder:addu32(1, tb.active_skill_id)    end
    if(tb.ultimate_skill_id) then    encoder:addu32(4, tb.ultimate_skill_id)    end
    if(tb.support_skill_id) then    encoder:addu32(5, tb.support_skill_id)    end
    if(tb.passive_skill_id) then    encoder:addu32(2, tb.passive_skill_id)    end
    if(tb.common_passive_skill_id) then    encoder:addu32(6, tb.common_passive_skill_id)    end
    if(tb.support_skill_id2) then    encoder:addu32(7, tb.support_skill_id2)    end
end

function pb.pb_GeneralSkillDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GeneralSkill) or {} 
    local __skill_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __skill_id ~= 0 then tb.skill_id = __skill_id end
    local __skill_lvl = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __skill_lvl ~= 0 then tb.skill_lvl = __skill_lvl end
    return tb
end

function pb.pb_GeneralSkillEncode(tb, encoder)
    if(tb.skill_id) then    encoder:addu32(1, tb.skill_id)    end
    if(tb.skill_lvl) then    encoder:addu32(2, tb.skill_lvl)    end
end

function pb.pb_GridSizeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GridSize) or {} 
    local __id = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __length = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __length ~= 0 then tb.length = __length end
    local __width = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __width ~= 0 then tb.width = __width end
    local __base_cnt = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __base_cnt ~= 0 then tb.base_cnt = __base_cnt end
    tb.total_locked = decoder:geti32ary(8)
    tb.unlocked = decoder:geti32ary(9)
    local __is_map_unlocked = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __is_map_unlocked ~= false then tb.is_map_unlocked = __is_map_unlocked end
    return tb
end

function pb.pb_GridSizeEncode(tb, encoder)
    if(tb.id) then    encoder:addi32(3, tb.id)    end
    if(tb.length) then    encoder:addi32(1, tb.length)    end
    if(tb.width) then    encoder:addi32(2, tb.width)    end
    if(tb.base_cnt) then    encoder:addi32(10, tb.base_cnt)    end
    if(tb.total_locked) then    encoder:addi32(8, tb.total_locked)    end
    if(tb.unlocked) then    encoder:addi32(9, tb.unlocked)    end
    if(tb.is_map_unlocked) then    encoder:addbool(11, tb.is_map_unlocked)    end
end

function pb.pb_HalfJoinConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HalfJoinConfig) or {} 
    local __enable_half_join = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __enable_half_join ~= false then tb.enable_half_join = __enable_half_join end
    local __forbid_join_time = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __forbid_join_time ~= 0 then tb.forbid_join_time = __forbid_join_time end
    local __forbid_join_kills = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __forbid_join_kills ~= 0 then tb.forbid_join_kills = __forbid_join_kills end
    local __forbid_join_round = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __forbid_join_round ~= 0 then tb.forbid_join_round = __forbid_join_round end
    local __forbid_join_score_diff = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __forbid_join_score_diff ~= 0 then tb.forbid_join_score_diff = __forbid_join_score_diff end
    local __half_join_on_game_start = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __half_join_on_game_start ~= false then tb.half_join_on_game_start = __half_join_on_game_start end
    local __fast_half_join_threshold = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __fast_half_join_threshold ~= 0 then tb.fast_half_join_threshold = __fast_half_join_threshold end
    local __enable_bot_half_join = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __enable_bot_half_join ~= false then tb.enable_bot_half_join = __enable_bot_half_join end
    local __bot_wait_gap = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __bot_wait_gap ~= 0 then tb.bot_wait_gap = __bot_wait_gap end
    local __specify_camp = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __specify_camp ~= 0 then tb.specify_camp = __specify_camp end
    local __half_join_intervel = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __half_join_intervel ~= 0 then tb.half_join_intervel = __half_join_intervel end
    local __half_join_time = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __half_join_time ~= 0 then tb.half_join_time = __half_join_time end
    return tb
end

function pb.pb_HalfJoinConfigEncode(tb, encoder)
    if(tb.enable_half_join) then    encoder:addbool(1, tb.enable_half_join)    end
    if(tb.forbid_join_time) then    encoder:addu32(2, tb.forbid_join_time)    end
    if(tb.forbid_join_kills) then    encoder:addu32(3, tb.forbid_join_kills)    end
    if(tb.forbid_join_round) then    encoder:addu32(4, tb.forbid_join_round)    end
    if(tb.forbid_join_score_diff) then    encoder:addu32(5, tb.forbid_join_score_diff)    end
    if(tb.half_join_on_game_start) then    encoder:addbool(6, tb.half_join_on_game_start)    end
    if(tb.fast_half_join_threshold) then    encoder:addi64(7, tb.fast_half_join_threshold)    end
    if(tb.enable_bot_half_join) then    encoder:addbool(8, tb.enable_bot_half_join)    end
    if(tb.bot_wait_gap) then    encoder:addi64(9, tb.bot_wait_gap)    end
    if(tb.specify_camp) then    encoder:addi64(10, tb.specify_camp)    end
    if(tb.half_join_intervel) then    encoder:addi64(11, tb.half_join_intervel)    end
    if(tb.half_join_time) then    encoder:addi64(12, tb.half_join_time)    end
end

function pb.pb_HeroDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Hero) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __is_unlock = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    tb.equipped_ability = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.equipped_ability[k] = pb.pb_CombatRoleAbilityDecode(v)
    end
    local __is_equipped = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_equipped ~= false then tb.is_equipped = __is_equipped end
    local __armed_force_id = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __armed_force_id ~= 0 then tb.armed_force_id = __armed_force_id end
    return tb
end

function pb.pb_HeroEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.is_unlock) then    encoder:addbool(5, tb.is_unlock)    end
    if(tb.equipped_ability) then
        for i=1,#(tb.equipped_ability) do
            pb.pb_CombatRoleAbilityEncode(tb.equipped_ability[i], encoder:addsubmsg(6))
        end
    end
    if(tb.is_equipped) then    encoder:addbool(7, tb.is_equipped)    end
    if(tb.armed_force_id) then    encoder:addu32(8, tb.armed_force_id)    end
end

function pb.pb_HeroAccessoryItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroAccessoryItem) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __slot = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __slot ~= 0 then tb.slot = __slot end
    local __unlock_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    return tb
end

function pb.pb_HeroAccessoryItemEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.slot) then    encoder:addi64(2, tb.slot)    end
    if(tb.unlock_time) then    encoder:addi64(3, tb.unlock_time)    end
end

function pb.pb_HeroAttrDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroAttr) or {} 
    local __base_load_level = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __base_load_level ~= 0 then tb.base_load_level = __base_load_level end
    local __upgrade_level = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __upgrade_level ~= 0 then tb.upgrade_level = __upgrade_level end
    tb.fashion = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.fashion[k] = pb.pb_HeroFashionDecode(v)
    end
    return tb
end

function pb.pb_HeroAttrEncode(tb, encoder)
    if(tb.base_load_level) then    encoder:addi32(1, tb.base_load_level)    end
    if(tb.upgrade_level) then    encoder:addi32(2, tb.upgrade_level)    end
    if(tb.fashion) then
        for i=1,#(tb.fashion) do
            pb.pb_HeroFashionEncode(tb.fashion[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_HeroFashionDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroFashion) or {} 
    local __slot = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __slot ~= 0 then tb.slot = __slot end
    local __id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    return tb
end

function pb.pb_HeroFashionEncode(tb, encoder)
    if(tb.slot) then    encoder:addu32(1, tb.slot)    end
    if(tb.id) then    encoder:addu64(2, tb.id)    end
end

function pb.pb_MatchModeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchModeInfo) or {} 
    local __game_mode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __game_rule = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __game_rule ~= 0 then tb.game_rule = __game_rule end
    local __sub_mode = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __sub_mode ~= 0 then tb.sub_mode = __sub_mode end
    local __team_mode = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __team_mode ~= 0 then tb.team_mode = __team_mode end
    local __raid_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __raid_id ~= 0 then tb.raid_id = __raid_id end
    local __map_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __match_mode_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    local __scheme_pool_id = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __scheme_pool_id ~= 0 then tb.scheme_pool_id = __scheme_pool_id end
    return tb
end

function pb.pb_MatchModeInfoEncode(tb, encoder)
    if(tb.game_mode) then    encoder:addu32(1, tb.game_mode)    end
    if(tb.game_rule) then    encoder:addu32(2, tb.game_rule)    end
    if(tb.sub_mode) then    encoder:addu32(3, tb.sub_mode)    end
    if(tb.team_mode) then    encoder:addu32(4, tb.team_mode)    end
    if(tb.raid_id) then    encoder:addu32(5, tb.raid_id)    end
    if(tb.map_id) then    encoder:addu32(6, tb.map_id)    end
    if(tb.match_mode_id) then    encoder:addu64(7, tb.match_mode_id)    end
    if(tb.scheme_pool_id) then    encoder:addi64(8, tb.scheme_pool_id)    end
end

function pb.pb_TdmTeamTagInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TdmTeamTagInfo) or {} 
    local __tdm_lvl1_tag = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __tdm_lvl1_tag ~= 0 then tb.tdm_lvl1_tag = __tdm_lvl1_tag end
    tb.tdm_language_type = decoder:getstrary(2)
    local __tdm_play_type = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __tdm_play_type ~= 0 then tb.tdm_play_type = __tdm_play_type end
    tb.language_type = decoder:getu32ary(4)
    return tb
end

function pb.pb_TdmTeamTagInfoEncode(tb, encoder)
    if(tb.tdm_lvl1_tag) then    encoder:addu32(1, tb.tdm_lvl1_tag)    end
    if(tb.tdm_language_type) then    encoder:addstr(2, tb.tdm_language_type)    end
    if(tb.tdm_play_type) then    encoder:addu32(3, tb.tdm_play_type)    end
    if(tb.language_type) then    encoder:addu32(4, tb.language_type)    end
end

function pb.pb_TDMDsDetailDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMDsDetail) or {} 
    local __scheme_id = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __scheme_id ~= 0 then tb.scheme_id = __scheme_id end
    local __is_finish_tdm_scheme = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_finish_tdm_scheme ~= false then tb.is_finish_tdm_scheme = __is_finish_tdm_scheme end
    local __normal_down_shift = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __normal_down_shift ~= 0 then tb.normal_down_shift = __normal_down_shift end
    local __tournament_down_shift = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __tournament_down_shift ~= 0 then tb.tournament_down_shift = __tournament_down_shift end
    local __dynamic_skill_level = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __dynamic_skill_level ~= 0 then tb.dynamic_skill_level = __dynamic_skill_level end
    local __real_hidden_score = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __real_hidden_score ~= 0 then tb.real_hidden_score = __real_hidden_score end
    local __match_wait_time = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __match_wait_time ~= 0 then tb.match_wait_time = __match_wait_time end
    local __match_bucket_ori = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __match_bucket_ori ~= 0 then tb.match_bucket_ori = __match_bucket_ori end
    local __match_bucket_lower = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __match_bucket_lower ~= 0 then tb.match_bucket_lower = __match_bucket_lower end
    tb.tdm_tag = pb.pb_TdmTeamTagInfoDecode(decoder:getsubmsg(15))
    local __ladder_level = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __ladder_level ~= 0 then tb.ladder_level = __ladder_level end
    local __ladder_score = decoder:geti64(32)
    if not PB_USE_DEFAULT_TABLE or __ladder_score ~= 0 then tb.ladder_score = __ladder_score end
    local __normal_warm_point = decoder:geti64(61)
    if not PB_USE_DEFAULT_TABLE or __normal_warm_point ~= 0 then tb.normal_warm_point = __normal_warm_point end
    local __rank_warm_point = decoder:geti64(62)
    if not PB_USE_DEFAULT_TABLE or __rank_warm_point ~= 0 then tb.rank_warm_point = __rank_warm_point end
    local __ailab_bucket_diff = decoder:geti64(63)
    if not PB_USE_DEFAULT_TABLE or __ailab_bucket_diff ~= 0 then tb.ailab_bucket_diff = __ailab_bucket_diff end
    return tb
end

function pb.pb_TDMDsDetailEncode(tb, encoder)
    if(tb.scheme_id) then    encoder:addi64(6, tb.scheme_id)    end
    if(tb.is_finish_tdm_scheme) then    encoder:addbool(7, tb.is_finish_tdm_scheme)    end
    if(tb.normal_down_shift) then    encoder:addi64(8, tb.normal_down_shift)    end
    if(tb.tournament_down_shift) then    encoder:addi64(9, tb.tournament_down_shift)    end
    if(tb.dynamic_skill_level) then    encoder:addi64(10, tb.dynamic_skill_level)    end
    if(tb.real_hidden_score) then    encoder:addi64(11, tb.real_hidden_score)    end
    if(tb.match_wait_time) then    encoder:addi64(12, tb.match_wait_time)    end
    if(tb.match_bucket_ori) then    encoder:addi64(13, tb.match_bucket_ori)    end
    if(tb.match_bucket_lower) then    encoder:addi64(14, tb.match_bucket_lower)    end
    if(tb.tdm_tag) then    pb.pb_TdmTeamTagInfoEncode(tb.tdm_tag, encoder:addsubmsg(15))    end
    if(tb.ladder_level) then    encoder:addi64(31, tb.ladder_level)    end
    if(tb.ladder_score) then    encoder:addi64(32, tb.ladder_score)    end
    if(tb.normal_warm_point) then    encoder:addi64(61, tb.normal_warm_point)    end
    if(tb.rank_warm_point) then    encoder:addi64(62, tb.rank_warm_point)    end
    if(tb.ailab_bucket_diff) then    encoder:addi64(63, tb.ailab_bucket_diff)    end
end

function pb.pb_MatchRoomTeamMemberInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchRoomTeamMemberInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __team_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __player_idx = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __player_idx ~= 0 then tb.player_idx = __player_idx end
    local __camp = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __camp ~= 0 then tb.camp = __camp end
    local __nick_name = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __nick_name ~= "" then tb.nick_name = __nick_name end
    local __plat_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __is_robot = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_robot ~= false then tb.is_robot = __is_robot end
    local __warm_ab_type = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __warm_ab_type ~= 0 then tb.warm_ab_type = __warm_ab_type end
    local __team_name = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __team_name ~= "" then tb.team_name = __team_name end
    local __is_team_leader = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_team_leader ~= false then tb.is_team_leader = __is_team_leader end
    local __first_idc = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __first_idc ~= "" then tb.first_idc = __first_idc end
    local __first_idc_rtt = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __first_idc_rtt ~= 0 then tb.first_idc_rtt = __first_idc_rtt end
    local __systeam_team_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __systeam_team_id ~= 0 then tb.systeam_team_id = __systeam_team_id end
    return tb
end

function pb.pb_MatchRoomTeamMemberInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.team_id) then    encoder:addu32(2, tb.team_id)    end
    if(tb.player_idx) then    encoder:addu32(3, tb.player_idx)    end
    if(tb.camp) then    encoder:addi64(4, tb.camp)    end
    if(tb.nick_name) then    encoder:addstr(5, tb.nick_name)    end
    if(tb.plat_id) then    encoder:addu32(6, tb.plat_id)    end
    if(tb.is_robot) then    encoder:addbool(7, tb.is_robot)    end
    if(tb.warm_ab_type) then    encoder:addu32(8, tb.warm_ab_type)    end
    if(tb.team_name) then    encoder:addstr(9, tb.team_name)    end
    if(tb.is_team_leader) then    encoder:addbool(10, tb.is_team_leader)    end
    if(tb.first_idc) then    encoder:addstr(11, tb.first_idc)    end
    if(tb.first_idc_rtt) then    encoder:addi32(12, tb.first_idc_rtt)    end
    if(tb.systeam_team_id) then    encoder:addu64(13, tb.systeam_team_id)    end
end

function pb.pb_MatchSOLDropCounterDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchSOLDropCounter) or {} 
    local __drop_logic_id = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __drop_logic_id ~= 0 then tb.drop_logic_id = __drop_logic_id end
    local __drop_key = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __drop_key ~= "" then tb.drop_key = __drop_key end
    local __count = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __count ~= 0 then tb.count = __count end
    local __today_trigger_cnt = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __today_trigger_cnt ~= 0 then tb.today_trigger_cnt = __today_trigger_cnt end
    local __start_of_today = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __start_of_today ~= 0 then tb.start_of_today = __start_of_today end
    return tb
end

function pb.pb_MatchSOLDropCounterEncode(tb, encoder)
    if(tb.drop_logic_id) then    encoder:addi64(1, tb.drop_logic_id)    end
    if(tb.drop_key) then    encoder:addstr(2, tb.drop_key)    end
    if(tb.count) then    encoder:addi64(3, tb.count)    end
    if(tb.today_trigger_cnt) then    encoder:addi64(4, tb.today_trigger_cnt)    end
    if(tb.start_of_today) then    encoder:addi64(5, tb.start_of_today)    end
end

function pb.pb_MysticalSkinInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MysticalSkinInfo) or {} 
    local __rarity = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rarity ~= 0 then tb.rarity = __rarity end
    local __color = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __color ~= 0 then tb.color = __color end
    local __material = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __material ~= 0 then tb.material = __material end
    local __wear = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __wear ~= 0 then tb.wear = __wear end
    local __unique_no = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __unique_no ~= 0 then tb.unique_no = __unique_no end
    local __kill_cnter = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __kill_cnter ~= 0 then tb.kill_cnter = __kill_cnter end
    local __custom_name = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __custom_name ~= "" then tb.custom_name = __custom_name end
    tb.appearance = pb.pb_MysticalAppearanceInfoDecode(decoder:getsubmsg(8))
    local __market_buy_time = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __market_buy_time ~= 0 then tb.market_buy_time = __market_buy_time end
    local __source_type = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __source_type ~= 0 then tb.source_type = __source_type end
    return tb
end

function pb.pb_MysticalSkinInfoEncode(tb, encoder)
    if(tb.rarity) then    encoder:addu32(1, tb.rarity)    end
    if(tb.color) then    encoder:addu64(2, tb.color)    end
    if(tb.material) then    encoder:addu64(3, tb.material)    end
    if(tb.wear) then    encoder:addu64(4, tb.wear)    end
    if(tb.unique_no) then    encoder:addu64(5, tb.unique_no)    end
    if(tb.kill_cnter) then    encoder:addu64(6, tb.kill_cnter)    end
    if(tb.custom_name) then    encoder:addstr(7, tb.custom_name)    end
    if(tb.appearance) then    pb.pb_MysticalAppearanceInfoEncode(tb.appearance, encoder:addsubmsg(8))    end
    if(tb.market_buy_time) then    encoder:addi64(9, tb.market_buy_time)    end
    if(tb.source_type) then    encoder:addu32(10, tb.source_type)    end
end

function pb.pb_MysticalAppearanceInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MysticalAppearanceInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __seed = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __seed ~= 0 then tb.seed = __seed end
    return tb
end

function pb.pb_MysticalAppearanceInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.seed) then    encoder:addu64(2, tb.seed)    end
end

function pb.pb_MysticalPendantInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MysticalPendantInfo) or {} 
    local __rarity = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rarity ~= 0 then tb.rarity = __rarity end
    local __wear = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __wear ~= 0 then tb.wear = __wear end
    local __unique_no = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __unique_no ~= 0 then tb.unique_no = __unique_no end
    local __custom_name = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __custom_name ~= "" then tb.custom_name = __custom_name end
    tb.appearance = pb.pb_MysticalAppearanceInfoDecode(decoder:getsubmsg(5))
    local __market_buy_time = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __market_buy_time ~= 0 then tb.market_buy_time = __market_buy_time end
    local __source_type = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __source_type ~= 0 then tb.source_type = __source_type end
    local __suit_active = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __suit_active ~= false then tb.suit_active = __suit_active end
    tb.show = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.show[k] = pb.pb_MysticalPendantShowInfoDecode(v)
    end
    return tb
end

function pb.pb_MysticalPendantInfoEncode(tb, encoder)
    if(tb.rarity) then    encoder:addu32(1, tb.rarity)    end
    if(tb.wear) then    encoder:addu64(2, tb.wear)    end
    if(tb.unique_no) then    encoder:addu64(3, tb.unique_no)    end
    if(tb.custom_name) then    encoder:addstr(4, tb.custom_name)    end
    if(tb.appearance) then    pb.pb_MysticalAppearanceInfoEncode(tb.appearance, encoder:addsubmsg(5))    end
    if(tb.market_buy_time) then    encoder:addi64(6, tb.market_buy_time)    end
    if(tb.source_type) then    encoder:addu32(7, tb.source_type)    end
    if(tb.suit_active) then    encoder:addbool(8, tb.suit_active)    end
    if(tb.show) then
        for i=1,#(tb.show) do
            pb.pb_MysticalPendantShowInfoEncode(tb.show[i], encoder:addsubmsg(9))
        end
    end
end

function pb.pb_MysticalPendantShowInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MysticalPendantShowInfo) or {} 
    local __show_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __show_type ~= 0 then tb.show_type = __show_type end
    local __rarity = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __rarity ~= 0 then tb.rarity = __rarity end
    tb.appearance = pb.pb_MysticalAppearanceInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_MysticalPendantShowInfoEncode(tb, encoder)
    if(tb.show_type) then    encoder:addu32(1, tb.show_type)    end
    if(tb.rarity) then    encoder:addu32(2, tb.rarity)    end
    if(tb.appearance) then    pb.pb_MysticalAppearanceInfoEncode(tb.appearance, encoder:addsubmsg(3))    end
end

function pb.pb_MysticalPendantSuitInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MysticalPendantSuitInfo) or {} 
    local __suit_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __suit_id ~= 0 then tb.suit_id = __suit_id end
    tb.pendant_map = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.pendant_map[k] = pb.pb_MysticalPendantSuitInfo_PendantMapEntryDecode(v)
    end
    local __gathered = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __gathered ~= false then tb.gathered = __gathered end
    local __actived = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __actived ~= false then tb.actived = __actived end
    local __season_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __season_id ~= 0 then tb.season_id = __season_id end
    local __red_point_exist = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __red_point_exist ~= false then tb.red_point_exist = __red_point_exist end
    return tb
end

function pb.pb_MysticalPendantSuitInfoEncode(tb, encoder)
    if(tb.suit_id) then    encoder:addu64(1, tb.suit_id)    end
    if(tb.pendant_map) then
        for i=1,#(tb.pendant_map) do
            pb.pb_MysticalPendantSuitInfo_PendantMapEntryEncode(tb.pendant_map[i], encoder:addsubmsg(2))
        end
    end
    if(tb.gathered) then    encoder:addbool(3, tb.gathered)    end
    if(tb.actived) then    encoder:addbool(4, tb.actived)    end
    if(tb.season_id) then    encoder:addu64(5, tb.season_id)    end
    if(tb.red_point_exist) then    encoder:addbool(6, tb.red_point_exist)    end
end

function pb.pb_NumeralActivityDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_NumeralActivityData) or {} 
    tb.objectives = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.objectives[k] = pb.pb_NumeralActivityObjectiveDecode(v)
    end
    tb.contracts = decoder:getu64ary(2)
    tb.boxes = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.boxes[k] = pb.pb_NumeralActivityPasswordBoxDecode(v)
    end
    tb.unlocked_boxes = decoder:getu64ary(4)
    tb.star_fire_objectives = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.star_fire_objectives[k] = pb.pb_NumeralActivityObjectiveDecode(v)
    end
    tb.quest_objectives = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.quest_objectives[k] = pb.pb_NumeralActivityObjectiveDecode(v)
    end
    local __orgin_commander_score = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __orgin_commander_score ~= 0 then tb.orgin_commander_score = __orgin_commander_score end
    return tb
end

function pb.pb_NumeralActivityDataEncode(tb, encoder)
    if(tb.objectives) then
        for i=1,#(tb.objectives) do
            pb.pb_NumeralActivityObjectiveEncode(tb.objectives[i], encoder:addsubmsg(1))
        end
    end
    if(tb.contracts) then    encoder:addu64(2, tb.contracts)    end
    if(tb.boxes) then
        for i=1,#(tb.boxes) do
            pb.pb_NumeralActivityPasswordBoxEncode(tb.boxes[i], encoder:addsubmsg(3))
        end
    end
    if(tb.unlocked_boxes) then    encoder:addu64(4, tb.unlocked_boxes)    end
    if(tb.star_fire_objectives) then
        for i=1,#(tb.star_fire_objectives) do
            pb.pb_NumeralActivityObjectiveEncode(tb.star_fire_objectives[i], encoder:addsubmsg(5))
        end
    end
    if(tb.quest_objectives) then
        for i=1,#(tb.quest_objectives) do
            pb.pb_NumeralActivityObjectiveEncode(tb.quest_objectives[i], encoder:addsubmsg(6))
        end
    end
    if(tb.orgin_commander_score) then    encoder:addi64(7, tb.orgin_commander_score)    end
end

function pb.pb_NumeralActivityPasswordBoxDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_NumeralActivityPasswordBox) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __password = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __password ~= "" then tb.password = __password end
    tb.unlocked_digits = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.unlocked_digits[k] = pb.pb_UnlockedDigitDecode(v)
    end
    return tb
end

function pb.pb_NumeralActivityPasswordBoxEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.password) then    encoder:addstr(2, tb.password)    end
    if(tb.unlocked_digits) then
        for i=1,#(tb.unlocked_digits) do
            pb.pb_UnlockedDigitEncode(tb.unlocked_digits[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_UnlockedDigitDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_UnlockedDigit) or {} 
    local __index = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __index ~= 0 then tb.index = __index end
    local __digit = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __digit ~= 0 then tb.digit = __digit end
    return tb
end

function pb.pb_UnlockedDigitEncode(tb, encoder)
    if(tb.index) then    encoder:addi32(1, tb.index)    end
    if(tb.digit) then    encoder:addi32(2, tb.digit)    end
end

function pb.pb_NumeralActivityObjectiveDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_NumeralActivityObjective) or {} 
    local __activity_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __activity_id ~= 0 then tb.activity_id = __activity_id end
    local __task_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __completed = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __completed ~= false then tb.completed = __completed end
    local __current_value = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __current_value ~= 0 then tb.current_value = __current_value end
    local __source = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    local __type = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __delta = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __delta ~= 0 then tb.delta = __delta end
    return tb
end

function pb.pb_NumeralActivityObjectiveEncode(tb, encoder)
    if(tb.activity_id) then    encoder:addu64(1, tb.activity_id)    end
    if(tb.task_id) then    encoder:addu64(2, tb.task_id)    end
    if(tb.id) then    encoder:addu64(3, tb.id)    end
    if(tb.completed) then    encoder:addbool(4, tb.completed)    end
    if(tb.current_value) then    encoder:addi64(5, tb.current_value)    end
    if(tb.source) then    encoder:addu32(6, tb.source)    end
    if(tb.type) then    encoder:addu32(7, tb.type)    end
    if(tb.delta) then    encoder:addi64(8, tb.delta)    end
end

function pb.pb_ODTdmCampDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ODTdmCamp) or {} 
    local __offense_stronghold = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __offense_stronghold ~= 0 then tb.offense_stronghold = __offense_stronghold end
    local __offense_total_stronghold = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __offense_total_stronghold ~= 0 then tb.offense_total_stronghold = __offense_total_stronghold end
    local __offense_reenforce = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __offense_reenforce ~= 0 then tb.offense_reenforce = __offense_reenforce end
    local __defense_stronghold = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __defense_stronghold ~= 0 then tb.defense_stronghold = __defense_stronghold end
    local __defense_total_stronghold = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __defense_total_stronghold ~= 0 then tb.defense_total_stronghold = __defense_total_stronghold end
    local __defense_kill_enemy = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __defense_kill_enemy ~= 0 then tb.defense_kill_enemy = __defense_kill_enemy end
    return tb
end

function pb.pb_ODTdmCampEncode(tb, encoder)
    if(tb.offense_stronghold) then    encoder:addi32(1, tb.offense_stronghold)    end
    if(tb.offense_total_stronghold) then    encoder:addi32(2, tb.offense_total_stronghold)    end
    if(tb.offense_reenforce) then    encoder:addi32(3, tb.offense_reenforce)    end
    if(tb.defense_stronghold) then    encoder:addi32(4, tb.defense_stronghold)    end
    if(tb.defense_total_stronghold) then    encoder:addi32(5, tb.defense_total_stronghold)    end
    if(tb.defense_kill_enemy) then    encoder:addi32(6, tb.defense_kill_enemy)    end
end

function pb.pb_OdTdmPlayerDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_OdTdmPlayer) or {} 
    local __military_rank = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __military_rank ~= 0 then tb.military_rank = __military_rank end
    local __rescue_num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __rescue_num ~= 0 then tb.rescue_num = __rescue_num end
    return tb
end

function pb.pb_OdTdmPlayerEncode(tb, encoder)
    if(tb.military_rank) then    encoder:addi32(1, tb.military_rank)    end
    if(tb.rescue_num) then    encoder:addi32(2, tb.rescue_num)    end
end

function pb.pb_PerkInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PerkInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __gid ~= 0 then tb.gid = __gid end
    local __health = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __health ~= 0 then tb.health = __health end
    local __health_max = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __health_max ~= 0 then tb.health_max = __health_max end
    tb.loc = pb.pb_PropLocationDecode(decoder:getsubmsg(6))
    local __length = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __length ~= 0 then tb.length = __length end
    local __width = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __width ~= 0 then tb.width = __width end
    return tb
end

function pb.pb_PerkInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.gid) then    encoder:addu64(2, tb.gid)    end
    if(tb.health) then    encoder:addi32(3, tb.health)    end
    if(tb.health_max) then    encoder:addi32(4, tb.health_max)    end
    if(tb.loc) then    pb.pb_PropLocationEncode(tb.loc, encoder:addsubmsg(6))    end
    if(tb.length) then    encoder:addi32(7, tb.length)    end
    if(tb.width) then    encoder:addi32(8, tb.width)    end
end

function pb.pb_PlayerObjectiveExtraDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerObjectiveExtraData) or {} 
    local __check_sum = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __check_sum ~= 0 then tb.check_sum = __check_sum end
    local __data1 = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __data1 ~= 0 then tb.data1 = __data1 end
    local __data2 = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __data2 ~= 0 then tb.data2 = __data2 end
    local __data3 = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __data3 ~= 0 then tb.data3 = __data3 end
    local __data4 = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __data4 ~= 0 then tb.data4 = __data4 end
    return tb
end

function pb.pb_PlayerObjectiveExtraDataEncode(tb, encoder)
    if(tb.check_sum) then    encoder:addi32(1, tb.check_sum)    end
    if(tb.data1) then    encoder:addu64(2, tb.data1)    end
    if(tb.data2) then    encoder:addu64(3, tb.data2)    end
    if(tb.data3) then    encoder:addu64(4, tb.data3)    end
    if(tb.data4) then    encoder:addu64(5, tb.data4)    end
end

function pb.pb_PlayerQuestDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerQuestData) or {} 
    local __quest_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __quest_id ~= 0 then tb.quest_id = __quest_id end
    local __quest_state = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __quest_state ~= 0 then tb.quest_state = __quest_state end
    local __quest_accept_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __quest_accept_time ~= 0 then tb.quest_accept_time = __quest_accept_time end
    tb.quest_var = decoder:geti64ary(5)
    tb.quest_objectives = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.quest_objectives[k] = pb.pb_PlayerQuestObjectiveDataDecode(v)
    end
    tb.marked_objective_id = decoder:getu64ary(7)
    local __expire_time = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __expire_time ~= 0 then tb.expire_time = __expire_time end
    tb.enter_map_info = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.enter_map_info[k] = pb.pb_QuestVarEntryDecode(v)
    end
    tb.event_occur_info = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.event_occur_info[k] = pb.pb_QuestVarEntryDecode(v)
    end
    local __reward_time = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __reward_time ~= 0 then tb.reward_time = __reward_time end
    return tb
end

function pb.pb_PlayerQuestDataEncode(tb, encoder)
    if(tb.quest_id) then    encoder:addu64(1, tb.quest_id)    end
    if(tb.quest_state) then    encoder:addi32(2, tb.quest_state)    end
    if(tb.quest_accept_time) then    encoder:addi64(3, tb.quest_accept_time)    end
    if(tb.quest_var) then    encoder:addi64(5, tb.quest_var)    end
    if(tb.quest_objectives) then
        for i=1,#(tb.quest_objectives) do
            pb.pb_PlayerQuestObjectiveDataEncode(tb.quest_objectives[i], encoder:addsubmsg(6))
        end
    end
    if(tb.marked_objective_id) then    encoder:addu64(7, tb.marked_objective_id)    end
    if(tb.expire_time) then    encoder:addi64(8, tb.expire_time)    end
    if(tb.enter_map_info) then
        for i=1,#(tb.enter_map_info) do
            pb.pb_QuestVarEntryEncode(tb.enter_map_info[i], encoder:addsubmsg(9))
        end
    end
    if(tb.event_occur_info) then
        for i=1,#(tb.event_occur_info) do
            pb.pb_QuestVarEntryEncode(tb.event_occur_info[i], encoder:addsubmsg(10))
        end
    end
    if(tb.reward_time) then    encoder:addi64(11, tb.reward_time)    end
end

function pb.pb_PlayerQuestObjectiveDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerQuestObjectiveData) or {} 
    local __quest_objective_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __quest_objective_id ~= 0 then tb.quest_objective_id = __quest_objective_id end
    local __has_completed = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __has_completed ~= false then tb.has_completed = __has_completed end
    local __value = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    local __has_marked = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __has_marked ~= false then tb.has_marked = __has_marked end
    local __spent_seconds = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __spent_seconds ~= 0 then tb.spent_seconds = __spent_seconds end
    tb.extra_data = pb.pb_PlayerObjectiveExtraDataDecode(decoder:getsubmsg(12))
    return tb
end

function pb.pb_PlayerQuestObjectiveDataEncode(tb, encoder)
    if(tb.quest_objective_id) then    encoder:addu64(1, tb.quest_objective_id)    end
    if(tb.has_completed) then    encoder:addbool(2, tb.has_completed)    end
    if(tb.value) then    encoder:addi64(3, tb.value)    end
    if(tb.has_marked) then    encoder:addbool(4, tb.has_marked)    end
    if(tb.spent_seconds) then    encoder:addi64(5, tb.spent_seconds)    end
    if(tb.extra_data) then    pb.pb_PlayerObjectiveExtraDataEncode(tb.extra_data, encoder:addsubmsg(12))    end
end

function pb.pb_PlayerRttDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerRttData) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __rtt = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __rtt ~= 0 then tb.rtt = __rtt end
    local __access_point = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __access_point ~= "" then tb.access_point = __access_point end
    local __idc = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __idc ~= "" then tb.idc = __idc end
    return tb
end

function pb.pb_PlayerRttDataEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.rtt) then    encoder:addi32(2, tb.rtt)    end
    if(tb.access_point) then    encoder:addstr(3, tb.access_point)    end
    if(tb.idc) then    encoder:addstr(4, tb.idc)    end
end

function pb.pb_PropGoldEggDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropGoldEgg) or {} 
    local __owner = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __owner ~= "" then tb.owner = __owner end
    local __time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    return tb
end

function pb.pb_PropGoldEggEncode(tb, encoder)
    if(tb.owner) then    encoder:addstr(1, tb.owner)    end
    if(tb.time) then    encoder:addi64(2, tb.time)    end
end

function pb.pb_PropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __gid ~= 0 then tb.gid = __gid end
    local __num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __position = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __position ~= 0 then tb.position = __position end
    local __source = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    local __reason = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __length = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __length ~= 0 then tb.length = __length end
    local __width = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __width ~= 0 then tb.width = __width end
    local __health = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __health ~= 0 then tb.health = __health end
    local __health_max = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __health_max ~= 0 then tb.health_max = __health_max end
    tb.loc = pb.pb_PropLocationDecode(decoder:getsubmsg(12))
    local __bind_type = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __bind_type ~= 0 then tb.bind_type = __bind_type end
    local __bind_player = decoder:getu64(14)
    if not PB_USE_DEFAULT_TABLE or __bind_player ~= 0 then tb.bind_player = __bind_player end
    local __break_tag = decoder:getu64(15)
    if not PB_USE_DEFAULT_TABLE or __break_tag ~= 0 then tb.break_tag = __break_tag end
    local __aging_type = decoder:geti32(16)
    if not PB_USE_DEFAULT_TABLE or __aging_type ~= 0 then tb.aging_type = __aging_type end
    local __aging_begin_time = decoder:geti64(17)
    if not PB_USE_DEFAULT_TABLE or __aging_begin_time ~= 0 then tb.aging_begin_time = __aging_begin_time end
    local __aging_duration = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __aging_duration ~= 0 then tb.aging_duration = __aging_duration end
    local __use_cnt = decoder:geti32(19)
    if not PB_USE_DEFAULT_TABLE or __use_cnt ~= 0 then tb.use_cnt = __use_cnt end
    local __use_cnt_max = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __use_cnt_max ~= 0 then tb.use_cnt_max = __use_cnt_max end
    local __tag = decoder:getu64(23)
    if not PB_USE_DEFAULT_TABLE or __tag ~= 0 then tb.tag = __tag end
    local __gained_time = decoder:geti64(25)
    if not PB_USE_DEFAULT_TABLE or __gained_time ~= 0 then tb.gained_time = __gained_time end
    tb.components = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.components[k] = pb.pb_ComponentDecode(v)
    end
    tb.record = pb.pb_PropRecordDecode(decoder:getsubmsg(20))
    tb.weapon = pb.pb_WeaponInfoDecode(decoder:getsubmsg(21))
    tb.hero = pb.pb_HeroAttrDecode(decoder:getsubmsg(24))
    local __drop_ai_gid = decoder:geti32(26)
    if not PB_USE_DEFAULT_TABLE or __drop_ai_gid ~= 0 then tb.drop_ai_gid = __drop_ai_gid end
    local __drop_point_key = decoder:getstr(27)
    if not PB_USE_DEFAULT_TABLE or __drop_point_key ~= "" then tb.drop_point_key = __drop_point_key end
    local __drop_box_key = decoder:getstr(28)
    if not PB_USE_DEFAULT_TABLE or __drop_box_key ~= "" then tb.drop_box_key = __drop_box_key end
    local __auction_armor_map_id = decoder:getu64(32)
    if not PB_USE_DEFAULT_TABLE or __auction_armor_map_id ~= 0 then tb.auction_armor_map_id = __auction_armor_map_id end
    local __durability_lvl = decoder:getu64(33)
    if not PB_USE_DEFAULT_TABLE or __durability_lvl ~= 0 then tb.durability_lvl = __durability_lvl end
    local __src_id = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __src_id ~= 0 then tb.src_id = __src_id end
    local __src_num = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __src_num ~= 0 then tb.src_num = __src_num end
    local __expire_timestamp = decoder:geti64(34)
    if not PB_USE_DEFAULT_TABLE or __expire_timestamp ~= 0 then tb.expire_timestamp = __expire_timestamp end
    tb.mystical_skin_data = pb.pb_MysticalSkinInfoDecode(decoder:getsubmsg(35))
    tb.mystical_pendant_data = pb.pb_MysticalPendantInfoDecode(decoder:getsubmsg(39))
    local __tglog_raw_gid = decoder:getu64(36)
    if not PB_USE_DEFAULT_TABLE or __tglog_raw_gid ~= 0 then tb.tglog_raw_gid = __tglog_raw_gid end
    local __tglog_src_player_id = decoder:getu64(37)
    if not PB_USE_DEFAULT_TABLE or __tglog_src_player_id ~= 0 then tb.tglog_src_player_id = __tglog_src_player_id end
    tb.sources = {}
    for k,v in pairs(decoder:getsubmsgary(38)) do
        tb.sources[k] = pb.pb_PropSourceDecode(v)
    end
    local __rights_id = decoder:geti64(40)
    if not PB_USE_DEFAULT_TABLE or __rights_id ~= 0 then tb.rights_id = __rights_id end
    local __activity_prop_mode = decoder:getu32(41)
    if not PB_USE_DEFAULT_TABLE or __activity_prop_mode ~= 0 then tb.activity_prop_mode = __activity_prop_mode end
    tb.gold_egg_record = pb.pb_PropGoldEggDecode(decoder:getsubmsg(42))
    return tb
end

function pb.pb_PropInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.gid) then    encoder:addu64(2, tb.gid)    end
    if(tb.num) then    encoder:addi64(3, tb.num)    end
    if(tb.position) then    encoder:addi32(4, tb.position)    end
    if(tb.source) then    encoder:addu64(5, tb.source)    end
    if(tb.reason) then    encoder:addi32(6, tb.reason)    end
    if(tb.length) then    encoder:addi32(7, tb.length)    end
    if(tb.width) then    encoder:addi32(8, tb.width)    end
    if(tb.health) then    encoder:addi32(10, tb.health)    end
    if(tb.health_max) then    encoder:addi32(11, tb.health_max)    end
    if(tb.loc) then    pb.pb_PropLocationEncode(tb.loc, encoder:addsubmsg(12))    end
    if(tb.bind_type) then    encoder:addi32(13, tb.bind_type)    end
    if(tb.bind_player) then    encoder:addu64(14, tb.bind_player)    end
    if(tb.break_tag) then    encoder:addu64(15, tb.break_tag)    end
    if(tb.aging_type) then    encoder:addi32(16, tb.aging_type)    end
    if(tb.aging_begin_time) then    encoder:addi64(17, tb.aging_begin_time)    end
    if(tb.aging_duration) then    encoder:addi64(18, tb.aging_duration)    end
    if(tb.use_cnt) then    encoder:addi32(19, tb.use_cnt)    end
    if(tb.use_cnt_max) then    encoder:addi32(22, tb.use_cnt_max)    end
    if(tb.tag) then    encoder:addu64(23, tb.tag)    end
    if(tb.gained_time) then    encoder:addi64(25, tb.gained_time)    end
    if(tb.components) then
        for i=1,#(tb.components) do
            pb.pb_ComponentEncode(tb.components[i], encoder:addsubmsg(9))
        end
    end
    if(tb.record) then    pb.pb_PropRecordEncode(tb.record, encoder:addsubmsg(20))    end
    if(tb.weapon) then    pb.pb_WeaponInfoEncode(tb.weapon, encoder:addsubmsg(21))    end
    if(tb.hero) then    pb.pb_HeroAttrEncode(tb.hero, encoder:addsubmsg(24))    end
    if(tb.drop_ai_gid) then    encoder:addi32(26, tb.drop_ai_gid)    end
    if(tb.drop_point_key) then    encoder:addstr(27, tb.drop_point_key)    end
    if(tb.drop_box_key) then    encoder:addstr(28, tb.drop_box_key)    end
    if(tb.auction_armor_map_id) then    encoder:addu64(32, tb.auction_armor_map_id)    end
    if(tb.durability_lvl) then    encoder:addu64(33, tb.durability_lvl)    end
    if(tb.src_id) then    encoder:addu64(30, tb.src_id)    end
    if(tb.src_num) then    encoder:addi64(31, tb.src_num)    end
    if(tb.expire_timestamp) then    encoder:addi64(34, tb.expire_timestamp)    end
    if(tb.mystical_skin_data) then    pb.pb_MysticalSkinInfoEncode(tb.mystical_skin_data, encoder:addsubmsg(35))    end
    if(tb.mystical_pendant_data) then    pb.pb_MysticalPendantInfoEncode(tb.mystical_pendant_data, encoder:addsubmsg(39))    end
    if(tb.tglog_raw_gid) then    encoder:addu64(36, tb.tglog_raw_gid)    end
    if(tb.tglog_src_player_id) then    encoder:addu64(37, tb.tglog_src_player_id)    end
    if(tb.sources) then
        for i=1,#(tb.sources) do
            pb.pb_PropSourceEncode(tb.sources[i], encoder:addsubmsg(38))
        end
    end
    if(tb.rights_id) then    encoder:addi64(40, tb.rights_id)    end
    if(tb.activity_prop_mode) then    encoder:addu32(41, tb.activity_prop_mode)    end
    if(tb.gold_egg_record) then    pb.pb_PropGoldEggEncode(tb.gold_egg_record, encoder:addsubmsg(42))    end
end

function pb.pb_PropSourceDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropSource) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __type = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __source = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __source ~= "" then tb.source = __source end
    return tb
end

function pb.pb_PropSourceEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.num) then    encoder:addi64(2, tb.num)    end
    if(tb.type) then    encoder:addu32(3, tb.type)    end
    if(tb.source) then    encoder:addstr(4, tb.source)    end
end

function pb.pb_PropLocationDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropLocation) or {} 
    local __pos = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __pos ~= 0 then tb.pos = __pos end
    local __space_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __space_id ~= 0 then tb.space_id = __space_id end
    local __start_x = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __start_x ~= 0 then tb.start_x = __start_x end
    local __start_y = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __start_y ~= 0 then tb.start_y = __start_y end
    local __x = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __x ~= 0 then tb.x = __x end
    local __y = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __y ~= 0 then tb.y = __y end
    local __rotate = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __rotate ~= false then tb.rotate = __rotate end
    local __is_newly_get = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __is_newly_get ~= false then tb.is_newly_get = __is_newly_get end
    return tb
end

function pb.pb_PropLocationEncode(tb, encoder)
    if(tb.pos) then    encoder:addi32(1, tb.pos)    end
    if(tb.space_id) then    encoder:addi32(6, tb.space_id)    end
    if(tb.start_x) then    encoder:addi32(2, tb.start_x)    end
    if(tb.start_y) then    encoder:addi32(3, tb.start_y)    end
    if(tb.x) then    encoder:addi32(4, tb.x)    end
    if(tb.y) then    encoder:addi32(5, tb.y)    end
    if(tb.rotate) then    encoder:addbool(7, tb.rotate)    end
    if(tb.is_newly_get) then    encoder:addbool(8, tb.is_newly_get)    end
end

function pb.pb_PropRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropRecord) or {} 
    local __Owner = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __Owner ~= "" then tb.Owner = __Owner end
    local __OwnerLevel = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __OwnerLevel ~= 0 then tb.OwnerLevel = __OwnerLevel end
    local __RecordType = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __RecordType ~= 0 then tb.RecordType = __RecordType end
    local __DeathReason = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __DeathReason ~= 0 then tb.DeathReason = __DeathReason end
    local __AvatarID = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __AvatarID ~= 0 then tb.AvatarID = __AvatarID end
    local __Killer = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __Killer ~= "" then tb.Killer = __Killer end
    local __KillerWeaponID = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __KillerWeaponID ~= 0 then tb.KillerWeaponID = __KillerWeaponID end
    local __KillTime = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __KillTime ~= 0 then tb.KillTime = __KillTime end
    local __KillerBulletID = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __KillerBulletID ~= 0 then tb.KillerBulletID = __KillerBulletID end
    local __MapID = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __MapID ~= 0 then tb.MapID = __MapID end
    local __KillTimestamp = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __KillTimestamp ~= 0 then tb.KillTimestamp = __KillTimestamp end
    local __KillerBulletLevel = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __KillerBulletLevel ~= 0 then tb.KillerBulletLevel = __KillerBulletLevel end
    local __CustomID = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __CustomID ~= 0 then tb.CustomID = __CustomID end
    return tb
end

function pb.pb_PropRecordEncode(tb, encoder)
    if(tb.Owner) then    encoder:addstr(1, tb.Owner)    end
    if(tb.OwnerLevel) then    encoder:addi32(6, tb.OwnerLevel)    end
    if(tb.RecordType) then    encoder:addu32(3, tb.RecordType)    end
    if(tb.DeathReason) then    encoder:addi32(4, tb.DeathReason)    end
    if(tb.AvatarID) then    encoder:addu64(2, tb.AvatarID)    end
    if(tb.Killer) then    encoder:addstr(8, tb.Killer)    end
    if(tb.KillerWeaponID) then    encoder:addu64(5, tb.KillerWeaponID)    end
    if(tb.KillTime) then    encoder:addi64(7, tb.KillTime)    end
    if(tb.KillerBulletID) then    encoder:addu64(9, tb.KillerBulletID)    end
    if(tb.MapID) then    encoder:addu64(10, tb.MapID)    end
    if(tb.KillTimestamp) then    encoder:addi64(11, tb.KillTimestamp)    end
    if(tb.KillerBulletLevel) then    encoder:addi32(12, tb.KillerBulletLevel)    end
    if(tb.CustomID) then    encoder:addi32(13, tb.CustomID)    end
end

function pb.pb_QuestDSGameStateDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_QuestDSGameState) or {} 
    local __state_key = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __state_key ~= 0 then tb.state_key = __state_key end
    local __action_value = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __action_value ~= 0 then tb.action_value = __action_value end
    return tb
end

function pb.pb_QuestDSGameStateEncode(tb, encoder)
    if(tb.state_key) then    encoder:addu64(1, tb.state_key)    end
    if(tb.action_value) then    encoder:addu64(2, tb.action_value)    end
end

function pb.pb_QuestVarEntryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_QuestVarEntry) or {} 
    local __key = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __key ~= 0 then tb.key = __key end
    local __value = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_QuestVarEntryEncode(tb, encoder)
    if(tb.key) then    encoder:addi64(1, tb.key)    end
    if(tb.value) then    encoder:addi64(2, tb.value)    end
end

function pb.pb_RoleLoadValueDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoleLoadValue) or {} 
    local __load_level = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __load_level ~= 0 then tb.load_level = __load_level end
    local __over_weight = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __over_weight ~= 0 then tb.over_weight = __over_weight end
    local __little_weight = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __little_weight ~= 0 then tb.little_weight = __little_weight end
    local __severe_over_weight = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __severe_over_weight ~= 0 then tb.severe_over_weight = __severe_over_weight end
    local __middle_weight = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __middle_weight ~= 0 then tb.middle_weight = __middle_weight end
    local __over_weight_v2 = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __over_weight_v2 ~= 0 then tb.over_weight_v2 = __over_weight_v2 end
    tb.little_weight_list = decoder:getu32ary(7)
    tb.middle_weight_list = decoder:getu32ary(8)
    return tb
end

function pb.pb_RoleLoadValueEncode(tb, encoder)
    if(tb.load_level) then    encoder:addu32(1, tb.load_level)    end
    if(tb.over_weight) then    encoder:addu32(2, tb.over_weight)    end
    if(tb.little_weight) then    encoder:addu32(4, tb.little_weight)    end
    if(tb.severe_over_weight) then    encoder:addu32(3, tb.severe_over_weight)    end
    if(tb.middle_weight) then    encoder:addu32(5, tb.middle_weight)    end
    if(tb.over_weight_v2) then    encoder:addu32(6, tb.over_weight_v2)    end
    if(tb.little_weight_list) then    encoder:addu32(7, tb.little_weight_list)    end
    if(tb.middle_weight_list) then    encoder:addu32(8, tb.middle_weight_list)    end
end

function pb.pb_RttBestIdcDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RttBestIdc) or {} 
    local __idc = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __idc ~= "" then tb.idc = __idc end
    local __rtt = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __rtt ~= 0 then tb.rtt = __rtt end
    tb.player_rtt_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.player_rtt_list[k] = pb.pb_PlayerRttDataDecode(v)
    end
    return tb
end

function pb.pb_RttBestIdcEncode(tb, encoder)
    if(tb.idc) then    encoder:addstr(1, tb.idc)    end
    if(tb.rtt) then    encoder:addi32(2, tb.rtt)    end
    if(tb.player_rtt_list) then
        for i=1,#(tb.player_rtt_list) do
            pb.pb_PlayerRttDataEncode(tb.player_rtt_list[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_SafehouseNumeralInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SafehouseNumeralInfo) or {} 
    tb.attr_operate_ids = decoder:getu32ary(1)
    return tb
end

function pb.pb_SafehouseNumeralInfoEncode(tb, encoder)
    if(tb.attr_operate_ids) then    encoder:addu32(1, tb.attr_operate_ids)    end
end

function pb.pb_PropGuidePriceDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropGuidePrice) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __price = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __price ~= 0 then tb.price = __price end
    return tb
end

function pb.pb_PropGuidePriceEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.price) then    encoder:addi64(2, tb.price)    end
end

function pb.pb_SecTLogHeadCommDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SecTLogHeadComm) or {} 
    local __GameSvrId = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __GameSvrId ~= "" then tb.GameSvrId = __GameSvrId end
    local __DtEventTime = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __DtEventTime ~= "" then tb.DtEventTime = __DtEventTime end
    local __VGameAppid = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __VGameAppid ~= "" then tb.VGameAppid = __VGameAppid end
    local __PlatID = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __PlatID ~= 0 then tb.PlatID = __PlatID end
    local __IZoneAreaID = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __IZoneAreaID ~= 0 then tb.IZoneAreaID = __IZoneAreaID end
    local __Vopenid = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __Vopenid ~= "" then tb.Vopenid = __Vopenid end
    local __AreaID = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __AreaID ~= 0 then tb.AreaID = __AreaID end
    local __ClientVersion = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __ClientVersion ~= "" then tb.ClientVersion = __ClientVersion end
    return tb
end

function pb.pb_SecTLogHeadCommEncode(tb, encoder)
    if(tb.GameSvrId) then    encoder:addstr(1, tb.GameSvrId)    end
    if(tb.DtEventTime) then    encoder:addstr(2, tb.DtEventTime)    end
    if(tb.VGameAppid) then    encoder:addstr(3, tb.VGameAppid)    end
    if(tb.PlatID) then    encoder:addi32(4, tb.PlatID)    end
    if(tb.IZoneAreaID) then    encoder:addi32(5, tb.IZoneAreaID)    end
    if(tb.Vopenid) then    encoder:addstr(6, tb.Vopenid)    end
    if(tb.AreaID) then    encoder:addi32(7, tb.AreaID)    end
    if(tb.ClientVersion) then    encoder:addstr(8, tb.ClientVersion)    end
end

function pb.pb_SecTLogHeadDeviceDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SecTLogHeadDevice) or {} 
    local __SystemSoftware = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __SystemSoftware ~= "" then tb.SystemSoftware = __SystemSoftware end
    local __SystemHardware = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __SystemHardware ~= "" then tb.SystemHardware = __SystemHardware end
    local __TelecomOper = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __TelecomOper ~= "" then tb.TelecomOper = __TelecomOper end
    local __Network = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __Network ~= "" then tb.Network = __Network end
    local __DeviceId = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __DeviceId ~= "" then tb.DeviceId = __DeviceId end
    local __RealIMEI = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __RealIMEI ~= "" then tb.RealIMEI = __RealIMEI end
    local __UserIP = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __UserIP ~= "" then tb.UserIP = __UserIP end
    return tb
end

function pb.pb_SecTLogHeadDeviceEncode(tb, encoder)
    if(tb.SystemSoftware) then    encoder:addstr(1, tb.SystemSoftware)    end
    if(tb.SystemHardware) then    encoder:addstr(2, tb.SystemHardware)    end
    if(tb.TelecomOper) then    encoder:addstr(3, tb.TelecomOper)    end
    if(tb.Network) then    encoder:addstr(4, tb.Network)    end
    if(tb.DeviceId) then    encoder:addstr(5, tb.DeviceId)    end
    if(tb.RealIMEI) then    encoder:addstr(6, tb.RealIMEI)    end
    if(tb.UserIP) then    encoder:addstr(7, tb.UserIP)    end
end

function pb.pb_SecTLogHeadRoleDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SecTLogHeadRole) or {} 
    local __RoleName = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __RoleName ~= "" then tb.RoleName = __RoleName end
    local __RoleId = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __RoleId ~= 0 then tb.RoleId = __RoleId end
    local __RoleType = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __RoleType ~= 0 then tb.RoleType = __RoleType end
    local __RoleLevel = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __RoleLevel ~= 0 then tb.RoleLevel = __RoleLevel end
    local __RoleRankLevel = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __RoleRankLevel ~= 0 then tb.RoleRankLevel = __RoleRankLevel end
    local __RoleVipLevel = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __RoleVipLevel ~= 0 then tb.RoleVipLevel = __RoleVipLevel end
    local __RoleBattlePoint = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __RoleBattlePoint ~= 0 then tb.RoleBattlePoint = __RoleBattlePoint end
    local __RoleGroupID = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __RoleGroupID ~= 0 then tb.RoleGroupID = __RoleGroupID end
    local __RoleGroupName = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __RoleGroupName ~= "" then tb.RoleGroupName = __RoleGroupName end
    local __PicUrl = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __PicUrl ~= "" then tb.PicUrl = __PicUrl end
    return tb
end

function pb.pb_SecTLogHeadRoleEncode(tb, encoder)
    if(tb.RoleName) then    encoder:addstr(1, tb.RoleName)    end
    if(tb.RoleId) then    encoder:addu64(2, tb.RoleId)    end
    if(tb.RoleType) then    encoder:addi32(3, tb.RoleType)    end
    if(tb.RoleLevel) then    encoder:addi32(4, tb.RoleLevel)    end
    if(tb.RoleRankLevel) then    encoder:addi32(5, tb.RoleRankLevel)    end
    if(tb.RoleVipLevel) then    encoder:addi32(6, tb.RoleVipLevel)    end
    if(tb.RoleBattlePoint) then    encoder:addi32(7, tb.RoleBattlePoint)    end
    if(tb.RoleGroupID) then    encoder:addu64(8, tb.RoleGroupID)    end
    if(tb.RoleGroupName) then    encoder:addstr(9, tb.RoleGroupName)    end
    if(tb.PicUrl) then    encoder:addstr(10, tb.PicUrl)    end
end

function pb.pb_StrongholdInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StrongholdInfo) or {} 
    local __stronghold_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __stronghold_id ~= 0 then tb.stronghold_id = __stronghold_id end
    local __state = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    tb.total_collections = decoder:getu64ary(6)
    tb.collections = decoder:getu64ary(3)
    local __rewarded = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __rewarded ~= false then tb.rewarded = __rewarded end
    tb.rewards = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.rewards[k] = pb.pb_StrongholdRewardDecode(v)
    end
    return tb
end

function pb.pb_StrongholdInfoEncode(tb, encoder)
    if(tb.stronghold_id) then    encoder:addu64(1, tb.stronghold_id)    end
    if(tb.state) then    encoder:addu32(2, tb.state)    end
    if(tb.total_collections) then    encoder:addu64(6, tb.total_collections)    end
    if(tb.collections) then    encoder:addu64(3, tb.collections)    end
    if(tb.rewarded) then    encoder:addbool(4, tb.rewarded)    end
    if(tb.rewards) then
        for i=1,#(tb.rewards) do
            pb.pb_StrongholdRewardEncode(tb.rewards[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_StrongholdRewardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StrongholdReward) or {} 
    local __stronghold_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __stronghold_id ~= 0 then tb.stronghold_id = __stronghold_id end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_StrongholdRewardEncode(tb, encoder)
    if(tb.stronghold_id) then    encoder:addu64(1, tb.stronghold_id)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(2))    end
end

function pb.pb_TDMBagArmedPresetDSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMBagArmedPresetDS) or {} 
    local __armedforce_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    local __default_bag_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __default_bag_id ~= 0 then tb.default_bag_id = __default_bag_id end
    tb.bags = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.bags[k] = pb.pb_TDMBagDSDecode(v)
    end
    local __max_bag_num = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __max_bag_num ~= 0 then tb.max_bag_num = __max_bag_num end
    local __expert_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    local __def_bag_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __def_bag_id ~= 0 then tb.def_bag_id = __def_bag_id end
    tb.armed_props = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.armed_props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_TDMBagArmedPresetDSEncode(tb, encoder)
    if(tb.armedforce_id) then    encoder:addu32(1, tb.armedforce_id)    end
    if(tb.default_bag_id) then    encoder:addi32(2, tb.default_bag_id)    end
    if(tb.bags) then
        for i=1,#(tb.bags) do
            pb.pb_TDMBagDSEncode(tb.bags[i], encoder:addsubmsg(3))
        end
    end
    if(tb.max_bag_num) then    encoder:addu32(4, tb.max_bag_num)    end
    if(tb.expert_id) then    encoder:addu32(5, tb.expert_id)    end
    if(tb.def_bag_id) then    encoder:addu32(6, tb.def_bag_id)    end
    if(tb.armed_props) then
        for i=1,#(tb.armed_props) do
            pb.pb_PropInfoEncode(tb.armed_props[i], encoder:addsubmsg(7))
        end
    end
end

function pb.pb_TDMBagDSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMBagDS) or {} 
    local __bag_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __bag_id ~= 0 then tb.bag_id = __bag_id end
    local __armedforce_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.props[k] = pb.pb_EquipPositionDecode(v)
    end
    local __talent_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __talent_id ~= 0 then tb.talent_id = __talent_id end
    tb.talent = pb.pb_ArmedForceTalentDecode(decoder:getsubmsg(5))
    local __expert_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    local __expert_bag_id = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __expert_bag_id ~= 0 then tb.expert_bag_id = __expert_bag_id end
    tb.equip_skin_props = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.equip_skin_props[k] = pb.pb_PropInfoDecode(v)
    end
    tb.equip_pendant_props = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.equip_pendant_props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_TDMBagDSEncode(tb, encoder)
    if(tb.bag_id) then    encoder:addu32(1, tb.bag_id)    end
    if(tb.armedforce_id) then    encoder:addu32(2, tb.armedforce_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_EquipPositionEncode(tb.props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.talent_id) then    encoder:addu32(4, tb.talent_id)    end
    if(tb.talent) then    pb.pb_ArmedForceTalentEncode(tb.talent, encoder:addsubmsg(5))    end
    if(tb.expert_id) then    encoder:addu32(6, tb.expert_id)    end
    if(tb.expert_bag_id) then    encoder:addu32(7, tb.expert_bag_id)    end
    if(tb.equip_skin_props) then
        for i=1,#(tb.equip_skin_props) do
            pb.pb_PropInfoEncode(tb.equip_skin_props[i], encoder:addsubmsg(8))
        end
    end
    if(tb.equip_pendant_props) then
        for i=1,#(tb.equip_pendant_props) do
            pb.pb_PropInfoEncode(tb.equip_pendant_props[i], encoder:addsubmsg(9))
        end
    end
end

function pb.pb_MPLevelDescDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPLevelDesc) or {} 
    local __sum_exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __sum_exp ~= 0 then tb.sum_exp = __sum_exp end
    tb.unlock_props = decoder:getu64ary(3)
    return tb
end

function pb.pb_MPLevelDescEncode(tb, encoder)
    if(tb.sum_exp) then    encoder:addi64(2, tb.sum_exp)    end
    if(tb.unlock_props) then    encoder:addu64(3, tb.unlock_props)    end
end

function pb.pb_MPLevelsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPLevels) or {} 
    local __weapon_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    local __cur_lvl = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __cur_lvl ~= 0 then tb.cur_lvl = __cur_lvl end
    tb.lvl_descs = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.lvl_descs[k] = pb.pb_MPLevelDescDecode(v)
    end
    return tb
end

function pb.pb_MPLevelsEncode(tb, encoder)
    if(tb.weapon_id) then    encoder:addu64(1, tb.weapon_id)    end
    if(tb.cur_lvl) then    encoder:addu32(2, tb.cur_lvl)    end
    if(tb.lvl_descs) then
        for i=1,#(tb.lvl_descs) do
            pb.pb_MPLevelDescEncode(tb.lvl_descs[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_TDMGameMVPEvaluatedDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMGameMVPEvaluated) or {} 
    local __dimension = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __dimension ~= 0 then tb.dimension = __dimension end
    local __value = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_TDMGameMVPEvaluatedEncode(tb, encoder)
    if(tb.dimension) then    encoder:addi32(1, tb.dimension)    end
    if(tb.value) then    encoder:addi32(2, tb.value)    end
end

function pb.pb_TDMMvpDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMMvp) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.evaluated_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.evaluated_list[k] = pb.pb_TDMGameMVPEvaluatedDecode(v)
    end
    return tb
end

function pb.pb_TDMMvpEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.evaluated_list) then
        for i=1,#(tb.evaluated_list) do
            pb.pb_TDMGameMVPEvaluatedEncode(tb.evaluated_list[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_MPPropPosCSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPPropPosCS) or {} 
    local __pos_id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __pos_id ~= 0 then tb.pos_id = __pos_id end
    local __prop_gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __prop_gid ~= 0 then tb.prop_gid = __prop_gid end
    local __prop_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    return tb
end

function pb.pb_MPPropPosCSEncode(tb, encoder)
    if(tb.pos_id) then    encoder:addi32(1, tb.pos_id)    end
    if(tb.prop_gid) then    encoder:addu64(2, tb.prop_gid)    end
    if(tb.prop_id) then    encoder:addu64(3, tb.prop_id)    end
end

function pb.pb_MPPresetCSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPPresetCS) or {} 
    local __bag_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __bag_id ~= 0 then tb.bag_id = __bag_id end
    tb.mp_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.mp_props[k] = pb.pb_MPPropPosCSDecode(v)
    end
    local __expert_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    return tb
end

function pb.pb_MPPresetCSEncode(tb, encoder)
    if(tb.bag_id) then    encoder:addu32(1, tb.bag_id)    end
    if(tb.mp_props) then
        for i=1,#(tb.mp_props) do
            pb.pb_MPPropPosCSEncode(tb.mp_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.expert_id) then    encoder:addu32(3, tb.expert_id)    end
end

function pb.pb_MPWeaponSettlementDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPWeaponSettlement) or {} 
    local __weapon_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    local __slot_index = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __slot_index ~= 0 then tb.slot_index = __slot_index end
    return tb
end

function pb.pb_MPWeaponSettlementEncode(tb, encoder)
    if(tb.weapon_id) then    encoder:addu64(1, tb.weapon_id)    end
    if(tb.slot_index) then    encoder:addi32(2, tb.slot_index)    end
end

function pb.pb_VehicleSlotPartCfgDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_VehicleSlotPartCfg) or {} 
    local __part_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __part_id ~= 0 then tb.part_id = __part_id end
    local __unlock_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __unlock_type ~= 0 then tb.unlock_type = __unlock_type end
    local __unlock_para = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __unlock_para ~= 0 then tb.unlock_para = __unlock_para end
    return tb
end

function pb.pb_VehicleSlotPartCfgEncode(tb, encoder)
    if(tb.part_id) then    encoder:addu64(1, tb.part_id)    end
    if(tb.unlock_type) then    encoder:addi32(2, tb.unlock_type)    end
    if(tb.unlock_para) then    encoder:addu32(3, tb.unlock_para)    end
end

function pb.pb_VehicleSlotCfgDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_VehicleSlotCfg) or {} 
    local __slot_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __slot_id ~= 0 then tb.slot_id = __slot_id end
    tb.parts = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.parts[k] = pb.pb_VehicleSlotPartCfgDecode(v)
    end
    return tb
end

function pb.pb_VehicleSlotCfgEncode(tb, encoder)
    if(tb.slot_id) then    encoder:addu32(1, tb.slot_id)    end
    if(tb.parts) then
        for i=1,#(tb.parts) do
            pb.pb_VehicleSlotPartCfgEncode(tb.parts[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_VehicleModificationRuleDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_VehicleModificationRule) or {} 
    local __vehicle_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __vehicle_id ~= 0 then tb.vehicle_id = __vehicle_id end
    tb.VehicleSlotCfgs = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.VehicleSlotCfgs[k] = pb.pb_VehicleSlotCfgDecode(v)
    end
    return tb
end

function pb.pb_VehicleModificationRuleEncode(tb, encoder)
    if(tb.vehicle_id) then    encoder:addu64(1, tb.vehicle_id)    end
    if(tb.VehicleSlotCfgs) then
        for i=1,#(tb.VehicleSlotCfgs) do
            pb.pb_VehicleSlotCfgEncode(tb.VehicleSlotCfgs[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_TDMNumeralDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMNumeral) or {} 
    tb.armedforce_id_list = decoder:getu32ary(2)
    local __selected_armedforce_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __selected_armedforce_id ~= 0 then tb.selected_armedforce_id = __selected_armedforce_id end
    local __selected_bag_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __selected_bag_id ~= 0 then tb.selected_bag_id = __selected_bag_id end
    tb.talent = pb.pb_ArmedForceTalentDecode(decoder:getsubmsg(5))
    tb.bags = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.bags[k] = pb.pb_TDMBagArmedPresetDSDecode(v)
    end
    local __selected_expert_bag_id = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __selected_expert_bag_id ~= 0 then tb.selected_expert_bag_id = __selected_expert_bag_id end
    tb.vehicle_store = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.vehicle_store[k] = pb.pb_TDMNumeral_VehicleStoreEntryDecode(v)
    end
    tb.part_store = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.part_store[k] = pb.pb_TDMNumeral_PartStoreEntryDecode(v)
    end
    tb.vehicles = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.vehicles[k] = pb.pb_VehicleDecode(v)
    end
    tb.parts = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.parts[k] = pb.pb_VehiclePartDecode(v)
    end
    tb.weapon_lvls = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.weapon_lvls[k] = pb.pb_MPLevelsDecode(v)
    end
    tb.account_lvls = pb.pb_MPLevelsDecode(decoder:getsubmsg(13))
    local __double_account_exp_rate = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __double_account_exp_rate ~= 0 then tb.double_account_exp_rate = __double_account_exp_rate end
    local __double_rank_score_rate = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __double_rank_score_rate ~= 0 then tb.double_rank_score_rate = __double_rank_score_rate end
    tb.preset_data = {}
    for k,v in pairs(decoder:getsubmsgary(16)) do
        tb.preset_data[k] = pb.pb_MPPresetCSDecode(v)
    end
    tb.armed_store = {}
    for k,v in pairs(decoder:getsubmsgary(17)) do
        tb.armed_store[k] = pb.pb_PropInfoDecode(v)
    end
    tb.forbidden_prop = decoder:getu64ary(18)
    tb.prop_data = pb.pb_TDMNumeralPropDecode(decoder:getsubmsg(19))
    tb.allow_prop = decoder:getu64ary(20)
    tb.vehicle_rules = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.vehicle_rules[k] = pb.pb_VehicleModificationRuleDecode(v)
    end
    local __orgin_commander_score = decoder:geti64(22)
    if not PB_USE_DEFAULT_TABLE or __orgin_commander_score ~= 0 then tb.orgin_commander_score = __orgin_commander_score end
    return tb
end

function pb.pb_TDMNumeralEncode(tb, encoder)
    if(tb.armedforce_id_list) then    encoder:addu32(2, tb.armedforce_id_list)    end
    if(tb.selected_armedforce_id) then    encoder:addu32(3, tb.selected_armedforce_id)    end
    if(tb.selected_bag_id) then    encoder:addu32(4, tb.selected_bag_id)    end
    if(tb.talent) then    pb.pb_ArmedForceTalentEncode(tb.talent, encoder:addsubmsg(5))    end
    if(tb.bags) then
        for i=1,#(tb.bags) do
            pb.pb_TDMBagArmedPresetDSEncode(tb.bags[i], encoder:addsubmsg(6))
        end
    end
    if(tb.selected_expert_bag_id) then    encoder:addu32(7, tb.selected_expert_bag_id)    end
    if(tb.vehicle_store) then
        for i=1,#(tb.vehicle_store) do
            pb.pb_TDMNumeral_VehicleStoreEntryEncode(tb.vehicle_store[i], encoder:addsubmsg(8))
        end
    end
    if(tb.part_store) then
        for i=1,#(tb.part_store) do
            pb.pb_TDMNumeral_PartStoreEntryEncode(tb.part_store[i], encoder:addsubmsg(9))
        end
    end
    if(tb.vehicles) then
        for i=1,#(tb.vehicles) do
            pb.pb_VehicleEncode(tb.vehicles[i], encoder:addsubmsg(10))
        end
    end
    if(tb.parts) then
        for i=1,#(tb.parts) do
            pb.pb_VehiclePartEncode(tb.parts[i], encoder:addsubmsg(11))
        end
    end
    if(tb.weapon_lvls) then
        for i=1,#(tb.weapon_lvls) do
            pb.pb_MPLevelsEncode(tb.weapon_lvls[i], encoder:addsubmsg(12))
        end
    end
    if(tb.account_lvls) then    pb.pb_MPLevelsEncode(tb.account_lvls, encoder:addsubmsg(13))    end
    if(tb.double_account_exp_rate) then    encoder:addu32(14, tb.double_account_exp_rate)    end
    if(tb.double_rank_score_rate) then    encoder:addu32(15, tb.double_rank_score_rate)    end
    if(tb.preset_data) then
        for i=1,#(tb.preset_data) do
            pb.pb_MPPresetCSEncode(tb.preset_data[i], encoder:addsubmsg(16))
        end
    end
    if(tb.armed_store) then
        for i=1,#(tb.armed_store) do
            pb.pb_PropInfoEncode(tb.armed_store[i], encoder:addsubmsg(17))
        end
    end
    if(tb.forbidden_prop) then    encoder:addu64(18, tb.forbidden_prop)    end
    if(tb.prop_data) then    pb.pb_TDMNumeralPropEncode(tb.prop_data, encoder:addsubmsg(19))    end
    if(tb.allow_prop) then    encoder:addu64(20, tb.allow_prop)    end
    if(tb.vehicle_rules) then
        for i=1,#(tb.vehicle_rules) do
            pb.pb_VehicleModificationRuleEncode(tb.vehicle_rules[i], encoder:addsubmsg(21))
        end
    end
    if(tb.orgin_commander_score) then    encoder:addi64(22, tb.orgin_commander_score)    end
end

function pb.pb_TDMWeaponDesignDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMWeaponDesign) or {} 
    tb.designs = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __design_name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __design_name ~= "" then tb.design_name = __design_name end
    local __slot_index = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __slot_index ~= 0 then tb.slot_index = __slot_index end
    local __available = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __available ~= false then tb.available = __available end
    return tb
end

function pb.pb_TDMWeaponDesignEncode(tb, encoder)
    if(tb.designs) then    pb.pb_PropInfoEncode(tb.designs, encoder:addsubmsg(1))    end
    if(tb.design_name) then    encoder:addstr(2, tb.design_name)    end
    if(tb.slot_index) then    encoder:addi32(3, tb.slot_index)    end
    if(tb.available) then    encoder:addbool(4, tb.available)    end
end

function pb.pb_TDMNumeralPropDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMNumeralProp) or {} 
    tb.weapon_store = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.weapon_store[k] = pb.pb_PropInfoDecode(v)
    end
    tb.weapon_designs = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.weapon_designs[k] = pb.pb_TDMWeaponDesignDecode(v)
    end
    tb.weapon_store_bytes = decoder:getstrary(3)
    tb.weapon_designs_bytes = decoder:getstrary(4)
    return tb
end

function pb.pb_TDMNumeralPropEncode(tb, encoder)
    if(tb.weapon_store) then
        for i=1,#(tb.weapon_store) do
            pb.pb_PropInfoEncode(tb.weapon_store[i], encoder:addsubmsg(1))
        end
    end
    if(tb.weapon_designs) then
        for i=1,#(tb.weapon_designs) do
            pb.pb_TDMWeaponDesignEncode(tb.weapon_designs[i], encoder:addsubmsg(2))
        end
    end
    if(tb.weapon_store_bytes) then    encoder:addbuffer(3, tb.weapon_store_bytes)    end
    if(tb.weapon_designs_bytes) then    encoder:addbuffer(4, tb.weapon_designs_bytes)    end
end

function pb.pb_TDMCarryoutBagDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMCarryoutBag) or {} 
    local __bag_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __bag_id ~= 0 then tb.bag_id = __bag_id end
    local __armed_prop_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __armed_prop_id ~= 0 then tb.armed_prop_id = __armed_prop_id end
    return tb
end

function pb.pb_TDMCarryoutBagEncode(tb, encoder)
    if(tb.bag_id) then    encoder:addu64(1, tb.bag_id)    end
    if(tb.armed_prop_id) then    encoder:addu64(2, tb.armed_prop_id)    end
end

function pb.pb_TDMTournamentScoreDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMTournamentScore) or {} 
    local __id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __score = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __result = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __value_a = decoder:getdouble(4)
    if not PB_USE_DEFAULT_TABLE or __value_a ~= 0 then tb.value_a = __value_a end
    local __value_b = decoder:getdouble(5)
    if not PB_USE_DEFAULT_TABLE or __value_b ~= 0 then tb.value_b = __value_b end
    local __value_c = decoder:getdouble(6)
    if not PB_USE_DEFAULT_TABLE or __value_c ~= 0 then tb.value_c = __value_c end
    local __value_d = decoder:getdouble(7)
    if not PB_USE_DEFAULT_TABLE or __value_d ~= 0 then tb.value_d = __value_d end
    local __value_e = decoder:getdouble(8)
    if not PB_USE_DEFAULT_TABLE or __value_e ~= 0 then tb.value_e = __value_e end
    return tb
end

function pb.pb_TDMTournamentScoreEncode(tb, encoder)
    if(tb.id) then    encoder:addu32(1, tb.id)    end
    if(tb.score) then    encoder:addi32(2, tb.score)    end
    if(tb.result) then    encoder:addi32(3, tb.result)    end
    if(tb.value_a) then    encoder:adddouble(4, tb.value_a)    end
    if(tb.value_b) then    encoder:adddouble(5, tb.value_b)    end
    if(tb.value_c) then    encoder:adddouble(6, tb.value_c)    end
    if(tb.value_d) then    encoder:adddouble(7, tb.value_d)    end
    if(tb.value_e) then    encoder:adddouble(8, tb.value_e)    end
end

function pb.pb_TDMPointWeightDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMPointWeight) or {} 
    local __rule_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rule_id ~= 0 then tb.rule_id = __rule_id end
    local __weight = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __weight ~= 0 then tb.weight = __weight end
    local __total_weight = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __total_weight ~= 0 then tb.total_weight = __total_weight end
    local __drop_item_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __drop_item_id ~= 0 then tb.drop_item_id = __drop_item_id end
    local __is_drop = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_drop ~= false then tb.is_drop = __is_drop end
    local __val = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __val ~= 0 then tb.val = __val end
    local __after_weight = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __after_weight ~= 0 then tb.after_weight = __after_weight end
    local __before_weight = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __before_weight ~= 0 then tb.before_weight = __before_weight end
    local __is_award_succ = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __is_award_succ ~= false then tb.is_award_succ = __is_award_succ end
    return tb
end

function pb.pb_TDMPointWeightEncode(tb, encoder)
    if(tb.rule_id) then    encoder:addu32(1, tb.rule_id)    end
    if(tb.weight) then    encoder:addi64(2, tb.weight)    end
    if(tb.total_weight) then    encoder:addi64(3, tb.total_weight)    end
    if(tb.drop_item_id) then    encoder:addu64(4, tb.drop_item_id)    end
    if(tb.is_drop) then    encoder:addbool(5, tb.is_drop)    end
    if(tb.val) then    encoder:addi64(6, tb.val)    end
    if(tb.after_weight) then    encoder:addi64(7, tb.after_weight)    end
    if(tb.before_weight) then    encoder:addi64(8, tb.before_weight)    end
    if(tb.is_award_succ) then    encoder:addbool(9, tb.is_award_succ)    end
end

function pb.pb_TDMPlayerDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMPlayer) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.basic_info = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(2))
    local __team_mvp = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __team_mvp ~= false then tb.team_mvp = __team_mvp end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.kill_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.death_info = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.death_info[k] = pb.pb_DsPlayerDeathInfoDecode(v)
    end
    tb.stronghold_list = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.stronghold_list[k] = pb.pb_TDMStrongholdInfoDecode(v)
    end
    local __score = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.props[k] = pb.pb_EquipPositionDecode(v)
    end
    local __leave = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __leave ~= false then tb.leave = __leave end
    tb.equip_use_time = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.equip_use_time[k] = pb.pb_EquipUseTimeDecode(v)
    end
    local __dead_count = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __dead_count ~= 0 then tb.dead_count = __dead_count end
    local __killed_by_ai_count = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __killed_by_ai_count ~= 0 then tb.killed_by_ai_count = __killed_by_ai_count end
    local __killed_by_player_count = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __killed_by_player_count ~= 0 then tb.killed_by_player_count = __killed_by_player_count end
    local __fire_count = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __fire_count ~= 0 then tb.fire_count = __fire_count end
    local __break_count = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __break_count ~= 0 then tb.break_count = __break_count end
    local __capture_count = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __capture_count ~= 0 then tb.capture_count = __capture_count end
    local __machine_gun_use_count = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __machine_gun_use_count ~= 0 then tb.machine_gun_use_count = __machine_gun_use_count end
    local __ammo_box_use_count = decoder:getu32(19)
    if not PB_USE_DEFAULT_TABLE or __ammo_box_use_count ~= 0 then tb.ammo_box_use_count = __ammo_box_use_count end
    local __kill_ai_count = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_count ~= 0 then tb.kill_ai_count = __kill_ai_count end
    local __kill_player_count = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __kill_player_count ~= 0 then tb.kill_player_count = __kill_player_count end
    local __living_time = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __living_time ~= 0 then tb.living_time = __living_time end
    local __play_time = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __is_join_at_middle = decoder:getbool(24)
    if not PB_USE_DEFAULT_TABLE or __is_join_at_middle ~= false then tb.is_join_at_middle = __is_join_at_middle end
    tb.ai_info = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(25))
    local __assist_count = decoder:getu32(26)
    if not PB_USE_DEFAULT_TABLE or __assist_count ~= 0 then tb.assist_count = __assist_count end
    local __leave_type = decoder:getu32(27)
    if not PB_USE_DEFAULT_TABLE or __leave_type ~= 0 then tb.leave_type = __leave_type end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(28))
    tb.bags = {}
    for k,v in pairs(decoder:getsubmsgary(29)) do
        tb.bags[k] = pb.pb_TDMCarryoutBagDecode(v)
    end
    tb.game_achievements = {}
    for k,v in pairs(decoder:getsubmsgary(30)) do
        tb.game_achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    tb.game_medal = {}
    for k,v in pairs(decoder:getsubmsgary(31)) do
        tb.game_medal[k] = pb.pb_DsGameMedalDecode(v)
    end
    local __game_exp = decoder:geti64(32)
    if not PB_USE_DEFAULT_TABLE or __game_exp ~= 0 then tb.game_exp = __game_exp end
    tb.tournament_scores = {}
    for k,v in pairs(decoder:getsubmsgary(33)) do
        tb.tournament_scores[k] = pb.pb_TDMTournamentScoreDecode(v)
    end
    local __vehicle_kill = decoder:getu32(34)
    if not PB_USE_DEFAULT_TABLE or __vehicle_kill ~= 0 then tb.vehicle_kill = __vehicle_kill end
    local __match_before_elo_point = decoder:getfloat(35)
    if not PB_USE_DEFAULT_TABLE or __match_before_elo_point ~= 0 then tb.match_before_elo_point = __match_before_elo_point end
    local __double_account_exp_rate = decoder:getu32(36)
    if not PB_USE_DEFAULT_TABLE or __double_account_exp_rate ~= 0 then tb.double_account_exp_rate = __double_account_exp_rate end
    local __double_rank_score_rate = decoder:getu32(37)
    if not PB_USE_DEFAULT_TABLE or __double_rank_score_rate ~= 0 then tb.double_rank_score_rate = __double_rank_score_rate end
    local __account_add_exp_buf = decoder:getdouble(38)
    if not PB_USE_DEFAULT_TABLE or __account_add_exp_buf ~= 0 then tb.account_add_exp_buf = __account_add_exp_buf end
    tb.preset_cs_list = {}
    for k,v in pairs(decoder:getsubmsgary(39)) do
        tb.preset_cs_list[k] = pb.pb_MPPresetCSDecode(v)
    end
    tb.weapon_list = {}
    for k,v in pairs(decoder:getsubmsgary(40)) do
        tb.weapon_list[k] = pb.pb_MPWeaponSettlementDecode(v)
    end
    local __total_shoot = decoder:getu32(41)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu32(42)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_head_down = decoder:getu32(43)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __total_kill_long_range = decoder:getu32(44)
    if not PB_USE_DEFAULT_TABLE or __total_kill_long_range ~= 0 then tb.total_kill_long_range = __total_kill_long_range end
    local __total_capture_point_time = decoder:getu32(45)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point_time ~= 0 then tb.total_capture_point_time = __total_capture_point_time end
    local __total_vehicle_use_time = decoder:getu32(46)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_use_time ~= 0 then tb.total_vehicle_use_time = __total_vehicle_use_time end
    local __total_damage_to_vehicle = decoder:getu32(47)
    if not PB_USE_DEFAULT_TABLE or __total_damage_to_vehicle ~= 0 then tb.total_damage_to_vehicle = __total_damage_to_vehicle end
    local __total_vehicle_kill = decoder:getu32(48)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_kill ~= 0 then tb.total_vehicle_kill = __total_vehicle_kill end
    local __total_vehicle_destroyed = decoder:getu32(49)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_destroyed ~= 0 then tb.total_vehicle_destroyed = __total_vehicle_destroyed end
    local __total_vehicle_repair = decoder:getu32(50)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_repair ~= 0 then tb.total_vehicle_repair = __total_vehicle_repair end
    local __cybercafe_settlement_buff = decoder:getdouble(51)
    if not PB_USE_DEFAULT_TABLE or __cybercafe_settlement_buff ~= 0 then tb.cybercafe_settlement_buff = __cybercafe_settlement_buff end
    local __score_rank = decoder:getu32(52)
    if not PB_USE_DEFAULT_TABLE or __score_rank ~= 0 then tb.score_rank = __score_rank end
    local __is_half_join = decoder:getbool(53)
    if not PB_USE_DEFAULT_TABLE or __is_half_join ~= false then tb.is_half_join = __is_half_join end
    local __university_settlement_buff = decoder:getdouble(54)
    if not PB_USE_DEFAULT_TABLE or __university_settlement_buff ~= 0 then tb.university_settlement_buff = __university_settlement_buff end
    local __valid_assist_count = decoder:getu32(55)
    if not PB_USE_DEFAULT_TABLE or __valid_assist_count ~= 0 then tb.valid_assist_count = __valid_assist_count end
    local __capture_flag_num = decoder:geti32(56)
    if not PB_USE_DEFAULT_TABLE or __capture_flag_num ~= 0 then tb.capture_flag_num = __capture_flag_num end
    local __rescue_contrib = decoder:geti32(57)
    if not PB_USE_DEFAULT_TABLE or __rescue_contrib ~= 0 then tb.rescue_contrib = __rescue_contrib end
    local __build_and_destroy_contrib = decoder:geti32(58)
    if not PB_USE_DEFAULT_TABLE or __build_and_destroy_contrib ~= 0 then tb.build_and_destroy_contrib = __build_and_destroy_contrib end
    local __capture_contrib = decoder:geti32(59)
    if not PB_USE_DEFAULT_TABLE or __capture_contrib ~= 0 then tb.capture_contrib = __capture_contrib end
    local __tactics_contrib = decoder:geti32(60)
    if not PB_USE_DEFAULT_TABLE or __tactics_contrib ~= 0 then tb.tactics_contrib = __tactics_contrib end
    local __short_hang_up_triggered = decoder:geti32(61)
    if not PB_USE_DEFAULT_TABLE or __short_hang_up_triggered ~= 0 then tb.short_hang_up_triggered = __short_hang_up_triggered end
    local __commander_contributor_title = decoder:geti32(62)
    if not PB_USE_DEFAULT_TABLE or __commander_contributor_title ~= 0 then tb.commander_contributor_title = __commander_contributor_title end
    local __double_rank_act_rate = decoder:getu32(63)
    if not PB_USE_DEFAULT_TABLE or __double_rank_act_rate ~= 0 then tb.double_rank_act_rate = __double_rank_act_rate end
    local __join_at_middle_time = decoder:getu32(64)
    if not PB_USE_DEFAULT_TABLE or __join_at_middle_time ~= 0 then tb.join_at_middle_time = __join_at_middle_time end
    tb.double_rank_info = pb.pb_TournamentRankDoubleInfoDecode(decoder:getsubmsg(65))
    local __sys_team_id = decoder:getu64(67)
    if not PB_USE_DEFAULT_TABLE or __sys_team_id ~= 0 then tb.sys_team_id = __sys_team_id end
    local __vehicle_kill_count = decoder:geti32(68)
    if not PB_USE_DEFAULT_TABLE or __vehicle_kill_count ~= 0 then tb.vehicle_kill_count = __vehicle_kill_count end
    local __vehicle_play_time = decoder:geti32(69)
    if not PB_USE_DEFAULT_TABLE or __vehicle_play_time ~= 0 then tb.vehicle_play_time = __vehicle_play_time end
    local __infantry_kill_count = decoder:geti32(70)
    if not PB_USE_DEFAULT_TABLE or __infantry_kill_count ~= 0 then tb.infantry_kill_count = __infantry_kill_count end
    local __infantry_play_time = decoder:geti32(71)
    if not PB_USE_DEFAULT_TABLE or __infantry_play_time ~= 0 then tb.infantry_play_time = __infantry_play_time end
    tb.od_player = pb.pb_OdTdmPlayerDecode(decoder:getsubmsg(100))
    return tb
end

function pb.pb_TDMPlayerEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.basic_info) then    pb.pb_DsPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.team_mvp) then    encoder:addbool(3, tb.team_mvp)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(5))
        end
    end
    if(tb.death_info) then
        for i=1,#(tb.death_info) do
            pb.pb_DsPlayerDeathInfoEncode(tb.death_info[i], encoder:addsubmsg(6))
        end
    end
    if(tb.stronghold_list) then
        for i=1,#(tb.stronghold_list) do
            pb.pb_TDMStrongholdInfoEncode(tb.stronghold_list[i], encoder:addsubmsg(7))
        end
    end
    if(tb.score) then    encoder:addi32(8, tb.score)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_EquipPositionEncode(tb.props[i], encoder:addsubmsg(9))
        end
    end
    if(tb.leave) then    encoder:addbool(10, tb.leave)    end
    if(tb.equip_use_time) then
        for i=1,#(tb.equip_use_time) do
            pb.pb_EquipUseTimeEncode(tb.equip_use_time[i], encoder:addsubmsg(11))
        end
    end
    if(tb.dead_count) then    encoder:addu32(12, tb.dead_count)    end
    if(tb.killed_by_ai_count) then    encoder:addu32(13, tb.killed_by_ai_count)    end
    if(tb.killed_by_player_count) then    encoder:addu32(14, tb.killed_by_player_count)    end
    if(tb.fire_count) then    encoder:addu32(15, tb.fire_count)    end
    if(tb.break_count) then    encoder:addu32(16, tb.break_count)    end
    if(tb.capture_count) then    encoder:addu32(17, tb.capture_count)    end
    if(tb.machine_gun_use_count) then    encoder:addu32(18, tb.machine_gun_use_count)    end
    if(tb.ammo_box_use_count) then    encoder:addu32(19, tb.ammo_box_use_count)    end
    if(tb.kill_ai_count) then    encoder:addu32(20, tb.kill_ai_count)    end
    if(tb.kill_player_count) then    encoder:addu32(21, tb.kill_player_count)    end
    if(tb.living_time) then    encoder:addu32(22, tb.living_time)    end
    if(tb.play_time) then    encoder:addi32(23, tb.play_time)    end
    if(tb.is_join_at_middle) then    encoder:addbool(24, tb.is_join_at_middle)    end
    if(tb.ai_info) then    pb.pb_DsAIBasicInfoEncode(tb.ai_info, encoder:addsubmsg(25))    end
    if(tb.assist_count) then    encoder:addu32(26, tb.assist_count)    end
    if(tb.leave_type) then    encoder:addu32(27, tb.leave_type)    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(28))    end
    if(tb.bags) then
        for i=1,#(tb.bags) do
            pb.pb_TDMCarryoutBagEncode(tb.bags[i], encoder:addsubmsg(29))
        end
    end
    if(tb.game_achievements) then
        for i=1,#(tb.game_achievements) do
            pb.pb_DsGameAchievementEncode(tb.game_achievements[i], encoder:addsubmsg(30))
        end
    end
    if(tb.game_medal) then
        for i=1,#(tb.game_medal) do
            pb.pb_DsGameMedalEncode(tb.game_medal[i], encoder:addsubmsg(31))
        end
    end
    if(tb.game_exp) then    encoder:addi64(32, tb.game_exp)    end
    if(tb.tournament_scores) then
        for i=1,#(tb.tournament_scores) do
            pb.pb_TDMTournamentScoreEncode(tb.tournament_scores[i], encoder:addsubmsg(33))
        end
    end
    if(tb.vehicle_kill) then    encoder:addu32(34, tb.vehicle_kill)    end
    if(tb.match_before_elo_point) then    encoder:addfloat(35, tb.match_before_elo_point)    end
    if(tb.double_account_exp_rate) then    encoder:addu32(36, tb.double_account_exp_rate)    end
    if(tb.double_rank_score_rate) then    encoder:addu32(37, tb.double_rank_score_rate)    end
    if(tb.account_add_exp_buf) then    encoder:adddouble(38, tb.account_add_exp_buf)    end
    if(tb.preset_cs_list) then
        for i=1,#(tb.preset_cs_list) do
            pb.pb_MPPresetCSEncode(tb.preset_cs_list[i], encoder:addsubmsg(39))
        end
    end
    if(tb.weapon_list) then
        for i=1,#(tb.weapon_list) do
            pb.pb_MPWeaponSettlementEncode(tb.weapon_list[i], encoder:addsubmsg(40))
        end
    end
    if(tb.total_shoot) then    encoder:addu32(41, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu32(42, tb.total_shoot_hit)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(43, tb.total_shoot_head_down)    end
    if(tb.total_kill_long_range) then    encoder:addu32(44, tb.total_kill_long_range)    end
    if(tb.total_capture_point_time) then    encoder:addu32(45, tb.total_capture_point_time)    end
    if(tb.total_vehicle_use_time) then    encoder:addu32(46, tb.total_vehicle_use_time)    end
    if(tb.total_damage_to_vehicle) then    encoder:addu32(47, tb.total_damage_to_vehicle)    end
    if(tb.total_vehicle_kill) then    encoder:addu32(48, tb.total_vehicle_kill)    end
    if(tb.total_vehicle_destroyed) then    encoder:addu32(49, tb.total_vehicle_destroyed)    end
    if(tb.total_vehicle_repair) then    encoder:addu32(50, tb.total_vehicle_repair)    end
    if(tb.cybercafe_settlement_buff) then    encoder:adddouble(51, tb.cybercafe_settlement_buff)    end
    if(tb.score_rank) then    encoder:addu32(52, tb.score_rank)    end
    if(tb.is_half_join) then    encoder:addbool(53, tb.is_half_join)    end
    if(tb.university_settlement_buff) then    encoder:adddouble(54, tb.university_settlement_buff)    end
    if(tb.valid_assist_count) then    encoder:addu32(55, tb.valid_assist_count)    end
    if(tb.capture_flag_num) then    encoder:addi32(56, tb.capture_flag_num)    end
    if(tb.rescue_contrib) then    encoder:addi32(57, tb.rescue_contrib)    end
    if(tb.build_and_destroy_contrib) then    encoder:addi32(58, tb.build_and_destroy_contrib)    end
    if(tb.capture_contrib) then    encoder:addi32(59, tb.capture_contrib)    end
    if(tb.tactics_contrib) then    encoder:addi32(60, tb.tactics_contrib)    end
    if(tb.short_hang_up_triggered) then    encoder:addi32(61, tb.short_hang_up_triggered)    end
    if(tb.commander_contributor_title) then    encoder:addi32(62, tb.commander_contributor_title)    end
    if(tb.double_rank_act_rate) then    encoder:addu32(63, tb.double_rank_act_rate)    end
    if(tb.join_at_middle_time) then    encoder:addu32(64, tb.join_at_middle_time)    end
    if(tb.double_rank_info) then    pb.pb_TournamentRankDoubleInfoEncode(tb.double_rank_info, encoder:addsubmsg(65))    end
    if(tb.sys_team_id) then    encoder:addu64(67, tb.sys_team_id)    end
    if(tb.vehicle_kill_count) then    encoder:addi32(68, tb.vehicle_kill_count)    end
    if(tb.vehicle_play_time) then    encoder:addi32(69, tb.vehicle_play_time)    end
    if(tb.infantry_kill_count) then    encoder:addi32(70, tb.infantry_kill_count)    end
    if(tb.infantry_play_time) then    encoder:addi32(71, tb.infantry_play_time)    end
    if(tb.od_player) then    pb.pb_OdTdmPlayerEncode(tb.od_player, encoder:addsubmsg(100))    end
end

function pb.pb_DsGameMedalDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsGameMedal) or {} 
    local __id = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __level = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __score = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    return tb
end

function pb.pb_DsGameMedalEncode(tb, encoder)
    if(tb.id) then    encoder:addi64(1, tb.id)    end
    if(tb.level) then    encoder:addi32(2, tb.level)    end
    if(tb.score) then    encoder:addi64(3, tb.score)    end
end

function pb.pb_TDMPlayerMVPEvaluatedDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMPlayerMVPEvaluated) or {} 
    local __dimension = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __dimension ~= 0 then tb.dimension = __dimension end
    local __value = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_TDMPlayerMVPEvaluatedEncode(tb, encoder)
    if(tb.dimension) then    encoder:addi32(1, tb.dimension)    end
    if(tb.value) then    encoder:addi32(2, tb.value)    end
end

function pb.pb_TDMStrongholdInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMStrongholdInfo) or {} 
    local __stronghold_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __stronghold_id ~= 0 then tb.stronghold_id = __stronghold_id end
    return tb
end

function pb.pb_TDMStrongholdInfoEncode(tb, encoder)
    if(tb.stronghold_id) then    encoder:addu64(1, tb.stronghold_id)    end
end

function pb.pb_TDMVehicleAddExpDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMVehicleAddExp) or {} 
    local __vehicle_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __vehicle_id ~= 0 then tb.vehicle_id = __vehicle_id end
    tb.exp_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.exp_list[k] = pb.pb_TDMVehicleExpDecode(v)
    end
    tb.unlock_part_list = decoder:getu64ary(3)
    return tb
end

function pb.pb_TDMVehicleAddExpEncode(tb, encoder)
    if(tb.vehicle_id) then    encoder:addu64(1, tb.vehicle_id)    end
    if(tb.exp_list) then
        for i=1,#(tb.exp_list) do
            pb.pb_TDMVehicleExpEncode(tb.exp_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.unlock_part_list) then    encoder:addu64(3, tb.unlock_part_list)    end
end

function pb.pb_TDMVehicleExpDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TDMVehicleExp) or {} 
    local __reason = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    return tb
end

function pb.pb_TDMVehicleExpEncode(tb, encoder)
    if(tb.reason) then    encoder:addu32(1, tb.reason)    end
    if(tb.exp) then    encoder:addi64(2, tb.exp)    end
end

function pb.pb_TgLogEntryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TgLogEntry) or {} 
    local __name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __pbtlog = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __pbtlog ~= "" then tb.pbtlog = __pbtlog end
    local __no_autofill = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __no_autofill ~= false then tb.no_autofill = __no_autofill end
    return tb
end

function pb.pb_TgLogEntryEncode(tb, encoder)
    if(tb.name) then    encoder:addstr(1, tb.name)    end
    if(tb.pbtlog) then    encoder:addbuffer(2, tb.pbtlog)    end
    if(tb.no_autofill) then    encoder:addbool(3, tb.no_autofill)    end
end

function pb.pb_TssTLogHeaderDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TssTLogHeader) or {} 
    tb.secTLogHeadComm = pb.pb_SecTLogHeadCommDecode(decoder:getsubmsg(4))
    tb.secTLogHeadDevice = pb.pb_SecTLogHeadDeviceDecode(decoder:getsubmsg(7))
    tb.secTLogHeadRole = pb.pb_SecTLogHeadRoleDecode(decoder:getsubmsg(8))
    return tb
end

function pb.pb_TssTLogHeaderEncode(tb, encoder)
    if(tb.secTLogHeadComm) then    pb.pb_SecTLogHeadCommEncode(tb.secTLogHeadComm, encoder:addsubmsg(4))    end
    if(tb.secTLogHeadDevice) then    pb.pb_SecTLogHeadDeviceEncode(tb.secTLogHeadDevice, encoder:addsubmsg(7))    end
    if(tb.secTLogHeadRole) then    pb.pb_SecTLogHeadRoleEncode(tb.secTLogHeadRole, encoder:addsubmsg(8))    end
end

function pb.pb_VehicleDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Vehicle) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __level = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    tb.slots = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.slots[k] = pb.pb_VehicleSlotDecode(v)
    end
    local __skin = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __skin ~= 0 then tb.skin = __skin end
    return tb
end

function pb.pb_VehicleEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.exp) then    encoder:addi64(2, tb.exp)    end
    if(tb.level) then    encoder:addu32(3, tb.level)    end
    if(tb.slots) then
        for i=1,#(tb.slots) do
            pb.pb_VehicleSlotEncode(tb.slots[i], encoder:addsubmsg(4))
        end
    end
    if(tb.skin) then    encoder:addu64(5, tb.skin)    end
end

function pb.pb_VehiclePartDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_VehiclePart) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    return tb
end

function pb.pb_VehiclePartEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
end

function pb.pb_MarkPropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MarkPropInfo) or {} 
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __mark_kind = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __mark_kind ~= 0 then tb.mark_kind = __mark_kind end
    local __mark_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __mark_num ~= 0 then tb.mark_num = __mark_num end
    local __change_time = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __change_time ~= 0 then tb.change_time = __change_time end
    return tb
end

function pb.pb_MarkPropInfoEncode(tb, encoder)
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(1))    end
    if(tb.mark_kind) then    encoder:addi32(2, tb.mark_kind)    end
    if(tb.mark_num) then    encoder:addi64(3, tb.mark_num)    end
    if(tb.change_time) then    encoder:addu64(4, tb.change_time)    end
end

function pb.pb_VehicleSlotDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_VehicleSlot) or {} 
    local __slot = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __slot ~= 0 then tb.slot = __slot end
    tb.part = pb.pb_VehiclePartDecode(decoder:getsubmsg(2))
    local __part_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __part_id ~= 0 then tb.part_id = __part_id end
    return tb
end

function pb.pb_VehicleSlotEncode(tb, encoder)
    if(tb.slot) then    encoder:addu32(1, tb.slot)    end
    if(tb.part) then    pb.pb_VehiclePartEncode(tb.part, encoder:addsubmsg(2))    end
    if(tb.part_id) then    encoder:addu64(3, tb.part_id)    end
end

function pb.pb_WeaponAttrInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponAttrInfo) or {} 
    local __tune_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __tune_id ~= 0 then tb.tune_id = __tune_id end
    local __value = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_WeaponAttrInfoEncode(tb, encoder)
    if(tb.tune_id) then    encoder:addu64(1, tb.tune_id)    end
    if(tb.value) then    encoder:addfloat(2, tb.value)    end
end

function pb.pb_MPSkinComponentsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPSkinComponents) or {} 
    local __skin_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __skin_id ~= 0 then tb.skin_id = __skin_id end
    tb.components = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.components[k] = pb.pb_PropInfoDecode(v)
    end
    tb.component_ids = decoder:getu64ary(3)
    return tb
end

function pb.pb_MPSkinComponentsEncode(tb, encoder)
    if(tb.skin_id) then    encoder:addu64(1, tb.skin_id)    end
    if(tb.components) then
        for i=1,#(tb.components) do
            pb.pb_PropInfoEncode(tb.components[i], encoder:addsubmsg(2))
        end
    end
    if(tb.component_ids) then    encoder:addu64(3, tb.component_ids)    end
end

function pb.pb_WeaponChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponChange) or {} 
    local __ID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ID ~= 0 then tb.ID = __ID end
    local __old_exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __old_exp ~= 0 then tb.old_exp = __old_exp end
    local __exp = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    tb.unlock_componets = decoder:getu64ary(4)
    tb.detail = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.detail[k] = pb.pb_WeaponExpAddDecode(v)
    end
    local __is_reach_add_max = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __is_reach_add_max ~= false then tb.is_reach_add_max = __is_reach_add_max end
    local __is_level_up = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_level_up ~= false then tb.is_level_up = __is_level_up end
    tb.converted_expcard = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.converted_expcard[k] = pb.pb_PropInfoDecode(v)
    end
    local __new_skin = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __new_skin ~= 0 then tb.new_skin = __new_skin end
    tb.unlock_componets_prop = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.unlock_componets_prop[k] = pb.pb_PropInfoDecode(v)
    end
    tb.skip_comps = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.skip_comps[k] = pb.pb_MPSkinComponentsDecode(v)
    end
    tb.global_comps = decoder:getu64ary(12)
    local __star_num = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __star_num ~= 0 then tb.star_num = __star_num end
    local __cur_kill_count = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __cur_kill_count ~= 0 then tb.cur_kill_count = __cur_kill_count end
    local __old_star_num = decoder:geti32(16)
    if not PB_USE_DEFAULT_TABLE or __old_star_num ~= 0 then tb.old_star_num = __old_star_num end
    local __old_kill_count = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __old_kill_count ~= 0 then tb.old_kill_count = __old_kill_count end
    return tb
end

function pb.pb_WeaponChangeEncode(tb, encoder)
    if(tb.ID) then    encoder:addu64(1, tb.ID)    end
    if(tb.old_exp) then    encoder:addi64(2, tb.old_exp)    end
    if(tb.exp) then    encoder:addi64(3, tb.exp)    end
    if(tb.unlock_componets) then    encoder:addu64(4, tb.unlock_componets)    end
    if(tb.detail) then
        for i=1,#(tb.detail) do
            pb.pb_WeaponExpAddEncode(tb.detail[i], encoder:addsubmsg(5))
        end
    end
    if(tb.is_reach_add_max) then    encoder:addbool(6, tb.is_reach_add_max)    end
    if(tb.is_level_up) then    encoder:addbool(7, tb.is_level_up)    end
    if(tb.converted_expcard) then
        for i=1,#(tb.converted_expcard) do
            pb.pb_PropInfoEncode(tb.converted_expcard[i], encoder:addsubmsg(9))
        end
    end
    if(tb.new_skin) then    encoder:addu64(10, tb.new_skin)    end
    if(tb.unlock_componets_prop) then
        for i=1,#(tb.unlock_componets_prop) do
            pb.pb_PropInfoEncode(tb.unlock_componets_prop[i], encoder:addsubmsg(8))
        end
    end
    if(tb.skip_comps) then
        for i=1,#(tb.skip_comps) do
            pb.pb_MPSkinComponentsEncode(tb.skip_comps[i], encoder:addsubmsg(11))
        end
    end
    if(tb.global_comps) then    encoder:addu64(12, tb.global_comps)    end
    if(tb.star_num) then    encoder:addi32(13, tb.star_num)    end
    if(tb.cur_kill_count) then    encoder:addi32(15, tb.cur_kill_count)    end
    if(tb.old_star_num) then    encoder:addi32(16, tb.old_star_num)    end
    if(tb.old_kill_count) then    encoder:addi32(17, tb.old_kill_count)    end
end

function pb.pb_WeaponExpAddDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponExpAdd) or {} 
    local __ID = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ID ~= 0 then tb.ID = __ID end
    local __add_exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __add_exp ~= 0 then tb.add_exp = __add_exp end
    local __source = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    return tb
end

function pb.pb_WeaponExpAddEncode(tb, encoder)
    if(tb.ID) then    encoder:addu64(1, tb.ID)    end
    if(tb.add_exp) then    encoder:addi64(2, tb.add_exp)    end
    if(tb.source) then    encoder:addi32(3, tb.source)    end
end

function pb.pb_WeaponStarInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponStarInfo) or {} 
    local __star_num = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __star_num ~= 0 then tb.star_num = __star_num end
    local __cur_kill_count = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __cur_kill_count ~= 0 then tb.cur_kill_count = __cur_kill_count end
    local __total_kill_count = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __total_kill_count ~= 0 then tb.total_kill_count = __total_kill_count end
    return tb
end

function pb.pb_WeaponStarInfoEncode(tb, encoder)
    if(tb.star_num) then    encoder:addi32(1, tb.star_num)    end
    if(tb.cur_kill_count) then    encoder:addi32(3, tb.cur_kill_count)    end
    if(tb.total_kill_count) then    encoder:addi32(4, tb.total_kill_count)    end
end

function pb.pb_WeaponInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponInfo) or {} 
    tb.equipped_styles = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.equipped_styles[k] = pb.pb_WeaponStyleInfoDecode(v)
    end
    tb.equipped_perk = pb.pb_PerkInfoDecode(decoder:getsubmsg(3))
    local __exp = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __sub_preset_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __sub_preset_id ~= 0 then tb.sub_preset_id = __sub_preset_id end
    local __skin_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __skin_id ~= 0 then tb.skin_id = __skin_id end
    tb.attr_set = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.attr_set[k] = pb.pb_WeaponAttrInfoDecode(v)
    end
    local __magazine_capacity = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __magazine_capacity ~= 0 then tb.magazine_capacity = __magazine_capacity end
    tb.load_bullets = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.load_bullets[k] = pb.pb_PropInfoDecode(v)
    end
    local __skin_gid = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __skin_gid ~= 0 then tb.skin_gid = __skin_gid end
    local __pendant_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __pendant_id ~= 0 then tb.pendant_id = __pendant_id end
    local __pendant_gid = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __pendant_gid ~= 0 then tb.pendant_gid = __pendant_gid end
    tb.weapon_star_info = pb.pb_WeaponStarInfoDecode(decoder:getsubmsg(13))
    return tb
end

function pb.pb_WeaponInfoEncode(tb, encoder)
    if(tb.equipped_styles) then
        for i=1,#(tb.equipped_styles) do
            pb.pb_WeaponStyleInfoEncode(tb.equipped_styles[i], encoder:addsubmsg(1))
        end
    end
    if(tb.equipped_perk) then    pb.pb_PerkInfoEncode(tb.equipped_perk, encoder:addsubmsg(3))    end
    if(tb.exp) then    encoder:addi64(4, tb.exp)    end
    if(tb.sub_preset_id) then    encoder:addu64(5, tb.sub_preset_id)    end
    if(tb.skin_id) then    encoder:addu64(6, tb.skin_id)    end
    if(tb.attr_set) then
        for i=1,#(tb.attr_set) do
            pb.pb_WeaponAttrInfoEncode(tb.attr_set[i], encoder:addsubmsg(7))
        end
    end
    if(tb.magazine_capacity) then    encoder:addi64(8, tb.magazine_capacity)    end
    if(tb.load_bullets) then
        for i=1,#(tb.load_bullets) do
            pb.pb_PropInfoEncode(tb.load_bullets[i], encoder:addsubmsg(9))
        end
    end
    if(tb.skin_gid) then    encoder:addu64(10, tb.skin_gid)    end
    if(tb.pendant_id) then    encoder:addu64(11, tb.pendant_id)    end
    if(tb.pendant_gid) then    encoder:addu64(12, tb.pendant_gid)    end
    if(tb.weapon_star_info) then    pb.pb_WeaponStarInfoEncode(tb.weapon_star_info, encoder:addsubmsg(13))    end
end

function pb.pb_WeaponSkinInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponSkinInfo) or {} 
    local __skin_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __skin_id ~= 0 then tb.skin_id = __skin_id end
    local __color = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __color ~= 0 then tb.color = __color end
    tb.funcs = decoder:getu64ary(3)
    tb.unlocked_funcs = decoder:getu64ary(4)
    local __transmog = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __transmog ~= false then tb.transmog = __transmog end
    local __skin_gid = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __skin_gid ~= 0 then tb.skin_gid = __skin_gid end
    return tb
end

function pb.pb_WeaponSkinInfoEncode(tb, encoder)
    if(tb.skin_id) then    encoder:addu64(1, tb.skin_id)    end
    if(tb.color) then    encoder:addu64(2, tb.color)    end
    if(tb.funcs) then    encoder:addu64(3, tb.funcs)    end
    if(tb.unlocked_funcs) then    encoder:addu64(4, tb.unlocked_funcs)    end
    if(tb.transmog) then    encoder:addbool(5, tb.transmog)    end
    if(tb.skin_gid) then    encoder:addu64(6, tb.skin_gid)    end
end

function pb.pb_WeaponSkinSetupDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponSkinSetup) or {} 
    local __weapon_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    local __skin_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __skin_id ~= 0 then tb.skin_id = __skin_id end
    local __skin_gid = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __skin_gid ~= 0 then tb.skin_gid = __skin_gid end
    local __pendant_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __pendant_id ~= 0 then tb.pendant_id = __pendant_id end
    local __pendant_gid = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __pendant_gid ~= 0 then tb.pendant_gid = __pendant_gid end
    return tb
end

function pb.pb_WeaponSkinSetupEncode(tb, encoder)
    if(tb.weapon_id) then    encoder:addu64(1, tb.weapon_id)    end
    if(tb.skin_id) then    encoder:addu64(2, tb.skin_id)    end
    if(tb.skin_gid) then    encoder:addu64(3, tb.skin_gid)    end
    if(tb.pendant_id) then    encoder:addu64(4, tb.pendant_id)    end
    if(tb.pendant_gid) then    encoder:addu64(5, tb.pendant_gid)    end
end

function pb.pb_WeaponStyleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponStyleInfo) or {} 
    local __style_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __style_id ~= 0 then tb.style_id = __style_id end
    local __slot_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __slot_id ~= 0 then tb.slot_id = __slot_id end
    return tb
end

function pb.pb_WeaponStyleInfoEncode(tb, encoder)
    if(tb.style_id) then    encoder:addu32(1, tb.style_id)    end
    if(tb.slot_id) then    encoder:addu32(2, tb.slot_id)    end
end

function pb.pb_WorldActorContentDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WorldActorContent) or {} 
    local __content_id = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __content_id ~= "" then tb.content_id = __content_id end
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_WorldActorContentEncode(tb, encoder)
    if(tb.content_id) then    encoder:addstr(1, tb.content_id)    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(2))    end
end

function pb.pb_WorldActorInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WorldActorInfo) or {} 
    local __actor_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __actor_id ~= 0 then tb.actor_id = __actor_id end
    local __state = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    tb.contents = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.contents[k] = pb.pb_WorldActorContentDecode(v)
    end
    return tb
end

function pb.pb_WorldActorInfoEncode(tb, encoder)
    if(tb.actor_id) then    encoder:addu64(1, tb.actor_id)    end
    if(tb.state) then    encoder:addu32(2, tb.state)    end
    if(tb.contents) then
        for i=1,#(tb.contents) do
            pb.pb_WorldActorContentEncode(tb.contents[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CsvTableRowDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CsvTableRow) or {} 
    tb.values = decoder:getstrary(1)
    return tb
end

function pb.pb_CsvTableRowEncode(tb, encoder)
    if(tb.values) then    encoder:addstr(1, tb.values)    end
end

function pb.pb_CsvTableDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CsvTableData) or {} 
    local __csv_name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __csv_name ~= "" then tb.csv_name = __csv_name end
    tb.fields = decoder:getstrary(2)
    tb.lines = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.lines[k] = pb.pb_CsvTableRowDecode(v)
    end
    return tb
end

function pb.pb_CsvTableDataEncode(tb, encoder)
    if(tb.csv_name) then    encoder:addstr(1, tb.csv_name)    end
    if(tb.fields) then    encoder:addstr(2, tb.fields)    end
    if(tb.lines) then
        for i=1,#(tb.lines) do
            pb.pb_CsvTableRowEncode(tb.lines[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CsvZipTableDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CsvZipTable) or {} 
    local __compress = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __compress ~= false then tb.compress = __compress end
    local __csv = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __csv ~= "" then tb.csv = __csv end
    local __csv_name = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __csv_name ~= "" then tb.csv_name = __csv_name end
    local __hash = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __hash ~= "" then tb.hash = __hash end
    local __origin_csv_size = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __origin_csv_size ~= 0 then tb.origin_csv_size = __origin_csv_size end
    return tb
end

function pb.pb_CsvZipTableEncode(tb, encoder)
    if(tb.compress) then    encoder:addbool(1, tb.compress)    end
    if(tb.csv) then    encoder:addbuffer(2, tb.csv)    end
    if(tb.csv_name) then    encoder:addstr(3, tb.csv_name)    end
    if(tb.hash) then    encoder:addstr(4, tb.hash)    end
    if(tb.origin_csv_size) then    encoder:addi32(5, tb.origin_csv_size)    end
end

function pb.pb_CsvZipPkgDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CsvZipPkg) or {} 
    tb.tables = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.tables[k] = pb.pb_CsvZipTableDecode(v)
    end
    local __seq = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __seq ~= 0 then tb.seq = __seq end
    local __idx = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __idx ~= 0 then tb.idx = __idx end
    local __total = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total ~= 0 then tb.total = __total end
    return tb
end

function pb.pb_CsvZipPkgEncode(tb, encoder)
    if(tb.tables) then
        for i=1,#(tb.tables) do
            pb.pb_CsvZipTableEncode(tb.tables[i], encoder:addsubmsg(1))
        end
    end
    if(tb.seq) then    encoder:addu64(2, tb.seq)    end
    if(tb.idx) then    encoder:addu32(3, tb.idx)    end
    if(tb.total) then    encoder:addu32(4, tb.total)    end
end

function pb.pb_CsvNumeralDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CsvNumeralData) or {} 
    tb.tables = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.tables[k] = pb.pb_CsvTableDataDecode(v)
    end
    tb.zip_tables = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.zip_tables[k] = pb.pb_CsvZipTableDecode(v)
    end
    return tb
end

function pb.pb_CsvNumeralDataEncode(tb, encoder)
    if(tb.tables) then
        for i=1,#(tb.tables) do
            pb.pb_CsvTableDataEncode(tb.tables[i], encoder:addsubmsg(1))
        end
    end
    if(tb.zip_tables) then
        for i=1,#(tb.zip_tables) do
            pb.pb_CsvZipTableEncode(tb.zip_tables[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_DsDeathDetailedInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsDeathDetailedInfo) or {} 
    local __helmet_item_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __helmet_item_id ~= 0 then tb.helmet_item_id = __helmet_item_id end
    local __helmet_health = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __helmet_health ~= 0 then tb.helmet_health = __helmet_health end
    local __helmet_health_max = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __helmet_health_max ~= 0 then tb.helmet_health_max = __helmet_health_max end
    local __helmet_src_health_max = decoder:getfloat(4)
    if not PB_USE_DEFAULT_TABLE or __helmet_src_health_max ~= 0 then tb.helmet_src_health_max = __helmet_src_health_max end
    local __breastplate_item_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __breastplate_item_id ~= 0 then tb.breastplate_item_id = __breastplate_item_id end
    local __breastplate_health = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __breastplate_health ~= 0 then tb.breastplate_health = __breastplate_health end
    local __breastplate_health_max = decoder:getfloat(7)
    if not PB_USE_DEFAULT_TABLE or __breastplate_health_max ~= 0 then tb.breastplate_health_max = __breastplate_health_max end
    local __breastplate_src_health_max = decoder:getfloat(8)
    if not PB_USE_DEFAULT_TABLE or __breastplate_src_health_max ~= 0 then tb.breastplate_src_health_max = __breastplate_src_health_max end
    local __maker_is_robot_ai = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __maker_is_robot_ai ~= false then tb.maker_is_robot_ai = __maker_is_robot_ai end
    local __maker_is_ailab_ai = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __maker_is_ailab_ai ~= false then tb.maker_is_ailab_ai = __maker_is_ailab_ai end
    local __maker_rank_match_score = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __maker_rank_match_score ~= 0 then tb.maker_rank_match_score = __maker_rank_match_score end
    tb.last_weapon_info = pb.pb_PropInfoDecode(decoder:getsubmsg(12))
    local __last_bullet_hp_damage = decoder:getfloat(13)
    if not PB_USE_DEFAULT_TABLE or __last_bullet_hp_damage ~= 0 then tb.last_bullet_hp_damage = __last_bullet_hp_damage end
    local __last_bullet_armor_damage = decoder:getfloat(14)
    if not PB_USE_DEFAULT_TABLE or __last_bullet_armor_damage ~= 0 then tb.last_bullet_armor_damage = __last_bullet_armor_damage end
    local __damage_part = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __damage_part ~= 0 then tb.damage_part = __damage_part end
    local __hero_id = decoder:getu64(16)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    return tb
end

function pb.pb_DsDeathDetailedInfoEncode(tb, encoder)
    if(tb.helmet_item_id) then    encoder:addu64(1, tb.helmet_item_id)    end
    if(tb.helmet_health) then    encoder:addfloat(2, tb.helmet_health)    end
    if(tb.helmet_health_max) then    encoder:addfloat(3, tb.helmet_health_max)    end
    if(tb.helmet_src_health_max) then    encoder:addfloat(4, tb.helmet_src_health_max)    end
    if(tb.breastplate_item_id) then    encoder:addu64(5, tb.breastplate_item_id)    end
    if(tb.breastplate_health) then    encoder:addfloat(6, tb.breastplate_health)    end
    if(tb.breastplate_health_max) then    encoder:addfloat(7, tb.breastplate_health_max)    end
    if(tb.breastplate_src_health_max) then    encoder:addfloat(8, tb.breastplate_src_health_max)    end
    if(tb.maker_is_robot_ai) then    encoder:addbool(9, tb.maker_is_robot_ai)    end
    if(tb.maker_is_ailab_ai) then    encoder:addbool(10, tb.maker_is_ailab_ai)    end
    if(tb.maker_rank_match_score) then    encoder:addi64(11, tb.maker_rank_match_score)    end
    if(tb.last_weapon_info) then    pb.pb_PropInfoEncode(tb.last_weapon_info, encoder:addsubmsg(12))    end
    if(tb.last_bullet_hp_damage) then    encoder:addfloat(13, tb.last_bullet_hp_damage)    end
    if(tb.last_bullet_armor_damage) then    encoder:addfloat(14, tb.last_bullet_armor_damage)    end
    if(tb.damage_part) then    encoder:addu32(15, tb.damage_part)    end
    if(tb.hero_id) then    encoder:addu64(16, tb.hero_id)    end
end

function pb.pb_DsHurtDetailedInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsHurtDetailedInfo) or {} 
    local __maker_player_uin = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __maker_player_uin ~= 0 then tb.maker_player_uin = __maker_player_uin end
    local __maker_player_name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __maker_player_name ~= "" then tb.maker_player_name = __maker_player_name end
    local __hp_damage = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __hp_damage ~= 0 then tb.hp_damage = __hp_damage end
    local __armor_damage = decoder:getfloat(4)
    if not PB_USE_DEFAULT_TABLE or __armor_damage ~= 0 then tb.armor_damage = __armor_damage end
    local __armor_item_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __armor_item_id ~= 0 then tb.armor_item_id = __armor_item_id end
    local __charater_part = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __charater_part ~= 0 then tb.charater_part = __charater_part end
    tb.character_parts = decoder:getu32ary(7)
    local __distance = decoder:getfloat(8)
    if not PB_USE_DEFAULT_TABLE or __distance ~= 0 then tb.distance = __distance end
    local __skill_id = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __skill_id ~= 0 then tb.skill_id = __skill_id end
    local __weapon_id = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    local __ammo_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __ammo_id ~= 0 then tb.ammo_id = __ammo_id end
    local __attacker_level = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __attacker_level ~= 0 then tb.attacker_level = __attacker_level end
    local __buff_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __buff_id ~= 0 then tb.buff_id = __buff_id end
    local __attacker_value_id = decoder:getu64(14)
    if not PB_USE_DEFAULT_TABLE or __attacker_value_id ~= 0 then tb.attacker_value_id = __attacker_value_id end
    local __bAttacker = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __bAttacker ~= false then tb.bAttacker = __bAttacker end
    local __taker_hero_id = decoder:getu64(17)
    if not PB_USE_DEFAULT_TABLE or __taker_hero_id ~= 0 then tb.taker_hero_id = __taker_hero_id end
    local __taker_player_name = decoder:getstr(18)
    if not PB_USE_DEFAULT_TABLE or __taker_player_name ~= "" then tb.taker_player_name = __taker_player_name end
    local __taker_health_percent = decoder:getfloat(19)
    if not PB_USE_DEFAULT_TABLE or __taker_health_percent ~= 0 then tb.taker_health_percent = __taker_health_percent end
    local __hp_damage_percent = decoder:getfloat(20)
    if not PB_USE_DEFAULT_TABLE or __hp_damage_percent ~= 0 then tb.hp_damage_percent = __hp_damage_percent end
    local __taker_max_health = decoder:getu64(21)
    if not PB_USE_DEFAULT_TABLE or __taker_max_health ~= 0 then tb.taker_max_health = __taker_max_health end
    local __taker_base_max_health = decoder:getfloat(22)
    if not PB_USE_DEFAULT_TABLE or __taker_base_max_health ~= 0 then tb.taker_base_max_health = __taker_base_max_health end
    local __is_penetrating_damage = decoder:getbool(23)
    if not PB_USE_DEFAULT_TABLE or __is_penetrating_damage ~= false then tb.is_penetrating_damage = __is_penetrating_damage end
    local __penetration_level_decrease = decoder:geti64(24)
    if not PB_USE_DEFAULT_TABLE or __penetration_level_decrease ~= 0 then tb.penetration_level_decrease = __penetration_level_decrease end
    local __taker_health_after = decoder:getfloat(25)
    if not PB_USE_DEFAULT_TABLE or __taker_health_after ~= 0 then tb.taker_health_after = __taker_health_after end
    return tb
end

function pb.pb_DsHurtDetailedInfoEncode(tb, encoder)
    if(tb.maker_player_uin) then    encoder:addu64(1, tb.maker_player_uin)    end
    if(tb.maker_player_name) then    encoder:addstr(2, tb.maker_player_name)    end
    if(tb.hp_damage) then    encoder:addfloat(3, tb.hp_damage)    end
    if(tb.armor_damage) then    encoder:addfloat(4, tb.armor_damage)    end
    if(tb.armor_item_id) then    encoder:addu64(5, tb.armor_item_id)    end
    if(tb.charater_part) then    encoder:addu32(6, tb.charater_part)    end
    if(tb.character_parts) then    encoder:addu32(7, tb.character_parts)    end
    if(tb.distance) then    encoder:addfloat(8, tb.distance)    end
    if(tb.skill_id) then    encoder:addu64(9, tb.skill_id)    end
    if(tb.weapon_id) then    encoder:addu64(10, tb.weapon_id)    end
    if(tb.ammo_id) then    encoder:addu64(11, tb.ammo_id)    end
    if(tb.attacker_level) then    encoder:addi64(12, tb.attacker_level)    end
    if(tb.buff_id) then    encoder:addu64(13, tb.buff_id)    end
    if(tb.attacker_value_id) then    encoder:addu64(14, tb.attacker_value_id)    end
    if(tb.bAttacker) then    encoder:addbool(16, tb.bAttacker)    end
    if(tb.taker_hero_id) then    encoder:addu64(17, tb.taker_hero_id)    end
    if(tb.taker_player_name) then    encoder:addstr(18, tb.taker_player_name)    end
    if(tb.taker_health_percent) then    encoder:addfloat(19, tb.taker_health_percent)    end
    if(tb.hp_damage_percent) then    encoder:addfloat(20, tb.hp_damage_percent)    end
    if(tb.taker_max_health) then    encoder:addu64(21, tb.taker_max_health)    end
    if(tb.taker_base_max_health) then    encoder:addfloat(22, tb.taker_base_max_health)    end
    if(tb.is_penetrating_damage) then    encoder:addbool(23, tb.is_penetrating_damage)    end
    if(tb.penetration_level_decrease) then    encoder:addi64(24, tb.penetration_level_decrease)    end
    if(tb.taker_health_after) then    encoder:addfloat(25, tb.taker_health_after)    end
end

function pb.pb_BhdMissionRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_BhdMissionRecord) or {} 
    local __chapter_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __chapter_id ~= 0 then tb.chapter_id = __chapter_id end
    local __rating = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __rating ~= 0 then tb.rating = __rating end
    local __time_cost = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __time_cost ~= 0 then tb.time_cost = __time_cost end
    local __progress = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __progress ~= 0 then tb.progress = __progress end
    local __member_num = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __member_num ~= 0 then tb.member_num = __member_num end
    local __finish_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __finish_time ~= 0 then tb.finish_time = __finish_time end
    local __rating_level = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __rating_level ~= 0 then tb.rating_level = __rating_level end
    local __kill_count = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __kill_count ~= 0 then tb.kill_count = __kill_count end
    local __damage = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __rescue_count = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __is_dropout = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __is_dropout ~= 0 then tb.is_dropout = __is_dropout end
    tb.achievements = decoder:geti64ary(13)
    local __loadout_id = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __loadout_id ~= 0 then tb.loadout_id = __loadout_id end
    local __role_id = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __role_id ~= 0 then tb.role_id = __role_id end
    return tb
end

function pb.pb_BhdMissionRecordEncode(tb, encoder)
    if(tb.chapter_id) then    encoder:addu32(2, tb.chapter_id)    end
    if(tb.rating) then    encoder:addu32(3, tb.rating)    end
    if(tb.time_cost) then    encoder:addu32(4, tb.time_cost)    end
    if(tb.progress) then    encoder:addu32(5, tb.progress)    end
    if(tb.member_num) then    encoder:addu32(6, tb.member_num)    end
    if(tb.finish_time) then    encoder:addi64(7, tb.finish_time)    end
    if(tb.rating_level) then    encoder:addu32(8, tb.rating_level)    end
    if(tb.kill_count) then    encoder:addu32(9, tb.kill_count)    end
    if(tb.damage) then    encoder:addu32(10, tb.damage)    end
    if(tb.rescue_count) then    encoder:addu32(11, tb.rescue_count)    end
    if(tb.is_dropout) then    encoder:addu32(12, tb.is_dropout)    end
    if(tb.achievements) then    encoder:addi64(13, tb.achievements)    end
    if(tb.loadout_id) then    encoder:addi64(14, tb.loadout_id)    end
    if(tb.role_id) then    encoder:addi64(15, tb.role_id)    end
end

function pb.pb_BhdSettlementDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_BhdSettlementData) or {} 
    tb.mission_record = pb.pb_BhdMissionRecordDecode(decoder:getsubmsg(1))
    local __team_kill = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __team_kill ~= 0 then tb.team_kill = __team_kill end
    local __team_damage = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __team_damage ~= 0 then tb.team_damage = __team_damage end
    local __team_rescue = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __team_rescue ~= 0 then tb.team_rescue = __team_rescue end
    local __team_alive_count = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __team_alive_count ~= 0 then tb.team_alive_count = __team_alive_count end
    local __team_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    return tb
end

function pb.pb_BhdSettlementDataEncode(tb, encoder)
    if(tb.mission_record) then    pb.pb_BhdMissionRecordEncode(tb.mission_record, encoder:addsubmsg(1))    end
    if(tb.team_kill) then    encoder:addu32(2, tb.team_kill)    end
    if(tb.team_damage) then    encoder:addu32(3, tb.team_damage)    end
    if(tb.team_rescue) then    encoder:addu32(4, tb.team_rescue)    end
    if(tb.team_alive_count) then    encoder:addu32(5, tb.team_alive_count)    end
    if(tb.team_id) then    encoder:addu64(6, tb.team_id)    end
end

function pb.pb_ArenaSettlementDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArenaSettlementData) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.teams = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.teams[k] = pb.pb_ArenaTeamDecode(v)
    end
    local __my_team_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __my_team_id ~= 0 then tb.my_team_id = __my_team_id end
    tb.rounds = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.rounds[k] = pb.pb_ArenaRoundDecode(v)
    end
    tb.knock_down_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.knock_down_array[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    tb.shoot_down_info_list = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.shoot_down_info_list[k] = pb.pb_DsPlayerKillInfoDecode(v)
    end
    return tb
end

function pb.pb_ArenaSettlementDataEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
    if(tb.teams) then
        for i=1,#(tb.teams) do
            pb.pb_ArenaTeamEncode(tb.teams[i], encoder:addsubmsg(2))
        end
    end
    if(tb.my_team_id) then    encoder:addu32(3, tb.my_team_id)    end
    if(tb.rounds) then
        for i=1,#(tb.rounds) do
            pb.pb_ArenaRoundEncode(tb.rounds[i], encoder:addsubmsg(4))
        end
    end
    if(tb.knock_down_array) then
        for i=1,#(tb.knock_down_array) do
            pb.pb_DsPlayerKillInfoEncode(tb.knock_down_array[i], encoder:addsubmsg(5))
        end
    end
    if(tb.shoot_down_info_list) then
        for i=1,#(tb.shoot_down_info_list) do
            pb.pb_DsPlayerKillInfoEncode(tb.shoot_down_info_list[i], encoder:addsubmsg(6))
        end
    end
end

function pb.pb_ArenaRoundDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArenaRound) or {} 
    local __round_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __round_id ~= 0 then tb.round_id = __round_id end
    local __result = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __win_team = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __win_team ~= 0 then tb.win_team = __win_team end
    local __round_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __round_time ~= 0 then tb.round_time = __round_time end
    return tb
end

function pb.pb_ArenaRoundEncode(tb, encoder)
    if(tb.round_id) then    encoder:addu32(1, tb.round_id)    end
    if(tb.result) then    encoder:addu32(2, tb.result)    end
    if(tb.win_team) then    encoder:addu32(3, tb.win_team)    end
    if(tb.round_time) then    encoder:addi32(4, tb.round_time)    end
end

function pb.pb_ArenaTeamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArenaTeam) or {} 
    local __team_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.players = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.players[k] = pb.pb_ArenaPlayerDecode(v)
    end
    local __score = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    return tb
end

function pb.pb_ArenaTeamEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu32(1, tb.team_id)    end
    if(tb.players) then
        for i=1,#(tb.players) do
            pb.pb_ArenaPlayerEncode(tb.players[i], encoder:addsubmsg(2))
        end
    end
    if(tb.score) then    encoder:addi32(3, tb.score)    end
end

function pb.pb_ArenaPlayerDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArenaPlayer) or {} 
    tb.basic_info = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(1))
    local __is_leavel = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_leavel ~= false then tb.is_leavel = __is_leavel end
    local __kill_player_count = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __kill_player_count ~= 0 then tb.kill_player_count = __kill_player_count end
    local __kill_ai_count = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_count ~= 0 then tb.kill_ai_count = __kill_ai_count end
    local __dead_count = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __dead_count ~= 0 then tb.dead_count = __dead_count end
    local __assist_count = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __assist_count ~= 0 then tb.assist_count = __assist_count end
    tb.final_props = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.final_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(8))
    local __rank = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __rank ~= 0 then tb.rank = __rank end
    local __play_time = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    return tb
end

function pb.pb_ArenaPlayerEncode(tb, encoder)
    if(tb.basic_info) then    pb.pb_DsPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(1))    end
    if(tb.is_leavel) then    encoder:addbool(2, tb.is_leavel)    end
    if(tb.kill_player_count) then    encoder:addu32(3, tb.kill_player_count)    end
    if(tb.kill_ai_count) then    encoder:addu32(4, tb.kill_ai_count)    end
    if(tb.dead_count) then    encoder:addu32(5, tb.dead_count)    end
    if(tb.assist_count) then    encoder:addu32(6, tb.assist_count)    end
    if(tb.final_props) then
        for i=1,#(tb.final_props) do
            pb.pb_EquipPositionEncode(tb.final_props[i], encoder:addsubmsg(7))
        end
    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(8))    end
    if(tb.rank) then    encoder:addu32(9, tb.rank)    end
    if(tb.play_time) then    encoder:addi32(10, tb.play_time)    end
end

function pb.pb_SOLGuideInstructionInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SOLGuideInstructionInfo) or {} 
    local __ds_room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_id ~= 0 then tb.ds_room_id = __ds_room_id end
    tb.match_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(2))
    local __original_equipment_price_without_keychain = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __original_equipment_price_without_keychain ~= 0 then tb.original_equipment_price_without_keychain = __original_equipment_price_without_keychain end
    local __carry_out_ai_price = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __carry_out_ai_price ~= 0 then tb.carry_out_ai_price = __carry_out_ai_price end
    local __carry_out_ai_slot = decoder:getfloat(5)
    if not PB_USE_DEFAULT_TABLE or __carry_out_ai_slot ~= 0 then tb.carry_out_ai_slot = __carry_out_ai_slot end
    local __carry_out_boss_price = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __carry_out_boss_price ~= 0 then tb.carry_out_boss_price = __carry_out_boss_price end
    local __carry_out_boss_slot = decoder:getfloat(7)
    if not PB_USE_DEFAULT_TABLE or __carry_out_boss_slot ~= 0 then tb.carry_out_boss_slot = __carry_out_boss_slot end
    local __carry_out_contain_price = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __carry_out_contain_price ~= 0 then tb.carry_out_contain_price = __carry_out_contain_price end
    local __carry_out_contain_slot = decoder:getfloat(9)
    if not PB_USE_DEFAULT_TABLE or __carry_out_contain_slot ~= 0 then tb.carry_out_contain_slot = __carry_out_contain_slot end
    local __carry_out_loot_price = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __carry_out_loot_price ~= 0 then tb.carry_out_loot_price = __carry_out_loot_price end
    local __carry_out_loot_slot = decoder:getfloat(11)
    if not PB_USE_DEFAULT_TABLE or __carry_out_loot_slot ~= 0 then tb.carry_out_loot_slot = __carry_out_loot_slot end
    local __slot_num = decoder:getfloat(12)
    if not PB_USE_DEFAULT_TABLE or __slot_num ~= 0 then tb.slot_num = __slot_num end
    local __escape_fail_reason = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __escape_fail_reason ~= 0 then tb.escape_fail_reason = __escape_fail_reason end
    local __carry_out_profit_price = decoder:geti64(14)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    tb.poi_info = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.poi_info[k] = pb.pb_PoiInfoDecode(v)
    end
    local __looting_cnt = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __looting_cnt ~= 0 then tb.looting_cnt = __looting_cnt end
    local __looting_cnt_pve = decoder:geti64(17)
    if not PB_USE_DEFAULT_TABLE or __looting_cnt_pve ~= 0 then tb.looting_cnt_pve = __looting_cnt_pve end
    local __looting_cnt_pvp = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __looting_cnt_pvp ~= 0 then tb.looting_cnt_pvp = __looting_cnt_pvp end
    local __looting_cnt_container = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __looting_cnt_container ~= 0 then tb.looting_cnt_container = __looting_cnt_container end
    local __looting_cnt_drop_point = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __looting_cnt_drop_point ~= 0 then tb.looting_cnt_drop_point = __looting_cnt_drop_point end
    local __loot_combat_supplies_slot = decoder:getfloat(21)
    if not PB_USE_DEFAULT_TABLE or __loot_combat_supplies_slot ~= 0 then tb.loot_combat_supplies_slot = __loot_combat_supplies_slot end
    local __loot_consumables_slot = decoder:getfloat(22)
    if not PB_USE_DEFAULT_TABLE or __loot_consumables_slot ~= 0 then tb.loot_consumables_slot = __loot_consumables_slot end
    local __loot_collections_slot = decoder:getfloat(23)
    if not PB_USE_DEFAULT_TABLE or __loot_collections_slot ~= 0 then tb.loot_collections_slot = __loot_collections_slot end
    tb.poi_stay_info = {}
    for k,v in pairs(decoder:getsubmsgary(24)) do
        tb.poi_stay_info[k] = pb.pb_PoiStayInfoDecode(v)
    end
    local __use_rental_props = decoder:getbool(25)
    if not PB_USE_DEFAULT_TABLE or __use_rental_props ~= false then tb.use_rental_props = __use_rental_props end
    tb.kick_down_info = {}
    for k,v in pairs(decoder:getsubmsgary(26)) do
        tb.kick_down_info[k] = pb.pb_KickDownInfoDecode(v)
    end
    local __carry_in_armor_level = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __carry_in_armor_level ~= 0 then tb.carry_in_armor_level = __carry_in_armor_level end
    local __carry_in_ammo_weighted_level = decoder:getfloat(28)
    if not PB_USE_DEFAULT_TABLE or __carry_in_ammo_weighted_level ~= 0 then tb.carry_in_ammo_weighted_level = __carry_in_ammo_weighted_level end
    local __contract_num = decoder:geti32(29)
    if not PB_USE_DEFAULT_TABLE or __contract_num ~= 0 then tb.contract_num = __contract_num end
    local __rescue_count = decoder:geti32(30)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __revive_count = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __revive_count ~= 0 then tb.revive_count = __revive_count end
    local __carry_in_helmet_level = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __carry_in_helmet_level ~= 0 then tb.carry_in_helmet_level = __carry_in_helmet_level end
    local __carry_in_armor_repair = decoder:geti32(33)
    if not PB_USE_DEFAULT_TABLE or __carry_in_armor_repair ~= 0 then tb.carry_in_armor_repair = __carry_in_armor_repair end
    local __carry_in_helmet_repair = decoder:geti32(34)
    if not PB_USE_DEFAULT_TABLE or __carry_in_helmet_repair ~= 0 then tb.carry_in_helmet_repair = __carry_in_helmet_repair end
    local __has_teammate_need_rescue = decoder:getbool(35)
    if not PB_USE_DEFAULT_TABLE or __has_teammate_need_rescue ~= false then tb.has_teammate_need_rescue = __has_teammate_need_rescue end
    local __team_type = decoder:geti32(36)
    if not PB_USE_DEFAULT_TABLE or __team_type ~= 0 then tb.team_type = __team_type end
    local __carry_out_enemy_price = decoder:geti64(37)
    if not PB_USE_DEFAULT_TABLE or __carry_out_enemy_price ~= 0 then tb.carry_out_enemy_price = __carry_out_enemy_price end
    return tb
end

function pb.pb_SOLGuideInstructionInfoEncode(tb, encoder)
    if(tb.ds_room_id) then    encoder:addu64(1, tb.ds_room_id)    end
    if(tb.match_info) then    pb.pb_MatchModeInfoEncode(tb.match_info, encoder:addsubmsg(2))    end
    if(tb.original_equipment_price_without_keychain) then    encoder:addi64(3, tb.original_equipment_price_without_keychain)    end
    if(tb.carry_out_ai_price) then    encoder:addi64(4, tb.carry_out_ai_price)    end
    if(tb.carry_out_ai_slot) then    encoder:addfloat(5, tb.carry_out_ai_slot)    end
    if(tb.carry_out_boss_price) then    encoder:addi64(6, tb.carry_out_boss_price)    end
    if(tb.carry_out_boss_slot) then    encoder:addfloat(7, tb.carry_out_boss_slot)    end
    if(tb.carry_out_contain_price) then    encoder:addi64(8, tb.carry_out_contain_price)    end
    if(tb.carry_out_contain_slot) then    encoder:addfloat(9, tb.carry_out_contain_slot)    end
    if(tb.carry_out_loot_price) then    encoder:addi64(10, tb.carry_out_loot_price)    end
    if(tb.carry_out_loot_slot) then    encoder:addfloat(11, tb.carry_out_loot_slot)    end
    if(tb.slot_num) then    encoder:addfloat(12, tb.slot_num)    end
    if(tb.escape_fail_reason) then    encoder:addi32(13, tb.escape_fail_reason)    end
    if(tb.carry_out_profit_price) then    encoder:addi64(14, tb.carry_out_profit_price)    end
    if(tb.poi_info) then
        for i=1,#(tb.poi_info) do
            pb.pb_PoiInfoEncode(tb.poi_info[i], encoder:addsubmsg(15))
        end
    end
    if(tb.looting_cnt) then    encoder:addi64(16, tb.looting_cnt)    end
    if(tb.looting_cnt_pve) then    encoder:addi64(17, tb.looting_cnt_pve)    end
    if(tb.looting_cnt_pvp) then    encoder:addi64(18, tb.looting_cnt_pvp)    end
    if(tb.looting_cnt_container) then    encoder:addi64(19, tb.looting_cnt_container)    end
    if(tb.looting_cnt_drop_point) then    encoder:addi64(20, tb.looting_cnt_drop_point)    end
    if(tb.loot_combat_supplies_slot) then    encoder:addfloat(21, tb.loot_combat_supplies_slot)    end
    if(tb.loot_consumables_slot) then    encoder:addfloat(22, tb.loot_consumables_slot)    end
    if(tb.loot_collections_slot) then    encoder:addfloat(23, tb.loot_collections_slot)    end
    if(tb.poi_stay_info) then
        for i=1,#(tb.poi_stay_info) do
            pb.pb_PoiStayInfoEncode(tb.poi_stay_info[i], encoder:addsubmsg(24))
        end
    end
    if(tb.use_rental_props) then    encoder:addbool(25, tb.use_rental_props)    end
    if(tb.kick_down_info) then
        for i=1,#(tb.kick_down_info) do
            pb.pb_KickDownInfoEncode(tb.kick_down_info[i], encoder:addsubmsg(26))
        end
    end
    if(tb.carry_in_armor_level) then    encoder:addi32(27, tb.carry_in_armor_level)    end
    if(tb.carry_in_ammo_weighted_level) then    encoder:addfloat(28, tb.carry_in_ammo_weighted_level)    end
    if(tb.contract_num) then    encoder:addi32(29, tb.contract_num)    end
    if(tb.rescue_count) then    encoder:addi32(30, tb.rescue_count)    end
    if(tb.revive_count) then    encoder:addi32(31, tb.revive_count)    end
    if(tb.carry_in_helmet_level) then    encoder:addi32(32, tb.carry_in_helmet_level)    end
    if(tb.carry_in_armor_repair) then    encoder:addi32(33, tb.carry_in_armor_repair)    end
    if(tb.carry_in_helmet_repair) then    encoder:addi32(34, tb.carry_in_helmet_repair)    end
    if(tb.has_teammate_need_rescue) then    encoder:addbool(35, tb.has_teammate_need_rescue)    end
    if(tb.team_type) then    encoder:addi32(36, tb.team_type)    end
    if(tb.carry_out_enemy_price) then    encoder:addi64(37, tb.carry_out_enemy_price)    end
end

function pb.pb_KickDownInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_KickDownInfo) or {} 
    local __ammo_level = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __ammo_level ~= 0 then tb.ammo_level = __ammo_level end
    local __armor_level = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __armor_level ~= 0 then tb.armor_level = __armor_level end
    return tb
end

function pb.pb_KickDownInfoEncode(tb, encoder)
    if(tb.ammo_level) then    encoder:addi32(1, tb.ammo_level)    end
    if(tb.armor_level) then    encoder:addi32(2, tb.armor_level)    end
end

function pb.pb_PoiInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PoiInfo) or {} 
    local __poi_name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __poi_name ~= "" then tb.poi_name = __poi_name end
    local __cur_server_time = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __cur_server_time ~= 0 then tb.cur_server_time = __cur_server_time end
    local __is_enter = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_enter ~= false then tb.is_enter = __is_enter end
    return tb
end

function pb.pb_PoiInfoEncode(tb, encoder)
    if(tb.poi_name) then    encoder:addstr(1, tb.poi_name)    end
    if(tb.cur_server_time) then    encoder:addfloat(2, tb.cur_server_time)    end
    if(tb.is_enter) then    encoder:addbool(3, tb.is_enter)    end
end

function pb.pb_PoiStayInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PoiStayInfo) or {} 
    local __poi_name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __poi_name ~= "" then tb.poi_name = __poi_name end
    local __stay_time = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __stay_time ~= 0 then tb.stay_time = __stay_time end
    local __enter_cnt = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __enter_cnt ~= 0 then tb.enter_cnt = __enter_cnt end
    return tb
end

function pb.pb_PoiStayInfoEncode(tb, encoder)
    if(tb.poi_name) then    encoder:addstr(1, tb.poi_name)    end
    if(tb.stay_time) then    encoder:addfloat(2, tb.stay_time)    end
    if(tb.enter_cnt) then    encoder:addi64(3, tb.enter_cnt)    end
end

function pb.pb_RoomsvrNumeralDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomsvrNumeral) or {} 
    local __deposit_price = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __deposit_price ~= 0 then tb.deposit_price = __deposit_price end
    local __mandel_brick_num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __mandel_brick_num ~= 0 then tb.mandel_brick_num = __mandel_brick_num end
    local __spawn_point_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __spawn_point_type ~= 0 then tb.spawn_point_type = __spawn_point_type end
    local __mandel_brick_win_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __mandel_brick_win_num ~= 0 then tb.mandel_brick_win_num = __mandel_brick_win_num end
    local __match_time_scale = decoder:getfloat(5)
    if not PB_USE_DEFAULT_TABLE or __match_time_scale ~= 0 then tb.match_time_scale = __match_time_scale end
    local __is_open_match_retreat = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __is_open_match_retreat ~= 0 then tb.is_open_match_retreat = __is_open_match_retreat end
    return tb
end

function pb.pb_RoomsvrNumeralEncode(tb, encoder)
    if(tb.deposit_price) then    encoder:addi64(1, tb.deposit_price)    end
    if(tb.mandel_brick_num) then    encoder:addi64(2, tb.mandel_brick_num)    end
    if(tb.spawn_point_type) then    encoder:addi32(3, tb.spawn_point_type)    end
    if(tb.mandel_brick_win_num) then    encoder:addi64(4, tb.mandel_brick_win_num)    end
    if(tb.match_time_scale) then    encoder:addfloat(5, tb.match_time_scale)    end
    if(tb.is_open_match_retreat) then    encoder:addu32(6, tb.is_open_match_retreat)    end
end

function pb.pb_RoomsvrParamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoomsvrParam) or {} 
    local __match_sequence = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __match_sequence ~= 0 then tb.match_sequence = __match_sequence end
    local __open_boss = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __open_boss ~= 0 then tb.open_boss = __open_boss end
    tb.iris_event_ids = decoder:getu64ary(3)
    local __mandel_brick_win_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __mandel_brick_win_num ~= 0 then tb.mandel_brick_win_num = __mandel_brick_win_num end
    local __match_time_scale = decoder:getfloat(5)
    if not PB_USE_DEFAULT_TABLE or __match_time_scale ~= 0 then tb.match_time_scale = __match_time_scale end
    local __is_open_match_retreat = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __is_open_match_retreat ~= 0 then tb.is_open_match_retreat = __is_open_match_retreat end
    return tb
end

function pb.pb_RoomsvrParamEncode(tb, encoder)
    if(tb.match_sequence) then    encoder:addu32(1, tb.match_sequence)    end
    if(tb.open_boss) then    encoder:addi32(2, tb.open_boss)    end
    if(tb.iris_event_ids) then    encoder:addu64(3, tb.iris_event_ids)    end
    if(tb.mandel_brick_win_num) then    encoder:addi64(4, tb.mandel_brick_win_num)    end
    if(tb.match_time_scale) then    encoder:addfloat(5, tb.match_time_scale)    end
    if(tb.is_open_match_retreat) then    encoder:addu32(6, tb.is_open_match_retreat)    end
end

function pb.pb_MPWeaponStatisticsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPWeaponStatistics) or {} 
    local __kill_count = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __kill_count ~= 0 then tb.kill_count = __kill_count end
    local __weapon_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    return tb
end

function pb.pb_MPWeaponStatisticsEncode(tb, encoder)
    if(tb.kill_count) then    encoder:addi32(1, tb.kill_count)    end
    if(tb.weapon_id) then    encoder:addu64(2, tb.weapon_id)    end
end

function pb.pb_DsMatchBaseRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsMatchBaseRecord) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __team_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __game_time = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __game_time ~= 0 then tb.game_time = __game_time end
    local __match_time = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __match_time ~= 0 then tb.match_time = __match_time end
    local __match_start_time = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __match_start_time ~= 0 then tb.match_start_time = __match_start_time end
    local __game_result = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __game_result ~= 0 then tb.game_result = __game_result end
    tb.match_mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(10))
    tb.achievements = {}
    for k,v in pairs(decoder:getsubmsgary(20)) do
        tb.achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    tb.sol = pb.pb_DsMatchBaseRecordIrisSOLDecode(decoder:getsubmsg(21))
    local __hero_id = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __military_rank = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __military_rank ~= 0 then tb.military_rank = __military_rank end
    local __armedforce_id = decoder:getu64(32)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    local __is_ranked_match = decoder:getbool(40)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __ranked_score_delta = decoder:geti64(41)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_delta ~= 0 then tb.ranked_score_delta = __ranked_score_delta end
    local __ranked_score = decoder:geti64(42)
    if not PB_USE_DEFAULT_TABLE or __ranked_score ~= 0 then tb.ranked_score = __ranked_score end
    local __ranked_level = decoder:geti32(43)
    if not PB_USE_DEFAULT_TABLE or __ranked_level ~= 0 then tb.ranked_level = __ranked_level end
    local __ranked_level_old = decoder:geti32(44)
    if not PB_USE_DEFAULT_TABLE or __ranked_level_old ~= 0 then tb.ranked_level_old = __ranked_level_old end
    local __ranked_score_season_no = decoder:getu32(45)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_season_no ~= 0 then tb.ranked_score_season_no = __ranked_score_season_no end
    local __ranked_coins = decoder:geti32(50)
    if not PB_USE_DEFAULT_TABLE or __ranked_coins ~= 0 then tb.ranked_coins = __ranked_coins end
    local __client_group = decoder:geti32(51)
    if not PB_USE_DEFAULT_TABLE or __client_group ~= 0 then tb.client_group = __client_group end
    return tb
end

function pb.pb_DsMatchBaseRecordEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.team_id) then    encoder:addu32(2, tb.team_id)    end
    if(tb.game_time) then    encoder:addu32(3, tb.game_time)    end
    if(tb.match_time) then    encoder:addu32(4, tb.match_time)    end
    if(tb.match_start_time) then    encoder:addi64(5, tb.match_start_time)    end
    if(tb.game_result) then    encoder:addu32(6, tb.game_result)    end
    if(tb.match_mode) then    pb.pb_MatchModeInfoEncode(tb.match_mode, encoder:addsubmsg(10))    end
    if(tb.achievements) then
        for i=1,#(tb.achievements) do
            pb.pb_DsGameAchievementEncode(tb.achievements[i], encoder:addsubmsg(20))
        end
    end
    if(tb.sol) then    pb.pb_DsMatchBaseRecordIrisSOLEncode(tb.sol, encoder:addsubmsg(21))    end
    if(tb.hero_id) then    encoder:addu64(30, tb.hero_id)    end
    if(tb.military_rank) then    encoder:addi32(31, tb.military_rank)    end
    if(tb.armedforce_id) then    encoder:addu64(32, tb.armedforce_id)    end
    if(tb.is_ranked_match) then    encoder:addbool(40, tb.is_ranked_match)    end
    if(tb.ranked_score_delta) then    encoder:addi64(41, tb.ranked_score_delta)    end
    if(tb.ranked_score) then    encoder:addi64(42, tb.ranked_score)    end
    if(tb.ranked_level) then    encoder:addi32(43, tb.ranked_level)    end
    if(tb.ranked_level_old) then    encoder:addi32(44, tb.ranked_level_old)    end
    if(tb.ranked_score_season_no) then    encoder:addu32(45, tb.ranked_score_season_no)    end
    if(tb.ranked_coins) then    encoder:addi32(50, tb.ranked_coins)    end
    if(tb.client_group) then    encoder:addi32(51, tb.client_group)    end
end

function pb.pb_DsMatchBaseRecordIrisSOLDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsMatchBaseRecordIrisSOL) or {} 
    local __collection_price = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __collection_price ~= 0 then tb.collection_price = __collection_price end
    local __damage = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __rescue = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __rescue ~= 0 then tb.rescue = __rescue end
    local __healing = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __healing ~= 0 then tb.healing = __healing end
    local __killPlayer = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __killPlayer ~= 0 then tb.killPlayer = __killPlayer end
    local __killAI = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __killAI ~= 0 then tb.killAI = __killAI end
    local __killBoss = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __killBoss ~= 0 then tb.killBoss = __killBoss end
    local __team_mate_price = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __team_mate_price ~= 0 then tb.team_mate_price = __team_mate_price end
    local __leave = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __leave ~= false then tb.leave = __leave end
    local __total_price = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __gained_price = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __gained_price ~= 0 then tb.gained_price = __gained_price end
    local __hero_id = decoder:getu64(20)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __killer_type = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __killer_type ~= 0 then tb.killer_type = __killer_type end
    tb.player = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(31))
    tb.ai = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(32))
    tb.boss = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(33))
    local __weapon = decoder:getu64(34)
    if not PB_USE_DEFAULT_TABLE or __weapon ~= 0 then tb.weapon = __weapon end
    local __revive = decoder:getu32(35)
    if not PB_USE_DEFAULT_TABLE or __revive ~= 0 then tb.revive = __revive end
    local __assist_cnt = decoder:getu32(36)
    if not PB_USE_DEFAULT_TABLE or __assist_cnt ~= 0 then tb.assist_cnt = __assist_cnt end
    local __total_shoot = decoder:getu32(37)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu32(38)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_down = decoder:getu32(39)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_down ~= 0 then tb.total_shoot_down = __total_shoot_down end
    local __total_shoot_head_down = decoder:getu32(40)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __total_contract_price = decoder:getu64(41)
    if not PB_USE_DEFAULT_TABLE or __total_contract_price ~= 0 then tb.total_contract_price = __total_contract_price end
    local __total_bring_mandel_brick = decoder:getu32(42)
    if not PB_USE_DEFAULT_TABLE or __total_bring_mandel_brick ~= 0 then tb.total_bring_mandel_brick = __total_bring_mandel_brick end
    local __total_bring_gold_sku = decoder:getu32(43)
    if not PB_USE_DEFAULT_TABLE or __total_bring_gold_sku ~= 0 then tb.total_bring_gold_sku = __total_bring_gold_sku end
    local __total_bring_red_sku = decoder:getu32(44)
    if not PB_USE_DEFAULT_TABLE or __total_bring_red_sku ~= 0 then tb.total_bring_red_sku = __total_bring_red_sku end
    local __total_search_cnt = decoder:getu32(45)
    if not PB_USE_DEFAULT_TABLE or __total_search_cnt ~= 0 then tb.total_search_cnt = __total_search_cnt end
    local __total_mileage = decoder:getu64(46)
    if not PB_USE_DEFAULT_TABLE or __total_mileage ~= 0 then tb.total_mileage = __total_mileage end
    local __begin_game_price = decoder:geti32(47)
    if not PB_USE_DEFAULT_TABLE or __begin_game_price ~= 0 then tb.begin_game_price = __begin_game_price end
    return tb
end

function pb.pb_DsMatchBaseRecordIrisSOLEncode(tb, encoder)
    if(tb.collection_price) then    encoder:addu64(1, tb.collection_price)    end
    if(tb.damage) then    encoder:addu64(2, tb.damage)    end
    if(tb.rescue) then    encoder:addu32(3, tb.rescue)    end
    if(tb.healing) then    encoder:addu32(4, tb.healing)    end
    if(tb.killPlayer) then    encoder:addu32(5, tb.killPlayer)    end
    if(tb.killAI) then    encoder:addu32(6, tb.killAI)    end
    if(tb.killBoss) then    encoder:addu32(7, tb.killBoss)    end
    if(tb.team_mate_price) then    encoder:addu64(9, tb.team_mate_price)    end
    if(tb.leave) then    encoder:addbool(10, tb.leave)    end
    if(tb.total_price) then    encoder:addu64(11, tb.total_price)    end
    if(tb.gained_price) then    encoder:addu64(12, tb.gained_price)    end
    if(tb.hero_id) then    encoder:addu64(20, tb.hero_id)    end
    if(tb.killer_type) then    encoder:addu32(30, tb.killer_type)    end
    if(tb.player) then    pb.pb_DsPlayerBasicInfoEncode(tb.player, encoder:addsubmsg(31))    end
    if(tb.ai) then    pb.pb_DsAIBasicInfoEncode(tb.ai, encoder:addsubmsg(32))    end
    if(tb.boss) then    pb.pb_DsAIBasicInfoEncode(tb.boss, encoder:addsubmsg(33))    end
    if(tb.weapon) then    encoder:addu64(34, tb.weapon)    end
    if(tb.revive) then    encoder:addu32(35, tb.revive)    end
    if(tb.assist_cnt) then    encoder:addu32(36, tb.assist_cnt)    end
    if(tb.total_shoot) then    encoder:addu32(37, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu32(38, tb.total_shoot_hit)    end
    if(tb.total_shoot_down) then    encoder:addu32(39, tb.total_shoot_down)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(40, tb.total_shoot_head_down)    end
    if(tb.total_contract_price) then    encoder:addu64(41, tb.total_contract_price)    end
    if(tb.total_bring_mandel_brick) then    encoder:addu32(42, tb.total_bring_mandel_brick)    end
    if(tb.total_bring_gold_sku) then    encoder:addu32(43, tb.total_bring_gold_sku)    end
    if(tb.total_bring_red_sku) then    encoder:addu32(44, tb.total_bring_red_sku)    end
    if(tb.total_search_cnt) then    encoder:addu32(45, tb.total_search_cnt)    end
    if(tb.total_mileage) then    encoder:addu64(46, tb.total_mileage)    end
    if(tb.begin_game_price) then    encoder:addi32(47, tb.begin_game_price)    end
end

function pb.pb_RescueInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RescueInfo) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __time_point = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __time_point ~= 0 then tb.time_point = __time_point end
    local __rescued_player_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __rescued_player_id ~= 0 then tb.rescued_player_id = __rescued_player_id end
    return tb
end

function pb.pb_RescueInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.time_point) then    encoder:adddouble(2, tb.time_point)    end
    if(tb.rescued_player_id) then    encoder:addu64(3, tb.rescued_player_id)    end
end

function pb.pb_HelpTeammateCarryOutInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HelpTeammateCarryOutInfo) or {} 
    local __helped_player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __helped_player_id ~= 0 then tb.helped_player_id = __helped_player_id end
    local __help_carryout_price = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __help_carryout_price ~= 0 then tb.help_carryout_price = __help_carryout_price end
    return tb
end

function pb.pb_HelpTeammateCarryOutInfoEncode(tb, encoder)
    if(tb.helped_player_id) then    encoder:addu64(1, tb.helped_player_id)    end
    if(tb.help_carryout_price) then    encoder:addu64(2, tb.help_carryout_price)    end
end

function pb.pb_KillAssistantInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_KillAssistantInfo) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __time_point = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __time_point ~= 0 then tb.time_point = __time_point end
    local __killed_player_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __killed_player_id ~= 0 then tb.killed_player_id = __killed_player_id end
    local __killed_team_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __killed_team_id ~= 0 then tb.killed_team_id = __killed_team_id end
    tb.killed_position = pb.pb_DsPlayerPositionDecode(decoder:getsubmsg(5))
    local __is_kill_team = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __is_kill_team ~= false then tb.is_kill_team = __is_kill_team end
    local __is_knock_down = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __is_knock_down ~= false then tb.is_knock_down = __is_knock_down end
    local __direct_dead = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __direct_dead ~= false then tb.direct_dead = __direct_dead end
    return tb
end

function pb.pb_KillAssistantInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.time_point) then    encoder:adddouble(2, tb.time_point)    end
    if(tb.killed_player_id) then    encoder:addu64(3, tb.killed_player_id)    end
    if(tb.killed_team_id) then    encoder:addu64(4, tb.killed_team_id)    end
    if(tb.killed_position) then    pb.pb_DsPlayerPositionEncode(tb.killed_position, encoder:addsubmsg(5))    end
    if(tb.is_kill_team) then    encoder:addbool(6, tb.is_kill_team)    end
    if(tb.is_knock_down) then    encoder:addbool(7, tb.is_knock_down)    end
    if(tb.direct_dead) then    encoder:addbool(8, tb.direct_dead)    end
end

function pb.pb_DropPickupItemInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DropPickupItemInfo) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __time_point = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __time_point ~= 0 then tb.time_point = __time_point end
    local __item_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __item_num = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __item_num ~= 0 then tb.item_num = __item_num end
    local __item_total_price = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __item_total_price ~= 0 then tb.item_total_price = __item_total_price end
    local __interaction_teammate_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __interaction_teammate_id ~= 0 then tb.interaction_teammate_id = __interaction_teammate_id end
    return tb
end

function pb.pb_DropPickupItemInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.time_point) then    encoder:adddouble(2, tb.time_point)    end
    if(tb.item_id) then    encoder:addu64(3, tb.item_id)    end
    if(tb.item_num) then    encoder:addu32(4, tb.item_num)    end
    if(tb.item_total_price) then    encoder:addu32(5, tb.item_total_price)    end
    if(tb.interaction_teammate_id) then    encoder:addu64(6, tb.interaction_teammate_id)    end
end

function pb.pb_DsTssReportDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsTssReportData) or {} 
    local __report_scence = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __report_scence ~= 0 then tb.report_scence = __report_scence end
    local __report_category = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __report_category ~= 0 then tb.report_category = __report_category end
    tb.report_reason = decoder:geti32ary(3)
    local __reported_player_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __reported_player_id ~= 0 then tb.reported_player_id = __reported_player_id end
    local __battle_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __battle_id ~= 0 then tb.battle_id = __battle_id end
    local __battle_time = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __battle_time ~= 0 then tb.battle_time = __battle_time end
    local __reported_profile_url = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __reported_profile_url ~= "" then tb.reported_profile_url = __reported_profile_url end
    local __report_desc = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __report_desc ~= "" then tb.report_desc = __report_desc end
    local __report_content = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __report_content ~= "" then tb.report_content = __report_content end
    local __entrance = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __entrance ~= 0 then tb.entrance = __entrance end
    local __nick_name = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __nick_name ~= "" then tb.nick_name = __nick_name end
    local __language_id = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __language_id ~= 0 then tb.language_id = __language_id end
    local __report_relationship = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __report_relationship ~= 0 then tb.report_relationship = __report_relationship end
    return tb
end

function pb.pb_DsTssReportDataEncode(tb, encoder)
    if(tb.report_scence) then    encoder:addi32(1, tb.report_scence)    end
    if(tb.report_category) then    encoder:addi32(2, tb.report_category)    end
    if(tb.report_reason) then    encoder:addi32(3, tb.report_reason)    end
    if(tb.reported_player_id) then    encoder:addu64(4, tb.reported_player_id)    end
    if(tb.battle_id) then    encoder:addu64(5, tb.battle_id)    end
    if(tb.battle_time) then    encoder:addi64(6, tb.battle_time)    end
    if(tb.reported_profile_url) then    encoder:addstr(7, tb.reported_profile_url)    end
    if(tb.report_desc) then    encoder:addstr(8, tb.report_desc)    end
    if(tb.report_content) then    encoder:addstr(9, tb.report_content)    end
    if(tb.entrance) then    encoder:addu32(10, tb.entrance)    end
    if(tb.nick_name) then    encoder:addstr(11, tb.nick_name)    end
    if(tb.language_id) then    encoder:addi32(12, tb.language_id)    end
    if(tb.report_relationship) then    encoder:addi32(13, tb.report_relationship)    end
end

