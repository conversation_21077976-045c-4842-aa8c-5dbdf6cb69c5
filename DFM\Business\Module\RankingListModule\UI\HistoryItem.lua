----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class HistoryItem : UIWidgetBase
local HistoryItem = ui("HistoryItem")

-- 每个赛季
function HistoryItem:Ctor()
    self._wtSeasonNum = self:Wnd("DFTextBlock_23", UITextBlock)  -- S赛季 (S1, S2, ...)
    self._wtSeasonTheme = self:Wnd("DFTextBlock", UITextBlock)  -- S赛季主题 (起源, 聚变, ...)
    self._wtSeasonTime = self:Wnd("DFTextBlock_1", UITextBlock)  -- 持续时间 2025/07/03-至今
    self._wtVerticalBox = self:Wnd("DFVerticalBox_49", UIWidgetBase) -- 内容栏
    self._wtDividerLine = self:Wnd("DFCanvasPosReContainer_1", UIWidgetBase) -- 分割线
    self._wtVerticalBox:ClearChildren()
end

function HistoryItem:RefreshHistoryItem(position, itemWidget, season_id, seasonNum, seasonTheme, seasonTime, seasonData, playerName, bIsLast)
    loginfo("HistoryItem:RefreshHistoryItem", position, itemWidget)
    if bIsLast then
        self._wtDividerLine:Visible()
    else
        self._wtDividerLine:Collapsed()
    end
    self._wtSeasonNum:SetText(seasonNum)
    self._wtSeasonTheme:SetText(seasonTheme)
    self._wtSeasonTime:SetText(seasonTime)
    local serialNumber = Server.TournamentServer:GetCurSerial()
    local cur_period = Server.RankingListServer:CalcPeriod(serialNumber, Server.RankingListServer:GetServerTime())  -- 当前所在期数
    local bIsCurrentSeason = season_id == serialNumber
    -- 使用ipairs遍历数组（推荐用于顺序数组）
    for index, item in ipairs(seasonData) do
        --print("索引:", index, "title:", item.title)
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItemList, self._wtVerticalBox)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            local bIsCurrentPeriod = false
            if bIsCurrentSeason and item.period == cur_period then
                bIsCurrentPeriod = true
            end
            uiIns:RefreshTitleSubItemList(item.period, item.content, bIsCurrentPeriod, playerName, index == #seasonData)
        end
    end
end

return HistoryItem
