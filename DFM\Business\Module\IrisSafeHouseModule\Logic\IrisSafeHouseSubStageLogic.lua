----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------



local IrisSafeHouseSubStageLogic = {}

local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local IrisSafeHouseEquipLogic = require "DFM.Business.Module.IrisSafeHouseModule.Logic.IrisSafeHouseEquipLogic"
local IrisSafeHouseConfig = require "DFM.Business.Module.IrisSafeHouseModule.IrisSafeHouseConfig"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local EnterIrisWorldLogic = require "DFM.Business.Module.IrisSafeHouseModule.Logic.EnterIrisWorldLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"

local UGameplayStatics = import "GameplayStatics"
local EViewTargetBlendFunction = import "EViewTargetBlendFunction"
local UDFMIrisEnterSubsystem = import "DFMIrisEnterSubsystem"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local ESafeHouseHUDMode = import "ESafeHouseHUDMode"
local UCharacterMovementComponent = import "CharacterMovementComponent"
local USafeHouseGameplayUtils = import "SafeHouseGameplayUtils"
local EMovementMode = import "EMovementMode"
local UGPClientSeamlessTravel = import "GPClientSeamlessTravel"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local ADFMTODLevelSequenceActor = import "DFMTODLevelSequenceActor"
local ADFMSafeHouseNPC = import "DFMSafeHouseNPC"
local ASafeHouseDepartment = import "SafeHouseDepartment"
local ASafeHouseBuilding = import "SafeHouseBuilding"
local UWidgetComponent = import "WidgetComponent"
local USkeletalMeshComponent = import "SkeletalMeshComponent"
local PerfGearPipeline = import "PerfGearPipeline"
local UGPInputHelper = import "GPInputHelper"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local EProcessInputCompReason = import "EProcessInputCompReason"

-- local ASafeHouseBuildingProxy = import "SafeHouseBuildingProxy"

IrisSafeHouseSubStageLogic.actionHandles = {}
IrisSafeHouseSubStageLogic.checkFallingHandle = nil
IrisSafeHouseSubStageLogic.fPreloadFinishCallback = nil
IrisSafeHouseSubStageLogic.preloadTargetLoc = nil
IrisSafeHouseSubStageLogic.DEFAULT_LOAD_TIMEOUT = 5
IrisSafeHouseSubStageLogic.bShouldTeleport = true
IrisSafeHouseSubStageLogic.bShouldKeepWorldComposition = false
IrisSafeHouseSubStageLogic.currentAppliedQuality = -1
IrisSafeHouseSubStageLogic.bPreloadCharacterMesh = true
IrisSafeHouseSubStageLogic.bShouldWaitCharacterMesh = false

local function log(...)
	loginfo("IrisSafeHouseSubStageLogic", ...)
end

function IrisSafeHouseSubStageLogic.Enter3DSafeHouse()
	log("Enter3DSafeHouse")
	-- Facade.GameFlowManager:EnterSubStage(ESubStage.SafeHouse3D)

	if not IrisSafeHouseSubStageLogic._CheckSafeHousePakDownload() then
		log("Enter3DSafeHouse SafeHouse pak not download, don't enter.")

		if Module.IrisSafeHouse.Field.bEnteringSafeHouse then
			Module.IrisSafeHouse.Field.bEnteringSafeHouse = false -- 完成进入安全屋的行为
			IrisSafeHouseConfig.evtFinishEnterSafeHouse:Invoke()
		end
		return
	end

	-- 预加载武器蓝图
	Module.IrisSafeHouse:PreloadWeaponBlueprints("SafeHouse")

	IrisSafeHouseSubStageLogic.Load3DSafeHouse(IrisSafeHouseSubStageLogic.DEFAULT_LOAD_TIMEOUT, IrisSafeHouseSubStageLogic.OnEnter3DSafeHouse)
end

local bIsEntering = false
function IrisSafeHouseSubStageLogic.OnEnter3DSafeHouse()
	bIsEntering = true
	-- if Server.IrisSafeHouseServer.bInSafeHouse3D then
	-- 	return
	-- end
	Server.IrisSafeHouseServer.bInSafeHouse3D = true

	-- @fixme 临时加的清理
	if DFHD_LUA == 1 then
		UGPInputHelper.ClearInputMode(GetGameInstance(), false)
	end
	-- 需要检查掉地
	IrisSafeHouseSubStageLogic._StartCheckFalling()

	if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
		Facade.UIManager:SetStackBottomKeepMode(false, "3DSafeHouse")
	end

	Facade.UIManager:PopAllUI(true, false, MapPopAllUIReason2Str.EnterSafeHouseHUD) --- [清空栈流程] 彻底清空栈，当前显示安全屋HUD
	Facade.UIManager:CloseAllPopUI()

	Facade.GameFlowManager:EnterSubStage(ESubStage.SafeHouse3D)

	IrisSafeHouseSubStageLogic.CheckTeleport()

	if Module.IrisSafeHouse.Field.bEnteringSafeHouse then
		Module.IrisSafeHouse.Field.bEnteringSafeHouse = false -- 完成进入安全屋的行为
		IrisSafeHouseConfig.evtFinishEnterSafeHouse:Invoke()
	end

	bIsEntering = false
end

function IrisSafeHouseSubStageLogic.Leave3DSafeHouse(bOpenMainPanel, mainTabIndex)
	if bIsEntering then
		logwarning("IrisSafeHouseSubStageLogic.Leave3DSafeHouse happens while entering, abort")
		return
	end

	-- if not Server.IrisSafeHouseServer.bInSafeHouse3D then
	-- 	return
	-- end
	Server.IrisSafeHouseServer.bInSafeHouse3D = false

	log("Leave3DSafeHouse", mainTabIndex)

	IrisSafeHouseSubStageLogic._StopCheckFalling()

	if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
		Facade.UIManager:SetStackBottomKeepMode(true, "3DSafeHouse")
	end

	local currentStage = Facade.GameFlowManager:GetCurrentSubStage()
	if currentStage == ESubStage.SafeHouse3D then
		Facade.GameFlowManager:EnterSubStage(ESubStage.None)
	end

	if bOpenMainPanel then
		mainTabIndex = setdefault(mainTabIndex, 1)

		if IsHD() then
			Module.IrisSafeHouse:OpenMainTabView(mainTabIndex)
		else
			Facade.UIManager:AsyncShowUI(UIName2ID.IrisWorldEntryMainPanel)
		end
	end

	if IrisSafeHouseSubStageLogic._ShouldUnload3DSafeHouse() then
		IrisSafeHouseSubStageLogic._UnloadSafeHouseConfig()
	end

	-- 释放武器蓝图
	Module.IrisSafeHouse:ReleaseWeaponBlueprints("SafeHouse")
end

function IrisSafeHouseSubStageLogic.Load3DSafeHouse(timeout, fPreloadFinishCallback)
	if not IrisSafeHouseSubStageLogic._CheckSafeHousePakDownload() then
		return
	end

	log("Preload3DSafeHouse")

	IrisSafeHouseSubStageLogic._LoadSafeHouseConfig()
	IrisSafeHouseSubStageLogic.SetShouldKeepWorldComposition(true)
	IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition(true)

	local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	if localCtrl:SpawnDefaultCharacter() then
		IrisSafeHouseSubStageLogic.TeleportTo3DSafeHouseDefaultLoc()
	end

	IrisSafeHouseSubStageLogic.bShouldWaitCharacterMesh = false
	if IrisSafeHouseSubStageLogic.bPreloadCharacterMesh then
		if IrisSafeHouseEquipLogic.PreloadCharacterMesh() then
			IrisSafeHouseSubStageLogic.bShouldWaitCharacterMesh = true
		end
	end

	local defaultLoc = IrisSafeHouseSubStageLogic.Get3DSafeHouseDefaultLoc()
	local importance = 2
	localCtrl:StartPreload3DSafeHouse(defaultLoc, importance, timeout)

	IrisSafeHouseSubStageLogic.fPreloadFinishCallback = fPreloadFinishCallback
	localCtrl.OnPreload3DSafeHouseDone:Bind(IrisSafeHouseSubStageLogic.OnLoad3DSafeHouseFinished)
end

function IrisSafeHouseSubStageLogic.OnLoad3DSafeHouseFinished()
	if IrisSafeHouseSubStageLogic.bPreloadCharacterMesh
	and IrisSafeHouseSubStageLogic.bShouldWaitCharacterMesh then
		local dfmCharacter = InGameController:Get():GetGPCharacter()
		if dfmCharacter and not dfmCharacter.bOnCharacterMeshLoadCompleteTPP then
			log("OnLoad3DSafeHouseFinished TPPMesh not load complete, delay enter.")
			Timer.DelayCall(0.1, IrisSafeHouseSubStageLogic.OnLoad3DSafeHouseFinished)
			return
		end
	end

	log("OnLoad3DSafeHouseFinished.")

	local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	localCtrl.OnPreload3DSafeHouseDone:Clear()

	IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition()

	-- Always focus safehouse loc
	local worldComposition = GetWorld().WorldComposition
	if worldComposition then
		local defaultLoc = IrisSafeHouseSubStageLogic.Get3DSafeHouseDefaultLoc()
		worldComposition:FocusPlayerLocation(defaultLoc)

		log("Always focus default location")
	end

	-- Disable tick by default
	IrisSafeHouseSubStageLogic._SetActorTick(false)

	if IrisSafeHouseSubStageLogic.fPreloadFinishCallback then
		IrisSafeHouseSubStageLogic.fPreloadFinishCallback()

		IrisSafeHouseSubStageLogic.fPreloadFinishCallback = nil
	end
end

function IrisSafeHouseSubStageLogic.CheckLeave3DSafeHouse(bOpenMainPanel)
	if Server.IrisSafeHouseServer.bInSafeHouse3D then
		IrisSafeHouseSubStageLogic.Leave3DSafeHouse(bOpenMainPanel)
		return true
	end

	return false
end

function IrisSafeHouseSubStageLogic.AddUIChangeListener()
	Facade.UIManager.Events.evtStackUIChanged:AddListener(IrisSafeHouseSubStageLogic.OnStackViewChanged_View)
end

function IrisSafeHouseSubStageLogic.RemoveUIChangeListener()
	Facade.UIManager.Events.evtStackUIChanged:RemoveListener(IrisSafeHouseSubStageLogic.OnStackViewChanged_View)
end

function IrisSafeHouseSubStageLogic.OnStackViewChanged_View()
	local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
	if curGameFlow ~= EGameFlowStageType.SafeHouse then
		return
	end

	local bIsInSeamlessEnter = UDFMIrisEnterSubsystem:Get(GetWorld()):IsInSeamlessEnter()
	if bIsInSeamlessEnter then
		return
	end

	-- 如果没有UI，且在安全屋gameflow，则说明回到了安全屋的substage
	local curView = Facade.UIManager:GetCurrentStackUI()
	local curStackUICount = Facade.UIManager:GetStackUICount()
	if curStackUICount == 0 then
		if not IsHD() or not Module.CommonBar:IsChangingPrimaryTab() then
			if Server.IrisSafeHouseServer.bInSafeHouse3D
			and not Server.IrisSafeHouseServer.bIsInRange 
			and not ItemOperaTool.bIsInCollectionRoom then
				-- 恢复SubStage
				Facade.GameFlowManager:EnterSubStage(ESubStage.SafeHouse3D)
			end
		end
	elseif curStackUICount == 1 then
		if IsHD() then
			local curViewUIIndex = Module.CommonBar:GetUIIndexInPrimaryTabGroupHD(curView)
			if curViewUIIndex == nil then
				-- 从3D特勤处打开非PrimaryTabUI时，逻辑上仍然在3D特勤处，按Esc会返回
			elseif curViewUIIndex == 1 then
				-- 从3D特勤处打开【开始游戏】页签时，逻辑上退出该状态
				IrisSafeHouseSubStageLogic.CheckLeave3DSafeHouse(false)
			else
				-- 从3D特勤处打开其他页签时，逻辑上仍然在3D特勤处，按Esc会返回
			end
		else
		end
	end
end

function IrisSafeHouseSubStageLogic.AddSafehouse3DEvents()
	
end

function IrisSafeHouseSubStageLogic.RemoveSafehouse3DEvents()
	
end

function IrisSafeHouseSubStageLogic.OnEnterSafeHouse3DSubStage()
	log("OnEnterSafeHouse3DSubStage")

	IrisSafeHouseSubStageLogic.AddSafehouse3DEvents()

	IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition()

	-- Display Action绑定
	IrisSafeHouseSubStageLogic._SetActions(true)
	-- 恢复大世界灯光系统
	IrisSafeHouseSubStageLogic._SetLight(true)
	-- 关闭3p weapon pool（暂时有bug，会导致3p武器的state有问题）
	IrisSafeHouseSubStageLogic._EnableWeapon3pPool(false)
	-- 切角色的一些状态
	IrisSafeHouseSubStageLogic._SetCharacter(true)
    -- 切相机和操控
	IrisSafeHouseSubStageLogic._SetController(true)
	-- 切HUD
	IrisSafeHouseSubStageLogic._SetHUD(true)
	-- 切HUDState
	IrisSafeHouseSubStageLogic._SetHUDState(true)
	-- 切CVar
	IrisSafeHouseSubStageLogic._SetConsoleVars(true)
	-- 切音效
	IrisSafeHouseSubStageLogic._SetSound(true)
	-- 动态物体的Tick
	IrisSafeHouseSubStageLogic._SetActorTick(true)
	-- 打开位置记录的定时器
	-- IrisSafeHouseSubStageLogic._AddRecordPosTimer()

	--看看有没有进入或离开安全屋ds的操作，只有当玩家在idle和matching状态下需要
	local matchServer = Server.MatchServer
	if matchServer:GetIsIdle() or matchServer:GetIsMatching() then
		if matchServer:SafeHouseDSStateIsToDS() then
			loginfo("IrisSafeHouseSubSafeLogic:EnterSafeHouseSubStage entersafehouse ds")
			Server.TeamServer:TryEnterSafeHouseDS()
		elseif matchServer:SafeHouseDSStateIsToStandalone() then
			loginfo("IrisSafeHouseSubSafeLogic:EnterSafeHouseSubStage leavesafehouse ds")
			matchServer:LeaveSafeHouseDS(0, matchServer:GetSafeHouseDSTeamId())
		end
	end

	IrisSafeHouseSubStageLogic._NotifyEnter3DSafeHouse(true)
end

function IrisSafeHouseSubStageLogic.OnLeaveSafeHouse3DSubStage(bInit)
	log("OnLeaveSafeHouse3DSubStage")

	IrisSafeHouseSubStageLogic.RemoveSafehouse3DEvents()

	IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition()

	-- IrisSafeHouseSubStageLogic.TeleportTo3DSafeHouseOutsideLoc()

	IrisSafeHouseSubStageLogic._SetActions(false)
	IrisSafeHouseSubStageLogic._SetLight(false)
	IrisSafeHouseSubStageLogic._EnableWeapon3pPool(true)
	IrisSafeHouseSubStageLogic._SetCharacter(false)
	IrisSafeHouseSubStageLogic._SetController(false)
	IrisSafeHouseSubStageLogic._SetHUD(false)
	IrisSafeHouseSubStageLogic._SetHUDState(false)
	IrisSafeHouseSubStageLogic._SetConsoleVars(false)
	IrisSafeHouseSubStageLogic._SetSound(false)
	IrisSafeHouseSubStageLogic._SetActorTick(false)
	-- IrisSafeHouseSubStageLogic._RemoveRecordPosTimer()

	IrisSafeHouseSubStageLogic._NotifyEnter3DSafeHouse(false)

	--关闭对话
	local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	if localCtrl then
		local pawn = localCtrl:GetPawn()
		if pawn then
			local dialog = pawn:GetComponentByClass(UE.PlayerDialogueComponent)
			if isvalid(dialog) then
				dialog:NetMultiReset()
			end
		end
	end

	if not bInit then
		-- 离开3D特勤处之前，Cache当前相机位置，避免PRT被卸载
		if not IrisSafeHouseSubStageLogic.CheckAppliedQualityChange() then
			-- LightUtil.SetFixedCameraPos(true)
		end
	end
end

function IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition_WorldInit(world)
	local worldName = UKismetSystemLibrary.GetObjectName(world)
    if worldName == "Iris_Entry" then
		local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
		if table.contains(IrisSafeHouseConfig.GameFlows2DisableWorldComposition, currentGameFlow) then
			log("UpdateEnableWorldCompositionAtWorldInit trigger disable world composition", worldName, currentGameFlow)
			IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition(false)
		end
	else
	end
end

function IrisSafeHouseSubStageLogic.OnMatchStateChanged(state)
	IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition()
	
	-- if Server.MatchServer:GetIsMatching() then
	-- 	IrisSafeHouseSubStageLogic.CheckLeave3DSafeHouse(true)
	-- end
end

function IrisSafeHouseSubStageLogic.UpdateEnableWorldComposition(bForceEnable)
	local bEnableWorldComposition
	if bForceEnable ~= nil then
		bEnableWorldComposition = bForceEnable
	else
		bEnableWorldComposition = IrisSafeHouseSubStageLogic._ShouldEnableWorldComposition()
	end
	bEnableWorldComposition = true
	local worldComposition = GetWorld().WorldComposition
	if worldComposition then
		worldComposition:SetWorldCompositionStreamingEnable(bEnableWorldComposition)
	end
end

function IrisSafeHouseSubStageLogic.SetShouldTeleport(value)
	log("SetShouldTeleport", value)
	IrisSafeHouseSubStageLogic.bShouldTeleport = value
end

function IrisSafeHouseSubStageLogic.CheckTeleport()
	if IrisSafeHouseSubStageLogic.bShouldTeleport then
		IrisSafeHouseSubStageLogic.bShouldTeleport = false
		IrisSafeHouseSubStageLogic.TeleportTo3DSafeHouseDefaultLoc()
	end
end

function IrisSafeHouseSubStageLogic.SetShouldKeepWorldComposition(value)
	log("SetShouldKeepWorldComposition", value)
	IrisSafeHouseSubStageLogic.bShouldKeepWorldComposition = value
end

function IrisSafeHouseSubStageLogic.Get3DSafeHouseDefaultLoc()
	local absLoc, rot
	local outActors
	local dfmCharacter = InGameController:Get():GetGPCharacter()
	if not dfmCharacter then
		return FVector.ZeroVector
	end

	outActors = UGameplayStatics.GetAllActorsWithTag(dfmCharacter, "SafeHousePS0", outActors)
	if outActors:Num() == 1 then
		local startPoint = outActors:Get(0)
		local loc = startPoint:K2_GetActorLocation()
		rot = startPoint:K2_GetActorRotation()
		absLoc = UGameplayStatics.RebaseLocalOriginOntoZero(dfmCharacter, loc)
	end

	return absLoc
end

function IrisSafeHouseSubStageLogic.TeleportTo3DSafeHouseDefaultLoc()
	-- local result, absLoc = Module.IrisSafeHouse:GetPlayerLocInSafeHouse()
	-- if not result or not absLoc then
	-- 	log("TeleportTo3DSafeHouseDefaultLoc use default loc")
	-- 	absLoc = IrisSafeHouseSubStageLogic.Get3DSafeHouseDefaultLoc()
	-- end
	
	local absLoc = IrisSafeHouseSubStageLogic.Get3DSafeHouseDefaultLoc()
	local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	localCtrl:ExecTeleport(absLoc)
end

function IrisSafeHouseSubStageLogic.TeleportTo3DSafeHouseOutsideLoc()
    local loc = FVector(-200000, -200000, 20000)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
	localCtrl:ExecTeleport(loc)
end

function IrisSafeHouseSubStageLogic.CheckAppliedQualityChange(curApplyQuality)
	if not curApplyQuality then
		curApplyQuality = PerfGearPipeline.Get():GetCurApplyQuality()
	end

	log("CheckAppliedQualityChange", curApplyQuality, IrisSafeHouseSubStageLogic.currentAppliedQuality)
	if IrisSafeHouseSubStageLogic.currentAppliedQuality ~= curApplyQuality then
		IrisSafeHouseSubStageLogic.currentAppliedQuality = curApplyQuality

		LightUtil.SetFixedCameraPos(false)
		return true
	end

	return false
end

-----------------------------------------------------------------------
--region Private

local recordPosTimerHandle = nil
function IrisSafeHouseSubStageLogic._AddRecordPosTimer()
	IrisSafeHouseSubStageLogic._RemoveRecordPosTimer()

	recordPosTimerHandle = Timer:NewIns(0.1,0)
	recordPosTimerHandle:AddListener(IrisSafeHouseSubStageLogic._UpdatePlayerLoc)
	recordPosTimerHandle:Start()
end

function IrisSafeHouseSubStageLogic._RemoveRecordPosTimer()
	if recordPosTimerHandle then
		recordPosTimerHandle:Release()
		recordPosTimerHandle = nil
	end
end

function IrisSafeHouseSubStageLogic._UpdatePlayerLoc()
	Module.IrisSafeHouse:SavePlayerLocInSafeHouse()
end

function IrisSafeHouseSubStageLogic._EnableWeapon3pPool(bEnable)
	local cmd = string.format("weapon.Enable3pPool %d", bEnable and 1 or 0)
	UKismetSystemLibrary.ExecuteConsoleCommand(
		GetGameInstance(),
		cmd,
		nil
	)
end

function IrisSafeHouseSubStageLogic._SetController(bEnterSafehouse3D)
    local localCtrl = UGameplayBlueprintHelper.GetLocalGPPlayerController(GetWorld())
	if localCtrl then
		localCtrl:ChangeTouchControlMode(bEnterSafehouse3D)

		local character = localCtrl:GetPawn()
		if bEnterSafehouse3D then
			-- character:SetEnablePhysics(true)
			localCtrl:GPSetViewTargetWithBlend(character, false, true, 0, EViewTargetBlendFunction.VTBlend_Linear, 0, false)
			localCtrl:SetProcessInputComp(EProcessInputCompReason.EProcessInputReason_3DSafeHouse, true)
		else
			-- character:SetEnablePhysics(false)
			localCtrl:PlayerStopRequest()
			localCtrl:SetProcessInputComp(EProcessInputCompReason.EProcessInputReason_3DSafeHouse, false)
		end
	end
end

function IrisSafeHouseSubStageLogic._SetCharacter(bEnterSafehouse3D)
	local dfmCharacter = InGameController:Get():GetGPCharacter()
	if not dfmCharacter then
		return
	end

	dfmCharacter.Blackboard.bIn3DSafeHouse = bEnterSafehouse3D

	if bEnterSafehouse3D then
		-- 初始化装备
		IrisSafeHouseEquipLogic.InitCharacterEquip()

		-- 初始化商业化轮盘
		IrisSafeHouseEquipLogic.InitCommertialRoulette()

		-- 无限体力
		-- USafeHouseGameplayUtils.SetCharacterStamina(dfmCharacter, 99999, 99999)
		USafeHouseGameplayUtils.SetCharacterStaminaChangeRate(dfmCharacter, 0.001)
	else
		-- USafeHouseGameplayUtils.SetCharacterStamina(dfmCharacter, 100, 100)
		USafeHouseGameplayUtils.SetCharacterStaminaChangeRate(dfmCharacter, 1)
	end

	dfmCharacter:SetActorHiddenInGame(not bEnterSafehouse3D)

	if bEnterSafehouse3D then
        dfmCharacter:PlayerWantStand()
    end

	local movementComp = dfmCharacter:GetComponentByClass(UCharacterMovementComponent)
	if movementComp then
		movementComp:SetDisablePlayerPerformMovement(not bEnterSafehouse3D, "IrisSafeHouseSubStageLogic")
	end
end

function IrisSafeHouseSubStageLogic._SetLight(bEnterSafehouse3D)
	if bEnterSafehouse3D then
		LightUtil.SetMiniWorld(false)
		-- LightUtil.SetFixedCameraPos(false)
		IrisSafeHouseSubStageLogic._DelaySetFixedCameraPos()
		LightUtil.SetSafeHouseHeightFog(true)
	else
		LightUtil.SetMiniWorld(true)
		IrisSafeHouseSubStageLogic._CancelDelaySetFixedCameraPos()
		-- LightUtil.SetFixedCameraPos(true)
		LightUtil.SetSafeHouseHeightFog(false)
	end
end

local fDelaySetFixedCameraPosHandle = nil

function IrisSafeHouseSubStageLogic._InternalSetFixedCameraPos()
	LightUtil.SetFixedCameraPos(true)
	fDelaySetFixedCameraPosHandle = nil
end

function IrisSafeHouseSubStageLogic._DelaySetFixedCameraPos()
	if fDelaySetFixedCameraPosHandle then
		return
	end

	log("_DelaySetFixedCameraPos")
	fDelaySetFixedCameraPosHandle = Timer.DelayCall(1, IrisSafeHouseSubStageLogic._InternalSetFixedCameraPos)
end

function IrisSafeHouseSubStageLogic._CancelDelaySetFixedCameraPos()
	if not fDelaySetFixedCameraPosHandle then
		return
	end

	log("_CancelDelaySetFixedCameraPos")
	Timer.CancelDelay(fDelaySetFixedCameraPosHandle)
	fDelaySetFixedCameraPosHandle = nil
end

local UDFMTODSubSystem = import "DFMTODSubSystem"
function IrisSafeHouseSubStageLogic._SetHUD(bEnterSafehouse3D)
	local localCtrl = UGameplayBlueprintHelper.GetLocalGPPlayerController(GetWorld())
	if localCtrl then
		if bEnterSafehouse3D then
			localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.SafeHouse3D, false)
			UDFMTODSubSystem.Get():SetSequenceManagerRunningStatus(false)
		else
			localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.None, false)
		end
	end
end

function IrisSafeHouseSubStageLogic._SetHUDState(bEnterSafehouse3D)
	local hudStateManager = UE.HUDStateManager.Get(GetWorld())
	if hudStateManager then
		if bEnterSafehouse3D then
			hudStateManager:RemoveState(UE.GameHUDSate.GHS_GlobalHideAllHUD, false)
		else
			hudStateManager:AddState(UE.GameHUDSate.GHS_GlobalHideAllHUD, false)
		end
	end
end

function IrisSafeHouseSubStageLogic._SetConsoleVars(bEnterSafehouse3D)
	local cmds = {}

	-- 3D特勤处锁LOD=0
	table.insert(cmds, string.format("r.ForceLOD %d", bEnterSafehouse3D and 0 or -1))

	for _, cmd in ipairs(cmds) do
        UKismetSystemLibrary.ExecuteConsoleCommand(
            GetGameInstance(),
            cmd,
            nil
        )
    end
end

function IrisSafeHouseSubStageLogic._SetSound(bEnterSafehouse3D)
	if bEnterSafehouse3D then
        -- 开启局内音效
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
	else
        -- 关闭局内音效
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
	end
end

function IrisSafeHouseSubStageLogic._ShouldEnableWorldComposition()
	local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
	if currentGameFlow == EGameFlowStageType.Game then
		return true
	end

	local currentSubStage = Facade.GameFlowManager:GetCurrentSubStage()
	if currentSubStage == ESubStage.SafeHouse3D then
		return true
	end

	if IrisSafeHouseSubStageLogic.bShouldKeepWorldComposition then
		return true
	end

	-- 需要判断组队状态/开赛/无缝等状态
	if Server.MatchServer:GetIsWaitForGotoGame() 
	or Server.MatchServer:GetIsInGame() then
	-- or Server.MatchServer:GetIsMatching() then
		return true
	end

	return false
end

function IrisSafeHouseSubStageLogic._SetActions(bEnterSafehouse3D)
	local inputMonitor = Facade.UIManager:GetInputMonitor()

	if bEnterSafehouse3D then
		-- romzhang 这里被我注释掉了，因为SafeHouse3D_OpenMoss虽然没功能，但是占用了Tab键
		-- local mossActionHandle = inputMonitor:AddDisplayActionBinding(
		-- 	"SafeHouse3D_OpenMoss",
		-- 	EInputEvent.IE_Pressed,
		-- 	IrisSafeHouseSubStageLogic._OnOpenMoss,
		-- 	nil,
		-- 	EDisplayInputActionPriority.UI_HUD
		-- )
		-- table.insert(IrisSafeHouseSubStageLogic.actionHandles, mossActionHandle)
	
		local chatActionHandle = inputMonitor:AddDisplayActionBinding(
			"SafeHouse3D_OpenChat",
			EInputEvent.IE_Pressed,
			IrisSafeHouseSubStageLogic._OnOpenChat,
			nil,
			EDisplayInputActionPriority.UI_HUD
		)
		table.insert(IrisSafeHouseSubStageLogic.actionHandles, chatActionHandle)
	
		local exitPanelActionHandle = inputMonitor:AddDisplayActionBinding(
			"SafeHouse3D_Back",
			EInputEvent.IE_Pressed,
			IrisSafeHouseSubStageLogic._OnOpenExitPanel,
			nil,
			EDisplayInputActionPriority.UI_HUD
		)
		table.insert(IrisSafeHouseSubStageLogic.actionHandles, exitPanelActionHandle)

		local leave3DSafeHouseHandle = inputMonitor:AddDisplayActionBinding(
			"Exit3DSafeHouse",
			EInputEvent.IE_Pressed,
			IrisSafeHouseSubStageLogic._OnLeave3DSafeHouse,
			nil,
			EDisplayInputActionPriority.UI_HUD
		)
		-- BEGIN MODIFICATION @ VIRTUOS : 是否绑定特勤处开始游戏的快捷键（用于传递Handler到UI绑定图标控件）
		if IsHD() then
			Module.IrisSafeHouse.Config.evtOnLeave3DSafeHouseActionBound:Invoke(leave3DSafeHouseHandle)
			Module.IrisSafeHouse.Field:SetLeave3DSafeHouseHandler(leave3DSafeHouseHandle)
		end
		-- END MODIFICATION
		table.insert(IrisSafeHouseSubStageLogic.actionHandles, leave3DSafeHouseHandle)
	else
		for _, handle in ipairs(IrisSafeHouseSubStageLogic.actionHandles) do
			inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
		end
		IrisSafeHouseSubStageLogic.actionHandles = {}
		-- BEGIN MODIFICATION @ VIRTUOS : 是否绑定特勤处开始游戏的快捷键（用于传递Handler到UI绑定图标控件）
		if IsHD() then
			Module.IrisSafeHouse.Config.evtOnLeave3DSafeHouseActionBound:Invoke(nil)
			Module.IrisSafeHouse.Field:SetLeave3DSafeHouseHandler(nil)
		end
		-- END MODIFICATION
	end
end

function IrisSafeHouseSubStageLogic._SetActorTick(bEnterSafehouse3D)
	local allNPCs
	allNPCs = UGameplayStatics.GetAllActorsOfClass(GetWorld(), ADFMSafeHouseNPC, allNPCs)
	for _, npc in ipairs(allNPCs) do
		npc:SetActorHiddenInGame(not bEnterSafehouse3D)
		npc:SetActorTickEnabled(bEnterSafehouse3D)

		local widgetComp = npc:GetComponentByClass(UWidgetComponent)
		if widgetComp then
			widgetComp:SetComponentTickEnabled(bEnterSafehouse3D)
		end

		local mesh = npc.MeshComponent
		if mesh then
			mesh:SetComponentTickEnabled(bEnterSafehouse3D)
		end

		for _, mesh in ipairs(npc.EquipSkeleMeshComps) do
			mesh:SetComponentTickEnabled(bEnterSafehouse3D)
		end
	end

	-- local allBuildingProxy
	-- allBuildingProxy = UGameplayStatics.GetAllActorsOfClass(GetWorld(), ASafeHouseBuildingProxy, allBuildingProxy)
	-- for _, buildingProxy in ipairs(allBuildingProxy) do
	-- 	buildingProxy:SetActorTickEnabled(bEnterSafehouse3D)
	-- end

	local allDepartments
	allDepartments = UGameplayStatics.GetAllActorsOfClass(GetWorld(), ASafeHouseDepartment, allDepartments)
	for _, department in ipairs(allDepartments) do
		department:SetActorTickEnabled(bEnterSafehouse3D)
		
		local widgetComp = department.TitleWidgetCmp
		if widgetComp then
			widgetComp:SetComponentTickEnabled(bEnterSafehouse3D)
		end
	end

	local allBuildings
	allBuildings = UGameplayStatics.GetAllActorsOfClass(GetWorld(), ASafeHouseBuilding, allBuildings)
	for _, building in ipairs(allBuildings) do
		building:SetActorTickEnabled(bEnterSafehouse3D)

		local widgetComp = building.Interaction3DTipWidgetCmp
		if widgetComp then
			widgetComp:SetComponentTickEnabled(bEnterSafehouse3D)
		end
	end

	local extraSafehouseSKMActors
    extraSafehouseSKMActors = UGameplayStatics.GetAllActorsWithTag(dfmCharacter, "SafeHouseSKM", extraSafehouseSKMActors)
	for _, extraSafehouseSKMActor in ipairs(extraSafehouseSKMActors) do
		extraSafehouseSKMActor:SetActorHiddenInGame(not bEnterSafehouse3D)
	end
end

function IrisSafeHouseSubStageLogic._NotifyEnter3DSafeHouse(bEnterSafehouse3D)
	USafeHouseGameplayUtils.NotifyEnter3DSafeHouse(GetWorld(), bEnterSafehouse3D)
end

function IrisSafeHouseSubStageLogic._OnOpenMoss()
	-- TODO
	log("_OnOpenMoss")
end

function IrisSafeHouseSubStageLogic._OnOpenChat()
	log("_OnOpenChat")
	Module.Mail:ShowMainPanel(Module.Mail.Config.ESocialType.Chat)
end

function IrisSafeHouseSubStageLogic._OnOpenExitPanel()
	log("_OnOpenExitPanel")

	if Facade.GameFlowManager:CheckIsInFrontEnd() then
		Facade.UIManager:AsyncShowUI(UIName2ID.SafeHouse3DExitPanel)
	else
		logwarning("_OnOpenExitPanel in game is invalid.")
	end
end

function IrisSafeHouseSubStageLogic._OnLeave3DSafeHouse()
	log("_OnLeave3DSafeHouse")

	if Module.Range:GetRangeLoadState() == 1 then
		log("_OnLeave3DSafeHouse skip while loading range")
		return
	end

	local subStage = Facade.GameFlowManager:GetCurrentSubStage()
	if subStage == ESubStage.SafeHouse3D then
		log("OnToggle3DSafeHouse Leave3DSafeHouse")

		IrisSafeHouseSubStageLogic.Leave3DSafeHouse(true)
		LogAnalysisTool.SignButtonClicked(10020001)
	end
end

function IrisSafeHouseSubStageLogic._LoadSafeHouseConfig()
	log("_LoadSafeHouseConfig")
	-- local seamlessTravel = UGPClientSeamlessTravel.Get(GetWorld())
	-- if seamlessTravel then
	-- 	seamlessTravel:LoadStreamingConfig("Iris_Entry", "SafeHouse")
	-- end

	local worldComposition = GetWorld().WorldComposition
	if not worldComposition then
		logwarning("_LoadSafeHouseConfig not world composition", GetWorld())
		return
	end

	local secondaryWorlds = worldComposition.SecondaryWorlds
	local currentExtraScanMaps = {}
	for mapName, _ in pairs(secondaryWorlds) do
		table.insert(currentExtraScanMaps, mapName)
	end
	if not table.contains(currentExtraScanMaps, "SafeHouse") then
		table.insert(currentExtraScanMaps, "SafeHouse")

		worldComposition:UseStreamingSettings("Iris_Entry", currentExtraScanMaps)
	end
end

function IrisSafeHouseSubStageLogic._UnloadSafeHouseConfig()
	log("_UnloadSafeHouseConfig")
	-- local seamlessTravel = UGPClientSeamlessTravel.Get(GetWorld())
	-- if seamlessTravel then
	-- 	seamlessTravel:LoadStreamingConfig("Iris_Entry", "SafeHouse")
	-- end

	local worldComposition = GetWorld().WorldComposition
	if not worldComposition then
		logwarning("_UnloadSafeHouseConfig not world composition", GetWorld())
		return
	end

	local secondaryWorlds = worldComposition.SecondaryWorlds
	local currentExtraScanMaps = {}
	for _, mapName in ipairs(secondaryWorlds) do
		table.insert(currentExtraScanMaps, mapName)
	end
	worldComposition:UseStreamingSettings("Iris_Entry", currentExtraScanMaps)

	worldComposition:ReleasePlayerLocation()
end

function IrisSafeHouseSubStageLogic._ShouldUnload3DSafeHouse()
	if IsHD() then
		return false
	end

	-- 手游低内存设备，退出3D特勤处需要卸载场景
	return Facade.UIManager:GetIsLowMemoryState()
end

function IrisSafeHouseSubStageLogic._OnExit3DSafeHouse()
	log("_OnExit3DSafeHouse")

	IrisSafeHouseSubStageLogic.Leave3DSafeHouse(true)
end

function IrisSafeHouseSubStageLogic._StartCheckFalling()
	local duration = 1
	local time = 10

	log("_StartCheckFalling", duration, time)
	IrisSafeHouseSubStageLogic.checkFallingHandle = Timer.DelayCallSomeTimes(duration, time, IrisSafeHouseSubStageLogic._CheckFalling)
end

function IrisSafeHouseSubStageLogic._StopCheckFalling()
	log("_StopCheckFalling")

	if IrisSafeHouseSubStageLogic.checkFallingHandle then
		Timer.CancelDelay(IrisSafeHouseSubStageLogic.checkFallingHandle)
		IrisSafeHouseSubStageLogic.checkFallingHandle = nil
	end
end

function IrisSafeHouseSubStageLogic._CheckFalling()
	local dfmCharacter = InGameController:Get():GetGPCharacter()
	if not dfmCharacter then
		return
	end

	local bFalling = dfmCharacter:IsFalling()
	local posZ = dfmCharacter:GetActorLocation().Z
	local threshold = -25000
	log("_CheckFalling", bFalling, posZ)

	-- if bFalling and posZ < threshold then
	if posZ < threshold then
		log("_CheckFalling character is falling, fallback to teleport.")

		IrisSafeHouseSubStageLogic.TeleportTo3DSafeHouseDefaultLoc()

		IrisSafeHouseSubStageLogic._StopCheckFalling()
		IrisSafeHouseSubStageLogic._StartCheckFalling()
	end
end

function IrisSafeHouseSubStageLogic._CheckSafeHouseLevelHasLoaded()
	
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Download

function IrisSafeHouseSubStageLogic.OnPakDownloadResult(moduleName, bSuccess)
    local safehouseModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SafeHouse)
	if moduleName == safehouseModuleName and bSuccess then
		log("OnPakDownloadResult success.")

		local worldComposition = GetWorld().WorldComposition
		if worldComposition then
			-- worldComposition:RequestReinitialize()
			worldComposition:Reinitialize()
		end
	end
end

function IrisSafeHouseSubStageLogic._CheckSafeHousePakDownload()
	-- PC不关心小包
	if IsHD() then
		return true
	end

    local safehouseModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SafeHouse)
	local bDownload = LiteDownloadManager:IsDownloadedByModuleName(safehouseModuleName)
	if bDownload then
		return true
	end

	local bDownloading = LiteDownloadManager:IsDownloadedByModuleName(safehouseModuleName)
	log("_CheckSafeHousePakDownload", bDownloading)
	if bDownloading then
		Module.LitePackage:ShowMainPanel()
	else
		local function fOnConfirm()
			log("_CheckSafeHousePakDownload Confirm download.")
			LiteDownloadManager:DownloadByModuleName(safehouseModuleName)

			Module.LitePackage:ShowMainPanel()
		end

		local pakTotalSize = LiteDownloadManager:GetTotalSizeByModuleName(safehouseModuleName)
		local pakNowSize = LiteDownloadManager:GetNowSizeByModuleName(safehouseModuleName)
		local pakTotalSizeMBStr = string.format("%.1f", (pakTotalSize - pakNowSize) / 1024 / 1024)
		local showStr = StringUtil.Key2StrFormat(IrisSafeHouseConfig.Loc.SafeHousePakDownloadConfirmText, {["PakSize"] = pakTotalSizeMBStr})
		Module.CommonTips:ShowConfirmWindow(showStr, fOnConfirm)
	end

	return false
end


--endregion
-----------------------------------------------------------------------

return IrisSafeHouseSubStageLogic