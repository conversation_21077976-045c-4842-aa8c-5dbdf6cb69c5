--cs_rank.protoencode&decode functions.
function pb.pb_RankListDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankList) or {} 
    local __season_no = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __season_no ~= 0 then tb.season_no = __season_no end
    tb.items = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.items[k] = pb.pb_RankItemDecode(v)
    end
    return tb
end

function pb.pb_RankListEncode(tb, encoder)
    if(tb.season_no) then    encoder:addu32(1, tb.season_no)    end
    if(tb.items) then
        for i=1,#(tb.items) do
            pb.pb_RankItemEncode(tb.items[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSRankItemExDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankItemExData) or {} 
    local __value = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_CSRankItemExDataEncode(tb, encoder)
    if(tb.value) then    encoder:addi64(1, tb.value)    end
end

function pb.pb_RankItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankItem) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __nick = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __nick ~= "" then tb.nick = __nick end
    local __logo = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __logo ~= "" then tb.logo = __logo end
    local __game_center = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __game_center ~= 0 then tb.game_center = __game_center end
    local __plat_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    tb.ex_data = pb.pb_CSRankItemExDataDecode(decoder:getsubmsg(8))
    local __nick_name = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __nick_name ~= "" then tb.nick_name = __nick_name end
    local __pic_url = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __pic_url ~= "" then tb.pic_url = __pic_url end
    local __level = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __season_level = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __season_level ~= 0 then tb.season_level = __season_level end
    local __sol_rank_score = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __sol_rank_score ~= 0 then tb.sol_rank_score = __sol_rank_score end
    local __sol_attended = decoder:getbool(14)
    if not PB_USE_DEFAULT_TABLE or __sol_attended ~= false then tb.sol_attended = __sol_attended end
    local __mp_rank_score = decoder:getu64(15)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_score ~= 0 then tb.mp_rank_score = __mp_rank_score end
    local __mp_attended = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __mp_attended ~= false then tb.mp_attended = __mp_attended end
    local __is_privacy = decoder:getbool(17)
    if not PB_USE_DEFAULT_TABLE or __is_privacy ~= false then tb.is_privacy = __is_privacy end
    local __mp_commander_rank_score = decoder:getu64(18)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_rank_score ~= 0 then tb.mp_commander_rank_score = __mp_commander_rank_score end
    local __show_commander_rank_points = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __show_commander_rank_points ~= 0 then tb.show_commander_rank_points = __show_commander_rank_points end
    return tb
end

function pb.pb_RankItemEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
    if(tb.nick) then    encoder:addstr(3, tb.nick)    end
    if(tb.logo) then    encoder:addstr(4, tb.logo)    end
    if(tb.game_center) then    encoder:addu32(5, tb.game_center)    end
    if(tb.plat_id) then    encoder:addi32(6, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(7, tb.account_type)    end
    if(tb.ex_data) then    pb.pb_CSRankItemExDataEncode(tb.ex_data, encoder:addsubmsg(8))    end
    if(tb.nick_name) then    encoder:addstr(9, tb.nick_name)    end
    if(tb.pic_url) then    encoder:addstr(10, tb.pic_url)    end
    if(tb.level) then    encoder:addu32(11, tb.level)    end
    if(tb.season_level) then    encoder:addu32(12, tb.season_level)    end
    if(tb.sol_rank_score) then    encoder:addu64(13, tb.sol_rank_score)    end
    if(tb.sol_attended) then    encoder:addbool(14, tb.sol_attended)    end
    if(tb.mp_rank_score) then    encoder:addu64(15, tb.mp_rank_score)    end
    if(tb.mp_attended) then    encoder:addbool(16, tb.mp_attended)    end
    if(tb.is_privacy) then    encoder:addbool(17, tb.is_privacy)    end
    if(tb.mp_commander_rank_score) then    encoder:addu64(18, tb.mp_commander_rank_score)    end
    if(tb.show_commander_rank_points) then    encoder:addi64(19, tb.show_commander_rank_points)    end
end

function pb.pb_CSRankGetListReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetListReq) or {} 
    local __rank_data_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_data_type ~= 0 then tb.rank_data_type = __rank_data_type end
    local __adcode = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __adcode ~= 0 then tb.adcode = __adcode end
    local __rank_board_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __rank_board_id ~= 0 then tb.rank_board_id = __rank_board_id end
    local __page_size = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __page_size ~= 0 then tb.page_size = __page_size end
    local __page_num = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __page_num ~= 0 then tb.page_num = __page_num end
    return tb
end

function pb.pb_CSRankGetListReqEncode(tb, encoder)
    if(tb.rank_data_type) then    encoder:addu32(1, tb.rank_data_type)    end
    if(tb.adcode) then    encoder:addu32(2, tb.adcode)    end
    if(tb.rank_board_id) then    encoder:addu32(3, tb.rank_board_id)    end
    if(tb.page_size) then    encoder:addu32(4, tb.page_size)    end
    if(tb.page_num) then    encoder:addu32(5, tb.page_num)    end
end

function pb.pb_CSRankGetListResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetListRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __total = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total ~= 0 then tb.total = __total end
    local __page_num = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __page_num ~= 0 then tb.page_num = __page_num end
    local __page_max = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __page_max ~= 0 then tb.page_max = __page_max end
    local __page_size = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __page_size ~= 0 then tb.page_size = __page_size end
    tb.list = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.list[k] = pb.pb_RankItemDecode(v)
    end
    local __self_rank_no = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __self_rank_no ~= 0 then tb.self_rank_no = __self_rank_no end
    return tb
end

function pb.pb_CSRankGetListResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.total) then    encoder:addu32(2, tb.total)    end
    if(tb.page_num) then    encoder:addu32(3, tb.page_num)    end
    if(tb.page_max) then    encoder:addu32(4, tb.page_max)    end
    if(tb.page_size) then    encoder:addu32(5, tb.page_size)    end
    if(tb.list) then
        for i=1,#(tb.list) do
            pb.pb_RankItemEncode(tb.list[i], encoder:addsubmsg(6))
        end
    end
    if(tb.self_rank_no) then    encoder:addi32(7, tb.self_rank_no)    end
end

function pb.pb_CSRankGetHandbookReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetHandbookReq) or {} 
    local __hand_book_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __hand_book_id ~= 0 then tb.hand_book_id = __hand_book_id end
    return tb
end

function pb.pb_CSRankGetHandbookReqEncode(tb, encoder)
    if(tb.hand_book_id) then    encoder:addu64(1, tb.hand_book_id)    end
end

function pb.pb_CSRankPlayerHandBookDataTypeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPlayerHandBookDataTypeInfo) or {} 
    local __rank_datatype_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_datatype_id ~= 0 then tb.rank_datatype_id = __rank_datatype_id end
    tb.rank_single_hand_book_data = pb.pb_RankSingleHandBookDataDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSRankPlayerHandBookDataTypeInfoEncode(tb, encoder)
    if(tb.rank_datatype_id) then    encoder:addu32(1, tb.rank_datatype_id)    end
    if(tb.rank_single_hand_book_data) then    pb.pb_RankSingleHandBookDataEncode(tb.rank_single_hand_book_data, encoder:addsubmsg(2))    end
end

function pb.pb_CSRankPlayerHandBookDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPlayerHandBookData) or {} 
    local __handbook_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __handbook_id ~= 0 then tb.handbook_id = __handbook_id end
    tb.rank_hand_book_data_type_info = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.rank_hand_book_data_type_info[k] = pb.pb_CSRankPlayerHandBookDataTypeInfoDecode(v)
    end
    return tb
end

function pb.pb_CSRankPlayerHandBookDataEncode(tb, encoder)
    if(tb.handbook_id) then    encoder:addu64(1, tb.handbook_id)    end
    if(tb.rank_hand_book_data_type_info) then
        for i=1,#(tb.rank_hand_book_data_type_info) do
            pb.pb_CSRankPlayerHandBookDataTypeInfoEncode(tb.rank_hand_book_data_type_info[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSRankGetHandbookResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetHandbookRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __hand_book_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __hand_book_id ~= 0 then tb.hand_book_id = __hand_book_id end
    tb.rank_hand_book_data_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.rank_hand_book_data_list[k] = pb.pb_CSRankPlayerHandBookDataDecode(v)
    end
    return tb
end

function pb.pb_CSRankGetHandbookResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.hand_book_id) then    encoder:addu64(2, tb.hand_book_id)    end
    if(tb.rank_hand_book_data_list) then
        for i=1,#(tb.rank_hand_book_data_list) do
            pb.pb_CSRankPlayerHandBookDataEncode(tb.rank_hand_book_data_list[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSRankPrivacySetReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPrivacySetReq) or {} 
    local __is_show_on_rank = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __is_show_on_rank ~= false then tb.is_show_on_rank = __is_show_on_rank end
    return tb
end

function pb.pb_CSRankPrivacySetReqEncode(tb, encoder)
    if(tb.is_show_on_rank) then    encoder:addbool(1, tb.is_show_on_rank)    end
end

function pb.pb_CSRankPrivacySetResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPrivacySetRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRankPrivacySetResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSRankPrivacyGetReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPrivacyGetReq) or {} 
    local __is_show_on_rank = decoder:getbool(34)
    if not PB_USE_DEFAULT_TABLE or __is_show_on_rank ~= false then tb.is_show_on_rank = __is_show_on_rank end
    return tb
end

function pb.pb_CSRankPrivacyGetReqEncode(tb, encoder)
    if(tb.is_show_on_rank) then    encoder:addbool(34, tb.is_show_on_rank)    end
end

function pb.pb_CSRankPrivacyGetResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPrivacyGetRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __is_show_on_rank = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_show_on_rank ~= false then tb.is_show_on_rank = __is_show_on_rank end
    return tb
end

function pb.pb_CSRankPrivacyGetResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.is_show_on_rank) then    encoder:addbool(2, tb.is_show_on_rank)    end
end

function pb.pb_CSRankZoneSetReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankZoneSetReq) or {} 
    local __rank_adcode = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_adcode ~= 0 then tb.rank_adcode = __rank_adcode end
    return tb
end

function pb.pb_CSRankZoneSetReqEncode(tb, encoder)
    if(tb.rank_adcode) then    encoder:addu32(1, tb.rank_adcode)    end
end

function pb.pb_CSRankZoneSetResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankZoneSetRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRankZoneSetResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
end

function pb.pb_CSRankZoneGetReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankZoneGetReq) or {} 
    local __none = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __none ~= 0 then tb.none = __none end
    return tb
end

function pb.pb_CSRankZoneGetReqEncode(tb, encoder)
    if(tb.none) then    encoder:addi32(1, tb.none)    end
end

function pb.pb_CSRankZoneGetResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankZoneGetRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __cur_adcode = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __cur_adcode ~= 0 then tb.cur_adcode = __cur_adcode end
    local __next_adcode = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __next_adcode ~= 0 then tb.next_adcode = __next_adcode end
    return tb
end

function pb.pb_CSRankZoneGetResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.cur_adcode) then    encoder:addu32(2, tb.cur_adcode)    end
    if(tb.next_adcode) then    encoder:addu32(3, tb.next_adcode)    end
end

function pb.pb_CSRankGetPlayerRankAuxDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerRankAuxDataReq) or {} 
    local __data_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __data_type ~= 0 then tb.data_type = __data_type end
    return tb
end

function pb.pb_CSRankGetPlayerRankAuxDataReqEncode(tb, encoder)
    if(tb.data_type) then    encoder:addu32(1, tb.data_type)    end
end

function pb.pb_CSRankGetPlayerRankAuxDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerRankAuxDataRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.rank_player_aux_data = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.rank_player_aux_data[k] = pb.pb_CSRankPlayerAuxDataDecode(v)
    end
    return tb
end

function pb.pb_CSRankGetPlayerRankAuxDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.rank_player_aux_data) then
        for i=1,#(tb.rank_player_aux_data) do
            pb.pb_CSRankPlayerAuxDataEncode(tb.rank_player_aux_data[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_CSPlayerAuxDataMatchInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPlayerAuxDataMatchInfo) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __score = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __time = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    local __score_sum = decoder:getdouble(4)
    if not PB_USE_DEFAULT_TABLE or __score_sum ~= 0 then tb.score_sum = __score_sum end
    local __match_mode_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __match_mode_id ~= 0 then tb.match_mode_id = __match_mode_id end
    local __timestamp = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    return tb
end

function pb.pb_CSPlayerAuxDataMatchInfoEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.score) then    encoder:adddouble(2, tb.score)    end
    if(tb.time) then    encoder:addfloat(3, tb.time)    end
    if(tb.score_sum) then    encoder:adddouble(4, tb.score_sum)    end
    if(tb.match_mode_id) then    encoder:addu64(5, tb.match_mode_id)    end
    if(tb.timestamp) then    encoder:addi64(6, tb.timestamp)    end
end

function pb.pb_CSRankGetPlayerRankAuxDataDetailReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerRankAuxDataDetailReq) or {} 
    local __data_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __data_type ~= 0 then tb.data_type = __data_type end
    return tb
end

function pb.pb_CSRankGetPlayerRankAuxDataDetailReqEncode(tb, encoder)
    if(tb.data_type) then    encoder:addu32(1, tb.data_type)    end
end

function pb.pb_CSRankGetPlayerRankAuxDataDetailResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerRankAuxDataDetailRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.rank_player_aux_data_detail = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.rank_player_aux_data_detail[k] = pb.pb_CSPlayerAuxDataMatchInfoDecode(v)
    end
    local __match_time_limit = decoder:getfloat(4)
    if not PB_USE_DEFAULT_TABLE or __match_time_limit ~= 0 then tb.match_time_limit = __match_time_limit end
    return tb
end

function pb.pb_CSRankGetPlayerRankAuxDataDetailResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.rank_player_aux_data_detail) then
        for i=1,#(tb.rank_player_aux_data_detail) do
            pb.pb_CSPlayerAuxDataMatchInfoEncode(tb.rank_player_aux_data_detail[i], encoder:addsubmsg(3))
        end
    end
    if(tb.match_time_limit) then    encoder:addfloat(4, tb.match_time_limit)    end
end

function pb.pb_CSRankBatchGetAuxDataReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankBatchGetAuxDataReq) or {} 
    tb.player_ids = decoder:getu64ary(1)
    return tb
end

function pb.pb_CSRankBatchGetAuxDataReqEncode(tb, encoder)
    if(tb.player_ids) then    encoder:addu64(1, tb.player_ids)    end
end

function pb.pb_CSRankBatchGetAuxDataResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankBatchGetAuxDataRes) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.player_aux_data = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.player_aux_data[k] = pb.pb_SinglePlayerAuxDataDecode(v)
    end
    return tb
end

function pb.pb_CSRankBatchGetAuxDataResEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.player_aux_data) then
        for i=1,#(tb.player_aux_data) do
            pb.pb_SinglePlayerAuxDataEncode(tb.player_aux_data[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_CSRankCheckPopUpWindowReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankCheckPopUpWindowReq) or {} 
    return tb
end

function pb.pb_CSRankCheckPopUpWindowReqEncode(tb, encoder)
end

function pb.pb_CSRankCheckPopUpWindowResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankCheckPopUpWindowRes) or {} 
    local __last_weekly_reward_time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __last_weekly_reward_time ~= 0 then tb.last_weekly_reward_time = __last_weekly_reward_time end
    return tb
end

function pb.pb_CSRankCheckPopUpWindowResEncode(tb, encoder)
    if(tb.last_weekly_reward_time) then    encoder:addi64(1, tb.last_weekly_reward_time)    end
end

function pb.pb_CSRankGetPlayerRankPercentageReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerRankPercentageReq) or {} 
    --unsupport field name:pb.CSRankGetPlayerRankPercentageReq.rank_data_type type:enum(14) num:1 repeat:1
    return tb
end

function pb.pb_CSRankGetPlayerRankPercentageReqEncode(tb, encoder)
    if(tb.rank_data_type) then    encoder:addu32(1, tb.rank_data_type)    end
end

function pb.pb_CSRankGetPlayerRankPercentageResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerRankPercentageRes) or {} 
    tb.list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.list[k] = pb.pb_PlayerRankPercentageInfoDecode(v)
    end
    local __result = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRankGetPlayerRankPercentageResEncode(tb, encoder)
    if(tb.list) then
        for i=1,#(tb.list) do
            pb.pb_PlayerRankPercentageInfoEncode(tb.list[i], encoder:addsubmsg(1))
        end
    end
    if(tb.result) then    encoder:addi32(2, tb.result)    end
end

function pb.pb_CSRankGetPlayerHistoricalRecordReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerHistoricalRecordReq) or {} 
    return tb
end

function pb.pb_CSRankGetPlayerHistoricalRecordReqEncode(tb, encoder)
end

function pb.pb_CSRankGetPlayerHistoricalRecordResDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankGetPlayerHistoricalRecordRes) or {} 
    tb.record_info = pb.pb_PlayerRankHistoricalRecordInfoDecode(decoder:getsubmsg(1))
    local __result = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CSRankGetPlayerHistoricalRecordResEncode(tb, encoder)
    if(tb.record_info) then    pb.pb_PlayerRankHistoricalRecordInfoEncode(tb.record_info, encoder:addsubmsg(1))    end
    if(tb.result) then    encoder:addi32(2, tb.result)    end
end

