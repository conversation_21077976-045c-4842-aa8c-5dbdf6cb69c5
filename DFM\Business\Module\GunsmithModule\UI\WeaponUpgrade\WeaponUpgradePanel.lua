----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
-- END MODIFICATION

local GunsmithSimulateStatePropmtUI = require "DFM.Business.Module.GunsmithModule.UI.Simulate.GunsmithSimulateStatePropmtUI"
local DFCommonButtonOnly = require "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonButtonOnly"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local GunsmithUI = require "DFM.Business.Module.GunsmithModule.UI.GunsmithUI"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EAssemblerCameraType = import "EAssemblerCameraType"
local EAssemblerCamPoint = import "EAssemblerCamPoint"
local UPartsDataTableManager = import "PartsDataTableManager"
local FAnchors = import "Anchors"
local DFCommonProgressBarOnly = require"DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonProgressBarOnly"
local GunsmithPartUnlockLogic = require "DFM.Business.Module.GunsmithModule.Logic.Unlock.GunsmithPartUnlockLogic"
local Config = Module.Gunsmith.Config

local EGunsmithMainPreviewUIPagination = {
    Default     = 0,
    Detail      = 1,
    Mission     = 2,
}

---@class WeaponUpgradePanel : LuaUIBaseView
--武器界面
local WeaponUpgradePanel = ui("WeaponUpgradePanel", GunsmithUI)

function WeaponUpgradePanel:Ctor()
	--返回键通用名称
	if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    end
	Module.CommonBar:RegStackUITopBarTitle(self, Module.Gunsmith.Config.Loc.GunsmithLevelUp)

	--武器名称
	self._wtWeaponNameTxt = self:Wnd("wNameLabel", UITextBlock)
	--武器详情
	self._wtWeaponBtn = self:Wnd("wtCheckBoxLookWeaponModel", DFCommonCheckButtonOnly)
	self._wtWeaponBtn:Event("OnCheckedBoxStateChangedNative", self._OnWeaponNewsClicked, self)
	--武器等级
	self._wtWeaponLevelTxt = self:Wnd("DFTextBlock_52", UITextBlock)
	--武器最高等级
	self._wtWeaponMaxLevelTxt = self:Wnd("DFTextBlock_154", UITextBlock)
	--武器当前等级经验比
	self._wtWeaponExpTxt = self:Wnd("DFTextBlock", UITextBlock)
	--武器当前等级经验值DFRadialImage
	self._wtProgressBarOnly = self:Wnd("WBP_DFCommonProgressBarV1", DFCommonProgressBarOnly)
	if self._wtProgressBarOnly then
		self._wtProgressBarOnly:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        self._wtProgressBar = self._wtProgressBarOnly.wtCommonProgressBar
    end
	--武器升级按钮
	self._wtWeaponUpBtn = self:Wnd("WBP_DFCommonButtonV2S1", DFCommonButtonOnly)
	self._wtWeaponUpBtn:Event("OnClicked",self._OnWeaponUpgradeClicked, self)
	--武器详情页
	self._wtWeaponInfo = self:Wnd("WBP_ItemDetailView_Equipped_C_0", UIWidgetBase)
	--武器配件详情页
	self._wtWeaponFittInfo = self:Wnd("WBP_GunStand_GunsmithDetailPartMainUI", UIWidgetBase)
	--- BEGIN MODIFICATION @ VIRTUOS
	if IsHD() then
		self._wtWeaponDetailView = self._wtWeaponFittInfo:Wnd("WBP_ItemDetailView_Equipped", ItemDetailView)
	end
	--- END MODIFICATION
	--详情页属性
	self._wtWeaponDetail = self._wtWeaponFittInfo:Wnd("WBP_ItemDetail_WeaponDetail_Equipped_37", UIWidgetBase)
	--武器名称/分割线(隐藏)
	self._wtNameLabel = self._wtWeaponDetail:Wnd("wNameLabel", UIWidgetBase)
	self._wtDFLineImage = self._wtWeaponDetail:Wnd("DFImage_76", UIWidgetBase)
	--武器属性详细数据(隐藏)
	self._wtWeaponNatures = {
		[1] = self._wtWeaponDetail:Wnd("wtDFCommonCheckBoxWithText", UIWidgetBase),
		[2] = self._wtWeaponDetail:Wnd("DFImage_91", UIWidgetBase),
		[3] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect_4", UIWidgetBase),
		[4] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect_5", UIWidgetBase),
		[5] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect", UIWidgetBase),
		[6] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect_1", UIWidgetBase),
		[7] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect_2", UIWidgetBase),
		[8] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect_3", UIWidgetBase),
		[9] = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_Effect_6", UIWidgetBase),
		[10] = self._wtWeaponDetail:Wnd("DFImage", UIWidgetBase),
		[11] = self._wtWeaponDetail:Wnd("wDesc", UIWidgetBase),
	}
	--初始化配件
	self._wtDFWrapBox = self:Wnd("DFWrapBox_77", UIWidgetBase)
	self._wtDFCanvasPanel = self:Wnd("DFCanvasPanel_36", UIWidgetBase)
	--预安装
	self._wtStatePropmtUI = self:Wnd("WBP_GunStand_GunsmithSimulateStatePropmtUI", GunsmithSimulateStatePropmtUI)

	--详细属性(跳转)
	self._wtInfo = self._wtWeaponDetail:Wnd("WBP_DFCommonButtonV3S1", DFCommonButtonOnly)
	self._wtInfo:RemoveEvent("OnClicked")
	self._wtInfo:Event("OnClicked", self._OnAssociatedFirearmsClicked, self)
	--配件属性显示(控制)
	self._wtLimitInfo1 = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_LimitGroup_5", ItemDetailContentLimitGroup)
	self._wtLimitInfo2 = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_LimitGroup_1", ItemDetailContentLimitGroup)
	self._wtLimitInfo3 = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_LimitGroup_2", ItemDetailContentLimitGroup)
	self._wtLimitInfo4 = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_LimitGroup", ItemDetailContentLimitGroup)
	self._wtLimitInfo5 = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_LimitGroup_4", ItemDetailContentLimitGroup)
	self._wtLimitInfo6 = self._wtWeaponDetail:Wnd("WBP_ItemDetailContent_LimitGroup_3", ItemDetailContentLimitGroup)
	self._wtLimitInfo = {
		["DisplayAttrValues.1"] = self._wtLimitInfo1,--射程
		["DisplayAttrValues.2"] = self._wtLimitInfo2,--后坐
		["DisplayAttrValues.3"] = self._wtLimitInfo3,--操控
		["DisplayAttrValues.5"] = self._wtLimitInfo4,--稳定
		["DisplayAttrValues.6"] = self._wtLimitInfo5,--腰射
		["DisplayAttrValues.4"] = self._wtLimitInfo6,--伤害
	}
	--武器配件列表
	self._wtScrollGridBox = UIUtil.WndWaterfallScrollBox(self, "wLvlScrollGridBox", self._OnGetItemCount, self._OnCreateItemWidget, nil, self._OnGetItemSize)
	--容器
	self._wtCanvasPanel = self:Wnd("CanvasPanel_0", UIWidgetBase)
	--武器配件进度控件(PC/手游)
	self._wtUpgradeSchedulePC = self:Wnd("WBP_Equipment_UpgradeSchedule_PCOnly", UIWidgetBase)
	self._wtUpgradeSchedule = self:Wnd("WBP_Equipment_UpgradeSchedule", UIWidgetBase)

	--配件详情页控制
	self._isPartInfo = false

	self._playAnimationTimerHandle = nil
	self._scrollGridBoxTimerHandle = nil
	-- BEGIN MODIFICATION @ VIRTUOS : 在刷新Item的时候将其添加至手柄的导航的事件
	if IsHD() then
		self:AddLuaEvent(Module.Gunsmith.Config.Events.evtOnGunsmithUpgradeItemUIInitItemData, self._OnGunsmithItemInitItemData, self)
	end
	-- END MODIFICATION
	self._wtDFCanvas = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
	--杀人星级
	self._wtSharkIcon = self:Wnd("DFImage_98", UIImage)
	self._wtCheck     = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
	if IsHD() then
		self._wtAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_114", self._OnHovered, self._OnUnhovered)
	else
		self._wtAnchor = self:Wnd("DFTipsAnchor_114", UIWidgetBase)
		self._wtCheck:SetCallback(self._OnCheckChanged, self)
	end
end

--#region 杀人升星 2025-5-12 ssy
function WeaponUpgradePanel:_OnHovered()
    self:_OnShowDescriptionTip()
end

function WeaponUpgradePanel:_OnUnhovered()
    self:_OnHideDescriptionTip()
end

function WeaponUpgradePanel:_OnShowDescriptionTip()
    self:_OnHideDescriptionTip()
    local contents = {
		{
			id = UIName2ID.Assembled_CommonMessageTips_V8,
			background = 1,
			data = {
				textContent = Config.Loc.GunsmithWeaponStarTipsTitle,
				styleRowId  = "C002",
				isLine = false,
			},
    	},
		{
			id = UIName2ID.Assembled_CommonMessageTips_V2,
			background = 1,
			data = {
				textContent = Config.Loc.GunsmithWeaponStarTipsDes,
				styleRowId  = "C002",
			},
		}
	}
    if #contents > 0 then
        self._handle = Module.CommonTips:ShowAssembledTips(contents, self._wtAnchor)
    end
end

function WeaponUpgradePanel:_OnHideDescriptionTip()
    if self._handle and self._handle.GetUIIns then
        self._wtCheck:SetIsChecked(false, false)
        Module.CommonTips:RemoveAssembledTips(self._handle, self._wtAnchor)
        self._handle = nil
    end
end

function WeaponUpgradePanel:_OnCheckChanged(bChecked)
    if bChecked then
        self:_OnShowDescriptionTip()
    else
        self:_OnHideDescriptionTip()
    end
end

--杀人星级
function WeaponUpgradePanel:_KillingRisingStar()
	if self._weaponItem and self._levelCfgs then
		local curLevel
		local weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
		if weaponFeature then
			curLevel = weaponFeature:GetWeaponLevel()
		end
		if curLevel and weaponFeature then
			local data = self._levelCfgs[curLevel]
			if data and data.Exp then
				--杀人星星数量
				local starNum = weaponFeature:GetStarNum()
				local killNum = weaponFeature:GetKillCount()
				local killMax = weaponFeature:GetKillCountPerStar()
				local starMax = DFMGlobalConst.GetGlobalConstNumber("WeaponStarMaxNum", 0)
				--杀人图标
				if starNum > 0 and data.Exp == 0 then
					self._wtCheck:Visible()
					self._wtSharkIcon:SelfHitTestInvisible()
				else
					self._wtCheck:Collapsed()
					self._wtSharkIcon:Collapsed()
				end
				if starNum == 0 then
					return
				end
				if data.Exp == 0 and starNum and killNum and killMax and starMax then
					local name = string.format(Config.Loc.GunsmithWeaponStarTipsTitles, starNum or "")

					if starNum >= starMax then
						name = StringUtil.Key2StrFormat(Config.Loc.WeaponHighestStarRating, {star = name})
					end
					--进度
					local curStar = killNum % killMax
					local maxStar = killMax
					local percent = curStar / maxStar
					self._wtWeaponLevelTxt:SetText(name or "")
					self._wtProgressBar:SetPercent(percent or 0)
					self._wtWeaponExpTxt:SetText(string.format("%s/%s", curStar, maxStar))
					--描述(升级按钮)
					if data.Exp == 0 then
						self._wtWeaponLevelTxt:SelfHitTestInvisible()
					else
						self._wtWeaponLevelTxt:Collapsed()
					end
					self._wtWeaponMaxLevelTxt:Collapsed()
					self._wtDFCanvas:Collapsed()
					--进度数据
					if starNum < starMax then
						self._wtProgressBar:SelfHitTestInvisible()
						self._wtWeaponExpTxt:SelfHitTestInvisible()
					else
						self._wtProgressBar:Collapsed()
						self._wtWeaponExpTxt:Collapsed()
					end
				end
			end
		end
	end
end
--#endregion

--武器数据
function WeaponUpgradePanel:_InitWeapon()
	if self._weaponItem and self._weaponItem.id then
		self._levelCfgs = Server.WeaponAssemblyServer:GetWeaponLevelUnLockData(self._weaponItem.id)
		local weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
		if weaponFeature then
			self._curWeaponLvl = weaponFeature:GetWeaponLevel()
			self._curWeaponExp = weaponFeature:GetWeaponExp()
		end
		local weapon, parts = Server.GrowthRoadServer:GetWeaponDataById(self._weaponItem.id)
		self._levelList = weapon
		self._firstLevelArr = parts
	end
end

function WeaponUpgradePanel:_InitUnLockLength()
	local weaponFeature, curLvl = nil, 1
	if self._weaponItem == nil then
		return
	end
	weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
	if weaponFeature then
		curLvl = weaponFeature:GetWeaponLevel()
	end

	local list = Server.GrowthRoadServer:GetUnLockPartById(self._weaponItem.id, curLvl)

	--初始化控件显示数据
	if self._wtUpgradeSchedulePC then
		self._wtUpgradeSchedulePC:SetVisibility(ESlateVisibility.Collapsed)
	end
	if self._wtUpgradeSchedule then
		self._wtUpgradeSchedule:SetVisibility(ESlateVisibility.Collapsed)
	end
	if IsHD() then
		self._wtUpgradeSchedulePC:InitItemData(list)
		self._wtUpgradeSchedulePC:SetVisibility(ESlateVisibility.HitTestSelfOnly)
	else
		self._wtUpgradeSchedule:InitItemData(list)
		self._wtUpgradeSchedule:SetVisibility(ESlateVisibility.HitTestSelfOnly)
	end
end

function WeaponUpgradePanel:OnOpen()
	--开启延迟剧中
	self._isDelayedDrama = true
end

function WeaponUpgradePanel:OnShowBegin()
	GunsmithUI.OnShowBegin(self)
	--事件/鼠标点击屏幕事件
	self:_AddEventListener()
	local gamelnst = GetGameInstance()
	self._buttonownHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
	--初始化控件显隐
	self._wtDFCanvasPanel:Collapsed()
	self._wtStatePropmtUI:SetActive(false)
	if IsHD() then
		Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIShow:Invoke()
	end
end

function WeaponUpgradePanel:OnHideBegin()
	GunsmithUI.OnHideBegin(self)
	--关闭延迟剧中
	self._isDelayedDrama = false
	--详情页会自动关闭:所以要清理临时数据,避免下次打开再次选中显示(选中item/武器详情开关)
	self._itemId = nil
	self._wtWeaponBtn:SetIsChecked(false)
	--end
	self:_AddNavigation()
	--退出时再次刷新(剧中):解决(上一次玩家滑动到其他位置后关闭导致)进入时剧中,滑动列表异常显示的问题！！！
	if IsHD() then
		self._wtScrollGridBox:RefreshAllItems()
	end
	self:_WeaponItemDrama()
	--///
	--鼠标事件移除
	if self._buttonownHandle then
		local gamelnst = GetGameInstance()
		UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._buttonownHandle)
		self._buttonownHandle = nil
	end
	self:_RemoveEventListener()
	Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIHide:Invoke()

	Module.ItemDetail:CloseItemDetailPanel()
	self:_OnHideDescriptionTip()
end

function WeaponUpgradePanel:OnHide()
    self:RemoverTimerHandler()
end

function WeaponUpgradePanel:RemoverTimerHandler()
    self:_InternalRemoverPlayAnimationTimerHandler()
    self:_InternalRemoverScrollGridBoxTimerHandler()
end

function WeaponUpgradePanel:_InternalRemoverPlayAnimationTimerHandler()
    if self._playAnimationTimerHandle then
        Timer.CancelDelay(self._playAnimationTimerHandle)
    end
    self._playAnimationTimerHandle = nil
end

function WeaponUpgradePanel:_InternalRemoverScrollGridBoxTimerHandler()
    if self._scrollGridBoxTimerHandle then
        Timer.CancelDelay(self._scrollGridBoxTimerHandle)
    end
    self._scrollGridBoxTimerHandle = nil
end

--初始化数据
function WeaponUpgradePanel:OpenFromMainUI(bFromUserClicked, bIsProcessedContext)
	if bIsProcessedContext then
		GunsmithUIContextLogic.ProcessContext()
   	end
	local gid = GunsmithUIContextLogic.GetGUID()
	local group = GunsmithUIContextLogic.GetGroupID()
	if gid == nil or group == nil then
		Module.CommonTips:ShowSimpleTip(Module.Gunsmith.Config.Loc.GunsmithComminSoon)
		return
	end
	self._weaponItem = Server.InventoryServer:GetItemByGid(gid, group)
	--初始化界面
	self:_InitPanel()
	--初始化镜头
    self:OnProcessUIUpdate(true)
end

function WeaponUpgradePanel:_GetWeaponNewData()
	GunsmithUIContextLogic.ProcessContext()
	local gid = GunsmithUIContextLogic.GetGUID()
	local group = GunsmithUIContextLogic.GetGroupID()
	if gid == nil or group == nil then
		return
	end
	self._weaponItem = Server.InventoryServer:GetItemByGid(gid, group)
end

--初始化界面
function WeaponUpgradePanel:_InitPanel()
	--初始化配件列表
	self:_InitWeapon()
	--初始化配件进度
	self:_InitUnLockLength()
	--初始化界面数据
	self:_InitWeaponData()
	-- 刷新配件列表
	local maxLevel, maxPart = self:_GetUpMaxLevel()
	Module.Gunsmith.Field:IsWeaponPartId(maxLevel, maxPart, true)
	
	self:_AddNavigation(true)
	self._wtScrollGridBox:RefreshAllItems()
	-- --当前等级剧中
	self:_WeaponItemDrama(maxLevel)
	--延迟0.47秒将动画定格到最后一帧,解决快速切换界面显示的异常
	self:_InternalRemoverPlayAnimationTimerHandler()
    self._playAnimationTimerHandle = Timer.DelayCall(0.47, function()
		self:StopAllAnimations()
        self:PlayWidgetAnimAt(self.WBP_Equipment_UpgradeMain1_in, 0.47)
    end, self)
end

--切换镜头
function WeaponUpgradePanel:OnProcessSceneObject()
	self._cameraPoint = EAssemblerCamPoint.POINT_GUN_UPGRADE
    GunsmithUIContextLogic.SetCameraType(EAssemblerCameraType.LOCK)
    GunsmithUIContextLogic.SetCameraDefaultPoint(self._cameraPoint)
    GunsmithUIContextLogic.SetFocusSocket(nil)

    GunsmithUI.OnProcessSceneObject(self)
end

function WeaponUpgradePanel:OnProcessUI()
end

function WeaponUpgradePanel:OnForceProcessUI()
	self._wtStatePropmtUI:UpdateUI()
end

function WeaponUpgradePanel:_GetUpMaxLevel()
	local maxLevel = nil
	local maxPart = nil
	local lockPartsInfo = nil
	local fReturnFunc = function()
		Module.Gunsmith.Field:SetUpgradeLockParts(lockPartsInfo)
		return maxLevel, maxPart
	end

	local bSimulate = GunsmithUIContextLogic.GetIsSimulateState()
	if not bSimulate then
		return fReturnFunc()
	end
	local frontend = GunsmithUIContextLogic.GetWeaponDescription4Frontend()
	local bIsNotValid = isinvalid(frontend)
	if bIsNotValid then
		return fReturnFunc()
	end
	local _
	_, lockPartsInfo = GunsmithUIContextLogic.GetIDsContainedFromWeaponDescription(frontend)
	for index1, value in ipairs(self._levelList or {}) do
		for index2, part in ipairs(value.UnlockParts or {}) do
			for key, lockPart in pairs(lockPartsInfo or {}) do
				if tonumber(lockPart) == tonumber(part) then
					if maxLevel == nil then
						maxLevel = value.Level
						maxPart = tonumber(part)
					else
						if maxLevel < value.Level then
							maxLevel = value.Level
							maxPart = tonumber(part)
						end
					end
				end
			end
		end
	end
	return fReturnFunc()
end

function WeaponUpgradePanel:_WeaponItemDrama(maxLevel)
	--第一次创建资源过多，确保所有的物体创建完成之后在剧中
	if maxLevel then
		maxLevel = self:_GetUpMaxLevel()
	end
	local curLevel = maxLevel or self._curWeaponLvl
	
	if self._isDelayedDrama then
		self:_InternalRemoverScrollGridBoxTimerHandler()
        self._scrollGridBoxTimerHandle = Timer.DelayCall(0, function ()
			if self._wtScrollGridBox and self._levelList and #self._levelList > 0 then
				local curLevelIndex = 1
				for index, value in ipairs(self._levelList or {}) do
					if curLevel >= value.Level then
						curLevelIndex = index
					else
						break
					end
				end
				--减一:解决滑动列表将最后一个元素剧中导致无法显示的问题
				if curLevelIndex == #self._levelList then
					curLevelIndex = curLevelIndex - 1
				end
				self._wtScrollGridBox:ScrollToIndexToScreen(curLevelIndex, 0.5, 0.5)
			end
        end)
    else
		if self._wtScrollGridBox and self._levelList and #self._levelList > 0 then
			local curLevelIndex = 1
			for index, value in ipairs(self._levelList or {}) do
				if curLevel >= value.Level then
					curLevelIndex = index
				else
					break
				end
			end
			--减一:解决滑动列表将最后一个元素剧中导致无法显示的问题
			if curLevelIndex == #self._levelList then
				curLevelIndex = curLevelIndex - 1
			end
			self._wtScrollGridBox:ScrollToIndexToScreen(curLevelIndex, 0.5, 0.5)
		end
    end
end

function WeaponUpgradePanel:_AddEventListener()
	--添加刷新配件详情监听
	self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked, self._OnWeaponModuleNews, self)
	--武器升级回包刷新
	self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionUsePropSucess, self._OnWeaponUpgradeItemUpdate, self)
	--断线重连监听器
	Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)
	self:AddLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate, self._OnProcessServerDataUpdated, self)
	--监听PopOnClose
	self:AddLuaEvent(Module.Gunsmith.Config.Events.evtWeaponUpgradePopOnClose, self._OnWeaponUpgradePopOnClose, self)
	self:AddLuaEvent(Module.Gunsmith.Config.Events.evtWeaponToOneItemView, self._OnWeaponToOneItemView, self)
end

function WeaponUpgradePanel:_OnWeaponUpgradePopOnClose()
	self:_AddNavigation(true)
end

function WeaponUpgradePanel:_OnWeaponToOneItemView()
	self._wtWeaponFittInfo:Collapsed()
	self._wtDFCanvasPanel:Collapsed()
	self:_WhiteFrameCollapsed(false)
end

function WeaponUpgradePanel:_OnRelayConnected()
	self:OpenFromMainUI()
end

--初始化数据
function WeaponUpgradePanel:_InitWeaponData()
	--关联枪械名称
	self._wtInfo:SetMainTitle(Module.Gunsmith.Config.Loc.GunsmithAssociatedFirearms)
	--武器清除box/隐藏名称
	if self._wtWeaponInfo.wItemDetailTitle then
		self._wtWeaponInfo.wItemDetailTitle:SetVisibility(ESlateVisibility.Collapsed)
	end
	--武器详情页
	self._wtWeaponInfo:SetVisibility(ESlateVisibility.Collapsed)
	--武器配件详情页
	self._wtWeaponFittInfo:SetVisibility(ESlateVisibility.Collapsed)
	--武器名称
	if self._weaponItem and self._weaponItem.name then
		self._wtWeaponNameTxt:SetText(self._weaponItem.name)
	end
	--武器升级按钮
	self._wtWeaponUpBtn:SetMainTitle(Module.Gunsmith.Config.Loc.GunsmithUpgrade)
	--武器等级、进度
	self:_SetCurLevel()
end

--关联枪械
function WeaponUpgradePanel:_OnAssociatedFirearmsClicked()
	if self._accessoryData == nil then
		return
	end
	local fFinishCallback = function (instance)
		--是否打开关联枪械
		self._isPartInfo = true

		local commonAnchor = FAnchors()
		commonAnchor.Minimum = FVector2D(0, 0)
		commonAnchor.Maximum = FVector2D(1, 1)
		instance.Slot:SetAnchors(commonAnchor)
		instance.Slot:SetOffsets(FMargin(0, 0, 0, 0))
	end
	local closeFtrearmsCallback = function ()
		--是否关闭关联枪械
		self._isPartInfo = false
	end
	Facade.UIManager:AsyncShowUI(UIName2ID.WeaponUpgradeFirearmPanel, fFinishCallback, nil, self._accessoryData, closeFtrearmsCallback)
end

--重置升级状态
function WeaponUpgradePanel:_ResetWeaponUpgrade()
	--初始化配件进度
	self:_InitUnLockLength()
	--武器等级、进度
	self:_SetCurLevel()
end

--设置武器等级、进度
function WeaponUpgradePanel:_SetCurLevel()
	if self._weaponItem then
		local weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
		if weaponFeature then
			self._curWeaponLvl = weaponFeature:GetWeaponLevel()
			self._curWeaponExp = weaponFeature:GetWeaponExp()
		end
	end
	if self._levelCfgs == nil or self._curWeaponLvl == nil or self._curWeaponExp == nil then
		return
	end
	local expSum, exp, curpercent, maxLevel
	if #self._levelCfgs < self._curWeaponLvl then
		curpercent = 1
		maxLevel = true
	else
		expSum = self._curWeaponExp - self._levelCfgs[self._curWeaponLvl].ExpSum
		exp = self._levelCfgs[self._curWeaponLvl].Exp
		curpercent = expSum / exp
		maxLevel = self._levelCfgs[self._curWeaponLvl].Exp <= 0
	end
	--达到最高等级
	if maxLevel then
		curpercent = 1
	end
	self._wtWeaponLevelTxt:SetVisibility(ESlateVisibility.Collapsed)
	self._wtWeaponMaxLevelTxt:SetVisibility(ESlateVisibility.Collapsed)
	if not maxLevel then
		--武器等级
		self._wtWeaponLevelTxt:SetVisibility(ESlateVisibility.HitTestSelfOnly)
		self._wtWeaponLevelTxt:SetText(string.format(Module.Gunsmith.Config.Loc.GunsmithCurrentLevel, self._curWeaponLvl))
		--不是最高级
		self._wtWeaponUpBtn:SetBtnEnable(true)
		self._wtWeaponUpBtn:SetVisibility(ESlateVisibility.Visible)
		if self._wtProgressBar then
			self._wtProgressBar:SetVisibility(ESlateVisibility.HitTestSelfOnly)
		end
		self._wtWeaponExpTxt:SetVisibility(ESlateVisibility.HitTestSelfOnly)
	else
		--武器最高等级
		self._wtWeaponMaxLevelTxt:SetVisibility(ESlateVisibility.HitTestSelfOnly)
		self._wtWeaponMaxLevelTxt:SetText(string.format(Module.Gunsmith.Config.Loc.GunsmithHighestLevel, self._curWeaponLvl))
		--最高级需要隐藏一些控件
		self._wtWeaponUpBtn:SetVisibility(ESlateVisibility.Collapsed)
		if self._wtProgressBar then
			self._wtProgressBar:SetVisibility(ESlateVisibility.Collapsed)
		end
		self._wtWeaponExpTxt:SetVisibility(ESlateVisibility.Collapsed)
	end
	--层级设高,解决被黑色背景挡住,无法显示的问题(严重)
	--武器当前等级进度条
	if self._wtProgressBar then
		self._wtProgressBar:SetType(1)
		self._wtProgressBar:SetPercent(curpercent or 0)
	end
	self._wtDFCanvas:Visible()
	--武器当前等级经验比
	self._wtWeaponExpTxt:SetText(string.format(Module.Gunsmith.Config.Loc.GunsmithExpRidExp, expSum or "", exp or ""))

	if not IsHD() then
		Module.Gunsmith.Config.Events.evtOnWeaponUpgradeUIShow:Invoke(maxLevel)
	end
	--杀人星级
	self:_KillingRisingStar()
end

function WeaponUpgradePanel:_OnGetItemCount()
	return self._levelList and #self._levelList or 0
end

function WeaponUpgradePanel:_OnGetItemSize(position)
	local baseValue = DFHD_LUA == 1 and 256 or 192
	local spaceX = 20
	local baseOffset = 40
	local itemCount = #self._levelList[position].UnlockParts
	return FVector2D(baseValue * itemCount + (itemCount - 1) * spaceX + baseOffset, 256)
end

function WeaponUpgradePanel:_OnCreateItemWidget(position, itemWidget)
	local callBack = {
		GetPropItem = CreateCallBack(self.GetPropItem, self),
		GetItemId = CreateCallBack(self.GetItemId, self),
	}
	if self._levelList and self._weaponItem then
		itemWidget:InitItemData(callBack, self._levelList[position], self._weaponItem)
	end
end

--经验卡刷新事件
function WeaponUpgradePanel:_OnWeaponUpgradeItemUpdate()

	loginfo("WeaponUpgradePanel: _OnWeaponUpgradeItemUpdate ok ")
	--断线重连:必须重新获取数据,否则升级不成功
	self:_GetWeaponNewData()
	--打开结算界面
	self:_OpenUpgradePanel()

	self:_ResetWeaponUpgrade()
	--刷新配件列表
	self._wtScrollGridBox:RefreshAllItems()
	--配件剧中
	self:_WeaponItemDrama()
end

function WeaponUpgradePanel:_OnProcessServerDataUpdated()
	self:OnProcessUIUpdate(true)
end

--打开打战场结算界面
function WeaponUpgradePanel:_OpenUpgradePanel()
    if self._weaponItem == nil then
		loginfo("WeaponUpgradePanel: self._weaponItem is nil ")
        return
    end
	local weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
	local oldExp = Module.Gunsmith.Field:GetOldWeaponExp()

	loginfo("WeaponUpgradePanel: weaponFeature = ", weaponFeature, ", oldExp = ", oldExp)

	if weaponFeature and oldExp then
		local newExp = weaponFeature:GetWeaponExp()
        local oldLevel = Server.WeaponAssemblyServer:GetWeaponLevelByExp(self._weaponItem.id, oldExp)
        local newLevel = Server.WeaponAssemblyServer:GetWeaponLevelByExp(self._weaponItem.id, newExp)
		if newLevel == oldLevel then
			loginfo("WeaponUpgradePanel: newLevel = ", newLevel, ", oldLevel = ", oldLevel)
			return
		end
        local partInfo = {}
        for key, curLevel in pairs(self._levelCfgs or {}) do
            local level = curLevel.Level
            if level > oldLevel and level <= newLevel then
                for _, itemId in ipairs(curLevel.UnlockParts or {}) do
                    table.insert(partInfo, {id = tonumber(itemId), num = 1})
                end
            end
        end
        local weaponChangeInfo = {{
            ID = self._weaponItem.id,
            old_exp = oldExp,
            exp = newExp,
            unlock_componets_prop = partInfo,
        }}
        Module.Settlement:OpenWeaponUpgradesPop(weaponChangeInfo)
    end
end

--鼠标点击屏幕事件
function WeaponUpgradePanel:_OnMouseButtonDown(mouseEvent)
	local sceenPos = mouseEvent:GetScreenSpacePosition()
	--提示处理
	if self._wtCheck then
		local isUnder = USlateBlueprintLibrary.IsUnderLocation(self._wtCheck:GetCachedGeometry(), sceenPos)
		if not isUnder then
			self:_OnHideDescriptionTip()
		end
	end
	-- BEGIN MODIFICATION @ VIRTUOS : 如果当前页面上还有弹窗存在，直接return
	if IsHD() then
		local PopController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
		local LastPopUI = PopController ~= nil and PopController:TryGetLastPopUI() or nil
		if LastPopUI then
			return 
		end
	end
	-- END MODIFICATION
	
	--如果打开了关联枪械,则直接return
	if self._isPartInfo then
		return
	end
	local wtWeaponBtn = nil
	if self._wtWeaponInfo:IsVisible() then
		wtWeaponBtn = self._wtWeaponInfo
	end
	if self._wtDFCanvasPanel:IsVisible() then
		wtWeaponBtn = self._wtDFCanvasPanel
	end
	if self._wtWeaponFittInfo:IsVisible() then
		wtWeaponBtn = self._wtWeaponFittInfo
	end
	if wtWeaponBtn then
	    local geometry = wtWeaponBtn:GetCachedGeometry()
    	local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
	    if not isUnder and not USlateBlueprintLibrary.IsUnderLocation(self._wtWeaponBtn:GetCachedGeometry(), sceenPos) then
			if wtWeaponBtn == self._wtWeaponInfo then
				self._wtWeaponBtn:SetIsChecked(false)
				-- BEGIN MODIFICATION @ VIRTUOS : 隐藏详细弹窗时移除导航组
				if IsHD() then
					self:_EnableNavigationWeaponInfo(false)
				end
				-- END MODIFICATION
			end
			wtWeaponBtn:Collapsed()
			--秒隐藏白框
			if self._itemView == nil or sceenPos == nil then
				return
			end
			local itemGeometry = self._itemView:GetCachedGeometry()
			local isGeometry = USlateBlueprintLibrary.IsUnderLocation(itemGeometry, sceenPos)
			if not isGeometry then
				self:_WhiteFrameCollapsed(false)
			end
		end
	end
end

--隐藏白框
function WeaponUpgradePanel:_WhiteFrameCollapsed(isBool)
	if self._miniPropItemView then
		self._miniPropItemView:EnableComponent(EComp.MissionItem, false)
		self._miniPropItemView = nil
		return
	end
	if self._itemView then
		local whiteFrame = self._itemView:FindOrAdd(EComp.MissionItem, UIName2ID.IVItemSelectedComponent, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
		if whiteFrame then
            if isBool then
                whiteFrame:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            else
                whiteFrame:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
	end
	if isBool == false then
		self._accessoryData = nil
		self._itemId = nil
    end
end

--武器详情回调
function WeaponUpgradePanel:_OnWeaponNewsClicked(isBool)
	--隐藏白框
	self._miniPropItemView = nil
	self:_WhiteFrameCollapsed(false)
	self:_OnHideAllWeaponModuleNews(isBool)
	if isBool == false or self._weaponItem == nil then
		-- BEGIN MODIFICATION @ VIRTUOS : 隐藏详细弹窗时移除导航组
		if IsHD() then
			self:_EnableNavigationWeaponInfo(false)
			self:_AddNavigation(true)
		end
		-- END MODIFICATION
		return
	end
	local weaponData = Server.InventoryServer:GetItemsById(tonumber(self._weaponItem.id), ESlotGroup.MPApply)[1]
	if weaponData == nil then
		weaponData = ItemBase:New(tonumber(self._weaponItem.id))
	end
	--刷新武器数据
	self._wtWeaponInfo:UpdateItem(weaponData)
	self._wtWeaponInfo:SetVisibility(ESlateVisibility.Visible)

	-- BEGIN MODIFICATION @ VIRTUOS : 显示详细属性弹窗时设置其导航组
	if IsHD() then
		self:_EnableNavigationWeaponInfo(true)
	end
	-- END MODIFICATION
end

--武器配件详情回调
function WeaponUpgradePanel:_OnWeaponModuleNews(itemStruct, itemView)
	if itemView == nil then
		return
	end
    if self._itemId == itemView.itemId then
        self._itemView = itemView
		if itemStruct then
			self._miniPropItemView = nil
			self:_OnHideAllWeaponModuleNews()
			self:_WhiteFrameCollapsed(false)
		else
			self._itemView:EnableComponent(EComp.MissionItem, true)
		end
        return
    else
		self:_OnHideAllWeaponModuleNews()
        self:_WhiteFrameCollapsed(false)
        self._itemView = itemView
        self:_WhiteFrameCollapsed(true)
		--一级特殊处理
		if itemView.Level == 1 and self._firstLevelArr and #self._firstLevelArr > 0 then
			--隐藏
			self:_GetWeaponItem(0)

			for index, item_id in ipairs(self._firstLevelArr or {}) do
				local itemView = self:_GetWeaponItem(1)

				if itemView and item_id then
					local accessory = Server.InventoryServer:GetItemsById(tonumber(item_id), ESlotGroup.MPApply)
					local accessoryData = nil
					if accessory and accessory[1] then
						accessoryData = accessory[1]
					else
						accessoryData = ItemBase:New(tonumber(item_id))
					end
					itemView.Level = nil
					--白框
					itemView:FindOrAdd(EComp.MissionItem, UIName2ID.IVItemSelectedComponent, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)

					--直接禁用预安装符号 ssy
					itemView:EnableComponent(EComp.TopLeftNewTag, false)

					if accessoryData then
						itemView:SetRootSize(102, 102)
						itemView:InitItem(accessoryData)
						itemView:BindCustomOnClicked(function()
							if self._miniPropItemView then
								self._miniPropItemView:EnableComponent(EComp.MissionItem, false)
							end
							itemView:EnableComponent(EComp.MissionItem, true)
							--打开配件详情
							self._miniPropItemView = itemView
							self._accessoryData = accessoryData
							self:_OpenModuleNews(accessoryData, itemView)
						end)
					end
					itemView:EnableComponent(EComp.ItemMark, false)
					itemView:EnableComponent(EComp.MissionItem, false)
				end
			end
			self._wtDFCanvasPanel:Visible()
			-- BEGIN MODIFICATION @ VIRTUOS : 展开武器配件时注册导航和返回快捷键
			if IsHD() then
				self:_EnableGamepadFeatureDFWrapBox(true)
			end
			-- END MODIFICATION
			self._accessoryData = itemStruct
			self._itemId = itemView.itemId
			return
		end
    end
	--保存选中配件数据
	self._accessoryData = itemStruct
	self._itemId = itemView.itemId
	self:_OpenModuleNews(itemStruct, itemView)
end

function WeaponUpgradePanel:_OpenModuleNews(itemStruct, itemView)
	--刷新配件数据
	if itemStruct and itemStruct.id then
		self._wtWeaponFittInfo:UpdateUI(itemStruct, true)
		self._wtWeaponFittInfo:SetVisibility(ESlateVisibility.Visible)
		-- BEGIN MODIFICATION @ VIRTUOS : 注册 ItemDetailContentAdapterDetail 中的快捷键: 关联枪械
		if IsHD() then
			self._wtWeaponDetail:InitShortcuts("Gunsmith_OpenDetail_Gamepad")
		end
		-- END MODIFICATION
	end
	--详情页武器名称隐藏
	if self._wtNameLabel and self._wtDFLineImage then
		self._wtNameLabel:SetVisibility(ESlateVisibility.Collapsed)
		self._wtDFLineImage:SetVisibility(ESlateVisibility.Collapsed)
	end
	if itemStruct and itemStruct.id then
		self:_WeaponPartLimitInfo(itemStruct)
	end
	--配件详情位置控制
	local fitt = self._wtWeaponFittInfo
	if fitt and fitt.SetRenderTranslation and fitt.RenderTransform then
		if self._wtDFCanvasPanel:IsVisible() then
			fitt:SetRenderTranslation(FVector2D(-690, fitt.RenderTransform.Translation.Y))
		else
			fitt:SetRenderTranslation(FVector2D(-32, fitt.RenderTransform.Translation.Y))
		end
	end
end

function WeaponUpgradePanel:_WeaponPartLimitInfo(itemStruct)
	--隐藏配件all属性
	for key, value in pairs(self._wtLimitInfo or {}) do
		if value then
			value:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	--隐藏武器详细属性
	for index, value in ipairs(self._wtWeaponNatures or {}) do
		if value then
			value:SetVisibility(ESlateVisibility.Collapsed)
		end
	end
	--目前(不显示其他的)
end

--隐藏all详情页
function WeaponUpgradePanel:_OnHideAllWeaponModuleNews(isBool)
	--刷新配件数据
	self._wtWeaponInfo:Collapsed()
	self._wtWeaponFittInfo:Collapsed()
	self._wtDFCanvasPanel:Collapsed()

	-- BEGIN MODIFICATION @ VIRTUOS : 
	if IsHD() then
		-- 移除配件导航和关闭快捷键
		self:_EnableGamepadFeatureDFWrapBox(false)
		-- 移除 ItemDetailContentAdapterDetail 中的快捷键
		self._wtWeaponDetail:RemoveShortcuts()
	end
	-- END MODIFICATION	
	self._wtWeaponBtn:SetIsChecked(isBool or false)
end

--武器升级回调
function WeaponUpgradePanel:_OnWeaponUpgradeClicked()
	--打开升级界面之后关闭详情控件
	self._miniPropItemView = nil
	self:_OnHideAllWeaponModuleNews()
	self:_WhiteFrameCollapsed(false)
	Module.Gunsmith.Config.Events.evtOnWeaponUpgradePanelBtnClicked:Invoke()
	Facade.UIManager:AsyncShowUI(UIName2ID.WeaponUpgradePop, nil, self, self._weaponItem, self:_GetUpMaxLevel())
end

--obj list class=WBP_CommonItemTemplate_C
--容器不可被其他框架销毁,WaterfallScrollBox不能刷新数量为0,否则会被控件框架回收,导致sub复用失效
function WeaponUpgradePanel:GetPropItem(uiNavId, container, type)
    return GunsmithPartUnlockLogic.GetSubUiInst(self, uiNavId, container, type)
end

--获取id
function WeaponUpgradePanel:GetItemId()
	return self._itemId
end

function WeaponUpgradePanel:_GetWeaponItem(type)
	return self:GetPropItem(UIName2ID.IVCommonItemTemplate, self._wtDFWrapBox, type)
end

function WeaponUpgradePanel:OnClose()
	self:_GetWeaponItem()
	self:RemoverTimerHandler()
end

function WeaponUpgradePanel:_RemoveEventListener()
	self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked)
	self:RemoveLuaEvent(Server.CollectionServer.Events.evtCollectionUsePropSucess)
	--断线重连移除
	Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected,self)

	self:RemoveLuaEvent(Module.Gunsmith.Config.Events.evtGunsmithCSPostAssemblyDepositPropUpdate)
end


---手柄适配 ssy 2025-5-29
---@param bEnable any
function WeaponUpgradePanel:_AddNavigation(bEnable)
	self:AddBtnHandle()
	self:AddTarHandle()
	if bEnable then
		self:AddWidgetHandle(self._wtScrollGridBox)
		self:AddTarHandle("Select_Gamepad")
		self:AddTarHandle("Gunsmith_ViewDetails_Gamepad", self._ShortcutEventViewDetails)
		self:AddBtnHandle("Common_RightStickY_Gamepad", self._ScrollDetailView, nil, nil, false, true)
		--是否升级
		local data = nil
		if self._weaponItem then
			local weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
			if weaponFeature then
				local level = weaponFeature:GetWeaponLevel()
				if level and self._levelCfgs then
					data = self._levelCfgs[level]
				end
		
			end
		end
		if data and data.Exp ~= 0 then
			local func = function()
				if self._wtWeaponUpBtn then
					self._wtWeaponUpBtn:ButtonClick()
				end
			end
			self:AddBtnHandle("Gunsmith_Upgrade_Gamepad", func, self._wtWeaponUpBtn)
		end
	end
end

---------------------------------------------------------------------------------------------------------------------------------------------

-- BEGIN MODIFICATION @ VIRTUOS : 
function WeaponUpgradePanel:_EnableNavigation(bEnable)
	if not IsHD() then
		return 
	end

	if bEnable then
		if not self._wtNavGroup then
			self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtScrollGridBox, self, "Hittest")
			if self._wtNavGroup then
				self._wtNavGroup:SetScrollRecipient(self._wtScrollGridBox)
			end
			WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
		end

		if not self._scrollHandle then
			self._scrollHandle = self:AddAxisInputActionBinding("Common_RightStickY_Gamepad", self._ScrollDetailView, self, EDisplayInputActionPriority.UI_Pop)
		end
	else
		if self._wtNavGroup then
			WidgetUtil.RemoveNavigationGroup(self)
			self._wtNavGroup = nil
		end

		if self._scrollHandle then
			self:RemoveInputActionBinding(self._scrollHandle)
			self._scrollHandle = nil
		end
	end
end

function WeaponUpgradePanel:_ScrollDetailView(axis)
    WidgetUtil.ScrollByAxis(self._wtWeaponDetailView._wtScrollBoxContent, axis)
end

-- 配件导航和快捷键
function WeaponUpgradePanel:_EnableGamepadFeatureDFWrapBox(bEnable)
	if not IsHD() then
		return 
	end

	if bEnable then
		if not self._wtNavGroup_DFWrapBox then
			self._wtNavGroup_DFWrapBox = WidgetUtil.RegisterNavigationGroup(self._wtDFWrapBox, self._wtDFWrapBox, "Hittest")
			if self._wtNavGroup_DFWrapBox then
				self._wtNavGroup_DFWrapBox:AddNavWidgetToArray(self._wtDFWrapBox)
				self._wtNavGroup_DFWrapBox:MarkIsStackControlGroup()
				self._wtNavGroup_DFWrapBox:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
			end
			WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup_DFWrapBox)

			self._BackHandler = self:AddInputActionBinding(
				"Back", 
				EInputEvent.IE_Pressed, 
				CreateCallBack(function(self)
					--配件详情显示事件/白框显示事件
					if self._accessoryData and self._itemView then
						Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked:Invoke(self._accessoryData, self._itemView)
					end
				end, self), 
				self, 
				EDisplayInputActionPriority.UI_Pop
			)
		end
	else
		if self._wtNavGroup_DFWrapBox then
			WidgetUtil.RemoveNavigationGroup(self._wtDFWrapBox)
			self._wtNavGroup_DFWrapBox = nil
		end
		if self._BackHandler then
			self:RemoveInputActionBinding(self._BackHandler)
			self._BackHandler = nil
		end
	end
end

function WeaponUpgradePanel:_EnableNavigationWeaponInfo(bEnable)
	if not IsHD() then
		return 
	end
	self._wtWeaponInfo:EnableSubUINavigation(bEnable)
	if bEnable then
		self._wtWeaponInfo:InitSubUIShortcuts()
	else
		self._wtWeaponInfo:RemoveSubUIShortcuts()
	end
end

-- 新增手柄快捷键（页面中显示的按钮：改装、预改装）
function WeaponUpgradePanel:_InitShortcuts()
	if not IsHD() then
		return 
	end

    -- 改装
    if self._wtWeaponUpBtn:IsVisible() and not self._DefaultClicked then
		local data = nil
		if self._weaponItem then
			local weaponFeature = self._weaponItem:GetFeature(EFeatureType.Weapon)
			if weaponFeature then
				local level = weaponFeature:GetWeaponLevel()
				if level and self._levelCfgs then
					data = self._levelCfgs[level]
				end
			end
		end
		if data and data.Exp ~= 0 then
			self._DefaultClicked = self:AddInputActionBinding("Gunsmith_Upgrade_Gamepad", EInputEvent.IE_Pressed, self._ShortcutEventUpgrade, self, EDisplayInputActionPriority.UI_Stack)
			self._wtWeaponUpBtn:SetDisplayInputAction("Gunsmith_Upgrade_Gamepad", true, nil, true)
		end
    end
	-- 设置快捷键和图标 - 查看详情
    local summaryList = {}
    table.insert(summaryList, {actionName = "Select_Gamepad",func = nil, caller = nil ,bUIOnly = true, bHideIcon = false})
	table.insert(summaryList, {actionName = "Gunsmith_ViewDetails_Gamepad",func = self._ShortcutEventViewDetails, caller = self ,bUIOnly = false, bHideIcon = false})
    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, false)
end

function WeaponUpgradePanel:_RemoveShortcuts()
	if not IsHD() then
		return 
	end

    if self._DefaultClicked then
        self:RemoveInputActionBinding(self._DefaultClicked)
        self._DefaultClicked= nil
    end
	Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function WeaponUpgradePanel:_ShortcutEventUpgrade()
	if IsHD() and self._wtWeaponUpBtn then
    	self._wtWeaponUpBtn:ButtonClick()
	end
end

function WeaponUpgradePanel:_ShortcutEventViewDetails()
	if IsHD() and self._wtWeaponBtn then
    	self._wtWeaponBtn:SelfClick()
	end
end

function WeaponUpgradePanel:_OnGunsmithItemInitItemData(ItemWiew)
	self:AddWidgetHandle(ItemWiew, true)
end
-- END MODIFICATION

function WeaponUpgradePanel:AddBtnHandle(actionName, func, btn, isLong, level, isAixs)
    if not IsHD() then
        return
    end
    if self._dfHandles == nil then
        self._dfHandles = {}
    end
    if actionName then
        --默认Pop级别
        local priority = level == false and EDisplayInputActionPriority.UI_Stack or EDisplayInputActionPriority.UI_Pop
        local handle = nil
        --默认非轴映射
        if isAixs then
            handle = self:AddAxisInputActionBinding(actionName, func, self, priority)--不支持佚名函数
        else
            handle = self:AddInputActionBinding(actionName, EInputEvent.IE_Pressed, CreateCallBack(func, self), self, priority)
        end
        if handle then
            table.insert(self._dfHandles, handle)
        end
        --是否长按(按钮特有:[btn:ButtonClick()/btn:SelfClick()])
        if btn and handle then
            if isLong then
                if btn.SetDisplayInputActionWithLongPress then
                    btn:SetDisplayInputActionWithLongPress(handle, self, actionName, true, nil, true)
                end
            else
                if btn.SetDisplayInputAction then
                    btn:SetDisplayInputAction(actionName, true, nil, true)
                end
            end
        end
    else
        --清除手柄响应
        for index, handle in ipairs(self._dfHandles or {}) do
            self:RemoveInputActionBinding(handle)
        end
        self._dfHandles = nil
    end
end

function WeaponUpgradePanel:AddWidgetHandle(widget, isAdd, isStack, isClick)
    if not IsHD() then
        return
    end
    if widget then
        if not isvalid(self._dfNavGroup) then
            self._dfNavGroup = WidgetUtil.RegisterNavigationGroup(widget, self, "Hittest")
            if isvalid(self._dfNavGroup) then
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._dfNavGroup)
            end
        end
        if isvalid(self._dfNavGroup) then
            local controller = nil
            if widget.wtDFCommonAddDecSlider and widget.wtDFCommonAddDecSlider.DFCommonAddDecHolder then
                controller = widget.wtDFCommonAddDecSlider.DFCommonAddDecHolder.SlotContainer
            end
            if isAdd then
                if controller then
                    self._dfNavGroup:AddNavWidgetToArray(controller)
                else
                    self._dfNavGroup:AddNavWidgetToArray(widget)
                end
                -- navGroup:SetNavSelectorWidgetVisibility(true)
            end
            if widget.RefreshAllItems then
                self._dfNavGroup:SetScrollRecipient(widget)
            end
            if isClick then
                self._dfNavGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            end
            if isStack then
                self._dfNavGroup:MarkIsStackControlGroup()
            end
            if widget.RefreshAllItems then
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._dfNavGroup)
            end
        end
    else
        --移除所有组
        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        self._dfNavGroup = nil
    end
end

function WeaponUpgradePanel:AddTarHandle(actionName, func)
    if not IsHD() then
        return
    end
    if self._dfTabBarInputs == nil then
        self._dfTabBarInputs = {}
    end
    if actionName then
        local callback = nil
        --没有func只做显示
        if func then
            callback = CreateCallBack(func, self)
        end
        --注册导航栏按钮
        if self._dfTabBarInputs then
            table.insert(self._dfTabBarInputs, {actionName = actionName, func = callback, caller = self, bUIOnly = false})
            Module.CommonBar:SetBottomBarTempInputSummaryList(self._dfTabBarInputs)
        end
    else
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._dfTabBarInputs = nil
    end
end

return WeaponUpgradePanel