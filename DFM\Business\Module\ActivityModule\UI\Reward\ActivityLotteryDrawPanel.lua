----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

--活动抽奖系统
---@class ActivityLotteryDrawPanel : LuaUIBaseView
local ActivityLotteryDrawPanel = ui("ActivityLotteryDrawPanel")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ActivityDrawName1 = require "DFM.Business.Module.ActivityModule.UI.Reward.ActivityDrawName1"
local ActivityDrawName2 = require "DFM.Business.Module.ActivityModule.UI.Reward.ActivityDrawName2"
local DFCommonButtonOnly = require "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonButtonOnly"
local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"
local FRTIParamData = import "RTIParamData"
local Config = Module.Activity.Config

local DrawEnum = {
    logistics = ActivityType.ActivityTypeLotteryTemplate,
    cooperate = ActivityType.ActivityTypeRaidLotteryTemplate,
}

function ActivityLotteryDrawPanel:Ctor()
    --核心奖励提示
    self._wtDrawsTxt = self:Wnd("wtGuaranteedGroupHintText", UITextBlock)
    --详情跳转
    self._wtSetails = {
        [1] = {
            panel = self:Wnd("PlatformPaddingBox_0", UIWidgetBase),
            btn = self:Wnd("WBP_Activity_PrizeName", ActivityDrawName1),
        },
        [2] = {
            panel = self:Wnd("PlatformPaddingBox_4", UIWidgetBase),
            btn = self:Wnd("WBP_Activity_PrizeName2_C_79", ActivityDrawName2),
        },
    }
    --定轨
    self._wtAppPanel = self:Wnd("PlatformPaddingBox_3", UIWidgetBase)
    self._wtCrossImg = self:Wnd("DFImage_122", UIImage)
    self._wtFIconImg = self:Wnd("DFImage_77", UIImage)
    self._wtFNameTxt = self:Wnd("DFTextBlock_79", UITextBlock)
    self._wtFTimeTxt = self:Wnd("WBP_Activity_ItemTab_92", UIWidgetBase):Wnd("DFRichTextBlock_21", UITextBlock)
    self._wtAppoiBtn = self:Wnd("DFButton_129", UIButton)
    self._wtAppoiBtn:Event("OnClicked", self._OnJumpClicked, self, 3)
    self._wtRedotIns = Module.ReddotTrie:CreateReddotIns(self:Wnd("Red", UIWidgetBase), nil, nil, nil, FVector2D(0, 12))

    self._wtComPanel = self:Wnd("WBP_SlotCompIconImage", UIWidgetBase)
    self._wtMainIcon = self._wtComPanel:Wnd("wtMainIcon", UIImage)
    self._wtMainIcon.OnIconLoaded:Add(CreateCPlusCallBack(self.OnUpRewardIconLoaded, self))
    self._wtComPanel:Collapsed()
    --活动抽奖
    self._wtDraws = {
        [1] = {
            icon = self:Wnd("DFImage_3", UIImage),
            num = self:Wnd("DFTextBlock_DrawOne_Mandel", UITextBlock),
            btn = self:Wnd("wtSingleActionBtn", DFCommonButtonOnly),
        },
        [2] = {
            icon = self:Wnd("DFImage", UIImage),
            num = self:Wnd("DFTextBlock_DrawOne_Mandel_1", UITextBlock),
            btn = self:Wnd("wtMultipleActionBtn", DFCommonButtonOnly),
        }
    }
    for index, value in ipairs(self._wtDraws or {}) do
        if value.btn then
            value.btn:Event("OnClicked", self._OnBtnClicked, self, index)
        end
    end
    self._wtCNDPanel = self:Wnd("WBP_Common_UnScaleBg", UIWidgetBase)
    self._wtCNDImage = self:Wnd("DFCDNImage_31", DFCDNImage)

    --禁用滚动控件集合
    self._wtHotzone = {}
    self:InjectLua()
end

function ActivityLotteryDrawPanel:GetContainer()
    if self._fGetCommonCtrl then
        return self._fGetCommonCtrl(EActivityCommCtrl.LotteryPrizePoolSlot)
    end
end

function ActivityLotteryDrawPanel:GetShowPoolBtn()
    if self._fGetCommonCtrl then
        return self._fGetCommonCtrl(EActivityCommCtrl.LotteryShowPoolButton)
    end
end

function ActivityLotteryDrawPanel:BindShowPrizePoolButton()
    local btn = self:GetShowPoolBtn()
    if btn then
        self:UnBindPrizePoolButton()
        btn:Event("OnCheckedClicked", self._OnOpenRewardClicked, self, false)
        btn:Event("OnUncheckedClicked", self._OnOpenRewardClicked, self, true)
        btn:Visible()
        btn:SetIsChecked(false)
        self._btn = btn
    end
end

function ActivityLotteryDrawPanel:UnBindPrizePoolButton()
    local btn = self:GetShowPoolBtn()
    if btn then
        btn:RemoveEvent("OnCheckedClicked")
        btn:RemoveEvent("OnUncheckedClicked")
    end
end

function ActivityLotteryDrawPanel:OnInitExtraData(activityID, fGetCommonCtrl)
    self._activityID = activityID
    self._fGetCommonCtrl = fGetCommonCtrl
    self:_InitData()
    --奖池按钮绑定
    self:BindShowPrizePoolButton()
end

function ActivityLotteryDrawPanel:OnShowBegin()
    --监听鼠标事件
    self:_AddMouseButtonUp(true)
    self:_AddEventListener()
    --这个请求会自动根据道具id获取奖池id
    Server.StoreServer:GetStoreBoxItem(self._drawId)
end

function ActivityLotteryDrawPanel:_AddEventListener()
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBoxItem, self._RefreshItemPanel, self)--拉取奖池数据
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreOpenBoxSuccess, self._OnMandelOpenBoxSucess, self)--请求抽奖
    self:AddLuaEvent(Module.ItemDetail.Config.evtItemDetailPanelClosed, self._OnItemDetailPanelClosed, self)--详情页关闭
    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnCloseRewardPanel, self)--奖励弹出关闭
    self:AddLuaEvent(Config.evtOpenLotteryDrawPanel, self._OnCloseRewardPanel, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelUpRewardChooseResult, self._OnStoreMandelUpReward, self)--定轨回包
end

function ActivityLotteryDrawPanel:_OnStoreMandelUpReward()
    --这个请求会自动根据道具id获取奖池id
    Server.StoreServer:GetStoreBoxItem(self._drawId)
end

function ActivityLotteryDrawPanel:_OnOpenLotteryDrawPanel(activityID)
    if activityID == self._activityID then
        self:_OnCloseRewardPanel()
    end
end

function ActivityLotteryDrawPanel:_OnCloseRewardPanel()
    if IsHD() then
        self:_AddGamepadInputs()
    end
end

--鼠标点击屏幕事件
function ActivityLotteryDrawPanel:_OnItemDetailPanelClosed()
    --如果有点击屏幕,立即关闭问号提示
    self._isDetailExist = Module.ItemDetail:GetMainPanelHandle()
end

function ActivityLotteryDrawPanel:_AddMouseButtonUp(isBool)
    local gamelnst = GetGameInstance()
    if gamelnst then
        if isBool then
            if self._btnHandle == nil then
                self._btnHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
            end
        else
            if self._btnHandle then
                UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._btnHandle)
                self._btnHandle = nil
            end
        end
    end
end

--鼠标点击屏幕事件
function ActivityLotteryDrawPanel:_OnMouseButtonDown(mouseEvent)
    --如果有点击屏幕,立即关闭问号提示
    local container = self:GetContainer()
    local btn       = self:GetShowPoolBtn()

    self._isDetailExist = Module.ItemDetail:GetMainPanelHandle()
    --正在判断容器
    if mouseEvent and container and btn then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry1 = container:GetCachedGeometry()
        local geometry2 = btn:GetCachedGeometry()
    	local isUnder1 = USlateBlueprintLibrary.IsUnderLocation(geometry1, sceenPos)
    	local isUnder2 = USlateBlueprintLibrary.IsUnderLocation(geometry2, sceenPos)
        if not isUnder1 and not isUnder2 then
            if self._isDetailExist == nil then
                self:_OpenRewardItem()
            end
        end
    end
end

function ActivityLotteryDrawPanel:_RefreshItemPanel(info)
    self._info = info
    self:_InitPanel()
end

function ActivityLotteryDrawPanel:_OnMandelOpenBoxSucess(change)
    --重新拉取奖励数据
    Server.StoreServer:GetStoreBoxItem(self._drawId)
    --重新拉新货币数量(刷新货币)唯赛季物流刷新货币/合作物流藏品会自动刷新
    Server.ActivityServer:SendLogisticsCurrencyNum(self._activityID)
    --展示获得奖励
    self:_OnShowRewardPanel(change)
end

function ActivityLotteryDrawPanel:_OnShowRewardPanel(change)
    if change == nil then
        return
    end
    local itemList = {}
    for _, value in ipairs(change.prop_changes or {}) do
        local e = PropChangeType
        local t = value.change_type
        if value.prop and (t == e.Add or t == e.PropChangeNone or t == e.Modify or t == e.SendBuyMail) and value.prop.num > 0 then
            --使用武器玄学皮肤item
            local item = ActivityLogic.GetWeaponSkinItem(value.prop)
            if item then
                table.insert(itemList, item)
            end
        end
    end
    for _, propChange in pairs(change.currency_changes) do
        if propChange.delta and propChange.currency_id then
            table.insert(itemList, ItemBase:NewIns(propChange.currency_id, propChange.delta))
            itemList[#itemList].src_id = propChange.src_id
        end
    end
    if #itemList == 0 then
        return
    end
    local tittle = Config.Loc.GetReward
    Module.Reward:OpenRewardPanel(tittle, nil, itemList, nil, nil, nil, true)
end

function ActivityLotteryDrawPanel:OnHideBegin()
    --鼠标事件移除
    self:RemoveAllLuaEvent()
    Module.ItemDetail:CloseItemDetailPanel()
end

function ActivityLotteryDrawPanel:_InitData()
    --获取物流凭证/合作凭证
    self._drawId = Server.ActivityServer:GetLotteryVoucherId(self._activityID)
    if self._drawId then
        self._icons = {
            [1] = {
                num = 1,
                icon = Config.VoucherIconPathList[self._drawId],
            },
            [2] = {
                num = 10,
                icon = Config.VoucherIconPathList[self._drawId],
            },
        }
    end
end

function ActivityLotteryDrawPanel:_IsShow(data, item)
    if data and item then
        if type(data) == "number" then
            return data == item.id
        end
        if type(data) == "table" then
            for index, id in ipairs(data or {}) do
                if id == item.id then
                    return true
                end
            end
        end
    end
    return false
end

function ActivityLotteryDrawPanel:_GetRewardInfo(data)
    local list = {}
    if data then
        for index, value in ipairs(data.group_list or {}) do
            local items = {}
            local start_time = nil
            local end_time   = nil
            for _, prop in ipairs(value.prop_list or {}) do
                local item = General.GetSkinItemData(prop.prop_info)
                if item then
                    local isHaved = General.IsHaved(item.id)
                    if not isHaved then
                        isHaved = not prop.restore_flag and prop.hit_sum > 0
                    end
                    item["prob"]     = prop.real_prob * 100
                    item["num_id"]   = prop.num_id
                    item["group_id"] = value.group_id
                    item["owned"]    = isHaved
                    item["restore_flag"] = prop.restore_flag
                    item["prob_showed"]  = prop.prob_showed ~= nil and prop.prob_showed * 100 or nil
                    item["acquisition_guaranteed"] = prop.acquisition_guaranteed
                    item["bound_flag"] = prop.bound_flag
                    item["core_flag"]  = value.core_flag
                    item["show_id1"]   = self:_IsShow(data.show_id1, item)
                    item["show_id2"]   = self:_IsShow(data.show_id2, item)
                    item["start_time"] = prop.begin_time or 0
                    item["end_time"]   = prop.end_time   or 0
                end
                if prop.begin_time ~= prop.end_time then
                    start_time = prop.begin_time
                    end_time   = prop.end_time
                end
                table.insert(items, item)
            end
            --限时的放在前面
            if value.core_flag then
                table.sort(items, function(a, b)
                    local a1 = a.start_time ~= a.end_time
                    local b1 = b.start_time ~= b.end_time
                    if a1 ~= b1 and a1 == true then
                        return true
                    end
                    return false
                end)
            end

            table.insert(list, {
                items = items,
                prob  = value.prob,
                start_time   = start_time or 0,--限时
                end_time     = end_time   or 0,--限时
                track_start_time = value.begin_time or 0,--定轨时间
                track_end_time   = value.end_time   or 0,--定轨时间
                show_id1     = data.show_id1,--兼容
                show_id2     = data.show_id2,--兼容
                show_id_list = data.show_id_list,--展示页
                group_id     = value.group_id,
                core_flag    = value.core_flag,--是否核心奖励
                time_assured = value.time_assured,--核心奖励保底抽数
                title_main   = value.core_flag and Module.Activity.Config.Loc.CorePrize or Module.Activity.Config.Loc.OtherPrize,
                title_sub    = value.core_flag and Module.Activity.Config.Loc.CorePrizeCurrentProb or Module.Activity.Config.Loc.OtherPrizeCurrentProb,
            })
        end
        --核心奖励排序
        local SortFunc = function(a, b)
            if a.core_flag then
                return a.core_flag
            else
                return false
            end
        end
        table.sort(list, SortFunc)
    end
    return list
end

function ActivityLotteryDrawPanel:_InverseOrderFunc(info)
    local list = {}
    --排序保证顺序
    local sortfunc = function(a, b)
        if a and b and a.open_time and b.open_time then
            if a.open_time > b.open_time then
                return true
            end
        end
        return false
    end
    if info then
        table.sort(info, sortfunc)
    end
    for key, historyInfo in ipairs(info or {}) do
        local add_props = historyInfo.add_props
        if add_props then
            --服务端先抽的做前面，需要都倒一下
            for i = #add_props, 1, -1 do
                table.insert(list, {
                    historyInfo = historyInfo,
                    prop = add_props[i],
                })
            end
        end
    end
    return list
end

function ActivityLotteryDrawPanel:_InitPanel()
    --数据整理
    local info = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    local itemInfo = ItemConfigTool.GetItemConfigById(self._drawId)--奖池id
    if itemInfo then
        self._boxId = itemInfo.ConnectedPool--奖池id
        local group = Server.StoreServer:GetMandelLotteryCoreGroup(self._boxId)
        if group then
            self._trackList = group.prop_list--定轨数据
            self._trackWeaponId = Server.StoreServer:GetMandelIDUpReward(self._boxId)--定轨Id
        end
        self._rewardList = self:_GetRewardInfo(self._info)--奖励数据
        self._historyList = self:_InverseOrderFunc(Server.StoreServer:GetLotteryRecordsByLotteryId(self._boxId))--抽奖历史
    end
    if info then
        self._desc = info.desc
    end
    --数据重定义(限时处理)*******
    local limit_time = false
    for _, value in ipairs(self._rewardList or {}) do
        --定轨时间
        local time1 = value.track_start_time
        local time2 = value.track_end_time
        if time1 and time2 and time1 ~= time2 then
            local time = General.GetCurTime()
            if time1 <= time and time <= time2 then
                self._end_time = time2
            end
        end
        --定轨请求组
        if value.core_flag then
            self._groupId = value.group_id
        end
        for index, item in ipairs(value.items or {}) do
            if item.core_flag and item.start_time ~= item.end_time then
                limit_time = true
            end
        end
        for index, item in ipairs(value.items or {}) do
            if limit_time then
                item.restore_flag = nil
            end
        end
    end
    self._showIds = {}
    --核心奖励提示
    for index, value in ipairs(self._rewardList or {}) do
        --展示奖励(兼容合作/海外物流)
        if type(value.show_id_list) == "table" and #value.show_id_list > 0 then
            self._showIds = value.show_id_list
        else
            if type(value.show_id1) == "number" then
                self._showIds = {value.show_id1}
            end
            if type(value.show_id1) == "table" then
                self._showIds = value.show_id1
            end
        end
        local is_owned = true
        for _, item in ipairs(value.items or {}) do
            if not item.owned and item.core_flag then
                is_owned = false
            end
        end
        if value.time_assured and value.core_flag then
            if is_owned then
                self._wtDrawsTxt:SetText(Config.Loc.ActivityCoreRewards)
            else
                self._wtDrawsTxt:SetText(string.format(Config.Loc.ActivityRemainingLotteryDraws, value.time_assured))
            end
            break
        end
    end
    --抽奖按钮
    for index, value in ipairs(self._wtDraws or {}) do
        local data = self._icons and self._icons[index]
        if data then
            if value.icon then
                value.icon:AsyncSetImagePath(data.icon or "", true)
            end
            if value.num then
                value.num:SetText(data.num or "")
            end
            if value.btn then
                value.btn:SetMainTitle(string.format(Config.Loc.ActivitySignFor, index == 1 and 1 or 10))
            end
        end
    end
    --展示集合
    local defaultIndex, data
    for index, showId in ipairs(self._showIds or {}) do
        local itemData = General.GetSkinItemData({id = showId})
        if itemData then
            data = {
                name = itemData.name,
                tips = Config.Loc.ActivityCoreLogisticsMaterials,
            }
            self._core_item = itemData
        end
        defaultIndex = index
        --CDNIcon背景
        self._wtCNDPanel:Collapsed()
        if index > 1 then
            data = {}
            if info and info.info1 then
                local url = string.format("Resource/Texture/Activity/%s", info.info1)
                self._wtCNDImage:SetCDNImage(url, false, Module.CDNIcon.Config.ECdnTagEnum.Activity)
            end
            self._wtCNDPanel:SelfHitTestInvisible()
            break
        end
    end
    self._defaultIndex = defaultIndex
    --跳转
    for index, value in ipairs(self._wtSetails or {}) do
        if value.panel then
            if index == defaultIndex then
                if value.btn then
                    local func = function()
                        self:_OnJumpClicked(index)
                    end
                    value.btn:InitData(data, func)
                end
                value.panel:SelfHitTestInvisible()
            else
                value.panel:Collapsed()
            end
        end
    end
    --定轨展示
    self._wtRedotIns:SetReddotVisible(false)
    local isAll = nil
    for index, value in ipairs(self._trackList or {}) do
        isAll = true
        local itemData = General.GetSkinItemData({id = value.prop_id})
        if itemData then
            if not General.IsHaved(itemData.id) then
                isAll = false
                break
            end
        end
    end
    if isAll ~= nil then
        if isAll then
            self._wtCrossImg:AsyncSetImagePath(Config.IconPath[14] or "", true)
            self._wtCrossImg:SelfHitTestInvisible()
            self._wtFIconImg:Collapsed()
            self._wtFNameTxt:SetText(Config.Loc.AllCoreAwards)
        else
            local itemData = General.GetSkinItemData({id = self._trackWeaponId})
            if itemData then
                if General.IsHaved(itemData.id) then
                    self._wtCrossImg:AsyncSetImagePath(Config.IconPath[13] or "", true)
                    self._wtCrossImg:SelfHitTestInvisible()
                    self._wtFIconImg:Collapsed()
                    self._wtFNameTxt:SetText(Config.Loc.ActRewardTitle)
                    self._wtRedotIns:SetReddotVisible(true, EReddotType.Normal)
                else
                    local param = FRTIParamData()
                    param.bShouldMerge = false
                    self._wtComPanel:Visible()
                    RuntimeIconTool.SetItemIcon(itemData, self._wtMainIcon, param)
    
                    -- self._wtFIconImg:AsyncSetImagePath(itemData.itemIconPath or "", false)
                    self._wtFIconImg:SelfHitTestInvisible()
                    self._wtCrossImg:Collapsed()
                    self._wtFNameTxt:SetText(itemData.name or "")
                end
            else
                self._wtCrossImg:AsyncSetImagePath(Config.IconPath[13] or "", true)
                self._wtCrossImg:SelfHitTestInvisible()
                self._wtFNameTxt:SetText(Config.Loc.ActRewardTitle)
                self._wtFIconImg:Collapsed()
                self._wtRedotIns:SetReddotVisible(true, EReddotType.Normal)
            end
        end
        --如果没有数据隐藏定轨
        local time = self._end_time
        if type(time) == "number" and time > 0 then
            self._wtFTimeTxt:SetText(General.GetTimeStr(time, true))
            self._wtFTimeTxt:SelfHitTestInvisible()
        else
            self._wtFTimeTxt:Collapsed()
        end
        if time then
            self._wtAppPanel:SelfHitTestInvisible()
        else
            self._wtAppPanel:Collapsed()
        end
    else
        self._wtAppPanel:Collapsed()
    end
    self:_AddGamepadInputs()
end

function ActivityLotteryDrawPanel:OnUpRewardIconLoaded(DFMImage, Tile)
    local weaponMaterial = self._wtFIconImg:GetDynamicMaterial()
    if weaponMaterial then
        self._wtComPanel:Collapsed()
        weaponMaterial:SetTextureParameterValue("Texture_Main", Tile.Texture)
        local size = self._wtFIconImg.slot:GetSize()
        local fRatio = Tile.RawSize.Y / Tile.RawSize.X
        self._wtFIconImg.slot:SetSize(FVector2D(size.X, size.X * fRatio))
    end
end

--跳转展示
function ActivityLotteryDrawPanel:_OnJumpClicked(index)
    if index == 1 then
        local itemData = self._core_item
        if itemData then
            if itemData.itemMainType == EItemType.WeaponSkin then
                Module.Collection:ShowWeaponSkinDetailPage(itemData)
            else
                Module.ItemDetail:OpenItemDetailPanel(itemData)
            end
        end
    elseif index == 2 then
        local list = {}
        for i, value in ipairs(self._showIds or {}) do
            table.insert(list, {id = value})
        end
        local data = {
            items = list,
            title = Config.Loc.ActivityReward,
        }
        Facade.UIManager:AsyncShowUI(UIName2ID.ActivityWeaponDisplayPanel, nil, self, data)
    elseif index == 3 then
        local func = function(itemData)
            if itemData then
                Server.StoreServer:ReqShopSetMandelBoxUp(self._boxId, self._groupId, itemData.id, itemData.num_id, 3)
            end
        end
        local content = {
            {
                id = UIName2ID.Assembled_CommonMessageTips_V2,
                data = {
                    textContent = Config.Loc.ActRewardTipys,
                    styleRowId  = "C002",
                },
            }
        }
        local data = {
            items = self._trackList,
            tipName = Config.Loc.ActRewardTitle,
            tipDesc = Config.Loc.ActRewardDescs,
            tipContent = content,
            title = Config.Loc.ActRewardTitle,
        }
        Facade.UIManager:AsyncShowUI(UIName2ID.ActivityWeaponDisplayPanel, nil, self, data, func)
    end
end

--请求抽奖
function ActivityLotteryDrawPanel:_OnBtnClicked(index)
    local drawId = self._drawId
    if drawId then
        local num = index == 1 and 1 or 10
        local items = {
            [1] = {
                id  = drawId,
                num = num,
            }
        }
        if General.GetPropNumByPropId(drawId) < num then
            local itemData = General.GetSkinItemData({id = drawId})
            if itemData then
                Module.CommonTips:ShowSimpleTip(General.GetText(Config.Loc.Insufficient, itemData.name))
            end
            return
        end
        Module.Reward:EnableNTFCall("Hero", false)--兜底方案(解决弹窗多弹的问题)
        Server.StoreServer:OpenMandelBox(items, num, false)--抽奖
    end
end

function ActivityLotteryDrawPanel:_OnOpenRewardClicked(isBool)
    local reward = self._rewardList
    local history = self._historyList
    if isBool then
        self:_OpenRewardItem(reward, history)
    else
        self:_OpenRewardItem()
    end
end

function ActivityLotteryDrawPanel:_OpenRewardItem(reward, history)
    if self._RewardListUI == nil then
        local uiNavId = UIName2ID.ActivityLotteryDrawItem
        local container = self:GetContainer()
        self._container = container
        if container and uiNavId then
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self, uiNavId, container)--获取sub实例
            self._RewardListUI = getfromweak(uiIns)
        end
    end
    if self._RewardListUI and self._container then
        self._wtHotzone = {self._RewardListUI}
        if reward then
            self._RewardListUI:InitItemData(reward, history, 1)
            self._container:SelfHitTestInvisible()
        else
            self._container:Collapsed()
        end
        if self._btn then
            self._btn:SetIsChecked(reward ~= nil)
        end
    end
end

function ActivityLotteryDrawPanel:OnClose()
    self:_RemoveGamepadInputs()
    self._RewardListUI = nil
    self._container = nil
    if self._wtRedotIns then
        self._wtRedotIns:SetReddotVisible(false)
        self._wtRedotIns = nil
    end
end

function ActivityLotteryDrawPanel:_AddGamepadInputs()
    self:_RemoveGamepadInputs()
    if IsHD() then
        self._handles = {}
        local actionNames = {
            [1] = "SingleDraw_ActivityPrize",
            [2] = "MultiDraw_ActivityPrize",
        }
        for index, widget in ipairs(self._wtDraws or {}) do
            if widget.btn then
                local handle = self:AddInputActionBinding(actionNames[index], EInputEvent.IE_Pressed, widget.btn.ButtonClick, widget.btn, EDisplayInputActionPriority.UI_Pop)
                if handle  then
                    widget.btn:SetDisplayInputActionWithLongPress(handle, self, actionNames[index], true, nil, true)
                    table.insert(self._handles, handle)
                end
            end
        end
        local award = function()
            local wtCommonMainBtn = self:GetShowPoolBtn()
            if wtCommonMainBtn then
                wtCommonMainBtn:SelfClick()
            end
        end
        local awardFunc = SafeCallBack(award, self)

        local details = function(caller, index)
            self:_OnJumpClicked(index)
        end
        local detailsFunc = SafeCallBack(details, self, self._defaultIndex)
        local analysis    = SafeCallBack(details, self, 3)

        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "Act_Reward_Analytic_Designation", func = analysis,    caller = self, bUIOnly = false},
            {actionName = "Act_Reward_Sign_Details",         func = detailsFunc, caller = self, bUIOnly = false},
            {actionName = "RewardsDetails_ActivityPrize",    func = awardFunc,   caller = self, bUIOnly = false},
        })
    end
end

function ActivityLotteryDrawPanel:_RemoveGamepadInputs()
    for index, handle in ipairs(self._handles or {}) do
        self:RemoveInputActionBinding(handle)
    end
    self._handles = nil
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

return ActivityLotteryDrawPanel