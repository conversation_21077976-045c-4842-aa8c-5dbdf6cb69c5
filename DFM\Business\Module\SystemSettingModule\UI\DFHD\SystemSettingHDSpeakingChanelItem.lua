----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDSpeakingChanelItem
local SystemSettingHDBaseItem = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDBaseItem"
local SystemSettingHDDropDownItem = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDDropDownItem"
local SystemSettingHDSpeakingChanelItem = class("SystemSettingHDSpeakingChanelItem", SystemSettingHDDropDownItem)

local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local EDFMGamePlaySubMode = import "EDFMGamePlaySubMode"


function SystemSettingHDSpeakingChanelItem:OnOpen()
    SystemSettingHDDropDownItem.OnOpen(self)
end

function SystemSettingHDSpeakingChanelItem:OnShowBegin()
    SystemSettingHDBaseItem.OnShowBegin(self)
    if not CommonSettingLogicHD.IsValidID(self.ID) then
        return
    end
    self._curIndex = CommonSettingLogicHD.GetDataByID(self.ID)
    local options = CommonSettingLogicHD.GetDropDownOptionsByID(self.ID)
    if self:IsInCommander() then
        table.insert(options, Module.GVoice.Config.Loc.GVoiceTeamIdentity_Commander)
    else
        self:TryResetChannelFromCommander()
    end
    UIUtil.InitDropDownBox(self._wtDropDown, options, {},  self._curIndex)
    loginfo("SpeakingChannel is ",self._curIndex)

end



function SystemSettingHDSpeakingChanelItem:IsInCommander() --是否在指挥官模式下，并且玩家为指挥官
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if Module.GVoice:IsCommanderGameMode() and (Module.GVoice:IsCampChannelEnable()) then
        return true
    end
    return false
end

function SystemSettingHDSpeakingChanelItem:OnReloadSetting()
    SystemSettingHDDropDownItem.OnReloadSetting(self)
end

function SystemSettingHDSpeakingChanelItem:_OnDropDownBoxOpenStateChanged(bOpen)
    SystemSettingHDDropDownItem._OnDropDownBoxOpenStateChanged(self, bOpen)
end

function SystemSettingHDSpeakingChanelItem:TryResetChannelFromCommander() --用于从指挥官模式退出后重置回全队频道
    local buttonType =  Module.GVoice:GetMicrophoneButtonType()
    local lastIndex = CommonSettingLogicHD.GetDataByID(self.ID)
    if lastIndex == ESpeakingChanel.Camp  then
        self._curIndex = ESpeakingChanel.All
        CommonSettingLogicHD.SetDataByID(self.ID,ESpeakingChanel.All)
    end
end

function SystemSettingHDSpeakingChanelItem:OnClose()
    SystemSettingHDDropDownItem.OnClose(self)
end
return SystemSettingHDSpeakingChanelItem