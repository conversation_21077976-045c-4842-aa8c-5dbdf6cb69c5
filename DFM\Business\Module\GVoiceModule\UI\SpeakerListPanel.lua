----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGVoice)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class SpeakerListPanel : LuaUIBaseView
local SpeakerListPanel = ui("SpeakerListPanel")

--region SpeakerContainer

---@class SpeakerContainer : Object
local SpeakerContainer = class("SpeakerContainer", Object)

function SpeakerContainer:Ctor(owner, uiRoot, viewMaxNum)
    self.owner = owner
    self.uiRoot = uiRoot
    self.dataList = {}
    self.viewList = {}
    self.viewMaxNum = viewMaxNum or 1
end

function SpeakerContainer:SetData(openId, isSpeaking, roomChannel, identityInfo)
    loginfo("SpeakerContainer:SetData ",openId, isSpeaking, roomChannel)
    local bChange = false
    if isSpeaking then
        --指挥官交接，移除上一个指挥官的显示
        local isCamp = roomChannel == EGVoiceRoomChannel.Camp
        local isCommander = isCamp and identityInfo and identityInfo.identity == 3
        if isCommander then
            if self.commanderOpenId and self.commanderOpenId ~= openId then
                loginfo("SpeakerContainer SetData remove old commander", self.commanderOpenId, openId)
                self:RemoveDataImmediately(self.commanderOpenId)
                self.commanderOpenId = openId
            end
        end
        --如果已有该玩家，更新显示状态
        local bFind = false
        for _, data in ipairs(self.dataList) do
            if data.id == openId then
                data.timer = os.time()
                data.bRemove = false
                bFind = true
                break
            end
        end
        --如果没有该玩家，新增一个
        if not bFind then
            local data = {
                id = openId,
                roomChannel = roomChannel,
                identityInfo = identityInfo,
                timer = os.time(),
                bRemove = false
            }
            self:AddData(data)
            bChange = true
        end
    else
        self:RemoveData(openId)
    end
    if bChange then
        self:UpdateViews()
    end
end

function SpeakerContainer:AddData(data)
    table.insert(self.dataList, data)
end

function SpeakerContainer:RemoveData(id)
    for i, data in ipairs(self.dataList) do
        if data.id == id then
            --table.remove(self.dataList, i)
            data.bRemove = true
            data.timer = os.time()
            break
        end
    end
end

function SpeakerContainer:RemoveDataByTick()
    local bRemove = false
    for i = #self.dataList, 1, -1 do
        local data = self.dataList[i]
        local interval = os.time() - data.timer
        --保底，15s还没清除则手动清一次，避免残留
        local bNeedRemove = interval > 15 or (data.bRemove and interval > 1)
        --没说话1秒后删除
        if bNeedRemove then
            local currentTime = os.time()
            if currentTime - data.timer >= 1 then
                table.remove(self.dataList, i)
                bRemove = true
                break
            end
        end
    end
    return bRemove
end

function SpeakerContainer:RemoveDataImmediately(id)
    local bRemove = false
    for i, data in ipairs(self.dataList) do
        if data.id == id then
            table.remove(self.dataList, i)
            bRemove = true
            break
        end
    end
    return bRemove
end

function SpeakerContainer:ParseDataList()
    local newDataList = {}
    local exData = nil
    local exTeamId = nil
    local exTeamIdList = nil
    for index, data in ipairs(self.dataList) do
        if index == self.viewMaxNum then
            exData = self:_GenViewData(data)
            if data.identityInfo and data.identityInfo.teamId then
                exTeamId = data.identityInfo.teamId
                table.insert(exTeamIdList, exTeamId)
            end
        elseif index > self.viewMaxNum then
            if not exTeamIdList then
                exTeamIdList = {}
            end
            if data.identityInfo and data.identityInfo.teamId and not table.contains(exTeamIdList, data.identityInfo.teamId) then
                table.insert(exTeamIdList, data.identityInfo.teamId)
            end
        else
            local newData = self:_GenViewData(data)
            table.insert(newDataList, newData)
        end
    end
    --数量超上限，合并成一个
    if exTeamIdList and #exTeamIdList > 1 then
        local exNameList = {}
        for _, teamId in ipairs(exTeamIdList) do
            local teamName = self:_GetCampTeamNameByTeamId(teamId)
            if not string.isempty(teamName) then
                table.insert(exNameList, teamName)
            end
        end
        local mergeName = table.concat(exNameList, "、")
        mergeName = Module.GVoice.Config.Loc.GVoiceTeamIdentity_TeamPreName .. mergeName
        exData.name = mergeName
        exData.isCommander = false
    end
    if exData then
        table.insert(newDataList, exData)
    end
    return newDataList
end

function SpeakerContainer:_GenViewData(data)
    local viewName = Module.GVoice:GetTeamMemberName(data.id) or ""
    local teamName = ""
    local roomChannel = data.roomChannel
    local isCamp = roomChannel == EGVoiceRoomChannel.Camp
    local isCommander = isCamp and data.identityInfo and data.identityInfo.identity == 3

    -- BEGIN MODIFICATION @ VIRTUOS: TRC - replace player name with platform online id.
    if IsPS5() then
        local playerOnlineId = self:GetPlatformOnlineId(data.id)
        if playerOnlineId ~= nil then
            viewName = playerOnlineId
        end
    end
    -- END MODIFICATION

    if isCamp then
        loginfo("SpeakerContainer _GenViewData isCamp", data.id, viewName, data.identityInfo.identity, data.identityInfo.teamId, isCommander)
        teamName = self:_GetTeamName(data.identityInfo) or ""
        viewName = teamName.." · "..viewName
    else
        loginfo("SpeakerContainer _GenViewData ", data.id, viewName)
    end
    local viewData = {
        name = viewName,
        teamName = teamName,
        roomChannel = roomChannel,
        isCommander = isCommander,
    }
    return viewData
end

function SpeakerContainer:_GetTeamName(identityInfo)
    local teamName = ""
    if identityInfo then
        if identityInfo.identity == 1 then
            teamName = Module.GVoice.Config.Loc.GVoiceTeamIdentity_TeamMember
        elseif identityInfo.identity == 2 then
            teamName = Module.GVoice.Config.Loc.GVoiceTeamIdentity_TeamLeader
            teamName = teamName..tostring(self:_GetCampTeamNameByTeamId(identityInfo.teamId))
        elseif identityInfo.identity == 3 then
            teamName = Module.GVoice.Config.Loc.GVoiceTeamIdentity_Commander
        end
    end
    return teamName
end

--大战场调用
function SpeakerContainer:_GetCampTeamNameByTeamId(teamId)
    local teamName = ""
    teamId = teamId or 0

    if self.teamNameList then
        if self.teamNameList[teamId] then
            teamName = self.teamNameList[teamId]
            return teamName
        end
    else
        self.teamNameList = {}
    end

    local gameState = UGameplayStatics.GetGameState(GetWorld())
    if not gameState then
        return teamName
    end
 
    local battleFieldCommanderDataComponent = gameState:GetCommanderComponent()

    if battleFieldCommanderDataComponent then
   
        local TeamNameMap = battleFieldCommanderDataComponent.TeamNameMap
        if TeamNameMap then
            if gameState.MatchInfo.MatchType == MatchType.MatchTypeRoom then
                local teamId_Room = (teamId % 5) * 2
                teamName = TeamNameMap:Get(teamId_Room)
                loginfo("SpeakerContainer _GetCampTeamNameByTeamId", teamName, teamId_Room, teamId)
            else
                teamName = TeamNameMap:Get(teamId)
                loginfo("SpeakerContainer _GetCampTeamNameByTeamId --", teamName, teamId)
            end
           
            self.teamNameList[teamId] = teamName
            loginfo("SpeakerContainer _GetCampTeamNameByTeamId", teamName)
        end

    end

    return teamName
end

function SpeakerContainer:UpdateViews()
    local dataList = self:ParseDataList()
    local dataListNum = #dataList
    local viewListNum = #self.viewList

    while dataListNum > viewListNum do
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self.owner, UIName2ID.SpeakerListItem, self.uiRoot)
        local uiIns = getfromweak(weakUIIns)
        local view = {
            instanceID = instanceID,
            uiIns = uiIns
        }
        table.insert(self.viewList, view)
        viewListNum = viewListNum + 1
    end

    while dataListNum < viewListNum do
        local view = table.remove(self.viewList)
        Facade.UIManager:RemoveSubUI(self.owner, UIName2ID.SpeakerListItem, view.instanceID)
        viewListNum = viewListNum - 1
    end

    for index, view in ipairs(self.viewList) do
        if view.uiIns then
            view.uiIns:SetData(dataList[index])
        else
            if dataList[index] then
                logwarning("SpeakerListPanel:UpdateViews uiIns is nil", dataList[index].name)
            end
        end
    end
end

function SpeakerContainer:OnTick()
    if self:RemoveDataByTick() then
        self:UpdateViews()
    end
end

--- BEGIN MODIFICATION @ VIRTUOS: TRC - replace player name with platform online id.
function SpeakerContainer:GetPlatformOnlineId(openId)
    local onlineId = nil

    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local OnlineIdManager = UDFMOnlineIdentityManager.Get(GetWorld())
        if OnlineIdManager then
            local numOpenId = ULuautils.GetUInt64StrToInt64(openId)
            local PS5OnlineId = OnlineIdManager:GetPlayerPlatformIdByOpenId(numOpenId)
            if not string.isempty(PS5OnlineId) then
                onlineId = PS5OnlineId
            end
        end
    end

    return onlineId
end
--- END MODIFICATION

--endregion

function SpeakerListPanel:Ctor()
    self._wtSpeakerItemRoot = self:Wnd("DFVerticalBox_57", UIWidgetBase)
    self._wtCampSpeakerItemRoot = self:Wnd("DFVerticalBox_46", UIWidgetBase)
    self._speakerContainer = nil
    self._speakerMaxViewNum = 4
    self._speakerCampContainer = nil
    self._speakerCampMaxViewNum = 4
    self._posType = -1
end

function SpeakerListPanel:OnClose()
    self:StopTick()
    loginfo("SpeakerListPanel:OnClose")
    Facade.UIManager:ClearSubUIByParent(self, self._wtSpeakerItemRoot)
    Facade.UIManager:ClearSubUIByParent(self, self._wtCampSpeakerItemRoot)
    self._speakerContainer = nil
    self._speakerCampContainer = nil
    Module.GVoice.Field:SetSpeakerListPanel(nil)

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())
        if DFMOnlineIdentityManager and DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate and self._OnlineIdUpdateHandle then
            DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate:Remove(self._OnlineIdUpdateHandle)
            self._OnlineIdUpdateHandle = nil
        end
    end
    --END MODIFICATION
end

function SpeakerListPanel:OnOpen()
    self:_AdjustPos()
    self:StartTick()

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager and DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate then
            self._OnlineIdUpdateHandle = DFMOnlineIdentityManager.OnPS5OnlineIdCacheUpdateDelegate:Add(
                CreateCPlusCallBack(self.HandleOnlineIdCacheUpdate, self)
            )
        end
    end
    --END MODIFICATION
end

function SpeakerListPanel:_AdjustPos()
    if self._isInGame then
        if self._posType ~= 1 then
            self._posType = 1
            self:SetType(self._posType)
        end
    else
        if self._posType ~= 0 then
            self._posType = 0
            self:SetType(self._posType)
        end
    end
end

function SpeakerListPanel:OnInitExtraData(openId, isSpeaking, isInGame, roomChannel, identityInfo)
    --loginfo("[GVoice] SpeakerListPanel:OnInitExtraData:", openId, isSpeaking, isInGame, roomChannel)
    self._isInGame = isInGame
    self:_AdjustPos()

    local isCamp = false

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsPS5() then
        self:UpdatePS5OnlineIdsByOpenId(openId)
    end
    --END MODIFICATION

    if roomChannel == EGVoiceRoomChannel.Camp then
        isCamp = true
        -- if identityInfo and identityInfo.identity and identityInfo.identity <= 1 then
        --     logerror("SpeakerListPanel:OnInitExtraData speakerContainer not camp but speak in camp room:", openId, isSpeaking, isInGame, roomChannel, identityInfo.identity)
        -- else
        --     isCamp = true
        -- end
    end

    if isCamp then
        if not self._speakerCampContainer then
            self._speakerCampContainer = SpeakerContainer:NewIns(self, self._wtCampSpeakerItemRoot, self._speakerCampMaxViewNum)
        end
        self._speakerCampContainer:SetData(openId, isSpeaking, roomChannel, identityInfo)
    else
        if not self._speakerContainer then
            self._speakerContainer = SpeakerContainer:NewIns(self, self._wtSpeakerItemRoot, self._speakerMaxViewNum)
        end
        self._speakerContainer:SetData(openId, isSpeaking, roomChannel, identityInfo)
    end
end

--创建定时器
function SpeakerListPanel:StartTick()
    self:StopTick()
    self.timeHandleTick = Timer:NewIns(1, 0)
    self.timeHandleTick:AddListener(self.OnTick, self)
    self.timeHandleTick:Start()
end

--取消定时器
function SpeakerListPanel:StopTick()
    if self.timeHandleTick then
        self.timeHandleTick:Release()
        self.timeHandleTick = nil
    end
end

function SpeakerListPanel:OnTick()
    if self._speakerContainer then
        self._speakerContainer:OnTick()
    end
    if self._speakerCampContainer then
        self._speakerCampContainer:OnTick()
    end
end

--BEGIN MODIFICATION @ VIRTUOS : 
function SpeakerListPanel:HandleOnlineIdCacheUpdate()
    if IsPS5() and self:IsVisible() then
        if self._speakerContainer then
            self._speakerContainer:UpdateViews()
        end

        if self._speakerCampContainer then
            self._speakerCampContainer:UpdateViews()
        end
    end
end

function SpeakerListPanel:UpdatePS5OnlineIdsByOpenId(OpenIdStr)
    if IsPS5() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager then
            local numOpenId = ULuautils.GetUInt64StrToInt64(OpenIdStr)
            DFMOnlineIdentityManager:AddPendingUpdateOpenId(numOpenId)
            DFMOnlineIdentityManager:BeginUpdatePendingOpenId()
        end
    end
end
--END MODIFICATION

return SpeakerListPanel
