----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDUIPanel
local SystemSettingHDUIPanel = ui("SystemSettingHDUIPanel")
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SystemSettingHDLanguagePanel = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDLanguagePanel"

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

function SystemSettingHDUIPanel:Ctor()
    self:_BindWidget()
end

function SystemSettingHDUIPanel:_BindWidget()
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
    self._wtItemPanel = self:Wnd("wtItemPanel", UILightWidget)
    self._wtItemTextLanuage = self:Wnd("_wtItemTextLanuage", UIWidgetBase)
    self._wtLanguagePanel = self:Wnd("_wtLanguagePanel", UILightWidget)
end

function SystemSettingHDUIPanel:_BindBtnEvent()
end

function SystemSettingHDUIPanel:OnOpen()
    self:_BindBtnEvent()

    if IsBuildRegionCN() and (not IsInEditor()) then
		self._wtItemTextLanuage:Collapsed()
        self._wtLanguagePanel:Collapsed()
	else
        self._wtLanguagePanel:SelfHitTestInvisible()
		self._wtItemTextLanuage:SelfHitTestInvisible() -- 海外包才需要语言切换
	end
    --self._wtLanguagePanel:SelfHitTestInvisible()

    local countToContinue = CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
    CommonSettingLogicHD.RefreshItemUIBackground(self._wtLanguagePanel, countToContinue)
end

function SystemSettingHDUIPanel:OnShowBegin()
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.UISetting then
        local list = {
                -- BEGIN MODIFICATION @ VIRTUOS : 增加确认按键提示
                {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
                -- END MODIFICATION
                {actionName = "Reset", func = self._OnReset, caller = self}
        }
        local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
        for _, v in ipairs(globalList) do
            table.insert(list, v)
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
        --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
        self:_RegisterNavGroup()
        --END MODIFICATION
    end
end

function SystemSettingHDUIPanel:OnShow()
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.UISetting then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
    end
end

function SystemSettingHDUIPanel:_OnReset()
    local fReset = function()
        SettingLogicHD.ResetCurrentSettings()
    end
    local resetInputTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetUITxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    Module.CommonTips:ShowConfirmWindow(resetInputTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

function SystemSettingHDUIPanel:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    self:_RemoveNavGroup()
    --END MODIFICATION
end

function SystemSettingHDUIPanel:OnHide()
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
end

function SystemSettingHDUIPanel:_OnClickBtn()
end

--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function SystemSettingHDUIPanel:_RegisterNavGroup()
    local wtScrollBox = self:Wnd("ScrollBox_206", UIWidgetBase)
    
    if wtScrollBox then
	    if not self._NavGroup then
	        self._NavGroup = WidgetUtil.RegisterNavigationGroup(wtScrollBox, self, "Hittest")
	    end
    end

	if self._NavGroup then
		-- 存在部分不希望导航的目标，因此需要逐个配置NavWidget
        local NavWidgetTable = {}
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_5", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Check_1", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Sprint", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_ListTitle_2", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Perspective_1", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_Perspective_2", NavWidgetTable)

        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_Mp", NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_Mp_1", NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_Mp_2", NavWidgetTable)
        self:_AddSliderWidgetToNavWidgetByName("WBP_SetUpComponent_Mp_3", NavWidgetTable)

        self:_AddSelfWidgetToNavWidgetByName("_wtItemTextLanuage", NavWidgetTable)
        self:_AddSelfWidgetToNavWidgetByName("WBP_SetUpComponent_MultipleChoice_2", NavWidgetTable)

        for index, value in ipairs(NavWidgetTable) do
            self._NavGroup:AddNavWidgetToArray(NavWidgetTable[index])
        end

        self._NavGroup:SetScrollRecipient(wtScrollBox)
		WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
end

function SystemSettingHDUIPanel:_RemoveNavGroup()
	if self._NavGroup then
		self._NavGroup = nil
	end
	WidgetUtil.RemoveNavigationGroup(self)
end

-- 根据名字查找控件并加入导航组
function SystemSettingHDUIPanel:_AddSelfWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            table.insert(NavWidgetTable, targetWidget)
        end
    end
end

-- 根据SplitBtn控件的名字，查找Slider控件并加入导航组
function SystemSettingHDUIPanel:_AddSliderWidgetToNavWidgetByName(WidgetName, NavWidgetTable)
    if WidgetName then
        local targetWidget = self:Wnd(WidgetName, UIWidgetBase)

        if targetWidget then
            local Slider = targetWidget:Wnd("Slider", UIWidgetBase)
            if Slider then
                local subSlider = Slider:Wnd("Slider_167", UIWidgetBase)
                if subSlider then
                    table.insert(NavWidgetTable, subSlider)
                end
            end
        end
    end
end
--END MODIFICATION

return SystemSettingHDUIPanel
