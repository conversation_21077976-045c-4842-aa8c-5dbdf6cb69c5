----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local InputSettingLogicHD = {}
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local UClientSettingHelperHD = import("ClientSettingHelperHD")
local UDFHDKeySettingManager = import "DFHDKeySettingManager"
local UDataTableSystemManagerLite = import("DataTableSystemManagerLite")
local FWeaponZoomDataHD = import("WeaponZoomDataHD")
local FZoomratedDataHD = import("ZoomratedDataHD")

local _sensitivitySetting = import("ClientSensitivitySettingHD").Get()
local _vehicleSetting = import("ClientVehicleSettingHD").Get()
local _bhdSetting = import("ClientBHDSetting").Get()

local _zoomDataTable
local _vehicleZoomDataTable
local _vehicleZoomDataTable2
local _vehicleZoomDataTable3
--local _sortedZoomData = nil
local _zoomDataTablePath = "WeaponZoomDataSettingHD"
local _vehicleZoomDataTablePath = "VehicleWeaponZoomDataSettingHD"
local _vehicleZoomDataTablePath2 = "VehicleWeaponZoomDataSettingHD2"
local _vehicleZoomDataTablePath3 = "VehicleWeaponZoomDataSettingHD3"
local _vehicleZoomDataTablePath4 = "VehicleWeaponZoomDataSettingHD4"
local UClientBaseSetting = import("ClientBaseSetting")
local MouseSensitivityMode = import("EMouseSensitivityMode")
local ESensitivityModeHD = import("ESensitivityModeHD")

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
local _gameSetting = import("ClientGameSettingHD").Get()
local UDFMGameplayInputManager = import("DFMGameplayInputManager")
local DFMGameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())

local _gamepadZoomDataTable = nil
local _gamepadVehicleZoomDataTable = nil
local _gamepadVehicleZoomDataTable2 = nil
local _gamepadVehicleZoomDataTable3 = nil

local _gamepadZoomDataTablePath = "WeaponZoomDataSettingHD_Gamepad"
local _gamepadVehicleZoomDataTablePath = "VehicleWeaponZoomDataSettingHD_Gamepad"
local _gamepadVehicleZoomDataTablePath2 = "VehicleWeaponZoomDataSettingHD_Gamepad2"
local _gamepadVehicleZoomDataTablePath3 = "VehicleWeaponZoomDataSettingHD_Gamepad3"
local _gamepadVehicleZoomDataTablePath4 = "VehicleWeaponZoomDataSettingHD_Gamepad4"

local EGamepadSensitivityPresetType = import("EGamepadSensitivityPresetType")
local _gamepadSensitivityPresetDatatable = nil
local _gamepadSensitivityPresetDatatablePath = "GamepadSensitivityPresetHD"

local _gamepadBHDSensitivityPresetDatatable = nil
local _gamepadBHDSensitivityPresetDatatablePath = "GamepadSensitivityPresetBHD"
-- END MODIFICATION

local function _GetZoomDataTable(index)
    if index == 0 then
        if not _zoomDataTable then
            _zoomDataTable = Facade.TableManager:GetTable(_zoomDataTablePath)
        end

        return _zoomDataTable
    elseif index == 1 then
        if not _vehicleZoomDataTable then
            _vehicleZoomDataTable = Facade.TableManager:GetTable(_vehicleZoomDataTablePath)
        end
        return _vehicleZoomDataTable

    elseif index == 2 then
        if not _vehicleZoomDataTable2 then
            _vehicleZoomDataTable2 = Facade.TableManager:GetTable(_vehicleZoomDataTablePath2)
        end
        return _vehicleZoomDataTable2

    elseif index == 3 then
        if not _vehicleZoomDataTable3 then
            _vehicleZoomDataTable3 = Facade.TableManager:GetTable(_vehicleZoomDataTablePath3)
        end
        return _vehicleZoomDataTable3
    elseif index == 4 then
        if not _vehicleZoomDataTable4 then
            _vehicleZoomDataTable4 = Facade.TableManager:GetTable(_vehicleZoomDataTablePath4)
        end
        return _vehicleZoomDataTable4
    end
end

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
local function _GetGamepadZoomDataTable(index)
    if index == 0 then
        if not _gamepadZoomDataTable then
            _gamepadZoomDataTable = Facade.TableManager:GetTable(_gamepadZoomDataTablePath)
        end

        return _zoomDataTable
    elseif index == 1 then
        if not _gamepadVehicleZoomDataTable then
            _gamepadVehicleZoomDataTable = Facade.TableManager:GetTable(_gamepadVehicleZoomDataTablePath)
        end
        return _gamepadVehicleZoomDataTable

    elseif index == 2 then
        if not _gamepadVehicleZoomDataTable2 then
            _gamepadVehicleZoomDataTable2 = Facade.TableManager:GetTable(_gamepadVehicleZoomDataTablePath2)
        end
        return _gamepadVehicleZoomDataTable2

    elseif index == 3 then
        if not _gamepadVehicleZoomDataTable3 then
            _gamepadVehicleZoomDataTable3 = Facade.TableManager:GetTable(_gamepadVehicleZoomDataTablePath3)
        end
        return _gamepadVehicleZoomDataTable3
    elseif index == 4 then
        if not _gamepadVehicleZoomDataTable4 then
            _gamepadVehicleZoomDataTable4 = Facade.TableManager:GetTable(_gamepadVehicleZoomDataTablePath4)
        end
        return _gamepadVehicleZoomDataTable4
    end
end
-- END MODIFICATION

function InputSettingLogicHD.GetZoomDataRow(id, index)
    local dataTable = _GetZoomDataTable(index)
    local foundData = dataTable[id]
    assert(foundData, "Can not find setting data: " .. id)
    return foundData
end

local function _ADSSensitivityGetter(settingObj, _, index)
    local zoomData = settingObj.ZoomDataHD:Get(index)
    return zoomData.ADSSensitivity
end

local function _ADSSensitivitySetter(settingObj, _, value, index)
    local zoomData = settingObj.ZoomDataHD:Get(index)
    zoomData.ADSSensitivity = value
end

local function _RegADSSensitivityBatch(id, index)
    SettingRegLogicHD.RegMapping(
        id,
        _sensitivitySetting,
        nil,
        _ADSSensitivityGetter,
        _ADSSensitivitySetter,
        nil,
        {index}
    )
end

local function _SceneFOVCalcOpenCameraFOVMobileFunc(settingMobileObj, value)
    settingMobileObj.bSceneFOVCalcOpenCameraFOV = value
end

local function _RegSceneFOVCalcOpenCameraFOVBatch(id, key)
    SettingRegLogicHD.RegPassThroughMapping(id, key)
    SettingRegLogicHD.RegMobileMapping(
        id,
        UClientBaseSetting.Get(GetGameInstance()),
        _SceneFOVCalcOpenCameraFOVMobileFunc
    )
end

local function _SideAimingControlModeSetter(settingObj, key, value)
    settingObj:SetSideAimingControlMode(value)
    local DFHDKeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    DFHDKeySettingMgr:PreprocRows()
    DFHDKeySettingMgr:ApplyFunctionMode(DFHDKeySettingMgr.CurKMSettingRows, "SideAimingControlModeSetter")
end

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : 外圈灵敏度加成
-- ExtraSensitivity
local function _ExtraSensitivityDataHDHorizontalValueGetter(settingObj, key)
    return settingObj[key].HorizontalExtraSensitivity
end
local function _ExtraSensitivityDataHDHorizontalValueSetter(settingObj, key, value)   
    settingObj[key].HorizontalExtraSensitivity = value
end
local function _ExtraSensitivityDataHDHorizontalValueApplyFunc(settingObj, key, value)
end

local function _ExtraSensitivityDataHDVerticalValueGetter(settingObj, key)
    return settingObj[key].VerticalExtraSensitivity
end
local function _ExtraSensitivityDataHDVerticalValueSetter(settingObj, key, value)
    settingObj[key].VerticalExtraSensitivity = value
end
local function _ExtraSensitivityDataHDValueApplyFunc(settingObj, key, value)
end

-- StarupTime
local function _ExtraSensitivityDataHDStartupTimeGetter(settingObj, key)
    return settingObj[key].StarupTime
end
local function _ExtraSensitivityDataHDStartupTimeSetter(settingObj, key, value)
    settingObj[key].StarupTime = value
end
local function _ExtraSensitivityDataHDStartupTimeApplyFunc(settingObj, key, value)
end

-- DelayTime
local function _ExtraSensitivityDataHDDelayTimeGetter(settingObj, key)
    return settingObj[key].DelayTime
end
local function _ExtraSensitivityDataHDDelayTimeSetter(settingObj, key, value)
    settingObj[key].DelayTime = value
end
local function _ExtraSensitivityDataHDDelayTimeApplyFunc(settingObj, key, value)
end

local function _ADSCustomGetter(settingObj, key)
    return settingObj[key].bEnableGamepadADSCustom
end
local function _ADSCustomSetter(settingObj, key, value)
    settingObj[key].bEnableGamepadADSCustom = value
end
local function _ADSCustomSetterApplyFunc(settingObj, key, value)
end

-- DeadZone
local function _GamepadThumbRestrictDataHDDeadZoneGetter(settingObj, key)
    return settingObj[key].DeadZone
end
local function _GamepadThumbRestrictDataHDDeadZoneSetter(settingObj, key, value)
    settingObj[key].DeadZone = value
end
local function _GamepadThumbRestrictDataHDDeadZoneApplyFunc(settingObj, key, value)
end

-- MaximumThreshold
local function _GamepadThumbRestrictDataHDMaximumThresholdGetter(settingObj, key)
    return settingObj[key].MaximumThreshold
end
local function _GamepadThumbRestrictDataHDMaximumThresholdSetter(settingObj, key, value)
    settingObj[key].MaximumThreshold = value
end
local function _GamepadThumbRestrictDataHDMaximumThresholdApplyFunc(settingObj, key, value)
end

-- AxisZoneAssist
local function _GamepadThumbRestrictDataHDAxisZoneAssistGetter(settingObj, key)
    return settingObj[key].AxisZoneAssist
end
local function _GamepadThumbRestrictDataHDAxisZoneAssistSetter(settingObj, key, value)
    settingObj[key].AxisZoneAssist = value
end
local function _GamepadThumbRestrictDataHDAxisZoneAssistApplyFunc(settingObj, key, value)
end
-- END MODIFICATION

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : 载具垂直水平灵敏度拆分
-- FPP 水平
local function _HorizontalSensitivityFPPGetter(settingObj, key)
    return settingObj[key].HorizontalSensitivityFPP
end
local function _HorizontalSensitivityFPPSetter(settingObj, key, value)
    settingObj[key].HorizontalSensitivityFPP = value
end
local function _HorizontalSensitivityFPPApplyFunc(settingObj, key, value)
end

-- FPP 垂直
local function _VerticalSensitivityFPPGetter(settingObj, key)
    return settingObj[key].VerticalSensitivityFPP
end
local function _VerticalSensitivityFPPSetter(settingObj, key, value)
    settingObj[key].VerticalSensitivityFPP = value
end
local function _VerticalSensitivityFPPApplyFunc(settingObj, key, value)
end

-- TPP 水平
local function _HorizontalSensitivityTPPGetter(settingObj, key)
    return settingObj[key].HorizontalSensitivityTPP
end
local function _HorizontalSensitivityTPPSetter(settingObj, key, value)
    settingObj[key].HorizontalSensitivityTPP = value
end
local function _HorizontalSensitivityTPPApplyFunc(settingObj, key, value)
end

-- TPP 垂直
local function _VerticalSensitivityTPPGetter(settingObj, key)
    return settingObj[key].VerticalSensitivityTPP
end
local function _VerticalSensitivityTPPSetter(settingObj, key, value)
    settingObj[key].VerticalSensitivityTPP = value
end
local function _VerticalSensitivityTPPApplyFunc(settingObj, key, value)
end
-- END MODIFICATION

local function _DriverWeaponSensitivityFPPGetter(settingObj, key)
    return settingObj[key].SensitivityFPP
end

local function _DriverWeaponSensitivityFPPSetter(settingObj, key, value)
    settingObj[key].SensitivityFPP = value
end

local function _DriverWeaponSensitivityFPPApplyFunc(settingObj, key, value)

end

local function _DriverWeaponSensitivityTPPGetter(settingObj, key)
    return settingObj[key].SensitivityTPP
end

local function _DriverWeaponSensitivityTPPSetter(settingObj, key, value)
    settingObj[key].SensitivityTPP = value
end

local function _DriverWeaponSensitivityTPPApplyFunc(settingObj, key, value)

end

local function _HelicopterDriverWeaponSensitivityFPPGetter(settingObj, key)
    return settingObj[key].SensitivityFPP
end

local function _HelicopterDriverWeaponSensitivityFPPSetter(settingObj, key, value)
    settingObj[key].SensitivityFPP = value
end

local function _HelicopterDriverWeaponSensitivityFPPApplyFunc(settingObj, key, value)

end

local function _HelicopterDriverWeaponSensitivityTPPGetter(settingObj, key)
    return settingObj[key].SensitivityTPP
end

local function _HelicopterDriverWeaponSensitivityTPPSetter(settingObj, key, value)
    settingObj[key].SensitivityTPP = value
end

local function _HelicopterDriverWeaponSensitivityTPPApplyFunc(settingObj, key, value)
end 

local function _HelicopterDriverWeaponAxisSensitivityGetter(settingObj, key)
    return settingObj.HelicopterDriverWeaponAxisSensitivity[key]
end

local function _HelicopterDriverWeaponAxisSensitivitySetter(settingObj, key, value)
    settingObj.HelicopterDriverWeaponAxisSensitivity[key] = value
end

local function _HelicopterDriverWeaponAxisSensitivityGamepadGetter(settingObj, key)
    return settingObj.HelicopterDriverWeaponAxisSensitivityGamepad[key]
end

local function _HelicopterDriverWeaponAxisSensitivityGamepadSetter(settingObj, key, value)
    settingObj.HelicopterDriverWeaponAxisSensitivityGamepad[key] = value
end

local function _PassengerWeaponSensitivityTPPGetter(settingObj, key)
    return settingObj[key].SensitivityTPP
end

local function _PassengerWeaponSensitivityTPPSetter(settingObj, key, value)
    settingObj[key].SensitivityTPP = value
end

local function _PassengerWeaponSensitivityTPPApplyFunc(settingObj, key, value)
end

local function _PassengerWeaponSensitivityFPPGetter(settingObj, key)
    return settingObj[key].SensitivityFPP
end

local function _PassengerWeaponSensitivityFPPSetter(settingObj, key, value)
    settingObj[key].SensitivityFPP = value
end

local function _PassengerWeaponSensitivityFPPApplyFunc(settingObj, key, value)
end

local function _SensitivityMode_DriverGetter(settingObj, key)
    return settingObj[key].SensitivityMode
end

local function _SensitivityMode_DriverSetter(settingObj, key, value)
    settingObj[key].SensitivityMode = value
end

local function _SensitivityMode_DriverApplyFunc(settingObj, key, value)
end

local function _SensitivityMode_HelicopterDriverGetter(settingObj, key)
    return settingObj[key].SensitivityMode
end

local function _SensitivityMode_HelicopterDriverSetter(settingObj, key, value)
    settingObj[key].SensitivityMode = value
end

local function _SensitivityMode_HelicopterDriverApplyFunc(settingObj, key, value)
end

local function _SensitivityMode_PassengerGetter(settingObj, key)
    return settingObj[key].SensitivityMode
end

local function _SensitivityMode_PassengerSetter(settingObj, key, value)
    settingObj[key].SensitivityMode = value
end

local function _SensitivityMode_PassengerApplyFunc(settingObj, key, value)
end

local function _MDV_DriverGetter(settingObj, key)
    return settingObj[key].MDV
end

local function _MDV_DriverSetter(settingObj, key, value)
    settingObj[key].MDV = value
end

local function _MDV_DriverApplyFunc(settingObj, key, value)
end

local function _MDV_HelicopterDriverGetter(settingObj, key)
    return settingObj[key].MDV
end

local function _MDV_HelicopterDriverSetter(settingObj, key, value)
    settingObj[key].MDV = value
end

local function _MDV_HelicopterDriverApplyFunc(settingObj, key, value)
end

local function _MDV_PassengerGetter(settingObj, key)
    return settingObj[key].MDV
end

local function _MDV_PassengerSetter(settingObj, key, value)
    settingObj[key].MDV = value
end

local function _MDV_PassengerApplyFunc(settingObj, key, value)
end

local function _BaseADSSensitivity_DriverGetter(settingObj, key)
    return settingObj[key].BaseADSSensitivity
end

local function _BaseADSSensitivity_DriverSetter(settingObj, key, value)
    settingObj[key].BaseADSSensitivity = value
end

local function _BaseADSSensitivity_DriverApplyFunc(settingObj, key, value)
end

local function _BaseADSSensitivity_HelicopterDriverGetter(settingObj, key)
    return settingObj[key].BaseADSSensitivity
end

local function _BaseADSSensitivity_HelicopterDriverSetter(settingObj, key, value)
    settingObj[key].BaseADSSensitivity = value
end

local function _BaseADSSensitivity_HelicopterDriverApplyFunc(settingObj, key, value)
end

local function _BaseADSSensitivity_PassengerGetter(settingObj, key)
    return settingObj[key].BaseADSSensitivity
end

local function _BaseADSSensitivity_PassengerSetter(settingObj, key, value)
    settingObj[key].BaseADSSensitivity = value
end

local function _BaseADSSensitivity_PassengerApplyFunc(settingObj, key, value)
end

local function _ActionLogicGamepadGetter(settingObj, key)
    return settingObj[key]
end
local function _ActionLogicGamepadSetter(settingObj, key, value)
    settingObj[key] = value
end
local function _ActionLogicGamepadSetterApplyFunc(settingObj, key, value)
    DFMGameplayInputManager:OnSetGamePadKeyActionLogicByName(key, value)
end

local function _AutoSprintTypeGamepadGetter(settingObj, key)
    return settingObj[key]
end
local function _AutoSprintTypeGamepadSetter(settingObj, key, value)
    settingObj[key] = value
end
local function _AutoSprintTypeGamepadSetterApplyFunc(settingObj, key, value)
    DFMGameplayInputManager:OnSetGamePadAutoSprintType(value)
end

local function _SideAimTypeGamepadGetter(settingObj, key)
    return settingObj[key]
end
local function _SideAimTypeGamepadSetter(settingObj, key, value)
    settingObj[key] = value
end
local function _SideAimTypeGamepadSetterApplyFunc(settingObj, key, value)
    DFMGameplayInputManager:OnSetGamePadSideAimingControlMode(value)
end

local function _SuperSprintsSwitchGamepadGetter(settingObj, key)
    return settingObj[key]
end
local function _SuperSprintsSwitchGamepadSetter(settingObj, key, value)
    settingObj[key] = value
end
local function _SuperSprintsSwitchGamepadSetterApplyFunc(settingObj, key, value)
    DFMGameplayInputManager:OnSetGamePadTriggerSuperSprintType(value)
end

local function _SlideDiveTriggerGamepadGetter(settingObj, key)
    return settingObj[key]
end
local function _SlideDiveTriggerGamepadSetter(settingObj, key, value)
    settingObj[key] = value
end
local function _SlideDiveTriggerGamepadSetterApplyFunc(settingObj, key, value)
    DFMGameplayInputManager:OnSetGamePadSlideDiveTriggerType(value)
end

----------固定翼
local function _JetAxisSensitivityGetter(settingObj, key)
    return settingObj.JetAxisSensitivity[key]
end

local function _JetAxisSensitivitySetter(settingObj, key, value)
    settingObj.JetAxisSensitivity[key] = value
end

local function _JetAxisSensitivityGamepadGetter(settingObj, key)
    return settingObj.JetAxisSensitivityGamepad[key]
end

local function _JetAxisSensitivityGamepadSetter(settingObj, key, value)
    settingObj.JetAxisSensitivityGamepad[key] = value
end

local function _SensitivityMode_JetGetter(settingObj, key)
    return settingObj[key].SensitivityMode
end

local function _SensitivityMode_JetSetter(settingObj, key, value)
    settingObj[key].SensitivityMode = value
end

local function _MDV_JetGetter(settingObj, key)
    return settingObj[key].MDV
end

local function _MDV_JetSetter(settingObj, key, value)
    settingObj[key].MDV = value
end

local function _BaseADSSensitivity_JetGetter(settingObj, key)
    return settingObj[key].BaseADSSensitivity
end

local function _BaseADSSensitivity_JetSetter(settingObj, key, value)
    settingObj[key].BaseADSSensitivity = value
end

------------------

local function _AnalogCursorDeadZoneApplyFunc(settingObj, key, value)
    value = value / 100
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "GPUINav.AnalogCursorDeadZone " .. tostring(value)
    )
end

local function _AnalogCursorSpeedApplyFunc(settingObj, key, value)
    value = value / 10
    UKismetSystemLibrary.ExecuteConsoleCommand(
        GetGameInstance(),
        "GPUINav.AnalogCursorViewportSpeedFactor " .. tostring(value)
    )
end

function InputSettingLogicHD.Register()
    SettingRegLogicHD.BeginRegSection(_sensitivitySetting)
    SettingRegLogicHD.RegPassThroughMapping("Sensitivity", "BaseSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("ADS_Sensitivty_Ratio", "BaseADSSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("MDV", "MDV")

    SettingRegLogicHD.RegPassThroughMapping("MDVSwitchingMode", "MDVSwitchingMode")
    SettingRegLogicHD.RegPassThroughMapping("SideAimingControlMode", "SideAimingControlMode")
    SettingRegLogicHD.RegSetMapping(
        "SideAimingControlMode",
        _sensitivitySetting,
        "SideAimingControlMode",
        _SideAimingControlModeSetter
    )

    -- SettingRegLogicHD.RegPassThroughMapping("ADS_Sensitivty_Custom", "bUseCustomADS")
    -- SettingRegLogicHD.RegPassThroughMapping("UISensitivity", "UISensitivity")
    SettingRegLogicHD.RegPassThroughMapping("VerticalSensitivity", "VerticalSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("HorizontalSensitivity", "HorizontalSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("AimHorizontalSensitivity", "AimHorizontalSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("AimVerticalSensitivity", "AimVerticalSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("SensitivityMode", "SensitivityMode")
    SettingRegLogicHD.RegPassThroughMapping("bAllVerticalMMReversed", "bAllVerticalMMReversed")
    SettingRegLogicHD.RegPassThroughMapping("bInfantryVerticalMMReversed", "bInfantryVerticalMMReversed")
    SettingRegLogicHD.RegPassThroughMapping("bVehicleVerticalMMReversed", "bVehicleVerticalMMReversed") 
    SettingRegLogicHD.RegPassThroughMapping("bHelicopterVerticalMMReversed", "bHelicopterVerticalMMReversed")
    SettingRegLogicHD.RegPassThroughMapping("bGunnerVerticalMMReversed", "bGunnerVerticalMMReversed")

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
    SettingRegLogicHD.RegPassThroughMapping("GamepadSensitivity", "BaseGamepadSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("ADS_Sensitivity_Ratio_Gamepad", "BaseGamepadADSSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("GamepadMDV", "GamepadMDV")
    SettingRegLogicHD.RegPassThroughMapping("GamepadMDVSwitchingMode", "GamepadMDVSwitchingMode")
    -- SettingRegLogicHD.RegPassThroughMapping("ADS_Sensitivty_Custom", "bUseGamepadCustomADS")
    -- SettingRegLogicHD.RegPassThroughMapping("UISensitivity", "UISensitivity")
    SettingRegLogicHD.RegPassThroughMapping("VerticalGamepadSensitivity", "VerticalGamepadSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("HorizontalGamepadSensitivity", "HorizontalGamepadSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("AimHorizontalGamepadSensitivity", "AimHorizontalGamepadSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("AimVerticalGamepadSensitivity", "AimVerticalGamepadSensitivity")
    SettingRegLogicHD.RegPassThroughMapping("GamepadSensitivityMode", "GamepadSensitivityMode")
    SettingRegLogicHD.RegPassThroughMapping("bAllVerticalMMReversedGamepad", "bAllVerticalMMReversedGamepad")
    SettingRegLogicHD.RegPassThroughMapping("bInfantryVerticalMMReversedGamepad", "bInfantryVerticalMMReversedGamepad")
    SettingRegLogicHD.RegPassThroughMapping("bVehicleVerticalMMReversedGamepad", "bVehicleVerticalMMReversedGamepad")
    SettingRegLogicHD.RegPassThroughMapping("bHelicopterVerticalMMReversedGamepad", "bHelicopterVerticalMMReversedGamepad")
    SettingRegLogicHD.RegPassThroughMapping("bGunnerVerticalMMReversedGamepad", "bGunnerVerticalMMReversedGamepad")
	
    SettingRegLogicHD.RegPassThroughMapping("GamepadSensitivityPresetMode", "GamepadSensitivityPresetMode")
    SettingRegLogicHD.RegPassThroughMapping("GamepadSensitivityCurveType", "GamepadSensitivityCurveType")

    SettingRegLogicHD.RegPassThroughMapping("bEnableScopeCustom", "bEnableScopeCustom")
    -- END MODIFICATION

    _RegSceneFOVCalcOpenCameraFOVBatch("bSceneFOVCalcOpenCameraFOV", "bSceneFOVCalcOpenCameraFOV")

    local MMReverseIDs = {
        "bInfantryVerticalMMReversed",
        "bVehicleVerticalMMReversed",
        "bHelicopterVerticalMMReversed",
        "bGunnerVerticalMMReversed"
    }

    for _, id in ipairs(MMReverseIDs) do
        SettingRegLogicHD.RegPrerequisite(
            id,
            "bAllVerticalMMReversed",
            function(value)
                return not value
            end
        )
    end

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS
    local GamepadMMReverseIDs = {
        "bInfantryVerticalMMReversedGamepad",
        "bVehicleVerticalMMReversedGamepad",
        "bHelicopterVerticalMMReversedGamepad",
        "bGunnerVerticalMMReversedGamepad"
    }

    for _, id in ipairs(GamepadMMReverseIDs) do
        SettingRegLogicHD.RegPrerequisite(
            id,
            "bAllVerticalMMReversedGamepad",
            function(value)
                return not value
            end
        )
    end

    -- 外圈灵敏度加成
    -- Base
    SettingRegLogicHD.RegMapping(
        "ExtraSensitivityDataHDHorizontalValue",
        _sensitivitySetting,
        "ExtraSensitivityDataHD",
        _ExtraSensitivityDataHDHorizontalValueGetter,
        _ExtraSensitivityDataHDHorizontalValueSetter,
        _ExtraSensitivityDataHDHorizontalValueApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ExtraSensitivityDataHDVerticalValue",
        _sensitivitySetting,
        "ExtraSensitivityDataHD",
        _ExtraSensitivityDataHDVerticalValueGetter,
        _ExtraSensitivityDataHDVerticalValueSetter,
        _ExtraSensitivityDataHDVerticalValueApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ExtraSensitivityDataHDStartTime",
        _sensitivitySetting,
        "ExtraSensitivityDataHD",
        _ExtraSensitivityDataHDStartupTimeGetter,
        _ExtraSensitivityDataHDStartupTimeSetter,
        _ExtraSensitivityDataHDStartupTimeApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ExtraSensitivityDataHDDelayTime",
        _sensitivitySetting,
        "ExtraSensitivityDataHD",
        _ExtraSensitivityDataHDDelayTimeGetter,
        _ExtraSensitivityDataHDDelayTimeSetter,
        _ExtraSensitivityDataHDDelayTimeApplyFunc
    )

    -- ADS
    SettingRegLogicHD.RegMapping(
        "ADSExtraSensitivityDataHDHorizontalValue",
        _sensitivitySetting,
        "ADSExtraSensitivityDataHD",
        _ExtraSensitivityDataHDHorizontalValueGetter,
        _ExtraSensitivityDataHDHorizontalValueSetter,
        _ExtraSensitivityDataHDHorizontalValueApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ADSExtraSensitivityDataHDVerticalValue",
        _sensitivitySetting,
        "ADSExtraSensitivityDataHD",
        _ExtraSensitivityDataHDVerticalValueGetter,
        _ExtraSensitivityDataHDVerticalValueSetter,
        _ExtraSensitivityDataHDVerticalValueApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ADSExtraSensitivityDataHDStartTime",
        _sensitivitySetting,
        "ADSExtraSensitivityDataHD",
        _ExtraSensitivityDataHDStartupTimeGetter,
        _ExtraSensitivityDataHDStartupTimeSetter,
        _ExtraSensitivityDataHDStartupTimeApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ADSExtraSensitivityDataHDDelayTime",
        _sensitivitySetting,
        "ADSExtraSensitivityDataHD",
        _ExtraSensitivityDataHDDelayTimeGetter,
        _ExtraSensitivityDataHDDelayTimeSetter,
        _ExtraSensitivityDataHDDelayTimeApplyFunc
    )

    -- 输入限制 右摇杆
    -- 死区
    SettingRegLogicHD.RegMapping(
        "GamepadRightThumbDeadZone",
        _sensitivitySetting,
        "GamepadRightThumbRestrictDataHD",
        _GamepadThumbRestrictDataHDDeadZoneGetter,
        _GamepadThumbRestrictDataHDDeadZoneSetter,
        _GamepadThumbRestrictDataHDDeadZoneApplyFunc
    )
    -- 最大输入
    SettingRegLogicHD.RegMapping(
        "GamepadRightThumbMaximumThreshold",
        _sensitivitySetting,
        "GamepadRightThumbRestrictDataHD",
        _GamepadThumbRestrictDataHDMaximumThresholdGetter,
        _GamepadThumbRestrictDataHDMaximumThresholdSetter,
        _GamepadThumbRestrictDataHDMaximumThresholdApplyFunc
    )
    -- 轴区死角
    SettingRegLogicHD.RegMapping(
        "GamepadRightThumbAxisZoneAssist",
        _sensitivitySetting,
        "GamepadRightThumbRestrictDataHD",
        _GamepadThumbRestrictDataHDAxisZoneAssistGetter,
        _GamepadThumbRestrictDataHDAxisZoneAssistSetter,
        _GamepadThumbRestrictDataHDAxisZoneAssistApplyFunc
    )

    -- 输入限制 左摇杆
    -- 死区
    SettingRegLogicHD.RegMapping(
        "GamepadLeftThumbDeadZone",
        _sensitivitySetting,
        "GamepadLeftThumbRestrictDataHD",
        _GamepadThumbRestrictDataHDDeadZoneGetter,
        _GamepadThumbRestrictDataHDDeadZoneSetter,
        _GamepadThumbRestrictDataHDDeadZoneApplyFunc
    )
    -- 最大输入
    SettingRegLogicHD.RegMapping(
        "GamepadLeftThumbMaximumThreshold",
        _sensitivitySetting,
        "GamepadLeftThumbRestrictDataHD",
        _GamepadThumbRestrictDataHDMaximumThresholdGetter,
        _GamepadThumbRestrictDataHDMaximumThresholdSetter,
        _GamepadThumbRestrictDataHDMaximumThresholdApplyFunc
    )
    -- 轴区死角
    SettingRegLogicHD.RegMapping(
        "GamepadLeftThumbAxisZoneAssist",
        _sensitivitySetting,
        "GamepadLeftThumbRestrictDataHD",
        _GamepadThumbRestrictDataHDAxisZoneAssistGetter,
        _GamepadThumbRestrictDataHDAxisZoneAssistSetter,
        _GamepadThumbRestrictDataHDAxisZoneAssistApplyFunc
    )
    
    -- _RegADSSensitivityBatch("ADS_Sensitivty_Ratio_1", 0)
    -- _RegADSSensitivityBatch("ADS_Sensitivty_Ratio_2", 1)
    -- _RegADSSensitivityBatch("ADS_Sensitivty_Ratio_3", 2)
    -- _RegADSSensitivityBatch("ADS_Sensitivty_Ratio_4", 3)
    -- _RegADSSensitivityBatch("ADS_Sensitivty_Ratio_5", 4)
    -- _RegADSSensitivityBatch("ADS_Sensitivty_Ratio_6", 5)

    SettingRegLogicHD.RegPassThroughMappingWithFunc("AnalogCursorDeadZone", "AnalogCursorDeadZone", _AnalogCursorDeadZoneApplyFunc)
    SettingRegLogicHD.RegPassThroughMappingWithFunc("AnalogCursorSpeed", "AnalogCursorSpeed", _AnalogCursorSpeedApplyFunc)


    SettingRegLogicHD.BeginRegSection(_gameSetting)
    SettingRegLogicHD.RegPassThroughMapping("bEnableGamepadRumble", "bEnableGamepadRumble")
	SettingRegLogicHD.RegPassThroughMapping("bEnableLBRBtoRBRT", "bEnableLBRBtoRBRT")
    SettingRegLogicHD.RegPassThroughMapping("bEnableBreath", "bEnableBreath")
    SettingRegLogicHD.RegPassThroughMapping("bAimAssistorGamepad", "bAimAssistorGamepad")
    SettingRegLogicHD.RegPassThroughMapping("bScopeOpenAutoPeek_Gamepad", "bScopeOpenAutoPeek_Gamepad")
    SettingRegLogicHD.RegPassThroughMapping("bCanMouseTurnMelee_GamePad", "bCanMouseTurnMelee_GamePad")
    SettingRegLogicHD.RegPassThroughMapping("bSwitchWeaponsIncludingBattlefieldProps_GamePad", "bSwitchWeaponsIncludingBattlefieldProps_GamePad")
    SettingRegLogicHD.RegPassThroughMapping("PeekOpenMode_Gamepad", "PeekOpenMode_Gamepad")
    SettingRegLogicHD.RegPassThroughMapping("bEnableAdaptiveTrigger", "bEnableAdaptiveTrigger")
    SettingRegLogicHD.RegPassThroughMapping("bConfirmSettingDiff", "bConfirmSettingDiff")
    SettingRegLogicHD.EndRegSection()
    -- END MODIFICATION

    SettingRegLogicHD.BeginRegSection(_vehicleSetting)
    SettingRegLogicHD.RegMapping(
        "DriverWeaponSensitivityFPP",
        _vehicleSetting,
        "DriverWeaponSensitivity",
        _DriverWeaponSensitivityFPPGetter,
        _DriverWeaponSensitivityFPPSetter,
        _DriverWeaponSensitivityFPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "DriverWeaponSensitivityTPP",
        _vehicleSetting,
        "DriverWeaponSensitivity",
        _DriverWeaponSensitivityTPPGetter,
        _DriverWeaponSensitivityTPPSetter,
        _DriverWeaponSensitivityTPPApplyFunc
    )

    -- SettingRegLogicHD.RegMapping(
    --     "HelicopterDriverWeaponSensitivityFPP",
    --     _vehicleSetting,
    --     "HelicopterDriverWeaponSensitivity",
    --     _HelicopterDriverWeaponSensitivityFPPGetter,
    --     _HelicopterDriverWeaponSensitivityFPPSetter,
    --     _HelicopterDriverWeaponSensitivityFPPApplyFunc
    -- )

    -- SettingRegLogicHD.RegMapping(
    --     "HelicopterDriverWeaponSensitivityTPP",
    --     _vehicleSetting,
    --     "HelicopterDriverWeaponSensitivity",
    --     _HelicopterDriverWeaponSensitivityTPPGetter,
    --     _HelicopterDriverWeaponSensitivityTPPSetter,
    --     _HelicopterDriverWeaponSensitivityTPPApplyFunc
    -- )

    SettingRegLogicHD.RegMapping(
        "PassengerWeaponSensitivityTPP",
        _vehicleSetting,
        "PassengerWeaponSensitivity",
        _PassengerWeaponSensitivityTPPGetter,
        _PassengerWeaponSensitivityTPPSetter,
        _PassengerWeaponSensitivityTPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "PassengerWeaponSensitivityFPP",
        _vehicleSetting,
        "PassengerWeaponSensitivity",
        _PassengerWeaponSensitivityFPPGetter,
        _PassengerWeaponSensitivityFPPSetter,
        _PassengerWeaponSensitivityFPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_Driver",
        _vehicleSetting,
        "DriverWeaponSensitivity",
        _SensitivityMode_DriverGetter,
        _SensitivityMode_DriverSetter,
        _SensitivityMode_DriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_HelicopterDriver",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivity",
        _SensitivityMode_HelicopterDriverGetter,
        _SensitivityMode_HelicopterDriverSetter,
        _SensitivityMode_HelicopterDriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_Passenger",
        _vehicleSetting,
        "PassengerWeaponSensitivity",
        _SensitivityMode_PassengerGetter,
        _SensitivityMode_PassengerSetter,
        _SensitivityMode_PassengerApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "MDV_Driver",
        _vehicleSetting,
        "DriverWeaponSensitivity",
        _MDV_DriverGetter,
        _MDV_DriverSetter,
        _MDV_DriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "MDV_HelicopterDriver",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivity",
        _MDV_HelicopterDriverGetter,
        _MDV_HelicopterDriverSetter,
        _MDV_HelicopterDriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "MDV_Passenger",
        _vehicleSetting,
        "PassengerWeaponSensitivity",
        _MDV_PassengerGetter,
        _MDV_PassengerSetter,
        _MDV_PassengerApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_Driver",
        _vehicleSetting,
        "DriverWeaponSensitivity",
        _BaseADSSensitivity_DriverGetter,
        _BaseADSSensitivity_DriverSetter,
        _BaseADSSensitivity_DriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_HelicopterDriver",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivity",
        _BaseADSSensitivity_HelicopterDriverGetter,
        _BaseADSSensitivity_HelicopterDriverSetter,
        _BaseADSSensitivity_HelicopterDriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_Passenger",
        _vehicleSetting,
        "PassengerWeaponSensitivity",
        _BaseADSSensitivity_PassengerGetter,
        _BaseADSSensitivity_PassengerSetter,
        _BaseADSSensitivity_PassengerApplyFunc
    )

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
    -- Begin 地面载具 
    -- FPP垂直水平灵敏度
    SettingRegLogicHD.RegMapping(
        "DriverWeaponHorizontalSensitivityFPP_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _HorizontalSensitivityFPPGetter,
        _HorizontalSensitivityFPPSetter,
        _HorizontalSensitivityFPPApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "DriverWeaponVerticalSensitivityFPP_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _VerticalSensitivityFPPGetter,
        _VerticalSensitivityFPPSetter,
        _VerticalSensitivityFPPApplyFunc
    )
    -- TPP垂直水平灵敏度
    SettingRegLogicHD.RegMapping(
        "DriverWeaponHorizontalSensitivityTPP_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _HorizontalSensitivityTPPGetter,
        _HorizontalSensitivityTPPSetter,
        _HorizontalSensitivityTPPApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "DriverWeaponVerticalSensitivityTPP_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _VerticalSensitivityTPPGetter,
        _VerticalSensitivityTPPSetter,
        _VerticalSensitivityTPPApplyFunc
    )
    -- ENd 地面载具 

    -- Begin 空中载具
    -- FPP垂直水平灵敏度
    -- SettingRegLogicHD.RegMapping(
    --     "HelicopterDriverWeaponHorizontalSensitivityFPP_Gamepad",
    --     _vehicleSetting,
    --     "HelicopterDriverWeaponSensitivityGamepad",
    --     _HorizontalSensitivityFPPGetter,
    --     _HorizontalSensitivityFPPSetter,
    --     _HorizontalSensitivityFPPApplyFunc
    -- )
    -- SettingRegLogicHD.RegMapping(
    --     "HelicopterDriverWeaponVerticalSensitivityFPP_Gamepad",
    --     _vehicleSetting,
    --     "HelicopterDriverWeaponSensitivityGamepad",
    --     _VerticalSensitivityFPPGetter,
    --     _VerticalSensitivityFPPSetter,
    --     _VerticalSensitivityFPPApplyFunc
    -- )
    -- TPP垂直水平灵敏度
    -- SettingRegLogicHD.RegMapping(
    --     "HelicopterDriverWeaponHorizontalSensitivityTPP_Gamepad",
    --     _vehicleSetting,
    --     "HelicopterDriverWeaponSensitivityGamepad",
    --     _HorizontalSensitivityTPPGetter,
    --     _HorizontalSensitivityTPPSetter,
    --     _HorizontalSensitivityTPPApplyFunc
    -- )
    -- SettingRegLogicHD.RegMapping(
    --     "HelicopterDriverWeaponVerticalSensitivityTPP_Gamepad",
    --     _vehicleSetting,
    --     "HelicopterDriverWeaponSensitivityGamepad",
    --     _VerticalSensitivityTPPGetter,
    --     _VerticalSensitivityTPPSetter,
    --     _VerticalSensitivityTPPApplyFunc
    -- )
    -- End 空中载具

    -- Begin 炮手
    -- FPP垂直水平灵敏度
    SettingRegLogicHD.RegMapping(
        "PassengerWeaponHorizontalSensitivityFPP_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _HorizontalSensitivityFPPGetter,
        _HorizontalSensitivityFPPSetter,
        _HorizontalSensitivityFPPApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "PassengerWeaponVerticalSensitivityFPP_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _VerticalSensitivityFPPGetter,
        _VerticalSensitivityFPPSetter,
        _VerticalSensitivityFPPApplyFunc
    )

    -- TPP垂直水平灵敏度
    SettingRegLogicHD.RegMapping(
        "PassengerWeaponHorizontalSensitivityTPP_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _HorizontalSensitivityTPPGetter,
        _HorizontalSensitivityTPPSetter,
        _HorizontalSensitivityTPPApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "PassengerWeaponVerticalSensitivityTPP_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _VerticalSensitivityTPPGetter,
        _VerticalSensitivityTPPSetter,
        _VerticalSensitivityTPPApplyFunc
    )
    -- End 炮手


    SettingRegLogicHD.RegMapping(
        "DriverWeaponSensitivityFPP_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _DriverWeaponSensitivityFPPGetter,
        _DriverWeaponSensitivityFPPSetter,
        _DriverWeaponSensitivityFPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "DriverWeaponSensitivityTPP_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _DriverWeaponSensitivityTPPGetter,
        _DriverWeaponSensitivityTPPSetter,
        _DriverWeaponSensitivityTPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "HelicopterDriverWeaponSensitivityFPP_Gamepad",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivityGamepad",
        _HelicopterDriverWeaponSensitivityFPPGetter,
        _HelicopterDriverWeaponSensitivityFPPSetter,
        _HelicopterDriverWeaponSensitivityFPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "HelicopterDriverWeaponSensitivityTPP_Gamepad",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivityGamepad",
        _HelicopterDriverWeaponSensitivityTPPGetter,
        _HelicopterDriverWeaponSensitivityTPPSetter,
        _HelicopterDriverWeaponSensitivityTPPApplyFunc
    )

    SettingRegLogicHD.RegMapping("HelicopterSensitivityTPPYaw", _vehicleSetting, "TPPYaw", _HelicopterDriverWeaponAxisSensitivityGetter, _HelicopterDriverWeaponAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityTPPRoll", _vehicleSetting, "TPPRoll", _HelicopterDriverWeaponAxisSensitivityGetter, _HelicopterDriverWeaponAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityTPPPitch", _vehicleSetting, "TPPPitch", _HelicopterDriverWeaponAxisSensitivityGetter, _HelicopterDriverWeaponAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityFPPYaw", _vehicleSetting, "FPPYaw", _HelicopterDriverWeaponAxisSensitivityGetter, _HelicopterDriverWeaponAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityFPPRoll", _vehicleSetting, "FPPRoll", _HelicopterDriverWeaponAxisSensitivityGetter, _HelicopterDriverWeaponAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityFPPPitch", _vehicleSetting, "FPPPitch", _HelicopterDriverWeaponAxisSensitivityGetter, _HelicopterDriverWeaponAxisSensitivitySetter, nil)

    SettingRegLogicHD.RegMapping("HelicopterSensitivityTPPYawGamepad", _vehicleSetting, "TPPYaw", _HelicopterDriverWeaponAxisSensitivityGamepadGetter, _HelicopterDriverWeaponAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityTPPRollGamepad", _vehicleSetting, "TPPRoll", _HelicopterDriverWeaponAxisSensitivityGamepadGetter, _HelicopterDriverWeaponAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityTPPPitchGamepad", _vehicleSetting, "TPPPitch", _HelicopterDriverWeaponAxisSensitivityGamepadGetter, _HelicopterDriverWeaponAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityFPPYawGamepad", _vehicleSetting, "FPPYaw", _HelicopterDriverWeaponAxisSensitivityGamepadGetter, _HelicopterDriverWeaponAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityFPPRollGamepad", _vehicleSetting, "FPPRoll", _HelicopterDriverWeaponAxisSensitivityGamepadGetter, _HelicopterDriverWeaponAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("HelicopterSensitivityFPPPitchGamepad", _vehicleSetting, "FPPPitch", _HelicopterDriverWeaponAxisSensitivityGamepadGetter, _HelicopterDriverWeaponAxisSensitivityGamepadSetter, nil)

    SettingRegLogicHD.RegMapping(
        "PassengerWeaponSensitivityTPP_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _PassengerWeaponSensitivityTPPGetter,
        _PassengerWeaponSensitivityTPPSetter,
        _PassengerWeaponSensitivityTPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "PassengerWeaponSensitivityFPP_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _PassengerWeaponSensitivityFPPGetter,
        _PassengerWeaponSensitivityFPPSetter,
        _PassengerWeaponSensitivityFPPApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_Driver_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _SensitivityMode_DriverGetter,
        _SensitivityMode_DriverSetter,
        _SensitivityMode_DriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_HelicopterDriver_Gamepad",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivityGamepad",
        _SensitivityMode_HelicopterDriverGetter,
        _SensitivityMode_HelicopterDriverSetter,
        _SensitivityMode_HelicopterDriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_Passenger_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _SensitivityMode_PassengerGetter,
        _SensitivityMode_PassengerSetter,
        _SensitivityMode_PassengerApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "MDV_Driver_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _MDV_DriverGetter,
        _MDV_DriverSetter,
        _MDV_DriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "MDV_HelicopterDriver_Gamepad",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivityGamepad",
        _MDV_HelicopterDriverGetter,
        _MDV_HelicopterDriverSetter,
        _MDV_HelicopterDriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "MDV_Passenger_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _MDV_PassengerGetter,
        _MDV_PassengerSetter,
        _MDV_PassengerApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_Driver_Gamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _BaseADSSensitivity_DriverGetter,
        _BaseADSSensitivity_DriverSetter,
        _BaseADSSensitivity_DriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_HelicopterDriver_Gamepad",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivityGamepad",
        _BaseADSSensitivity_HelicopterDriverGetter,
        _BaseADSSensitivity_HelicopterDriverSetter,
        _BaseADSSensitivity_HelicopterDriverApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_Passenger_Gamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _BaseADSSensitivity_PassengerGetter,
        _BaseADSSensitivity_PassengerSetter,
        _BaseADSSensitivity_PassengerApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "bEnableHelicopterDriverADSCustomGamepad",
        _vehicleSetting,
        "HelicopterDriverWeaponSensitivityGamepad",
        _ADSCustomGetter,
        _ADSCustomSetter,
        _ADSCustomSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "bEnableDriverADSCustomGamepad",
        _vehicleSetting,
        "DriverWeaponSensitivityGamepad",
        _ADSCustomGetter,
        _ADSCustomSetter,
        _ADSCustomSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "bEnablePassengerADSCustomGamepad",
        _vehicleSetting,
        "PassengerWeaponSensitivityGamepad",
        _ADSCustomGetter,
        _ADSCustomSetter,
        _ADSCustomSetterApplyFunc
    )
    -- END MODIFICATION

    ------------固定翼
    SettingRegLogicHD.RegMapping("JetSensitivityTPPYaw", _vehicleSetting, "TPPYaw", _JetAxisSensitivityGetter, _JetAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityTPPRoll", _vehicleSetting, "TPPRoll", _JetAxisSensitivityGetter, _JetAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityTPPPitch", _vehicleSetting, "TPPPitch", _JetAxisSensitivityGetter, _JetAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityFPPYaw", _vehicleSetting, "FPPYaw", _JetAxisSensitivityGetter, _JetAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityFPPRoll", _vehicleSetting, "FPPRoll", _JetAxisSensitivityGetter, _JetAxisSensitivitySetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityFPPPitch", _vehicleSetting, "FPPPitch", _JetAxisSensitivityGetter, _JetAxisSensitivitySetter, nil)

    SettingRegLogicHD.RegMapping("JetSensitivityTPPYawGamepad", _vehicleSetting, "TPPYaw", _JetAxisSensitivityGamepadGetter, _JetAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityTPPRollGamepad", _vehicleSetting, "TPPRoll", _JetAxisSensitivityGamepadGetter, _JetAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityTPPPitchGamepad", _vehicleSetting, "TPPPitch", _JetAxisSensitivityGamepadGetter, _JetAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityFPPYawGamepad", _vehicleSetting, "FPPYaw", _JetAxisSensitivityGamepadGetter, _JetAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityFPPRollGamepad", _vehicleSetting, "FPPRoll", _JetAxisSensitivityGamepadGetter, _JetAxisSensitivityGamepadSetter, nil)
    SettingRegLogicHD.RegMapping("JetSensitivityFPPPitchGamepad", _vehicleSetting, "FPPPitch", _JetAxisSensitivityGamepadGetter, _JetAxisSensitivityGamepadSetter, nil)

    SettingRegLogicHD.RegMapping(
        "SensitivityMode_Jet",
        _vehicleSetting,
        "JetSensitivity",
        _SensitivityMode_JetGetter,
        _SensitivityMode_JetSetter,
        nil
    )

    SettingRegLogicHD.RegMapping(
        "MDV_Jet",
        _vehicleSetting,
        "JetSensitivity",
        _MDV_JetGetter,
        _MDV_JetSetter,
        nil
    )

    SettingRegLogicHD.RegMapping(
        "BaseADSSensitivity_Jet",
        _vehicleSetting,
        "JetSensitivity",
        _BaseADSSensitivity_JetGetter,
        _BaseADSSensitivity_JetSetter,
        nil
    )


    SettingRegLogicHD.RegMapping(
        "bEnableJetADSCustomGamepad",
        _vehicleSetting,
        "JetSensitivityGamepad",
        _ADSCustomGetter,
        _ADSCustomSetter,
        _ADSCustomSetterApplyFunc
    )


    SettingRegLogicHD.RegMapping(
        "Sprint_ActionLogic_Gamepad",
        _gameSetting,
        "Sprint_ActionLogic_Gamepad",
        _ActionLogicGamepadGetter,
        _ActionLogicGamepadSetter,
        _ActionLogicGamepadSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "ToggleAiming_ActionLogic_Gamepad",
        _gameSetting,
        "ToggleAiming_ActionLogic_Gamepad",
        _ActionLogicGamepadGetter,
        _ActionLogicGamepadSetter,
        _ActionLogicGamepadSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "Rescue_ActionLogic_Gamepad",
        _gameSetting,
        "Rescue_ActionLogic_Gamepad",
        _ActionLogicGamepadGetter,
        _ActionLogicGamepadSetter,
        _ActionLogicGamepadSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "AbandonRescue_ActionLogic_Gamepad",
        _gameSetting,
        "AbandonRescue_ActionLogic_Gamepad",
        _ActionLogicGamepadGetter,
        _ActionLogicGamepadSetter,
        _ActionLogicGamepadSetterApplyFunc
    )

    SettingRegLogicHD.RegMapping(
        "AutoSprintType_Gamepad",
        _gameSetting,
        "AutoSprintType_Gamepad",
        _AutoSprintTypeGamepadGetter,
        _AutoSprintTypeGamepadSetter,
        _AutoSprintTypeGamepadSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "SideAimType_Gamepad",
        _gameSetting,
        "SideAimType_Gamepad",
        _SideAimTypeGamepadGetter,
        _SideAimTypeGamepadSetter,
        _SideAimTypeGamepadSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "SuperSprintsSwitch_Gamepad",
        _gameSetting,
        "SuperSprintsSwitch_Gamepad",
        _SuperSprintsSwitchGamepadGetter,
        _SuperSprintsSwitchGamepadSetter,
        _SuperSprintsSwitchGamepadSetterApplyFunc
    )
    SettingRegLogicHD.RegMapping(
        "SlideDiveTrigger_Gamepad",
        _gameSetting,
        "SlideDiveTrigger_Gamepad",
        _SlideDiveTriggerGamepadGetter,
        _SlideDiveTriggerGamepadSetter,
        _SlideDiveTriggerGamepadSetterApplyFunc
    )

    SettingRegLogicHD.EndRegSection()
    
end

function InputSettingLogicHD.GetSortedZoomData(index)
    local dataTable = _GetZoomDataTable(index)
    -- cpy
    local _sortedZoomData = {}
    for _, row in pairs(dataTable) do
        table.insert(_sortedZoomData, row)
    end

    table.sort(
        _sortedZoomData,
        function(a, b)
            return a.ZoomRate < b.ZoomRate
        end
    )

    return _sortedZoomData
end

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
function InputSettingLogicHD.GetSortedZoomData_Gamepad(index)
    local dataTable = _GetGamepadZoomDataTable(index)
    -- cpy
    _sortedGamepadZoomData = {}
    for _, row in pairs(dataTable) do
        table.insert(_sortedGamepadZoomData, row)
    end

    table.sort(
        _sortedGamepadZoomData,
        function(a, b)
            return a.ZoomRate < b.ZoomRate
        end
    )

    return _sortedGamepadZoomData
end
-- END MODIFICATION

local function _LoadDefaultsFromDataTable()
    local zoomData = InputSettingLogicHD.GetSortedZoomData(0)

    -- 为BHD也生成一份
    _sensitivitySetting.ZoomDataHD:Clear()
    _bhdSetting.ZoomDataHD:Clear()
    for _, tmpData in ipairs(zoomData) do
        local newConfig = FWeaponZoomDataHD()
        -- newConfig.Desc = tmpData.Desc
        newConfig.DefaultADSSensitivity = tmpData.DefaultADSSensitivity
        newConfig.ADSSensitivity = tmpData.DefaultADSSensitivity
        newConfig.ADSSensitivity2 = tmpData.DefaultADSSensitivity
        newConfig.ZoomRate = tmpData.ZoomRate
        _sensitivitySetting.ZoomDataHD:Add(newConfig)
        _bhdSetting.ZoomDataHD:Add(newConfig)
    end

    _sensitivitySetting.ZoomratedMDV:Clear()
    _bhdSetting.ZoomratedMDV:Clear()
    for _, tmpData in ipairs(zoomData) do
        local newConfig = FZoomratedDataHD()
        -- newConfig.Desc = tmpData.Desc
        newConfig.Value = tmpData.DefaultMDV
        newConfig.ZoomRate = tmpData.ZoomRate
        _sensitivitySetting.ZoomratedMDV:Add(newConfig)
        _bhdSetting.ZoomratedMDV:Add(newConfig)
    end

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
    local gamepadZoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(0)

    _sensitivitySetting.GamepadZoomDataHD:Clear()
    for _, tmpData in ipairs(gamepadZoomData) do
        local newConfig = FWeaponZoomDataHD()
        -- newConfig.Desc = tmpData.Desc
        newConfig.DefaultADSSensitivity = tmpData.DefaultADSSensitivity
        newConfig.ADSSensitivity = tmpData.DefaultADSSensitivity
        newConfig.ZoomRate = tmpData.ZoomRate
        _sensitivitySetting.GamepadZoomDataHD:Add(newConfig)
    end
    
    _sensitivitySetting.GamepadZoomratedMDV:Clear()
    for _, tmpData in ipairs(gamepadZoomData) do
        local newConfig = FZoomratedDataHD()
        -- newConfig.Desc = tmpData.Desc
        newConfig.Value = tmpData.DefaultMDV
        newConfig.ZoomRate = tmpData.ZoomRate
        _sensitivitySetting.GamepadZoomratedMDV:Add(newConfig)
    end
    -- END MODIFICATION
end

local function _LoadVehicleDefaultsFromDataTable(index, bIsGamepad)

    local sensitivityMode
    if index == 1 then --地面载具
        sensitivityMode = MouseSensitivityMode.EVehicleDriveWeapon
    elseif index == 2 then --空中载具
        sensitivityMode = MouseSensitivityMode.EHelicopterDriveWeapon
    elseif index == 3 then --炮手
        sensitivityMode = MouseSensitivityMode.EVehicleWeapon
    elseif index == 4 then --固定翼
        sensitivityMode = MouseSensitivityMode.EJet
    end

    if bIsGamepad then
        local vehicleGamepadZoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(index)

        ---@todo vts应该是写错了，手柄理论上要和鼠标分开
        _vehicleSetting:ClearZoomDataHD(sensitivityMode, true)
        for _, tmpData in ipairs(vehicleGamepadZoomData) do
            local newConfig = FWeaponZoomDataHD()
            -- newConfig.Desc = tmpData.Desc
            newConfig.DefaultADSSensitivity = tmpData.DefaultADSSensitivity
            newConfig.DefaultADSSensitivity2 = tmpData.DefaultADSSensitivity2
            newConfig.ADSSensitivity = tmpData.DefaultADSSensitivity
            newConfig.ADSSensitivity2 = tmpData.DefaultADSSensitivity2
            newConfig.ZoomRate = tmpData.ZoomRate
            _vehicleSetting:AddZoomDataHD(sensitivityMode, newConfig, true)
        end
        _vehicleSetting:ClearZoomratedMDV(sensitivityMode, true)
        for _, tmpData in ipairs(vehicleGamepadZoomData) do
            local newConfig = FZoomratedDataHD()
            -- newConfig.Desc = tmpData.Desc
            newConfig.Value = tmpData.DefaultMDV
            newConfig.ZoomRate = tmpData.ZoomRate
            _vehicleSetting:AddZoomratedMDV(sensitivityMode, newConfig, true)
        end
    else
        local vehicleZoomData = InputSettingLogicHD.GetSortedZoomData(index)

        _vehicleSetting:ClearZoomDataHD(sensitivityMode, false)
        for _, tmpData in ipairs(vehicleZoomData) do
            local newConfig = FWeaponZoomDataHD()
            -- newConfig.Desc = tmpData.Desc
            newConfig.DefaultADSSensitivity = tmpData.DefaultADSSensitivity
            newConfig.DefaultADSSensitivity2 = tmpData.DefaultADSSensitivity2
            newConfig.ADSSensitivity = tmpData.DefaultADSSensitivity
            newConfig.ADSSensitivity2 = tmpData.DefaultADSSensitivity2
            newConfig.ZoomRate = tmpData.ZoomRate
            _vehicleSetting:AddZoomDataHD(sensitivityMode, newConfig, false)
        end
        _vehicleSetting:ClearZoomratedMDV(sensitivityMode, false)
        for _, tmpData in ipairs(vehicleZoomData) do
            local newConfig = FZoomratedDataHD()
            -- newConfig.Desc = tmpData.Desc
            newConfig.Value = tmpData.DefaultMDV
            newConfig.ZoomRate = tmpData.ZoomRate
            _vehicleSetting:AddZoomratedMDV(sensitivityMode, newConfig, false)
        end
    end
end

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : Add gamepad sensitivity
function InputSettingLogicHD.GetPresetDatatable_Gamepad()
    if not _gamepadSensitivityPresetDatatable then
        _gamepadSensitivityPresetDatatable = Facade.TableManager:GetTable(_gamepadSensitivityPresetDatatablePath)
    end
    return _gamepadSensitivityPresetDatatable
end

function InputSettingLogicHD.GetBHDPresetDatatable_Gamepad()
    if not _gamepadBHDSensitivityPresetDatatable then
        _gamepadBHDSensitivityPresetDatatable = Facade.TableManager:GetTable(_gamepadBHDSensitivityPresetDatatablePath)
    end
    return _gamepadBHDSensitivityPresetDatatable
end
-- END MODIFICATION

function InputSettingLogicHD.CheckDataTable()
    if UClientSettingHelperHD.IsEditor() then
        local originTable = Facade.TableManager:GetDataTableManager():GetDataTable(_zoomDataTablePath)
        local guid = UDataTableSystemManagerLite.GetTableAssetGuid(originTable)

        if guid ~= _sensitivitySetting.ZoomDataTableGUID then
            _LoadDefaultsFromDataTable()

            _sensitivitySetting.ZoomDataTableGUID = guid
            _sensitivitySetting:SaveToDefault()
            _bhdSetting:SaveToDefault()
        end

        local function CheckVehicleData(dataTablePath, guidField, index, isGamepad)
            local origintable = Facade.TableManager:GetDataTableManager():GetDataTable(dataTablePath)
            local guid = UDataTableSystemManagerLite.GetTableAssetGuid(origintable)

            if guid ~= _vehicleSetting[guidField] then
                _LoadVehicleDefaultsFromDataTable(index, isGamepad)
                _vehicleSetting[guidField] = guid
                _vehicleSetting:SaveToDefault()
            end
        end

        CheckVehicleData(_vehicleZoomDataTablePath, "ZoomDataTableGUID", 1, false)
        CheckVehicleData(_vehicleZoomDataTablePath2, "ZoomDataTableGUID2", 2, false)
        CheckVehicleData(_vehicleZoomDataTablePath3, "ZoomDataTableGUID3", 3, false)
        CheckVehicleData(_vehicleZoomDataTablePath4, "ZoomDataTableGUID4", 4, false)
        CheckVehicleData(_vehicleZoomDataTablePath4, "GamepadZoomDataTableGUID", 1, true)
        CheckVehicleData(_vehicleZoomDataTablePath4, "GamepadZoomDataTableGUID2", 2, true)
        CheckVehicleData(_vehicleZoomDataTablePath4, "GamepadZoomDataTableGUID3", 3, true)
        CheckVehicleData(_vehicleZoomDataTablePath4, "GamepadZoomDataTableGUID4", 4, true)

    end
end

-- BEGIN MODIFICATION @LiDailong - VIRTUOS
function InputSettingLogicHD.GetSettingID(mode, bIsGamepad)
    -- 特殊处理
    if mode == MouseSensitivityMode.ENormalWeapon and bIsGamepad then
        return "GamepadSensitivityMode"
    end

    local suffix = bIsGamepad and "_Gamepad" or ""
    
    if mode == MouseSensitivityMode.ENormalWeapon then
        return "SensitivityMode" .. suffix
    elseif mode == MouseSensitivityMode.EVehicleDriveWeapon then
        return "SensitivityMode_Driver" .. suffix
    elseif mode == MouseSensitivityMode.EHelicopterDriveWeapon then
        return "SensitivityMode_HelicopterDriver" .. suffix
    else
        return "SensitivityMode_Passenger" .. suffix
    end
end

function InputSettingLogicHD.GetCurrentADSSubIndex(mode, bGamepad)
    -- Strict bool
    local bIsGamepad = not not bGamepad
    
    local settingID = InputSettingLogicHD.GetSettingID(mode, bIsGamepad)

    local senMode = CommonSettingLogicHD.GetDataByID(settingID)
    if senMode == ESensitivityModeHD.MDVXADS then
        return 0
    else
        return 1
    end
end
-- END MODIFICATION

-- BEGIN MODIFICATION @LiDailong - VIRTUOS
function InputSettingLogicHD.SetADSSensitivity(mode, index, subIndex, value, bUsingForGamepad)
    -- Strict bool
    local bIsGamepad = not not bUsingForGamepad

    if mode == MouseSensitivityMode.ENormalWeapon then
        _sensitivitySetting:SetADSSensitivityByIndex(index - 1, subIndex, value, bIsGamepad)
        SettingRegLogicHD.AddPendingSaveObj(_sensitivitySetting)
    else
        _vehicleSetting:SetADSSensitivityByIndex(mode, index - 1, subIndex, value, bIsGamepad)
        SettingRegLogicHD.AddPendingSaveObj(_vehicleSetting)
    end
end

function InputSettingLogicHD.GetADSSensitivity(mode, index, subIndex, bUsingForGamepad)
    -- Strict bool
    local bIsGamepad = not not bUsingForGamepad

    if mode == MouseSensitivityMode.ENormalWeapon then
        return _sensitivitySetting:GetCustomADSSensitivityByIndex(index - 1, subIndex, bIsGamepad)
    else
        return _vehicleSetting:GetCustomADSSensitivityByIndex(mode, index - 1, subIndex, bIsGamepad)
    end
end

function InputSettingLogicHD.ResetAllADSSensitivity(bUsingForGamepad)
    -- Strict bool
    local bIsGamepad = not not bUsingForGamepad

    local zoomData = nil
    if bIsGamepad then
        zoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(0)
    else
        zoomData = InputSettingLogicHD.GetSortedZoomData(0)
    end
    for i, tmpData in ipairs(zoomData) do
        _sensitivitySetting:SetADSSensitivityByIndex(i - 1, 0, tmpData.DefaultADSSensitivity, bIsGamepad)
        _sensitivitySetting:SetADSSensitivityByIndex(i - 1, 1, tmpData.DefaultADSSensitivity, bIsGamepad)
    end

    local function ResetVehicleADSSensitivity(mode, index)
        local VehicleZoomData = nil
        if bIsGamepad then
            VehicleZoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(index)
        else
            VehicleZoomData = InputSettingLogicHD.GetSortedZoomData(index)
        end
        for i, tmpData in ipairs(VehicleZoomData) do
            _vehicleSetting:SetADSSensitivityByIndex(mode, i - 1, 0, tmpData.DefaultADSSensitivity, bIsGamepad)
            _vehicleSetting:SetADSSensitivityByIndex(mode, i - 1, 1, tmpData.DefaultADSSensitivity2, bIsGamepad)
        end
    end

    ResetVehicleADSSensitivity(MouseSensitivityMode.EVehicleDriveWeapon, 1)
    ResetVehicleADSSensitivity(MouseSensitivityMode.EHelicopterDriveWeapon, 2)
    ResetVehicleADSSensitivity(MouseSensitivityMode.EVehicleWeapon, 3)
    ResetVehicleADSSensitivity(MouseSensitivityMode.EJet, 4)

    SettingRegLogicHD.AddPendingSaveObj(_sensitivitySetting)
    SettingRegLogicHD.AddPendingSaveObj(_vehicleSetting)
end

function InputSettingLogicHD.SetZoomratedMDV(mode, index, value, bUsingForGamepad)
    -- Strict bool
    local bIsGamepad = not not bUsingForGamepad

    if mode == MouseSensitivityMode.ENormalWeapon then
        _sensitivitySetting:SetZoomratedMDVByIndex(index - 1, value, bIsGamepad)
        SettingRegLogicHD.AddPendingSaveObj(_sensitivitySetting)
    else
        _vehicleSetting:SetZoomratedMDVByIndex(mode, index - 1, value, bIsGamepad)
        SettingRegLogicHD.AddPendingSaveObj(_vehicleSetting)
    end
end

function InputSettingLogicHD.GetZoomratedMDV(mode, index, bUsingForGamepad)
    -- Strict bool
    local bIsGamepad = not not bUsingForGamepad

    if mode == MouseSensitivityMode.ENormalWeapon then
        return _sensitivitySetting:GetZoomratedMDVByIndex(index - 1, bIsGamepad)
    else
        return _vehicleSetting:GetZoomratedMDVByIndex(mode, index - 1, bIsGamepad)
    end
end

function InputSettingLogicHD.ResetAllZoomratedMDV(bGamepad)
    -- Strict bool
    local bIsGamepad = not not bGamepad

    local zoomData = nil
    if bIsGamepad then
        zoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(0)
    else
        zoomData = InputSettingLogicHD.GetSortedZoomData(0)
    end
    for i, tmpData in ipairs(zoomData) do
        _sensitivitySetting:SetZoomratedMDVByIndex(i - 1, tmpData.DefaultMDV, bIsGamepad)
    end

    local function ResetVehicleZoomratedMDV(mode, index)
        local VehicleZoomData = nil
        if bIsGamepad then
            VehicleZoomData = InputSettingLogicHD.GetSortedZoomData_Gamepad(index)
        else
            VehicleZoomData = InputSettingLogicHD.GetSortedZoomData(index)
        end
        for i, tmpData in ipairs(VehicleZoomData) do
            _vehicleSetting:SetZoomratedMDVByIndex(mode, i - 1, tmpData.DefaultMDV, bIsGamepad)
        end
    end

    ResetVehicleZoomratedMDV(MouseSensitivityMode.EVehicleDriveWeapon, 1)
    ResetVehicleZoomratedMDV(MouseSensitivityMode.EHelicopterDriveWeapon, 2)
    ResetVehicleZoomratedMDV(MouseSensitivityMode.EVehicleWeapon, 3)
    ResetVehicleZoomratedMDV(MouseSensitivityMode.EJet, 4)

    SettingRegLogicHD.AddPendingSaveObj(_sensitivitySetting)
    SettingRegLogicHD.AddPendingSaveObj(_vehicleSetting)
end

function InputSettingLogicHD.VerifySettings()
    if _vehicleSetting.PassengerWeaponSensitivity.ZoomratedMDV:Num() == 0 then
        -- 老版本不兼容
        _vehicleSetting:ResetToDefault()
    end

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS
    local datatable = InputSettingLogicHD.GetPresetDatatable_Gamepad()
    local datatableBHD = InputSettingLogicHD.GetBHDPresetDatatable_Gamepad()
    local presetType = CommonSettingLogicHD.GetDataByID("GamepadSensitivityPresetMode")
    local presetTypeBHD = CommonSettingLogicHD.GetDataByID("GamepadSensitivityPresetMode_BHD")
    
    if datatable ~= nil and presetType ~= nil and datatableBHD ~= nil and presetTypeBHD ~= nil then
        local setMappings = SettingRegLogicHD.GetSetMapping()
        for id, _ in pairs(setMappings) do    
            -- 加载灵敏度预设值.
            local row = datatable[id]
            if row and presetType ~= EGamepadSensitivityPresetType.Custom then
                local value = _sensitivitySetting:GetPresetValueByType(row.PresetValues, presetType)
                CommonSettingLogicHD.SetDataByID(id, value)
            end

            -- BHD
            local BHDRow = datatableBHD[id]
            if BHDRow and presetTypeBHD ~= EGamepadSensitivityPresetType.Custom then
                local value = _bhdSetting:GetBHDPresetValueByType(BHDRow.PresetValues, presetTypeBHD)
                CommonSettingLogicHD.SetDataByID(id, value)
            end
        end
    end
    -- END MODIFICATION
end

return InputSettingLogicHD
