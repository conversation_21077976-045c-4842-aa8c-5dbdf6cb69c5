----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
----- LOG FUNCTION AUTO GENERATE END -----------
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"


---@class FriendAddBox : LuaUIBaseView
local FriendAddBox = ui("FriendAddBox")

function FriendAddBox:Ctor()
    loginfo("FriendAddBox:Ctor")
    self._playerInfo ={}
    self._wtPlayerNameText = self:Wnd("TextBlock_84", UITextBlock)
    --self._wtMeter = self:Wnd("TextBlock_138", UITextBlock)
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtPlayerIcon:SetFocusable(false)

    self._wtAddFriendBtn = self:Wnd("WBP_FriendAddButton_C_0", UIWidgetBase)
    self._wtBtnBox = self._wtAddFriendBtn:Wnd("wtAddFriendBtn", DFCommonButtonOnly)
    self._wtBtnBox:Event("OnClicked", self._OnAddFriend, self)
    self._wtAddEndImage = self._wtAddFriendBtn:Wnd("Image_79", UIImage)

    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)
    self._wtFriendBlock = self:Wnd("WBP_FriendBlock" ,UIWidgetBase)
    self._wtFriendBlock:Event("OnClicked", self._OnAddBlack, self)

    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)


    if IsHD() then
        self._wtBHDMark = self:Wnd("WBP_BHD_OwnMark",UIWidgetBase)
        self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self._wtBHDMark, "DFTipsAnchor", self.ShowTips,self.CloseTips)

        self.PlayerInfoActionName = "BHDPlayerInfo_Gamepad"
        self.AddFriendActionName = "BHDAddFriendQuick_Gamepad"
        self.AddBlackActionName = "BHDPlayerAddBlack_Gamepad"
        self._actionList = {self.PlayerInfoActionName, self.AddFriendActionName, self.AddBlackActionName}

        self._wtRootButton = self:Wnd("DFButton_435", UIWidgetBase)
        self._wtRootButton:Event("OnHovered", self._OnHovered, self)
        self._wtRootButton:Event("OnUnHovered",self._OnUnHovered,self)
    else
        self._wtRootButton = self:Wnd("DFButton_435", UIWidgetBase)
        self._wtRootButton:Collapsed()
    end
end

function FriendAddBox:_OnHovered()
    for _, action in ipairs(self._actionList) do
        if action == self.AddFriendActionName then
            self:AddInputActionBinding(action, EInputEvent.IE_Pressed, self._OnAddFriend, self, EDisplayInputActionPriority.UI_Pop)
        elseif action == self.AddBlackActionName then
            if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.LobbyBHD then
                self:AddInputActionBinding(action, EInputEvent.IE_Pressed, self._OnAddBlack, self, EDisplayInputActionPriority.UI_Pop)
            end
        elseif action == self.PlayerInfoActionName then
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
            self:AddInputActionBinding(action, EInputEvent.IE_Pressed, self._OnPlayerInfo, self, EDisplayInputActionPriority.UI_Pop)
        end
    end

    Module.Friend.Config.Events.evtOnFriendAddBoxHovered:Invoke(self._actionList)
end

function FriendAddBox:_OnUnHovered()
    for _, action in ipairs(self._actionList) do
        self:RemoveInputActionBinding(action)
    end
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    Module.Friend.Config.Events.evtOnFriendAddBoxUnHovered:Invoke(self._actionList)
end

function FriendAddBox:OnOpen()
    self:AddListeners()

    if not IsHD() then
        self:SetCppValue("bPreciseClick", true)
    end
end

function FriendAddBox:AddListeners()
    self:AddLuaEvent(Server.FriendServer.Events.evtSendAddSucess, self._AddPlayerSucced, self)
    self:AddLuaEvent(Server.FriendServer.Events.evtAddRoomAllPlayer, self._ChangeBtn, self)
end

function FriendAddBox:OnClose()
    self:RemoveAllLuaEvent()
end

function FriendAddBox:ShowUI(data, addFriendType)
    self._addFriendType = addFriendType
    self._playerInfo = data
    self._wtPlayerNameText:SetText(self._playerInfo.nick_name)
    if IsHD() then
        --self:SetBHDOwnMark(self._playerInfo)
    end
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.LobbyBHD then
        self:SetStyleInBHD()
        return
    end

    -- self._playIconInfo = {
    --     pic_url = self._playerInfo.pic_url,
    --     player_id = self._playerInfo.player_id,
    --     nick_name = self._playerInfo.nick_name,
    --     level = 0
    -- }

    -- -- BEGIN MODIFICATION @ VIRTUOS : Xbox gamer tag
    -- if IsConsole() then
    --     self._playIconInfo.plat = self._playerInfo.plat
    -- end
    -- -- END MODIFICATION

    self._playIconInfo = self._playerInfo

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        self._playIconInfo.rank_score = self._playerInfo.sol_rank_score
        if self._addFriendType == FriendApplySource.SystemApply then
            self._playIconInfo.level = self._playerInfo.season_level
            self._playIconInfo.rank_attended = self._playerInfo.sol_attended
        else
            self._playIconInfo.level = self._playerInfo.season_lvl
            self._playIconInfo.rank_attended = self._playerInfo.sol_rank_attended
        end
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        self._playIconInfo.level = self._playerInfo.level
        self._playIconInfo.rank_score = self._playerInfo.mp_rank_score

        if self._addFriendType == FriendApplySource.SystemApply then
            self._playIconInfo.rank_attended = self._playerInfo.mp_attended
        else
            self._playIconInfo.rank_attended = self._playerInfo.mp_rank_attended
        end
    end
    FriendLogic.InitRankByIconInfo(self._wtRankIcon, self._wtRankDivision, self._playIconInfo)

    local btnTbl = {
        HeadButtonType.AddFriend,
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddBlack,
    }

    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList, btnTbl, self._addFriendType)
    self._wtBtnBox:SetIsEnabledStyle(true)
    self._wtBtnBox:Visible()
    self._wtAddEndImage:Collapsed()

    if Module.Friend:CheckIsGameFriend(self._playerInfo.player_id) then
        self._wtAddFriendBtn:Collapsed()
    else
        self._wtAddFriendBtn:Visible()
    end
    --[[
    if addFriendType == FriendApplySource.NearbyApply then
        self._wtMeter:Visible()
        self._wtPlayerIcon:CollapsedLevel()
        if self._playerInfo.distance > 1000 then
            self._wtMeter:SetText(math.ceil(self._playerInfo.distance/1000) .. "km")
        else
            self._wtMeter:SetText("<1km")
        end
    else
        self._wtMeter:Collapsed()
    end
    ]]
end

function FriendAddBox:_PlayBoxAnimation()
    self:Visible()
    self:PlayAnimation(self.WBP_FriendAddBox_in_01, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false)
end

function FriendAddBox:_OnAddFriend()
    if self._addFriendType == FriendApplySource.NearbyApply then
        Module.Friend:AddFriend(tonumber(self._playerInfo.player_id), self._addFriendType, nil, nil, nil, self._playerInfo.plat_id)
        return
    end
    Module.Friend:AddFriend(self._playerInfo.player_id, self._addFriendType, nil, nil, nil, self._playerInfo.plat_id)
end

function FriendAddBox:_OnAddBlack()
    Server.FriendServer:AddPlayerToBlackList(self._playerInfo.player_id)
end

function FriendAddBox:_OnPlayerInfo()
    if self._wtPlayerIcon then
        self._wtPlayerIcon:ActiveClick()
    end
end

function FriendAddBox:_AddPlayerSucced(playerId)
    Module.GCloudSDK:OnAddFriendReport()
    if playerId == self._playerInfo.player_id then
        self._wtBtnBox:SetIsEnabledStyle(false)
    end
end

function FriendAddBox:_ChangeBtn()
    if not Module.Friend:CheckIsFriend(self._playerInfo.player_id) then
        self._wtBtnBox:Collapsed()
        self._wtAddEndImage:Visible()
    end
end

function FriendAddBox:SetStyleInBHD()
    self._playIconInfo = {
        pic_url = self._playerInfo.pic_url,
        player_id = self._playerInfo.player_id,
    }
    local btnTbl = {
        HeadButtonType.AddFriend,
        HeadButtonType.AddBlack,
    }
    FriendLogic.InitRankByIconInfo(self._wtRankIcon, self._wtRankDivision, self._playIconInfo)
    self._wtFriendBlock:SelfHitTestInvisible()
    self._wtRankIcon:Collapsed()
    self._wtRankDivision:Collapsed()
    --self._wtPlayerIcon:HitTestInvisible()
    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList,btnTbl)
end

function FriendAddBox:SetBHDOwnMark(info)
    --info.bhd_is_purchased = true
    if info.bhd_is_purchased then
        self._wtBHDMark:SelfHitTestInvisible()
    else
        self._wtBHDMark:Collapsed()
    end
end

function FriendAddBox:ShowTips()
    local datas = {}
    if self._tipsInfo ~= ""  then
        table.insert(datas, {textContent = Module.LobbyBHD.Config.Loc.BHDOwned})
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas,self._wtDFTipsAnchor)
    end

end

function FriendAddBox:CloseTips()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
    end
end

--[[ function FriendAddBox:ResetSetStyle()
    self._wtPlayerIcon:Visible()
    self._wtRankIcon:Visible()
    self._wtRankDivision:Visible()
end ]]

return FriendAddBox