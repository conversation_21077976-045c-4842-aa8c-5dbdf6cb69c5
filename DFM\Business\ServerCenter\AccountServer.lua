----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSAccount)
----- LOG FUNCTION AUTO GENERATE END -----------



-- @class AccountServer : ServerBase
local AccountServer = class("AccountServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local UGameVersionUtils = import "GameVersionUtils"
local BHDHelperTool = require "DFM.StandaloneLua.BusinessTool.BHDHelperTool"
local function log(...)
    loginfo("[AccountServer]", ...)
end

function AccountServer:Ctor()
    self.Events = {
        evtPlayerIdChanged = LuaEvent:NewIns("AccountServer.evtPlayerIdChanged"),
        evtKeyNode = LuaEvent:NewIns("AccountServer.evtKeyNode"),
        evtOnGetPunishNtf = LuaEvent:NewIns("AccountServer.evtOnGetPunishNtf"),
        evtPlayerHeadIconChange = LuaEvent:NewIns("AccountServer.evtPlayerHeadIconChange"),
        evtOnGetLightFeatureRes = LuaEvent:NewIns("AccountServer.evtOnGetLightFeatureRes"),
        evtOnAccountLoginSuccess = LuaEvent:NewIns("AccountServer.evtOnAccountLoginSuccess"),
        evtOnGVoicePunish = LuaEvent:NewIns("AccountServer.evtOnGVoicePunish"),
        evtPlayerStateCodeChanged = LuaEvent:NewIns("AccountServer.evtPlayerStateCodeChanged"),
        evtCheckParentControl = LuaEvent:NewIns("AccountServer.evtCheckParentControl"),

    }
    self.inviteTab = {}
    self.DEFAULT_COLD_TIME = 15
    self.profilePicUrl = ""
    self.cacheClientInfo = {}
    self:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_Online)
    Facade.ConfigManager:SetBoolean("EnableFakePlatIdType", true)  -- 默认开启
end

-- 玩家头像
function AccountServer:SetProfilePicUrl(url)
    local playerInfo = Server.SDKInfoServer:GetGamePlayerInfo()
    if playerInfo and url then
        playerInfo:SetPicUrl(url)
    else
        logerror("[AccountServer][SetProfilePicUrl] url or playerInfo is nil ")
    end
end

function AccountServer:GetProfilePicUrl()
    local playerInfo = Server.SDKInfoServer:GetGamePlayerInfo()
    if playerInfo and url then
        return playerInfo:GetPicUrl(url)
    else
        logerror("[AccountServer][GetProfilePicUrl] url or playerInfo is nil ")
        return ""
    end
end

function AccountServer:OnInitServer()
    Facade.ProtoManager:AddNtfListener("CSGMNotifyNtf", self.OnCSGMNotifyNtf, self)
    self:SetPlayerId(Server.SDKInfoServer:GetOpenId()) -- Todo:暂时playerId就是openId
    Facade.ProtoManager:AddNtfListener("CSPunishNtf", self.OnCSPunishNtf, self)
    Facade.ProtoManager:AddNtfListener("CSAccountRealTimeVoiceNtf", self.OnAccountRealTimeVoiceNtf, self)
end

function AccountServer:RecordInvitePlayerColdTab(playerId)
    loginfo("[AccountServer][RecordInvitePlayerColdTab] playerId:", playerId)
    local info = {playerId = playerId, coldTime = self.DEFAULT_COLD_TIME}
    self.inviteTab = self.inviteTab or {}
    self.inviteTab[playerId] = info
end

function AccountServer:GetRandomName(fCallBack)
    logerror("[AccountServer][GetRandomName]...")
    local RecvRandomName = function(res)
        if fCallBack then
            fCallBack(res)
        end
    end

    local req = pb.CSAccountRandNickReq:New()
    req.language = LocalizeTool.GetCurrentCulture()
    req:Request(RecvRandomName,{maxWaitTime = 10, bEnableHighFrequency = true})
end

function AccountServer:ReqCreatePlayer(playerName,fCallBack)
    logerror("[AccountServer][ReqCreatePlayer] playerName:", playerName)
    local CreatePlayerRes = function(res)
        if fCallBack then
            fCallBack(res)
        end
    end
    local channelId = EChannelType.kChannelNone
    local req = pb.CSAccountRegisterReq:New()
    req.game_nick = playerName
    req.client_info = self.cacheClientInfo
    req.build_version = VersionUtil.GetVersionFull()
    req.res_version = VersionUtil.GetResVersion()
    req.channel_id = Server.SDKInfoServer:GetChannel()
    req.regist_channel = Server.SDKInfoServer:GetConfigChannelId()
    req.core_user_id = Server.SDKInfoServer:GetCoreUserId()
    if LoginUtil.IsSimulatorInWhiteList() == true then
        req.is_gamematrix = true
    end
    req:Request(CreatePlayerRes,{bShowLoading = true, whiteListErrorShow = {Err.AccountRegisterTimeLimit}})
end

function AccountServer:GetStateInfo(fCallBack, param)
    logwarning("[AccountServer][GetStateInfo]...")

    local OnCSStateGetInfoRes = function(res)
        logwarning("[AccountServer][GetStateInfo] result:",res.result)
        logtable(res,true)
        self:_ParsePlayerStateCode(res)
        if fCallBack then
            fCallBack(res)
        end
    end

    local req = pb.CSStateGetInfoReq:New()
    local bHighFrequency = true
    local bResendAfterReconnected = false
    if param  then
        if param.bHighFrequency then
            bHighFrequency = param.bHighFrequency
        end
       
        if param.bNeedResendAfterReconnected then
            logerror("[AccountServer][GetStateInfo] bNeedResendAfterReconnected")
            bResendAfterReconnected = param.bNeedResendAfterReconnected
        end
    end
    req:Request(OnCSStateGetInfoRes,{bBuildEmptyRecv = false, bEnableHighFrequency = bHighFrequency, bNeedResendAfterReconnected = bResendAfterReconnected})
end

function AccountServer:SaveData(openId, areaId)
    logwarning("[AccountServer][GetStateInfo] openId and areId:",openId, areaId)
    Facade.ConfigManager:SetString("lastOpenId", openId)
    Facade.ConfigManager:SetString("lastUserAreaId", areaId)
end

function AccountServer:OnCSGMNotifyNtf(ntf)
    logwarning("[AccountServer][OnCSGMNotifyNtf]...")
    self.CheckAIRatingContent = ntf.content
end

--- 账号处罚相关
function AccountServer:OnCSPunishNtf(ntf)
    logwarning("[AccountServer][OnCSPunishNtf]...")
    local msg = self:SerializePunishNtfMessage(ntf)
    self.Events.evtOnGetPunishNtf:Invoke(msg, ntf)
end

--- 解析处罚数据并拼接成客户端字符串
function AccountServer:SerializePunishNtfMessage(ntf)
    local limitMessage = nil

    if ntf.custom_reason and not (ntf.custom_reason == "") then
        limitMessage = ntf.custom_reason
    elseif ntf.reason then
        local accountPunishData = Facade.TableManager:GetTable("AccountPunishReason")
        local findedMsgData = table.find(accountPunishData, function(v, k) return k == tostring(ntf.reason) end)
        if findedMsgData then
            limitMessage = findedMsgData.Message
        else
            limitMessage = string.format(ServerTipCode.PunishDataNoConfig, ntf.reason)
        end
    else
        logerror("接收到异常封禁数据包")
        logtable(ntf)
        return limitMessage
    end

    local limitOverTime = nil
    local limitOverTimeStr = nil
    if ntf.over_time then
        limitOverTime = ntf.over_time
        limitOverTimeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(ntf.over_time)
    end

    local limitStartTime = nil
    local limitStartTimeStr = nil
    if ntf.punish_start_time then
        limitStartTime = ntf.punish_start_time
        limitStartTimeStr = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(ntf.punish_start_time)
    end

    local limitLength = nil
    if limitStartTime and limitOverTime then
        limitLength = TimeUtil.TransSeconds2Str(limitOverTime - limitStartTime)
    end

    limitMessage = StringUtil.Key2StrFormat(limitMessage, {["FREETIME"]=limitOverTimeStr, ["STARTTIME"]=limitStartTimeStr, ["BANTIME"]=limitLength});

    return limitMessage
end

function AccountServer:AccountLoginReq(fCallBack, extParam)
    logerror("[AccountServer][AccountLoginReq]...")
    local OnCSAccountLoginRes = function(res)
        self.Events.evtKeyNode:Invoke("PointReport",{Login = string.format("LoginState_%d",res.result or -999)})
        logerror("[AccountServer][AccountLoginReq] callback")
        local downloadUrl = ""
        if res.result == 0 then
            local playerId = res.player_id
            local nickName = res.game_nick
            local playerAbilityScore = res.capability
            local picUrl = res.pic_url
            local areaId = res.area_id
            local zoneId = res.zone_id
            Server.SDKInfoServer:SetZoneId(zoneId)
            Server.RankingListServer:SetAreaAcode(tostring(res.area_code))
            
            Server.GuideServer:SetStageFromLogin(res.guide_stage_id)
            Server.AccountServer:SetPlayerRegisterState(res.is_register)
            Server.PayServer:SetOfferId(res.offer_id)
            
            Facade.ProtoManager:SetCanSendProto(true)
            Server.PropertyServer:SetAbilityScore(playerAbilityScore)
            self:SetProfilePicUrl(picUrl or "")
            self:SaveData(Server.SDKInfoServer:GetOpenIdStr(), areaId)
            self:SetPlayerId(playerId)
            Server.PayServer:SetRegistrationRegion(res.country_belonging)
            Server.SDKInfoServer:SetLoginRegionCode(res.country_code)
            Server.SDKInfoServer:SetLaunchForm(0)
            Server.SDKInfoServer:SetUserName(nickName)
            local key = Server.SDKInfoServer:GetSelectAddrKey()
            Server.SDKInfoServer:SaveServerAddr(key)
            --- BEGIN MODIFICATION @ VIRTUOS: 接入XSX米大师支付
            if IsConsole() then
                Server.SDKInfoServer:SetAreaId(areaId)
	        end
            --- END MODIFICATION
            downloadUrl = res.download_url
            if( downloadUrl) and downloadUrl ~= "" then
                local systemUtil = require "DFM.YxFramework.Managers.UI.Util.UISystemUtil"
                systemUtil.OpenWeb(downloadUrl)
            end

            -- 轻特征信息登录下发
            local lightFeatureRes = {
                name = res.tss_feature_name,
                data = res.tss_feature_data,
                crc = res.tss_feature_data_crc
            }
            self.Events.evtOnGetLightFeatureRes:Invoke(lightFeatureRes)

            -- 需要等登陆包发完之后，ACE的包才能开始发，否则会被服务器踢下线
            -- 2024-11-26 PC也需要轻特征信息
            self.Events.evtOnAccountLoginSuccess:Invoke(lightFeatureRes)

            -- ASA数据采集和上报
            if PLATFORM_IOS then
                local data = {}
                LogAnalysisTool.O2ASAReportedData(data)
            end

            -- 家长控制信息
            if IsBuildRegionGlobal() then
                self.Events.evtCheckParentControl:Invoke(0.1, true)
            end

            --- 上报登录信息
            if IsBuildRegionGlobal() then
                local LoginAgreementStatus = pb.LoginAgreementStatus:New()
                LoginAgreementStatus.LoginAgreementStatus = self._isShowPrivacyPanel and 0 or 1
                LogAnalysisTool.AddTglog(LoginAgreementStatus)
            end

            --记录灰度数据
            self:SetExpTagId(res.exp_tag_id)
        end
        if fCallBack and downloadUrl == "" then
            fCallBack(res)
        end
    end

    if not self.cacheClientInfo or #self.cacheClientInfo == 0 then
        self.cacheClientInfo = LogAnalysisTool.GetClientInfoLog()
        self.cacheClientInfo.match_client_type = self:GetClientType()
    end
    local ClientInfo = self.cacheClientInfo
    local ChannelId = Server.SDKInfoServer:GetChannel()
    local Language = LocalizeTool.GetCurrentCulture()

    self.Events.evtKeyNode:Invoke("PointReport",{Login = "TryLoginReq"})
    local req = pb.CSAccountLoginReq:New()
    req.game_nick = string.sub( Server.SDKInfoServer:GetOpenId(),1,13) -- TODO:用于服务器自动注册
    req.pic_url = Server.SDKInfoServer:GetPictureUrl()
    req.plat_id = self:GetPlatIdType()
    req.channel_id = ChannelId
    req.language = Language
    req.build_version = VersionUtil.GetVersionFull()
    req.res_version = VersionUtil.GetResVersion()
    req.lua_version = VersionUtil.GetLuaVersion()

    req.is_simulator        = false -- EmulatorManager.Instance.MatchAsEmulator(); -- Tss组件调用
    req.is_gamepad          = false -- InputManager.Instance.IsGamePadAvailable(); -- TODO: 后续识别补充
    req.server_url          = Server.SDKInfoServer:GetConnectedUrl() -- StringUtil.StringToUtf8Bytes(ApolloWrapper.Login.GetConnectedURL());

    req.login_channel = Server.SDKInfoServer:GetConfigChannelId()

    -- 好友邀请回流的playerid
    req.recall_inviter      = nil -- nil -- DataStoreManager.Instance.FriendRecallDataStore.CallbackInviterPlayerId;
    -- 是否是手动重连
    req.is_reconnect        = Facade.ProtoManager:GetReconnectState() -- StageManager.Instance.GetLobbyLoginStage() == LobbyLoginStage.Reconnect;

    req.quick_reconnect      = Facade.ProtoManager:GetQuickReconnectState() -- OtherLoginMsgDataAgent.Instance.UseFastReconnect() ? true : false;
    -- 重连的理由
    req.reconnect_reason    = nil -- loginReasonToServer;

    req.match_client_type = self:GetClientType()

    req.client_info = ClientInfo
    req.token = Server.PayServer:GetPayToken()
    req.core_user_id = Server.SDKInfoServer:GetCoreUserId()
    -- 强制登录
    req.force_kick_old_conn = (extParam and extParam.force_kick_old_conn) or false

    req.extra_login_method = ExtraLoginMethod.by_no_care
    if VersionUtil.IsGameChannelSteam() then
        req.extra_login_method = ExtraLoginMethod.by_steam
    elseif VersionUtil.IsGameChannelEpic() then
        req.extra_login_method = ExtraLoginMethod.by_epic
    end

    req.launcher_channel = VersionUtil.GetLauncherChannel()

    req.wglogin_info = Server.SDKInfoServer:GetWeLoginInfo()
    req.ga_access_token = Server.SDKInfoServer:GetAccessToken()
    if LoginUtil.IsSimulatorInWhiteList() == true then
        req.is_gamematrix = true
    end

    local bEnableHighFrequency = false
    if extParam then
        bEnableHighFrequency = extParam.bEnableHighFrequency or false
    end

    if IsHD() or _WITH_EDITOR == 1 then
        local netbarInfo = Server.SDKInfoServer:GetNetBarInfo()
        if netbarInfo and netbarInfo.bIsNetBarMachine then
            req.netbar_token = Server.SDKInfoServer:GetNetBarToken()
            req.macs = Server.SDKInfoServer:GetNetBarMacs()
        end
    end

    if IsBuildRegionGlobal() or IsBuildRegionGA() then
        req.intl_info = req.intl_info or pb.ClientIntlInfo:New()
        req.intl_info.channel_id = Server.SDKInfoServer:GetLoginSubChannelID()
        req.intl_info.sdk_version = UE.INTLSDKAPI.GetSDKVersion()
        req.intl_info.token = Server.SDKInfoServer:GetToken()
        req.intl_info.intl_country_belonging = tonumber(Server.SDKInfoServer:GetRegionNumericCode())
    end
    if DFHD_LUA == 1 then
        if PLATFORM_WINDOWS and IsWeGameEnabled() then
            req.bhd_build_version = VersionUtil.GetBHDVersion()
        else
            req.bhd_build_version = BHDHelperTool.GetLocalBHDClientVersion()
        end
    else
        req.bhd_build_version = 0
    end

    if Server.PayServer:IsGoogleEnable() then
        req.gwallet_info = req.gwallet_info or pb.ClientGwalletInfo:New()
        Server.PayServer:FillClientGwalletInfo(req.gwallet_info)
    end

    if IsBuildRegionGlobal() then
        req.appchannel = Server.SDKInfoServer:GetStoreChannel()
    end
   
    logtable(req,true)
    req:Request(OnCSAccountLoginRes,{whiteListErrorShow = {Err.AccountPlayerNotRegistered},bEnableHighFrequency = bEnableHighFrequency })
end

function AccountServer:OnDestroyServer()
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self:RemoveAllLuaEvent()
end

function AccountServer:GetPlayerId()
    local playerId = Server.SDKInfoServer:GetOpenId()
    return playerId or 0
end

function AccountServer:GetPlayerIdStr()
    local playerId = Server.SDKInfoServer:GetOpenIdStr()
    return playerId or ""
end

-- uint64 从lua中传递到c++ 
--服务器下发的openID是uint64 其他地方来的openId是 string
function AccountServer:SetPlayerId(playerId)
    logerror("[AccountServer][SetPlayerId] playerId:", tostring(playerId))
    Server.SDKInfoServer:SetOpenId(playerId)
    Facade.ConfigManager:InitPlayer(self:GetPlayerIdStr())
    Facade.SaveGameManager:InitSaveGame(self:GetPlayerId())
    self.Events.evtPlayerIdChanged:Invoke()
end

function AccountServer:SetPlayerRegisterState(state)
    local playerInfo = Server.SDKInfoServer:GetGamePlayerInfo();
	if state == nil then
		return
	end
    if playerInfo then
        self.registerState = state --true 已注册 false 未注册
        playerInfo:SetRegisterState(state)
    else
        logerror("[AccountServer][SetPlayerRegisterState] playerInfo is nil")
    end
end

function AccountServer:GetPlayerRegisterState()
    local playerInfo = Server.SDKInfoServer:GetGamePlayerInfo();
    if playerInfo then
        return playerInfo:IsRegisterState()
    end
end

function AccountServer:_GetPlayerStateCode()
    if not self._playerStateCode then
        logerror("[AccountServer][GetPlayerStateCode] error")
        self._playerStateCode = 1
    end
    return self._playerStateCode
end

function AccountServer:GetPlayerState()
    return self:_GetPlayerStateCode()
end

function AccountServer:ResetPlayerStateCode()
    self:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_Online)
end

function AccountServer:SetPlayerStateCode(enumPlayerState)
    logwarning("[AccountServer][SetPlayerState] enumPlayerState:", enumPlayerState)
    self._playerStateCode = self._playerStateCode or 1
    if enumPlayerState == GlobalPlayerStateEnums.EPlayerState_Offline then
        self._playerStateCode = GlobalPlayerStateEnums.EPlayerState_Offline
    elseif enumPlayerState == GlobalPlayerStateEnums.EPlayerState_Online then
        self._playerStateCode = GlobalPlayerStateEnums.EPlayerState_Online
    elseif enumPlayerState == GlobalPlayerStateEnums.EPlayerState_InTeam then
        self._playerStateCode = self._playerStateCode & (1023 - GlobalPlayerStateEnums.EPlayerState_InRoom)
        self._playerStateCode = self._playerStateCode | GlobalPlayerStateEnums.EPlayerState_InTeam
    elseif enumPlayerState == GlobalPlayerStateEnums.EPlayerState_InRoom then
        self._playerStateCode = self._playerStateCode & (1023 - GlobalPlayerStateEnums.EPlayerState_InTeam)
        self._playerStateCode = self._playerStateCode | GlobalPlayerStateEnums.EPlayerState_InRoom
    elseif enumPlayerState == GlobalPlayerStateEnums.EPlayerState_InMatch then
        self._playerStateCode = self._playerStateCode & (1023 - GlobalPlayerStateEnums.EPlayerState_Matching)
        self._playerStateCode = self._playerStateCode | GlobalPlayerStateEnums.EPlayerState_InMatch
    elseif enumPlayerState == GlobalPlayerStateEnums.EPlayerState_Matching then
        self._playerStateCode = self._playerStateCode & (1023 - GlobalPlayerStateEnums.EPlayerState_InMatch)
        self._playerStateCode = self._playerStateCode | GlobalPlayerStateEnums.EPlayerState_Matching
    else
        self._playerStateCode = enumPlayerState
    end
    self.Events.evtPlayerStateCodeChanged:Invoke(self._playerStateCode)
end

function AccountServer:IsInRoom()
    log(
        "[AccountServer][IsInRoom]",
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InRoom,
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InRoom ~= 0
    )
    return self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InRoom ~= 0
end
function AccountServer:IsInTeam()--调用的地方太多，log注释掉
    return self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InTeam ~= 0
end

function AccountServer:IsInIdle()
    log(
        "[AccountServer][IsInIdle]",
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_Online,
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_Online ~= 0
    )
    return self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_Online ~= 0
end

function AccountServer:IsPlayerInMatching()
    log(
        "[AccountServer][IsPlayerInMatching]",
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_Matching,
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_Matching ~= 0
    )
    return self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_Matching ~= 0
end

function AccountServer:IsPlayerInGame()
    log(
        "[AccountServer][IsPlayerInGame]",
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InMatch,
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InMatch ~= 0
    )
    return self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_InMatch ~= 0
end

function AccountServer:IsInPickHeroStage()
    log(
        "[AccountServer][IsInPickHeroStage]",
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_PickHeroStage,
        self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_PickHeroStage ~= 0
    )
    return self:_GetPlayerStateCode() & GlobalPlayerStateEnums.EPlayerState_PickHeroStage ~= 0
end

function AccountServer:_ParsePlayerStateCode(res)
    loginfo("AccountServer:_ParsePlayerStateCode",res.State)
    if not res.State then
        logerror("[AccountServer][ParsePlayerStateCode] error")
        self:SetPlayerStateCode(GlobalPlayerStateEnums.EPlayerState_Online)
        return
    end
    local localStateCode = res.State
    self._playerStateCode = localStateCode

    if self:IsInTeam() then
        Server.TeamServer:UpdateTeamInfos(res.TeamID)
    elseif self:IsInRoom() then
        Server.RoomServer:ReqRoomInfo(res.RoomID)
    end

    if self:IsPlayerInGame() or self:IsInPickHeroStage() then
        Server.MatchServer:SetMatchModeInfo(res.mode)
    end
end

function AccountServer:GetPlatIdType()
    local isPC = DFHD_LUA == 1
    local isMobile = IsMobile()
    local isSimulator = false
    local isGamePad = false
    --判断是否模拟器
    if isMobile then
        local UGameTss = import "DFMGameTss"
        local gameTssIns = UGameTss.Get(GetGameInstance())
        if gameTssIns then
            local emulatorName = gameTssIns:GetEmulatorName()
            loginfo(StringUtil.SequentialFormat("[AccountServer] GetClientType emulatorName:{0}", tostring(emulatorName)))
            if not string.isempty(emulatorName) then
                isSimulator = true
            end
        end
    end
    --先判断是PC还是Mobile,如果是Mobile先看是不是模拟器、接外设
    local platId = PlatIDType.Plat_Invalid
    if isPC then
        platId = PlatIDType.Plat_PC
    elseif isMobile then
        if isSimulator and LoginUtil.IsSimulatorInWhiteList() then
            platId = PlatIDType.Plat_Android
        elseif isGamePad then
            platId = PlatIDType.Plat_Invalid
        else
            if PLATFORM_IOS then
                platId = PlatIDType.Plat_IOS
            elseif PLATFORM_OPENHARMONY then
                platId = PlatIDType.Plat_Harmony
            else
                platId = PlatIDType.Plat_Android
            end
        end
    elseif _WITH_EDITOR == 1 then
        platId = PlatIDType.Plat_Invalid
    elseif PLATFORM_WINDOWS then
        platId = PlatIDType.Plat_Windows
    end

    --- BEGIN MODIFICATION @ VIRTUOS: console平台登录信息
    if IsXboxSeries() then
        platId = PlatIDType.Plat_XBox
    elseif PLATFORM_PS4 == 1 or IsPS5Family() then
        platId = PlatIDType.Plat_Playstation
    end
    --- END MODIFICATION

    logerror(StringUtil.SequentialFormat("[AccountServer][GetPlatIdType] isPC:{0} isMobile:{1} isSimulator:{2} isGamePad:{3} client_type:{4}", tostring(isPC), tostring(isMobile), tostring(isSimulator), tostring(isGamePad), tostring(platId)))
    return platId
end

function AccountServer:GetClientType()
    local isPC = DFHD_LUA == 1
    local isMobile = IsMobile()
    local isSimulator = false
    local isGamePad = false
    --判断是否模拟器
    if isMobile then
        local UGameTss = import "DFMGameTss"
        local gameTssIns = UGameTss.Get(GetGameInstance())
        if gameTssIns then
            local emulatorName = gameTssIns:GetEmulatorName()
            loginfo(StringUtil.SequentialFormat("[AccountServer][GetClientType] emulatorName:{0}", tostring(emulatorName)))
            if not string.isempty(emulatorName) then
                isSimulator = true
            end
        end
    end
    --先判断是PC还是Mobile,如果是Mobile先看是不是模拟器、接外设
    local clientType = MatchClientType.Client_Invalid
    if isPC then
        if _WITH_EDITOR == 1 then
            clientType = MatchClientType.Client_PC_Editor
        else
            clientType = MatchClientType.Client_PC
        end
    elseif isMobile then
        if isSimulator then
            clientType = MatchClientType.Client_Simulator
        elseif isGamePad then
            clientType = MatchClientType.Client_Controller
        else
            if PLATFORM_IOS then
                clientType = MatchClientType.Client_IOS
            else
                clientType = MatchClientType.Client_Android
            end
        end
    elseif _WITH_EDITOR == 1 then
        clientType = MatchClientType.Client_Editor
    elseif PLATFORM_WINDOWS then
        clientType = MatchClientType.Client_Windows
    end
    loginfo(StringUtil.SequentialFormat("[AccountServer][GetClientType] isPC:{0} isMobile:{1} isSimulator:{2} isGamePad:{3} client_type:{4}", tostring(isPC), tostring(isMobile), tostring(isSimulator), tostring(isGamePad), tostring(clientType)))
    return clientType
end

function AccountServer:UpdatePlayerHeadIcon(avatarId)
    local req = pb.CSAccountUpdateAvatarReq:New()
    req.avatar_id = avatarId
    req:Request(
        function(res)
            if res.result == 0 then
                if avatarId ~= 0 then
                    Server.RoleInfoServer:SetPicUrl(tostring(avatarId))
                    self.Events.evtPlayerHeadIconChange:Invoke(avatarId)
                else
                    Server.RoleInfoServer:SetPicUrl(tostring(Server.SDKInfoServer:GetPictureUrl()))
                    self.Events.evtPlayerHeadIconChange:Invoke(tostring(Server.SDKInfoServer:GetPictureUrl()))
                end

            end
        end
    )
end

function AccountServer:ReqAccountAllowRealTimeVoice()
    loginfo("[AccountServer][ReqAccountAllowRealTimeVoice]")
    local req = pb.CSAccountAllowRealTimeVoiceReq:New()
    req:Request(
        function(res)
            if res.result == 0 and res.punish_info ~= nil then
                self.Events.evtOnGVoicePunish:Invoke(res.punish_info)
            end
        end
    )
end

function AccountServer:ReqAccountUnicodeConf(fCallBack)
    logerror("[AccountServer][ReqAccountUnicodeConf]")
    local req = pb.CSAccountGetUnicodeConfReq:New()
    req:Request(fCallBack)
end

function AccountServer:OnAccountRealTimeVoiceNtf(ntf)
    if ntf ~= nil and ntf.punish_info ~= nil then
        self.Events.evtOnGVoicePunish:Invoke(ntf.punish_info)
    end
end

function AccountServer:ReqGetPlayerProfile(playerId,fCallback)
    loginfo("[AccountServer][ReqAccountGetPlayerProfile] playerId: ",playerId)
    local fGetPlayerProfileRes=function(res)
        loginfo("[AccountServer][ReqAccountGetPlayerProfile] fGetPlayerProfileRes:",res.result)
        if res.result==0 then
            if fCallback then
                fCallback(res)
            end
        end
    end
    local req=pb.CSAccountGetPlayerProfileReq:New()
    req.player_id=playerId or self:GetPlayerId()
    req:Request(fGetPlayerProfileRes)
end

function AccountServer:ReportAccountLaunchId(game_center_type)
    logerror("[AccountServer][ReportAccountLaunchId] game_center_type: ", game_center_type)
    local req = pb.CSClientGameCenterSelectedNtf:New()
    req.game_center_type = game_center_type
    req:SendNtf()
end

function AccountServer:SetIsShowPrivacyPanel(isShowPrivacyPanel)
    loginfo("AccountServer:SetIsShowPrivacyPanel",isShowPrivacyPanel)
    self._isShowPrivacyPanel = isShowPrivacyPanel
end

function AccountServer:IsOpenFakePlat()
    local enableFakePlatIdType = Facade.ConfigManager:GetBoolean("EnableFakePlatIdType", false) -- 默认false
    return enableFakePlatIdType and (IsXboxSeries() or IsPS5Family())  and not self:IsCrossPlat()
end

function AccountServer:IsCrossPlat()
    return (IsXboxSeries() and Server.RoleInfoServer.can_cross_plat_play) or (IsPS5Family() and Server.RoleInfoServer.hasCrossPlayPrivilege)
end


-- 判断是否是假的同平台玩家 但是其实是跨平台玩家
function AccountServer:IsSamePlatPlayer(curPlatID)
    return self:GetPlatIdType() == curPlatID
end

function AccountServer:CheckExpTagIdValid(exp_tag_id)
    if self._exp_tag_ids and #self._exp_tag_ids > 0 then
        if table.contains(self._exp_tag_ids, exp_tag_id) then
            return true
        else
            logerror("[AccountServer][CheckExpTagIdValid] exp_tag_id not in exp_tag_ids", exp_tag_id)
            return false
        end
    end
end

---@desc 直播时长事件上报
---@param social_id number 社交平台名称
---@param result number 停留时间
function AccountServer:OnCSSocialAccountFlowReq(social_id, result)
    local SocialAccountsFlow = pb.SocialAccountsFlow:New()
    if SocialAccountsFlow then
        SocialAccountsFlow.SocialID = social_id
        SocialAccountsFlow.Result = result
        LogAnalysisTool.AddTglog(SocialAccountsFlow)
    end
end


function AccountServer:SetExpTagId(exp_tag_ids)
    self._exp_tag_ids  = exp_tag_ids or {}
end

return AccountServer
