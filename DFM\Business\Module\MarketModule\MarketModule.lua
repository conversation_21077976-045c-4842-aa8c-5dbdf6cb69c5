----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMMarket)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class MarketModule : ModuleBase
local MarketModule = class("MarketModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local MarketLogic = require "DFM.Business.Module.MarketModule.Logic.MarketLogic"
local MarketEventLogic = require "DFM.Business.Module.MarketModule.Logic.MarketEventLogic"

function MarketModule:Ctor()
end

---------------------------------------------------------------------------------
--- Mo<PERSON><PERSON> 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function MarketModule:OnInitModule()
    Module.Market.Field:GenMarketTabInfo()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认[常驻]加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function MarketModule:OnLoadModule()
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function MarketModule:OnUnloadModule()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function MarketModule:OnDestroyModule()
    self:CloseMainPanel()
    self:RemoveAllLuaEvent()
end

---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function MarketModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- MarketEventLogic.RemoveLobbyListeners()
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function MarketModule:OnGameFlowChangeEnter(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- MarketEventLogic.AddLobbyListeners()
    end
end

---------------------------------------------------------------------------------
--- Loading 生命周期
--- 设置bAutoLoading = true则下列生命周期有效
--- 模块[Loading]加载资源，区分局内外
---------------------------------------------------------------------------------
---@overload fun(ModuleBase, OnLoadingLogin2Frontend)
function MarketModule:OnLoadingLogin2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function MarketModule:OnLoadingGame2Frontend(gameFlowType)
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function MarketModule:OnLoadingFrontend2Game(gameFlowType)
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------
function MarketModule:ShowMainPanel(subPageType)
    subPageType = setdefault(subPageType, EMarketSubPageType.MandelBrick)
    return MarketLogic.ShowMainPanelProcess(subPageType)
end

function MarketModule:CloseMainPanel()
    return MarketLogic.CloseMainPanelProcess()
end

function MarketModule:DoSomeThing(...)
    return MarketLogic.DoSomeThingProcess(...)
end

--------------------------------------------------------------------------
--- Public 显示购买曼德尔砖弹窗 API
--------------------------------------------------------------------------
---@param subPageType EMarketSubPageType
---@param itemId number
function MarketModule:JumpToMandelBrickBuyPage(itemId)
    return MarketLogic.ShowBuyMandelBrickPopProcess(itemId)
end

--------------------------------------------------------------------------
--- Public 显示售卖曼德尔砖弹窗 API
--------------------------------------------------------------------------
---@param itemId number
function MarketModule:JumpToMandelBrickSellPage(itemId)
    return MarketLogic.ShowSellMandelBrickPopProcess(itemId)
end

--------------------------------------------------------------------------
--- Public 显示购买玄学皮肤页 API
--------------------------------------------------------------------------
---@param subPageType EMarketSubPageType
---@param itemId number
function MarketModule:JumpToMysticalSkinBuyPage(subPageType, itemId)
    return MarketLogic.ShowBuyMysticalSkinPopProcess(subPageType, itemId)
end

--------------------------------------------------------------------------
--- Public 显示售卖玄学皮肤页 API
--------------------------------------------------------------------------
---@param itemId number
function MarketModule:JumpToMysticalSkinSellPage(itemId)
    return MarketLogic.ShowSellMysticalSkinSellPageProcess(itemId)
end

--------------------------------------------------------------------------
--- Public 显示售卖玄学皮肤弹窗 API
--------------------------------------------------------------------------
---@param mysticalSkinInsItem userdata
function MarketModule:JumpToMysticalSkinSellPopWindow(mysticalSkinInsItem)
    return MarketLogic.ShowSellMysticalSkinPopProcess(mysticalSkinInsItem)
end

--------------------------------------------------------------------------
--- Public 数据点处理算法 API
--------------------------------------------------------------------------
---@param itemId number
function MarketModule:dataPointsProcessing(dataPoints, intervalNum, bFrontAdapt, bBackAdapt)
    return MarketLogic.dataPointsProcessing(dataPoints, intervalNum, bFrontAdapt, bBackAdapt)
end

return MarketModule
