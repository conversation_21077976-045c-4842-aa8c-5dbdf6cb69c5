----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
----- LOG FUNCTION AUTO GENERATE END -----------

local SolDetailKillListBox = ui("SolDetailKillListBox")
local UDFMSettlementManager = import "DFMSettlementManager"
local settlementMgrIns = UDFMSettlementManager.Get(GetWorld())

function SolDetailKillListBox:Ctor()
    self._wtCampImage = self:Wnd("DFImage_Line_1", UIImage)
    self._wtNickName = self:Wnd("wtNameTB",UIWidgetBase)
    self._wtLevelTB = self:Wnd("wtLevelTB", UITextBlock)
    self._wtRankIcon = self:Wnd("wtRankIconImg", UIWidgetBase)
    self._wtRankName = self:Wnd("wtRankNameTB", UIWidgetBase)
    self._wtCampTB = self:Wnd("wtCampTB", UIWidgetBase)
    self._wtTimeTB = self:Wnd("wtTimeTB", UIWidgetBase)
    -- BEGIN MODIFICATION @ VIRTUOS : 历史击杀无法用手柄浏览scrollbox
    if IsHD() then
        self:SetCppValue("bIsFocusable", true)
    end
    -- 平台Icon的引用
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    -- END MODIFICATION
end

function SolDetailKillListBox:OnOpen()
    
end

function SolDetailKillListBox:OnClose()

end

function SolDetailKillListBox:OnInitExtraData()
    
end

function SolDetailKillListBox:RefreshKillBox(value)
    local killInfoDataRow = {}
    killInfoDataRow.time = TimeUtil.GetSecondsFormatMMSSStr(value.timestamp)
    logerror("SolDetailKillListBox:RefreshKillBox",value.enemy_type, value.player.player_id, value.player.game_nick, value.player.plat_id)
    if value.enemy_type == EGspEnemyType.kGspEnemyTypePlayer then
        killInfoDataRow.plat_id = value.player.plat_id
        killInfoDataRow.name = value.player.game_nick
        killInfoDataRow.level = value.player.level
        killInfoDataRow.rankScore = value.player.rank_match_score
    
        local rankInfo = Module.Ranking:GetMinorDataByScore(value.player.rank_match_score)
        killInfoDataRow.rankName = rankInfo.RankName
    
        killInfoDataRow.camp = Module.Settlement.Config.Loc.KilledHero
    else
        killInfoDataRow.plat_id = value.ai.plat_id or PlatIDType.Plat_PC
        killInfoDataRow.name = value.ai.game_nick
        killInfoDataRow.level = value.ai.level and value.ai.level ~= 0 and value.ai.level or "--"
        killInfoDataRow.rankScore = value.ai.rank_match_score or 0
        local rankInfo = Module.Ranking:GetMinorDataByScore(value.ai.rank_match_score)
        killInfoDataRow.rankName = rankInfo.RankName
    
        if value.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
            killInfoDataRow.camp = Module.Settlement.Config.Loc.KilledHero
        elseif value.enemy_type == EGspEnemyType.kGspEnemyTypeAI or value.enemy_type == EGspEnemyType.kGspEnemyTypeBoss then
            killInfoDataRow.level = "--"
            killInfoDataRow.name = settlementMgrIns:Read4Buffer(value.ai.game_nick)
            if value.ai.tag_array[1] == "SOL_DeltaPMC" then
                killInfoDataRow.camp = Module.Settlement.Config.Loc.KilledEnemyDeltaPMC
            elseif value.ai.tag_array[1] == "SOL_DarkTide" then
                killInfoDataRow.camp = Module.Settlement.Config.Loc.KilledEnemyDarkTide
            else
                killInfoDataRow.camp = Module.Settlement.Config.Loc.KilledEnemyHudSon
            end
        else
            killInfoDataRow.level = "--"
            killInfoDataRow.name = Module.Settlement.Config.Loc.Crocodile
            killInfoDataRow.camp = Module.Settlement.Config.Loc.WildAnimals
        end
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole() and self._wtPlatformIcon then
        local platIconPath = Module.Friend:GetPlatformIconPath(killInfoDataRow.plat_id)
        if platIconPath then
            self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
            self._wtPlatformIcon:SelfHitTestInvisible()
        else
            self._wtPlatformIcon:Collapsed()
        end
    end

    -- TRC: using Platform Online ID as player name.
    if IsPS5Family() then
        local onlineID = Module.Social:GetPS5OnlineIdByUID(killInfoDataRow.plat_id)
        if string.isempty(onlineID) then
            -- async require online id if not cached yet.
            local callback = function(onlineID)
                self._wtNickName:SetText(onlineID)
            end
            Module.Social:AsyncGetPS5OnlineIdByUID(killInfoDataRow.plat_id, callback, self)
        else
            killInfoDataRow.name = onlineID
        end
    end
    --- END MODIFICATION
   
    self._wtTimeTB:SetText(killInfoDataRow.time)
    self._wtNickName:SetText(killInfoDataRow.name)
    self._wtLevelTB:SetText(killInfoDataRow.level)
    
    self._wtCampTB:SetText(killInfoDataRow.camp)


    if value.enemy_type == EGspEnemyType.kGspEnemyTypePlayer or value.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
        self._wtRankIcon:Visible()
        self._wtRankName:Visible()
        if killInfoDataRow.rankScore < 1000 then
            self._wtRankIcon:SetRankIconNone()
            self._wtRankName:SetText(Module.RoleInfo.Config.Loc.NoRankDataTxt)
        else
            self._wtRankIcon:SetRankingIconByScore(killInfoDataRow.rankScore)
            self._wtRankName:SetText(killInfoDataRow.rankName)
        end

    else
        self._wtRankIcon:Collapsed()
        self._wtRankName:SetText("--")
    end

    if value.enemy_type == EGspEnemyType.kGspEnemyTypePlayer or value.enemy_type == EGspEnemyType.kGspEnemyTypePlayerAI then
        self._wtCampImage:AsyncSetImagePath(Module.RoleInfo.Config.KillerPlayerTypePath)
        self:SetType(true)
    else
        self._wtCampImage:AsyncSetImagePath(Module.RoleInfo.Config.KillerAITypePath)
        self:SetType(false)
    end
end

return SolDetailKillListBox