---------- LOG FUNCTION AUTO GENERATE -------------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
-------- LOG FUNCTION AUTO GENERATE END -----------

--#region引用
--- 对应蓝图:WBP_MorgenGame
--- @class MorgenMain : LuaUIBaseView @这是注释
local MorgenMain = ui("MorgenMain")
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local MorgenBtn = require "DFM.Business.Module.ActivityModule.UI.MorgenGame.MorgenBtn"
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
--#endregion


function MorgenMain:Ctor()

    self._wtRewardPanel = self:Wnd("DFCanvasPanel_1", UIWidgetBase)
    self._wtRewardBtn = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
    self._wtRewardBtn:Event("OnClicked", self.ShowRewardPop, self)
    self._wtBestLevel = self:Wnd("DFTextBlock_72", UITextBlock)
    self._wtBestLocation = self:Wnd("DFTextBlock", UITextBlock)

    self._wtPopBtnPanel = self:Wnd("DFCanvasPanel_2", UIWidgetBase)
    --- 右侧按钮
    self._wtPopBtn1 = self:Wnd("WBP_MorgenGame_Btn", MorgenBtn)
    self._wtPopBtn2 = self:Wnd("WBP_MorgenGame_Btn_1", MorgenBtn)
    self._wtPopBtn3 = self:Wnd("WBP_MorgenGame_Btn_2", MorgenBtn)
    self._wtPopBtn4 = self:Wnd("WBP_MorgenGame_Btn_3", MorgenBtn)

    self._wtReddotCanvas = self._wtPopBtn4:Wnd("wredDotTipsWidget", UIWidgetBase)
    self._wtRewardReddot = Module.ReddotTrie:CreateReddotIns(self._wtReddotCanvas)

    self.popBtnTabel = {
        [1] = self._wtPopBtn1,
        [2] = self._wtPopBtn2,
        [3] = self._wtPopBtn3,
        [4] = self._wtPopBtn4,
    }
    setmetatable(self.popBtnTabel, weakmeta)

    --- 点击回调
    self.PopBtnCallbacks = {
        [1] = CreateCallBack(self.ShowExploreLogPop, self),
        [2] = CreateCallBack(self.ShowEventLogPop, self),
        [3] = CreateCallBack(self.ShowSanityLogPop, self),
        [4] = CreateCallBack(self.ShowRewardPop, self),
    }

    --- 理智值部分
    self._wtSanityNum = self:Wnd("DFRichTextBlock_82", UITextBlock)

    self._wtSanityRuleBox = self:Wnd("wtCommonCheckInstruction", UIWidgetBase)
    self._wtSanityRuleBtn = self._wtSanityRuleBox:Wnd("DFCheckBox_Icon", UICheckBox)
    
    local fOnShowTips = CreateCallBack(self._OnHovered, self)
    local fOnHideTips = CreateCallBack(self._OnUnhovered, self)
    self._wtCommonCheckInstruction = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
    self._wtCommonCheckInstruction:SetCallback(self._OnCheckStateChanged, self)
    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_95", fOnShowTips, fOnHideTips, self)

    --- 主要按钮
    self._wtExploreBtn = self:Wnd("wtCommonButtonV1S2", DFCommonButtonOnly)
    self._wtStartGameBtn = self:Wnd("WBP_DFCommonButtonV1S3", DFCommonButtonOnly)
    self._wtExploreBtn:Event("OnClicked", self.OnExploreBtnClicked, self)
    self._wtExploreBtn:Event("OnDeClicked", self.OnExploreBtnDeClicked, self)
    self._wtStartGameBtn:Event("OnClicked", self.OnStartGameClicked, self)

    -------------------------------------------- 以上数据/以下控件 ------------------------------------------------

    -- 禁用滚动控件集合
    self._wtHotzone = {
        self._wtExploreBtn,
        self._wtStartGameBtn,
    }
    setmetatable(self._wtHotzone, weakmeta)
end

-----------------------------------------------------生命周期-----------------------------------------------------
--#region

function MorgenMain:OnInitExtraData(activityID)
    self._activityID = activityID
end

function MorgenMain:OnOpen()
    self:SetAllText()
    self:SetPopBtnData()

    local loadingUIIdList = {
        UIName2ID.MorgenExploreQuickPop
    }
    Facade.UIManager:PreloadLoadingUIAssetList(loadingUIIdList)

    Facade.ResourceManager:AsyncLoadResources(ActivityConfig.MorgenLoadingStub, ActivityConfig.MorgenSpineList, nil, self)
end

function MorgenMain:OnShowBegin()
    self:InitData()
    self:InitEvent()
    self:RefreshMainReddot()
end

function MorgenMain:OnShow()
    self:InitGamepad()
	self:ResetToMain()
	self:ShowSanityGainTip()
    self:PlayAnimation(self.WBP_MorgenGame_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, false)
end

function MorgenMain:OnHideBegin()
	self:DisableGamepad()
	self:RemoveAllLuaEvent()
end

function MorgenMain:OnHide()
    --鼠标事件移除
	if self._buttonownHandle then
		local gamelnst = GetGameInstance()
		UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._buttonownHandle)
		self._buttonownHandle = nil
	end
end

function MorgenMain:OnClose()
    self._wtHotzone = {}
    self.popBtnTabel = {}
end
--#endregion

-----------------------------------------------------数据处理-----------------------------------------------------
--#region

function MorgenMain:InitData()
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if activityInfo == nil or next(activityInfo) == nil then return end

    -- 基础数据
    self.allInfo = activityInfo.arknights_game_info or {}

    -- local info = {time = 1748422094, result = 3, cost_san_num = 100, gain_money_num = 200, cycle = 2, level = 1010013}
    -- self.allInfo.best_match_record = info

    self.curCycle = 1
    if self.allInfo.best_match_record.result and self.allInfo.best_match_record.result == 3 then
        self.curCycle = self.allInfo.best_match_record.cycle + 1
    elseif self.allInfo.best_match_record.time ~= 0 then
        self.curCycle = self.allInfo.best_match_record.cycle
    end

    self:RefreshUI()
    self:RefreshMainReddot()
end

--- 分数转化理智提示
function MorgenMain:ShowSanityGainTip()
    -- local info = {time = 1755592474, score = 1000000, san_num = 100, game_mode = 1}
    -- table.insert(self.allInfo.san_records, info)
    -- local info = {time = 1755592474, score = 1000000, san_num = 100, game_mode = 2}
    -- table.insert(self.allInfo.san_records, info)

    --- 去空
    for index, info in ipairs(self.allInfo.san_records) do
        if info.san_num == 0 then
            table.remove(self.allInfo.san_records, index)
        end
    end
    if self.allInfo.san_records and next(self.allInfo.san_records) then

        table.sort(self.allInfo.san_records, function(a, b) return a.time > b.time end)

        local lastUpdateTime = self.allInfo.last_san_update_time or 0
        local lastMoneyTime = self.allInfo.san_records[1].time or 0
        local totalSans = 0

        if lastUpdateTime < lastMoneyTime then
            for index, sanInfo in ipairs(self.allInfo.san_records) do
                if lastUpdateTime < sanInfo.time then
                    totalSans = totalSans + sanInfo.san_num
                else
                    break
                end
            end
        end
    
        if totalSans > 0 then
            local StoneImg = "Reason"
            local MoneyText = StringUtil.Key2StrFormat(Module.Activity.Config.Loc.CurrencyStr, {img = StoneImg, num = totalSans})
            Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.ArkGainStone..MoneyText)
            Server.ActivityServer:SendActivityTriggerReq(self._activityID)
        end
    end
end

function MorgenMain:SetPopBtnData()
    for i, Btn in ipairs(self.popBtnTabel) do
        Btn:RefreshInfo(self.PopBtnCallbacks[i], ActivityConfig.Loc.MogenPopBtnText[i], ActivityConfig.MogenPopBtnIcon[i])
    end
end
--#endregion

----------------------------------------------------- UI表现 -----------------------------------------------------
--#region

function MorgenMain:ResetToMain()
    self._wtCommonCheckInstruction:SetIsChecked(false, false)
end

function MorgenMain:RefreshUI()
    local transTable = {
        [1] = 1,
        [2] = 0,
        [3] = 2,
    }
    self:SetStyle(transTable[self.curCycle]) -- 0头像，1军牌，2记录

    if self.curCycle == 3 then
        local levelConfig = ConfigManager.GetLevelConfigTable()
        local curLevelInfo = levelConfig[self.allInfo.best_match_record.level] or {}

        -- self._wtRewardPanel:HitTestInvisible()

        self._wtBestLevel:SetText(ActivityConfig.Loc.MogenRewardText[5])
        self._wtBestLevel:SetText(ActivityLogic.HandleLocalizeText(curLevelInfo.levelName))
        self._wtBestLocation:SetText(ActivityLogic.HandleLocalizeText(curLevelInfo.levelSubName))
    end

	self._wtSanityNum:SetText(string.format(ActivityConfig.Loc.MorgenSanity, self.allInfo.san_num))
    --- 理智大于零才可用
    -- self._wtExploreBtn:SetBtnEnable(self.allInfo.san_num > 0)
    self._wtExploreBtn:SetIsEnabledStyle(self.allInfo.san_num > 0)
    
    
    self._wtExploreBtn:BP_SetMainTitle(ActivityConfig.Loc.MogenGameBtnText[1])
    -- self:SwitchExploreHandle(true)
    
    --- 进行一次游戏后才有快速探索
    -- if self.allInfo.best_match_record.time ~= 0 then
    -- else
    --     self._wtExploreBtn:SetBtnEnable(false)
    --     local LockImage = "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"lock2\" align=\"0\"/>"
    --     self._wtExploreBtn:BP_SetMainTitle(LockImage..ActivityConfig.Loc.MogenGameBtnText[1])
    --     self:SwitchExploreHandle(false)
    -- end
end

function MorgenMain:RefreshReddot()
end

function MorgenMain:EnableExplore()
    self._wtExploreBtn:SetBtnEnable(true)
end
--#endregion

-----------------------------------------------------响应操作-----------------------------------------------------
--#region

function MorgenMain:InitEvent()
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityInfoUpdate, self.InitData, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTaskChange, self.InitData, self)
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityTriggerRes, self.UpdateSanityTime, self)

    -- 通用指令
    self:AddLuaEvent(Module.Activity.Config.evtActivityGeneralCommand, self.EnableExplore, self)

    local gamelnst = GetGameInstance()
    self._buttonownHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
end

--- 打开界面时的理智获取提示
function MorgenMain:UpdateSanityTime(activityID, activity_info)
    if self._activityID == activityID then
        self.allInfo.last_san_update_time = activity_info.last_san_update_time
    end
end

function MorgenMain:_OnMouseButtonDown()
    self:ShowSanityRuleTip(false)
end

--- 开始游戏
function MorgenMain:OnStartGameClicked()
    local morgenModuleName = "Event10001Only"
    if LiteDownloadManager:IsHDDownloadStatus() then
        morgenModuleName = LiteDownloadManager:GetHDRuntimeNameByModuleName(morgenModuleName)
        loginfo("MorgenMain.OnStartGameClicked GetHDRuntimeNameByModuleName morgenModuleName:"..morgenModuleName)
    end
    local bDownloaded = LiteDownloadManager:IsDownloadedByModuleName(morgenModuleName)
	loginfo("MorgenMain.OnStartGameClicked", bDownloaded)
	if bDownloaded then
        self:SendMagicTowerStartReq()
        Module.Activity:OpenMagicTowerLevelLoading(self._activityID)
	else
		local function fOnConfirm()
			loginfo("MorgenMain.OnStartGameClicked Confirm download.")
			LiteDownloadManager:DownloadByModuleName(morgenModuleName)

			Module.LitePackage:ShowMainPanel()
		end

		local pakTotalSize = LiteDownloadManager:GetTotalSizeByModuleName(morgenModuleName)
		local pakNowSize = LiteDownloadManager:GetNowSizeByModuleName(morgenModuleName)
		local pakTotalSizeMBStr = string.format("%.1f", (pakTotalSize - pakNowSize) / 1024 / 1024)
		local showStr = StringUtil.Key2StrFormat(ActivityConfig.Loc.MorgenDownloadTips, {["PakSize"] = pakTotalSizeMBStr})
		Module.CommonTips:ShowConfirmWindow(showStr, fOnConfirm)
	end
end

function MorgenMain:SendMagicTowerStartReq()
    if not self._activityID or self._activityID == 0 then return end

    Server.ActivityServer:SendMagicTowerStartReq(self._activityID)
end

--- 快速探索
function MorgenMain:OnExploreBtnGamePad()
    if self.allInfo.san_num > 0 then
        self:OnExploreBtnClicked()
    else
        self:OnExploreBtnDeClicked()
    end
end

--- 快速探索
function MorgenMain:OnExploreBtnClicked()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenExploreQuickPop, function() end, self, self._activityID, self.allInfo.san_num)
end

--- 快速探索
function MorgenMain:OnExploreBtnDeClicked()
    Module.CommonTips:ShowSimpleTip(ActivityConfig.Loc.MorgenLackSanity)
end
    
function MorgenMain:ShowSanityRuleTip(bShow)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtDFTipsAnchor)
        self._tipHandle = nil
    end

    if bShow then
        self._tipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(ActivityConfig.Loc.MogenGameBtnText[3], self._wtDFTipsAnchor)
    else
        if self._wtCommonCheckInstruction then
            self._wtCommonCheckInstruction:SetIsChecked(false, false)
        end
    end
end

function MorgenMain:_OnCheckStateChanged(bChecked)
    self:ShowSanityRuleTip(bChecked)
end

function MorgenMain:_OnHovered()
    self:ShowSanityRuleTip(true)
end

function MorgenMain:_OnUnhovered()
    self:ShowSanityRuleTip(false)
end

--- 四个pop界面
function MorgenMain:ShowRewardPop()
    if not next(self.allInfo.rewards) and not VersionUtil.IsShipping() then
        Module.CommonTips:ShowSimpleTip("rewards not valid")
        return
    end
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenRewardPop, function() end, self, self._activityID, 
        self.allInfo.rewards, self.curCycle, self.allInfo.gain_money_num, self.allInfo.meet_reward_npc)
end

function MorgenMain:ShowExploreLogPop()
    -- local info = {time = 1748422094, result = 3, cost_san_num = 100, gain_money_num = 200, cycle = 2, level = 6, is_auto = true, location = "123"}
    -- table.insert(self.allInfo.match_records, info)
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenExploreLogPop, function() end, self, self.allInfo.match_records)
end

function MorgenMain:ShowEventLogPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenEventLogPop, function() end, self, self.curCycle)
end

function MorgenMain:ShowSanityLogPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.MorgenSanityPop, function() end, self, self.allInfo.san_records)
end
--#endregion

-----------------------------------------------------其他函数-----------------------------------------------------
--#region

function MorgenMain:RefreshMainReddot()
    self:SetReddot(self._wtRewardReddot, Server.ActivityServer:GetReddotStateByMorgenGame({arknights_game_info = self.allInfo}))
end

function MorgenMain:SetReddot(reddot, bVisible)
    if reddot then
        if bVisible then
            reddot:SetReddotVisible(true, EReddotType.Normal)
        else
            reddot:SetReddotVisible(false)
        end
    end
end

function MorgenMain:SetAllText()
    self._wtExploreBtn:BP_SetMainTitle(ActivityConfig.Loc.MogenGameBtnText[1])
    self._wtStartGameBtn:BP_SetMainTitle(ActivityConfig.Loc.MogenGameBtnText[2])
end

function MorgenMain:NavBack()
end

function MorgenMain:InitGamepad()
    if not IsHD() then return end
    if not self:IsVisible() then return end
    self:DisableGamepad()

    if not self._popBtn1NavGroup then
        self._popBtn1NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtPopBtn1, self, "Hittest")
        self._popBtn2NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtPopBtn2, self, "Hittest")
        self._popBtn3NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtPopBtn3, self, "Hittest")
        self._popBtn4NavGroup = WidgetUtil.RegisterNavigationGroup(self._wtPopBtn4, self, "Hittest")
        if self._popBtn1NavGroup then
            self._popBtn1NavGroup:AddNavWidgetToArray(self._wtPopBtn1)
            self._popBtn2NavGroup:AddNavWidgetToArray(self._wtPopBtn2)
            self._popBtn3NavGroup:AddNavWidgetToArray(self._wtPopBtn3)
            self._popBtn4NavGroup:AddNavWidgetToArray(self._wtPopBtn4)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._popBtn1NavGroup)
        end
    end

    if not self._startGameHandle then
        self._startGameHandle = self:AddInputActionBinding("Common_ButtonLeft_Gamepad", EInputEvent.IE_Pressed, self.OnStartGameClicked, self, EDisplayInputActionPriority.UI_Stack)
    	self._wtStartGameBtn:SetDisplayInputAction("Common_ButtonLeft_Gamepad", true, nil, true)
    end

    self:SwitchExploreHandle(true)
    
    local gamePadInputList = {
        {actionName = "SanityUse", func = function()
            self._wtCommonCheckInstruction:SelfClick()
        end, caller = self, bUIOnly = false},
    }
    
    Module.CommonBar:SetBottomBarTempInputSummaryList(gamePadInputList)
end

function MorgenMain:SwitchExploreHandle(bIsOpen)
    if bIsOpen then
        --- 长按
        if not self._exploreHandle then
            self._exploreHandle = self:AddHoldInputActionBinding("ReceiveAll_Gamepad", self.OnExploreBtnGamePad, self, EDisplayInputActionPriority.UI_Stack)
            self._wtExploreBtn:SetDisplayInputActionWithLongPress(self._exploreHandle, self, "ReceiveAll_Gamepad", true, nil, true)
            self._wtExploreBtn:SetKeyIconVisibility(true)
        end
    else
        self._wtExploreBtn:SetKeyIconVisibility(false)
        if self._exploreHandle then
            self:RemoveInputActionBinding(self._exploreHandle)
            self._exploreHandle = nil
        end
    end
end

function MorgenMain:DisableGamepad()
    if not IsHD() then return end

    if self._bookScrollNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._bookScrollNavGroup = nil
    end

    if self._startGameHandle then
        self:RemoveInputActionBinding(self._startGameHandle)
        self._startGameHandle = nil
    end

    if self._exploreHandle then
        self:RemoveInputActionBinding(self._exploreHandle)
        self._exploreHandle = nil
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._popBtn1NavGroup = nil
    self._popBtn2NavGroup = nil
    self._popBtn3NavGroup = nil
    self._popBtn4NavGroup = nil

    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

--#endregion
-----------------------------------------------------  END  -----------------------------------------------------

return MorgenMain