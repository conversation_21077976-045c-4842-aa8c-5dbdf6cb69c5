----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHUD)
----- LOG FUNCTION AUTO GENERATE END -----------



local SOLKillFeedbackSpecialInfoView = hud("SOLKillFeedbackSpecialInfoView")
local HudConfig = require "DFM.Business.Module.HUDModule.HUDConfig"
local EGameHUDState = import "GameHUDSate"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local UGameplayStatics = import "GameplayStatics"
local ADFMCharacter = import "DFMCharacter"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local ULAI = import "LAI"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local FAnchors = import "Anchors"
local Queue=require("DFM.YxFramework.Core.Library.deque")

local function log(...)
    --loginfo("[SOLKillFeedbackSpecialInfoView]", ...)
end

--- 行为状态
local Behavior = {
    ---空闲
    Idle = "Idle",
    ---初始化
    Init = "Init",
    ---等待展示
    Wait = "Wait",
    ---淡入
    FadeIn = "FadeIn",
    ---淡入中
    FadeInKeep = "FadeInKeep",
    ---展示中
    Show = "Show",
    ---淡出
    FadeOut ="FadeOut",
    ---检查淡出
    FadeOutKeep = "FadeOutKeep",
    ---退出
    Exit = "Exit"
}

local SubBehavior = {
    Idle = {
        Entry = "Idle.Entry",
        ---退出
        Exit = "Idle.Exit"
    },
    Show = {
        ---进入
        Entry = "Show.Entry",
        ---静态展示
        Idle = "Show.Idle",
        ---移到右边
        Move = "Show.Move",
        ---检查移动完成
        MoveKeep = "Show.MoveKeep",
        ---退出
        Exit = "Show.Exit",
        --直接退出，moveout后直接Behavior.Exit
        DirectlyExit = "Show.DirectlyExit",
    }
}

--- 配置
local Config = {
    --- 延迟播放 等待IconView时间
    WaitIconViewTime = 0,
    --- 淡入中时间
    FadeInKeepTime = 0.2333,
    --- 最大显示停留时间
    ShowKeepTime = 2.4333,
    --- 向下移动时间
    MoveTime = 0.2333,
    --- 向下移动距离
    MoveDistance = 46,
    --- 淡出时间
    FadeOutKeepTime = 0.4,

    -- 远距离击杀阈值
    FarKillDis = 30,
}

--- 获取积分动效HUD
function SOLKillFeedbackSpecialInfoView:Ctor()
    Config.ShowKeepTime = self.ShowKeepTime
    Config.FarKillDis = self.FarKillDis

    ---当前状态
    self.CurrentBehavior = Behavior.Idle
    ---当前子状态
    self.CurrentSubBehavior = SubBehavior.Idle.Entry

    ---时间记录表 {Behavior:TimeStamp}
    self.BehaviorTimeRecord = {}
    ---时间记录表 {Behavior:{{SubBehavior:TimeStamp}}}
    self.SubBehaviorTimeRecord = {}

    --- 初始坐标X
    self.DefaultX = 0

    ---需要右移次数
    self.NeedMove = 0

    -- 设置显隐规则
    self:_InitInVisibleGameHudState()

    self.DistanceText = self:Wnd("AddScore_1", UITextBlock)
    self.DistanceTextBox = self:Wnd("HorizontalBox_2", UIWidgetBase)
    self.KillTeamText = self:Wnd("DFTextBlock_166", UITextBlock)
    self.SelfRescueText = self:Wnd("DFTextBlock_Self", UITextBlock)
    self.FarDescText = self:Wnd("FarDesc", UITextBlock)

    ---当前阶段使用的Move动画
    self.MoveAnimation = nil
    ---当前阶段使用的FadeOut动画
    self.FadeOutAnimation = nil
end

-- 位置设置
function SOLKillFeedbackSpecialInfoView:InitPos(InPosSetting_PC, InPosSetting_Mobile)
    if DFHD_LUA == 1 then
        self.PosSetting = InPosSetting_PC
        local scale = self.PosSetting.ScaleToMobile * self.PosSetting.FontScaleToMobile
        self:SetRenderScale(FVector2D(scale, scale))
    else
        self.PosSetting = InPosSetting_Mobile
    end
end

--- 创建出来之后初始化
function SOLKillFeedbackSpecialInfoView:InitUI()
    for _, behavior in pairs(Behavior) do
        self.BehaviorTimeRecord[behavior] = 0

        if SubBehavior[behavior] then
            if self.SubBehaviorTimeRecord[behavior] == nil then
                self.SubBehaviorTimeRecord[behavior] = {}
            end

            for _,subBehavior in pairs(SubBehavior[behavior]) do
                self.SubBehaviorTimeRecord[behavior][subBehavior] = 0
            end
        end
    end

    ---设置状态为：初始化
    self.CurrentBehavior = Behavior.Init
    self.CurrentSubBehavior = nil
end

---设置文字内容
---@param meter number 距离
function SOLKillFeedbackSpecialInfoView:SetContent(meter, bKillTeam, bShowSelfRescue)

    if self.SelfRescueText then

        if bShowSelfRescue then
            self.SelfRescueText:Visible()
            self.KillTeamText:Hidden()
            self.DistanceTextBox:Hidden()
            return
        end
    
        self.SelfRescueText:Hidden()
        
    end
   
    if bKillTeam then
        self.DistanceTextBox:Hidden()

        self.KillTeamText:Visible()
        self.KillTeamText:SetColorAndOpacity(self.KillTeamColor)
    else
        self.KillTeamText:Hidden()

        if meter >= Config.FarKillDis then
            self.DistanceTextBox:Visible()
            self.FarDescText:SetColorAndOpacity(self.FarDisColor)

            self.DistanceText:SetText(tostring(meter) .. Module.WeaponAssembly.Config.Loc.Meter)
        else
            self.DistanceTextBox:Hidden()
        end
    end
end

--- 设置是否需要移动(当事件队列有多个事件排队时)
function SOLKillFeedbackSpecialInfoView:SetNeedMove(bNeedMove)
    if bNeedMove then
        self.NeedMove = self.NeedMove + 1
    else
        self.NeedMove = self.NeedMove - 1
        assert(self.NeedMove >= 0)
    end
end

---播放Animation
---@param animation any
---@param bPlay boolean true:播放 false:停止播放 默认true
function SOLKillFeedbackSpecialInfoView:Play(animation, bPlay)
    if bPlay then
        self:PlayAnimation(animation, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:StopAnimation(animation)
    end
end

--- 播放动画：刚创建出来 从大变小
function SOLKillFeedbackSpecialInfoView:PlayAnimation_FadeIn(bPlay)
    self:Play(self.Anima_DistanceMoveIn, bPlay)
end

--- 播放动画：向下移动 有新的Icon创建出来了
function SOLKillFeedbackSpecialInfoView:PlayAnimation_Move(bPlay)
    if bPlay then
        self:Play(self.MoveAnimation, true)
    else
        self.HorizontalBox_0:SetRenderTranslation(FVector2D(0, 0))
    end
end

--- 播放动画：渐隐消失
function SOLKillFeedbackSpecialInfoView:PlayAnimation_FadeOut(bPlay)
    self:Play(self.Anima_DistanceFadeOut, bPlay)
end

function SOLKillFeedbackSpecialInfoView:Update(dt)
    ---记录当前状态持续时间
    self.BehaviorTimeRecord[self.CurrentBehavior] = (self.BehaviorTimeRecord[self.CurrentBehavior] or 0) + dt
    if self.SubBehaviorTimeRecord[self.CurrentBehavior] then
        self.SubBehaviorTimeRecord[self.CurrentBehavior][self.CurrentSubBehavior] = (self.SubBehaviorTimeRecord[self.CurrentBehavior][self.CurrentSubBehavior] or 0) + dt
    end

    ---状态切换
    if self.CurrentBehavior == Behavior.Idle then
        
    end

    if self.CurrentBehavior == Behavior.Init then
        self:Hidden()
        local itemAnchor = FAnchors()
        itemAnchor.Minimum = FVector2D(0.5, 1)
        itemAnchor.Maximum = FVector2D(0.5, 1)
        self.Slot:SetAnchors(itemAnchor)
        self.Slot:SetAlignment(FVector2D(0.5, 0))

        --新创建的排在上面
        self.Slot:SetPosition(FVector2D(0, self.PosSetting.SpecialInfoYOffset))

        self.CurrentBehavior = Behavior.Wait
    end

    if self.CurrentBehavior == Behavior.Wait then
        if self.BehaviorTimeRecord[Behavior.Wait] >= Config.WaitIconViewTime then
            self.CurrentBehavior = Behavior.FadeIn
            --在Main里面判断当前FadeIn，驱动之前的Move
            return
        end
    end

    if self.CurrentBehavior == Behavior.FadeIn then
        self:Visible()

        self:PlayAnimation_FadeIn(true)

        self.CurrentBehavior = Behavior.FadeInKeep
    end

    if self.CurrentBehavior == Behavior.FadeInKeep then
        ---FadeIn时间超过配置时间,转换到下一状态:Show
        if self.BehaviorTimeRecord[Behavior.FadeInKeep] >= Config.FadeInKeepTime then
            self.CurrentBehavior = Behavior.Show
            self.CurrentSubBehavior = SubBehavior.Show.Entry
        end
    end

    if self.CurrentBehavior == Behavior.Show then
        if self.CurrentSubBehavior == SubBehavior.Show.Entry then
            if self.NeedMove > 0 then
                self.CurrentSubBehavior = SubBehavior.Show.Move
            else
                self.CurrentSubBehavior = SubBehavior.Show.Idle
            end
        end

        if self.CurrentSubBehavior == SubBehavior.Show.Idle then
            ---Show时间超过配置最长保持时间
            if self.BehaviorTimeRecord[Behavior.Show] < Config.ShowKeepTime then
                if self.NeedMove > 0 then
                    self.CurrentSubBehavior = SubBehavior.Show.Move
                end
            else
                --@退出Show
                self.CurrentSubBehavior = SubBehavior.Show.Exit
            end
        end

        if self.CurrentSubBehavior == SubBehavior.Show.Move then
            self.MoveAnimation = self.Anima_DistanceMoveOut
            self:PlayAnimation_Move(true)

            self.CurrentSubBehavior = SubBehavior.Show.MoveKeep
        end

        if self.CurrentSubBehavior == SubBehavior.Show.MoveKeep then
            if self.SubBehaviorTimeRecord[self.CurrentBehavior][self.CurrentSubBehavior] >= Config.MoveTime then
                if self:IsAnimationPlaying(self.MoveAnimation) == false then
                    self:PlayAnimation_Move(false)
                    ---Slot向下移动46
                    local oldPosition = self.Slot:GetPosition()
                    self.Slot:SetPosition(FVector2D(oldPosition.X, oldPosition.Y + Config.MoveDistance))
                    
                    self:SetNeedMove(false)
    
                    self.CurrentSubBehavior = SubBehavior.Show.DirectlyExit
                    self.CurrentBehavior = Behavior.Exit
                end
            end
        end

        if self.CurrentSubBehavior == SubBehavior.Show.Exit then
            self.CurrentBehavior = Behavior.FadeOut

            --这里return，等下一帧处理，让下一帧开始，MainView通知其他InfoView进入Move状态
            return
        end
    end

    if self.CurrentBehavior == Behavior.FadeOut then
        self.FadeOutAnimation = self.Anima_DistanceFadeOut
        self:PlayAnimation_FadeOut(true)

        self.CurrentBehavior = Behavior.FadeOutKeep
    end

    if self.CurrentBehavior == Behavior.FadeOutKeep then
        if self.BehaviorTimeRecord[Behavior.FadeOutKeep] >= Config.FadeOutKeepTime then
            self.CurrentBehavior = Behavior.Exit
        end
    end

    if self.CurrentBehavior == Behavior.Exit then
        self:Hidden()
    end
end

function SOLKillFeedbackSpecialInfoView:IsInFadeInState()
    return self.CurrentBehavior == Behavior.FadeIn
end

---是否在Show状态
function SOLKillFeedbackSpecialInfoView:IsInShowState()
    return self.CurrentBehavior == Behavior.Show
end

---是否在Show.Idle状态
function SOLKillFeedbackSpecialInfoView:IsInShowIdleState()
    return self.CurrentBehavior == Behavior.Show and self.CurrentSubBehavior == SubBehavior.Show.Idle
end

function SOLKillFeedbackSpecialInfoView:IsInShowIdleOrExitState()
    return (self.CurrentBehavior == Behavior.Show and self.CurrentSubBehavior == SubBehavior.Show.Idle)
        or (self.CurrentBehavior == Behavior.Exit)
end

---是否在Move状态
function SOLKillFeedbackSpecialInfoView:IsInMoveState()
    return self.CurrentBehavior == Behavior.Show and (self.CurrentSubBehavior == SubBehavior.Show.Move or self.CurrentSubBehavior == SubBehavior.Show.MoveKeep)
end

---切换状态:Move (仅Show状态)
function SOLKillFeedbackSpecialInfoView:Move()
    self:SetNeedMove(true)
end

---是否处于FadeOut状态
function SOLKillFeedbackSpecialInfoView:IsInFadeOutState()
    return self.CurrentBehavior == Behavior.FadeOut
end

---是否处于FadeOutKeep状态
function SOLKillFeedbackSpecialInfoView:IsInFadeOutKeepState()
    return self.CurrentBehavior == Behavior.FadeOutKeep
end

---是否处于Exit状态
function SOLKillFeedbackSpecialInfoView:IsInExitState()
    return self.CurrentBehavior == Behavior.Exit
end

function SOLKillFeedbackSpecialInfoView:_InitInVisibleGameHudState()

end

function SOLKillFeedbackSpecialInfoView:_InitCallbacks()
    for i = 1, #self.callbacks do
        local callback = self.callbacks[i]
        callback[3] = callback[1]:Add(CreateCallBack(callback[2], self))
    end
end

function SOLKillFeedbackSpecialInfoView:Destroy()
    
end

return SOLKillFeedbackSpecialInfoView