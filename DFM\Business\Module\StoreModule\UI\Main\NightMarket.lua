----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class NightMarket : LuaUIBaseView
local NightMarket = ui("NightMarket")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"

function NightMarket:Ctor()
    self._NMCanvas = self:Wnd("DFCanvasPanel_20", UIWidgetBase)
    self._NMShowList = self:Wnd("WBP_Store_NM_ShowList", UIWidgetBase)

    self._SearchBtn = self:Wnd("WBP_DFCommonButtonV1S3", DFCommonButtonOnly)
    self._SearchBtn:Event("OnClicked", self.OnSearchBtnClick, self)

    self._wtTipBtn = self:Wnd("wtCommonCheckInstruction", UIButton)
    self._wtTipBtn:Event("OnCheckStateChanged", self.OnTipBtnClick, self)

    self._wtName = self:Wnd("wtTextBundleName", UITextBlock)
    self._wtCountWidget = self:Wnd("wtCountDown", UIWidgetBase)
    if self._wtCountWidget ~= nil then
        self._wtTextCountDown = self._wtCountWidget:Wnd("wtTextCountDown", UITextBlock)
    end
    self._wtTip = self:Wnd("wtTextTip", UITextBlock)
end

function NightMarket:OnInitExtraData()
    self.needPlayBgm = true
    self._wtName:SetText(LocalizeTool.GetTransStr("LOC_StoreMainTab,LuckyNest_TabName"))
    self._SearchBtn:SetMainTitle(LocalizeTool.GetTransStr(StoreConfig.Loc.NightMarketSearch))
    self._wtTip:SetText(LocalizeTool.GetTransStr(StoreConfig.Loc.NightMarketMianTabTip))
end

function NightMarket:OnOpenBegin()

end

function NightMarket:OnOpen()
end

function NightMarket:ChangeBtn()
    self._SearchBtn:SetIsEnabled(true)
end

function NightMarket:OnShowBegin()
    self:AddLuaEvent(Module.Store.Config.evtStoreNMMainShow, self.ShowMain, self)
    self:AddLuaEvent(LuaGlobalEvents.evtSceneLoaded,self.OnRefreshModel, self)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() and not Server.StoreServer:GetNMHasSearched() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION

    if not Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "IsChooseNest") then
        self._SearchBtn:SetIsEnabled(false)
    end

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_LuckyNest_In)

    if self.needPlayBgm then
        if Module.Store:GetCanPlayMusic() then
            if Server.StoreServer:IsHiddenBoxRemain() then
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_LuckyNest_Music_GoldenEgg)
            else
                Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_LuckyNest_Music_Normal)
            end
        end
        self.needPlayBgm = false
    end

    Module.Store:SetCanPlayMusic(true)

    if Server.StoreServer:GetNMHasSearched() then
        self.goods = Server.StoreServer:GetLuckyNestGoods()
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "HideNest") --隐藏鸟窝
        self._NMCanvas:Collapsed()
        self._NMShowList:Visible()
        self._NMShowList:SetCard(self.goods)
    else
        self._NMShowList:Collapsed()
        self._NMCanvas:Visible()
    end

    local fAllLevelFinishCallback = CreateCallBack(function()
        Facade.GameFlowManager:EnterSubStage(ESubStage.LuckyNest)
    end,self)
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.LuckyNest, true, nil, fAllLevelFinishCallback, false, 30)
end

function NightMarket:OnShow()
    if Server.StoreServer:GetNMHasSearched() then
        LogAnalysisTool.DoSendStoreViewPageReportLog(13, 2, 0, 0)
    else
        LogAnalysisTool.DoSendStoreViewPageReportLog(13, 1, 0, 0)
    end
end

function NightMarket:OnSearchBtnClick()
    Module.CommonBar:SetTopAndSecPanVisible(false)
    self.isSearchBtnClick = true
    self:Hide()

    if not Server.StoreServer:GetNMHasSearched() then
        Server.StoreServer:SendShopRaffleLuckyNestReq()
    end

    Module.Store:SetCanPlayMusic(false)

    -- 播放演绎视频
    self:PlaySearchVedio()
end

function NightMarket:OnTipBtnClick()
    local itemDescDataList = {}
    table.insert(itemDescDataList, LocalizeTool.GetTransStr("LOC_StoreLuckyNest,LuckyNest_Rules"))
    Module.CommonWidget:ShowCommonPopWindowV2Waterfall(nil, nil, StoreConfig.Loc.StaffLotteryPopTitle, 
    nil, itemDescDataList, true)
end

function NightMarket:PlaySearchVedio()
    local nestId = Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "GetSelectedNestId")
    local eggId = Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "GetSelectedEggId")
    LogAnalysisTool.DoSendLuckyNestSelectReportLog(nestId, eggId)
    Facade.HallSceneManager:FoceCallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "PlayDiggingNestSeq")
end

function NightMarket:ShowMain()
    self:Show()
end

function NightMarket:OnHideBegin()
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest,"OnDisplayCtrlUnInit")

    if Module.Store:GetCanPlayMusic() == true and not self.isSearchBtnClick then
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_LuckyNest_Out) 
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_LuckyNest_Music_Stop)
    end
    self.isSearchBtnClick = false

    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function NightMarket:OnHide()
end

function NightMarket:OnClose()
    self:RemoveLuaEvent(Module.Store.Config.evtStoreNMMainShow, self.ShowMain, self)
    local ctrl =  Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.LuckyNest)
    if ctrl then
        ctrl.OnPlayDiggingNestSeqFinishedEvent:Clear()
        ctrl.OnOnPlayFallingEggSeqFinishedEvent:Clear()
    end
end

function NightMarket:OnRefreshModel(curSubStageType)
    if curSubStageType == ESubStage.LuckyNest then 
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest,"OnDisplayCtrlUnInit")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest,"OnDisplayCtrlInit")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "SetDisplayType", "Main")
        local ctrl =  Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.LuckyNest)
        if ctrl then
            ctrl.OnPlayDiggingNestSeqFinishedEvent:Clear()
            ctrl.OnOnPlayFallingEggSeqFinishedEvent:Clear()
            ctrl.OnSelectNest:Clear()
            ctrl.OnSelectNest:Add(CreateCPlusCallBack(self.ChangeBtn, self))
            ctrl.OnPlayDiggingNestSeqFinishedEvent:Add(CreateCPlusCallBack(self.PlayDiggingNestSeqFinished, self))
            ctrl.OnOnPlayFallingEggSeqFinishedEvent:Add(CreateCPlusCallBack(self.PlayFallingEggSeqFinished, self))
        end

    end
end

function NightMarket:PlayDiggingNestSeqFinished()
    -- if not Server.StoreServer:IsHiddenBoxRemain() then
    --     Module.CommonBar:SetTopAndSecPanVisible(true)
    -- end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.LuckyNest, "SetDisplayType", "Main")
    -- 打开商品搜索弹窗蓝图
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreNMShowPop, nil, nil)
end

function NightMarket:PlayFallingEggSeqFinished()
    Module.CommonBar:SetTopAndSecPanVisible(true)
    Module.Store.Config.evtStoreNMMainShow:Invoke()
    local nmBonusItems = Server.StoreServer:GetNMBonusItems()
    local itemList = {}
    for index, value in ipairs(nmBonusItems) do
        if value.prop then
            local item = ItemHelperTool.CreateItemByPropInfo(value.prop)
            table.insert(itemList, item)
        end
    end
    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
    Module.Store:SetCanPlayMusic(true)
end

function NightMarket:RefreshTime()
    local timestamp = Server.StoreServer:GetNMEndTime()
    if timestamp and timestamp > 0 then
        self.offlineSeconds = TimeUtil.GetLocalRemainTime2Seconds(timestamp)
        local showStr = ""
        local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(self.offlineSeconds)
        if self.offlineSeconds > 3600 * 24 then
            showStr = TimeUtil.GetSecondsFormatDDHHMMSSString(self.offlineSeconds, "DDHH_Second")
        elseif self.offlineSeconds > 3600 then
            showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeHourTip, hour, min)
        else
            showStr = string.format(StoreConfig.Loc.StoreBuyLimitTimeMinSecTip, min, sec)
        end

        self._wtTextCountDown:SetText(showStr)
    end
end

function NightMarket:_EnableGamepadFeature()

    -- 配置keyIcon
    if self._SearchBtn then
        self._SearchBtn:SetDisplayInputAction("MallPurchase", true, nil, true)
    end

    -- 配置输入
    if not self._Purchase then
        self._Purchase = self:AddInputActionBinding(
        "MallPurchase", 
        EInputEvent.IE_Pressed, 
        self.OnSearchBtnClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    self:_SetBottomBar()
end

function NightMarket:_SetBottomBar()
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    --显示按键提示
    local summaryList ={}

    table.insert(summaryList, {actionName = "Select", func = nil, caller = self, bUIOnly = false, bHideIcon = false})
    table.insert(summaryList, {actionName = "Assembly_Confirm",func = self.OnTipBtnClick, caller = self ,bUIOnly = false, bHideIcon = false})

    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function NightMarket:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.RemoveNavigationGroup(self)

    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end

end


return NightMarket