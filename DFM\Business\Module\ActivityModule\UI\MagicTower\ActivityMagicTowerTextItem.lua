----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

--朋友圈
---@class ActivityMagicTowerTextItem : LuaUIBaseView
local ActivityMagicTowerTextItem = ui("ActivityMagicTowerTextItem")

function ActivityMagicTowerTextItem:Ctor()
    self._wtText = self:Wnd("DFComputeTextBlock"    , UITextBlock)
end

function ActivityMagicTowerTextItem:InitData(text)
    self._wtText:SetText(text)
end

function ActivityMagicTowerTextItem:OnShowBegin()
end

function ActivityMagicTowerTextItem:OnHideBegin()
end

function ActivityMagicTowerTextItem:OnClose()
end

function ActivityMagicTowerTextItem:PlayAnim()
    self._wtText:PlayAnim_ComputeTextBlock()
end

return ActivityMagicTowerTextItem