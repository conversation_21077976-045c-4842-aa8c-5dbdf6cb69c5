----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPostLaunch)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class PostLaunchWaitPanel : LuaUIBaseView
local PostLaunchWaitPanel = ui("PostLaunchWaitPanel")
local PostLaunchLogic = require("DFM.Business.Module.PostLaunchModule.Logic.PostLaunchLogic")
local UDFMPSOCacheSystem = PostLaunchLogic.GetUDFMPSOCacheSystem()
local EPSOCachePrecompileState = import "EPSOCachePrecompileState"
local Field = Module.PostLaunch.Field
local Config = Module.PostLaunch.Config

local log = function(...) print("[PostLaunchWaitPanel] ", ...) end

function PostLaunchWaitPanel:Ctor()
    self._stageIndex = 0
    self._binded = false
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Hidden)
    self._wtMediaImage = self:Wnd("BgMediaImg", UIMediaImage)
    self._wtMediaImage:Play(Module.PostLaunch.Config.BGMediaRowName)
end

function PostLaunchWaitPanel:_OnPrecompileAllPSOFinish()
    if self._binded then
        UDFMPSOCacheSystem.Get().OnPrecompileAllPSOFinish:Remove(self._OnPrecompileAllPSOFinish, self)
    end
    Timer.DelayCall(0.5, function()
        PostLaunchLogic.NextPanelProcess()
    end)
    -- 锁定输入，如果有的话
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function PostLaunchWaitPanel:OnOpen()
    self._stageIndex = Module.PostLaunch.Field.nextStageIndex
    PostLaunchLogic.OnStagePanelOpen(self)

    if Field.shouldPrecompileAll and Field.cachedPSOPrecompileState ~= EPSOCachePrecompileState.PrecompiledAll then
        if PostLaunchLogic.RunPSOTaskScheduler() then
            Module.CommonBar:RegStackUIInputSummary(self, {{actionName = "BackgroundPSO", func = self._BackgroundPSO, caller = self}})
            --一旦进入这个界面，就要全速进行预热
            local taskScheduler = UDFMPSOCacheSystem.Get():GetTaskScheduler()
            if taskScheduler then
                taskScheduler:Fullspeed()
            end
        end
    end
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function PostLaunchWaitPanel:OnClose()
    self:RemoveAllLuaEvent()
    self._wtMediaImage:StopMedia(true)
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function PostLaunchWaitPanel:OnShow()
    PostLaunchLogic.OnStagePanelShow(self)

    if Config.NotWaitPSO then
        self:_OnPrecompileAllPSOFinish()
        log("Skip wait PSO")
        return
    end

    if Field.shouldPrecompileAll and Field.cachedPSOPrecompileState ~= EPSOCachePrecompileState.PrecompiledAll then
        UDFMPSOCacheSystem.Get().OnPrecompileAllPSOFinish:Add(self._OnPrecompileAllPSOFinish, self)
        self._binded = true
    else
        self:_OnPrecompileAllPSOFinish()
        -- 锁定输入，如果有的话
    end
end

---@overload fun(LuaUIBaseView, OnHide)
function PostLaunchWaitPanel:OnHide()
    if self._binded then
        UDFMPSOCacheSystem.Get().OnPrecompileAllPSOFinish:Remove(self._OnPrecompileAllPSOFinish, self)
        self._binded = false
    end
end

function PostLaunchWaitPanel:_DoBackgroundPSO()
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.DFDFBackgroundPSOTask 1", nil)
    UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "pso.EnablePrecompileAllOtherQuality 1", nil)
    --一旦离开这个界面，就要自动调度
    local taskScheduler = UDFMPSOCacheSystem.Get():GetTaskScheduler()
    if taskScheduler then
        taskScheduler:AutoSchedule(true)
    end

    self:_OnPrecompileAllPSOFinish()
end

function PostLaunchWaitPanel:_BackgroundPSO()
    local warningText = Module.PostLaunch.Config.Loc.BackgroundPSOWarning
    local confirmText = Module.PostLaunch.Config.Loc.BackgroundPSOConfirm
    local cancelText = Module.PostLaunch.Config.Loc.BackgroundPSOCancel

    Module.CommonTips:ShowConfirmWindow(warningText, CreateCallBack(self._DoBackgroundPSO, self), nil, cancelText, confirmText)
end

return PostLaunchWaitPanel
