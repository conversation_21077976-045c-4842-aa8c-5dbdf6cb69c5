require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_rank_editor_pb"
end

pb.__pb_RankList = {
    season_no = 0,
}
pb.__pb_RankList.__name = "RankList"
pb.__pb_RankList.__index = pb.__pb_RankList
pb.__pb_RankList.__pairs = __pb_pairs

pb.RankList = { __name = "RankList", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankList : ProtoBase
---@field public season_no number
---@field public items pb_RankItem[]

---@return pb_RankList
function pb.RankList:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankItemExData = {
    value = 0,
}
pb.__pb_CSRankItemExData.__name = "CSRankItemExData"
pb.__pb_CSRankItemExData.__index = pb.__pb_CSRankItemExData
pb.__pb_CSRankItemExData.__pairs = __pb_pairs

pb.CSRankItemExData = { __name = "CSRankItemExData", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankItemExData : ProtoBase
---@field public value number

---@return pb_CSRankItemExData
function pb.CSRankItemExData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RankItem = {
    player_id = 0,
    score = 0,
    nick = "",
    logo = "",
    game_center = 0,
    plat_id = 0,
    account_type = 0,
    nick_name = "",
    pic_url = "",
    level = 0,
    season_level = 0,
    sol_rank_score = 0,
    sol_attended = false,
    mp_rank_score = 0,
    mp_attended = false,
    is_privacy = false,
    mp_commander_rank_score = 0,
    show_commander_rank_points = 0,
}
pb.__pb_RankItem.__name = "RankItem"
pb.__pb_RankItem.__index = pb.__pb_RankItem
pb.__pb_RankItem.__pairs = __pb_pairs

pb.RankItem = { __name = "RankItem", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RankItem : ProtoBase
---@field public player_id number
---@field public score number
---@field public nick string
---@field public logo string
---@field public game_center number
---@field public plat_id number
---@field public account_type number
---@field public ex_data pb_CSRankItemExData
---@field public nick_name string
---@field public pic_url string
---@field public level number
---@field public season_level number
---@field public sol_rank_score number
---@field public sol_attended boolean
---@field public mp_rank_score number
---@field public mp_attended boolean
---@field public is_privacy boolean
---@field public mp_commander_rank_score number
---@field public show_commander_rank_points number

---@return pb_RankItem
function pb.RankItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetListReq = {
    rank_data_type = 0,
    adcode = 0,
    rank_board_id = 0,
    page_size = 0,
    page_num = 0,
}
pb.__pb_CSRankGetListReq.__name = "CSRankGetListReq"
pb.__pb_CSRankGetListReq.__index = pb.__pb_CSRankGetListReq
pb.__pb_CSRankGetListReq.__pairs = __pb_pairs

pb.CSRankGetListReq = { __name = "CSRankGetListReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetListReq : ProtoBase
---@field public rank_data_type number
---@field public adcode number
---@field public rank_board_id number
---@field public page_size number
---@field public page_num number

---@return pb_CSRankGetListReq
function pb.CSRankGetListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetListRes = {
    result = 0,
    total = 0,
    page_num = 0,
    page_max = 0,
    page_size = 0,
    self_rank_no = 0,
}
pb.__pb_CSRankGetListRes.__name = "CSRankGetListRes"
pb.__pb_CSRankGetListRes.__index = pb.__pb_CSRankGetListRes
pb.__pb_CSRankGetListRes.__pairs = __pb_pairs

pb.CSRankGetListRes = { __name = "CSRankGetListRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetListRes : ProtoBase
---@field public result number
---@field public total number
---@field public page_num number
---@field public page_max number
---@field public page_size number
---@field public list pb_RankItem[]
---@field public self_rank_no number

---@return pb_CSRankGetListRes
function pb.CSRankGetListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetHandbookReq = {
    hand_book_id = 0,
}
pb.__pb_CSRankGetHandbookReq.__name = "CSRankGetHandbookReq"
pb.__pb_CSRankGetHandbookReq.__index = pb.__pb_CSRankGetHandbookReq
pb.__pb_CSRankGetHandbookReq.__pairs = __pb_pairs

pb.CSRankGetHandbookReq = { __name = "CSRankGetHandbookReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetHandbookReq : ProtoBase
---@field public hand_book_id number

---@return pb_CSRankGetHandbookReq
function pb.CSRankGetHandbookReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPlayerHandBookDataTypeInfo = {
    rank_datatype_id = 0,
}
pb.__pb_CSRankPlayerHandBookDataTypeInfo.__name = "CSRankPlayerHandBookDataTypeInfo"
pb.__pb_CSRankPlayerHandBookDataTypeInfo.__index = pb.__pb_CSRankPlayerHandBookDataTypeInfo
pb.__pb_CSRankPlayerHandBookDataTypeInfo.__pairs = __pb_pairs

pb.CSRankPlayerHandBookDataTypeInfo = { __name = "CSRankPlayerHandBookDataTypeInfo", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPlayerHandBookDataTypeInfo : ProtoBase
---@field public rank_datatype_id number
---@field public rank_single_hand_book_data pb_RankSingleHandBookData

---@return pb_CSRankPlayerHandBookDataTypeInfo
function pb.CSRankPlayerHandBookDataTypeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPlayerHandBookData = {
    handbook_id = 0,
}
pb.__pb_CSRankPlayerHandBookData.__name = "CSRankPlayerHandBookData"
pb.__pb_CSRankPlayerHandBookData.__index = pb.__pb_CSRankPlayerHandBookData
pb.__pb_CSRankPlayerHandBookData.__pairs = __pb_pairs

pb.CSRankPlayerHandBookData = { __name = "CSRankPlayerHandBookData", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPlayerHandBookData : ProtoBase
---@field public handbook_id number
---@field public rank_hand_book_data_type_info pb_CSRankPlayerHandBookDataTypeInfo[]

---@return pb_CSRankPlayerHandBookData
function pb.CSRankPlayerHandBookData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetHandbookRes = {
    result = 0,
    hand_book_id = 0,
}
pb.__pb_CSRankGetHandbookRes.__name = "CSRankGetHandbookRes"
pb.__pb_CSRankGetHandbookRes.__index = pb.__pb_CSRankGetHandbookRes
pb.__pb_CSRankGetHandbookRes.__pairs = __pb_pairs

pb.CSRankGetHandbookRes = { __name = "CSRankGetHandbookRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetHandbookRes : ProtoBase
---@field public result number
---@field public hand_book_id number
---@field public rank_hand_book_data_list pb_CSRankPlayerHandBookData[]

---@return pb_CSRankGetHandbookRes
function pb.CSRankGetHandbookRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPrivacySetReq = {
    is_show_on_rank = false,
}
pb.__pb_CSRankPrivacySetReq.__name = "CSRankPrivacySetReq"
pb.__pb_CSRankPrivacySetReq.__index = pb.__pb_CSRankPrivacySetReq
pb.__pb_CSRankPrivacySetReq.__pairs = __pb_pairs

pb.CSRankPrivacySetReq = { __name = "CSRankPrivacySetReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPrivacySetReq : ProtoBase
---@field public is_show_on_rank boolean

---@return pb_CSRankPrivacySetReq
function pb.CSRankPrivacySetReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPrivacySetRes = {
    result = 0,
}
pb.__pb_CSRankPrivacySetRes.__name = "CSRankPrivacySetRes"
pb.__pb_CSRankPrivacySetRes.__index = pb.__pb_CSRankPrivacySetRes
pb.__pb_CSRankPrivacySetRes.__pairs = __pb_pairs

pb.CSRankPrivacySetRes = { __name = "CSRankPrivacySetRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPrivacySetRes : ProtoBase
---@field public result number

---@return pb_CSRankPrivacySetRes
function pb.CSRankPrivacySetRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPrivacyGetReq = {
    is_show_on_rank = false,
}
pb.__pb_CSRankPrivacyGetReq.__name = "CSRankPrivacyGetReq"
pb.__pb_CSRankPrivacyGetReq.__index = pb.__pb_CSRankPrivacyGetReq
pb.__pb_CSRankPrivacyGetReq.__pairs = __pb_pairs

pb.CSRankPrivacyGetReq = { __name = "CSRankPrivacyGetReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPrivacyGetReq : ProtoBase
---@field public is_show_on_rank boolean

---@return pb_CSRankPrivacyGetReq
function pb.CSRankPrivacyGetReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankPrivacyGetRes = {
    result = 0,
    is_show_on_rank = false,
}
pb.__pb_CSRankPrivacyGetRes.__name = "CSRankPrivacyGetRes"
pb.__pb_CSRankPrivacyGetRes.__index = pb.__pb_CSRankPrivacyGetRes
pb.__pb_CSRankPrivacyGetRes.__pairs = __pb_pairs

pb.CSRankPrivacyGetRes = { __name = "CSRankPrivacyGetRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankPrivacyGetRes : ProtoBase
---@field public result number
---@field public is_show_on_rank boolean

---@return pb_CSRankPrivacyGetRes
function pb.CSRankPrivacyGetRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankZoneSetReq = {
    rank_adcode = 0,
}
pb.__pb_CSRankZoneSetReq.__name = "CSRankZoneSetReq"
pb.__pb_CSRankZoneSetReq.__index = pb.__pb_CSRankZoneSetReq
pb.__pb_CSRankZoneSetReq.__pairs = __pb_pairs

pb.CSRankZoneSetReq = { __name = "CSRankZoneSetReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankZoneSetReq : ProtoBase
---@field public rank_adcode number

---@return pb_CSRankZoneSetReq
function pb.CSRankZoneSetReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankZoneSetRes = {
    result = 0,
}
pb.__pb_CSRankZoneSetRes.__name = "CSRankZoneSetRes"
pb.__pb_CSRankZoneSetRes.__index = pb.__pb_CSRankZoneSetRes
pb.__pb_CSRankZoneSetRes.__pairs = __pb_pairs

pb.CSRankZoneSetRes = { __name = "CSRankZoneSetRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankZoneSetRes : ProtoBase
---@field public result number

---@return pb_CSRankZoneSetRes
function pb.CSRankZoneSetRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankZoneGetReq = {
    none = 0,
}
pb.__pb_CSRankZoneGetReq.__name = "CSRankZoneGetReq"
pb.__pb_CSRankZoneGetReq.__index = pb.__pb_CSRankZoneGetReq
pb.__pb_CSRankZoneGetReq.__pairs = __pb_pairs

pb.CSRankZoneGetReq = { __name = "CSRankZoneGetReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankZoneGetReq : ProtoBase
---@field public none number

---@return pb_CSRankZoneGetReq
function pb.CSRankZoneGetReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankZoneGetRes = {
    result = 0,
    cur_adcode = 0,
    next_adcode = 0,
}
pb.__pb_CSRankZoneGetRes.__name = "CSRankZoneGetRes"
pb.__pb_CSRankZoneGetRes.__index = pb.__pb_CSRankZoneGetRes
pb.__pb_CSRankZoneGetRes.__pairs = __pb_pairs

pb.CSRankZoneGetRes = { __name = "CSRankZoneGetRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankZoneGetRes : ProtoBase
---@field public result number
---@field public cur_adcode number
---@field public next_adcode number

---@return pb_CSRankZoneGetRes
function pb.CSRankZoneGetRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerRankAuxDataReq = {
    data_type = 0,
}
pb.__pb_CSRankGetPlayerRankAuxDataReq.__name = "CSRankGetPlayerRankAuxDataReq"
pb.__pb_CSRankGetPlayerRankAuxDataReq.__index = pb.__pb_CSRankGetPlayerRankAuxDataReq
pb.__pb_CSRankGetPlayerRankAuxDataReq.__pairs = __pb_pairs

pb.CSRankGetPlayerRankAuxDataReq = { __name = "CSRankGetPlayerRankAuxDataReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerRankAuxDataReq : ProtoBase
---@field public data_type number

---@return pb_CSRankGetPlayerRankAuxDataReq
function pb.CSRankGetPlayerRankAuxDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerRankAuxDataRes = {
    result = 0,
}
pb.__pb_CSRankGetPlayerRankAuxDataRes.__name = "CSRankGetPlayerRankAuxDataRes"
pb.__pb_CSRankGetPlayerRankAuxDataRes.__index = pb.__pb_CSRankGetPlayerRankAuxDataRes
pb.__pb_CSRankGetPlayerRankAuxDataRes.__pairs = __pb_pairs

pb.CSRankGetPlayerRankAuxDataRes = { __name = "CSRankGetPlayerRankAuxDataRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerRankAuxDataRes : ProtoBase
---@field public result number
---@field public rank_player_aux_data pb_CSRankPlayerAuxData[]

---@return pb_CSRankGetPlayerRankAuxDataRes
function pb.CSRankGetPlayerRankAuxDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPlayerAuxDataMatchInfo = {
    room_id = 0,
    score = 0,
    time = 0,
    score_sum = 0,
    match_mode_id = 0,
    timestamp = 0,
}
pb.__pb_CSPlayerAuxDataMatchInfo.__name = "CSPlayerAuxDataMatchInfo"
pb.__pb_CSPlayerAuxDataMatchInfo.__index = pb.__pb_CSPlayerAuxDataMatchInfo
pb.__pb_CSPlayerAuxDataMatchInfo.__pairs = __pb_pairs

pb.CSPlayerAuxDataMatchInfo = { __name = "CSPlayerAuxDataMatchInfo", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPlayerAuxDataMatchInfo : ProtoBase
---@field public room_id number
---@field public score number
---@field public time number
---@field public score_sum number
---@field public match_mode_id number
---@field public timestamp number

---@return pb_CSPlayerAuxDataMatchInfo
function pb.CSPlayerAuxDataMatchInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerRankAuxDataDetailReq = {
    data_type = 0,
}
pb.__pb_CSRankGetPlayerRankAuxDataDetailReq.__name = "CSRankGetPlayerRankAuxDataDetailReq"
pb.__pb_CSRankGetPlayerRankAuxDataDetailReq.__index = pb.__pb_CSRankGetPlayerRankAuxDataDetailReq
pb.__pb_CSRankGetPlayerRankAuxDataDetailReq.__pairs = __pb_pairs

pb.CSRankGetPlayerRankAuxDataDetailReq = { __name = "CSRankGetPlayerRankAuxDataDetailReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerRankAuxDataDetailReq : ProtoBase
---@field public data_type number

---@return pb_CSRankGetPlayerRankAuxDataDetailReq
function pb.CSRankGetPlayerRankAuxDataDetailReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerRankAuxDataDetailRes = {
    result = 0,
    match_time_limit = 0,
}
pb.__pb_CSRankGetPlayerRankAuxDataDetailRes.__name = "CSRankGetPlayerRankAuxDataDetailRes"
pb.__pb_CSRankGetPlayerRankAuxDataDetailRes.__index = pb.__pb_CSRankGetPlayerRankAuxDataDetailRes
pb.__pb_CSRankGetPlayerRankAuxDataDetailRes.__pairs = __pb_pairs

pb.CSRankGetPlayerRankAuxDataDetailRes = { __name = "CSRankGetPlayerRankAuxDataDetailRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerRankAuxDataDetailRes : ProtoBase
---@field public result number
---@field public rank_player_aux_data_detail pb_CSPlayerAuxDataMatchInfo[]
---@field public match_time_limit number

---@return pb_CSRankGetPlayerRankAuxDataDetailRes
function pb.CSRankGetPlayerRankAuxDataDetailRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankBatchGetAuxDataReq = {
}
pb.__pb_CSRankBatchGetAuxDataReq.__name = "CSRankBatchGetAuxDataReq"
pb.__pb_CSRankBatchGetAuxDataReq.__index = pb.__pb_CSRankBatchGetAuxDataReq
pb.__pb_CSRankBatchGetAuxDataReq.__pairs = __pb_pairs

pb.CSRankBatchGetAuxDataReq = { __name = "CSRankBatchGetAuxDataReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankBatchGetAuxDataReq : ProtoBase
---@field public player_ids number[]

---@return pb_CSRankBatchGetAuxDataReq
function pb.CSRankBatchGetAuxDataReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankBatchGetAuxDataRes = {
    result = 0,
}
pb.__pb_CSRankBatchGetAuxDataRes.__name = "CSRankBatchGetAuxDataRes"
pb.__pb_CSRankBatchGetAuxDataRes.__index = pb.__pb_CSRankBatchGetAuxDataRes
pb.__pb_CSRankBatchGetAuxDataRes.__pairs = __pb_pairs

pb.CSRankBatchGetAuxDataRes = { __name = "CSRankBatchGetAuxDataRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankBatchGetAuxDataRes : ProtoBase
---@field public result number
---@field public player_aux_data pb_SinglePlayerAuxData[]

---@return pb_CSRankBatchGetAuxDataRes
function pb.CSRankBatchGetAuxDataRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankCheckPopUpWindowReq = {
}
pb.__pb_CSRankCheckPopUpWindowReq.__name = "CSRankCheckPopUpWindowReq"
pb.__pb_CSRankCheckPopUpWindowReq.__index = pb.__pb_CSRankCheckPopUpWindowReq
pb.__pb_CSRankCheckPopUpWindowReq.__pairs = __pb_pairs

pb.CSRankCheckPopUpWindowReq = { __name = "CSRankCheckPopUpWindowReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankCheckPopUpWindowReq : ProtoBase

---@return pb_CSRankCheckPopUpWindowReq
function pb.CSRankCheckPopUpWindowReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankCheckPopUpWindowRes = {
    last_weekly_reward_time = 0,
}
pb.__pb_CSRankCheckPopUpWindowRes.__name = "CSRankCheckPopUpWindowRes"
pb.__pb_CSRankCheckPopUpWindowRes.__index = pb.__pb_CSRankCheckPopUpWindowRes
pb.__pb_CSRankCheckPopUpWindowRes.__pairs = __pb_pairs

pb.CSRankCheckPopUpWindowRes = { __name = "CSRankCheckPopUpWindowRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankCheckPopUpWindowRes : ProtoBase
---@field public last_weekly_reward_time number

---@return pb_CSRankCheckPopUpWindowRes
function pb.CSRankCheckPopUpWindowRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerRankPercentageReq = {
}
pb.__pb_CSRankGetPlayerRankPercentageReq.__name = "CSRankGetPlayerRankPercentageReq"
pb.__pb_CSRankGetPlayerRankPercentageReq.__index = pb.__pb_CSRankGetPlayerRankPercentageReq
pb.__pb_CSRankGetPlayerRankPercentageReq.__pairs = __pb_pairs

pb.CSRankGetPlayerRankPercentageReq = { __name = "CSRankGetPlayerRankPercentageReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerRankPercentageReq : ProtoBase
---@field public rank_data_type number[]

---@return pb_CSRankGetPlayerRankPercentageReq
function pb.CSRankGetPlayerRankPercentageReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerRankPercentageRes = {
    result = 0,
}
pb.__pb_CSRankGetPlayerRankPercentageRes.__name = "CSRankGetPlayerRankPercentageRes"
pb.__pb_CSRankGetPlayerRankPercentageRes.__index = pb.__pb_CSRankGetPlayerRankPercentageRes
pb.__pb_CSRankGetPlayerRankPercentageRes.__pairs = __pb_pairs

pb.CSRankGetPlayerRankPercentageRes = { __name = "CSRankGetPlayerRankPercentageRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerRankPercentageRes : ProtoBase
---@field public list pb_PlayerRankPercentageInfo[]
---@field public result number

---@return pb_CSRankGetPlayerRankPercentageRes
function pb.CSRankGetPlayerRankPercentageRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerHistoricalRecordReq = {
}
pb.__pb_CSRankGetPlayerHistoricalRecordReq.__name = "CSRankGetPlayerHistoricalRecordReq"
pb.__pb_CSRankGetPlayerHistoricalRecordReq.__index = pb.__pb_CSRankGetPlayerHistoricalRecordReq
pb.__pb_CSRankGetPlayerHistoricalRecordReq.__pairs = __pb_pairs

pb.CSRankGetPlayerHistoricalRecordReq = { __name = "CSRankGetPlayerHistoricalRecordReq", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerHistoricalRecordReq : ProtoBase

---@return pb_CSRankGetPlayerHistoricalRecordReq
function pb.CSRankGetPlayerHistoricalRecordReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRankGetPlayerHistoricalRecordRes = {
    result = 0,
}
pb.__pb_CSRankGetPlayerHistoricalRecordRes.__name = "CSRankGetPlayerHistoricalRecordRes"
pb.__pb_CSRankGetPlayerHistoricalRecordRes.__index = pb.__pb_CSRankGetPlayerHistoricalRecordRes
pb.__pb_CSRankGetPlayerHistoricalRecordRes.__pairs = __pb_pairs

pb.CSRankGetPlayerHistoricalRecordRes = { __name = "CSRankGetPlayerHistoricalRecordRes", __service="rank", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRankGetPlayerHistoricalRecordRes : ProtoBase
---@field public record_info pb_PlayerRankHistoricalRecordInfo
---@field public result number

---@return pb_CSRankGetPlayerHistoricalRecordRes
function pb.CSRankGetPlayerHistoricalRecordRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


