---@class ActivityQuestionStruct : LuaObject
-- ActivityQuestionStruct
ActivityQuestionStruct = class('ActivityQuestionStruct', LuaObject)

---@param ActivityQuestionStruct AT
function ActivityQuestionStruct:Ctor(ActivityQuestion)
    self:InitActivityStruct(ActivityQuestion)
end

---@param ActivityQuestionStruct AT
function ActivityQuestionStruct:InitActivityStruct(ActivityQuestion)
    self.GoalID = ActivityQuestion.GoalID or 0
    self.Question = ActivityQuestion.Question or ""
    self.Answer = ActivityQuestion.Answer or 0
    self.Choice1 = ActivityQuestion.Choice1 or ""
    self.Choice2 = ActivityQuestion.Choice2 or ""
    self.Choice3 = ActivityQuestion.Choice3 or ""
    self.Choice4 = ActivityQuestion.Choice4 or ""
end

return ActivityQuestionStruct