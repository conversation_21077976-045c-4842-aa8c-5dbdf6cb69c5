----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLogin)
----- LOG FUNCTION AUTO GENERATE END -----------



UITable[UIName2ID.LoginInterface] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginInterface",
    BPKey = "WBP_LoginInterface",
    SubUIs = {
        UIName2ID.LoginButton,
    },
}

UITable[UIName2ID.LoginInterfaceINTL] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginInterfaceINTL",
    BPKey = "WBP_LoginInterfaceINTL",
}

UITable[UIName2ID.LoginAgePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginAgePop",
    BPKey = "WBP_Login_Ageappropriatetips",
    IsModal = true,
}

UITable[UIName2ID.RegisterView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.LoginModule.UI.RegisterView",
    BPKey = "WBP_LoginSetname",
    Anim = {
        FlowInAni = "WBP_LoginSetname_in_01",
        FlowOutAni = "WBP_LoginSetname_out",
    }
}

UITable[UIName2ID.LoginServerList] = {
    UILayer = EUILayer.Pop,
    SubUIs = {
        UIName2ID.LoginServerCell,
        UIName2ID.LoginServerGroupCell,
    },
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginServerList",
    BPKey = "WBP_ServerList",
    IsModal = true,
}

UITable[UIName2ID.LoginServerCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginServerCell",
    BPKey = "F_Login_Element_Server"
}

UITable[UIName2ID.LoginServerGroupCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginServerGroupCell",
    BPKey = "F_Login_Element_ServerBtn"
}

UITable[UIName2ID.VersionDetail] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.VersionDetail",
    BPKey = "WBP_VersionDetail",
    SubUIs = {
        UIName2ID.VersionDetailGroupCell,
    }
}

UITable[UIName2ID.VersionDetailGroupCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.LoginModule.UI.VersionDetailGroupCell",
    BPKey = "F_VersionDetail_GroupBtn"
}

UITable[UIName2ID.LanguagePopView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LanguagePopView",
    BPKey = "WBP_SetUp_PopWindow_Language_SetUp",
    SubUIs = {
        UIName2ID.SystemSettingLanguageCell,
    },
    IsModal = true,
}

UITable[UIName2ID.TestUIAnimation] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.TestUIAnimation",
    BPKey = "WBP_TestUIAnimation"
}

UITable[UIName2ID.LoginButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginButtonView",
    BPKey = "WBP_LoginButton"
}

UITable[UIName2ID.MoreChiocesPopView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.MoreChiocesPopView",
    BPKey = "WBP_Login_MoreChioces_Pop",
    SubUIs = {
        UIName2ID.LoginButtonV2,
    },
    IsModal = true,
}

UITable[UIName2ID.LoginButtonV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginButtonViewV2",
    BPKey = "WBP_LoginButton_V2"
}

UITable[UIName2ID.LoginWebPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.LoginWebPanel",
    BPKey = "WBP_Login_Web"
}

UITable[UIName2ID.PrivacyPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.PrivacyPanel",
    BPKey = "WBP_WW_PrivacyCompliance_Pop",
    IsModal = true,
}

UITable[UIName2ID.PrivacyItem] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.LoginModule.UI.PrivacyItem",
    BPKey = "WBP_WW_PrivacyCompliance_Entry"
}

local LoginConfig = {

    MethodName = 
{
    kMethodNameUndefine                 = 000,

    kMethodNameAutoLogin                = 111,
    kMethodNameLogin                    = 112,
    kMethodNameBind                     = 113,
    kMethodNameGetLoginRet              = 114,
    kMethodNameSwitchUser               = 115,
    kMethodNameQueryUserInfo            = 116,
    kMethodNameLogout                   = 117,
    kMethodNameLoginWithConfirmCode     = 118,
    kMethodNameWakeUp                   = 119,
    KMethodNameScheme                   = 120,
    kMethodNameResetGuest               = 121,

    kMethodNameShareToWall              = 211,
    kMethodNameSendMessageToFriend      = 212,
    kMethodNameQueryFriend              = 213,
    kMethodNameAddFriend                = 214,

    kMethodNameCreateGroup              = 311,
    kMethodNameBindGroup                = 312,
    kMethodNameGetGroupList             = 313,
    kMethodNameGetGroupState            = 314,
    kMethodNameJoinGroup                = 315,
    kMethodNameUnbindGroup              = 316,
    kMethodNameRemindToBindGroup        = 317,
    kMethodNameSendMessageToGroup       = 318,
    kMethodNameGetGroupRelation         = 319,

    kMethodNameCloseWebViewURL          = 411,
    kMethodNameGetEncodeURL             = 412,
    kMethodNameWebViewJsCall            = 413,
    kMethodNameWebViewJsShare           = 414,
    kMethodNameWebViewJsSendMessage     = 415,
    kMethodNameWebViewEmbedProgress     = 416,


    kMethodNameRegisterPush             = 511,
    kMethodNameUnregisterPush           = 512,
    kMethodNameSetTagForPush            = 513,
    kMethodNameDeleteTagForPush         = 514,
    kMethodNameAddLocalNotify           = 515,
    kMethodNameClearLocalNotify         = 516,
    kMethodNameNotifyCallback           = 517,
    kMethodNameNotifyShow               = 518,
    kMethodNameNotifyClick              = 519,
    kMethodNameSetAccountPush           = 520,
    kMethodNameDeleteAccountPush        = 521,




    kMethodNameLoadNoticeData           = 611,

    kMethodNameSetupGame                = 711,
    kMethodNameShowGameLeaderBoard      = 712,
    kMethodNameSetScore                 = 713,
    kMethodNameShowGameAchievement      = 714,
    kMethodNameUnlockGameAchieve        = 715,

    KMethodNameToolsOpenDeepLink        = 911,
    KMethodNameToolsConvertShortUrl     = 912,
    KMethodNameToolsGetFreeFlowInfo     = 913,

    kMethodNameCrashExtraData           = 1011,
    kMethodNameCrashExtraMessage        = 1012,
    
    kMethodNameExtend                   = 1111,
},

    EINTLPanelType = {
        Login = 0,
        AccountCenter = 1,
        NativeLogin = 2,
        AutoLogin = 3,
    },

    -- ConnectorPublicDefine.h GCloud.Conn.ChannelType
    -- EChannelType = {
    --     kChannelNone = 0,
    --     kChannelWechat = 1,
    --     kChannelQQ = 2,
    --     kChannelGuest = 3,
    --     kChannelFacebook = 4,
    --     kChannelGameCenter = 5,
    --     kChannelGooglePlay = 6
    -- },
    --MSDK好友模块分享类型
    MSDKFriendReqType = {
        kMSDKFriendReqTypeText                  = 10000,            --文字分享
        kMSDKFriendReqTypeLink                  = 10001,            --链接分享
        kMSDKFriendReqTypeIMG                   = 10002,            --图片分享
        kMSDKFriendReqTypeInvite                = 10003,            --应用邀请
        kMSDKFriendReqTypeMusic                 = 10004,            --音乐分享
        kMSDKFriendReqTypeVideo                 = 10005,            --视频分享
        kMSDKFriendReqTypeMiniApp               = 10006,            --小程序分享
        kMSDKFriendReqTypePullUpMiniApp         = 10007,            --小程序拉起
        kMSDKFriendReqTypeArk                   = 10008,            --ARK分享
        kMSDKFriendReqTypeOpenBusinessView      = 10009,            --业务功能拉起
        kMSDKFriendReqTypeWXGameLine            = 10010,            --
        kMSDKFriendReqTypeWXChannelShareVideo   = 10011,            --分享至视频号
        kMSDKFriendReqTypeWXStateText           = 10012,            --分享文本至WX状态
        kMSDKFriendReqTypeWXStatePhoto          = 10013,            --分享图片至WX状态
        kMSDKFriendReqTypeWXStateMusicVideo     = 10014,            --分享音乐视频至WX状态

        kMSDKFriendReqTypeTextSilent            = 20000,            --文字分享（静默）
        kMSDKFriendReqTypeLinkSilent            = 20001,            --链接分享 (静默)
        kMSDKFriendReqTypeIMGSilent             = 20002,            --图片分享 （静默）
        kMSDKFriendReqTypeInviteSilent          = 20003,            --应用邀请 (静默）
        kMSDKFriendReqTypeMusicSilent           = 20004,            --音乐分享 (静默)
        kMSDKFriendReqTypeVideoSilent           = 20005,            --视频分享 (静默)
        kMSDKFriendReqTypeMiniAppSilent         = 20006,            --小程序分享 (静默)
    },
    Loc = {
        LoginInterfaceTitle = NSLOCTEXT("LoginModule", "Lua_Login_LoginInterfaceTitle", "登录"),
        LoginFail = NSLOCTEXT("LoginModule", "Lua_LoginView_LoginFail", "网络断开，请关闭后重试"),
        BranchDaily = NSLOCTEXT("LoginModule", "Lua_LoginView_BranchDaily", "分支_Daily服务器"),
        MatchServerName = NSLOCTEXT("LoginModule", "Lua_LoginView_MatchServerName", "CE服"),
        ReturnToLoginScene = NSLOCTEXT("LoginModule", "Lua_LoginView_ReturnToLoginScene", "此处等待重回登录界面"),

        LanguageTip = NSLOCTEXT("LoginModule", "Lua_LoginView_LanguageChange", "当前语言已切换为%s"),
        LanguageFailTip = NSLOCTEXT("LoginModule", "Lua_LoginView_LanguageChangeFail", "语言切换失败"),
        -- BEGIN MODIFICATION - VIRTUOS
        FailGetThirdPartAccountInfoTip = NSLOCTEXT("LoginModule", "Console_Lua_LoginView_FailGetThirdPartAccountInfoTip", "未获取账号信息，请查询Xbox网络后重试"),
        -- END MODIFICATION - VIRTUOS
        FailLogin = NSLOCTEXT("LoginModule", "Lua_LoginView_FailLogin", "%s:登录回包失败，请重试"),
        ThirdPartLoginPIETip = NSLOCTEXT("LoginModule", "Lua_LoginView_ThirdPartLoginPIETip", "需要在调试面板使用账号登录"),
        StayInQueueTip = NSLOCTEXT("LoginModule", "Lua_LoginView_StayInQueueTip", "登录排队中，共%d人排队，当前排名第%d，预计等候：%d秒"),

        RegisterTipNormal = NSLOCTEXT("LoginModule", "Lua_LoginView_RegisterTipNormal", "*名称上限为14个字符，不支持符号"),
        RegisterErrTipTooLong = NSLOCTEXT("LoginModule", "Lua_LoginView_RegisterErrTipTooLong", "*名称上限为14个字符，您已超出"),
        RegisterErrTipEmpty = NSLOCTEXT("LoginModule", "Lua_LoginView_RegisterErrTipEmpty", "*昵称不能为空"),
        RegisterErrTipSpecialCharacter = NSLOCTEXT("LoginModule", "Lua_LoginView_RegisterErrTipSpecialCharacter", "*名称不支持符号"),
        AccountNeedAllNum = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountNeedAllNum", "账号登录需要全数字"),
        MobileNotAllowAccountLogin = NSLOCTEXT("LoginModule", "Lua_LoginView_MobileNotAllowAccountLogin", "手机端不允许账号直接登录"),
        BackToLogin = NSLOCTEXT("LoginModule", "Lua_LoginView_BackToLogin", "返回登录"),
        LoginSuccess = NSLOCTEXT("LoginModule", "Lua_LoginView_LoginSuccess", "登陆成功"),
        EnterUserName = NSLOCTEXT("LoginModule", "Lua_LoginView_EnterUserName", "请输入用户名"),
        UnConfigText = NSLOCTEXT("LoginModule", "Lua_LoginView_UnConfigText", "当前错误码未配置: %d"),
        ConfirmBtnText = NSLOCTEXT("LoginModule", "Lua_LoginView_Confirm", "确定"),
        AccountLimitDataInvalid = NSLOCTEXT("LoginModule", "Lua_LoginView_BackPackInvalid", "账号封禁回包异常"),
        AccountNotInWhiteList = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountNotInWhiteList", "账号不在白名单"),
        AccountNotInWhiteListGloablDev = NSLOCTEXT("LoginModule", "Lua_AccountNotInWhiteListGloablDev", "账号不在白名单(开发环境已将OpenId复制到剪切板，请联系服务器同学添加白名单)"),
        AccountLimitText = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountLimitText", "{Message}\n解封时间为：{LimitTime}"),
        AccountLimitForever = NSLOCTEXT("LoginModule", "Lua_LoginView_AccounLimitForever", "当前账户已被永久封禁"),
        PunishDataNoConfig = NSLOCTEXT("LoginModule", "Lua_LoginView_PunishDataNoConfig", "玩家处罚封禁信息未配置：%d"),
        PunishDataInvalid = NSLOCTEXT("LoginModule", "Lua_LoginView_PunishDataInvalid", "封禁信息回包异常"),
        Version = NSLOCTEXT("LoginModule", "Lua_LoginView_Version", "AppVer:%s  ResVer:%s  LgcVer:%s"),
-- BEGIN VIRTUOS MODIFICATION
        ClickTooOften = NSLOCTEXT("LoginModule", "Console_Lua_LoginView_ClickTooOften", "抱歉，点击过于频繁"),
-- END VIRTUOS MODIFICATION
        RegisterErrNoOpening = NSLOCTEXT("LoginModule", "Lua_RegisterView_RegisterErrNoOpening", "您好，游戏在当前时间段未开放注册"),
        RegisterErrNoOpeningTime = NSLOCTEXT("LoginModule", "Lua_RegisterView_RegisterErrNoOpeningTime", "您好，游戏在当前时间段未开放注册，下次开放时间为{BeginTime}"),
        RegisterErrNoOpeningTimeSpan = NSLOCTEXT("LoginModule", "Lua_RegisterView_RegisterErrNoOpeningTimeSpan", "您好，游戏在当前时间段未开放注册，下次开放时间为{BeginTime} - {EndTime}"),
    
        FixTipsText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixTipsText", "当出现资源异常或游戏无法启动的现象时，可尝试进行客户端修复。"),
        FixNoticeText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixNoticeText", "该操作会清除本地资源文件和补丁，并进行重新下载。请在良好的网络环境下进行，并手动重启游戏。"),
        FixPufferTipsText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixPufferTipsText", "该操作将检测损坏的资源文件并重新下载，请问是否确认修复？"),
        FixPufferNoticeText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixPufferNoticeText", "*需要重启后生效"),
        FixCancelBtnText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixCancelBtnText", "取消"),
        FixConfirmBtnText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixConfirmBtnText", "确定"),
        FixPufferConfirmBtnText = NSLOCTEXT("LoginModule", "Lua_FixNoticeView_FixPufferConfirmBtnText", "立即重启"),
        ThirdPartyLoginFailed = NSLOCTEXT("LoginModule", "Lua_LoginView_ThirdPartyLoginFailed", "登录渠道授权失败"),
        CancelAuth = NSLOCTEXT("LoginModule", "Lua_LoginView_CancelAuth", "您取消了授权。"),
        NoUsableAnnounceData = NSLOCTEXT("LoginModule", "Lua_Announce_NoUsableAnnounceData", "暂无公告。"),
        ExitClient = NSLOCTEXT("LoginModule", "Lua_ExitClient_PCOnly", "退出游戏"),
        ServerInfoNotFound = NSLOCTEXT("LoginModule", "Lua_LoginView_ServerInfoNotFound", "未找到服务器信息配置"),
        AccountLoginServiceClosed =  NSLOCTEXT("LoginModule", "Lua_LoginView_AccountLoginServiceClosed", "服务器维护中，具体开服时间请关注官方最新信息。"),
        AccountLoginServiceClosed_Exper =  NSLOCTEXT("LoginModule", "Lua_LoginView_AccountLoginServiceClosed_Exper", "当前不在测试时间段或您不在本次测试白名单，请留意官方信息"),
        LoginFailed = NSLOCTEXT("LoginModule", "Lua_LoginView_LoginFailed", "%s"),
        LoginFailedMobile = NSLOCTEXT("LoginModule", "Lua_LoginView_LoginFailedMobile", "%s"),
        ReadServiceInfo = NSLOCTEXT("LoginModule", "Lua_LoginView_NeedReadServiceInfo", "请阅读并勾选同意下方的腾讯游戏许可及服务协议、隐私保护指引、儿童隐私保护指引和第三方信息共享清单即可进入游戏"),
        LoginLimitTips =  NSLOCTEXT("LoginModule", "Lua_LoginView_LoginLimitTips", "您当前登录失败次数过多，请%s秒后重试"),
        CancelLogin = NSLOCTEXT("LoginModule", "Lua_LoginView_CancelLogin", "放弃登录"),
        LoginAndNotReconnect = NSLOCTEXT("LoginModule", "Lua_LoginView_LoginAndNotReconnect", "登录并放弃重连"),
        LoginConfirmContent =  NSLOCTEXT("LoginModule", "Lua_LoginView_LoginConfirmContent", "您处于另一个平台的对局中，继续当前平台登录将使您永远无法回到这场对局中，是否确定登录？"),
        LoginCrossPlatform =  NSLOCTEXT("LoginModule", "Lua_LoginView_CrossPlatform", "账号已在另一个平台的设备上登录，当前对局被强制结束，将返回登录界面。"),
        LoginCrossPlatformLobby =  NSLOCTEXT("LoginModule", "Lua_LoginView_CrossPlatformLobby", "账号已在另一个平台的设备上登录，此设备账号被强制下线。"),
        AccountNeedUpdateClientVersion = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountNeedUpdateClientVersion", "当前客户端不可用，请退出游戏更新至新版本。"),
        ReconnectConfirmContent =  NSLOCTEXT("LoginModule", "Lua_LoginView_ReconnectConfirmContent", "您处于另一个平台的对局中，继续当前平台重连将使您永远无法回到这场对局中，是否确定重连？"),
        CancelReconnect = NSLOCTEXT("LoginModule", "Lua_LoginView_CancelReconnect", "放弃重连"),
        ConfirmReconnect = NSLOCTEXT("LoginModule", "Lua_LoginView_ConfirmReconnect", "重连并放弃对局"),
        RuleLinkUrl = NSLOCTEXT("LoginModule", "Lua_LoginView_RuleLinkUrl", "我已详细阅读并同意<dfmrichtext style=\"Login_Rules\" link=\"https://df.qq.com/cp/a20240125main/tencent_other_contract.shtml\">腾讯游戏许可及服务协议</>、<dfmrichtext style=\"Login_Rules\" link=\"https://df.qq.com/cp/a20240125main/tencent_other_privacy_mobile.shtml\">隐私保护指引</>、<dfmrichtext style=\"Login_Rules\" link=\"https://df.qq.com/cp/a20240125main/tencent_other_children_privacy.shtml\">儿童隐私保护指引</>和<dfmrichtext style=\"Login_Rules\" link=\"https://df.qq.com/cp/a20240125main/tencent_other_privacy_SDK_mobile.shtml\">第三方信息共享清单</>"),
        SimulatorNotAllow = NSLOCTEXT("LoginModule", "Lua_LoginView_SimulatorNotAllow", "为保证游戏公平性，请勿使用模拟器游戏，建议前往df.qq.com下载PC客户端体验"),
        DeleteAccountFail = NSLOCTEXT("LoginModule", "Lua_LoginView_DeleteAccountFail", "账号注销失败"),
-- BEGIN VIRTUOS MODIFICATION
        DeleteAccountFinish = NSLOCTEXT("LoginModule", "Console_Lua_LoginView_DeleteAccountFinish", "您的账号已完成注销，点击确认将退出游戏。"),
        CrossPlayPrivilegeChanged = NSLOCTEXT("LoginModule", "Console_Lua_LoginView_CrossPlayPrivilegeChanged", "检测到跨平台游玩权限与当前队伍设置不符，将退出当前队伍并应用新设置"),
-- END VIRTUOS MODIFICATION
        AccountForbiddenLowVersionLogin = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountForbiddenLowVersionLogin", "您已经登录过新版本，无法使用旧版本进入游戏。若希望用此平台进入游戏，请使用最新版本的客户端。"),
        AccountVerWhiteListFailed = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountVerWhiteListFailed", "当前版本不在白名单"),
        ErrorAuthFailed = NSLOCTEXT("LoginModule", "Lua_LoginView_ErrorAuthFailed", "登录鉴权失败，请重新授权"),
        AccountCnSteamForbidOverseaUser = NSLOCTEXT("LoginModule", "Lua_LoginView_AccountCnSteamForbidOverseaUser", "很抱歉，由于您的登录地区IP存在异常，已被限制登录，请关闭加速器等网络工具后再次尝试"),
        AccountInvalidClientVersion =  NSLOCTEXT("LoginModule", "Lua_LoginView_AccountInvalidClientVersion", "非法的客户端版本"),
        TipParentControlOverTimeLimit = NSLOCTEXT("LoginModule", "Lua_LoginView_TipParentControlOverTimeLimit", "Your play time for the day has reached the parent/guardian's limit, please take a break and play again tomorrow."),
        TipParentControlOverNoAccess = NSLOCTEXT("LoginModule", "Lua_LoginView_TipParentControlOverNoAccess", "No access have been set by your parent/guardian at the current time, please contact your parent/guardian for details."),
        TipParentControlOverTimeLimitInGame = NSLOCTEXT("LoginModule", "Lua_LoginView_TipParentControlOverTimeLimitInGame", "You have reached the agreed time limit, please leave the game and take some rest."),
        TipParentControlOverNoAccessInGame = NSLOCTEXT("LoginModule", "Lua_LoginView_TipParentControlOverNoAccessInGame", "Your play time has exceeded the time set by your parent/guardian, please leave the game and take a break."),
        NeedSwitchAccount = NSLOCTEXT("LoginModule", "Lua_LoginView_NeedSwitchAccount", "此账号与当前游戏登录的账号不一致，是否切换账号？"),
        WeGameMSDKError = NSLOCTEXT("LoginModule", "Lua_LoginView_WeGameMSDKError", "您的登录信息有误"),
        NameHasInvalidChar = NSLOCTEXT("LoginModule", "Lua_LoginView_NameHasInvalidChar", "昵称含有生僻字符，请修改"),

        RuleLinkUrl_Global = NSLOCTEXT("LoginModule", "Lua_LoginView_RuleLinkUrl_Global", "我已详细阅读并同意<dfmrichtext style=\"Login_Rules\" link=\"https://www.playdeltaforce.com/terms-of-use.html\">Delta Force用户协议</>、<dfmrichtext style=\"Login_Rules\" link=\"https://www.playdeltaforce.com/privacy-policy.html\">Delta Force隐私政策</>"),
        RuleLinkUrl_Garena = NSLOCTEXT("LoginModule", "Lua_LoginView_RuleLinkUrl_Garena", "我已阅读并同意<dfmrichtext style=\"Login_Rules\" link=\"https://contentgarena-a.akamaihd.net/legal/tos/tos_en.html\">服务条款</>、<dfmrichtext style=\"Login_Rules\" link=\"https://contentgarena-a.akamaihd.net/legal/pp/pp_en.html\">隐私协议</>"),
        PrivacyPanelTitle = NSLOCTEXT("LoginModule", "Lua_PrivacyPanel_PrivacyPanelTitle", "合规确认"),
        PrivacyBtnTitle = NSLOCTEXT("LoginModule", "Lua_PrivacyPanel_PrivacyBtnTitle", "查看"),
        UserAgreement = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_UserAgreement", "用户协议"),
        PrivacyAgreement = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_PrivacyAgreement", "隐私协议"),
        OpenUrl = NSLOCTEXT("LoginModule", "Lua_LoginView_OpenUrl", "立即前往"),
        CustomerHelp = NSLOCTEXT("LoginModule", "Lua_LoginView_CustomerHelp", "帮助"),
    },

    NickName = {
        NickNameMinLength = 1,
        NickNameMaxLength = 14,
    },

    -- 返回登录界面的gameflow事件
    flowEvtToEnterLogin = LuaGameFlowEvent:NewIns("flowEvtToEnterLogin", "Login"),
    flowEvtLoginSuccess = LuaGameFlowEvent:NewIns("flowEvtLoginSuccess", "Login"),
    flowEvtLoginFailed = LuaGameFlowEvent:NewIns("flowEvtLoginFailed", "Login"),
    flowEvtReOpenLogin = LuaGameFlowEvent:NewIns("flowEvtReOpenLogin", "Login"), -- 语言切换后需要重新打开登录界面

    Events = {
        evtServerGroupSelect = LuaEvent:NewIns("evtServerGroupSelect"),
        evtServerAddrChanged = LuaEvent:NewIns("evtServerAddrChanged"),
        evtGetRandomPlayerName = LuaEvent:NewIns("evtGetRandomPlayerName"),
        
        evtVersionTypeSelect = LuaEvent:NewIns("evtVersionTypeSelect"),
        evtOnLanguageChanged = LuaEvent:NewIns("evtOnLanguageChanged"),
        evtOnLoginStartDirectLogin = LuaEvent:NewIns("evtOnLoginStartDirectLogin"),
        evtOnLoginStartGetThirdPartInfo = LuaEvent:NewIns("evtOnLuaGetThirdPartInfo"),
        evtOnLoginStartConnect = LuaEvent:NewIns("evtOnLoginStartConnect"),
        evtOnLoginStartLoginReq = LuaEvent:NewIns("evtOnLoginStartLoginReqReq"),
        evtOnGuideStartReport = LuaEvent:NewIns("evtOnGuideStartReport"),  -- INTL事件上报所需的引导开始事件
        -- evtOnLoginStartGetNeccesaryData = LuaEvent:NewIns("evtOnLoginStartGetNeccesaryData"),
        evtOnLoginSuccess = LuaEvent:NewIns("evtOnLoginSuccess"),
        evtOnStartRegister = LuaEvent:NewIns("evtOnStartRegister"),
        evtOnRegisterSuccess = LuaEvent:NewIns("evtOnRegisterSuccess"),
        evtOnRegisterFail = LuaEvent:NewIns("evtOnRegisterFail"), -- 崩溃及直接杀进程时无法统计注册失败。
        evtOnLoginFailed = LuaEvent:NewIns("evtOnLoginFailed"),
        evtOnGatewayKickPlayer = LuaEvent:NewIns("evtOnGatewayKickPlayer"),
        -- DFM_HD start
        evtShowUrl = LuaEvent:NewIns("evtShowUrl"),
        -- DFM_HD end
        evtOnRefreshVersion = LuaEvent:NewIns("evtOnRefreshVersion"),
        evtOnRegisterFinish = LuaEvent:NewIns("evtOnRegisterFinish"),
        evtOnGetTaskBtnClick = LuaEvent:NewIns("evtOnGetTaskBtnClick"),
        evtOnSetServiceAgree = LuaEvent:NewIns("evtOnSetServiceAgree"),
        evtOnRefreshToken =  LuaEvent:NewIns("evtOnRefreshToken"),
        evtOnPrivacyStatusChange = LuaEvent:NewIns("evtOnPrivacyStatusChange"),
        evtOnPrivacyPanelConfirm = LuaEvent:NewIns("evtOnPrivacyPanelConfirm"),
        evtOnRefreshLoginPanel = LuaEvent:NewIns("evtOnRefreshLoginPanel"),
    },

    BGMediaRowName = "LoginMedia", -- 背景视频配置(具体信息配置在MediaResTable中)

    LoginSprite={
        [EChannelType.kChannelNone] = "",
        [EChannelType.kChannelWechat] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0006.Login_Icon_0006'",
        [EChannelType.kChannelQQ] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0005.Login_Icon_0005'",
        [EChannelType.kChannelGuest] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/CommonHall_Login_Icon_0101.CommonHall_Login_Icon_0101'",
        [EChannelType.kChannelFacebook] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0010.Login_Icon_0010'",
        [EChannelType.kChannelGameCenter] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0007.Login_Icon_0007'",
        [EChannelType.kChannelGooglePlay] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0009.Login_Icon_0009'",
        [EChannelType.kChannelTwitter] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0011.Login_Icon_0011'",
        [EChannelType.kChannelGarena] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0017.Login_Icon_0017'",
        [EChannelType.kChannelLine] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0012.Login_Icon_0012'",
        [EChannelType.kChannelApple] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0008.Login_Icon_0008'",
        [EChannelType.kChannelKaKao] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0013.Login_Icon_0013'",
        [EChannelType.kChannelLevelInfinite] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0007.Login_Icon_0007'",
        [EChannelType.kChannelDiscord] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0014.Login_Icon_0014'",
        [EChannelType.kChannelSteam] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0015.Login_Icon_0015'",
        [EChannelType.kChannelEpic] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Icon_0016.Login_Icon_0016'",
    },
    LoginLoc={
        [EChannelType.kChannelNone] = "...",
        [EChannelType.kChannelWechat] = NSLOCTEXT("LoginModule", "Lua_LoginView_WXLogin", "微信登录"),
        [EChannelType.kChannelQQ] = NSLOCTEXT("LoginModule", "Lua_LoginView_QQLogin", "QQ登录"),
        [EChannelType.kChannelGuest] = NSLOCTEXT("LoginModule", "Lua_LoginView_GuestLogin", "游客登录"),
        [EChannelType.kChannelFacebook] = NSLOCTEXT("LoginModule", "Lua_LoginView_FaceBookLogin", "FaceBook"),
        [EChannelType.kChannelGameCenter] = NSLOCTEXT("LoginModule", "Lua_LoginView_GameCenterLogin", "游戏中心"),
        [EChannelType.kChannelGooglePlay] = NSLOCTEXT("LoginModule", "Lua_LoginView_GoogleLogin", "Google"),
        [EChannelType.kChannelTwitter] = NSLOCTEXT("LoginModule", "Lua_LoginView_TwitterLogin", "X"),
        [EChannelType.kChannelGarena] = NSLOCTEXT("LoginModule", "Lua_LoginView_GarenaLogin", "Garena"),
        [EChannelType.kChannelLine] = NSLOCTEXT("LoginModule", "Lua_LoginView_LineLogin", "Line"),
        [EChannelType.kChannelApple] = NSLOCTEXT("LoginModule", "Lua_LoginView_AppleLogin", "Sign in with Apple"),
        [EChannelType.kChannelKaKao] = NSLOCTEXT("LoginModule", "Lua_LoginView_KaKaoLogin", "KaKao"),
        [EChannelType.kChannelLevelInfinite] = NSLOCTEXT("LoginModule", "Lua_LoginView_LevelInfiniteLogin", "EMAIL"),
        [EChannelType.kChannelDiscord] = NSLOCTEXT("LoginModule", "Lua_LoginView_DiscordLogin", "Discord"),
        [EChannelType.kChannelSteam] = NSLOCTEXT("LoginModule", "Lua_LoginView_SteamLogin", "Steam"),
        [EChannelType.kChannelEpic] = NSLOCTEXT("LoginModule", "Lua_LoginView_EpicLogin", "Epic"),

    },
    LoginBgSprite={
        [EChannelType.kChannelNone] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_04.Login_De_04'",
        [EChannelType.kChannelWechat] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_04.Login_De_04'",
        [EChannelType.kChannelQQ] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelGuest] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_04.Login_De_04'",
        [EChannelType.kChannelFacebook] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelGameCenter] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_04.Login_De_04'",
        [EChannelType.kChannelGooglePlay] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelTwitter] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelGarena] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelLine] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelApple] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelKaKao] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelLevelInfinite] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelDiscord] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelSteam] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
        [EChannelType.kChannelEpic] = "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_De_03.Login_De_03'",
    },
    LoginSpriteColor={
        [EChannelType.kChannelNone] = FLinearColor("#64B86AFF"),
        [EChannelType.kChannelWechat] = FLinearColor("#64B86AFF"),
        [EChannelType.kChannelQQ] = FLinearColor("#66A0C6FF"),
        [EChannelType.kChannelGuest] = FLinearColor("#1B1B26FF"), -- FLinearColor("#5BD880FF"),
        [EChannelType.kChannelFacebook] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelGameCenter] = FLinearColor("#5BD880FF"),
        [EChannelType.kChannelGooglePlay] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelTwitter] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelGarena] = FLinearColor("#D13732FF"),
        [EChannelType.kChannelLine] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelApple] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelKaKao] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelLevelInfinite] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelDiscord] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelSteam] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelEpic] = FLinearColor("#FFFFFFFF"),
    },
    LoginTextColor={
        [EChannelType.kChannelNone] = FLinearColor("#000000FF"),
        [EChannelType.kChannelWechat] = FLinearColor("#000000FF"),
        [EChannelType.kChannelQQ] = FLinearColor("#000000FF"),
        [EChannelType.kChannelGuest] = FLinearColor("#000000FF"),
        [EChannelType.kChannelFacebook] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelGameCenter] = FLinearColor("#000000FF"),
        [EChannelType.kChannelGooglePlay] = FLinearColor("#000000FF"),
        [EChannelType.kChannelTwitter] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelGarena] = FLinearColor("#D13732FF"),
        [EChannelType.kChannelLine] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelApple] = FLinearColor("#000000FF"),
        [EChannelType.kChannelKaKao] = FLinearColor("#000000FF"),
        [EChannelType.kChannelLevelInfinite] = FLinearColor("#000000FF"),
        [EChannelType.kChannelDiscord] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelSteam] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelEpic] = FLinearColor("#000000FF"),
    },
    LoginBgColor={
        [EChannelType.kChannelNone] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelWechat] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelQQ] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelGuest] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelFacebook] = FLinearColor("#1877F2FF"),
        [EChannelType.kChannelGameCenter] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelGooglePlay] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelTwitter] = FLinearColor("#000000FF"),
        [EChannelType.kChannelGarena] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelLine] = FLinearColor("#52B24DFF"),
        [EChannelType.kChannelApple] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelKaKao] = FLinearColor("#FFF812FF"),
        [EChannelType.kChannelLevelInfinite] = FLinearColor("#FFFFFFFF"),
        [EChannelType.kChannelDiscord] = FLinearColor("#5A65EAFF"),
        [EChannelType.kChannelSteam] = FLinearColor("#3C628DFF"),
        [EChannelType.kChannelEpic] = FLinearColor("#FFFFFFFF"),
    },
    PCLogin = {
        QQUrl = "https://graph.qq.com/oauth2.0/authorize?response_type=%s&client_id=%d&redirect_uri=%s&state=%s",
        WeChatUrl = "https://open.weixin.qq.com/connect/qrconnect?appid=%s&redirect_uri=%s&response_type=%s&scope=%s&state=%s#wechat_redirect",
        RedirectUri = "https://df.qq.com/",
        WeChatScope = "snsapi_login",
        ResponseType = "code",
        State = "test",
    },
    LoginBtnBg = {
        "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Bg_01.Login_Bg_01'",
        "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Bg_02.Login_Bg_02'",
        "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_Bg_03.Login_Bg_03'"
    },
    LoginBtnBgGlobal = {
        "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_White.Login_White'",
        "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_White.Login_White'",
        "PaperSprite'/Game/UI/UIAtlas/System/Login/BakedSprite/Login_White.Login_White'"
    },
    CountryLimitUrl = "https://www.playdeltaforceconsole.com/redirect/",
    MaterialPath = "/Game/Models/Weapons/MilitaryProps/props_outsider_system_694/Materials/props_outsider_system_695_04",

    GlobalPrivacy = {
        { 
            Name = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_UserAgreement", "用户协议"),
            Url = "https://www.playdeltaforce.com/terms-of-use.html"
        },
        {
            Name = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_PrivacyAgreement", "隐私协议"),
            Url = "https://www.playdeltaforce.com/privacy-policy.html"
        }
    },

    GarenaPrivacy = {
        { 
            Name = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_UserAgreement_Garena", "用户协议"),
            Url = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_UserAgreement_URL_Garena", "https://contentgarena-a.akamaihd.net/legal/tos/tos_en.html")
        },
        {
            Name = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_PrivacyAgreement_Garena", "隐私协议"),
            Url = NSLOCTEXT("LoginModule", "Lua_PrivacyItem_PrivacyAgreement_URL_Garena", "https://contentgarena-a.akamaihd.net/legal/pp/pp_en.html")
        }
    }
}

LoginConfig.GetRegisterErrNoOpeningTimeTips = function(openingNextBeginTime, openingNextEndTime)
    if openingNextBeginTime and openingNextBeginTime > 0 then
        openingNextEndTime = openingNextEndTime and openingNextEndTime or 0
        local param = {
            ["BeginTime"] = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(openingNextBeginTime),
            ["EndTime"] = TimeUtil.TransTimestamp2YYMMDDHHMMSSCNStr(openingNextEndTime)
        }
        if openingNextEndTime > 0 then
            return StringUtil.Key2StrFormat(LoginConfig.Loc.RegisterErrNoOpeningTimeSpan, param)
        else
            return StringUtil.Key2StrFormat(LoginConfig.Loc.RegisterErrNoOpeningTime, param)
        end
    else
        return LoginConfig.Loc.RegisterErrNoOpening
    end
end        

return LoginConfig
