----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMItemDetail)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ItemDetailView : LuaUIBaseView
local ItemDetailView = ui("ItemDetailView")
local ItemDetailLogic = require "DFM.Business.Module.ItemDetailModule.ItemDetailLogic"
local ItemLabelMarkBtn = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemLabelMarkBtn"
local ItemDetailConfig = require "DFM.Business.Module.ItemDetailModule.ItemDetailConfig"
local UModularWeaponDesc = import "ModularWeaponDesc"
local FWeaponDataAttribute = import "WeaponDataAttribute"
local WeaponHelperTool = require"DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local ShopHelperTool = require "DFM.StandaloneLua.BusinessTool.ShopHelperTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local FRTIParamData = import "RTIParamData"
local UGlobalConstWrapper = import "GlobalConstWrapper"
local UGPBattleFieldSystem = import "GPBattleFieldSystem"

function ItemDetailView:Ctor()
	self._wtItemDetailTitle = self:Wnd("wItemDetailTitle", UIWidgetBase)
	self._wtItemIcon = self:Wnd("wItemviewImage", UIWidgetBase):Wnd("ItemIcon", UIImage)
	-- self._wtShowModelBtn = self:Wnd("wShowModelButton", UIButton)
	-- if self._wtShowModelBtn then
	-- 	self._wtShowModelBtn:Event("OnClicked", self._OnShowModelBtnClick, self)
	-- 	self._wtShowModelBtn:SetVisibility(ESlateVisibility.Collapsed)
	-- end
	self._wtDividLine = self:Wnd("DFImage_103", UIImage)
	self._wtSubNameTxt = self:Wnd("TextBlock_155", UITextBlock)

	self._wtLabelMarkBtn = self:Wnd("wLabelMarkBtn", ItemLabelMarkBtn)
	self._wtItemSpaceGrid = self:Wnd("wItemSpaceGrid", UIWidgetBase)
	--self._wtItemSpacePanel = self:Wnd("wItemSpacePanel", UIWidgetBase)
	self._wtWeightText = self:Wnd("wWeightText", UITextBlock)

	self._wtTimeLimitItem = self:Wnd("wTimeLimitItem", UIWidgetBase)
	self._wtTimeLimitText = self._wtTimeLimitItem:Wnd("wTimeText", UITextBlock)

	self._wtItemBindTipsAlignWidget = self:Wnd("wBindTipsAlignWidget", UIImage)
	self._wtItemBindBtn = self:Wnd("wItemCheckbox", UIWidgetBase)
	self._wtItemBindRealBtn = self._wtItemBindBtn:Wnd("wWeaponDetailBtn_1", UIButton)

	self._wtItemTeamBindBtn = self:Wnd("wItemCheckbox_1", UIWidgetBase)
	self._wtItemTeamBindRealBtn = self._wtItemTeamBindBtn:Wnd("wWeaponDetailBtn_1", UIButton)

	self._wtItemReadyBreakBindBtn = self:Wnd("wItemCheckbox_2", UIWidgetBase)
	self._wtItemReadyBreakBindRealBtn = self._wtItemReadyBreakBindBtn:Wnd("wWeaponDetailBtn_1", UIButton)

	self._wtScrollBoxContent = self:Wnd("DFScrollBox_0", UIScrollBox)
	--- BEGIN MODIFICATION @ VIRTUOS
	if IsHD() then
		if not self._wtScrollBoxContent then
			self._wtScrollBoxContent = self:Wnd("wScrollBoxContent", UIScrollBox)
		end
	end
	--- END MODIFICATION
	self._onContentScrollCallback = nil
	self._wtBtnPanel = self:Wnd("WBP_ItemDetailPanelBtn", UIWidgetBase)
	self._btnParentSlot = self._wtBtnPanel:Wnd("DFWrapBox_91", UIWidgetBase)
	self._wtBtnPanel:Collapsed()

	if IsHD() then
		self._wtItemBindRealBtn:Event("OnHovered", self._OnItemBindBtnHovered, self, self._wtItemBindBtn)
		self._wtItemBindRealBtn:Event("OnUnhovered", self._OnItemBindBtnUnHovered, self, self._wtItemBindBtn)
		--self._wtTipsAnchor_Bind = UIUtil.WndTipsAnchor(self, "_wtTipsAnchor_Bind", self._OnShowBindTip, self._OnHideBindTip)

		self._wtItemTeamBindRealBtn:Event("OnHovered", self._OnItemBindBtnHovered, self, self._wtItemTeamBindBtn)
		self._wtItemTeamBindRealBtn:Event("OnUnhovered", self._OnItemBindBtnUnHovered, self, self._wtItemTeamBindBtn)
		--self._wtTipsAnchor_TeamBind = UIUtil.WndTipsAnchor(self, "_wtTipsAnchor_TeamBind", self._OnShowBindTip, self._OnHideBindTip)

		self._wtItemReadyBreakBindRealBtn:Event("OnHovered", self._OnItemReadyBreakBindBtnClicked, self)
		self._wtItemReadyBreakBindRealBtn:Event("OnUnhovered", self._OnItemReadyBreakBindBtnClicked, self)
		--self._wtTipsAnchor_ReadyBreakBind = UIUtil.WndTipsAnchor(self, "_wtTipsAnchor_ReadyBreakBind", self._OnItemReadyBreakBindBtnClicked, self._OnItemReadyBreakBindBtnClicked)
		self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "wtDFTipsAnchor", self._ShowCollectTip, self._HideCollectTip)
		self._wtLabelMarkBtn._wtLabelMarkCheckBox:Event("OnCheckStateChanged", self._OnCollectBtnClicked, self)
	else
		self._wtItemBindRealBtn:Event("OnClicked", self._OnItemBindBtnClicked, self, self._wtItemBindBtn)
		self._wtItemTeamBindRealBtn:Event("OnClicked", self._OnItemBindBtnClicked, self, self._wtItemTeamBindBtn)
		self._wtItemReadyBreakBindRealBtn:Event("OnClicked", self._OnItemReadyBreakBindBtnClicked, self)
	end

	self._wtItemDetailContent = self:Wnd("wItemDetailContent", UIWidgetBase)
	self._wtContentSizeBox = self:Wnd("wContentSizeBox", UISizeBox)

	self._wtItemPoorWeaponBtn = self:Wnd("wItemPoorWeaponCheckBox", UIWidgetBase)
	self._wtItemPoorWeaponRealBtn = self._wtItemPoorWeaponBtn:Wnd("wWeaponDetailBtn_1", UIButton)
	self._wtItemPoorWeaponRealBtn:Event("OnClicked", self._OnPoorWeaponBtnClicked, self)

	self._wtCurrencyRichText = self:Wnd("wCurrencyText", UITextBlock)
	self._wBluePrintItem = self:Wnd("wBluePrintItem", UIWidgetBase)
	self._skinDesc = self:Wnd("DFTextBlock_713", UITextBlock)
	self._giftBg = self:Wnd("WBP_Itemview_Collections", UIWidgetBase)
	self._wticonItem = self:Wnd("wtIconItem", UIWidgetBase)
	-- self._wticonItemInGame = self:Wnd("CanvasPanel_55", UIWidgetBase)
	-- if self._wticonItemInGame then
	-- 	self._wticonItemInGame:SetVisibility(ESlateVisibility.Visible)
	-- end

	-- 隐藏款挂饰
	self._wtOrnamentsPlusImg = self:Wnd("DFImage_152", UIImage)
	self._wtOrnamentsPlusText = self:Wnd("DFTextBlock_170", UITextBlock)
	self._wtOrnamentsPlusImg:Collapsed()
	self._wtOrnamentsPlusText:Collapsed()

	-- 放大镜
	self._wtDetailButton = self:Wnd("Magnifier", DFCommonButtonOnly)
	if self._wtDetailButton then
		self._wtDetailButton:Event("OnClicked", self._OnDetailButtonCallBack, self)
		self._wtDetailButton:Collapsed()
	end

	-- 真放大镜
	self._wtDetailButton2 = self:Wnd("WBP_CommonIconButton", DFCommonButtonOnly)
	if self._wtDetailButton2 then
		self._wtDetailButton2:Event("OnClicked", self._OnDetailButtonCallBack2, self)
		self._wtDetailButton2:Collapsed()
	end

	-- 局内限定标记
	self._wtLimitInGame = self:Wnd("DFCanvasPanel_114", UIWidgetBase)

	self._wtTransBtnList = nil	-- 快捷转移列表

	self._bFastEquippable = false
	self._bShowFullImg = true
	self._bShowBtn = true
	self._fOnShowModelPanelCallback = nil

	self._fTopLeftSlotInitCB = nil
	self._fTopBtnSlotInitCB = nil
	self._fMiddleBtnSlotInitCB = nil
	self._fBottomBtnSlotInitCB = nil
	self._fFloorBtnSlotInitCB = nil

	self._bInMp = false
	self._parent = nil
	self._bigTipsAlignWidget = self._wtItemDetailTitle	 --	带关闭按钮的tips位置停靠的widget，一般是弹窗详情页的标题
	self._bCollectionItem = false

	self._tradeMode = ETradeMode.Sell

	self.showNum = nil
	self.bOutBtn = false

	self._bNeedHideUnequipBulletBtn = false
	self._bPlayAnimOnShow = true
	self._bShowIconItem = true -- 默认itemicon可见
	self.btnNum=0
	
	self:InjectLua()

	if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
        self:SetIsIgnoreWorldForceGc(true)
    end

	self.contentHeight = 0
end

function ItemDetailView:Destroy()
	if self._autoScrollWidget then
		self._autoScrollWidget:Release()
		self._autoScrollWidget = nil
    end
end

------------------------------------ Override function ------------------------------------
function ItemDetailView:OnInitExtraData()
end

function ItemDetailView:OnOpen()
	self:_InitDetailViewTopLeftSlot()
	self:_InitDetailViewTopBtnSlot()
	self:_InitDetailViewMiddleBtnSlot()
	self:_InitDetailViewBottomBtnSlot()
	self:_InitDetailViewFloorBtnSlot()
	self:_InitAutoScrollWidget()
end

function ItemDetailView:OnClose()
	-- Facade.UIManager:ClearSubUIByParent(self, self._wBottomBtnSlot)
	Facade.UIManager:ClearSubUIByParent(self, self.wUnderContentSlot)
    Facade.UIManager:ClearSubUIByParent(self, self._btnParentSlot)
end

function ItemDetailView:OnShow()
	-- TODO: auto call subview OnShow
	-- self._wtItemDetailViewImage:Show()

	self._autoScrollWidget:EnableComponent(true)
	if DFHD_LUA == 1 then
	else
		if self._bPlayAnimOnShow then
			self:PlayWidgetAnim(self.WBP_ItemDetailView_in)
		end
	end
	self:_AddEventListener()
end

function ItemDetailView:OnHide()
	-- TODO: auto call subview OnHide
	-- self._wtItemDetailViewImage:Hide()

	self._autoScrollWidget:EnableComponent(false)
	-- self:PlayWidgetAnim(self.WBP_ItemDetailView_out)
	self:_RemoveEventListener()
end

------------------------------------ Event listener function ------------------------------------

-- function ItemDetailView:_OnShowModelBtnClick()
-- 	Facade.UIManager:AsyncShowUI(UIName2ID.ItemDetailModelPanel, nil, nil, self._itemStruct)
-- 	if self._fOnShowModelPanelCallback then
-- 		safecall(self._fOnShowModelPanelCallback)
-- 	end
-- 	Module.ItemDetail:CloseItemDetailPanel()
-- end

function ItemDetailView:_OnWeaponDetailBtnClicked()
	Facade.SoundManager:PlayUIAudioEvent(GlobalUIAudioRes.UIClick)
	-- TODO
	-- 跳转改枪台，接口待处理
end

function ItemDetailView:_OnAdapterDetailBtnClicked()
	Facade.SoundManager:PlayUIAudioEvent(GlobalUIAudioRes.UIClick)
	Module.WeaponAssembly:OpenPartStyleChangeView(self._itemStruct)
end

function ItemDetailView:_OnItemBindBtnClicked(btnIns)
	loginfo("ItemDetailView:_OnItemBindBtnClicked")
	if not self._itemStruct then
		logerror("not self._itemStruct!")
		return
	end

	local fOnClose = function()
		btnIns:SetCppValue("Type", 0)
		btnIns:SetStyle()
	end
	-- 切换选中态
	local handle = Module.ItemDetail:OpenItemBindTips(self._itemStruct.bindType, btnIns, self._wtItemBindTipsAlignWidget, fOnClose)
	if handle then
		btnIns:SetCppValue("Type", 1)
		btnIns:SetStyle()
		loginfo("ItemDetailView:_OnItemBindBtnClicked 1")
	else
		btnIns:SetCppValue("Type", 0)
		btnIns:SetStyle()
		loginfo("ItemDetailView:_OnItemBindBtnClicked 2")
	end
end

function ItemDetailView:_OnItemBindBtnHovered(btnIns)
	self:_OnItemBindBtnClicked(btnIns)
end

function ItemDetailView:_OnItemBindBtnUnHovered(btnIns)
	Module.ItemDetail:CloseItemCommonMiniTips(btnIns)
	btnIns:SetCppValue("Type", 0)
	btnIns:SetStyle()
end

function ItemDetailView:_TipsIsClose(bClose)
    -- 当 tips 关闭
	self._wtItemBindBtn:SetCppValue("Type", 0)
	self._wtItemBindBtn:SetStyle()
	self._wtItemTeamBindBtn:SetCppValue("Type", 0)
	self._wtItemTeamBindBtn:SetStyle()
end

function ItemDetailView:_OnItemReadyBreakBindBtnClicked()
	Module.ItemDetail:OpenReadyBreakWeaponTips(self._wtItemBindBtn, self._wtItemBindTipsAlignWidget)
end

function ItemDetailView:_OnPoorWeaponBtnClicked()
	Module.ItemDetail:OpenPoorWeaponTips(self._wtItemPoorWeaponBtn, self._wtItemBindTipsAlignWidget)
end

function ItemDetailView:_OnItemDetailViewHide()
	self:Collapsed()
end

function ItemDetailView:_OnItemMove(itemMoveCmd)
	local item = itemMoveCmd.item
	if self._itemStruct and itemMoveCmd.Reason == PropChangeType.Modify and item and item.gid == self._itemStruct.gid then
		self._itemStruct = item
		self:_UpdateDetailViewImage()
		self:_UpdateDetailViewMiddleBtnSlot()
		self:_UpdateWeaponBluePrint()
		self:_SetItemPrice()
	end
end

function ItemDetailView:_OnAutpScrolled(CurOffset)
	if CurOffset >= 392 + 38 then
		self._wtBtnPanel:SelfHitTestInvisible()
	elseif CurOffset < 392 + 38 then
		self._wtBtnPanel:Collapsed()
	end
end

function ItemDetailView:_InitAutoScrollWidget()
    self._wtUpMask = self:Wnd("wtUpMask", CommonDragDropMask)
    self._wtUpMask:Collapsed()
    self._wtDownMask = self:Wnd("wtDownMask", CommonDragDropMask)
    self._wtDownMask:Collapsed()

    ---@type AutoScrollParam
    local param = {}
    param.upMask = self._wtUpMask
    param.downMask = self._wtDownMask
    param.scrollSpeed = DFMGlobalConst.GetDepositConstNumber("AutoScrollSpeed", 2)
    param.bindScrollBox = self._wtScrollBoxContent
    param.fScrollCallback = self._OnAutpScrolled
    param.scrollCaller = self
    self._autoScrollWidget = Module.CommonWidget:CreateScrollComponent(param)
    self._autoScrollWidget:SetSleepAndMove(0.3, 0.3, 2 * ItemConfig.DefaultItemViewSize)
end

------------------------------------ Private function ------------------------------------
function ItemDetailView:_AddEventListener()
	self:AddLuaEvent(Module.ItemDetail.Config.evtItemDetailViewHide, self._OnItemDetailViewHide, self)
	self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
	self:AddLuaEvent(Server.LootingServer.Events.evtLootingItemMove, self._OnItemMove, self)
	self:AddLuaEvent(ItemDetailConfig.evtItemDetailTipsClose, self._TipsIsClose, self)
end

function ItemDetailView:_RemoveEventListener()
	self:RemoveAllLuaEvent()
end

---------- 初始化Slot，这里主要处理Solt的动态挂载逻辑 ----------
function ItemDetailView:_InitDetailViewTopLeftSlot()
	if self._fTopLeftSlotInitCB ~= nil then
		self._fTopLeftSlotInitCB()
	end
 end 

function ItemDetailView:_InitDetailViewTopBtnSlot()
	-- if self:GetViewParent(true) and self:GetViewParent(true)._wtShowModelBtn then
	-- -- 其他固定挂载逻辑 
	-- else
	-- end
	if self._fTopBtnSlotInitCB ~= nil then
		self._fTopBtnSlotInitCB()
	end
end

function ItemDetailView:_InitDetailViewMiddleBtnSlot()
	if self._fMiddleBtnSlotInitCB ~= nil then
		self._fMiddleBtnSlotInitCB()
	end
end

function ItemDetailView:_InitDetailViewBottomBtnSlot()
	if self._fBottomBtnSlotInitCB ~= nil then
		self._fBottomBtnSlotInitCB()
	end
end

function ItemDetailView:_InitDetailViewFloorBtnSlot()
	if self._fFloorBtnSlotInitCB ~= nil then
		self._fFloorBtnSlotInitCB()
	end
end

-------------------------------------------------------------

-- 更新详情页物品标题
function ItemDetailView:_UpdateDetailViewTitle()
	self._wtItemDetailTitle:SetItemTitle(self._itemStruct)
end

function ItemDetailView:_UpdateDetailViewSubTitle()
	self._wtSubNameTxt:SetText(self._itemStruct.subName)
end

function ItemDetailView:_UpdateDividLine()
	if self._itemStruct.subName == "" or self._wtCurrencyRichText.Visibility == ESlateVisibility.Collapsed then
		if self._wtDividLine then
			self._wtDividLine:Collapsed()
		end
	else
		if self._wtDividLine then
			self._wtDividLine:SelfHitTestInvisible()
		end
	end
end

function ItemDetailView:_UpdateDetailViewImage()
	if self._bShowFullImg then
		-- self._wtItemDetailViewImage:UpdateItem(self._itemStruct)
		if self._itemStruct:IsWeaponFromItemType() then
			local RTIParam = FRTIParamData()
			RTIParam.bHandleWeaponSkinStaticIcon = false
			RuntimeIconTool.SetItemIcon(self._itemStruct, self._wtItemIcon, RTIParam)
		elseif not ItemOperaTool.CheckRunWarehouseLogic() then
			local bUseSafeBoxSkin = false
			if self._itemStruct.InSlot and self._itemStruct.InSlot.SlotType == ESlotType.SafeBox then
				local safeBoxSkinID = Module.Looting:GetSafeBoxSkinID()
				if safeBoxSkinID ~= 0 then
					local itemAsset = ItemConfigTool.GetItemAssetById(safeBoxSkinID)
					if itemAsset then
						bUseSafeBoxSkin = true
						RuntimeIconTool.SetStaticIcon(itemAsset.itemIconPath, self._wtItemIcon)
					end
				end
			end
			if not bUseSafeBoxSkin then
				RuntimeIconTool.SetItemIcon(self._itemStruct, self._wtItemIcon)
			end
		else
			RuntimeIconTool.SetItemIcon(self._itemStruct, self._wtItemIcon)
		end
	end
end

function ItemDetailView:_UpdateDetailViewTopBtn()
	-- local weaponFeature = self._itemStruct:GetFeature(EFeatureType.Weapon)
	-- local adpaterFeature =  self._itemStruct:GetAdapter()
	-- if weaponFeature and weaponFeature:IsWeapon() and not weaponFeature:IsMeleeWeapon() then --1.武器
		--self._wtWeaponStyleBtn:SetVisibility(ESlateVisibility.Visible)	-- TODO 等功能实现再打开
		--self._wtWeaponStyleBtn:SetVisibility(ESlateVisibility.Collapsed)
		--self._wtAdapterStyleBtn:SetVisibility(ESlateVisibility.Collapsed)
	-- elseif adpaterFeature then
		--self._wtWeaponStyleBtn:SetVisibility(ESlateVisibility.Collapsed)
		-- local styleRuleId = adpaterFeature:GetStyleRuleId()
		-- local inventoryItem = Server.InventoryServer:GetItemByGid(self._itemStruct.gid)
		--self._wtAdapterStyleBtn:SetVisibility(ESlateVisibility.Collapsed)
	-- else
		--self._wtWeaponStyleBtn:SetVisibility(ESlateVisibility.Collapsed)
		--self._wtAdapterStyleBtn:SetVisibility(ESlateVisibility.Collapsed)
	-- end
	self._wtLabelMarkBtn:SetItem(self._itemStruct.id)
	local itemMainType = ItemHelperTool.GetMainTypeById(self._itemStruct.id)
	-- 武器类、经验、货币、藏品，不显示收藏按钮
	if not ItemDetailLogic.IsShowLabelMarkBtn(self._itemStruct) then
		self:SetLabelMarkBtnVisible(false)
	else
		self:SetLabelMarkBtnVisible(true)
	end
end

function ItemDetailView:_UpdateItemSpace()
	local length, height = self._itemStruct.length, self._itemStruct.width
	if length == 0 or height == 0 or ItemDetailLogic.IsCurrencyItem(self._itemStruct.id)
	or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Adapter and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == ItemConfig.EAdapterItemType.Pendant
	or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == EEquipmentType.SafeBox
	or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == EEquipmentType.KeyChain
	or self._bInMp then
		self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.Collapsed)
		return
	else
		self._wtItemSpaceGrid:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end

	-- 先将所有格子置灰
	for idx = 0, 27 do
		local grid = self._wtItemSpaceGrid:GetChildAt(idx)
		grid:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Basic_White(30%)"))
	end
	-- 枪械道具和其他道具显示不同的格子数量
	if not self._itemStruct:GetFeature(EFeatureType.Weapon) then
		for h = 1, 4 do
			local sIdx = h * 7 - 1
			local grid = self._wtItemSpaceGrid:GetChildAt(sIdx)
			if grid then
				grid:Collapsed()
			end
		end
	else
		for idx = 0, 27 do
			local grid = self._wtItemSpaceGrid:GetChildAt(idx)
			grid:SelfHitTestInvisible()
		end
	end
	for h = 1, height do
		local sIdx = 7 * (h - 1)
		for idx = sIdx, sIdx + length - 1 do
			local grid = self._wtItemSpaceGrid:GetChildAt(idx)
			if grid then
				grid:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("Basic_White"))
			end
		end
	end
end

function ItemDetailView:_UpdateTimeLimitItem()
	-- 不是限时/次道具
	if not self._itemStruct:IsTimeLimitItem() then
		self._wtTimeLimitItem:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	-- 限时/次道具
	self._wtTimeLimitItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	local state, dayLeft, endTime = self._itemStruct:IsTimeLimitItemValid()
	local useDesc = ""
	if state == ETimeLimitState.UpToDate then
		-- 时长
		if self._itemStruct.aginType == PropAgingType.AgingDuration then
			useDesc = string.format(Module.ItemDetail.Config.Loc.canUseTimerDesc, dayLeft) -- 天
		else
			--local yy, mm, dd = TimeUtil.TransUnixTimestamp2YYMMDD(endTime)
			useDesc = string.format(Module.ItemDetail.Config.Loc.canUseDateDesc, TimeUtil.TransUnixTimestamp2YYMMDDStr(endtime, "YY/MM/DD")) -- 至YYMMDD
			if dayLeft < 3 then
				-- 绿色
				self._wtTimeLimitItem:BpSetColor(true)
			else
				self._wtTimeLimitItem:BpSetColor(false)
			end
		end 
	elseif state == ETimeLimitState.CanBeUsed then
		useDesc = StringUtil.PluralTextFormat(Module.ItemDetail.Config.Loc.canUseNumDesc, {["NumOfUse"]=self._itemStruct.useCnt}) -- "可用%d次" 单复数改造[aidenliao]
	else
		useDesc = Module.ItemDetail.Config.Loc.outOfDate
	end
	self._wtTimeLimitText:SetText(useDesc)
	-- 限时物品超时按钮不可见
	if self._bShowBtn then
		self._bShowBtn = state ~= ETimeLimitState.OutOfDate
	end
end

function ItemDetailView:_UpdateDetailViewMiddleBtnSlot()
	local itemFeature = self._itemStruct:GetFeature()
	-- 重量信息
	if self._itemStruct.weight == nil then
		logbox("物品重量为空，请检查。物品id:", self._itemStruct.id)
		self._itemStruct.weight = 0
	end
	if self._bInMp or ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin
		or ItemDetailLogic.IsPrestige(self._itemStruct.id)
		or ItemDetailLogic.IsActivityItem(self._itemStruct.id)
		or ItemDetailLogic.IsCurrencyItem(self._itemStruct.id)
		or ItemDetailLogic.IsExpItem(self._itemStruct.id)
		or (itemFeature and itemFeature.IsSafeBox and itemFeature:IsSafeBox())
		or (itemFeature and itemFeature.IsKeyChain and itemFeature:IsKeyChain())
		or self._itemStruct.weight == 0 then
		self._wtWeightText:SetVisibility(ESlateVisibility.Collapsed)
	else
		if self._itemStruct.weight < 0.1 then
			self._wtWeightText:SetText(ItemDetailConfig.Loc.weigthMin)
		else
			self._wtWeightText:SetText(StringUtil.Key2StrFormat(ItemDetailConfig.Loc.weigth, {["weight"]=string.format("%.2f", self._itemStruct.weight)}))
		end
		self._wtWeightText:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	end

	-- 空间信息
	self:_UpdateItemSpace()

	-- 限时/次信息
	self:_UpdateTimeLimitItem()

	-- 破损信息
	self._wtItemReadyBreakBindBtn:SetVisibility(ESlateVisibility.Collapsed)
	-- local equipmentFeature = self._itemStruct:GetFeature(EFeatureType.Equipment)
	-- local isReadyBreak = false
	-- if equipmentFeature and (equipmentFeature:IsHelmet() or equipmentFeature:IsBreastPlate())then
		-- 判断道具是否为损毁状态
		-- if equipmentFeature.curDurability <= 0 then
		-- 	-- self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
		-- 	-- self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
		-- 	self._wtItemReadyBreakBindBtn:SetVisibility(ESlateVisibility.Visible)
		-- 	self._wtItemReadyBreakBindBtn:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("LightNegative"))
		-- 	isReadyBreak = true
		-- end
		-- if equipmentFeature:IsCantRepair() then
		-- 	-- self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
		-- 	-- self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
		-- 	self._wtItemReadyBreakBindBtn:SetVisibility(ESlateVisibility.Visible)
		-- 	self._wtItemReadyBreakBindBtn:SetColorAndOpacity(Facade.ColorManager:GetLinerColor("LightNegative"))--红色
		-- end
	-- end
	local adapterFeature = self._itemStruct:GetFeature(EFeatureType.Adapter)
	if adapterFeature then
		local attackInfo = adapterFeature:GetAttackerInfo()
		if attackInfo and attackInfo.durabilty == 0 then
			self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
			self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
			self._wtItemReadyBreakBindBtn:SetVisibility(ESlateVisibility.Visible)
			-- isReadyBreak = true
		end
	end

	-- 绑定信息
	if (self._bInMp) or self._bCollectionItem
		or (ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Receiver) and (ItemHelperTool.GetSubTypeById(self._itemStruct.id) == ItemConfig.EWeaponItemType.Melee)
		or (ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == EEquipmentType.SafeBox)
		or (ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Equipment and ItemHelperTool.GetSubTypeById(self._itemStruct.id) == EEquipmentType.KeyChain)
		or (ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.Adapter) and (ItemHelperTool.GetSubTypeById(self._itemStruct.id) == ItemConfig.EAdapterItemType.Pendant) then
		self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
		self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
	else
		if self._itemStruct:CheckIsBind() then
			if self._itemStruct:CheckIsTeammateBind() then
				self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
				self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Visible)
			else
				self._wtItemBindBtn:SetVisibility(ESlateVisibility.Visible)
				self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
			end
		else
			self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
			self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
		end
	end

	-- 预设信息
	local weaponFeature = self._itemStruct:GetFeature(EFeatureType.Weapon)
	if weaponFeature and weaponFeature:IsShowPoorWeapon() and (not self._bInMp) then
		self._wtItemPoorWeaponBtn:SetVisibility(ESlateVisibility.Visible)
	else
		self._wtItemPoorWeaponBtn:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemDetailView:_UpdateDetailViewBottomBtnSlot(btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
	self._btnTypeList = ItemDetailLogic.CreateItemDetailBtns(self._itemStruct, btnTypeList)
	if self._btnNavId == nil then
		self:InitContentBtnCfg(true, true, self._wtItemDetailContent, self._wtItemDetailContent:GetBtnWrapBox(), self._btnTypeList, UIName2ID.ItemDetailDFCommonButtonV3S1, addtionPanelSlot, loadFinishCallback, {...})
	else
		self:InitContentBtnCfg(true, false, self._wtItemDetailContent, self._wtItemDetailContent:GetBtnWrapBox(), self._btnTypeList, self._btnNavId, addtionPanelSlot, loadFinishCallback, {...})
	end
end

function ItemDetailView:_UpdateDetailViewOutContentSlot(itemStruct, btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
	if not self.wUnderContentSlot then return end
	if self._bShowBtn == true then
		if not self._btnTypeList or not self._btnTypeList[1] or (self._btnTypeList[1] and not self._btnTypeList[1].bKeepSlotContentBtn) then
			Facade.UIManager:RemoveSubUIByParent(self, self.wUnderContentSlot)
			ItemDetailLogic.CreateItemDetailBtnsUI(itemStruct, self, self.wUnderContentSlot, btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
		end
	end
end

function ItemDetailView:UpdateDetailViewOutContentSlotByAuctionBuyBtn(itemStruct, btnTypeList, addtionPanelSlot, loadFinishCallback, ...)
	if self._wtDetailViewBtnIns then
		self._wtDetailViewBtnIns:OnInitExtraData(itemStruct, btnTypeList, addtionPanelSlot, ...)
		loadFinishCallback(self._wtDetailViewBtnIns)
	end
end

function ItemDetailView:_UpdateOrnamentsPlusStyle()
	local itemMainType = ItemHelperTool.GetMainTypeById(self._itemStruct.id)
    local itemSubType = ItemHelperTool.GetSubTypeById(self._itemStruct.id)
	local level = ItemHelperTool.GetQualityTypeById(self._itemStruct.id)
	if itemMainType == EItemType.Adapter and itemSubType == ItemConfig.EAdapterItemType.Pendant and level == ItemDetailConfig.MysticalPendantPlusId then
		self._wtOrnamentsPlusImg:HitTestInvisible()
		local pendantDataRow = CollectionLogic.GetPendantDataRow(self._itemStruct.id)
		if pendantDataRow then
			local mysticalPendantSuitRow =  CollectionLogic.GetMysticalPendantSuitRow(pendantDataRow.SeasonID)
			if mysticalPendantSuitRow then
				self._wtOrnamentsPlusImg:AsyncSetImagePath(mysticalPendantSuitRow.SeasonImg)
			end
		end
		self._wtOrnamentsPlusText:HitTestInvisible()
	else
		self._wtOrnamentsPlusImg:Collapsed()
		self._wtOrnamentsPlusText:Collapsed()
	end
end

function ItemDetailView:_UpdateLimitInGame()
	if self._wtLimitInGame then
		if UGlobalConstWrapper.IsItemIDForbiddenToCarryOut(self._itemStruct.id) then
			self._wtLimitInGame:SelfHitTestInvisible()
		else
			local bShow = false
			if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
				local ownerPlayerId = self._itemStruct:GetOwnerPlayerId()
				local playerState = UGPBattleFieldSystem.Get(GetWorld()):GetCurrentLocalPlayerOrOBPlayerState()
				if isvalid(playerState) and playerState:IsItemForbiddenToCarryOut(self._itemStruct.id, ownerPlayerId) then
					bShow = true
					self._wtLimitInGame:SelfHitTestInvisible()
				end
			end
			if not bShow then
				self._wtLimitInGame:Collapsed()
			end
		end
	end
end

function ItemDetailView:GetDetailContent()
    return self._wtItemDetailContent
end

function ItemDetailView:GetBtnWrapBox()
    return self._wtItemDetailContent:GetBtnWrapBox()
end

-- function ItemDetailView:_Reset()
	-- self:SetShowModelBtnVisible(false)
	-- self:SetPriceVisible(true)
	-- self:SetLabelMarkBtnVisible(true)
	-- self:SetTrackBtnVisible(true)
	-- self:SetSourceDescVisible(true)
	-- self:SetUsePlaceDescVisible(true)

	-- self._wtLabelMarkBtn:SetVisibility(ESlateVisibility.Visible)
	-- self._wtItemReadyBreakBindBtn:SetVisibility(ESlateVisibility.Collapsed)

	-- self._bigTipsAlignWidget = self._wtItemDetailTitle
	-- self._bCollectionItem = false

	-- self._btnTypeList = nil
	-- self._btnNavId = nil
-- end

------------------------------------ Public function ------------------------------------
-- 更新详情页物品详情信息
-- itemStruct：物品信息
-- bShowBtn：是否显示按钮,默认显示
-- btnTypeList：按钮类型：ItemDetailConfig.ButtonType,选择模块提供通用按钮类型,也设置自定义按钮,仓库按钮会更具道具类型添加默认按钮
-- btnNavId：按钮SubView,默认不填,会根据btnTypeList数量使用加载默认SubView,设置自定义按钮SubView
function ItemDetailView:UpdateItem(itemStruct, bShowBtn, btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
	---@type ItemBase
	self._itemStruct = itemStruct
	-- self:_Reset()
	self._btnNavId = btnNavId
	self._btnTypeList = btnTypeList
	if bShowBtn ~= nil then
		self._bShowBtn = bShowBtn
	end
	local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
	self._bInMp = armedForceMode == EArmedForceMode.MP

	local itemFeature = self._itemStruct:GetFeature()
	if itemFeature.IsCollecionItem and itemFeature:IsCollecionItem() then
		self._bCollectionItem = true
	else
		self._bCollectionItem = false
	end
	self:SetDetailBtnVisible(false)
	self:SetDetailBtnVisible2(false)
	self:_UpdateDetailViewTitle()
	self:_UpdateDetailViewImage()
	self:_UpdateWeaponBluePrint()
	self:_SetItemPrice()
	self:_UpdateDetailViewSubTitle()
	-- self:_SetSkinDesc()
	self:_SetGiftBoxBg()

	self:_UpdateDetailViewTopBtn()
	self:_UpdateDetailViewMiddleBtnSlot()

	if bShowBtn then
		self._wtItemDetailContent:ResetContentBtnCfg()
		Facade.UIManager:RemoveSubUIByParent(self._wtItemDetailContent, self._wtItemDetailContent:GetBtnWrapBox())
		if self._wtItemDetailContent:GetBtnWrapBox2() then
			Facade.UIManager:RemoveSubUIByParent(self._wtItemDetailContent, self._wtItemDetailContent:GetBtnWrapBox2())
		end
		if not self._btnTypeList or not self._btnTypeList[1] or (self._btnTypeList[1] and not self._btnTypeList[1].bKeepSlotContentBtn) then
			Facade.UIManager:RemoveSubUIByParent(self, self.wUnderContentSlot)
		end
		if not self.bOutBtn then
			self:_UpdateDetailViewBottomBtnSlot(self._btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
		else
			self:_UpdateDetailViewOutContentSlot(self._itemStruct, self._btnTypeList, btnNavId, addtionPanelSlot, loadFinishCallback, ...)
		end
	else
		Facade.UIManager:RemoveSubUIByParent(self, self.wUnderContentSlot)
	end

	if self._wticonItem then
		if self._bShowIconItem then
			self._wticonItem:Visible()
		else
			self._wticonItem:Collapsed()
		end
		self._wtItemDetailContent:SetItem(self._itemStruct, true, nil, nil, self._bShowIconItem, self)
	else
		self._wtItemDetailContent:SetItem(self._itemStruct, true, nil, nil, true, self)
	end
	self:SetBigTipsAlignWidget(self._bigTipsAlignWidget)
	self:_UpdateOrnamentsPlusStyle()
	if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
		self:_UpdateLimitInGame()
	end

	LogAnalysisTool.DoSendOpenItemDetailUILog(itemStruct.id)
	self._bFromPop = false
end

function ItemDetailView:UpdateTransBtn(transBtnList)
	local parent = self:Wnd("DFNamedSlot_94")
	if not parent then return end
	if transBtnList and #transBtnList > 0 then
		parent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		if self._wtTransBtnList then
			self._wtTransBtnList:UpdateData(transBtnList)
		else
			local weakUiIns = Facade.UIManager:AddSubUI(self, UIName2ID.ItemDetailTransferBar, parent, nil)
            if weakUiIns and getfromweak(weakUiIns) then
                local uiIns = getfromweak(weakUiIns)
                if uiIns then
					self._wtTransBtnList = uiIns
                    parent:AddChild(uiIns)
					self._wtTransBtnList:UpdateData(transBtnList)
					self.TransUISpacing = uiIns:Bp_GetSpacing()
                end
            else
                logerror("ItemDetailView:UpdateTransBtn:UIManager:AddSubUI: weakUiIns is nil!")
            end
		end
	else
		parent:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemDetailView:GetVisibleTransBtn()
	local parent = self:Wnd("DFNamedSlot_94")
	if self._wtTransBtnList and parent and parent:IsVisible() then
		return self._wtTransBtnList
	end

	return nil
end

function ItemDetailView:SetTransBtnListPos(pos)
	self:Bp_SetTipPos(pos)
end

-- 获取UI的宽度与边距，用于计算侧边栏的位置
function ItemDetailView:GetSpacing()
	return self.TransUISpacing or 0
end

-- 设置详情页底部按钮可见 此接口已废弃，请使用bShowBtn
function ItemDetailView:SetBtnVisible(bShowBtn)
	-- if not self._wBottomBtnSlot then return end
	-- if self._bShowBtn == bShowBtn then
	-- 	return
	-- end
	-- self._bShowBtn = bShowBtn
	-- if self._bShowBtn then
	-- 	self._wBottomBtnSlot:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	-- else
	-- 	self._wBottomBtnSlot:SetVisibility(ESlateVisibility.Collapsed)
	-- end
end

function ItemDetailView:SetIsOutBtnSlot(bOutBtn)
	self.bOutBtn = bOutBtn
end

-- 设置是否可快速装配
function ItemDetailView:SetFastEquippable(bFastEquippable)
	self._bFastEquippable = bFastEquippable
end

function ItemDetailView:GetItem()
	return self._itemStruct
end

-- 设置交易模式
-- tradeMode: 出售（显示价格）:ETradeMode.Sell； 购买（隐藏价格）:ETradeMode.Buy；只显示数量：ETradeMode.ShowNum
function ItemDetailView:SetTradeMode(tradeMode)
	self._tradeMode = tradeMode
	self:_SetItemPrice()
end

-- 设置价格显隐性
function ItemDetailView:SetPriceVisible(bVisible)
	if self._bInMp then
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
		self._wtDividLine:Collapsed()
		return
	end
	if bVisible then
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtCurrencyRichText:SetVisibility(ESlateVisibility.Collapsed)
	end
	self:_UpdateDividLine()
end

function ItemDetailView:SetCustomShowNum(showNum)
	self.showNum = showNum
	self:_SetItemPrice()
end

function ItemDetailView:ResetCustomShowNum()
	self.showNum = nil
	self:_SetItemPrice()
end

function ItemDetailView:_SetItemPrice(customRichText)
	local showItem = self._itemStruct

	local bShowPrice = ItemDetailLogic.IsShowPrice(self._itemStruct)
	if not bShowPrice then
		self:SetPriceVisible(bShowPrice)
		return
	end

	local realPrice = 0
	local showNum = self.showNum or showItem.num
	if self._tradeMode == ETradeMode.Sell then
		realPrice = showItem:GetSingleSellPrice()
		self._wtCurrencyRichText:SetText(ShopHelperTool.GetDynamicGuidePriceRichTextByItemV3(showItem, 1, nil, nil, customRichText, nil, FVector2D(44, 44)))
		self:SetPriceVisible(realPrice ~= 0)
	elseif self._tradeMode == ETradeMode.ShowNum then
		self._wtCurrencyRichText:SetText(string.format(ItemDetailConfig.Loc.itemNum, showNum))
		self:SetPriceVisible(showNum ~= 0)
	else
		local bForceDefault = true
		realPrice = Server.ShopServer:GetShopBuyPriceByItem(showItem)
		self._wtCurrencyRichText:SetText(ShopHelperTool.GetShopBuyPriceRichTextByItemV2(showItem, 1, false, bForceDefault, customRichText))
		self:SetPriceVisible(realPrice ~= 0)
	end
end

function ItemDetailView:_SetSkinDesc()
	if not Facade.GameFlowManager:CheckIsInFrontEnd() then
		return
	end

	if not self._skinDesc then
		return
	end

	if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin then
		local weaponInfo = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
		local desc = weaponInfo[self._itemStruct.id].SkinDescription
		self._skinDesc:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		self._skinDesc:SetText(desc)
	else
		self._skinDesc:SetVisibility(ESlateVisibility.Collapsed)
	end

	self._skinDesc:Collapsed()
end

function ItemDetailView:_SetGiftBoxBg()
	if not self._giftBg then
		return
	end
	local itemFeature = self._itemStruct:GetFeature()
	local featureType = itemFeature:GetFeatureType()
	if featureType == EFeatureType.Reward and ( itemFeature:IsUndecipheredBrick() or itemFeature:IsDecipheredBrick() ) then
		self._giftBg:SelfHitTestInvisible()
		-- self._giftBg:PlayWidgetAnim(self._giftBg.WBP_Itemview_Collections_loop, 1)
		self._giftBg:PlayWidgetAnim(self._giftBg.WBP_Itemview_Collections_loop_real, 0)
	else
		self._giftBg:Collapsed()
	end
end

function ItemDetailView:_UpdateWeaponBluePrint()
	local itemFeature = self._itemStruct and self._itemStruct:GetFeature(EFeatureType.Weapon) or nil
	if itemFeature and ItemHelperTool.GetMainTypeById(self._itemStruct.id) ~= EItemType.WeaponSkin then
		self._wBluePrintItem:ResetData()
		self._wBluePrintItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		local weaponPropInfo = self._itemStruct:GetRawPropInfo()
		local isWeaponBP = itemFeature:IsEquipWeaponBP()
		local skinId = self._itemStruct:Get_PropInfo_Weapon_SkinID()
		local skinGId = self._itemStruct:Get_PropInfo_Weapon_SkinGUID()
		local desc = self._itemStruct:GetRawDescObj()
		-- 是否装备皮肤
		if skinId ~= 0 then
			self._wBluePrintItem:SetWeaponSkinID(skinId)
			self._wBluePrintItem:SetShowStyle(Module.CommonWidget.Config.EIVWeaponBPShowStyle.NameAndShading)
			-- self._wBluePrintItem:SetShowStyle(Module.CommonWidget.Config.EIVWeaponBPShowStyle.OnlyName)
			self._wBluePrintItem:fRefreshFunc()
			self._wBluePrintItem:SetShowType(3)
			-- 玄学皮肤信息
			if ItemHelperTool.GetThirdTypeById(skinId) == 0 and isvalid(desc) then
				local mysticalPropInfo = desc:GetSkinInfo()
				if mysticalPropInfo then
					local wearText = ItemConfigTool.GetMysticalWearConfig(mysticalPropInfo.Wear) or ""
					local rateText = ""
					local ratityID = ItemConfigTool.GetItemRatityID(mysticalPropInfo.MysticalId)
					if ItemConfigTool.GetItemQuality(skinId) == 5 and Module.Inventory.Config.MysticalSkinRarityTxtMapping[ratityID] then
						rateText = string.format("%s|", Module.Inventory.Config.MysticalSkinRarityTxtMapping[ratityID])
					end
					self._wBluePrintItem:SetMysticalInfo(string.format(Module.ItemDetail.Config.Loc.MysticalSkinWearAndRarity, rateText, wearText))
				end
			end
		else
			-- 是否拥有皮肤
			local bIsHaveBlueprint, skinDatas = Server.CollectionServer:IsHaveBlueprint(self._itemStruct.id)
			if bIsHaveBlueprint then
				self._wBluePrintItem:SetShowStyle(Module.CommonWidget.Config.EIVWeaponBPShowStyle.ShadingAndNoEquipText)
				self._wBluePrintItem:SetWeaponSkinsNumber(#skinDatas)
				self._wBluePrintItem:fRefreshFunc()
				self._wBluePrintItem:SetShowType(3)
			else
				self._wBluePrintItem:SetVisibility(ESlateVisibility.Collapsed)
			end
		end
		
		-- 蓝图详情页不显示蓝图相关图标
		-- if ItemHelperTool.GetMainTypeById(self._itemStruct.id) == EItemType.WeaponSkin then
		-- 	-- 若为武器藏品
		-- 	self._wBluePrintItem:SetWeaponSkinID(self._itemStruct.id)
		-- 	self._wBluePrintItem:SetShowStyle(Module.CommonWidget.Config.EIVWeaponBPShowStyle.NameAndShading)
		-- 	self._wBluePrintItem:fRefreshFunc()
		-- 	self._wBluePrintItem:SetShowType(4)
		-- end
	elseif self._itemStruct and self._itemStruct.InSlot and self._itemStruct.InSlot.SlotType == ESlotType.SafeBox then
		local safeBoxSkinID = 0
		if ItemOperaTool.CheckRunWarehouseLogic() then
			safeBoxSkinID = Server.CollectionServer.safeboxInfoEquiped
		else
			safeBoxSkinID = Module.Looting:GetSafeBoxSkinID()
		end
		-- 必须是3x3才需要显示外观名字
		if safeBoxSkinID ~= 0 and (tonumber(self._itemStruct.id) % 10) == 4 then
			self._wBluePrintItem:ResetData()
			self._wBluePrintItem:SetWeaponSkinID(safeBoxSkinID)
			self._wBluePrintItem:SetShowStyle(Module.CommonWidget.Config.EIVWeaponBPShowStyle.NameAndShading)
			self._wBluePrintItem:fRefreshFunc()
			self._wBluePrintItem:SetShowType(3)
			self._wBluePrintItem:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
		else
			self._wBluePrintItem:SetVisibility(ESlateVisibility.Collapsed)
		end
	else
		self._wBluePrintItem:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemDetailView:SetCloseBtnVisible(bShow)
	self._wtItemDetailTitle:SetCloseBtnVisible(bShow)
end

function ItemDetailView:MarkOpenFromPop(bFromPop)
	self._bFromPop = bFromPop
	self._wtItemDetailTitle:MarkOpenFromPop(bFromPop)
	self._wtItemDetailContent:MarkOpenFromPop(bFromPop)
end

function ItemDetailView:SetCloseBtnCallback(callback)
	self._wtItemDetailTitle:SetCloseBtnCallback(callback)
end

function ItemDetailView:SetModelPanelShowCallback(callback)
	self._fOnShowModelPanelCallback = callback
end

-- function ItemDetailView:SetShowModelBtnVisible(bVisible)
-- 	if not self._wtShowModelBtn then return end
-- 	if bVisible then
-- 		self._wtShowModelBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
-- 	else
-- 		self._wtShowModelBtn:SetVisibility(ESlateVisibility.Collapsed)
-- 	end
-- end

-- 根据实际业务需求，此接口从SetItemBindBtnVisible(bVisible)改为SetItemBindBtnInVisible()
function ItemDetailView:SetItemBindBtnInVisible()
	self._wtItemBindBtn:SetVisibility(ESlateVisibility.Collapsed)
	self._wtItemTeamBindBtn:SetVisibility(ESlateVisibility.Collapsed)
end

function ItemDetailView:SetLabelMarkBtnVisible(bVisible)
	if Facade.GameFlowManager:IsInOBMode() then
		self._wtLabelMarkBtn:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	if self._bInMp then
		self._wtLabelMarkBtn:SetVisibility(ESlateVisibility.Collapsed)
		return
	end
	if bVisible then
		self._wtLabelMarkBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
	else
		self._wtLabelMarkBtn:SetVisibility(ESlateVisibility.Collapsed)
	end
end

function ItemDetailView:ResetContentBtnCfg()
	self._wtItemDetailContent:ResetContentBtnCfg()
end

function ItemDetailView:InitContentBtnCfg(bShowBtn, bItemContentBtn, btnParent, btnParentSlot, btnTypeList, btnNavId, floorSlot, btnCreateFinished, btnExtraParams)
	self._wtItemDetailContent:InitContentBtnCfg(bShowBtn, bItemContentBtn, btnParent, btnParentSlot, btnTypeList, btnNavId, floorSlot, btnCreateFinished, btnExtraParams)
end

-- function ItemDetailView:SetTrackBtnVisible(bVisible)
-- 	self._wtItemDetailContent:SetTrackBtnVisible(bVisible)
-- end

function ItemDetailView:SetKeyDurabilityVisible(bVisible)
	self._wtItemDetailContent:SetKeyDurabilityVisible(bVisible)
end

function ItemDetailView:SetContentHeight(contentHeight, sizeboxHeight)
	self.contentHeight = contentHeight
	self._wtContentSizeBox:SetMaxDesiredHeight(contentHeight)
	self.SizeBox = self:Wnd("wItemViewSizeBox", LuaUIBaseView)
	self.SizeBox:SetMaxDesiredHeight(sizeboxHeight)
end

function ItemDetailView:SetBtnNum(btnNum)
	self.btnNum = btnNum
end

function ItemDetailView:UpdateNewContentSizeBoxHeight()
	if IsHD() then
		self._wtContentSizeBox:SetMaxDesiredHeight(self.contentHeight - math.max(math.floor(self.btnNum / 2) * (88 + ((self.btnNum - 1) * 8)), 88))
	else
		self._wtContentSizeBox:SetMaxDesiredHeight(self.contentHeight - math.max(math.floor(self.btnNum / 2) * (72 + ((self.btnNum - 1) * 8)), 72))
	end
end

function ItemDetailView:ResetNewContentSizeBoxHeight()
	self._wtContentSizeBox:SetMaxDesiredHeight(self.contentHeight)
end

function ItemDetailView:SetSourceDescVisible(bVisible)
	self._wtItemDetailContent:SetSourceDescVisible(bVisible)
end

function ItemDetailView:SetUsePlaceDescVisible(bVisible)
	self._wtItemDetailContent:SetUsePlaceDescVisible(bVisible)
end

function ItemDetailView:SetAdjustBtnPosWhenIconHide(bVisible)
	self._wtItemDetailContent:SetAdjustBtnPosWhenIconHide(bVisible)
end

function ItemDetailView:SetWeaponAdapterVisible(bVisible)
	self._wtItemDetailContent:SetWeaponAdapterVisible(bVisible)
end

function ItemDetailView:SetWeaponBulletVisible(bVisible)
	self._wtItemDetailContent:SetWeaponBulletVisible(bVisible)
end

function ItemDetailView:SetWeaponBulletUnEquipVisible(bVisible)
	self._wtItemDetailContent:SetWeaponBulletUnEquipVisible(bVisible)
end

function ItemDetailView:SetSourceInfo(sourceInfo)
	self._wtItemDetailContent:SetSourceInfo(sourceInfo)
end

function ItemDetailView:SetUsePlaceInfo(usePlaceInfo)
	self._wtItemDetailContent:SetUsePlaceInfo(usePlaceInfo)
end

function ItemDetailView:SetAdapterDragState(bCanDrag)
    self._wtItemDetailContent:SetAdapterDragState(bCanDrag)
end

function ItemDetailView:SetBlindBoxCallBack(callbackFunc)
    Module.ItemDetail.Field:SetBlindBoxCallBack(callbackFunc)
end

function ItemDetailView:SetAdapterTipsShowButtonState(bTipsShowBtton)
    self._wtItemDetailContent:SetAdapterTipsShowButtonState(bTipsShowBtton)
end

function ItemDetailView:SetAdapterFastUnequipState(bCanFastUnequip)
    self._wtItemDetailContent:SetAdapterFastUnequipState(bCanFastUnequip)
end

function ItemDetailView:SetOnPoorWeaponState(bOnPoorWeapon)
	self._wtItemDetailContent:SetOnPoorWeaponState(bOnPoorWeapon)
end

---@param showState ETipsWeaponShowState
function ItemDetailView:SetWeaponShowState(showState)
    self._wtItemDetailContent:SetWeaponShowState(showState)
end

function ItemDetailView:SetTitleDiffShow(bShow, bUp)
    --self._wtItemDetailTitle:SetShowDiffVisible(bShow, bUp)
end

-- 标识是否直接从itemdetailview打开
function ItemDetailView:SetFromItemView(bFromItemDetailView)
	self._wtItemDetailContent:SetFromItemView(bFromItemDetailView)
end

function ItemDetailView:PlayInAnimation(bShow, bUp)
    self:StopAnimation(self.Anima_AutoIn)
    self:PlayAnimation(self.Anima_AutoIn, 0, 1, EUMGSequencePlayMode.Forward, 1, true)
end

function ItemDetailView:SetDragPanel(dragWidget, parent)
	self._wtItemDetailTitle:SetDragPanel(dragWidget, parent)
end

function ItemDetailView:SetCanDragState(bCanDrag)
	self._wtItemDetailTitle:SetCanDragState(bCanDrag)
end

function ItemDetailView:SetBigTipsAlignWidget(widget)
	self._bigTipsAlignWidget = widget
	self._wtItemDetailContent:SetBigTipsAlignWidget(widget)
end

function ItemDetailView:SetBulletDamge(damage, armorDamage)
	self._wtItemDetailContent:SetBulletDamge(damage, armorDamage)
end

function ItemDetailView:Reset()
	self._bShowIconItem = true
	self._bigTipsAlignWidget = self._wtItemDetailTitle
	self._tradeMode = ETradeMode.Sell
	if self._wtBtnPanel and self._wtBtnPanel.Collapsed then
		self._wtBtnPanel:Collapsed()
	end
	self._wtItemDetailContent:Reset()
end
----------- 处理Solt扩展接口 -----------

-- function ItemDetailView:GetTopBtntSlot()
-- 	return self._wTopBtnSlot--:GetContent()
-- end

-- function ItemDetailView:GetMiddleBtnSlot()
-- 	return self._wMiddleBtnSlot--:GetContent()
-- end

-- function ItemDetailView:GetBottomBtnSlot()
-- 	return self._wBottomBtnSlot--:GetContent()
-- end

-- function ItemDetailView:GetFloorBtnSlot()
	-- return self._wFloorBtnSlot--:GetContent()
-- end

function ItemDetailView:BindTopLeftSlotInit(fInitCallBack)
	self._fTopLeftSlotInitCB = fInitCallBack
end

function ItemDetailView:BindTopBtntSlotInit(fInitCallBack)
	self._fTopBtnSlotInitCB = fInitCallBack
end

function ItemDetailView:BindMiddleBtnSlotInit(fInitCallBack)
	self._fMiddleBtnSlotInitCB = fInitCallBack
end

function ItemDetailView:BindBottomBtnSlotInit(fInitCallBack)
	self._fBottomBtnSlotInitCB = fInitCallBack
end

function ItemDetailView:BindFloorBtnSlotInit(fInitCallBack)
	self._fFloorBtnSlotInitCB = fInitCallBack
end

------------- ItemDetailContent -------------
function ItemDetailView:GetContentSlot()
	return self._wContentSlot--:GetContent()
end

function ItemDetailView:ScrollToContentEnd()
	self._wtScrollBoxContent:ScrollToContentEnd()
end

function ItemDetailView:ScrollToContentStart()
	self._wtScrollBoxContent:ScrollToContentStart()
end

function ItemDetailView:_OnScrolled(CurOffset)
    if self._onContentScrollCallback then
        self._onContentScrollCallback(CurOffset)
    end

	if CurOffset >= 392 + 38 then
		self._wtBtnPanel:SelfHitTestInvisible()
	elseif CurOffset < 392 + 38 then
		self._wtBtnPanel:Collapsed()
	end
end

function ItemDetailView:SetContentScrollCallback(callback)
	if callback then
        self._wtScrollBoxContent:Event("OnUserScrolled", self._OnScrolled, self)
    else
        self._wtScrollBoxContent:RemoveEvent("OnUserScrolled")
    end
    self._onContentScrollCallback = callback
end

-- 设置打开武器属性界面的顶点和锚点，默认按钮位置
function ItemDetailView:SetWeaponAttrOffset(weaponAttrOffset, weaponAttrAlignment)
	self._wtItemDetailContent:SetWeaponAttrOffset(weaponAttrOffset, weaponAttrAlignment)
end

---@param direction ENewTipsAlignDirect
function ItemDetailView:SetWeaponTipsShowDirection(direction)
	self._wtItemDetailContent:SetWeaponTipsShowDirection(direction)
end

function ItemDetailView:SetWeaponTipsShowDirection(direction)
	self._wtItemDetailContent:SetWeaponTipsShowDirection(direction)
end


-- 播放来源用途指定按钮光效
--@param btnType EItemSource
--@param times number 动画次数，默认无限循环
function ItemDetailView:PlayBreathAnimSource(btnType, times)
	self._wtItemDetailContent:PlayBreathAnimSource(btnType, times)
end

-- 停止来源用途指定按钮光效
--@param btnType EItemSource
function ItemDetailView:StopBreathAnimSource(btnType)
	self._wtItemDetailContent:StopBreathAnimSource(btnType)
end

function ItemDetailView:AddToDetailContent(widget)
	self._wtItemDetailContent:AddToContent(widget)
end

function ItemDetailView:ResetDetailContent()
	self._wtItemDetailContent:ResetContent()
end

-- 显示隐藏默认的ItemDetailContent
function ItemDetailView:SetDefaultContentVisible(bVisible)
	self._wtItemDetailContent:SetVisibility(bVisible and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

function ItemDetailView:SetAssemblyQuickOperationInfo(fUnEquipWeaponBulletCallback)
	self._wtItemDetailContent:SetAssemblyQuickOperationInfo(fUnEquipWeaponBulletCallback)
end

function ItemDetailView:GetSourceWeakUIIns()
	return self._wtItemDetailContent:GetSourceWeakUIIns()
end


--------------------------
--region slot ui插入和清理(不适用于现有架构已移除，后续有需求再加)

--endregion
--------------------------

function ItemDetailView:IsShowIconItem(bShow)
	if not self._wticonItem then
		return
	end
	self._bShowIconItem = bShow
end

function ItemDetailView:_ShowCollectTip()
	if self.collectTipHandle then
		self:_HideCollectTip()
	else
		local text = self._wtLabelMarkBtn._bMark and ItemDetailConfig.Loc.uncollectItem or ItemDetailConfig.Loc.collectItem
		self.collectTipHandle = Module.CommonTips:ShowCommonMessageWithAnchor(text, self._wtDFTipsAnchor)
	end
end

function ItemDetailView:_HideCollectTip()
	if self.collectTipHandle then
		Module.CommonTips:RemoveCommonMessageWithAnchor(self.collectTipHandle, self._wtDFTipsAnchor)
		self.collectTipHandle = nil
	end
end

function ItemDetailView:_OnCollectBtnClicked()
	self:_HideCollectTip()
	self:_ShowCollectTip()
end

-- 设置右上角放大镜按钮回调
function ItemDetailView:SetDetailBtnClickedCallback(fCallback, callerIns)
	if fCallback then
		self._detailBtnCallbackfunc = SafeCallBack(fCallback, callerIns)
	end
end

function ItemDetailView:SetDetailBtnClickedCallback2(fCallback, callerIns)
	if fCallback then
		self._detailBtnCallbackfunc2 = SafeCallBack(fCallback, callerIns)
	end
end

function ItemDetailView:_OnDetailButtonCallBack()
	if self._detailBtnCallbackfunc then
		self._detailBtnCallbackfunc()
	end
end

function ItemDetailView:_OnDetailButtonCallBack2()
	if self._detailBtnCallbackfunc2 then
		self._detailBtnCallbackfunc2()
	end
end

-- 设置右上角放大镜按钮显隐性
function ItemDetailView:SetDetailBtnVisible(bVisible)
	if self._wtDetailButton then
		if bVisible then
			self._wtDetailButton:SelfHitTestInvisible()
		else
			self._wtDetailButton:Collapsed()
		end
	end
end

function ItemDetailView:SetDetailBtnVisible2(bVisible)
	if self._wtDetailButton2 then
		if bVisible then
			self._wtDetailButton2:SelfHitTestInvisible()
		else
			self._wtDetailButton2:Collapsed()
		end
	end
end

function ItemDetailView:SetNeedHideUnequipBulletBtnState(state)
	self._bNeedHideUnequipBulletBtn = state
	if self._wtItemDetailContent.SetNeedHideUnequipBulletBtnState then
		self._wtItemDetailContent:SetNeedHideUnequipBulletBtnState(state)
	end
end

function ItemDetailView:SetOpenFoldDetail(bOpenFoldDetail)
	self._bOpenFoldDetail = bOpenFoldDetail
	self._wtItemDetailTitle:SetOpenFoldDetail(bOpenFoldDetail)
end

function ItemDetailView:SetStatePlayAnimOnShow(state)
	self._bPlayAnimOnShow = state
end

return ItemDetailView