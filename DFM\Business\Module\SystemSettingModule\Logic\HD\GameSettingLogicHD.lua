----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local GameSettingLogicHD = {}
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local ComminicateSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.ComminicateSettingLogic"
local VehicleSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.VehicleSettingLogic"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local EDFMGamePlayMode = import("EDFMGamePlayMode")

local _gameSetting = import("ClientGameSettingHD").Get()
local _gameSettingMobileClass = import("ClientGameSetting")
local _baseSettingMobile = import("ClientBaseSetting").Get()
local _controlSettingMobile = import("ClientControlSetting").Get()
local _comminicateSettingMobile = import("ClientComminicateSetting").Get()
local _vehicleSetting = import("ClientVehicleSettingHD").Get()

local function AutoHoldBreathOnZoom_MobileFunc(settingMobileObj, value)
    settingMobileObj.bAutoHoldBreathOnZoom = value
end

local function RunBreakReload_MobileFunc(settingMobileObj, value)
    settingMobileObj.bRunBreakReload = value
end

local function SceneFOVCalcOpenCameraFOV_MobileFunc(settingMobileObj, value)
    settingMobileObj.bSceneFOVCalcOpenCameraFOV = value
end

local function AutoUpRun_MobileFunc(settingMobileObj, value)
    settingMobileObj.bAutoUpRun = value
end

local function AutoUpRunMP_MobileFunc(settingMobileObj, value)
    settingMobileObj.bAutoUpRunMP = value
end

-- local function SeparateVault_MobileFunc(settingMobileObj, value)
--     settingMobileObj.bSeparateVault = value
-- end

local function AutoParachute_MobileFunc(settingMobileObj, value)
    settingMobileObj.bAutoParachute = value
end

local function VaultTriggerMode_MobileFunc(settingMobileObj, value)
    settingMobileObj.bVaultTriggerMode = value
end

local function SensitivityChangeMode_MobileFunc(settingMobileObj, value)
    settingMobileObj.SensitivityChangeMode = value
end

local function _VehicleCannonCamFollowApplyFunc(settingObj, key, value)
    VehicleSettingLogic.ProcessCannonCamFollow(value)
end

local function _VehiclePlaneMouseReverseApplyFunc(settingObj, key, value)
end

local function _VehicleDriverWeaponSensitivityFPPApplyFunc(settingObj, key, value)
end

local function _VehicleDriverWeaponSensitivityTPPApplyFunc(settingObj, key, value)
end

local function _VehicleHelicopterDriverWeaponSensitivityFPPApplyFunc(settingObj, key, value)
end

local function _VehicleHelicopterDriverWeaponSensitivityTPPApplyFunc(settingObj, key, value)
end

local function _VehiclePassengerWeaponSensitivityApplyFunc(settingObj, key, value)
end

local function _VehicleFOVRatioTPPApplyFunc(settingObj, key, value)
end

local _PlayModes = {
    EDFMGamePlayMode.GamePlayMode_SOL,
    EDFMGamePlayMode.GamePlayMode_Raid,
    EDFMGamePlayMode.GamePlayMode_Breakthrough
}

local function _OpenFlagVoiceApplyFunc(settingObj, value)
    for _, mode in ipairs(_PlayModes) do
        ComminicateSettingLogic.ProcessbOpenFlagVoice(mode, value)
    end
end

-- BEGIN MODIFICATION - VIRTUOS
local function _OpenTextChatInGameApplyFunc(settingObj, value)
    ComminicateSettingLogic.ProcessbOpenTextChatInGame(value)
end

local function _CrossplayFlagApplyFunc(settingObj, key, value)
    if rawget(Server, "RoleInfoServer") then
        Server.RoleInfoServer:ReqSetCrossplay(value)
    end
end

local function _MixedLoadingChangeFunc(settingObj, key, value)
    if settingObj.NotifyCloseSystemSetting then
        --settingObj.NotifyCloseSystemSetting(GetWorld())
    end
end

-- END MODIFICATION - VIRTUOS

local function _AutoMarkingTeammateWishItemApplyFunc(settingObj, value)
    ComminicateSettingLogic.ProcessAutoMarkingTeammateWishItem(value)
end

local function _RouletteColorAApplyFunc(settingObj, value)
    for _, mode in ipairs(_PlayModes) do
        ComminicateSettingLogic.ProcessRouletteColorA(mode, value)
    end
end

local function _FlagColorAApplyFunc(settingObj, value)
    for _, mode in ipairs(_PlayModes) do
        ComminicateSettingLogic.ProcessFlagColorA(mode, value)
    end
end

local function _MPSingleClickCreateLocMarkApplyFunc(settingObj, value)
    ComminicateSettingLogic.ProcessMPSingleClickCreateLocMark(value)
end

local function _SafeBoxPreferMarkItemApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("Looting") then
        Module.Looting:SetSafeBoxPreferMarkItem(value)
    end
end

local function _SafeBoxPreferHighValueItemApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("Looting") then
        Module.Looting:SetSafeBoxPreferHighValueItem(value)
    end
end

local function _SafeBoxPriceThresholdApplyFunc(settingObj, key, value)
    if Facade.ModuleManager:IsModuleValid("Looting") then
        Module.Looting:SetSafeBoxPriceThreshold(value)
    end
end

local function _InventoryAutoNewlineApplyFunc(settingObj, key, value)
    if rawget(Server, "InventoryServer") then
        Server.InventoryServer:LocalChangeSortStyle(
            value and eDepositSortStyle.class_first or eDepositSortStyle.space_first
        )
        Server.InventoryServer:DoChangeSortConfig()
    end
end

local function _InventorySortEveryEnterApplyFunc(settingObj, key, value)
    if rawget(Server, "InventoryServer") then
        Server.InventoryServer:LocalSetSortEveryEnter(value)
        Server.InventoryServer:DoChangeSortConfig()
    end
end


local function _HitEffectColorApplyFunc(settingObj, key, value)
end

function GameSettingLogicHD.ProcessSecondLanguage(secondLanguage)
    if _gameSetting then
        _gameSetting.SecondLanguage = secondLanguage
    end
end

function GameSettingLogicHD.ApplyLateSettings()
    local bSafeBoxPreferMarkItem = CommonSettingLogicHD.GetDataByID("bSafeBoxPreferMarkItem")
    _SafeBoxPreferMarkItemApplyFunc(_gameSetting, nil, bSafeBoxPreferMarkItem)

    local bSafeBoxPreferHighValueItem = CommonSettingLogicHD.GetDataByID("bSafeBoxPreferHighValueItem")
    _SafeBoxPreferHighValueItemApplyFunc(_gameSetting, nil, bSafeBoxPreferHighValueItem)

    local SafeBoxPriceThreshold = CommonSettingLogicHD.GetDataByID("SafeBoxPriceThreshold")
    _SafeBoxPriceThresholdApplyFunc(_gameSetting, nil, SafeBoxPriceThreshold)

    local bInventoryAutoNewline = CommonSettingLogicHD.GetDataByID("bInventoryAutoNewline")
    _InventoryAutoNewlineApplyFunc(_gameSetting, nil, bInventoryAutoNewline)

    local bInventorySortEveryEnter = CommonSettingLogicHD.GetDataByID("bInventorySortEveryEnter")
    _InventorySortEveryEnterApplyFunc(_gameSetting, nil, bInventorySortEveryEnter)
end

function GameSettingLogicHD.Register()
    SettingRegLogicHD.BeginRegSection(_gameSetting)
    -- SettingRegLogicHD.RegPassThroughMapping("bRunBreakReload", "bRunBreakReload")
    -- SettingRegLogicHD.RegMobileMapping("bRunBreakReload", _baseSettingMobile, RunBreakReload_MobileFunc, nil)

    -- SettingRegLogicHD.RegPassThroughMapping("bAutoHoldBreathOnZoom", "bAutoHoldBreathOnZoom")
    -- SettingRegLogicHD.RegMobileMapping("bAutoHoldBreathOnZoom", _baseSettingMobile, AutoHoldBreathOnZoom_MobileFunc, nil)

    SettingRegLogicHD.RegPassThroughMapping("bSceneFOVCalcOpenCameraFOV", "bSceneFOVCalcOpenCameraFOV")
    SettingRegLogicHD.RegMobileMapping(
        "bSceneFOVCalcOpenCameraFOV",
        _baseSettingMobile,
        SceneFOVCalcOpenCameraFOV_MobileFunc,
        nil
    )

    SettingRegLogicHD.RegPassThroughMapping("bAutoUpRun", "bAutoUpRun")
    SettingRegLogicHD.RegMobileMapping("bAutoUpRun", _controlSettingMobile, AutoUpRun_MobileFunc, nil)
    SettingRegLogicHD.RegPassThroughMapping("bAutoUpRunMP", "bAutoUpRunMP")
    SettingRegLogicHD.RegMobileMapping("bAutoUpRunMP", _controlSettingMobile, AutoUpRunMP_MobileFunc, nil)
    -- SettingRegLogicHD.RegPassThroughMapping("bSeparateVault", "bSeparateVault")
    -- SettingRegLogicHD.RegMobileMapping("bSeparateVault", _controlSettingMobile, SeparateVault_MobileFunc, nil)
    SettingRegLogicHD.RegPassThroughMapping("bVaultTriggerMode", "bVaultTriggerMode")
    SettingRegLogicHD.RegMobileMapping("bVaultTriggerMode", _controlSettingMobile, VaultTriggerMode_MobileFunc, nil)
    SettingRegLogicHD.RegPassThroughMapping("SensitivityChangeMode", "SensitivityChangeMode")
    SettingRegLogicHD.RegMobileMapping(
        "SensitivityChangeMode",
        _controlSettingMobile,
        SensitivityChangeMode_MobileFunc,
        nil
    )
    SettingRegLogicHD.RegPassThroughMapping("bAutoParachute", "bAutoParachute")
    SettingRegLogicHD.RegPassThroughMapping("HelicopterScopeMode", "HelicopterScopeMode")
    SettingRegLogicHD.RegPassThroughMapping("JetScopeMode", "JetScopeMode")
    SettingRegLogicHD.RegMobileMapping(
        "bAutoParachute",
        _controlSettingMobile,
        AutoParachute_MobileFunc,
        nil
    )
    SettingRegLogicHD.RegPassThroughMapping("bReloadBreakMirror", "bReloadBreakMirror")
    SettingRegLogicHD.RegApplyMapping(
        "bMixedLoading",
         _gameSetting,
         "bMixedLoading",
         _MixedLoadingChangeFunc
        )
    SettingRegLogicHD.RegPassThroughMapping("bCanMouseTurnMelee", "bCanMouseTurnMelee")
    SettingRegLogicHD.RegPassThroughMapping("bAirVehicleAutoBalanceAssist", "bAirVehicleAutoBalanceAssist")
    SettingRegLogicHD.RegPassThroughMapping("bFixedWingTPP", "bFixedWingTPP")

    SettingRegLogicHD.RegPassThroughMapping("bOpenFlagVoice", "bOpenFlagVoice")
    SettingRegLogicHD.RegMobileMapping("bOpenFlagVoice", _comminicateSettingMobile, _OpenFlagVoiceApplyFunc, nil)

    -- BEGIN MODIFICATION - VIRTUOS
    if PLATFORM_GEN9 == 1 then
        SettingRegLogicHD.RegPassThroughMapping("bOpenTextChatInGame", "bOpenTextChatInGame")
        SettingRegLogicHD.RegMobileMapping("bOpenTextChatInGame", _comminicateSettingMobile, _OpenTextChatInGameApplyFunc, nil)
        SettingRegLogicHD.RegPassThroughMapping("bCrossplay", "bCrossplay")
        SettingRegLogicHD.RegApplyMapping(
            "bCrossplay",
            _gameSetting,
            "bCrossplay",
            _CrossplayFlagApplyFunc
        )
    end
    -- END MODIFICATION - VIRTUOS
    
    SettingRegLogicHD.RegPassThroughMapping("bAutoMarkingTeammateWishItem", "bAutoMarkingTeammateWishItem")
    SettingRegLogicHD.RegMobileMapping(
        "bAutoMarkingTeammateWishItem",
        _comminicateSettingMobile,
        _AutoMarkingTeammateWishItemApplyFunc,
        nil
    )

    SettingRegLogicHD.RegPassThroughMapping("RouletteColorA", "RouletteColorA")
    SettingRegLogicHD.RegMobileMapping("RouletteColorA", _comminicateSettingMobile, _RouletteColorAApplyFunc, nil)

    SettingRegLogicHD.RegPassThroughMapping("FlagColorA", "FlagColorA")
    SettingRegLogicHD.RegMobileMapping("FlagColorA", _comminicateSettingMobile, _FlagColorAApplyFunc, nil)

    SettingRegLogicHD.RegPassThroughMapping("DrugDuration", "DrugDuration")

    SettingRegLogicHD.RegPassThroughMapping("bMPSingleClickCreateLocMark", "bMPSingleClickCreateLocMark")
    SettingRegLogicHD.RegMobileMapping("bMPSingleClickCreateLocMark", _comminicateSettingMobile, _MPSingleClickCreateLocMarkApplyFunc, nil)

    SettingRegLogicHD.RegPassThroughMapping("bSafeBoxPreferMarkItem", "bSafeBoxPreferMarkItem")
    SettingRegLogicHD.RegApplyMapping(
        "bSafeBoxPreferMarkItem",
        _gameSetting,
        "bSafeBoxPreferMarkItem",
        _SafeBoxPreferMarkItemApplyFunc
    )

    SettingRegLogicHD.RegPassThroughMapping("bSafeBoxPreferHighValueItem", "bSafeBoxPreferHighValueItem")
    SettingRegLogicHD.RegApplyMapping(
        "bSafeBoxPreferHighValueItem",
        _gameSetting,
        "bSafeBoxPreferHighValueItem",
        _SafeBoxPreferHighValueItemApplyFunc
    )

    SettingRegLogicHD.RegPassThroughMapping("SafeBoxPriceThreshold", "SafeBoxPriceThreshold")
    SettingRegLogicHD.RegApplyMapping(
        "SafeBoxPriceThreshold",
        _gameSetting,
        "SafeBoxPriceThreshold",
        _SafeBoxPriceThresholdApplyFunc
    )

    SettingRegLogicHD.RegPassThroughMapping("bShareSpoils", "bShareSpoils")
    SettingRegLogicHD.RegPassThroughMapping("LootSharing", "LootSharing")

    SettingRegLogicHD.RegPassThroughMapping("bInventoryAutoNewline", "bInventoryAutoNewline")
    SettingRegLogicHD.RegApplyMapping(
        "bInventoryAutoNewline",
        _gameSetting,
        "bInventoryAutoNewline",
        _InventoryAutoNewlineApplyFunc
    )

    SettingRegLogicHD.RegPassThroughMapping("bInventorySortEveryEnter", "bInventorySortEveryEnter")
    SettingRegLogicHD.RegApplyMapping(
        "bInventorySortEveryEnter",
        _gameSetting,
        "bInventorySortEveryEnter",
        _InventorySortEveryEnterApplyFunc
    )
    SettingRegLogicHD.RegPassThroughMapping("bShareSpoilTeam", "bShareSpoilTeam")
    SettingRegLogicHD.RegPassThroughMapping("bShareSpoilAll", "bShareSpoilAll")
    SettingRegLogicHD.RegPassThroughMapping("HitEffectColor", "HitEffectColor")
    SettingRegLogicHD.RegApplyMapping("HitEffectColor", _gameSetting, "HitEffectColor", _HitEffectColorApplyFunc)

    SettingRegLogicHD.BeginRegSection(_vehicleSetting)
    SettingRegLogicHD.RegPassThroughMapping("CannonCamFollow", "CannonCamFollow")
    SettingRegLogicHD.RegApplyMapping(
        "CannonCamFollow",
        _vehicleSetting,
        "CannonCamFollow",
        _VehicleCannonCamFollowApplyFunc
    )

    SettingRegLogicHD.RegPassThroughMapping("CannonCamFollowGamepad", "CannonCamFollowGamepad")
    
    -- SettingRegLogicHD.RegPassThroughMapping("VehiclePlaneMouseReverse", "VehiclePlaneMouseReverse")
    -- SettingRegLogicHD.RegApplyMapping("VehiclePlaneMouseReverse", _vehicleSetting, "VehiclePlaneMouseReverse", _VehiclePlaneMouseReverseApplyFunc)

    SettingRegLogicHD.RegPassThroughMapping("FOVRatioTPP", "FOVRatioTPP")
    SettingRegLogicHD.RegApplyMapping("FOVRatioTPP", _vehicleSetting, "FOVRatioTPP", _VehicleFOVRatioTPPApplyFunc)

    SettingRegLogicHD.EndRegSection()
end

return GameSettingLogicHD
