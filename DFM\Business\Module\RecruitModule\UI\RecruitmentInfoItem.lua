----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRecruit)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class RecruitmentInfoItem : LuaUIBaseView
local RecruitmentInfoItem = ui("RecruitmentInfoItem")
local RecruitConfig = Module.Recruit.Config
local RecruitLogic = require "DFM.Business.Module.RecruitModule.Logic.RecruitLogic"
local RecruitPlayerHeadItem = require "DFM.Business.Module.RecruitModule.UI.RecruitPlayerHeadItem"
local RankIconAbbr = require "DFM.Business.Module.RankingModule.UI.RankIconAbbr"
local RecruitmentInfoPlayerClassItem = require "DFM.Business.Module.RecruitModule.UI.RecruitmentInfoPlayerClassItem"

function RecruitmentInfoItem:Ctor()
    self._wtMapNameText = self:Wnd("wtMapNameText", UITextBlock)
    self._wtWithMicHint = self:Wnd("wtWithMicHint", UIImage)
    self._wtWithRankHint = self:Wnd("wtWithRankHint", UIImage)
    self._wtLabelContainer = self:Wnd("wtLabelContainer", UIWidgetBase)
    self._wtLabelContainer:ClearChildren()
    self._wtTeammateNumText = self:Wnd("wtTeammateNumText", UITextBlock)
    self._wtCaptainHead = self:Wnd("wtCaptainHead", RecruitPlayerHeadItem)
    self._wtPlayerNameTxt = self:Wnd("wtPlayerNameTxt", UITextBlock)
    self._wtRankIcon = self:Wnd("wtRankIcon", RankIconAbbr)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)
    self._wtJoinBtn = self:Wnd("wtJoinBtn", DFCommonButtonOnly)
    self._wtJoinBtn:Event("OnClicked", self._OnJoinBtnClicked, self)
    self._wtPlayerClassItems = {}
    for i = 1, 4, 1 do
        local playerClassItem = self:Wnd(string.format("wtPlayerClass_%s", tostring(i)), RecruitmentInfoPlayerClassItem)
        if playerClassItem then
            table.insert(self._wtPlayerClassItems, playerClassItem)
        end
    end
    self._recruitmentInfo = nil
    self._captainPlayerInfo = nil

    --- BEGIN MODIFICATION @ VIRTUOS: Platform icon
    if IsConsole() then
        self._wtPlatformIcon = self:Wnd("wtPlatformIcon", UIImage)
    end
    --- END MODIFICATION
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
---@overload fun(LuaUIBaseView, OnInitExtraData)
function RecruitmentInfoItem:OnInitExtraData()
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function RecruitmentInfoItem:OnOpen()
    self:Hidden()
    self:AddListeners()
end

-- UI监听事件、协议
function RecruitmentInfoItem:AddListeners()
    self:AddLuaEvent(RecruitConfig.Events.evtOnTeamMemberInfoChanged, self._OnTeamMemberInfoChanged, self)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function RecruitmentInfoItem:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self, self._wtLabelContainer)
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function RecruitmentInfoItem:OnShow()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function RecruitmentInfoItem:OnHide()
end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function RecruitmentInfoItem:OnAnimFinished(anim)
end

function RecruitmentInfoItem:InitRecruitmentInfo(recruitmentInfo)
    if self._bIsLoading then
        return
    end 
    self._bIsLoading = true
    self._recruitmentInfo = recruitmentInfo or {}
    self._captainPlayerInfo = nil
    self._wtMapNameText:Collapsed()
    self._wtRankIcon:SetRankIconNone()
    self._wtRankIcon:SelfHitTestInvisible()
    self._wtRankDivision:SetText(RecruitConfig.Loc.NoRankDivision)
    self._wtRankDivision:SelfHitTestInvisible()
    self._wtWithMicHint:Collapsed()
    self._wtWithRankHint:Collapsed()
    self._wtLabelContainer:SelfHitTestInvisible()
    self._wtTeammateNumText:Collapsed()
    local maxMemberNum = RecruitLogic.GetTeamMemberMaxNum()
    self._wtCaptainHead:InitPlayerInfo(nil)
    for index, playerClassItem in ipairs(self._wtPlayerClassItems) do
        if index <= maxMemberNum then
            playerClassItem:InitPlayerClassInfo(nil)
            playerClassItem:Visible()
        else
            playerClassItem:Collapsed()
        end
    end
    if self._recruitmentInfo ~= nil then
        if self._recruitmentInfo.filter.bIsMP then
            self._wtMapNameText:SetText(Module.GameMode:GetTDMModeNameByGroupId(self._recruitmentInfo.filter.group_id))
        else
            self._wtMapNameText:SetText(Module.GameMode:GetStandardMapNameByMatchModeId(self._recruitmentInfo.filter.match_mode_id))
        end
        self._wtMapNameText:SelfHitTestInvisible()
        if self._recruitmentInfo.filter.micChoice == RecruitmentMicrophoneType.RecruitmentMicrophoneEnable then
            self._wtWithMicHint:SelfHitTestInvisible()
        end
        if self._recruitmentInfo.filter.rankDivisionChoice == RecruitmentRankType.RecruitmentRankEnable then
            self._wtWithRankHint:SelfHitTestInvisible()
        end
        local params = {
                        curNum = tostring(table.nums(self._recruitmentInfo.playerInfoList)), 
                        maxNum = tostring(self._recruitmentInfo.filter.bIsMP and RecruitConfig.MPMaxMemberNum or RecruitConfig.SOLMaxMemberNum)
                    }
        self._wtTeammateNumText:SetText(StringUtil.Key2StrFormat(Module.Recruit.Config.Loc.NumFormat,params))
        self._wtTeammateNumText:SelfHitTestInvisible()
        local btnTbl =  {
            HeadButtonType.PlayerInformat,
            HeadButtonType.AddFriend,
            HeadButtonType.Report
        }
        for index, playerClassItem in ipairs(self._wtPlayerClassItems) do
            playerClassItem:InitPlayerClassInfo(self._recruitmentInfo.playerInfoList[index])
            if self._recruitmentInfo.playerInfoList[index] and self._recruitmentInfo.playerInfoList[index].player_id == self._recruitmentInfo.captainId then
                self._captainPlayerInfo = self._recruitmentInfo.playerInfoList[index]
                self._wtCaptainHead:InitPlayerInfo(self._captainPlayerInfo, false, false, btnTbl)
                self._wtCaptainHead:SetRankIconVisible(false)
                self._wtPlayerNameTxt:SetText(self._captainPlayerInfo.nick_name)
                if RecruitLogic.IsInMp() == true then
                    if self._captainPlayerInfo.mp_rank_attended == true then
                        local targetScore = (self._captainPlayerInfo.show_commander_rank_points ~= nil and self._captainPlayerInfo.show_commander_rank_points > 0) and self._captainPlayerInfo.mp_commander_score or self._captainPlayerInfo.mp_rank_score
                        self._wtRankIcon:SetTournamentIconByScore(targetScore)
                        local rankInfo = Module.Tournament:GetRankDataByScore(targetScore)
                        self._wtRankDivision:SetText(rankInfo and rankInfo.Name or RecruitConfig.Loc.NoRankDivision)
                    else
                        self._wtRankIcon:SetRankIconNone()
                    end
                else
                    if self._captainPlayerInfo.sol_rank_attended == true then
                        self._wtRankIcon:SetRankingIconByScore(self._captainPlayerInfo.sol_rank_score)
                        local rankInfo = Module.Ranking:GetMinorDataByScore(self._captainPlayerInfo.sol_rank_score)
                        self._wtRankDivision:SetText(rankInfo and rankInfo.RankName or RecruitConfig.Loc.NoRankDivision)
                    else
                        self._wtRankIcon:SetRankIconNone()
                    end
                end

                --- BEGIN MODIFICATION @ VIRTUOS
                if IsConsole() and self._wtPlatformIcon then
                    local platID = self._recruitmentInfo.playerInfoList[index].plat_id
                    local platIconPath = Module.Friend:GetPlatformIconPath(platID, false, false)
                    if platIconPath then
                        self._wtPlatformIcon:AsyncSetImagePath(platIconPath, false)
                        self._wtPlatformIcon:SelfHitTestInvisible()
                    else
                        self._wtPlatformIcon:Collapsed()
                    end
                end
                --- using Platform Online ID as player name.
                if IsPS5() then
                    local callback = function(onlineID)
                        self._wtPlayerNameTxt:SetText(onlineID)
                    end
                    Module.Social:AsyncGetPS5OnlineIdByUID(self._captainPlayerInfo.player_id, callback, self)
                end
                --- END MODIFICATION
            end
        end
        local numOfLabel = 0
        for groupId, group in pairs(self._recruitmentInfo.filter.selectedLabelIDs) do
            numOfLabel = numOfLabel + table.nums(group)
        end
        if numOfLabel > 0 then
            Timer.DelayCall(0.01, function()
                Facade.UIManager:RemoveSubUIByParent(self, self._wtLabelContainer)
                local labelContainerGeometry = self._wtLabelContainer:GetCachedGeometry()
                local containerSize = labelContainerGeometry:GetLocalSize()
                local labelInfo 
                local stop = false
                for groupId, group in pairs(self._recruitmentInfo.filter.selectedLabelIDs) do
                    for labelId, value in pairs(group) do
                        if self._wtLabelContainer:GetChildrenCount() >= 3 then
                            stop = true
                            break
                        end
                        local weakUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.RecruitmentInfoLabelItem, self._wtLabelContainer)
                        local itemWidget = getfromweak(weakUIIns)
                        if itemWidget then
                            local fontSize = itemWidget:GetFontSize()*1.2
                            local numOfByteCharAllowedEachLabel = math.floor((containerSize.X-(fontSize*2*numOfLabel))/numOfLabel/fontSize)
                            labelInfo = {}
                            labelInfo.id = labelId
                            labelInfo.name = RecruitConfig.LabelId2Text[labelId]
                            labelInfo.iconPath = RecruitConfig.LabelId2IconPath[labelId]
                            itemWidget:InitLabelInfo(labelInfo, numOfByteCharAllowedEachLabel)
                        end
                    end
                    if stop == true then
                        break
                    end
                end
                self._bIsLoading = false
            end)
        else
            Facade.UIManager:RemoveSubUIByParent(self, self._wtLabelContainer)
            self._wtLabelContainer:Collapsed()
            self._bIsLoading = false
        end
    else
        Facade.UIManager:RemoveSubUIByParent(self, self._wtLabelContainer)
        self._wtLabelContainer:Collapsed()
        self._bIsLoading = false
    end
    self:_OnTeamMemberInfoChanged()
end

function RecruitmentInfoItem:_OnJoinBtnClicked()
    self._wtJoinBtn:Hidden()
    if self._recruitmentInfo.filter.micChoice == RecruitmentMicrophoneType.RecruitmentMicrophoneEnable then
        RecruitLogic.AddTeamIdRequireMic(self._recruitmentInfo.teamId)
    end
    -- BEGIN MODIFICATION - VIRTUOS
    local function handleJoinBtnClicked()
    -- END MODIFICATION - VIRTUOS
            Server.TeamServer:ApplyJoinFromRecruitment(
                self._recruitmentInfo.teamId
            )
            Timer.DelayCall(
                RecruitConfig.JoinBtnDelayTime,
                function()
                    if hasdestroy(self) then
                        return
                    end
                    self:_OnTeamMemberInfoChanged()
                end
            )
    end
    -- BEGIN MODIFICATION - VIRTUOS
    if IsXSX() or IsPS5() then
        Module.Team:CanTeamWithPlayerViaCrossPlayPermission(self._recruitmentInfo.captainId, function(res)
            if res then
                handleJoinBtnClicked()
            else
                -- 双方跨平台权限不一致，无法一起组队
                Module.CommonTips:ShowSimpleTip(Module.Team.Config.Loc.CannotTeamUpDueToCrossNetworkPrivilegeMismatch)
            end
        end)
    else
        handleJoinBtnClicked()
    end
    -- END MODIFICATION - VIRTUOS    
end

function RecruitmentInfoItem:_OnTeamMemberInfoChanged()
    if hasdestroy(self) then
        return
    end
    if self._recruitmentInfo ~= nil then
        if self._captainPlayerInfo ~= nil and self._recruitmentInfo.teamId ~= Server.TeamServer:GetTeamID() then
            self._wtJoinBtn:SelfHitTestInvisible()
            return
        end
    end
    if hasdestroy(self._wtJoinBtn) then
        return
    end
    self._wtJoinBtn:Hidden()
end


function RecruitmentInfoItem:PlayInAnim(position)
    if self._timerHandle then
        Timer.CancelDelay(self._timerHandle)
        self._timerHandle = nil
    end
    self._timerHandle = Timer.DelayCall((position or 0) * 0.067, function ()
        self:SelfHitTestInvisible()
        self:PlayAnimation(self.WBP_Recruit_Main_RecruitInfo_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    end, self)
end


return RecruitmentInfoItem
