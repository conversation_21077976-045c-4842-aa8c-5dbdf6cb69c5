----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



--------------------------------------------------------------------------
--- UI路径映射配置示例（UILayer表示需要加入的层级）
--------------------------------------------------------------------------
UITable[UIName2ID.ActivityMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityMainPanel",
    BPKey = "WBP_Activity_MainPanel",
    ReConfig = {
        IsPoolEnable = true,
        IsPoolEnableLow = true, 
    },
    IsReleaseClearRef = true,
    SubUIs = {
        UIName2ID.ActivityTabBtn,--手游Tab按钮
        UIName2ID.ActivityScreenCovering,
        UIName2ID.ActivityItemPanel,
        UIName2ID.ActivityDailiesPanel,
        UIName2ID.ActivitySignInPanel,
        UIName2ID.ActivityFullScreenButton,
        UIName2ID.ActivityGrowthMilestones,
        UIName2ID.ActivityNoviceChallenge,
        UIName2ID.ActivityGrowthSignInPanel,
        UIName2ID.ActivityHomePage,
        UIName2ID.ActivityFrontPage,
        UIName2ID.ActivityHafkIntelligence,
        UIName2ID.ActivityAsaraBazaar,
        UIName2ID.ActivityExchangeOnly,
        UIName2ID.ActivityCommonTask,
        UIName2ID.ActivitySingleTask,
        UIName2ID.ActivityTopicPanel,
        UIName2ID.ActivityTastingWeaponsPanel,
        UIName2ID.ActivityOrderedPanel,
        UIName2ID.ActivityCollectionPanel,
        UIName2ID.ActivitySniperPanel,
        UIName2ID.ActivityAssemble,
        UIName2ID.ActivityMossMainPanel,
        UIName2ID.ActivityLotteryDrawPanel,
        UIName2ID.ActivityStarFireMainPanel,
        UIName2ID.ActivityFilingTaskMainPanel,
        UIName2ID.ActivityExchangeHaffCoins,
        UIName2ID.ActivityDecryptMainPanel,
        UIName2ID.ActivityRoleSurvey,
        UIName2ID.ActivityRocketLaunch,
        UIName2ID.ActivityStars,
        UIName2ID.CommonEmptyContent,
        UIName2ID.ActivityBartender,
        UIName2ID.ActivityEquipment,
        UIName2ID.ActivityAccessories,
        UIName2ID.ActivityWeapon,
        UIName2ID.ActivitySignInTemplatePanel,
        UIName2ID.ActivityCommonDailyTask,
        UIName2ID.ActivityPeakPanel,
        UIName2ID.ActivityRadioMainPanel,
        UIName2ID.ActivityUniversalExchangeTemplatePanel,
        UIName2ID.MPVehicleUnlockMainPanel,
        UIName2ID.PandoraActivitySubUI,
        UIName2ID.ActivitySimpleMilestonesPanel,
        UIName2ID.ActivitySubscribePanel,
        UIName2ID.ActivityRadioItemV3,
        UIName2ID.ActivityStoreSubPanel,
        UIName2ID.ArkRecruitMain,
        UIName2ID.PandoraSubActivity,
        UIName2ID.ActivityTaraMain,
        UIName2ID.CommonStoreHome,
        UIName2ID.MorgenMain,
    },
    Anim = {
        FlowInAni = "WBP_Activity_MainPanel_in_xuanfu",
        FlowOutAni = "WBP_Activity_MainPanel_out"
    }
}

UITable[UIName2ID.ActivitySubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivitySubItem",
    BPKey = "WBP_Activity_LeftTab",
}

UITable[UIName2ID.ActivityCommonDesction] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityCommonDesction",
    BPKey = "WBP_Activity_EventDescriptionPop",
    IsModal = true,
}

UITable[UIName2ID.ActivityFrontPage] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FrontPage.ActivityFrontPage",
    BPKey = "WBP_Activity_Entrance",
    SubUIs = {
        UIName2ID.ActivityFrontPageRow,
        UIName2ID.ActivityFrontPageCrossoverBg,
        UIName2ID.ActivityFrontPageNormalLayout,
        UIName2ID.ActivityFrontPageCrossoverLayout,
        UIName2ID.ActivityFrontPageAnniversaryLayout,
    }
}

UITable[UIName2ID.ActivityFrontPageCrossoverBg] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.YxFramework.Managers.UI.LuaUIBaseView",
    BPKey = "WBP_Activity_CrossoverBg",
}

UITable[UIName2ID.ActivityFrontPageNormalLayout] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FrontPage.Layouts.ActivityFrontPageNormalLayout",
    BPKey = "WBP_Activity_NormalLayout",
    SubUIs = {
        UIName2ID.ActivityFrontPageRow
    }
}

UITable[UIName2ID.ActivityFrontPageCrossoverLayout] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FrontPage.Layouts.ActivityFrontPageCrossoverLayout",
    BPKey = "WBP_Activity_CrossoverLayout",
    SubUIs = {
        UIName2ID.ActivityFrontPageBtn
    }
}

UITable[UIName2ID.ActivityFrontPageAnniversaryLayout] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FrontPage.Layouts.ActivityFrontPageAnniversaryLayout",
    BPKey = "WBP_Activity_NormalLayout_alias1",
    SubUIs = {
        UIName2ID.ActivityFrontPageRow
    }
}

UITable[UIName2ID.ActivityFrontPageRow] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FrontPage.Layouts.ActivityFrontPageRow",
    BPKey = "WBP_Activity_EntranceBtnBox",
    SubUIs = {
        UIName2ID.ActivityFrontPageBtn
    }
}

UITable[UIName2ID.ActivityFrontPageBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FrontPage.ActivityFrontPageBtn",
    BPKey = "WBP_Activity_EntranceBtn",
}  

UITable[UIName2ID.ActivityTabItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityTabItem",
    BPKey = "WBP_Activity_StarterQuestsTab"
}

UITable[UIName2ID.ActivityScreenCovering] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityScreenCovering",
    BPKey = "WBP_Activity_ScreenCovering"
    -- Anim = {
    --     FlowInAni = "WBP_Activity_ScreenCovering_in",
    --     FlowOutAni = "WBP_Activity_ScreenCovering_out",
    -- }
}

UITable[UIName2ID.PandoraActivitySubUI] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.GameletModule.UI.GameletSub.GameletSubActivity",
    BPKey = "WBP_GameletUIPanel",
}

--通用简易任务模板
UITable[UIName2ID.ActivityCommonTask] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityCommonTask",
    BPKey = "WBP_Activity_Common",
    Anim = {
        FlowInAni = "WBP_Activity_Common_In",
        FlowOutAni = "WBP_Activity_Common_Out"
    }
}

--简易七日模板
UITable[UIName2ID.ActivityCommonDailyTask] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityCommonDailyTask",
    BPKey = "WBP_Activity_Common_Extension",
    Anim = {
        FlowInAni = "WBP_Activity_Common_In",
        FlowOutAni = "WBP_Activity_Common_Out"
    }
}

--载具解锁模板
UITable[UIName2ID.MPVehicleUnlockMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MPVehicleUnlock.MPVehicleUnlockMainPanel",
    BPKey = "WBP_PlayVehicle_Main",
    Anim = {
        FlowInAni = "WBP_Activity_Common_In",
        FlowOutAni = "WBP_Activity_Common_Out"
    }
}

--兑换商城
UITable[UIName2ID.ActivityItemPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityItemPanel",
    BPKey = "WBP_Activity_ItemPanel",
    SubUIs = {
        UIName2ID.ActivityItemAcquisition,
        UIName2ID.ActivityExchangeMall
    },
    Anim = {
        FlowInAni = "WBP_Activity_ItemPanel_in",
        FlowOutAni = "WBP_Activity_ItemPanel_out"
    }
}

--哈夫克/新版里程碑
UITable[UIName2ID.ActivityHafkIntelligence] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityHafkIntelligence",
    BPKey = "WBP_Activity_HafkIntelligence",
    SubUIs = {
        UIName2ID.ActivityGrowthRewardList,
        UIName2ID.ActivityItemAcquisition,
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
        FlowInAni = "WBP_Activity_MilestonesPanel_in",
        FlowOutAni = "WBP_Activity_MilestonesPanel_out"
    }
}

--阿萨拉/新版兑换商城
UITable[UIName2ID.ActivityAsaraBazaar] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityAsaraBazaar",
    BPKey = "WBP_Activity_AsaraBazaar",
    Anim = {
        FlowInAni = "WBP_Activity_ItemPanel_in",
        FlowOutAni = "WBP_Activity_ItemPanel_out"
    },
    SubUIs = {
        UIName2ID.ActivityRedeemMall,
        UIName2ID.ActivityItemAcquisition,
        UIName2ID.ActivityBazaarProjects
    },
}

--任务/兑换按钮
UITable[UIName2ID.ActivityBazaarProjects] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityBazaarProjects",
    BPKey = "WBP_Activity_BazaarProjects",
}

-- --里程碑任务
-- UITable[UIName2ID.ActivityMilestonesPanel] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityMilestonesPanel",
--     BPKey = "WBP_Activity_MilestonesPanel",
--     SubUIs = {
--         UIName2ID.ActivityGrowthRewardList,
--         UIName2ID.ActivityMilestonesSub,
--         UIName2ID.IVCommonItemTemplate,
--     },
--     Anim = {
--         FlowInAni = "WBP_Activity_MilestonesPanel_in",
--         FlowOutAni = "WBP_Activity_MilestonesPanel_out"
--     }
-- }

--里程碑大奖选择弹窗
UITable[UIName2ID.ActivityPickRewardPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityPickRewardPop",
    BPKey = "WBP_Reflow_ChangeRewards",
    SubUIs = {UIName2ID.ActivityPickRewardItem},
    IsModal = true,
}

UITable[UIName2ID.ActivityPickRewardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityPickRewardItem",
    BPKey = "WBP_Reflow_ChangeRewards_Item",
}

--旧里程碑奖励[暂时废弃]
UITable[UIName2ID.ActivityRewardList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityRewardList",
    BPKey = "WBP_Activity_MilestonePrizes",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--新里程碑奖励
UITable[UIName2ID.ActivityGrowthRewardList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityGrowthRewardList",
    BPKey = "WBP_Activity_MilestonePrizes_3",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--单一任务模板
UITable[UIName2ID.ActivitySingleTask] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivitySingleTask",
    BPKey = "WBP_Activity_SingleRewardPanel",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--任务子UI1
UITable[UIName2ID.ActivityItemAcquisition] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityItemAcquisition",
    BPKey = "WBP_Activity_ItemAcquisition_1",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--任务子UI2
UITable[UIName2ID.ActivityItemAcquisition2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityItemAcquisition2",
    BPKey = "WBP_Activity_ItemAcquisition_2",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--纯兑换类
UITable[UIName2ID.ActivityExchangeOnly] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityExchangeOnly",
    BPKey = "WBP_Activity_ExchangeOnly",
    Anim = {
        FlowInAni = "WBP_Activity_ItemPanel_in",
        FlowOutAni = "WBP_Activity_ItemPanel_out"
    }
}

--纯兑换双物品框
UITable[UIName2ID.ActivityExchangeHorizontal] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityExchangeHorizontal",
    BPKey = "WBP_Activity_ItemExchange_Horizontal"
}

--任务跳转子UI
UITable[UIName2ID.ActivityTaskJump] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityTaskJump",
    BPKey = "WBP_Activity_TaskJump",
    Anim = {
        FlowInAni = "WBP_Activity_AsaraBazaar_Item_in",
        FlowOutAni = "WBP_Activity_AsaraBazaar_Item_out"
    }
}

--每日任务
UITable[UIName2ID.ActivityDailiesPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.DailyTasks.ActivityDailiesPanel",
    BPKey = "WBP_Activity_DailyActivity",
    SubUIs = {
        UIName2ID.ActivityDailiesSub,
        -- UIName2ID.ActivityCommonButtonV2S1,
        -- UIName2ID.ActivityCommonButtonV2S2,
    },
    Anim = {
        FlowInAni = "WBP_Activity_DailyActivity_In",
        -- FlowOutAni = "WBP_Activity_DailyActivity_Out"
    }
}

--每日任务子UI
UITable[UIName2ID.ActivityDailiesSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.DailyTasks.ActivityDailiesSub",
    BPKey = "WBP_Activity_DailyActivityBox_1",
    SubUIs = {
        UIName2ID.ActivityCommonButtonV2S1,
        UIName2ID.ActivityCommonButtonV2S2,
        UIName2ID.IVCommonItemTemplate,
    },
}

--首页大屏
UITable[UIName2ID.ActivityHomePage] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.HomePage.ActivityHomePage",
    BPKey = "WBP_Activity_HomePage",
    Anim = {
        FlowInAni = "WBP_Activity_HomePage_in",
        FlowOutAni = "WBP_Activity_HomePage_out"
    }
}

--首页子UI
UITable[UIName2ID.ActivityHomePageSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.HomePage.ActivityHomePageSub",
    BPKey = "WBP_Activity_HomePageBtn",
    SubUIs = {
        UIName2ID.ActivityHomePagePoster,
    },
    Anim = {
        FlowInAni = "WBP_Activity_HomePageBtn_in",
        FlowOutAni = "WBP_Activity_HomePageBtn_out"
    }
}

--首页子UI图片
UITable[UIName2ID.ActivityHomePagePoster] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.HomePage.ActivityHomePagePoster",
    BPKey = "WBP_Activity_HomePosters",
    Anim = {
        FlowInAni = "IN",
        FlowOutAni = "OUT"
    }
}

--全屏任务
UITable[UIName2ID.ActivityFullScreenButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.FullScreen.ActivityFullScreenButton",
    BPKey = "WBP_Activity_TrackTasksEntrance"
}

--兑换商城子UI1
UITable[UIName2ID.ActivityRedeemMall] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityRedeemMall",
    BPKey = "WBP_Activity_RedeemMall"
}

--兑换商城子UI2
UITable[UIName2ID.ActivityExchangeMall] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityExchangeMall",
    BPKey = "WBP_Activity_ItemExchange_1",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--兑换商城子UI弹窗
UITable[UIName2ID.ActivityRedeemPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeMall.ActivityRedeemPopWindow",
    BPKey = "WBP_Activity_RewardRedemptionPop",
    Anim = {
        FlowInAni = "WBP_Activity_RewardRedemptionPop_in",
        FlowOutAni = "WBP_Activity_RewardRedemptionPop_out",
    },
    IsModal = true,
}

--追踪按钮
UITable[UIName2ID.ActivityTrackingBar] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityTrackingBar",
    BPKey = "WBP_Activity_TrackingBar"
    -- Anim = {
    --     FlowInAni = "WBP_Activity_TrackingBar_in",
    --     FlowOutAni = "WBP_Activity_TrackingBar_out",
    -- }
}

--签到任务
UITable[UIName2ID.ActivitySignInPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.SignIn.ActivitySignInPanel",
    BPKey = "WBP_Activity_SignInPanel",
    Anim = {
        FlowInAni = "WBP_Activity_SignInPanel_in",
        FlowOutAni = "WBP_Activity_SignInPanel_out"
    }
}

--签到子UI1
UITable[UIName2ID.ActivitySignInItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.SignIn.ActivitySignInItem",
    BPKey = "WBP_Activity_SignIn_1"
    -- Anim = {
    --     FlowInAni = "WBP_Activity_SignIn_1_loop",
    -- }
}

--签到子UI2
UITable[UIName2ID.ActivityGrowthSignInItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.SignIn.ActivityGrowthSignInItem",
    BPKey = "WBP_Activity_SignIn_1"
}

--按钮1
UITable[UIName2ID.ActivityCommonButtonV2S2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonButtonOnly",
    BPKey = "WBP_DFCommonButtonV2S2"
}

--按钮2
UITable[UIName2ID.ActivityCommonButtonV2S1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonButtonOnly",
    BPKey = "WBP_DFCommonButtonV2S2_BasicsButton"
}

-- --New红点
-- UITable[UIName2ID.ReddotDotNew] =
-- {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.ReddotTrieModule.UI.ReddotDotNew",
--     BPKey = "WBP_CommonPoint_V2",
-- }

-- --Hot红点
-- UITable[UIName2ID.ReddotDotHot] =
-- {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.ReddotTrieModule.UI.ReddotDotTodo",
--     BPKey = "WBP_CommonPoint_V6",
-- }

--新兵里程碑
UITable[UIName2ID.ActivityGrowthMilestones] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityGrowthMilestones",
    BPKey = "WBP_Activity_DailiesTotalPanel_4",
    SubUIs = {
        UIName2ID.ActivityGrowthRewardList,
        UIName2ID.ActivityMilestonesSub,
        UIName2ID.IVCommonItemTemplate,
    },
    Anim = {
        FlowInAni = "WBP_Activity_DailiesTotalPanel_2_in",
        FlowOutAni = "WBP_Activity_DailiesTotalPanel_2_out"
    }
}

--任务子UI
UITable[UIName2ID.ActivityMilestonesSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityMilestonesSub",
    BPKey = "WBP_Activity_DailiesPanel_5",
    SubUIs = {
        UIName2ID.ActivityCommonButtonV2S1,
        UIName2ID.ActivityCommonButtonV2S2,
        UIName2ID.IVCommonItemTemplate,
    },
}

--新兵挑战
UITable[UIName2ID.ActivityGrowthChallenge] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityGrowthChallenge",
    BPKey = "WBP_Activity_DailiesTotalPanel_3",
    Anim = {
        FlowInAni = "WBP_Activity_DailiesTotalPanel_3_in",
        FlowOutAni = "WBP_Activity_DailiesTotalPanel_3_out"
    }
}

--新兵挑战界面
UITable[UIName2ID.ActivityNoviceChallenge] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityNoviceChallenge",
    BPKey = "WBP_Activity_NoviceChallenge",
    Anim = {
        -- FlowInAni = "WBP_Activity_NoviceChallenge_in",
        FlowOutAni = "WBP_Activity_NoviceChallenge_out"
    }
}

--新兵挑战按钮
UITable[UIName2ID.ActivityChallengeProjects] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityChallengeProjects",
    BPKey = "WBP_Activity_ChallengeProjects",
}

UITable[UIName2ID.ActivityCommonProject] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityCommonProject",
    BPKey = "WBP_Activity_CommonProjects",
    Anim = {
        FlowInAni = "WBP_Activity_ChallengeProjects_in",
        FlowOutAni = "WBP_Activity_ChallengeProjects_out"
    }
}

--新兵挑战列表
UITable[UIName2ID.ActivitySetDifference] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivitySetDifference",
    BPKey = "WBP_Activity_SetDifference",
    Anim = {
        FlowInAni = "WBP_AuctionOnShelfUncountable_in",
        FlowOutAni = "WBP_AuctionOnShelfUncountable_out"
    },
    IsModal = true,
}

--新兵挑战任务item
UITable[UIName2ID.ActivityDailiesPanelItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityDailiesPanelItem",
    BPKey = "WBP_Activity_DailiesPanel_4",
    SubUIs = {
        UIName2ID.ActivityCommonButtonV2S1,
        UIName2ID.ActivityCommonButtonV2S2
    },
}

--新兵挑战子UI
UITable[UIName2ID.ActivityChallengeSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityChallengeSub",
    BPKey = "WBP_Activity_DailiesPanel_3",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    },
}

--新兵签到任务
UITable[UIName2ID.ActivityGrowthSignInPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Recruits.ActivityGrowthSignInPanel",
    BPKey = "WBP_Activity_SignInPanel_2",
    Anim = {
        FlowInAni = "WBP_Activity_SignInPanel_2_in",
        FlowOutAni = "WBP_Activity_SignInPanel_2_out"
    }
}

--收藏模版活动
UITable[UIName2ID.ActivityCollectionPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Collection.ActivityCollectionPanel",
    BPKey = "WBP_Activity_CollectionPanel",
    Anim = {
        FlowInAni = "WBP_Activity_CollectionPanel_in",
        FlowOutAni = "WBP_Activity_CollectionPanel_out"
    },
    SubUIs = {
        UIName2ID.ActivityCollectionItem,
    },
}

--收藏模版活动
UITable[UIName2ID.ActivityCollectionItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Collection.ActivityCollectionItem",
    BPKey = "WBP_Activity_CollectionPanelItem",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    },
}

--//品鉴panel
UITable[UIName2ID.ActivityTastingWeaponsPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Appraise.ActivityTastingWeaponsPanel",
    BPKey = "WBP_Activity_TastingWeapons",
    SubUIs = {
        UIName2ID.ActivityTastingWeaponsItem,
        UIName2ID.ActivityItemAcquisition,
    },
    Anim = {
        FlowInAni = "WBP_Activity_ItemPanel_in",
        FlowOutAni = "WBP_Activity_ItemPanel_out"
    }
}

--品鉴沾板详情item
UITable[UIName2ID.ActivityTastingWeaponsItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Appraise.ActivityTastingWeaponsItem",
    BPKey = "WBP_Activity_TastingWeaponsItem",
}
--//end

--主题活动
UITable[UIName2ID.ActivityTopicPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Topic.ActivityTopicPanel",
    BPKey = "WBP_Activity_TopicActivity",
    Anim = {
        FlowInAni = "WBP_Activity_DailiesTotalPanel_2_in",
        FlowOutAni = "WBP_Activity_DailiesTotalPanel_2_out"
    },
    SubUIs = {
        UIName2ID.ActivityItemAcquisition,
        UIName2ID.ActivityTopicPasswordItem,
        UIName2ID.IVCommonItemTemplate,
    }
}

--主题密码item
UITable[UIName2ID.ActivityTopicPasswordItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Topic.ActivityTopicPasswordItem",
    BPKey = "WBP_Activity_TopicPasswordBox"
}

--主题地图弹窗
UITable[UIName2ID.ActivityTopicPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Topic.ActivityTopicPopWindow",
    BPKey = "WBP_ActivityMapInformation",
    IsModal = true,
}

--主题地图弹窗子UI
UITable[UIName2ID.ActivityTopicPopWindowSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Topic.ActivityTopicPopWindowSub",
    BPKey = "WBP_ActivityMapInformationItem"
}

--串行顺序
UITable[UIName2ID.ActivityOrderedPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Order.ActivityOrderedPanel",
    BPKey = "WBP_Activity_SequenceModePanel",
    Anim = {
        FlowInAni = "WBP_Activity_SequenceModePanel_in",
        FlowOutAni = "WBP_Activity_SequenceModePanel_out"
    },
    SubUIs = {
        UIName2ID.ActivityOrderedItem,
    }
}

--串行顺序item
UITable[UIName2ID.ActivityOrderedItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Order.ActivityOrderedItem",
    BPKey = "WBP_Activity_SequenceModePanel_Box",
    Anim = {
        FlowInAni = "WBP_Activity_DailyActivityBox_1_In",
    },
    SubUIs = {
        UIName2ID.IVCommonItemTemplate,
    }
}

--狙击制作人
UITable[UIName2ID.ActivitySniperPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Sniper.ActivitySniperPanel",
    BPKey = "WBP_Activity_SniperMaker",
    SubUIs = {
        UIName2ID.ActivitySniperStatus,
        UIName2ID.ActivityMilestonesSub,
    },
    Anim = {
        FlowInAni = "WBP_Activity_SniperMaker_in",
    },
}

UITable[UIName2ID.ActivitySniperStatus] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Sniper.ActivitySniperStatus",
    BPKey = "WBP_Activity_ProducerStatus",
}

--活动H5模板
UITable[UIName2ID.ActivityAssemble] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Assemble.ActivityAssemble",
    BPKey = "WBP_Activity_SkipPanel",
}

---//活动:建设Moss
UITable[UIName2ID.ActivityMossMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityMossMainPanel",
    BPKey = "WBP_Activity_ConstructionMoss",
    SubUIs = {
        UIName2ID.ActivityItemAcquisition,
        UIName2ID.ActivityMossMainItem,
        UIName2ID.ActivityGrowthRewardList,
        UIName2ID.ActivityFinalRewardView,
    },
    Anim = {
        FlowInAni = "WBP_Activity_MilestonesPanel_in",
        FlowOutAni = "WBP_Activity_MilestonesPanel_out"
    },
}

--建设Moss:Item
UITable[UIName2ID.ActivityMossMainItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityMossMainItem",
    BPKey = "WBP_Activity_ConstructionMoss_Box",
}

--建设Moss:Item2
UITable[UIName2ID.ActivityMossMainItem2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityMossMainItem",
    BPKey = "WBP_Activity_TotalComputingPower",
}

--建设Moss:替换硬件
UITable[UIName2ID.ActivityMossHardwarePanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityMossHardwarePanel",
    BPKey = "WBP_Activity_ReplacemenHardwaret",
    IsAnimBlock = false,
    IsModal = true,
}

--建设Moss:提升算法效率
UITable[UIName2ID.ActivityMossAlgorithmPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityMossAlgorithmPanel",
    BPKey = "WBP_Activity_AlgorithmEfficiency",
    SubUIs = {
        UIName2ID.ActivityMossAlgorithmItem,
    },
    IsAnimBlock = false,
    IsModal = true,
}

--建设Moss:提升算法效率item
UITable[UIName2ID.ActivityMossAlgorithmItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityMossAlgorithmItem",
    BPKey = "WBP_Activity_AlgorithmEfficiency_Box",
}

--里程碑最高奖励
UITable[UIName2ID.ActivityFinalRewardView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Moss.ActivityFinalRewardView",
    BPKey = "WBP_Activity_FinalReward",
}
---//end

---//活动抽奖系统
UITable[UIName2ID.ActivityLotteryDrawPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityLotteryDrawPanel",
    BPKey = "WBP_Activity_PrizeDraw",
    SubUIs = {
        UIName2ID.ActivityLotteryDrawItem,
        UIName2ID.ActivityDrawName1,
        UIName2ID.ActivityDrawName2,
    },
    Anim = {
        FlowInAni = "WBP_Activity_PrizeDraw_in",
        FlowOutAni = "WBP_Activity_PrizeDraw_out"
    },
}

--活动抽奖系统:奖励列表
UITable[UIName2ID.ActivityLotteryDrawItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityLotteryDrawItem",
    BPKey = "WBP_Activity_PrizeDetails",
    SubUIs = {
        UIName2ID.StorePrizesList,
        UIName2ID.CommonEmptyContent,
    },
}

--活动抽奖系统:武器名称
UITable[UIName2ID.ActivityDrawName1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityDrawName1",
    BPKey = "WBP_Activity_PrizeName",
}

--活动抽奖系统:武器名称
UITable[UIName2ID.ActivityDrawName2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityDrawName2",
    BPKey = "WBP_Activity_PrizeName2",
}

--活动抽奖系统:物流凭证
UITable[UIName2ID.ActivityLogisticsVoucherPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityLogisticsVoucherPanel",
    BPKey = "WBP_Activity_PrizeDrawVoucherPop",
    SubUIs = {
        UIName2ID.ActivityLogisticsVoucherItem,
        UIName2ID.CommonEmptyContent,
    },
    IsModal = true,
}

--活动抽奖系统:物流凭证item
UITable[UIName2ID.ActivityLogisticsVoucherItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityLogisticsVoucherItem",
    BPKey = "WBP_Activity_GetVoucherBox",
}

UITable[UIName2ID.ActivityWeaponDisplayPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Reward.ActivityWeaponDisplayPanel",
    BPKey = "WBP_Activity_ProductPreview",
    ReConfig = {
        IsPoolEnable = true,
    },
    LinkSubStage = ESubStage.HallWeaponShow,
}
---//end

--活动手游tab按钮优化
-- UITable[UIName2ID.ActivityTabBtn] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.ActivityModule.UI.ActivityBtn.ActivityTabBtn",
--     BPKey = "WBP_Activity_Tab1",
-- }

UITable[UIName2ID.ActivityTabBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.CommonUILibraryModule.UI.Library.DFCommonCheckButtonOnly",
    BPKey = "WBP_DFCommonCommercialTabV1",
}

--星火主题活动
UITable[UIName2ID.ActivityStarFireMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireMainPanel",
    BPKey = "WBP_Activity_StarFire_Main",
    SubUIs = {
        UIName2ID.ActivityStarFireSingleTower,
        UIName2ID.ActivityStarFireStageBtn,
        UIName2ID.ActivityStarFireStageDetail,
        UIName2ID.ActivityStarFireTaskPanel,
        UIName2ID.ActivityStarFireTaskEntry,
    },
    -- Anim = {
    --     FlowInAni = "WBP_Activity_StarFire_Main_in",
    --     FlowOutAni = "WBP_Activity_StarFire_Main_out"
    -- },
}

UITable[UIName2ID.ActivityStarFireTaskPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireTaskPanel",
    BPKey = "WBP_Activity_StarFire_TaskPanel_01",
    Anim = {
        FlowInAni = "WBP_Activity_StarFire_TaskPanel_01_in",
        FlowOutAni = "WBP_Activity_StarFire_TaskPanel_01_out"
    },
}

UITable[UIName2ID.ActivityStarFireSingleTower] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireSingleTower",
    BPKey = "WBP_Activity_StarFire_TaskPanel_02",
    Anim = {
        FlowInAni = "WBP_Activity_StarFire_TaskPanel_02_in",
        FlowOutAni = "WBP_Activity_StarFire_TaskPanel_02_out"
    },
}

UITable[UIName2ID.ActivityStarFireTaskEntry] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireTaskEntry",
    BPKey = "WBP_Activity_StarFire_TaskEntry",
}

UITable[UIName2ID.ActivityStarFireStageDetail] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireStageDetail",
    BPKey = "WBP_Activity_StarFire_StageDetail",
}

UITable[UIName2ID.ActivityStarFireStageBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireStageBtn",
    BPKey = "WBP_Activity_StarFire_StageBtn",
}

UITable[UIName2ID.ActivityStarFireClueDesction] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.StarFire.ActivityStarFireClueDesction",
    BPKey = "WBP_Activity_CluePop",
    IsModal = true,
}

-- //归档主题活动

-- 归档活动-首次弹出窗口
UITable[UIName2ID.ActivityFilingFirstPopWnd] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.ActivityFilingFirstPopWnd",
    BPKey = "WBP_Activity_FilingActivityHint",
    IsModal = true,

}

-- 归档活动-主界面
UITable[UIName2ID.ActivityFilingTaskMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.ActivityFilingTaskMainPanel",
    BPKey = "WBP_Activity_KillBossAdjutant",
    SubUIs = {
        UIName2ID.ActivityFilingProgress,
        UIName2ID.ActivityFilingLocation,
        UIName2ID.ActivityFilingArchivesPanel
    },
    Anim = {
        FlowInAni = "WBP_Activity_KillBossAdjutant_in",
        FlowOutAni = "WBP_Activity_KillBossAdjutant_out"
    },
}

-- 归档活动-主界面语音
UITable[UIName2ID.ActivityFilingDialogBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.ActivityFilingDialogBox",
    BPKey = "WBP_Activity_DialogBox",
    Anim = {
        FlowInAni = "WBP_Activity_DialogBox_In",
        FlowOutAni = "WBP_Activity_DialogBox_Out"
    },
}

-- 归档活动-主界面归档进度
UITable[UIName2ID.ActivityFilingProgress] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.ActivityFilingProgress",
    BPKey = "WBP_Activity_FilingProgress",
    SubUIs = {
        UIName2ID.ActivityFilingItemView,
    },
    Anim = {
        FlowInAni = "WBP_Activity_FilingProgress_in",
        FlowOutAni = "WBP_Activity_FilingProgress_out"

    },
}

-- 归档活动-物品按钮
UITable[UIName2ID.ActivityFilingItemView] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.ActivityFilingItemView",
    BPKey = "WBP_Activity_Itemview",
}

-- 归档活动-主界面地点
UITable[UIName2ID.ActivityFilingLocation] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Tasks.ActivityFilingLocation",
    BPKey = "WBP_Activity_BossTaskBtn",
    SubUIs = {
        UIName2ID.ActivityFilingBossHeadShot,
    },
    Anim = {
        FlowInAni = "WBP_Activity_BossTaskBtn_in",
        FlowOutAni = "WBP_Activity_BossTaskBtn_out"
    },
}

-- 归档活动-主界面Boss图标
UITable[UIName2ID.ActivityFilingBossHeadShot] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Tasks.ActivityFilingBossHeadShot",
    BPKey = "WBP_Activity_BossHeadshot",
    Anim = {
        FlowInAni = "WBP_Activity_BossHeadshot_in",
    },
}

-- 归档活动-主界面任务
UITable[UIName2ID.ActivityFilingTaskBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Tasks.ActivityFilingTaskBox",
    BPKey = "WBP_Activity_BossTaskBox",
    SubUIs = {
        UIName2ID.ActivityFilingItemView,
    },
}

-- 归档活动-主界面提交信物
UITable[UIName2ID.ActivityFilingSubmitKeepsake] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Tasks.ActivityFilingSubmitKeepsake",
    BPKey = "WBP_Activity_SubmitKeepsake",
}

-- 归档活动-档案界面
UITable[UIName2ID.ActivityFilingArchivesPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.ActivityFilingArchivesPanel",
    BPKey = "WBP_Activity_KillBossArchivespanel",
    SubUIs = {
        UIName2ID.ActivityFilingBossInfoCard,
    },
    Anim = {
        FlowInAni = "WBP_Activity_KillBossArchivespanel_in",
        FlowOutAni = "WBP_Activity_KillBossArchivespanel_out"
    },
}

-- 归档活动-档案Boss卡片
UITable[UIName2ID.ActivityFilingBossInfoCard] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.ActivityFilingBossInfoCard",
    BPKey = "WBP_Activity_BossInfoCard",
    Anim = {
        FlowInAni = "WBP_Activity_BossInfoCard_in",
        FlowOutAni = "WBP_Activity_BossInfoCard_out"
    },
}

--- 归档活动-档案势力列表
UITable[UIName2ID.ActivityFilingArchivesBoxList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.ActivityFilingArchivesBoxList",
    BPKey = "WBP_Activity_ArchivesBoxList",
    SubUIs = {
        UIName2ID.ActivityFilingArchivesBox,
    },
}

--- 归档活动-档案Boss图标
UITable[UIName2ID.ActivityFilingArchivesBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.ActivityFilingArchivesBox",
    BPKey = "WBP_Activity_ArchivesBox",
}

--- 归档活动-档案下属头像
UITable[UIName2ID.********************************] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.********************************",
    BPKey = "WBP_Activity_WBP_Activity_BossHeadshot_2",
}

--- 归档活动-档案信息条目
UITable[UIName2ID.ActivityFilingArchivesInfo] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Filing.Archives.ActivityFilingArchivesInfo",
    BPKey = "WBP_Activity_InformationItems",
}


--兑换活动
UITable[UIName2ID.ActivityExchangeHaffCoins] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Collection.ActivityExchangeHaffCoins",
    BPKey = "WBP_Activity_ExchangeHaffCoins",
    SubUIs = {
        UIName2ID.ActivityExchangeHaffCoinsItem,
    },
}

UITable[UIName2ID.ActivityExchangeHaffCoinsItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Collection.ActivityExchangeHaffCoinsItem",
    BPKey = "WBP_Activity_ItemExchangeHaffCoins"
}

UITable[UIName2ID.ActivityRewardRedemptionPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Collection.ActivityRewardRedemptionPop",
    BPKey = "WBP_Activity_RewardRedemptionPop",
    Anim = {
        FlowInAni = "WBP_Activity_RewardRedemptionPop_in",
        FlowOutAni = "WBP_Activity_RewardRedemptionPop_out",
    },
    IsModal = true,
}

--活动通用控件
UITable[UIName2ID.ActivityClaimAllBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityClaimAllBtn",
    BPKey = "WBP_Activity_ClaimAllBtn",
}

UITable[UIName2ID.ActivityTaskRefreshTime] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityTaskRefreshTime",
    BPKey = "WBP_Activity_TaskRefreshTime",
}

UITable[UIName2ID.ActivityTaskUnlockTime] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityTaskUnlockTime",
    BPKey = "WBP_Activity_TaskUnlockTime",
}

--里程碑
UITable[UIName2ID.ActivityMilestoneList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityMilestoneList",
    BPKey = "WBP_Activity_MilestoneList",
}

--*****活动:脑机解密
UITable[UIName2ID.ActivityDecryptMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Decrypt.ActivityDecryptMainPanel",
    BPKey = "WBP_Activity_BrainComputer",
    SubUIs = {
        UIName2ID.ActivityDecryptBrainPanel,
        UIName2ID.ActivityDecryptPropPanel,
        UIName2ID.ActivityDecryptPropTask,
    },
    Anim = {
        FlowInAni = "WBP_Activity_BrainComputer_in",
    }
}

--脑机频道
UITable[UIName2ID.ActivityDecryptBrainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Decrypt.ActivityDecryptBrainPanel",
    BPKey = "WBP_Activity_HafkPanel",
    Anim = {
        FlowInAni = "WBP_Activity_HafkPanel_in",
    }
}

--道具频道
UITable[UIName2ID.ActivityDecryptPropPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Decrypt.ActivityDecryptPropPanel",
    BPKey = "WBP_Activity_AsaraPanel",
    SubUIs = {
        UIName2ID.ActivityDecryptPropItem,
    },
    Anim = {
        FlowInAni = "WBP_Activity_AsaraPanel_in",
    }
}

--道具子项
UITable[UIName2ID.ActivityDecryptPropItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Decrypt.ActivityDecryptPropItem",
    BPKey = "WBP_Activity_MaterialItem",
}

--道具任务
UITable[UIName2ID.ActivityDecryptPropTask] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Decrypt.ActivityDecryptPropTask",
    BPKey = "WBP_Activity_UnlockMissions",
}

-------------活动物品投放-------------
--装备
UITable[UIName2ID.ActivityEquipment] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityEquipment",
    BPKey = "WBP_PlayEquipment_Main",
    SubUIs = {
        UIName2ID.ActivityEquipmentItem,
        UIName2ID.ActivityEquipmentTaskList,
    },
    Anim = {
        FlowInAni = "WBP_PlayEquipment_Main_in",
        FlowOutAni = "WBP_PlayEquipment_Main_out"
    }
}

--装备子项
UITable[UIName2ID.ActivityEquipmentItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityEquipmentItem",
    BPKey = "WBP_PlayEquipment_Item",
}

--装备任务列表
UITable[UIName2ID.ActivityEquipmentTaskList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityEquipmentTaskList",
    BPKey = "WBP_PlayEquipment_List",
    Anim = {
        FlowInAni = "WBP_PlayEquipment_List_in",
        FlowOutAni = "WBP_PlayEquipment_List_out"
    }
}

--装备制造弹窗
UITable[UIName2ID.ActivityEquipmentPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityEquipmentPopWindow",
    BPKey = "WBP_PlayEquipment_Pop",
    IsModal = true,
}

--配件
UITable[UIName2ID.ActivityAccessories] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityAccessories",
    BPKey = "WBP_PlayAccessories_Main",
    SubUIs = {
        UIName2ID.ActivityAccessoriesItemV1,
        UIName2ID.ActivityAccessoriesItemV2,
    },
    Anim = {
        FlowInAni = "WBP_PlayAccessories_Main_in",
        FlowOutAni = "WBP_PlayAccessories_Main_out"
    }
}

--SOL配件子项
UITable[UIName2ID.ActivityAccessoriesItemV1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityAccessoriesItemV1",
    BPKey = "WBP_PlayAccessories_ItemV1",
}

--MP配件子项
UITable[UIName2ID.ActivityAccessoriesItemV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityAccessoriesItemV2",
    BPKey = "WBP_PlayAccessories_ItemV2",
}

--配件制造弹窗
UITable[UIName2ID.ActivityAccessoriesPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityAccessoriesPopWindow",
    BPKey = "WBP_PlayAccessories_Pop",
    IsModal = true,
}

--枪械
UITable[UIName2ID.ActivityWeapon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityWeapon",
    BPKey = "WBP_PlayGuns_Main",
    SubUIs = {
        UIName2ID.ActivityWeaponEntryBtn,
        UIName2ID.ActivityWeaponManufacture,
        UIName2ID.ActivityWeaponSubPanel,
    }
}

--枪械入口按钮
UITable[UIName2ID.ActivityWeaponEntryBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityWeaponEntryBtn",
    BPKey = "WBP_PlayGuns_MainBtnS1",
    Anim = {
        FlowInAni = "WBP_PlayGuns_MainBtnS1_in",
        FlowOutAni = "WBP_PlayGuns_MainBtnS1_out"
    }
}

--枪械制造框
UITable[UIName2ID.ActivityWeaponManufacture] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityWeaponManufacture",
    BPKey = "WBP_PlayGuns_Ingredientes",
    SubUIs = {
        UIName2ID.ActivityDecryptPropItem,
    },
    Anim = {
        FlowInAni = "WBP_Activity_AsaraPanel_in",
    }
}

--MP枪械子界面
UITable[UIName2ID.ActivityWeaponSubPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityWeaponSubPanel",
    BPKey = "WBP_PlayGuns_Permission",
    Anim = {
        FlowInAni = "WBP_PlayGuns_Permission_in",
        FlowOutAni = "WBP_PlayGuns_Permission_out"
    }
}

--MP头像子项
UITable[UIName2ID.ActivityWeaponSubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Distribution.ActivityWeaponSubItem",
    BPKey = "WBP_PlayGuns_PermissionItem",
}

-------------大战场巅峰赛-------------
--主面板
UITable[UIName2ID.ActivityPeakPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.PeakCompetition.ActivityPeakPanel",
    BPKey = "WBP_BattlefieldEverest_Main",
    SubUIs = {
        UIName2ID.ActivityMilestonesSub,
        UIName2ID.ActivityPeakRankingItem,
    },
    Anim = {
        FlowInAni = "WBP_BattlefieldEverest_Main_in",
        FlowOutAni = "WBP_BattlefieldEverest_Main_out"
    }
}

--排名项
UITable[UIName2ID.ActivityPeakRankingItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.PeakCompetition.ActivityPeakRankingItem",
    BPKey = "WBP_BattlefieldEverest_Ranking_Entry",
    SubUIs = {
        UIName2ID.ActivityPeakRankingNumber,
    },
    Anim = {
        FlowInAni = "WBP_BattlefieldEverest_Ranking_Entry_in"
    }
}

--排名数字
UITable[UIName2ID.ActivityPeakRankingNumber] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.PeakCompetition.ActivityPeakRankingNumber",
    BPKey = "WBP_BattlefieldEverest_RankingNumber",
}

--规则说明弹窗
UITable[UIName2ID.ActivityPeakRuleExplanation] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.PeakCompetition.ActivityPeakRuleExplanation",
    BPKey = "WBP_BattlefieldEverest_Pop",
    SubUIs = {
        UIName2ID.ActivityPeakGraphicItem,
    },
    IsModal = true,
}

--规则图例
UITable[UIName2ID.ActivityPeakGraphicItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.PeakCompetition.ActivityPeakGraphicItem",
    BPKey = "WBP_BattlefieldEverest_Graphic_Item",
}

--------------阿萨拉电台--------------
--主面板
UITable[UIName2ID.ActivityRadioMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioMainPanel",
    BPKey = "WBP_RadioNews_Main",
    SubUIs = {
        UIName2ID.ActivityRadionChildMainPanel,
        UIName2ID.ActivityRadioScreen,
        UIName2ID.ActivityCommonProject,
    },
    Anim = {
        FlowInAni = "WBP_RadioNews_Main_in",
        FlowOutAni = "WBP_RadioNews_Main_out"
    }
}

--主面板屏幕
UITable[UIName2ID.ActivityRadioScreen] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioScreen",
    BPKey = "WBP_RadioNews_Screen",
}

--子加载面板
UITable[UIName2ID.ActivityRadionChildMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadionChildMainPanel",
    BPKey = "WBP_RadioNews_ChildPanel_Main",
    SubUIs = {
        UIName2ID.ActivityRadioChildPanelV1,
        UIName2ID.ActivityRadioChildPanelV2,
        UIName2ID.ActivityRadioChildPanelV3,
    },
    -- Anim = {
    --     FlowInAni = "WBP_RadioNews_ChildPanel_Main_in",
    --     FlowOutAni = "WBP_RadioNews_ChildPanel_Main_out"
    -- }
}

--子面板1 —— 本期热品
UITable[UIName2ID.ActivityRadioChildPanelV1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioChildPanelV1",
    BPKey = "WBP_RadioNews_ChildPanel_01",
    Anim = {
        FlowInAni = "WBP_RadioNews_ChildPanel_01_in",
        FlowOutAni = "WBP_RadioNews_ChildPanel_01_out"
    }
}

--子面板2 —— 调台获取
UITable[UIName2ID.ActivityRadioChildPanelV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioChildPanelV2",
    BPKey = "WBP_RadioNews_ChildPanel_02",
    Anim = {
        FlowInAni = "WBP_RadioNews_ChildPanel_02_in",
        FlowOutAni = "WBP_RadioNews_ChildPanel_02_out"
    }
}

--子面板3 —— 新闻纪录
UITable[UIName2ID.ActivityRadioChildPanelV3] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioChildPanelV3",
    BPKey = "WBP_RadioNews_ChildPanel_03",
}

--子控件 —— 任务item
UITable[UIName2ID.ActivityRadioItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioItem",
    BPKey = "WBP_RadioNews_Item",
}

--子控件1 —— 任务item
UITable[UIName2ID.ActivityRadioItemV1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioItemV1",
    BPKey = "WBP_RadioNews_Item_01",
}

--子控件2 —— 新闻纪录
UITable[UIName2ID.ActivityRadioItemV2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioItemV2",
    BPKey = "WBP_RadioNews_Item_02",
}

--子控件3 —— 道具槽位
UITable[UIName2ID.ActivityRadioItemV3] = {
    UILayer = EUILayer.Sub,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonWidgetModule.UI.ItemView.Components.IVComponentBase"),
    BPKey = "WBP_SlotCompLockedMask",
}

--子控件4 —— 进度数字标
UITable[UIName2ID.ActivityRadioItemV4] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioItemV4",
    BPKey = "WBP_RadioNews_Item_04",
}

--配件制造弹窗
UITable[UIName2ID.ActivityRadioPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioPopWindow",
    BPKey = "WBP_RadioNews_Pop",
    IsModal = true,
}

--本期热品弹窗
UITable[UIName2ID.ActivityRadioHotPopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RadioNews.ActivityRadioHotPopWindow",
    BPKey = "WBP_RadioNews_Pop_03",
    IsModal = true,
}

--------------魔塔局内--------------
--游戏主界面
UITable[UIName2ID.ActivityMagicTowerLevelLoading] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerLevelLoading",
    BPKey = "WBP_MorgenGame_LevelLoading",
}
UITable[UIName2ID.ActivityMagicTowerTextItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerTextItem",
    BPKey = "WBP_MorgenGame_TextItem",
}
--游戏主界面
UITable[UIName2ID.ActivityMagicTowerMainPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerMainPanel",
    BPKey = "WBP_MorgenGame_MainPanel",
    SubUIs = {
        UIName2ID.ActivityMagicTowerNumericalPanel,
        UIName2ID.ActivityMagicTowerKeyItem,
        UIName2ID.ActivityMagicTowerMove,
    },
    LinkSubStage = ESubStage.MagicTower,
    bEnableWorldRendering = true,
}

--大对话主界面
UITable[UIName2ID.ActivityMagicTowerDialoguePanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerDialoguePanel",
    BPKey = "WBP_MorgenGame_NpcDialoguePanel",
    SubUIs = {
        UIName2ID.ActivityMagicTowerDialogueBtn
    },
    Anim = {
        FlowInAni = "WBP_MorgenGame_NpcDialoguePanel_in",
        FlowOutAni = "WBP_MorgenGame_NpcDialoguePanel_out",
    },
    LinkSubStage = ESubStage.MagicTower,
}

--回答选项
UITable[UIName2ID.ActivityMagicTowerDialogueBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerDialogueBtn",
    BPKey = "WBP_MorgenGame_DialogueBtn",
    Anim = {
        FlowInAni = "WBP_MorgenGame_DialogueBtn_in",
        FlowOutAni = "WBP_MorgenGame_DialogueBtn_out",
    }
}

--主角信息框
UITable[UIName2ID.ActivityMagicTowerNumericalPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalPanel",
    BPKey = "WBP_MorgenGame_NumericalPanel",
    SubUIs = {
        UIName2ID.ActivityMagicTowerNumericalBox,
    },
    Anim = {
        FlowInAni = "WBP_MorgenGame_NumericalPanel_in",
        FlowOutAni = "WBP_MorgenGame_NumericalPanel_out",
    }
}

--人物数值框
UITable[UIName2ID.ActivityMagicTowerNumericalBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerNumericalBox",
    BPKey = "WBP_MorgenGame_NumericalBox",
}

--钥匙卡槽
UITable[UIName2ID.ActivityMagicTowerKeyItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerKeyItem",
    BPKey = "WBP_MorgenGame_KeyItem",
}

--商人界面
UITable[UIName2ID.ActivityMagicTowerMerchant] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerMerchant",
    BPKey = "WBP_MorgenGame_MerchantPop",
    SubUIs = {
        UIName2ID.ActivityMagicTowerMerchantItem
    },
    LinkSubStage = ESubStage.MagicTower
}

--商人卡片
UITable[UIName2ID.ActivityMagicTowerMerchantItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerMerchantItem",
    BPKey = "WBP_MorgenGame_MerchantItem",
    Anim = {
        FlowInAni = "WBP_MorgenGame_MerchantItem_in",
    }
}

--战斗加载界面
UITable[UIName2ID.ActivityMagicTowerCombatLoading] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerCombatLoading",
    BPKey = "WBP_MorgenGame_CombatLoading",
    -- Anim = {
    --     FlowInAni = "WBP_DoctorGames_CombatLoading_in",
    -- },
    LinkSubStage = ESubStage.MagicTower
}

--战斗界面
UITable[UIName2ID.ActivityMagicTowerCombatPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerCombatPanel",
    BPKey = "WBP_MorgenGame_CombatPanel",
    LinkSubStage = ESubStage.MagicTower
}

--确认弹窗
UITable[UIName2ID.ActivityMagicTowerConfirmWindow] = {
    UILayer = EUILayer.Pop,
    IsModal = true,
    LuaPath = string.format(ResourcePath.BUSINESS_MODULE, "CommonTipsModule.UI.ConfirmWindows"),
    BPKey = "WBP_ConfirmWindow",
    Anim = {
        FlowInAni = "Ani_in"
    },
    LinkSubStage = ESubStage.MagicTower
}

--撤离界面
UITable[UIName2ID.ActivityMagicTowerEvacuatePanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerEvacuatePanel",
    BPKey = "WBP_MorgenGame_EvacuatePanel",
    LinkSubStage = ESubStage.MagicTower,
    Anim = {
        -- FlowInAni = "WBP_DoctorGames_EvacuatePanel_in",
        FlowOutAni = "WBP_DoctorGames_EvacuatePanel_out",
    }
}

--角色头顶3DUI
UITable[UIName2ID.ActivityMagicTowerTextItem3D] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerTextItem3D",
    BPKey = "WBP_MorgenGame_TextItem_2",
}

UITable[UIName2ID.MTSafePanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafePanel",
    BPKey = "WBP_MorgenGame_SearchPanel",
    SubUIs = {
        UIName2ID.MTSafeItem
    },
    Anim = {
        FlowInAni = "WBP_DoctorGames_SearchPanel_in",
        FlowOutAni = "WBP_DoctorGames_SearchPanel_out",
    },
    LinkSubStage = ESubStage.MagicTower
}

UITable[UIName2ID.MTSafeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.Safe.MTSafeItem",
    BPKey = "WBP_MorgenGame_SafeItem",
    LinkSubStage = ESubStage.MagicTower
}


UITable[UIName2ID.ActivityMagicTowerMove] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerMove",
    BPKey = "WBP_MorgenGame_Move",
}


---------------巡游泰拉---------------
--引导界面
UITable[UIName2ID.ActivityTouringIntroPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringIntroPanel",
    BPKey = "WBP_TouringTerra_ChoiceBlogger",
    SubUIs = {
        UIName2ID.ActivityTouringIntroItem
    },
    IsModal = true,
}

--引导子项
UITable[UIName2ID.ActivityTouringIntroItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringIntroItem",
    BPKey = "WBP_TouringTerra_BloggersItem",
}

--主界面
UITable[UIName2ID.ActivityTouringMainPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringMainPanel",
    BPKey = "WBP_TouringTerra_MainPanel",
    SubUIs = {
        UIName2ID.ActivityTouringHeadShot,
    },
}

UITable[UIName2ID.ActivityRadioRuleDescription] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringRuleDescription",
    BPKey = "WBP_RadioNews_Pop_04",
    SubUIs = {
        UIName2ID.ActivityTouringRuleBox,
    },
    IsModal = true,
}

UITable[UIName2ID.ActivityRadioRuleBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringRuleBox",
    BPKey = "WBP_RadioNews_Item_05",
}

--规则弹窗
UITable[UIName2ID.ActivityTouringRuleDescription] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringRuleDescription",
    BPKey = "WBP_TouringTerra_RuleDescription",
    SubUIs = {
        UIName2ID.ActivityTouringRuleBox,
    },
    IsModal = true,
}

--规则图文
UITable[UIName2ID.ActivityTouringRuleBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringRuleBox",
    BPKey = "WBP_TouringTerra_RuleBox",
}

--头像item
UITable[UIName2ID.ActivityTouringHeadShot] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringHeadShot",
    BPKey = "WBP_TouringTerra_HeadShot",
}

--里程碑tab
UITable[UIName2ID.ActivityTouringMilestoneTab] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.TouringTerra.ActivityTouringMilestoneTab",
    BPKey = "WBP_TouringTerra_Tab",
}

-------------角色调查活动-------------
UITable[UIName2ID.ActivityRoleSurvey] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RoleSurvey.ActivityRoleSurvey",
    BPKey = "WBP_Activity_RoleSurvey",

    SubUIs = {
        UIName2ID.ActivityIntelligenceDetails
    }
}

UITable[UIName2ID.ActivityIntelligenceDetails] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RoleSurvey.ActivityIntelligenceDetails",
    BPKey = "WBP_Activity_IntelligenceDetails",
    
    SubUIs = {
        UIName2ID.ActivityCommonButtonV2S1
    }
}

UITable[UIName2ID.ActivityRoleSurveyExchange] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.RoleSurvey.ActivityRoleSurveyExchange",
    BPKey = "WBP_Activity_RoleSurveyExchange",
    IsModal = true,
}

-------------繁星活动-------------

UITable[UIName2ID.ActivityRocketLaunch] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Stars.ActivityRocketLaunch",
    BPKey = "WBP_Activity_RocketLaunch",
}

UITable[UIName2ID.ActivityStars] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Stars.ActivityStars",
    BPKey = "WBP_Activity_Stars",
}

-- //end

----------------------------------------调酒师---------------------------------------
UITable[UIName2ID.ActivityBartender] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartender",
    BPKey = "WBP_CocktailPanel",
    Anim = {
        FlowInAni = "WBP_CocktailPanel_0in",
        FlowOutAni = "WBP_CocktailPanel_0out",
        -- bManuelAnim = true,
    },
    SubUIs = {
        UIName2ID.ActivityBartenderExchangeItem,
        UIName2ID.ActivityBartenderDopA,
        UIName2ID.ActivityBartenderDopB,
        UIName2ID.IVItemSelectedComponent,
        UIName2ID.IVTextIconComponent,
        UIName2ID.IVGreyMask,
    },
}

UITable[UIName2ID.ActivityBartenderDopA] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderDopA",
    BPKey = "WBP_Cocktail_RewardBox",
}

UITable[UIName2ID.ActivityBartenderDopB] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderDopB",
    BPKey = "WBP_Cocktail_PrizeOverview_2",
}

UITable[UIName2ID.ActivityBartenderExchangeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderExchangeItem",
    BPKey = "WBP_Cocktail_ExchangeBox",
}

UITable[UIName2ID.ActivityBartenderItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderItem",
    BPKey = "WBP_Cocktail_WineItem",
}

UITable[UIName2ID.ActivityBartenderPicturePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderPicturePop",
    BPKey = "WBP_Cocktail_Illustrated",
    IsModal = true,
}

UITable[UIName2ID.ActivityBartenderRewardPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderRewardPop",
    BPKey = "WBP_Cocktail_PrizeOverview",
    SubUIs = {
        UIName2ID.ActivityBartenderRewardSubItem,
    },
    IsModal = true,
}

UITable[UIName2ID.ActivityBartenderRewardSubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderRewardSubItem",
    BPKey = "WBP_Cocktail_RewardBox_2",
}

UITable[UIName2ID.ActivityBartenderRulerPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderRulerPop",
    BPKey = "WBP_Cocktail_RuleDescription",
    SubUIs = {
        UIName2ID.ActivityBartenderRulerSubItem,
    },
    IsModal = true,
}

UITable[UIName2ID.ActivityBartenderRulerSubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Bartender.ActivityBartenderRulerSubItem",
    BPKey = "WBP_Cocktail_RuleBox",
}
------------------------------------------------------------------------------------------------

-------------------------------------新年集换卡--------------------------------------------------
---(She1临时不上活动)
-- UITable[UIName2ID.ActivityTackCards] = {
--     UILayer = EUILayer.Sub,
--     LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.View.ActivityTackCards",
--     BPKey = "WBP_Spring_Main_01",
--     Anim = {
--         FlowInAni = "WBP_Spring_Main_01_in",
--         FlowOutAni = "WBP_Spring_Main_01_out",
--     },
--     SubUIs = {
--         UIName2ID.ActivityCardHolder,
--         UIName2ID.ActivityJumpBtn,
--         UIName2ID.IVItemSelectedComponent,
--         UIName2ID.IVTextIconComponent,
--         UIName2ID.IVGreyMask,
--     },

-- }
-- --卡包获取
-- UITable[UIName2ID.ActivityObtainCards] = {
--     UILayer = EUILayer.Stack,
--     LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.View.ActivityObtainCards",
--     BPKey = "WBP_Spring_Main_02",
--     Anim = {
--         FlowInAni = "WBP_Spring_Main_02_in",
--         FlowOutAni = "WBP_Spring_Main_02_out",
--     },
-- }
-- --卡片收藏
-- UITable[UIName2ID.ActivityCollectCards] = {
--     UILayer = EUILayer.Stack,
--     LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.View.ActivityCollectCards",
--     BPKey = "WBP_Spring_Main_03",
--     Anim = {
--         FlowInAni = "WBP_Spring_Main_03_in",
--         FlowOutAni = "WBP_Spring_Main_03_out",
--     },
-- }
-- --奖励兑换
-- UITable[UIName2ID.ActivityExchangeCards] = {
--     UILayer = EUILayer.Stack,
--     LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.View.ActivityExchangeCards",
--     BPKey = "WBP_Spring_Main_04",
--     Anim = {
--         FlowInAni = "WBP_Spring_Main_04_in",
--         FlowOutAni = "WBP_Spring_Main_04_out",
--     },
-- }

--拆包
UITable[UIName2ID.ActivityUnpackingPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Pop.ActivityUnpackingPop",
    BPKey = "WBP_Spring_UnpackReward_Pop",
    Anim = {
        FlowInAni = "WBP_Spring_UnpackReward_Pop_in",
        FlowOutAni = "WBP_Spring_UnpackReward_Pop_out",
    },
    IsModal = true,
}
--合成
UITable[UIName2ID.ActivityCardSynthesisPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Pop.ActivityCardSynthesisPop",
    BPKey = "WBP_Spring_Synthetic_Pop",
    Anim = {
        FlowInAni = "WBP_Spring_Synthetic_Pop_in",
    },
    IsModal = true,
}
--好友赠送
UITable[UIName2ID.ActivityFriendGiftPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Pop.ActivityFriendGiftPop",
    BPKey = "WBP_Spring_FriendSent_Pop",
    IsModal = true,
}
--mp卡包获取纪录/赠送纪录/规则
UITable[UIName2ID.ActivityNewRecordPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Pop.ActivityNewRecordPop",
    BPKey = "WBP_Spring_HistoryLog_Pop",
    IsModal = true,
}
--赠送好友
UITable[UIName2ID.ActivityGiftFriendPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Pop.ActivityGiftFriendPop",
    BPKey = "WBP_Spring_SentGift_Pop",
    IsModal = true,
}

--卡包
UITable[UIName2ID.ActivityCardHolder] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityCardHolder",
    BPKey = "WBP_Spring_CardBag_Item",
}
--卡片
UITable[UIName2ID.ActivityCardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityCardItem",
    BPKey = "WBP_Spring_CardItem",
    Anim = {
        bManuelAnim = true,
    },
}
--好友卡片
UITable[UIName2ID.ActivityFriendCardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityFriendCardItem",
    BPKey = "WBP_Spring_GiftCard_Item",
}
--好友名称
UITable[UIName2ID.ActivityFriendNameItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityFriendNameItem",
    BPKey = "WBP_Spring_FriendEntry_Item",
}
--按钮
UITable[UIName2ID.ActivityJumpBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityJumpBtn",
    BPKey = "WBP_Event_SkipBtn",
}
--获取
UITable[UIName2ID.ActivityObtainItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityObtainItem",
    BPKey = "WBP_Spring_CardBag_Item_V2",
}

--赠送好友item
UITable[UIName2ID.ActivityGiftFriendItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityGiftFriendItem",
    BPKey = "WBP_Spring_FriendItem",
}
--兑换名片
UITable[UIName2ID.ActivityBusinessCardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityBusinessCardItem",
    BPKey = "WBP_Spring_HeroCardBox",
}
--兑换物品
UITable[UIName2ID.ActivityExchangetem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Cards.Common.ActivityExchangetem",
    BPKey = "WBP_Spring_ExchangeBox",
}
------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------
--#region 签到模板 2024-11 finnywxu

UITable[UIName2ID.ActivitySignInTemplatePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.SignInTemplate.ActivitySignInTemplatePanel",
    BPKey = "WBP_Activity_SignInTemplatePanel",
    SubUIs = {UIName2ID.ActivitySignInTemplateItem},
}

UITable[UIName2ID.ActivitySignInTemplateItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.SignInTemplate.ActivitySignInTemplateItem",
    BPKey = "WBP_Activity_SignInTemplateBox",
    Anim = {FlowInAni = "WBP_Activity_DailyActivityBox_1_In",},
    SubUIs = {UIName2ID.IVCommonItemTemplate, UIName2ID.ActivityCommonButtonV2S2},
}

--#endregion
------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------
--#region 万能兑换模板 2025-2-17

UITable[UIName2ID.ActivityUniversalExchangeTemplatePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityUniversalExchangeTemplatePanel",
    BPKey = "WBP_MagnumExchange",
    SubUIs = {
        UIName2ID.ActivityUniversalExchangeTemplateItem
    },
    Anim = {
        FlowInAni = "WBP_MagnumExchange_in",
        FlowOutAni = "WBP_MagnumExchange_out",
    },
}

UITable[UIName2ID.ActivityUniversalExchangeTemplateItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityUniversalExchangeTemplateItem",
    BPKey = "WBP_MagnumExchangeItem",
    SubUIs = {
        UIName2ID.ActivityUniversalExchangeTemplateChildItem
    },
}

UITable[UIName2ID.ActivityUniversalExchangeTemplateChildItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityUniversalExchangeTemplateChildItem",
    BPKey = "WBP_PropItem",
}

UITable[UIName2ID.ActivityUniversalExchangeTemplatePop1] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityUniversalExchangeTemplatePop1",
    BPKey = "WBP_TokenExchangePop",
    IsModal = true,
}

UITable[UIName2ID.ActivityUniversalExchangeTemplatePop2] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityUniversalExchangeTemplatePop2",
    BPKey = "WBP_PropExchangePop",
    SubUIs = {
        UIName2ID.ActivityUniversalExchangeSwitchItem
    },
    IsModal = true,
}

UITable[UIName2ID.ActivityTemplateReplacementMaterialPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityTemplateReplacementMaterialPop",
    BPKey = "WBP_ReplaceMaterial",
    SubUIs = {
        UIName2ID.ActivityUniversalExchangeSwitchItem
    },
    IsModal = true,
}

UITable[UIName2ID.ActivityUniversalExchangeSwitchItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ExchangeTemplate.ActivityUniversalExchangeSwitchItem",
    BPKey = "WBP_ReplaceMaterialItem",
}

--#endregion
------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------
--#region 简易里程碑模板 2025-3-3
UITable[UIName2ID.ActivitySimpleMilestonesPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Simple.ActivitySimpleMilestonesPanel",
    BPKey = "WBP_SimpleMilestone",
    SubUIs = {
        UIName2ID.ActivityMilestoneList,
    },
    Anim = {
        FlowInAni = "WBP_SimpleMilestone_in",
        FlowOutAni = "WBP_SimpleMilestone_out",
    },
}
--#endregion
------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------
--#region QQ微信订阅 2025-3-3
UITable[UIName2ID.ActivitySubscribePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Subscribe.ActivitySubscribePanel",
    BPKey = "WBP_OneDayCheckIn_Main",
    SubUIs = {
        UIName2ID.ActivitySubscribeItem,
    },
}
UITable[UIName2ID.ActivitySubscribeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Subscribe.ActivitySubscribeItem",
    BPKey = "WBP_OneDayCheckIn_Box",
}
--#endregion
------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------
--#region 方舟商城兑换活动(如果是体验服就不加载)
if not BUILD_REGION_CN_EXPER then
    UITable[UIName2ID.ActivityStoreSubPanel] = {
        UILayer = EUILayer.Sub,
        LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreSubPanel",
        BPKey = "WBP_ArknightsShop_HomePage",
        Anim = {
            FlowInAni = "WBP_ArknightsShop_HomePage_in",
            FlowOutAni = "WBP_ArknightsShop_HomePage_out",
        },
        SubUIs = {
            UIName2ID.ActivityStoreSubItem,
            UIName2ID.ActivityJumpBtn,
        },
    }
end

UITable[UIName2ID.ActivityStoreSubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreSubItem",
    BPKey = "WBP_ArknightsShop_Btn",
}

UITable[UIName2ID.ActivityStoreMain] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreMain",
    BPKey = "WBP_ArknightsShop_PrizePanel",
    Anim = {
        FlowInAni = "WBP_ArknightsShop_PrizePanel_in",
        FlowOutAni = "WBP_ArknightsShop_PrizePanel_out",
    },
    SubUIs = {
        UIName2ID.ActivityStoreItem1,
        UIName2ID.ActivityStoreItem2,
        UIName2ID.ActivityStoreItem3,
        UIName2ID.ActivityStoreItem4,
    },
}

UITable[UIName2ID.ActivityStoreItem1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreItem1",
    BPKey = "WBP_ArknightsShop_PrizeItem",
}

UITable[UIName2ID.ActivityStoreItem2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreItem2",
    BPKey = "WBP_ArknightsShop_PropItem",
}

UITable[UIName2ID.ActivityStoreItem3] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreItem3",
    BPKey = "WBP_ArknightsShop_HeadshotItem",
}

UITable[UIName2ID.ActivityStoreItem4] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Store.ActivityStoreItem4",
    BPKey = "WBP_ArknightsShop_CardItem",
}

--#endregion
------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------
--#region 通用商城兑换活动
UITable[UIName2ID.CommonStoreHome] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.CommonStoreHome",
    BPKey = "WBP_ExchangeShop_HomePage",
    SubUIs = {
        UIName2ID.CommonStoreBtn,
    },
}

UITable[UIName2ID.CommonStoreBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.CommonStoreBtn",
    BPKey = "WBP_ExchangeShop_Btn",
}

UITable[UIName2ID.CommonStoreStackPage] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.CommonStoreStackPage",
    BPKey = "WBP_ExchangeShop_PrizePanel",
    SubUIs = {
        UIName2ID.CommonStorePrizeItem,
        UIName2ID.CommonStorePropItem,
        UIName2ID.CommonStoreHeadItem,
        UIName2ID.CommonStoreCardItem,
    },
}

UITable[UIName2ID.CommonStorePrizeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.Item.CommonStorePrizeItem",
    BPKey = "WBP_ExchangeShop_PrizeItem",
}

UITable[UIName2ID.CommonStorePropItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.Item.CommonStorePropItem",
    BPKey = "WBP_ExchangeShop_PropItem",
}

UITable[UIName2ID.CommonStoreHeadItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.Item.CommonStoreHeadItem",
    BPKey = "WBP_ExchangeShop_HeadshotItem",
}

UITable[UIName2ID.CommonStoreCardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.CommonStore.Item.CommonStoreCardItem",
    BPKey = "WBP_ExchangeShop_CardIt",
}

UITable[UIName2ID.StoreFxYearOne] = {
    UILayer = EUILayer.Sub,
    BPKey = "WBP_VFX_1stAnniversaryShop",
}

--#endregion
------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------
--#region 明日方舟招募活动 2025-5-12 kirkxia

--- 主界面
UITable[UIName2ID.ArkRecruitMain] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.ArkRecruitMain",
    BPKey = "WBP_ArkRecruitment_Main",
    SubUIs = {
        UIName2ID.ArkRecruitSelectPanel,
        UIName2ID.ArkRecruitBook,
        UIName2ID.ArkRecruitTask,
    },
}

--- 玩法提示
UITable[UIName2ID.ArkRecruitRulePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.ArkRecruitRulePop",
    BPKey = "WBP_ArkRecruitment_Rule_Pop",
    IsModal = true,
}

--- 以下为两个蓝图，共用一个Lua
--- 招募卡片1
UITable[UIName2ID.ArkRecruitCard] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitCard",
    BPKey = "WBP_ArkRecruitment_Item_02",
}
--- 招募卡片2
UITable[UIName2ID.ArkRecruitCard2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitCard2",
    BPKey = "WBP_ArkRecruitment_Item_03",
}

--- 招募进度
UITable[UIName2ID.ArkRecruitProgress] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.ArkRecruitProgress",
    BPKey = "WBP_ArkRecruitment_Item_01",
}

--- 招募进度_小
UITable[UIName2ID.ArkRecruitState] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitState",
    BPKey = "WBP_ArkRecruitment_RecruitState",
}

--- 发布招募
UITable[UIName2ID.ArkRecruitRecruitPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitRecruitPop",
    BPKey = "WBP_ArkRecruitment_Recruit_Pop",
    IsModal = true,
}

--- 招募广告
UITable[UIName2ID.ArkRecruitRecruitAd] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitRecruitAd",
    BPKey = "WBP_ArkRecruitment_Item_04",
}

--- 选择干员界面
UITable[UIName2ID.ArkRecruitSelectPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitSelectPanel",
    BPKey = "WBP_ArkRecruitment_SelectCharacter",
}

--- 单个干员
UITable[UIName2ID.ArkRecruitSelectItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitSelectItem",
    BPKey = "WBP_ArkRecruitment_ApplyItem",
    bManuelAnim = true,
}

--- 获得干员界面
UITable[UIName2ID.ArkRecruitUnlockPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Recruit.ArkRecruitUnlockPop",
    BPKey = "WBP_ArkRecruitment_UnlockCharacter",
    IsModal = true,
}

--- 获取途径
UITable[UIName2ID.ArkRecruitTask] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Task.ArkRecruitTask",
    BPKey = "WBP_ArkRecruitment_Access",
}

--- 历史记录弹窗
UITable[UIName2ID.ArkRecruitHistoryPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Task.ArkRecruitHistoryPop",
    BPKey = "WBP_Store_AllPrizesPop",
    -- bManuelAnim = true,
    IsModal = true,
}

--- 历史记录条目
UITable[UIName2ID.ArkRecruitHistory] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Task.ArkRecruitHistory",
    BPKey = "WBP_Store_SweepHistory",
}

--- 图鉴总
UITable[UIName2ID.ArkRecruitBook] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Book.ArkRecruitBook",
    BPKey = "WBP_ArkRecruitment_Book",
}
--- 图鉴组
UITable[UIName2ID.ArkRecruitBookGroup] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Book.ArkRecruitBookGroup",
    BPKey = "WBP_ArkRecruitment_ItemIcon_List",
}
--- 图鉴个
UITable[UIName2ID.ArkRecruitBookItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Book.ArkRecruitBookItem",
    BPKey = "WBP_ArkRecruitment_ItemIcon",
}
--- 图鉴立绘
UITable[UIName2ID.ArkRecruitBookShow] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.ArkRecruit.Book.ArkRecruitBookShow",
    BPKey = "WBP_ArkRecruitment_SlideShow_Item",
}
--#endregion
------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------
--#region 博士的游戏-局外 2025-7-7 kirkxia

--- 主界面
UITable[UIName2ID.MorgenMain] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.MorgenMain",
    BPKey = "WBP_MorgenGame",
    SubUIs = {
    },
}

--- 功能Btn
UITable[UIName2ID.MorgenBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.MorgenBtn",
    BPKey = "WBP_MorgenGame_Btn",
}

--- 快速探索
UITable[UIName2ID.MorgenExploreQuickPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Explore.MorgenExploreQuickPop",
    BPKey = "WBP_MorgenGame_Explore",
    IsModal = true,
}
--- 探索状态
UITable[UIName2ID.MorgenExploreState] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Explore.MorgenExploreState",
    BPKey = "WBP_MorgenGame_Item6",
}

--- 游戏结算记录
UITable[UIName2ID.MorgenExploreLogPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Explore.MorgenExploreLogPop",
    BPKey = "WBP_MorgenGame_SettlementLogs",
    IsModal = true,
}
--- 结算Item
UITable[UIName2ID.MorgenExploreLogItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Explore.MorgenExploreLogItem",
    BPKey = "WBP_MorgenGame_Item2",
}


--- 奖励总览
UITable[UIName2ID.MorgenRewardPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenRewardPop",
    BPKey = "WBP_MorgenGame_RewardOverview",
    IsModal = true,
}
--- 奖励Item
UITable[UIName2ID.MorgenRewardItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenRewardItem",
    BPKey = "WBP_MorgenGame_Item5",
}
--- 任务Item
UITable[UIName2ID.MorgenTaskItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenTaskItem",
    BPKey = "WBP_MorgenGame_Item9",
}


--- 理智获取记录
UITable[UIName2ID.MorgenSanityPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenSanityPop",
    BPKey = "WBP_MorgenGame_AcquireLogs",
    IsModal = true,
}
--- 理智获取Item
UITable[UIName2ID.MorgenSanityItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenSanityItem",
    BPKey = "WBP_MorgenGame_Item7",
    bManuelAnim = true,
}

--- 规则弹窗
UITable[UIName2ID.MorgenRulePop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenRulePop",
    BPKey = "WBP_MorgenGame_RulesPop",
    IsModal = true,
}
--- 规则Item
UITable[UIName2ID.MorgenRuleItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Other.MorgenRuleItem",
    BPKey = "WBP_MorgenGame_RulesItem",
}
------------------------------------------------------------------------------------------------
--- 事件记录/图鉴
UITable[UIName2ID.MorgenEventLogPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Event.MorgenEventLogPop",
    BPKey = "WBP_MorgenGame_EventLogs",
    IsModal = true,
}

--- 图鉴事件Item
UITable[UIName2ID.MorgenEventItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Event.MorgenEventItem",
    BPKey = "WBP_MorgenGame_Item",
}

--- 图鉴事件段落Item
UITable[UIName2ID.MorgenEventTextItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Event.MorgenEventTextItem",
    BPKey = "WBP_MorgenGame_Item3",
}

--- 敌人Item
UITable[UIName2ID.MorgenEnemyItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.MorgenGame.Event.MorgenEnemyItem",
    BPKey = "WBP_MorgenGame_Item4",
}
--#endregion
------------------------------------------------------------------------------------------------

------------------------------------------------------------------------------------------------
--#region 阿萨拉巡旅 2025-7-16 ssy

--阿萨拉主页
UITable[UIName2ID.ActivityTaraMain] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraMain",
    BPKey = "WBP_PatrolAsala_MainPanel_Q1ZNK",
    SubUIs = {
        UIName2ID.ActivityTaraMainItem,
    },
    Anim = {
        FlowInAni = "WBP_PatrolAsala_MainPanel_Q1ZNK_in",
        FlowOutAni = "WBP_PatrolAsala_MainPanel_Q1ZNK_out",
    },
}

UITable[UIName2ID.ActivityTaraMainItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraMainItem",
    BPKey = "WBP_PatrolAsala_Item_Q1ZNK",
    SubUIs = {
        UIName2ID.ActivityTaraItem1,
        UIName2ID.ActivityTaraItem2,
        UIName2ID.ActivityTaraItem3,
    },
}

--头像
UITable[UIName2ID.ActivityTaraHead] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraHead",
    BPKey = "WBP_PatrolAsala_HeadShot_Q1ZNK",
}

--朋友圈
UITable[UIName2ID.ActivityTaraItem1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem1",
    BPKey = "WBP_PatrolAsala_ArticleItem_Q1ZNK",
}

--点赞
UITable[UIName2ID.ActivityTaraItem2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem2",
    BPKey = "WBP_PatrolAsala_LikeItem_Q1ZNK",
}

--评论
UITable[UIName2ID.ActivityTaraItem3] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem3",
    BPKey = "WBP_PatrolAsala_CommentItem_Q1ZNK",
}

--里程碑item
UITable[UIName2ID.ActivityTaraItem4] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem4",
    BPKey = "WBP_PatrolAsala_Tab_Q1ZNK",
}

--巡游泰拉
UITable[UIName2ID.ActivityTaraItem5] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem5",
    BPKey = "WBP_PatrolAsala_BloggersItem_Q1ZNK",
}

--切换
UITable[UIName2ID.ActivityTaraPanel1] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPanel1",
    BPKey = "WBP_PatrolAsala_SwitchBlogger_Q1ZNK",
}

--发送
UITable[UIName2ID.ActivityTaraPanel2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPanel2",
    BPKey = "WBP_PatrolAsala_InputField_Q1ZNK",
}

--选择
UITable[UIName2ID.ActivityTaraPop1] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPop1",
    BPKey = "WBP_PatrolAsala_ChoiceBlogger_Q1ZNK",
}

--#endregion
------------------------------------------------------------------------------------------------

local ActivityConfig = {
    -- -- 一级页签枚举
    -- EActivitySelect = {
    --     --普通
    --     OrdinaryActivity = 1,
    --     --跟踪
    --     TrackActivity = 2,
    --     --每日
    --     ChallengeActivitiy = 3,
    --     --签到
    --     CheckInTasks = 4,
    --     --新兵
    --     RecruitActivity = 5,
    --     --热点
    --     ActiveActivity = 6,
    -- },

    --------------------------------------------------------------------------
    -- 事件声明    
    evtActivityGeneralCommand = LuaEvent:NewIns("evtActivityGeneralCommand"),   -- 活动通用控制台指令
    evtMagicTowerSwitchLevel = LuaEvent:NewIns("evtMagicTowerSwitchLevel"),
    evtMagicTowerSetPlayer = LuaEvent:NewIns("evtMagicTowerSetPlayer"),
    evtProcessMagicTowerAstarPath = LuaEvent:NewIns("evtProcessMagicTowerAstarPath"),
    evtSwitchMagicTowerDebugInfo = LuaEvent:NewIns("evtSwitchMagicTowerDebugInfo"),
    evtMagicTowerOpenSafePanel = LuaEvent:NewIns("evtMagicTowerOpenSafePanel"),
    evtSwitchMagicTowerCreateItemMode = LuaEvent:NewIns("evtSwitchMagicTowerCreateItemMode"),
    evtMagicTowerLevelStateChanged = LuaEvent:NewIns("evtMagicTowerLevelStateChanged"),
    evtMagicTowerLevelGameOver = LuaEvent:NewIns("evtMagicTowerLevelGameOver"),
    evtMagicTowerResidueBossHealth = LuaEvent:NewIns("evtMagicTowerResidueBossHealth"),
    evtMagicTowerSafePanelLoadFinish = LuaEvent:NewIns("evtMagicTowerSafePanelLoadFinish"),
    evtMagicTowerMoveReq = LuaEvent:NewIns("evtMagicTowerMoveReq"),
    evtMagicTowerTileMapClickInfo = LuaEvent:NewIns("evtMagicTowerTileMapClickInfo"),
    evtMagicTowerMoveFinish = LuaEvent:NewIns("evtMagicTowerMoveFinish"),
    evtActivitySubBetweenInteractive = LuaEvent:NewIns("evtActivitySubBetweenInteractive"), -- 调酒师活动sub之间交互事件(可通用)
    evtActivityDecryptIsPlayUnLockAnim = LuaEvent:NewIns("evtActivityDecryptIsPlayUnLockAnim"), -- 解密活动是否播放解锁动效
    evtActivityOnClosePopUi = LuaEvent:NewIns("evtActivityOnClosePopUi"), -- 关闭popui事件
    evtAppraiseWeaponBtnClicked = LuaEvent:NewIns("evtAppraiseWeaponBtnClicked"),    
    evtActivityCollectionAfterReward = LuaEvent:NewIns("evtActivityCollectionAfterReward"), -- 收藏模版活动领奖成功之后触发，用于刷新整个UI
    evtActivityMainPanelForward = LuaEvent:NewIns("evtActivityMainPanelForward"),    
    evtActivityMainPanelBackward = LuaEvent:NewIns("evtActivityMainPanelBackward"), -- 主面板上下动画播放
    evtSubItemChanged = LuaEvent:NewIns("evtSubItemChanged"),
    evtAsaraCurIndexChanged = LuaEvent:NewIns("evtAsaraCurIndexChanged"),
    evtNoviceCurIndexChanged = LuaEvent:NewIns("evtNoviceCurIndexChanged"),  
    evtFrontPageClickChanged = LuaEvent:NewIns("evtFrontPageClickChanged"),
    evtSubItemScrollToIndex = LuaEvent:NewIns("evtSubItemScrollToIndex"),
    evtSubItemClickChanged = LuaEvent:NewIns("evtSubItemClickChanged"),
    evtStartBtnHover = LuaEvent:NewIns("evtStartBtnHover"), -- 二级页签边栏按钮悬停
    evtSubItemClicked = LuaEvent:NewIns("evtSubItemClicked"), -- 二级页签活动按钮点击
    evtSubItemUnHover= LuaEvent:NewIns("evtSubItemUnHover"), -- 二级页签面板悬停离开
    evtActivityBack = LuaEvent:NewIns("evtActivityBack"),
    evtBackBtnChanged = LuaEvent:NewIns("evtBackBtnChanged"), -- 恢复主面板返回绑定
    evtRedeemPopWindowShow = LuaEvent:NewIns("evtRedeemPopWindowShow"),
    evtRedeemPopWindowHide = LuaEvent:NewIns("evtRedeemPopWindowHide"),
    evtRadioPopWindowShow = LuaEvent:NewIns("evtRadioPopWindowShow"),
    evtRadioPopWindowHide = LuaEvent:NewIns("evtRadioPopWindowHide"),
    evtSignItemClick = LuaEvent:NewIns("evtSignItemClick"),
    evtHomePageCollapsed = LuaEvent:NewIns("evtHomePageCollapsed"),
    evtHomePageSubHover = LuaEvent:NewIns("evtHomePageSubHover"),
    evtHomePageSubUnHover = LuaEvent:NewIns("evtHomePageSubUnHover"),
    evtTrackingStateChanged = LuaEvent:NewIns("evtTrackingStateChanged"),
    evtTrackingBtnChanged = LuaEvent:NewIns("evtTrackingBtnChanged"),
    evtFullTrackingStateChanged = LuaEvent:NewIns("evtFullTrackingStateChanged"),
    evtExchangeChangeBoxStateChanged = LuaEvent:NewIns("evtExchangeChangeBoxStateChanged"),
    evtMilestonesStateChanged = LuaEvent:NewIns("evtMilestonesStateChanged"),
    evtRecruitsMilestonesStateChanged = LuaEvent:NewIns("evtRecruitsMilestonesStateChanged"),
    evtActivityTrackState = LuaEvent:NewIns("evtActivityTrackState"),
    evtActivityGrowthChallenge = LuaEvent:NewIns("evtActivityGrowthChallenge"),
    evtActivityJumpTab = LuaEvent:NewIns("evtActivityJumpTab"),
    evtFilingLocationSelected = LuaEvent:NewIns("evtFilingLocationSelected"),
    evtFilingTaskOpen = LuaEvent:NewIns("evtFilingTaskOpen"),
    evtFilingTaskClosed = LuaEvent:NewIns("evtFilingTaskClosed"),
    evtFilingTaskSwitchTask = LuaEvent:NewIns("evtFilingTaskSwitchTask"),
    evtFilingTaskSwitchSelected = LuaEvent:NewIns("evtFilingTaskSwitchSelected"),
    evtFilingArchiveBossSelected = LuaEvent:NewIns("evtFilingArchiveBossSelected"),
    evtFilingOnSequenceItemClick = LuaEvent:NewIns("evtFilingOnSequenceItemClick"),
    evtFilingOnFinalItemClick = LuaEvent:NewIns("evtFilingOnFinalItemClick"),
    evtFilingOnCancelInfoSelected = LuaEvent:NewIns("evtFilingOnCancelInfoSelected"),
    evtFilingOnArchivePanelSwitch = LuaEvent:NewIns("evtFilingOnArchivePanelSwitch"),
    evtNewGoForward  = LuaEvent:NewIns("evtNewGoForward"),
    evtNewGoBackward = LuaEvent:NewIns("evtNewGoBackward"),
    evtSubGeneral = LuaEvent:NewIns("evtSubGeneral"),
    evtOpenLotteryDrawPanel = LuaEvent:NewIns("evtOpenLotteryDrawPanel"),
    evtOnClickTaraItem = LuaEvent:NewIns("evtOnClickTaraItem"),
    evtOpenTaraPanel = LuaEvent:NewIns("evtOpenTaraPanel"),
    evtRefreshTaraPanel = LuaEvent:NewIns("evtRefreshTaraPanel"),
    evtAddTaraHandleAdaptation = LuaEvent:NewIns("evtAddTaraHandleAdaptation"),

    EPlayerState = {
        Matching = 1,
        Playing = 2,
        Offline = 3,
        Online = 4,
        Refreshing = 5,
    },
    
    EActivityTabBtnPath = {
        [6] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_05.Activity_Icon_05'",--首页
        [5] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_09.Activity_Icon_09'",--新兵
        [8] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_26.Activity_Icon_26'",--赛季
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_06.Activity_Icon_06'",--常规
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_08.Activity_Icon_08'",--跟踪
        [4] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_24.Activity_Icon_24'",--签到
        [9] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_07.Activity_Icon_07'",--外链(待定)
        [3] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_10.Activity_Icon_10'",--每日
        [7] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_25.Activity_Icon_25'",--赛季物流
    },
    EActivityImagePath = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_02.Activity_Btn_02'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_01.Activity_Btn_01'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_01.Activity_Icon_01'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_03.Activity_Icon_03'",
        [5] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_02.Activity_Icon_02'",
        [6] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_04.Activity_Icon_04'"
    },
    EActivitySelectPath = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_De_03.Activity_De_03'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_De_04.Activity_De_04'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_02.Activity_Icon_02'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_De_05.Activity_De_05'"
    },
    EReciveimgPath = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_05.Activity_Btn_05'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_06.Activity_Btn_06'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_07.Activity_Btn_07'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_08.Activity_Btn_08'"
    },
    ETrackimgPath = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_De_21.Activity_De_21'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_De_22.Activity_De_22'"
    },
    EFullScreenimgPath = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_03.Activity_Btn_03'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Btn_04.Activity_Btn_04'"
    },
    BRewardimgPath = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Bg_10.Activity_Bg_10'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Bg_11.Activity_Bg_11'"
    },
    ETabItemimgPath ={
        [1] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Bg_20.Activity_Bg_20'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_White.Activity_White'"
    },
    ERadioImgPath = {
        [1] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/RadioNews/Texture/RadioNews_Sp_De_015.RadioNews_Sp_De_015'",
        [2] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/RadioNews/Texture/RadioNews_Sp_De_016.RadioNews_Sp_De_016'",
        [3] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/RadioNews/Texture/RadioNews_Sp_De_017.RadioNews_Sp_De_017'",
    },
    EMagicTowerImgPath = {
        [1] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Atlas/BakedSprite/MorgenGame_Icon_04.MorgenGame_Icon_04'",
        [2] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Assets/Items_256_384/MG_Prop_KeyCard_Size2.MG_Prop_KeyCard_Size2'"
    },

    DailyMissionRewardsimgPath = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_De_10.Activity_De_10'",
    MossHardwareUpIconPath = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Set.Common_De_Set'",--硬件图标路径
    LogisticsConfigurationIconPath = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_02.Activity_Icon_02'",--物流凭证图标路径
    IconPropsCollectPath = "PaperSprite'/Game/UI/UIIcon/ItemIcon/2_1/Icon_props_collect_m100d_1066.Icon_props_collect_m100d_1066'",
    BGMediaRowName = "ActivityBgMedia", -- 背景视频配置(具体信息配置在MediaResTable中)
    BrainIconPath = "PaperSprite'/Game/UI/UIIcon/ItemIcon/2_2/Icon_props_collect_relink_1125.Icon_props_collect_relink_1125'",--脑机图标
    --抽奖凭证iconpath
    VoucherIconPathList = {
        [35010000001] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_02.Activity_Icon_02",
        [32270000001] = "PaperSprite'/Game/UI/UIIcon/ItemIcon/0_0/LogisticsVoucher_Red.LogisticsVoucher_Red",
    },
    --moss背景cdn地址
    MossBgCdnUrl = "Resource/Texture/Activity/C=60224285B2BC0771E5788A02DF5E4BB0F5E4CE3FAEEFB5010784F7A7F7B328599711668FA865990DD92CA5DDBF5D94AD.png",
    DrinkBgCdnUrl = "Resource/Texture/Activity/C=9F203173F55D0AA384CC1A35DE47EE19DF61D3231F4E554DD5EEEF98CBA15FC658A15AFA9AF9E80512C4271E393F9E97.jpg",
    --调酒师规则
    DrinkIconList = {
        [1] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/Cocktail/Texture/Sp/Cocktail_Sp_07.Cocktail_Sp_07'",
        [2] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/Cocktail/Texture/Sp/Cocktail_Sp_06.Cocktail_Sp_06'",
        [3] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/Cocktail/Texture/Sp/Cocktail_Sp_05.Cocktail_Sp_05'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0113.Common_Tips_Icon_0113'",--图鉴
        [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Task_Icon_0101.Common_Task_Icon_0101'",--追踪
        [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemClass_Icon_0050.Common_ItemClass_Icon_0050'",--消息
    },
    --道具品质图标
    QualityIconMapping = {
        [0] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
        [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0402.Common_ItemProp_Icon_0402'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0403.Common_ItemProp_Icon_0403'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0404.Common_ItemProp_Icon_0404'",
        [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0405.Common_ItemProp_Icon_0405'",
        [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0406.Common_ItemProp_Icon_0406'",
    },
    --新年集换卡图标
    NewYeasCards = {
        [1]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_Icon_01.SpringFestival_Sp_Icon_01'",--sol卡包
        [2]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_Icon_02.SpringFestival_Sp_Icon_02'",--mp卡包
        [3]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Atlas/BakedSprite/SpringFestival_Icon_0001.SpringFestival_Icon_0001'",--卡包获取图标
        [4]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Atlas/BakedSprite/SpringFestival_Icon_0002.SpringFestival_Icon_0002'",--卡片收藏图标
        [5]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Atlas/BakedSprite/SpringFestival_Icon_0003.SpringFestival_Icon_0003'",--奖励兑换图标
        [6]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_02.SpringFestival_Sp_02'",--武器背景
        [7]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_01.SpringFestival_Sp_01'",--名片背景
        [8]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_05.SpringFestival_Sp_05'",--道具背景
        [9]  = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_04.SpringFestival_Sp_04'",--获得背景
        [10] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_05.SpringFestival_Sp_05'",--集卡背景
        [11] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0007.Common_Card_0007'",--干员图标
        [12] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0004.Common_Card_0004'",--野怪图标
        [13] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0118.Common_Tips_Icon_0118'",--载具图标
        [14] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_03.SpringFestival_Sp_03'",--临时枪皮
        [15] = "C=60224285B2BC0771E5788A02DF5E4BB0F5E4CE3FAEEFB5010784F7A7F7B328599711668FA865990DD92CA5DDBF5D94AD.png",--地图(长弓溪谷)
        [16] = "C=9F203173F55D0AA384CC1A35DE47EE19DF61D3231F4E554DD5EEEF98CBA15FC658A15AFA9AF9E80512C4271E393F9E97.jpg",--地图(零号大坝)
        [17] = "C=9F203173F55D0AA384CC1A35DE47EE19DF61D3231F4E554DD5EEEF98CBA15FC658A15AFA9AF9E80512C4271E393F9E97.jpg",--地图
        [18] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/SpringFestival/Texture/SpringFestival_Sp_De_07.SpringFestival_Sp_De_07'",--剪刀
    },
    --静态图片资源路径
    IconPath = {
        --方舟兑换商城活动手游按钮图标
        [1] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Store_Commercial/AN252G/Atlas/BakedSprite/Crossover_Store_Arknights.Crossover_Store_Arknights'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Merchant_Icon_0106.CommonHall_Merchant_Icon_0106'",
        [3] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_07.CommonHall_Hero_Icon_07'",
        [4] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Store_Commercial/AN252G/Atlas/BakedSprite/Crossover_De_10.Crossover_De_10'",
        --方舟兑换商城背景
        [5] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/ArknightsShop/Texture/ArknightsShop_SP_02.ArknightsShop_SP_02'",
        [6] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/ArknightsShop/Texture/ArknightsShop_SP_02.ArknightsShop_SP_02'",
        [7] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/ArknightsShop/Texture/ArknightsShop_SP_02.ArknightsShop_SP_02'",
        [8] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/ArknightsShop/Texture/ArknightsShop_SP_01.ArknightsShop_SP_01'",
        --跳转图标
        [9] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Task_Icon_0101.CommonHall_Task_Icon_0101'",
        --阿萨拉巡旅规则图标
        [10] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/PatrolAsala_Q1ZNK/Texture/PatrolAsala_SP_13_Q1ZNK.PatrolAsala_SP_13_Q1ZNK'",
        [11] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/PatrolAsala_Q1ZNK/Texture/PatrolAsala_SP_14_Q1ZNK.PatrolAsala_SP_14_Q1ZNK'",
        [12] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/PatrolAsala_Q1ZNK/Texture/PatrolAsala_SP_15_Q1ZNK.PatrolAsala_SP_15_Q1ZNK'",
        --物流定轨
        [13] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Money_De_01.Common_Money_De_01'",
        [14] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_De_Hook.Common_De_Hook'",
        --限时唯一
        [15] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_01.Activity_Icon_01'",--唯一
        [16] = "PaperSprite'/Game/UI/UIAtlas/System/Activity/BakedSprite/Activity_Icon_27.Activity_Icon_27'",--限时
    },
    --阿萨拉电台图标
    RadioNewsIcon = {
        [1] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/RadioNews/Atlas/BakedSprite/RadioNews_Icon_003.RadioNews_Icon_003",
        [2] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/RadioNews/Atlas/BakedSprite/RadioNews_Icon_001.RadioNews_Icon_001",
        [3] = "PaperSprite'/Game/BluePrints/UI/UMG/System/Event/RadioNews/Atlas/BakedSprite/RadioNews_Icon_002.RadioNews_Icon_002",
    },
    --明日方舟精英等级图标
    ArkLevelIcon = {
        [1] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/ArknightsRecruitment/Texture/Level/ArknightsRecruitment_Sp_Level_001.ArknightsRecruitment_Sp_Level_001'",
        [2] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/ArknightsRecruitment/Texture/Level/ArknightsRecruitment_Sp_Level_002.ArknightsRecruitment_Sp_Level_002'",
    },
    
    --魔塔局外Icon
    MogenPopBtnIcon = {
        [1] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_HistoryRecord.CommonHall_HistoryRecord'",
        [2] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_TopBar_Icon_0003.CommonHall_TopBar_Icon_0003'",
        [3] = "Texture2D'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Texture/MorgenGame_Sp_53.MorgenGame_Sp_53'",
        [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Task_Icon_0101.Common_Task_Icon_0101'",
    },
    
    MorgenLoadingStub = ResStub:NewIns("MorgenLoadingStub"),
    --- 为子控件准备的图片列表
    MorgenSpineList =
    {
        SpineData1 = "SpineAtlasAsset'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Spine/Spine_UI_Jem/Spine_UI_Jem.Spine_UI_Jem-atlas'",
        SpineData2 = "SpineSkeletonDataAsset'/Game/BluePrints/UI/UMG/System/Event/MorgenGame/Spine/Spine_UI_Jem/Spine_UI_Jem.Spine_UI_Jem-data'",
    },

    --// 无须翻译的测试文本
    testLoc = {
        unableToGetCurrencyRichTextID = "无法获取货币富文本ID",
        unableToGetAccessoriesInSaleList = "交易行无在售配件信息",
        activityNotCompleted = "该活动尚未竣工",
        MagicTowerSwitchState = "魔塔周目变更成功！"
    },
    --// end
    
    --本地化文本
    Loc = {
        -----本地化拼接<俄罗斯套娃>：StringUtil.Key2StrFormat(Montage, {data = ""})
        Montage  = NSLOCTEXT("ActivityModule", "Lua_Activity_Montage", "{data1}{data2}"),--拼接
        Red      = NSLOCTEXT("ActivityModule", "Lua_Activity_Red"    , "<customstyle color=\"Color_DarkNegative\">{data}</>"),--红色
        Yellow   = NSLOCTEXT("ActivityModule", "Lua_Activity_Yellow" , "<customstyle color=\"Color_Highlight01\">{data}</>" ),--黄色
        Green    = NSLOCTEXT("ActivityModule", "Lua_Activity_Green"  , "<customstyle color=\"Color_Highlight02\">{data}</>" ),--绿色
        Grey     = NSLOCTEXT("ActivityModule", "Lua_Activity_Grey"   , "<customstyle color=\"Color_Quality00\">{data}</>"   ),--灰色(待确定颜色)
        Slash    = NSLOCTEXT("ActivityModule", "Lua_Activity_Slash"  , "/"),--斜杠
        Burden   = NSLOCTEXT("ActivityModule", "Lua_Activity_Burden" , "-"),--减号
        Colon    = NSLOCTEXT("ActivityModule", "Lua_Activity_Colon"  , ":"),--冒号
        Comma    = NSLOCTEXT("ActivityModule", "Lua_Activity_Comma"  , ","),--逗号
        Plus     = NSLOCTEXT("ActivityModule", "Lua_Activity_Plus"   , "+"),--加号
        Hundred  = NSLOCTEXT("ActivityModule", "Lua_Activity_Hundred", "%"),--百分号
        Bracket1 = NSLOCTEXT("ActivityModule", "Lua_Activity_Bracket1", "("),--括号(
        Bracket2 = NSLOCTEXT("ActivityModule", "Lua_Activity_Bracket2", ")"),--括号)
        CurrencyStr = NSLOCTEXT("ActivityModule", "Lua_Activity_CurrencyStr", "<dfmrichtext type=\"img\" width=\"50\" height=\"50\" id=\"{img}\" align=\"0\"/> {num}"),--货币
        -----end
        NoWay = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText_No_Way", "向目标地格移动受阻，换条路吧！"),
        ChooseBlogger = NSLOCTEXT("ActivityModule", "Lua_Activity_ChooseBlogger", "请选择同行博主，选择后仍可随时切换博主"),
        CompletedByPeers = NSLOCTEXT("ActivityModule", "Lua_Activity_CompletedByPeers", "游记<customstyle color=\"Color_DarkNegative\">已完成</>，请选择下一位同行博主，选择后仍可随时切换博主"),
        Asala = NSLOCTEXT("ActivityModule", "Lua_Activity_Asala", "阿萨拉"),
        ScoreContent = NSLOCTEXT("ActivityModule", "Lua_Activity_ScoreContent", "·  {txt}<customstyle color=\"Color_Highlight02\">{num}</>"),
        ScoreLimit = NSLOCTEXT("ActivityModule", "Lua_Activity_ScoreLimit", "得分上限：{data}"),
        AllBloggers = NSLOCTEXT("ActivityModule", "Lua_Activity_AllBloggers", "·  所有博主<customstyle color=\"Color_Highlight02\">共享同一上限</>，超过上限之后无法继续得分"),
        TheUpperLimitIs = NSLOCTEXT("ActivityModule", "Lua_Activity_TheUpperLimitIs", "·  该上限在<customstyle color=\"Color_Highlight02\">每日4:00清零</>"),
        TodaysUpperLimit = NSLOCTEXT("ActivityModule", "Lua_Activity_TodaysUpperLimit", "今日：{min}/{max}"),
        RewardTips = NSLOCTEXT("ActivityModule", "Lua_Activity_RewardTips", "发出邮寄后至少获得以下奖励"),
        AllBlogsToRewards = NSLOCTEXT("ActivityModule", "Lua_Activity_AllBlogsToRewards", "({min}<customstyle color=\"Color_Quality00\">/{max}</>)解锁所有博客获得奖励"),
        ThisWillInterrupt = NSLOCTEXT("ActivityModule", "Lua_Activity_ThisWillInterrupt", "这将中断当前与<customstyle color=\"Color_Highlight01\">{name}</>同行，是否确认？"),

        ActUITextBlock = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock1", "选择博主"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock2", "推进进度"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock3", "最终奖励"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock4", "选择一位同行的博主，一起展开阿萨拉之旅"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock5", "通过局内行为推进进度，解锁博主朋友圈"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock6", "解锁博主的所有朋友圈即可获得最终名片奖励"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock7", "同行中"),
            [8] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock8", "已完成"),
            [9] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock9", "选择"),
            [10] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock10", "已拥有"),
            [11] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActUITextBlock11", "同行"),
        },

        ActRewardTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_ActRewardTitle", "签收指定"),
        ActRewardTipys = NSLOCTEXT("ActivityModule", "Lua_Activity_ActRewardTipys", "在签收物流奖励时，可以从核心奖励池中指定1个未获取的核心奖励。当签收获得核心奖励时，必定为所指定的核心奖励。\n在指定核心奖励后，可以随时更换目标，已签收次数不会重置，保底将会继承。"),
        ActRewardDescs = NSLOCTEXT("ActivityModule", "Lua_Activity_ActRewardDescs", "签收指定后核心奖励必定出现该外观"),
        SupplementAwards = NSLOCTEXT("ActivityModule", "Lua_Activity_supplementAwards", "每赛季持续补充奖励"),
        ActRewardAddTwo = NSLOCTEXT("ActivityModule", "Lua_Activity_ActRewardAddTwo", "奖励 +{num}"),
        SharePicture = NSLOCTEXT("ActivityModule", "Lua_Activity_SharePicture", "分享图片"),
        YellowAvailable = NSLOCTEXT("ActivityModule", "Lua_Activity_YellowAvailable", "可领取"),

        --万能兑换模板
        AllCoreAwards = NSLOCTEXT("ActivityModule", "Lua_Activity_AllCoreAwards", "已拥有所有核心奖励"),
        AnalyticDesignation = NSLOCTEXT("ActivityModule", "Lua_Activity_AnalyticDesignation", "解析指定"),
        Unlock = NSLOCTEXT("ActivityModule", "Lua_Activity_Unlock", "解锁"),
        After = NSLOCTEXT("ActivityModule", "Lua_Activity_After", "后"),
        UnlockAfter = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockAfter", "{name}{time}后解锁"),
        MoreRewards = NSLOCTEXT("ActivityModule", "Lua_Activity_MoreRewards", "更多奖励"),
        GrandPrize = NSLOCTEXT("ActivityModule", "Lua_Activity_GrandPrize", "大奖"),
        ProfilePicture = NSLOCTEXT("ActivityModule", "Lua_Activity_ProfilePicture", "头像"),
        Insufficient = NSLOCTEXT("ActivityModule", "Lua_Activity_Insufficient", "{current}不足"),
        ToBeUnlocked = NSLOCTEXT("ActivityModule", "Lua_Activity_ToBeUnlocked", "待解锁"),
        SentViaEmail = NSLOCTEXT("ActivityModule", "Lua_Activity_SentViaEmail", "仓库已满，已通过邮件下发。"),
        SubscribeSuccess  = NSLOCTEXT("ActivityModule", "Lua_Activity_SubscribeSuccess", "订阅成功"),
        Combinations  = NSLOCTEXT("ActivityModule", "Lua_Activity_Combinations", "可选择以下组合"),
        Use  = NSLOCTEXT("ActivityModule", "Lua_Activity_Use", "使用"),
        Subscribe  = NSLOCTEXT("ActivityModule", "Lua_Activity_Subscribe", "订阅"),
        WeChat  = NSLOCTEXT("ActivityModule", "Lua_Activity_WeChat", "微信"),
        QQ  = NSLOCTEXT("ActivityModule", "Lua_Activity_QQ", "QQ"),
        Game  = NSLOCTEXT("ActivityModule", "Lua_Activity_Game", "游戏"),
        Available  = NSLOCTEXT("ActivityModule", "Lua_Activity_Available", "可得"),
        NextDate  = NSLOCTEXT("ActivityModule", "Lua_Activity_NextDate", "下次刷新时间"),
        Obtain = NSLOCTEXT("ActivityModule", "Lua_Activity_Obtain", "获取{icon}"),
        ScoringBehavior = NSLOCTEXT("ActivityModule", "Lua_Activity_ScoringBehavior", "得分行为"),
        Today = NSLOCTEXT("ActivityModule", "Lua_Activity_Today", "今日"),
        Material = NSLOCTEXT("ActivityModule", "Lua_Activity_Material", "材料"),
        InsufficientPleaseCollect = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientPleaseCollect", "{current}不足，请收集"),
        CancelCollection = NSLOCTEXT("ActivityModule", "Lua_Activity_CancelCollection", "取消收藏"),
        Collected = NSLOCTEXT("ActivityModule", "Lua_Activity_Collected", "已收藏，材料充足将有红点提示"),
        ToBeOpened = NSLOCTEXT("ActivityModule", "Lua_Activity_ToBeOpened", "待开放"),
        LaterOpen = NSLOCTEXT("ActivityModule", "Lua_Activity_LaterOpen", "<customstyle color=\"Color_Highlight01\">{time}</>后开放"),
        LaterOpenTime = NSLOCTEXT("ActivityModule", "Lua_Activity_LaterOpenTime", "{time}后开放"),
        PostUpdateListing = NSLOCTEXT("ActivityModule", "Lua_Activity_PostUpdateListing", "<customstyle color=\"Color_Highlight01\">{time}</>后更新上架"),
        ExchangeNum = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeNum", "兑换 {num}"),
        ExchangeColonNum = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeColonNum", "兑换: {num}"),
        --新年集换卡
        All = NSLOCTEXT("ActivityModule", "Lua_Activity_All", "全部"),
        OperatorCard = NSLOCTEXT("ActivityModule", "Lua_Activity_OperatorCard", "干员卡"),
        WildMonsterCard = NSLOCTEXT("ActivityModule", "Lua_Activity_WildMonsterCard", "敌兵卡"),
        VehicleCard = NSLOCTEXT("ActivityModule", "Lua_Activity_VehicleCard", "载具卡"),
        Gift = NSLOCTEXT("ActivityModule", "Lua_Activity_Gift", "赠送"),
        Compound = NSLOCTEXT("ActivityModule", "Lua_Activity_Compound", "合成"),
        RandomlySelectAStaffCard = NSLOCTEXT("ActivityModule", "Lua_Activity_RandomlySelectAStaffCard", "随机一张干员卡"),
        CardBagDropsName1 = NSLOCTEXT("ActivityModule", "Lua_Activity_CardBagDropsName1", "烽火地带卡包"),
        CardBagDropsName2 = NSLOCTEXT("ActivityModule", "Lua_Activity_CardBagDropsName2", "全面战场卡包：下一局获取概率%s%%"),
        CardBagDropsDesc1 = NSLOCTEXT("ActivityModule", "Lua_Activity_CardBagDropsDesc1", "进行任何烽火地带行动都有一定概率发现烽火地带卡包"),
        CardBagDropsDesc2 = NSLOCTEXT("ActivityModule", "Lua_Activity_CardBagDropsDesc2", "每完成一局全面战场，都有概率获得全面战场卡包"),
        GetRecords = NSLOCTEXT("ActivityModule", "Lua_Activity_GetRecords", "获取纪录"),
        ScissorsObtained = NSLOCTEXT("ActivityModule", "Lua_Activity_ScissorsObtained", "剪刀获得"),
        CardPackageAcquisition = NSLOCTEXT("ActivityModule", "Lua_Activity_CardPackageAcquisition", "卡包获取"),
        CardCollection = NSLOCTEXT("ActivityModule", "Lua_Activity_CardCollection", "卡片收藏"),
        TodayGiftLimitHasBeenReached = NSLOCTEXT("ActivityModule", "Lua_Activity_TodayGiftLimitHasBeenReached", "今日赠送已达上限，请明日再试"),
        PleaseEnterYourFriendNickname = NSLOCTEXT("ActivityModule", "Lua_Activity_PleaseEnterYourFriendNickname", "只能赠送给游戏好友，请输入好友昵称"),
        TodayRemainingGiftCount = NSLOCTEXT("ActivityModule", "Lua_Activity_TodayRemainingGiftCount", "今日剩余赠送次数：%s"),
        InsufficientCards = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientCards", "卡片不足"),
        Unpacking = NSLOCTEXT("ActivityModule", "Lua_Activity_Unpacking", "拆包"),
        ConsumingOperatorCards = NSLOCTEXT("ActivityModule", "Lua_Activity_ConsumingOperatorCards", "(<customstyle color=\"Color_Highlight01\">%s</>/%s) 消耗所有干员卡各一张"),
        ViewCollection = NSLOCTEXT("ActivityModule", "Lua_Activity_ViewCollection", "查看收集"),
        TheLimitHasBeenReached = NSLOCTEXT("ActivityModule", "Lua_Activity_TheLimitHasBeenReached", "已达限制"),
        WaitingForRefresh = NSLOCTEXT("ActivityModule", "Lua_Activity_WaitingForRefresh", "等待刷新"),
        PleaseWaitForRefresh = NSLOCTEXT("ActivityModule", "Lua_Activity_PleaseWaitForRefresh", "请等待刷新"),
        AlreadyRedeemed = NSLOCTEXT("ActivityModule", "Lua_Activity_AlreadyRedeemed", "已兑换"),
        PleaseCollect = NSLOCTEXT("ActivityModule", "Lua_Activity_PleaseCollect", "请收集"),
        NewYearCollectionCardExchange = NSLOCTEXT("ActivityModule", "Lua_Activity_NewYearCollectionCardExchange", "新春集换卡"),
        NumberOfDetachableCards = NSLOCTEXT("ActivityModule", "Lua_Activity_NumberOfDetachableCards", "可拆包次数：%s"),
        CongratulationsOnObtaining = NSLOCTEXT("ActivityModule", "Lua_Activity_CongratulationsOnObtaining", "恭喜获得"),
        ProbabilityExplanation = NSLOCTEXT("ActivityModule", "Lua_Activity_ProbabilityExplanation", "概率说明"),
        Message = NSLOCTEXT("ActivityModule", "Lua_Activity_Message", "赠卡纪录"),
        DisassembleAgain = NSLOCTEXT("ActivityModule", "Lua_Activity_DisassembleAgain", "再拆一次"),
        GetMPCardBagPass = NSLOCTEXT("ActivityModule", "Lua_Activity_GetCardBagPass", "全面战场结算，以%s%%的获取概率判断%s，获一张%s，概率恢复默认"),
        GetMPCardBagFail = NSLOCTEXT("ActivityModule", "Lua_Activity_GetCardBagFail", "全面战场结算，以%s%%的获取概率判断%s，下次获取概率%s"),
        Pass = NSLOCTEXT("ActivityModule", "Lua_Activity_Pass", "通过"),
        Fail = NSLOCTEXT("ActivityModule", "Lua_Activity_Fail", "未通过"),
        ComprehensiveBattlefieldCardPackage = NSLOCTEXT("ActivityModule", "Lua_Activity_ComprehensiveBattlefieldCardPackage", "<customstyle color=\"Color_Highlight01\">全面战场卡包</>"),
        Rise10 = NSLOCTEXT("ActivityModule", "Lua_Activity_Rise10", "上升%s%%"),
        FriendGift = NSLOCTEXT("ActivityModule", "Lua_Activity_FriendGift", "【%s】赠送你一张%s"),
        GiftFriend = NSLOCTEXT("ActivityModule", "Lua_Activity_GiftFriend", "你赠送【%s】一张%s"),
        HeroCard = NSLOCTEXT("ActivityModule", "Lua_Activity_HeroCard", "<customstyle color=\"Color_Highlight01\">%s</>"),
        MonsterCard = NSLOCTEXT("ActivityModule", "Lua_Activity_MonsterCard", "<customstyle color=\"C002\">%s</>"),
        AFriendGiftedItToYou = NSLOCTEXT("ActivityModule", "Lua_Activity_AFriendGiftedItToYou", "好友赠送给你<customstyle color=\"Color_Highlight01\">%s</>张卡"),
        BusinessCard = NSLOCTEXT("ActivityModule", "Lua_Activity_BusinessCard", "名片"),
        GoodsAndMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_GoodsAndMaterials", "物资"),
        NoNewsAtTheMoment = NSLOCTEXT("ActivityModule", "Lua_Activity_NoNewsAtTheMoment", "暂无纪录"),
        ColorPercentage = NSLOCTEXT("ActivityModule", "Lua_Activity_ColorPercentage", "<customstyle color=\"C001\">%s</><customstyle color=\"C002\">/%s</>"),
        NewYearCardRuler = NSLOCTEXT("ActivityModule", "Lua_Activity_NewYearCardRuler", "活动期间，每完成一局全面战场游戏，都有概率获取一张全面战场卡包。获取的默认概率为80%，如果某次没有获取，则下次获取的概率会上升5%，直到100%获取为止；如果某次获取了，则下次获取的概率恢复默认概率。"),
        GiftSuccessful = NSLOCTEXT("ActivityModule", "Lua_Activity_GiftSuccessful", "赠送成功"),
        PleaseChooseABaseLiquor = NSLOCTEXT("ActivityModule", "Lua_Activity_PleaseChooseABaseLiquor", "请选择一份基底"),
        PleaseSelectAuxiliaryMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_PleaseSelectAuxiliaryMaterials", "请选择辅助材料"),
        DailyScissorsTask = NSLOCTEXT("ActivityModule", "Lua_Activity_DailyScissorsTask", "每日剪刀任务"),
        ComprehensiveBattlefieldCardPacks = NSLOCTEXT("ActivityModule", "Lua_Activity_ComprehensiveBattlefieldCardPacks", "根据既往对局，您新获得了%s包全面战场卡包"),
        InsufficientCardPack = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientCardPack", "卡包不足"),
        InsufficientScissors = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientScissors", "剪刀不足"),
        --调酒师
        BartenderRulerName1 = NSLOCTEXT("ActivityModule", "Lua_Activity_BartenderRulerName1", "收集原料"),
        BartenderRulerName2 = NSLOCTEXT("ActivityModule", "Lua_Activity_BartenderRulerName2", "搭配调制"),
        BartenderRulerName3 = NSLOCTEXT("ActivityModule", "Lua_Activity_BartenderRulerName3", "局内畅饮"),
        BartenderRulerDesc1 = NSLOCTEXT("ActivityModule", "Lua_Activity_BartenderRulerDesc1", "通过每日任务或商店获得特调原料"),
        BartenderRulerDesc2 = NSLOCTEXT("ActivityModule", "Lua_Activity_BartenderRulerDesc2", "尝试组合原料，产出高品质特调"),
        BartenderRulerDesc3 = NSLOCTEXT("ActivityModule", "Lua_Activity_BartenderRulerDesc3", "特调带入局内畅饮，获得奇妙效果"),
        WhenSelectingTheSameMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_WhenSelectingTheSameMaterials", "针对已解锁配方，在选择同样的材料时，出现对应的特调"),
        RawMaterialAcquisition = NSLOCTEXT("ActivityModule", "Lua_Activity_RawMaterialAcquisition", "原料获取"),
        Allocate = NSLOCTEXT("ActivityModule", "Lua_Activity_Allocate", "调配"),
        IllustratedHandbook = NSLOCTEXT("ActivityModule", "Lua_Activity_IllustratedHandbook", "图鉴"),
        RewardOverview = NSLOCTEXT("ActivityModule", "Lua_Activity_RewardOverview", "奖励总览"),
        TaskAcquisition = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskAcquisition", "任务获取"),
        ExchangeToObtain = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeToObtain", "原料兑换"),
        ModulationOfFinishedProducts = NSLOCTEXT("ActivityModule", "Lua_Activity_ModulationOfFinishedProducts", "调制成品"),
        ModulationMaterial = NSLOCTEXT("ActivityModule", "Lua_Activity_ModulationMaterial", "调制材料"),
        SelectDrink = NSLOCTEXT("ActivityModule", "Lua_Activity_SelectDrink", "请选择基底"),
        SelectMaterial = NSLOCTEXT("ActivityModule", "Lua_Activity_SelectMaterial", "请选择1-2种材料"),
        SelectedBaseLiquor = NSLOCTEXT("ActivityModule", "Lua_Activity_SelectedBaseLiquor", "已选基底"),
        FlavoredBeverages = NSLOCTEXT("ActivityModule", "Lua_Activity_FlavoredBeverages", "风味饮料"),
        AuxiliaryMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_AuxiliaryMaterials", "辅料"),
        UnknownFormula = NSLOCTEXT("ActivityModule", "Lua_Activity_UnknownFormula", "未知配方"),
        NextStep = NSLOCTEXT("ActivityModule", "Lua_Activity_NextStep", "下一步"),
        Modulate = NSLOCTEXT("ActivityModule", "Lua_Activity_Modulate", "调制"),
        Convertible = NSLOCTEXT("ActivityModule", "Lua_Activity_Convertible", "可兑换:%s"),
        PressAndShake = NSLOCTEXT("ActivityModule", "Lua_Activity_PressAndShake", "按住摇晃，充分融合"),
        LongPressAndShake = NSLOCTEXT("ActivityModule", "Lua_Activity_LongPressAndShake", "长按摇晃"),
        EndShaking = NSLOCTEXT("ActivityModule", "Lua_Activity_EndShaking", "结束摇晃"),
        CollectXWineToObtain = NSLOCTEXT("ActivityModule", "Lua_Activity_CollectXWineToObtain", "收集%s种图鉴内特调可得（%s/%s）"),
        SuccessfulModulation = NSLOCTEXT("ActivityModule", "Lua_Activity_SuccessfulModulation", "调制成功后解锁信息"),
        -------------------------------------
        InsufficientMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientMaterials", "材料不足"),

        LogisticsVouchersName = NSLOCTEXT("ActivityModule", "Lua_Activity_LogisticsVouchersName", "获取物流凭证"),
        CooperateVouchersName = NSLOCTEXT("ActivityModule", "Lua_Activity_CooperateVouchersName", "获取合作凭证"),
        LogisticsTime = NSLOCTEXT("ActivityModule", "Lua_Activity_LogisticsTime", "赛季物流凭证<customstyle color=\"Color_Highlight01\">%s天</>后过期，参加活动可获得更多物流凭证"),
        CooperateTime = NSLOCTEXT("ActivityModule", "Lua_Activity_CooperateTime", "参加烽火地带合作行动可以获得合作物流凭证，用以在合作物流活动中执行签收"),
        JumpText = NSLOCTEXT("ActivityModule", "Lua_Activity_JumpZhanYesakaName", "参与烽火地带-<customstyle color=\"Color_Highlight01\">%s</>获得"),
        CooperateActionName = NSLOCTEXT("ActivityModule", "Lua_Activity_CooperateActionName", "合作行动"),

        UnLock = NSLOCTEXT("ActivityModule", "Lua_Activity_UnLock", "解锁材料"),--脑机解密
        BuyTips = NSLOCTEXT("ActivityModule", "Lua_Activity_BuyTips", "前往阿萨拉频道生产仿品也能起到同样效果，确认仍然高价购买哈夫克正品脑机吗？"),--脑机解密
        MaterialLock = NSLOCTEXT("ActivityModule", "Lua_Activity_MaterialLock", "材料未解锁"),
        Synthesis = NSLOCTEXT("ActivityModule", "Lua_Activity_Synthesis", "仿制"),
        Quota = NSLOCTEXT("ActivityModule", "Lua_Activity_Quota", "今日限购%s个"),
        SoldOut = NSLOCTEXT("ActivityModule", "Lua_Activity_SoldOut", "今天已售完"),
        Purchased = NSLOCTEXT("ActivityModule", "Lua_Activity_Purchased", "每日限购%s个，当前已购买%s"),
        RestrictedSynthesis = NSLOCTEXT("ActivityModule", "Lua_Activity_RestrictedSynthesis", "每日限仿制次数：%s"),
        SynthesisUsedUp = NSLOCTEXT("ActivityModule", "Lua_Activity_SynthesisUsedUp", "今日仿制次数已用完。"),
        View = NSLOCTEXT("ActivityModule", "Lua_Activity_View", "查看"),
        UnlockMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockMaterials", "解锁材料 * %s"),
        One = NSLOCTEXT("ActivityModule", "Lua_Activity_One", "一"),
        Two = NSLOCTEXT("ActivityModule", "Lua_Activity_Two", "二"),
        Three = NSLOCTEXT("ActivityModule", "Lua_Activity_Three", "三"),
        Four = NSLOCTEXT("ActivityModule", "Lua_Activity_Four", "四"),
        HafkChannel = NSLOCTEXT("ActivityModule", "Lua_Activity_HafkChannel", "哈夫克频道"),
        AsalaChannel = NSLOCTEXT("ActivityModule", "Lua_Activity_AsalaChannel", "阿萨拉频道"),
        GTIChannel = NSLOCTEXT("ActivityModule", "Lua_Activity_GTIChannel", "GTI频道"),
        ImplantationFrequency = NSLOCTEXT("ActivityModule", "Lua_Activity_ImplantationFrequency", "局内使用次数"),
        HafkCoinNum = NSLOCTEXT("ActivityModule", "Lua_Activity_HafkCoinNum", "<dfmrichtext type=\"img\" align=\"0\" id=\"%s\"/>%s"),
        BrainDecryption = NSLOCTEXT("ActivityModule", "Lua_Activity_BrainDecryption", "脑机解密"),
        PurchaseSuccessful = NSLOCTEXT("ActivityModule", "Lua_Activity_PurchaseSuccessful", "购买成功"),
        RedColorPercentage = NSLOCTEXT("ActivityModule", "Lua_Activity_RedColorPercentage", "<customstyle color=\"Color_DarkNegative\">%s</>/%s"),

        HafkSale = NSLOCTEXT("ActivityModule", "Lua_Activity_HafkSale", "哈夫克公司销售"),
        HafkSale1 = NSLOCTEXT("ActivityModule", "Lua_Activity_HafkSale1", "哈夫克集团高价招募脑机人体实验者，测试越多，奖励越多！"),
        HafkSale2 = NSLOCTEXT("ActivityModule", "Lua_Activity_HafkSale2", "哈夫克尖端科技精华，贵，但有道理！"),
        HafkSale3 = NSLOCTEXT("ActivityModule", "Lua_Activity_HafkSale3", "阿萨拉本土的仿制脑机侵害了哈夫克公司专利权，不管多贵也请支持正版！"),
        AsalaSale = NSLOCTEXT("ActivityModule", "Lua_Activity_AsalaSale", "阿萨拉长老"),
        AsalaSale1 = NSLOCTEXT("ActivityModule", "Lua_Activity_AsalaSale1", "这脑机没什么神秘的，只要知道了配方，我们也能仿制，效果一模一样！"),
        AsalaSale2 = NSLOCTEXT("ActivityModule", "Lua_Activity_AsalaSale2", "如此低成本的东西竟然因为哈夫克专利权就卖出这等高价，天理难容啊..."),
        AsalaSale3 = NSLOCTEXT("ActivityModule", "Lua_Activity_AsalaSale3", "那我们的仿制脑机参加测试也能骗来哈夫克奖品，他们根本就分不出来！"),

        TimesLimitedReturn = NSLOCTEXT("ActivityModule", "Lua_Activity_TimesLimitedReturn", "{time1} - {time2}返场"),
        CorePrize = NSLOCTEXT("ActivityModule", "Lua_Activity_CorePrize", "核心奖励"),--活动抽奖
        OtherPrize = NSLOCTEXT("ActivityModule", "Lua_Activity_OtherPrize", "其他奖励"),
        GetReward = NSLOCTEXT("ActivityModule", "Lua_Activity_GetReward", "获得物品"),
        UnlockEmpty = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockEmpty", "当前没有签收内容"),
        TypeTaskIslock = NSLOCTEXT("ActivityModule", "Lua_Activity_TypeTaskIslock", "需要完成前置任务才能解锁"),
        Islock = NSLOCTEXT("ActivityModule", "Lua_Activity_Islock", "未解锁"),
        MandelTabItemOwnedTip = NSLOCTEXT("ActivityModule", "Lua_Activity_MandelTabItemOwnedTip", "此标记物品无法重复获取"),
        MandelTabItemCouldRepeatTip = NSLOCTEXT("ActivityModule", "Lua_Activity_MandelTabItemCouldRepeatTip", "所有物品都可重复获得"),
        CorePrizeCurrentProb = NSLOCTEXT("ActivityModule", "Lua_Activity_CorePrizeCurrentProb", "核心奖励当前概率"),
        OtherPrizeCurrentProb = NSLOCTEXT("ActivityModule", "Lua_Activity_OtherPrizeCurrentProb", "其他奖励当前概率"),
        ActivityReward = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityReward", "奖励"),
        ActivityScanHistory = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityScanHistory", "签收历史"),
        ActivitySignFor = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivitySignFor", "签收%s次"),
        ActivityCoreRewards = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityCoreRewards", "核心奖励已获得"),
        ActivityNotCoreRewards = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityNotCoreRewards", "暂时还无法浏览奖励"),
        NoRelatedActivitiesAvailable = NSLOCTEXT("ActivityModule", "Lua_Activity_NoRelatedActivitiesAvailable", "当前暂无相关活动"),

        ActivityRemainingLotteryDraws = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityRemainingLotteryDraws", "再签收<customstyle color=\"Color_Highlight01\"> %s </>次必然获得一个<customstyle color=\"Color_Highlight01\">核心奖励</>"),
        ActivityParticipateInObtaining = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityParticipateInObtaining", "参与<customstyle color=\"Color_Highlight01\">%s</>获取"),
        ActivityLotteryReminder = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityLotteryReminder", "此类物品不会重复获得"),
        ActivityCoreLogisticsMaterials = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityCoreLogisticsMaterials", "核心物流物资"),

        LogisticsInsufficient = NSLOCTEXT("ActivityModule", "Lua_Activity_LogisticsInsufficient", "物流凭证不足"),
        CooperateInsufficient = NSLOCTEXT("ActivityModule", "Lua_Activity_CooperateInsufficient", "合作凭证不足"),

        ActivityInstalled = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityInstalled", "已安装:<customstyle color=\"Color_Highlight02\">%s</>"),--活动moss
        ActivityEfficiency = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityEfficiency", "[+%s]"),
        ActivityInstallMaxNum = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityInstallMaxNum", "最多可安装%s个"),
        ActivityHardwareUnderTipe = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityHardwareUnderTipe", "硬件数量不足 请查看详情来源获取更多硬件"),
        ActivityUnloadInst = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityUnloadInst", "需要卸下初级硬件 之后再安装高级硬件"),
        ActivityHardwareEfficiency = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityHardwareEfficiency", "硬件算力"),
        ActivityAlgorithmEfficiency = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityAlgorithmEfficiency", "算法算力"),
        ActivityHighestInHistory = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityHighestInHistory", "总算力"),
        ActivityAdvancedHardware = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityAdvancedHardware", "高级硬件"),
        ActivityBeginnerLevelHardware = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityBeginnerLevelHardware", "初级硬件"),
        ActivityAnEfficiency = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityAnEfficiency", "+%s"),

        InsertChip = NSLOCTEXT("ActivityModule", "Lua_Activity_InsertChip", "插入芯片：%s/%s"),
        AnalyzeMandelBricks = NSLOCTEXT("ActivityModule", "Lua_Activity_AnalyzeMandelBricks", "分析曼德尔砖：%s"),

        CurEndTask = NSLOCTEXT("ActivityModule", "Lua_Activity_CurEndTask", "当前是最后一波任务"),
        HistoryMaxDesc = NSLOCTEXT("ActivityModule", "Lua_Activity_HistoryMaxDesc", "历史最高算力"),

        ActivityJump = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityJump", "跳转"),--活动H5
        ActivitytrackAndComplete = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivitytrackAndComplete", "追踪并完成后可获得如下奖励"),
        ActivityComplete = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityComplete", "完成活动后可获得如下奖励"),
        ActivityTaskTips = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityTaskTips", "该活动可自行选择用那把武器完成任务"),--名枪品鉴
        CopySuccessTips = NSLOCTEXT("ActivityModule", "Lua_Activity_CopySuccessTips", "复制成功"),
        NullState = NSLOCTEXT("ActivityModule", "Lua_Activity_NullState", "空"),
        TopBarTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_TopBarTitle", "活动"),

        ActiveTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_ActiveTitle", "活动首页"),
        RecruitTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_RecruitTitle", "新兵福利"),
        CompetitionSeason = NSLOCTEXT("ActivityModule", "Lua_Activity_CompetitionSeason", "重磅推荐"),
        OrdinaryTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_OrdinaryTitle", "常规活动"),
        TrackTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_TrackTitle", "追踪"),
        CheckInTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_CheckInTitle", "签到"),
        OutsideTheChain = NSLOCTEXT("ActivityModule", "Lua_Activity_OutsideTheChain", "其他"),
        DailyTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_DailyTitle", "周期福利"),
        SeasonalLogistics = NSLOCTEXT("ActivityModule", "Lua_Activity_SeasonalLogistics", "战备物流"),

        --// temp
        -- ActiveTitle = "活动首页",
        -- RecruitTitle = "新兵福利",
        -- CompetitionSeason = "重磅推荐",
        -- OrdinaryTitle = "常规活动",
        -- TrackTitle = "追踪",
        -- CheckInTitle = "签到",
        -- OutsideTheChain = "其他",
        -- DailyTitle = "周期福利",
        -- SeasonalLogistics = "战备物流",
        --// end

        CancelTrack = NSLOCTEXT("ActivityModule", "Lua_Activity_CancelTrack", "取消追踪"),
        DailySubTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_DailySubTitle", "每日活动"),
        DailyChallenge = NSLOCTEXT("ActivityModule", "Lua_Activity_DailyChallenge", "日常挑战"),
        ExchangeTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeTitle", "兑换"),

        ExchangeSuccess = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeSuccess", "兑换成功"),
        TaskTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskTitle", "任务"),
        CurrencyNum = NSLOCTEXT("ActivityModule", "Lua_Activity_CurrencyNum", "x%d"),
        CurrencyMaxNum = NSLOCTEXT("ActivityModule", "Lua_Activity_CurrencyMaxNum", "X%s"),
        Jump = NSLOCTEXT("ActivityModule", "Lua_Activity_Jump", "跳转系统尚未开发完成，请手动跳转"),
        ChallengeTask = NSLOCTEXT("ActivityModule", "Lua_Activity_ChallengeTask", "挑战任务"),
        RewardExchange = NSLOCTEXT("ActivityModule", "Lua_Activity_RewardExchange", "奖励兑换"),
        ActivityFinishedByReward = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityFinishedByReward", "奖励全部领取后结束"),
        ActionLevelReached = NSLOCTEXT("ActivityModule", "Lua_Activity_ActionLevelReached", "行动等级达到%d级"),
        ChallengeTaskDesc = NSLOCTEXT("ActivityModule", "Lua_Activity_ChallengeTaskDesc", "通过完成任务获取阿萨拉金币"),
        RewardExchangeDesc = NSLOCTEXT("ActivityModule", "Lua_Activity_RewardExchangeDesc", "使用阿萨拉金币兑换丰厚奖励"),
        EnableMaxRewardTitle = NSLOCTEXT("ActivityModule", "Lua_Activity_EnableMaxRewardTitle", "全部完成可领取"),
        RewardAll = NSLOCTEXT("ActivityModule", "Lua_Activity_RewardAll", "领取全部"),
        CurrentCumulative = NSLOCTEXT("ActivityModule", "Lua_Activity_CurrentCumulative", "当前累计"),
        CurrentOpeningTimes = NSLOCTEXT("ActivityModule", "Lua_Activity_CurrentOpeningTimes", "当前开启次数"),
        DistanceFromNextRefresh = NSLOCTEXT("ActivityModule", "Lua_Activity_DistanceFromNextRefresh", "距离下次刷新"),
        CouldObtainPassword = NSLOCTEXT("ActivityModule", "Lua_Activity_CouldObtainPassword", "花费芯片可获得保险箱密码"),
        GoToWareHouseToCheck = NSLOCTEXT("ActivityModule", "Lua_Activity_GoToWareHouseToCheck", "前往仓库查看"),
        GetMoreChip = NSLOCTEXT("ActivityModule", "Lua_Activity_GetMoreChip", "获得更多芯片"),
        CannotExchangeChip = NSLOCTEXT("ActivityModule", "Lua_Activity_CannotExchangeChip", "芯片数量不足，无法兑换"),
        NeedChooseOneNumber = NSLOCTEXT("ActivityModule", "Lua_Activity_NeedChooseOneNumber", "请选择一位密码"),
        MapInformation = NSLOCTEXT("ActivityModule", "Lua_Activity_MapInformation", "地图情报"),
        SignalTransmissionStation = NSLOCTEXT("ActivityModule", "Lua_Activity_SignalTransmissionStation", "%s信号发射站"),
        PasswordPositionIndicated = NSLOCTEXT("ActivityModule", "Lua_Activity_PasswordPositionIndicated", "%s的位置已经标明"),
        ChipmapPositionIndicated = NSLOCTEXT("ActivityModule", "Lua_Activity_ChipmapPositionIndicated", "已知的诺翁芯片生产基地已经被标明，每个生产基地都需要消耗1个秘钥才能进入，请确保携带最够的秘钥入局"),
        ClickedQuickly = NSLOCTEXT("ActivityModule", "Lua_Activity_ClickedQuickly", "点击过于频繁！"),
        PasswordKnown = NSLOCTEXT("ActivityModule", "Lua_Activity_PasswordKnown", "已获知"),
        Matching = NSLOCTEXT("ActivityModule", "Lua_Activity_Matching", "匹配中"),
        Playing = NSLOCTEXT("ActivityModule", "Lua_Activity_Playing", "游戏中"),
        Offline = NSLOCTEXT("ActivityModule", "Lua_Activity_Offline", "离线"),
        OtherBehaviors = NSLOCTEXT("ActivityModule", "Lua_Activity_OtherBehaviors", "其它行为中"),
        GoToMatch = NSLOCTEXT("ActivityModule", "Lua_Activity_GoToMatch", "去匹配"),
        GoPlayToo = NSLOCTEXT("ActivityModule", "Lua_Activity_GoPlayToo", "我也去玩"),
        ToFinish = NSLOCTEXT("ActivityModule", "Lua_Activity_ToFinish", "去完成"),
        Receive = NSLOCTEXT("ActivityModule", "Lua_Activity_Receive", "领取"),
        ReceiveReward = NSLOCTEXT("ActivityModule", "Lua_Activity_ReceiveReward", "领取奖励"),
        Completed = NSLOCTEXT("ActivityModule", "Lua_Activity_Completed", "已领取"),
        AlreadyCompleted = NSLOCTEXT("ActivityModule", "Lua_Activity_AlreadyCompleted", "已完成"),
        LimitNum = NSLOCTEXT("ActivityModule", "Lua_Activity_LimitNum", "兑换限额%d/%d"),
        SurplusExchangeNum = NSLOCTEXT("ActivityModule", "Lua_Activity_SurplusExchangeNum", "剩余兑换次数：{SlotNum}"),
        SurplusRefreshNum = NSLOCTEXT("ActivityModule", "Lua_Activity_SurplusRefreshNum", "剩余刷新次数：%d"),
        NotSurplusRefreshNum = NSLOCTEXT("ActivityModule", "Lua_Activity_NotSurplusRefreshNum", "剩余刷新次数：<customstyle color=\"Color_DarkNegative\">0</>"),
        RichText = NSLOCTEXT("ActivityModule", "Lua_Activity_RichText", "<dfmrichtext type=\"img\" id=\"%s\"/>×%d"),
        TipsDefault = NSLOCTEXT("ActivityModule", "Lua_Activity_TipsDefault", "当前活动还未开发"),
        TipsMaxTip = NSLOCTEXT("ActivityModule", "Lua_Activity_TipsMaxTip", "当前最大追踪任务个数为3"),
        NotLimitRedemption = NSLOCTEXT("ActivityModule", "Lua_Activity_NotLimitRedemption", "不限制"),
        InTrack = NSLOCTEXT("ActivityModule", "Lua_Activity_InTrack", "追踪中"),
        Trace = NSLOCTEXT("ActivityModule", "Lua_Activity_Trace", "已追踪"),
        DistanceToStart = NSLOCTEXT("ActivityModule", "Lua_Activity_DistanceToStart", "距离开启"),
        NotTracked = NSLOCTEXT("ActivityModule", "Lua_Activity_NotTracked", "未追踪"),
        TraceTask = NSLOCTEXT("ActivityModule", "Lua_Activity_TraceTask", "当前已追踪%d/%d"),
        MitConfirm = NSLOCTEXT("ActivityModule", "Lua_Activity_MitConfirm", "确认提交%s吗?"),
        RemindTip = NSLOCTEXT("ActivityModule", "Lua_Activity_RemindTip", "兑换提醒"),
        TaskTime = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskTime", "活动时间:%s %02d:%02d - %s %02d:%02d"),
        ExchangeRefreshTime = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeRefreshTime", "%d小时%d分后开放兑换"),
        ExchangeRefreshTimeDay = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeRefreshTimeDay", "%d天后开放兑换"),
        TaskRefreshTime = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTime", "%02d:%02d:%02d"),
        TaskRefreshTimeDay = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTimeDay", "%d天%d小时"),
        TaskRefreshTimeHour = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTimeHour", "%d小时%d分钟"),
        TaskRefreshTimeMinute = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTimeMinute", "%d分钟%d秒"),
        TaskRefreshTimeSecond = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTimeSecond", "%d秒"),
        TaskRefreshTimeHourOnly = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTimeHourOnly", "%d小时"),
        TaskRefreshTimeLessOneHour = NSLOCTEXT("ActivityModule", "Lua_Activity_TaskRefreshTimeLessOneHour", "<1小时"),
        ActivityRefreshTimeDay = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityRefreshTimeDay", "%d天%d小时开始活动"),
        ActivityRefreshTimeHour = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityRefreshTimeHour", "%d小时%d分钟开始活动"),
        ActivityRefreshTimeMinute = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityRefreshTimeMinute", "%d分钟%d秒开始活动"),
        ActivityRefreshTimeSecond = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityRefreshTimeSecond", "%d秒开始活动"),
        DayStr = NSLOCTEXT("ActivityModule", "Lua_Activity_Day", "第%s天"),
        Rewarded = NSLOCTEXT("ActivityModule", "Lua_Activity_Rewarded", "已达上限"),
        Day8AwardTip = NSLOCTEXT("ActivityModule", "Lua_Activity_Day8Award", "最后一日奖励"),
        TipAwardGet = NSLOCTEXT("ActivityModule", "Lua_Activity_TipAwardGet", "你已获取该奖励"),
        TipSingNotAvaiable = NSLOCTEXT("ActivityModule", "Lua_Activity_TipSingNotAvaiable", "还没有到签到开放时间哦"),
        SignItemDayTip = NSLOCTEXT("ActivityModule", "Lua_Activity_SignItemDayTip", "{NumOfDay} day"),
        SignItem8DayReward = NSLOCTEXT("ActivityModule", "Lua_Activity_SignItem8DayReward", "八天奖励"),
        day1 = NSLOCTEXT("ActivityModule", "Lua_Activity_day1", "第一天"),
        day2 = NSLOCTEXT("ActivityModule", "Lua_Activity_day2", "第二天"),
        day3 = NSLOCTEXT("ActivityModule", "Lua_Activity_day3", "第三天"),
        day4 = NSLOCTEXT("ActivityModule", "Lua_Activity_day4", "第四天"),
        day5 = NSLOCTEXT("ActivityModule", "Lua_Activity_day5", "第五天"),
        day6 = NSLOCTEXT("ActivityModule", "Lua_Activity_day6", "第六天"),
        day7 = NSLOCTEXT("ActivityModule", "Lua_Activity_day7", "第七天"),
        TipMpToSol = NSLOCTEXT("ActivityModule", "Lua_Activity_TipMpToSol", "当前处在全面战场模式中，此任务要求前往烽火地带模式，是否确认"),
        TipSolToMp = NSLOCTEXT("ActivityModule", "Lua_Activity_TipSolToMp", "当前处在烽火地带模式中，此任务要求前往全面战场模式，是否确认"),
        MPTip = NSLOCTEXT("ActivityModule", "Lua_Activity_MPTip", "全面战场"),
        SolTip = NSLOCTEXT("ActivityModule", "Lua_Activity_SolTip", "烽火地带"),
        AllModelTip = NSLOCTEXT("ActivityModule", "Lua_Activity_AllModelTip", "全模式"),
        Cancel = NSLOCTEXT("ActivityModule", "Lua_Activity_Cancel", "取消"),
        Confirm = NSLOCTEXT("ActivityModule", "Lua_Activity_Confirm", "确认"),
        Remove = NSLOCTEXT("ActivityModule", "Lua_Activity_Remove", "移除"),
        JumpToPanel = NSLOCTEXT("ActivityModule", "Lua_Activity_JumpToPanel", "去完成"),
        JumpError = NSLOCTEXT("ActivityModule", "Lua_Activity_JumpError", "跳转页签不存在，请手动跳转"),
        JumpParamErrpr = NSLOCTEXT("ActivityModule", "Lua_Activity_JumpParamErrpr", "跳转请求参数非法"),
        Notjump = NSLOCTEXT("ActivityModule", "Lua_Activity_Notjump", "此任务不适用自动跳转，请按照指示加油完成任务吧！"),
        Staytuned = NSLOCTEXT("ActivityModule", "Lua_Activity_Staytuned", "敬请期待"),
        Notjump1 = NSLOCTEXT("ActivityModule", "Lua_Activity_Notjump1", "该活动即将开始，敬请期待！"),
        PatternTendencyFriendly = NSLOCTEXT("ActivityModule", "Lua_Activity_PatternTendencyFriendly", "模式倾向:全模式友好"),
        HeavilyDependentSOL = NSLOCTEXT("ActivityModule", "Lua_Activity_HeavilyDependentSOL", "模式倾向：烽火地带独占"),
        MildDependenceSOL = NSLOCTEXT("ActivityModule", "Lua_Activity_MildDependenceSOL", "模式倾向：更适合烽火地带"),
        MildDependenceMP = NSLOCTEXT("ActivityModule", "Lua_Activity_MildDependenceMP", "模式倾向：更适合全面战场模式"),
        HeavilyDependentMP = NSLOCTEXT("ActivityModule", "Lua_Activity_HeavilyDependentMP", "模式倾向：全面战场独占"),
        TeamsToComplete = NSLOCTEXT("ActivityModule", "Lua_Activity_TeamsToComplete", "该任务要求组队完成，请注意您的组队状况哦"),
        TipSol = NSLOCTEXT("ActivityModule", "Lua_Activity_TipSol", "当前处在烽火地带模式中"),
        TipMp = NSLOCTEXT("ActivityModule", "Lua_Activity_TipMp", "当前处在全面战场模式中"),
        ButtonStateError = NSLOCTEXT("ActivityModule", "Lua_Activity_ButtonStateError", "按钮状态错误"),
        CancelTrackedTaskTrackCurrenttask = NSLOCTEXT("ActivityModule","Lua_Activity_CancelTrackedTaskTrackCurrenttask","同时只能追踪一个活动，当前已追踪<customstyle color=\"Color_Highlight01\">%s</>，是否放弃追踪<customstyle color=\"Color_Highlight01\">%s</>以追踪<customstyle color=\"Color_Highlight01\">%s</>?\n(<customstyle color=\"Color_Highlight01\">%s</>的活动进度仍然会保留)"),
        RichTextStyle = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_RichTextStyle1", "( %d<customstyle color=\"C002\">/%d</> ) %s"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_RichTextStyle2", "<customstyle color=\"C003\">( %d/%d ) %s</>"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_RichTextStyle3", "( <customstyle color=\"C001\">%d</>/%d ) %s"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_RichTextStyle4", "<customstyle color=\"C000\">( %d/%d ) %s</>"),
        },
        SequenceTaskName = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_SequenceTaskName1", "任务 · 一"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_SequenceTaskName2", "任务 · 二"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_SequenceTaskName3", "任务 · 三"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_SequenceTaskName4", "任务 · 四"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_SequenceTaskName5", "任务 · 五"),
        },
        NuowenKeyDialogue = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_NuowenKeyDialogue1", "哈夫克公司的那帮家伙在研究一种神秘的芯片，他们的研究基地被一种特殊的秘钥保护…"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_NuowenKeyDialogue2", "据说诺翁芯片对哈夫克集团至关重要，虽然还不知道那是什么，但我们会奖励所有破坏哈夫克计划的人"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_NuowenKeyDialogue3", "哈夫克集团用秘钥把很多区域保护了起来，据说他们在里面研究一种特殊的诺翁芯片…"),
        },
        NuowenPasswordDialogue = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_NuowenPasswordDialogue1", "我们阿萨拉军阀也不是吃素的，哈夫克诺翁保险箱的密码已经被我们全部破译了！想要密码就拿芯片来换吧"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_NuowenPasswordDialogue2", "现在外面到处都是散落的诺翁计划保险箱，哈夫克的计划已经被我们搅乱了！快去趁乱开几个保险箱吧，拿到了就是赚到"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_NuowenPasswordDialogue3", "哈夫克集团的这些保险箱往往只需要四位密码验证，这种程度的防护根本挡不住我们阿萨拉黑客的攻击…想要密码的话，我可以给你！"),
        },
        SniperStatus = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_SniperStatus1", "制作人正在匹配一局游戏……"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_SniperStatus2", "制作人正在进行一局游戏……"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_SniperStatus3", "制作人正在思考三角洲的未来……"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_SniperStatus4", "制作人正在检阅他的大作……"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_SniperStatus5", "正在获取制作人状态……"),
        },
        StarFireTasks = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireTasks1", "积攒充能"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireTasks2", "入局维修"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireTasks3", "信号接收"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireTasks4", "线索搜寻"),
        },
        StarFireSignalTower = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower1", "检查站"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower2", "蓝港码头"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower3", "钻石皇后酒店"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower4", "区域变电站"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower5", "阿米亚小镇"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower6", "超星车站"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_StarFireSignalTower7", "哈夫克雷达站"),
        },
        ProfitLevel = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ProfitLevel1", "有限"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ProfitLevel2", "可观"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ProfitLevel3", "丰厚"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ProfitLevel4", "暴利"),
        },
        ChargingEnergy = NSLOCTEXT("ActivityModule", "Lua_Activity_ChargingEnergy", "充能"),
        ChargedEnergy = NSLOCTEXT("ActivityModule", "Lua_Activity_ChargedEnergy", "已充能"),
        CheckLocation = NSLOCTEXT("ActivityModule", "Lua_Activity_CheckLocation", "查看位置"),
        CheckClue = NSLOCTEXT("ActivityModule", "Lua_Activity_CheckClue", "查看线索"),
        TimeToTime = NSLOCTEXT("ActivityModule", "Lua_Activity_TimeToTime", "%s - %s"),
        NumRidNum = NSLOCTEXT("ActivityModule", "Lua_Activity_NumRidNum", "%s/%s"),
        Percentage = NSLOCTEXT("ActivityModule", "Lua_Activity_Percentage", "%s%%"),
        Percentage1 = NSLOCTEXT("ActivityModule", "Lua_Activity_Percentage1", "<customstyle color=\"Color_Highlight01\">%s</>/%s"),
        Percentage2 = NSLOCTEXT("ActivityModule", "Lua_Activity_Percentage2", "<customstyle color=\"C003\">%s/%s</>"),
        Percentage3 = NSLOCTEXT("ActivityModule", "Lua_Activity_Percentage3", "<customstyle color=\"C001\">%s</><customstyle color=\"C002\">/%s</>"),
        Percentage4 = NSLOCTEXT("ActivityModule", "Lua_Activity_Percentage4", "<customstyle color=\"Color_DarkNegative\">%s</>/%s"),
        OpenOnDay = NSLOCTEXT("ActivityModule", "Lua_Activity_OpenOnDay", "第%d天开启"),
        GotoCollection = NSLOCTEXT("ActivityModule", "Lua_Activity_Collection_GotoCollection", "前往收集"),
        CanBeSubmitted = NSLOCTEXT("ActivityModule", "Lua_Activity_Collection_AvailableForCollection", "提交"),
        Submitted = NSLOCTEXT("ActivityModule", "Lua_Activity_Collection_AvailableForCollectionDone", "已提交"),
        SubmitKeepsake = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_SubmitKeepsake", "提交信物"),
        TaskProgress = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_TaskProgress", "任务进度"),
        OpenTime = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_OpenTime", "开启时间"),
        ForceSuffix = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_ForceSuffix", "势力"),
        MapName1 = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_MapName1", "长弓溪谷"),
        MapName2 = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_MapName2", "巴克什"),
        MapName3 = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_MapName3", "零号大坝"),
        MapName4 = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_MapName4", "航天基地"),
        PercentageNum = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_PercentageNum", "%s%%"),
        TaskLockTip = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_TaskLockTip", "任务未解锁，请先完成前置任务"),
        LocationLockTip = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_LocationLockTip", "%s天%s小时后开放该任务线"),
        ArchiveLockTip = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_ArchiveLockTip", "敌人信息尚未解锁"),
        LackOfProps = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_LackOfProps", "道具数量不足"),
        ArchiveBackText = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_ArchiveBackText", "档案"),
        InfoExampleText = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_InfoExampleText", "%s：<customstyle color=\"C001\">%s</>"),
        Birthday = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_Birthday", "出生日期"),
        Height = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_Height", "身高"),
        Weight = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_Weight", "体重"),
        Camp = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_Camp", "阵营"),
        Map = NSLOCTEXT("ActivityModule", "Lua_Activity_Filing_Map", "据点"),
        RoleSurveyClueTabTitle = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyClueTabTitle1", "任务获取"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyClueTabTitle2", "资料勘察"),
        },
        RoleSurveyBossBtnCollect = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyBossBtnCollect", "去收集"),
        RoleSurveyBossBtnAuth = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyBossBtnAuth", "授权"),
        RoleSurveyBossBtnGoto = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyBossBtnGoto", "前往"),
        RoleSurveyBossBtnKilled = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyBossBtnKilled", "已击杀"),
        RoleSurveyOptionRewardDesc = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyOptionRewardDesc", "回答正确可额外获得线索"),
        RoleSurveyFinalRewardDesc = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyFinalRewardDesc", "完成最终任务获取"),
        RoleSurveyInfoNumber = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoNumber", "情报%d"),
        RoleSurveyInfoNumberFinish = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoNumberFinish", "(√) 情报%d"),
        RoleSurveyInfoBtnGoto = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoBtnGoto", "去获取"),
        RoleSurveyInfoBtnCommit = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoBtnCommit", "提交"),
        RoleSurveyInfoBtnCommited = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoBtnCommited", "已提交"),
        RoleSurveyInfoClueNum = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoClueNum", "%d/%d"),
        RoleSurveyInfoTitleNum = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoTitleNum", "(%d/%d)"),
        RoleSurveyInfoExchangeBtn = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoExchangeBtn", "兑换"),
        RoleSurveyExchangeLock = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyExchangeLock", "解锁所有情报后解锁兑换"),
        RoleSurveyOptionAlreadyChoose = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyOptionAlreadyChoose", "已选择选项，请勿重复选择"),
        RoleSurveyStageBtnBossType0 = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyStageBtnBossType0", "行动收网"),
        RoleSurveyStageBtnBossType1 = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyStageBtnBossType1", "审查通过"),
        RoleSurveyInfoDesc = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoDesc", "情报详情"),
        RoleSurveyInfoNoReadyDescType0 = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoNoReadyDescType0", "目前情报线索不足，还不能轻举妄动，以免打草惊蛇，需要更多情报才能申请行动授权。"),
        RoleSurveyInfoNoReadyDescType1 = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoNoReadyDescType1", "目前情报线索不足，需要更多情报才能申请背调通过。"),
        RoleSurveyBossTitleType0 = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyBossTitleType0", "行动档案"),
        RoleSurveyBossTitleType1 = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyBossTitleType1", "审查档案"),
        RoleSurveyInfoTitleType = NSLOCTEXT("ActivityModule", "Lua_Activity_RoleSurveyInfoTitleType", "情报解析"),
        StarsDecryptTimes = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptTimes", "当前有%d次解密机会"),
        StarsDecryptBtnTxt = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptBtnTxt", "解密"),
        StarsDecryptSuccTxt = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptSuccTxt", "%s"),
        StarsNoDecrypt = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsNoDecrypt", "未解密"),
        StarsDecryptKeyTech = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptKeyTech", "核心技术"),
        StarsDecryptOrdinaryTech = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptOrdinaryTech", "不重要的资料"),
        StarsDecryptTaskUnComplete = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptTaskUnComplete", "未完成"),
        StarsDecryptTips = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptTips", "活动期间，在全部地图搜索【资料情报】类道具，每成功带出一件【资料情报】类道具，即可获得一次解密次数"),
        StarsDecryptNoTimesTips = NSLOCTEXT("ActivityModule", "Lua_Activity_StarsDecryptNoTimesTips", "当前无解密次数"),
        ExchangeNumFormat = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeNumFormat", "{countNum}/{maxNum}"),
        NoMoreRefresh = NSLOCTEXT("ActivityModule", "Lua_Activity_NoMoreRefresh", "本轮任务后不再刷新"),
        Fulled = NSLOCTEXT("ActivityModule", "Lua_Activity_Fulled", "已满"),
        GetPoints = NSLOCTEXT("ActivityModule", "Lua_Activity_GetPoints", "获取点数"),
        PointsAreFull = NSLOCTEXT("ActivityModule", "Lua_Activity_PointsAreFull", "点数已满"),
        InsufficientBadges = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientBadges", "徽章不足，未解锁制造权限"),
        UnlockManufacturing = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockManufacturing", "解锁制造"),
        CanManufacturedToday = NSLOCTEXT("ActivityModule", "Lua_Activity_CanManufacturedToday", "今日可制造：%s"),
        CanPurchase = NSLOCTEXT("ActivityModule", "Lua_Activity_CanPurchase", "可购买：%s"),
        CommodityTrading = NSLOCTEXT("ActivityModule", "Lua_Activity_CommodityTrading", "商品交易"),
        TradingPoints = NSLOCTEXT("ActivityModule", "Lua_Activity_TradingPoints", "交易点数"),
        UnlockTransaction = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockTransaction", "解锁交易"),
        UnlockAccessories = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockAccessories", "配件解锁"),
        ObtainedTheRightToUse = NSLOCTEXT("ActivityModule", "Lua_Activity_ObtainedTheRightToUse", "已获得使用权"),
        UsePermanentlyAfterUnlock = NSLOCTEXT("ActivityModule", "Lua_Activity_UsePermanentlyAfterUnlock", "解锁后可永久使用"),
        RefreshPurchaseRestrictions = NSLOCTEXT("ActivityModule", "Lua_Activity_RefreshPurchaseRestrictions", "刷新购买限制"),
        Obtained = NSLOCTEXT("ActivityModule", "Lua_Activity_Obtained", "已获得"),
        HafcoinInsufficient = NSLOCTEXT("ActivityModule", "Lua_Activity_HafcoinInsufficient", "哈夫币不足"),
        PurchaseLimitReachedToday = NSLOCTEXT("ActivityModule", "Lua_Activity_PurchaseLimitReachedToday", "该商品当日购买次数已用完"),
        LackOfReputation = NSLOCTEXT("ActivityModule", "Lua_Activity_LackOfReputation", "交易点数不足，获取更多点数以解锁交易"),
        PurchasePermissionUnlocked = NSLOCTEXT("ActivityModule", "Lua_Activity_PurchasePermissionUnlocked", "已解锁该商品购买权限"),
        InsufficientUnlockPoints = NSLOCTEXT("ActivityModule", "Lua_Activity_InsufficientUnlockPoints", "解锁点数不足，请获取更多点数"),
        ManufacturingPermission = NSLOCTEXT("ActivityModule", "Lua_Activity_ManufacturingPermission", "制造权限"),
        UsingPermission = NSLOCTEXT("ActivityModule", "Lua_Activity_UsingPermission", "使用权限"),
        GunManufacturing = NSLOCTEXT("ActivityModule", "Lua_Activity_GunManufacturing", "枪械制造"),
        PermissionUnlocking = NSLOCTEXT("ActivityModule", "Lua_Activity_PermissionUnlocking", "权限解锁"),
        MaterialInsufficient = NSLOCTEXT("ActivityModule", "Lua_Activity_MaterialInsufficient", "当前材料不足"),
        DailyLimitReached = NSLOCTEXT("ActivityModule", "Lua_Activity_DailyLimitReached", "当日次数已用完"),
        SignInTemplateLocked = NSLOCTEXT("ActivityModule", "Lua_Activity_SignInTemplateLocked", "未完成"),
        AutoClosedSingleModeFunc = NSLOCTEXT("ActivityModule", "Lua_Activity_AutoClosedSingleModeFunc", "已关闭单模式活动可见功能"),
        UsableWeapons = NSLOCTEXT("ActivityModule", "Lua_Activity_UsableWeapons", "可使用<customstyle color=\"Color_Highlight01\">%s</>的枪械"),
        AccessoryUsage = NSLOCTEXT("ActivityModule", "Lua_Activity_AccessoryUsage", "配件用途"),
        UnlockManufacturingByLevel = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockManufacturingByLevel", "达到%s解锁制造权"),
        UnlockManufacturingAtFirst = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockManufacturingAtFirst", "请先获取制造点数以解锁制造权"),
        ExchangeLimitReachedToday = NSLOCTEXT("ActivityModule", "Lua_Activity_ExchangeLimitReachedToday", "今日兑换已达上限"),
        SwitchToCorrespondingMode = NSLOCTEXT("ActivityModule", "Lua_Activity_SwitchToCorrespondingMode", "当前处在其他模式中，请切换到对应模式查看"),
        GameplayIntroduction = NSLOCTEXT("ActivityModule", "Lua_Activity_GameplayIntroduction", "玩法介绍"),
        PlayDescription = NSLOCTEXT("ActivityModule", "Lua_Activity_PlayDescription", "玩法说明"),
        SupplementExplanation = NSLOCTEXT("ActivityModule", "Lua_Activity_SupplementExplanation", "补充说明"),
        CompetitionRule = NSLOCTEXT("ActivityModule", "Lua_Activity_CompetitionRule", "参赛规则"),
        PointsRule = NSLOCTEXT("ActivityModule", "Lua_Activity_PointsRule", "积分规则"),
        NoInformationContent = NSLOCTEXT("ActivityModule", "Lua_Activity_NoInformationContent", "暂无信息内容"),
        Tuning = NSLOCTEXT("ActivityModule", "Lua_Activity_Tuning", "调台"),
        RemainingTimes = NSLOCTEXT("ActivityModule", "Lua_Activity_RemainingTimes", "剩余次数：%s"),
        NoTuningTimes = NSLOCTEXT("ActivityModule", "Lua_Activity_NoTuningTimes", "没有调台次数，请收集"),
        HotProducts = NSLOCTEXT("ActivityModule", "Lua_Activity_HotProducts", "本期热品"),
        TuningObtain = NSLOCTEXT("ActivityModule", "Lua_Activity_TuningObtain", "调台获取"),
        ObtainTuning = NSLOCTEXT("ActivityModule", "Lua_Activity_ObtainTuning", "获取调台"),
        ListenInformation = NSLOCTEXT("ActivityModule", "Lua_Activity_ListenInformation", "听取信息"),
        NewsRecord = NSLOCTEXT("ActivityModule", "Lua_Activity_NewsRecord", "新闻记录"),
        AudienceWelfare = NSLOCTEXT("ActivityModule", "Lua_Activity_AudienceWelfare", "听众福利"),
        MoreListenMoreRewards = NSLOCTEXT("ActivityModule", "Lua_Activity_MoreListenMoreRewards", "越多收听，越多奖励"),
        TuningTimes = NSLOCTEXT("ActivityModule", "Lua_Activity_TuningTimes", "可调台次数：%s"),
        PreselectedRewards = NSLOCTEXT("ActivityModule", "Lua_Activity_PreselectedRewards", "当前可预选<customstyle color=\"Color_Highlight01\">%s</>个奖励"),
        GoToExchange = NSLOCTEXT("ActivityModule", "Lua_Activity_GoToExchange", "前往换购"),
        PreselectReward = NSLOCTEXT("ActivityModule", "Lua_Activity_PreselectReward", "预选奖励"),
        PreselectRewardIsFulled = NSLOCTEXT("ActivityModule", "Lua_Activity_PreselectRewardIsFulled", "预选内容已满，请解锁更多或移除其它奖励"),
        PleaseChooseReward = NSLOCTEXT("ActivityModule", "Lua_Activity_PleaseChooseReward", "请选择您的奖励"),
        UnlockReward = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockReward", "获取调台次数达%s解锁"),
        AddAdjustTimes = NSLOCTEXT("ActivityModule", "Lua_Activity_AddAdjustTimes", "调台次数+%s"),

        ArkBook = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBook", "图鉴"),
        ArkResource = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResource", "资源获取"),
        ArkRecruitInfo = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitInfo", "招聘信息"),
        ArkBoardLevel = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardLevel1", "金牌刊位"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardLevel2", "银牌刊位"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardLevel3", "铜牌刊位"),
        },
        ArkBoardState = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardState1", "响应人数"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardState2", "招聘中"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardState3", "待发布"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBoardState4", "发起招募"),
        },
        ArkRuleText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText1", "规则说明"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText2", "收集资源"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText3", "干员招募"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText4", "解锁奖励"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText5", "日常入局，完成任务，获取资源"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText6", "选择刊位，选择广告，招聘干员"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText7", "完成招聘，解锁奖励"),
            [8] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRuleText8", "补充说明"),
        },
        ArkRecruitText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText1", "发布招募"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText2", "广告内容："),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText3", "刊载位置："),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText4", "预计用时："),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText5", "预计响应人数："),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText6", "合成玉预算"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText7", "招聘许可"),
            [8] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitText8", "发布"),
        },
        ArkResourceText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText1", "招聘许可获取"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText2", "烽火地带：局内带出"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText3", "全面战场：积分获取"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText4", "进行任何烽火地带行动都有一定概率发现合成玉"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText5", "根据每局积分获得不定数量的合成玉"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText6", "获取记录"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText7", "您的%s全面战场积分已经转化为%s个<customstyle color=\"Color_Highlight01\">【合成玉】</>"),
            [8] = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkResourceText8", "您的%s带出物资价值已经转化为%s个<customstyle color=\"Color_Highlight01\">【合成玉】</>"),
        },

        ArkLeftRecruitNum = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkLeftRecruitNum", "剩余招聘次数：%s"),
        ArkRecruitHeros = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruitHeros", "共有%s名应聘者，该录用谁？"),
        ArkUnlock = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkUnlock", "恭喜解锁"),
        ArkEliteLevel = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkEliteLevel", "精英阶段%s"),
        ArkDoneRecruit = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkDoneRecruit", "招聘次数已用尽"),
        ArkRecruiting = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkRecruiting", "正在招聘中"),
        ArkTimeNotEnough = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkTimeNotEnough", "活动剩余时间不足"),
        ArkBookLocked = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBookLocked", "未解锁"),
        ArkBookUnlockReward = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkBookUnlockReward", "解锁可得"),
        ArkLackResource = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkLackResource", "所需资源不足"),
        
        ArkGainStone = NSLOCTEXT("ActivityModule", "Lua_Activity_ArkGainStone", "您的烽火地带/全面战场表现已转化为"),
        
        MorgenGainCoin = NSLOCTEXT("ActivityModule", "Lua_Activity_MorgenGainCoin", "获得"),
        MorgenSanity = NSLOCTEXT("ActivityModule", "Lua_Activity_MorgenSanity", "理智值:%s"),
        MorgenLackSanity = NSLOCTEXT("ActivityModule", "Lua_Activity_MorgenLackSanity", "理智不足，请收集"),
        MorgenDownloadTips = NSLOCTEXT("ActivityModule", "Lua_Activity_MorgenDownloadTips", "须首先下载狂蜂夜想曲扩展包{PakSize}MB资源，是否确认下载?"),
        MogenPopBtnText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenBtnText1", "记录"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenBtnText2", "图鉴"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenBtnText3", "理智"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityReward", "奖励"),
        },
        MogenResultText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenResultText1", "失败"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenResultText2", "撤离"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenResultText3", "中退"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenResultText4", "通关"),
        },
        MogenExploreBtnText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreBtnText1", "开始探索"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreBtnText2", "进行中"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreBtnText3", "再来一次"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreBtnText4", "确定"),
        },
        MogenExploreText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreText1", "快速探索"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreText2", "收益："),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreText3", "点击开始探索，开始快速通关"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreText4", "快速探索消耗理智值，当理智为0或者被击败时，则结束"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreText5", "正在探索中，是否中止探索"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreText6", "剩余理智不足"),
        },
        MogenExploreRecordText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreRecordText1", "通过"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreRecordText2", "获得%s个<customstyle color=\"Color_Highlight01\">【龙门币】</>"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreRecordText3", "通关结束"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreRecordText4", "当前已中止通关"),
        },
        MogenExploreStateText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreStateText1", "通关中……"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreStateText2", "探索完成"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreStateText3", "已终止"),
        },
        MogenRuleText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRuleText1", "收集理智"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRuleText2", "日常入局，获取理智"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRuleText3", "启动游戏"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRuleText4", "启动或快速探索参与游戏"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRuleText5", "收集龙门币"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRuleText6", "冲关赢取龙门币兑换大奖"),
        },
        MogenExploreLogText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText1", "结算记录"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText2", "快速探索模式"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText3", "时间"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText4", "结果"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText5", "探索边界"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText6", "消耗"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenExploreLogText7", "带出"),
        },
        MogenEventPopText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEventPopText1", "事件记录"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEventPopText2", "途中偶遇"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEventPopText3", "非我族类"),
        },
        MogenEnemyText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEnemyText1", "攻击"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEnemyText2", "防御"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEnemyText3", "血量"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenEnemyText4", "暴击率"),
        },
        MogenRewardText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRewardText1", "奖励总览"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRewardText2", "推进游戏进程可获得海量龙门币，通关游戏可获取以下奖励"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRewardText3", "第一轮"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRewardText4", "第二轮"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenRewardText5", "最高记录"),
        },
        MogenSanityText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenSanityText1", "您的%s全面战场积分已经转化为<customstyle color=\"Color_Theme_01\">%s</>理智"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenSanityText2", "您的%s带出物资价值已经转化为<customstyle color=\"Color_Theme_01\">%s</>理智"),
        },
        MogenGameBtnText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenGameBtnText1", "快速探索"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenGameBtnText2", "启动游戏"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MogenGameBtnText3", "理智用于快速探索"),
        },

        MagicTowerText = {
            [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText1", "当前挑战的怪物难度较大，是否确认仍要进行挑战?"),
            [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText2", "快速战斗已开启"),
            [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText3", "快速战斗已关闭"),
            [4] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText4", "预测战斗结果已开启"),
            [5] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText5", "预测战斗结果已关闭"),
            [6] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText6", "本次战斗结果高度不稳定，预测的可信性偏低，可能出现意想不到的战斗结果，是否继续？"),
            [7] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText7", "%s%s 对 %s 造成 %s 点伤害"),
            [8] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText8", "你"),
            [9] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText9", "敌人"),
            [10] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText10", "暴击！ "),
            [11] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText11", "退出游戏将失去当前进度，是否确认要退出? "),
            [12] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText12", "战斗结束"),
            [13] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText13", "胜利！"),
            [14] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText14", "失败！"),
            [15] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText15", "已开启门禁"),
            [16] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText16", "门禁权限卡不足"),
            [17] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText17", "未解锁该战斗训练师！"),
            [18] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText18", "该模式下可点击敌人查看其属性！"),
            [19] = NSLOCTEXT("ActivityModule", "Lua_Activity_MagicTowerText19", "我是%s，不是怪物！"),
        },
        
        -- 因为只上国服修改文本时决定不再走本地化流程
        RadioDescTxt = {
            -- [1] = NSLOCTEXT("ActivityModule", "Lua_Activity_RadioDescTxt1", "查看任务，完成获得调台次数，调台次数可用于调台"),
            -- [2] = NSLOCTEXT("ActivityModule", "Lua_Activity_RadioDescTxt2", "点击调台听取信息，根据信息判断下期集市物品，同时解锁预选奖励节点"),
            -- [3] = NSLOCTEXT("ActivityModule", "Lua_Activity_RadioDescTxt3", "预选下期集市物品，不占您的仓库，活动周期结束后，发放至您的邮箱"),
            [1] = "查看任务，完成获得调台次数，调台次数可用于调台",
            [2] = "点击调台听取信息，根据信息判断下期集市物品，同时解锁预选奖励节点",
            [3] = "预选下期集市物品，不占您的仓库，活动周期结束后，发放至您的邮箱",
        },

        UnlockCondition          = NSLOCTEXT("ActivityModule", "Lua_Activity_UnlockCondition"           , "解锁条件"),
        ForecastDayAndHourFmt    = NSLOCTEXT("ActivityModule", "Lua_Activity_ForecastDayAndHourFmt"     , "距离开放还有%d天%d时"),
        ForecastHourAndMinFmt    = NSLOCTEXT("ActivityModule", "Lua_Activity_ForecastHourAndMinFmt"     , "距离开放还有%d时%d分"),
        ForecastMinAndSecFmt     = NSLOCTEXT("ActivityModule", "Lua_Activity_ForecastMinAndSecFmt"      , "距离开放还有%d分%d秒"),
        ForecastSecFmt           = NSLOCTEXT("ActivityModule", "Lua_Activity_ForecastSecFmt"            , "距离开放还有%d秒"),
        NetworkFailure           = NSLOCTEXT("ActivityModule", "Lua_Activity_NetworkFailure"            , "活动数据暂未更新，请稍后重试"),
        JumpTargetMissing        = NSLOCTEXT("ActivityModule", "Lua_Activity_JumpTargetMissing"         , "该活动不在当前模式，请切换模式进入"),
        JumpGroupFailed          = NSLOCTEXT("ActivityModule", "Lua_Activity_JumpGroupFailed"           , "尝试跳转的活动分组当前模式下为空"),
        HighFrequency            = NSLOCTEXT("ActivityModule", "Lua_Activity_HighFrequency"             , "点击过于频繁，请稍后重试"),
        RewardSelectedFmt        = NSLOCTEXT("ActivityModule", "Lua_Activity_RewardSelected"            , "已更换奖励: {rewardText}"),

        ForecastCountDownFmt     = NSLOCTEXT("ActivityModule", "Lua_Activity_ForecastCountDownFmt"      , "<customstyle color=\"C006\">%s</>后开放"),

        ChooseFinalReward        = NSLOCTEXT("ActivityModule", "Lua_Activity_ChooseFinalReward"         , "选择最终奖励"),
        -- ActivityEndCountDownFmt  = NSLOCTEXT("ActivityModule", "Lua_Activity_ActivityEndCountDownFmt"   , "%s"),

        -- UnlockCondition          = "解锁条件",
        -- ForecastDayAndHourFmt    = "距离开放还有%d天%d时",
        -- ForecastHourAndMinFmt    = "距离开放还有%d时%d分",
        -- ForecastMinAndSecFmt     = "距离开放还有%d分%d秒",
        -- ForecastSecFmt           = "距离开放还有%d秒",
        -- NetworkFailure           = "活动数据暂未更新，请稍后重试",
    },

    -- 活动列表每一行出场动画间隔
    FrontPageAnimationRowInterval = 0.05,
}

ActivityConfig.MogenTower = {
    StepLength = 256, --地图长度单位
    -- StepLength = 76.8, --地图长度单位
}

ActivityConfig.MogenTower.SpineInitScaleTable = {
    [4020201]= FVector(0.7, 0.7, 0.7),
    [4020202]= FVector(0.7, 0.7, 0.7),
    [4020203]= FVector(0.73, 0.73, 0.73),
    [4020204]= FVector(0.7, 0.7, 0.7),
    [4020205]= FVector(0.7, 0.7, 0.7),
    [4020206]= FVector(0.73, 0.73, 0.73),
    [4020207]= FVector(0.7, 0.7, 0.7),
    [4020208]= FVector(0.7, 0.7, 0.7),
    [4020209]= FVector(0.7, 0.7, 0.7),
    [4020210]= FVector(0.7, 0.7, 0.7),
    [4020211]= FVector(0.7, 0.7, 0.7),
    [4020212]= FVector(0.7, 0.7, 0.7),
}


---@enum ActivityConfig.MogenTower.AlignmentType
ActivityConfig.MogenTower.AlignmentType = {
    Center      = 1,
    CenterTop   = 2,
    CenterBottom= 3,
}

return ActivityConfig
