----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class ActivityMagicTowerTextItem3D : LuaUIBaseView
local ActivityMagicTowerTextItem3D = ui("ActivityMagicTowerTextItem3D")

function ActivityMagicTowerTextItem3D:Ctor()
    self._wtNumTxt = self:Wnd("DFTextBlock_0", UITextBlock)
    self._wtNumTxt:Collapsed()
end

function ActivityMagicTowerTextItem3D:SetNumTxt(num)
    if num and num ~= "" then
        self._wtNumTxt:SetText(tostring(num))
        self._wtNumTxt:SelfHitTestInvisible()
    else
        self._wtNumTxt:Collapsed()
    end
end

return ActivityMagicTowerTextItem3D