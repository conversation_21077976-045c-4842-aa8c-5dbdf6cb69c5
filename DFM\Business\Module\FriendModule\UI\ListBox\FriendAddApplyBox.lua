----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
----- LOG FUNCTION AUTO GENERATE END -----------
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"


---@class FriendAddApplyBox : LuaUIBaseView
local FriendAddApplyBox = ui("FriendAddApplyBox")

function FriendAddApplyBox:Ctor()
    loginfo("FriendAddApplyBox:Ctor")
    self._wtPlayerNameText = self:Wnd("wtPlayerName", UITextBlock)
    self._wtApplyFrom = self:Wnd("wtAddFrom", UITextBlock)

    self._wtIgnoreBtn = self:Wnd("wtIgnoreBtn", UIWidgetBase)
    self._wtAgreeBtn = self:Wnd("wtAgreeBtn", UIWidgetBase)
    self._wtIgnoreBtn:Event("OnClicked", self._OnBtnRefuseFriendClick, self)
    self._wtAgreeBtn:Event("OnClicked", self._OnBtnApplyFriendClick, self)
    self._wtRankIcon = self:Wnd("wtRankIcon" ,UIWidgetBase)
    self._wtRankDivision = self:Wnd("wtRankDivision", UITextBlock)

    self._wtMilitary = self:Wnd("WBP_Friend_BrandAvatar", UIWidgetBase)
    if IsHD() then
        self._wtBHDMark = self:Wnd("WBP_BHD_OwnMark",UIWidgetBase)
        self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self._wtBHDMark, "DFTipsAnchor", self.ShowTips,self.CloseTips)
        self._wtFocusBtn = self:Wnd("DFButton_435", UIButton)
        self._wtFocusBtn:Event("OnFocusReceivedEvent", self._OnFocusReceived, self)
        self._wtFocusBtn:Event("OnFocusLostEvent", self._OnFocusLost, self)
    end
    self._wtPlayerIcon = self:Wnd("WBP_CommonHeadIcon", CommonHeadIcon)
    self._wtPlayerIcon:SetFocusable(false)
    self._playerInfo = {}
    self._applyMsg = ""
    self._navGroup = nil
    self.PlayerInfoActionName = "BHDPlayerInfo_Gamepad"
    self._actionList = {self.PlayerInfoActionName}
end

function FriendAddApplyBox:InitNavGroup()
    if not self._navGroup then
        -- 创建导航组
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        if self._navGroup then
            -- 把两个按钮加入导航组
            self._navGroup:AddNavWidgetToArray(self._wtIgnoreBtn)
            self._navGroup:AddNavWidgetToArray(self._wtAgreeBtn)
        end
    end
end

function FriendAddApplyBox:ShowUI(data)
    self._playerInfo = data.player_info
    self._applyMsg = data.apply_msg
    self._source = data.source
    self._wtPlayerNameText:SetText(self._playerInfo.nick_name)
    self._wtApplyFrom:SetText(self._source ~= 0 and Module.Friend.Config.friendApplyType[self._source] or "")
    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.LobbyBHD then
        self:SetStyleInBHD()
        return
    end

    local playerInfo = {
        pic_url = self._playerInfo.pic_url,
        player_id = self._playerInfo.player_id,
        level = 0,
        nick_name = self._playerInfo.nick_name,
        --- BEGIN MODIFICATION @ VIRTUOS:Logo
        plat_id = self._playerInfo.plat or PlatIDType.Plat_Invalid,
        --- END MODIFICATION
    }

    if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
        playerInfo.level = self._playerInfo.season_lvl
        playerInfo.rank_attended = self._playerInfo.sol_rank_attended
        playerInfo.rank_score = self._playerInfo.sol_rank_score
    elseif Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby then
        playerInfo.level = self._playerInfo.level
        playerInfo.rank_attended = self._playerInfo.mp_rank_attended
        playerInfo.rank_score = self._playerInfo.mp_rank_score

        -- azhengzheng:传入胜者为王段位信息
        playerInfo.show_commander_rank_points = self._playerInfo.show_commander_rank_points
        playerInfo.mp_commander_score = self._playerInfo.mp_commander_score
    end

    FriendLogic.InitRankByIconInfo(self._wtRankIcon, self._wtRankDivision, playerInfo)
    local btnTbl = {
        HeadButtonType.PlayerInformat,
        HeadButtonType.AddFriend,
        HeadButtonType.AddBlack,
    }

    self._wtPlayerIcon:InitPortrait(playerInfo, HeadIconType.HeadList, btnTbl)
    if IsHD() then
        --self:SetBHDOwnMark(self._playerInfo)
    end
    -- self:InitNavGroup()
    -- self:UpdateGamepadKeyIcon()
end

function FriendAddApplyBox:_PlayBoxAnimation()
    self:Visible()
    self:PlayAnimation(self.WBP_FriendAddApplyBox_in, 0.0, 1, EUMGSequencePlayMode.Forward, 1.0, false)
end

function FriendAddApplyBox:_OnBtnApplyFriendClick()
    Server.FriendServer:ProcessNewFriendReq(self._playerInfo.player_id, true)
end

function FriendAddApplyBox:_OnBtnRefuseFriendClick()
    Server.FriendServer:ProcessNewFriendReq(self._playerInfo.player_id, false)
end

function FriendAddApplyBox:OnClose()
    self:RemoveAllLuaEvent()
    self:ClearNavGroup()
end

function FriendAddApplyBox:SetStyleInBHD()
    self._playIconInfo = {
        pic_url = self._playerInfo.pic_url,
        player_id = self._playerInfo.player_id,
    }
    FriendLogic.InitRankByIconInfo(self._wtRankIcon, self._wtRankDivision, self._playIconInfo)
    self._wtRankIcon:Collapsed()
    self._wtRankDivision:Collapsed()
    self._wtPlayerIcon:HitTestInvisible()
    self._wtPlayerIcon:InitPortrait(self._playIconInfo, HeadIconType.HeadList)

end

function FriendAddApplyBox:SetBHDOwnMark(info)
    --info.bhd_is_purchased = true
    if info.bhd_is_purchased then
        self._wtBHDMark:SelfHitTestInvisible()
    else
        self._wtBHDMark:Collapsed()
    end
end

function FriendAddApplyBox:ShowTips()
    local datas = {}
    if self._tipsInfo ~= ""  then
        table.insert(datas, {textContent = Module.LobbyBHD.Config.Loc.BHDOwned})
        self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas,self._wtDFTipsAnchor)
    end

end

function FriendAddApplyBox:CloseTips()
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
    end
end

function FriendAddApplyBox:ClearNavGroup()
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
end

function FriendAddApplyBox:UpdateGamepadKeyIcon()
        if IsHD() then
        if self._wtAgreeBtn and self._wtIgnoreBtn then
            self._wtAgreeBtn:SetDisplayInputAction("Common_ButtonLeft_Gamepad", true, nil, true)
            self._wtIgnoreBtn:SetDisplayInputAction("Common_ButtonTop_Gamepad", true, nil, true)
            self._wtAgreeBtn:SetKeyIconVisibility(true)
            self._wtIgnoreBtn:SetKeyIconVisibility(true)
            self._wtAgreeBtn.testid = self._playerInfo.player_id
        end

        Module.Friend.Config.Events.evtOnFriendAddBoxHovered:Invoke(self._actionList)

        -- 输入绑定
        if self._agreeInputHandle == nil then
            self._agreeInputHandle = self:AddInputActionBinding(
                "Common_ButtonLeft_Gamepad", 
                EInputEvent.IE_Pressed, 
                self._OnBtnApplyFriendClick, 
                self, 
                EDisplayInputActionPriority.UI_Pop
            )
        end

        if self._ignoreInputHandle == nil then
            self._ignoreInputHandle = self:AddInputActionBinding(
                "Common_ButtonTop_Gamepad", 
                EInputEvent.IE_Pressed, 
                self._OnBtnRefuseFriendClick, 
                self, 
                EDisplayInputActionPriority.UI_Pop
            )
        end

        if self._playerInfoActionNameInputHandle == nil then
            self._playerInfoActionNameInputHandle = self:AddInputActionBinding(
                "BHDPlayerInfo_Gamepad",
                EInputEvent.IE_Pressed,
                self._OnPlayerInfo,
                self, EDisplayInputActionPriority.UI_Pop)
        end
    end

    
end

function FriendAddApplyBox:_OnPlayerInfo()
    if self._wtPlayerIcon then
        self._wtPlayerIcon:ActiveClick()
    end
end

function FriendAddApplyBox:_OnFocusReceived()
    if not IsHD() then
        return
    end
    self:UpdateGamepadKeyIcon()
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function FriendAddApplyBox:_OnFocusLost()
    if not IsHD() then
        return
    end
    if self._wtAgreeBtn and self._wtIgnoreBtn then
        self._wtAgreeBtn:SetDisplayInputAction("", false, nil, true)
        self._wtIgnoreBtn:SetDisplayInputAction("", false, nil, true)
        -- self._wtAgreeBtn:SetKeyIconVisibility(false)
        -- self._wtIgnoreBtn:SetKeyIconVisibility(false)
    end

    if self._agreeInputHandle then
        self:RemoveInputActionBinding(self._agreeInputHandle)
        self._agreeInputHandle = nil
    end

    if self._ignoreInputHandle then
        self:RemoveInputActionBinding(self._ignoreInputHandle)
        self._ignoreInputHandle = nil
    end

    if self._playerInfoActionNameInputHandle then
        self:RemoveInputActionBinding(self._playerInfoActionNameInputHandle)
        self._playerInfoActionNameInputHandle = nil
    end

    for _, action in ipairs(self._actionList) do
        self:RemoveInputActionBinding(action)
    end

    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    Module.Friend.Config.Events.evtOnFriendAddBoxUnHovered:Invoke(self._actionList)
end

return FriendAddApplyBox