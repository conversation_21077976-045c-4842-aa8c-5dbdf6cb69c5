----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local CollectionRoomLogic = require "DFM.Business.Module.CollectionRoomModule.Logic.CollectionRoomLogic"
local CurrencyHelperTool = require "DFM.StandaloneLua.BusinessTool.CurrencyHelperTool"
local InteractionTip = require "DFM.Business.Module.CollectionRoomModule.UI.CollectionRoomInteractionTip"

local UHallCameraCtrlComponentBase = import "HallCameraCtrlComponentBase"
local AHallCollectionRoomDisplayCtrl = import "HallCollectionRoomDisplayCtrl"
local UDFMGameHudDelegates = import "DFMGameHudDelegates"
local USlateBlueprintLibrary = import "SlateBlueprintLibrary"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"

local EUILifeState = {
    ShowBegin = 1,
    Show = 2,
    Hide = 3,
}

---@class CollectionRoomPanel : LuaUIBaseView
local CollectionRoomPanel = ui("CollectionRoomPanel")

function CollectionRoomPanel:Ctor()
    self._wtRootPanel = self:Wnd("DFCanvasPanel_27", UIWidgetBase)
    self._wtEnterCollectionRoomBtn = self:Wnd("wtCommonButtonV1S1", DFCommonButtonOnly)
    self._wtEnterCollectionRoomBtn:Event("OnClicked", self._EnterCollectionRoomBtnClicked, self)
    self._wtLastMainCameraBtn = self:Wnd("DFButton_222", DFButtonOnly)
    self._wtLastMainCameraBtn:Event("OnClicked", self._OnLastMainCameraBtnClicked, self)
    self._wtNextMainCameraBtn = self:Wnd("DFButton_1", DFButtonOnly)
    self._wtNextMainCameraBtn:Event("OnClicked", self._OnNextMainCameraBtnClicked, self)
    self._wtCurrentLevel = self:Wnd("DFTextBlock", UITextBlock)
    self._wtTotalValue = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtCurrentShowAreaName = self:Wnd("DFTextBlock_359", UITextBlock)
    self._wtCommonCheckInstruction = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
    self._wtMusicPlayer = self._wtRootPanel:Wnd("WBP_CollectionHome_MusicPlayer", UIWidgetBase)
    if not IsHD() then
        self._wtCommonCheckInstruction:Event("OnCheckStateChanged", self._OnCheckStateChanged, self)
    end
    self._wtTipsAnchor = UIUtil.WndTipsAnchor(self, "DescTipAnchor", self._ShowTips, self._HideTips)
    self._wtUpgradeBtn = self:Wnd("WBP_CommonIconButton", DFButtonOnly)
    self._wtUpgradeBtn:Event("OnClicked", self._OnUpgradeBtnClicked, self)
    self._wtInteractionTip = self:Wnd("WBP_CollectionRoom_InteractionTip", InteractionTip)
    self._wtDFCanvasPanel = self:Wnd("DFCanvasPanel_3", UIWidgetBase)
    -- self._wtTotalPriceTitle = self:Wnd("DFTextBlock_193", UITextBlock)
    -- self._wtTotalPriceTitle:SetText(CollectionRoomConfig.Loc.CollectionRoomValue)
    

    ---@type number DIY展柜数量
    self._diyCabinetNum = 0
    ---@type number 收藏室总价值
    self._totalValue = 0
    ---@type table<EShowCabinetType,number[]> 各收藏柜价值
    self._cabinetValueMap = {}
    self._tipsHandle = nil
    ---@type CollectionRoomMainPanel
    self._parent = nil
    self._displayInputHandles = {}
    ---@type boolean
    self._bShowUpgrade_Gamepad = false
    ---@type number
    self._lifeState = EUILifeState.Hide
end

function CollectionRoomPanel:OnInitExtraData(parent)
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnInitExtraData", self, parent)
    self._parent = parent
end

function CollectionRoomPanel:OnOpen()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnOpen", self)
end

function CollectionRoomPanel:OnClose()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnClose", self)
end

function CollectionRoomPanel:OnActivate()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnActivate", self)
end

function CollectionRoomPanel:OnDeactivate()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnDeactivate", self)
end

function CollectionRoomPanel:OnShowBegin()
    if self._parent and self._parent:GetCurrentSubUINavID() ~= self.UINavID then
        PrintCollectionRoomUILifeLog("error", "CollectionRoomPanel:OnShowBegin blocked because UINavID not match", self)
       return
    end
    if self._lifeState ~= EUILifeState.Hide then
        PrintCollectionRoomUILifeLog("error", "CollectionRoomPanel:OnShowBegin blocked because lifeState=", self._lifeState, self)
        return
    end
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnShowBegin", self)
    self._lifeState = EUILifeState.ShowBegin
    local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    if deviceData and deviceData.level then
        self._wtCurrentLevel:SetText(string.format(CollectionRoomConfig.Loc.CurrentLevel, deviceData.level))
        if deviceData.level >= Server.BlackSiteServer:GetDeviceMaxLevel(EBlackSiteDeviceName2Id.CollectionRoom) then
            self._wtUpgradeBtn:Collapsed()
            self._bShowUpgrade_Gamepad = false
        else
            self._wtUpgradeBtn:Visible()
            self._bShowUpgrade_Gamepad = true
        end
    else
        self._wtUpgradeBtn:Visible()
        logerror("CollectionRoomPanel:OnShowBegin fail to get level")
    end
    self._diyCabinetNum = CollectionRoomLogic.GetDIYCabinetNum()
    local totalValue, upgradeValue, displayCabinetValue, specialCabinetValue, diyCabinetValues, seasonLimitedValue = CollectionRoomLogic.GetCollectionRoomTotalValue()
    self._totalValue = totalValue
    self._cabinetValueMap[0] = upgradeValue
    self._cabinetValueMap[EShowCabinetType.Display] = displayCabinetValue
    self._cabinetValueMap[EShowCabinetType.Special] = specialCabinetValue
    self._cabinetValueMap[EShowCabinetType.DIY] = diyCabinetValues
    self._cabinetValueMap[-1] = seasonLimitedValue
    self._wtTotalValue:SetText(CurrencyHelperTool.GetCurrencyNumFormatStr(self._totalValue))

    -- 注册红点
    self:_RegisterReddot()
    self:_SetViewTargetToMainCamera(true)

    Module.Guide.Config.EGuideEvent.evtOnGuideMsg:Invoke(EGuideMsgSig.CollectionRoomPanelOnShowBegin)
    if not IsHD() then
        local gameInst = GetGameInstance()
        if gameInst then
            UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Add(self._OnHandleMouseButtonUpEvent, self)
        end
    end

    -- 手柄适配
    if IsHD() then
        self:_EnableGamePadFeature()
    end
    
    self._wtMusicPlayer:SwitchState(true)
end

function CollectionRoomPanel:OnShow()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnShow", self)
    if self._lifeState ~= EUILifeState.ShowBegin then
        PrintCollectionRoomUILifeLog("error", "CollectionRoomPanel:OnShow blocked because lifeState=", self._lifeState, self)
        return
    end
    self._lifeState = EUILifeState.Show
    if IsHD() then
        self:AddLuaEvent(CollectionRoomConfig.Events.evtOnFocusInteractionTip, self._OnInteractionTipFocusReceived, self)
        self:AddLuaEvent(CollectionRoomConfig.Events.evtOnMusicPlayerEndOpening, self._OnMusicPlayerEndOpening, self)
        self:AddLuaEvent(CollectionRoomConfig.Events.evtOnMusicPlayerStartClosing, self._OnMusicPlayerStartClosing, self)
    end
end

function CollectionRoomPanel:OnHide()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnHide", self)
    if self._lifeState == EUILifeState.Hide then
        PrintCollectionRoomUILifeLog("error", "CollectionRoomPanel:OnHide blocked because lifeState=", self._lifeState, self)
        return
    end
    self._lifeState = EUILifeState.Hide
    if not IsHD() then
        local gameInst = GetGameInstance()
        if gameInst then
            UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonUpEvent:Remove(self._OnHandleMouseButtonUpEvent, self)
        end
    end
    self:_HideTips()
    self._wtCommonCheckInstruction:SetIsChecked(false, false)
    self:_UnBindCameraMovedDoneEvent()
    self._wtDFCanvasPanel:SelfHitTestInvisible()

    if self._reddotIns then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._reddotIns)
    end

    -- 手柄适配
    if IsHD() then
        self:_DisableGamePadFeature()
    end
    
end

function CollectionRoomPanel:_BindCameraMovedDoneEvent()
    if not self._eventHandle then
        local Ctrl = UGameplayStatics.GetActorOfClass(GetWorld(), AHallCollectionRoomDisplayCtrl)
        if Ctrl then
            local cameraComp = Ctrl:GetComponentByClass(UHallCameraCtrlComponentBase)
            if cameraComp then
                self._eventHandle = cameraComp.HallCameraMovedDone:Add(CreateCPlusCallBack(self._OnHallCameraMovedDone, self))
            end
        end
    end
end

function CollectionRoomPanel:_UnBindCameraMovedDoneEvent()
    if self._eventHandle then
        local Ctrl = UGameplayStatics.GetActorOfClass(GetWorld(), AHallCollectionRoomDisplayCtrl)
        if Ctrl then
            local cameraComp = Ctrl:GetComponentByClass(UHallCameraCtrlComponentBase)
            if cameraComp then
                cameraComp.HallCameraMovedDone:Remove(self._eventHandle)
            end
        end
        self._eventHandle = nil
    end
end

function CollectionRoomPanel:_OnHandleMouseButtonUpEvent(mouseEvent)
    local screenPos = mouseEvent:GetScreenSpacePosition()
    local geometry = self._wtCommonCheckInstruction:GetCachedGeometry()
    local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, screenPos)
    if not isUnder then
        self:_HideTips()
        self._wtCommonCheckInstruction:SetIsChecked(false, false)
    end
end

function CollectionRoomPanel:_OnHallCameraMovedDone()
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:_OnHallCameraMovedDone")
    self._wtDFCanvasPanel:SelfHitTestInvisible()
    self:PlayWidgetAnim(self.WBP_CollectionHome_Main_in_1)
    self:_UnBindCameraMovedDoneEvent()
end

function CollectionRoomPanel:_SetViewTargetToMainCamera(bFromOnShowBegin)
    bFromOnShowBegin = setdefault(bFromOnShowBegin, false)
    local targetCameraId = Module.CollectionRoom.Field.mainPanelTargetCameraId
    if targetCameraId == 1 then
        self._wtCurrentShowAreaName:SetText(CollectionRoomConfig.Loc.SpecialCabinetAreaName)
        self._wtInteractionTip:SetCabinetTypeAndId(EShowCabinetType.Special, 1)
        self._wtLastMainCameraBtn:SetIsEnabled(false)
        self._wtNextMainCameraBtn:SetIsEnabled(true)
        self:SetInteractionPosition(1)
    elseif targetCameraId == 2 then
        self._wtCurrentShowAreaName:SetText(CollectionRoomConfig.Loc.DisplayCabinetAreaName)
        self._wtInteractionTip:SetCabinetTypeAndId(EShowCabinetType.Display, 1)
        self._wtLastMainCameraBtn:SetIsEnabled(true)
        self._wtNextMainCameraBtn:SetIsEnabled(true)
        self:SetInteractionPosition(0)
    elseif targetCameraId == 3 then
        self._wtCurrentShowAreaName:SetText(CollectionRoomConfig.Loc.DIYCabinetAreaName)
        self._wtInteractionTip:SetCabinetTypeAndId(EShowCabinetType.DIY, 1)
        self._wtLastMainCameraBtn:SetIsEnabled(true)
        self._wtNextMainCameraBtn:SetIsEnabled(false)
        self:SetInteractionPosition(2)
    end
    local targetDisplayType = CollectionRoomConfig.CameraConfig.Main[targetCameraId]
    local bImmediatelyFlag = CollectionRoomLogic.SetViewTargetToNamedCamera(targetDisplayType, true)
    if not bImmediatelyFlag then
        self._wtDFCanvasPanel:Collapsed()
        self:_BindCameraMovedDoneEvent()
    else
        self._wtDFCanvasPanel:SelfHitTestInvisible()
    end

    if isvalid(self._reddotIns) then
        Module.ReddotTrie:UpdateDynamicReddot(self._wtInteractionTip, EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._reddotIns, self._fCheckReddot, nil)
    end
end

function CollectionRoomPanel:_OnSubStageChangeEnter(curSubStageType)
    if curSubStageType == ESubStage.CollectionRoom then
        self:_SetViewTargetToMainCamera()
    end
end

function CollectionRoomPanel:_EnterCollectionRoomBtnClicked()
    local targetCameraId = Module.CollectionRoom.Field.mainPanelTargetCameraId
    CollectionRoomLogic.EnterCollectionRoom(ECollectionRoomEnterFrom.MainPanel, {targetCameraId})
end

function CollectionRoomPanel:_OnLastMainCameraBtnClicked()
    local targetCameraId = Module.CollectionRoom.Field.mainPanelTargetCameraId
    if targetCameraId > 1 then
        Module.CollectionRoom.Field.mainPanelTargetCameraId = targetCameraId - 1
        self:_SetViewTargetToMainCamera()
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.MainPanelSwitchCamera)
    end
    if IsHD() then
        WidgetUtil.SetUserFocusToWidget(self._wtInteractionTip,true)
        self:_HideTips()
    end
end

function CollectionRoomPanel:_OnNextMainCameraBtnClicked()
    local targetCameraId = Module.CollectionRoom.Field.mainPanelTargetCameraId
    if targetCameraId < #CollectionRoomConfig.CameraConfig.Main then
        Module.CollectionRoom.Field.mainPanelTargetCameraId = targetCameraId + 1
        self:_SetViewTargetToMainCamera()
        LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.MainPanelSwitchCamera)
    end
    if IsHD() then
        WidgetUtil.SetUserFocusToWidget(self._wtInteractionTip,true)
        self:_HideTips()
    end
end

function CollectionRoomPanel:_OnCheckStateChanged(bChecked)
    if bChecked then
        self:_ShowTips()
    else
        self:_HideTips()
    end
end

function CollectionRoomPanel:_ShowTips()
    if self._tipsHandle then
        return
    end
    local contents = {}
    -- 标题
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.CollectionRoomValue,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._totalValue, CurrencyHelperTool.EKMThousandsType.None)),
        useBigFont = true,
    }})
    -- tips内容
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.UpgradeCostValue,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[0], CurrencyHelperTool.EKMThousandsType.None))
    }})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.SeasonLimitValue,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[-1], CurrencyHelperTool.EKMThousandsType.None))
    }})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.DisplayCabinetName,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[EShowCabinetType.Display], CurrencyHelperTool.EKMThousandsType.None))
    }})
    table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
        textTitle = CollectionRoomConfig.Loc.SpecialCabinetName,
        textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[EShowCabinetType.Special], CurrencyHelperTool.EKMThousandsType.None))
    }})
    for i = 1,self._diyCabinetNum do
        table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V11, data = {
            textTitle = CollectionRoomConfig.Loc.DIYCabinetName,
            textContent = string.format(CollectionRoomConfig.Loc.CabinetValue, CurrencyHelperTool.GetCurrencyNumFormatStr(self._cabinetValueMap[EShowCabinetType.DIY][i], CurrencyHelperTool.EKMThousandsType.None)),
            hideBottomLine = i == self._diyCabinetNum,
        }})
    end
    self._tipsHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtTipsAnchor)
end

function CollectionRoomPanel:_HideTips()
    if self._tipsHandle then
        Module.CommonTips:RemoveAssembledTips(self._tipsHandle, self._wtTipsAnchor)
        self._tipsHandle = nil
    end
end

function CollectionRoomPanel:_OnUpgradeBtnClicked()
    local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    if deviceData then
        Facade.UIManager:AsyncShowUI(UIName2ID.WBP_BlackSite_Upgrade, nil, nil, deviceData)
    end
end

function CollectionRoomPanel:_RegisterReddot()
    self._reddotIns =
    Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.CollectionRoom, ECollectionRoomDynamicDataType.PutIn, self._fCheckReddot, nil, self._wtInteractionTip, {EReddotType.Normal})
end

function CollectionRoomPanel:_fCheckReddot()
    local targetCameraId = Module.CollectionRoom.Field.mainPanelTargetCameraId
    if targetCameraId == 1 then
        return CollectionRoomLogic.JudgeHaveItemCanShevle(EShowCabinetType.Special)
    elseif targetCameraId == 2 then
        return CollectionRoomLogic.JudgeHaveItemCanShevle(EShowCabinetType.Display)
    else
        return false
    end
end

--region 手柄适配
function CollectionRoomPanel:_EnableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RegisterNavGroup(false)
    self:_BindDisplayAction()

    self._wtEnterCollectionRoomBtn:SetDisplayInputAction("CM_Enter", true, nil, true)
    -- lt/rt
    local WBP_GamepadKeyIconBox_Left = self:Wnd("WBP_CommonKeyIconBox_Left", HDKeyIconBox)
    local WBP_GamepadKeyIconBox_Right = self:Wnd("WBP_CommonKeyIconBox_Right", HDKeyIconBox)
    self.WBP_GamepadKeyIconBox_Left:SetOnlyDisplayOnGamepad(true)
    self.WBP_GamepadKeyIconBox_Right :SetOnlyDisplayOnGamepad(true)
    self.WBP_GamepadKeyIconBox_Left:InitByDisplayInputActionName("CM_LastPage", true, 0.0, false)
    self.WBP_GamepadKeyIconBox_Right :InitByDisplayInputActionName("CM_NextPage", true, 0.0, false)

    self:_UpdateSummaryList()
    self:_EnableInputTypeChangedHandle(true)
    
end

function CollectionRoomPanel:_DisableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RemoveNavGroup()
    self:_UnBindDisplayAction()
    self:_EnableInputTypeChangedHandle(false)

end

function CollectionRoomPanel:_RegisterNavGroup(bIsShowMusicPlayer)
    if not IsHD() then
        return
    end
    if self._navGroup then
        self:_RemoveNavGroup()
    end
    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootPanel, self, "Hittest")
    end

    self._navGroup:AddNavWidgetToArray(self._wtInteractionTip)
    self._navGroup:SetNavSelectorWidgetVisibility(true)

    if bIsShowMusicPlayer then
        
        local tempWidget = self._wtMusicPlayer:Wnd("DFCanvasPanel_2",UIWidgetBase)
        if not self._navGroupMusicPlayer then
            self._navGroupMusicPlayer = WidgetUtil.RegisterNavigationGroup(tempWidget, self, "Hittest")
        end
        self._navGroupMusicPlayer:AddNavWidgetToArray(tempWidget)
        self._navGroupMusicPlayer:SetNavSelectorWidgetVisibility(false)
        -- self._navGroupMusicPlayer.OnNavGroupFocusLostEvent:Add(self._OnNavGrouMusicPlayerFocusLost, self)
        -- self._navGroup:AddNavWidgetToArray(self._wtMusicPlayer)
    end
end

-- function CollectionRoomPanel:_OnNavGrouMusicPlayerFocusLost()
--     self:_RegisterNavGroup()
--     InventoryNavManager.FocusWithTimer(self._wtInteractionTip,0.6)
-- end

function CollectionRoomPanel:_RemoveNavGroup()
    if not IsHD() then
        return 
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._navGroup = nil
    self._navGroupMusicPlayer = nil
end

function CollectionRoomPanel:_UpdateSummaryList()
    if not IsHD() then
        return 
    end

    if WidgetUtil.IsGamepad() then
        local summaryList = {}
        if self._navGroupMusicPlayer then 
            table.insert(summaryList, {actionName = "Select_Gamepad", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
        else
            table.insert(summaryList, {actionName = "CM_Edit", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        if self._bShowUpgrade_Gamepad then
            table.insert(summaryList, {actionName = "CM_Upgrade", func = self._OnUpgradeBtnClicked, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        table.insert(summaryList, {actionName = "CM_BGM_Manage", func = self._OpenMusicPlayer_Gamepad, caller = self ,bUIOnly = false, bHideIcon = false})
        table.insert(summaryList, {actionName = "Common_ToggleTip", func = nil, caller = self ,bUIOnly = false, bHideIcon = false})
        Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false)
    else
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end

function CollectionRoomPanel:_BindDisplayAction()
    if not IsHD() then
        return 
    end

    if self._displayInputHandles then
        self:_UnBindDisplayAction()
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    -- X键进入收藏室
    local handle = inputMonitor:AddDisplayActionBinding("CM_Enter", EInputEvent.IE_Pressed, self._EnterCollectionRoomBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- RS提示
    handle = inputMonitor:AddDisplayActionBinding("Common_ToggleTip", EInputEvent.IE_Pressed, self.FilpTips, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- LT切换至左侧页面
    handle = inputMonitor:AddDisplayActionBinding("CM_LastPage", EInputEvent.IE_Pressed, self._OnLastMainCameraBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)
    -- RT切换至右侧页面
    handle = inputMonitor:AddDisplayActionBinding("CM_NextPage", EInputEvent.IE_Pressed, self._OnNextMainCameraBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
    table.insert(self._displayInputHandles, handle)

end

function CollectionRoomPanel:_UnBindDisplayAction()
    if not IsHD() then
        return 
    end
    local inputMonitor = Facade.UIManager:GetInputMonitor()

    if self._displayInputHandles then
        for i, handle in ipairs(self._displayInputHandles) do
            inputMonitor:RemoveDisplayActoinBingingForHandle(handle)
        end
        self._displayInputHandles = {}
    end
end

-- 手柄功能接口
-- 切换tips
function CollectionRoomPanel:FilpTips()
    if self._tipsHandle then
        self:_HideTips()
    else
        self:_ShowTips()
    end
end

function CollectionRoomPanel:OnAnimStarted(animation)
    PrintCollectionRoomUILifeLog("info", "CollectionRoomPanel:OnAnimStarted", animation)
end

function CollectionRoomPanel:OnAnimFinished(animation)
    if animation == self.WBP_CollectionHome_Main_in_1 or animation == self.WBP_CollectionHome_Main_in then
        InventoryNavManager.FocusWithTimer(self._wtInteractionTip)
    end
end

-- 绑定输入类型切换事件
function CollectionRoomPanel:_EnableInputTypeChangedHandle(bEnable)
    if not IsHD() then
        return 
    end

    if bEnable then
        if not self._onNotifyInputTypeChangedHandle then
            self._onNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
        end
    else
        if self._onNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._onNotifyInputTypeChangedHandle)
            self._onNotifyInputTypeChangedHandle = nil
        end
    end
end

-- 切换到非手柄时直接隐藏Tip
function CollectionRoomPanel:_HandleInputTypeChanged(inputType)
    if not IsHD() then
        return 
    end
    self:_UpdateSummaryList()
end

function CollectionRoomPanel:_OpenMusicPlayer_Gamepad()
    self._wtMusicPlayer:SwitchState(false)
    self:_RegisterNavGroup(true)
    if self._overrideBackHandle == nil then
        self._overrideBackHandle = self:AddInputActionBinding("Back", EInputEvent.IE_Pressed, self._CloseMusicPlayer_Gamepad, self, EDisplayInputActionPriority.UI_Pop)
    end
    self:_UpdateSummaryList()
end

function CollectionRoomPanel:_CloseMusicPlayer_Gamepad()
    self:_RegisterNavGroup(false)
    self:_UpdateSummaryList()
    if self._overrideBackHandle then
        self:RemoveInputActionBinding(self._overrideBackHandle)
        self._overrideBackHandle = nil
    end
    self._wtMusicPlayer:SwitchState(true)
end

function CollectionRoomPanel:_OnInteractionTipFocusReceived()
    -- she3：导航组失焦的回调用不了，she3版本只有这个按键会被导航，暂时先用他的聚焦事件
    if self._navGroupMusicPlayer then
        self:_CloseMusicPlayer_Gamepad()
    end
end

function CollectionRoomPanel:_OnMusicPlayerEndOpening()
    if WidgetUtil.IsGamepad() then
        InventoryNavManager.FocusWithTimer(self._wtMusicPlayer:GetDefaultFocusWidget())
    end
end

function CollectionRoomPanel:_OnMusicPlayerStartClosing()
    if WidgetUtil.IsGamepad() then
        InventoryNavManager.FocusWithTimer(self._wtInteractionTip)
    end
end
--endregion

return CollectionRoomPanel