----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class QuestSeasonEntranceSub : LuaUIBaseView

local QuestSeasonEntranceSub = ui("QuestSeasonEntranceSub")
function QuestSeasonEntranceSub:Ctor()

    self._wtTitleText = self:Wnd("DFTextBlock_55", UITextBlock)
    self._wtProgressText = self:Wnd("DFTextBlock_112", UITextBlock)
    self._wtTipText = self:Wnd("DFTextBlock", UITextBlock)

    self._wtLockPanel = self:Wnd("WBP_SlotCompMaskLock", UIWidgetBase)
    self._wtContentPanel = self:Wnd("DFCanvas_Content", UIWidgetBase)
    self._wtBtn = self:Wnd("DFBtn", UIButton)
    if self._wtBtn then 
        self._wtBtn:Event("OnClicked", self._OnButtonClicked, self)
    end

    self._wtBgImg = self:Wnd("DFImage_Icon", UIImage)

    self._bisCollection = nil
    self._collectorData = nil
    self._bIsLocked = nil
    self._openLevel = nil

end

function QuestSeasonEntranceSub:OnHide()
    self:RemoveAllLuaEvent()
    if self.curReddotProxy ~= nil then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Quest,EQuestDynamicDataType.EQuestSeasonReddot,self.curReddotProxy)
        self.curReddotProxy = nil
    end
end

function QuestSeasonEntranceSub:InitInfo(bisCollection, lineInfo)
    self._bisCollection = bisCollection
    if bisCollection then
        self._wtTitleText:SetText(Module.Quest.Config.Loc.QuestSeasonCollection)
        self._collectorData = Server.QuestServer:GetCollectorDataInfo()
        if self._collectorData then
            local finish = self._collectorData:GetCollectorProgress()
            local total = 0
            for key, value in pairs(lineInfo._overConditionList) do
                if key == Server.QuestServer.EQuestSeasonConditionType.Collection then
                    total = value.param
                    break
                end
            end
            self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonCollectionProgress,finish,total))
            self:_OnRefreshTimePanel()
            self:AddLuaEvent(Server.QuestServer.Events.evtQuestSeasonCollectorTimeRefresh, self._OnRefreshTimePanel, self)
        else
            logerror("QuestSeasonEntranceSub: No Valid Collector Data")
        end
    else
        self._wtTitleText:SetText(Module.Quest.Config.Loc.QuestSeasonContract)
        local count, total, name, selectIndex = Server.QuestServer._questFactData:GetFactProgress()
        self._wtProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonContractProgress,count,total))
        if selectIndex == nil then
            self._wtTipText:SetText(Module.Quest.Config.Loc.QuestSeasonContractNone)
        else
            self._wtTipText:SetText(name)
        end

    end
    self:_RegisterReddot()
end

function QuestSeasonEntranceSub:SetIsLocked(bIsLocked, levelNum)
    self._bIsLocked = bIsLocked
    self._openLevel = levelNum
    if bIsLocked then 
        self._wtLockPanel:SelfHitTestInvisible()
        self._wtContentPanel:Collapsed()
        self._wtLockPanel:SetDescText(string.format(Module.Quest.Config.Loc.LvlLimitPureTxt, levelNum))
    else
        self._wtLockPanel:Collapsed()
        self._wtContentPanel:Visible()
    end
end

function QuestSeasonEntranceSub:_OnButtonClicked()
    
    if self._bIsLocked then
        Module.CommonTips:ShowSimpleTip(string.format(Module.Quest.Config.Loc.QuestSeasonLocked, self._openLevel))
        return 
    end

    if self._bisCollection == true then 
        Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonCollectionPanel)
    else
        Facade.UIManager:AsyncShowUI(UIName2ID.QuestSeasonFactContractPanel)
    end
end

function QuestSeasonEntranceSub:_OnRefreshTimePanel()
    if self._collectorData == nil then
        return
    end
    local delta = self._collectorData:GetAutoRefreshTimeDelta()
    local str = ""
    if delta > 0 then
        local day, hour, min, sec = TimeUtil.GetSecondsFormatDDHHMMSS(delta)
        if day > 0 then
            str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeDay, day, hour, min)
        elseif hour > 0 then
            str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeHour, hour, min)
        elseif min > 0 then
            str = string.format(Module.Quest.Config.Loc.TaskLineRefreshTimeMin, sec > 0 and min + 1 or min)
        end
        str = str .. Module.Quest.Config.Loc.QuestSeasonCollectionRefresh
        self._wtTipText:SetText(str)
    else
        Server.QuestServer:CollectorTaskRefreshReq()
    end
end

function QuestSeasonEntranceSub:SetBgImg(path)
    self._wtBgImg:AsyncSetImagePath(path)
end

function QuestSeasonEntranceSub:_RegisterReddot()
    if self.curReddotProxy == nil then
        local fCheckReddot = function ()
            if self._bIsLocked then 
                return false
            end
            local needRet = false
            if self._bisCollection then
                needRet = self._collectorData:IsCanGainRewardExist()
            else
                needRet = Server.QuestServer._questFactData:IsCanGainRewardExist()
            end

            return needRet
        end
        local styleParams = {
            placeMode = EReddotPlaceMode.Custom,
            placeOffset = FVector2D(0, 12)
        }
        self.curReddotProxy = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Quest,
            EQuestDynamicDataType.EQuestSeasonReddot,fCheckReddot,nil,self,styleParams)
    end
end

return QuestSeasonEntranceSub