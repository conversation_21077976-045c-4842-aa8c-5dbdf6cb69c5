----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPlayerReturn)
----- LOG FUNCTION AUTO GENERATE END -----------

local ActivityHafkIntelligence = require "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityHafkIntelligence"
local ActivityConfig = Module.Activity.Config
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local IPlayerReturnSubActivityPanel = require "DFM.Business.Module.PlayerReturnModule.UI.SubPanels.IPlayerReturnSubActivityPanel"
local UKismetInputLibrary = import "KismetInputLibrary"
local ActivityFinalRewardView = require "DFM.Business.Module.ActivityModule.UI.Moss.ActivityFinalRewardView"
local ActivityClaimAllBtn = require "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityClaimAllBtn"
local ActivityTaskRefreshTime = require "DFM.Business.Module.ActivityModule.UI.CommonUI.ActivityTaskRefreshTime"
local NavigationAgent = require "DFM.Business.DataStruct.Common.Agent.NavigationAgent"
local IPlayerReturnSubActivity      = require "DFM.Business.Module.PlayerReturnModule.SubActivities.IPlayerReturnSubActivity"
local InputBindingAgent = require "DFM.Business.DataStruct.Common.Agent.InputBindingAgent"
local ScrollViewLength = 7
local ActivityGrowthRewardList      = require "DFM.Business.Module.ActivityModule.UI.Milestone.ActivityGrowthRewardList"

---@class PlayerReturnMilestonePanel : IPlayerReturnSubActivityPanel
local PlayerReturnMilestonePanel = ui("PlayerReturnMilestonePanel", IPlayerReturnSubActivityPanel)

function PlayerReturnMilestonePanel:Ctor()
    self._activityID = 0
    self._milestonesReward = {}
    self._allTask = {}
    self._task = {}
    self._taskSOL = {}
    self._taskMP = {}

    self._navMgr = NavigationAgent.Create(self)

    --SOL/MP标签
    self._tabIndex = 1
    self._wtTabGroupBox = UIUtil.WndTabGroupBox(self, "WBP_Activity_Tab3", self._OnGroupBoxCount, self._OnGroupBoxWidget, self._OnGroupBoxIndexChanged)
    self._wtTabListReddot = {}
    
    --最高奖励
    self._wtMaxRewardPanel = self:Wnd("WBP_Activity_FinalReward_C_0", ActivityFinalRewardView) ---@type ActivityFinalRewardView
    
    --任务刷新倒计时
    self._wtTaskRefreshTime = self:Wnd("WBP_Activity_TaskTime", ActivityTaskRefreshTime)

    --里程碑货币
    self._wtCurrencyImg = self:Wnd("DFImage_546", UIImage)
    self._wtCurrencyTitle = self:Wnd("DFTextBlock_1", UITextBlock)
    self._wtCurrencytxt = self:Wnd("DFTextBlock_92", UITextBlock)

    --任务滚动框
    self._wtTaskScrollBox = UIUtil.WndScrollGridBox(self, "DFScrollGridBox_59", self._OnGetCurrencyCount, self._OnProcessCurrencyWidget)
    --里程碑奖励滚动框
    self._wtRewardScrollBox = UIUtil.WndScrollGridBox(self, "DFScrollGridBox_164", self._OnGetRewardCount, self._OnProcessRewardWidget)
    self._oldPosition = 0
    self._leftPosition = 0
    self._rightPosition = 0

    --大奖框
    self._wtFinalRewardContainer = self:Wnd("DFCanvasPosReContainer_2", UIWidgetBase)
    --右侧面板Canvas
    self._wtRightContentsPanel   = self:Wnd("DFCanvasPanel_84", UIWidgetBase)

    --一键领取按钮
    self._wtAllClaimBtn = self:Wnd("WBP_Activity_ClaimAllBtn", ActivityClaimAllBtn)

    --禁用滚动控件集合
    self._wtTaskPaddingBox = self:Wnd("DFCanvasPosReContainer_3", UIWidgetBase)
    self._wtRewardPaddingBox = self:Wnd("PlatformPaddingBox_0", UIWidgetBase)
    self._wtHotzone = {
        self._wtTaskScrollBox,
        self._wtRewardPaddingBox,
    }
    self:InjectLua()

    if IsHD() then
        self._inputMgr = InputBindingAgent.New(self)
        self._inputMgr:AddSummary(
            "ChooseFinalReward",
            {
                actionName   = "Common_ButtonTop_Gamepad",
                displayName  = ActivityConfig.Loc.ChooseFinalReward,
                optionalStr  = ActivityConfig.Loc.ChooseFinalReward,
                func         = self._OnChooseFinalReward,
                caller       = self,
            },
            true,           -- 初始仅注册不启用
            false           -- 不更新BottomBar
        )

        self._wtAllClaimBtn._wtAllClaimBtn:SetDisplayInputAction("Common_ButtonLeft_Gamepad", false, nil, true)
    end
end

---@param itemWidget ActivityGrowthRewardList
function PlayerReturnMilestonePanel:_OnProcessRewardWidget(position, itemWidget)
    ActivityHafkIntelligence._OnProcessRewardWidget(self, position, itemWidget)

    -- C index to Lua index
    position = position + 1
    
    -- 除最终奖励，列表最后一项调用 SetLastLength
    local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
    if not activityInfo then return end
    if (#activityInfo.milestone_award - 1) == position then
        itemWidget:SetLastLength(1)
    else
        itemWidget:SetLastLength(0)
    end
end

PlayerReturnMilestonePanel.OnShow = ActivityHafkIntelligence.OnShow
PlayerReturnMilestonePanel.SetDefaultFocus = ActivityHafkIntelligence.SetDefaultFocus
PlayerReturnMilestonePanel.OnShowBegin = ActivityHafkIntelligence.OnShowBegin
PlayerReturnMilestonePanel.RefreshInputSummary = ActivityHafkIntelligence.RefreshInputSummary
PlayerReturnMilestonePanel._AllowChooseFinalReward = ActivityHafkIntelligence._AllowChooseFinalReward
PlayerReturnMilestonePanel._OnChooseFinalReward = ActivityHafkIntelligence._OnChooseFinalReward
PlayerReturnMilestonePanel._InitEvent = ActivityHafkIntelligence._InitEvent
PlayerReturnMilestonePanel._RemoveEvent = ActivityHafkIntelligence._RemoveEvent
PlayerReturnMilestonePanel.OnInitExtraData = ActivityHafkIntelligence.OnInitExtraData
PlayerReturnMilestonePanel.OnHideBegin = ActivityHafkIntelligence.OnHideBegin
PlayerReturnMilestonePanel.OnClose = ActivityHafkIntelligence.OnClose
PlayerReturnMilestonePanel._MoveToRewardIndex = ActivityHafkIntelligence._MoveToRewardIndex
PlayerReturnMilestonePanel.RefreshUI = ActivityHafkIntelligence.RefreshUI
PlayerReturnMilestonePanel._InitTaskByModel = ActivityHafkIntelligence._InitTaskByModel
PlayerReturnMilestonePanel.RefreshCurrency = ActivityHafkIntelligence.RefreshCurrency
PlayerReturnMilestonePanel.RefreshActivityTime = ActivityHafkIntelligence.RefreshActivityTime
PlayerReturnMilestonePanel.RefreshFinalReward = ActivityHafkIntelligence.RefreshFinalReward
PlayerReturnMilestonePanel.RefreshTaskReddot = ActivityHafkIntelligence.RefreshTaskReddot
PlayerReturnMilestonePanel.RefreshTabReddot = ActivityHafkIntelligence.RefreshTabReddot
PlayerReturnMilestonePanel._SetClaimBtnType = ActivityHafkIntelligence._SetClaimBtnType
PlayerReturnMilestonePanel._OnGroupBoxCount = ActivityHafkIntelligence._OnGroupBoxCount
PlayerReturnMilestonePanel._OnGroupBoxWidget = ActivityHafkIntelligence._OnGroupBoxWidget
PlayerReturnMilestonePanel._OnGroupBoxIndexChanged = ActivityHafkIntelligence._OnGroupBoxIndexChanged
PlayerReturnMilestonePanel._OnGetCurrencyCount = ActivityHafkIntelligence._OnGetCurrencyCount
PlayerReturnMilestonePanel._OnProcessCurrencyWidget = ActivityHafkIntelligence._OnProcessCurrencyWidget
PlayerReturnMilestonePanel._OnGetRewardCount = ActivityHafkIntelligence._OnGetRewardCount
PlayerReturnMilestonePanel._OnFixViewItemCount = ActivityHafkIntelligence._OnFixViewItemCount
PlayerReturnMilestonePanel._OnLeftbtnClick = ActivityHafkIntelligence._OnLeftbtnClick
PlayerReturnMilestonePanel._OnRightbtnClick = ActivityHafkIntelligence._OnRightbtnClick
PlayerReturnMilestonePanel._OnLeftbtnPress = ActivityHafkIntelligence._OnLeftbtnPress
PlayerReturnMilestonePanel._OnRightbtnPress = ActivityHafkIntelligence._OnRightbtnPress

function PlayerReturnMilestonePanel:OnPlayerReturnEnter(from)
    IPlayerReturnSubActivityPanel.OnPlayerReturnEnter(self, from)
    self:RefreshUI()
end

function PlayerReturnMilestonePanel:OnShowBegin()
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    local actv = IPlayerReturnSubActivity.Get(ReturnActivityType.ReturnActivityTypeNormalTask, armedForceMode)
    local activityInfo = actv:GetActivityInfo()
    if activityInfo then
        self._activityID = activityInfo.actv_id
    else
        self._activityID = nil
        return
    end

    self._wtTitle:SetText(activityInfo.name)
    ActivityHafkIntelligence.OnShowBegin(self)

    if not self:IsUIActive() then
        if self._inputMgr then
            self._inputMgr:DeactivateAll()
        end
    end
end


return PlayerReturnMilestonePanel