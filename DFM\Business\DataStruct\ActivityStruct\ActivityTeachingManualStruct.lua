---@class ActivityTeachingManualStruct : LuaObject
-- ActivityTeachingManualStruct
ActivityTeachingManualStruct = class('ActivityTeachingManualStruct', LuaObject)

---@param ActivityQuestionStruct AT
function ActivityTeachingManualStruct:Ctor(ActivityTeachingManual)
    self:InitActivityStruct(ActivityTeachingManual)
end

---@param ActivityQuestionStruct AT
function ActivityTeachingManualStruct:InitActivityStruct(ActivityTeachingManual)
    self.ID = ActivityTeachingManual.ID or 0
    self.Text = ActivityTeachingManual.Text or ""
    self.Picture = ActivityTeachingManual.Picture or ""
end

return ActivityTeachingManualStruct