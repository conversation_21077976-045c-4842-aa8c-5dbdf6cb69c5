----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class WeaponUpgradeItem : LuaUIBaseView
--武器item
local WeaponUpgradeItem = ui("WeaponUpgradeItem")
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPInputType = import"EGPInputType"

function WeaponUpgradeItem:Ctor()
    --等级
    self._wtLevelTxt = self:Wnd("wLvlText", UITextBlock)
    --容器
    self._wtAdapterBox = self:Wnd("wAdapterBox", UIWidgetBase)
end

function WeaponUpgradeItem:OnOpen()
end

function WeaponUpgradeItem:InitItemData(callBack, curLevel, weaponItem)
    if callBack == nil or weaponItem == nil or curLevel == nil then
        logerror("weaponItem is nil, curLevel is nil")
        return
    end
    --父类对象池函数
    self._callBack = callBack
    --选中数据
    self._curLevel = curLevel
    --武器数据
    self._weaponItem = weaponItem
    --回收配件
    self:_GetPropItem(0)
    --是否解锁显示
    local curWeaponLvl = weaponItem:GetFeature(EFeatureType.Weapon):GetWeaponLevel()
    --是否解锁
    local unLock = curLevel.Level <= curWeaponLvl
    self:BpSetSelected(unLock and 0 or 1)
    --显示等级
    self._wtLevelTxt:SetText(curLevel.Level)
    --初始化数据
    for _, itemId in ipairs(curLevel.UnlockParts or {}) do
        --获取配件数据
        local accessory = Server.InventoryServer:GetItemsById(tonumber(itemId), ESlotGroup.MPApply)
        local accessoryData = accessory and accessory[1] or nil
        if accessoryData == nil then
			accessoryData = ItemBase:New(tonumber(itemId))
		end
        --获取配件蓝图
        local itemView = self:_GetPropItem(1)
        --初始化数据
        if itemView then

            -- BEGIN MODIFICATION @ VIRTUOS : 在刷新Item的时候将其添加至手柄的导航的事件
            if IsHD() then
                Module.Gunsmith.Config.Events.evtOnGunsmithUpgradeItemUIInitItemData:Invoke(itemView)
            end
            -- END MODIFICATION

            --修改item元素的DefaultSize尺寸
            if DFHD_LUA == 1 then
                itemView:SetRootSize(256, 256)
            else
                itemView:SetRootSize(192, 192)
            end
            itemView:InitItem(accessoryData)
            --是否解锁蒙板
            local lockMask = itemView:FindOrAdd(EComp.ItemMark, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
            --进一步判断配件是否解锁:通过MP仓库判断
            if weaponItem.id and itemId and unLock == false then
                unLock = Server.InventoryServer:IsMPWeaponSvrUnLockPart(weaponItem.id, tonumber(itemId))
            end
            --配件蒙板设置
            if unLock then
                lockMask:SetVisibility(ESlateVisibility.Collapsed)
            else
                lockMask:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            end
            itemView.itemId = itemId
            itemView.Level = curLevel.Level
            --初始化白框
            self:_SetWeaponModuleWhiteFrame(itemView)
            -- 设置标识
            local bIsLockPart = Module.Gunsmith.Field:IsUpgradeLockPart(itemId)
            self:_SetUsingYellowComponent(itemView, bIsLockPart)
            if self._callBack.GetItemId then
                local id = self._callBack.GetItemId()
                if id == itemId then
                    Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked:Invoke(nil, itemView)
                end
            end
            itemView:BindCustomOnClicked(function()
                --配件详情显示事件/白框显示事件
                Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked:Invoke(accessoryData, itemView)
            end)
            --判断是否需要选中
            if Module.Gunsmith.Field:IsWeaponPartId(curLevel.Level, itemId) then
                Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked:Invoke(accessoryData, itemView)
            end
            --添加悬浮/方便适配手柄
            if IsHD() and itemView.SetCppValue and itemView.Event then
                itemView:SetCppValue("bHandleHover", true)
                itemView:Event("OnHovered", self._OnButtonHovered, self, accessoryData, itemView)
                itemView:Event("OnUnhovered", self._OnButtonHovered, self)
            end
        end
    end
end

function WeaponUpgradeItem:_OnButtonHovered(accessoryData, itemView)
    if WidgetUtil.GetCurrentInputType() == EGPInputType.Gamepad then
        if self._curLevel and self._curLevel.Level ~= 1 then
            Module.Gunsmith.Config.Events.evtGunsmithRefreshAccessoryDetailsClicked:Invoke(accessoryData, itemView)
            if itemView == nil then
                Module.Gunsmith.Config.Events.evtWeaponToOneItemView:Invoke()
            end
        end
    end
end

--设置白框
function WeaponUpgradeItem:_SetWeaponModuleWhiteFrame(itemView)
    if itemView == nil then
        return
    end
    local whiteFrame = itemView:FindOrAdd(EComp.MissionItem, UIName2ID.IVItemSelectedComponent, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
    if whiteFrame then
        whiteFrame:SetVisibility(ESlateVisibility.Collapsed)
    end
end

--设置标识
function WeaponUpgradeItem:_SetUsingYellowComponent(itemView, bIsLockPart)
    if itemView == nil then
        return
    end
    local usingYellowComponent = itemView:FindOrAdd(EComp.TopLeftNewTag, UIName2ID.IVUsingYellowComponent, EIVSlotPos.TopLeft, EIVCompOrder.Order1)
    if usingYellowComponent then
        if bIsLockPart then
            usingYellowComponent:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        else
            usingYellowComponent:SetVisibility(ESlateVisibility.Collapsed)
        end
    end
end

function WeaponUpgradeItem:OnClose()
    self:_GetPropItem()
end

function WeaponUpgradeItem:_GetPropItem(type)
    if self._callBack and self._callBack.GetPropItem then
        return self._callBack.GetPropItem(UIName2ID.IVCommonItemTemplate, self._wtAdapterBox, type)
    end
end


return WeaponUpgradeItem