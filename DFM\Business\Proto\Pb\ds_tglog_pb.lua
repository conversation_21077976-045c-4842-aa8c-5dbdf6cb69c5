require("DFM.Business.Proto.Pb.tglog_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.ds_tglog_editor_pb"
end

pb.__pb_DsMPRoundFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    EscapeFailReason = 0,
    MatchUUID = "",
    KillAILabCount = 0,
    KilledByAILabCount = 0,
    DamageTake = 0,
    DamageMake = 0,
    DamageTakeAsVehicle = 0,
    DamageMakeAsVehicle = 0,
    DamageTakeAsInfantry = 0,
    DamageMakeAsInfantry = 0,
    MovingTime = 0,
    MovingDistance = 0,
    KillByHeadShotCount = 0,
    KilledByHeadShotCount = 0,
    PassiveHealingTriggerCount = 0,
    PassiveHealingEndCount = 0,
    PassiveHealingEndReason = "",
    TotalVehicleDestroyed = 0,
    AssistCount = 0,
    BeSaveNum = 0,
    RescueNum = 0,
}
pb.__pb_DsMPRoundFlow.__name = "DsMPRoundFlow"
pb.__pb_DsMPRoundFlow.__index = pb.__pb_DsMPRoundFlow
pb.__pb_DsMPRoundFlow.__pairs = __pb_pairs

pb.DsMPRoundFlow = { __name = "DsMPRoundFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMPRoundFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public EscapeFailReason number
---@field public MatchUUID string
---@field public KillAILabCount number
---@field public KilledByAILabCount number
---@field public DamageTake number
---@field public DamageMake number
---@field public DamageTakeAsVehicle number
---@field public DamageMakeAsVehicle number
---@field public DamageTakeAsInfantry number
---@field public DamageMakeAsInfantry number
---@field public MovingTime number
---@field public MovingDistance number
---@field public KillByHeadShotCount number
---@field public KilledByHeadShotCount number
---@field public PassiveHealingTriggerCount number
---@field public PassiveHealingEndCount number
---@field public PassiveHealingEndReason string
---@field public TotalVehicleDestroyed number
---@field public AssistCount number
---@field public BeSaveNum number
---@field public RescueNum number

---@return pb_DsMPRoundFlow
function pb.DsMPRoundFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMPVehicleFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    VehicleID = 0,
    VehicleItemID = 0,
    Level = 0,
    UsedTime = 0,
    Killplayer = 0,
    AssistKillplayer = 0,
    KillVehicle = 0,
    AssistKillVehicle = 0,
    PartList = "",
}
pb.__pb_DsMPVehicleFlow.__name = "DsMPVehicleFlow"
pb.__pb_DsMPVehicleFlow.__index = pb.__pb_DsMPVehicleFlow
pb.__pb_DsMPVehicleFlow.__pairs = __pb_pairs

pb.DsMPVehicleFlow = { __name = "DsMPVehicleFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMPVehicleFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public VehicleID number
---@field public VehicleItemID number
---@field public Level number
---@field public UsedTime number
---@field public Killplayer number
---@field public AssistKillplayer number
---@field public KillVehicle number
---@field public AssistKillVehicle number
---@field public PartList string

---@return pb_DsMPVehicleFlow
function pb.DsMPVehicleFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsRoundFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    HasBlueBox = 0,
    FireNum = 0,
    HitNum = 0,
    TotalDamage = 0,
    TotalMileage = 0,
    CarryoutAIPrice = 0,
    CarryoutAILabPrice = 0,
    CarryoutBossPrice = 0,
    CarryoutContainPrice = 0,
    CarryoutLootPrice = 0,
    CarryoutEnemyPrice = 0,
    CarryoutSelfPrice = 0,
    CarryoutSafeBoxPrice = 0,
    CarryoutGridNum = 0,
    CarryoutTotalPrice = 0,
    LootTotalNum = 0,
    MatchType = 0,
    MatchPool = 0,
    EscapeFailReason = 0,
    ArmedForceId = 0,
    TotalCurrency = 0,
    CurrencyFromEnemy = 0,
    CurrencyFromTeammate = 0,
    CurrencyFromQuest = 0,
    BlueprintHumanFightCount = 0,
    BlueprintAiFightCount = 0,
    BlueprintLifetime = 0,
    LootEncounterNum = 0,
    MatchUUID = "",
    SelfDisconnectNum = 0,
    TeamDisconnectNum = 0,
    KeyChainCarryInPrice = 0,
    KeyChainCarryOutPrice = 0,
    FinalPrice = 0,
    OriginalEquipmentPriceWithoutKeyChain = 0,
    TotalFightCnt = 0,
    TotalFightDuration = 0,
    TotalFightVariance = 0,
    KillsInFighting = 0,
    FightingTeamCnt = 0,
    FightingInterval = 0,
    FightingIntervalVariance = 0,
    FightingPlayerCnt = 0,
    GetDebuffInFightingCnt = 0,
    ArmorReduceInFighting = 0,
    HPHealInFighting = 0,
    CarryOutPVEGridNum = 0,
    CarryOutProfitPrice = 0,
    AssistCnt = 0,
    CarryOutForTeammatePrice = 0,
    BeSaveNum = 0,
    RescueNum = 0,
    DeathReason = 0,
    AccidentType = 0,
    KillerID = 0,
    KillerName = "",
    KillerWeaponItemID = 0,
    KillerBulletItemID = 0,
    KillerBulletLevel = 0,
    DeathCharacterParts = "",
    DeathHelmetHealth = 0,
    DeathArmorHealth = 0,
    DeathHelmetMaxHealth = 0,
    DeathArmorMaxHealth = 0,
    DeathHelmetItemID = 0,
    DeathArmorItemID = 0,
    KillerSkillID = 0,
    KillerHeroID = 0,
    DeathHPDamage = 0,
    DeathArmorDamage = 0,
    LastShootDistance = 0,
    CarryOutGainPrice = 0,
    DropGroupEnableType = 0,
}
pb.__pb_DsRoundFlow.__name = "DsRoundFlow"
pb.__pb_DsRoundFlow.__index = pb.__pb_DsRoundFlow
pb.__pb_DsRoundFlow.__pairs = __pb_pairs

pb.DsRoundFlow = { __name = "DsRoundFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsRoundFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public HasBlueBox number
---@field public FireNum number
---@field public HitNum number
---@field public TotalDamage number
---@field public TotalMileage number
---@field public CarryoutAIPrice number
---@field public CarryoutAILabPrice number
---@field public CarryoutBossPrice number
---@field public CarryoutContainPrice number
---@field public CarryoutLootPrice number
---@field public CarryoutEnemyPrice number
---@field public CarryoutSelfPrice number
---@field public CarryoutSafeBoxPrice number
---@field public CarryoutGridNum number
---@field public CarryoutTotalPrice number
---@field public LootTotalNum number
---@field public MatchType number
---@field public MatchPool number
---@field public EscapeFailReason number
---@field public ArmedForceId number
---@field public TotalCurrency number
---@field public CurrencyFromEnemy number
---@field public CurrencyFromTeammate number
---@field public CurrencyFromQuest number
---@field public BlueprintHumanFightCount number
---@field public BlueprintAiFightCount number
---@field public BlueprintLifetime number
---@field public LootEncounterNum number
---@field public MatchUUID string
---@field public SelfDisconnectNum number
---@field public TeamDisconnectNum number
---@field public KeyChainCarryInPrice number
---@field public KeyChainCarryOutPrice number
---@field public FinalPrice number
---@field public OriginalEquipmentPriceWithoutKeyChain number
---@field public TotalFightCnt number
---@field public TotalFightDuration number
---@field public TotalFightVariance number
---@field public KillsInFighting number
---@field public FightingTeamCnt number
---@field public FightingInterval number
---@field public FightingIntervalVariance number
---@field public FightingPlayerCnt number
---@field public GetDebuffInFightingCnt number
---@field public ArmorReduceInFighting number
---@field public HPHealInFighting number
---@field public CarryOutPVEGridNum number
---@field public CarryOutProfitPrice number
---@field public AssistCnt number
---@field public CarryOutForTeammatePrice number
---@field public BeSaveNum number
---@field public RescueNum number
---@field public DeathReason number
---@field public AccidentType number
---@field public KillerID number
---@field public KillerName string
---@field public KillerWeaponItemID number
---@field public KillerBulletItemID number
---@field public KillerBulletLevel number
---@field public DeathCharacterParts string
---@field public DeathHelmetHealth number
---@field public DeathArmorHealth number
---@field public DeathHelmetMaxHealth number
---@field public DeathArmorMaxHealth number
---@field public DeathHelmetItemID number
---@field public DeathArmorItemID number
---@field public KillerSkillID number
---@field public KillerHeroID number
---@field public DeathHPDamage number
---@field public DeathArmorDamage number
---@field public LastShootDistance number
---@field public CarryOutGainPrice number
---@field public DropGroupEnableType number

---@return pb_DsRoundFlow
function pb.DsRoundFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsNeverConnectionFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    CountryCode = 0,
}
pb.__pb_DsNeverConnectionFlow.__name = "DsNeverConnectionFlow"
pb.__pb_DsNeverConnectionFlow.__index = pb.__pb_DsNeverConnectionFlow
pb.__pb_DsNeverConnectionFlow.__pairs = __pb_pairs

pb.DsNeverConnectionFlow = { __name = "DsNeverConnectionFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsNeverConnectionFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public CountryCode number

---@return pb_DsNeverConnectionFlow
function pb.DsNeverConnectionFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DeadDiscardItemFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    ItemID = 0,
    DeadDiscardNum = 0,
    DeadDiscardNum2 = 0,
}
pb.__pb_DeadDiscardItemFlow.__name = "DeadDiscardItemFlow"
pb.__pb_DeadDiscardItemFlow.__index = pb.__pb_DeadDiscardItemFlow
pb.__pb_DeadDiscardItemFlow.__pairs = __pb_pairs

pb.DeadDiscardItemFlow = { __name = "DeadDiscardItemFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DeadDiscardItemFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public ItemID number
---@field public DeadDiscardNum number
---@field public DeadDiscardNum2 number

---@return pb_DeadDiscardItemFlow
function pb.DeadDiscardItemFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PropDropFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    Source = 0,
    SubSource = 0,
    ItemID = 0,
    ItemName = "",
    Quality = 0,
    ItemNum = 0,
    SourceID = 0,
    AIEquipType = "",
    MapId = 0,
    AIDropList = "",
}
pb.__pb_PropDropFlow.__name = "PropDropFlow"
pb.__pb_PropDropFlow.__index = pb.__pb_PropDropFlow
pb.__pb_PropDropFlow.__pairs = __pb_pairs

pb.PropDropFlow = { __name = "PropDropFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PropDropFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public Source number
---@field public SubSource number
---@field public ItemID number
---@field public ItemName string
---@field public Quality number
---@field public ItemNum number
---@field public SourceID number
---@field public AIEquipType string
---@field public MapId number
---@field public AIDropList string

---@return pb_PropDropFlow
function pb.PropDropFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PLayerFightEvent = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AIGid = 0,
    RoomId = 0,
    EventID = 0,
    HitType = 0,
    IsKill = 0,
    DeBuffID = 0,
    HpChange = 0,
    ArmChange = 0,
    LootQuality = 0,
    GameTimeSecs = 0,
}
pb.__pb_PLayerFightEvent.__name = "PLayerFightEvent"
pb.__pb_PLayerFightEvent.__index = pb.__pb_PLayerFightEvent
pb.__pb_PLayerFightEvent.__pairs = __pb_pairs

pb.PLayerFightEvent = { __name = "PLayerFightEvent", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PLayerFightEvent : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AIGid number
---@field public RoomId number
---@field public EventID number
---@field public HitType number
---@field public IsKill number
---@field public DeBuffID number
---@field public HpChange number
---@field public ArmChange number
---@field public LootQuality number
---@field public GameTimeSecs number

---@return pb_PLayerFightEvent
function pb.PLayerFightEvent:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIFightFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    PlayerId = 0,
    AIGid = 0,
    RoomId = 0,
    POI = "",
    TimeStamp = 0,
    IsLeaveFight = 0,
    CurrState = 0,
    Reason = "",
    Distance = 0,
    LastState = 0,
    LastStateTimeStamp = 0,
}
pb.__pb_AIFightFlow.__name = "AIFightFlow"
pb.__pb_AIFightFlow.__index = pb.__pb_AIFightFlow
pb.__pb_AIFightFlow.__pairs = __pb_pairs

pb.AIFightFlow = { __name = "AIFightFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIFightFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public PlayerId number
---@field public AIGid number
---@field public RoomId number
---@field public POI string
---@field public TimeStamp number
---@field public IsLeaveFight number
---@field public CurrState number
---@field public Reason string
---@field public Distance number
---@field public LastState number
---@field public LastStateTimeStamp number

---@return pb_AIFightFlow
function pb.AIFightFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIDropFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    AIGid = 0,
    RoomId = 0,
    TotalValue = 0,
    IsPickUp = 0,
    PlayerId = 0,
}
pb.__pb_AIDropFlow.__name = "AIDropFlow"
pb.__pb_AIDropFlow.__index = pb.__pb_AIDropFlow
pb.__pb_AIDropFlow.__pairs = __pb_pairs

pb.AIDropFlow = { __name = "AIDropFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIDropFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public AIGid number
---@field public RoomId number
---@field public TotalValue number
---@field public IsPickUp number
---@field public PlayerId number

---@return pb_AIDropFlow
function pb.AIDropFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LootItemFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    Value = 0,
    ItemId = 0,
    ItemCount = 0,
    PlayerId = 0,
    AIGid = 0,
    SourcePos = 0,
    TargetPos = 0,
    Location = "",
    IsDrop = 0,
    LootSourceType = 0,
    LootSourceInfo = 0,
    Weight = 0,
    BigAreaName = "",
    ItemLocation = "",
}
pb.__pb_LootItemFlow.__name = "LootItemFlow"
pb.__pb_LootItemFlow.__index = pb.__pb_LootItemFlow
pb.__pb_LootItemFlow.__pairs = __pb_pairs

pb.LootItemFlow = { __name = "LootItemFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LootItemFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public Value number
---@field public ItemId number
---@field public ItemCount number
---@field public PlayerId number
---@field public AIGid number
---@field public SourcePos number
---@field public TargetPos number
---@field public Location string
---@field public IsDrop number
---@field public LootSourceType number
---@field public LootSourceInfo number
---@field public Weight number
---@field public BigAreaName string
---@field public ItemLocation string

---@return pb_LootItemFlow
function pb.LootItemFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIBaseInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AIGid = 0,
    RoomId = 0,
    MapID = 0,
    AIType = "",
    AILevel = 0,
    POI = "",
    AIStyle = "",
    BornLevelName = "",
    BornSeconds = 0,
    DSVersion = "",
    Tags = "",
    AILayoutName = "",
    TKVNormalTime = 0,
    TKVPrudentTime = 0,
    OverkillTime = 0,
    CamperTime = 0,
    OtherTime = 0,
    AIDropID = 0,
    AISpawnTemplate = "",
    MapName = "",
}
pb.__pb_AIBaseInfo.__name = "AIBaseInfo"
pb.__pb_AIBaseInfo.__index = pb.__pb_AIBaseInfo
pb.__pb_AIBaseInfo.__pairs = __pb_pairs

pb.AIBaseInfo = { __name = "AIBaseInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIBaseInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AIGid number
---@field public RoomId number
---@field public MapID number
---@field public AIType string
---@field public AILevel number
---@field public POI string
---@field public AIStyle string
---@field public BornLevelName string
---@field public BornSeconds number
---@field public DSVersion string
---@field public Tags string
---@field public AILayoutName string
---@field public TKVNormalTime number
---@field public TKVPrudentTime number
---@field public OverkillTime number
---@field public CamperTime number
---@field public OtherTime number
---@field public AIDropID number
---@field public AISpawnTemplate string
---@field public MapName string

---@return pb_AIBaseInfo
function pb.AIBaseInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIStuckInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AIGid = 0,
    RoomId = 0,
    MapID = 0,
    AIType = "",
    POI = "",
    Prefecture = "",
    StuckLoc = "",
}
pb.__pb_AIStuckInfo.__name = "AIStuckInfo"
pb.__pb_AIStuckInfo.__index = pb.__pb_AIStuckInfo
pb.__pb_AIStuckInfo.__pairs = __pb_pairs

pb.AIStuckInfo = { __name = "AIStuckInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIStuckInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AIGid number
---@field public RoomId number
---@field public MapID number
---@field public AIType string
---@field public POI string
---@field public Prefecture string
---@field public StuckLoc string

---@return pb_AIStuckInfo
function pb.AIStuckInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SOLTeamFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerGid = 0,
    RoomId = 0,
    MapID = 0,
    Teammate1Gid = 0,
    Teammate1Dist = 0,
    Teammate2Gid = 0,
    Teammate2Dist = 0,
    Timestamp = 0,
}
pb.__pb_SOLTeamFlow.__name = "SOLTeamFlow"
pb.__pb_SOLTeamFlow.__index = pb.__pb_SOLTeamFlow
pb.__pb_SOLTeamFlow.__pairs = __pb_pairs

pb.SOLTeamFlow = { __name = "SOLTeamFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SOLTeamFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerGid number
---@field public RoomId number
---@field public MapID number
---@field public Teammate1Gid number
---@field public Teammate1Dist number
---@field public Teammate2Gid number
---@field public Teammate2Dist number
---@field public Timestamp number

---@return pb_SOLTeamFlow
function pb.SOLTeamFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIFightDesignatedPlayer = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AIGid = 0,
    RoomId = 0,
    PlayerId = 0,
    FireNum = 0,
    HitNum = 0,
    HitHeadNum = 0,
    TotalDamage = 0,
    TotalDist = 0,
    KnockDownNum = 0,
    IsKill = 0,
    PlayerHitNum = 0,
    PlayerHitHeadNum = 0,
    PlayerTotalDamage = 0,
    PlayerTotalDist = 0,
    PlayerIsKill = 0,
    PlayerIsAssassinate = 0,
    PlayerIsFound = 0,
    TotalArmorDamage = 0,
    MapId = 0,
    HitThoraxNum = 0,
    HitRightArmNum = 0,
    HitLeftArmNum = 0,
    HitRightLegNum = 0,
    HitLeftLegNum = 0,
    HitFullBodyNum = 0,
    ArmorLevelWhenDie = 0,
    AIDifficultyPool = 0,
    DeathHitPart = 0,
    IsKnockDown = 0,
    DeathHitPartNew = "",
    AIHitPlayerTotalDist = 0,
    MovementMode = 0,
    MovementCustomMode = 0,
    DamageBeforeDeath = 0,
    PlayerShieldDamage = 0,
    PlayerTotalArmorDamage = 0,
    HitAbdomenNum = 0,
    HitHeadDamage = 0,
    HitThoraxDamage = 0,
    HitAbdomenDamage = 0,
    HitRightArmDamage = 0,
    HitLeftArmDamage = 0,
    HitRightLegDamage = 0,
    HitLeftLegDamage = 0,
    HitFullBodyDamage = 0,
    HitHeadArmorDamage = 0,
    HitThoraxArmorDamage = 0,
    HitAbdomenArmorDamage = 0,
    HitRightArmArmorDamage = 0,
    HitLeftArmArmorDamage = 0,
    HitRightLegArmorDamage = 0,
    HitLeftLegArmorDamage = 0,
    HitFullBodyArmorDamage = 0,
    TTK = 0,
    ShootedHelmetID = 0,
    ShootedArmorID = 0,
    HitBulletNumThroughSoftCover = 0,
    MapName = "",
    DSVersion = "",
    AILevel = 0,
    AIType = "",
    AISpawnTemplate = "",
    FractureCausedByDamage = 0,
    BleedingCausedByDamage = 0,
    ChestArmorBeforeFirstFireOnKill = 0,
    HeadArmorBeforeFirstFireOnKill = 0,
    HealthBeforeFirstFireOnKill = 0,
    DistanceToPlayerBeforeFirstFireOnKill = 0,
    DistanceOnKill = 0,
    TimeFromFirstFireToKill = 0,
    HighestDamagePerSingleFire = 0,
    FireCountDistribution_0_5M = 0,
    FireCountDistribution_5_10M = 0,
    FireCountDistribution_10_20M = 0,
    FireCountDistribution_20_30M = 0,
    FireCountDistribution_30_40M = 0,
    FireCountDistribution_40_50M = 0,
    FireCountDistribution_50_60M = 0,
    FireCountDistribution_60_70M = 0,
    FireCountDistribution_70_80M = 0,
    FireCountDistribution_80_90M = 0,
    FireCountDistribution_90_100M = 0,
    FireCountDistribution_100_150M = 0,
    FireCountDistribution_150_200M = 0,
    FireCountDistribution_Above200M = 0,
    HitCntDistribution_0_5M = 0,
    HitCntDistribution_5_10M = 0,
    HitCntDistribution_10_20M = 0,
    HitCntDistribution_20_30M = 0,
    HitCntDistribution_30_40M = 0,
    HitCntDistribution_40_50M = 0,
    HitCntDistribution_50_60M = 0,
    HitCntDistribution_60_70M = 0,
    HitCntDistribution_70_80M = 0,
    HitCntDistribution_80_90M = 0,
    HitCntDistribution_90_100M = 0,
    HitCntDistribution_100_150M = 0,
    HitCntDistribution_150_200M = 0,
    HitCntDistribution_Above200M = 0,
    ConsecutiveHits3TimesCount = 0,
    ConsecutiveHits3TimesMinTime = 0,
    ConsecutiveHits4TimesCount = 0,
    ConsecutiveHits4TimesMinTime = 0,
    ConsecutiveHitsGreaterThan4TimesCount = 0,
    ConsecutiveHitsGreaterThan4TimesMinTime = 0,
    ConsecutiveHeadHits2TimesCount = 0,
    ConsecutiveHeadHits2TimesMinTime = 0,
    ConsecutiveHeadHits3TimesCount = 0,
    ConsecutiveHeadHits3TimesMinTime = 0,
    ConsecutiveHeadHits4TimesCount = 0,
    ConsecutiveHeadHits4TimesMinTime = 0,
    ConsecutiveHeadHitsGreaterThan4TimesCount = 0,
    ConsecutiveHeadHitsGreaterThan4TimesMinTime = 0,
    ConsecutiveLimbHits3TimesCount = 0,
    ConsecutiveLimbHits3TimesMinTime = 0,
    ConsecutiveLimbHits4TimesCount = 0,
    ConsecutiveLimbHits4TimesMinTime = 0,
    ConsecutiveLimbHitsGreaterThan4TimesCount = 0,
    ConsecutiveLimbHitsGreaterThan4TimesMinTime = 0,
    FirstFireDistance = 0,
    MeleeAttackFireCnt = 0,
    MeleeAttackHitCnt = 0,
    MeleeAttackDamage = 0,
    ConsecutiveHits2TimesCount = 0,
    ConsecutiveHits2TimesMinTime = 0,
}
pb.__pb_AIFightDesignatedPlayer.__name = "AIFightDesignatedPlayer"
pb.__pb_AIFightDesignatedPlayer.__index = pb.__pb_AIFightDesignatedPlayer
pb.__pb_AIFightDesignatedPlayer.__pairs = __pb_pairs

pb.AIFightDesignatedPlayer = { __name = "AIFightDesignatedPlayer", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIFightDesignatedPlayer : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AIGid number
---@field public RoomId number
---@field public PlayerId number
---@field public FireNum number
---@field public HitNum number
---@field public HitHeadNum number
---@field public TotalDamage number
---@field public TotalDist number
---@field public KnockDownNum number
---@field public IsKill number
---@field public PlayerHitNum number
---@field public PlayerHitHeadNum number
---@field public PlayerTotalDamage number
---@field public PlayerTotalDist number
---@field public PlayerIsKill number
---@field public PlayerIsAssassinate number
---@field public PlayerIsFound number
---@field public TotalArmorDamage number
---@field public MapId number
---@field public HitThoraxNum number
---@field public HitRightArmNum number
---@field public HitLeftArmNum number
---@field public HitRightLegNum number
---@field public HitLeftLegNum number
---@field public HitFullBodyNum number
---@field public ArmorLevelWhenDie number
---@field public AIDifficultyPool number
---@field public DeathHitPart number
---@field public IsKnockDown number
---@field public DeathHitPartNew string
---@field public AIHitPlayerTotalDist number
---@field public MovementMode number
---@field public MovementCustomMode number
---@field public DamageBeforeDeath number
---@field public PlayerShieldDamage number
---@field public PlayerTotalArmorDamage number
---@field public HitAbdomenNum number
---@field public HitHeadDamage number
---@field public HitThoraxDamage number
---@field public HitAbdomenDamage number
---@field public HitRightArmDamage number
---@field public HitLeftArmDamage number
---@field public HitRightLegDamage number
---@field public HitLeftLegDamage number
---@field public HitFullBodyDamage number
---@field public HitHeadArmorDamage number
---@field public HitThoraxArmorDamage number
---@field public HitAbdomenArmorDamage number
---@field public HitRightArmArmorDamage number
---@field public HitLeftArmArmorDamage number
---@field public HitRightLegArmorDamage number
---@field public HitLeftLegArmorDamage number
---@field public HitFullBodyArmorDamage number
---@field public TTK number
---@field public ShootedHelmetID number
---@field public ShootedArmorID number
---@field public HitBulletNumThroughSoftCover number
---@field public MapName string
---@field public DSVersion string
---@field public AILevel number
---@field public AIType string
---@field public AISpawnTemplate string
---@field public FractureCausedByDamage number
---@field public BleedingCausedByDamage number
---@field public ChestArmorBeforeFirstFireOnKill number
---@field public HeadArmorBeforeFirstFireOnKill number
---@field public HealthBeforeFirstFireOnKill number
---@field public DistanceToPlayerBeforeFirstFireOnKill number
---@field public DistanceOnKill number
---@field public TimeFromFirstFireToKill number
---@field public HighestDamagePerSingleFire number
---@field public FireCountDistribution_0_5M number
---@field public FireCountDistribution_5_10M number
---@field public FireCountDistribution_10_20M number
---@field public FireCountDistribution_20_30M number
---@field public FireCountDistribution_30_40M number
---@field public FireCountDistribution_40_50M number
---@field public FireCountDistribution_50_60M number
---@field public FireCountDistribution_60_70M number
---@field public FireCountDistribution_70_80M number
---@field public FireCountDistribution_80_90M number
---@field public FireCountDistribution_90_100M number
---@field public FireCountDistribution_100_150M number
---@field public FireCountDistribution_150_200M number
---@field public FireCountDistribution_Above200M number
---@field public HitCntDistribution_0_5M number
---@field public HitCntDistribution_5_10M number
---@field public HitCntDistribution_10_20M number
---@field public HitCntDistribution_20_30M number
---@field public HitCntDistribution_30_40M number
---@field public HitCntDistribution_40_50M number
---@field public HitCntDistribution_50_60M number
---@field public HitCntDistribution_60_70M number
---@field public HitCntDistribution_70_80M number
---@field public HitCntDistribution_80_90M number
---@field public HitCntDistribution_90_100M number
---@field public HitCntDistribution_100_150M number
---@field public HitCntDistribution_150_200M number
---@field public HitCntDistribution_Above200M number
---@field public ConsecutiveHits3TimesCount number
---@field public ConsecutiveHits3TimesMinTime number
---@field public ConsecutiveHits4TimesCount number
---@field public ConsecutiveHits4TimesMinTime number
---@field public ConsecutiveHitsGreaterThan4TimesCount number
---@field public ConsecutiveHitsGreaterThan4TimesMinTime number
---@field public ConsecutiveHeadHits2TimesCount number
---@field public ConsecutiveHeadHits2TimesMinTime number
---@field public ConsecutiveHeadHits3TimesCount number
---@field public ConsecutiveHeadHits3TimesMinTime number
---@field public ConsecutiveHeadHits4TimesCount number
---@field public ConsecutiveHeadHits4TimesMinTime number
---@field public ConsecutiveHeadHitsGreaterThan4TimesCount number
---@field public ConsecutiveHeadHitsGreaterThan4TimesMinTime number
---@field public ConsecutiveLimbHits3TimesCount number
---@field public ConsecutiveLimbHits3TimesMinTime number
---@field public ConsecutiveLimbHits4TimesCount number
---@field public ConsecutiveLimbHits4TimesMinTime number
---@field public ConsecutiveLimbHitsGreaterThan4TimesCount number
---@field public ConsecutiveLimbHitsGreaterThan4TimesMinTime number
---@field public FirstFireDistance number
---@field public MeleeAttackFireCnt number
---@field public MeleeAttackHitCnt number
---@field public MeleeAttackDamage number
---@field public ConsecutiveHits2TimesCount number
---@field public ConsecutiveHits2TimesMinTime number

---@return pb_AIFightDesignatedPlayer
function pb.AIFightDesignatedPlayer:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HelicopterCrash = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    DeadNum = 0,
    IsLowHP = 0,
    IsAutoTrackBulletHit = 0,
}
pb.__pb_HelicopterCrash.__name = "HelicopterCrash"
pb.__pb_HelicopterCrash.__index = pb.__pb_HelicopterCrash
pb.__pb_HelicopterCrash.__pairs = __pb_pairs

pb.HelicopterCrash = { __name = "HelicopterCrash", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HelicopterCrash : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public DeadNum number
---@field public IsLowHP number
---@field public IsAutoTrackBulletHit number

---@return pb_HelicopterCrash
function pb.HelicopterCrash:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AIHurtWeakPlayerFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AIGid = 0,
    RoomId = 0,
    PlayerState = 0,
    StartTimeStamp = 0,
    EndTimeStamp = 0,
    ArmorDamage = 0,
    HPDamage = 0,
    FireNum = 0,
    HitNum = 0,
}
pb.__pb_AIHurtWeakPlayerFlow.__name = "AIHurtWeakPlayerFlow"
pb.__pb_AIHurtWeakPlayerFlow.__index = pb.__pb_AIHurtWeakPlayerFlow
pb.__pb_AIHurtWeakPlayerFlow.__pairs = __pb_pairs

pb.AIHurtWeakPlayerFlow = { __name = "AIHurtWeakPlayerFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AIHurtWeakPlayerFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AIGid number
---@field public RoomId number
---@field public PlayerState number
---@field public StartTimeStamp number
---@field public EndTimeStamp number
---@field public ArmorDamage number
---@field public HPDamage number
---@field public FireNum number
---@field public HitNum number

---@return pb_AIHurtWeakPlayerFlow
function pb.AIHurtWeakPlayerFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabAnalysisInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    KillPlayer = 0,
    KillNpc = 0,
    KillAILab = 0,
    AILabDeathNum = 0,
    AILabSpawnNum = 0,
    AILabRetreatNum = 0,
    AILabRecycleNum = 0,
    AILabSuicideNum = 0,
    AILabDeliverSpawnNum = 0,
    AILabDeliverSucNum = 0,
    AILabGameStartState = 0,
    AILabDisconnectedMidway = 0,
    AILabAddBotFaildNum = 0,
    AILabReqTimeoutNum = 0,
}
pb.__pb_AILabAnalysisInfo.__name = "AILabAnalysisInfo"
pb.__pb_AILabAnalysisInfo.__index = pb.__pb_AILabAnalysisInfo
pb.__pb_AILabAnalysisInfo.__pairs = __pb_pairs

pb.AILabAnalysisInfo = { __name = "AILabAnalysisInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabAnalysisInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public KillPlayer number
---@field public KillNpc number
---@field public KillAILab number
---@field public AILabDeathNum number
---@field public AILabSpawnNum number
---@field public AILabRetreatNum number
---@field public AILabRecycleNum number
---@field public AILabSuicideNum number
---@field public AILabDeliverSpawnNum number
---@field public AILabDeliverSucNum number
---@field public AILabGameStartState number
---@field public AILabDisconnectedMidway number
---@field public AILabAddBotFaildNum number
---@field public AILabReqTimeoutNum number

---@return pb_AILabAnalysisInfo
function pb.AILabAnalysisInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabLootInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    GameTimeSecs = 0,
    Uin = 0,
    ItemId = 0,
    ItemNum = 0,
    ItemValue = 0,
}
pb.__pb_AILabLootInfo.__name = "AILabLootInfo"
pb.__pb_AILabLootInfo.__index = pb.__pb_AILabLootInfo
pb.__pb_AILabLootInfo.__pairs = __pb_pairs

pb.AILabLootInfo = { __name = "AILabLootInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabLootInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public GameTimeSecs number
---@field public Uin number
---@field public ItemId number
---@field public ItemNum number
---@field public ItemValue number

---@return pb_AILabLootInfo
function pb.AILabLootInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabDropInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    GameTimeSecs = 0,
    Uin = 0,
    ItemId = 0,
    ItemNum = 0,
    ItemValue = 0,
}
pb.__pb_AILabDropInfo.__name = "AILabDropInfo"
pb.__pb_AILabDropInfo.__index = pb.__pb_AILabDropInfo
pb.__pb_AILabDropInfo.__pairs = __pb_pairs

pb.AILabDropInfo = { __name = "AILabDropInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabDropInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public GameTimeSecs number
---@field public Uin number
---@field public ItemId number
---@field public ItemNum number
---@field public ItemValue number

---@return pb_AILabDropInfo
function pb.AILabDropInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabBattleInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    GameTimeSecs = 0,
    KillerUin = 0,
    BeKillUin = 0,
    KillType = 0,
    BeKillType = 0,
}
pb.__pb_AILabBattleInfo.__name = "AILabBattleInfo"
pb.__pb_AILabBattleInfo.__index = pb.__pb_AILabBattleInfo
pb.__pb_AILabBattleInfo.__pairs = __pb_pairs

pb.AILabBattleInfo = { __name = "AILabBattleInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabBattleInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public GameTimeSecs number
---@field public KillerUin number
---@field public BeKillUin number
---@field public KillType number
---@field public BeKillType number

---@return pb_AILabBattleInfo
function pb.AILabBattleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabGameStatis = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    Uin = 0,
    GameTimeSecs = 0,
    BornDist = 0,
    ArmDamageTotal = 0,
    HpDamageTotal = 0,
    KillInBeRecueNm = 0,
    KillInRecueNum = 0,
    FireNum = 0,
    HitNum = 0,
    FireDist = 0,
    HitDist = 0,
    KillDist = 0,
    ArmDamage = 0,
    HPDamage = 0,
    HitHead = 0,
    HitThorax = 0,
    HitRightArm = 0,
    HitLeftArm = 0,
    HitRightLeg = 0,
    HitLeftLeg = 0,
    HitFullBody = 0,
    TargetUin = 0,
    TagetOpenId = 0,
    AILabLevel = 0,
    MakerWeaponId = 0,
    MakerArmorId = 0,
    MakerBulletId = 0,
    TakerWeaponId = 0,
    TakerArmorId = 0,
    TakerBulletId = 0,
    AILabArmDamage = 0,
    AILabHpDamage = 0,
    TeamId = 0,
    TargetTeamId = 0,
    DurSecs = 0,
}
pb.__pb_AILabGameStatis.__name = "AILabGameStatis"
pb.__pb_AILabGameStatis.__index = pb.__pb_AILabGameStatis
pb.__pb_AILabGameStatis.__pairs = __pb_pairs

pb.AILabGameStatis = { __name = "AILabGameStatis", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabGameStatis : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public Uin number
---@field public GameTimeSecs number
---@field public BornDist number
---@field public ArmDamageTotal number
---@field public HpDamageTotal number
---@field public KillInBeRecueNm number
---@field public KillInRecueNum number
---@field public FireNum number
---@field public HitNum number
---@field public FireDist number
---@field public HitDist number
---@field public KillDist number
---@field public ArmDamage number
---@field public HPDamage number
---@field public HitHead number
---@field public HitThorax number
---@field public HitRightArm number
---@field public HitLeftArm number
---@field public HitRightLeg number
---@field public HitLeftLeg number
---@field public HitFullBody number
---@field public TargetUin number
---@field public TagetOpenId number
---@field public AILabLevel number
---@field public MakerWeaponId number
---@field public MakerArmorId number
---@field public MakerBulletId number
---@field public TakerWeaponId number
---@field public TakerArmorId number
---@field public TakerBulletId number
---@field public AILabArmDamage number
---@field public AILabHpDamage number
---@field public TeamId number
---@field public TargetTeamId number
---@field public DurSecs number

---@return pb_AILabGameStatis
function pb.AILabGameStatis:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabPlayerStatis = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    Uin = 0,
    SpawnX = 0,
    SpawnY = 0,
    SpawnZ = 0,
    TargetUin = 0,
    DistanceTarget = 0,
    JoinType = 0,
    AiScore = 0,
    BehaviorType = 0,
    Difficulty = 0,
    KillPlayer = 0,
    KillNpc = 0,
    KillAILab = 0,
    KillBoss = 0,
    ExitStatus = 0,
}
pb.__pb_AILabPlayerStatis.__name = "AILabPlayerStatis"
pb.__pb_AILabPlayerStatis.__index = pb.__pb_AILabPlayerStatis
pb.__pb_AILabPlayerStatis.__pairs = __pb_pairs

pb.AILabPlayerStatis = { __name = "AILabPlayerStatis", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabPlayerStatis : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public Uin number
---@field public SpawnX number
---@field public SpawnY number
---@field public SpawnZ number
---@field public TargetUin number
---@field public DistanceTarget number
---@field public JoinType number
---@field public AiScore number
---@field public BehaviorType number
---@field public Difficulty number
---@field public KillPlayer number
---@field public KillNpc number
---@field public KillAILab number
---@field public KillBoss number
---@field public ExitStatus number

---@return pb_AILabPlayerStatis
function pb.AILabPlayerStatis:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_AILabDeliver = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerUin = 0,
    AILabUin = 0,
    GameTimeSecs = 0,
    OutSystemScore = 0,
    PlayerKillScore = 0,
    PlayerHpScore = 0,
    PlayerArmScore = 0,
    PlayerBloodyScore = 0,
    PlayerHurt = 0,
    PlayerIdle = 0,
    PlayerMinHp = 0,
    GameScoreTotal = 0,
    DeliverNum = 0,
    DeliverDiffcult = 0,
    DeliverAILabType = 0,
}
pb.__pb_AILabDeliver.__name = "AILabDeliver"
pb.__pb_AILabDeliver.__index = pb.__pb_AILabDeliver
pb.__pb_AILabDeliver.__pairs = __pb_pairs

pb.AILabDeliver = { __name = "AILabDeliver", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_AILabDeliver : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerUin number
---@field public AILabUin number
---@field public GameTimeSecs number
---@field public OutSystemScore number
---@field public PlayerKillScore number
---@field public PlayerHpScore number
---@field public PlayerArmScore number
---@field public PlayerBloodyScore number
---@field public PlayerHurt number
---@field public PlayerIdle number
---@field public PlayerMinHp number
---@field public GameScoreTotal number
---@field public DeliverNum number
---@field public DeliverDiffcult number
---@field public DeliverAILabType number

---@return pb_AILabDeliver
function pb.AILabDeliver:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMAILabSectorStatis = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    Schemeid = 0,
    AILabUin = 0,
    IsRankedMatch = 0,
    CampId = 0,
    SectorId = 0,
    FireNum = 0,
    HitNum = 0,
    HPDamage = 0,
    CarrierDamage = 0,
    RescueNum = 0,
    DeadNum = 0,
    DepoloyNum = 0,
    KillPlayerNum = 0,
    KillBotNum = 0,
    KillPlayerAINum = 0,
    CaptureCount = 0,
    SuicideNum = 0,
    BreatheHeal = 0,
    BreatheHealInterruptTimes = 0,
    BreatheHealInterruptReason = "",
    KillNum = 0,
    Death = 0,
    Assist = 0,
    Score = 0,
    Mvp = 0,
    IsLeave = 0,
    Occupy = 0,
    IsWinner = 0,
    BreakCount = 0,
}
pb.__pb_TDMAILabSectorStatis.__name = "TDMAILabSectorStatis"
pb.__pb_TDMAILabSectorStatis.__index = pb.__pb_TDMAILabSectorStatis
pb.__pb_TDMAILabSectorStatis.__pairs = __pb_pairs

pb.TDMAILabSectorStatis = { __name = "TDMAILabSectorStatis", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMAILabSectorStatis : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public Schemeid number
---@field public AILabUin number
---@field public IsRankedMatch number
---@field public CampId number
---@field public SectorId number
---@field public FireNum number
---@field public HitNum number
---@field public HPDamage number
---@field public CarrierDamage number
---@field public RescueNum number
---@field public DeadNum number
---@field public DepoloyNum number
---@field public KillPlayerNum number
---@field public KillBotNum number
---@field public KillPlayerAINum number
---@field public CaptureCount number
---@field public SuicideNum number
---@field public BreatheHeal number
---@field public BreatheHealInterruptTimes number
---@field public BreatheHealInterruptReason string
---@field public KillNum number
---@field public Death number
---@field public Assist number
---@field public Score number
---@field public Mvp number
---@field public IsLeave number
---@field public Occupy number
---@field public IsWinner number
---@field public BreakCount number

---@return pb_TDMAILabSectorStatis
function pb.TDMAILabSectorStatis:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_GemHotMapInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = "",
    PlayerLoc = "",
    SceneLoc = "",
    MapLoc = "",
    MapSize = "",
    ModeID = "",
    PlayerId = 0,
    RoomId = 0,
    Minutes = 0,
    GamePlayEventID = 0,
    StartGroupID = 0,
    Seconds = 0,
    GlickoRating = 0,
    GlickoRatingDev = 0,
    MatchType = 0,
    CampID = 0,
    VehicleID = 0,
    BotType = 0,
}
pb.__pb_GemHotMapInfo.__name = "GemHotMapInfo"
pb.__pb_GemHotMapInfo.__index = pb.__pb_GemHotMapInfo
pb.__pb_GemHotMapInfo.__pairs = __pb_pairs

pb.GemHotMapInfo = { __name = "GemHotMapInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_GemHotMapInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID string
---@field public PlayerLoc string
---@field public SceneLoc string
---@field public MapLoc string
---@field public MapSize string
---@field public ModeID string
---@field public PlayerId number
---@field public RoomId number
---@field public Minutes number
---@field public GamePlayEventID number
---@field public StartGroupID number
---@field public Seconds number
---@field public GlickoRating number
---@field public GlickoRatingDev number
---@field public MatchType number
---@field public CampID number
---@field public VehicleID number
---@field public BotType number

---@return pb_GemHotMapInfo
function pb.GemHotMapInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_XPPChangeInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    ChangeToFPPCount = 0,
    ChangeToTPPCount = 0,
}
pb.__pb_XPPChangeInfo.__name = "XPPChangeInfo"
pb.__pb_XPPChangeInfo.__index = pb.__pb_XPPChangeInfo
pb.__pb_XPPChangeInfo.__pairs = __pb_pairs

pb.XPPChangeInfo = { __name = "XPPChangeInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_XPPChangeInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public ChangeToFPPCount number
---@field public ChangeToTPPCount number

---@return pb_XPPChangeInfo
function pb.XPPChangeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchEndInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    MatchHasNoPlayerExistTime = 0,
}
pb.__pb_MatchEndInfo.__name = "MatchEndInfo"
pb.__pb_MatchEndInfo.__index = pb.__pb_MatchEndInfo
pb.__pb_MatchEndInfo.__pairs = __pb_pairs

pb.MatchEndInfo = { __name = "MatchEndInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchEndInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public MatchHasNoPlayerExistTime number

---@return pb_MatchEndInfo
function pb.MatchEndInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerNetworkStat = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    PingMs = 0,
    InBytesPerSecond = 0,
    OutBytesPerSecond = 0,
    InPacketsPerSecond = 0,
    OutPacketsPerSecond = 0,
    InPacketsLost = 0,
    OutPacketsLost = 0,
    MapID = 0,
    RoomID = 0,
    BestIdc = "",
    BestAccessPoint = "",
    BestIdcRtt = 0,
}
pb.__pb_PlayerNetworkStat.__name = "PlayerNetworkStat"
pb.__pb_PlayerNetworkStat.__index = pb.__pb_PlayerNetworkStat
pb.__pb_PlayerNetworkStat.__pairs = __pb_pairs

pb.PlayerNetworkStat = { __name = "PlayerNetworkStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerNetworkStat : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public PingMs number
---@field public InBytesPerSecond number
---@field public OutBytesPerSecond number
---@field public InPacketsPerSecond number
---@field public OutPacketsPerSecond number
---@field public InPacketsLost number
---@field public OutPacketsLost number
---@field public MapID number
---@field public RoomID number
---@field public BestIdc string
---@field public BestAccessPoint string
---@field public BestIdcRtt number

---@return pb_PlayerNetworkStat
function pb.PlayerNetworkStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerNetworkOverallView = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    MaxPingMs = 0,
    MaxPingTime = 0,
    MinPingMs = 0,
    AvgPingMs = 0,
    SDPingMs = 0,
    MaxJitter = 0,
    MaxJitterTime = 0,
    AvgJitter = 0,
    SDJitter = 0,
    PercentOfLessThan30 = 0,
    PercentOfLessThan50 = 0,
    PercentOfLessThan80 = 0,
    PercentOfLessThan100 = 0,
    PercentOfLessThan120 = 0,
    PercentOfLessThan150 = 0,
    PercentOfLessThan200 = 0,
    PercentOfLessThan250 = 0,
    PercentOfLessThan300 = 0,
    PercentOfLessThan350 = 0,
    PercentOfLessThan400 = 0,
    PercentOfLessThan450 = 0,
    PercentOfMoreThan450 = 0,
    InPeakBytes = 0,
    InTotalBytes = 0,
    OutPeakBytes = 0,
    OutTotalBytes = 0,
    InPeakPackets = 0,
    InTotalPackets = 0,
    OutPeakPackets = 0,
    OutTotalPackets = 0,
    InTotalPacketsLost = 0,
    OutTotalPacketsLost = 0,
    TotalGameTime = 0,
    MapID = 0,
    RoomID = 0,
    BestIdc = "",
    BestAccessPoint = "",
    BestIdcRtt = 0,
    ClientIP = "",
    IsHalfJoin = 0,
    ConnectCnt = 0,
    OldMaxPingMs = 0,
    OldMaxPingTime = 0,
    OldMinPingMs = 0,
    OldAvgPingMs = 0,
    OldSDPingMs = 0,
    OldMaxJitter = 0,
    OldMaxJitterTime = 0,
    OldAvgJitter = 0,
    OldSDJitter = 0,
    MapName = "",
    DSVersion = "",
    Network = "",
    MobileNetwork = "",
    VClientIP = "",
    DSDomain = "",
    DSIP = "",
    DSPort = 0,
    MinRTO = 0,
    MaxRTO = 0,
    AvgRTO = 0,
    Idc = "",
    FirstIdc = "",
    FirstIdcRtt = 0,
    MatchModeID = 0,
    GameMode = 0,
    MatchType = 0,
    SchemeID = 0,
    CourseID = 0,
}
pb.__pb_PlayerNetworkOverallView.__name = "PlayerNetworkOverallView"
pb.__pb_PlayerNetworkOverallView.__index = pb.__pb_PlayerNetworkOverallView
pb.__pb_PlayerNetworkOverallView.__pairs = __pb_pairs

pb.PlayerNetworkOverallView = { __name = "PlayerNetworkOverallView", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerNetworkOverallView : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public MaxPingMs number
---@field public MaxPingTime number
---@field public MinPingMs number
---@field public AvgPingMs number
---@field public SDPingMs number
---@field public MaxJitter number
---@field public MaxJitterTime number
---@field public AvgJitter number
---@field public SDJitter number
---@field public PercentOfLessThan30 number
---@field public PercentOfLessThan50 number
---@field public PercentOfLessThan80 number
---@field public PercentOfLessThan100 number
---@field public PercentOfLessThan120 number
---@field public PercentOfLessThan150 number
---@field public PercentOfLessThan200 number
---@field public PercentOfLessThan250 number
---@field public PercentOfLessThan300 number
---@field public PercentOfLessThan350 number
---@field public PercentOfLessThan400 number
---@field public PercentOfLessThan450 number
---@field public PercentOfMoreThan450 number
---@field public InPeakBytes number
---@field public InTotalBytes number
---@field public OutPeakBytes number
---@field public OutTotalBytes number
---@field public InPeakPackets number
---@field public InTotalPackets number
---@field public OutPeakPackets number
---@field public OutTotalPackets number
---@field public InTotalPacketsLost number
---@field public OutTotalPacketsLost number
---@field public TotalGameTime number
---@field public MapID number
---@field public RoomID number
---@field public BestIdc string
---@field public BestAccessPoint string
---@field public BestIdcRtt number
---@field public ClientIP string
---@field public IsHalfJoin number
---@field public ConnectCnt number
---@field public OldMaxPingMs number
---@field public OldMaxPingTime number
---@field public OldMinPingMs number
---@field public OldAvgPingMs number
---@field public OldSDPingMs number
---@field public OldMaxJitter number
---@field public OldMaxJitterTime number
---@field public OldAvgJitter number
---@field public OldSDJitter number
---@field public MapName string
---@field public DSVersion string
---@field public Network string
---@field public MobileNetwork string
---@field public VClientIP string
---@field public DSDomain string
---@field public DSIP string
---@field public DSPort number
---@field public MinRTO number
---@field public MaxRTO number
---@field public AvgRTO number
---@field public Idc string
---@field public FirstIdc string
---@field public FirstIdcRtt number
---@field public MatchModeID number
---@field public GameMode number
---@field public MatchType number
---@field public SchemeID number
---@field public CourseID number

---@return pb_PlayerNetworkOverallView
function pb.PlayerNetworkOverallView:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerNetworkOneConnection = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    ClientIP = "",
    CLBIP = "",
    MapName = "",
    DSVersion = "",
    MapID = 0,
    RoomID = 0,
    MaxPingMs = 0,
    MaxPingTime = 0,
    MinPingMs = 0,
    AvgPingMs = 0,
    SDPingMs = 0,
    MaxJitter = 0,
    MaxJitterTime = 0,
    AvgJitter = 0,
    SDJitter = 0,
    PercentOfLessThan30 = 0,
    PercentOfLessThan50 = 0,
    PercentOfLessThan80 = 0,
    PercentOfLessThan100 = 0,
    PercentOfLessThan120 = 0,
    PercentOfLessThan150 = 0,
    PercentOfLessThan200 = 0,
    PercentOfLessThan250 = 0,
    PercentOfLessThan300 = 0,
    PercentOfLessThan350 = 0,
    PercentOfLessThan400 = 0,
    PercentOfLessThan450 = 0,
    PercentOfMoreThan450 = 0,
    InPeakBytes = 0,
    InTotalBytes = 0,
    OutPeakBytes = 0,
    OutTotalBytes = 0,
    InPeakPackets = 0,
    InTotalPackets = 0,
    OutPeakPackets = 0,
    OutTotalPackets = 0,
    InTotalPacketsLost = 0,
    OutTotalPacketsLost = 0,
    ConnectionTime = 0,
    ReconnectType = 0,
    NetConnectionChange = 0,
}
pb.__pb_PlayerNetworkOneConnection.__name = "PlayerNetworkOneConnection"
pb.__pb_PlayerNetworkOneConnection.__index = pb.__pb_PlayerNetworkOneConnection
pb.__pb_PlayerNetworkOneConnection.__pairs = __pb_pairs

pb.PlayerNetworkOneConnection = { __name = "PlayerNetworkOneConnection", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerNetworkOneConnection : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public ClientIP string
---@field public CLBIP string
---@field public MapName string
---@field public DSVersion string
---@field public MapID number
---@field public RoomID number
---@field public MaxPingMs number
---@field public MaxPingTime number
---@field public MinPingMs number
---@field public AvgPingMs number
---@field public SDPingMs number
---@field public MaxJitter number
---@field public MaxJitterTime number
---@field public AvgJitter number
---@field public SDJitter number
---@field public PercentOfLessThan30 number
---@field public PercentOfLessThan50 number
---@field public PercentOfLessThan80 number
---@field public PercentOfLessThan100 number
---@field public PercentOfLessThan120 number
---@field public PercentOfLessThan150 number
---@field public PercentOfLessThan200 number
---@field public PercentOfLessThan250 number
---@field public PercentOfLessThan300 number
---@field public PercentOfLessThan350 number
---@field public PercentOfLessThan400 number
---@field public PercentOfLessThan450 number
---@field public PercentOfMoreThan450 number
---@field public InPeakBytes number
---@field public InTotalBytes number
---@field public OutPeakBytes number
---@field public OutTotalBytes number
---@field public InPeakPackets number
---@field public InTotalPackets number
---@field public OutPeakPackets number
---@field public OutTotalPackets number
---@field public InTotalPacketsLost number
---@field public OutTotalPacketsLost number
---@field public ConnectionTime number
---@field public ReconnectType number
---@field public NetConnectionChange number

---@return pb_PlayerNetworkOneConnection
function pb.PlayerNetworkOneConnection:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSDamageValidate = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomID = 0,
    SourceName = "",
    SourcePlayerID = 0,
    DestName = "",
    DestPlayerID = 0,
    SourceWeaponID = 0,
    RuleID = 0,
    Damage = 0,
    DamageType = 0,
    DamageResult = 0,
    ResultCode = 0,
    PingMs = 0,
    RewindTime = 0,
    BInVehicle1P = 0,
    BInVehicle3P = 0,
    BHasMovementBase1P = 0,
    BHasMovementBase3P = 0,
    MoveSpeed1P = 0,
    MoveSpeed3P = 0,
    Distance = 0,
    TargetType = 0,
}
pb.__pb_DSDamageValidate.__name = "DSDamageValidate"
pb.__pb_DSDamageValidate.__index = pb.__pb_DSDamageValidate
pb.__pb_DSDamageValidate.__pairs = __pb_pairs

pb.DSDamageValidate = { __name = "DSDamageValidate", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSDamageValidate : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomID number
---@field public SourceName string
---@field public SourcePlayerID number
---@field public DestName string
---@field public DestPlayerID number
---@field public SourceWeaponID number
---@field public RuleID number
---@field public Damage number
---@field public DamageType number
---@field public DamageResult number
---@field public ResultCode number
---@field public PingMs number
---@field public RewindTime number
---@field public BInVehicle1P number
---@field public BInVehicle3P number
---@field public BHasMovementBase1P number
---@field public BHasMovementBase3P number
---@field public MoveSpeed1P number
---@field public MoveSpeed3P number
---@field public Distance number
---@field public TargetType number

---@return pb_DSDamageValidate
function pb.DSDamageValidate:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponChoiceFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    GameMode = 0,
    MapId = 0,
    ArmedforceID = 0,
    WeaponID = 0,
}
pb.__pb_WeaponChoiceFlow.__name = "WeaponChoiceFlow"
pb.__pb_WeaponChoiceFlow.__index = pb.__pb_WeaponChoiceFlow
pb.__pb_WeaponChoiceFlow.__pairs = __pb_pairs

pb.WeaponChoiceFlow = { __name = "WeaponChoiceFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponChoiceFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public GameMode number
---@field public MapId number
---@field public ArmedforceID number
---@field public WeaponID number

---@return pb_WeaponChoiceFlow
function pb.WeaponChoiceFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponHarmFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    GameMode = 0,
    MapId = 0,
    ArmedforceID = 0,
    WeaponID = 0,
    IsKill = 0,
    IsHeadshot = 0,
    IsAI = 0,
    Distance = 0,
    AimType = 0,
    WeaponFireMode = 0,
    BulletItemID = 0,
    BulletResidueAfterShoot = 0,
    IsAILab = 0,
    IsPlayer = 0,
    ArmorId = 0,
    RoomID = 0,
    Location = "",
    HarmValue = 0,
    StraightDistance = 0,
}
pb.__pb_WeaponHarmFlow.__name = "WeaponHarmFlow"
pb.__pb_WeaponHarmFlow.__index = pb.__pb_WeaponHarmFlow
pb.__pb_WeaponHarmFlow.__pairs = __pb_pairs

pb.WeaponHarmFlow = { __name = "WeaponHarmFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponHarmFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public GameMode number
---@field public MapId number
---@field public ArmedforceID number
---@field public WeaponID number
---@field public IsKill number
---@field public IsHeadshot number
---@field public IsAI number
---@field public Distance number
---@field public AimType number
---@field public WeaponFireMode number
---@field public BulletItemID number
---@field public BulletResidueAfterShoot number
---@field public IsAILab number
---@field public IsPlayer number
---@field public ArmorId number
---@field public RoomID number
---@field public Location string
---@field public HarmValue number
---@field public StraightDistance number

---@return pb_WeaponHarmFlow
function pb.WeaponHarmFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_LootFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    TotalLootCnt = 0,
    AILabAIAccessCnt = 0,
    NormalAIAccessCnt = 0,
    OtherPlayerAccessCnt = 0,
    MapBoxAccessCnt = 0,
    DropPointAccessCnt = 0,
    SpawnPoint = "",
    BeforeFirstLootDuration = 0,
    DurationMS = 0,
    RoomId = 0,
}
pb.__pb_LootFlow.__name = "LootFlow"
pb.__pb_LootFlow.__index = pb.__pb_LootFlow
pb.__pb_LootFlow.__pairs = __pb_pairs

pb.LootFlow = { __name = "LootFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_LootFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public TotalLootCnt number
---@field public AILabAIAccessCnt number
---@field public NormalAIAccessCnt number
---@field public OtherPlayerAccessCnt number
---@field public MapBoxAccessCnt number
---@field public DropPointAccessCnt number
---@field public SpawnPoint string
---@field public BeforeFirstLootDuration number
---@field public DurationMS number
---@field public RoomId number

---@return pb_LootFlow
function pb.LootFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_QuestChangeFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    Level = 0,
    QuestID = 0,
    QuestState = 0,
    ObjectiveID = 0,
    RoomId = 0,
    MapId = 0,
    EscapeState = 0,
}
pb.__pb_QuestChangeFlow.__name = "QuestChangeFlow"
pb.__pb_QuestChangeFlow.__index = pb.__pb_QuestChangeFlow
pb.__pb_QuestChangeFlow.__pairs = __pb_pairs

pb.QuestChangeFlow = { __name = "QuestChangeFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_QuestChangeFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public Level number
---@field public QuestID number
---@field public QuestState number
---@field public ObjectiveID number
---@field public RoomId number
---@field public MapId number
---@field public EscapeState number

---@return pb_QuestChangeFlow
function pb.QuestChangeFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Character3PState = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    ExtrapolateTimeMs = 0,
    ExtrapolateCount = 0,
    AvgServerReplicationDeltaTimeMs = 0,
    PingMs = 0,
    RoomId = 0,
    InterploateSnapCount = 0,
    RealtimeInterpolateDelayMs = 0,
    PlayerId3P = 0,
}
pb.__pb_Character3PState.__name = "Character3PState"
pb.__pb_Character3PState.__index = pb.__pb_Character3PState
pb.__pb_Character3PState.__pairs = __pb_pairs

pb.Character3PState = { __name = "Character3PState", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Character3PState : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public ExtrapolateTimeMs number
---@field public ExtrapolateCount number
---@field public AvgServerReplicationDeltaTimeMs number
---@field public PingMs number
---@field public RoomId number
---@field public InterploateSnapCount number
---@field public RealtimeInterpolateDelayMs number
---@field public PlayerId3P number

---@return pb_Character3PState
function pb.Character3PState:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Character1PState = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    ErrMovementModeCount = 0,
    ErrLargePositionCount = 0,
    RoomId = 0,
}
pb.__pb_Character1PState.__name = "Character1PState"
pb.__pb_Character1PState.__index = pb.__pb_Character1PState
pb.__pb_Character1PState.__pairs = __pb_pairs

pb.Character1PState = { __name = "Character1PState", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Character1PState : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public ErrMovementModeCount number
---@field public ErrLargePositionCount number
---@field public RoomId number

---@return pb_Character1PState
function pb.Character1PState:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSEndGameWeaponUsedTime = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    MapId = 0,
    GameMode = 0,
    WeaponID = 0,
    WeaponGID = 0,
    UsedTime = 0,
}
pb.__pb_DSEndGameWeaponUsedTime.__name = "DSEndGameWeaponUsedTime"
pb.__pb_DSEndGameWeaponUsedTime.__index = pb.__pb_DSEndGameWeaponUsedTime
pb.__pb_DSEndGameWeaponUsedTime.__pairs = __pb_pairs

pb.DSEndGameWeaponUsedTime = { __name = "DSEndGameWeaponUsedTime", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSEndGameWeaponUsedTime : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public MapId number
---@field public GameMode number
---@field public WeaponID number
---@field public WeaponGID number
---@field public UsedTime number

---@return pb_DSEndGameWeaponUsedTime
function pb.DSEndGameWeaponUsedTime:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSEveryShootFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    MapId = 0,
    GameMode = 0,
    ShooterId = 0,
    ShooterArmedforceID = 0,
    ShooterLevel = 0,
    ShooterLoc = "",
    SooterWeaponID = 0,
    ShooterWeaponGID = 0,
    ShooterComponentList = "",
    ShootMode = 0,
    IsWaistShoot = 0,
    ShootDamage = 0,
    ShootedId = 0,
    ShootedPlayerType = 0,
    ShootedHelmetID = 0,
    ShootedArmorID = 0,
    ShootedDistance = 0,
    ShootedPart = 0,
    ShootedIsDead = 0,
    ShootedLoc = "",
    SootedWeaponID = 0,
    ShootedWeaponGID = 0,
    ShootedComponentList = "",
    BulletItemID = 0,
    BulletResidueAfterShoot = 0,
    ShooterComponentSwitchList = "",
    SightItemID = "",
    BulletCapacity = 0,
    BulletItemLevel = 0,
    IsHit = 0,
    IsHeadshot = 0,
    WeaponFireMode = 0,
    ShootArmorDamage = 0,
}
pb.__pb_DSEveryShootFlow.__name = "DSEveryShootFlow"
pb.__pb_DSEveryShootFlow.__index = pb.__pb_DSEveryShootFlow
pb.__pb_DSEveryShootFlow.__pairs = __pb_pairs

pb.DSEveryShootFlow = { __name = "DSEveryShootFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSEveryShootFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public MapId number
---@field public GameMode number
---@field public ShooterId number
---@field public ShooterArmedforceID number
---@field public ShooterLevel number
---@field public ShooterLoc string
---@field public SooterWeaponID number
---@field public ShooterWeaponGID number
---@field public ShooterComponentList string
---@field public ShootMode number
---@field public IsWaistShoot number
---@field public ShootDamage number
---@field public ShootedId number
---@field public ShootedPlayerType number
---@field public ShootedHelmetID number
---@field public ShootedArmorID number
---@field public ShootedDistance number
---@field public ShootedPart number
---@field public ShootedIsDead number
---@field public ShootedLoc string
---@field public SootedWeaponID number
---@field public ShootedWeaponGID number
---@field public ShootedComponentList string
---@field public BulletItemID number
---@field public BulletResidueAfterShoot number
---@field public ShooterComponentSwitchList string
---@field public SightItemID string
---@field public BulletCapacity number
---@field public BulletItemLevel number
---@field public IsHit number
---@field public IsHeadshot number
---@field public WeaponFireMode number
---@field public ShootArmorDamage number

---@return pb_DSEveryShootFlow
function pb.DSEveryShootFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSWeaponDataWhenKillFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    MapId = 0,
    GameMode = 0,
    ShooterId = 0,
    SooterWeaponID = 0,
    ShooterComponentList = "",
    Position = 0,
    BulletItemID = 0,
    BulletResidueAfterShoot = 0,
    BulletCapacity = 0,
    SightItemID = "",
}
pb.__pb_DSWeaponDataWhenKillFlow.__name = "DSWeaponDataWhenKillFlow"
pb.__pb_DSWeaponDataWhenKillFlow.__index = pb.__pb_DSWeaponDataWhenKillFlow
pb.__pb_DSWeaponDataWhenKillFlow.__pairs = __pb_pairs

pb.DSWeaponDataWhenKillFlow = { __name = "DSWeaponDataWhenKillFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSWeaponDataWhenKillFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public MapId number
---@field public GameMode number
---@field public ShooterId number
---@field public SooterWeaponID number
---@field public ShooterComponentList string
---@field public Position number
---@field public BulletItemID number
---@field public BulletResidueAfterShoot number
---@field public BulletCapacity number
---@field public SightItemID string

---@return pb_DSWeaponDataWhenKillFlow
function pb.DSWeaponDataWhenKillFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    Camp = 0,
    SectorDuration = 0,
    TransferTimeStamp = 0,
    HighestScore = 0,
    LowestScore = 0,
    AverageScore = 0,
    AttackerAddedVotes = 0,
    AttackerVotes = 0,
    TriggerTaskTimeStamp = 0,
    TaskRewardVotes = 0,
    AttackerConsumedVotes = 0,
    HighestMilitaryRank = 0,
    MapID = 0,
    RoomId = 0,
    WeaponInfo = "",
    SectorStartTime = "",
    TransferTime = "",
    PlayerId = 0,
    Camp0Score = 0,
    Camp1Score = 0,
    Camp0CurScoreSpeed = 0,
    Camp1CurScoreSpeed = 0,
    AttackerAiConsumedVotes = 0,
    AttackerAiAvgConsumedVotes = 0,
}
pb.__pb_TDMSectorFlow.__name = "TDMSectorFlow"
pb.__pb_TDMSectorFlow.__index = pb.__pb_TDMSectorFlow
pb.__pb_TDMSectorFlow.__pairs = __pb_pairs

pb.TDMSectorFlow = { __name = "TDMSectorFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public Camp number
---@field public SectorDuration number
---@field public TransferTimeStamp number
---@field public HighestScore number
---@field public LowestScore number
---@field public AverageScore number
---@field public AttackerAddedVotes number
---@field public AttackerVotes number
---@field public TriggerTaskTimeStamp number
---@field public TaskRewardVotes number
---@field public AttackerConsumedVotes number
---@field public HighestMilitaryRank number
---@field public MapID number
---@field public RoomId number
---@field public WeaponInfo string
---@field public SectorStartTime string
---@field public TransferTime string
---@field public PlayerId number
---@field public Camp0Score number
---@field public Camp1Score number
---@field public Camp0CurScoreSpeed number
---@field public Camp1CurScoreSpeed number
---@field public AttackerAiConsumedVotes number
---@field public AttackerAiAvgConsumedVotes number

---@return pb_TDMSectorFlow
function pb.TDMSectorFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorOccupiedStatusFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    Camp = 0,
    OccupiedStatus = 0,
    OccupiedStatusStartTimeStamp = 0,
    OccupiedStatusDuration = 0,
    MapID = 0,
    RoomId = 0,
    AnchorName = "",
    PlayerId = 0,
    OccupiedPercent = 0,
    Camp0InAnchor = 0,
    Camp1InAnchor = 0,
    AttackerLeftVotes = 0,
    BalanceValue = 0,
    IsConcentratedFireSectorAnchor = 0,
    AnchorOwnerCamp = 0,
    AnchorOwnerForceType = 0,
}
pb.__pb_TDMSectorOccupiedStatusFlow.__name = "TDMSectorOccupiedStatusFlow"
pb.__pb_TDMSectorOccupiedStatusFlow.__index = pb.__pb_TDMSectorOccupiedStatusFlow
pb.__pb_TDMSectorOccupiedStatusFlow.__pairs = __pb_pairs

pb.TDMSectorOccupiedStatusFlow = { __name = "TDMSectorOccupiedStatusFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorOccupiedStatusFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public Camp number
---@field public OccupiedStatus number
---@field public OccupiedStatusStartTimeStamp number
---@field public OccupiedStatusDuration number
---@field public MapID number
---@field public RoomId number
---@field public AnchorName string
---@field public PlayerId number
---@field public OccupiedPercent number
---@field public Camp0InAnchor number
---@field public Camp1InAnchor number
---@field public AttackerLeftVotes number
---@field public BalanceValue number
---@field public IsConcentratedFireSectorAnchor number
---@field public AnchorOwnerCamp number
---@field public AnchorOwnerForceType number

---@return pb_TDMSectorOccupiedStatusFlow
function pb.TDMSectorOccupiedStatusFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorCarrierRefreshFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    CarrierType = 0,
    CarrierID = 0,
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
}
pb.__pb_TDMSectorCarrierRefreshFlow.__name = "TDMSectorCarrierRefreshFlow"
pb.__pb_TDMSectorCarrierRefreshFlow.__index = pb.__pb_TDMSectorCarrierRefreshFlow
pb.__pb_TDMSectorCarrierRefreshFlow.__pairs = __pb_pairs

pb.TDMSectorCarrierRefreshFlow = { __name = "TDMSectorCarrierRefreshFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorCarrierRefreshFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public CarrierType number
---@field public CarrierID number
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number

---@return pb_TDMSectorCarrierRefreshFlow
function pb.TDMSectorCarrierRefreshFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorCarrierDestroyedFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    CarrierType = 0,
    CarrierID = 0,
    DestroyerCarrierType = 0,
    MapID = 0,
    RoomId = 0,
    DeadPosition = "",
    PlayerId = 0,
    DamageStateNum = 0,
    DamageStateTimeLength = "",
    DeadDamageSateTimeLength = 0,
}
pb.__pb_TDMSectorCarrierDestroyedFlow.__name = "TDMSectorCarrierDestroyedFlow"
pb.__pb_TDMSectorCarrierDestroyedFlow.__index = pb.__pb_TDMSectorCarrierDestroyedFlow
pb.__pb_TDMSectorCarrierDestroyedFlow.__pairs = __pb_pairs

pb.TDMSectorCarrierDestroyedFlow = { __name = "TDMSectorCarrierDestroyedFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorCarrierDestroyedFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public CarrierType number
---@field public CarrierID number
---@field public DestroyerCarrierType number
---@field public MapID number
---@field public RoomId number
---@field public DeadPosition string
---@field public PlayerId number
---@field public DamageStateNum number
---@field public DamageStateTimeLength string
---@field public DeadDamageSateTimeLength number

---@return pb_TDMSectorCarrierDestroyedFlow
function pb.TDMSectorCarrierDestroyedFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorCarrierStatusFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    CarrierType = 0,
    CarrierID = 0,
    CarrierPosition = "",
    MapID = 0,
    RoomId = 0,
    Distance = 0,
    PlayerId = 0,
    HighThreatValue = 0,
    SpawnTime = "",
    BeHighThreatTime = "",
    ClearHighThreatTime = "",
}
pb.__pb_TDMSectorCarrierStatusFlow.__name = "TDMSectorCarrierStatusFlow"
pb.__pb_TDMSectorCarrierStatusFlow.__index = pb.__pb_TDMSectorCarrierStatusFlow
pb.__pb_TDMSectorCarrierStatusFlow.__pairs = __pb_pairs

pb.TDMSectorCarrierStatusFlow = { __name = "TDMSectorCarrierStatusFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorCarrierStatusFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public CarrierType number
---@field public CarrierID number
---@field public CarrierPosition string
---@field public MapID number
---@field public RoomId number
---@field public Distance number
---@field public PlayerId number
---@field public HighThreatValue number
---@field public SpawnTime string
---@field public BeHighThreatTime string
---@field public ClearHighThreatTime string

---@return pb_TDMSectorCarrierStatusFlow
function pb.TDMSectorCarrierStatusFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorCarrierOccupiedFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    OccupierType = 0,
    SectorID = 0,
    CarrierType = 0,
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
}
pb.__pb_TDMSectorCarrierOccupiedFlow.__name = "TDMSectorCarrierOccupiedFlow"
pb.__pb_TDMSectorCarrierOccupiedFlow.__index = pb.__pb_TDMSectorCarrierOccupiedFlow
pb.__pb_TDMSectorCarrierOccupiedFlow.__pairs = __pb_pairs

pb.TDMSectorCarrierOccupiedFlow = { __name = "TDMSectorCarrierOccupiedFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorCarrierOccupiedFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public OccupierType number
---@field public SectorID number
---@field public CarrierType number
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number

---@return pb_TDMSectorCarrierOccupiedFlow
function pb.TDMSectorCarrierOccupiedFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorDeployFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    Camp = 0,
    DeployType = 0,
    DeployArmedForceType = 0,
    DeployCarrierType = 0,
    MapID = 0,
    RoomId = 0,
    SectorRevival = "",
    TeammateRevival = "",
    PlayerId = 0,
    VehicleCairrierID = 0,
    EnemyDisScore = 0,
    EnemyOcclusionScore = 0,
    EnemyFireScore = 0,
    MateDisScore = 0,
    AnchorDisScore = 0,
    SelectedMateDisScore = 0,
    DeadMateScore = 0,
    OtherMateScore = 0,
}
pb.__pb_TDMSectorDeployFlow.__name = "TDMSectorDeployFlow"
pb.__pb_TDMSectorDeployFlow.__index = pb.__pb_TDMSectorDeployFlow
pb.__pb_TDMSectorDeployFlow.__pairs = __pb_pairs

pb.TDMSectorDeployFlow = { __name = "TDMSectorDeployFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorDeployFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public Camp number
---@field public DeployType number
---@field public DeployArmedForceType number
---@field public DeployCarrierType number
---@field public MapID number
---@field public RoomId number
---@field public SectorRevival string
---@field public TeammateRevival string
---@field public PlayerId number
---@field public VehicleCairrierID number
---@field public EnemyDisScore number
---@field public EnemyOcclusionScore number
---@field public EnemyFireScore number
---@field public MateDisScore number
---@field public AnchorDisScore number
---@field public SelectedMateDisScore number
---@field public DeadMateScore number
---@field public OtherMateScore number

---@return pb_TDMSectorDeployFlow
function pb.TDMSectorDeployFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorSupportFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    Camp = 0,
    SupportType = 0,
    SupportCount = 0,
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    VehicleID = 0,
    EnemyDisScore = 0,
    EnemyOcclusionScore = 0,
    EnemyFireScore = 0,
    MateDisScore = 0,
    AnchorDisScore = 0,
    SelectedMateDisScore = 0,
    DeadMateScore = 0,
    OtherMateScore = 0,
}
pb.__pb_TDMSectorSupportFlow.__name = "TDMSectorSupportFlow"
pb.__pb_TDMSectorSupportFlow.__index = pb.__pb_TDMSectorSupportFlow
pb.__pb_TDMSectorSupportFlow.__pairs = __pb_pairs

pb.TDMSectorSupportFlow = { __name = "TDMSectorSupportFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorSupportFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public Camp number
---@field public SupportType number
---@field public SupportCount number
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public VehicleID number
---@field public EnemyDisScore number
---@field public EnemyOcclusionScore number
---@field public EnemyFireScore number
---@field public MateDisScore number
---@field public AnchorDisScore number
---@field public SelectedMateDisScore number
---@field public DeadMateScore number
---@field public OtherMateScore number

---@return pb_TDMSectorSupportFlow
function pb.TDMSectorSupportFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorBattleKillFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    Camp = 0,
    KillerType = 0,
    KilledType = 0,
    KillerBodyStatus = 0,
    KillerCarrierType = 0,
    KilledCarrierType = 0,
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
}
pb.__pb_TDMSectorBattleKillFlow.__name = "TDMSectorBattleKillFlow"
pb.__pb_TDMSectorBattleKillFlow.__index = pb.__pb_TDMSectorBattleKillFlow
pb.__pb_TDMSectorBattleKillFlow.__pairs = __pb_pairs

pb.TDMSectorBattleKillFlow = { __name = "TDMSectorBattleKillFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorBattleKillFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public Camp number
---@field public KillerType number
---@field public KilledType number
---@field public KillerBodyStatus number
---@field public KillerCarrierType number
---@field public KilledCarrierType number
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number

---@return pb_TDMSectorBattleKillFlow
function pb.TDMSectorBattleKillFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorBattleAssistFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    Camp = 0,
    AssistantType = 0,
    KilledType = 0,
    AssistantBodyStatus = 0,
    AssistantCarrierType = 0,
    KilledCarrierType = 0,
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
}
pb.__pb_TDMSectorBattleAssistFlow.__name = "TDMSectorBattleAssistFlow"
pb.__pb_TDMSectorBattleAssistFlow.__index = pb.__pb_TDMSectorBattleAssistFlow
pb.__pb_TDMSectorBattleAssistFlow.__pairs = __pb_pairs

pb.TDMSectorBattleAssistFlow = { __name = "TDMSectorBattleAssistFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorBattleAssistFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public Camp number
---@field public AssistantType number
---@field public KilledType number
---@field public AssistantBodyStatus number
---@field public AssistantCarrierType number
---@field public KilledCarrierType number
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number

---@return pb_TDMSectorBattleAssistFlow
function pb.TDMSectorBattleAssistFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_Ds2DsaPkgStatis = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PkgNmae = "",
    PkgMaxLength = 0,
}
pb.__pb_Ds2DsaPkgStatis.__name = "Ds2DsaPkgStatis"
pb.__pb_Ds2DsaPkgStatis.__index = pb.__pb_Ds2DsaPkgStatis
pb.__pb_Ds2DsaPkgStatis.__pairs = __pb_pairs

pb.Ds2DsaPkgStatis = { __name = "Ds2DsaPkgStatis", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_Ds2DsaPkgStatis : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PkgNmae string
---@field public PkgMaxLength number

---@return pb_Ds2DsaPkgStatis
function pb.Ds2DsaPkgStatis:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsShopStationBuyRecord = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerID = 0,
    BuyTime = 0,
    ItemID = 0,
    ItemNum = 0,
    TotalPrices = 0,
    GameTime = 0,
    ShooterLevel = 0,
    RoomID = 0,
    MapId = 0,
}
pb.__pb_DsShopStationBuyRecord.__name = "DsShopStationBuyRecord"
pb.__pb_DsShopStationBuyRecord.__index = pb.__pb_DsShopStationBuyRecord
pb.__pb_DsShopStationBuyRecord.__pairs = __pb_pairs

pb.DsShopStationBuyRecord = { __name = "DsShopStationBuyRecord", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsShopStationBuyRecord : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerID number
---@field public BuyTime number
---@field public ItemID number
---@field public ItemNum number
---@field public TotalPrices number
---@field public GameTime number
---@field public ShooterLevel number
---@field public RoomID number
---@field public MapId number

---@return pb_DsShopStationBuyRecord
function pb.DsShopStationBuyRecord:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSolCharacterRebornLogData = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    CharacterDeathNum = 0,
    DeathToRebornTime = 0,
    RebornAliveTime = 0,
    IsRetreat = 0,
    MatchStartPriceValue = 0,
    MatchEndPriceValue = 0,
    TeamID = 0,
    TeamPlayerNum = 0,
    GameTime = 0,
    ShooterLevel = 0,
    RoomID = 0,
    MapId = 0,
    PlayerImpendingDeathNum = 0,
    ImpendingDeathToDeathFastNum = 0,
    ImpendingDeathGiveUpNum = 0,
    ImpendingDeathRescueNum = 0,
    ImpendingDeathForHelpNum = 0,
    DeathForHelpNum = 0,
    PlayerCarryNum = 0,
    PlayerCarryAINum = 0,
    PlayerCarryTeamMemberNum = 0,
    PlayerCarryPlayerNum = 0,
    PlayerID = 0,
}
pb.__pb_DsSolCharacterRebornLogData.__name = "DsSolCharacterRebornLogData"
pb.__pb_DsSolCharacterRebornLogData.__index = pb.__pb_DsSolCharacterRebornLogData
pb.__pb_DsSolCharacterRebornLogData.__pairs = __pb_pairs

pb.DsSolCharacterRebornLogData = { __name = "DsSolCharacterRebornLogData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSolCharacterRebornLogData : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public CharacterDeathNum number
---@field public DeathToRebornTime number
---@field public RebornAliveTime number
---@field public IsRetreat number
---@field public MatchStartPriceValue number
---@field public MatchEndPriceValue number
---@field public TeamID number
---@field public TeamPlayerNum number
---@field public GameTime number
---@field public ShooterLevel number
---@field public RoomID number
---@field public MapId number
---@field public PlayerImpendingDeathNum number
---@field public ImpendingDeathToDeathFastNum number
---@field public ImpendingDeathGiveUpNum number
---@field public ImpendingDeathRescueNum number
---@field public ImpendingDeathForHelpNum number
---@field public DeathForHelpNum number
---@field public PlayerCarryNum number
---@field public PlayerCarryAINum number
---@field public PlayerCarryTeamMemberNum number
---@field public PlayerCarryPlayerNum number
---@field public PlayerID number

---@return pb_DsSolCharacterRebornLogData
function pb.DsSolCharacterRebornLogData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SolMandelBrickAnalysisData = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    IsSpawn = 0,
    IsPickup = 0,
    IsTakeOut = 0,
    FinalState = 0,
    LifeTime = 0,
    AllEngagedPlayerNum = 0,
    AllDeadPlayerNum = 0,
    BeAppeared_LifeTime = 0,
    BeAppeared_EngagedPlayerNum = 0,
    BeAppeared_DeadPlayerNum = 0,
    BeingWanted_LifeTime = 0,
    BeingWanted_EngagedPlayerNum = 0,
    BeingWanted_DeadPlayerNum = 0,
    BeingWanted_TransferPlayerNum = 0,
    BeingWanted_TransferTeamNum = 0,
    Deciphering_LifeTime = 0,
    Deciphering_EngagedPlayerNum = 0,
    Deciphering_DeadPlayerNum = 0,
    Deciphering_TransferPlayerNum = 0,
    Deciphering_TransferTeamNum = 0,
    RoomID = 0,
    MapID = 0,
    EngageRate = 0,
    EngagedNum = 0,
    BeAppeared_DeadNumNew = 0,
    BeingWanted_DeadNumNew = 0,
    Deciphering_DeadNumNew = 0,
    FirstPickUp_AllPlayerNum = 0,
    FirstPickUp_AllTeamNum = 0,
    FirstDecipher_AllPlayerNum = 0,
    FirstDecipher_AllTeamNum = 0,
    ManualDiscardCount = 0,
    FirstPickUpAndFirstDecipherSameTeam = 0,
    FirstDecipherAndTakeOutSameTeam = 0,
    FirstPickUpAndTakeOutSameTeam = 0,
}
pb.__pb_SolMandelBrickAnalysisData.__name = "SolMandelBrickAnalysisData"
pb.__pb_SolMandelBrickAnalysisData.__index = pb.__pb_SolMandelBrickAnalysisData
pb.__pb_SolMandelBrickAnalysisData.__pairs = __pb_pairs

pb.SolMandelBrickAnalysisData = { __name = "SolMandelBrickAnalysisData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SolMandelBrickAnalysisData : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public IsSpawn number
---@field public IsPickup number
---@field public IsTakeOut number
---@field public FinalState number
---@field public LifeTime number
---@field public AllEngagedPlayerNum number
---@field public AllDeadPlayerNum number
---@field public BeAppeared_LifeTime number
---@field public BeAppeared_EngagedPlayerNum number
---@field public BeAppeared_DeadPlayerNum number
---@field public BeingWanted_LifeTime number
---@field public BeingWanted_EngagedPlayerNum number
---@field public BeingWanted_DeadPlayerNum number
---@field public BeingWanted_TransferPlayerNum number
---@field public BeingWanted_TransferTeamNum number
---@field public Deciphering_LifeTime number
---@field public Deciphering_EngagedPlayerNum number
---@field public Deciphering_DeadPlayerNum number
---@field public Deciphering_TransferPlayerNum number
---@field public Deciphering_TransferTeamNum number
---@field public RoomID number
---@field public MapID number
---@field public EngageRate number
---@field public EngagedNum number
---@field public BeAppeared_DeadNumNew number
---@field public BeingWanted_DeadNumNew number
---@field public Deciphering_DeadNumNew number
---@field public FirstPickUp_AllPlayerNum number
---@field public FirstPickUp_AllTeamNum number
---@field public FirstDecipher_AllPlayerNum number
---@field public FirstDecipher_AllTeamNum number
---@field public ManualDiscardCount number
---@field public FirstPickUpAndFirstDecipherSameTeam number
---@field public FirstDecipherAndTakeOutSameTeam number
---@field public FirstPickUpAndTakeOutSameTeam number

---@return pb_SolMandelBrickAnalysisData
function pb.SolMandelBrickAnalysisData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSolBlueprintWeaponAnalysisData = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    IsPickup = 0,
    FirstPickupTeamId = 0,
    FirstPickupAvgLevel = 0,
    TransferCount = 0,
    FightTeamCount = 0,
    IsTakeOut = 0,
    LifeTime = 0,
    TakeOutTeamId = 0,
    TakeOutAvgLevel = 0,
    RoomID = 0,
    MapID = 0,
}
pb.__pb_DsSolBlueprintWeaponAnalysisData.__name = "DsSolBlueprintWeaponAnalysisData"
pb.__pb_DsSolBlueprintWeaponAnalysisData.__index = pb.__pb_DsSolBlueprintWeaponAnalysisData
pb.__pb_DsSolBlueprintWeaponAnalysisData.__pairs = __pb_pairs

pb.DsSolBlueprintWeaponAnalysisData = { __name = "DsSolBlueprintWeaponAnalysisData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSolBlueprintWeaponAnalysisData : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public IsPickup number
---@field public FirstPickupTeamId number
---@field public FirstPickupAvgLevel number
---@field public TransferCount number
---@field public FightTeamCount number
---@field public IsTakeOut number
---@field public LifeTime number
---@field public TakeOutTeamId number
---@field public TakeOutAvgLevel number
---@field public RoomID number
---@field public MapID number

---@return pb_DsSolBlueprintWeaponAnalysisData
function pb.DsSolBlueprintWeaponAnalysisData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsContractReportData = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    PlayerID = 0,
    QuestID = 0,
    ContractReportType = 0,
    PreProcessingTime = 0,
    ProcessingTime = 0,
    FailureStep = 0,
    ContractRewards = 0,
    FinishContractTime = "",
    RecivedPos = "",
    ReciveTotalPrice = 0,
    DamageToAI = 0,
    DamageToPlayers = 0,
    DamagedByAI = 0,
    DamagedByPlayers = 0,
    KillAICount = 0,
    KillPlayerCount = 0,
    CompleteTotalPrice = 0,
    HasEnterQuestVolume = 0,
}
pb.__pb_DsContractReportData.__name = "DsContractReportData"
pb.__pb_DsContractReportData.__index = pb.__pb_DsContractReportData
pb.__pb_DsContractReportData.__pairs = __pb_pairs

pb.DsContractReportData = { __name = "DsContractReportData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsContractReportData : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public PlayerID number
---@field public QuestID number
---@field public ContractReportType number
---@field public PreProcessingTime number
---@field public ProcessingTime number
---@field public FailureStep number
---@field public ContractRewards number
---@field public FinishContractTime string
---@field public RecivedPos string
---@field public ReciveTotalPrice number
---@field public DamageToAI number
---@field public DamageToPlayers number
---@field public DamagedByAI number
---@field public DamagedByPlayers number
---@field public KillAICount number
---@field public KillPlayerCount number
---@field public CompleteTotalPrice number
---@field public HasEnterQuestVolume number

---@return pb_DsContractReportData
function pb.DsContractReportData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsWeaponKillInfos = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    ShooterId = 0,
    ShooterLoc = "",
    ShooterWeaponID = 0,
    ShooterWeaponGID = 0,
    ShooterComponentList = "",
    ShooterAmmoID = 0,
    ShootedId = 0,
    ShootedPlayerType = 0,
    ShootedHP = 0,
    ShootedArmor = 0,
    ShootedHelmetID = 0,
    ShootedArmorID = 0,
    ShootedDistance = 0,
    ShootedLoc = "",
    ShootedWeaponID = 0,
    ShootedWeaponGID = 0,
    ShootedComponentList = "",
    ConsumeBulletNum = 0,
    HitBulletNum = 0,
    Duration = 0,
    LegHitCnt = 0,
    LegUpperHitCnt = 0,
    ArmHitCnt = 0,
    ArmUpperHitCnt = 0,
    HeadHitCnt = 0,
    ThorxHitCnt = 0,
    ThorxUpperHitCnt = 0,
    ShooterArmedforceID = 0,
    ShootDamage = 0,
    ShootArmorDamage = 0,
    ShootedAmmoID = 0,
    IsShooterInVehicle = 0,
    IsShootedInVehicle = 0,
    IsShootedInRescue = 0,
    IsShootedRevived5S = 0,
    ShooterArmorID = 0,
    ShooterHelmetID = 0,
    ShooterArmorDurability = 0,
    ShooterHelmetDurability = 0,
    ShootedHelmetDurability = 0,
    ShootHelmetDamage = 0,
    ArmorDurabilityDamage = 0,
    HelmetDurabilityDamage = 0,
    ValidBodyDamage = 0,
    SectorId = 0,
    ShooterMaxHP = 0,
    ShootedMaxHP = 0,
    IsZoom = 0,
    IsOpenLight = 0,
    IsOpenVisitNight = 0,
    IsValidKill = 0,
    IsGamepad = 0,
}
pb.__pb_DsWeaponKillInfos.__name = "DsWeaponKillInfos"
pb.__pb_DsWeaponKillInfos.__index = pb.__pb_DsWeaponKillInfos
pb.__pb_DsWeaponKillInfos.__pairs = __pb_pairs

pb.DsWeaponKillInfos = { __name = "DsWeaponKillInfos", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsWeaponKillInfos : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public ShooterId number
---@field public ShooterLoc string
---@field public ShooterWeaponID number
---@field public ShooterWeaponGID number
---@field public ShooterComponentList string
---@field public ShooterAmmoID number
---@field public ShootedId number
---@field public ShootedPlayerType number
---@field public ShootedHP number
---@field public ShootedArmor number
---@field public ShootedHelmetID number
---@field public ShootedArmorID number
---@field public ShootedDistance number
---@field public ShootedLoc string
---@field public ShootedWeaponID number
---@field public ShootedWeaponGID number
---@field public ShootedComponentList string
---@field public ConsumeBulletNum number
---@field public HitBulletNum number
---@field public Duration number
---@field public LegHitCnt number
---@field public LegUpperHitCnt number
---@field public ArmHitCnt number
---@field public ArmUpperHitCnt number
---@field public HeadHitCnt number
---@field public ThorxHitCnt number
---@field public ThorxUpperHitCnt number
---@field public ShooterArmedforceID number
---@field public ShootDamage number
---@field public ShootArmorDamage number
---@field public ShootedAmmoID number
---@field public IsShooterInVehicle number
---@field public IsShootedInVehicle number
---@field public IsShootedInRescue number
---@field public IsShootedRevived5S number
---@field public ShooterArmorID number
---@field public ShooterHelmetID number
---@field public ShooterArmorDurability number
---@field public ShooterHelmetDurability number
---@field public ShootedHelmetDurability number
---@field public ShootHelmetDamage number
---@field public ArmorDurabilityDamage number
---@field public HelmetDurabilityDamage number
---@field public ValidBodyDamage number
---@field public SectorId number
---@field public ShooterMaxHP number
---@field public ShootedMaxHP number
---@field public IsZoom number
---@field public IsOpenLight number
---@field public IsOpenVisitNight number
---@field public IsValidKill number
---@field public IsGamepad number

---@return pb_DsWeaponKillInfos
function pb.DsWeaponKillInfos:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsBattleFieldWeaponKillInfos = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    ShooterId = 0,
    ShooterLoc = "",
    ShooterWeaponID = 0,
    ShooterWeaponGID = 0,
    ShooterComponentList = "",
    ShooterAmmoID = 0,
    ShootedId = 0,
    ShootedPlayerType = 0,
    ShootedHP = 0,
    ShootedDistance = 0,
    ShootedLoc = "",
    ShootedWeaponID = 0,
    ShootedWeaponGID = 0,
    ShootedComponentList = "",
    ConsumeBulletNum = 0,
    HitBulletNum = 0,
    Duration = 0,
    LegHitCnt = 0,
    LegUpperHitCnt = 0,
    ArmHitCnt = 0,
    ArmUpperHitCnt = 0,
    HeadHitCnt = 0,
    ThorxHitCnt = 0,
    ThorxUpperHitCnt = 0,
    ShooterArmedforceID = 0,
    ShootDamage = 0,
    ShootArmorDamage = 0,
    ShootedAmmoID = 0,
    IsShooterInVehicle = 0,
    IsShootedInVehicle = 0,
    IsShootedInRescue = 0,
    IsShootedRevived5S = 0,
    ValidBodyDamage = 0,
    SectorId = 0,
    VehicleCairrierID = 0,
    KillerCampID = 0,
    KilledCampID = 0,
    ShooterHasArmorPlate = 0,
    ShootedHasArmorPlate = 0,
    IsValidKill = 0,
    IsZoom = 0,
    IsOpenLight = 0,
    IsOpenVisitNight = 0,
    KillerBotType = 0,
    KilledBotType = 0,
    IsGamepad = 0,
}
pb.__pb_DsBattleFieldWeaponKillInfos.__name = "DsBattleFieldWeaponKillInfos"
pb.__pb_DsBattleFieldWeaponKillInfos.__index = pb.__pb_DsBattleFieldWeaponKillInfos
pb.__pb_DsBattleFieldWeaponKillInfos.__pairs = __pb_pairs

pb.DsBattleFieldWeaponKillInfos = { __name = "DsBattleFieldWeaponKillInfos", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsBattleFieldWeaponKillInfos : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public ShooterId number
---@field public ShooterLoc string
---@field public ShooterWeaponID number
---@field public ShooterWeaponGID number
---@field public ShooterComponentList string
---@field public ShooterAmmoID number
---@field public ShootedId number
---@field public ShootedPlayerType number
---@field public ShootedHP number
---@field public ShootedDistance number
---@field public ShootedLoc string
---@field public ShootedWeaponID number
---@field public ShootedWeaponGID number
---@field public ShootedComponentList string
---@field public ConsumeBulletNum number
---@field public HitBulletNum number
---@field public Duration number
---@field public LegHitCnt number
---@field public LegUpperHitCnt number
---@field public ArmHitCnt number
---@field public ArmUpperHitCnt number
---@field public HeadHitCnt number
---@field public ThorxHitCnt number
---@field public ThorxUpperHitCnt number
---@field public ShooterArmedforceID number
---@field public ShootDamage number
---@field public ShootArmorDamage number
---@field public ShootedAmmoID number
---@field public IsShooterInVehicle number
---@field public IsShootedInVehicle number
---@field public IsShootedInRescue number
---@field public IsShootedRevived5S number
---@field public ValidBodyDamage number
---@field public SectorId number
---@field public VehicleCairrierID number
---@field public KillerCampID number
---@field public KilledCampID number
---@field public ShooterHasArmorPlate number
---@field public ShootedHasArmorPlate number
---@field public IsValidKill number
---@field public IsZoom number
---@field public IsOpenLight number
---@field public IsOpenVisitNight number
---@field public KillerBotType number
---@field public KilledBotType number
---@field public IsGamepad number

---@return pb_DsBattleFieldWeaponKillInfos
function pb.DsBattleFieldWeaponKillInfos:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsCharacterTakeDamageEveryGame = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    PlayerId = 0,
    TotalDamage = 0,
    TotalDamageHp = 0,
    TotalDamageArmor = 0,
}
pb.__pb_DsCharacterTakeDamageEveryGame.__name = "DsCharacterTakeDamageEveryGame"
pb.__pb_DsCharacterTakeDamageEveryGame.__index = pb.__pb_DsCharacterTakeDamageEveryGame
pb.__pb_DsCharacterTakeDamageEveryGame.__pairs = __pb_pairs

pb.DsCharacterTakeDamageEveryGame = { __name = "DsCharacterTakeDamageEveryGame", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsCharacterTakeDamageEveryGame : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public PlayerId number
---@field public TotalDamage number
---@field public TotalDamageHp number
---@field public TotalDamageArmor number

---@return pb_DsCharacterTakeDamageEveryGame
function pb.DsCharacterTakeDamageEveryGame:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ArmedPropUseCnt = 0,
    PassiveSkillUseCnt = 0,
    ActiveSkillUseCnt = 0,
    ArmedPropEffectValue = 0,
    ArmedPropEffectPlayer = 0,
    ArmedPropEffectAI = 0,
    ActiveSkillEffectValue = 0,
    ActiveSkillEffectPlayer = 0,
    ActiveSkillEffectAI = 0,
}
pb.__pb_ArmedforceDataFlow.__name = "ArmedforceDataFlow"
pb.__pb_ArmedforceDataFlow.__index = pb.__pb_ArmedforceDataFlow
pb.__pb_ArmedforceDataFlow.__pairs = __pb_pairs

pb.ArmedforceDataFlow = { __name = "ArmedforceDataFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ArmedPropUseCnt number
---@field public PassiveSkillUseCnt number
---@field public ActiveSkillUseCnt number
---@field public ArmedPropEffectValue number
---@field public ArmedPropEffectPlayer number
---@field public ArmedPropEffectAI number
---@field public ActiveSkillEffectValue number
---@field public ActiveSkillEffectPlayer number
---@field public ActiveSkillEffectAI number

---@return pb_ArmedforceDataFlow
function pb.ArmedforceDataFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowAssault = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillUseCnt = 0,
    PassiveSkillUseTime = 0,
    PassiveSkillBeKill = 0,
    Hit2SPassiveSkillUseCnt = 0,
    ArmedPropUseCnt = 0,
    ArmedPropUse2FireTime = 0,
    ArmedPropFireCnt = 0,
    ArmedPropKnockDownPlayerCnt = 0,
    ArmedPropHitDistance = 0,
    ArmedPropDestroyVehicleCnt = 0,
    ArmedPropHitVehicleCnt = 0,
    ArmedPropHitPlayerCnt = 0,
    ArmedPropHitPlayerNum = 0,
    ActiveSkillUseCnt = 0,
    ActiveSkillUseTime = 0,
    ActiveSkillKillPlayerCnt = 0,
    ActiveSkillKillAICnt = 0,
    AntiTankGrenadeTotalDamage = 0,
    AntiTankGrenadeBullet1TotalDamage = 0,
    AntiTankGrenadeBullet2TotalDamage = 0,
    AntiTankGrenadeBullet3TotalDamage = 0,
    AntiTankGrenadeHitVehicleCnt = 0,
    AntiTankGrenadeHitVehicleAvgDamage = 0,
    AntiTankGrenadeDestroyVehicleCnt = 0,
    SmokeResuceCntIn15S = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    AntiPersonnelFireCount = 0,
    AntiPersonnelKillCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowAssault.__name = "ArmedforceDataFlowAssault"
pb.__pb_ArmedforceDataFlowAssault.__index = pb.__pb_ArmedforceDataFlowAssault
pb.__pb_ArmedforceDataFlowAssault.__pairs = __pb_pairs

pb.ArmedforceDataFlowAssault = { __name = "ArmedforceDataFlowAssault", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowAssault : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillUseCnt number
---@field public PassiveSkillUseTime number
---@field public PassiveSkillBeKill number
---@field public Hit2SPassiveSkillUseCnt number
---@field public ArmedPropUseCnt number
---@field public ArmedPropUse2FireTime number
---@field public ArmedPropFireCnt number
---@field public ArmedPropKnockDownPlayerCnt number
---@field public ArmedPropHitDistance number
---@field public ArmedPropDestroyVehicleCnt number
---@field public ArmedPropHitVehicleCnt number
---@field public ArmedPropHitPlayerCnt number
---@field public ArmedPropHitPlayerNum number
---@field public ActiveSkillUseCnt number
---@field public ActiveSkillUseTime number
---@field public ActiveSkillKillPlayerCnt number
---@field public ActiveSkillKillAICnt number
---@field public AntiTankGrenadeTotalDamage number
---@field public AntiTankGrenadeBullet1TotalDamage number
---@field public AntiTankGrenadeBullet2TotalDamage number
---@field public AntiTankGrenadeBullet3TotalDamage number
---@field public AntiTankGrenadeHitVehicleCnt number
---@field public AntiTankGrenadeHitVehicleAvgDamage number
---@field public AntiTankGrenadeDestroyVehicleCnt number
---@field public SmokeResuceCntIn15S number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public AntiPersonnelFireCount number
---@field public AntiPersonnelKillCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowAssault
function pb.ArmedforceDataFlowAssault:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowClaire = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillUseCnt = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    AntiPersonnelFireCount = 0,
    AntiPersonnelKillCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    RopeSkillUseCount = 0,
    RopeSkillManualPullCount = 0,
    RopeSkillAutoPullCount = 0,
    RopeSkillDeadCountWhenPull = 0,
    RopeSkillDestoryedCount = 0,
    RopeSkillMoveDis = 0,
    RopeSkillOverDisCount = 0,
    RollCount = 0,
    DeadCountWhenRoll = 0,
    ElectricShockBombUseCount = 0,
    ElectricShockBombAffectedCount = 0,
    ElectricShockBombBoxAffectedCount = 0,
    ElectricShockBombSphereAffectedCount = 0,
    ElectricShockAssistCount = 0,
    ElectricShockThrowDis = 0,
    PassiveTriggerCount = 0,
    PassiveTriggerCountBullet = 0,
    PassiveTriggerCountExplosion = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowClaire.__name = "ArmedforceDataFlowClaire"
pb.__pb_ArmedforceDataFlowClaire.__index = pb.__pb_ArmedforceDataFlowClaire
pb.__pb_ArmedforceDataFlowClaire.__pairs = __pb_pairs

pb.ArmedforceDataFlowClaire = { __name = "ArmedforceDataFlowClaire", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowClaire : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillUseCnt number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public AntiPersonnelFireCount number
---@field public AntiPersonnelKillCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public RopeSkillUseCount number
---@field public RopeSkillManualPullCount number
---@field public RopeSkillAutoPullCount number
---@field public RopeSkillDeadCountWhenPull number
---@field public RopeSkillDestoryedCount number
---@field public RopeSkillMoveDis number
---@field public RopeSkillOverDisCount number
---@field public RollCount number
---@field public DeadCountWhenRoll number
---@field public ElectricShockBombUseCount number
---@field public ElectricShockBombAffectedCount number
---@field public ElectricShockBombBoxAffectedCount number
---@field public ElectricShockBombSphereAffectedCount number
---@field public ElectricShockAssistCount number
---@field public ElectricShockThrowDis number
---@field public PassiveTriggerCount number
---@field public PassiveTriggerCountBullet number
---@field public PassiveTriggerCountExplosion number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowClaire
function pb.ArmedforceDataFlowClaire:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowSupport = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillRescueSuceesCnt = 0,
    PassiveSkillRescueCnt = 0,
    PassiveSkillRescueFailedTime = 0,
    ArmedPropFireCnt = 0,
    Hit5SArmedPropFireCnt = 0,
    ArmedProp5SPassiveSkillRescueCnt = 0,
    ArmedProp5SKillEnermyCnt = 0,
    ArmedProp5SBeKillCnt = 0,
    ActiveSkillUseCnt = 0,
    ActiveSkillUse2FireTime = 0,
    Hit5SSelfCureCnt = 0,
    SelfCureCnt = 0,
    ActiveSkillEffectPlayerNum = 0,
    WeaponFireCnt = 0,
    SelfCureHealth = 0,
    TeammateCureHealth = 0,
    SmokeResuceCntIn15S = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    PassiveSkillUseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowSupport.__name = "ArmedforceDataFlowSupport"
pb.__pb_ArmedforceDataFlowSupport.__index = pb.__pb_ArmedforceDataFlowSupport
pb.__pb_ArmedforceDataFlowSupport.__pairs = __pb_pairs

pb.ArmedforceDataFlowSupport = { __name = "ArmedforceDataFlowSupport", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowSupport : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillRescueSuceesCnt number
---@field public PassiveSkillRescueCnt number
---@field public PassiveSkillRescueFailedTime number
---@field public ArmedPropFireCnt number
---@field public Hit5SArmedPropFireCnt number
---@field public ArmedProp5SPassiveSkillRescueCnt number
---@field public ArmedProp5SKillEnermyCnt number
---@field public ArmedProp5SBeKillCnt number
---@field public ActiveSkillUseCnt number
---@field public ActiveSkillUse2FireTime number
---@field public Hit5SSelfCureCnt number
---@field public SelfCureCnt number
---@field public ActiveSkillEffectPlayerNum number
---@field public WeaponFireCnt number
---@field public SelfCureHealth number
---@field public TeammateCureHealth number
---@field public SmokeResuceCntIn15S number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public PassiveSkillUseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowSupport
function pb.ArmedforceDataFlowSupport:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowScout = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillMarkEnermyNum = 0,
    ArmedPropUseCnt = 0,
    ArmedPropUse2FireTime = 0,
    ArmedPropFireCnt = 0,
    ArmedPropKnockDownNum = 0,
    ArmedPropHitDistance = 0,
    ArmedPropDestroyVehcleCnt = 0,
    ArmedPropHitVehcleCnt = 0,
    ArmedPropHitPlayerCnt = 0,
    ArmedPropHitPlayerNum = 0,
    ActiveSkillFireCnt = 0,
    ActiveSkillUseCnt = 0,
    ActiveSkillEffectEnermyNum = 0,
    ActiveSkillEffectPlayerNum = 0,
    ActiveSkillEffectAINum = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    PassiveSkillUseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    LaserPointerLockCompleteNum = 0,
    LaserPointerAssistNum = 0,
    RebornFlagUseNum = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowScout.__name = "ArmedforceDataFlowScout"
pb.__pb_ArmedforceDataFlowScout.__index = pb.__pb_ArmedforceDataFlowScout
pb.__pb_ArmedforceDataFlowScout.__pairs = __pb_pairs

pb.ArmedforceDataFlowScout = { __name = "ArmedforceDataFlowScout", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowScout : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillMarkEnermyNum number
---@field public ArmedPropUseCnt number
---@field public ArmedPropUse2FireTime number
---@field public ArmedPropFireCnt number
---@field public ArmedPropKnockDownNum number
---@field public ArmedPropHitDistance number
---@field public ArmedPropDestroyVehcleCnt number
---@field public ArmedPropHitVehcleCnt number
---@field public ArmedPropHitPlayerCnt number
---@field public ArmedPropHitPlayerNum number
---@field public ActiveSkillFireCnt number
---@field public ActiveSkillUseCnt number
---@field public ActiveSkillEffectEnermyNum number
---@field public ActiveSkillEffectPlayerNum number
---@field public ActiveSkillEffectAINum number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public PassiveSkillUseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public LaserPointerLockCompleteNum number
---@field public LaserPointerAssistNum number
---@field public RebornFlagUseNum number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowScout
function pb.ArmedforceDataFlowScout:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowEngineer = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillEffectValue = 0,
    PassiveSkillEffectCnt = 0,
    FixVehicleAndWeaponCnt = 0,
    ArmedPropUseCnt = 0,
    ArmedPropUse2FireTime = 0,
    ArmedPropBeDestroyCnt = 0,
    ArmedPropActivatedNum = 0,
    ArmedPropKillNum = 0,
    ArmedPropPlacementCnt = 0,
    ActiveSkillWave1EffectNum = 0,
    ActiveSkillWave2EffectNum = 0,
    ActiveSkillWave3EffectNum = 0,
    ActiveSkillWave4EffectNum = 0,
    ActiveSkillDetectDefense = 0,
    ActiveSkillUseCnt = 0,
    ActiveSkillKillPlayerNum = 0,
    ActiveSkillKillAINum = 0,
    CampVehicleAvgFixCnt = 0,
    CampVehicleAvgFixHealth = 0,
    CampVehicleAvgFixTime = 0,
    WeaponAvgFixCnt = 0,
    WeaponAvgFixHealth = 0,
    WeaponAvgFixTime = 0,
    UseActiveAbilityClickCnt = 0,
    UseActiveAbilityLongPressCnt = 0,
    BeKillActivateMineIn8S = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    PassiveSkillUseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    ADSDefenceCount = 0,
    AT4DestoryVehicleCount = 0,
    SpearDestoryVehicleCount = 0,
    StingerDestoryVehicleCount = 0,
    AT4DamageCount = 0,
    SpearDamageCount = 0,
    StingerDamageCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    ADSCoolDownCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowEngineer.__name = "ArmedforceDataFlowEngineer"
pb.__pb_ArmedforceDataFlowEngineer.__index = pb.__pb_ArmedforceDataFlowEngineer
pb.__pb_ArmedforceDataFlowEngineer.__pairs = __pb_pairs

pb.ArmedforceDataFlowEngineer = { __name = "ArmedforceDataFlowEngineer", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowEngineer : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillEffectValue number
---@field public PassiveSkillEffectCnt number
---@field public FixVehicleAndWeaponCnt number
---@field public ArmedPropUseCnt number
---@field public ArmedPropUse2FireTime number
---@field public ArmedPropBeDestroyCnt number
---@field public ArmedPropActivatedNum number
---@field public ArmedPropKillNum number
---@field public ArmedPropPlacementCnt number
---@field public ActiveSkillWave1EffectNum number
---@field public ActiveSkillWave2EffectNum number
---@field public ActiveSkillWave3EffectNum number
---@field public ActiveSkillWave4EffectNum number
---@field public ActiveSkillDetectDefense number
---@field public ActiveSkillUseCnt number
---@field public ActiveSkillKillPlayerNum number
---@field public ActiveSkillKillAINum number
---@field public CampVehicleAvgFixCnt number
---@field public CampVehicleAvgFixHealth number
---@field public CampVehicleAvgFixTime number
---@field public WeaponAvgFixCnt number
---@field public WeaponAvgFixHealth number
---@field public WeaponAvgFixTime number
---@field public UseActiveAbilityClickCnt number
---@field public UseActiveAbilityLongPressCnt number
---@field public BeKillActivateMineIn8S number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public PassiveSkillUseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public ADSDefenceCount number
---@field public AT4DestoryVehicleCount number
---@field public SpearDestoryVehicleCount number
---@field public StingerDestoryVehicleCount number
---@field public AT4DamageCount number
---@field public SpearDamageCount number
---@field public StingerDamageCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public ADSCoolDownCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowEngineer
function pb.ArmedforceDataFlowEngineer:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowWolfWarriors = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveActivateCnt = 0,
    PassiveActivateCntTryCount = 0,
    FallReduceDamageCnt = 0,
    FallReduceDamageCnt1 = 0,
    FallReduceDamageCnt2 = 0,
    FallDamageBreakBoneCnt = 0,
    FireAirCannonCnt = 0,
    AirCannonHitPlayerCnt = 0,
    AirCannonHitPlayerDistance = 0,
    AirCannonHitAICnt = 0,
    AirCannonKnockbackKillCnt = 0,
    AirCannonKnockbackBeKillCnt = 0,
    AirCannonKnockbackMultiPlayerCnt = 0,
    RushForwardCnt = 0,
    RushBackCnt = 0,
    RushLeftCnt = 0,
    RushRightCnt = 0,
    RushCnt = 0,
    RushBeHit2S = 0,
    UseRushCntInCDAwards2S = 0,
    BekillRush2S = 0,
    BekillRushing = 0,
    ThrowC4Cnt = 0,
    C4KillPlayerCnt = 0,
    C4KillAICnt = 0,
    C4HitPlayerCnt = 0,
    C4HitAICnt = 0,
    C4HitVehicle = 0,
    C4DestroyVehicle = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    PassiveSkillUseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowWolfWarriors.__name = "ArmedforceDataFlowWolfWarriors"
pb.__pb_ArmedforceDataFlowWolfWarriors.__index = pb.__pb_ArmedforceDataFlowWolfWarriors
pb.__pb_ArmedforceDataFlowWolfWarriors.__pairs = __pb_pairs

pb.ArmedforceDataFlowWolfWarriors = { __name = "ArmedforceDataFlowWolfWarriors", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowWolfWarriors : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveActivateCnt number
---@field public PassiveActivateCntTryCount number
---@field public FallReduceDamageCnt number
---@field public FallReduceDamageCnt1 number
---@field public FallReduceDamageCnt2 number
---@field public FallDamageBreakBoneCnt number
---@field public FireAirCannonCnt number
---@field public AirCannonHitPlayerCnt number
---@field public AirCannonHitPlayerDistance number
---@field public AirCannonHitAICnt number
---@field public AirCannonKnockbackKillCnt number
---@field public AirCannonKnockbackBeKillCnt number
---@field public AirCannonKnockbackMultiPlayerCnt number
---@field public RushForwardCnt number
---@field public RushBackCnt number
---@field public RushLeftCnt number
---@field public RushRightCnt number
---@field public RushCnt number
---@field public RushBeHit2S number
---@field public UseRushCntInCDAwards2S number
---@field public BekillRush2S number
---@field public BekillRushing number
---@field public ThrowC4Cnt number
---@field public C4KillPlayerCnt number
---@field public C4KillAICnt number
---@field public C4HitPlayerCnt number
---@field public C4HitAICnt number
---@field public C4HitVehicle number
---@field public C4DestroyVehicle number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public PassiveSkillUseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowWolfWarriors
function pb.ArmedforceDataFlowWolfWarriors:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowDavid = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    PassiveSkillUseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    CruiseMissileAimOnFire = 0,
    CruiseMissileAimOffFire = 0,
    CruiseMissileMainHitTerrestrial = 0,
    CruiseMissileMainHitAerial = 0,
    CruiseMissileMainExplosionDamageToTerrestrial = 0,
    CruiseMissileMainExplosionDamageToAerial = 0,
    CruiseMissileMainDestroyTerrestrial = 0,
    CruiseMissileMainDestroyAerial = 0,
    CruiseMissileMainHitPlayerBeforeAcc = 0,
    CruiseMissileMainHitPlayerAfterAcc = 0,
    CruiseMissileMainExplodePlayer = 0,
    CruiseMissileMainExplodeAIPlayer = 0,
    CruiseMissileMainExplodeAINPC = 0,
    CruiseMissileSubExplodePlayer = 0,
    CruiseMissileSubExplodeAIPlayer = 0,
    CruiseMissileSubExplodeAINPC = 0,
    CruiseMissileMainExplosionDamage = 0,
    CruiseMissileSubExplosionDamage = 0,
    CruiseMissileMainKnockDownPlayer = 0,
    CruiseMissileMainKnockDownAIPlayer = 0,
    CruiseMissileMainKnockDownAINPC = 0,
    CruiseMissileHurtDeadIn10S = 0,
    CruiseMissileMainDestroyed = 0,
    CruiseMissileAcc = 0,
    ThrowBlockingThrowNumber = 0,
    ThrowBlockingThrowTakeDamageNumber = 0,
    ThrowBlockingTakePointDamage = 0,
    ThrowBlockingTakeRadialDamage = 0,
    ThrowBlockingTakeDamageLifeTime = 0,
    ThrowBlockingMainTakePointDamage = 0,
    ThrowBlockingMainTakeRadialDamage = 0,
    ThrowBlockingSpawnTakeDamage = 0,
    ThrowBlockingSpawnDestroyed = 0,
    ThrowBlockingRadialDestroyed = 0,
    ThrowBlockingPointDestroyed = 0,
    ThrowBlockingMainPartDestroyed = 0,
    IncendiaryThrowNumber = 0,
    IncendiaryHitPlayer = 0,
    IncendiaryPlayerDamage = 0,
    IncendiaryInsidePlayerKillNum = 0,
    IncendiaryOutsidePlayerKillNum = 0,
    IncendiaryExtinguishedNum = 0,
    IncendiaryDestroyBlockingNum = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowDavid.__name = "ArmedforceDataFlowDavid"
pb.__pb_ArmedforceDataFlowDavid.__index = pb.__pb_ArmedforceDataFlowDavid
pb.__pb_ArmedforceDataFlowDavid.__pairs = __pb_pairs

pb.ArmedforceDataFlowDavid = { __name = "ArmedforceDataFlowDavid", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowDavid : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public PassiveSkillUseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public CruiseMissileAimOnFire number
---@field public CruiseMissileAimOffFire number
---@field public CruiseMissileMainHitTerrestrial number
---@field public CruiseMissileMainHitAerial number
---@field public CruiseMissileMainExplosionDamageToTerrestrial number
---@field public CruiseMissileMainExplosionDamageToAerial number
---@field public CruiseMissileMainDestroyTerrestrial number
---@field public CruiseMissileMainDestroyAerial number
---@field public CruiseMissileMainHitPlayerBeforeAcc number
---@field public CruiseMissileMainHitPlayerAfterAcc number
---@field public CruiseMissileMainExplodePlayer number
---@field public CruiseMissileMainExplodeAIPlayer number
---@field public CruiseMissileMainExplodeAINPC number
---@field public CruiseMissileSubExplodePlayer number
---@field public CruiseMissileSubExplodeAIPlayer number
---@field public CruiseMissileSubExplodeAINPC number
---@field public CruiseMissileMainExplosionDamage number
---@field public CruiseMissileSubExplosionDamage number
---@field public CruiseMissileMainKnockDownPlayer number
---@field public CruiseMissileMainKnockDownAIPlayer number
---@field public CruiseMissileMainKnockDownAINPC number
---@field public CruiseMissileHurtDeadIn10S number
---@field public CruiseMissileMainDestroyed number
---@field public CruiseMissileAcc number
---@field public ThrowBlockingThrowNumber number
---@field public ThrowBlockingThrowTakeDamageNumber number
---@field public ThrowBlockingTakePointDamage number
---@field public ThrowBlockingTakeRadialDamage number
---@field public ThrowBlockingTakeDamageLifeTime number
---@field public ThrowBlockingMainTakePointDamage number
---@field public ThrowBlockingMainTakeRadialDamage number
---@field public ThrowBlockingSpawnTakeDamage number
---@field public ThrowBlockingSpawnDestroyed number
---@field public ThrowBlockingRadialDestroyed number
---@field public ThrowBlockingPointDestroyed number
---@field public ThrowBlockingMainPartDestroyed number
---@field public IncendiaryThrowNumber number
---@field public IncendiaryHitPlayer number
---@field public IncendiaryPlayerDamage number
---@field public IncendiaryInsidePlayerKillNum number
---@field public IncendiaryOutsidePlayerKillNum number
---@field public IncendiaryExtinguishedNum number
---@field public IncendiaryDestroyBlockingNum number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowDavid
function pb.ArmedforceDataFlowDavid:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowHackclaw = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    PassiveSkillUseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    PassiveSkillMarkEnermyNum = 0,
    UsePathTracingCnt = 0,
    PathTracingMarkPlayerCnt = 0,
    PathTracingMarkPawnCnt = 0,
    KillMarkedPlayerCnt = 0,
    PathTracingUserDieCnt = 0,
    PathTracingLineTotalTime = 0,
    UseFlashDroneCnt = 0,
    FlashDroneFlowLineCnt = 0,
    FlashDroneFlashCnt = 0,
    FlashDroneFlashPlayerCnt = 0,
    FlashDroneFlashAICnt = 0,
    FlashDroneFlashRobotCnt = 0,
    FlashDroneNearFlashCnt = 0,
    FlashDroneMidFlashCnt = 0,
    FlashDroneFarFlashCnt = 0,
    FlashDroneBackFlashCnt = 0,
    FlashDroneFlashAssistCnt = 0,
    FlashDroneFlashBeDestoryCnt = 0,
    UseDataKnifeCnt = 0,
    DataKnifeHitPlayerCnt = 0,
    DataKnifeHitAICnt = 0,
    DataKnifeHitRobotCnt = 0,
    DataKnifeHitPlayerDamage = 0,
    DataKnifeKillPlayerCnt = 0,
    DataKnifeKillAICnt = 0,
    DataKnifeKillRobotCnt = 0,
    DataKnifeAssistCnt = 0,
    DataKnifeEMPSuppressionMineCnt = 0,
    DataKnifeEMPPathTracingCnt = 0,
    DataKnifeEMPRebornFlagCnt = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowHackclaw.__name = "ArmedforceDataFlowHackclaw"
pb.__pb_ArmedforceDataFlowHackclaw.__index = pb.__pb_ArmedforceDataFlowHackclaw
pb.__pb_ArmedforceDataFlowHackclaw.__pairs = __pb_pairs

pb.ArmedforceDataFlowHackclaw = { __name = "ArmedforceDataFlowHackclaw", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowHackclaw : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public PassiveSkillUseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public PassiveSkillMarkEnermyNum number
---@field public UsePathTracingCnt number
---@field public PathTracingMarkPlayerCnt number
---@field public PathTracingMarkPawnCnt number
---@field public KillMarkedPlayerCnt number
---@field public PathTracingUserDieCnt number
---@field public PathTracingLineTotalTime number
---@field public UseFlashDroneCnt number
---@field public FlashDroneFlowLineCnt number
---@field public FlashDroneFlashCnt number
---@field public FlashDroneFlashPlayerCnt number
---@field public FlashDroneFlashAICnt number
---@field public FlashDroneFlashRobotCnt number
---@field public FlashDroneNearFlashCnt number
---@field public FlashDroneMidFlashCnt number
---@field public FlashDroneFarFlashCnt number
---@field public FlashDroneBackFlashCnt number
---@field public FlashDroneFlashAssistCnt number
---@field public FlashDroneFlashBeDestoryCnt number
---@field public UseDataKnifeCnt number
---@field public DataKnifeHitPlayerCnt number
---@field public DataKnifeHitAICnt number
---@field public DataKnifeHitRobotCnt number
---@field public DataKnifeHitPlayerDamage number
---@field public DataKnifeKillPlayerCnt number
---@field public DataKnifeKillAICnt number
---@field public DataKnifeKillRobotCnt number
---@field public DataKnifeAssistCnt number
---@field public DataKnifeEMPSuppressionMineCnt number
---@field public DataKnifeEMPPathTracingCnt number
---@field public DataKnifeEMPRebornFlagCnt number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowHackclaw
function pb.ArmedforceDataFlowHackclaw:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerMovementStat = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    IgnoreMovePkgNum = 0,
    PkgLoseNum = 0,
    TimestampAbnormalNum = 0,
    ZAxisAbnormalNum = 0,
    CheckFailAfterPkgLoseNum = 0,
    MoveDragNum = 0,
    TriggerOfflineSyncNum = 0,
    TotalPkgNum = 0,
    TrustCustomModeClientMovementNum = 0,
    CheckSucAfterLoseImitateNum = 0,
    ServerClientPosDisLess5 = 0,
    ServerClientPosDisLess50 = 0,
    ClientMoveDisLess50 = 0,
    VelocityAbnormalNum = 0,
    DistanceAbnormalNum = 0,
    RoomID = 0,
}
pb.__pb_PlayerMovementStat.__name = "PlayerMovementStat"
pb.__pb_PlayerMovementStat.__index = pb.__pb_PlayerMovementStat
pb.__pb_PlayerMovementStat.__pairs = __pb_pairs

pb.PlayerMovementStat = { __name = "PlayerMovementStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerMovementStat : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public IgnoreMovePkgNum number
---@field public PkgLoseNum number
---@field public TimestampAbnormalNum number
---@field public ZAxisAbnormalNum number
---@field public CheckFailAfterPkgLoseNum number
---@field public MoveDragNum number
---@field public TriggerOfflineSyncNum number
---@field public TotalPkgNum number
---@field public TrustCustomModeClientMovementNum number
---@field public CheckSucAfterLoseImitateNum number
---@field public ServerClientPosDisLess5 number
---@field public ServerClientPosDisLess50 number
---@field public ClientMoveDisLess50 number
---@field public VelocityAbnormalNum number
---@field public DistanceAbnormalNum number
---@field public RoomID number

---@return pb_PlayerMovementStat
function pb.PlayerMovementStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerStat = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    PlayerId = 0,
    ReloginNumber = 0,
    MoveDragNum = 0,
    PacketLossRate = 0,
    MaxPingMs = 0,
    MinPingMs = 0,
    InvalidPingCount = 0,
    LastConnectionFailureType = 0,
    AvgPingMs = 0,
    HostSvrIp = "",
    HostPort = 0,
    UserIp = "",
    StartDate = "",
    EndDate = "",
}
pb.__pb_PlayerStat.__name = "PlayerStat"
pb.__pb_PlayerStat.__index = pb.__pb_PlayerStat
pb.__pb_PlayerStat.__pairs = __pb_pairs

pb.PlayerStat = { __name = "PlayerStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerStat : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public PlayerId number
---@field public ReloginNumber number
---@field public MoveDragNum number
---@field public PacketLossRate number
---@field public MaxPingMs number
---@field public MinPingMs number
---@field public InvalidPingCount number
---@field public LastConnectionFailureType number
---@field public AvgPingMs number
---@field public HostSvrIp string
---@field public HostPort number
---@field public UserIp string
---@field public StartDate string
---@field public EndDate string

---@return pb_PlayerStat
function pb.PlayerStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSCharacterArmorTakeDamage = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    HelmetArmorId = 0,
    ArmorId = 0,
    ShooterType = 0,
    AmmoId = 0,
    AttackLevel = 0,
    TotalDamage = 0,
    TotalThoraxDamage = 0,
    TotalAbdomenDamage = 0,
    TotalArmDamage = 0,
    LegHitCnt = 0,
    LegUpperHitCnt = 0,
    ArmHitCnt = 0,
    ArmUpperHitCnt = 0,
    HeadHitCnt = 0,
    ThorxHitCnt = 0,
    ThorxUpperHitCnt = 0,
    EquipTime = 0,
}
pb.__pb_DSCharacterArmorTakeDamage.__name = "DSCharacterArmorTakeDamage"
pb.__pb_DSCharacterArmorTakeDamage.__index = pb.__pb_DSCharacterArmorTakeDamage
pb.__pb_DSCharacterArmorTakeDamage.__pairs = __pb_pairs

pb.DSCharacterArmorTakeDamage = { __name = "DSCharacterArmorTakeDamage", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSCharacterArmorTakeDamage : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public HelmetArmorId number
---@field public ArmorId number
---@field public ShooterType number
---@field public AmmoId number
---@field public AttackLevel number
---@field public TotalDamage number
---@field public TotalThoraxDamage number
---@field public TotalAbdomenDamage number
---@field public TotalArmDamage number
---@field public LegHitCnt number
---@field public LegUpperHitCnt number
---@field public ArmHitCnt number
---@field public ArmUpperHitCnt number
---@field public HeadHitCnt number
---@field public ThorxHitCnt number
---@field public ThorxUpperHitCnt number
---@field public EquipTime number

---@return pb_DSCharacterArmorTakeDamage
function pb.DSCharacterArmorTakeDamage:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_NewDSCharacterArmorTakeHealthDebuff = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    HelmetArmorId = 0,
    BodyArmorId = 0,
    ShooterType = 0,
    AmmoId = 0,
    TotalWoundTime = 0,
    ArmBrokenTime = 0,
    LegBrokenTime = 0,
    PainTime = 0,
    HeadPain = 0,
    AbdomenPain = 0,
    ThorxPain = 0,
    ThroxWoundCnt = 0,
    AbdomenWoundCnt = 0,
    ArmBrokenCnt = 0,
    LegBrokenCnt = 0,
    BoneCrackedCnt = 0,
}
pb.__pb_NewDSCharacterArmorTakeHealthDebuff.__name = "NewDSCharacterArmorTakeHealthDebuff"
pb.__pb_NewDSCharacterArmorTakeHealthDebuff.__index = pb.__pb_NewDSCharacterArmorTakeHealthDebuff
pb.__pb_NewDSCharacterArmorTakeHealthDebuff.__pairs = __pb_pairs

pb.NewDSCharacterArmorTakeHealthDebuff = { __name = "NewDSCharacterArmorTakeHealthDebuff", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_NewDSCharacterArmorTakeHealthDebuff : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public HelmetArmorId number
---@field public BodyArmorId number
---@field public ShooterType number
---@field public AmmoId number
---@field public TotalWoundTime number
---@field public ArmBrokenTime number
---@field public LegBrokenTime number
---@field public PainTime number
---@field public HeadPain number
---@field public AbdomenPain number
---@field public ThorxPain number
---@field public ThroxWoundCnt number
---@field public AbdomenWoundCnt number
---@field public ArmBrokenCnt number
---@field public LegBrokenCnt number
---@field public BoneCrackedCnt number

---@return pb_NewDSCharacterArmorTakeHealthDebuff
function pb.NewDSCharacterArmorTakeHealthDebuff:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSCharacterUseItemHealth = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    ItemId = 0,
    HelmetId = 0,
    ArmorId = 0,
    HP = 0,
    HelmetArmor = 0,
    BodyArmor = 0,
    TotalHealHP = 0,
    TotalHealWound = 0,
    TotalHealArmor = 0,
    Duration = 0,
    HeadPain = 0,
    AbdomenPain = 0,
    ThorxPain = 0,
    InFighting = 0,
    UseInput = 0,
    CureBodyPart1 = 0,
    CureBodyPart2 = 0,
    CureBodyPart3 = 0,
    CureBodyPart4 = 0,
    CureBodyPart5 = 0,
    CureBodyPart6 = 0,
    CureBodyPart7 = 0,
    IsReapeat = 0,
}
pb.__pb_DSCharacterUseItemHealth.__name = "DSCharacterUseItemHealth"
pb.__pb_DSCharacterUseItemHealth.__index = pb.__pb_DSCharacterUseItemHealth
pb.__pb_DSCharacterUseItemHealth.__pairs = __pb_pairs

pb.DSCharacterUseItemHealth = { __name = "DSCharacterUseItemHealth", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSCharacterUseItemHealth : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public ItemId number
---@field public HelmetId number
---@field public ArmorId number
---@field public HP number
---@field public HelmetArmor number
---@field public BodyArmor number
---@field public TotalHealHP number
---@field public TotalHealWound number
---@field public TotalHealArmor number
---@field public Duration number
---@field public HeadPain number
---@field public AbdomenPain number
---@field public ThorxPain number
---@field public InFighting number
---@field public UseInput number
---@field public CureBodyPart1 number
---@field public CureBodyPart2 number
---@field public CureBodyPart3 number
---@field public CureBodyPart4 number
---@field public CureBodyPart5 number
---@field public CureBodyPart6 number
---@field public CureBodyPart7 number
---@field public IsReapeat number

---@return pb_DSCharacterUseItemHealth
function pb.DSCharacterUseItemHealth:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsGameGlobalItemFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    ItemID = 0,
    SourceID = "",
    InitialNum = 0,
    TotalConsumeNum = 0,
    OwnerPlayerConsumeNum = 0,
    DiscardNum = 0,
    BringOutNum = 0,
    RepairAddNum = 0,
    PassByPlayers = "",
    AIListsWhenConsume = "",
    ConsumePlayers = "",
    BringoutPlayers = "",
    ActivelyDiscardNum = 0,
    PlayerBringOutNum = 0,
    PlayerRepairAddNum = 0,
    StartDurability = 0,
    StartMaxDurability = 0,
    EndDurability = 0,
    EndMaxDurability = 0,
    InitialGuidePrice = 0,
    DynamicGuidePrice = 0,
    DropLogicInfo = "",
}
pb.__pb_DsGameGlobalItemFlow.__name = "DsGameGlobalItemFlow"
pb.__pb_DsGameGlobalItemFlow.__index = pb.__pb_DsGameGlobalItemFlow
pb.__pb_DsGameGlobalItemFlow.__pairs = __pb_pairs

pb.DsGameGlobalItemFlow = { __name = "DsGameGlobalItemFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsGameGlobalItemFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public ItemID number
---@field public SourceID string
---@field public InitialNum number
---@field public TotalConsumeNum number
---@field public OwnerPlayerConsumeNum number
---@field public DiscardNum number
---@field public BringOutNum number
---@field public RepairAddNum number
---@field public PassByPlayers string
---@field public AIListsWhenConsume string
---@field public ConsumePlayers string
---@field public BringoutPlayers string
---@field public ActivelyDiscardNum number
---@field public PlayerBringOutNum number
---@field public PlayerRepairAddNum number
---@field public StartDurability number
---@field public StartMaxDurability number
---@field public EndDurability number
---@field public EndMaxDurability number
---@field public InitialGuidePrice number
---@field public DynamicGuidePrice number
---@field public DropLogicInfo string

---@return pb_DsGameGlobalItemFlow
function pb.DsGameGlobalItemFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_NewDSBulletDamageInfo = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    AmmoId = 0,
    Damage1 = 0,
    Damage2 = 0,
    Damage3 = 0,
    Damage4 = 0,
    Damage5 = 0,
    Damage6 = 0,
    HelmetDamage1 = 0,
    HelmetDamage2 = 0,
    HelmetDamage3 = 0,
    HelmetDamage4 = 0,
    HelmetDamage5 = 0,
    HelmetDamage6 = 0,
    HPDamage = 0,
    LegHitCnt = 0,
    LegUpperHitCnt = 0,
    ArmHitCnt = 0,
    ArmUpperHitCnt = 0,
    HeadHitCnt = 0,
    ThorxHitCnt = 0,
    ThorxUpperHitCnt = 0,
    ShootedBulletNum = 0,
    HitBulletNum = 0,
    WeaponId = 0,
    TotalHitBodyCnt = 0,
    TotalHitArmorCnt = 0,
    Distance = 0,
    ShootedType = 0,
    HitBulletNumThroughSoftCover = 0,
}
pb.__pb_NewDSBulletDamageInfo.__name = "NewDSBulletDamageInfo"
pb.__pb_NewDSBulletDamageInfo.__index = pb.__pb_NewDSBulletDamageInfo
pb.__pb_NewDSBulletDamageInfo.__pairs = __pb_pairs

pb.NewDSBulletDamageInfo = { __name = "NewDSBulletDamageInfo", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_NewDSBulletDamageInfo : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public AmmoId number
---@field public Damage1 number
---@field public Damage2 number
---@field public Damage3 number
---@field public Damage4 number
---@field public Damage5 number
---@field public Damage6 number
---@field public HelmetDamage1 number
---@field public HelmetDamage2 number
---@field public HelmetDamage3 number
---@field public HelmetDamage4 number
---@field public HelmetDamage5 number
---@field public HelmetDamage6 number
---@field public HPDamage number
---@field public LegHitCnt number
---@field public LegUpperHitCnt number
---@field public ArmHitCnt number
---@field public ArmUpperHitCnt number
---@field public HeadHitCnt number
---@field public ThorxHitCnt number
---@field public ThorxUpperHitCnt number
---@field public ShootedBulletNum number
---@field public HitBulletNum number
---@field public WeaponId number
---@field public TotalHitBodyCnt number
---@field public TotalHitArmorCnt number
---@field public Distance number
---@field public ShootedType number
---@field public HitBulletNumThroughSoftCover number

---@return pb_NewDSBulletDamageInfo
function pb.NewDSBulletDamageInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_NewDSBulletRouletee = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GameMode = 0,
    MapId = 0,
    RoomID = 0,
    ChangeCarriedAmmoCnt = 0,
    ChangeCarriedAmmoByPlayer = 0,
    UseRouleteeCnt = 0,
    RouleteeOpenCnt = 0,
    UseRouleteeInFight = 0,
}
pb.__pb_NewDSBulletRouletee.__name = "NewDSBulletRouletee"
pb.__pb_NewDSBulletRouletee.__index = pb.__pb_NewDSBulletRouletee
pb.__pb_NewDSBulletRouletee.__pairs = __pb_pairs

pb.NewDSBulletRouletee = { __name = "NewDSBulletRouletee", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_NewDSBulletRouletee : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GameMode number
---@field public MapId number
---@field public RoomID number
---@field public ChangeCarriedAmmoCnt number
---@field public ChangeCarriedAmmoByPlayer number
---@field public UseRouleteeCnt number
---@field public RouleteeOpenCnt number
---@field public UseRouleteeInFight number

---@return pb_NewDSBulletRouletee
function pb.NewDSBulletRouletee:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSolInteractorAnalysisData = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    EconomyAnalysisID = 0,
    MiniGameEndState = 0,
    MiniGameDuration = 0,
}
pb.__pb_DsSolInteractorAnalysisData.__name = "DsSolInteractorAnalysisData"
pb.__pb_DsSolInteractorAnalysisData.__index = pb.__pb_DsSolInteractorAnalysisData
pb.__pb_DsSolInteractorAnalysisData.__pairs = __pb_pairs

pb.DsSolInteractorAnalysisData = { __name = "DsSolInteractorAnalysisData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSolInteractorAnalysisData : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public EconomyAnalysisID number
---@field public MiniGameEndState number
---@field public MiniGameDuration number

---@return pb_DsSolInteractorAnalysisData
function pb.DsSolInteractorAnalysisData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFightHitFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    TargetType = 0,
    DamageToArmor = 0,
    DamageToHP = 0,
    HitDistance = 0,
    IndexID = 0,
    POIName = "",
    HitCount = 0,
    HitDistanceStat = "",
    FlowStr = "",
    ArenaRoundCount = 0,
}
pb.__pb_DsFightHitFlow.__name = "DsFightHitFlow"
pb.__pb_DsFightHitFlow.__index = pb.__pb_DsFightHitFlow
pb.__pb_DsFightHitFlow.__pairs = __pb_pairs

pb.DsFightHitFlow = { __name = "DsFightHitFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFightHitFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public TargetType number
---@field public DamageToArmor number
---@field public DamageToHP number
---@field public HitDistance number
---@field public IndexID number
---@field public POIName string
---@field public HitCount number
---@field public HitDistanceStat string
---@field public FlowStr string
---@field public ArenaRoundCount number

---@return pb_DsFightHitFlow
function pb.DsFightHitFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFightBehitedFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    TargetType = 0,
    DamageToArmor = 0,
    DamageToHP = 0,
    HitDistance = 0,
    IndexID = 0,
    POIName = "",
    FlowStr = "",
    ArenaRoundCount = 0,
}
pb.__pb_DsFightBehitedFlow.__name = "DsFightBehitedFlow"
pb.__pb_DsFightBehitedFlow.__index = pb.__pb_DsFightBehitedFlow
pb.__pb_DsFightBehitedFlow.__pairs = __pb_pairs

pb.DsFightBehitedFlow = { __name = "DsFightBehitedFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFightBehitedFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public TargetType number
---@field public DamageToArmor number
---@field public DamageToHP number
---@field public HitDistance number
---@field public IndexID number
---@field public POIName string
---@field public FlowStr string
---@field public ArenaRoundCount number

---@return pb_DsFightBehitedFlow
function pb.DsFightBehitedFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFightHPGainedFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    PropID = 0,
    BuffID = 0,
    ArmorRecover = 0,
    HPRecover = 0,
    PropArmorRecover = 0,
    PropBuffHPRecover = 0,
    IndexID = 0,
    POIName = "",
    FlowStr = "",
    ArenaRoundCount = 0,
}
pb.__pb_DsFightHPGainedFlow.__name = "DsFightHPGainedFlow"
pb.__pb_DsFightHPGainedFlow.__index = pb.__pb_DsFightHPGainedFlow
pb.__pb_DsFightHPGainedFlow.__pairs = __pb_pairs

pb.DsFightHPGainedFlow = { __name = "DsFightHPGainedFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFightHPGainedFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public PropID number
---@field public BuffID number
---@field public ArmorRecover number
---@field public HPRecover number
---@field public PropArmorRecover number
---@field public PropBuffHPRecover number
---@field public IndexID number
---@field public POIName string
---@field public FlowStr string
---@field public ArenaRoundCount number

---@return pb_DsFightHPGainedFlow
function pb.DsFightHPGainedFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_QuestConsumePropFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    Level = 0,
    ObjectiveID = 0,
    ConsumeItemList = "",
    RoomID = 0,
}
pb.__pb_QuestConsumePropFlow.__name = "QuestConsumePropFlow"
pb.__pb_QuestConsumePropFlow.__index = pb.__pb_QuestConsumePropFlow
pb.__pb_QuestConsumePropFlow.__pairs = __pb_pairs

pb.QuestConsumePropFlow = { __name = "QuestConsumePropFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_QuestConsumePropFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public Level number
---@field public ObjectiveID number
---@field public ConsumeItemList string
---@field public RoomID number

---@return pb_QuestConsumePropFlow
function pb.QuestConsumePropFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsQuestOperatePointFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    OperatePointName = "",
    OperateTime = 0,
    RoomId = 0,
}
pb.__pb_DsQuestOperatePointFlow.__name = "DsQuestOperatePointFlow"
pb.__pb_DsQuestOperatePointFlow.__index = pb.__pb_DsQuestOperatePointFlow
pb.__pb_DsQuestOperatePointFlow.__pairs = __pb_pairs

pb.DsQuestOperatePointFlow = { __name = "DsQuestOperatePointFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsQuestOperatePointFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public OperatePointName string
---@field public OperateTime number
---@field public RoomId number

---@return pb_DsQuestOperatePointFlow
function pb.DsQuestOperatePointFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPlayerInOrOutPOITriggerData = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    MapID = 0,
    POIName = "",
    PlayerUin = 0,
    CurServerTime = 0,
    IsEnter = 0,
}
pb.__pb_DsPlayerInOrOutPOITriggerData.__name = "DsPlayerInOrOutPOITriggerData"
pb.__pb_DsPlayerInOrOutPOITriggerData.__index = pb.__pb_DsPlayerInOrOutPOITriggerData
pb.__pb_DsPlayerInOrOutPOITriggerData.__pairs = __pb_pairs

pb.DsPlayerInOrOutPOITriggerData = { __name = "DsPlayerInOrOutPOITriggerData", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPlayerInOrOutPOITriggerData : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public MapID number
---@field public POIName string
---@field public PlayerUin number
---@field public CurServerTime number
---@field public IsEnter number

---@return pb_DsPlayerInOrOutPOITriggerData
function pb.DsPlayerInOrOutPOITriggerData:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSVehiclePhysicsReplicationFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    ClientRole = 0,
    ClientPosition = "",
    ClientRotation = "",
    ClientLinearVelocity = "",
    ClientAngularVelocity = "",
    ClientIsSimulating = 0,
    ServerPosition = "",
    ServerRotation = "",
    ServerLinearVelocity = "",
    ServerAngularVelocity = "",
    LinDiff = 0,
    AngDiff = 0,
}
pb.__pb_DSVehiclePhysicsReplicationFlow.__name = "DSVehiclePhysicsReplicationFlow"
pb.__pb_DSVehiclePhysicsReplicationFlow.__index = pb.__pb_DSVehiclePhysicsReplicationFlow
pb.__pb_DSVehiclePhysicsReplicationFlow.__pairs = __pb_pairs

pb.DSVehiclePhysicsReplicationFlow = { __name = "DSVehiclePhysicsReplicationFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSVehiclePhysicsReplicationFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public ClientRole number
---@field public ClientPosition string
---@field public ClientRotation string
---@field public ClientLinearVelocity string
---@field public ClientAngularVelocity string
---@field public ClientIsSimulating number
---@field public ServerPosition string
---@field public ServerRotation string
---@field public ServerLinearVelocity string
---@field public ServerAngularVelocity string
---@field public LinDiff number
---@field public AngDiff number

---@return pb_DSVehiclePhysicsReplicationFlow
function pb.DSVehiclePhysicsReplicationFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSDropPointVisitFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    SourceID = 0,
    RoomID = 0,
    UinArr = "",
    SourceIDStr = "",
}
pb.__pb_DSDropPointVisitFlow.__name = "DSDropPointVisitFlow"
pb.__pb_DSDropPointVisitFlow.__index = pb.__pb_DSDropPointVisitFlow
pb.__pb_DSDropPointVisitFlow.__pairs = __pb_pairs

pb.DSDropPointVisitFlow = { __name = "DSDropPointVisitFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSDropPointVisitFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public SourceID number
---@field public RoomID number
---@field public UinArr string
---@field public SourceIDStr string

---@return pb_DSDropPointVisitFlow
function pb.DSDropPointVisitFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MPRedeployExpertBagFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    MapID = 0,
    SectorID = 0,
    BagChangeRecord = "",
}
pb.__pb_MPRedeployExpertBagFlow.__name = "MPRedeployExpertBagFlow"
pb.__pb_MPRedeployExpertBagFlow.__index = pb.__pb_MPRedeployExpertBagFlow
pb.__pb_MPRedeployExpertBagFlow.__pairs = __pb_pairs

pb.MPRedeployExpertBagFlow = { __name = "MPRedeployExpertBagFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MPRedeployExpertBagFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public MapID number
---@field public SectorID number
---@field public BagChangeRecord string

---@return pb_MPRedeployExpertBagFlow
function pb.MPRedeployExpertBagFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroAccessoryEmoteSlotUseFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    ModeID = 0,
    MapID = 0,
    TeamNumber = 0,
    ActionType = 0,
    PropID = 0,
    SlotQualityLevel = 0,
    RoomId = 0,
}
pb.__pb_HeroAccessoryEmoteSlotUseFlow.__name = "HeroAccessoryEmoteSlotUseFlow"
pb.__pb_HeroAccessoryEmoteSlotUseFlow.__index = pb.__pb_HeroAccessoryEmoteSlotUseFlow
pb.__pb_HeroAccessoryEmoteSlotUseFlow.__pairs = __pb_pairs

pb.HeroAccessoryEmoteSlotUseFlow = { __name = "HeroAccessoryEmoteSlotUseFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroAccessoryEmoteSlotUseFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public ModeID number
---@field public MapID number
---@field public TeamNumber number
---@field public ActionType number
---@field public PropID number
---@field public SlotQualityLevel number
---@field public RoomId number

---@return pb_HeroAccessoryEmoteSlotUseFlow
function pb.HeroAccessoryEmoteSlotUseFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMPlayerFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    Camp = 0,
    MapID = 0,
    RoomId = 0,
    RedeployStayTime = 0,
    SwitchHeroStayTime = 0,
    EnterFirstDeployViewTime = "",
    BeginFirstDeployTime = "",
    BackpackStayTime = 0,
    TotalScore = 0,
    SpawnRebornFlagNum = 0,
    TeammateRebornOnMyRebornFlagNum = 0,
    HalfJoinGame = 0,
    TeamIntoGame = 0,
    MatchResult = 0,
    RescueTeammateCount = 0,
    RescueCampmateCount = 0,
    MarkingTotalCount = 0,
    MarkingLocationCount = 0,
    EnterSectorNum = "",
    EnterSectorTime = "",
    JoinSectorTime = "",
    KillNumInSector = "",
    DeathNumInSector = "",
    BattleSupportUseNum = 0,
    BattleSupportAssistNum = 0,
    BattleSupportKillNum = 0,
    BattleSupportCostScore = 0,
    SBattleSupportUseNum = "",
    SBattleSupportAssistNum = "",
    SBattleSupportKillNum = "",
    SBattleSupportCostScore = "",
    LoadFinishOpenDeployViewTime = "",
    InteractorTime = "",
    InteractnumberofSatellitedish = 0,
    ActivityActorInteractorTime = "",
}
pb.__pb_TDMPlayerFlow.__name = "TDMPlayerFlow"
pb.__pb_TDMPlayerFlow.__index = pb.__pb_TDMPlayerFlow
pb.__pb_TDMPlayerFlow.__pairs = __pb_pairs

pb.TDMPlayerFlow = { __name = "TDMPlayerFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMPlayerFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public Camp number
---@field public MapID number
---@field public RoomId number
---@field public RedeployStayTime number
---@field public SwitchHeroStayTime number
---@field public EnterFirstDeployViewTime string
---@field public BeginFirstDeployTime string
---@field public BackpackStayTime number
---@field public TotalScore number
---@field public SpawnRebornFlagNum number
---@field public TeammateRebornOnMyRebornFlagNum number
---@field public HalfJoinGame number
---@field public TeamIntoGame number
---@field public MatchResult number
---@field public RescueTeammateCount number
---@field public RescueCampmateCount number
---@field public MarkingTotalCount number
---@field public MarkingLocationCount number
---@field public EnterSectorNum string
---@field public EnterSectorTime string
---@field public JoinSectorTime string
---@field public KillNumInSector string
---@field public DeathNumInSector string
---@field public BattleSupportUseNum number
---@field public BattleSupportAssistNum number
---@field public BattleSupportKillNum number
---@field public BattleSupportCostScore number
---@field public SBattleSupportUseNum string
---@field public SBattleSupportAssistNum string
---@field public SBattleSupportKillNum string
---@field public SBattleSupportCostScore string
---@field public LoadFinishOpenDeployViewTime string
---@field public InteractorTime string
---@field public InteractnumberofSatellitedish number
---@field public ActivityActorInteractorTime string

---@return pb_TDMPlayerFlow
function pb.TDMPlayerFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMMidWithdrawalFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    Camp = 0,
    MapID = 0,
    RoomId = 0,
    SectorId = 0,
    JoinGameTime = "",
    MidWithdrawalTime = "",
    AttackerLeftVotes = 0,
    MidWithdrawalSectorTime = 0,
    TotalKillNum = 0,
    TotalDeathNum = 0,
    TotalRankScore = 0,
    TeammateNum = 0,
}
pb.__pb_TDMMidWithdrawalFlow.__name = "TDMMidWithdrawalFlow"
pb.__pb_TDMMidWithdrawalFlow.__index = pb.__pb_TDMMidWithdrawalFlow
pb.__pb_TDMMidWithdrawalFlow.__pairs = __pb_pairs

pb.TDMMidWithdrawalFlow = { __name = "TDMMidWithdrawalFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMMidWithdrawalFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public Camp number
---@field public MapID number
---@field public RoomId number
---@field public SectorId number
---@field public JoinGameTime string
---@field public MidWithdrawalTime string
---@field public AttackerLeftVotes number
---@field public MidWithdrawalSectorTime number
---@field public TotalKillNum number
---@field public TotalDeathNum number
---@field public TotalRankScore number
---@field public TeammateNum number

---@return pb_TDMMidWithdrawalFlow
function pb.TDMMidWithdrawalFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMRescueTLogFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    Camp = 0,
    MapID = 0,
    RoomId = 0,
    SectorId = 0,
    ImpendingDeathInAnchor = 0,
    ImpendingDeathLocation = "",
    IsRescued = 0,
    RescueFailureReason = 0,
    RequestRescueTotalTime = 0,
    RequestRescueCount = 0,
    ResponseRescueCount = 0,
    RescuePlayerUin = 0,
    DeathTime = 0,
    AbandonTime = 0,
}
pb.__pb_TDMRescueTLogFlow.__name = "TDMRescueTLogFlow"
pb.__pb_TDMRescueTLogFlow.__index = pb.__pb_TDMRescueTLogFlow
pb.__pb_TDMRescueTLogFlow.__pairs = __pb_pairs

pb.TDMRescueTLogFlow = { __name = "TDMRescueTLogFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMRescueTLogFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public Camp number
---@field public MapID number
---@field public RoomId number
---@field public SectorId number
---@field public ImpendingDeathInAnchor number
---@field public ImpendingDeathLocation string
---@field public IsRescued number
---@field public RescueFailureReason number
---@field public RequestRescueTotalTime number
---@field public RequestRescueCount number
---@field public ResponseRescueCount number
---@field public RescuePlayerUin number
---@field public DeathTime number
---@field public AbandonTime number

---@return pb_TDMRescueTLogFlow
function pb.TDMRescueTLogFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMDSRoundFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    MapID = 0,
    RoomId = 0,
    WaitingForPlayersMutateTime = "",
}
pb.__pb_TDMDSRoundFlow.__name = "TDMDSRoundFlow"
pb.__pb_TDMDSRoundFlow.__index = pb.__pb_TDMDSRoundFlow
pb.__pb_TDMDSRoundFlow.__pairs = __pb_pairs

pb.TDMDSRoundFlow = { __name = "TDMDSRoundFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMDSRoundFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public MapID number
---@field public RoomId number
---@field public WaitingForPlayersMutateTime string

---@return pb_TDMDSRoundFlow
function pb.TDMDSRoundFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSOpenBoxPropDropFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    SourceID = "",
    SubSource = 0,
    ItemList = "",
}
pb.__pb_DSOpenBoxPropDropFlow.__name = "DSOpenBoxPropDropFlow"
pb.__pb_DSOpenBoxPropDropFlow.__index = pb.__pb_DSOpenBoxPropDropFlow
pb.__pb_DSOpenBoxPropDropFlow.__pairs = __pb_pairs

pb.DSOpenBoxPropDropFlow = { __name = "DSOpenBoxPropDropFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSOpenBoxPropDropFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public SourceID string
---@field public SubSource number
---@field public ItemList string

---@return pb_DSOpenBoxPropDropFlow
function pb.DSOpenBoxPropDropFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponKillVehicleInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    WeaponID = 0,
    CarType = 0,
    KillDistance = 0,
    MapID = 0,
    SectorID = 0,
}
pb.__pb_WeaponKillVehicleInfoFlow.__name = "WeaponKillVehicleInfoFlow"
pb.__pb_WeaponKillVehicleInfoFlow.__index = pb.__pb_WeaponKillVehicleInfoFlow
pb.__pb_WeaponKillVehicleInfoFlow.__pairs = __pb_pairs

pb.WeaponKillVehicleInfoFlow = { __name = "WeaponKillVehicleInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponKillVehicleInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public WeaponID number
---@field public CarType number
---@field public KillDistance number
---@field public MapID number
---@field public SectorID number

---@return pb_WeaponKillVehicleInfoFlow
function pb.WeaponKillVehicleInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleDeadInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    CarType = 0,
    DeadPosition = "",
}
pb.__pb_VehicleDeadInfoFlow.__name = "VehicleDeadInfoFlow"
pb.__pb_VehicleDeadInfoFlow.__index = pb.__pb_VehicleDeadInfoFlow
pb.__pb_VehicleDeadInfoFlow.__pairs = __pb_pairs

pb.VehicleDeadInfoFlow = { __name = "VehicleDeadInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleDeadInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public CarType number
---@field public DeadPosition string

---@return pb_VehicleDeadInfoFlow
function pb.VehicleDeadInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleDeadBySelfInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    CarType = 0,
    DeadPosition = "",
    MapID = 0,
    SectorID = 0,
}
pb.__pb_VehicleDeadBySelfInfoFlow.__name = "VehicleDeadBySelfInfoFlow"
pb.__pb_VehicleDeadBySelfInfoFlow.__index = pb.__pb_VehicleDeadBySelfInfoFlow
pb.__pb_VehicleDeadBySelfInfoFlow.__pairs = __pb_pairs

pb.VehicleDeadBySelfInfoFlow = { __name = "VehicleDeadBySelfInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleDeadBySelfInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public CarType number
---@field public DeadPosition string
---@field public MapID number
---@field public SectorID number

---@return pb_VehicleDeadBySelfInfoFlow
function pb.VehicleDeadBySelfInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsPerShotCounter = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    RecId = 0,
    TargetType = 0,
    AmmoId = 0,
    Recoil = 0,
    Control = 0,
    Stable = 0,
    HipShot = 0,
    Price = 0,
    ShootCnt = 0,
    Dist = 0,
    Duration = 0,
    HitBullet = 0,
    CostAmmo = 0,
    HitHead = 0,
    HitThrox = 0,
    HitAbdomen = 0,
    HitArm = 0,
    HitLeg = 0,
    GlickoRating = 0,
    MPHiddenScore = 0,
}
pb.__pb_DsPerShotCounter.__name = "DsPerShotCounter"
pb.__pb_DsPerShotCounter.__index = pb.__pb_DsPerShotCounter
pb.__pb_DsPerShotCounter.__pairs = __pb_pairs

pb.DsPerShotCounter = { __name = "DsPerShotCounter", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsPerShotCounter : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public RecId number
---@field public TargetType number
---@field public AmmoId number
---@field public Recoil number
---@field public Control number
---@field public Stable number
---@field public HipShot number
---@field public Price number
---@field public ShootCnt number
---@field public Dist number
---@field public Duration number
---@field public HitBullet number
---@field public CostAmmo number
---@field public HitHead number
---@field public HitThrox number
---@field public HitAbdomen number
---@field public HitArm number
---@field public HitLeg number
---@field public GlickoRating number
---@field public MPHiddenScore number

---@return pb_DsPerShotCounter
function pb.DsPerShotCounter:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMPlayerPointFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    ExpertID = 0,
    ScoreStr = "",
    BotType = 0,
}
pb.__pb_TDMPlayerPointFlow.__name = "TDMPlayerPointFlow"
pb.__pb_TDMPlayerPointFlow.__index = pb.__pb_TDMPlayerPointFlow
pb.__pb_TDMPlayerPointFlow.__pairs = __pb_pairs

pb.TDMPlayerPointFlow = { __name = "TDMPlayerPointFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMPlayerPointFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public ExpertID number
---@field public ScoreStr string
---@field public BotType number

---@return pb_TDMPlayerPointFlow
function pb.TDMPlayerPointFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SyncPlayer = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    CallId = 0,
    NumeralSvr = "",
    IsHalfJoin = 0,
    Token = "",
    Result = 0,
    RoomId = 0,
    MapId = 0,
    MapName = "",
    DSVersion = "",
}
pb.__pb_SyncPlayer.__name = "SyncPlayer"
pb.__pb_SyncPlayer.__index = pb.__pb_SyncPlayer
pb.__pb_SyncPlayer.__pairs = __pb_pairs

pb.SyncPlayer = { __name = "SyncPlayer", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SyncPlayer : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public CallId number
---@field public NumeralSvr string
---@field public IsHalfJoin number
---@field public Token string
---@field public Result number
---@field public RoomId number
---@field public MapId number
---@field public MapName string
---@field public DSVersion string

---@return pb_SyncPlayer
function pb.SyncPlayer:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSLogin = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    IsReLogin = 0,
    IP = "",
    Port = 0,
    Token = "",
    Result = 0,
    ErrorMsg = "",
    RoomId = 0,
    MapId = 0,
    MapName = "",
    DSVersion = "",
    Idc = "",
}
pb.__pb_DSLogin.__name = "DSLogin"
pb.__pb_DSLogin.__index = pb.__pb_DSLogin
pb.__pb_DSLogin.__pairs = __pb_pairs

pb.DSLogin = { __name = "DSLogin", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSLogin : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public IsReLogin number
---@field public IP string
---@field public Port number
---@field public Token string
---@field public Result number
---@field public ErrorMsg string
---@field public RoomId number
---@field public MapId number
---@field public MapName string
---@field public DSVersion string
---@field public Idc string

---@return pb_DSLogin
function pb.DSLogin:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSLogout = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    Reason = 0,
    RoomId = 0,
    MapId = 0,
    Idc = "",
}
pb.__pb_DSLogout.__name = "DSLogout"
pb.__pb_DSLogout.__index = pb.__pb_DSLogout
pb.__pb_DSLogout.__pairs = __pb_pairs

pb.DSLogout = { __name = "DSLogout", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSLogout : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public Reason number
---@field public RoomId number
---@field public MapId number
---@field public Idc string

---@return pb_DSLogout
function pb.DSLogout:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMPlayerMedalScorePointFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MedalType = 0,
    MedalLevel = 0,
    MedalScore = 0,
    ScoreStr = "",
}
pb.__pb_TDMPlayerMedalScorePointFlow.__name = "TDMPlayerMedalScorePointFlow"
pb.__pb_TDMPlayerMedalScorePointFlow.__index = pb.__pb_TDMPlayerMedalScorePointFlow
pb.__pb_TDMPlayerMedalScorePointFlow.__pairs = __pb_pairs

pb.TDMPlayerMedalScorePointFlow = { __name = "TDMPlayerMedalScorePointFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMPlayerMedalScorePointFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MedalType number
---@field public MedalLevel number
---@field public MedalScore number
---@field public ScoreStr string

---@return pb_TDMPlayerMedalScorePointFlow
function pb.TDMPlayerMedalScorePointFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMeleeWeaponPerformance = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    GameMode = 0,
    MapId = 0,
    SectorId = 0,
    CampID = 0,
    ShooterWeaponID = 0,
    EnemyPlayerType = 0,
    SwitchMeleeCount = 0,
    KillCount = 0,
    DamageSum = 0,
    CheckWeaponCount = 0,
}
pb.__pb_DsMeleeWeaponPerformance.__name = "DsMeleeWeaponPerformance"
pb.__pb_DsMeleeWeaponPerformance.__index = pb.__pb_DsMeleeWeaponPerformance
pb.__pb_DsMeleeWeaponPerformance.__pairs = __pb_pairs

pb.DsMeleeWeaponPerformance = { __name = "DsMeleeWeaponPerformance", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMeleeWeaponPerformance : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public GameMode number
---@field public MapId number
---@field public SectorId number
---@field public CampID number
---@field public ShooterWeaponID number
---@field public EnemyPlayerType number
---@field public SwitchMeleeCount number
---@field public KillCount number
---@field public DamageSum number
---@field public CheckWeaponCount number

---@return pb_DsMeleeWeaponPerformance
function pb.DsMeleeWeaponPerformance:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFixedWeaponDamageRepair = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapId = 0,
    SectorId = 0,
    CampId = 0,
    WeaponID = 0,
    SpecificWeaponID = 0,
    TakeDamageSum = 0,
    DamageType = 0,
    MakerWeaponID = 0,
    BrokenCount = 0,
    RepairFromBrokenCount = 0,
    RepairCount = 0,
    RepairHealthSum = 0,
    SkillID = 0,
}
pb.__pb_DsFixedWeaponDamageRepair.__name = "DsFixedWeaponDamageRepair"
pb.__pb_DsFixedWeaponDamageRepair.__index = pb.__pb_DsFixedWeaponDamageRepair
pb.__pb_DsFixedWeaponDamageRepair.__pairs = __pb_pairs

pb.DsFixedWeaponDamageRepair = { __name = "DsFixedWeaponDamageRepair", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFixedWeaponDamageRepair : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapId number
---@field public SectorId number
---@field public CampId number
---@field public WeaponID number
---@field public SpecificWeaponID number
---@field public TakeDamageSum number
---@field public DamageType number
---@field public MakerWeaponID number
---@field public BrokenCount number
---@field public RepairFromBrokenCount number
---@field public RepairCount number
---@field public RepairHealthSum number
---@field public SkillID number

---@return pb_DsFixedWeaponDamageRepair
function pb.DsFixedWeaponDamageRepair:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFixedWeaponToVehicle = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapId = 0,
    SectorId = 0,
    CampId = 0,
    WeaponID = 0,
    SpecificWeaponID = 0,
    TakerVehicleID = 0,
    DamageType = 0,
    KD = 0,
    ExplodeDamageSum = 0,
    PointDamageSum = 0,
    KillType = 0,
    ValidTTK = 0,
    ValidConsumeBTK = 0,
    ValidKillBTK = 0,
    ValidHitRate = 0,
    Distance = 0,
    KillCount = 0,
    BeKilledCount = 0,
}
pb.__pb_DsFixedWeaponToVehicle.__name = "DsFixedWeaponToVehicle"
pb.__pb_DsFixedWeaponToVehicle.__index = pb.__pb_DsFixedWeaponToVehicle
pb.__pb_DsFixedWeaponToVehicle.__pairs = __pb_pairs

pb.DsFixedWeaponToVehicle = { __name = "DsFixedWeaponToVehicle", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFixedWeaponToVehicle : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapId number
---@field public SectorId number
---@field public CampId number
---@field public WeaponID number
---@field public SpecificWeaponID number
---@field public TakerVehicleID number
---@field public DamageType number
---@field public KD number
---@field public ExplodeDamageSum number
---@field public PointDamageSum number
---@field public KillType number
---@field public ValidTTK number
---@field public ValidConsumeBTK number
---@field public ValidKillBTK number
---@field public ValidHitRate number
---@field public Distance number
---@field public KillCount number
---@field public BeKilledCount number

---@return pb_DsFixedWeaponToVehicle
function pb.DsFixedWeaponToVehicle:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsFixedWeaponToCharacter = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapId = 0,
    SectorId = 0,
    CampId = 0,
    WeaponID = 0,
    SpecificWeaponID = 0,
    TakerPlayerType = 0,
    IsTakerInVehicle = 0,
    IsTakerInRescue = 0,
    IsTakerInFiveSecondAfterRevive = 0,
    DamageType = 0,
    ExplodeDamageSum = 0,
    PointDamageSum = 0,
    KD = 0,
    KillType = 0,
    ValidTTK = 0,
    ValidConsumeBTK = 0,
    ValidKillBTK = 0,
    ValidHitRate = 0,
    Distance = 0,
    KillCount = 0,
    BeKilledCount = 0,
}
pb.__pb_DsFixedWeaponToCharacter.__name = "DsFixedWeaponToCharacter"
pb.__pb_DsFixedWeaponToCharacter.__index = pb.__pb_DsFixedWeaponToCharacter
pb.__pb_DsFixedWeaponToCharacter.__pairs = __pb_pairs

pb.DsFixedWeaponToCharacter = { __name = "DsFixedWeaponToCharacter", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsFixedWeaponToCharacter : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapId number
---@field public SectorId number
---@field public CampId number
---@field public WeaponID number
---@field public SpecificWeaponID number
---@field public TakerPlayerType number
---@field public IsTakerInVehicle number
---@field public IsTakerInRescue number
---@field public IsTakerInFiveSecondAfterRevive number
---@field public DamageType number
---@field public ExplodeDamageSum number
---@field public PointDamageSum number
---@field public KD number
---@field public KillType number
---@field public ValidTTK number
---@field public ValidConsumeBTK number
---@field public ValidKillBTK number
---@field public ValidHitRate number
---@field public Distance number
---@field public KillCount number
---@field public BeKilledCount number

---@return pb_DsFixedWeaponToCharacter
function pb.DsFixedWeaponToCharacter:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMSectorRedeploySwitchArmItemFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    SectorID = 0,
    SwitchArmItemRecord = "",
}
pb.__pb_TDMSectorRedeploySwitchArmItemFlow.__name = "TDMSectorRedeploySwitchArmItemFlow"
pb.__pb_TDMSectorRedeploySwitchArmItemFlow.__index = pb.__pb_TDMSectorRedeploySwitchArmItemFlow
pb.__pb_TDMSectorRedeploySwitchArmItemFlow.__pairs = __pb_pairs

pb.TDMSectorRedeploySwitchArmItemFlow = { __name = "TDMSectorRedeploySwitchArmItemFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMSectorRedeploySwitchArmItemFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public SectorID number
---@field public SwitchArmItemRecord string

---@return pb_TDMSectorRedeploySwitchArmItemFlow
function pb.TDMSectorRedeploySwitchArmItemFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsReportInvalidItem = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    PlayerId = 0,
    ItemGID = 0,
    ItemID = 0,
    Num = 0,
}
pb.__pb_DsReportInvalidItem.__name = "DsReportInvalidItem"
pb.__pb_DsReportInvalidItem.__index = pb.__pb_DsReportInvalidItem
pb.__pb_DsReportInvalidItem.__pairs = __pb_pairs

pb.DsReportInvalidItem = { __name = "DsReportInvalidItem", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsReportInvalidItem : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public PlayerId number
---@field public ItemGID number
---@field public ItemID number
---@field public Num number

---@return pb_DsReportInvalidItem
function pb.DsReportInvalidItem:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMRankSkillFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    PlayerId = 0,
    RankSkillUseNum = 0,
    KillEventSkillNum = 0,
    ZeroDamageEventSkillNum = 0,
    SkillID = 0,
}
pb.__pb_TDMRankSkillFlow.__name = "TDMRankSkillFlow"
pb.__pb_TDMRankSkillFlow.__index = pb.__pb_TDMRankSkillFlow
pb.__pb_TDMRankSkillFlow.__pairs = __pb_pairs

pb.TDMRankSkillFlow = { __name = "TDMRankSkillFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMRankSkillFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public PlayerId number
---@field public RankSkillUseNum number
---@field public KillEventSkillNum number
---@field public ZeroDamageEventSkillNum number
---@field public SkillID number

---@return pb_TDMRankSkillFlow
function pb.TDMRankSkillFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMStarFireActivityQuest = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    SignalStationComplete = "",
    CollectDiaryComplete = "",
    EscapeWithDiaryComplete = "",
    CollectKeyComplete = 0,
    EscapeWithKeyComplete = 0,
    DeadCount = 0,
    DeadCountNearSignalStation = 0,
    CarryDavidNum = 0,
    DeadCountWhenCarryDavid = 0,
    EscapeWithCarryDavidNum = 0,
    DeadCountNearSignalStationStr = "",
}
pb.__pb_TDMStarFireActivityQuest.__name = "TDMStarFireActivityQuest"
pb.__pb_TDMStarFireActivityQuest.__index = pb.__pb_TDMStarFireActivityQuest
pb.__pb_TDMStarFireActivityQuest.__pairs = __pb_pairs

pb.TDMStarFireActivityQuest = { __name = "TDMStarFireActivityQuest", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMStarFireActivityQuest : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public SignalStationComplete string
---@field public CollectDiaryComplete string
---@field public EscapeWithDiaryComplete string
---@field public CollectKeyComplete number
---@field public EscapeWithKeyComplete number
---@field public DeadCount number
---@field public DeadCountNearSignalStation number
---@field public CarryDavidNum number
---@field public DeadCountWhenCarryDavid number
---@field public EscapeWithCarryDavidNum number
---@field public DeadCountNearSignalStationStr string

---@return pb_TDMStarFireActivityQuest
function pb.TDMStarFireActivityQuest:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_OpExeInGameUsage = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    PlayerId = 0,
    Modeid = 0,
    MapId = 0,
    Teamnumber = 0,
    UsedExeID = 0,
    SlotQualityLevel = 0,
    Heroid = 0,
    ExeTarget = 0,
    ExeSuc = 0,
}
pb.__pb_OpExeInGameUsage.__name = "OpExeInGameUsage"
pb.__pb_OpExeInGameUsage.__index = pb.__pb_OpExeInGameUsage
pb.__pb_OpExeInGameUsage.__pairs = __pb_pairs

pb.OpExeInGameUsage = { __name = "OpExeInGameUsage", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_OpExeInGameUsage : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public PlayerId number
---@field public Modeid number
---@field public MapId number
---@field public Teamnumber number
---@field public UsedExeID number
---@field public SlotQualityLevel number
---@field public Heroid number
---@field public ExeTarget number
---@field public ExeSuc number

---@return pb_OpExeInGameUsage
function pb.OpExeInGameUsage:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMHitBackActivity = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    PakMachineHasPreItem = 0,
    PakMachineHasSubmitItem = 0,
    PakMachineHasItemAndEscaped = 0,
    PakMachineDieInRange = 0,
}
pb.__pb_TDMHitBackActivity.__name = "TDMHitBackActivity"
pb.__pb_TDMHitBackActivity.__index = pb.__pb_TDMHitBackActivity
pb.__pb_TDMHitBackActivity.__pairs = __pb_pairs

pb.TDMHitBackActivity = { __name = "TDMHitBackActivity", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMHitBackActivity : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public PakMachineHasPreItem number
---@field public PakMachineHasSubmitItem number
---@field public PakMachineHasItemAndEscaped number
---@field public PakMachineDieInRange number

---@return pb_TDMHitBackActivity
function pb.TDMHitBackActivity:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSVehicleAttackInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    VehicleId = 0,
    WeaponId = 0,
    AttackDistance = 0,
    VehicleLocation = "",
    AttackedTargetType = 0,
    AttackedTargetId = 0,
    AttackedTargetName = "",
    IsAttackedTargetDead = 0,
    DamageValue = 0,
    IsAttackedTargetAI = 0,
    GamePlatform = 0,
    MapID = 0,
    VehicleGetMethods = 0,
    SectorID = 0,
    CampID = 0,
}
pb.__pb_DSVehicleAttackInfoFlow.__name = "DSVehicleAttackInfoFlow"
pb.__pb_DSVehicleAttackInfoFlow.__index = pb.__pb_DSVehicleAttackInfoFlow
pb.__pb_DSVehicleAttackInfoFlow.__pairs = __pb_pairs

pb.DSVehicleAttackInfoFlow = { __name = "DSVehicleAttackInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSVehicleAttackInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public VehicleId number
---@field public WeaponId number
---@field public AttackDistance number
---@field public VehicleLocation string
---@field public AttackedTargetType number
---@field public AttackedTargetId number
---@field public AttackedTargetName string
---@field public IsAttackedTargetDead number
---@field public DamageValue number
---@field public IsAttackedTargetAI number
---@field public GamePlatform number
---@field public MapID number
---@field public VehicleGetMethods number
---@field public SectorID number
---@field public CampID number

---@return pb_DSVehicleAttackInfoFlow
function pb.DSVehicleAttackInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSVehicleDamageInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    VehicleId = 0,
    WeaponId = 0,
    AttackDistance = 0,
    VehicleLocation = "",
    IsVehicleDead = 0,
    DamageSourceType = 0,
    DamageSourceId = 0,
    DamageSourceName = "",
    DamageValue = 0,
    IsDamageSourceAI = 0,
    DamageSourceSkillId = 0,
    GamePlatform = 0,
    MapID = 0,
    SkillLevel = 0,
    GetOnVehicleTime = 0,
    VehicleGetMethods = 0,
    VehicleBeDamageTime = 0,
    VehicleDeadTime = 0,
}
pb.__pb_DSVehicleDamageInfoFlow.__name = "DSVehicleDamageInfoFlow"
pb.__pb_DSVehicleDamageInfoFlow.__index = pb.__pb_DSVehicleDamageInfoFlow
pb.__pb_DSVehicleDamageInfoFlow.__pairs = __pb_pairs

pb.DSVehicleDamageInfoFlow = { __name = "DSVehicleDamageInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSVehicleDamageInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public VehicleId number
---@field public WeaponId number
---@field public AttackDistance number
---@field public VehicleLocation string
---@field public IsVehicleDead number
---@field public DamageSourceType number
---@field public DamageSourceId number
---@field public DamageSourceName string
---@field public DamageValue number
---@field public IsDamageSourceAI number
---@field public DamageSourceSkillId number
---@field public GamePlatform number
---@field public MapID number
---@field public SkillLevel number
---@field public GetOnVehicleTime number
---@field public VehicleGetMethods number
---@field public VehicleBeDamageTime number
---@field public VehicleDeadTime number

---@return pb_DSVehicleDamageInfoFlow
function pb.DSVehicleDamageInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleWeaponUsagePreferenceFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    GamePlatform = 0,
    PlayerId = 0,
    RankScore = 0,
    MapID = 0,
    HiddenScore = 0,
    VehicleID = 0,
    SeatWeaponInfo_0 = "",
    SeatWeaponInfo_1 = "",
    SeatWeaponInfo_2 = "",
    SeatWeaponInfo_3 = "",
    SeatWeaponInfo_4 = "",
    SeatWeaponInfo_5 = "",
    RoomID = 0,
}
pb.__pb_VehicleWeaponUsagePreferenceFlow.__name = "VehicleWeaponUsagePreferenceFlow"
pb.__pb_VehicleWeaponUsagePreferenceFlow.__index = pb.__pb_VehicleWeaponUsagePreferenceFlow
pb.__pb_VehicleWeaponUsagePreferenceFlow.__pairs = __pb_pairs

pb.VehicleWeaponUsagePreferenceFlow = { __name = "VehicleWeaponUsagePreferenceFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleWeaponUsagePreferenceFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public GamePlatform number
---@field public PlayerId number
---@field public RankScore number
---@field public MapID number
---@field public HiddenScore number
---@field public VehicleID number
---@field public SeatWeaponInfo_0 string
---@field public SeatWeaponInfo_1 string
---@field public SeatWeaponInfo_2 string
---@field public SeatWeaponInfo_3 string
---@field public SeatWeaponInfo_4 string
---@field public SeatWeaponInfo_5 string
---@field public RoomID number

---@return pb_VehicleWeaponUsagePreferenceFlow
function pb.VehicleWeaponUsagePreferenceFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleInGameDataFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    VehicleInstanceID = 0,
    VehicleID = 0,
    GamePlatform = 0,
    VehicleActivityTime = 0,
    RoomSkillLevel = 0,
    MapID = 0,
    CampID = 0,
    VehicleGetMethods = 0,
    SeatUseTime_0 = 0,
    SeatScoreInfo_0 = "",
    SeatUseTime_1 = 0,
    SeatScoreInfo_1 = "",
    SeatUseTime_2 = 0,
    SeatScoreInfo_2 = "",
    SeatUseTime_3 = 0,
    SeatScoreInfo_3 = "",
    SeatUseTime_4 = 0,
    SeatScoreInfo_4 = "",
    SeatUseTime_5 = 0,
    SeatScoreInfo_5 = "",
    RoomID = 0,
}
pb.__pb_VehicleInGameDataFlow.__name = "VehicleInGameDataFlow"
pb.__pb_VehicleInGameDataFlow.__index = pb.__pb_VehicleInGameDataFlow
pb.__pb_VehicleInGameDataFlow.__pairs = __pb_pairs

pb.VehicleInGameDataFlow = { __name = "VehicleInGameDataFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleInGameDataFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public VehicleInstanceID number
---@field public VehicleID number
---@field public GamePlatform number
---@field public VehicleActivityTime number
---@field public RoomSkillLevel number
---@field public MapID number
---@field public CampID number
---@field public VehicleGetMethods number
---@field public SeatUseTime_0 number
---@field public SeatScoreInfo_0 string
---@field public SeatUseTime_1 number
---@field public SeatScoreInfo_1 string
---@field public SeatUseTime_2 number
---@field public SeatScoreInfo_2 string
---@field public SeatUseTime_3 number
---@field public SeatScoreInfo_3 string
---@field public SeatUseTime_4 number
---@field public SeatScoreInfo_4 string
---@field public SeatUseTime_5 number
---@field public SeatScoreInfo_5 string
---@field public RoomID number

---@return pb_VehicleInGameDataFlow
function pb.VehicleInGameDataFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_VehicleUsagePreferenceFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    VehicleInstanceID = 0,
    VehicleID = 0,
    GamePlatform = 0,
    RoomSkillLevel = 0,
    MapID = 0,
    PlayerId = 0,
    CampID = 0,
    VehicleGetMethods = 0,
    SeatUseTime_0 = 0,
    SeatUseTime_1 = 0,
    SeatUseTime_2 = 0,
    SeatUseTime_3 = 0,
    SeatUseTime_4 = 0,
    SeatUseTime_5 = 0,
    RoomID = 0,
}
pb.__pb_VehicleUsagePreferenceFlow.__name = "VehicleUsagePreferenceFlow"
pb.__pb_VehicleUsagePreferenceFlow.__index = pb.__pb_VehicleUsagePreferenceFlow
pb.__pb_VehicleUsagePreferenceFlow.__pairs = __pb_pairs

pb.VehicleUsagePreferenceFlow = { __name = "VehicleUsagePreferenceFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_VehicleUsagePreferenceFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public VehicleInstanceID number
---@field public VehicleID number
---@field public GamePlatform number
---@field public RoomSkillLevel number
---@field public MapID number
---@field public PlayerId number
---@field public CampID number
---@field public VehicleGetMethods number
---@field public SeatUseTime_0 number
---@field public SeatUseTime_1 number
---@field public SeatUseTime_2 number
---@field public SeatUseTime_3 number
---@field public SeatUseTime_4 number
---@field public SeatUseTime_5 number
---@field public RoomID number

---@return pb_VehicleUsagePreferenceFlow
function pb.VehicleUsagePreferenceFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerGuideCompensationFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    HafCoinLose = 0,
    PlayerLv = 0,
    StageId = 0,
}
pb.__pb_PlayerGuideCompensationFlow.__name = "PlayerGuideCompensationFlow"
pb.__pb_PlayerGuideCompensationFlow.__index = pb.__pb_PlayerGuideCompensationFlow
pb.__pb_PlayerGuideCompensationFlow.__pairs = __pb_pairs

pb.PlayerGuideCompensationFlow = { __name = "PlayerGuideCompensationFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerGuideCompensationFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public HafCoinLose number
---@field public PlayerLv number
---@field public StageId number

---@return pb_PlayerGuideCompensationFlow
function pb.PlayerGuideCompensationFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSTDMWinterActivity = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomID = 0,
    PlayerID = 0,
    ItemHeapHasPreItem = 0,
    ItemHeapEnterArea = 0,
    ItemHeapHasSubmitItem = 0,
    ItemHeapHasEscapedWithItem = 0,
    ItemHeapHasEscapedWithProduct = 0,
}
pb.__pb_DSTDMWinterActivity.__name = "DSTDMWinterActivity"
pb.__pb_DSTDMWinterActivity.__index = pb.__pb_DSTDMWinterActivity
pb.__pb_DSTDMWinterActivity.__pairs = __pb_pairs

pb.DSTDMWinterActivity = { __name = "DSTDMWinterActivity", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSTDMWinterActivity : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomID number
---@field public PlayerID number
---@field public ItemHeapHasPreItem number
---@field public ItemHeapEnterArea number
---@field public ItemHeapHasSubmitItem number
---@field public ItemHeapHasEscapedWithItem number
---@field public ItemHeapHasEscapedWithProduct number

---@return pb_DSTDMWinterActivity
function pb.DSTDMWinterActivity:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSDoorOpenInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomID = 0,
    DoorID = 0,
    KeyID = 0,
    PlayerID = 0,
    OpenTime = 0,
    DoorIDName = "",
    KeyIDName = "",
}
pb.__pb_DSDoorOpenInfoFlow.__name = "DSDoorOpenInfoFlow"
pb.__pb_DSDoorOpenInfoFlow.__index = pb.__pb_DSDoorOpenInfoFlow
pb.__pb_DSDoorOpenInfoFlow.__pairs = __pb_pairs

pb.DSDoorOpenInfoFlow = { __name = "DSDoorOpenInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSDoorOpenInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomID number
---@field public DoorID number
---@field public KeyID number
---@field public PlayerID number
---@field public OpenTime number
---@field public DoorIDName string
---@field public KeyIDName string

---@return pb_DSDoorOpenInfoFlow
function pb.DSDoorOpenInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSArmedforceDataFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SwarmsEquipCount = 0,
    SwarmsCastTotalDelayTime = 0,
    SwarmsCastCount = 0,
    SwarmsHitPlayerCount = 0,
    SwarmsHitAICount = 0,
    SwarmsKillCount = 0,
    SwarmsAssistKillCount = 0,
    SwarmsHitCount0_20 = 0,
    SwarmsHitCount20_40 = 0,
    SwarmsHitCount40_60 = 0,
    SwarmsManualClearCount = 0,
    BlindingSmokeEquipCount = 0,
    BlindingSmokeUseCount = 0,
    BlindingSmokeHitPlayerCount = 0,
    BlindingSmokeHitAICount = 0,
    BlindingSmokeHitPlayerTime = 0,
    BlindingSmokeKillPlayerCount = 0,
    BlindingSmokeKillAICount = 0,
    BlindingSmoke3SecondsKillPlayerCount = 0,
    BlindingSmokeDestroyCount = 0,
    BlindingSmokeHitMoreThanOnePlayerCount = 0,
}
pb.__pb_DSArmedforceDataFlow.__name = "DSArmedforceDataFlow"
pb.__pb_DSArmedforceDataFlow.__index = pb.__pb_DSArmedforceDataFlow
pb.__pb_DSArmedforceDataFlow.__pairs = __pb_pairs

pb.DSArmedforceDataFlow = { __name = "DSArmedforceDataFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSArmedforceDataFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SwarmsEquipCount number
---@field public SwarmsCastTotalDelayTime number
---@field public SwarmsCastCount number
---@field public SwarmsHitPlayerCount number
---@field public SwarmsHitAICount number
---@field public SwarmsKillCount number
---@field public SwarmsAssistKillCount number
---@field public SwarmsHitCount0_20 number
---@field public SwarmsHitCount20_40 number
---@field public SwarmsHitCount40_60 number
---@field public SwarmsManualClearCount number
---@field public BlindingSmokeEquipCount number
---@field public BlindingSmokeUseCount number
---@field public BlindingSmokeHitPlayerCount number
---@field public BlindingSmokeHitAICount number
---@field public BlindingSmokeHitPlayerTime number
---@field public BlindingSmokeKillPlayerCount number
---@field public BlindingSmokeKillAICount number
---@field public BlindingSmoke3SecondsKillPlayerCount number
---@field public BlindingSmokeDestroyCount number
---@field public BlindingSmokeHitMoreThanOnePlayerCount number

---@return pb_DSArmedforceDataFlow
function pb.DSArmedforceDataFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSSOLPlayerRepairReport = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    ArmorId = 0,
    HelmetId = 0,
    ArmorRepairItemId = 0,
    HelmetRepairItemId = 0,
    DurabilityBeforeRepair = 0,
    DurabilityLimitBeforeRepair = 0,
    DurabilityAfterRepair = 0,
    DurabilityLimitAfterRepair = 0,
    AverageRecoverDurability = 0,
    TotalRecoverDurability = 0,
}
pb.__pb_DSSOLPlayerRepairReport.__name = "DSSOLPlayerRepairReport"
pb.__pb_DSSOLPlayerRepairReport.__index = pb.__pb_DSSOLPlayerRepairReport
pb.__pb_DSSOLPlayerRepairReport.__pairs = __pb_pairs

pb.DSSOLPlayerRepairReport = { __name = "DSSOLPlayerRepairReport", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSSOLPlayerRepairReport : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public ArmorId number
---@field public HelmetId number
---@field public ArmorRepairItemId number
---@field public HelmetRepairItemId number
---@field public DurabilityBeforeRepair number
---@field public DurabilityLimitBeforeRepair number
---@field public DurabilityAfterRepair number
---@field public DurabilityLimitAfterRepair number
---@field public AverageRecoverDurability number
---@field public TotalRecoverDurability number

---@return pb_DSSOLPlayerRepairReport
function pb.DSSOLPlayerRepairReport:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSSOLPlayerHealthStateReport = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    ArmorId = 0,
    HelmetId = 0,
    AmmoId = 0,
    DamageMaker = 0,
    WoundTimes = 0,
    WoundPart = 0,
    ArmFractureTimes = 0,
    ArmFracturePart = 0,
    LegFractureTimes = 0,
    LegFracturePart = 0,
}
pb.__pb_DSSOLPlayerHealthStateReport.__name = "DSSOLPlayerHealthStateReport"
pb.__pb_DSSOLPlayerHealthStateReport.__index = pb.__pb_DSSOLPlayerHealthStateReport
pb.__pb_DSSOLPlayerHealthStateReport.__pairs = __pb_pairs

pb.DSSOLPlayerHealthStateReport = { __name = "DSSOLPlayerHealthStateReport", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSSOLPlayerHealthStateReport : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public ArmorId number
---@field public HelmetId number
---@field public AmmoId number
---@field public DamageMaker number
---@field public WoundTimes number
---@field public WoundPart number
---@field public ArmFractureTimes number
---@field public ArmFracturePart number
---@field public LegFractureTimes number
---@field public LegFracturePart number

---@return pb_DSSOLPlayerHealthStateReport
function pb.DSSOLPlayerHealthStateReport:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSSOLPlayerTreatmentReport = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    ArmorId = 0,
    HelmetId = 0,
    ItemId = 0,
    HP = 0,
    TotalUseTimes = 0,
    TotalRecoverHP = 0,
    TotalTreatmentWoundNum = 0,
    TotalTreatmentPartNum = 0,
}
pb.__pb_DSSOLPlayerTreatmentReport.__name = "DSSOLPlayerTreatmentReport"
pb.__pb_DSSOLPlayerTreatmentReport.__index = pb.__pb_DSSOLPlayerTreatmentReport
pb.__pb_DSSOLPlayerTreatmentReport.__pairs = __pb_pairs

pb.DSSOLPlayerTreatmentReport = { __name = "DSSOLPlayerTreatmentReport", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSSOLPlayerTreatmentReport : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public ArmorId number
---@field public HelmetId number
---@field public ItemId number
---@field public HP number
---@field public TotalUseTimes number
---@field public TotalRecoverHP number
---@field public TotalTreatmentWoundNum number
---@field public TotalTreatmentPartNum number

---@return pb_DSSOLPlayerTreatmentReport
function pb.DSSOLPlayerTreatmentReport:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SecTriggerClickFlow = {
    GameSvrId = "",
    DtEventTime = "",
    GameAppID = "",
    PlatID = 0,
    IZoneAreaID = 0,
    PlayerId = 0,
    Level = 0,
    OpenID = "",
    MapId = 0,
    GameType = 0,
    GameMode = 0,
    RoomId = 0,
    BattleID = 0,
    MatchModule = 0,
    FlowDataStr = "",
    StrIndex = 0,
}
pb.__pb_SecTriggerClickFlow.__name = "SecTriggerClickFlow"
pb.__pb_SecTriggerClickFlow.__index = pb.__pb_SecTriggerClickFlow
pb.__pb_SecTriggerClickFlow.__pairs = __pb_pairs

pb.SecTriggerClickFlow = { __name = "SecTriggerClickFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SecTriggerClickFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public GameAppID string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public PlayerId number
---@field public Level number
---@field public OpenID string
---@field public MapId number
---@field public GameType number
---@field public GameMode number
---@field public RoomId number
---@field public BattleID number
---@field public MatchModule number
---@field public FlowDataStr string
---@field public StrIndex number

---@return pb_SecTriggerClickFlow
function pb.SecTriggerClickFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SecMoveReport = {
    GameSvrId = "",
    DtEventTime = "",
    GameAppID = "",
    PlatID = 0,
    IZoneAreaID = 0,
    PlayerId = 0,
    Level = 0,
    OpenID = "",
    MapId = 0,
    GameType = 0,
    GameMode = 0,
    RoomId = 0,
    BattleID = 0,
    MatchModule = 0,
    FlowDataStr = "",
    FlowDataStr2 = "",
}
pb.__pb_SecMoveReport.__name = "SecMoveReport"
pb.__pb_SecMoveReport.__index = pb.__pb_SecMoveReport
pb.__pb_SecMoveReport.__pairs = __pb_pairs

pb.SecMoveReport = { __name = "SecMoveReport", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SecMoveReport : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public GameAppID string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public PlayerId number
---@field public Level number
---@field public OpenID string
---@field public MapId number
---@field public GameType number
---@field public GameMode number
---@field public RoomId number
---@field public BattleID number
---@field public MatchModule number
---@field public FlowDataStr string
---@field public FlowDataStr2 string

---@return pb_SecMoveReport
function pb.SecMoveReport:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSTimingSkillDataFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    DireWolfArmedPropDamageCountAsKiller10S = 0,
    DireWolfArmedPropDamageCountAsKilled10S = 0,
    DireWolfArmedPropDamageCountAsKiller30S = 0,
    DireWolfArmedPropDamageCountAsKilled30S = 0,
    DireWolfOverloadAsKiller = 0,
    DireWolfOverloadAsKilled = 0,
    DragonBeKnockBackAsKiller10S = 0,
    DragonBeKnockBackAsKilled10S = 0,
    DragonBeKnockBackAsKiller30S = 0,
    DragonBeKnockBackAsKilled30S = 0,
    DragonUseDashAsKiller10S = 0,
    DragonUseDashAsKilled10S = 0,
    DragonC4DamageCountAsKiller10S = 0,
    DragonC4DamageCountAsKilled10S = 0,
    DragonC4DamageCountAsKiller30S = 0,
    DragonC4DamageCountAsKilled30S = 0,
    LunaElecBowDamageCountAsKiller10S = 0,
    LunaElecBowDamageCountAsKilled10S = 0,
    LunaElecBowDamageCountAsKiller30S = 0,
    LunaElecBowDamageCountAsKilled30S = 0,
    LunaDetectBowMarkedAsKiller10S = 0,
    LunaDetectBowMarkedAsKilled10S = 0,
    LunaDetectBowMarkedAsKiller30S = 0,
    LunaDetectBowMarkedAsKilled30S = 0,
    LunaGrenadeDamageCountAsKiller10S = 0,
    LunaGrenadeDamageCountAsKilled10S = 0,
    LunaGrenadeDamageCountAsKiller30S = 0,
    LunaGrenadeDamageCountAsKilled30S = 0,
    HackerTracingAsKiller = 0,
    HackerTracingAsKilled = 0,
    HackerBeFlashBuffTimeAsKiller10S = 0,
    HackerBeFlashBuffTimeAsKilled10S = 0,
    FoleyBeHealedCountAsKiller10S = 0,
    FoleyBeHealedCountAsKilled10S = 0,
    FoleyBeHealedCountAsKiller30S = 0,
    FoleyBeHealedCountAsKilled30S = 0,
    TerryTrapBeHitAsKiller10S = 0,
    TerryTrapBeHitAsKilled10S = 0,
    TerryTrapBeActiveAsKiller10S = 0,
    TerryTrapBeActiveAsKilled10S = 0,
    TerryBeSonicBuffTimeAsKiller10S = 0,
    TerryBeSonicBuffTimeAsKilled10S = 0,
    TerryGrenadeDamageCountAsKiller10S = 0,
    TerryGrenadeDamageCountAsKilled10S = 0,
    TerryGrenadeDamageCountAsKiller30S = 0,
    TerryGrenadeDamageCountAsKilled30S = 0,
    DavidTVDamageCountAsKiller10S = 0,
    DavidTVDamageCountAsKiller30S = 0,
    DavidTVDamageCountAsKilled10S = 0,
    DavidTVDamageCountAsKilled30S = 0,
    DavidFireDamageCountAsKiller10S = 0,
    DavidFireDamageCountAsKilled10S = 0,
    DavidFireDamageCountAsKiller30S = 0,
    DavidFireDamageCountAsKilled30S = 0,
    MoveDistanceAsKiller10S = 0,
    MoveDistanceAsKilled10S = 0,
    MoveDistanceAsKiller30S = 0,
    MoveDistanceAsKilled30S = 0,
    SprintDistanceAsKiller10S = 0,
    SprintDistanceAsKilled10S = 0,
    SprintDistanceAsKiller30S = 0,
    SprintDistanceAsKilled30S = 0,
    SquatDistanceAsKiller10S = 0,
    SquatDistanceAsKilled10S = 0,
    SquatDistanceAsKiller30S = 0,
    SquatDistanceAsKilled30S = 0,
    JumpTimesAsKiller10S = 0,
    JumpTimesAsKilled10S = 0,
    GameMultiField = "",
    GamePOIKiller = "",
    GamePOIKilled = "",
    GameGlicko = 0,
    ZoyaBeSwarmedAsKiller10S = 0,
    ZoyaBeSwarmedAsKilled10S = 0,
    ZoyaBeSwarmedAsKiller30S = 0,
    ZoyaBeSwarmedAsKilled30S = 0,
    ZoyaBeAdrenalineAsKiller10S = 0,
    ZoyaBeAdrenalineAsKilled10S = 0,
    ZoyaBeAdrenalineAsKiller30S = 0,
    ZoyaBeAdrenalineAsKilled30S = 0,
    ZoyaBeBlindedAsKiller10S = 0,
    ZoyaBeBlindedAsKilled10S = 0,
    ZoyaBeBlindedAsKiller30S = 0,
    ZoyaBeBlindedAsKilled30S = 0,
    AlexieBeShieldAttackedAsKiller10S = 0,
    AlexieBeShieldAttackedAsKilled10S = 0,
    AlexieBeShieldAttackedAsKiller30S = 0,
    AlexieBeShieldAttackedAsKilled30S = 0,
    AlexieBeChainHookedAsKiller10S = 0,
    AlexieBeChainHookedAsKilled10S = 0,
    AlexieBeChainHookedAsKiller30S = 0,
    AlexieBeChainHookedAsKilled30S = 0,
    AlexieBeWireSlowedAsKiller10S = 0,
    AlexieBeWireSlowedAsKilled10S = 0,
    AlexieBeWireSlowedAsKiller30S = 0,
    AlexieBeWireSlowedAsKilled30S = 0,
    GameType = 0,
    ZoyaInAdrenalineAsKiller = 0,
    ZoyaInAdrenalineAsKilled = 0,
    ShadowBeInSilenceAsKiller = 0,
    ShadowBeInSilenceAsKilled = 0,
    ShadowBeInfluencedByKnifeDroneAsKiller10S = 0,
    ShadowBeInfluencedByKnifeDroneAsKilled10S = 0,
    ShadowBeInfluencedByKnifeDroneAsKiller30S = 0,
    ShadowBeInfluencedByKnifeDroneAsKilled30S = 0,
    ShadowBeHurtByKnifeDroneAsKiller10S = 0,
    ShadowBeHurtByKnifeDroneAsKilled10S = 0,
    ShadowBeHurtByKnifeDroneAsKiller30S = 0,
    ShadowBeHurtByKnifeDroneAsKilled30S = 0,
    ShadowBeFlashedAsKiller10S = 0,
    ShadowBeFlashedAsKilled10S = 0,
    ShadowBeFlashedAsKiller30S = 0,
    ShadowBeFlashedAsKilled30S = 0,
    PlayerHero = "",
    PlayerKillCount = 0,
    PlayerAssistCount = 0,
    GamePOI = "",
    ClaireBeInEmergencyAvoidAsKiller = 0,
    ClaireBeInEmergencyAvoidAsKilled = 0,
    ClaireBeInRollAsKiller10S = 0,
    ClaireBeInRollAsKilled10S = 0,
    ClaireBeInRollAsKiller30S = 0,
    ClaireBeInRollAsKilled30S = 0,
    ClaireBeElectricDamageAsKiller10S = 0,
    ClaireBeElectricDamageAsKilled10S = 0,
    ClaireBeElectricDamageAsKiller30S = 0,
    ClaireBeElectricDamageAsKilled30S = 0,
    OldDengBeDetectByUltraAsKiller10S = 0,
    OldDengBeDetectByUltraAsKilled10S = 0,
    OldDengBeDetectByUltraAsKiller30S = 0,
    OldDengBeDetectByUltraAsKilled30S = 0,
    OldDengBeDetectByBirdAsKiller10S = 0,
    OldDengBeDetectByBirdAsKilled10S = 0,
    OldDengBeDetectByBirdAsKiller30S = 0,
    OldDengBeDetectByBirdAsKilled30S = 0,
    OldDengBeEMPAsKiller10S = 0,
    OldDengBeEMPAsKilled10S = 0,
    OldDengBeEMPAsKiller30S = 0,
    OldDengBeEMPAsKilled30S = 0,
}
pb.__pb_DSTimingSkillDataFlow.__name = "DSTimingSkillDataFlow"
pb.__pb_DSTimingSkillDataFlow.__index = pb.__pb_DSTimingSkillDataFlow
pb.__pb_DSTimingSkillDataFlow.__pairs = __pb_pairs

pb.DSTimingSkillDataFlow = { __name = "DSTimingSkillDataFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSTimingSkillDataFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public DireWolfArmedPropDamageCountAsKiller10S number
---@field public DireWolfArmedPropDamageCountAsKilled10S number
---@field public DireWolfArmedPropDamageCountAsKiller30S number
---@field public DireWolfArmedPropDamageCountAsKilled30S number
---@field public DireWolfOverloadAsKiller number
---@field public DireWolfOverloadAsKilled number
---@field public DragonBeKnockBackAsKiller10S number
---@field public DragonBeKnockBackAsKilled10S number
---@field public DragonBeKnockBackAsKiller30S number
---@field public DragonBeKnockBackAsKilled30S number
---@field public DragonUseDashAsKiller10S number
---@field public DragonUseDashAsKilled10S number
---@field public DragonC4DamageCountAsKiller10S number
---@field public DragonC4DamageCountAsKilled10S number
---@field public DragonC4DamageCountAsKiller30S number
---@field public DragonC4DamageCountAsKilled30S number
---@field public LunaElecBowDamageCountAsKiller10S number
---@field public LunaElecBowDamageCountAsKilled10S number
---@field public LunaElecBowDamageCountAsKiller30S number
---@field public LunaElecBowDamageCountAsKilled30S number
---@field public LunaDetectBowMarkedAsKiller10S number
---@field public LunaDetectBowMarkedAsKilled10S number
---@field public LunaDetectBowMarkedAsKiller30S number
---@field public LunaDetectBowMarkedAsKilled30S number
---@field public LunaGrenadeDamageCountAsKiller10S number
---@field public LunaGrenadeDamageCountAsKilled10S number
---@field public LunaGrenadeDamageCountAsKiller30S number
---@field public LunaGrenadeDamageCountAsKilled30S number
---@field public HackerTracingAsKiller number
---@field public HackerTracingAsKilled number
---@field public HackerBeFlashBuffTimeAsKiller10S number
---@field public HackerBeFlashBuffTimeAsKilled10S number
---@field public FoleyBeHealedCountAsKiller10S number
---@field public FoleyBeHealedCountAsKilled10S number
---@field public FoleyBeHealedCountAsKiller30S number
---@field public FoleyBeHealedCountAsKilled30S number
---@field public TerryTrapBeHitAsKiller10S number
---@field public TerryTrapBeHitAsKilled10S number
---@field public TerryTrapBeActiveAsKiller10S number
---@field public TerryTrapBeActiveAsKilled10S number
---@field public TerryBeSonicBuffTimeAsKiller10S number
---@field public TerryBeSonicBuffTimeAsKilled10S number
---@field public TerryGrenadeDamageCountAsKiller10S number
---@field public TerryGrenadeDamageCountAsKilled10S number
---@field public TerryGrenadeDamageCountAsKiller30S number
---@field public TerryGrenadeDamageCountAsKilled30S number
---@field public DavidTVDamageCountAsKiller10S number
---@field public DavidTVDamageCountAsKiller30S number
---@field public DavidTVDamageCountAsKilled10S number
---@field public DavidTVDamageCountAsKilled30S number
---@field public DavidFireDamageCountAsKiller10S number
---@field public DavidFireDamageCountAsKilled10S number
---@field public DavidFireDamageCountAsKiller30S number
---@field public DavidFireDamageCountAsKilled30S number
---@field public MoveDistanceAsKiller10S number
---@field public MoveDistanceAsKilled10S number
---@field public MoveDistanceAsKiller30S number
---@field public MoveDistanceAsKilled30S number
---@field public SprintDistanceAsKiller10S number
---@field public SprintDistanceAsKilled10S number
---@field public SprintDistanceAsKiller30S number
---@field public SprintDistanceAsKilled30S number
---@field public SquatDistanceAsKiller10S number
---@field public SquatDistanceAsKilled10S number
---@field public SquatDistanceAsKiller30S number
---@field public SquatDistanceAsKilled30S number
---@field public JumpTimesAsKiller10S number
---@field public JumpTimesAsKilled10S number
---@field public GameMultiField string
---@field public GamePOIKiller string
---@field public GamePOIKilled string
---@field public GameGlicko number
---@field public ZoyaBeSwarmedAsKiller10S number
---@field public ZoyaBeSwarmedAsKilled10S number
---@field public ZoyaBeSwarmedAsKiller30S number
---@field public ZoyaBeSwarmedAsKilled30S number
---@field public ZoyaBeAdrenalineAsKiller10S number
---@field public ZoyaBeAdrenalineAsKilled10S number
---@field public ZoyaBeAdrenalineAsKiller30S number
---@field public ZoyaBeAdrenalineAsKilled30S number
---@field public ZoyaBeBlindedAsKiller10S number
---@field public ZoyaBeBlindedAsKilled10S number
---@field public ZoyaBeBlindedAsKiller30S number
---@field public ZoyaBeBlindedAsKilled30S number
---@field public AlexieBeShieldAttackedAsKiller10S number
---@field public AlexieBeShieldAttackedAsKilled10S number
---@field public AlexieBeShieldAttackedAsKiller30S number
---@field public AlexieBeShieldAttackedAsKilled30S number
---@field public AlexieBeChainHookedAsKiller10S number
---@field public AlexieBeChainHookedAsKilled10S number
---@field public AlexieBeChainHookedAsKiller30S number
---@field public AlexieBeChainHookedAsKilled30S number
---@field public AlexieBeWireSlowedAsKiller10S number
---@field public AlexieBeWireSlowedAsKilled10S number
---@field public AlexieBeWireSlowedAsKiller30S number
---@field public AlexieBeWireSlowedAsKilled30S number
---@field public GameType number
---@field public ZoyaInAdrenalineAsKiller number
---@field public ZoyaInAdrenalineAsKilled number
---@field public ShadowBeInSilenceAsKiller number
---@field public ShadowBeInSilenceAsKilled number
---@field public ShadowBeInfluencedByKnifeDroneAsKiller10S number
---@field public ShadowBeInfluencedByKnifeDroneAsKilled10S number
---@field public ShadowBeInfluencedByKnifeDroneAsKiller30S number
---@field public ShadowBeInfluencedByKnifeDroneAsKilled30S number
---@field public ShadowBeHurtByKnifeDroneAsKiller10S number
---@field public ShadowBeHurtByKnifeDroneAsKilled10S number
---@field public ShadowBeHurtByKnifeDroneAsKiller30S number
---@field public ShadowBeHurtByKnifeDroneAsKilled30S number
---@field public ShadowBeFlashedAsKiller10S number
---@field public ShadowBeFlashedAsKilled10S number
---@field public ShadowBeFlashedAsKiller30S number
---@field public ShadowBeFlashedAsKilled30S number
---@field public PlayerHero string
---@field public PlayerKillCount number
---@field public PlayerAssistCount number
---@field public GamePOI string
---@field public ClaireBeInEmergencyAvoidAsKiller number
---@field public ClaireBeInEmergencyAvoidAsKilled number
---@field public ClaireBeInRollAsKiller10S number
---@field public ClaireBeInRollAsKilled10S number
---@field public ClaireBeInRollAsKiller30S number
---@field public ClaireBeInRollAsKilled30S number
---@field public ClaireBeElectricDamageAsKiller10S number
---@field public ClaireBeElectricDamageAsKilled10S number
---@field public ClaireBeElectricDamageAsKiller30S number
---@field public ClaireBeElectricDamageAsKilled30S number
---@field public OldDengBeDetectByUltraAsKiller10S number
---@field public OldDengBeDetectByUltraAsKilled10S number
---@field public OldDengBeDetectByUltraAsKiller30S number
---@field public OldDengBeDetectByUltraAsKilled30S number
---@field public OldDengBeDetectByBirdAsKiller10S number
---@field public OldDengBeDetectByBirdAsKilled10S number
---@field public OldDengBeDetectByBirdAsKiller30S number
---@field public OldDengBeDetectByBirdAsKilled30S number
---@field public OldDengBeEMPAsKiller10S number
---@field public OldDengBeEMPAsKilled10S number
---@field public OldDengBeEMPAsKiller30S number
---@field public OldDengBeEMPAsKilled30S number

---@return pb_DSTimingSkillDataFlow
function pb.DSTimingSkillDataFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchTickFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MatchModeID = 0,
    CurrTime = 0,
    UnitPoolTickInfo = "",
    TotalTeam = 0,
    TickTeam = 0,
    TickTimeCostMs = 0,
    PoolKey = "",
}
pb.__pb_MatchTickFlow.__name = "MatchTickFlow"
pb.__pb_MatchTickFlow.__index = pb.__pb_MatchTickFlow
pb.__pb_MatchTickFlow.__pairs = __pb_pairs

pb.MatchTickFlow = { __name = "MatchTickFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchTickFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MatchModeID number
---@field public CurrTime number
---@field public UnitPoolTickInfo string
---@field public TotalTeam number
---@field public TickTeam number
---@field public TickTimeCostMs number
---@field public PoolKey string

---@return pb_MatchTickFlow
function pb.MatchTickFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMMatchTickFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PoolKey = "",
    CurrTime = 0,
    TickTimeCostMs = 0,
    HalfJoinTimeCostMs = 0,
    HalfJoinRooms = 0,
    BotHalfJoinTimeCostMs = 0,
    BotHalfJoinNum = 0,
    UnitPoolTickInfo = "",
    UnitPoolTickTimeCost = 0,
}
pb.__pb_TDMMatchTickFlow.__name = "TDMMatchTickFlow"
pb.__pb_TDMMatchTickFlow.__index = pb.__pb_TDMMatchTickFlow
pb.__pb_TDMMatchTickFlow.__pairs = __pb_pairs

pb.TDMMatchTickFlow = { __name = "TDMMatchTickFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMMatchTickFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PoolKey string
---@field public CurrTime number
---@field public TickTimeCostMs number
---@field public HalfJoinTimeCostMs number
---@field public HalfJoinRooms number
---@field public BotHalfJoinTimeCostMs number
---@field public BotHalfJoinNum number
---@field public UnitPoolTickInfo string
---@field public UnitPoolTickTimeCost number

---@return pb_TDMMatchTickFlow
function pb.TDMMatchTickFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ReportInvisibleFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    AIGid = 0,
    RoomId = 0,
    MapID = 0,
    AIType = "",
    DSVersion = "",
    InvisibleType = 0,
    OffsetDist = 0,
    PlayerId = 0,
}
pb.__pb_ReportInvisibleFlow.__name = "ReportInvisibleFlow"
pb.__pb_ReportInvisibleFlow.__index = pb.__pb_ReportInvisibleFlow
pb.__pb_ReportInvisibleFlow.__pairs = __pb_pairs

pb.ReportInvisibleFlow = { __name = "ReportInvisibleFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ReportInvisibleFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public AIGid number
---@field public RoomId number
---@field public MapID number
---@field public AIType string
---@field public DSVersion string
---@field public InvisibleType number
---@field public OffsetDist number
---@field public PlayerId number

---@return pb_ReportInvisibleFlow
function pb.ReportInvisibleFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_BreakthroughBigEventInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomID = 0,
    TriggerTimes = 0,
}
pb.__pb_BreakthroughBigEventInfoFlow.__name = "BreakthroughBigEventInfoFlow"
pb.__pb_BreakthroughBigEventInfoFlow.__index = pb.__pb_BreakthroughBigEventInfoFlow
pb.__pb_BreakthroughBigEventInfoFlow.__pairs = __pb_pairs

pb.BreakthroughBigEventInfoFlow = { __name = "BreakthroughBigEventInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_BreakthroughBigEventInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomID number
---@field public TriggerTimes number

---@return pb_BreakthroughBigEventInfoFlow
function pb.BreakthroughBigEventInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowZoya = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillUseCnt = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    AntiPersonnelFireCount = 0,
    AntiPersonnelKillCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    SwarmsEquipCount = 0,
    SwarmsCastTotalDelayTime = 0,
    SwarmsCastCount = 0,
    SwarmsHitPlayerCount = 0,
    SwarmsHitAICount = 0,
    SwarmsKillCount = 0,
    SwarmsAssistKillCount = 0,
    SwarmsHitCount0_20 = 0,
    SwarmsHitCount20_40 = 0,
    SwarmsHitCount40_60 = 0,
    SwarmsManualClearCount = 0,
    BlindingSmokeEquipCount = 0,
    BlindingSmokeUseCount = 0,
    BlindingSmokeHitPlayerCount = 0,
    BlindingSmokeHitAICount = 0,
    BlindingSmokeHitPlayerTime = 0,
    BlindingSmokeKillPlayerCount = 0,
    BlindingSmokeKillAICount = 0,
    BlindingSmoke3SecondsKillPlayerCount = 0,
    BlindingSmokeDestroyCount = 0,
    BlindingSmokeHitMoreThanOnePlayerCount = 0,
    AdrenalineTotalAssistCount = 0,
    AdrenalineFrightingUseCount = 0,
    PassiveSkillTotalSaveTime = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowZoya.__name = "ArmedforceDataFlowZoya"
pb.__pb_ArmedforceDataFlowZoya.__index = pb.__pb_ArmedforceDataFlowZoya
pb.__pb_ArmedforceDataFlowZoya.__pairs = __pb_pairs

pb.ArmedforceDataFlowZoya = { __name = "ArmedforceDataFlowZoya", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowZoya : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillUseCnt number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public AntiPersonnelFireCount number
---@field public AntiPersonnelKillCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public SwarmsEquipCount number
---@field public SwarmsCastTotalDelayTime number
---@field public SwarmsCastCount number
---@field public SwarmsHitPlayerCount number
---@field public SwarmsHitAICount number
---@field public SwarmsKillCount number
---@field public SwarmsAssistKillCount number
---@field public SwarmsHitCount0_20 number
---@field public SwarmsHitCount20_40 number
---@field public SwarmsHitCount40_60 number
---@field public SwarmsManualClearCount number
---@field public BlindingSmokeEquipCount number
---@field public BlindingSmokeUseCount number
---@field public BlindingSmokeHitPlayerCount number
---@field public BlindingSmokeHitAICount number
---@field public BlindingSmokeHitPlayerTime number
---@field public BlindingSmokeKillPlayerCount number
---@field public BlindingSmokeKillAICount number
---@field public BlindingSmoke3SecondsKillPlayerCount number
---@field public BlindingSmokeDestroyCount number
---@field public BlindingSmokeHitMoreThanOnePlayerCount number
---@field public AdrenalineTotalAssistCount number
---@field public AdrenalineFrightingUseCount number
---@field public PassiveSkillTotalSaveTime number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowZoya
function pb.ArmedforceDataFlowZoya:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_IpCityFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    IP = "",
    CountryCode = 0,
    AreaCode = 0,
    PossibleCountryCode = "",
    PossibleAreaCode = "",
}
pb.__pb_IpCityFlow.__name = "IpCityFlow"
pb.__pb_IpCityFlow.__index = pb.__pb_IpCityFlow
pb.__pb_IpCityFlow.__pairs = __pb_pairs

pb.IpCityFlow = { __name = "IpCityFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_IpCityFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public IP string
---@field public CountryCode number
---@field public AreaCode number
---@field public PossibleCountryCode string
---@field public PossibleAreaCode string

---@return pb_IpCityFlow
function pb.IpCityFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsArenaRoundFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoundResult = "",
    FinalResult = 0,
    FinalScore = "",
    FinalRank = 0,
    RoundTime = "",
    RoundKillPlayerCnt = "",
    RoundKillAICnt = "",
    RoundAssistCnt = "",
    RoundDeadCnt = "",
    RoundFinalProps = "",
    RoundDamageCnt = "",
    RoundHealCnt = "",
    MidWithdrawalRound = 0,
    ArmedForceSelect = 0,
    DeadReason = 0,
    LootDetail = "",
    RoundFighting = 0,
    RoundUseItemDetail = "",
    RoundUseSkillDetail = "",
    RoundBuyPropDetail = "",
    RoundStartScore = "",
    RoundEndScore = "",
    RoomId = 0,
    DeadReasonStr = "",
    RoundFightingStr = "",
    CurrentExpertID = 0,
}
pb.__pb_DsArenaRoundFlow.__name = "DsArenaRoundFlow"
pb.__pb_DsArenaRoundFlow.__index = pb.__pb_DsArenaRoundFlow
pb.__pb_DsArenaRoundFlow.__pairs = __pb_pairs

pb.DsArenaRoundFlow = { __name = "DsArenaRoundFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsArenaRoundFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoundResult string
---@field public FinalResult number
---@field public FinalScore string
---@field public FinalRank number
---@field public RoundTime string
---@field public RoundKillPlayerCnt string
---@field public RoundKillAICnt string
---@field public RoundAssistCnt string
---@field public RoundDeadCnt string
---@field public RoundFinalProps string
---@field public RoundDamageCnt string
---@field public RoundHealCnt string
---@field public MidWithdrawalRound number
---@field public ArmedForceSelect number
---@field public DeadReason number
---@field public LootDetail string
---@field public RoundFighting number
---@field public RoundUseItemDetail string
---@field public RoundUseSkillDetail string
---@field public RoundBuyPropDetail string
---@field public RoundStartScore string
---@field public RoundEndScore string
---@field public RoomId number
---@field public DeadReasonStr string
---@field public RoundFightingStr string
---@field public CurrentExpertID number

---@return pb_DsArenaRoundFlow
function pb.DsArenaRoundFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsArenaKnockDownFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomId = 0,
    FlowStr = "",
}
pb.__pb_DsArenaKnockDownFlow.__name = "DsArenaKnockDownFlow"
pb.__pb_DsArenaKnockDownFlow.__index = pb.__pb_DsArenaKnockDownFlow
pb.__pb_DsArenaKnockDownFlow.__pairs = __pb_pairs

pb.DsArenaKnockDownFlow = { __name = "DsArenaKnockDownFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsArenaKnockDownFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomId number
---@field public FlowStr string

---@return pb_DsArenaKnockDownFlow
function pb.DsArenaKnockDownFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowSineva = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillUseCnt = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    AntiPersonnelFireCount = 0,
    AntiPersonnelKillCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    UseBlastShieldCount = 0,
    UseChainHookCount = 0,
    UseBarbedWireGrenadeCount = 0,
    BlastShieldCounterProjectileCount = 0,
    BlastShieldBlockingDamage = 0,
    BlastShieldKnockBackCount = 0,
    BlastShieldDamage = 0,
    ChainHookHitAllyCount = 0,
    ChainHookHitEnemyCount = 0,
    ChainHookHitImpendingDeathAllyCount = 0,
    BarbedWireGrenadeHitCount = 0,
    PassiveSkillBlockingDamage = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    DeadCountWhenEquipBlastShield = 0,
    DeadCountWhenBlastShieldGlassBroken = 0,
    DeadCountWhenBlastShieldGlassNotBroken = 0,
    BlastShieldBlockingBulletDamage = 0,
    BlastShieldBlockingOtherDamage = 0,
    ChainHookHitRealPlayerCount = 0,
    ChainHookPullRealPlayerCount = 0,
    ChainHookBrokenCount = 0,
    ChainHookBrokenByBulletCount = 0,
    ChainHookBrokenByMeleeCount = 0,
    BarbedWireGrenadeBrokenCount = 0,
    BarbedWireGrenadeBrokenByBulletCount = 0,
    BarbedWireGrenadeBrokenByExplosionCount = 0,
    BarbedWireGrenadeBrokenByMeleeCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowSineva.__name = "ArmedforceDataFlowSineva"
pb.__pb_ArmedforceDataFlowSineva.__index = pb.__pb_ArmedforceDataFlowSineva
pb.__pb_ArmedforceDataFlowSineva.__pairs = __pb_pairs

pb.ArmedforceDataFlowSineva = { __name = "ArmedforceDataFlowSineva", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowSineva : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillUseCnt number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public AntiPersonnelFireCount number
---@field public AntiPersonnelKillCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public UseBlastShieldCount number
---@field public UseChainHookCount number
---@field public UseBarbedWireGrenadeCount number
---@field public BlastShieldCounterProjectileCount number
---@field public BlastShieldBlockingDamage number
---@field public BlastShieldKnockBackCount number
---@field public BlastShieldDamage number
---@field public ChainHookHitAllyCount number
---@field public ChainHookHitEnemyCount number
---@field public ChainHookHitImpendingDeathAllyCount number
---@field public BarbedWireGrenadeHitCount number
---@field public PassiveSkillBlockingDamage number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public DeadCountWhenEquipBlastShield number
---@field public DeadCountWhenBlastShieldGlassBroken number
---@field public DeadCountWhenBlastShieldGlassNotBroken number
---@field public BlastShieldBlockingBulletDamage number
---@field public BlastShieldBlockingOtherDamage number
---@field public ChainHookHitRealPlayerCount number
---@field public ChainHookPullRealPlayerCount number
---@field public ChainHookBrokenCount number
---@field public ChainHookBrokenByBulletCount number
---@field public ChainHookBrokenByMeleeCount number
---@field public BarbedWireGrenadeBrokenCount number
---@field public BarbedWireGrenadeBrokenByBulletCount number
---@field public BarbedWireGrenadeBrokenByExplosionCount number
---@field public BarbedWireGrenadeBrokenByMeleeCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowSineva
function pb.ArmedforceDataFlowSineva:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsKillInfos = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    TeamID = 0,
    StartTime = "",
    GameTime = 0,
    KillerPlayerId = 0,
    ArmedForceId = 0,
    HeroId = 0,
    Killedvopenid = 0,
    KilledType = 0,
    KilledName = "",
    KilledLevel = 0,
    KilledRankScore = 0,
    KilledRank = 0,
    KilledCamp = 0,
    KilledArmedForceId = 0,
    KilledHeroId = 0,
}
pb.__pb_DsKillInfos.__name = "DsKillInfos"
pb.__pb_DsKillInfos.__index = pb.__pb_DsKillInfos
pb.__pb_DsKillInfos.__pairs = __pb_pairs

pb.DsKillInfos = { __name = "DsKillInfos", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsKillInfos : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public TeamID number
---@field public StartTime string
---@field public GameTime number
---@field public KillerPlayerId number
---@field public ArmedForceId number
---@field public HeroId number
---@field public Killedvopenid number
---@field public KilledType number
---@field public KilledName string
---@field public KilledLevel number
---@field public KilledRankScore number
---@field public KilledRank number
---@field public KilledCamp number
---@field public KilledArmedForceId number
---@field public KilledHeroId number

---@return pb_DsKillInfos
function pb.DsKillInfos:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMPTransportFlagResultFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    FlagOwnerCamp = 0,
    TransportIndex = 0,
    TransportTime = 0,
    NumberOfContests = 0,
    TransportResult = 0,
}
pb.__pb_DsMPTransportFlagResultFlow.__name = "DsMPTransportFlagResultFlow"
pb.__pb_DsMPTransportFlagResultFlow.__index = pb.__pb_DsMPTransportFlagResultFlow
pb.__pb_DsMPTransportFlagResultFlow.__pairs = __pb_pairs

pb.DsMPTransportFlagResultFlow = { __name = "DsMPTransportFlagResultFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMPTransportFlagResultFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public FlagOwnerCamp number
---@field public TransportIndex number
---@field public TransportTime number
---@field public NumberOfContests number
---@field public TransportResult number

---@return pb_DsMPTransportFlagResultFlow
function pb.DsMPTransportFlagResultFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsBhdRoundFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    PlayerCount = 0,
    TaskSucc = 0,
    DeathCount = 0,
    RescureCount = 0,
    LoadOutID = 0,
    BandageCount = 0,
    KillCount = 0,
    PlayTime = 0,
    BHDLevel = 0,
    LevelProgress = 0,
    FailReason = 0,
    RetryCount = 0,
    AmmoCount = 0,
    BHDScore = 0,
}
pb.__pb_DsBhdRoundFlow.__name = "DsBhdRoundFlow"
pb.__pb_DsBhdRoundFlow.__index = pb.__pb_DsBhdRoundFlow
pb.__pb_DsBhdRoundFlow.__pairs = __pb_pairs

pb.DsBhdRoundFlow = { __name = "DsBhdRoundFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsBhdRoundFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public PlayerCount number
---@field public TaskSucc number
---@field public DeathCount number
---@field public RescureCount number
---@field public LoadOutID number
---@field public BandageCount number
---@field public KillCount number
---@field public PlayTime number
---@field public BHDLevel number
---@field public LevelProgress number
---@field public FailReason number
---@field public RetryCount number
---@field public AmmoCount number
---@field public BHDScore number

---@return pb_DsBhdRoundFlow
function pb.DsBhdRoundFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_EvacuationRecords = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    ClientVersion = "",
    GameTime = 0,
    EvacuationName = 0,
    EvacuationStageType = 0,
    EvacuationEnteredPlayerUinArrayStr = "",
    EvacuationEnteredPlayerNameArrayStr = "",
    EvacuationNameStr = "",
}
pb.__pb_EvacuationRecords.__name = "EvacuationRecords"
pb.__pb_EvacuationRecords.__index = pb.__pb_EvacuationRecords
pb.__pb_EvacuationRecords.__pairs = __pb_pairs

pb.EvacuationRecords = { __name = "EvacuationRecords", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_EvacuationRecords : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public ClientVersion string
---@field public GameTime number
---@field public EvacuationName number
---@field public EvacuationStageType number
---@field public EvacuationEnteredPlayerUinArrayStr string
---@field public EvacuationEnteredPlayerNameArrayStr string
---@field public EvacuationNameStr string

---@return pb_EvacuationRecords
function pb.EvacuationRecords:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerEnterEvacuationRecords = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    PlayerName = "",
    ArmedforceID = 0,
    ClientVersion = "",
    GameTime = 0,
    PlayerEnterEvacuationTime = 0,
    PlayerEnterEvacuationName = "",
    PlayerEnterEvacuationStageType = 0,
}
pb.__pb_PlayerEnterEvacuationRecords.__name = "PlayerEnterEvacuationRecords"
pb.__pb_PlayerEnterEvacuationRecords.__index = pb.__pb_PlayerEnterEvacuationRecords
pb.__pb_PlayerEnterEvacuationRecords.__pairs = __pb_pairs

pb.PlayerEnterEvacuationRecords = { __name = "PlayerEnterEvacuationRecords", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerEnterEvacuationRecords : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public PlayerName string
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public GameTime number
---@field public PlayerEnterEvacuationTime number
---@field public PlayerEnterEvacuationName string
---@field public PlayerEnterEvacuationStageType number

---@return pb_PlayerEnterEvacuationRecords
function pb.PlayerEnterEvacuationRecords:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerLeftEvacuationRecords = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    PlayerName = "",
    ArmedforceID = 0,
    ClientVersion = "",
    GameTime = 0,
    PlayerLeftEvacuationTime = 0,
    PlayerLeftEvacuationName = "",
    PlayerLeftEvacuationStageType = 0,
}
pb.__pb_PlayerLeftEvacuationRecords.__name = "PlayerLeftEvacuationRecords"
pb.__pb_PlayerLeftEvacuationRecords.__index = pb.__pb_PlayerLeftEvacuationRecords
pb.__pb_PlayerLeftEvacuationRecords.__pairs = __pb_pairs

pb.PlayerLeftEvacuationRecords = { __name = "PlayerLeftEvacuationRecords", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerLeftEvacuationRecords : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public PlayerName string
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public GameTime number
---@field public PlayerLeftEvacuationTime number
---@field public PlayerLeftEvacuationName string
---@field public PlayerLeftEvacuationStageType number

---@return pb_PlayerLeftEvacuationRecords
function pb.PlayerLeftEvacuationRecords:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsMPAiStat = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    MapName = "",
    DurationTime = 0,
    AiNum = 0,
    AiKickNum = 0,
    AiKillPlayer = 0,
    AiKillBot = 0,
    AiKilledByPlayer = 0,
    PlayerKillPlayer = 0,
    AiReDeployNum = 0,
    AiDosuicideNum = 0,
    AiDosuicideT1 = 0,
    AiDosuicideT2 = 0,
    AiDosuicideT3 = 0,
    AiDosuicideT4 = 0,
    AiDosuicideT5 = 0,
    NoMove6Sec = 0,
    NoMove4Sec = 0,
    NoMove2Sec = 0,
    AiSmokeNum = 0,
    AiSmokeWallNum = 0,
    AiSkillNum = 0,
    RescuePlayerNum = 0,
    RescueBotNum = 0,
    MedicRescuePlayerNum = 0,
    MedicRescueBotNum = 0,
    FindPathFail = 0,
    BattleRouteSearchFail = 0,
}
pb.__pb_DsMPAiStat.__name = "DsMPAiStat"
pb.__pb_DsMPAiStat.__index = pb.__pb_DsMPAiStat
pb.__pb_DsMPAiStat.__pairs = __pb_pairs

pb.DsMPAiStat = { __name = "DsMPAiStat", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsMPAiStat : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public MapName string
---@field public DurationTime number
---@field public AiNum number
---@field public AiKickNum number
---@field public AiKillPlayer number
---@field public AiKillBot number
---@field public AiKilledByPlayer number
---@field public PlayerKillPlayer number
---@field public AiReDeployNum number
---@field public AiDosuicideNum number
---@field public AiDosuicideT1 number
---@field public AiDosuicideT2 number
---@field public AiDosuicideT3 number
---@field public AiDosuicideT4 number
---@field public AiDosuicideT5 number
---@field public NoMove6Sec number
---@field public NoMove4Sec number
---@field public NoMove2Sec number
---@field public AiSmokeNum number
---@field public AiSmokeWallNum number
---@field public AiSkillNum number
---@field public RescuePlayerNum number
---@field public RescueBotNum number
---@field public MedicRescuePlayerNum number
---@field public MedicRescueBotNum number
---@field public FindPathFail number
---@field public BattleRouteSearchFail number

---@return pb_DsMPAiStat
function pb.DsMPAiStat:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedForceOutfitUpdate = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerID = 0,
    OufitIndex = 0,
    DurabilityPropID1 = 0,
    DurabilityPropLvl1 = 0,
    DurabilityPropID2 = 0,
    DurabilityPropLvl2 = 0,
    DurabilityPropID3 = 0,
    DurabilityPropLvl3 = 0,
}
pb.__pb_ArmedForceOutfitUpdate.__name = "ArmedForceOutfitUpdate"
pb.__pb_ArmedForceOutfitUpdate.__index = pb.__pb_ArmedForceOutfitUpdate
pb.__pb_ArmedForceOutfitUpdate.__pairs = __pb_pairs

pb.ArmedForceOutfitUpdate = { __name = "ArmedForceOutfitUpdate", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedForceOutfitUpdate : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerID number
---@field public OufitIndex number
---@field public DurabilityPropID1 number
---@field public DurabilityPropLvl1 number
---@field public DurabilityPropID2 number
---@field public DurabilityPropLvl2 number
---@field public DurabilityPropID3 number
---@field public DurabilityPropLvl3 number

---@return pb_ArmedForceOutfitUpdate
function pb.ArmedForceOutfitUpdate:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RecoveryAmountChangeFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    FlowId = 0,
    EventID = 0,
    Reason = 0,
    MoneyPaper = 0,
    RecoveryLevel = 0,
    RecoveryRate = 0,
    RecoveryAmount = 0,
    Amount = 0,
    OldAmount = 0,
    OldRate = 0,
    OldLevel = 0,
}
pb.__pb_RecoveryAmountChangeFlow.__name = "RecoveryAmountChangeFlow"
pb.__pb_RecoveryAmountChangeFlow.__index = pb.__pb_RecoveryAmountChangeFlow
pb.__pb_RecoveryAmountChangeFlow.__pairs = __pb_pairs

pb.RecoveryAmountChangeFlow = { __name = "RecoveryAmountChangeFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RecoveryAmountChangeFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public FlowId number
---@field public EventID number
---@field public Reason number
---@field public MoneyPaper number
---@field public RecoveryLevel number
---@field public RecoveryRate number
---@field public RecoveryAmount number
---@field public Amount number
---@field public OldAmount number
---@field public OldRate number
---@field public OldLevel number

---@return pb_RecoveryAmountChangeFlow
function pb.RecoveryAmountChangeFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSSyncLoginState = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    MapName = "",
    DSVersion = "",
    SyncPlayerCnt = 0,
    LoginPlayerCnt = 0,
    ReconnectCnt = 0,
    ConnectionCnt = 0,
    JoinCnt = 0,
    ConnectCnt = 0,
    LoginCnt = 0,
    CloseCnt = 0,
}
pb.__pb_DSSyncLoginState.__name = "DSSyncLoginState"
pb.__pb_DSSyncLoginState.__index = pb.__pb_DSSyncLoginState
pb.__pb_DSSyncLoginState.__pairs = __pb_pairs

pb.DSSyncLoginState = { __name = "DSSyncLoginState", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSSyncLoginState : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public MapName string
---@field public DSVersion string
---@field public SyncPlayerCnt number
---@field public LoginPlayerCnt number
---@field public ReconnectCnt number
---@field public ConnectionCnt number
---@field public JoinCnt number
---@field public ConnectCnt number
---@field public LoginCnt number
---@field public CloseCnt number

---@return pb_DSSyncLoginState
function pb.DSSyncLoginState:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSPlayerSyncLogin = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    MapName = "",
    DSVersion = "",
    ConnectCnt = 0,
    ConnectIPList = "",
    LoginCnt = 0,
    JoinCnt = 0,
    DSCloseConnCnt = 0,
    ClientCloseConnCnt = 0,
    CloseCnt = 0,
}
pb.__pb_DSPlayerSyncLogin.__name = "DSPlayerSyncLogin"
pb.__pb_DSPlayerSyncLogin.__index = pb.__pb_DSPlayerSyncLogin
pb.__pb_DSPlayerSyncLogin.__pairs = __pb_pairs

pb.DSPlayerSyncLogin = { __name = "DSPlayerSyncLogin", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSPlayerSyncLogin : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public MapName string
---@field public DSVersion string
---@field public ConnectCnt number
---@field public ConnectIPList string
---@field public LoginCnt number
---@field public JoinCnt number
---@field public DSCloseConnCnt number
---@field public ClientCloseConnCnt number
---@field public CloseCnt number

---@return pb_DSPlayerSyncLogin
function pb.DSPlayerSyncLogin:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMRedeployBagFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    PlayerId = 0,
    ExpertID = 0,
    MainWeaponID = 0,
    MainWeaponPreset = 0,
    SecondWeaponID = 0,
    SecondWeaponPreset = 0,
    MeleeWeaponID = 0,
    ArmedForceItem = 0,
}
pb.__pb_TDMRedeployBagFlow.__name = "TDMRedeployBagFlow"
pb.__pb_TDMRedeployBagFlow.__index = pb.__pb_TDMRedeployBagFlow
pb.__pb_TDMRedeployBagFlow.__pairs = __pb_pairs

pb.TDMRedeployBagFlow = { __name = "TDMRedeployBagFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMRedeployBagFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public PlayerId number
---@field public ExpertID number
---@field public MainWeaponID number
---@field public MainWeaponPreset number
---@field public SecondWeaponID number
---@field public SecondWeaponPreset number
---@field public MeleeWeaponID number
---@field public ArmedForceItem number

---@return pb_TDMRedeployBagFlow
function pb.TDMRedeployBagFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBreakthroughScoreFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    SectorID = 0,
    Camp = 0,
    TotalScore = "",
    AnchorTotalScore = "",
    AnchorInRedeploy = "",
    AnchorOutRedeploy = "",
    RedeployOnTeam = 0,
    RedeployOnRebornFlag = 0,
    PlayerNum = 0,
    GetSupportPoints = 0,
    UsedSupportPoints = 0,
    RemainingSupportPoints = 0,
}
pb.__pb_TDMBreakthroughScoreFlow.__name = "TDMBreakthroughScoreFlow"
pb.__pb_TDMBreakthroughScoreFlow.__index = pb.__pb_TDMBreakthroughScoreFlow
pb.__pb_TDMBreakthroughScoreFlow.__pairs = __pb_pairs

pb.TDMBreakthroughScoreFlow = { __name = "TDMBreakthroughScoreFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBreakthroughScoreFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public SectorID number
---@field public Camp number
---@field public TotalScore string
---@field public AnchorTotalScore string
---@field public AnchorInRedeploy string
---@field public AnchorOutRedeploy string
---@field public RedeployOnTeam number
---@field public RedeployOnRebornFlag number
---@field public PlayerNum number
---@field public GetSupportPoints number
---@field public UsedSupportPoints number
---@field public RemainingSupportPoints number

---@return pb_TDMBreakthroughScoreFlow
function pb.TDMBreakthroughScoreFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsBoxExtraDropFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    OpenBoxID = 0,
    UniqueName = "",
    DropLogicID0 = 0,
    BadluckTypes0 = 0,
    BadluckParas0 = 0,
    Counter0 = 0,
    ItemID0 = 0,
    DropLogicID1 = 0,
    BadluckTypes1 = 0,
    BadluckParas1 = 0,
    Counter1 = 0,
    ItemID1 = 0,
    DropLogicID2 = 0,
    BadluckTypes2 = 0,
    BadluckParas2 = 0,
    Counter2 = 0,
    ItemID2 = 0,
    DropLogicID3 = 0,
    BadluckTypes3 = 0,
    BadluckParas3 = 0,
    Counter3 = 0,
    ItemID3 = 0,
    DropLogicID4 = 0,
    BadluckTypes4 = 0,
    BadluckParas4 = 0,
    Counter4 = 0,
    ItemID4 = 0,
    DropLogicID5 = 0,
    BadluckTypes5 = 0,
    BadluckParas5 = 0,
    Counter5 = 0,
    ItemID5 = 0,
    DropLogicID6 = 0,
    BadluckTypes6 = 0,
    BadluckParas6 = 0,
    Counter6 = 0,
    ItemID6 = 0,
    DropLogicID7 = 0,
    BadluckTypes7 = 0,
    BadluckParas7 = 0,
    Counter7 = 0,
    ItemID7 = 0,
    DropLogicID8 = 0,
    BadluckTypes8 = 0,
    BadluckParas8 = 0,
    Counter8 = 0,
    ItemID8 = 0,
    DropLogicID9 = 0,
    BadluckTypes9 = 0,
    BadluckParas9 = 0,
    Counter9 = 0,
    ItemID9 = 0,
}
pb.__pb_DsBoxExtraDropFlow.__name = "DsBoxExtraDropFlow"
pb.__pb_DsBoxExtraDropFlow.__index = pb.__pb_DsBoxExtraDropFlow
pb.__pb_DsBoxExtraDropFlow.__pairs = __pb_pairs

pb.DsBoxExtraDropFlow = { __name = "DsBoxExtraDropFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsBoxExtraDropFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public OpenBoxID number
---@field public UniqueName string
---@field public DropLogicID0 number
---@field public BadluckTypes0 number
---@field public BadluckParas0 number
---@field public Counter0 number
---@field public ItemID0 number
---@field public DropLogicID1 number
---@field public BadluckTypes1 number
---@field public BadluckParas1 number
---@field public Counter1 number
---@field public ItemID1 number
---@field public DropLogicID2 number
---@field public BadluckTypes2 number
---@field public BadluckParas2 number
---@field public Counter2 number
---@field public ItemID2 number
---@field public DropLogicID3 number
---@field public BadluckTypes3 number
---@field public BadluckParas3 number
---@field public Counter3 number
---@field public ItemID3 number
---@field public DropLogicID4 number
---@field public BadluckTypes4 number
---@field public BadluckParas4 number
---@field public Counter4 number
---@field public ItemID4 number
---@field public DropLogicID5 number
---@field public BadluckTypes5 number
---@field public BadluckParas5 number
---@field public Counter5 number
---@field public ItemID5 number
---@field public DropLogicID6 number
---@field public BadluckTypes6 number
---@field public BadluckParas6 number
---@field public Counter6 number
---@field public ItemID6 number
---@field public DropLogicID7 number
---@field public BadluckTypes7 number
---@field public BadluckParas7 number
---@field public Counter7 number
---@field public ItemID7 number
---@field public DropLogicID8 number
---@field public BadluckTypes8 number
---@field public BadluckParas8 number
---@field public Counter8 number
---@field public ItemID8 number
---@field public DropLogicID9 number
---@field public BadluckTypes9 number
---@field public BadluckParas9 number
---@field public Counter9 number
---@field public ItemID9 number

---@return pb_DsBoxExtraDropFlow
function pb.DsBoxExtraDropFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowWuMing = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillUseCnt = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    AntiPersonnelFireCount = 0,
    AntiPersonnelKillCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    KnockCountWhenUseSilenceZone = 0,
    SilenceZoneUseCount = 0,
    BeKnockedCountWhenUseSilenceZone = 0,
    SilenceZoneAntiDetectCount = 0,
    KnifeDroneUseCount = 0,
    KnifeDroneLockedUseCount = 0,
    KnifeDroneKillCount = 0,
    KnifeDroneLockedKillCount = 0,
    KnifeDroneLockedHitCount = 0,
    KnifeDroneTimeBetweenHitAndKnocked = 0,
    KnifeDroneDestoryedCount = 0,
    C303FlashUseCount = 0,
    C303FlashHitRealPlayerCount = 0,
    C303FlashHitAILabCount = 0,
    C303FlashHitAICount = 0,
    C303FlashHeavyHitCount = 0,
    C303FlashMidHitCount = 0,
    C303FlashLightHitCount = 0,
    C303FlashBackHitCount = 0,
    C303FlashKnockedAfter10SCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    BotType = 0,
    SkillAbilityCustomUse1 = 0,
    SkillAbilityCustomUse2 = 0,
    C303KnifeDroneHitPlayerCount = 0,
    C303KnifeDroneLockedHitPlayerCount = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowWuMing.__name = "ArmedforceDataFlowWuMing"
pb.__pb_ArmedforceDataFlowWuMing.__index = pb.__pb_ArmedforceDataFlowWuMing
pb.__pb_ArmedforceDataFlowWuMing.__pairs = __pb_pairs

pb.ArmedforceDataFlowWuMing = { __name = "ArmedforceDataFlowWuMing", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowWuMing : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillUseCnt number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public AntiPersonnelFireCount number
---@field public AntiPersonnelKillCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public KnockCountWhenUseSilenceZone number
---@field public SilenceZoneUseCount number
---@field public BeKnockedCountWhenUseSilenceZone number
---@field public SilenceZoneAntiDetectCount number
---@field public KnifeDroneUseCount number
---@field public KnifeDroneLockedUseCount number
---@field public KnifeDroneKillCount number
---@field public KnifeDroneLockedKillCount number
---@field public KnifeDroneLockedHitCount number
---@field public KnifeDroneTimeBetweenHitAndKnocked number
---@field public KnifeDroneDestoryedCount number
---@field public C303FlashUseCount number
---@field public C303FlashHitRealPlayerCount number
---@field public C303FlashHitAILabCount number
---@field public C303FlashHitAICount number
---@field public C303FlashHeavyHitCount number
---@field public C303FlashMidHitCount number
---@field public C303FlashLightHitCount number
---@field public C303FlashBackHitCount number
---@field public C303FlashKnockedAfter10SCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public BotType number
---@field public SkillAbilityCustomUse1 number
---@field public SkillAbilityCustomUse2 number
---@field public C303KnifeDroneHitPlayerCount number
---@field public C303KnifeDroneLockedHitPlayerCount number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowWuMing
function pb.ArmedforceDataFlowWuMing:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchSOLDropFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    MatchUUID = "",
    ExpTag = "",
    DropLogicIDs = "",
    DropKeys = "",
    DropKeyCounters = "",
}
pb.__pb_MatchSOLDropFlow.__name = "MatchSOLDropFlow"
pb.__pb_MatchSOLDropFlow.__index = pb.__pb_MatchSOLDropFlow
pb.__pb_MatchSOLDropFlow.__pairs = __pb_pairs

pb.MatchSOLDropFlow = { __name = "MatchSOLDropFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchSOLDropFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public MatchUUID string
---@field public ExpTag string
---@field public DropLogicIDs string
---@field public DropKeys string
---@field public DropKeyCounters string

---@return pb_MatchSOLDropFlow
function pb.MatchSOLDropFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DsSOLNightEventFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapId = 0,
    Difficulty = 0,
    EventType = 0,
    EquipmentState = 0,
    Content = "",
}
pb.__pb_DsSOLNightEventFlow.__name = "DsSOLNightEventFlow"
pb.__pb_DsSOLNightEventFlow.__index = pb.__pb_DsSOLNightEventFlow
pb.__pb_DsSOLNightEventFlow.__pairs = __pb_pairs

pb.DsSOLNightEventFlow = { __name = "DsSOLNightEventFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DsSOLNightEventFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapId number
---@field public Difficulty number
---@field public EventType number
---@field public EquipmentState number
---@field public Content string

---@return pb_DsSOLNightEventFlow
function pb.DsSOLNightEventFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_DSEventReport = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapName = "",
    DSVersion = "",
    EventName = "",
    Count = 0,
    RelatePlayerID = 0,
    RelateInfo = "",
}
pb.__pb_DSEventReport.__name = "DSEventReport"
pb.__pb_DSEventReport.__index = pb.__pb_DSEventReport
pb.__pb_DSEventReport.__pairs = __pb_pairs

pb.DSEventReport = { __name = "DSEventReport", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_DSEventReport : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapName string
---@field public DSVersion string
---@field public EventName string
---@field public Count number
---@field public RelatePlayerID number
---@field public RelateInfo string

---@return pb_DSEventReport
function pb.DSEventReport:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBreakthroughReviveGiveUpGuideFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    FinishGuideReason = 0,
    DeltaTimeSinceDyingShowRescue = 0,
}
pb.__pb_TDMBreakthroughReviveGiveUpGuideFlow.__name = "TDMBreakthroughReviveGiveUpGuideFlow"
pb.__pb_TDMBreakthroughReviveGiveUpGuideFlow.__index = pb.__pb_TDMBreakthroughReviveGiveUpGuideFlow
pb.__pb_TDMBreakthroughReviveGiveUpGuideFlow.__pairs = __pb_pairs

pb.TDMBreakthroughReviveGiveUpGuideFlow = { __name = "TDMBreakthroughReviveGiveUpGuideFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBreakthroughReviveGiveUpGuideFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public FinishGuideReason number
---@field public DeltaTimeSinceDyingShowRescue number

---@return pb_TDMBreakthroughReviveGiveUpGuideFlow
function pb.TDMBreakthroughReviveGiveUpGuideFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBreakthroughRedeployLongStayGuideFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    FirstClickRedeployButtonDeltaTime = 0,
    ClickRedeployButtonTimes = 0,
    IsGiveUpGameInRedeploy = 0,
    AutoRedeployGuideShowTimes = 0,
    InRedeployTime = 0,
    IsAutoRedeploy = 0,
}
pb.__pb_TDMBreakthroughRedeployLongStayGuideFlow.__name = "TDMBreakthroughRedeployLongStayGuideFlow"
pb.__pb_TDMBreakthroughRedeployLongStayGuideFlow.__index = pb.__pb_TDMBreakthroughRedeployLongStayGuideFlow
pb.__pb_TDMBreakthroughRedeployLongStayGuideFlow.__pairs = __pb_pairs

pb.TDMBreakthroughRedeployLongStayGuideFlow = { __name = "TDMBreakthroughRedeployLongStayGuideFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBreakthroughRedeployLongStayGuideFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public FirstClickRedeployButtonDeltaTime number
---@field public ClickRedeployButtonTimes number
---@field public IsGiveUpGameInRedeploy number
---@field public AutoRedeployGuideShowTimes number
---@field public InRedeployTime number
---@field public IsAutoRedeploy number

---@return pb_TDMBreakthroughRedeployLongStayGuideFlow
function pb.TDMBreakthroughRedeployLongStayGuideFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SpatialPrecomputedVisibility = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomId = 0,
    MapName = "",
    ViewRate = 0,
    InvisibleRate = 0,
    AICullRate = 0,
    PlayerCullRate = 0,
    SyncAICullRate = 0,
    SyncPlayerCullRate = 0,
}
pb.__pb_SpatialPrecomputedVisibility.__name = "SpatialPrecomputedVisibility"
pb.__pb_SpatialPrecomputedVisibility.__index = pb.__pb_SpatialPrecomputedVisibility
pb.__pb_SpatialPrecomputedVisibility.__pairs = __pb_pairs

pb.SpatialPrecomputedVisibility = { __name = "SpatialPrecomputedVisibility", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SpatialPrecomputedVisibility : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomId number
---@field public MapName string
---@field public ViewRate number
---@field public InvisibleRate number
---@field public AICullRate number
---@field public PlayerCullRate number
---@field public SyncAICullRate number
---@field public SyncPlayerCullRate number

---@return pb_SpatialPrecomputedVisibility
function pb.SpatialPrecomputedVisibility:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBreakthroughCommanderFastRedeployFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    SectorID = 0,
    Camp = 0,
    TLogStr = "",
}
pb.__pb_TDMBreakthroughCommanderFastRedeployFlow.__name = "TDMBreakthroughCommanderFastRedeployFlow"
pb.__pb_TDMBreakthroughCommanderFastRedeployFlow.__index = pb.__pb_TDMBreakthroughCommanderFastRedeployFlow
pb.__pb_TDMBreakthroughCommanderFastRedeployFlow.__pairs = __pb_pairs

pb.TDMBreakthroughCommanderFastRedeployFlow = { __name = "TDMBreakthroughCommanderFastRedeployFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBreakthroughCommanderFastRedeployFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public SectorID number
---@field public Camp number
---@field public TLogStr string

---@return pb_TDMBreakthroughCommanderFastRedeployFlow
function pb.TDMBreakthroughCommanderFastRedeployFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_WeaponDataFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    HeroId = 0,
    WeaponId = 0,
    WeaponPartsIdList = "",
    AdvantageousRange = 0,
    RecoilControl = 0,
    ControlSpeed = 0,
    WeaponStability = 0,
    ShootDispersion = 0,
    AimContinueVerticalRecoilScale = 0,
    AimContinueHorizontalRecoilScale = 0,
    AimSingleVerticalRecoilScale = 0,
    AimSingleHorizontalRecoilScale = 0,
    KeepFireStability = 0,
    AimingOnAnimTime = 0,
    RunningToAimingDelay = 0,
    AimMoveSpeed = 0,
}
pb.__pb_WeaponDataFlow.__name = "WeaponDataFlow"
pb.__pb_WeaponDataFlow.__index = pb.__pb_WeaponDataFlow
pb.__pb_WeaponDataFlow.__pairs = __pb_pairs

pb.WeaponDataFlow = { __name = "WeaponDataFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_WeaponDataFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public HeroId number
---@field public WeaponId number
---@field public WeaponPartsIdList string
---@field public AdvantageousRange number
---@field public RecoilControl number
---@field public ControlSpeed number
---@field public WeaponStability number
---@field public ShootDispersion number
---@field public AimContinueVerticalRecoilScale number
---@field public AimContinueHorizontalRecoilScale number
---@field public AimSingleVerticalRecoilScale number
---@field public AimSingleHorizontalRecoilScale number
---@field public KeepFireStability number
---@field public AimingOnAnimTime number
---@field public RunningToAimingDelay number
---@field public AimMoveSpeed number

---@return pb_WeaponDataFlow
function pb.WeaponDataFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_BattlefieldWeaponScoreFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    WeaponId = 0,
    WeaponScoreStr = "",
}
pb.__pb_BattlefieldWeaponScoreFlow.__name = "BattlefieldWeaponScoreFlow"
pb.__pb_BattlefieldWeaponScoreFlow.__index = pb.__pb_BattlefieldWeaponScoreFlow
pb.__pb_BattlefieldWeaponScoreFlow.__pairs = __pb_pairs

pb.BattlefieldWeaponScoreFlow = { __name = "BattlefieldWeaponScoreFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_BattlefieldWeaponScoreFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public WeaponId number
---@field public WeaponScoreStr string

---@return pb_BattlefieldWeaponScoreFlow
function pb.BattlefieldWeaponScoreFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMTacticalConquestEvolutionRebornFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    EnterRedeployTimes = 0,
    DirectRedeployTimes = 0,
    AutoRedeployTimes = 0,
}
pb.__pb_TDMTacticalConquestEvolutionRebornFlow.__name = "TDMTacticalConquestEvolutionRebornFlow"
pb.__pb_TDMTacticalConquestEvolutionRebornFlow.__index = pb.__pb_TDMTacticalConquestEvolutionRebornFlow
pb.__pb_TDMTacticalConquestEvolutionRebornFlow.__pairs = __pb_pairs

pb.TDMTacticalConquestEvolutionRebornFlow = { __name = "TDMTacticalConquestEvolutionRebornFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMTacticalConquestEvolutionRebornFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public EnterRedeployTimes number
---@field public DirectRedeployTimes number
---@field public AutoRedeployTimes number

---@return pb_TDMTacticalConquestEvolutionRebornFlow
function pb.TDMTacticalConquestEvolutionRebornFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBattleFieldCommanderPlayerInfoFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    SectorID = 0,
    PlayerId = 0,
    CampID = 0,
    TeamID = 0,
    OldTeamID = 0,
    TeamIdentity = 0,
    PlayerRole = 0,
}
pb.__pb_TDMBattleFieldCommanderPlayerInfoFlow.__name = "TDMBattleFieldCommanderPlayerInfoFlow"
pb.__pb_TDMBattleFieldCommanderPlayerInfoFlow.__index = pb.__pb_TDMBattleFieldCommanderPlayerInfoFlow
pb.__pb_TDMBattleFieldCommanderPlayerInfoFlow.__pairs = __pb_pairs

pb.TDMBattleFieldCommanderPlayerInfoFlow = { __name = "TDMBattleFieldCommanderPlayerInfoFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBattleFieldCommanderPlayerInfoFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public SectorID number
---@field public PlayerId number
---@field public CampID number
---@field public TeamID number
---@field public OldTeamID number
---@field public TeamIdentity number
---@field public PlayerRole number

---@return pb_TDMBattleFieldCommanderPlayerInfoFlow
function pb.TDMBattleFieldCommanderPlayerInfoFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TDMBattleFieldCommanderModeVoteFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MapID = 0,
    RoomId = 0,
    PlayerId = 0,
    VoteStatus = 0,
    VoteNum = 0,
    BIsCommader = 0,
}
pb.__pb_TDMBattleFieldCommanderModeVoteFlow.__name = "TDMBattleFieldCommanderModeVoteFlow"
pb.__pb_TDMBattleFieldCommanderModeVoteFlow.__index = pb.__pb_TDMBattleFieldCommanderModeVoteFlow
pb.__pb_TDMBattleFieldCommanderModeVoteFlow.__pairs = __pb_pairs

pb.TDMBattleFieldCommanderModeVoteFlow = { __name = "TDMBattleFieldCommanderModeVoteFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TDMBattleFieldCommanderModeVoteFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MapID number
---@field public RoomId number
---@field public PlayerId number
---@field public VoteStatus number
---@field public VoteNum number
---@field public BIsCommader number

---@return pb_TDMBattleFieldCommanderModeVoteFlow
function pb.TDMBattleFieldCommanderModeVoteFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TdmBreakthroughAIFireDataFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    SectorID = 0,
    CampId = 0,
    MatchModeID = 0,
    AverageFireNum = 0,
}
pb.__pb_TdmBreakthroughAIFireDataFlow.__name = "TdmBreakthroughAIFireDataFlow"
pb.__pb_TdmBreakthroughAIFireDataFlow.__index = pb.__pb_TdmBreakthroughAIFireDataFlow
pb.__pb_TdmBreakthroughAIFireDataFlow.__pairs = __pb_pairs

pb.TdmBreakthroughAIFireDataFlow = { __name = "TdmBreakthroughAIFireDataFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TdmBreakthroughAIFireDataFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public SectorID number
---@field public CampId number
---@field public MatchModeID number
---@field public AverageFireNum number

---@return pb_TdmBreakthroughAIFireDataFlow
function pb.TdmBreakthroughAIFireDataFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TdmBreakthroughDataForAIFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    MatchModeID = 0,
    LevelBrokenNum = 0,
    FortificationDestoryedNum = 0,
    PlayerSkillExplodeNum = 0,
    VehicleExplodeNum = 0,
    FieldSupportNum = 0,
    FieldSupportMissileNum = 0,
}
pb.__pb_TdmBreakthroughDataForAIFlow.__name = "TdmBreakthroughDataForAIFlow"
pb.__pb_TdmBreakthroughDataForAIFlow.__index = pb.__pb_TdmBreakthroughDataForAIFlow
pb.__pb_TdmBreakthroughDataForAIFlow.__pairs = __pb_pairs

pb.TdmBreakthroughDataForAIFlow = { __name = "TdmBreakthroughDataForAIFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TdmBreakthroughDataForAIFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public MatchModeID number
---@field public LevelBrokenNum number
---@field public FortificationDestoryedNum number
---@field public PlayerSkillExplodeNum number
---@field public VehicleExplodeNum number
---@field public FieldSupportNum number
---@field public FieldSupportMissileNum number

---@return pb_TdmBreakthroughDataForAIFlow
function pb.TdmBreakthroughDataForAIFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchAreaSetFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    MatchArea = "",
    OldMatchArea = "",
    OldMatchAreaRtt = 0,
    NewMatchAreaRtt = 0,
}
pb.__pb_MatchAreaSetFlow.__name = "MatchAreaSetFlow"
pb.__pb_MatchAreaSetFlow.__index = pb.__pb_MatchAreaSetFlow
pb.__pb_MatchAreaSetFlow.__pairs = __pb_pairs

pb.MatchAreaSetFlow = { __name = "MatchAreaSetFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchAreaSetFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public MatchArea string
---@field public OldMatchArea string
---@field public OldMatchAreaRtt number
---@field public NewMatchAreaRtt number

---@return pb_MatchAreaSetFlow
function pb.MatchAreaSetFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ArmedforceDataFlowOldDeng = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    RoomID = 0,
    MapID = 0,
    HeroID = 0,
    ArmedforceID = 0,
    ClientVersion = "",
    SeasonLvl = 0,
    GameTime = 0,
    Escape = 0,
    CollapseCnt = 0,
    BeRescuedCnt = 0,
    RescuedCnt = 0,
    DamageNum = 0,
    KillPlayerNum = 0,
    KillAINum = 0,
    CarryInBagValue = 0,
    CarryOutBagValue = 0,
    PassiveSkillUseCnt = 0,
    AbilityUseCnt = 0,
    ArmedforceItem1UseCnt = 0,
    ArmedforceItem2UseCnt = 0,
    AbilityAvgUseCntInBattle = 0,
    ArmedforceItem1AvgUseCntInBattle = 0,
    ArmedforceItem2AvgUseCntInBattle = 0,
    PassiveSkillAvgUseCntInBattle = 0,
    AbilityUseCntc = 0,
    ArmedforceItem1UseCntPerMinute = 0,
    ArmedforceItem2UseCntPerMinute = 0,
    EnermyCastSkillCntIn30M = 0,
    EnermyCastSkillCntIn75M = 0,
    EnermyCastSkillCntIn200M = 0,
    RescueTeamateCnt = 0,
    RescueCampCnt = 0,
    MakePlayerDamageInVehicle = 0,
    MakeVehicleDamageInVehicle = 0,
    AvgKillCnt = 0,
    AvgAssistCnt = 0,
    AvgDieCnt = 0,
    AvgRescueCnt = 0,
    AntiPersonnelFireCount = 0,
    AntiPersonnelKillCount = 0,
    EMPFireCount = 0,
    EMPAffectedCount = 0,
    ClaymoreTriggerCount = 0,
    ClaymoreKillCount = 0,
    AntiTankTriggerCount = 0,
    AntiTankKillCount = 0,
    AdrenalineActiveCount = 0,
    AdrenalineTotalLevelCount = 0,
    AdrenalineTotalKillCount = 0,
    AmmoPackSelfCount = 0,
    AmmoPackOtherCount = 0,
    HealthPackSelfCount = 0,
    HelthPackOtherCount = 0,
    AmmoBoxUseCount = 0,
    AmmoBoxEffectCount = 0,
    HealthBoxUseCount = 0,
    HealthBoxEffectCount = 0,
    InjectionUseCount = 0,
    InjectionHealAmount = 0,
    ArmorPlateUseCount = 0,
    ArmorPlateDefenceAmount = 0,
    HighExplosionFireCount = 0,
    HighExplosionKillCount = 0,
    SmokeExplosionFireCount = 0,
    SmokeExplosionHitPlayerCount = 0,
    FlameThrowerUseCount = 0,
    FlameThrowerKillCount = 0,
    C303PassiveBeTriggeredCount = 0,
    C303PassiveBeTriggeredTime = 0,
    C303PassiveSOLDisturbanceTime = 0,
    C303PassiveMPDisturbanceTime = 0,
    FootprintSearchCount = 0,
    BionicSpyUseCount = 0,
    BionicSpySuccessCount = 0,
    BionicSpyFailedCount = 0,
    BionicSpyDeadCount = 0,
    BionicBirdUseCount = 0,
    BionicBirdMarkCount = 0,
    BionicBirdLaunchCount = 0,
    BionicBirdLaunchHitCount = 0,
    BionicBirdDestroyedCount = 0,
    C203EMPCastCount = 0,
    C203EMPHitPlayerCount = 0,
    C203EMPAssistCount = 0,
    BFPAntiPersonnelFireCount = 0,
    BFPAntiPersonnelKillCount = 0,
    BFPEMPFireCount = 0,
    BFPEMPAffectedCount = 0,
    BFPLaserPointerLockCompleteNum = 0,
    BFPLaserPointerAssistNum = 0,
    BFPRebornFlagUseNum = 0,
    BFPADSDefenceCount = 0,
    BFPADSCoolDownCount = 0,
    BFPAT4DestoryVehicleCount = 0,
    BFPSpearDestoryVehicleCount = 0,
    BFPStingerDestoryVehicleCount = 0,
    BFPAT4DamageCount = 0,
    BFPSpearDamageCount = 0,
    BFPStingerDamageCount = 0,
}
pb.__pb_ArmedforceDataFlowOldDeng.__name = "ArmedforceDataFlowOldDeng"
pb.__pb_ArmedforceDataFlowOldDeng.__index = pb.__pb_ArmedforceDataFlowOldDeng
pb.__pb_ArmedforceDataFlowOldDeng.__pairs = __pb_pairs

pb.ArmedforceDataFlowOldDeng = { __name = "ArmedforceDataFlowOldDeng", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ArmedforceDataFlowOldDeng : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public RoomID number
---@field public MapID number
---@field public HeroID number
---@field public ArmedforceID number
---@field public ClientVersion string
---@field public SeasonLvl number
---@field public GameTime number
---@field public Escape number
---@field public CollapseCnt number
---@field public BeRescuedCnt number
---@field public RescuedCnt number
---@field public DamageNum number
---@field public KillPlayerNum number
---@field public KillAINum number
---@field public CarryInBagValue number
---@field public CarryOutBagValue number
---@field public PassiveSkillUseCnt number
---@field public AbilityUseCnt number
---@field public ArmedforceItem1UseCnt number
---@field public ArmedforceItem2UseCnt number
---@field public AbilityAvgUseCntInBattle number
---@field public ArmedforceItem1AvgUseCntInBattle number
---@field public ArmedforceItem2AvgUseCntInBattle number
---@field public PassiveSkillAvgUseCntInBattle number
---@field public AbilityUseCntc number
---@field public ArmedforceItem1UseCntPerMinute number
---@field public ArmedforceItem2UseCntPerMinute number
---@field public EnermyCastSkillCntIn30M number
---@field public EnermyCastSkillCntIn75M number
---@field public EnermyCastSkillCntIn200M number
---@field public RescueTeamateCnt number
---@field public RescueCampCnt number
---@field public MakePlayerDamageInVehicle number
---@field public MakeVehicleDamageInVehicle number
---@field public AvgKillCnt number
---@field public AvgAssistCnt number
---@field public AvgDieCnt number
---@field public AvgRescueCnt number
---@field public AntiPersonnelFireCount number
---@field public AntiPersonnelKillCount number
---@field public EMPFireCount number
---@field public EMPAffectedCount number
---@field public ClaymoreTriggerCount number
---@field public ClaymoreKillCount number
---@field public AntiTankTriggerCount number
---@field public AntiTankKillCount number
---@field public AdrenalineActiveCount number
---@field public AdrenalineTotalLevelCount number
---@field public AdrenalineTotalKillCount number
---@field public AmmoPackSelfCount number
---@field public AmmoPackOtherCount number
---@field public HealthPackSelfCount number
---@field public HelthPackOtherCount number
---@field public AmmoBoxUseCount number
---@field public AmmoBoxEffectCount number
---@field public HealthBoxUseCount number
---@field public HealthBoxEffectCount number
---@field public InjectionUseCount number
---@field public InjectionHealAmount number
---@field public ArmorPlateUseCount number
---@field public ArmorPlateDefenceAmount number
---@field public HighExplosionFireCount number
---@field public HighExplosionKillCount number
---@field public SmokeExplosionFireCount number
---@field public SmokeExplosionHitPlayerCount number
---@field public FlameThrowerUseCount number
---@field public FlameThrowerKillCount number
---@field public C303PassiveBeTriggeredCount number
---@field public C303PassiveBeTriggeredTime number
---@field public C303PassiveSOLDisturbanceTime number
---@field public C303PassiveMPDisturbanceTime number
---@field public FootprintSearchCount number
---@field public BionicSpyUseCount number
---@field public BionicSpySuccessCount number
---@field public BionicSpyFailedCount number
---@field public BionicSpyDeadCount number
---@field public BionicBirdUseCount number
---@field public BionicBirdMarkCount number
---@field public BionicBirdLaunchCount number
---@field public BionicBirdLaunchHitCount number
---@field public BionicBirdDestroyedCount number
---@field public C203EMPCastCount number
---@field public C203EMPHitPlayerCount number
---@field public C203EMPAssistCount number
---@field public BFPAntiPersonnelFireCount number
---@field public BFPAntiPersonnelKillCount number
---@field public BFPEMPFireCount number
---@field public BFPEMPAffectedCount number
---@field public BFPLaserPointerLockCompleteNum number
---@field public BFPLaserPointerAssistNum number
---@field public BFPRebornFlagUseNum number
---@field public BFPADSDefenceCount number
---@field public BFPADSCoolDownCount number
---@field public BFPAT4DestoryVehicleCount number
---@field public BFPSpearDestoryVehicleCount number
---@field public BFPStingerDestoryVehicleCount number
---@field public BFPAT4DamageCount number
---@field public BFPSpearDamageCount number
---@field public BFPStingerDamageCount number

---@return pb_ArmedforceDataFlowOldDeng
function pb.ArmedforceDataFlowOldDeng:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_BulletFXMismatchFlow = {
    GameSvrId = "",
    DtEventTime = "",
    VGameAppid = "",
    PlatID = 0,
    IZoneAreaID = 0,
    Vopenid = "",
    PlayerId = 0,
    RoomID = 0,
    IsOptimized = 0,
    BulletYawDiff = 0,
    AICharacterID = 0,
}
pb.__pb_BulletFXMismatchFlow.__name = "BulletFXMismatchFlow"
pb.__pb_BulletFXMismatchFlow.__index = pb.__pb_BulletFXMismatchFlow
pb.__pb_BulletFXMismatchFlow.__pairs = __pb_pairs

pb.BulletFXMismatchFlow = { __name = "BulletFXMismatchFlow", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_BulletFXMismatchFlow : ProtoBase
---@field public GameSvrId string
---@field public DtEventTime string
---@field public VGameAppid string
---@field public PlatID number
---@field public IZoneAreaID number
---@field public Vopenid string
---@field public PlayerId number
---@field public RoomID number
---@field public IsOptimized number
---@field public BulletYawDiff number
---@field public AICharacterID number

---@return pb_BulletFXMismatchFlow
function pb.BulletFXMismatchFlow:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


