----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRankingList)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class HistoryTitleSubItemList : UIWidgetBase
local HistoryTitleSubItemList = ui("HistoryTitleSubItemList")


function HistoryTitleSubItemList:Ctor()
    self._wtPeriodTxtBlock = self:Wnd("DFTextBlock_66", UITextBlock)
    self._wtWrapBox = self:Wnd("DFWrapBox_1", UIWidgetBase)
    self._wtWrapBox:ClearChildren()
end


function HistoryTitleSubItemList:RefreshTitleSubItemList(period, resList, isCurrent, playerName)
    if period == nil or resList == nil then
        return
    end
    -- She3特殊处理
    if period == -1 then
        self._wtPeriodTxtBlock:SetText(Module.RankingList.Config.Loc.InSeason)
    else
        if isCurrent then
            self._wtPeriodTxtBlock:SetText(string.format(Module.RankingList.Config.Loc.CurrentPeriodNum, period))
            local green = FLinearColor(0.0588, 0.9686, 0.5882, 1)
            self._wtPeriodTxtBlock:SetColorAndOpacity(FSlateColor(green))  -- 设置为绿色
        else
            self._wtPeriodTxtBlock:SetText(string.format(Module.RankingList.Config.Loc.PeriodNum, period))
        end
    end
    -- resList[i]: playerName, titleId, adCode, ranking
    -- She3特殊处理:period为-1时代表敬请期待
    if period == -1 then
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItem, self._wtWrapBox)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            local slot = UWidgetLayoutLibrary.SlotAsWrapBoxSlot(uiIns)
            slot:SetFillEmptySpace(true)
            uiIns:SetDesc(Module.RankingList.Config.Loc.ComingSoon)
        end
    elseif #resList == 0 then  -- 当前期:虚位以待
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItem, self._wtWrapBox)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            local slot = UWidgetLayoutLibrary.SlotAsWrapBoxSlot(uiIns)
            slot:SetFillEmptySpace(true)
            uiIns:SetDesc(Module.RankingList.Config.Loc.EmptyDesc)
        end
    else
        for i, item in ipairs(resList) do
            local titleId, adCode, ranking = item.titleId, item.adCode, item.ranking
            loginfo(string.format("[%d]: playerName=%s, titleId=%s, adCode=%s, ranking=%s", 
                i, playerName, titleId, adCode, ranking))
            local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.HistoryTitleSubItem, self._wtWrapBox)
            local uiIns = getfromweak(weakUIIns)
            if uiIns then
                uiIns:RefreshTitleSubItem(playerName, titleId, adCode, ranking)
            end
        end
    end
end

return HistoryTitleSubItemList
