local UKismetSystemLibrary = import "KismetSystemLibrary"
local Locations = require "Automation.PerfTest.Locations"

return function()
    local GameInstance = GetGameInstance()
    local cnt = 0
    for i, Location in ipairs(Locations.SolPlayerLocations_InFront) do
        cnt = cnt + 1
        if(cnt <= 5)then
        local Cmd = string.format("GMSpawnDFMCharacterInLocation 88000000009 11080002002 11050001002 11070002006 10010000032 10010000035 %f %f %f", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
        elseif(cnt <= 10)then
        local Cmd = string.format("GMSpawnDFMCharacterInLocation 88000000010 11080003003 11050001003 11070003001 10010000037 10010000039 %f %f %f", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
        elseif(cnt <= 15)then
        local Cmd = string.format("GMSpawnDFMCharacterInLocation 88000000012 11080005001 11050002001 11070003003 10010000109 10010000212 %f %f %f", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
        elseif(cnt <= 20)then
        local Cmd = string.format("GMSpawnDFMCharacterInLocation 88000000013 11080005003 11050002002 11070004002 10050000003 10060000010 %f %f %f", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
        elseif(cnt <= 25)then
        local Cmd = string.format("GMSpawnDFMCharacterInLocation 88000000017 11080005004 11050002003 11070005001 10010000115 10010000094 %f %f %f", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
        else
        local Cmd = string.format("GMSpawnDFMCharacterInLocation 88000000020 11080005005 11050003002 11070005002 10050000027 10010000202 %f %f %f", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
        end
        break
    end
end
