local LazyObject = {}

LazyObject.bPrintDebugMsg = IsInEditor()

local fPrintLazyInitPos = function()
    local info = debug.getinfo(4)
    local pos = string.format("LazyObject lazy init %s:%d", info.short_src, info.linedefined)
    loginfo(pos)
end

local fLazyInit = function(t, k)
    local fLazyObjInitFunc = t.__fLazyObjInitFunc
    local lazyObjInitParams = t.__lazyObjInitParams
    t.__fLazyObjInitFunc = nil
    t.__lazyObjInitParams = nil
    setmetatable(t, nil)

    local cloneT = clone(t)
    table.clear(t)

    fLazyObjInitFunc(t, table.unpack(lazyObjInitParams))

    for k,v in pairs(cloneT) do
        t[k] = v
    end

    if LazyObject.bPrintDebugMsg then
        fPrintLazyInitPos()
    end
end

local fLazyObjectIndex = function(t, k)
    fLazyInit(t, k)

    return t[k]
end

-- local fLazyObjectPairs = function(t, k)
--     fLazyInit(t, k)

--     return next, t, nil
-- end

local lazyObjectMeta = {__index = fLazyObjectIndex}

function LazyObject.CreateLazyObject(fLazyObjInitFunc, ...)
    assert(type(fLazyObjInitFunc) == "function")

    local lazyObj = setmetatable({}, lazyObjectMeta)
    lazyObj.__fLazyObjInitFunc = fLazyObjInitFunc
    lazyObj.__lazyObjInitParams = {...}
    if IsInEditor() then
        lazyObj.__lazy = true
    end

    return lazyObj
end

return LazyObject