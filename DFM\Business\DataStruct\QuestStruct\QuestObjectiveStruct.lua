---@class QuestObjectiveStruct : LuaObject
local QuestObjectiveStruct = class("QuestObjectiveStruct", LuaObject)
OverrideClassByDynamicUD(
	QuestObjectiveStruct, true,
	{"id", FieldType.Integer},
	{"type", FieldType.Integer},
	{"param1", FieldType.String},
	{"param2", FieldType.String},
	{"listParam", FieldType.Table},
	{"numNeeded", FieldType.Integer},
	{"desc", FieldType.String},
	{"bMustComplete", FieldType.Boolean},
	{"bShowTracking", FieldType.Boolean},
	{"preObjectiveID", FieldType.Integer},
	{"bIsComplexPropObjective", FieldType.Boolean},
	{"bResetWhenQuestFailed", FieldType.Boolean},
	{"mapId", FieldType.Userdata},
	{"timeLimit", FieldType.Number},
	{"ownerQuestInfo", FieldType.Userdata},
	{"bAcceptedState", FieldType.Boolean},
	{"bIsFinsih", FieldType.Boolean},
	{"num", FieldType.Integer},
	{"bTracking", FieldType.Boolean},
	{"spendTime", FieldType.Number},
	{"bUnLock", FieldType.Boolean},
	{"complexPropRequirment", FieldType.Table},
	{"bIsComplexWeaponObjective", FieldType.Boolean}
)
local UGPBlueprintUtils = import "GPBlueprintUtils"
-- QuestObjectiveType = {
-- UnknownQuestObjectiveType = 0,
-- Submit = 1,
-- Currency = 2,
-- Gather = 3,
-- Escaped = 4,
-- }

-- 记录属于局内 不需要局外弹tip的目标类型
local NtShowUpdateTipObjectType = {
    QuestObjectiveType.Gather, --3
	QuestObjectiveType.Escaped, --4
	QuestObjectiveType.Kill, --5
    QuestObjectiveType.ExploreMapPosition, --8
	QuestObjectiveType.PlaceProp, --9
	QuestObjectiveType.DSGameInteractive, --12
    13,14,16,17,18,19,22,23,24,33,34,36
}
function QuestObjectiveStruct:Ctor(objectiveCfg, ownerQuestInfo)
	self.id = objectiveCfg.ObjectiveID
	-- QuestObjectiveType 定义
	self.type = objectiveCfg.Type
	self.param1 = objectiveCfg.Param1
	self.param2 = objectiveCfg.Param2
	self.listParam = table.tolist(objectiveCfg.ListParam)
	self.numNeeded = objectiveCfg.RequiredCount or 0
	self.desc = objectiveCfg.ObjectiveDesc
	self.bMustComplete = objectiveCfg.ShouldMustbeCompleted
	self.bShowTracking = objectiveCfg.bShowTracking
	self.preObjectiveID = objectiveCfg.PrerequestObjectiveID
	self.bIsComplexPropObjective = objectiveCfg.IsComplexPropObjective
	self.bResetWhenQuestFailed = objectiveCfg.ResetWhenQuestFailed  -- 下局是否需要重置
	-- 任务目标底图id列表
	self.mapId = objectiveCfg.MapID
	-- 限时任务限时
	self.timeLimit = objectiveCfg.TimeLimit
	self.ownerQuestInfo = ownerQuestInfo
	-- 是否接受状态(大世界任务特有)
	self.bAcceptedState = false
	-- 目标是否完成
	self.bIsFinsih = false
	-- 目标进行值
	self.num = 0
	-- 是否是追踪中的目标
	self.bTracking = false
	-- 限时任务耗时
	self.spendTime = 0
	-- 是否解锁
	self.bUnLock = tonumber(self.preObjectiveID) == 0
	-- 复杂物品规则
	self.complexPropRequirment = {}
	-- 配件类改枪 or 数值类改枪
	self.bIsComplexWeaponObjective = objectiveCfg.IsComplexWeaponObjective
end

function QuestObjectiveStruct:HotFixObjective(objectiveCfg, ownerQuestInfo, complexPropObjTable)
	self.type = objectiveCfg.Type
	self.param1 = objectiveCfg.Param1 ~= "" and objectiveCfg.Param1 or nil
	self.param2 = objectiveCfg.Param2
	self.listParam = table.tolist(objectiveCfg.ListParam)
	self.numNeeded = objectiveCfg.RequiredCount or 0
	self.desc = objectiveCfg.ObjectiveDesc
	self.bMustComplete = objectiveCfg.ShouldMustbeCompleted
	self.bShowTracking = objectiveCfg.bShowTracking
	self.preObjectiveID = objectiveCfg.PrerequestObjectiveID
	self.bIsComplexPropObjective = objectiveCfg.IsComplexPropObjective
	self.bResetWhenQuestFailed = objectiveCfg.ResetWhenQuestFailed  -- 下局是否需要重置
	self.mapId = objectiveCfg.MapID
	self.timeLimit = objectiveCfg.TimeLimit
	self.ownerQuestInfo = ownerQuestInfo
	self.bUnLock = tonumber(self.preObjectiveID) == 0
	if complexPropObjTable then
		self:AddComplexPropObjective(complexPropObjTable)
	else
		self.complexPropRequirment = {}
	end
end

function QuestObjectiveStruct:GetQuestObjectDesc()
	local _, objDesc = UGPBlueprintUtils.CovertSpecialString(tostring((self.desc)))
	return objDesc
end

function QuestObjectiveStruct:UpdateObjective(objectiveData, canNotify)
	canNotify = setdefault(canNotify, false)
	self.bAcceptedState = true
	local has_Delta = false
	if self.num ~= objectiveData.value then
		self.num = objectiveData.value
		has_Delta = true
	end
	if self.spendTime ~= objectiveData.spent_seconds then
		self.spendTime = objectiveData.spent_seconds
		has_Delta = true
	end
	if self.bIsFinsih ~= objectiveData.has_completed then
		self.bIsFinsih = objectiveData.has_completed
		has_Delta = true
	end
	self:UpdateTrack(objectiveData.has_marked)
	if has_Delta and canNotify then
		Server.QuestServer.Events.evtUpdateObjectiveState:Invoke(self, self.ownerQuestInfo)
	end
end

function QuestObjectiveStruct:UpdateTrack(bTracking)
	self.bTracking = bTracking
end

function QuestObjectiveStruct:ResetObjective()
	self.bAcceptedState = false
	self.num = 0
	self.bIsFinsih = false
	Server.QuestServer.Events.evtUpdateObjectiveState:Invoke(self, self.ownerQuestInfo)
end

-- itemId: number
-- bindType: PropBindingType
function QuestObjectiveStruct:IsObjectiveItem(itemId, bindType)
	local targetBindType = tonumber(self.param2) or -1
	if bindType and targetBindType < PropBindingType.BindingBinded or bindType == targetBindType then
		-- 提交道具&收集道具&局内拾取道具&局内放置类
		if self.type == QuestObjectiveType.Submit or self.type == QuestObjectiveType.Gather 
			or self.type == QuestObjectiveType.DSPickUpProp or self.type == QuestObjectiveType.PlaceProp then
			return self.param1 == tostring(itemId)
		-- 撤离带出道具&局内使用
		elseif self.type == QuestObjectiveType.CarryOutProps or self.type == QuestObjectiveType.DSUserProp then
			for _, idInt in ipairs(self.listParam) do
				if idInt == itemId then
					return true
				end
			end
		-- 商人处购买指定物品
		-- else
		end
	end
	return false
end

function QuestObjectiveStruct:AddComplexPropObjective(complexPropObjTable)
	self.complexPropRequirment = {
		['itemType'] = tonumber(complexPropObjTable.ItemType),
		['itemQuality'] = tonumber(complexPropObjTable.ItemQuality),
		['bIsMoreThanQuality'] = complexPropObjTable.IsMoreThanQuality,
		['itemPrice'] = tonumber(complexPropObjTable.ItemPrice),
		['bIsMoreThanPrice'] = complexPropObjTable.IsMoreThanPrice,
		['objectiveIcon'] = complexPropObjTable.ObjectiveIcon,
		['complexPropName'] = complexPropObjTable.RuleName,
	}
end

function QuestObjectiveStruct:IsNeedShowUpdateTip()
	if table.isInList(self.type, NtShowUpdateTipObjectType) then
		return false
	end
	return true
end

return QuestObjectiveStruct