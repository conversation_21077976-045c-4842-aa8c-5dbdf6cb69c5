----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHUDToolBar)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local HUDToolBarLogic = require "DFM.Business.Module.HUDToolBarModule.Logic.HUDToolBarLogic"
local HUDToolBarConfig = require "DFM.Business.Module.HUDToolBarModule.HUDToolBarConfig"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local LuaUIHUDBaseView = require "DFM.YxFramework.Managers.UI.Layer.HUD.LuaUIHUDBaseView"

local GameHUDState = import "GameHUDSate"
local UWidgetLayoutLibrary = import "WidgetLayoutLibrary"
local UKismetInputLibrary = import "KismetInputLibrary"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local UDFMGameplayDelegates = import "DFMGameplayDelegates"
local UWidgetBlueprintLibrary = import "WidgetBlueprintLibrary"
local UGPWidgetUtil = import "GPWidgetUtil"
local UKismetMathLibrary = import "KismetMathLibrary"
local UHUDStateManager = import "HUDStateManager"

---@class ControllerRouleteeBase : LuaUIHUDBaseView
local ControllerRouleteeBase = hud("ControllerRouleteeMed")

ControllerRouleteeBase.CLICK_AREA_TOLERANCE = 5	-- in px

local function log(...)
	loginfo("[ControllerRouleteeBase]", ...)
end



function ControllerRouleteeBase:Ctor()
	self.rouletteType = HUDToolBarConfig.ERouletteType.None

	self._bHasInitGeometry = false
	self._factor = 0
	self._uiRadius = 0
	self._innerRadius = 0
	self._outerRadius = 0

	self._originalCenter = FVector2D.ZeroVector
	self._dynamicCenter = FVector2D.ZeroVector
	self._rootGeometry = nil
	self._bInDragState = false
	self._startAbsPos = FVector2D.ZeroVector
	self._longPressTimer = nil

	self._wtRootCanvas = self:Wnd("wtRootCanvas", UIWidgetBase)
	self._wtMovableRoot = self:Wnd("wtMovableRoot", UIWidgetBase)

	if DFHD_LUA ~= 1 then
		self._wtArrowBg = self:Wnd("wtArrowBg", UIImage)
		self._wtActiveState = self:Wnd("wtActiveState",UIWidgetBase)
		self._wtCircleBg = self:Wnd("wtCircleBg", UIImage)
		self._wtMoveIcon = self:Wnd("wtMoveIcon", UIWidgetBase)
		self._wtMoveIcon:Collapsed()
	end

	self._wtItemInfoPanel = self:Wnd("wtItemInfoPanel", UIWidgetBase)
	--self._wtItemIcon = self:Wnd("wtItemIcon", UIWidgetBase):Wnd("ItemIcon", UIImage)
	self._wtItemIcon = self:Wnd("ItemIcon", UIImage)
	if DFHD_LUA ~= 1 then
		-- self._wtHealthIcon = self:Wnd("wtHealthIcon", UIImage)
		-- self._wtHealthIcon:Collapsed()
	end

	self._wtCurNum = self:Wnd("wtCurNum", UITextBlock)
	self._wtMaxNum = self:Wnd("wtMaxNum", UITextBlock)

	self:_InitHUDState()

	self.bCanClickRouletee = true

    if DFHD_LUA == 1 then
		self._innerRadius = 50
		self._outerRadius = 300
		self.ScreenCenter = FVector2D.ZeroVector
        self.ViewportPoint = FVector2D.ZeroVector
        self.ViewportScale = 1
        self.localCtrl = UGameplayStatics.GetPlayerController(GetGameInstance(), 0)
        self.world = GetWorld()
		self.cursorOffset = FVector2D.ZeroVector
        self.NormalizedCursorPosition = FVector2D.ZeroVector
		self._wtNoItemIcon = self:Wnd("NoItemIcon", UIImage)
	end

	self.bEnableTick = true

	
end

function ControllerRouleteeBase:OnSOLSettlementFirstUIShow()
	print("ControllerRouleteeBase:OnSOLSettlementFirstUIShow()")
	self.bEnableTick = false
	HUDToolBarLogic.HideRoulettePanel()
end

function ControllerRouleteeBase:OnClientSelfDied()
	print("ControllerRouleteeBase:OnClientSelfDied()")
	self.bEnableTick = false
	HUDToolBarLogic.HideRoulettePanel()
end

function ControllerRouleteeBase:OnClientSelfReborn()
	print("ControllerRouleteeBase:OnClientSelfReborn()")
	self.bEnableTick = true
	HUDToolBarLogic.HideRoulettePanel()
end

function ControllerRouleteeBase:GetInjectionList()
	local list = LuaUIHUDBaseView.GetInjectionList(self)
	table.append(list, {
		"OnTouchStarted",
		"OnTouchMoved",
		"OnTouchEnded",
		"OnStartLocalFocus",
	})
	return list
end

function ControllerRouleteeBase:OnNativePaint(context)
	if self._bInDragState then
		UGPWidgetUtil.DrawCircle(context, self._dynamicCenter, FVector2D(1, 1), ColorUtil.GetLinearColorByHex("FFFFFFFF"), self._innerRadius, 12, false, 1)
		UGPWidgetUtil.DrawCircle(context, self._dynamicCenter, FVector2D(1, 1), ColorUtil.GetLinearColorByHex("FF0000FF"), self._outerRadius, 12, false, 1)
	end
end


--==================================================
--region Life function
function ControllerRouleteeBase:OnOpen()
	self:_ToggleDragState(false)

    self._callbackHandler = UGPGameplayDelegates.Get(GetWorld()).OnSkillApplyAssaultEffect:Add(CreateCPlusCallBack(self._OnSkillApplyAssaultEffect, self))

	self:_InitDelegate()
	print("ControllerRouleteeBase:OnOpen()")
end
function ControllerRouleteeBase:OnClose()
	if self._callbackHandler then
		UGPGameplayDelegates.Get(GetWorld()).OnSkillApplyAssaultEffect:Remove(self._callbackHandler)
		self._callbackHandler = nil
	end

	self:_RemoveDelegate()
	print("ControllerRouleteeBase:OnClose()")
end
function ControllerRouleteeBase:OnShow()
	self:_InitLuaEvents()

	self:_InternalRefresh()


end
function ControllerRouleteeBase:OnHide()
	self:RemoveAllLuaEvent()

	self:_ReleaseLongPressTimer()


end

function ControllerRouleteeBase:Destroy()
	print("ControllerRouleteeBase:Destroy()")
	
end

function ControllerRouleteeBase:_OnSkillApplyAssaultEffect(apply, duration, IsSelf)
	if apply and IsSelf then
		self.bCanClickRouletee = false
		Timer.DelayCall(duration,function()
			self.bCanClickRouletee = true
		end)
	end
end

function ControllerRouleteeBase:OnInitExtraData()

end

ControllerRouleteeBase.TouchIndex = -1

function ControllerRouleteeBase:Imp_OnTouchStarted(inGeometry, inGestureEvent)
	log("OnNativeTouchStarted")
	if ControllerRouleteeBase.TouchIndex == -1 then
		ControllerRouleteeBase.TouchIndex = UKismetInputLibrary.PointerEvent_GetPointerIndex(inGestureEvent)
	end 
	
	--local playerInput = self:_GetPlayerInput()
	--playerInput:RegisterInterestedTouchIndex(ControllerRouleteeBase.TouchIndex)

	local reply = UWidgetBlueprintLibrary.Handled()
	if not self:_CheckShouldTriggerRoulette() then
		return reply
	end

	self:_PrepareGeometryData()

	self:_StartLongPressTimer()

	local absolutePoint = UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(inGestureEvent)
	self._startAbsPos = self._rootGeometry:AbsoluteToLocal(absolutePoint)

	self:SetCppValue("bUseLuaPaint", Module.HUDToolBar.Field:GetShowRouletteDebugInfo())
	reply = UWidgetBlueprintLibrary.CaptureMouse(reply, self)
	return reply
end

function ControllerRouleteeBase:Imp_OnTouchMoved(inGeometry, inGestureEvent)
	log("OnNativeOnTouchMoved")

	local reply = UWidgetBlueprintLibrary.Handled()
	local touchIndex = UKismetInputLibrary.PointerEvent_GetPointerIndex(inGestureEvent)
	
	-- Check should trigger ruolette first
	if not self:_CheckShouldTriggerRoulette() then
		return reply
	end

	if touchIndex ~= ControllerRouleteeBase.TouchIndex then
		return reply
	end
	if not self._rootGeometry then
		return UWidgetBlueprintLibrary.Unhandled()
	end

	
	-- local absolutePoint = UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(inGestureEvent)
	local localPos = self:_GetSmoothedTouchPosition(ControllerRouleteeBase.TouchIndex,inGestureEvent)
	if self._bInDragState then
		-- local localPos = self._rootGeometry:AbsoluteToLocal(absolutePoint)
		local diffVector = localPos - self._dynamicCenter
		local distance = diffVector:Size()
		local tolerance = 0.000001
		local dirVector = diffVector:GetSafeNormal(tolerance)

		-- Here factor is from last frame
		-- We use last frame factor to get a radius
		local radius = self._factor >= 1 and self._innerRadius or self._outerRadius
		self._factor = distance / radius

		-- factor change may cause radius chanage
		-- So we calculate these two values again
		radius = self._factor >= 1 and self._innerRadius or self._outerRadius
		self._factor = distance / radius

		-- local targetMoveBtnPos = dirVector * math.min(distance, radius)
		-- local movableBtnSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtMoveIcon)
		-- movableBtnSlot:SetPosition(targetMoveBtnPos)
		if DFHD_LUA ~= 1 then 
			-- 小圆点
			local targetMoveBtnPos = dirVector * math.min(distance, radius)
			local movableBtnSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtMoveIcon)
			movableBtnSlot:SetPosition(targetMoveBtnPos)
			-- 白色高亮渐变块
			local moveAngle = self:CalculateAngle(FVector2D(0, 1), FVector2D(dirVector.X, -dirVector.Y))
			if distance < tolerance then
				moveAngle = 0
			end
			self._wtActiveState:SetRenderTransformAngle(moveAngle - 45)
		end

		-- Check whether we should update dynamic center position
		local offset = distance - self._outerRadius
		if offset > 0 then
			self._dynamicCenter = self._dynamicCenter + dirVector * offset
		end

		dirVector.Y = -dirVector.Y
		HUDToolBarLogic.NotifyPalettePanel(dirVector, self._factor)


	else
		-- check should go into drag state
		if (localPos - self._startAbsPos):Size() < ControllerRouleteeBase.CLICK_AREA_TOLERANCE then
			-- nothing
		else
			self:_ReleaseLongPressTimer()

			-- local localPos = self._rootGeometry:AbsoluteToLocal(absolutePoint)

			local distance = (localPos - self._dynamicCenter):Size()
			if distance < self._uiRadius then
				self:_ToggleDragState(true)
			end
		end
	end

	reply = UWidgetBlueprintLibrary.CaptureMouse(reply, self)

	return reply
end

function ControllerRouleteeBase:Imp_OnTouchEnded(inGeometry, inGestureEvent)
	log("OnNativeTouchEnded")
	
	ControllerRouleteeBase.TouchIndex_End = UKismetInputLibrary.PointerEvent_GetPointerIndex(inGestureEvent)
	
	local shouldHandleSelect = true
	if ControllerRouleteeBase.TouchIndex ~= ControllerRouleteeBase.TouchIndex_End then
		shouldHandleSelect = false
	end
	
	local localPos = self:_GetSmoothedTouchPosition(ControllerRouleteeBase.TouchIndex,inGestureEvent)
	
	--local playerInput = self:_GetPlayerInput()
	--playerInput:UnRegisterInterestedTouchIndex(ControllerRouleteeBase.TouchIndex)
	
	ControllerRouleteeBase.TouchIndex = -1

	-- Check should trigger ruolette first
	if not self:_CheckShouldTriggerRoulette() then
		if shouldHandleSelect then
			self:_HandleClickLogic()
		end
		return
	end

	if not self._rootGeometry then
		return UWidgetBlueprintLibrary.Unhandled()
	end

	self:_ReleaseLongPressTimer()

	if self._bInDragState then
		self:_ToggleDragState(false)

		-- local movableBtnSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtMoveIcon)
		-- movableBtnSlot:SetPosition(FVector2D.ZeroVector)
		if DFHD_LUA ~= 1 then
			local movableBtnSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(self._wtMoveIcon)
			movableBtnSlot:SetPosition(FVector2D.ZeroVector)
		end

		if shouldHandleSelect then
			HUDToolBarLogic.NotifyPalettePanelSearchDone()
		end
	else
		if shouldHandleSelect then
			local distance = (localPos - self._originalCenter):Size()
			if distance < self._uiRadius then
				self:_HandleClickLogic()
			end
		end
	end
end

--endregion
--==================================================

-----------------------------------------------------------------------
--region Should override

function ControllerRouleteeBase:_GetRootBtn()
	return nil
end

--endregion
-----------------------------------------------------------------------

function ControllerRouleteeBase:_GetRootBtn()
	return self._wtRootBtn
end

--endregion
-----------------------------------------------------------------------

--==================================================
--region Public API
--endregion
--==================================================


--==================================================
--region Private API

function ControllerRouleteeBase:_InitHUDState()
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Operating3DUI);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Settlement);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_PrepareTime);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_RescueSomeOne);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Burden);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_DriveVehicle);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_RideVehicle);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_CutScene);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_UseHeavyMachineGun);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Reloading);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_ForbidFire);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_UseTelescope);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Monitor);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_PVEQuestPanelOnly);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_SOLQuestOperation);
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Dying)
	--self:AddStateToInVisibleGameHudState(GameHUDState.GHS_Dead)	
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_OpenMap)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_RedeployView)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_BattleScoreDetails)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_SafeHouseRange)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_DyingView)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_ObserverMode)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_EscPanel)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_GlobalHideAllHUD)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_BeingRescue)
	self:AddStateToInVisibleGameHudState(GameHUDState.GHS_SOLKillerInfoCardView)
	if DFHD_LUA ~= 1 then
		self:AddStateToInVisibleGameHudState(GameHUDState.GHS_LiveSpectate) 
		self:AddStateToInVisibleGameHudState(GameHUDState.GHS_ObserverMode)
	end
	
	if DFHD_LUA == 1 then
		self:AddStateToInVisibleGameHudState(GameHUDState.GHS_WeaponInspect)
		--self:AddStateToInVisibleGameHudState(GameHUDState.GHS_SkillInspect)
	end

	--if DFHD_LUA == 1 then
		--self:AddStatesToAlwaysShowGameHudState({GameHUDState.GHS_LiveSpectate})
	--end
end

function ControllerRouleteeBase:_PrepareGeometryData()
	-- if not self._bHasInitGeometry then
		local rootBtn = self:_GetRootBtn()

		if not rootBtn then
			logerror("ControllerRouleteeBase rootBtn == nil")
		end

		if rootBtn then
			self._bHasInitGeometry = true

			local slot = UWidgetLayoutLibrary.SlotAsCanvasSlot(rootBtn)
			local size = slot:GetSize()
			self._uiRadius = (size.X + size.Y) / 4
	
			local geometry = rootBtn:GetCachedGeometry()
			local midAbsPos = geometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.CENTER_CENTER_VECTOR)
	
			local rootGeometry = self._wtRootCanvas:GetCachedGeometry()
			if not rootGeometry then
				logerror("ControllerRouleteeBase rootGeometry == nil")
			end
			
			self._rootGeometry = rootGeometry
			self._originalCenter = rootGeometry:AbsoluteToLocal(midAbsPos)
		end
	-- end

	self._innerRadius = Module.HUDToolBar.Field:GetRouletteInnerRadius()
	self._outerRadius = Module.HUDToolBar.Field:GetRouletteOuterRadius()
	self._factor = 0

	self._startAbsPos = FVector2D.ZeroVector
	self._dynamicCenter = self._originalCenter
end

function ControllerRouleteeBase:_InitLuaEvents()
	
end

function ControllerRouleteeBase:_InitDelegate()
	if UDFMGameplayDelegates ~= nil then
		self._fOnSOLSettlementFirstUIShow = CreateCallBack(self.OnSOLSettlementFirstUIShow, self)
		self._OnSOLSettlementFirstUIShowHandler = UDFMGameplayDelegates.Get(GetWorld()).OnSOLSettlementFirstUIShow:Add(self._fOnSOLSettlementFirstUIShow)
	end
	if UGPGameplayDelegates ~= nil then
		self._fOnClientSelfDied = CreateCallBack(self.OnClientSelfDied, self)
		self._OnClientSelfDiedHandler = UGPGameplayDelegates.Get(GetWorld()).OnClientSelfDied:Add(self._fOnClientSelfDied)
	end
	if UGPGameplayDelegates ~= nil then
		self._fOnClientSelfReborn = CreateCallBack(self.OnClientSelfReborn, self)
		self._OnClientSelfRebornHandler = UGPGameplayDelegates.Get(GetWorld()).OnClientSelfReborn:Add(self._fOnClientSelfReborn)
	end
	if UHUDStateManager ~= nil then
		if self.OnHudStateAddedHandler == nil then
			local fOnHudStateChanged = CreateCallBack(self.OnHudStateAdded, self)
			self.OnHudStateAddedHandler = UHUDStateManager.Get(self).OnGameHUDStateAdded:Add(fOnHudStateChanged)
		end
		if self.OnHudStateRemovedHandler == nil then
			local fOnHudStateChanged = CreateCallBack(self.OnHudStateRemoved, self)
			self.OnHudStateRemovedHandler = UHUDStateManager.Get(self).OnGameHUDStateRemoved:Add(fOnHudStateChanged)
		end
	end
end

function ControllerRouleteeBase:_RemoveDelegate()
	if isvalid(self._OnSOLSettlementFirstUIShowHandler) then
		UDFMGameplayDelegates.Get(GetWorld()).OnSOLSettlementFirstUIShow:Remove(self._OnSOLSettlementFirstUIShowHandler)
		self._OnSOLSettlementFirstUIShowHandler = nil
	end
	if isvalid(self._OnClientSelfDiedHandler) then
		UGPGameplayDelegates.Get(GetWorld()).OnClientSelfDied:Remove(self._OnClientSelfDiedHandler)
		self._OnClientSelfDiedHandler = nil
	end
	if isvalid(self._OnClientSelfRebornHandler) then
		UGPGameplayDelegates.Get(GetWorld()).OnClientSelfReborn:Remove(self._OnClientSelfRebornHandler)
		self._OnClientSelfRebornHandler = nil
	end
	if UHUDStateManager ~= nil then
		if self.OnHudStateAddedHandler ~= nil then
			UHUDStateManager.Get(GetWorld()).OnGameHUDStateAdded:Remove(self.OnHudStateAddedHandler)
			self.OnHudStateAddedHandler = nil
		end
		if self.OnHudStateRemovedHandler ~= nil then
			UHUDStateManager.Get(GetWorld()).OnGameHUDStateRemoved:Remove(self.OnHudStateRemovedHandler)
			self.OnHudStateRemovedHandler = nil
		end
	end
end
function ControllerRouleteeBase:_InternalRefresh()
	if not self:_CheckShouldTriggerRoulette() then
		if DFHD_LUA ~= 1 then
			self._wtArrowBg:Collapsed()
		end
	end
end

function ControllerRouleteeBase:_RefreshEmpty()
	if DFHD_LUA == 1 then
		-- 加载空状态
		self._wtNoItemIcon:SelfHitTestInvisible()
	end
	self._wtItemIcon:Collapsed()
	log("ControllerRouleteeMed _wtItemIcon:Collapsed()")
	self._wtItemInfoPanel:Collapsed()
end

function ControllerRouleteeBase:_ToggleDragState(bInDragState)
	self._bInDragState = bInDragState

	if bInDragState then
		self._wtRootCanvas:Visible()

		if DFHD_LUA ~= 1 then
			self._wtArrowBg:Collapsed()
			if self._wtActiveState then
				self._wtActiveState:SelfHitTestInvisible()
				self._wtMoveIcon:SelfHitTestInvisible()
			end
		end
		-- self._wtItemIcon:Collapsed()
		-- self._wtMoveIcon:SelfHitTestInvisible()
		-- self._wtMoveIcon:Collapsed()	-- 先不显示
		-- self._wtItemInfoPanel:Collapsed()

		HUDToolBarLogic.ShowRoulettePanel(self.rouletteType)
	else
		self._wtRootCanvas:SelfHitTestInvisible()

		if DFHD_LUA ~= 1 then
			self._wtArrowBg:SelfHitTestInvisible()
			if self._wtActiveState then
				self._wtActiveState:Collapsed()
				self._wtMoveIcon:Collapsed()
			end
		end
		-- self._wtItemIcon:SelfHitTestInvisible()
		--log("ControllerRouleteeBase _wtItemIcon:Collapsed()")
		-- self._wtMoveIcon:Collapsed()
		-- self._wtItemInfoPanel:SelfHitTestInvisible()

		-- self:_InternalRefresh()	-- Refresh again

		HUDToolBarLogic.HideRoulettePanel()
	end

	self:_ReleaseLongPressTimer()
end

function ControllerRouleteeBase:_HandleClickLogic()
	
end

function ControllerRouleteeBase:_CheckShouldTriggerRoulette()
	return not InGameController:Get():IsMPMode() and self.bCanClickRouletee
end

function ControllerRouleteeBase:_StartLongPressTimer()
	self:_ReleaseLongPressTimer()

	local longPressDuration = Module.HUDToolBar.Field:GetRouletteLongPressDuration()
	self._longPressTimer = Timer.DelayCall(longPressDuration, self._OnLongPressed, self)
end

function ControllerRouleteeBase:_OnLongPressed()
	self:_ToggleDragState(true)
end

function ControllerRouleteeBase:_ReleaseLongPressTimer()
    Timer.CancelDelay(self._longPressTimer)
    self._longPressTimer = nil
end

--function ControllerRouleteeBase:_GetPlayerInput()
--	local pc = InGameController:Get():GetGPPlayerController()
--	if pc then
--		return pc.PlayerInput
--	end
--	return nil
--end

function ControllerRouleteeBase:_GetSmoothedTouchPosition(touchIndex,pointerEvent)
	if not self._rootGeometry then
		return FVector2D.ZeroVector
	end
	local viewportGeometry = UWidgetLayoutLibrary.GetViewportWidgetGeometry(GetWorld())

	--local playerInput = self:_GetPlayerInput()
	--local pos = playerInput:GetSmoothedTouchPosition(touchIndex)
	local pos = UKismetInputLibrary.PointerEvent_GetScreenSpacePosition(pointerEvent)
	local viewportScale = UWidgetLayoutLibrary.GetViewportScale(GetWorld())

	-- local ret = pos / viewportScale
	local ret = pos
	ret = viewportGeometry:LocalToAbsolute(LuaGlobalConst.TOP_LEFT_VECTOR) + ret

	return self._rootGeometry:AbsoluteToLocal(ret)
	-- return pos / viewportScale
end

function ControllerRouleteeBase:CalculateAngle(InVectorA, InVectorB)
	-- 计算向量的长度
	MagnitudeA = InVectorA:Size()
	MagnitudeB = InVectorB:Size()

	-- 计算向量的点积
	DotProduct = FVector2D.DotProduct(InVectorA, InVectorB)

	-- 计算夹角的余弦值
	CosineAngle = DotProduct / (MagnitudeA * MagnitudeB)

	-- 计算夹角的弧度值及角度
	--Radians = FMath.Acos(CosineAngle)
	--Angle = FMath.RadiansToDegrees(Radians)
	Radians = UKismetMathLibrary.Acos(CosineAngle)
	Angle = UKismetMathLibrary.RadiansToDegrees(Radians)

	-- 计算向量的叉积
	CrossProduct = FVector2D.CrossProduct(InVectorA, InVectorB)

	-- 判断夹角的方向
	if (CrossProduct > 0) then
		return 360.0 - Angle
	else
		return Angle
	end
	return 0.0
end

function ControllerRouleteeBase:OnHudStateAdded(State)
	--- BEGIN MODIFICATION @ VIRTUOS
	if table.contains(self.InVisibleGameHudState, State) then
	--- END MODIFICATION
		ControllerRouleteeBase.TouchIndex = -1
		self:_ReleaseLongPressTimer()
		if self._bInDragState then
			self:_ToggleDragState(false)
		end
	end

	if DFHD_LUA == 1 then
		self:CheckHDRouletteCanTick()
	end
end

function ControllerRouleteeBase:OnHudStateRemoved(State)
	if DFHD_LUA == 1 then
		self:CheckHDRouletteCanTick()
	end
end

function ControllerRouleteeBase:CheckHDRouletteCanTick()
	if DFHD_LUA == 1 then
		if UHUDStateManager ~= nil then
			local hudStateManager = UHUDStateManager.Get(self)
			if isvalid(hudStateManager) then
				for i, value in pairs(self.InVisibleGameHudState) do
					if hudStateManager:HasState(value) then
						self.bEnableTick = false
						return
					end
				end
			end
		end
		self.bEnableTick = true
	end
end

--endregion
--==================================================


return ControllerRouleteeBase