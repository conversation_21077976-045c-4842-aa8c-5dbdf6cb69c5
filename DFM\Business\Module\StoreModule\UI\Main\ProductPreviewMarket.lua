----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"


---@class ProductPreviewMarket : LuaUIBaseView
local ProductPreviewMarket = ui("ProductPreviewMarket")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local UGPGameViewportClient = import "GPGameViewportClient"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local LiteCommonDownload = require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"

function ProductPreviewMarket:Ctor()
    self.curSelectIndex = 1
    self.goodsItems = {}
    self.lastLoadModelID = 0
    self._wtWaterFallList = UIUtil.WndWaterfallScrollBox(self, "wtWaterfallView", self._OnGetItemCount,
        self._OnProcessItemWidget)

    self._wtTextBundleName = self:Wnd("wtBundleName", UITextBlock)
    self._wtTextBundleName:Collapsed()
    self._wtCountWidget = self:Wnd("wtStoreCountDown", UIWidgetBase)
    if self._wtCountWidget ~= nil then
        self._wtTextCountDown = self._wtCountWidget:Wnd("wtTextCountDown", UITextBlock)
    end

    self._wtTextWeaponName = self:Wnd("wtTextWeaponName", UITextBlock)
    self._wtTextWeaponDesc = self:Wnd("wtWeaponDesc", UITextBlock)
    self._wtTextPrice = self:Wnd("wtRichTextPrice", UITextBlock)
    self._wtTextContainTip = self:Wnd("wtTextContainTip", UITextBlock)
    self._wtTextGoodsTip = self:Wnd("wtTextGoodsTip", UITextBlock)

    self._wtButtonBuyBundle = self:Wnd("wtButtonBuyBundle", DFCommonButtonOnly)
    self._wtButtonBuyBundle:Event("OnClicked", self._OnButtonBuyBundleClick, self)

    self._wtButtonPreview = self:Wnd("wtButtonPreview", UIButton)
    self._wtButtonPreview:Event("OnClicked", self._OnButtonPreviewClick, self)


    self._wtButtonBuySelect = self:Wnd("wtButtonBuySelect", DFCommonButtonOnly)
    -- self._wtButtonBuySelect:Event("OnClicked", self._OnButtonBuySelectOneClick, self)
    self._wtButtonBuySelect:BP_SetMainTitle(StoreConfig.Loc.RecommendBundleBuySelectOne)

    self._wtButtonItemPreview = self:Wnd("wtButtonItemPreview", DFCommonButtonOnly)
    self._wtButtonItemPreview:Event("OnClicked", self._OnButtonRoleSkinPreviewClick, self)
    self._wtButtonItemPreview:BP_SetMainTitle(StoreConfig.Loc.RecommendBundleItemPreview)

    self._wtDetailCheckBtn = self:Wnd("wtDetailCheckBtn", DFCheckBoxOnly)

    self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_257", self._OnHovered, self._OnUnhovered)
    if DFHD_LUA == 1 then
        self._wtDetailCheckBtn:Event("OnCheckStateChanged", self._OnCheckStateChanged, self)
    end

    if DFHD_LUA == 1 then
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.DefaultSecondary)
    else
        Module.CommonBar:RegStackUITopBarCurrencyTypeList(self,
            { ECurrencyClientId.BindDiamond, ECurrencyClientId.UnbindDiamond })
        Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)
    end

    self._wtDiscountRoot = self:Wnd("wtDiscountRoot", UIWidgetBase)
    self._wtTextDiscount = self:Wnd("wtTextDiscount", UITextBlock)

    self.wtImageIcon = self:Wnd("DFImage_191", UIImage)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)

    --download
    self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)
    self._wtNameIcon = self:Wnd("wt_NameIcon", UIImage)

    self._wtImageRoot = self:Wnd("DFScaleBox_0", UIWidgetBase)
    self._wtImageSparyRoot = self:Wnd("DFScaleBox_Spray", UIWidgetBase)
    self.wtImageSpray = self:Wnd("DFImage_Spray", UIImage)


    self._wtButtonBuySelect:Collapsed()
    self._wtTextGoodsTip:Collapsed()
    self._wtCountWidget:Collapsed()
    self._wtTextPrice:Collapsed()
    self._wtTextContainTip:Collapsed()

    if self._wtImageRoot ~= nil then
        self._wtImageRoot:Collapsed()
    end

    if self._wtImageSparyRoot ~= nil then
        self._wtImageSparyRoot:Collapsed()
    end
    
    if IsHD() then
        self._RotationVec = FVector2D(0, 0)
        self._WeaponRotationZoom = 600
        self._WeaponScaleZoom = 30
    end
end

function ProductPreviewMarket:_OnGetItemCount()
    loginfo("[RecommendHomepage] _OnGetItemCount #self.goodsItems:" ..
        #self.goodsItems)
    return #self.goodsItems or 0
end

function ProductPreviewMarket:_OnProcessItemWidget(position, itemWidget)
    local curdata = self.goodsItems[position]
    if curdata ~= nil then
        itemWidget:Visible()
        local bIsOwned = nil
        if curdata.price > 0 then
            local collectionItemInfo = Server.CollectionServer:GetWeaponSkinIfOwned(curdata.id)
            if collectionItemInfo ~= nil and collectionItemInfo.num > 0 then
                bIsOwned = true
            end

            if curdata.curWeight == 0 then
                bIsOwned = true
            end
        end
        itemWidget:SetItemInfo(position, curdata, 1, nil, false, bIsOwned)
    else
        itemWidget:Collapsed()
    end

    loginfo("[ProductPreviewMarket] _OnProcessItemWidget posIdx:" .. position)
end

function ProductPreviewMarket:OnCloseBtnClick()

end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function ProductPreviewMarket:OnInitExtraData(shopData, isLuckyNest)
    self.shopData = shopData
    self.isLuckyNest = isLuckyNest
    if self.shopData ~= nil then
        self.goodsItems = self.shopData.GoodsItems

        if self._wtNameIcon ~= nil then
            local nameIconPath = self.shopData.ImageSourceLogo
            if nameIconPath ~= nil and nameIconPath ~= "" then
                self._wtNameIcon:Visible()
                self._wtNameIcon:AsyncSetImagePath(nameIconPath, true)
            end
        end
    end

    Module.CommonBar:RegStackUITopBarTitle(self, StoreConfig.Loc.StoreMainTabWeaponTimeLimitMarket)
end

function ProductPreviewMarket:OnShowBegin()
    self:RegisterEventListeners()

    self._wtWaterFallList:RefreshAllItems()
    if self.shopData ~= nil then
        -- self._wtTextBundleName:SetText(self.shopData.ShopName)
    end

    self:_RefreshShowInfo()

    if IsHD() then
        self:_EnableGamepadFeature()
    end
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function ProductPreviewMarket:OnShow()
    local curdata = self.goodsItems[1]
    if curdata ~= nil then
        self:RefreshStoreModelInfo(curdata.id)
        self:ReportViewDetailEvent(curdata.id, 0, 0, 0, 0)
    end
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function ProductPreviewMarket:OnHide()
    self.lastLoadModelID = 0
    self:UnRegisterEventListeners()
    self:ClearTips()
    Facade.HallSceneManager:ResetRootActorOffset() --重新设置rootActor的偏移
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetWheelInteract", true)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
end

function ProductPreviewMarket:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function ProductPreviewMarket:_RefreshShowInfo()
    local curdata = self.goodsItems[1]
    local itemData = ItemBase:New(curdata.id)
    
    local newData = Server.StoreServer:GetSpecialBackDataByID(curdata.id)
    if newData then
        self.bIsSellOut = newData.CurWeight == 0
    else
        self.bIsSellOut = false
    end

    if self.isLuckyNest then
        local buyRecord = Server.StoreServer:GetLuckyNestBoughtGoods()
        if buyRecord and buyRecord[curdata.id] then
            self.bIsSellOut = true
        else
            self.bIsSellOut = false
        end
    end

    local price = self.shopData.Price
    local priceOrign = self.shopData.PricePreDis
    local currencyItemId = self.shopData.CurrencyType

    if itemData ~= nil then
        if self._wtTextWeaponName ~= nil then
            self._wtTextWeaponName:SetText(itemData.name)
        end

        if self._wtQualityIcon ~= nil then
            self._wtQualityIcon:AsyncSetImagePath(Module.Collection.Config.QualityIconMapping[itemData.quality])
            self._wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(itemData.quality))
            self._wtQualityIcon:SelfHitTestInvisible()
        end

        Facade.HallSceneManager:SetDisplayBackground(curdata.id, true)

        if self._wtTextWeaponDesc ~= nil then
            local outStr = itemData.description
            local itemMainType = ItemHelperTool.GetMainTypeById(curdata.id)
            if itemMainType == EItemType.WeaponSkin then
                if itemData.itemSubType == ItemConfig.EWeaponItemType.Melee then
                    local skinsInfo = Facade.TableManager:GetTable("WeaponSkin/MeleeWeaponSkinDataTable")
                    local skinInfo = skinsInfo[curdata.id]
                    if skinInfo then
                        outStr = skinInfo.SkinDescription
                    end
                else
                    local skinsInfo = Facade.TableManager:GetTable("WeaponSkin/WeaponSkinDataTable")
                    local skinInfo = skinsInfo[curdata.id]
                    if skinInfo then
                        outStr = skinInfo.SkinDescription
                    end
                end
            end

            self._wtTextWeaponDesc:SetText(outStr)
        end

        if self._wtButtonBuyBundle ~= nil then

            if self.bIsSellOut then
                self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                self._wtButtonBuyBundle:SetMainTitle(StoreConfig.Loc.RecommendBundleAllItemBuyButtonTip)
                self._wtButtonBuyBundle:SetIsEnabled(false)
            else
                self._wtButtonBuyBundle:SetIsEnabled(true)
                if priceOrign > price then
                    self._wtButtonBuyBundle:SetV1S3ButtonStyle(1)
                    local imgPath = Module.Currency:GetImgPathByItemId(currencyItemId)
                    self._wtButtonBuyBundle:SetV1S3TextIcon(imgPath)
                    self._wtButtonBuyBundle:SetMainTitle("  " .. MathUtil.GetNumberFormatStr(price, 1) .. "  ")
                    self._wtButtonBuyBundle:SetV1S3SubTitle("  " .. MathUtil.GetNumberFormatStr(priceOrign, 1) .. "  ")
                else
                    self._wtButtonBuyBundle:SetV1S3ButtonStyle(0)
                    local priceStr = string.format(StoreConfig.Loc.ItemPriceNormal,
                        Module.Currency:GetRichTxtImgByItemId(currencyItemId),
                        MathUtil.GetNumberFormatStr(price, 1))

                    self._wtButtonBuyBundle:SetMainTitle(priceStr)
                end
            end
        end

        if self._wtCommonDownload ~= nil then
            if Module.ExpansionPackCoordinator:IsSupportLitePackage() then
                local moduleName = Module.ExpansionPackCoordinator:GetDownloadCategary(curdata.id)
                if moduleName ~= nil and moduleName ~= "None" then
                    local bDownloaded = self._wtCommonDownload:InitModuleKey(moduleName)
                    if bDownloaded then
                        self._wtCommonDownload:Hidden()
                    else
                        self._wtCommonDownload:Visible()
                    end
                else
                    self._wtCommonDownload:Hidden()
                end
            else
                self._wtCommonDownload:Hidden()
            end
        end
    end

    if self._wtDiscountRoot ~= nil and self._wtTextDiscount ~= nil then
        if self.shopData.IsDiscountIntensity == 1 and priceOrign > price and self.bIsSellOut == false then
            self._wtDiscountRoot:Visible()
            local disCount = (priceOrign - price) / priceOrign
            local showDiscountStr = string.format("%.0f", disCount * 100)
            self._wtTextDiscount:SetText(StringUtil.SequentialFormat(StoreConfig.Loc.ItemDiscount, showDiscountStr))
        else
            self._wtDiscountRoot:Collapsed()
        end
    end
end

function ProductPreviewMarket:_OnButtonRoleSkinPreviewClick()
    local curdata = self.goodsItems[1]
    if curdata == nil then
        return
    end
    -- 打开配件预览界面
    local imageSourceLogo = nil
    if self.shopData ~= nil then
        imageSourceLogo = self.shopData.ImageSourceLogo
    end

    Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, curdata.id, 0, imageSourceLogo)
    -- 埋点
    -- LogAnalysisTool.SignButtonClicked(10154003)
end

function ProductPreviewMarket:_OnButtonPreviewClick()
    local curdata = self.goodsItems[1]
    if curdata ~= nil then
        local item = ItemBase:New(curdata.id)
        local skinInfo = pb.WeaponSkinInfo:New()
        skinInfo.skin_id = curdata.id
        skinInfo.skin_gid = 0
        local prop = pb.PropInfo:New()
        prop.id = curdata.id
        prop.gid = 0
        prop.num = 1
        curdata.prop = prop
        ItemBase.SetWeaponSkinInfo2PropInfo(curdata.prop, skinInfo)
        ItemBase.SetWeaponSkinID2PropInfo(curdata.prop, curdata.prop.id)
        ItemBase.SetWeaponSkinGUID2PropInfo(curdata.prop, curdata.prop.gid)
        item:SetRawPropInfo(curdata.prop, true)
        if item ~= nil then
            if item.itemMainType == EItemType.WeaponSkin or item.itemMainType == EItemType.Weapon then
                Module.Collection:ShowWeaponSkinDetailPage(item)
                -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item, nil, true)
            else
                local anchorItem = self._wtButtonPreview
                -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item)
                if self._wtWaterFallList ~= nil then
                    local child = self._wtWaterFallList:GetItemByIndex(self.curSelectIndex)
                    if child ~= nil then
                        anchorItem = child
                    end
                end

                Module.ItemDetail:OpenItemDetailPanel(item, anchorItem, nil, nil, nil, nil, nil, nil, nil, nil)
            end
        end
    end

    self:ClearTips()
end

function ProductPreviewMarket:ClearTips()
    if DFHD_LUA == 1 then
        self:_OnUnhovered()
    else
        if self._wtDetailCheckBtn ~= nil then
            self._wtDetailCheckBtn:SetIsChecked(false)
        end
    end
end

function ProductPreviewMarket:RefreshSelectItemInfo()
    local curdata = self.goodsItems[self.curSelectIndex]
    if curdata ~= nil then
        local itemData = ItemBase:New(curdata.id)
    end
end

function ProductPreviewMarket:RefreshStoreModelInfo(id)
    self:OnRefreshModel(id)
end

function ProductPreviewMarket:OnRefreshModel(itemId)

    if itemId == nil then
        return
    end

    local itemData = ItemBase:NewIns(itemId)
    if not itemData then
        return
    end

    self._wtButtonItemPreview:Collapsed()

    if self._wtImageRoot ~= nil then
        self._wtImageRoot:Collapsed()
    end

    if self._wtImageSparyRoot ~= nil then
        self._wtImageSparyRoot:Collapsed()
    end

    Facade.HallSceneManager:ResetRootActorOffset() --重新设置rootActor的偏移
    Module.Hero:ShowOperatorWatch("None") --删除手表
    -- 设置组件的Type
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence") -- 重置干员Sequence
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopPlaySequenceVoice") -- 停止播放干员语音

    local uiSettings = UITable[UIName2ID.StoreProductPreview]
    if uiSettings ~= nil then
        if itemData.itemMainType == EItemType.Fashion then
            uiSettings.LinkSubStage = ESubStage.HallHero
        else
            uiSettings.LinkSubStage = ESubStage.HallMall
        end
    end

    if itemData.itemMainType == EItemType.Fashion then -- 红角时装
        self:ShowRewardFashion(itemId)
    elseif itemData.itemMainType == EItemType.WeaponSkin then -- 武器皮肤
        self:ShowRewardWeaponSkin(itemData)
    elseif itemData.itemMainType == EItemType.Adapter then -- 配件
        self:ShowRewardAdapter(itemData)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.AnimShow then -- 表演动画3p
        self:PlayCharacterAnim(itemData.id)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Gesture then -- 表演手势1p
        self:PlayGesture(itemData.id)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Lines then -- 台词
        self:ShowLinesInfo()
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Watch then -- 手表
        self:ShowOperatorWatch(itemId)
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.Card then -- 名片
        self:ShowShowCardInfo()
        local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
        -- 静态图集路径
        local iconPath = ""
        if itemAsset ~= nil then
            iconPath = itemAsset.ItemIconPath
        end

        local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
        if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
            iconPath = gameItemRow.MallItemIcon
        end
        if iconPath ~= "" then
            self._wtImageRoot:Visible()
            self:SetSize(4)
            self.wtImageIcon:AsyncSetImagePath(iconPath, true)
        end
    elseif itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SparyPaint then -- 喷漆
        -- 道具资源信息
        local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
        -- 静态图集路径
        local iconPath = ""
        if itemAsset ~= nil then
            iconPath = itemAsset.ItemIconPath
        end

        local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
        if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
            iconPath = gameItemRow.MallItemIcon
        end
        self:ShowSparyPaintInfo(itemData, iconPath)
    else -- 其他物品
        self.Type = 0
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")

        -- DFImage_191
        if self.wtImageIcon ~= nil then
            -- 道具资源信息
            local itemAsset = ItemConfigTool.GetItemAssetById(itemData.id)
            -- 静态图集路径
            local iconPath = ""
            if itemAsset ~= nil then
                iconPath = itemAsset.ItemIconPath
            end

            local gameItemRow = ItemConfigTool.GetItemConfigById(itemData.id)
            if gameItemRow and gameItemRow.MallItemIcon and gameItemRow.MallItemIcon ~= "" then
                iconPath = gameItemRow.MallItemIcon
            end

            self._wtImageRoot:Visible()
            self._wtImageSparyRoot:Collapsed()

            local bSetSize = false
            if itemData.itemMainType == EItemType.HeroAccessory then
                if itemData.itemSubType == EHeroAccessroy.SparyPaint then
                    self._wtImageRoot:Collapsed()
                    self._wtImageSparyRoot:Visible()
                    local SprayPaintDatas = HeroHelperTool.GetAllSprayPaintData()
                    self.SprayPaintData = SprayPaintDatas[itemData.id]
                    if self.SprayPaintData ~= nil then
                        
                        self:SetBigImgtImg(self.SprayPaintData.SprayPaintDisplay)
                    end
                else
                    self:SetSize(1)
                end

                bSetSize = true
            elseif itemData.itemMainType == EItemType.CollectionProp or itemData.id == ECurrencyItemId.Diamond then
                bSetSize = true
                if IsHD() then
                    self:SetSize(1)
                else
                    self:SetSize(5)
                end
            elseif itemData.itemMainType == EItemType.SocialAppearance then
                bSetSize = true
                if itemData.itemSubType == ESocialAppearanceType.SocialAvatarTab then
                    self:SetSize(1)
                elseif itemData.itemSubType == ESocialAppearanceType.SparyPaint then

                elseif itemData.itemSubType == ESocialAppearanceType.SocialMilitaryTab then
                    self:SetSize(3)
                else
                    bSetSize = false
                end
            end

            if bSetSize == false then
                self:SetSize(0)
            end

            if iconPath ~= "" then
                self.wtImageIcon:AsyncSetImagePath(iconPath, true)
            end

            self.wtImageIcon:Visible()
        end
    end
end

function ProductPreviewMarket:ShowRewardFashion(itemId)
    local fAllLevelFinishCallback = function()
        local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
        if curSubStage ~= ESubStage.HallHero then
            Facade.GameFlowManager:EnterSubStage(ESubStage.HallHero)
        end

        local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(itemId))
        if self.SetCharacterSuitAvatarWithSequence == "None" then -- 播放干员Sequence
            Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, false,
                self.SetCharacterSuitAvatarWithSequence, true)
            self.SetCharacterSuitAvatarWithSequence = "State2"
        else -- 干员站姿
            Module.Hero:PlayCharacterAnimSeqByFashionId(tostring(HeroId), itemId, true,
                self.SetCharacterSuitAvatarWithSequence, true)
        end
        -- --设置当前展示的角色
        -- Module.Hero:SetCurShowHeroById(HeroId)
    end

    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallHero, true, nil, fAllLevelFinishCallback,
        false, 30)

    self._wtButtonItemPreview:Visible()
    -- self._wtBackgroundImg:Collapsed()
end

-- 3P动画
function ProductPreviewMarket:PlayCharacterAnim(itemId)
    self.Type = 4

    local fAllLevelFinishCallback = function()
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallHero)
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "ResetCharacterSequence")
        Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "StopCameraFade") --停止摄像机淡入淡出

        local HeroActionAnimData = HeroHelperTool.GetAllAnimShowData()
        if HeroActionAnimData[itemId] then
            local belongedHeorIds = HeroActionAnimData[itemId].BelongedHeroIDs
            local defFashionId = Server.HeroServer:GetHeroDefFashionID(belongedHeorIds[1])
            if defFashionId == 0 then
                defFashionId = 30000020006 -- 如果为空，默认为威龙
            end
            local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(defFashionId))
            Module.Hero:PlayActionShowAnim(tostring(HeroId), itemId)
            -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCameraAppearanceDisplayType", "CharacterShow", itemId)
            -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "SetCharacterActionShow", defFashionId, itemId)

            -- local HeroId = Server.HeroServer:GetBelongedHeroIdByHeroFashionId(tostring(defFashionId))
            -- if HeroId ~= 0 then
            --     Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallHero, "PlaySceneVideo", tostring(HeroId))
            -- end
        end
    end
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.HallHero, true, nil, fAllLevelFinishCallback,
        false, 30)

    -- self._wtBackgroundImg:Collapsed()
end

-- 1P手势动画
function ProductPreviewMarket:PlayGesture(itemId)
    self.Type = 0
    self._videoBG:Visible()
    Timer.DelayCall(0.3, function()
        self._mediaComponent:Stop()
        self._mediaComponent:Play(itemId)
    end)
    -- self._wtBackgroundImg:Collapsed()
end

-- 显示手表
function ProductPreviewMarket:ShowOperatorWatch(itemId)
    self.Type = 4
    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end
    Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 3, EOffsetType.ZOffset)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWatch", itemId)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")

    -- self._wtBackgroundImg:Collapsed()
end

-- 武器皮肤
function ProductPreviewMarket:ShowRewardWeaponSkin(item)
    self.Type = 5

    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetWheelInteract", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    --Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), item.id,
    --false, false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeaponAutoBoundAdapter", item:GetRawDescObj()
        , false, false)

    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")

    -- self._wtBackgroundImg:Collapsed()
end

-- 配件
function ProductPreviewMarket:ShowRewardAdapter(item)
    self.Type = 5

    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end
    Facade.HallSceneManager:SetRootActorOffset("WatchMeshShow", 6, EOffsetType.ZOffset)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", true)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", item:GetRawDescObj(), item.id,
        false, false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")

    -- self._wtBackgroundImg:Collapsed()
end

-- 台词
function ProductPreviewMarket:ShowLinesInfo()
    self.Type = 4

    local curSubStage = Facade.GameFlowManager:GetCurrentSubStage()
    if curSubStage ~= ESubStage.HallMall then
        Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    end
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")

    -- self._wtBackgroundImg:Collapsed()
end

function ProductPreviewMarket:ShowSparyPaintInfo(itemData, iconPath)
    self.Type = 2
    self.DFScaleBox_Spray:Visible()
    -- self._wtBackgroundImg:Collapsed()

    Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")

    if iconPath then
        if itemData.itemMainType == EItemType.HeroAccessory and itemData.itemSubType == EHeroAccessroy.SparyPaint then --  喷漆
            local SprayPaintDatas = HeroHelperTool.GetAllSprayPaintData()
            self.SprayPaintData = SprayPaintDatas[itemData.id]
            if self.SprayPaintData ~= nil then
                self:SetBigImgtImg(self.SprayPaintData.SprayPaintDisplay)
            end
        end
    end
end

-- 名片
function ProductPreviewMarket:ShowShowCardInfo()
    self.Type = 1
    Facade.GameFlowManager:EnterSubStage(ESubStage.HallMall)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetIsAdapter", false)
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "ResetWeapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "DestroyWatch")
    -- self._wtBackgroundImg:Visible()
end

--设置喷漆大图
function ProductPreviewMarket:SetBigImgtImg(imgPath)
    self:PlayAnimation(self.WBP_Store_ProductPreview_spray, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
    if not imgPath or string.len(imgPath) <= 0 then return end
    ResImageUtil.AsyncLoadImgObjByPath(imgPath, true, self.SetTextureParam, self)
end

function ProductPreviewMarket:SetTextureParam(imageAsset, bAutoResize)
    if self.SprayPaintData then
        local DynamicMatIns = self.wtImageSpray:GetDynamicMaterial()
        if DynamicMatIns then
            DynamicMatIns:SetTextureParameterValue("Texture_Flipbook", imageAsset)
            DynamicMatIns:SetScalarParameterValue("X_Flipbook", self.SprayPaintData.SprayPaintOffset[1])
            DynamicMatIns:SetScalarParameterValue("Y_Flipbook", self.SprayPaintData.SprayPaintOffset[2])
        end
    end
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function ProductPreviewMarket:OnOpen()
    self._displayCtrlActor = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.HallWeaponShow)
end

function ProductPreviewMarket:RegisterEventListeners()
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._ModuleDownloadResult, self)
    self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged, self._ModuleCheckResult, self)
    self:AddLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged, self._OnPackStateChanged, self)

    self:AddLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick, self._OnStoreProductViewBundleItemClickClick, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyMarketSuc, self._OnStoreBuyHotRecommendationSuc,
        self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyMarketUpdate, self._OnStoreBuyRecordUpdate, self)
    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self) --断线重连

    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnRewardPanelClosed, self) --奖励弹窗关闭

    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestSuc, self._OnStoreBuyHotRecommendationSuc, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestUpdate, self._OnStoreBuyRecordUpdate, self)
end

function ProductPreviewMarket:UnRegisterEventListeners()
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged)
    self:RemoveLuaEvent(Module.ExpansionPackCoordinator.Config.Events.evtExpansionPackStateChanged)

    self:RemoveLuaEvent(StoreConfig.evtStoreProductViewBundleItemClick)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyMarketSuc)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyMarketUpdate)

    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)

    self:RemoveLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnRewardPanelClosed)

    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestSuc)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBuyLuckyNestUpdate)
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function ProductPreviewMarket:OnClose()
    self:RemoveAllLuaEvent()

    self.lastLoadModelID = 0

    -- Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayType", "Weapon")
    Facade.HallSceneManager:CallSceneCtrlFunctionBySubstage(ESubStage.HallMall, "SetDisplayWeapon", nil, 0,
        false, false)
end

function ProductPreviewMarket:_OnStoreProductViewBundleItemClickClick(position)
    local curdata = self.goodsItems[1]
    if curdata ~= nil then
        self:RefreshStoreModelInfo(curdata.id)
    end

    self:ClearTips()
end

function ProductPreviewMarket:_OnStoreBuyRecordUpdate()
    if self.isLuckyNest then
        self:_RefreshShowInfo(true)
    else
        self:_RefreshShowInfo()
    end
    self._wtWaterFallList:RefreshAllItems()
end

function ProductPreviewMarket:_OnRelayConnected()
    self.SendShopGetBuyRecord = true
    Server.StoreServer:SendShopGetSpecialBackInfoReq()
end

function ProductPreviewMarket:_OnRewardPanelClosed()
    local curdata = self.goodsItems[1]
    if curdata ~= nil then
        self:RefreshStoreModelInfo(curdata.id)
    end
end

-----bundel

function ProductPreviewMarket:_OnStoreBuyHotRecommendationSuc(dataChange)
    --update buy record
    loginfo("[ProductPreviewMarket] _OnStoreBuyHotRecommendationSuc")
    local itemList = {}
    if dataChange then
        if dataChange.prop_changes then
            local prop_changes = dataChange.prop_changes
            for _, propChange in ipairs(prop_changes) do
                if propChange.prop then
                    local item = ItemHelperTool.CreateItemByPropInfo(propChange.prop, propChange)
                    table.insert(itemList, item)
                    self.nowGetRewardID = propChange.prop.id
                end
            end
        end
    end

    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList, nil, false, false, true)
end

function ProductPreviewMarket:_OnCloseBtnClicked()

end

function ProductPreviewMarket:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end

function ProductPreviewMarket:_OnTabPressed()
    Facade.UIManager:CloseUI(self)
end

function ProductPreviewMarket:ReportViewDetailEvent(bindid, eventType, eventParam, payType, payParam)
    local BindType = 4
    local BundleID = bindid

    LogAnalysisTool.DoSendStoreViewDetailEventReportLog(BindType, BundleID, eventType, eventParam, payType, payParam)
end

function ProductPreviewMarket:_OnButtonBuyBundleClick()
    local isSellOut = false

    Facade.UIManager:AsyncShowUI(UIName2ID.StoreRecommendBuyMarket, nil, nil, self.shopData, self.goodsItems,
        self.goodsItems, self.isLuckyNest)

    self:ClearTips()
end

function ProductPreviewMarket:_OnHovered()
    if self._wtDetailCheckBtn ~= nil and self._wtDetailCheckBtn:IsVisible() == false then
        return
    end

    self._wtTipHandle = self:ShowCommonMessageWithAnchor(StoreConfig.Loc.RecommendBundleItemIsPresentTip,
        self._wtDFTipsAnchor)
end

function ProductPreviewMarket:_OnUnhovered()
    self:RemoveCommonMessageWithAnchor(self._wtTipHandle, self._wtDFTipsAnchor)
    self._wtTipHandle = nil
end

function ProductPreviewMarket:_OnCheckStateChanged(bChecked)
    self:_OnUnhovered()
    if bChecked then
        if self._wtDetailCheckBtn ~= nil and self._wtDetailCheckBtn:IsVisible() == false then
            return
        end

        self._wtTipHandle = self:ShowCommonMessageWithAnchor(StoreConfig.Loc.RecommendBundleItemIsPresentTip,
            self._wtDFTipsAnchor)
    else
        if self._wtTipHandle then
            self:RemoveCommonMessageWithAnchor(self._wtTipHandle, self._wtDFTipsAnchor)
            self._wtTipHandle = nil
        end
    end
end

function ProductPreviewMarket:ShowCommonMessageWithAnchor(content, tipsAnchor)
    local function loadFinCall(uiIns)
        if isvalid(tipsAnchor) then
            tipsAnchor:BindTipsWidget(uiIns)
            uiIns:Visible()
        end
    end

    local data = {
        textContent = content,
        styleRowId = "C002"
    }
    local handle = Facade.UIManager:AsyncShowUI(UIName2ID.CommonMessageTips2, loadFinCall, nil, { data })
    return handle
end

function ProductPreviewMarket:RemoveCommonMessageWithAnchor(handle, tipsAnchor)
    if handle then
        local uiIns = handle:GetUIIns()
        if uiIns then
            if isvalid(tipsAnchor) then
                tipsAnchor:UnbindTipsWidget()
            end
        end
        Facade.UIManager:CloseUIByHandle(handle)
    end
end

function ProductPreviewMarket:_ModuleDownloadResult(moduleName, bSuccess, errorCode, bShowTips)
    if self._wtCommonDownload == nil then
        return
    end

    --if IsMobile() or IsInEditor() then
    logerror("[ProductPreview] _ModuleDownloadResult ")
    local curdata = self.goodsItems[self.curSelectIndex]
    if curdata ~= nil then
        local nowModuleName = Module.ExpansionPackCoordinator:GetDownloadCategary(curdata.id)
        if nowModuleName ~= nil and nowModuleName ~= "None" then
            local bDownloaded = Module.ExpansionPackCoordinator:IsDownloadedByModuleName(nowModuleName)
            if bDownloaded then
                logerror("[ProductPreview] _ModuleDownloadResult bDownloaded:true")
                self._wtCommonDownload:Hidden()
                self:OnRefreshModel(curdata.id)
            else
                logerror("[ProductPreview] _ModuleDownloadResult bDownloaded:false")
                self._wtCommonDownload:Visible()
            end
        else
            self._wtCommonDownload:Hidden()
        end
    end
    --end
end

function ProductPreviewMarket:_OnPackStateChanged(moduleName, packState)
    local isSuccess = packState == Module.ExpansionPackCoordinator.Config.PackState.Installed
    self:_ModuleDownloadResult(moduleName, isSuccess, 0)
end

function ProductPreviewMarket:_ModuleCheckResult()
    if self._wtCommonDownload == nil then
        return
    end

    if IsMobile() or IsInEditor() then
        local curdata = self.goodsItems[self.curSelectIndex]
        if curdata ~= nil then
            local moduleName = Module.ExpansionPackCoordinator:GetDownloadCategary(curdata.id)
            if moduleName ~= nil and moduleName ~= "None" then
                local bDownloaded = self._wtCommonDownload:InitModuleKey(moduleName)
                if bDownloaded then
                    self._wtCommonDownload:Hidden()
                else
                    self._wtCommonDownload:Visible()
                end
            else
                self._wtCommonDownload:Hidden()
            end
        end
    end
end

function ProductPreviewMarket:_WeaponRotationX(value)
    if not IsHD() then
        return
    end

    self._RotationVec.X = value * -1 * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end

end
function ProductPreviewMarket:_WeaponRotationY(value)
    if not IsHD() then
        return
    end

    self._RotationVec.Y = value * self._WeaponRotationZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleRotationByGamepad(self._RotationVec)
    end
end

function ProductPreviewMarket:_WeaponScaleUp(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()
    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function ProductPreviewMarket:_WeaponScaleDown(value)
    if not IsHD() then
        return
    end
    local scaleValue = value * -1 * self._WeaponScaleZoom * LuaTickController:Get():GetDeltaTime()

    if isvalid(self._displayCtrlActor) then
        self._displayCtrlActor:HandleScaleByGamepad(scaleValue)
    end
end

function ProductPreviewMarket:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 切换页签的时候也会触发onshowbegin，要确保visible的时候才初始化手柄功能
    if not self:IsVisible() then
        return
    end

    self.hasTipClick = false

    -- 配置keyIcon
    if self._wtButtonBuyBundle then
        self._wtButtonBuyBundle:SetDisplayInputAction("MallPurchase", true, nil, true)
    end

    -- 配置输入
    if not self._Purchase then
        self._Purchase = self:AddInputActionBinding(
        "MallPurchase", 
        EInputEvent.IE_Pressed, 
        self._OnButtonBuyBundleClick,
        self, 
        EDisplayInputActionPriority.UI_Stack
        )  
    end

    -- 设置导航
    if self._wtWaterFallList then
        if not self._NavGroup_WaterFall then
            self._NavGroup_WaterFall = WidgetUtil.RegisterNavigationGroup(self._wtWaterFallList , self, "Hittest")
        end
        self._NavGroup_WaterFall:AddNavWidgetToArray(self._wtWaterFallList)
        self._NavGroup_WaterFall:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup_WaterFall)
    end
    
    self:_RefreshGampadBottomBar()

    --武器旋转的输入
    if self._RotationX == nil then
        self._RotationX = self:AddAxisInputActionBinding("Common_Right_X", self._WeaponRotationX, self, EDisplayInputActionPriority.UI_Stack)
    end
    if self._RotationY == nil then
        self._RotationY = self:AddAxisInputActionBinding("Common_Right_Y", self._WeaponRotationY, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleUp == nil then
        self._ScaleUp = self:AddAxisInputActionBinding("Common_Right_Trigger_Axis", self._WeaponScaleUp, self, EDisplayInputActionPriority.UI_Stack)
    end

    if self._ScaleDown == nil then
        self._ScaleDown = self:AddAxisInputActionBinding("Common_Left_Trigger_Axis", self._WeaponScaleDown, self, EDisplayInputActionPriority.UI_Stack)
    end
end

function ProductPreviewMarket:_RefreshGampadBottomBar()
    local summaryList = {}
    if self._wtButtonPreview then
        table.insert(summaryList, {actionName = "Store_ItemDetail", func = self._OnButtonPreviewClick, caller = self, bUIOnly = false, bHideIcon = false})
    end

    Module.CommonBar:SetBottomBarInputSummaryList(summaryList, false)
end

function ProductPreviewMarket:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.RemoveNavigationGroup(self)
    self._NavGroup_WaterFall = nil
    self._NavGroup_ButtonRoot = nil

    if self._Purchase then
        self:RemoveInputActionBinding(self._Purchase)
        self._Purchase = nil
    end
end
--- END MODIFICATION

return ProductPreviewMarket
