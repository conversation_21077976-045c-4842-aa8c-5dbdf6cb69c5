--------------------------------------------------------------------------
--- 业务被C++调用的流程方法&全局方法放这里
--------------------------------------------------------------------------
-- Some C++ API About Business
-- For Example:
--      (0)Business Init
--      (1)LuaFrontEndGameMode BeginPlay/EndPlay
--      (2)Event Route About UDFMGamePlayDelegates, UGPGamePlayDelegates, .....
--      (3)Others
--

--------------------------------------------------------------------------
--- Entrance
--------------------------------------------------------------------------
local log = function(...)
    print('[DFMMain Business]  ',...)
end
require "DFM.YxFramework.Core.FrameHelper.PlatformHelper"
local ImportHelper = require "DFM.YxFramework.Core.FrameHelper.ImportHelper"
local EntranceFlow = require("DFM.BusinessEntrance.EntranceFlow")
local EntranceCppBind = require("DFM.BusinessEntrance.EntranceCppBind")
--------------------------------------------------------------------------
--- GameFlowStage
--------------------------------------------------------------------------

-------------------------------------------------新单机流程相关 begin-------------------------------------------------
--- GFStage_Standalone
--[[
    单机环境下的系统拉起。
    放到一个确定的时间点，这里选择GameInstance的OnStart，这样无论是直连ds还是单机，都在world之前。而且是world init之前。
    有问题，这个时间点无法判定当前level。
    现在暴露到蓝图里面的OnStartGameInstance是一个不确定的时间点，单机时在world.beginplay之后。连ds时在world.beginplay之前。
但两种启动方式都在world的init之后。相对world init又是确定的。
    需不需要考虑重新回到login的情况？好像不用，非login情况不会重启虚拟机
]]--
-- LuaSubSystem调用
function GFStandalone_Init()
    EntranceFlow.StandaloneEntrance(IsNewGameFlowLifeTime())
end

function IsStandalone()
    return EntranceFlow.bStanalone
end

function IsNewGameFlowLifeTime()
    return GetGameInstance().NewGameFlowLifeTime and true or false
end

function IsWorldTypePIE()
    local world = GetWorld()
    local netMode = ULuautils.GetWorldType(world)
    return netMode == 3; --EWorldType::Type.PIE
end

function IsNetModeStandalone()
    local world = GetWorld()
    local netMode = ULuautils.GetWorldNetMode(world)
    return netMode == 0; --ENetMode.NM_Standalone
end

function IsStandaloneGame()
    return not IsWorldTypePIE() and IsNetModeStandalone()
end
-------------------------------------------------新单机流程相关 end-------------------------------------------------

-------------------------------------------------主流程相关 begin---------------------------------------------------
--- 启动流程LaunchStep
function GLS_LuaWarmup()
    print("GLS_LuaWarmup()")
     -- 目前只有一步需要处理，初始化UILayers，未来拓展
    EntranceFlow.LuaWarmupEntrance()
end

--- GFStage_Hotfix
--- 第0步：初始化和热更
function GFHotfix_InitHotfixBusiness()
    print("GFHotfix_InitHotfixBusiness called version 171142")
    EntranceFlow.HotFixEntrance()
end

--- GFStage_GCloud
--- 第0.2步：Maple初始化配置和DNS地址 & 公告
function GFHotfix_InitGCloudBusiness()
    print("GFHotfix_InitGCloudBusiness called version 17:42")
    --EntranceFlow.GCloudEntrance()
end

--- GFStage_DeviceCheck
--- 第0.3步：检查当前设备是否可以登录
function GFDeviceCheck_CheckDevice()
    EntranceFlow.DeviceCheckEntrance()
end

--- GFStage_InstallPathCheck
--- 第0.4步：检查安装路径是否合法
function GFInstallPathCheck_CheckPath()
    EntranceFlow.InstallPathCheckEntrance()
end

--- GFStage_VersionUpdate
--- 第0.5步：版本更新
function GFVersionUpdate_InitVersionUpdateBusiness()
    EntranceFlow.VersionUpdateEntrance()
end

--- GFStage_VersionUpdate
--- 第0.6步：Puffer更新
function GFVersionUpdate_InitPufferUpdateBusiness()
    EntranceFlow.PufferUpdateEntrance()
end

--- GFStage_Login
--- 第一步：资源预加载
function GFLogin_InitPreloadBusiness()
    EntranceFlow.PreloadEntrance()
end

function GGPUDriverWarningAndQuitHD()
    EntranceFlow.GPUDriverWarningAndQuitHD()
end

function GCheckSystemVersionHD()
    EntranceFlow.CheckSystemVersionHD()
end

function GMemoryWarningHD()
    if DFHD_LUA == 1 then
        EntranceFlow.MemoryWarningHD()
    end
end

function GVideoMemoryWarningHD()
    if DFHD_LUA == 1 then
        EntranceFlow.VideoMemoryWarningHD()
    end
end

function GCheckHardwareModelHD()
    if DFHD_LUA == 1 then
        EntranceFlow.CheckHardwareModelHD()
    end
end

function GGPUCrashDebuggingWarningHD()
    if DFHD_LUA == 1 then
        EntranceFlow.GPUCrashDebuggingWarning()
    end
end

--- GFStage_Login
--- 第二步：登录初始化业务
function GFLogin_InitLoginBusiness(bInGameDebug)
    EntranceFlow.LoginEntrance(bInGameDebug)
end

--- GFStage_Login
---第三步：初始化基础业务
function GFLogin_InitLobbyBusiness()
    EntranceFlow.LobbyEntrance()
end

--- GFStage_PostLaunch
--- PC首次配置
function GFPostLaunch_InitBusiness()
    EntranceFlow.PostLaunchEntrance()
end

--------------------------------------------------------------------------
--- Lua Flow Step (When Open Level With Config)
--------------------------------------------------------------------------
---第四步：登录——到模式大厅拉取Server数据(FlowStep调用)
function FS_ModeHallFetchServerBusiness()
    EntranceFlow.ServerFetchEntrance()
end

function DoFetchServerBusiness()
    EntranceFlow.DoServerFetchEntrance()
end

function FS_LuaReLoadLocalizationData()
    --TODO:重新加载所有Config.Loc中的本地化文本信息，并清空位于LuaMacro中的本地化文本缓存
    EntranceFlow.SwitchLocEntrance()
end

-------------------------------------------------主流程相关 end-------------------------------------------------

--------------------------------------------------------------------------
--- 其他单机流程
--------------------------------------------------------------------------
--- 主流程之外的单机ds初始化 BP_DFMGameInstance
function InitInGameDebugBusiness()
    logwarning("DFMGameInstance InitInGameDebugBusiness")
    EntranceFlow.InGameDebugEntrance()
end

--- 主流程之内[从登录选择开发按钮进入到单机]的单机ds初始化
function InitSingleLobbyBusiness()
    EntranceFlow.SingleLobbyEntrance()
end

--------------------------------------------------------------------------
--- 业务ShutDown流程
--------------------------------------------------------------------------
function ShutDownBusiness()
    EntranceFlow.ShutDownBusinessEntrance()
end

--------------------------------------------------------------------------
--- 从局内返回大厅时的虚拟机重启流程，全部都在LoadingFlowStage中处理
--- 需要恢复所有Lua侧的功能
--------------------------------------------------------------------------
function LFS_RestartLoginBusiness()
    EntranceFlow.RestartLoginBusinessEntrance()
end

function LFS_ReconnectServer()
    -- 需要重连服务器
    EntranceFlow.DoReconnectEntrance()
end

function LFS_RestartLobbyBusiness()
    EntranceFlow.RestartLobbyBusinessEntrance()
end

--------------------------------------------------------------------------
--- world
--------------------------------------------------------------------------
__World = GetGameInstance():GetWorld()
function GetWorld()
    return __World
end

function IsCurrentWorldValid()
    local LevelName = UGameplayStatics.GetCurrentLevelName(GetGameInstance(), true)
    -- return LevelName ~= "Untitled"
    return string.find(LevelName, "Untitled") == nil
end

function IsLoadingWorld()
    local LevelName = UGameplayStatics.GetCurrentLevelName(GetGameInstance(), true)
    return LevelName == "LoadingMap"
end

function IsInEditor()
    return _WITH_EDITOR == 1
end

function IsHD()
    return DFHD_LUA == 1
end

function IsMobile()
    return (PLATFORM_ANDROID == 1 or PLATFORM_OPENHARMONY  == 1 or PLATFORM_IOS  == 1)
end

--- BEGIN MODIFICATION @ VIRTUOS
function IsWindows()
    return DFHD_LUA == 1 and PLATFORM_WINDOWS == 1
end

function IsXSX()
    return DFCONSOLE_LUA == 1 and PLATFORM_XSX == 1
end

function IsXSS()
    return DFCONSOLE_LUA == 1 and PLATFORM_XSS == 1
end

function IsXboxSeries()
    return IsXSX() or IsXSS()
end

function IsPS5()
    return DFCONSOLE_LUA == 1 and PLATFORM_PS5 == 1
end

function IsPS5Pro()
    return DFCONSOLE_LUA == 1 and PLATFORM_PS5_PRO == 1
end

function IsPS5Family()
    return IsPS5() or IsPS5Pro()
end

function IsConsole()
    return DFCONSOLE_LUA == 1
end

--- 主机Lua热更时加入该log，用于标记热更是否生效，合回stable时需要删除
function ConsoleHotfixLog(format,...)
    local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMHotUpdate)
    if not VersionUtil.IsRelease() then
        local errMsg = "错误! ConsoleHotfixLog未删除，合回Stable后是否忘记将其删除了？.\n" .. debug.traceback()
        logerror(errMsg)
    else
        logerror("!!!Console Hotfix Log!!!", string.format(format, ...))
    end
end
--- END MODIFICATION

function IsWeGameEnabled()
    local UGameSDKStatics = import "GameSDKStatics"
    if UGameSDKStatics.GetWeGameSDKEnabled then
        return UGameSDKStatics.GetWeGameSDKEnabled()
    end
    return false
end

-- 是否正在采集性能
-- 目前认为开了Lua符号即正在采集性能
function IsCollectingPerformanceData()
    local LuaSymbolTool = require "DFM.YxFramework.Plugin.Performance.LuaSymbolTool"
    if LuaSymbolTool then
        return LuaSymbolTool.IsLuaSymbolOn()
    end
end

function IsChannelStoreAPK()
    return CHANNEL_STORE_BASE_APK
end

--region BuildRegion 
--区域化相关判断
--如果只区分国内和海外，可以使用REGION_CN和REGION_OVERSEAS
--如果只判断区域，可以使用IsBuildRegionCN()等函数
--如果需要判断具体构建渠道，比如是否国内体验服等，请使用BUILD_REGION_CN_EXPER等具体变量

---是否国服
function IsBuildRegionCN()
    return BUILD_REGION_CN or BUILD_REGION_CN_EXPER or BUILD_REGION_CN_MATCH
end

---是否全球服
function IsBuildRegionGlobal()
    return BUILD_REGION_GLOBAL or BUILD_REGION_GLOBAL_EXPER or BUILD_REGION_GLOBAL_MATCH
end

--- 是否GA服
function IsBuildRegionGA()
    return BUILD_REGION_GA or BUILD_REGION_GA_EXPER
end

--endregion

-- UE GC 前调用
local _GCStepTimer
local _GCStepCount = 0
local _GCTotalTime = 0

local function _GCStepLoop()
    local getPlatformTimeSecond = getPlatformTimeSecond
    local collectgarbage = collectgarbage
    local stepResult = false
    local startTime = getPlatformTimeSecond()
    local endTime = startTime
    repeat
        stepResult = collectgarbage("step", 10)
        endTime = getPlatformTimeSecond()
        -- loginfo("lua gc step", (endTime - startTime) * 1e6)
    until stepResult or endTime - startTime > 0.001 --最大累计时间1毫秒
    -- loginfo(string.format("lua gc step time: %s us", (endTime - startTime) * 1e6), "count", collectgarbage("count"))
    return stepResult
end

local function _OnGCStep()
    _GCStepCount = _GCStepCount + 1
    local startTime = getPlatformTimeSecond()
    local stepResult = _GCStepLoop()
    local endTime = getPlatformTimeSecond()
    _GCTotalTime = _GCTotalTime + endTime - startTime
    if stepResult then
        _GCStepTimer:Stop()
        _GCStepTimer = nil
        loginfo(string.format("lua gc step total time: %f ms, count：%d", _GCTotalTime * 1000, _GCStepCount))
    end
end

function OnLuaHandleUEPreGC()
    -- local bInFrontend = Facade.GameFlowManager:IsInFrontendFromProcessor()

    -- if not bInFrontend then
    --     return
    -- end
    -- 暂时打开PC局内的GC

    if DFHD_LUA == 1 then
        if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Game then
            if not _GCStepTimer then
                local stepResult = _GCStepLoop()
                _GCStepCount = 1
                _GCTotalTime = 0
                if stepResult then
                    return
                end
                local timer = Timer:NewIns(0.001, 0)
                timer:AddListener(_OnGCStep)
                timer:Start()
                _GCStepTimer = timer
            end
            loginfo("[OnLuaHandleUEPreGC]")
        end
    end
end

-- 跳转地图时,会调用该函数,清除对Actor的引用,防止GC失败
function OnWorldCleanup(world, bSessionEnded, bCleanupResources)
    log("OnWorldCleanup", world, bSessionEnded, bCleanupResources)

    --- 屏蔽掉landmard的打开
    if world == GetWorld() and IsCurrentWorldValid() and not IsLoadingWorld() then
        local levelName = UGameplayStatics.GetCurrentLevelName(GetGameInstance(), true)
        log("Actual cleanup at main world clean up.", levelName, Facade.GameFlowManager:GetCurrentGameFlow())
        if DFMGlobalEvents then
            DFMGlobalEvents.evtWorldCleanUp:Invoke(world, bSessionEnded, bCleanupResources)
        end
        if LuaGlobalEvents then
            LuaGlobalEvents.evtBeforeManagerCleanUp:Invoke()
            LuaGlobalEvents.evtPostManagerCleanUp:Invoke()
        end
        EntranceFlow.DoForceCleanUpObjects()
        ImportHelper.ClearAllCacheStr()
    end
end

function UECall_OnPostCleanupDynamicObjects()
    -- gameflow中触发了这个，代表是断线重连
    -- 无缝进入局内的PostCleanupDynamicObjects一定是在LobbyToGame触发的
    -- 所以当gameflow为SafeHouse时，可以判断为组队安全屋的无缝，此时也不做清理
    local currentGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if currentGameFlow ~= EGameFlowStageType.Game 
    and currentGameFlow ~= EGameFlowStageType.SafeHouse
    and currentGameFlow ~= EGameFlowStageType.GameSettlement then
        if DFMGlobalEvents then
            DFMGlobalEvents.evtCleanUpDynamicObj:Invoke()
        end
        if LuaGlobalEvents then
            LuaGlobalEvents.evtBeforeManagerCleanUp:Invoke()
            LuaGlobalEvents.evtPostManagerCleanUp:Invoke()
        end
        EntranceFlow.DoForceCleanUpObjects()
        ImportHelper.ClearAllCacheStr()
    end
    logerror("UECall_OnPostCleanupDynamicObjects ", Facade.GameFlowManager:GetCurrentGameFlow(), " ", Facade.GameFlowManager:GetCurrentGameFlow() ~= EGameFlowStageType.Game)
end

--------------------------------------------------------------------------
--- Cpp Bind
--------------------------------------------------------------------------
-- for object orientation
-- execution sequence : basec++ ctor -> baselua ctor -> derivedc++ ctor -> derivedlua ctor
function Ctor(classpath, inscpp, ...)
    return EntranceCppBind.CtorBind(classpath, inscpp, ...)
end

-- 给LuaCall使用的,当LuaCall传入this指针时,也就是这里的inscpp变量
-- 假如对这个变量调用过LuaCtor,那么inscpp到这时就已经转变成lua实例,再去该lua实例调用它的类的方法
function Call(functionName, inscpp, ...)
   return EntranceCppBind.Call(functionName, inscpp, ...)
end

--------------------------------------------------------------------------
--- For C++ Call
--------------------------------------------------------------------------
-- For GameFlowGraph
function LuaCallModuleFunction(moduleName, funcName, ...)
    return Facade.ModuleManager:CallModuleFunction(moduleName, funcName, ...)
end

--------------------------------------------------------------------------
--- UE Call Business
--------------------------------------------------------------------------
function UECall_ShowCommonTip(text, duration, bPositive, bDFMShow, bDFHDShow)
    if EntranceFlow.bBaseInit then
        Module.CommonTips:ShowSimpleTip(text, duration, bPositive, bDFMShow, bDFHDShow)
    end
end

function UECall_HideActiveCommonTips()
    if EntranceFlow.bBaseInit then
        Module.CommonTips:HideActiveTips()
    end
end

function UECall_ShowConfirmWindowWithSingleBtn(text)
    Module.CommonTips:ShowConfirmWindowWithSingleBtn(text)
end

function UECall_PrintCoStack()
    print("====================== UECall_PrintCoStack ======================")
    LogUtil.LogTraceback()
end

function UECall_QuitFromGame()
    print("UECall_QuitFromGame: QuitFromGame", true)
end

function UECall_CommitTransition(bShow, AnimSpeedScale)
    Facade.UIManager:CommitTransition(bShow)
end

-------------------------------------------------HUD相关 start-------------------------------------------------
function UECall_CreateHUD_ADFMHUD_OnConstruction(hudTable, baseHudTable)
    EntranceFlow.HUDOnConstructionEntrance(hudTable, baseHudTable)
end

function UECall_CreateHUD_ADFMHUD_BeginPlay(hudTable, baseHudTable)
    EntranceFlow.HUDBeginPlayEntrance(hudTable, baseHudTable) -- MS24 5.16 预加载 创建实例 分开【此处是预加载】
end

function UECall_CreateHUD_ADFMHUD_StartInGame(hudTable, baseHudTable)
    EntranceFlow.HUDStartInGameEntrance(hudTable, baseHudTable) -- MS24 5.16 预加载 创建实例 分开【此处是创建实例】
end

function UECall_DeleteHUD_ADFMHUD_EndPlay(hudTable, baseHudTable)
    EntranceFlow.HUDEndPlayEntrance(hudTable, baseHudTable)
end

function UECall_CreateHUD_AIrisWorldHUD_PreloadRuleHud(hudTable, baseHudTable)
    EntranceFlow.HUDPreloadRuleEntrance(hudTable, baseHudTable)
end

function UECall_DeleteHUD_AIrisWorldHUD_DeletePanels(hudTable, baseHudTable)
    EntranceFlow.HUDDeletePanelsEntrance(hudTable, baseHudTable)
end

function UECall_DestroyAllHUD()
    Facade.UIManager:GetLayerControllerByType(EUILayer.HUD):Reset()
end
-------------------------------------------------HUD相关 end-------------------------------------------------

-------------------------------------------------Replay start-------------------------------------------------
function UECall_LuaBroadcastKillCamExit()
    logerror("UECall_LuaBroadcastKillCamExit execute ")
    Module.Settlement.Config.Events.evtEliminationReplayEnd:Invoke()
end
-------------------------------------------------Replay end-------------------------------------------------

-------------------------------------------------重连相关 start-------------------------------------------------
function UECall_ShowExitToLoginPanel(ShowTips)
--- BEGIN MODIFICATION @ VIRTUOS
    if IsConsole()  then 
        local  function CancelFunc()
            logerror("UECall_ShowExitToLoginPanel execute Function")
            if Facade.ModuleManager:IsModuleValid("Login") then
                Module.Login:BackToLogin(true)
            end
        end
    
        Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
            Module.CommonTips.Config.Loc.ConnectNotLinkBackToLogin,
            CancelFunc,
            NSLOCTEXT("GlobalText", "Lua_Proto_BackToLogin", "返回登录"))

        if PLATFORM_PS5 == 1 then
            local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
            local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetWorld())
            DFMOnlineIdentityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
        end
    else
        Module.CommonTips:ShowConfirmWindowWithSingleBtn(
            ShowTips,
            function()
                Module.InGame.Config.flowEvtReturnToLobby:Invoke()
            end,
            NSLOCTEXT("GlobalText", "LuaLocText_HotUpdate_OK", "确认")
        )
    end
--- END MODIFICATION
end

function UECall_IsCrossPlat()
    return Server.AccountServer:IsCrossPlat()
end

function UECall_ExitToLogin()
    local DFMGameLoadingManager = import("DFMGameLoadingManager").GetGameLoadingManager(GetGameInstance())
    if DFMGameLoadingManager then
        DFMGameLoadingManager:BroadcastLoadingFailed2Login()
    end
end

function UECall_ShowReconnectPanel()
    local function ConfirmFunc()
        logerror("UECall_ShowReconnectPanel confirm reconnect ")
        local reconnectSubSystem = UE.SeamlessReconnectSubSystem.Get(GetWorld())
        if isvalid(reconnectSubSystem) then
            UE.DFMIrisEnterSeamlessGameplayHelper.SetClientSeamlessTravelEnable(GetWorld(), true)
            reconnectSubSystem:ReStartReconnect()
        end
        Server.MatchServer.lastReconnectPanelConfirmTime = TimeUtil.GetCurrentTimeMillis()
    end

--- BEGIN MODIFICATION @ VIRTUOS
if IsConsole()  then 
    local  function CancelFunc()
        logerror("UECall_ShowReconnectPanel cancel reconnect ")
        if Facade.ModuleManager:IsModuleValid("Login") then
            Module.Login:BackToLogin(true)
        end
    end

    Module.CommonTips:ShowConfirmWindowWithSingleBtnAlwaysCallback(
        Module.CommonTips.Config.Loc.ConnectNotLinkBackToLogin,
        CancelFunc,
        NSLOCTEXT("GlobalText", "Lua_Proto_BackToLogin", "返回登录"))
        
    if PLATFORM_PS5 == 1 then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetWorld())
        DFMOnlineIdentityManager:SetUsingPlatformPremiumFeatures(EPlatformPremiumFeaturesType.RealtimeMultiplay, false)
    end
else
    local  function CancelFunc()
        logerror("UECall_ShowReconnectPanel cancel reconnect ")
        Module.InGame.Config.flowEvtReturnToLobby:Invoke()
    end

    Module.GCloudSDK:ShowCommonTip(
        NSLOCTEXT("GlobalText", "Lua_Reconnect_ReconnectPanel", "网络中断, 是否尝试重新连接"),
        NSLOCTEXT("GlobalText", "Lua_Reconnect_ReconnectPanel_Retry", "重新连接"), 
        NSLOCTEXT("GlobalText", "Lua_Reconnect_ReconnectPanel_Back", "返回大厅"),
        false, ConfirmFunc, CancelFunc, true, false)
end 
--- END MODIFICATION
    local function f()
        local bServerInGame = Server.AccountServer:IsPlayerInGame()
        logerror("UECall_ShowReconnectPanel bServerInGame = ", bServerInGame)
        local curFlow = Facade.GameFlowManager:GetCurrentGameFlow()
        if bServerInGame == false and 
        (curFlow == EGameFlowStageType.Game or curFlow == EGameFlowStageType.GameSettlement)  then
            local function ConfirmFunc()
                Module.InGame.Config.flowEvtReturnToLobby:Invoke()
                logwarning("UECall_ShowReconnectPanel 对局结束，返回大厅")
            end

            Module.GCloudSDK:ShowCommonTip(
                NSLOCTEXT("GlobalText", "Lua_Reconnect_GameFinished", "对局已结束"),
                NSLOCTEXT("GlobalText", "Lua_Reconnect_GameFinishedComfirm", "确认"),
                nil, true, ConfirmFunc, nil, true, false)           
        end
    end
    logerror("UECall_ShowReconnectPanel getstateInfo now")
    local param = {bNeedResendAfterReconnected = true}
    Server.AccountServer:GetStateInfo(f,param)
end

function UECall_SeamlessInGameReturnToLobby()
    logerror("UECall_SeamlessInGameReturnToLobby()!!!!!")
    Module.InGame.Config.flowEvtReturnToLobby:Invoke()
    local UDFMIrisEnterSubsystem = import "DFMIrisEnterSubsystem"
    local DFMIrisEnterSubsystem = UDFMIrisEnterSubsystem.Get(GetWorld())
    if DFMIrisEnterSubsystem then
        DFMIrisEnterSubsystem:HideHUD("SeamlessMediaView")
        logerror("DFMIrisEnterSubsystem:HideHUD: SeamlessMediaView")
    end

    -- 停止监听重连
    local USeamlessReconnectSubSystem = import "SeamlessReconnectSubSystem"
    local SeamlessReconnectSubSystem = USeamlessReconnectSubSystem.Get(GetGameInstance())
    if SeamlessReconnectSubSystem then
        logwarning("Stop Listen Net Disconnect!")
        SeamlessReconnectSubSystem:StopListenNetDisconnect()
    end

    -- TODO:MS24临时处理，由于LobbyToGame没有对EvtReturnToLobby做处理，导致在这个时候触发lua的流转事件是不会响应的，因此需要额外处理，触发C++的流转
    -- 否则会导致LoadingManager的流转触发了两次
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    if curGameFlow == EGameFlowStageType.LobbyToGame then
        if DFMIrisEnterSubsystem and DFMIrisEnterSubsystem.OnLobbyToGameTimeout then
            logwarning("UECall_SeamlessInGameReturnToLobby Invoke OnGameSeamlessLoadingTimeout!")
            DFMIrisEnterSubsystem:OnLobbyToGameTimeout() -- 触发回大厅的回调
        end
    else
        logwarning("Skip DFMIrisEnterSubsystem:OnLobbyToGameTimeout()!!!")
    end
end

function UECall_SeamlessTravelFailed2Loading()
    local UDFMGameLoadingManager = import("DFMGameLoadingManager")
    UDFMGameLoadingManager.GetGameLoadingManager(GetWorld()):BroadcastSeamlessTravelFailed2Loading()
end

function UECall_SeamlessTeleportBackSafeHouse()
    Module.IrisSafeHouse:SeamlessFailReturn2SafeHouse()
end

-------------------------------------------------重连相关 end-------------------------------------------------

--------------------------------------------------------------------------
--- Async Loading
--------------------------------------------------------------------------
function OnAsyncLoadingFlush(traceBack, bSyncLoadFromLua)
    local showMessage
    if bSyncLoadFromLua then
        showMessage = "AsyncLoadingFlush happens in Lua.\n" .. debug.traceback()
    else
        showMessage = "AsyncLoadingFlush happens in Cpp.\n" .. traceBack
    end
    LuaGlobalEvents.evtAsyncLoadingFlush:Invoke(showMessage)
end


------------------------------debug.traceback begin------------------------------
local OldDebugTraceback = debug.traceback
local function EmptyDebugTraceback(thread, message, level)
    return "notraceback"
end
function ToggleLuaDebugTraceback(bToggle)
    loginfo("ToggleLuaDebugTraceback", bToggle)
    if bToggle == 1 then
        debug.traceback = OldDebugTraceback
    else
        debug.traceback = EmptyDebugTraceback
    end
end
------------------------------debug.traceback end------------------------------

------------------------------ Performance mode -------------------------------

function EnableLuaPerformanceMode(bEnabled)
    local luaPerfTool = require "DFM.YxFramework.Plugin.Performance.LuaPerfTool"
    luaPerfTool.EnablePerfMode(bEnabled)
end

-------------------------------------------------------------------------------


------------------------------ Weapon Main -------------------------------
function UECall_LuaWeaponMain()
    local LuaWeaponMain = require "DFM.Business.Module.WeaponGameplayModule.LuaWeaponMain"
    LuaWeaponMain.LuaWeaponMain()
end

-------------------------------------------------------------------------------

--------------------------------清理版本更新相关--------------------------------
function UECall_ClearVersionFiles()
    VersionUtil.ClearFiles()
end
-------------------------------------------------------------------------------

--- BEGIN MODIFICATION @ VIRTUOS
----------------------UE Call Lua Cancel Matching-----------------------
function UECall_CancelMatching()
    if IsXSX() and rawget(Server, "MatchServer") and rawget(Server, "TeamServer") then
        if Server.MatchServer:GetIsMatching() then
            Server.TeamServer:SendCancelMatching()
            loginfo("Cancel Matching when suspend game in XSX.")
        end
    end
end
------------------------------------------------------------------------
--- END MODIFICATION