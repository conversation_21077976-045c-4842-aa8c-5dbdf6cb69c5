require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require("DFM.Business.Proto.Pb.errcode_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_matchroom_editor_pb"
end

pb.__pb_CSPrepareJoinMatchNtf = {
    ds_room_id = 0,
    time_stamp = 0,
    player_id = 0,
    team_id = 0,
    random_seed = 0,
    player_idx = 0,
    game_mode = 0,
    iris_opening_event_id = 0,
    iris_event_course_id = 0,
    match_mode_id = 0,
    spawn_point_config_id = 0,
    tod_weather_id = 0,
    is_observer = false,
    observer_id = 0,
}
pb.__pb_CSPrepareJoinMatchNtf.__name = "CSPrepareJoinMatchNtf"
pb.__pb_CSPrepareJoinMatchNtf.__index = pb.__pb_CSPrepareJoinMatchNtf
pb.__pb_CSPrepareJoinMatchNtf.__pairs = __pb_pairs

pb.CSPrepareJoinMatchNtf = { __name = "CSPrepareJoinMatchNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPrepareJoinMatchNtf : ProtoBase
---@field public ds_room_id number
---@field public time_stamp number
---@field public player_id number
---@field public team_id number
---@field public random_seed number
---@field public player_idx number
---@field public game_mode number
---@field public iris_opening_event_id number
---@field public iris_event_course_id number
---@field public room_member_infos pb_MatchRoomTeamMemberInfo[]
---@field public match_mode_id number
---@field public box_event number[]
---@field public spawn_point_config_id number
---@field public tod_weather_id number
---@field public iris_event_ids number[]
---@field public is_observer boolean
---@field public observer_id number

---@return pb_CSPrepareJoinMatchNtf
function pb.CSPrepareJoinMatchNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_PlayerJoinMatchNtf = {
    ds_room_id = 0,
    ds_textual_ip = "",
    ds_port = 0,
    map_id = 0,
    result = 0,
    is_half_join = false,
    ds_domain = "",
}
pb.__pb_PlayerJoinMatchNtf.__name = "PlayerJoinMatchNtf"
pb.__pb_PlayerJoinMatchNtf.__index = pb.__pb_PlayerJoinMatchNtf
pb.__pb_PlayerJoinMatchNtf.__pairs = __pb_pairs

pb.PlayerJoinMatchNtf = { __name = "PlayerJoinMatchNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_PlayerJoinMatchNtf : ProtoBase
---@field public ds_room_id number
---@field public ds_textual_ip string
---@field public ds_port number
---@field public map_id number
---@field public result number
---@field public is_half_join boolean
---@field public player pb_MatchPlayerInfo
---@field public ds_domain string
---@field public ds_ip_list pb_HostInfo[]

---@return pb_PlayerJoinMatchNtf
function pb.PlayerJoinMatchNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomCallNumeralNtf = {
    ds_room_id = 0,
    player_id = 0,
    match_mode_id = 0,
    check_gap = 0,
}
pb.__pb_CSMatchRoomCallNumeralNtf.__name = "CSMatchRoomCallNumeralNtf"
pb.__pb_CSMatchRoomCallNumeralNtf.__index = pb.__pb_CSMatchRoomCallNumeralNtf
pb.__pb_CSMatchRoomCallNumeralNtf.__pairs = __pb_pairs

pb.CSMatchRoomCallNumeralNtf = { __name = "CSMatchRoomCallNumeralNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomCallNumeralNtf : ProtoBase
---@field public ds_room_id number
---@field public player_id number
---@field public match_mode_id number
---@field public check_gap number

---@return pb_CSMatchRoomCallNumeralNtf
function pb.CSMatchRoomCallNumeralNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomCheckCallNumeralReq = {
    ds_room_id = 0,
    player_id = 0,
    game_mode = 0,
    match_mode_id = 0,
}
pb.__pb_CSMatchRoomCheckCallNumeralReq.__name = "CSMatchRoomCheckCallNumeralReq"
pb.__pb_CSMatchRoomCheckCallNumeralReq.__index = pb.__pb_CSMatchRoomCheckCallNumeralReq
pb.__pb_CSMatchRoomCheckCallNumeralReq.__pairs = __pb_pairs

pb.CSMatchRoomCheckCallNumeralReq = { __name = "CSMatchRoomCheckCallNumeralReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomCheckCallNumeralReq : ProtoBase
---@field public ds_room_id number
---@field public player_id number
---@field public game_mode number
---@field public match_mode_id number

---@return pb_CSMatchRoomCheckCallNumeralReq
function pb.CSMatchRoomCheckCallNumeralReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomCheckCallNumeralRes = {
    result = 0,
}
pb.__pb_CSMatchRoomCheckCallNumeralRes.__name = "CSMatchRoomCheckCallNumeralRes"
pb.__pb_CSMatchRoomCheckCallNumeralRes.__index = pb.__pb_CSMatchRoomCheckCallNumeralRes
pb.__pb_CSMatchRoomCheckCallNumeralRes.__pairs = __pb_pairs

pb.CSMatchRoomCheckCallNumeralRes = { __name = "CSMatchRoomCheckCallNumeralRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomCheckCallNumeralRes : ProtoBase
---@field public result number
---@field public ntf pb_CSPlayerJoinMatchNtf

---@return pb_CSMatchRoomCheckCallNumeralRes
function pb.CSMatchRoomCheckCallNumeralRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPlayerJoinMatchNtf = {
    ds_room_id = 0,
    ds_textual_ip = "",
    ds_port = 0,
    map_id = 0,
    result = 0,
    is_half_join = false,
    ds_domain = "",
    match_mode_id = 0,
    enc_flag = 0,
}
pb.__pb_CSPlayerJoinMatchNtf.__name = "CSPlayerJoinMatchNtf"
pb.__pb_CSPlayerJoinMatchNtf.__index = pb.__pb_CSPlayerJoinMatchNtf
pb.__pb_CSPlayerJoinMatchNtf.__pairs = __pb_pairs

pb.CSPlayerJoinMatchNtf = { __name = "CSPlayerJoinMatchNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPlayerJoinMatchNtf : ProtoBase
---@field public ds_room_id number
---@field public ds_textual_ip string
---@field public ds_port number
---@field public map_id number
---@field public result number
---@field public is_half_join boolean
---@field public player pb_MatchPlayerInfo
---@field public ds_domain string
---@field public match_mode_id number
---@field public ds_ip_list pb_HostInfo[]
---@field public xunyou_ip_list pb_HostInfo[]
---@field public enc_flag number

---@return pb_CSPlayerJoinMatchNtf
function pb.CSPlayerJoinMatchNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPlayerJoinSafehouseNtf = {
    ds_room_id = 0,
    ds_textual_ip = "",
    map_id = 0,
    ds_domain = "",
    ds_token = "",
    ds_port = 0,
    sys_team_id = 0,
}
pb.__pb_CSPlayerJoinSafehouseNtf.__name = "CSPlayerJoinSafehouseNtf"
pb.__pb_CSPlayerJoinSafehouseNtf.__index = pb.__pb_CSPlayerJoinSafehouseNtf
pb.__pb_CSPlayerJoinSafehouseNtf.__pairs = __pb_pairs

pb.CSPlayerJoinSafehouseNtf = { __name = "CSPlayerJoinSafehouseNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPlayerJoinSafehouseNtf : ProtoBase
---@field public ds_room_id number
---@field public ds_textual_ip string
---@field public map_id number
---@field public ds_domain string
---@field public ds_token string
---@field public ds_port number
---@field public sys_team_id number
---@field public ds_ip_list pb_HostInfo[]

---@return pb_CSPlayerJoinSafehouseNtf
function pb.CSPlayerJoinSafehouseNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomMatchEndNtf = {
    ds_room_id = 0,
    reason = 0,
}
pb.__pb_CSMatchRoomMatchEndNtf.__name = "CSMatchRoomMatchEndNtf"
pb.__pb_CSMatchRoomMatchEndNtf.__index = pb.__pb_CSMatchRoomMatchEndNtf
pb.__pb_CSMatchRoomMatchEndNtf.__pairs = __pb_pairs

pb.CSMatchRoomMatchEndNtf = { __name = "CSMatchRoomMatchEndNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomMatchEndNtf : ProtoBase
---@field public ds_room_id number
---@field public reason number

---@return pb_CSMatchRoomMatchEndNtf
function pb.CSMatchRoomMatchEndNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomReconnectReq = {
    is_reconnect = false,
    room_id = 0,
}
pb.__pb_CSMatchRoomReconnectReq.__name = "CSMatchRoomReconnectReq"
pb.__pb_CSMatchRoomReconnectReq.__index = pb.__pb_CSMatchRoomReconnectReq
pb.__pb_CSMatchRoomReconnectReq.__pairs = __pb_pairs

pb.CSMatchRoomReconnectReq = { __name = "CSMatchRoomReconnectReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomReconnectReq : ProtoBase
---@field public is_reconnect boolean
---@field public room_id number

---@return pb_CSMatchRoomReconnectReq
function pb.CSMatchRoomReconnectReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomReconnectRes = {
    player_id = 0,
    ds_textual_ip = "",
    ds_ip = 0,
    ds_port = 0,
    result = 0,
    ds_token = "",
    map_id = 0,
    ds_room_id = 0,
    ds_team_id = 0,
    ds_domian = "",
    ds_version = "",
    is_observer = false,
    enc_flag = 0,
}
pb.__pb_CSMatchRoomReconnectRes.__name = "CSMatchRoomReconnectRes"
pb.__pb_CSMatchRoomReconnectRes.__index = pb.__pb_CSMatchRoomReconnectRes
pb.__pb_CSMatchRoomReconnectRes.__pairs = __pb_pairs

pb.CSMatchRoomReconnectRes = { __name = "CSMatchRoomReconnectRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomReconnectRes : ProtoBase
---@field public player_id number
---@field public ds_textual_ip string
---@field public ds_ip number
---@field public ds_port number
---@field public result number
---@field public ds_token string
---@field public map_id number
---@field public ds_room_id number
---@field public ds_team_id number
---@field public ds_domian string
---@field public ds_version string
---@field public ds_ip_list pb_HostInfo[]
---@field public is_observer boolean
---@field public xunyou_ip_list pb_HostInfo[]
---@field public enc_flag number

---@return pb_CSMatchRoomReconnectRes
function pb.CSMatchRoomReconnectRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomGetTdmRoomArmedForceTReq = {
    room_id = 0,
}
pb.__pb_CSMatchRoomGetTdmRoomArmedForceTReq.__name = "CSMatchRoomGetTdmRoomArmedForceTReq"
pb.__pb_CSMatchRoomGetTdmRoomArmedForceTReq.__index = pb.__pb_CSMatchRoomGetTdmRoomArmedForceTReq
pb.__pb_CSMatchRoomGetTdmRoomArmedForceTReq.__pairs = __pb_pairs

pb.CSMatchRoomGetTdmRoomArmedForceTReq = { __name = "CSMatchRoomGetTdmRoomArmedForceTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomGetTdmRoomArmedForceTReq : ProtoBase
---@field public room_id number

---@return pb_CSMatchRoomGetTdmRoomArmedForceTReq
function pb.CSMatchRoomGetTdmRoomArmedForceTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomGetTdmRoomArmedForceTRes = {
    result = 0,
    room_id = 0,
    room_start_time = 0,
    stage_end_time = 0,
}
pb.__pb_CSMatchRoomGetTdmRoomArmedForceTRes.__name = "CSMatchRoomGetTdmRoomArmedForceTRes"
pb.__pb_CSMatchRoomGetTdmRoomArmedForceTRes.__index = pb.__pb_CSMatchRoomGetTdmRoomArmedForceTRes
pb.__pb_CSMatchRoomGetTdmRoomArmedForceTRes.__pairs = __pb_pairs

pb.CSMatchRoomGetTdmRoomArmedForceTRes = { __name = "CSMatchRoomGetTdmRoomArmedForceTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomGetTdmRoomArmedForceTRes : ProtoBase
---@field public result number
---@field public room_id number
---@field public room_start_time number
---@field public stage_end_time number
---@field public player_armed_force_array pb_CSMatchRoomTdmArmedForceShowInfo[]

---@return pb_CSMatchRoomGetTdmRoomArmedForceTRes
function pb.CSMatchRoomGetTdmRoomArmedForceTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomTdmArmedForceShowInfo = {
    player_id = 0,
    armedforce_id = 0,
    hero_id = 0,
}
pb.__pb_CSMatchRoomTdmArmedForceShowInfo.__name = "CSMatchRoomTdmArmedForceShowInfo"
pb.__pb_CSMatchRoomTdmArmedForceShowInfo.__index = pb.__pb_CSMatchRoomTdmArmedForceShowInfo
pb.__pb_CSMatchRoomTdmArmedForceShowInfo.__pairs = __pb_pairs

pb.CSMatchRoomTdmArmedForceShowInfo = { __name = "CSMatchRoomTdmArmedForceShowInfo", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomTdmArmedForceShowInfo : ProtoBase
---@field public player_id number
---@field public armedforce_id number
---@field public profile pb_PlayerSimpleInfo
---@field public hero_id number

---@return pb_CSMatchRoomTdmArmedForceShowInfo
function pb.CSMatchRoomTdmArmedForceShowInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSetTdmRoomArmedForceTReq = {
    room_id = 0,
    select_bag_id = 0,
}
pb.__pb_CSMatchRoomSetTdmRoomArmedForceTReq.__name = "CSMatchRoomSetTdmRoomArmedForceTReq"
pb.__pb_CSMatchRoomSetTdmRoomArmedForceTReq.__index = pb.__pb_CSMatchRoomSetTdmRoomArmedForceTReq
pb.__pb_CSMatchRoomSetTdmRoomArmedForceTReq.__pairs = __pb_pairs

pb.CSMatchRoomSetTdmRoomArmedForceTReq = { __name = "CSMatchRoomSetTdmRoomArmedForceTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSetTdmRoomArmedForceTReq : ProtoBase
---@field public room_id number
---@field public select_bag_id number

---@return pb_CSMatchRoomSetTdmRoomArmedForceTReq
function pb.CSMatchRoomSetTdmRoomArmedForceTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSetTdmRoomArmedForceTRes = {
    result = 0,
}
pb.__pb_CSMatchRoomSetTdmRoomArmedForceTRes.__name = "CSMatchRoomSetTdmRoomArmedForceTRes"
pb.__pb_CSMatchRoomSetTdmRoomArmedForceTRes.__index = pb.__pb_CSMatchRoomSetTdmRoomArmedForceTRes
pb.__pb_CSMatchRoomSetTdmRoomArmedForceTRes.__pairs = __pb_pairs

pb.CSMatchRoomSetTdmRoomArmedForceTRes = { __name = "CSMatchRoomSetTdmRoomArmedForceTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSetTdmRoomArmedForceTRes : ProtoBase
---@field public result number

---@return pb_CSMatchRoomSetTdmRoomArmedForceTRes
function pb.CSMatchRoomSetTdmRoomArmedForceTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomTdmChangeArmedForceNtf = {
    room_id = 0,
}
pb.__pb_CSMatchRoomTdmChangeArmedForceNtf.__name = "CSMatchRoomTdmChangeArmedForceNtf"
pb.__pb_CSMatchRoomTdmChangeArmedForceNtf.__index = pb.__pb_CSMatchRoomTdmChangeArmedForceNtf
pb.__pb_CSMatchRoomTdmChangeArmedForceNtf.__pairs = __pb_pairs

pb.CSMatchRoomTdmChangeArmedForceNtf = { __name = "CSMatchRoomTdmChangeArmedForceNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomTdmChangeArmedForceNtf : ProtoBase
---@field public room_id number
---@field public player_armed_force pb_CSMatchRoomTdmArmedForceShowInfo

---@return pb_CSMatchRoomTdmChangeArmedForceNtf
function pb.CSMatchRoomTdmChangeArmedForceNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomGetSolRoomTeamTReq = {
    room_id = 0,
}
pb.__pb_CSMatchRoomGetSolRoomTeamTReq.__name = "CSMatchRoomGetSolRoomTeamTReq"
pb.__pb_CSMatchRoomGetSolRoomTeamTReq.__index = pb.__pb_CSMatchRoomGetSolRoomTeamTReq
pb.__pb_CSMatchRoomGetSolRoomTeamTReq.__pairs = __pb_pairs

pb.CSMatchRoomGetSolRoomTeamTReq = { __name = "CSMatchRoomGetSolRoomTeamTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomGetSolRoomTeamTReq : ProtoBase
---@field public room_id number

---@return pb_CSMatchRoomGetSolRoomTeamTReq
function pb.CSMatchRoomGetSolRoomTeamTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomGetSolRoomTeamTRes = {
    result = 0,
    room_id = 0,
    room_start_time = 0,
    stage_end_time = 0,
}
pb.__pb_CSMatchRoomGetSolRoomTeamTRes.__name = "CSMatchRoomGetSolRoomTeamTRes"
pb.__pb_CSMatchRoomGetSolRoomTeamTRes.__index = pb.__pb_CSMatchRoomGetSolRoomTeamTRes
pb.__pb_CSMatchRoomGetSolRoomTeamTRes.__pairs = __pb_pairs

pb.CSMatchRoomGetSolRoomTeamTRes = { __name = "CSMatchRoomGetSolRoomTeamTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomGetSolRoomTeamTRes : ProtoBase
---@field public result number
---@field public room_id number
---@field public room_start_time number
---@field public stage_end_time number
---@field public player_info_array pb_SolRoomTeamShowInfo[]

---@return pb_CSMatchRoomGetSolRoomTeamTRes
function pb.CSMatchRoomGetSolRoomTeamTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSetSolRoomHeroTReq = {
    room_id = 0,
    hero_id = 0,
}
pb.__pb_CSMatchRoomSetSolRoomHeroTReq.__name = "CSMatchRoomSetSolRoomHeroTReq"
pb.__pb_CSMatchRoomSetSolRoomHeroTReq.__index = pb.__pb_CSMatchRoomSetSolRoomHeroTReq
pb.__pb_CSMatchRoomSetSolRoomHeroTReq.__pairs = __pb_pairs

pb.CSMatchRoomSetSolRoomHeroTReq = { __name = "CSMatchRoomSetSolRoomHeroTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSetSolRoomHeroTReq : ProtoBase
---@field public room_id number
---@field public hero_id number
---@field public new_fashions pb_HeroFashion[]

---@return pb_CSMatchRoomSetSolRoomHeroTReq
function pb.CSMatchRoomSetSolRoomHeroTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSetSolRoomHeroTRes = {
    result = 0,
}
pb.__pb_CSMatchRoomSetSolRoomHeroTRes.__name = "CSMatchRoomSetSolRoomHeroTRes"
pb.__pb_CSMatchRoomSetSolRoomHeroTRes.__index = pb.__pb_CSMatchRoomSetSolRoomHeroTRes
pb.__pb_CSMatchRoomSetSolRoomHeroTRes.__pairs = __pb_pairs

pb.CSMatchRoomSetSolRoomHeroTRes = { __name = "CSMatchRoomSetSolRoomHeroTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSetSolRoomHeroTRes : ProtoBase
---@field public result number

---@return pb_CSMatchRoomSetSolRoomHeroTRes
function pb.CSMatchRoomSetSolRoomHeroTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSolChangeHeroNtf = {
    room_id = 0,
    player_id = 0,
}
pb.__pb_CSMatchRoomSolChangeHeroNtf.__name = "CSMatchRoomSolChangeHeroNtf"
pb.__pb_CSMatchRoomSolChangeHeroNtf.__index = pb.__pb_CSMatchRoomSolChangeHeroNtf
pb.__pb_CSMatchRoomSolChangeHeroNtf.__pairs = __pb_pairs

pb.CSMatchRoomSolChangeHeroNtf = { __name = "CSMatchRoomSolChangeHeroNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSolChangeHeroNtf : ProtoBase
---@field public room_id number
---@field public player_id number
---@field public hero_info pb_CSHero

---@return pb_CSMatchRoomSolChangeHeroNtf
function pb.CSMatchRoomSolChangeHeroNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SolRoomTeamShowInfo = {
    player_id = 0,
    is_ready = false,
    nick_name = "",
    player_idx = 0,
    title = 0,
    rank_title_adcode = 0,
    rank_title_rank_no = 0,
    plat_id = 0,
    is_bot = false,
    rand_hero_time = 0,
}
pb.__pb_SolRoomTeamShowInfo.__name = "SolRoomTeamShowInfo"
pb.__pb_SolRoomTeamShowInfo.__index = pb.__pb_SolRoomTeamShowInfo
pb.__pb_SolRoomTeamShowInfo.__pairs = __pb_pairs

pb.SolRoomTeamShowInfo = { __name = "SolRoomTeamShowInfo", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SolRoomTeamShowInfo : ProtoBase
---@field public player_id number
---@field public hero_info pb_CSHero
---@field public is_ready boolean
---@field public nick_name string
---@field public player_idx number
---@field public title number
---@field public rank_title_adcode number
---@field public rank_title_rank_no number
---@field public plat_id number
---@field public is_bot boolean
---@field public rand_hero_time number
---@field public rand_hero_info pb_CSHero

---@return pb_SolRoomTeamShowInfo
function pb.SolRoomTeamShowInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSolReadyTReq = {
    room_id = 0,
}
pb.__pb_CSMatchRoomSolReadyTReq.__name = "CSMatchRoomSolReadyTReq"
pb.__pb_CSMatchRoomSolReadyTReq.__index = pb.__pb_CSMatchRoomSolReadyTReq
pb.__pb_CSMatchRoomSolReadyTReq.__pairs = __pb_pairs

pb.CSMatchRoomSolReadyTReq = { __name = "CSMatchRoomSolReadyTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSolReadyTReq : ProtoBase
---@field public room_id number

---@return pb_CSMatchRoomSolReadyTReq
function pb.CSMatchRoomSolReadyTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSolReadyTRes = {
    result = 0,
}
pb.__pb_CSMatchRoomSolReadyTRes.__name = "CSMatchRoomSolReadyTRes"
pb.__pb_CSMatchRoomSolReadyTRes.__index = pb.__pb_CSMatchRoomSolReadyTRes
pb.__pb_CSMatchRoomSolReadyTRes.__pairs = __pb_pairs

pb.CSMatchRoomSolReadyTRes = { __name = "CSMatchRoomSolReadyTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSolReadyTRes : ProtoBase
---@field public result number

---@return pb_CSMatchRoomSolReadyTRes
function pb.CSMatchRoomSolReadyTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSolReadyNtf = {
    room_id = 0,
    player_id = 0,
}
pb.__pb_CSMatchRoomSolReadyNtf.__name = "CSMatchRoomSolReadyNtf"
pb.__pb_CSMatchRoomSolReadyNtf.__index = pb.__pb_CSMatchRoomSolReadyNtf
pb.__pb_CSMatchRoomSolReadyNtf.__pairs = __pb_pairs

pb.CSMatchRoomSolReadyNtf = { __name = "CSMatchRoomSolReadyNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSolReadyNtf : ProtoBase
---@field public room_id number
---@field public player_id number

---@return pb_CSMatchRoomSolReadyNtf
function pb.CSMatchRoomSolReadyNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomStartMatchTglogTReq = {
    ds_room_id = 0,
    sec_report_data = "",
    client_start_time = "",
}
pb.__pb_CSMatchRoomStartMatchTglogTReq.__name = "CSMatchRoomStartMatchTglogTReq"
pb.__pb_CSMatchRoomStartMatchTglogTReq.__index = pb.__pb_CSMatchRoomStartMatchTglogTReq
pb.__pb_CSMatchRoomStartMatchTglogTReq.__pairs = __pb_pairs

pb.CSMatchRoomStartMatchTglogTReq = { __name = "CSMatchRoomStartMatchTglogTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomStartMatchTglogTReq : ProtoBase
---@field public ds_room_id number
---@field public sec_report_data string
---@field public client_start_time string

---@return pb_CSMatchRoomStartMatchTglogTReq
function pb.CSMatchRoomStartMatchTglogTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomStartMatchTglogTRes = {
    result = 0,
}
pb.__pb_CSMatchRoomStartMatchTglogTRes.__name = "CSMatchRoomStartMatchTglogTRes"
pb.__pb_CSMatchRoomStartMatchTglogTRes.__index = pb.__pb_CSMatchRoomStartMatchTglogTRes
pb.__pb_CSMatchRoomStartMatchTglogTRes.__pairs = __pb_pairs

pb.CSMatchRoomStartMatchTglogTRes = { __name = "CSMatchRoomStartMatchTglogTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomStartMatchTglogTRes : ProtoBase
---@field public result number

---@return pb_CSMatchRoomStartMatchTglogTRes
function pb.CSMatchRoomStartMatchTglogTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPlayerJoinDsStatusReq = {
    ds_room_id = 0,
    match_mode_id = 0,
    status = 0,
    fail_reason = 0,
    desc = "",
}
pb.__pb_CSPlayerJoinDsStatusReq.__name = "CSPlayerJoinDsStatusReq"
pb.__pb_CSPlayerJoinDsStatusReq.__index = pb.__pb_CSPlayerJoinDsStatusReq
pb.__pb_CSPlayerJoinDsStatusReq.__pairs = __pb_pairs

pb.CSPlayerJoinDsStatusReq = { __name = "CSPlayerJoinDsStatusReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPlayerJoinDsStatusReq : ProtoBase
---@field public ds_room_id number
---@field public match_mode_id number
---@field public status number
---@field public fail_reason number
---@field public desc string

---@return pb_CSPlayerJoinDsStatusReq
function pb.CSPlayerJoinDsStatusReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSPlayerJoinDsStatusRes = {
    result = 0,
}
pb.__pb_CSPlayerJoinDsStatusRes.__name = "CSPlayerJoinDsStatusRes"
pb.__pb_CSPlayerJoinDsStatusRes.__index = pb.__pb_CSPlayerJoinDsStatusRes
pb.__pb_CSPlayerJoinDsStatusRes.__pairs = __pb_pairs

pb.CSPlayerJoinDsStatusRes = { __name = "CSPlayerJoinDsStatusRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSPlayerJoinDsStatusRes : ProtoBase
---@field public result number

---@return pb_CSPlayerJoinDsStatusRes
function pb.CSPlayerJoinDsStatusRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSyncMemberOptionReq = {
    option_type = 0,
    ds_room_id = 0,
    data = "",
}
pb.__pb_CSMatchRoomSyncMemberOptionReq.__name = "CSMatchRoomSyncMemberOptionReq"
pb.__pb_CSMatchRoomSyncMemberOptionReq.__index = pb.__pb_CSMatchRoomSyncMemberOptionReq
pb.__pb_CSMatchRoomSyncMemberOptionReq.__pairs = __pb_pairs

pb.CSMatchRoomSyncMemberOptionReq = { __name = "CSMatchRoomSyncMemberOptionReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSyncMemberOptionReq : ProtoBase
---@field public option_type number
---@field public ds_room_id number
---@field public data string

---@return pb_CSMatchRoomSyncMemberOptionReq
function pb.CSMatchRoomSyncMemberOptionReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSyncMemberOptionRes = {
    result = 0,
}
pb.__pb_CSMatchRoomSyncMemberOptionRes.__name = "CSMatchRoomSyncMemberOptionRes"
pb.__pb_CSMatchRoomSyncMemberOptionRes.__index = pb.__pb_CSMatchRoomSyncMemberOptionRes
pb.__pb_CSMatchRoomSyncMemberOptionRes.__pairs = __pb_pairs

pb.CSMatchRoomSyncMemberOptionRes = { __name = "CSMatchRoomSyncMemberOptionRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSyncMemberOptionRes : ProtoBase
---@field public result number

---@return pb_CSMatchRoomSyncMemberOptionRes
function pb.CSMatchRoomSyncMemberOptionRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSyncMemberOptionNtf = {
    option_type = 0,
    ds_room_id = 0,
    member_id = 0,
    data = "",
}
pb.__pb_CSMatchRoomSyncMemberOptionNtf.__name = "CSMatchRoomSyncMemberOptionNtf"
pb.__pb_CSMatchRoomSyncMemberOptionNtf.__index = pb.__pb_CSMatchRoomSyncMemberOptionNtf
pb.__pb_CSMatchRoomSyncMemberOptionNtf.__pairs = __pb_pairs

pb.CSMatchRoomSyncMemberOptionNtf = { __name = "CSMatchRoomSyncMemberOptionNtf", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSyncMemberOptionNtf : ProtoBase
---@field public option_type number
---@field public ds_room_id number
---@field public member_id number
---@field public data string

---@return pb_CSMatchRoomSyncMemberOptionNtf
function pb.CSMatchRoomSyncMemberOptionNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomPickStageReconnectReq = {
    ds_room_id = 0,
}
pb.__pb_CSMatchRoomPickStageReconnectReq.__name = "CSMatchRoomPickStageReconnectReq"
pb.__pb_CSMatchRoomPickStageReconnectReq.__index = pb.__pb_CSMatchRoomPickStageReconnectReq
pb.__pb_CSMatchRoomPickStageReconnectReq.__pairs = __pb_pairs

pb.CSMatchRoomPickStageReconnectReq = { __name = "CSMatchRoomPickStageReconnectReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomPickStageReconnectReq : ProtoBase
---@field public ds_room_id number

---@return pb_CSMatchRoomPickStageReconnectReq
function pb.CSMatchRoomPickStageReconnectReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomPickStageReconnectRes = {
    result = 0,
    ds_room_id = 0,
    time_stamp = 0,
    player_id = 0,
    team_id = 0,
    random_seed = 0,
    player_idx = 0,
    game_mode = 0,
    iris_opening_event_id = 0,
    iris_event_course_id = 0,
    match_mode_id = 0,
    spawn_point_config_id = 0,
}
pb.__pb_CSMatchRoomPickStageReconnectRes.__name = "CSMatchRoomPickStageReconnectRes"
pb.__pb_CSMatchRoomPickStageReconnectRes.__index = pb.__pb_CSMatchRoomPickStageReconnectRes
pb.__pb_CSMatchRoomPickStageReconnectRes.__pairs = __pb_pairs

pb.CSMatchRoomPickStageReconnectRes = { __name = "CSMatchRoomPickStageReconnectRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomPickStageReconnectRes : ProtoBase
---@field public result number
---@field public ds_room_id number
---@field public time_stamp number
---@field public player_id number
---@field public team_id number
---@field public random_seed number
---@field public player_idx number
---@field public game_mode number
---@field public iris_opening_event_id number
---@field public iris_event_course_id number
---@field public room_member_infos pb_MatchRoomTeamMemberInfo[]
---@field public match_mode_id number
---@field public box_event number[]
---@field public spawn_point_config_id number
---@field public iris_event_ids number[]

---@return pb_CSMatchRoomPickStageReconnectRes
function pb.CSMatchRoomPickStageReconnectRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomGetPlayStationMatchIdTReq = {
    ds_room_id = 0,
}
pb.__pb_CSMatchRoomGetPlayStationMatchIdTReq.__name = "CSMatchRoomGetPlayStationMatchIdTReq"
pb.__pb_CSMatchRoomGetPlayStationMatchIdTReq.__index = pb.__pb_CSMatchRoomGetPlayStationMatchIdTReq
pb.__pb_CSMatchRoomGetPlayStationMatchIdTReq.__pairs = __pb_pairs

pb.CSMatchRoomGetPlayStationMatchIdTReq = { __name = "CSMatchRoomGetPlayStationMatchIdTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomGetPlayStationMatchIdTReq : ProtoBase
---@field public ds_room_id number

---@return pb_CSMatchRoomGetPlayStationMatchIdTReq
function pb.CSMatchRoomGetPlayStationMatchIdTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomGetPlayStationMatchIdTRes = {
    result = 0,
    ds_room_id = 0,
    match_id = "",
}
pb.__pb_CSMatchRoomGetPlayStationMatchIdTRes.__name = "CSMatchRoomGetPlayStationMatchIdTRes"
pb.__pb_CSMatchRoomGetPlayStationMatchIdTRes.__index = pb.__pb_CSMatchRoomGetPlayStationMatchIdTRes
pb.__pb_CSMatchRoomGetPlayStationMatchIdTRes.__pairs = __pb_pairs

pb.CSMatchRoomGetPlayStationMatchIdTRes = { __name = "CSMatchRoomGetPlayStationMatchIdTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomGetPlayStationMatchIdTRes : ProtoBase
---@field public result number
---@field public ds_room_id number
---@field public match_id string
---@field public players pb_MatchRoomPlayerPlatInfo[]

---@return pb_CSMatchRoomGetPlayStationMatchIdTRes
function pb.CSMatchRoomGetPlayStationMatchIdTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_MatchRoomPlayerPlatInfo = {
    player_id = 0,
    player_nick = "",
    plat_id = 0,
    camp = 0,
}
pb.__pb_MatchRoomPlayerPlatInfo.__name = "MatchRoomPlayerPlatInfo"
pb.__pb_MatchRoomPlayerPlatInfo.__index = pb.__pb_MatchRoomPlayerPlatInfo
pb.__pb_MatchRoomPlayerPlatInfo.__pairs = __pb_pairs

pb.MatchRoomPlayerPlatInfo = { __name = "MatchRoomPlayerPlatInfo", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_MatchRoomPlayerPlatInfo : ProtoBase
---@field public player_id number
---@field public player_nick string
---@field public plat_id number
---@field public camp number

---@return pb_MatchRoomPlayerPlatInfo
function pb.MatchRoomPlayerPlatInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSetPlayStationMatchIdTReq = {
    ds_room_id = 0,
    match_id = "",
}
pb.__pb_CSMatchRoomSetPlayStationMatchIdTReq.__name = "CSMatchRoomSetPlayStationMatchIdTReq"
pb.__pb_CSMatchRoomSetPlayStationMatchIdTReq.__index = pb.__pb_CSMatchRoomSetPlayStationMatchIdTReq
pb.__pb_CSMatchRoomSetPlayStationMatchIdTReq.__pairs = __pb_pairs

pb.CSMatchRoomSetPlayStationMatchIdTReq = { __name = "CSMatchRoomSetPlayStationMatchIdTReq", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSetPlayStationMatchIdTReq : ProtoBase
---@field public ds_room_id number
---@field public match_id string

---@return pb_CSMatchRoomSetPlayStationMatchIdTReq
function pb.CSMatchRoomSetPlayStationMatchIdTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSMatchRoomSetPlayStationMatchIdTRes = {
    result = 0,
}
pb.__pb_CSMatchRoomSetPlayStationMatchIdTRes.__name = "CSMatchRoomSetPlayStationMatchIdTRes"
pb.__pb_CSMatchRoomSetPlayStationMatchIdTRes.__index = pb.__pb_CSMatchRoomSetPlayStationMatchIdTRes
pb.__pb_CSMatchRoomSetPlayStationMatchIdTRes.__pairs = __pb_pairs

pb.CSMatchRoomSetPlayStationMatchIdTRes = { __name = "CSMatchRoomSetPlayStationMatchIdTRes", __service="matchroom", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSMatchRoomSetPlayStationMatchIdTRes : ProtoBase
---@field public result number

---@return pb_CSMatchRoomSetPlayStationMatchIdTRes
function pb.CSMatchRoomSetPlayStationMatchIdTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


