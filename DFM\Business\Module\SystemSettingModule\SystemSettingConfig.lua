----- <PERSON>O<PERSON> FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



require("DFM.Business.Proto.Pb.common_pb")

UITable[UIName2ID.SystemSettingMainView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingMainView",
    BPKey = "WBP_SetUp_Main",
    SubUIs = {
        UIName2ID.CommonMainTabLiveS2V1,
        UIName2ID.SystemSettingBasePanel,
        UIName2ID.SystemSettingSetMode,
        UIName2ID.SystemSettingControlPanel,
        UIName2ID.SystemSettingSensitityPanel,
        UIName2ID.SystemSettingSystemSettingPanel,
        UIName2ID.SystemSettingComminicatePanel,
        UIName2ID.SystemSettingDisplayPanel,
        UIName2ID.SystemSettingVehiclePanel,
        UIName2ID.SystemSettingLanguagePanel,
        UIName2ID.SystemSettingPrivacy,
        UIName2ID.SystemSettingOtherPanel,
        UIName2ID.SystemSettingLitePackagePanel,
    },
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
        IsPoolEnableLow = true,
    },
    IsReleaseClearRef = true,
    bEnableWorldRendering = true
}

UITable[UIName2ID.SystemSettingBasePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingBasePanel",
    BPKey = "WBP_SetUp_Basic",
    Anim = {
        FlowInAni = "WBP_SetUp_Basic_in",
        FlowOutAni = "WBP_SetUp_Basic_out"
    },
    SubUIs = {
        UIName2ID.SystemSettingBaseFightPanel,
        UIName2ID.SystemSettingBaseControlPanel,
    },
}

UITable[UIName2ID.SystemSettingSetMode] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingSetMode",
    BPKey = "WBP_SetUp_Mode",
    Anim = {
        FlowInAni = "Anima_in",
        FlowOutAni = "WBP_SetUp_Language_out"
    },
    SubUIs = {
        UIName2ID.MusicPlayerWidget,
    },
}

UITable[UIName2ID.SystemSettingControlPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingControlPanel",
    BPKey = "WBP_SetUp_Control",
}

UITable[UIName2ID.SystemSettingLitePackagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingLitePackagePanel",
    BPKey = "WBP_SetUp_GameResource",
    SubUIs = {
        UIName2ID.LitePackageNormalItem,
    },
}


UITable[UIName2ID.SystemSettingComminicatePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingComminicatePanel",
    BPKey = "WBP_SetUp_Comminicate",
    SubUIs = {
        UIName2ID.SettingQuickChatBtn,
        UIName2ID.UseChatBtn,
    },
}

UITable[UIName2ID.SystemSettingVehiclePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingVehicleBasePanel",
    BPKey = "WBP_SetUp_Vehicle",
        SubUIs = {
        UIName2ID.SystemSettingVehicleControl,
        UIName2ID.SystemSettingVehicleSensitivity,
        UIName2ID.SystemSettingVehicleGyroSensitivity,
    },
}

--[[ UITable[UIName2ID.SystemSettingVehicleBasePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingVehicleBasePanel",
    BPKey = "WBP_SetUp_Vehicle_Panel",
    SubUIs = {
        UIName2ID.SystemSettingVehicleControl,
        UIName2ID.SystemSettingVehicleSensitivity,
        UIName2ID.SystemSettingVehicleGyroSensitivity,
    },
} ]]

UITable[UIName2ID.SystemSettingVehicleControl] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingVehicleControl",
    BPKey = "WBP_SetUp_VehicleV1",
}

UITable[UIName2ID.SystemSettingVehicleSensitivity] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingVehicleSensitivity",
    BPKey = "WBP_SetUp_VehicleV2",
}

UITable[UIName2ID.SystemSettingVehicleGyroSensitivity] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingVehicleGyroSensitivity",
    BPKey = "WBP_SetUp_VehicleV3",
}

UITable[UIName2ID.SystemSettingSystemSettingPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingSystemSettingPanel",
    BPKey = "WBP_SetUp_Arrangement",
    SubUIs = {
        UIName2ID.SystemSettingPanelSortOrderCell,
        UIName2ID.SystemSettingPanelTypeCheckCell
    },
}

UITable[UIName2ID.SystemSettingPrivacy] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingPrivacy",
    BPKey = "WBP_SetUp_Privacy",
}

UITable[UIName2ID.SystemSettingPrivacyBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingPrivacyBtn",
    BPKey = "WBP_SetUp_PrivacyBtn",
}

UITable[UIName2ID.SystemSettingPanelSortOrderCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingPanelSortOrderCell",
    BPKey = "WBP_SetUpComponent_ArrangemenItem"
}

UITable[UIName2ID.SystemSettingPanelSortOrderCellHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingPanelSortOrderCellHD",
    BPKey = "WBP_SetUpComponent_ArrangemenItemHD"
}

UITable[UIName2ID.SystemSettingPanelSortPanelHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingPanelSortPanelHD",
    BPKey = "WBP_SetUp_Game_SortPanel"
}

UITable[UIName2ID.UseChatBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.UseChatBtn",
    BPKey = "WBP_SetUpComponent_QuickChatInformation1"
}

UITable[UIName2ID.SettingQuickChatBtn] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingQuickChatBtn",
    BPKey = "WBP_SetUpComponent_QuickChatInformation2"
}

UITable[UIName2ID.SystemSettingPanelTypeCheckCell] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingPanelTypeCheckCell",
    BPKey = "WBP_SetUpComponent_ArrangemenCheckItem"
}

UITable[UIName2ID.SystemSettingVolumePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingVolumePanel",
    BPKey = "WBP_SetUp_Sound"
}

UITable[UIName2ID.SystemSettingDisplayPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingDisplayPanel",
    BPKey = "WBP_SetUp_SoundPicture",
}

UITable[UIName2ID.SystemSettingSensitityPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingSensitityPanel",
    BPKey = "WBP_SetUp_Sensitivity",
}

UITable[UIName2ID.SystemSettingOtherPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingOtherPanel",
    BPKey = "SystemSetting_FrontEnd_Other"
}

UITable[UIName2ID.SystemSettingLanguagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingLanguagePanel",
    BPKey = "WBP_SetUp_Language_Panel",
    SubUIs = {
        UIName2ID.SystemSettingLanguageCell,
    }
}


UITable[UIName2ID.SettingSlider] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingSlider",
	BPKey = "WBP_SetUpComponent_SplitBtn"
}

UITable[UIName2ID.SettingBtnTwo] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingBtn",
    BPKey = "WBP_SetUpComponent_TwoBtn"
}

UITable[UIName2ID.SettingBtnTwoWithIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingBtn",
    BPKey = "WBP_SetUpComponent_TwoBtn_WithIcon"
}

UITable[UIName2ID.SettingBtnThree] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingBtn",
    BPKey = "WBP_SetUpComponent_ThreeBtnDragBox"
}

UITable[UIName2ID.SettingBtnFour] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingBtn",
    BPKey = "WBP_SetUpComponent_FourBtn"
}

UITable[UIName2ID.SettingBtnDrag] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.SettingBtn",
    BPKey = "WBP_SetUpComponent_DragSubBtn"
}

UITable[UIName2ID.LitePackageSettingItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Component.LitePackageSettingItem",
    BPKey = "WBP_SetUp_DownloadItem"
}

UITable[UIName2ID.SystemSettingButton] = {
	UILayer = EUILayer.HUD,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SystemSettingButton",
	BPKey = "WBP_ControllerButtonSetting",
    HUDName = "SystemSettingButton",
    ZOrderBase = EHUDBaseZorder.Touch,
    ZOrderOffset = 0,
}
--设置重置云端Pop界面
UITable[UIName2ID.SystemSettingResetPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingResetPanel",
    BPKey = "WBP_SetUp_PopWindow_Reset",
    IsModal = true,
    --- 默认Cache配置
    -- ReConfig = {
    --     IsPoolEnable = true,
    --     MaxPoolLength = 1,
    -- }
}

UITable[UIName2ID.SystemSettingSensitivityInstructions] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingSensitivityInstructions",
    BPKey = "WBP_SetUp_PopWindow_SensitivityInstructions_SetUp"
}

UITable[UIName2ID.SystemSettingResourceRepair] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingResourceRepair",
    BPKey = "WBP_SetUp_PopWindow_ResourceRepair",

}

UITable[UIName2ID.SystemSettingResourceRepairTitle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingResourceRepairTitle",
    BPKey = "WBP_SetUp_PopWindow_RepairTitle",

}

UITable[UIName2ID.SystemSettingCloudShare] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingCloudShare",
    BPKey = "WBP_SetUp_PopWindow_Share"
}

UITable[UIName2ID.SystemSettingCloudLoad] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingCloudLoad",
    BPKey = "WBP_SetUp_PopWindow_LoadScheme"
}

UITable[UIName2ID.SettingDesc] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SettingDesc",
    BPKey = "WBP_CommonDetailsTip"
}

UITable[UIName2ID.SystemSettingProgramManage] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingProgramManage",
    BPKey = "WBP_SetUp_PopWindow_ProgramManagement"
}

UITable[UIName2ID.SystemSettingCloudStorage] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingCloudStorage",
    BPKey = "WBP_SetUp_PopWindow_CloudStorage"
}

UITable[UIName2ID.SystemSettingCreditSystem] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingCreditSystem",
    BPKey = "WBP_SetUp_CreditSystem",
    IsModal = true,
}

UITable[UIName2ID.SetUpPop_Management_V1] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.SetUpPop_Management_V1",
    BPKey = "WBP_SetUpPop_Management_V1",

}



------------------------自定义布局
UITable[UIName2ID.CustomLayoutMain] = {
	UILayer = EUILayer.Stack,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutMain",
	BPKey = "WBP_CustomMain",
    SubUIs = {
        UIName2ID.CustomLayoutSOL,
        UIName2ID.CustomLayoutBattle,
    }
}

UITable[UIName2ID.VehicleLayoutMain] = {
	UILayer = EUILayer.Stack,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.VehicleLayoutMain",
	BPKey = "WBP_VehicleMain",
    SubUIs = {
        UIName2ID.VehicleLayoutBasePanel,
        UIName2ID.VehicleLayoutTank,
        UIName2ID.VehicleLayoutWeaponVehicleButton,
        UIName2ID.VehicleLayoutHelicopter,
    }
}

UITable[UIName2ID.CustomLayoutSOL] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutSOL",
	BPKey = "WBP_CustomSOL"
}

UITable[UIName2ID.CustomLayoutBattle] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutBattle",
	BPKey = "WBP_CustomBattle"
}

UITable[UIName2ID.CustomLayoutBasePanel] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutBasePanel",
	BPKey = "WBP_CustomBaseTemplate"
}

UITable[UIName2ID.CustomLayoutBG] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutBG",
	BPKey = "WBP_CustomBaseForBG"
}

UITable[UIName2ID.VehicleLayoutBasePanel] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.VehicleLayoutBasePanel",
	BPKey = "WBP_CustomVehicle_Custom"
}

UITable[UIName2ID.VehicleLayoutHelicopter] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.VehicleLayoutHelicopter",
	BPKey = "WBP_CustomVehicleHelicopter"
}

UITable[UIName2ID.VehicleLayoutJet] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.AerialVehicleLayoutBasePanel",
	BPKey = "WBP_CustomVehicleHelicopter"
}

UITable[UIName2ID.VehicleLayoutTank] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.VehicleLayoutTank",
	BPKey = "WBP_CustomVehicleTank"
}

UITable[UIName2ID.VehicleLayoutWeaponVehicleButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.VehicleLayoutWeaponVehicleButton",
    BPKey = "WBP_CustomWeaponVehicle"
}

UITable[UIName2ID.CustomLayoutAttachPanel] = {
	UILayer = EUILayer.Sub,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutAttachPanel",
	BPKey = "WBP_Custom_Edit"
}

UITable[UIName2ID.CustomLayoutLoadLayout] = {
	UILayer = EUILayer.Pop,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutLoadLayout",
	BPKey = "WBP_CustomLayoutLoadingWindow"
}

UITable[UIName2ID.CustomLayoutShareLayout] = {
	UILayer = EUILayer.Pop,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutShareLayout",
	BPKey = "WBP_CustomLayoutSharingWindow"
}

UITable[UIName2ID.CustomLayoutApplyLayout] = {
	UILayer = EUILayer.Pop,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutApplyLayout",
	BPKey = "WBP_CustomWindoApplyLayoutWindow_V1"
}

UITable[UIName2ID.CustomLayoutManeger] = {
	UILayer = EUILayer.Pop,
	LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutManeger",
	BPKey = "WBP_Custom_Maneger"
}

UITable[UIName2ID.CustomLayoutScheme] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutScheme",
    BPKey = "WBP_Custom_Scheme",
}

UITable[UIName2ID.CustomLayoutSchemeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutSchemeItem",
    BPKey = "WBP_Custom_SchemeItem",
}

UITable[UIName2ID.CustomLayoutSynchronization] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutSynchronization",
    BPKey = "WBP_Custom_Synchronization",
}

UITable[UIName2ID.CustomLayoutSchemeName] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.CustomLayout.CustomLayoutSchemeName",
    BPKey = "WBP_Custom_SchemeName",
}

-- DFHD Start
UITable[UIName2ID.SystemSettingHDEntrance] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDEntrance",
    BPKey = "WBP_SetUp_Entrance",
    Anim = {
        FlowInAni = "WBP_SetUp_Entrance_in",
        FlowOutAni = "WBP_SetUp_Entrance_out"
    },
    IsModal = true,
}

UITable[UIName2ID.SystemSettingHDMainView] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDMainView",
    BPKey = "WBP_SetUp_Main_SetUp",
    Anim = {
        FlowInAni = "WBP_SetUp_Main_in",
        FlowOutAni = "WBP_SetUp_Main_out"
    },
    SubUIs = {
        UIName2ID.SystemSettingHDGamePlayPanel,
        UIName2ID.SystemSettingHDInputPanel,
        UIName2ID.SystemSettingHDUIPanel,
        UIName2ID.SystemSettingHDVideoPanel,
        UIName2ID.SystemSettingHDRadioPanel,
        UIName2ID.SystemSettingHDSimpleDesc,
        UIName2ID.SystemSettingHDInputSensitivityPanel,
        UIName2ID.SystemSettingHDInputPanel,
        -- BEGIN MODIFICATION @LiDailong - VIRTUOS : support gamepad
        UIName2ID.SystemSettingHDInputSensitivityPanelGamepad,
        UIName2ID.SystemSettingConsoleUnlinkAccountDesc,
        -- END MODIFICATION
        UIName2ID.InputKeyFunctionGroup,
        UIName2ID.InputFunctionKeyItem,
        UIName2ID.InputFunctionKeyItemSub,
        UIName2ID.InputFunctionKeyItemSub_DefaultChangedTips,
        --BEGIN MODIFICATION TanXiaoLiang@VIRTUOS
        UIName2ID.SystemSettingHDInputControlPanel_Gamepad,
        UIName2ID.SystemSettingHDDropDownItem_Gamepad,
        UIName2ID.DisplayInputPreset_Gamepad,
        UIName2ID.DisplayInputPresetItem_Gamepad,
        UIName2ID.SystemSettingHDInputPanel_Gamepad,
        UIName2ID.InputKeyFunctionGroup_Gamepad,
        UIName2ID.InputFunctionKeyItem_Gamepad,
        --END MODIFICATION
        -- UIName2ID.SystemSettingHDLanguagePanel,
        UIName2ID.SystemSettingHDHitEffectDesc,
        UIName2ID.SystemSettingHDDropDownSubtextDesc,
        UIName2ID.SystemSettingHDPrivacyPanel,
        UIName2ID.SystemSettingHDDownloadCenterPanel,
    },
    ReConfig = {
        IsPoolEnable = true,
        IsPoolEnableLow = true,
    },
    IsReleaseClearRef = true,
    bEnableWorldRendering = true
}

UITable[UIName2ID.SystemSettingHDGamePlayPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDGamePlayPanel",
    BPKey = "WBP_SetUp_Game",
    SubUIs = {
        UIName2ID.SystemSettingPanelSortOrderCellHD,
        UIName2ID.SystemSettingPanelSortPanelHD
    }
}

UITable[UIName2ID.SystemSettingHDInputPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDInputPanel",
    BPKey = "WBP_SetUp_InputV2",
    SubUIs = {
        UIName2ID.SystemSettingHDKeySettingFunctionItemDesc
    }
}

UITable[UIName2ID.SystemSettingHDInputSensitivityPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDInputSensitivityPanel",
    BPKey = "WBP_SetUp_InputV1",
    SubUIs = {
        UIName2ID.SystemSettingHDADSItem,
        UIName2ID.SystemSettingHDMDVItem
    }
}

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : support gamepad
UITable[UIName2ID.SystemSettingHDInputSensitivityPanelGamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDInputSensitivityPanelGamepad",
    BPKey = "WBP_SetUp_InputV1_Gamepad",
    SubUIs = {
        UIName2ID.SystemSettingHDADSItem,
        UIName2ID.SystemSettingHDMDVItem
    }
}
-- END MODIFICATION

UITable[UIName2ID.SystemSettingHDUIPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDUIPanel",
    BPKey = "WBP_SetUp_UI"
}

UITable[UIName2ID.SystemSettingHDVRamPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDVramPanel",
    BPKey = "WBP_TestVRam"
}

UITable[UIName2ID.SystemSettingHDRadioPrompt] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDRadioPrompt",
    BPKey = "WBP_SetUpComponent_Prompt"
}

UITable[UIName2ID.SystemSettingHDVideoPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDVideoPanel",
    BPKey = "WBP_SetUp_Video",
    SubUIs = {
        UIName2ID.SystemSettingHDVRamPanel
    }
}

UITable[UIName2ID.SystemSettingHDRadioPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDRadioPanel",
    BPKey = "WBP_SetUp_Radio",
    SubUIs = {
        UIName2ID.SystemSettingHDRadioPrompt
    }
}

UITable[UIName2ID.BrightnessSettingHDPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Jump.BrightnessSettingHDPanel",
    BPKey = "WBP_SetUpPicture_Pc",

    Anim = {
        FlowInAni = "WBP_SetUpPicture_Pc_in",
        FlowOutAni = "WBP_SetUpPicture_Pc_out",
    }
}

UITable[UIName2ID.SystemSettingHDLanguagePanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDLanguagePanel",
    BPKey = "WBP_SetUp_Language_Pc"
}

UITable[UIName2ID.SystemSettingHDPrivacyPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDPrivacyPanel",
    BPKey = "WBP_SetUp_Privacy_Pc"
}

UITable[UIName2ID.SystemSettingHDDownloadCenterPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDDownloadCenterPanel",
    BPKey = "WBP_SetUp_GameResource_PC",
    SubUIs = {
        UIName2ID.SystemSettingHDDownloadCenterItem
    }
}

UITable[UIName2ID.SystemSettingHDDownloadCenterItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDDownloadCenterItem",
    BPKey = "WBP_SetUpComponent_ResourceList",
    SubUIs = {
        UIName2ID.SystemSettingHDExpansionPackItem
    }
}

UITable[UIName2ID.SystemSettingHDExpansionPackItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.SystemSettingHDExpansionPackItem",
    BPKey = "WBP_SetUp_PopWindow_DownLoad"
}

UITable[UIName2ID.InputKeyButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.InputKeyButton",
    BPKey = "WBP_SetUp_ButtonKeyChoice"
}

UITable[UIName2ID.InputFunctionKeyItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.InputFunctionKeyItem",
    BPKey = "WBP_SetUpComponent_FunctionKeyChoice"
}

UITable[UIName2ID.InputFunctionKeyItemSub] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.InputFunctionKeyItemSub",
    BPKey = "WBP_SetUpComponent_FunctionKeyChoiceItem"
}

UITable[UIName2ID.InputFunctionKeyItemSub_DefaultChangedTips] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.InputFunctionKeyItemSub_DefaultChangedTips",
    BPKey = "WBP_SetUpComponent_InputRevoke_V1"
}

UITable[UIName2ID.InputKeyFunctionGroup] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.InputKeyFunctionGroup",
    BPKey = "WBP_SetUpComponent_FunctionGroup"
}

--BEGIN MODIFICATION TanXiaoLiang@VIRTUOS - Gamepad Support
UITable[UIName2ID.SystemSettingHDInputPanel_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.SystemSettingHDInputPanel_Gamepad",
    BPKey = "WBP_SetUp_InputV2_Gamepad"
}

UITable[UIName2ID.InputKeyFunctionGroup_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.InputKeyFunctionGroup_Gamepad",
    -- 用PC的UI显示
    BPKey = "WBP_SetUpComponent_FunctionGroup"
}

UITable[UIName2ID.InputFunctionKeyItem_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.InputFunctionKeyItem_Gamepad",
    BPKey = "WBP_SetUpComponent_FunctionKeyChoice_Gamepad"
}

UITable[UIName2ID.InputKeyButton_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.InputKeyButton_Gamepad",
    BPKey = "WBP_SetUp_ButtonKeyChoice_Gamepad"
}

UITable[UIName2ID.KeyRebindingTips_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.KeyRebindingTips_Gamepad",
    BPKey = "WBP_SetUpComponent_KeyRebindingTips_Gamepad"
}

UITable[UIName2ID.SystemSettingHDInputControlPanel_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.SystemSettingHDInputControlPanel_Gamepad",
    BPKey = "WBP_SetUp_InputControl_Gamepad"
}

UITable[UIName2ID.SystemSettingHDDropDownItem_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.SystemSettingHDDropDownItem_Gamepad",
    BPKey = "WBP_SetUpComponent_DropDownItem_Gamepad"
}

UITable[UIName2ID.DisplayInputPreset_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.DisplayInputPreset_Gamepad",
    BPKey = "WBP_SetUp_DisplayInputPreset_Gamepad"
}

UITable[UIName2ID.DisplayInputPresetItem_Gamepad] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.DisplayInputPresetItem_Gamepad",
    BPKey = "WBP_SetUp_DisplayInputPresetItem_Gamepad"
}
--END MODIFICATION

UITable[UIName2ID.SystemSettingHDSliderItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDSliderItem",
    BPKey = "WBP_SetUpComponent_SplitBtn_2"
}

UITable[UIName2ID.SystemSettingHDADSItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDADSItem",
    BPKey = "WBP_SetUpComponent_SplitBtn_3"
}

UITable[UIName2ID.SystemSettingHDMDVItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDMDVItem",
    BPKey = "WBP_SetUpComponent_SplitBtn_3"
}

UITable[UIName2ID.SystemSettingHDPlayerVolumeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDPlayerVolumeItem",
    BPKey = "WBP_SetUpComponent_SplitBtn_3"
}

UITable[UIName2ID.SystemSettingHDSliderItem2] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDSliderItem",
    BPKey = "WBP_SetUpComponent_SplitBtn_3"
}

UITable[UIName2ID.SystemSettingHDTwoBtnItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDTwoBtnItem",
    BPKey = "WBP_SetUpComponent_TwoBtnHD"
}

UITable[UIName2ID.SystemSettingHDDropDownItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDDropDownItem",
    BPKey = "WBP_SetUpComponent_MultipleChoice"
}

UITable[UIName2ID.SystemSettingHDColorDropDownItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDDropDownItem",
    BPKey = "WBP_SetUpComponent_MultipleChoiceColor"
}

UITable[UIName2ID.SystemSettingHDSwitcherItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDSwitcherItem",
    BPKey = "WBP_SetUpComponent_Check"
}

UITable[UIName2ID.SystemSettingHDJumpItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.SystemSettingHDJumpItem",
    BPKey = "WBP_SetUpComponent_Jump_Pc"
}

UITable[UIName2ID.SystemSettingSecondLanguagePopView] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingSecondLanguagePopView",
    BPKey = "WBP_SetUp_PopWindow_Language_SetUp"
}

UITable[UIName2ID.SystemSettingHDSimpleDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDSimpleDesc",
    BPKey = "WBP_SetUpComponentSimpleDesc"
}

UITable[UIName2ID.SystemSettingHDOneParamDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDOneParamDesc",
    BPKey = "WBP_SetUpComponentOneParamDesc"
}

UITable[UIName2ID.SystemSettingHDHitEffectDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDHitEffectDesc",
    BPKey = "WBP_SetUpHitColor_Pc"
}

UITable[UIName2ID.SystemSettingHDDropDownSubtextDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDDropDownSubtextDesc",
    BPKey = "WBP_SetUpComponentDropDownSubtextDesc"
}

UITable[UIName2ID.SystemSettingHDKeySettingFunctionItemDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDKeySettingFunctionItemDesc",
    BPKey = "WBP_SetUpComponentKeySettingItemDesc"
}

UITable[UIName2ID.SystemSettingBaseControlPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingBaseControlPanel",
    BPKey = "WBP_SetUp_BasicControl"
}

UITable[UIName2ID.SystemSettingBaseFightPanel] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingBaseFightPanel",
    BPKey = "WBP_SetUp_BasicFighting"
}

UITable[UIName2ID.SystemSettingReportVoice] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingReportVoiceConfirm",
    BPKey = "WBP_VoiceReport",
    IsModal = true,
}

UITable[UIName2ID.SystemSettingHDCreditSystem] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDCreditSystem",
    BPKey = "WBP_SetUp_CreditSystem_Pc",
    IsModal = true,
}

UITable[UIName2ID.SystemSettingLitePackageCleanUpPanel] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Panel.SystemSettingLitePackageCleanUpPanel",
    BPKey = "WBP_SetUp_PopWindow_CleanUpResources",
    SubUIs = {
        UIName2ID.LitePackageNormalItem,
    },
}

UITable[UIName2ID.SystemSettingModePopWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingModePopWindow",
    BPKey = "WBP_SetUp_GameMode",
    SubUIs = {
        UIName2ID.SystemSettingModeSubItem,
    },
}

UITable[UIName2ID.SystemSettingModeSubItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingModeSubItem",
    BPKey = "WBP_SetUp_GameMode_Item",
}

UITable[UIName2ID.SystemSettingHDPersonalInformation] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDPersonalInformation",
    BPKey = "WBP_SetUpPop_Management_V1",
    IsModal = true,
}

UITable[UIName2ID.SystemSettingHDPrivacyHelp] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDPrivacyHelp",
    BPKey = "WBP_SetUpPop_Explain_V1",
    IsModal = true,
}

UITable[UIName2ID.SystemSettingPrivacyHelp] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SystemSettingPrivacyHelp",
    BPKey = "WBP_SetUpPop_Explain_V1",
    IsModal = true,
}

UITable[UIName2ID.SystemSettingHDDCountDownConfirmWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDDCountDownConfirmWindow",
    BPKey = "WBP_ConfirmWindow",
    IsModal = true,
}

UITable[UIName2ID.SetUpPopWindowThrowMode] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.SetUpPopWindowThrowMode",
    BPKey = "WBP_SetUp_PopWindow_ThrowMode",
}

UITable[UIName2ID.PopWindowThrowModeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.Pop.PopWindowThrowModeItem",
    BPKey = "WBP_SetUp_PopWindow_ThrowMode_Item",
}

UITable[UIName2ID.SystemSettingHDHeroDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDHeroDesc",
    BPKey = "WBP_SetUpComponentFull",
}

UITable[UIName2ID.SystemSettingHDHeroItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDHeroItem",
    BPKey = "WBP_SetUp_HeroItem",
}

UITable[UIName2ID.SystemSettingHDArmedForceItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDArmedForceGroupItem",
    BPKey = "WBP_SetUp_ListPop_Item",
    SubUIs = {
        UIName2ID.SystemSettingHDHeroItem
    },
}

UITable[UIName2ID.SystemSettingHDHeroListTopPanel] = {
    UILayer = EUILayer.Top,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDHeroListTopPanel",
    BPKey = "WBP_SetUp_ListPop",
    SubUIs = {
        UIName2ID.SystemSettingHDArmedForceItem
    },
    IsModal = true,
    ReConfig = {
        IsPoolEnable = true,
        MaxPoolLength = 1,
    },
    ZOrderOffset = 750,

}

-- BEGIN MODIFICATION @ VIRTUOS: Add bind account feature for XSX
UITable[UIName2ID.SystemSettingConsoleUnlinkAccountDesc] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingConsoleUnlinkAccountDesc",
    BPKey = "WBP_SetUpUnlinkAccount"
}
-- END MODIFICATION

UITable[UIName2ID.SystemSettingHDCloudSettingWindow] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDCloudSettingWindow",
    BPKey = "WBP_SetUp_PopWindow_ProgramManagementHD",
    IsModal = true,
}

UITable[UIName2ID.SystemSettingHDCloudSettingSlot] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Dynamic.SystemSettingHDCloudSettingSlot",
    BPKey = "WBP_SetUp_PopWindow_ProgramItemHD",
}

UITable[UIName2ID.SystemSettingHDCloudSettingChangeName] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.SystemSettingModule.UI.DFHD.Pop.SystemSettingHDCloudSettingChangeName",
    BPKey = "WBP_SetUp_PopWindow_SchemeNameHD",
    IsModal = true,
}

local SystemSettingConfig =
{
    Loc = {
        shareCodeCopyComplete = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ShareCodeCopyComplete", "复制成功，分享给朋友们吧"),
        checkShareCode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CheckShareCode", "请检查是否为20位分享码"),
        FireBtnCanNotBothHide = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_FireBtnCanNotBothHide", "左右开火键不能同时隐藏"),

        XinYongTips = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_XinYongTips", "由于您的腾讯游戏信用＜%s分，存在异常或不良游戏行为，%s。"),
        ChatLimitTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SayLimit", "由于您的腾讯游戏信用＜350分，存在异常或不良游戏行为，无法聊天发言"),
        AddFriendLimitTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_AddFriendLimit", "由于您的腾讯游戏信用＜350分，存在异常或不良游戏行为，无法添加好友"),
        unSetText = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_unSetText", "文本未设置"),
        RepairTitle = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RepairTitle", "客户端修复"),
        RepairBtnTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RepairBtnTxt", "修复"),
        RepairContent = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RepairContent", "删除游戏资源包和过往更新内容，恢复客户端到最初安装后的状态。"),
        RepairConfirm = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RepairConfirm", "该操作将检测损坏的资源文件并重新下载，请问是否确认修复？ \n *需要重启后生效"),
        Selected = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Selected", "已选 %s"),
        SecondLanguageIs = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SecondLanguageIs", "胜者为王第二语言：%s"),

        Credit = {
            Confirm = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Credit_Confirm", "确认"),
            ViewDetails = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Credit_ViewDetails", "查看详情"),
        },

        ModeSelect = {
            AlreadyIn = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ModeSelect_AlreadyIn", "已在该模式中"),
            Confirm = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ModeSelect_Confirm", "是否确认切换[%s]模式"),
            SOL = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Credit_SOL", "烽火地带"),
            MP = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Credit_MP", "全面战场"),
            Deny = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ModeSelect_Deny", "模式选择无效"),
        },

        Sensitity = {
            defaultDesc = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_DefaultDesc", "转向灵敏度"),
            normalAcc = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_NormalAcc", "镜头加速度值"),
            fireAcc = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_=FireAcc", "开火加速度值"),
        },
        ThrowMode = {
            IdletoThrowMode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_IdletoThrowMode", "手持"),
            DirectThrow = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_DirectThrow", "快投"),
        },

        NeedSelectResetPanel = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NeedSelectResetPanel", "请选择重置项"),
        ResetSuccess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ResetSuccess", "重置成功"),

        NotTextCloud = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NotTextCloud", "请输入方案名称"),

        Display = {
            quity = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Display_quity", "画面品质设置成功"),
            frame = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Display_frame", "帧数设置成功"),
            style = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Display_style", "画面风格设置成功"),
            quityNeedReset = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Display_quityNeedReset", "画面品质设置成功，部分效果重启后生效"),
        },

        Return = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Return", "返回"),

        Language ={
            Title = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LanguageTitle", "语言设置"),
            Confirm = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Language_Confirm_Btn", "确认"),
            Cancel = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Language_Cancel_Btn", "取消"),
            WeatherExchangeCurCultureTo = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Language_WeatherExchangeCurCultureTo", "是否将当前语言切换为 %s")
        },
        LanguageChangeSuccess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LanguageChangeSuccess", "语言切换成功。"),
        LanguageChangeFailed = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LanguageChangeFailed", "语言切换失败"),

        SettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Setting_Txt", "设置"),
        BaseSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Base_Setting_Txt", "基础设置"),
        ModelSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_ModelSetting_Txt", "模式切换"),
        ControlSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Control_Setting_Txt", "操作设置"),
        VeHicleSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_VeHicle_Setting_Txt", "载具设置"),
        SystemSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_System_Setting_Txt", "玩法设置"),
        DisplaySettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Display_Setting_Txt", "音画设置"),
        SoundSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Sound_Setting_Txt", "声音设置"),
        SensititySettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Sensitity_Setting_Txt", "灵敏度设置"),
        LanguageSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Language_Setting_Txt", "语言设置"),
        LitePackageSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Language_LitePackageSettingTxt", "下载中心"),
        PrivacySettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Language_PrivacySettingTxt", "隐私合规"),
        OtherSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Other_Setting_Txt", "开发者选项"),
        ComminicateSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Language_Comminicate_Txt", "局内交流设置"),

        CancleResetTxt = NSLOCTEXT("SystemSettingModule", "Lua_Cancle_Reset_Txt", "取消"),
        ResetTxt = NSLOCTEXT("SystemSettingModule", "Lua_Reset_Txt", "确认重置"),
        AskResetTxt = NSLOCTEXT("SystemSettingModule", "Lua_Ask_Reset_Txt", "是否重置%s？"),
        EnterTxt = NSLOCTEXT("SystemSettingModule", "Lua_Enter_Txt", "确认"),
        DleteFileTxt = NSLOCTEXT("SystemSettingModule", "Lua_Ask_DleteFileTxt", "是否删除资源包？"),

        TurnModuleTxt = NSLOCTEXT("SystemSettingModule", "Lua_Turn_Module_Txt", "转向模式"),
        GroySettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_Groy_Setting_Txt", "陀螺仪设置"),
        GlobalSensitityTxt = NSLOCTEXT("SystemSettingModule", "Lua_Global_Sensitity_Txt", "全局灵敏度"),
        GroySensitityTxt = NSLOCTEXT("SystemSettingModule", "Lua_Groy_Sensitity_Txt", "陀螺仪灵敏度"),
		SwipeSensitityTxt = NSLOCTEXT("SystemSettingModule", "Lua_Swipe_Sensitity_Txt", "滑屏灵敏度"),

		--自定义布局
        OtherSharedLayout = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_OtherSharedLayout", "<customstyle color=\"Color_Highlight02\">%s</>的分享"),
        Share = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_Share", "分享"),
        Save = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_Save", "保存"),
        NewShareCode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_NewShareCode", "<customstyle color=\"Color_Highlight02\">新的分享码</>会使旧的失效"),
        CancelShare = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_CancelShare", "取消"),
        GanerateNewShareCode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_GanerateNewShareCode", "生成新的分享码"),
        ApplyLayout = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_ApplyLayout", "应用"),
        ManagerLayout = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_ManagerLayout", "方案管理"),
        UnableLoad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_UnableLoad", "当前界面无法载入%s"),
        ShareLayoutName = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_ShareLayoutName", "<customstyle color=\"Color_Highlight02\">%s</>的分享码"),
        ApplyOtherLayout = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_ApplyOtherLayout", "将<customstyle color=\"Color_Highlight02\">%s</>的布局方案应用于\n"),
        Load = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Load", "载入"),
        Cancel = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Cancel", "取消"),
        CodeCopy = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CodeCopy", "复制代码"),

        YouRMatching = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_YouRMatching", "匹配中无法返回"),
        Tip = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Tip", "提示"),
        BtnQuitGame = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BtnQuitGame", "放弃战斗"),
        ReChallenge = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ReChallenge", "重新挑战"),
        QuitGame = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_QuitGame", "是否退出游戏"),
        CloudTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudTxt", "云端保存：%s"),
        CloudGUIDCopyTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudGUIDCopyTxt", "已复制分享码"),
        SettingPlan = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SettingPlan", "%s方案管理"),
        EnterCloud = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_EnterCloud", "确认上传<customstyle color=\"Color_Highlight01\"> %s </>到云端吗？"),
        HistoryCloud = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HistoryCloud", "历史%s方案：%s"),
        LoadSuccess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LoadSuccess", "载入成功"),
        NoCLoud = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NoCLoud", "历史%s方案：无"),
        CommincateNumTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CommincateNumTxt", "局内聊天快捷消息 (%s/8)"),
        Quit = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Quit", "战斗仍在继续，确认放弃战斗?"),
        ConfirmQuitGame = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ConfirmQuitGame", "{Quit}\n\n{DeductPoint}"),
        ConfirmQuitLogin = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ConfirmQuitLogin", "确定退出登录？"),
        ConfirmRechallenge = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ConfirmRechallenge", "确定重新挑战吗？"),
        DeductPoint = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_DeductPoint", "<dfmrichtext type=\"css\" font=\"Font'/Game/UI/Fonts/FZLTZHJW_ZH_Font.FZLTZHJW_ZH_Font'\">*频繁中退会扣除信誉分，低信誉等级将限制游戏权益</>"),
        ConfirmReturnToModeHall = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ConfirmReturnToModeHall", "确认返回模式大厅吗？"),
        PlayerSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_PlayerSettingTxt", "%s的%s"),
        NotSelect = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NotSelect", "暂未选择"),

        NewCloudSave = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Custom_NewCloudSave", "是否保存本地方案至<customstyle color=\"Color_Highlight02\">%s</>？\n\n *保存新的方案会覆盖历史方案"),

        SolMode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SolMode", "烽火地带"),
        RaidMode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RaidMode", "合作行动"),
        BattleFieldMode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BattleFieldMode", "全面战场经典"),
        BreakthroughMode = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BreakthroughMode", "全面战场"),

        ResetDun = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ResetDun", "，"),

        LayoutNameTable = {
            ["BaseLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BaseLayout", "默认的基础布局"),
            ["VehicleLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_VehicleLayout", "默认的载具布局"),
            ["SOLLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SOLLayout", "危险行动"),
            ["RaidLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RaidLayout", "合作行动"),
            ["ClassLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ClassLayout", "全面战场经典"),
            ["BattleLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BattleLayout", "全面战场攻防"),
            ["ButtonStyle"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ButtonStyle", "按键控制"),
            ["GliderStyle"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GliderStyle", "滑杆控制"),
            ["JoyStickStyle"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_JoyStickStyle", "摇杆控制"),
            ["TankLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_TankLayout", "战斗载具-坦克操作"),
            ["HelicopterLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HelicopterLayout", "战斗载具-直升机操作"),
            ["WeaponVehicleButtonLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_WeaponVehicleButtonLayout", "战斗载具-四键操作"),
        },

        -- DFHD Start
        HDEntrance = {
            leaveBattle = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDEntrance_leaveBattle", "战斗仍在继续，确认放弃战斗？"),
            quitGame = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDEntrance_exitGame", "您确认要退回桌面？确认将导致游戏关闭。"),
            cancel = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDEntrance_cancel", "取消"),
            confirm = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDEntrance_confirm", "确认")
        },

        HDSettingTab = {
            GamePlaySettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_GamePlaySetting_Txt", "游戏"),
            InputSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSetting_Txt", "键盘&鼠标"),
            InputSettingSensitivityTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingSensitivity_Txt", "鼠标灵敏度"),
            -- BEGIN MODIFICATION @LiDailong - VIRTUOS : support gamepad
            InputSettingTxt_Gamepad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_GamepadSetting_Txt", "控制器"),
            InputSettingGamepadSensitivityTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingGamepadSensitivity_Txt", "控制器灵敏度"),
            -- END MODIFICATION
            InputSetting3CTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSetting3C_Txt", "操控&战斗"),
            InputSettingUITxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingUI_Txt", "界面&交互"),
            InputSettingVehicleTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingVehicle_Txt", "载具"),
            InputSettingAimingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingAiming_Txt", "瞄准"),
            -- BEGIN MODIFICATION TanXiaoLiang@VIRTUOS
            InputSettingControlTxt_Gamepad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingControl_Txt", "操控设置"),
            InputSettingControl_FootTitleTxt_Gamepad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingControl_FootTitleTxt", "徒步"),
            InputSettingControl_VehicleTitleTxt_Gamepad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingControl_VehicleTitleTxt", "地面载具"),
            InputSettingControl_HelicopterTitleTxt_Gamepad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingControl_HelicopterTitleTxt", "空中载具"),
            InputSettingControl_CustomInputPresetTxt_Gamepad = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_InputSettingControl_CustomInputPreset_Txt", "自定义"),
            -- END MODIFICATION
            UISettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_UISetting_Txt", "界面"),
            VideoSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_VideoSetting_Txt", "视频"),
            RadioSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_RadioSetting_Txt", "音频"),
            PrivacySettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_PrivacySetting_Txt", "隐私合规"),
            LanguageSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_Language_Txt", "语言"),
            DownloadCenterTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSettingTab_DownloadCenter_Txt", "下载中心")
        },

        HDSetting = {
            HDSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HD_Txt", "设置"),
            ReturnTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ReturnTxt", "返回"),
            ResetTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetTxt", "重置"),
            ApplyTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ApplyTxt", "应用"),
            ApplyingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ApplyingTxt", "应用中"),
            UnbindTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_UnbindTxt", "解绑"),
            InputModeSwitchTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_Switch", "切换"),
            InputModeHoldTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_Hold", "按住"),
            InputModeMixTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_Mix", "混合"),
            InputModeUnfoldTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_Unfold", "展开"),
            InputModeFoldTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_Fold", "折叠"),
            ThrownDefaultTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ThrownDefault", "默认"),
            ThrownDefaultFixTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ThrownDefaultFix", "默认（%s）"),
            ThrownInHandTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ThrownInHand", "手持"),
            ThrownQuickTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ThrownQuick", "快投"),
            ResetInputSensitivityTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetInputSensitivity", "确认重置所有<customstyle color=\"Color_Highlight01\">鼠标灵敏度设置</>吗？"),
            -- BEGIN MODIFICATION @LiDailong - VIRTUOS : support gamepad
            ResetGamepadInputSensitivityTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetGamepadInputSensitivity", "确认重置所有<customstyle color=\"Color_Highlight01\">控制器灵敏度设置</>吗？"),
            GamepadVoiceButtonTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadVoiceButton", "长按View(Xbox)/Touch pad button(PS)"),
            GamepadVoiceButtonTxt_XSX = NSLOCTEXT("SystemSettingModule", "MicrophoneButtonType_XSX", "长按“视图”按钮说话"),
            GamepadVoiceButtonTxt_Sony = NSLOCTEXT("SystemSettingModule", "MicrophoneButtonType_Sony", "长按触摸板键说话"),
            ResetControlSettingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetControlSetting", "确认重置所有<customstyle color=\"Color_Highlight01\">操控设置</>吗？"),
            -- END MODIFICATION
            ResetInput3CTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetInput3C", "确认重置所有<customstyle color=\"Color_Highlight01\">操控&战斗按键设置</>吗？"),
            ResetInputUITxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetInputUI", "确认重置所有<customstyle color=\"Color_Highlight01\">界面&交互按键设置</>吗？"),
            ResetInputVehicleTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetInputVehicle", "确认重置所有<customstyle color=\"Color_Highlight01\">载具按键设置</>吗？"),
            ResetInputAimingTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetInputAiming", "确认重置所有<customstyle color=\"Color_Highlight01\">瞄准按键设置</>吗？"),
            ResetInputGMTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetInputGM", "确认重置所有<customstyle color=\"Color_Highlight01\">GM按键设置</>吗？"),
            ApplyGlobalTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ApplyGlobalTxt", "确认此设置页内容应用全局设置，清除该干员专属设置吗？"),
            ResetGamePlayTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetGamePlay", "确认重置所有<customstyle color=\"Color_Highlight01\">游戏设置</>吗？"),
            ResetUITxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetUI", "确认重置所有<customstyle color=\"Color_Highlight01\">界面设置</>吗？"),
            ResetVideoTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetVideo", "确认重置所有<customstyle color=\"Color_Highlight01\">视频设置</>吗？"),
            ResetRadioTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetRadio", "确认重置所有<customstyle color=\"Color_Highlight01\">音频设置</>吗？"),
            ResetPrivacyTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResetPrivacy", "确认重置所有<customstyle color=\"Color_Highlight01\">隐私设置</>吗？"),
            ImpConfirmConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ImpConfirmConfirm", "确认绑定"),
            ImpConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ImpConfirm", "%s 已被“%s”绑定，仍要绑定吗？"),
            KeyBindChangeTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_KeyBindChange", "从 %s 变为 %s"),
            KeyBindConflictTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_KeyBindConflict", "已被 %s 绑定"),
            KeyBindAlreadyBoundTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_KeyBindAlreadyBound", "已绑定该键位"),
            AxisKeyBindAlreadyBoundTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_AxisKeyBindAlreadyBound", "已绑定该轴输入，如需反向绑定请先解绑"),
            KeyEnterNotAvaliableTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_KeyEnterNotAvaliable", "[Enter]键已被聊天功能使用，无法设为其他功能的按键"),
            RelativeBindTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_RelativeBind", "相关："),
            RelativeBindToTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_RelativeBindTo", "绑定："),
            OrTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_Or", "/ %s"),
            AskConfirmVideoTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_AskConfirmVideoTxt", "视频设置已变更，是否要应用设置？"),
            CancelConfirmVideoTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_CancelConfirmVideoTxt", "不，取消"),
            ConfirmVideoTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ConfirmVideoTxt", "应用"),
            KeySettingItemConflictTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_KeySettingItemConflict", "该功能使用的按键同时绑定了以下功能："),
            DUNHAOTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DUNHAO", "、%s"),
            KeyConflictWithTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_KeyConflictWith", "与“%s”冲突，已自动解绑"),

            AskConfirmUploadTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_AskConfirmUploadTxt", "    出于优化游戏产品性能、提升您的游戏体验，我们将收集您的设备名称、设备类型、型号和版本、系统版本、IP地址、设备识别符（如MAC地址）、设备软件版本信息、网络类型、网络使用习惯、产品崩溃信息等信息，并可能会记录一些我们认为有风险的链接信息；以进一步分析并排查可能存在的系统异常、系统风险。\n    若您允许我们获取上述信息用于游戏体验优化，点击“同意上传”即可相关信息发送至官方；若您不允许我们获取上述信息，点击“拒绝上传”即可关闭此界面。"),
            CancelConfirmUploadTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_CancelConfirmUploadTxt", "取消"),
            ConfirmUploadTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ConfirmUploadTxt", "上传"),
            UploadSuccessTips = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_UploadSuccessTxt", "上传成功"),
            UploadFailTips = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_UploadFailTxt", "上传失败"),

            PostCultureRebootTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_PostCultureRebootTxt", "切换语言后部分资源需要重新登录游戏后生效，是否确认？"),
            PostCultureRebootConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_PostCultureRebootConfirmTxt", "确认"),
            PostCultureRebootCancelTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_PostCultureRebootCancelTxt", "取消"),
            RayTracingRebootTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_RayTracingRebootTxt", "光线追踪特性在游戏重启前不生效。"),
            RayTracingRebootConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_RayTracingRebootConfirmTxt", "确认"),
            DeleteAccountTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DeleteAccountTxt", "您即将退出账号并进入账号注销流程。账号成功注销后，该账号下全平台的个人信息及游戏相关数据将被删除或匿名化处理，且无法恢复。请确认是否继续?"),
            DeleteAccountCancelTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DeleteAccountCancelTxt", "取消"),
            DeleteAccountConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DeleteAccountConfirmTxt", "确认"),
            ScopeUseRTTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ScopeUseRTTxt", "该功能开启后将有较大的性能开销，请谨慎开启。"),
            ScopeUseRTConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ScopeUseRTConfirmTxt", "确认开启"),
            ScopeUseRTCancelTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ScopeUseRTCancelTxt", "不开启"),

            PrivacyHelpConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_PrivacyHelpConfirmTxt", "确认"),
            OverheatWarningTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_OverheatWarningTxt", "您的电脑温度过高，建议调整<customstyle color=\"Color_Highlight01\">帧数上限</>以提高游戏稳定性。"),

            EstimatedVRamTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_EstimatedVRamTxt", "%.2f/%.2f GB"),
            GameVRamTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GameVRamTxt", "三角洲行动：(%.2f/%.2f GB)"),
            OtherVRamTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_OtherVRamTxt", "其他应用：%.2f GB"),

            WithoutACLineWarningTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_WithoutACLineWarningTxt", "为保证最佳游戏体验，请插入电源。"),

            DisplayModesChangeTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DisplayModesChangeTxt", "是否保留这些显示设置：显示器/显示模式/分辨率/屏幕刷新率？"),
            DisplayModesCountDownTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DisplayModesCountDownTxt", "在%d秒内还原为以前的显示设置。"),
            DisplayModesReserveTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DisplayModesReserveTxt", "保留更改"),
            DisplayModesRecoverTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_DisplayModesRecoverTxt", "恢复"),

            VRamTipTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_VRamTargetTipTxt", "增加显存的使用可以提升游戏性能，但如果没有足够的显存供其他应用程序使用，可能会导致游戏卡顿。"),

            SwitchListeningChanelAllTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_SwitchListeningChanelAllTxt", "正处于全队语音频道，无法关闭语音收听"),
            SwitchListeningChanelTeamTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_SwitchListeningChanelTeamTxt", "正处于组队语音频道，无法关闭语音收听"),

            ResizableBarWarningTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResizableBarWarningTxt", "建议进入BIOS设置并启用Resizable Bar功能，这将有助于提升显卡性能表现，带来更流畅的体验。"),
            ResizableBarConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResizableBarConfirmTxt", "确认"),
            ResizableBarTileTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_ResizableBarTitleTxt", "Resizable Bar启用提示"),

            RecompilePSOTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_RecompilePSOTxt", "着色器将在游戏重启后进行重新预热。"),
            RecompilePSOConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_RecompilePSOConfirmTxt", "确定"),
            SettingConflictTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_SettingConflictTxt", "检测到您在云端和本地的设置存在差异，请选择您希望使用的设置来源。"),
            SettingConflictUseRemoteTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_SettingConflictUseRemoteTxt", "使用云端设置"),
            SettingConflictUseLocalTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_SettingConflictUseLocalTxt", "使用本地设置"),

            GamepadIMCBaseInputTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadIMCBaseInputTxt", "徒步"),
            GamepadIMCVehicleInputTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadIMCVehicleInputTxt", "地面载具"),
            GamepadIMCHelicopterInputTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadIMCHelicopterInputTxt", "空中载具"),
            GamepadPresetDisplayNameDefaultTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadPresetDisplayNameDefaultTxt", "默认预设"),
            GamepadPresetDisplayNameLeftHandTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadPresetDisplayNameLeftHandTxt", "左利手预设"),
            GamepadPresetDisplayNameTraditionalTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GamepadPresetDisplayNameTraditionalTxt", "传统预设"),

            HeroSpecificInput = {
                GlobalNickName = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalNickName", "全局"),
                GlobalTitle = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalTitle", "会应用于未配置专属方案的全部干员"),
                ApplyGlobal = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ApplyGlobal", "为该干员应用全局配置方案，是否确认？"),
                Reset = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ResetHeroInput", "为该干员应用默认配置方案，是否确认？"),
            },

            GPUCrashDebuggingRebootTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GPUCrashDebuggingRebootTxt", "GPU调试模式在游戏重启前不生效。"),
            GPUCrashDebuggingRebootConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GPUCrashDebuggingRebootConfirmTxt", "确认"),

            GenericRebootTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GenericRebootTxt", "选项在游戏重启前不生效。"),
            GenericRebootConfirmTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDSetting_GenericRebootConfirmTxt", "确认"),

            CloudSettingTxt = {
                Uploading = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudSaveUploading", "保存至云端..."),
                UploadSuccess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudSaveUploadSuccess", "已保存"),
                UploadFail = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudSaveUploadFail", "保存失败"),
                AutoUploadTip = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_AutoUploadTip", "勾选后，修改设置会自动上传覆盖当前应用中的设置项存档。"),
                AutoNamePrefix = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_AutoNamePrefix", "自定义方案"),
                LocalNameDefault = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LocalNameDefault", "本地方案 "),
                NameEmptyTip = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NameEmptyTip", "方案名称不能为空"),
                NameHasInvalidChar = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NameHasInvalidChar", "方案名含有生僻字符，请修改"),
                NameFail = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_NameFail", "修改方案名称失败"),
                ConfirmDelete = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ConfirmDelete", "确认删除方案？"),
                ConfirmSave = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ConfirmSave", "确认保存方案？"),
                CloudSettingSave = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudSettingSave", "保存"),
                CloudSettingAutoSaving = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CloudSettingAutoSaving", "自动保存中"),
            },
        },
        -- DFHD End

        -- BEGIN MODIFICATION - VIRTUOS
        CrossPlaySwitchEnableTxt =  NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CrossPlaySwitchEnableTxt", "跨平台游玩已开启"),
        CrossPlaySwitchDisableTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CrossPlaySwitchDisableTxt", "跨平台游玩已关闭"),
        UnableToModifyCrossPlaySwicthTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UnableToModifyCrossPlaySwicthTxt", "当前状态无法修改跨平台游玩设置"),
        -- END MODIFICATION - VIRTUOS
        CustomLayoutSyncTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CustomLayoutSyncTxt", "<customstyle color=\"Color_Highlight02\">%s</>本地方案同步"),
        CustomLayoutSyncTipTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CustomLayoutSyncTipTxt", "是否同步以下核心布局至<customstyle color=\"Color_Highlight02\">%s</>模式"),

        SaveCustomLayoutTips = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SaveCustomLayoutTips", "当前布局未保存，是否退出"),
        LoadCustomLayoutSuccess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LoadCustomLayoutSuccess", "加载布局成功"),
        SyncLondCustom = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SyncLondCustom", "同步"),
        SaveCustomSchemeName = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SaveCustomSchemeName", "上传"),
        SaveCustomSchemeSuccess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SaveCustomSchemeSuccess", "保存预设成功"),
        OthersShare = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_OthersShare", "<customstyle color=\"Color_Highlight02\">%s</>的分享"),
        checkMaxQuality = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_checkMaxQuality", "高画质无法支持当前的帧率，自动帮你切换到合适的帧率"),
        checkMaxFPS = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_checkMaxFPS", "高帧率无法支持当前的画质，自动帮你切换到合适的画质"),
        checkRefreshRateSupport = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_checkRefreshRateSupport", "需要手动开启高刷支持"),
        inSufficientFPS = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_inSufficientFPS", "为了保证游玩体验，本次修改会自动降低帧率至合适水平，是否继续？"),
        confirmBtnText = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_confirmBtnText", "确定"),
        cancelBtnText = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_cancelBtnText", "取消"),
        fPSChangeSucess = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_fPSChangeSucess", "帧数修改成功"),
        UseCommunicateMax = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UseCommunicateMax", "局内交流条目已达上限"),
        RemakeGameConfig = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RemakeGameConfig", "重启游戏启用适配设置"),
        QuitLogin = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_QuitLogin", "退出登录"),
        LeaveCollectionRoom = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LeaveCollectionRoom", "离开收藏室"),
        LeaveRange = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LeaveRange", "离开靶场"),
        DolbySupport = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_DolbySupport", "需要设备支持杜比全景声"),
        PlayQuitGame = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_PlayQuitGame", "退出游戏"),
        SystemTipsTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SystemTipsTxt", "需要将【%s】设为【%s】"),
        Setting1Txt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Setting1Txt", "一键开镜开火"),
        Setting2Txt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Setting2Txt", "全部关闭"),
        Setting3Txt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Setting3Txt", "全部开启"),
        Setting4Txt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Setting4Txt", "开镜模式"),
        Setting5Txt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Setting5Txt", "点击开镜"),
        Setting6Txt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Setting6Txt", "自定义"),
        CustomSchemeTitle = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CustomSchemeTitle", "预设"),
        LocalCustomTitle = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_LocalCustomTitle", "本地方案"),

        AllCustomAirVehicleTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_AllCustomAirVehicleTxt", "全部空中载具"),
        AllCustomGroundVehicleTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_AllCustomGroundVehicleTxt", "全部地面载具"),
        CustomSchemeTipTxt = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_CustomSchemeTipTxt", "可以将本地方案上传储存至云端，也可以将已上传的云端方案载入至本地使用或分享给其他玩家"),

        CustomImageTbl = {
            ["SOLLayout"] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Sync_Icon_0001.Common_Sync_Icon_0001'",
            ["BattleLayout"] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Sync_Icon_0002.Common_Sync_Icon_0002'",
            ["ButtonStyle"] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Sync_Icon_0004.Common_Sync_Icon_0004'",
            ["GliderStyle"] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Sync_Icon_0004.Common_Sync_Icon_0004'",
            ["JoyStickStyle"] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Sync_Icon_0004.Common_Sync_Icon_0004'",
            ["HelicopterLayout"] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Sync_Icon_0005.Common_Sync_Icon_0005'",
        },

        LayoutImageTxtTbl = {
            ["SOLLayout"] = "<dfmrichtext type=\"img\" id=\"SyncSol\"/>",
            ["BattleLayout"] = "<dfmrichtext type=\"img\" id=\"SyncMp\"/>",
            ["ButtonStyle"] = "<dfmrichtext type=\"img\" id=\"SyncIcon1\"/>",
            ["GliderStyle"] = "<dfmrichtext type=\"img\" id=\"SyncIcon1\"/>",
            ["JoyStickStyle"] = "<dfmrichtext type=\"img\" id=\"SyncIcon1\"/>",
            ["HelicopterLayout"] = "<dfmrichtext type=\"img\" id=\"SyncIcon2\"/>",
            ["JetGliderStyle"] = "<dfmrichtext type=\"img\" id=\"SyncIcon2\"/>",
            ["JetJoyStickStyle"] = "<dfmrichtext type=\"img\" id=\"SyncIcon2\"/>",
        },

        SensitityTxtTbl = {
            [0] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt1", "机瞄、红点、全息"),
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt2", "2倍镜"),
            [2] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt3", "3倍镜"),
            [3] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt4", "4倍镜"),
            [4] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt5", "5倍镜"),
            [5] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt6", "6倍镜"),
            [6] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt7", "7倍镜"),
            [7] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt8", "8倍镜"),
            [8] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt9", "9倍镜"),
            [9] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt10", "10倍镜"),
            [10] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt11", "11倍镜"),
            [11] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SensitityTxt12", ">=12倍镜"),
        },




        SettingMiaoFire = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SettingMiaoFire", "瞄准和射击"),

        VehicleSensitityTxtTbl = {
            [0] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_VehicleSensitityTxt1", "载具武器FPP灵敏度"),
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_VehicleSensitityTxt2", "载具武器瞄准灵敏度"),
        },

        UserProtoTxtTbl = {
            UserInfoManagement = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserInfoManagement", "个人信息管理"),
            PrivacyAuthorityManagement = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_PrivacyAuthorityManagement", "隐私权限管理"),
            TencentProto = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserProtoTxt1", "腾讯游戏用户协议"),
            PrivacyDefend = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserProtoTxt2", "隐私保护"),
            ChildProvacyDefend = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserProtoTxt4", "儿童隐私保护"),
            ThirdParty = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserProtoTxt5", "第三方信息共享"),
            InformationDel = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserProtoTxt6", "账号注销及个人信息删除"),
            Other = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserProtoTxt7", "粤B2-20090059-3477A"),
            PrivacyHelper = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_PrivacyHelper", "隐私小助手"),
            TencentCredit = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_TencentCredit", "腾讯游戏信用"),
            PrivacyPolicy = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_PrivacyPolicy", "隐私政策"),
            UserAgreement = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UserAgreement", "用户协议"),
            PersonalList = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_PersonalList", "个人信息收集清单"),

            -- BEGIN MODIFICATION @ VIRTUOS: Add bind account feature for XSX
            BindAccount = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BindAccount", "账号绑定"),
            UnbindAccount = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_UnbindAccount", "账号解绑"),
            -- END MODIFICATION
        },
        --载具滑屏灵敏度
        VehicleSliderSensitity = {
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_ThirdPersonCam", "第三人称镜头灵敏度"),
            [2] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_FirstPersonCam", "第一人称镜头灵敏度"),
            [3] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_AimSensitivity", "瞄准灵敏度缩放系数"),
            [4] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_ThirdPersonFire", "第三人称开火灵敏度"),
            [5] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_FirstPersonFire", "第一人称开火灵敏度"),
            [6] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_CamMDV", "镜头MDV"),
        },

        --载具滑屏灵敏度(炮手)
        PassengerSliderSensitity = {
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_FirstPersonCam", "第一人称镜头灵敏度"),
            [2] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_AimSensitivity", "瞄准灵敏度缩放系数"),
            [3] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_FirstPersonFire", "第一人称开火灵敏度"),
            [4] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_CamMDV", "镜头MDV"),
        },

        --载具系数
        VehicleRate = {
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_Rate1-2", "1-2倍"),
            [2] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_Rate2-3", "2-3倍"),
            [3] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_Rate3-4", "3-4倍"),
            [4] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_Rate4", "4倍"),
        },


        --载具瞄准系数
        VehicleAimSensitity = {
            MagnifyLevel1 = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_MagnifyLevel1", "1-2倍"),
            MagnifyLevel2 = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_MagnifyLevel2", "2-3倍"),
            MagnifyLevel3 = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_MagnifyLevel3", "3-4倍"),
            MagnifyLevel4 = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_Sensitity_MagnifyLevel4", "4倍"),
        },
        --全局灵敏度
        GlobalSensititySlider = {
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider1", "总滑屏灵敏度"),
            [2] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider2", "滑屏MDV"),
            [3] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider3", "总滑屏垂直灵敏度"),
            [4] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider4", "总滑屏水平灵敏度"),
            [5] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider5", "总开火灵敏度"),
            [6] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider6", "滑屏开火MDV"),
            [7] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider7", "总开火垂直灵敏度"),
            [8] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalSensititySlider8", "总开火水平灵敏度"),
        },

        --全局陀螺仪灵敏度
        GlobalGyroSensititySlider = {
            [1] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider1", "陀螺仪灵敏度"),
            [2] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider2", "陀螺仪MDV"),
            [3] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider3", "陀螺仪垂直灵敏度"),
            [4] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider4", "陀螺仪水平灵敏度"),
            [5] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider5", "陀螺仪开火灵敏度"),
            [6] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider6", "陀螺仪开火MDV"),
            [7] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider7", "陀螺仪开火垂直灵敏度"),
            [8] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GlobalGyroSensititySlider8", "陀螺仪开火水平灵敏度"),
        },
    },

    Event = {
        evtSliderValueChange = LuaEvent:NewIns("evtSliderValueChange"),
        evtGlobalSensitivitySliderValueChange = LuaEvent:NewIns("evtGlobalSensitivitySliderValueChange"),
        evtLanguageCellSelected = LuaEvent:NewIns("evtLanguageCellSelected"),
        evtFunctionBtnRotationModeChange = LuaEvent:NewIns("evtFunctionBtnRotationModeChange"),
        evtSensitityValueChange = LuaEvent:NewIns("evtSensitityValueChange"),
        evtSettingSliderValueChange = LuaEvent:NewIns("evtSettingSliderValueChange"),

        evtAttachNewDragBtn = LuaEvent:NewIns("evtAttachNewDragBtn"),
        evtVehicleModeSwitch = LuaEvent:NewIns("evtVehicleModeSwitch"),

        evtSettingBtnClick = LuaEvent:NewIns("evtSettingBtnClick"),

        evtResetSystemSetting = LuaEvent:NewIns("evtResetSystemSetting"),
        evtLoadSchemeData = LuaEvent:NewIns("evtLoadSchemeData"),
        evtCloseSchemePanel = LuaEvent:NewIns("evtCloseSchemePanel"),

        --载具模式改变
        evtVehicleModeChange = LuaEvent:NewIns("evtVehicleModeChange"),
        evtWeaponVehicleModeChange = LuaEvent:NewIns("evtWeaponVehicleModeChange"),
        evtSystemSettingMainUIOpen = LuaEvent:NewIns("evtSystemSettingMainUIOpen"),
        evtSystemSettingMainUIClose = LuaEvent:NewIns("evtSystemSettingMainUIClose"),

        -- HD
        evtPendingSettingChangedHD = LuaEvent:NewIns("evtPendingSettingChangedHD"),
        -- id, value
        evtSettingValueChangedHD = LuaEvent:NewIns("evtSettingValueChangedHD"),
        -- id, value
        evtPreSettingValueChangedHD = LuaEvent:NewIns("evtPreSettingValueChangedHD"),

        evtSystemSettingHDEntraceOnShowBegin = LuaEvent:NewIns("evtEscShowBeginHD"),
        evtSystemSettingHDEntranceOnHideBegin = LuaEvent:NewIns("evtEscHideHD"),
        evtSettingMainShowBeginHD = LuaEvent:NewIns("evtSettingMainShowBeginHD"),
        evtSettingMainHideHD = LuaEvent:NewIns("evtSettingMainHideHD"),

        evtSettingBrightnessShowHD = LuaEvent:NewIns("evtSettingBrightnessShowHD"),
        evtSettingBrightnessHideHD = LuaEvent:NewIns("evtSettingBrightnessHideHD"),

        evtSettingSwitchModeChanged = LuaEvent:NewIns("evtSettingSwitchModeChanged"),
        evtCloseTips = LuaEvent:NewIns("evtCloseTips"),

        -- 音乐音效设置改变
        evtSettingVolumeChanged = LuaEvent:NewIns("evtSettingVolumeChanged"),

        evtDownloadItemSelected = LuaEvent:NewIns("evtDownloadItemSelected"),
        evtSetUserFocusToCrossplay = LuaEvent:NewIns("evtSetUserFocusToCrossplay"),
        evtSetSecondLanguage = LuaEvent:NewIns("evtSetSecondLanguage"),
        evtChangeInvisibleState = LuaEvent:NewIns("evtChangeInvisibleState"),

        -- 云设置
            -- 上传状态变更，已同步/上传中
        evtCloudSettingUploadStateChanged = LuaEvent:NewIns("evtCloudSettingUploadStateChanged"),
            -- 槽状态变更：应用，增、删
        evtCloudSettingSlotStateChanged = LuaEvent:NewIns("evtCloudSettingSlotStateChanged"),
            -- 手动修改键位成功时
        evtCloudSettingKeySettingChanged = LuaEvent:NewIns("evtCloudSettingKeySettingChanged"),
    },

    Paths = {
        meterial1 = "Material'/Game/UI/UIVideo/M_Media_SystemSetting.M_Media_SystemSetting'",
        player1 = "MediaPlayer'/Game/UI/UIVideo/UI_MediaPlayer_SystemSetting.UI_MediaPlayer_SystemSetting'",
        meterial2 = "Material'/Game/UI/UIVideo/M_Media_SystemSetting_2.M_Media_SystemSetting_2'",
        player2 = "MediaPlayer'/Game/UI/UIVideo/UI_MediaPlayer_SystemSetting_2.UI_MediaPlayer_SystemSetting_2'"
    },

    --设置Tab
    ESystemSettingPanel = {
        SetMode = 1,
        BaseSetting = 2,
        ControlSetting = 3,
        SensititySetting = 4,
        SystemSetting = 5,
        ComminicateSetting = 6,
        DisplaySetting = 7,
        VeHicleSetting = 8,
        PufferDownloadSetting = 9,
        LanguageSetting = 10,
        PrivacySetting = 11,
        OtherSetting = 12 -- [aidenliao] 这两个设置选项动态添加，详见SystemSettingMainView:_InitDynamicBtns()
    },


    --开火模式
    EFireMode = {
	    Press = 0,
	    Release = 1,
    },
    --开镜模式
    EScopeOpenMode = {
	    Click = 0,
	    Hold = 1,
	    Mix = 2
    },
    --一键开镜开火模式
    EQuickScopeOpenMode = {
	    AllOpen = 0,
	    AllClose = 1,
	    Custom = 2
    },

    --灵敏度级别
    ERotationSensitityLevel = {
        Low = 0,
        Middle = 1,
        High = 2,
        Custom = 3
    },
    SystemBtnClickType = {
        OnClick = 0,
        Click = 1,
    },

    -- DFHD Start
    ESettingGameType = {
        DeltaForce = 1,
        BlackHawkDown = 2,
    },

    -- BEGIN MODIFICATION @LiDailong - VIRTUOS
    -- 设置Tab index
    ESystemSettingHDPanel = {
        GamePlaySetting = 1,
        InputSetting = 2,
        InputSetting_Gamepad = 3,
        UISetting = 4,
        VideoSetting = 5,
        RadioSetting = 6,
        PrivacySetting = 7,
        Language = 8,
        DownloadCenter = 9
    },
    -- END MODIFICATION

    ESystemSettingHDInputPanel = {
        Sensitivity = 1,
        CCC = 2,
        UI = 3,
        Vehicle = 4,
        GM = 5
    },

    --BEGIN MODIFICATION @VIRTUOS
    ESystemSettingHDInputPanel_Gamepad = {
        Sensitivity = 1,
        Control = 2,
        CCC = 3,
        Aiming = 4,
        Vehicle = 5
    },
    --END MODIFICATION

    EAudioDynamicRangeHD = {
        HomeCinema = 0,
        Headphones = 1,
        NightMode = 2
    },

    EVideoBrightnessHD = {
        Low = 40,
        High = 60,
    },
    -- DFHD End

    --信用拦截类型
    EXinyongType = {
        ChatLimit = 0,
        AddFriendLimit = 1,
    },

    --战斗载具
    EBattleVehicle = {
        Tank = "TankLayout",
        Helicopter = "HelicopterLayout",
        WeaponVehicleButton = "WeaponVehicleButtonLayout",
        Jet = "Jet",
    },

    --战斗机不同操作模式
    EJetVehicleMode = {
        Glider      = "JetGliderStyle",
        JoyStick    = "JetJoyStickStyle"
    },

    --玩法对应的布局
    EGameRule2LayoutName = {
    [MatchGameRule.SOLGameRule] = "SOLLayout",
    [MatchGameRule.RaidGameRule] = "SOLLayout",
    [MatchGameRule.TDMClassGameRule] = "ClassLayout",
    [MatchGameRule.TDMODGameRule] = "BattleLayout",
    [MatchGameRule.TDMConquestGameRule] = "BattleLayout",
    [MatchGameRule.TDMBlitzGameRule] = "BattleLayout",
    [MatchGameRule.TDMActivityGameRule] = "BattleLayout",
    [MatchGameRule.TDMHumanMachineGameRule] = "BattleLayout",
    [MatchGameRule.ArenaGameRule] = "SOLLayout",
    },

    E3DAudioType = {
        Stereo = 0,
        DolbyAtoms = 1
    },

    EVehicleSensitivityMode ={
    NONE = 0,
    Vehicle_Driver_FPP = 1,
    Vehicle_Driver_TPP = 2,
    Vehicle_Weapon_Passenger_FPP = 3,
    Vehicle_Weapon_Passenger_TPP = 4,
    Helicopter_Driver_FPP = 5,
    Helicopter_Driver_TPP = 6,
    },

    -- 国服包下可选语音
    CNWwiseLanguageList = {
        [1] = {
            Culture = "Chinese",
            DisplayName = "中文"
        },
        [2] = {
            Culture = "English",
            DisplayName = "英语"
        }
    },


    ResourceList = {
        ["HDResource"] = {
            DisplayName = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HDResource", "高清资源"),
        },
    },

    SimpleCloudSettingIDs = {
        -- 游戏页
        "bCrossplay",
        "bCanMouseTurnMelee",
        "bSceneFOVCalcOpenCameraFOV",
        "bReloadBreakMirror",
        "bMixedLoading",
        "bVaultTriggerMode",
        "bAirVehicleAutoBalanceAssist",
        "bFixedWingTPP",
        "bAutoParachute",
        "HitEffectColor",
        "RouletteColorA",
        "FlagColorA",
        "DrugDuration",
        "bMPSingleClickCreateLocMark",
        "bSafeBoxPreferMarkItem",
        "bSafeBoxPreferHighValueItem",
        "SafeBoxPriceThreshold",
        "bShareSpoils",
        "bInventoryAutoNewline",
        "bInventorySortEveryEnter",
        -- "bConfirmSettingDiff",
        -- 键鼠页灵敏度
        "Sensitivity",
        "VerticalSensitivity",
        "HorizontalSensitivity",
        "ADS_Sensitivty_Ratio",
        "AimVerticalSensitivity",
        "AimHorizontalSensitivity",
        "SensitivityMode",
        "MDV",
        "MDVSwitchingMode",
        "SideAimingControlMode",
        "bAllVerticalMMReversed",
        "bInfantryVerticalMMReversed",
        "bVehicleVerticalMMReversed",
        "bHelicopterVerticalMMReversed",
        "bGunnerVerticalMMReversed",
        "CannonCamFollow",
        "DriverWeaponSensitivityTPP",
        "DriverWeaponSensitivityFPP",
        "BaseADSSensitivity_Driver",
        "SensitivityMode_Driver",
        "MDV_Driver",
        "HelicopterDriverWeaponSensitivityTPP",
        "HelicopterDriverWeaponSensitivityFPP",
        "BaseADSSensitivity_HelicopterDriver",
        "SensitivityMode_HelicopterDriver",
        "MDV_HelicopterDriver",
        "PassengerWeaponSensitivityFPP",
        "BaseADSSensitivity_Passenger",
        "SensitivityMode_Passenger",
        "MDV_Passenger",
        -- 手柄页灵敏度
        "GamepadRightThumbDeadZone",
        "GamepadRightThumbMaximumThreshold",
        "GamepadLeftThumbDeadZone",
        "GamepadLeftThumbMaximumThreshold",
        "GamepadSensitivityCurveType",
        "GamepadSensitivityPresetMode",
        "HorizontalGamepadSensitivity",
        "VerticalGamepadSensitivity",
        "ExtraSensitivityDataHDHorizontalValue",
        "ExtraSensitivityDataHDVerticalValue",
        "ExtraSensitivityDataHDStartTime",
        "ExtraSensitivityDataHDDelayTime",
        "AimHorizontalGamepadSensitivity",
        "AimVerticalGamepadSensitivity",
        "ADSExtraSensitivityDataHDHorizontalValue",
        "ADSExtraSensitivityDataHDVerticalValue",
        "ADSExtraSensitivityDataHDStartTime",
        "ADSExtraSensitivityDataHDDelayTime",
        "bEnableScopeCustom",
        "GamepadMDV",
        "GamepadMDVSwitchingMode",
        -- "bSceneFOVCalcOpenCameraFOV", 手柄和鼠标用的同一个变量？
        "bAllVerticalMMReversedGamepad",
        "bInfantryVerticalMMReversedGamepad",
        "bVehicleVerticalMMReversedGamepad",
        "bHelicopterVerticalMMReversedGamepad",
        "bGunnerVerticalMMReversedGamepad",
        "CannonCamFollowGamepad",
        "bEnableGamepadRumble",
        "bEnableAdaptiveTrigger",
        "DriverWeaponHorizontalSensitivityFPP_Gamepad",
        "DriverWeaponVerticalSensitivityFPP_Gamepad",
        "DriverWeaponHorizontalSensitivityTPP_Gamepad",
        "DriverWeaponVerticalSensitivityTPP_Gamepad",
        "BaseADSSensitivity_Driver_Gamepad",
        "bEnableDriverADSCustomGamepad",
        "MDV_Driver_Gamepad",
        "HelicopterDriverWeaponHorizontalSensitivityFPP_Gamepad",
        "HelicopterDriverWeaponVerticalSensitivityFPP_Gamepad",
        "HelicopterDriverWeaponHorizontalSensitivityTPP_Gamepad",
        "HelicopterDriverWeaponVerticalSensitivityTPP_Gamepad",
        "BaseADSSensitivity_HelicopterDriver_Gamepad",
        "SensitivityMode_HelicopterDriver_Gamepad",
        "bEnableHelicopterDriverADSCustomGamepad",
        "MDV_HelicopterDriver_Gamepad",
        "PassengerWeaponHorizontalSensitivityFPP_Gamepad",
        "PassengerWeaponVerticalSensitivityFPP_Gamepad",
        -- "PassengerWeaponHorizontalSensitivityTPP_Gamepad",
        -- "PassengerWeaponVerticalSensitivityTPP_Gamepad",
        -- "PassengerWeaponSensitivityTPP_Gamepad",
        "BaseADSSensitivity_Passenger_Gamepad",
        "SensitivityMode_Passenger_Gamepad",
        "bEnablePassengerADSCustomGamepad",
        "MDV_Passenger_Gamepad",
        "bAimAssistorGamepad",
        -- 手柄页操控设置
        -- 预设
        "AutoSprintType_Gamepad",
        "Sprint_ActionLogic_Gamepad",
        "SuperSprintsSwitch_Gamepad",
        "SlideDiveTrigger_Gamepad",
        "bEnableLBRBtoRBRT",
        "ToggleAiming_ActionLogic_Gamepad",
        "SideAimType_Gamepad",
        "bEnableBreath",
        "PeekOpenMode_Gamepad",
        "bScopeOpenAutoPeek_Gamepad",
        "bCanMouseTurnMelee_GamePad",
        "bSwitchWeaponsIncludingBattlefieldProps_GamePad",
        "Rescue_ActionLogic_Gamepad",
        "AbandonRescue_ActionLogic_Gamepad",
        -- 界面页
        "Display_PerformanceStatus",
        "bEnablePickupDetailHUD",
        "bHurtCloseLootPanel",
        "bSprintCloseLootPanel",
        "bMapAutoRotateMp",
        "InfantryVision",
        "VehicleVision",
        "AirPlaneVision",
        "MpMapScale",
        "bSprintCloseLootPanel",
    }
}

return SystemSettingConfig