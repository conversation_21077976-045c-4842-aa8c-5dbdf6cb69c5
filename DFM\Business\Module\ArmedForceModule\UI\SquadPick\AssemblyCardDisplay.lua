----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMArmedForce)
----- LOG FUNCTION AUTO GENERATE END -----------



local AssemblyCardDisplay = ui("AssemblyCardDisplay")
---@class AssemblyCardDisplay : UIWidgetBase

function AssemblyCardDisplay:Ctor()
    loginfo("AssemblyCardDisplay:Ctor")

    self._wtCardDisplayBox=self:Wnd("DFHorizontalBox_0",UIWidgetBase)
    self._displayItemInstIdList={}
    Module.CommonBar:RegStackUIHideBar(self)
end

function AssemblyCardDisplay:OnInitExtraData(cardInfos, IndextoFashionSuitTable,needWaitGameplayConfig)
    loginfo("AssemblyCardDisplay:OnInitExtraData",needWaitGameplayConfig)
    self._cardInfos=cardInfos
    self._needWaitGameplayConfig=needWaitGameplayConfig
    logtable(self._cardInfos,true)

    self._IndextoFashionSuitTable = IndextoFashionSuitTable -- 选定的英雄相关信息
end

function AssemblyCardDisplay:OnShow()
    loginfo("AssemblyCardDisplay:OnShow()")

    Server.ArmedForceServer.Events.evtAssemblyCardDisplayOnShow:Invoke()
end

function AssemblyCardDisplay:OnOpen()
    loginfo("AssemblyCardDisplay:OnOpen")

    Facade.UIManager:RemoveSubUIByParent(self,self._wtCardDisplayBox)
    self._displayItemInstIdList={}
    for k,v in pairs(self._cardInfos or {})do
        local weakInst,instId=Facade.UIManager:AddSubUI(self,UIName2ID.HeroBussinessCard,self._wtCardDisplayBox,k,false)
        local uiInst=getfromweak(weakInst)
        if uiInst then
            --设置名片小尺寸
            uiInst:SetCppValue("TypeSize",0)
            uiInst:SetCppValue("Type",0)
            uiInst:SetType(0)
            uiInst:Settypesize(0)
            
            table.insert(self._displayItemInstIdList,instId)
            if v.player_id==Server.AccountServer:GetPlayerId() then
                uiInst:InitCardData(tostring(v.hero_id))
            else
                uiInst:SetCardInfoByAccessories2(v.accessories)
                uiInst:SetPlayerName(v.nick_name)
            end

            --BEGIN MODIFICATION @ VIRTUOS : 在Console平台，为不可交互的PlayerName禁用聚焦，防止聚焦框被微软误判为CFR问题。
            if IsConsole() then
                uiInst:SetPlayerNameFocusable(false)
            end
            --END MODIFICATION

            --BEGIN MODIFICATION @ VIRTUOS : 
            if IsPS5Family() then
                local playerNickName = v.nick_name
                local playerOnlineId = self:GetPS5OnlineIdByOpenId(v.player_id)
                if playerOnlineId ~= nil then
                    playerNickName = playerOnlineId
                end
                uiInst:SetPlayerName(playerNickName)
            end
            --END MODIFICATION

            --- BEGIN MODIFICATION @ VIRTUOS
            if IsConsole() and uiInst.SetPlayerPlatformIDType then
                -- 判断是否为AI
                logerror("AssemblyCardDisplay:OnOpen", v.is_bot, v.plat_id)
                if v.is_bot and not v.plat_id then
                    -- AI默认设置为PC平台
                    uiInst:SetPlayerPlatformIDType(PlatIDType.Plat_PC)
                end
                uiInst:SetPlayerPlatformIDType(v.plat_id)
                if not Server.AccountServer:IsCrossPlat() then
                    uiInst:SetPlayerPlatformIDType(nil)
                end
            end
            --- END MODIFICATION

            uiInst:UpdateTitleInfo(nil,v.title,v.rank_title_adcode,v.rank_title_rank_no)
            
        end
    end
    
    
    if self._needWaitGameplayConfig then
        self:AddLuaEvent(Server.MatchServer.Events.evtGameplayConfigLoaded,self._OnGameplayConfigLoaded,self)--gameplayConfig加载完成
    else
        Facade.ProtoManager:AddNtfListener("CSPlayerJoinMatchNtf", self._OnPlayerJoinMatch, self)--入局通知
    end
    

end

function AssemblyCardDisplay:OnClose()
    loginfo("AssemblyCardDisplay:OnClose")
    Facade.ProtoManager:RemoveAllNtfListenerByCaller(self)
    self:RemoveAllLuaEvent()
    Facade.UIManager:ClearSubUIByParent(self,self._wtCardDisplayBox)
    Module.Preparation:ForwardHeroFashionSuitTable(self._IndextoFashionSuitTable)
    Module.Preparation:ForwardEquipmentData()
    local matchModeInfo=Server.MatchServer:GetMatchModeInfo()
    local transitionTime=0
    if matchModeInfo and matchModeInfo.game_mode==MatchGameMode.TDMGameMode then
        transitionTime=5
    else
        transitionTime=3
    end
    if Server.AccountServer:IsInRoom() or (matchModeInfo and matchModeInfo.game_mode==MatchGameMode.TDMGameMode) or Server.MatchServer:IsSeamlessTravelEnabled() or IsHD() then
        Facade.UIManager:CommitTransition(true, 1, transitionTime,ETransitionChangeReason.ProtoWaiting)--打开黑幕
        Facade.UIManager:SetIsJumpRollbackTransition(true)
    end
end

function AssemblyCardDisplay:_OnGameplayConfigLoaded()
    loginfo("AssemblyCardDisplay:_OnGameplayConfigLoaded")
    self:_CloseSelf()
end

function AssemblyCardDisplay:_OnPlayerJoinMatch()
    loginfo("AssemblyCardDisplay:_OnPlayerJoinMatch")
    self:_CloseSelf()
end

function AssemblyCardDisplay:_CloseSelf()
    loginfo("AssemblyCardDisplay:_CloseSelf")
    Facade.UIManager:CloseUI(self)
end

--BEGIN MODIFICATION @ VIRTUOS : 
function AssemblyCardDisplay:GetPS5OnlineIdByOpenId(openId)
    if IsPS5Family() then
        local UDFMOnlineIdentityManager = import "DFMOnlineIdentityManager"
        local DFMOnlineIdentityManager = UDFMOnlineIdentityManager.Get(GetGameInstance())

        if DFMOnlineIdentityManager then
            local PS5OnlineId = DFMOnlineIdentityManager:GetPlayerPlatformIdByOpenId(openId)
            if not string.isempty(PS5OnlineId) then
                return PS5OnlineId
            end
        end
    end

    return nil
end
--END MODIFICATION

return AssemblyCardDisplay
