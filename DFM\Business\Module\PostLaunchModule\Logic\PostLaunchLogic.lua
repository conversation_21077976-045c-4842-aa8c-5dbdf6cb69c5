----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMPostLaunch)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @LiDailong - VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

local PostLaunchLogic = {}
local UDFMPSOCacheSystem = import "DFMPSOCacheSystem"
local UGPGConfigUtils = import "GPGConfigUtils"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UGameSDKManager = import "GameSDKManager"
local Config = require("DFM.Business.Module.PostLaunchModule.PostLaunchConfig")
local build_version = VersionUtil.GetVersionFull()
local ULuaExtension = import "LuaExtension"
local UDFMPSOTaskScheduler = import "DFMPSOTaskScheduler"
local GameLaunchReportTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.GameLaunchReportTool"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"

local log = function(...) print("[PostLaunchLogic] ", ...) end

local command = string.lower(UKismetSystemLibrary.GetCommandLine())
log("command=["..command.."]")

PostLaunchLogic.ConfigStageOrder = {
    Config.EConfigStage.None,
}

local ConfigStageOrder = PostLaunchLogic.ConfigStageOrder

-------------------------------------------------
--调试用 开始
-------------------------------------------------
--这个文件不会提交，所以永远不会将debug版交上去
local DebugLogicPath = "DFM.Business.Module.PostLaunchModule.Logic.Debug.PostLaunchDebugLogic_DO_NOT_SUBMIT"
local DebugLogic = nil
local debugDummyPSO = false
local debugForcePSO = false
local debugForceFistLaunch = false
PostLaunchLogic.InitDebug = function()
    if isexist(DebugLogicPath) then
        DebugLogic = require(DebugLogicPath)
        debugForceFistLaunch = DebugLogic.debugForceFistLaunch
        debugDummyPSO = DebugLogic.debugDummyPSO
        debugForcePSO = DebugLogic.debugForcePSO
        if debugDummyPSO then --换成PSO模拟器
            UDFMPSOCacheSystem = PostLaunchLogic.GetUDFMPSOCacheSystem()
        end
        if debugForcePSO then
            UGPGConfigUtils.SetString(Config.ItemPSO.section, Config.ItemPSO.key, "1.2.3.4", Config.ItemPSO.ini)
        end
    end
end

PostLaunchLogic.GetUDFMPSOCacheSystem = function()
    if debugDummyPSO then
        local Cls = require("DFM.Business.Module.PostLaunchModule.Logic.Debug.PostLaunchPSOSimulatorLogic")
        return Cls
    else
        return UDFMPSOCacheSystem
    end
end
-------------------------------------------------
--调试用 结束
-------------------------------------------------

PostLaunchLogic.RunPSOTaskScheduler = function()
    -- if UDFMPSOTaskScheduler.RunPSOTaskScheduler() then
    if IsConsole() then
        return false
    end

    if UDFMPSOTaskScheduler.PSOTaskSchedulerDebug() then
        return true
    else
        local UWeGameManager = import "WeGameSDKManager"
        if UWeGameManager then
            local WeGameManager = UWeGameManager.Get(GetGameInstance())
            local distributeID = WeGameManager:GetDistributeID()
            for _, v in pairs(Config.WeGameDistributeID_InternetBar) do
                if distributeID == v then
                    return true
                end
            end
        end
    end        
    -- end
    return false
end

PostLaunchLogic.IsInternetBar = function()
    if IsConsole() then
        return false
    end

    local UWeGameManager = import "WeGameSDKManager"
    if UWeGameManager then
        local WeGameManager = UWeGameManager.Get(GetGameInstance())
        local distributeID = WeGameManager:GetDistributeID()
        for _, v in pairs(Config.WeGameDistributeID_InternetBar) do
            if distributeID == v then
                return true
            end
        end
    end     
    -- end
    return false
end

PostLaunchLogic.FirstPanelProcess = function()
    -- local GameInstance = GetGameInstance()
    -- UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, "Log Slua Log", nil)
    Facade.UIManager:SetStackBottomKeepMode(true, "PostLaunchLogic")
    local field = Module.PostLaunch.Field

    field.isFirstLaunch = PostLaunchLogic._IsFirstLaunch()
    field.shouldPrecompileAll = PostLaunchLogic._ShouldPrecompileAll()
    log("shouldPrecompileAll:" .. tostring(field.shouldPrecompileAll))
    Config.AlwaysWait = Config.AlwaysWait and (field.isFirstLaunch or field.shouldPrecompileAll)


    -- 首次启动
    if field.isFirstLaunch then
        Module.SystemSetting:ApplyRecommandGraphicsQulitySettingHD()
        -- 国服不选语言，其它选
        if not IsBuildRegionCN() or IsInEditor() then
            table.insert(ConfigStageOrder, Config.EConfigStage.Culture)
        else
            -- 国服直接跳过语言选择，直接上报完成即可
            GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_Culture)
        end
        table.insert(ConfigStageOrder, Config.EConfigStage.Brightness)
        table.insert(ConfigStageOrder, Config.EConfigStage.AudioOutputMode)
    else
        -- 非首次启动，前序的都跳过了，直接上报成功
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_Culture)
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_Brightness)
        GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_AudioOutputMode)
    end

    local bShowCommonBar = false
    -- 等待PSO预热的控件
    if field.shouldPrecompileAll then
        if PostLaunchLogic.IsInternetBar() then
            log("disable r.ShaderPipelineCache.PrecompilePSODiff")
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.ShaderPipelineCache.PrecompilePSODiff 0", nil)
        end

        -- 网吧开启PSO动态调度
        if PostLaunchLogic.RunPSOTaskScheduler() then
            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "r.DFRunPSOTaskScheduler 1", nil)
            Module.CommonBar:ShowTopBar()
            Module.CommonBar:ShowBottomBar()
            bShowCommonBar = true
        end

        UDFMPSOCacheSystem.Get():PrecompileAll()
        field.cachedPSOPrecompileState = UDFMPSOCacheSystem.Get():GetPrecompileState()
        Module.PostLaunch.Field.PSOProgressWidgetHandle = Facade.UIManager:AsyncShowUI(UIName2ID.PostLaunchPSOProgressWidget)
    else
        UDFMPSOCacheSystem.Get():ResumePSOBatching()
    end

    if Config.AlwaysWait then
        table.insert(ConfigStageOrder, Config.EConfigStage.WaitUntilDone)
    end

    table.insert(ConfigStageOrder, Config.EConfigStage.Complete)

    if (#ConfigStageOrder > 2) and (not bShowCommonBar) then --可能有界面，就显示CommonBar
        Module.CommonBar:ShowTopBar()
        if IsHD() then
            Module.CommonBar:ShowBottomBar()
        end
    end

    log("ConfigStageOrder:")
    for i = 1, #ConfigStageOrder do
        log(ConfigStageOrder[i])
    end

    PostLaunchLogic.NextPanelProcess()
end

PostLaunchLogic._IsFirstLaunch = function()
    if PostLaunchLogic._IsForceFirstLaunch() then
        return true
    end
    if PostLaunchLogic._IsSkipFirstLaunch() then
        return false
    end
    local configurated = false
    local result = false
    result, configurated = UGPGConfigUtils.GetBool(Config.Item.section, Config.Item.key, nil, Config.Item.ini)
    log(Config.Item.key .. " " .. tostring(result) .. " " .. tostring(configurated))
    return not (result and configurated)
end

PostLaunchLogic._ShouldPrecompileAll = function()
    if UDFMPSOCacheSystem.Get():ShouldPrecompileAll() then
        if not PostLaunchLogic._SkipGPUDriverVersionCheck() then
            -- 若GetGPUDriverVersionHD返回值有效，且配置文件值有效，且与函数返回值不同，则预热
            local field = Module.PostLaunch.Field
            field.GPUDriverVersion = PostLaunchLogic._GetGPUDriverVersion()
            if field.GPUDriverVersion ~= "" then
                local LastGPUDriverVersion = PostLaunchLogic._GetLastGPUDriverVersion()
                if (LastGPUDriverVersion ~= "") then
                    if (LastGPUDriverVersion ~= field.GPUDriverVersion) then
                        return true
                    end
                    field.GPUDriverVersion = "" -- 二者相同，不必更新记录
                end
            end
        end
        local folderPath = FPaths.ProjectSavedDir()
        local files = ULuaExtension.Ext_FindFilesRecursive({}, folderPath, "*.ushaderprecache", true, false, false)

        -- 已预热文件不存在，重新预热
        if files == nil or #files <= 0 then
            log("ShouldPrecompileAll because NOT found ushaderprecache file")
            return true
        end

        -- 有些机器不会完成文件保存，只会留下1m大小的文件。
        -- for i = 1, #files do
        --     local fileSize = ULuaExtension.Ext_GetFileSize(files[i])
        --     -- 存在默认创建的预热文件(1M大小)，重新预热
        --     if fileSize == 1024*1024 or fileSize <= 0 then
        --         log("ShouldPrecompileAll because found " .. files[i] .. " with size = " .. tostring(fileSize))
        --         return true
        --     end
        -- end

        local PSODone = "0.0.0.0"
        local result = false
        result, PSODone = UGPGConfigUtils.GetString(Config.ItemPSO.section, Config.ItemPSO.key, nil, Config.ItemPSO.ini)
        log(Config.ItemPSO.key .. " " .. tostring(result) .. " " .. PSODone)
        return not (result and PSODone == build_version)
    end
    return false
end

PostLaunchLogic._IsForceFirstLaunch = function()
    return debugForceFistLaunch or (string.find(command, "-forcefirstlaunch") ~= nil)
end

PostLaunchLogic._IsSkipFirstLaunch = function()
    return string.find(command, "-skipfirstlaunch") ~= nil
end

local bShouldRemoveDefaultBackground = false -- 会导致stage之间黑屏，关闭
local bRemovedDefaultBackground = false
-- 到下一个界面
PostLaunchLogic.NextPanelProcess = function()
    local field = Module.PostLaunch.Field
    if not PostLaunchLogic._TryUpdateStage(true) then
        return
    end

    local OnNextUI = function(uiIns, _)
        if uiIns then
            field.PanelCount = field.PanelCount + 1
        end
        field.currentStageIndex = field.nextStageIndex

        if bShouldRemoveDefaultBackground and (not bRemovedDefaultBackground) then
            local sdkManager = UGameSDKManager and UGameSDKManager.Get(GetGameInstance())
            if sdkManager then --移除资源加载界面
                sdkManager:RemoveDefaultBackground()
            end
            bRemovedDefaultBackground = true
        end
    end

    -- 上报当前流程结束
    local currentStage = ConfigStageOrder[field.currentStageIndex]
    PostLaunchLogic._MarkStageStateAndReport(currentStage)

    if field.nextStageIndex > field.currentStageIndex then
        local nextStage = ConfigStageOrder[field.nextStageIndex]
        if nextStage == Config.EConfigStage.Culture or nextStage == Config.EConfigStage.Brightness or nextStage == Config.EConfigStage.AudioOutputMode then
            -- ...Culture->Brightness->AudioOutputMode->...
            PostLaunchLogic._ShowUIForNextStage(nextStage, OnNextUI)
        elseif nextStage == Config.EConfigStage.WaitUntilDone then
            if not Config.AlwaysWait and field:IsPSOCachePrecompileDone() then
                -- 跳过等待PSO的过程
                OnNextUI(nil, nil)
                PostLaunchLogic.NextPanelProcess()
            else
                Facade.SoundManager:PlayLoginStartBGM() -- 触发登录界面音乐--bug=130036911 [【安卓|iOS|PC】【AtBug】【CCB接】着色器阶段播放登录界面声音](https://tapd.woa.com/r/t?id=130036911&type=bug)
                PostLaunchLogic._ShowUIForNextStage(nextStage, OnNextUI)
                field.waitingWidgetHandle = Facade.UIManager:AsyncShowUI(UIName2ID.PostLaunchWaitWidget)
            end
        elseif nextStage == Config.EConfigStage.Complete then
            PostLaunchLogic.Complete()
        else
            log("ignore unsupport next stage: ", nextStage)
        end
    end
end

PostLaunchLogic._MarkStageStateAndReport = function(curStage)
    local field = Module.PostLaunch.Field
    local reportAndStageMap = {
        [Config.EConfigStage.Culture] = LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_Culture,
        [Config.EConfigStage.Brightness] = LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_Brightness,
        [Config.EConfigStage.AudioOutputMode] = LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_AudioOutputMode,
    }

    if table.containskey(reportAndStageMap, curStage) then
        -- 先看是不是跑过，再决定是不是要上报
        if not field:IsStageFinished(curStage) then
            -- 上报下
            GameLaunchReportTool.ReportGameLaunchStep(reportAndStageMap[curStage])
        end
        -- 此时是海外，语言选择走过了
        field:MarkStageFinish(curStage)
    end
end

PostLaunchLogic._ShowUIForNextStage = function(stage, callback)
    local nextUI = Module.PostLaunch.Config.PostLaunchStageConfig[stage].UI_ID
    if nextUI ~= nil then
        Facade.UIManager:AsyncShowUI(nextUI, callback, nil)
        if DebugLogic then
            DebugLogic.OnLoadingUI()
        end
    end
end

-- Module工作完成
PostLaunchLogic.Complete = function()
    local field = Module.PostLaunch.Field
    -- 关闭PSO进度控件
    if field.PSOProgressWidgetHandle then
        Facade.UIManager:CloseUIByHandle(field.PSOProgressWidgetHandle)
        field.PSOProgressWidgetHandle = nil
    end
    -- 关闭等待控件
    if field.waitingWidgetHandle then
        Facade.UIManager:CloseUIByHandle(field.waitingWidgetHandle)
        field.waitingWidgetHandle = nil
    end
    -- 关闭栈界面
    if field.PanelCount > 0 then
        local stackUIController = Facade.UIManager:GetLayerControllerByType(EUILayer.Stack)
        stackUIController:PopUIByNum(field.PanelCount, true)
        field.PanelCount = 0
    end

    -- 记录玩家是否完成首次启动配置
    UGPGConfigUtils.SetBool(Config.Item.section, Config.Item.key, true , Config.Item.ini)
    log(Config.Item.key .. " set true")

    -- 判断是否启用了后台预热，如果是后台预热则要延后写入ini
    local bBackgroundPSORunning = false
    local taskScheduler = UDFMPSOCacheSystem.Get():GetTaskScheduler()
    if taskScheduler then
        if taskScheduler:IsRunning() then
            bBackgroundPSORunning = UDFMPSOTaskScheduler.BackgoundPSOTask()
        end
    end
    if bBackgroundPSORunning then
        if field.shouldPrecompileAll then
            taskScheduler:AddPendingFinishedIniMark(Config.ItemPSO.section, Config.ItemPSO.key, build_version, Config.ItemPSO.ini)
        end

        if field.GPUDriverVersion ~= "" then
            taskScheduler:AddPendingFinishedIniMark(Config.ItemGPUDriverVersion.section, Config.ItemGPUDriverVersion.key, field.GPUDriverVersion, Config.ItemGPUDriverVersion.ini)
        end
    else
        -- 记录已完成PSO预热的游戏版本
        if (not Config.NotWaitPSO) and field.shouldPrecompileAll then
            UGPGConfigUtils.SetString(Config.ItemPSO.section, Config.ItemPSO.key, build_version, Config.ItemPSO.ini)
            log(Config.ItemPSO.key .. " set " .. build_version)
        end

        -- 记录已完成PSO预热的GPU driver版本
        if (not Config.NotWaitPSO) and field.GPUDriverVersion ~= "" then
            UGPGConfigUtils.SetString(Config.ItemGPUDriverVersion.section, Config.ItemGPUDriverVersion.key, field.GPUDriverVersion, Config.ItemGPUDriverVersion.ini)
            log(Config.ItemGPUDriverVersion.key .. " set " .. field.GPUDriverVersion)
        end
    end

    -- 上报PostLaunch流程结束，此时实际上也是PSO编译结束，如果没有PSO编译，其实也是直接结束
    GameLaunchReportTool.ReportGameLaunchStep(LogAnalysisTool.EGameLaunchOutOfLoginStepName.PostLaunch_Setting_PSODone)
    field:ResetFinishedStages()

    Config.flowEvtPostLaunchDone:Invoke("Done")
end

-- 到前一个界面
PostLaunchLogic.PreviousPanelProcess = function()
    local field = Module.PostLaunch.Field
    if not PostLaunchLogic._TryUpdateStage(false) then
        return
    end
    if field.nextStageIndex < 1 then
        return
    end

    if field.nextStageIndex < field.currentStageIndex then
        local nextStage = ConfigStageOrder[field.nextStageIndex]
        if nextStage == Config.EConfigStage.Culture or nextStage == Config.EConfigStage.Brightness or nextStage == Config.EConfigStage.AudioOutputMode then
            -- ...<-Culture<-Brightness<-AudioOutputMode<-...
            PostLaunchLogic._OnPreviousUI()
        else
            log("ignore unsupport previous stage: ", nextStage)
        end
    end
end

-- 到前一个界面了
PostLaunchLogic._OnPreviousUI = function()
    local field = Module.PostLaunch.Field
    if field.PanelCount > 0 then
        field.PanelCount = field.PanelCount - 1
    end
    if field.currentStageIndex == Config.EConfigStage.WaitUntilDone then
        if field.waitingWidgetHandle then
            Facade.UIManager:CloseUIByHandle(field.waitingWidgetHandle)
            field.waitingWidgetHandle = nil
        end
    end
    field.currentStageIndex = field.nextStageIndex
end

-- 执行界面切换
PostLaunchLogic._TryUpdateStage = function(isNext)
    local field = Module.PostLaunch.Field
    if field.nextStageIndex == field.currentStageIndex then
        if isNext then
            -- 前进
            if field.currentStageIndex > (#ConfigStageOrder + 1) then
                return false
            end
            field.nextStageIndex = field.currentStageIndex + 1
        else
            -- 后退
            if field.currentStageIndex < 2 then
                return false
            end
            field.nextStageIndex = field.currentStageIndex - 1
        end
        return true
    end
    log("当前stage切换尚未完成", field.currentStageIndex, "->", field.nextStageIndex)
    return false
end

-- 界面Show了
PostLaunchLogic.OnStagePanelShow = function(uiIns)
    local field = Module.PostLaunch.Field
    while field.currentStageIndex > uiIns._stageIndex
    do
        local original = field.currentStageIndex
        PostLaunchLogic.PreviousPanelProcess()
        if original == field.currentStageIndex then
            log("救命啊，死循环了", field.currentStageIndex, "->", stageIndex)
            break
        end
    end
end

-- 界面Open了
PostLaunchLogic.OnStagePanelOpen = function(uiIns)
    local stageConfig = Module.PostLaunch.Config.PostLaunchStageConfig[ConfigStageOrder[2]]
    if stageConfig.CanBack and stageConfig.UI_ID == uiIns.UINavID then -- 能返回 + 第一页
        return
    end

    Module.CommonBar:RegStackUIDefaultEsc(uiIns, Module.PostLaunch.Config.PostLaunchStageConfig[ConfigStageOrder[uiIns._stageIndex]].CanBack)
end

local lockInteract = false
-- 界面构造了
PostLaunchLogic.OnStagePanelConstruct = function(uiIns)
    local stageConfig = Module.PostLaunch.Config.PostLaunchStageConfig[ConfigStageOrder[2]]
    -- BEGIN MODIFICATION @ VIRTUOS : 
    if IsConsole() then
        return
    end
    -- END MODIFICATION  
    if stageConfig.CanBack and stageConfig.UI_ID == uiIns.UINavID then -- 能返回 + 第一页
        local OnShow = function(uiIns, _)
            Module.PostLaunch.Field.confirmWindowIns = uiIns
            lockInteract = false
        end
        Show = function()
            if not lockInteract and Module.PostLaunch.Field.confirmWindowIns == nil then
                lockInteract = true
                Facade.UIManager:AsyncShowUI(UIName2ID.PostLaunchConfirmWindow, OnShow, nil)
            end
        end
        -- 注册返回桌面按钮
        Module.CommonBar:RegStackUIInputSummary(uiIns, {
            {actionName = "BackToDesktop", func = Show, caller = nil, bUIOnly = false, bHideIcon = false},
        })
    end
end

PostLaunchLogic._GetGPUDriverVersion = function()
    local UVideoSettingHelper = import("VideoSettingHelper")
    local GPUDriverVersion = UVideoSettingHelper.GetGPUDriverVersionHD("")
    local GPUDriverVersionStruct = UVideoSettingHelper.ParseGPUDriverHD(GPUDriverVersion)
    if (GPUDriverVersionStruct.WDDM ~= 0) or (GPUDriverVersionStruct.DX ~= 0) or (GPUDriverVersionStruct.Base ~= 0) or (GPUDriverVersionStruct.Build ~= 0) then
        return string.trim(GPUDriverVersion)
    end
    return ""
end

PostLaunchLogic._GetLastGPUDriverVersion = function()
    local LastGPUDriverVersion = ""
    local result = false
    result, LastGPUDriverVersion = UGPGConfigUtils.GetString(Config.ItemGPUDriverVersion.section, Config.ItemGPUDriverVersion.key, nil, Config.ItemGPUDriverVersion.ini)
    log(Config.ItemGPUDriverVersion.key .. " " .. tostring(result) .. " " .. LastGPUDriverVersion)
    if LastGPUDriverVersion ~= "" then
        local UVideoSettingHelper = import("VideoSettingHelper")
        local GPUDriverVersionStruct = UVideoSettingHelper.ParseGPUDriverHD(LastGPUDriverVersion)
        if (GPUDriverVersionStruct.WDDM ~= 0) or (GPUDriverVersionStruct.DX ~= 0) or (GPUDriverVersionStruct.Base ~= 0) or (GPUDriverVersionStruct.Build ~= 0) then
            return string.trim(LastGPUDriverVersion)
        end
    end
    return ""
end

PostLaunchLogic._SkipGPUDriverVersionCheck = function()
    local UWeGameManager = import "WeGameSDKManager"
    if UWeGameManager then
        local WeGameManager = UWeGameManager.Get(GetGameInstance())
        local distributeID = WeGameManager:GetDistributeID()
        log("_SkipGPUDriverVersionCheck" .. tostring(distributeID))
        -- 网吧渠道跳过驱动检查
        local skipGPUDriverVersionCheck = false
        for _, v in pairs(Config.WeGameDistributeID_InternetBar) do
            if distributeID == v then 
                log("_SkipGPUDriverVersionCheck skipped!")
                return true
            end
        end
    end
    log("_SkipGPUDriverVersionCheck NOT skipped!")
    return false
end

return PostLaunchLogic
