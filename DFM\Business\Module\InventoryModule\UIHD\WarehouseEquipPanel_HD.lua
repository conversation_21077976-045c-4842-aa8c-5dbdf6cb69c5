----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local InvSlotView = require "DFM.Business.Module.InventoryModule.UI.Common.InvSlotView"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local CommonDragDropMask = require "DFM.Business.Module.CommonWidgetModule.UI.CommonDragDropMask"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemView = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemView"
local WarehouseEquipSlotView = require "DFM.Business.Module.InventoryModule.UI.MainV2.WarehouseEquipSlotView"
local KeyContainerView_HD    = require "DFM.Business.Module.InventoryModule.UIHD.KeyContainerView_HD"
local WarehouseContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseContainerPanel_HD"
local WarehouseKeyContainerPanel_HD = require "DFM.Business.Module.InventoryModule.UI.Common.WarehouseKeyContainerPanel_HD"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local InventoryModule  = require "DFM.Business.Module.InventoryModule.InventoryModule"
local InventoryNavManager = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder

local EViewDragMode = import "EViewDragMode"
local EDescendantScrollDestination = import "EDescendantScrollDestination"
local ULuaHudHelperLibrary = import "LuaHudHelperLibrary"


---@class WarehouseEquipPanel_HD : LuaUIBaseView
local WarehouseEquipPanel_HD = ui("WarehouseEquipPanel_HD")

local MAGIC_NUM = 1209.28 -- 切换到2对应的offset
local MAGIC_NUM_2 = 1282.28 -- 切换到3对应的offset

local function log(...)
    loginfo("[WarehouseEquipPanel_HD]", ...)
end

function WarehouseEquipPanel_HD:Ctor()
    self._bSkipNextMainScrollCallback = false
    self._allitemviews = {}
    self._bAddItemview = false

    ---@type table<ESlotType, WarehouseEquipSlotView>
    self._mapSlotType2EquipViews = {}

    ---@type table<ESlotType, InvSlotView>
    self._mapSlotType2ContainerViews = {}
    ---@type WareHouseContainerBox_HD
    self._wtSafeBox = nil

    self._wtMainCanvas = self:Wnd("wtMainCanvas", UIWidgetBase)
    self._wtMainScrollBox = self:Wnd("wtMainScrollBox", UIScrollBox)
    Module.Inventory:InitItemScrollBoxParams(self._wtMainScrollBox)
    self._wtMainScrollBox:Event("OnUserScrolled", self._OnMainScrolled, self)
    self._wtMainBox = self:Wnd("wtMainBox", UIWidgetBase)
    self._wtScrollingSafeBoxSlot = self:Wnd("SafeBoxSubSlot_1", UINamedSlot)
    self._wtFixedSafeBoxSlot = self:Wnd("SafeBoxSubSlot_2", UINamedSlot)

    self._wtEquipValueWidget = self:Wnd("wtEquipmentValueWidget", UIWidgetBase)
    self._wtEquipValueTxt = self._wtEquipValueWidget:Wnd("DFRichTextBlock_61", UITextBlock)

    self._wtShowHeroSkinBtn = self:Wnd("wtDFCommonCheckBoxWithText", DFCheckBoxWithText)
    self._wtShowHeroSkinBtn:Event("OnCheckStateChanged", self._IsShowHeroSkin, self)
    self._wtMaskPanel = self:Wnd("wtMaskPanel", UIWidgetBase)

    self._wtLeftShoulderKey = self:Wnd("KeyIcon_01",HDKeyIconBox)
    self._wtRightShoulderKey = self:Wnd("KeyIcon_02",HDKeyIconBox)
    self._wtLeftShoulderKeyInEquipValue = self._wtEquipValueWidget:Wnd("KeyIcon_02", HDKeyIconBox)
    self._wtRightShoulderKeyInEquipValue = self._wtEquipValueWidget:Wnd("KeyIcon_01", HDKeyIconBox)

    self._wtUpMask = self:Wnd("wtUpMask", CommonDragDropMask)
    self._wtUpMask:Collapsed()
    self._wtDownMask = self:Wnd("wtDownMask", CommonDragDropMask)
    self._wtDownMask:Collapsed()

    if IsHD() then
        self._wtLeftShoulderKey:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtRightShoulderKey:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
        self._wtLeftShoulderKeyInEquipValue:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0.0, false)
        self._wtRightShoulderKeyInEquipValue:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0.0, false)
    end


    self:_InitEquipmentSlotViews()
    self:_InitContainerSlotViews()
    -- self:_InitAutoScrollWidget()

end

function WarehouseEquipPanel_HD:Destroy()
    if self._autoScrollWidget then
        self._autoScrollWidget:Release()
        self._autoScrollWidget = nil
    end
    self:_OnRemoveSubUI()
end

--==================================================
--region Life function

function WarehouseEquipPanel_HD:OnShowBegin()
    self:_InitHeroName()
    self:_InitHeroSkinBtn()
    InventoryNavManager.backpackPanel = self
end

function WarehouseEquipPanel_HD:OnShow()
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtUpdateItemCapacity, self._OnCapacityChanged, self)
    self:AddLuaEvent(InventoryConfig.Events.evtJumpToEquipPanel, self._JumpToEquipSubPanel, self)
    -- self:AddLuaEvent(InventoryConfig.Events.evtEquipPanelNavChange, self._OnEquipPanelNavChanged, self)
    -- self:AddLuaEvent(InventoryConfig.Events.evtShowEquipCompareIcon, self._OnShowEquipCompare, self)
	self:AddLuaEvent(Module.Inventory.Config.Events.evtWidgetIntoView, self._WidgetIntoView, self)
    self:AddLuaEvent(Module.Inventory.Config.Events.evtChangeSafeBoxState, self._ChangeSafeBoxState, self)

    local leftWeaponSlot = self._mapSlotType2EquipViews[ESlotType.MainWeaponLeft]
    local rightWeaponSlot = self._mapSlotType2EquipViews[ESlotType.MainWeaponRight]
    Module.CommonWidget:RegisterWeaponSelection(leftWeaponSlot:GetMainItemView(), rightWeaponSlot:GetMainItemView())
    self:AddLuaEvent(Server.ArmedForceServer.Events.evtArmedForceRentalStatusChanged, self._CheckRentalStatus, self)

    self:_RefreshEquipValue()
    self:_RefreshView()
    
    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self._curShowTipsIndex = 0
    end
	--END MODIFICATION
    InventoryNavManager.backpackPanel = self
end

function WarehouseEquipPanel_HD:OnHide()
    self:RemoveAllLuaEvent()
    Module.CommonWidget:UnregisterWeaponSelection()

    --BEGIN MODIFICATION @ VIRTUOS : 
    if IsHD() then
        self._curShowTipsIndex = 0
    end
	--END MODIFICATION
    -- self._autoScrollWidget:EnableComponent(false)
end

function WarehouseEquipPanel_HD:OnInitExtraData()

end

--endregion
--==================================================


--==================================================
--region Public API

function WarehouseEquipPanel_HD:SetRentalState(bIsRental)
    local slotGroup = bIsRental and ESlotGroup.MainRental or ESlotGroup.Player
    for slotType, equipSlotView in pairs(self._mapSlotType2EquipViews) do
        if Server.ArmedForceServer:IsRentalReferenceSlot(slotType) then
            -- 近战武器只不刷新数据
            if slotType ~= ESlotType.MeleeWeapon then equipSlotView:InitGroup(slotGroup) end
            equipSlotView:RefreshView()
        end
    end
    for slotType, invSlotView in pairs(self._mapSlotType2ContainerViews) do
        if Server.ArmedForceServer:IsRentalReferenceSlot(slotType) then
            invSlotView:InitServerSlot(slotType, slotGroup)
            invSlotView:RefreshView()

            invSlotView:ShowMask(bIsRental)
        end
    end
    local allWarehouseContainerPanel = {self._wtCHContainerView, self._wtPocketContainerView, self._wtBagContainerView}
    for _, containerPanel in ipairs(allWarehouseContainerPanel) do
        local bindContainerSlot = containerPanel.bindContainerSlot
        if Server.ArmedForceServer:IsRentalReferenceSlot(bindContainerSlot.SlotType) then
            containerPanel:InitContainerSlot(bindContainerSlot.SlotType, slotGroup)
            containerPanel:RefreshView()

            local containerSlotView = containerPanel:GetContainerSlotView()
            if containerSlotView then
                containerSlotView:ShowMask(bIsRental)
            end
        end
    end
end

function WarehouseEquipPanel_HD:PlayInAnim()
    self:PlayWidgetAnim(self.Ani_in)
end

-- function WarehouseEquipPanel_HD:ShowMidBtns(bShow)
--     if bShow then
--         self._wtMidBtnRoot:SelfHitTestInvisible()
--     else
--         self._wtMidBtnRoot:Collapsed()
--     end
-- end

function WarehouseEquipPanel_HD:ForceSetNavTabByIndex(index)
    if self._navTabComponent then
        local num = self._navTabComponent:GetMainTabsNum()
        local currentIndex = self._navTabComponent:GetCurrentIndex()
        if index ~= currentIndex and index >= 1 and index <= num then
            self._navTabComponent:SetMainIndex(index, true)
        end
    end
end

local BGCOLOR = 
{
    [1] = ColorUtil.GetLinearColorByHex("02090E4C"),
    [2] = ColorUtil.GetLinearColorByHex("DD7B7AFF")
}
function WarehouseEquipPanel_HD:SetContainerNameBG(type, style)
    if type == ESlotType.BagSpaceContainer then
        self._wtBagNameBG:SetColorAndOpacity(BGCOLOR[style])
    end
end

-- 【新手引导】高亮装备槽位
function WarehouseEquipPanel_HD:GuideHighlightEquipPart()
    for slotType, equipView in pairs(self._mapSlotType2EquipViews) do
        local highlightView = equipView:GetHighlightView()
        if highlightView then
            highlightView:SelfHitTestInvisible()
            highlightView:PlayGuideHighlight(true)
        end
    end
end

-- 【新手引导】高亮背包槽位
function WarehouseEquipPanel_HD:GuideHighlightBagPart()
    local highlightView = self._wtBagSlotView:GetHighlightView()
    if highlightView then
        self._wtBagSlotView:SetHighlightMode(InvSlotView.EHighlightMode.AvailableSlot)

        highlightView:SelfHitTestInvisible()
        highlightView:PlayGuideHighlight(true)
    end
end

-- 【新手引导】停止高亮
function WarehouseEquipPanel_HD:GuideStopHighlight()
    for slotType, equipView in pairs(self._mapSlotType2EquipViews) do
        local highlightView = equipView:GetHighlightView()
        if highlightView then
            highlightView:StopAllHighlights()
        end
    end

    local highlightView = self._wtBagSlotView:GetHighlightView()
    if highlightView then
        highlightView:StopAllHighlights()
    end
end

-- 【新手引导】移动至背包的位置
function WarehouseEquipPanel_HD:GuideMoveToBagPart(speed, interval)
    -- self:ForceSetNavTabByIndex(2)

    speed = setdefault(speed, 0.05)
    interval = setdefault(interval, 0.01)

    -- local geometry = self._wtBagSubPanel:GetCachedGeometry()
    local geometry = self._wtBagContainerView:GetCachedGeometry()
    local localPos = geometry:GetLocalPositionAtCoordinates(LuaGlobalConst.TOP_LEFT_VECTOR)

    self._wtMainScrollBox:ScrollToOffsetWithParams(localPos.Y, speed, interval)
end


-- 进入整理、出售、扩容箱管理界面时需要显示遮罩
function WarehouseEquipPanel_HD:ShowMask(bShow)
    self._wtMaskPanel:Setvisibility(bShow and ESlateVisibility.SelfHitTestInvisible or ESlateVisibility.Collapsed)
end

--endregion
--==================================================


--==================================================
--region Private API

function WarehouseEquipPanel_HD:_OnRemoveSubUI()
end

function WarehouseEquipPanel_HD:_RefreshView()
    self._wtBagSlotView:RefreshView()
    self._wtSafeBoxSlotView:RefreshView()
    self:_RefreshAllCapacity()
    -- self:_RefreshTransferBtn()
    -- self:_RefreshContainerStatus()
end

function WarehouseEquipPanel_HD:_RefreshAllCapacity()
    self:_RefreshCapacity(ESlotType.BagSpaceContainer)
    self:_RefreshCapacity(ESlotType.SafeBoxContainer)
end

function WarehouseEquipPanel_HD:_RefreshCapacity(containerSlotType)
    -- local targetTextBlock, targetContainerSlot
    -- if containerSlotType == ESlotType.BagSpaceContainer then
    --     targetTextBlock = self._wtBagCapacity
    --     if self._wtBagSlotView.itemSlot then
    --         targetContainerSlot = self._wtBagSlotView.itemSlot
    --     else
    --         targetContainerSlot = Server.InventoryServer:GetSlot(ESlotType.BagSpaceContainer)
    --     end
    -- elseif containerSlotType == ESlotType.SafeBoxContainer then
    --     targetTextBlock = self._wtSafeBoxCapacity
    --     targetContainerSlot = Server.InventoryServer:GetSlot(ESlotType.SafeBoxContainer)
    -- else
    --     return
    -- end

    -- local totalCapacity = targetContainerSlot:GetTotalCapacity()
    -- local usedCapacity = targetContainerSlot:GetUsedCapacity()

    -- targetTextBlock:SetText(string.format("%d/%d", usedCapacity, totalCapacity))
end

function WarehouseEquipPanel_HD:_RefreshTransferBtn()
    local bTransferAllow = true
    local items2Transfer = self:_GetItems2Transfer()
    if #items2Transfer == 0 then
        bTransferAllow = false
    end

    -- self:SetCppValue("BpDisableMove", not bTransferAllow)
    -- self:Bp_UpdateMoveBtn()

    -- if bTransferAllow then
    --     self:PlayWidgetAnim(self.MoveAnim, 0)
    -- else
    --     self:StopWidgetAnim(self.MoveAnim)
    -- end
end

local function fCheckContainerShouldShow(type, panel, name)
    local bagSlot = Server.InventoryServer:GetSlot(type)
    local bHasBag = bagSlot:GetEquipItem()
    if bHasBag then
        panel:SelfHitTestInvisible()
        if name then
            local slotName = Module.Inventory.Config.SlotNameMapping[type]
            if bHasBag:CheckIsTeammateBind() then
                name:SetText(string.format(Module.Inventory.Config.Loc.BindingEquipmentContainerName,slotName))
            else
                name:SetText(string.format(Module.Inventory.Config.Loc.EquipmentContainerName,slotName))
            end
        end
    else
        panel:Collapsed()
    end
end


function WarehouseEquipPanel_HD:_RefreshContainerStatus()
    fCheckContainerShouldShow(ESlotType.KeyChain, self._wtKeyContainerView)
end

function WarehouseEquipPanel_HD:_InitEquipmentSlotViews()
    ---@type ArmedForceNormalSlotView[]
    local allEquipSlotViews = self:MultiWnd("WBP_WarehouseEquipSlotView")
    self._mapSlotType2EquipViews = {}
    for _, equipSlotView in ipairs(allEquipSlotViews) do
        local slotType = equipSlotView:GetSlotType()
        equipSlotView.bUseDotLine = true
        self._mapSlotType2EquipViews[slotType] = equipSlotView
        -- InventoryNavManager.AllInvSlotView[slotType] = equipSlotView
    end

    -- 钥匙包view
    self._wtKeyContainerView = self:Wnd("wtKeyContainerView", UIWidgetBase):Wnd("wtEquipSlotView", WarehouseEquipSlotView)
    self._mapSlotType2EquipViews[ESlotType.KeyChain] = self._wtKeyContainerView
end

local ETextStyle = {
    Default = 0,
    Equipment = 1,
    ItemName = 2,
    AuctionText = 3,
}
local function fPostItemRefresh(itemView)
    if itemView.item then
        if itemView.item:CheckIsTeammateBind() then
            itemView:FindOrAdd(EComp.GreyMask, UIName2ID.IVGreyMask, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
            local maskComponent = itemView:GetComponent(EComp.GreyMask)
            if maskComponent then
                maskComponent:HitTestInvisible()
            end

            local bindingComponent = itemView:GetComponent(EComp.BottomLeftIconText)
            if bindingComponent and bindingComponent:IsEnabled() then
                itemView:SetPosOrder(EComp.BottomLeftIconText, EIVCompOrder.High)
            end
        else
            local equipmentFeature = itemView.item:GetFeature(EFeatureType.Equipment)
            local inSlot = itemView.item.InSlot
            local bHandleDrag = true
            if inSlot then
                bHandleDrag = inSlot.SlotType ~= ESlotType.SafeBox and inSlot.SlotType ~= ESlotType.KeyChain
            end
            itemView:SetHandleDrag(bHandleDrag)
        end
    end
end

function WarehouseEquipPanel_HD:_InitContainerSlotViews()
    self._mapSlotType2ContainerViews = {}

    self._wtCHContainerView = self:Wnd("wtCHContainerView", WarehouseContainerPanel_HD)
    self._wtCHContainerView:InitContainerSlot(ESlotType.ChestHangingContainer)
    self._wtPocketContainerView = self:Wnd("wtPocketContainerView", WarehouseContainerPanel_HD)
    self._wtPocketContainerView:InitContainerSlot(ESlotType.Pocket)
    self._wtBagContainerView = self:Wnd("wtBagContainerView", WarehouseContainerPanel_HD)
    self._wtBagContainerView:InitContainerSlot(ESlotType.BagContainer)
    self._wtBagSlotView = self._wtBagContainerView._wtContainerSlotView
    self._mapSlotType2EquipViews[ESlotType.ChestHanging] = self._wtCHContainerView:GetEquipSlotView()
    self._mapSlotType2EquipViews[ESlotType.Bag] = self._wtBagContainerView:GetEquipSlotView()
    self._mapSlotType2ContainerViews[ESlotType.BagContainer] = self._wtBagContainerView:GetContainerSlotView()
    self._mapSlotType2ContainerViews[ESlotType.ChestHangingContainer] = self._wtCHContainerView:GetContainerSlotView()
    self._mapSlotType2ContainerViews[ESlotType.Pocket] = self._wtPocketContainerView:GetContainerSlotView()

    self._wtKeyContainerView = self:Wnd("wtKeyContainerView", WarehouseKeyContainerPanel_HD)

    self._wtSafeBox = Facade.UIManager:CreateSubUIBindOwner(self, UIName2ID.WareHouseContainerBox_HD)
    if Facade.ConfigManager:GetUserBoolean("SafeBoxbFixed", true) then
        self._wtFixedSafeBoxSlot:AddChild(self._wtSafeBox)
    else
        self._wtScrollingSafeBoxSlot:AddChild(self._wtSafeBox)
    end

    for slotType, equipSlotView in pairs(self._mapSlotType2EquipViews) do
        if slotType ~= ESlotType.Helmet and slotType ~= ESlotType.BreastPlate
            and slotType ~= ESlotType.Pistrol and slotType ~= ESlotType.MeleeWeapon
            and slotType ~= ESlotType.MainWeaponLeft and slotType ~= ESlotType.MainWeaponRight then
            equipSlotView:BindScrollBox(self._wtMainScrollBox)
            -- InventoryNavManager.AllInvSlotView[slotType] = equipSlotView
        end
    end
    for _, slotView in pairs(self._mapSlotType2ContainerViews) do
        slotView:BindScrollBox(self._wtMainScrollBox)
        -- InventoryNavManager.AllInvSlotView[slotView.itemSlot.SlotType] = slotView
    end
    self._wtKeyContainerView:BindScrollBox(self._wtMainScrollBox)
    self:_InitSafeBoxContainer()

    --BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:InitWHContainerViewConfig()
    end
	--END MODIFICATION
end

function WarehouseEquipPanel_HD:_InitSafeBoxContainer()
    if self._wtSafeBox then
        self._wtSafeBox:SetCheckBoxState()
        self._wtSafeBox:SetEquipSlotViewType(ESlotType.SafeBox)
        self._wtSafeBox:InitContainerBox(ESlotType.SafeBoxContainer)
        self._wtSafeBoxCapacity = self._wtSafeBox._wtBoxCapacity
        self._wtSafeBoxSlotView = self._wtSafeBox:GetContainerSlotView()
        self._wtSafeBoxSlotView:SetSlotMaxPreviewSize(5, 0)
        self._mapSlotType2ContainerViews[ESlotType.SafeBoxContainer] = self._wtSafeBoxSlotView
        self._mapSlotType2EquipViews[ESlotType.SafeBox] = self._wtSafeBox:GetEquipSlotView()
        if Facade.ConfigManager:GetUserBoolean("SafeBoxbFixed", true) then
            self._mapSlotType2EquipViews[ESlotType.SafeBox]:UnBindScrollBox()
            self._wtSafeBoxSlotView:UnBindScrollBox()
        else
            self._mapSlotType2EquipViews[ESlotType.SafeBox]:BindScrollBox(self._wtMainScrollBox)
            self._wtSafeBoxSlotView:BindScrollBox(self._wtMainScrollBox)
        end
        self._wtSafeBoxSlotView:RefreshView()
        self:_RefreshAllCapacity()
    end
end

function WarehouseEquipPanel_HD:_InitAutoScrollWidget()
    self._wtUpMask = self:Wnd("wtUpMask", CommonDragDropMask)
    self._wtUpMask:Collapsed()
    self._wtDownMask = self:Wnd("wtDownMask", CommonDragDropMask)
    self._wtDownMask:Collapsed()

    ---@type AutoScrollParam
    local param = {}
    param.upMask = self._wtUpMask
    param.downMask = self._wtDownMask
    param.scrollSpeed = DFMGlobalConst.GetDepositConstNumber("AutoScrollSpeed", InventoryConfig.AUTO_SCROLL_SPEED)
    param.bindScrollBox = self._wtMainScrollBox
    param.fScrollCallback = nil
    param.scrollCaller = self
    self._autoScrollWidget = Module.CommonWidget:CreateScrollComponent(param)
    self._autoScrollWidget:SetSleepAndMove(InventoryConfig.SCROLL_SLEEP_TIME, InventoryConfig.SCROLL_MOVE_TIME,
        InventoryConfig.SCROLL_MOVE_DISTANCE)
end

function WarehouseEquipPanel_HD:_InitHeroName()
    local heroInfoTable = Facade.TableManager:GetTable("Hero/HeroData")
    local curHeroUsedID
    if Facade.UIManager:GetCurrentStackUIId() == UIName2ID.WarehouseMain then
        curHeroUsedID = Server.HeroServer:GetCurUsedHeroId()
    else
        local playerCharacter = InGameController:Get():GetGPCharacter()
        local cmp = playerCharacter and playerCharacter.DFMCharacterAppearanceFPP
        if cmp then
            curHeroUsedID = cmp.AvatarId
            local HeroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData")
            local heroFashionData = HeroFashionDataTable[tostring(cmp.AvatarId)]
            if heroFashionData then
                curHeroUsedID = heroFashionData.BelongedHeroID
            end
        end
    end
    local heroInfo = heroInfoTable[tostring(curHeroUsedID)]
    local heroName = heroInfo and heroInfo.Name or ""

    local heroNameTable = self:MultiWnd("HeroNameText")
    for _, heroNameText in ipairs(heroNameTable) do
        heroNameText:SetText(heroName)
    end
end

function WarehouseEquipPanel_HD:_IsShowHeroSkin(bIsChecked)
    Module.LobbyDisplay:SetCharacterDisplayEquipment(bIsChecked)
    InventoryConfig.Events.evtRefreshCharacter:Invoke()
end

function WarehouseEquipPanel_HD:_InitHeroSkinBtn()
    local bIsShow = Module.LobbyDisplay:GetCharacterDisplayEquipment()
    self._wtShowHeroSkinBtn:SetIsChecked(bIsShow)
end

function WarehouseEquipPanel_HD:_GetItems2Transfer()
    local items2Transfer = {}
    local targetContainerTypes = {
        ESlotType.BagContainer,
        ESlotType.ChestHangingContainer,
        ESlotType.Pocket,
        ESlotType.SafeBoxContainer,
    }
    for _, targetContainerType in ipairs(targetContainerTypes) do
        local targetContainer = Server.InventoryServer:GetSlot(targetContainerType)
        local insideItems = targetContainer:GetItems()
        for _, item in ipairs(insideItems) do
            table.insert(items2Transfer, item)
        end
    end

    return items2Transfer
end

function WarehouseEquipPanel_HD:_OnMainScrolled(offset)
    if self._bSkipNextMainScrollCallback then
        self._bSkipNextMainScrollCallback = false
        return
    end
end

function WarehouseEquipPanel_HD:_OnTransferAllBtnClicked()
    local items2Transfer = self:_GetItems2Transfer()
    if #items2Transfer == 0 then
        Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseNothing2Transfer)
        InventoryConfig.Events.evtWareHouseTransferEnd:Invoke()
        return
    end

    Module.Inventory.Config.Events.evtSaveExpansionNum:Invoke()
    local toDepositIds = Server.InventoryServer:GetAllDepositIds()
    local toDepositMainSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
    local isTransferSeccess
    for _, item in ipairs(items2Transfer) do
        local bCanTrans = ItemOperaTool.DoPlaceItem(item, toDepositMainSlot, false)
        if not bCanTrans then
            -- 寻找其他扩容箱
            for _, depositId in pairs(toDepositIds) do
                local otherDepositSlot = Server.InventoryServer:GetSlot(depositId)
                if ItemOperaTool.DoPlaceItem(item, otherDepositSlot, false) then
                    isTransferSeccess = true
                end
            end
        else
            isTransferSeccess = true
        end
    end

    if not isTransferSeccess then
        Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.InventorySpaceLacking)
    end

    local fTransferCallback = function(res)
        if res.result == 0 then
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseTransferAllSuccess)
            Module.Inventory.Config.Events.evtCompareExpansionNum:Invoke()
        else
            Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.WarehouseTransferAllFail)
        end
        InventoryConfig.Events.evtWareHouseTransferEnd:Invoke(res)
    end
    Server.InventoryServer:SyncInventoryChanged(fTransferCallback, {allow_partly = true, is_transfer_all = true})
end

function WarehouseEquipPanel_HD:_OnArmedForceBtnClick()
    Module.ArmedForce:ShowMainPanel()
end

function WarehouseEquipPanel_HD:_OnNavTavChanged(mainIndex, subIndex, component)
    self._bSkipNextMainScrollCallback = true
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIWHTab)
    local targetOffset = 0
    self._wtMainScrollBox:EndInertialScrolling()
    if mainIndex == 1 then
        -- targetOffset = 0
        -- self._wtMainScrollBox:ScrollToStart()
        self._wtMainScrollBox:ScrollWidgetIntoView(self._wtEquipSubPanel, true, EDescendantScrollDestination.TopOrLeft)
    elseif mainIndex == 2 then
        -- targetOffset = WarehouseEquipPanel_HD.MainScrollOffsetMark

        -- self._wtMainScrollBox:SetScrollOffset(targetOffset)
        -- self._wtMainScrollBox:ScrollWidgetIntoView(self._wtBagSubPanel, true, EDescendantScrollDestination.TopOrLeft)
        self._wtMainScrollBox:ScrollWidgetIntoView(self._wtTransferAllBtn, true, EDescendantScrollDestination.TopOrLeft)
    elseif mainIndex == 3 then
        -- targetOffset = self._wtMainScrollBox:GetScrollOffsetOfEnd()
        -- self._wtMainScrollBox:ScrollToEnd()
        self._wtMainScrollBox:ScrollWidgetIntoView(self._wtSafeBoxSubPanel, true, EDescendantScrollDestination.TopOrLeft)
    elseif mainIndex == 4 then
        -- targetOffset = self._wtMainScrollBox:GetScrollOffsetOfEnd()
        self._wtMainScrollBox:ScrollWidgetIntoView(self._wtKeyContainerView, true, EDescendantScrollDestination.TopOrLeft)

        -- self._wtKeyContainerView:ForceCollapse(false)
    end

    self._navTabs[mainIndex]:PlaySelectedAnim()
end

function WarehouseEquipPanel_HD:_OnEquipPanelNavChanged(mainIndex)
    self._navTabComponent:SetMainIndex(mainIndex, false)
    self:_OnNavTavChanged(mainIndex)
    self._wtBagSlotView:SetHighlightMode(InvSlotView.EHighlightMode.None)
end

function WarehouseEquipPanel_HD:_OnShowEquipCompare(slotType, bImprove, bHide)
    local widget = self._mapSlotType2EquipViews[slotType]:GetMainItemView()
    if widget then
        return
    end
    if not bHide then
        local compareComponent = widget:FindOrAdd(EComp.GetMask, UIName2ID.IVComparedComponent, EIVSlotPos.MaskLayer, EIVCompOrder.Order1)
        compareComponent:PlayComponentAnim()
        compareComponent:SetWidgetType(bImprove)
    else
        widget:EnableComponent(EComp.GetMask,false)
    end
end

function WarehouseEquipPanel_HD:_OnItemMove(itemMoveInfo)
    ---@param self WarehouseEquipPanel_HD
    local function fDelayScrollTo(self, itemMoveInfo)
        if not itemMoveInfo then
            return
        end
        local item = itemMoveInfo.item
        ---@type ItemLocation
        local newLoc = itemMoveInfo.NewLoc
        local newSlot = newLoc and newLoc.ItemSlot or nil
        if newSlot and (newSlot:IsEquipSlot() or newSlot:IsContainerSlot()) then
            Module.CommonWidget:SetItemScrollCmd(itemMoveInfo, self._wtMainScrollBox)
            --self._wtMainScrollBox:EndInertialScrolling()
            --self:ScrolltoItem(item)
        end
    end
    if not Module.Inventory.Config.bEnableScrollDispatch then
        fDelayScrollTo(self, itemMoveInfo)
    end

    local targetSlot = itemMoveInfo and itemMoveInfo.NewLoc and itemMoveInfo.NewLoc.ItemSlot
    local sourceSlot = itemMoveInfo and itemMoveInfo.OldLoc and itemMoveInfo.OldLoc.ItemSlot
    if (targetSlot and targetSlot:IsEquipSlot()) or (sourceSlot and sourceSlot:IsEquipSlot()) then
        self:_RefreshEquipValue()
    end
    --Timer.DelayCall(LuaGlobalConst.TIMER_DURACTION_FOR_ONE_FLAME, fDelayScrollTo, self, itemMoveInfo)

    -- self:_CheckKeyChainTab()
    -- self:_RefreshContainerStatus()
    -- self:_RefreshTransferBtn()
end

function WarehouseEquipPanel_HD:ScrolltoItem(item)
    local newSlot = item.InSlot
    local targetScroll2Widget
    if newSlot then
        targetScroll2Widget = self._mapSlotType2EquipViews[newSlot.SlotType]

        if not targetScroll2Widget then
            local containerSlotView = self._mapSlotType2ContainerViews[newSlot.SlotType]
            if newSlot.SlotType == ESlotType.KeyChainContainer then
                targetScroll2Widget = self._wtKeyContainerView
            else
                targetScroll2Widget = containerSlotView and containerSlotView:GetViewByItem(item)
            end
        end
    end

    if targetScroll2Widget and not self:_IsShowInView(targetScroll2Widget) then
        self._wtMainScrollBox:EndInertialScrolling()
        self._wtMainScrollBox:ScrollWidgetIntoView(targetScroll2Widget, true, EDescendantScrollDestination.Center)
    end
end

function WarehouseEquipPanel_HD:_IsShowInView(viewIns)
    -- 判断itemview是不是在视口中
    local mainScrollBoxGeometry = self._wtMainScrollBox:GetCachedGeometry()
    local mainScrollBoxSize = mainScrollBoxGeometry:GetLocalSize()
    local viewGeometry = viewIns:GetCachedGeometry()
    local viewAbsoluteLocationCC = viewGeometry:GetAbsolutePositionAtCoordinates(LuaGlobalConst.CENTER_CENTER_VECTOR)
    local viewCenterPoint = mainScrollBoxGeometry:AbsoluteToLocal(viewAbsoluteLocationCC)

    local viewSize = viewGeometry:GetLocalSize()
    local downOffset = viewCenterPoint.Y + (viewSize.Y / 2.0)
    local upOffset = viewCenterPoint.Y - (viewSize.Y / 2.0)

    if upOffset >= 0 and downOffset <= mainScrollBoxSize.Y then
        return true
    else
        return false
    end
end

function WarehouseEquipPanel_HD:_JumpToEquipSubPanel()
    self._wtMainScrollBox:ScrollWidgetIntoView(self._wtEquipSubPanel, true, EDescendantScrollDestination.TopOrLeft)
    -- self._wtMainScrollBox:ScrollToStart()
end

---@param equipContaierSlot ItemSlot
function WarehouseEquipPanel_HD:_OnCapacityChanged(equipContainerSlot, bAdd)
    self:_RefreshCapacity(equipContainerSlot.SlotType)
end

function WarehouseEquipPanel_HD:_WidgetIntoView(widget)
    self._wtMainScrollBox:ScrollWidgetIntoView(widget, true, EDescendantScrollDestination.IntoView)
end

function WarehouseEquipPanel_HD:_ChangeSafeBoxState(bChecked)
    -- 当进行切换时，首先隐藏当前所在的ui
    local weakUIIns, instanceId
    if bChecked then
        self._wtSafeBox:RemoveFromParent()
        self._wtFixedSafeBoxSlot:AddChild(self._wtSafeBox)
    else
        self._wtSafeBox:RemoveFromParent()
        self._wtScrollingSafeBoxSlot:AddChild(self._wtSafeBox)
    end
    self:_InitSafeBoxContainer()

    --BEGIN MODIFICATION @ VIRTUOS :
    if IsHD() then
        -- self:InitWHContainerViewConfig()
        -- self._wtMainScrollBox:ScrollToEnd()
        -- InventoryNavManager.FocusWithTimer(self._wtSafeBox)
        InventoryNavManager.FocusToSafeBox(true)
    end
	--END MODIFICATION
end

function WarehouseEquipPanel_HD:_RefreshEquipValue()
    local equipValue = Module.ArmedForce:GetAllEquipmentValue()
    local equipValueTxt = MathUtil.GetNumberFormatStr(equipValue)
    self._wtEquipValueTxt:SetText(string.format(InventoryConfig.Loc.PriceStrText, equipValueTxt))
end

function WarehouseEquipPanel_HD:_CheckRentalStatus()
    self:_RefreshEquipValue()
end

--BEGIN MODIFICATION @ VIRTUOS : UI Gamepad
function WarehouseEquipPanel_HD:InitWHContainerViewConfig()
    if not IsHD() then
        return 
    end

    -- 记录页面中的WarehouseContainerView
    local WHContainerViewConfig = {
        self._wtCHContainerView,
        self._wtPocketContainerView,
        self._wtBagContainerView,
        self._wtKeyContainerView,
        self._wtSafeBox
    }

    self._wtWHContainerViews = {}
    self._wtHoverBtnArray = {}

    for index, curWHContainerView in ipairs(WHContainerViewConfig) do
        if curWHContainerView then
            table.insert(self._wtWHContainerViews, curWHContainerView)
            local wtHoverBtn = curWHContainerView:Wnd("wtHoverBtn", UIWidgetBase)
            if wtHoverBtn then
                table.insert(self._wtHoverBtnArray, wtHoverBtn)
            else
                local wtTipsCheckBox = curWHContainerView:Wnd("wtTipsCheckBox", UIWidgetBase)
                if wtTipsCheckBox then
                    table.insert(self._wtHoverBtnArray, wtTipsCheckBox)
                end
            end
        end
    end

    self:DisabelHoverTipsFocus()
end

-- 关闭页面中 “问号” 提示的可聚焦
function WarehouseEquipPanel_HD:DisabelHoverTipsFocus()
    if not IsHD() then
        return 
    end

    for key, value in pairs(self._wtHoverBtnArray) do
        local checkBtn = value:Wnd("DFCheckBox_Icon", UICheckBox)
        if checkBtn then
            checkBtn:SetCppValue("IsFocusable", false)
        end
    end
end

function WarehouseEquipPanel_HD:ShowHoverTips()
    if not IsHD() then
        return 
    end

    -- 通过按键逐个显示HoverTips
    if self._curShowTipsIndex < #self._wtWHContainerViews then
        local lastHoveTip = self._wtWHContainerViews[self._curShowTipsIndex]
        if lastHoveTip and lastHoveTip.OnUnHoverTipByGamepad then
            lastHoveTip:OnUnHoverTipByGamepad()
        end

        self._curShowTipsIndex = self._curShowTipsIndex + 1
        local hoverTip = self._wtWHContainerViews[self._curShowTipsIndex]
        if hoverTip and hoverTip.OnHoverTipByGamepad then
            hoverTip:OnHoverTipByGamepad()

            if self._wtMainScrollBox then
                self._wtMainScrollBox:ScrollWidgetIntoView(hoverTip, true, EDescendantScrollDestination.IntoView)
            end
        end

    elseif self._curShowTipsIndex == #self._wtWHContainerViews then
        self:CloseHoverTips()
    end
end

function WarehouseEquipPanel_HD:CloseHoverTips()
    if not IsHD() then
        return 
    end
    
    if self._curShowTipsIndex ~= 0 then
        local hoverTip = self._wtWHContainerViews[self._curShowTipsIndex]
        if hoverTip and hoverTip.OnUnHoverTipByGamepad then
            hoverTip:OnUnHoverTipByGamepad()
        end
        self._curShowTipsIndex = 0
    end
end

function WarehouseEquipPanel_HD:ShowShoulderTips(bShouldShow)
    if bShouldShow == true and not WidgetUtil.IsUsingFreeAnalogCursor() then
        self._wtLeftShoulderKey:SelfHitTestInvisible()
        self._wtLeftShoulderKey:SetOnlyDisplayOnGamepad(true)
        
        self._wtRightShoulderKey:SelfHitTestInvisible()
        self._wtRightShoulderKey:SetOnlyDisplayOnGamepad(true)
        
    else
        self._wtLeftShoulderKey:Collapsed()
        self._wtRightShoulderKey:Collapsed()
    end
end

function WarehouseEquipPanel_HD:ShowShoulderTipsInEquipValue(bShouldShow)
    if bShouldShow == true and not WidgetUtil.IsUsingFreeAnalogCursor() then
        self._wtLeftShoulderKeyInEquipValue:SelfHitTestInvisible()
        self._wtLeftShoulderKeyInEquipValue:SetOnlyDisplayOnGamepad(true)

        self._wtRightShoulderKeyInEquipValue:SelfHitTestInvisible()
        self._wtRightShoulderKeyInEquipValue:SetOnlyDisplayOnGamepad(true)
    else
        self._wtLeftShoulderKeyInEquipValue:Collapsed()
        self._wtRightShoulderKeyInEquipValue:Collapsed()
    end
end

function WarehouseEquipPanel_HD:ScrollToStart()
    self._wtMainScrollBox:ScrollToStart()
    Module.CommonWidget.Config.Events.evtScrollBoxStartScroll:Invoke(self._wtMainScrollBox)
end

--END MODIFICATION

--endregion
--==================================================

return WarehouseEquipPanel_HD