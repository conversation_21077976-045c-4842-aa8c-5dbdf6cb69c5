
------------------------------------
-- Create By Tan<PERSON><PERSON><PERSON><PERSON>ian<PERSON>@VIRTUOS
------------------------------------

----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------


---@class SystemSettingHDInputControlPanel_Gamepad
local SystemSettingHDInputControlPanel_Gamepad = ui("SystemSettingHDInputControlPanel_Gamepad")
local SettingConfig = require "DFM.Business.Module.SystemSettingModule.SystemSettingConfig"
local DisplayInputPresetDropDownItem_Gamepad = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Gamepad.DisplayInputPresetDropDownItem_Gamepad"
local UDFMGameplayInputManager = import("DFMGameplayInputManager")
local EDFMInputMappingTableType = import "EDFMInputMappingTableType"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local EGPGamepadInputType = import("EGPGamepadInputType")


function SystemSettingHDInputControlPanel_Gamepad:Ctor()
    self:_BindEvent()   
    self.InputPresetRows = {}
end

function SystemSettingHDInputControlPanel_Gamepad:_BindWidget()
    self._wtDescRootPanel =  self:Wnd("_wtDescRootPanel", UILightWidget)
    ----------------------------------------------- 输入预设 -----------------------------------------------
    self._wtDisplayInputPreset = self:Wnd("_wtDisplayInputPreset", UIWidgetBase)
    self._wtItemForInputPreset = self:Wnd("_wtItemForInputPreset", DisplayInputPresetDropDownItem_Gamepad)
    self._wtItemForFootJoystickInputPreset = self:Wnd("_wtItemForFootJoystickInputPreset", DisplayInputPresetDropDownItem_Gamepad)
    self._wtItemForVehicleJoystickInputPreset = self:Wnd("_wtItemForVehicleJoystickInputPreset", DisplayInputPresetDropDownItem_Gamepad)
    self._wtItemForHelicopterJoystickInputPreset = self:Wnd("_wtItemForHelicopterJoystickInputPreset", DisplayInputPresetDropDownItem_Gamepad)
    self._wtImage_Gamepad_PS5 = self._wtDisplayInputPreset:Wnd("Image_Gamepad_PS5", UIImage)
    self._wtImage_Gamepad_Xbox = self._wtDisplayInputPreset:Wnd("Image_Gamepad", UIImage)

    -- 因为PS5手柄和XSX手柄的按键提示位置不同，所以在ps5平台需要设置padding
    self._wtItem_SpecialLeft = self._wtDisplayInputPreset:Wnd("_wtItem_SpecialLeft", UIWidgetBase)
    self._wtItem_LeftTrigger = self._wtDisplayInputPreset:Wnd("_wtItem_LeftTrigger", UIWidgetBase)
    self._wtItem_LeftShoulder = self._wtDisplayInputPreset:Wnd("_wtItem_LeftShoulder", UIWidgetBase)
    self._wtItem_LS = self._wtDisplayInputPreset:Wnd("_wtItem_LS", UIWidgetBase)
    self._wtItem_LeftThumbstickBtn = self._wtDisplayInputPreset:Wnd("_wtItem_LeftThumbstickBtn", UIWidgetBase)
    self._wtItem_DpadLeft = self._wtDisplayInputPreset:Wnd("_wtItem_DpadLeft", UIWidgetBase)
    self._wtItem_DpadUp = self._wtDisplayInputPreset:Wnd("_wtItem_DpadUp", UIWidgetBase)
    self._wtItem_DpadRight = self._wtDisplayInputPreset:Wnd("_wtItem_DpadRight", UIWidgetBase)
    self._wtItem_DpadDown = self._wtDisplayInputPreset:Wnd("_wtItem_DpadDown", UIWidgetBase)

    
    self._wtItem_SpecialRight = self._wtDisplayInputPreset:Wnd("_wtItem_SpecialRight", UIWidgetBase)
    self._wtItem_RightTrigger = self._wtDisplayInputPreset:Wnd("_wtItem_RightTrigger", UIWidgetBase)
    self._wtItem_RightShoulder = self._wtDisplayInputPreset:Wnd("_wtItem_RightShoulder", UIWidgetBase)
    self._wtItem_FaceBtnLeft = self._wtDisplayInputPreset:Wnd("_wtItem_FaceBtnLeft", UIWidgetBase)
    self._wtItem_FaceBtnTop = self._wtDisplayInputPreset:Wnd("_wtItem_FaceBtnTop", UIWidgetBase)
    self._wtItem_FaceBtnRight = self._wtDisplayInputPreset:Wnd("_wtItem_FaceBtnRight", UIWidgetBase)
    self._wtItem_FaceBtnBottom = self._wtDisplayInputPreset:Wnd("_wtItem_FaceBtnBottom", UIWidgetBase)
    self._wtItem_RightThumbstickBtn = self._wtDisplayInputPreset:Wnd("_wtItem_RightThumbstickBtn", UIWidgetBase)
    self._wtItem_RS = self._wtDisplayInputPreset:Wnd("_wtItem_RS", UIWidgetBase)
    ----------------------------------------------- 输入预设 -----------------------------------------------

    self._wtDropDownItemForAimMode = self:Wnd("_wtDropDownItemForAimMode", UIWidgetBase)
    self._wtDropDownItemForSideAimControl = self:Wnd("_wtDropDownItemForSideAimControl", UIWidgetBase)

    self._wtLBRBtoRBRTCheck = self:Wnd("_wtLBRBtoRBRTCheck", UILightWidget)
end

function SystemSettingHDInputControlPanel_Gamepad:_BindEvent()
    self._wtItemForInputPreset:BindEventOnOptionChanged(self, self._OnClickDropDownItemBox)
    self._wtItemForFootJoystickInputPreset:BindEventOnOptionChanged(self, self._OnClickDropDownItemBox)
    self._wtItemForVehicleJoystickInputPreset:BindEventOnOptionChanged(self, self._OnClickDropDownItemBox)
    self._wtItemForHelicopterJoystickInputPreset:BindEventOnOptionChanged(self, self._OnClickDropDownItemBox)

    self:AddLuaEvent(self._wtDropDownItemForAimMode.evtOnOptionChanged, self._OnAimModeChanged, self)
    self:AddLuaEvent(self._wtDropDownItemForSideAimControl.evtOnOptionChanged, self._OnSideAimControlModeChanged, self)
end

function SystemSettingHDInputControlPanel_Gamepad:OnOpen()
    self:_BindWidget()

end

function SystemSettingHDInputControlPanel_Gamepad:OnShow()
    -- 接框架：设置Desc显示面板
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.InputSetting_Gamepad then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)

        if IsConsole() then
            -- UI Navigation
            self:_RegisterNavGroup()
        end
    end

    self:_SetKeyHintPositionAndImage()
    if not IsConsole() then
        -- UI Navigation
        self:_RegisterNavGroup()
    end
    self:InitConfig()
end

function SystemSettingHDInputControlPanel_Gamepad:OnShowBegin()

end

function SystemSettingHDInputControlPanel_Gamepad:OnHide()
    self:_RemoveNavGroup()
end

function SystemSettingHDInputControlPanel_Gamepad:OnClose()
    self:_RemoveNavGroup()
end

function SystemSettingHDInputControlPanel_Gamepad:InitConfig()
    self:InitDropDownItemForInputPreset()
    self:InitDropDownItemForJoystickInputPreset()
end

----------------------------------------------- 输入预设 -----------------------------------------------
function SystemSettingHDInputControlPanel_Gamepad:InitDropDownItemForInputPreset()
    self.InputPresetRows = {}
    self.IndexToPresetName = {}
    local DropDownLocs = {}
    local DropDownActivatedIndex = 0

    -- 获取所有预设数据
    local GameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())
    self.InputPresetRows = GameplayInputManager:GetAllInputPresetRows()
    local ActivatedInputPresetName = GameplayInputManager:GetActivatedInputPresetName()
    -- 设置下拉菜单的选项
    for PresetName, PresetData in pairs(self.InputPresetRows) do
        table.insert(DropDownLocs, PresetData.DisplayName)
        table.insert(self.IndexToPresetName, PresetName)
        if PresetName == ActivatedInputPresetName then
            DropDownActivatedIndex = (#self.IndexToPresetName - 1)
        end
    end

    -- 如果玩家进行了KeyRebinding，则显示Custom选项
    if GameplayInputManager:HasKeyRebindings() then
        table.insert(DropDownLocs, SettingConfig.Loc.HDSettingTab.InputSettingControl_CustomInputPresetTxt_Gamepad)
        table.insert(self.IndexToPresetName, "Custom")
        if GameplayInputManager:IsEnableForKeyRebindings() then
            DropDownActivatedIndex = (#self.IndexToPresetName - 1)
        end
    end

    self._wtItemForInputPreset:InitDropDownBox(DropDownLocs, {}, DropDownActivatedIndex, self._wtDisplayInputPreset, self, self._OnPresetDropDownHovered)
end

function SystemSettingHDInputControlPanel_Gamepad:InitDropDownItemForJoystickInputPreset()
    self.IndexToFootJoystickInputPresetName = {}
    self.IndexToVehicleJoystickInputPresetName = {}
    self.IndexToHelicopterJoystickInputPresetName = {}

    local IMCs = { EDFMInputMappingTableType.BaseInput, EDFMInputMappingTableType.VehicleInput, EDFMInputMappingTableType.HelicopterInput }
    local IMCNames = { Module.SystemSetting.Config.Loc.HDSetting.GamepadIMCBaseInputTxt, Module.SystemSetting.Config.Loc.HDSetting.GamepadIMCVehicleInputTxt, Module.SystemSetting.Config.Loc.HDSetting.GamepadIMCHelicopterInputTxt }
    local PresetNames = { "Default", "LeftHand", "Traditional" }
    local PresetDisplayNames = { Module.SystemSetting.Config.Loc.HDSetting.GamepadPresetDisplayNameDefaultTxt, Module.SystemSetting.Config.Loc.HDSetting.GamepadPresetDisplayNameLeftHandTxt, Module.SystemSetting.Config.Loc.HDSetting.GamepadPresetDisplayNameTraditionalTxt }
    local GameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())
    local ActivatedPresetNames = GameplayInputManager:GetActivatedJoystickInputPresetNames()
    
    for i, IMC in ipairs(IMCs) do
        local IndexToPresetName = {}
        local DropDownLocs = {}

        -- 初始化下拉菜单选项数据
        local DropDownActivatedIndex = 0
        local ActivatedPresetName = ActivatedPresetNames:Get(IMC)
        for j, PresetName in ipairs(PresetNames) do
            table.insert(IndexToPresetName, PresetName)
            if ActivatedPresetName == PresetName then
                DropDownActivatedIndex = (#IndexToPresetName - 1)
            end
        end
        for j, PresetDisplayName in ipairs(PresetDisplayNames) do
            table.insert(DropDownLocs, PresetDisplayName)
        end

        -- 设置下拉菜单选项
        if IMC == EDFMInputMappingTableType.BaseInput then
            self.IndexToFootJoystickInputPresetName = IndexToPresetName
            self._wtItemForFootJoystickInputPreset:SetTitleText(IMCNames[i])
            self._wtItemForFootJoystickInputPreset:InitDropDownBox(DropDownLocs, {}, DropDownActivatedIndex, self._wtDisplayInputPreset, self, self._OnPresetDropDownHovered)
        elseif IMC == EDFMInputMappingTableType.VehicleInput then
            self.IndexToVehicleJoystickInputPresetName = IndexToPresetName
            self._wtItemForVehicleJoystickInputPreset:SetTitleText(IMCNames[i])
            self._wtItemForVehicleJoystickInputPreset:InitDropDownBox(DropDownLocs, {}, DropDownActivatedIndex, self._wtDisplayInputPreset, self, self._OnPresetDropDownHovered)
        elseif IMC == EDFMInputMappingTableType.HelicopterInput then
            self.IndexToHelicopterJoystickInputPresetName = IndexToPresetName
            self._wtItemForHelicopterJoystickInputPreset:SetTitleText(IMCNames[i])
            self._wtItemForHelicopterJoystickInputPreset:InitDropDownBox(DropDownLocs, {}, DropDownActivatedIndex, self._wtDisplayInputPreset, self, self._OnPresetDropDownHovered)
        end
    end
end

function SystemSettingHDInputControlPanel_Gamepad:_OnClickDropDownItemBox(itemIns, curIndex, lastIndex)
    local GameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())
    local bIsInputPresetItem = true
    -- 切换基础输入预设
    if itemIns == self._wtItemForInputPreset then
        local SelectedPresetName = self.IndexToPresetName[curIndex+1]
        if SelectedPresetName then
            GameplayInputManager:SetInputPreset(SelectedPresetName)
        end
    -- 切换摇杆输入预设
    elseif itemIns == self._wtItemForFootJoystickInputPreset then
        local SelectedPresetName = self.IndexToFootJoystickInputPresetName[curIndex+1]
        if SelectedPresetName then
            GameplayInputManager:SetJoystickInputPreset(EDFMInputMappingTableType.BaseInput, SelectedPresetName)
        end
    elseif itemIns == self._wtItemForVehicleJoystickInputPreset then
        local SelectedPresetName = self.IndexToVehicleJoystickInputPresetName[curIndex+1]
        if SelectedPresetName then
            GameplayInputManager:SetJoystickInputPreset(EDFMInputMappingTableType.VehicleInput, SelectedPresetName)
        end
    elseif itemIns == self._wtItemForHelicopterJoystickInputPreset then
        local SelectedPresetName = self.IndexToHelicopterJoystickInputPresetName[curIndex+1]
        if SelectedPresetName then
            GameplayInputManager:SetJoystickInputPreset(EDFMInputMappingTableType.HelicopterInput, SelectedPresetName)
        end
    else
        bIsInputPresetItem = false
    end

    if bIsInputPresetItem then
        GameplayInputManager:ApplyInputSettings()

        -- 刷新下拉菜单
        self:InitDropDownItemForInputPreset()
        self:InitDropDownItemForJoystickInputPreset()

        -- 刷新输入预设显示
        self:_OnPresetDropDownHovered(itemIns)
        return
    end
end

function SystemSettingHDInputControlPanel_Gamepad:_OnPresetDropDownHovered(itemIns)
    if self._wtDisplayInputPreset then
        local PresetDescIndex = -1
        -- 基础输入预设
        if itemIns == self._wtItemForInputPreset then
            
        -- 摇杆输入预设
        elseif itemIns == self._wtItemForFootJoystickInputPreset then
            PresetDescIndex = 0
        elseif itemIns == self._wtItemForVehicleJoystickInputPreset then
            PresetDescIndex = 1
        elseif itemIns == self._wtItemForHelicopterJoystickInputPreset then
            PresetDescIndex = 2
        end
        if PresetDescIndex >= 0 then
            self._wtDisplayInputPreset:ActivateBottomTabByIndex(PresetDescIndex)
        end
    end
end
----------------------------------------------- 输入预设 -----------------------------------------------

-- 瞄准和侧瞄的耦合
function SystemSettingHDInputControlPanel_Gamepad:_OnAimModeChanged(index) -- 0 切换 1 按住
    local GamePadSideAimingControlMode = CommonSettingLogicHD.GetDataByID("SideAimType_Gamepad")
    if GamePadSideAimingControlMode > 0 then
        CommonSettingLogicHD.SetDataByID("SideAimType_Gamepad", index + 1)
        self._wtDropDownItemForSideAimControl:ReloadSetting()
    end
end
function SystemSettingHDInputControlPanel_Gamepad:_OnSideAimControlModeChanged(index) -- 0 随倍率 1 按住 2 切换
    if index > 0 then
        CommonSettingLogicHD.SetDataByID("ToggleAiming_ActionLogic_Gamepad", index - 1)
        self._wtDropDownItemForAimMode:ReloadSetting()
    end
end

-- UI导航 
function SystemSettingHDInputControlPanel_Gamepad:_RegisterNavGroup()
    local _wtScrollBox = self:Wnd("_wtScrollBoxForPanel", LuaUIBaseView)
	if not self._NavGroup then
	    self._NavGroup = WidgetUtil.RegisterNavigationGroup(_wtScrollBox, self, "Hittest")
	end
 
	if self._NavGroup then
        self._NavGroup:AddNavWidgetToArray(_wtScrollBox)
        -- 设置可滚动的导航容器
        self._NavGroup:SetScrollRecipient(_wtScrollBox)
		WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
    local list = {{actionName = "ResetInput", func = self._OnClickResetBtn, caller = self}}
    local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
    for _, v in ipairs(globalList) do
        table.insert(list, v)
    end
    Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
end
 
function SystemSettingHDInputControlPanel_Gamepad:_RemoveNavGroup()
    WidgetUtil.RemoveNavigationGroup(self)
    if self._NavGroup then
        self._NavGroup = nil
	end

    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function SystemSettingHDInputControlPanel_Gamepad:_OnClickResetBtn()
    local function fReset()
        SettingLogicHD.ResetCurrentSettings()

        -- 手柄默认预设临时重置，TODO手柄自定义按键重置+上云
        local GameplayInputManager = UDFMGameplayInputManager.Get(GetGameInstance())
        local Default = "DefaultInputPreset"
        GameplayInputManager:SetInputPreset(Default)
        GameplayInputManager:SetJoystickInputPreset(EDFMInputMappingTableType.BaseInput, Default)
        GameplayInputManager:SetJoystickInputPreset(EDFMInputMappingTableType.VehicleInput, Default)
        GameplayInputManager:SetJoystickInputPreset(EDFMInputMappingTableType.HelicopterInput, Default)

        self:InitConfig()
    end
    
    
    local contentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetControlSettingTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    self.confirmWindowHandle =
        Module.CommonTips:ShowConfirmWindow(contentTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

function SystemSettingHDInputControlPanel_Gamepad:_SetKeyHintPositionAndImage()
    if IsPS5() or WidgetUtil.GetCurrentGamepadInputType() == EGPGamepadInputType.Sony then
        self._wtImage_Gamepad_Xbox:SetVisibility(ESlateVisibility.Collapsed)
        self._wtImage_Gamepad_PS5:SetVisibility(ESlateVisibility.HitTestSelfOnly)

        -- 因为PS5手柄的布局和XSX不同，所以需要单独设置Posotion
        self._wtItem_SpecialLeft.Slot:SetPosition(FVector2D(0, -532))
        self._wtItem_LeftTrigger.Slot:SetPosition(FVector2D(0, -420))
        self._wtItem_LeftShoulder.Slot:SetPosition(FVector2D(0, -280))
        self._wtItem_LS.Slot:SetPosition(FVector2D(0, 280))
        self._wtItem_LeftThumbstickBtn.Slot:SetPosition(FVector2D(0, 410))
        self._wtItem_DpadLeft.Slot:SetPosition(FVector2D(0, -220))
        self._wtItem_DpadUp.Slot:SetPosition(FVector2D(0, -100))
        self._wtItem_DpadRight.Slot:SetPosition(FVector2D(0, 30))
        self._wtItem_DpadDown.Slot:SetPosition(FVector2D(0, 150))
    
        
        self._wtItem_SpecialRight.Slot:SetPosition(FVector2D(0, -532))
        self._wtItem_RightTrigger.Slot:SetPosition(FVector2D(0,  -416))
        self._wtItem_RightShoulder.Slot:SetPosition(FVector2D(0, -290))
        self._wtItem_FaceBtnLeft.Slot:SetPosition(FVector2D(0, -208))
        self._wtItem_FaceBtnTop.Slot:SetPosition(FVector2D(0, -92))
        self._wtItem_FaceBtnRight.Slot:SetPosition(FVector2D(0, 28))
        self._wtItem_FaceBtnBottom.Slot:SetPosition(FVector2D(0, 156))
        self._wtItem_RightThumbstickBtn.Slot:SetPosition(FVector2D(0, 408))
        self._wtItem_RS.Slot:SetPosition(FVector2D(0, 288))
    else
        self._wtImage_Gamepad_Xbox:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        self._wtImage_Gamepad_PS5:SetVisibility(ESlateVisibility.Collapsed)
    end
end

return SystemSettingHDInputControlPanel_Gamepad