----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class CommanderMainPanel : LuaUIBaseView
local CommanderMainPanel = ui("CommanderMainPanel")
local TournamentConfig = Module.Tournament.Config
local TournamentLogic = require "DFM.Business.Module.TournamentModule.Logic.TournamentLogic"
local CommonWidgetConfig=Module.CommonWidget.Config
local RankingConfig=Module.Ranking.Config

function CommanderMainPanel:Ctor()
    loginfo("CommanderMainPanel:Ctor")
    self._wtRankRecorderBtn=self:Wnd("WBP_WinnnerTakesAll_Main_Item01_C_0",UIWidgetBase)--排位记录
    self._wtRankRewardBtn=self:Wnd("WBP_WinnnerTakesAll_Main_Item01_C_1",UIWidgetBase)--全部奖励
    self._wtLevelPreviewBtn=self:Wnd("wtCommonIconBtnFind",DFCommonButtonOnly)--段位预览
    self._wtSeasonDescBtn=self:Wnd("wtCommonCheckInstruction",DFCheckBoxOnly)--赛季详情
    self._wtRankScoreTitle=self:Wnd("DFTextBlock_401",UITextBlock)
    self._wtLevelProgressBox=self:Wnd("DFHorizontalBox_2",UIWidgetBase)
    self._wtLevelNameText=self:Wnd("DFTextBlock_143",UITextBlock)--段位名
    self._wtRankScoreText=self:Wnd("DFRichTextBlock_48",UITextBlock)--段位分
    self._progressList=self:MultiWnd("WBP_Ranking_Progressbar",UIWidgetBase)--进度条列表
    self._wtSeasonNameText=self:Wnd("DFTextBlock_75",UITextBlock)--赛季名
    self._wtSeasonDurationText=self:Wnd("DFTextBlock_299",UITextBlock)--赛季时间
    self._wtSeasonDescText=self:Wnd("DFTextBlock_180",UITextBlock)--赛季描述
    self._progressList=self:MultiWnd("WBP_Ranking_Progressbar",UIWidgetBase)--进度条列表
    self._wtRankStarItem=self:Wnd("WBP_SettlementSectionStarBar",UIWidgetBase)--段位星控件
    self._wtNotAttendedText=self:Wnd("DFTextBlock",UITextBlock)
    self._wtJumpToMapBtn=self:Wnd("wtCommonButtonV1S1",DFCommonButtonOnly)
    self._wtRankWidgetSlot=self:Wnd("DFNamedSlot_159",UIWidgetBase)
    self._wtRankCountText=self:Wnd("DFTextBlock_1",UITextBlock)
    self._wtAbilityItemCommander=self:Wnd("WBP_WinnnerTakesAll_Main_Item02",UIWidgetBase)
    self._wtAbilityItemVehicle=self:Wnd("WBP_WinnnerTakesAll_Main_Item02_1",UIWidgetBase)
    self._wtAbilityItemInfantry=self:Wnd("WBP_WinnnerTakesAll_Main_Item02_3",UIWidgetBase)
    self._wtAbilityItemRescue=self:Wnd("WBP_WinnnerTakesAll_Main_Item02_2",UIWidgetBase)
    
    self._wtJumpToMapBtn:Event("OnClicked",self.JumpToModeSelector,self)
    self._wtRankScoreTitle:SetText(Module.Tournament.Config.Loc.CurrentScore)
    self._wtRankRecorderBtn:AddClickCallback(CreateCallBack(self.OpenRankRecorderPanel, self))
    self._wtRankRewardBtn:AddClickCallback(CreateCallBack(self.OpenRankRewardPanel, self))
    self._wtLevelPreviewBtn:Event("OnClicked",self.OpenLevelPreviewPanel,self)
    self._wtSeasonDescBtn:Event("OnCheckStateChanged",self.OpenSeasonDescPanel,self)

    for k,v in pairs(self._progressList) do
        v:Collapsed()
    end
    
end

function CommanderMainPanel:OnInitExtraData(params)
    loginfo("CommanderMainPanel:OnInitExtraData")
end

function CommanderMainPanel:OnOpen()
    loginfo("CommanderMainPanel:OnOpen")
    self:AddBtnReddots()
    self:AddListeners()
    self:AddMouseButtonDownEvent()
    self:RefreshView()
    self:RefreshNewUnlockReddot()
    self:CheckToShowUnlockTips()
    Server.TournamentServer:ReqGetCommanderAbilitiesPercentage()
end

---@overload fun(LuaUIBaseView, OnShowBegin)
function CommanderMainPanel:OnShowBegin()
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    -- END MODIFICATION
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function CommanderMainPanel:OnShow()
    loginfo("CommanderMainPanel:OnShow")
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CommanderMainPanel:OnHide()
    loginfo("CommanderMainPanel:OnHide")

end

-- BEGIN MODIFICATION @ VIRTUOS
function CommanderMainPanel:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadInputs()
    end
end
-- END MODIFICATION

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function CommanderMainPanel:OnClose()
    loginfo("CommanderMainPanel:OnClose")
    
    self:RemoveAllLuaEvent()
    self:RemoveMouseButtonDownEvent()
    self:RemoveAllActionHandle()
    Facade.UIManager:ClearSubUIByParent(self,self._wtRankWidgetSlot)
end

function CommanderMainPanel:AddBtnReddots()
    loginfo("CommanderMainPanel:AddBtnReddots")
    self._wtRankRewardBtn:AddReddot(EReddotTrieObserverType.Tournament,"NewRewardCommander")
end

-- UI监听事件、协议
function CommanderMainPanel:AddListeners()
    self:AddLuaEvent(Server.TournamentServer.Events.evtTournamentInfoUpdated, self.RefreshView, self)
    self:AddLuaEvent(Server.TournamentServer.Events.evtCommanderAbilitiesPercentageUpdated, self.InitAbilityData, self)

end

function CommanderMainPanel:AddMouseButtonDownEvent()
    loginfo("CommanderMainPanel:AddMouseButtonDownEvent")
    local gameInst=GetGameInstance()
    if gameInst then
        self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(GetGameInstance()).OnHandleMouseButtonDownEvent:Add(self._OnMouseButtonDown,self)
    end
end

function CommanderMainPanel:RemoveMouseButtonDownEvent()
    loginfo("CommanderMainPanel:RemoveMouseButtonDownEvent")
    if self._mouseButtonDownHandle then
        local gameInst=GetGameInstance()
        if gameInst then
            self._mouseButtonDownHandle=UDFMGameHudDelegates.Get(gameInst).OnHandleMouseButtonDownEvent:Remove(self._mouseButtonDownHandle)
        end
    end
end

function CommanderMainPanel:RemoveAllActionHandle()
    loginfo("CommanderMainPanel:RemoveAllActionHandle")
    if self._openRecordPanelActionHandle then
        self:RemoveInputActionBinding(self._openRecordPanelActionHandle)
        self._openRecordPanelActionHandle=nil
    end
    if self._openRewardPanelActionHandle then
        self:RemoveInputActionBinding(self._openRewardPanelActionHandle)
        self._openRewardPanelActionHandle=nil
    end
    if self._jumpModeSelectorActionHandle then
        self:RemoveInputActionBinding(self._jumpModeSelectorActionHandle)
        self._jumpModeSelectorActionHandle=nil
    end
end

function CommanderMainPanel:RefreshView()
    loginfo("CommanderMainPanel:RefreshView")
    self:InitSeasonName()
    self:InitLevelName()
    self:InitAbilityData()
end

function CommanderMainPanel:RefreshNewUnlockReddot()
    loginfo("CommanderMainPanel:RefreshNewUnlockReddot")
    if not Server.TipsRecordServer:GetBoolean(Server.TipsRecordServer.keys.isCommanderUnlockedOnce) then
        Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.isCommanderUnlockedOnce,true)
        Server.TournamentServer:BroadcastUpdateReddot()
    end

end

function CommanderMainPanel:CheckToShowUnlockTips()
    loginfo("CommanderMainPanel:CheckToShowUnlockTips")
    local isUnlock,unlockTips=Module.BattlefieldEntry:CheckIsCommanderUnlock()
    if not isUnlock then
        Module.CommonTips:ShowSimpleTip(unlockTips,5)
    end
end

function CommanderMainPanel:OpenRankRecorderPanel()
    loginfo("CommanderMainPanel:OpenRankRecorderPanel")
    Facade.UIManager:AsyncShowUI(UIName2ID.CommanderRecordPanel)

end

function CommanderMainPanel:OpenRankRewardPanel()
    loginfo("CommanderMainPanel:OpenRankRewardPanel")
    Module.Tournament:ShowRewardPanel(Server.TournamentServer:GetCommanderMajorLevel(),Module.Ranking.Config.RankModeType.Commander)

end

function CommanderMainPanel:OpenLevelPreviewPanel()
    loginfo("CommanderMainPanel:OpenLevelPreviewPanel")

    Facade.UIManager:AsyncShowUI(UIName2ID.TournamentPreviewWindow,nil,nil,TournamentConfig.ETournamentPreviewType.CommanderLevelPreview)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.LevelDesc)

end

function CommanderMainPanel:OpenSeasonDescPanel()
    loginfo("CommanderMainPanel:OpenSeasonDescPanel")
    self._wtSeasonDescBtn:SetIsChecked(false,false)
    Facade.UIManager:AsyncShowUI(UIName2ID.TournamentPreviewWindow,nil,nil,TournamentConfig.ETournamentPreviewType.CommanderSeasonDesc)
    LogAnalysisTool.SignButtonClicked(TournamentConfig.ButtonLogId.SeasonDesc)

end

function CommanderMainPanel:JumpToModeSelector()
    loginfo("CommanderMainPanel:JumpToModeSelector")
    local commanderGroupId=nil
    for k,v in pairs(Server.GameModeServer:GetAvailableTDMGroup() or {}) do
        for i,j in pairs(Server.GameModeServer:GetTDMMapInGroup(v) or {}) do
            if Module.GameMode:GetGameRuleByMatchModeId(j.match_id)==MatchGameRule.TDMCommanderGameRule then
                commanderGroupId=v
                break
            end
        end
        if commanderGroupId then
            break
        end
    end
    if commanderGroupId then
        Module.BattlefieldEntry:OpenModeSelector(nil, commanderGroupId, true,function()Module.Tournament:CloseMainPanel()end)
    else
        Module.CommonTips:ShowSimpleTip(Module.Tournament.Config.Loc.ModeNotOpen)
    end

end

function CommanderMainPanel:InitSeasonName()
    loginfo("CommanderMainPanel:InitSeasonName")
    local seasonConfig=Module.Tournament:GetSeasonConfigBySerial(Server.TournamentServer:GetCurSerial())
    if seasonConfig then
        local startTimeStr=""
        local endTimeStr=""
        local timeOffset=IsBuildRegionCN() and 8*3600 or 0
        if seasonConfig.TimeDisplayDigit==2 then
            startTimeStr=TimeUtil.TransUnixTimestamp2YYMMStr(Server.TournamentServer:GetUIStartTime()+timeOffset,"YY/MM")
            endTimeStr=TimeUtil.TransUnixTimestamp2YYMMStr(Server.TournamentServer:GetUIEndTime()+timeOffset,"YY/MM")
        else
            startTimeStr=TimeUtil.TransUnixTimestamp2YYMMDDStr(Server.TournamentServer:GetUIStartTime()+timeOffset,"YY/MM/DD")
            endTimeStr=TimeUtil.TransUnixTimestamp2YYMMDDStr(Server.TournamentServer:GetUIEndTime()+timeOffset,"YY/MM/DD")
        end
        self._wtSeasonDurationText:SetText(StringUtil.Key2StrFormat(Module.Tournament.Config.Loc.SeasonDuration,{startTime=startTimeStr,endTime=endTimeStr}))
        self._wtSeasonNameText:SetText(seasonConfig.Name or "?")
        self._wtSeasonDescText:SetText(seasonConfig.SeasonDescription or "?")
    end
    
end

function CommanderMainPanel:InitLevelName()
    loginfo("CommanderMainPanel:InitLevelName")
    if not Server.TournamentServer:GetCommanderHasAttended() then--无段位
        self._wtNotAttendedText:SelfHitTestInvisible()
        self._wtLevelNameText:Collapsed()
        self._wtRankStarItem:Collapsed()
        self._wtRankScoreTitle:Collapsed()
        self._wtRankScoreText:Collapsed()
        for k,v in pairs(self._progressList) do
            v:Collapsed()
        end
        Facade.UIManager:RemoveSubUIByParent(self,self._wtRankWidgetSlot)
        local weakIns=Facade.UIManager:AddSubUI(self,Module.Tournament.Config.CommanderRankLevel2BGMap[0],self._wtRankWidgetSlot)
        local uiIns=getfromweak(weakIns)
        if uiIns then
            uiIns:SetRankIconNone()
        end
    else
        local minorLevelInfo=Module.Tournament:GetCommanderRankDataByMinor(Server.TournamentServer:GetCommanderMinorLevel())
        if minorLevelInfo then
            self._wtLevelNameText:SelfHitTestInvisible()
            self._wtRankStarItem:SelfHitTestInvisible()
            self._wtRankScoreTitle:SelfHitTestInvisible()
            self._wtRankScoreText:SelfHitTestInvisible()
            self._wtNotAttendedText:Collapsed()
            self._wtLevelNameText:SetText(minorLevelInfo.Name)
            local rankScore=Server.TournamentServer:GetCommanderRankScore()
            for k,v in pairs(self._progressList) do
                v:Collapsed()
            end
            Facade.UIManager:RemoveSubUIByParent(self,self._wtRankWidgetSlot)
            local weakIns=Facade.UIManager:AddSubUI(self,Module.Tournament.Config.CommanderRankLevel2BGMap[minorLevelInfo.TierTypeID],self._wtRankWidgetSlot)
            local uiIns=getfromweak(weakIns)
            if uiIns then
                uiIns:SetRankIconByScore(rankScore)
            end
            if minorLevelInfo.TierTypeID==Module.Tournament:GetCommanderDefinedMaxMajorLevel() then--传奇段位
                local starNum=Module.Tournament:GetCommanderStarNumByScore(rankScore)
                self._wtRankScoreText:SetText(string.format(Module.Ranking.Config.Loc.RankScoreDivide,rankScore,minorLevelInfo.MinPoint+Module.Tournament:GetCommanderRankConstant()*starNum))
                if self._progressList[1] then
                    self._progressList[1]:SelfHitTestInvisible()
                    self._progressList[1]:BPSetPercent(Module.Tournament:GetCommanderLastStarPercentByScore(rankScore))
                end
                self._wtRankStarItem:SetRankStarNum(-1)
                self._wtRankStarItem:SetRankStarState(starNum)
            else
                local starNum=Module.Tournament:GetCommanderStarNumByScore(rankScore)
                local nextRankData=Module.Tournament:GetCommanderNextRankDataByScore(rankScore)
                local maxPoint=nextRankData and nextRankData.MinPoint or rankScore
                self._wtRankScoreText:SetText(string.format(Module.Tournament.Config.Loc.RankScoreDivide,rankScore,maxPoint))
                local starsDivided=minorLevelInfo.StarsDivided or 1
                for i=1,starsDivided do--进度条是用小进度条拼的，用几个显示几个
                    if self._progressList[i] then
                        self._progressList[i]:SelfHitTestInvisible()
                        if i<starNum then
                            self._progressList[i]:BPSetPercent(1)
                        elseif i>starNum then
                            self._progressList[i]:BPSetPercent(0)
                        else
                            local percent=Module.Tournament:GetCommanderLastStarPercentByScore(rankScore)
                            self._progressList[i]:BPSetPercent(percent)
                        end
                    end
                end
                self._wtRankStarItem:SetRankStarNum(starsDivided)
                self._wtRankStarItem:SetRankStarState(starNum)
            end
        else
            logerror("CommanderMainPanel:InitLevelName minorLevelInfo is nil!!!")
        end
    end
end

function CommanderMainPanel:InitAbilityData()
    loginfo("CommanderMainPanel:InitAbilityData")
    local abilitiesPercentageMap=Server.TournamentServer:GetCommanderAbilitiesPercentageMap()
    local levelDivideParam=Module.Tournament:GetCommanderAbilityLevelParam()
    local abilityData=Server.TournamentServer:GetCommanderAbilityData()
    local commanderLevel=self:TransPercent2Level(abilitiesPercentageMap and abilitiesPercentageMap[RankDataType.TDM_VICTORY_UNIT_COMMAND_SCORE] or 1, levelDivideParam)
    local infantryLevel=self:TransPercent2Level(abilitiesPercentageMap and abilitiesPercentageMap[RankDataType.TDM_VICTORY_UNIT_INFANTRY_KILL_SCORE] or 1, levelDivideParam)
    local vehicleLevel=self:TransPercent2Level(abilitiesPercentageMap and abilitiesPercentageMap[RankDataType.TDM_VICTORY_UNIT_VEHICLE_KILL_SCORE] or 1, levelDivideParam)
    local rescueLevel=self:TransPercent2Level(abilitiesPercentageMap and abilitiesPercentageMap[RankDataType.TDM_VICTORY_UNIT_INFANTRY_RESCUE_SCORE] or 1, levelDivideParam)
    local commanderIcon=Module.Tournament.Config.CommanderLevel2IconMap[commanderLevel]
    local infantryIcon=Module.Tournament.Config.InfantryLevel2IconMap[infantryLevel]
    local vehicleIcon=Module.Tournament.Config.VehicleLevel2IconMap[vehicleLevel]
    local rescueIcon=Module.Tournament.Config.RescueLevel2IconMap[rescueLevel]
    local commanderDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityCommander_Text") or "?"
    local infantryDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityVehicleKill_Text") or "?"
    local vehicleDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityInfantryKill_Text") or "?"
    local rescueDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityRescue_Text") or "?"
    local commanderTipDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityCommanderMarker") or "?"
    local infantryTipDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityVehicleKillMarker") or "?"
    local vehicleTipDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityInfantryKillMarker") or "?"
    local rescueTipDesc=Module.Tournament:GetCommanderWordParamByKey("AbilityRescueMarker") or "?"
    local totalGameCount=abilityData and abilityData.TotalFight or 0

    self._wtRankCountText:SetText(StringUtil.Key2StrFormat(Module.Tournament.Config.Loc.TotalRankCount,{count=totalGameCount}))
    self._wtAbilityItemCommander:SetAbilityInfo({desc=commanderDesc,icon=commanderIcon,tipDesc=commanderTipDesc})
    self._wtAbilityItemInfantry:SetAbilityInfo({desc=infantryDesc,icon=infantryIcon,tipDesc=infantryTipDesc})
    self._wtAbilityItemVehicle:SetAbilityInfo({desc=vehicleDesc,icon=vehicleIcon,tipDesc=vehicleTipDesc})
    self._wtAbilityItemRescue:SetAbilityInfo({desc=rescueDesc,icon=rescueIcon,tipDesc=rescueTipDesc})
    
end

function CommanderMainPanel:TransPercent2Level(percent,levelDivideParam)
    local paramlevel1=levelDivideParam[1] or 0
    local paramlevel2=levelDivideParam[2] or 0
    local paramlevel3=levelDivideParam[3] or 0
    if percent*100<=paramlevel1 then
        return 1
    elseif percent*100<=paramlevel2 then
        return 2
    else
        return 3
    end

end

function CommanderMainPanel:_OnMouseButtonDown(mouseEvent)
    local screenPosition=mouseEvent:GetScreenSpacePosition()

end

-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CommanderMainPanel:OnAnimFinished(anim)
end

-- BEGIN MODIFICATION @ VIRTUOS
function CommanderMainPanel:_InitGamepadInputs()
    if not IsHD() then
        return
    end

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
        self._navGroup:AddNavWidgetToArray(self._wtLeftPanel)

        self._navGroup:MarkIsStackControlGroup()
        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end

    if not self._gamepadSummaryList then
        self._gamepadSummaryList = {
            {actionName = "TournamentPreview", func = self.OpenLevelPreviewPanel, caller = self},
            {actionName = "Ranking_SeasonRules", func = self.OpenSeasonDescPanel, caller = self},
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)
    end
    self._wtRankRecorderBtn:SetKeyIconByActionName("Common_ButtonLeft_Gamepad",true)
    self._wtRankRewardBtn:SetKeyIconByActionName("Common_ButtonTop",true)
    self._wtJumpToMapBtn:SetDisplayInputAction("Common_ButtonBottom_Gamepad",true,0,true)
    if not self._openRecordPanelActionHandle then
        self._openRecordPanelActionHandle=self:AddInputActionBinding("Common_ButtonLeft_Gamepad",EInputEvent.IE_Pressed,self.OpenRankRecorderPanel,self,EDisplayInputActionPriority.UI_Stack)
    end
    if not self._openRewardPanelActionHandle then
        self._openRewardPanelActionHandle=self:AddInputActionBinding("Common_ButtonTop",EInputEvent.IE_Pressed,self.OpenRankRewardPanel,self,EDisplayInputActionPriority.UI_Stack)
    end
    if not self._jumpModeSelectorActionHandle then
        self._jumpModeSelectorActionHandle=self:AddInputActionBinding("Common_ButtonBottom_Gamepad",EInputEvent.IE_Pressed,self.JumpToModeSelector,self,EDisplayInputActionPriority.UI_Stack)
    end
end

function CommanderMainPanel:_DisableGamepadInputs()
    if not IsHD() then
        return
    end
    
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end

    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end
end
-- END MODIFICATION

return CommanderMainPanel    
