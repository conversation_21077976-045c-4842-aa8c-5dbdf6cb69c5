----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMWeaponAssembly)
----- LOG FUNCTION AUTO GENERATE END -----------

---阿萨拉巡旅
---@class ActivityTaraMain : LuaUIBaseView
local ActivityTaraMain = ui("ActivityTaraMain")
local ActivityTaraHead  = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraHead"
local ActivityTaraItem1 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem1"
local ActivityTaraHead2 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraItem2"
local ActivityTaraPanel1 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPanel1"
local ActivityTaraPanel2 = require "DFM.Business.Module.ActivityModule.UI.Tara.ActivityTaraPanel2"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UTextStyleBlueprintLib = import "TextStyleBlueprintLib"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local Deep = require "DFM.Business.DataStruct.Common.Base.Deep"
local EConsumeMouseWheel = import "EConsumeMouseWheel"
local CommonWidgetConfig = require "DFM.Business.Module.CommonWidgetModule.CommonWidgetConfig"
local EComp = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder = CommonWidgetConfig.EIVCompOrder
local Config = Module.Activity.Config

ETaraHandleType = {
    None    = 0, --默认
    Add     = 1, --添加
    Send    = 2, --发送
    Refresh = 3, --刷新
    Comment = 4, --评论
    Switch  = 5, --切换
    Anim    = 6, --阶梯动画
    Anim2   = 7, --阶梯动画二级
    EnterAnim = 8,--入场动画
    Hide    = 9, --隐藏所有
    Receive = 10,--领取奖励
}

function ActivityTaraMain:Ctor()
    self._wtState = self:Wnd("WBP_CommonButtonV2S2_37" , DFCommonButtonOnly)
    self._wtFHead = self:Wnd("NewWidgetBlueprint"      , ActivityTaraHead)
    self._wtToday = self:Wnd("DFRichTextBlock_0"       , UITextBlock)
    self._wtFCard = self:Wnd("DFImage_83"              , UIImage)
    self._wtTitle = self:Wnd("DFTextBlock"             , UITextBlock)
    self._wtFIcon = self:Wnd("DFImage_496"             , UIImage)
    self._wtFName = self:Wnd("DFTextBlock_168"         , UITextBlock)
    self._wtDFMax = self:Wnd("DFTextBlock_309"         , UITextBlock)
    self._wtDFGou = self:Wnd("WBP_SlotCompGet"         , UIWidgetBase)
    self._wtMaxTp = self:Wnd("DFRichTextBlock"         , UITextBlock)
    self._wtState:Event("OnClicked", self._OnReqClicked, self)
    self._wtState:Visible()
    self._wtHeadr = self:Wnd("DFCanvasPanel_45"        , UIWidgetBase)
    self._wtHeadIns = Module.ReddotTrie:CreateReddotIns(self._wtHeadr, nil, nil, nil, FVector2D(0, 12))
    --奖励提示
    self._wtPanls = self:Wnd("DFCanvasPanel_51"        , UIWidgetBase)
    self._wtEnBtn = self:Wnd("DFButton_49"             , UIButton)
    self._wtEnTxt = self:Wnd("DFTextBlock_185"         , UITextBlock)
    self._wtEnBtn:Event("OnClicked", self._OnToFirstrewardClicked, self)
    --cdn
    self._wtAdImg = self:Wnd("DFCDNImage_68"           , DFCDNImage)
    self._wtBgImg = self:Wnd("DFImage_159"             , UIImage)
    self._wtStrokeImg = self:Wnd("Image_2"             , UIImage)
    --切换/发送
    self._wtSwitchItem = self:Wnd("WBP_PatrolAsala_SwitchBlogger_Q1ZNK", ActivityTaraPanel1)
    self._wtSendNeItem = self:Wnd("WBP_PatrolAsala_InputField_Q1ZNK"   , ActivityTaraPanel2)
    self._wtReddotsIns = Module.ReddotTrie:CreateReddotIns(self:Wnd("Red", UIWidgetBase))
    --解锁
    self._wtLockPnael = self:Wnd("Lock", UIWidgetBase)
    self._wtLockTipes = self:Wnd("DFTextBlock_2", UITextBlock)
    self._wtLockNoany = self:Wnd("WBP_Common_NoAnything_1", UIWidgetBase)
    self._wtLockBox = UIUtil.WndWaterfallScrollBox(self, "DFScrollGridBox", self._OnLockWaterfallCount, self._OnLockWaterfallWidget)
    self._wtLineImg = self:Wnd("DFImage", UIImage)
    --名片
    self._wtbusCardBtn = self:Wnd("DFButton_16", UIButton)
    self._wtbusCardBtn:Event("OnClicked"   , self._OnClicked, self)
    self._wtbusCardBtn:Event("OnHovered"   , self._OnCardHovered, self)
    self._wtbusCardBtn:Event("OnUnHovered" , self._OnCardUnHovered, self)
    --手柄第三组
    self._wtHandleGroup3 = self:Wnd("DFCanvasPosReContainer_0", UIWidgetBase)
    --里程碑
    self._wtMilestoneBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_150", self._OnWaterfallCount, self._OnWaterfallWidget)
    --waterfull
    self._wtWaterfallBox = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_0", self._OnWeChatWaterfallCount, self._OnWeChatWaterfallWidget)
    self._wtWaterfallBox.OnScrolling:Add(CreateCPlusCallBack(self._OnScrolling, self))
    self._wtWaterfallBox.OnReachBottom:Add(CreateCPlusCallBack(self._OnScrolling, self))
    --光效
    self._wtLightEfficiy = self:Wnd("DFCanvasPanel_VFX_2", UIWidgetBase)

    self._wtCheck = self:Wnd("wtCommonCheckInstruction", DFCheckBoxOnly)
    if IsHD() then
        self._wtAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor_92", self._OnHovered, self._OnUnhovered, nil)
    else
        self._wtAnchor = self:Wnd("DFTipsAnchor_92", UIWidgetBase)
        self._wtCheck:SetCallback(self._OnCheckChanged, self)
    end
    self:Wnd("CanvasPanel_0", UIWidgetBase):Visible()
    self._wtHotzone = {
        self._wtMilestoneBox,
        self._wtWaterfallBox,
    }
    self:InjectLua()
end

function ActivityTaraMain:_OnLockWaterfallCount()
    if self._tipsRewards then
        return #self._tipsRewards
    end
    return 0
end

function ActivityTaraMain:_OnLockWaterfallWidget(position, itemWidget)
    local index = position + 1
    if itemWidget and self._tipsRewards then
        local itemData = ActivityLogic.GetWeaponSkinItem(self._tipsRewards[index])
        if itemData then
            self._btn = nil
            itemWidget:InitItem(itemData)
            self:_SetFramework(itemWidget, false)
            itemWidget:BindCustomOnClicked(SafeCallBack(self._OnRewardClicked, self, itemData, itemWidget))
            itemWidget:SetCppValue("bIsFocusable", true)
        end
    end
end

function ActivityTaraMain:_OnRewardClicked(itemData, btn)
    if itemData and btn then
        self:_SetFramework(self._btn, false)
        self._btn = btn
        self._itemData = itemData
        self:_SetFramework(btn, true)
        Module.ItemDetail:OpenItemDetailPanel(itemData, btn)
    end
end

function ActivityTaraMain:_SetFramework(btn, frame)
    if btn then
        local uiNavId = UIName2ID.IVItemSelectedComponent
        local CompId, SlotPos = EComp.PutInSafebox, EIVSlotPos.BorderLayer
        local addWidget = btn:FindOrAdd(CompId, uiNavId, SlotPos, EIVCompOrder.Order1)
        btn:EnableComponent(CompId, frame)
    end
end

function ActivityTaraMain:SetSuspendedFrame()
    self._wtStrokeImg:SetRenderOpacity(1)
end

function ActivityTaraMain:_OnCardHovered()
    --判断详情页类型
    if self:IsExistCardMainDetail() then
        return
    end
    self:SetImageBorder(true)
    self._wtStrokeImg:SetRenderOpacity(0.3)
end

function ActivityTaraMain:_OnCardUnHovered()
    if self:IsExistCardMainDetail() then
        return
    end
    self:SetImageBorder(false)
end

function ActivityTaraMain:_OnToFirstrewardClicked()
    self:_AwardReceivingTips(true)
end

--滑动触发
function ActivityTaraMain:_OnScrolling()
    --检测滑动情况
    self:_AwardReceivingTips()
end

function ActivityTaraMain:_OnWeChatWaterfallCount()
    if self._lines then
        return #self._lines
    end
    return 0
end

function ActivityTaraMain:_OnWeChatWaterfallWidget(position, itemWidget)
    local index = position
    local list = self._lines
    if itemWidget and list then
        local data = list[index]
        itemWidget:InitData(self._activityID, data, index, self._weChatIndex, self._hero, self._heros, #self._lines)
        self:_AwardReceivingTips()
    end
end

function ActivityTaraMain:_OnReqClicked()
    local hero = self._hero
    if hero.rawState == 0 then
        local confirmHandle = function ()
            Server.ActivityServer:SendTaraSwitchReq(self._activityID, hero.heroId)
        end
        local name = nil
        for index, item in ipairs(self._heros or {}) do
            if item.state == 1 then
                local count = 0
                for _, wechatMoment in ipairs(item.items or {}) do
                    if wechatMoment.count and wechatMoment.count > count then
                        count = wechatMoment.count
                    end
                end
                if item.count and item.count < count then
                    name = item.name
                end
                break
            end
        end
        Module.CommonTips:ShowConfirmWindow(ActivityLogic.GetText(Config.Loc.ThisWillInterrupt, name or ""), confirmHandle)
    end
end

function ActivityTaraMain:_OnClicked()
    local hero = self._hero
    if self._activityID and hero then
        if self._isAvailable then
            Module.Reward:EnableNTFCall("Hero", false)--兜底方案(解决弹窗多弹的问题)
            Server.ActivityServer:SendTaraMaxRewardReq(self._activityID, hero.heroId)
        else
            --打开详情页
            if self._cardItem then
                self:SetSuspendedFrame()
                Module.ItemDetail:OpenItemDetailPanel(self._cardItem, self._wtbusCardBtn)--详情页
            end
        end
    end
end

function ActivityTaraMain:IsExistCardMainDetail()
    local ItemDetail = Module.ItemDetail:GetMainPanelHandle()
    local itemData = self._cardItem
    if itemData and ItemDetail then
        local extraParams = ItemDetail.extraParams
        if extraParams then
            return itemData == extraParams[1]
        end
    end
end

function ActivityTaraMain:_OnHovered()
    self:_OnUnhovered()
    if self._tips and self._hero then
        local contents = {}
        local title = {
            id = UIName2ID.Assembled_CommonMessageTips_V8,
            background = 1,
            data = {
                textContent = Config.Loc.ScoringBehavior,
                styleRowId  = "C002",
                isLineIcon = true,
            },
        }
        table.insert(contents, title)
        for index, value in ipairs(self._tips or {}) do
            table.insert(contents, {
                id = UIName2ID.Assembled_CommonMessageTips_V5,
                background = 1,
                data = {
                    textContent = ActivityLogic.GetText(Config.Loc.ScoreContent, value.desc),
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            })
        end
        local list = {
            {
                id = UIName2ID.Assembled_CommonMessageTips_V8,
                background = 1,
                data = {
                    textContent = ActivityLogic.GetText(Config.Loc.ScoreLimit, self._hero.todaymax),
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            },
            {
                id = UIName2ID.Assembled_CommonMessageTips_V5,
                background = 1,
                data = {
                    textContent = Config.Loc.AllBloggers,
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            },
            {
                id = UIName2ID.Assembled_CommonMessageTips_V5,
                background = 1,
                data = {
                    textContent = Config.Loc.TheUpperLimitIs,
                    styleRowId  = "C002",
                    isLineIcon = true,
                },
            },
        }
        for index, value in ipairs(list or {}) do
            table.insert(contents, value)
        end
        self._handle = Module.CommonTips:ShowAssembledTips(contents, self._wtAnchor)
    end
end

function ActivityTaraMain:_OnUnhovered()
    if self._handle and self._handle.GetUIIns then
        self._wtCheck:SetIsChecked(false, false)
        Module.CommonTips:RemoveAssembledTips(self._handle, self._wtAnchor)
        self._handle = nil
    end
end

function ActivityTaraMain:_OnCheckChanged(isBool)
    if isBool then
        self:_OnHovered()
    else
        self:_OnUnhovered()
    end
end

function ActivityTaraMain:OnInitExtraData(activityID, fGetCommonCtrl)
    self._activityID = activityID
    self._fGetCommonCtrl = fGetCommonCtrl
    self._hero = nil
end

function ActivityTaraMain:_SetMianHeadline(isBool)
    local btn = nil
    if self._fGetCommonCtrl then
        btn = self._fGetCommonCtrl(EActivityCommCtrl.MainInfoPaddingBox)
    end
    if btn then
        if isBool then
            btn:SelfHitTestInvisible()
        else
            btn:Collapsed()
        end
    end
end

function ActivityTaraMain:_InitData()
    if self._activityID then
        local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
        if activityInfo then
            self._desc = activityInfo.details
            local info = activityInfo.ahsarah_travel_info
            self._cdn  = activityInfo.info1
            if info then
                local heroIds = {}
                self._heros = {}
                --获取玩家头像
                local GetUrl = function()
                    local url = Server.RoleInfoServer.picUrl
                    if url == "" then
                        return Server.SDKInfoServer:GetPictureUrl()
                    else
                        return url
                    end
                end
                --整理干员ID
                for index, hero in ipairs(info.hero_info or {}) do
                    heroIds[hero.hero_id] = hero
                end
                self._heroHeads = heroIds
                for index, data in ipairs(info.line_infos or {}) do
                    local item = {}
                    local hero = heroIds[data.hero_id]
                    if hero then
                        item.heroId = data.hero_id  --干员ID
                        item.name   = hero.hero_name--干员名称
                        item.path   = hero.image    --头像本地路径
                    end
                    item.items = {}
                    for _, message in ipairs(data.messages or {}) do
                        local wechatMoments = {}
                        wechatMoments.heroHeads  = heroIds--干员头像
                        wechatMoments.playerId   = info.self_id                   --玩家ID
                        wechatMoments.playerName = Server.RoleInfoServer.nickName --玩家昵称
                        wechatMoments.playerPath = GetUrl()                       --玩家头像
                        wechatMoments.id       = message.id           --唯一Id
                        wechatMoments.heroId   = data.hero_id         --干员Id(自己)
                        wechatMoments.count    = message.progress     --进度节点
                        wechatMoments.title    = message.title        --标题
                        wechatMoments.desc     = message.desc         --描述
                        wechatMoments.icons    = message.images       --图标数组
                        wechatMoments.time     = message.time         --时间
                        wechatMoments.likes    = message.like_hero_ids--点赞干员ID
                        wechatMoments.goldId   = activityInfo.gold_id --点赞代币id
                        wechatMoments.goldNum  = activityInfo.gold_num--点赞代币数量
                        wechatMoments.contents = message.reply_choices--可回复内容数组
                        wechatMoments.replied  = message.replied      --是否回复
                        wechatMoments.rewards  = message.show_rewards --未解锁展示奖励
                        wechatMoments.news = {}
                        for _, relay in ipairs(message.relays or {}) do
                            local new = {}
                            new.heroHeads  = heroIds--干员头像
                            new.playerId   = info.self_id                   --玩家ID
                            new.playerName = Server.RoleInfoServer.nickName --玩家昵称
                            new.playerPath = GetUrl()                       --玩家头像
                            new.wechatId = message.id         --朋友圈Id
                            new.id       = relay.id           --唯一Id
                            new.heroId   = relay.hero_id      --干员Id
                            new.time     = relay.time         --时间
                            new.comment  = relay.desc         --评论
                            new.rewards  = relay.rewards      --奖励
                            new.received = relay.received     --是否领取
                            new.atId     = relay.at_id        --回复干员ID
                            new.replied  = relay.replied      --是否回复
                            new.contents = relay.reply_choices--可回复内容数组
                            table.insert(wechatMoments.news, new)
                        end
                        table.insert(item.items, wechatMoments)
                    end
                    local state = data.state
                    local count = 0
                    for _, value in ipairs(item.items or {}) do
                        if value.count >= count then
                            count = value.count
                        end
                    end
                    if data.progress >= count and state == 1 then
                        state = 2
                    end
                    item.heroHeads  = heroIds--干员头像
                    item.act_id   = self._activityID
                    item.title    = data.name              --标题
                    item.rawState = data.state             --同行状态(原始状态)
                    item.state    = state                  --同行状态
                    item.count    = data.progress          --进度
                    item.received = data.received          --是否领取
                    item.rewards  = data.final_rewards     --奖励数组
                    item.todaymin = info.today_progress    --今日进度
                    item.todaymax = info.daily_max_progress--今日上限
                    item.tips     = info.score_descs       --提示描述数组
                    table.insert(self._heros, item)
                end
            end
        end
    end
end

function ActivityTaraMain:_GetCurHero()
    for index, hero in ipairs(self._heros or {}) do
        if hero.rawState == 1 then
            return hero
        end
    end
    return nil
end

function ActivityTaraMain:_IsNotStarted()
    for index, hero in ipairs(self._heros or {}) do
        if hero.rawState == 0 then
            return true
        end
    end
    return false
end

function ActivityTaraMain:_IsFinishedCurHero()
    for index, hero in ipairs(self._heros or {}) do
        if hero.rawState == 1 then
            local count = 0
            for _, wechatMoment in ipairs(hero.items or {}) do
                if wechatMoment.count and wechatMoment.count > count then
                    count = wechatMoment.count
                end
            end
            if hero.count and hero.count >= count and hero.received then
                return true
            end
            break
        end
    end
    return false
end

function ActivityTaraMain:_OpenSelectHeroPop()
    Facade.UIManager:AsyncShowUI(UIName2ID.ActivityTaraPop1, nil, self, self._activityID, self._heros)
end

function ActivityTaraMain:_RefreshAvatar(hero)
    if hero then
        self._wtFHead:InitData(self._activityID, hero, nil, 1)
        self._wtFHead:SetHeadBtn(true)
        self._wtFName:SetText(ActivityLogic.GetText(hero.name))
        self._wtTitle:SetText(ActivityLogic.GetText(hero.title))
        --同行3状态
        local stateStr = nil
        if hero.rawState == 0 then
            stateStr = Config.Loc.ActUITextBlock[11]
        elseif hero.rawState == 1 then
            stateStr = Config.Loc.ActUITextBlock[7]
        else
            stateStr = Config.Loc.ActUITextBlock[8]
        end
        if stateStr then
            self._wtState:SetMainTitle(stateStr)
        end
        if hero.rawState == 0 then
            self._wtState:SetIsEnabled(true)
            self._wtState:Visible()
        else
            self._wtState:SetIsEnabled(false)
            self._wtState:HitTestInvisible()
        end
    end
end

function ActivityTaraMain:_RefreshTips(hero)
    if hero then
        self._tips = hero.tips
        self._wtDFMax:SetText(hero.count or "")
        self._wtToday:SetText(ActivityLogic.GetText(Config.Loc.TodaysUpperLimit, hero.todaymin, hero.todaymax))
        UTextStyleBlueprintLib.Blueprint_SetTextStyle(self._wtToday, "C002")
    end
end

function ActivityTaraMain:_RefreshBusinessCard(hero)
    if hero then
        local count = self:_GetUnLockMaxIndex(hero)
        --名片显示
        for index, prop in ipairs(hero.rewards or {}) do
            local itemData = ActivityLogic.GetWeaponSkinItem(prop)
            if itemData then
                self._cardItem = itemData
                self._wtFCard:AsyncSetImagePath(itemData.itemIconPath or "", true)
                break
            end
        end
        --按钮状态
        local maxCount = self:_GetMaxIndex(hero)
        if maxCount then
            self._wtMaxTp:SetText(ActivityLogic.GetText(Config.Loc.AllBlogsToRewards, count, maxCount))
            self._wtBgImg:SelfHitTestInvisible()
            if hero.received then
                --已领取
                self._wtDFGou:SetVisibility(ESlateVisibility.HitTestSelfOnly)
                self._wtReddotsIns:SetReddotVisible(false)
                self._wtMaxTp:Collapsed()
                self._wtBgImg:Collapsed()
                self:PlayAnim()
            else
                --可领取
                if count == maxCount then
                    self._wtReddotsIns:SetReddotVisible(true, EReddotType.Normal)
                    self:PlayAnim(self.Anim_pickup_in, false)
                    self._wtMaxTp:SetText(StringUtil.Key2StrFormat("<customstyle color=\"Color_Highlight01\">{receive}</>", {receive = Config.Loc.YellowAvailable}))
                else
                    self._wtReddotsIns:SetReddotVisible(false)
                    self:PlayAnim()
                end
                self._wtDFGou:SetVisibility(ESlateVisibility.Collapsed)
            end
            self._isAvailable = not hero.received and count == maxCount
        end
    end
end

function ActivityTaraMain:_GetDefaultIndex(hero)
    local defaultIndex = 1
    if hero then
        local maxIndex = self:_GetUnLockMaxIndex(hero)
        local firstReddotIndex = self:_GetTheFirstMilestoneReddot(hero)
        if maxIndex < firstReddotIndex then
            defaultIndex = maxIndex
        else
            defaultIndex = firstReddotIndex
        end
    end
    return defaultIndex
end

function ActivityTaraMain:_GetMaxIndex(hero)
    if hero and hero.items then
        return #hero.items
    end
    return 0
end

function ActivityTaraMain:_GetUnLockMaxIndex(hero)
    local defaultIndex = 1
    if hero then
        for index, item in ipairs(hero.items or {}) do
            if item.count and hero.count and hero.count >= item.count then
                defaultIndex = index
            end
        end
    end
    return defaultIndex
end

function ActivityTaraMain:_InitPanel()
    local hero = self:_GetCurHero()
    --打开干员选择界面
    if hero == nil or (self:_IsFinishedCurHero() and self:_IsNotStarted()) then
        self:_OpenSelectHeroPop()
    end
    self._hero = hero
    if hero then
        self._defaultIndex = self:_GetDefaultIndex(hero)
        self:_InitAdaptationHandle()
        self:_RefreshInterface(hero)
    end
end

function ActivityTaraMain:_GetFristHero()
    for index, hero in ipairs(self._heros or {}) do
        return hero
    end
end

function ActivityTaraMain:_RefreshInterface(hero)
    if hero then
        self:_LoadingBg()
        self:_RefreshAvatar(hero)
        self:_RefreshTips(hero)
        self:_RefreshBusinessCard(hero)
        self:_SetHeadReddot()
        self:_OpenTaraPanel(self._activityID)
        self:_RefresMilestoneBox(hero)
        self:_RefreshWaterBox(self._defaultIndex)
    end
end

function ActivityTaraMain:_RefresMilestoneBox(hero)
    if hero then
        self._list = hero.items
        self._wtMilestoneBox:RefreshAllItems()
    end
end

function ActivityTaraMain:_IsVisibe()
    return self:GetVisibility() ~= ESlateVisibility.Collapsed
end

function ActivityTaraMain:_LoadingBg()
    if self._cdn then
        local url = string.format("Resource/Texture/Activity/%s", self._cdn)
        self._wtAdImg:SetCDNImage(url, true, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    end
end

function ActivityTaraMain:_GetTheFirstMilestoneReddot(hero)
    local act_id = self._activityID
    local defIndex = 1
    if hero and act_id then
        for index, item in ipairs(hero.items or {}) do
            defIndex = index
            --评论奖励
            for i, new in ipairs(item.news or {}) do
                if not new.received and new.rewards and #new.rewards> 0 then
                    return index
                end
            end
            --新解锁
            if act_id and hero.heroId and item.count and hero.count and hero.count >= item.count then
                local key = act_id..hero.heroId..item.count
                local isNewReddot = Server.ActivityServer:GetUserInt(key, 0)
                if isNewReddot == 1 then
                    return index
                end
            end
        end
    end
    return defIndex
end

--头像红点
function ActivityTaraMain:_SetHeadReddot()
    local isReddot = false
    local hero = self._hero
    local act_id = self._activityID
    for index, value in ipairs(self._heros or {}) do
        if hero and hero.heroId ~= value.heroId then
            --计算红点
            local count = 0
            for _, item in ipairs(value.items) do
                --新解锁(需要先解锁)
                if act_id and value.heroId and item.count and value.count and value.count >= item.count then
                    local key = act_id..value.heroId..item.count
                    local isNewReddot = Server.ActivityServer:GetUserInt(key, 0)
                    if isNewReddot == 1 then
                        isReddot = true
                        break
                    end
                    --评论奖励
                    for i, new in ipairs(item.news or {}) do
                        if not new.received and new.rewards and #new.rewards> 0 then
                            isReddot = true
                            break
                        end
                    end
                end
                --大奖进度
                if item.count > count then
                    count = item.count
                end
            end
            --大奖状态
            if not value.received and value.count >= count then
                isReddot = true
                break
            end
        end
    end

    if self._wtHeadIns then
        if isReddot then
            self._wtHeadIns:SetReddotVisible(true, EReddotType.Normal)
        else
            self._wtHeadIns:SetReddotVisible(false)
        end
    end
end

function ActivityTaraMain:_OnClickTaraItem(index, isAnim)
    self._defaultIndex = index
    self._weChatIndex  = nil
    self:_RefreshWaterBox(index, isAnim)
end

function ActivityTaraMain:_RefreshWaterBox(index, isAnim)
    local hero = self._hero
    if self._list then
        local data = self._list[index]
        if data and hero then
            self._lines = {}
            local isUnLock = false
            table.insert(self._lines, {milestone = data})
            if hero.count and data.count and hero.count >= data.count then
                table.insert(self._lines, {thumbs_up = data})
                self._likeData = data
                for _, new in ipairs(data.news or {}) do
                    table.insert(self._lines, {comment = new})
                end
                isUnLock = true
            end
            local state = 0
            local key = nil
            if hero.act_id and hero.heroId and data.count then
                key = hero.act_id..hero.heroId..data.count
                state = Server.ActivityServer:GetUserInt(key, 0)
            end
            --刷新前全部隐藏,解决UI显示错误
            Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Hide)
            self._wtLockPnael:Collapsed()
            self._wtWaterfallBox:Collapsed()
            if isUnLock then
                self._wtWaterfallBox:Visible()
                self:_RefreshList()
                if key and (isAnim or (state == 1)) then
                    Server.ActivityServer:SetUserInt(key, 2)
                    Timer.DelayCall(0, self._UpdateUnLockAnim, self, #self._lines)
                end
            else
                self._wtPanls:Collapsed()
                self._tipsRewards = data.rewards
                self._wtLockPnael:SelfHitTestInvisible()
                if self._tipsRewards and #self._tipsRewards > 0 then
                    self._wtLockBox:Visible()
                    self._wtLineImg:SelfHitTestInvisible()
                    self._wtLockTipes:SelfHitTestInvisible()
                    self._wtLockBox:RefreshAllItems()
                    self._wtLockTipes:SetText(Config.Loc.RewardTips)
                else
                    self._wtLineImg:Collapsed()
                    self._wtLockBox:Collapsed()
                    self._wtLockTipes:Collapsed()
                end
                self._wtLockNoany:StopAllAnimations()
                self._wtLockNoany:PlayAnimation(self._wtLockNoany.WBP_Common_NoAnything_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
            end
            --延迟一帧执行确保不被主界面覆盖
            self:_InitTobBarHandle()
            Module.ItemDetail:CloseItemDetailPanel()
        end
    end
end

function ActivityTaraMain:_RefreshList()
    --重置极限操作
    self._isMoveToLastItem = nil
    if self._lines and #self._lines > 0 then
        --区分是否定位
        if self._count then
            local count = #self._lines
            if self._count < count then
                self._toEndIndex = count
            end
            self._count = nil
        end
        --区分只刷新数据
        if self._refreshData then
            self._refreshData = nil
            self._wtWaterfallBox:RefreshVisibleItems()
        else
            self._wtWaterfallBox:RefreshAllItems()
        end
        self._wtWaterfallBox:Visible()
    else
        self._wtWaterfallBox:Collapsed()
    end
    --可领奖提示
    self:_AwardReceivingTips()
    --消息定位
    self:_ToEndItemIndex()
end

function ActivityTaraMain:_UpdateUnLockAnim(count)
    Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Anim, 0.5, count)
end

function ActivityTaraMain:_IsHaveAnim()
    --重新激活waterfull
    local func = function()
        --开启滚轮
        self._noMovement = true
        self._wtWaterfallBox:SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)
    end
    local act_id = self._activityID
    local hero = self._hero
    local index = self._defaultIndex
    local list = self._list
    if list and index and hero and act_id then
        local data = list[index]
        if data and hero.heroId and data.count then
            local key = act_id..hero.heroId..data.count
            local reddotState = Server.ActivityServer:GetUserInt(key, 0)
            if reddotState == 1 then
                --禁用滚轮
                self._noMovement = false
                self._wtWaterfallBox:SetConsumeMouseWheel(EConsumeMouseWheel.Never)
                Timer.DelayCall(2, func, self)
            end
        end
    end
end

function ActivityTaraMain:_ToEndItemIndex()
    if self._toEndIndex then
        self._wtWaterfallBox:ScrollToEndByIndex(self._toEndIndex)
        self._toEndIndex = nil
    end
end

function ActivityTaraMain:_AwardReceivingTips(isMove)
    self._wtPanls:Collapsed()
    --是否已经滑倒最后一个
    if self._isMoveToLastItem then
        return
    end
    local list = self._lines
    if list then
        local indexs = {}
        for index, value in ipairs(list or {}) do
            local data = value.comment
            if data then
                if not data.received and data.rewards and #data.rewards > 0 then
                    table.insert(indexs, index)
                end
            end
        end
        local rewardNum = 0
        local toIndex   = nil
        local firstIndex = self._wtWaterfallBox:GetFirstVisibleItemIndex()
        local lastIndex  = self._wtWaterfallBox:GetLastVisibleItemIndex()
        for index, value in ipairs(indexs or {}) do
            if value > lastIndex then
                if toIndex == nil then
                    toIndex = value
                end
                rewardNum = rewardNum + 1
            end
        end
        --显示奖励数量
        if rewardNum > 0 then
            self._wtEnTxt:SetText(StringUtil.Key2StrFormat(Config.Loc.ActRewardAddTwo, {num = rewardNum}))
            self._wtPanls:SelfHitTestInvisible()
        else
            self._isMoveToLastItem = true
            self._wtPanls:Collapsed()
        end
        --定位移动
        if isMove and toIndex and self._noMovement ~= false then
            self._wtWaterfallBox:ScrollToEndByIndex(toIndex)
        end
    end
end

function ActivityTaraMain:_AddEventListener()
    self:AddLuaEvent(Config.evtOnClickTaraItem, self._OnClickTaraItem, self)--里程碑点击
    self:AddLuaEvent(Config.evtOpenTaraPanel, self._OpenTaraPanel, self)--打开评论/切换
    self:AddLuaEvent(Config.evtRefreshTaraPanel, self._OnRefreshTaraPanel, self)--本地刷新
    --回复/评论/切换/领奖
    self:AddLuaEvent(Server.ActivityServer.Events.evtActTaraTrainingTravel, self._OnActTaraTrainingTravel, self)
    --手柄适配
    self:AddLuaEvent(Config.evtAddTaraHandleAdaptation, self._OnAddTaraHandleAdaptation, self)
    --奖励关闭
    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnCloseRewardPanel, self)
    --详情页关闭
    self:AddLuaEvent(Module.ItemDetail.Config.evtItemDetailPanelClosed, self._OnItemDetailPanelClosed, self)
end

function ActivityTaraMain:_OnItemDetailPanelClosed()
    Timer.DelayCall(0, self._OnCardUnHovered, self)
    Timer.DelayCall(0, self._RefreshRewardFrame, self)
    -- --手柄模式异常处理
    -- if self:IsExistCardMainDetail() and IsHD() and WidgetUtil.IsGamepad() then
    --     Timer.DelayCall(0, self._OnCardHandleMode, self)
    -- end
end

function ActivityTaraMain:_OnCardHandleMode()
    if not self:IsExistCardMainDetail() then
        self:_OnCardUnHovered()
        self:_RefreshInterface(self._hero)
    end
end

function ActivityTaraMain:_OnCloseRewardPanel()
    --界面开启状态
    if self:_IsVisibe() then
        local hero = self._hero
        if hero then
            local count = 0
            local receivedAllTheRewards = true
            local isThereAreNoPeers = false
            for index, item in ipairs(self._heros or {}) do
                if item.rawState == 0 then
                    isThereAreNoPeers = true
                    break
                end
            end
            for index, item in ipairs(hero.items or {}) do
                if item.count and item.count > count then
                    count = item.count
                end
            end
            --只查询大奖
            if not hero.received or (hero.count and hero.count < count) then
                receivedAllTheRewards = false
            end
            --打开选人界面
            if receivedAllTheRewards and hero.rawState == 1 and isThereAreNoPeers then
                self:_OpenSelectHeroPop()
            end
        end
    end
end

--手柄适配分发器🎮
function ActivityTaraMain:_OnAddTaraHandleAdaptation(handleType, ...)
    if handleType == ETaraHandleType.Comment then
        self:_SetCommentData(...)
        self:_SetHandleAdaptation()
    end
    if handleType == ETaraHandleType.Anim then
        self:_IsHaveAnim()
    end
end

function ActivityTaraMain:_SetCommentData(widget, data, index, weChatIndex, islike)
    self._widget = widget
    self._data  = data
    self._index = index
    self._weChatIndex = weChatIndex
    self._islike = islike
end

function ActivityTaraMain:_OnActTaraTrainingTravel(isCurHero)
    --记录旧的数据
    self._oldItems = self._heros
    self:_InitData()
    --启动一个定时器
    self:_RunAnTimer(isCurHero)
    self:_RefreshInterfaceView(isCurHero)
end

--####策划某改(异常显示回复评论信息)
function ActivityTaraMain:_RunAnTimer(isCurHero)
    local GetDataByList = function(list, key, value)
        for index, data in pairs(list or {}) do
            if data and data[key] == value then
                return data
            end
        end
    end
    --是否有数据差异
    if self._oldItems and self._heros then
        self._middleItems = self._heros
        --对比数据
        local newList = Deep.DeepCopy(self._heros)
        local isRun = false
        for index, value in ipairs(newList or {}) do
            for _, item in ipairs(value.items or {}) do
                local hero = GetDataByList(self._oldItems, "heroId", value.heroId)
                local wechat = GetDataByList(hero and hero.items, "id", item.id)
                if wechat and item and wechat.news and item.news and #wechat.news < #item.news then
                    --移除评论以后的
                    local count = #wechat.news + 1
                    local data = item.news[count]
                    if data and data.heroId == data.playerId then
                        count = count + 1
                    end
                    for i = 1, #item.news, 1 do
                        local info = table.remove(item.news, count)
                        if info then
                            isRun = true
                        else
                            break
                        end
                    end
                    break
                end
            end
            if self._hero and self._hero.heroId == value.heroId then
                self._hero = value
            end
        end
        --是否只刷新数据
        self._refreshData = not isRun
        if isRun then
            self._heros = newList
            --启动倒计时
            local random = math.random(2, 3)
            Timer.DelayCall(random, self._OnCountdown, self, isCurHero)
        end
    end
end

function ActivityTaraMain:_OnCountdown(isCurHero)
    if self._middleItems then
        self._heros = self._middleItems
    end
    self:_RefreshInterfaceView(isCurHero)
end

function ActivityTaraMain:_RefreshInterfaceView(isCurHero)
    --计算原始长度
    if self._lines then
        self._count = #self._lines
    end
    --是否只刷当前干员
    local hero = self._hero
    if isCurHero and hero then
        self._hero = self:_GetHeroByHeroId(hero.heroId)
        self:_RefreshInterface(self._hero)
    else
        self._hero = self:_GetCurHero()
        self:_RefreshInterface(self._hero)
    end
end

function ActivityTaraMain:_GetHeroByHeroId(heroId)
    for index, hero in ipairs(self._heros or {}) do
        if hero.heroId == heroId then
            return hero
        end
    end
    return nil
end

function ActivityTaraMain:_OnRefreshTaraPanel(activityID, data, index)
    if self._activityID == activityID then
        self._hero = data
        local hero = data
        if hero then
            self._weChatIndex = nil
            self._defaultIndex = self:_GetDefaultIndex(hero)
            self:_RefreshInterface(hero)
        end
    end
end

function ActivityTaraMain:_OpenTaraPanel(activityID, data, index)
    if activityID == self._activityID then
        --初始化评论状态(初始化->OnShowBegin需要屏蔽)
        if not self:_IsVisibe() then
            return
        end
        --货币栏显示控制
        if data and index then
            --关闭弹窗
            self:_OnUnhovered()
            Module.ItemDetail:CloseItemDetailPanel()
            self:_SetMianHeadline(false)
            --打开面板(导航栏)
            if index == 1 then
                self:_InitTobBarHandle(1)
                self._wtSwitchItem:InitData(self._activityID, data, self._heros)
            else
                self:_InitTobBarHandle(2)
                self._wtSendNeItem:InitData(self._activityID, data, self._heros)
            end
            Module.CommonBar:BindPersistentBackHandler(self._OnBack, self)
            return
        end
        --手柄恢复
        self:_InitTobBarHandle()
        --控制面板
        self:_SetMianHeadline(true)
        self._wtSwitchItem:OnStopAnim()
        self._wtSendNeItem:OnStopAnim()
        --判断是否同行干员
        local hero = self._hero
        if hero and hero.rawState ~= 1 then
            Module.CommonBar:BindPersistentBackHandler(self._ToSameLineHero, self)
        else
            --恢复主活动ESC
            Config.evtBackBtnChanged:Invoke()
        end
    end
end

function ActivityTaraMain:_OnBack()
    self:_RefreshInterface(self._hero)
end

function ActivityTaraMain:_ToSameLineHero()
    local hero = self._hero
    self._hero = self:_GetCurHero()
    --如果id不是同一个干员清空
    if hero and self._hero then
        if hero.heroId ~= self._hero.heroId then
            self._weChatIndex = nil
            self._defaultIndex = self:_GetDefaultIndex(self._hero)
        end
    end
    self:_RefreshInterface(self._hero)
end

function ActivityTaraMain:OnShowBegin()
    --如果UI不是处于一个运行状态(停止一切逻辑&&)
    if not self:_IsVisibe() then
        return
    end
    self:_AddMouseButtonUp()
    self:_AddEventListener()
    self._refreshData = nil
    self:_InitData()
    self:_InitPanel()
    --播放入场动画
    self:_RunItemEnterAnim()
end

function ActivityTaraMain:_RunItemEnterAnim()
    local funcAnim = function()
        Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.EnterAnim, 0)
        self._wtWaterfallBox:Visible()
    end
    local func = function()
        Timer.DelayCall(0.23, funcAnim, self)
        self._wtWaterfallBox:Collapsed()
    end
    Timer.DelayCall(0, func, self)
end

function ActivityTaraMain:OnHideBegin()
    self:_RemoveMouseButtonUp()
    self:RemoveAllLuaEvent()
    self:_OnUnhovered()
    Module.ItemDetail:CloseItemDetailPanel()
end

function ActivityTaraMain:OnClose()
    self._subs = nil
    self._wtHotzone = nil
    if self._wtReddotsIns then
        self._wtReddotsIns:SetReddotVisible(false)
    end
    if self._wtHeadIns then
        self._wtHeadIns:SetReddotVisible(false)
    end
    self._wtHeadIns = nil
    self._wtReddotsIns = nil
end

function ActivityTaraMain:_OnWaterfallCount()
    if self._list then
        return #self._list
    end
    return 0
end

function ActivityTaraMain:_OnWaterfallWidget(position, itemWidget)
    local index = position
    if self._list and itemWidget then
        local data = self._list[index]
        itemWidget:InitData(data, index, self._defaultIndex, self._hero)
    end
end

--初始化手柄适配🎮
function ActivityTaraMain:_InitAdaptationHandle()
    if IsHD() then
        if isvalid(self._navGroup) then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        else
            self._navGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
            self._navGroup:AddNavWidgetToArray(self._wtMilestoneBox)
            self._navGroup:SetScrollRecipient(self._wtMilestoneBox)
            self._navGroup:AddNavWidgetToArray(self._wtWaterfallBox)
            self._navGroup:SetScrollRecipient(self._wtWaterfallBox)
            self._navGroup:AddNavWidgetToArray(self._wtLockBox)

            self._navGroup:AddNavWidgetToArray(self._wtHandleGroup3)
            self._navGroup:AddNavWidgetToArray(self._wtCheck)

            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        end
    end
end

--移除手柄适配🎮
function ActivityTaraMain:_RemoveHandleAdaptation()
    if IsHD() then
        self:_AddInputAction(false)
        self._navGroup = nil
        WidgetUtil.RemoveNavigationGroup(self)
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
        Module.CommonBar:RecoverBottomBarInputSummaryList()--清空导航栏适配
    end
end

function ActivityTaraMain:_AddInputAction(isAddAction)
    if IsHD() then
        if isAddAction then
            local actionName = "SingleDraw_ActivityPrize"
            local handle = self:AddInputActionBinding(actionName, EInputEvent.IE_Pressed, self._OnReqClicked, self, EDisplayInputActionPriority.UI_Pop)
            local btn = self._wtState
            if handle and btn then
                self._handle = btn:SetDisplayInputActionWithLongPress(handle, self, actionName, true, nil, true)
            end
        else
            if self._handle then
                self:RemoveInputActionBinding(self._handle)
                self._handle = nil
            end
        end
    end
end

function ActivityTaraMain:_InitTobBarHandle(handleType)
    Timer.DelayCall(0, self._SetHandleAdaptation, self, handleType)
end

--设置导航栏手柄🎮
function ActivityTaraMain:_SetHandleAdaptation(handleType)
    if IsHD() then
        --导航栏
        local func1 = function()--切换集合
            self:_OpenTaraPanel(self._activityID, {}, 1)
        end
        local func2 = function()--规则详情
            if self._fGetCommonCtrl then
                local btn = self._fGetCommonCtrl(EActivityCommCtrl.ShowRuleButton)
                if btn then
                    self:_OnUnhovered()
                    Module.ItemDetail:CloseItemDetailPanel()
                    btn:SelfClick()
                end
            end
        end
        local func3 = function()--评论
            self:_OpenTaraPanel(self._activityID, self._data, 2)
        end
        local func4 = function()--发送
            Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Send)
        end
        local func5 = function()--刷新
            Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Refresh)
        end
        local func6 = function()--领取奖励
            Config.evtAddTaraHandleAdaptation:Invoke(ETaraHandleType.Receive, self._index)
        end
        local func7 = function()--点赞
            if self._likeData then
                Server.ActivityServer:SendTaraLikeReq(self._activityID, self._likeData.id)
                self._likeData = nil
            end
        end
        --默认开始界面
        local tabBarInputs = {}
        table.insert(tabBarInputs, {actionName = "ActTara_SwitchingCollections", func = func1, caller = self, bUIOnly = false})
        if self._data then
            table.insert(tabBarInputs, {actionName = "ActTara_Comment"         , func = func3, caller = self, bUIOnly = false})
        end
        table.insert(tabBarInputs, {actionName = "ActTara_RuleDetails"         , func = func2, caller = self, bUIOnly = false})
        if self._index then
            table.insert(tabBarInputs, {actionName = "ActivityReceiveReward"   , func = func6, caller = self, bUIOnly = false})
        end
        --点赞
        if self._islike then
            table.insert(tabBarInputs, {actionName = "Activity_Asara_Likes_It" , func = func7, caller = self, bUIOnly = false})
        end
        --新增同行X键
        if handleType == 2 or handleType == 1 then
            self:_AddInputAction(false)
        else
            self:_AddInputAction(true)
        end
        if handleType == 2 then
            tabBarInputs = {
                {actionName = "ActTara_Send"   , func = func4, caller = self, bUIOnly = false},
                {actionName = "ActTara_Refresh", func = func5, caller = self, bUIOnly = false},
            }
        end
        if handleType == 1 then
            tabBarInputs = {}
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(tabBarInputs, true)
    end
end

function ActivityTaraMain:_AddMouseButtonUp()
    local gamelnst = GetGameInstance()
    if self._btnHandle == nil and gamelnst then
        self._btnHandle = UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Add(self._OnMouseButtonDown, self)
    end
end

function ActivityTaraMain:_RemoveMouseButtonUp()
    local gamelnst = GetGameInstance()
    if self._btnHandle and gamelnst then
        UDFMGameHudDelegates.Get(gamelnst).OnHandleMouseButtonUpEvent:Remove(self._btnHandle)
        self._btnHandle = nil
    end
end

--鼠标点击屏幕事件
function ActivityTaraMain:_OnMouseButtonDown(mouseEvent)
    local btn = self._wtCheck
    if mouseEvent and btn then
        local sceenPos = mouseEvent:GetScreenSpacePosition()
        local geometry = btn:GetCachedGeometry()
    	local isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
        if not isUnder then
            self:_OnUnhovered()
        end
        local IsActivateView = function()
            if self._wtSwitchItem:GetVisibility() == ESlateVisibility.Visible then
                return self._wtSwitchItem
            end
            if self._wtSendNeItem:GetVisibility() == ESlateVisibility.Visible then
                return self._wtSendNeItem
            end
        end
        local view = IsActivateView()
        if view and view:IsItClosed(sceenPos) then
            self:_OnBack()
        end
        geometry = self._wtbusCardBtn:GetCachedGeometry()
    	isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
        if not isUnder then
            --延迟一帧判断(确保详情页执行完成)
            Timer.DelayCall(0, self._OnCardUnHovered, self)
        end
        if self._btn then
            geometry = self._btn:GetCachedGeometry()
            isUnder = USlateBlueprintLibrary.IsUnderLocation(geometry, sceenPos)
            if not isUnder then
                Timer.DelayCall(0, self._RefreshRewardFrame, self)
            end
        end
    end
end

function ActivityTaraMain:_RefreshRewardFrame()
    local ItemDetail = Module.ItemDetail:GetMainPanelHandle()
    local itemData = self._itemData
    local isHave = false
    if itemData and ItemDetail then
        local extraParams = ItemDetail.extraParams
        if extraParams then
            isHave = itemData == extraParams[1]
        end
    end
    if not isHave and self._btn then
        self:_SetFramework(self._btn, false)
    end
end

function ActivityTaraMain:PlayAnim(animName, isLoop)
    local loop = 1
    if isLoop then
        loop = 0
    end
    if animName then
        self:PlayAnimation(animName, 0, loop, EUMGSequencePlayMode.Forward, 1, false)
    else
        self:StopAnimation(self.Anim_pickup_loop)
        self._wtLightEfficiy:SetRenderOpacity(0)
    end
end

function ActivityTaraMain:OnAnimFinished(anim)
    if anim == self.Anim_pickup_in then
        self:PlayAnim(self.Anim_pickup_loop, true)
    end
end

--设置图片描边
function ActivityTaraMain:SetImageBorder(isBorder)
    local widget = self._wtStrokeImg
    if widget then
        if isBorder then
            widget:SelfHitTestInvisible()
        else
            widget:Collapsed()
        end
        local material = widget:GetOverrideMaterial()--获取材质对象
        if material then
            local materialInst = UKismetMaterialLibrary.CreateDynamicMaterialInstance(self, material, "None")
            if materialInst then
                if isBorder then
                    widget:SetStyle(1)
                    materialInst:SetVectorParameterValue("GlowColor", FLinearColor(1, 1, 1, 1))
                    materialInst:SetScalarParameterValue("OutLineAlpha", 3)
                else
                    widget:SetStyle(0)
                    materialInst:SetVectorParameterValue("GlowColor", FLinearColor(1, 1, 1, 0))
                    materialInst:SetScalarParameterValue("OutLineAlpha", 0)
                end
                widget:SetOverrideMaterial(materialInst)
            end
        end
    end
end

return ActivityTaraMain