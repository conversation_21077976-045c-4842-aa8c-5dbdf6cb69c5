----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMTournament)
----- LOG FUNCTION AUTO GENERATE END -----------



    --------------------------------------------------------------------------
    --- UI路径映射配置示例（UILayer表示需要加入的层级）
    --------------------------------------------------------------------------
    UITable[UIName2ID.TournamentMainPanel] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentMainPanel", 
        BPKey = "WBP_BattlePointMatch_Main",
        SubUIs={
            UIName2ID.MPRankBG01,
            UIName2ID.MPRankBG02,
            UIName2ID.MPRankBG03,
            UIName2ID.MPRankBG04,
            UIName2ID.MPRankBG05,
            UIName2ID.MPRankBG06,
            UIName2ID.MPRankBG07,
            UIName2ID.MPRankBG08,
            UIName2ID.MPRankBGNone,
        },
    }

    UITable[UIName2ID.CommanderMainPanel] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderMainPanel", 
        BPKey = "WBP_WinnnerTakesAll_Main",
        SubUIs={
            UIName2ID.CommanderRankBG01,
            UIName2ID.CommanderRankBG02,
            UIName2ID.CommanderRankBG03,
            UIName2ID.CommanderRankBG04,
            UIName2ID.CommanderRankBGNone,
        },
    }

    UITable[UIName2ID.TournamentBasePanel] = {
        UILayer = EUILayer.Stack, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentBasePanel", 
        BPKey = "WBP_BattlePointMatch_Base",
        SubUIs={
            UIName2ID.TournamentMainPanel,
            UIName2ID.CommanderMainPanel,
        },
        ReConfig = {
            IsPoolEnable = true,
        }
    }

    UITable[UIName2ID.CommanderMainItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderMainItem", 
        BPKey = "WBP_WinnnerTakesAll_Main_Item01",
    }

    UITable[UIName2ID.CommanderAbilityItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderAbilityItem", 
        BPKey = "WBP_WinnnerTakesAll_Main_Item02",
    }

    UITable[UIName2ID.TournamentMainPanelScoreItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentMainPanelScoreItem", 
        BPKey = "WBP_BattlePointMatch_MainPointItem",
        -- Anim = {
        --     FlowInAni = "In_Anim",
        -- }
    }
    
    UITable[UIName2ID.CommanderRecordPanel] = {
        UILayer = EUILayer.Stack, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRecordPanel", 
        BPKey = "WBP_WinnnerTakesAll_Record",
        SubUIs={
            UIName2ID.CommanderRecordPanelStatsItem,
            UIName2ID.CommanderRecordPanelEventsItem,
            UIName2ID.CommanderRecordPanelAchieveItem,
            UIName2ID.CommonEmptyContent,
        }
    }

    UITable[UIName2ID.CommanderRecordPanelStatsItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRecordPanelStatsItem", 
        BPKey = "WBP_WinnnerTakesAll_Record_Item02",
    }

    UITable[UIName2ID.CommanderRecordPanelEventsItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRecordPanelEventsItem", 
        BPKey = "WBP_WinnnerTakesAll_Record_Item01",
    }

    UITable[UIName2ID.CommanderRecordPanelAchieveItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRecordPanelAchieveItem", 
        BPKey = "WBP_WinnnerTakesAll_Record_Item03",
    }

    UITable[UIName2ID.TournamentPreviewWindow] = {
        UILayer = EUILayer.Pop, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentPreviewWindow", 
        BPKey = "WBP_BattlePointMatch_PopUpTips",
        IsModal = true,
        -- Anim = {
        --     FlowInAni = "In_Anim",
        -- }
    }

    UITable[UIName2ID.TournamentPreviewWindowItem] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentPreviewWindowItem", 
        BPKey = "WBP_BattlePointMatch_RankDesItem",
        -- Anim = {
        --     FlowInAni = "In_Anim",
        -- }
    }

    UITable[UIName2ID.TournamentRewardPanel] = {
        UILayer = EUILayer.Stack, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRewardPanel", 
        BPKey = "WBP_BattlePointMatch_RankGain",
        SubUIs={
            UIName2ID.IVCommonItemTemplate,
            UIName2ID.TournamentRewardPanelItem,
        }
        -- Anim = {
        --     FlowInAni = "In_Anim",
        -- }
    }

    UITable[UIName2ID.TournamentRewardPanelItem] = {
        UILayer = EUILayer.Stack, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRewardPanelItem", 
        BPKey = "WBP_BattlePointMatch_RankIconItem",
        -- Anim = {
        --     FlowInAni = "In_Anim",
        -- }
    }

    UITable[UIName2ID.MPRankBGNone] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_None",
        
    }

    UITable[UIName2ID.MPRankBG01] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_01",
        
    }

    UITable[UIName2ID.MPRankBG02] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_02",
        
    }

    UITable[UIName2ID.MPRankBG03] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_03",
        
    }

    UITable[UIName2ID.MPRankBG04] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_04",
        
    }

    UITable[UIName2ID.MPRankBG05] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_05",
        
    }

    UITable[UIName2ID.MPRankBG06] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_06",
        
    }

    UITable[UIName2ID.MPRankBG07] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_07",
        
    }

    UITable[UIName2ID.MPRankBG08] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.TournamentRankBG", 
        BPKey = "WBP_MP_Ranking_08",
        
    }

    UITable[UIName2ID.CommanderRankBGNone] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRankBG", 
        BPKey = "WBP_WinnerTakesAll_None",
        
    }

    UITable[UIName2ID.CommanderRankBG01] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRankBG", 
        BPKey = "WBP_WinnerTakesAll_01",
        
    }

    UITable[UIName2ID.CommanderRankBG02] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRankBG", 
        BPKey = "WBP_WinnerTakesAll_02",
        
    }

    UITable[UIName2ID.CommanderRankBG03] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRankBG", 
        BPKey = "WBP_WinnerTakesAll_03",
        
    }

    UITable[UIName2ID.CommanderRankBG04] = {
        UILayer = EUILayer.Sub, 
        LuaPath = "DFM.Business.Module.TournamentModule.UI.CommanderRankBG", 
        BPKey = "WBP_WinnerTakesAll_04",
        
    }

    local TournamentConfig = 
    {
        --------------------------------------------------------------------------
        ---Table配置示例
        --------------------------------------------------------------------------
        TournamentTierTable = Facade.TableManager:GetTable("TournamentTier"),
        TournamentRewardsTable = Facade.TableManager:GetTable("TournamentRewards"),
        TournamentSeasonTable = Facade.TableManager:GetTable("TournamentSeason"),
        TournamentTitleTable = Facade.TableManager:GetTable("TournamentTitle"),
        TournamentParameterTable = Facade.TableManager:GetTable("TournamentParameter"),
        CommanderTierTable = Facade.TableManager:GetTable("CommanderTier"),
        CommanderRewardsTable = Facade.TableManager:GetTable("CommanderRewards"),
        CommanderSeasonTable = Facade.TableManager:GetTable("CommanderSeason"),
        CommanderParameterTable = Facade.TableManager:GetTable("CommanderParameter"),
        CommanderWordParameterTable = Facade.TableManager:GetTable("CommanderWordParameter"),

        --------------------------------------------------------------------------
        ---LuaEvent声明示例
        --------------------------------------------------------------------------
        -- evtMainPanelTest = LuaEvent:NewIns("evtMainPanelTest"),
        -- evtTournamentVarChanged = LuaEvent:NewIns("evtTournamentVarChanged"),
        evtTournamentMainUIShow = LuaEvent:NewIns("evtTournamentMainUIShow"),
        evtOpenRankRewardPanelBtnClicked = LuaEvent:NewIns("evtOpenRankRewardPanelBtnClicked"),
        
        Loc = {
            SeasonName = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonName", "S%d赛季"),
            LevelDescNone = NSLOCTEXT("TournamentModule", "Lua_Tournament_LevelDescNone", "您尚未参加晋升之路计算军功，<customstyle color=\"Color_Highlight02\">勾选计算军功模式</>进行游戏即可获得军功及军衔"),
            CommanderLevelDescNone = NSLOCTEXT("TournamentModule", "Lua_Tournament_CommanderLevelDescNone", "您尚未参加作战，进行胜者为王作战，即可获得段位"),
            RankListFriend = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankListFriend", "好友排名"),
            RankListGlobal = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankListGlobal", "全服排名"),
            RankYearTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankYearTitle", "%d赛年"),
            AllMajorLevels = NSLOCTEXT("TournamentModule", "Lua_Tournament_AllMajorLevels", "全部军衔"),
            AverageCollection = NSLOCTEXT("TournamentModule", "Lua_Tournament_AverageCollection", "平均带出"),
            EscapeRate = NSLOCTEXT("TournamentModule", "Lua_Tournament_EscapeRate", "撤离率"),
            KD = NSLOCTEXT("TournamentModule", "Lua_Tournament_KD", "K/D"),
            CommonUsedHero = NSLOCTEXT("TournamentModule", "Lua_Tournament_CommonUsedHero", "常用干员"),
            GameNum = NSLOCTEXT("TournamentModule", "Lua_Tournament_GameNum", "对局数"),
            Current = NSLOCTEXT("TournamentModule", "Lua_Tournament_Current", "当前"),
            EndSerial = NSLOCTEXT("TournamentModule", "Lua_Tournament_EndSerial", "赛季结束时"),
            ReceiveAllRewards = NSLOCTEXT("TournamentModule", "Lua_Tournament_ReceiveAllRewards", "一键领取"),
            ReceiveAllSuccess = NSLOCTEXT("TournamentModule", "Lua_Tournament_ReceiveAllSuccess", "一键领取成功"),
            ReceiveSingleSuccess = NSLOCTEXT("TournamentModule", "Lua_Tournament_ReceiveSingleSuccess", "%s军衔奖励领取成功"),
            ReceiveParticSuccess = NSLOCTEXT("TournamentModule", "Lua_Tournament_ReceiveParticSuccess", "参与赛季奖励领取成功"),
            RewardsAlreadyRewarded = NSLOCTEXT("TournamentModule", "Lua_Tournament_RewardsAlreadyRewarded", "已领取过奖励"),
            NoRewardsCanReceive = NSLOCTEXT("TournamentModule", "Lua_Tournament_NoRewardsCanReceive", "当前没有可领取的奖励"),
            RankStarxN = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankStarxN", "x%d"),
            RankStarDivide = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankStarDivide", "%d/%d"),
            RankScoreDivide = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankScoreDivide", "<customstyle color=\"C000\">%d</>/%d"),
            NameWithStar = NSLOCTEXT("TournamentModule", "Lua_Tournament_NameWithStar", "{levelName}{starNum}星"),
            Unknow = NSLOCTEXT("TournamentModule", "Lua_Tournament_Unknow", "不明"),
            YYMMDD = NSLOCTEXT("TournamentModule", "Lua_Tournament_YYMMDD", "{year}-{month}-{day}"),
            FirstReachMajorLevelInSeason = NSLOCTEXT("TournamentModule", "Lua_Tournament_FirstReachMajorLevelInSeason", "全新的突破，你的军衔达到了%s！"),
            FirstReachMinorLevelInSeason = NSLOCTEXT("TournamentModule", "Lua_Tournament_FirstReachMinorLevelInSeason", "你的军衔达到了%s，小有进步"),
            FirstGameInSeason = NSLOCTEXT("TournamentModule", "Lua_Tournament_FirstGameInSeason", "你以%s军衔加入了该赛季的积分赛"),
            WinStreak = NSLOCTEXT("TournamentModule", "Lua_Tournament_WinStreak", "你在{levelName}军衔下，完成了{streakCount}连胜，可喜可贺"),
            WinAfterLoseStreak = NSLOCTEXT("TournamentModule", "Lua_Tournament_WinAfterLoseStreak", "在{levelName}军衔下，坚持不懈的你在{streakCount}连败后重拾节奏，反败为胜"),
            TenTimesGame = NSLOCTEXT("TournamentModule", "Lua_Tournament_TenTimesGame", "你已经进行了10次赛季内的行动"),
            FiftyTimesGame = NSLOCTEXT("TournamentModule", "Lua_Tournament_FiftyTimesGame", "你已经进行了50次赛季内的行动"),
            HundredsTimesGame = NSLOCTEXT("TournamentModule", "Lua_Tournament_HundredsTimesGame", "你已经进行了%d次赛季内的行动"),
            NotInTheList = NSLOCTEXT("TournamentModule", "Lua_Tournament_NotInTheList", "未上榜"),
            TournamentTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentTitle", "晋升之路"),
            CommanderTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_CommanderTitle", "胜者为王"),

            RewardTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_RewardTitle", "军衔奖励"),
            CurrentScore = NSLOCTEXT("TournamentModule", "Lua_Tournament_CurrentScore", "当前军功"),
            ReturnBack = NSLOCTEXT("TournamentModule", "Lua_Tournament_ReturnBack", "回到原位"),
            ScoreRange = NSLOCTEXT("TournamentModule", "Lua_Tournament_ScoreRange", "%d-%d"),
            ScoreLeast = NSLOCTEXT("TournamentModule", "Lua_Tournament_ScoreLeast", ">=%d"),
            ScoreNameShoot = NSLOCTEXT("TournamentModule", "Lua_Tournament_ScoreNameShoot", "射击"),
            ScoreNameTactics = NSLOCTEXT("TournamentModule", "Lua_Tournament_ScoreNameTactics", "战术"),
            ScoreNameVehicle = NSLOCTEXT("TournamentModule", "Lua_Tournament_ScoreNameVehicle", "载具/反载"),
            SeasonDescTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonDescTitle", "赛季规则"),
            SeasonDescSubTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonDescSubTitle", "赛季说明"),
            TournamentPreviewTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentPreviewTitle", "军功系统"),
            TournamentPreviewSubTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentPreviewSubTitle", "军功与军衔的对应关系"),
            RewardUnlockDesc = NSLOCTEXT("TournamentModule", "Lua_Tournament_RewardUnlockDesc", "<customstyle color=\"Color_Highlight02\">（{curCount}/{needCount}）</>军衔达到{levelName}及以上并进行{gameCount}场游戏可解锁以下奖励"),
            RewardUnlockDescLevelOnly = NSLOCTEXT("TournamentModule", "Lua_Tournament_RewardUnlockDescLevelOnly", "军衔达到{levelName}及以上可解锁以下奖励"),
            NotLimit = NSLOCTEXT("TournamentModule", "Lua_Tournament_NotLimit", "无上限"),
            RecordTitle = NSLOCTEXT("TournamentModule", "Lua_Tournament_RecordTitle", "服役记录"),
            RankingRecord = NSLOCTEXT("TournamentModule", "Lua_Tournament_RankingRecord", "晋升记录"),
            CoreRewardDesc = NSLOCTEXT("TournamentModule", "Lua_Tournament_CoreRewardDesc", "到达<customstyle color=\"Color_Highlight02\">{levelName}</>军衔后再游玩 <customstyle color=\"Color_Highlight01\">{gameCount}</>场即可获得<customstyle color=\"Color_Quality03\">{rewardName}</>"),
            SeasonInInitProtectTime = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonInInitProtectTime", "赛季重置初始化中，请稍等%d秒"),
            TournamentLadder = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentLadder", "军功榜"),
            TournamentLevel = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentLevel", "军衔"),
            TournamentScoreMin = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentScoreMin", "军功最低阈值"),
            TournamentScoreMax = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentScoreMax", "军功最高阈值"),
            TournamentScore = NSLOCTEXT("TournamentModule", "Lua_Tournament_TournamentScore", "军功"),
            NoneLevel = NSLOCTEXT("TournamentModule", "Lua_Tournament_NoneLevel", "无军衔"),
            RecordNoAnything = NSLOCTEXT("TournamentModule", "Lua_Tournament_RecordNoAnything", "您在该赛季未参加晋升之路"),
            RecordNoAnythingCommander = NSLOCTEXT("TournamentModule", "Lua_Tournament_RecordNoAnythingCommander", "您在该赛季未参加胜者为王"),
            SeasonDuration = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonDuration", "{startTime}-{endTime}"),
            SeasonDurationCommanderRecord = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonDurationCommanderRecord", "S{serial}赛季时间：{startYear}/{startMonth}-{endYear}/{endMonth}"),
            ModeNotOpen = NSLOCTEXT("TournamentModule", "Lua_Tournament_ModeNotOpen", "模式暂未开放"),
            TotalRankCount = NSLOCTEXT("TournamentModule", "Lua_Tournament_TotalRankCount", "总战局数：{count}"),
            AbilityCommander = NSLOCTEXT("TournamentModule", "Lua_Tournament_AbilityCommander", "指挥"),
            AbilityVehicleKill = NSLOCTEXT("TournamentModule", "Lua_Tournament_AbilityVehicleKill", "载具击杀"),
            AbilityInfantryKill = NSLOCTEXT("TournamentModule", "Lua_Tournament_AbilityInfantryKill", "步战击杀"),
            AbilityRescue = NSLOCTEXT("TournamentModule", "Lua_Tournament_AbilityRescue", "救援"),
            SeasonRankCount = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonRankCount", "对局数"),
            SeasonRankCountAsCommander = NSLOCTEXT("TournamentModule", "Lua_Tournament_SeasonRankCountAsCommander", "指挥场次"),
            WinRateAsCommander = NSLOCTEXT("TournamentModule", "Lua_Tournament_WinRateAsCommander", "指挥胜率"),
            VehicleKillPerMin = NSLOCTEXT("TournamentModule", "Lua_Tournament_VehicleKillPerMin", "载具击败/分钟"),
            InfantryKillPerMin = NSLOCTEXT("TournamentModule", "Lua_Tournament_InfantryKillPerMin", "击败/分钟"),
            RescuePerMin = NSLOCTEXT("TournamentModule", "Lua_Tournament_RescuePerMin", "救援/分钟"),
            AchieveCommander = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchieveCommander", "优秀指挥官"),
            AchieveKiller = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchieveKiller", "最高击败"),
            AchieveStronghold = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchieveStronghold", "争夺据点"),
            AchievePilot = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchievePilot", "王牌飞行员"),
            AchieveTactic = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchieveTactic", "战术大师"),
            AchieveDoctor = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchieveDoctor", "战地医生"),
            AchievePanzer = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchievePanzer", "王牌装甲车"),
            AchieveDeVehicle = NSLOCTEXT("TournamentModule", "Lua_Tournament_AchieveDeVehicle", "载具克星"),

        },

        ETournamentPreviewType =
        {
            SeasonDesc=1,
            LevelPreview=2,
            CommanderSeasonDesc=3,
            CommanderLevelPreview=4,
        },

        ButtonLogId={
            SeasonDesc=10140003,
            LevelDesc=10140004,
            TitleDesc=10140005,
            Rank=10140006,
            Record=10140007,
            Reward=10140008,
            MaxReward=10140009,
            NextReward=10140010,

        },

        CoreRewardIconUrlPrefix="Resource/Texture/Activity/%s",

        RankLevel2BGMap = {
            [0] = UIName2ID.MPRankBGNone,
            [1] = UIName2ID.MPRankBG01,
            [2] = UIName2ID.MPRankBG02,
            [3] = UIName2ID.MPRankBG03,
            [4] = UIName2ID.MPRankBG04,
            [5] = UIName2ID.MPRankBG05,
            [6] = UIName2ID.MPRankBG06,
            [7] = UIName2ID.MPRankBG07
        },
        CommanderRankLevel2BGMap = {
            [0] = UIName2ID.CommanderRankBGNone,
            [1] = UIName2ID.CommanderRankBG01,
            [2] = UIName2ID.CommanderRankBG02,
            [3] = UIName2ID.CommanderRankBG03,
            [4] = UIName2ID.CommanderRankBG04,
            
        },
        CommanderLevel2IconMap = {
            [0] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_10.BattlefieldTitleIcon_10'",
            [1] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_12.BattlefieldTitleIcon_12'",
            [2] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_11.BattlefieldTitleIcon_11'",
            [3] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_10.BattlefieldTitleIcon_10'",
        },
        VehicleLevel2IconMap = {
            [0] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_07.BattlefieldTitleIcon_07'",
            [1] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_09.BattlefieldTitleIcon_09'",
            [2] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_08.BattlefieldTitleIcon_08'",
            [3] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_07.BattlefieldTitleIcon_07'",
        },
        InfantryLevel2IconMap = {
            [0] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_04.BattlefieldTitleIcon_04'",
            [1] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_06.BattlefieldTitleIcon_06'",
            [2] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_05.BattlefieldTitleIcon_05'",
            [3] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_04.BattlefieldTitleIcon_04'",
        },
        RescueLevel2IconMap = {
            [0] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_13.BattlefieldTitleIcon_13'",
            [1] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_15.BattlefieldTitleIcon_15'",
            [2] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_14.BattlefieldTitleIcon_14'",
            [3] = "PaperSprite'/Game/UI/UIAtlas/System/BattlefieldTitleIcon/BakedSprite/BattlefieldTitleIcon_13.BattlefieldTitleIcon_13'",
        },
        AchieveIconMap = {
            Commander = "PaperSprite'/Game/UI/UIAtlas/System/BattlePointMatch/BakedSprite/BattlePointMatch_Icon_05.BattlePointMatch_Icon_05'",
            Killer = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0004.Common_Card_0004'",
            Stronghold = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0005.Common_Card_0005'",
            Pilot = "PaperSprite'/Game/UI/UIAtlas/System/BattlePointMatch/BakedSprite/BattlePointMatch_Icon_03.BattlePointMatch_Icon_03'",
            Tactic = "PaperSprite'/Game/UI/UIAtlas/System/BattlePointMatch/BakedSprite/BattlePointMatch_Icon_01.BattlePointMatch_Icon_01'",
            Doctor = "PaperSprite'/Game/UI/UIAtlas/System/BattlePointMatch/BakedSprite/BattlePointMatch_Icon_04.BattlePointMatch_Icon_04'",
            Panzer = "PaperSprite'/Game/UI/UIAtlas/System/BattlePointMatch/BakedSprite/BattlePointMatch_Icon_02.BattlePointMatch_Icon_02'",
            DeVehicle = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Card_0008.Common_Card_0008'",

        },
    }
    
    return TournamentConfig
