----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
----- LOG FUNCTION AUTO GENERATE END -----------


UITable[UIName2ID.RoleInfoMainPanelSelf] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleInfoMainPanel",
    BPKey = "WBP_RoleInfo_MainPanel",
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    SubUIs = {
        UIName2ID.RoleInfoPersonal,
        UIName2ID.RoleInfoRecord,
        UIName2ID.RoleInfoSocialChange,
        UIName2ID.RoleInfoAchievement,
        UIName2ID.RoleInfoMainDetail,
        UIName2ID.ReputationMain
    },
    LinkSubStage = ESubStage.HallIndividual,
}

UITable[UIName2ID.RoleInfoMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleInfoMainPanel",
    BPKey = "WBP_RoleInfo_MainPanel",
    --- 默认Cache配置
    ReConfig = {
        IsPoolEnable = true,
    },
    SubUIs = {
        UIName2ID.RoleInfoPersonal,
        UIName2ID.RoleInfoRecord,
        UIName2ID.RoleInfoSocialChange,
        UIName2ID.RoleInfoMainDetail,
    },
    LinkSubStage = ESubStage.HallIndividual,
}

--个人信息
UITable[UIName2ID.RoleInfoPersonal] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoPersonal",
    BPKey = "WBP_RoleInfo_PersonalHomepage",
}

UITable[UIName2ID.RoleInfoAssetTip] = {
	UILayer = EUILayer.Pop,
	LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.RoleInfoAssetTip",
	BPKey = "WBP_RoleInfo_PropertyTips"
}

UITable[UIName2ID.PersonalInformation] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.PersonalInformation",
    BPKey = "WBP_RoleInfo_PersonalInformation",
}

UITable[UIName2ID.RoleInfoLikeDetail] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.RoleInfoLikeDetail",
    BPKey = "WBP_RoleInfo_LikeDetails",
    IsModal = true,
}

UITable[UIName2ID.RoleInfoLikePlayer] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.RoleInfoLikePlayer",
    BPKey = "WBP_RoleInfo_LikePlayer",
}

--个人详细信息

UITable[UIName2ID.RoleInfoMainDetail] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoMainDetail",
    BPKey = "WBP_RoleInfo_Detail_MainUI",
    SubUIs = {
        UIName2ID.RoleInfoMPDetail,
        UIName2ID.RoleInfoSOLDetail,
    },
}

UITable[UIName2ID.RoleInfoMPDetail] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoMPDetail",
    BPKey = "WBP_RoleInfo_Detail",
}

UITable[UIName2ID.RoleInfoSOLDetail] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoSOLDetail",
    BPKey = "WBP_RoleInfo_Detail",
}

UITable[UIName2ID.RoleInfoDetailPentagon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoDetailPentagon",
    BPKey = "WBP_RoleInfo_DetailPentagon",
}

UITable[UIName2ID.RoleInfoDetailList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoDetailList",
    BPKey = "WBP_RoleInfo_DetailList",
}

--end

--历史战绩
UITable[UIName2ID.RoleInfoRecord] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoRecord",
    BPKey = "WBP_RoleInfo_HistoricalRecord_Main",
    SubUIs = {
        UIName2ID.RoleInfoSolList,
        UIName2ID.RoleInfoMPList,
    },
}

UITable[UIName2ID.RoleInfoSolList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoSolList",
    BPKey = "WBP_RoleInfo_HistoricalRecord_SOL",
    SubUIs = {
        UIName2ID.RecordListBox,
        UIName2ID.CommonEmptyContent,
    },
}

UITable[UIName2ID.RoleInfoMPList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoMPList",
    BPKey = "WBP_RoleInfo_HistoricalRecord_MP",
    SubUIs = {
        UIName2ID.RecordListBox,
        UIName2ID.CommonEmptyContent,
    },
}

UITable[UIName2ID.RecordListBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RecordListBox",
    BPKey = "WBP_RoleInfo_HistoricalRecordList",
    SubUIs = {
        UIName2ID.AchievementMainPanel,
    },
}

UITable[UIName2ID.AchievementIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.AchievementIcon",
    BPKey = "WBP_RoleInfo_Achievement",
}

--end

--sol详细历史战绩


UITable[UIName2ID.SolDetailMainPanelHD] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailMainPanelHD",
    BPKey = "WBP_RoleInfo_Detail_MainUI",
    SubUIs = {
        UIName2ID.SolDetailRecordHD,
        UIName2ID.SolDetailDamage,
        UIName2ID.SolDetailKillList,
        UIName2ID.SolDetailRewardHD,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.SolDetailMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailMainPanel",
    BPKey = "WBP_RoleInfo_Detail_MainUI",
    SubUIs = {
        UIName2ID.SolDetailRecord,
        UIName2ID.SolDetailDamage,
        UIName2ID.SolDetailReward,
        UIName2ID.SolDetailKillList,
    },
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.SolDetailRecord] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailRecord",
    BPKey = "WBP_RoleInfo_RecordDetail_SOL",
    SubUIs = {
        UIName2ID.SolDetailRecordBox,
    },
}

UITable[UIName2ID.SolDetailRecordHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailRecord",
    BPKey = "WBP_RoleInfo_RecordDetail_SOL",
    SubUIs = {
        UIName2ID.SolDetailRecordBox,
    },
}

UITable[UIName2ID.SolDetailRecordBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailRecordBox",
    BPKey = "WBP_RoleInfo_RecordDetailBox_SOL",
    SubUIs = {
        UIName2ID.AchievementMainPanel,
    },
}

--淘汰详情
UITable[UIName2ID.SolDetailDamage] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailDamage",
    BPKey = "WBP_RoleInfo_SettlementFailDetail",
    SubUIs = {
        UIName2ID.SolDeadlyComponet,
        UIName2ID.SolHurtComponet,
        UIName2ID.SolAttackComponet,
    },
}

UITable[UIName2ID.SolDeadlyComponet] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.SolDeadlyComponet",
    BPKey = "WBP_SettlementDeadly_RoleInfo",
}

UITable[UIName2ID.SolHurtComponet] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.SolHurtComponet",
    BPKey = "WBP_SettlementUnderAttack_V1_RoleInfo",
}

UITable[UIName2ID.SolAttackComponet] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.SolAttackComponet",
    BPKey = "WBP_SettlementUnderAttack_V2_RoleInfo",
}

UITable[UIName2ID.SolDetailReward] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailReward",
    BPKey = "WBP_RoleInfo_Reward",
    SubUIs = {
        UIName2ID.IVWarehouseTemplate,
    },
}

UITable[UIName2ID.SolDetailRewardHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailReward",
    BPKey = "WBP_RoleInfo_Reward_PC",
    SubUIs = {
        UIName2ID.IVWarehouseTemplate,
    },
}

UITable[UIName2ID.SolDetailKillList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailKillList",
    BPKey = "WBP_SettlementTipsV2_BeatList",
    SubUIs = {
        UIName2ID.CommonEmptyContent,
    },
}

UITable[UIName2ID.SolDetailKillListBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.SolDetailKillListBox",
    BPKey = "WBP_SettlementTipsV2_BeatListItem",
}

--end

--MP详细历史战绩

UITable[UIName2ID.RecordDetailMPMainHD] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.RecordDetailMPMainHD",
    BPKey = "WBP_RoleInfo_RecordDetail_Main",
    SubUIs = {
        UIName2ID.MPDetailRecordHD,
        UIName2ID.MPScoreBoardHD,
    },
}

UITable[UIName2ID.MPDetailRecordHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPDetailRecord",
    BPKey = "WBP_RoleInfo_RecordDetail_MP_PC"
}

UITable[UIName2ID.MPScoreBoardHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPScoreBoard",
    BPKey = "WBP_RoleInfo_DetailedData_MP_PC"
}

UITable[UIName2ID.MPScoreBoard] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPScoreBoard",
    BPKey = "WBP_RoleInfo_DetailedData_02"
}

UITable[UIName2ID.MPDetailRecord] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPDetailRecord",
    BPKey = "WBP_RoleInfo_RecordDetail_MP"
}

UITable[UIName2ID.RecordDataBoxMP] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.RecordDataBoxMP",
    BPKey = "WBP_RoleInfo_DetailedData_Box_02"
}

UITable[UIName2ID.MPScoreDetailHD] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPScoreDetail",
    BPKey = "WBP_SettlementBattle_Integral_PC_S",
    Anim = {
        bManuelAnim = true
    },
    IsModal = true,
}

UITable[UIName2ID.MPScoreDetail] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPScoreDetail",
    BPKey = "WBP_SettlementBattle_Integral",
    Anim = {
        bManuelAnim = true
    },
    IsModal = true,
}

UITable[UIName2ID.BattleScoreMedal] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.BattleScoreMedal",
    BPKey = "WBP_BattleScoreMedal"
}

UITable[UIName2ID.MPDetailRecordBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.MPDetailRecordBox",
    BPKey = "WBP_RoleInfo_RecordDetailBox_MP",
    SubUIs = {
        UIName2ID.AchievementMainPanel,
    },
}

UITable[UIName2ID.AchievementListTips] = {
    UILayer = EUILayer.Tip,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.AchievementListTips",
    BPKey = "WBP_RoleInfo_DetailsTip_Scroll",
    ReConfig = {
        IsPoolEnable = true,
    },
    SubUIs = {
        UIName2ID.AchievementTip,
    }
}

UITable[UIName2ID.AchievementTip] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.AchievementTip",
    BPKey = "WBP_RoleInfo_MessageTips",
}

--end

--Raid详细历史战绩

UITable[UIName2ID.RaidDetailRecord] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.RaidDetailRecord",
    BPKey = "WBP_RoleInfo_RecordDetail_RAID",
    SubUIs = {
        UIName2ID.RaidDetailRecordBox,
    },
}

UITable[UIName2ID.RaidDetailRecordBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.RaidDetailRecordBox",
    BPKey = "WBP_RoleInfo_RecordDetailBox_RAID",

}

--end

--竞技场详情
UITable[UIName2ID.ArenaDetailMainPanel] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.ArenaDetailMainPanel",
    BPKey = "WBP_RoleInfo_Detail_MainUI",
    SubUIs = {
        UIName2ID.ArenaDetailRecordHD,
        UIName2ID.ArenaScordBoardHD
    },
    ReConfig = {
        IsPoolEnable = true,
    },
}

UITable[UIName2ID.ArenaDetailRecord] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.ArenaDetailRecord",
    BPKey = "WBP_TheArena_Record",
}

UITable[UIName2ID.ArenaDetailRecordHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.ArenaDetailRecord",
    BPKey = "WBP_TheArena_Record",
}

UITable[UIName2ID.ArenaDetailRecordBox] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.ArenaDetailRecordBox",
    BPKey = "WBP_TheArena_RecordItem",
}

UITable[UIName2ID.ArenaScordBoard] = {
    UILayer = EUILayer.Stack,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.ArenaScordBoard",
    BPKey = "WBP_TheArena_Results",
    SubUIs = {
        UIName2ID.ArenaTeamDetailInfoItem,
    },
}

UITable[UIName2ID.ArenaScordBoardHD] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.DetailRecord.ArenaScordBoard",
    BPKey = "WBP_TheArena_Results",
    SubUIs = {
        UIName2ID.ArenaTeamDetailInfoItem,
    },
}

--社交定制

UITable[UIName2ID.RoleInfoSocialChange] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoSocialChange",
    BPKey = "WBP_RoleInfo_ChangeMainPanel",
    SubUIs = {
        UIName2ID.SocialChangeAvatar,
        UIName2ID.SocialChangeMilitary,
        UIName2ID.SocialChangeTitle,
        UIName2ID.SocialChangeBadge,
    },
}

UITable[UIName2ID.SocialChangeAvatar] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.SocialChangeAvatar",
    BPKey = "WBP_RoleInfo_ChangeAvatar",
    SubUIs = {
        UIName2ID.RoleInfoAvtar,
    },
}

UITable[UIName2ID.SocialChangeMilitary] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.SocialChangeAvatar",
    BPKey = "WBP_RoleInfo_ChangeMilitaryCard",
}

UITable[UIName2ID.SocialChangeTitle] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.SocialChangeTitle",
    BPKey = "WBP_RoleInfo_Title",
    Anim = {
        FlowInAni = "WBP_RoleInfo_Title_in",
        FlowOutAni = "WBP_RoleInfo_Title_out",
    },
    SubUIs = {
        UIName2ID.SocialTitleItem_Big,
        UIName2ID.CommonSocialTitleItem_Big,
    },
}

UITable[UIName2ID.SocialChangeBadge] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.SocialChangeBadge",
    BPKey = "WBP_RoleInfo_ChangeBadge",
    Anim = {
        FlowInAni = "WBP_RoleInfo_ChangeAvatar_in",
        FlowOutAni = "WBP_RoleInfo_ChangeAvatar_out",
    },
    SubUIs = {
        UIName2ID.SocialBadgeItem,
    },
}

UITable[UIName2ID.BadgeSyncHero] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.BadgeSyncHero",
    BPKey = "WBP_RoleInfo_Synchronous",
    SubUIs = {
        UIName2ID.BadgeSyncHeroItem,
    },
    IsModal = true,
}

UITable[UIName2ID.BadgeSyncHeroItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.BadgeSyncHeroItem",
    BPKey = "WBP_RoleInfo_SynchronousHero"
}

UITable[UIName2ID.BadgeGetPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.BadgeGetPop",
    BPKey = "WBP_Achievement_SideTips"
}

UITable[UIName2ID.SocialBadgeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.SocialBadgeItem",
    BPKey = "WBP_RoleInfo_ChangeBadge_Item",
    SubUIs = {
        UIName2ID.DragBadgeItemPreview,
    },
}

UITable[UIName2ID.RoleInfoAvtar] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoAvtar",
    BPKey = "WBP_RoleInfo_Avatars",
    SubUIs = {
        UIName2ID.SelectedComponent,
        UIName2ID.UsingComponent,
        UIName2ID.LockComponent,
    },
}

UITable[UIName2ID.RoleInfoMilitaryCard] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoAvtar",
    BPKey = "WBP_RoleInfo_MilitaryCardItem",
    SubUIs = {
        UIName2ID.SelectedComponent,
        UIName2ID.UsingComponent,
        UIName2ID.LockComponent,
    },
}

-- 列表元素
UITable[UIName2ID.SocialTitleItem_Big] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.SocialTitleItem_Big",
    BPKey = "WBP_RoleInfo_TitleItem_Big",
    SubUIs = {
        UIName2ID.SelectedComponent,
        UIName2ID.UsingComponent,
        UIName2ID.LockComponent,
    },
}

-- 通用展示
UITable[UIName2ID.CommonSocialTitleItem_Big] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.CommonSocialTitleItem_Big",
    BPKey = "WBP_RoleInfo_TitleItem",
    Anim = {
		bManuelAnim = true,
	}
}

UITable[UIName2ID.SocialTitleItem_Small] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.SocialTitleItem_Small",
    BPKey = "WBP_RoleInfo_TitleItem_Small",
}


UITable[UIName2ID.RewardSocialTitle] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.RewardSocialTitle",
    BPKey = "WBP_Hero_Title",
    Anim = {
        FlowInAni = "WBP_Hero_Title_in",
        FlowOutAni = "WBP_Hero_Title_out",
    },
    SubUIs = {
        UIName2ID.RewardDetail,
    }
}


--社交item子config

UITable[UIName2ID.SelectedComponent] = {
    UILayer = EUILayer.Sub,
    BPKey = "WBP_SlotCompSelected"
}

UITable[UIName2ID.UsingComponent] = {
    UILayer = EUILayer.Sub,
    BPKey = "WBP_SlotCompUsing"
}

UITable[UIName2ID.LockComponent] = {
    UILayer = EUILayer.Sub,
    BPKey = "WBP_SlotCompMaskSmallLock",
}


--end

UITable[UIName2ID.RoleBadgeChoice] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.RoleBadgeChoice",
    BPKey = "WBP_RoleInfo_BadgeChoice",
    SubUIs = {
        UIName2ID.HeroBadgeItem,
    },
    IsModal = true,
}

UITable[UIName2ID.BadgeChoiceList] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.BadgeChoiceList",
    BPKey = "WBP_RoleInfo_BadgeChoiceList",
    SubUIs = {
        UIName2ID.HeroBadgeItem,
    },
}

UITable[UIName2ID.BadgeDisplayItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.BadgeDisplayItem",
    BPKey = "WBP_RoleInfo_BadgeDisplay",
    SubUIs = {
        UIName2ID.RoleItemHighlight,
    },
}

UITable[UIName2ID.RolePlayerCard] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RolePlayerCard",
    BPKey = "WBP_RoleInfo_DatingCard"
}

UITable[UIName2ID.RoleInfoWeaponUnlockButton] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.WeaponUnlock.RoleInfoWeaponUnlockButton",
    BPKey = "WBP_RoleInfo_WeaponUnlock_Btn",
    Anim = {
        FlowInAni = "WBP_RoleInfo_WeaponUnlock_Btn_in",
        FlowOutAni = "WBP_RoleInfo_WeaponUnlock_Btn_out"
    }
}
UITable[UIName2ID.RoleInfoWeaponUnlockProgress] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.WeaponUnlock.RoleInfoWeaponUnlockProgress",
    BPKey = "WBP_RoleInfo_WeaponUnlock_Progress",
    -- Anim = {
    --     FlowInAni = "WBP_RoleInfo_WeaponUnlock_Progress_in",
    --     FlowOutAni = "WBP_RoleInfo_WeaponUnlock_Progress_out"
    -- }
}

UITable[UIName2ID.RoleItemHighlight] = {
    UILayer = EUILayer.Sub,
    BPKey = "WVP_Common_DragSelectedBox"
}

-- 账号成就
UITable[UIName2ID.RoleInfoAchievement] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleSubUI.RoleInfoAchievement",
    BPKey = "WBP_Achievement_Main",

    SubUIs = {
        UIName2ID.RoleInfoAchivenmentListItem,
        -- UIName2ID.HeroBadgeDetail,
    },
    Anim = {
        FlowInAni = "WBP_Achievement_Main_in"
    }
}

UITable[UIName2ID.HeroBadgeDetail] = {
    UILayer = EUILayer.Sub,
    BPKey = "WBP_AchievementDetails",
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.HeroBadgeDetail",
}

UITable[UIName2ID.RoleInfoAchivenmentListItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleInfoAchivenmentListItem",
    BPKey = "WBP_Achievement_SeriesAchievements",
    SubUIs = {
        UIName2ID.AchievementBadgeItem,
    }
}

UITable[UIName2ID.AchievementBadgeItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.AchievementBadgeItem",
    BPKey = "WBP_Achievement_BadgeItem",
}

UITable[UIName2ID.RoleLevelIcon] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.RoleLevelIcon",
    BPKey = "WBP_RoleInfo_RankRewards",
}

UITable[UIName2ID.ItemBook] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.ItemBook",
    BPKey = "WBP_RoleInfo_CollectionProgress",
    SubUIs = {
        UIName2ID.IVCommonItemTemplate
    },
    IsModal = true,
}

UITable[UIName2ID.CommanderRankPop] = {
    UILayer = EUILayer.Pop,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.Pop.CommanderRankPop",
    BPKey = "WBP_RoleInfo_RankDisplay",
    IsModal = true
}

UITable[UIName2ID.WinnerItem] = {
    UILayer = EUILayer.Sub,
    LuaPath = "DFM.Business.Module.RoleInfoModule.UI.RoleComponent.WinnerItem",
    BPKey = "WBP_RoleInfo_RankDisplayIcon"
}

UITable[UIName2ID.CybercafeMainPanel] = {
}


local RoleInfoConfig = {
    evtRoleSeasonChanged = LuaEvent:NewIns("evtRoleSeasonChanged"),
    CardTable = Facade.TableManager:GetTable("RoleQualityConfig"),
    MapTable = Facade.TableManager:GetTable("MapConfig"),

    Event = {
        evtRoleMainPanelOpen = LuaEvent:NewIns("evtRoleMainPanelOpen"),
        evtRoleMainPanelClose = LuaEvent:NewIns("evtRoleMainPanelClose"),
        evtRefreshSelfModel = LuaEvent:NewIns("evtRefreshSelfModel"),
        evtSaveSelfModel = LuaEvent:NewIns("evtSaveSelfModel"),
        evtOpenSocialChangePanel = LuaEvent:NewIns("evtOpenSocialChangePanel"),
        -- BEGIN MODIFICATION @ VIRTUOS
        evtBadgeSlotSelected = LuaEvent:NewIns("evtBadgeSlotSelected"),
        -- END MODIFICATION
        evtBattleScoreMedalOnHover = LuaEvent:NewIns("evtBattleScoreMedalOnHover"),
        evtOnTitleItemBigSelectedBtnClicked = LuaEvent:NewIns("RoleInfo.evtOnTitleItemBigSelectedBtnClicked"),
        evtAchievementListSelectChange = LuaEvent:NewIns("RoleInfo.evtAchievementListSelectChange"),
        evtAchievementSelectChange = LuaEvent:NewIns("RoleInfo.evtAchievementSelectChange"),
        evtSocialBadgeSelectChange = LuaEvent:NewIns("RoleInfo.evtSocialBadgeSelectChange"),
        evtSocialBadgeHeroChange = LuaEvent:NewIns("RoleInfo.evtSocialBadgeHeroChange"),
        evtSocialBadgeSyncHero = LuaEvent:NewIns("RoleInfo.evtSocialBadgeSyncHero"),
        evtSocialBadgeSyncHeroDeselectAll = LuaEvent:NewIns("RoleInfo.evtSocialBadgeSyncHeroDeselectAll"),
    },

    InformationImagePath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0004.RoleInfo_Icon_0004'",
    RecordImagePath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0005.RoleInfo_Icon_0005'",
    DetailImagePath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0014.RoleInfo_Icon_0014'",
    SocialImagePath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Vehicles_Icon_0001.CommonHall_Vehicles_Icon_0001",
    ReputationImagePath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_Reputation.CommonHall_Icon_Reputation",
    NoRankImagePath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/Sp/CommonHall_Sp_NullDan.CommonHall_Sp_NullDan",
    AchievementImagePath = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hero_Icon_10.CommonHall_Hero_Icon_10'",

    SolDamagePath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0015.RoleInfo_Icon_0015'",
    SolRewardPath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0006.RoleInfo_Icon_0006'",
    SolKillListPath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0013.RoleInfo_Icon_0013'",

    MPWinImagePath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0002.RoleInfo_Icon_0002'",
    MPFailImagePath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_0001.RoleInfo_Icon_0001'",

    KillerAITypePath = "PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_De_198.Settlement_De_198'",
    KillerPlayerTypePath = "PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_De_199.Settlement_De_199'",

    MissKillerPath = "PaperSprite'/Game/UI/UIAtlas/System/Settlement/BakedSprite/Settlement_Icon_0020.Settlement_Icon_0020'",
    CollectRoomImgPath = "PaperSprite'/Game/UI/UIAtlas/System/PathofGrowth/BakedSprite/PatnofGrowth_SecretService_10.PatnofGrowth_SecretService_10'",

    SolRecordImagePath = {
        [EGspPlayerResultType.kGspPlayerResultEscaped] = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSpriteRoleInfo_Icon_Leave.RoleInfo_Icon_Leave'",
        [EGspPlayerResultType.kGspPlayerResultKilled] = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_Disapper.RoleInfo_Icon_Disapper'",
        [EGspPlayerResultType.kGspPlayerResultMissing] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hall_Icon_0213.CommonHall_Hall_Icon_0213'",
    },

    -- TitleDefaultIcon = "PaperSprite'/Game/UI/UIAtlas/Texture/HeroTitle/Title_Big_Icon_0001.Title_Big_Icon_0001'", 
    -- TitleDefaultBigTexture = "PaperSprite'/Game/UI/UIAtlas/Texture/HeroTitle/Title_Big_000.Title_Big_000'", -- 大底图
    -- TitleDefaultSmallTexture ="PaperSprite'/Game/UI/UIAtlas/Texture/HeroTitle/Title_Big_000.Title_Big_000'", -- 小称号带Icon

    Loc = {
        BasicGrowthRoad = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicGrowthRoad", "成长之路"),

        BasicStatisticsName0 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName0", "仓库物品总价值"),
        BasicStatisticsName1 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName1", "总战局数"),
        BasicStatisticsName2 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName2", "成功撤离数"),
        BasicStatisticsName3 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName3", "阵亡次数"),
        BasicStatisticsName4 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName4", "总淘汰数"),
        BasicStatisticsName5 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName5", "幸存率"),
        BasicStatisticsName6 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName6", "最长连续撤离数"),
        BasicStatisticsName7 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName7", "平均存活时间"),
        BasicStatisticsName8 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BasicStatisticsName8", "K/D"),
        RadraListName0 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RadraListName0", "战损比"),
        RadraListName4 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RadraListName4", "获胜率"),
        CopyTips = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_CopyTips", "玩家ID已复制到剪贴板"),
        RoleInformationTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleInformationTxt", "个人信息"),
        RolePlayerRecordTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RolePlayerRecordTxt", "历史战绩"),
        RoleDetailTabTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleDetailTabTxt", "详细数据"),
        AchievementTabTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleAchievementTabTxt", "账号成就"),
        RoleSocialTabTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleSocialTabTxt", "社交定制"),
        ReputationTabTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ReputationTabTxt", "信誉档案"),
        RoleDroTxtSol = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleDroTxt1", "烽火地带"),
        RoleDroTxtRaid = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleDroTxt2", "合作行动"),
        RoleDroTxtMP = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleDroTxt3", "全面战场"),
        RoleSolDroTxt1 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleSolDroTxt1", "总览"),
        RoleSolDroTxt2 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleSolDroTxt2", "PMC"),
        RoleSolDroTxt3 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleSolDroTxt3", "SCAV"),
        RoleRaidDroTxt1 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleRaidDroTxt1", "响尾蛇"),
        RoleRaidDroTxt2 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleRaidDroTxt2", "瓦尔基里"),
        RoleMPDroTxt1 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleMPDroTxt1", "总览"),
        RoleMPDroTxt2 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleMPDroTxt2", "经典模式"),
        RoleMPDroTxt3 = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleMPDroTxt3", "攻防模式"),
        GameAllTime = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_GameAllTime", "游戏时长"),
        SolSuccessPrecent = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolSuccessPrecent", "撤离率"),
        HelpMoneyCount = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_HelpMoneyCount", "累计帮带资产"),
        TotalMoneyCount = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_TotalMoneyCount", "总资产"),
        KillEnemyCount = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KillEnemyCount", "击败玩家数"),
        PlayerLevel = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PlayerLevel", "账号等级:Lv"),
        PlayerPraised = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PlayerPraised", "点赞："),
        PlayerId = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PlayerId", "编号:"),
        RaidTotalPassPerfect = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidTotalPassPerfect", "完美通关次数"),
        RaidPassPercent = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidPassPercent", "通关率"),
        RaidHallFirstPassTime = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidHallFirstPassTime", "炼狱首通日期"),
        RaidHallFastPassTime = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidHallFastPassTime", "炼狱通关最快时间"),
        MPTotalMVP = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MPTotalMVP", "MVP次数"),
        NoPass = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_NoPass", "未通关"),

        RecordText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RecordText", "战绩"),
        ScoreBoard = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ScoreBoard", "计分板"),


        SolRecordTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolRecordTxt", "战绩详情"),
        SolRecordDamageTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolRecordDamage", "淘汰详情"),
        SolRecordRewardTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RewardTxt", "行动收获"),
        SolRecordKillListTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KillListTxt", "击败列表"),

        ArenaRecordTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ArenaRecordTxt", "战绩"),
        ArenaScordTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ArenaScordTxt", "赛绩"),

        NoLimit = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_NoLimit", "不限"),
        RoleMainTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleMainTitle", "角色信息"),
        RecordTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RecordTitle", "详细战绩"),
        ArenaRecordTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ArenaRecordTitle", "赛绩"),
        AccountLevelUnlockTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AccountLevelUnlockTitle", "账号等级解锁"),

        TitleRegionServer = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_TitleRegionServer", "三角洲"),

        MoreRecordTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MoreRecordTitle", "更多"),

        TeamPlay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_TeamPlay", "开黑队友"),
        DataPlay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_DataPlay", "伴侣"),
        SifuPlay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SifuPlay", "师傅"),
        TudiPlay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_TudiPlay", "徒弟"),

        AllDay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MorningDay", "全天"),
        CenterDay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_CenterDay", "中午"),
        NightDay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_NightDay", "晚上"),

        WorkDay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_WorkDay", "工作日"),
        WeekEnd = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_WeekEnd", "周末"),
        EveryDay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_EveryDay", "每天"),
        KillPlayer = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KillPlayer", "击败玩家"),
        RaidScore = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidScore", "得分"),
        PlayerCardTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PlayerCardTxt", "我想找：%s\n常在时段：%s\n模式偏好：%s"),

        MillonMoneyTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MillonMoneyTxt", "%s百万"),
        IconUse = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_IconUse", "使用"),
        IconIsUse = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_IconIsUse", "使用中"),
        DefaultIcon = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_DefaultIcon", "默认头像"),
        PlatformIcon = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_Platform", "平台头像"),
        LockIcon = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_LockIcon", "未解锁"),
        Unequip = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_Unequip", "卸下"),
        JumpGoto = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_JumpGoto", "前往"),

        DefaultMilitary = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_DefaultMilitary", "默认军牌"),
        DefaultTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_DefaultTitle", "默认称号"),


        RoleWinTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleWinTxt", "胜利"),
        RoleLoseTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleLoseTxt", "失败"),
        RoleQuitTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleQuitTxt", "中途退出"),

        roleResetTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_roleResetTxt", "重置"),

        noReportPlayer = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_noReportPlayer", "没有可以举报的玩家"),

        noAddFriendPlayer = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_noAddFriendPlayer", "没有可以添加好友的玩家"),

        MPIsAttackNo = "HAAVK",
        MPIsAttackYes = "GTI",

        SolInGameTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolInGameTxt", "游戏中"),

        SOLLevelText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SOLLevelText", "行动等级：%s"),
        MPLevelText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MPLevelText", "战场等级：%s"),

        SOLLevelDesc = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SOLLevelDesc", "行动等级"),
        MPLevelDesc = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MPLevelDesc", "战场等级"),
        MPStarDesc = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MPStarDesc", "战场星级"),
        SOLRoomDesc = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SOLRoomDesc", "收藏室"),
        SOLRoomBook = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SOLRoomBook", "收藏室图鉴"),
        CollectionNumFormat = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_CollectionNumFormat", "图鉴收集：%s"),
        
        LevelText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_LevelText", "等级：%s"),

        SocialAvatarTab = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialAvatarTab", "头像"),
        SocialMilitaryTab = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialMilitaryTab", "军牌"),
        SocialTitleTab = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialTitleTab", "称号"),
        SocialBadgeTab = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialBadgeTab", "徽章"),
        NoRankDataTxt = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_NoRankDataTxt", "无段位"),

        KdTipShow = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KdTipShow", "分别为常规、机密、绝密行动中的数据"),

        RankScoreUpdateAdd = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RankScoreUpdateAdd", "%s<customstyle color=\"Color_Highlight01\">(%s)</>"),

        solKdaText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_solKdaText", "%s <dfmrichtext type=\"img\" width=\"4\" height=\"55\" id=\"WhiteLine\"/> %s <dfmrichtext type=\"img\" width=\"4\" height=\"55\" id=\"WhiteLine\"/> %s"),

        RoleMinete = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleMinete", "分钟"),

        QuitRoleMain = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_QuitRoleMain", "个人形象信息未保存，是否退出个人信息界面"),

        RecordMapTimeStr = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RecordMapTimeStr", "%s-%s %s"),

        SeasonOverview = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SeasonOverview", "赛季总览"),

        SeasonNumber = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SeasonNumber", "赛季%s"),

        NoRecordData = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_NoRecordData", "暂无历史战绩"),

        HideRecordData = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_HideRecordData", "该玩家隐藏了战绩"),

        DataMissing = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_DataMissing", "数据丢失"),
        AttackWeaponAmmoItem = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AttackWeaponAmmoItem", "敌方攻击：%s │ %s"),
        AttackWeaponItem = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AttackWeaponItem", "敌方攻击：%s"),
        AttackWeaponItemAI = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AttackWeaponItemAI", "敌方攻击：%s │ %s 级子弹"),
        AmmoItemDataText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AmmoItemDataText", "我方防具：%s（%s/%s），耐久度%s"),
        AmmoItemNameText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AmmoItemNameText", "我方防具：%s"),

        SelfAttackWeaponAmmoItem = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SelfAttackWeaponAmmoItem", "我方攻击：%s │ %s"),
        SelfAttackWeaponItem = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SelfAttackWeaponItem", "我方攻击：%s"),
        AnmyAmmoItemDataText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AnmyAmmoItemDataText", "敌方防具：%s（%s/%s）"),
        AnmyAmmoItemNameText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AnmyAmmoItemNameText", "敌方防具：%s"),

        BadgeItemTips = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BadgeItemTips", "解锁时间：%s\n%s"),

        AttackDistance = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AttackDistance", "来自 %s 米"),
        RewardPrice = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RewardPrice", "行动收获：%s"),

        OtherDeathSeason = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_OtherDeathSeason", "其他意外死因"),

        SeasonNow = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SeasonNow", "当前赛季"),
        SeasonAll = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SeasonAll", "赛季总览"),

        RecordMin = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RecordMin", "min"),
        RecordHour = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RecordHour", "h"),

        KillListEmpty = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KillListEmpty", "无击败数据"),

        MpMadelTipTxt =  NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MpMadelTipTxt", "共有战斗、占领、后勤、信息，四类勋章。通过不同行为获得勋章积分提高勋章等级"),

        MpQuitTipTxt =  NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MpQuitTipTxt", "胜利和失败都会获得对战奖励，中途退出会随着退出次数变多逐渐受到严厉的惩罚"),

        StudentBuffTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_StudentBuffTitle", "三角洲行动特权"),
        StudentBuff = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_StudentBuff", "<dfmrichtext type=\"img\" id=\"School\"/>高校特权"),
        StudentBuffExp = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_StudentBuffExp", "·额外经验加成<customstyle color=\"Color_Highlight02\"> %s%%</>"),

        SocialLeftDay = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialLeftDay", "%s天"),
        SocialLeftHour = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialLeftHour", "%s小时"),
        SocialLeftOneHour = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialLeftOneHour", "<1小时"),
        SocialLeftEnd = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialLeftEnd", "%s 过期"),

        SocialUnavailableText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialUnavailiableText", "尽请期待"),
        SocialGoObtain = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SocialGoObtain", "前往获取"),

        RaidGameFail = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidGameFail", "行动失败"),
        RaidGameSuccess = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidGameSuccess", "行动成功"),

        CareerTop = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_CareerTop", "生涯最高"),
        SeasonTop = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SeasonTop", "赛季最高"),

        SocialGainTitleText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SoicalGainTitleText", "获得称号"),
        MPDetailRecordSocoreText = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_MPDetailRecordSocoreText", "{curRankSoce} ({addScore})"),

        AddFriendPath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_04.RoleInfo_Btn_04'",
        ReportPath = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_02.RoleInfo_Btn_02'",
        AddFriendEnd = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Btn_08.RoleInfo_Btn_08'",

        SocialTipImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_PlayerState_Icon_0301.Common_PlayerState_Icon_0301'",
        SocialTimeImage = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0011.Common_ItemProp_Icon_0011'",

        ClickTooFast = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ClickTooFast", "您的操作过于频繁, 请稍后再试"),
        All = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RoleAchievementAll", "全部"),

        PenetrationInfo = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PenetrationInfo", "该伤害为穿透后衰减伤害，穿透等级衰减%d级"),
        PenetrationInfoNodecrease = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PenetrationInfoNodecrease", "该伤害为穿透伤害，穿透等级不衰减"),
        BadgeUnlockTip = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BadgeUnlockTip", "完成任务解锁"),
        BadgeFullTip = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BadgeFullTip", "栏位已满，需卸下后再装配"),
        BadgeSyncTip = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BadgeSyncTip", "已同步应用至所选干员名片"),

        ScoreDetailTitle = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ScoreDetailTitle", "军功结算"),
        PathToPromotion = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PathToPromotion", "晋升之路"),
        WinnerTakesAll = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_WinnerTakesAll", "胜者为王"),

        DataAll = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_DataAll", "数据总览"),
        PathToPromotionData = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_PathToPromotionData", "晋升之路数据"),
        WinnerTakesAllData = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_WinnerTakesAllData", "胜者为王数据"),
        RankData = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RankData", "排位赛数据"),

        SolResultIcon = {
            [EGspPlayerResultType.kGspPlayerResultEscaped] = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_Leave.RoleInfo_Icon_Leave'",
            [EGspPlayerResultType.kGspPlayerResultKilled] = "PaperSprite'/Game/UI/UIAtlas/System/RoleInfo/BakedSprite/RoleInfo_Icon_Disapper.RoleInfo_Icon_Disapper'",
            [EGspPlayerResultType.kGspPlayerResultMissing] = "PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Hall_Icon_0213.CommonHall_Hall_Icon_0213'",
        },

        ArenaResultTxt = {
            [ArenaResult.AR_None] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AR_None", "中途退出"),
            [ArenaResult.AR_Win] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AR_Win", "胜利"),
            [ArenaResult.AR_Loss] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AR_Loss", "失败"),
            [ArenaResult.AR_Draw] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AR_Draw", "平局"),
        },
        RaidLevel = {
            [1] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidEsay", "简单"),
            [2] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidNormal", "困难"),
            [3] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidHight", "炼狱"),
        },
        SoLDetailTxt = {
            [1] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolDetailMoney", "总资产"),
            [2] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolDetailPlayer", "玩家"),
            [3] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolDetailEnmy", "敌方势力"),
            [4] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolDetailBoss", "首领"),
            [5] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolDetailHelp", "救助次数"),
            [6] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_SolDetailLiveTime", "生存时间"),
        },
        RaidDetailTxt = {
            [1] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidDetailLevel", "评级"),
            [2] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidDetailScore", "得分"),
            [3] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidDetailLive", "通关命数"),
            [4] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidDetailBoss", "击败数"),
            [5] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidDetailHelp", "救助次数"),
            [6] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_RaidDetailPassTime", "通关时间"),
        },

        ScoreAddText = {
            [EAddRankScoreReason.EAddRankScoreReason_KillAI] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KillAI", "击杀AI：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_KillPlayer] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_KillPlayerScore", "击杀玩家：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_LootingBox] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_LootingBox", "搜索箱子：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_Escape_Failed] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_Escape_Failed", "撤离失败：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_Contract] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_Contract", "完成合同：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_BluePrint] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BluePrint", "成功破译曼德尔砖：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_CarryOutPrice] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_CarryOutPrice", "带出资产分：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_AssistKillPlayer] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_AssistKillPlayer", "助攻击杀：%s"),
            [EAddRankScoreReason.EAddRankScoreReason_HighQualityPrice] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_HighQualityPrice", "带出高价值道具：+%s"),
            [EAddRankScoreReason.EAddRankScoreReason_ReputationAward] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_ReputationAward", "信誉非凡每日首胜：+%s"),
        },

        SolResultColor = {
            [EGspPlayerResultType.kGspPlayerResultEscaped] = Facade.ColorManager:GetSlateColor("LightPositive"),
            [EGspPlayerResultType.kGspPlayerResultKilled] = Facade.ColorManager:GetSlateColor("Basic_White"),
            [EGspPlayerResultType.kGspPlayerResultMissing] = Facade.ColorManager:GetSlateColor("Basic_White"),
            [EGspPlayerResultType.kGspPlayerResultUndetermined] = Facade.ColorManager:GetSlateColor("Highlight01"),
            [EGspPlayerResultType.kGspPlayerResultQuit] = Facade.ColorManager:GetSlateColor("Basic_White"),
            [EGspPlayerResultType.kGspPlayerResultGiveUp] = Facade.ColorManager:GetSlateColor("Basic_White"),
        },

        BookTitles = {
            [1] = NSLOCTEXT("RoleInfoModule", "Lua_RoleInfo_BookGun", "枪械")
        }
    },

    TitleRankingText =  "#<customstyle color=\"Emphasize\" size=\"Size_BoldTitle04\">%d</>",
    TitleRankingTextSmall =  "<customstyle color=\"Emphasize\" size=\"Size_TinyContent04\">%d</>",
    TitleRankingTextListItem = "<customstyle color=\"Emphasize\" size=\"Size_TinyContent04\">#</>%d",

    PrisonLowMapModeId = 148801103,
    PrisonMediumMapModeId = 148802113,
    PrisonHighMapModeId = 148803123
}

return RoleInfoConfig
