ServerTipCode = {}
--------------------------------------------------------------------------
--- 购买相关Server Tip
--------------------------------------------------------------------------
ServerTipCode.PanicSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PanicSuccess", "恭喜你，抢购成功")
ServerTipCode.BuySuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_BuySuccess", "购买的<BuyItem>%s</>已存入仓库")
ServerTipCode.BuyCollectionSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_BuyCollectionSuccess", "购买的<BuyItem>%s</>已存入藏品仓库")
ServerTipCode.BuyFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_BuyFail", "<BuyItem>%s</>购买失败")
ServerTipCode.BuyAndEquipSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_BuyAndEquipSuccess", "购买的<BuyItem>%s</>已装备")
ServerTipCode.PanicFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PanicFail", "抢购商品<BuyItem>%s</>已经卖空，祝你下次好运^_^")

ServerTipCode.MultiBuySuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiBuySuccess", "批量购买<BuyItem>%s</>成功")
ServerTipCode.MultiExchangeSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiExchangeSuccess", "批量兑换<BuyItem>%s</>成功")
ServerTipCode.MultiBuySingleSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiBuySingleSuccess", "购买<BuyItem>%s</>成功")
ServerTipCode.MultiExchangeSingleSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiExchangeSingleSuccess", "兑换<BuyItem>%s</>成功")
ServerTipCode.MultiBuyFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiBuyFail", "批量购买失败")
ServerTipCode.MultiExchangeFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiExchangeFail", "批量兑换失败")
ServerTipCode.MultiExchangeNoSpaceFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MultiExchangeNoSpaceFail", "仓库已满，操作失败")

ServerTipCode.AssemblyBuySuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_AssemblyBuySuccess", "成功购买并装备了改装道具<BuyItem>%s</>")
ServerTipCode.AssemblyBuyFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_AssemblyBuyFail", "改装道具<BuyItem>%s</>购买失败")

ServerTipCode.ExchangeSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ExchangeSuccess", "兑换的<BuyItem>%s</>已存入仓库")
ServerTipCode.ExchangeCollectionSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ExchangeCollectionSuccess", "兑换的<BuyItem>%s</>已存入藏品仓库")
ServerTipCode.ExchangeAndEquipSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ExchangeAndEquipSuccess", "兑换的<BuyItem>%s</>已装备")

ServerTipCode.CannotRecycleItem =
    NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_CannotRecycleThisItem", "存在不可回收的道具！出售失败")

ServerTipCode.RenewalSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_RenewalSuccess", "购买成功")
--------------------------------------------------------------------------
--- 预设相关Server Tip
--------------------------------------------------------------------------
ServerTipCode.SaveToPresetSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SaveToPresetSuccess", "方案已保存至<Green>%s</>")
ServerTipCode.NoPrePreset = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_NoPrePreset", "无上次配装")
ServerTipCode.ApplyPresetSuccessfully = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ApplyPresetSuccessfully", "应用配装成功")
ServerTipCode.PagePresetNameChangeTip = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PagePresetNameChangeTip", "配装方案名称修改为<Green>%s</>")
ServerTipCode.SavedSchemeSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SavedSchemeSuccess", "保存方案成功")

--------------------------------------------------------------------------
--- 配装相关Server Tip
--------------------------------------------------------------------------
ServerTipCode.EnterTheRentalStatusTips = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_EnterTheRentalStatusTips", "正在使用<customstyle color=\"Color_Highlight02\">制式套装</>，成功入局后才会消耗制式套装券")
ServerTipCode.ExitTheRentalStatusTips = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ExitTheRentalStatusTips", "取消使用<customstyle color=\"Color_Highlight02\">制式套装</>，本次没有消耗制式套装券")
ServerTipCode.DefaultPlanString = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_DefaultPlanString", "自定义方案%s")

--------------------------------------------------------------------------
--- 负重相关Server Tip
--------------------------------------------------------------------------
ServerTipCode.OverLoadTips = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_OverLoadTips", "已<customstyle color=\"Color_Highlight01\">进入负重状态</>")
ServerTipCode.SuperOverLoadTips = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SuperOverLoadTips", "已<customstyle color=\"Color_DarkNegative\">进入超重状态</>")

--------------------------------------------------------------------------
--- 任务Server Tip
--------------------------------------------------------------------------
ServerTipCode.TrackSucc = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_TrackSucc", "已在局内同步追踪目标")
ServerTipCode.TrackFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_TrackFail", "已取消局内同步追踪目标")
ServerTipCode.TrackReplace = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_TrackReplace", "已替换局内同步追踪目标")
ServerTipCode.AcceptingQuest = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_AcceptingQuest", "已接取任务")
--------------------------------------------------------------------------
--- 天赋Server Tip
--------------------------------------------------------------------------
ServerTipCode.ActivateSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ActivateSuccess", "激活天赋成功！")
ServerTipCode.ActivateFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ActivateFail", "激活天赋失败！")

--- 仓库
ServerTipCode.MeleeWeaponSkinIDZero = NSLOCTEXT("ServerTipCode", "Lua_Inventory_MeleeWeaponSkinIDZero", "近战武器内填写皮肤id为0，请重置账号")
ServerTipCode.MeleeWeaponSyncFailed = NSLOCTEXT("ServerTipCode", "Lua_Inventory_MeleeWeaponSyncFailed", "请求同步近战皮肤和藏品数据失败，请重置账号")
ServerTipCode.MeleeWeaponSkinIdNotExist = NSLOCTEXT("ServerTipCode", "Lua_Inventory_MeleeWeaponSkinIdNotExist", "近战武器内皮肤id版本较老，请重置账号")

ServerTipCode.RepairSuccess = NSLOCTEXT("ServerTipCode", "Lua_Inventory_RepairSuccess", "修理成功")
ServerTipCode.RepairFail = NSLOCTEXT("ServerTipCode", "Lua_Inventory_RepairFail", "维修[%s]失败")
ServerTipCode.MaintainanceSuccess = NSLOCTEXT("ServerTipCode", "Lua_Inventory_MaintainanceSuccess", "保养成功！<ItemHighlight>%s</>已经可以正常使用")
ServerTipCode.MaintainanceFail = NSLOCTEXT("ServerTipCode", "Lua_Inventory_MaintainanceFail", "货币不足，再多积累积累")
ServerTipCode.ReapirSuccess = NSLOCTEXT("ServerTipCode", "Lua_Inventory_ReapirSuccess", "修理成功")
ServerTipCode.SplitDepositSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SplitDepositSuccess", "仓库拆分成功")
ServerTipCode.SplitDepositFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SplitDepositFail", "拆分失败，请转移部分主仓库物品到新拆分的仓库")
ServerTipCode.CombineDepositFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_CombineDepositFail", "仓库合并失败")
ServerTipCode.CombineDepositSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_CombineDepositSuccess", "仓库合并成功")
ServerTipCode.NoAvailableEmptyExtSlot = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_NoAvailableEmptyExtSlot", "可用的扩容位不足")
ServerTipCode.MarkItem = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MarkItem", "<customstyle color=\"Color_Highlight01\">%s</>成功添加至收藏列表")
ServerTipCode.UnmarkItem = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_UnmarkItem", "已从收藏中移除<customstyle color=\"Color_Highlight01\">%s</>")
ServerTipCode.markLackofItems = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_markLackofItems", "缺少的道具已添加至收藏列表")
ServerTipCode.MarkFull = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MarkFull", "收藏道具数量已达上限，请在交易行收藏列表中清理")
ServerTipCode.UnlockKeySpaceSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_UnlockKeySpaceSuccess", "已解锁{SlotNum}个钥匙槽")
ServerTipCode.UnlockKeySpaceFail_LvlNotMatch = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_UnlockKeySpaceFail_LvlNotMatch", "扩容道具等级与槽位等级不匹配")
ServerTipCode.UnlockKeySpaceFail_Common = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_UnlockKeySpaceFail_Common", "钥匙解锁道具使用失败")

ServerTipCode.LevelUpExtItemSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_LevelUpExtItemSuccess", "成功升级为%s")
ServerTipCode.LevelUpExtItemFail_MaterialNotEnough = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_LevelUpExtItemFail_MaterialNotEnough", "升级材料不足")

ServerTipCode.SortSlotSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SortSlotSuccess", "整理成功")
ServerTipCode.SortSlotFail = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SortSlotFail", "剩余空间无法分类换行整理，可前往设置切换整理方式")
ServerTipCode.SortSlotFailTips1 = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SortSlotFailTips1", "当前选中空间无法继续整理，可勾选扩容箱后重试")
ServerTipCode.SortSlotFailTips2 = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SortSlotFailTips2", "空间不足无法自动整理，请手动处理")

ServerTipCode.PullInvDataFailed = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PullInvDataFailed", "拉取仓库数据失败")
ServerTipCode.PullInvDataFailedErrCode = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PullInvDataFailedErrCode", "拉取仓库数据失败，错误码:%s")
ServerTipCode.MovePreBehaveFailed = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_MovePreBehaveFailed", "道具移动预表现与后台计算不一致，请检查。")
ServerTipCode.ReplacePermissionItemFailed = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_ReplacePermissionItemFailed", "仓库空间不足")
ServerTipCode.TransferFailedInsufficientSpace = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_TransferFailedInsufficientSpace", "容器空间不足,无法转移")

ServerTipCode.EquipExtItemSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_EquipExtItemSuccess", "成功安装<customstyle color=\"Color_Highlight01\">%s</>")

ServerTipCode.SellSuccess = NSLOCTEXT("ServerTipCode", "Lua_Inventory_SellSuccess", "出售成功！获得%s")
ServerTipCode.SellFail = NSLOCTEXT("ServerTipCode", "Lua_Shop_SellFail", "出售失败")
ServerTipCode.CantSellGuideItem = NSLOCTEXT("ServerTipCode", "Lua_Shop_CantSellGuideItem", "请解锁交易行后再出售，获得更高收益")
ServerTipCode.SellFailMallCallDeposit = NSLOCTEXT("ServerTipCode", "Lua_Shop_SellFailMallCallDeposit", "货币已达到上限，无法出售")
ServerTipCode.PullCollectionDataFailed = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PullCollectionDataFailed", "拉取藏品仓库数据失败")
ServerTipCode.AssemblyFailed = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_AssemblyFailed", "组装失败：")

ServerTipCode.DepositGuidePropInvalidMove = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_DepositGuidePropInvalidMove", "教学道具，无法执行该操作")

-- 愿望单相关
ServerTipCode.WishItemGetSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_WishItemGetSuccess", "收藏道具<customstyle color=\"Color_Highlight01\">%s</>获取成功")
ServerTipCode.WishItemNumReach = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_WishItemNumReach", "收藏道具<customstyle color=\"Color_Highlight01\">%s</>获取成功，已达到需求数")
ServerTipCode.WishItemStillNeedNum = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_WishItemStillNeedNum", "收藏道具<customstyle color=\"Color_Highlight01\">%s</>获取成功，还需<customstyle color=\"Color_Highlight01\">%s</>个")

-- 礼包 Server Tip
ServerTipCode.RewardOpenBlindBoxFail = NSLOCTEXT("ServerTipCode", "Lua_RewardOpenBlindBox_Fail", "打开失败")
ServerTipCode.RewardOpenBlindBoxNone = NSLOCTEXT("ServerTipCode", "Lua_RewardOpenBlindBox_None", "没有奖励")
ServerTipCode.GetItemTitle = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_GetItemTitle", "获得物品")
ServerTipCode.RewardInventoryIsFull =
    NSLOCTEXT("ServerTipCode", "Lua_RewardOpenBlindBox_InventoryIsFull", "仓库无足够空间，物品已通过邮件送达，请注意查收")
ServerTipCode.RewardInventoryAndEmailIsFull =
    NSLOCTEXT("ServerTipCode", "Lua_RewardOpenBlindBox_InventoryAndEmailIsFull", "仓库和邮箱均无足够空间，请整理后再开启")
ServerTipCode.RepairFail = NSLOCTEXT("ServerTipCode", "Lua_Inventory_RepairFail", "维修[%s]失败")

--------------------------------------------------------------------------
--- 拍卖行Server Tip
--------------------------------------------------------------------------
ServerTipCode.BuyAuctionFailedNotEnough =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionFailedNotEnough", "仓库空间不足，请整理空间后再购买")
ServerTipCode.BuyAuctionPartFailedNotEnough =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionPartFailedNotEnough", "由于仓库空间不足，部分物品购买成功，请整理空间后再购买")
ServerTipCode.BuyAuctionFailedLowestPrice =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionFailedLowestPrice", "购买失败，最低价格道具已售罄")
ServerTipCode.BuyAuctionFailedWeapon =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionFailedWeapon", "所选枪械道具已经售罄")
ServerTipCode.BuyAuctionFailedAlreadyBoughtByOthers =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionFaildAlreadyBoughtByOthers", "此道具已被其他人买走")
ServerTipCode.BuyAuctionFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionFailed", "购买失败")
ServerTipCode.CurrencyNotEnoughBuyAuctionFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_CurrencyNotEnoughBuyAuctionFailed", "购买失败，哈夫币不足")
ServerTipCode.BuyAuctionItemDetail =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionItemDetail", "{Name}x{Num}")
ServerTipCode.BuyAuctionSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionSuccess", "<ItemHighlight>%s</>购买成功，已存入仓库")
ServerTipCode.BatchBuyAuctionSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BatchBuyAuctionSuccess", "批量购买成功，已存入仓库")
ServerTipCode.BuyAuctionSuccessLower =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionSuccessLower", "<ItemHighlight>%s</>购买成功，价格更新，已用更低价格成交")
ServerTipCode.BuyAuctionSuccessSectional =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionSuccessSectional", "<ItemHighlight>%s</>购买成功，交易部分成功，可点击继续购买")
ServerTipCode.BuyAuctionSuccessFailed =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAuctionSuccessFailed", "价格更新，购买失败")
ServerTipCode.AuctionTradingisBooming = NSLOCTEXT("ServerTipCode", "Lua_Auction_AuctionTradingisBooming", "购买过于火爆，请稍后再试~")
ServerTipCode.BatchBuyAuctionFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_BatchBuyAuctionFailed", "<ItemHighlight>%s</>已售罄")
ServerTipCode.BuyAndEquipAuctionSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_BuyAndEquipAuctionSuccess", "<ItemHighlight>%s</>已从交易行购买并成功装备")
ServerTipCode.SellGoodFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_SellGoodFailed", "商品上架失败")
ServerTipCode.AuctionSellLocked = NSLOCTEXT("ServerTipCode", "Lua_Auction_AuctionSellLocked", "3级解锁出售")
ServerTipCode.ResellGoodFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_ResellGoodFailed", "重新上架失败")
ServerTipCode.SellGoodSuccess = NSLOCTEXT("ServerTipCode", "Lua_Auction_SellGoodSuccess", "<ItemHighlight>%s</>已成功上架,售卖结果会以邮件通知")
ServerTipCode.SellAllGoodSuccess = NSLOCTEXT("ServerTipCode", "Lua_Auction_SellAllGoodSuccess", "已成功上架%d件物品")
ServerTipCode.SplitSellGoodFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_SplitSellGoodFailed", "部分物品上架失败，已放回仓库")
ServerTipCode.ReSellGoodSuccess = NSLOCTEXT("ServerTipCode", "Lua_Auction_ReSellGoodSuccess", "<ItemHighlight>%s</>已成功上架")
ServerTipCode.PullOffGoodFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_PullOffGoodFailed", "商品下架失败")
ServerTipCode.PullOffGoodSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_PullOffGoodSuccess", "下架成功，<ItemHighlight>%s</>已送至<ItemHighlight>仓库</>")
ServerTipCode.PullOffGoodSuccess_Mail =
    NSLOCTEXT("ServerTipCode", "Lua_Auction_PullOffGoodSuccess_Mail", "下架成功，<ItemHighlight>%s</>已送至<ItemHighlight>邮箱</>")
ServerTipCode.FetchPlayerInfoFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_FetchPlayerInfoFailed", "拉取交易行个人信息失败")
ServerTipCode.FetchAuctionGoodsFailed = NSLOCTEXT("ServerTipCode", "Lua_Acution_FetchGoodTypeListFailed", "拉取商品列表失败")
ServerTipCode.FetchAuctionSaleMailFailed = NSLOCTEXT("ServerTipCode", "Lua_Auction_FetchAuctionSaleMailFailed", "拉取交易行交易邮件失败")
ServerTipCode.SellPoorWeaponNotAllowed =
    NSLOCTEXT("ServerTipCode", "Lua_Acution_SellPoorWeaponNotAllowed", "<ItemHighlight>磨损</>的武器不可上架")
ServerTipCode.SellBindItemNotAllowed =
    NSLOCTEXT("ServerTipCode", "Lua_Acution_SellBindItemNotAllowed", "<ItemHighlight>绑定状态</>的物品不可上架")
ServerTipCode.SellBindItemNotAllowedWithBindItemName =
    NSLOCTEXT("ServerTipCode","Lua_Acution_SellBindItemNotAllowedWithBindItemName","<ItemHighlight>绑定状态</>的<ItemHighlight>%s</>不可上架")
ServerTipCode.SellLowDurabilityItemNotAllowed =
    NSLOCTEXT("ServerTipCode", "Lua_Acution_SellLowDurabilityItemNotAllowed", "<ItemHighlight>耐久度不足</>的物品不可上架")
ServerTipCode.SellLowDurabilityIsNotFull =
    NSLOCTEXT("ServerTipCode", "Lua_Acution_SellLowDurabilityIsNotFull", "<ItemHighlight>耐久度非满</>无法上架")
    ServerTipCode.SellLowDurabilityIsBelowTheMinimum =
    NSLOCTEXT("ServerTipCode", "Lua_Acution_SellLowDurabilityIsBelowTheMinimum", "<ItemHighlight>耐久度</>低于最低上架要求")
ServerTipCode.SellNotAvailableForSaleAtAuctionItemNotAllowed = NSLOCTEXT("ServerTipCode", "Lua_Acution_SellNotAvailableForSaleAtAuctionItemNotAllowed", "<ItemHighlight>%s</>不可上架")
ServerTipCode.SellNotSellableItemNotAllowed =
    NSLOCTEXT("ServerTipCode", "Lua_Acution_SellNotSellableItemNotAllowed", "<ItemHighlight>%s</>不可交易")
ServerTipCode.SellNotAvailableInMatching = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SellNotAvailableInMatching", "匹配中不可交易")
ServerTipCode.SellNotAvailableInReadying = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SellNotAvailableInReadying", "准备中不可交易")
ServerTipCode.Collected = NSLOCTEXT("ServerTipCode", "Lua_Acution_Collected", "收藏成功")
ServerTipCode.UnCollected = NSLOCTEXT("ServerTipCode", "Lua_Acution_UnCollected", "取消收藏")
ServerTipCode.CollectedFull = NSLOCTEXT("ServerTipCode", "Lua_Acution_CollectedFull", "收藏列表已满")

ServerTipCode.AuctionDataRequestIsEmpty = NSLOCTEXT("ServerTipCode", "Lua_Acution_AuctionDataRequestIsEmpty", "交易行数据请求为空")

ServerTipCode.ChangesInItemTransactionInformation = NSLOCTEXT("ServerTipCode", "Lua_Acution_ChangesInItemTransactionInformation", "物品交易信息发生变化，购买失败")

--------------------------------------------------------------------------
--- 市场Server Tip
--------------------------------------------------------------------------
ServerTipCode.FetchMarketPlayerInfoFailed = NSLOCTEXT("ServerTipCode", "Lua_Market_FetchMarketPlayerInfoFailed", "拉取市场个人信息失败")
ServerTipCode.FetchMarketGoodsFailed = NSLOCTEXT("ServerTipCode", "Lua_Market_FetchMarketGoodsFailed", "拉取市场商品列表失败")
ServerTipCode.MarketDataRequestIsEmpty = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketDataRequestIsEmpty", "市场数据请求为空")
ServerTipCode.FetchMarketSaleMailFailed = NSLOCTEXT("ServerTipCode", "Lua_Market_FetchMarketSaleMailFailed", "拉取市场交易邮件失败")
ServerTipCode.BuyMarketFailedLowestPrice =
    NSLOCTEXT("ServerTipCode", "Lua_Market_BuyMarketFailedLowestPrice", "最低价道具已经售罄")
ServerTipCode.BuyMarketItemDetail =
    NSLOCTEXT("ServerTipCode", "Lua_Market_BuyMarketItemDetail", "{Name}x{Num}")
ServerTipCode.BuyMarketSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_Market_BuyMarketSuccess", "<ItemHighlight>%s</>购买成功，已存入藏品仓库")
ServerTipCode.BuyMarketSuccessLower =
    NSLOCTEXT("ServerTipCode", "Lua_Market_BuyMarketSuccessLower", "价格更新，已用更低价格成交")
ServerTipCode.BuyMarketSuccessSectional =
    NSLOCTEXT("ServerTipCode", "Lua_Market_BuyMarketSuccessSectional", "价格更新，交易部分成功")
ServerTipCode.MarketPullOffGoodSuccess =
    NSLOCTEXT("ServerTipCode", "Lua_Market_MarketPullOffGoodSuccess", "下架成功，<ItemHighlight>%s</>已送至<ItemHighlight>藏品仓库</>")
ServerTipCode.FetchMarketSaleMailFailed = NSLOCTEXT("ServerTipCode", "Lua_Market_FetchMarketSaleMailFailed", "拉取市场交易邮件失败")
ServerTipCode.MarketBuyExceedingLimit = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketBuyExceedingLimit", "购买数量超过日限购数量")
ServerTipCode.BuyMarketSuccessFailed =
    NSLOCTEXT("ServerTipCode", "Lua_Market_BuyMarketSuccessFailed", "价格更新，购买失败")
ServerTipCode.MarketTradingisBooming = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketTradingisBooming", "<ItemHighlight>%s</>购买过于火爆，请稍后再试~")
ServerTipCode.MarketSellExceedingLimit = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketSellExceedingLimit", "当日上架数量已达上限")
ServerTipCode.MarketIsNotOpen = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketIsNotOpen", "未在市场交易开放时间")
ServerTipCode.MarketIsLocked = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketIsLocked", "市场暂未解锁")
ServerTipCode.MarketAddCollectionSuccess = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketAddCollectionSuccess", "成功添加至我的关注")
ServerTipCode.MarketDelCollectionSuccess = NSLOCTEXT("ServerTipCode", "Lua_Market_MarketDelCollectionSuccess", "成功取消关注")

--安全屋Server START
ServerTipCode.SafeHouseSellResult01 =
    NSLOCTEXT("ServerTipCode","Lua_SafeHouse_SafeHouseSellResult01","成功出售<ItemHighlight>%s</>,收入<ItemHighlight>%s</><ItemHighlight>%s</>")
ServerTipCode.GetThingsSuccess = NSLOCTEXT("ServerTipCode","Lua_SafeHouse_GetThingsSuccess","领取成功")
ServerTipCode.VisitorHasLeave = NSLOCTEXT("ServerTipCode","Lua_SafeHouse_VisitorHasLeave","访客已离开")
ServerTipCode.SystemLocked = NSLOCTEXT("ServerTipCode","Lua_SafeHouse_SystemLocked","系统未解锁")
ServerTipCode.SafeHouseEchangeCellBuySuccess = NSLOCTEXT("ServerTipCode","Lua_SafeHouse_EchangeCellBuySuccess","已成功兑换<ItemHighlight>%s</>")

--------------------------------------------------------------------------
--- 邮件Server Tip
--------------------------------------------------------------------------
ServerTipCode.MailReceiveFailed = NSLOCTEXT("ServerTipCode", "Lua_Mail_ReceiveFailed", "领取奖励失败")
ServerTipCode.MailReceiveStackFull = NSLOCTEXT("ServerTipCode", "Lua_Mail_ReceiveStackFull", "物品堆叠已满")
ServerTipCode.MailInventoryIsFull = NSLOCTEXT("ServerTipCode", "Lua_Mail_InventoryIsFull", "<customstyle color=\"Color_DarkNegative\">仓库已满</>请清理后重试")
ServerTipCode.MailFullDepositReceivePart = NSLOCTEXT("ServerTipCode", "Lua_Mail_FullDepositReceivePart", "仓库已满,仅领取部分邮件")
ServerTipCode.MailReleaseAllNewMailSuccess = NSLOCTEXT("ServerTipCode", "Lua_Mail_ReleaseAllNewMailSuccess", "清理成功")
ServerTipCode.MailReleaseAllNewMailFail = NSLOCTEXT("ServerTipCode", "Lua_Mail_ReleaseAllNewMailFail", "清理失败")
ServerTipCode.DepositCurrencyOverFlow = NSLOCTEXT("ServerTipCode", "Lua_Mail_DepositCurrencyOverFlow", "哈夫币已达上限")

--------------------------------------------------------------------------
--- SystemSettingServer Tip
--------------------------------------------------------------------------
ServerTipCode.SaveCustomLayoutComplete =
    NSLOCTEXT("ServerTipCode", "Lua_SystemSetting_SaveCustomLayoutComplete", "布局已保存")
ServerTipCode.SwitchCustomLayoutComplete =
    NSLOCTEXT("ServerTipCode", "Lua_SystemSetting_SwitchCustomLayoutComplete", "已切换至%s")
ServerTipCode.SaveCustomSchemeSuccess = NSLOCTEXT("ServerTipCode", "Lua_SystemSetting_SaveCustomSchemeSuccess", "保存预设成功")
ServerTipCode.LayoutNameTable = {
    ["BaseLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BaseLayout", "默认的基础布局"),
    ["VehicleLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_VehicleLayout", "默认的载具布局"),
    ["SOLLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_SOLLayout", "危险行动"),
    ["RaidLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_RaidLayout", "合作行动"),
    ["ClassLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ClassLayout", "全面战场经典"),
    ["BattleLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_BattleLayout", "全面战场攻防"),
    ["ButtonStyle"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_ButtonStyle", "按键控制"),
    ["GliderStyle"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_GliderStyle", "滑杆控制"),
    ["JoyStickStyle"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_JoyStickStyle", "摇杆控制"),
    ["TankLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_TankLayout", "战斗载具-坦克操作"),
    ["HelicopterLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_HelicopterLayout", "战斗载具-直升机操作"),
    ["WeaponVehicleButtonLayout"] = NSLOCTEXT("SystemSettingModule", "Lua_SystemSetting_WeaponVehicleButtonLayout", "战斗载具-四键操作"),
}
--------------------------------------------------------------------------
--- RoomServer Tip
--------------------------------------------------------------------------
ServerTipCode.RoomWrongPwd =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomWrongPwd", "密码错误")
ServerTipCode.RoomInvalidPwd =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomInvalidPwd", "无效密码")
ServerTipCode.RoomNotMapInfo =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomNotMapInfo", "Mapconfig中未找到地图id为【%s】的地图信息")

ServerTipCode.RoomNotExist =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomNotExist", "房间不存在")
ServerTipCode.RoomJoinWaitTime =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomJoinWaitTime", "暂不可加入房间")
ServerTipCode.RoomIsEnough =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomIsEnough", "房间已经满员,无法加入")
ServerTipCode.RoomNotReady =
    NSLOCTEXT("ServerTipCode", "Lua_Room_RoomNotReady", "当前有玩家未准备")

ServerTipCode.OthersRefuseToJoinRoom = NSLOCTEXT("ServerTipCode", "Lua_Room_OthersRefuseToJoinRoom", "对方拒绝加入你的房间")
ServerTipCode.YouAreOutRoom = NSLOCTEXT("ServerTipCode", "Lua_Room_YouAreOutRoom", "你被踢出房间")
ServerTipCode.SomeoneOutRoom = NSLOCTEXT("ServerTipCode", "Lua_Room_SomeoneOutRoom", "有玩家被请出房间")
ServerTipCode.OutRoom = NSLOCTEXT("ServerTipCode", "Lua_Room_OutRoom", "被请出房间")
ServerTipCode.EditFail = NSLOCTEXT("ServerTipCode", "Lua_Room_EditFail", "修改失败")
ServerTipCode.EditTeamNameFailed = NSLOCTEXT("ServerTipCode", "Lua_Room_EditTeamNameFailed", "修改队伍名称失败")

--------------------------------------------------------------------------
--- SocialServer Tip
--------------------------------------------------------------------------
ServerTipCode.RoomNotIdle =
    NSLOCTEXT("ServerTipCode", "Lua_Social_RoomNotIdle", "房间已开赛")
ServerTipCode.RoomBeginWaitTips =NSLOCTEXT("ServerTipCode", "Lua_Room__RoomBeginWaitTips", "房间开赛, 等待入局")
--------------------------------------------------------------------------
--- FrontEndChatServer Tip
--------------------------------------------------------------------------
ServerTipCode.ChatForbidMsg = NSLOCTEXT("ServerTipCode", "Lua_Chat_ChatForbidMsg", "您已被禁止在聊天频道发言，解封时间为：%s")
ServerTipCode.ChatForbidVoice = NSLOCTEXT("ServerTipCode", "Lua_Chat_ChatForbidVoice", "您已被禁止在聊天频道发语音，解封时间为：%s")
ServerTipCode.ChatForbidStranger = NSLOCTEXT("ServerTipCode", "Lua_Chat_ChatForbidStranger", "对方禁止了陌生人私聊")
ServerTipCode.AppointSuccess        = NSLOCTEXT("ServerTipCode", "Lua_Chat_AppointSuccess", "已预约")
ServerTipCode.AppointFailed         = NSLOCTEXT("ServerTipCode", "Lua_Chat_AppointFailed", "预约失败")
--------------------------------------------------------------------------
--- 好友Server Tip
--------------------------------------------------------------------------

ServerTipCode.FriendAddApplySuccess = NSLOCTEXT("ServerTipCode", "Lua_Friend_AddApplySuccess", "发送好友请求成功")
ServerTipCode.FriendAddApplyFail = NSLOCTEXT("ServerTipCode", "Lua_Friend_AddApplyFail", "发送好友请求失败")
ServerTipCode.FriendAddApplyFailByBlack = NSLOCTEXT("ServerTipCode", "Lua_Friend_AddApplyFail_Black", "已被对方拉黑，添加好友失败")
ServerTipCode.FriendDel = NSLOCTEXT("ServerTipCode", "Lua_Friend_Del", "好友已删除")
ServerTipCode.FriendAdd = NSLOCTEXT("ServerTipCode", "Lua_Friend_Add", "添加好友成功")
ServerTipCode.FriendRefuse = NSLOCTEXT("ServerTipCode", "Lua_Friend_Refuse", "拒绝好友申请")
ServerTipCode.FriendAddBlackList = NSLOCTEXT("ServerTipCode", "Lua_Friend_Black_List", "已把该玩家拉入黑名单")
ServerTipCode.FriendRoomQuit = NSLOCTEXT("ServerTipCode", "Lua_Friend_RoomQuit", "已退出房间")
ServerTipCode.ReceiveFriendGift = NSLOCTEXT("ServerTipCode", "Lua_Receive_Friend_Gift", "收到好友礼物")
ServerTipCode.GiveFriendGift = NSLOCTEXT("ServerTipCode", "Lua_Give_Friend_gift", "已送好友礼物")
ServerTipCode.FriendAddAllSuccess = NSLOCTEXT("ServerTipCode", "Lua_Friend_AddAllSuccess", "已向房间内所有玩家发送好友申请")
ServerTipCode.AgreeAllApply = NSLOCTEXT("ServerTipCode", "Lua_Agree_All_Apply", "已同意所有好友申请")
ServerTipCode.RefuseAllApply = NSLOCTEXT("ServerTipCode", "Lua_Refuse_All_Apply", "已忽略所有好友申请")
ServerTipCode.FriendNumFull = NSLOCTEXT("ServerTipCode", "Lua_Friend_Num_Full", "好友数量已满")
ServerTipCode.FriendCallSvrFail = NSLOCTEXT("ServerTipCode", "Lua_Friend_Call_Svr_Fail", "调用服务失败")
ServerTipCode.FriendRepeatedApply = NSLOCTEXT("ServerTipCode", "Lua_Friend_Repeate_Apply", "重复申请")
ServerTipCode.AddFriendFail = NSLOCTEXT("ServerTipCode", "Lua_Friend_AddFriendFail", "发送好友请求失败")
ServerTipCode.RemoveBlackListSuccess = NSLOCTEXT("ServerTipCode", "Lua_Friend_RemoveBlackListSuccess", "解除屏蔽成功")
ServerTipCode.PraisSuccess = NSLOCTEXT("ServerTipCode", "Lua_Refuse_PraisSuccess", "点赞成功")
ServerTipCode.TipsSuccess = NSLOCTEXT("ServerTipCode", "Lua_Friend_TipsSuccess", "修改备注成功")

--------------------------------------------------------------------------
--- TeamServer Tip
--------------------------------------------------------------------------
ServerTipCode.TeamIngoreReq = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamIngoreReq", "%s忽视了你的请求")
ServerTipCode.TeamRefuse = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamRefuse", "%s暂无法响应你的邀请")
ServerTipCode.TeamApplyRefuse = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamApplyRefuse", "%s暂无法响应你的申请")
ServerTipCode.TeamMemJoin = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamMemJoin", "%s加入了队伍")
ServerTipCode.TeamMemExit = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamMemExit", "%s离开了队伍")
ServerTipCode.TeamSelfJoin = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamSelfJoin", "你加入了队伍")
ServerTipCode.TeamSelfExit = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamSelfExit", "你离开了队伍")
ServerTipCode.TeamOwnerDismiss = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamOwnerDismiss", "房主解散了队伍")
ServerTipCode.TeamDisband = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamDisband", "%s离开了队伍，队伍已解散")
ServerTipCode.TeamChangeCaptain = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamChangeCaptain", "%s离开了队伍，队长转交给%s")
ServerTipCode.TeamSwitchCaptain = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamSwitchCaptain", "%s成为了队长")
ServerTipCode.TeamSendInviteSuccess = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamSendInviteSuccess", "成功发送组队邀请")
ServerTipCode.TeamSendApplySuccess = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamSendApplySuccess", "成功发送入队申请")
ServerTipCode.RemoveFromTeam = NSLOCTEXT("ServerTipCode", "Lua_Team_RemoveFromTeam", "你已被移出队伍")
ServerTipCode.TeamMateIsKicked = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamMateIsKicked", "%s已被移出队伍")
ServerTipCode.RefuseJoinPrepare = NSLOCTEXT("ServerTipCode", "Lua_Team_RefuseJoinPrepare", "拒绝加入备战")
ServerTipCode.TeamMatchingRefuseLeave = NSLOCTEXT("ServerTipCode", "Lua_Team_MatchingRefuseLeave", "正在匹配中，无法请队友离开队伍")
ServerTipCode.TeamScavInCD = NSLOCTEXT("ServerTipCode", "Lua_Team_ScavInCD", "自己或队友的渗透者玩法还在冷却中, 无法开赛!")
ServerTipCode.TeamCantSwitchMap = NSLOCTEXT("ServerTipCode", "Lua_Team_CantSwitchMap", "队友数量过多，无法切换地图!")
ServerTipCode.TeamTeammateNotReady = NSLOCTEXT("ServerTipCode", "Lua_Team_TeammateNotReady", "队友未准备,不能开始")
ServerTipCode.TeamMatchTimeOut = NSLOCTEXT("ServerTipCode", "Lua_Team_MatchTimeOut", "匹配达到保底超时，未收到超时协议。")
ServerTipCode.TeamMatchIDIllegal = NSLOCTEXT("ServerTipCode", "Lua_Team_MatchIDIllegal", "MatchID(%s)非法!")
ServerTipCode.InviteYouToStart = NSLOCTEXT("ServerTipCode", "Lua_Team_InviteYouToStart", "邀请你开赛")
ServerTipCode.NetInvalidOutTime = NSLOCTEXT("ServerTipCode", "Lua_Team_NetInvalidOutTime", "网络异常，组队超时")
ServerTipCode.DesinerClosed = NSLOCTEXT("ServerTipCode", "Lua_Team_DesinerClosed", "策划大大不给玩了")
ServerTipCode.TeamStartMatchFailed = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamStartMatchFailed", "组队开赛失败")
ServerTipCode.ReqTimeOut = NSLOCTEXT("ServerTipCode", "Lua_Team_ReqTimeOut", "请求超时")
ServerTipCode.AlreadyInTeam = NSLOCTEXT("ServerTipCode", "Lua_Team_AlreadyInTeam", "已经在一个队伍中了")
ServerTipCode.WaitForServerResponse = NSLOCTEXT("ServerTipCode", "Lua_Team_WaitForServerResponse", "等待服务器响应")
ServerTipCode.TeamCantExitOnMatchSuccess = NSLOCTEXT("ServerTipCode", "Lua_Team_TeamCantExitOnMatchSuccess", "对局匹配成功，无法退出队伍")
ServerTipCode.MatchGateRankedMatchNotUnlock = NSLOCTEXT("ServerTipCode", "Lua_Team_MatchGateRankedMatchNotUnlock", "玩家排位赛未解锁")
ServerTipCode.MatchGateScoreMatchNotUnlock = NSLOCTEXT("ServerTipCode", "Lua_Team_MatchGateScoreMatchNotUnlock", "玩家晋升之路未解锁")
ServerTipCode.ChangeBHDIsOpenQuickJoinModeSucceed = NSLOCTEXT("ServerTipCode", "Lua_Team_ChangeBHDIsOpenQuickJoinModeSucceed", "修改队伍快速匹配模式成功")
ServerTipCode.ChangeBHDIsOpenQuickJoinModeFailed = NSLOCTEXT("ServerTipCode", "Lua_Team_ChangeBHDIsOpenQuickJoinModeFailed", "修改队伍快速匹配模式失败")
ServerTipCode.CanNotParticipateRank = NSLOCTEXT("ServerTipCode", "Lua_Team_CanNotParticipateRank", "{firstName}{secondName}{thirdName}{suffixSpace}信誉等级过低，禁止参与排位赛和晋升之路")
ServerTipCode.CanNotParticipateTeamGame = NSLOCTEXT("ServerTipCode", "Lua_Team_CanNotParticipateTeamGame", "{firstName}{secondName}{thirdName}{suffixSpace}信誉等级过低，禁止组队游戏")
ServerTipCode.CannotTeamUpDueToNetworkMismatch = NSLOCTEXT("ServerTipCode", "Lua_Team_CannotTeamUpDueToNetworkMismatch", "双方跨网络设置不一致，无法与该玩家组队")

--------------------------------------------------------------------------
--- 英雄Server Tip
--------------------------------------------------------------------------
ServerTipCode.HeroChangedSuccessfully = NSLOCTEXT("ServerTipCode", "Lua_Hero_ChangedSuccessfully", "切换成功")
ServerTipCode.HeroChangedFailed = NSLOCTEXT("ServerTipCode", "Lua_Hero_ChangedFailed", "切换失败，维持原有配置")
ServerTipCode.HeroChangedListNil = NSLOCTEXT("ServerTipCode", "Lua_Hero_ChangedListNil", "当前英雄id为0，切换失败，请检查当前兵种下是否存在已解锁英雄")
ServerTipCode.HeroAccessoryChangedFailed = NSLOCTEXT("ServerTipCode", "Lua_Hero_AccessoryChangedFailed", "当前道具id为0，切换失败，请检查当前干员下是否存在已解锁道具")
ServerTipCode.HeroFetchInfoFailed = NSLOCTEXT("ServerTipCode", "Lua_Hero_FetchInfoFailed", "拉取干员协议失败")

--------------------------------------------------------------------------
--- 招募Server Tip
--------------------------------------------------------------------------
ServerTipCode.RecruitCreateSuccess = NSLOCTEXT("ServerTipCode", "Lua_Recruit_CreateSuccess", "创建招募信息成功")
--------------------------------------------------------------------------
--- 结算Server Tip
--------------------------------------------------------------------------
ServerTipCode.UnProtected = NSLOCTEXT("ServerTipCode", "Lua_Settlement_UnProtected", "无防护")
ServerTipCode.GetSettlementInfoFailed = NSLOCTEXT("ServerTipCode", "Lua_Settlement_GetSettlementInfoFailed", "获取结算失败")
ServerTipCode.AccidentalDeath = NSLOCTEXT("ServerTipCode", "Lua_Settlement_AccidentalDeath", "意外淘汰")
ServerTipCode.Boss = NSLOCTEXT("ServerTipCode", "Lua_Settlement_Boss", "首领：%s")

--------------------------------------------------------------------------
--- Season Server Tip
--------------------------------------------------------------------------
ServerTipCode.GetSeasonExpFailed = NSLOCTEXT("ServerTipCode", "Lua_Season_GetSeasonExpFailed", "获取战区经验失败")

--------------------------------------------------------------------------
--- MatchServer Tip
--------------------------------------------------------------------------
ServerTipCode.MatchLocalDSDebug = NSLOCTEXT("ServerTipCode", "Lua_Match_LocalDSDebug", "本地DS调试")
ServerTipCode.MatchBeginFailed = NSLOCTEXT("ServerTipCode", "Lua_Match_MatchBeginFailed", "房间开赛失败")
ServerTipCode.MatchFailed = NSLOCTEXT("ServerTipCode", "Lua_Match_MatchFailed", "开赛失败")
ServerTipCode.JoinMatchFailed = NSLOCTEXT("ServerTipCode", "Lua_Match_JoinMatchFailed", "入局失败")
ServerTipCode.MatchNodeNotExist = NSLOCTEXT("ServerTipCode", "Lua_Match_MatchNodeNotExist", "MatchNodeNotExist")
ServerTipCode.MatchPlayerNotInMatching = NSLOCTEXT("ServerTipCode", "Lua_Match_MatchPlayerNotInMatching", "MatchPlayerNotInMatching")
ServerTipCode.MatchJoinTimeOut = NSLOCTEXT("ServerTipCode", "Lua_Match_MatchJoinTimeOut", "入局超时")
ServerTipCode.MatchNetworkFailure = NSLOCTEXT("ServerTipCode", "Lua_Match_MatchNetworkFailure", "网络错误")

--------------------------------------------------------------------------
--- SafeHouseServer Tip
--------------------------------------------------------------------------
ServerTipCode.SafeHouseProductionStop = NSLOCTEXT("ServerTipCode", "Lua_SafeHouse_ProductionStop", "生产已终止")
ServerTipCode.SafeHouseUpdateStop = NSLOCTEXT("ServerTipCode", "Lua_SafeHouse_UpdateStop", "升级已终止")

--------------------------------------------------------------------------
--- SystemSettingServer Tip
--------------------------------------------------------------------------
ServerTipCode.SystemSettingRetryShareCode = NSLOCTEXT("ServerTipCode", "Lua_SystemSetting_RetryShareCode", "分享码过期，请检查后重试")
ServerTipCode.SystemSettingSaveCloudSucc = NSLOCTEXT("ServerTipCode", "Lua_SystemSetting_SaveCloudSucc", "上传云端成功")

--------------------------------------------------------------------------
--- ReportServer Tip
--------------------------------------------------------------------------
ServerTipCode.ReportSuccess = NSLOCTEXT("ServerTipCode", "Lua_Report_Success", "举报成功")
ServerTipCode.ReportFail = NSLOCTEXT("ServerTipCode", "Lua_Report_Fail", "举报失败")

--------------------------------------------------------------------------
--- WeaponAssemblyServer Tip
--------------------------------------------------------------------------
ServerTipCode.WeaponAssemblyCantInstallPart = NSLOCTEXT("ServerTipCode", "Lua_WeaponAssembly_CantInstallPart", "此配件不可安装在该装备上")
ServerTipCode.WeaponAssemblyUnEnchangeTip = NSLOCTEXT("ServerTipCode", "Lua_WeaponAssembly_UnEnchangeTip", "仓库容量不足，无法替换配件")

--- GunsmithServer Tip
--------------------------------------------------------------------------
ServerTipCode.GunsmithCantInstallPart = NSLOCTEXT("ServerTipCode", "Lua_Gunsmith_CantInstallPart", "此配件不可安装在该装备上")
ServerTipCode.GunsmithUnEnchangeTip = NSLOCTEXT("ServerTipCode", "Lua_Gunsmith_UnEnchangeTip", "仓库容量不足，无法替换配件")
ServerTipCode.GunsmithUnlockPurchasSuccessTip = NSLOCTEXT("ServerTipCode", "Lua_Gunsmith_GunsmithUnlockPurchasSuccessTip", "购买成功")

--------------------------------------------------------------------------
--- ShopStationServer Tip
--------------------------------------------------------------------------
ServerTipCode.ShopStation_ReachLimit = NSLOCTEXT("ServerTipCode", "Lua_ShopStation_ReachLimit", "已达到库存上限")
ServerTipCode.ShopStation_NoEnoughSpace = NSLOCTEXT("ServerTipCode", "Lua_ShopStation_NoEnoughSpace", "背包空间不足，请清理后再试")
--------------------------------------------------------------------------
--- RoleinfoServer Tip
--------------------------------------------------------------------------
ServerTipCode.SaveAppearanceSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SaveAppearanceSuccess", "保存外观成功")

--- ModuleUnlockServer Tip
ServerTipCode.NotOpen = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_NotOpen", "暂未开放,敬请期待")
ServerTipCode.NotUnlocked = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_NotUnlocked", "未解锁")


--- StoreServer Tip
ServerTipCode.StoreBoxNotFound = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_StoreBoxNotFound", "未查到奖池信息")

--- LootingServer Tip
ServerTipCode.LootBadNetwork = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_LootBadNetwork", "检测到网络环境不稳定，建议重新打开容器搜索")


ServerTipCode.PendantRenameSuccess = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PendantRenameSuccess", "挂饰改名成功")

ServerTipCode.PendantRenameFailed = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PendantRenameFailed", "挂饰改名失败")

--- Punish Tip
ServerTipCode.PunishDataNoConfig = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_PunishDataNoConfig", "玩家处罚封禁信息未配置：%d")
--------------------------------------------------------------------------
--- RecoveryServer Tip
--------------------------------------------------------------------------
ServerTipCode.RecoveryMessage = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_RecoveryMessage", "由于存在追缴状态，获取的哈夫币%d已用于追缴系统还款")
ServerTipCode.SellSuccessRecovery = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SellSuccessRecovery", "出售成功！获得 %s%d, 其中 %s%d已用于追缴系统还款")
ServerTipCode.SellSuccessRecoveryAll = NSLOCTEXT("ServerTipCode", "Lua_ServerTipCode_SellSuccessRecoveryAll", "出售成功！获得 %s%d, 其中 %s%d已用于追缴系统还款，已缴纳完所有罚款")
ServerTipCode.SubmitSuccessAll = NSLOCTEXT("ServerTipCode","Lua_ServerTipCode_SubmitSuccessAll", "已缴纳全部罚款")
ServerTipCode.SubmitSuccess = NSLOCTEXT("ServerTipCode","Lua_ServerTipCode_SubmitSuccess", "已缴纳成功，剩余 %s%d")
ServerTipCode.SettlementRecoveryAll = NSLOCTEXT("ServerTipCode","Lua_ServerTipCode_SettlementRecoveryAll", "获得行动报酬 %s%d，其中 %s%d已用于追缴系统还款，已缴纳完所有罚款")
ServerTipCode.SettlementRecovery = NSLOCTEXT("ServerTipCode","Lua_ServerTipCode_SettlementRecovery", "获得行动报酬 %s%d，其中 %s%d已用于追缴系统还款")