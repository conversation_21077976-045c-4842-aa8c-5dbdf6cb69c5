----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------

local function log(...)
    loginfo("[SafeBoxAndKeyChainMain]", ...)
end

local SafeBoxAndKeyChainMain = class("SafeBoxAndKeyChainMain", LuaUIBaseView)

local InventoryConfig        = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local ItemOperaTool          = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemHelperTool         = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local ItemConfigTool         = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local InventoryLogic         = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local WidgetUtil             = require "DFM.YxFramework.Util.WidgetUtil"
local InventoryNavManager    = require "DFM.Business.Module.InventoryModule.Logic.InventoryNavManager"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local EDepartmentType        = Module.Shop.Config.EDepartmentType
local CommonWidgetConfig     = Module.CommonWidget.Config
local EComp                  = CommonWidgetConfig.EIVWarehouseTempComponent
local EIVSlotPos             = CommonWidgetConfig.EIVSlotPos
local EIVCompOrder           = CommonWidgetConfig.EIVCompOrder

function SafeBoxAndKeyChainMain:Ctor()
    self._wtItemDetailView = self:Wnd("wtItemDetailView", UIWidgetBase)
    self._wtSafeBoxAndKeyChainItem = self:Wnd("wtWeaponSkinScrollView", UIWidgetBase)
    self._wtSafeBpxAndKeyChainList = UIUtil.WndWaterfallScrollBox(self, "wtWeaponSkinScrollView", self._OnGetItemCount, self._OnProcessItemWidget)

    self._wtQuestionHintBox = self:Wnd("wtQuestionHintBox", UIWidgetBase)
    self._wtQuestionHintText = self:Wnd("wtQuestionHintText", UITextBlock)
    self._wtIconTips = self:Wnd("DFImage_86", UIImage)
    self._wtQuestionHintBox:Visible()
    self._wtQuestionHintText:Visible()
    self._wtIconTips:Collapsed()
    self._wtItemId = self:Wnd("wtItemID", UITextBlock)

    -- 按钮
    self._wtApplyBtn = self:Wnd("wtApplyBtn", DFCommonButtonOnly)
    self._wtDetailBtn = self:Wnd("wtDetailBtn_PC", DFCommonButtonOnly)

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtApplyBtn:SetDisplayInputAction("CommonBar_Confirm", true, nil, true)
        self._wtDetailBtn:SetDisplayInputAction("Common_ButtonLeft_Gamepad", true, nil, true)
    end
    --- END MODIFICATION


    self._wtBgIcon = self:Wnd("Image_Bg", DFCDNImage)
    self._wtItemIcon = self:Wnd("Image_Icon", DFCDNImage)
    self._wtTransformWidget = self:Wnd("DFScaleBox_0", UIWidgetBase)

    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})

    self._wtItemWidgetTable = {}
end

-----------------------------------------------------------------------
--region Life function

function SafeBoxAndKeyChainMain:OnInitExtraData(type, item)
    self._type = type
    self._alreadySelectedItem = item
    self._bUsingItem = item
end

function SafeBoxAndKeyChainMain:OnOpen()
    self._isCustomizePanel = InventoryConfig.ESafeBoxType.Normal
end

function SafeBoxAndKeyChainMain:OnShowBegin()
    local topBarTitile = ""
    if self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal then
        topBarTitile = InventoryConfig.SlotNameMapping[ESlotType.SafeBox]
        self:_GetSafeBoxAndKeyChainInfo()
    else
        topBarTitile = InventoryConfig.Loc.CustomizeText
        self:_GotoCustomizeFunc()
    end
    if not IsHD() then
        Module.CommonBar:RegStackUITopBarTitle(self, topBarTitile)
    end
    self:_EnableGamePadFeature()
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
end

function SafeBoxAndKeyChainMain:OnShow()
    self:AddLuaEvent(Server.CollectionServer.Events.evtCollectionUsePropSucess, self.RefreshWidget, self)
    self:AddLuaEvent(Server.CollectionServer.Events.evtEquipSafeBoxSkin, self.RefreshSkinWidget, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self.OnItemMove, self)
    if self._navGroup then
        -- WidgetUtil.SetUserFocusToWidget(self._selectedCell, true)
        InventoryNavManager.FocusWithTimer(self._selectedCell)
    end
    self:_RefreshInfoPanel(self._alreadySelectedItem)
end

function SafeBoxAndKeyChainMain:OnHideBegin()
    self:_DisableGamePadFeature()
end

function SafeBoxAndKeyChainMain:OnHide()
    self:RemoveAllLuaEvent()
    self:Reset()
end

function SafeBoxAndKeyChainMain:OnClose()
    self._wtItemWidgetTable = {}
end

function SafeBoxAndKeyChainMain:Reset()
    self._selectedCell = nil
    self._selectedPos = nil
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Private function

function SafeBoxAndKeyChainMain:_OnGetItemCount()
    return self._groupItems and #self._groupItems or 0
end

function SafeBoxAndKeyChainMain:_OnProcessItemWidget(position, itemWidget)
    self._wtItemWidgetTable[position] = itemWidget
    itemWidget:ChangeDefaultMode(CommonWidgetConfig.EIVItemViewMode.ListItemView)
    itemWidget:InitItem(self._groupItems[position])
    itemWidget:SetSelected(self._groupItems[position], false)
    local itemNameComp = itemWidget:FindOrAdd(EComp.TopLeftIconText, UIName2ID.IVTextQualityComponent, EIVSlotPos.TopLeft)
    itemNameComp:RefreshComponent()
    -- 网吧特权需要增加角标
    local equipmentFeature = self._groupItems[position] and self._groupItems[position]:GetFeature(EFeatureType.Equipment)
    if equipmentFeature and equipmentFeature:IsNetBarSafeBox() then
        local rightComponent = itemWidget:FindOrAdd(EComp.BottomRightIconText, UIName2ID.IVTextIconComponent, EIVSlotPos.BottomRight)
        rightComponent:ShowIconOnly("PaperSprite'/Game/UI/UIAtlas/CommonHall/BakedSprite/CommonHall_Icon_Cybercafe.CommonHall_Icon_Cybercafe'")
        rightComponent:EnableComponent(true)
    else
        itemWidget:EnableComponent(EComp.BottomRightIconText, false)
    end
    if self._groupItems[position].id == self._bUsingItem.id or self:_IsUsingSafeBoxSkin(self._groupItems[position].id) then
        local topRightComponent = itemWidget:FindOrAdd(EComp.TopRightIconText, UIName2ID.IVUsingComponent, EIVSlotPos.TopRight)
        if topRightComponent then
            topRightComponent:EnableComponent(true)
        end
    else
        itemWidget:EnableComponent(EComp.TopRightIconText, false)
    end

    local fClickCb = CreateCallBack(self._SafeBoxItemClicked, self, itemWidget, position)
    itemWidget:BindCustomOnClicked(fClickCb)
    if self._alreadySelectedItem.id % 10 == itemWidget.item.id % 10 then
        self:_SafeBoxItemClicked(itemWidget, position)
        self._selectedCell = itemWidget
    end

    if self._navGroup then
        self._navGroup:AddNavWidgetToArray(itemWidget)
    end
end

function SafeBoxAndKeyChainMain:_SafeBoxItemClicked(itemWidget, position)
    if self._selectedPos ~= position then
        if self._selectedCell then
            self._selectedCell:SetSelected(self._alreadySelectedItem, false)
        end
        itemWidget:SetSelected(itemWidget.item, true)
        self._selectedCell = itemWidget
        self._selectedPos = position
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIItemSelect)
        self._alreadySelectedItem = itemWidget.item
        self:_RefreshInfoPanel(itemWidget.item)
    end
end

-- 刷新面板
function SafeBoxAndKeyChainMain:_RefreshInfoPanel(item)
    self:InitItemDetailPanel(item)
    if not VersionUtil.IsShipping() then
        self._wtItemId:Visible()
        self._wtItemId:SetText(string.format("%s", item.id))
    else
        self._wtItemId:Collapsed()
    end

    if self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal then
        self:_RefreshBtnPanel(item)
    else
        self:_RefreshSkinPanel(item)
    end
    self:_SetBackGroundImg(true, item.id)
    InventoryNavManager.FocusWithTimer(self._selectedCell)
end

-- 刷新安全箱面板
function SafeBoxAndKeyChainMain:_RefreshBtnPanel(item)
    local itemFeature = item:GetFeature(EFeatureType.Equipment)

    local cardsTable = ItemConfigTool.FindPermissionActivateCard(item.id)
    local activateCardTable = {}
    -- 获取体验卡道具
    for _, cardID in ipairs(cardsTable) do
        local activateCard = Server.CollectionServer:GetCollectionItemById(cardID, nil, true)
        table.insert(activateCardTable, activateCard)
    end

    self.activateCard = activateCardTable[1]

    -- 只有顶级安全箱才有自定义功能
    local itemIdLast = tonumber(self._alreadySelectedItem.id) % 10
    if itemIdLast == 4 then
        self._wtDetailBtn:Visible()
        self._wtDetailBtn:RemoveEvent("OnClicked")
        self._wtDetailBtn:RemoveEvent("OnDeClicked")
        self._wtDetailBtn:SetMainTitle(InventoryConfig.Loc.CustomizeText)
        -- 体验服需要屏蔽
        if BUILD_REGION_CN_EXPER then
            self._wtDetailBtn:SetIsEnabledStyle(false)
            local function ShowTips()
                Module.CommonTips:ShowSimpleTip(Module.BattlefieldEntry.Config.Loc.CommingSoon)
            end
            self._wtDetailBtn:Event("OnDeClicked", ShowTips, self)
        else
            self._wtDetailBtn:SetIsEnabledStyle(true)
            self._wtDetailBtn:Event("OnClicked", self._GotoCustomizeFunc, self)
        end
    else
        self._wtDetailBtn:Collapsed()
    end

    self._wtApplyBtn:RemoveEvent("OnClicked")
    self._wtQuestionHintText:Visible()
    if itemFeature:PermissionCanUse() then
        -- 免费和未过期
        local bUsing = self._bUsingItem.id == self._alreadySelectedItem.id

        local timeStr
        if itemFeature:PermissionItemIsFree() then
            timeStr = InventoryConfig.Loc.PermanentText
        else
            if itemFeature:IsNetBarSafeBox() then
                timeStr = InventoryConfig.Loc.NetBarSafeBoxText
            else
                timeStr = InventoryLogic.GetRemainTime(itemFeature._expireTimes)
            end
        end
        local timeStr = string.format(InventoryConfig.Loc.UseTimeText, timeStr)
        self._wtQuestionHintText:SetText(timeStr)
        self._wtApplyBtn:SetBtnEnable(true and not bUsing)
        self._wtApplyBtn:SetMainTitle(bUsing and InventoryConfig.Loc.ItemUsedText or InventoryConfig.Loc.UseContainerText)
        self._wtApplyBtn:Event("OnClicked", self._OnUseButtonClick, self)
    elseif not self.activateCard and not itemFeature:PermissionCanUse() then
        -- 无权限卡且无权限
        self._wtApplyBtn:SetBtnEnable(true)
        self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.GotoGetText)
        self._wtApplyBtn:Event("OnClicked", self._OnRenewalBtnClick, self)

        -- 高级安全箱和顶级安全箱需要显示获取途径
        local getWayTxt
        if Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleBattlePass) then
            -- local curSeason = Server.BattlePassServer:GetSeasonNum()
            local curSeason = Server.RankingServer:GetCurSerial()
            if itemIdLast == 2 or itemIdLast == 3 then
                getWayTxt = string.format(InventoryConfig.Loc.GetSafeBoxWayTxt_2, curSeason)
            end
        else
            getWayTxt = Module.Inventory.Config.Loc.BPLockTxt
        end
        if itemIdLast == 4 then
            getWayTxt = string.format(InventoryConfig.Loc.GetSafeBoxWayTxt_1, curSeason and curSeason or Server.RankingServer:GetCurSerial())
        end
        self._wtQuestionHintText:SetText(getWayTxt)
    else
        local activateCardNum = 0
        for _, card in ipairs(activateCardTable) do
            activateCardNum = activateCardNum + card.num
        end
        local activateCardTxt = string.format(InventoryConfig.Loc.ActivateCardNum, activateCardNum)
        self._wtQuestionHintText:SetText(activateCardTxt)

        self._wtApplyBtn:SetBtnEnable(true)
        self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.ActivatePermissions)
        self._wtApplyBtn:Event("OnClicked", self._OnUseActivateCardFunc, self)
    end
end

-- 刷新安全箱皮肤面板
function SafeBoxAndKeyChainMain:_RefreshSkinPanel(item)
    local equipedSkinId = Server.CollectionServer.safeboxInfoEquiped
    self._wtQuestionHintText:Collapsed()
    self._wtApplyBtn:RemoveEvent("OnClicked")

    local itemFeature = item:GetFeature(EFeatureType.Equipment)
    -- local isDefaultSkin = itemFeature and itemFeature:IsSafeBox()
    local isDefaultSkin = item.id == "33010010001"

    if (equipedSkinId and equipedSkinId == item.id) or (equipedSkinId == 0 and isDefaultSkin) then
        self._wtApplyBtn:SetBtnEnable(false)
        self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.ItemUsedText)
    else
        -- 判断当前安全箱外观是否已拥有, 默认外观允许安装
        if self:_IsHaveSafeBoxSkin(item.id) or isDefaultSkin then
            self._wtApplyBtn:SetBtnEnable(true)
            self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.UseContainerText)
            self._wtApplyBtn:Event("OnClicked", self.UseSafeBoxSkinBtn, self)
        else
            local safeboxSkinData = ItemHelperTool.FindSafeBoxSkinByID(item.id)
            if not safeboxSkinData then
                return
            end
            local curSeason = Server.BattlePassServer:GetSeasonID()
            local seasonNum = ItemHelperTool.GetSeasonBySeasonID(safeboxSkinData.SeasonID)
            local getWayTxt = string.format(InventoryConfig.Loc.GetSafeBoxWayTxt_1, seasonNum)
            self._wtQuestionHintText:SelfHitTestInvisible()
            self._wtQuestionHintText:SetText(getWayTxt)
            loginfo("SunBillowsTest-curSeason:", curSeason, " seasonNum:", seasonNum)
            if curSeason == safeboxSkinData.SeasonID then
                self._wtApplyBtn:SetBtnEnable(true)
                self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.GotoGetText)
                self._wtApplyBtn:Event("OnClicked", self._OnRenewalBtnClick, self)
            else
                self._wtApplyBtn:SetBtnEnable(false)
                self._wtApplyBtn:SetMainTitle(InventoryConfig.Loc.RepairText)
            end
        end
    end
    self._wtDetailBtn:SetMainTitle(InventoryConfig.Loc.DetailCheckTxt)
    self._wtDetailBtn:RemoveEvent("OnClicked")
    self._wtDetailBtn:Event("OnClicked", self._OnDetailBtnClick, self)
end

function SafeBoxAndKeyChainMain:_SetBackGroundImg(bShow, propId)
    Module.Collection:SetBackgroundImgByPropId({self._wtBgIcon, self._wtItemIcon}, bShow, Module.CDNIcon.Config.ECdnTagEnum.Collection, nil, propId, self._wtTransformWidget)

    --[[
    local gameItemRow = ItemConfigTool.GetItemConfigById(propId)
    local CDNImagePaths, imageTransforms
    if gameItemRow then
        CDNImagePaths = gameItemRow.Pictures
        imageTransforms = imageTransforms or gameItemRow.PictureTransforms
    end
    if bShow then
        local eCdnTagEnum = Module.CDNIcon.Config.ECdnTagEnum.SafeBox
        local bgCDNPathPrefix = self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal and "Resource/Texture/Collection/MS24/" or "Resource/Texture/Safebox/"
        -- local bgCDNPathPrefix = "Resource/Texture/Safebox/"
        local itemCDNPathPrefix = (self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal or tostring(propId) == "33010010001") and "Resource/Texture/Collection/MS24/" or "Resource/Texture/Safebox/"
        self._wtBgIcon:SetCDNImage(bgCDNPathPrefix..CDNImagePaths[1], false, eCdnTagEnum)
        self._wtBgIcon:SelfHitTestInvisible()

        self._wtItemIcon:SetCDNImage(itemCDNPathPrefix..CDNImagePaths[2], false, eCdnTagEnum)
        self._wtItemIcon:SelfHitTestInvisible()

        if imageTransforms ~= nil and #imageTransforms > 0 then
            if imageTransforms[1] ~= nil then
                if imageTransforms[1].Translation then
                    self._wtTransformWidget:SetRenderTranslation(imageTransforms[1].Translation)
                end
                if imageTransforms[1].Angle then
                    self._wtTransformWidget:SetRenderTransformAngle(imageTransforms[1].Angle)
                end
            end
        else
            self._wtTransformWidget:SetRenderTranslation(FVector2D(0, 0))
            self._wtTransformWidget:SetRenderTransformAngle(0)
        end
    else
        self._wtBgIcon:Collapsed()
        self._wtItemIcon:Collapsed()
        self._wtTransformWidget:Collapsed()
    end
    ]]--
end

-- 使用按钮
function SafeBoxAndKeyChainMain:_OnUseButtonClick()
    log("_OnUseButtonClick")
    ItemOperaTool.DoPlacePermissionItem(self._alreadySelectedItem, self._type)
end

-- 前往获取按钮
function SafeBoxAndKeyChainMain:_OnRenewalBtnClick()
    log("_OnUseButtonClick")
    -- 根据不同的扩容箱判断前往获取按钮应该跳转到何处
    local equipmentFeature = self._alreadySelectedItem:GetFeature(EFeatureType.Equipment)
    local isSafeBox = equipmentFeature and equipmentFeature:IsSafeBox()
    local isKeyChain = equipmentFeature and equipmentFeature:IsKeyChain()
    local itemIdLast = tonumber(self._alreadySelectedItem.id) % 10
    if isKeyChain then
        if itemIdLast == 2 then
            -- 跳转至部门-任务
            if IsHD() then
                Module.Quest:Jump()
            else
                Module.Shop:OpenChooseDepartmentView(EDepartmentType.Quest)
            end
        elseif itemIdLast == 3 or itemIdLast == 4 then
            -- 跳转至部门-军需处
            Module.Shop:ShowChooseMerchantPanel()
        elseif itemIdLast == 5 then
            if Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleRankSOL) then
                Module.Ranking:ShowMainPanel()
            else
                Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.RankingSOLUnLockTxt)
            end
        end
    elseif isSafeBox then
        if itemIdLast == 4 then
            -- 部门-任务
            Module.Jump:JumpByID(Module.Quest.Config.QuestSeasonMainJumpID)
        else
            -- 通行证界面
            if Module.ModuleUnlock:IsModuleUnlock(SwitchModuleID.ModuleBattlePass) then
                Module.BattlePass:ShowBattleProcess(UIName2ID.BattlePassMain)
            else
                Module.CommonTips:ShowSimpleTip(Module.Inventory.Config.Loc.BPLockTxt)
            end
        end
    else
        -- 安全箱皮肤获取
        -- 部门-任务
        Module.Jump:JumpByID(Module.Quest.Config.QuestSeasonMainJumpID)
    end
    -- self:_OnCloseBtnClick()
end

-- 激活权限
function SafeBoxAndKeyChainMain:_OnUseActivateCardFunc()
    -- local function afterUseActivateCardFunc()
    --     self:RefreshWidget()
    -- end
    -- if self.activateCard then
    --     Server.CollectionServer:DoUseMultiItems({self.activateCard}, {1}, {}, {}, afterUseActivateCardFunc)
    -- end
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionActivationCard, nil, self, self.activateCard)
end

-- 前往自定义界面
function SafeBoxAndKeyChainMain:_GotoCustomizeFunc()
    -- 先将原本的groupItems进行替换
    self:_GetSafeBoxSkinInfo()
    local equipSafeBoxSkinID = Server.CollectionServer.safeboxInfoEquiped
    if equipSafeBoxSkinID and equipSafeBoxSkinID ~= 0 then
        for _, skinItem in ipairs(self._groupItems) do
            if skinItem.id == equipSafeBoxSkinID then
                self._alreadySelectedItem = skinItem
            end
        end
    else
        self._alreadySelectedItem = self._groupItems[1]
    end

    if not IsHD() then
        Module.CommonBar:ChangeBackBtnText(InventoryConfig.Loc.CustomizeText)
    end
    Module.CommonBar:BindBackHandler(self._OnTopBarBackBtnClicked, self)
    self._isCustomizePanel = InventoryConfig.ESafeBoxType.SafeBoxSkin
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    self:_RefreshInfoPanel(self._alreadySelectedItem)
    WidgetUtil.HideFreeAnalogCursor(false)
end

-- 查看详情界面
function SafeBoxAndKeyChainMain:_OnDetailBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxSkinDetailPage, nil, self, self._alreadySelectedItem)
end

-- 使用外观接口
function SafeBoxAndKeyChainMain:UseSafeBoxSkinBtn()
    local skinId = self._alreadySelectedItem.id
    if ItemHelperTool.FindSafeBoxSkinByID(skinId) then
        Server.CollectionServer:EquipSafeBoxSkin(tonumber(skinId))
    else
        Server.CollectionServer:EquipSafeBoxSkin(0)
    end
    self:_OnTopBarBackBtnClicked()
    Module.CommonTips:ShowSimpleTip(Module.Collection.Config.Loc.AppearanceApplied)
end

function SafeBoxAndKeyChainMain:_OnCloseBtnClick()
    Facade.UIManager:CloseUI(self)
end

function SafeBoxAndKeyChainMain:_AddNoPermissionInfo(ownGroup, allItemsGroup)
    -- 1.赛季道具优先
    -- 2.每种类型的道具只能存在一个
    table.sort(ownGroup, function (a, b)
        return a.id > b.id
    end)
    local actTable = {}
    for key, itemInfo in ipairs(ownGroup) do
        if allItemsGroup[tostring(itemInfo.id)] then
            allItemsGroup[tostring(itemInfo.id)] = nil
        end
        local subStr = itemInfo.id % 10
        if not actTable[subStr] then
            -- 如果道具还有权限则加入，如果没权限则找下一个有权限的,如果都没权限则默认没激活
            local equipFeature = itemInfo:GetFeature(EFeatureType.Equipment)
            if equipFeature and equipFeature:PermissionCanUse() then
                actTable[subStr] = itemInfo
            end
        end
    end

    for key, itemInfo in pairs(allItemsGroup) do
        local type = tonumber(itemInfo.VirtualItemID) % 10
        if itemInfo and not actTable[type] then
            local newItem = ItemBase:New(tonumber(itemInfo.VirtualItemID))
            actTable[type] = newItem
        end
    end

    local function fsortfunc(a, b)
        local id_a = a.id % 10
        local id_b = b.id % 10
        return id_a < id_b
    end
    table.sort(actTable, fsortfunc)
    return actTable
end

function SafeBoxAndKeyChainMain:_AddSafeBoxSkinInfo(allSkinGroup, safeboxSkinList)
    for _, skinInfo in ipairs(safeboxSkinList) do
        local skinItem = ItemBase:New(skinInfo)
        table.insert(self._safeboxSkinList, skinItem)
    end
end

function SafeBoxAndKeyChainMain:_GetSafeBoxAndKeyChainInfo()
    local ownGroup = Server.InventoryServer:GetSafeBoxItems()
    local allItemsGroup = ItemHelperTool.FindPermissonArray(1)
    self._groupItems = self:_AddNoPermissionInfo(ownGroup, allItemsGroup)
end

function SafeBoxAndKeyChainMain:_GetSafeBoxSkinInfo()
    self._groupItems = ItemHelperTool.FindSafeBoxSkinData()
    table.insert(self._groupItems, 1, ItemBase:New("33010010001"))
end

function SafeBoxAndKeyChainMain:_OnTopBarBackBtnClicked()
    Module.CommonBar:BindBackHandler(nil)
    self:_GetSafeBoxAndKeyChainInfo()
    self._alreadySelectedItem = self._groupItems[4]
    self._isCustomizePanel = InventoryConfig.ESafeBoxType.Normal
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    self:_RefreshInfoPanel(self._alreadySelectedItem)
    if not IsHD() then
        Module.CommonBar:ChangeBackBtnText(InventoryConfig.SlotNameMapping[ESlotType.SafeBox])
    end
    WidgetUtil.HideFreeAnalogCursor(false)
end

function SafeBoxAndKeyChainMain:_IsHaveSafeBoxSkin(itemID)
    local safeboxSkinList = Server.CollectionServer._propSafeBoxList
    for _, skinID in ipairs(safeboxSkinList) do
        if skinID == itemID then
            return true
        end
    end
end

--endregion
-----------------------------------------------------------------------

function SafeBoxAndKeyChainMain:OnItemMove(moveItemInfo)
    local newSlot = moveItemInfo and moveItemInfo.NewLoc and moveItemInfo.NewLoc.ItemSlot
    if newSlot and newSlot.SlotType == ESlotType.SafeBox then
        self._bUsingItem = moveItemInfo.item
        -- self._alreadySelectedItem = self._bUsingItem
        -- self:_RefreshInfoPanel(self._alreadySelectedItem)
        self:RefreshWidget()
    end
end

-- 重新刷新界面
function SafeBoxAndKeyChainMain:RefreshWidget()
    if self._isCustomizePanel == InventoryConfig.ESafeBoxType.Normal then
        self:_GetSafeBoxAndKeyChainInfo()
    else
        self:_GetSafeBoxSkinInfo()
    end
    self._selectedPos = nil
    self._wtSafeBpxAndKeyChainList:RefreshAllItems()
    -- self:_SafeBoxItemClicked(self._wtItemWidgetTable[self._selectedPos], self._selectedPos)
end

-- 使用安全箱外观之后刷新
function SafeBoxAndKeyChainMain:RefreshSkinWidget()
    self:_RefreshInfoPanel(self._alreadySelectedItem)
end

-- 初始化详情页界面
function SafeBoxAndKeyChainMain:InitItemDetailPanel(item)
    -- local defaultSkinItem
    -- if self._isCustomizePanel == InventoryConfig.ESafeBoxType.SafeBoxSkin then
    --     local equipFeature = item:GetFeature(EFeatureType.Equipment)
    --     if equipFeature and equipFeature:IsSafeBox() then
    --         defaultSkinItem = ItemBase:New("33010010001")
    --     end
    -- end
    -- item = defaultSkinItem or item
    self._wtItemDetailView:UpdateItem(item, true)
end

-----------------------------------------------------------------------
--region 手柄适配

function SafeBoxAndKeyChainMain:_EnableGamePadFeature()
    if not IsHD() then
        return
    end
    
    self:_RegisterNavGroup()
end

function SafeBoxAndKeyChainMain:_DisableGamePadFeature()
    if not IsHD() then
        return
    end

    self:_RemoveNavGroup()
end

function SafeBoxAndKeyChainMain:_RegisterNavGroup()
    if not IsHD() then
        return
    end

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtSafeBpxAndKeyChainList, self, "Hittest")
        self._navGroup:SetScrollRecipient(self._wtSafeBpxAndKeyChainList)
        self._navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
    end
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

    if not self._wtApplyBtnHandle then
        self._wtApplyBtnHandle = self:AddInputActionBinding("CommonBar_Confirm", EInputEvent.IE_Pressed, self._wtApplyBtn.ButtonClick, self._wtApplyBtn, EDisplayInputActionPriority.UI_Stack)
    end
    if not self._wtDetailBtnHandle then
        self._wtDetailBtnHandle = self:AddInputActionBinding("Common_ButtonLeft_Gamepad", EInputEvent.IE_Pressed, self._wtDetailBtn.ButtonClick, self._wtDetailBtn, EDisplayInputActionPriority.UI_Stack)
    end
end

function SafeBoxAndKeyChainMain:_RemoveNavGroup()
    if not IsHD() then
        return
    end

    WidgetUtil.RemoveNavigationGroup(self)
    self._navGroup = nil
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

    if self._wtApplyBtnHandle then
        self:RemoveInputActionBinding(self._wtApplyBtnHandle)
        self._wtApplyBtnHandle = nil
    end

    if self._wtDetailBtnHandle then
        self:RemoveInputActionBinding(self._wtDetailBtnHandle)
        self._wtDetailBtnHandle = nil
    end
end

-- 判断当前所使用的安全箱外观
function SafeBoxAndKeyChainMain:_IsUsingSafeBoxSkin(skinID)
    local curUsingSafeBoxID = Server.CollectionServer.safeboxInfoEquiped
    if curUsingSafeBoxID ~= 0 then
        return curUsingSafeBoxID == skinID
    else
        return tostring(skinID) == "33010010001"
    end
end

--endregion
-----------------------------------------------------------------------

return SafeBoxAndKeyChainMain