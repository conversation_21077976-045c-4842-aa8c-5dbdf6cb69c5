local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
local HeroProfessionDataTable = Facade.TableManager:GetTable("Hero/HeroProfessionData")
local FashionMallDataTable = Facade.TableManager:GetTable("Fashion/FashionMallData")
local ArmedForceDataTable = Facade.TableManager:GetTable("ArmedForceData")
local ExpertInfoDataTable = Facade.TableManager:GetTable("Skill/ExpertTalentData")
local HeroFashionDataTable = Facade.TableManager:GetTable("Hero/HeroFashionData")
local HeroWatchDataTable = Facade.TableManager:GetTable("HeroDFWatchData")
local HeroVODataTable = Facade.TableManager:GetTable("Hero/HeroVOData")
local HeroRecruitGoalDescTable = Facade.TableManager:GetTable("Hero/RecruitGoalDesc")
local RecrtuitTaskDataTable = Facade.TableManager:GetTable("Hero/RecruitTaskData")
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local HeroItemData = Facade.TableManager:GetTable("Hero/HeroItemData")
local LocalizeTool = require "DFM.StandaloneLua.BusinessTool.LocalizeTool"

-- local AbilityDataTable = Facade.TableManager:GetTable("AbilityData")
-- local CombatRoleAbilityDataTable = Facade.TableManager:GetTable("CombatRoleAbilityData")

---------------------------------------------------------------------------------
--- 此处放置Hero相关的工具类方法
---------------------------------------------------------------------------------
local HeroHelperTool = {}

function HeroHelperTool.IsHeroId(id)
    return HeroDataTable[id] ~= nil
end

function HeroHelperTool.GetHeroProto(heroId)
    return HeroDataTable[heroId] or {}
end

function HeroHelperTool.GetHeroName(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["Name"] or ""
end

function HeroHelperTool.GetHeroTitle(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["Title"] or ""
end

function HeroHelperTool.GetHeroDesc(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["Desc"] or ""
end

function HeroHelperTool.GetHeroIcon(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["Icon"]
end

function HeroHelperTool.GetHeroExpertIcon(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["ExpertIcon"]
end

function HeroHelperTool.GetHeroProf(heroId)
    return tostring(HeroHelperTool.GetHeroProto(heroId)["Profession"] or "None")
end

function HeroHelperTool.GetHeroTalent(heroId)
    return "" --tostring(HeroHelperTool.GetHeroProto(heroId)["Talent"] or "None")
end

function HeroHelperTool.GetHeroCategoryId(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["ArmedForceId"] or "0"
end

function HeroHelperTool.GetHeroAvatarIcon(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["AvatarIcon"]
end

function HeroHelperTool.GetHeroArmedForceId(heroId)
    return HeroHelperTool.GetHeroCategoryId(heroId)
end

function HeroHelperTool.GetHeroArmedForceName(heroId)
    local armId = HeroHelperTool.GetHeroCategoryId(heroId)
    for _, v in pairs(ArmedForceDataTable) do
        if v["ArmedForceId"] == armId then
            return v["Name"] or ""
        end
    end
    return ""
end

function HeroHelperTool.IsHeroSelectable(heroId)
    return HeroHelperTool.GetHeroProto(heroId)["bUserSelectable"]
end

function HeroHelperTool.GetProfProto(profId)
    return HeroProfessionDataTable[profId] or {}
end

function HeroHelperTool.GetProfName(profId)
    return HeroHelperTool.GetProfProto(profId)["Name"] or ""
end

function HeroHelperTool.GetProfShortName(profId)
    return HeroHelperTool.GetProfProto(profId)["ShortName"] or ""
end

function HeroHelperTool.GetProfDesc(profId)
    return HeroHelperTool.GetProfProto(profId)["Desc"] or ""
end

function HeroHelperTool.GetProfIcon(profId)
    return HeroHelperTool.GetProfProto(profId)["Icon"] or ""
end

function HeroHelperTool.GetProfNameByHeroId(heroId)
    local profId = HeroHelperTool.GetHeroProf(heroId)
    return HeroHelperTool.GetProfName(profId)
end

function HeroHelperTool.GetProfShortNameByHeroId(heroId)
    local profId = HeroHelperTool.GetHeroProf(heroId)
    return HeroHelperTool.GetProfShortName(profId)
end

function HeroHelperTool.GetProfDescByHeroId(heroId)
    local profId = HeroHelperTool.GetHeroProf(heroId)
    return HeroHelperTool.GetProfDesc(profId)
end

function HeroHelperTool.GetProfIconByHeroId(heroId)
    local profId = HeroHelperTool.GetHeroProf(heroId)
    return HeroHelperTool.GetProfIcon(profId)
end

function HeroHelperTool.GetHeroesByProfId(profId)
    local heroes = {}
    local insert = table.insert
    for heroId, HeroDataRow in pairs(HeroDataTable) do
        if tostring(HeroDataRow["Profession"]) == profId then
            insert(heroes, heroId)
        end
    end
    return heroes
end

local function GetFashionMallProto(ItemId)
    return FashionMallDataTable[ItemId] or {}
end

--- 是否能通过商城购买
function HeroHelperTool.IsHeroForSale(ItemId)
    return GetFashionMallProto(ItemId)["ForSale"] or false
end

function HeroHelperTool.GetHeroMoneyType1(ItemId)
    local MoneyType1 = GetFashionMallProto(ItemId)["Money1"]
    return MoneyType1 and tostring(MoneyType1) or ""
end

function HeroHelperTool.GetHeroPrice1(ItemId)
    return GetFashionMallProto(ItemId)["Price1"] or 0
end

-- function HeroHelperTool.GetHeroSpecialItemInfo(heroId, gameMode)
--     local specialItems = Server.HeroServer:GetHeroModeSpecialItems(heroId, gameMode)
--     if specialItems and specialItems[1] then
--         local specialItemInfo = {}
--         local heroInfo = HeroDataTable[tostring(heroId)]
--         local combatRoleId = heroInfo.CombatRoleId
--         local itemInfo = ItemConfigTool.GetItemConfigById(specialItems[1].id)
--         if not itemInfo then
--             loginfo("HeroHelperTool.GetHeroSpecialItemInfo 不存在的道具 id="..specialItems[1].id)
--             return nil
--         end
--         specialItemInfo.Name = itemInfo.Name
--         specialItemInfo.Desc = itemInfo.Description
--         local bFound = false
--         for _, combatInfo in pairs(CombatRoleAbilityDataTable) do
--             if combatInfo.CombatRoleAbilityID == combatRoleId and combatInfo.GameMode == gameMode then
--                 if combatInfo.AbilityList then
--                     specialItemInfo.Icon = combatInfo.SpecialItemIcon
--                     bFound = true
--                 end
--             end
--         end
--         if not bFound then
--             loginfo("HeroHelperTool.GetHeroSpecialItemInfo 道具没有配置图标 CombatRoleAbilityID="
--                 ..combatRoleId.."GameMode="..gameMode)
--         end
--         return specialItemInfo
--     end
--     return nil
-- end

-- function HeroHelperTool.GetHeroAbilityInfo(heroId, gameMode)
--     local heroInfo = HeroDataTable[tostring(heroId)]
--     local combatRoleId = heroInfo.CombatRoleId
--     for _, combatInfo in pairs(CombatRoleAbilityDataTable) do
--         if combatInfo.CombatRoleAbilityID == combatRoleId and combatInfo.GameMode == gameMode then
--             if combatInfo.AbilityList then
--                 return AbilityDataTable[tostring(combatInfo.AbilityList)]
--             end
--         end
--     end
--     loginfo("HeroHelperTool.GetHeroAbilityInfo 没有配置英雄技能 CombatRoleAbilityID="
--         ..combatRoleId.."GameMode="..gameMode)
--     return nil
-- end

-- function HeroHelperTool.GetArmedForceAbilityInfo(heroId)
--     local armedForceInfo = HeroHelperTool.GetArmedForceInfo(heroId)
--     return AbilityDataTable[tostring(armedForceInfo.AbilityId)]
-- end

function HeroHelperTool.GetArmedForceWeaponIconList(heroId)
    heroId = tostring(heroId)
    local resultList = {}
    local armedForceInfo = HeroHelperTool.GetArmedForceInfo(heroId)
    if armedForceInfo then
        local weaponTypeListStr = armedForceInfo.weaponTypeList
        local weaponTypeList =  string.split(weaponTypeListStr, ',')
        if weaponTypeList then
            for idx, weaponType in pairs(weaponTypeList) do
                local weaponItemType = tonumber(weaponType) % 1800
                local iconPath = ItemConfig.MapWeapon2Image[weaponItemType]
                resultList[idx] = iconPath
            end
        end
    end
    return resultList
end

function HeroHelperTool.GetArmedForceWeaponIconListByArmId(armId)
    local resultList = {}
    local armedForceInfo = ArmedForceDataTable[tostring(armId)]
    local weaponTypeListStr = armedForceInfo.weaponTypeList
    local weaponTypeList =  string.split(weaponTypeListStr, ',')
    if weaponTypeList then
        for idx, weaponType in pairs(weaponTypeList) do
            local weaponItemType = tonumber(weaponType) % 1800
            local iconPath = ItemConfig.MapWeapon2Image[weaponItemType]
            resultList[idx] = iconPath
        end
    end
    return resultList
end

function HeroHelperTool.GetArmedForceInfo(heroId)
    local armId = HeroHelperTool.GetHeroArmedForceId(heroId)
    return ArmedForceDataTable[tostring(armId)]
end

function HeroHelperTool.GetArmedForceInfoByArmId(armId)
    return ArmedForceDataTable[tostring(armId)]
end

function HeroHelperTool.SetHeroIcon(imageWidget, heroId, bAutoSize)
    bAutoSize = setdefault(bAutoSize, true)
    heroId = tostring(heroId)
    local heroIcon = HeroHelperTool.GetHeroIcon(heroId)
    if heroIcon and heroIcon.AssetPathName and heroIcon.AssetPathName ~= "None" then
        imageWidget:AsyncSetImagePath(heroIcon, bAutoSize)
        imageWidget:SelfHitTestInvisible()
    else
        imageWidget:Collapsed()
    end
end

-- 兵种详情

function HeroHelperTool.GetExpertInfo(armId)
    return ExpertInfoDataTable[armId] or {}
end

function HeroHelperTool.GetExpertTypeName(armId)
    return HeroHelperTool.GetExpertInfo(armId)["ExpertTypeName"] or ""
end

function HeroHelperTool.GetExpertTalentName(armId)
    return HeroHelperTool.GetExpertInfo(armId)["ExpertTalentName"] or ""
end

function HeroHelperTool.GetExpertTalentIcon(armId)
    return HeroHelperTool.GetExpertInfo(armId)["Icon"]
end

function HeroHelperTool.GetExpertTalentDesc(armId)
    return HeroHelperTool.GetExpertInfo(armId)["ExpertTalentDesc"] or ""
end

---------------------------------------------------------------------------------------------------------------------------------------------------------------
-------------------------------------------------------------------------角色商业化相关----------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------------------------------------------------

-------------------------------------------------------
-----------------------角色名片-------------------------
-------------------------------------------------------
local heroCarData = Facade.TableManager:GetTable("HeroCardData") or {}
local heroBadgeData = Facade.TableManager:GetTable("HeroBadgeData") or {}
local heroTitleData = Facade.TableManager:GetTable("HeroTitleData") or {}
local heroKillLineData = Facade.TableManager:GetTable("HeroKillLineData") or {}

-- 获取干员名片客户端表现数据
function HeroHelperTool.GetHeroCardData()
    return heroCarData
end

-- 获取干员徽章客户端表现数据
function HeroHelperTool.GetHeroBadgeData()
    return heroBadgeData
end

-- 获取干员头衔客户端表现数据
function HeroHelperTool.GetHeroTitleData()
    return heroTitleData
end

-- 获取干员击杀台词客户端表现数据
function HeroHelperTool.GetHeroKillLineData()
    return heroKillLineData
end

-- 获取干员名片客户端表现Row数据
function HeroHelperTool.GetHeroCardDataRow(id)
    local data = heroCarData[tostring(id)]
    if data then
        return data
    end
    logwarning("heroCarData is nil, id:", id)
end

-- 获取干员徽章客户端表现Row数据
function HeroHelperTool.GetHeroBadgeDataRow(id)
    local data = heroBadgeData[tostring(id)]
    if data then
        return data
    end
    logwarning("heroBadgeData is nil, id:", id)
end

-- 获取干员头衔客户端表现Row数据
function HeroHelperTool.GetHeroTitleDataRow(id)
    local data = heroTitleData[tostring(id)]
    if data then
        return data
    end
    logwarning("heroTitleData is nil, id:", id)
end

-- 获取干员击杀台词客户端表现Row数据
function HeroHelperTool.GetHeroKillLineDataRow(id)
    local data = heroKillLineData[tostring(id)]
    if data then
        return data
    end
    logwarning("heroKillLineData is nil, id:", id)
end

-- 获取已装备的干员名片图片路径
function HeroHelperTool.GetSelectedHeroCardPath(heroIdStr)
    local cardId = Server.HeroServer:GetSelectedBusinessCard(heroIdStr)
    if cardId and heroCarData[tostring(cardId)] then
        return heroCarData[tostring(cardId)].CardPath
    end
    return nil
end

-- 根据名片道具ID获取图片路径
function HeroHelperTool.GetHeroCardPathByCardId(cardId)
    if cardId and heroCarData[tostring(cardId)] then
        return heroCarData[tostring(cardId)].CardPath
    end
    return nil
end

-- 获取已装备的干员头衔名称
function HeroHelperTool.GetSelectedHeroTitleName(heroIdStr)
    local titleId = Server.HeroServer:GetSelectedTitle(heroIdStr)
    if titleId and heroTitleData[tostring(titleId)] then
        return heroTitleData[tostring(titleId)].HeroTitleName
    end
    return nil
end

-- 根据头衔道具ID获取头衔名称
function HeroHelperTool.GetHeroTitleNameByTitleId(titleId)
    if titleId and heroTitleData[tostring(titleId)] then
        return heroTitleData[tostring(titleId)].HeroTitleName
    end
    return nil
end

-- 获取已装备的干员头衔图片
function HeroHelperTool.GetSelectedHeroTitleImg(heroIdStr)
    local titleId = Server.HeroServer:GetSelectedTitle(heroIdStr)
    if titleId and heroTitleData[tostring(titleId)] then
        return heroTitleData[tostring(titleId)].HeroTitleImage
    end
    return nil
end

-- 根据头衔道具ID获取头衔图片
function HeroHelperTool.GetHeroTitleImgByTitleId(titleId)
    if titleId and heroTitleData[tostring(titleId)] then
        return heroTitleData[tostring(titleId)].HeroTitleImage
    end
    return nil
end

-- 根据徽章道具ID获取图片路径
function HeroHelperTool.GetHeroBadgePathByBadgeId(badgeId)
    if badgeId and heroBadgeData[tostring(badgeId)] then
        return heroBadgeData[tostring(badgeId)].BadgeImage
    end
    return nil
end

-------------------------------------------------------
-----------------------角色名片成就相关-------------------------
-------------------------------------------------------
local GetHZAchievementDataTable = function()
    return Facade.TableManager:GetTable("HZAchievement")
end

local GetTXAchievementDataTable = function()
    return Facade.TableManager:GetTable("TXAchievement")
end

local GetAchievementDataTable = function()
    return Facade.TableManager:GetTable("Achievement")
end

HeroHelperTool.GetHZAchievementDataTableRow = function(HZAchievementId)
    for k, Row in pairs(GetHZAchievementDataTable()) do
        if Row.ID == HZAchievementId then
            return Row
        end
    end
    logwarning("HeroHelperTool.GetHZAchievementDataTableRow HZAchievementCfg is nil, HZAchievementId:", HZAchievementId)
    return nil
end

HeroHelperTool.GetTXAchievementDataTableRow = function(TXAchievementId)
    for k, Row in pairs(GetTXAchievementDataTable()) do
        if Row.ID == TXAchievementId then
            return Row
        end
    end
    logwarning("HeroHelperTool.GetTXAchievementDataTableRow TXAchievementCfg is nil, TXAchievementId:", TXAchievementId)
    return nil
end

HeroHelperTool.GetAchievementDataTableRow = function(AchievementId)
    for k, Row in pairs(GetAchievementDataTable()) do
        if Row.ID == AchievementId then
            return Row
        end
    end
    logwarning("HeroHelperTool.GetAchievementDataTableRow AchievementCfg is nil, AchievementId:", AchievementId)
    return nil
end

function HeroHelperTool.GetHZAchievementRowByModel(badgeType, heroID)
    local RowData = {}
    for index, row in pairs(GetHZAchievementDataTable()) do
        local isHide = false
        if row.Hide == -1 then
            if Server.HeroServer:IsAccessoryUnlocked(tostring(heroID), row.BadgeId[1]) then
                isHide = false
            else
                isHide = true
            end
        end
        if not isHide then
            if badgeType == Module.Hero.Config.EBadgeType.Exclusive then
                if row.Model == badgeType then
                    if heroID == row.Heroid then
                        table.insert(RowData, row)
                    end
                end
            else
                if row.Model == badgeType then
                    table.insert(RowData, row)
                end
            end
        end
    end
    table.sort(RowData, function(a,b)
        return a.Order < b.Order
    end)
    return RowData
end

function HeroHelperTool.GetTXAchievementRowByModel(titleType, heroID)
    local RowData = {}
    for index, row in pairs(GetTXAchievementDataTable()) do
        local isHide = false
        if row.Hide == -1 then
            if Server.HeroServer:IsAccessoryUnlocked(tostring(heroID), row.HeroTitleId) then
                isHide = false
            else
                isHide = true
            end
        end
        if not isHide then
            if titleType == Module.Hero.Config.ETitleType.Exclusive then
                if row.Model == titleType then
                    if heroID == row.Heroid then
                        table.insert(RowData, row)
                    end
                end
            else
                if row.Model == titleType then
                    table.insert(RowData, row)
                end
            end
        end
    end
    table.sort(RowData, function(a,b)
        return a.Order < b.Order
    end)
    return RowData
end

---------弃用--------
--临时测试使用，目前后台的局外配置局内带入的功能还没有做，只能先这样通过角色ID来获取卡片信息
--后面会直接通过后台给的卡片ID来获取卡片信息(因为存在后期每行BelongedHeroIDs填0的情况)
-- function HeroHelperTool.GetHeroCardDataRow(heroIds)
--     local cardDataRow = nil
--     if #heroIds == 0 then
--        logerror("[echewzhu] the heroIds is empty!!")
--        return nil
--     end
--     -- 如果BelongedHeroIDS只配置了一个0，那么这个Row就是通用的
--     -- 如果配置了HeroId，那么这个卡片只给这几个干员使用
--     for k,v in pairs(heroCarData)do
--         --TODO
--         -- if tonumber(v.BelongedHeroIDs[1]) == 0  then
--         --     cardDataRow = 
--         -- end
--         for i,j in pairs(v.BelongedHeroIDs)do
--             if tostring(j) == tostring(heroIds[i]) then
--                 cardDataRow = v
--             end
--         end
--     end
--     return cardDataRow or nil
-- end
---------弃用--------

---------弃用--------
--获取名片图片路径
function HeroHelperTool.GetHeroCardPath(heroId)
    local cardDataRow = HeroHelperTool.GetHeroCardDataRow({heroId})
    if not cardDataRow then
        logerror("[echewzhu] CardDataRow is not valid!")
        return nil
    end
    return cardDataRow.CardPath or nil
end
---------弃用--------

-------------------------------------------------------
-----------------------角色处决-------------------------
-------------------------------------------------------

local heroExecutionData = Facade.TableManager:GetTable("HeroExecutionData")
--获取所有属于此角色的处决信息
function HeroHelperTool.GetAllFinisherData()
    return heroExecutionData or {}
end

--获取处决所依赖外观的名字
function HeroHelperTool.GetFinisherAcquireFahsionName(inFinisherId)

    inFinisherId = tostring(inFinisherId)
    
    local res = "None"
    if  inFinisherId then
      if heroExecutionData[inFinisherId] then
        local acquireFashionId = heroExecutionData[inFinisherId].BelongedFashionIDs and heroExecutionData[inFinisherId].BelongedFashionIDs[1]
        local fasionData = HeroHelperTool.GetFashionDataTable()
        res = fasionData and fasionData[acquireFashionId] and fasionData[acquireFashionId].Name
      end
    end

    return res
end


-------------------------------------------------------
-----------------------角色手势-------------------------
-------------------------------------------------------

--获取所有属于此角色的处决信息
local HeroGestureData = Facade.TableManager:GetTable("HeroGestureData")
function HeroHelperTool.GetAllGestureData()
    return HeroGestureData or {}
end

-------------------------------------------------------
-----------------------角色表演动作-------------------------
-------------------------------------------------------

--获取所有属于此角色的动作信息
local CharacterAnimShowData = Facade.TableManager:GetTable("CharacterAnimShowData")
function HeroHelperTool.GetAllAnimShowData()
    return CharacterAnimShowData or {}
end

-------------------------------------------------------
-----------------------角色喷漆-------------------------
-------------------------------------------------------

--获取所有属于此角色的喷漆信息
local HeroSprayPaintData = Facade.TableManager:GetTable("HeroSprayPaintData")
function HeroHelperTool.GetAllSprayPaintData()
    return HeroSprayPaintData or {}
end

-------------------------------------------------------
-----------------------角色台词-------------------------
-------------------------------------------------------

--获取所有属于此角色的台词信息
local HeroLinesData = Facade.TableManager:GetTable("HeroLinesData")
function HeroHelperTool.GetAllLinesData()
    return HeroLinesData or {}
end

function HeroHelperTool.GetLinesData(rowName)
    return HeroLinesData[tostring(rowName)] or nil
end

--找到两张表中相同的元素
function HeroHelperTool.FindCommonElements(accessoriesByType, dataTableDatas)
    local commonElementList = {}
    for index, accessory in pairs(accessoriesByType) do
        local data = dataTableDatas[tostring(accessory.item.prop_id)]
        if data then
            table.insert(commonElementList, data)
        end
    end
    return commonElementList
end

-------------------------------------------------------
-----------------------兵种道具(包括物品、处决)-------------------------
-------------------------------------------------------

local HeroItemData = Facade.TableManager:GetTable("Hero/HeroItemData")
function HeroHelperTool.GetAllHeroItemData()
    return HeroItemData or {}
end

function HeroHelperTool.GetHeroItemRowByID(heroItemID)
    local heroItemData = HeroHelperTool.GetAllHeroItemData()
    if not heroItemID  then
        return
    end
    ---@type HeroConfig.CommercialType  
    local heroItemType
    local rowData
    if not rowData and heroItemData then
        rowData = HeroItemData[tostring(heroItemID)]
        heroItemType = 10
    end
    if not rowData and heroExecutionData then
        rowData = heroExecutionData[tostring(heroItemID)]
        heroItemType = 6
    end
    if not rowData then
        heroItemType = nil
    end
    return rowData,heroItemType
end

---获取时装配件的icon
function HeroHelperTool.GetHeroItemIconByID(heroItemID)
    local rowData,heroItemType = HeroHelperTool.GetHeroItemRowByID(heroItemID);
    if rowData then
        if heroItemType == 10 then --干员道具
            return rowData.HeroItemImage
        elseif  heroItemType == 6 then --处决
            return rowData.ExecutionImage
        end
    end
end

---获取时装配件的名字
function HeroHelperTool.GetHeroItemNameByID(heroItemID)
    local rowData,heroItemType = HeroHelperTool.GetHeroItemRowByID(heroItemID);
    if rowData then
        if heroItemType == 10 then --干员道具
            return rowData.HeroItemName
        elseif  heroItemType == 6 then --处决
            return rowData.ExecutionName
        end
    end
end

-------------------------------------------------------
-----------------------干员手表-------------------------
-------------------------------------------------------

function HeroHelperTool.GetAllWatchData()
    return HeroWatchDataTable or {}
end

-------------------------------------------------------
-----------------------角色商业化表格合集-------------------------
-------------------------------------------------------
local CommercialDataTables = {
    [EHeroAccessroy.Gesture] = HeroGestureData or {},
    [EHeroAccessroy.Execution] = heroExecutionData or {},
    [EHeroAccessroy.AnimShow] = CharacterAnimShowData or {},
    [EHeroAccessroy.SparyPaint] = HeroSprayPaintData or {},
    [EHeroAccessroy.Lines] = HeroLinesData or {},
    [0] = HeroFashionDataTable or {}, --时装不属于常规商业化
    [EHeroAccessroy.SoldierProp] = HeroItemData or {},
    [EHeroAccessroy.Watch] = HeroWatchDataTable or {},
    [EHeroAccessroy.Badge] = heroBadgeData or {},
    [EHeroAccessroy.Card] = heroCarData or {},
    [EHeroAccessroy.Title] = heroTitleData or {},
    [EHeroAccessroy.KillaLines] = heroKillLineData or {},
}

--获取商业化表格的PurchaseMethod ButtonDescription GoodsId
function HeroHelperTool.GetInfoUsingAtButton(accessoryId)
    accessoryId = setdefault(accessoryId, 0)
    local type = ItemHelperTool.GetSubTypeById(accessoryId)
    local dataTable = CommercialDataTables[type or 0]
    local jumpId = nil
    local goodsId = 0
    local description = ""
    if dataTable and dataTable[tostring(accessoryId)] then
        goodsId = dataTable[tostring(accessoryId)].GoodsId or 0

        if type > 0 and type <= EHeroAccessroy.SoldierProp then --部分商业化资源双模式拆分
            if  Facade.GameFlowManager:GetCurrentGameFlow()== EGameFlowStageType.Lobby then --MP大厅
                jumpId = dataTable[tostring(accessoryId)].PurchaseMethodMP and dataTable[tostring(accessoryId)].PurchaseMethodMP[1]
                description = dataTable[tostring(accessoryId)].ButtonDescriptionMP
            elseif Facade.GameFlowManager:GetCurrentGameFlow()== EGameFlowStageType.SafeHouse then --SOL大厅
                jumpId = dataTable[tostring(accessoryId)].PurchaseMethod and dataTable[tostring(accessoryId)].PurchaseMethod[1]
                description = dataTable[tostring(accessoryId)].ButtonDescription
            end
        else
            jumpId = dataTable[tostring(accessoryId)].PurchaseMethod and dataTable[tostring(accessoryId)].PurchaseMethod[1]
            description = dataTable[tostring(accessoryId)].ButtonDescription
        end 

        description = description or Module.Hero.Config.Loc.HeroCommercialDefault
    end
    return jumpId, goodsId, description
end

--是否是默认的干员商业化道具
function HeroHelperTool.IsDefaultAccessory(dataRow)
    if not dataRow then
        return false
    end
    local IsDefUnlock = dataRow.IsDefUnlock
    local IsDefEquip = dataRow.IsDefEquip
    return IsDefUnlock and IsDefEquip
end

local GeneralAccessoryCache = {}
--是否是通用商业化道具，BelongedHeroIDs== {0}做判断依据
function HeroHelperTool.IsGeneralAccessory(accessoryId)
    accessoryId = tostring(accessoryId)
    if GeneralAccessoryCache[accessoryId] then
        return GeneralAccessoryCache[accessoryId]
    end
    
   local accessoryRow  = HeroHelperTool.GetAccessoryDataRow(accessoryId)
   if accessoryRow and accessoryRow.BelongedHeroIDs then
        GeneralAccessoryCache[accessoryId] = (#accessoryRow.BelongedHeroIDs == 1 and tonumber(accessoryRow.BelongedHeroIDs[1]) == 0)
   end
   return GeneralAccessoryCache[accessoryId]
end

--获取干员商业化道具的行数据
function HeroHelperTool.GetAccessoryDataRow(accessoryId)
    if not CommercialDataTables or not accessoryId then
        return
    end
    local accessoryIdStr= tostring(accessoryId)
    local subType = ItemHelperTool.GetSubTypeById(tonumber(accessoryId))
    return CommercialDataTables[subType] and CommercialDataTables[subType][accessoryIdStr]
end


function HeroHelperTool.GetFashionDataRow(inFashionId)
    return  HeroHelperTool.GetFashionDataTable()[inFashionId]
end

function HeroHelperTool.GetFashionDataTable()
    return HeroFashionDataTable or {}
end


local heroPropsId2FashionDataRow = {}
---根据兵种道具id查询对应的时装数据
function HeroHelperTool.FindFashionDataRowByHeroPropsId(inHeroPropsId)
    inHeroPropsId = tostring(inHeroPropsId)

    if heroPropsId2FashionDataRow[inHeroPropsId] then
        return heroPropsId2FashionDataRow[inHeroPropsId]
    end
    local dataTable =  HeroHelperTool.GetFashionDataTable()
    for k, dataRow in pairs(dataTable) do
      for index, heroPropsId in ipairs(dataRow.HeroItemIDs) do
        if heroPropsId == inHeroPropsId then
            heroPropsId2FashionDataRow[inHeroPropsId] = dataRow
          return dataRow
        end
      end
    end
end

--获取干员商业化道具行数据字段
---@param accessoryId string|number 干员商业化道具ID
---@param fieldName string 行字段名
function HeroHelperTool.GetAccessoryRowField(accessoryId, fieldName)
    if not accessoryId or not fieldName then
        return
    end
    local accessoryRow  = HeroHelperTool.GetAccessoryDataRow(accessoryId)
    return accessoryRow and accessoryRow[fieldName]
end

--#reigon other

function HeroHelperTool.GetAllHeroVOData()
    return HeroVODataTable or {}
end


--列表中查找商业道具未读项的索引
function HeroHelperTool.FindUnreadCommerceItemIndex(inList, inHeroID , InItemIDStr)
    if not inList then
        return
    end

    for k,v in ipairs(inList) do
        if v[InItemIDStr] and  not Server.HeroServer:IsAccessoryReaded(inHeroID, v[InItemIDStr]) then
            return k
        end
    end
   
end

---获取干员解锁信息
---@param inHeroId number|string 干员ID
---@return table unLockInfo  解锁信息
---@return table heroInfo  干员信息
function HeroHelperTool.GetHeroUnlockInfo(inHeroId)
    inHeroId = tonumber(inHeroId)
	if inHeroId == nil then
		loginfo("heroId is empty, get data failed")
		return
	end

    local InfoTable = HeroHelperTool.GetHeroUnlock()
	--整理解锁数据
	local unLockInfo = {}
	for key, value in pairs(InfoTable or {}) do
		if value and inHeroId == value.HeroId then
			local list = {}
			list.HeroId = value.HeroId
			list.GetType = value.GetType
			list.Parameter = tostring(value.Parameter)
			list.Name = value.Name
			table.insert(unLockInfo, list)
		end
	end
	local heroInfo = HeroDataTable and HeroDataTable[tostring(inHeroId)]

	return unLockInfo, heroInfo
end

--获取干员解锁数据
function HeroHelperTool.GetHeroUnlock()
    local info = Server.HeroServer:GetHeroUnlockInfo()
    local list = {}
    local time = Facade.ClockManager:GetServerTimestamp()
    --处理数据
    for index, value in ipairs(info or {}) do
         local name = ""
        --本地化处理(海外)
        if value.name then
            name = LocalizeTool.GetTransStr(value.name)
        end
        local hero = {
            HeroId = value.hero_id,
            GetType = value.get_type,
            Parameter = value.parameter,
            Name = name,
            start_time = value.start_time,
            end_time = value.end_time,
        }
        if value.get_type == 4 then
            table.insert(list, hero)
        elseif value.start_time <= time then
            if 0 < value.end_time then
                if time <= value.end_time then
                    table.insert(list, hero)
                end
            else
                table.insert(list, hero)
            end
        end
    end
    return list
end

function HeroHelperTool.GetHeroUnlockInfoDataRows(inHeroId)
    inHeroId = tonumber(inHeroId)
    local infoList = {}
    local InfoTable = Facade.TableManager:GetTable("HeroUnLock")
    for key, value in pairs(InfoTable or {}) do
        if value and inHeroId == value.HeroId then
            table.insert(infoList, value)
        end
    end

    return infoList
end



---是否是可招募获取的干员
function HeroHelperTool.IsRecruitableHero(inHeroId)
    return Server.HeroServer:IsHeroRecruitable(inHeroId)
    -- local unlockInfo,_ = HeroHelperTool.GetHeroUnlockInfoDataRows(inHeroId)
    -- if not unlockInfo then
    --     return false
    -- end
    -- for k,v in pairs(unlockInfo) do
    --     if v.GetType == 7 then
    --         return true
    --     end
    -- end
    -- return false
end

function HeroHelperTool.GetAllHeroRecruitGoalDescData()
    return HeroRecruitGoalDescTable or {}
end

function HeroHelperTool.GetHeroRecruitGoalDescRow(inGoalId)
    if  not inGoalId then
        return
    end
    return HeroHelperTool.GetAllHeroRecruitGoalDescData()[tonumber(inGoalId)]
end

--- 获取目标完成进，0-1
---@param inGoalData HeroRecruitmentTaskItemData
function HeroHelperTool.GetGoalProcess(inGoalData)
 if not inGoalData or inGoalData.cur_value == nil or inGoalData.max_value == nil then
    return false    
 end
 return inGoalData.cur_value/inGoalData.max_value
end

--#endregion

function HeroHelperTool.GetRecrtuitTaskDataTable()
    return  RecrtuitTaskDataTable or {}
end

function HeroHelperTool.GetRecrtuitTaskDataRow(inHeroId)
    local RecrtuitTaskDataTable =  HeroHelperTool.GetRecrtuitTaskDataTable()
    for k,v in pairs(RecrtuitTaskDataTable) do
        if v.HeroID == inHeroId then
            return v
        end
    end
end

---排序任务
---@param rulesHeroId string|number
function HeroHelperTool.SortRecruitmentGoals(inGoalList, rulesHeroId)
    rulesHeroId = tonumber(rulesHeroId)
    if not inGoalList or #inGoalList == 0 then
        return false
    end

    local RecrtuitTaskRow = HeroHelperTool.GetRecrtuitTaskDataRow(rulesHeroId)
    local goalId2SortPrior = {}
    if RecrtuitTaskRow and RecrtuitTaskRow.OpMetaGoals then
        for k,v in ipairs(RecrtuitTaskRow.OpMetaGoals) do
            goalId2SortPrior[v] = k
        end
        table.sort(inGoalList,function (a,b)
            return goalId2SortPrior[a.goal_id] < goalId2SortPrior[b.goal_id]
        end)

        return true
    else
        return false
    end
    
end

--干员道具
function HeroHelperTool.GetHeroItemData(itemId)
    local itemData = nil
    if HeroItemData then
        itemData = HeroItemData[itemId]
    end
    return itemData
end 

function HeroHelperTool.GetHeroPropsMysticalSkinLotteryTable()
    return Facade.TableManager:GetTable("HeroPropsMysticalSkinLottery") or {}
end

function HeroHelperTool.GetHeroPropsMysticalSkinLotteryDataTable()
    return Facade.TableManager:GetTable("HeroPropsMysticalSkinLotteryData") or {}
end

function HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataTable()
    return Facade.TableManager:GetTable("HeroPropsMysticalSkinLotteryDescData") or {}
end

function HeroHelperTool.GetHeroPropsMysticalSkinStorageDataTable()
    return Facade.TableManager:GetTable("HeroPropsMysticalSkinStorageData") or {}
end

--获取兵种道具玄学奖池信息按class分类
function HeroHelperTool.GetHeroPropsMysticalSkinLotteryDataRowByClass(InLotteryId)
    local HeroPropsMysticalSkinLotteryData = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDataTable()
    local ret = {}
    for k, dataRow in pairs(HeroPropsMysticalSkinLotteryData) do
        if dataRow.HeroItemLotteryId == InLotteryId then
            ret[dataRow.Class] = dataRow
        end
    end

    return ret
end

--获取兵种道具玄学奖池信息通过品质
function HeroHelperTool.GetHeroPropsMysticalSkinLotteryDataRowByQuality(InLotteryId,InQuality)
    local classMap = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDataRowByClass(InLotteryId)
    if not classMap then
        return
    end
    local class = HeroHelperTool.GetHeroPropsMysticalClassByIndex(InQuality)
    if not class then
        return
    end
    return classMap[class]
end

--获取兵种道具玄学奖池描述信息通过品质
function HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(InLotteryId,InQuality)
    local classMap = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByClass(InLotteryId)
    if not classMap then
        return
    end
    local class = HeroHelperTool.GetHeroPropsMysticalClassByIndex(InQuality)
    if not class then
        return
    end
    return classMap[class]
end

--获取兵种道具玄学奖池描述信息按class分类
function HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByClass(InLotteryId)
    local HeroPropsMysticalSkinLotteryDescData  = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataTable()
    local ret = {}
    for k, dataRow in pairs(HeroPropsMysticalSkinLotteryDescData) do
        if dataRow.HeroItemLotteryId == InLotteryId then
            ret[dataRow.Class] = dataRow
        end
    end

    return ret
end

--根据兵种道具id获取对应玄学奖池id
function HeroHelperTool.GetLotteryIdByHeroPropsId(InHeroPropsId)
    local HeroPropsMysticalSkinLottery = HeroHelperTool.GetHeroPropsMysticalSkinLotteryTable()
    InHeroPropsId = tonumber(InHeroPropsId)
    for k, dataRow in pairs(HeroPropsMysticalSkinLottery) do
        for idx, heroPropsId in pairs(dataRow.HeroItemID) do
            if heroPropsId == InHeroPropsId  then
                return  dataRow.HeroItemLotteryId
            end
        end
    end
end

--获取对应玄学奖池信息
function HeroHelperTool.GetLotteryRowByLotteryId(InLotteryId)
    local HeroPropsMysticalSkinLottery = HeroHelperTool.GetHeroPropsMysticalSkinLotteryTable()
    InLotteryId = tostring(InLotteryId)
    return HeroPropsMysticalSkinLottery[InLotteryId]
end


--获取兵种道具玄学奖池槽位信息

---@param InSlotIndex number 从1开始
function HeroHelperTool.GetHeroPropsMysticalSkinStorageDataRow(InLotteryId, InSlotIndex)
     
    local HeroPropsMysticalSkinStorageData = HeroHelperTool.GetHeroPropsMysticalSkinStorageDataTable()
    for k, dataRow in pairs(HeroPropsMysticalSkinStorageData) do
        if dataRow.HeroItemLotteryId == InLotteryId and dataRow.Slot == InSlotIndex then
            return dataRow
        end
    end
end

local PropsMysticalClass2Index = {
    [1] = "S",
    [2] = "A",
    [3] = "B",
    [4] = "C",
    [5] = "D",
}

---数字品阶转为Class
function HeroHelperTool.GetHeroPropsMysticalClassByIndex(InIndex)
    return PropsMysticalClass2Index[InIndex]
end

function HeroHelperTool.GetHeroPropsItemDetailTipsData(InlotteryId , inQuality)
    local tipsData = {}
    local lotteryDescD = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(InlotteryId,HeroArmedPropFashionQuality.Quality_D)
    local lotteryDescC = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(InlotteryId,HeroArmedPropFashionQuality.Quality_C)
    local lotteryDescB = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(InlotteryId,HeroArmedPropFashionQuality.Quality_B)
    local lotteryDescA = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(InlotteryId,HeroArmedPropFashionQuality.Quality_A)
    local lotteryDescS = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(InlotteryId,HeroArmedPropFashionQuality.Quality_S)

    -- if not (lotteryDescD and lotteryDescC and lotteryDescB and lotteryDescA and lotteryDescS) then
    --     return
    -- end
    tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle1Line(Module.Hero.Config.Loc.QualityDesc)

    ---D
    if lotteryDescD then
        local dName = lotteryDescD.Classname
        if inQuality == HeroArmedPropFashionQuality.Quality_D then
            dName  =StringUtil.Key2StrFormat(Module.Hero.Config.Loc.CurClass,{curClassName = lotteryDescD.Classname}) 
        end
    
        local dDesc = ""
        local effectStrListD = HeroHelperTool.GetHeroPropsItemDetailEeffctDescData(InlotteryId, HeroArmedPropFashionQuality.Quality_D)
        for index, str in ipairs(effectStrListD) do
            if dDesc ~= "" then
                dDesc =  dDesc.."\n" --新起一行
            end
            dDesc = dDesc.."+"..str
        end
        tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle2AndContentLine(dName,dDesc)
    end
   

    ---C
    if lotteryDescC then
        local cName = lotteryDescC.Classname
        if inQuality == HeroArmedPropFashionQuality.Quality_C then
            cName  =StringUtil.Key2StrFormat(Module.Hero.Config.Loc.CurClass,{curClassName = lotteryDescC.Classname})  
        end
        local cDesc = ""
        local effectStrListC = HeroHelperTool.GetHeroPropsItemDetailEeffctDescData(InlotteryId, HeroArmedPropFashionQuality.Quality_C)
        for index, str in ipairs(effectStrListC) do
            if cDesc ~= "" then
                cDesc =  cDesc.."\n" --新起一行
            end
            cDesc = cDesc.."+"..str
        end
        tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle2AndContentLine(cName,cDesc)
    end

    ---B
    if lotteryDescB then
        local bName = lotteryDescB.Classname
        if inQuality == HeroArmedPropFashionQuality.Quality_B then
            bName  =StringUtil.Key2StrFormat(Module.Hero.Config.Loc.CurClass,{curClassName = lotteryDescB.Classname})  
        end
        local bDesc = ""
        local effectStrListB = HeroHelperTool.GetHeroPropsItemDetailEeffctDescData(InlotteryId, HeroArmedPropFashionQuality.Quality_B)
        for index, str in ipairs(effectStrListB) do
            if bDesc ~= "" then
                bDesc =  bDesc.."\n" --新起一行
            end
            bDesc = bDesc.."+"..str
        end
        tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle2AndContentLine(bName,bDesc)
    end

    ---A
    if lotteryDescA then
        local aName = lotteryDescA.Classname
        if inQuality == HeroArmedPropFashionQuality.Quality_A then
            aName  =StringUtil.Key2StrFormat(Module.Hero.Config.Loc.CurClass,{curClassName = lotteryDescA.Classname})  
        end
        local aDesc = ""
        local effectStrListA = HeroHelperTool.GetHeroPropsItemDetailEeffctDescData(InlotteryId, HeroArmedPropFashionQuality.Quality_A)
        for index, str in ipairs(effectStrListA) do
            if aDesc ~= "" then
                aDesc =  aDesc.."\n" --新起一行
            end
            aDesc = aDesc.."+"..str
        end
        tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle2AndContentLine(aName,aDesc)
    end

    ---S
    if lotteryDescS then
        local sName = lotteryDescS.Classname
        if inQuality == HeroArmedPropFashionQuality.Quality_S then
            sName  =StringUtil.Key2StrFormat(Module.Hero.Config.Loc.CurClass,{curClassName = lotteryDescS.Classname})  
        end
        local sDesc = ""
        local effectStrListS = HeroHelperTool.GetHeroPropsItemDetailEeffctDescData(InlotteryId, HeroArmedPropFashionQuality.Quality_S)
        for index, str in ipairs(effectStrListS) do
            if sDesc ~= "" then
                sDesc =  sDesc.."\n" --新起一行
            end
            sDesc = sDesc.."+"..str
        end
        tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle2AndContentLine(sName,sDesc)
    end

    tipsData[#tipsData+1] = HeroHelperTool.GetTipsTitle2AndContentLine(nil,Module.Hero.Config.Loc.AdvanceDesc)

    return tipsData
end

function HeroHelperTool.GetHeroPropsItemDetailEeffctDescData(inLotteryId,inQuality)
    local ret  = {}
    local lotteryDesc = HeroHelperTool.GetHeroPropsMysticalSkinLotteryDescDataRowByQuality(inLotteryId,inQuality)
    if not lotteryDesc then
        return ret
    end

    for index = 1, 6 do
        local str = lotteryDesc[string.format("FeatureDesc%d",index)]
        if str ~= "" then
            ret[#ret+1] = lotteryDesc[string.format("FeatureDesc%d",index)]
        end
    end
    return ret
end

function HeroHelperTool.GetTipsTitle1Line(inContext,bHasSpaceLine)
    return {id = UIName2ID.Assembled_CommonMessageTips_V8, data = {textContent = inContext, styleRowId = "C000", fontStyleId = "Header3_28pt"}}
end

function HeroHelperTool.GetTipsTitle2AndContentLine(inTitle,inContext,bHasSpaceLine)
    return {id = UIName2ID.Assembled_CommonMessageTips_V6, data = {name = inTitle , desc = inContext, styleRowId = "C000", fontStyleId = "Header3_28pt"}}
end


function HeroHelperTool.GetTipsTitle2Line(inContext,bHasSpaceLine)
    if bHasSpaceLine then
        return {id = UIName2ID.Assembled_CommonMessageTips_V5, data = {textContent = inContext, styleRowId = "C000", fontStyleId = "Header1_24pt"}}
        
    else
        return {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = inContext, styleRowId = "C000", fontStyleId = "Header1_24pt"}}
    end
end

function HeroHelperTool.GetTipsNormalTextLine(inContext,bHasSpaceLine)
    if bHasSpaceLine then
        return {id = UIName2ID.Assembled_CommonMessageTips_V5, data = {textContent = inContext, styleRowId = "C000", fontStyleId = "Header1_24pt"}}
        
    else
        return {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = inContext, styleRowId = "C000", fontStyleId = "Header1_24pt"}}
    end
end

function HeroHelperTool.GetTipsSmallTextLine(inContext)
    return {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = inContext, styleRowId = "C000", fontStyleId = "Body0_20pt"}}
end

--获取兵种道具抽奖卷数量
function HeroHelperTool.GetHeroPropsLotteryTokenNum(inLotteryId)
    local ret = 0
    local lotteryDataRow = HeroHelperTool.GetLotteryRowByLotteryId(inLotteryId)
    if not lotteryDataRow then
        return ret
    end
    local lotteryTokenId = tonumber(lotteryDataRow.LotteryKeyId)
    ret = Server.CollectionServer:GetCollectionItemsNumById(lotteryTokenId)
    return ret
end

function HeroHelperTool.GetHeroPropsMysticalClassSortName(inQuality)
    local Ret = "None"
    
   if inQuality == HeroArmedPropFashionQuality.Quality_S then
        Ret = Module.Hero.Config.Loc.ClassSSortName
    elseif inQuality == HeroArmedPropFashionQuality.Quality_A then
        Ret = Module.Hero.Config.Loc.ClassASortName
    elseif inQuality == HeroArmedPropFashionQuality.Quality_B then
        Ret = Module.Hero.Config.Loc.ClassBSortName
    elseif inQuality == HeroArmedPropFashionQuality.Quality_C then
        Ret = Module.Hero.Config.Loc.ClassCSortName
    elseif inQuality == HeroArmedPropFashionQuality.Quality_D then
        Ret = Module.Hero.Config.Loc.ClassDSortName
    end
    return Ret
end

--创建itembase根据当前方案赋予feature
function HeroHelperTool.CreateHeroPropsItemWithCurScheme(heroPropsId)
    local item = ItemBase:New(heroPropsId)
    if item then
        local feature = item:GetFeature(EFeatureType.HeroProps)
        if feature then
            local curHeroPropsFashionInfo = Server.HeroServer:GetCurHeroPropsFashionInfo(heroPropsId)
            if curHeroPropsFashionInfo then
                feature:SetFashionInfo(curHeroPropsFashionInfo.fashion_id, curHeroPropsFashionInfo.quality)
            end
            feature:SetNeedRHI(true)
            item:RefreshIsUseRenderTexture()
        end
    end
    return item
end

---三角卷货币使用
function HeroHelperTool.IsEnoughCurrency(inCurrencyId,inRequireNum)
    if inCurrencyId ~= ECurrencyItemId.BindDiamond then
        return
    end

    local ownedCurrencyNum = Module.Currency:GetNumByItemId(inCurrencyId)
    local currency_type_sub =  ECurrencyItemId.Diamond
    local currentCurrencyCount_sub = Module.Currency:GetNumByItemId(currency_type_sub)
    return ownedCurrencyNum+currentCurrencyCount_sub >= inRequireNum
end

---获取购买方案使用三角卷和三角币组合
---@param requreNum number 目标数量
---@return boolean bIsEnough 是否足够
---@return number diamondNum 方案使用三角卷数量
---@return number bindNum 方案使用三角币数量
function HeroHelperTool.GetBuySchemeDiamondAndBind(requreNum)
    local bIsEnough = false
    local diamondNum = 0
    local bindNum = 0

    local ownedDiamondNum = Module.Currency:GetNumByItemId(ECurrencyItemId.Diamond)
    local ownedBindNum = Module.Currency:GetNumByItemId(ECurrencyItemId.BindDiamond)
    local sum = ownedDiamondNum + ownedBindNum

    bIsEnough = sum >= requreNum

    if bIsEnough then
        if ownedBindNum >= requreNum then
            bindNum = requreNum
            diamondNum = 0
        else
            bindNum = ownedBindNum
            diamondNum = requreNum - bindNum
        end
    else
        diamondNum = ownedDiamondNum
        bindNum  = ownedBindNum
    end
    return bIsEnough , diamondNum , bindNum
end

--获取设置界面的按键输入设置值， ActionOrAxisName 记录在 KeyMappingDefaultSettings 表中
---@param inActionOrAxisName string 输入操作名
---@param inConjunctionStr string 多个输入键之间的连接符
function HeroHelperTool.GetSettingInputKeyNames(inActionOrAxisName , inConjunctionStr)
    if not IsHD() then
        return 
    end
    local UDFHDKeySettingManager = import "DFHDKeySettingManager"
    if not isvalid(UDFHDKeySettingManager) then
        return
    end

    local KeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())

    if not isvalid(KeySettingMgr) then
        return
    end

    local row = KeySettingMgr:GetRowByActionOrAxisName(inActionOrAxisName, 0, true)

    if not row then
        return
    end

    local  results = UDFHDKeySettingManager.GetValidKeysFromSettingRow(row, 0)

    if not results then
        return
    end

    local rt = nil

    local UDFHDKeyIconManager = import "DFHDKeyIconManager"
    if not isvalid(UDFHDKeyIconManager) then
        return
    end
    local KeyIconMgr = UDFHDKeyIconManager.Get(GetGameInstance())
    if not isvalid(KeyIconMgr) then
        return
    end

    for index, v in ipairs(results) do
        local KeyStr = KeyIconMgr:GetNameByFKey(v)
        local bIsExeConjunction = (rt and inConjunctionStr)
        if bIsExeConjunction then
            rt = rt..inConjunctionStr..KeyStr
        else
            rt = KeyStr
        end
    end
    return rt
end


---判断FVecter2D位置是否在ui控件区域
function HeroHelperTool.IsPosInWidgetArea(inPos,inUIWidget)
    if not inUIWidget or not inPos then
        return false
    end
    local geometry = inUIWidget:GetCachedGeometry()
    return USlateBlueprintLibrary.IsUnderLocation(geometry, inPos)
end


--获取topbar货币区域的组件
function HeroHelperTool.GetTopBarCurrencyAreaWidget()
    local topBar =  Module.CommonBar:GetTopBarUIInst()
    if not topBar then
        return
    end
    if IsHD() then
        return topBar._wtGP_Currency
    else
        return topBar._wtHbInfo
    end
end

--获取所属于皮肤的商业化道具，如果没有规定则为空
function HeroHelperTool.GetAccessoryIdBelongHeroFashion(inFashionId,inAccessoryType)
    local fashionDataTable = HeroHelperTool.GetFashionDataTable()
    local fashionDataRow = fashionDataTable[inFashionId]
    local items = fashionDataRow.HeroItemIDs
    for _, heroCommerceItemID in ipairs(items) do
        if ItemHelperTool.GetSubTypeById(heroCommerceItemID) == inAccessoryType then
           return heroCommerceItemID 
        end
    end
end

--判断时装是否能装备处决
function HeroHelperTool.ExcutionCanEquipedFashionId(inFashionId,inExcutionId)
    local ret = false
    inFashionId = tostring(inFashionId)
    local excutionDataRow =  HeroHelperTool.GetAccessoryDataRow(inExcutionId)
    if not excutionDataRow then
        return ret
    end
    for _, fashionId in ipairs(excutionDataRow.BelongedFashionIDs) do
        if fashionId == "0" or fashionId == inFashionId then
            ret = true
            break
        end
    end
    return ret
end

--判断是否上了干员档案
function HeroHelperTool.CheckHasHeroProfile(heroId)
    local heroProfile = Facade.TableManager:GetTable("HeroProfile")
    if heroProfile then
        for _, profile in pairs(heroProfile) do
            if tostring(profile.ID) == tostring(heroId) then
                return true
            end
        end
    end
    return false
end

function HeroHelperTool.IsShowFashionTipsDetail(fashionId)
    if not fashionId then
        return false
    end
    local fashionQuality = ItemHelperTool.GetQualityTypeById(fashionId)

	return fashionQuality == 5 or fashionQuality == 4
end

return HeroHelperTool
