----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDRadioPanel
local SystemSettingHDRadioPanel = ui("SystemSettingHDRadioPanel")
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local UGPAudioStatics = import "GPAudioStatics"
--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION

function SystemSettingHDRadioPanel:Ctor()
    self:_BindWidget()
end

function SystemSettingHDRadioPanel:_BindWidget()
    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
    self._wtItemPanel = self:Wnd("wtItemPanel", UILightWidget)
    self._wtSpeakingChanelDropDownBox = self:Wnd("WBP_SetUpComponent_MultipleChoice_2", UIWidgetBase)
    self._wtListeningChanelDropDownBox = self:Wnd("WBP_SetUpComponent_MultipleChoice_4", UIWidgetBase)
    self._wtTeamPlayerList = self:Wnd("wtPlayerList", UIWidgetBase)
    self._wtPrompt = self:Wnd("_wtPrompt", UILightWidget)
end

function SystemSettingHDRadioPanel:_BindBtnEvent()
    self:AddLuaEvent(self._wtSpeakingChanelDropDownBox.evtOnOptionChanged, self._OnSpeakingChanelChanged, self)
    self:AddLuaEvent(self._wtListeningChanelDropDownBox.evtOnOptionChanged, self._OnListeningChanelChanged, self)
end

function SystemSettingHDRadioPanel:OnOpen()
    self:_BindBtnEvent()
    CommonSettingLogicHD.RefreshItemUIBackground(self._wtItemPanel)
    if UGPAudioStatics:IsUserMono() then
        self._vramPanel = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDRadioPrompt, self._wtPrompt, nil)
    end
end

function SystemSettingHDRadioPanel:OnShowBegin()
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.RadioSetting then
	    local list = {
	        -- BEGIN MODIFICATION @ VIRTUOS : 增加确认按键提示
	        {actionName = "Setting_Confirm_Gamepad", func = nil, caller = nil, bUIOnly = true},
	        -- END MODIFICATION
	        {actionName = "Reset", func = self._OnReset, caller = self}
	    }
	    local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
	    for _, v in ipairs(globalList) do
	        table.insert(list, v)
	    end
        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
        --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
        self:_RegisterNavGroup()
        --END MODIFICATION
    end
end

function SystemSettingHDRadioPanel:OnShow()
    if Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.RadioSetting then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
    end
    self:RefreshTeamPlayerList()
end

function SystemSettingHDRadioPanel:OnHide()
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)
end

function SystemSettingHDRadioPanel:_OnReset()
    local fReset = function()
        Module.GVoice.Field:ResetRoomMemberVolume()
        SettingLogicHD.ResetCurrentSettings()
        self:RefreshTeamPlayerList()
    end
    local resetInputTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetRadioTxt
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    Module.CommonTips:ShowConfirmWindow(resetInputTxt, CreateCallBack(fReset, self), nil, cancelTxt, confirmTxt)
end

function SystemSettingHDRadioPanel:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    --BEGIN MODIFICATION @ VIRTUOS : UI Navigation
    self:_RemoveNavGroup()
    --END MODIFICATION
end

function SystemSettingHDRadioPanel:OnClose()
    self:RemoveAllLuaEvent()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtPrompt)
end

function SystemSettingHDRadioPanel:_OnClickBtn()
end

function SystemSettingHDRadioPanel:_OnSpeakingChanelChanged(index)
    if index ~= ESpeakingChanel.Close and CommonSettingLogicHD.GetDataByID("ListeningChanel") == EListenChanel.Close then
        CommonSettingLogicHD.SetDataByID("ListeningChanel", EListenChanel.All)
        self._wtListeningChanelDropDownBox:ReloadSetting()
        self:_OnListeningChanelChanged(EListenChanel.All)
    else
        self:RefreshTeamPlayerList()
    end
end

function SystemSettingHDRadioPanel:_OnListeningChanelChanged(index)
    self:RefreshTeamPlayerList()
    if index == EListenChanel.All or index == EListenChanel.Camp then
        for i, item in ipairs(self._wtTeamPlayerList:GetAllChildren()) do
            item:DisableItem(false)
        end
    else
        for i, item in ipairs(self._wtTeamPlayerList:GetAllChildren()) do
            item:DisableItem(true)
        end
    end
end

function SystemSettingHDRadioPanel:RefreshTeamPlayerList()
    local bInGame = not Facade.GameFlowManager:CheckIsInFrontEnd()
    local memberInfoList = {}
    -- if bInGame then
    --     local playerState = Facade.GameFlowManager:GetPlayerState()
    --     memberInfoList = playerState and playerState.MemberInfoList or {}
    -- else
    --     local teamInfos = Server.TeamServer:GetTeamInfos()
    --     memberInfoList = teamInfos.Members or {}
    -- end
    local btnType = Module.GVoice:GetSpeakerButtonType()
    memberInfoList = Module.GVoice:GetTeamMemberList(btnType)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtTeamPlayerList)
    if Module.GVoice:IsCommanderGameMode() then -- 指挥官模式需要对玩家列表特殊过滤一下
        memberInfoList = self:RefreshTeamPlayerInCommander(memberInfoList)
    end
    --memberInfoList = self:RefreshTeamPlayerInCommander(memberInfoList)
    for _,memberInfo in pairs(memberInfoList) do
        local playerID = memberInfo.playerId or memberInfo.playerUin
        if not Server.TeamServer:IsMe(playerID) then
            local weakIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.SystemSettingHDPlayerVolumeItem, self._wtTeamPlayerList)
            local item = getfromweak(weakIns)
            item:Init(memberInfo)
        end
    end
end

function SystemSettingHDRadioPanel:RefreshTeamPlayerInCommander(memberInfoList)
    local memberList = {}
    for _,memberInfo in pairs(memberInfoList) do
        local playerID = memberInfo.playerId or memberInfo.playerUin
        if not Server.TeamServer:IsMe(playerID) then
            if Module.GVoice:GetTeamMemberName(playerID) or (memberInfo.teamIdentity and memberInfo.teamIdentity > 1 )  then --队伍成员与小队队长和指挥官都进入玩家列表
                table.insert(memberList, memberInfo)
            end

        end
    end
    return memberList
end



--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
function SystemSettingHDRadioPanel:_RegisterNavGroup()
    local wtScrollBox = self:Wnd("ScrollBox_206", UIWidgetBase)
	if not self._NavGroup then
	    self._NavGroup = WidgetUtil.RegisterNavigationGroup(wtScrollBox, self, "Hittest")
	end

	if self._NavGroup then
		self._NavGroup:AddNavWidgetToArray(wtScrollBox)
        self._NavGroup:SetScrollRecipient(wtScrollBox)
		WidgetUtil.TryFocusDefaultWidgetByGroup(self._NavGroup)
	end
end

function SystemSettingHDRadioPanel:_RemoveNavGroup()
	if self._NavGroup then
		self._NavGroup = nil
	end
	WidgetUtil.RemoveNavigationGroup(self)
end
--END MODIFICATION

return SystemSettingHDRadioPanel
