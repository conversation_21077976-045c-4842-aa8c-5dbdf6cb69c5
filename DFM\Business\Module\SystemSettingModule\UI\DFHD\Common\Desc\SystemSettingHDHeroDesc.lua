----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local HeroDataTable = Facade.TableManager:GetTable("Hero/HeroData")
local ArmedForceDataTable = Facade.TableManager:GetTable("ArmedForceData")

---@class SystemSettingHDHeroDesc
local SystemSettingHDBaseDesc = require "DFM.Business.Module.SystemSettingModule.UI.DFHD.Common.Desc.SystemSettingHDBaseDesc"
local SystemSettingHDHeroDesc = class("SystemSettingHDHeroDesc", SystemSettingHDBaseDesc)
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"

function SystemSettingHDHeroDesc:Ctor()
    self._wtNickName = self:Wnd("wtNickName", UITextBlock)
    self._wtArmedForceIcon = self:Wnd("wtArmedForceIcon", UIImage)
    self._wtNameOrDes = self:Wnd("wtNameOrDes", UITextBlock)
    self._wtHeadIcon = self:Wnd("wtHeadIcon", UIImage)
    self._wChangeHeroBtn = self:Wnd("wChangeHeroBtn", UIButton)
    self._wHasConfigTips = self:Wnd("DFCanvasPanel_HasConfig", UIWidgetBase)

    self._wChangeHeroBtn:Event("OnClicked", self._OnClickChangeHeroBtn, self)

    self._HeroListWidgetHandle = nil

    self._lastHeroId = nil

end

function SystemSettingHDHeroDesc:Init()
    self._lastHeroId = nil
    self:_OnChangeHero(Module.SystemSetting.Field:GetCurrentHero())
end

function SystemSettingHDHeroDesc:OnOpen()
    -- self:Init()
end

function SystemSettingHDHeroDesc:OnShow()
    self:Init()
    Module.SystemSetting.Field:SetHeroDesc(self)
end

function SystemSettingHDHeroDesc:_OnClickChangeHeroBtn()
    local function onSelectedHero(Id)
        Id = setdefault(Id, self._lastHeroId)
        self:_OnChangeHero(Id)
    end

    self.ParentWidget:SetRenderOpacity(0.1)
    self._HeroListWidgetHandle = Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDHeroListTopPanel, nil, nil, self, onSelectedHero)
end

function SystemSettingHDHeroDesc:_UpdateHasConfigTips()
    if SystemSettingLogic.HDIsHeroHasSetting(self._lastHeroId) then
        self._wHasConfigTips:SelfHitTestInvisible()
    else
        self._wHasConfigTips:Collapsed() -- Long3 12.10 讨论决定不要这个tips
        -- She3 7.28 常有玩家没注意自己的干员设置了专属按键，优化：在打开设置时，若当前干员有专属设置则默认修改当前干员，故这里还是显示tips
    end
end

function SystemSettingHDHeroDesc:_OnChangeHero(heroId)
    if heroId == nil then -- She3 7.28 常有玩家没注意自己的干员设置了专属按键，优化：在打开设置时，若当前干员有专属设置则默认修改当前干员
        heroId = SystemSettingLogic.HDGetCurHeroID()
        if not SystemSettingLogic.HDIsHeroHasSetting(heroId) then
            heroId = nil
        end
    end
    heroId = setdefault(heroId, "0")
    heroId = tostring(heroId)

    self.ParentWidget:SetRenderOpacity(1.0)

    if self._lastHeroId == heroId then
        return
    end
    self._lastHeroId = heroId
    Module.SystemSetting.Field:SetCurrentHero(heroId)

    SystemSettingLogic.HDRefreshCurSettingPanel(heroId)
    self:_UpdateHasConfigTips()

    if heroId == "0" then
        -- 全局配置
        self:BP_SetDefault()
        self._wtNickName:SetText(Module.SystemSetting.Config.Loc.HDSetting.HeroSpecificInput.GlobalNickName)
        self._wtNameOrDes:SetText(Module.SystemSetting.Config.Loc.HDSetting.HeroSpecificInput.GlobalTitle)
    else
        local heroInfo = HeroDataTable[heroId]
        if heroInfo then
            self._wtNickName:SetText(heroInfo.Name)
            self._wtNameOrDes:SetText(heroInfo.Title)
            self._wtHeadIcon:AsyncSetImagePath(heroInfo.Icon)
    
            local armedForceInfo = ArmedForceDataTable[tostring(heroInfo.armedForceId)]
            self._wtArmedForceIcon:AsyncSetImagePath(armedForceInfo.Icon)
            self._wtArmedForceIcon:SetColorAndOpacity(armedForceInfo.RedeployHeroViewColor)
            local textColor = Facade.ColorManager:GetSlateColorByRowName(armedForceInfo.RedeployHeroViewColorType)
            -- self._wtNameOrDes:SetColorAndOpacity(textColor) She3 8.8 均使用白色
        else
            logerror("SystemSettingHDHeroDesc:_OnChangeHero, heroId", heroId)
        end
    end
end

return SystemSettingHDHeroDesc
