require("DFM.Business.Proto.Pb.errcode_pb")
require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_room_editor_pb"
end

RoomState = {
RoomIdle = 0,
RoomInMatch = 1,
RoomWaitDS = 2,
}
RoomMemberState = {
RMIdle = 0,
RMReady = 1,
RMInMatch = 2,
RMFinish = 3,
}
RoomChangeType = {
RMemberJoin = 0,
RMemberQuit = 1,
RMemberKick = 2,
RMemberState = 3,
RChangeSeat = 4,
RChangeSetting = 5,
RChangeOwner = 6,
RChangeRoomState = 7,
RChangeEditMode = 8,
RChangeTeamName = 9,
RChangeTeamInfo = 10,
}
RoomTeamChangeType = {
RTeamNull = 0,
RTeamMantelBrickNum = 1,
RTeamHeroBPInfo = 2,
}
pb.__pb_RoomBaseInfo = {
    RoomID = 0,
    RoomName = "",
    State = 0,
    IsPublic = false,
    Password = "",
    AllowEditMode = false,
    EnableEditMode = false,
    OwnerPlayerID = 0,
    OwnerNickName = "",
    CurPlayerNum = 0,
    MaxPlayerNum = 0,
    TeamMemberSize = 0,
    TeamNum = 0,
    CurOBNum = 0,
    MaxOBNum = 0,
    CreateTime = 0,
    MatchSequence = 0,
}
pb.__pb_RoomBaseInfo.__name = "RoomBaseInfo"
pb.__pb_RoomBaseInfo.__index = pb.__pb_RoomBaseInfo
pb.__pb_RoomBaseInfo.__pairs = __pb_pairs

pb.RoomBaseInfo = { __name = "RoomBaseInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomBaseInfo : ProtoBase
---@field public RoomID number
---@field public RoomName string
---@field public Mode pb_MatchModeInfo
---@field public State number
---@field public IsPublic boolean
---@field public Password string
---@field public AllowEditMode boolean
---@field public EnableEditMode boolean
---@field public OwnerPlayerID number
---@field public OwnerNickName string
---@field public CurPlayerNum number
---@field public MaxPlayerNum number
---@field public TeamMemberSize number
---@field public TeamNum number
---@field public CurOBNum number
---@field public MaxOBNum number
---@field public CreateTime number
---@field public MatchSequence number

---@return pb_RoomBaseInfo
function pb.RoomBaseInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomTdmParam = {
    PlayerNum = 0,
    BeginVoteRatio = 0,
    ConquestSectorVoteRatio = 0,
    GameTimeRatio = 0,
    ReviveTimeRatio = 0,
}
pb.__pb_RoomTdmParam.__name = "RoomTdmParam"
pb.__pb_RoomTdmParam.__index = pb.__pb_RoomTdmParam
pb.__pb_RoomTdmParam.__pairs = __pb_pairs

pb.RoomTdmParam = { __name = "RoomTdmParam", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomTdmParam : ProtoBase
---@field public PlayerNum number
---@field public BeginVoteRatio number
---@field public ConquestSectorVoteRatio number
---@field public GameTimeRatio number
---@field public ReviveTimeRatio number

---@return pb_RoomTdmParam
function pb.RoomTdmParam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomParamKeyValue = {
    ParamID = 0,
    ParamValue = "",
    ParamType = 0,
}
pb.__pb_RoomParamKeyValue.__name = "RoomParamKeyValue"
pb.__pb_RoomParamKeyValue.__index = pb.__pb_RoomParamKeyValue
pb.__pb_RoomParamKeyValue.__pairs = __pb_pairs

pb.RoomParamKeyValue = { __name = "RoomParamKeyValue", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomParamKeyValue : ProtoBase
---@field public ParamID number
---@field public ParamValue string
---@field public ParamType number

---@return pb_RoomParamKeyValue
function pb.RoomParamKeyValue:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomParam = {
    TemplateID = 0,
}
pb.__pb_RoomParam.__name = "RoomParam"
pb.__pb_RoomParam.__index = pb.__pb_RoomParam
pb.__pb_RoomParam.__pairs = __pb_pairs

pb.RoomParam = { __name = "RoomParam", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomParam : ProtoBase
---@field public TemplateID number
---@field public ParamList pb_RoomParamKeyValue[]

---@return pb_RoomParam
function pb.RoomParam:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMemberInfo = {
    PlayerID = 0,
    SeatID = 0,
    TeamID = 0,
    TeamName = "",
    IsTeamLeader = false,
    IsObserver = false,
    ObserverSeatID = 0,
    State = 0,
    EnterTime = 0,
    TotalDepositPrice = 0,
    LastKillPlayerNum = 0,
    LastBluePrintSpecialId = 0,
    LoseStreakCnt = 0,
    IsSettle = false,
    PlatId = 0,
    OfflineTime = 0,
    TotalKillPlayerNum = 0,
    TotalAssistNum = 0,
}
pb.__pb_RoomMemberInfo.__name = "RoomMemberInfo"
pb.__pb_RoomMemberInfo.__index = pb.__pb_RoomMemberInfo
pb.__pb_RoomMemberInfo.__pairs = __pb_pairs

pb.RoomMemberInfo = { __name = "RoomMemberInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMemberInfo : ProtoBase
---@field public PlayerID number
---@field public SeatID number
---@field public TeamID number
---@field public TeamName string
---@field public IsTeamLeader boolean
---@field public IsObserver boolean
---@field public ObserverSeatID number
---@field public State number
---@field public EnterTime number
---@field public TotalDepositPrice number
---@field public LastKillPlayerNum number
---@field public LastBluePrintSpecialId number
---@field public LoseStreakCnt number
---@field public IsSettle boolean
---@field public PlatId number
---@field public OfflineTime number
---@field public TotalKillPlayerNum number
---@field public TotalAssistNum number

---@return pb_RoomMemberInfo
function pb.RoomMemberInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_HeroBanInfo = {
    HeroID = 0,
    ChoosedNum = 0,
    ChooseLimit = 0,
}
pb.__pb_HeroBanInfo.__name = "HeroBanInfo"
pb.__pb_HeroBanInfo.__index = pb.__pb_HeroBanInfo
pb.__pb_HeroBanInfo.__pairs = __pb_pairs

pb.HeroBanInfo = { __name = "HeroBanInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_HeroBanInfo : ProtoBase
---@field public HeroID number
---@field public ChoosedNum number
---@field public ChooseLimit number

---@return pb_HeroBanInfo
function pb.HeroBanInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMemberBattleInfo = {
    PlayerID = 0,
    TotalKillPlayerNum = 0,
    TotalAssistNum = 0,
}
pb.__pb_RoomMemberBattleInfo.__name = "RoomMemberBattleInfo"
pb.__pb_RoomMemberBattleInfo.__index = pb.__pb_RoomMemberBattleInfo
pb.__pb_RoomMemberBattleInfo.__pairs = __pb_pairs

pb.RoomMemberBattleInfo = { __name = "RoomMemberBattleInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMemberBattleInfo : ProtoBase
---@field public PlayerID number
---@field public TotalKillPlayerNum number
---@field public TotalAssistNum number

---@return pb_RoomMemberBattleInfo
function pb.RoomMemberBattleInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomTeamInfo = {
    TeamID = 0,
    TeamName = "",
    TotalDepositPrice = 0,
    MantelBrickNum = 0,
    LastManderlBrickRoomID = 0,
    SpawnPointType = 0,
}
pb.__pb_RoomTeamInfo.__name = "RoomTeamInfo"
pb.__pb_RoomTeamInfo.__index = pb.__pb_RoomTeamInfo
pb.__pb_RoomTeamInfo.__pairs = __pb_pairs

pb.RoomTeamInfo = { __name = "RoomTeamInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomTeamInfo : ProtoBase
---@field public TeamID number
---@field public TeamName string
---@field public TotalDepositPrice number
---@field public MantelBrickNum number
---@field public HeroBanInfoList pb_HeroBanInfo[]
---@field public LastManderlBrickRoomID number
---@field public SpawnPointType number

---@return pb_RoomTeamInfo
function pb.RoomTeamInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomInfo = {
}
pb.__pb_RoomInfo.__name = "RoomInfo"
pb.__pb_RoomInfo.__index = pb.__pb_RoomInfo
pb.__pb_RoomInfo.__pairs = __pb_pairs

pb.RoomInfo = { __name = "RoomInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomInfo : ProtoBase
---@field public Base pb_RoomBaseInfo
---@field public Players pb_RoomMemberInfo[]
---@field public Observers pb_RoomMemberInfo[]
---@field public TdmParam pb_RoomTdmParam
---@field public TeamInfo pb_RoomTeamInfo[]
---@field public Param pb_RoomParam

---@return pb_RoomInfo
function pb.RoomInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMatchRoundMemberInfo = {
    MatchSequence = 0,
    MatchStartTime = 0,
    TeamID = 0,
    SeatID = 0,
    IsObserver = false,
    ObserverSeatID = 0,
    KillPlayerNum = 0,
    GameResult = 0,
    BluePrintSpecialId = 0,
    Salary = 0,
    BrickBonus = 0,
    KillBonus = 0,
    FailKillBonus = 0,
    FailSupply = 0,
    SupplyTime = 0,
    TotalDepositPrice = 0,
}
pb.__pb_RoomMatchRoundMemberInfo.__name = "RoomMatchRoundMemberInfo"
pb.__pb_RoomMatchRoundMemberInfo.__index = pb.__pb_RoomMatchRoundMemberInfo
pb.__pb_RoomMatchRoundMemberInfo.__pairs = __pb_pairs

pb.RoomMatchRoundMemberInfo = { __name = "RoomMatchRoundMemberInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMatchRoundMemberInfo : ProtoBase
---@field public MatchSequence number
---@field public MatchStartTime number
---@field public TeamID number
---@field public SeatID number
---@field public IsObserver boolean
---@field public ObserverSeatID number
---@field public KillPlayerNum number
---@field public GameResult number
---@field public BluePrintSpecialId number
---@field public Salary number
---@field public BrickBonus number
---@field public KillBonus number
---@field public FailKillBonus number
---@field public FailSupply number
---@field public SupplyTime number
---@field public TotalDepositPrice number

---@return pb_RoomMatchRoundMemberInfo
function pb.RoomMatchRoundMemberInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMatchMemberInfo = {
    PlayerID = 0,
    TotalKillPlayerNum = 0,
    TotalAssistNum = 0,
}
pb.__pb_RoomMatchMemberInfo.__name = "RoomMatchMemberInfo"
pb.__pb_RoomMatchMemberInfo.__index = pb.__pb_RoomMatchMemberInfo
pb.__pb_RoomMatchMemberInfo.__pairs = __pb_pairs

pb.RoomMatchMemberInfo = { __name = "RoomMatchMemberInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMatchMemberInfo : ProtoBase
---@field public PlayerID number
---@field public RoundMemberInfo pb_RoomMatchRoundMemberInfo[]
---@field public TotalKillPlayerNum number
---@field public TotalAssistNum number

---@return pb_RoomMatchMemberInfo
function pb.RoomMatchMemberInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMatchRoundTeamInfo = {
    MatchSequence = 0,
    MatchStartTime = 0,
}
pb.__pb_RoomMatchRoundTeamInfo.__name = "RoomMatchRoundTeamInfo"
pb.__pb_RoomMatchRoundTeamInfo.__index = pb.__pb_RoomMatchRoundTeamInfo
pb.__pb_RoomMatchRoundTeamInfo.__pairs = __pb_pairs

pb.RoomMatchRoundTeamInfo = { __name = "RoomMatchRoundTeamInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMatchRoundTeamInfo : ProtoBase
---@field public MatchSequence number
---@field public MatchStartTime number
---@field public Players number[]

---@return pb_RoomMatchRoundTeamInfo
function pb.RoomMatchRoundTeamInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMatchTeamInfo = {
    TeamID = 0,
}
pb.__pb_RoomMatchTeamInfo.__name = "RoomMatchTeamInfo"
pb.__pb_RoomMatchTeamInfo.__index = pb.__pb_RoomMatchTeamInfo
pb.__pb_RoomMatchTeamInfo.__pairs = __pb_pairs

pb.RoomMatchTeamInfo = { __name = "RoomMatchTeamInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMatchTeamInfo : ProtoBase
---@field public TeamID number
---@field public RoundTeamInfo pb_RoomMatchRoundTeamInfo[]

---@return pb_RoomMatchTeamInfo
function pb.RoomMatchTeamInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomCreateReq = {
    RoomName = "",
    IsPublic = false,
    Password = "",
    AllowEditMode = false,
    MatchSequence = 0,
}
pb.__pb_CSRoomCreateReq.__name = "CSRoomCreateReq"
pb.__pb_CSRoomCreateReq.__index = pb.__pb_CSRoomCreateReq
pb.__pb_CSRoomCreateReq.__pairs = __pb_pairs

pb.CSRoomCreateReq = { __name = "CSRoomCreateReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomCreateReq : ProtoBase
---@field public RoomName string
---@field public IsPublic boolean
---@field public Password string
---@field public AllowEditMode boolean
---@field public Mode pb_MatchModeInfo
---@field public TdmParam pb_RoomTdmParam
---@field public MatchSequence number
---@field public Param pb_RoomParam

---@return pb_CSRoomCreateReq
function pb.CSRoomCreateReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomCreateRes = {
    result = 0,
    room_default_name = "",
}
pb.__pb_CSRoomCreateRes.__name = "CSRoomCreateRes"
pb.__pb_CSRoomCreateRes.__index = pb.__pb_CSRoomCreateRes
pb.__pb_CSRoomCreateRes.__pairs = __pb_pairs

pb.CSRoomCreateRes = { __name = "CSRoomCreateRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomCreateRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo
---@field public room_default_name string

---@return pb_CSRoomCreateRes
function pb.CSRoomCreateRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeSettingTReq = {
    RoomID = 0,
    RoomName = "",
    IsPublic = false,
    Password = "",
    AllowEditMode = false,
    MatchSequence = 0,
}
pb.__pb_CSRoomChangeSettingTReq.__name = "CSRoomChangeSettingTReq"
pb.__pb_CSRoomChangeSettingTReq.__index = pb.__pb_CSRoomChangeSettingTReq
pb.__pb_CSRoomChangeSettingTReq.__pairs = __pb_pairs

pb.CSRoomChangeSettingTReq = { __name = "CSRoomChangeSettingTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeSettingTReq : ProtoBase
---@field public RoomID number
---@field public RoomName string
---@field public IsPublic boolean
---@field public Password string
---@field public AllowEditMode boolean
---@field public Mode pb_MatchModeInfo
---@field public TdmParam pb_RoomTdmParam
---@field public MatchSequence number
---@field public Param pb_RoomParam

---@return pb_CSRoomChangeSettingTReq
function pb.CSRoomChangeSettingTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeSettingTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeSettingTRes.__name = "CSRoomChangeSettingTRes"
pb.__pb_CSRoomChangeSettingTRes.__index = pb.__pb_CSRoomChangeSettingTRes
pb.__pb_CSRoomChangeSettingTRes.__pairs = __pb_pairs

pb.CSRoomChangeSettingTRes = { __name = "CSRoomChangeSettingTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeSettingTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeSettingTRes
function pb.CSRoomChangeSettingTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomJoinTReq = {
    RoomID = 0,
    Password = "",
}
pb.__pb_CSRoomJoinTReq.__name = "CSRoomJoinTReq"
pb.__pb_CSRoomJoinTReq.__index = pb.__pb_CSRoomJoinTReq
pb.__pb_CSRoomJoinTReq.__pairs = __pb_pairs

pb.CSRoomJoinTReq = { __name = "CSRoomJoinTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomJoinTReq : ProtoBase
---@field public RoomID number
---@field public Password string

---@return pb_CSRoomJoinTReq
function pb.CSRoomJoinTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomJoinTRes = {
    result = 0,
}
pb.__pb_CSRoomJoinTRes.__name = "CSRoomJoinTRes"
pb.__pb_CSRoomJoinTRes.__index = pb.__pb_CSRoomJoinTRes
pb.__pb_CSRoomJoinTRes.__pairs = __pb_pairs

pb.CSRoomJoinTRes = { __name = "CSRoomJoinTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomJoinTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomJoinTRes
function pb.CSRoomJoinTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetInfoTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomGetInfoTReq.__name = "CSRoomGetInfoTReq"
pb.__pb_CSRoomGetInfoTReq.__index = pb.__pb_CSRoomGetInfoTReq
pb.__pb_CSRoomGetInfoTReq.__pairs = __pb_pairs

pb.CSRoomGetInfoTReq = { __name = "CSRoomGetInfoTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetInfoTReq : ProtoBase
---@field public RoomID number

---@return pb_CSRoomGetInfoTReq
function pb.CSRoomGetInfoTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetInfoTRes = {
    result = 0,
}
pb.__pb_CSRoomGetInfoTRes.__name = "CSRoomGetInfoTRes"
pb.__pb_CSRoomGetInfoTRes.__index = pb.__pb_CSRoomGetInfoTRes
pb.__pb_CSRoomGetInfoTRes.__pairs = __pb_pairs

pb.CSRoomGetInfoTRes = { __name = "CSRoomGetInfoTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetInfoTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomGetInfoTRes
function pb.CSRoomGetInfoTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetListReq = {
    GameMode = 0,
}
pb.__pb_CSRoomGetListReq.__name = "CSRoomGetListReq"
pb.__pb_CSRoomGetListReq.__index = pb.__pb_CSRoomGetListReq
pb.__pb_CSRoomGetListReq.__pairs = __pb_pairs

pb.CSRoomGetListReq = { __name = "CSRoomGetListReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetListReq : ProtoBase
---@field public GameMode number

---@return pb_CSRoomGetListReq
function pb.CSRoomGetListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetListRes = {
    result = 0,
}
pb.__pb_CSRoomGetListRes.__name = "CSRoomGetListRes"
pb.__pb_CSRoomGetListRes.__index = pb.__pb_CSRoomGetListRes
pb.__pb_CSRoomGetListRes.__pairs = __pb_pairs

pb.CSRoomGetListRes = { __name = "CSRoomGetListRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetListRes : ProtoBase
---@field public result number
---@field public RoomList pb_RoomBaseInfo[]

---@return pb_CSRoomGetListRes
function pb.CSRoomGetListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeReadyStateTReq = {
    RoomID = 0,
    IsReady = false,
}
pb.__pb_CSRoomChangeReadyStateTReq.__name = "CSRoomChangeReadyStateTReq"
pb.__pb_CSRoomChangeReadyStateTReq.__index = pb.__pb_CSRoomChangeReadyStateTReq
pb.__pb_CSRoomChangeReadyStateTReq.__pairs = __pb_pairs

pb.CSRoomChangeReadyStateTReq = { __name = "CSRoomChangeReadyStateTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeReadyStateTReq : ProtoBase
---@field public RoomID number
---@field public IsReady boolean

---@return pb_CSRoomChangeReadyStateTReq
function pb.CSRoomChangeReadyStateTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeReadyStateTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeReadyStateTRes.__name = "CSRoomChangeReadyStateTRes"
pb.__pb_CSRoomChangeReadyStateTRes.__index = pb.__pb_CSRoomChangeReadyStateTRes
pb.__pb_CSRoomChangeReadyStateTRes.__pairs = __pb_pairs

pb.CSRoomChangeReadyStateTRes = { __name = "CSRoomChangeReadyStateTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeReadyStateTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeReadyStateTRes
function pb.CSRoomChangeReadyStateTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeEditModeTReq = {
    RoomID = 0,
    EnableEditMode = false,
}
pb.__pb_CSRoomChangeEditModeTReq.__name = "CSRoomChangeEditModeTReq"
pb.__pb_CSRoomChangeEditModeTReq.__index = pb.__pb_CSRoomChangeEditModeTReq
pb.__pb_CSRoomChangeEditModeTReq.__pairs = __pb_pairs

pb.CSRoomChangeEditModeTReq = { __name = "CSRoomChangeEditModeTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeEditModeTReq : ProtoBase
---@field public RoomID number
---@field public EnableEditMode boolean

---@return pb_CSRoomChangeEditModeTReq
function pb.CSRoomChangeEditModeTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeEditModeTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeEditModeTRes.__name = "CSRoomChangeEditModeTRes"
pb.__pb_CSRoomChangeEditModeTRes.__index = pb.__pb_CSRoomChangeEditModeTRes
pb.__pb_CSRoomChangeEditModeTRes.__pairs = __pb_pairs

pb.CSRoomChangeEditModeTRes = { __name = "CSRoomChangeEditModeTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeEditModeTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeEditModeTRes
function pb.CSRoomChangeEditModeTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeCampTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomChangeCampTReq.__name = "CSRoomChangeCampTReq"
pb.__pb_CSRoomChangeCampTReq.__index = pb.__pb_CSRoomChangeCampTReq
pb.__pb_CSRoomChangeCampTReq.__pairs = __pb_pairs

pb.CSRoomChangeCampTReq = { __name = "CSRoomChangeCampTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeCampTReq : ProtoBase
---@field public RoomID number

---@return pb_CSRoomChangeCampTReq
function pb.CSRoomChangeCampTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeCampTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeCampTRes.__name = "CSRoomChangeCampTRes"
pb.__pb_CSRoomChangeCampTRes.__index = pb.__pb_CSRoomChangeCampTRes
pb.__pb_CSRoomChangeCampTRes.__pairs = __pb_pairs

pb.CSRoomChangeCampTRes = { __name = "CSRoomChangeCampTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeCampTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeCampTRes
function pb.CSRoomChangeCampTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomQuitTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomQuitTReq.__name = "CSRoomQuitTReq"
pb.__pb_CSRoomQuitTReq.__index = pb.__pb_CSRoomQuitTReq
pb.__pb_CSRoomQuitTReq.__pairs = __pb_pairs

pb.CSRoomQuitTReq = { __name = "CSRoomQuitTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomQuitTReq : ProtoBase
---@field public RoomID number

---@return pb_CSRoomQuitTReq
function pb.CSRoomQuitTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomQuitTRes = {
    result = 0,
}
pb.__pb_CSRoomQuitTRes.__name = "CSRoomQuitTRes"
pb.__pb_CSRoomQuitTRes.__index = pb.__pb_CSRoomQuitTRes
pb.__pb_CSRoomQuitTRes.__pairs = __pb_pairs

pb.CSRoomQuitTRes = { __name = "CSRoomQuitTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomQuitTRes : ProtoBase
---@field public result number

---@return pb_CSRoomQuitTRes
function pb.CSRoomQuitTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomKickMemberTReq = {
    RoomID = 0,
    KickPlayerID = 0,
}
pb.__pb_CSRoomKickMemberTReq.__name = "CSRoomKickMemberTReq"
pb.__pb_CSRoomKickMemberTReq.__index = pb.__pb_CSRoomKickMemberTReq
pb.__pb_CSRoomKickMemberTReq.__pairs = __pb_pairs

pb.CSRoomKickMemberTReq = { __name = "CSRoomKickMemberTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomKickMemberTReq : ProtoBase
---@field public RoomID number
---@field public KickPlayerID number

---@return pb_CSRoomKickMemberTReq
function pb.CSRoomKickMemberTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomKickMemberTRes = {
    result = 0,
}
pb.__pb_CSRoomKickMemberTRes.__name = "CSRoomKickMemberTRes"
pb.__pb_CSRoomKickMemberTRes.__index = pb.__pb_CSRoomKickMemberTRes
pb.__pb_CSRoomKickMemberTRes.__pairs = __pb_pairs

pb.CSRoomKickMemberTRes = { __name = "CSRoomKickMemberTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomKickMemberTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomKickMemberTRes
function pb.CSRoomKickMemberTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeSeatTReq = {
    RoomID = 0,
    FromSeatID = 0,
    IsFromObserverSeat = false,
    ToSeatID = 0,
    IsToObserverSeat = false,
}
pb.__pb_CSRoomChangeSeatTReq.__name = "CSRoomChangeSeatTReq"
pb.__pb_CSRoomChangeSeatTReq.__index = pb.__pb_CSRoomChangeSeatTReq
pb.__pb_CSRoomChangeSeatTReq.__pairs = __pb_pairs

pb.CSRoomChangeSeatTReq = { __name = "CSRoomChangeSeatTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeSeatTReq : ProtoBase
---@field public RoomID number
---@field public FromSeatID number
---@field public IsFromObserverSeat boolean
---@field public ToSeatID number
---@field public IsToObserverSeat boolean

---@return pb_CSRoomChangeSeatTReq
function pb.CSRoomChangeSeatTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeSeatTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeSeatTRes.__name = "CSRoomChangeSeatTRes"
pb.__pb_CSRoomChangeSeatTRes.__index = pb.__pb_CSRoomChangeSeatTRes
pb.__pb_CSRoomChangeSeatTRes.__pairs = __pb_pairs

pb.CSRoomChangeSeatTRes = { __name = "CSRoomChangeSeatTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeSeatTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeSeatTRes
function pb.CSRoomChangeSeatTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeOwnerTReq = {
    RoomID = 0,
    NewOWnerID = 0,
}
pb.__pb_CSRoomChangeOwnerTReq.__name = "CSRoomChangeOwnerTReq"
pb.__pb_CSRoomChangeOwnerTReq.__index = pb.__pb_CSRoomChangeOwnerTReq
pb.__pb_CSRoomChangeOwnerTReq.__pairs = __pb_pairs

pb.CSRoomChangeOwnerTReq = { __name = "CSRoomChangeOwnerTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeOwnerTReq : ProtoBase
---@field public RoomID number
---@field public NewOWnerID number

---@return pb_CSRoomChangeOwnerTReq
function pb.CSRoomChangeOwnerTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeOwnerTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeOwnerTRes.__name = "CSRoomChangeOwnerTRes"
pb.__pb_CSRoomChangeOwnerTRes.__index = pb.__pb_CSRoomChangeOwnerTRes
pb.__pb_CSRoomChangeOwnerTRes.__pairs = __pb_pairs

pb.CSRoomChangeOwnerTRes = { __name = "CSRoomChangeOwnerTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeOwnerTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeOwnerTRes
function pb.CSRoomChangeOwnerTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomInviteTReq = {
    RoomID = 0,
    InviteeID = 0,
    Source = 0,
}
pb.__pb_CSRoomInviteTReq.__name = "CSRoomInviteTReq"
pb.__pb_CSRoomInviteTReq.__index = pb.__pb_CSRoomInviteTReq
pb.__pb_CSRoomInviteTReq.__pairs = __pb_pairs

pb.CSRoomInviteTReq = { __name = "CSRoomInviteTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomInviteTReq : ProtoBase
---@field public RoomID number
---@field public InviteeID number
---@field public Source number

---@return pb_CSRoomInviteTReq
function pb.CSRoomInviteTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomInviteTRes = {
    result = 0,
}
pb.__pb_CSRoomInviteTRes.__name = "CSRoomInviteTRes"
pb.__pb_CSRoomInviteTRes.__index = pb.__pb_CSRoomInviteTRes
pb.__pb_CSRoomInviteTRes.__pairs = __pb_pairs

pb.CSRoomInviteTRes = { __name = "CSRoomInviteTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomInviteTRes : ProtoBase
---@field public result number

---@return pb_CSRoomInviteTRes
function pb.CSRoomInviteTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomResponseInviteTReq = {
    RoomID = 0,
    IsAgree = false,
    InviterID = 0,
    RefuseMessage = "",
}
pb.__pb_CSRoomResponseInviteTReq.__name = "CSRoomResponseInviteTReq"
pb.__pb_CSRoomResponseInviteTReq.__index = pb.__pb_CSRoomResponseInviteTReq
pb.__pb_CSRoomResponseInviteTReq.__pairs = __pb_pairs

pb.CSRoomResponseInviteTReq = { __name = "CSRoomResponseInviteTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomResponseInviteTReq : ProtoBase
---@field public RoomID number
---@field public IsAgree boolean
---@field public InviterID number
---@field public RefuseMessage string

---@return pb_CSRoomResponseInviteTReq
function pb.CSRoomResponseInviteTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomResponseInviteTRes = {
    result = 0,
}
pb.__pb_CSRoomResponseInviteTRes.__name = "CSRoomResponseInviteTRes"
pb.__pb_CSRoomResponseInviteTRes.__index = pb.__pb_CSRoomResponseInviteTRes
pb.__pb_CSRoomResponseInviteTRes.__pairs = __pb_pairs

pb.CSRoomResponseInviteTRes = { __name = "CSRoomResponseInviteTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomResponseInviteTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomResponseInviteTRes
function pb.CSRoomResponseInviteTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomApplyLeaderTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomApplyLeaderTReq.__name = "CSRoomApplyLeaderTReq"
pb.__pb_CSRoomApplyLeaderTReq.__index = pb.__pb_CSRoomApplyLeaderTReq
pb.__pb_CSRoomApplyLeaderTReq.__pairs = __pb_pairs

pb.CSRoomApplyLeaderTReq = { __name = "CSRoomApplyLeaderTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomApplyLeaderTReq : ProtoBase
---@field public RoomID number

---@return pb_CSRoomApplyLeaderTReq
function pb.CSRoomApplyLeaderTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomApplyLeaderTRes = {
    result = 0,
}
pb.__pb_CSRoomApplyLeaderTRes.__name = "CSRoomApplyLeaderTRes"
pb.__pb_CSRoomApplyLeaderTRes.__index = pb.__pb_CSRoomApplyLeaderTRes
pb.__pb_CSRoomApplyLeaderTRes.__pairs = __pb_pairs

pb.CSRoomApplyLeaderTRes = { __name = "CSRoomApplyLeaderTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomApplyLeaderTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomApplyLeaderTRes
function pb.CSRoomApplyLeaderTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomApplyLeaderResponseTReq = {
    RoomID = 0,
    ApplyPlayerID = 0,
    IsAgree = false,
}
pb.__pb_CSRoomApplyLeaderResponseTReq.__name = "CSRoomApplyLeaderResponseTReq"
pb.__pb_CSRoomApplyLeaderResponseTReq.__index = pb.__pb_CSRoomApplyLeaderResponseTReq
pb.__pb_CSRoomApplyLeaderResponseTReq.__pairs = __pb_pairs

pb.CSRoomApplyLeaderResponseTReq = { __name = "CSRoomApplyLeaderResponseTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomApplyLeaderResponseTReq : ProtoBase
---@field public RoomID number
---@field public ApplyPlayerID number
---@field public IsAgree boolean

---@return pb_CSRoomApplyLeaderResponseTReq
function pb.CSRoomApplyLeaderResponseTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomApplyLeaderResponseTRes = {
    result = 0,
}
pb.__pb_CSRoomApplyLeaderResponseTRes.__name = "CSRoomApplyLeaderResponseTRes"
pb.__pb_CSRoomApplyLeaderResponseTRes.__index = pb.__pb_CSRoomApplyLeaderResponseTRes
pb.__pb_CSRoomApplyLeaderResponseTRes.__pairs = __pb_pairs

pb.CSRoomApplyLeaderResponseTRes = { __name = "CSRoomApplyLeaderResponseTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomApplyLeaderResponseTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomApplyLeaderResponseTRes
function pb.CSRoomApplyLeaderResponseTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeLeaderTReq = {
    RoomID = 0,
    LeaderPlayerID = 0,
}
pb.__pb_CSRoomChangeLeaderTReq.__name = "CSRoomChangeLeaderTReq"
pb.__pb_CSRoomChangeLeaderTReq.__index = pb.__pb_CSRoomChangeLeaderTReq
pb.__pb_CSRoomChangeLeaderTReq.__pairs = __pb_pairs

pb.CSRoomChangeLeaderTReq = { __name = "CSRoomChangeLeaderTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeLeaderTReq : ProtoBase
---@field public RoomID number
---@field public LeaderPlayerID number

---@return pb_CSRoomChangeLeaderTReq
function pb.CSRoomChangeLeaderTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeLeaderTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeLeaderTRes.__name = "CSRoomChangeLeaderTRes"
pb.__pb_CSRoomChangeLeaderTRes.__index = pb.__pb_CSRoomChangeLeaderTRes
pb.__pb_CSRoomChangeLeaderTRes.__pairs = __pb_pairs

pb.CSRoomChangeLeaderTRes = { __name = "CSRoomChangeLeaderTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeLeaderTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeLeaderTRes
function pb.CSRoomChangeLeaderTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeTeamLeaderTReq = {
    RoomID = 0,
    TeamLeaderPlayerID = 0,
}
pb.__pb_CSRoomChangeTeamLeaderTReq.__name = "CSRoomChangeTeamLeaderTReq"
pb.__pb_CSRoomChangeTeamLeaderTReq.__index = pb.__pb_CSRoomChangeTeamLeaderTReq
pb.__pb_CSRoomChangeTeamLeaderTReq.__pairs = __pb_pairs

pb.CSRoomChangeTeamLeaderTReq = { __name = "CSRoomChangeTeamLeaderTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeTeamLeaderTReq : ProtoBase
---@field public RoomID number
---@field public TeamLeaderPlayerID number

---@return pb_CSRoomChangeTeamLeaderTReq
function pb.CSRoomChangeTeamLeaderTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeTeamLeaderTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeTeamLeaderTRes.__name = "CSRoomChangeTeamLeaderTRes"
pb.__pb_CSRoomChangeTeamLeaderTRes.__index = pb.__pb_CSRoomChangeTeamLeaderTRes
pb.__pb_CSRoomChangeTeamLeaderTRes.__pairs = __pb_pairs

pb.CSRoomChangeTeamLeaderTRes = { __name = "CSRoomChangeTeamLeaderTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeTeamLeaderTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeTeamLeaderTRes
function pb.CSRoomChangeTeamLeaderTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetMatchModeListReq = {
    game_mode = 0,
}
pb.__pb_CSRoomGetMatchModeListReq.__name = "CSRoomGetMatchModeListReq"
pb.__pb_CSRoomGetMatchModeListReq.__index = pb.__pb_CSRoomGetMatchModeListReq
pb.__pb_CSRoomGetMatchModeListReq.__pairs = __pb_pairs

pb.CSRoomGetMatchModeListReq = { __name = "CSRoomGetMatchModeListReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetMatchModeListReq : ProtoBase
---@field public game_mode number

---@return pb_CSRoomGetMatchModeListReq
function pb.CSRoomGetMatchModeListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_RoomMatchModeInfo = {
}
pb.__pb_RoomMatchModeInfo.__name = "RoomMatchModeInfo"
pb.__pb_RoomMatchModeInfo.__index = pb.__pb_RoomMatchModeInfo
pb.__pb_RoomMatchModeInfo.__pairs = __pb_pairs

pb.RoomMatchModeInfo = { __name = "RoomMatchModeInfo", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_RoomMatchModeInfo : ProtoBase
---@field public mode_info pb_MatchModeInfo
---@field public param_template_list number[]

---@return pb_RoomMatchModeInfo
function pb.RoomMatchModeInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetMatchModeListRes = {
    result = 0,
}
pb.__pb_CSRoomGetMatchModeListRes.__name = "CSRoomGetMatchModeListRes"
pb.__pb_CSRoomGetMatchModeListRes.__index = pb.__pb_CSRoomGetMatchModeListRes
pb.__pb_CSRoomGetMatchModeListRes.__pairs = __pb_pairs

pb.CSRoomGetMatchModeListRes = { __name = "CSRoomGetMatchModeListRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetMatchModeListRes : ProtoBase
---@field public result number
---@field public mode_info_array pb_MatchModeInfo[]
---@field public mode_info_list pb_RoomMatchModeInfo[]

---@return pb_CSRoomGetMatchModeListRes
function pb.CSRoomGetMatchModeListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomBeginMatchTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomBeginMatchTReq.__name = "CSRoomBeginMatchTReq"
pb.__pb_CSRoomBeginMatchTReq.__index = pb.__pb_CSRoomBeginMatchTReq
pb.__pb_CSRoomBeginMatchTReq.__pairs = __pb_pairs

pb.CSRoomBeginMatchTReq = { __name = "CSRoomBeginMatchTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomBeginMatchTReq : ProtoBase
---@field public RoomID number

---@return pb_CSRoomBeginMatchTReq
function pb.CSRoomBeginMatchTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomBeginMatchTRes = {
    result = 0,
}
pb.__pb_CSRoomBeginMatchTRes.__name = "CSRoomBeginMatchTRes"
pb.__pb_CSRoomBeginMatchTRes.__index = pb.__pb_CSRoomBeginMatchTRes
pb.__pb_CSRoomBeginMatchTRes.__pairs = __pb_pairs

pb.CSRoomBeginMatchTRes = { __name = "CSRoomBeginMatchTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomBeginMatchTRes : ProtoBase
---@field public result number

---@return pb_CSRoomBeginMatchTRes
function pb.CSRoomBeginMatchTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeNtf = {
    RoomID = 0,
    Type = 0,
}
pb.__pb_CSRoomChangeNtf.__name = "CSRoomChangeNtf"
pb.__pb_CSRoomChangeNtf.__index = pb.__pb_CSRoomChangeNtf
pb.__pb_CSRoomChangeNtf.__pairs = __pb_pairs

pb.CSRoomChangeNtf = { __name = "CSRoomChangeNtf", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeNtf : ProtoBase
---@field public RoomID number
---@field public Type number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeNtf
function pb.CSRoomChangeNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomResponseInviteNtf = {
    RoomID = 0,
    IsAgree = false,
    RefuseMessage = "",
}
pb.__pb_CSRoomResponseInviteNtf.__name = "CSRoomResponseInviteNtf"
pb.__pb_CSRoomResponseInviteNtf.__index = pb.__pb_CSRoomResponseInviteNtf
pb.__pb_CSRoomResponseInviteNtf.__pairs = __pb_pairs

pb.CSRoomResponseInviteNtf = { __name = "CSRoomResponseInviteNtf", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomResponseInviteNtf : ProtoBase
---@field public RoomID number
---@field public IsAgree boolean
---@field public RefuseMessage string

---@return pb_CSRoomResponseInviteNtf
function pb.CSRoomResponseInviteNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomBeInvitedTipsNtf = {
    RoomID = 0,
    Source = 0,
}
pb.__pb_CSRoomBeInvitedTipsNtf.__name = "CSRoomBeInvitedTipsNtf"
pb.__pb_CSRoomBeInvitedTipsNtf.__index = pb.__pb_CSRoomBeInvitedTipsNtf
pb.__pb_CSRoomBeInvitedTipsNtf.__pairs = __pb_pairs

pb.CSRoomBeInvitedTipsNtf = { __name = "CSRoomBeInvitedTipsNtf", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomBeInvitedTipsNtf : ProtoBase
---@field public RoomID number
---@field public Inviter pb_PlayerSimpleInfo
---@field public Source number

---@return pb_CSRoomBeInvitedTipsNtf
function pb.CSRoomBeInvitedTipsNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomApplyLeaderNtf = {
    RoomID = 0,
    ApplyPlayerID = 0,
}
pb.__pb_CSRoomApplyLeaderNtf.__name = "CSRoomApplyLeaderNtf"
pb.__pb_CSRoomApplyLeaderNtf.__index = pb.__pb_CSRoomApplyLeaderNtf
pb.__pb_CSRoomApplyLeaderNtf.__pairs = __pb_pairs

pb.CSRoomApplyLeaderNtf = { __name = "CSRoomApplyLeaderNtf", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomApplyLeaderNtf : ProtoBase
---@field public RoomID number
---@field public ApplyPlayerID number

---@return pb_CSRoomApplyLeaderNtf
function pb.CSRoomApplyLeaderNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomApplyLeaderResponseNtf = {
    RoomID = 0,
    IsAgree = false,
}
pb.__pb_CSRoomApplyLeaderResponseNtf.__name = "CSRoomApplyLeaderResponseNtf"
pb.__pb_CSRoomApplyLeaderResponseNtf.__index = pb.__pb_CSRoomApplyLeaderResponseNtf
pb.__pb_CSRoomApplyLeaderResponseNtf.__pairs = __pb_pairs

pb.CSRoomApplyLeaderResponseNtf = { __name = "CSRoomApplyLeaderResponseNtf", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomApplyLeaderResponseNtf : ProtoBase
---@field public RoomID number
---@field public IsAgree boolean

---@return pb_CSRoomApplyLeaderResponseNtf
function pb.CSRoomApplyLeaderResponseNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomKickMemberNtf = {
    KickedPlayerID = 0,
}
pb.__pb_CSRoomKickMemberNtf.__name = "CSRoomKickMemberNtf"
pb.__pb_CSRoomKickMemberNtf.__index = pb.__pb_CSRoomKickMemberNtf
pb.__pb_CSRoomKickMemberNtf.__pairs = __pb_pairs

pb.CSRoomKickMemberNtf = { __name = "CSRoomKickMemberNtf", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomKickMemberNtf : ProtoBase
---@field public KickedPlayerID number

---@return pb_CSRoomKickMemberNtf
function pb.CSRoomKickMemberNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeTeamNameTReq = {
    RoomID = 0,
    TeamID = 0,
    TeamName = "",
}
pb.__pb_CSRoomChangeTeamNameTReq.__name = "CSRoomChangeTeamNameTReq"
pb.__pb_CSRoomChangeTeamNameTReq.__index = pb.__pb_CSRoomChangeTeamNameTReq
pb.__pb_CSRoomChangeTeamNameTReq.__pairs = __pb_pairs

pb.CSRoomChangeTeamNameTReq = { __name = "CSRoomChangeTeamNameTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeTeamNameTReq : ProtoBase
---@field public RoomID number
---@field public TeamID number
---@field public TeamName string

---@return pb_CSRoomChangeTeamNameTReq
function pb.CSRoomChangeTeamNameTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeTeamNameTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeTeamNameTRes.__name = "CSRoomChangeTeamNameTRes"
pb.__pb_CSRoomChangeTeamNameTRes.__index = pb.__pb_CSRoomChangeTeamNameTRes
pb.__pb_CSRoomChangeTeamNameTRes.__pairs = __pb_pairs

pb.CSRoomChangeTeamNameTRes = { __name = "CSRoomChangeTeamNameTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeTeamNameTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeTeamNameTRes
function pb.CSRoomChangeTeamNameTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomBatchChangeTeamNameTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomBatchChangeTeamNameTReq.__name = "CSRoomBatchChangeTeamNameTReq"
pb.__pb_CSRoomBatchChangeTeamNameTReq.__index = pb.__pb_CSRoomBatchChangeTeamNameTReq
pb.__pb_CSRoomBatchChangeTeamNameTReq.__pairs = __pb_pairs

pb.CSRoomBatchChangeTeamNameTReq = { __name = "CSRoomBatchChangeTeamNameTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomBatchChangeTeamNameTReq : ProtoBase
---@field public RoomID number
---@field public TeamInfos pb_RoomTeamInfo[]

---@return pb_CSRoomBatchChangeTeamNameTReq
function pb.CSRoomBatchChangeTeamNameTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomBatchChangeTeamNameTRes = {
    result = 0,
}
pb.__pb_CSRoomBatchChangeTeamNameTRes.__name = "CSRoomBatchChangeTeamNameTRes"
pb.__pb_CSRoomBatchChangeTeamNameTRes.__index = pb.__pb_CSRoomBatchChangeTeamNameTRes
pb.__pb_CSRoomBatchChangeTeamNameTRes.__pairs = __pb_pairs

pb.CSRoomBatchChangeTeamNameTRes = { __name = "CSRoomBatchChangeTeamNameTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomBatchChangeTeamNameTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomBatchChangeTeamNameTRes
function pb.CSRoomBatchChangeTeamNameTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeTeamInfoTReq = {
    RoomID = 0,
    TeamID = 0,
    ChangeType = 0,
}
pb.__pb_CSRoomChangeTeamInfoTReq.__name = "CSRoomChangeTeamInfoTReq"
pb.__pb_CSRoomChangeTeamInfoTReq.__index = pb.__pb_CSRoomChangeTeamInfoTReq
pb.__pb_CSRoomChangeTeamInfoTReq.__pairs = __pb_pairs

pb.CSRoomChangeTeamInfoTReq = { __name = "CSRoomChangeTeamInfoTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeTeamInfoTReq : ProtoBase
---@field public RoomID number
---@field public TeamID number
---@field public ChangeType number
---@field public TeamInfo pb_RoomTeamInfo
---@field public MemberBattles pb_RoomMemberBattleInfo[]

---@return pb_CSRoomChangeTeamInfoTReq
function pb.CSRoomChangeTeamInfoTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomChangeTeamInfoTRes = {
    result = 0,
}
pb.__pb_CSRoomChangeTeamInfoTRes.__name = "CSRoomChangeTeamInfoTRes"
pb.__pb_CSRoomChangeTeamInfoTRes.__index = pb.__pb_CSRoomChangeTeamInfoTRes
pb.__pb_CSRoomChangeTeamInfoTRes.__pairs = __pb_pairs

pb.CSRoomChangeTeamInfoTRes = { __name = "CSRoomChangeTeamInfoTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomChangeTeamInfoTRes : ProtoBase
---@field public result number
---@field public Info pb_RoomInfo

---@return pb_CSRoomChangeTeamInfoTRes
function pb.CSRoomChangeTeamInfoTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetBaseInfoTReq = {
    RoomID = 0,
}
pb.__pb_CSRoomGetBaseInfoTReq.__name = "CSRoomGetBaseInfoTReq"
pb.__pb_CSRoomGetBaseInfoTReq.__index = pb.__pb_CSRoomGetBaseInfoTReq
pb.__pb_CSRoomGetBaseInfoTReq.__pairs = __pb_pairs

pb.CSRoomGetBaseInfoTReq = { __name = "CSRoomGetBaseInfoTReq", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetBaseInfoTReq : ProtoBase
---@field public RoomID number

---@return pb_CSRoomGetBaseInfoTReq
function pb.CSRoomGetBaseInfoTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSRoomGetBaseInfoTRes = {
    result = 0,
}
pb.__pb_CSRoomGetBaseInfoTRes.__name = "CSRoomGetBaseInfoTRes"
pb.__pb_CSRoomGetBaseInfoTRes.__index = pb.__pb_CSRoomGetBaseInfoTRes
pb.__pb_CSRoomGetBaseInfoTRes.__pairs = __pb_pairs

pb.CSRoomGetBaseInfoTRes = { __name = "CSRoomGetBaseInfoTRes", __service="room", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSRoomGetBaseInfoTRes : ProtoBase
---@field public result number
---@field public Base pb_RoomBaseInfo

---@return pb_CSRoomGetBaseInfoTRes
function pb.CSRoomGetBaseInfoTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


