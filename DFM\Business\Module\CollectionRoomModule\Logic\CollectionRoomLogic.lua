----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollectionRoom)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionRoomConfig = require "DFM.Business.Module.CollectionRoomModule.CollectionRoomConfig"
local InGameController = require "DFM.YxFramework.Managers.GameFlow.InGameControl.InGameController"
local ItemHelperTool   = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ItemOperaTool      = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemConfigTool     = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local CommonBarHDTabGroupLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarHDTabGroupLogic2"
local UCharacterMovementComponent = import "CharacterMovementComponent"
local EViewTargetBlendFunction = import "EViewTargetBlendFunction"
local EProcessInputCompReason = import "EProcessInputCompReason"
local FEquipmentInfo = import "EquipmentInfo"
local ESafeHouseHUDMode = import "ESafeHouseHUDMode"
local UHUDStateManager = import "HUDStateManager"
local UGPQuestUtils = import "GPQuestUtils"
local UCollectionRoomPickupActorManager = import "CollectionRoomPickupActorManager"
local UGameplayStatics = import "GameplayStatics"
local AHallCollectionRoomDisplayCtrl = import "HallCollectionRoomDisplayCtrl"
local APickupManager = import "PickupManager"
local EAttachmentRule = import "EAttachmentRule"
local GameHUDState = import "GameHUDSate"
local UKismetMathLibrary = import "KismetMathLibrary"
local FHitResult = import "HitResult"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local UCollectionRoomConstantWrap = import "CollectionRoomConstantWrap"
local UCollectionRoomLimitedCollectionWrap = import "CollectionRoomLimitedCollectionWrap"
local UPickupStaticMeshComponent = import "PickupStaticMeshComponent"
local USkeletalMeshComponent = import "SkeletalMeshComponent"
local FFrameTime = import "FrameTime"
local UExponentialHeightFogComponent = import "ExponentialHeightFogComponent"
local USafeHouseGameplayUtils = import "SafeHouseGameplayUtils"
local UGPAudioStatics = import "GPAudioStatics"
local EGameAkType = import "EGameAkType"
local EGPAudioWaterPreset = import "EGPAudioWaterPreset"
local FCommercializationItemInfo = import "CommercializationItemInfo"
local ABaseHUD = import "BaseHUD"
local UGameplayBlueprintHelper = import "GameplayBlueprintHelper"
local UCharacterEquipComponent = import "CharacterEquipComponent"
local UWeaponManagerComponent = import "WeaponManagerComponent"
local UInventoryManager = import "InventoryManager"
local ESwitchWeaponContext = import "ESwitchWeaponContext"
local EWeaponSwitchType = import "EWeaponSwitchType"
local AInventoryPickup = import "InventoryPickup"
local UItemIDUtil = import "ItemIDUtil"
local DFMGameDataKeyForStoryState = import "DFMGameDataKeyForStoryState"
local UDFMGameGPM = import "DFMGameGPM"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local UGPUserInterfaceUtil = import "GPUserInterfaceUtil"

local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"
local UGPGameplayDelegates = import "GPGameplayDelegates"
local MemoryUtil = require "DFM.YxFramework.Util.MemoryUtil"
local UHardwareParamHelper = import "HardwareParamHelper"

bEnableCollectionRoomUILifeLog = true
function PrintCollectionRoomUILifeLog(level, ...)
    if bEnableCollectionRoomUILifeLog then
        _ENV["log" .. level]("[CollectionRoomUILife]", ...)
    end
end

---@return CollectionRoomField
local function getField()
    return Module.CollectionRoom.Field
end

---@class CollectionRoomLogic
local CollectionRoomLogic = {}
CollectionRoomLogic.DEFAULT_LOAD_TIMEOUT = 5
CollectionRoomLogic.ENABLE_FRAMING_SPAWN_PICKUP = true
CollectionRoomLogic.ENABLE_FRAMING_DESTROY_PICKUP = true
local defaultCameraDistance = 100
local HitResult = FHitResult()

CollectionRoomLogic.WEAPON_SLOT_TYPES = {
    ESlotType.MainWeaponLeft,
    ESlotType.MainWeaponRight,
    ESlotType.Pistrol,
    ESlotType.MeleeWeapon
}

ECollectionRoomEnterFrom = {
    Interactor = 1,
    BlackSite = 2,
    MainPanel = 3,
    EscFromMainPanel = 4,
}

ECollectionRoomLeaveFrom = {
    None = 0,
    Setting = 1,
    Interactor = 2,
    EnterMainPanel = 3,
    Match = 4,
    EnterSafeHouse = 5,
    EnterRange = 6,
}

ECollectionRoomMainPanelEnterFrom = {
    BlackSite = 1,
    Deposit = 2,
    Interactor = 3,
    EscFromScene = 4,
}

---------------------------------------------------------------------------------
--- Logic可以拆分多个，用于业务逻辑的编写，部分需要开放的接口由Module进行转发
---------------------------------------------------------------------------------
--- 可以仅模块内部调用，也可以在Module中被公开

function FocusDisplayOrSpecialCabinetGrid(cabinetType, gridId)
    return CollectionRoomConfig.Events.evtFocusDisplayOrSpecialCabinetGrid:Invoke(cabinetType, gridId)
end

function CollectionRoomLogic.CallCtrlFunction(funcName, ...)
    if bCollectionRoomEnableVerboseLog then
        loginfo("CollectionRoomLogic.CallCtrlFunction", funcName, ...)
    end
    if not funcName then
        return
    end
    local sceneCtrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.CollectionRoom)
    return Facade.HallSceneManager:CallSceneCtrlFunction(sceneCtrl,funcName,...)
end

function CollectionRoomLogic.GetCtrlProperty(propertyName)
    loginfo("CollectionRoomLogic.GetCtrlProperty", propertyName)
    if not propertyName then
        return
    end
    local Ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.CollectionRoom)
    return Ctrl and Ctrl[propertyName]
end

---@param pageId number 默认打开的页签ID，1=收藏室，2=图鉴，默认值是1
function CollectionRoomLogic.ShowMainPanel(pageId, enterMethod, cameraId)
    loginfo("CollectionRoomLogic.ShowMainPanel", pageId, enterMethod, cameraId)
    if not CollectionRoomLogic.CheckCollectionRoomPakDownload() then
        loginfo("ShowMainPanel collectionroom pak not download, don't enter.")
        return
    end
    local function showMainPanel()
        getField().enterMainPanelMethod = enterMethod
        local function fLevelsLoadedCallback()
            Facade.GameFlowManager:EnterSubStage(ESubStage.CollectionRoom)
            --- 这里不允许直接捕获...作为上值，require会报错
            Module.CollectionRoom.Field.mainPanelTargetCameraId = cameraId or 2
            Facade.UIManager:AsyncShowUI(UIName2ID.CollectionRoomMainPanel, nil, nil, pageId)
        end
        CollectionRoomLogic.LoadCollectionRoomLevels(fLevelsLoadedCallback)
    end
    if ItemOperaTool.bIsInCollectionRoom then
        local CollectionRoomPickupActorManager = UCollectionRoomPickupActorManager.Get(GetWorld())
        if CollectionRoomPickupActorManager:HasPickupActorsNeedReset() then
            Module.CommonTips:ShowConfirmWindow(
                    CollectionRoomConfig.Loc.ManageCollectionResetWarning,
                    function()
                        CollectionRoomLogic.ResetAllPickupActors()
                        showMainPanel()
                    end,
                    nil,
                    CollectionRoomConfig.Loc.Cancel,
                    CollectionRoomConfig.Loc.Confirm
            )
            return
        end
    end
    showMainPanel()
end

function CollectionRoomLogic.EnterCollectionRoom(enterFrom, extraParams)
    loginfo("CollectionRoomLogic.EnterCollectionRoom", enterFrom)
    if ItemOperaTool.bIsInCollectionRoom then
        Facade.UIManager:CloseAllPopUI()
        Facade.UIManager:PopAllUI(true, true, MapPopAllUIReason2Str.EnterCollectionRoom)
        return
    end

    if not CollectionRoomLogic.CheckCollectionRoomPakDownload() then
		loginfo("CollectionRoomLogic.EnterCollectionRoom collectionroom pak not download, don't enter.")
		return
	end
    ItemOperaTool.AddUseCharacterBusiness(ItemOperaTool.SafeHouseBusiness.CollectionRoom)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if isvalid(localCtrl) then
        if not localCtrl.bHasSpawnCharacter then
            loginfo("CollectionRoomLogic.EnterCollectionRoom no character, need to spawn")
            localCtrl:SpawnDefaultCharacter()
            CollectionRoomLogic._InitFashion()
            local dfmCharacter = Facade.GameFlowManager:GetCharacter()
            if isvalid(dfmCharacter) and dfmCharacter.OnFppMeshLoadSuccess then
                dfmCharacter.OnFppMeshLoadSuccess:Add(CollectionRoomLogic._OnFppMeshLoadSuccess)
            end
            getField().loadingCharacterMeshTimer = Timer.DelayCall(5, CollectionRoomLogic._OnFppMeshLoadSuccess, nil, true)
        end
    else
        logerror("CollectionRoomLogic.EnterCollectionRoom localCtrl is invalid")
    end
    local function fLevelsLoadedCallback()
        Facade.GameFlowManager:EnterSubStage(ESubStage.CollectionRoom)
        getField().enterFrom = enterFrom
        getField().extraParams = extraParams
        if getField().enterCollectionRoomMethod == nil then
            getField().enterCollectionRoomMethod = enterFrom
            getField().enterCollectionRoomTime = os.date("%Y-%m-%d %H:%M:%S")
        end
        CollectionRoomLogic._OnEnterCollectionRoom()
    end
    CollectionRoomLogic.LoadCollectionRoomLevels(fLevelsLoadedCallback, true)
end

function CollectionRoomLogic.LeaveCollectionRoom(leaveFrom, bForce)
    bForce = setdefault(bForce, false)
    loginfo("CollectionRoomLogic.LeaveCollectionRoom", leaveFrom, bForce)
    if not ItemOperaTool.bIsInCollectionRoom then
        return
    end
    if bForce then
        logerror("CollectionRoomLogic.LeaveCollectionRoom", leaveFrom, bForce)
    end
    local function func()
        CollectionRoomLogic._OnLeaveCollectionRoom(leaveFrom)
    end
    if not bForce and UCollectionRoomPickupActorManager.Get(GetWorld()):HasPickupActorsNeedReset() then
        Module.CommonTips:ShowConfirmWindow(
            CollectionRoomConfig.Loc.LeaveCollectionRoomResetWarning,
            func,
            nil,
            CollectionRoomConfig.Loc.Cancel,
            CollectionRoomConfig.Loc.Confirm
        )
    else
        func()
    end
end

function CollectionRoomLogic._BindEvents()
    loginfo("CollectionRoomLogic._BindEvents")
    Facade.UIManager.Events.evtStackUIChanged:AddListener(CollectionRoomLogic._OnStackUIChanged)
    Server.CollectionRoomServer.Events.evtCollectionRoomCabinetGridChange:AddListener(CollectionRoomLogic._OnCollectionRoomCabinetGridChange)
    Server.MatchServer.Events.evtMatchStateChanged:AddListener(CollectionRoomLogic._OnMatchStateChanged)
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPickupActorMeshCreated:Add(CollectionRoomLogic._OnPickupActorMeshCreated)
    LuaGlobalEvents.evtStageChangeEnterBefore:AddListener(CollectionRoomLogic._OnSubStageChangeEnterBefore)
    Module.BlackSite.Config.Events.evtDeviceUpgradeSuccess:AddListener(CollectionRoomLogic._OnDeviceUpgradeSuccess)
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).ItemOwnerChange:Add(CollectionRoomLogic._OnItemOwnerChange)
    Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange:AddListener(CollectionRoomLogic._OnMusicPlayerPlayStateChange)
end

function CollectionRoomLogic._UnBindEvents()
    loginfo("CollectionRoomLogic._UnBindEvents")
    Facade.UIManager.Events.evtStackUIChanged:RemoveListener(CollectionRoomLogic._OnStackUIChanged)
    Server.CollectionRoomServer.Events.evtCollectionRoomCabinetGridChange:RemoveListener(CollectionRoomLogic._OnCollectionRoomCabinetGridChange)
    Server.MatchServer.Events.evtMatchStateChanged:RemoveListener(CollectionRoomLogic._OnMatchStateChanged)
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).OnPickupActorMeshCreated:Remove(CollectionRoomLogic._OnPickupActorMeshCreated)
    LuaGlobalEvents.evtStageChangeEnterBefore:RemoveListener(CollectionRoomLogic._OnSubStageChangeEnterBefore)
    UDFMGameplayGlobalDelegates.Get(GetGameInstance()).ItemOwnerChange:Remove(CollectionRoomLogic._OnItemOwnerChange)
    Module.MusicPlayer.Config.Events.evtOnMusicPlayerPlayStateChange:RemoveListener(CollectionRoomLogic._OnMusicPlayerPlayStateChange)
end

function CollectionRoomLogic._OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.SafeHouse then
        getField():LoadDataTable()
        CollectionRoomLogic._BindEvents()
        if IsHD() and not isvalid(getField().lightSeqActor) then
            local lightSeqActorClass = ULuaExtension.LoadClassByName(CollectionRoomConfig.LightSeqPath)
            getField().lightSeqActor = GetWorld():SpawnActor(lightSeqActorClass, FVector.ZeroVector, FRotator.ZeroRotator)
        end
    end
end

function CollectionRoomLogic._OnGameFlowChangeLeave(gameFlowType)
    if gameFlowType == EGameFlowStageType.SafeHouse then
        CollectionRoomLogic._UnBindEvents()
    end
end

function CollectionRoomLogic._OnSubStageChangeEnterBefore(curSubStageType)
    loginfo("CollectionRoomLogic._OnSubStageChangeEnterBefore", curSubStageType)
    if ItemOperaTool.bIsInCollectionRoom then
        if curSubStageType == ESubStage.SafeHouse3D then
            CollectionRoomLogic.LeaveCollectionRoom(ECollectionRoomLeaveFrom.EnterSafeHouse, true)
        end
        if curSubStageType == ESubStage.Range then
            CollectionRoomLogic.LeaveCollectionRoom(ECollectionRoomLeaveFrom.EnterRange, true)
        end
        if curSubStageType == ESubStage.HallMatch then
            CollectionRoomLogic.LeaveCollectionRoom(ECollectionRoomLeaveFrom.Match, true)
        end
    end
end

function CollectionRoomLogic._OnSubStageChangeEnter(curSubStageType)
    loginfo("CollectionRoomLogic._OnSubStageChangeEnter", curSubStageType)
    if curSubStageType == ESubStage.CollectionRoom then
        CollectionRoomLogic._OnEnterCollectionRoomSubStage()
    end
end

function CollectionRoomLogic._OnEnterCollectionRoomSubStage()
    loginfo("CollectionRoomLogic._OnEnterCollectionRoomSubStage")
    CollectionRoomLogic._OnStateChange(true, nil, nil)
    if CollectionRoomConfig.bUsePauseAnimSolution then
        CollectionRoomLogic.TryReplayRobotArmAnim()
    end
end

function CollectionRoomLogic._OnSubStageChangeLeave(preSubStageType)
    loginfo("CollectionRoomLogic._OnSubStageChangeLeave", preSubStageType)
    if preSubStageType == ESubStage.CollectionRoom then
        CollectionRoomLogic._OnLeaveCollectionRoomSubStage()
    elseif preSubStageType == ESubStage.CollectionLibrary then
        --CollectionRoomLogic.DestroyLimitedPickupActor()
    end
end

function CollectionRoomLogic._OnLeaveCollectionRoomSubStage()
    loginfo("CollectionRoomLogic._OnLeaveCollectionRoomSubStage")
    CollectionRoomLogic._OnStateChange(false, nil, nil)
    if not ItemOperaTool.bIsInCollectionRoom and Facade.UIManager:GetStackUICount() > 0 then
        if CollectionRoomConfig.bUsePauseAnimSolution then
            CollectionRoomLogic.PauseRobotArmAnim()
        else
            CollectionRoomLogic.SetViewTargetToNamedCamera("AvoidRobotArmSound", false, true)
        end
    end
    CollectionRoomLogic.CallCtrlFunction("ResetDisplayType")
end

function CollectionRoomLogic._OnLoadCollectionRoomLevel()
    loginfo("CollectionRoomLogic._OnLoadCollectionRoomLevel")
    CollectionRoomLogic.SpawnAllPickupActor()
    CollectionRoomLogic.SetDisplayCabinet()
    if getField().bIsFirstLoadLevel then
        -- 第一次加载上来一定要强制设置，避免使用默认姿态
        CollectionRoomLogic._SetSpecialCabinet(true)
    end
    getField().bIsFirstLoadLevel = false
end

function CollectionRoomLogic._OnDeviceUpgradeSuccess(deviceId)
    if deviceId == EBlackSiteDeviceName2Id.CollectionRoom then
        Server.CollectionRoomServer:FetchServerData()
        Server.CollectionRoomServer.Events.evtIsClickedReddot:Invoke()
    end
end

function CollectionRoomLogic._OnItemOwnerChange(itemGid, oldOwner, newOwner)
    if ItemOperaTool.bIsInCollectionRoom then
        if oldOwner and issubclass(LAI.GetObjectClass(oldOwner), AInventoryPickup) and oldOwner:GetAttachParentActor() then
            local itemID = UItemIDUtil.ToUint64(oldOwner.PickupItemInfo.ItemId)
            local gridId = getField().itemIdToSpecialGridIdMap[itemID]
            if gridId then
                local gridData = Server.CollectionRoomServer:GetGridData(EShowCabinetType.Special, 1, gridId)
                if gridData and gridData.item and gridData.item.gid == itemGid then
                    CollectionRoomLogic.RefreshSpecialCabinetState(gridId, true)
                end
            end
        end
    end
end

function CollectionRoomLogic._OnMusicPlayerPlayStateChange()
    if ItemOperaTool.bIsInCollectionRoom then
        -- 如果不再播放背景歌曲那么停掉BGM，防止听到大厅默认BGM
        if not Module.MusicPlayer:IsPlayingMusic() then
            Facade.SoundManager:StopBGM()
            Module.MusicPlayer:SetMuiscMode_2D()
        else
            Module.MusicPlayer:SetMuiscMode_3D()
        end
    end
end

function CollectionRoomLogic._ClearData()
    loginfo("CollectionRoomLogic._ClearData")
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if isvalid(invMgr) then
        invMgr:ClearCollectionRoomInventory()
    end
    UCollectionRoomPickupActorManager.Get(GetWorld()):ClearItemInventoryInfo()
    --- 重置各种标志
    CollectionRoomLogic._SetIsInCollectionRoom(false)
    getField():ResetField()
end

function CollectionRoomLogic._OnEnterCollectionRoom()
    loginfo("CollectionRoomLogic._OnEnterCollectionRoom")
    Timer.DelayCall(0, function()
        getField().bIsPopingAllUIForEnterCollectionRoom = true
        Facade.UIManager:CloseAllPopUI()
        Facade.UIManager:PopAllUI(true, true, MapPopAllUIReason2Str.EnterCollectionRoom)
        getField().bIsPopingAllUIForEnterCollectionRoom = false
        CollectionRoomLogic._SetIsInCollectionRoom(true)
        CollectionRoomLogic._OnStateChange(nil, true, nil)
        Facade.UIManager:SetStackBottomKeepMode(false, "CollectionRoom")
        CollectionRoomLogic._SetController(true)
        CollectionRoomLogic._InitEquipmentAndWeapon(true)
    end)
    CollectionRoomLogic._HandleLevelSequence(true)
    CollectionRoomLogic.SetViewTargetToPawn()
    -- 传送前先隐藏Character（后续会在CollectionRoomLogic._SetCharacter显示）
    --bug=147635194 [【ALL】【iOS|安卓】【必现】收藏室-首次下载完场景资源，进入3D收藏室内，会闪过一帧人物双手](https://tapd.woa.com/r/t?id=147635194&type=bug)
    local dfmCharacter = Facade.GameFlowManager:GetCharacter()
    if isvalid(dfmCharacter) then
        dfmCharacter:SetActorHiddenInGame(true)
    end
    CollectionRoomLogic._Teleport(true)
end

---@param leaveFrom number 从哪里退出的
---@param bForce boolean 是否强制退出
function CollectionRoomLogic._OnLeaveCollectionRoom(leaveFrom)
    loginfo("CollectionRoomLogic._OnLeaveCollectionRoom", leaveFrom)
    CollectionRoomLogic.SendCollectionRoomQuitFlow(leaveFrom)
    CollectionRoomLogic._SetIsInCollectionRoom(false)
    ItemOperaTool.RemoveUseCharacterBusiness(ItemOperaTool.SafeHouseBusiness.CollectionRoom)
    CollectionRoomLogic._OnStateChange(nil, false, nil)
    CollectionRoomLogic._SetController(false)
    CollectionRoomLogic.ResetAllPickupActors()
    CollectionRoomLogic._InitEquipmentAndWeapon(false)
    CollectionRoomLogic._HandleLevelSequence(false)
    Facade.UIManager:SetStackBottomKeepMode(true, "CollectionRoom")
    if CollectionRoomLogic.IsNeedToClearMemory() then
        Module.Looting:DestroyMainPanel()
    end
    local bNeedUnLoadLevel = CollectionRoomLogic:_HandleLeaveTarget(leaveFrom) -- 这里可能会切SubStage，除了传送以外的收尾的步骤都应该放在这之前
    CollectionRoomLogic._Teleport(false, leaveFrom)
    getField().enterFrom = nil
    getField().extraParams = {}
    getField().bFirstEnterAfterLogin = false
    if bNeedUnLoadLevel then
        CollectionRoomLogic.UnLoadCollectionRoomLevels(true)
    end
end

function CollectionRoomLogic._SetIsInCollectionRoom(bIsInCollectionRoom)
    bIsInCollectionRoom = setdefault(bIsInCollectionRoom, false)
    loginfo("CollectionRoomLogic._SetIsInCollectionRoom", bIsInCollectionRoom)
    ItemOperaTool.SetIsInCollectionRoom(bIsInCollectionRoom)
    local dfmCharacter = Facade.GameFlowManager:GetCharacter()
    if isvalid(dfmCharacter) and isvalid(dfmCharacter.Blackboard) then
        dfmCharacter.Blackboard:SetIsInCollectionRoom(bIsInCollectionRoom)
    end
    local playerState = Facade.GameFlowManager:GetPlayerState()
    if isvalid(playerState) and isvalid(playerState.MapInfoComp) then
        playerState.MapInfoComp.GetIsWishItemDelegate:Clear()
        if bIsInCollectionRoom then
            playerState.MapInfoComp.GetIsWishItemDelegate:Bind(CollectionRoomLogic.IsWishItem)
        end
    end
end

function CollectionRoomLogic.IsWishItem(itemId)
    return Server.InventoryServer:CheckItemMarked(itemId)
end

function CollectionRoomLogic._OnStateChange(bInSubStage, bInScene, bShowMainPanel)
    if bInSubStage ~= nil then
        getField().bInCollectionRoomSubStage = bInSubStage
    elseif not getField().bInCollectionRoomSubStage then
        return
    end
    if bInSubStage == nil then
        bInSubStage = Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.CollectionRoom
    end
    bInScene = setdefault(bInScene, ItemOperaTool.bIsInCollectionRoom)
    bShowMainPanel = setdefault(bShowMainPanel, getField().mainPanel ~= nil)
    loginfo("CollectionRoomLogic._OnStateChange", bInSubStage, bInScene, bShowMainPanel)
    -- 为了防止影响到其他业务，所有的入参都应该and bInSubStage（仅收藏室的业务可以不用，但加上也没坏处）
    CollectionRoomLogic._SetHUDState(bInSubStage and bInScene and not bShowMainPanel)
    CollectionRoomLogic._SetCharacter(bInSubStage and bInScene)
    CollectionRoomLogic._SetSound(bInSubStage and bInScene)
    CollectionRoomLogic._BindOrUnBindInputEvent(bInSubStage and bInScene and not bShowMainPanel)
    CollectionRoomLogic._SetLight(bInSubStage)
    Module.Looting:BindEvents(bInSubStage and bInScene and not bShowMainPanel)
    CollectionRoomConfig.Events.evtShowOrHideAllGridMarker:Invoke(bInSubStage and bShowMainPanel)
    CollectionRoomLogic:_SetBGM(bInSubStage and bInScene)
    CollectionRoomLogic._SetSpecialCabinet()
    CollectionRoomLogic._ReportTGPA(bInSubStage and bInScene and not bShowMainPanel)
    CollectionRoomLogic._SetConsoleVars(bInSubStage)
end

function CollectionRoomLogic._SetHUDState(bEnterCollectionRoom)
    bEnterCollectionRoom = setdefault(bEnterCollectionRoom, false)
    local hudStateManager = UHUDStateManager.Get(GetWorld())
    if hudStateManager then
        if bEnterCollectionRoom then
            hudStateManager:RemoveState(GameHUDState.GHS_GlobalHideAllHUD, true)
            hudStateManager:AddState(GameHUDState.GHS_CollectionRoom, true)
        else
            hudStateManager:AddState(GameHUDState.GHS_GlobalHideAllHUD, true)
            hudStateManager:RemoveState(GameHUDState.GHS_CollectionRoom, true)
        end
    end
end

local function ExecuteAfterTwoFrames(func)
    Timer.DelayCall(0, function()
        Timer.DelayCall(0, function()
            func()
        end)
    end)
end

CollectionRoomLogic.retryTimes = 10
CollectionRoomLogic.delaySeconds = 0.1

function CollectionRoomLogic:_HandleLeaveTarget(leaveFrom)
    loginfo("CollectionRoomLogic._HandleLeaveTarget", leaveFrom)
    local bNeedDefaultHandle = true
    local bNeedUnLoadLevel = true
    if getField().enterFrom == ECollectionRoomEnterFrom.Interactor then
        -- Facade.GameFlowManager:EnterSubStage(ESubStage.SafeHouse3D)
        Module.IrisSafeHouse:Enter3DSafeHouse()
        bNeedDefaultHandle = false
    elseif IsHD() and leaveFrom == ECollectionRoomLeaveFrom.Interactor then
        -- Facade.GameFlowManager:EnterSubStage(ESubStage.SafeHouse3D)
        Module.IrisSafeHouse:Enter3DSafeHouse()
        bNeedDefaultHandle = false
    else
        Facade.GameFlowManager:EnterSubStage(ESubStage.None)
    end
    if leaveFrom == ECollectionRoomLeaveFrom.Setting or (leaveFrom == ECollectionRoomLeaveFrom.Interactor and not IsHD()) then
        if getField().enterFrom == ECollectionRoomEnterFrom.BlackSite then
            Module.IrisSafeHouse:OpenMainTabView()
            CollectionRoomLogic._OpenBlackSitePage(CollectionRoomLogic.retryTimes)
            bNeedDefaultHandle = false
        elseif getField().enterFrom == ECollectionRoomEnterFrom.MainPanel then
            bNeedUnLoadLevel = false
            Module.IrisSafeHouse:OpenMainTabView()
            CollectionRoomLogic._OpenBlackSitePageAndCollectionRoomMainPanel(CollectionRoomLogic.retryTimes, getField().extraParams)
            bNeedDefaultHandle = false
        end
    end
    if bNeedDefaultHandle then
        logwarning("CollectionRoomLogic._HandleLeaveTarget use default handle")
        Module.IrisSafeHouse:OpenMainTabView()
    end
    return bNeedUnLoadLevel
end

function CollectionRoomLogic._OpenBlackSitePage(retryTimes)
    if Facade.UIManager:GetCurrentStackUIId() ~= UIName2ID.IrisWorldEntryMainPanel then
        if retryTimes <= 0 then
            return
        end
        retryTimes = retryTimes - 1
        Timer.DelayCall(CollectionRoomLogic.delaySeconds, CollectionRoomLogic._OpenBlackSitePage, nil, retryTimes)
        return
    end
    if IsHD() then
        Module.IrisSafeHouse:SetNextJumpToBlackSiteUIIdx(BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement)
        CommonBarHDTabGroupLogic.OnPrimaryTabChanged(6, 1)
        ExecuteAfterTwoFrames(function()
            Module.IrisSafeHouse:SetNextJumpToBlackSiteUIIdx(nil)
        end)
    else
        Module.BlackSite:JumpToMainPanelByUIIdx(BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement, true)
    end
end

function CollectionRoomLogic._OpenBlackSitePageAndCollectionRoomMainPanel(retryTimes, extraParams)
    if Facade.UIManager:GetCurrentStackUIId() ~= UIName2ID.IrisWorldEntryMainPanel then
        if retryTimes <= 0 then
            return
        end
        retryTimes = retryTimes - 1
        Timer.DelayCall(CollectionRoomLogic.delaySeconds, CollectionRoomLogic._OpenBlackSitePageAndCollectionRoomMainPanel, nil, retryTimes, extraParams)
        return
    end
    if IsHD() then
        Module.IrisSafeHouse:SetNextJumpToBlackSiteUIIdx(BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement)
        CommonBarHDTabGroupLogic.OnPrimaryTabChanged(6, 1)
        ExecuteAfterTwoFrames(function()
            CollectionRoomLogic.ShowMainPanel(1, ECollectionRoomMainPanelEnterFrom.EscFromScene, table.unpack(extraParams))
            Module.IrisSafeHouse:SetNextJumpToBlackSiteUIIdx(nil)
        end)
    else
        Module.BlackSite:JumpToMainPanelByUIIdx(BlackSiteDefine.EBlackSiteEntranceType.CollectionRoomManagement, true)
        CollectionRoomLogic.ShowMainPanel(1, ECollectionRoomMainPanelEnterFrom.EscFromScene, table.unpack(extraParams))
        Module.IrisSafeHouse:SetNextJumpToBlackSiteUIIdx(nil)
    end
end

function CollectionRoomLogic.GetNeedLoadedLevels()
    local levels = {"CollectionRoom_Collection", "CollectionRoom_DisplayCtrl", "CollectionRoom_Audio"}
    if IsHD() then
        table.insert(levels, "CollectionRoom_Lighting_HD")
    end
    return levels
end

function CollectionRoomLogic.CheckCollectionRoomPakDownload()
    	-- PC不关心小包
	if IsHD() then
		return true
	end

    local safehouseModuleName = LiteDownloadManager:GetSystemDownloadModuleName(EDownloadSystem.SafeHouse)
	local bDownload = LiteDownloadManager:IsDownloadedByModuleName(safehouseModuleName)
	if bDownload then
		return true
	end

	local bDownloading = LiteDownloadManager:IsDownloadedByModuleName(safehouseModuleName)
	loginfo("CollectionRoomLogic.CheckCollectionRoomPakDownload", bDownloading)
	if bDownloading then
		Module.LitePackage:ShowMainPanel()
	else
		local function fOnConfirm()
			loginfo("CollectionRoomLogic.CheckCollectionRoomPakDownload Confirm download.")
			LiteDownloadManager:DownloadByModuleName(safehouseModuleName)

			Module.LitePackage:ShowMainPanel()
		end

		local pakTotalSize = LiteDownloadManager:GetTotalSizeByModuleName(safehouseModuleName)
		local pakNowSize = LiteDownloadManager:GetNowSizeByModuleName(safehouseModuleName)
		local pakTotalSizeMBStr = string.format("%.1f", (pakTotalSize - pakNowSize) / 1024 / 1024)
		local showStr = StringUtil.Key2StrFormat(CollectionRoomConfig.Loc.CollectionRoomPakDownloadConfirmText, {["PakSize"] = pakTotalSizeMBStr})
		Module.CommonTips:ShowConfirmWindow(showStr, fOnConfirm)
	end

	return false
end

function CollectionRoomLogic.LoadCollectionRoomLevels(fLevelsLoadedCallback, bCallbackIsEnterCollectionRoom)
    loginfo("CollectionRoomLogic.LoadCollectionRoomLevels")
    if getField().bIsLoadingLevels then
        logerror("CollectionRoomLogic.LoadCollectionRoomLevels levels are being loaded, don't repeatedly load levels")
        return
    end
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not isvalid(localCtrl) then
        logerror("CollectionRoomLogic.LoadCollectionRoomLevels localCtrl is not valid")
        return
    end
    local worldComposition = GetWorld().WorldComposition
    if not isvalid(worldComposition) then
        logerror("CollectionRoomLogic.LoadCollectionRoomLevels world composition is not valid")
        return
    end

    localCtrl:StopLoadOrUnLoadCollectionRoom()

    getField().customLevelsLoadedCallback = fLevelsLoadedCallback
    getField().bCallbackIsEnterCollectionRoom = bCallbackIsEnterCollectionRoom or false
    getField().bIsInCollectionRoomProcess = true
    getField().bIsLoadingLevels = true

    local currentExtraScanMaps = {}
    for mapName, _ in pairs(worldComposition.SecondaryWorlds) do
        table.insert(currentExtraScanMaps, mapName)
    end
    if not table.contains(currentExtraScanMaps, "CollectionRoom") then
        table.insert(currentExtraScanMaps, "CollectionRoom")
        worldComposition:UseStreamingSettings("Iris_Entry", currentExtraScanMaps)

        local defaultLoc = CollectionRoomLogic.GetCollectionRoomDefaultLoc()
        localCtrl:StartLoadOrUnLoadCollectionRoom(defaultLoc, 1, CollectionRoomLogic.DEFAULT_LOAD_TIMEOUT)
        localCtrl.OnLoadOrUnloadCollectionRoomDone:Bind(CollectionRoomLogic.OnStreamingLevelsLoadFinished)
        return
    end
    CollectionRoomLogic.LoadDynamicRequestLevels()
end

function CollectionRoomLogic.IsNeedToClearMemory()
    -- 仅限手游低内存设备需要卸载关卡
    return MemoryUtil.IsLogicLowMemoryDevice()
end

---@param bIsActive boolean 是否主动触发，如果是低内存事件触发则传入false
function CollectionRoomLogic.UnLoadCollectionRoomLevels(bIsActive)
    if not CollectionRoomLogic.IsNeedToClearMemory() then
        return
    end
    logerror("CollectionRoomLogic.UnLoadCollectionRoomLevels")
    bIsActive = setdefault(bIsActive, false)
    if not bIsActive then
        -- 如果是低内存触发的卸载场景，不能在玩家还在收藏室场景或者界面的时候进行，否则玩家会被观察到
        local substage = Facade.GameFlowManager:GetCurrentSubStage()
        if substage == ESubStage.CollectionRoom then
            logerror("CollectionRoomLogic.UnLoadCollectionRoomLevels blocked because substage is", substage)
            return
        end
    end
    CollectionRoomLogic.DestroyAllPickupActor()
    local worldComposition = GetWorld().WorldComposition
    if not isvalid(worldComposition) then
        logerror("CollectionRoomLogic.UnLoadCollectionRoomLevels world composition is not valid")
        return
    end
    if getField().bIsLoadingLevels then
        logerror("CollectionRoomLogic.UnLoadCollectionRoomLevels levels are being loaded, stop loading levels")
        local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        if isvalid(localCtrl) then
            localCtrl:StopLoadOrUnLoadCollectionRoom()
        end
        getField().bIsLoadingLevels = false
    end
    getField().bIsInCollectionRoomProcess = false

    local currentExtraScanMaps = {}
    for mapName, _ in pairs(worldComposition.SecondaryWorlds) do
        table.insert(currentExtraScanMaps, mapName)
    end
    if table.contains(currentExtraScanMaps, "CollectionRoom") then
        table.removebyvalue(currentExtraScanMaps, "CollectionRoom")
        worldComposition:UseStreamingSettings("Iris_Entry", currentExtraScanMaps)
    end
	worldComposition:ReleasePlayerLocation()
end

function CollectionRoomLogic.GetCollectionRoomDefaultLoc()
    local absLoc = FVector.ZeroVector
    local outActors
    outActors = UGameplayStatics.GetAllActorsWithTag(GetWorld(), "CollectionRoomFocus", outActors)
    if outActors:Num() == 1 then
        local startPoint = outActors:Get(0)
        local loc = startPoint:K2_GetActorLocation()
        absLoc = UGameplayStatics.RebaseLocalOriginOntoZero(GetWorld(), loc)
        local rebasingOriginLoc = UGameplayStatics.GetWorldOriginLocation(GetWorld())
        loginfo("CollectionRoomLogic.GetCollectionRoomDefaultLoc EnableMultiplayerWorldOriginRebasing=", UHardwareParamHelper.GetConsoleVariableRefIntValue("p.EnableMultiplayerWorldOriginRebasing"))
        loginfo("CollectionRoomLogic.GetCollectionRoomDefaultLoc GetWorld()=", GetWorld())
        loginfo("CollectionRoomLogic.GetCollectionRoomDefaultLoc loc=", tostring(loc))
        loginfo("CollectionRoomLogic.GetCollectionRoomDefaultLoc absLoc=", tostring(absLoc))
        loginfo("CollectionRoomLogic.GetCollectionRoomDefaultLoc originLoc=", rebasingOriginLoc.X, rebasingOriginLoc.Y, rebasingOriginLoc.Z)
    end

    return absLoc
end

function CollectionRoomLogic.OnStreamingLevelsLoadFinished()
    loginfo("CollectionRoomLogic.OnStreamingLevelsLoadFinished")
    CollectionRoomLogic.LoadDynamicRequestLevels()
end

function CollectionRoomLogic.LoadDynamicRequestLevels()
    loginfo("CollectionRoomLogic.LoadDynamicRequestLevels")
    local function fAllLevelFinishCallback(list, reason)
        loginfo("CollectionRoomLogic.LoadDynamicRequestLevels fAllLevelFinishCallback")
        getField().bIsLoadingLevels = false
        if reason == EOperateStreamLevelReason.Success then
            -- 如果当前已经不在打开收藏室场景或界面的流程中了，那么不再继续执行回调
            if not getField().bIsInCollectionRoomProcess then
                logerror("CollectionRoomLogic.LoadDynamicRequestLevels blocked because not in collection room process")
                return
            end
            CollectionRoomLogic._OnLoadCollectionRoomLevel()
            -- loadingCharacterMeshTimer存在说明当前正在等待CharacterMesh加载完成
            -- loadingCharacterMeshTimer不存在说明已经加载完成或者等待超时了，不必阻拦
            if getField().bCallbackIsEnterCollectionRoom and getField().loadingCharacterMeshTimer then
                local dfmCharacter = Facade.GameFlowManager:GetCharacter()
                -- 进一步检查CharacterMesh是否加载完成，加载已完成，也不必阻拦
                if isvalid(dfmCharacter) and not dfmCharacter.bOnCharacterMeshLoadCompleteFPP then
                    loginfo("CollectionRoomLogic.LoadDynamicRequestLevels blocked because callback is enter collectionroom and character mesh loaded not completed")
                    return
                end
            end
            getField().bIsInCollectionRoomProcess = false
            if getField().customLevelsLoadedCallback then
                getField().customLevelsLoadedCallback()
                getField().customLevelsLoadedCallback = nil
            end
        else
            getField().bIsInCollectionRoomProcess = false
            logerror("CollectionRoomLogic.LoadDynamicRequestLevels not success reason=", reason)
        end
    end
    local levels = CollectionRoomLogic.GetNeedLoadedLevels()
    if #levels > 0 then
        Facade.LevelLoadManager:AsyncOperateStreamLevels(levels, {}, true, nil, fAllLevelFinishCallback, false, 30)
    else
        fAllLevelFinishCallback()
    end
end

function CollectionRoomLogic._SetCharacter(bEnterCollectionRoom)
    local dfmCharacter = Facade.GameFlowManager:GetCharacter()
    if not isvalid(dfmCharacter) then
        return
    end
    local movementComp = dfmCharacter:GetComponentByClass(UCharacterMovementComponent)
    if movementComp then
        movementComp.Velocity = FVector.ZeroVector
        movementComp:SetDisablePlayerPerformMovement(not bEnterCollectionRoom, "CollectionRoomLogicSetCharacter")
    end
    dfmCharacter:SetActorHiddenInGame(not bEnterCollectionRoom)
    if bEnterCollectionRoom then
        CollectionRoomLogic._InitFashion()
        CollectionRoomLogic._InitCommertialRoulette()
        -- 无限体力
        USafeHouseGameplayUtils.SetCharacterStaminaChangeRate(dfmCharacter, 0.001)
    else
        USafeHouseGameplayUtils.SetCharacterStaminaChangeRate(dfmCharacter, 1)
    end
end

function CollectionRoomLogic._SetController(bEnterCollectionRoom)
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not isvalid(localCtrl) then
        return
    end
    if bEnterCollectionRoom then
        localCtrl:SetProcessInputComp(EProcessInputCompReason.EProcessInputReason_CollectionRoom, true)
        localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.CollectionRoom, false)
        -- 重置左下角英雄头像，因为玩家在进收藏室前可能会换英雄
        local hud = ABaseHUD.GetHUD(GetWorld())
        if hud then
            local panel = hud:GetPanel("LocalPlayerInfo")
            if panel and panel[1] and panel[1].Reset then
                panel[1]:Reset()
            end
        end
    else
        localCtrl:PlayerStopRequest()
        localCtrl:SetProcessInputComp(EProcessInputCompReason.EProcessInputReason_CollectionRoom, false)
        localCtrl:SetSafeHouseHUDMode(ESafeHouseHUDMode.None, CollectionRoomLogic.IsNeedToClearMemory())
    end
end

function CollectionRoomLogic.SetViewTargetToPawn()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    if not isvalid(localCtrl) then
        return
    end
    localCtrl:GPSetViewTargetWithBlend(localCtrl:GetPawn(), false, true, 0, EViewTargetBlendFunction.VTBlend_Linear, 0, false)
end

-- 初始化表情轮盘
function CollectionRoomLogic._InitCommertialRoulette()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
    local dfmCharacter = localCtrl:GetPawn()
    if not isvalid(dfmCharacter) then
        return false
    end

    local heroIdStr = Server.HeroServer:GetCurUsedHeroId()
    ---@type table<number, pb_CSHeroAccessory>
    local accessories = Server.HeroServer:GetAccessoriesInRoulette(heroIdStr)

    local commertialComp = dfmCharacter.DFCommercializationComponent
    local items = {}
    for _, accessory in pairs(accessories) do
        local item = accessory.item
        local propId = item.prop_id
        local slot = item.slot

        local commercializationItemInfo = FCommercializationItemInfo()
        commercializationItemInfo.HeroId = heroIdStr
        commercializationItemInfo.ItemId = propId
        commercializationItemInfo.Slot = slot
        table.insert(items, commercializationItemInfo)
    end
    commertialComp.CommercializationItems = items
    commertialComp:OnItemsUpdated()

    return true
end

function CollectionRoomLogic._SetLight(bEnterCollectionRoom)
    local outActors
    outActors = UGameplayStatics.GetAllActorsWithTag(GetWorld(), "CollectionRoomHeightFog", outActors)
    local heightFogActor = outActors[1]
    local heightFogComp = isvalid(heightFogActor) and heightFogActor:GetComponentByClass(UExponentialHeightFogComponent) or nil

    if bEnterCollectionRoom then
        LightUtil.SetMiniWorld(false)
        LightUtil.SetFixedCameraPos(false)
        if isvalid(heightFogComp) then
            heightFogComp:SetVisibility(true, false)
        end
    else
        LightUtil.SetMiniWorld(true)
        if isvalid(heightFogComp) then
            heightFogComp:SetVisibility(false, false)
        end
    end
end

function CollectionRoomLogic:_SetBGM(bEnterCollectionRoom)
    if bEnterCollectionRoom then
        if not Module.MusicPlayer:IsPlayingMusic() then
            Module.MusicPlayer:SetMuiscMode_2D()
            -- 如果没有在播放音乐就关闭局外BGM
            Facade.SoundManager:StopBGM()
        else
            Module.MusicPlayer:SetMuiscMode_3D()
        end
    else
        Module.MusicPlayer:SetMuiscMode_2D()
        -- 开启局外BGM
        if not Module.MusicPlayer:IsPlayingMusic() then
            Facade.SoundManager:PlaySafeHouseStartBGM()
        end
    end
end

function CollectionRoomLogic._SetSound(bEnterCollectionRoom)
    if bEnterCollectionRoom then
        -- 开启局内音效
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOn)
    else
        -- 关闭局内音效
        Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.IngameSoundOff)
    end
end

function CollectionRoomLogic._InitEquipment(bEnterCollectionRoom)
    loginfo("CollectionRoomLogic._InitEquipment", bEnterCollectionRoom)
    local character = InGameController:Get():GetGPCharacter()
    if not isvalid(character) then
        logerror("CollectionRoomLogic._InitEquipment character is not valid")
        return
    end
    local equipCmp = UGameplayBlueprintHelper.FindComponentByClass(character, UCharacterEquipComponent)
    if not isvalid(equipCmp) then
        logerror("CollectionRoomLogic._InitEquipment equipCmp is not valid")
        return
    end
    for slotType, equipmentType in pairs(ESlotTypeToEEquipmentType) do
        equipCmp:DoServerUnequip(equipmentType)
        if not bEnterCollectionRoom then
            local slot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
            local item = slot and slot:GetEquipItem()
            if item then
                local equipInfo = FEquipmentInfo()
                equipInfo.ItemId = item.id
                equipCmp:DoServerEquip(equipmentType, equipInfo)
            end
        end
    end
end

function CollectionRoomLogic._InitFashion()
    loginfo("CollectionRoomLogic._InitFashion")
    local character = InGameController:Get():GetGPCharacter()
    if not character then
        return
    end
    local CharacterFashionComponent = character.CharacterFashionComponent
    if not CharacterFashionComponent then
        return
    end
    local heroId = tonumber(Server.HeroServer:GetCurUsedHeroId()) or 88000000001
    local fashionAvatarId = Server.HeroServer:GetFashionSuitIdByHeroId(heroId)
    local bDisplayFashion = Server.HeroServer:GetFashionPriorByCategory(FashionPriorCategory.FashionSOLHouse) or false

    CharacterFashionComponent:UpdateUsingHeroFashionData(heroId, fashionAvatarId, bDisplayFashion, bDisplayFashion, bDisplayFashion)
    CharacterFashionComponent:UpdateUsingHeroFashionWatchData(heroId, Server.HeroServer:GetUsedWatchAccessory(fashionAvatarId))

    CharacterFashionComponent.currentCharacterHeroID = heroId
    CharacterFashionComponent:OnRep_CharacterAvatarID()

    local playerState = InGameController:Get():GetPlayerState()
    local playerName = Server.RoleInfoServer.nickName or "Default"
    playerState:SetPlayerName(playerName)

    playerState.HeroID = heroId

    local heroInfo = ItemConfigTool.GetHeroInfoById(heroId)
    if heroInfo then
        local armId = heroInfo.ArmedForceId
        character.Blackboard.CurrentArmdedForce = armId

        loginfo("CollectionRoomLogic._InitFashion", heroId, armId)
    else
        logwarning(string.format("CollectionRoomLogic._InitFashion failed, heroId(%d) not found", heroId))
    end
end

function CollectionRoomLogic._InitEquipmentAndWeapon(bEnterCollectionRoom)
    local invMgr = Facade.GameFlowManager:GetCharacterInventoryMgr()
    if isvalid(invMgr) then
        invMgr:ClearAllInventory()
    end
    CollectionRoomLogic._InitWeapon(bEnterCollectionRoom)
    CollectionRoomLogic._InitEquipment(bEnterCollectionRoom)
    if bEnterCollectionRoom then
        if isvalid(invMgr) then
            local slot = Server.InventoryServer:GetSlot(ESlotType.MeleeWeapon)
            local meleeWeaponItem = slot and slot:GetEquipItem()
            local meleeWeaponItemId = meleeWeaponItem and meleeWeaponItem.id or 18100000002
            invMgr:OnEnterCollectionRoom(meleeWeaponItemId)
        end
        if not Module.Looting:GetMainPanelHandle() then
            Module.Looting:PreCreateUIs()
        end
    else
        if isvalid(invMgr) then
            invMgr:OnLeaveCollectionRoom()
        end
        Server.LootingServer:SetHasFetchedPlayerItems(false)
    end
end

local function fAddGid(components)
    for _, component in ipairs(components) do
        if component.prop_data.gid == 0 then
            component.prop_data.gid = GetGid()
        end

        fAddGid(component.prop_data.components)
    end
end

function CollectionRoomLogic._InitWeapon(bEnterCollectionRoom)
    loginfo("CollectionRoomLogic._InitWeapon", bEnterCollectionRoom)
    local character = InGameController:Get():GetGPCharacter()
    if not isvalid(character) then
        logerror("CollectionRoomLogic._InitWeapon character is not valid")
        return
    end
    local weaponCmp = UGameplayBlueprintHelper.FindComponentByClass(character, UWeaponManagerComponent)
    if not isvalid(weaponCmp) then
        logerror("CollectionRoomLogic._InitWeapon weaponCmp is not valid")
        return
    end
    weaponCmp:RemoveAllWeapon(true)
    if not bEnterCollectionRoom then
        local switchToWeaponType
        for _, slotType in ipairs(CollectionRoomLogic.WEAPON_SLOT_TYPES) do
            local targetSlot = Server.InventoryServer:GetSlot(slotType, ESlotGroup.Player)
            if targetSlot:IsWeaponSlot() then
                local weapon = targetSlot:GetEquipItem()
                if weapon then
                    -- 武器配件依赖有一个GUID
                    fAddGid(weapon.components)

                    local weaponDesc = WeaponAssemblyTool.PropInfo_To_Desc(weapon.rawPropInfo)
                    if weaponCmp.BP_AddWeaponDesc then
                        weaponCmp:BP_AddWeaponDesc(weaponDesc, 0, slotType)
                    end

                    if switchToWeaponType == nil then
                        switchToWeaponType = slotType
                    end
                end
            end
        end

        if switchToWeaponType then
            weaponCmp:CancelSwitchCurrentWeapon()
            weaponCmp:SwitchToWeapon(ESwitchWeaponContext.SafeHouse, switchToWeaponType, EWeaponSwitchType.Instant)
        end
    end
end

function CollectionRoomLogic._HandleLevelSequence(bEnterCollectionRoom)
    if not IsHD() then
        return
    end
    loginfo("CollectionRoomLogic._HandleLevelSequence", bEnterCollectionRoom)
    if getField().bFirstEnterAfterLogin then
        if bEnterCollectionRoom then
            CollectionRoomLogic.PlayLevelSequence(1)
        else
            CollectionRoomLogic.PlayLevelSequence(nil, nil, true)
        end
    end
end

function CollectionRoomLogic.PlayLevelSequence(startLabelOrFrame, stopLabelOrFrame, bPlayWhole)
    if not IsHD() then
        return
    end
    if not isvalid(getField().lightSeqActor) then
        logerror("CollectionRoomLogic.PlayLevelSequence lightSeqActor not valid")
        return
    end
    local sequencePlayer = getField().lightSeqActor.SequencePlayer
    if not isvalid(sequencePlayer) then
        logerror("CollectionRoomLogic.PlayLevelSequence sequencePlayer not valid")
        return
    end
    sequencePlayer:Stop()
    if bPlayWhole then
        sequencePlayer:Play()
    else
        if type(startLabelOrFrame) == "string" then
            sequencePlayer:JumpToMarkedFrame(startLabelOrFrame)
        elseif type(startLabelOrFrame) == "number" then
            sequencePlayer:JumpToFrame(FFrameTime(startLabelOrFrame, 0))
        end
        if type(stopLabelOrFrame) == "string" then
            sequencePlayer:PlayToMarkedFrame(stopLabelOrFrame)
        elseif type(stopLabelOrFrame) == "number" then
            sequencePlayer:PlayToFrame(FFrameTime(stopLabelOrFrame, 0))
        end
    end
end

function CollectionRoomLogic.SetDisplayCabinet(values)
    loginfo("CollectionRoomLogic.SetDisplayCabinet")
    CollectionRoomLogic.CallCtrlFunction("SetDisplayCabinetTexture")
    CollectionRoomLogic._ModifyDisplayCabinetTexture(function()
        local grids = Server.CollectionRoomServer:GetGridDataMap(EShowCabinetType.Display)
        if grids then
            for _, cabinetGridData in pairs(grids) do
                trycall(function()
                    if values then
                        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetTexture", cabinetGridData.id, values)
                    else
                        local level = getField().displayGridIdToLevel[cabinetGridData.id]
                        if not level then
                            level = cabinetGridData.level
                        end
                        if level then
                            CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetTexture", cabinetGridData.id, {-1, 0, level * 0.5})
                        end
                    end
                end)
            end
        end
    end)
    CollectionRoomLogic._ModifyDisplayCabinetBracketTexture(function()
        local grids = Server.CollectionRoomServer:GetGridDataMap(EShowCabinetType.Display)
        if grids then
            for _, cabinetGridData in pairs(grids) do
                trycall(function()
                    if cabinetGridData.item and cabinetGridData.item.id ~= 0 then
                        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetBracketTexture", cabinetGridData.id, {-1, -1, 1})
                    else
                        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetBracketTexture", cabinetGridData.id, {-1, -1, 0})
                    end
                end)
            end
        end
    end)
end

function CollectionRoomLogic._UpdateLvUpTime(maxLvUpTime, startTime)
    local deltaTime = TimeUtil.GetCurrentTimeMillis() - startTime
    local totalSec = CollectionRoomConfig.displayCabinetLevelUpTotalSeconds
    local stage1Sec = CollectionRoomConfig.displayCabinetLevelUpStage1Seconds
    local stage2Sec = CollectionRoomConfig.displayCabinetLevelUpStage2Seconds
    local GridLvUptime
    if deltaTime < totalSec then
        if deltaTime < stage1Sec then
            GridLvUptime = deltaTime / stage1Sec * maxLvUpTime
        else
            GridLvUptime = ((totalSec - deltaTime) / stage2Sec) * maxLvUpTime
        end
        getField().displayGridLevelUpInfo.timer = Facade.LuaFramingManager:RegisterFrameTask(CollectionRoomLogic._UpdateLvUpTime, nil, {maxLvUpTime, startTime})
    else
        GridLvUptime = 0
        CollectionRoomLogic.StopDisplayCabinetLevelUp()
    end
    CollectionRoomLogic.CallCtrlFunction("SetDisplayCabinetScalarParam", "GridLvUptime", GridLvUptime)
end

function CollectionRoomLogic.StartDisplayCabinetLevelUp(gridId, level)
    loginfo("CollectionRoomLogic.StartDisplayCabinetLevelUp", gridId, level)
    CollectionRoomLogic.StopDisplayCabinetLevelUp()
    getField().displayGridLevelUpInfo.gridId = gridId
    getField().displayGridLevelUpInfo.level = level
    level = level or Server.CollectionRoomServer:GetCabinetGridLevel(EShowCabinetType.Display, 1, gridId)
    CollectionRoomLogic._ModifyDisplayCabinetTexture(function()
        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetTexture", gridId, {-1, 1, level * 0.5})
    end)
    getField().displayGridLevelUpInfo.timer = Facade.LuaFramingManager:RegisterFrameTask(CollectionRoomLogic._UpdateLvUpTime, nil, {1, TimeUtil.GetCurrentTimeMillis()})
    CollectionRoomLogic.PlayDisplayCabinetLevelUpAudio(gridId, level)
end

function CollectionRoomLogic.StopDisplayCabinetLevelUp()
    local info = getField().displayGridLevelUpInfo
    if next(info) then
        loginfo("CollectionRoomLogic.StopDisplayCabinetLevelUp")
        if info.timer then
            Facade.LuaFramingManager:CancelFrameTask(info.timer)
        end
        CollectionRoomLogic.SetDisplayCabinet()
        getField().displayGridLevelUpInfo = {}
    end
end

function CollectionRoomLogic.PlayDisplayCabinetLevelUpAudio(gridId, level)
    local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.CollectionRoom)
    if isvalid(ctrl) then
        local audioEventAsset
        if level == 1 then
            audioEventAsset = ctrl.AudioEventAssetMap:Get("Exhibition_Cabinet_Light_White")
        elseif level == 2 then
            audioEventAsset = ctrl.AudioEventAssetMap:Get("Exhibition_Cabinet_Light_Gold")
        end
        if audioEventAsset then
            local placeActor = CollectionRoomLogic.GetPlaceActorByCabinetAndGrid(EShowCabinetType.Display, 1, gridId)
            if isvalid(placeActor) then
                UGPAudioStatics.PlayGPAudioEventAtLocationOpt(audioEventAsset, placeActor:K2_GetActorLocation(), placeActor:K2_GetActorRotation(), "", GetWorld(),
                        false, EGameAkType.EGAMEAK_DEFAULT, true, 1, false, FVector.ZeroVector, false, 0, EGPAudioWaterPreset.NotSet)
            end
        end
    end
end

function CollectionRoomLogic.PlaySpecialCabinetLevelUpAudio(gridId, level)
    local ctrl = Facade.HallSceneManager:GetSceneCtrlBySubStage(ESubStage.CollectionRoom)
    if isvalid(ctrl) then
        local audioEventAsset
        if level == 1 then
            audioEventAsset = ctrl.AudioEventAssetMap:Get("Exhibition_Cabinet_Light_White")
        elseif level == 2 then
            audioEventAsset = ctrl.AudioEventAssetMap:Get("Exhibition_Cabinet_Light_Gold")
        end
        if audioEventAsset then
            local focusLocation = CollectionRoomLogic.CallSpecialCabinetFunction(gridId, "GetCameraFocusLocation") or FVector.ZeroVector
            UGPAudioStatics.PlayGPAudioEventAtLocationOpt(audioEventAsset, focusLocation, FRotator.ZeroRotator, "", GetWorld(),
                    false, EGameAkType.EGAMEAK_DEFAULT, true, 1, false, FVector.ZeroVector, false, 0, EGPAudioWaterPreset.NotSet)
        end
    end
end

function CollectionRoomLogic.SetDisplayCabinetGridLevel(gridId, level)
    loginfo("CollectionRoomLogic.SetDisplayCabinetGridLevel", gridId, level)
    CollectionRoomLogic._ModifyDisplayCabinetTexture(function()
        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetTexture", gridId, {-1, 1, level * 0.5})
    end)
    CollectionRoomLogic.UpdateDisplayCabinetGridLevelCache(gridId, level)
    CollectionRoomLogic.StopDisplayCabinetLevelUp()
    LogAnalysisTool.ReportCollectionRoomButtonClickedFlow(ECollectionRoomButtonType.DisplayCabinetSwitchLevel)
end

function CollectionRoomLogic.SetDisplayCabinetGridValues(gridId, values)
    loginfo("CollectionRoomLogic.SetDisplayCabinetGridLevel", gridId)
    CollectionRoomLogic._ModifyDisplayCabinetTexture(function()
        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetTexture", gridId, values)
    end)
end

function CollectionRoomLogic._StopDisplayCabinetLevelUpAnim()
    loginfo("CollectionRoomLogic._StopDisplayCabinetLevelUpAnim")
    CollectionRoomLogic.SetDisplayCabinet()
    CollectionRoomLogic.CallCtrlFunction("SetDisplayCabinetScalarParam", "GridLvUptime", 0)
end

function CollectionRoomLogic.SpawnAllPickupActor()
    loginfo("CollectionRoomLogic.SpawnAllPickupActor")
    if getField().bIsSpawnOrDestroyingAllPickups == true then
        return
    else
        getField():CancelAllFramingTasks()
    end
    getField().bIsSpawnOrDestroyingAllPickups = nil
    for _, cabinetGroup in pairs(Server.CollectionRoomServer.idToCabinetDataMap) do
        for _, cabinetData in pairs(cabinetGroup) do
            for _, cabinetGridData in pairs(cabinetData.grids) do
                if CollectionRoomLogic.ENABLE_FRAMING_SPAWN_PICKUP then
                    getField().bIsSpawnOrDestroyingAllPickups = true
                    CollectionRoomLogic.DeferSpawnPickupActor(cabinetData.type, cabinetData.id, cabinetGridData.id)
                else
                    CollectionRoomLogic.SpawnPickupActor(cabinetData.type, cabinetData.id, cabinetGridData.id)
                end
            end
        end
    end
end

function CollectionRoomLogic.DeferSpawnPickupActor(cabinetType, cabinetId, cabinetGridId)
    if bCollectionRoomEnableVerboseLog then
        loginfo("CollectionRoomLogic.DeferSpawnPickupActor", cabinetType, cabinetId, cabinetGridId)
    end
    local handle = Facade.LuaFramingManager:RegisterFrameTask(function()
        CollectionRoomLogic.SpawnPickupActor(cabinetType, cabinetId, cabinetGridId)
        getField():SetFramingTaskHandle(cabinetType, cabinetId, cabinetGridId, nil)
        if getField().bIsSpawnOrDestroyingAllPickups and getField():HasNoFramingTasks() then
            getField().bIsSpawnOrDestroyingAllPickups = nil
        end
    end)
    getField():SetFramingTaskHandle(cabinetType, cabinetId, cabinetGridId, handle)
end

function CollectionRoomLogic.DestroyAllPickupActor()
    loginfo("CollectionRoomLogic.DestroyAllPickupActor")
    if getField().bIsSpawnOrDestroyingAllPickups == false then
        return
    else
        getField():CancelAllFramingTasks()
    end
    getField().bIsSpawnOrDestroyingAllPickups = nil
    UCollectionRoomPickupActorManager.Get(GetWorld()):RemoveAllCollectionItems()
end

function CollectionRoomLogic.DeferDestroyPickupActor(cabinetType, cabinetId, cabinetGridId)
    loginfo("CollectionRoomLogic.DeferDestroyPickupActor", cabinetType, cabinetId, cabinetGridId)
    local handle = Facade.LuaFramingManager:RegisterFrameTask(function()
        CollectionRoomLogic.DestroyPickupActor(cabinetType, cabinetId, cabinetGridId)
        getField():SetFramingTaskHandle(cabinetType, cabinetId, cabinetGridId, nil)
        if getField().bIsSpawnOrDestroyingAllPickups == false and getField():HasNoFramingTasks() then
            getField().bIsSpawnOrDestroyingAllPickups = nil
        end
    end)
    getField():SetFramingTaskHandle(cabinetType, cabinetId, cabinetGridId, handle)
end

function CollectionRoomLogic.SpawnPickupActor(cabinetType, cabinetId, cabinetGridId, itemId)
    if CollectionRoomLogic.IsLimitedGrid(cabinetType, cabinetGridId) then
        return
    end
    if bCollectionRoomEnableVerboseLog then
        loginfo("CollectionRoomLogic.SpawnPickupActor", cabinetType, cabinetId, cabinetGridId)
    end
    itemId = setdefault(itemId, 0)
    local CollectionRoomPickupActorManager = UCollectionRoomPickupActorManager.Get(GetWorld())
    local itemData = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, cabinetId, cabinetGridId)
    local itemGid = itemData and itemData.gid or 0
    itemId = itemData and itemData.id or itemId
    if itemId ~= 0 then
        CollectionRoomPickupActorManager:AddCollectionItem(cabinetType, cabinetId, cabinetGridId, itemGid, itemId)
        if cabinetType == EShowCabinetType.Special and itemGid ~= 0 then
            CollectionRoomLogic.RefreshSpecialCabinetState(cabinetGridId, false)
        end
    else
        if bCollectionRoomEnableVerboseLog then
            loginfo(string.format("CollectionRoomLogic.SpawnPickupActor fail to spawn pickupActor because itemId=0 for cabinetId=%d cabinetGridId=%d", cabinetId, cabinetGridId))
        end
        return
    end
    local pickupActor = CollectionRoomPickupActorManager:GetPickupActor(cabinetType, cabinetId, cabinetGridId)
    if bCollectionRoomEnableVerboseLog and isvalid(pickupActor) then
        loginfo(string.format("CollectionRoomLogic.SpawnPickupActor succeed to spawn pickupActor cabinetId=%d cabinetGridId=%d", cabinetId, cabinetGridId))
    end
    return pickupActor
end

function CollectionRoomLogic.DestroyTempPickupActor(bCurrOrTaget)
    loginfo("CollectionRoomLogic.DestroyTempPickupActor", bCurrOrTaget)
    ---@type CabinetGridAddress
    local addr
    if bCurrOrTaget then
        addr = getField().currFocusGridAddr
        if addr and not Server.CollectionRoomServer:GetCabinetItemByGrid(addr.cabinetType, addr.cabinetId, addr.gridId) then
            CollectionRoomLogic.DestroyPickupActor(addr.cabinetType, addr.cabinetId, addr.gridId)
            getField().currFocusGridAddr = nil
        end
    else
        addr = getField().targetFocusGridAddr
        if addr and not Server.CollectionRoomServer:GetCabinetItemByGrid(addr.cabinetType, addr.cabinetId, addr.gridId) then
            CollectionRoomLogic.DestroyPickupActor(addr.cabinetType, addr.cabinetId, addr.gridId)
            getField().targetFocusGridAddr = nil
        end
    end
end

function CollectionRoomLogic.DestroyPickupActor(cabinetType, cabinetId, cabinetGridId)
    if CollectionRoomLogic.IsLimitedGrid(cabinetType, cabinetGridId) then
        return
    end
    loginfo("CollectionRoomLogic.DestroyPickupActor", cabinetType, cabinetId, cabinetGridId)
    UCollectionRoomPickupActorManager.Get(GetWorld()):RemoveCollectionItem(cabinetType, cabinetId, cabinetGridId)
    if cabinetType == EShowCabinetType.Special then
        CollectionRoomLogic.RefreshSpecialCabinetState(cabinetGridId, true)
    end
end

function CollectionRoomLogic.GetPickupActor(cabinetType, cabinetId, cabinetGridId)
    return UCollectionRoomPickupActorManager.Get(GetWorld()):GetPickupActor(cabinetType, cabinetId, cabinetGridId)
end

function CollectionRoomLogic._CheckSpawnTempPickupActor(cabinetType, cabinetId, gridId)
    if cabinetType == EShowCabinetType.Display then
        local addr = getField().currFocusGridAddr
        if addr and addr.cabinetType == cabinetType and addr.cabinetId == cabinetId and addr.gridId == gridId then
            if CollectionRoomLogic.IsVirtualGrid(cabinetType, gridId) then
                local config = CollectionRoomLogic.GetDisplayOrSpecialGridConfig(cabinetType, gridId)
                if config and config.ItemID ~= 0 then
                    CollectionRoomLogic.SpawnPickupActor(cabinetType, cabinetId, gridId, tonumber(config.ItemID))
                end
            end
        end
    end
end

function CollectionRoomLogic.OnCabinetGridLevelUp(cabinetType, cabinetId, gridId)
    loginfo("CollectionRoomLogic.OnCabinetGridLevelUp", cabinetType, cabinetId, gridId)
    if cabinetType == EShowCabinetType.Display then
        CollectionRoomLogic.UpdateDisplayCabinetGridLevelCache(gridId, nil)
        CollectionRoomLogic.StartDisplayCabinetLevelUp(gridId)
        local pickupActor = CollectionRoomLogic.GetPickupActor(cabinetType, cabinetId, gridId)
        CollectionRoomLogic._TrySetFakeLightMaterialForPickupActor(pickupActor)
    elseif cabinetType == EShowCabinetType.Special then
        local level = Server.CollectionRoomServer:GetCabinetGridLevel(cabinetType, cabinetId, gridId)
        CollectionRoomLogic.PlaySpecialCabinetAnim(gridId, level, true)
    end
end

function CollectionRoomLogic._Teleport(bEnter, leaveFrom)
    loginfo("CollectionRoomLogic._Teleport", bEnter, leaveFrom)
    local character = InGameController:Get():GetGPCharacter()
    local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)

    if not isvalid(localCtrl) then
        logerror("CollectionRoomLogic._Teleport no localCtrl")
        return
    end

    if not isvalid(character) then
        logerror("CollectionRoomLogic._Teleport no character")
        return
    end

    local PSTag
    -- 如果是从3D特勤处进来的，需要记录下来之前的位置
    if bEnter then
        if IsHD() and getField().bFirstEnterAfterLogin then
            PSTag = "CollectionRoomPS0"
        else
            PSTag = "CollectionRoomPS1"
        end
        getField().fromSafeHouseLoc = character:K2_GetActorLocation()
        getField().fromSafeHouseRot = character:K2_GetActorRotation()
    else
        if getField().enterFrom == ECollectionRoomEnterFrom.Interactor
                or (leaveFrom == ECollectionRoomLeaveFrom.Interactor and IsHD()) then
            PSTag = "CollectionRoomEntrancePS"
        elseif not (getField().fromSafeHouseLoc and getField().fromSafeHouseRot) then
            PSTag = "SafeHousePS0"
            logerror("CollectionRoomLogic._Teleport fail to find fromSafeHouse loc and rot, use PSTag=SafeHousePS0")
        end
    end
    local loc, rot
    if PSTag then
        loginfo("CollectionRoomLogic._Teleport use PSTag=", PSTag)
        local outActors
        outActors = UGameplayStatics.GetAllActorsWithTag(localCtrl, PSTag, outActors)
        if outActors:Num() >= 1 then
            local startPoint = outActors:Get(0)
            loc = startPoint:K2_GetActorLocation()
            rot = startPoint:K2_GetActorRotation()
        else
            logerror("CollectionRoomLogic._Teleport fail to find actor for PSTag=", PSTag)
        end
    else
        if not bEnter then
            if getField().fromSafeHouseLoc and getField().fromSafeHouseRot then
                loginfo("CollectionRoomLogic._Teleport use fromSafeHouse loc and rot")
                loc = getField().fromSafeHouseLoc
                rot = getField().fromSafeHouseRot
            else
                loginfo("CollectionRoomLogic._Teleport use 3DSafeHouseDefaultLoc")
                loc = Module.IrisSafeHouse:Get3DSafeHouseDefaultLoc()
                rot = character:K2_GetActorRotation()
            end
        end
    end
    if not bEnter then
        getField().fromSafeHouseLoc = nil
        getField().fromSafeHouseRot = nil
    end

    if loc and rot then
        logerror("CollectionRoomLogic._Teleport loc=", tostring(loc), "rot=", tostring(rot))
        loc = UGameplayStatics.RebaseLocalOriginOntoZero(localCtrl, loc)
        localCtrl:ExecTeleport(loc)
        character:K2_SetActorRotation(rot, false)
    else
        logerror("CollectionRoomLogic._Teleport fail to find loc or rot")
    end
end

function CollectionRoomLogic._OnFppMeshLoadSuccess(bTimeout)
    bTimeout = setdefault(bTimeout, false)
    if bTimeout then
        logerror("CollectionRoomLogic._OnFppMeshLoadSuccess timeout")
    else
        loginfo("CollectionRoomLogic._OnFppMeshLoadSuccess")
    end
    getField():CancelLoadingCharacterMeshTimer()
    local dfmCharacter = Facade.GameFlowManager:GetCharacter()
    if isvalid(dfmCharacter) and dfmCharacter.OnFppMeshLoadSuccess then
        dfmCharacter.OnFppMeshLoadSuccess:Remove(CollectionRoomLogic._OnFppMeshLoadSuccess)
    end
    -- 这里是Character Mesh加载完成的回调，需要关注以下几点：
    -- 1. 有回调可执行
    -- 2. 回调是打开收藏室场景（Character Mesh只影响进入收藏室的流程，不影响打开收藏室界面）
    -- 3. 当前还在打开收藏室场景的流程中（如果不在了，不应该继续）
    -- 4. 收藏室关卡加载完毕（如果关卡没加载完，也不应该继续，应该用关卡加载完的那个回调）
    if getField().customLevelsLoadedCallback and getField().bCallbackIsEnterCollectionRoom and getField().bIsInCollectionRoomProcess and not getField().bIsLoadingLevels then
        getField().bIsInCollectionRoomProcess = false
        getField().customLevelsLoadedCallback()
        getField().customLevelsLoadedCallback = nil
    else
        loginfo("CollectionRoomLogic._OnFppMeshLoadSuccess fail to execute custom callback",
                getField().customLevelsLoadedCallback, getField().bCallbackIsEnterCollectionRoom, getField().bIsInCollectionRoomProcess, getField().bIsLoadingLevels)
    end
end

function CollectionRoomLogic._BindOrUnBindInputEvent(bEnterCollectionRoom)
    local inputMonitor = Facade.UIManager:GetInputMonitor()
    if bEnterCollectionRoom then
        if not CollectionRoomLogic._OnNotifyInputTypeChangedHandle then
            CollectionRoomLogic._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CollectionRoomLogic._OnInputTypeChanged)
            -- 初始化
            local curInputType = WidgetUtil.GetCurrentInputType()
            CollectionRoomLogic._OnInputTypeChanged(curInputType)
        end
        -- HD requires a long press on the special right key.
        UGPGameplayDelegates.Get(GetGameInstance()).OnOpenSystemSettingHDEntrance:Bind(CollectionRoomLogic._ToggleSettingEntrance, self)
    else
        if CollectionRoomLogic.actionBackHandle then
            inputMonitor:RemoveDisplayActoinBingingForHandle(CollectionRoomLogic.actionBackHandle)
            CollectionRoomLogic.actionBackHandle = nil
        end
        UGPGameplayDelegates.Get(GetGameInstance()).OnOpenSystemSettingHDEntrance:Clear()
        if CollectionRoomLogic._OnNotifyInputTypeChangedHandle then
            UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(CollectionRoomLogic._OnNotifyInputTypeChangedHandle)
            CollectionRoomLogic._OnNotifyInputTypeChangedHandle = nil
        end
    end
end

function CollectionRoomLogic._OnInputTypeChanged(InputType)
    if not IsHD() then
       return
    end

    local inputMonitor = Facade.UIManager:GetInputMonitor()
    -- Hide side scroll box if gamepad active.
    if InputType == EGPInputType.Gamepad then
        if CollectionRoomLogic.actionBackHandle then
            inputMonitor:RemoveDisplayActoinBingingForHandle(CollectionRoomLogic.actionBackHandle)
            CollectionRoomLogic.actionBackHandle = nil
        end
    else
        if not CollectionRoomLogic.actionBackHandle then
            CollectionRoomLogic.actionBackHandle =
            inputMonitor:AddDisplayActionBinding(
                    "Back",
                    EInputEvent.IE_Pressed,
                    CollectionRoomLogic._ToggleSettingEntrance,
                    self,
                    EDisplayInputActionPriority.UI_HUD
            )
        end
    end
end

function CollectionRoomLogic._ToggleSettingEntrance()
    loginfo("CollectionRoomLogic._ToggleSettingEntrance")
    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UIBack)
    if Facade.UIManager:GetStackUICount() > 0 then
        return
    end
    -- Module.SystemSetting:ShowOrHideSystemSettingHDEntrance()
    -- 采用新版侧滑菜单
    Facade.UIManager:AsyncShowUI(UIName2ID.SafeHouse3DExitPanel)
end

function CollectionRoomLogic.GetPlaceActorByCabinetAndGrid(cabinetType, cabinetId, cabinetGridId)
    local CollectionRoomPickupActorManager = UCollectionRoomPickupActorManager.Get(GetWorld())
    if CollectionRoomPickupActorManager.GetPlaceActorByCabinetAndGrid then
        return CollectionRoomPickupActorManager:GetPlaceActorByCabinetAndGrid(cabinetType, cabinetId, cabinetGridId)
    end
end

function CollectionRoomLogic.SetViewTargetToNamedCamera(cameraName, bEnableRepeat, bImmediatelyFlag)
    loginfo("CollectionRoomLogic.SetViewTargetToNamedCamera", cameraName, bEnableRepeat, bImmediatelyFlag)
    --PC和手游都会走到这里，但是这里的修改只是为了避免后续的Lua报错而做的判空保护，理论上无影响，PC上实测确实也无影响
    if cameraName == nil then
        return false
    end
    bEnableRepeat = setdefault(bEnableRepeat, false)
    local currentDisplayType = CollectionRoomLogic.CallCtrlFunction("GetDisplayType")
    bImmediatelyFlag = setdefault(bImmediatelyFlag, currentDisplayType == cameraName or currentDisplayType == nil
            or currentDisplayType == "None" or currentDisplayType == "AvoidRobotArmSound")
    CollectionRoomLogic.CallCtrlFunction("SetDisplayType", cameraName, bEnableRepeat, bImmediatelyFlag)
    return bImmediatelyFlag
end

function CollectionRoomLogic.GetCollectionRoomTotalValue()
    local totalValue = 0
    local upgradeValue = CollectionRoomLogic.GetCollectionRoomUpgradeValue()
    local displayCabinetValue, seasonLimitedValue = CollectionRoomLogic.GetCollectionRoomCabinetValue(EShowCabinetType.Display)
    local specialCabinetValue = CollectionRoomLogic.GetCollectionRoomCabinetValue(EShowCabinetType.Special)
    totalValue = totalValue + upgradeValue + displayCabinetValue + seasonLimitedValue + specialCabinetValue
    local diyCabinetValues = {}
    for cabinetId = 1,CollectionRoomLogic.GetDIYCabinetNum() do
        diyCabinetValues[cabinetId] = CollectionRoomLogic.GetCollectionRoomCabinetValue(EShowCabinetType.DIY, cabinetId)
        totalValue = totalValue + diyCabinetValues[cabinetId]
    end
    return totalValue, upgradeValue, displayCabinetValue, specialCabinetValue, diyCabinetValues, seasonLimitedValue
end

-- 收藏室不动产价值，包含特勤处升级收藏室的消耗价值和收藏室升级展位的消耗价值
function CollectionRoomLogic.GetCollectionRoomUpgradeValue()
    -- 特勤处升级收藏室的消耗价值
    local blackSiteUpgradeValue = 0
    --local deviceData = Server.BlackSiteServer:GetDeviceData(EBlackSiteDeviceName2Id.CollectionRoom)
    --if deviceData and deviceData.level then
    --    local blackSiteUpgradeTable = Facade.TableManager:GetTable("SafeHouse/SafeHouseUpgrade")
    --    for _, config in pairs(blackSiteUpgradeTable) do
    --        if config.DeviceId == EBlackSiteDeviceName2Id.CollectionRoom and config.Level <= deviceData.level then
    --            local itemList = string.split(config.ItemList, ";")
    --            for _, value in pairs(itemList) do
    --                if not string.isempty(value) then
    --                    local arr = string.split(value, ":")
    --                    local itemID = arr[1]
    --                    local itemNum = tonumber(arr[2])
    --                    if itemID == "17020000010" then
    --                        blackSiteUpgradeValue = blackSiteUpgradeValue + itemNum
    --                    else
    --                        blackSiteUpgradeValue = blackSiteUpgradeValue + Server.ShopServer:GetShopDynamicGuidePrice(tonumber(itemID)) * itemNum
    --                    end
    --                end
    --            end
    --        end
    --    end
    --end

    -- 收藏室升级展位的消耗价值
    local gridUpgradeCostValue = 0
    for cabinetType, cabinetGroup in pairs(Server.CollectionRoomServer.idToCabinetDataMap) do
        for cabinetId, cabinetData in pairs(cabinetGroup) do
            for gridId, cabinetGridData in pairs(cabinetData.grids) do
                if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
                    if cabinetGridData.level > 0 then
                        for level = 1, cabinetGridData.level do
                            if getField().upgradeItems and getField().upgradeItems[cabinetType]
                                    and getField().upgradeItems[cabinetType][cabinetGridData.id]
                                    and getField().upgradeItems[cabinetType][cabinetGridData.id][level] then
                                local upgradeItems = getField().upgradeItems[cabinetType][cabinetGridData.id][level]
                                for index = 1, #upgradeItems.items do
                                    local itemId = upgradeItems.items[index]
                                    if not string.isempty(itemId) then
                                        local quantity = tonumber(upgradeItems.quantity[index])
                                        if itemId == "17020000010" then
                                            gridUpgradeCostValue = gridUpgradeCostValue + quantity
                                        else
                                            gridUpgradeCostValue = gridUpgradeCostValue + Server.ShopServer:GetShopDynamicGuidePrice(tonumber(itemId)) * quantity
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
    end

    return blackSiteUpgradeValue + gridUpgradeCostValue
end

function CollectionRoomLogic.GetCollectionRoomCabinetValue(cabinetType, cabinetId)
    if cabinetType ~= EShowCabinetType.DIY then
        cabinetId = 1
    end
    local totalValue = 0
    local seasonLimitedValue = 0
    local cabinetGroup = Server.CollectionRoomServer.idToCabinetDataMap[cabinetType]
    if cabinetGroup then
        local cabinetData = cabinetGroup[cabinetId]
        if cabinetData then
            for _, cabinetGridData in pairs(cabinetData.grids) do
                if cabinetGridData.item and cabinetGridData.item.id ~= 0 then
                    local value = Server.ShopServer:GetShopDynamicGuidePrice(cabinetGridData.item.id)
                    if cabinetType == EShowCabinetType.Display and CollectionRoomLogic.IsLimitedGrid(cabinetType, cabinetGridData.id) then
                        seasonLimitedValue = seasonLimitedValue + value
                    else
                        totalValue = totalValue + value
                    end
                end
            end
        end
    end
    return totalValue, seasonLimitedValue
end

function CollectionRoomLogic.GetItemNumByCabinet(cabinetType, cabinetId)
    if cabinetType ~= EShowCabinetType.DIY then
        cabinetId = 1
    end
    if not Server.CollectionRoomServer.idToCabinetDataMap[cabinetType] then
        return 0
    end
    local cabinetData = Server.CollectionRoomServer.idToCabinetDataMap[cabinetType][cabinetId]
    if not cabinetData then
        return 0
    end
    local num = 0
    for _, grid in pairs(cabinetData.grids) do
        if grid.item then
            if cabinetType ~= EShowCabinetType.Display or not CollectionRoomLogic.IsLimitedGrid(cabinetType, grid.id) then
                num = num + 1
            end
        end
    end
    return num
end

function CollectionRoomLogic._OnCollectionRoomCabinetGridChange(cabinetGridDiffType, cabinetType, cabinetId, gridId, bFraming)
    loginfo("CollectionRoomLogic._OnCollectionRoomCabinetGridChange", cabinetGridDiffType, cabinetType, cabinetId, gridId, bFraming)
    local handleFunc = function()
        if cabinetGridDiffType == ECabinetGridDiffType.Add then
            CollectionRoomLogic.SpawnPickupActor(cabinetType, cabinetId, gridId)
            if cabinetType == EShowCabinetType.Display then
                if not CollectionRoomLogic.IsLimitedGrid(cabinetType, gridId) then
                    CollectionRoomLogic._ModifyDisplayCabinetBracketTexture(function()
                        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetBracketTexture", gridId, {-1, -1, 1})
                    end)
                end
            end
        elseif cabinetGridDiffType == ECabinetGridDiffType.Remove then
            CollectionRoomLogic.DestroyPickupActor(cabinetType, cabinetId, gridId)
            CollectionRoomLogic._CheckSpawnTempPickupActor(cabinetType, cabinetId, gridId)
            if cabinetType == EShowCabinetType.Display then
                if not CollectionRoomLogic.IsLimitedGrid(cabinetType, gridId) then
                    CollectionRoomLogic._ModifyDisplayCabinetBracketTexture(function()
                        CollectionRoomLogic.CallCtrlFunction("WriteToDisplayCabinetBracketTexture", gridId, {-1, -1, 0})
                    end)
                end
            end
        elseif cabinetGridDiffType == ECabinetGridDiffType.LevelUp then
            CollectionRoomLogic.OnCabinetGridLevelUp(cabinetType, cabinetId, gridId)
        elseif cabinetGridDiffType == ECabinetGridDiffType.LockState then
            if cabinetType == EShowCabinetType.Special then
                -- 解锁珍藏柜时一定要重设等级，避免还在使用未解锁时的
                CollectionRoomLogic._SetSpecialCabinet(true)
            end
        end
    end
    if bFraming then
        Facade.LuaFramingManager:RegisterFrameTask(handleFunc)
    else
        handleFunc()
    end
end

function CollectionRoomLogic._ModifyDisplayCabinetTexture(modifyFuc)
    if type(modifyFuc) ~= "function" then
        return
    end
    CollectionRoomLogic.CallCtrlFunction("LockTextureBulkData")
    trycall(modifyFuc)
    CollectionRoomLogic.CallCtrlFunction("UnLockTextureBulkData")
end

function CollectionRoomLogic._ModifyDisplayCabinetBracketTexture(modifyFuc)
    if type(modifyFuc) ~= "function" then
        return
    end
    CollectionRoomLogic.CallCtrlFunction("LockBracketTextureBulkData")
    trycall(modifyFuc)
    CollectionRoomLogic.CallCtrlFunction("UnLockBracketTextureBulkData")
end

function CollectionRoomLogic._OnStackUIChanged()
    local mainPanel = Facade.UIManager:GetStackUIByUINavId(UIName2ID.CollectionRoomMainPanel)
    if not getField().mainPanel and mainPanel then
        getField().mainPanel = mainPanel
        UGPAudioStatics.SetEmoteSoundOn()
        CollectionRoomLogic._OnStateChange(nil, nil, true)
        if ItemOperaTool.bIsInCollectionRoom then
            CollectionRoomLogic.SendCollectionRoomQuitFlow(ECollectionRoomLeaveFrom.EnterMainPanel)
        end
    elseif getField().mainPanel and not mainPanel then
        getField().mainPanel = nil
        UGPAudioStatics.SetEmoteSoundOff()
        CollectionRoomLogic.SendCollectionFlow()
        CollectionRoomLogic._OnStateChange(nil, nil, false)
        CollectionRoomLogic.CallCtrlFunction("ResetDisplayType")
        if ItemOperaTool.bIsInCollectionRoom then
            getField().enterCollectionRoomMethod = ECollectionRoomEnterFrom.EscFromMainPanel
            getField().enterCollectionRoomTime = os.date("%Y-%m-%d %H:%M:%S")
            CollectionRoomLogic.SetViewTargetToPawn()
        else
            if not getField().bIsPopingAllUIForEnterCollectionRoom then
                CollectionRoomLogic.UnLoadCollectionRoomLevels(true)
            end
        end
    end
    local stackUICount = Facade.UIManager:GetStackUICount()
    local bInCollectionRoomSubStage = Facade.GameFlowManager:GetCurrentSubStage() == ESubStage.CollectionRoom
    if ItemOperaTool.bIsInCollectionRoom and stackUICount == 0 and not bInCollectionRoomSubStage then
        loginfo("CollectionRoomLogic._OnStackUIChanged to 0")
        Facade.GameFlowManager:EnterSubStage(ESubStage.CollectionRoom)
    end
end

function CollectionRoomLogic._OnMatchStateChanged()
    if Server.MatchServer:GetIsWaitForGotoGame() then
        CollectionRoomLogic.LeaveCollectionRoom(ECollectionRoomLeaveFrom.Match, true)
        CollectionRoomLogic._StopCollectionRoomProcess("MatchState")
    end
end

function CollectionRoomLogic._StopCollectionRoomProcess(reason)
    -- 如果是匹配成功或者进局内的Loading时在打开收藏室场景或界面的异步流程中，那么阻止流程继续进行
    -- 否则可能被被拉回到收藏室场景或界面
    if getField().bIsInCollectionRoomProcess then
        logerror("CollectionRoomLogic._StopCollectionRoomProcess stop collection room process", reason)
        local localCtrl = UGameplayStatics.GetPlayerController(GetWorld(), 0)
        if isvalid(localCtrl) then
            localCtrl:StopLoadOrUnLoadCollectionRoom()
        end
        getField().bIsInCollectionRoomProcess = false
    end
end

function CollectionRoomLogic.ResetAllPickupActors()
    loginfo("CollectionRoomLogic.ResetAllPickupActors")
    for _, cabinetGroup in pairs(Server.CollectionRoomServer.idToCabinetDataMap) do
        for _, cabinetData in pairs(cabinetGroup) do
            for _, cabinetGridData in pairs(cabinetData.grids) do
                Facade.LuaFramingManager:RegisterFrameTask(function()
                    UCollectionRoomPickupActorManager.Get(GetWorld()):ResetPickupActor(cabinetData.type, cabinetData.id, cabinetGridData.id)
                end)
            end
        end
    end
end

function CollectionRoomLogic.GetUpgradeItems(cabinetType, slotID)
    if getField().upgradeItems and getField().upgradeItems[cabinetType] and getField().upgradeItems[cabinetType][slotID] then
        return getField().upgradeItems[cabinetType][slotID]
    else
        logerror("CollectionRoomLogic.GetUpgradeItems attempt to index nil")
    end
end

function CollectionRoomLogic.SpawnLimitedPickupActor(cabinetType, gridId)
    loginfo("CollectionRoomLogic.SpawnLimitedPickupActor", cabinetType, gridId)
    local spawnPoint = UGPQuestUtils.GetActorByName(GetWorld(), "ItemSpawnPointNew")
    if spawnPoint then
        local cabinetItem = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, 1, gridId)
        if cabinetItem then
            local pickupActor = getField().limitedPickupActor
            if isvalid(pickupActor) then
                if cabinetItem.id == tonumber(pickupActor.InventoryIdName) then
                    pickupActor:SetActorHiddenInGame(false)
                    return
                end
                pickupActor:K2_DestroyActor()
                getField().limitedPickupActor = nil
            end
            local loc = UCollectionRoomLimitedCollectionWrap.GetLocationByItemID(ULuautils.GetUInt64String(cabinetItem.id))
            pickupActor = APickupManager.SpawnPickupActor(tostring(cabinetItem.id), cabinetItem.gid, FVector.ZeroVector, FRotator.ZeroRotator, false, GetWorld(), false, false)
            if isvalid(pickupActor) then
                pickupActor.bIsSeasonLimited = true
                loc = spawnPoint:K2_GetActorLocation() + loc
                pickupActor:K2_SetActorLocation(loc, false, HitResult, false)
                local rotatePoint = UGPQuestUtils.GetActorByName(GetWorld(), "ItemRotatePoint")
                if rotatePoint then
                    rotatePoint:K2_SetActorLocation(loc, false, HitResult, false)
                    pickupActor:K2_AttachToActor(rotatePoint, "None", EAttachmentRule.KeepRelative, EAttachmentRule.KeepRelative, EAttachmentRule.KeepRelative, false)
                    pickupActor:K2_SetActorRelativeLocation(FVector.ZeroVector, false, HitResult, false)
                end
                getField().limitedPickupActor = pickupActor
            end
        end
    end
end

function CollectionRoomLogic.ShowOrHideLimitedPickupActor(bShow)
    loginfo("CollectionRoomLogic.ShowOrHideLimitedPickupActor", bShow)
    local pickupActor = getField().limitedPickupActor
    if isvalid(pickupActor) then
        pickupActor:SetActorHiddenInGame(not bShow)
    end
end

function CollectionRoomLogic.DestroyLimitedPickupActor()
    loginfo("CollectionRoomLogic.DestroyLimitedPickupActor")
    local pickupActor = getField().limitedPickupActor
    if isvalid(pickupActor) then
        pickupActor:K2_DestroyActor()
    end
    getField().limitedPickupActor = nil
end

function CollectionRoomLogic.GetItemIdByCabinetTypeAndGridId(cabinetType, gridId)
    local table = Facade.TableManager:GetTable("CollectionRoomCollection")
    if table then
        for _, config in pairs(table) do
            if (cabinetType == EShowCabinetType.Display and config.SlotType == 1)
                    or (cabinetType == EShowCabinetType.Special and config.SlotType == 2) then
                if config.SlotID == gridId then
                    return tonumber(config.ItemID)
                end
            end
        end
    end
    return 0
end

function CollectionRoomLogic.IsLimitedGrid(cabinetType, gridId)
    return cabinetType == EShowCabinetType.Display and getField().limitedGridId[gridId] or false
end

function CollectionRoomLogic.IsVirtualGrid(cabinetType, gridId)
    if cabinetType == EShowCabinetType.Display then
        local item = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, 1, gridId)
        if not item then
            return true
        end
    end
    return false
end

-----------------------------------------------------------------------
--region   读配置表接口

function CollectionRoomLogic.GetItemCapacityByCabinet(cabinetType, cabinetId)
    local capacity = 0
    if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
        local table = Facade.TableManager:GetTable("CollectionRoomCollection")
        if table then
            for _, config in pairs(table) do
                if not config.IsLimited then
                    if (cabinetType == EShowCabinetType.Display and config.SlotType == 1)
                            or (cabinetType == EShowCabinetType.Special and config.SlotType == 2) then
                        capacity = capacity + 1
                    end
                end
            end
        end
    else
        local table = Facade.TableManager:GetTable("CollectionDIYCabinet")
        if table then
            for _, config in pairs(table) do
                if config.DIYRackID == cabinetId then
                    capacity = capacity + 1
                end
            end
        end
    end
    return capacity
end

function CollectionRoomLogic.GetDIYCabinetNum()
    local table = Facade.TableManager:GetTable("CollectionDIYCabinet")
    local diyRackIDs = {}
    local diyCabinetNum = 0
    if table then
        for _, config in pairs(table) do
            if not diyRackIDs[config.DIYRackID] then
                diyRackIDs[config.DIYRackID] = true
                diyCabinetNum = diyCabinetNum + 1
            end
        end
    end
    return diyCabinetNum
end

---@desc 使用格子ID获取收藏柜/珍藏柜对应格子的配置
function CollectionRoomLogic.GetDisplayOrSpecialGridConfig(cabinetType, gridId)
    local table = Facade.TableManager:GetTable("CollectionRoomCollection")
    if table then
        for _, config in pairs(table) do
            if ((cabinetType == EShowCabinetType.Display and config.SlotType == 1)
                or (cabinetType == EShowCabinetType.Special and config.SlotType == 2))
                and config.SlotID == gridId then
                return config
            end
        end
    end
end

-- 根据展柜id获取各个DIY展柜的展位组（大型/小型/通用）数量
function CollectionRoomLogic.GetDIYCabinetBoothNum(cabinetID)
    local collectionDIYCabinetData = getField().collectionDIYCabinetData
    if not collectionDIYCabinetData[cabinetID] then
        return 0
    end
    local num = 0
    for _, _ in pairs(collectionDIYCabinetData[cabinetID]) do
        num = num + 1
    end
    return num
end

---@desc 使用格子ID获取展示台对应格子的配置
function CollectionRoomLogic.GetDIYCabinetGridConfig(cabinetId, gridId)
    local table = Facade.TableManager:GetTable("CollectionDIYCabinet")
    if table then
        for _, config in pairs(table) do
            if config.DIYRackID == cabinetId and config.RackSlotID == gridId then
                return config
            end
        end
    end
end

---@desc 根据分类获取格子道具信息
function CollectionRoomLogic.GetGridInfoByType()
    local gridInfoTable = Facade.TableManager:GetTable("CollectionRoomCollection")
    local type2GridInfo = {}
    if gridInfoTable then
        for _, config in ipairs(gridInfoTable) do
            declare_if_nil(type2GridInfo, config.Type, {})
            -- 判断限定
            if CollectionRoomLogic.IsDisplayByItemId(config.itemID) then
                table.insert(type2GridInfo[config.Type], config)
            end
        end
    end
    return type2GridInfo
end

-- 根据道具id判断是否是赛季限定道具
function CollectionRoomLogic.IsSeasonLimitedItem(itemID)
    local bIsLimited = false
    local collectionInfos = Facade.TableManager:GetTable("CollectionRoomCollection")
    if collectionInfos then
        for _, config in pairs(collectionInfos) do
            if itemID == config.ItemID and config.IsLimited then
                bIsLimited = true
                break
            end
        end
    end
    return bIsLimited
end


function CollectionRoomLogic.ShowUpgradePanel(cabinetType, gridId, item, bLeaveFocusModeOnHide)
    loginfo("CollectionRoomLogic.ShowUpgradePanel", cabinetType, gridId, item, bLeaveFocusModeOnHide)
    local bIsLimited = false
    local table = Facade.TableManager:GetTable("CollectionRoomCollection")
    if table then
        for _, config in pairs(table) do
            if (config.SlotType == 1 and cabinetType == EShowCabinetType.Display)
                or (config.SlotType == 2 and cabinetType == EShowCabinetType.Special) then
                if config.SlotID == gridId then
                    bIsLimited = config.IsLimited
                    break
                end
            end
        end
    end
    local UIName = "CollectionUpgradePanel"
    if bIsLimited then
        UIName = UIName .. "WithLevel"
    end
    Facade.UIManager:AsyncShowUI(UIName2ID[UIName], nil, nil, cabinetType, gridId, item, bIsLimited, bLeaveFocusModeOnHide)
end

function CollectionRoomLogic.FocusOnDisplaySpecialCabinetGrid(cabinetType, gridId, bHideVirtual)
    loginfo("CollectionRoomLogic.FocusOnDisplaySpecialCabinetGrid", cabinetType, gridId)
    local currFocusGridAddr = getField().currFocusGridAddr
    if currFocusGridAddr and currFocusGridAddr.cabinetType == cabinetType and currFocusGridAddr.gridId == gridId then
        loginfo(string.format("CollectionRoomDisplaySpecialCabinetPanel:FocusOnDisplaySpecialCabinetGrid don't repeating set cabinetType=%d grid=%d", cabinetType, gridId or 0))
        return
    end
    local config = CollectionRoomLogic.GetDisplayOrSpecialGridConfig(cabinetType, gridId)
    if not config then
        logerror(string.format("CollectionRoomDisplaySpecialCabinetPanel:FocusOnDisplaySpecialCabinetGrid fail to get config for cabinetType=%d grid=%d", cabinetType, gridId or 0))
        return
    end
    local cameraId = config.CameraID ~= 0 and config.CameraID or 1
    local cameraDisplayType = CollectionRoomConfig.CameraConfig.DisplayCabinet.DisplayType[cameraId].Focus
    local cameraActor = CollectionRoomLogic.CallCtrlFunction("GetCameraActorByDisplayType", cameraDisplayType)
    if not isvalid(cameraActor) then
        logerror(string.format("CollectionRoomDisplaySpecialCabinetPanel:FocusOnDisplaySpecialCabinetGrid fail to get CameraActor for DisplayType=%s", cameraDisplayType))
        return
    end
    CollectionRoomLogic.DestroyTempPickupActor(false)
    getField().targetFocusGridAddr = {cabinetType = cabinetType, cabinetId = 1, gridId = gridId}
    if CollectionRoomLogic.IsVirtualGrid(cabinetType, gridId) then
        local pickupActor = CollectionRoomLogic.SpawnPickupActor(cabinetType, 1, gridId, tonumber(config.ItemID))
        if isvalid(pickupActor) and bHideVirtual then
            pickupActor:SetActorHiddenInGame(true)
        end
        return
    end
    CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor(cabinetType, gridId)
end

function CollectionRoomLogic._OnPickupActorMeshCreated(pickupActor)
    if not isvalid(pickupActor) then
        return
    end
    loginfo("CollectionRoomLogic._OnPickupActorMeshCreated", pickupActor)
    CollectionRoomLogic._TrySetFakeLightMaterialForPickupActor(pickupActor)
    if Facade.GameFlowManager:GetCurrentSubStage() ~= ESubStage.CollectionRoom then
        return
    end
    local addr = getField().targetFocusGridAddr or getField().currFocusGridAddr
    if not addr then
        return
    end
    if pickupActor ~= CollectionRoomLogic.GetPickupActor(addr.cabinetType, addr.cabinetId, addr.gridId) then
        logerror(string.format("CollectionRoomLogic._OnPickupActorMeshCreated pickupActor is not cabinetType=%d gridId=%d", addr.cabinetType, addr.gridId or 0))
        return
    end
    local config = CollectionRoomLogic.GetDisplayOrSpecialGridConfig(addr.cabinetType, addr.gridId)
    if not config then
        logerror(string.format("CollectionRoomLogic._OnPickupActorMeshCreated fail to get config for cabinetType=%d gridId=%d", addr.cabinetType, addr.gridId or 0))
        return
    end
    if not Server.CollectionRoomServer:GetCabinetItemByGrid(addr.cabinetType, addr.cabinetId, addr.gridId) then
         CollectionRoomLogic._SetVirtualMaterialForPickupActor(pickupActor)
    end
    if getField().targetFocusGridAddr then
        CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor(addr.cabinetType, addr.gridId)
    end
end

function CollectionRoomLogic._TrySetFakeLightMaterialForPickupActor(pickupActor)
    if isvalid(pickupActor) and pickupActor.ShowCabinetType == UE.EShowCabinetType.Display then
        local placeActor = pickupActor:GetAttachParentActor()
        if isvalid(placeActor) then
            local gridData = Server.CollectionRoomServer:GetGridData(placeActor.CabinetType, placeActor.CabinetId, placeActor.GridId)
            if gridData.item and gridData.level and gridData.level >= 2 then
                CollectionRoomLogic._SetFakeLightMaterialForPickupActor(pickupActor)
            end
        end
    end
end

function CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor(cabinetType, gridId)
    loginfo("CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor", cabinetType, gridId)
    local pickupActor
    if cabinetType == EShowCabinetType.Display then
        pickupActor = CollectionRoomLogic.GetPickupActor(cabinetType, 1, gridId)
        if not isvalid(pickupActor) then
            return
        end
    end
    local config = CollectionRoomLogic.GetDisplayOrSpecialGridConfig(cabinetType, gridId)
    if not config then
        logerror(string.format("CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor fail to get config for cabinetType=%d gridId=%d", cabinetType, gridId or 0))
        return
    end
    local cameraId = config.CameraID > 0 and config.CameraID or 1
    local cameraDisplayType
    if cabinetType == EShowCabinetType.Special then
        cameraDisplayType = CollectionRoomConfig.CameraConfig.SpecialCabinet.Focus
    elseif cabinetType == EShowCabinetType.Display then
        cameraDisplayType = CollectionRoomConfig.CameraConfig.DisplayCabinet.DisplayType[cameraId].Focus
    end
    local cameraActor = CollectionRoomLogic.CallCtrlFunction("GetCameraActorByDisplayType", cameraDisplayType)
    if not isvalid(cameraActor) then
        logerror(string.format("CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor fail to get CameraActor for DisplayType=%s", cameraDisplayType))
        return
    end
    local focusLocation
    local cameraDistance = defaultCameraDistance
    local globalDistanceRatio
    if cabinetType == EShowCabinetType.Display then
        globalDistanceRatio = CollectionRoomLogic.GetCtrlProperty("DisplayCabinetGlobalCameraDistanceRatio")
        local min = FVector.ZeroVector
        local max = FVector.ZeroVector
        local bGet = false
        bGet, min, max = pickupActor:GetLocalBounds(min, max)
        local bUseCalculatedDistance = true
        if CollectionRoomConfig.bUseCameraTypeOrDistanceRatio then
            if config.CameraType > 0 then
                local distanceArray = CollectionRoomLogic.GetCtrlProperty("DisplayCabinetCameraDistanceArray")
                if distanceArray and distanceArray[config.CameraType] then
                    cameraDistance = distanceArray[config.CameraType]
                    bUseCalculatedDistance = false
                end
            end
        end
        if bGet then
            local center = (min + max) / 2
            focusLocation = UKismetMathLibrary.TransformLocation(pickupActor:GetTransform(), center)
            if bUseCalculatedDistance then
                local size = max - min
                size = math.max(math.max(math.abs(size.X), math.abs(size.Y)), math.abs(size.Z))
                cameraDistance = 4 * size * 2 ^ (- size / 100)
            end
        else
            logwarning(string.format("CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor fail to get meshComponent for cabinetType=%d gridId=%d, focus PlaceActor", cabinetType, gridId or 0))
            local placeActor = CollectionRoomLogic.GetPlaceActorByCabinetAndGrid(cabinetType, 1, gridId)
            if not isvalid(placeActor) then
                logerror(string.format("CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor fail to get PlaceActor for cabinetType=%d gridId=%d", cabinetType, gridId or 0))
                return
            end
            focusLocation = placeActor:GetActorLocation()
        end
    elseif cabinetType == EShowCabinetType.Special then
        globalDistanceRatio = CollectionRoomLogic.GetCtrlProperty("SpecialCabinetGlobalCameraDistanceRatio")
        local configDistance = CollectionRoomLogic.GetCtrlProperty("SpecialCabinetCameraDistance")
        if configDistance > 0 then
            cameraDistance = configDistance
        end
        focusLocation = CollectionRoomLogic.CallSpecialCabinetFunction(gridId, "GetCameraFocusLocation")
    end
    if globalDistanceRatio <= 0 then
        globalDistanceRatio = 1
    end
    local itemDistanceRatio = config.CameraDistanceRatio
    if itemDistanceRatio <= 0 then
        itemDistanceRatio = 1
    end
    cameraDistance = cameraDistance * globalDistanceRatio * itemDistanceRatio
    local biasDirection
    if cabinetType == EShowCabinetType.Display then
        biasDirection = CollectionRoomLogic.GetCtrlProperty("DisplayCabinetCameraBiasDirection")
    elseif cabinetType == EShowCabinetType.Special then
        biasDirection = CollectionRoomLogic.GetCtrlProperty("SpecialCabinetCameraBiasDirection")
    end
    if not biasDirection or biasDirection:IsNearlyZero(0.000001) then
        logwarning(string.format("CollectionRoomLogic._FocusOnDisplaySpecialCabinetPickupActor fail to get CameraBiasDirection for cabinetType=%d gridId=%d, use default one", cabinetType, gridId or 0))
        biasDirection = FVector(50, 300, 0)
    end
    local relativeDirection = biasDirection:GetSafeNormal(0.000001)
    local forwardVector = UKismetMathLibrary.GetForwardVector(cameraActor:GetActorRotation())
    forwardVector.X = - forwardVector.X
    forwardVector.Y = - forwardVector.Y
    forwardVector.Z = - forwardVector.Z
    local rightVector = UKismetMathLibrary.GetRightVector(cameraActor:GetActorRotation())
    local upVector = UKismetMathLibrary.GetUpVector(cameraActor:GetActorRotation())
    local relativePosition = (rightVector * relativeDirection.X + forwardVector * relativeDirection.Y + upVector * relativeDirection.Z) * cameraDistance
    local cameraActorNewLocation = focusLocation + relativePosition
    cameraActor:K2_SetActorLocation(cameraActorNewLocation, false, HitResult, false)
    CollectionRoomLogic.SetViewTargetToNamedCamera(cameraDisplayType, true)
    CollectionRoomLogic.DestroyTempPickupActor(true)
    getField().currFocusGridAddr = getField().targetFocusGridAddr
    getField().targetFocusGridAddr = nil
end

function CollectionRoomLogic._SetFakeLightMaterialForPickupActor(pickupActor)
    if not isvalid(pickupActor) then
        return
    end
    local component = pickupActor:GetComponentByClass(UPickupStaticMeshComponent)
    if not isvalid(component) then
        component = pickupActor:GetComponentByClass(USkeletalMeshComponent)
    end
    if not isvalid(component) then
        return
    end
    local materials = component:GetMaterials()
    for index, material in ipairs(materials) do
        local UMaterialInstanceDynamic = import "MaterialInstanceDynamic"
        local dynamicMaterial
        if issubclass(LAI.GetObjectClass(material), UMaterialInstanceDynamic) then
            dynamicMaterial = material
        else
            dynamicMaterial = UKismetMaterialLibrary.CreateDynamicMaterialInstance(self, material, "None")
            component:SetMaterial(index-1, dynamicMaterial)
        end
        if isvalid(dynamicMaterial) then
            dynamicMaterial:SetScalarParameterValue("FakeLightSwitch", 1)
        end
    end
end

function CollectionRoomLogic._SetVirtualMaterialForPickupActor(pickupActor)
    if not isvalid(pickupActor) then
        return
    end
    local virtualMaterial = CollectionRoomLogic.CallCtrlFunction("GetPickupVirtualMaterial")
    if virtualMaterial then
        local function func(component, material, index)
            if isvalid(material) and isvalid(material.MaterialInterface) then
                local dynamicMaterial = UKismetMaterialLibrary.CreateDynamicMaterialInstance(self, virtualMaterial, "None")
                if isvalid(dynamicMaterial) then
                    local baseColorMap = ULuaExtension.GetTextureParameterValue("BaseColorMap", material.MaterialInterface)
                    dynamicMaterial:SetTextureParameterValue("BaseColorMap", baseColorMap)
                    local NRMMap = ULuaExtension.GetTextureParameterValue("NRMMap", material.MaterialInterface)
                    dynamicMaterial:SetTextureParameterValue("NRMMap", NRMMap)
                    component:SetMaterial(index-1, dynamicMaterial)
                end
            end
        end
        local staticMeshComponent = pickupActor:GetComponentByClass(UPickupStaticMeshComponent)
        if isvalid(staticMeshComponent) then
            local staticMesh = staticMeshComponent:GetStaticMesh()
            if isvalid(staticMesh) then
                for index, staticMaterial in ipairs(staticMesh.StaticMaterials) do
                    func(staticMeshComponent, staticMaterial, index)
                end
            end
        else
            local skeletalMeshComponent = pickupActor:GetComponentByClass(USkeletalMeshComponent)
            if isvalid(skeletalMeshComponent) then
                local skeletalMesh = skeletalMeshComponent.SkeletalMesh
                if isvalid(skeletalMesh) then
                    for index, skeletalMaterial in ipairs(skeletalMesh.Materials) do
                        func(skeletalMeshComponent, skeletalMaterial, index)
                    end
                end
            end
        end
    end
end

function CollectionRoomLogic.LeaveFocusMode()
    loginfo("CollectionRoomLogic.LeaveFocusMode")
    CollectionRoomLogic.DestroyTempPickupActor(true)
    CollectionRoomLogic.DestroyTempPickupActor(false)
    getField().currFocusGridAddr = nil
    getField().targetFocusGridAddr = nil
end

function CollectionRoomLogic.IsDisplayByItemId(itemID)
    local displayTable = Facade.TableManager:GetTable("Collection2DDisplay")
    for _, info in pairs(displayTable) do
        if info.ItemID == tostring(itemID) then
            local curTimetamp = Facade.ClockManager:GetLocalTimestamp()
            local specifiedTimeStr = info.LimitedRevealTime
            local Y, M, D, h, m, s = string.match(specifiedTimeStr, "(%d+)-(%d+)-(%d+),(%d+):(%d+):(%d+)")
            local specifiedTimetamp = os.time({year=Y, month=M, day=D, hour=h, min=m, sec=s})
            return curTimetamp > specifiedTimetamp
        end
    end
    return true
end

function CollectionRoomLogic.GetDisplayCabinetGridLevelCache(inGridId)
    return getField().displayGridIdToLevel[inGridId]
end

function CollectionRoomLogic.UpdateDisplayCabinetGridLevelCache(inGridId, inLevel)
    loginfo("CollectionRoomLogic.UpdateDisplayCabinetGridLevelCache", inGridId, inLevel)
    if getField().displayGridIdToLevel[inGridId] == inLevel then
        return
    end
    getField().displayGridIdToLevel[inGridId] = inLevel
    local array = {}
    for gridId, level in pairs(getField().displayGridIdToLevel) do
        table.insert(array, string.format("%d:%d", gridId, level))
    end
    Facade.ConfigManager:SetUserString("DisplayCabinetLevels", table.concat(array, ","))
end

--endregion
-----------------------------------------------------------------------


-----------------------------------------------------------------------
--region 移动道具

-- 移动道具至展柜
function CollectionRoomLogic.DoPlaceCabinet(item, cabinetType, cabinetId, targetGridId)
    -- 根据item判断该道具位于哪个位置
    if not item then return end

    if Server.InventoryServer:GetItemByGid(item.gid) then
        local shelveItemInfo = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, cabinetId, targetGridId)
        if shelveItemInfo then
            -- 交换
            local targetItem = ItemBase:New(shelveItemInfo.id, shelveItemInfo.num, shelveItemInfo.gid)
            Server.CollectionRoomServer:ExchangeShowCabinet(cabinetType, cabinetId, targetGridId, item, targetItem)
        else
            -- 上架
            Server.CollectionRoomServer:ShelveShowCabinet(cabinetType, cabinetId, targetGridId, item.gid)
        end
    elseif item:IsCollectionCabinetItem() then
        if item.gridId ~= targetGridId then
            Server.CollectionRoomServer:MoveShowCabinet(cabinetType, cabinetId, item.gridId, targetGridId)
        end
    end
end

-- 下架展柜道具
function CollectionRoomLogic.WithdrawShowCabinet(cabinetId, gridId, itemLocation)
    local cabinetType
    if ItemOperaTool:CheckIsDIYCabinetPanel() then
        cabinetType = EShowCabinetType.DIY
    end
    Server.CollectionRoomServer:WithdrawShowCabinet(cabinetType, cabinetId, gridId, itemLocation)
end

-- 根据id找到仓库中已有的道具并放入指定grid
function CollectionRoomLogic.PlaceCollectionByItemID(cabinetType, cabinetId, gridId, itemId)
    local items = CollectionRoomLogic.GetCollectionItemsByCondition(itemId, true)
    CollectionRoomLogic.DoPlaceCabinet(items[1], cabinetType, cabinetId, gridId)
end

-- 将指定展柜的道具放入至仓库中（第一版接口
function CollectionRoomLogic.UnEquipItemAnyWhere(cabinetType, cabinetId, gridId, item)
    -- 默认卸载至主仓库
    local targetSlot
    local allDepositIds = Server.InventoryServer:GetAllDepositIds()
    allDepositIds = table.unique(allDepositIds, true)
    for _, depositId in ipairs(allDepositIds) do
        local depositSlot = Server.InventoryServer:GetSlot(depositId)
        if depositSlot and depositSlot:CheckItemFitSlot(item) then
            local itemLocation = depositSlot:TryFindLocationForItem(item)
            if itemLocation then
                Server.CollectionRoomServer:WithdrawShowCabinet(cabinetType, cabinetId, gridId, itemLocation)
                return
            end
        end
    end
    Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.Loc.UnablePutinWH)

    -- if targetSlot then
    --     local itemLocation = targetSlot:TryFindLocationForItem(item)

    -- else
    --     Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.Loc.UnablePutinWH)
    -- end
end

--endregion
-----------------------------------------------------------------------


---@desc 该道具是否可以放入对应diy展柜
function CollectionRoomLogic.CanPutInDIYCabinetGrid(item, cabinetInfo)
    -- 判断该展位是否有道具上架
    local shelveItemInfo = Server.CollectionRoomServer:GetCabinetItemByGrid(EShowCabinetType.DIY, 1, cabinetInfo.RackSlotID)
    if shelveItemInfo then
        if item:IsCollectionCabinetItem() then
            -- 交换要考虑互换道具的尺寸是否一致
            local targetItem = ItemBase:New(shelveItemInfo.id, shelveItemInfo.num, shelveItemInfo.gid)
            if CollectionRoomLogic.CheckCabinetCanPutInItem(item, cabinetInfo) and CollectionRoomLogic.CheckCabinetCanPutInItem(targetItem, item._cabinetInfo) then
                return true
            else
                return false, CollectionRoomConfig.EDIYPlaceFailReason.SizeSwapMisMatch
            end
        else
            -- 将展位上的道具与仓库道具进行互换
            local isValid, reason = CollectionRoomLogic.CheckCabinetCanPutInItem(item, cabinetInfo)
            if isValid and CollectionRoomLogic.CheckCabinetCanExchangeItem(shelveItemInfo, item) then
                return true
            else
                if isValid then
                    reason = CollectionRoomConfig.EDIYPlaceFailReason.ExchangeFailed
                end
                return false, reason
            end
        end
    else
        return CollectionRoomLogic.CheckCabinetCanPutInItem(item, cabinetInfo)
    end
end

-- 当前道具是否满足该展位放置条件
function CollectionRoomLogic.CheckCabinetCanPutInItem(item, cabinetInfo)
    if not item then
        return
    end
    if not item.id then
        return
    end

    -- 暂定15开头的道具都能放入DIY展柜、有明确banneditemid的不能放入、有明确格子大小的item不能放入
    local itemMainID = ItemHelperTool.GetMainTypeById(item.id)
    local cabinetSlotType = cabinetInfo.SlotType
    local banneditemid = tonumber(cabinetInfo.BannedItemID)
    if itemMainID ~= 15 then
        return false, CollectionRoomConfig.EDIYPlaceFailReason.ItemTypeMisMatch
    end
    if item:CheckIsBind() then
        return false, CollectionRoomConfig.EDIYPlaceFailReason.BindItemCantPlace
    end
    if banneditemid ~= 0 and item.id == banneditemid then
        return false
    end
    if cabinetSlotType == 1 and (item.length > 1 or item.width > 1) then
        -- 只能上架1x1
        return false, CollectionRoomConfig.EDIYPlaceFailReason.SizeMisMatch
    end
    -- 收藏室参数表.xlsx里配置的ItemIDBanExemptionPrefix能放入DIY展柜
    local itemIDString = ULuautils.GetUInt64String(item.id)
    local ItemIDBanExemptionPrefix = UCollectionRoomConstantWrap.GetConstantByName("ItemIDBanExemptionPrefix")
    local prefixArray = string.split(ItemIDBanExemptionPrefix, ",")
    for _, prefix in pairs(prefixArray) do
        if string.starts_with(itemIDString, prefix) then
            return true
        end
    end
    -- 收藏室参数表.xlsx里配置的ItemIDBanPrefix不能放入DIY展柜
    local itemIDBanPrefix = UCollectionRoomConstantWrap.GetConstantByName("ItemIDBanPrefix")
    prefixArray = string.split(itemIDBanPrefix, ",")
    for _, prefix in pairs(prefixArray) do
        if string.starts_with(itemIDString, prefix) then
            return false, CollectionRoomConfig.EDIYPlaceFailReason.ItemTypeMisMatch
        end
    end
    return true
end

-- 展位藏品能否和仓库藏品进行交换
function CollectionRoomLogic.CheckCabinetCanExchangeItem(shelveItemInfo, item)
    local targetItem = ItemBase:New(shelveItemInfo.id, shelveItemInfo.num, shelveItemInfo.gid)

    -- 1. 交换时比较交换两个物品的尺寸大小
    local targetLen = targetItem.width
    local targetWidth = targetItem.length

    if targetItem.length <= item.length and targetItem.width <= item.width then
        return true
    elseif targetLen <= item.length and targetWidth <= item.width then
        return true
    end

    -- 2. 如果被交换的尺寸过大，则判断仓库是否有空间放入。
    local allDepositIds = Server.InventoryServer:GetAllDepositIds()
    allDepositIds = table.unique(allDepositIds, true)
    for _, depositId in ipairs(allDepositIds) do
        local depositSlot = Server.InventoryServer:GetSlot(depositId)
        if depositSlot and depositSlot:CheckItemFitSlot(item) then
            local itemLocation = depositSlot:TryFindLocationForItem(item)
            if itemLocation then
                return true
            end
        end
    end

    return false
end

-- 通过错误码弹出失败tips
function CollectionRoomLogic.ShowPlaceDIYFaildTip(faildReason)
    local tipsTxt = CollectionRoomConfig.PlaceFailReasonTips[faildReason]
    Module.CommonTips:ShowSimpleTip(tipsTxt)
end

function CollectionRoomLogic.GetItemInfoByCabinetType(cabinetType, slotID)
    local gridIDToItem = getField().gridIdToItemMap
    if gridIDToItem and gridIDToItem[cabinetType] and gridIDToItem[cabinetType][slotID] then
        return gridIDToItem[cabinetType][slotID]
    end
end

function CollectionRoomLogic._SetSpecialCabinet(bForce)
    bForce = setdefault(bForce, false)
    -- 如果升起来过，那么就不再强制设置珍藏柜等级，防止破坏了机械臂的姿态
    if not bForce and not (getField().bFirstEnterAfterLogin and not getField().bSpecialCabinetAnimHasTriggered) then
        return
    end
    local grids = Server.CollectionRoomServer:GetGridDataMap(EShowCabinetType.Special)
    if getField().gridIdToItemMap then
        local specialGridNum = table.nums(getField().gridIdToItemMap[EShowCabinetType.Special])
        for gridId = 1, specialGridNum do
            local cabinetGridData = grids and grids[gridId]
            if not cabinetGridData or CollectionRoomLogic.IsSpecialCabinetRegardedAsLocked() then
                CollectionRoomLogic.SetSpecialCabinetLevel(gridId, -1)
            else
                CollectionRoomLogic.SetSpecialCabinetLevel(cabinetGridData.id, cabinetGridData.level)
            end
        end
    end
end

function CollectionRoomLogic._SetConsoleVars(bInSubStage)
    local cmds = {}

    -- 收藏室关闭阴影屏占比剔除
    local radiusThreshold = 0
    if bInSubStage and getField().oldRadiusThreshold < 0 then
        getField().oldRadiusThreshold = UGPUserInterfaceUtil.FindConsoleVariable_Float("r.Shadow.RadiusThreshold")
        table.insert(cmds, string.format("r.Shadow.RadiusThreshold %f", radiusThreshold))
    elseif not bInSubStage and getField().oldRadiusThreshold >= 0 then
        radiusThreshold = getField().oldRadiusThreshold
        getField().oldRadiusThreshold = -1
        table.insert(cmds, string.format("r.Shadow.RadiusThreshold %f", radiusThreshold))
    end

	for _, cmd in ipairs(cmds) do
        UKismetSystemLibrary.ExecuteConsoleCommand(
            GetGameInstance(),
            cmd,
            nil
        )
    end
end

function CollectionRoomLogic._ReportTGPA(bEnterCollectionRoom)
    local targetState = bEnterCollectionRoom and DFMGameDataKeyForStoryState.CommonGame or DFMGameDataKeyForStoryState.Lobby
    UDFMGameGPM.UpdateGameInfo(targetState)
end

function CollectionRoomLogic.SetSpecialCabinetLevel(gridId, level)
    loginfo("CollectionRoomLogic.SetSpecialCabinetLevel", gridId, level)
    local funcName = string.format("Lv%d_Final", math.max(level+1, 0))
    CollectionRoomLogic.RefreshSpecialCabinetState(gridId)
    CollectionRoomLogic.CallSpecialCabinetFunction(gridId, funcName)
end

function CollectionRoomLogic.PlayAllSpecialCabinetAnim()
    loginfo("CollectionRoomLogic.PlayAllSpecialCabinetAnim")
    local grids = Server.CollectionRoomServer:GetGridDataMap(EShowCabinetType.Special)
    if grids then
        for _, cabinetGridData in pairs(grids) do
            CollectionRoomLogic.PlaySpecialCabinetAnim(cabinetGridData.id, cabinetGridData.level)
        end
    end
end

function CollectionRoomLogic.PlaySpecialCabinetAnim(gridId, level, bLevelUp)
    loginfo("CollectionRoomLogic.PlaySpecialCabinetAnim", gridId, level, bLevelUp)
    bLevelUp = setdefault(bLevelUp, false)
    level = setdefault(level, 0)
    local funcName
    if bLevelUp then
        funcName = string.format("Lv%d-%d", level, level+1)
        CollectionRoomLogic.PlaySpecialCabinetLevelUpAudio(gridId, level)
    else
        funcName = string.format("Lv%d", level+1)
    end
    CollectionRoomLogic.RefreshSpecialCabinetState(gridId)
    CollectionRoomLogic.CallSpecialCabinetFunction(gridId, funcName)
end

function CollectionRoomLogic.RefreshSpecialCabinetState(gridId, bIsEmpty)
    loginfo("CollectionRoomLogic.RefreshSpecialCabinetState", gridId, bIsEmpty)
    bIsEmpty = setdefault(bIsEmpty, CollectionRoomLogic.CheckSpecialGridEmpty(gridId))
    local bIsLocked = CollectionRoomLogic.IsSpecialCabinetRegardedAsLocked()
    local gridLevel = bIsLocked and -1 or Server.CollectionRoomServer:GetCabinetGridLevel(EShowCabinetType.Special, 1, gridId)
    CollectionRoomLogic.CallSpecialCabinetFunction(gridId, bIsEmpty and "LensFlareOff" or "LensFlareOn")
    CollectionRoomLogic.CallSpecialCabinetFunction(gridId, (bIsEmpty or gridLevel < 1) and "LightOff" or "LightOn")
    CollectionRoomLogic.SetSpecialCabinetParam(gridId, "IsPlace", not bIsEmpty)
    CollectionRoomLogic.PauseOrReplayAnim(gridId, bIsEmpty or gridLevel < 2)
end

function CollectionRoomLogic.PauseRobotArmAnim()
    local specialGridNum = table.nums(getField().gridIdToItemMap[EShowCabinetType.Special])
    for gridId = 1, specialGridNum do
        CollectionRoomLogic.PauseOrReplayAnim(gridId, true)
    end
end

function CollectionRoomLogic.TryReplayRobotArmAnim()
    local specialGridNum = table.nums(getField().gridIdToItemMap[EShowCabinetType.Special])
    for gridId = 1, specialGridNum do
        local bIsEmpty = CollectionRoomLogic.CheckSpecialGridEmpty(gridId)
        local bIsLocked = CollectionRoomLogic.IsSpecialCabinetRegardedAsLocked()
        local gridLevel = bIsLocked and -1 or Server.CollectionRoomServer:GetCabinetGridLevel(EShowCabinetType.Special, 1, gridId)
        if not (bIsEmpty or gridLevel < 2) then
            CollectionRoomLogic.PauseOrReplayAnim(gridId, false)
        end
    end
end

function CollectionRoomLogic.IsSpecialCabinetRegardedAsLocked()
    return (not Server.CollectionRoomServer:IsCabinetUnlocked(EShowCabinetType.Special))
            or (ItemOperaTool.bIsInCollectionRoom and getField().mainPanel == nil
            and getField().bFirstEnterAfterLogin and not getField().bSpecialCabinetAnimHasTriggered)
end

function CollectionRoomLogic.PauseOrReplayAnim(gridId, bPause)
    bPause = setdefault(bPause, false)
    loginfo("CollectionRoomLogic.PauseOrReplayAnim", gridId, bPause)
    local funcName
    if bPause then
        funcName = "PauseAnims"
    else
        funcName = "ReplayAnims"
    end
    CollectionRoomLogic.CallSpecialCabinetFunction(gridId, funcName)
end

function CollectionRoomLogic.CheckSpecialGridEmpty(gridId)
    local pickupActor = UCollectionRoomPickupActorManager.Get(GetWorld()):GetPickupActor(EShowCabinetType.Special, 1, gridId)
    return not isvalid(pickupActor) or not pickupActor:GetAttachParentActor()
end

function CollectionRoomLogic.CallSpecialCabinetFunction(gridId, funcName)
    if funcName == nil then
        return
    end
    local actor = CollectionRoomLogic.CallCtrlFunction("GetActorByKey", "SpecialCabinetActor"..gridId)
    if isvalid(actor) and actor[funcName] then
        return actor[funcName](actor)
    end
end

function CollectionRoomLogic.SetSpecialCabinetParam(gridId, paramName, value)
    if paramName == nil or value == nil then
        return
    end
    local actor = CollectionRoomLogic.CallCtrlFunction("GetActorByKey", "SpecialCabinetActor"..gridId)
    if isvalid(actor) and actor[paramName] ~= nil then
        actor[paramName] = value
    end
end

-- 退出收藏室场景上报
function CollectionRoomLogic.SendCollectionRoomQuitFlow(leaveFrom)
    loginfo("CollectionRoomLogic.SendCollectionRoomQuitFlow")
    local CollectionRoomQuitFlow = pb.CollectionRoomQuitFlow:New()
    CollectionRoomQuitFlow.PlayerId = Server.AccountServer:GetPlayerId()
    CollectionRoomQuitFlow.EnterTime = getField().enterCollectionRoomTime or ""
    CollectionRoomQuitFlow.LeaveTime = os.date("%Y-%m-%d %H:%M:%S")
    CollectionRoomQuitFlow.EnterMethod = getField().enterCollectionRoomMethod or 0
    CollectionRoomQuitFlow.LeaveMethod = leaveFrom or ECollectionRoomLeaveFrom.None
    CollectionRoomQuitFlow.PickupTimes = getField().pickupTimes or 0
    LogAnalysisTool.AddTglog(CollectionRoomQuitFlow)
    getField().enterCollectionRoomMethod = nil
    getField().enterCollectionRoomTime = nil
    getField().pickupTimes = 0
end

-- 收藏室流水上报
function CollectionRoomLogic.SendCollectionFlow()
    loginfo("CollectionRoomLogic.SendCollectionFlow")
    local CollectionFlow = pb.CollectionFlow:New()
    local CollectionProgress = {}
    local CollectionLevel = {}
    local DIYProgress = {}
    local CollectionValue = {}
    ---@type table<number,table<number,number>>
    local IdToValueMap = {}
    local TotalCostValue = 0
    local TotalStoreValue = 0
    for cabinetType, cabinetGroup in pairs(Server.CollectionRoomServer.idToCabinetDataMap) do
        for cabinetId, cabinetData in pairs(cabinetGroup) do
            declare_if_nil(IdToValueMap, cabinetType, {})
            declare_if_nil(IdToValueMap[cabinetType], cabinetId, 0)
            if cabinetType == EShowCabinetType.Display then
                declare_if_nil(IdToValueMap, 4, {})
                declare_if_nil(IdToValueMap[4], cabinetId, 0)
            end
            for gridId, cabinetGridData in pairs(cabinetData.grids) do
                if cabinetType == EShowCabinetType.Display or cabinetType == EShowCabinetType.Special then
                    if cabinetGridData.item and cabinetGridData.item.id ~= 0 then
                        table.insert(CollectionProgress, ULuautils.GetUInt64String(cabinetGridData.item.id))
                        table.insert(CollectionLevel, cabinetGridData.item.id .. "," .. cabinetGridData.level)
                        local value = Server.ShopServer:GetShopDynamicGuidePrice(cabinetGridData.item.id)
                        local cabinet_type = cabinetType
                        if CollectionRoomLogic.IsLimitedGrid(cabinetType, cabinetGridData.id) then
                            cabinet_type = 4
                        end
                        IdToValueMap[cabinet_type][cabinetId] = IdToValueMap[cabinet_type][cabinetId] + value
                        TotalStoreValue = TotalStoreValue + value
                    else
                        local config = CollectionRoomLogic.GetDisplayOrSpecialGridConfig(EShowCabinetType.Display, gridId)
                        if config then
                            table.insert(CollectionLevel, config.ItemID .. "," .. cabinetGridData.level)
                        end
                    end
                    if cabinetGridData.level > 0 then
                        for level = 1, cabinetGridData.level do
                            if getField().upgradeItems and getField().upgradeItems[cabinetType]
                                    and getField().upgradeItems[cabinetType][cabinetGridData.id]
                                    and getField().upgradeItems[cabinetType][cabinetGridData.id][level] then
                                local upgradeItems = getField().upgradeItems[cabinetType][cabinetGridData.id][level]
                                for index = 1, #upgradeItems.items do
                                    local itemId = upgradeItems.items[index]
                                    if not string.isempty(itemId) then
                                        local quantity = tonumber(upgradeItems.quantity[index])
                                        if itemId == "17020000010" then
                                            TotalCostValue = TotalCostValue + quantity
                                        else
                                            TotalCostValue = TotalCostValue + Server.ShopServer:GetShopDynamicGuidePrice(tonumber(itemId)) * quantity
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
                if cabinetType == EShowCabinetType.DIY then
                    if cabinetGridData.item and cabinetGridData.item.id ~= 0 then
                        table.insert(DIYProgress, table.concat({cabinetId, gridId, ULuautils.GetUInt64String(cabinetGridData.item.id)}, ","))
                        local value = Server.ShopServer:GetShopDynamicGuidePrice(cabinetGridData.item.id)
                        IdToValueMap[cabinetType][cabinetId] = IdToValueMap[cabinetType][cabinetId] + value
                        TotalStoreValue = TotalStoreValue + value
                    else
                        table.insert(DIYProgress, table.concat({cabinetId, gridId, 0}, ","))
                    end
                end
            end
            table.insert(CollectionValue, table.concat({cabinetType, cabinetId, IdToValueMap[cabinetType][cabinetId]}, ","))
            if cabinetType == EShowCabinetType.Display then
                table.insert(CollectionValue, table.concat({4, cabinetId, IdToValueMap[4][cabinetId]}, ","))
            end
        end
    end
    CollectionProgress = table.concat(CollectionProgress, ",")
    CollectionLevel = table.concat(CollectionLevel, ";")
    DIYProgress = table.concat(DIYProgress, ";")
    CollectionValue = table.concat(CollectionValue, ";")
    CollectionFlow.PlayerId = Server.AccountServer:GetPlayerId()
    CollectionFlow.CollectionProgress = CollectionProgress or ""
    CollectionFlow.CollectionLevel = CollectionLevel or ""
    CollectionFlow.DIYProgress = DIYProgress or ""
    CollectionFlow.CollectionValue = CollectionValue or ""
    CollectionFlow.UIOpenMethod = getField().enterMainPanelMethod or 0
    CollectionFlow.TotalCostValue = TotalCostValue and string.format("%d", math.round(TotalCostValue)) or "0"
    CollectionFlow.TotalStoreValue = TotalStoreValue and string.format("%d", math.round(TotalStoreValue)) or "0"
    local diyCabinetInteractNum = {}
    for index, num in pairs(getField().diyCabinetInteractNum) do
        table.insert(diyCabinetInteractNum, string.format("%d,%d", index, num))
    end
    getField().diyCabinetInteractNum = {}
    CollectionFlow.DIYInteractNum = table.concat(diyCabinetInteractNum, ";")
    LogAnalysisTool.AddTglog(CollectionFlow)
    getField().enterMainPanelMethod = nil
end

-- 判断当前展位是否可升级
function CollectionRoomLogic.IsCabinetCanLevelUp(cabinetType, gridId)
    if not Server.CollectionRoomServer:IsCabinetUnlocked(cabinetType) then
        return false
    end
    local gridLevel = Server.CollectionRoomServer:GetCabinetGridLevel(cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, gridId)
    gridLevel = gridLevel or 0

    local itemList = CollectionRoomLogic.GetUpgradeItems(cabinetType, gridId)[gridLevel + 1]
    local bShelveItem = Server.CollectionRoomServer:GetCabinetItemByGrid(cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, gridId)
    if table.isempty(itemList) or not bShelveItem then
        return false
    end
    local upgradItem2Info = {}

    for key, value in ipairs(itemList.items) do
        -- 哈夫币单独算
        if value ~= "17020000010" then
            declare_if_nil(upgradItem2Info, key, {})
            upgradItem2Info[key].itemID = tonumber(value)
            upgradItem2Info[key].item2Bind = tonumber(itemList.bindInfo[key])
            upgradItem2Info[key].item2Quan = tonumber(itemList.quantity[key])
        else
            upgradItem2Info.currency = tonumber(itemList.quantity[key])
        end
    end
    local canLevelUp = true

    -- 首先判断价格是否满足条件
    if upgradItem2Info.currency then
        canLevelUp = upgradItem2Info.currency > Server.InventoryServer:GetPlayerCurrencyNum() and false or true
    end

    -- 各个道具数量是否足够，有一个不足就返回false
    for index, value in ipairs(upgradItem2Info) do
        local function fIsSpecifyBindItem(item)
            return item.id == value.itemID and not item:CheckIsBind()
        end
        local function fIsSpecifyUnBindItem(item)
            return item.id == value.itemID
        end
        local items
        -- 首先从仓库拿到满足条件的道具
        if tonumber(value.item2Bind) == 0 then
            items = Server.InventoryServer:GetItemsByCondition(fIsSpecifyBindItem)
        elseif tonumber(value.item2Bind) == 1  then
            items = Server.InventoryServer:GetItemsByCondition(fIsSpecifyUnBindItem)
        end
        if table.isempty(items) or #items < tonumber(value.item2Quan) then
            canLevelUp = false
            break
        end
    end
    return canLevelUp
end

-- 当上架红点被点击时
function CollectionRoomLogic.ClickRedPoint(itemId, bShelve)
    local displayAndSpecialItemList
    if bShelve then
        displayAndSpecialItemList = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.CollectionRoomShelveMap)
    else
        displayAndSpecialItemList = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.CollectionRoomLevelMap)
    end
    displayAndSpecialItemList[tostring(itemId)] = true
    local tipsRecordKey = bShelve and Server.TipsRecordServer.keys.CollectionRoomShelveMap or Server.TipsRecordServer.keys.CollectionRoomLevelMap
    Server.TipsRecordServer:SetMap(tipsRecordKey, displayAndSpecialItemList)
    Server.CollectionRoomServer.Events.evtIsClickedReddot:Invoke()
end

-- 查找该柜子红点是否被点击
function CollectionRoomLogic.IsClickRedPoint(itemId, bShelve)
    local displayAndSpecialItemList
    if bShelve then
        displayAndSpecialItemList = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.CollectionRoomShelveMap)
    else
        displayAndSpecialItemList = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.CollectionRoomLevelMap)
    end
    return displayAndSpecialItemList[tostring(itemId)]
end

-- 仓库道具上架DIY展位
-- tips：因为是配表和ui显示的序号相反，所以需要倒着遍历，但是由于没有1，所以需要特殊处理
function CollectionRoomLogic.ShevleDIYCabinet(item, diyCabinetId, DIYCabinetInfo)
    CollectionRoomLogic.DIYCabinetInteractNumSelfIncreaseOne(diyCabinetId)
    -- 首先找到哪些展柜没有上架
    local rackSlotInfoList = {}
    for sortID, rackSlotList in pairs(DIYCabinetInfo) do
        rackSlotInfoList[sortID - 1] = rackSlotList
        -- table.insert(rackSlotInfoList, rackSlotList)
    end
    local isValid, faildReason
    for i = #rackSlotInfoList, 1, -1 do
        -- 首先判断该道具是否可上架该展位，然后再判断该展位是否已有道具上架
        for _, rackSlotInfo in ipairs(rackSlotInfoList[i]) do
            isValid, faildReason = CollectionRoomLogic.CanPutInDIYCabinetGrid(item, rackSlotInfo)
            if isValid then
                local alreaydItem = Server.CollectionRoomServer:GetCabinetItemByGrid(EShowCabinetType.DIY, diyCabinetId, rackSlotInfo.RackSlotID)
                if not alreaydItem then
                    CollectionRoomLogic.DoPlaceCabinet(item, EShowCabinetType.DIY, diyCabinetId, rackSlotInfo.RackSlotID)
                    return
                end
            else
                -- 有些不合法的直接return
                if faildReason == CollectionRoomConfig.EDIYPlaceFailReason.ItemTypeMisMatch
                or faildReason == CollectionRoomConfig.EDIYPlaceFailReason.BindItemCantPlace then
                    CollectionRoomLogic.ShowPlaceDIYFaildTip(faildReason)
                    return
                end
            end
        end
    end
    Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.PlaceFailReasonTips[2])
end

-- 根据道具直接上架展位
function CollectionRoomLogic.DoPlaceItemToCabinet(item)
    -- 获取当前的展位列表数据
    local cabinetData = getField().collectionDIYCabinetData
    for cabinetID, cabinetInfo in ipairs(cabinetData) do
        CollectionRoomLogic.ShevleDIYCabinet(item, cabinetID, cabinetInfo)
    end
end

-- 将收藏品从展位上卸下(仅针对diy列表的道具)
function CollectionRoomLogic.UnShelveDIYCabinet(itemStruct, targetSlot)
    if itemStruct:IsCollectionCabinetItem() then
        if not targetSlot then
            local allDepositIds = Server.InventoryServer:GetAllDepositIds()
            local curSelectDepositId = Module.Inventory:GetCurDepositId()
            table.insert(allDepositIds, 1, curSelectDepositId)
            allDepositIds = table.unique(allDepositIds, true)
            for _, depositId in ipairs(allDepositIds) do
                local depositSlot = Server.InventoryServer:GetSlot(depositId)
                if depositSlot and depositSlot:CheckItemFitSlot(itemStruct) then
                    local targetLocation = depositSlot:TryFindLocationForItem(itemStruct)
                    if targetLocation then
                        CollectionRoomLogic.WithdrawShowCabinet(itemStruct.cabinetId, itemStruct.gridId, targetLocation)
                        return
                    end
                end
            end
        end
    Module.CommonTips:ShowSimpleTip(CollectionRoomConfig.Loc.UnablePutinWH)
    end
end

-- 根据id获取当前仓库所拥有的收藏品数量(不包括背包胸挂)
function CollectionRoomLogic.GetCollectionItemsNum(itemID, bUnBind)
    local UnBindCondition = function (item)
        return item.id == tonumber(itemID) and not item:CheckIsBind() --and item.InSlot:IsExtContainerSlot()
    end
    local BindCondition = function (item)
        return item.id == tonumber(itemID) --and item.InSlot:IsExtContainerSlot()
    end

    local bindConditionFunc = bUnBind and UnBindCondition or BindCondition
    return Server.InventoryServer:GetItemNumByCondition(bindConditionFunc)
end

-- 根据id获取当前仓库所拥有的收藏品数量(不包括背包胸挂)
function CollectionRoomLogic.GetCollectionItemsByCondition(itemID, bUnBind)
    local fIsUnBindItem = function(item)
        return item.id == tonumber(itemID) and not item:CheckIsBind() --and item.InSlot:IsExtContainerSlot()
    end
    local fIsBindSpecifyItem = function(item)
        return item.id == tonumber(itemID) --and item.InSlot:IsExtContainerSlot()
    end
    local bindConditionFunc = bUnBind and fIsUnBindItem or fIsBindSpecifyItem
    return Server.InventoryServer:GetItemsByCondition(bindConditionFunc)
end

-- 将限定收藏品的价值从图鉴展柜价值中筛选出来
function CollectionRoomLogic.GetDisplayCabinetValue(cabinetID)
    local displayCabinetPrice = 0
    local displayCabinet = Server.CollectionRoomServer.idToCabinetDataMap and Server.CollectionRoomServer.idToCabinetDataMap[EShowCabinetType.Display]
    local cabinet = displayCabinet and displayCabinet[cabinetID]
    for gridId, grid in pairs(cabinet.grids) do
        -- 限定不算入展柜的总价值
        if not CollectionRoomLogic.IsLimitedGrid(EShowCabinetType.Display, gridId) and  grid.price then
            displayCabinetPrice = displayCabinetPrice + grid.price
        end
    end
    return displayCabinetPrice
end


-- 通过道具类型id获取该类型的藏品数量
function CollectionRoomLogic.GetTypeItemNum(itemType)
    local gridInfoTable = Facade.TableManager:GetTable("CollectionRoomCollection")
    local type2GridInfo = {}
    local itemsNum = 0
    local unLockNums = 0
    if gridInfoTable then
        for _, config in ipairs(gridInfoTable) do
            if itemType == 9 then
                -- 全部
                if CollectionRoomLogic.IsDisplayByItemId(config.itemID) then
                    itemsNum = itemsNum + 1
                    local cabinetItem = Server.CollectionRoomServer:GetCabinetItemByGrid(config.SlotType, 1, config.SlotID)
                    if cabinetItem then
                        unLockNums = unLockNums + 1
                    end
                end
            elseif itemType == 10 then
                -- 赛季限定
                if CollectionRoomLogic.IsSeasonLimitedItem(tostring(config.itemID)) and CollectionRoomLogic.IsDisplayByItemId(config.itemID) then
                    itemsNum = itemsNum + 1
                    local cabinetItem = Server.CollectionRoomServer:GetCabinetItemByGrid(config.SlotType, 1, config.SlotID)
                    if cabinetItem then
                        unLockNums = unLockNums + 1
                    end
                end
            else
                -- 根据分类
                if config.Type == itemType and CollectionRoomLogic.IsDisplayByItemId(config.itemID) then
                    itemsNum = itemsNum + 1
                    local cabinetItem = Server.CollectionRoomServer:GetCabinetItemByGrid(config.SlotType, 1, config.SlotID)
                    if cabinetItem then
                        unLockNums = unLockNums + 1
                    end
                end
            end
        end
    end
    return itemsNum, unLockNums
end

-----------------------------------------------------------------------
--region Reddot JudgeFunc

function CollectionRoomLogic.JudgeHaveItemCanShevle(cabinetType)
    local collectionItems = Facade.TableManager:GetTable("CollectionRoomCollection")
    for _, itemInfo in ipairs(collectionItems) do
        if itemInfo.SlotType == cabinetType and Server.CollectionRoomServer:IsCabinetUnlocked(itemInfo.SlotType) and CollectionRoomLogic.CheckCabinetShelveFun(itemInfo) then
            return true
        end
    end
    return false
end

function CollectionRoomLogic.CheckCabinetShelveFun(itemInfo)
    local bShevle = Server.CollectionRoomServer:GetCabinetItemByGrid(itemInfo.SlotType, 1, itemInfo.SlotID)
    local itemId = itemInfo.ItemID
    if bShevle or CollectionRoomLogic.IsClickRedPoint(itemId, true) or itemInfo.IsLimited then
        return false
    else
        local itemNumInWH = CollectionRoomLogic.GetCollectionItemsNum(tonumber(itemId), true)
        return itemNumInWH > 0
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region Filter and Sort Func

function CollectionRoomLogic.GetFilterList(itemList, idx)
    local filterList = {}
    if idx == 0 then
        filterList = itemList
    elseif idx == 1 then
        -- 只显示已上架
        filterList = CollectionRoomLogic.FilterByShelve(itemList, true)
    elseif idx == 2 then
        -- 只显示未上架
        filterList = CollectionRoomLogic.FilterByShelve(itemList, false)
    elseif idx == 3 then
        filterList = CollectionRoomLogic.FilterByLimit(itemList)
    end
    return filterList
end

function CollectionRoomLogic.UpdateListBySort(itemList, idx)
    local function sortByValueUP(a, b)
        local itemA = ItemBase:NewIns(a.itemID)
        local itemB = ItemBase:NewIns(b.itemID)
        if itemA:GetSingleSellPrice() == itemB:GetSingleSellPrice() then
            return a.ID < b.ID
        else
            return itemA:GetSingleSellPrice() < itemB:GetSingleSellPrice()
        end
    end

    local function sortByValueDown(a, b)
        local itemA = ItemBase:NewIns(a.itemID)
        local itemB = ItemBase:NewIns(b.itemID)
        if itemA:GetSingleSellPrice() == itemB:GetSingleSellPrice() then
            return a.ID < b.ID
        else
            return itemA:GetSingleSellPrice() > itemB:GetSingleSellPrice()
        end
    end
    
    local function sortByLevelUP(a, b)
        local itemALevel = Server.CollectionRoomServer:GetCabinetGridLevel(a.cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, a.gridID)
        itemALevel = a.isLimited and -1 or itemALevel
        local itemBLevel = Server.CollectionRoomServer:GetCabinetGridLevel(b.cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, b.gridID)
        itemBLevel = b.isLimited and -1 or itemBLevel
        if itemALevel == itemBLevel then
            return a.ID < b.ID
        else
            return itemALevel < itemBLevel
        end
    end

    local function sortByLevelDown(a, b)
        local itemALevel = Server.CollectionRoomServer:GetCabinetGridLevel(a.cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, a.gridID)
        itemALevel = a.isLimited and -1 or itemALevel
        local itemBLevel = Server.CollectionRoomServer:GetCabinetGridLevel(b.cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, b.gridID)
        itemBLevel = b.isLimited and -1 or itemBLevel
        if itemALevel == itemBLevel then
            return a.ID < b.ID
        else
            return itemALevel > itemBLevel
        end
    end
    if idx == 0 then
        
    elseif idx == 1 then
        table.sort(itemList, sortByValueUP)
    elseif idx == 2 then
        table.sort(itemList, sortByValueDown)
    elseif idx == 3 then
        table.sort(itemList, sortByLevelUP)
    elseif idx == 4 then
        table.sort(itemList, sortByLevelDown)
    end

end

function CollectionRoomLogic.FilterByShelve(itemList, bShelve)
    local list = {}
    for index, config in ipairs(itemList) do
        local itemPropInfo = Server.CollectionRoomServer:GetCabinetItemByGrid(config.cabinetType, CollectionRoomConfig.EDisplayCabinet.DisplayCabinet_1, config.gridID)
        if bShelve and itemPropInfo then
            table.insert(list, config)
        elseif not bShelve and not itemPropInfo then
            table.insert(list, config)
        end
    end
    return list
end

function CollectionRoomLogic.FilterByLimit(itemList)
    local list = {}
    for index, config in ipairs(itemList) do
        local isLimitedItem = CollectionRoomLogic.IsSeasonLimitedItem(tostring(config.itemID))
        if isLimitedItem then
            table.insert(list, config)
        end
    end
    return list
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region CollectionListPanel SearchBar Records

function CollectionRoomLogic.GetSearchRecords()
    loginfo("CollectionRoomLogic.GetSearchRecords")
    return Facade.ConfigManager:GetUserArray("CollectionRoomSearchRecords", {})
end

function CollectionRoomLogic.RemoveSearchRecord(propId)
    loginfo("CollectionRoomLogic.RemoveSearchRecord")
    local tmpRecords = Facade.ConfigManager:GetUserArray("CollectionRoomSearchRecords", {})
    table.removebyvalue(tmpRecords, propId)
    Facade.ConfigManager:SetUserArray("CollectionRoomSearchRecords", tmpRecords)
end

function CollectionRoomLogic.AddSearchRecord(propId)
    loginfo("CollectionRoomLogic.AddSearchRecord")
    local tmpRecords = Facade.ConfigManager:GetUserArray("CollectionRoomSearchRecords", {})
    if not table.contains(tmpRecords, propId) then
        table.insert(tmpRecords, 1, propId)
    else
        table.removebyvalue(tmpRecords, propId)
        table.insert(tmpRecords, 1, propId)
    end
    
    if #tmpRecords > 30 then
        table.remove(tmpRecords, #tmpRecords)
    end
    Facade.ConfigManager:SetUserArray("CollectionRoomSearchRecords", tmpRecords)
end

function CollectionRoomLogic.DIYCabinetInteractNumSelfIncreaseOne(cabinetId)
    if cabinetId then
        getField().diyCabinetInteractNum[cabinetId] = (getField().diyCabinetInteractNum[cabinetId] or 0) + 1
    end
end

--endregion
-----------------------------------------------------------------------



return CollectionRoomLogic

