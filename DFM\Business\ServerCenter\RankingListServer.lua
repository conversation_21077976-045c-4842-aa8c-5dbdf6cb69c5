----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSReport)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class RankingListServer : ServerBase
local RankingListServer = class("RankingListServer", require("DFM.YxFramework.Managers.Server.ServerBase"))
local URankingListSetting = import "RankingListSetting"

local function log(...)
    loginfo("", "[RankingListServer]", ...)
end

local function printtable(t, prefix)
    log(prefix)
    logtable(t)
end

function RankingListServer:Ctor()
    self:OnInitData()
    self._localAdcode = 0
    self.Events = {
        evtGetRankListEnd = LuaEvent:NewIns("RankingListServer.evtGetRankListEnd"),
        evtGetRankMatchListEnd = LuaEvent:NewIns("RankingListServer.evtGetRankMatchListEnd"),
        evtRankingListRedDot = LuaEvent:NewIns("RankingListServer.evtRankingListRedDot"),
    }
    self._rankingListSetting = URankingListSetting:Get(GetWorld())

    self._bIsShowRedDot = self._rankingListSetting.bIsShowRedPoint
    self._bIsStopPeriod = false --是否为休赛期
    self._selfDayInterval = 7  -- 7天为1期
    self._bIsHistoryProcess = false -- 历史记录数据是否已经预处理(使用懒处理)
end

function RankingListServer:OnInitData()
    self._RankListTbl = {
        [RankDataType.SOL_SCORE] = {},
        [RankDataType.SOL_BRINGOUT_VALUE] = {},
        [RankDataType.SOL_KILL] = {},
        -- [RankDataType.] = {},
        -- [RankDataType.SOL_KILL_BOSS] = {},
        -- [RankDataType.SOL_MANDEL] = {},
        [RankDataType.TDM_SCORE] = {},
        [RankDataType.TDM_KILL] = {},
        [RankDataType.TDM_CAPTURE_ZONE_POINT] = {},
        [RankDataType.TDM_RESCUE_POINT] = {},
        -- [RankDataType.TDM_ENGINEER_POINT] = {},
        -- [RankDataType.TDM_TACTICAL_POINT] = {},
        [RankDataType.TDM_VICTORY_UNIT_SCORE] = {},
    }
    self._selfRankScore = {}
    self._selfRankData = {}
    self._selfBookData = {}
    self._selfDeltaData = {}
    self._selfHistoryData = {}
    self._seasonTimeTbl = {}  -- 赛季起始/结束表
    --timer锁支持
    self._RankListTimerTbl = {
        [RankDataType.SOL_SCORE] = {},
        [RankDataType.SOL_BRINGOUT_VALUE] = {},
        [RankDataType.SOL_KILL] = {},
        -- [RankDataType.] = {},
        -- [RankDataType.SOL_KILL_BOSS] = {},
        -- [RankDataType.SOL_MANDEL] = {},
        [RankDataType.TDM_SCORE] = {},
        [RankDataType.TDM_KILL] = {},
        [RankDataType.TDM_CAPTURE_ZONE_POINT] = {},
        [RankDataType.TDM_RESCUE_POINT] = {},
        -- [RankDataType.TDM_ENGINEER_POINT] = {},
        -- [RankDataType.TDM_TACTICAL_POINT] = {},
        [RankDataType.TDM_VICTORY_UNIT_SCORE] = {},
    }

    for key, value in pairs(self._RankListTimerTbl) do
        self._RankListTbl[key][1] = {}
        self._RankListTbl[key][2] = {}
        self._RankListTimerTbl[key][1] = {}
        self._RankListTimerTbl[key][2] = {}
        self._RankListTimerTbl[key][1].CanReFresh = true
        self._RankListTimerTbl[key][2].CanReFresh = true
    end
end

function RankingListServer:OnUnInstallData()
    self._selfRankScore = {}
    self._selfRankData = {}
    self._selfBookData = {}
    self._RankListTbl = {}
    self._RankListTimerTbl = {}
    self._selfHistoryData = {}
    self._seasonTimeTbl = {}
end

function RankingListServer:OnInitServer()
end

function RankingListServer:OnDestroyServer()
end

function RankingListServer:OnLoadingLogin2Frontend(gameFlowType)

end

function RankingListServer:OnLoadingGame2Frontend(gameFlowType)
    self:OnInitData()
end

function RankingListServer:OnLoadingFrontend2Game(gameFlowType)

end

function RankingListServer:OnGameFlowChangeEnter(gameFlowType)
    if gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse or gameFlowType == EGameFlowStageType.Lobby or gameFlowType == EGameFlowStageType.LobbyBHD then
        self:GetSelfRankScore()
        self:GetSelfHandBookData()
        self:GetRankArea()
        self:GetSeasonInfoData()
        self:GetHistoryData()
    elseif gameFlowType == EGameFlowStageType.Game then
        self:OnUnInstallData() --彻底进入局内再清除数据
    end
end

function RankingListServer:RefreshPlayerTitle(playerId, delayCall, isPrivacy)
    isPrivacy = setdefault(isPrivacy, false)
    local req = pb.CSAccountGetPlayerProfileReq:New()
    req.player_id = playerId
    req:Request(
        function(res)
            if res.result == 0 then
                if delayCall then
                    delayCall(res, isPrivacy)
                end
            end
        end
        ,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:FetchServerData()

end

function RankingListServer:SetTimerEnd(rankType, areaId)
    if areaId ~= 0 then
        if self._RankListTimerTbl[rankType][2] then
            self._RankListTimerTbl[rankType][2].Time = nil
            self._RankListTimerTbl[rankType][2].CanReFresh = true
        end
    else
        if self._RankListTimerTbl[rankType][1] then
            self._RankListTimerTbl[rankType][1].Time = nil
            self._RankListTimerTbl[rankType][1].CanReFresh = true
        end
    end
end

function RankingListServer:GetRankListBy(rankType, areaId)
    if not self._RankListTbl[rankType] then
        logerror("RankingListServer:GetRankListBy rankType not exist" , rankType , areaId)
        return
    end

    if areaId ~= 0 then
        if self._RankListTimerTbl[rankType][2] and self._RankListTimerTbl[rankType][2].areaId == areaId and (not self._RankListTimerTbl[rankType][2].CanReFresh) then
            self.Events.evtGetRankListEnd:Invoke(self._RankListTbl[rankType][2], rankType, areaId)
            loginfo("RankingListServer:GetRankListBy areaId havedate return")
            return
        end

        if self._RankListTbl[rankType][2] and #self._RankListTbl[rankType][2] ~= 0 then
            self._RankListTbl[rankType][2] = {}
        end

        self._RankListTimerTbl[rankType][2].Time = Timer:NewIns(5, 1)
        local timerCallback = CreateCallBack(self.SetTimerEnd, self, rankType, areaId)
        self._RankListTimerTbl[rankType][2].Time:AddListener(timerCallback)
        self._RankListTimerTbl[rankType][2].CanReFresh = false
        self._RankListTimerTbl[rankType][2].areaId = areaId
        self._RankListTimerTbl[rankType][2].Time:Start()
    else

        if self._RankListTimerTbl[rankType][1] and (not self._RankListTimerTbl[rankType][1].CanReFresh) then
            self.Events.evtGetRankListEnd:Invoke(self._RankListTbl[rankType][1], rankType, areaId)
            loginfo("RankingListServer:GetRankListBy havedate return")
            return
        end

        if self._RankListTbl[rankType][1] and #self._RankListTbl[rankType][1] ~= 0 then
            self._RankListTbl[rankType][1] = {}
        end

        self._RankListTimerTbl[rankType][1].Time = Timer:NewIns(5, 1)
        local timerCallback = CreateCallBack(self.SetTimerEnd, self, rankType, areaId)
        self._RankListTimerTbl[rankType][1].Time:AddListener(timerCallback)
        self._RankListTimerTbl[rankType][1].CanReFresh = false
        self._RankListTimerTbl[rankType][1].Time:Start()
    end
    self:_GetRankListByPagIndex(1, rankType, areaId)
end
    
function RankingListServer:_GetRankListByPagIndex(pagIndex, rankType, areaId)
    local OnCSRankGetListRes = function(res)
        if res.result == 0 then
            if res.page_max and pagIndex <= res.page_max then
                loginfo("FriendServer:_FetchFriendSectionList", pagIndex)
                for i , rankInfo in ipairs(res.list) do
                    if areaId ~= 0 then
                        table.insert(self._RankListTbl[rankType][2], rankInfo)
                    else
                        table.insert(self._RankListTbl[rankType][1], rankInfo)
                    end
                end
                if pagIndex == res.page_max then
                    if areaId ~= 0 then
                        self.Events.evtGetRankListEnd:Invoke(self._RankListTbl[rankType][2], rankType, areaId)
                    else
                        self.Events.evtGetRankListEnd:Invoke(self._RankListTbl[rankType][1], rankType, areaId)
                    end
                else
                    self:_GetRankListByPagIndex(pagIndex + 1, rankType, areaId)
                end
            else
                loginfo("RankingListServer:_GetRankListByPagIndex", pagIndex)
                if areaId ~= 0 then
                    self.Events.evtGetRankListEnd:Invoke(self._RankListTbl[rankType][2], rankType, areaId)
                else
                    self.Events.evtGetRankListEnd:Invoke(self._RankListTbl[rankType][1], rankType, areaId)
                end
            end
        end
    end

    local req = pb.CSRankGetListReq:New()
    req.page_num = pagIndex
    req.page_size = 100
    req.adcode = areaId
    req.rank_data_type = rankType
    req:Request(OnCSRankGetListRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end


function RankingListServer:SetRankPrivacy(isShowOnRank)
    local OnCSRankPrivacySetRes = function(res)
    end
    local req = pb.CSRankPrivacySetReq:New()
    req.is_show_on_rank = isShowOnRank
    req:Request(OnCSRankPrivacySetRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:GetRankArea(delayCall)
    local OnCSRankZoneGetRes = function(res)
        if res.result == 0 then
            self._selfAdcode = res.cur_adcode
            self._selfNextAdcode = res.next_adcode
            if delayCall then
                delayCall(res.cur_adcode, res.next_adcode)
            end
        end
    end
    local req = pb.CSRankZoneGetReq:New()
    req:Request(OnCSRankZoneGetRes,{bNeedResendAfterReconnected = true})
end

function RankingListServer:GetAdcode()
    return self._selfAdcode
end

function RankingListServer:GetNextAdcode()
    return self._selfNextAdcode
end

function RankingListServer:SetRankArea(adcode, delayCall)
    local OnCSRankZoneSetRes = function(res)
        if res.result == 0 then
            if delayCall then
                delayCall(tonumber(self._selfAdcode) == 0)
            end
            if tonumber(self._selfAdcode) == 0 then
                self._selfAdcode = adcode
                self:GetSelfHandBookData()
            else
                self._selfNextAdcode = adcode
            end
        end
    end
    local req = pb.CSRankZoneSetReq:New()
    req.rank_adcode = adcode
    req:Request(OnCSRankZoneSetRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:GetSelfRankScore()
    local OnCSRankPlayerAuxDataRes = function(res)
        if res.result == 0 then
            self._bIsStopPeriod = false
            for index, data in ipairs(res.rank_player_aux_data) do
                self._selfRankScore[data.rank_data_type] = data.score
                self._selfRankData = res.rank_player_aux_data
            end
        else
            self._bIsStopPeriod = true
        end
    end
    local req = pb.CSRankGetPlayerRankAuxDataReq:New()
    req.data_type = 0
    req:Request(OnCSRankPlayerAuxDataRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:GetIsStopPeriod()
    return self._bIsStopPeriod
end

function RankingListServer:GetSelfRankData()
    return self._selfRankData
end

function RankingListServer:GetSelfRankDataMap()
    return self._selfRankScore
end

function RankingListServer:GetSelfHandBookData()
    local OnCSRankGetHandbookRes = function(res)
        if res.result == 0 then
            self._selfBookData = {}
            for _, data in ipairs(res.rank_hand_book_data_list) do
                self._selfBookData[data.handbook_id] = data
            end
        end
    end
    local req = pb.CSRankGetHandbookReq:New()
    req.hand_book_id = 0
    req:Request(OnCSRankGetHandbookRes,{bNeedResendAfterReconnected = true})
end

function RankingListServer:GetSelfBookData()
    return self._selfBookData
end

function RankingListServer:GetPlayerRankMatchInfo(dataType)
    local OnCSRankGetPlayerRankAuxDataDetailRes = function(res)
        if res.result == 0 then
            self.Events.evtGetRankMatchListEnd:Invoke(res)
        end
    end
    local req = pb.CSRankGetPlayerRankAuxDataDetailReq:New()
    req.data_type = dataType
    req:Request(OnCSRankGetPlayerRankAuxDataDetailRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:SetAreaAcode(adcode)
    self._localAdcode = adcode
    local length = string.len(self._localAdcode)
    if length == 6 then
        self._localAdcode = string.sub(adcode, 1, 4) .. "00"
    end
end

function RankingListServer:GetLocalAcode()
    return self._localAdcode
end

function RankingListServer:GetbShowRedDot()
    return not self._bIsShowRedDot
end

function RankingListServer:SetRankingListCheck()
    self._rankingListSetting.bIsShowRedPoint = true
    self._rankingListSetting:SaveDataConfig()
    self.Events.evtRankingListRedDot:Invoke()
end

function RankingListServer:GetSeasonInfoData()
    local OnCSSeasonGetInfoRes = function(res)
        if res.result == 0 then
            local season_info = res.season_info
            local rank_summary = season_info.rank_summary
            local mp_rank_summary = season_info.mp_rank_summary
            self._selfDeltaData = {
                [RankDataType.SOL_SCORE] = {rank_summary.score_delta_by_mobile, rank_summary.score_delta_by_pc},  --sol变化分
                [RankDataType.TDM_SCORE] = {mp_rank_summary.score_delta_by_mobile, mp_rank_summary.score_delta_by_pc},  --mp变化分
                --  [RankDataType.SOL_SCORE] = {100, 40},  --sol变化分
                --  [RankDataType.TDM_SCORE] = {11, 41},  --mp变化分
            }
        end
    end
    local req = pb.CSSeasonGetInfoReq:New()
    req:Request(OnCSSeasonGetInfoRes,{bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:GetSelfDeltaData()
    return self._selfDeltaData
end

function RankingListServer:GetSeasonTimeTbl()
    local serialNumber = Server.TournamentServer:GetCurSerial()
    for index = 1, serialNumber do
        if IsBuildRegionCN() then
            local seasonConfig = Module.Tournament:GetSeasonConfigBySerial(index)
            table.insert(self._seasonTimeTbl, {
                StartTime = seasonConfig.StartTime,
                EndTime = seasonConfig.EndTime
            })
        else
            if index ~= 1 then
                local seasonConfig = Module.Tournament:GetSeasonConfigBySerial(index)
                table.insert(self._seasonTimeTbl, {
                StartTime = seasonConfig.StartTime,
                EndTime = seasonConfig.EndTime
            })
            end
        end
    end
end

function RankingListServer:GetHistoryData()
    -- 每个元素需要基于timestamp做倒序！
    -- mock
    -- local res = {
    --     {
    --         season_id = 5,
    --         season_data = {
    --            {rank_data_type = RankDataType.SOL_SCORE, title_id = 42030161314, adCode = 0, ranking_no = 4, timestamp = os.time(), region = RankBoardRegion.REGION_CITY, cycleinfo = 1},
    --            {rank_data_type = RankDataType.SOL_SCORE, title_id = 42030161226, adCode = 0, ranking_no = 4, timestamp = os.time(), region = RankBoardRegion.REGION_PREVINCE, cycleinfo = 2},
    --         },
    --     },
    --     {
    --         season_id = 4,
    --         season_data = {
    --             { rank_data_type = RankDataType.SOL_KILL, title_id = 42030161314, adCode = 2, ranking_no = 6, timestamp = self:_ToTimestamp("2025-04-30 12:00:00"), region = RankBoardRegion.REGION_CITY, cycleinfo = 1 },
    --         }
    --     },
    --     {
    --         season_id = 3,
    --         season_data = {
    --             { rank_data_type = RankDataType.SOL_KILL, title_id = 42030161314, adCode = 2, ranking_no = 6, timestamp = self:_ToTimestamp("2025-02-20 12:00:00"), region = RankBoardRegion.REGION_CITY, cycleinfo = 1 },
    --             { rank_data_type = RankDataType.SOL_BRINGOUT_VALUE, title_id = 42030162201, adCode = 2, ranking_no = 6, timestamp = self:_ToTimestamp("2025-02-20 12:00:00"), region = RankBoardRegion.REGION_ALL, cycleinfo = 1 },
    --         { rank_data_type = RankDataType.SOL_SCORE, title_id = 42030161226, adCode = 2, ranking_no = 6, timestamp = self:_ToTimestamp("2025-02-20 12:00:00"), region = RankBoardRegion.REGION_PREVINCE, cycleinfo = 1 },
    --         }
    --     },
    --     {
    --         season_id = 1,
    --         season_data = {
    --             { rank_data_type = RankDataType.SOL_KILL, title_id = 42030161314, adCode = 2, ranking_no = 6, timestamp = self:_ToTimestamp("2024-10-30 12:00:00"), region = RankBoardRegion.REGION_CITY, cycleinfo = 1 },
    --         }
    --     }
    -- }
    -- self._selfHistoryData = self:_HistoryPreProcess(res)
    local OnRankGetPlayerHistoricalRecordRes = function(res)
        if res.result == 0 then
            self._selfHistoryData = res.list
            self._bIsHistoryProcess = false
        end
    end
    local req = pb.CSRankGetPlayerHistoricalRecordReq:New()
    req:Request(OnRankGetPlayerHistoricalRecordRes, {bEnableHighFrequency = true, bNeedResendAfterReconnected = true})
end

function RankingListServer:GetSelfHistoryData()
    if not self._bIsHistoryProcess then
        self._bIsHistoryProcess = true
        self:GetSeasonTimeTbl()  -- 获取赛季时间表(这里会涉及其他服务的请求，存在依赖关系，使用懒加载破除)
        self._selfHistoryData = self:_HistoryPreProcess(self._selfHistoryData)
    end
    return self._selfHistoryData
end

function RankingListServer:_HistoryPreProcess(dataList)
    if dataList == nil then
        return {}
    end
    --data.country_rank_cnt = res.country_rank_cnt
    --data.province_rank_cnt = res.province_rank_cnt
    --data.city_rank_cnt = res.city_rank_cnt
    local serialNumber = Server.TournamentServer:GetCurSerial()
    local records = {}  -- 预处理后完整的list数据
    for i, item in ipairs(dataList) do  -- 遍历每个赛季数据
        if i > serialNumber then
            logwarning("RankingListServer:_HistoryPreProcess season_id invalid", item.season_id, serialNumber)
            break
        end
        local season_records = {}
        season_records.season_id = item.season_id
        -- 先根据timestamp倒序
        table.sort(item.season_data, function(a, b)
            return a.timestamp > b.timestamp
        end)
        local period_data = {}  -- 计算赛季内每期内容
        for key, value in pairs(item.season_data) do
            local cur_period = self:CalcPeriod(item.season_id, value.timestamp)  -- 算出是第几期数据
            local period_item = {
                titleId = value.title_id,
                adCode = value.adCode,
                ranking = value.ranking_no,
                rank_data_type = value.rank_data_type,
                region = value.region
            }
            -- 是否存在该期内容
            local content_ref = nil
            for _, _iter_season_data in ipairs(period_data) do
                if _iter_season_data.period == cur_period then  -- 存在当期内容
                    content_ref = _iter_season_data.content -- 获取content引用
                    break
                end
            end
            if content_ref == nil then  -- 不存在当期数据则添加
                content_ref = {}
                table.insert(period_data, {
                    period = cur_period,
                    content = content_ref
                })
            end
            table.insert(content_ref, period_item)
        end
        if #period_data > 0 then  -- 赛季内存在记录
            for _, every_period in ipairs(period_data) do
                table.sort(every_period.content, function(a, b)
                    return self:_RegionMapping(a.region) > self:_RegionMapping(b.region)  -- 按国>省>市降序
                end)
            end
            season_records.season_data = period_data  -- 添加每期汇总的内容
            table.insert(records, season_records)  -- 添加每季汇总的内容
        end
    end
    return records
end

function RankingListServer:_RegionMapping(regionType)
    if regionType == RankBoardRegion.REGION_ALL then
        return 4
    elseif regionType == RankBoardRegion.REGION_PREVINCE then
        return 3
    elseif regionType == RankBoardRegion.REGION_CITY then
        return 2
    elseif regionType == RankBoardRegion.REGION_COUNTY then
        return 1
    else
        return 0
    end
end


function RankingListServer:_ToTimestamp(str)
    if str == nil or str == "" then
        return 0
    end
    local year, month, day, hour, min, sec = str:match("(%d+)%-(%d+)%-(%d+)%s+(%d+):(%d+):(%d+)")
    return os.time({
        year = tonumber(year),
        month = tonumber(month),
        day = tonumber(day),
        hour = tonumber(hour),
        min = tonumber(min),
        sec = tonumber(sec)
    })
end

function RankingListServer:_BetweenTime(nowTimestamp, beginTimestamp, endTimestamp)
    if nowTimestamp >= beginTimestamp and nowTimestamp <= endTimestamp then
        return true
    end
    return false
end

function RankingListServer:CalcPeriod(season_id, timestamp)
    if season_id < 1 or season_id > #self._seasonTimeTbl then
        logerror("RankingListServer:_CalcPeriod season_id invalid", season_id)
        return 1
    end
    local StartTime = self._seasonTimeTbl[season_id].StartTime
    local StartTimestamp = self:_ToTimestamp(StartTime)
    local diffDays = math.floor((timestamp - StartTimestamp) / 86400)
    local periodNum = math.floor(diffDays / self._selfDayInterval) + 1
    if periodNum < 1 then
        periodNum = 1
    end
    return periodNum
end

return RankingListServer