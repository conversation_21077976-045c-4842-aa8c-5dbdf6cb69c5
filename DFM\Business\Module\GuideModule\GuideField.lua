----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class GuideField : FieldBase
local GuideField = class("GuideField", require"DFM.YxFramework.Managers.Module.FieldBase" )
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local GuideLogic  = require "DFM.Business.Module.GuideModule.GuideLogic"

local PlatformLogic = require "DFM.YxFramework.Managers.UI.Platform.PlatformLogic"

local DefaultShowDebugInfoState = true

local function log(...) loginfo("[GuideField]", ...) end
local function err(...) logerror("[GuideField]", ...) end
local function warning(...) logwarning("[GuideField]", ...) end

local function with_profile(sig, func, ...)
    local t = os.clock()
    func(...)
    local gap = os.clock() - t
    logwarning("profile with", sig, " execute time:", gap)
end

---@class FieldProxy
---@field _debugName string
---@field Reset function()

---@param name string
---@param default any
function GuideField:useField(name, default)
    ---@type FieldProxy
    local proxyOjb = {
        _debugName = name,
        _value = type(default) == "table" and simpleclone(default) or default,
        Get = function(obj) return obj._value end,
        Set = function(obj, value) obj._value = value end,
        Reset = function(obj) obj._value = type(default) == "table" and simpleclone(default) or default end
    }

    self:_AddFieldProxy(proxyOjb)
    return proxyOjb
end

---@param name string
---@param default table
function GuideField:useMap(name,default)
    default = default or {}
    ---@type FieldProxy
    local proxyOjb = {
        _debugName = name,
        _value = simpleclone(default),
        Get = function(obj, key) return obj._value[key] end,
        Set = function(obj, key, value) obj._value[key] = value end,
        Reset = function(obj) obj._value = simpleclone(default) end
    }

    self:_AddFieldProxy(proxyOjb)
    return proxyOjb
end

function  GuideField:_AddFieldProxy(proxy)
    if table.contains(self._fieldProxies, proxy) then
        assert(false, "proxy already exist",proxy._debugName)
    end
    table.insert(self._fieldProxies, proxy)
end



function GuideField:Ctor()
    ---@type FieldProxy[]
    self._fieldProxies = {}
    setmetatable(self._fieldProxies, weakmeta_kv)

    self._curGuideId = 0
    self._curGuideCfg = nil
    self._curGuideData = nil

    self._guideMainUIHandle = nil
    self._guideMainUI = nil
    self._guideGmUIHandle = nil
    self._guideCutSceneUIHandle = nil

    self._guideWidgetProxyMap = setmetatable({}, weakmeta_value)  -- string: widget

    self.guideIdToCfg = {}
    self.guideStageIdToStageCfg = {}
    self.waitPlayGuide = nil
    self.isGuidingInGame = false -- 局内是否接管引导。局内接管期间，局外的引导不启动
    self.bSkipGuidePopWindowShow = false -- 是否正在处理跳过新手引导的弹窗
    self.onMainUILoadFinishCallback = nil -- 启动引导时，如果guideMainUI没有加载完毕，需要等加载完毕在callback真正启动引导
    self.loadingUINumUpSafeHouse = 0 -- 正在加载中的挡住3d安全屋hud的ui数量。加载完毕后数量-1

    -- 引导暂停状态
    self.pauseGuideUINum = 0    -- 当某些ui存在时，暂停当前引导，新启动的引导也处于暂停状态，等这些ui完全关闭了再继续引导。比如升级弹窗
    self.pauseGuideUINames = {}
    self.ignorePauseUINum = 0   -- pause ui 存在，但是更上层的ui需要被引导，当这个计数大于0的时候，视为pauseGuideUINum=0

    self.bInMovieType = false   -- 当前是否处于电影模式。在弹窗暂停引导时，也要退出电影模式
    self.bLoadingGuideRes = false   -- 是否有引导在等待资源加载后启动。加载时先隐藏所有ui

    -- 需要隔一段时间自动播放的引导数据
    self.guideAutoPlayInfoList = {} -- {stageId = ..., lastTime = ..., nextTime = ...}
    self.guideAutoPlayTimer = nil

    self.guideClickDataInGame = nil
    self.guideNounExplainHandleInGame = nil
    self.miniMapMarkItemListInGame = nil
    self.miniMapInGameOpenState = false -- 局内是否打开小地图
    self.escInGameOpenState = false -- 局内是否打开esc
    self.lootingInGameOpenState = false
    self.lootingAiIndexInGame = 0 -- 局内正在looting的ai
    self.interactionPanelShowState = 0
    self.solCaptureInGameOpenState = false
    self.teamInfoDetailInGameOpenState = false -- 队伍信息hud状态
    self.loadingHideAllState = false
    self.solDyingViewState = false -- 是否sol濒死
    self._flagGuideUIShowState = {}

    self._bShowDebugInfo = DefaultShowDebugInfoState
    self._modifyVisibilityWidgets = {}

    -- 局内的一些数据缓存
    self.inGamePlayerArmor = 0
    self.inGamePlayerHelmetArmor = 0
    self.inGamePlayerMaxArmor = 0
    self.inGamePlayerHelmetMaxArmor = 0
    self.inGamePlayerHealth = 0
    self.inGamePlayerMaxHealth = 0
    self.inGameBuffList = {}
    self.inGameWaitPlayQueue = {}
    self.bChangeRouletteState = false
    self.bInCombat = false
    self.bSetBigMapInput = false
    -- self.bBackFromSafeHouse = false -- 是否从安全屋gameflow切换出来
    self.timerForGuideInGameList = nil
    self.InGameIsAliveState            = self:useField("InGameIsAliveState", nil)

    self.bWaitSettlementInFrontEnd = true
    self.bMpSettlementPopEnd = true
    self.bSolSettlementPopEnd = true
    self.cacheSpecItems = {}

    -- fallback cleaned on OnDestroyField and GameFlowChanged
    self.curShowUIFlags = {} ---@type EGuideShowUIFlag[]

    self.bControlLoadingView = false    -- 是否延迟loading界面的关闭
    self.fakeLoadingTimer = nil -- 假loading界面的进度 定时器
    self.fakeLoadingBeginTime = 0
    self.fakeLoadingEndTime = 0

    self.useInputGateList = {}

    self._cacheUIDict = {}  -- 复用的ui  uiId:(list)uiIns
    self.curUseUIDict = {} -- 正在使用的ui  uiId:{(list)cfgId: uiIns}
    self._ingameInputStateFlag = {} -- 是否启用/禁用输入
    self._delayShowDataInGame = {}

    self.sendOnceSpecLogIds = {} -- 启动客户端后只上报一次的id。上报过一次后存起来，本次运行不再上报

    -- azhengzheng:用于判断撤离教学视频是否正在播放
    self._isEvacuateTeachingPlaying = nil

    self.SafeBoxGuideData              = self:useField("SafeBoxGuideData", nil)
    self.IrisEnterStageState           = self:useField("IrisEnterStageState", { current = nil, last = nil })
    self.ClientSkipGuides              = self:useMap("ClientSkipGuides", {})
    self.LastEvacuationPointGuideType  = self:useField("LastEvacuationPointPopWindowId", nil)

    self.InvalidBatchGuideStageIdss = self:useField("InvlaidBatchGuideIds", {})
    self.ShowedInvlaidBatchGuideStageIds = self:useMap("ShowedInvlaidBatchGuideIds", {})

    self.LastDsRoomIdOnDeathDamageInfoView = self:useField("LastDsRoomIdOnDeathDamageInfoView", nil)

    self._stageNeedCDCache                 = {}

    -- 记录引导修改了的外部状态
    self.modifiedExternalStates            = {} ---@type table<GuideConfig.EExternalModification,boolean>

    self.lastPlayedSkillAgainstEntry = nil

    self:_InitCfgTable()

    self:AddLuaEvent(Server.GuideServer.Events.evtNewPlayerGuidePassed, self._OnEvtNewPlayerGuidePassed, self)
    self:AddLuaEvent(Server.GuideServer.Events.evtSkipGuideStage, self._OnEvtNewPlayerGuideSkip, self)
    self:AddLuaEvent(DFMGlobalEvents.evtWorldCleanUp, self._OnWorldCleanUp, self)
end


function GuideField:OnDestroyField()
    self:StopFakeLoadingTimer()
    self:ClearCurShowUIState()
    self:RemoveAllLuaEvent()
    self.guideClickDataInGame = nil
    self.miniMapMarkItemListInGame = nil
    self.miniMapInGameOpenState = false
    self.solDyingViewState = false
    self.escInGameOpenState = false
    self.lootingInGameOpenState = false
    self.lootingAiIndexInGame = 0
    self.solCaptureInGameOpenState = false
    self.teamInfoDetailInGameOpenState = false -- 队伍信息hud状态
    self.loadingHideAllState = false
    self._flagGuideUIShowState = {}
    self.loadingUINumUpSafeHouse = 0

    self.pauseGuideUINum = 0
    self.pauseGuideUINames = {}
    self.ignorePauseUINum = 0

    self.waitPlayGuide = nil
    self._modifyVisibilityWidgets = {}
    self._guideWidgetProxyMap = setmetatable({}, weakmeta_value)
    self.guideIdToCfg = {} ---@type table<number, FGuideConfig>
    self.guideStageIdToStageCfg = {} ---@type table<number, FGuideStageClientOnlyConfig>
    self.cacheSpecItems = {}
    -- self.bBackFromSafeHouse = false
    self.modifiedExternalStates= {}

    for _, proxy in ipairs(self._fieldProxies) do
        loginfo("reset field proxy", proxy._debugName)
        proxy:Reset()
    end
end

function GuideField:ClearInGameInfo()
    log("ClearInGameInfo")
    self.inGamePlayerArmor = 0
    self.inGamePlayerHelmetArmor = 0
    self.inGamePlayerMaxArmor = 0
    self.inGamePlayerHelmetMaxArmor = 0
    self.inGamePlayerHealth = 0
    self.inGamePlayerMaxHealth = 0
    self.inGameBuffList = {}
    self.inGameWaitPlayQueue = {}
    self._delayShowDataInGame = {}
    self:ResetFlagForOtherModule()
    self:CleanAutoPlayTimer()
    self:CleanAutoPlayInfo()
    self.bInCombat = false
    self.lootingAiIndexInGame = 0

    self.SafeBoxGuideData:Reset()
    self.IrisEnterStageState:Reset()
    self.InGameIsAliveState:Reset()
end

function GuideField:ResetFlagForOtherModule()
    if self.bChangeRouletteState then
        Module.HUDToolBar:SetFlagDisableCommendInGuide(false)
        self.bChangeRouletteState = false
    end
    if self.bSetBigMapInput then
        local UDFMHudHelper = import "DFMHudHelper"
        UDFMHudHelper.EnableBigmapInput(GetWorld(), true)
        self.bSetBigMapInput = false
    end
    Module.ItemDetail:SetDisableItemdDetailPanelClickBgCloseState(false)
    Module.ItemDetail:SetDisableItemDetailPanelShowState(false)
    --self:ClearCurShowUIState()
    Module.SandBoxMap:SetRestrictSelectMapFlag(false)
    Module.SandBoxMap:SetUnlockMapByGuide(nil)
    Module.SandBoxMap:SetDisableSelectModeByGuide(false)
    Module.CommonWidget:SetDisableHover(false)
    Module.CommonWidget:SetFlagDisableOpForGuide(false)
    Module.Looting:SetDisableScrollSearchedItemState(false)
    Module.SandBoxMap:SetGuideDisableJumpMandelState(false)
    -- Module.Hero:SetFlagDisableClickBgCloseHeroSkill(false)

    Module.Guide.Config.EGuideEvent.evtGuideModification:Invoke("recover", self.modifiedExternalStates)
    self.modifiedExternalStates = {}
end


-----------------------------------------------------------------------------
--region AutoPlay

function GuideField:CleanAutoPlayTimer()
    if self.guideAutoPlayTimer then
        Timer.CancelDelay(self.guideAutoPlayTimer)
        self.guideAutoPlayTimer = nil
    end
end

function GuideField:CleanAutoPlayInfo()
    self.guideAutoPlayInfoList = {}
end

function GuideField:PushAutoPlayGuideInfo(stageId, lastTime, nextTime)
    log("PushAutoPlayGuideInfo", stageId, lastTime, nextTime)
    local info = self:GetAutoPlayGuideInfoByStageId(stageId)
    if info then
        info.lastTime = lastTime
        info.nextTime = nextTime
    else
        info = {
            stageId = stageId,
            lastTime = lastTime,
            nextTime = nextTime,
        }
        table.insert(self.guideAutoPlayInfoList, info)
    end
end

function GuideField:GetFirstAutoPlayGuideInfo(bPop)
    if #self.guideAutoPlayInfoList == 0 then
        log("GetFirstAutoPlayGuideInfo is nil", bPop)
        return nil
    end
    local ret = self.guideAutoPlayInfoList[1]
    local retIdx = 1
    for idx, info in ipairs(self.guideAutoPlayInfoList) do
        if info.nextTime < ret.nextTime then
            ret = info
            retIdx = idx
        end
    end
    if bPop then
        table.remove(self.guideAutoPlayInfoList, retIdx)
    end
    log("GetFirstAutoPlayGuideInfo", bPop, ret.stageId, ret.lastTime, ret.nextTime)
    return ret
end

function GuideField:GetAutoPlayGuideInfoByStageId(stageId, bPop)
    local ret
    local retIdx
    for idx, info in ipairs(self.guideAutoPlayInfoList) do
        if info.stageId == stageId then
            ret = info
            retIdx = idx
        end
    end
    if bPop and retIdx then
        table.remove(self.guideAutoPlayInfoList, retIdx)
    end
    return ret
end

--#endregion
-------------------------------------------------

function GuideField:_InitCfgTable()
    local guideConfigTable = GuideConfig.TableGuideConfig
    for _, cfg in pairs(guideConfigTable) do
        self.guideIdToCfg[cfg.GuideId] = cfg
    end

    --local guideConfigTable = GuideConfig.TableGuideConfigForLocalDev
    --for _, cfg in pairs(guideConfigTable) do
    --    self.guideIdToCfg[cfg.GuideId] = cfg
    --end

    local guideConfigTable = GuideConfig.TableGuideStageConfig
    for _, cfg in pairs(guideConfigTable) do
        self.guideStageIdToStageCfg[cfg.GuideStageId] = cfg
    end

    -- pc的在通用基础上进行覆盖
    -- if DFHD_LUA == 1 then
    --     local guideConfigTableForHD = GuideConfig.TableGuideConfigForHD
    --     for _, cfg in pairs(guideConfigTableForHD) do
    --         self.guideIdToCfg[cfg.GuideId] = cfg
    --     end
    -- end

    -- TODO: use test framework to validate all the guide config table on game start
    -- validate required wtf at start time 

    if not VersionUtil.IsShipping() then
        with_profile("GuideField:_InitCfgTable Validate Required Events and functions", function()
            ---@type GuideDataCustom
            local dummyGuideData = require("DFM.Business.Module.GuideModule.Data.GuideDataCustom")
            for _, cfg in pairs(self.guideIdToCfg) do
                for _, evtStr in pairs(cfg.EndEvent) do
                    if string.sub(evtStr, #evtStr) == ')' then
                        -- Extract event name and arguments using string.match
                        evtStr = string.match(evtStr, "^(.-)%((.*)%)$")
                    end
                    if GuideConfig.EGuideEvent[evtStr] == nil then
                        Module.CommonTips:ShowSimpleTip(string.format("Failed to find event '%s' on GuideConfig %d", evtStr, cfg.GuideId), 10)
                    end
                end
                if cfg.GuideType == GuideConfig.EGuideData.Custom then
                    local funcName = cfg.Args
                    if tonumber(funcName) == nil then
                        local delimiter = GuideConfig.CustomDataFuncSeparator
                        local pos = string.find(cfg.Args, delimiter)
                        if pos ~= nil then
                            funcName = string.sub(funcName, 1, pos - 1)
                        end
                        if dummyGuideData[funcName] == nil then
                            Module.CommonTips:ShowSimpleTip(string.format("Failed to find function '%s' in GuideDataCustom, GuideId %d", funcName, cfg.GuideId),10)
                        end
                    end
                end
            end
        end)
    end
end

function GuideField:GetGuideCfg(guideId)
    return self.guideIdToCfg[guideId]
end


function GuideField:GetGuideStageCfg(stageId)
    return self.guideStageIdToStageCfg[stageId]
end

function GuideField:SetCurGuideInfo(guideId, guideCfg, guideData)
    -- log("GuideField:SetCurGuideInfo", guideId, guideData)
    self._curGuideId = guideId
    self._curGuideCfg = guideCfg
    ---@type GuideDataBase
    self._curGuideData = guideData

    if guideCfg then
        Server.GuideServer.playingStageId = guideCfg.GuideStageId
    else
        Server.GuideServer.playingStageId = nil
    end
end


---@return GuideMainUI|nil
function GuideField:GetMainUI()
    --if not self._guideMainUIHandle then
    --    return nil
    --end
    local mainUI = (self._guideMainUIHandle and self._guideMainUIHandle:GetUIIns()) or self._guideMainUI
    if isvalid(mainUI) then
        if type(mainUI) == "table" and mainUI.__cppinst and not slua.isValid(mainUI.__cppinst) then
            err("GetMainUI main ui cpp ins is nil")
            self._guideMainUI = nil
            return nil
        end
        if hasdestroy(mainUI) then
            err("GetMainUI main ui hasdestroy")
            self._guideMainUI = nil
            return nil
        end

        return mainUI
    else
        self._guideMainUI = nil
        return nil
    end
end

function GuideField:SetMainUI(uiIns)
    self._guideMainUI = uiIns
end

function GuideField:SetMainUIHandle(handle)
    self._guideMainUIHandle = handle
    self._guideMainUI = nil
end

function GuideField:GetMainUIHandle(handle)
    return self._guideMainUIHandle
end

function GuideField:GetGmUIHandle()
    return self._guideGmUIHandle
end

function GuideField:SetGmUIHandle(handle)
    self._guideGmUIHandle = handle
end

function GuideField:GetCutSceneUIHandle()
    return self._guideCutSceneUIHandle
end

function GuideField:SetCutSceneUIHandle(handle)
    self._guideCutSceneUIHandle = handle
end

function GuideField:IsGuiding()
    return self._curGuideData ~= nil or self.isGuidingInGame or self.bSkipGuidePopWindowShow
end

function GuideField:IsStatePause()
    return self.ignorePauseUINum <= 0 and self.pauseGuideUINum > 0
end

function GuideField:GetCurGuideId()
    return self._curGuideData and self._curGuideData:GetGuideId() or nil
end

function GuideField:GetCurGuideData()
    return self._curGuideData
end

function GuideField:GetCurGuideType()
    return self._curGuideData and self._curGuideData._guideType or nil
end

---@return integer -1表示无
function GuideField:GetCurGuideStage()
    local curGuideId = tonumber (self:GetCurGuideId())
    if not curGuideId then
        return -1
    end
    return math.floor( curGuideId/1000)
end

function GuideField:AddProxyWidget(name, uiIns)
    -- loginfo("GuideField:AddProxyWidget", name, uiIns)

    self._guideWidgetProxyMap[name] = uiIns
end

function GuideField:RemoveProxyWidget(name)
    -- loginfo("GuideField:RemoveProxyWidget", name)
    self._guideWidgetProxyMap[name] = nil
end

function GuideField:GetProxyWidget(name)
    -- loginfo("GuideField:GetProxyWidget", name, self._guideWidgetProxyMap[name])
    return self._guideWidgetProxyMap[name]
end

function GuideField:_OnEvtNewPlayerGuidePassed()
    self.waitPlayGuide = GuideLogic.GetCurNewPlayerGuideStage()
    GuideConfig.EGuideEvent.evtGuideNewPlayerStageUpdate:Invoke()
    local update3CState = function ()
        Module.Guide:Set3CFinished()
    end
    Server.GuideServer:RequestSolMatchCountInfo(update3CState)

    if self.bControlLoadingView then
        if self.waitPlayGuide ~= GuideConfig.EGuideStage.newPlayerGuideStage1 then
            log("_OnEvtNewPlayerGuidePassed, release loading view control")
            local UDFMGameLoadingManager = import("DFMGameLoadingManager")
            local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
            if DFMGameLoadingManager then
                DFMGameLoadingManager:SetCloseLoadingViewAfterLoading(true)
                Module.Guide.Field:StopFakeLoadingTimer()
                self.bControlLoadingView = false

                -- 如果在安全屋阶段，loading还存在，就干掉
                if Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
                    log("_OnEvtNewPlayerGuidePassed ShutDownLoadingView")
                end
            else
                err("_OnEvtNewPlayerGuidePassed, Reset fail, DFMGameLoadingManager is nil !!!")
            end
        else
            log("_OnEvtNewPlayerGuidePassed cur stage is newMatch, cant shutdown")
        end
    end
end

function GuideField:_OnEvtNewPlayerGuideSkip(skipStageList, bSkipAll)
    if bSkipAll then
        Module.Guide:StopAllGuide(true)
    else
        if self._curGuideCfg then
            local curStageId = self._curGuideCfg.GuideStageId
            if table.contains(skipStageList, curStageId) then
                Module.Guide:StopAllGuide(true)
            end
        end
    end
end

function GuideField:ResetNewPlayerGuidePassed()
    self.waitPlayGuide = GuideLogic.GetCurNewPlayerGuideStage()
end

function GuideField:OnClearField()
    self._modifyVisibilityWidgets = {}

    self:ResetUIList()
end

function GuideField:ResetUIList()
    -- 清理ui缓存
    for _, uiList in pairs(self._cacheUIDict) do
        for _, uiIns in pairs(uiList) do
            Facade.UIManager:CloseUI(uiIns)
        end
    end
    for _, uiList in pairs(self.curUseUIDict) do
        for _, uiIns in pairs(uiList) do
            Facade.UIManager:CloseUI(uiIns)
        end
    end
    self._cacheUIDict = {}
    self.curUseUIDict = {}
end


function GuideField:GetGmShowDebugInfoState()
    return self._bShowDebugInfo
end

function GuideField:SetGmShowDebugInfoState(state)
    self._bShowDebugInfo = state
end

function GuideField:GetGmPosType()
    return self._gmPosType
end

function GuideField:SetGmPosType(type)
    self._gmPosType = type
end

-----------------------------------------------------
--region Pause

function GuideField:AddPauseGuideUINum(key)
    if key and table.contains(self.pauseGuideUINames, key) then
        log("AddPauseGuideUINum repeat", self.pauseGuideUINum, key)
        return
    end

    if key then
        table.insert(self.pauseGuideUINames, key)
    end

    self.pauseGuideUINum = self.pauseGuideUINum + 1
    log("AddPauseGuideUINum", self.pauseGuideUINum, key)
    if self._curGuideData then
        if self:IsStatePause() then
            self._curGuideData:Pause()
        end
    end
    
    if self.guideClickDataInGame then
        if self:IsStatePause() then
            self.guideClickDataInGame:Pause()
        end
    end

    if self.bInMovieType then
        GuideLogic.CloseGuideCutSceneUI()
    end

    if self:IsStatePause() then
        self:_OnPause()
    end
end

function GuideField:ReducePauseGuideUINum(key)
    if key and not table.contains(self.pauseGuideUINames, key) then
        log("ReducePauseGuideUINum fail, key not in list", self.pauseGuideUINum, key)
        return
    end

    if key then
        table.removebyvalue(self.pauseGuideUINames, key)
    end

    self.pauseGuideUINum = self.pauseGuideUINum - 1
    log("ReducePauseGuideUINum", self.pauseGuideUINum, key)
    if self.pauseGuideUINum < 0 then
        logwarning("ReducePauseGuideUINum num fail", self.pauseGuideUINum)
        self.pauseGuideUINum = 0
    end

    if self.pauseGuideUINum < 1 then
        if self._curGuideData then
            self._curGuideData:Restart()
        end

        if self.guideClickDataInGame then
            self.guideClickDataInGame:Restart()
        end

        if self.bInMovieType then
            GuideLogic.ShowGuideCutSceneUI()
        end

        self:_OnRestart()
    end
end

function GuideField:AddIgnorePauseUINum()
    self.ignorePauseUINum = self.ignorePauseUINum + 1
    log("AddIgnorePauseUINum", self.ignorePauseUINum)
    if self._curGuideData then
        self._curGuideData:Restart()
    end

    if self.guideClickDataInGame then
        self.guideClickDataInGame:Restart()
    end

    if self.bInMovieType then
        GuideLogic.ShowGuideCutSceneUI()
    end

    self:_OnRestart()
end

function GuideField:ReduceIgnorePauseUINum()
    self.ignorePauseUINum = self.ignorePauseUINum - 1
    log("ReduceIgnorePauseUINum", self.ignorePauseUINum)
    if self:IsStatePause() then
        if self._curGuideData then
            self._curGuideData:Pause()
        end

        if self.guideClickDataInGame then
            self.guideClickDataInGame:Pause()
        end

        if self.bInMovieType then
            GuideLogic.CloseGuideCutSceneUI()
        end
        
        self:_OnPause()
    end
end

function GuideField:DumpPauseGuideUIState()
    log("DumpPauseGuideUISate", self.pauseGuideUINum, self.ignorePauseUINum)
    log("pauseGuideUINames", table.concat(self.pauseGuideUINames, ", "))
    -- log("curShowLayerFlagInfos", table.concat(self.curShowLayerFlagInfos, ", "))
end


function GuideField:_OnPause()
    GuideConfig.EGuideEvent.evtGuidePause:Invoke()
    GuideLogic.SetInputStateAllPlatform()
    self:AddFlagShowUI(GuideConfig.EGuideShowUIFlag.Pause)
    if not VersionUtil.IsShipping() then
        self:DumpPauseGuideUIState()
    end
end

function GuideField:_OnRestart()
    GuideConfig.EGuideEvent.evtGuideRestart:Invoke()
    GuideLogic.SetInputStateAllPlatform()
    self:RemoveFlagShowUI(GuideConfig.EGuideShowUIFlag.Pause)
    if not VersionUtil.IsShipping() then
        self:DumpPauseGuideUIState()
    end
end

--endregion
----------------------------------------------------



---------------------------------------------------
--region FlagShowUI

---@param showUIFlag EGuideShowUIFlag
function GuideField:AddFlagShowUI(showUIFlag)
    -- TODO: 下个版本支持更多debug输出
    log("AddFlagShowUI before", showUIFlag, #self.curShowUIFlags)
    if table.contains(self.curShowUIFlags, showUIFlag) then
        -- warning("AddFlagShowUI is exit", showUIFlag)
        return
    end
    

    table.insert(self.curShowUIFlags, showUIFlag)
    log("AddFlagShowUI success, cur count ", #self.curShowUIFlags)
    self:UpdateCurShowUIState()
end

---@param showUIFlag EGuideShowUIFlag
function GuideField:RemoveFlagShowUI(showUIFlag)
    log("RemoveFlagShowUI before", showUIFlag, #self.curShowUIFlags)
    if not table.contains(self.curShowUIFlags, showUIFlag) then
        -- warning("RemoveFlagShowUI is not exist", showUIFlag)
        return
    end

    table.removebyvalue(self.curShowUIFlags, showUIFlag)
    -- table.removebyvalue(self.curShowLayerFlagInfos, GuideConfig.EGuideShowUIFlagInfo[showUIFlag])
    log("RemoveFlagShowUI  success,  cur count ", #self.curShowUIFlags)
    self:UpdateCurShowUIState()
end

---Update the current ui layers and hud state based on the current show ui layer flags
function GuideField:UpdateCurShowUIState()
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    
    GuideLogic.SetGuideInputGate("GuidePause", false)

    -- If No flags -> release guide control for UI layers
    if not self.curShowUIFlags or #self.curShowUIFlags == 0 then
        if Facade.UIManager:IsHidingLayersBelowPop() then
            Facade.UIManager:SetBelowPopUILayerVisible(false, ELayerRuleChangeReason.GuideProcessing)
        else
            Facade.UIManager:ResetLayersToVisible(ELayerRuleChangeReason.GuideProcessing)
        end
        if hudStateManager then
            hudStateManager:RemoveState(UE.GameHUDSate.GHS_TutorialHideAllHUD, true)
        end
        return
    end

    -- If guide pause flag -> hide mask -> recover other guide modification to layers
    if table.contains(self.curShowUIFlags, GuideConfig.EGuideShowUIFlag.Pause) then
        local allLayers = {}
        if Facade.UIManager:IsHidingLayersBelowPop() then
            for _, layer in pairs(EUILayer) do
                if layer == EUILayer.Pop or layer == EUILayer.Tip or layer == EUILayer.Mask or layer == EUILayer.Watermark or PlatformLogic.IsForceAreaType(layer) then
                    table.insert(allLayers, layer)
                end
            end
        else
            for _, layer in pairs(EUILayer) do
                table.insert(allLayers, layer)
            end
        end
        table.removebyvalue(allLayers, EUILayer.Mask) 
        Facade.UIManager:LayersOnly(allLayers, ELayerRuleChangeReason.GuideProcessing)

        GuideLogic.SetGuideInputGate("GuidePause", true)
        return
    end

    -- Guide Control ALL LAYER -> merge allowed layers into a set
    local realShowUILayerList = {}
    for _, flag in pairs(self.curShowUIFlags) do
        local flagLayers = GuideConfig.EGuideShowUIFlagInfo[flag] -- cfg!
        for _, layer in pairs(flagLayers) do
            if not table.contains(realShowUILayerList, layer) then
                table.insert(realShowUILayerList, layer)
            end
        end
    end
    if not realShowUILayerList or #realShowUILayerList == 0 then
        warning("UpdateCurShowUIState, curShowUIFlags not empty, but realShowUILayerList is empty!!!")
        return
    end

    -- add/remove hud state 
    if hudStateManager then
        if table.contains(realShowUILayerList, EUILayer.HUD) then
            hudStateManager:RemoveState(UE.GameHUDSate.GHS_TutorialHideAllHUD, true)
        else
            hudStateManager:AddState(UE.GameHUDSate.GHS_TutorialHideAllHUD, true)
        end
    end
    Facade.UIManager:LayersOnly(realShowUILayerList, ELayerRuleChangeReason.GuideProcessing)
end

function GuideField:ClearCurShowUIState()
    log("ClearCurShowUIState", table.concat(self.curShowUIFlags, ", "))
    if #self.curShowUIFlags > 0 then
        self.curShowUIFlags = {}
        self:UpdateCurShowUIState()
    end
end


--endregion
---------------------------------------------------


-------------------------------------------------
--region UIState

function GuideField:AddModifyVisibilityWidget(widgetName, widget, oriVisibility)
    log("AddModifyVisibilityWidget", widgetName)
    if self._modifyVisibilityWidgets[widgetName] then
        return
    end

    local widgetInfo = {
        widget = widget,
        oriVisibility = oriVisibility,
    }
    self._modifyVisibilityWidgets[widgetName] = widgetInfo
    --table.insert(self._modifyVisibilityWidgets, widgetInfo)
end

function GuideField:ResetModifyVisibilityWidgets(bResetAll)
    log("ResetModifyVisibilityWidgets")
    if bResetAll then
        for _, widgetInfo in pairs(self._modifyVisibilityWidgets) do
            widgetInfo.widget:SetVisibility(widgetInfo.oriVisibility)
        end
        self._modifyVisibilityWidgets = {}
    end
end

function GuideField:GetCacheUIIns(uiId)
    -- log("Try GetCacheUIIns", uiId)
    local uiIns
    if self._cacheUIDict[uiId] and #self._cacheUIDict[uiId] > 0 then
        log("GetCacheUIIns",uiId, #self._cacheUIDict[uiId])
        uiIns = self._cacheUIDict[uiId][#self._cacheUIDict[uiId]]
        table.remove(self._cacheUIDict[uiId])

        if not uiIns or isinvalid(uiIns) or uiIns.__cppinst == nil then
            warning('复用的UI失效', uiId, uiIns)
            return self:GetCacheUIIns(uiId)
        end

        if uiIns.Reset then
			uiIns:Reset()
		end
    end

    return uiIns
end

function GuideField:ReturnCacheSubUI(uiId, uiIns)
    local uiList = self._cacheUIDict[uiId]
    if not uiList then
        uiList = {}
        self._cacheUIDict[uiId] = uiList
    end
    table.insert(uiList, uiIns)
    -- log("ReturnCacheSubUI", uiId, #uiList)
end

function GuideField:SetGuideUIState(uiId, bVisible, key)
    local keyList = self._flagGuideUIShowState[uiId]
    if not keyList then
        self._flagGuideUIShowState[uiId] = {}
    end
    self._flagGuideUIShowState[uiId][key] = bVisible

    keyList = self._flagGuideUIShowState[uiId]
    bVisible = true
    for k, value in pairs(keyList) do
        if value == false then
            log("SetGuideUIState, check not show", k)
            bVisible = false
        end
    end

    local uiInsList = self._cacheUIDict[uiId]
    for _, uiIns in pairs(uiInsList or {}) do
        if
            uiIns and
            not hasdestroy(uiIns) and
            uiIns.UINavID == uiId
        then
            if uiIns.SetRenderOpacity then
                uiIns:SetRenderOpacity(bVisible and 1 or 0)
                log("SetGuideUIState _cacheUIDict", uiId, bVisible)
            else
                logerror("ui ins has no SetRenderOpacity", uiId, UIName2ID.GetNameByID(uiId))
            end
        end
    end

    uiInsList = self.curUseUIDict[uiId]
    for _, uiIns in pairs(uiInsList or {}) do
        if
            uiIns and
            not hasdestroy(uiIns) and
            uiIns.UINavID == uiId
        then
            if uiIns.SetRenderOpacity then
                uiIns:SetRenderOpacity(bVisible and 1 or 0)
                log("SetGuideUIState curUseUIDict", uiId, bVisible)
            else
                logerror("ui ins has no SetRenderOpacity", uiId, UIName2ID.GetNameByID(uiId))
            end
        end
    end
end

function GuideField:GetGuideUIState(uiId)
    local keyList = self._flagGuideUIShowState[uiId]
    if not keyList then
        return true
    end

    for k, value in pairs(keyList) do
        if value == false then
            log("GetGuideUIState, check not show", k)
            return false
        end
    end

    return true
end

--endregion
-----------------------------------------


function GuideField:SetInMovieTypeState(bInMovieType)
    self.bInMovieType = bInMovieType
end

function GuideField:SetInputStateInGame(key, bEnable)
    log("SetInputStateInGame", key, bEnable)
    if bEnable then
        if table.contains(self._ingameInputStateFlag, key) then
            log("SetInputStateInGame repeat", key)
        else
            log("SetInputStateInGame add success", key)
            table.insert(self._ingameInputStateFlag, key)
        end
    else
        if table.contains(self._ingameInputStateFlag, key) then
            log("SetInputStateInGame remove success", key)
            table.removebyvalue(self._ingameInputStateFlag, key)
        else
            log("SetInputStateInGame no key", key)
        end
    end

    GuideLogic.UpdateHDInputState()
end

function GuideField:GetInputStateInGame()
    local ret
    if #self._ingameInputStateFlag > 0 then
        for _, key in pairs(self._ingameInputStateFlag) do
            log("GetInputStateInGame cur key", key)
        end
        ret = false
    else
        ret = true
    end
    log("GetInputStateInGame", ret)
    return ret
end

function GuideField:StartFakeLoadingTimer(duration)
    duration = setdefault(duration, 5)
    if duration <= 0 then
        logerror("StartFakeLoadingTimer duration is less then 0")
        return
    end
    if not self.fakeLoadingTimer then
        self.fakeLoadingBeginTime = TimeUtil.GetCurrentTimeMillis()
        self.fakeLoadingEndTime = self.fakeLoadingBeginTime + duration
        self.fakeLoadingTimer = Timer:NewIns(duration / 100, 0)
        self.fakeLoadingTimer:AddListener(self._RefreshFakeLoadingTimer, self)
        self.fakeLoadingTimer:Start()
    end
end

function GuideField:StopFakeLoadingTimer()
    if self.fakeLoadingTimer then
        self.fakeLoadingTimer:Stop()
        self.fakeLoadingTimer:Release()
        self.fakeLoadingTimer = nil
    end
end


function GuideField:_RefreshFakeLoadingTimer()
    local curTime = TimeUtil.GetCurrentTimeMillis()
    if curTime >= self.fakeLoadingEndTime then
        self:StopFakeLoadingTimer()
    end

    local UDFMGameLoadingManager = import("DFMGameLoadingManager")
    local DFMGameLoadingManager = UDFMGameLoadingManager.GetGameLoadingManager(GetGameInstance())
    if DFMGameLoadingManager then
        local percent = (curTime - self.fakeLoadingBeginTime) / (self.fakeLoadingEndTime - self.fakeLoadingBeginTime)
        DFMGameLoadingManager:UpdateLoadingProgressManually(percent > 1 and 1 or percent)
    end
end

function GuideField:_OnWorldCleanUp()
    log("GuideField:_OnWorldCleanUp")
    --关闭所有持有的UI
    self._modifyVisibilityWidgets = {}
    self:ResetUIList()
end

function GuideField:SetIsEvacuateTeachingPlaying(isPlaying)
    self._isEvacuateTeachingPlaying = isPlaying
end

function GuideField:GetIsEvacuateTeachingPlaying()
    return self._isEvacuateTeachingPlaying
end


-- make a cacahe, because those parameter are lonstval. or that each param need to check and loop each time
---@param guideStageCfg FGuideStageClientOnlyConfig
function GuideField:IsGuideStageNeedCD(guideStageCfg)
    return self._stageNeedCDCache[guideStageCfg.GuideStageId] ~= nil and
        self._stageNeedCDCache[guideStageCfg.GuideStageId] or
        (function()
            local bNeed =
                table.find(GuideLogic.GetRealStartCondition(guideStageCfg), function(condtion)
                    return condtion.ConditionID == GuideConfig.ECondition.PlayCD
                end) or
                false
            self._stageNeedCDCache[guideStageCfg.GuideStageId] = bNeed
            log("cached IsGuideStageNeedCD", guideStageCfg.GuideStageId, bNeed)
            return bNeed
        end)()
end


---@param key GuideConfig.EExternalModification
function GuideField:AddExternalModified(key)
    if not self.modifiedExternalStates[key] then
        self.modifiedExternalStates[key] = true
        log("GuideField:AddExternalModified", key)
    else
        log("GuideField:AddExternalModified, already modified", key)
    end
end


return GuideField
