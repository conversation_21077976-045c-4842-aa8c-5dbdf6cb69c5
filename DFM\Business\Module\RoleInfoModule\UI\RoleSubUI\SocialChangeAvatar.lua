----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMRoleInfo)
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
----- LOG FUNCTION AUTO GENERATE END -----------
local RoleInfoLogic = require "DFM.Business.Module.RoleInfoModule.Logic.RoleInfoLogic"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local LiteCommonDownload = require "DFM.Business.Module.LitePackageModule.UI.LiteCommonDownload"
local SocialChangeAvatar = ui("SocialChangeAvatar")
---@class SocialChangeAvatar : LuaUIBaseView

--- BEGIN MODIFICATION @ VIRTUOS
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import "EGPUINavWidgetFocusedAction"
--- END MODIFICATION

function SocialChangeAvatar:Ctor()
    self._wtSocialImage = self:Wnd("wtSocialImage", UIImage)
    self._wtNameLabel = self:Wnd("wtNameLabel", UITextBlock)
    self._wtAvatarDescription = self:Wnd("wtAvatarDescription", UITextBlock)
    self._wtChangeBtn = self:Wnd("wtChangeBtn", UIWidgetBase)
    self._wtChangeBtn:Event("OnClicked", self._OnUseIconClick, self)
    self._wtSocialList = UIUtil.WndScrollGridBox(self, "wtSocialList", self.OnGetItemCount, self._OnProcessWidget)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)


    if LiteDownloadManager:IsSupportLitePackage() then
        self._wtCommonDownload = self:Wnd("WBP_CommonDownload", LiteCommonDownload)
        self._wtCommonDownload:Collapsed()
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self._wtChangeBtn:SetDisplayInputAction("Confirm", true, nil, true)
    end
    --- END MODIFICATION

    self._wtButtonDesc = self:Wnd("wtButtonDesc", UIButton)
    self._wtDescImage = self:Wnd("wtDescImage", UIImage)
    self._wtUnlockCondition = self:Wnd("wtUnlockCondition", UIWidgetBase)

    self._wtSelectWidget = nil
    self._wtUsingWidget = nil
    self._selectId = nil
    self._selectInfo = nil
    self._index = 1

    self.downloadModuleName = "UICustomized"
end

function SocialChangeAvatar:OnInitExtraData(SocialType)
    self._SocialType = SocialType
    self._socialTable = {}
    self._selectId = 0
    self._selfSocialId = 0
    self:RefreshSocialData()
end

function SocialChangeAvatar:CheckRed(info)
    local redMap = Server.RoleInfoServer:GetSocialReddotHash()
    if redMap and redMap[info.AvatarID] then
        return true
    end
    return false
end

function SocialChangeAvatar:PlayScrollItem()
    local scrollIndex = self._index

    if self._SocialType == 2 then
        scrollIndex = math.floor(((self._index - 1) / 2)) * 2
    end

    self._wtSocialList:ScrollToItem(scrollIndex, true, true, 15, 0, false)
end

function SocialChangeAvatar:ClearCurRed(index)
    if self._SocialType == index then
        local propInfos = {}
        local redMap = Server.RoleInfoServer:GetSocialReddotHash()
        local id
        for _, value in ipairs(self._socialTable) do
            id = value.AvatarID
            if redMap and redMap[id] then
                table.insert(propInfos, redMap[id])
            end
        end
        if #propInfos > 0 then
            Server.RoleInfoServer:UpdateRedDot(propInfos)
        end
    end
end

function SocialChangeAvatar:InitAvatarData()
    self._socialTable = {}
    local url = IsBuildRegionCN() and Server.SDKInfoServer:GetPictureUrl() or ""

    local picUrl = Server.RoleInfoServer.picUrl -- 没看懂啥意思
    self._selfSocialId = (picUrl == url or picUrl == "") and 0 or tonumber(picUrl)
    self._selectId = picUrl == url and 0 or tonumber(picUrl)

    local Config = Module.RoleInfo.Config

    table.insert(self._socialTable, {
        AvatarType = 1,
        AvatarID = 0,
        AvatarDescription = Config.Loc.DefaultIcon,
        Islock = true,
        url = url and url or "",
        ResourceName = Config.DefaultAvatarPath,
        AvatarName = Config.Loc.DefaultIcon,
        JumpID = 0,
        JumpIDMP = 0,
        ButtonDesc = "",
        ButtonDescMP = "",
        ShopID = 0,
        SocialType = LimitSocialTypeDef.NONE,
        StartTime = 0,
        EndTime = 0,
    })

    local avatarMap = Server.RoleInfoServer:GetAvatarTbl()
    for index, value in ipairs(avatarMap) do
        table.insert(self._socialTable, value)
        if self._selectInfo == nil and self:CheckRed(value) then
            self._selectInfo = value
            self._selectId = value.AvatarID
            self._index = index + 1
        end
    end

    if self._selectInfo == nil then
        self._selectInfo = self._socialTable[1]
    end

    if self._selectInfo then
        self:RefreshShowIcon(self._selectInfo, self._index)
    end

    self:PlayScrollItem()
end

function SocialChangeAvatar:InitMilitaryData()
    self._socialTable = {}
    table.insert(self._socialTable, {
        AvatarType = 2,
        AvatarID = 0,
        AvatarDescription = Module.RoleInfo.Config.Loc.DefaultMilitary,
        Islock = true,
        ResourceName = "PaperSprite'/Game/UI/UIAtlas/Texture/BrandAvatar/BrandAvatar_0000.BrandAvatar_0000'",
        AvatarName = Module.RoleInfo.Config.Loc.DefaultMilitary,
        JumpID = 0,
        JumpIDMP = 0,
        ButtonDesc = "",
        ButtonDescMP = "",
        ShopId = 0,
        SocialType = LimitSocialTypeDef.NONE,
        StartTime = 0,
        EndTime = 0,
    })
    self._selfSocialId = Server.RoleInfoServer.militaryId
    self._selectId = self._selfSocialId
    local militarytbl = Server.RoleInfoServer:GetMilitaryTbl()
    for index, value in ipairs(militarytbl) do
        table.insert(self._socialTable, value)

        if self._selectInfo == nil and self:CheckRed(value) then
            self._selectInfo = value
            self._selectId = value.AvatarID
            self._index = index + 1
        end
    end

    if self._selectInfo == nil then
        self._selectInfo = self._socialTable[1]
    end

    if self._selectInfo then
        self:RefreshShowIcon(self._selectInfo, self._index)
    end

    self:PlayScrollItem()
end

function SocialChangeAvatar:CheckDownloadModule()
    if self._wtCommonDownload == nil then
        return
    end

    if self._SocialType == 1 then
        local downloadType = LiteDownloadManager:GetDownloadCategary(self._selectId)
        if downloadType ~= self.downloadModuleName then
            self._wtCommonDownload:Collapsed()
            return
        end
    end

    local bDownloaded = self._wtCommonDownload:InitModuleKey(self.downloadModuleName)
    if not bDownloaded then
        self._wtCommonDownload:SelfHitTestInvisible()

        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleStateMaybeChanged,
            self._OnDownloadStateChange, self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult,
            self._ModuleDownloadResult, self)
    else
        self._wtCommonDownload:Collapsed()
    end
end

function SocialChangeAvatar:_ModuleDownloadResult(moduleName, bSuccess, errorCode, bShowTips)
    if self._wtCommonDownload == nil then
        return
    end
    if IsMobile() then
        logerror("[ProductPreview] _ModuleDownloadResult ")
        if moduleName ~= nil and moduleName == self.downloadModuleName then
            if bSuccess then
                logerror("[ProductPreview] _ModuleDownloadResult bDownloaded:true")
                self._wtCommonDownload:Collapsed()
                self:_OnPlayerSocialChange()
            else
                logerror("[ProductPreview] _ModuleDownloadResult bDownloaded:false")
                self._wtCommonDownload:SelfHitTestInvisible()
            end
        end
    end
end

function SocialChangeAvatar:_OnDownloadStateChange(moduleName, bDownloaded)
    if moduleName == self.downloadModuleName then
        if bDownloaded then
            self._wtCommonDownload:Collapsed()
        else
            self._wtCommonDownload:SelfHitTestInvisible()
        end
    end
end

function SocialChangeAvatar:RefreshSocialData()
    self._index = 1
    self._selectInfo = nil

    if self._SocialType == 1 then
        self:InitAvatarData()
    elseif self._SocialType == 2 then
        self:InitMilitaryData()
    end

    self:CheckDownloadModule()
end

function SocialChangeAvatar:RefreshUseChange()
    if self._SocialType == 1 then
        self:_OnUseAvatar()
    elseif self._SocialType == 2 then
        self:_OnUseMilitary()
    end
end

function SocialChangeAvatar:OnShowBegin()
    self:AddLuaEvent(Server.AccountServer.Events.evtPlayerHeadIconChange, self._OnPlayerSocialChange, self)
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtMilitaryChange, self._OnPlayerSocialChange, self)
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtGetSocialAvatarDataEnd, self._OnPlayerSocialChange, self)
    self:AddLuaEvent(Server.RoleInfoServer.Events.evtGetSocialAvatarRedUpdate, self.ClearCurRed, self)
    self._wtSocialList:RefreshAllItems()

    --- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_InitGamepadInputs()
    end
    --- END MODIFICATION
end

function SocialChangeAvatar:OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS
    if IsHD() then
        self:_DisableGamepadInputs()
    end
    -- END MODIFICATION
    self:RemoveAllLuaEvent()
end

function SocialChangeAvatar:OnOpen()

end

function SocialChangeAvatar:OnClose()
end

function SocialChangeAvatar:OnGetItemCount()
    return #self._socialTable
end

function SocialChangeAvatar:_OnProcessWidget(index, widget)
    local info = self._socialTable[index + 1]
    widget:SetParent(self, index + 1)
    widget:OnInitAvatarData(info)
    widget:RemoveUsingUI()
    widget:RemoveSelectUI()

    if self._selfSocialId == self._socialTable[index + 1].AvatarID then
        self._wtUsingWidget = widget
        self._wtUsingWidget:OnUsingAvatar()
    end

    if self._selectId == self._socialTable[index + 1].AvatarID then
        self._wtSelectWidget = widget
        self._wtSelectWidget:OnSelectAvatar()
        self:RefreshShowIcon(info, index + 1)
    end
    if self._SocialType == 1 and index == 0 then
        widget:SetUrlImage()
    end
end

function SocialChangeAvatar:SetSelectWidget(widget, index)
    if self._wtSelectWidget then
        self._wtSelectWidget:RemoveSelectUI()
    end
    self._wtSelectWidget = widget
    local info = self._socialTable[index]
    self._selectId = info.AvatarID
    self:CheckDownloadModule()
    self:RefreshShowIcon(info, index)
end

function SocialChangeAvatar:RefreshShowIcon(info, index)
    if index == 1 and self._SocialType == 1 then
        if info.url ~= "" then
            local urlHightLight = self:GetHigtLightUrl(info.url)
            local callback = CreateCallBack(self._SetUrlImage, self)
            if urlHightLight then
                Module.Social:SetProfileFromUrl(urlHightLight, callback, true, true)
            else
                Module.Social:SetProfileFromUrl(info.url, callback, true, true)
            end
        else
            self._wtSocialImage:AsyncSetImagePath(info.ResourceName, true)
        end
        self:RefreshSocialName(info)
        self:SetIconCanUse(info)
        return
    end

    -- Config.DefaultAvatarPath

    if info.ResourceName then
        self._wtSocialImage:AsyncSetImagePath(info.ResourceName, true)
    end

    if LiteDownloadManager:IsSupportLitePackage() and self._SocialType == 1 then
        if self._wtCommonDownload:IsVisible() then
            self._wtCommonDownload:Collapsed()
        end
    end

    self:RefreshSocialName(info)
    self:SetIconCanUse(info)
end

function SocialChangeAvatar:RefreshSocialName(info)
    self._selectInfo = info
    self._wtNameLabel:SetText(info.AvatarName)
    self._wtNameLabel:PlayAnim_ComputeTextBlock()
    self._wtAvatarDescription:SetText(info.AvatarDescription)
    local quality = ItemConfigTool.GetItemQuality(info.AvatarID) ~= 0 and ItemConfigTool.GetItemQuality(info.AvatarID) or
        1
    local itemColor = ItemConfigTool.GetItemQualityLinearColor(quality)
    self._wtQualityIcon:SetColorAndOpacity(itemColor)
    self._wtQualityIcon:AsyncSetImagePath(Module.Collection.Config.QualityIconMapping[quality])
end

function SocialChangeAvatar:_OnPlayerSocialChange()
    self:RefreshSocialData()
    self._wtSocialList:RefreshVisibleItems()
end

function SocialChangeAvatar:_OnUseIconClick()
    if self._selectInfo.Islock then
        self:RefreshUseChange()
    else
        if RoleInfoLogic.IsInMp() then
            if Module.Jump:CheckCanJumpByID(self._selectInfo.JumpIDMP) then
                Module.Jump:JumpByID(self._selectInfo.JumpIDMP)
            end
        else
            if Module.Jump:CheckCanJumpByID(self._selectInfo.JumpID) then
                Module.Jump:JumpByID(self._selectInfo.JumpID)
            end
        end
    end
end

function SocialChangeAvatar:_OnUseAvatar()
    Server.AccountServer:UpdatePlayerHeadIcon(self._selectId)
end

function SocialChangeAvatar:_OnUseMilitary()
    Server.RoleInfoServer:SetMilitaryId(self._selectId)
end

function SocialChangeAvatar:SetIconCanUse(info)
    self._wtChangeBtn:SetIsEnabledStyle(true)
    self._wtUnlockCondition:Collapsed()
    local NowTime = Facade.ClockManager:GetServerTimestamp()
    if info.Islock then
        if info.SocialType ~= LimitSocialTypeDef.NONE then
            if info.EndTime > NowTime then
                self._wtDescImage:AsyncSetImagePath(Module.RoleInfo.Config.Loc.SocialTimeImage, false)
                self._wtButtonDesc:SetText(string.format(Module.RoleInfo.Config.Loc.SocialLeftEnd,
                    TimeUtil.TransTimestamp2YYMMDDHHMMSSStr(info.EndTime)))
                self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.IconUse)
                self._wtUnlockCondition:Visible()
            else
                self._wtChangeBtn:SetIsEnabledStyle(false)
                self._wtUnlockCondition:Collapsed()
                self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.LockIcon)
            end
        else
            self._wtChangeBtn:SetIsEnabledStyle(true)
            self._wtUnlockCondition:Collapsed()
            self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.IconUse)
        end
    else

        if RoleInfoLogic.IsInMp() then
            if Module.Jump:CheckCanJumpByID(self._selectInfo.JumpIDMP, { bNoShowLoadWindow = true }) then
                self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.JumpGoto)
            else
                self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.LockIcon)
                self._wtChangeBtn:SetIsEnabledStyle(false)
            end
        else
            if Module.Jump:CheckCanJumpByID(self._selectInfo.JumpID, { bNoShowLoadWindow = true }) then
                self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.JumpGoto)
            else
                self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.LockIcon)
                self._wtChangeBtn:SetIsEnabledStyle(false)
            end
        end
        if RoleInfoLogic.IsInMp() then
            if tostring(info.ButtonDescMP) ~= "" then
                self._wtButtonDesc:SetText(info.ButtonDescMP)
                self._wtDescImage:AsyncSetImagePath(Module.RoleInfo.Config.Loc.SocialTipImage, false)
                self._wtUnlockCondition:Visible()
            else
                self._wtUnlockCondition:Collapsed()
            end
        else
            if tostring(info.ButtonDesc) ~= "" then
                self._wtButtonDesc:SetText(info.ButtonDesc)
                self._wtDescImage:AsyncSetImagePath(Module.RoleInfo.Config.Loc.SocialTipImage, false)
                self._wtUnlockCondition:Visible()
            else
                self._wtUnlockCondition:Collapsed()
            end
        end
    end
    if self._selfSocialId == info.AvatarID then
        self._wtChangeBtn:BP_SetMainTitle(Module.RoleInfo.Config.Loc.IconIsUse)
        self._wtChangeBtn:SetIsEnabledStyle(false)
    end
end

function SocialChangeAvatar:_SetUrlImage(ProfileImg)
    if not ResConvertUtil.IsEmptyImage(ProfileImg) then
        if self._wtSocialImage and self._wtSocialImage.SetBrush then
            self._wtSocialImage:SetBrush(ProfileImg.Brush)
        end
    end
end

function SocialChangeAvatar:GetHigtLightUrl(url)
    local urltbl = {
        "http://qh.qlogo.cn/ek_qqapp/",
        "http://qh.qlogo.cn/ek_qqthird/",
        "https://qh.qlogo.cn/ek_qqapp/",
        "https://qh.qlogo.cn/ek_qqthird/",
    }
    local urlHead = false
    for index, value in ipairs(urltbl) do
        if string.find(url, value) then
            urlHead = true
        end
    end
    if not urlHead then
        return nil
    end
    local last_slash_index = url:find("/[^/]*$") -- 查找最后一个斜杠及其后面的部分
    if last_slash_index then
        local number_str = url:sub(last_slash_index + 1) -- 获取最后一个斜杠后面的字符串
        local number = tonumber(number_str) -- 尝试转换为数字
        if number then
            url = url:sub(1, last_slash_index) .. "0" -- 替换为0
        end
    else
        return nil
    end
    return url
end

--- BEGIN MODIFICATION @ VIRTUOS
function SocialChangeAvatar:_InitGamepadInputs()
    if not self:IsVisible() then
        return
    end

    -- Informal fix to inputs missing issue caused by unexpected "OnShowBegin" calls from RoleInfoMainPanel and disordered lifecycle.
    self:_DisableGamepadInputs()

    if not self._navGroup then
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._wtSocialList, self, "Hittest")
        if self._navGroup then
            self._navGroup:AddNavWidgetToArray(self._wtSocialList)
            self._navGroup:SetScrollRecipient(self._wtSocialList)
            self._navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            self._navGroup:SetSimulatedClickIsIgnored(true)
        end
        -- WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)

        WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
    end

    if not self._gamepadSummaryList then
        self._gamepadSummaryList =
        {
            { actionName = "Confirm", func = self._OnUseIconClick, caller = self, bUIOnly = false },
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._gamepadSummaryList, false, false)
    end
end

function SocialChangeAvatar:_DisableGamepadInputs()
    if self._navGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._navGroup = nil
    end
    -- WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

    if self._gamepadSummaryList then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        self._gamepadSummaryList = nil
    end
end

--- END MODIFICATION

return SocialChangeAvatar
