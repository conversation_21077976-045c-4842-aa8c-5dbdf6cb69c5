----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGunsmith)
----- LOG FUNCTION AUTO GENERATE END -----------



local UAssembleWeaponDataLibrary = import "AssembleWeaponDataLibrary"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local GunsmithUIContextLogic = require "DFM.Business.Module.GunsmithModule.Logic.GunsmithUIContextLogic"

local GunsmithPurchaseLogic = {}

GunsmithPurchaseLogic.CURRENCY_ID = ECurrencyItemId.UnBindBankNote
GunsmithPurchaseLogic.CURRENCY_Type = ECurrencyClientType.OnlyUnBind

---@param dataContainer ItemUIDataContainer<GunsmithFluctuationPopItemUIData>
function GunsmithPurchaseLogic.FormatItemUIData2PBCSCheapBuyRes(dataContainer, weaponGUID)
    -- local req = pb.CSCheapBuyReq:New()
    local req = pb.CSSerialCheapBuyReq:New()
    req.buy_list = {}
    req.scene = CSCheapBuyScene.CheapBuyScene_Gunsmith

    local count = dataContainer:GetCount()
    for i = 1, count do
        local data = dataContainer:Get(i)
        GunsmithPurchaseLogic._FormatItemUIData(data, req.buy_list, weaponGUID)
    end
    return req
end

function GunsmithPurchaseLogic._FormatItemUIData(itemUIData, buy_list, weaponGUID)
    local bSelect = itemUIData:GetSelect()
    if not bSelect then
        return
    end

    local purchaseData = itemUIData:GetData()
    if purchaseData == nil then
        return
    end

    local socketGUID = itemUIData:GetSocketGUID()
    local channel = purchaseData:GetChannel()
    local count = purchaseData:GetUseCount()
    if channel == CheapBuyChannel.Channel_Mall then
        GunsmithPurchaseLogic._FormatItemUIData4Mall(purchaseData, buy_list, weaponGUID, socketGUID, count)
    elseif channel == CheapBuyChannel.Channel_Auction then
        GunsmithPurchaseLogic._FormatItemUIData4Auction(purchaseData, buy_list, weaponGUID, socketGUID, count)
    end
end

function GunsmithPurchaseLogic._FormatItemUIData4Mall(purchaseData, buy_list, weaponGUID, socketGUID, count, bEquip)
    local shopItem = purchaseData:GetShopItem()
    if shopItem == nil then
        return
    end

    local in_mall_prop = shopItem:GetRawProp()
    if in_mall_prop == nil then
        return
    end

    local buyinfo = pb.PropBuyInfo:New()
    buyinfo.channel = CheapBuyChannel.Channel_Mall
    table.insert(buy_list, buyinfo)

    --- mall_prop
    local mall_prop = clone(in_mall_prop)
    buyinfo.mall_prop = mall_prop

    mall_prop.prop_info.num = count

    --- mall_prices
    local mall_prices = pb.PropPrice:New()
    buyinfo.mall_prices = {}
    buyinfo.mall_prices[1] = mall_prices
    mall_prices.money_type = ECurrencyItemId.UnBindBankNote

    local price = shopItem:GetShopPropBuyPrice()
    local totalPrice = price * count
    mall_prices.price = totalPrice

    bEquip = setdefault(bEquip, false)
    if not bEquip then
        return
    end
    local itemID =  mall_prop.prop_info.id
    local assembleInfo = GunsmithPurchaseLogic._FormatItemUIData4AssembleInfo(socketGUID, itemID, weaponGUID)
    mall_prop.assemble_info = assembleInfo
end

function GunsmithPurchaseLogic._FormatItemUIData4Auction(purchaseData, buy_list, weaponGUID, socketGUID, count, bEquip)
    local buyinfo = pb.PropBuyInfo:New()
    buyinfo.channel = CheapBuyChannel.Channel_Auction
    table.insert(buy_list, buyinfo)

    local auction_prop = pb.CSAuctionBatchBuyReqInfo:New()
    buyinfo.auction_prop = auction_prop

    local id = purchaseData:GetID()
    id = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(id)
    auction_prop.prop_id = id
    auction_prop.currency = ECurrencyItemId.UnBindBankNote

    local price = purchaseData:GetPrice()
    auction_prop.price = price

    auction_prop.total_num = count

    bEquip = setdefault(bEquip, false)
    if not bEquip then
        return
    end
    local assembleInfo = GunsmithPurchaseLogic._FormatItemUIData4AssembleInfo(socketGUID, id, weaponGUID)
    auction_prop.assemble_info = assembleInfo
end

function GunsmithPurchaseLogic._FormatItemUIData4AssembleInfo(socketGUID, itemID, targetGUID)
    local info = pb.AssembleInfo:New()
    info.pos_guid = socketGUID
    info.id = itemID
    info.target_gid = targetGUID
    info.num = 1
    return info
end

-- 单个配件购买
function GunsmithPurchaseLogic.SingleItemUIData2PBCSCheapBuyRes(itemUIData, weaponGUID, socketGUID)
    -- local req = pb.CSCheapBuyReq:New()
    local req = pb.CSSerialCheapBuyReq:New()
    req.buy_list = {}
    req.scene = CSCheapBuyScene.CheapBuyScene_Gunsmith

    if itemUIData == nil then
        return
    end

    local bEquip = GunsmithPurchaseLogic._InternalGetIsEquip(socketGUID)

    local channel = itemUIData:GetBuyChannel()
    if channel == CheapBuyChannel.Channel_Mall then
        GunsmithPurchaseLogic._FormatItemUIData4Mall(itemUIData, req.buy_list, weaponGUID, socketGUID, 1, bEquip)
    elseif channel == CheapBuyChannel.Channel_Auction then
        GunsmithPurchaseLogic._FormatItemUIData4Auction(itemUIData, req.buy_list, weaponGUID, socketGUID, 1, bEquip)
    end

    return req
end

function GunsmithPurchaseLogic._InternalGetIsEquip(socketGUID)
    local weaponDescription = GunsmithUIContextLogic.GetWeaponDescription4BackupServer()
    local serverItemID, serverItemGUID, serverFlag = GunsmithUIContextLogic.GetWeaponDescriptionSocketItemIDs(weaponDescription, socketGUID)
    return serverItemID == nil or serverItemID == 0
end

return GunsmithPurchaseLogic