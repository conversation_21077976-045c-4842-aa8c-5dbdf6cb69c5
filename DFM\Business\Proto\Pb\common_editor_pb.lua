--common.protoencode&decode functions.
function pb.pb_EmptyDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Empty) or {} 
    return tb
end

function pb.pb_EmptyEncode(tb, encoder)
end

function pb.pb_IsolationInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IsolationInfo) or {} 
    local __type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __duration = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __duration ~= 0 then tb.duration = __duration end
    local __start_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    return tb
end

function pb.pb_IsolationInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addu32(1, tb.type)    end
    if(tb.duration) then    encoder:addi64(2, tb.duration)    end
    if(tb.start_time) then    encoder:addi64(3, tb.start_time)    end
end

function pb.pb_CSClientInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSClientInfo) or {} 
    local __system_sofrware = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __system_sofrware ~= "" then tb.system_sofrware = __system_sofrware end
    local __system_hardward = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __system_hardward ~= "" then tb.system_hardward = __system_hardward end
    local __telecom_oper = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __telecom_oper ~= "" then tb.telecom_oper = __telecom_oper end
    local __network = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __network ~= "" then tb.network = __network end
    local __screen_width = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __screen_width ~= 0 then tb.screen_width = __screen_width end
    local __screen_height = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __screen_height ~= 0 then tb.screen_height = __screen_height end
    local __denisty = decoder:getfloat(7)
    if not PB_USE_DEFAULT_TABLE or __denisty ~= 0 then tb.denisty = __denisty end
    local __login_channel = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __login_channel ~= 0 then tb.login_channel = __login_channel end
    local __cpu_hardware = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __cpu_hardware ~= "" then tb.cpu_hardware = __cpu_hardware end
    local __memory = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __memory ~= 0 then tb.memory = __memory end
    local __gl_render = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __gl_render ~= "" then tb.gl_render = __gl_render end
    local __gl_version = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __gl_version ~= "" then tb.gl_version = __gl_version end
    local __device_id = decoder:getstr(13)
    if not PB_USE_DEFAULT_TABLE or __device_id ~= "" then tb.device_id = __device_id end
    local __client_ip = decoder:getstr(14)
    if not PB_USE_DEFAULT_TABLE or __client_ip ~= "" then tb.client_ip = __client_ip end
    local __client_ip_v6 = decoder:getstr(15)
    if not PB_USE_DEFAULT_TABLE or __client_ip_v6 ~= "" then tb.client_ip_v6 = __client_ip_v6 end
    local __equipment_type = decoder:geti32(16)
    if not PB_USE_DEFAULT_TABLE or __equipment_type ~= 0 then tb.equipment_type = __equipment_type end
    local __build_info = decoder:getstr(17)
    if not PB_USE_DEFAULT_TABLE or __build_info ~= "" then tb.build_info = __build_info end
    local __appsflyer_device_id = decoder:getstr(18)
    if not PB_USE_DEFAULT_TABLE or __appsflyer_device_id ~= "" then tb.appsflyer_device_id = __appsflyer_device_id end
    local __oaid = decoder:getstr(19)
    if not PB_USE_DEFAULT_TABLE or __oaid ~= "" then tb.oaid = __oaid end
    local __xid = decoder:getstr(20)
    if not PB_USE_DEFAULT_TABLE or __xid ~= "" then tb.xid = __xid end
    local __caid = decoder:getstr(21)
    if not PB_USE_DEFAULT_TABLE or __caid ~= "" then tb.caid = __caid end
    local __hotfix = decoder:getstr(22)
    if not PB_USE_DEFAULT_TABLE or __hotfix ~= "" then tb.hotfix = __hotfix end
    local __client_version = decoder:getstr(23)
    if not PB_USE_DEFAULT_TABLE or __client_version ~= "" then tb.client_version = __client_version end
    local __match_client_type = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __match_client_type ~= 0 then tb.match_client_type = __match_client_type end
    local __cpu_brand = decoder:getstr(25)
    if not PB_USE_DEFAULT_TABLE or __cpu_brand ~= "" then tb.cpu_brand = __cpu_brand end
    local __cpu_chipset = decoder:getstr(26)
    if not PB_USE_DEFAULT_TABLE or __cpu_chipset ~= "" then tb.cpu_chipset = __cpu_chipset end
    local __user_agent = decoder:getstr(27)
    if not PB_USE_DEFAULT_TABLE or __user_agent ~= "" then tb.user_agent = __user_agent end
    local __system_language = decoder:getstr(28)
    if not PB_USE_DEFAULT_TABLE or __system_language ~= "" then tb.system_language = __system_language end
    local __game_language = decoder:getstr(29)
    if not PB_USE_DEFAULT_TABLE or __game_language ~= "" then tb.game_language = __game_language end
    local __game_center = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __game_center ~= 0 then tb.game_center = __game_center end
    local __core_user_id = decoder:getstr(31)
    if not PB_USE_DEFAULT_TABLE or __core_user_id ~= "" then tb.core_user_id = __core_user_id end
    local __mobile_network = decoder:getstr(32)
    if not PB_USE_DEFAULT_TABLE or __mobile_network ~= "" then tb.mobile_network = __mobile_network end
    local __vendor_id = decoder:getstr(33)
    if not PB_USE_DEFAULT_TABLE or __vendor_id ~= "" then tb.vendor_id = __vendor_id end
    local __device_imei = decoder:getstr(34)
    if not PB_USE_DEFAULT_TABLE or __device_imei ~= "" then tb.device_imei = __device_imei end
    local __idfv = decoder:getstr(35)
    if not PB_USE_DEFAULT_TABLE or __idfv ~= "" then tb.idfv = __idfv end
    return tb
end

function pb.pb_CSClientInfoEncode(tb, encoder)
    if(tb.system_sofrware) then    encoder:addstr(1, tb.system_sofrware)    end
    if(tb.system_hardward) then    encoder:addstr(2, tb.system_hardward)    end
    if(tb.telecom_oper) then    encoder:addstr(3, tb.telecom_oper)    end
    if(tb.network) then    encoder:addstr(4, tb.network)    end
    if(tb.screen_width) then    encoder:addi32(5, tb.screen_width)    end
    if(tb.screen_height) then    encoder:addi32(6, tb.screen_height)    end
    if(tb.denisty) then    encoder:addfloat(7, tb.denisty)    end
    if(tb.login_channel) then    encoder:addi32(8, tb.login_channel)    end
    if(tb.cpu_hardware) then    encoder:addstr(9, tb.cpu_hardware)    end
    if(tb.memory) then    encoder:addi32(10, tb.memory)    end
    if(tb.gl_render) then    encoder:addstr(11, tb.gl_render)    end
    if(tb.gl_version) then    encoder:addstr(12, tb.gl_version)    end
    if(tb.device_id) then    encoder:addstr(13, tb.device_id)    end
    if(tb.client_ip) then    encoder:addstr(14, tb.client_ip)    end
    if(tb.client_ip_v6) then    encoder:addstr(15, tb.client_ip_v6)    end
    if(tb.equipment_type) then    encoder:addi32(16, tb.equipment_type)    end
    if(tb.build_info) then    encoder:addstr(17, tb.build_info)    end
    if(tb.appsflyer_device_id) then    encoder:addstr(18, tb.appsflyer_device_id)    end
    if(tb.oaid) then    encoder:addstr(19, tb.oaid)    end
    if(tb.xid) then    encoder:addstr(20, tb.xid)    end
    if(tb.caid) then    encoder:addstr(21, tb.caid)    end
    if(tb.hotfix) then    encoder:addstr(22, tb.hotfix)    end
    if(tb.client_version) then    encoder:addstr(23, tb.client_version)    end
    if(tb.match_client_type) then    encoder:addi32(24, tb.match_client_type)    end
    if(tb.cpu_brand) then    encoder:addstr(25, tb.cpu_brand)    end
    if(tb.cpu_chipset) then    encoder:addstr(26, tb.cpu_chipset)    end
    if(tb.user_agent) then    encoder:addstr(27, tb.user_agent)    end
    if(tb.system_language) then    encoder:addstr(28, tb.system_language)    end
    if(tb.game_language) then    encoder:addstr(29, tb.game_language)    end
    if(tb.game_center) then    encoder:addu32(30, tb.game_center)    end
    if(tb.core_user_id) then    encoder:addstr(31, tb.core_user_id)    end
    if(tb.mobile_network) then    encoder:addstr(32, tb.mobile_network)    end
    if(tb.vendor_id) then    encoder:addstr(33, tb.vendor_id)    end
    if(tb.device_imei) then    encoder:addstr(34, tb.device_imei)    end
    if(tb.idfv) then    encoder:addstr(35, tb.idfv)    end
end

function pb.pb_ClientIntlInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ClientIntlInfo) or {} 
    local __channel_id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __channel_id ~= 0 then tb.channel_id = __channel_id end
    local __sdk_version = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __sdk_version ~= "" then tb.sdk_version = __sdk_version end
    local __token = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __token ~= "" then tb.token = __token end
    local __intl_country_belonging = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __intl_country_belonging ~= 0 then tb.intl_country_belonging = __intl_country_belonging end
    return tb
end

function pb.pb_ClientIntlInfoEncode(tb, encoder)
    if(tb.channel_id) then    encoder:addi32(1, tb.channel_id)    end
    if(tb.sdk_version) then    encoder:addstr(2, tb.sdk_version)    end
    if(tb.token) then    encoder:addstr(3, tb.token)    end
    if(tb.intl_country_belonging) then    encoder:addi32(4, tb.intl_country_belonging)    end
end

function pb.pb_IntlParentControlInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IntlParentControlInfo) or {} 
    local __enable = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __enable ~= false then tb.enable = __enable end
    local __weekday_time_start = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __weekday_time_start ~= "" then tb.weekday_time_start = __weekday_time_start end
    local __weekday_time_end = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __weekday_time_end ~= "" then tb.weekday_time_end = __weekday_time_end end
    local __weekday_time_limit = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __weekday_time_limit ~= 0 then tb.weekday_time_limit = __weekday_time_limit end
    local __weekend_time_start = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __weekend_time_start ~= "" then tb.weekend_time_start = __weekend_time_start end
    local __weekend_time_end = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __weekend_time_end ~= "" then tb.weekend_time_end = __weekend_time_end end
    local __weekend_time_limit = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __weekend_time_limit ~= 0 then tb.weekend_time_limit = __weekend_time_limit end
    return tb
end

function pb.pb_IntlParentControlInfoEncode(tb, encoder)
    if(tb.enable) then    encoder:addbool(1, tb.enable)    end
    if(tb.weekday_time_start) then    encoder:addstr(2, tb.weekday_time_start)    end
    if(tb.weekday_time_end) then    encoder:addstr(3, tb.weekday_time_end)    end
    if(tb.weekday_time_limit) then    encoder:addi32(4, tb.weekday_time_limit)    end
    if(tb.weekend_time_start) then    encoder:addstr(5, tb.weekend_time_start)    end
    if(tb.weekend_time_end) then    encoder:addstr(6, tb.weekend_time_end)    end
    if(tb.weekend_time_limit) then    encoder:addi32(7, tb.weekend_time_limit)    end
end

function pb.pb_ClientGwalletInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ClientGwalletInfo) or {} 
    local __req_from = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __req_from ~= 0 then tb.req_from = __req_from end
    local __user_ip = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __user_ip ~= "" then tb.user_ip = __user_ip end
    local __language_code = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __language_code ~= "" then tb.language_code = __language_code end
    local __user_access_token = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __user_access_token ~= "" then tb.user_access_token = __user_access_token end
    return tb
end

function pb.pb_ClientGwalletInfoEncode(tb, encoder)
    if(tb.req_from) then    encoder:addi32(1, tb.req_from)    end
    if(tb.user_ip) then    encoder:addstr(2, tb.user_ip)    end
    if(tb.language_code) then    encoder:addstr(3, tb.language_code)    end
    if(tb.user_access_token) then    encoder:addstr(4, tb.user_access_token)    end
end

function pb.pb_HostInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HostInfo) or {} 
    local __ds_domain = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __ds_domain ~= "" then tb.ds_domain = __ds_domain end
    tb.ds_ip_info = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.ds_ip_info[k] = pb.pb_DsIpInfoDecode(v)
    end
    return tb
end

function pb.pb_HostInfoEncode(tb, encoder)
    if(tb.ds_domain) then    encoder:addstr(1, tb.ds_domain)    end
    if(tb.ds_ip_info) then
        for i=1,#(tb.ds_ip_info) do
            pb.pb_DsIpInfoEncode(tb.ds_ip_info[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_DsIpInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsIpInfo) or {} 
    local __ds_textual_ip = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __ds_textual_ip ~= "" then tb.ds_textual_ip = __ds_textual_ip end
    local __ds_port = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    return tb
end

function pb.pb_DsIpInfoEncode(tb, encoder)
    if(tb.ds_textual_ip) then    encoder:addstr(1, tb.ds_textual_ip)    end
    if(tb.ds_port) then    encoder:addu32(2, tb.ds_port)    end
end

function pb.pb_DsHostInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DsHostInfo) or {} 
    local __ds_ip = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __ds_ip ~= 0 then tb.ds_ip = __ds_ip end
    local __ds_port = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __ds_port ~= 0 then tb.ds_port = __ds_port end
    local __ds_isp = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __ds_isp ~= 0 then tb.ds_isp = __ds_isp end
    return tb
end

function pb.pb_DsHostInfoEncode(tb, encoder)
    if(tb.ds_ip) then    encoder:addu32(1, tb.ds_ip)    end
    if(tb.ds_port) then    encoder:addu32(2, tb.ds_port)    end
    if(tb.ds_isp) then    encoder:addu32(3, tb.ds_isp)    end
end

function pb.pb_PropGridPageDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropGridPage) or {} 
    local __grid_page_id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __grid_page_id ~= 0 then tb.grid_page_id = __grid_page_id end
    local __tag = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __tag ~= 0 then tb.tag = __tag end
    local __grid_length = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __grid_length ~= 0 then tb.grid_length = __grid_length end
    local __grid_width = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __grid_width ~= 0 then tb.grid_width = __grid_width end
    local __create_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __create_time ~= 0 then tb.create_time = __create_time end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.props[k] = pb.pb_PropInfoDecode(v)
    end
    tb.prop_types = decoder:geti32ary(5)
    return tb
end

function pb.pb_PropGridPageEncode(tb, encoder)
    if(tb.grid_page_id) then    encoder:addi32(1, tb.grid_page_id)    end
    if(tb.tag) then    encoder:addi32(6, tb.tag)    end
    if(tb.grid_length) then    encoder:addi32(2, tb.grid_length)    end
    if(tb.grid_width) then    encoder:addi32(3, tb.grid_width)    end
    if(tb.create_time) then    encoder:addi64(7, tb.create_time)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_PropInfoEncode(tb.props[i], encoder:addsubmsg(4))
        end
    end
    if(tb.prop_types) then    encoder:addi32(5, tb.prop_types)    end
end

function pb.pb_UsePropCmdDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_UsePropCmd) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __prop_gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __prop_gid ~= 0 then tb.prop_gid = __prop_gid end
    local __num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __target_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __target_id ~= 0 then tb.target_id = __target_id end
    local __target_gid = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __target_gid ~= 0 then tb.target_gid = __target_gid end
    return tb
end

function pb.pb_UsePropCmdEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.prop_gid) then    encoder:addu64(2, tb.prop_gid)    end
    if(tb.num) then    encoder:addi64(3, tb.num)    end
    if(tb.target_id) then    encoder:addu64(4, tb.target_id)    end
    if(tb.target_gid) then    encoder:addu64(5, tb.target_gid)    end
end

function pb.pb_CurrencyDescDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CurrencyDesc) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __last_update_time = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __last_update_time ~= 0 then tb.last_update_time = __last_update_time end
    local __src_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __src_id ~= 0 then tb.src_id = __src_id end
    local __src_num = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __src_num ~= 0 then tb.src_num = __src_num end
    return tb
end

function pb.pb_CurrencyDescEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.num) then    encoder:addi64(2, tb.num)    end
    if(tb.last_update_time) then    encoder:addu64(3, tb.last_update_time)    end
    if(tb.src_id) then    encoder:addu64(4, tb.src_id)    end
    if(tb.src_num) then    encoder:addi64(5, tb.src_num)    end
end

function pb.pb_CurrencyInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CurrencyInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __src_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __src_id ~= 0 then tb.src_id = __src_id end
    local __src_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __src_num ~= 0 then tb.src_num = __src_num end
    local __reason = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    return tb
end

function pb.pb_CurrencyInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.num) then    encoder:addi64(2, tb.num)    end
    if(tb.src_id) then    encoder:addu64(3, tb.src_id)    end
    if(tb.src_num) then    encoder:addi64(4, tb.src_num)    end
    if(tb.reason) then    encoder:addi32(5, tb.reason)    end
end

function pb.pb_PositionChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PositionChange) or {} 
    local __pos_id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __pos_id ~= 0 then tb.pos_id = __pos_id end
    local __tag = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __tag ~= 0 then tb.tag = __tag end
    local __create_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __create_time ~= 0 then tb.create_time = __create_time end
    tb.space = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.space[k] = pb.pb_GridSizeDecode(v)
    end
    local __change_type = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __change_type ~= 0 then tb.change_type = __change_type end
    local __src_prop_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __src_prop_id ~= 0 then tb.src_prop_id = __src_prop_id end
    return tb
end

function pb.pb_PositionChangeEncode(tb, encoder)
    if(tb.pos_id) then    encoder:addi32(1, tb.pos_id)    end
    if(tb.tag) then    encoder:addi32(3, tb.tag)    end
    if(tb.create_time) then    encoder:addi64(4, tb.create_time)    end
    if(tb.space) then
        for i=1,#(tb.space) do
            pb.pb_GridSizeEncode(tb.space[i], encoder:addsubmsg(2))
        end
    end
    if(tb.change_type) then    encoder:addu32(5, tb.change_type)    end
    if(tb.src_prop_id) then    encoder:addu64(6, tb.src_prop_id)    end
end

function pb.pb_PropChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropChange) or {} 
    local __change_type = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __change_type ~= 0 then tb.change_type = __change_type end
    tb.src = pb.pb_PropLocationDecode(decoder:getsubmsg(5))
    tb.dest = pb.pb_PropLocationDecode(decoder:getsubmsg(6))
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(3))
    local __channel = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __channel ~= 0 then tb.channel = __channel end
    local __delta = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __delta ~= 0 then tb.delta = __delta end
    local __exceed_percent = decoder:getdouble(9)
    if not PB_USE_DEFAULT_TABLE or __exceed_percent ~= 0 then tb.exceed_percent = __exceed_percent end
    local __MatchUUID = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __MatchUUID ~= "" then tb.MatchUUID = __MatchUUID end
    local __is_presented = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __is_presented ~= false then tb.is_presented = __is_presented end
    return tb
end

function pb.pb_PropChangeEncode(tb, encoder)
    if(tb.change_type) then    encoder:addu32(4, tb.change_type)    end
    if(tb.src) then    pb.pb_PropLocationEncode(tb.src, encoder:addsubmsg(5))    end
    if(tb.dest) then    pb.pb_PropLocationEncode(tb.dest, encoder:addsubmsg(6))    end
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(3))    end
    if(tb.channel) then    encoder:addi64(7, tb.channel)    end
    if(tb.delta) then    encoder:addi64(8, tb.delta)    end
    if(tb.exceed_percent) then    encoder:adddouble(9, tb.exceed_percent)    end
    if(tb.MatchUUID) then    encoder:addstr(10, tb.MatchUUID)    end
    if(tb.is_presented) then    encoder:addbool(11, tb.is_presented)    end
end

function pb.pb_CurrencyChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CurrencyChange) or {} 
    local __currency_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __delta = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __delta ~= 0 then tb.delta = __delta end
    local __current_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __current_num ~= 0 then tb.current_num = __current_num end
    local __src_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __src_id ~= 0 then tb.src_id = __src_id end
    local __src_num = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __src_num ~= 0 then tb.src_num = __src_num end
    return tb
end

function pb.pb_CurrencyChangeEncode(tb, encoder)
    if(tb.currency_id) then    encoder:addu64(1, tb.currency_id)    end
    if(tb.delta) then    encoder:addi64(2, tb.delta)    end
    if(tb.current_num) then    encoder:addi64(3, tb.current_num)    end
    if(tb.src_id) then    encoder:addu64(4, tb.src_id)    end
    if(tb.src_num) then    encoder:addi64(5, tb.src_num)    end
end

function pb.pb_DataChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DataChange) or {} 
    tb.prop_changes = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.prop_changes[k] = pb.pb_PropChangeDecode(v)
    end
    tb.currency_changes = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.currency_changes[k] = pb.pb_CurrencyChangeDecode(v)
    end
    local __reason = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __source = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __source ~= 0 then tb.source = __source end
    tb.pos_changes = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.pos_changes[k] = pb.pb_PositionChangeDecode(v)
    end
    local __cur_extension_num = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __cur_extension_num ~= 0 then tb.cur_extension_num = __cur_extension_num end
    local __max_extension_num = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __max_extension_num ~= 0 then tb.max_extension_num = __max_extension_num end
    local __upperlimit_extension_num = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __upperlimit_extension_num ~= 0 then tb.upperlimit_extension_num = __upperlimit_extension_num end
    tb.weapon_skin_setup = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.weapon_skin_setup[k] = pb.pb_WeaponSkinSetupDecode(v)
    end
    local __mall_sell_subreason = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __mall_sell_subreason ~= "" then tb.mall_sell_subreason = __mall_sell_subreason end
    local __room_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __match_uuid = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __match_uuid ~= "" then tb.match_uuid = __match_uuid end
    tb.recovery_amount_changes = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.recovery_amount_changes[k] = pb.pb_RecoveryAmountChangeDecode(v)
    end
    local __sub_reason = decoder:getstr(14)
    if not PB_USE_DEFAULT_TABLE or __sub_reason ~= "" then tb.sub_reason = __sub_reason end
    return tb
end

function pb.pb_DataChangeEncode(tb, encoder)
    if(tb.prop_changes) then
        for i=1,#(tb.prop_changes) do
            pb.pb_PropChangeEncode(tb.prop_changes[i], encoder:addsubmsg(1))
        end
    end
    if(tb.currency_changes) then
        for i=1,#(tb.currency_changes) do
            pb.pb_CurrencyChangeEncode(tb.currency_changes[i], encoder:addsubmsg(2))
        end
    end
    if(tb.reason) then    encoder:addi32(3, tb.reason)    end
    if(tb.source) then    encoder:addu64(4, tb.source)    end
    if(tb.pos_changes) then
        for i=1,#(tb.pos_changes) do
            pb.pb_PositionChangeEncode(tb.pos_changes[i], encoder:addsubmsg(5))
        end
    end
    if(tb.cur_extension_num) then    encoder:addi32(6, tb.cur_extension_num)    end
    if(tb.max_extension_num) then    encoder:addi32(7, tb.max_extension_num)    end
    if(tb.upperlimit_extension_num) then    encoder:addi32(8, tb.upperlimit_extension_num)    end
    if(tb.weapon_skin_setup) then
        for i=1,#(tb.weapon_skin_setup) do
            pb.pb_WeaponSkinSetupEncode(tb.weapon_skin_setup[i], encoder:addsubmsg(9))
        end
    end
    if(tb.mall_sell_subreason) then    encoder:addstr(10, tb.mall_sell_subreason)    end
    if(tb.room_id) then    encoder:addu64(11, tb.room_id)    end
    if(tb.match_uuid) then    encoder:addstr(12, tb.match_uuid)    end
    if(tb.recovery_amount_changes) then
        for i=1,#(tb.recovery_amount_changes) do
            pb.pb_RecoveryAmountChangeEncode(tb.recovery_amount_changes[i], encoder:addsubmsg(13))
        end
    end
    if(tb.sub_reason) then    encoder:addstr(14, tb.sub_reason)    end
end

function pb.pb_CompoundCommandDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CompoundCommand) or {} 
    local __compound_type = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __compound_type ~= 0 then tb.compound_type = __compound_type end
    local __root_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __root_id ~= 0 then tb.root_id = __root_id end
    local __root_gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __root_gid ~= 0 then tb.root_gid = __root_gid end
    local __node_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __node_id ~= 0 then tb.node_id = __node_id end
    local __node_gid = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __node_gid ~= 0 then tb.node_gid = __node_gid end
    local __slot = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __slot ~= 0 then tb.slot = __slot end
    local __target_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __target_id ~= 0 then tb.target_id = __target_id end
    local __target_gid = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __target_gid ~= 0 then tb.target_gid = __target_gid end
    local __target_num = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __target_num ~= 0 then tb.target_num = __target_num end
    tb.styles = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.styles[k] = pb.pb_WeaponStyleInfoDecode(v)
    end
    tb.loc = pb.pb_PropLocationDecode(decoder:getsubmsg(11))
    tb.swap_gun = pb.pb_PropInfoDecode(decoder:getsubmsg(12))
    tb.peer_swap_gun = pb.pb_PropInfoDecode(decoder:getsubmsg(13))
    tb.update_attr_props = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.update_attr_props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_CompoundCommandEncode(tb, encoder)
    if(tb.compound_type) then    encoder:addi32(8, tb.compound_type)    end
    if(tb.root_id) then    encoder:addu64(1, tb.root_id)    end
    if(tb.root_gid) then    encoder:addu64(2, tb.root_gid)    end
    if(tb.node_id) then    encoder:addu64(3, tb.node_id)    end
    if(tb.node_gid) then    encoder:addu64(4, tb.node_gid)    end
    if(tb.slot) then    encoder:addi32(5, tb.slot)    end
    if(tb.target_id) then    encoder:addu64(6, tb.target_id)    end
    if(tb.target_gid) then    encoder:addu64(7, tb.target_gid)    end
    if(tb.target_num) then    encoder:addi64(9, tb.target_num)    end
    if(tb.styles) then
        for i=1,#(tb.styles) do
            pb.pb_WeaponStyleInfoEncode(tb.styles[i], encoder:addsubmsg(10))
        end
    end
    if(tb.loc) then    pb.pb_PropLocationEncode(tb.loc, encoder:addsubmsg(11))    end
    if(tb.swap_gun) then    pb.pb_PropInfoEncode(tb.swap_gun, encoder:addsubmsg(12))    end
    if(tb.peer_swap_gun) then    pb.pb_PropInfoEncode(tb.peer_swap_gun, encoder:addsubmsg(13))    end
    if(tb.update_attr_props) then
        for i=1,#(tb.update_attr_props) do
            pb.pb_PropInfoEncode(tb.update_attr_props[i], encoder:addsubmsg(14))
        end
    end
end

function pb.pb_DepositSortConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DepositSortConfig) or {} 
    local __sort_style = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __sort_style ~= 0 then tb.sort_style = __sort_style end
    tb.sort_class_order = decoder:geti32ary(2)
    tb.extension_first_class = decoder:geti32ary(3)
    local __sort_every_enter = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __sort_every_enter ~= false then tb.sort_every_enter = __sort_every_enter end
    local __has_sorted = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __has_sorted ~= false then tb.has_sorted = __has_sorted end
    return tb
end

function pb.pb_DepositSortConfigEncode(tb, encoder)
    if(tb.sort_style) then    encoder:addi32(1, tb.sort_style)    end
    if(tb.sort_class_order) then    encoder:addi32(2, tb.sort_class_order)    end
    if(tb.extension_first_class) then    encoder:addi32(3, tb.extension_first_class)    end
    if(tb.sort_every_enter) then    encoder:addbool(4, tb.sort_every_enter)    end
    if(tb.has_sorted) then    encoder:addbool(5, tb.has_sorted)    end
end

function pb.pb_WeaponApplySkinCmdDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponApplySkinCmd) or {} 
    local __weapon_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    local __weapon_gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __weapon_gid ~= 0 then tb.weapon_gid = __weapon_gid end
    local __skin_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __skin_id ~= 0 then tb.skin_id = __skin_id end
    local __apply_all = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __apply_all ~= false then tb.apply_all = __apply_all end
    local __bag_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __bag_id ~= 0 then tb.bag_id = __bag_id end
    local __skin_gid = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __skin_gid ~= 0 then tb.skin_gid = __skin_gid end
    local __pendant_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __pendant_id ~= 0 then tb.pendant_id = __pendant_id end
    local __pendant_apply_all = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __pendant_apply_all ~= false then tb.pendant_apply_all = __pendant_apply_all end
    local __pendant_gid = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __pendant_gid ~= 0 then tb.pendant_gid = __pendant_gid end
    return tb
end

function pb.pb_WeaponApplySkinCmdEncode(tb, encoder)
    if(tb.weapon_id) then    encoder:addu64(1, tb.weapon_id)    end
    if(tb.weapon_gid) then    encoder:addu64(2, tb.weapon_gid)    end
    if(tb.skin_id) then    encoder:addu64(3, tb.skin_id)    end
    if(tb.apply_all) then    encoder:addbool(4, tb.apply_all)    end
    if(tb.bag_id) then    encoder:addu32(5, tb.bag_id)    end
    if(tb.skin_gid) then    encoder:addu64(6, tb.skin_gid)    end
    if(tb.pendant_id) then    encoder:addu64(7, tb.pendant_id)    end
    if(tb.pendant_apply_all) then    encoder:addbool(8, tb.pendant_apply_all)    end
    if(tb.pendant_gid) then    encoder:addu64(9, tb.pendant_gid)    end
end

function pb.pb_ConfigVersionInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ConfigVersionInfo) or {} 
    local __ver = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __ver ~= "" then tb.ver = __ver end
    local __load_time = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __load_time ~= 0 then tb.load_time = __load_time end
    return tb
end

function pb.pb_ConfigVersionInfoEncode(tb, encoder)
    if(tb.ver) then    encoder:addstr(1, tb.ver)    end
    if(tb.load_time) then    encoder:addu64(2, tb.load_time)    end
end

function pb.pb_MerchantGiftInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MerchantGiftInfo) or {} 
    local __intimacy_lvl = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __intimacy_lvl ~= 0 then tb.intimacy_lvl = __intimacy_lvl end
    local __gift_times = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __gift_times ~= 0 then tb.gift_times = __gift_times end
    local __gift_timestamp = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __gift_timestamp ~= 0 then tb.gift_timestamp = __gift_timestamp end
    return tb
end

function pb.pb_MerchantGiftInfoEncode(tb, encoder)
    if(tb.intimacy_lvl) then    encoder:addi32(1, tb.intimacy_lvl)    end
    if(tb.gift_times) then    encoder:addi32(2, tb.gift_times)    end
    if(tb.gift_timestamp) then    encoder:addi64(3, tb.gift_timestamp)    end
end

function pb.pb_RepConditionDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RepCondition) or {} 
    local __rep_type = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __rep_type ~= 0 then tb.rep_type = __rep_type end
    local __rep_lvl = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __rep_lvl ~= 0 then tb.rep_lvl = __rep_lvl end
    return tb
end

function pb.pb_RepConditionEncode(tb, encoder)
    if(tb.rep_type) then    encoder:addu64(1, tb.rep_type)    end
    if(tb.rep_lvl) then    encoder:addi64(2, tb.rep_lvl)    end
end

function pb.pb_MerchantDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Merchant) or {} 
    local __id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __vip_score = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __vip_score ~= 0 then tb.vip_score = __vip_score end
    local __vip_lvl = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __vip_lvl ~= 0 then tb.vip_lvl = __vip_lvl end
    local __intimacy = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __intimacy ~= 0 then tb.intimacy = __intimacy end
    local __intimacy_lvl = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __intimacy_lvl ~= 0 then tb.intimacy_lvl = __intimacy_lvl end
    local __today_intimacy = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __today_intimacy ~= 0 then tb.today_intimacy = __today_intimacy end
    local __intimacy_timestamp = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __intimacy_timestamp ~= 0 then tb.intimacy_timestamp = __intimacy_timestamp end
    tb.gifts = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.gifts[k] = pb.pb_MallPropInfoDecode(v)
    end
    tb.gift_info = pb.pb_MerchantGiftInfoDecode(decoder:getsubmsg(9))
    tb.gm_gifts = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.gm_gifts[k] = pb.pb_MallPropInfoDecode(v)
    end
    tb.unlock_conditions = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.unlock_conditions[k] = pb.pb_RepConditionDecode(v)
    end
    return tb
end

function pb.pb_MerchantEncode(tb, encoder)
    if(tb.id) then    encoder:addu32(1, tb.id)    end
    if(tb.vip_score) then    encoder:addi32(2, tb.vip_score)    end
    if(tb.vip_lvl) then    encoder:addi32(3, tb.vip_lvl)    end
    if(tb.intimacy) then    encoder:addi32(4, tb.intimacy)    end
    if(tb.intimacy_lvl) then    encoder:addi32(5, tb.intimacy_lvl)    end
    if(tb.today_intimacy) then    encoder:addi32(6, tb.today_intimacy)    end
    if(tb.intimacy_timestamp) then    encoder:addi64(7, tb.intimacy_timestamp)    end
    if(tb.gifts) then
        for i=1,#(tb.gifts) do
            pb.pb_MallPropInfoEncode(tb.gifts[i], encoder:addsubmsg(8))
        end
    end
    if(tb.gift_info) then    pb.pb_MerchantGiftInfoEncode(tb.gift_info, encoder:addsubmsg(9))    end
    if(tb.gm_gifts) then
        for i=1,#(tb.gm_gifts) do
            pb.pb_MallPropInfoEncode(tb.gm_gifts[i], encoder:addsubmsg(10))
        end
    end
    if(tb.unlock_conditions) then
        for i=1,#(tb.unlock_conditions) do
            pb.pb_RepConditionEncode(tb.unlock_conditions[i], encoder:addsubmsg(11))
        end
    end
end

function pb.pb_PropPriceDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PropPrice) or {} 
    local __money_type = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __money_type ~= 0 then tb.money_type = __money_type end
    local __price = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __price ~= 0 then tb.price = __price end
    local __gid = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __gid ~= 0 then tb.gid = __gid end
    return tb
end

function pb.pb_PropPriceEncode(tb, encoder)
    if(tb.money_type) then    encoder:addu64(1, tb.money_type)    end
    if(tb.price) then    encoder:addi64(2, tb.price)    end
    if(tb.gid) then    encoder:addu64(3, tb.gid)    end
end

function pb.pb_OptPropPriceDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_OptPropPrice) or {} 
    tb.prices = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.prices[k] = pb.pb_PropPriceDecode(v)
    end
    local __exchange_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __exchange_type ~= 0 then tb.exchange_type = __exchange_type end
    local __exchange_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    return tb
end

function pb.pb_OptPropPriceEncode(tb, encoder)
    if(tb.prices) then
        for i=1,#(tb.prices) do
            pb.pb_PropPriceEncode(tb.prices[i], encoder:addsubmsg(1))
        end
    end
    if(tb.exchange_type) then    encoder:addu32(2, tb.exchange_type)    end
    if(tb.exchange_id) then    encoder:addu64(3, tb.exchange_id)    end
end

function pb.pb_CurrencyExchangeRuleDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CurrencyExchangeRule) or {} 
    local __item_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __ratio = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __ratio ~= 0 then tb.ratio = __ratio end
    return tb
end

function pb.pb_CurrencyExchangeRuleEncode(tb, encoder)
    if(tb.item_id) then    encoder:addu64(1, tb.item_id)    end
    if(tb.ratio) then    encoder:adddouble(2, tb.ratio)    end
end

function pb.pb_CurrencyExchangeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CurrencyExchangeInfo) or {} 
    local __currency_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __item_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __enable_exchg = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __enable_exchg ~= false then tb.enable_exchg = __enable_exchg end
    tb.rules = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.rules[k] = pb.pb_CurrencyExchangeRuleDecode(v)
    end
    return tb
end

function pb.pb_CurrencyExchangeInfoEncode(tb, encoder)
    if(tb.currency_id) then    encoder:addu64(1, tb.currency_id)    end
    if(tb.item_id) then    encoder:addu64(2, tb.item_id)    end
    if(tb.enable_exchg) then    encoder:addbool(3, tb.enable_exchg)    end
    if(tb.rules) then
        for i=1,#(tb.rules) do
            pb.pb_CurrencyExchangeRuleEncode(tb.rules[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_MallExchangeRuleDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MallExchangeRule) or {} 
    local __item_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __ratio = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __ratio ~= 0 then tb.ratio = __ratio end
    return tb
end

function pb.pb_MallExchangeRuleEncode(tb, encoder)
    if(tb.item_id) then    encoder:addu64(1, tb.item_id)    end
    if(tb.ratio) then    encoder:addfloat(2, tb.ratio)    end
end

function pb.pb_MallExchangeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MallExchangeInfo) or {} 
    local __currency_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __currency_id ~= 0 then tb.currency_id = __currency_id end
    local __item_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __enable_exchg = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __enable_exchg ~= false then tb.enable_exchg = __enable_exchg end
    tb.rules = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.rules[k] = pb.pb_MallExchangeRuleDecode(v)
    end
    return tb
end

function pb.pb_MallExchangeInfoEncode(tb, encoder)
    if(tb.currency_id) then    encoder:addu64(1, tb.currency_id)    end
    if(tb.item_id) then    encoder:addu64(2, tb.item_id)    end
    if(tb.enable_exchg) then    encoder:addbool(3, tb.enable_exchg)    end
    if(tb.rules) then
        for i=1,#(tb.rules) do
            pb.pb_MallExchangeRuleEncode(tb.rules[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_AssembleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AssembleInfo) or {} 
    local __pos_guid = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __pos_guid ~= 0 then tb.pos_guid = __pos_guid end
    local __id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __target_gid = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __target_gid ~= 0 then tb.target_gid = __target_gid end
    local __num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __target_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __target_id ~= 0 then tb.target_id = __target_id end
    local __target_buy_gid = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __target_buy_gid ~= 0 then tb.target_buy_gid = __target_buy_gid end
    return tb
end

function pb.pb_AssembleInfoEncode(tb, encoder)
    if(tb.pos_guid) then    encoder:addu64(1, tb.pos_guid)    end
    if(tb.id) then    encoder:addu64(2, tb.id)    end
    if(tb.target_gid) then    encoder:addu64(3, tb.target_gid)    end
    if(tb.num) then    encoder:addi64(4, tb.num)    end
    if(tb.target_id) then    encoder:addu64(5, tb.target_id)    end
    if(tb.target_buy_gid) then    encoder:addu64(6, tb.target_buy_gid)    end
end

function pb.pb_MallPropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MallPropInfo) or {} 
    tb.prop_info = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __exchange_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __exchange_type = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __exchange_type ~= 0 then tb.exchange_type = __exchange_type end
    local __merchant_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __merchant_id ~= 0 then tb.merchant_id = __merchant_id end
    local __preset_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __preset_id ~= 0 then tb.preset_id = __preset_id end
    tb.prices = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.prices[k] = pb.pb_PropPriceDecode(v)
    end
    local __unlock_condition = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __unlock_condition ~= "" then tb.unlock_condition = __unlock_condition end
    local __order = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __order ~= 0 then tb.order = __order end
    local __for_sale = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __for_sale ~= false then tb.for_sale = __for_sale end
    local __is_panic_buying = decoder:getbool(12)
    if not PB_USE_DEFAULT_TABLE or __is_panic_buying ~= false then tb.is_panic_buying = __is_panic_buying end
    local __purchase_limit = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __purchase_limit ~= 0 then tb.purchase_limit = __purchase_limit end
    local __label_no1 = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __label_no1 ~= 0 then tb.label_no1 = __label_no1 end
    local __label_no2 = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __label_no2 ~= 0 then tb.label_no2 = __label_no2 end
    local __zero_dur_recycle_price = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __zero_dur_recycle_price ~= 0 then tb.zero_dur_recycle_price = __zero_dur_recycle_price end
    local __unlock_lv = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __unlock_lv ~= 0 then tb.unlock_lv = __unlock_lv end
    local __exchange_num = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __exchange_num ~= 0 then tb.exchange_num = __exchange_num end
    local __cannot_recycle = decoder:getbool(19)
    if not PB_USE_DEFAULT_TABLE or __cannot_recycle ~= false then tb.cannot_recycle = __cannot_recycle end
    local __mall_recycle_price = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __mall_recycle_price ~= 0 then tb.mall_recycle_price = __mall_recycle_price end
    local __zero_dur_mall_recycle_price = decoder:geti64(21)
    if not PB_USE_DEFAULT_TABLE or __zero_dur_mall_recycle_price ~= 0 then tb.zero_dur_mall_recycle_price = __zero_dur_mall_recycle_price end
    tb.assemble_info = pb.pb_AssembleInfoDecode(decoder:getsubmsg(22))
    local __step_size = decoder:geti64(23)
    if not PB_USE_DEFAULT_TABLE or __step_size ~= 0 then tb.step_size = __step_size end
    local __auto_sort_on_cheapbuy = decoder:getbool(24)
    if not PB_USE_DEFAULT_TABLE or __auto_sort_on_cheapbuy ~= false then tb.auto_sort_on_cheapbuy = __auto_sort_on_cheapbuy end
    local __unlock_quest_id = decoder:getu64(25)
    if not PB_USE_DEFAULT_TABLE or __unlock_quest_id ~= 0 then tb.unlock_quest_id = __unlock_quest_id end
    local __recycle_price_percent = decoder:getfloat(26)
    if not PB_USE_DEFAULT_TABLE or __recycle_price_percent ~= 0 then tb.recycle_price_percent = __recycle_price_percent end
    local __limit_time = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __limit_time ~= 0 then tb.limit_time = __limit_time end
    local __start_limit_unixtime = decoder:getu32(28)
    if not PB_USE_DEFAULT_TABLE or __start_limit_unixtime ~= 0 then tb.start_limit_unixtime = __start_limit_unixtime end
    local __end_limit_unixtime = decoder:getu32(29)
    if not PB_USE_DEFAULT_TABLE or __end_limit_unixtime ~= 0 then tb.end_limit_unixtime = __end_limit_unixtime end
    local __isitbound = decoder:getstr(30)
    if not PB_USE_DEFAULT_TABLE or __isitbound ~= "" then tb.isitbound = __isitbound end
    local __isitbound_val = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __isitbound_val ~= 0 then tb.isitbound_val = __isitbound_val end
    local __start_limit_unix = decoder:geti64(32)
    if not PB_USE_DEFAULT_TABLE or __start_limit_unix ~= 0 then tb.start_limit_unix = __start_limit_unix end
    local __end_limit_unix = decoder:geti64(33)
    if not PB_USE_DEFAULT_TABLE or __end_limit_unix ~= 0 then tb.end_limit_unix = __end_limit_unix end
    local __unlock_round = decoder:geti32(34)
    if not PB_USE_DEFAULT_TABLE or __unlock_round ~= 0 then tb.unlock_round = __unlock_round end
    return tb
end

function pb.pb_MallPropInfoEncode(tb, encoder)
    if(tb.prop_info) then    pb.pb_PropInfoEncode(tb.prop_info, encoder:addsubmsg(1))    end
    if(tb.exchange_id) then    encoder:addu64(5, tb.exchange_id)    end
    if(tb.exchange_type) then    encoder:addu32(6, tb.exchange_type)    end
    if(tb.merchant_id) then    encoder:addu32(3, tb.merchant_id)    end
    if(tb.preset_id) then    encoder:addu64(4, tb.preset_id)    end
    if(tb.prices) then
        for i=1,#(tb.prices) do
            pb.pb_PropPriceEncode(tb.prices[i], encoder:addsubmsg(7))
        end
    end
    if(tb.unlock_condition) then    encoder:addstr(9, tb.unlock_condition)    end
    if(tb.order) then    encoder:addi32(10, tb.order)    end
    if(tb.for_sale) then    encoder:addbool(11, tb.for_sale)    end
    if(tb.is_panic_buying) then    encoder:addbool(12, tb.is_panic_buying)    end
    if(tb.purchase_limit) then    encoder:addi32(13, tb.purchase_limit)    end
    if(tb.label_no1) then    encoder:addu32(14, tb.label_no1)    end
    if(tb.label_no2) then    encoder:addu32(15, tb.label_no2)    end
    if(tb.zero_dur_recycle_price) then    encoder:addi64(16, tb.zero_dur_recycle_price)    end
    if(tb.unlock_lv) then    encoder:addu32(17, tb.unlock_lv)    end
    if(tb.exchange_num) then    encoder:addi32(18, tb.exchange_num)    end
    if(tb.cannot_recycle) then    encoder:addbool(19, tb.cannot_recycle)    end
    if(tb.mall_recycle_price) then    encoder:addi64(20, tb.mall_recycle_price)    end
    if(tb.zero_dur_mall_recycle_price) then    encoder:addi64(21, tb.zero_dur_mall_recycle_price)    end
    if(tb.assemble_info) then    pb.pb_AssembleInfoEncode(tb.assemble_info, encoder:addsubmsg(22))    end
    if(tb.step_size) then    encoder:addi64(23, tb.step_size)    end
    if(tb.auto_sort_on_cheapbuy) then    encoder:addbool(24, tb.auto_sort_on_cheapbuy)    end
    if(tb.unlock_quest_id) then    encoder:addu64(25, tb.unlock_quest_id)    end
    if(tb.recycle_price_percent) then    encoder:addfloat(26, tb.recycle_price_percent)    end
    if(tb.limit_time) then    encoder:addi32(27, tb.limit_time)    end
    if(tb.start_limit_unixtime) then    encoder:addu32(28, tb.start_limit_unixtime)    end
    if(tb.end_limit_unixtime) then    encoder:addu32(29, tb.end_limit_unixtime)    end
    if(tb.isitbound) then    encoder:addstr(30, tb.isitbound)    end
    if(tb.isitbound_val) then    encoder:addu32(31, tb.isitbound_val)    end
    if(tb.start_limit_unix) then    encoder:addi64(32, tb.start_limit_unix)    end
    if(tb.end_limit_unix) then    encoder:addi64(33, tb.end_limit_unix)    end
    if(tb.unlock_round) then    encoder:addi32(34, tb.unlock_round)    end
end

function pb.pb_GreatEarnGoodsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GreatEarnGoods) or {} 
    local __item_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __group_num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __group_num ~= 0 then tb.group_num = __group_num end
    local __num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_GreatEarnGoodsEncode(tb, encoder)
    if(tb.item_id) then    encoder:addu64(1, tb.item_id)    end
    if(tb.group_num) then    encoder:addi64(2, tb.group_num)    end
    if(tb.num) then    encoder:addi64(3, tb.num)    end
end

function pb.pb_MallMysteryShopItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MallMysteryShopItem) or {} 
    tb.item = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    tb.exchanged_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.exchanged_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __exchange_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __exchange_num ~= 0 then tb.exchange_num = __exchange_num end
    local __bought = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __bought ~= false then tb.bought = __bought end
    local __iden = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __iden ~= "" then tb.iden = __iden end
    local __item_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __price = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __price ~= 0 then tb.price = __price end
    return tb
end

function pb.pb_MallMysteryShopItemEncode(tb, encoder)
    if(tb.item) then    pb.pb_PropInfoEncode(tb.item, encoder:addsubmsg(1))    end
    if(tb.exchanged_props) then
        for i=1,#(tb.exchanged_props) do
            pb.pb_PropInfoEncode(tb.exchanged_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.exchange_num) then    encoder:addi64(3, tb.exchange_num)    end
    if(tb.bought) then    encoder:addbool(4, tb.bought)    end
    if(tb.iden) then    encoder:addstr(5, tb.iden)    end
    if(tb.item_id) then    encoder:addu64(6, tb.item_id)    end
    if(tb.price) then    encoder:addi64(7, tb.price)    end
end

function pb.pb_MallPropGridPageDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MallPropGridPage) or {} 
    local __slot_sub_id = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __slot_sub_id ~= "" then tb.slot_sub_id = __slot_sub_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.props[k] = pb.pb_MallPropInfoDecode(v)
    end
    return tb
end

function pb.pb_MallPropGridPageEncode(tb, encoder)
    if(tb.slot_sub_id) then    encoder:addstr(5, tb.slot_sub_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_MallPropInfoEncode(tb.props[i], encoder:addsubmsg(4))
        end
    end
end

function pb.pb_MallLabelPropsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MallLabelProps) or {} 
    local __label_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __label_id ~= 0 then tb.label_id = __label_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.props[k] = pb.pb_MallPropInfoDecode(v)
    end
    return tb
end

function pb.pb_MallLabelPropsEncode(tb, encoder)
    if(tb.label_id) then    encoder:addu32(1, tb.label_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_MallPropInfoEncode(tb.props[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_MerchantChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MerchantChange) or {} 
    tb.merchant = pb.pb_MerchantDecode(decoder:getsubmsg(1))
    local __old_vip_level = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __old_vip_level ~= 0 then tb.old_vip_level = __old_vip_level end
    local __old_vip_score = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __old_vip_score ~= 0 then tb.old_vip_score = __old_vip_score end
    local __old_intimacy = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __old_intimacy ~= 0 then tb.old_intimacy = __old_intimacy end
    local __old_intimacy_level = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __old_intimacy_level ~= 0 then tb.old_intimacy_level = __old_intimacy_level end
    return tb
end

function pb.pb_MerchantChangeEncode(tb, encoder)
    if(tb.merchant) then    pb.pb_MerchantEncode(tb.merchant, encoder:addsubmsg(1))    end
    if(tb.old_vip_level) then    encoder:addi32(2, tb.old_vip_level)    end
    if(tb.old_vip_score) then    encoder:addi32(3, tb.old_vip_score)    end
    if(tb.old_intimacy) then    encoder:addi32(4, tb.old_intimacy)    end
    if(tb.old_intimacy_level) then    encoder:addi32(5, tb.old_intimacy_level)    end
end

function pb.pb_TradeChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TradeChange) or {} 
    local __trade_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __trade_type ~= 0 then tb.trade_type = __trade_type end
    tb.add_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.add_props[k] = pb.pb_MallPropInfoDecode(v)
    end
    tb.del_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.del_props[k] = pb.pb_MallPropInfoDecode(v)
    end
    return tb
end

function pb.pb_TradeChangeEncode(tb, encoder)
    if(tb.trade_type) then    encoder:addi32(1, tb.trade_type)    end
    if(tb.add_props) then
        for i=1,#(tb.add_props) do
            pb.pb_MallPropInfoEncode(tb.add_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.del_props) then
        for i=1,#(tb.del_props) do
            pb.pb_MallPropInfoEncode(tb.del_props[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_PlayerLimitMallItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerLimitMallItem) or {} 
    local __exchange_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __exchange_id ~= 0 then tb.exchange_id = __exchange_id end
    local __id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __recycle_num_daily = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __recycle_num_daily ~= 0 then tb.recycle_num_daily = __recycle_num_daily end
    local __first_buy_time = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __first_buy_time ~= 0 then tb.first_buy_time = __first_buy_time end
    local __curr_buy_limit = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __curr_buy_limit ~= 0 then tb.curr_buy_limit = __curr_buy_limit end
    local __next_period_start = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __next_period_start ~= 0 then tb.next_period_start = __next_period_start end
    local __limit_type = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __limit_type ~= 0 then tb.limit_type = __limit_type end
    local __buy_num = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __buy_num ~= 0 then tb.buy_num = __buy_num end
    return tb
end

function pb.pb_PlayerLimitMallItemEncode(tb, encoder)
    if(tb.exchange_id) then    encoder:addu64(1, tb.exchange_id)    end
    if(tb.id) then    encoder:addu64(2, tb.id)    end
    if(tb.recycle_num_daily) then    encoder:addi32(4, tb.recycle_num_daily)    end
    if(tb.first_buy_time) then    encoder:addi64(8, tb.first_buy_time)    end
    if(tb.curr_buy_limit) then    encoder:addi32(9, tb.curr_buy_limit)    end
    if(tb.next_period_start) then    encoder:addi64(10, tb.next_period_start)    end
    if(tb.limit_type) then    encoder:addi32(12, tb.limit_type)    end
    if(tb.buy_num) then    encoder:addi32(13, tb.buy_num)    end
end

function pb.pb_ProfileSeasonInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ProfileSeasonInfo) or {} 
    local __level = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __next_level_exp = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __next_level_exp ~= 0 then tb.next_level_exp = __next_level_exp end
    local __serial = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __serial ~= 0 then tb.serial = __serial end
    local __total_exp = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __total_exp ~= 0 then tb.total_exp = __total_exp end
    local __last_update_time = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __last_update_time ~= 0 then tb.last_update_time = __last_update_time end
    local __boon_exp_remain = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __boon_exp_remain ~= 0 then tb.boon_exp_remain = __boon_exp_remain end
    local __day_exp = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __day_exp ~= 0 then tb.day_exp = __day_exp end
    local __bonus_percent = decoder:getfloat(9)
    if not PB_USE_DEFAULT_TABLE or __bonus_percent ~= 0 then tb.bonus_percent = __bonus_percent end
    local __total_price = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __avg_survival_time = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __avg_survival_time ~= 0 then tb.avg_survival_time = __avg_survival_time end
    local __max_survival_time = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __max_survival_time ~= 0 then tb.max_survival_time = __max_survival_time end
    local __max_collection_price = decoder:geti64(25)
    if not PB_USE_DEFAULT_TABLE or __max_collection_price ~= 0 then tb.max_collection_price = __max_collection_price end
    local __settled = decoder:getbool(26)
    if not PB_USE_DEFAULT_TABLE or __settled ~= false then tb.settled = __settled end
    local __kd = decoder:getfloat(30)
    if not PB_USE_DEFAULT_TABLE or __kd ~= 0 then tb.kd = __kd end
    local __damage = decoder:getfloat(31)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __collection_price = decoder:getfloat(32)
    if not PB_USE_DEFAULT_TABLE or __collection_price ~= 0 then tb.collection_price = __collection_price end
    local __support = decoder:getfloat(33)
    if not PB_USE_DEFAULT_TABLE or __support ~= 0 then tb.support = __support end
    local __winning_percentage = decoder:getfloat(34)
    if not PB_USE_DEFAULT_TABLE or __winning_percentage ~= 0 then tb.winning_percentage = __winning_percentage end
    local __total_game_time = decoder:getu64(100)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_fight = decoder:getu32(101)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_escape = decoder:getu32(102)
    if not PB_USE_DEFAULT_TABLE or __total_escape ~= 0 then tb.total_escape = __total_escape end
    local __max_escape_streak = decoder:getu32(103)
    if not PB_USE_DEFAULT_TABLE or __max_escape_streak ~= 0 then tb.max_escape_streak = __max_escape_streak end
    local __cur_escape_streak = decoder:getu32(104)
    if not PB_USE_DEFAULT_TABLE or __cur_escape_streak ~= 0 then tb.cur_escape_streak = __cur_escape_streak end
    local __total_fracture = decoder:getu32(105)
    if not PB_USE_DEFAULT_TABLE or __total_fracture ~= 0 then tb.total_fracture = __total_fracture end
    local __total_bleed = decoder:getu32(106)
    if not PB_USE_DEFAULT_TABLE or __total_bleed ~= 0 then tb.total_bleed = __total_bleed end
    local __total_pain = decoder:getu32(107)
    if not PB_USE_DEFAULT_TABLE or __total_pain ~= 0 then tb.total_pain = __total_pain end
    local __total_food_consumed = decoder:getu32(108)
    if not PB_USE_DEFAULT_TABLE or __total_food_consumed ~= 0 then tb.total_food_consumed = __total_food_consumed end
    local __total_drug_consumed = decoder:getu32(109)
    if not PB_USE_DEFAULT_TABLE or __total_drug_consumed ~= 0 then tb.total_drug_consumed = __total_drug_consumed end
    local __total_healing = decoder:getu64(110)
    if not PB_USE_DEFAULT_TABLE or __total_healing ~= 0 then tb.total_healing = __total_healing end
    local __max_healing = decoder:getu64(111)
    if not PB_USE_DEFAULT_TABLE or __max_healing ~= 0 then tb.max_healing = __max_healing end
    local __avg_healing = decoder:getu64(112)
    if not PB_USE_DEFAULT_TABLE or __avg_healing ~= 0 then tb.avg_healing = __avg_healing end
    local __total_resurrect = decoder:getu32(113)
    if not PB_USE_DEFAULT_TABLE or __total_resurrect ~= 0 then tb.total_resurrect = __total_resurrect end
    local __max_resurrect = decoder:getu32(114)
    if not PB_USE_DEFAULT_TABLE or __max_resurrect ~= 0 then tb.max_resurrect = __max_resurrect end
    local __avg_resurrect = decoder:getu32(115)
    if not PB_USE_DEFAULT_TABLE or __avg_resurrect ~= 0 then tb.avg_resurrect = __avg_resurrect end
    local __total_death = decoder:getu32(116)
    if not PB_USE_DEFAULT_TABLE or __total_death ~= 0 then tb.total_death = __total_death end
    local __total_missing = decoder:getu32(117)
    if not PB_USE_DEFAULT_TABLE or __total_missing ~= 0 then tb.total_missing = __total_missing end
    local __total_move_distance = decoder:getu64(200)
    if not PB_USE_DEFAULT_TABLE or __total_move_distance ~= 0 then tb.total_move_distance = __total_move_distance end
    local __max_move_distance = decoder:getu64(201)
    if not PB_USE_DEFAULT_TABLE or __max_move_distance ~= 0 then tb.max_move_distance = __max_move_distance end
    local __avg_move_distance = decoder:getu64(202)
    if not PB_USE_DEFAULT_TABLE or __avg_move_distance ~= 0 then tb.avg_move_distance = __avg_move_distance end
    local __max_jump = decoder:getu64(203)
    if not PB_USE_DEFAULT_TABLE or __max_jump ~= 0 then tb.max_jump = __max_jump end
    local __total_walk_distance = decoder:getu64(204)
    if not PB_USE_DEFAULT_TABLE or __total_walk_distance ~= 0 then tb.total_walk_distance = __total_walk_distance end
    local __max_walk_distance = decoder:getu64(205)
    if not PB_USE_DEFAULT_TABLE or __max_walk_distance ~= 0 then tb.max_walk_distance = __max_walk_distance end
    local __avg_walk_distance = decoder:getu64(206)
    if not PB_USE_DEFAULT_TABLE or __avg_walk_distance ~= 0 then tb.avg_walk_distance = __avg_walk_distance end
    local __total_loot_corpse = decoder:getu32(300)
    if not PB_USE_DEFAULT_TABLE or __total_loot_corpse ~= 0 then tb.total_loot_corpse = __total_loot_corpse end
    local __max_loot_corpse = decoder:getu32(301)
    if not PB_USE_DEFAULT_TABLE or __max_loot_corpse ~= 0 then tb.max_loot_corpse = __max_loot_corpse end
    local __avg_loot_corpse = decoder:getu32(302)
    if not PB_USE_DEFAULT_TABLE or __avg_loot_corpse ~= 0 then tb.avg_loot_corpse = __avg_loot_corpse end
    local __total_strongbox_open = decoder:getu32(303)
    if not PB_USE_DEFAULT_TABLE or __total_strongbox_open ~= 0 then tb.total_strongbox_open = __total_strongbox_open end
    local __max_strongbox_open = decoder:getu32(304)
    if not PB_USE_DEFAULT_TABLE or __max_strongbox_open ~= 0 then tb.max_strongbox_open = __max_strongbox_open end
    local __avg_strongbox_open = decoder:getu32(305)
    if not PB_USE_DEFAULT_TABLE or __avg_strongbox_open ~= 0 then tb.avg_strongbox_open = __avg_strongbox_open end
    local __total_unlock_door = decoder:getu32(306)
    if not PB_USE_DEFAULT_TABLE or __total_unlock_door ~= 0 then tb.total_unlock_door = __total_unlock_door end
    local __max_unlock_door = decoder:getu32(307)
    if not PB_USE_DEFAULT_TABLE or __max_unlock_door ~= 0 then tb.max_unlock_door = __max_unlock_door end
    local __avg_unlock_door = decoder:getu32(308)
    if not PB_USE_DEFAULT_TABLE or __avg_unlock_door ~= 0 then tb.avg_unlock_door = __avg_unlock_door end
    local __total_game_egg = decoder:getu32(309)
    if not PB_USE_DEFAULT_TABLE or __total_game_egg ~= 0 then tb.total_game_egg = __total_game_egg end
    local __max_game_egg = decoder:getu32(310)
    if not PB_USE_DEFAULT_TABLE or __max_game_egg ~= 0 then tb.max_game_egg = __max_game_egg end
    local __avg_game_egg = decoder:getu32(311)
    if not PB_USE_DEFAULT_TABLE or __avg_game_egg ~= 0 then tb.avg_game_egg = __avg_game_egg end
    local __total_loot_container = decoder:getu32(312)
    if not PB_USE_DEFAULT_TABLE or __total_loot_container ~= 0 then tb.total_loot_container = __total_loot_container end
    local __max_loot_container = decoder:getu32(313)
    if not PB_USE_DEFAULT_TABLE or __max_loot_container ~= 0 then tb.max_loot_container = __max_loot_container end
    local __avg_loot_container = decoder:getu32(314)
    if not PB_USE_DEFAULT_TABLE or __avg_loot_container ~= 0 then tb.avg_loot_container = __avg_loot_container end
    local __total_kd = decoder:getfloat(400)
    if not PB_USE_DEFAULT_TABLE or __total_kd ~= 0 then tb.total_kd = __total_kd end
    local __max_kd = decoder:getfloat(401)
    if not PB_USE_DEFAULT_TABLE or __max_kd ~= 0 then tb.max_kd = __max_kd end
    local __avg_kd = decoder:getfloat(402)
    if not PB_USE_DEFAULT_TABLE or __avg_kd ~= 0 then tb.avg_kd = __avg_kd end
    local __total_damage = decoder:getu64(403)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __max_damage = decoder:getu64(404)
    if not PB_USE_DEFAULT_TABLE or __max_damage ~= 0 then tb.max_damage = __max_damage end
    local __avg_damage = decoder:getu64(405)
    if not PB_USE_DEFAULT_TABLE or __avg_damage ~= 0 then tb.avg_damage = __avg_damage end
    local __total_shoot_down = decoder:getu32(406)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_down ~= 0 then tb.total_shoot_down = __total_shoot_down end
    local __max_shoot_down = decoder:getu32(407)
    if not PB_USE_DEFAULT_TABLE or __max_shoot_down ~= 0 then tb.max_shoot_down = __max_shoot_down end
    local __avg_shoot_down = decoder:getu32(408)
    if not PB_USE_DEFAULT_TABLE or __avg_shoot_down ~= 0 then tb.avg_shoot_down = __avg_shoot_down end
    local __total_kill = decoder:getu32(409)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __max_kill = decoder:getu32(410)
    if not PB_USE_DEFAULT_TABLE or __max_kill ~= 0 then tb.max_kill = __max_kill end
    local __avg_kill = decoder:getu32(411)
    if not PB_USE_DEFAULT_TABLE or __avg_kill ~= 0 then tb.avg_kill = __avg_kill end
    local __total_kill_alone = decoder:getu32(412)
    if not PB_USE_DEFAULT_TABLE or __total_kill_alone ~= 0 then tb.total_kill_alone = __total_kill_alone end
    local __max_kill_alone = decoder:getu32(413)
    if not PB_USE_DEFAULT_TABLE or __max_kill_alone ~= 0 then tb.max_kill_alone = __max_kill_alone end
    local __avg_kill_alone = decoder:getu32(414)
    if not PB_USE_DEFAULT_TABLE or __avg_kill_alone ~= 0 then tb.avg_kill_alone = __avg_kill_alone end
    local __total_head_shot = decoder:getu32(415)
    if not PB_USE_DEFAULT_TABLE or __total_head_shot ~= 0 then tb.total_head_shot = __total_head_shot end
    local __max_head_shot = decoder:getu32(416)
    if not PB_USE_DEFAULT_TABLE or __max_head_shot ~= 0 then tb.max_head_shot = __max_head_shot end
    local __avg_head_shot = decoder:getu32(417)
    if not PB_USE_DEFAULT_TABLE or __avg_head_shot ~= 0 then tb.avg_head_shot = __avg_head_shot end
    local __total_hit = decoder:getfloat(418)
    if not PB_USE_DEFAULT_TABLE or __total_hit ~= 0 then tb.total_hit = __total_hit end
    local __max_hit = decoder:getfloat(419)
    if not PB_USE_DEFAULT_TABLE or __max_hit ~= 0 then tb.max_hit = __max_hit end
    local __avg_float = decoder:getfloat(420)
    if not PB_USE_DEFAULT_TABLE or __avg_float ~= 0 then tb.avg_float = __avg_float end
    local __max_hit_distance = decoder:getu64(421)
    if not PB_USE_DEFAULT_TABLE or __max_hit_distance ~= 0 then tb.max_hit_distance = __max_hit_distance end
    local __max_kill_distance = decoder:getu64(422)
    if not PB_USE_DEFAULT_TABLE or __max_kill_distance ~= 0 then tb.max_kill_distance = __max_kill_distance end
    local __total_kill_boss = decoder:getu32(423)
    if not PB_USE_DEFAULT_TABLE or __total_kill_boss ~= 0 then tb.total_kill_boss = __total_kill_boss end
    local __max_kill_boss = decoder:getu32(424)
    if not PB_USE_DEFAULT_TABLE or __max_kill_boss ~= 0 then tb.max_kill_boss = __max_kill_boss end
    local __avg_kill_boss = decoder:getu32(425)
    if not PB_USE_DEFAULT_TABLE or __avg_kill_boss ~= 0 then tb.avg_kill_boss = __avg_kill_boss end
    local __total_kill_player = decoder:getu32(426)
    if not PB_USE_DEFAULT_TABLE or __total_kill_player ~= 0 then tb.total_kill_player = __total_kill_player end
    local __max_kill_player = decoder:getu32(427)
    if not PB_USE_DEFAULT_TABLE or __max_kill_player ~= 0 then tb.max_kill_player = __max_kill_player end
    local __avg_kill_player = decoder:getu32(428)
    if not PB_USE_DEFAULT_TABLE or __avg_kill_player ~= 0 then tb.avg_kill_player = __avg_kill_player end
    local __total_kill_robot = decoder:getu32(429)
    if not PB_USE_DEFAULT_TABLE or __total_kill_robot ~= 0 then tb.total_kill_robot = __total_kill_robot end
    local __max_kill_robot = decoder:getu32(430)
    if not PB_USE_DEFAULT_TABLE or __max_kill_robot ~= 0 then tb.max_kill_robot = __max_kill_robot end
    local __avg_kill_robot = decoder:getu32(431)
    if not PB_USE_DEFAULT_TABLE or __avg_kill_robot ~= 0 then tb.avg_kill_robot = __avg_kill_robot end
    local __total_pistol_kill = decoder:getu32(432)
    if not PB_USE_DEFAULT_TABLE or __total_pistol_kill ~= 0 then tb.total_pistol_kill = __total_pistol_kill end
    local __max_pistol_kill = decoder:getu32(433)
    if not PB_USE_DEFAULT_TABLE or __max_pistol_kill ~= 0 then tb.max_pistol_kill = __max_pistol_kill end
    local __avg_pistol_kill = decoder:getu32(434)
    if not PB_USE_DEFAULT_TABLE or __avg_pistol_kill ~= 0 then tb.avg_pistol_kill = __avg_pistol_kill end
    local __total_melee_kill = decoder:getu32(435)
    if not PB_USE_DEFAULT_TABLE or __total_melee_kill ~= 0 then tb.total_melee_kill = __total_melee_kill end
    local __max_melee_kill = decoder:getu32(436)
    if not PB_USE_DEFAULT_TABLE or __max_melee_kill ~= 0 then tb.max_melee_kill = __max_melee_kill end
    local __avg_melee_kill = decoder:getu32(437)
    if not PB_USE_DEFAULT_TABLE or __avg_melee_kill ~= 0 then tb.avg_melee_kill = __avg_melee_kill end
    local __total_sniper_gun_kill = decoder:getu32(438)
    if not PB_USE_DEFAULT_TABLE or __total_sniper_gun_kill ~= 0 then tb.total_sniper_gun_kill = __total_sniper_gun_kill end
    local __max_sniper_gun_kill = decoder:getu32(439)
    if not PB_USE_DEFAULT_TABLE or __max_sniper_gun_kill ~= 0 then tb.max_sniper_gun_kill = __max_sniper_gun_kill end
    local __avg_sniper_gun_kill = decoder:getu32(440)
    if not PB_USE_DEFAULT_TABLE or __avg_sniper_gun_kill ~= 0 then tb.avg_sniper_gun_kill = __avg_sniper_gun_kill end
    local __total_shot_gun_kill = decoder:getu32(441)
    if not PB_USE_DEFAULT_TABLE or __total_shot_gun_kill ~= 0 then tb.total_shot_gun_kill = __total_shot_gun_kill end
    local __max_shot_gun_kill = decoder:getu32(442)
    if not PB_USE_DEFAULT_TABLE or __max_shot_gun_kill ~= 0 then tb.max_shot_gun_kill = __max_shot_gun_kill end
    local __avg_shot_gun_kill = decoder:getu32(443)
    if not PB_USE_DEFAULT_TABLE or __avg_shot_gun_kill ~= 0 then tb.avg_shot_gun_kill = __avg_shot_gun_kill end
    local __total_rifle_kill = decoder:getu32(444)
    if not PB_USE_DEFAULT_TABLE or __total_rifle_kill ~= 0 then tb.total_rifle_kill = __total_rifle_kill end
    local __max_rifle_kill = decoder:getu32(445)
    if not PB_USE_DEFAULT_TABLE or __max_rifle_kill ~= 0 then tb.max_rifle_kill = __max_rifle_kill end
    local __avg_rifle_kill = decoder:getu32(446)
    if not PB_USE_DEFAULT_TABLE or __avg_rifle_kill ~= 0 then tb.avg_rifle_kill = __avg_rifle_kill end
    local __total_machine_gun_kill = decoder:getu32(447)
    if not PB_USE_DEFAULT_TABLE or __total_machine_gun_kill ~= 0 then tb.total_machine_gun_kill = __total_machine_gun_kill end
    local __max_machine_gun_kill = decoder:getu32(448)
    if not PB_USE_DEFAULT_TABLE or __max_machine_gun_kill ~= 0 then tb.max_machine_gun_kill = __max_machine_gun_kill end
    local __avg_machine_gun_kill = decoder:getu32(449)
    if not PB_USE_DEFAULT_TABLE or __avg_machine_gun_kill ~= 0 then tb.avg_machine_gun_kill = __avg_machine_gun_kill end
    local __total_grenade_kill = decoder:getu32(450)
    if not PB_USE_DEFAULT_TABLE or __total_grenade_kill ~= 0 then tb.total_grenade_kill = __total_grenade_kill end
    local __max_grenade_kill = decoder:getu32(451)
    if not PB_USE_DEFAULT_TABLE or __max_grenade_kill ~= 0 then tb.max_grenade_kill = __max_grenade_kill end
    local __avg_grenade_kill = decoder:getu32(452)
    if not PB_USE_DEFAULT_TABLE or __avg_grenade_kill ~= 0 then tb.avg_grenade_kill = __avg_grenade_kill end
    local __total_carrier_kill = decoder:getu32(453)
    if not PB_USE_DEFAULT_TABLE or __total_carrier_kill ~= 0 then tb.total_carrier_kill = __total_carrier_kill end
    local __max_carrier_kill = decoder:getu32(454)
    if not PB_USE_DEFAULT_TABLE or __max_carrier_kill ~= 0 then tb.max_carrier_kill = __max_carrier_kill end
    local __avg_carrier_kill = decoder:getu32(455)
    if not PB_USE_DEFAULT_TABLE or __avg_carrier_kill ~= 0 then tb.avg_carrier_kill = __avg_carrier_kill end
    tb.summary = pb.pb_SummaryDecode(decoder:getsubmsg(500))
    tb.rank_summary = pb.pb_RankSummaryDecode(decoder:getsubmsg(600))
    tb.mp_rank_summary = pb.pb_MPRankSummaryDecode(decoder:getsubmsg(601))
    tb.achievements_counters = {}
    for k,v in pairs(decoder:getsubmsgary(610)) do
        tb.achievements_counters[k] = pb.pb_SingleMatchAchievementCounterDecode(v)
    end
    tb.mp_achievements_counters = {}
    for k,v in pairs(decoder:getsubmsgary(620)) do
        tb.mp_achievements_counters[k] = pb.pb_SingleMatchAchievementCounterDecode(v)
    end
    tb.commander_summary = pb.pb_CommanderSummaryDecode(decoder:getsubmsg(630))
    tb.commander_extra = pb.pb_CommanderExtraDecode(decoder:getsubmsg(631))
    return tb
end

function pb.pb_ProfileSeasonInfoEncode(tb, encoder)
    if(tb.level) then    encoder:addu32(1, tb.level)    end
    if(tb.exp) then    encoder:addi64(2, tb.exp)    end
    if(tb.next_level_exp) then    encoder:addi64(3, tb.next_level_exp)    end
    if(tb.serial) then    encoder:addu32(4, tb.serial)    end
    if(tb.total_exp) then    encoder:addi64(5, tb.total_exp)    end
    if(tb.last_update_time) then    encoder:addu32(6, tb.last_update_time)    end
    if(tb.boon_exp_remain) then    encoder:addi64(7, tb.boon_exp_remain)    end
    if(tb.day_exp) then    encoder:addi64(8, tb.day_exp)    end
    if(tb.bonus_percent) then    encoder:addfloat(9, tb.bonus_percent)    end
    if(tb.total_price) then    encoder:addi64(15, tb.total_price)    end
    if(tb.avg_survival_time) then    encoder:addu32(20, tb.avg_survival_time)    end
    if(tb.max_survival_time) then    encoder:addu32(21, tb.max_survival_time)    end
    if(tb.max_collection_price) then    encoder:addi64(25, tb.max_collection_price)    end
    if(tb.settled) then    encoder:addbool(26, tb.settled)    end
    if(tb.kd) then    encoder:addfloat(30, tb.kd)    end
    if(tb.damage) then    encoder:addfloat(31, tb.damage)    end
    if(tb.collection_price) then    encoder:addfloat(32, tb.collection_price)    end
    if(tb.support) then    encoder:addfloat(33, tb.support)    end
    if(tb.winning_percentage) then    encoder:addfloat(34, tb.winning_percentage)    end
    if(tb.total_game_time) then    encoder:addu64(100, tb.total_game_time)    end
    if(tb.total_fight) then    encoder:addu32(101, tb.total_fight)    end
    if(tb.total_escape) then    encoder:addu32(102, tb.total_escape)    end
    if(tb.max_escape_streak) then    encoder:addu32(103, tb.max_escape_streak)    end
    if(tb.cur_escape_streak) then    encoder:addu32(104, tb.cur_escape_streak)    end
    if(tb.total_fracture) then    encoder:addu32(105, tb.total_fracture)    end
    if(tb.total_bleed) then    encoder:addu32(106, tb.total_bleed)    end
    if(tb.total_pain) then    encoder:addu32(107, tb.total_pain)    end
    if(tb.total_food_consumed) then    encoder:addu32(108, tb.total_food_consumed)    end
    if(tb.total_drug_consumed) then    encoder:addu32(109, tb.total_drug_consumed)    end
    if(tb.total_healing) then    encoder:addu64(110, tb.total_healing)    end
    if(tb.max_healing) then    encoder:addu64(111, tb.max_healing)    end
    if(tb.avg_healing) then    encoder:addu64(112, tb.avg_healing)    end
    if(tb.total_resurrect) then    encoder:addu32(113, tb.total_resurrect)    end
    if(tb.max_resurrect) then    encoder:addu32(114, tb.max_resurrect)    end
    if(tb.avg_resurrect) then    encoder:addu32(115, tb.avg_resurrect)    end
    if(tb.total_death) then    encoder:addu32(116, tb.total_death)    end
    if(tb.total_missing) then    encoder:addu32(117, tb.total_missing)    end
    if(tb.total_move_distance) then    encoder:addu64(200, tb.total_move_distance)    end
    if(tb.max_move_distance) then    encoder:addu64(201, tb.max_move_distance)    end
    if(tb.avg_move_distance) then    encoder:addu64(202, tb.avg_move_distance)    end
    if(tb.max_jump) then    encoder:addu64(203, tb.max_jump)    end
    if(tb.total_walk_distance) then    encoder:addu64(204, tb.total_walk_distance)    end
    if(tb.max_walk_distance) then    encoder:addu64(205, tb.max_walk_distance)    end
    if(tb.avg_walk_distance) then    encoder:addu64(206, tb.avg_walk_distance)    end
    if(tb.total_loot_corpse) then    encoder:addu32(300, tb.total_loot_corpse)    end
    if(tb.max_loot_corpse) then    encoder:addu32(301, tb.max_loot_corpse)    end
    if(tb.avg_loot_corpse) then    encoder:addu32(302, tb.avg_loot_corpse)    end
    if(tb.total_strongbox_open) then    encoder:addu32(303, tb.total_strongbox_open)    end
    if(tb.max_strongbox_open) then    encoder:addu32(304, tb.max_strongbox_open)    end
    if(tb.avg_strongbox_open) then    encoder:addu32(305, tb.avg_strongbox_open)    end
    if(tb.total_unlock_door) then    encoder:addu32(306, tb.total_unlock_door)    end
    if(tb.max_unlock_door) then    encoder:addu32(307, tb.max_unlock_door)    end
    if(tb.avg_unlock_door) then    encoder:addu32(308, tb.avg_unlock_door)    end
    if(tb.total_game_egg) then    encoder:addu32(309, tb.total_game_egg)    end
    if(tb.max_game_egg) then    encoder:addu32(310, tb.max_game_egg)    end
    if(tb.avg_game_egg) then    encoder:addu32(311, tb.avg_game_egg)    end
    if(tb.total_loot_container) then    encoder:addu32(312, tb.total_loot_container)    end
    if(tb.max_loot_container) then    encoder:addu32(313, tb.max_loot_container)    end
    if(tb.avg_loot_container) then    encoder:addu32(314, tb.avg_loot_container)    end
    if(tb.total_kd) then    encoder:addfloat(400, tb.total_kd)    end
    if(tb.max_kd) then    encoder:addfloat(401, tb.max_kd)    end
    if(tb.avg_kd) then    encoder:addfloat(402, tb.avg_kd)    end
    if(tb.total_damage) then    encoder:addu64(403, tb.total_damage)    end
    if(tb.max_damage) then    encoder:addu64(404, tb.max_damage)    end
    if(tb.avg_damage) then    encoder:addu64(405, tb.avg_damage)    end
    if(tb.total_shoot_down) then    encoder:addu32(406, tb.total_shoot_down)    end
    if(tb.max_shoot_down) then    encoder:addu32(407, tb.max_shoot_down)    end
    if(tb.avg_shoot_down) then    encoder:addu32(408, tb.avg_shoot_down)    end
    if(tb.total_kill) then    encoder:addu32(409, tb.total_kill)    end
    if(tb.max_kill) then    encoder:addu32(410, tb.max_kill)    end
    if(tb.avg_kill) then    encoder:addu32(411, tb.avg_kill)    end
    if(tb.total_kill_alone) then    encoder:addu32(412, tb.total_kill_alone)    end
    if(tb.max_kill_alone) then    encoder:addu32(413, tb.max_kill_alone)    end
    if(tb.avg_kill_alone) then    encoder:addu32(414, tb.avg_kill_alone)    end
    if(tb.total_head_shot) then    encoder:addu32(415, tb.total_head_shot)    end
    if(tb.max_head_shot) then    encoder:addu32(416, tb.max_head_shot)    end
    if(tb.avg_head_shot) then    encoder:addu32(417, tb.avg_head_shot)    end
    if(tb.total_hit) then    encoder:addfloat(418, tb.total_hit)    end
    if(tb.max_hit) then    encoder:addfloat(419, tb.max_hit)    end
    if(tb.avg_float) then    encoder:addfloat(420, tb.avg_float)    end
    if(tb.max_hit_distance) then    encoder:addu64(421, tb.max_hit_distance)    end
    if(tb.max_kill_distance) then    encoder:addu64(422, tb.max_kill_distance)    end
    if(tb.total_kill_boss) then    encoder:addu32(423, tb.total_kill_boss)    end
    if(tb.max_kill_boss) then    encoder:addu32(424, tb.max_kill_boss)    end
    if(tb.avg_kill_boss) then    encoder:addu32(425, tb.avg_kill_boss)    end
    if(tb.total_kill_player) then    encoder:addu32(426, tb.total_kill_player)    end
    if(tb.max_kill_player) then    encoder:addu32(427, tb.max_kill_player)    end
    if(tb.avg_kill_player) then    encoder:addu32(428, tb.avg_kill_player)    end
    if(tb.total_kill_robot) then    encoder:addu32(429, tb.total_kill_robot)    end
    if(tb.max_kill_robot) then    encoder:addu32(430, tb.max_kill_robot)    end
    if(tb.avg_kill_robot) then    encoder:addu32(431, tb.avg_kill_robot)    end
    if(tb.total_pistol_kill) then    encoder:addu32(432, tb.total_pistol_kill)    end
    if(tb.max_pistol_kill) then    encoder:addu32(433, tb.max_pistol_kill)    end
    if(tb.avg_pistol_kill) then    encoder:addu32(434, tb.avg_pistol_kill)    end
    if(tb.total_melee_kill) then    encoder:addu32(435, tb.total_melee_kill)    end
    if(tb.max_melee_kill) then    encoder:addu32(436, tb.max_melee_kill)    end
    if(tb.avg_melee_kill) then    encoder:addu32(437, tb.avg_melee_kill)    end
    if(tb.total_sniper_gun_kill) then    encoder:addu32(438, tb.total_sniper_gun_kill)    end
    if(tb.max_sniper_gun_kill) then    encoder:addu32(439, tb.max_sniper_gun_kill)    end
    if(tb.avg_sniper_gun_kill) then    encoder:addu32(440, tb.avg_sniper_gun_kill)    end
    if(tb.total_shot_gun_kill) then    encoder:addu32(441, tb.total_shot_gun_kill)    end
    if(tb.max_shot_gun_kill) then    encoder:addu32(442, tb.max_shot_gun_kill)    end
    if(tb.avg_shot_gun_kill) then    encoder:addu32(443, tb.avg_shot_gun_kill)    end
    if(tb.total_rifle_kill) then    encoder:addu32(444, tb.total_rifle_kill)    end
    if(tb.max_rifle_kill) then    encoder:addu32(445, tb.max_rifle_kill)    end
    if(tb.avg_rifle_kill) then    encoder:addu32(446, tb.avg_rifle_kill)    end
    if(tb.total_machine_gun_kill) then    encoder:addu32(447, tb.total_machine_gun_kill)    end
    if(tb.max_machine_gun_kill) then    encoder:addu32(448, tb.max_machine_gun_kill)    end
    if(tb.avg_machine_gun_kill) then    encoder:addu32(449, tb.avg_machine_gun_kill)    end
    if(tb.total_grenade_kill) then    encoder:addu32(450, tb.total_grenade_kill)    end
    if(tb.max_grenade_kill) then    encoder:addu32(451, tb.max_grenade_kill)    end
    if(tb.avg_grenade_kill) then    encoder:addu32(452, tb.avg_grenade_kill)    end
    if(tb.total_carrier_kill) then    encoder:addu32(453, tb.total_carrier_kill)    end
    if(tb.max_carrier_kill) then    encoder:addu32(454, tb.max_carrier_kill)    end
    if(tb.avg_carrier_kill) then    encoder:addu32(455, tb.avg_carrier_kill)    end
    if(tb.summary) then    pb.pb_SummaryEncode(tb.summary, encoder:addsubmsg(500))    end
    if(tb.rank_summary) then    pb.pb_RankSummaryEncode(tb.rank_summary, encoder:addsubmsg(600))    end
    if(tb.mp_rank_summary) then    pb.pb_MPRankSummaryEncode(tb.mp_rank_summary, encoder:addsubmsg(601))    end
    if(tb.achievements_counters) then
        for i=1,#(tb.achievements_counters) do
            pb.pb_SingleMatchAchievementCounterEncode(tb.achievements_counters[i], encoder:addsubmsg(610))
        end
    end
    if(tb.mp_achievements_counters) then
        for i=1,#(tb.mp_achievements_counters) do
            pb.pb_SingleMatchAchievementCounterEncode(tb.mp_achievements_counters[i], encoder:addsubmsg(620))
        end
    end
    if(tb.commander_summary) then    pb.pb_CommanderSummaryEncode(tb.commander_summary, encoder:addsubmsg(630))    end
    if(tb.commander_extra) then    pb.pb_CommanderExtraEncode(tb.commander_extra, encoder:addsubmsg(631))    end
end

function pb.pb_CommanderExtraDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CommanderExtra) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_fight_as_commander = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_fight_as_commander ~= 0 then tb.total_fight_as_commander = __total_fight_as_commander end
    local __win_ratio = decoder:getfloat(3)
    if not PB_USE_DEFAULT_TABLE or __win_ratio ~= 0 then tb.win_ratio = __win_ratio end
    local __kill_by_vehicle_per_minute = decoder:getfloat(4)
    if not PB_USE_DEFAULT_TABLE or __kill_by_vehicle_per_minute ~= 0 then tb.kill_by_vehicle_per_minute = __kill_by_vehicle_per_minute end
    local __defeat_by_per_minute = decoder:getfloat(5)
    if not PB_USE_DEFAULT_TABLE or __defeat_by_per_minute ~= 0 then tb.defeat_by_per_minute = __defeat_by_per_minute end
    local __help_per_minute = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __help_per_minute ~= 0 then tb.help_per_minute = __help_per_minute end
    return tb
end

function pb.pb_CommanderExtraEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_fight_as_commander) then    encoder:addu32(2, tb.total_fight_as_commander)    end
    if(tb.win_ratio) then    encoder:addfloat(3, tb.win_ratio)    end
    if(tb.kill_by_vehicle_per_minute) then    encoder:addfloat(4, tb.kill_by_vehicle_per_minute)    end
    if(tb.defeat_by_per_minute) then    encoder:addfloat(5, tb.defeat_by_per_minute)    end
    if(tb.help_per_minute) then    encoder:addfloat(6, tb.help_per_minute)    end
end

function pb.pb_SingleMatchAchievementCounterDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SingleMatchAchievementCounter) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __times = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __times ~= 0 then tb.times = __times end
    return tb
end

function pb.pb_SingleMatchAchievementCounterEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.times) then    encoder:addu32(2, tb.times)    end
end

function pb.pb_MatchTitleCounterDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchTitleCounter) or {} 
    local __id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __times = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __times ~= 0 then tb.times = __times end
    return tb
end

function pb.pb_MatchTitleCounterEncode(tb, encoder)
    if(tb.id) then    encoder:addi32(1, tb.id)    end
    if(tb.times) then    encoder:addi32(2, tb.times)    end
end

function pb.pb_PlayerMatchRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerMatchRecord) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __nick = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __nick ~= "" then tb.nick = __nick end
    local __plat_id = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    local __game_time = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __game_time ~= 0 then tb.game_time = __game_time end
    local __game_result = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __game_result ~= 0 then tb.game_result = __game_result end
    local __kill = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __kill ~= 0 then tb.kill = __kill end
    local __damage = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __mvp = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __mvp ~= false then tb.mvp = __mvp end
    local __collection_price = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __collection_price ~= 0 then tb.collection_price = __collection_price end
    local __killPlayer = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __killPlayer ~= 0 then tb.killPlayer = __killPlayer end
    local __killAI = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __killAI ~= 0 then tb.killAI = __killAI end
    local __killBoss = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __killBoss ~= 0 then tb.killBoss = __killBoss end
    tb.sol_data = pb.pb_MatchBaseRecordIrisSOLDecode(decoder:getsubmsg(20))
    tb.raid_data = pb.pb_MatchBaseRecordIrisRaidDecode(decoder:getsubmsg(21))
    tb.mp_data = pb.pb_MatchBaseRecordMPDecode(decoder:getsubmsg(22))
    tb.arena_data = pb.pb_MatchBaseRecordArenaDecode(decoder:getsubmsg(23))
    local __team_id = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.achievements = {}
    for k,v in pairs(decoder:getsubmsgary(40)) do
        tb.achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    local __is_ai = decoder:getbool(50)
    if not PB_USE_DEFAULT_TABLE or __is_ai ~= false then tb.is_ai = __is_ai end
    local __display_ai = decoder:getbool(51)
    if not PB_USE_DEFAULT_TABLE or __display_ai ~= false then tb.display_ai = __display_ai end
    return tb
end

function pb.pb_PlayerMatchRecordEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.nick) then    encoder:addstr(2, tb.nick)    end
    if(tb.plat_id) then    encoder:addi32(9, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(10, tb.account_type)    end
    if(tb.game_time) then    encoder:addu32(4, tb.game_time)    end
    if(tb.game_result) then    encoder:addu32(7, tb.game_result)    end
    if(tb.kill) then    encoder:addu32(3, tb.kill)    end
    if(tb.damage) then    encoder:addu32(5, tb.damage)    end
    if(tb.mvp) then    encoder:addbool(6, tb.mvp)    end
    if(tb.collection_price) then    encoder:addu64(8, tb.collection_price)    end
    if(tb.killPlayer) then    encoder:addu32(15, tb.killPlayer)    end
    if(tb.killAI) then    encoder:addu32(16, tb.killAI)    end
    if(tb.killBoss) then    encoder:addu32(17, tb.killBoss)    end
    if(tb.sol_data) then    pb.pb_MatchBaseRecordIrisSOLEncode(tb.sol_data, encoder:addsubmsg(20))    end
    if(tb.raid_data) then    pb.pb_MatchBaseRecordIrisRaidEncode(tb.raid_data, encoder:addsubmsg(21))    end
    if(tb.mp_data) then    pb.pb_MatchBaseRecordMPEncode(tb.mp_data, encoder:addsubmsg(22))    end
    if(tb.arena_data) then    pb.pb_MatchBaseRecordArenaEncode(tb.arena_data, encoder:addsubmsg(23))    end
    if(tb.team_id) then    encoder:addu32(30, tb.team_id)    end
    if(tb.achievements) then
        for i=1,#(tb.achievements) do
            pb.pb_DsGameAchievementEncode(tb.achievements[i], encoder:addsubmsg(40))
        end
    end
    if(tb.is_ai) then    encoder:addbool(50, tb.is_ai)    end
    if(tb.display_ai) then    encoder:addbool(51, tb.display_ai)    end
end

function pb.pb_MatchBaseRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecord) or {} 
    local __key = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __key ~= "" then tb.key = __key end
    local __match_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __game_time = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __game_time ~= 0 then tb.game_time = __game_time end
    local __game_result = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __game_result ~= 0 then tb.game_result = __game_result end
    local __match_time = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __match_time ~= 0 then tb.match_time = __match_time end
    tb.match_mode = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(10))
    local __room_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    tb.achievements = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    tb.sol = pb.pb_MatchBaseRecordIrisSOLDecode(decoder:getsubmsg(20))
    tb.raid = pb.pb_MatchBaseRecordIrisRaidDecode(decoder:getsubmsg(21))
    tb.mp = pb.pb_MatchBaseRecordMPDecode(decoder:getsubmsg(22))
    tb.arena = pb.pb_MatchBaseRecordArenaDecode(decoder:getsubmsg(23))
    tb.ai_data = {}
    for k,v in pairs(decoder:getsubmsgary(24)) do
        tb.ai_data[k] = pb.pb_AIPlayerDataDecode(v)
    end
    local __hero_id = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __match_start_time = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __match_start_time ~= 0 then tb.match_start_time = __match_start_time end
    local __military_rank = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __military_rank ~= 0 then tb.military_rank = __military_rank end
    local __armedforce_id = decoder:getu64(33)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    local __is_ranked_match = decoder:getbool(40)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __ranked_score_delta = decoder:geti64(41)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_delta ~= 0 then tb.ranked_score_delta = __ranked_score_delta end
    local __ranked_score = decoder:geti64(42)
    if not PB_USE_DEFAULT_TABLE or __ranked_score ~= 0 then tb.ranked_score = __ranked_score end
    local __ranked_level = decoder:geti32(43)
    if not PB_USE_DEFAULT_TABLE or __ranked_level ~= 0 then tb.ranked_level = __ranked_level end
    local __ranked_level_old = decoder:geti32(44)
    if not PB_USE_DEFAULT_TABLE or __ranked_level_old ~= 0 then tb.ranked_level_old = __ranked_level_old end
    local __ranked_score_season_no = decoder:getu32(45)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_season_no ~= 0 then tb.ranked_score_season_no = __ranked_score_season_no end
    local __ranked_coins = decoder:geti32(50)
    if not PB_USE_DEFAULT_TABLE or __ranked_coins ~= 0 then tb.ranked_coins = __ranked_coins end
    local __ranked_score_shoot = decoder:geti64(100)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_shoot ~= 0 then tb.ranked_score_shoot = __ranked_score_shoot end
    local __ranked_score_tactics = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_tactics ~= 0 then tb.ranked_score_tactics = __ranked_score_tactics end
    local __ranked_score_vehicle = decoder:geti64(102)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_vehicle ~= 0 then tb.ranked_score_vehicle = __ranked_score_vehicle end
    local __ranked_score_shoot_delta = decoder:geti64(103)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_shoot_delta ~= 0 then tb.ranked_score_shoot_delta = __ranked_score_shoot_delta end
    local __ranked_score_tactics_delta = decoder:geti64(104)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_tactics_delta ~= 0 then tb.ranked_score_tactics_delta = __ranked_score_tactics_delta end
    local __ranked_score_vehicle_delta = decoder:geti64(105)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_vehicle_delta ~= 0 then tb.ranked_score_vehicle_delta = __ranked_score_vehicle_delta end
    tb.tournament_data = pb.pb_MatchBaseRecordMPTournamentDecode(decoder:getsubmsg(110))
    local __client_group = decoder:geti32(111)
    if not PB_USE_DEFAULT_TABLE or __client_group ~= 0 then tb.client_group = __client_group end
    local __is_victory_unite_match = decoder:getbool(120)
    if not PB_USE_DEFAULT_TABLE or __is_victory_unite_match ~= false then tb.is_victory_unite_match = __is_victory_unite_match end
    return tb
end

function pb.pb_MatchBaseRecordEncode(tb, encoder)
    if(tb.key) then    encoder:addstr(1, tb.key)    end
    if(tb.match_type) then    encoder:addu32(2, tb.match_type)    end
    if(tb.game_time) then    encoder:addu32(4, tb.game_time)    end
    if(tb.game_result) then    encoder:addu32(5, tb.game_result)    end
    if(tb.match_time) then    encoder:addu32(9, tb.match_time)    end
    if(tb.match_mode) then    pb.pb_MatchModeInfoEncode(tb.match_mode, encoder:addsubmsg(10))    end
    if(tb.room_id) then    encoder:addu64(11, tb.room_id)    end
    if(tb.achievements) then
        for i=1,#(tb.achievements) do
            pb.pb_DsGameAchievementEncode(tb.achievements[i], encoder:addsubmsg(12))
        end
    end
    if(tb.sol) then    pb.pb_MatchBaseRecordIrisSOLEncode(tb.sol, encoder:addsubmsg(20))    end
    if(tb.raid) then    pb.pb_MatchBaseRecordIrisRaidEncode(tb.raid, encoder:addsubmsg(21))    end
    if(tb.mp) then    pb.pb_MatchBaseRecordMPEncode(tb.mp, encoder:addsubmsg(22))    end
    if(tb.arena) then    pb.pb_MatchBaseRecordArenaEncode(tb.arena, encoder:addsubmsg(23))    end
    if(tb.ai_data) then
        for i=1,#(tb.ai_data) do
            pb.pb_AIPlayerDataEncode(tb.ai_data[i], encoder:addsubmsg(24))
        end
    end
    if(tb.hero_id) then    encoder:addu64(30, tb.hero_id)    end
    if(tb.match_start_time) then    encoder:addi64(31, tb.match_start_time)    end
    if(tb.military_rank) then    encoder:addi32(32, tb.military_rank)    end
    if(tb.armedforce_id) then    encoder:addu64(33, tb.armedforce_id)    end
    if(tb.is_ranked_match) then    encoder:addbool(40, tb.is_ranked_match)    end
    if(tb.ranked_score_delta) then    encoder:addi64(41, tb.ranked_score_delta)    end
    if(tb.ranked_score) then    encoder:addi64(42, tb.ranked_score)    end
    if(tb.ranked_level) then    encoder:addi32(43, tb.ranked_level)    end
    if(tb.ranked_level_old) then    encoder:addi32(44, tb.ranked_level_old)    end
    if(tb.ranked_score_season_no) then    encoder:addu32(45, tb.ranked_score_season_no)    end
    if(tb.ranked_coins) then    encoder:addi32(50, tb.ranked_coins)    end
    if(tb.ranked_score_shoot) then    encoder:addi64(100, tb.ranked_score_shoot)    end
    if(tb.ranked_score_tactics) then    encoder:addi64(101, tb.ranked_score_tactics)    end
    if(tb.ranked_score_vehicle) then    encoder:addi64(102, tb.ranked_score_vehicle)    end
    if(tb.ranked_score_shoot_delta) then    encoder:addi64(103, tb.ranked_score_shoot_delta)    end
    if(tb.ranked_score_tactics_delta) then    encoder:addi64(104, tb.ranked_score_tactics_delta)    end
    if(tb.ranked_score_vehicle_delta) then    encoder:addi64(105, tb.ranked_score_vehicle_delta)    end
    if(tb.tournament_data) then    pb.pb_MatchBaseRecordMPTournamentEncode(tb.tournament_data, encoder:addsubmsg(110))    end
    if(tb.client_group) then    encoder:addi32(111, tb.client_group)    end
    if(tb.is_victory_unite_match) then    encoder:addbool(120, tb.is_victory_unite_match)    end
end

function pb.pb_SummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_Summary) or {} 
    local __total_collection_price = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __total_collection_price ~= 0 then tb.total_collection_price = __total_collection_price end
    local __total_damage = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __total_rescue = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_rescue ~= 0 then tb.total_rescue = __total_rescue end
    local __total_healing = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_healing ~= 0 then tb.total_healing = __total_healing end
    local __total_kill_player = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_kill_player ~= 0 then tb.total_kill_player = __total_kill_player end
    local __total_kill_ai = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __total_kill_ai ~= 0 then tb.total_kill_ai = __total_kill_ai end
    local __total_kill_boss = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __total_kill_boss ~= 0 then tb.total_kill_boss = __total_kill_boss end
    local __total_teammate_price = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __total_teammate_price ~= 0 then tb.total_teammate_price = __total_teammate_price end
    local __total_leave = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __total_leave ~= 0 then tb.total_leave = __total_leave end
    local __sol_total_game_cnt = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __sol_total_game_cnt ~= 0 then tb.sol_total_game_cnt = __sol_total_game_cnt end
    local __sol_total_battle = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __sol_total_battle ~= 0 then tb.sol_total_battle = __sol_total_battle end
    local __sol_total_sneak = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __sol_total_sneak ~= 0 then tb.sol_total_sneak = __sol_total_sneak end
    return tb
end

function pb.pb_SummaryEncode(tb, encoder)
    if(tb.total_collection_price) then    encoder:addu64(1, tb.total_collection_price)    end
    if(tb.total_damage) then    encoder:addu64(2, tb.total_damage)    end
    if(tb.total_rescue) then    encoder:addu32(3, tb.total_rescue)    end
    if(tb.total_healing) then    encoder:addu32(4, tb.total_healing)    end
    if(tb.total_kill_player) then    encoder:addu32(5, tb.total_kill_player)    end
    if(tb.total_kill_ai) then    encoder:addu32(6, tb.total_kill_ai)    end
    if(tb.total_kill_boss) then    encoder:addu32(7, tb.total_kill_boss)    end
    if(tb.total_teammate_price) then    encoder:addu64(8, tb.total_teammate_price)    end
    if(tb.total_leave) then    encoder:addu32(9, tb.total_leave)    end
    if(tb.sol_total_game_cnt) then    encoder:addu32(10, tb.sol_total_game_cnt)    end
    if(tb.sol_total_battle) then    encoder:addu32(11, tb.sol_total_battle)    end
    if(tb.sol_total_sneak) then    encoder:addu32(12, tb.sol_total_sneak)    end
end

function pb.pb_RankEventDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankEvent) or {} 
    local __timestamp = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    local __event_type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __event_type ~= 0 then tb.event_type = __event_type end
    local __event_value = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __event_value ~= 0 then tb.event_value = __event_value end
    local __event_value_extra1 = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __event_value_extra1 ~= 0 then tb.event_value_extra1 = __event_value_extra1 end
    local __event_value_extra2 = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __event_value_extra2 ~= 0 then tb.event_value_extra2 = __event_value_extra2 end
    local __event_value_extra3 = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __event_value_extra3 ~= 0 then tb.event_value_extra3 = __event_value_extra3 end
    return tb
end

function pb.pb_RankEventEncode(tb, encoder)
    if(tb.timestamp) then    encoder:addi64(1, tb.timestamp)    end
    if(tb.event_type) then    encoder:addu32(2, tb.event_type)    end
    if(tb.event_value) then    encoder:addi32(3, tb.event_value)    end
    if(tb.event_value_extra1) then    encoder:addi32(4, tb.event_value_extra1)    end
    if(tb.event_value_extra2) then    encoder:addi32(5, tb.event_value_extra2)    end
    if(tb.event_value_extra3) then    encoder:addi32(6, tb.event_value_extra3)    end
end

function pb.pb_RankHeroSummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankHeroSummary) or {} 
    local __heroid = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __heroid ~= 0 then tb.heroid = __heroid end
    local __used_cnt = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __used_cnt ~= 0 then tb.used_cnt = __used_cnt end
    return tb
end

function pb.pb_RankHeroSummaryEncode(tb, encoder)
    if(tb.heroid) then    encoder:addu64(1, tb.heroid)    end
    if(tb.used_cnt) then    encoder:addu32(2, tb.used_cnt)    end
end

function pb.pb_RankMajorSummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankMajorSummary) or {} 
    local __majorid = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __majorid ~= 0 then tb.majorid = __majorid end
    local __total_game_cnt = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_game_cnt ~= 0 then tb.total_game_cnt = __total_game_cnt end
    local __total_game_escaped = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_game_escaped ~= 0 then tb.total_game_escaped = __total_game_escaped end
    local __total_collection_price = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_collection_price ~= 0 then tb.total_collection_price = __total_collection_price end
    local __total_kill = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_death = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __total_death ~= 0 then tb.total_death = __total_death end
    tb.heros_used = {}
    for k,v in pairs(decoder:getsubmsgary(20)) do
        tb.heros_used[k] = pb.pb_RankHeroSummaryDecode(v)
    end
    return tb
end

function pb.pb_RankMajorSummaryEncode(tb, encoder)
    if(tb.majorid) then    encoder:addu32(1, tb.majorid)    end
    if(tb.total_game_cnt) then    encoder:addu32(2, tb.total_game_cnt)    end
    if(tb.total_game_escaped) then    encoder:addu32(3, tb.total_game_escaped)    end
    if(tb.total_collection_price) then    encoder:addu32(4, tb.total_collection_price)    end
    if(tb.total_kill) then    encoder:addu32(5, tb.total_kill)    end
    if(tb.total_death) then    encoder:addu32(6, tb.total_death)    end
    if(tb.heros_used) then
        for i=1,#(tb.heros_used) do
            pb.pb_RankHeroSummaryEncode(tb.heros_used[i], encoder:addsubmsg(20))
        end
    end
end

function pb.pb_RankSummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankSummary) or {} 
    local __score = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __score_max = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score_max ~= 0 then tb.score_max = __score_max end
    local __score_old = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __score_old ~= 0 then tb.score_old = __score_old end
    local __score_max_old = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __score_max_old ~= 0 then tb.score_max_old = __score_max_old end
    local __score_delta_by_mobile = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __score_delta_by_mobile ~= 0 then tb.score_delta_by_mobile = __score_delta_by_mobile end
    local __score_delta_by_pc = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __score_delta_by_pc ~= 0 then tb.score_delta_by_pc = __score_delta_by_pc end
    tb.levels_rewarded = decoder:getu32ary(10)
    local __level = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __level_max = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __level_max ~= 0 then tb.level_max = __level_max end
    local __major_level = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __major_level ~= 0 then tb.major_level = __major_level end
    local __major_level_max = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __major_level_max ~= 0 then tb.major_level_max = __major_level_max end
    local __level_score = decoder:getu32(24)
    if not PB_USE_DEFAULT_TABLE or __level_score ~= 0 then tb.level_score = __level_score end
    local __levelup_need_score = decoder:getu32(25)
    if not PB_USE_DEFAULT_TABLE or __levelup_need_score ~= 0 then tb.levelup_need_score = __levelup_need_score end
    tb.rank_events = {}
    for k,v in pairs(decoder:getsubmsgary(30)) do
        tb.rank_events[k] = pb.pb_RankEventDecode(v)
    end
    local __win_streak = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __win_streak ~= 0 then tb.win_streak = __win_streak end
    local __lose_streak = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __lose_streak ~= 0 then tb.lose_streak = __lose_streak end
    local __total_play = decoder:getu32(33)
    if not PB_USE_DEFAULT_TABLE or __total_play ~= 0 then tb.total_play = __total_play end
    local __score_max_season_no = decoder:getu32(34)
    if not PB_USE_DEFAULT_TABLE or __score_max_season_no ~= 0 then tb.score_max_season_no = __score_max_season_no end
    tb.major_summaries = {}
    for k,v in pairs(decoder:getsubmsgary(40)) do
        tb.major_summaries[k] = pb.pb_RankMajorSummaryDecode(v)
    end
    local __has_attended = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __has_attended ~= false then tb.has_attended = __has_attended end
    return tb
end

function pb.pb_RankSummaryEncode(tb, encoder)
    if(tb.score) then    encoder:addi64(1, tb.score)    end
    if(tb.score_max) then    encoder:addi64(2, tb.score_max)    end
    if(tb.score_old) then    encoder:addi64(3, tb.score_old)    end
    if(tb.score_max_old) then    encoder:addi64(4, tb.score_max_old)    end
    if(tb.score_delta_by_mobile) then    encoder:addi64(5, tb.score_delta_by_mobile)    end
    if(tb.score_delta_by_pc) then    encoder:addi64(6, tb.score_delta_by_pc)    end
    if(tb.levels_rewarded) then    encoder:addu32(10, tb.levels_rewarded)    end
    if(tb.level) then    encoder:addu32(20, tb.level)    end
    if(tb.level_max) then    encoder:addu32(21, tb.level_max)    end
    if(tb.major_level) then    encoder:addu32(22, tb.major_level)    end
    if(tb.major_level_max) then    encoder:addu32(23, tb.major_level_max)    end
    if(tb.level_score) then    encoder:addu32(24, tb.level_score)    end
    if(tb.levelup_need_score) then    encoder:addu32(25, tb.levelup_need_score)    end
    if(tb.rank_events) then
        for i=1,#(tb.rank_events) do
            pb.pb_RankEventEncode(tb.rank_events[i], encoder:addsubmsg(30))
        end
    end
    if(tb.win_streak) then    encoder:addu32(31, tb.win_streak)    end
    if(tb.lose_streak) then    encoder:addu32(32, tb.lose_streak)    end
    if(tb.total_play) then    encoder:addu32(33, tb.total_play)    end
    if(tb.score_max_season_no) then    encoder:addu32(34, tb.score_max_season_no)    end
    if(tb.major_summaries) then
        for i=1,#(tb.major_summaries) do
            pb.pb_RankMajorSummaryEncode(tb.major_summaries[i], encoder:addsubmsg(40))
        end
    end
    if(tb.has_attended) then    encoder:addbool(100, tb.has_attended)    end
end

function pb.pb_CommanderSummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CommanderSummary) or {} 
    tb.info = pb.pb_MPSeasonInfoDecode(decoder:getsubmsg(200))
    local __score = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __score_max = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score_max ~= 0 then tb.score_max = __score_max end
    local __score_delta_by_mobile = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __score_delta_by_mobile ~= 0 then tb.score_delta_by_mobile = __score_delta_by_mobile end
    local __score_delta_by_pc = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __score_delta_by_pc ~= 0 then tb.score_delta_by_pc = __score_delta_by_pc end
    tb.levels_rewarded = decoder:getu32ary(10)
    local __old_score = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __old_score ~= 0 then tb.old_score = __old_score end
    local __old_score_max = decoder:geti64(21)
    if not PB_USE_DEFAULT_TABLE or __old_score_max ~= 0 then tb.old_score_max = __old_score_max end
    local __level = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __level_max = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __level_max ~= 0 then tb.level_max = __level_max end
    local __major_level = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __major_level ~= 0 then tb.major_level = __major_level end
    local __major_level_max = decoder:getu32(33)
    if not PB_USE_DEFAULT_TABLE or __major_level_max ~= 0 then tb.major_level_max = __major_level_max end
    local __level_score = decoder:getu32(34)
    if not PB_USE_DEFAULT_TABLE or __level_score ~= 0 then tb.level_score = __level_score end
    local __levelup_need_score = decoder:getu32(35)
    if not PB_USE_DEFAULT_TABLE or __levelup_need_score ~= 0 then tb.levelup_need_score = __levelup_need_score end
    tb.rank_events = {}
    for k,v in pairs(decoder:getsubmsgary(40)) do
        tb.rank_events[k] = pb.pb_RankEventDecode(v)
    end
    local __win_streak = decoder:getu32(41)
    if not PB_USE_DEFAULT_TABLE or __win_streak ~= 0 then tb.win_streak = __win_streak end
    local __lose_streak = decoder:getu32(42)
    if not PB_USE_DEFAULT_TABLE or __lose_streak ~= 0 then tb.lose_streak = __lose_streak end
    local __total_play = decoder:getu32(43)
    if not PB_USE_DEFAULT_TABLE or __total_play ~= 0 then tb.total_play = __total_play end
    local __score_max_season_no = decoder:getu32(44)
    if not PB_USE_DEFAULT_TABLE or __score_max_season_no ~= 0 then tb.score_max_season_no = __score_max_season_no end
    tb.major_summaries = {}
    for k,v in pairs(decoder:getsubmsgary(50)) do
        tb.major_summaries[k] = pb.pb_MPRankMajorSummaryDecode(v)
    end
    local __has_attended = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __has_attended ~= false then tb.has_attended = __has_attended end
    local __total_win = decoder:getu32(60)
    if not PB_USE_DEFAULT_TABLE or __total_win ~= 0 then tb.total_win = __total_win end
    local __total_kill = decoder:getu32(61)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(62)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __total_game_time = decoder:getu64(63)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __acked_times_as_commander = decoder:getu32(70)
    if not PB_USE_DEFAULT_TABLE or __acked_times_as_commander ~= 0 then tb.acked_times_as_commander = __acked_times_as_commander end
    local __acked_times_as_soldier = decoder:getu32(71)
    if not PB_USE_DEFAULT_TABLE or __acked_times_as_soldier ~= 0 then tb.acked_times_as_soldier = __acked_times_as_soldier end
    tb.last_days = {}
    for k,v in pairs(decoder:getsubmsgary(80)) do
        tb.last_days[k] = pb.pb_MPSeasonInfoDecode(v)
    end
    tb.titles = {}
    for k,v in pairs(decoder:getsubmsgary(90)) do
        tb.titles[k] = pb.pb_MatchTitleCounterDecode(v)
    end
    return tb
end

function pb.pb_CommanderSummaryEncode(tb, encoder)
    if(tb.info) then    pb.pb_MPSeasonInfoEncode(tb.info, encoder:addsubmsg(200))    end
    if(tb.score) then    encoder:addi64(1, tb.score)    end
    if(tb.score_max) then    encoder:addi64(2, tb.score_max)    end
    if(tb.score_delta_by_mobile) then    encoder:addi64(6, tb.score_delta_by_mobile)    end
    if(tb.score_delta_by_pc) then    encoder:addi64(7, tb.score_delta_by_pc)    end
    if(tb.levels_rewarded) then    encoder:addu32(10, tb.levels_rewarded)    end
    if(tb.old_score) then    encoder:addi64(20, tb.old_score)    end
    if(tb.old_score_max) then    encoder:addi64(21, tb.old_score_max)    end
    if(tb.level) then    encoder:addu32(30, tb.level)    end
    if(tb.level_max) then    encoder:addu32(31, tb.level_max)    end
    if(tb.major_level) then    encoder:addu32(32, tb.major_level)    end
    if(tb.major_level_max) then    encoder:addu32(33, tb.major_level_max)    end
    if(tb.level_score) then    encoder:addu32(34, tb.level_score)    end
    if(tb.levelup_need_score) then    encoder:addu32(35, tb.levelup_need_score)    end
    if(tb.rank_events) then
        for i=1,#(tb.rank_events) do
            pb.pb_RankEventEncode(tb.rank_events[i], encoder:addsubmsg(40))
        end
    end
    if(tb.win_streak) then    encoder:addu32(41, tb.win_streak)    end
    if(tb.lose_streak) then    encoder:addu32(42, tb.lose_streak)    end
    if(tb.total_play) then    encoder:addu32(43, tb.total_play)    end
    if(tb.score_max_season_no) then    encoder:addu32(44, tb.score_max_season_no)    end
    if(tb.major_summaries) then
        for i=1,#(tb.major_summaries) do
            pb.pb_MPRankMajorSummaryEncode(tb.major_summaries[i], encoder:addsubmsg(50))
        end
    end
    if(tb.has_attended) then    encoder:addbool(100, tb.has_attended)    end
    if(tb.total_win) then    encoder:addu32(60, tb.total_win)    end
    if(tb.total_kill) then    encoder:addu32(61, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(62, tb.total_killed)    end
    if(tb.total_game_time) then    encoder:addu64(63, tb.total_game_time)    end
    if(tb.acked_times_as_commander) then    encoder:addu32(70, tb.acked_times_as_commander)    end
    if(tb.acked_times_as_soldier) then    encoder:addu32(71, tb.acked_times_as_soldier)    end
    if(tb.last_days) then
        for i=1,#(tb.last_days) do
            pb.pb_MPSeasonInfoEncode(tb.last_days[i], encoder:addsubmsg(80))
        end
    end
    if(tb.titles) then
        for i=1,#(tb.titles) do
            pb.pb_MatchTitleCounterEncode(tb.titles[i], encoder:addsubmsg(90))
        end
    end
end

function pb.pb_RankMatchScoreChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankMatchScoreChange) or {} 
    local __reason = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __limit = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __limit ~= 0 then tb.limit = __limit end
    return tb
end

function pb.pb_RankMatchScoreChangeEncode(tb, encoder)
    if(tb.reason) then    encoder:addu32(1, tb.reason)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
    if(tb.limit) then    encoder:addi64(3, tb.limit)    end
end

function pb.pb_MPRankMajorSummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPRankMajorSummary) or {} 
    local __majorid = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __majorid ~= 0 then tb.majorid = __majorid end
    local __total_game_cnt = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_game_cnt ~= 0 then tb.total_game_cnt = __total_game_cnt end
    local __total_kill = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_death = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_death ~= 0 then tb.total_death = __total_death end
    tb.heros_used = {}
    for k,v in pairs(decoder:getsubmsgary(20)) do
        tb.heros_used[k] = pb.pb_RankHeroSummaryDecode(v)
    end
    return tb
end

function pb.pb_MPRankMajorSummaryEncode(tb, encoder)
    if(tb.majorid) then    encoder:addu32(1, tb.majorid)    end
    if(tb.total_game_cnt) then    encoder:addu32(2, tb.total_game_cnt)    end
    if(tb.total_kill) then    encoder:addu32(3, tb.total_kill)    end
    if(tb.total_death) then    encoder:addu32(4, tb.total_death)    end
    if(tb.heros_used) then
        for i=1,#(tb.heros_used) do
            pb.pb_RankHeroSummaryEncode(tb.heros_used[i], encoder:addsubmsg(20))
        end
    end
end

function pb.pb_MPRankSummaryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPRankSummary) or {} 
    local __score = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __score_max = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score_max ~= 0 then tb.score_max = __score_max end
    local __score_shoot = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __score_shoot ~= 0 then tb.score_shoot = __score_shoot end
    local __score_tactics = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __score_tactics ~= 0 then tb.score_tactics = __score_tactics end
    local __score_vehicle = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __score_vehicle ~= 0 then tb.score_vehicle = __score_vehicle end
    local __score_delta_by_mobile = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __score_delta_by_mobile ~= 0 then tb.score_delta_by_mobile = __score_delta_by_mobile end
    local __score_delta_by_pc = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __score_delta_by_pc ~= 0 then tb.score_delta_by_pc = __score_delta_by_pc end
    tb.levels_rewarded = decoder:getu32ary(10)
    local __old_score = decoder:geti64(20)
    if not PB_USE_DEFAULT_TABLE or __old_score ~= 0 then tb.old_score = __old_score end
    local __old_score_max = decoder:geti64(21)
    if not PB_USE_DEFAULT_TABLE or __old_score_max ~= 0 then tb.old_score_max = __old_score_max end
    local __old_score_shoot = decoder:geti64(22)
    if not PB_USE_DEFAULT_TABLE or __old_score_shoot ~= 0 then tb.old_score_shoot = __old_score_shoot end
    local __old_score_tactics = decoder:geti64(23)
    if not PB_USE_DEFAULT_TABLE or __old_score_tactics ~= 0 then tb.old_score_tactics = __old_score_tactics end
    local __old_score_vehicle = decoder:geti64(24)
    if not PB_USE_DEFAULT_TABLE or __old_score_vehicle ~= 0 then tb.old_score_vehicle = __old_score_vehicle end
    local __level = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __level_max = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __level_max ~= 0 then tb.level_max = __level_max end
    local __major_level = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __major_level ~= 0 then tb.major_level = __major_level end
    local __major_level_max = decoder:getu32(33)
    if not PB_USE_DEFAULT_TABLE or __major_level_max ~= 0 then tb.major_level_max = __major_level_max end
    local __level_score = decoder:getu32(34)
    if not PB_USE_DEFAULT_TABLE or __level_score ~= 0 then tb.level_score = __level_score end
    local __levelup_need_score = decoder:getu32(35)
    if not PB_USE_DEFAULT_TABLE or __levelup_need_score ~= 0 then tb.levelup_need_score = __levelup_need_score end
    tb.rank_events = {}
    for k,v in pairs(decoder:getsubmsgary(40)) do
        tb.rank_events[k] = pb.pb_RankEventDecode(v)
    end
    local __win_streak = decoder:getu32(41)
    if not PB_USE_DEFAULT_TABLE or __win_streak ~= 0 then tb.win_streak = __win_streak end
    local __lose_streak = decoder:getu32(42)
    if not PB_USE_DEFAULT_TABLE or __lose_streak ~= 0 then tb.lose_streak = __lose_streak end
    local __total_play = decoder:getu32(43)
    if not PB_USE_DEFAULT_TABLE or __total_play ~= 0 then tb.total_play = __total_play end
    local __score_max_season_no = decoder:getu32(44)
    if not PB_USE_DEFAULT_TABLE or __score_max_season_no ~= 0 then tb.score_max_season_no = __score_max_season_no end
    tb.major_summaries = {}
    for k,v in pairs(decoder:getsubmsgary(50)) do
        tb.major_summaries[k] = pb.pb_MPRankMajorSummaryDecode(v)
    end
    local __has_attended = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __has_attended ~= false then tb.has_attended = __has_attended end
    local __total_win = decoder:getu32(60)
    if not PB_USE_DEFAULT_TABLE or __total_win ~= 0 then tb.total_win = __total_win end
    local __total_kill = decoder:getu32(61)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(62)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __total_game_time = decoder:getu64(63)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    return tb
end

function pb.pb_MPRankSummaryEncode(tb, encoder)
    if(tb.score) then    encoder:addi64(1, tb.score)    end
    if(tb.score_max) then    encoder:addi64(2, tb.score_max)    end
    if(tb.score_shoot) then    encoder:addi64(3, tb.score_shoot)    end
    if(tb.score_tactics) then    encoder:addi64(4, tb.score_tactics)    end
    if(tb.score_vehicle) then    encoder:addi64(5, tb.score_vehicle)    end
    if(tb.score_delta_by_mobile) then    encoder:addi64(6, tb.score_delta_by_mobile)    end
    if(tb.score_delta_by_pc) then    encoder:addi64(7, tb.score_delta_by_pc)    end
    if(tb.levels_rewarded) then    encoder:addu32(10, tb.levels_rewarded)    end
    if(tb.old_score) then    encoder:addi64(20, tb.old_score)    end
    if(tb.old_score_max) then    encoder:addi64(21, tb.old_score_max)    end
    if(tb.old_score_shoot) then    encoder:addi64(22, tb.old_score_shoot)    end
    if(tb.old_score_tactics) then    encoder:addi64(23, tb.old_score_tactics)    end
    if(tb.old_score_vehicle) then    encoder:addi64(24, tb.old_score_vehicle)    end
    if(tb.level) then    encoder:addu32(30, tb.level)    end
    if(tb.level_max) then    encoder:addu32(31, tb.level_max)    end
    if(tb.major_level) then    encoder:addu32(32, tb.major_level)    end
    if(tb.major_level_max) then    encoder:addu32(33, tb.major_level_max)    end
    if(tb.level_score) then    encoder:addu32(34, tb.level_score)    end
    if(tb.levelup_need_score) then    encoder:addu32(35, tb.levelup_need_score)    end
    if(tb.rank_events) then
        for i=1,#(tb.rank_events) do
            pb.pb_RankEventEncode(tb.rank_events[i], encoder:addsubmsg(40))
        end
    end
    if(tb.win_streak) then    encoder:addu32(41, tb.win_streak)    end
    if(tb.lose_streak) then    encoder:addu32(42, tb.lose_streak)    end
    if(tb.total_play) then    encoder:addu32(43, tb.total_play)    end
    if(tb.score_max_season_no) then    encoder:addu32(44, tb.score_max_season_no)    end
    if(tb.major_summaries) then
        for i=1,#(tb.major_summaries) do
            pb.pb_MPRankMajorSummaryEncode(tb.major_summaries[i], encoder:addsubmsg(50))
        end
    end
    if(tb.has_attended) then    encoder:addbool(100, tb.has_attended)    end
    if(tb.total_win) then    encoder:addu32(60, tb.total_win)    end
    if(tb.total_kill) then    encoder:addu32(61, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(62, tb.total_killed)    end
    if(tb.total_game_time) then    encoder:addu64(63, tb.total_game_time)    end
end

function pb.pb_AIPlayerDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AIPlayerData) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.player_data = pb.pb_SpecificModeDataDecode(decoder:getsubmsg(2))
    tb.game_achievements = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.game_achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    local __plat_id = decoder:geti32(21)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    return tb
end

function pb.pb_AIPlayerDataEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.player_data) then    pb.pb_SpecificModeDataEncode(tb.player_data, encoder:addsubmsg(2))    end
    if(tb.game_achievements) then
        for i=1,#(tb.game_achievements) do
            pb.pb_DsGameAchievementEncode(tb.game_achievements[i], encoder:addsubmsg(3))
        end
    end
    if(tb.plat_id) then    encoder:addi32(21, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(22, tb.account_type)    end
end

function pb.pb_SpecificModeDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SpecificModeData) or {} 
    tb.sol = pb.pb_MatchBaseRecordIrisSOLDecode(decoder:getsubmsg(20))
    tb.raid = pb.pb_MatchBaseRecordIrisRaidDecode(decoder:getsubmsg(21))
    tb.mp = pb.pb_MatchBaseRecordMPDecode(decoder:getsubmsg(22))
    tb.arena = pb.pb_MatchBaseRecordArenaDecode(decoder:getsubmsg(23))
    return tb
end

function pb.pb_SpecificModeDataEncode(tb, encoder)
    if(tb.sol) then    pb.pb_MatchBaseRecordIrisSOLEncode(tb.sol, encoder:addsubmsg(20))    end
    if(tb.raid) then    pb.pb_MatchBaseRecordIrisRaidEncode(tb.raid, encoder:addsubmsg(21))    end
    if(tb.mp) then    pb.pb_MatchBaseRecordMPEncode(tb.mp, encoder:addsubmsg(22))    end
    if(tb.arena) then    pb.pb_MatchBaseRecordArenaEncode(tb.arena, encoder:addsubmsg(23))    end
end

function pb.pb_MatchBaseRecordIrisSOLDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordIrisSOL) or {} 
    local __collection_price = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __collection_price ~= 0 then tb.collection_price = __collection_price end
    local __damage = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __rescue = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __rescue ~= 0 then tb.rescue = __rescue end
    local __healing = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __healing ~= 0 then tb.healing = __healing end
    local __killPlayer = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __killPlayer ~= 0 then tb.killPlayer = __killPlayer end
    local __killAI = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __killAI ~= 0 then tb.killAI = __killAI end
    local __killBoss = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __killBoss ~= 0 then tb.killBoss = __killBoss end
    local __team_mate_price = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __team_mate_price ~= 0 then tb.team_mate_price = __team_mate_price end
    local __leave = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __leave ~= false then tb.leave = __leave end
    local __total_price = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __gained_price = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __gained_price ~= 0 then tb.gained_price = __gained_price end
    local __hero_id = decoder:getu64(20)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __killer_type = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __killer_type ~= 0 then tb.killer_type = __killer_type end
    tb.player = pb.pb_DsPlayerBasicInfoDecode(decoder:getsubmsg(31))
    tb.ai = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(32))
    tb.boss = pb.pb_DsAIBasicInfoDecode(decoder:getsubmsg(33))
    local __weapon = decoder:getu64(34)
    if not PB_USE_DEFAULT_TABLE or __weapon ~= 0 then tb.weapon = __weapon end
    local __revive = decoder:getu32(35)
    if not PB_USE_DEFAULT_TABLE or __revive ~= 0 then tb.revive = __revive end
    local __assist_cnt = decoder:getu32(36)
    if not PB_USE_DEFAULT_TABLE or __assist_cnt ~= 0 then tb.assist_cnt = __assist_cnt end
    local __total_shoot = decoder:getu32(37)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu32(38)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_down = decoder:getu32(39)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_down ~= 0 then tb.total_shoot_down = __total_shoot_down end
    local __total_shoot_head_down = decoder:getu32(40)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __total_contract_price = decoder:getu64(41)
    if not PB_USE_DEFAULT_TABLE or __total_contract_price ~= 0 then tb.total_contract_price = __total_contract_price end
    local __total_bring_mandel_brick = decoder:getu32(42)
    if not PB_USE_DEFAULT_TABLE or __total_bring_mandel_brick ~= 0 then tb.total_bring_mandel_brick = __total_bring_mandel_brick end
    local __total_bring_gold_sku = decoder:getu32(43)
    if not PB_USE_DEFAULT_TABLE or __total_bring_gold_sku ~= 0 then tb.total_bring_gold_sku = __total_bring_gold_sku end
    local __total_bring_red_sku = decoder:getu32(44)
    if not PB_USE_DEFAULT_TABLE or __total_bring_red_sku ~= 0 then tb.total_bring_red_sku = __total_bring_red_sku end
    local __total_search_cnt = decoder:getu32(45)
    if not PB_USE_DEFAULT_TABLE or __total_search_cnt ~= 0 then tb.total_search_cnt = __total_search_cnt end
    local __total_mileage = decoder:getu64(46)
    if not PB_USE_DEFAULT_TABLE or __total_mileage ~= 0 then tb.total_mileage = __total_mileage end
    local __begin_game_price = decoder:geti32(47)
    if not PB_USE_DEFAULT_TABLE or __begin_game_price ~= 0 then tb.begin_game_price = __begin_game_price end
    local __safebox_skin_id = decoder:getu64(48)
    if not PB_USE_DEFAULT_TABLE or __safebox_skin_id ~= 0 then tb.safebox_skin_id = __safebox_skin_id end
    return tb
end

function pb.pb_MatchBaseRecordIrisSOLEncode(tb, encoder)
    if(tb.collection_price) then    encoder:addu64(1, tb.collection_price)    end
    if(tb.damage) then    encoder:addu64(2, tb.damage)    end
    if(tb.rescue) then    encoder:addu32(3, tb.rescue)    end
    if(tb.healing) then    encoder:addu32(4, tb.healing)    end
    if(tb.killPlayer) then    encoder:addu32(5, tb.killPlayer)    end
    if(tb.killAI) then    encoder:addu32(6, tb.killAI)    end
    if(tb.killBoss) then    encoder:addu32(7, tb.killBoss)    end
    if(tb.team_mate_price) then    encoder:addu64(9, tb.team_mate_price)    end
    if(tb.leave) then    encoder:addbool(10, tb.leave)    end
    if(tb.total_price) then    encoder:addu64(11, tb.total_price)    end
    if(tb.gained_price) then    encoder:addu64(12, tb.gained_price)    end
    if(tb.hero_id) then    encoder:addu64(20, tb.hero_id)    end
    if(tb.killer_type) then    encoder:addu32(30, tb.killer_type)    end
    if(tb.player) then    pb.pb_DsPlayerBasicInfoEncode(tb.player, encoder:addsubmsg(31))    end
    if(tb.ai) then    pb.pb_DsAIBasicInfoEncode(tb.ai, encoder:addsubmsg(32))    end
    if(tb.boss) then    pb.pb_DsAIBasicInfoEncode(tb.boss, encoder:addsubmsg(33))    end
    if(tb.weapon) then    encoder:addu64(34, tb.weapon)    end
    if(tb.revive) then    encoder:addu32(35, tb.revive)    end
    if(tb.assist_cnt) then    encoder:addu32(36, tb.assist_cnt)    end
    if(tb.total_shoot) then    encoder:addu32(37, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu32(38, tb.total_shoot_hit)    end
    if(tb.total_shoot_down) then    encoder:addu32(39, tb.total_shoot_down)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(40, tb.total_shoot_head_down)    end
    if(tb.total_contract_price) then    encoder:addu64(41, tb.total_contract_price)    end
    if(tb.total_bring_mandel_brick) then    encoder:addu32(42, tb.total_bring_mandel_brick)    end
    if(tb.total_bring_gold_sku) then    encoder:addu32(43, tb.total_bring_gold_sku)    end
    if(tb.total_bring_red_sku) then    encoder:addu32(44, tb.total_bring_red_sku)    end
    if(tb.total_search_cnt) then    encoder:addu32(45, tb.total_search_cnt)    end
    if(tb.total_mileage) then    encoder:addu64(46, tb.total_mileage)    end
    if(tb.begin_game_price) then    encoder:addi32(47, tb.begin_game_price)    end
    if(tb.safebox_skin_id) then    encoder:addu64(48, tb.safebox_skin_id)    end
end

function pb.pb_MatchBaseRecordMPTeamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordMPTeam) or {} 
    local __team_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    tb.player_list = decoder:getu64ary(2)
    return tb
end

function pb.pb_MatchBaseRecordMPTeamEncode(tb, encoder)
    if(tb.team_id) then    encoder:addu32(1, tb.team_id)    end
    if(tb.player_list) then    encoder:addu64(2, tb.player_list)    end
end

function pb.pb_MatchBaseRecordMPCampDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordMPCamp) or {} 
    local __color = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __color ~= 0 then tb.color = __color end
    tb.team_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.team_list[k] = pb.pb_MatchBaseRecordMPTeamDecode(v)
    end
    local __attacker = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __attacker ~= false then tb.attacker = __attacker end
    return tb
end

function pb.pb_MatchBaseRecordMPCampEncode(tb, encoder)
    if(tb.color) then    encoder:addu32(1, tb.color)    end
    if(tb.team_list) then
        for i=1,#(tb.team_list) do
            pb.pb_MatchBaseRecordMPTeamEncode(tb.team_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.attacker) then    encoder:addbool(3, tb.attacker)    end
end

function pb.pb_MatchBaseRecordIrisRaidDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordIrisRaid) or {} 
    local __score = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __level = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __lives = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __lives ~= 0 then tb.lives = __lives end
    local __kill_num = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __kill_num ~= 0 then tb.kill_num = __kill_num end
    local __play_time = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __leave = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __leave ~= false then tb.leave = __leave end
    tb.player_list = decoder:getu64ary(7)
    local __assit_num = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __assit_num ~= 0 then tb.assit_num = __assit_num end
    local __success = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __success ~= false then tb.success = __success end
    local __total_damage = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __death_count = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __death_count ~= 0 then tb.death_count = __death_count end
    local __cost_value = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __cost_value ~= 0 then tb.cost_value = __cost_value end
    local __hero_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    return tb
end

function pb.pb_MatchBaseRecordIrisRaidEncode(tb, encoder)
    if(tb.score) then    encoder:addu32(1, tb.score)    end
    if(tb.level) then    encoder:addu32(2, tb.level)    end
    if(tb.lives) then    encoder:addu32(3, tb.lives)    end
    if(tb.kill_num) then    encoder:addi32(4, tb.kill_num)    end
    if(tb.play_time) then    encoder:addi32(5, tb.play_time)    end
    if(tb.leave) then    encoder:addbool(6, tb.leave)    end
    if(tb.player_list) then    encoder:addu64(7, tb.player_list)    end
    if(tb.assit_num) then    encoder:addi32(8, tb.assit_num)    end
    if(tb.success) then    encoder:addbool(9, tb.success)    end
    if(tb.total_damage) then    encoder:addu32(10, tb.total_damage)    end
    if(tb.death_count) then    encoder:addu32(11, tb.death_count)    end
    if(tb.cost_value) then    encoder:addi32(12, tb.cost_value)    end
    if(tb.hero_id) then    encoder:addu64(13, tb.hero_id)    end
end

function pb.pb_MatchBaseRecordMPTournamentDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordMPTournament) or {} 
    local __is_ranked_match = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __rank_match_score = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    tb.rank_shields = {}
    for k,v in pairs(decoder:getsubmsgary(25)) do
        tb.rank_shields[k] = pb.pb_TournamentRankShieldDecode(v)
    end
    local __ranked_score_leave_penalty = decoder:geti64(26)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_leave_penalty ~= 0 then tb.ranked_score_leave_penalty = __ranked_score_leave_penalty end
    local __ranked_score_half_join_score = decoder:geti64(27)
    if not PB_USE_DEFAULT_TABLE or __ranked_score_half_join_score ~= 0 then tb.ranked_score_half_join_score = __ranked_score_half_join_score end
    local __time_factor = decoder:getdouble(29)
    if not PB_USE_DEFAULT_TABLE or __time_factor ~= 0 then tb.time_factor = __time_factor end
    local __raw_total_ranked_score = decoder:geti64(30)
    if not PB_USE_DEFAULT_TABLE or __raw_total_ranked_score ~= 0 then tb.raw_total_ranked_score = __raw_total_ranked_score end
    local __real_total_ranked_score = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __real_total_ranked_score ~= 0 then tb.real_total_ranked_score = __real_total_ranked_score end
    local __self_ranking = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __self_ranking ~= 0 then tb.self_ranking = __self_ranking end
    local __rank_score_spm = decoder:geti64(33)
    if not PB_USE_DEFAULT_TABLE or __rank_score_spm ~= 0 then tb.rank_score_spm = __rank_score_spm end
    local __rank_score_spm_score = decoder:geti64(34)
    if not PB_USE_DEFAULT_TABLE or __rank_score_spm_score ~= 0 then tb.rank_score_spm_score = __rank_score_spm_score end
    local __rank_score_result_score = decoder:geti64(35)
    if not PB_USE_DEFAULT_TABLE or __rank_score_result_score ~= 0 then tb.rank_score_result_score = __rank_score_result_score end
    local __rank_score_defeated_extra_score = decoder:geti64(36)
    if not PB_USE_DEFAULT_TABLE or __rank_score_defeated_extra_score ~= 0 then tb.rank_score_defeated_extra_score = __rank_score_defeated_extra_score end
    local __rank_score_camps_gap_extra_score = decoder:geti64(37)
    if not PB_USE_DEFAULT_TABLE or __rank_score_camps_gap_extra_score ~= 0 then tb.rank_score_camps_gap_extra_score = __rank_score_camps_gap_extra_score end
    local __rank_score_origin_spm_point = decoder:geti64(38)
    if not PB_USE_DEFAULT_TABLE or __rank_score_origin_spm_point ~= 0 then tb.rank_score_origin_spm_point = __rank_score_origin_spm_point end
    local __rank_score_my_camp_point = decoder:getdouble(39)
    if not PB_USE_DEFAULT_TABLE or __rank_score_my_camp_point ~= 0 then tb.rank_score_my_camp_point = __rank_score_my_camp_point end
    local __rank_score_enemy_camp_point = decoder:getdouble(40)
    if not PB_USE_DEFAULT_TABLE or __rank_score_enemy_camp_point ~= 0 then tb.rank_score_enemy_camp_point = __rank_score_enemy_camp_point end
    local __rank_score_reputation_extra_score = decoder:geti64(41)
    if not PB_USE_DEFAULT_TABLE or __rank_score_reputation_extra_score ~= 0 then tb.rank_score_reputation_extra_score = __rank_score_reputation_extra_score end
    local __play_time = decoder:geti32(42)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.double_rank_info = pb.pb_TournamentRankDoubleInfoDecode(decoder:getsubmsg(43))
    local __rank_score_discount_rate = decoder:getdouble(44)
    if not PB_USE_DEFAULT_TABLE or __rank_score_discount_rate ~= 0 then tb.rank_score_discount_rate = __rank_score_discount_rate end
    local __rank_score_discount_ceiling = decoder:geti64(45)
    if not PB_USE_DEFAULT_TABLE or __rank_score_discount_ceiling ~= 0 then tb.rank_score_discount_ceiling = __rank_score_discount_ceiling end
    return tb
end

function pb.pb_MatchBaseRecordMPTournamentEncode(tb, encoder)
    if(tb.is_ranked_match) then    encoder:addbool(16, tb.is_ranked_match)    end
    if(tb.rank_match_score) then    encoder:addi64(18, tb.rank_match_score)    end
    if(tb.rank_shields) then
        for i=1,#(tb.rank_shields) do
            pb.pb_TournamentRankShieldEncode(tb.rank_shields[i], encoder:addsubmsg(25))
        end
    end
    if(tb.ranked_score_leave_penalty) then    encoder:addi64(26, tb.ranked_score_leave_penalty)    end
    if(tb.ranked_score_half_join_score) then    encoder:addi64(27, tb.ranked_score_half_join_score)    end
    if(tb.time_factor) then    encoder:adddouble(29, tb.time_factor)    end
    if(tb.raw_total_ranked_score) then    encoder:addi64(30, tb.raw_total_ranked_score)    end
    if(tb.real_total_ranked_score) then    encoder:addi64(31, tb.real_total_ranked_score)    end
    if(tb.self_ranking) then    encoder:addi32(32, tb.self_ranking)    end
    if(tb.rank_score_spm) then    encoder:addi64(33, tb.rank_score_spm)    end
    if(tb.rank_score_spm_score) then    encoder:addi64(34, tb.rank_score_spm_score)    end
    if(tb.rank_score_result_score) then    encoder:addi64(35, tb.rank_score_result_score)    end
    if(tb.rank_score_defeated_extra_score) then    encoder:addi64(36, tb.rank_score_defeated_extra_score)    end
    if(tb.rank_score_camps_gap_extra_score) then    encoder:addi64(37, tb.rank_score_camps_gap_extra_score)    end
    if(tb.rank_score_origin_spm_point) then    encoder:addi64(38, tb.rank_score_origin_spm_point)    end
    if(tb.rank_score_my_camp_point) then    encoder:adddouble(39, tb.rank_score_my_camp_point)    end
    if(tb.rank_score_enemy_camp_point) then    encoder:adddouble(40, tb.rank_score_enemy_camp_point)    end
    if(tb.rank_score_reputation_extra_score) then    encoder:addi64(41, tb.rank_score_reputation_extra_score)    end
    if(tb.play_time) then    encoder:addi32(42, tb.play_time)    end
    if(tb.double_rank_info) then    pb.pb_TournamentRankDoubleInfoEncode(tb.double_rank_info, encoder:addsubmsg(43))    end
    if(tb.rank_score_discount_rate) then    encoder:adddouble(44, tb.rank_score_discount_rate)    end
    if(tb.rank_score_discount_ceiling) then    encoder:addi64(45, tb.rank_score_discount_ceiling)    end
end

function pb.pb_MatchBaseRecordMPDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordMP) or {} 
    local __kill = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __kill ~= 0 then tb.kill = __kill end
    local __death = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __death ~= 0 then tb.death = __death end
    local __assist = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __assist ~= 0 then tb.assist = __assist end
    local __score = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __mvp = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __mvp ~= false then tb.mvp = __mvp end
    local __leave = decoder:getbool(6)
    if not PB_USE_DEFAULT_TABLE or __leave ~= false then tb.leave = __leave end
    local __occupy = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __occupy ~= 0 then tb.occupy = __occupy end
    tb.camp_list = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.camp_list[k] = pb.pb_MatchBaseRecordMPCampDecode(v)
    end
    local __color = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __color ~= 0 then tb.color = __color end
    local __is_winner = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_winner ~= false then tb.is_winner = __is_winner end
    local __hero_id = decoder:getu64(11)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __military_rank = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __military_rank ~= 0 then tb.military_rank = __military_rank end
    local __attacker = decoder:getbool(13)
    if not PB_USE_DEFAULT_TABLE or __attacker ~= false then tb.attacker = __attacker end
    local __armedforce_id = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    local __rescue = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __rescue ~= 0 then tb.rescue = __rescue end
    local __total_shoot = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu32(17)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_head_down = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __total_kill_long_range = decoder:getu32(19)
    if not PB_USE_DEFAULT_TABLE or __total_kill_long_range ~= 0 then tb.total_kill_long_range = __total_kill_long_range end
    local __total_capture_point_time = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point_time ~= 0 then tb.total_capture_point_time = __total_capture_point_time end
    local __total_vehicle_use_time = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_use_time ~= 0 then tb.total_vehicle_use_time = __total_vehicle_use_time end
    local __total_damage_to_vehicle = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __total_damage_to_vehicle ~= 0 then tb.total_damage_to_vehicle = __total_damage_to_vehicle end
    local __total_vehicle_kill = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_kill ~= 0 then tb.total_vehicle_kill = __total_vehicle_kill end
    local __total_vehicle_destroyed = decoder:getu32(24)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_destroyed ~= 0 then tb.total_vehicle_destroyed = __total_vehicle_destroyed end
    local __total_vehicle_repair = decoder:getu32(25)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_repair ~= 0 then tb.total_vehicle_repair = __total_vehicle_repair end
    tb.game_medal = {}
    for k,v in pairs(decoder:getsubmsgary(26)) do
        tb.game_medal[k] = pb.pb_DsGameMedalDecode(v)
    end
    local __capture_flag_num = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __capture_flag_num ~= 0 then tb.capture_flag_num = __capture_flag_num end
    local __rescue_contrib = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __rescue_contrib ~= 0 then tb.rescue_contrib = __rescue_contrib end
    local __build_and_destroy_contrib = decoder:geti32(29)
    if not PB_USE_DEFAULT_TABLE or __build_and_destroy_contrib ~= 0 then tb.build_and_destroy_contrib = __build_and_destroy_contrib end
    local __capture_contrib = decoder:geti32(30)
    if not PB_USE_DEFAULT_TABLE or __capture_contrib ~= 0 then tb.capture_contrib = __capture_contrib end
    local __tactics_contrib = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __tactics_contrib ~= 0 then tb.tactics_contrib = __tactics_contrib end
    local __commander_score_delta = decoder:geti64(40)
    if not PB_USE_DEFAULT_TABLE or __commander_score_delta ~= 0 then tb.commander_score_delta = __commander_score_delta end
    local __commander_score = decoder:geti64(41)
    if not PB_USE_DEFAULT_TABLE or __commander_score ~= 0 then tb.commander_score = __commander_score end
    local __vehicle_kill_count = decoder:geti32(42)
    if not PB_USE_DEFAULT_TABLE or __vehicle_kill_count ~= 0 then tb.vehicle_kill_count = __vehicle_kill_count end
    local __vehicle_play_time = decoder:geti32(43)
    if not PB_USE_DEFAULT_TABLE or __vehicle_play_time ~= 0 then tb.vehicle_play_time = __vehicle_play_time end
    local __infantry_kill_count = decoder:geti32(44)
    if not PB_USE_DEFAULT_TABLE or __infantry_kill_count ~= 0 then tb.infantry_kill_count = __infantry_kill_count end
    local __infantry_play_time = decoder:geti32(45)
    if not PB_USE_DEFAULT_TABLE or __infantry_play_time ~= 0 then tb.infantry_play_time = __infantry_play_time end
    local __commander_contributor_title = decoder:geti32(46)
    if not PB_USE_DEFAULT_TABLE or __commander_contributor_title ~= 0 then tb.commander_contributor_title = __commander_contributor_title end
    return tb
end

function pb.pb_MatchBaseRecordMPEncode(tb, encoder)
    if(tb.kill) then    encoder:addu32(1, tb.kill)    end
    if(tb.death) then    encoder:addu32(2, tb.death)    end
    if(tb.assist) then    encoder:addu32(3, tb.assist)    end
    if(tb.score) then    encoder:addu32(4, tb.score)    end
    if(tb.mvp) then    encoder:addbool(5, tb.mvp)    end
    if(tb.leave) then    encoder:addbool(6, tb.leave)    end
    if(tb.occupy) then    encoder:addu32(7, tb.occupy)    end
    if(tb.camp_list) then
        for i=1,#(tb.camp_list) do
            pb.pb_MatchBaseRecordMPCampEncode(tb.camp_list[i], encoder:addsubmsg(8))
        end
    end
    if(tb.color) then    encoder:addu32(9, tb.color)    end
    if(tb.is_winner) then    encoder:addbool(10, tb.is_winner)    end
    if(tb.hero_id) then    encoder:addu64(11, tb.hero_id)    end
    if(tb.military_rank) then    encoder:addi32(12, tb.military_rank)    end
    if(tb.attacker) then    encoder:addbool(13, tb.attacker)    end
    if(tb.armedforce_id) then    encoder:addu32(14, tb.armedforce_id)    end
    if(tb.rescue) then    encoder:addu32(15, tb.rescue)    end
    if(tb.total_shoot) then    encoder:addu32(16, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu32(17, tb.total_shoot_hit)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(18, tb.total_shoot_head_down)    end
    if(tb.total_kill_long_range) then    encoder:addu32(19, tb.total_kill_long_range)    end
    if(tb.total_capture_point_time) then    encoder:addu32(20, tb.total_capture_point_time)    end
    if(tb.total_vehicle_use_time) then    encoder:addu32(21, tb.total_vehicle_use_time)    end
    if(tb.total_damage_to_vehicle) then    encoder:addu32(22, tb.total_damage_to_vehicle)    end
    if(tb.total_vehicle_kill) then    encoder:addu32(23, tb.total_vehicle_kill)    end
    if(tb.total_vehicle_destroyed) then    encoder:addu32(24, tb.total_vehicle_destroyed)    end
    if(tb.total_vehicle_repair) then    encoder:addu32(25, tb.total_vehicle_repair)    end
    if(tb.game_medal) then
        for i=1,#(tb.game_medal) do
            pb.pb_DsGameMedalEncode(tb.game_medal[i], encoder:addsubmsg(26))
        end
    end
    if(tb.capture_flag_num) then    encoder:addi32(27, tb.capture_flag_num)    end
    if(tb.rescue_contrib) then    encoder:addi32(28, tb.rescue_contrib)    end
    if(tb.build_and_destroy_contrib) then    encoder:addi32(29, tb.build_and_destroy_contrib)    end
    if(tb.capture_contrib) then    encoder:addi32(30, tb.capture_contrib)    end
    if(tb.tactics_contrib) then    encoder:addi32(31, tb.tactics_contrib)    end
    if(tb.commander_score_delta) then    encoder:addi64(40, tb.commander_score_delta)    end
    if(tb.commander_score) then    encoder:addi64(41, tb.commander_score)    end
    if(tb.vehicle_kill_count) then    encoder:addi32(42, tb.vehicle_kill_count)    end
    if(tb.vehicle_play_time) then    encoder:addi32(43, tb.vehicle_play_time)    end
    if(tb.infantry_kill_count) then    encoder:addi32(44, tb.infantry_kill_count)    end
    if(tb.infantry_play_time) then    encoder:addi32(45, tb.infantry_play_time)    end
    if(tb.commander_contributor_title) then    encoder:addi32(46, tb.commander_contributor_title)    end
end

function pb.pb_MatchBaseRecordArenaDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchBaseRecordArena) or {} 
    local __kill = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __kill ~= 0 then tb.kill = __kill end
    local __death = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __death ~= 0 then tb.death = __death end
    local __assist = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __assist ~= 0 then tb.assist = __assist end
    local __result = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __team_score = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __team_score ~= 0 then tb.team_score = __team_score end
    local __hero_id = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __rank = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __rank ~= 0 then tb.rank = __rank end
    tb.final_props = {}
    for k,v in pairs(decoder:getsubmsgary(20)) do
        tb.final_props[k] = pb.pb_EquipPositionDecode(v)
    end
    return tb
end

function pb.pb_MatchBaseRecordArenaEncode(tb, encoder)
    if(tb.kill) then    encoder:addu32(1, tb.kill)    end
    if(tb.death) then    encoder:addu32(2, tb.death)    end
    if(tb.assist) then    encoder:addu32(3, tb.assist)    end
    if(tb.result) then    encoder:addu32(10, tb.result)    end
    if(tb.team_score) then    encoder:addu32(11, tb.team_score)    end
    if(tb.hero_id) then    encoder:addu64(12, tb.hero_id)    end
    if(tb.rank) then    encoder:addu64(13, tb.rank)    end
    if(tb.final_props) then
        for i=1,#(tb.final_props) do
            pb.pb_EquipPositionEncode(tb.final_props[i], encoder:addsubmsg(20))
        end
    end
end

function pb.pb_SeasonChangeInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SeasonChangeInfo) or {} 
    local __level = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __next_level_exp = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __next_level_exp ~= 0 then tb.next_level_exp = __next_level_exp end
    local __old_level = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __old_level ~= 0 then tb.old_level = __old_level end
    local __old_exp = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __old_exp ~= 0 then tb.old_exp = __old_exp end
    local __old_next_level_exp = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __old_next_level_exp ~= 0 then tb.old_next_level_exp = __old_next_level_exp end
    local __delta_exp = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __delta_exp ~= 0 then tb.delta_exp = __delta_exp end
    tb.add_exps = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.add_exps[k] = pb.pb_AccountExpChangeDecode(v)
    end
    local __bonus_exp = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __bonus_exp ~= 0 then tb.bonus_exp = __bonus_exp end
    local __room_id = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __map_id = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __old_season_no = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __old_season_no ~= 0 then tb.old_season_no = __old_season_no end
    local __season_no = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __season_no ~= 0 then tb.season_no = __season_no end
    local __old_total_exp = decoder:geti64(22)
    if not PB_USE_DEFAULT_TABLE or __old_total_exp ~= 0 then tb.old_total_exp = __old_total_exp end
    local __total_exp = decoder:geti64(23)
    if not PB_USE_DEFAULT_TABLE or __total_exp ~= 0 then tb.total_exp = __total_exp end
    return tb
end

function pb.pb_SeasonChangeInfoEncode(tb, encoder)
    if(tb.level) then    encoder:addu32(1, tb.level)    end
    if(tb.exp) then    encoder:addi64(2, tb.exp)    end
    if(tb.next_level_exp) then    encoder:addi64(3, tb.next_level_exp)    end
    if(tb.old_level) then    encoder:addu32(4, tb.old_level)    end
    if(tb.old_exp) then    encoder:addi64(5, tb.old_exp)    end
    if(tb.old_next_level_exp) then    encoder:addi64(6, tb.old_next_level_exp)    end
    if(tb.delta_exp) then    encoder:addi64(7, tb.delta_exp)    end
    if(tb.add_exps) then
        for i=1,#(tb.add_exps) do
            pb.pb_AccountExpChangeEncode(tb.add_exps[i], encoder:addsubmsg(8))
        end
    end
    if(tb.bonus_exp) then    encoder:addi64(9, tb.bonus_exp)    end
    if(tb.room_id) then    encoder:addu64(10, tb.room_id)    end
    if(tb.map_id) then    encoder:addi32(11, tb.map_id)    end
    if(tb.old_season_no) then    encoder:addu32(20, tb.old_season_no)    end
    if(tb.season_no) then    encoder:addu32(21, tb.season_no)    end
    if(tb.old_total_exp) then    encoder:addi64(22, tb.old_total_exp)    end
    if(tb.total_exp) then    encoder:addi64(23, tb.total_exp)    end
end

function pb.pb_AccountExpChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AccountExpChange) or {} 
    local __add_exp = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __add_exp ~= 0 then tb.add_exp = __add_exp end
    local __reason = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    return tb
end

function pb.pb_AccountExpChangeEncode(tb, encoder)
    if(tb.add_exp) then    encoder:addi64(1, tb.add_exp)    end
    if(tb.reason) then    encoder:addu32(2, tb.reason)    end
end

function pb.pb_SeasonInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SeasonInfo) or {} 
    local __serial = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __serial ~= 0 then tb.serial = __serial end
    local __level = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __exp = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __total_exp = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __total_exp ~= 0 then tb.total_exp = __total_exp end
    local __last_update_time = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __last_update_time ~= 0 then tb.last_update_time = __last_update_time end
    local __boon_exp_remain = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __boon_exp_remain ~= 0 then tb.boon_exp_remain = __boon_exp_remain end
    local __day_exp = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __day_exp ~= 0 then tb.day_exp = __day_exp end
    local __bonus_percent = decoder:getfloat(8)
    if not PB_USE_DEFAULT_TABLE or __bonus_percent ~= 0 then tb.bonus_percent = __bonus_percent end
    return tb
end

function pb.pb_SeasonInfoEncode(tb, encoder)
    if(tb.serial) then    encoder:addu32(1, tb.serial)    end
    if(tb.level) then    encoder:addu32(2, tb.level)    end
    if(tb.exp) then    encoder:addi64(3, tb.exp)    end
    if(tb.total_exp) then    encoder:addi64(4, tb.total_exp)    end
    if(tb.last_update_time) then    encoder:addu32(5, tb.last_update_time)    end
    if(tb.boon_exp_remain) then    encoder:addi64(6, tb.boon_exp_remain)    end
    if(tb.day_exp) then    encoder:addi64(7, tb.day_exp)    end
    if(tb.bonus_percent) then    encoder:addfloat(8, tb.bonus_percent)    end
end

function pb.pb_AccountPunishItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AccountPunishItem) or {} 
    local __type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __reason = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __begin = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __begin ~= 0 then tb.begin = __begin end
    local ___end = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or ___end ~= 0 then tb._end = ___end end
    local __custom_reason = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __custom_reason ~= "" then tb.custom_reason = __custom_reason end
    local __match_punish_id = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __match_punish_id ~= 0 then tb.match_punish_id = __match_punish_id end
    local __punish_end = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __punish_end ~= 0 then tb.punish_end = __punish_end end
    local __reporting_mode = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __reporting_mode ~= 0 then tb.reporting_mode = __reporting_mode end
    local __number_reports = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __number_reports ~= 0 then tb.number_reports = __number_reports end
    return tb
end

function pb.pb_AccountPunishItemEncode(tb, encoder)
    if(tb.type) then    encoder:addu32(1, tb.type)    end
    if(tb.reason) then    encoder:addu32(2, tb.reason)    end
    if(tb.begin) then    encoder:addi64(3, tb.begin)    end
    if(tb._end) then    encoder:addi64(4, tb._end)    end
    if(tb.custom_reason) then    encoder:addstr(5, tb.custom_reason)    end
    if(tb.match_punish_id) then    encoder:addi64(6, tb.match_punish_id)    end
    if(tb.punish_end) then    encoder:addi64(7, tb.punish_end)    end
    if(tb.reporting_mode) then    encoder:addi64(8, tb.reporting_mode)    end
    if(tb.number_reports) then    encoder:addi64(9, tb.number_reports)    end
end

function pb.pb_IrisSOLDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IrisSOLData) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_escape = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_escape ~= 0 then tb.total_escape = __total_escape end
    local __total_game_time = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_kill = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __carry_teammate_assets = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __carry_teammate_assets ~= 0 then tb.carry_teammate_assets = __carry_teammate_assets end
    local __total_collection_price = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __total_collection_price ~= 0 then tb.total_collection_price = __total_collection_price end
    local __total_gained_price = decoder:getu64(40)
    if not PB_USE_DEFAULT_TABLE or __total_gained_price ~= 0 then tb.total_gained_price = __total_gained_price end
    local __total_miss = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __total_miss ~= 0 then tb.total_miss = __total_miss end
    local __total_price = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __total_fight_all = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __total_fight_all ~= 0 then tb.total_fight_all = __total_fight_all end
    local __total_quit = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __total_quit ~= 0 then tb.total_quit = __total_quit end
    local __total_giveup = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __total_giveup ~= 0 then tb.total_giveup = __total_giveup end
    local __kill_low_stakes = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __kill_low_stakes ~= 0 then tb.kill_low_stakes = __kill_low_stakes end
    local __kill_med_stakes = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __kill_med_stakes ~= 0 then tb.kill_med_stakes = __kill_med_stakes end
    local __kill_high_stakes = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __kill_high_stakes ~= 0 then tb.kill_high_stakes = __kill_high_stakes end
    local __killed_low_stakes = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __killed_low_stakes ~= 0 then tb.killed_low_stakes = __killed_low_stakes end
    local __killed_med_stakes = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __killed_med_stakes ~= 0 then tb.killed_med_stakes = __killed_med_stakes end
    local __killed_high_stakes = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __killed_high_stakes ~= 0 then tb.killed_high_stakes = __killed_high_stakes end
    local __current_asset = decoder:geti64(33)
    if not PB_USE_DEFAULT_TABLE or __current_asset ~= 0 then tb.current_asset = __current_asset end
    local __non_current_assets = decoder:geti64(34)
    if not PB_USE_DEFAULT_TABLE or __non_current_assets ~= 0 then tb.non_current_assets = __non_current_assets end
    local __rank_attended = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __rank_attended ~= false then tb.rank_attended = __rank_attended end
    local __rank_score = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __rank_score ~= 0 then tb.rank_score = __rank_score end
    local __rank_score_max = decoder:geti64(102)
    if not PB_USE_DEFAULT_TABLE or __rank_score_max ~= 0 then tb.rank_score_max = __rank_score_max end
    local __rank_max_season_no = decoder:getu32(103)
    if not PB_USE_DEFAULT_TABLE or __rank_max_season_no ~= 0 then tb.rank_max_season_no = __rank_max_season_no end
    return tb
end

function pb.pb_IrisSOLDataEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_escape) then    encoder:addu32(2, tb.total_escape)    end
    if(tb.total_game_time) then    encoder:addu64(3, tb.total_game_time)    end
    if(tb.total_kill) then    encoder:addu32(5, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(6, tb.total_killed)    end
    if(tb.carry_teammate_assets) then    encoder:addu64(7, tb.carry_teammate_assets)    end
    if(tb.total_collection_price) then    encoder:addu64(8, tb.total_collection_price)    end
    if(tb.total_gained_price) then    encoder:addu64(40, tb.total_gained_price)    end
    if(tb.total_miss) then    encoder:addu32(9, tb.total_miss)    end
    if(tb.total_price) then    encoder:addu64(10, tb.total_price)    end
    if(tb.total_fight_all) then    encoder:addu32(11, tb.total_fight_all)    end
    if(tb.total_quit) then    encoder:addu32(12, tb.total_quit)    end
    if(tb.total_giveup) then    encoder:addu32(13, tb.total_giveup)    end
    if(tb.kill_low_stakes) then    encoder:addu32(20, tb.kill_low_stakes)    end
    if(tb.kill_med_stakes) then    encoder:addu32(21, tb.kill_med_stakes)    end
    if(tb.kill_high_stakes) then    encoder:addu32(22, tb.kill_high_stakes)    end
    if(tb.killed_low_stakes) then    encoder:addu32(30, tb.killed_low_stakes)    end
    if(tb.killed_med_stakes) then    encoder:addu32(31, tb.killed_med_stakes)    end
    if(tb.killed_high_stakes) then    encoder:addu32(32, tb.killed_high_stakes)    end
    if(tb.current_asset) then    encoder:addi64(33, tb.current_asset)    end
    if(tb.non_current_assets) then    encoder:addi64(34, tb.non_current_assets)    end
    if(tb.rank_attended) then    encoder:addbool(100, tb.rank_attended)    end
    if(tb.rank_score) then    encoder:addi64(101, tb.rank_score)    end
    if(tb.rank_score_max) then    encoder:addi64(102, tb.rank_score_max)    end
    if(tb.rank_max_season_no) then    encoder:addu32(103, tb.rank_max_season_no)    end
end

function pb.pb_IrisRaidDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IrisRaidData) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_pass = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_pass ~= 0 then tb.total_pass = __total_pass end
    local __total_pass_perfect = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_pass_perfect ~= 0 then tb.total_pass_perfect = __total_pass_perfect end
    local __hell_mode_1st_pass = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __hell_mode_1st_pass ~= 0 then tb.hell_mode_1st_pass = __hell_mode_1st_pass end
    local __hell_mode_fastest_pass = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __hell_mode_fastest_pass ~= 0 then tb.hell_mode_fastest_pass = __hell_mode_fastest_pass end
    return tb
end

function pb.pb_IrisRaidDataEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_pass) then    encoder:addu32(2, tb.total_pass)    end
    if(tb.total_pass_perfect) then    encoder:addu32(3, tb.total_pass_perfect)    end
    if(tb.hell_mode_1st_pass) then    encoder:addu32(4, tb.hell_mode_1st_pass)    end
    if(tb.hell_mode_fastest_pass) then    encoder:addu32(5, tb.hell_mode_fastest_pass)    end
end

function pb.pb_MPDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPData) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_win = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_win ~= 0 then tb.total_win = __total_win end
    local __total_kill = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __total_mvp = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_mvp ~= 0 then tb.total_mvp = __total_mvp end
    local __total_game_time = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_score = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __total_score ~= 0 then tb.total_score = __total_score end
    local __total_quit = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __total_quit ~= 0 then tb.total_quit = __total_quit end
    local __rank_attended = decoder:getbool(100)
    if not PB_USE_DEFAULT_TABLE or __rank_attended ~= false then tb.rank_attended = __rank_attended end
    local __rank_score = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __rank_score ~= 0 then tb.rank_score = __rank_score end
    local __rank_score_max = decoder:geti64(102)
    if not PB_USE_DEFAULT_TABLE or __rank_score_max ~= 0 then tb.rank_score_max = __rank_score_max end
    local __rank_max_season_no = decoder:getu32(103)
    if not PB_USE_DEFAULT_TABLE or __rank_max_season_no ~= 0 then tb.rank_max_season_no = __rank_max_season_no end
    local __commander_score = decoder:geti64(200)
    if not PB_USE_DEFAULT_TABLE or __commander_score ~= 0 then tb.commander_score = __commander_score end
    return tb
end

function pb.pb_MPDataEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_win) then    encoder:addu32(2, tb.total_win)    end
    if(tb.total_kill) then    encoder:addu32(3, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(4, tb.total_killed)    end
    if(tb.total_mvp) then    encoder:addu32(5, tb.total_mvp)    end
    if(tb.total_game_time) then    encoder:addu64(6, tb.total_game_time)    end
    if(tb.total_score) then    encoder:addu64(7, tb.total_score)    end
    if(tb.total_quit) then    encoder:addu32(8, tb.total_quit)    end
    if(tb.rank_attended) then    encoder:addbool(100, tb.rank_attended)    end
    if(tb.rank_score) then    encoder:addi64(101, tb.rank_score)    end
    if(tb.rank_score_max) then    encoder:addi64(102, tb.rank_score_max)    end
    if(tb.rank_max_season_no) then    encoder:addu32(103, tb.rank_max_season_no)    end
    if(tb.commander_score) then    encoder:addi64(200, tb.commander_score)    end
end

function pb.pb_AttrValueDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AttrValue) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __value = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __value ~= 0 then tb.value = __value end
    return tb
end

function pb.pb_AttrValueEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.value) then    encoder:addfloat(2, tb.value)    end
end

function pb.pb_RoleAttrValueDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoleAttrValue) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.base_attr = pb.pb_AttrValueDecode(decoder:getsubmsg(2))
    tb.buff_attr_add = pb.pb_AttrValueDecode(decoder:getsubmsg(3))
    tb.buff_attr_add_percent = pb.pb_AttrValueDecode(decoder:getsubmsg(4))
    return tb
end

function pb.pb_RoleAttrValueEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.base_attr) then    pb.pb_AttrValueEncode(tb.base_attr, encoder:addsubmsg(2))    end
    if(tb.buff_attr_add) then    pb.pb_AttrValueEncode(tb.buff_attr_add, encoder:addsubmsg(3))    end
    if(tb.buff_attr_add_percent) then    pb.pb_AttrValueEncode(tb.buff_attr_add_percent, encoder:addsubmsg(4))    end
end

function pb.pb_RepLevelConfigDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RepLevelConfig) or {} 
    local __level = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    return tb
end

function pb.pb_RepLevelConfigEncode(tb, encoder)
    if(tb.level) then    encoder:addi64(1, tb.level)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
end

function pb.pb_RoleQualityCardDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoleQualityCard) or {} 
    local __card_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __card_id ~= 0 then tb.card_id = __card_id end
    local __quality_value = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __quality_value ~= 0 then tb.quality_value = __quality_value end
    local __is_unlock = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    local __unlock_time = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    local __is_disabled = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_disabled ~= false then tb.is_disabled = __is_disabled end
    local __level = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    return tb
end

function pb.pb_RoleQualityCardEncode(tb, encoder)
    if(tb.card_id) then    encoder:addu64(1, tb.card_id)    end
    if(tb.quality_value) then    encoder:addi64(2, tb.quality_value)    end
    if(tb.is_unlock) then    encoder:addbool(3, tb.is_unlock)    end
    if(tb.unlock_time) then    encoder:addu64(4, tb.unlock_time)    end
    if(tb.is_disabled) then    encoder:addbool(5, tb.is_disabled)    end
    if(tb.level) then    encoder:addi64(6, tb.level)    end
end

function pb.pb_QualityChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_QualityChange) or {} 
    local __card_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __card_id ~= 0 then tb.card_id = __card_id end
    local __value_delta = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __value_delta ~= 0 then tb.value_delta = __value_delta end
    local __prop_id = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    return tb
end

function pb.pb_QualityChangeEncode(tb, encoder)
    if(tb.card_id) then    encoder:addu64(1, tb.card_id)    end
    if(tb.value_delta) then    encoder:addi64(2, tb.value_delta)    end
    if(tb.prop_id) then    encoder:addu64(3, tb.prop_id)    end
end

function pb.pb_AttrSkillDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_AttrSkill) or {} 
    local __level = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __exp = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    return tb
end

function pb.pb_AttrSkillEncode(tb, encoder)
    if(tb.level) then    encoder:addi32(1, tb.level)    end
    if(tb.exp) then    encoder:addi64(2, tb.exp)    end
end

function pb.pb_GlobalPlayerStateInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GlobalPlayerStateInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __state = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __map_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __team_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __team_zone_id = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __team_zone_id ~= 0 then tb.team_zone_id = __team_zone_id end
    local __team_svr_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __team_svr_id ~= 0 then tb.team_svr_id = __team_svr_id end
    local __conn_bus_id = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __conn_bus_id ~= 0 then tb.conn_bus_id = __conn_bus_id end
    return tb
end

function pb.pb_GlobalPlayerStateInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.state) then    encoder:addu32(2, tb.state)    end
    if(tb.map_id) then    encoder:addu32(3, tb.map_id)    end
    if(tb.team_id) then    encoder:addu64(4, tb.team_id)    end
    if(tb.team_zone_id) then    encoder:addi32(7, tb.team_zone_id)    end
    if(tb.team_svr_id) then    encoder:addu32(6, tb.team_svr_id)    end
    if(tb.conn_bus_id) then    encoder:addi32(5, tb.conn_bus_id)    end
end

function pb.pb_PlayerSimpleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerSimpleInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __nick_name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __nick_name ~= "" then tb.nick_name = __nick_name end
    local __pic_url = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __pic_url ~= "" then tb.pic_url = __pic_url end
    local __level = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __gender = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __gender ~= 0 then tb.gender = __gender end
    local __register_time = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __register_time ~= 0 then tb.register_time = __register_time end
    local __safehouse_degree = decoder:getfloat(20)
    if not PB_USE_DEFAULT_TABLE or __safehouse_degree ~= 0 then tb.safehouse_degree = __safehouse_degree end
    local __season_lvl = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __season_lvl ~= 0 then tb.season_lvl = __season_lvl end
    local __sol_rank_score = decoder:getu64(40)
    if not PB_USE_DEFAULT_TABLE or __sol_rank_score ~= 0 then tb.sol_rank_score = __sol_rank_score end
    local __sol_rank_attended = decoder:getbool(41)
    if not PB_USE_DEFAULT_TABLE or __sol_rank_attended ~= false then tb.sol_rank_attended = __sol_rank_attended end
    local __mp_rank_score = decoder:getu64(42)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_score ~= 0 then tb.mp_rank_score = __mp_rank_score end
    local __mp_rank_attended = decoder:getbool(43)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_attended ~= false then tb.mp_rank_attended = __mp_rank_attended end
    local __mp_commander_score = decoder:getu64(49)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_score ~= 0 then tb.mp_commander_score = __mp_commander_score end
    local __show_commander_rank_points = decoder:geti64(50)
    if not PB_USE_DEFAULT_TABLE or __show_commander_rank_points ~= 0 then tb.show_commander_rank_points = __show_commander_rank_points end
    local __military_tag = decoder:getu64(44)
    if not PB_USE_DEFAULT_TABLE or __military_tag ~= 0 then tb.military_tag = __military_tag end
    local __title = decoder:getu64(45)
    if not PB_USE_DEFAULT_TABLE or __title ~= 0 then tb.title = __title end
    local __rank_title_adcode = decoder:getu32(47)
    if not PB_USE_DEFAULT_TABLE or __rank_title_adcode ~= 0 then tb.rank_title_adcode = __rank_title_adcode end
    local __rank_title_rank_no = decoder:geti64(48)
    if not PB_USE_DEFAULT_TABLE or __rank_title_rank_no ~= 0 then tb.rank_title_rank_no = __rank_title_rank_no end
    local __game_center = decoder:getu32(46)
    if not PB_USE_DEFAULT_TABLE or __game_center ~= 0 then tb.game_center = __game_center end
    local __vip_lvl = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __vip_lvl ~= 0 then tb.vip_lvl = __vip_lvl end
    local __svip_lvl = decoder:geti32(31)
    if not PB_USE_DEFAULT_TABLE or __svip_lvl ~= 0 then tb.svip_lvl = __svip_lvl end
    local __monthcard_level = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __monthcard_level ~= 0 then tb.monthcard_level = __monthcard_level end
    local __ex_vip_flag = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __ex_vip_flag ~= 0 then tb.ex_vip_flag = __ex_vip_flag end
    local __fight_point = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __fight_point ~= 0 then tb.fight_point = __fight_point end
    local __role_id = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __role_id ~= 0 then tb.role_id = __role_id end
    local __headwear_id = decoder:getu64(80)
    if not PB_USE_DEFAULT_TABLE or __headwear_id ~= 0 then tb.headwear_id = __headwear_id end
    local __backdecoration_id = decoder:getu64(81)
    if not PB_USE_DEFAULT_TABLE or __backdecoration_id ~= 0 then tb.backdecoration_id = __backdecoration_id end
    local __head_frame = decoder:getu64(63)
    if not PB_USE_DEFAULT_TABLE or __head_frame ~= 0 then tb.head_frame = __head_frame end
    local __btype = decoder:getu32(64)
    if not PB_USE_DEFAULT_TABLE or __btype ~= 0 then tb.btype = __btype end
    local __account_lvl = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __account_lvl ~= 0 then tb.account_lvl = __account_lvl end
    local __account_type = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    local __login_conn_index = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __login_conn_index ~= 0 then tb.login_conn_index = __login_conn_index end
    local __login_gateway = decoder:getstr(26)
    if not PB_USE_DEFAULT_TABLE or __login_gateway ~= "" then tb.login_gateway = __login_gateway end
    local __login_tconnd = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __login_tconnd ~= 0 then tb.login_tconnd = __login_tconnd end
    local __plat = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __plat ~= 0 then tb.plat = __plat end
    local __last_login_version = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __last_login_version ~= 0 then tb.last_login_version = __last_login_version end
    tb.exp_tag_id = decoder:geti32ary(29)
    local __can_cross_plat_play = decoder:getbool(82)
    if not PB_USE_DEFAULT_TABLE or __can_cross_plat_play ~= false then tb.can_cross_plat_play = __can_cross_plat_play end
    local __bhd_is_purchased = decoder:getbool(30)
    if not PB_USE_DEFAULT_TABLE or __bhd_is_purchased ~= false then tb.bhd_is_purchased = __bhd_is_purchased end
    local __country_code = decoder:geti32(32)
    if not PB_USE_DEFAULT_TABLE or __country_code ~= 0 then tb.country_code = __country_code end
    local __area_code = decoder:geti32(33)
    if not PB_USE_DEFAULT_TABLE or __area_code ~= 0 then tb.area_code = __area_code end
    local __ps_can_cross_plat_play = decoder:getbool(34)
    if not PB_USE_DEFAULT_TABLE or __ps_can_cross_plat_play ~= false then tb.ps_can_cross_plat_play = __ps_can_cross_plat_play end
    local __xbox_can_cross_plat_play = decoder:getbool(35)
    if not PB_USE_DEFAULT_TABLE or __xbox_can_cross_plat_play ~= false then tb.xbox_can_cross_plat_play = __xbox_can_cross_plat_play end
    local __login_time = decoder:geti64(36)
    if not PB_USE_DEFAULT_TABLE or __login_time ~= 0 then tb.login_time = __login_time end
    return tb
end

function pb.pb_PlayerSimpleInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.nick_name) then    encoder:addstr(2, tb.nick_name)    end
    if(tb.pic_url) then    encoder:addstr(3, tb.pic_url)    end
    if(tb.level) then    encoder:addu32(4, tb.level)    end
    if(tb.gender) then    encoder:addi32(5, tb.gender)    end
    if(tb.register_time) then    encoder:addu64(9, tb.register_time)    end
    if(tb.safehouse_degree) then    encoder:addfloat(20, tb.safehouse_degree)    end
    if(tb.season_lvl) then    encoder:addu32(23, tb.season_lvl)    end
    if(tb.sol_rank_score) then    encoder:addu64(40, tb.sol_rank_score)    end
    if(tb.sol_rank_attended) then    encoder:addbool(41, tb.sol_rank_attended)    end
    if(tb.mp_rank_score) then    encoder:addu64(42, tb.mp_rank_score)    end
    if(tb.mp_rank_attended) then    encoder:addbool(43, tb.mp_rank_attended)    end
    if(tb.mp_commander_score) then    encoder:addu64(49, tb.mp_commander_score)    end
    if(tb.show_commander_rank_points) then    encoder:addi64(50, tb.show_commander_rank_points)    end
    if(tb.military_tag) then    encoder:addu64(44, tb.military_tag)    end
    if(tb.title) then    encoder:addu64(45, tb.title)    end
    if(tb.rank_title_adcode) then    encoder:addu32(47, tb.rank_title_adcode)    end
    if(tb.rank_title_rank_no) then    encoder:addi64(48, tb.rank_title_rank_no)    end
    if(tb.game_center) then    encoder:addu32(46, tb.game_center)    end
    if(tb.vip_lvl) then    encoder:addi32(6, tb.vip_lvl)    end
    if(tb.svip_lvl) then    encoder:addi32(31, tb.svip_lvl)    end
    if(tb.monthcard_level) then    encoder:addi32(15, tb.monthcard_level)    end
    if(tb.ex_vip_flag) then    encoder:addu32(10, tb.ex_vip_flag)    end
    if(tb.fight_point) then    encoder:addi32(7, tb.fight_point)    end
    if(tb.role_id) then    encoder:addu64(8, tb.role_id)    end
    if(tb.headwear_id) then    encoder:addu64(80, tb.headwear_id)    end
    if(tb.backdecoration_id) then    encoder:addu64(81, tb.backdecoration_id)    end
    if(tb.head_frame) then    encoder:addu64(63, tb.head_frame)    end
    if(tb.btype) then    encoder:addu32(64, tb.btype)    end
    if(tb.account_lvl) then    encoder:addu32(21, tb.account_lvl)    end
    if(tb.account_type) then    encoder:addi32(24, tb.account_type)    end
    if(tb.login_conn_index) then    encoder:addi32(25, tb.login_conn_index)    end
    if(tb.login_gateway) then    encoder:addstr(26, tb.login_gateway)    end
    if(tb.login_tconnd) then    encoder:addi32(27, tb.login_tconnd)    end
    if(tb.plat) then    encoder:addi32(28, tb.plat)    end
    if(tb.last_login_version) then    encoder:addu64(13, tb.last_login_version)    end
    if(tb.exp_tag_id) then    encoder:addi32(29, tb.exp_tag_id)    end
    if(tb.can_cross_plat_play) then    encoder:addbool(82, tb.can_cross_plat_play)    end
    if(tb.bhd_is_purchased) then    encoder:addbool(30, tb.bhd_is_purchased)    end
    if(tb.country_code) then    encoder:addi32(32, tb.country_code)    end
    if(tb.area_code) then    encoder:addi32(33, tb.area_code)    end
    if(tb.ps_can_cross_plat_play) then    encoder:addbool(34, tb.ps_can_cross_plat_play)    end
    if(tb.xbox_can_cross_plat_play) then    encoder:addbool(35, tb.xbox_can_cross_plat_play)    end
    if(tb.login_time) then    encoder:addi64(36, tb.login_time)    end
end

function pb.pb_MatchPlayerInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MatchPlayerInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __camp = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __camp ~= 0 then tb.camp = __camp end
    local __seat_index = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __seat_index ~= 0 then tb.seat_index = __seat_index end
    local __zone_bus_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __zone_bus_id ~= 0 then tb.zone_bus_id = __zone_bus_id end
    local __is_robot = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_robot ~= false then tb.is_robot = __is_robot end
    tb.profile = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(6))
    local __ai_level = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __ai_level ~= 0 then tb.ai_level = __ai_level end
    local __is_halfway_join = decoder:getbool(8)
    if not PB_USE_DEFAULT_TABLE or __is_halfway_join ~= false then tb.is_halfway_join = __is_halfway_join end
    local __is_team = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __is_team ~= false then tb.is_team = __is_team end
    local __is_rand_map = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_rand_map ~= false then tb.is_rand_map = __is_rand_map end
    local __zone_id = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __zone_id ~= 0 then tb.zone_id = __zone_id end
    local __team_id = decoder:getu64(13)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __team_member_count = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __team_member_count ~= 0 then tb.team_member_count = __team_member_count end
    local __team_forbid_stranger = decoder:getbool(15)
    if not PB_USE_DEFAULT_TABLE or __team_forbid_stranger ~= false then tb.team_forbid_stranger = __team_forbid_stranger end
    local __roomsvr_team_id = decoder:getu32(16)
    if not PB_USE_DEFAULT_TABLE or __roomsvr_team_id ~= 0 then tb.roomsvr_team_id = __roomsvr_team_id end
    local __is_dead = decoder:getbool(17)
    if not PB_USE_DEFAULT_TABLE or __is_dead ~= false then tb.is_dead = __is_dead end
    local __group_id = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __group_id ~= 0 then tb.group_id = __group_id end
    local __ai_robot = decoder:getu32(19)
    if not PB_USE_DEFAULT_TABLE or __ai_robot ~= 0 then tb.ai_robot = __ai_robot end
    local __ds_seat_index = decoder:geti32(21)
    if not PB_USE_DEFAULT_TABLE or __ds_seat_index ~= 0 then tb.ds_seat_index = __ds_seat_index end
    local __observer_type = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __observer_type ~= 0 then tb.observer_type = __observer_type end
    local __observer_info = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __observer_info ~= 0 then tb.observer_info = __observer_info end
    local __ds_token = decoder:getstr(24)
    if not PB_USE_DEFAULT_TABLE or __ds_token ~= "" then tb.ds_token = __ds_token end
    local __match_pool_id = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __match_pool_id ~= 0 then tb.match_pool_id = __match_pool_id end
    local __score = decoder:getu64(26)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    local __tag = decoder:getu32(27)
    if not PB_USE_DEFAULT_TABLE or __tag ~= 0 then tb.tag = __tag end
    tb.sol_cal_info = pb.pb_SOLPlayerMatchCalInfoDecode(decoder:getsubmsg(28))
    tb.exp_tag_id = decoder:geti32ary(29)
    local __exp = decoder:geti64(30)
    if not PB_USE_DEFAULT_TABLE or __exp ~= 0 then tb.exp = __exp end
    local __hidden_score = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __hidden_score ~= 0 then tb.hidden_score = __hidden_score end
    local __match_uuid = decoder:getstr(32)
    if not PB_USE_DEFAULT_TABLE or __match_uuid ~= "" then tb.match_uuid = __match_uuid end
    local __is_ranked_match = decoder:getbool(33)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __tdm_scheme_id = decoder:geti64(34)
    if not PB_USE_DEFAULT_TABLE or __tdm_scheme_id ~= 0 then tb.tdm_scheme_id = __tdm_scheme_id end
    local __is_finish_tdm_scheme = decoder:getbool(35)
    if not PB_USE_DEFAULT_TABLE or __is_finish_tdm_scheme ~= false then tb.is_finish_tdm_scheme = __is_finish_tdm_scheme end
    local __is_observer = decoder:getbool(36)
    if not PB_USE_DEFAULT_TABLE or __is_observer ~= false then tb.is_observer = __is_observer end
    local __observer_id = decoder:getu32(37)
    if not PB_USE_DEFAULT_TABLE or __observer_id ~= 0 then tb.observer_id = __observer_id end
    local __sol_rank_score = decoder:geti64(38)
    if not PB_USE_DEFAULT_TABLE or __sol_rank_score ~= 0 then tb.sol_rank_score = __sol_rank_score end
    local __sol_rank_level = decoder:getu32(39)
    if not PB_USE_DEFAULT_TABLE or __sol_rank_level ~= 0 then tb.sol_rank_level = __sol_rank_level end
    local __sol_attended = decoder:getbool(40)
    if not PB_USE_DEFAULT_TABLE or __sol_attended ~= false then tb.sol_attended = __sol_attended end
    local __isolation_tag = decoder:getu32(41)
    if not PB_USE_DEFAULT_TABLE or __isolation_tag ~= 0 then tb.isolation_tag = __isolation_tag end
    local __sol_season_match_count = decoder:geti64(42)
    if not PB_USE_DEFAULT_TABLE or __sol_season_match_count ~= 0 then tb.sol_season_match_count = __sol_season_match_count end
    local __sol_season_rank_count = decoder:geti64(43)
    if not PB_USE_DEFAULT_TABLE or __sol_season_rank_count ~= 0 then tb.sol_season_rank_count = __sol_season_rank_count end
    local __use_rental_props = decoder:getbool(44)
    if not PB_USE_DEFAULT_TABLE or __use_rental_props ~= false then tb.use_rental_props = __use_rental_props end
    local __enable_replay = decoder:getbool(45)
    if not PB_USE_DEFAULT_TABLE or __enable_replay ~= false then tb.enable_replay = __enable_replay end
    local __tdm_rank_score = decoder:geti64(46)
    if not PB_USE_DEFAULT_TABLE or __tdm_rank_score ~= 0 then tb.tdm_rank_score = __tdm_rank_score end
    local __tdm_rank_level = decoder:getu32(47)
    if not PB_USE_DEFAULT_TABLE or __tdm_rank_level ~= 0 then tb.tdm_rank_level = __tdm_rank_level end
    tb.player_label = pb.pb_PlayerLabelDecode(decoder:getsubmsg(48))
    local __reputation_level = decoder:getu32(49)
    if not PB_USE_DEFAULT_TABLE or __reputation_level ~= 0 then tb.reputation_level = __reputation_level end
    tb.tdm_detail = pb.pb_TDMDsDetailDecode(decoder:getsubmsg(50))
    tb.arena_cal_info = pb.pb_ArenaPlayerMatchCalInfoDecode(decoder:getsubmsg(51))
    local __tdm_rank_max_score = decoder:geti64(52)
    if not PB_USE_DEFAULT_TABLE or __tdm_rank_max_score ~= 0 then tb.tdm_rank_max_score = __tdm_rank_max_score end
    local __punish_replay_begin_time = decoder:geti64(53)
    if not PB_USE_DEFAULT_TABLE or __punish_replay_begin_time ~= 0 then tb.punish_replay_begin_time = __punish_replay_begin_time end
    local __punish_replay_report_cnts = decoder:geti64(54)
    if not PB_USE_DEFAULT_TABLE or __punish_replay_report_cnts ~= 0 then tb.punish_replay_report_cnts = __punish_replay_report_cnts end
    local __tdm_lost_days = decoder:geti64(55)
    if not PB_USE_DEFAULT_TABLE or __tdm_lost_days ~= 0 then tb.tdm_lost_days = __tdm_lost_days end
    local __tdm_last_rank_max_score = decoder:geti64(56)
    if not PB_USE_DEFAULT_TABLE or __tdm_last_rank_max_score ~= 0 then tb.tdm_last_rank_max_score = __tdm_last_rank_max_score end
    return tb
end

function pb.pb_MatchPlayerInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.camp) then    encoder:addi32(2, tb.camp)    end
    if(tb.seat_index) then    encoder:addi32(3, tb.seat_index)    end
    if(tb.zone_bus_id) then    encoder:addu32(4, tb.zone_bus_id)    end
    if(tb.is_robot) then    encoder:addbool(5, tb.is_robot)    end
    if(tb.profile) then    pb.pb_PlayerSimpleInfoEncode(tb.profile, encoder:addsubmsg(6))    end
    if(tb.ai_level) then    encoder:addi32(7, tb.ai_level)    end
    if(tb.is_halfway_join) then    encoder:addbool(8, tb.is_halfway_join)    end
    if(tb.is_team) then    encoder:addbool(9, tb.is_team)    end
    if(tb.is_rand_map) then    encoder:addbool(10, tb.is_rand_map)    end
    if(tb.zone_id) then    encoder:addi32(12, tb.zone_id)    end
    if(tb.team_id) then    encoder:addu64(13, tb.team_id)    end
    if(tb.team_member_count) then    encoder:addu32(14, tb.team_member_count)    end
    if(tb.team_forbid_stranger) then    encoder:addbool(15, tb.team_forbid_stranger)    end
    if(tb.roomsvr_team_id) then    encoder:addu32(16, tb.roomsvr_team_id)    end
    if(tb.is_dead) then    encoder:addbool(17, tb.is_dead)    end
    if(tb.group_id) then    encoder:addu32(18, tb.group_id)    end
    if(tb.ai_robot) then    encoder:addu32(19, tb.ai_robot)    end
    if(tb.ds_seat_index) then    encoder:addi32(21, tb.ds_seat_index)    end
    if(tb.observer_type) then    encoder:addu32(22, tb.observer_type)    end
    if(tb.observer_info) then    encoder:addu32(23, tb.observer_info)    end
    if(tb.ds_token) then    encoder:addstr(24, tb.ds_token)    end
    if(tb.match_pool_id) then    encoder:addi32(25, tb.match_pool_id)    end
    if(tb.score) then    encoder:addu64(26, tb.score)    end
    if(tb.tag) then    encoder:addu32(27, tb.tag)    end
    if(tb.sol_cal_info) then    pb.pb_SOLPlayerMatchCalInfoEncode(tb.sol_cal_info, encoder:addsubmsg(28))    end
    if(tb.exp_tag_id) then    encoder:addi32(29, tb.exp_tag_id)    end
    if(tb.exp) then    encoder:addi64(30, tb.exp)    end
    if(tb.hidden_score) then    encoder:addi64(31, tb.hidden_score)    end
    if(tb.match_uuid) then    encoder:addstr(32, tb.match_uuid)    end
    if(tb.is_ranked_match) then    encoder:addbool(33, tb.is_ranked_match)    end
    if(tb.tdm_scheme_id) then    encoder:addi64(34, tb.tdm_scheme_id)    end
    if(tb.is_finish_tdm_scheme) then    encoder:addbool(35, tb.is_finish_tdm_scheme)    end
    if(tb.is_observer) then    encoder:addbool(36, tb.is_observer)    end
    if(tb.observer_id) then    encoder:addu32(37, tb.observer_id)    end
    if(tb.sol_rank_score) then    encoder:addi64(38, tb.sol_rank_score)    end
    if(tb.sol_rank_level) then    encoder:addu32(39, tb.sol_rank_level)    end
    if(tb.sol_attended) then    encoder:addbool(40, tb.sol_attended)    end
    if(tb.isolation_tag) then    encoder:addu32(41, tb.isolation_tag)    end
    if(tb.sol_season_match_count) then    encoder:addi64(42, tb.sol_season_match_count)    end
    if(tb.sol_season_rank_count) then    encoder:addi64(43, tb.sol_season_rank_count)    end
    if(tb.use_rental_props) then    encoder:addbool(44, tb.use_rental_props)    end
    if(tb.enable_replay) then    encoder:addbool(45, tb.enable_replay)    end
    if(tb.tdm_rank_score) then    encoder:addi64(46, tb.tdm_rank_score)    end
    if(tb.tdm_rank_level) then    encoder:addu32(47, tb.tdm_rank_level)    end
    if(tb.player_label) then    pb.pb_PlayerLabelEncode(tb.player_label, encoder:addsubmsg(48))    end
    if(tb.reputation_level) then    encoder:addu32(49, tb.reputation_level)    end
    if(tb.tdm_detail) then    pb.pb_TDMDsDetailEncode(tb.tdm_detail, encoder:addsubmsg(50))    end
    if(tb.arena_cal_info) then    pb.pb_ArenaPlayerMatchCalInfoEncode(tb.arena_cal_info, encoder:addsubmsg(51))    end
    if(tb.tdm_rank_max_score) then    encoder:addi64(52, tb.tdm_rank_max_score)    end
    if(tb.punish_replay_begin_time) then    encoder:addi64(53, tb.punish_replay_begin_time)    end
    if(tb.punish_replay_report_cnts) then    encoder:addi64(54, tb.punish_replay_report_cnts)    end
    if(tb.tdm_lost_days) then    encoder:addi64(55, tb.tdm_lost_days)    end
    if(tb.tdm_last_rank_max_score) then    encoder:addi64(56, tb.tdm_last_rank_max_score)    end
end

function pb.pb_PlayerLabelDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerLabel) or {} 
    local __sol_match_rank_preference_pc = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __sol_match_rank_preference_pc ~= 0 then tb.sol_match_rank_preference_pc = __sol_match_rank_preference_pc end
    local __sol_match_rank_preference_mobile = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __sol_match_rank_preference_mobile ~= 0 then tb.sol_match_rank_preference_mobile = __sol_match_rank_preference_mobile end
    local __sol_combat_density_pc = decoder:geti32(16)
    if not PB_USE_DEFAULT_TABLE or __sol_combat_density_pc ~= 0 then tb.sol_combat_density_pc = __sol_combat_density_pc end
    local __sol_combat_density_mobile = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __sol_combat_density_mobile ~= 0 then tb.sol_combat_density_mobile = __sol_combat_density_mobile end
    local __sol_combat_density_player_pc = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __sol_combat_density_player_pc ~= 0 then tb.sol_combat_density_player_pc = __sol_combat_density_player_pc end
    local __sol_combat_density_player_mobile = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __sol_combat_density_player_mobile ~= 0 then tb.sol_combat_density_player_mobile = __sol_combat_density_player_mobile end
    local __sol_combat_density_ai_pc = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __sol_combat_density_ai_pc ~= 0 then tb.sol_combat_density_ai_pc = __sol_combat_density_ai_pc end
    local __sol_combat_density_ai_mobile = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __sol_combat_density_ai_mobile ~= 0 then tb.sol_combat_density_ai_mobile = __sol_combat_density_ai_mobile end
    local __social_preference_pc = decoder:geti32(18)
    if not PB_USE_DEFAULT_TABLE or __social_preference_pc ~= 0 then tb.social_preference_pc = __social_preference_pc end
    local __social_preference_mobile = decoder:geti32(19)
    if not PB_USE_DEFAULT_TABLE or __social_preference_mobile ~= 0 then tb.social_preference_mobile = __social_preference_mobile end
    local __sol_team_match_preference_pc = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __sol_team_match_preference_pc ~= 0 then tb.sol_team_match_preference_pc = __sol_team_match_preference_pc end
    local __sol_team_match_preference_mobile = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __sol_team_match_preference_mobile ~= 0 then tb.sol_team_match_preference_mobile = __sol_team_match_preference_mobile end
    local __sol_collaborative_action_preference_pc = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __sol_collaborative_action_preference_pc ~= 0 then tb.sol_collaborative_action_preference_pc = __sol_collaborative_action_preference_pc end
    local __sol_collaborative_action_preference_mobile = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __sol_collaborative_action_preference_mobile ~= 0 then tb.sol_collaborative_action_preference_mobile = __sol_collaborative_action_preference_mobile end
    local __sol_microphone_motive = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __sol_microphone_motive ~= 0 then tb.sol_microphone_motive = __sol_microphone_motive end
    local __sol_microphone_time = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __sol_microphone_time ~= 0 then tb.sol_microphone_time = __sol_microphone_time end
    local __sol_typing_prefernce = decoder:geti32(13)
    if not PB_USE_DEFAULT_TABLE or __sol_typing_prefernce ~= 0 then tb.sol_typing_prefernce = __sol_typing_prefernce end
    local __sol_rescue_ability = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __sol_rescue_ability ~= 0 then tb.sol_rescue_ability = __sol_rescue_ability end
    local __sol_revive_ability = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __sol_revive_ability ~= 0 then tb.sol_revive_ability = __sol_revive_ability end
    local __sol_carry_in_equip_level = decoder:geti32(101)
    if not PB_USE_DEFAULT_TABLE or __sol_carry_in_equip_level ~= 0 then tb.sol_carry_in_equip_level = __sol_carry_in_equip_level end
    return tb
end

function pb.pb_PlayerLabelEncode(tb, encoder)
    if(tb.sol_match_rank_preference_pc) then    encoder:addi32(1, tb.sol_match_rank_preference_pc)    end
    if(tb.sol_match_rank_preference_mobile) then    encoder:addi32(2, tb.sol_match_rank_preference_mobile)    end
    if(tb.sol_combat_density_pc) then    encoder:addi32(16, tb.sol_combat_density_pc)    end
    if(tb.sol_combat_density_mobile) then    encoder:addi32(17, tb.sol_combat_density_mobile)    end
    if(tb.sol_combat_density_player_pc) then    encoder:addi32(3, tb.sol_combat_density_player_pc)    end
    if(tb.sol_combat_density_player_mobile) then    encoder:addi32(4, tb.sol_combat_density_player_mobile)    end
    if(tb.sol_combat_density_ai_pc) then    encoder:addi32(5, tb.sol_combat_density_ai_pc)    end
    if(tb.sol_combat_density_ai_mobile) then    encoder:addi32(6, tb.sol_combat_density_ai_mobile)    end
    if(tb.social_preference_pc) then    encoder:addi32(18, tb.social_preference_pc)    end
    if(tb.social_preference_mobile) then    encoder:addi32(19, tb.social_preference_mobile)    end
    if(tb.sol_team_match_preference_pc) then    encoder:addi32(7, tb.sol_team_match_preference_pc)    end
    if(tb.sol_team_match_preference_mobile) then    encoder:addi32(8, tb.sol_team_match_preference_mobile)    end
    if(tb.sol_collaborative_action_preference_pc) then    encoder:addi32(9, tb.sol_collaborative_action_preference_pc)    end
    if(tb.sol_collaborative_action_preference_mobile) then    encoder:addi32(10, tb.sol_collaborative_action_preference_mobile)    end
    if(tb.sol_microphone_motive) then    encoder:addi32(11, tb.sol_microphone_motive)    end
    if(tb.sol_microphone_time) then    encoder:addi32(12, tb.sol_microphone_time)    end
    if(tb.sol_typing_prefernce) then    encoder:addi32(13, tb.sol_typing_prefernce)    end
    if(tb.sol_rescue_ability) then    encoder:addi32(14, tb.sol_rescue_ability)    end
    if(tb.sol_revive_ability) then    encoder:addi32(15, tb.sol_revive_ability)    end
    if(tb.sol_carry_in_equip_level) then    encoder:addi32(101, tb.sol_carry_in_equip_level)    end
end

function pb.pb_SOLPlayerMatchCalInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SOLPlayerMatchCalInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __course_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __course_id ~= 0 then tb.course_id = __course_id end
    local __scheme_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __scheme_id ~= 0 then tb.scheme_id = __scheme_id end
    local __skill_score = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __skill_score ~= 0 then tb.skill_score = __skill_score end
    local __gm_loot_style = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __gm_loot_style ~= 0 then tb.gm_loot_style = __gm_loot_style end
    local __glicko_rating = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating ~= 0 then tb.glicko_rating = __glicko_rating end
    local __glicko_rating_dev = decoder:getfloat(7)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_dev ~= 0 then tb.glicko_rating_dev = __glicko_rating_dev end
    local __deposit_asset = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __deposit_asset ~= 0 then tb.deposit_asset = __deposit_asset end
    local __sol_match_cnt = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __sol_match_cnt ~= 0 then tb.sol_match_cnt = __sol_match_cnt end
    local __streak_count = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __streak_count ~= 0 then tb.streak_count = __streak_count end
    local __streak_loot_count = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __streak_loot_count ~= 0 then tb.streak_loot_count = __streak_loot_count end
    local __sol_match_course_cnt = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __sol_match_course_cnt ~= 0 then tb.sol_match_course_cnt = __sol_match_course_cnt end
    local __sol_total_cnt = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __sol_total_cnt ~= 0 then tb.sol_total_cnt = __sol_total_cnt end
    local __gm_course_id = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __gm_course_id ~= 0 then tb.gm_course_id = __gm_course_id end
    local __gm_scheme_id = decoder:getu32(15)
    if not PB_USE_DEFAULT_TABLE or __gm_scheme_id ~= 0 then tb.gm_scheme_id = __gm_scheme_id end
    local __gm_model_score = decoder:getfloat(16)
    if not PB_USE_DEFAULT_TABLE or __gm_model_score ~= 0 then tb.gm_model_score = __gm_model_score end
    local __sol_course_difficult_cnt = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __sol_course_difficult_cnt ~= 0 then tb.sol_course_difficult_cnt = __sol_course_difficult_cnt end
    local __deposit_asset_no_virtual_prop = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __deposit_asset_no_virtual_prop ~= 0 then tb.deposit_asset_no_virtual_prop = __deposit_asset_no_virtual_prop end
    local __total_lose_match_count = decoder:getu32(19)
    if not PB_USE_DEFAULT_TABLE or __total_lose_match_count ~= 0 then tb.total_lose_match_count = __total_lose_match_count end
    local __system_control_lose_count = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __system_control_lose_count ~= 0 then tb.system_control_lose_count = __system_control_lose_count end
    local __old_glicko_rating = decoder:getfloat(21)
    if not PB_USE_DEFAULT_TABLE or __old_glicko_rating ~= 0 then tb.old_glicko_rating = __old_glicko_rating end
    local __old_glicko_rating_dev = decoder:getfloat(22)
    if not PB_USE_DEFAULT_TABLE or __old_glicko_rating_dev ~= 0 then tb.old_glicko_rating_dev = __old_glicko_rating_dev end
    local __gm_force_course_id = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __gm_force_course_id ~= 0 then tb.gm_force_course_id = __gm_force_course_id end
    local __sol_rank_match_count = decoder:geti64(25)
    if not PB_USE_DEFAULT_TABLE or __sol_rank_match_count ~= 0 then tb.sol_rank_match_count = __sol_rank_match_count end
    local __gm_course_type = decoder:geti32(26)
    if not PB_USE_DEFAULT_TABLE or __gm_course_type ~= 0 then tb.gm_course_type = __gm_course_type end
    local __gm_force_course_type = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __gm_force_course_type ~= 0 then tb.gm_force_course_type = __gm_force_course_type end
    local __glicko_after_adjust = decoder:getfloat(28)
    if not PB_USE_DEFAULT_TABLE or __glicko_after_adjust ~= 0 then tb.glicko_after_adjust = __glicko_after_adjust end
    local __rank_score_after_adjust = decoder:geti64(29)
    if not PB_USE_DEFAULT_TABLE or __rank_score_after_adjust ~= 0 then tb.rank_score_after_adjust = __rank_score_after_adjust end
    local __gm_force_scheme_id = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __gm_force_scheme_id ~= 0 then tb.gm_force_scheme_id = __gm_force_scheme_id end
    local __gm_force_max_wait_time = decoder:getbool(31)
    if not PB_USE_DEFAULT_TABLE or __gm_force_max_wait_time ~= false then tb.gm_force_max_wait_time = __gm_force_max_wait_time end
    local __glicko_adjust_value_by_rule = decoder:getfloat(32)
    if not PB_USE_DEFAULT_TABLE or __glicko_adjust_value_by_rule ~= 0 then tb.glicko_adjust_value_by_rule = __glicko_adjust_value_by_rule end
    local __rank_adjust_value_by_rule = decoder:geti64(33)
    if not PB_USE_DEFAULT_TABLE or __rank_adjust_value_by_rule ~= 0 then tb.rank_adjust_value_by_rule = __rank_adjust_value_by_rule end
    local __glicko_adjust_value_by_back = decoder:getfloat(34)
    if not PB_USE_DEFAULT_TABLE or __glicko_adjust_value_by_back ~= 0 then tb.glicko_adjust_value_by_back = __glicko_adjust_value_by_back end
    local __rank_adjust_value_by_back = decoder:geti64(35)
    if not PB_USE_DEFAULT_TABLE or __rank_adjust_value_by_back ~= 0 then tb.rank_adjust_value_by_back = __rank_adjust_value_by_back end
    local __gm_glicko_adjust_value = decoder:getfloat(36)
    if not PB_USE_DEFAULT_TABLE or __gm_glicko_adjust_value ~= 0 then tb.gm_glicko_adjust_value = __gm_glicko_adjust_value end
    local __gm_rank_adjust_value = decoder:geti64(37)
    if not PB_USE_DEFAULT_TABLE or __gm_rank_adjust_value ~= 0 then tb.gm_rank_adjust_value = __gm_rank_adjust_value end
    local __gm_medium_scheme_id = decoder:getu32(38)
    if not PB_USE_DEFAULT_TABLE or __gm_medium_scheme_id ~= 0 then tb.gm_medium_scheme_id = __gm_medium_scheme_id end
    local __last_bankrupt_timestamp = decoder:geti64(40)
    if not PB_USE_DEFAULT_TABLE or __last_bankrupt_timestamp ~= 0 then tb.last_bankrupt_timestamp = __last_bankrupt_timestamp end
    --unsupport field name:pb.SOLPlayerMatchCalInfo.model_is_scheme_list type:bool(8) num:41 repeat:1
    tb.model_kill_boss_list = decoder:geti32ary(42)
    tb.model_kill_player_list = decoder:geti32ary(43)
    tb.model_game_time_list = decoder:geti64ary(44)
    tb.model_total_loot_price_list = decoder:geti64ary(45)
    tb.model_price_change_list = decoder:geti64ary(46)
    --unsupport field name:pb.SOLPlayerMatchCalInfo.model_escaped_list type:bool(8) num:47 repeat:1
    tb.model_begin_game_price_list = decoder:getfloatary(48)
    tb.price_change = decoder:geti64ary(49)
    local __avg_begin_game_price = decoder:geti64(50)
    if not PB_USE_DEFAULT_TABLE or __avg_begin_game_price ~= 0 then tb.avg_begin_game_price = __avg_begin_game_price end
    local __lose_price = decoder:geti64(51)
    if not PB_USE_DEFAULT_TABLE or __lose_price ~= 0 then tb.lose_price = __lose_price end
    local __afk = decoder:geti64(52)
    if not PB_USE_DEFAULT_TABLE or __afk ~= 0 then tb.afk = __afk end
    local __last_lose_price = decoder:geti64(53)
    if not PB_USE_DEFAULT_TABLE or __last_lose_price ~= 0 then tb.last_lose_price = __last_lose_price end
    tb.scheme_cnt_tab = {}
    for k,v in pairs(decoder:getsubmsgary(54)) do
        tb.scheme_cnt_tab[k] = pb.pb_SOLPlayerMatchCalInfo_SchemeCntTabEntryDecode(v)
    end
    local __warmMatchCountToday = decoder:getu32(55)
    if not PB_USE_DEFAULT_TABLE or __warmMatchCountToday ~= 0 then tb.warmMatchCountToday = __warmMatchCountToday end
    local __isReachWarmLevel2Limit = decoder:getbool(56)
    if not PB_USE_DEFAULT_TABLE or __isReachWarmLevel2Limit ~= false then tb.isReachWarmLevel2Limit = __isReachWarmLevel2Limit end
    local __loss_contact_accepted = decoder:getbool(57)
    if not PB_USE_DEFAULT_TABLE or __loss_contact_accepted ~= false then tb.loss_contact_accepted = __loss_contact_accepted end
    local __continues_no_brick_match_cnt = decoder:geti32(58)
    if not PB_USE_DEFAULT_TABLE or __continues_no_brick_match_cnt ~= 0 then tb.continues_no_brick_match_cnt = __continues_no_brick_match_cnt end
    tb.cdp_stat_map = {}
    for k,v in pairs(decoder:getsubmsgary(59)) do
        tb.cdp_stat_map[k] = pb.pb_SOLPlayerMatchCalInfo_CdpStatMapEntryDecode(v)
    end
    local __enable_replay = decoder:getbool(70)
    if not PB_USE_DEFAULT_TABLE or __enable_replay ~= false then tb.enable_replay = __enable_replay end
    local __punish_replay_begin_time = decoder:geti64(71)
    if not PB_USE_DEFAULT_TABLE or __punish_replay_begin_time ~= 0 then tb.punish_replay_begin_time = __punish_replay_begin_time end
    local __avg_loot_price = decoder:geti64(100)
    if not PB_USE_DEFAULT_TABLE or __avg_loot_price ~= 0 then tb.avg_loot_price = __avg_loot_price end
    local __avg_loot_expected_price = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __avg_loot_expected_price ~= 0 then tb.avg_loot_expected_price = __avg_loot_expected_price end
    local __avg_contract_num = decoder:getfloat(102)
    if not PB_USE_DEFAULT_TABLE or __avg_contract_num ~= 0 then tb.avg_contract_num = __avg_contract_num end
    local __equip_price = decoder:geti64(103)
    if not PB_USE_DEFAULT_TABLE or __equip_price ~= 0 then tb.equip_price = __equip_price end
    local __avg_fighting_with_player_num = decoder:getfloat(105)
    if not PB_USE_DEFAULT_TABLE or __avg_fighting_with_player_num ~= 0 then tb.avg_fighting_with_player_num = __avg_fighting_with_player_num end
    local __avg_fighting_with_ai_num = decoder:getfloat(106)
    if not PB_USE_DEFAULT_TABLE or __avg_fighting_with_ai_num ~= 0 then tb.avg_fighting_with_ai_num = __avg_fighting_with_ai_num end
    local __Last24HourTotalChangePrice = decoder:geti64(107)
    if not PB_USE_DEFAULT_TABLE or __Last24HourTotalChangePrice ~= 0 then tb.Last24HourTotalChangePrice = __Last24HourTotalChangePrice end
    local __outfit_price = decoder:geti64(108)
    if not PB_USE_DEFAULT_TABLE or __outfit_price ~= 0 then tb.outfit_price = __outfit_price end
    local __equip_level = decoder:getdouble(109)
    if not PB_USE_DEFAULT_TABLE or __equip_level ~= 0 then tb.equip_level = __equip_level end
    local __easy_equip_isolation_for_newbie = decoder:getbool(110)
    if not PB_USE_DEFAULT_TABLE or __easy_equip_isolation_for_newbie ~= false then tb.easy_equip_isolation_for_newbie = __easy_equip_isolation_for_newbie end
    local __easy_equip_isolation_for_back = decoder:getbool(111)
    if not PB_USE_DEFAULT_TABLE or __easy_equip_isolation_for_back ~= false then tb.easy_equip_isolation_for_back = __easy_equip_isolation_for_back end
    local __player_back_type = decoder:getu32(114)
    if not PB_USE_DEFAULT_TABLE or __player_back_type ~= 0 then tb.player_back_type = __player_back_type end
    local __player_type = decoder:getu32(115)
    if not PB_USE_DEFAULT_TABLE or __player_type ~= 0 then tb.player_type = __player_type end
    local __tide_type = decoder:getu32(116)
    if not PB_USE_DEFAULT_TABLE or __tide_type ~= 0 then tb.tide_type = __tide_type end
    local __commonly_used_plat = decoder:getu32(117)
    if not PB_USE_DEFAULT_TABLE or __commonly_used_plat ~= 0 then tb.commonly_used_plat = __commonly_used_plat end
    local __drop_logic_id = decoder:geti64(130)
    if not PB_USE_DEFAULT_TABLE or __drop_logic_id ~= 0 then tb.drop_logic_id = __drop_logic_id end
    local __drop_buff_id = decoder:geti64(131)
    if not PB_USE_DEFAULT_TABLE or __drop_buff_id ~= 0 then tb.drop_buff_id = __drop_buff_id end
    tb.drop_counters = {}
    for k,v in pairs(decoder:getsubmsgary(132)) do
        tb.drop_counters[k] = pb.pb_MatchSOLDropCounterDecode(v)
    end
    return tb
end

function pb.pb_SOLPlayerMatchCalInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.course_id) then    encoder:addu32(2, tb.course_id)    end
    if(tb.scheme_id) then    encoder:addu32(3, tb.scheme_id)    end
    if(tb.skill_score) then    encoder:addi64(4, tb.skill_score)    end
    if(tb.gm_loot_style) then    encoder:addi32(5, tb.gm_loot_style)    end
    if(tb.glicko_rating) then    encoder:addfloat(6, tb.glicko_rating)    end
    if(tb.glicko_rating_dev) then    encoder:addfloat(7, tb.glicko_rating_dev)    end
    if(tb.deposit_asset) then    encoder:addu64(8, tb.deposit_asset)    end
    if(tb.sol_match_cnt) then    encoder:addi32(9, tb.sol_match_cnt)    end
    if(tb.streak_count) then    encoder:addi32(10, tb.streak_count)    end
    if(tb.streak_loot_count) then    encoder:addi64(11, tb.streak_loot_count)    end
    if(tb.sol_match_course_cnt) then    encoder:addi32(12, tb.sol_match_course_cnt)    end
    if(tb.sol_total_cnt) then    encoder:addi64(13, tb.sol_total_cnt)    end
    if(tb.gm_course_id) then    encoder:addu32(14, tb.gm_course_id)    end
    if(tb.gm_scheme_id) then    encoder:addu32(15, tb.gm_scheme_id)    end
    if(tb.gm_model_score) then    encoder:addfloat(16, tb.gm_model_score)    end
    if(tb.sol_course_difficult_cnt) then    encoder:addi32(17, tb.sol_course_difficult_cnt)    end
    if(tb.deposit_asset_no_virtual_prop) then    encoder:addi64(18, tb.deposit_asset_no_virtual_prop)    end
    if(tb.total_lose_match_count) then    encoder:addu32(19, tb.total_lose_match_count)    end
    if(tb.system_control_lose_count) then    encoder:addu32(20, tb.system_control_lose_count)    end
    if(tb.old_glicko_rating) then    encoder:addfloat(21, tb.old_glicko_rating)    end
    if(tb.old_glicko_rating_dev) then    encoder:addfloat(22, tb.old_glicko_rating_dev)    end
    if(tb.gm_force_course_id) then    encoder:addu32(23, tb.gm_force_course_id)    end
    if(tb.sol_rank_match_count) then    encoder:addi64(25, tb.sol_rank_match_count)    end
    if(tb.gm_course_type) then    encoder:addi32(26, tb.gm_course_type)    end
    if(tb.gm_force_course_type) then    encoder:addi32(27, tb.gm_force_course_type)    end
    if(tb.glicko_after_adjust) then    encoder:addfloat(28, tb.glicko_after_adjust)    end
    if(tb.rank_score_after_adjust) then    encoder:addi64(29, tb.rank_score_after_adjust)    end
    if(tb.gm_force_scheme_id) then    encoder:addu32(30, tb.gm_force_scheme_id)    end
    if(tb.gm_force_max_wait_time) then    encoder:addbool(31, tb.gm_force_max_wait_time)    end
    if(tb.glicko_adjust_value_by_rule) then    encoder:addfloat(32, tb.glicko_adjust_value_by_rule)    end
    if(tb.rank_adjust_value_by_rule) then    encoder:addi64(33, tb.rank_adjust_value_by_rule)    end
    if(tb.glicko_adjust_value_by_back) then    encoder:addfloat(34, tb.glicko_adjust_value_by_back)    end
    if(tb.rank_adjust_value_by_back) then    encoder:addi64(35, tb.rank_adjust_value_by_back)    end
    if(tb.gm_glicko_adjust_value) then    encoder:addfloat(36, tb.gm_glicko_adjust_value)    end
    if(tb.gm_rank_adjust_value) then    encoder:addi64(37, tb.gm_rank_adjust_value)    end
    if(tb.gm_medium_scheme_id) then    encoder:addu32(38, tb.gm_medium_scheme_id)    end
    if(tb.last_bankrupt_timestamp) then    encoder:addi64(40, tb.last_bankrupt_timestamp)    end
    if(tb.model_is_scheme_list) then    encoder:addbool(41, tb.model_is_scheme_list)    end
    if(tb.model_kill_boss_list) then    encoder:addi32(42, tb.model_kill_boss_list)    end
    if(tb.model_kill_player_list) then    encoder:addi32(43, tb.model_kill_player_list)    end
    if(tb.model_game_time_list) then    encoder:addi64(44, tb.model_game_time_list)    end
    if(tb.model_total_loot_price_list) then    encoder:addi64(45, tb.model_total_loot_price_list)    end
    if(tb.model_price_change_list) then    encoder:addi64(46, tb.model_price_change_list)    end
    if(tb.model_escaped_list) then    encoder:addbool(47, tb.model_escaped_list)    end
    if(tb.model_begin_game_price_list) then    encoder:addfloat(48, tb.model_begin_game_price_list)    end
    if(tb.price_change) then    encoder:addi64(49, tb.price_change)    end
    if(tb.avg_begin_game_price) then    encoder:addi64(50, tb.avg_begin_game_price)    end
    if(tb.lose_price) then    encoder:addi64(51, tb.lose_price)    end
    if(tb.afk) then    encoder:addi64(52, tb.afk)    end
    if(tb.last_lose_price) then    encoder:addi64(53, tb.last_lose_price)    end
    if(tb.scheme_cnt_tab) then
        for i=1,#(tb.scheme_cnt_tab) do
            pb.pb_SOLPlayerMatchCalInfo_SchemeCntTabEntryEncode(tb.scheme_cnt_tab[i], encoder:addsubmsg(54))
        end
    end
    if(tb.warmMatchCountToday) then    encoder:addu32(55, tb.warmMatchCountToday)    end
    if(tb.isReachWarmLevel2Limit) then    encoder:addbool(56, tb.isReachWarmLevel2Limit)    end
    if(tb.loss_contact_accepted) then    encoder:addbool(57, tb.loss_contact_accepted)    end
    if(tb.continues_no_brick_match_cnt) then    encoder:addi32(58, tb.continues_no_brick_match_cnt)    end
    if(tb.cdp_stat_map) then
        for i=1,#(tb.cdp_stat_map) do
            pb.pb_SOLPlayerMatchCalInfo_CdpStatMapEntryEncode(tb.cdp_stat_map[i], encoder:addsubmsg(59))
        end
    end
    if(tb.enable_replay) then    encoder:addbool(70, tb.enable_replay)    end
    if(tb.punish_replay_begin_time) then    encoder:addi64(71, tb.punish_replay_begin_time)    end
    if(tb.avg_loot_price) then    encoder:addi64(100, tb.avg_loot_price)    end
    if(tb.avg_loot_expected_price) then    encoder:addi64(101, tb.avg_loot_expected_price)    end
    if(tb.avg_contract_num) then    encoder:addfloat(102, tb.avg_contract_num)    end
    if(tb.equip_price) then    encoder:addi64(103, tb.equip_price)    end
    if(tb.avg_fighting_with_player_num) then    encoder:addfloat(105, tb.avg_fighting_with_player_num)    end
    if(tb.avg_fighting_with_ai_num) then    encoder:addfloat(106, tb.avg_fighting_with_ai_num)    end
    if(tb.Last24HourTotalChangePrice) then    encoder:addi64(107, tb.Last24HourTotalChangePrice)    end
    if(tb.outfit_price) then    encoder:addi64(108, tb.outfit_price)    end
    if(tb.equip_level) then    encoder:adddouble(109, tb.equip_level)    end
    if(tb.easy_equip_isolation_for_newbie) then    encoder:addbool(110, tb.easy_equip_isolation_for_newbie)    end
    if(tb.easy_equip_isolation_for_back) then    encoder:addbool(111, tb.easy_equip_isolation_for_back)    end
    if(tb.player_back_type) then    encoder:addu32(114, tb.player_back_type)    end
    if(tb.player_type) then    encoder:addu32(115, tb.player_type)    end
    if(tb.tide_type) then    encoder:addu32(116, tb.tide_type)    end
    if(tb.commonly_used_plat) then    encoder:addu32(117, tb.commonly_used_plat)    end
    if(tb.drop_logic_id) then    encoder:addi64(130, tb.drop_logic_id)    end
    if(tb.drop_buff_id) then    encoder:addi64(131, tb.drop_buff_id)    end
    if(tb.drop_counters) then
        for i=1,#(tb.drop_counters) do
            pb.pb_MatchSOLDropCounterEncode(tb.drop_counters[i], encoder:addsubmsg(132))
        end
    end
end

function pb.pb_ArenaPlayerMatchCalInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArenaPlayerMatchCalInfo) or {} 
    local __glicko_rating_pc = decoder:getdouble(1)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_pc ~= 0 then tb.glicko_rating_pc = __glicko_rating_pc end
    local __glicko_rating_dev_pc = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_dev_pc ~= 0 then tb.glicko_rating_dev_pc = __glicko_rating_dev_pc end
    local __glicko_rating_mobile = decoder:getdouble(3)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_mobile ~= 0 then tb.glicko_rating_mobile = __glicko_rating_mobile end
    local __glicko_rating_dev_mobile = decoder:getdouble(4)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_dev_mobile ~= 0 then tb.glicko_rating_dev_mobile = __glicko_rating_dev_mobile end
    local __glicko_rating_console = decoder:getdouble(11)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_console ~= 0 then tb.glicko_rating_console = __glicko_rating_console end
    local __glicko_rating_dev_console = decoder:getdouble(12)
    if not PB_USE_DEFAULT_TABLE or __glicko_rating_dev_console ~= 0 then tb.glicko_rating_dev_console = __glicko_rating_dev_console end
    local __winlose_elo_pc = decoder:getdouble(5)
    if not PB_USE_DEFAULT_TABLE or __winlose_elo_pc ~= 0 then tb.winlose_elo_pc = __winlose_elo_pc end
    local __winlose_elo_mobile = decoder:getdouble(6)
    if not PB_USE_DEFAULT_TABLE or __winlose_elo_mobile ~= 0 then tb.winlose_elo_mobile = __winlose_elo_mobile end
    local __winlose_elo_console = decoder:getdouble(13)
    if not PB_USE_DEFAULT_TABLE or __winlose_elo_console ~= 0 then tb.winlose_elo_console = __winlose_elo_console end
    local __fight_elo_pc = decoder:getdouble(7)
    if not PB_USE_DEFAULT_TABLE or __fight_elo_pc ~= 0 then tb.fight_elo_pc = __fight_elo_pc end
    local __fight_elo_mobile = decoder:getdouble(8)
    if not PB_USE_DEFAULT_TABLE or __fight_elo_mobile ~= 0 then tb.fight_elo_mobile = __fight_elo_mobile end
    local __fight_elo_console = decoder:getdouble(14)
    if not PB_USE_DEFAULT_TABLE or __fight_elo_console ~= 0 then tb.fight_elo_console = __fight_elo_console end
    local __fight_elo_with_hotstart_pc = decoder:getdouble(9)
    if not PB_USE_DEFAULT_TABLE or __fight_elo_with_hotstart_pc ~= 0 then tb.fight_elo_with_hotstart_pc = __fight_elo_with_hotstart_pc end
    local __fight_elo_with_hotstart_mobile = decoder:getdouble(10)
    if not PB_USE_DEFAULT_TABLE or __fight_elo_with_hotstart_mobile ~= 0 then tb.fight_elo_with_hotstart_mobile = __fight_elo_with_hotstart_mobile end
    local __fight_elo_with_hotstart_console = decoder:getdouble(15)
    if not PB_USE_DEFAULT_TABLE or __fight_elo_with_hotstart_console ~= 0 then tb.fight_elo_with_hotstart_console = __fight_elo_with_hotstart_console end
    local __sys_team_id = decoder:getu64(16)
    if not PB_USE_DEFAULT_TABLE or __sys_team_id ~= 0 then tb.sys_team_id = __sys_team_id end
    return tb
end

function pb.pb_ArenaPlayerMatchCalInfoEncode(tb, encoder)
    if(tb.glicko_rating_pc) then    encoder:adddouble(1, tb.glicko_rating_pc)    end
    if(tb.glicko_rating_dev_pc) then    encoder:adddouble(2, tb.glicko_rating_dev_pc)    end
    if(tb.glicko_rating_mobile) then    encoder:adddouble(3, tb.glicko_rating_mobile)    end
    if(tb.glicko_rating_dev_mobile) then    encoder:adddouble(4, tb.glicko_rating_dev_mobile)    end
    if(tb.glicko_rating_console) then    encoder:adddouble(11, tb.glicko_rating_console)    end
    if(tb.glicko_rating_dev_console) then    encoder:adddouble(12, tb.glicko_rating_dev_console)    end
    if(tb.winlose_elo_pc) then    encoder:adddouble(5, tb.winlose_elo_pc)    end
    if(tb.winlose_elo_mobile) then    encoder:adddouble(6, tb.winlose_elo_mobile)    end
    if(tb.winlose_elo_console) then    encoder:adddouble(13, tb.winlose_elo_console)    end
    if(tb.fight_elo_pc) then    encoder:adddouble(7, tb.fight_elo_pc)    end
    if(tb.fight_elo_mobile) then    encoder:adddouble(8, tb.fight_elo_mobile)    end
    if(tb.fight_elo_console) then    encoder:adddouble(14, tb.fight_elo_console)    end
    if(tb.fight_elo_with_hotstart_pc) then    encoder:adddouble(9, tb.fight_elo_with_hotstart_pc)    end
    if(tb.fight_elo_with_hotstart_mobile) then    encoder:adddouble(10, tb.fight_elo_with_hotstart_mobile)    end
    if(tb.fight_elo_with_hotstart_console) then    encoder:adddouble(15, tb.fight_elo_with_hotstart_console)    end
    if(tb.sys_team_id) then    encoder:addu64(16, tb.sys_team_id)    end
end

function pb.pb_SupplyNumsInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SupplyNumsInfo) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    return tb
end

function pb.pb_SupplyNumsInfoEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.num) then    encoder:addi32(2, tb.num)    end
end

function pb.pb_SupplyConfigInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SupplyConfigInfo) or {} 
    local __config_id = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __config_id ~= 0 then tb.config_id = __config_id end
    local __config_name = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __config_name ~= "" then tb.config_name = __config_name end
    local __config_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __config_type ~= 0 then tb.config_type = __config_type end
    local __modify_time = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __modify_time ~= 0 then tb.modify_time = __modify_time end
    tb.supplys = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.supplys[k] = pb.pb_SupplyNumsInfoDecode(v)
    end
    return tb
end

function pb.pb_SupplyConfigInfoEncode(tb, encoder)
    if(tb.config_id) then    encoder:addi32(1, tb.config_id)    end
    if(tb.config_name) then    encoder:addbuffer(2, tb.config_name)    end
    if(tb.config_type) then    encoder:addi32(3, tb.config_type)    end
    if(tb.modify_time) then    encoder:addu64(4, tb.modify_time)    end
    if(tb.supplys) then
        for i=1,#(tb.supplys) do
            pb.pb_SupplyNumsInfoEncode(tb.supplys[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_GspAchievementInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspAchievementInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    tb.score_map_key_array = decoder:getstrary(3)
    tb.score_map_value_array = decoder:getstrary(4)
    return tb
end

function pb.pb_GspAchievementInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.score_map_key_array) then    encoder:addstr(3, tb.score_map_key_array)    end
    if(tb.score_map_value_array) then    encoder:addstr(4, tb.score_map_value_array)    end
end

function pb.pb_GspMatchInfoNewDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspMatchInfoNew) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __map_id = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __match_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_subtype = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __match_mode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    local __team_size = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __team_size ~= 0 then tb.team_size = __team_size end
    local __start_time = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __player_count = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __player_count ~= 0 then tb.player_count = __player_count end
    local __ai_count = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __ai_count ~= 0 then tb.ai_count = __ai_count end
    local __boss_count = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __boss_count ~= 0 then tb.boss_count = __boss_count end
    local __is_guide = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __is_guide ~= false then tb.is_guide = __is_guide end
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(12))
    return tb
end

function pb.pb_GspMatchInfoNewEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.map_id) then    encoder:addi32(2, tb.map_id)    end
    if(tb.match_type) then    encoder:addi32(3, tb.match_type)    end
    if(tb.match_subtype) then    encoder:addi32(9, tb.match_subtype)    end
    if(tb.match_mode) then    encoder:addi32(4, tb.match_mode)    end
    if(tb.team_size) then    encoder:addi32(8, tb.team_size)    end
    if(tb.start_time) then    encoder:addu64(5, tb.start_time)    end
    if(tb.player_count) then    encoder:addi32(6, tb.player_count)    end
    if(tb.ai_count) then    encoder:addi32(7, tb.ai_count)    end
    if(tb.boss_count) then    encoder:addi32(10, tb.boss_count)    end
    if(tb.is_guide) then    encoder:addbool(11, tb.is_guide)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(12))    end
end

function pb.pb_GspTeamInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspTeamInfo) or {} 
    local __team_id = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __team_id ~= 0 then tb.team_id = __team_id end
    local __teammate_count = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __teammate_count ~= 0 then tb.teammate_count = __teammate_count end
    local __team_rank = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __team_rank ~= 0 then tb.team_rank = __team_rank end
    tb.team_achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.team_achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    return tb
end

function pb.pb_GspTeamInfoEncode(tb, encoder)
    if(tb.team_id) then    encoder:addi32(4, tb.team_id)    end
    if(tb.teammate_count) then    encoder:addi32(1, tb.teammate_count)    end
    if(tb.team_rank) then    encoder:addi32(2, tb.team_rank)    end
    if(tb.team_achievement_array) then
        for i=1,#(tb.team_achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.team_achievement_array[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_GspPlayerBasicInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayerBasicInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __game_nick = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __game_nick ~= "" then tb.game_nick = __game_nick end
    local __gender = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __gender ~= 0 then tb.gender = __gender end
    local __avatar = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __avatar ~= "" then tb.avatar = __avatar end
    local __background = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __background ~= "" then tb.background = __background end
    local __level = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __grade = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __grade ~= 0 then tb.grade = __grade end
    local __is_scav = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __is_scav ~= false then tb.is_scav = __is_scav end
    local __team_seqno = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __team_seqno ~= 0 then tb.team_seqno = __team_seqno end
    local __has_title = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __has_title ~= false then tb.has_title = __has_title end
    local __title = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __title ~= 0 then tb.title = __title end
    local __be_praised_num = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __be_praised_num ~= 0 then tb.be_praised_num = __be_praised_num end
    local __rank_match_score = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __plat_id = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __plat_id ~= 0 then tb.plat_id = __plat_id end
    local __account_type = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __account_type ~= 0 then tb.account_type = __account_type end
    return tb
end

function pb.pb_GspPlayerBasicInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.game_nick) then    encoder:addstr(2, tb.game_nick)    end
    if(tb.gender) then    encoder:addi32(3, tb.gender)    end
    if(tb.avatar) then    encoder:addstr(4, tb.avatar)    end
    if(tb.background) then    encoder:addstr(5, tb.background)    end
    if(tb.level) then    encoder:addi32(6, tb.level)    end
    if(tb.grade) then    encoder:addi32(7, tb.grade)    end
    if(tb.is_scav) then    encoder:addbool(11, tb.is_scav)    end
    if(tb.team_seqno) then    encoder:addi32(8, tb.team_seqno)    end
    if(tb.has_title) then    encoder:addbool(10, tb.has_title)    end
    if(tb.title) then    encoder:addu64(9, tb.title)    end
    if(tb.be_praised_num) then    encoder:addi32(12, tb.be_praised_num)    end
    if(tb.rank_match_score) then    encoder:addi64(13, tb.rank_match_score)    end
    if(tb.plat_id) then    encoder:addi32(14, tb.plat_id)    end
    if(tb.account_type) then    encoder:addi32(15, tb.account_type)    end
end

function pb.pb_GspAIBasicInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspAIBasicInfo) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __game_nick = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __game_nick ~= "" then tb.game_nick = __game_nick end
    local __gender = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __gender ~= 0 then tb.gender = __gender end
    tb.tag_array = decoder:getstrary(5)
    local __level = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    local __ai_lab_level = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __ai_lab_level ~= 0 then tb.ai_lab_level = __ai_lab_level end
    local __rank_match_score = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    return tb
end

function pb.pb_GspAIBasicInfoEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.game_nick) then    encoder:addstr(2, tb.game_nick)    end
    if(tb.gender) then    encoder:addi32(3, tb.gender)    end
    if(tb.tag_array) then    encoder:addstr(5, tb.tag_array)    end
    if(tb.level) then    encoder:addi32(6, tb.level)    end
    if(tb.ai_lab_level) then    encoder:addi32(7, tb.ai_lab_level)    end
    if(tb.rank_match_score) then    encoder:addi64(8, tb.rank_match_score)    end
end

function pb.pb_GspPlayerPositionDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayerPosition) or {} 
    local __x = decoder:getdouble(1)
    if not PB_USE_DEFAULT_TABLE or __x ~= 0 then tb.x = __x end
    local __y = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __y ~= 0 then tb.y = __y end
    local __z = decoder:getdouble(3)
    if not PB_USE_DEFAULT_TABLE or __z ~= 0 then tb.z = __z end
    return tb
end

function pb.pb_GspPlayerPositionEncode(tb, encoder)
    if(tb.x) then    encoder:adddouble(1, tb.x)    end
    if(tb.y) then    encoder:adddouble(2, tb.y)    end
    if(tb.z) then    encoder:adddouble(3, tb.z)    end
end

function pb.pb_GspDamageInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspDamageInfo) or {} 
    local __timestamp = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_GspPlayerPositionDecode(decoder:getsubmsg(8))
    local __equipment_recorded = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __equipment_recorded ~= false then tb.equipment_recorded = __equipment_recorded end
    tb.equipment = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.equipment[k] = pb.pb_EquipPositionDecode(v)
    end
    local __equipment_price = decoder:getdouble(11)
    if not PB_USE_DEFAULT_TABLE or __equipment_price ~= 0 then tb.equipment_price = __equipment_price end
    local __has_weapon = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __has_weapon ~= false then tb.has_weapon = __has_weapon end
    local __weapon = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __weapon ~= 0 then tb.weapon = __weapon end
    tb.weapon_prop = pb.pb_PropInfoDecode(decoder:getsubmsg(13))
    local __has_ammo = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __has_ammo ~= false then tb.has_ammo = __has_ammo end
    local __ammo = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __ammo ~= 0 then tb.ammo = __ammo end
    local __hit_distance = decoder:getfloat(16)
    if not PB_USE_DEFAULT_TABLE or __hit_distance ~= 0 then tb.hit_distance = __hit_distance end
    local __damage = decoder:getdouble(5)
    if not PB_USE_DEFAULT_TABLE or __damage ~= 0 then tb.damage = __damage end
    local __body_part = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __body_part ~= 0 then tb.body_part = __body_part end
    local __enemy_equipment_id = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __enemy_equipment_id ~= 0 then tb.enemy_equipment_id = __enemy_equipment_id end
    local __health_max = decoder:geti32(15)
    if not PB_USE_DEFAULT_TABLE or __health_max ~= 0 then tb.health_max = __health_max end
    local __health = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __health ~= 0 then tb.health = __health end
    return tb
end

function pb.pb_GspDamageInfoEncode(tb, encoder)
    if(tb.timestamp) then    encoder:addi32(7, tb.timestamp)    end
    if(tb.position) then    pb.pb_GspPlayerPositionEncode(tb.position, encoder:addsubmsg(8))    end
    if(tb.equipment_recorded) then    encoder:addbool(9, tb.equipment_recorded)    end
    if(tb.equipment) then
        for i=1,#(tb.equipment) do
            pb.pb_EquipPositionEncode(tb.equipment[i], encoder:addsubmsg(1))
        end
    end
    if(tb.equipment_price) then    encoder:adddouble(11, tb.equipment_price)    end
    if(tb.has_weapon) then    encoder:addbool(10, tb.has_weapon)    end
    if(tb.weapon) then    encoder:addu64(2, tb.weapon)    end
    if(tb.weapon_prop) then    pb.pb_PropInfoEncode(tb.weapon_prop, encoder:addsubmsg(13))    end
    if(tb.has_ammo) then    encoder:addbool(3, tb.has_ammo)    end
    if(tb.ammo) then    encoder:addu64(4, tb.ammo)    end
    if(tb.hit_distance) then    encoder:addfloat(16, tb.hit_distance)    end
    if(tb.damage) then    encoder:adddouble(5, tb.damage)    end
    if(tb.body_part) then    encoder:addi32(6, tb.body_part)    end
    if(tb.enemy_equipment_id) then    encoder:addu64(12, tb.enemy_equipment_id)    end
    if(tb.health_max) then    encoder:addi32(15, tb.health_max)    end
    if(tb.health) then    encoder:addi32(14, tb.health)    end
end

function pb.pb_GspAccidentInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspAccidentInfo) or {} 
    local __timestamp = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_GspPlayerPositionDecode(decoder:getsubmsg(2))
    local __accident_type = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __accident_type ~= 0 then tb.accident_type = __accident_type end
    local __body_part = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __body_part ~= 0 then tb.body_part = __body_part end
    return tb
end

function pb.pb_GspAccidentInfoEncode(tb, encoder)
    if(tb.timestamp) then    encoder:addi32(1, tb.timestamp)    end
    if(tb.position) then    pb.pb_GspPlayerPositionEncode(tb.position, encoder:addsubmsg(2))    end
    if(tb.accident_type) then    encoder:addi32(3, tb.accident_type)    end
    if(tb.body_part) then    encoder:addi32(4, tb.body_part)    end
end

function pb.pb_GspPlayerDeathInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayerDeathInfo) or {} 
    local __reason = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __timestamp = decoder:geti32(14)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_GspPlayerPositionDecode(decoder:getsubmsg(15))
    tb.damage = pb.pb_GspDamageInfoDecode(decoder:getsubmsg(9))
    tb.accident = pb.pb_GspAccidentInfoDecode(decoder:getsubmsg(13))
    tb.player = pb.pb_GspPlayerBasicInfoDecode(decoder:getsubmsg(10))
    tb.ai = pb.pb_GspAIBasicInfoDecode(decoder:getsubmsg(11))
    tb.boss = pb.pb_GspAIBasicInfoDecode(decoder:getsubmsg(12))
    return tb
end

function pb.pb_GspPlayerDeathInfoEncode(tb, encoder)
    if(tb.reason) then    encoder:addi32(1, tb.reason)    end
    if(tb.timestamp) then    encoder:addi32(14, tb.timestamp)    end
    if(tb.position) then    pb.pb_GspPlayerPositionEncode(tb.position, encoder:addsubmsg(15))    end
    if(tb.damage) then    pb.pb_GspDamageInfoEncode(tb.damage, encoder:addsubmsg(9))    end
    if(tb.accident) then    pb.pb_GspAccidentInfoEncode(tb.accident, encoder:addsubmsg(13))    end
    if(tb.player) then    pb.pb_GspPlayerBasicInfoEncode(tb.player, encoder:addsubmsg(10))    end
    if(tb.ai) then    pb.pb_GspAIBasicInfoEncode(tb.ai, encoder:addsubmsg(11))    end
    if(tb.boss) then    pb.pb_GspAIBasicInfoEncode(tb.boss, encoder:addsubmsg(12))    end
end

function pb.pb_GspMatchEventDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspMatchEvent) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __timestamp = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_GspPlayerPositionDecode(decoder:getsubmsg(3))
    tb.param_map_key_array = decoder:getstrary(9)
    tb.param_map_value_array = decoder:getstrary(10)
    local __kill_count = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __kill_count ~= 0 then tb.kill_count = __kill_count end
    local __total_price = decoder:getdouble(6)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __produced_price = decoder:getdouble(7)
    if not PB_USE_DEFAULT_TABLE or __produced_price ~= 0 then tb.produced_price = __produced_price end
    local __KM_traveled = decoder:getfloat(8)
    if not PB_USE_DEFAULT_TABLE or __KM_traveled ~= 0 then tb.KM_traveled = __KM_traveled end
    return tb
end

function pb.pb_GspMatchEventEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.timestamp) then    encoder:addi32(2, tb.timestamp)    end
    if(tb.position) then    pb.pb_GspPlayerPositionEncode(tb.position, encoder:addsubmsg(3))    end
    if(tb.param_map_key_array) then    encoder:addstr(9, tb.param_map_key_array)    end
    if(tb.param_map_value_array) then    encoder:addstr(10, tb.param_map_value_array)    end
    if(tb.kill_count) then    encoder:addi32(5, tb.kill_count)    end
    if(tb.total_price) then    encoder:adddouble(6, tb.total_price)    end
    if(tb.produced_price) then    encoder:adddouble(7, tb.produced_price)    end
    if(tb.KM_traveled) then    encoder:addfloat(8, tb.KM_traveled)    end
end

function pb.pb_GspPlayerKillInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayerKillInfo) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __timestamp = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    tb.position = pb.pb_GspPlayerPositionDecode(decoder:getsubmsg(3))
    tb.damage = pb.pb_GspDamageInfoDecode(decoder:getsubmsg(14))
    local __total_damage = decoder:getdouble(15)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __enemy_type = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __enemy_type ~= 0 then tb.enemy_type = __enemy_type end
    tb.player = pb.pb_GspPlayerBasicInfoDecode(decoder:getsubmsg(10))
    tb.ai = pb.pb_GspAIBasicInfoDecode(decoder:getsubmsg(11))
    tb.boss = pb.pb_GspAIBasicInfoDecode(decoder:getsubmsg(12))
    tb.damage_list = {}
    for k,v in pairs(decoder:getsubmsgary(20)) do
        tb.damage_list[k] = pb.pb_GspDamageInfoDecode(v)
    end
    tb.enemy_damage_list = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.enemy_damage_list[k] = pb.pb_GspDamageInfoDecode(v)
    end
    return tb
end

function pb.pb_GspPlayerKillInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.timestamp) then    encoder:addi32(2, tb.timestamp)    end
    if(tb.position) then    pb.pb_GspPlayerPositionEncode(tb.position, encoder:addsubmsg(3))    end
    if(tb.damage) then    pb.pb_GspDamageInfoEncode(tb.damage, encoder:addsubmsg(14))    end
    if(tb.total_damage) then    encoder:adddouble(15, tb.total_damage)    end
    if(tb.enemy_type) then    encoder:addi32(7, tb.enemy_type)    end
    if(tb.player) then    pb.pb_GspPlayerBasicInfoEncode(tb.player, encoder:addsubmsg(10))    end
    if(tb.ai) then    pb.pb_GspAIBasicInfoEncode(tb.ai, encoder:addsubmsg(11))    end
    if(tb.boss) then    pb.pb_GspAIBasicInfoEncode(tb.boss, encoder:addsubmsg(12))    end
    if(tb.damage_list) then
        for i=1,#(tb.damage_list) do
            pb.pb_GspDamageInfoEncode(tb.damage_list[i], encoder:addsubmsg(20))
        end
    end
    if(tb.enemy_damage_list) then
        for i=1,#(tb.enemy_damage_list) do
            pb.pb_GspDamageInfoEncode(tb.enemy_damage_list[i], encoder:addsubmsg(21))
        end
    end
end

function pb.pb_GspPlayerMatchStatNewDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayerMatchStatNew) or {} 
    local __blood_loss = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __blood_loss ~= 0 then tb.blood_loss = __blood_loss end
    local __HP_healed = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __HP_healed ~= 0 then tb.HP_healed = __HP_healed end
    local __dehydration_times = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __dehydration_times ~= 0 then tb.dehydration_times = __dehydration_times end
    local __drink_consumed = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __drink_consumed ~= 0 then tb.drink_consumed = __drink_consumed end
    local __food_consumed = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __food_consumed ~= 0 then tb.food_consumed = __food_consumed end
    local __KM_traveled = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __KM_traveled ~= 0 then tb.KM_traveled = __KM_traveled end
    local __bodies_looted = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __bodies_looted ~= 0 then tb.bodies_looted = __bodies_looted end
    local __weapons_found = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __weapons_found ~= 0 then tb.weapons_found = __weapons_found end
    local __accessories_found = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __accessories_found ~= 0 then tb.accessories_found = __accessories_found end
    local __provisions_found = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __provisions_found ~= 0 then tb.provisions_found = __provisions_found end
    local __rescue_count = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __revive_count = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __revive_count ~= 0 then tb.revive_count = __revive_count end
    local __unlock_times = decoder:geti32(17)
    if not PB_USE_DEFAULT_TABLE or __unlock_times ~= 0 then tb.unlock_times = __unlock_times end
    local __total_damage = decoder:getdouble(13)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __total_price = decoder:getdouble(14)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __safebox_price = decoder:getdouble(18)
    if not PB_USE_DEFAULT_TABLE or __safebox_price ~= 0 then tb.safebox_price = __safebox_price end
    local __produced_price = decoder:getdouble(15)
    if not PB_USE_DEFAULT_TABLE or __produced_price ~= 0 then tb.produced_price = __produced_price end
    local __actual_produced_price = decoder:getdouble(16)
    if not PB_USE_DEFAULT_TABLE or __actual_produced_price ~= 0 then tb.actual_produced_price = __actual_produced_price end
    local __resurgence_count = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    return tb
end

function pb.pb_GspPlayerMatchStatNewEncode(tb, encoder)
    if(tb.blood_loss) then    encoder:addi32(1, tb.blood_loss)    end
    if(tb.HP_healed) then    encoder:addi32(2, tb.HP_healed)    end
    if(tb.dehydration_times) then    encoder:addi32(3, tb.dehydration_times)    end
    if(tb.drink_consumed) then    encoder:addi32(4, tb.drink_consumed)    end
    if(tb.food_consumed) then    encoder:addi32(5, tb.food_consumed)    end
    if(tb.KM_traveled) then    encoder:addfloat(6, tb.KM_traveled)    end
    if(tb.bodies_looted) then    encoder:addi32(7, tb.bodies_looted)    end
    if(tb.weapons_found) then    encoder:addi32(8, tb.weapons_found)    end
    if(tb.accessories_found) then    encoder:addi32(9, tb.accessories_found)    end
    if(tb.provisions_found) then    encoder:addi32(10, tb.provisions_found)    end
    if(tb.rescue_count) then    encoder:addi32(11, tb.rescue_count)    end
    if(tb.revive_count) then    encoder:addi32(12, tb.revive_count)    end
    if(tb.unlock_times) then    encoder:addi32(17, tb.unlock_times)    end
    if(tb.total_damage) then    encoder:adddouble(13, tb.total_damage)    end
    if(tb.total_price) then    encoder:adddouble(14, tb.total_price)    end
    if(tb.safebox_price) then    encoder:adddouble(18, tb.safebox_price)    end
    if(tb.produced_price) then    encoder:adddouble(15, tb.produced_price)    end
    if(tb.actual_produced_price) then    encoder:adddouble(16, tb.actual_produced_price)    end
    if(tb.resurgence_count) then    encoder:addi32(27, tb.resurgence_count)    end
end

function pb.pb_GspPlayingPlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayingPlayerStatus) or {} 
    return tb
end

function pb.pb_GspPlayingPlayerStatusEncode(tb, encoder)
end

function pb.pb_GspEndGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspEndGamePlayerStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __extraction_point = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __extraction_point ~= 0 then tb.extraction_point = __extraction_point end
    tb.death_info = pb.pb_GspPlayerDeathInfoDecode(decoder:getsubmsg(3))
    local __extraction_location = decoder:getstr(25)
    if not PB_USE_DEFAULT_TABLE or __extraction_location ~= "" then tb.extraction_location = __extraction_location end
    local __play_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __has_main_weapon = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __has_main_weapon ~= false then tb.has_main_weapon = __has_main_weapon end
    local __main_weapon = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __main_weapon ~= 0 then tb.main_weapon = __main_weapon end
    tb.match_log = {}
    for k,v in pairs(decoder:getsubmsgary(18)) do
        tb.match_log[k] = pb.pb_GspMatchEventDecode(v)
    end
    tb.match_stat = pb.pb_GspPlayerMatchStatNewDecode(decoder:getsubmsg(19))
    local __performance_score = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.attr_array = {}
    for k,v in pairs(decoder:getsubmsgary(10)) do
        tb.attr_array[k] = pb.pb_DSAttrValueDecode(v)
    end
    tb.buff_array = {}
    for k,v in pairs(decoder:getsubmsgary(11)) do
        tb.buff_array[k] = pb.pb_AttrBuffDecode(v)
    end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.carry_out_props = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.carry_out_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    tb.C_price_values = decoder:geti32ary(16)
    tb.season_change_info = pb.pb_SeasonChangeInfoDecode(decoder:getsubmsg(17))
    tb.shoot_down_by_enemy_array = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.shoot_down_by_enemy_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    local __success_escape_index = decoder:getfloat(22)
    if not PB_USE_DEFAULT_TABLE or __success_escape_index ~= 0 then tb.success_escape_index = __success_escape_index end
    tb.weapon_change = {}
    for k,v in pairs(decoder:getsubmsgary(23)) do
        tb.weapon_change[k] = pb.pb_WeaponChangeDecode(v)
    end
    tb.carry_out_health_list = {}
    for k,v in pairs(decoder:getsubmsgary(24)) do
        tb.carry_out_health_list[k] = pb.pb_EquipHealthDecode(v)
    end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(26))
    local __blue_print_special_id = decoder:getu64(27)
    if not PB_USE_DEFAULT_TABLE or __blue_print_special_id ~= 0 then tb.blue_print_special_id = __blue_print_special_id end
    local __blue_print_type = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __blue_print_type ~= 0 then tb.blue_print_type = __blue_print_type end
    local __blue_print_price = decoder:geti64(29)
    if not PB_USE_DEFAULT_TABLE or __blue_print_price ~= 0 then tb.blue_print_price = __blue_print_price end
    local __contract_quest_price = decoder:geti64(30)
    if not PB_USE_DEFAULT_TABLE or __contract_quest_price ~= 0 then tb.contract_quest_price = __contract_quest_price end
    local __carry_out_new_props_price = decoder:geti64(31)
    if not PB_USE_DEFAULT_TABLE or __carry_out_new_props_price ~= 0 then tb.carry_out_new_props_price = __carry_out_new_props_price end
    local __friend_add_exp_buf = decoder:getdouble(32)
    if not PB_USE_DEFAULT_TABLE or __friend_add_exp_buf ~= 0 then tb.friend_add_exp_buf = __friend_add_exp_buf end
    local __safehouse_add_exp_buf = decoder:getdouble(33)
    if not PB_USE_DEFAULT_TABLE or __safehouse_add_exp_buf ~= 0 then tb.safehouse_add_exp_buf = __safehouse_add_exp_buf end
    local __cost_price = decoder:geti32(34)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    local __carry_out_profit_price = decoder:geti32(35)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    local __account_add_exp_buf = decoder:getdouble(36)
    if not PB_USE_DEFAULT_TABLE or __account_add_exp_buf ~= 0 then tb.account_add_exp_buf = __account_add_exp_buf end
    local __cybercafe_settlement_buff = decoder:getdouble(37)
    if not PB_USE_DEFAULT_TABLE or __cybercafe_settlement_buff ~= 0 then tb.cybercafe_settlement_buff = __cybercafe_settlement_buff end
    local __carry_out_without_teammate_price = decoder:geti64(38)
    if not PB_USE_DEFAULT_TABLE or __carry_out_without_teammate_price ~= 0 then tb.carry_out_without_teammate_price = __carry_out_without_teammate_price end
    local __university_settlement_buff = decoder:getdouble(39)
    if not PB_USE_DEFAULT_TABLE or __university_settlement_buff ~= 0 then tb.university_settlement_buff = __university_settlement_buff end
    return tb
end

function pb.pb_GspEndGamePlayerStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.extraction_point) then    encoder:addi32(2, tb.extraction_point)    end
    if(tb.death_info) then    pb.pb_GspPlayerDeathInfoEncode(tb.death_info, encoder:addsubmsg(3))    end
    if(tb.extraction_location) then    encoder:addstr(25, tb.extraction_location)    end
    if(tb.play_time) then    encoder:addi32(4, tb.play_time)    end
    if(tb.has_main_weapon) then    encoder:addbool(20, tb.has_main_weapon)    end
    if(tb.main_weapon) then    encoder:addu64(5, tb.main_weapon)    end
    if(tb.match_log) then
        for i=1,#(tb.match_log) do
            pb.pb_GspMatchEventEncode(tb.match_log[i], encoder:addsubmsg(18))
        end
    end
    if(tb.match_stat) then    pb.pb_GspPlayerMatchStatNewEncode(tb.match_stat, encoder:addsubmsg(19))    end
    if(tb.performance_score) then    encoder:addfloat(6, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(9, tb.individual_rank)    end
    if(tb.attr_array) then
        for i=1,#(tb.attr_array) do
            pb.pb_DSAttrValueEncode(tb.attr_array[i], encoder:addsubmsg(10))
        end
    end
    if(tb.buff_array) then
        for i=1,#(tb.buff_array) do
            pb.pb_AttrBuffEncode(tb.buff_array[i], encoder:addsubmsg(11))
        end
    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(12))
        end
    end
    if(tb.carry_out_props) then
        for i=1,#(tb.carry_out_props) do
            pb.pb_EquipPositionEncode(tb.carry_out_props[i], encoder:addsubmsg(13))
        end
    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(14))
        end
    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(15))
        end
    end
    if(tb.C_price_values) then    encoder:addi32(16, tb.C_price_values)    end
    if(tb.season_change_info) then    pb.pb_SeasonChangeInfoEncode(tb.season_change_info, encoder:addsubmsg(17))    end
    if(tb.shoot_down_by_enemy_array) then
        for i=1,#(tb.shoot_down_by_enemy_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.shoot_down_by_enemy_array[i], encoder:addsubmsg(21))
        end
    end
    if(tb.success_escape_index) then    encoder:addfloat(22, tb.success_escape_index)    end
    if(tb.weapon_change) then
        for i=1,#(tb.weapon_change) do
            pb.pb_WeaponChangeEncode(tb.weapon_change[i], encoder:addsubmsg(23))
        end
    end
    if(tb.carry_out_health_list) then
        for i=1,#(tb.carry_out_health_list) do
            pb.pb_EquipHealthEncode(tb.carry_out_health_list[i], encoder:addsubmsg(24))
        end
    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(26))    end
    if(tb.blue_print_special_id) then    encoder:addu64(27, tb.blue_print_special_id)    end
    if(tb.blue_print_type) then    encoder:addi32(28, tb.blue_print_type)    end
    if(tb.blue_print_price) then    encoder:addi64(29, tb.blue_print_price)    end
    if(tb.contract_quest_price) then    encoder:addi64(30, tb.contract_quest_price)    end
    if(tb.carry_out_new_props_price) then    encoder:addi64(31, tb.carry_out_new_props_price)    end
    if(tb.friend_add_exp_buf) then    encoder:adddouble(32, tb.friend_add_exp_buf)    end
    if(tb.safehouse_add_exp_buf) then    encoder:adddouble(33, tb.safehouse_add_exp_buf)    end
    if(tb.cost_price) then    encoder:addi32(34, tb.cost_price)    end
    if(tb.carry_out_profit_price) then    encoder:addi32(35, tb.carry_out_profit_price)    end
    if(tb.account_add_exp_buf) then    encoder:adddouble(36, tb.account_add_exp_buf)    end
    if(tb.cybercafe_settlement_buff) then    encoder:adddouble(37, tb.cybercafe_settlement_buff)    end
    if(tb.carry_out_without_teammate_price) then    encoder:addi64(38, tb.carry_out_without_teammate_price)    end
    if(tb.university_settlement_buff) then    encoder:adddouble(39, tb.university_settlement_buff)    end
end

function pb.pb_GspQuitGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspQuitGamePlayerStatus) or {} 
    local __reason = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    tb.season_change_info = pb.pb_SeasonChangeInfoDecode(decoder:getsubmsg(2))
    local __play_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __has_main_weapon = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __has_main_weapon ~= false then tb.has_main_weapon = __has_main_weapon end
    local __main_weapon = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __main_weapon ~= 0 then tb.main_weapon = __main_weapon end
    tb.match_stat = pb.pb_GspPlayerMatchStatNewDecode(decoder:getsubmsg(19))
    local __performance_score = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.carry_out_props = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.carry_out_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    tb.weapon_change = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.weapon_change[k] = pb.pb_WeaponChangeDecode(v)
    end
    tb.carry_out_health_list = {}
    for k,v in pairs(decoder:getsubmsgary(22)) do
        tb.carry_out_health_list[k] = pb.pb_EquipHealthDecode(v)
    end
    local __friend_add_exp_buf = decoder:getdouble(23)
    if not PB_USE_DEFAULT_TABLE or __friend_add_exp_buf ~= 0 then tb.friend_add_exp_buf = __friend_add_exp_buf end
    local __safehouse_add_exp_buf = decoder:getdouble(24)
    if not PB_USE_DEFAULT_TABLE or __safehouse_add_exp_buf ~= 0 then tb.safehouse_add_exp_buf = __safehouse_add_exp_buf end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(25))
    local __rescue_count = decoder:geti32(26)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __resurgence_count = decoder:geti32(27)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    local __carry_out_profit_price = decoder:geti32(28)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    local __cost_price = decoder:geti32(29)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    local __account_add_exp_buf = decoder:getdouble(30)
    if not PB_USE_DEFAULT_TABLE or __account_add_exp_buf ~= 0 then tb.account_add_exp_buf = __account_add_exp_buf end
    local __cybercafe_settlement_buff = decoder:getdouble(31)
    if not PB_USE_DEFAULT_TABLE or __cybercafe_settlement_buff ~= 0 then tb.cybercafe_settlement_buff = __cybercafe_settlement_buff end
    local __carry_out_without_teammate_price = decoder:geti64(32)
    if not PB_USE_DEFAULT_TABLE or __carry_out_without_teammate_price ~= 0 then tb.carry_out_without_teammate_price = __carry_out_without_teammate_price end
    local __university_settlement_buff = decoder:getdouble(38)
    if not PB_USE_DEFAULT_TABLE or __university_settlement_buff ~= 0 then tb.university_settlement_buff = __university_settlement_buff end
    return tb
end

function pb.pb_GspQuitGamePlayerStatusEncode(tb, encoder)
    if(tb.reason) then    encoder:addi32(1, tb.reason)    end
    if(tb.season_change_info) then    pb.pb_SeasonChangeInfoEncode(tb.season_change_info, encoder:addsubmsg(2))    end
    if(tb.play_time) then    encoder:addi32(4, tb.play_time)    end
    if(tb.has_main_weapon) then    encoder:addbool(20, tb.has_main_weapon)    end
    if(tb.main_weapon) then    encoder:addu64(5, tb.main_weapon)    end
    if(tb.match_stat) then    pb.pb_GspPlayerMatchStatNewEncode(tb.match_stat, encoder:addsubmsg(19))    end
    if(tb.performance_score) then    encoder:addfloat(6, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(9, tb.individual_rank)    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(12))
        end
    end
    if(tb.carry_out_props) then
        for i=1,#(tb.carry_out_props) do
            pb.pb_EquipPositionEncode(tb.carry_out_props[i], encoder:addsubmsg(13))
        end
    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(14))
        end
    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(15))
        end
    end
    if(tb.weapon_change) then
        for i=1,#(tb.weapon_change) do
            pb.pb_WeaponChangeEncode(tb.weapon_change[i], encoder:addsubmsg(21))
        end
    end
    if(tb.carry_out_health_list) then
        for i=1,#(tb.carry_out_health_list) do
            pb.pb_EquipHealthEncode(tb.carry_out_health_list[i], encoder:addsubmsg(22))
        end
    end
    if(tb.friend_add_exp_buf) then    encoder:adddouble(23, tb.friend_add_exp_buf)    end
    if(tb.safehouse_add_exp_buf) then    encoder:adddouble(24, tb.safehouse_add_exp_buf)    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(25))    end
    if(tb.rescue_count) then    encoder:addi32(26, tb.rescue_count)    end
    if(tb.resurgence_count) then    encoder:addi32(27, tb.resurgence_count)    end
    if(tb.carry_out_profit_price) then    encoder:addi32(28, tb.carry_out_profit_price)    end
    if(tb.cost_price) then    encoder:addi32(29, tb.cost_price)    end
    if(tb.account_add_exp_buf) then    encoder:adddouble(30, tb.account_add_exp_buf)    end
    if(tb.cybercafe_settlement_buff) then    encoder:adddouble(31, tb.cybercafe_settlement_buff)    end
    if(tb.carry_out_without_teammate_price) then    encoder:addi64(32, tb.carry_out_without_teammate_price)    end
    if(tb.university_settlement_buff) then    encoder:adddouble(38, tb.university_settlement_buff)    end
end

function pb.pb_RankCurrencyChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankCurrencyChange) or {} 
    local __reason = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    return tb
end

function pb.pb_RankCurrencyChangeEncode(tb, encoder)
    if(tb.reason) then    encoder:addu32(1, tb.reason)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
end

function pb.pb_RankCurrencyResultDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankCurrencyResult) or {} 
    local __coefficient = decoder:getdouble(1)
    if not PB_USE_DEFAULT_TABLE or __coefficient ~= 0 then tb.coefficient = __coefficient end
    local __raw_value = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __raw_value ~= 0 then tb.raw_value = __raw_value end
    local __conf_limit_pre_match = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __conf_limit_pre_match ~= 0 then tb.conf_limit_pre_match = __conf_limit_pre_match end
    local __pre_weekly_value = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __pre_weekly_value ~= 0 then tb.pre_weekly_value = __pre_weekly_value end
    local __conf_limit_pre_weekly = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __conf_limit_pre_weekly ~= 0 then tb.conf_limit_pre_weekly = __conf_limit_pre_weekly end
    local __conf_daily_first_escape = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __conf_daily_first_escape ~= 0 then tb.conf_daily_first_escape = __conf_daily_first_escape end
    local __actual_value = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __actual_value ~= 0 then tb.actual_value = __actual_value end
    tb.currency_changes = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.currency_changes[k] = pb.pb_RankCurrencyChangeDecode(v)
    end
    local __is_daily_first_escape = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __is_daily_first_escape ~= false then tb.is_daily_first_escape = __is_daily_first_escape end
    local __is_limit_pre_match = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_limit_pre_match ~= false then tb.is_limit_pre_match = __is_limit_pre_match end
    local __is_pre_week_limit = decoder:getbool(11)
    if not PB_USE_DEFAULT_TABLE or __is_pre_week_limit ~= false then tb.is_pre_week_limit = __is_pre_week_limit end
    return tb
end

function pb.pb_RankCurrencyResultEncode(tb, encoder)
    if(tb.coefficient) then    encoder:adddouble(1, tb.coefficient)    end
    if(tb.raw_value) then    encoder:addi64(2, tb.raw_value)    end
    if(tb.conf_limit_pre_match) then    encoder:addi64(3, tb.conf_limit_pre_match)    end
    if(tb.pre_weekly_value) then    encoder:addi64(4, tb.pre_weekly_value)    end
    if(tb.conf_limit_pre_weekly) then    encoder:addi64(5, tb.conf_limit_pre_weekly)    end
    if(tb.conf_daily_first_escape) then    encoder:addi64(6, tb.conf_daily_first_escape)    end
    if(tb.actual_value) then    encoder:addi64(7, tb.actual_value)    end
    if(tb.currency_changes) then
        for i=1,#(tb.currency_changes) do
            pb.pb_RankCurrencyChangeEncode(tb.currency_changes[i], encoder:addsubmsg(8))
        end
    end
    if(tb.is_daily_first_escape) then    encoder:addbool(9, tb.is_daily_first_escape)    end
    if(tb.is_limit_pre_match) then    encoder:addbool(10, tb.is_limit_pre_match)    end
    if(tb.is_pre_week_limit) then    encoder:addbool(11, tb.is_pre_week_limit)    end
end

function pb.pb_GspPlayerGameStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayerGameStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.basic_info = pb.pb_GspPlayerBasicInfoDecode(decoder:getsubmsg(2))
    local __mode_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __mode_id ~= 0 then tb.mode_id = __mode_id end
    tb.playing = pb.pb_GspPlayingPlayerStatusDecode(decoder:getsubmsg(5))
    tb.end_game = pb.pb_GspEndGamePlayerStatusDecode(decoder:getsubmsg(3))
    tb.quit_game = pb.pb_GspQuitGamePlayerStatusDecode(decoder:getsubmsg(4))
    local __money_paper = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __money_paper ~= 0 then tb.money_paper = __money_paper end
    tb.rank_score_changes = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.rank_score_changes[k] = pb.pb_RankMatchScoreChangeDecode(v)
    end
    local __rank_match_score = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score ~= 0 then tb.rank_match_score = __rank_match_score end
    local __is_ranked_match = decoder:getbool(10)
    if not PB_USE_DEFAULT_TABLE or __is_ranked_match ~= false then tb.is_ranked_match = __is_ranked_match end
    local __rank_match_score_delta = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __rank_match_score_delta ~= 0 then tb.rank_match_score_delta = __rank_match_score_delta end
    tb.game_achievements = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.game_achievements[k] = pb.pb_DsGameAchievementDecode(v)
    end
    tb.unlock_mp_guns = decoder:getu64ary(13)
    local __result = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    tb.mystical_skins = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.mystical_skins[k] = pb.pb_PropInfoDecode(v)
    end
    local __trigger_rank_shield = decoder:getbool(16)
    if not PB_USE_DEFAULT_TABLE or __trigger_rank_shield ~= false then tb.trigger_rank_shield = __trigger_rank_shield end
    tb.rank_match_shield = pb.pb_RankShieldDecode(decoder:getsubmsg(17))
    local __sol_double_rank_multiple_value = decoder:getu32(18)
    if not PB_USE_DEFAULT_TABLE or __sol_double_rank_multiple_value ~= 0 then tb.sol_double_rank_multiple_value = __sol_double_rank_multiple_value end
    local __team_mate_price = decoder:geti64(19)
    if not PB_USE_DEFAULT_TABLE or __team_mate_price ~= 0 then tb.team_mate_price = __team_mate_price end
    local __assist_cnt = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __assist_cnt ~= 0 then tb.assist_cnt = __assist_cnt end
    tb.mystical_pendants = {}
    for k,v in pairs(decoder:getsubmsgary(21)) do
        tb.mystical_pendants[k] = pb.pb_PropInfoDecode(v)
    end
    tb.rank_currency_result = pb.pb_RankCurrencyResultDecode(decoder:getsubmsg(22))
    local __exactly_leave = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __exactly_leave ~= 0 then tb.exactly_leave = __exactly_leave end
    local __safebox_skin_id = decoder:getu64(24)
    if not PB_USE_DEFAULT_TABLE or __safebox_skin_id ~= 0 then tb.safebox_skin_id = __safebox_skin_id end
    return tb
end

function pb.pb_GspPlayerGameStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.basic_info) then    pb.pb_GspPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.mode_id) then    encoder:addi32(6, tb.mode_id)    end
    if(tb.playing) then    pb.pb_GspPlayingPlayerStatusEncode(tb.playing, encoder:addsubmsg(5))    end
    if(tb.end_game) then    pb.pb_GspEndGamePlayerStatusEncode(tb.end_game, encoder:addsubmsg(3))    end
    if(tb.quit_game) then    pb.pb_GspQuitGamePlayerStatusEncode(tb.quit_game, encoder:addsubmsg(4))    end
    if(tb.money_paper) then    encoder:addi64(7, tb.money_paper)    end
    if(tb.rank_score_changes) then
        for i=1,#(tb.rank_score_changes) do
            pb.pb_RankMatchScoreChangeEncode(tb.rank_score_changes[i], encoder:addsubmsg(8))
        end
    end
    if(tb.rank_match_score) then    encoder:addi64(9, tb.rank_match_score)    end
    if(tb.is_ranked_match) then    encoder:addbool(10, tb.is_ranked_match)    end
    if(tb.rank_match_score_delta) then    encoder:addi64(11, tb.rank_match_score_delta)    end
    if(tb.game_achievements) then
        for i=1,#(tb.game_achievements) do
            pb.pb_DsGameAchievementEncode(tb.game_achievements[i], encoder:addsubmsg(12))
        end
    end
    if(tb.unlock_mp_guns) then    encoder:addu64(13, tb.unlock_mp_guns)    end
    if(tb.result) then    encoder:addu32(14, tb.result)    end
    if(tb.mystical_skins) then
        for i=1,#(tb.mystical_skins) do
            pb.pb_PropInfoEncode(tb.mystical_skins[i], encoder:addsubmsg(15))
        end
    end
    if(tb.trigger_rank_shield) then    encoder:addbool(16, tb.trigger_rank_shield)    end
    if(tb.rank_match_shield) then    pb.pb_RankShieldEncode(tb.rank_match_shield, encoder:addsubmsg(17))    end
    if(tb.sol_double_rank_multiple_value) then    encoder:addu32(18, tb.sol_double_rank_multiple_value)    end
    if(tb.team_mate_price) then    encoder:addi64(19, tb.team_mate_price)    end
    if(tb.assist_cnt) then    encoder:addu32(20, tb.assist_cnt)    end
    if(tb.mystical_pendants) then
        for i=1,#(tb.mystical_pendants) do
            pb.pb_PropInfoEncode(tb.mystical_pendants[i], encoder:addsubmsg(21))
        end
    end
    if(tb.rank_currency_result) then    pb.pb_RankCurrencyResultEncode(tb.rank_currency_result, encoder:addsubmsg(22))    end
    if(tb.exactly_leave) then    encoder:addi32(23, tb.exactly_leave)    end
    if(tb.safebox_skin_id) then    encoder:addu64(24, tb.safebox_skin_id)    end
end

function pb.pb_GspPlayingTeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPlayingTeamMateStatus) or {} 
    local __play_time = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.current_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.current_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(4))
    return tb
end

function pb.pb_GspPlayingTeamMateStatusEncode(tb, encoder)
    if(tb.play_time) then    encoder:addi32(1, tb.play_time)    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(2))
        end
    end
    if(tb.current_props) then
        for i=1,#(tb.current_props) do
            pb.pb_EquipPositionEncode(tb.current_props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(4))    end
end

function pb.pb_GspEndGameTeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspEndGameTeamMateStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __play_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __has_main_weapon = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __has_main_weapon ~= false then tb.has_main_weapon = __has_main_weapon end
    local __main_weapon = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __main_weapon ~= 0 then tb.main_weapon = __main_weapon end
    tb.match_stat = pb.pb_GspPlayerMatchStatNewDecode(decoder:getsubmsg(19))
    local __performance_score = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.carry_out_props = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.carry_out_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(21))
    local __rescue_count = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __resurgence_count = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    local __carry_out_profit_price = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    local __cost_price = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    return tb
end

function pb.pb_GspEndGameTeamMateStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.play_time) then    encoder:addi32(4, tb.play_time)    end
    if(tb.has_main_weapon) then    encoder:addbool(20, tb.has_main_weapon)    end
    if(tb.main_weapon) then    encoder:addu64(5, tb.main_weapon)    end
    if(tb.match_stat) then    pb.pb_GspPlayerMatchStatNewEncode(tb.match_stat, encoder:addsubmsg(19))    end
    if(tb.performance_score) then    encoder:addfloat(6, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(9, tb.individual_rank)    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(12))
        end
    end
    if(tb.carry_out_props) then
        for i=1,#(tb.carry_out_props) do
            pb.pb_EquipPositionEncode(tb.carry_out_props[i], encoder:addsubmsg(13))
        end
    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(14))
        end
    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(15))
        end
    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(21))    end
    if(tb.rescue_count) then    encoder:addi32(22, tb.rescue_count)    end
    if(tb.resurgence_count) then    encoder:addi32(23, tb.resurgence_count)    end
    if(tb.carry_out_profit_price) then    encoder:addi32(24, tb.carry_out_profit_price)    end
    if(tb.cost_price) then    encoder:addi32(25, tb.cost_price)    end
end

function pb.pb_GspQuitGameTeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspQuitGameTeamMateStatus) or {} 
    local __reason = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __play_time = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __has_main_weapon = decoder:getbool(20)
    if not PB_USE_DEFAULT_TABLE or __has_main_weapon ~= false then tb.has_main_weapon = __has_main_weapon end
    local __main_weapon = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __main_weapon ~= 0 then tb.main_weapon = __main_weapon end
    tb.match_stat = pb.pb_GspPlayerMatchStatNewDecode(decoder:getsubmsg(19))
    local __performance_score = decoder:getfloat(6)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(9)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.carry_in_props = {}
    for k,v in pairs(decoder:getsubmsgary(12)) do
        tb.carry_in_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.carry_out_props = {}
    for k,v in pairs(decoder:getsubmsgary(13)) do
        tb.carry_out_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(14)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(15)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    tb.hero = pb.pb_DSHeroDecode(decoder:getsubmsg(21))
    local __rescue_count = decoder:geti32(22)
    if not PB_USE_DEFAULT_TABLE or __rescue_count ~= 0 then tb.rescue_count = __rescue_count end
    local __resurgence_count = decoder:geti32(23)
    if not PB_USE_DEFAULT_TABLE or __resurgence_count ~= 0 then tb.resurgence_count = __resurgence_count end
    local __carry_out_profit_price = decoder:geti32(24)
    if not PB_USE_DEFAULT_TABLE or __carry_out_profit_price ~= 0 then tb.carry_out_profit_price = __carry_out_profit_price end
    local __cost_price = decoder:geti32(25)
    if not PB_USE_DEFAULT_TABLE or __cost_price ~= 0 then tb.cost_price = __cost_price end
    return tb
end

function pb.pb_GspQuitGameTeamMateStatusEncode(tb, encoder)
    if(tb.reason) then    encoder:addi32(1, tb.reason)    end
    if(tb.play_time) then    encoder:addi32(4, tb.play_time)    end
    if(tb.has_main_weapon) then    encoder:addbool(20, tb.has_main_weapon)    end
    if(tb.main_weapon) then    encoder:addu64(5, tb.main_weapon)    end
    if(tb.match_stat) then    pb.pb_GspPlayerMatchStatNewEncode(tb.match_stat, encoder:addsubmsg(19))    end
    if(tb.performance_score) then    encoder:addfloat(6, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(9, tb.individual_rank)    end
    if(tb.carry_in_props) then
        for i=1,#(tb.carry_in_props) do
            pb.pb_EquipPositionEncode(tb.carry_in_props[i], encoder:addsubmsg(12))
        end
    end
    if(tb.carry_out_props) then
        for i=1,#(tb.carry_out_props) do
            pb.pb_EquipPositionEncode(tb.carry_out_props[i], encoder:addsubmsg(13))
        end
    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(14))
        end
    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(15))
        end
    end
    if(tb.hero) then    pb.pb_DSHeroEncode(tb.hero, encoder:addsubmsg(21))    end
    if(tb.rescue_count) then    encoder:addi32(22, tb.rescue_count)    end
    if(tb.resurgence_count) then    encoder:addi32(23, tb.resurgence_count)    end
    if(tb.carry_out_profit_price) then    encoder:addi32(24, tb.carry_out_profit_price)    end
    if(tb.cost_price) then    encoder:addi32(25, tb.cost_price)    end
end

function pb.pb_GspTeamMateGameStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspTeamMateGameStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.basic_info = pb.pb_GspPlayerBasicInfoDecode(decoder:getsubmsg(2))
    local __mode_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __mode_id ~= 0 then tb.mode_id = __mode_id end
    tb.playing = pb.pb_GspPlayingTeamMateStatusDecode(decoder:getsubmsg(5))
    tb.end_game = pb.pb_GspEndGameTeamMateStatusDecode(decoder:getsubmsg(3))
    tb.quit_game = pb.pb_GspQuitGameTeamMateStatusDecode(decoder:getsubmsg(4))
    tb.ai_info = pb.pb_GspAIBasicInfoDecode(decoder:getsubmsg(7))
    return tb
end

function pb.pb_GspTeamMateGameStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.basic_info) then    pb.pb_GspPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.mode_id) then    encoder:addi32(6, tb.mode_id)    end
    if(tb.playing) then    pb.pb_GspPlayingTeamMateStatusEncode(tb.playing, encoder:addsubmsg(5))    end
    if(tb.end_game) then    pb.pb_GspEndGameTeamMateStatusEncode(tb.end_game, encoder:addsubmsg(3))    end
    if(tb.quit_game) then    pb.pb_GspQuitGameTeamMateStatusEncode(tb.quit_game, encoder:addsubmsg(4))    end
    if(tb.ai_info) then    pb.pb_GspAIBasicInfoEncode(tb.ai_info, encoder:addsubmsg(7))    end
end

function pb.pb_GspPVEMissionInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEMissionInfo) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __match_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_subtype = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __match_mode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    local __team_size = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __team_size ~= 0 then tb.team_size = __team_size end
    local __map_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __start_time = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __player_count = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __player_count ~= 0 then tb.player_count = __player_count end
    return tb
end

function pb.pb_GspPVEMissionInfoEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.match_type) then    encoder:addi32(2, tb.match_type)    end
    if(tb.match_subtype) then    encoder:addi32(3, tb.match_subtype)    end
    if(tb.match_mode) then    encoder:addi32(4, tb.match_mode)    end
    if(tb.team_size) then    encoder:addi32(5, tb.team_size)    end
    if(tb.map_id) then    encoder:addu64(6, tb.map_id)    end
    if(tb.start_time) then    encoder:addu64(7, tb.start_time)    end
    if(tb.player_count) then    encoder:addi32(8, tb.player_count)    end
end

function pb.pb_GspPVEMissionStatDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEMissionStat) or {} 
    local __kill_count = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __kill_count ~= 0 then tb.kill_count = __kill_count end
    local __total_damage = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __total_damage ~= 0 then tb.total_damage = __total_damage end
    local __death_count = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __death_count ~= 0 then tb.death_count = __death_count end
    local __task_count = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __task_count ~= 0 then tb.task_count = __task_count end
    return tb
end

function pb.pb_GspPVEMissionStatEncode(tb, encoder)
    if(tb.kill_count) then    encoder:addi64(1, tb.kill_count)    end
    if(tb.total_damage) then    encoder:addi64(2, tb.total_damage)    end
    if(tb.death_count) then    encoder:addi64(3, tb.death_count)    end
    if(tb.task_count) then    encoder:addi64(4, tb.task_count)    end
end

function pb.pb_GspPVEEndGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEEndGamePlayerStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __play_time = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __performance_score = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.mission_stat = pb.pb_GspPVEMissionStatDecode(decoder:getsubmsg(6))
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    tb.first_time_award_array = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.first_time_award_array[k] = pb.pb_PropInfoDecode(v)
    end
    tb.daily_award_array = {}
    for k,v in pairs(decoder:getsubmsgary(9)) do
        tb.daily_award_array[k] = pb.pb_PropInfoDecode(v)
    end
    tb.season_change_info = pb.pb_SeasonChangeInfoDecode(decoder:getsubmsg(10))
    return tb
end

function pb.pb_GspPVEEndGamePlayerStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.play_time) then    encoder:addi32(2, tb.play_time)    end
    if(tb.performance_score) then    encoder:addi32(3, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(4, tb.individual_rank)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(5))
        end
    end
    if(tb.mission_stat) then    pb.pb_GspPVEMissionStatEncode(tb.mission_stat, encoder:addsubmsg(6))    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(7))
        end
    end
    if(tb.first_time_award_array) then
        for i=1,#(tb.first_time_award_array) do
            pb.pb_PropInfoEncode(tb.first_time_award_array[i], encoder:addsubmsg(8))
        end
    end
    if(tb.daily_award_array) then
        for i=1,#(tb.daily_award_array) do
            pb.pb_PropInfoEncode(tb.daily_award_array[i], encoder:addsubmsg(9))
        end
    end
    if(tb.season_change_info) then    pb.pb_SeasonChangeInfoEncode(tb.season_change_info, encoder:addsubmsg(10))    end
end

function pb.pb_GspPVEQuitGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEQuitGamePlayerStatus) or {} 
    local __play_time = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    tb.season_change_info = pb.pb_SeasonChangeInfoDecode(decoder:getsubmsg(3))
    return tb
end

function pb.pb_GspPVEQuitGamePlayerStatusEncode(tb, encoder)
    if(tb.play_time) then    encoder:addi32(1, tb.play_time)    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(2))
        end
    end
    if(tb.season_change_info) then    pb.pb_SeasonChangeInfoEncode(tb.season_change_info, encoder:addsubmsg(3))    end
end

function pb.pb_GspPVEPlayingGamePlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEPlayingGamePlayerStatus) or {} 
    return tb
end

function pb.pb_GspPVEPlayingGamePlayerStatusEncode(tb, encoder)
end

function pb.pb_GspPVEPlayerStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEPlayerStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.basic_info = pb.pb_GspPlayerBasicInfoDecode(decoder:getsubmsg(2))
    tb.equiped_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.equiped_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.end_game = pb.pb_GspPVEEndGamePlayerStatusDecode(decoder:getsubmsg(4))
    tb.quit_game = pb.pb_GspPVEQuitGamePlayerStatusDecode(decoder:getsubmsg(5))
    tb.playing = pb.pb_GspPVEPlayingGamePlayerStatusDecode(decoder:getsubmsg(6))
    return tb
end

function pb.pb_GspPVEPlayerStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.basic_info) then    pb.pb_GspPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.equiped_props) then
        for i=1,#(tb.equiped_props) do
            pb.pb_EquipPositionEncode(tb.equiped_props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.end_game) then    pb.pb_GspPVEEndGamePlayerStatusEncode(tb.end_game, encoder:addsubmsg(4))    end
    if(tb.quit_game) then    pb.pb_GspPVEQuitGamePlayerStatusEncode(tb.quit_game, encoder:addsubmsg(5))    end
    if(tb.playing) then    pb.pb_GspPVEPlayingGamePlayerStatusEncode(tb.playing, encoder:addsubmsg(6))    end
end

function pb.pb_GspPVEEndGameTeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEEndGameTeamMateStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __play_time = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __performance_score = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.mission_stat = pb.pb_GspPVEMissionStatDecode(decoder:getsubmsg(6))
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    return tb
end

function pb.pb_GspPVEEndGameTeamMateStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.play_time) then    encoder:addi32(2, tb.play_time)    end
    if(tb.performance_score) then    encoder:addi32(3, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(4, tb.individual_rank)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(5))
        end
    end
    if(tb.mission_stat) then    pb.pb_GspPVEMissionStatEncode(tb.mission_stat, encoder:addsubmsg(6))    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(7))
        end
    end
end

function pb.pb_GspPVEQuitGameTeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEQuitGameTeamMateStatus) or {} 
    local __play_time = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    local __performance_score = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __performance_score ~= 0 then tb.performance_score = __performance_score end
    local __individual_rank = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __individual_rank ~= 0 then tb.individual_rank = __individual_rank end
    tb.kill_array = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.kill_array[k] = pb.pb_GspPlayerKillInfoDecode(v)
    end
    tb.mission_stat = pb.pb_GspPVEMissionStatDecode(decoder:getsubmsg(5))
    tb.achievement_array = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.achievement_array[k] = pb.pb_GspAchievementInfoDecode(v)
    end
    return tb
end

function pb.pb_GspPVEQuitGameTeamMateStatusEncode(tb, encoder)
    if(tb.play_time) then    encoder:addi32(1, tb.play_time)    end
    if(tb.performance_score) then    encoder:addi32(2, tb.performance_score)    end
    if(tb.individual_rank) then    encoder:addi32(3, tb.individual_rank)    end
    if(tb.kill_array) then
        for i=1,#(tb.kill_array) do
            pb.pb_GspPlayerKillInfoEncode(tb.kill_array[i], encoder:addsubmsg(4))
        end
    end
    if(tb.mission_stat) then    pb.pb_GspPVEMissionStatEncode(tb.mission_stat, encoder:addsubmsg(5))    end
    if(tb.achievement_array) then
        for i=1,#(tb.achievement_array) do
            pb.pb_GspAchievementInfoEncode(tb.achievement_array[i], encoder:addsubmsg(6))
        end
    end
end

function pb.pb_GspPVEPlayingGameTeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVEPlayingGameTeamMateStatus) or {} 
    return tb
end

function pb.pb_GspPVEPlayingGameTeamMateStatusEncode(tb, encoder)
end

function pb.pb_GspPVETeamMateStatusDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_GspPVETeamMateStatus) or {} 
    local __type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.basic_info = pb.pb_GspPlayerBasicInfoDecode(decoder:getsubmsg(2))
    tb.equiped_props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.equiped_props[k] = pb.pb_EquipPositionDecode(v)
    end
    tb.end_game = pb.pb_GspPVEEndGameTeamMateStatusDecode(decoder:getsubmsg(4))
    tb.quit_game = pb.pb_GspPVEQuitGameTeamMateStatusDecode(decoder:getsubmsg(5))
    tb.playing = pb.pb_GspPVEPlayingGameTeamMateStatusDecode(decoder:getsubmsg(6))
    return tb
end

function pb.pb_GspPVETeamMateStatusEncode(tb, encoder)
    if(tb.type) then    encoder:addi32(1, tb.type)    end
    if(tb.basic_info) then    pb.pb_GspPlayerBasicInfoEncode(tb.basic_info, encoder:addsubmsg(2))    end
    if(tb.equiped_props) then
        for i=1,#(tb.equiped_props) do
            pb.pb_EquipPositionEncode(tb.equiped_props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.end_game) then    pb.pb_GspPVEEndGameTeamMateStatusEncode(tb.end_game, encoder:addsubmsg(4))    end
    if(tb.quit_game) then    pb.pb_GspPVEQuitGameTeamMateStatusEncode(tb.quit_game, encoder:addsubmsg(5))    end
    if(tb.playing) then    pb.pb_GspPVEPlayingGameTeamMateStatusEncode(tb.playing, encoder:addsubmsg(6))    end
end

function pb.pb_RaidPlayerDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RaidPlayerData) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __raid_score = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __raid_score ~= 0 then tb.raid_score = __raid_score end
    local __raid_level = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __raid_level ~= 0 then tb.raid_level = __raid_level end
    local __raid_kd = decoder:getdouble(4)
    if not PB_USE_DEFAULT_TABLE or __raid_kd ~= 0 then tb.raid_kd = __raid_kd end
    local __kill_num = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __kill_num ~= 0 then tb.kill_num = __kill_num end
    tb.evaluate_prop_list = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.evaluate_prop_list[k] = pb.pb_PropInfoDecode(v)
    end
    tb.evaluate_prize_ids = decoder:geti32ary(8)
    return tb
end

function pb.pb_RaidPlayerDataEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.raid_score) then    encoder:addi32(2, tb.raid_score)    end
    if(tb.raid_level) then    encoder:addi32(3, tb.raid_level)    end
    if(tb.raid_kd) then    encoder:adddouble(4, tb.raid_kd)    end
    if(tb.kill_num) then    encoder:addi32(5, tb.kill_num)    end
    if(tb.evaluate_prop_list) then
        for i=1,#(tb.evaluate_prop_list) do
            pb.pb_PropInfoEncode(tb.evaluate_prop_list[i], encoder:addsubmsg(6))
        end
    end
    if(tb.evaluate_prize_ids) then    encoder:addi32(8, tb.evaluate_prize_ids)    end
end

function pb.pb_RaidSettlementDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RaidSettlementData) or {} 
    tb.player_array = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.player_array[k] = pb.pb_RaidPlayerDataDecode(v)
    end
    local __play_time = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __play_time ~= 0 then tb.play_time = __play_time end
    tb.weapon_change = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.weapon_change[k] = pb.pb_WeaponChangeDecode(v)
    end
    return tb
end

function pb.pb_RaidSettlementDataEncode(tb, encoder)
    if(tb.player_array) then
        for i=1,#(tb.player_array) do
            pb.pb_RaidPlayerDataEncode(tb.player_array[i], encoder:addsubmsg(1))
        end
    end
    if(tb.play_time) then    encoder:addi32(2, tb.play_time)    end
    if(tb.weapon_change) then
        for i=1,#(tb.weapon_change) do
            pb.pb_WeaponChangeEncode(tb.weapon_change[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_MPBagCSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPBagCS) or {} 
    local __bag_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __bag_id ~= 0 then tb.bag_id = __bag_id end
    local __armedforce_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    tb.props = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.props[k] = pb.pb_MPPropPosCSDecode(v)
    end
    tb.modified_props = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.modified_props[k] = pb.pb_PropInfoDecode(v)
    end
    local __name = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    local __expert_id = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    local __expert_bag_id = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __expert_bag_id ~= 0 then tb.expert_bag_id = __expert_bag_id end
    return tb
end

function pb.pb_MPBagCSEncode(tb, encoder)
    if(tb.bag_id) then    encoder:addu32(1, tb.bag_id)    end
    if(tb.armedforce_id) then    encoder:addu32(2, tb.armedforce_id)    end
    if(tb.props) then
        for i=1,#(tb.props) do
            pb.pb_MPPropPosCSEncode(tb.props[i], encoder:addsubmsg(3))
        end
    end
    if(tb.modified_props) then
        for i=1,#(tb.modified_props) do
            pb.pb_PropInfoEncode(tb.modified_props[i], encoder:addsubmsg(6))
        end
    end
    if(tb.name) then    encoder:addstr(7, tb.name)    end
    if(tb.expert_id) then    encoder:addu32(8, tb.expert_id)    end
    if(tb.expert_bag_id) then    encoder:addu32(9, tb.expert_bag_id)    end
end

function pb.pb_MPWeaponStoreCSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPWeaponStoreCS) or {} 
    local __weapon_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __weapon_id ~= 0 then tb.weapon_id = __weapon_id end
    tb.skin_comps = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.skin_comps[k] = pb.pb_MPSkinComponentsDecode(v)
    end
    tb.unlocked_comps = decoder:getu64ary(4)
    tb.global_comps = decoder:getu64ary(5)
    return tb
end

function pb.pb_MPWeaponStoreCSEncode(tb, encoder)
    if(tb.weapon_id) then    encoder:addu64(1, tb.weapon_id)    end
    if(tb.skin_comps) then
        for i=1,#(tb.skin_comps) do
            pb.pb_MPSkinComponentsEncode(tb.skin_comps[i], encoder:addsubmsg(3))
        end
    end
    if(tb.unlocked_comps) then    encoder:addu64(4, tb.unlocked_comps)    end
    if(tb.global_comps) then    encoder:addu64(5, tb.global_comps)    end
end

function pb.pb_MPBagArmedPresetCSDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPBagArmedPresetCS) or {} 
    local __armedforce_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __armedforce_id ~= 0 then tb.armedforce_id = __armedforce_id end
    local __expert_id = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    local __default_bag_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __default_bag_id ~= 0 then tb.default_bag_id = __default_bag_id end
    local __def_bag_id = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __def_bag_id ~= 0 then tb.def_bag_id = __def_bag_id end
    tb.bags = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.bags[k] = pb.pb_MPBagCSDecode(v)
    end
    local __max_bag_num = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __max_bag_num ~= 0 then tb.max_bag_num = __max_bag_num end
    return tb
end

function pb.pb_MPBagArmedPresetCSEncode(tb, encoder)
    if(tb.armedforce_id) then    encoder:addu32(1, tb.armedforce_id)    end
    if(tb.expert_id) then    encoder:addu32(5, tb.expert_id)    end
    if(tb.default_bag_id) then    encoder:addu32(2, tb.default_bag_id)    end
    if(tb.def_bag_id) then    encoder:addu32(6, tb.def_bag_id)    end
    if(tb.bags) then
        for i=1,#(tb.bags) do
            pb.pb_MPBagCSEncode(tb.bags[i], encoder:addsubmsg(3))
        end
    end
    if(tb.max_bag_num) then    encoder:addu32(4, tb.max_bag_num)    end
end

function pb.pb_PlayerCarryoutPropsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerCarryoutProps) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.final_props = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.final_props[k] = pb.pb_PropInfoDecode(v)
    end
    return tb
end

function pb.pb_PlayerCarryoutPropsEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.final_props) then
        for i=1,#(tb.final_props) do
            pb.pb_PropInfoEncode(tb.final_props[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_PraiseItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PraiseItem) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    local __praise_time = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __praise_time ~= 0 then tb.praise_time = __praise_time end
    return tb
end

function pb.pb_PraiseItemEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.praise_time) then    encoder:addu64(2, tb.praise_time)    end
end

function pb.pb_CSHeroArmedPropStoreDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroArmedPropStore) or {} 
    local __position = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __position ~= 0 then tb.position = __position end
    tb.fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSHeroArmedPropStoreEncode(tb, encoder)
    if(tb.position) then    encoder:addu32(1, tb.position)    end
    if(tb.fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.fashion, encoder:addsubmsg(2))    end
end

function pb.pb_CSHeroArmedPropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroArmedPropInfo) or {} 
    tb.cur_fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(1))
    tb.init_fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(2))
    tb.last_fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(3))
    local __store_fashion_max_num = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __store_fashion_max_num ~= 0 then tb.store_fashion_max_num = __store_fashion_max_num end
    tb.store_fashion = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.store_fashion[k] = pb.pb_CSHeroArmedPropStoreDecode(v)
    end
    local __armed_prop_id = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __armed_prop_id ~= 0 then tb.armed_prop_id = __armed_prop_id end
    local __cur_lottery_times = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __cur_lottery_times ~= 0 then tb.cur_lottery_times = __cur_lottery_times end
    local __cur_lottery_quality = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __cur_lottery_quality ~= 0 then tb.cur_lottery_quality = __cur_lottery_quality end
    local __total_lottery_times = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __total_lottery_times ~= 0 then tb.total_lottery_times = __total_lottery_times end
    return tb
end

function pb.pb_CSHeroArmedPropInfoEncode(tb, encoder)
    if(tb.cur_fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.cur_fashion, encoder:addsubmsg(1))    end
    if(tb.init_fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.init_fashion, encoder:addsubmsg(2))    end
    if(tb.last_fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.last_fashion, encoder:addsubmsg(3))    end
    if(tb.store_fashion_max_num) then    encoder:addu32(5, tb.store_fashion_max_num)    end
    if(tb.store_fashion) then
        for i=1,#(tb.store_fashion) do
            pb.pb_CSHeroArmedPropStoreEncode(tb.store_fashion[i], encoder:addsubmsg(6))
        end
    end
    if(tb.armed_prop_id) then    encoder:addu64(7, tb.armed_prop_id)    end
    if(tb.cur_lottery_times) then    encoder:addi32(8, tb.cur_lottery_times)    end
    if(tb.cur_lottery_quality) then    encoder:addu32(9, tb.cur_lottery_quality)    end
    if(tb.total_lottery_times) then    encoder:addi32(10, tb.total_lottery_times)    end
end

function pb.pb_CSHeroDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHero) or {} 
    local __hero_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    local __can_use = decoder:getbool(14)
    if not PB_USE_DEFAULT_TABLE or __can_use ~= false then tb.can_use = __can_use end
    local __is_unlock = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    local __is_netbar_unlock = decoder:getbool(13)
    if not PB_USE_DEFAULT_TABLE or __is_netbar_unlock ~= false then tb.is_netbar_unlock = __is_netbar_unlock end
    local __armed_force_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __armed_force_id ~= 0 then tb.armed_force_id = __armed_force_id end
    local __expert_id = decoder:getu32(7)
    if not PB_USE_DEFAULT_TABLE or __expert_id ~= 0 then tb.expert_id = __expert_id end
    tb.hero_body = pb.pb_PropInfoDecode(decoder:getsubmsg(6))
    local __load_level = decoder:geti32(11)
    if not PB_USE_DEFAULT_TABLE or __load_level ~= 0 then tb.load_level = __load_level end
    tb.fashion_list = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.fashion_list[k] = pb.pb_FashionItemDecode(v)
    end
    tb.fashion_equipped = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.fashion_equipped[k] = pb.pb_HeroFashionDecode(v)
    end
    tb.accessories = {}
    for k,v in pairs(decoder:getsubmsgary(8)) do
        tb.accessories[k] = pb.pb_CSHeroAccessoryDecode(v)
    end
    tb.sol_expert_data = pb.pb_ExpertDataDecode(decoder:getsubmsg(9))
    tb.mp_expert_data = pb.pb_ExpertDataDecode(decoder:getsubmsg(10))
    tb.grow_line_data = pb.pb_CSHeroGrowLineDataDecode(decoder:getsubmsg(12))
    tb.challenge_data = pb.pb_CSHeroChallengeDataDecode(decoder:getsubmsg(15))
    tb.armed_prop_fashion_info = {}
    for k,v in pairs(decoder:getsubmsgary(16)) do
        tb.armed_prop_fashion_info[k] = pb.pb_CSHeroArmedPropInfoDecode(v)
    end
    tb.red_dot_info = {}
    for k,v in pairs(decoder:getsubmsgary(22)) do
        tb.red_dot_info[k] = pb.pb_HeroReddotInfoDecode(v)
    end
    return tb
end

function pb.pb_CSHeroEncode(tb, encoder)
    if(tb.hero_id) then    encoder:addu64(1, tb.hero_id)    end
    if(tb.can_use) then    encoder:addbool(14, tb.can_use)    end
    if(tb.is_unlock) then    encoder:addbool(2, tb.is_unlock)    end
    if(tb.is_netbar_unlock) then    encoder:addbool(13, tb.is_netbar_unlock)    end
    if(tb.armed_force_id) then    encoder:addu32(3, tb.armed_force_id)    end
    if(tb.expert_id) then    encoder:addu32(7, tb.expert_id)    end
    if(tb.hero_body) then    pb.pb_PropInfoEncode(tb.hero_body, encoder:addsubmsg(6))    end
    if(tb.load_level) then    encoder:addi32(11, tb.load_level)    end
    if(tb.fashion_list) then
        for i=1,#(tb.fashion_list) do
            pb.pb_FashionItemEncode(tb.fashion_list[i], encoder:addsubmsg(4))
        end
    end
    if(tb.fashion_equipped) then
        for i=1,#(tb.fashion_equipped) do
            pb.pb_HeroFashionEncode(tb.fashion_equipped[i], encoder:addsubmsg(5))
        end
    end
    if(tb.accessories) then
        for i=1,#(tb.accessories) do
            pb.pb_CSHeroAccessoryEncode(tb.accessories[i], encoder:addsubmsg(8))
        end
    end
    if(tb.sol_expert_data) then    pb.pb_ExpertDataEncode(tb.sol_expert_data, encoder:addsubmsg(9))    end
    if(tb.mp_expert_data) then    pb.pb_ExpertDataEncode(tb.mp_expert_data, encoder:addsubmsg(10))    end
    if(tb.grow_line_data) then    pb.pb_CSHeroGrowLineDataEncode(tb.grow_line_data, encoder:addsubmsg(12))    end
    if(tb.challenge_data) then    pb.pb_CSHeroChallengeDataEncode(tb.challenge_data, encoder:addsubmsg(15))    end
    if(tb.armed_prop_fashion_info) then
        for i=1,#(tb.armed_prop_fashion_info) do
            pb.pb_CSHeroArmedPropInfoEncode(tb.armed_prop_fashion_info[i], encoder:addsubmsg(16))
        end
    end
    if(tb.red_dot_info) then
        for i=1,#(tb.red_dot_info) do
            pb.pb_HeroReddotInfoEncode(tb.red_dot_info[i], encoder:addsubmsg(22))
        end
    end
end

function pb.pb_CSHeroChallengeDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroChallengeData) or {} 
    tb.goal_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.goal_list[k] = pb.pb_CSHeroGrowLineGoalDecode(v)
    end
    local __seletced_num = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __seletced_num ~= 0 then tb.seletced_num = __seletced_num end
    local __is_cur_selected = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __is_cur_selected ~= false then tb.is_cur_selected = __is_cur_selected end
    return tb
end

function pb.pb_CSHeroChallengeDataEncode(tb, encoder)
    if(tb.goal_list) then
        for i=1,#(tb.goal_list) do
            pb.pb_CSHeroGrowLineGoalEncode(tb.goal_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.seletced_num) then    encoder:addu32(3, tb.seletced_num)    end
    if(tb.is_cur_selected) then    encoder:addbool(4, tb.is_cur_selected)    end
end

function pb.pb_CSHeroAccessoryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroAccessory) or {} 
    tb.item = pb.pb_HeroAccessoryItemDecode(decoder:getsubmsg(1))
    local __is_unlock = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    local __is_selected = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_selected ~= false then tb.is_selected = __is_selected end
    local __is_read = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __is_read ~= false then tb.is_read = __is_read end
    local __unlocked_timestamp = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __unlocked_timestamp ~= 0 then tb.unlocked_timestamp = __unlocked_timestamp end
    return tb
end

function pb.pb_CSHeroAccessoryEncode(tb, encoder)
    if(tb.item) then    pb.pb_HeroAccessoryItemEncode(tb.item, encoder:addsubmsg(1))    end
    if(tb.is_unlock) then    encoder:addbool(2, tb.is_unlock)    end
    if(tb.is_selected) then    encoder:addbool(3, tb.is_selected)    end
    if(tb.is_read) then    encoder:addbool(4, tb.is_read)    end
    if(tb.unlocked_timestamp) then    encoder:addi64(5, tb.unlocked_timestamp)    end
end

function pb.pb_ExpertDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ExpertData) or {} 
    tb.skill = pb.pb_ExpertSkillDecode(decoder:getsubmsg(1))
    tb.armed_prop1 = pb.pb_PropInfoDecode(decoder:getsubmsg(2))
    tb.armed_prop2 = pb.pb_PropInfoDecode(decoder:getsubmsg(3))
    tb.armed_prop_pool1 = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.armed_prop_pool1[k] = pb.pb_ArmedforcePropDecode(v)
    end
    tb.armed_prop_pool2 = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.armed_prop_pool2[k] = pb.pb_ArmedforcePropDecode(v)
    end
    return tb
end

function pb.pb_ExpertDataEncode(tb, encoder)
    if(tb.skill) then    pb.pb_ExpertSkillEncode(tb.skill, encoder:addsubmsg(1))    end
    if(tb.armed_prop1) then    pb.pb_PropInfoEncode(tb.armed_prop1, encoder:addsubmsg(2))    end
    if(tb.armed_prop2) then    pb.pb_PropInfoEncode(tb.armed_prop2, encoder:addsubmsg(3))    end
    if(tb.armed_prop_pool1) then
        for i=1,#(tb.armed_prop_pool1) do
            pb.pb_ArmedforcePropEncode(tb.armed_prop_pool1[i], encoder:addsubmsg(4))
        end
    end
    if(tb.armed_prop_pool2) then
        for i=1,#(tb.armed_prop_pool2) do
            pb.pb_ArmedforcePropEncode(tb.armed_prop_pool2[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_ArmedforcePropDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ArmedforceProp) or {} 
    tb.prop = pb.pb_PropInfoDecode(decoder:getsubmsg(1))
    local __is_unlock = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    return tb
end

function pb.pb_ArmedforcePropEncode(tb, encoder)
    if(tb.prop) then    pb.pb_PropInfoEncode(tb.prop, encoder:addsubmsg(1))    end
    if(tb.is_unlock) then    encoder:addbool(2, tb.is_unlock)    end
end

function pb.pb_FashionItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_FashionItem) or {} 
    tb.fashion = pb.pb_HeroFashionDecode(decoder:getsubmsg(1))
    local __is_unlock = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    local __is_def = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __is_def ~= false then tb.is_def = __is_def end
    local __is_read = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __is_read ~= false then tb.is_read = __is_read end
    local __is_netbar_unlock = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __is_netbar_unlock ~= false then tb.is_netbar_unlock = __is_netbar_unlock end
    return tb
end

function pb.pb_FashionItemEncode(tb, encoder)
    if(tb.fashion) then    pb.pb_HeroFashionEncode(tb.fashion, encoder:addsubmsg(1))    end
    if(tb.is_unlock) then    encoder:addbool(2, tb.is_unlock)    end
    if(tb.is_def) then    encoder:addbool(3, tb.is_def)    end
    if(tb.is_read) then    encoder:addbool(4, tb.is_read)    end
    if(tb.is_netbar_unlock) then    encoder:addbool(5, tb.is_netbar_unlock)    end
end

function pb.pb_CSHeroGrowLineGoalDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroGrowLineGoal) or {} 
    local __goal_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __goal_id ~= 0 then tb.goal_id = __goal_id end
    local __cur_value = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __cur_value ~= 0 then tb.cur_value = __cur_value end
    local __max_value = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __max_value ~= 0 then tb.max_value = __max_value end
    return tb
end

function pb.pb_CSHeroGrowLineGoalEncode(tb, encoder)
    if(tb.goal_id) then    encoder:addu64(1, tb.goal_id)    end
    if(tb.cur_value) then    encoder:addi64(2, tb.cur_value)    end
    if(tb.max_value) then    encoder:addi64(3, tb.max_value)    end
end

function pb.pb_CSHeroGrowLineArchiveDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroGrowLineArchive) or {} 
    local __archive_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __archive_type ~= 0 then tb.archive_type = __archive_type end
    tb.archive_ids = decoder:getu64ary(2)
    local __red_point = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __red_point ~= false then tb.red_point = __red_point end
    return tb
end

function pb.pb_CSHeroGrowLineArchiveEncode(tb, encoder)
    if(tb.archive_type) then    encoder:addu32(1, tb.archive_type)    end
    if(tb.archive_ids) then    encoder:addu64(2, tb.archive_ids)    end
    if(tb.red_point) then    encoder:addbool(3, tb.red_point)    end
end

function pb.pb_CSHeroGrowLineDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroGrowLineData) or {} 
    local __level = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    tb.goal_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.goal_list[k] = pb.pb_CSHeroGrowLineGoalDecode(v)
    end
    local __seletced_num = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __seletced_num ~= 0 then tb.seletced_num = __seletced_num end
    tb.rewards = decoder:getu64ary(4)
    tb.archives = {}
    for k,v in pairs(decoder:getsubmsgary(5)) do
        tb.archives[k] = pb.pb_CSHeroGrowLineArchiveDecode(v)
    end
    return tb
end

function pb.pb_CSHeroGrowLineDataEncode(tb, encoder)
    if(tb.level) then    encoder:addu32(1, tb.level)    end
    if(tb.goal_list) then
        for i=1,#(tb.goal_list) do
            pb.pb_CSHeroGrowLineGoalEncode(tb.goal_list[i], encoder:addsubmsg(2))
        end
    end
    if(tb.seletced_num) then    encoder:addu32(3, tb.seletced_num)    end
    if(tb.rewards) then    encoder:addu64(4, tb.rewards)    end
    if(tb.archives) then
        for i=1,#(tb.archives) do
            pb.pb_CSHeroGrowLineArchiveEncode(tb.archives[i], encoder:addsubmsg(5))
        end
    end
end

function pb.pb_CSHeroGrowLineDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSHeroGrowLine) or {} 
    local __hero_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __hero_id ~= 0 then tb.hero_id = __hero_id end
    tb.grow_line_data = pb.pb_CSHeroGrowLineDataDecode(decoder:getsubmsg(2))
    return tb
end

function pb.pb_CSHeroGrowLineEncode(tb, encoder)
    if(tb.hero_id) then    encoder:addu64(1, tb.hero_id)    end
    if(tb.grow_line_data) then    pb.pb_CSHeroGrowLineDataEncode(tb.grow_line_data, encoder:addsubmsg(2))    end
end

function pb.pb_HeroArmedPropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroArmedPropInfo) or {} 
    tb.cur_fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(1))
    tb.init_fashion = pb.pb_HeroArmedPropFashionDecode(decoder:getsubmsg(2))
    tb.history_fashion = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.history_fashion[k] = pb.pb_HeroArmedPropFashionDecode(v)
    end
    local __store_fashion_max_num = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __store_fashion_max_num ~= 0 then tb.store_fashion_max_num = __store_fashion_max_num end
    tb.store_fashion = {}
    for k,v in pairs(decoder:getsubmsgary(6)) do
        tb.store_fashion[k] = pb.pb_HeroArmedPropInfo_StoreFashionEntryDecode(v)
    end
    local __cur_lottery_times = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __cur_lottery_times ~= 0 then tb.cur_lottery_times = __cur_lottery_times end
    local __cur_quality = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __cur_quality ~= 0 then tb.cur_quality = __cur_quality end
    tb.fashionid_history = decoder:getu64ary(9)
    local __total_lottery_num = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __total_lottery_num ~= 0 then tb.total_lottery_num = __total_lottery_num end
    return tb
end

function pb.pb_HeroArmedPropInfoEncode(tb, encoder)
    if(tb.cur_fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.cur_fashion, encoder:addsubmsg(1))    end
    if(tb.init_fashion) then    pb.pb_HeroArmedPropFashionEncode(tb.init_fashion, encoder:addsubmsg(2))    end
    if(tb.history_fashion) then
        for i=1,#(tb.history_fashion) do
            pb.pb_HeroArmedPropFashionEncode(tb.history_fashion[i], encoder:addsubmsg(4))
        end
    end
    if(tb.store_fashion_max_num) then    encoder:addu32(5, tb.store_fashion_max_num)    end
    if(tb.store_fashion) then
        for i=1,#(tb.store_fashion) do
            pb.pb_HeroArmedPropInfo_StoreFashionEntryEncode(tb.store_fashion[i], encoder:addsubmsg(6))
        end
    end
    if(tb.cur_lottery_times) then    encoder:addi32(7, tb.cur_lottery_times)    end
    if(tb.cur_quality) then    encoder:addu32(8, tb.cur_quality)    end
    if(tb.fashionid_history) then    encoder:addu64(9, tb.fashionid_history)    end
    if(tb.total_lottery_num) then    encoder:addu32(10, tb.total_lottery_num)    end
end

function pb.pb_HeroReddotInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_HeroReddotInfo) or {} 
    local __type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    tb.value = decoder:getu64ary(2)
    return tb
end

function pb.pb_HeroReddotInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addu32(1, tb.type)    end
    if(tb.value) then    encoder:addu64(2, tb.value)    end
end

function pb.pb_WorldChatRoomInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WorldChatRoomInfo) or {} 
    local __room_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __type = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __state = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __cur_member_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __cur_member_num ~= 0 then tb.cur_member_num = __cur_member_num end
    local __created_time = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __created_time ~= 0 then tb.created_time = __created_time end
    local __last_active_time = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __last_active_time ~= 0 then tb.last_active_time = __last_active_time end
    local __info_record_time = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __info_record_time ~= 0 then tb.info_record_time = __info_record_time end
    local __SourceNode = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __SourceNode ~= "" then tb.SourceNode = __SourceNode end
    local __group_id = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __group_id ~= "" then tb.group_id = __group_id end
    return tb
end

function pb.pb_WorldChatRoomInfoEncode(tb, encoder)
    if(tb.room_id) then    encoder:addu64(1, tb.room_id)    end
    if(tb.type) then    encoder:addu32(2, tb.type)    end
    if(tb.state) then    encoder:addu32(3, tb.state)    end
    if(tb.cur_member_num) then    encoder:addi64(4, tb.cur_member_num)    end
    if(tb.created_time) then    encoder:addi64(5, tb.created_time)    end
    if(tb.last_active_time) then    encoder:addi64(6, tb.last_active_time)    end
    if(tb.info_record_time) then    encoder:addi64(7, tb.info_record_time)    end
    if(tb.SourceNode) then    encoder:addstr(8, tb.SourceNode)    end
    if(tb.group_id) then    encoder:addstr(9, tb.group_id)    end
end

function pb.pb_SafehouseDeviceLevelDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SafehouseDeviceLevel) or {} 
    local __device_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __device_id ~= 0 then tb.device_id = __device_id end
    local __level = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __level ~= 0 then tb.level = __level end
    return tb
end

function pb.pb_SafehouseDeviceLevelEncode(tb, encoder)
    if(tb.device_id) then    encoder:addu32(1, tb.device_id)    end
    if(tb.level) then    encoder:addu32(2, tb.level)    end
end

function pb.pb_StreakInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StreakInfo) or {} 
    local __match_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_sub_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __match_sub_type ~= 0 then tb.match_sub_type = __match_sub_type end
    local __match_mode = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    local __victory_streak = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __victory_streak ~= 0 then tb.victory_streak = __victory_streak end
    local __defeat_streak = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __defeat_streak ~= 0 then tb.defeat_streak = __defeat_streak end
    local __kill_player_count = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __kill_player_count ~= 0 then tb.kill_player_count = __kill_player_count end
    local __kill_ai_count = decoder:geti32(7)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_count ~= 0 then tb.kill_ai_count = __kill_ai_count end
    local __map_id = decoder:geti32(8)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(9))
    return tb
end

function pb.pb_StreakInfoEncode(tb, encoder)
    if(tb.match_type) then    encoder:addi32(1, tb.match_type)    end
    if(tb.match_sub_type) then    encoder:addi32(2, tb.match_sub_type)    end
    if(tb.match_mode) then    encoder:addi32(3, tb.match_mode)    end
    if(tb.victory_streak) then    encoder:addi32(4, tb.victory_streak)    end
    if(tb.defeat_streak) then    encoder:addi32(5, tb.defeat_streak)    end
    if(tb.kill_player_count) then    encoder:addi32(6, tb.kill_player_count)    end
    if(tb.kill_ai_count) then    encoder:addi32(7, tb.kill_ai_count)    end
    if(tb.map_id) then    encoder:addi32(8, tb.map_id)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(9))    end
end

function pb.pb_StreakTopRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StreakTopRecord) or {} 
    local __streak_count = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __streak_count ~= 0 then tb.streak_count = __streak_count end
    local __kill_player_count = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __kill_player_count ~= 0 then tb.kill_player_count = __kill_player_count end
    local __kill_ai_count = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_count ~= 0 then tb.kill_ai_count = __kill_ai_count end
    return tb
end

function pb.pb_StreakTopRecordEncode(tb, encoder)
    if(tb.streak_count) then    encoder:addi32(1, tb.streak_count)    end
    if(tb.kill_player_count) then    encoder:addi32(2, tb.kill_player_count)    end
    if(tb.kill_ai_count) then    encoder:addi32(3, tb.kill_ai_count)    end
end

function pb.pb_StreakInfoTopRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StreakInfoTopRecord) or {} 
    local __match_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_sub_type = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __match_sub_type ~= 0 then tb.match_sub_type = __match_sub_type end
    local __match_mode = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    tb.victory_record = pb.pb_StreakTopRecordDecode(decoder:getsubmsg(4))
    tb.defeat_record = pb.pb_StreakTopRecordDecode(decoder:getsubmsg(5))
    local __map_id = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    tb.mode_info = pb.pb_MatchModeInfoDecode(decoder:getsubmsg(7))
    return tb
end

function pb.pb_StreakInfoTopRecordEncode(tb, encoder)
    if(tb.match_type) then    encoder:addi32(1, tb.match_type)    end
    if(tb.match_sub_type) then    encoder:addi32(2, tb.match_sub_type)    end
    if(tb.match_mode) then    encoder:addi32(3, tb.match_mode)    end
    if(tb.victory_record) then    pb.pb_StreakTopRecordEncode(tb.victory_record, encoder:addsubmsg(4))    end
    if(tb.defeat_record) then    pb.pb_StreakTopRecordEncode(tb.defeat_record, encoder:addsubmsg(5))    end
    if(tb.map_id) then    encoder:addi32(6, tb.map_id)    end
    if(tb.mode_info) then    pb.pb_MatchModeInfoEncode(tb.mode_info, encoder:addsubmsg(7))    end
end

function pb.pb_CoPlayerSOLRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CoPlayerSOLRecord) or {} 
    local __result = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __actual_produced_price = decoder:getdouble(2)
    if not PB_USE_DEFAULT_TABLE or __actual_produced_price ~= 0 then tb.actual_produced_price = __actual_produced_price end
    local __kill_ai_count = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __kill_ai_count ~= 0 then tb.kill_ai_count = __kill_ai_count end
    local __kill_boss_count = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __kill_boss_count ~= 0 then tb.kill_boss_count = __kill_boss_count end
    local __kill_player_count = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __kill_player_count ~= 0 then tb.kill_player_count = __kill_player_count end
    local __kill_scav_count = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __kill_scav_count ~= 0 then tb.kill_scav_count = __kill_scav_count end
    return tb
end

function pb.pb_CoPlayerSOLRecordEncode(tb, encoder)
    if(tb.result) then    encoder:addi32(1, tb.result)    end
    if(tb.actual_produced_price) then    encoder:adddouble(2, tb.actual_produced_price)    end
    if(tb.kill_ai_count) then    encoder:addi32(3, tb.kill_ai_count)    end
    if(tb.kill_boss_count) then    encoder:addi32(4, tb.kill_boss_count)    end
    if(tb.kill_player_count) then    encoder:addi32(5, tb.kill_player_count)    end
    if(tb.kill_scav_count) then    encoder:addi32(6, tb.kill_scav_count)    end
end

function pb.pb_CoPlayerRaidRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CoPlayerRaidRecord) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CoPlayerRaidRecordEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_CoPlayerBFRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CoPlayerBFRecord) or {} 
    return tb
end

function pb.pb_CoPlayerBFRecordEncode(tb, encoder)
end

function pb.pb_CoPlayerTDMRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CoPlayerTDMRecord) or {} 
    local __result = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    return tb
end

function pb.pb_CoPlayerTDMRecordEncode(tb, encoder)
    if(tb.result) then    encoder:addu32(1, tb.result)    end
end

function pb.pb_RecentCoPlayerInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RecentCoPlayerInfo) or {} 
    local __match_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __match_type ~= 0 then tb.match_type = __match_type end
    local __match_subtype = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __match_subtype ~= 0 then tb.match_subtype = __match_subtype end
    local __match_mode = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __match_mode ~= 0 then tb.match_mode = __match_mode end
    local __match_submode = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __match_submode ~= 0 then tb.match_submode = __match_submode end
    local __room_id = decoder:getu64(9)
    if not PB_USE_DEFAULT_TABLE or __room_id ~= 0 then tb.room_id = __room_id end
    local __finish_time = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __finish_time ~= 0 then tb.finish_time = __finish_time end
    local __map_id = decoder:geti32(12)
    if not PB_USE_DEFAULT_TABLE or __map_id ~= 0 then tb.map_id = __map_id end
    local __game_mode = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __game_mode ~= 0 then tb.game_mode = __game_mode end
    local __game_rule = decoder:getu32(14)
    if not PB_USE_DEFAULT_TABLE or __game_rule ~= 0 then tb.game_rule = __game_rule end
    tb.player_info = pb.pb_PlayerSimpleInfoDecode(decoder:getsubmsg(6))
    tb.sol_record = pb.pb_CoPlayerSOLRecordDecode(decoder:getsubmsg(7))
    tb.bf_record = pb.pb_CoPlayerBFRecordDecode(decoder:getsubmsg(8))
    tb.tdm_record = pb.pb_CoPlayerTDMRecordDecode(decoder:getsubmsg(11))
    tb.raid_record = pb.pb_CoPlayerRaidRecordDecode(decoder:getsubmsg(10))
    return tb
end

function pb.pb_RecentCoPlayerInfoEncode(tb, encoder)
    if(tb.match_type) then    encoder:addi32(1, tb.match_type)    end
    if(tb.match_subtype) then    encoder:addi32(2, tb.match_subtype)    end
    if(tb.match_mode) then    encoder:addi32(3, tb.match_mode)    end
    if(tb.match_submode) then    encoder:addi32(4, tb.match_submode)    end
    if(tb.room_id) then    encoder:addu64(9, tb.room_id)    end
    if(tb.finish_time) then    encoder:addi32(5, tb.finish_time)    end
    if(tb.map_id) then    encoder:addi32(12, tb.map_id)    end
    if(tb.game_mode) then    encoder:addu32(13, tb.game_mode)    end
    if(tb.game_rule) then    encoder:addu32(14, tb.game_rule)    end
    if(tb.player_info) then    pb.pb_PlayerSimpleInfoEncode(tb.player_info, encoder:addsubmsg(6))    end
    if(tb.sol_record) then    pb.pb_CoPlayerSOLRecordEncode(tb.sol_record, encoder:addsubmsg(7))    end
    if(tb.bf_record) then    pb.pb_CoPlayerBFRecordEncode(tb.bf_record, encoder:addsubmsg(8))    end
    if(tb.tdm_record) then    pb.pb_CoPlayerTDMRecordEncode(tb.tdm_record, encoder:addsubmsg(11))    end
    if(tb.raid_record) then    pb.pb_CoPlayerRaidRecordEncode(tb.raid_record, encoder:addsubmsg(10))    end
end

function pb.pb_DSMachineLoadDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DSMachineLoad) or {} 
    local __ds_count = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __ds_count ~= 0 then tb.ds_count = __ds_count end
    local __ds_max_num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __ds_max_num ~= 0 then tb.ds_max_num = __ds_max_num end
    local __ds_father_count = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __ds_father_count ~= 0 then tb.ds_father_count = __ds_father_count end
    local __ds_room_count = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __ds_room_count ~= 0 then tb.ds_room_count = __ds_room_count end
    local __support_ds_type = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __support_ds_type ~= 0 then tb.support_ds_type = __support_ds_type end
    local __single_room_ds_count = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __single_room_ds_count ~= 0 then tb.single_room_ds_count = __single_room_ds_count end
    local __multi_room_ds_count = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __multi_room_ds_count ~= 0 then tb.multi_room_ds_count = __multi_room_ds_count end
    local __is_disabled = decoder:getbool(9)
    if not PB_USE_DEFAULT_TABLE or __is_disabled ~= false then tb.is_disabled = __is_disabled end
    local __multi_room_ds_room_count = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __multi_room_ds_room_count ~= 0 then tb.multi_room_ds_room_count = __multi_room_ds_room_count end
    local __total_score = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __total_score ~= 0 then tb.total_score = __total_score end
    local __used_score = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __used_score ~= 0 then tb.used_score = __used_score end
    local __cpu_cores_count = decoder:geti64(100)
    if not PB_USE_DEFAULT_TABLE or __cpu_cores_count ~= 0 then tb.cpu_cores_count = __cpu_cores_count end
    local __cpu_idle_percent = decoder:geti64(101)
    if not PB_USE_DEFAULT_TABLE or __cpu_idle_percent ~= 0 then tb.cpu_idle_percent = __cpu_idle_percent end
    local __cpu_usage_percent = decoder:geti64(102)
    if not PB_USE_DEFAULT_TABLE or __cpu_usage_percent ~= 0 then tb.cpu_usage_percent = __cpu_usage_percent end
    local __cpu_reserved_idle_percent = decoder:geti64(103)
    if not PB_USE_DEFAULT_TABLE or __cpu_reserved_idle_percent ~= 0 then tb.cpu_reserved_idle_percent = __cpu_reserved_idle_percent end
    local __mem_total_mb = decoder:geti64(150)
    if not PB_USE_DEFAULT_TABLE or __mem_total_mb ~= 0 then tb.mem_total_mb = __mem_total_mb end
    local __mem_available_mb = decoder:geti64(151)
    if not PB_USE_DEFAULT_TABLE or __mem_available_mb ~= 0 then tb.mem_available_mb = __mem_available_mb end
    local __mem_shared_mb = decoder:geti64(152)
    if not PB_USE_DEFAULT_TABLE or __mem_shared_mb ~= 0 then tb.mem_shared_mb = __mem_shared_mb end
    local __mem_ds_father_used_mb = decoder:geti64(153)
    if not PB_USE_DEFAULT_TABLE or __mem_ds_father_used_mb ~= 0 then tb.mem_ds_father_used_mb = __mem_ds_father_used_mb end
    local __mem_ds_used_mb = decoder:geti64(154)
    if not PB_USE_DEFAULT_TABLE or __mem_ds_used_mb ~= 0 then tb.mem_ds_used_mb = __mem_ds_used_mb end
    local __mem_reserved_in_mb = decoder:geti64(155)
    if not PB_USE_DEFAULT_TABLE or __mem_reserved_in_mb ~= 0 then tb.mem_reserved_in_mb = __mem_reserved_in_mb end
    local __player_num = decoder:geti64(200)
    if not PB_USE_DEFAULT_TABLE or __player_num ~= 0 then tb.player_num = __player_num end
    local __ai_num = decoder:geti64(201)
    if not PB_USE_DEFAULT_TABLE or __ai_num ~= 0 then tb.ai_num = __ai_num end
    local __robot_num = decoder:geti64(202)
    if not PB_USE_DEFAULT_TABLE or __robot_num ~= 0 then tb.robot_num = __robot_num end
    return tb
end

function pb.pb_DSMachineLoadEncode(tb, encoder)
    if(tb.ds_count) then    encoder:addi64(1, tb.ds_count)    end
    if(tb.ds_max_num) then    encoder:addi64(2, tb.ds_max_num)    end
    if(tb.ds_father_count) then    encoder:addi64(3, tb.ds_father_count)    end
    if(tb.ds_room_count) then    encoder:addi64(4, tb.ds_room_count)    end
    if(tb.support_ds_type) then    encoder:addu32(6, tb.support_ds_type)    end
    if(tb.single_room_ds_count) then    encoder:addi64(7, tb.single_room_ds_count)    end
    if(tb.multi_room_ds_count) then    encoder:addi64(8, tb.multi_room_ds_count)    end
    if(tb.is_disabled) then    encoder:addbool(9, tb.is_disabled)    end
    if(tb.multi_room_ds_room_count) then    encoder:addi64(10, tb.multi_room_ds_room_count)    end
    if(tb.total_score) then    encoder:addi64(11, tb.total_score)    end
    if(tb.used_score) then    encoder:addi64(12, tb.used_score)    end
    if(tb.cpu_cores_count) then    encoder:addi64(100, tb.cpu_cores_count)    end
    if(tb.cpu_idle_percent) then    encoder:addi64(101, tb.cpu_idle_percent)    end
    if(tb.cpu_usage_percent) then    encoder:addi64(102, tb.cpu_usage_percent)    end
    if(tb.cpu_reserved_idle_percent) then    encoder:addi64(103, tb.cpu_reserved_idle_percent)    end
    if(tb.mem_total_mb) then    encoder:addi64(150, tb.mem_total_mb)    end
    if(tb.mem_available_mb) then    encoder:addi64(151, tb.mem_available_mb)    end
    if(tb.mem_shared_mb) then    encoder:addi64(152, tb.mem_shared_mb)    end
    if(tb.mem_ds_father_used_mb) then    encoder:addi64(153, tb.mem_ds_father_used_mb)    end
    if(tb.mem_ds_used_mb) then    encoder:addi64(154, tb.mem_ds_used_mb)    end
    if(tb.mem_reserved_in_mb) then    encoder:addi64(155, tb.mem_reserved_in_mb)    end
    if(tb.player_num) then    encoder:addi64(200, tb.player_num)    end
    if(tb.ai_num) then    encoder:addi64(201, tb.ai_num)    end
    if(tb.robot_num) then    encoder:addi64(202, tb.robot_num)    end
end

function pb.pb_WeaponCheckItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_WeaponCheckItems) or {} 
    local __should_check = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __should_check ~= false then tb.should_check = __should_check end
    return tb
end

function pb.pb_WeaponCheckItemsEncode(tb, encoder)
    if(tb.should_check) then    encoder:addbool(1, tb.should_check)    end
end

function pb.pb_DrugCheckItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_DrugCheckItems) or {} 
    local __should_check = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __should_check ~= false then tb.should_check = __should_check end
    local __total_treatment_threshold = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __total_treatment_threshold ~= 0 then tb.total_treatment_threshold = __total_treatment_threshold end
    tb.clean_buffs = decoder:getu32ary(3)
    return tb
end

function pb.pb_DrugCheckItemsEncode(tb, encoder)
    if(tb.should_check) then    encoder:addbool(1, tb.should_check)    end
    if(tb.total_treatment_threshold) then    encoder:addu64(2, tb.total_treatment_threshold)    end
    if(tb.clean_buffs) then    encoder:addu32(3, tb.clean_buffs)    end
end

function pb.pb_EquipmentCheckItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipmentCheckItems) or {} 
    local __helmet_durability_threshold = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __helmet_durability_threshold ~= 0 then tb.helmet_durability_threshold = __helmet_durability_threshold end
    local __armor_durability_threshold = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __armor_durability_threshold ~= 0 then tb.armor_durability_threshold = __armor_durability_threshold end
    local __should_check_helmet = decoder:getbool(4)
    if not PB_USE_DEFAULT_TABLE or __should_check_helmet ~= false then tb.should_check_helmet = __should_check_helmet end
    local __should_check_armor = decoder:getbool(5)
    if not PB_USE_DEFAULT_TABLE or __should_check_armor ~= false then tb.should_check_armor = __should_check_armor end
    return tb
end

function pb.pb_EquipmentCheckItemsEncode(tb, encoder)
    if(tb.helmet_durability_threshold) then    encoder:addu32(2, tb.helmet_durability_threshold)    end
    if(tb.armor_durability_threshold) then    encoder:addu32(3, tb.armor_durability_threshold)    end
    if(tb.should_check_helmet) then    encoder:addbool(4, tb.should_check_helmet)    end
    if(tb.should_check_armor) then    encoder:addbool(5, tb.should_check_armor)    end
end

function pb.pb_ContainerCheckItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ContainerCheckItems) or {} 
    local __should_check = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __should_check ~= false then tb.should_check = __should_check end
    local __total_volume_threshold = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_volume_threshold ~= 0 then tb.total_volume_threshold = __total_volume_threshold end
    local __should_check_remains = decoder:getbool(3)
    if not PB_USE_DEFAULT_TABLE or __should_check_remains ~= false then tb.should_check_remains = __should_check_remains end
    return tb
end

function pb.pb_ContainerCheckItemsEncode(tb, encoder)
    if(tb.should_check) then    encoder:addbool(1, tb.should_check)    end
    if(tb.total_volume_threshold) then    encoder:addu32(2, tb.total_volume_threshold)    end
    if(tb.should_check_remains) then    encoder:addbool(3, tb.should_check_remains)    end
end

function pb.pb_StyleCheckItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_StyleCheckItems) or {} 
    local __default_not_recommond = decoder:getbool(1)
    if not PB_USE_DEFAULT_TABLE or __default_not_recommond ~= false then tb.default_not_recommond = __default_not_recommond end
    local __reserve_setting_pos = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __reserve_setting_pos ~= false then tb.reserve_setting_pos = __reserve_setting_pos end
    return tb
end

function pb.pb_StyleCheckItemsEncode(tb, encoder)
    if(tb.default_not_recommond) then    encoder:addbool(1, tb.default_not_recommond)    end
    if(tb.reserve_setting_pos) then    encoder:addbool(2, tb.reserve_setting_pos)    end
end

function pb.pb_OutfitCheckEntryDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_OutfitCheckEntry) or {} 
    local __panel_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __panel_type ~= 0 then tb.panel_type = __panel_type end
    tb.values = decoder:geti32ary(3)
    return tb
end

function pb.pb_OutfitCheckEntryEncode(tb, encoder)
    if(tb.panel_type) then    encoder:addi32(1, tb.panel_type)    end
    if(tb.values) then    encoder:addi32(3, tb.values)    end
end

function pb.pb_OutfitCheckItemsDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_OutfitCheckItems) or {} 
    tb.weapon = pb.pb_WeaponCheckItemsDecode(decoder:getsubmsg(1))
    tb.drug = pb.pb_DrugCheckItemsDecode(decoder:getsubmsg(2))
    tb.container = pb.pb_ContainerCheckItemsDecode(decoder:getsubmsg(4))
    tb.equipment = pb.pb_EquipmentCheckItemsDecode(decoder:getsubmsg(5))
    tb.outfit_style = pb.pb_StyleCheckItemsDecode(decoder:getsubmsg(6))
    tb.check_entries = {}
    for k,v in pairs(decoder:getsubmsgary(7)) do
        tb.check_entries[k] = pb.pb_OutfitCheckEntryDecode(v)
    end
    return tb
end

function pb.pb_OutfitCheckItemsEncode(tb, encoder)
    if(tb.weapon) then    pb.pb_WeaponCheckItemsEncode(tb.weapon, encoder:addsubmsg(1))    end
    if(tb.drug) then    pb.pb_DrugCheckItemsEncode(tb.drug, encoder:addsubmsg(2))    end
    if(tb.container) then    pb.pb_ContainerCheckItemsEncode(tb.container, encoder:addsubmsg(4))    end
    if(tb.equipment) then    pb.pb_EquipmentCheckItemsEncode(tb.equipment, encoder:addsubmsg(5))    end
    if(tb.outfit_style) then    pb.pb_StyleCheckItemsEncode(tb.outfit_style, encoder:addsubmsg(6))    end
    if(tb.check_entries) then
        for i=1,#(tb.check_entries) do
            pb.pb_OutfitCheckEntryEncode(tb.check_entries[i], encoder:addsubmsg(7))
        end
    end
end

function pb.pb_EquipmentRentalPlanDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipmentRentalPlan) or {} 
    local __type_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type_id ~= 0 then tb.type_id = __type_id end
    local __preset_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __preset_id ~= 0 then tb.preset_id = __preset_id end
    local __refresh_ts = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __refresh_ts ~= 0 then tb.refresh_ts = __refresh_ts end
    local __consumable_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __consumable_id ~= 0 then tb.consumable_id = __consumable_id end
    return tb
end

function pb.pb_EquipmentRentalPlanEncode(tb, encoder)
    if(tb.type_id) then    encoder:addu32(1, tb.type_id)    end
    if(tb.preset_id) then    encoder:addu32(2, tb.preset_id)    end
    if(tb.refresh_ts) then    encoder:addi64(3, tb.refresh_ts)    end
    if(tb.consumable_id) then    encoder:addu64(5, tb.consumable_id)    end
end

function pb.pb_EquipmentRentalPlanDetailDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipmentRentalPlanDetail) or {} 
    local __consumable_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __consumable_id ~= 0 then tb.consumable_id = __consumable_id end
    local __type_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __type_id ~= 0 then tb.type_id = __type_id end
    local __preset_id = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __preset_id ~= 0 then tb.preset_id = __preset_id end
    tb.details = {}
    for k,v in pairs(decoder:getsubmsgary(4)) do
        tb.details[k] = pb.pb_EquipmentRentalPropDecode(v)
    end
    local __preset_title = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __preset_title ~= "" then tb.preset_title = __preset_title end
    local __refresh_price = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __refresh_price ~= 0 then tb.refresh_price = __refresh_price end
    local __refresh_enable = decoder:getbool(7)
    if not PB_USE_DEFAULT_TABLE or __refresh_enable ~= false then tb.refresh_enable = __refresh_enable end
    local __preset_price = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __preset_price ~= 0 then tb.preset_price = __preset_price end
    local __consumable_type = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __consumable_type ~= 0 then tb.consumable_type = __consumable_type end
    return tb
end

function pb.pb_EquipmentRentalPlanDetailEncode(tb, encoder)
    if(tb.consumable_id) then    encoder:addu64(1, tb.consumable_id)    end
    if(tb.type_id) then    encoder:addu32(2, tb.type_id)    end
    if(tb.preset_id) then    encoder:addu32(3, tb.preset_id)    end
    if(tb.details) then
        for i=1,#(tb.details) do
            pb.pb_EquipmentRentalPropEncode(tb.details[i], encoder:addsubmsg(4))
        end
    end
    if(tb.preset_title) then    encoder:addstr(5, tb.preset_title)    end
    if(tb.refresh_price) then    encoder:addi64(6, tb.refresh_price)    end
    if(tb.refresh_enable) then    encoder:addbool(7, tb.refresh_enable)    end
    if(tb.preset_price) then    encoder:addu64(8, tb.preset_price)    end
    if(tb.consumable_type) then    encoder:addu32(10, tb.consumable_type)    end
end

function pb.pb_EquipmentRentalPropDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_EquipmentRentalProp) or {} 
    local __position = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __position ~= 0 then tb.position = __position end
    local __item_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __item_id ~= 0 then tb.item_id = __item_id end
    local __item_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __item_num ~= 0 then tb.item_num = __item_num end
    return tb
end

function pb.pb_EquipmentRentalPropEncode(tb, encoder)
    if(tb.position) then    encoder:addi32(1, tb.position)    end
    if(tb.item_id) then    encoder:addu64(2, tb.item_id)    end
    if(tb.item_num) then    encoder:addi64(3, tb.item_num)    end
end

function pb.pb_IDCDSRoomLoadDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IDCDSRoomLoad) or {} 
    local __ds_room_type = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __ds_room_type ~= 0 then tb.ds_room_type = __ds_room_type end
    local __unused_ds_room_num = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __unused_ds_room_num ~= 0 then tb.unused_ds_room_num = __unused_ds_room_num end
    local __max_ds_room_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __max_ds_room_num ~= 0 then tb.max_ds_room_num = __max_ds_room_num end
    local __used_ds_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __used_ds_num ~= 0 then tb.used_ds_num = __used_ds_num end
    return tb
end

function pb.pb_IDCDSRoomLoadEncode(tb, encoder)
    if(tb.ds_room_type) then    encoder:addi32(1, tb.ds_room_type)    end
    if(tb.unused_ds_room_num) then    encoder:addi64(2, tb.unused_ds_room_num)    end
    if(tb.max_ds_room_num) then    encoder:addi64(3, tb.max_ds_room_num)    end
    if(tb.used_ds_num) then    encoder:addi64(4, tb.used_ds_num)    end
end

function pb.pb_IDCDSLoadDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IDCDSLoad) or {} 
    local __ds_svr_count = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __ds_svr_count ~= 0 then tb.ds_svr_count = __ds_svr_count end
    local __used_ds_num = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __used_ds_num ~= 0 then tb.used_ds_num = __used_ds_num end
    local __max_ds_num = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __max_ds_num ~= 0 then tb.max_ds_num = __max_ds_num end
    local __ds_room_count = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __ds_room_count ~= 0 then tb.ds_room_count = __ds_room_count end
    tb.single_room_type = pb.pb_IDCDSTypeLoadDecode(decoder:getsubmsg(8))
    tb.multi_room_type = pb.pb_IDCDSTypeLoadDecode(decoder:getsubmsg(9))
    local __ds_svr_enabled_count = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __ds_svr_enabled_count ~= 0 then tb.ds_svr_enabled_count = __ds_svr_enabled_count end
    local __ds_svr_disabled_count = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __ds_svr_disabled_count ~= 0 then tb.ds_svr_disabled_count = __ds_svr_disabled_count end
    local __ds_svr_full_load_count = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __ds_svr_full_load_count ~= 0 then tb.ds_svr_full_load_count = __ds_svr_full_load_count end
    local __ds_svr_availabled_count = decoder:geti64(13)
    if not PB_USE_DEFAULT_TABLE or __ds_svr_availabled_count ~= 0 then tb.ds_svr_availabled_count = __ds_svr_availabled_count end
    tb.bhd_room_type = pb.pb_IDCDSTypeLoadDecode(decoder:getsubmsg(14))
    local __idc_total_score = decoder:geti64(15)
    if not PB_USE_DEFAULT_TABLE or __idc_total_score ~= 0 then tb.idc_total_score = __idc_total_score end
    local __idc_used_score = decoder:geti64(16)
    if not PB_USE_DEFAULT_TABLE or __idc_used_score ~= 0 then tb.idc_used_score = __idc_used_score end
    local __is_idc_score_load = decoder:getbool(17)
    if not PB_USE_DEFAULT_TABLE or __is_idc_score_load ~= false then tb.is_idc_score_load = __is_idc_score_load end
    local __curr_used_ds_num = decoder:geti64(18)
    if not PB_USE_DEFAULT_TABLE or __curr_used_ds_num ~= 0 then tb.curr_used_ds_num = __curr_used_ds_num end
    return tb
end

function pb.pb_IDCDSLoadEncode(tb, encoder)
    if(tb.ds_svr_count) then    encoder:addi64(2, tb.ds_svr_count)    end
    if(tb.used_ds_num) then    encoder:addi64(3, tb.used_ds_num)    end
    if(tb.max_ds_num) then    encoder:addi64(4, tb.max_ds_num)    end
    if(tb.ds_room_count) then    encoder:addi64(5, tb.ds_room_count)    end
    if(tb.single_room_type) then    pb.pb_IDCDSTypeLoadEncode(tb.single_room_type, encoder:addsubmsg(8))    end
    if(tb.multi_room_type) then    pb.pb_IDCDSTypeLoadEncode(tb.multi_room_type, encoder:addsubmsg(9))    end
    if(tb.ds_svr_enabled_count) then    encoder:addi64(10, tb.ds_svr_enabled_count)    end
    if(tb.ds_svr_disabled_count) then    encoder:addi64(11, tb.ds_svr_disabled_count)    end
    if(tb.ds_svr_full_load_count) then    encoder:addi64(12, tb.ds_svr_full_load_count)    end
    if(tb.ds_svr_availabled_count) then    encoder:addi64(13, tb.ds_svr_availabled_count)    end
    if(tb.bhd_room_type) then    pb.pb_IDCDSTypeLoadEncode(tb.bhd_room_type, encoder:addsubmsg(14))    end
    if(tb.idc_total_score) then    encoder:addi64(15, tb.idc_total_score)    end
    if(tb.idc_used_score) then    encoder:addi64(16, tb.idc_used_score)    end
    if(tb.is_idc_score_load) then    encoder:addbool(17, tb.is_idc_score_load)    end
    if(tb.curr_used_ds_num) then    encoder:addi64(18, tb.curr_used_ds_num)    end
end

function pb.pb_IDCDSTypeLoadDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IDCDSTypeLoad) or {} 
    local __ds_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __ds_type ~= 0 then tb.ds_type = __ds_type end
    local __effective_weight = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __effective_weight ~= 0 then tb.effective_weight = __effective_weight end
    local __max_ds_num = decoder:geti64(10)
    if not PB_USE_DEFAULT_TABLE or __max_ds_num ~= 0 then tb.max_ds_num = __max_ds_num end
    local __used_ds_num = decoder:geti64(11)
    if not PB_USE_DEFAULT_TABLE or __used_ds_num ~= 0 then tb.used_ds_num = __used_ds_num end
    local __unused_ds_num = decoder:geti64(12)
    if not PB_USE_DEFAULT_TABLE or __unused_ds_num ~= 0 then tb.unused_ds_num = __unused_ds_num end
    local __used_ds_room_count = decoder:geti64(100)
    if not PB_USE_DEFAULT_TABLE or __used_ds_room_count ~= 0 then tb.used_ds_room_count = __used_ds_room_count end
    return tb
end

function pb.pb_IDCDSTypeLoadEncode(tb, encoder)
    if(tb.ds_type) then    encoder:addu32(1, tb.ds_type)    end
    if(tb.effective_weight) then    encoder:addi64(2, tb.effective_weight)    end
    if(tb.max_ds_num) then    encoder:addi64(10, tb.max_ds_num)    end
    if(tb.used_ds_num) then    encoder:addi64(11, tb.used_ds_num)    end
    if(tb.unused_ds_num) then    encoder:addi64(12, tb.unused_ds_num)    end
    if(tb.used_ds_room_count) then    encoder:addi64(100, tb.used_ds_room_count)    end
end

function pb.pb_SSHelloReqDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SSHelloReq) or {} 
    return tb
end

function pb.pb_SSHelloReqEncode(tb, encoder)
end

function pb.pb_RoundtripAddrDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoundtripAddr) or {} 
    local __domain = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __domain ~= "" then tb.domain = __domain end
    local __vip = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __vip ~= "" then tb.vip = __vip end
    local __vport = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __vport ~= 0 then tb.vport = __vport end
    local __idc = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __idc ~= "" then tb.idc = __idc end
    local __access_point = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __access_point ~= "" then tb.access_point = __access_point end
    return tb
end

function pb.pb_RoundtripAddrEncode(tb, encoder)
    if(tb.domain) then    encoder:addstr(3, tb.domain)    end
    if(tb.vip) then    encoder:addstr(4, tb.vip)    end
    if(tb.vport) then    encoder:addu32(5, tb.vport)    end
    if(tb.idc) then    encoder:addstr(6, tb.idc)    end
    if(tb.access_point) then    encoder:addstr(7, tb.access_point)    end
end

function pb.pb_RoundtripIDCDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoundtripIDC) or {} 
    local __idc = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __idc ~= "" then tb.idc = __idc end
    tb.services = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.services[k] = pb.pb_RoundtripAddrDecode(v)
    end
    local __access_point = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __access_point ~= "" then tb.access_point = __access_point end
    return tb
end

function pb.pb_RoundtripIDCEncode(tb, encoder)
    if(tb.idc) then    encoder:addstr(1, tb.idc)    end
    if(tb.services) then
        for i=1,#(tb.services) do
            pb.pb_RoundtripAddrEncode(tb.services[i], encoder:addsubmsg(2))
        end
    end
    if(tb.access_point) then    encoder:addstr(3, tb.access_point)    end
end

function pb.pb_RoundtripTimeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RoundtripTime) or {} 
    local __rtt = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __rtt ~= 0 then tb.rtt = __rtt end
    tb.addr = pb.pb_RoundtripAddrDecode(decoder:getsubmsg(2))
    local __result = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __result ~= 0 then tb.result = __result end
    local __packet_loss = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __packet_loss ~= 0 then tb.packet_loss = __packet_loss end
    return tb
end

function pb.pb_RoundtripTimeEncode(tb, encoder)
    if(tb.rtt) then    encoder:addi32(1, tb.rtt)    end
    if(tb.addr) then    pb.pb_RoundtripAddrEncode(tb.addr, encoder:addsubmsg(2))    end
    if(tb.result) then    encoder:addu32(3, tb.result)    end
    if(tb.packet_loss) then    encoder:addi32(4, tb.packet_loss)    end
end

function pb.pb_IDCRoundtripTimeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_IDCRoundtripTime) or {} 
    local __idc = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __idc ~= "" then tb.idc = __idc end
    local __access_point = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __access_point ~= "" then tb.access_point = __access_point end
    tb.services = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.services[k] = pb.pb_RoundtripTimeDecode(v)
    end
    local __rtt = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __rtt ~= 0 then tb.rtt = __rtt end
    local __packet_loss = decoder:geti32(6)
    if not PB_USE_DEFAULT_TABLE or __packet_loss ~= 0 then tb.packet_loss = __packet_loss end
    return tb
end

function pb.pb_IDCRoundtripTimeEncode(tb, encoder)
    if(tb.idc) then    encoder:addstr(1, tb.idc)    end
    if(tb.access_point) then    encoder:addstr(5, tb.access_point)    end
    if(tb.services) then
        for i=1,#(tb.services) do
            pb.pb_RoundtripTimeEncode(tb.services[i], encoder:addsubmsg(3))
        end
    end
    if(tb.rtt) then    encoder:addi32(4, tb.rtt)    end
    if(tb.packet_loss) then    encoder:addi32(6, tb.packet_loss)    end
end

function pb.pb_SwitchConditionParamDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SwitchConditionParam) or {} 
    local __type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __param = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __param ~= 0 then tb.param = __param end
    return tb
end

function pb.pb_SwitchConditionParamEncode(tb, encoder)
    if(tb.type) then    encoder:addu32(1, tb.type)    end
    if(tb.param) then    encoder:addu64(2, tb.param)    end
end

function pb.pb_MidasTokenDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MidasToken) or {} 
    local __openid = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __openid ~= "" then tb.openid = __openid end
    local __openkey = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __openkey ~= "" then tb.openkey = __openkey end
    local __session_id = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __session_id ~= "" then tb.session_id = __session_id end
    local __session_type = decoder:getstr(4)
    if not PB_USE_DEFAULT_TABLE or __session_type ~= "" then tb.session_type = __session_type end
    local __pf = decoder:getstr(5)
    if not PB_USE_DEFAULT_TABLE or __pf ~= "" then tb.pf = __pf end
    local __pfkey = decoder:getstr(6)
    if not PB_USE_DEFAULT_TABLE or __pfkey ~= "" then tb.pfkey = __pfkey end
    local __reason = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __reason ~= "" then tb.reason = __reason end
    local __pay_channel = decoder:getstr(8)
    if not PB_USE_DEFAULT_TABLE or __pay_channel ~= "" then tb.pay_channel = __pay_channel end
    local __sub_channel = decoder:getstr(9)
    if not PB_USE_DEFAULT_TABLE or __sub_channel ~= "" then tb.sub_channel = __sub_channel end
    local __region = decoder:getstr(10)
    if not PB_USE_DEFAULT_TABLE or __region ~= "" then tb.region = __region end
    local __currency_type = decoder:getstr(11)
    if not PB_USE_DEFAULT_TABLE or __currency_type ~= "" then tb.currency_type = __currency_type end
    local __cancel_url = decoder:getstr(12)
    if not PB_USE_DEFAULT_TABLE or __cancel_url ~= "" then tb.cancel_url = __cancel_url end
    local __return_url = decoder:getstr(13)
    if not PB_USE_DEFAULT_TABLE or __return_url ~= "" then tb.return_url = __return_url end
    return tb
end

function pb.pb_MidasTokenEncode(tb, encoder)
    if(tb.openid) then    encoder:addstr(1, tb.openid)    end
    if(tb.openkey) then    encoder:addstr(2, tb.openkey)    end
    if(tb.session_id) then    encoder:addstr(3, tb.session_id)    end
    if(tb.session_type) then    encoder:addstr(4, tb.session_type)    end
    if(tb.pf) then    encoder:addstr(5, tb.pf)    end
    if(tb.pfkey) then    encoder:addstr(6, tb.pfkey)    end
    if(tb.reason) then    encoder:addstr(7, tb.reason)    end
    if(tb.pay_channel) then    encoder:addstr(8, tb.pay_channel)    end
    if(tb.sub_channel) then    encoder:addstr(9, tb.sub_channel)    end
    if(tb.region) then    encoder:addstr(10, tb.region)    end
    if(tb.currency_type) then    encoder:addstr(11, tb.currency_type)    end
    if(tb.cancel_url) then    encoder:addstr(12, tb.cancel_url)    end
    if(tb.return_url) then    encoder:addstr(13, tb.return_url)    end
end

function pb.pb_LotteryBoxDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LotteryBoxData) or {} 
    local __box_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __box_id ~= 0 then tb.box_id = __box_id end
    local __num = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __gid = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __gid ~= 0 then tb.gid = __gid end
    local __group_id = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __group_id ~= 0 then tb.group_id = __group_id end
    local __prop_id = decoder:getu64(5)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __opened_prop_id = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __opened_prop_id ~= 0 then tb.opened_prop_id = __opened_prop_id end
    local __opened_prop_gid = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __opened_prop_gid ~= 0 then tb.opened_prop_gid = __opened_prop_gid end
    local __opened_prop_num = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __opened_prop_num ~= 0 then tb.opened_prop_num = __opened_prop_num end
    return tb
end

function pb.pb_LotteryBoxDataEncode(tb, encoder)
    if(tb.box_id) then    encoder:addu64(1, tb.box_id)    end
    if(tb.num) then    encoder:addu32(2, tb.num)    end
    if(tb.gid) then    encoder:addu64(3, tb.gid)    end
    if(tb.group_id) then    encoder:addu32(4, tb.group_id)    end
    if(tb.prop_id) then    encoder:addu64(5, tb.prop_id)    end
    if(tb.opened_prop_id) then    encoder:addu64(6, tb.opened_prop_id)    end
    if(tb.opened_prop_gid) then    encoder:addu64(7, tb.opened_prop_gid)    end
    if(tb.opened_prop_num) then    encoder:addi64(8, tb.opened_prop_num)    end
end

function pb.pb_PaymentRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PaymentRecord) or {} 
    local __payment_idx = decoder:geti32(1)
    if not PB_USE_DEFAULT_TABLE or __payment_idx ~= 0 then tb.payment_idx = __payment_idx end
    local __bought_num = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __bought_num ~= 0 then tb.bought_num = __bought_num end
    tb.batch_item_list = decoder:getu64ary(3)
    return tb
end

function pb.pb_PaymentRecordEncode(tb, encoder)
    if(tb.payment_idx) then    encoder:addi32(1, tb.payment_idx)    end
    if(tb.bought_num) then    encoder:addi32(2, tb.bought_num)    end
    if(tb.batch_item_list) then    encoder:addu64(3, tb.batch_item_list)    end
end

function pb.pb_ShopBuyRecordDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_ShopBuyRecord) or {} 
    local __shop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __shop_id ~= 0 then tb.shop_id = __shop_id end
    local __bought_time = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __bought_time ~= 0 then tb.bought_time = __bought_time end
    tb.pay_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.pay_list[k] = pb.pb_PaymentRecordDecode(v)
    end
    local __bought_num = decoder:geti32(4)
    if not PB_USE_DEFAULT_TABLE or __bought_num ~= 0 then tb.bought_num = __bought_num end
    return tb
end

function pb.pb_ShopBuyRecordEncode(tb, encoder)
    if(tb.shop_id) then    encoder:addu64(1, tb.shop_id)    end
    if(tb.bought_time) then    encoder:addu64(2, tb.bought_time)    end
    if(tb.pay_list) then
        for i=1,#(tb.pay_list) do
            pb.pb_PaymentRecordEncode(tb.pay_list[i], encoder:addsubmsg(3))
        end
    end
    if(tb.bought_num) then    encoder:addi32(4, tb.bought_num)    end
end

function pb.pb_CSPunishNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPunishNtf) or {} 
    local __over_time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __over_time ~= 0 then tb.over_time = __over_time end
    local __reason = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __custom_reason = decoder:getstr(3)
    if not PB_USE_DEFAULT_TABLE or __custom_reason ~= "" then tb.custom_reason = __custom_reason end
    local __punish_start_time = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __punish_start_time ~= 0 then tb.punish_start_time = __punish_start_time end
    return tb
end

function pb.pb_CSPunishNtfEncode(tb, encoder)
    if(tb.over_time) then    encoder:addi64(1, tb.over_time)    end
    if(tb.reason) then    encoder:addu32(2, tb.reason)    end
    if(tb.custom_reason) then    encoder:addstr(3, tb.custom_reason)    end
    if(tb.punish_start_time) then    encoder:addi64(4, tb.punish_start_time)    end
end

function pb.pb_TssTLogsPkgDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TssTLogsPkg) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.logs = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.logs[k] = pb.pb_TssTLogsPkg_LogItemDecode(v)
    end
    return tb
end

function pb.pb_TssTLogsPkgEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.logs) then
        for i=1,#(tb.logs) do
            pb.pb_TssTLogsPkg_LogItemEncode(tb.logs[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_SSUpdateResDBNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SSUpdateResDBNtf) or {} 
    local __name = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __name ~= "" then tb.name = __name end
    return tb
end

function pb.pb_SSUpdateResDBNtfEncode(tb, encoder)
    if(tb.name) then    encoder:addstr(1, tb.name)    end
end

function pb.pb_CSUpdatePlayerBalanceNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSUpdatePlayerBalanceNtf) or {} 
    local __notify_time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __notify_time ~= 0 then tb.notify_time = __notify_time end
    local __reason = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    return tb
end

function pb.pb_CSUpdatePlayerBalanceNtfEncode(tb, encoder)
    if(tb.notify_time) then    encoder:addi64(1, tb.notify_time)    end
    if(tb.reason) then    encoder:addu32(2, tb.reason)    end
end

function pb.pb_CollectionPropInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CollectionPropInfo) or {} 
    local __id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __gid = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __gid ~= 0 then tb.gid = __gid end
    local __num = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __num ~= 0 then tb.num = __num end
    local __rights_id = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __rights_id ~= 0 then tb.rights_id = __rights_id end
    return tb
end

function pb.pb_CollectionPropInfoEncode(tb, encoder)
    if(tb.id) then    encoder:addu64(1, tb.id)    end
    if(tb.gid) then    encoder:addu64(2, tb.gid)    end
    if(tb.num) then    encoder:addi32(3, tb.num)    end
    if(tb.rights_id) then    encoder:addi64(4, tb.rights_id)    end
end

function pb.pb_CollectionRankTitleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CollectionRankTitleInfo) or {} 
    local __prop_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __adcode = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __adcode ~= 0 then tb.adcode = __adcode end
    local __rank_no = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __rank_no ~= 0 then tb.rank_no = __rank_no end
    return tb
end

function pb.pb_CollectionRankTitleInfoEncode(tb, encoder)
    if(tb.prop_id) then    encoder:addu64(1, tb.prop_id)    end
    if(tb.adcode) then    encoder:addu32(2, tb.adcode)    end
    if(tb.rank_no) then    encoder:addi64(3, tb.rank_no)    end
end

function pb.pb_UnlockDepartmentDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_UnlockDepartment) or {} 
    local __id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __id ~= 0 then tb.id = __id end
    local __is_unlock = decoder:getbool(2)
    if not PB_USE_DEFAULT_TABLE or __is_unlock ~= false then tb.is_unlock = __is_unlock end
    return tb
end

function pb.pb_UnlockDepartmentEncode(tb, encoder)
    if(tb.id) then    encoder:addu32(1, tb.id)    end
    if(tb.is_unlock) then    encoder:addbool(2, tb.is_unlock)    end
end

function pb.pb_RankShieldDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankShield) or {} 
    local __rank_id = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_id ~= 0 then tb.rank_id = __rank_id end
    local __used_shied = decoder:geti32(2)
    if not PB_USE_DEFAULT_TABLE or __used_shied ~= 0 then tb.used_shied = __used_shied end
    local __total_shied = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __total_shied ~= 0 then tb.total_shied = __total_shied end
    return tb
end

function pb.pb_RankShieldEncode(tb, encoder)
    if(tb.rank_id) then    encoder:addu32(1, tb.rank_id)    end
    if(tb.used_shied) then    encoder:addi32(2, tb.used_shied)    end
    if(tb.total_shied) then    encoder:addi32(3, tb.total_shied)    end
end

function pb.pb_CsvClientHashTableDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CsvClientHashTable) or {} 
    local __csv_names = decoder:getstr(1)
    if not PB_USE_DEFAULT_TABLE or __csv_names ~= "" then tb.csv_names = __csv_names end
    local __csv_hash = decoder:getstr(2)
    if not PB_USE_DEFAULT_TABLE or __csv_hash ~= "" then tb.csv_hash = __csv_hash end
    return tb
end

function pb.pb_CsvClientHashTableEncode(tb, encoder)
    if(tb.csv_names) then    encoder:addstr(1, tb.csv_names)    end
    if(tb.csv_hash) then    encoder:addstr(2, tb.csv_hash)    end
end

function pb.pb_SOLSeasonInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SOLSeasonInfo) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_escape = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_escape ~= 0 then tb.total_escape = __total_escape end
    local __total_game_time = decoder:getu64(3)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_kill = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __carry_teammate_assets = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __carry_teammate_assets ~= 0 then tb.carry_teammate_assets = __carry_teammate_assets end
    local __total_collection_price = decoder:getu64(8)
    if not PB_USE_DEFAULT_TABLE or __total_collection_price ~= 0 then tb.total_collection_price = __total_collection_price end
    local __total_gained_price = decoder:getu64(40)
    if not PB_USE_DEFAULT_TABLE or __total_gained_price ~= 0 then tb.total_gained_price = __total_gained_price end
    local __total_miss = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __total_miss ~= 0 then tb.total_miss = __total_miss end
    local __total_price = decoder:getu64(10)
    if not PB_USE_DEFAULT_TABLE or __total_price ~= 0 then tb.total_price = __total_price end
    local __total_fight_all = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __total_fight_all ~= 0 then tb.total_fight_all = __total_fight_all end
    local __total_quit = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __total_quit ~= 0 then tb.total_quit = __total_quit end
    local __total_giveup = decoder:getu32(13)
    if not PB_USE_DEFAULT_TABLE or __total_giveup ~= 0 then tb.total_giveup = __total_giveup end
    local __kill_low_stakes = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __kill_low_stakes ~= 0 then tb.kill_low_stakes = __kill_low_stakes end
    local __kill_med_stakes = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __kill_med_stakes ~= 0 then tb.kill_med_stakes = __kill_med_stakes end
    local __kill_high_stakes = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __kill_high_stakes ~= 0 then tb.kill_high_stakes = __kill_high_stakes end
    local __killed_low_stakes = decoder:getu32(30)
    if not PB_USE_DEFAULT_TABLE or __killed_low_stakes ~= 0 then tb.killed_low_stakes = __killed_low_stakes end
    local __killed_med_stakes = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __killed_med_stakes ~= 0 then tb.killed_med_stakes = __killed_med_stakes end
    local __killed_high_stakes = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __killed_high_stakes ~= 0 then tb.killed_high_stakes = __killed_high_stakes end
    local __total_shoot = decoder:getu64(60)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu64(61)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_shoot_down = decoder:getu32(62)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_down ~= 0 then tb.total_shoot_down = __total_shoot_down end
    local __total_shoot_head_down = decoder:getu32(63)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_head_down ~= 0 then tb.total_shoot_head_down = __total_shoot_head_down end
    local __total_kill_ai = decoder:getu32(64)
    if not PB_USE_DEFAULT_TABLE or __total_kill_ai ~= 0 then tb.total_kill_ai = __total_kill_ai end
    local __total_kill_boss = decoder:getu32(65)
    if not PB_USE_DEFAULT_TABLE or __total_kill_boss ~= 0 then tb.total_kill_boss = __total_kill_boss end
    local __total_assist_kill = decoder:getu32(66)
    if not PB_USE_DEFAULT_TABLE or __total_assist_kill ~= 0 then tb.total_assist_kill = __total_assist_kill end
    local __total_contract_price = decoder:getu64(70)
    if not PB_USE_DEFAULT_TABLE or __total_contract_price ~= 0 then tb.total_contract_price = __total_contract_price end
    local __total_bring_mandel_brick = decoder:getu32(71)
    if not PB_USE_DEFAULT_TABLE or __total_bring_mandel_brick ~= 0 then tb.total_bring_mandel_brick = __total_bring_mandel_brick end
    local __total_bring_gold_sku = decoder:getu32(72)
    if not PB_USE_DEFAULT_TABLE or __total_bring_gold_sku ~= 0 then tb.total_bring_gold_sku = __total_bring_gold_sku end
    local __total_bring_red_sku = decoder:getu32(73)
    if not PB_USE_DEFAULT_TABLE or __total_bring_red_sku ~= 0 then tb.total_bring_red_sku = __total_bring_red_sku end
    local __total_pickup_teammate = decoder:getu32(80)
    if not PB_USE_DEFAULT_TABLE or __total_pickup_teammate ~= 0 then tb.total_pickup_teammate = __total_pickup_teammate end
    local __total_revive_teammate = decoder:getu32(81)
    if not PB_USE_DEFAULT_TABLE or __total_revive_teammate ~= 0 then tb.total_revive_teammate = __total_revive_teammate end
    local __total_move = decoder:getu64(90)
    if not PB_USE_DEFAULT_TABLE or __total_move ~= 0 then tb.total_move = __total_move end
    local __total_search_cnt = decoder:getu32(91)
    if not PB_USE_DEFAULT_TABLE or __total_search_cnt ~= 0 then tb.total_search_cnt = __total_search_cnt end
    tb.recent_match_data = {}
    for k,v in pairs(decoder:getsubmsgary(100)) do
        tb.recent_match_data[k] = pb.pb_SOLRadarDataDecode(v)
    end
    return tb
end

function pb.pb_SOLSeasonInfoEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_escape) then    encoder:addu32(2, tb.total_escape)    end
    if(tb.total_game_time) then    encoder:addu64(3, tb.total_game_time)    end
    if(tb.total_kill) then    encoder:addu32(5, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(6, tb.total_killed)    end
    if(tb.carry_teammate_assets) then    encoder:addu64(7, tb.carry_teammate_assets)    end
    if(tb.total_collection_price) then    encoder:addu64(8, tb.total_collection_price)    end
    if(tb.total_gained_price) then    encoder:addu64(40, tb.total_gained_price)    end
    if(tb.total_miss) then    encoder:addu32(9, tb.total_miss)    end
    if(tb.total_price) then    encoder:addu64(10, tb.total_price)    end
    if(tb.total_fight_all) then    encoder:addu32(11, tb.total_fight_all)    end
    if(tb.total_quit) then    encoder:addu32(12, tb.total_quit)    end
    if(tb.total_giveup) then    encoder:addu32(13, tb.total_giveup)    end
    if(tb.kill_low_stakes) then    encoder:addu32(20, tb.kill_low_stakes)    end
    if(tb.kill_med_stakes) then    encoder:addu32(21, tb.kill_med_stakes)    end
    if(tb.kill_high_stakes) then    encoder:addu32(22, tb.kill_high_stakes)    end
    if(tb.killed_low_stakes) then    encoder:addu32(30, tb.killed_low_stakes)    end
    if(tb.killed_med_stakes) then    encoder:addu32(31, tb.killed_med_stakes)    end
    if(tb.killed_high_stakes) then    encoder:addu32(32, tb.killed_high_stakes)    end
    if(tb.total_shoot) then    encoder:addu64(60, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu64(61, tb.total_shoot_hit)    end
    if(tb.total_shoot_down) then    encoder:addu32(62, tb.total_shoot_down)    end
    if(tb.total_shoot_head_down) then    encoder:addu32(63, tb.total_shoot_head_down)    end
    if(tb.total_kill_ai) then    encoder:addu32(64, tb.total_kill_ai)    end
    if(tb.total_kill_boss) then    encoder:addu32(65, tb.total_kill_boss)    end
    if(tb.total_assist_kill) then    encoder:addu32(66, tb.total_assist_kill)    end
    if(tb.total_contract_price) then    encoder:addu64(70, tb.total_contract_price)    end
    if(tb.total_bring_mandel_brick) then    encoder:addu32(71, tb.total_bring_mandel_brick)    end
    if(tb.total_bring_gold_sku) then    encoder:addu32(72, tb.total_bring_gold_sku)    end
    if(tb.total_bring_red_sku) then    encoder:addu32(73, tb.total_bring_red_sku)    end
    if(tb.total_pickup_teammate) then    encoder:addu32(80, tb.total_pickup_teammate)    end
    if(tb.total_revive_teammate) then    encoder:addu32(81, tb.total_revive_teammate)    end
    if(tb.total_move) then    encoder:addu64(90, tb.total_move)    end
    if(tb.total_search_cnt) then    encoder:addu32(91, tb.total_search_cnt)    end
    if(tb.recent_match_data) then
        for i=1,#(tb.recent_match_data) do
            pb.pb_SOLRadarDataEncode(tb.recent_match_data[i], encoder:addsubmsg(100))
        end
    end
end

function pb.pb_SOLRadarDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SOLRadarData) or {} 
    local __match_time = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __match_time ~= 0 then tb.match_time = __match_time end
    local __total_pickup_teammate = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __total_pickup_teammate ~= 0 then tb.total_pickup_teammate = __total_pickup_teammate end
    local __total_revive_teammate = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __total_revive_teammate ~= 0 then tb.total_revive_teammate = __total_revive_teammate end
    local __total_assist_kill = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __total_assist_kill ~= 0 then tb.total_assist_kill = __total_assist_kill end
    local __total_kill_player = decoder:getu32(20)
    if not PB_USE_DEFAULT_TABLE or __total_kill_player ~= 0 then tb.total_kill_player = __total_kill_player end
    local __total_kill_ai = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __total_kill_ai ~= 0 then tb.total_kill_ai = __total_kill_ai end
    local __total_kill_boss = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __total_kill_boss ~= 0 then tb.total_kill_boss = __total_kill_boss end
    local __total_move = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __total_move ~= 0 then tb.total_move = __total_move end
    local __total_search_cnt = decoder:getu32(31)
    if not PB_USE_DEFAULT_TABLE or __total_search_cnt ~= 0 then tb.total_search_cnt = __total_search_cnt end
    local __total_contract_price = decoder:getu64(32)
    if not PB_USE_DEFAULT_TABLE or __total_contract_price ~= 0 then tb.total_contract_price = __total_contract_price end
    local __total_escape = decoder:getu32(40)
    if not PB_USE_DEFAULT_TABLE or __total_escape ~= 0 then tb.total_escape = __total_escape end
    local __total_game_time = decoder:getu64(41)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_gained_price = decoder:getu64(50)
    if not PB_USE_DEFAULT_TABLE or __total_gained_price ~= 0 then tb.total_gained_price = __total_gained_price end
    local __total_bring_mandel_brick = decoder:getu32(51)
    if not PB_USE_DEFAULT_TABLE or __total_bring_mandel_brick ~= 0 then tb.total_bring_mandel_brick = __total_bring_mandel_brick end
    local __total_bring_gold_sku = decoder:getu32(52)
    if not PB_USE_DEFAULT_TABLE or __total_bring_gold_sku ~= 0 then tb.total_bring_gold_sku = __total_bring_gold_sku end
    local __total_bring_red_sku = decoder:getu32(53)
    if not PB_USE_DEFAULT_TABLE or __total_bring_red_sku ~= 0 then tb.total_bring_red_sku = __total_bring_red_sku end
    return tb
end

function pb.pb_SOLRadarDataEncode(tb, encoder)
    if(tb.match_time) then    encoder:addu64(1, tb.match_time)    end
    if(tb.total_pickup_teammate) then    encoder:addu32(10, tb.total_pickup_teammate)    end
    if(tb.total_revive_teammate) then    encoder:addu32(11, tb.total_revive_teammate)    end
    if(tb.total_assist_kill) then    encoder:addu32(12, tb.total_assist_kill)    end
    if(tb.total_kill_player) then    encoder:addu32(20, tb.total_kill_player)    end
    if(tb.total_kill_ai) then    encoder:addu32(21, tb.total_kill_ai)    end
    if(tb.total_kill_boss) then    encoder:addu32(22, tb.total_kill_boss)    end
    if(tb.total_move) then    encoder:addu64(30, tb.total_move)    end
    if(tb.total_search_cnt) then    encoder:addu32(31, tb.total_search_cnt)    end
    if(tb.total_contract_price) then    encoder:addu64(32, tb.total_contract_price)    end
    if(tb.total_escape) then    encoder:addu32(40, tb.total_escape)    end
    if(tb.total_game_time) then    encoder:addu64(41, tb.total_game_time)    end
    if(tb.total_gained_price) then    encoder:addu64(50, tb.total_gained_price)    end
    if(tb.total_bring_mandel_brick) then    encoder:addu32(51, tb.total_bring_mandel_brick)    end
    if(tb.total_bring_gold_sku) then    encoder:addu32(52, tb.total_bring_gold_sku)    end
    if(tb.total_bring_red_sku) then    encoder:addu32(53, tb.total_bring_red_sku)    end
end

function pb.pb_RaidSeasonInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RaidSeasonInfo) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_pass = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_pass ~= 0 then tb.total_pass = __total_pass end
    local __total_pass_perfect = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_pass_perfect ~= 0 then tb.total_pass_perfect = __total_pass_perfect end
    local __hell_mode_1st_pass = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __hell_mode_1st_pass ~= 0 then tb.hell_mode_1st_pass = __hell_mode_1st_pass end
    local __hell_mode_fastest_pass = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __hell_mode_fastest_pass ~= 0 then tb.hell_mode_fastest_pass = __hell_mode_fastest_pass end
    return tb
end

function pb.pb_RaidSeasonInfoEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_pass) then    encoder:addu32(2, tb.total_pass)    end
    if(tb.total_pass_perfect) then    encoder:addu32(3, tb.total_pass_perfect)    end
    if(tb.hell_mode_1st_pass) then    encoder:addu32(4, tb.hell_mode_1st_pass)    end
    if(tb.hell_mode_fastest_pass) then    encoder:addu32(5, tb.hell_mode_fastest_pass)    end
end

function pb.pb_MPSeasonInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MPSeasonInfo) or {} 
    local __total_fight = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __total_fight ~= 0 then tb.total_fight = __total_fight end
    local __total_win = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __total_win ~= 0 then tb.total_win = __total_win end
    local __total_kill = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_killed = decoder:getu32(4)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __total_mvp = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __total_mvp ~= 0 then tb.total_mvp = __total_mvp end
    local __total_game_time = decoder:getu64(6)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_score = decoder:getu64(7)
    if not PB_USE_DEFAULT_TABLE or __total_score ~= 0 then tb.total_score = __total_score end
    local __total_quit = decoder:getu32(8)
    if not PB_USE_DEFAULT_TABLE or __total_quit ~= 0 then tb.total_quit = __total_quit end
    local __total_help = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __total_help ~= 0 then tb.total_help = __total_help end
    local __total_assist = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __total_assist ~= 0 then tb.total_assist = __total_assist end
    local __total_fight_as_commander = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __total_fight_as_commander ~= 0 then tb.total_fight_as_commander = __total_fight_as_commander end
    local __total_win_as_commander = decoder:getu32(12)
    if not PB_USE_DEFAULT_TABLE or __total_win_as_commander ~= 0 then tb.total_win_as_commander = __total_win_as_commander end
    local __total_shoot = decoder:getu64(20)
    if not PB_USE_DEFAULT_TABLE or __total_shoot ~= 0 then tb.total_shoot = __total_shoot end
    local __total_shoot_hit = decoder:getu64(21)
    if not PB_USE_DEFAULT_TABLE or __total_shoot_hit ~= 0 then tb.total_shoot_hit = __total_shoot_hit end
    local __total_kill_head = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __total_kill_head ~= 0 then tb.total_kill_head = __total_kill_head end
    local __total_kill_long_range = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __total_kill_long_range ~= 0 then tb.total_kill_long_range = __total_kill_long_range end
    local __total_capture_point = decoder:getu32(24)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point ~= 0 then tb.total_capture_point = __total_capture_point end
    local __total_capture_point_time = decoder:getu64(25)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point_time ~= 0 then tb.total_capture_point_time = __total_capture_point_time end
    local __total_vehicle_use_time = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_use_time ~= 0 then tb.total_vehicle_use_time = __total_vehicle_use_time end
    local __total_damage_to_vehicle = decoder:getu64(31)
    if not PB_USE_DEFAULT_TABLE or __total_damage_to_vehicle ~= 0 then tb.total_damage_to_vehicle = __total_damage_to_vehicle end
    local __total_vehicle_kill = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_kill ~= 0 then tb.total_vehicle_kill = __total_vehicle_kill end
    local __total_vehicle_destroyed = decoder:getu32(33)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_destroyed ~= 0 then tb.total_vehicle_destroyed = __total_vehicle_destroyed end
    local __total_vehicle_repair = decoder:getu64(34)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_repair ~= 0 then tb.total_vehicle_repair = __total_vehicle_repair end
    tb.recent_match_data = {}
    for k,v in pairs(decoder:getsubmsgary(100)) do
        tb.recent_match_data[k] = pb.pb_MpRadarDataDecode(v)
    end
    local __stat_by_day = decoder:getbool(201)
    if not PB_USE_DEFAULT_TABLE or __stat_by_day ~= false then tb.stat_by_day = __stat_by_day end
    local __start_time = decoder:getu32(202)
    if not PB_USE_DEFAULT_TABLE or __start_time ~= 0 then tb.start_time = __start_time end
    local __end_time = decoder:getu32(203)
    if not PB_USE_DEFAULT_TABLE or __end_time ~= 0 then tb.end_time = __end_time end
    return tb
end

function pb.pb_MPSeasonInfoEncode(tb, encoder)
    if(tb.total_fight) then    encoder:addu32(1, tb.total_fight)    end
    if(tb.total_win) then    encoder:addu32(2, tb.total_win)    end
    if(tb.total_kill) then    encoder:addu32(3, tb.total_kill)    end
    if(tb.total_killed) then    encoder:addu32(4, tb.total_killed)    end
    if(tb.total_mvp) then    encoder:addu32(5, tb.total_mvp)    end
    if(tb.total_game_time) then    encoder:addu64(6, tb.total_game_time)    end
    if(tb.total_score) then    encoder:addu64(7, tb.total_score)    end
    if(tb.total_quit) then    encoder:addu32(8, tb.total_quit)    end
    if(tb.total_help) then    encoder:addu32(9, tb.total_help)    end
    if(tb.total_assist) then    encoder:addu32(10, tb.total_assist)    end
    if(tb.total_fight_as_commander) then    encoder:addu32(11, tb.total_fight_as_commander)    end
    if(tb.total_win_as_commander) then    encoder:addu32(12, tb.total_win_as_commander)    end
    if(tb.total_shoot) then    encoder:addu64(20, tb.total_shoot)    end
    if(tb.total_shoot_hit) then    encoder:addu64(21, tb.total_shoot_hit)    end
    if(tb.total_kill_head) then    encoder:addu32(22, tb.total_kill_head)    end
    if(tb.total_kill_long_range) then    encoder:addu32(23, tb.total_kill_long_range)    end
    if(tb.total_capture_point) then    encoder:addu32(24, tb.total_capture_point)    end
    if(tb.total_capture_point_time) then    encoder:addu64(25, tb.total_capture_point_time)    end
    if(tb.total_vehicle_use_time) then    encoder:addu64(30, tb.total_vehicle_use_time)    end
    if(tb.total_damage_to_vehicle) then    encoder:addu64(31, tb.total_damage_to_vehicle)    end
    if(tb.total_vehicle_kill) then    encoder:addu32(32, tb.total_vehicle_kill)    end
    if(tb.total_vehicle_destroyed) then    encoder:addu32(33, tb.total_vehicle_destroyed)    end
    if(tb.total_vehicle_repair) then    encoder:addu64(34, tb.total_vehicle_repair)    end
    if(tb.recent_match_data) then
        for i=1,#(tb.recent_match_data) do
            pb.pb_MpRadarDataEncode(tb.recent_match_data[i], encoder:addsubmsg(100))
        end
    end
    if(tb.stat_by_day) then    encoder:addbool(201, tb.stat_by_day)    end
    if(tb.start_time) then    encoder:addu32(202, tb.start_time)    end
    if(tb.end_time) then    encoder:addu32(203, tb.end_time)    end
end

function pb.pb_MpRadarDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MpRadarData) or {} 
    local __match_time = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __match_time ~= 0 then tb.match_time = __match_time end
    local __total_vehicle_kill = decoder:getu32(10)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_kill ~= 0 then tb.total_vehicle_kill = __total_vehicle_kill end
    local __total_vehicle_destroyed = decoder:getu32(11)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_destroyed ~= 0 then tb.total_vehicle_destroyed = __total_vehicle_destroyed end
    local __total_damage_to_vehicle = decoder:getu64(12)
    if not PB_USE_DEFAULT_TABLE or __total_damage_to_vehicle ~= 0 then tb.total_damage_to_vehicle = __total_damage_to_vehicle end
    local __total_kill = decoder:getu32(21)
    if not PB_USE_DEFAULT_TABLE or __total_kill ~= 0 then tb.total_kill = __total_kill end
    local __total_kill_head = decoder:getu32(22)
    if not PB_USE_DEFAULT_TABLE or __total_kill_head ~= 0 then tb.total_kill_head = __total_kill_head end
    local __total_kill_long_range = decoder:getu32(23)
    if not PB_USE_DEFAULT_TABLE or __total_kill_long_range ~= 0 then tb.total_kill_long_range = __total_kill_long_range end
    local __total_vehicle_repair = decoder:getu64(30)
    if not PB_USE_DEFAULT_TABLE or __total_vehicle_repair ~= 0 then tb.total_vehicle_repair = __total_vehicle_repair end
    local __total_game_time = decoder:getu64(31)
    if not PB_USE_DEFAULT_TABLE or __total_game_time ~= 0 then tb.total_game_time = __total_game_time end
    local __total_killed = decoder:getu32(32)
    if not PB_USE_DEFAULT_TABLE or __total_killed ~= 0 then tb.total_killed = __total_killed end
    local __total_capture_point_time = decoder:getu64(40)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point_time ~= 0 then tb.total_capture_point_time = __total_capture_point_time end
    local __total_capture_point = decoder:getu32(41)
    if not PB_USE_DEFAULT_TABLE or __total_capture_point ~= 0 then tb.total_capture_point = __total_capture_point end
    local __total_help = decoder:getu32(50)
    if not PB_USE_DEFAULT_TABLE or __total_help ~= 0 then tb.total_help = __total_help end
    local __total_assist = decoder:getu32(51)
    if not PB_USE_DEFAULT_TABLE or __total_assist ~= 0 then tb.total_assist = __total_assist end
    return tb
end

function pb.pb_MpRadarDataEncode(tb, encoder)
    if(tb.match_time) then    encoder:addu64(1, tb.match_time)    end
    if(tb.total_vehicle_kill) then    encoder:addu32(10, tb.total_vehicle_kill)    end
    if(tb.total_vehicle_destroyed) then    encoder:addu32(11, tb.total_vehicle_destroyed)    end
    if(tb.total_damage_to_vehicle) then    encoder:addu64(12, tb.total_damage_to_vehicle)    end
    if(tb.total_kill) then    encoder:addu32(21, tb.total_kill)    end
    if(tb.total_kill_head) then    encoder:addu32(22, tb.total_kill_head)    end
    if(tb.total_kill_long_range) then    encoder:addu32(23, tb.total_kill_long_range)    end
    if(tb.total_vehicle_repair) then    encoder:addu64(30, tb.total_vehicle_repair)    end
    if(tb.total_game_time) then    encoder:addu64(31, tb.total_game_time)    end
    if(tb.total_killed) then    encoder:addu32(32, tb.total_killed)    end
    if(tb.total_capture_point_time) then    encoder:addu64(40, tb.total_capture_point_time)    end
    if(tb.total_capture_point) then    encoder:addu32(41, tb.total_capture_point)    end
    if(tb.total_help) then    encoder:addu32(50, tb.total_help)    end
    if(tb.total_assist) then    encoder:addu32(51, tb.total_assist)    end
end

function pb.pb_TaskGoalItemDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_TaskGoalItem) or {} 
    local __task_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __task_id ~= 0 then tb.task_id = __task_id end
    local __goal_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __goal_id ~= 0 then tb.goal_id = __goal_id end
    local __cur_value = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __cur_value ~= 0 then tb.cur_value = __cur_value end
    local __max_value = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __max_value ~= 0 then tb.max_value = __max_value end
    local __type = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    return tb
end

function pb.pb_TaskGoalItemEncode(tb, encoder)
    if(tb.task_id) then    encoder:addu64(1, tb.task_id)    end
    if(tb.goal_id) then    encoder:addu64(2, tb.goal_id)    end
    if(tb.cur_value) then    encoder:addi64(3, tb.cur_value)    end
    if(tb.max_value) then    encoder:addi64(4, tb.max_value)    end
    if(tb.type) then    encoder:addu32(5, tb.type)    end
end

function pb.pb_CSPopularityNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSPopularityNtf) or {} 
    local __trigger_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __trigger_type ~= 0 then tb.trigger_type = __trigger_type end
    return tb
end

function pb.pb_CSPopularityNtfEncode(tb, encoder)
    if(tb.trigger_type) then    encoder:addu32(1, tb.trigger_type)    end
end

function pb.pb_SSCurrencyChangeNtfDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SSCurrencyChangeNtf) or {} 
    tb.deposit_change = pb.pb_DataChangeDecode(decoder:getsubmsg(1))
    return tb
end

function pb.pb_SSCurrencyChangeNtfEncode(tb, encoder)
    if(tb.deposit_change) then    pb.pb_DataChangeEncode(tb.deposit_change, encoder:addsubmsg(1))    end
end

function pb.pb_LimitSocialInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_LimitSocialInfo) or {} 
    local __type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __type ~= 0 then tb.type = __type end
    local __effective_start_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __effective_start_time ~= 0 then tb.effective_start_time = __effective_start_time end
    local __expired_time = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __expired_time ~= 0 then tb.expired_time = __expired_time end
    local __prop_id = decoder:getu64(4)
    if not PB_USE_DEFAULT_TABLE or __prop_id ~= 0 then tb.prop_id = __prop_id end
    local __rank_title_adcode = decoder:getu32(5)
    if not PB_USE_DEFAULT_TABLE or __rank_title_adcode ~= 0 then tb.rank_title_adcode = __rank_title_adcode end
    local __rank_title_rank_no = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __rank_title_rank_no ~= 0 then tb.rank_title_rank_no = __rank_title_rank_no end
    return tb
end

function pb.pb_LimitSocialInfoEncode(tb, encoder)
    if(tb.type) then    encoder:addu32(1, tb.type)    end
    if(tb.effective_start_time) then    encoder:addi64(2, tb.effective_start_time)    end
    if(tb.expired_time) then    encoder:addi64(3, tb.expired_time)    end
    if(tb.prop_id) then    encoder:addu64(4, tb.prop_id)    end
    if(tb.rank_title_adcode) then    encoder:addu32(5, tb.rank_title_adcode)    end
    if(tb.rank_title_rank_no) then    encoder:addi64(6, tb.rank_title_rank_no)    end
end

function pb.pb_BhdSpecDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_BhdSpecData) or {} 
    local __bhd_special_hp = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __bhd_special_hp ~= 0 then tb.bhd_special_hp = __bhd_special_hp end
    local __bhd_special_ammo = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __bhd_special_ammo ~= 0 then tb.bhd_special_ammo = __bhd_special_ammo end
    return tb
end

function pb.pb_BhdSpecDataEncode(tb, encoder)
    if(tb.bhd_special_hp) then    encoder:addu64(1, tb.bhd_special_hp)    end
    if(tb.bhd_special_ammo) then    encoder:addu64(2, tb.bhd_special_ammo)    end
end

function pb.pb_RecoveryAmountChangeDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RecoveryAmountChange) or {} 
    local __time = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __time ~= 0 then tb.time = __time end
    local __event_id = decoder:getu32(2)
    if not PB_USE_DEFAULT_TABLE or __event_id ~= 0 then tb.event_id = __event_id end
    local __reason = decoder:geti32(3)
    if not PB_USE_DEFAULT_TABLE or __reason ~= 0 then tb.reason = __reason end
    local __money_paper = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __money_paper ~= 0 then tb.money_paper = __money_paper end
    local __recovery_level = decoder:geti32(5)
    if not PB_USE_DEFAULT_TABLE or __recovery_level ~= 0 then tb.recovery_level = __recovery_level end
    local __recovery_rate = decoder:getdouble(6)
    if not PB_USE_DEFAULT_TABLE or __recovery_rate ~= 0 then tb.recovery_rate = __recovery_rate end
    local __recovery_amount = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __recovery_amount ~= 0 then tb.recovery_amount = __recovery_amount end
    local __amount = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __amount ~= 0 then tb.amount = __amount end
    local __old_amount = decoder:geti64(9)
    if not PB_USE_DEFAULT_TABLE or __old_amount ~= 0 then tb.old_amount = __old_amount end
    local __old_level = decoder:geti32(10)
    if not PB_USE_DEFAULT_TABLE or __old_level ~= 0 then tb.old_level = __old_level end
    local __old_rate = decoder:getdouble(11)
    if not PB_USE_DEFAULT_TABLE or __old_rate ~= 0 then tb.old_rate = __old_rate end
    return tb
end

function pb.pb_RecoveryAmountChangeEncode(tb, encoder)
    if(tb.time) then    encoder:addi64(1, tb.time)    end
    if(tb.event_id) then    encoder:addu32(2, tb.event_id)    end
    if(tb.reason) then    encoder:addi32(3, tb.reason)    end
    if(tb.money_paper) then    encoder:addi64(4, tb.money_paper)    end
    if(tb.recovery_level) then    encoder:addi32(5, tb.recovery_level)    end
    if(tb.recovery_rate) then    encoder:adddouble(6, tb.recovery_rate)    end
    if(tb.recovery_amount) then    encoder:addi64(7, tb.recovery_amount)    end
    if(tb.amount) then    encoder:addi64(8, tb.amount)    end
    if(tb.old_amount) then    encoder:addi64(9, tb.old_amount)    end
    if(tb.old_level) then    encoder:addi32(10, tb.old_level)    end
    if(tb.old_rate) then    encoder:adddouble(11, tb.old_rate)    end
end

function pb.pb_RankHandBookStateInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankHandBookStateInfo) or {} 
    local __state = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __state ~= 0 then tb.state = __state end
    local __unlock_time = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __unlock_time ~= 0 then tb.unlock_time = __unlock_time end
    return tb
end

function pb.pb_RankHandBookStateInfoEncode(tb, encoder)
    if(tb.state) then    encoder:addu32(1, tb.state)    end
    if(tb.unlock_time) then    encoder:addi64(2, tb.unlock_time)    end
end

function pb.pb_RankSingleHandBookDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_RankSingleHandBookData) or {} 
    tb.hand_book_state = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.hand_book_state[k] = pb.pb_RankHandBookStateInfoDecode(v)
    end
    return tb
end

function pb.pb_RankSingleHandBookDataEncode(tb, encoder)
    if(tb.hand_book_state) then
        for i=1,#(tb.hand_book_state) do
            pb.pb_RankHandBookStateInfoEncode(tb.hand_book_state[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_CSRankPlayerAuxDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CSRankPlayerAuxData) or {} 
    local __rank_data_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_data_type ~= 0 then tb.rank_data_type = __rank_data_type end
    local __score = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __score ~= 0 then tb.score = __score end
    return tb
end

function pb.pb_CSRankPlayerAuxDataEncode(tb, encoder)
    if(tb.rank_data_type) then    encoder:addu32(1, tb.rank_data_type)    end
    if(tb.score) then    encoder:addi64(2, tb.score)    end
end

function pb.pb_PlayerRankPercentageInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerRankPercentageInfo) or {} 
    local __rank_data_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_data_type ~= 0 then tb.rank_data_type = __rank_data_type end
    local __percentage = decoder:getfloat(2)
    if not PB_USE_DEFAULT_TABLE or __percentage ~= 0 then tb.percentage = __percentage end
    return tb
end

function pb.pb_PlayerRankPercentageInfoEncode(tb, encoder)
    if(tb.rank_data_type) then    encoder:addu32(1, tb.rank_data_type)    end
    if(tb.percentage) then    encoder:addfloat(2, tb.percentage)    end
end

function pb.pb_PlayerRankHistoricalTitleInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerRankHistoricalTitleInfo) or {} 
    local __rank_data_type = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __rank_data_type ~= 0 then tb.rank_data_type = __rank_data_type end
    local __title_id = decoder:getu64(2)
    if not PB_USE_DEFAULT_TABLE or __title_id ~= 0 then tb.title_id = __title_id end
    local __adCode = decoder:getu32(3)
    if not PB_USE_DEFAULT_TABLE or __adCode ~= 0 then tb.adCode = __adCode end
    local __ranking_no = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __ranking_no ~= 0 then tb.ranking_no = __ranking_no end
    local __timestamp = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __timestamp ~= 0 then tb.timestamp = __timestamp end
    local __region = decoder:getu32(6)
    if not PB_USE_DEFAULT_TABLE or __region ~= 0 then tb.region = __region end
    local __cycleinfo = decoder:getstr(7)
    if not PB_USE_DEFAULT_TABLE or __cycleinfo ~= "" then tb.cycleinfo = __cycleinfo end
    return tb
end

function pb.pb_PlayerRankHistoricalTitleInfoEncode(tb, encoder)
    if(tb.rank_data_type) then    encoder:addu32(1, tb.rank_data_type)    end
    if(tb.title_id) then    encoder:addu64(2, tb.title_id)    end
    if(tb.adCode) then    encoder:addu32(3, tb.adCode)    end
    if(tb.ranking_no) then    encoder:addi64(4, tb.ranking_no)    end
    if(tb.timestamp) then    encoder:addi64(5, tb.timestamp)    end
    if(tb.region) then    encoder:addu32(6, tb.region)    end
    if(tb.cycleinfo) then    encoder:addstr(7, tb.cycleinfo)    end
end

function pb.pb_PlayerRankHistoricalSeasonInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerRankHistoricalSeasonInfo) or {} 
    local __season_no = decoder:getu32(1)
    if not PB_USE_DEFAULT_TABLE or __season_no ~= 0 then tb.season_no = __season_no end
    tb.title_list = {}
    for k,v in pairs(decoder:getsubmsgary(2)) do
        tb.title_list[k] = pb.pb_PlayerRankHistoricalTitleInfoDecode(v)
    end
    return tb
end

function pb.pb_PlayerRankHistoricalSeasonInfoEncode(tb, encoder)
    if(tb.season_no) then    encoder:addu32(1, tb.season_no)    end
    if(tb.title_list) then
        for i=1,#(tb.title_list) do
            pb.pb_PlayerRankHistoricalTitleInfoEncode(tb.title_list[i], encoder:addsubmsg(2))
        end
    end
end

function pb.pb_PlayerRankHistoricalRecordInfoDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_PlayerRankHistoricalRecordInfo) or {} 
    tb.season_list = {}
    for k,v in pairs(decoder:getsubmsgary(1)) do
        tb.season_list[k] = pb.pb_PlayerRankHistoricalSeasonInfoDecode(v)
    end
    return tb
end

function pb.pb_PlayerRankHistoricalRecordInfoEncode(tb, encoder)
    if(tb.season_list) then
        for i=1,#(tb.season_list) do
            pb.pb_PlayerRankHistoricalSeasonInfoEncode(tb.season_list[i], encoder:addsubmsg(1))
        end
    end
end

function pb.pb_SinglePlayerAuxDataDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_SinglePlayerAuxData) or {} 
    local __player_id = decoder:getu64(1)
    if not PB_USE_DEFAULT_TABLE or __player_id ~= 0 then tb.player_id = __player_id end
    tb.rank_aux_data_list = {}
    for k,v in pairs(decoder:getsubmsgary(3)) do
        tb.rank_aux_data_list[k] = pb.pb_CSRankPlayerAuxDataDecode(v)
    end
    return tb
end

function pb.pb_SinglePlayerAuxDataEncode(tb, encoder)
    if(tb.player_id) then    encoder:addu64(1, tb.player_id)    end
    if(tb.rank_aux_data_list) then
        for i=1,#(tb.rank_aux_data_list) do
            pb.pb_CSRankPlayerAuxDataEncode(tb.rank_aux_data_list[i], encoder:addsubmsg(3))
        end
    end
end

function pb.pb_MpRankNumeralDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_MpRankNumeral) or {} 
    local __mp_rank_play = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_play ~= 0 then tb.mp_rank_play = __mp_rank_play end
    local __mp_rank_win = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_win ~= 0 then tb.mp_rank_win = __mp_rank_win end
    local __mp_rank_gametime = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_gametime ~= 0 then tb.mp_rank_gametime = __mp_rank_gametime end
    local __mp_rank_score = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_score ~= 0 then tb.mp_rank_score = __mp_rank_score end
    local __mp_rank_kill = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_kill ~= 0 then tb.mp_rank_kill = __mp_rank_kill end
    local __mp_rank_death = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __mp_rank_death ~= 0 then tb.mp_rank_death = __mp_rank_death end
    return tb
end

function pb.pb_MpRankNumeralEncode(tb, encoder)
    if(tb.mp_rank_play) then    encoder:addi64(1, tb.mp_rank_play)    end
    if(tb.mp_rank_win) then    encoder:addi64(2, tb.mp_rank_win)    end
    if(tb.mp_rank_gametime) then    encoder:addi64(3, tb.mp_rank_gametime)    end
    if(tb.mp_rank_score) then    encoder:addi64(4, tb.mp_rank_score)    end
    if(tb.mp_rank_kill) then    encoder:addi64(5, tb.mp_rank_kill)    end
    if(tb.mp_rank_death) then    encoder:addi64(6, tb.mp_rank_death)    end
end

function pb.pb_CommanderNumeralDecode(decoder)
    local PB_USE_DEFAULT_TABLE = PB_USE_DEFAULT_TABLE
    local tb = PB_USE_DEFAULT_TABLE and setmetatable({}, pb.__pb_CommanderNumeral) or {} 
    local __mp_commander_play = decoder:geti64(1)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_play ~= 0 then tb.mp_commander_play = __mp_commander_play end
    local __mp_commander_win = decoder:geti64(2)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_win ~= 0 then tb.mp_commander_win = __mp_commander_win end
    local __mp_commander_gametime = decoder:geti64(3)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_gametime ~= 0 then tb.mp_commander_gametime = __mp_commander_gametime end
    local __mp_commander_score = decoder:geti64(4)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_score ~= 0 then tb.mp_commander_score = __mp_commander_score end
    local __mp_commander_kill = decoder:geti64(5)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_kill ~= 0 then tb.mp_commander_kill = __mp_commander_kill end
    local __mp_commander_death = decoder:geti64(6)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_death ~= 0 then tb.mp_commander_death = __mp_commander_death end
    local __mp_commander_like = decoder:geti64(7)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_like ~= 0 then tb.mp_commander_like = __mp_commander_like end
    local __mp_commander_recommend = decoder:geti64(8)
    if not PB_USE_DEFAULT_TABLE or __mp_commander_recommend ~= 0 then tb.mp_commander_recommend = __mp_commander_recommend end
    local __total_fight_as_commander = decoder:getu32(9)
    if not PB_USE_DEFAULT_TABLE or __total_fight_as_commander ~= 0 then tb.total_fight_as_commander = __total_fight_as_commander end
    local __win_ratio = decoder:getfloat(10)
    if not PB_USE_DEFAULT_TABLE or __win_ratio ~= 0 then tb.win_ratio = __win_ratio end
    local __percentile_of_win_ratio = decoder:getfloat(11)
    if not PB_USE_DEFAULT_TABLE or __percentile_of_win_ratio ~= 0 then tb.percentile_of_win_ratio = __percentile_of_win_ratio end
    local __kill_by_vehicle_per_minute = decoder:getfloat(12)
    if not PB_USE_DEFAULT_TABLE or __kill_by_vehicle_per_minute ~= 0 then tb.kill_by_vehicle_per_minute = __kill_by_vehicle_per_minute end
    local __percentile_of_kill_by_vehicle_per_minute = decoder:getfloat(13)
    if not PB_USE_DEFAULT_TABLE or __percentile_of_kill_by_vehicle_per_minute ~= 0 then tb.percentile_of_kill_by_vehicle_per_minute = __percentile_of_kill_by_vehicle_per_minute end
    local __defeat_by_per_minute = decoder:getfloat(14)
    if not PB_USE_DEFAULT_TABLE or __defeat_by_per_minute ~= 0 then tb.defeat_by_per_minute = __defeat_by_per_minute end
    local __percentile_of_defeat_per_minute = decoder:getfloat(15)
    if not PB_USE_DEFAULT_TABLE or __percentile_of_defeat_per_minute ~= 0 then tb.percentile_of_defeat_per_minute = __percentile_of_defeat_per_minute end
    local __help_per_minute = decoder:getfloat(16)
    if not PB_USE_DEFAULT_TABLE or __help_per_minute ~= 0 then tb.help_per_minute = __help_per_minute end
    local __percentile_of_help_per_minute = decoder:getfloat(17)
    if not PB_USE_DEFAULT_TABLE or __percentile_of_help_per_minute ~= 0 then tb.percentile_of_help_per_minute = __percentile_of_help_per_minute end
    return tb
end

function pb.pb_CommanderNumeralEncode(tb, encoder)
    if(tb.mp_commander_play) then    encoder:addi64(1, tb.mp_commander_play)    end
    if(tb.mp_commander_win) then    encoder:addi64(2, tb.mp_commander_win)    end
    if(tb.mp_commander_gametime) then    encoder:addi64(3, tb.mp_commander_gametime)    end
    if(tb.mp_commander_score) then    encoder:addi64(4, tb.mp_commander_score)    end
    if(tb.mp_commander_kill) then    encoder:addi64(5, tb.mp_commander_kill)    end
    if(tb.mp_commander_death) then    encoder:addi64(6, tb.mp_commander_death)    end
    if(tb.mp_commander_like) then    encoder:addi64(7, tb.mp_commander_like)    end
    if(tb.mp_commander_recommend) then    encoder:addi64(8, tb.mp_commander_recommend)    end
    if(tb.total_fight_as_commander) then    encoder:addu32(9, tb.total_fight_as_commander)    end
    if(tb.win_ratio) then    encoder:addfloat(10, tb.win_ratio)    end
    if(tb.percentile_of_win_ratio) then    encoder:addfloat(11, tb.percentile_of_win_ratio)    end
    if(tb.kill_by_vehicle_per_minute) then    encoder:addfloat(12, tb.kill_by_vehicle_per_minute)    end
    if(tb.percentile_of_kill_by_vehicle_per_minute) then    encoder:addfloat(13, tb.percentile_of_kill_by_vehicle_per_minute)    end
    if(tb.defeat_by_per_minute) then    encoder:addfloat(14, tb.defeat_by_per_minute)    end
    if(tb.percentile_of_defeat_per_minute) then    encoder:addfloat(15, tb.percentile_of_defeat_per_minute)    end
    if(tb.help_per_minute) then    encoder:addfloat(16, tb.help_per_minute)    end
    if(tb.percentile_of_help_per_minute) then    encoder:addfloat(17, tb.percentile_of_help_per_minute)    end
end

