----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------


---@description [[文字列表通用弹窗]]
--- 标题
--- 子项
--- {
---     titleText=<titleText>,
---     descText=<descText>,
--- }

local CommonPopWindows = require "DFM.Business.Module.CommonWidgetModule.UI.CommonPopWindows"
local WidgetUtil                    = require "DFM.YxFramework.Util.WidgetUtil"

---@class CommonPopWindowV2Waterfall : LuaUIBaseView
local CommonPopWindowV2Waterfall = ui("CommonPopWindowV2Waterfall")

function CommonPopWindowV2Waterfall:Ctor()
    self._wtCommonPopWindow = self:Wnd("wtCommonPopWin", CommonPopWindows)
    self._wtWaterfallScrollView = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_Content", self._OnGetItemCount, self._OnProcessItemWidget)
    self._wtTbTitle = self:Wnd("DFTextBlock_ContentTitle", UITextBlock)
    self._wtImgTitleLine = self:Wnd("DFImage_Line", UIImage)
    self._wtSlotExtraContent = self:Wnd("DFNamedSlot_ExtraContent", UINamedSlot)

    local fCallbackIns = CreateCallBack(self._OnCloseBtnClicked, self)
    self._wtCommonPopWindow:BindCloseCallBack(fCallbackIns)
end

-----------------------------------------------------生命周期-----------------------------------------------------
function CommonPopWindowV2Waterfall:OnInitExtraData(windowTitleStr, contentTitleText, itemDescDataList, bShowTitle)
    self.windowTitleStr = setdefault(windowTitleStr, "")
    self.contentTitleText = contentTitleText
    self.itemDescDataList = itemDescDataList or {}
    self.bShowTitle = setdefault(bShowTitle, true)
end

function CommonPopWindowV2Waterfall:OnOpen()
    if self.windowTitleStr then
        self._wtCommonPopWindow:SetTitle(self.windowTitleStr)
    end
    self._wtWaterfallScrollView:RefreshAllItems()
        if self.bShowTitle then
            self._wtTbTitle:Visible()
            self._wtImgTitleLine:Visible()
        else
            self._wtTbTitle:Collapsed()
            self._wtImgTitleLine:Collapsed()
        end

    if self.contentTitleText then
        self._wtTbTitle:SetText(self.contentTitleText)
        self._wtTbTitle:Visible()
        self._wtImgTitleLine:Visible()
    else
        self._wtTbTitle:Collapsed()
        self._wtImgTitleLine:Collapsed()
    end
end

function CommonPopWindowV2Waterfall:OnClose()
    self.itemDescDataList = {}
end

function CommonPopWindowV2Waterfall:OnShowBegin()
    loginfo("CommonPopWindowV2Waterfall:OnShowBegin")    
    
    if IsHD() then
        self:_EnableGamepadFeature(true)
    end
end

function CommonPopWindowV2Waterfall:OnHideBegin()
    loginfo("CommonPopWindowV2Waterfall:OnHideBegin") 

    if IsHD() then
        self:_EnableGamepadFeature(false)
    end
end

function CommonPopWindowV2Waterfall:OnNavBack()
    self:_OnCloseBtnClicked()
    return true
end

function CommonPopWindowV2Waterfall:_OnCloseBtnClicked()
    Facade.UIManager:CloseUI(self)
end

function CommonPopWindowV2Waterfall:_OnGetItemCount()
    return #self.itemDescDataList or 0
end

function CommonPopWindowV2Waterfall:_OnProcessItemWidget(position, itemWidget)
    local itemData = self.itemDescDataList[position]
    itemWidget:SetData(itemData)
end

function CommonPopWindowV2Waterfall:_EnableGamepadFeature(bEnable)
    if not IsHD() then
        return
    end

    if bEnable then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:AddNavWidgetToArray(self)
                self._wtNavGroup:MarkIsStackControlGroup()            
                WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
                WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self, WidgetUtil.ENavConfigPriority.UI_Pop)
            end
        end
    else
        if self._wtNavGroup then
            WidgetUtil.RemoveNavigationGroup(self)
            self._wtNavGroup = nil
        end
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
end

-----------------------------------------------------Public API-----------------------------------------------------
function CommonPopWindowV2Waterfall:UpdateIsShowTitle(bShowTitle)
    self.bShowTitle = setdefault(bShowTitle, true)
    if self.bShowTitle then
        self._wtTbTitle:Visible()
        self._wtImgTitleLine:Visible()
    else
        self._wtTbTitle:Collapsed()
        self._wtImgTitleLine:Collapsed()
    end
end

function CommonPopWindowV2Waterfall:UpdateContentTitle(contentTitleText)
    self.contentTitleText = contentTitleText
    self._wtTbTitle:SetText(contentTitleText)
end

function CommonPopWindowV2Waterfall:UpdateItemDataDescList(itemDescDataList)
    self.itemDescDataList = itemDescDataList or {}
    self._wtWaterfallScrollView:RefreshAllItems()
end

function CommonPopWindowV2Waterfall:SetConfirmBtnType(eEConfirmBtnType, mapHandleType2BindData, bNeedBlock)
    self._wtCommonPopWindow:SetConfirmBtnType(eEConfirmBtnType, mapHandleType2BindData, bNeedBlock)
end

function CommonPopWindowV2Waterfall:GetCommonPopWindow()
    return self._wtCommonPopWindow
end

function CommonPopWindowV2Waterfall:GetSlotForExtraContent()
    return self._wtSlotExtraContent
end

return CommonPopWindowV2Waterfall
