----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class QuestTaskDetailItem : LuaUIBaseView
local QuestTaskDetailItem = ui("QuestTaskDetailItem")

function QuestTaskDetailItem:Ctor()
    self._wtQuestPlanText = self:Wnd("wQuestPlanText", UITextBlock)
    self._wtDFSizeBox2 = self:Wnd("DFSizeBox_2", UIWidgetBase)

    self._wtQuestObjectiveBox = self:Wnd("DFVerticalBox_336", UIWidgetBase)

    self._wtQuestConditionBox = self:Wnd("DFVerticalBox_ChallengeBox", UIWidgetBase)

    self._wtDFSizeBox_3 = self:Wnd("DFSizeBox_3", UIWidgetBase)
    self._wtWBP_CommonButtonV3S1_91 = self:Wnd("WBP_CommonButtonV3S1_91", DFCommonButtonOnly)
    self._wtWBP_CommonButtonV3S1_91:Event("OnClicked", self._OnClickBtn, self)
    self.DFCanvasPanel_108 = self:Wnd("DFCanvasPanel_108", UIWidgetBase)
    self._bShowUp = false
end

------------------------------------ Override function ------------------------------------

function QuestTaskDetailItem:OnOpen()
    self:_AddEventListener()
    -- print( "xhz >>>>>>>>>>>>> QuestTaskDetailItem OnOpen >>>>>>>>>>>>>>>> " )
    self:_UpdateQuestDetailInfo()
    if IsHD() then
        self:ShowUp(true)
    else
        self:ShowUp(false)
    end
    self._bShowUp = false
    self._wtWBP_CommonButtonV3S1_91:SetMainTitle("")
    self:SetArrow(false)
    self._wtWBP_CommonButtonV3S1_91:Visible()
end

function QuestTaskDetailItem:RefreshObjectInfo(questInfo)
    self._questInfo = questInfo
    self:_UpdateQuestDetailInfo()
    if IsHD() then
        self:ShowUp(true)
    else
        self:ShowUp(false)
    end
    self._bShowUp = false
    self:SetArrow(false)
    self._wtWBP_CommonButtonV3S1_91:SetMainTitle("")
    self._wtWBP_CommonButtonV3S1_91:Visible()
end

function QuestTaskDetailItem:OnClose()
    self:_RemoveEventListener()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    -- print( "xhz >>>>>>>>>>>>> QuestTaskDetailItem OnClose >>>>>>>>>>>>>>>> " )
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestConditionBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
    --self:PlayWidgetAnimAt(self.WBP_TaskDetail_Item_out, 0, 1, EUMGSequencePlayMode.Forward, 1)
end

function QuestTaskDetailItem:_OnClickBtn()
    if not self._bShowUp then
        self:ShowUp(true)
        self._bShowUp = true
        self:SetArrow(true)
    else
        self:ShowUp(false)
        self._bShowUp = false
        self:SetArrow(false)
    end
    Module.Quest.Config.evtQuestDetailItemFoldToggled:Invoke()
end

function QuestTaskDetailItem:OnShow()
    self:_AddEventListener()
end

function QuestTaskDetailItem:OnHide()
    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end
    self:_RemoveEventListener()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestConditionBox)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
end

function QuestTaskDetailItem:_AddEventListener()
    self:AddLuaEvent(Module.Quest.Config.evtQuestTaskItemUpdate, self.RefreshObjectInfo, self)
end

function QuestTaskDetailItem:_RemoveEventListener()
    self:RemoveLuaEvent(Module.Quest.Config.evtQuestTaskItemUpdate)
end

function QuestTaskDetailItem:_UpdateText()
    local textSize = self._wtQuestPlanText:GetDesiredSize()
    if textSize.Y >= 350 then
        self._wtDFSizeBox_3:SelfHitTestInvisible()
        if self._timerHandle then
            self._timerHandle:Release()
            self._timerHandle = nil
        end
    end
end

function QuestTaskDetailItem:_UpdateQuestDetailInfo()

    if self._questInfo == nil then
        return
    end

    self._wtQuestPlanText:SetText(self._questInfo.desc)
    self._wtDFSizeBox_3:Collapsed()

    if self._timerHandle then
        self._timerHandle:Release()
        self._timerHandle = nil
    end

    if not IsHD() then
        self._timerHandle = Timer:NewIns(0.1, 0)
        self._timerHandle:AddListener(self._UpdateText, self)
        self._timerHandle:Start()
    else
        self._wtDFSizeBox_3:Collapsed()
    end

    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestConditionBox)
    local conditions = self._questInfo:GetAllQusetConditions()
    if #conditions > 0 then
        self.DFCanvasPanel_108:SelfHitTestInvisible()
        for idx, conditionInfo in ipairs(conditions) do
            Facade.UIManager:AddSubUI(
                self,
                UIName2ID.QuestConditionItem,
                self._wtQuestConditionBox,
                nil,
                conditionInfo,
                idx,
                true
            )
        end
    else
        self.DFCanvasPanel_108:Collapsed()
    end

    local objects = self._questInfo:GetQusetObjectives()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtQuestObjectiveBox)
    if #objects > 0 then
        self._wtQuestObjectiveBox:SelfHitTestInvisible()
        for idx, objInfo in ipairs(objects) do
            Facade.UIManager:AddSubUI(
                self,
                UIName2ID.QuestObjectiveItem,
                self._wtQuestObjectiveBox,
                nil,
                objInfo,
                idx,
                self._questInfo
            )
        end
    else
        self._wtQuestObjectiveBox:Collapsed()
    end
end

return QuestTaskDetailItem
