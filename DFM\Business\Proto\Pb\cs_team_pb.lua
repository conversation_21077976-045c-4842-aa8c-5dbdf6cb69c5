require("DFM.Business.Proto.Pb.common_pb")
require("DFM.Business.Proto.Pb.ds_common_pb")
require("DFM.Business.Proto.Pb.errcode_pb")
require("DFM.Business.Proto.Pb.cs_matchinginfo_pb")
require("DFM.Business.Proto.Pb.cs_recruitment_pb")
require "DFM.Business.Proto.ProtoBaseHint"

pb = pb or {}

if __proto_require_editor_file == true then
    require "DFM.Business.Proto.pb.cs_team_editor_pb"
end

TeamMemberState = {
MemUnReady = 0,
MemReady = 1,
MemOffline = 2,
MemInMatch = 8,
MemFinish = 4,
MemEquitp = 16,
MemPrepare = 32,
MemWaitPrepare = 64,
MemRefusePrepare = 128,
MemPrepareComplete = 256,
}
TeamState = {
TeamIdle = 0,
TeamAllocMatch = 1,
TeamInMatch = 2,
}
TeamRecruitmentState = {
TeamRecruitmentStateNotExist = 0,
TeamRecruitmentStateOpen = 1,
TeamRecruitmentStateEnd = 2,
}
TeamChangeType = {
TeamChangeNone = 0,
TeamLeader = 1,
MemberJoin = 2,
MemberLeave = 3,
MemberKick = 4,
MemberState = 5,
MemberEquipPosition = 6,
TeamMatchIDChange = 7,
MemberPackUpdate = 8,
TeamChangeIsRankedMatch = 9,
TeamStateChange = 10,
TeamFace2FaceCreate = 11,
TeamAllChange = 20,
MemberLevel = 21,
MemberPackDownloadFinish = 22,
TeamDismiss = 23,
QuickJoinChange = 24,
SessionUpdate = 25,
}
TeamEvent = {
TeamInvalid = 0,
TeamNotifyPrepare = 1,
TeamRefusePrepare = 2,
}
TeamUnReadyType = {
MemberUnReady = 0,
MemberEquipNotEnough = 1,
MemberPackQuestMiss = 2,
MemberPropNotAllowedIntoDS = 3,
}
TeamBroadcastType = {
TBTNone = 0,
TBTEquitpCompletion = 1,
TBTEquitpReady = 2,
TBTPackQuestMiss = 3,
}
TeamUpdatePackQuestType = {
TeamUpdatePackQuestNone = 0,
TeamUpdatePackQuestJoinTeam = 1,
TeamUpdatePackQuestFinish = 2,
}
pb.__pb_TeamMemberInfo = {
    PlayerID = 0,
    State = 0,
    Nick = "",
    Seat = 0,
    JoinTimestamp = 0,
    EquipPrice = 0,
}
pb.__pb_TeamMemberInfo.__name = "TeamMemberInfo"
pb.__pb_TeamMemberInfo.__index = pb.__pb_TeamMemberInfo
pb.__pb_TeamMemberInfo.__pairs = __pb_pairs

pb.TeamMemberInfo = { __name = "TeamMemberInfo", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TeamMemberInfo : ProtoBase
---@field public PlayerID number
---@field public State number
---@field public Nick string
---@field public Seat number
---@field public Info pb_PlayerSimpleInfo
---@field public JoinTimestamp number
---@field public Props pb_EquipPosition[]
---@field public fashion pb_HeroFashion[]
---@field public PackQuestID number[]
---@field public EquipPrice number
---@field public accessories pb_CSHeroAccessory[]

---@return pb_TeamMemberInfo
function pb.TeamMemberInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_TeamInfo = {
    TeamID = 0,
    LeaderID = 0,
    MatchID = 0,
    State = 0,
    IsAddMember = false,
    IsRankedMatch = false,
    AddMemberType = 0,
    GroupID = 0,
    DSTextualIP = "",
    DSPort = 0,
    DSANode = "",
    DSDomain = "",
    DSRoomID = 0,
    Token = "",
    IsOpenQuickJoin = false,
    Version = "",
    MaxMemberSize = 0,
    RecruitmentState = 0,
    WorldChatRoomId = 0,
    WorldChatRoomIndex = 0,
    lastPostTime = 0,
    Session = "",
}
pb.__pb_TeamInfo.__name = "TeamInfo"
pb.__pb_TeamInfo.__index = pb.__pb_TeamInfo
pb.__pb_TeamInfo.__pairs = __pb_pairs

pb.TeamInfo = { __name = "TeamInfo", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_TeamInfo : ProtoBase
---@field public TeamID number
---@field public LeaderID number
---@field public MatchID number
---@field public State number
---@field public IsAddMember boolean
---@field public IsRankedMatch boolean
---@field public Members pb_TeamMemberInfo[]
---@field public Mode pb_MatchModeInfo
---@field public AddMemberType number
---@field public Modes pb_MatchModeInfo[]
---@field public GroupID number
---@field public DSTextualIP string
---@field public DSPort number
---@field public DSANode string
---@field public DSDomain string
---@field public DSRoomID number
---@field public Token string
---@field public ds_ip_list pb_HostInfo[]
---@field public IsOpenQuickJoin boolean
---@field public Version string
---@field public MaxMemberSize number
---@field public RecruitmentState number
---@field public Filter pb_RecruitmentFilter
---@field public WorldChatRoomId number
---@field public WorldChatRoomIndex number
---@field public lastPostTime number
---@field public Session string

---@return pb_TeamInfo
function pb.TeamInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamInfoTReq = {
    TeamID = 0,
}
pb.__pb_CSTeamInfoTReq.__name = "CSTeamInfoTReq"
pb.__pb_CSTeamInfoTReq.__index = pb.__pb_CSTeamInfoTReq
pb.__pb_CSTeamInfoTReq.__pairs = __pb_pairs

pb.CSTeamInfoTReq = { __name = "CSTeamInfoTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamInfoTReq : ProtoBase
---@field public TeamID number

---@return pb_CSTeamInfoTReq
function pb.CSTeamInfoTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamInfoTRes = {
    result = 0,
}
pb.__pb_CSTeamInfoTRes.__name = "CSTeamInfoTRes"
pb.__pb_CSTeamInfoTRes.__index = pb.__pb_CSTeamInfoTRes
pb.__pb_CSTeamInfoTRes.__pairs = __pb_pairs

pb.CSTeamInfoTRes = { __name = "CSTeamInfoTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamInfoTRes : ProtoBase
---@field public result number
---@field public Info pb_TeamInfo

---@return pb_CSTeamInfoTRes
function pb.CSTeamInfoTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamCreateReq = {
    MatchID = 0,
    AddMemberType = 0,
    spawn_point = 0,
    GroupID = 0,
    IsAddMember = false,
    CreateType = 0,
    IsRankedMatch = false,
}
pb.__pb_CSTeamCreateReq.__name = "CSTeamCreateReq"
pb.__pb_CSTeamCreateReq.__index = pb.__pb_CSTeamCreateReq
pb.__pb_CSTeamCreateReq.__pairs = __pb_pairs

pb.CSTeamCreateReq = { __name = "CSTeamCreateReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamCreateReq : ProtoBase
---@field public MatchID number
---@field public Mode pb_MatchModeInfo
---@field public Modes pb_MatchModeInfo[]
---@field public AddMemberType number
---@field public spawn_point number
---@field public GroupID number
---@field public IsAddMember boolean
---@field public PackQuestID number[]
---@field public CreateType number
---@field public IsRankedMatch boolean

---@return pb_CSTeamCreateReq
function pb.CSTeamCreateReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamCreateRes = {
    result = 0,
}
pb.__pb_CSTeamCreateRes.__name = "CSTeamCreateRes"
pb.__pb_CSTeamCreateRes.__index = pb.__pb_CSTeamCreateRes
pb.__pb_CSTeamCreateRes.__pairs = __pb_pairs

pb.CSTeamCreateRes = { __name = "CSTeamCreateRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamCreateRes : ProtoBase
---@field public result number
---@field public Info pb_TeamInfo

---@return pb_CSTeamCreateRes
function pb.CSTeamCreateRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamInviteTReq = {
    TeamID = 0,
    MatchID = 0,
    InviteeID = 0,
    source = 0,
    RoomID = 0,
    SpecialChannel = 0,
}
pb.__pb_CSTeamInviteTReq.__name = "CSTeamInviteTReq"
pb.__pb_CSTeamInviteTReq.__index = pb.__pb_CSTeamInviteTReq
pb.__pb_CSTeamInviteTReq.__pairs = __pb_pairs

pb.CSTeamInviteTReq = { __name = "CSTeamInviteTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamInviteTReq : ProtoBase
---@field public TeamID number
---@field public MatchID number
---@field public InviteeID number
---@field public source number
---@field public Mode pb_MatchModeInfo
---@field public RoomID number
---@field public SpecialChannel number

---@return pb_CSTeamInviteTReq
function pb.CSTeamInviteTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamInviteTRes = {
    result = 0,
}
pb.__pb_CSTeamInviteTRes.__name = "CSTeamInviteTRes"
pb.__pb_CSTeamInviteTRes.__index = pb.__pb_CSTeamInviteTRes
pb.__pb_CSTeamInviteTRes.__pairs = __pb_pairs

pb.CSTeamInviteTRes = { __name = "CSTeamInviteTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamInviteTRes : ProtoBase
---@field public result number

---@return pb_CSTeamInviteTRes
function pb.CSTeamInviteTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamToInviteeNtf = {
    TeamID = 0,
    MatchID = 0,
    InviterID = 0,
    InviterNick = "",
    InviterLevel = 0,
    InviterPicUrl = "",
    TeamMemberNum = 0,
    source = 0,
    GroupID = 0,
    InviterAccountLevel = 0,
    RoomID = 0,
    SpecialChannel = 0,
}
pb.__pb_CSTeamToInviteeNtf.__name = "CSTeamToInviteeNtf"
pb.__pb_CSTeamToInviteeNtf.__index = pb.__pb_CSTeamToInviteeNtf
pb.__pb_CSTeamToInviteeNtf.__pairs = __pb_pairs

pb.CSTeamToInviteeNtf = { __name = "CSTeamToInviteeNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamToInviteeNtf : ProtoBase
---@field public TeamID number
---@field public MatchID number
---@field public InviterID number
---@field public InviterNick string
---@field public InviterLevel number
---@field public InviterPicUrl string
---@field public Inviter pb_PlayerSimpleInfo
---@field public TeamMemberNum number
---@field public source number
---@field public Mode pb_MatchModeInfo
---@field public Modes pb_MatchModeInfo[]
---@field public GroupID number
---@field public InviterAccountLevel number
---@field public RoomID number
---@field public SpecialChannel number

---@return pb_CSTeamToInviteeNtf
function pb.CSTeamToInviteeNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamResponseInviteTReq = {
    TeamID = 0,
    IsAgree = false,
    RefuseMessage = "",
    InviterID = 0,
    source = 0,
    InviteeGameMode = 0,
    RoomID = 0,
}
pb.__pb_CSTeamResponseInviteTReq.__name = "CSTeamResponseInviteTReq"
pb.__pb_CSTeamResponseInviteTReq.__index = pb.__pb_CSTeamResponseInviteTReq
pb.__pb_CSTeamResponseInviteTReq.__pairs = __pb_pairs

pb.CSTeamResponseInviteTReq = { __name = "CSTeamResponseInviteTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamResponseInviteTReq : ProtoBase
---@field public TeamID number
---@field public IsAgree boolean
---@field public RefuseMessage string
---@field public InviterID number
---@field public source number
---@field public InviteeGameMode number
---@field public RoomID number
---@field public PackQuestID number[]

---@return pb_CSTeamResponseInviteTReq
function pb.CSTeamResponseInviteTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamResponseInviteTRes = {
    result = 0,
}
pb.__pb_CSTeamResponseInviteTRes.__name = "CSTeamResponseInviteTRes"
pb.__pb_CSTeamResponseInviteTRes.__index = pb.__pb_CSTeamResponseInviteTRes
pb.__pb_CSTeamResponseInviteTRes.__pairs = __pb_pairs

pb.CSTeamResponseInviteTRes = { __name = "CSTeamResponseInviteTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamResponseInviteTRes : ProtoBase
---@field public result number
---@field public Info pb_TeamInfo

---@return pb_CSTeamResponseInviteTRes
function pb.CSTeamResponseInviteTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamResponseInviteNtf = {
    TeamID = 0,
    IsAgree = false,
    RefuseMessage = "",
    InviteeNick = "",
}
pb.__pb_CSTeamResponseInviteNtf.__name = "CSTeamResponseInviteNtf"
pb.__pb_CSTeamResponseInviteNtf.__index = pb.__pb_CSTeamResponseInviteNtf
pb.__pb_CSTeamResponseInviteNtf.__pairs = __pb_pairs

pb.CSTeamResponseInviteNtf = { __name = "CSTeamResponseInviteNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamResponseInviteNtf : ProtoBase
---@field public TeamID number
---@field public IsAgree boolean
---@field public RefuseMessage string
---@field public InviteeNick string
---@field public Invitee pb_PlayerSimpleInfo

---@return pb_CSTeamResponseInviteNtf
function pb.CSTeamResponseInviteNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyJoinTReq = {
    team_id = 0,
    target_player_id = 0,
    apply_player_game_mode = 0,
    source = 0,
    SpecialChannel = 0,
}
pb.__pb_CSTeamApplyJoinTReq.__name = "CSTeamApplyJoinTReq"
pb.__pb_CSTeamApplyJoinTReq.__index = pb.__pb_CSTeamApplyJoinTReq
pb.__pb_CSTeamApplyJoinTReq.__pairs = __pb_pairs

pb.CSTeamApplyJoinTReq = { __name = "CSTeamApplyJoinTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyJoinTReq : ProtoBase
---@field public team_id number
---@field public target_player_id number
---@field public apply_player_game_mode number
---@field public source number
---@field public PackQuestID number[]
---@field public SpecialChannel number

---@return pb_CSTeamApplyJoinTReq
function pb.CSTeamApplyJoinTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyJoinTRes = {
    result = 0,
}
pb.__pb_CSTeamApplyJoinTRes.__name = "CSTeamApplyJoinTRes"
pb.__pb_CSTeamApplyJoinTRes.__index = pb.__pb_CSTeamApplyJoinTRes
pb.__pb_CSTeamApplyJoinTRes.__pairs = __pb_pairs

pb.CSTeamApplyJoinTRes = { __name = "CSTeamApplyJoinTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyJoinTRes : ProtoBase
---@field public result number

---@return pb_CSTeamApplyJoinTRes
function pb.CSTeamApplyJoinTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyJoinNtf = {
    apply_player_game_mode = 0,
    source = 0,
    SpecialChannel = 0,
}
pb.__pb_CSTeamApplyJoinNtf.__name = "CSTeamApplyJoinNtf"
pb.__pb_CSTeamApplyJoinNtf.__index = pb.__pb_CSTeamApplyJoinNtf
pb.__pb_CSTeamApplyJoinNtf.__pairs = __pb_pairs

pb.CSTeamApplyJoinNtf = { __name = "CSTeamApplyJoinNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyJoinNtf : ProtoBase
---@field public apply_player pb_PlayerSimpleInfo
---@field public apply_player_game_mode number
---@field public source number
---@field public SpecialChannel number

---@return pb_CSTeamApplyJoinNtf
function pb.CSTeamApplyJoinNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamResponseJoinTReq = {
    team_id = 0,
    agree = false,
    refuse_message = "",
    apply_player_id = 0,
    source = 0,
    apply_player_game_mode = 0,
}
pb.__pb_CSTeamResponseJoinTReq.__name = "CSTeamResponseJoinTReq"
pb.__pb_CSTeamResponseJoinTReq.__index = pb.__pb_CSTeamResponseJoinTReq
pb.__pb_CSTeamResponseJoinTReq.__pairs = __pb_pairs

pb.CSTeamResponseJoinTReq = { __name = "CSTeamResponseJoinTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamResponseJoinTReq : ProtoBase
---@field public team_id number
---@field public agree boolean
---@field public refuse_message string
---@field public apply_player_id number
---@field public source number
---@field public apply_player_game_mode number

---@return pb_CSTeamResponseJoinTReq
function pb.CSTeamResponseJoinTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamResponseJoinTRes = {
    result = 0,
}
pb.__pb_CSTeamResponseJoinTRes.__name = "CSTeamResponseJoinTRes"
pb.__pb_CSTeamResponseJoinTRes.__index = pb.__pb_CSTeamResponseJoinTRes
pb.__pb_CSTeamResponseJoinTRes.__pairs = __pb_pairs

pb.CSTeamResponseJoinTRes = { __name = "CSTeamResponseJoinTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamResponseJoinTRes : ProtoBase
---@field public result number

---@return pb_CSTeamResponseJoinTRes
function pb.CSTeamResponseJoinTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamResponseJoinNtf = {
    result = 0,
    team_id = 0,
    agree = false,
    refuse_message = "",
}
pb.__pb_CSTeamResponseJoinNtf.__name = "CSTeamResponseJoinNtf"
pb.__pb_CSTeamResponseJoinNtf.__index = pb.__pb_CSTeamResponseJoinNtf
pb.__pb_CSTeamResponseJoinNtf.__pairs = __pb_pairs

pb.CSTeamResponseJoinNtf = { __name = "CSTeamResponseJoinNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamResponseJoinNtf : ProtoBase
---@field public result number
---@field public leader pb_PlayerSimpleInfo
---@field public team_id number
---@field public agree boolean
---@field public refuse_message string
---@field public info pb_TeamInfo

---@return pb_CSTeamResponseJoinNtf
function pb.CSTeamResponseJoinNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamMemberChangeNtf = {
    TeamID = 0,
    LeaderID = 0,
    Type = 0,
}
pb.__pb_CSTeamMemberChangeNtf.__name = "CSTeamMemberChangeNtf"
pb.__pb_CSTeamMemberChangeNtf.__index = pb.__pb_CSTeamMemberChangeNtf
pb.__pb_CSTeamMemberChangeNtf.__pairs = __pb_pairs

pb.CSTeamMemberChangeNtf = { __name = "CSTeamMemberChangeNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamMemberChangeNtf : ProtoBase
---@field public TeamID number
---@field public LeaderID number
---@field public Type number
---@field public Member pb_TeamMemberInfo

---@return pb_CSTeamMemberChangeNtf
function pb.CSTeamMemberChangeNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamMemberBatchChangeNtf = {
    TeamID = 0,
    LeaderID = 0,
    Type = 0,
}
pb.__pb_CSTeamMemberBatchChangeNtf.__name = "CSTeamMemberBatchChangeNtf"
pb.__pb_CSTeamMemberBatchChangeNtf.__index = pb.__pb_CSTeamMemberBatchChangeNtf
pb.__pb_CSTeamMemberBatchChangeNtf.__pairs = __pb_pairs

pb.CSTeamMemberBatchChangeNtf = { __name = "CSTeamMemberBatchChangeNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamMemberBatchChangeNtf : ProtoBase
---@field public TeamID number
---@field public LeaderID number
---@field public Type number
---@field public MemberInfos pb_TeamMemberInfo[]

---@return pb_CSTeamMemberBatchChangeNtf
function pb.CSTeamMemberBatchChangeNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamBaseInfoChangeNtf = {
    TeamID = 0,
    Type = 0,
    MatchID = 0,
    State = 0,
    IsAddMember = false,
    LeaderID = 0,
    GroupID = 0,
    IsRankedMatch = false,
    AddMemberType = 0,
    spawn_point = 0,
    NtfTime = 0,
    node_id = "",
    IsOpenQuickJoin = false,
    Session = "",
}
pb.__pb_CSTeamBaseInfoChangeNtf.__name = "CSTeamBaseInfoChangeNtf"
pb.__pb_CSTeamBaseInfoChangeNtf.__index = pb.__pb_CSTeamBaseInfoChangeNtf
pb.__pb_CSTeamBaseInfoChangeNtf.__pairs = __pb_pairs

pb.CSTeamBaseInfoChangeNtf = { __name = "CSTeamBaseInfoChangeNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamBaseInfoChangeNtf : ProtoBase
---@field public TeamID number
---@field public Type number
---@field public MatchID number
---@field public State number
---@field public IsAddMember boolean
---@field public LeaderID number
---@field public Mode pb_MatchModeInfo
---@field public Modes pb_MatchModeInfo[]
---@field public GroupID number
---@field public IsRankedMatch boolean
---@field public AddMemberType number
---@field public spawn_point number
---@field public NtfTime number
---@field public ShowInfo pb_MatchIngShowInfo
---@field public node_id string
---@field public IsOpenQuickJoin boolean
---@field public Session string

---@return pb_CSTeamBaseInfoChangeNtf
function pb.CSTeamBaseInfoChangeNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamExitTReq = {
    TeamID = 0,
}
pb.__pb_CSTeamExitTReq.__name = "CSTeamExitTReq"
pb.__pb_CSTeamExitTReq.__index = pb.__pb_CSTeamExitTReq
pb.__pb_CSTeamExitTReq.__pairs = __pb_pairs

pb.CSTeamExitTReq = { __name = "CSTeamExitTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamExitTReq : ProtoBase
---@field public TeamID number

---@return pb_CSTeamExitTReq
function pb.CSTeamExitTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamExitTRes = {
    result = 0,
}
pb.__pb_CSTeamExitTRes.__name = "CSTeamExitTRes"
pb.__pb_CSTeamExitTRes.__index = pb.__pb_CSTeamExitTRes
pb.__pb_CSTeamExitTRes.__pairs = __pb_pairs

pb.CSTeamExitTRes = { __name = "CSTeamExitTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamExitTRes : ProtoBase
---@field public result number

---@return pb_CSTeamExitTRes
function pb.CSTeamExitTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamKickTReq = {
    TeamID = 0,
    KickPlayerID = 0,
}
pb.__pb_CSTeamKickTReq.__name = "CSTeamKickTReq"
pb.__pb_CSTeamKickTReq.__index = pb.__pb_CSTeamKickTReq
pb.__pb_CSTeamKickTReq.__pairs = __pb_pairs

pb.CSTeamKickTReq = { __name = "CSTeamKickTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamKickTReq : ProtoBase
---@field public TeamID number
---@field public KickPlayerID number

---@return pb_CSTeamKickTReq
function pb.CSTeamKickTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamKickTRes = {
    result = 0,
}
pb.__pb_CSTeamKickTRes.__name = "CSTeamKickTRes"
pb.__pb_CSTeamKickTRes.__index = pb.__pb_CSTeamKickTRes
pb.__pb_CSTeamKickTRes.__pairs = __pb_pairs

pb.CSTeamKickTRes = { __name = "CSTeamKickTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamKickTRes : ProtoBase
---@field public result number

---@return pb_CSTeamKickTRes
function pb.CSTeamKickTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamSetReadyTReq = {
    TeamID = 0,
    Ready = false,
}
pb.__pb_CSTeamSetReadyTReq.__name = "CSTeamSetReadyTReq"
pb.__pb_CSTeamSetReadyTReq.__index = pb.__pb_CSTeamSetReadyTReq
pb.__pb_CSTeamSetReadyTReq.__pairs = __pb_pairs

pb.CSTeamSetReadyTReq = { __name = "CSTeamSetReadyTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamSetReadyTReq : ProtoBase
---@field public TeamID number
---@field public Ready boolean

---@return pb_CSTeamSetReadyTReq
function pb.CSTeamSetReadyTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamSetReadyTRes = {
    result = 0,
    face_verify_url = "",
}
pb.__pb_CSTeamSetReadyTRes.__name = "CSTeamSetReadyTRes"
pb.__pb_CSTeamSetReadyTRes.__index = pb.__pb_CSTeamSetReadyTRes
pb.__pb_CSTeamSetReadyTRes.__pairs = __pb_pairs

pb.CSTeamSetReadyTRes = { __name = "CSTeamSetReadyTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamSetReadyTRes : ProtoBase
---@field public result number
---@field public face_verify_url string

---@return pb_CSTeamSetReadyTRes
function pb.CSTeamSetReadyTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamPrepareTReq = {
    TeamID = 0,
    Prepare = false,
    Refuse = false,
}
pb.__pb_CSTeamPrepareTReq.__name = "CSTeamPrepareTReq"
pb.__pb_CSTeamPrepareTReq.__index = pb.__pb_CSTeamPrepareTReq
pb.__pb_CSTeamPrepareTReq.__pairs = __pb_pairs

pb.CSTeamPrepareTReq = { __name = "CSTeamPrepareTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamPrepareTReq : ProtoBase
---@field public TeamID number
---@field public Prepare boolean
---@field public Refuse boolean

---@return pb_CSTeamPrepareTReq
function pb.CSTeamPrepareTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamPrepareTRes = {
    result = 0,
    face_verify_url = "",
}
pb.__pb_CSTeamPrepareTRes.__name = "CSTeamPrepareTRes"
pb.__pb_CSTeamPrepareTRes.__index = pb.__pb_CSTeamPrepareTRes
pb.__pb_CSTeamPrepareTRes.__pairs = __pb_pairs

pb.CSTeamPrepareTRes = { __name = "CSTeamPrepareTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamPrepareTRes : ProtoBase
---@field public result number
---@field public face_verify_url string

---@return pb_CSTeamPrepareTRes
function pb.CSTeamPrepareTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamPrepareNtf = {
    Prepare = false,
}
pb.__pb_CSTeamPrepareNtf.__name = "CSTeamPrepareNtf"
pb.__pb_CSTeamPrepareNtf.__index = pb.__pb_CSTeamPrepareNtf
pb.__pb_CSTeamPrepareNtf.__pairs = __pb_pairs

pb.CSTeamPrepareNtf = { __name = "CSTeamPrepareNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamPrepareNtf : ProtoBase
---@field public Prepare boolean

---@return pb_CSTeamPrepareNtf
function pb.CSTeamPrepareNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamNotifyPrepareTReq = {
    TeamID = 0,
    MemberID = 0,
}
pb.__pb_CSTeamNotifyPrepareTReq.__name = "CSTeamNotifyPrepareTReq"
pb.__pb_CSTeamNotifyPrepareTReq.__index = pb.__pb_CSTeamNotifyPrepareTReq
pb.__pb_CSTeamNotifyPrepareTReq.__pairs = __pb_pairs

pb.CSTeamNotifyPrepareTReq = { __name = "CSTeamNotifyPrepareTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamNotifyPrepareTReq : ProtoBase
---@field public TeamID number
---@field public MemberID number

---@return pb_CSTeamNotifyPrepareTReq
function pb.CSTeamNotifyPrepareTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamNotifyPrepareTRes = {
    result = 0,
}
pb.__pb_CSTeamNotifyPrepareTRes.__name = "CSTeamNotifyPrepareTRes"
pb.__pb_CSTeamNotifyPrepareTRes.__index = pb.__pb_CSTeamNotifyPrepareTRes
pb.__pb_CSTeamNotifyPrepareTRes.__pairs = __pb_pairs

pb.CSTeamNotifyPrepareTRes = { __name = "CSTeamNotifyPrepareTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamNotifyPrepareTRes : ProtoBase
---@field public result number

---@return pb_CSTeamNotifyPrepareTRes
function pb.CSTeamNotifyPrepareTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamPrepareEventNtf = {
    event = 0,
    TeamID = 0,
    MemberID = 0,
    SourcePlayerID = 0,
}
pb.__pb_CSTeamPrepareEventNtf.__name = "CSTeamPrepareEventNtf"
pb.__pb_CSTeamPrepareEventNtf.__index = pb.__pb_CSTeamPrepareEventNtf
pb.__pb_CSTeamPrepareEventNtf.__pairs = __pb_pairs

pb.CSTeamPrepareEventNtf = { __name = "CSTeamPrepareEventNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamPrepareEventNtf : ProtoBase
---@field public event number
---@field public TeamID number
---@field public MemberID number
---@field public SourcePlayerID number

---@return pb_CSTeamPrepareEventNtf
function pb.CSTeamPrepareEventNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamStartMatchTReq = {
    TeamID = 0,
    IsAddMember = false,
    Raid_ID = 0,
    rule = 0,
    spawn_point = 0,
    IsRandedMatch = false,
    IsRankedMatch = false,
    IsForceStartMatch = false,
}
pb.__pb_CSTeamStartMatchTReq.__name = "CSTeamStartMatchTReq"
pb.__pb_CSTeamStartMatchTReq.__index = pb.__pb_CSTeamStartMatchTReq
pb.__pb_CSTeamStartMatchTReq.__pairs = __pb_pairs

pb.CSTeamStartMatchTReq = { __name = "CSTeamStartMatchTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamStartMatchTReq : ProtoBase
---@field public TeamID number
---@field public IsAddMember boolean
---@field public Raid_ID number
---@field public rule number
---@field public spawn_point number
---@field public IsRandedMatch boolean
---@field public IsRankedMatch boolean
---@field public IsForceStartMatch boolean

---@return pb_CSTeamStartMatchTReq
function pb.CSTeamStartMatchTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamStartMatchTRes = {
    result = 0,
    node_id = "",
    face_verify_url = "",
}
pb.__pb_CSTeamStartMatchTRes.__name = "CSTeamStartMatchTRes"
pb.__pb_CSTeamStartMatchTRes.__index = pb.__pb_CSTeamStartMatchTRes
pb.__pb_CSTeamStartMatchTRes.__pairs = __pb_pairs

pb.CSTeamStartMatchTRes = { __name = "CSTeamStartMatchTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamStartMatchTRes : ProtoBase
---@field public result number
---@field public ShowInfo pb_MatchIngShowInfo
---@field public node_id string
---@field public ntf pb_CSRoomMatchStartFailNtf
---@field public CheckFailedPlayerIds number[]
---@field public CheckReputationFailedPlayerIds number[]
---@field public face_verify_url string

---@return pb_CSTeamStartMatchTRes
function pb.CSTeamStartMatchTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamUnreadyNtf = {
    team_id = 0,
    result = 0,
}
pb.__pb_CSTeamUnreadyNtf.__name = "CSTeamUnreadyNtf"
pb.__pb_CSTeamUnreadyNtf.__index = pb.__pb_CSTeamUnreadyNtf
pb.__pb_CSTeamUnreadyNtf.__pairs = __pb_pairs

pb.CSTeamUnreadyNtf = { __name = "CSTeamUnreadyNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamUnreadyNtf : ProtoBase
---@field public team_id number
---@field public unready_member_array number[]
---@field public result number

---@return pb_CSTeamUnreadyNtf
function pb.CSTeamUnreadyNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamCancelMatchTReq = {
    TeamID = 0,
}
pb.__pb_CSTeamCancelMatchTReq.__name = "CSTeamCancelMatchTReq"
pb.__pb_CSTeamCancelMatchTReq.__index = pb.__pb_CSTeamCancelMatchTReq
pb.__pb_CSTeamCancelMatchTReq.__pairs = __pb_pairs

pb.CSTeamCancelMatchTReq = { __name = "CSTeamCancelMatchTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamCancelMatchTReq : ProtoBase
---@field public TeamID number

---@return pb_CSTeamCancelMatchTReq
function pb.CSTeamCancelMatchTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamCancelMatchTRes = {
    result = 0,
}
pb.__pb_CSTeamCancelMatchTRes.__name = "CSTeamCancelMatchTRes"
pb.__pb_CSTeamCancelMatchTRes.__index = pb.__pb_CSTeamCancelMatchTRes
pb.__pb_CSTeamCancelMatchTRes.__pairs = __pb_pairs

pb.CSTeamCancelMatchTRes = { __name = "CSTeamCancelMatchTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamCancelMatchTRes : ProtoBase
---@field public result number

---@return pb_CSTeamCancelMatchTRes
function pb.CSTeamCancelMatchTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeMatchIDTReq = {
    TeamID = 0,
    MatchID = 0,
    IsAddMember = false,
    GroupID = 0,
    AddMemberType = 0,
    spawn_point = 0,
}
pb.__pb_CSTeamChangeMatchIDTReq.__name = "CSTeamChangeMatchIDTReq"
pb.__pb_CSTeamChangeMatchIDTReq.__index = pb.__pb_CSTeamChangeMatchIDTReq
pb.__pb_CSTeamChangeMatchIDTReq.__pairs = __pb_pairs

pb.CSTeamChangeMatchIDTReq = { __name = "CSTeamChangeMatchIDTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeMatchIDTReq : ProtoBase
---@field public TeamID number
---@field public MatchID number
---@field public IsAddMember boolean
---@field public Mode pb_MatchModeInfo
---@field public Modes pb_MatchModeInfo[]
---@field public GroupID number
---@field public AddMemberType number
---@field public spawn_point number

---@return pb_CSTeamChangeMatchIDTReq
function pb.CSTeamChangeMatchIDTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeMatchIDTRes = {
    result = 0,
}
pb.__pb_CSTeamChangeMatchIDTRes.__name = "CSTeamChangeMatchIDTRes"
pb.__pb_CSTeamChangeMatchIDTRes.__index = pb.__pb_CSTeamChangeMatchIDTRes
pb.__pb_CSTeamChangeMatchIDTRes.__pairs = __pb_pairs

pb.CSTeamChangeMatchIDTRes = { __name = "CSTeamChangeMatchIDTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeMatchIDTRes : ProtoBase
---@field public result number

---@return pb_CSTeamChangeMatchIDTRes
function pb.CSTeamChangeMatchIDTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeLeaderTReq = {
    TeamID = 0,
    NewLeaderID = 0,
}
pb.__pb_CSTeamChangeLeaderTReq.__name = "CSTeamChangeLeaderTReq"
pb.__pb_CSTeamChangeLeaderTReq.__index = pb.__pb_CSTeamChangeLeaderTReq
pb.__pb_CSTeamChangeLeaderTReq.__pairs = __pb_pairs

pb.CSTeamChangeLeaderTReq = { __name = "CSTeamChangeLeaderTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeLeaderTReq : ProtoBase
---@field public TeamID number
---@field public NewLeaderID number

---@return pb_CSTeamChangeLeaderTReq
function pb.CSTeamChangeLeaderTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeLeaderTRes = {
    result = 0,
}
pb.__pb_CSTeamChangeLeaderTRes.__name = "CSTeamChangeLeaderTRes"
pb.__pb_CSTeamChangeLeaderTRes.__index = pb.__pb_CSTeamChangeLeaderTRes
pb.__pb_CSTeamChangeLeaderTRes.__pairs = __pb_pairs

pb.CSTeamChangeLeaderTRes = { __name = "CSTeamChangeLeaderTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeLeaderTRes : ProtoBase
---@field public result number

---@return pb_CSTeamChangeLeaderTRes
function pb.CSTeamChangeLeaderTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyLeaderTReq = {
    TeamID = 0,
}
pb.__pb_CSTeamApplyLeaderTReq.__name = "CSTeamApplyLeaderTReq"
pb.__pb_CSTeamApplyLeaderTReq.__index = pb.__pb_CSTeamApplyLeaderTReq
pb.__pb_CSTeamApplyLeaderTReq.__pairs = __pb_pairs

pb.CSTeamApplyLeaderTReq = { __name = "CSTeamApplyLeaderTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyLeaderTReq : ProtoBase
---@field public TeamID number

---@return pb_CSTeamApplyLeaderTReq
function pb.CSTeamApplyLeaderTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyLeaderTRes = {
    result = 0,
}
pb.__pb_CSTeamApplyLeaderTRes.__name = "CSTeamApplyLeaderTRes"
pb.__pb_CSTeamApplyLeaderTRes.__index = pb.__pb_CSTeamApplyLeaderTRes
pb.__pb_CSTeamApplyLeaderTRes.__pairs = __pb_pairs

pb.CSTeamApplyLeaderTRes = { __name = "CSTeamApplyLeaderTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyLeaderTRes : ProtoBase
---@field public result number

---@return pb_CSTeamApplyLeaderTRes
function pb.CSTeamApplyLeaderTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyLeaderNtf = {
    TeamID = 0,
    ApplyPlayerID = 0,
}
pb.__pb_CSTeamApplyLeaderNtf.__name = "CSTeamApplyLeaderNtf"
pb.__pb_CSTeamApplyLeaderNtf.__index = pb.__pb_CSTeamApplyLeaderNtf
pb.__pb_CSTeamApplyLeaderNtf.__pairs = __pb_pairs

pb.CSTeamApplyLeaderNtf = { __name = "CSTeamApplyLeaderNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyLeaderNtf : ProtoBase
---@field public TeamID number
---@field public ApplyPlayerID number

---@return pb_CSTeamApplyLeaderNtf
function pb.CSTeamApplyLeaderNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyLeaderResponseTReq = {
    TeamID = 0,
    ApplyPlayerID = 0,
    IsAgree = false,
}
pb.__pb_CSTeamApplyLeaderResponseTReq.__name = "CSTeamApplyLeaderResponseTReq"
pb.__pb_CSTeamApplyLeaderResponseTReq.__index = pb.__pb_CSTeamApplyLeaderResponseTReq
pb.__pb_CSTeamApplyLeaderResponseTReq.__pairs = __pb_pairs

pb.CSTeamApplyLeaderResponseTReq = { __name = "CSTeamApplyLeaderResponseTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyLeaderResponseTReq : ProtoBase
---@field public TeamID number
---@field public ApplyPlayerID number
---@field public IsAgree boolean

---@return pb_CSTeamApplyLeaderResponseTReq
function pb.CSTeamApplyLeaderResponseTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyLeaderResponseTRes = {
    result = 0,
}
pb.__pb_CSTeamApplyLeaderResponseTRes.__name = "CSTeamApplyLeaderResponseTRes"
pb.__pb_CSTeamApplyLeaderResponseTRes.__index = pb.__pb_CSTeamApplyLeaderResponseTRes
pb.__pb_CSTeamApplyLeaderResponseTRes.__pairs = __pb_pairs

pb.CSTeamApplyLeaderResponseTRes = { __name = "CSTeamApplyLeaderResponseTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyLeaderResponseTRes : ProtoBase
---@field public result number

---@return pb_CSTeamApplyLeaderResponseTRes
function pb.CSTeamApplyLeaderResponseTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamApplyLeaderResponseNtf = {
    TeamID = 0,
    IsAgree = false,
}
pb.__pb_CSTeamApplyLeaderResponseNtf.__name = "CSTeamApplyLeaderResponseNtf"
pb.__pb_CSTeamApplyLeaderResponseNtf.__index = pb.__pb_CSTeamApplyLeaderResponseNtf
pb.__pb_CSTeamApplyLeaderResponseNtf.__pairs = __pb_pairs

pb.CSTeamApplyLeaderResponseNtf = { __name = "CSTeamApplyLeaderResponseNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamApplyLeaderResponseNtf : ProtoBase
---@field public TeamID number
---@field public IsAgree boolean

---@return pb_CSTeamApplyLeaderResponseNtf
function pb.CSTeamApplyLeaderResponseNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamEquipPositionChangeTNtf = {
    TeamID = 0,
}
pb.__pb_CSTeamEquipPositionChangeTNtf.__name = "CSTeamEquipPositionChangeTNtf"
pb.__pb_CSTeamEquipPositionChangeTNtf.__index = pb.__pb_CSTeamEquipPositionChangeTNtf
pb.__pb_CSTeamEquipPositionChangeTNtf.__pairs = __pb_pairs

pb.CSTeamEquipPositionChangeTNtf = { __name = "CSTeamEquipPositionChangeTNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamEquipPositionChangeTNtf : ProtoBase
---@field public TeamID number

---@return pb_CSTeamEquipPositionChangeTNtf
function pb.CSTeamEquipPositionChangeTNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamIntoEquipTNtf = {
    TeamID = 0,
    IsIntoState = false,
}
pb.__pb_CSTeamIntoEquipTNtf.__name = "CSTeamIntoEquipTNtf"
pb.__pb_CSTeamIntoEquipTNtf.__index = pb.__pb_CSTeamIntoEquipTNtf
pb.__pb_CSTeamIntoEquipTNtf.__pairs = __pb_pairs

pb.CSTeamIntoEquipTNtf = { __name = "CSTeamIntoEquipTNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamIntoEquipTNtf : ProtoBase
---@field public TeamID number
---@field public IsIntoState boolean

---@return pb_CSTeamIntoEquipTNtf
function pb.CSTeamIntoEquipTNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeStateTReq = {
    TeamID = 0,
    Set = false,
    State = 0,
}
pb.__pb_CSTeamChangeStateTReq.__name = "CSTeamChangeStateTReq"
pb.__pb_CSTeamChangeStateTReq.__index = pb.__pb_CSTeamChangeStateTReq
pb.__pb_CSTeamChangeStateTReq.__pairs = __pb_pairs

pb.CSTeamChangeStateTReq = { __name = "CSTeamChangeStateTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeStateTReq : ProtoBase
---@field public TeamID number
---@field public Set boolean
---@field public State number

---@return pb_CSTeamChangeStateTReq
function pb.CSTeamChangeStateTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeStateTRes = {
    result = 0,
}
pb.__pb_CSTeamChangeStateTRes.__name = "CSTeamChangeStateTRes"
pb.__pb_CSTeamChangeStateTRes.__index = pb.__pb_CSTeamChangeStateTRes
pb.__pb_CSTeamChangeStateTRes.__pairs = __pb_pairs

pb.CSTeamChangeStateTRes = { __name = "CSTeamChangeStateTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeStateTRes : ProtoBase
---@field public result number

---@return pb_CSTeamChangeStateTRes
function pb.CSTeamChangeStateTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeIsRankTReq = {
    TeamId = 0,
    isRankedMatch = false,
}
pb.__pb_CSTeamChangeIsRankTReq.__name = "CSTeamChangeIsRankTReq"
pb.__pb_CSTeamChangeIsRankTReq.__index = pb.__pb_CSTeamChangeIsRankTReq
pb.__pb_CSTeamChangeIsRankTReq.__pairs = __pb_pairs

pb.CSTeamChangeIsRankTReq = { __name = "CSTeamChangeIsRankTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeIsRankTReq : ProtoBase
---@field public TeamId number
---@field public isRankedMatch boolean

---@return pb_CSTeamChangeIsRankTReq
function pb.CSTeamChangeIsRankTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeIsRankTRes = {
    result = 0,
}
pb.__pb_CSTeamChangeIsRankTRes.__name = "CSTeamChangeIsRankTRes"
pb.__pb_CSTeamChangeIsRankTRes.__index = pb.__pb_CSTeamChangeIsRankTRes
pb.__pb_CSTeamChangeIsRankTRes.__pairs = __pb_pairs

pb.CSTeamChangeIsRankTRes = { __name = "CSTeamChangeIsRankTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeIsRankTRes : ProtoBase
---@field public result number

---@return pb_CSTeamChangeIsRankTRes
function pb.CSTeamChangeIsRankTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamEnterWorldTReq = {
    TeamID = 0,
    rule = 0,
    spawn_point = 0,
}
pb.__pb_CSTeamEnterWorldTReq.__name = "CSTeamEnterWorldTReq"
pb.__pb_CSTeamEnterWorldTReq.__index = pb.__pb_CSTeamEnterWorldTReq
pb.__pb_CSTeamEnterWorldTReq.__pairs = __pb_pairs

pb.CSTeamEnterWorldTReq = { __name = "CSTeamEnterWorldTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamEnterWorldTReq : ProtoBase
---@field public TeamID number
---@field public rule number
---@field public spawn_point number

---@return pb_CSTeamEnterWorldTReq
function pb.CSTeamEnterWorldTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamEnterWorldTRes = {
    result = 0,
}
pb.__pb_CSTeamEnterWorldTRes.__name = "CSTeamEnterWorldTRes"
pb.__pb_CSTeamEnterWorldTRes.__index = pb.__pb_CSTeamEnterWorldTRes
pb.__pb_CSTeamEnterWorldTRes.__pairs = __pb_pairs

pb.CSTeamEnterWorldTRes = { __name = "CSTeamEnterWorldTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamEnterWorldTRes : ProtoBase
---@field public result number

---@return pb_CSTeamEnterWorldTRes
function pb.CSTeamEnterWorldTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamRecruitmentTReq = {
    TeamID = 0,
}
pb.__pb_CSTeamRecruitmentTReq.__name = "CSTeamRecruitmentTReq"
pb.__pb_CSTeamRecruitmentTReq.__index = pb.__pb_CSTeamRecruitmentTReq
pb.__pb_CSTeamRecruitmentTReq.__pairs = __pb_pairs

pb.CSTeamRecruitmentTReq = { __name = "CSTeamRecruitmentTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamRecruitmentTReq : ProtoBase
---@field public TeamID number

---@return pb_CSTeamRecruitmentTReq
function pb.CSTeamRecruitmentTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamRecruitmentTRes = {
    result = 0,
}
pb.__pb_CSTeamRecruitmentTRes.__name = "CSTeamRecruitmentTRes"
pb.__pb_CSTeamRecruitmentTRes.__index = pb.__pb_CSTeamRecruitmentTRes
pb.__pb_CSTeamRecruitmentTRes.__pairs = __pb_pairs

pb.CSTeamRecruitmentTRes = { __name = "CSTeamRecruitmentTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamRecruitmentTRes : ProtoBase
---@field public result number

---@return pb_CSTeamRecruitmentTRes
function pb.CSTeamRecruitmentTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamGetInviteListReq = {
}
pb.__pb_CSTeamGetInviteListReq.__name = "CSTeamGetInviteListReq"
pb.__pb_CSTeamGetInviteListReq.__index = pb.__pb_CSTeamGetInviteListReq
pb.__pb_CSTeamGetInviteListReq.__pairs = __pb_pairs

pb.CSTeamGetInviteListReq = { __name = "CSTeamGetInviteListReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamGetInviteListReq : ProtoBase

---@return pb_CSTeamGetInviteListReq
function pb.CSTeamGetInviteListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamGetInviteListRes = {
    result = 0,
}
pb.__pb_CSTeamGetInviteListRes.__name = "CSTeamGetInviteListRes"
pb.__pb_CSTeamGetInviteListRes.__index = pb.__pb_CSTeamGetInviteListRes
pb.__pb_CSTeamGetInviteListRes.__pairs = __pb_pairs

pb.CSTeamGetInviteListRes = { __name = "CSTeamGetInviteListRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamGetInviteListRes : ProtoBase
---@field public result number
---@field public invite_list pb_ClientSaveInviteInfo[]

---@return pb_CSTeamGetInviteListRes
function pb.CSTeamGetInviteListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamSaveInviteListReq = {
}
pb.__pb_CSTeamSaveInviteListReq.__name = "CSTeamSaveInviteListReq"
pb.__pb_CSTeamSaveInviteListReq.__index = pb.__pb_CSTeamSaveInviteListReq
pb.__pb_CSTeamSaveInviteListReq.__pairs = __pb_pairs

pb.CSTeamSaveInviteListReq = { __name = "CSTeamSaveInviteListReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamSaveInviteListReq : ProtoBase
---@field public invite_list pb_ClientSaveInviteInfo[]

---@return pb_CSTeamSaveInviteListReq
function pb.CSTeamSaveInviteListReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamSaveInviteListRes = {
    result = 0,
}
pb.__pb_CSTeamSaveInviteListRes.__name = "CSTeamSaveInviteListRes"
pb.__pb_CSTeamSaveInviteListRes.__index = pb.__pb_CSTeamSaveInviteListRes
pb.__pb_CSTeamSaveInviteListRes.__pairs = __pb_pairs

pb.CSTeamSaveInviteListRes = { __name = "CSTeamSaveInviteListRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamSaveInviteListRes : ProtoBase
---@field public result number

---@return pb_CSTeamSaveInviteListRes
function pb.CSTeamSaveInviteListRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_ClientSaveInviteInfo = {
    invite_type = 0,
    team_id = 0,
    member_num = 0,
    match_id = 0,
    invite_time = 0,
    state = 0,
    flag = 0,
}
pb.__pb_ClientSaveInviteInfo.__name = "ClientSaveInviteInfo"
pb.__pb_ClientSaveInviteInfo.__index = pb.__pb_ClientSaveInviteInfo
pb.__pb_ClientSaveInviteInfo.__pairs = __pb_pairs

pb.ClientSaveInviteInfo = { __name = "ClientSaveInviteInfo", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_ClientSaveInviteInfo : ProtoBase
---@field public invite_type number
---@field public team_id number
---@field public member_num number
---@field public match_id number
---@field public invite_time number
---@field public inviter pb_PlayerSimpleInfo
---@field public state number
---@field public flag number
---@field public mode pb_MatchModeInfo
---@field public modes pb_MatchModeInfo[]

---@return pb_ClientSaveInviteInfo
function pb.ClientSaveInviteInfo:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamBroadcastTReq = {
    team_id = 0,
    broadcast_type = 0,
}
pb.__pb_CSTeamBroadcastTReq.__name = "CSTeamBroadcastTReq"
pb.__pb_CSTeamBroadcastTReq.__index = pb.__pb_CSTeamBroadcastTReq
pb.__pb_CSTeamBroadcastTReq.__pairs = __pb_pairs

pb.CSTeamBroadcastTReq = { __name = "CSTeamBroadcastTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamBroadcastTReq : ProtoBase
---@field public team_id number
---@field public broadcast_type number

---@return pb_CSTeamBroadcastTReq
function pb.CSTeamBroadcastTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamBroadcastTRes = {
    result = 0,
}
pb.__pb_CSTeamBroadcastTRes.__name = "CSTeamBroadcastTRes"
pb.__pb_CSTeamBroadcastTRes.__index = pb.__pb_CSTeamBroadcastTRes
pb.__pb_CSTeamBroadcastTRes.__pairs = __pb_pairs

pb.CSTeamBroadcastTRes = { __name = "CSTeamBroadcastTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamBroadcastTRes : ProtoBase
---@field public result number

---@return pb_CSTeamBroadcastTRes
function pb.CSTeamBroadcastTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamBroadcastNtf = {
    team_id = 0,
    broadcast_type = 0,
    player_id = 0,
}
pb.__pb_CSTeamBroadcastNtf.__name = "CSTeamBroadcastNtf"
pb.__pb_CSTeamBroadcastNtf.__index = pb.__pb_CSTeamBroadcastNtf
pb.__pb_CSTeamBroadcastNtf.__pairs = __pb_pairs

pb.CSTeamBroadcastNtf = { __name = "CSTeamBroadcastNtf", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamBroadcastNtf : ProtoBase
---@field public team_id number
---@field public broadcast_type number
---@field public player_id number

---@return pb_CSTeamBroadcastNtf
function pb.CSTeamBroadcastNtf:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamUpdatePackQuestTReq = {
    team_id = 0,
    update_pack_quest_type = 0,
}
pb.__pb_CSTeamUpdatePackQuestTReq.__name = "CSTeamUpdatePackQuestTReq"
pb.__pb_CSTeamUpdatePackQuestTReq.__index = pb.__pb_CSTeamUpdatePackQuestTReq
pb.__pb_CSTeamUpdatePackQuestTReq.__pairs = __pb_pairs

pb.CSTeamUpdatePackQuestTReq = { __name = "CSTeamUpdatePackQuestTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamUpdatePackQuestTReq : ProtoBase
---@field public team_id number
---@field public PackQuestID number[]
---@field public update_pack_quest_type number

---@return pb_CSTeamUpdatePackQuestTReq
function pb.CSTeamUpdatePackQuestTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamUpdatePackQuestTRes = {
    result = 0,
}
pb.__pb_CSTeamUpdatePackQuestTRes.__name = "CSTeamUpdatePackQuestTRes"
pb.__pb_CSTeamUpdatePackQuestTRes.__index = pb.__pb_CSTeamUpdatePackQuestTRes
pb.__pb_CSTeamUpdatePackQuestTRes.__pairs = __pb_pairs

pb.CSTeamUpdatePackQuestTRes = { __name = "CSTeamUpdatePackQuestTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamUpdatePackQuestTRes : ProtoBase
---@field public result number

---@return pb_CSTeamUpdatePackQuestTRes
function pb.CSTeamUpdatePackQuestTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamJoinFromRecruitmentTReq = {
    team_id = 0,
}
pb.__pb_CSTeamJoinFromRecruitmentTReq.__name = "CSTeamJoinFromRecruitmentTReq"
pb.__pb_CSTeamJoinFromRecruitmentTReq.__index = pb.__pb_CSTeamJoinFromRecruitmentTReq
pb.__pb_CSTeamJoinFromRecruitmentTReq.__pairs = __pb_pairs

pb.CSTeamJoinFromRecruitmentTReq = { __name = "CSTeamJoinFromRecruitmentTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamJoinFromRecruitmentTReq : ProtoBase
---@field public team_id number
---@field public PackQuestID number[]

---@return pb_CSTeamJoinFromRecruitmentTReq
function pb.CSTeamJoinFromRecruitmentTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamJoinFromRecruitmentTRes = {
    result = 0,
}
pb.__pb_CSTeamJoinFromRecruitmentTRes.__name = "CSTeamJoinFromRecruitmentTRes"
pb.__pb_CSTeamJoinFromRecruitmentTRes.__index = pb.__pb_CSTeamJoinFromRecruitmentTRes
pb.__pb_CSTeamJoinFromRecruitmentTRes.__pairs = __pb_pairs

pb.CSTeamJoinFromRecruitmentTRes = { __name = "CSTeamJoinFromRecruitmentTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamJoinFromRecruitmentTRes : ProtoBase
---@field public result number
---@field public Info pb_TeamInfo

---@return pb_CSTeamJoinFromRecruitmentTRes
function pb.CSTeamJoinFromRecruitmentTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamJoinFromMiniProgramTReq = {
    team_id = 0,
}
pb.__pb_CSTeamJoinFromMiniProgramTReq.__name = "CSTeamJoinFromMiniProgramTReq"
pb.__pb_CSTeamJoinFromMiniProgramTReq.__index = pb.__pb_CSTeamJoinFromMiniProgramTReq
pb.__pb_CSTeamJoinFromMiniProgramTReq.__pairs = __pb_pairs

pb.CSTeamJoinFromMiniProgramTReq = { __name = "CSTeamJoinFromMiniProgramTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamJoinFromMiniProgramTReq : ProtoBase
---@field public team_id number
---@field public PackQuestID number[]

---@return pb_CSTeamJoinFromMiniProgramTReq
function pb.CSTeamJoinFromMiniProgramTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamJoinFromMiniProgramTRes = {
    result = 0,
}
pb.__pb_CSTeamJoinFromMiniProgramTRes.__name = "CSTeamJoinFromMiniProgramTRes"
pb.__pb_CSTeamJoinFromMiniProgramTRes.__index = pb.__pb_CSTeamJoinFromMiniProgramTRes
pb.__pb_CSTeamJoinFromMiniProgramTRes.__pairs = __pb_pairs

pb.CSTeamJoinFromMiniProgramTRes = { __name = "CSTeamJoinFromMiniProgramTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamJoinFromMiniProgramTRes : ProtoBase
---@field public result number
---@field public Info pb_TeamInfo

---@return pb_CSTeamJoinFromMiniProgramTRes
function pb.CSTeamJoinFromMiniProgramTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamDismissTReq = {
    team_id = 0,
}
pb.__pb_CSTeamDismissTReq.__name = "CSTeamDismissTReq"
pb.__pb_CSTeamDismissTReq.__index = pb.__pb_CSTeamDismissTReq
pb.__pb_CSTeamDismissTReq.__pairs = __pb_pairs

pb.CSTeamDismissTReq = { __name = "CSTeamDismissTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamDismissTReq : ProtoBase
---@field public team_id number

---@return pb_CSTeamDismissTReq
function pb.CSTeamDismissTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamDismissTRes = {
    result = 0,
}
pb.__pb_CSTeamDismissTRes.__name = "CSTeamDismissTRes"
pb.__pb_CSTeamDismissTRes.__index = pb.__pb_CSTeamDismissTRes
pb.__pb_CSTeamDismissTRes.__pairs = __pb_pairs

pb.CSTeamDismissTRes = { __name = "CSTeamDismissTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamDismissTRes : ProtoBase
---@field public result number

---@return pb_CSTeamDismissTRes
function pb.CSTeamDismissTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSBhdTeamBeginMatchTReq = {
    team_id = 0,
    client_version = "",
}
pb.__pb_CSBhdTeamBeginMatchTReq.__name = "CSBhdTeamBeginMatchTReq"
pb.__pb_CSBhdTeamBeginMatchTReq.__index = pb.__pb_CSBhdTeamBeginMatchTReq
pb.__pb_CSBhdTeamBeginMatchTReq.__pairs = __pb_pairs

pb.CSBhdTeamBeginMatchTReq = { __name = "CSBhdTeamBeginMatchTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSBhdTeamBeginMatchTReq : ProtoBase
---@field public team_id number
---@field public client_version string

---@return pb_CSBhdTeamBeginMatchTReq
function pb.CSBhdTeamBeginMatchTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSBhdTeamBeginMatchTRes = {
    result = 0,
}
pb.__pb_CSBhdTeamBeginMatchTRes.__name = "CSBhdTeamBeginMatchTRes"
pb.__pb_CSBhdTeamBeginMatchTRes.__index = pb.__pb_CSBhdTeamBeginMatchTRes
pb.__pb_CSBhdTeamBeginMatchTRes.__pairs = __pb_pairs

pb.CSBhdTeamBeginMatchTRes = { __name = "CSBhdTeamBeginMatchTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSBhdTeamBeginMatchTRes : ProtoBase
---@field public result number

---@return pb_CSBhdTeamBeginMatchTRes
function pb.CSBhdTeamBeginMatchTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeIsOpenQuickJoinTReq = {
    TeamId = 0,
    isOpenQuickJoin = false,
}
pb.__pb_CSTeamChangeIsOpenQuickJoinTReq.__name = "CSTeamChangeIsOpenQuickJoinTReq"
pb.__pb_CSTeamChangeIsOpenQuickJoinTReq.__index = pb.__pb_CSTeamChangeIsOpenQuickJoinTReq
pb.__pb_CSTeamChangeIsOpenQuickJoinTReq.__pairs = __pb_pairs

pb.CSTeamChangeIsOpenQuickJoinTReq = { __name = "CSTeamChangeIsOpenQuickJoinTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeIsOpenQuickJoinTReq : ProtoBase
---@field public TeamId number
---@field public isOpenQuickJoin boolean

---@return pb_CSTeamChangeIsOpenQuickJoinTReq
function pb.CSTeamChangeIsOpenQuickJoinTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamChangeIsOpenQuickJoinTRes = {
    result = 0,
}
pb.__pb_CSTeamChangeIsOpenQuickJoinTRes.__name = "CSTeamChangeIsOpenQuickJoinTRes"
pb.__pb_CSTeamChangeIsOpenQuickJoinTRes.__index = pb.__pb_CSTeamChangeIsOpenQuickJoinTRes
pb.__pb_CSTeamChangeIsOpenQuickJoinTRes.__pairs = __pb_pairs

pb.CSTeamChangeIsOpenQuickJoinTRes = { __name = "CSTeamChangeIsOpenQuickJoinTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamChangeIsOpenQuickJoinTRes : ProtoBase
---@field public result number

---@return pb_CSTeamChangeIsOpenQuickJoinTRes
function pb.CSTeamChangeIsOpenQuickJoinTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamBhdNextLevelReadyTReq = {
    team_id = 0,
    is_ready = false,
    chapter_id = 0,
}
pb.__pb_CSTeamBhdNextLevelReadyTReq.__name = "CSTeamBhdNextLevelReadyTReq"
pb.__pb_CSTeamBhdNextLevelReadyTReq.__index = pb.__pb_CSTeamBhdNextLevelReadyTReq
pb.__pb_CSTeamBhdNextLevelReadyTReq.__pairs = __pb_pairs

pb.CSTeamBhdNextLevelReadyTReq = { __name = "CSTeamBhdNextLevelReadyTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamBhdNextLevelReadyTReq : ProtoBase
---@field public team_id number
---@field public is_ready boolean
---@field public chapter_id number

---@return pb_CSTeamBhdNextLevelReadyTReq
function pb.CSTeamBhdNextLevelReadyTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamBhdNextLevelReadyTRes = {
    result = 0,
}
pb.__pb_CSTeamBhdNextLevelReadyTRes.__name = "CSTeamBhdNextLevelReadyTRes"
pb.__pb_CSTeamBhdNextLevelReadyTRes.__index = pb.__pb_CSTeamBhdNextLevelReadyTRes
pb.__pb_CSTeamBhdNextLevelReadyTRes.__pairs = __pb_pairs

pb.CSTeamBhdNextLevelReadyTRes = { __name = "CSTeamBhdNextLevelReadyTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamBhdNextLevelReadyTRes : ProtoBase
---@field public result number

---@return pb_CSTeamBhdNextLevelReadyTRes
function pb.CSTeamBhdNextLevelReadyTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamSessionUpdateTReq = {
    team_id = 0,
    session = "",
}
pb.__pb_CSTeamSessionUpdateTReq.__name = "CSTeamSessionUpdateTReq"
pb.__pb_CSTeamSessionUpdateTReq.__index = pb.__pb_CSTeamSessionUpdateTReq
pb.__pb_CSTeamSessionUpdateTReq.__pairs = __pb_pairs

pb.CSTeamSessionUpdateTReq = { __name = "CSTeamSessionUpdateTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamSessionUpdateTReq : ProtoBase
---@field public team_id number
---@field public session string

---@return pb_CSTeamSessionUpdateTReq
function pb.CSTeamSessionUpdateTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamSessionUpdateTRes = {
    result = 0,
}
pb.__pb_CSTeamSessionUpdateTRes.__name = "CSTeamSessionUpdateTRes"
pb.__pb_CSTeamSessionUpdateTRes.__index = pb.__pb_CSTeamSessionUpdateTRes
pb.__pb_CSTeamSessionUpdateTRes.__pairs = __pb_pairs

pb.CSTeamSessionUpdateTRes = { __name = "CSTeamSessionUpdateTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamSessionUpdateTRes : ProtoBase
---@field public result number

---@return pb_CSTeamSessionUpdateTRes
function pb.CSTeamSessionUpdateTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SSTeamGetLeaderInfoReq = {
}
pb.__pb_SSTeamGetLeaderInfoReq.__name = "SSTeamGetLeaderInfoReq"
pb.__pb_SSTeamGetLeaderInfoReq.__index = pb.__pb_SSTeamGetLeaderInfoReq
pb.__pb_SSTeamGetLeaderInfoReq.__pairs = __pb_pairs

pb.SSTeamGetLeaderInfoReq = { __name = "SSTeamGetLeaderInfoReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SSTeamGetLeaderInfoReq : ProtoBase

---@return pb_SSTeamGetLeaderInfoReq
function pb.SSTeamGetLeaderInfoReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_SSTeamGetLeaderInfoRes = {
    result = 0,
    leader_id = 0,
}
pb.__pb_SSTeamGetLeaderInfoRes.__name = "SSTeamGetLeaderInfoRes"
pb.__pb_SSTeamGetLeaderInfoRes.__index = pb.__pb_SSTeamGetLeaderInfoRes
pb.__pb_SSTeamGetLeaderInfoRes.__pairs = __pb_pairs

pb.SSTeamGetLeaderInfoRes = { __name = "SSTeamGetLeaderInfoRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_SSTeamGetLeaderInfoRes : ProtoBase
---@field public result number
---@field public leader_id number

---@return pb_SSTeamGetLeaderInfoRes
function pb.SSTeamGetLeaderInfoRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamGetShortNumTReq = {
    team_id = 0,
}
pb.__pb_CSTeamGetShortNumTReq.__name = "CSTeamGetShortNumTReq"
pb.__pb_CSTeamGetShortNumTReq.__index = pb.__pb_CSTeamGetShortNumTReq
pb.__pb_CSTeamGetShortNumTReq.__pairs = __pb_pairs

pb.CSTeamGetShortNumTReq = { __name = "CSTeamGetShortNumTReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamGetShortNumTReq : ProtoBase
---@field public team_id number

---@return pb_CSTeamGetShortNumTReq
function pb.CSTeamGetShortNumTReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamGetShortNumTRes = {
    result = 0,
    team_short_num_id = "",
    expire_time = 0,
}
pb.__pb_CSTeamGetShortNumTRes.__name = "CSTeamGetShortNumTRes"
pb.__pb_CSTeamGetShortNumTRes.__index = pb.__pb_CSTeamGetShortNumTRes
pb.__pb_CSTeamGetShortNumTRes.__pairs = __pb_pairs

pb.CSTeamGetShortNumTRes = { __name = "CSTeamGetShortNumTRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamGetShortNumTRes : ProtoBase
---@field public result number
---@field public team_short_num_id string
---@field public expire_time number

---@return pb_CSTeamGetShortNumTRes
function pb.CSTeamGetShortNumTRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamJoinByShortNumReq = {
    team_short_num_id = "",
}
pb.__pb_CSTeamJoinByShortNumReq.__name = "CSTeamJoinByShortNumReq"
pb.__pb_CSTeamJoinByShortNumReq.__index = pb.__pb_CSTeamJoinByShortNumReq
pb.__pb_CSTeamJoinByShortNumReq.__pairs = __pb_pairs

pb.CSTeamJoinByShortNumReq = { __name = "CSTeamJoinByShortNumReq", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamJoinByShortNumReq : ProtoBase
---@field public team_short_num_id string
---@field public PackQuestID number[]

---@return pb_CSTeamJoinByShortNumReq
function pb.CSTeamJoinByShortNumReq:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


pb.__pb_CSTeamJoinByShortNumRes = {
    result = 0,
}
pb.__pb_CSTeamJoinByShortNumRes.__name = "CSTeamJoinByShortNumRes"
pb.__pb_CSTeamJoinByShortNumRes.__index = pb.__pb_CSTeamJoinByShortNumRes
pb.__pb_CSTeamJoinByShortNumRes.__pairs = __pb_pairs

pb.CSTeamJoinByShortNumRes = { __name = "CSTeamJoinByShortNumRes", __service="team", InternalNew = JustForProtoNew}

------------------------------------------------------
--- Auto Generated Code For EmmyLua Hint
---@class pb_CSTeamJoinByShortNumRes : ProtoBase
---@field public result number
---@field public Info pb_TeamInfo

---@return pb_CSTeamJoinByShortNumRes
function pb.CSTeamJoinByShortNumRes:New()
	local Req = self:InternalNew()
	return Req
end
------------------------------------------------------


