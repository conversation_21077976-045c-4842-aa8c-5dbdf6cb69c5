----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMQuest)
----- LOG FUNCTION AUTO GENERATE END -----------



local QuestSeasonDot = require "DFM.Business.Module.QuestModule.UI.Season.QuestSeasonDot"
---@class QuestSeasonEntranceMain : LuaUIBaseView

local QuestSeasonEntranceMain = ui("QuestSeasonEntranceMain")
function QuestSeasonEntranceMain:Ctor()

    self._wtStageText = self:Wnd("DFTextBlock_112", UITextBlock)
    self._wtStageIconList = {
        self:Wnd("WBP_SeasonalTasks_PhaseItem", UIWidgetBase),
        self:Wnd("WBP_SeasonalTasks_PhaseItem_1", UIWidgetBase),
        self:Wnd("WBP_SeasonalTasks_PhaseItem_2", UIWidgetBase),
        self:Wnd("WBP_SeasonalTasks_PhaseItem_3", UIWidgetBase),
    }

    self._wtCurrentStarText = self:Wnd("DFRichTextBlock_1", UITextBlock)
    self._wtGoalStarText = self:Wnd("DFRichTextBlock_0", UITextBlock)

    self._wtMainProgressText = self:Wnd("DFTextBlock_122", UITextBlock)
    self._wtBranchHard = self:Wnd("DFWrapBox_63", UIWidgetBase)
    self._wtBranchHardPanel = self:Wnd("DFHorizontalBox_1", UIWidgetBase)
    self._wtBranchEasy = self:Wnd("DFWrapBox", UIWidgetBase)
    self._wtBranchEasyPanel = self:Wnd("DFHorizontalBox_4", UIWidgetBase)
    self._wtDivLineHard = self:Wnd("DFImage_6", UIWidgetBase)
    self._wtDivLineEasy = self:Wnd("DFImage_7", UIWidgetBase)

    self._wtContentPanel = self:Wnd("DFCanvas_Content", UIWidgetBase)
    self._wtLockPanel = self:Wnd("WBP_SlotCompMaskLock", UIWidgetBase)

    self._wtBtn = self:Wnd("DFBtn", UIButton)
    if self._wtBtn then
        self._wtBtn:Event("OnClicked", self._OnBtnClicked, self)
    end

    self._wtBgImg = self:Wnd("DFImage_Icon", UIImage)
    self._lineInfo = nil
    self._bIsLocked = true
    self._openLevel = nil
end

function QuestSeasonEntranceMain:OnHide()
    if self.curReddotProxy ~= nil then
        Module.ReddotTrie:UnRegisterDynamicReddot(EReddotTrieObserverType.Quest,EQuestDynamicDataType.EQuestSeasonReddot,self.curReddotProxy)
        self.curReddotProxy = nil
    end
end

function QuestSeasonEntranceMain:InitInfo(lineInfo)
    
    local curStageIcon = self._wtStageIconList[1]
    local curStage = nil

    for index, value in pairs(lineInfo.table_StageArry) do
        if value.order == 1 then 
            curStage = value.stageID
            break
        end
    end

    for index, value in pairs(lineInfo.table_StageArry) do
        local stageIcon = self._wtStageIconList[value.order]
        if stageIcon then
            if lineInfo:IsUnLockByStageID(value.stageID) then
                local text = stageIcon:Wnd("DFTextBlock_42", UITextBlock)
                text:SetText(tostring(value.order))
                text:Visible()
                stageIcon:SetStateType(2)
                if curStage then
                    local curOrder = lineInfo:GetStageInfoByID(curStage).order
                    if value.order > curOrder then
                        curStage = value.stageID
                        curStageIcon = stageIcon
                    end 
                else
                    if value.order == 1 then
                        curStage = value.stageID
                    end
                end
            else
                stageIcon:SetStateType(0)
            end
        end
    end
    curStageIcon:SetStateType(1)

    local stageInfo = lineInfo:GetStageInfoByID(curStage)
    self._wtStageText:SetText(stageInfo.name)

    local gainedStar = lineInfo:CalGainStarByStageID(curStage)
    self._wtCurrentStarText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonMissionCurStage,
    gainedStar,
    lineInfo:CalTotalStarByStageID(curStage)
    ))

    local nextStage = lineInfo:GetNextStageInfoByID(curStage)
    if nextStage and nextStage.unlockNeedStar > gainedStar then
        self._wtGoalStarText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonMissionGoal, nextStage.unlockNeedStar - gainedStar))
        self._wtGoalStarText:SelfHitTestInvisible()
    else
        self._wtGoalStarText:Collapsed()
    end


    -- Current Stage Progress
    local mainGroup = lineInfo:GetMainGroupIDByStageID(curStage)
    local completeNum = lineInfo:GetCompletedQuestNum(mainGroup)
    local total = lineInfo:GetAllQuestIdsByGroupID(mainGroup)
    self._wtMainProgressText:SetText(string.format(Module.Quest.Config.Loc.QuestSeasonStarProgress,
    lineInfo:GetCompletedQuestNum(mainGroup),
    #total
    ))

    -- Hard Branch Quest Progress
    local EQuestSeasonType = Module.Quest.Config.EQuestSeasonType
    local branchHard = lineInfo:GetSortedBranchGroupByStageIDAndType(curStage, EQuestSeasonType.Hard)
    if #branchHard > 0 then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtBranchHard)
        for index, value in pairs(branchHard) do
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonDot, self._wtBranchHard)
            local dot = getfromweak(uiIns)
            if dot then
                if lineInfo:IsGroupComplete(value) then
                    dot:SetType(1)
                else
                    dot:SetType(0)
                end
            end
        end
        self._wtBranchHardPanel:SelfHitTestInvisible()
        self._wtDivLineHard:SelfHitTestInvisible()
    else
        self._wtBranchHardPanel:Collapsed()
        self._wtDivLineHard:Collapsed()
    end

    local branchEasy = lineInfo:GetSortedBranchGroupByStageIDAndType(curStage, EQuestSeasonType.Easy)
    if #branchEasy > 0 then
        Facade.UIManager:RemoveSubUIByParent(self, self._wtBranchEasy)
        for index, value in pairs(branchEasy) do
            local uiIns, instanceID = Facade.UIManager:AddSubUI(self,UIName2ID.QuestSeasonDot, self._wtBranchEasy)
            local dot = getfromweak(uiIns)
            if dot then
                if lineInfo:IsGroupComplete(value) then
                    dot:SetType(1)
                else
                    dot:SetType(0)
                end
            end
        end
        self._wtBranchEasyPanel:SelfHitTestInvisible()
        self._wtDivLineEasy:SelfHitTestInvisible()
    else
        self._wtBranchEasyPanel:Collapsed()
        self._wtDivLineEasy:Collapsed()
    end

    self._lineInfo = lineInfo

    self:_RegisterReddot()

end

function QuestSeasonEntranceMain:SetIsLocked(bIsLocked, levelNum)
    self._bIsLocked = bIsLocked
    self._openLevel = levelNum
    if bIsLocked then 
        self._wtLockPanel:SelfHitTestInvisible()
        self._wtContentPanel:Collapsed()
        self._wtLockPanel:SetDescText(string.format(Module.Quest.Config.Loc.LvlLimitPureTxt, levelNum))
    else
        self._wtLockPanel:Collapsed()
        self._wtContentPanel:Visible()
    end
end

function QuestSeasonEntranceMain:SetBgImg(path)
    self._wtBgImg:AsyncSetImagePath(path)
end

function QuestSeasonEntranceMain:_OnBtnClicked()
    if self._bIsLocked then
        Module.CommonTips:ShowSimpleTip(string.format(Module.Quest.Config.Loc.QuestSeasonLocked, self._openLevel))
        return 
    end

    if self._lineInfo then
        Module.Quest:OpenSeasonListPanel(self._lineInfo, nil, nil)
    end
end

function QuestSeasonEntranceMain:_RegisterReddot()
    if self.curReddotProxy == nil then
        local fCheckReddot = function (lineInfo)
            local needRet = Server.QuestServer:GetNotifyQuestInSeasonLine(lineInfo)
            return needRet
        end
        local styleParams = {
            placeMode = EReddotPlaceMode.Custom,
            placeOffset = FVector2D(0, 12)
        }
        self.curReddotProxy = Module.ReddotTrie:RegisterDynamicReddot(EReddotTrieObserverType.Quest,
            EQuestDynamicDataType.EQuestSeasonReddot,fCheckReddot,self._lineInfo,self, styleParams)
    end
end

return QuestSeasonEntranceMain