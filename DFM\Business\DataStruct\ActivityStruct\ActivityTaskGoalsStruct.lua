---@class ActivityTaskGoalsStruct : LuaObject
-- 对应ActivityTaskGoals表格
ActivityTaskGoalsStruct = class('ActivityTaskGoalsStruct', LuaObject)

OverrideClassByDynamicUD(ActivityTaskGoalsStruct, false,
    {"GoalID", FieldType.Integer},
    {"GoalType", FieldType.Integer}
)

---@param ActivityConfig AT
function ActivityTaskGoalsStruct:Ctor(ActivityConfig)
    self:InitActivityStruct(ActivityConfig)
end

---@param ActivityConfig AT
function ActivityTaskGoalsStruct:InitActivityStruct(ActivityConfig)
    self.GoalID = ActivityConfig.GoalID or 0
    self.GoalType = ActivityConfig.GoalType or 0
end

return ActivityTaskGoalsStruct