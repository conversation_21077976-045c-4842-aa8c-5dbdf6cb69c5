---@class ActivityTableTaskStruct : LuaObject
ActivityTableTaskStruct = class('ActivityTableTaskStruct', LuaObject)
-- 对应ActivityTask表格
---@param ActivityTableTaskStruct AT
function ActivityTableTaskStruct:Ctor(ActivityConfig)
    self:InitActivityStruct(ActivityConfig)
end

---@param ActivityTableTaskStruct AT
function ActivityTableTaskStruct:InitActivityStruct(ActivityConfig)

    self.TaskID = ActivityConfig.TaskID or 0
    self.Name = ActivityConfig.Name or ""
    self.Desc = ActivityConfig.Desc or ""
    self.ObjectiveList = ActivityConfig.ObjectiveList or 0
    self.Reward1ID = ActivityConfig.Reward1ID or 0
    self.Reward1Num = ActivityConfig.Reward1Num or 0
    self.Reward2ID = ActivityConfig.Reward2ID or 0
    self.Reward2Num = ActivityConfig.Reward2Num or 0
    self.Reward3ID = ActivityConfig.Reward3ID or 0
    self.Reward3Num = ActivityConfig.Reward3Num or 0

    self.RedirectWay = ActivityConfig.RedirectWay or 0
    self.ManualRedirectPara1 = ActivityConfig.ManualRedirectPara1 or 0
    self.ManualRedirectPara2 = ActivityConfig.ManualRedirectPara2 or 0
    self.ManualRedirectPara3 = ActivityConfig.ManualRedirectPara3 or 0

    self.game_mode = ActivityConfig.game_mode or 0
    self.game_rule = ActivityConfig.game_rule or 0
    self.sub_mode = ActivityConfig.sub_mode or 0
    self.team_mode = ActivityConfig.team_mode or 0
    self.raid_id = ActivityConfig.raid_id or 0
    self.map_id = ActivityConfig.map_id or 0
end

return ActivityTableTaskStruct