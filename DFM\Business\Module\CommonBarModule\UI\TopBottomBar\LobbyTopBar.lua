----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCommonBar)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class LobbyTopBar : LuaUIBaseView
local LobbyTopBar = ui("LobbyTopBar")


local LobbyDrawView = require "DFM.Business.Module.CommonBarModule.UI.TopBottomBar.LobbyDrawView"
local LobbyTopMoreDrawView = require "DFM.Business.Module.CommonBarModule.UI.TopBottomBar.LobbyTopMoreDrawView"
local CommonHeadIcon = require "DFM.Business.Module.CommonWidgetModule.UI.CommonHeadIcon"
local LiteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local CommonBarUnlockLogic = require "DFM.Business.Module.CommonBarModule.Logic.CommonBarUnlockLogic"
local CustomerServicesEntranceType = import "ECustomerServicesEntranceType"
local IPlayerReturnSubActivity = require "DFM.Business.Module.PlayerReturnModule.SubActivities.PlayerReturnSubActivities"

local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum
local EHorizontalAlignment = import "EHorizontalAlignment"

function LobbyTopBar:Ctor()
    self._wtPlayHead = self:Wnd("wtPlayHead", CommonHeadIcon)

    self._wtHbLeftTop = self:Wnd("DFHorizontalBox_Left", UIWidgetBase)
    self._reddotProxyTable = {}

    self._wtVbLeftTop = self:Wnd("DFVerticalBox_0",UIWidgetBase)

    self._wtAnnouncementPanel=self:Wnd("WBP_Hall_Announcement", Announcement)
	self._wtAnnouncementPanel:Collapsed()

    self._wtDownloadWidget = self:Wnd("wtDownloadBtn", UIWidgetBase)
    self._wtDownloadRedDotWidget= Module.ReddotTrie:CreateReddotIns(self._wtDownloadWidget)
    self._wtDownloadBtn = self._wtDownloadWidget:Wnd("wtSetupBtn", UIButton)
    self._wtDownloadBtn:Event("OnClicked", self._OnDownloadBtnClick, self)

    self._wtImage_Invisibility = self:Wnd("DFImage_Invisibility", UIImage)

    -- self._wtCustomerServiceBtn = self:Wnd("wtCustomerServiceBtn", UIWidgetBase)
    -- self._wtCustomerServiceBtn:OnInitExtraData(CustomerServicesEntranceType.MainPage)

    -- if Module.CustomerServices:CheckEntranceEnable(CustomerServicesEntranceType.MainPage)
    --         and Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemCustomerService) == EFirstLockResult.Open
    --         and (not VersionUtil.IsShipping()) then
    --     self._wtCustomerServiceBtn:Visible()
    -- else
    --     self._wtCustomerServiceBtn:Collapsed()
    -- end

    self._wtAllBtnDrawView = self:Wnd("wtAllBtn", LobbyTopMoreDrawView)
    self._wtLobbyDrawView = self:Wnd("WBP_Common_LobbyDraw", LobbyDrawView)

    if IsHD() then
        self._wtAllBtnDrawView:Collapsed()
        self._wtLobbyDrawView:Collapsed()
        self._wtPlayHead:Collapsed()
        self._wtDownloadBtn:Collapsed()
        self._wtDownloadWidget:Collapsed()
        -- self._wtRecruitBtn:Collapsed()
        -- self._wtInviteBtn:Collapsed()
    end
end

function LobbyTopBar:OnShowBegin()
    loginfo("LobbyTopBar:OnShowBegin")
    self:InitStaticTopBarButtons()
    self:InitDynamicLeftTopBarButtons()
    self:InitRecoveryHallTip()
    self:InitReflowTip()
    self:InitDownloadReddotMobile()
    Module.CommonBar:SetIsLobbyTopBarShow(true)
    Module.CommonBar.Config.evtLobbyTopBarShow:Invoke()
    self:CheckDownloadReddotMobile()
end

function LobbyTopBar:OnShow()
    loginfo("LobbyTopBar:OnShow")
end

function LobbyTopBar:InitReflowTip()
    if IsHD() then
        return
    end

    local currentGameMode = Server.ArmedForceServer:GetCurArmedForceMode()
    local bShowReturnTip = Module.PlayerReturn:IsActive(currentGameMode)

    if bShowReturnTip then
        self:_AddReflowTipWidget()
        return
    else
        Server.TipsRecordServer:SetBoolean(Server.TipsRecordServer.keys.PlayerReturnClickEntrance,false)
        self:_RemoveReflowTipWidget()
    end
end

function LobbyTopBar:InitStaticTopBarButtons()
    local myPlayerSimpleInfo = {
        player_id = Server.AccountServer:GetPlayerId(),
        nick_name = Server.RoleInfoServer.nickName or "",
        pic_url = Server.RoleInfoServer.picUrl,
        level = Server.RoleInfoServer.accountLevel,
        season_lvl = Server.RoleInfoServer.seasonLevel,
        gender = Server.RoleInfoServer.gender,
        safehouse_degree = 0,
    }
    --根据大厅显示对应模式等级
    local curGameFlow = Facade.GameFlowManager:GetCurrentGameFlow()
    --SOL/MP
    if curGameFlow == EGameFlowStageType.SafeHouse then
        myPlayerSimpleInfo.level = Server.RoleInfoServer.seasonLevel
    else
        myPlayerSimpleInfo.level = Server.RoleInfoServer.accountLevel
    end

    self._wtPlayHead:InitPortrait(myPlayerSimpleInfo, HeadIconType.HeadPerInformat)
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if DFHD_LUA == 0 then
        if armedForceMode == EArmedForceMode.SOL then
            self._wtPlayHead:SetLogClickId(ButtonIdEnum.SOLLobbyRoleInfo)
        else
            self._wtPlayHead:SetLogClickId(ButtonIdEnum.MPLobbyRoleInfo)
        end
    else
        --- TODO 暂无经分
    end

    -- self:_RefreshLiveRadioBtn()
    if Module.LiveRadio and Module.LiveRadio.Config and Module.LiveRadio.Config.evtLiveRadioRefresh then
        Module.LiveRadio.Config.evtLiveRadioRefresh:AddListener(self._RefreshLiveRadioBtn, self)
    end
    -- if self._LiveStreamRedDot == nil then
    --     local key = "LiveRadioEntrance"
    --     local data = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.LiveRadio, key)
    --     self._LiveStreamRedDot = Module.ReddotTrie:RegisterStaticReddotDot(self._wtLiveStreamRedDotCanvas, {{ reddotData = data}})
    --     loginfo("_LiveStreamRedDot RegisterStaticReddotDot")
    -- end

    if LiteDownloadManager:IsSupportLitePackage() then
        -- self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadBegin, self._OnDownloadBegin, self)
        -- self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress, self._OnDownloadProgress, self)
        -- self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self._OnDownloadResult, self)

        -- 定时刷新功能
        if self.tickHandle then
            self.tickHandle:Release()
            self.tickHandle = nil
        end

        local bDownlaodedAll = LiteDownloadManager:IsAllQuestDownloaded()
        if bDownlaodedAll == false then
            self._wtDownloadWidget:Visible()
            self.tickHandle = Timer:NewIns(1, 0)
            self.tickHandle:AddListener(function()     
                self:_OnDownloadEventUpdate()
            end, self)
            self.tickHandle:Start()
        else
            self._wtDownloadWidget:Collapsed()
        end

        local bHasQuestDownloading = LiteDownloadManager:IsQuestDownloading()
        if bHasQuestDownloading then
            self._wtDownloadWidget:PlayAnimation(self._wtDownloadWidget.WBP_Hall_TopLeft_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
        end
    end

    --我自己是隐身状态，不显示准备图标，仅显示隐身状态图标
    if (myPlayerSimpleInfo.player_id == Server.AccountServer:GetPlayerId()) and Module.Friend:GetSelfLoginInvisible() then
        self._wtImage_Invisibility:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    else
        self._wtImage_Invisibility:Collapsed()
    end
end

function LobbyTopBar:InitDynamicLeftTopBarButtons()
    local Config = Module.CommonBar.Config
    local minVisibleBtn
    local curVisibleLength = 0
    local idx = 0
    Facade.UIManager:RemoveSubUIByParent(self, self._wtHbLeftTop)
	self:ReleaseReddotProxies()

    if Config.MobileTopLeftTabInfo and next(Config.MobileTopLeftTabInfo) then
        for index, tabInfo in ipairs(Config.MobileTopLeftTabInfo) do
            if Module.CommonBar.Field:CheckTabBtnShow(tabInfo) then
                local weakUIIns = nil
                if tabInfo.enName == "MicroOfficialWeb" then
                    weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.TopBarButtonLeftMicroOfficial, self._wtHbLeftTop, index)
                else
                    weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.TopBarButtonLeft, self._wtHbLeftTop, index)
                end
                local topBarButtonLeft = getfromweak(weakUIIns)
                if topBarButtonLeft then
                    if tabInfo.iconID then
                        topBarButtonLeft:SetBtnIcon(tabInfo.iconID)
                    end
                    -- if tabInfo.btnTitle then
                    --     topBarButtonLeft:SetBtnTitle(tabInfo.btnTitle)
                    -- end
                    local firstLockID = tabInfo.firstLockID or false
                    local secondLockID = tabInfo.secondLockID or false

                    local lockData = {
                        firstLockID = firstLockID,
                        secondLockID = secondLockID,
                        uiNavID = tabInfo.openUIID,
                        fFailedCallback = tabInfo.openFaildFunc,
                        failedCallbackCaller = nil,

                        fCustomLockChecker = tabInfo.customCheckFunc,
                        customLockCheckerCaller = nil
                    }
                    CommonBarUnlockLogic.InitBarBtnClickedState(topBarButtonLeft, tabInfo.openFunc, lockData)
                    topBarButtonLeft:SetLastBtnShow(false)

                    if topBarButtonLeft:IsVisible() then
                        if minVisibleBtn == nil then
                            minVisibleBtn = topBarButtonLeft
                        end
                    end

                    if topBarButtonLeft:IsVisible() and topBarButtonLeft:GetRenderOpacity() > 0 then
                        curVisibleLength = curVisibleLength + 1
                    end
                    if curVisibleLength > 1 then
                        topBarButtonLeft.Slot:SetPadding(FMargin(39, 0, 0, 0))
                    end

                    local placeOperation1 = function(reddot)
                        local FAnchors = import("Anchors")
                        local anchors = FAnchors()
                        anchors.Minimum = FVector2D(1, 0)
                        anchors.Maximum = FVector2D(1, 0)
                        local canvasSlot = reddot.Slot
                        canvasSlot:SetAnchors(anchors)
                        canvasSlot:SetPosition(FVector2D(0, 0))
                        canvasSlot:SetAlignment(FVector2D(1, 0))
                        -- reddot:SetRenderScale(FVector2D(0.8,0.8))
                    end
                    ---@type ReddotTrieTabRegItem
                    local reddotTrieTabRegItem = tabInfo.reddotTrieTabRegItem
                    if reddotTrieTabRegItem and not self._reddotProxyTable[index] then
                        if next(reddotTrieTabRegItem) then
                            local newProxy =
                                Module.ReddotTrie:RegisterStaticReddotDotWithConfig(
                                topBarButtonLeft:BP_GetReddotPanel(),
                                reddotTrieTabRegItem.reddotDataConfigWithStyleList
                            )
                            newProxy:SetDotPlaceMode(
                                EReddotPlaceMode.Custom,
                                placeOperation1
                            )
                            self._reddotProxyTable[index] = {
                                reddotTrieTabRegItem.uiNavId,
                                newProxy,
                                bNewReddotTrie = true
                            }
                        end
                    end

                    -- Module.Guide:AddGuideWidgetProxy(Module.Guide.Config.EGuideProxyWidget[string.format("guideProxyTopBarMore%d", index)], topBarButtonLeft)
                    idx = idx + 1
                end
            end
        end
        if minVisibleBtn and minVisibleBtn.SetLastBtnShow then
            minVisibleBtn:SetLastBtnShow(true)
        end
    end
    if curVisibleLength >= 1 then
        self._wtHbLeftTop:SetVisibility(ESlateVisibility.Visible)
    else
        self._wtHbLeftTop:SetVisibility(ESlateVisibility.Collapsed)
    end
end

function LobbyTopBar:InitRecoveryHallTip()
    
    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    local isInRecoveryState = Server.RecoveryServer:IsInRecoveryState()

    if armedForceMode and isInRecoveryState and armedForceMode == EArmedForceMode.SOL then
        if self._wtRecoveryTipID ~= nil then
            self:_RemoveRecoveryHallTip()
        end
        local uiIns, instanceID = Facade.UIManager:AddSubUIAtIndex(self,UIName2ID.RecoveryHallTips,0,self._wtVbLeftTop)
        self._wtRecoveryTipButton = getfromweak(uiIns)
        local verticalBoxSlot = UWidgetLayoutLibrary.SlotAsVerticalBoxSlot(self._wtRecoveryTipButton)
        if verticalBoxSlot then
            verticalBoxSlot:SetHorizontalAlignment(EHorizontalAlignment.HAlign_Left)
        end
        self._wtRecoveryTipID = instanceID
        self:AddLuaEvent(Server.RecoveryServer.Events.evtUpdateRecoveryAmount, self._OnUpdateRecoveryAmount, self)
        self:AddLuaEvent(Server.RecoveryServer.Events.evtSumbitedRecoverySuccess, self._OnUpdateRecoveryAmount, self)
    end

end

function LobbyTopBar:_OnUpdateRecoveryAmount(changes, bhasRecoveredAll)
    if bhasRecoveredAll then
        self:_RemoveRecoveryHallTip()
    end
end

function LobbyTopBar:_RemoveRecoveryHallTip()
    Facade.UIManager:RemoveSubUI(self, UIName2ID.RecoveryHallTips, self._wtRecoveryTipID)
    self._wtRecoveryTipID = nil
    self:RemoveLuaEvent(Server.RecoveryServer.Events.evtUpdateRecoveryAmount, self._OnUpdateRecoveryAmount)
    self:RemoveLuaEvent(Server.RecoveryServer.Events.evtSumbitedRecoverySuccess, self._OnUpdateRecoveryAmount)
end

function LobbyTopBar:OnHideBegin()
    loginfo("LobbyTopBar:OnHideBegin")
    Module.CommonBar:SetIsLobbyTopBarShow(false)
    Module.CommonBar.Config.evtLobbyTopBarHide:Invoke()

end

function LobbyTopBar:OnHide()
    loginfo("LobbyTopBar:OnHide")
    self:ReleaseReddotProxies()
    if LiteDownloadManager:IsSupportLitePackage() then
        -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadBegin)
        -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress)
        -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
        if self.tickHandle then
            self.tickHandle:Release()
            self.tickHandle = nil
        end
    end

    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if self._wtRecoveryTipID and armedForceMode and armedForceMode == EArmedForceMode.SOL then
        self:_RemoveRecoveryHallTip()
    end

    self:_RemoveReflowTipWidget()
    self:RemoveDownloadReddotMobile()

    -- if self._LiveStreamRedDot then
    --     loginfo("_LiveStreamRedDot UnRegisterStaticReddotDot")
    --     Module.ReddotTrie:UnRegisterStaticReddotDot(self._LiveStreamRedDot)
    --     self._LiveStreamRedDot = nil
    -- end

end

function LobbyTopBar:_AddReflowTipWidget()
    self:_RemoveReflowTipWidget()
    local uiIns, instanceID = Facade.UIManager:AddSubUIAtIndex(self, UIName2ID.PlayerReturnEntrance, 1, self._wtVbLeftTop)
    self._wtReflowTipID = instanceID
end

function LobbyTopBar:_RemoveReflowTipWidget()
    if self._wtReflowTipID then
        Facade.UIManager:RemoveSubUI(self, UIName2ID.PlayerReturnEntrance, self._wtReflowTipID)
        self._wtReflowTipID = nil
    end
end

function LobbyTopBar:OnOpen()
    local data = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.RoleInfo, "RoleInfoMessage")
    local reddotCanvas=self._wtPlayHead and self._wtPlayHead:Wnd("DFCanvasPanel_110")
    self._reddot= Module.ReddotTrie:RegisterStaticReddotDot(reddotCanvas or self._wtPlayHead, {{reddotData = data,reddotStyle={reddotType=EReddotType.Normal}}})
end

function LobbyTopBar:OnClose()
    loginfo("LobbyTopBar:OnClose")
    self:ReleaseReddotProxies()
    if Module.LiveRadio and Module.LiveRadio.Config and Module.LiveRadio.Config.evtLiveRadioRefresh then
        Module.LiveRadio.Config.evtLiveRadioRefresh:RemoveListener(self._RefreshLiveRadioBtn, self)
    end
    
    if LiteDownloadManager:IsSupportLitePackage() then
        -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadBegin)
        -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress)
        -- self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
        if self.tickHandle then
            self.tickHandle:Release()
            self.tickHandle = nil
        end
    end
end


function LobbyTopBar:ReleaseReddotProxies()
    for _, proxy in pairs(self._reddotProxyTable) do
        Module.ReddotTrie:UnRegisterStaticReddotDot(proxy[2])
    end
    self._reddotProxyTable = {}
end

function LobbyTopBar:_RefreshLiveRadioBtn()
    self:InitDynamicLeftTopBarButtons()
end

function LobbyTopBar:_OnDownloadBtnClick()
    --Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CommingSoon)
    Module.LitePackage:ShowMainPanel(true)

    local armedForceMode = Server.ArmedForceServer:GetCurArmedForceMode()
    if DFHD_LUA == 0 then
        if armedForceMode == EArmedForceMode.SOL then
            LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyDownload)
        else
            LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPLobbyDownload)
        end
    else
        --- TODO 暂无经分
    end
end

function LobbyTopBar:_OnLiveStreamBtnClick()
    --Module.CommonTips:ShowSimpleTip(Module.CommonTips.Config.Loc.CommingSoon)
    Module.LiveRadio:ShowMainPanel()
    --- TODO 暂无经分
end

function LobbyTopBar:_OnDownloadBegin(modulename)
    self:_OnDownloadEventUpdate()
end

function LobbyTopBar:_OnDownloadProgress(modulename, nowSize, totalSize)
    self:_OnDownloadEventUpdate()
end

function LobbyTopBar:_OnDownloadResult(modulename)
    self:_OnDownloadEventUpdate()
end

function LobbyTopBar:_OnDownloadEventUpdate()
    local bDownlaodedAll = LiteDownloadManager:IsAllQuestDownloaded()
    if bDownlaodedAll then
        self._wtDownloadWidget:Collapsed()

        if self.tickHandle then
            self.tickHandle:Release()
            self.tickHandle = nil
        end

        return
    else
        self._wtDownloadWidget:Visible()
    end

    local bHasQuestDownloading = LiteDownloadManager:IsQuestDownloading()
    local bPlayingAnim = self._wtDownloadWidget:IsAnimationPlaying(self._wtDownloadWidget.WBP_Hall_TopLeft_loop)
    if bHasQuestDownloading then
        if not bPlayingAnim then
            self._wtDownloadWidget:PlayAnimation(self._wtDownloadWidget.WBP_Hall_TopLeft_loop, 0, 0, EUMGSequencePlayMode.Forward, 1, true)
        end
    else
        if bPlayingAnim then
            self._wtDownloadWidget:StopAnimation(self._wtDownloadWidget.WBP_Hall_TopLeft_loop)
        end
    end
end

--跑马灯公告
function LobbyTopBar:ShowAnnouncement()
	local bShow,bRolling,content=Server.FrontEndChatServer:CheckShowAnnouncement()
	if bShow then
		self._wtAnnouncementPanel:SetData(bRolling,content)
		self._wtAnnouncementPanel:SelfHitTestInvisible()
		self._wtAnnouncementPanel:PlayAnimIn()
	else
		-- self._wtAnnouncementPanel:Collapsed()
		self._wtAnnouncementPanel:PlayAnimOut()
	end
	return bShow
end

function LobbyTopBar:HideAnnouncementManuel()
	self._wtAnnouncementPanel:PlayAnimOut()
end

function LobbyTopBar:InitDownloadReddotMobile()
    if IsHD() then
        return
    end
    self:AddLuaEvent(Module.LitePackage.Config.evtReddotChanged, self.CheckDownloadReddotMobile, self)
end

function LobbyTopBar:RemoveDownloadReddotMobile()
    if IsHD() then
        return
    end
    self:RemoveLuaEvent(Module.LitePackage.Config.evtReddotChanged)
end

function LobbyTopBar:CheckDownloadReddotMobile()
    if not IsHD() then
        if Module.LitePackage:CheckHasPackageReddot() then
            self._wtDownloadRedDotWidget:SetReddotVisible(true,EReddotType.Normal)
        else
            self._wtDownloadRedDotWidget:SetReddotVisible(false,EReddotType.Normal)
        end
    end
end

return LobbyTopBar