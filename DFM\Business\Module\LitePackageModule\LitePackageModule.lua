----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMLitePackage)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class LitePackageModule : ModuleBase
local LitePackageModule           = class("LitePackageModule", require("DFM.YxFramework.Managers.Module.ModuleBase"))
local LitePackageLogic            = require "DFM.Business.Module.LitePackageModule.Logic.LitePackageLogic"
local LitePackageEventLogic       = require "DFM.Business.Module.LitePackageModule.Logic.LitePackageEventLogic"
local UDFMGameplayGlobalDelegates = import "DFMGameplayGlobalDelegates"
local LiteDownloadManager         = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
local UDFMGameMaple               = import "DFMGameMaple"
local DFGameLaunchManager         = import("DFGameLaunchManager").GetGameLaunchManager(GetGameInstance())
local ELaunchStepResult           = import "ELaunchStepResult"
local UKismetSystemLibrary = import "KismetSystemLibrary"
local EQuitPreference = import "EQuitPreference"
local ULuaExtension = import("LuaExtension")

local UGameVersionUtils = import "GameVersionUtils"
local UZipLibrary            = import("ZipLibrary")
local UGameSDKManager = import "GameSDKManager"
local gameSDKManager = UGameSDKManager.Get(GetGameInstance())
local UDFMGameGPM = import "DFMGameGPM"
local UHashCalculator = import "HashCalculator"

local LiteDownloadMBNum = 1024 * 1024
local ToTipsDownloadSizeMBWithoutWifi = 100
local PerfGearPipeline = import "PerfGearPipeline"
local PerfGearPipelineInst = PerfGearPipeline.Get()

local HD_WIFI_DOWNLOAD_MODEL_LEVEL = 4      --1档机器
local HD_WIFI_DOWNLOAD_MODEL_LEVEL_CN_LITE = 2   --4档机器
local HD_DOWNLOAD_MODEL_LEVEL = 2   -- 高清档位
local HD_DOWNLOAD_NEED_SPACE = 2   -- 登录前高清需要检测磁盘空间
local HD_DOWNLOAD_NEED_SPACE_NEWPLAYER = 7   -- 登录前高清需要检测磁盘空间新玩家
local HD_WIFI_DOWNLOAD_SPACE_GB = 5
local HD_WIFI_DOWNLOAD_TRIGGER_STYLE = 1
local HD_LiteDownloadGBNum = 1024 * 1024 * 1024
local LITE_NEED_CHECK_BEFORE_LOGIN_MODULE_STATA = {"SOLOnly","MPOnly"}

local STR_KEY_NOCOMBINE = "nocombine"
local STR_KEY_NEEDCOMBINE = "needcombine"
local STR_KEY_FINISHEDCOMBINE = "finishedcombine"

local MPModuleName = "MPOnly"
local SOLModuleName = "SOLOnly"

local MPModuleNameHD = "MPHDOnly"
local SOLModuleNameHD = "SOLHDOnly"

local PAK_COMBINE_TIMES = 2

function LitePackageModule:Ctor()
end

---------------------------------------------------------------------------------
--- Module 生命周期
---------------------------------------------------------------------------------
--- 模块Init回调，用于初始化一些数据
---@overload fun(ModuleBase, OnInitModule)
function LitePackageModule:OnInitModule()
    LiteDownloadManager:Init()
    LiteDownloadManager:ResetEvent()
    --LitePackageLogic:InitPackageReddot()
end

--- 若为非懒加载模块，则在Init后调用;对应每个OnGameFlowChangeEnter
--- 模块默认加载资源（预加载UI蓝图、需要用到的图片等等
---@overload fun(ModuleBase, OnLoadModule)
function LitePackageModule:OnLoadModule()
    -- LiteDownloadManager:Init()
    -- loginfo("[LitePackageModule] OnLoadModule puffer check init.")
end

--- 无论是否懒加载都会调用，对应每个OnGameFlowChangeLeave
--- 模块默认卸载资源
---@overload fun(ModuleBase, OnUnloadModule)
function LitePackageModule:OnUnloadModule()
end

--- 注销LuaEvent、Timer监听
---@overload fun(ModuleBase, OnDestroyModule)
function LitePackageModule:OnDestroyModule()
    self:CloseMainPanel()
    self:RemoveAllLuaEvent()
    -- LiteDownloadManager:CancelAll()
    LiteDownloadManager:RemoveEvent()
end

local bReleasePuffer = true
---@overload fun(ModuleBase, OnGameFlowChangeLeave)
function LitePackageModule:OnGameFlowChangeLeave(gameFlowType)
    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- LitePackageEventLogic.RemoveLobbyListeners()
    end
    -- if LitePackageLogic:IsOpenMountMapLazy() and
    --     (gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse) then -- 离开模式大厅和安全屋需要mount地图pak
    --     LiteDownloadManager:MountMapPaks()
    --     loginfo("[LitePackageModule] LiteDownloadManager:MountMapPaks")
    -- end
    if LitePackageLogic:IsOpenMountMapLazy() and
        (gameFlowType == EGameFlowStageType.GameToLobby 
        or gameFlowType == EGameFlowStageType.GameToSafeHouse 
        or gameFlowType == EGameFlowStageType.LobbyToLogin) then
            LiteDownloadManager:UnMountMapPaks()
            loginfo("[LitePackageModule] LiteDownloadManager:UnMountMapPaks")
    end
end

---@overload fun(ModuleBase, OnGameFlowChangeEnter)
function LitePackageModule:OnGameFlowChangeEnter(gameFlowType)
    --loginfo("[LitePackageModule] OnGameFlowChangeEnter gameFlowType: "..gameFlowType)
    if gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse or
        ameFlowType == EGameFlowStageType.Lobby then
        LiteDownloadManager:Init()
        LiteDownloadManager:StartCheckModuleInfosAsync()
        loginfo("[LitePackageModule] OnGameFlowChangeEnter puffer check init.")
    end

    if Facade.GameFlowManager:CheckIsInFrontEnd(gameFlowType) then
        --- LitePackageEventLogic.AddLobbyListeners()
    end

    -- if LitePackageLogic:IsOpenMountMapLazy() and
    --     (gameFlowType == EGameFlowStageType.ModeHall or gameFlowType == EGameFlowStageType.SafeHouse) then -- 进入模式大厅和安全屋需要unmount地图pak
    --     LiteDownloadManager:UnMountMapPaks()
    --     loginfo("[LitePackageModule] LiteDownloadManager:UnMountMapPaks")
    -- end
end

---@overload fun(ModuleBase, OnLoadingGame2Frontend)
function LitePackageModule:OnLoadingGame2Frontend(gameFlowType)
    if DFHD_LUA == 1 then
        return
    end

    if bReleasePuffer then
        LiteDownloadManager:CreateMgrTimer()
        LiteDownloadManager:InitPufferDownloader()
    else
        LiteDownloadManager:CreateMgrTimer()
    end
    loginfo("[LitePackageModule] OnLoadingGame2Frontend puffer CreateMgrTimer and InitPufferDownloader")
end

---@overload fun(ModuleBase, OnLoadingFrontend2Game)
function LitePackageModule:OnLoadingFrontend2Game(gameFlowType)
    if DFHD_LUA == 1 then
        return
    end

    if bReleasePuffer then
        LiteDownloadManager:ClearMgrTimer()
        LiteDownloadManager:ReleasePufferDownloader()
    else
        LiteDownloadManager:ClearMgrTimer()
    end
    loginfo("[LitePackageModule] OnLoadingFrontend2Game puffer ClearMgrTimer and ReleasePufferDownloader")
end

---------------------------------------------------------------------------------
--- Module Public API
---------------------------------------------------------------------------------
function LitePackageModule:ShowMainPanel(...)
    return LitePackageLogic.ShowMainPanelProcess(...)
end

function LitePackageModule:CloseMainPanel()
    return LitePackageLogic.CloseMainPanelProcess()
end

function LitePackageModule:DoSomeThing(...)
    return LitePackageLogic.DoSomeThingProcess(...)
end

function LitePackageModule:StartDLCProcess()
    if DFHD_LUA == 1 then
        self:FinishPufferUpdateStep(true)
        loginfo("[LitePackageModule] DFHD_LUA FinishPufferUpdateStep.")
    else
        self.fakeNowSize = 0

        local versionApp = UGameVersionUtils.GetAppVersion()
        Facade.ConfigManager:SetString("LastFinishedPreDownload", versionApp)

        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleCheckResult,
            self.DownloadManagerNtfModuleCheckResult, self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress,
            self.OnModuleDownloadProgress, self)
        self:AddLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult, self.ModuleDownloadResult,
            self)
        self:AddLuaEvent(Module.LitePackage.Config.evtPopSelectPopClose, self.OnPopSelectPopClose, self)

        local bSupportLitePack = LiteDownloadManager:IsSupportLitePackage()
        local bSimulateLitePack = LiteDownloadManager:IsSimulateLitePack()
        --GM Open litepack and download witheditor
        LiteDownloadManager:GMSwicthEditorLitePackage(bSimulateLitePack)
        LiteDownloadManager:GMEnableEditorDownload(bSimulateLitePack)

        if bSupportLitePack or bSimulateLitePack then
            -- init puffer
            if LiteDownloadManager:IsPufferInitSucceed() == false then
                loginfo("[LitePackageModule] StartDLCProcess bSimulateLitePack IsPufferInitSucceed() == false. try InitPufferDownloader")
                LiteDownloadManager:InitPufferDownloader()
            end

            local bNeedFixPak = LiteDownloadManager:GetLaunchFixPak()
            if bNeedFixPak == true then
                self:UpdateLoadingTips(Module.LitePackage.Config.Loc.BeforeLogin_Check_DLC_AND_RES_NEED_FIX)
            else
                self:UpdateLoadingTips(Module.LitePackage.Config.Loc.BeforeLogin_Check_DLC)
            end

            self:UpdatePercentFloat(0)

            local ins = UDFMGameMaple.GetMapleIns(GetGameInstance())
            if ins then
                if ins:IsMapleInited() then
                    local speed = ins:GetCustomData("PufferMaxSpeed")
                    if speed ~= "" then
                        loginfo("[LitePackageModule] GetCustomData PufferMaxSpeed:" .. speed)
                        local speedNumber = tonumber(speed)
                        LiteDownloadManager:ResetMapleMaxSpeed(speedNumber)
                    end
                end
            end

            LiteDownloadManager:CheckAndSetWholePackageStatesByFileNames()
            self:UpdatePufferConfig()
        else
            loginfo("[LitePackageModule] bSupportLitePack or bSimulateLitePack = false FinishPufferUpdateStep.")
            self:FinishPufferUpdateStep(true)
        end
    end
end

function LitePackageModule:CheckAndSetCombinePakNames()
    -- cord all combined paknames
    if #self.combinedPakNames > 0 then
        loginfo("[LitePackageModule] BeginCombinePaks SetCombinePakNames num:"..tostring(#self.combinedPakNames))
        LiteDownloadManager:SetCombinePakNames(self.combinedPakNames)

        --mark for next login to set for puffer
        LiteDownloadManager:SetNextLoginToSetCombinedPaks(self.combinedPakNames)
    end
end

function LitePackageModule:UpdateBeforeLoginDLC()
    local blauncherSkip = false

    local bMobileFullPackage = LiteDownloadManager:IsMobileFullPackage()
    local bSimulateLitePack = LiteDownloadManager:IsSimulateLitePack()
    local bSimulateLitePackDownloadBeforeLogin = LiteDownloadManager:IsSimulateLitePackDownloadBeforeLogin()
    local skipUpdateStr = UGameVersionUtils.GetLauncherParamsByKey("skipPuffer")
    if skipUpdateStr == "yes" then
        blauncherSkip = true
        loginfo("[LitePackageModule] blauncherSkip.")
    end

    local bNeedFinishUpdate = blauncherSkip or bMobileFullPackage or
        (bSimulateLitePack and bSimulateLitePackDownloadBeforeLogin == false)
    if bNeedFinishUpdate then
        loginfo("[LitePackageModule] UpdateBeforeLoginDLC FinishPufferUpdateStep.")
        self:FinishPufferUpdateStep(true)
    else
        loginfo("[LitePackageModule] UpdateBeforeLoginDLC DoDownloadBeforeLogin.")
        LiteDownloadManager:SetReportServerFlag(true)

        self:DoDownloadBeforeLogin()
        -- if PLATFORM_IOS then
        --     self:FinishPufferUpdateStep(true)
        --     loginfo("[LitePackageModule] UpdateBeforeLoginDLC DoDownloadBeforeLogin. PLATFORM_IOS skip")
        -- else
        --     self:DoDownloadBeforeLogin()
        -- end
    end
end

function LitePackageModule:ShowBeforeLoginDownloadPop(content, confirmTxt, cancelTxt)
    local bisInReview = UGameVersionUtils.IsInReview()

    local confirmHandle = function()
        self:_afterCheckAndNeedDownload()
        loginfo("[LitePackageModule] ShowBeforeLoginDownloadPop _afterCheckAndNeedDownload.")
    end

    local cancelHandle = function()
        loginfo("[LitePackageModule] ShowBeforeLoginDownloadPop QuitGame.")
        if PLATFORM_IOS then
            if bisInReview then
                UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.GMTestCrash")
            end
        else
            UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        end
    end

    local bShowOneButton = false
    if PLATFORM_IOS then
        if bisInReview == false then
            bShowOneButton = true
        end
    end
    Module.GCloudSDK:ShowCommonTip(content, confirmTxt, cancelTxt, bShowOneButton, confirmHandle, cancelHandle)
end

function LitePackageModule:ShowUpdatePanel(...)
    -- Facade.ConfigManager:SetString("LastFinishedPreDownload", "")
    -- self:CheckPreDownload()
    self.nowVersion = UGameVersionUtils.GetAppVersion()
    self.versionCombineKey = string.format("HasBeenFinishedPakCombine_%s", self.nowVersion)
    --Facade.ConfigManager:SetString("LastAppVersionForPuffer", "0.201.10946.0")
    local lastVerion = Facade.ConfigManager:GetString("LastAppVersionForPuffer", "nil")
    if lastVerion == "nil" then
        self.bIsRelease = VersionUtil.IsRelease()
        local bIsShipping = UGameVersionUtils.IsShipping()
        if self.bIsRelease then
            if BUILD_REGION_CN_EXPER then
                self.localVersion = "2.201.10970.0"
            else
                if IsBuildRegionCN() then
                    self.localVersion = "1.201.37101.0"
                elseif IsBuildRegionGlobal() then
                    self.localVersion = "1.202.37101.0"
                else
                    self.localVersion = "1.203.37102.0"
                end
            end
        else
            if IsBuildRegionCN() then
                self.localVersion = "0.201.10145.0"
            elseif IsBuildRegionGlobal() then
                self.localVersion = "0.202.10145.0"
            else
                self.localVersion = "0.203.10145.0"
            end
        end
    else
        self.localVersion = lastVerion
    end

    -- if PLATFORM_IOS then
    --     self.localVersion = ""
    -- end
    if IsInEditor() then
        self.localVersion = "0.201.1032.0"
        self.nowVersion = "0.201.12758.0"
    end

    Facade.ConfigManager:SetString(self.versionCombineKey, STR_KEY_NOCOMBINE)

    LiteDownloadManager:CheckAndSetCombinedPaksToPuffer()
    -- local deviceId = ULuaExtension.GetDeviceID()
    -- loginfo("[LitePackageModule] ShowUpdatePanel deviceId:"..deviceId)

    self:StartDLCProcess()
end

function LitePackageModule:CheckAndFixPaksAllDeep()
    loginfo("[LiteDownloadManager] CheckAndFixPaksAllDeep AsyncCalculateFileMD5.")
    local UFlibPakHelper = import "FlibPakHelper"
    local UHashCalculator = import "HashCalculator"

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)
    local showStr = Module.LitePackage.Config.Loc.LITE_PAK_FIX_DEEP_CHECK_MD5
    self:UpdateLoadingTips(showStr)

    self.deepFixCheckMD5Paks = {}
    if pakFiles ~= nil then
        self.deepFixCheckAllPaksCount = #pakFiles
    end

    for _, needCheckPakFilePath in pairs(pakFiles) do
        if needCheckPakFilePath then
            table.insert(self.deepFixCheckMD5Paks, needCheckPakFilePath)
            UHashCalculator.AsyncCalculateFileMD5(needCheckPakFilePath, slua.createDelegate(CreateCPlusCallBack(self.OnAsyncCalculateFileMD5, self)))
            loginfo("[LiteDownloadManager] CheckAndFixPaksAllDeep AsyncCalculateFileMD5 file:"..needCheckPakFilePath)
        end
    end

    LiteDownloadManager:SetAllPakFixDeep(false)
end

function LitePackageModule:OnAsyncCalculateFileMD5(EHashType, FilePath, OutputFileHash)
    -- local info = string.format("[LitePackageModule] OnAsyncCalculateFileMD5 ,FilePath:%s OutputFileHash:%s", FilePath, OutputFileHash)
    -- loginfo(info)
    if self.deepFixCheckMD5Paks ~= nil then
        table.removebyvalue(self.deepFixCheckMD5Paks, FilePath, true)
    end

    if OutputFileHash ~= nil then
        local pakName = FilePath:match("([^/\\]+)$")
        local liteDownloadManager = require "DFM.Business.Module.LitePackageModule.Logic.LiteDownloadManager"
        local correctlyMD5 = liteDownloadManager:GetPakFileCorrectlyMD5(pakName)
        local md5Info = string.format("[LiteDownloadQuest] OnAsyncCalculateFileMD5 GetPakFileCorrectlyMD5 pakName:%s, md5:%s, correctlyMD5:%s", pakName, OutputFileHash, correctlyMD5)
        loginfo(md5Info)

        if correctlyMD5 ~= nil and correctlyMD5 ~= OutputFileHash then
            --need fix
            if self.FixDeepPakNames == nil then
                self.FixDeepPakNames = {}
            end
            table.insert(self.FixDeepPakNames, pakName)
            liteDownloadManager:DeleteFileByPakName(pakName)
            loginfo("[LiteDownloadQuest] OnAsyncCalculateFileMD5 FixDeepPakNames add pakName:"..pakName)
        end
    end

    if self.deepFixCheckAllPaksCount ~= nil and self.deepFixCheckMD5Paks ~= nil then
        local showStr = Module.LitePackage.Config.Loc.LITE_PAK_FIX_DEEP_CHECK_MD5
        local fixIngCount = #self.deepFixCheckMD5Paks
        showStr = showStr..string.format("[%s/%s]", self.deepFixCheckAllPaksCount - fixIngCount, self.deepFixCheckAllPaksCount)
        self:UpdateLoadingTips(showStr)
    end

    if self.deepFixCheckMD5Paks == nil or #self.deepFixCheckMD5Paks <= 0 then
        LiteDownloadManager:SetLaunchFixPakFinished()
        self:CheckAndFixPaks()
    end
end

function LitePackageModule:CheckAndFixPaks()
    -- local bIsShipping = UGameVersionUtils.IsShipping()
    -- local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

    -- local UFlibPakHelper = import "FlibPakHelper"
    -- local pakFiles = UFlibPakHelper.ScanPakFilesByPath(pufferPath)
    local pakFiles = LiteDownloadManager:GetNeedFixPaks()
    self.needFixPakName = {}
    for _, value in pairs(pakFiles) do
        if value then
            local bFileExist = ULuaExtension.Ext_FileExists(value)
            local pakFileName = string.match(value, "([^/]+)$")
            local correctlyMD5 = LiteDownloadManager:GetPakFileCorrectlyMD5(pakFileName)
            local localMD5 = ULuaExtension.CalcFileMD5(value)
            local checkinfo = string.format("[LitePackageModule] CheckAndFixPaks pakFileName:%s, correctlyMD5:%s, localMD5:%s", pakFileName, correctlyMD5, localMD5)
            loginfo(checkinfo)

            if (correctlyMD5 ~= "" and correctlyMD5 ~= nil and localMD5 ~= "" and localMD5 ~= nil and correctlyMD5 ~= localMD5) or LiteDownloadManager:GetDebugPakFix() then
                table.insert(self.needFixPakName, pakFileName)
                LiteDownloadManager:DeleteFileByPakName(pakFileName)
            end
        end
    end

    LiteDownloadManager:SetNeedFixPakFinished()
    loginfo("[LitePackageModule] CheckAndFixPaks need fix pakname count:"..#self.needFixPakName)

    if self.FixDeepPakNames ~= nil and #self.FixDeepPakNames > 0 then
        for index, pakFileName in ipairs(self.FixDeepPakNames) do
            table.insert(self.needFixPakName, pakFileName)
            self.bDeepFix = true
            loginfo("[LitePackageModule] CheckAndFixPaks need fix add fixdeep pakName:"..pakFileName)
        end
    end

    if #self.needFixPakName > 0 then
        self.pakResFixModuleName = "RuntimePakFixed"
        LiteDownloadManager:RegisterRuntimeMultiModule(self.pakResFixModuleName, self.needFixPakName, true)
        self.nowdownloadModule = self.pakResFixModuleName
        LiteDownloadManager:DownloadForceByModuleName(self.pakResFixModuleName, true)
    else
        self:CheckAndStartPakRecombine()
    end
end

----- pak combine start -----
function LitePackageModule:CheckAndStartPakRecombineAfterPop()
    local bNeedDownload = self:RegisterRuntimeDownloadModule()
    -- LiteDownloadManager:CalcLastVersionPakInfos()
    local versionCombineContent = Facade.ConfigManager:GetString(self.versionCombineKey, STR_KEY_NOCOMBINE)
    local bExistsPakCombineConfigFilePath = false
    if versionCombineContent == STR_KEY_NOCOMBINE then
        local pakCombineConfigFileName = ""
        local localVersion = self.localVersion --Facade.ConfigManager:GetString("LastAppVersionForPuffer", "0.201.10946.0")
        local nowVersion = self.nowVersion -- UGameVersionUtils.GetAppVersion()
        if localVersion ~= "" then
            logerror("[LitePackageModule] UpdatePakRecombineConfig nowVersion:"..tostring(nowVersion))
            if localVersion ~= nowVersion then
                pakCombineConfigFileName = string.format("%s_%s/manifest.txt", localVersion, nowVersion)
                Facade.ConfigManager:SetString(self.versionCombineKey, STR_KEY_NEEDCOMBINE)
                versionCombineContent = STR_KEY_NEEDCOMBINE
            else
                Facade.ConfigManager:SetString(self.versionCombineKey, STR_KEY_FINISHEDCOMBINE)
                versionCombineContent = STR_KEY_FINISHEDCOMBINE
            end
        else
            Facade.ConfigManager:SetString(self.versionCombineKey, STR_KEY_FINISHEDCOMBINE)
            versionCombineContent = STR_KEY_FINISHEDCOMBINE
        end

        if pakCombineConfigFileName ~= "" then
            local bIsShipping = UGameVersionUtils.IsShipping()
            local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
            local pakCombineConfigFilePath = pufferPath.."/"..pakCombineConfigFileName
            bExistsPakCombineConfigFilePath = ULuaExtension.Ext_FileExists(pakCombineConfigFilePath)
        end
    end

    if versionCombineContent == STR_KEY_FINISHEDCOMBINE or versionCombineContent == STR_KEY_NOCOMBINE then
        local combinedPakNames = LiteDownloadManager:GetNextLoginToSetCombinedPaks()
        LiteDownloadManager:SetCombinePakNames(combinedPakNames)
        self:UpdateBeforeLoginDLC()
    else
        if bExistsPakCombineConfigFilePath then
            self:UpdatePakRecombinePaks()
        else
            self:UpdateBeforeLoginDLC()
        end
    end
end

function LitePackageModule:CheckAndStartPakRecombine()
    local popSelectKey = string.format("HASBEEN_POP_TO_SELECT_DOWNLOAD_GAME_MODE_%s", self.nowVersion)
    -- Facade.ConfigManager:SetBoolean(popSelectKey, false)
    local bHasBeenPop = Facade.ConfigManager:GetBoolean(popSelectKey, false)
    if bHasBeenPop == false then
        Facade.ConfigManager:SetBoolean(popSelectKey, true)
        local bNeedShowPop = false

        if (IsBuildRegionGlobal() or IsBuildRegionGA()) then
            bNeedShowPop = true

            -- CBT NEED CHECK XID
            -- local xID = UDFMGameGPM.GetXID()
            -- logerror("[LitePackageModule] CheckAndStartPakRecombine xID:"..tostring(xID))
            -- if xID ~= nil then
            --     bNeedShowPop = self:CheckXIDShowPop(xID)
            -- end
            Facade.ConfigManager:SetString("DOWNLOAD_BEFORELOGIN_GAME_MODE", "MP")
            Facade.ConfigManager:SetString("GAME_MODE_SELECT_RESULT_STYLE", "1,3")
        else
            Facade.ConfigManager:SetString("DOWNLOAD_BEFORELOGIN_GAME_MODE", "SOL")
        end

        logerror("[LitePackageModule] CheckAndStartPakRecombine bNeedShowPop:"..tostring(bNeedShowPop))
        local bisInReview = UGameVersionUtils.IsInReview()
        if bisInReview then
            bNeedShowPop = false
        end

        if bNeedShowPop then
            Facade.ConfigManager:SetString("GAME_MODE_SELECT_RESULT_STYLE", "1,3")
            Facade.UIManager:AsyncShowUI(UIName2ID.LiteSelectDownloadPop, self.OnLiteSelectDownloadPopFinished, self, MPModuleName, SOLModuleName)
        else
            LiteDownloadManager:ReportDownloadSelectEvent(1, 1, 1, "")
            Facade.ConfigManager:SetString("GAME_MODE_SELECT_RESULT_STYLE", "1,1")
            self:CheckAndStartPakRecombineAfterPop()
        end
    else
        self:CheckAndStartPakRecombineAfterPop()
    end
end


function LitePackageModule:CheckXIDShowPop(xID)
    local last_char = xID:sub(-1)
    local allowed_chars = { ["1"] = true,  ["3"] = true, ["5"] = true, ["7"] = true, ["9"] = true, ["b"] = true, ["d"] = true , ["f"] = true}
    last_char = string.lower(last_char)

    if allowed_chars[last_char] then
        return true
    else
        return false
    end
end

function LitePackageModule:OnPopSelectPopClose()
    self:CheckAndStartPakRecombineAfterPop()
end

function LitePackageModule:OnLiteSelectDownloadPopFinished(...)
    loginfo("[LitePackageModule] OnLiteSelectDownloadPopFinished")
end

function LitePackageModule:UpdatePakRecombinePaks()
    loginfo("[LitePackageModule] UpdatePakRecombinePaks.")

    self.dictNowVersionBeforeLoginPaks = LiteDownloadManager:GetPakNamesByModuleName(self.ModuleKey, true)
    if self.dictNowVersionBeforeLoginPaks == nil or #self.dictNowVersionBeforeLoginPaks <= 0 then
        loginfo("self.dictNowVersionBeforeLoginPaks == 0")
        return
    end
    self.beforeLoginExistPaks = {}
    self.CombinePakWithPatchPakPathAsyncQueue = {}

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

    self.dictLastVersionAllPaks = LiteDownloadManager:GetLastVersionAllPaks()
    if self.dictNowVersionBeforeLoginPaks ~= nil and #self.dictNowVersionBeforeLoginPaks > 0 then
        for index, pakName in ipairs(self.dictNowVersionBeforeLoginPaks) do
            -- loginfo("[LitePackageModule] UpdatePakRecombinePaks dictNowVersionBeforeLoginPaks pakname:"..pakName)
            local pakIndex = pakName:match("pakchunk%d+optional?") or pakName:match("pakchunk%d+")
            for indexOldCombine, pakNameOldNeedCombine in pairs(self.dictLastVersionAllPaks) do
                local pakOldCombineIndex = pakNameOldNeedCombine:match("pakchunk%d+optional?") or pakNameOldNeedCombine:match("pakchunk%d+")
                if pakIndex == pakOldCombineIndex then
                    local pakOldPath = pufferPath.."/"..pakNameOldNeedCombine
                    local bExists = ULuaExtension.Ext_FileExists(pakOldPath)
                    if bExists then
                        local counter = LiteDownloadManager:GetPakCombinedCount(pakNameOldNeedCombine)
                        if counter < 2 then
                            table.insert(self.beforeLoginExistPaks, pakNameOldNeedCombine)
                            loginfo("[LitePackageModule] UpdatePakRecombinePaks exists old pakname:"..pakNameOldNeedCombine)
                        else
                            LiteDownloadManager:DeleteFileByPakName(pakOldPath)
                            loginfo("[LitePackageModule] UpdatePakRecombinePaks exists old pakname but counter > 1:"..pakNameOldNeedCombine)
                        end
                    else
                        -- loginfo("[LitePackageModule] UpdatePakRecombinePaks not exists old pakname:"..pakNameOldNeedCombine)
                    end

                    break
                end
            end
        end
    end

    self.needCombinePak = {}
    self.dickCombineMainfestPaks = LiteDownloadManager:GetAllCombineMainfestPaks()
    if self.dickCombineMainfestPaks ~= nil and #self.dickCombineMainfestPaks > 0 then
        for index, pakName in ipairs(self.dickCombineMainfestPaks) do
            -- loginfo("[LitePackageModule] UpdatePakRecombinePaks dickCombineMainfestPaks pakname:"..pakName)
            table.insert(self.needCombinePak, pakName)
        end
    end

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

    self.needCombineToDownlaodPak = {}
    self.downloadToCombineQueue = {}
    self.needCombineToDownlaodPakIndexs = {}
    for index, pakName in pairs(self.beforeLoginExistPaks) do
        local pakIndex = pakName:match("pakchunk%d+optional?") or pakName:match("pakchunk%d+")
        -- loginfo("[LitePackageModule] UpdatePakRecombinePaks pakIndex:"..tostring(pakIndex))
        for indexNeedCombine, pakNameNeedCombine in pairs(self.dickCombineMainfestPaks) do
            local pakNeedCombineIndex = pakNameNeedCombine:match("pakchunk%d+optional?") or pakNameNeedCombine:match("pakchunk%d+")
            if pakIndex == pakNeedCombineIndex then
                local combinePakPath = self.combinePath.."/"..pakNameNeedCombine
                local bHasBeenExistInpuffer = LiteDownloadManager:CheckFileIsInPuffer(combinePakPath)
                loginfo("[LitePackageModule] UpdatePakRecombinePaks need download pakname:"..combinePakPath..", InPuffer:"..tostring(bHasBeenExistInpuffer))
                if bHasBeenExistInpuffer then
                    table.insert(self.needCombineToDownlaodPak, pakNameNeedCombine)
                    table.insert(self.downloadToCombineQueue, pakNameNeedCombine)

                    self.needCombineToDownlaodPakIndexs[pakIndex] = true
                end
                break
            end
        end
    end

    self.combinedPakNames = LiteDownloadManager:GetNextLoginToSetCombinedPaks()
    if self.combinedPakNames == nil then
        self.combinedPakNames = {}
    end

    self:CheckSpaceAndNetState()
end

function LitePackageModule:CheckUpdateSpaceEnough(needUpdataSizeGB)
    return true
    -- local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
    -- logerror("[LitePackageModule] CheckUpdateSpaceEnough freeSpace:"..tostring(freeSpace))
    -- logerror("[LitePackageModule] CheckUpdateSpaceEnough needUpdataSizeGB:"..tostring(needUpdataSizeGB))
    -- if freeSpace <= 0 then
    --     return true
    -- end
    -- local LiteDownloadGBNum = 1024 * 1024 * 1024
    -- local spaceGB = freeSpace / LiteDownloadGBNum

    -- if spaceGB - needUpdataSizeGB < 3 then
    --     return false
    -- end

    -- return true
end

function LitePackageModule:CheckSpaceAndNetState()
    local netState = LiteDownloadManager:GetNetworkState()
    local bToTips = false
    local beginTotalSize = self:GetCurrentDownloadBeginTotalSize()
    local beginNowSize = self:GetCurrentDownloadedNowSize()
    if beginNowSize >= beginTotalSize then
        beginNowSize = beginTotalSize - 1000
    end

    local needUpdataSize = (beginTotalSize - beginNowSize) / LiteDownloadMBNum
    local showStr = ""
    local strRetry = Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_Confirm
    local strQuitgame = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_QuitGame

    local bSpaceEnough = self:CheckUpdateSpaceEnough(0)
    if bSpaceEnough == false then
        self.spaceGBNotEough = true
        bToTips = true
        showStr = Module.LitePackage.Config.Loc.LITE_DOWNLOAD_CHECK_SPACE_NOT_ENOUGH
        strRetry = Module.LitePackage.Config.Loc.LITE_SELECT_POP_OK
    else
        self.spaceGBNotEough = false
    end

    if netState == 2 or bToTips == true then
        if self.spaceGBNotEough ~= true and needUpdataSize > ToTipsDownloadSizeMBWithoutWifi then
            bToTips = true
            showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_Not_Wifi,
                tostring(needUpdataSize))
        end

        if bToTips then
            local bisInReview = UGameVersionUtils.IsInReview()
            local confirmHandle = function()
                if self.spaceGBNotEough ~= true then
                    self.hasBeenPopNetTips = true
                    if #self.downloadToCombineQueue <= 0 then
                        --没有合并任务需要下载，开始登录前更新
                        self:UpdateBeforeLoginDLC()
                    else
                        self:DownloadAndCombinePak()
                    end
                    loginfo("[LitePackageModule] CheckSpaceAndNetState beigin DownloadAndCombinePak.")
                else
                    if PLATFORM_IOS then
                        if bisInReview then
                            UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.GMTestCrash")
                        end
                    else
                        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
                    end
                    loginfo("[LitePackageModule] CheckSpaceAndNetState space not enough.")
                end
            end

            local cancelHandle = function()
                loginfo("[LitePackageModule] CheckSpaceAndNetState QuitGame.")
                if PLATFORM_IOS then
                    if bisInReview then
                        UKismetSystemLibrary.ExecuteConsoleCommand(GetGameInstance(), "GPAudio.GMTestCrash")
                    end
                else
                    UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
                end
            end

            local bShowOneButton = false
            if PLATFORM_IOS then
                if bisInReview == false then
                    bShowOneButton = true
                end
            else
                if self.spaceGBNotEough == true then
                    bShowOneButton = true
                end
            end
            Module.GCloudSDK:ShowCommonTip(showStr, strRetry, strQuitgame, bShowOneButton, confirmHandle, cancelHandle, nil, false)
        else
            if #self.downloadToCombineQueue <= 0 then
                --没有合并任务需要下载，开始登录前更新
                self:UpdateBeforeLoginDLC()
            else
                self:DownloadAndCombinePak()
            end
        end
    else
        if #self.downloadToCombineQueue <= 0 then
            --没有合并任务需要下载，开始登录前更新
            self:UpdateBeforeLoginDLC()
        else
            self:DownloadAndCombinePak()
        end
    end
end

function LitePackageModule:DownloadAndCombinePak()
    self.bNeedUpdateBeforeLoginDLC = true

    if self.nowDonwloadCombinePakPath ~= nil then
        self:CombinePakWithPatchPakPathAsync(self.nowDonwloadCombinePakPath)
        self.nowDonwloadCombinePakPath = nil
    end

    if self.downloadToCombineQueue ~= nil and #self.downloadToCombineQueue > 0 then
        local nowPakname = self.downloadToCombineQueue[1]
        local toDownloadPakPath = self.combinePath.."/"..nowPakname

        local registerPakNames = {toDownloadPakPath}
        self.UpdateCombinePakKey = string.format("RuntimeUpdateCombinePakKey%s", tostring(#self.downloadToCombineQueue)) --"RuntimeUpdateCombinePakKey" --
        LiteDownloadManager:RegisterRuntimeMultiModule(self.UpdateCombinePakKey, registerPakNames, true)

        loginfo("[LitePackageModule] DownloadAndCombinePak DownloadByModuleName:".. nowPakname)
        self.nowdownloadModule = self.UpdateCombinePakKey
        LiteDownloadManager:DownloadByModuleName(self.UpdateCombinePakKey, false)

        self.bNeedUpdateBeforeLoginDLC = false
        self.nowDonwloadCombinePakPath = nowPakname
        table.remove(self.downloadToCombineQueue, 1)
    end

    if self.nowCombineOutPak == nil and self.nowDonwloadCombinePakPath == nil then
        --没有合并任务和下载任务，开始登录前更新
        self:UpdateBeforeLoginDLC()
    end
    -- if self.bNeedUpdateBeforeLoginDLC then
    --     -- cord all combined paknames
    --     if #self.combinedPakNames > 0 then
    --         loginfo("[LitePackageModule] BeginCombinePaks SetCombinePakNames num:"..tostring(#self.combinedPakNames))
    --         LiteDownloadManager:SetCombinePakNames(self.combinedPakNames)

    --         --mark for next login to set for puffer
    --         LiteDownloadManager:SetNextLoginToSetCombinedPaks(self.combinedPakNames)
    --     end

    --     self:UpdateBeforeLoginDLC()
    -- end
end

function LitePackageModule:CombinePakWithPatchPakPathAsync(pakNameNeedCombine)
    loginfo("[LitePackageModule] CombinePakWithPatchPakPathAsync:".. pakNameNeedCombine)
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakNeedCombineIndex = pakNameNeedCombine:match("pakchunk%d+optional?") or pakNameNeedCombine:match("pakchunk%d+")

    for index, pakName in pairs(self.beforeLoginExistPaks) do
        local pakIndex = pakName:match("pakchunk%d+optional?") or pakName:match("pakchunk%d+")
        if pakIndex == pakNeedCombineIndex then
            -- loginfo("[LitePackageModule] CombinePakWithPatchPakPathAsync pakIndex:"..tostring(pakIndex))
            local versionPakPath = pufferPath.."/"..pakName
            local combinePakPath = pufferPath.."/"..self.combinePath.."/"..pakNameNeedCombine
            local bExists = ULuaExtension.Ext_FileExists(versionPakPath)
            local bExistsCombine = ULuaExtension.Ext_FileExists(combinePakPath)

            loginfo("[LitePackageModule] CombinePakWithPatchPakPathAsync bExists:"..tostring(bExists))
            loginfo("[LitePackageModule] CombinePakWithPatchPakPathAsync bExistsCombine:"..tostring(bExistsCombine))

            if bExists == true and bExistsCombine == true then
                -- loginfo("[LitePackageModule] CombinePakWithPatchPakPathAsync bExists == true and bExistsCombine == true")

                local allVersionPaks = LiteDownloadManager:GetAllPakNames()
                local outPakPath = ""
                local outPakName = ""
                if allVersionPaks ~= nil and #allVersionPaks > 0 then
                    for k, nowVersionPakname in pairs(allVersionPaks) do
                        local nowVersionPakIndex = nowVersionPakname:match("pakchunk%d+optional?") or nowVersionPakname:match("pakchunk%d+")
                        if pakIndex == nowVersionPakIndex then
                            outPakPath = pufferPath.."/"..nowVersionPakname
                            outPakName = nowVersionPakname
                        end
                    end
                end

                loginfo("[LitePackageModule] CombinePakWithPatchPakPathAsync outPakPath:"..outPakPath)
                local combineInfo = {}
                table.insert(combineInfo, versionPakPath)
                table.insert(combineInfo, combinePakPath)
                table.insert(combineInfo, outPakPath)
                table.insert(combineInfo, pakName)
                table.insert(combineInfo, pakNameNeedCombine)
                table.insert(combineInfo, outPakName)
                table.insert(self.CombinePakWithPatchPakPathAsyncQueue, combineInfo)
            end

            break
        end
    end

    self:CheckCombinePakWithPatchPakQuest()
end

function LitePackageModule:CheckCombinePakWithPatchPakQuest()
    local bHasQuestDownloading = self.nowDonwloadCombinePakPath ~= nil
    if self.nowCombineOutPak == nil then
        if #self.CombinePakWithPatchPakPathAsyncQueue > 0 then
            local infos = self.CombinePakWithPatchPakPathAsyncQueue[1]
            if infos ~= nil and #infos >= 6 then
                -- local str = string.format("[LitePackageModule] CheckCombinePakWithPatchPakQuest info --> infos[1]:%s, infos[2]:%s, infos[3]:%s", infos[1], infos[2], infos[3])
                -- logerror(str)
                table.remove(self.CombinePakWithPatchPakPathAsyncQueue, 1)
                -- local pakWorker = LiteDownloadManager:MergePaksAsync(infos[1], infos[2], infos[3], true)

                local inMd5 = LiteDownloadManager:GetLastVersionPakCorrectlyMD5(infos[4])
                local patchMd5 = LiteDownloadManager:GetPathPakCorrectlyMD5(infos[5])
                local outMd5 = LiteDownloadManager:GetPakFileCorrectlyMD5(infos[6])
                local bHasNil =false
                if inMd5 == nil or patchMd5 == nil or outMd5 == nil then
                    bHasNil = true
                    if inMd5 == nil then
                        inMd5 = ""
                    end
                    if patchMd5 == nil then
                        patchMd5 = ""
                    end
                    if outMd5 == nil then
                        outMd5 = ""
                    end
                end

                LiteDownloadManager:SetPakCombinedCount(infos[4])
                local pakWorker = LiteDownloadManager:MergePaksAsyncChecked(infos[1], inMd5, infos[2], patchMd5, infos[3], outMd5)
                self.nowCombineOutPak = infos[6]

                -- bUPakWorker.FMergePaksCompleteDynamic:Add(OnCombineProgress)
                if pakWorker ~= nil then
                    self.nowMergeBeginTime = os.time()
                    logerror("[LitePackageModule] CheckCombinePakWithPatchPakQuest: nowCombineOutPak: "..self.nowCombineOutPak)
                    pakWorker.OnMergeComplete:Add(self.OnCombineResult, self)
                elseif bHasQuestDownloading == false then
                    logerror("[LitePackageModule] CheckCombinePakWithPatchPakQuest: pakWorker is nil!!")
                    -- self:UpdateBeforeLoginDLC()
                end
            elseif bHasQuestDownloading == false then
                logerror("[LitePackageModule] CheckCombinePakWithPatchPakQuest: infos error!!")
                self:UpdateBeforeLoginDLC()
            end
        elseif bHasQuestDownloading == false then
            logerror("[LitePackageModule] CheckCombinePakWithPatchPakQuest finished!")
            self:UpdateBeforeLoginDLC()
        end
    end
end

function LitePackageModule:OnCombineResult(ErrCode, SrcPakFile, PatchPakFile, OutputPakFile, OutputPakFileMD5)
    local str = string.format("[LitePackageModule] CheckCombinePakWithPatchPakQuest OnCombineResult:ErrCode:%s, OutputPakFile:%s, OutputPakFileMD5:%s", tostring(ErrCode), OutputPakFile, OutputPakFileMD5)
    loginfo(str)
    if ErrCode == 0 then
        local correctlyMD5 = LiteDownloadManager:GetPakFileCorrectlyMD5(self.nowCombineOutPak)
        if correctlyMD5 == OutputPakFileMD5 then
            table.insert(self.combinedPakNames, self.nowCombineOutPak)
            self:CheckAndSetCombinePakNames()
        end

        local md5Check = string.format("[LitePackageModule] CheckCombinePakWithPatchPakQuest correctlyMD5:%s, OutputPakFileMD5:%s",correctlyMD5, OutputPakFileMD5)
        loginfo(md5Check)
    end

    local srcPakName = string.match(SrcPakFile, "([^/]+)$")
    local patchPakName = string.match(PatchPakFile, "([^/]+)$")
    LiteDownloadManager:DeleteFileByPakName(srcPakName)
    local patchPakNameFinal = self.combinePath.."/"..patchPakName
    LiteDownloadManager:DeleteFileByPakName(patchPakNameFinal)

    local beiginTiemStr = ""
    local endTiemStr = ""

    local mergeEndTime = os.time() 
    if self.nowMergeBeginTime ~= nil then
        beiginTiemStr = tostring(self.nowMergeBeginTime)
    end
    if mergeEndTime ~= nil then
        endTiemStr = tostring(mergeEndTime)
    end

    LiteDownloadManager:ReportPakMergeEvent(0, ErrCode, self.nowCombineOutPak, OutputPakFileMD5, beiginTiemStr, endTiemStr,"")
    self.nowCombineOutPak = nil
    self:CheckCombinePakWithPatchPakQuest()
end

function LitePackageModule:CombinePakWithPatchPakPath(pakNameNeedCombine)
    loginfo("[LitePackageModule] CombinePakWithPatchPakPath:".. pakNameNeedCombine)
    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")
    local pakNeedCombineIndex = pakNameNeedCombine:match("pakchunk%d+optional?") or pakNameNeedCombine:match("pakchunk%d+")

    for index, pakName in pairs(self.beforeLoginExistPaks) do
        local pakIndex = pakName:match("pakchunk%d+optional?") or pakName:match("pakchunk%d+")
        if pakIndex == pakNeedCombineIndex then
            loginfo("[LitePackageModule] CombinePakWithPatchPakPath pakIndex:"..tostring(pakIndex))
            local versionPakPath = pufferPath.."/"..pakName
            local combinePakPath = pufferPath.."/"..self.combinePath.."/"..pakNameNeedCombine
            local bExists = ULuaExtension.Ext_FileExists(versionPakPath)
            local bExistsCombine = ULuaExtension.Ext_FileExists(combinePakPath)

            loginfo("[LitePackageModule] CombinePakWithPatchPakPath bExists:"..tostring(bExists))
            loginfo("[LitePackageModule] CombinePakWithPatchPakPath bExistsCombine:"..tostring(bExistsCombine))

            if bExists == true and bExistsCombine == true then
                loginfo("[LitePackageModule] CombinePakWithPatchPakPath bExists == true and bExistsCombine == true")

                local allVersionPaks = LiteDownloadManager:GetAllPakNames()
                local outPakPath = ""
                local outPakName = ""
                if allVersionPaks ~= nil and #allVersionPaks > 0 then
                    for k, nowVersionPakname in pairs(allVersionPaks) do
                        local nowVersionPakIndex = nowVersionPakname:match("pakchunk%d+optional?") or nowVersionPakname:match("pakchunk%d+")
                        if pakIndex == nowVersionPakIndex then
                            outPakPath = pufferPath.."/"..nowVersionPakname
                            outPakName = nowVersionPakname
                        end
                    end
                end

                loginfo("[LitePackageModule] CombinePakWithPatchPakPath outPakPath:"..outPakPath)
                local combineRet = LiteDownloadManager:MergePaks(versionPakPath, combinePakPath, outPakPath, true)
                loginfo("[LitePackageModule] CombinePakWithPatchPakPath ret:"..tostring(combineRet))
                if combineRet == 0 then
                    table.insert(self.combinedPakNames, outPakName)
                else
                    -- report errorcode
                    -- loginfo("[LitePackageModule] BeginCombinePaks MergePaks failed errorcode:"..tostring(combineRet))
                end
            end

            break
        end
    end
end

----- pak recombine end -----


--checkpredownload  start ---
local timerHandler  = nil
local bUsePreDownload = true

local timerHandlerMoveFile = nil
local moveFileTimeStamp = 0
function LitePackageModule:CheckPreDownload()
    local bHasPreDownload = false
    if PLATFORM_ANDROID and bUsePreDownload then
        local versionApp = UGameVersionUtils.GetAppVersion()
        local lastFinishedPreDownload = Facade.ConfigManager:GetString("LastFinishedPreDownload", "")
        if versionApp ~= lastFinishedPreDownload then
            local versionRes = UGameVersionUtils.GetResVersion()
            local versionStr = string.format("{'1':'%s', '2':'%s'}", versionApp, versionApp)
            local ret = LiteDownloadManager:LiteGetDataFromTGPA("GetPredownPath2", versionStr)
            loginfo("[LitePackageModule] CheckPreDownload LiteGetDataFromTGPA:"..ret)
            local numberRet = tonumber(ret)
            if numberRet == nil then
                -- maybe file path
                local isPath = string.match(ret, "[\\/]+") ~= nil
                if isPath then
                    -- now unzip file to puffer
                    local localPath = ret..string.format("/df_predownload_%s.zip", versionApp)
                    local bZipExists = ULuaExtension.Ext_FileExists(localPath)
                    if bZipExists ~= true then
                        localPath = ret..string.format("/df_predownload_%s_1.zip", versionApp)
                        bZipExists = ULuaExtension.Ext_FileExists(localPath)
                    end

                    loginfo("[LitePackageModule] CheckPreDownload LiteGetDataFromTGPA is a path, zippath:"..localPath)

                    if bZipExists == true then
                        bHasPreDownload = true
                        local bIsShipping = UGameVersionUtils.IsShipping()
                        local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

                        timerHandler = Timer:NewIns(0.1, 0)
                        timerHandler:AddListener(self.OnCheckPreDownloadProgress, self)
                        timerHandler:Start()
                        self:UpdatePercentFloat(0)
                        self.unzipPredownload = 1
                        UZipLibrary.UnzipArchiveSimple(localPath, pufferPath)
                    else
                        bHasPreDownload = false
                    end
                end
            elseif numberRet == 1 then
                loginfo("[LitePackageModule] CheckPreDownload now moving file to predownload")
                -- now moving file to predownload
                bHasPreDownload = true
                self:UpdateLoadingTips(Module.LitePackage.Config.Loc.BeforeLogin_Check_PreDownload)
                LiteDownloadManager:RegisterTgpaCallBack()
                moveFileTimeStamp = os.time()

                timerHandlerMoveFile = Timer:NewIns(0.1, 0)
                timerHandlerMoveFile:AddListener(self.OnCheckMoveFileProgress, self)
                timerHandlerMoveFile:Start()
                self:UpdatePercentFloat(0)
            else
                self:SetPreDownloadFinished()
            end
        end
    end

    if bHasPreDownload == false then
        self:StartDLCProcess()
    end
end

function LitePackageModule:OnCheckMoveFileProgress()
    local state = LiteDownloadManager:GetPreDownloadState()
    loginfo("[LitePackageModule] OnCheckMoveFileProgress, state:"..state)
    local progress = LiteDownloadManager:GetPreDownloadStateProgress()
    loginfo("[LitePackageModule] OnCheckMoveFileProgress, progress:"..progress)

    local nowtime = os.time()
    local waitinfo = "."
    if nowtime ~= nil then
        local counter = nowtime % 4
        for i = 1, counter do
            waitinfo = waitinfo .. "."
        end
    end
    local showStr = Module.LitePackage.Config.Loc.BeforeLogin_Check_PreDownload .. waitinfo
    self:UpdateLoadingTips(showStr)

    local diffSeconds = nil
    if nowtime ~= nil and moveFileTimeStamp ~= nil and moveFileTimeStamp > 0 and nowtime > 0 then
        diffSeconds = os.difftime(nowtime, moveFileTimeStamp)
    end

    if diffSeconds == nil then
        --时间获取异常
        if timerHandlerMoveFile then
            timerHandlerMoveFile:Release()
            timerHandlerMoveFile = nil
        end

        self:ReportPreDownloadInfo(1, 0, 1100)
        LiteDownloadManager:UnRegisterTgpaCallBack()
        self:StartDLCProcess()
    else
        if diffSeconds > 120 or (state >= 3 and progress >= 100) then
            local versionApp = UGameVersionUtils.GetAppVersion()
            local predownload_path = "/storage/emulated/0/Android/data/com.tencent.tmgp.dfm/files/TGPA/PreDownload"
            local localPath = predownload_path..string.format("/df_predownload_%s.zip", versionApp)
            local bZipExists = ULuaExtension.Ext_FileExists(localPath)

            local zip1Path = predownload_path..string.format("/df_predownload_%s_1.zip", versionApp)
            local bZip1Exists = ULuaExtension.Ext_FileExists(zip1Path)

            local zip2Path = predownload_path..string.format("/df_predownload_%s_2.zip", versionApp)
            local bZip2Exists = ULuaExtension.Ext_FileExists(zip2Path)

            local bCouldBeginUnzip = false
            if bZipExists then
                loginfo("[LitePackageModule] OnCheckMoveFileProgress bZipExists")
                bCouldBeginUnzip = true
            end

            if bZip1Exists == true and bZip2Exists == true then
                loginfo("[LitePackageModule] OnCheckMoveFileProgress bZip1Exists == true and bZip2Exists == true")
                bCouldBeginUnzip = true
            end

            if diffSeconds > 120 then
                loginfo("[LitePackageModule] OnCheckMoveFileProgress diffSeconds > 20")
                bCouldBeginUnzip = true
            end

            if bCouldBeginUnzip then
                if timerHandlerMoveFile then
                    timerHandlerMoveFile:Release()
                    timerHandlerMoveFile = nil
                end

                LiteDownloadManager:UnRegisterTgpaCallBack()

                local unzipPath = ""
                if bZipExists == true then
                    unzipPath = localPath
                elseif bZip1Exists == true then
                    unzipPath = zip1Path
                end

                loginfo("[LitePackageModule] OnCheckMoveFileProgress begin unzip, zippath:"..unzipPath)

                if unzipPath ~= "" then
                    local bIsShipping = UGameVersionUtils.IsShipping()
                    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

                    timerHandler = Timer:NewIns(0.1, 0)
                    timerHandler:AddListener(self.OnCheckPreDownloadProgress, self)
                    timerHandler:Start()
                    self:UpdatePercentFloat(0)
                    self.unzipPredownload = 1
                    UZipLibrary.UnzipArchiveSimple(unzipPath, pufferPath)
                else
                    self:ReportPreDownloadInfo(1, 0, 2000)
                    self:SetPreDownloadFinished()
                    self:StartDLCProcess()
                end
            end

        end
    end
end


function LitePackageModule:OnCheckPreDownloadProgress()
    local ret = UZipLibrary.GetUnZipCompletedResult()
    loginfo("[LitePackageModule] OnCheckPreDownloadProgress, ret:"..ret)
    if ret == 1 then
        if timerHandler then
            timerHandler:Release()
            timerHandler = nil
        end

        --- check predownload2.zip
        local bZip2Exists = false
        local zip2Path = ""
        if self.unzipPredownload == 1 then
            local versionApp = UGameVersionUtils.GetAppVersion()
            local predownload_path = "/storage/emulated/0/Android/data/com.tencent.tmgp.dfm/files/TGPA/PreDownload"
            zip2Path = predownload_path..string.format("/df_predownload_%s_2.zip", versionApp)
            bZip2Exists = ULuaExtension.Ext_FileExists(zip2Path)
        end

        if bZip2Exists == true then
            self:UnZipPreDownloadZip2(zip2Path)
        else
            self:ReportPreDownloadInfo(1, 1, 0)
            self:SetPreDownloadFinished()
            LiteDownloadManager:CheckAndSetPreDownload()
            self:StartDLCProcess()
            self:CheckAndDeletePreDownloadZip()
        end
    elseif ret == -1 then
        if timerHandler then
            timerHandler:Release()
            timerHandler = nil
        end
        self:ReportPreDownloadInfo(1, 0, 1000)
        self:SetPreDownloadFinished()
        self:StartDLCProcess()
        self:CheckAndDeletePreDownloadZip()
    else
        local waitinfo = "."
        local nowtime = os.time()
        if nowtime ~= nil then
            local counter = nowtime % 4
            for i = 1, counter do
                waitinfo = waitinfo .. "."
            end
        end

        local showStr = Module.LitePackage.Config.Loc.BeforeLogin_Unzip_PreDownload .. waitinfo
        self:UpdateLoadingTips(showStr)
        logwarning("[LitePackageModule] OnCheckPreDownloadProgress >> waiting for UnZip...")
    end
end


function LitePackageModule:UnZipPreDownloadZip2(localPath)
    loginfo("[LitePackageModule] UnZipPreDownloadZip2 begin unzip 2, zippath:"..localPath)

    local bIsShipping = UGameVersionUtils.IsShipping()
    local pufferPath = gameSDKManager:GetSavedPath(bIsShipping, "Puffer")

    timerHandler = Timer:NewIns(0.1, 0)
    timerHandler:AddListener(self.OnCheckPreDownloadProgress, self)
    timerHandler:Start()
    self:UpdatePercentFloat(0)
    self.unzipPredownload = 2
    UZipLibrary.UnzipArchiveSimple(localPath, pufferPath)
end

--- findPatchFile 1:找到预下载文件 0:未找到
--- openPatch 1:文件可用 0:文件不可用
function LitePackageModule:ReportPreDownloadInfo(findPatchFile, openPatch, errorCode)
    local versionApp = UGameVersionUtils.GetAppVersion()
    local patchFile = string.format("/df_predownload_%s.zip", versionApp)
    local predownload_path = "/storage/emulated/0/Android/data/com.tencent.tmgp.dfm/files/TGPA/PreDownload"
    local zipMD5 = ""

    local localPath = predownload_path..string.format("/df_predownload_%s.zip", versionApp)
    local bZipExists = ULuaExtension.Ext_FileExists(localPath)
    if bZipExists ~= true then
        localPath = predownload_path..string.format("/df_predownload_%s_1.zip", versionApp)
        bZipExists = ULuaExtension.Ext_FileExists(localPath)
        patchFile = string.format("/df_predownload_%s_1.zip", versionApp)
    end

    if bZipExists then
        zipMD5 = ULuaExtension.CalcFileMD5(localPath)
    end

    local zip2MD5 = ""
    local zip2Path = predownload_path..string.format("/df_predownload_%s_2.zip", versionApp)
    local bZip2Exists = ULuaExtension.Ext_FileExists(zip2Path)
    if bZip2Exists then
        zip2MD5 = ULuaExtension.CalcFileMD5(zip2Path)
    end

    loginfo("[LitePackageModule] ReportPreDownloadInfo calc predownload zipMD5:"..zipMD5)

    LiteDownloadManager:ReportPreDownloadEvent(1, errorCode, 99, 1, findPatchFile, openPatch, patchFile, zipMD5, 0, 1, 1, predownload_path)
    LiteDownloadManager:ReportPreDownloadEventTGPA(1, errorCode, 99, 1, findPatchFile, openPatch, patchFile, zipMD5, 0, 1, 1, predownload_path)

    if zip2MD5 ~= "" then
        LiteDownloadManager:ReportPreDownloadEvent(1, errorCode, 99, 1, findPatchFile, openPatch, zip2Path, zip2MD5, 0, 1, 1, predownload_path)
        LiteDownloadManager:ReportPreDownloadEventTGPA(1, errorCode, 99, 1, findPatchFile, openPatch, zip2Path, zip2MD5, 0, 1, 1, predownload_path)

        loginfo("[LitePackageModule] ReportPreDownloadInfo calc predownload zip2MD5:"..zip2MD5)
    end

    logwarning("[LitePackageModule] ReportPreDownloadInfo, errorCode:"..errorCode)
end

function LitePackageModule:CheckAndDeletePreDownloadZip()
    local versionApp = UGameVersionUtils.GetAppVersion()
    local predownload_path = "/storage/emulated/0/Android/data/com.tencent.tmgp.dfm/files/TGPA/PreDownload"

    local localPath = predownload_path..string.format("/df_predownload_%s.zip", versionApp)
    local bZipExists = ULuaExtension.Ext_FileExists(localPath)
    if bZipExists then
        -- os.remove(localPath)
        logwarning("[LitePackageModule] DeletePreDownloadZip, localPath:"..localPath)
    end

    local localPath1 = predownload_path..string.format("/df_predownload_%s_1.zip", versionApp)
    local bZip1Exists = ULuaExtension.Ext_FileExists(localPath1)
    if bZip1Exists then
        -- os.remove(localPath1)
        logwarning("[LitePackageModule] DeletePreDownloadZip, localPath1:"..localPath1)
    end

    local localPath2 = predownload_path..string.format("/df_predownload_%s_2.zip", versionApp)
    local bZip2Exists = ULuaExtension.Ext_FileExists(localPath2)
    if bZip2Exists then
        -- os.remove(localPath2)
        logwarning("[LitePackageModule] DeletePreDownloadZip, localPath2:"..localPath2)
    end

    logwarning("[LitePackageModule] DeletePreDownloadZip, finish")
end

function LitePackageModule:SetPreDownloadFinished()
    local versionApp = UGameVersionUtils.GetAppVersion()
    Facade.ConfigManager:SetString("LastFinishedPreDownload", versionApp)
    logwarning("[LitePackageModule] SetPreDownloadFinished")
end

--checkpredownload  end ---

function LitePackageModule:OnStartMatching()
    -- LiteDownloadManager:MountMapPaks()
    loginfo("[LitePackageModule] OnStartMatching MountMapPaks.")
end

function LitePackageModule:UpdatePufferConfig()
    local versionCombineContent = Facade.ConfigManager:GetString(self.versionCombineKey, STR_KEY_NOCOMBINE)
    local pakCombineConfigFileName = ""
    local pakCombineLocalVersionFileName = ""
    if versionCombineContent ~= STR_KEY_FINISHEDCOMBINE then
        local localVersion = self.localVersion --Facade.ConfigManager:GetString("LastAppVersionForPuffer", "0.201.10946.0")
        logerror("[LitePackageModule] UpdatePufferConfig localVersion:"..tostring(localVersion))
        if localVersion ~= "" then
            local nowVersion = self.nowVersion -- UGameVersionUtils.GetAppVersion()
            logerror("[LitePackageModule] UpdatePufferConfig nowVersion:"..tostring(nowVersion))
            if localVersion ~= nowVersion then
                self.combinePath = string.format("%s_%s", localVersion, nowVersion)
                LiteDownloadManager:SetCombineInfo(self.combinePath)
                pakCombineConfigFileName = string.format("%s_%s/manifest.txt", localVersion, nowVersion)
                pakCombineLocalVersionFileName = string.format("%s_%s/puffer_file_info.df", localVersion, nowVersion)
            end
        end
    end

    self.puffer_config_key = "RuntimeLitePackModuleConfig"
    local ini_configs = { "LiteModuleInfo.ini", "LiteMultiModuleInfo.ini", "puffer_file_info.df" }
    if pakCombineConfigFileName ~= "" then
        table.insert(ini_configs, pakCombineConfigFileName)
        table.insert(ini_configs, pakCombineLocalVersionFileName)
    end

    LiteDownloadManager:RegisterRuntimeMultiModule(self.puffer_config_key, ini_configs, true)
    self.nowdownloadModule = self.puffer_config_key
    LiteDownloadManager:DownloadForceByModuleName(self.puffer_config_key, true)
    loginfo("[LitePackageModule] UpdatePufferConfig.")
end

function LitePackageModule:RegisterRuntimeDownloadModule()
    local childModule = {}
    self.ModuleKey = "RuntimeBeforeLogin"
    local bNeedDownload = false
    self.nowdownloadModule = self.ModuleKey
    self.haeBeenCalcTotalSize = nil

    --check dlc in IsSupportLitePackage or not
    if LiteDownloadManager:IsSupportLitePackage() then
        if not LiteDownloadDataTable then
            LiteDownloadDataTable = Facade.TableManager:GetTable("LitePackageDownload")
        end

        if LiteDownloadDataTable then
            for k, v in pairs(LiteDownloadDataTable) do
                if v.TriggerStyle == 1 then
                    table.insert(childModule, v.ModuleKey)
                    bNeedDownload = true
                end
            end
        end

        -- if UGameVersionUtils.IsShipping() == false then
        --     --add test map
        --     table.insert(childModule, "TestMap")
        -- end

        local bisInReview = UGameVersionUtils.IsInReview()

        local APMLevel = 0
        UDFMGameGPM.Get(GetWorld())
        if PLATFORM_IOS then
            APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        elseif PLATFORM_ANDROID then
            APMLevel = UDFMGameGPM.GetDeviceLevelByQcc("level6")
        else
            APMLevel = 4
        end

        -- if IsBuildRegionGA() then
        --     --global & deviceLevel >= 4 & space > 5G download hd res
        --     local netState = LiteDownloadManager:GetNetworkState()
        --     if netState == HD_WIFI_DOWNLOAD_TRIGGER_STYLE then
        --         local deviceLevel = APMLevel
        --         loginfo("[LitePackageModule] DoDownloadBeforeLogin IsBuildRegionGA deviceLevel:"..tostring(deviceLevel))
        --         local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
        --         local spaceGB = freeSpace / HD_LiteDownloadGBNum
        --         if spaceGB >= HD_WIFI_DOWNLOAD_SPACE_GB and deviceLevel >= HD_WIFI_DOWNLOAD_MODEL_LEVEL then
        --             table.insert(childModule, "MapIceLandOnly")
        --             table.insert(childModule, "MapShaftedOnly")
        --             loginfo("[LitePackageModule] DoDownloadBeforeLogin GA add MapIceLandOnly and MapShaftedOnly")
        --         end
        --     end
        -- end

        -- cn & deviceLevel == 5 & space > 5G download hdres
        local deviceLevel = APMLevel
        local bHDHasBeenDownloaded = LiteDownloadManager:IsHDDownloadStatus()

        local spaceGB = 0
        local bHasBeenWriteLoginHDFlag = Facade.ConfigManager:GetBoolean("LITE_HAS_BEEN_WRITE_HD_FLAG", false)
        local bHasBeenDeleteHD = Facade.ConfigManager:GetBoolean("LITE_HAS_BEEN_DELETE_HD", false)
        local freeSpace = LiteDownloadManager:GetDeviceFreeSpace()
        if freeSpace ~= nil and freeSpace > 0 then
            spaceGB = freeSpace / HD_LiteDownloadGBNum
        end

        local bNeedHD = false
        if bHasBeenWriteLoginHDFlag == false then
            Facade.ConfigManager:SetBoolean("LITE_HAS_BEEN_WRITE_HD_FLAG", true)
            if IsBuildRegionCN() then
                if deviceLevel > HD_DOWNLOAD_MODEL_LEVEL or bHDHasBeenDownloaded then
                    bNeedHD = true
                end
            else
                if bHDHasBeenDownloaded then
                    bNeedHD = true
                end
            end

            local downloadHDLimit = HD_DOWNLOAD_NEED_SPACE
            if IsBuildRegionCN() and bNeedHD and bHDHasBeenDownloaded == false and deviceLevel > HD_DOWNLOAD_MODEL_LEVEL then
                downloadHDLimit = HD_DOWNLOAD_NEED_SPACE_NEWPLAYER
            end

            if spaceGB > 0 and spaceGB < downloadHDLimit then
                self.nowChangeToLow = true
                bNeedHD = false
                LiteDownloadManager:SetHDDownloadFlagStr(false)
                loginfo("[LitePackageModule] DoDownloadBeforeLogin WriteLoginHDFlag spaceGB > 0 and spaceGB < HD_DOWNLOAD_NEED_SPACE, change to HD = false")
            end

            Facade.ConfigManager:SetBoolean("LITE_NEED_UPDATE_HD_FLAG", bNeedHD)
            loginfo("[LitePackageModule] DoDownloadBeforeLogin WriteLoginHDFlag:"..tostring(bNeedHD))
        else
            bNeedHD = Facade.ConfigManager:GetBoolean("LITE_NEED_UPDATE_HD_FLAG", false)
            loginfo("[LitePackageModule] DoDownloadBeforeLogin GetLoginHDFlag:"..tostring(bNeedHD))
        end

        if bisInReview == true or bHasBeenDeleteHD then
            bNeedHD = false
            loginfo("[LitePackageModule] DoDownloadBeforeLogin bisInReview or bHasBeenDeleteHD set HD = false"..tostring(bNeedHD))
        end

        if bNeedHD then
            table.insert(childModule, "OtherBeforeLoginHD")
        end

        local playerSelectGameMode = Facade.ConfigManager:GetString("DOWNLOAD_BEFORELOGIN_GAME_MODE", "MP")
        if IsBuildRegionCN() then
            playerSelectGameMode = "SOL"
        end

        if playerSelectGameMode == "MP" then
            table.insert(childModule, MPModuleName)
            if bNeedHD then
                table.insert(childModule, MPModuleNameHD)
            end
        else
            table.insert(childModule, SOLModuleName)
            if bNeedHD then
                table.insert(childModule, SOLModuleNameHD)
            end
        end
        logerror("[LitePackageModule] DoDownloadBeforeLogin playerSelectGameMode:", playerSelectGameMode)

        table.insert(childModule,"MapCommon0")
        if not IsBuildRegionCN() then
            local locPakModule = LitePackageLogic.GetLocalizationResModuleName()
            table.insert(childModule, locPakModule)
            logerror("[LitePackageModule] try download cur culture localization pak:", locPakModule)
        end

        if bNeedDownload then
            -- logerror("[LitePackageModule] DoDownloadBeforeLogin #childModule:", tostring(#childModule))

            local moduleAllPaks = {}
            for key, module in pairs(childModule) do
                local paks = LiteDownloadManager:GetPakNamesByModuleName(module, false)
                -- logerror("[LitePackageModule] module:"..module.."  GetPakNamesByModuleName #paks:", tostring(#paks))
                if paks ~= nil and #paks > 0 then
                    for id, pakName in pairs(paks) do
                        local bContains = table.contains(moduleAllPaks, pakName)
                        if bContains == false then
                            table.insert(moduleAllPaks, pakName)
                            -- logerror("[LitePackageModule] DoDownloadBeforeLogin moduleAllPaks add pak:", pakName)
                        end
                    end
                end
            end
            LiteDownloadManager:RegisterRuntimeMultiModule(self.ModuleKey, moduleAllPaks, true)
            self.AllDownloadBeforeloginFiles = moduleAllPaks
        end
    else
        bNeedDownload = false
    end

    return bNeedDownload
end

local LiteDownloadDataTable = nil
function LitePackageModule:DoDownloadBeforeLogin(...)
    local bNeedDownload = self:RegisterRuntimeDownloadModule()

    if bNeedDownload then
        LiteDownloadManager:CheckModuleAsync(self.ModuleKey)
    else
        self:FinishPufferUpdateStep(true)
    end
end

function LitePackageModule:_afterCheckAndNeedDownload()
    self.nowdownloadModule = self.ModuleKey
    LiteDownloadManager:DownloadByModuleName(self.ModuleKey, false)
    -- local showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_DLC, "", "", "", "")
    self:UpdateLoadingTips(showStr)

    LiteDownloadManager:ReportDLCUpdateEvent(self.recordTotalSize, self.recordNowSize, self.recordPercent,
        self.recordTimeStart, "", "", "", 0, -1, "")
    loginfo("[LitePackageModule] _afterCheckAndNeedDownload DoDownloadBeforeLogin.")

    Module.GCloudSDK:PreCompileAllPSOOnDownloading()
end

function LitePackageModule:DownloadManagerNtfModuleCheckResult(moduleName, bDownloaded, nowSize, totalSize, errorCode)
    if moduleName ~= self.ModuleKey then
        return
    end 
    
    --use backgroud download
    LiteDownloadManager:SetBGDEnabled(true)

    if errorCode > 0 then
        -- local bisInReview = UGameVersionUtils.IsInReview()
        -- if bisInReview then
        --     self:FinishPufferUpdateStep(true)
        --     loginfo("[LitePackageModule] DownloadManagerNtfModuleCheckResult download failed but bisInReview FinishPufferUpdateStep.")
        -- else
            
        -- end
        local showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed, tostring(errorCode))
        local strRetry = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_Retry
        local strQuitgame = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_QuitGame
        self:ShowUpdateFinishFailWindow(showStr, strRetry, strQuitgame)

        return
    end

    if bDownloaded then
        self:UpdatePercentFloat(100)
        self:FinishPufferUpdateStep(true)
    else
        self.needRecord = 1
        local startTime = os.date("%Y-%m-%d %H:%M:%S")
        self.recordTimeStart = startTime
        self.recordTimeEnd = startTime
        self.reportSpeedTimeStart = startTime
        self.reportSpeedTimeStamp = os.time()

        self.recordTotalSize = totalSize
        self.recordNowSize = nowSize
        self.reportSizeStart = nowSize
        self.recordPercent = math.floor(nowSize / totalSize * 100)

        local netState = LiteDownloadManager:GetNetworkState()
        if netState == 1 then
            local bisInReview = UGameVersionUtils.IsInReview()
            local needUpdataSize = (totalSize - nowSize) / LiteDownloadMBNum
            if bisInReview then
                local showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_CheckText, tostring(needUpdataSize))
                local strRetry = Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_Confirm
                local strQuitgame = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_QuitGame
                self:ShowBeforeLoginDownloadPop(showStr, strRetry, strQuitgame)
            else
                self:_afterCheckAndNeedDownload()
            end
        else
            local needUpdataSize = (totalSize - nowSize) / LiteDownloadMBNum
            local showStr = ""
            local bToTips = false
            if needUpdataSize < 1 then
                bToTips = true
                showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_Not_Wifi_NOSize,
                    tostring(needUpdataSize))
            end

            if needUpdataSize > ToTipsDownloadSizeMBWithoutWifi then
                bToTips = true
                showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_Not_Wifi,
                    tostring(needUpdataSize))
            end

            if bToTips and self.hasBeenPopNetTips ~= true then
                local strRetry = Module.LitePackage.Config.Loc.BeforeLogin_Download_Update_Confirm
                local strQuitgame = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_QuitGame
                self:ShowBeforeLoginDownloadPop(showStr, strRetry, strQuitgame)
            else
                self:_afterCheckAndNeedDownload()
            end
        end
    end
end

function LitePackageModule:GetSizeStr(size)
    local str = string.format("%.1f", size / LiteDownloadMBNum)
    str = str..Module.VersionUpdate.Config.Loc.UpdateSizeMB
    return str
end

function LitePackageModule:GetCurrentDownloadedNowSize()
    local nowDownloadedSize = 0

    if self.needCombineToDownlaodPak ~= nil then
        for index, pakName in pairs(self.needCombineToDownlaodPak) do
            local combinePakPath = self.combinePath.."/"..pakName
            local fileState, nowSize, totalSize = LiteDownloadManager:GetPufferFileStateAndSize(combinePakPath)
            local bInDownloadQueue = table.contains(self.downloadToCombineQueue, pakName)
            if self.nowDonwloadCombinePakPath == pakName then
                bInDownloadQueue = true
            end

            if bInDownloadQueue then
                nowDownloadedSize = nowDownloadedSize + nowSize
            else
                nowDownloadedSize = nowDownloadedSize + totalSize
            end
        end
    end

    if self.CurrentDownloadNoPatchPaks ~= nil then
        for index, pakName in pairs(self.CurrentDownloadNoPatchPaks) do
            local fileState, nowSize, totalSize = LiteDownloadManager:GetPufferFileStateAndSize(pakName)
            nowDownloadedSize = nowDownloadedSize + nowSize
        end
    end

    loginfo("[LitePackageModule] GetCurrentDownloadedNowSize nowDownloadedSize:"..nowDownloadedSize)
    loginfo("[LitePackageModule] GetCurrentDownloadedNowSize #self.CurrentDownloadNoPatchPaks:"..tostring(#self.CurrentDownloadNoPatchPaks))
    return nowDownloadedSize
end

function LitePackageModule:GetCurrentDownloadBeginTotalSize()
    local ret = 1
    if self.haeBeenCalcTotalSize == nil then
        self.haeBeenCalcTotalSize = true
        self.CurrentDownloadBeginSize = 0
        self.CurrentDownloadNoPatchPaks = {}

        if self.needCombineToDownlaodPak ~= nil then
            for index, pakName in pairs(self.needCombineToDownlaodPak) do
                local combinePakPath = self.combinePath.."/"..pakName
                local fileState, nowSize, totalSize = LiteDownloadManager:GetPufferFileStateAndSize(combinePakPath)
                self.CurrentDownloadBeginSize = self.CurrentDownloadBeginSize + totalSize
                loginfo("[LitePackageModule] GetCurrentDownloadBeginTotalSize add:"..pakName.." totalSize:"..totalSize)
            end
        end

        if self.AllDownloadBeforeloginFiles ~= nil then
            -- logerror("[LitePackageModule] GetCurrentDownloadBeginTotalSize #self.AllDownloadBeforeloginFiles:"..tostring(#self.AllDownloadBeforeloginFiles))
            for index, pakName in pairs(self.AllDownloadBeforeloginFiles) do
                local pakIndex = pakName:match("pakchunk%d+optional?") or pakName:match("pakchunk%d+")

                local bHasPatch = false
                if self.needCombineToDownlaodPakIndexs ~= nil and self.needCombineToDownlaodPakIndexs[pakIndex] == true then
                    bHasPatch = true
                end

                if bHasPatch == false then
                    local fileState, nowSize, totalSize = LiteDownloadManager:GetPufferFileStateAndSize(pakName)
                    if fileState == false then
                        self.CurrentDownloadBeginSize = self.CurrentDownloadBeginSize + totalSize
                        table.insert(self.CurrentDownloadNoPatchPaks, pakName)
                        -- loginfo("[LitePackageModule] GetCurrentDownloadBeginTotalSize bHasPatch = false add:"..pakName.." totalSize:"..totalSize)
                    -- else
                    --     loginfo("[LitePackageModule] GetCurrentDownloadBeginTotalSize fileState == true add:"..pakName.." totalSize:"..totalSize)
                    end
                end
            end
        end
    end

    ret = self.CurrentDownloadBeginSize
    return ret
end

local FakeNowSizeRepeatCount = 3
local FakeDownloadSpeed = 1
local FakeCount = 1
function LitePackageModule:OnModuleDownloadProgress(moduleName, nowSize, totalSize)
    if self.puffer_config_key == moduleName then
        loginfo("[LitePackageModule] OnModuleDownloadProgress modulename = puffer_config_key")
    elseif self.pakResFixModuleName == moduleName then
        local percent = math.floor(nowSize / totalSize * 1000) / 10
        local showStr = Module.LitePackage.Config.Loc.LITE_PAK_FIX_DEEP_FIX_PAK

        self:UpdateLoadingTips(showStr)
        self:UpdatePercentFloat(percent)

    elseif self.UpdateCombinePakKey == moduleName then
        local counterNow = #self.downloadToCombineQueue
        local counterAll = #self.needCombineToDownlaodPak

        local beginTotalSize = self:GetCurrentDownloadBeginTotalSize()
        if beginTotalSize <= 0 then
            beginTotalSize = 1
        end
        local beginNowSize = self:GetCurrentDownloadedNowSize()
        if beginNowSize >= beginTotalSize then
            beginNowSize = beginTotalSize - 1000
        end

        local percent = math.floor(beginNowSize / beginTotalSize * 1000) / 10

        local downloadSpeed = LiteDownloadManager:GetDownlaodSpeed()
        local nowSizeStr = self:GetSizeStr(beginNowSize)
        local totalSizeStr = self:GetSizeStr(beginTotalSize)
        local speedStr = self:GetSizeStr(downloadSpeed)
        local remainTimeStr = ""

        if downloadSpeed > 0 then
            local remainTime = (totalSize - nowSize) / downloadSpeed
            local time = math.floor(remainTime)

            if time ~= nil and time > 0 then
                remainTimeStr = TimeUtil.GetSecondsFormatMMSSStr(time)
            end
        end

        local downloadStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_DLC, nowSizeStr, totalSizeStr, remainTimeStr, speedStr)
        local showStr = string.format(Module.LitePackage.Config.Loc.LITE_PAK_COMBINE_PROGRESS_TIP, counterAll - counterNow, counterAll, downloadStr)

        self:UpdateLoadingTips(showStr)
        self:UpdatePercentFloat(percent)
    elseif self.ModuleKey == moduleName then
        if totalSize > 0 then
            self.recordTotalSize = totalSize
            self.recordNowSize = nowSize
            self.recordPercent = math.floor(nowSize / totalSize * 100)
            local downloadSpeed = LiteDownloadManager:GetDownlaodSpeed()
            --self._wtDownloadingPercent:SetText(percent*100 .. "%")

            local beginTotalSize = self:GetCurrentDownloadBeginTotalSize()
            local beginNowSize = self:GetCurrentDownloadedNowSize()
            if beginNowSize >= beginTotalSize then
                beginNowSize = beginTotalSize - 1000
            end

            if beginTotalSize > 0 then
                totalSize = beginTotalSize
                nowSize = beginNowSize
            end

            local percent = math.floor(nowSize / totalSize * 1000) / 10

            local fakePercent = self:GetFakeProgress(percent)
            if fakePercent > 0 and fakePercent <= 100 then
                nowSize = math.floor(totalSize / 100 * fakePercent)
                local speedRate = fakePercent / percent
                if speedRate > 1.5 then
                    speedRate = 1.5
                end
                downloadSpeed = downloadSpeed * speedRate
                if downloadSpeed / LiteDownloadMBNum >30 then
                    downloadSpeed = 30
                end
            end

            local nowSizeStr = self:GetSizeStr(nowSize)
            local totalSizeStr = self:GetSizeStr(totalSize)
            local speedStr = self:GetSizeStr(downloadSpeed)
            local remainTimeStr = ""

            if downloadSpeed > 0 then
                local remainTime = (totalSize - nowSize) / downloadSpeed
                local time = math.floor(remainTime)

                if time ~= nil and time > 0 then
                    remainTimeStr = TimeUtil.GetSecondsFormatMMSSStr(time)
                end
            end

            local downloadStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_DLC, nowSizeStr, totalSizeStr, remainTimeStr, speedStr)
            local showStr  = string.format(Module.LitePackage.Config.Loc.LITE_PAK_NO_COMBINE_PROGRESS_TIP, downloadStr)
            if self.bIsRelease == false then
                showStr = showStr.." [RealPercent:"..percent.."%]"
            end

            if self.nowChangeToLow == true then
                showStr = string.format(Module.LitePackage.Config.Loc.LITE_PAK_UPDATE_HD_TO_LOW, downloadStr)
            end

            self:UpdateLoadingTips(showStr)
            self:UpdatePercentFloat(fakePercent)

            --30s report download event
            local nowTime = os.time()
            local diffSeconds = 0
            if self.reportSpeedTimeStamp ~= nil and nowTime ~= nil and self.reportSpeedTimeStamp > 0 and nowTime > 0 then
                diffSeconds = os.difftime(nowTime, self.reportSpeedTimeStamp)
            end

            if diffSeconds >= 30 then
                local reportTime = os.date("%Y-%m-%d %H:%M:%S")
                self.reportSpeedTimeEnd = reportTime
                self.reportSizeEnd = self.recordNowSize
                local traffic = self.reportSizeEnd - self.reportSizeStart
                self.reportSizeStart = self.reportSizeEnd
                LiteDownloadManager:ReportDLCUpdateEvent(self.recordTotalSize, self.recordNowSize, self.recordPercent,
                    self.recordTimeStart, self.recordTimeEnd, self.reportSpeedTimeStart, self.reportSpeedTimeEnd, traffic
                    , -1, "")
                loginfo("[LitePackageModule] OnModuleDownloadProgress ReportDLCUpdateEvent traffic")
                self.reportSpeedTimeStart = reportTime
                self.reportSpeedTimeStamp = os.time()
            end
        end
    end
end

function LitePackageModule:GetFakeProgress(progress)
    if totalSize == 0 then
        return 0
    end

    local fakeProgress = 0
    local point = self:GetPointFromLUT(progress / 100)
    if point == nil then
        return fakeProgress
    end

    fakeProgress = point.y * 100
    if fakeProgress < 0 then
        return 0
    end

    if fakeProgress > 100 then
        return 100
    end

    return fakeProgress
end

function LitePackageModule:ModuleDownloadResult(moduleName, bDownloaded, errorCode)
    self.recordTimeEnd = os.date("%Y-%m-%d %H:%M:%S")
    if errorCode > 0 then
        -- local bisInReview = UGameVersionUtils.IsInReview()
        -- if bisInReview then
        --     self:FinishPufferUpdateStep(true)
        --     loginfo("[LitePackageModule] ModuleDownloadResult download failed but bisInReview FinishPufferUpdateStep.")
        -- else
        -- end

        local bInitSucced = LiteDownloadManager:IsPufferInitSucceed()
        if bInitSucced == false then
            local initErrorCode = LiteDownloadManager:GetPufferInitErrorCode()
            if initErrorCode > 0 then
                loginfo("[LitePackageModule] ModuleDownloadResult puffer initErrorCode:"..initErrorCode)
                errorCode = initErrorCode
            end

            local showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed, tostring(errorCode))
            local strRetry = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_Retry
            local strQuitgame = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_QuitGame
            self:ShowUpdateFinishFailWindow(showStr, strRetry, strQuitgame)
        else
            local showStr = string.format(Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed, tostring(errorCode))
            local strRetry = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_Retry
            local strQuitgame = Module.LitePackage.Config.Loc.BeforeLogin_Download_Failed_QuitGame
            self:ShowUpdateFinishFailWindow(showStr, strRetry, strQuitgame)
            if self.needRecord == 1 then
                LiteDownloadManager:ReportDLCUpdateEvent(self.recordTotalSize, self.recordNowSize, self.recordPercent,
                    self.recordTimeStart, self.recordTimeEnd, "", "", 0, 1, tostring(errorCode))
                loginfo("[LitePackageModule] ModuleDownloadResult ReportDLCUpdateEvent 1.")
            end
        end

        -- 出现错误，需要上报
        if DFGameLaunchManager then
            DFGameLaunchManager:FinishStep("PufferResUpdate", ELaunchStepResult.ELSR_Failed, tostring(errorCode))
        end

        return
    end

    if self.puffer_config_key == moduleName then
        LiteDownloadManager:ReloadLiteConfig()
        LiteDownloadManager:ResetPaksFileSizeAndMD5()
        local bNeedDownload = self:RegisterRuntimeDownloadModule() -- register runtimedownload module

        -- check and delete res
        LiteDownloadManager:StartDeletePaksByModuleNames()
        LiteDownloadManager:CheckAndStartDeleteHDRes()

        -- check user mark fix all pak
        local bNeedFixDeep = LiteDownloadManager:GetAllPakFixDeep()
        local bNeedFixPak = LiteDownloadManager:GetLaunchFixPak()
        if bNeedFixDeep == true then
            self:CheckAndFixPaksAllDeep()
        elseif bNeedFixPak == true then
            LiteDownloadManager:SetLaunchFixPakFinished()
            self:CheckAndFixPaks()
        else
            self:CheckAndStartPakRecombine()
        end
    elseif self.pakResFixModuleName == moduleName then
        LiteDownloadManager:ReportPakFixInfoToCrashSight("FIXED")
        loginfo("[LitePackageModule] ModuleDownloadResult self.pakResFixModuleName == moduleName.")
        self:CheckAndStartPakRecombine()
    elseif self.UpdateCombinePakKey == moduleName then
        loginfo("[LitePackageModule] ModuleDownloadResult DownloadAndCombinePak.")
        self:DownloadAndCombinePak()
    else
        self:FinishPufferUpdateStep(true)
        loginfo("[LitePackageModule] ModuleDownloadResult FinishPufferUpdateStep.")
        if self.needRecord == 1 then
            local traffic = self.recordTotalSize - self.reportSizeStart
            LiteDownloadManager:ReportDLCUpdateEvent(self.recordTotalSize, self.recordTotalSize, 100,
                self.recordTimeStart, self.recordTimeEnd, self.reportSpeedTimeStart, self.recordTimeEnd, traffic, 0,
                tostring(errorCode))
            loginfo("[LitePackageModule] ModuleDownloadResult ReportDLCUpdateEvent 0.")
        end
    end
end

function LitePackageModule:Retry()
    local initErrorCode = LiteDownloadManager:GetPufferInitErrorCode()
    if initErrorCode > 0 then
        loginfo("[LitePackageModule] ModuleDownloadResult puffer initErrorCode:"..initErrorCode)
        LiteDownloadManager:ReleasePufferDownloader()
        LiteDownloadManager:InitPufferDownloader()
    end

    self:UpdateLoadingTips(Module.LitePackage.Config.Loc.BeforeLogin_Check_DLC)
    if self.nowdownloadModule == self.puffer_config_key then
        LiteDownloadManager:DownloadForceByModuleName(self.nowdownloadModule, true)
    else
        LiteDownloadManager:DownloadByModuleName(self.nowdownloadModule, false)
    end

    loginfo("[LitePackageModule] Retry dlc update.")
end

function LitePackageModule:ShowUpdateFinishFailWindow(content, confirmTxt, cancelTxt)
    local confirmHandle = function()
        self:Retry()
    end

    local cancelHandle = function()
        UKismetSystemLibrary.QuitGame(GetGameInstance(), nil, EQuitPreference.Quit, false)
        loginfo("[LitePackageModule] ShowUpdateFinishFailWindow QuitGame.")
    end

    local bShowOneButton = false
    if PLATFORM_IOS then
        bShowOneButton = true
    end

    Module.GCloudSDK:ShowCommonTip(content, confirmTxt, cancelTxt, bShowOneButton, confirmHandle, cancelHandle)
end

function LitePackageModule:CloseUpdatePanel()
    --Facade.UIManager:CloseUIByHandle(self._mainPanelHandle)
end

function LitePackageModule:FinishPufferUpdateStep(bSuc)
    LiteDownloadManager:SetBGDEnabled(false)
    LiteDownloadManager:SetReportServerFlag(true)

    loginfo("[LitePackageModule] FinishPufferUpdateStep enter.")
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleCheckResult)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadProgress)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtDownloadManagerNtfModuleDownloadResult)
    self:RemoveLuaEvent(Module.LitePackage.Config.evtPopSelectPopClose)

    -- loginfo("[LitePackageModule] [PREDOWNLOAD TEST] Calc and print all files md5.")
    --self:CloseUpdatePanel()

    LitePackageLogic.CheckAndDeleteUnnecessaryPaks() -- 检查并处理删除不必要的pak逻辑

    LiteDownloadManager:MountDownloadedPak()
    LiteDownloadManager:ProcessPakDebug()

    -- mount之后再初始化sql和本地化
    LitePackageLogic.InitLocalizeRes()
    
    
    -- LiteDownloadManager:SetImmDLMaxDownloadsPerTask(3)
    -- self:ReportPreDownloadInfo(1, 1, 0)

    -- self:CheckPredownloadMD5()

    UDFMGameplayGlobalDelegates.Get(GetWorld()):OnPacUpdateEnd(true)
    LiteDownloadManager:ResetAllDownloadedQuestIDsWithNoServer()

    if DFGameLaunchManager then
        DFGameLaunchManager:FinishStep("PufferResUpdate", ELaunchStepResult.ELSR_Success, "")
        loginfo("[LitePackageModule] DFGameLaunchManager:FinishStep PufferResUpdate")
    else
        loginfo("[LitePackageModule] DFGameLaunchManager == nil!!")
    end

    if (IsBuildRegionGlobal() or IsBuildRegionGA()) or true then
        for key, value in pairs(LITE_NEED_CHECK_BEFORE_LOGIN_MODULE_STATA) do
            LiteDownloadManager:CheckModuleAsync(value)
        end
    end

    Facade.ConfigManager:SetString(self.versionCombineKey, STR_KEY_FINISHEDCOMBINE)

    -- local size, totlasize = LiteDownloadManager:GetPufferFileSize("a1613e62_pak-0-2-pakchunk61-Android_ASTCClient.pak")
    -- loginfo("[LitePackageModule] ModuleDownloadResult FinishPufferUpdateStep.size:"..tostring(size))
    -- loginfo("[LitePackageModule] ModuleDownloadResult FinishPufferUpdateStep.totlasize:"..tostring(totlasize))
    -- local ret = LiteDownloadManager:CheckModuleNameNotRumtimeSpecial("RuntimeUpdateCombinePakKey29")
    -- loginfo("[LitePackageModule] ModuleDownloadResult FinishPufferUpdateStep. CheckModuleNameNotRumtimeSpecial ret:"..tostring(ret))

    LiteDownloadManager:RegisterHDDownloadStatusMultiModule()
    LiteDownloadManager:StartCheckModuleInfosAsync()
    LitePackageLogic:InitPackageReddot()
    
    if PLATFORM_ANDROID or PLATFORM_IOS then
        Module.GCloudSDK:OnPufferDownloadFinish()
    end
    -- LiteDownloadManager:CheckAllPaksMD5()

    LiteDownloadManager:CheckAndSetPakFileStateToNeedDownload()
end

function LitePackageModule:CheckPredownloadMD5()
    LiteDownloadManager:CheckPredownloadMD5()
end



function LitePackageModule:CheckIsMapsDownload(mapList, bFromRoom)
    return LiteDownloadManager:CheckIsMapsDownload(mapList, bFromRoom)
end

function LitePackageModule:GetQuestNameByMapId(mapId)
    return LiteDownloadManager:GetQuestNameByMapId(mapId)
end

function LitePackageModule:GetLowModuleName(moduleName)
    return LiteDownloadManager:GetLowModuleName(moduleName)
end

function LitePackageModule:GetQuestNameByModuleName(moduleName)
    return LiteDownloadManager:GetQuestNameByModuleName(moduleName)
end

function LitePackageModule:GetDownloadCategary(iItemID)
    return LiteDownloadManager:GetDownloadCategary(iItemID)
end

function LitePackageModule:IsDownloadedByModuleName(moduleName)
    return LiteDownloadManager:IsDownloadedByModuleName(moduleName)
end

function LitePackageModule:GetWeaponPartItemResDownloaded()
    return LiteDownloadManager:GetWeaponPartItemResDownloaded()
end

function LitePackageModule:GetWeaponPartModuleKey()
    return LiteDownloadManager:GetWeaponPartModuleKey()
end

function LitePackageModule:SetUserBoolean(str, judge)
    Facade.ConfigManager:SetBoolean(str, judge)
end

function LitePackageModule:DownloadHDCollection()
    LiteDownloadManager:DownloadHDCollection()
end

function LitePackageModule:UpdatePercentFloat(percent)
    Module.GCloudSDK:UpdatePercentFloat(percent)
end

function LitePackageModule:UpdateLoadingTips(tips)
    Module.GCloudSDK:UpdateLoadingTips(tips)
end


function LitePackageModule:GetHDCollectionNeedDownloadSize()
    local sHDDownloadCollection = LiteDownloadManager:GetHDCollectionModuleName()
    return LiteDownloadManager:GetTotalSizeByModuleName(sHDDownloadCollection) - LiteDownloadManager:GetNowSizeByModuleName(sHDDownloadCollection)
end

function LitePackageModule:ResetAllDownloadedQuestIDs()
    LiteDownloadManager:ResetAllDownloadedQuestIDs()
end

function LitePackageModule:SendDownloadSettingInfo(key,value)
    local tglog = pb.DownloadSettingInfo:New()
    tglog.DownLoadMode = 0
    tglog.DefinitionChange = 0
    tglog.PackageCleanUP = 0
    tglog.PackageFix = 0
    tglog[key] = value
    LogAnalysisTool.AddTglog(tglog)
end

function LitePackageModule:GetPointFromLUT(t)
    if self.curveLUT == nil then
        local controlPoints = {
            {x = 0, y = 0}, {x = 0.23, y = 0.67},
            {x = 0.73, y = 0.73}, {x = 1, y = 1}
        }
        self.curveLUT = self:PrecomputeCurve(controlPoints, 1000)
    end

    if self.curveLUT ~= nil then
        local idx = math.floor(t * #self.curveLUT)
        idx = math.min(idx, #self.curveLUT)  -- 防止越界
        return self.curveLUT[idx]
    end

    return t
end

function LitePackageModule:EasyProgress(p0, p1, p2, p3, t)
    return (1-t)^3*p0 + 3*t*(1-t)^2*p1 + 3*t^2*(1-t)*p2 + t^3*p3
end

-- 三阶贝塞尔曲线计算函数
function LitePackageModule:CubicBezier(p0, p1, p2, p3, t)
    local u = 1 - t
    local u2 = u * u
    local t2 = t * t
    local u3 = u2 * u
    local t3 = t2 * t
    -- 混合控制点权重
    local x = u3*p0.x + 3*u2*t*p1.x + 3*u*t2*p2.x + t3*p3.x
    local y = u3*p0.y + 3*u2*t*p1.y + 3*u*t2*p2.y + t3*p3.y
    return {x = x, y = y}
end

function LitePackageModule:PrecomputeCurve(points, segments)
    local lut = {}
    for i = 0, segments do
        local t = i / segments
        local point = self:CubicBezier(points[1], points[2], points[3], points[4], t)
        lut[i] = point  -- 存储坐标
    end
    return lut
end


function LitePackageModule:CachQuestToDownload()
    LiteDownloadManager:CachQuestToDownload()
end

function LitePackageModule:ClearQuestToDownload()
    LiteDownloadManager:ClearQuestToDownload()
end

function LitePackageModule:CheckWIFIDownloadQuests(trigger)
    LiteDownloadManager:CheckWIFIDownloadQuests(trigger)
end


function LitePackageModule:GetSystemDownloadModuleName(trigger)
    return LiteDownloadManager:GetSystemDownloadModuleName(trigger)
end

function LitePackageModule:CheckHasPackageReddot()
    return LitePackageLogic.CheckHasPackageReddot()
end

function LitePackageModule:ReleasePufferDownloader()
    LiteDownloadManager:ReleasePufferDownloader()
end

function LitePackageModule:GetCantReportReason()
    return LiteDownloadManager:GetCantReportReason()
end

return LitePackageModule
