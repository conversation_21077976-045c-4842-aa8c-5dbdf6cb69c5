----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class ActivityMagicTowerConfigManager
local ActivityMagicTowerConfigManager = {}

local function testAudioLog(...)
    loginfo("[Heimu_Audio] ", ...)
end

local function testAudioWarningLog(...)
    logwarning("[Heimu_Audio] ", ...)
end

ActivityMagicTowerConfigManager._musicIdx = nil

ActivityMagicTowerConfigManager.ITEM_CLASS_NAME = {
    ENEMY    = "enemy",
    NPC      = "npc",
    HERO     = "hero",
    PROP     = "prop",
    TREASURE = "treasure",
    DOOR     = "door"
}

ActivityMagicTowerConfigManager.ITEM_PREFIX = {
    ENEMY    = "301",
    NPC      = "302",
    HERO     = "303",
    PROP     = "304",
    TREASURE = "305",
    DOOR     = "20311"
}

local AUDIO_NAME = {
    [1]  = "UI_MorgenGame_Footsteps",                 -- 玩家走路
    [2]  = "UI_MorgenGame_Loot",                      -- looting过程
    [3]  = "UI_MorgenGame_Electronic_Lock",           -- 玩家使用门禁卡
    [4]  = "UI_MorgenGame_Scene_Switch",              -- 场景切换音效
    [5]  = "UI_MorgenGame_Reward_Props",              -- 拾取道具
    [6]  = "UI_MorgenGame_Reward_Money",              -- 加龙门币
    [7]  = "UI_MorgenGame_Upgrade",                   -- 升级，能力强化音效
    [8]  = "UI_MorgenGame_Chat",                      -- 对话出现音效
    [9]  = "UI_MorgenGame_Dead_Monster",              -- 怪物死亡
    [10] = "UI_MorgenGame_Attack_Common_Player",      -- 玩家普通攻击
    [11] = "UI_MorgenGame_Attack_Critical_Player",    -- 玩家暴击，挥动
    [12] = "UI_MorgenGame_Attack_Common_Monster",     -- 怪物非枪械攻击
    [13] = "UI_MorgenGame_Attack_Common_Wpn",         -- 怪物枪械攻击
    [14] = "UI_MorgenGame_Attack_Critical_Monster",   -- 怪物非枪械暴击
    [15] = "UI_MorgenGame_Attack_Critical_Wpn",       -- 怪物枪械暴击
    [16] = "UI_SOL_Settlement_Fail",                  -- 玩家撤离失败
    [17] = "UI_SOL_Settlement_Success",               -- 玩家撤离成功
    [18] = "UI_MorgenGame_Game_Enter",                -- 进入开始界面
    [19] = "UI_MorgenGame_Battle_Start",              -- 战斗开始
}

local MUSIC_NAME = {
    [1]  = "UI_MorgenGame_Interface_In",              -- 进入界面场景播放
    [2]  = "UI_MorgenGame_Interface_Out",             -- 退出界面场景播放
    [3]  = "Music_MorgenGame_Outdoor_Normal_Loop",    -- 外部普通场景（LevelType = 1）
    [4]  = "Music_MorgenGame_Outdoor_Museum_Loop",    -- 外部博物馆（LevelType = 2）
    [5]  = "Music_MorgenGame_Indoor_Normal_Loop",     -- 内塔普通场景（LevelType = 3）
    [6]  = "Music_MorgenGame_Case_Heavy",             -- 关键事件
    [7]  = "Music_MorgenGame_Combat_Boss1",           -- BOSS战，雷斯
    [8]  = "Music_MorgenGame_Combat_Boss2",           -- BOSS战，赛义德
    [9]  = "Music_MorgenGame_Combat_Boss3",           -- BOSS战，德穆兰
    [10] = "Music_Lobby_MingRiFangZhou_Start",        -- 进入明日方舟页签
    [11] = "Music_Lobby_MingRiFangZhou_Stop",         -- 离开明日方舟页签
}

local TYPE_RULES = {
    [ActivityMagicTowerConfigManager.ITEM_PREFIX.ENEMY   ] = ActivityMagicTowerConfigManager.ITEM_CLASS_NAME.ENEMY   ,
    [ActivityMagicTowerConfigManager.ITEM_PREFIX.NPC     ] = ActivityMagicTowerConfigManager.ITEM_CLASS_NAME.NPC     ,
    [ActivityMagicTowerConfigManager.ITEM_PREFIX.HERO    ] = ActivityMagicTowerConfigManager.ITEM_CLASS_NAME.HERO    ,
    [ActivityMagicTowerConfigManager.ITEM_PREFIX.PROP    ] = ActivityMagicTowerConfigManager.ITEM_CLASS_NAME.PROP    ,
    [ActivityMagicTowerConfigManager.ITEM_PREFIX.TREASURE] = ActivityMagicTowerConfigManager.ITEM_CLASS_NAME.TREASURE,
    [ActivityMagicTowerConfigManager.ITEM_PREFIX.DOOR    ] = ActivityMagicTowerConfigManager.ITEM_CLASS_NAME.DOOR    ,
}

ActivityMagicTowerConfigManager.ParseItemTypeByID = function(itemID)
    if not itemID then return end

    local idStr = tostring(itemID)
    if #idStr < 3 then return nil end

    if #idStr >= 5 then
        local prefix5 = string.sub(idStr, 1, 5)
        if TYPE_RULES[prefix5] then
            return TYPE_RULES[prefix5]
        end
    end

    local prefix3 = string.sub(idStr, 1, 3)
    return TYPE_RULES[prefix3]
end

ActivityMagicTowerConfigManager.PlayAudio = function(index, isBool)
    if index == nil or AUDIO_NAME == nil or AUDIO_NAME[index] == nil then return end
    
    isBool = setdefault(isBool, true)

    testAudioLog("播放音频事件：", index, isBool)
    
    if isBool then
        Facade.SoundManager:PlayUIAudioEvent(AUDIO_NAME[index])
    else
        Facade.SoundManager:StopUIAudioEvent(AUDIO_NAME[index])
    end
end

ActivityMagicTowerConfigManager.PlayMusic = function(index, isBool)
    if index == nil or MUSIC_NAME == nil or MUSIC_NAME[index] == nil then return end
    
    isBool = setdefault(isBool, true)

    if ActivityMagicTowerConfigManager._musicIdx == index then
        testAudioWarningLog("重复的音乐事件, 不播放：", index, isBool)
        return
    end
    ActivityMagicTowerConfigManager._musicIdx = index

    testAudioLog("播放音乐：", index, isBool)

    if isBool then
        Facade.SoundManager:PlayMusicByName(MUSIC_NAME[index])
    else
        Facade.SoundManager:StopMusicByName(MUSIC_NAME[index])
    end
end

ActivityMagicTowerConfigManager.ClearMusicIdx = function()
    testAudioWarningLog("清除音乐索引")
    ActivityMagicTowerConfigManager._musicIdx = nil
end

--// 关卡表相关
ActivityMagicTowerConfigManager.GetLevelConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerLevelConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            levelID                = value.LevelID, -- 关卡ID
            levelStructure         = value.LeveStructure, -- 关卡结构ID

            levelName              = value.LevelName, -- 关卡名字
            levelSubName           = value.LevelSubName, -- 关卡名字后缀

            levelType              = value.LevelType, -- 关卡类型 [1：外围 2：内塔] (暂未使用)
            levelCycle             = value.LevelCycle, -- 该关卡所属周目 (暂未使用)

            levelSizeHorizontal    = value.LevelSizeHorizontal,
            levelSizeVertical      = value.LevelSizeVertical,

            lackgroundAsset        = value.BackgroundAsset,
            fightAsset             = value.FightAsset,
        }

        allConfig[value.LevelID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetLevelStructureConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerLevelStructureConfig")
    local allConfig = {}

    for key, value in configTable:LessGCPairs() do
        local levelStructureID = value.LevelStructureID -- 关卡结构ID

        local converted = {
            -- ID                     = value.ID, -- 无意义序号ID
            -- levelStructureID       = value.LevelStructureID, -- 关卡结构ID

            coordinateX            = value.CoordinateX, -- 格子X轴坐标
            coordinateY            = value.CoordinateY, -- 格子Y轴坐标

            specialPoint           = value.SpecialPoint, -- 特殊点ID
        }

        if not allConfig[levelStructureID] then
            allConfig[levelStructureID] = {}
        end
        table.insert(allConfig[levelStructureID], converted)
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetSpecialPointConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerLevelSpecialPointConfig")
    local allConfig = {}

    for key, value in pairs (configTable or {}) do
        local converted = {
            specialPointID         = value.SpecialPointID, -- 特殊点ID
            specialPointType       = value.SpecialPointType, -- 特殊点类型 [1:主角出生 2:新手主角出生 3:无条件撤离 4:丢钱撤离 5:拉闸 6:需拉闸撤离 7:联通 8:纯对话 9:墙 10:spawn 11:门]
            
            -- 个别特殊点独有属性
            moneyDeduction         = value.MoneyDeduction, -- 仅对4生效
            triggeredPoint         = value.TriggeredPoint, -- 仅对5生效 [5激活6]
            transType              = value.TransType, -- 仅对7生效，对应联通类型ID
            spawnProb              = value.SpawnProb, -- 仅对10生效，对应spawn概型ID
            direction              = value.Direction, -- 仅对10生效
            doorAsset              = value.DoorAsset, -- 仅对11生效

            -- 所有特殊点通用属性
            info3                  = value.Info3, -- 特殊字段
            dialogue               = value.Dialogue, -- 对应对话ID
            completeTip            = value.CompleteTip, -- 完整交互后飘窗内容
            unableTip              = value.UnableTip, -- 无法执行功能时飘窗内容
        }

        allConfig[value.SpecialPointID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetLevelTransTypeConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerLevelTransTypeConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            transTypeID            = value.TransTypeID, -- Spawn概型ID
            targetLevel            = value.TargetLevel, -- 目标关卡ID

            targetX                = value.TargetX, -- 目标坐标X
            targetY                = value.TargetY, -- 目标坐标Y
        }

        allConfig[value.TransTypeID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetLevelSpawnProbConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerLevelSpawnProbConfig")
    local allConfig = {}

    for key, value in configTable:LessGCPairs() do
        local converted = {
            ID                     = value.ID, -- 无意义序号ID (其实是有意义的，spawnProbID不唯一)
            spawnProbID            = value.SpawnProbID, -- Spawn概型ID

            weight                 = value.Weight, -- 生成概率权重
            result                 = value.Result, -- 结果：生成的ItemID [需要去所有Item表查找]
        }

        allConfig[value.ID] = converted
    end
    
    return allConfig
end
--// end

--// Item表相关
ActivityMagicTowerConfigManager.GetPropConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerPropConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            propID                 = value.PropID, -- 道具ID
            attributeChange        = value.AttributeChange, -- 造成的属性变更ID

            -- Item表通用属性
            name                   = value.Name, -- 名字
            disappearTime          = value.DisappearTime, -- 交互几次后消失 [0：永远不消失] 

            disappearNote          = value.DisappearNote, -- 销毁后飘窗文本
            dialogue               = value.Dialogue, -- 触碰后触发对话ID
            disappearDialogue      = value.DisappearDialogue, -- 销毁后触发对话ID [如果同时有飘窗和对话，先对话再飘窗]
            smallDialogue          = value.SmallDialogue, -- 小对话ID

            portraitAsset          = value.PortraitAsset, -- 立绘资源ID
            assetSide              = value.AssetSide, -- 立绘位置 [1：左边 2：右边]

            itemAsset              = value.ItemAsset, -- 图上物品资源ID
            avatarIsset            = value.AvatarIsset, -- 头像资源ID
        }

        allConfig[value.PropID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetEnemyConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerEnemyConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            enemyID                = value.EnemyID, -- 怪物ID
            
            isVisible              = value.IsVisible, -- 是否局外显示
            
            enemyLevel             = value.EnemyLevel, -- 怪物等级
            health                 = value.Health, -- 生命值
            attack                 = value.Attack, -- 攻击力
            defense                = value.Defense, -- 防御力
            hitProb                = value.HitProb, -- 暴击率 [100以内的整数]

            isCompulsoryConfirming = value.IsCompulsoryConfirming, -- 是否强制二次确认接战 [0：否 1：是]
            confirmingText         = value.ConfirmingText, -- 强制二次确认接战提示文本
            isCompulsoryAnimation  = value.IsCompulsoryAnimation, -- 是否无法自动战斗 [0：否 1：是]

            spine                  = value.Spine, -- Spine动画ID
            defeatReward           = value.DefeatReward, -- 击败后奖励ID

            -- Item表通用属性
            name                   = value.Name,
            disappearTime          = value.DisappearTime, -- 交互几次后消失 [0：永远不消失] 

            disappearNote          = value.DisappearNote, -- 销毁后飘窗文本
            dialogue               = value.Dialogue, -- 触碰后触发对话ID
            disappearDialogue      = value.DisappearDialogue, -- 销毁后触发对话ID [如果同时有飘窗和对话，先对话再飘窗]
            smallDialogue          = value.SmallDialogue, -- 小对话ID

            portraitAsset          = value.PortraitAsset, -- 立绘资源ID
            assetSide              = value.AssetSide, -- 立绘位置 [1：左边 2：右边]

            itemAsset              = value.ItemAsset, -- 图上物品资源ID
            avatarIsset            = value.AvatarIsset, -- 头像资源ID
        }

        allConfig[value.EnemyID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetNPCConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerNPCConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            npcID                  = value.NPCID, -- NPCID
            
            tag                    = value.Tag, -- 标签
            shortDesc              = value.ShortDesc, -- 简介
            descOne                = value.DescOne, -- 一周目介绍
            descTwo                = value.DescTwo, -- 二周目介绍
            spine                  = value.Spine, -- Spine动画ID

            -- Item表通用属性
            name                   = value.Name,
            disappearTime          = value.DisappearTime, -- 交互几次后消失 [0：永远不消失] 

            disappearNote          = value.DisappearNote, -- 销毁后飘窗文本
            dialogue               = value.Dialogue, -- 触碰后触发对话ID
            disappearDialogue      = value.DisappearDialogue, -- 销毁后触发对话ID [如果同时有飘窗和对话，先对话再飘窗]
            smallDialogue          = value.SmallDialogue, -- 小对话ID

            portraitAsset          = value.PortraitAsset, -- 立绘资源ID
            assetSide              = value.AssetSide, -- 立绘位置 [1：左边 2：右边]

            itemAsset              = value.ItemAsset, -- 图上物品资源ID
            avatarIsset            = value.AvatarIsset, -- 头像资源ID

            isVisible              = value.IsVisible, -- 是否局外可见
            outAvatar              = value.OutAvatar, -- 局外头像路径
        }

        allConfig[value.NPCID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetHeroConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerHeroConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            heroID                 = value.HeroID, -- 主角ID
            
            health                 = value.Health, -- 初始生命值
            attack                 = value.Attack, -- 初始攻击力
            defense                = value.Defense, -- 初始防御力
            hitProb                = value.HitProb, -- 初始暴击率

            spine                  = value.Spine, -- Spine动画ID

            -- Item表通用属性
            name                   = value.Name,
            disappearTime          = value.DisappearTime, -- 交互几次后消失 [0：永远不消失] 

            disappearNote          = value.DisappearNote, -- 销毁后飘窗文本
            dialogue               = value.Dialogue, -- 触碰后触发对话ID
            disappearDialogue      = value.DisappearDialogue, -- 销毁后触发对话ID [如果同时有飘窗和对话，先对话再飘窗]
            smallDialogue          = value.SmallDialogue, -- 小对话ID

            portraitAsset          = value.PortraitAsset, -- 立绘资源ID
            assetSide              = value.AssetSide, -- 立绘位置 [1：左边 2：右边]

            itemAsset              = value.ItemAsset, -- 图上物品资源ID
            avatarIsset            = value.AvatarIsset, -- 头像资源ID
        }

        allConfig[value.HeroID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetTreasureConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerTreasureConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            treasureID             = value.TreasureID, -- 保险ID

            treasureProb           = value.TreasureProb, -- 保险概型ID
            treasureDesc           = value.TreasureDesc, -- 描述文本

            -- Item表通用属性
            name                   = value.Name,
            disappearTime          = value.DisappearTime, -- 交互几次后消失 [0：永远不消失] 

            disappearNote          = value.DisappearNote, -- 销毁后飘窗文本
            dialogue               = value.Dialogue, -- 触碰后触发对话ID
            disappearDialogue      = value.DisappearDialogue, -- 销毁后触发对话ID [如果同时有飘窗和对话，先对话再飘窗]
            smallDialogue          = value.SmallDialogue, -- 小对话ID

            portraitAsset          = value.PortraitAsset, -- 立绘资源ID
            assetSide              = value.AssetSide, -- 立绘位置 [1：左边 2：右边]

            itemAsset              = value.ItemAsset, -- 图上物品资源ID
            avatarIsset            = value.AvatarIsset, -- 头像资源ID
        }

        allConfig[value.TreasureID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetTreasureItemConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerTreasureItemConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            treasureItemID         = value.TreasureItemID, -- 保险物品ID
            attributeChange        = value.AttributeChange, -- 造成的属性变更ID

            name                   = value.Name, -- 名字
            showNumber             = value.ShowNumber, -- 显示数量 [不配则不显示，即1]
            connectedItem          = value.ConnectedItem, -- 关联DF游戏真正道具ID，用来获取图片资源

            showRarity             = value.ShowRarity, -- 以什么品级呈现
            claimNote              = value.ClaimNote, -- 拾取后触发的弹窗文本
            detectTime             = value.DetectTime, -- 搜索转圈时间 [s]
            bIsSearched            = false, -- 是否搜索了
        }

        allConfig[value.TreasureItemID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetTreasureProbConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerTreasureProbConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            ID                     = value.ID, -- 无意义序号ID
            treasureProbID         = value.TreasureProbID, -- 保险概型ID
            treasureItemID         = value.TreasureItemID, -- 保险物品ID

            group                  = value.Group, -- 组
            groupProb              = value.GroupProb, -- 组概率

            treasureItemWeight     = value.TreasureItemWeight, -- 保险物品权重
        }

        allConfig[value.ID] = converted
    end
    
    return allConfig
end
--// end

--// 对话表相关
ActivityMagicTowerConfigManager.GetSmallDialogueConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerSmallDialogueConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            smallDialogueID         = value.SmallDiaogueID, -- 小对话ID

            condition              = value.Condition, -- 前提条件ID
            isIndependent          = value.IsIndependent, -- 是否独立触发
            selfCancelRate         = value.SelfCancelRate, -- 自我否决概率

            weightDefault          = value.WeightDefault, -- 默认权重 [用于和视野中其他带有小对话的Item进行比较]
            weightPlayed           = value.WeightPlayed, -- 一旦被播放过一次，则权重改为此数
        }

        allConfig[value.SmallDiaogueID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetSmallDialogueLineConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerSmallDialogueLineConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            lineID                 = value.LineID, -- 小对话台词ID
            smallDialogueID        = value.SmallDialogueID, -- 小对话ID

            content                = value.Content, -- 台词内容
            duration               = value.Duration, -- 持续时间
            weight                 = value.Weight, -- 权重
            condition              = value.Condition, -- 前提条件ID
        }

        allConfig[value.LineID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetDialogueConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerDialogueConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            dialogueID             = value.DialogueID, -- 大对话ID

            condition              = value.Condition, -- 前提条件ID
            shopNPC                = value.ShopNPC, -- 绑定的 商人NPC ID
            shopSLot               = value.ShopSLot, -- 商人槽位
        }

        allConfig[value.DialogueID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetDialogueGroupConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerDialogueGroupConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            dialogueGroupID        = value.DialogueGroupID, -- 对话组ID
            belongingDialogue      = value.BelongingDialogue, -- 所属大对话ID

            startLine              = value.StartLine, -- 初始台词ID [对应台词链表头]
            condition              = value.Condition, -- 前提条件ID
        }

        allConfig[value.DialogueGroupID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetDialogueLineConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerDialogueLineConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            ID                     = value.ID, -- 台词ID
            nextLine               = value.NextLine, -- 下一台词ID

            cancelAction           = value.CancelAction, -- 是否取消接下来的交互 [0:否, 1:是]
            text                   = value.Text, -- 台词内容
            tipStart               = value.TipStart, -- 开始说话时飘窗内容

            killed                 = value.Killed, -- 说完后消灭的人，可以是npc，可以是怪物

            forcedSpeaker          = value.ForcedSpeaker, -- 强制说话者名字 [当说话者不为item实例时]
            speaker                = value.Speaker, -- 说话者ID

            attributeChange        = value.AttributeChange, -- 造成的属性变更ID

            answerBranch1          = value.AnswerBranch1, -- 说完后展开的回答选项1 ID
            answerBranch2          = value.AnswerBranch2, -- 说完后展开的回答选项2 ID
            answerBranch3          = value.AnswerBranch3, -- 说完后展开的回答选项3 ID
            answerBranch4          = value.Answerbranch4, -- 说完后展开的回答选项4 ID
            answerBranch5          = value.Answerbranch5, -- 说完后展开的回答选项5 ID
        }

        allConfig[value.ID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetAnswerBranchConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerAnswerBranchConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            ID                     = value.ID, -- 回答选项ID
            nextLine               = value.NextLine, -- 下一台词ID

            text                   = value.Text, -- 回答内容

            showCondition          = value.ShowCondition, -- 出现条件ID [不满足不出现]
            availableCondition     = value.AvailableCondition, -- 可用条件ID [不满足点了不生效]

            cycleType              = value.CycleType, -- 无下一句台词时的处理方式 [1：直接结束对话 2：重复问题，仍可选择]
            expDeducted            = value.ExpDeducted, -- 预计扣除战斗经验数量 [只用于显示，真正的扣除看属性变化配置]

            tip                    = value.Tip, -- 点击后飘窗内容
            conditionNotMetTip     = value.ConditionNotMetTip, -- availableCondition不满足时的飘窗

            attributeChangeCondition = value.AttributeChangeCondition, -- 作为条件的属性变化ID
            attributeChangeResult    = value.AttributeChangeResult, -- 作为结果的属性变化ID
        }

        allConfig[value.ID] = converted
    end
    
    return allConfig
end
--// end

--// 杂项表相关
ActivityMagicTowerConfigManager.GetAssetConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerAssetConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            assetID                = value.AssetID, -- 资源ID

            pcCDN                  = value.PCCDN, -- 端游资源cdn地址
            mobileCDN              = value.MobileCDN, -- 手游资源cdn地址

            pcDirectory            = value.PCDirectory, -- 端游资源本地路径
            mobileDirectory        = value.MobileDirectory, -- 手游资源本地路径

            downloadOrder          = value.DownloadOrder, -- 资源下载顺序 [越小越优先]
        }

        allConfig[value.AssetID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetAssetImgPath = function(assetID)
    local assetImgPath = ""
    local configTable = ActivityMagicTowerConfigManager.GetAssetConfigTable()
    local configData = assetID and tonumber(assetID) and configTable[tonumber(assetID)]

    if configData then        
        if IsHD() then
            if configData.pcCDN and configData.pcCDN ~= "" and configData.pcCDN ~= "-" and configData.pcCDN ~= "None" then
                assetImgPath = configData.pcCDN
            else
                assetImgPath = configData.pcDirectory
            end
        else
            if configData.mobileCDN and configData.mobileCDN ~= "" and configData.mobileCDN ~= "-" and configData.pcCDN ~= "None" then
                assetImgPath = configData.mobileCDN
            else
                assetImgPath = configData.mobileDirectory
            end
        end
    end

    return assetImgPath
end

ActivityMagicTowerConfigManager.GetSpineConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerSpineConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            spineID                = value.SpineID, -- SpineID

            directoryAtalas        = value.DirectoryAtalas, -- 图集文件路径
            directorySkeleton      = value.DirectorySkeleton, -- 骨骼文件路径

            isLoopRelax            = value.IsLoopRelax, -- 是否特殊填loop [0：正常填对应的loop 1：loop写Relax]
        }

        allConfig[value.SpineID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetSpineAtalasAndSkeleton = function(spineID)
    local atalas = ""
    local skeleton = ""
    local configTable = ActivityMagicTowerConfigManager.GetSpineConfigTable()
    local configData = spineID and tonumber(spineID) and configTable[tonumber(spineID)]

    if configData then
        atalas = configData.directoryAtalas or ""
        skeleton = configData.directorySkeleton or ""
    end

    return atalas, skeleton
end

ActivityMagicTowerConfigManager.GetAttributeChangeConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerAttributeChangeConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            attributeChangeID      = value.AttributeChangeID, -- 属性变化ID

            changeType             = value.ChangeType, -- 变化类型 [0：标准变化] (暂未使用)

            healthChangeWay        = value.HealthChangeWay, -- 生命变化方式 [1：+= 2：-= 3：*= 4：=]
            healthChangeValue      = value.HealthChangeValue, -- 生命改变参数

            attackChangeWay        = value.AttackChangeWay, -- 攻击变化方式 [1：+= 2：-= 3：*= 4：=]
            attackChangeValue      = value.AttackChangeValue, -- 攻击改变参数

            defenseChangeWay       = value.DefenseChangeWay, -- 防御变化方式 [1：+= 2：-= 3：*= 4：=]
            defenseChangeValue     = value.DefenseChangeValue, -- 防御改变参数

            hitProbChangeWay       = value.HitProbChangeWay, -- 爆率变化方式 [1：+= 2：-= 3：*= 4：=]
            hitProbChangeValue     = value.HitProbChangeValue, -- 爆率改变参数

            expChangeWay           = value.ExpChangeWay, -- 经验变化方式 [1：+= 2：-= 3：*= 4：=]
            expChangeValue         = value.ExpChangeValue, -- 经验改变参数

            keyChangeWay           = value.KeyChangeWay, -- 钥匙数量变化方式 [1：+= 2：-= 3：*= 4：=]
            keyChangeValue         = value.KeyChangeValue, -- 钥匙数量改变参数

            moneyChangeWay         = value.MoneyChangeWay, -- 龙门币变化方式 [1：+= 2：-= 3：*= 4：=]
            moneyChangeValue       = value.MoneyChangeValue, -- 龙门币改变参数
        }

        allConfig[value.AttributeChangeID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetDeathLinesConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerDeathLinesConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            lineID                 = value.LineID, -- 台词ID

            lineTitle              = value.LineTitle, -- 标题台词内容
            lineDesc               = value.LineDesc, -- 名人名言内容 [只有死亡结局才会出现]
            killer                 = value.Killer, -- 击杀者ID [如果同一个 enemyID 有多人，则随机选一个; 0:死于谁都可以 1：自己退出]
            evacuateCondition      = value.EvacuateCondition, -- 通关情况 [1：普通撤离 2：一周目通关 3：二周目通关]
        }

        allConfig[value.LineID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetCutSceneConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerCutSceneConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            lineID                 = value.LineID, -- 单句台词ID [鼠标每点击以下，或者倒计时到位，触发下一句]
            paragraphID            = value.ParagraphID, -- 单页台词ID [单页内的句子全部结束后，则换页]
            sceneID                = value.SceneID, -- 整个一套台词的ID

            endurance              = value.Endurance, -- 本句倒计时时间 [s]
            content                = value.Content, -- 本句内容
        }

        allConfig[value.LineID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetConstantConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerConstantConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            constantID             = value.ConstantID, -- 常量ID
            constantValue          = value.ConstantValue, -- 常量值
        }

        allConfig[value.ConstantID] = converted
    end
    
    return allConfig
end

--// end

--// 条件表相关
ActivityMagicTowerConfigManager.GetConditionConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerConditionConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            conditionID             = value.ConditionID, -- 条件ID
            subConditionCombination = value.SubConditionCombination, -- 子条件组合方式 [((子条件id1)&(子条件id2)||(子条件id3))]
        }

        allConfig[value.ConditionID] = converted
    end
    
    return allConfig
end

ActivityMagicTowerConfigManager.GetSubConditionConfigTable = function()
    local configTable = Facade.TableManager:GetTable("MorgenTowerSubConditionConfig")
    local allConfig = {}

    for key, value in pairs(configTable or {}) do
        local converted = {
            subConditionID         = value.SubConditionID, -- 子条件ID
            subConditionType       = value.SubConditionType, -- 子条件类型 [1：某属性在某范围内 2：与某Item交互完成次数(仅触发不算) 3：与某SpecialPoint交互完成次数(仅触发不算) 4：进入过某Level 5：执行过某大对话分支回答]

            targetAttribute        = value.TargetAttribute, -- 仅对1生效，属性类型
            attributeRange         = value.AttributeRange, -- 仅对1生效，属性范围

            itemID                 = value.ItemID, -- 仅对2生效，ItemID
            itemInteractTime       = value.ItemInteractTime, -- 仅对2生效，要求次数范围

            specialPointID         = value.SpecialPointID, -- 仅对3生效，SpecialPointID
            specialPointInteractTime = value.SpecialPointInteractTime, -- 仅对3生效，要求次数范围

            levelID                = value.LevelID, -- 仅对4生效，对应LevelID

            answerBranchID         = value.AnswerBranchID, -- 仅对5生效，大对话分支ID
        }

        allConfig[value.SubConditionID] = converted
    end
    
    return allConfig
end

--// end

return ActivityMagicTowerConfigManager