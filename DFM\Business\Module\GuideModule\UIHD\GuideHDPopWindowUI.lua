----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGuide)
----- LOG FUNCTION AUTO GENERATE END -----------



local InputSummaryItemHD = require "DFM.Business.Module.CommonBarModule.UI.BottomBarHD.InputSummaryItemHD"
local GuideConfig = require "DFM.Business.Module.GuideModule.GuideConfig"
local UGPInputHelper = import("GPInputHelper")

-- BEGIN MODIFICATION @ VIRTUOS :
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import "EGPInputType"

local GuideUILogic = require "DFM.Business.Module.GuideModule.Logic.GuideUILogic"

---@class GuideHDPopWindowUI : LuaUIBaseView
local GuideHDPopWindowUI = ui("GuideHDPopWindowUI")

local log = function(...) loginfo("[GuideHDPopWindowUI]", ...) end
local warn = function(...) logwarning("[GuideHDPopWindowUI]", ...) end
local err = function(...) logerror("[GuideHDPopWindowUI]", ...) end

function GuideHDPopWindowUI:Ctor()
    self._wtTitle = self:Wnd("wtTitle", UITextBlock)
    self._wtContentImg = self:Wnd("wtContentImg", UIImage)
    self._wtMediaImage = self:Wnd("wtMediaImage", UIMediaImage)
    self._wtDesc = self:Wnd("wtDesc", UITextBlock)
    self._wtTabSummary = self:Wnd("WBP_TopBarHD_InputSummary", InputSummaryItemHD)
    self._wtTabSummary:EnableAutoSetVisibility(false)

    self._wtNamedSlot = self:Wnd("wtNamedSlot", UIWidgetBase)

    self._wtBtnPre = self:Wnd("wtBtnPre", UIButton)
    self._wtBtnPre:Event("OnClicked", self._OnBtnPreClick, self)
    self._wtBtnNext = self:Wnd("wtBtnNext", UIButton)
    self._wtBtnNext:Event("OnClicked", self._OnBtnNextClick, self)
    self._wtDotBox = self:Wnd("wtDotWrap", UILightWidget)
    self._DotIns = {}

    self._wtGamepadSwitchIcon = self:Wnd("WBP_CommonKeyIconBox", UIWidgetBase)

    self:Reset()
end

--==================================================
--region Life function
function GuideHDPopWindowUI:Reset()
    self._showData = nil
    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
    end
    self._bSelfActive = false
    self._videoName = nil
    self._callbackOnEnd = nil
    self._curIdx = 1
    self._bWaitImgLoad = false
    self._bEnableSelfInput = true
end

function GuideHDPopWindowUI:RecycleSelf()
    self:Hide()
    if self._bSelfActive then
        Module.Guide.Field:ReturnCacheSubUI(UIName2ID.GuideHDPopWindowUI, self)
        self._bSelfActive = false
    end
end

function GuideHDPopWindowUI:OnOpen()
end

function GuideHDPopWindowUI:OnClose()
    self._bClose = true

    self:_StopMedia()
    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
        Module.Guide:SetGuideInputGate("GuideHDPopWindowUI", false)
    end

    -- Module.Guide.Field:SetInputStateInGame("GuideHDPopWindowUI", false)
end

function GuideHDPopWindowUI:SetData(showData)
    if not showData then return end
    ---@type GuidePopWindowUIShowData
    self._showData = showData
    self._callbackOnEnd = showData.callbackOnEnd
end

function GuideHDPopWindowUI:OnShowBegin()
    self:_StopMedia()
    self._bSelfActive = true
    self:_InitDots()
    self:_UpdateShow()
    self:_PreloadResources(1, #self._showData.descInfo)

    -- self._wtTabSummary:SetData("GuideSkip")

    self._hActionGuideSkip = self:AddInputActionBinding(
        "GuideSkip",
        EInputEvent.IE_Pressed,
        self._OnSkipBtnClick,
        self,
        EDisplayInputActionPriority.UI_Guide
    )

    self:_EnableGamepadFeature()

    Module.Guide:SetGuideInputGate("GuideHDPopWindowUI", true)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDPopWindowUI", true, false)
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true)
    end

    Facade.SoundManager:PlayUIAudioEvent(DFMAudioRes.UI_Tutorial_Popup_Large)

    self:SetRenderOpacity(Module.Guide:GetGuideUIState(UIName2ID.GuideHDPopWindowUI) and 1 or 0)



    -- Module.Guide.Field:SetInputStateInGame("GuideHDPopWindowUI", true)
    self:_PlayAudio()
end

--#region gamepad feature
function GuideHDPopWindowUI:_EnableGamepadFeature()
    if not self._OnNotifyInputTypeChangedHandle then
        self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(
        self._OnInputTypeChanged, self))
        local curInputType = WidgetUtil.GetCurrentInputType()
        self:_OnInputTypeChanged(curInputType)
    end

    if not self._ThumbStickRight then
        self._ThumbStickRight = self:AddInputActionBinding("GuideHDPopWindow_Right_Gamepad", EInputEvent.IE_Pressed,
            self._OnBtnNextClick, self, EDisplayInputActionPriority.UI_Guide)
    end
    if not self._ThumbStickLeft then
        self._ThumbStickLeft = self:AddInputActionBinding("GuideHDPopWindow_Left_Gamepad", EInputEvent.IE_Pressed,
            self._OnBtnPreClick, self, EDisplayInputActionPriority.UI_Guide)
    end

    -- BEGIN MODIFICATION @ VIRTUOS :
    -- 移除导航Accept配置以修复问题：鼠标点击左右箭头后，UI导航聚集在button后，导航的Accept输入屏蔽了UI按键输入。
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
    -- END MODIFICATION
end

function GuideHDPopWindowUI:_DisableGamepadFeature()
    if not IsHD() then
        return
    end

    if self._OnNotifyInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
        self._OnNotifyInputTypeChangedHandle = nil
    end

    if self._ThumbStickRight then
        self:RemoveInputActionBinding(self._ThumbStickRight)
        self._ThumbStickRight = nil
    end
    if self._ThumbStickLeft then
        self:RemoveInputActionBinding(self._ThumbStickLeft)
        self._ThumbStickLeft = nil
    end

    if self._wtNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtNavGroup = nil
    end

    -- BEGIN MODIFICATION @ VIRTUOS :
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    -- END MODIFICATIO7
end

function GuideHDPopWindowUI:_OnInputTypeChanged(inputType)
    if IsHD() then
        self:_UpdateShow()
    end
end

--endregion


function GuideHDPopWindowUI:OnHide()
    self:_StopMedia()
    GuideUILogic.GetPopWindowStub():ClearAllResRefs()

    self:RemoveAllLuaEvent()
    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
        Module.Guide:SetGuideInputGate("GuideHDPopWindowUI", false)
    end

    -- Module.Guide.Field:SetInputStateInGame("GuideHDPopWindowUI", false)
    UGPInputHelper.WantInputMode_UIOnly(GetGameInstance(), "GuideHDPopWindowUI", false, false)
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
    end

    self:_DisableGamepadFeature()
end

function GuideHDPopWindowUI:OnInitExtraData(showData)
    self:SetData(showData)
end

function GuideHDPopWindowUI:_UpdateShow()
    self._wtMediaImage:SetVisibility(ESlateVisibility.Collapsed)
    self._wtContentImg:SetVisibility(ESlateVisibility.Collapsed)
    self._wtNamedSlot:SetVisibility(ESlateVisibility.Collapsed)
    self._wtNamedSlot:ClearChildren()
    self._bWaitImgLoad = false

    if not self._showData then return end

    if not self._showData.descInfo then
        return
    end
    log("GuidePopWindowUI:_UpdateShow()", self._curIdx, #self._showData.descInfo)
    if self._curIdx < 1 or self._curIdx > #self._showData.descInfo then
        return
    end

    self:_StopMedia()

    if IsHD() and WidgetUtil.IsGamepad() then
        if not self._wtNavGroup then
            self._wtNavGroup = WidgetUtil.RegisterNavigationGroup(self, self, "Hittest")
            if self._wtNavGroup then
                self._wtNavGroup:MarkIsStackControlGroup()
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtNavGroup)
        end
    end


    local descInfo = self._showData.descInfo[self._curIdx]
    self._wtTitle:SetText(descInfo.title)
    if descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Image then
        -- 仅图片
        self:_LoadImage(descInfo)
    elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Video then
        -- 播视频
        -- local videoConfig = GuideConfig.TableGuideVideoConfig[descInfo.exContentParams[1]]
        local videoName = descInfo.exContentParams[1]
        log("GuidePopWindowUI show video ", videoName)
        self._wtMediaImage:Visible()
        self._videoName = videoName
        self:_PlayMedia()
    elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.SubWBP then
        local subWbpTable = GuideConfig.TableGuideWBPConfig[descInfo.exContentParams[1]]
        if subWbpTable then
            loginfo("_UpdateShow SubWBP", subWbpTable.WBPPath.AssetPathName)
            -- local bpSoftClass  = subWbpTable.WBPPath
            -- self:UseSlotAsChildBp(bpSoftClass)
            GuideUILogic.CreateUI(self, subWbpTable.WBPPath, function(uiIns)
                if hasdestroy(uiIns) then
                    err("_LoadSubBP in creation uiIns is destroyed")
                    return
                end
                self._wtNamedSlot:SetVisibility(ESlateVisibility.Visible)
                self._wtNamedSlot:AddChild(uiIns)
            end)
        end
    else
        assert(false, "please contact developer for supportting...")
    end
    self._wtDesc:SetText(descInfo.descText)

    self:_UpdateDotsAndBtn()
end

function GuideHDPopWindowUI:_PreloadResources(l, r)
    local assetPaths = {}

    local insertSubWBP = function(subWbpTableIdx)
        local subWbpTable = GuideConfig.TableGuideWBPConfig[subWbpTableIdx]
        if subWbpTable then
            log("preload sub ui", subWbpTable.WBPPath.AssetPathName)
            local path = subWbpTable.WBPPath.AssetPathName .. "_C"
            table.insert(assetPaths, path)
        else
            err("could not find subWBP config", subWbpTableIdx)
        end
    end
    local tryInsertImage = function(imagePath)
        if imagePath and imagePath ~= "" then
            table.insert(assetPaths, imagePath)
        else
            warn("GuidePopWindowUI:_PreloadShowResources imagePath is nil or empty")
        end
    end


    for idx = l, r do
        local descInfo = self._showData.descInfo[idx]
        if descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Image then
            tryInsertImage(FLuaHelper.SoftObjectPtrToString(descInfo.Image))
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.Video then
            -- do nothing for now (cahce checking if streaming video)
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.SubWBP then
            insertSubWBP(descInfo.exContentParams[1])
        elseif descInfo.exContentType == GuideConfig.EGuidePopWindowItemExContentType.FullScreenVideo then
            tryInsertImage(FLuaHelper.SoftObjectPtrToString(descInfo.Image))
        end
    end

    Facade.ResourceManager:AsyncLoadResources(
        GuideUILogic.GetPopWindowStub(),
        assetPaths,
        function(mapPath2ResIns)
            for path, resIns in pairs(mapPath2ResIns) do
                if hasdestroy(resIns) then
                    warn("PreloadResources failed, resource is invalid", path)
                else
                    log("PreloadResources success", path)
                end
            end
        end
    )
end

---@param descInfo GuidePopWindowShowData_DescInfo
function GuideHDPopWindowUI:_LoadImage(descInfo)
    self._bWaitImgLoad = true
    self._wtContentImg:SetVisibility(ESlateVisibility.Collapsed)

    local descImage = FLuaHelper.SoftObjectPtrToString(descInfo.Image)
    local bAutoResize = true

    local function OnImageLoadFinished(imageAsset)
        -- loginfo("GuidePopWindowUI finish load img")
        if not hasdestroy(imageAsset) then
            local classname = LAI.GetObjectClassName(imageAsset)
            if classname == "PaperSprite" then
                self._wtContentImg:SetBrushFromAtlasInterface(imageAsset, false)
                if bAutoResize then
                    self._wtContentImg:SetBrushSize(imageAsset.BakedSourceDimension)
                end
            else
                self._wtContentImg:SetBrushFromTexture(imageAsset, false)
            end
            if self._bWaitImgLoad then
                self._wtContentImg:SetVisibility(ESlateVisibility.Visible)
            else
                logwarning("GuidePopWindowUI img finish load,but not show now", self._curIdx)
            end
        end
        self._bWaitImgLoad = false
    end

    -- use preload's stub intead of stubUIImages
    -- ResImageUtil.StaticAsyncLoadImgObjByPath(descImage, true, OnImageLoadFinished)
    Facade.ResourceManager:AsyncLoadResource(GuideUILogic.GetPopWindowStub(), descImage, function(mapPath2ResIns)
        local imageAsset = mapPath2ResIns[descImage]
        OnImageLoadFinished(imageAsset)
    end)
end

function GuideHDPopWindowUI:_OnSkipBtnClick()
    if not self._bEnableSelfInput then
        loginfo("GuideHDPopWindowUI:_OnSkipBtnClick, cur disable self input")
        -- return
    end

    if self._showData and self._showData.descInfo and self._curIdx + 1 <= #self._showData.descInfo then
        self._curIdx = self._curIdx + 1
        self:_UpdateShow()
        self:PlayWidgetAnim(self.Amina_qiehuan)
        return
    end

    if self._callbackOnEnd then
        self._callbackOnEnd(GuideConfig.EPopWindowUICloseReason.CloseBtnClicked)
        return
    end

    self:RecycleSelf()
end

function GuideHDPopWindowUI:_OnMediaPlayEnd()
    self._wtMediaImage.OnMediaPlayEnd:Clear()
    self:_PlayMedia()
end

function GuideHDPopWindowUI:_PlayMedia()
    if not self._videoName then return end
    self._wtMediaImage:Play(self._videoName)
end

function GuideHDPopWindowUI:_StopMedia()
    if not self._videoName then return end
    self._wtMediaImage:StopMedia(true)
    self._videoName = nil
end

function GuideHDPopWindowUI:_OnBtnNextClick()
    if self._showData and self._showData.descInfo then
        if self._curIdx >= #self._showData.descInfo then
            return
        end

        self._curIdx = self._curIdx + 1
        self:_UpdateShow()
        self:PlayWidgetAnim(self.Amina_qiehuan)
    end
end

function GuideHDPopWindowUI:_OnBtnPreClick()
    if self._showData and self._showData.descInfo then
        if self._curIdx < 2 then
            return
        end

        self._curIdx = self._curIdx - 1
        self:_UpdateShow()
        self:PlayWidgetAnim(self.Amina_qiehuan)
    end
end

function GuideHDPopWindowUI:_InitDots()
    self._DotIns = {}
    self._wtDotBox:ClearChildren()
    Facade.UIManager:ClearSubUIByParent(self, self._wtDotBox)
    self._wtDotBox:SetVisibility(ESlateVisibility.Collapsed)
    self._wtBtnNext:SetVisibility(ESlateVisibility.Collapsed)
    self._wtBtnPre:SetVisibility(ESlateVisibility.Collapsed)

    if not self._showData or not self._showData.descInfo or #self._showData.descInfo <= 1 then
        return
    end

    self._wtDotBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    local len = #self._showData.descInfo

    for i = 1, len do
        local weakUIIns = Facade.UIManager:AddSubUI(self, UIName2ID.DFButtonCarousel2, self._wtDotBox)
        local ins = getfromweak(weakUIIns)
        if ins then
            table.insert(self._DotIns, weakUIIns)
            if IsHD() then
                ins:Event("OnClicked", self._OnClickedDot, self, i)
            end
        end
    end
end

function GuideHDPopWindowUI:_UpdateDotsAndBtn()
    self._wtGamepadSwitchIcon:Hidden()

    if self._showData and self._showData.descInfo then
        local totalNum = #self._showData.descInfo
        self._wtTabSummary:SetData(self._curIdx >= totalNum and "GuideSkip" or "GuideNext")
        -- self._wtTabSummary:SetData(self._curIdx >= totalNum and "GuideSkip" or "GuideSkip")

        if WidgetUtil.IsGamepad() and totalNum > 1 then
            self._wtGamepadSwitchIcon:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
            self._wtGamepadSwitchIcon:InitByDisplayInputActionName("GuideHDPopWindow_RightStick_Gamepad", true, 0, false)
        else
            self._wtGamepadSwitchIcon:Hidden()
        end



        for idx, dotWeakIns in ipairs(self._DotIns) do
            local dotIns = getfromweak(dotWeakIns)
            if idx == self._curIdx then
                dotIns:SetStyle(1)
            else
                dotIns:SetStyle(0)
            end
        end

        self._wtBtnNext:SetVisibility(self._curIdx >= totalNum and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
        self._wtBtnPre:SetVisibility(self._curIdx < 2 and ESlateVisibility.Collapsed or ESlateVisibility.Visible)
    end
end

function GuideHDPopWindowUI:_OnClickedDot(idx)
    if self._showData and self._showData.descInfo then
        if idx > #self._showData.descInfo or idx < 1 then
            return
        end

        self._curIdx = idx
        self:_UpdateShow()
        self:PlayWidgetAnim(self.Amina_qiehuan)
    end
end

function GuideHDPopWindowUI:_PlayAudio()
    if not self._showData then return end
    local audioEvent = self._showData.audio
    if audioEvent then
        Facade.SoundManager:PlayGuideAudio(audioEvent)
    end
end

function GuideHDPopWindowUI:SetInputState(bEnableSelfInput)
    loginfo("GuideHDPopWindowUI:SetInputState", bEnableSelfInput, self._bSelfActive, self._bEnableSelfInput)
    if self._bSelfActive then
        self._bEnableSelfInput = bEnableSelfInput
        Module.Guide:SetGuideInputGate("GuideHDPopWindowUI", bEnableSelfInput)

        if bEnableSelfInput then
            self:_RemoveInputBinding()
            self._hActionGuideSkip = self:AddInputActionBinding(
                "GuideSkip",
                EInputEvent.IE_Pressed,
                self._OnSkipBtnClick,
                self,
                EDisplayInputActionPriority.UI_Guide
            )
        else
            self:_RemoveInputBinding()
        end
    end
end

function GuideHDPopWindowUI:_RemoveInputBinding()
    if self._hActionGuideSkip then
        self:RemoveInputActionBinding(self._hActionGuideSkip)
        self._hActionGuideSkip = nil
    end
end

return GuideHDPopWindowUI
