----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMReward)
----- LOG FUNCTION AUTO GENERATE END -----------



local RewardViewLogic = {}
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local HeroHelperTool = require "DFM.StandaloneLua.BusinessTool.HeroHelperTool"
local RewardConfig   = require "DFM.Business.Module.RewardModule.RewardConfig"
local SettlementDefine = require "DFM.Business.DataStruct.SettlementStruct.SettlementDefine"
local WeaponAssemblyTool = require "DFM.StandaloneLua.BusinessTool.StructTool.WeaponAssemblyTool"
local ThemeHelperTool = require "DFM.StandaloneLua.BusinessTool.ThemeHelperTool"
--------------------------------------------------------------------------
--- 通用奖励界面
--------------------------------------------------------------------------
RewardViewLogic.ShowRewardPanel = function(title, tip, items, qualities, bShowOnlyInMP, bUseCustomSort, backgroundType)
    if (RewardViewLogic.CanShowReward() 
        or Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.SOL 
        or Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.MP
        or Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.Arena)
        and (bShowOnlyInMP ~= true or (bShowOnlyInMP == true and RewardViewLogic.IsInMp())) then
        if items and #items > 0 then
            local conversion, filterItems = RewardViewLogic.ExtractConversion(items)
            local bNeedThemeStyle = false
            for index, item in ipairs(items) do
                if ThemeHelperTool.CheckIsThemeItem(item) then
                    bNeedThemeStyle = true
                    break
                end
            end
            if bUseCustomSort ~= true then
                table.sort(filterItems, RewardViewLogic.UniversalSort)
            end
            local itemsGroups = {}
            declare_if_nil(itemsGroups, 0, {})
            declare_if_nil(itemsGroups[0], "items", filterItems)
            declare_if_nil(itemsGroups[0], "title", "")
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.Reward, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(bNeedThemeStyle and UIName2ID.RewardPanel_ThemeStyle1 or UIName2ID.RewardPanel, nil, nil, title, tip, itemsGroups, nil, nil, conversion, backgroundType)
                return popHandle
            end)

        end
    else
        local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.Reward)
        if oldCache then
            --检查本地缓存已有的数据
            local keepOldItems = {}
            local bFound
            if oldCache[4] and oldCache[4] > 0 then
                for i = 9, 8 + oldCache[4]*3, 3 do
                    bFound = false
                    for index, item in ipairs(items) do
                        if oldCache[i] == item.id then
                            bFound = true
                            break
                        end
                    end
                    if not bFound then
                        table.insert(items, {id=oldCache[i], num=oldCache[i+1], weaponId=oldCache[i+2]})
                    end
                end
            end
            bFound = false
            if oldCache[5] and oldCache[5] > 0 then
                for i = 9 + oldCache[4]*3, 8 + oldCache[4]*3 + oldCache[5]*2, 2 do
                    bFound = false
                    for index, quality in ipairs(qualities) do
                        if oldCache[i] == quality.id then
                            bFound = true
                            break
                        end
                    end
                    if not bFound then
                        table.insert(qualities, {id=oldCache[i], num=oldCache[i+1]})
                    end
                end
            end
        end
        local cacheList = {}
        table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.Reward)
        table.insert(cacheList, title)
        table.insert(cacheList, tip)
        table.insert(cacheList, items ~= nil and #items or 0)
        table.insert(cacheList, qualities ~= nil and #qualities or 0)
        table.insert(cacheList, bShowOnlyInMP and 1 or 0)
        table.insert(cacheList, bUseCustomSort and 1 or 0)
        table.insert(cacheList, backgroundType)
        if items ~= nil then
            for index, item in ipairs(items) do
                table.insert(cacheList, item.id)
                table.insert(cacheList, item.num)
                table.insert(cacheList, item.weaponId)
            end
        end
        if qualities ~= nil then
            for index, quality in ipairs(qualities) do
                table.insert(cacheList, quality.id)
                table.insert(cacheList, quality.num)
            end
        end
        RewardViewLogic.AddCachePopAction(cacheList)
    end
end

RewardViewLogic.ShowSafehouseLevelUpRewardPanel = function(deviceId, fCallbackIns)
    if deviceId ~= nil then
        if RewardViewLogic.CanShowReward() and Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            local deviceData = Module.BlackSite:GetDeviceData(deviceId)
            if isvalid(deviceData) then
                local currentLevel = deviceData:GetLevel() or 1
                local preLevel = currentLevel - 1
                local productionLine = deviceData:GetCanProduce() and deviceData:GetLevelProduceList(currentLevel) or {}
                local unlockNum = table.nums(productionLine)
                local infoList = {}
                local itemList = {}
                local MallInfo = Facade.TableManager:GetTable("Mall")
                local MallInfoRecord
                local item
                if unlockNum > 0 then
                    local mallEntry
                    for index, productionLine in pairs(productionLine) do
                        for _, Product in pairs(productionLine:GetProductList()) do
                            item = ItemBase:New(Product.Id)
                            mallEntry = nil
                            for key, entry in pairs(MallInfo) do
                                if entry.ItemId == Product.Id then
                                    mallEntry = entry
                                    break
                                end
                            end
                            item.order = mallEntry and mallEntry.order or -1
                            table.insert(itemList, item)
                        end
                    end
                end
                if #itemList > 0 then
                    table.sort(itemList, RewardViewLogic.QualitySort)
                    table.insert(infoList,  {IconPath="",Description=string.format(Module.Reward.Config.Loc.ProductionFormulaUnlocked, string.format("%02d",itemList[1].quality-1), itemList[1].name)})
                end
                if not deviceData:GetCanProduce() then
                    local gainDescList = deviceData:GetLevelUnlockGainDescriptionList(currentLevel)
                    table.append(infoList, gainDescList)
                end
                if #infoList > 0 then
                    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.SafehouseLevelUpReward, function ()
                        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.SafehouseLevelUpRewardPanel, fCallbackIns, nil, deviceData, infoList, itemList)
                        return popHandle
                    end)
                end
            end
        else
            RewardViewLogic.AddCachePopAction({Module.Reward.Config.EShowPopPanelType.SafehouseLevelUpReward, deviceId})
            if fCallbackIns then
                fCallbackIns()
            end
        end
    end
end



RewardViewLogic.ShowModuleUnlockPanel = function(moduleId)
    if moduleId ~= nil then
        if RewardViewLogic.CanShowReward() and Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.SafeHouse then
            if moduleId == SwitchModuleID.ModuleABTLobbyMP then 
                if DFHD_LUA == 1 then
                    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.ModuleUnlock, function ()
                        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.BattleFieldUnlockPanel, nil, nil)
                        return popHandle
                    end) 
                end
            else
                local moduleUnlockConfig = Facade.TableManager:GetTable("Module/ModuleUnlockConfig")
                for key, moduleUnlockConfigRow in pairs(moduleUnlockConfig) do
                    if moduleUnlockConfigRow.ID == tonumber(moduleId) then
                        local newUnlock = {}
                        newUnlock.description = moduleUnlockConfigRow.description
                        newUnlock.iconPath = moduleUnlockConfigRow.iconPath
                        newUnlock.unlockId = moduleUnlockConfigRow.ID
                        Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.ModuleUnlock, function ()
                            local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.LevelUpUnlockPanel, nil, nil, 1, newUnlock , nil)
                            return popHandle
                        end)
                        break
                    end
                end
            end
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.ModuleUnlock)
            local moduleNum = 1
            local moduleIds = {}
            if oldCache and oldCache[2] then
                moduleNum = oldCache[2]
                for i = 3, 2 + moduleNum, 1 do
                    if oldCache[i] == nil then
                        break
                    else
                        table.insert(moduleIds, oldCache[i]) 
                        moduleNum = moduleNum + 1
                    end
                end
            end
            table.insert(moduleIds, moduleId)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.ModuleUnlock)
            table.insert(cacheList, moduleNum)
            table.append(cacheList, moduleIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end


RewardViewLogic.ShowBHDUnlockPop = function()
    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.ModuleUnlock, function ()
        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.BHDUnlockPop)
        return popHandle
    end)
end


RewardViewLogic.ShowReputationLevelUpPanel = function(unlockedItems, merchantId)
    if unlockedItems ~= nil and merchantId ~= nil then
        local merchantStruct = Server.ShopServer:GetMerchantStructById(merchantId)
        if merchantStruct and merchantStruct.traderConfig then
            local prestigeData = merchantStruct:GetPrestigeData()
            if prestigeData.level > 1 then
                if RewardViewLogic.CanShowReward() then
                    table.sort(unlockedItems, RewardViewLogic.PriceSort)
                    local MallInfo = Facade.TableManager:GetTable("Mall")
                    local itemsInMall = {}
                    local mallEntry = nil
                    for id, item in ipairs(unlockedItems) do
                        mallEntry = nil
                        for key, entry in pairs(MallInfo) do
                            if entry.ItemId == item.id then
                                mallEntry = entry
                                break
                            end
                        end
                        if mallEntry ~= nil then
                            item.order = mallEntry.order
                            table.insert(itemsInMall, item)
                        end
                    end
                    unlockedItems = itemsInMall
                    table.sort(unlockedItems, RewardViewLogic.OrderSort)
                    local unlockedInfo = {}
                    local infoText
                    for id, item in ipairs(unlockedItems) do
                        infoText = string.format(Module.Reward.Config.Loc.MerchandiseUnlocked, item.name or Module.Reward.Config.Loc.Unknown) 
                        table.insert(unlockedInfo, {Description=infoText})   
                    end
                    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.ReputationLevelUp, function ()
                        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.ReputationLevelUpPanel, nil, nil, merchantStruct, unlockedInfo)
                        return popHandle
                    end)
                else
                    local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.ReputationLevelUp)
                    if oldCache then
                        --检查本地缓存已有的数据
                        if oldCache[3] and tonumber(oldCache[3]) == merchantId then
                            local bFound
                            if oldCache[2] and oldCache[2] > 0 then
                                for i = 4, 3 + oldCache[2]*2, 2 do
                                    bFound = false
                                    for index, item in ipairs(unlockedItems) do
                                        if oldCache[i] == item.id then
                                            bFound = true
                                            break
                                        end
                                    end
                                    if not bFound then
                                        table.insert(unlockedItems, {id=oldCache[i], num=oldCache[i+1]})
                                    end
                                end
                            end
                        end
                    end
                    local cacheList = {}
                    table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.ReputationLevelUp)
                    table.insert(cacheList, unlockedItems ~= nil and #unlockedItems or 0)
                    table.insert(cacheList, merchantId)
                    if unlockedItems ~= nil then
                        for index, item in ipairs(unlockedItems) do
                            table.insert(cacheList, item.id)
                            table.insert(cacheList, item.num)
                        end
                    end
                    RewardViewLogic.AddCachePopAction(cacheList)
                end
            end
        end
    end
end


--------------------------------------------------------------------------
--- 系统开启界面
--------------------------------------------------------------------------
RewardViewLogic.ShowSystemOpenPanelProcess = function(title, name, systemImg, fCloseCb, questLineInfo)
    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.SystemOpen, function ()
        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.SystemOpenPanel, nil, nil, title, name, systemImg, fCloseCb, questLineInfo)
        Module.Reward.Field:AddSyetemOpenHandle(popHandle)
        return popHandle
    end)
end

RewardViewLogic.ShowQuestDetailOpenPanelProcess = function (title, fCloseCb, questInfos)
    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.QuestDetailOpen, function ()
        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.QuestDetailOpenPanel, nil, nil, title, fCloseCb, questInfos)
        return popHandle
    end)
end

RewardViewLogic.CloseSystemOpenPanelProcess = function()
    local sysOpenPanelHandle = Module.Reward.Field:PopSyetemOpenHandle()
    if sysOpenPanelHandle then
        Facade.UIManager:CloseUIByHandle(sysOpenPanelHandle)
    end
end

RewardViewLogic.ShowSOLPathOfGrowthLevelUpPanel = function(seasonLv, preSeasonLv)
    loginfo("azhengzheng:Server.RoleInfoServer.deltaExp is " .. Server.RoleInfoServer.deltaExp)
    if seasonLv ~= nil and preSeasonLv ~= nil and RewardViewLogic.CanShowReward() == true then
        logwarning(string.format("RewardViewLogic.ShowSOLPathOfGrowthLevelUpPanel CurrentGameFlow:%s", Facade.GameFlowManager:GetCurrentGameFlow()))
        --解锁道具项
        local unlockInfo = {}
        local rewardItems = {}
        local SOLData = Server.GrowthRoadServer:GetSOLData()
        for key, value in pairs(SOLData or {}) do
            if value and value[1] then
                if value[1].isEmpty == true then
                    value[1].itemName = Module.Reward.Config.Loc.NoPathOfGrowthUnlock
                    value[1].iconPath = Module.Gunsmith.Config.MainUIConfig.GrowthRoadImage.EmptyIconPath
                end
            end
            local unlockItems = {}
            for index, data in ipairs(value) do
                table.insert(unlockItems, data)
                if data.isReward == true then
                    if key <= seasonLv and key > preSeasonLv then
                        table.insert(rewardItems, data.itemData) 
                    end
                end
            end
            table.insert(unlockInfo, {items=unlockItems, level=key})
        end
        --排序(避免数据未从小到大)
        table.sort(unlockInfo, function (a, b)
            return a.level < b.level
        end)
        Facade.SoundManager:PreLoadAssetByName("UI_Settlement_Bar_Loop")
        Facade.SoundManager:PreLoadAssetByName("UI_Growing_Success_Loop")
        Facade.SoundManager:PreLoadAssetByName("UI_Growing_Success")
        Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.SOLPathOfGrowthLevelUp, function ()
            local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.PathOfGrowthLevelUpPanel, nil, nil, false ,seasonLv, preSeasonLv, unlockInfo, Server.SettlementServer:GetSettlementInfoSource() == SettlementDefine.ESettlementInfoSource.SOL and #rewardItems <= 0)
            return popHandle
        end)
        if #rewardItems > 0 then
            RewardViewLogic.ShowRewardPanel(Module.Reward.Config.Loc.PathOfGrowthReward, nil, rewardItems, nil, false, nil)
        end
    end
end

RewardViewLogic.ShowMPPathOfGrowthLevelUpPanel = function(accountLv, preAccountLv)
    if accountLv ~= nil and preAccountLv ~= nil and RewardViewLogic.CanShowReward() == true then
        logwarning(string.format("RewardViewLogic.ShowMPPathOfGrowthLevelUpPanel CurrentGameFlow:%s", Facade.GameFlowManager:GetCurrentGameFlow()))
        --解锁道具项
        local unlockInfo = {}
        local rewardItems = {}
        local MPData = Server.GrowthRoadServer:GetMPData()
        for key, value in pairs(MPData or {}) do
            if value and value[1] then
                if value[1].isEmpty == true then
                    value[1].itemName = Module.Reward.Config.Loc.NoPathOfGrowthUnlock
                    value[1].iconPath = Module.Gunsmith.Config.MainUIConfig.GrowthRoadImage.EmptyIconPath
                end
            end
            local unlockItems = {}
            for index, data in ipairs(value) do
                table.insert(unlockItems, data)
                if data.isReward == true then
                    if key <= accountLv and key > preAccountLv then
                        table.insert(rewardItems, data.itemData)
                    end
                end
            end
            table.insert(unlockInfo, {items=unlockItems, level=key})
        end
        --排序(避免数据未从小到大)
        table.sort(unlockInfo, function (a, b)
            return a.level < b.level
        end)
        Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.MPPathOfGrowthLevelUp, function ()
            local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.PathOfGrowthLevelUpPanel, nil, nil, true, accountLv, preAccountLv, unlockInfo, Server.SettlementServer:CheckIsNewMPSettlement() == true)
            return popHandle
        end)
        if #rewardItems > 0 then
            RewardViewLogic.ShowRewardPanel(Module.Reward.Config.Loc.PathOfGrowthReward, nil, rewardItems, nil, false, nil)
        end
    end
end

-- BP结算奖励展示
RewardViewLogic.OpenBPSettlementPanel = function(iBeginLevel, iUpToLevel, fBeginExp, fUpToExp, iExpSourceType, iExpChgNum)
    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.BattlePassSettlement, function ()
        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassSettlement, nil, nil, iBeginLevel, iUpToLevel, fBeginExp, fUpToExp, iExpSourceType, iExpChgNum, false)
        Server.BattlePassServer:ClearSettleInfoCacheInFront()
        return popHandle
    end)
end

-- BP解锁展示界面
RewardViewLogic.OpenBPUnlockPanel = function(eType)
    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.BattlePassUnlock, function ()
        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.BattlePassBuyBP, nil, nil, eType)
        return popHandle
    end)
end

--------------------------------------------------------------------------
--- 英雄、时装、动作
--------------------------------------------------------------------------
RewardViewLogic.ShowHeroStuffUnlockPanel = function(heroStuffId)
    if heroStuffId ~= nil then
        local isInFrontEnd = RewardViewLogic.CanShowReward()
        loginfo(string.format("RewardViewLogic.ShowHeroStuffUnlockPanel : heroStuffId = %s , CheckIsInFrontEnd = %s",tostring(heroStuffId) ,tostring(isInFrontEnd)))
        if isInFrontEnd then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.HeroUnlock, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.HeroStuffUnlockPanel, nil, nil, heroStuffId)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.HeroUnlock)
            local heroStuffNum = 1
            local heroStuffIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 1 do
                    table.insert(heroStuffIds, oldCache[i]) 
                    heroStuffNum = heroStuffNum + 1
                end
            end
            table.insert(heroStuffIds, heroStuffId)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.HeroUnlock)
            table.insert(cacheList, heroStuffNum)
            table.append(cacheList, heroStuffIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end


--- 手表解锁
RewardViewLogic.ShowWatchUnlockPanel = function(watchId)
    if watchId ~= nil then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WatchUnlock, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WatchUnlockPanel, nil, nil, watchId)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.WatchUnlock)
            local watchNum = 1
            local watchIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 1 do
                    table.insert(watchIds, oldCache[i]) 
                    watchNum = watchNum + 1
                end
            end
            table.insert(watchIds, watchId)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.WatchUnlock)
            table.insert(cacheList, watchNum)
            table.append(cacheList, watchIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

-- 特殊道具解锁
RewardViewLogic.ShowStuffGainPop = function(type, stuffId)
    if type ~= nil and stuffId ~= nil then
        --干员商业化道具唯一
        local lastActionWrapper = Module.Reward.Field:GetLastStuffActionWrapper()
        if lastActionWrapper and  lastActionWrapper.stuffId == stuffId then
            loginfo(string.format("[jobsjunlin]RewardViewLogic.ShowStuffGainPop lastActionWrapper is exist, type = %s, stuffId = %s", tostring(type), tostring(stuffId)))
            return
        end

        local isExist = RewardViewLogic.IsExistPopUIActionQueue(Module.Reward.Config.EShowPopPanelType.StuffUnlock,stuffId)
        if isExist then
            loginfo(string.format("[jobsjunlin]RewardViewLogic.ShowStuffGainPop isExist, type = %s, stuffId = %s", tostring(type), tostring(stuffId)))
            return
        end
        if RewardViewLogic.CanShowReward() then
            local action = function ()
                if type > 5 then
                    local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.GestureGainPop, nil, nil, stuffId)
                    return popHandle
                else
                    local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.StuffGainPop, nil, nil, type, stuffId)
                    return popHandle
                end
            end
            local actionWrapper = {stuffId = stuffId ,subType = type}
            local actionWrapper_meta = {
                __call = function(self, ...)
                    loginfo(string.format("[jobsjunlin]RewardViewLogic.ShowStuffGainPop actionWrapper call, type = %s, stuffId = %s", tostring(self.subType), tostring(self.stuffId)))
                    Module.Reward.Field:SetLastStuffActionWrapper(actionWrapper)
                    return action(...)
                end
            }
            setmetatable(actionWrapper, actionWrapper_meta)
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.StuffUnlock,actionWrapper)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.StuffUnlock)
            local stuffNum = 1
            local types = {}
            local stuffIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 2 do
                    table.insert(types, oldCache[i]) 
                    table.insert(stuffIds, oldCache[i+1]) 
                    stuffNum = stuffNum + 1
                end
            end
            table.insert(types, type)
            table.insert(stuffIds, stuffId)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.StuffUnlock)
            table.insert(cacheList, stuffNum)
            for i = 1, #types, 1 do
                table.insert(cacheList, types[i])
                table.insert(cacheList, stuffIds[i])
            end
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

--挂饰获得
RewardViewLogic.ShowPendantGainPop = function(pendantItem)
    if pendantItem ~= nil then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.PendantGain, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.PendantGainPop, nil, nil, pendantItem)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.PendantGain)
            local pendantNum = 1
            local pendantIds = {}
            local pendantGids = {}
            if oldCache and oldCache[2] then
                pendantNum = oldCache[2]
                for i = 3, 2 + pendantNum*2, 2 do
                    if oldCache[i] == nil then
                        break
                    else
                        table.insert(pendantIds, oldCache[i]) 
                        table.insert(pendantGids, oldCache[i+1]) 
                        pendantNum = pendantNum + 1
                    end
                end
            end
            table.insert(pendantIds, pendantItem.id)
            table.insert(pendantGids, pendantItem.gid)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.PendantGain)
            table.insert(cacheList, pendantNum)
            for index, pendantId in ipairs(pendantIds) do
                table.insert(cacheList, pendantIds[index])
                table.insert(cacheList, pendantGids[index])
            end
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

-- 曼德尔砖特殊获得界面
RewardViewLogic.ShowMandelBrickGainPop = function(mandelBrickId)
    if mandelBrickId ~= nil then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.MandelBrickGain, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.MandelBrickGainPop, nil, nil, mandelBrickId)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.MandelBrickGain)
            local mandelBrickNum = 1
            local mandelBrickIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 1 do
                    table.insert(mandelBrickIds, oldCache[i]) 
                    mandelBrickNum = mandelBrickNum + 1
                end
            end
            table.insert(mandelBrickIds, mandelBrickId)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.MandelBrickGain)
            table.insert(cacheList, mandelBrickNum)
            table.append(cacheList, mandelBrickIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

RewardViewLogic.OpenVehicleSkinGainPop = function(vehicleSkinItem)
    if vehicleSkinItem then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.VehicleSkinGain, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.VehicleSkinGainPop, nil, nil, vehicleSkinItem)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.VehicleSkinGain)
            local skinNum = 1
            local skinIds = {}
            local skinGids = {}
            if oldCache and oldCache[2] then
                skinNum = oldCache[2]
                for i = 3, 2 + skinNum*2, 2 do
                    if oldCache[i] == nil then
                        break
                    else
                        table.insert(skinIds, oldCache[i]) 
                        table.insert(skinGids, oldCache[i+1]) 
                        skinNum = skinNum + 1
                    end
                end
            end
            
            table.insert(skinIds, vehicleSkinItem.id)
            table.insert(skinGids, vehicleSkinItem.gid)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.VehicleSkinGain)
            table.insert(cacheList, skinNum)
            for index, skinId in ipairs(skinIds) do
                table.insert(cacheList, skinIds[index])
                table.insert(cacheList, skinGids[index])
            end
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end


RewardViewLogic.ShowWeaponSkinGainPop = function(weaponSkinItem, bHasMore)
    if weaponSkinItem then
        logerror("[v_dzhanshen] RewardViewLogic.ShowWeaponSkinGainPop skindItemid="..tostring(weaponSkinItem.id).." gid="..tostring(weaponSkinItem.gid))
        if RewardViewLogic.CanShowReward() and Module.Reward:GetShowRewardScene() == false then
            local meleeSkinDataTable = WeaponHelperTool.GetMeleeWeaponSkinDataTable()
            local lotteryBoxGroupConfig = Module.Reward.Config.lotteryBoxGroupConfig
            local lotteryBoxPropConfig = Module.Reward.Config.lotteryBoxPropConfig
            local bIsTopQuality = true
            if weaponSkinItem.itemSubType ~= ItemConfig.EWeaponItemType.Melee 
                and weaponSkinItem.quality < ItemConfig.EWeaponSkinQualityType.Orange 
                and bHasMore == true then
                local groupId = nil
                for key, configRow in pairs(lotteryBoxPropConfig) do
                    if configRow.PropID == weaponSkinItem.id then
                        groupId = configRow.GroupID
                        break
                    end
                end
                if groupId then
                    local boxId = nil
                    for key, lotteryBoxGroupConfigRow in pairs(lotteryBoxGroupConfig) do
                        if lotteryBoxGroupConfigRow.GroupID == groupId then
                            boxId = lotteryBoxGroupConfigRow.BoxID
                            break
                        end
                    end
                    if boxId then
                        local lotteryBoxGroupConfigRows = Server.StoreServer:GetLotteryBoxGroupConfigByID(boxId)
                        for index, lotteryBoxGroupConfigRow in ipairs(lotteryBoxGroupConfigRows) do
                            local lotteryBoxPropConfigRows = {}
                            Server.StoreServer:GetLotteryBoxPropConfigByID(lotteryBoxGroupConfigRow.GroupID, lotteryBoxPropConfigRows)
                            for index, lotteryBoxPropConfigRow in ipairs(lotteryBoxPropConfigRows) do
                                local itemConfig = ItemConfigTool.GetItemConfigById(lotteryBoxPropConfigRow.PropID)
                                if itemConfig and itemConfig.Quality > weaponSkinItem.quality then
                                    bIsTopQuality = false
                                    break
                                end
                            end
                            if bIsTopQuality == false then
                                break
                            end
                        end
                    end
                end
            end
            if weaponSkinItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
                local fNormalNextActionCallback = function()
                    Module.Reward.Field:PopShowPopUIAction(
                        {
                            type = Module.Reward.Config.EShowPopPanelType.WeaponSkinGain,
                            action = function ()
                                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponSkinGainPop , nil, nil, weaponSkinItem)
                                return popHandle
                            end
                        }    
                    )
                end
                local fMysticalNextActionCallback = function()
                    Module.Reward.Field:PopShowPopUIAction(
                        {
                            type = Module.Reward.Config.EShowPopPanelType.WeaponSkinGain,
                            action = function ()
                                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.MysticalWeaponSkinGainPop , nil, nil, weaponSkinItem)
                                return popHandle
                            end
                        }    
                    )
                end
                if weaponSkinItem.itemSubType == ItemConfig.EWeaponItemType.Melee then
                    local videoID
                    if meleeSkinDataTable[weaponSkinItem.id] then
                        videoID = meleeSkinDataTable[weaponSkinItem.id].UnlockVideoPath
                    end
                    if videoID and videoID ~= "" then
                        Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                            return Module.CommonWidget:ShowFullScreenVideoView(videoID, false, true, fNormalNextActionCallback, nil, 0, 0)
                        end)
                    else
                        Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                            local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponSkinGainPop, nil, nil, weaponSkinItem)
                            return popHandle
                        end)
                    end
                elseif weaponSkinItem.gid ~= 0 then
                    --[[
                    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponSkinCutScenePage, nil, nil, weaponSkinItem, fMysticalNextActionCallback)
                        return popHandle
                    end)
                    --]]
                    --if weaponSkinItem.itemList and not weaponSkinItem.bSceneShow then
                    --    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                    --        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.RewardSceneViewTen, nil, nil, fMysticalNextActionCallback, weaponSkinItem.itemList, nil, 0, 1, weaponSkinItem)
                    --        return popHandle
                    --    end)
                    --else
                    --    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                    --        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.MysticalWeaponSkinGainPop , nil, nil, weaponSkinItem)
                    --        return popHandle
                    --    end)
                    --end
                    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.MysticalWeaponSkinGainPop , nil, nil, weaponSkinItem)
                        return popHandle
                    end)
                else
                    Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                        local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponSkinGainPop, nil, nil, weaponSkinItem)
                        return popHandle
                    end)
                end
            elseif bIsTopQuality == true then
                Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain, function ()
                    local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponSkinGainPop, nil, nil, weaponSkinItem)
                    return popHandle
                end)
            end
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain)
            local skinNum = 1
            local skinIds = {}
            local skinGids = {}
            if oldCache and oldCache[2] then
                skinNum = oldCache[2]
                for i = 3, 2 + skinNum*2, 2 do
                    if oldCache[i] == nil then
                        break
                    else
                        table.insert(skinIds, oldCache[i]) 
                        table.insert(skinGids, oldCache[i+1]) 
                        skinNum = skinNum + 1
                    end
                end
            end
            
            table.insert(skinIds, weaponSkinItem.id)
            table.insert(skinGids, weaponSkinItem.gid)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.WeaponSkinGain)
            table.insert(cacheList, skinNum)
            for index, skinId in ipairs(skinIds) do
                table.insert(cacheList, skinIds[index])
                table.insert(cacheList, skinGids[index])
            end
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

RewardViewLogic.ShowWeaponSkinRenamedPage = function(weaponSkinItem)
    if weaponSkinItem ~= nil then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinRenamed, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponSkinRenamedPage, nil, nil, weaponSkinItem)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.WeaponSkinRenamed)
            local skinNum = 1
            local skinIds = {}
            local skinGids = {}
            if oldCache and oldCache[2] then
                skinNum = oldCache[2]
                for i = 3, 2 + skinNum*2, 2 do
                    if oldCache[i] == nil then
                        break
                    else
                        table.insert(skinIds, oldCache[i]) 
                        table.insert(skinGids, oldCache[i+1]) 
                        skinNum = skinNum + 1
                    end
                end
            end
            
            if weaponSkinItem ~= nil then
                table.insert(skinIds, weaponSkinItem.id)
                table.insert(skinGids, weaponSkinItem.gid)
            end

            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.WeaponSkinRenamed)
            table.insert(cacheList, skinNum)
            for index, skinId in ipairs(skinIds) do
                table.insert(cacheList, skinIds[index])
                table.insert(cacheList, skinGids[index])
            end
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

--武器解锁/获得界面
RewardViewLogic.ShowWeaponGainPop = function(weaponItems)
    if weaponItems ~= nil and #weaponItems > 0 then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponGain, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponGainPop, nil, nil, weaponItems)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.WeaponGain)
            local weaponNum = 1
            local weaponIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 1 do
                    table.insert(weaponIds, oldCache[i]) 
                    weaponNum = weaponNum + 1
                end
            end
            for index, weaponItem in ipairs(weaponItems) do
                table.insert(weaponIds, weaponItem.id)
            end
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.WeaponGain)
            table.insert(cacheList, weaponNum)
            table.append(cacheList, weaponIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

--配件解锁/获得界面
RewardViewLogic.ShowWeaponAdapterGainPop = function(adapterItem)
    if adapterItem ~= nil then
        if RewardViewLogic.CanShowReward() then
            local weaponItems = {}
            local weaponIds = UE.AssembleWeaponDataLibrary.GetPartInstallReceiverList(adapterItem.id)
            local presetId
            for index, id in ipairs(weaponIds) do
                presetId = WeaponAssemblyTool.ChangeWeaponItemIDToPresetID(id, RewardViewLogic.IsInMp() and EArmedForceMode.MP or EArmedForceMode.SOL)
                local weaponItem = ItemBase:NewIns(presetId)
                if weaponItem then
                    table.insert(weaponItems, weaponItem)
                end
            end
            --[[
            weaponInfoList = Server.WeaponAssemblyServer:GetAllWeaponByWeaponPartsId(adapterItem.id)
            local weaponPresetList = WeaponHelperTool.GetRecFunctionTableDataTable()
            if weaponPresetList then
                for _, weaponInfo in pairs(weaponInfoList or {}) do
                    if weaponPresetList[weaponInfo.id] then
                        local weaponItem = ItemBase:NewIns(weaponPresetList[weaponInfo.id].MPDefaultPreset)
                        if weaponItem then
                            table.insert(weaponItems, weaponItem)
                        end
                    end
                end
            end
            --]]
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.WeaponAccessoryGain, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.WeaponAdapterGainPop, nil, nil, adapterItem, weaponItems)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.WeaponAccessoryGain)
            local adapterNum = 1
            local adapterIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 1 do
                    table.insert(adapterIds, oldCache[i]) 
                    adapterNum = adapterNum + 1
                end
            end
            table.insert(adapterIds, adapterItem.id)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.WeaponAdapterGain)
            table.insert(cacheList, adapterNum)
            table.append(cacheList, adapterIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

-- 曼德尔砖特殊获得界面
RewardViewLogic.ShowSafeBoxGainPop = function(safeBoxId)
    if safeBoxId ~= nil then
        if RewardViewLogic.CanShowReward() then
            Module.Reward.Field:PushShowPopUIAction(Module.Reward.Config.EShowPopPanelType.SafeBoxGain, function ()
                local popHandle = Facade.UIManager:AsyncShowUI(UIName2ID.SafeBoxGainPop, nil, nil, safeBoxId)
                return popHandle
            end)
        else
            local oldCache = RewardViewLogic.GetCachePopAction(Module.Reward.Config.EShowPopPanelType.SafeBoxGain)
            local safeBoxNum = 1
            local safeBoxIds = {}
            if oldCache and oldCache[2] then
                for i = 3, 2 + oldCache[2], 1 do
                    table.insert(safeBoxIds, oldCache[i]) 
                    safeBoxNum = safeBoxNum + 1
                end
            end
            table.insert(safeBoxIds, safeBoxId)
            local cacheList = {}
            table.insert(cacheList, Module.Reward.Config.EShowPopPanelType.SafeBoxGain)
            table.insert(cacheList, safeBoxNum)
            table.append(cacheList, safeBoxIds)
            RewardViewLogic.AddCachePopAction(cacheList)
        end
    end
end

RewardViewLogic.EnablePopUI = function(bEnable)
    Module.Reward.Field:EnablePopUI(bEnable)
end

RewardViewLogic.EnableNTFCall = function(key, bEnable)
    Module.Reward.Field:EnableNTFCall(key, bEnable)
end

RewardViewLogic.IsNTFCallEnabled = function(key)
    return Module.Reward.Field:GetIsNTFCallEnabled(key)
end

RewardViewLogic.GetPendingPopUINum = function()
    return Module.Reward.Field:GetPendingPopUINum()
end

RewardViewLogic.GetPendingPopUINumByType = function (showPopPanelType)
    return Module.Reward.Field:GetPendingPopUINumByType(showPopPanelType)
end


--尝试展示下一奖励界面
RewardViewLogic.ShowRewardsIfPossible = function()
    -- 延迟一下，不要在当帧close的时候打开新的同layer UI，有可能对应的layer正在reset
    local _delay = function()
        if Module.Reward:IsShowingPopUI() == false then
            Module.Reward.Config.Events.evtPopUIListEmpty:Invoke()
            Module.Reward.Field:PopShowPopUIAction()
        end
    end
    Timer.DelayCall(0.01, _delay)
end

--直接展示下一奖励界面
RewardViewLogic.ShowNextRewards = function(bStopPopup)
    -- 延迟一下，不要在当帧close的时候打开新的同layer UI，有可能对应的layer正在reset
    local _delay = function()
        if Module.Reward:IsShowingPopUI() == false then
            Module.Reward.Config.Events.evtPopUIListEmpty:Invoke()
        end
        Module.Reward.Field:PopShowPopUIAction(nil, bStopPopup)
    end
    Timer.DelayCall(0.01, _delay)
    RewardConfig.Events.evtGeneralRewardPanelClosed:Invoke()
end


RewardViewLogic.ClearActionByType = function(showPopPanelType)
    Module.Reward.Field:ClearActionByType(showPopPanelType)
end

RewardViewLogic.ContainsSpecialPanelInQueue = function()
    return Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.MandelBrickGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.PendantGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.HeroUnlock)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.StuffUnlock)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.VehicleSkinGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.WatchUnlock)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.VehicleGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.WeaponGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.WeaponAdapterGain)
    or Module.Reward:HasActionInQueue(Module.Reward.Config.EShowPopPanelType.SafeBoxGain)
end

RewardViewLogic.HasActionInQueue = function(showPopPanelType)
    return Module.Reward.Field:HasActionInQueue(showPopPanelType)
end

RewardViewLogic.SkipAllSpecialPanels = function()
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.MandelBrickGain)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.PendantGain)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.HeroUnlock)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.StuffUnlock)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.VehicleSkinGain)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.WatchUnlock)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.WeaponSkinGain)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.VehicleGain)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.WeaponGain)
    Module.Reward:ClearActionByType(Module.Reward.Config.EShowPopPanelType.WeaponAdapterGain)
end

RewardViewLogic.IsInMp = function()
    return Facade.GameFlowManager:GetCurrentGameFlow() == EGameFlowStageType.Lobby
end


RewardViewLogic.ToggleStackUI = function(bEnable)
    RewardViewLogic.ToggleStackUILayer(bEnable)
    RewardViewLogic.ToggleStackUILevel(bEnable)
end


RewardViewLogic.ToggleStackUILayer = function(bVisible)
    Facade.UIManager:SetBelowPopUILayerVisible(bVisible, ELayerRuleChangeReason.BusinessPending)
end


RewardViewLogic.ToggleStackUILevel = function(bEnable)
    local curLevel
    if bEnable == true then
        curLevel = Module.Reward.Field:GetCurrentStackUILevel()
        Module.Reward.Field:SetCurrentStackUILevel(nil)
        if curLevel then
            Module.LobbyDisplay.Config.Events.evtPOPUISwitchLevel:Invoke(curLevel)
        end
    else
        curLevel = Facade.GameFlowManager:GetCurrentSubStage()
        Module.Reward.Field:SetCurrentStackUILevel(curLevel)
    end
end


RewardViewLogic.HasStoreStackUILevel = function()
    local curLevel ,bTopBarVisible = Module.Reward.Field:GetCurrentStackUILevel()
    return curLevel ~= nil 
end
--------------------------------------------------------------------------
--- 缓存弹窗界面
--------------------------------------------------------------------------
---本地缓存弹窗数据
---@param actionParam table {EShowPopPanelType, ...}
RewardViewLogic.AddCachePopAction = function(actionParam)
    -- 例：{Module.Reward.Config.EShowPopPanelType.SeasonLevelUpMP, level, preLevel}
    -- 目前都是需要这三种情况，如果有特殊的，可增加EShowPopPanelType判断自行处理
    local cachePopAction = RewardViewLogic.GetCachePopAction(actionParam[1])
    cachePopAction = actionParam
    logwarning(string.format("RewardViewLogic.AddCachePopAction : EShowPopPanelType = %s, %s-->%s", actionParam[1], actionParam[2], actionParam[3]))
    RewardViewLogic.SetCachePopAction(cachePopAction)
end


RewardViewLogic.DoCachePopAction = function()
    local typeInOrder = {}
    local typeDontRequireSort = {}
    for key, type in pairs(Module.Reward.Config.EShowPopPanelType) do
        if Module.Reward.Config.ESortPopPanelType[type] then
            table.insert(typeInOrder, type)
        else
            table.insert(typeDontRequireSort, type) 
        end
    end
    table.sort(typeInOrder, function (a, b)
        return Module.Reward.Config.ESortPopPanelType[a] < Module.Reward.Config.ESortPopPanelType[b]
    end)
    table.append(typeInOrder, typeDontRequireSort)
    for _, showPopPanelType in ipairs(typeInOrder) do
        local cachePopAction = RewardViewLogic.GetCachePopAction(showPopPanelType)
        if not table.isempty(cachePopAction) then
            logwarning(string.format("RewardViewLogic.DoCachePopAction : EShowPopPanelType = %s, %s-->%s", cachePopAction[1], cachePopAction[2], cachePopAction[3]))
            RewardViewLogic.RemoveCachePopAction(showPopPanelType)
            if showPopPanelType == Module.Reward.Config.EShowPopPanelType.ModuleUnlock then
                local moduleNum = cachePopAction[2] or 0
                for i = 3, 2 + moduleNum, 1 do
                    if cachePopAction[i] == nil then
                        break
                    else
                        RewardViewLogic.ShowModuleUnlockPanel(cachePopAction[i])
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.HeroUnlock then
                local heroStuffNum = cachePopAction[2] or 0
                for i = 3, 2 + heroStuffNum, 1 do
                    if cachePopAction[i] ~= nil then
                        RewardViewLogic.ShowHeroStuffUnlockPanel(cachePopAction[i])
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.WatchUnlock then
                local heroStuffNum = cachePopAction[2] or 0
                for i = 3, 2 + heroStuffNum, 1 do
                    if cachePopAction[i] ~= nil then
                        RewardViewLogic.ShowWatchUnlockPanel(cachePopAction[i])
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.StuffUnlock then
                local stuffNum = cachePopAction[2] or 0
                for i = 3, 2 + stuffNum*2, 2 do
                    if cachePopAction[i] ~= nil and cachePopAction[i+1] ~= nil then
                        RewardViewLogic.ShowStuffGainPop(cachePopAction[i], cachePopAction[i+1])
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.MandelBrickGain then
                local mandelBrickNum = cachePopAction[2] or 0
                for i = 3, 2 + mandelBrickNum*2, 2 do
                    if cachePopAction[i] ~= nil and cachePopAction[i+1] ~= nil then
                        RewardViewLogic.ShowMandelBrickGainPop(cachePopAction[i])
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.PendantGain then
                local pendantNum = cachePopAction[2] or 0
                for i = 3, 2 + pendantNum*2, 1 do
                    if cachePopAction[i] == nil then
                        break
                    else
                        local pendantItem = Server.CollectionServer:GetHangingIfOwned(cachePopAction[i], cachePopAction[i+1])
                        RewardViewLogic.ShowPendantGainPop(pendantItem)
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.SafehouseLevelUpReward then
                RewardViewLogic.ShowSafehouseLevelUpRewardPanel(cachePopAction[2])
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.Reward then
                local title = tostring(cachePopAction[2])
                local tip = tostring(cachePopAction[3])
                local itemsNum = tonumber(cachePopAction[4])
                local qualitiesNum = tonumber(cachePopAction[5])
                local bShowOnlyInMP = cachePopAction[6] > 0
                local bUseCustomSort = cachePopAction[7] > 0
                local backgroudType = cachePopAction[8]
                local items = {}
                local qualities = {}
                if itemsNum > 0 then
                    for i = 9, 8 + itemsNum*3, 3 do
                        local newItem = ItemBase:New(cachePopAction[i], cachePopAction[i+1])
                        newItem.weaponId = cachePopAction[i+2]
                        table.insert(items, newItem) 
                    end
                end
                if qualitiesNum > 0 then
                    for i = 9 + itemsNum, 8 + itemsNum + qualitiesNum*2, 2 do
                        table.insert(qualities, {id=cachePopAction[i], num=cachePopAction[i+1]}) 
                    end
                end
                RewardViewLogic.ShowRewardPanel(title, tip, items, qualities, bShowOnlyInMP, bUseCustomSort)
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.WeaponSkinGain then
                local skinNum = cachePopAction[2] or 0
                local bHasMore = false
                for i = 3, 2 + skinNum*2, 1 do
                    if cachePopAction[i] == nil then
                        break
                    else
                        local weaponSkinItem = Server.CollectionServer:GetWeaponSkinIfOwned(cachePopAction[i], cachePopAction[i+1])
                        RewardViewLogic.ShowWeaponSkinGainPop(weaponSkinItem, bHasMore)
                        bHasMore = true
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.WeaponSkinRenamed then
                local skinNum = cachePopAction[2] or 0
                local weaponSkinItems = {}
                for i = 3, 2 + skinNum*2, 1 do
                    if cachePopAction[i] == nil then
                        break
                    else
                        local weaponSkinItem = Server.CollectionServer:GetWeaponSkinIfOwned(cachePopAction[i], cachePopAction[i+1])
                        RewardViewLogic.ShowWeaponSkinRenamedPage(weaponSkinItem)
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.ReputationLevelUp then
                local unlockedItemsNum = tonumber(cachePopAction[2])
                local merchantId = tonumber(cachePopAction[3])
                if merchantId ~= nil and unlockedItemsNum > 0 then
                    local unlockedItems = {}
                    for i = 4, 3 + unlockedItemsNum*2, 2 do
                       table.insert(unlockedItems, ItemBase:New(cachePopAction[i], cachePopAction[i+1])) 
                    end
                    RewardViewLogic.ShowReputationLevelUpPanel(unlockedItems, merchantId)
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.VehicleSkinGain then
                local skinNum = cachePopAction[2] or 0
                for i = 3, 2 + skinNum*2, 1 do
                    if cachePopAction[i] == nil then
                        break
                    else
                        local vehicleSkinItem = Server.CollectionServer:GetCollectionItemById(cachePopAction[i], cachePopAction[i+1])
                        RewardViewLogic.OpenVehicleSkinGainPop(vehicleSkinItem)
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.VehicleGain then
                local itemNum = cachePopAction[2] or 0
                for i = 3, 2 + itemNum*2, 1 do
                    if cachePopAction[i] == nil then
                        break
                    else
                        local vehicleItem = Server.CollectionServer:GetCollectionItemById(cachePopAction[i], cachePopAction[i+1])
                        RewardViewLogic.OpenVehicleSkinGainPop(vehicleItem)
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.WeaponGain then
                local weaponNum = cachePopAction[2] or 0
                local weaponItems = {}
                for i = 3, 2 + weaponNum, 1 do
                    if cachePopAction[i] ~= nil then
                        local newItem = ItemBase:New(cachePopAction[i])
                        table.insert(weaponItems, newItem)
                    end
                end
                RewardViewLogic.ShowWeaponGainPop(weaponItems)
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.WeaponAdapterGain then
                local adapterNum = cachePopAction[2] or 0
                for i = 3, 2 + adapterNum, 1 do
                    if cachePopAction[i] ~= nil then
                        local newItem = ItemBase:New(cachePopAction[i])
                        RewardViewLogic.ShowWeaponAdapterGainPop(newItem)
                    end
                end
            elseif showPopPanelType == Module.Reward.Config.EShowPopPanelType.SafeBoxGain then
                local safeBoxNum = cachePopAction[2] or 0
                for i = 3, 2 + safeBoxNum*2, 2 do
                    if cachePopAction[i] ~= nil and cachePopAction[i+1] ~= nil then
                        RewardViewLogic.ShowSafeBoxGainPop(cachePopAction[i])
                    end
                end
            end
        end 
    end
end


RewardViewLogic.ClearCachePopAction = function()
    for _, showPopPanelType in pairs(Module.Reward.Config.EShowPopPanelType) do
        RewardViewLogic.RemoveCachePopAction(showPopPanelType)
    end
    local safeHouseDeviceTable = Facade.TableManager:GetTable("SafeHouse/SafeHouseDevice")
    if safeHouseDeviceTable then
        for i, row in pairs(safeHouseDeviceTable) do
            Facade.ConfigManager:SetUserArray(string.format("%s_CacheBuildingEffectAction_%s", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" , tostring(row.DeviceId)), {})
        end
    end
    Facade.ConfigManager:SetUserArray(string.format("%s_CacheCollectionPropReachMaxNum", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" ), {})
end



RewardViewLogic.GetCachePopAction = function(showPopPanelType)
    return Facade.ConfigManager:GetUserArray(string.format("%s_CachePopAction_%s", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" , showPopPanelType), {})
end

RewardViewLogic.SetCachePopAction = function(actionParam)
    logwarning(string.format("RewardViewLogic.SetCachePopAction : EShowPopPanelType = %s, %s-->%s", actionParam[1], actionParam[2], actionParam[3]))
    Facade.ConfigManager:SetUserArray(string.format("%s_CachePopAction_%s", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" , actionParam[1]), actionParam)
end


RewardViewLogic.RemoveCachePopAction = function(showPopPanelType)
    logwarning(string.format("RewardViewLogic.RemoveCachePopAction : EShowPopPanelType = %s", showPopPanelType))
    Facade.ConfigManager:SetUserArray(string.format("%s_CachePopAction_%s", Facade.ConfigManager:GetString("LastServerKey", "") or "UnkownServerKey" , showPopPanelType), {})
end

RewardViewLogic.UniversalSort = function(a, b)
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.sortWeight ~= b.sortWeight then
        return a.sortWeight > b.sortWeight
    end
    return a.id > b.id
end


RewardViewLogic.QualitySort = function(a, b)
    if a.quality ~= b.quality then
        return a.quality > b.quality
    end
    if a.price ~= b.price then
        return a.price > b.price
    end
    return a.id > b.id
end

RewardViewLogic.PriceSort = function(a, b)
    if a.price ~= b.price then
        return a.price > b.price
    end
    return a.id > b.id
end

RewardViewLogic.TypeSort = function(a, b)
    if a.itemMainType ~= b.itemMainType then
        if a.itemMainType == EItemType.WeaponSkin or 
        a.itemMainType ==  EItemType.Weapon or
        a.itemMainType == EItemType.Receiver or
        a.itemMainType ==  EItemType.PoorWeapon or
        a.itemMainType == EItemType.ThrowWeapon
        then
            if b.itemMainType == EItemType.WeaponSkin or 
            b.itemMainType ==  EItemType.Weapon or
            b.itemMainType == EItemType.Receiver or
            b.itemMainType ==  EItemType.PoorWeapon or
            b.itemMainType == EItemType.ThrowWeapon
            then
                if a.itemMainType == EItemType.ThrowWeapon then
                    if b.itemMainType == EItemType.ThrowWeapon then
                        return a.id > b.id
                    else
                        return true
                    end
                else
                    if b.itemMainType == EItemType.ThrowWeapon then
                        return false
                    else
                        return a.id > b.id
                    end 
                end
            else
                return true
            end
        else
            if b.itemMainType == EItemType.WeaponSkin or 
            b.itemMainType ==  EItemType.Weapon or
            b.itemMainType == EItemType.Receiver or
            b.itemMainType ==  EItemType.PoorWeapon or
            b.itemMainType == EItemType.ThrowWeapon
            then
                return false
            else
                return a.id > b.id
            end
        end
    end
    return a.id > b.id
end

RewardViewLogic.OrderSort = function(a, b)
    return a.order < b.order
end

--划分奖励物品（是否武器类型）
RewardViewLogic.CheckIsWeapon = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType ==  EItemType.Weapon or
    itemMainType ==  EItemType.Receiver or
    itemMainType ==  EItemType.PoorWeapon or
    itemMainType ==  EItemType.ThrowWeapon
end


--划分奖励物品（是否外观类型）
RewardViewLogic.CheckIsWeaponSkin = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.WeaponSkin
end

--划分奖励物品（是否头像类型）
RewardViewLogic.CheckIsProfilePhoto = function(itemId)
    local socialDataTable = Facade.TableManager:GetTable("SocialAvatarDataTable")
    return RewardViewLogic.CheckIslegalItemId(itemId) and socialDataTable[itemId] and socialDataTable[itemId].AvatarType == 1
end

--划分奖励物品（是否军牌类型）
RewardViewLogic.CheckIsBrand = function(itemId)
    local socialDataTable = Facade.TableManager:GetTable("SocialAvatarDataTable")
    return RewardViewLogic.CheckIslegalItemId(itemId) and socialDataTable[itemId] and socialDataTable[itemId].AvatarType == 2
end

--划分奖励物品（是否干员类型）
RewardViewLogic.CheckIsHero = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.Hero
end

--划分奖励物品（是否干员服装类型）
RewardViewLogic.CheckIsHeroFashion = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.Fashion
end

--划分奖励物品（是否干员动作类型）
RewardViewLogic.CheckIsHeroAnim = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.HeroAccessory and itemSubType == EHeroAccessroy.AnimShow
end

--划分奖励物品（是否干员名片类型）
RewardViewLogic.CheckIsHeroCard = function(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and HeroHelperTool.GetHeroCardDataRow(tostring(itemId)) ~= nil
end

--划分奖励物品（是否干员手表）
RewardViewLogic.CheckIsWatch = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.HeroAccessory and itemSubType == EHeroAccessroy.Watch
end

--划分奖励物品（是否干员处决）
RewardViewLogic.CheckIsExecution = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.HeroAccessory and itemSubType == EHeroAccessroy.Execution
end

--划分奖励物品（是否干员手势）
RewardViewLogic.CheckIsGesture = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.HeroAccessory and itemSubType == EHeroAccessroy.Gesture
end

--划分奖励物品（是否干员语音）
RewardViewLogic.CheckIsKillline = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.HeroAccessory and (itemSubType == EHeroAccessroy.Lines or itemSubType == EHeroAccessroy.KillaLines)
end

--划分奖励物品（是否干员喷漆）
RewardViewLogic.CheckIsSprayPaint = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.HeroAccessory and itemSubType == EHeroAccessroy.SparyPaint
end

--划分奖励物品（是否枪械配件）
RewardViewLogic.CheckIsWeaponAdapter = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.Adapter and itemSubType ~= ItemConfig.EAdapterItemType.Pendant
end

--划分奖励物品（是否枪械挂饰类型）
RewardViewLogic.CheckIsPendant = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.Adapter and itemSubType == ItemConfig.EAdapterItemType.Pendant
end

--划分奖励物品（是否载具皮肤）
RewardViewLogic.CheckIsVehicleSkin = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.VehicleSkin
end

--划分奖励物品（是否载具）
RewardViewLogic.CheckIsVehicle = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) and itemMainType == EItemType.Vehicle
end

--划分奖励物品（是否载具）
RewardViewLogic.CheckIsMandelBrick = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) 
    and itemMainType == EItemType.Gift 
    and itemSubType == ECollectableType.LotteryBox
end

-- 是否哈夫币
RewardViewLogic.CheckIsMoneyPaper = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) 
    and itemMainType == EItemType.Token 
    and itemSubType == ETokenType.Currency
end

-- 是否是安全箱/安全箱皮肤
RewardViewLogic.CheckIsSafeBox = function(itemId)
    local itemMainType = ItemHelperTool.GetMainTypeById(itemId)
    local itemSubType = ItemHelperTool.GetSubTypeById(itemId)
    return RewardViewLogic.CheckIslegalItemId(itemId) 
    and (itemMainType == EItemType.Equipment
    and itemSubType == EEquipmentType.SafeBox or
    itemMainType == 33
    and itemSubType == 1)
end

--检查道具item是否合规可用
RewardViewLogic.CheckIslegalItemId = function(itemId)
    local itemConfigRow = ItemConfigTool.GetItemConfigById(itemId)
    return itemConfigRow ~= nil
end

--划分奖励（基于蓝图或武器或仓库）
RewardViewLogic.SplitReward = function(itemList)
    local bUseSplitByWeapon = true
    local bUseSplitByBlueprint = true
    for index, item in ipairs(itemList) do
        if item.itemMainType == EItemType.Adapter then
            if not item.weaponId then
                bUseSplitByWeapon = false
            end 
            if not item.attachToBlueprintId then
                bUseSplitByBlueprint = false
            end 
        else
            bUseSplitByWeapon = false
            bUseSplitByBlueprint = false
            break
        end
    end
    if bUseSplitByWeapon then
        return RewardViewLogic.SplitByWeapon(itemList)
    elseif bUseSplitByBlueprint then
        return RewardViewLogic.SplitByBlueprint(itemList)
    else
        return RewardViewLogic.SplitByInventory(itemList)
    end
end


--划分奖励物品（根据所处仓库）
RewardViewLogic.SplitByInventory = function(itemList)
    InventoryIdToTitle = {
        [0] = Module.Reward.Config.Loc.PropReward,
        [1] = Module.Reward.Config.Loc.SOLReward,
        [3] = Module.Reward.Config.Loc.CollectionReward,
        [10] = Module.Reward.Config.Loc.ExpReward,
        [11] = Module.Reward.Config.Loc.ExpReward,
        [12] = Module.Reward.Config.Loc.ReputationReward,
    }
    local inventoryGroups = {}
    if RewardViewLogic.IsInMp() then
        local itemToInventoryDataTable = Facade.TableManager:GetTable("GameItemToInventory")
        for index, item in ipairs(itemList) do
            local entry
            if itemToInventoryDataTable ~= nil then
                entry = itemToInventoryDataTable[item.id] or itemToInventoryDataTable[item.itemMainType * 100 + item.itemSubType] or itemToInventoryDataTable[item.itemMainType]
                if entry then
                    item.inventory = entry.Inventory
                end
            end
            if not InventoryIdToTitle[item.inventory] then
                item.inventory = 3
            end
            declare_if_nil(inventoryGroups, item.inventory, {})
            declare_if_nil(inventoryGroups[item.inventory], "items", {})
            table.insert(inventoryGroups[item.inventory].items,item)
            declare_if_nil(inventoryGroups[item.inventory], "title", InventoryIdToTitle[item.inventory] or InventoryIdToTitle[3])
        end
    else
        declare_if_nil(inventoryGroups, 0, {})
        declare_if_nil(inventoryGroups[0], "items", itemList)
        declare_if_nil(inventoryGroups[0], "title", InventoryIdToTitle[0]) 
    end
    return inventoryGroups
end



--划分奖励物品（根据所属蓝图获取并划分配件）
RewardViewLogic.GetWeaponPartsByBlueprint = function(weaponSkins)
    local skinToPartsMap = {}
    for index, item in ipairs(weaponSkins) do
        if item.itemMainType == EItemType.WeaponSkin then
            local weaponParts = RewardViewLogic.GetWeaponPartsBySkinId(item.id)
            if weaponParts and #weaponParts > 0 then
                declare_if_nil(skinToPartsMap, item.id, {})
                declare_if_nil(skinToPartsMap[item.id], "items", weaponParts)
                local gameItemRow = ItemConfigTool.GetItemConfigById(tostring(item.id))
                local names = string.split(gameItemRow.Name, "-")
                skinToPartsMap[item.id].title = string.format(Module.Reward.Config.Loc.PartsUnlockedBySkin, string.format("%02d",item.quality-1),gameItemRow.Name, names[1])
            end
        end
    end
    return skinToPartsMap
end


--计算重复物品的分解价值
RewardViewLogic.ExtractConversion = function(itemList)
    local conversion = {}
    local filterItems = {}
    for index, item in ipairs(itemList) do
        if item.src_id and item.src_id ~= 0 then
            table.insert(filterItems, ItemBase:NewIns(item.src_id))
            filterItems[#filterItems].conversionTxt = string.format(ECurrencyClientType2RichIconTxtV3[MapCurrencyId2ClientType[item.id]].."%d", 48, 48, item.num)
            declare_if_nil(conversion, item.id, 0)
            conversion[item.id] = conversion[item.id] + item.num
        else
            table.insert(filterItems, item)
        end
    end
    return #table.keys(conversion) > 0 and conversion or nil, filterItems
end


--获取某蓝图相关的武器配件
function RewardViewLogic.GetWeaponPartsBySkinId(weaponSkinId)
    local weaponSkinDataTable = WeaponHelperTool.GetWeaponSkinDataTable()
    if weaponSkinDataTable[weaponSkinId] then
        local presetId = weaponSkinDataTable[blueprintId].BlueprintId
        -- 通过预设找到非虚空配件
        local weaponParts= {}
        local gunDescTable = Facade.TableManager:GetTable("WeaponPart/GunDescTable")
        for _, gunDesc in pairs(gunDescTable) do
            if gunDesc.DesId == presetId and gunDesc.Index > 1 then
                local itemConfig = ItemConfigTool.GetItemConfigById(tostring(gunDesc.ItemId))
                if itemConfig then
                    if not itemConfig.IsModelOnly then
                        table.insert(weaponParts, ItemBase:New(gunDesc.ItemId))
                    end
                end
            end
        end
        return weaponParts
    else
        return {}
    end
end

function RewardViewLogic.GetGlobalPosAndSizeByWidget(widget)
    if widget == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

    if not widget.GetCachedGeometry then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local alignWidgetGeometry = widget:GetCachedGeometry()
	local itemScreenPosLT = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(0, 0))
    local itemScreenPosRB = alignWidgetGeometry:GetAbsolutePositionAtCoordinates(FVector2D(1, 1))

    return itemScreenPosLT,  FVector2D(itemScreenPosRB.X - itemScreenPosLT.X, itemScreenPosRB.Y- itemScreenPosLT.Y)
end

--世界坐标转换成本地坐标
function RewardViewLogic.GetLocalPosAndSize(parentWidget, globalPos, globalSize)
    if parentWidget == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local parentWidgetGeometry = parentWidget:GetCachedGeometry()
    if parentWidgetGeometry == nil then
        return FVector2D(0, 0), FVector2D(0, 0)
    end

	local localPos = parentWidgetGeometry:AbsoluteToLocal(globalPos)
    local localSize = parentWidgetGeometry:AbsoluteToLocal(globalPos + globalSize) - localPos
    return localPos,  localSize
end

RewardViewLogic.CanShowReward = function()
    return Facade.GameFlowManager:CheckIsInFrontEnd() and not Server.MatchServer:GetIsWaitForGotoGame()
end

---@param panelType Module.Reward.Config.EShowPopPanelType
RewardViewLogic.IsExistPopUIActionQueue = function(panelType,...)
    local ret = false
    if panelType == Module.Reward.Config.EShowPopPanelType.StuffUnlock then
        local stuffId   = unpack({...})
        if not stuffId then
            return ret
        end
        local queue =  Module.Reward.Field:GetPopUIActionQueue()
        if not queue then
            return ret
        end

        for _, actionInfo in ipairs(queue) do
            if actionInfo.type == Module.Reward.Config.EShowPopPanelType.StuffUnlock then
                if actionInfo.action and actionInfo.action.stuffId then
                   if actionInfo.action.stuffId == stuffId then
                        ret = true
                        break
                    end
                end
            end
        end
    end

    return ret
end

return RewardViewLogic