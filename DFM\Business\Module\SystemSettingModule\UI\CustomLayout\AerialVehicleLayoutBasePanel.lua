----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class AerialVehicleLayoutBasePanel
local AerialVehicleLayoutBasePanel = ui("AerialVehicleLayoutBasePanel")
local EJetVehicleMode = Module.SystemSetting.Config.EJetVehicleMode

function AerialVehicleLayoutBasePanel:Ctor()
    self._wtJoystickMoveButton = self:Wnd("VehicleJoystickMoveButton",UIWidgetBase)
    self._wtThrottleSliderButton = self:Wnd("ThrottleSliderButton",UIWidgetBase)
    self._wtVehicleSkillBtn3 = self:Wnd("VehicleSkillBtn_3",UIWidgetBase)
    self._wtLeftVehicleJoystick = self:Wnd("LeftVehicleJoystick",UIWidgetBase)
    self._wtRightVehicleJoystick = self:Wnd("RightVehicleJoystick",UIWidgetBase)
end

function AerialVehicleLayoutBasePanel:OnInitExtraData(layoutKey)
    self.mLayoutKey = layoutKey
    self:SwitchLayout()
end

function AerialVehicleLayoutBasePanel:SwitchLayout()
    self._wtThrottleSliderButton:Collapsed()
    self._wtLeftVehicleJoystick:Collapsed()
    self._wtRightVehicleJoystick:Collapsed()
    self._wtVehicleSkillBtn3:Collapsed()

    if self.mLayoutKey == EJetVehicleMode.Glider then
        self._curLayoutName = EJetVehicleMode.Glider
        self._wtThrottleSliderButton:Visible()
    elseif self.mLayoutKey == EJetVehicleMode.JoyStick then
        self._curLayoutName = EJetVehicleMode.JoyStick
        self._wtThrottleSliderButton:Visible()
        self._wtLeftVehicleJoystick:Visible()
        self._wtRightVehicleJoystick:Visible()
    else
    end

end

return AerialVehicleLayoutBasePanel