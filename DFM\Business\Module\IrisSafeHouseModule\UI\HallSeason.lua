----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMIrisSafeHouse)
----- LOG FUNCTION AUTO GENERATE END -----------

local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local RankingConfig  = require "DFM.Business.Module.RankingModule.RankingConfig"

local HallSeason=ui("HallSeason")

local RankModeType=RankingConfig.RankModeType
local ButtonIdEnum = ButtonIdConfig.Enum
function HallSeason:Ctor()
    loginfo("HallSeason:Ctor")
    self._wtEntranceBtn = self:Wnd("DFButton_0", UIButton)
    self._wtEntranceName = self:Wnd("DFTextBlock_38", UITextBlock)
    self._wtReddotCanvas = self:Wnd("DFCanvasPanel_110", UIWidgetBase)
    self._wtEntranceBtn:Event("OnClicked",self._OnEntranceBtnClicked,self)
end

function HallSeason:OnClose()
    loginfo("HallSeason:OnClose")
    if self._rankReddot then
        Module.ReddotTrie:UnRegisterStaticReddotDot(self._rankReddot)
    end
    self:RemoveAllLuaEvent()
end

function HallSeason:_OnEntranceBtnClicked()
    loginfo("HallSeason:_OnEntranceBtnClicked")
    if self._rankMode==RankModeType.Ranking then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SOLLobbyRank)
        if not Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleRankSOL) then
            local unlockInfo=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleRankSOL)
            Module.CommonTips:ShowSimpleTip(unlockInfo and unlockInfo.unlocktips)
            return
        end
        Module.Ranking:ShowMainPanel()
    else
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.MPLobbyTournament)
        if not Server.ModuleUnlockServer:IsModuleUnlock(SwitchModuleID.ModuleScoreMP) then
            local unlockInfo=Server.ModuleUnlockServer:GetModuleUnlockInfoById(SwitchModuleID.ModuleScoreMP)
            Module.CommonTips:ShowSimpleTip(unlockInfo and unlockInfo.unlocktips)
            return
        end
        local defaultIndex=Module.BattlefieldEntry:CheckIsCommanderMode() and 2 or 1
        Module.Tournament:ShowMainPanel(nil,defaultIndex)
    end
end

function HallSeason:InitRankEntrance(rankMode)
    loginfo("HallSeason:InitRankEntrance",rankMode)
    local gameFlow=Facade.GameFlowManager:GetCurrentGameFlow()
    if (rankMode==RankModeType.Ranking and gameFlow~=EGameFlowStageType.SafeHouse) or 
    (rankMode==RankModeType.Tournament and gameFlow~=EGameFlowStageType.Lobby) then
        logerror("HallSeason:InitRankEntrance, error type!!!","rankMode",rankMode,"gameFlow",gameFlow)
        --rankMode=gameFlow==EGameFlowStageType.SafeHouse and RankModeType.Ranking or RankModeType.Tournament
    end
    self._rankMode=rankMode
    if rankMode==RankModeType.Ranking then
        local fInitRankEntrance=CreateCallBack(function(self)
            self:RefreshSeasonName(RankModeType.Ranking)
            local reddotData=Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Ranking,"")
            if reddotData then
                if self._wtReddotCanvas then
                    self._rankReddot=Module.ReddotTrie:RegisterStaticReddotDot(self._wtReddotCanvas,{{reddotData=reddotData}})
                end
            else
                logerror("HallSeason:InitRankEntrance reddotData is nil!!!")
            end
        end,self)

        if not Server.RankingServer:GetCurSerial() then
            Server.RankingServer:ReqSeasonRankInfo(nil,fInitRankEntrance)
        else
            fInitRankEntrance()
        end
    
    else
        local fInitRankEntrance=CreateCallBack(function(self)
            self:RefreshSeasonName(RankModeType.Tournament)
            local reddotData=Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Tournament,"")
            if reddotData then
                if self._wtReddotCanvas then
                    self._rankReddot=Module.ReddotTrie:RegisterStaticReddotDot(self._wtReddotCanvas,{{reddotData=reddotData}})
                end
            else
                logerror("HallSeason:InitRankEntrance reddotData is nil!!!")
            end
        end,self)
        
        if not Server.TournamentServer:GetCurSerial() then
            Server.TournamentServer:ReqSeasonRankInfo(nil,fInitRankEntrance)
        else
            fInitRankEntrance()
        end
    end
    if rankMode==RankModeType.Ranking then
        self:AddLuaEvent(Server.RankingServer.Events.evtSeasonRankInfoUpdated, self.RefreshSeasonName, self)
    else
        self:AddLuaEvent(Server.TournamentServer.Events.evtTournamentInfoUpdated, self.RefreshSeasonName, self)
    end
end

function HallSeason:RefreshSeasonName(rankMode)
    loginfo("HallSeason:RefreshSeasonName",rankMode)
    rankMode=rankMode or self._rankMode
    if rankMode==RankModeType.Ranking then
        local seasonConfig=Module.Ranking:GetRankSeasonConfigBySerial(Server.RankingServer:GetCurSerial())
        if seasonConfig then
            self._wtEntranceName:SetText(seasonConfig.SeasonName)
        end
            
    else
        local seasonConfig=Module.Tournament:GetSeasonConfigBySerial(Server.TournamentServer:GetCurSerial())
        if seasonConfig then
            self._wtEntranceName:SetText(seasonConfig.Name)
        end
    end
end

return HallSeason