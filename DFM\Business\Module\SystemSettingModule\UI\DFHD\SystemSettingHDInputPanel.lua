----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------



---@class SystemSettingHDInputPanel
local SystemSettingHDInputPanel = ui("SystemSettingHDInputPanel")
local EKeyActionLogic = import "EKeyActionLogic"
local EFunctionBusinessLogic = import "EFunctionBusinessLogic"
local EConsumeMouseWheel = import "EConsumeMouseWheel"
local EKeyFunctionBindType = import "EKeyFunctionBindType"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local Config = require "DFM.Business.Module.SystemSettingModule.SystemSettingConfig"
local ESystemSettingHDPanel = Config.ESystemSettingHDPanel
local ESystemSettingHDInputPanel = Config.ESystemSettingHDInputPanel

function SystemSettingHDInputPanel:Ctor()
    loginfo("[george]<InputPanel> Ctor()")
    self:_BindWidget()

    -- 接框架设置Bar按键提示
    self.BottomBarState = {
        Common = 1,
        Hovering = 2,
        Selecting = 3,
        Quit = 4,
    }

    self.TipsBarShowSeconds = 10
    self:SetCppValue("bSupportModifierKeys", true)

    self.InputFunctionKeyItemsTable = {}
    self._SubUIIDlist = {}
end

function SystemSettingHDInputPanel:_BindWidget()
    self._wtScrollBox = self:Wnd("InputPanelScrollBox", UIWidgetBase)
    self._wtFunctionGroupList = self:Wnd("FunctionGroupList", UIWidgetBase)
    self._wtFunctionKeyPoolList = self:Wnd("FunctionKeyPoolList", UIWidgetBase)
    -- kinds of tips
    self._wtTipsBar = self:Wnd("WBP_SetUpComponent_TipsBar", UIWidgetBase)

    self._wtTipsBarChange = self._wtTipsBar:Wnd("Tips_Change", UIWidgetBase)
    self._wtTipsBarKeyIconBox = self._wtTipsBar:Wnd("Box", HDKeyIconBox)
    self._wtTipsBarTextBlock = self._wtTipsBar:Wnd("TextBlock", UITextBlock)
    self._wtTipsBarRevokeBtn = self._wtTipsBar:Wnd("WBP_SetUp_RevokeBtn", UIWidgetBase)

    self._wtDescRootPanel = self:Wnd("DescRootPanel", UILightWidget)
end

function SystemSettingHDInputPanel:_BindBtnEvent()
    self._wtTipsBarRevokeBtn:Wnd("Button_163", UIButton):Event(
        "OnClicked",
        self._OnClickTipsBarRevokeBtn,
        self
    )

end

function SystemSettingHDInputPanel:OnOpen()
    -- loginfo("[george]<InputPanel> OnOpen()")
    self:_BindBtnEvent()

    self.__cppinst.KeySettingManager.NotifyKeyMappingsFullUpdated:Add(self.UpdateFunctionKeyMappingList, self)
    self.__cppinst.OnEndKeySelecting:Bind(self._OnEndKeySelecting, self)
    self.__cppinst.NotifyUpdateHoveringRow:Bind(self.UpdateHoveringRow, self)
end

function SystemSettingHDInputPanel:OnShow()
    -- loginfo("[george]<InputPanel> OnShow()")
    self:HideTipsBar()
    if self:IsCurInInputPanel() then
        Module.SystemSetting.Field:SetDescRootPanelHD(self._wtDescRootPanel)
        Module.SystemSetting.Field:SetCurrentSettingPanelHD(self)
        self:SwitchBottomBarState(self.BottomBarState.Common)
    end

    self.__cppinst.KeySettingManager:OnCurSettingHeroChanged(Module.SystemSetting.Field:GetCurrentHero())
end

function SystemSettingHDInputPanel:OnHideBegin()
    Module.CommonBar:RecoverBottomBarInputSummaryList()
end

function SystemSettingHDInputPanel:OnHide()
    -- loginfo("[george]<InputPanel> OnHide()")
    self:SwitchBottomBarState(self.BottomBarState.Quit)
    CommonSettingLogicHD.RemoveDesc()
    Module.SystemSetting.Field:SetDescRootPanelHD(nil)
    Module.SystemSetting.Field:SetCurrentSettingPanelHD(nil)

    self.__cppinst.KeySettingManager.CurSettingHeroId = self.__cppinst.KeySettingManager.InvalidSettingHeroId
    self:_RemoveNavGroup()
end

function SystemSettingHDInputPanel:OnClose()
    -- loginfo("[george]<InputPanel> OnClose()")
    self.__cppinst.KeySettingManager.NotifyKeyMappingsFullUpdated:Remove(self.UpdateFunctionKeyMappingList, self)
    self.__cppinst.OnEndKeySelecting:Clear()
    self.__cppinst.NotifyUpdateHoveringRow:Clear()

    Facade.UIManager:RemoveSubUIByParent(self.SettingMainView, self._wtFunctionGroupList)
end

function SystemSettingHDInputPanel:IsCurInInputPanel()
    return Module.SystemSetting.Field:GetCurrentTabTypeHD() == Module.SystemSetting.Config.ESystemSettingHDPanel.InputSetting
end

function SystemSettingHDInputPanel:UpdateFunctionKeyMappingList()
    loginfo("[george]<InputPanel> UpdateFunctionKeyMappingList()")
    Facade.UIManager:RemoveSubUIByParent(self.SettingMainView, self._wtFunctionGroupList)
    self.__cppinst.KeySettingManager:PreprocRows()
    -- local ExistingGroupNum = self._wtFunctionGroupList:GetChildrenCount()
    self.KeyFunctionTypeDataTable = setdefault(self.KeyFunctionTypeDataTable, Facade.TableManager:GetTable("KeyMappings/KeyFunctionType"))
    self.validGroupNum = 0
    for i = 1, #self.__cppinst.KeyFunctionTypeListInThisPanel do
        for k, v in pairs(self.KeyFunctionTypeDataTable) do
            -- loginfo("[george]<InputPanel> UpdateFunctionKeyMappingList(), "..self.__cppinst.KeyFunctionTypeListInThisPanel[i].." "..v.keyFunctionType)
            if self.__cppinst.KeyFunctionTypeListInThisPanel[i] == v.keyFunctionType then
                if not v.bHide then
                    local functionType = v.keyFunctionType
                    self.validGroupNum = self.validGroupNum + 1
                    -- if self.validGroupNum <= ExistingGroupNum then
                    --     self._wtFunctionGroupList:GetChildAt(self.validGroupNum - 1):InitFunctionGroup(functionType, self.validGroupNum, self)
                    -- else
                        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self.SettingMainView, UIName2ID.InputKeyFunctionGroup, self._wtFunctionGroupList)
                        local uiIns = getfromweak(weakUIIns)
                        if uiIns then
                            uiIns:InitFunctionGroup(functionType, self.validGroupNum, self)
                        end
                        self._SubUIIDlist[#self._SubUIIDlist] = instanceID
                    -- end
                end
                break
            end
        end
    end

    -- 界面刷新，重新初始化导航
    self:_RemoveNavGroup()
    self:_RegisterNavGroup()
    -- loginfo("[george]<InputPanel> UpdateFunctionKeyMappingList(), Show "..self.validGroupNum.." groups of key mappings")
    -- If groups we have now more than needed, remove those (here we need to remove from end)
    -- for i = ExistingGroupNum, self.validGroupNum + 1, -1 do
    --     local instanceID = self._SubUIIDlist[i]
    --     if instanceID then
    --         Facade.UIManager:RemoveSubUI(self.SettingMainView, UIName2ID.InputKeyFunctionGroup, instanceID)
    --     end
    --     self._SubUIIDlist[i] = nil
    -- end
end

function SystemSettingHDInputPanel:UpdateFunctionKeyItem(inputFunctionKeyItem)
    if inputFunctionKeyItem ~= nil then
        local newRowInfo = self.__cppinst.KeySettingManager:GetRowByActionOrAxisName(inputFunctionKeyItem.rowInfo.ActionOrAxisName, inputFunctionKeyItem.rowInfo.AxisScale, false)
        inputFunctionKeyItem:InitFunctionKeyItem(newRowInfo, self, self.__cppinst.KeySettingManager:GetBinderRowsByActionOrAxisName(inputFunctionKeyItem.rowInfo.ActionOrAxisName, EKeyFunctionBindType.None))
        -- 成对
        if newRowInfo.PairedWrappedAsActionName ~= "" then
            for key, value in pairs(self.InputFunctionKeyItemsTable) do
                if key == newRowInfo.PairedWrappedAsActionName then
                    local pairedNewRowInfo = self.__cppinst.KeySettingManager:GetRowByActionOrAxisName(value.rowInfo.ActionOrAxisName, value.rowInfo.AxisScale, false)
                    value:InitFunctionKeyItem(pairedNewRowInfo, self, self.__cppinst.KeySettingManager:GetBinderRowsByActionOrAxisName(value.rowInfo.ActionOrAxisName, EKeyFunctionBindType.None))
                    break
                end
            end
        end
    end
end

function SystemSettingHDInputPanel:PlayerConfirmedNewAddedChange(ActionOrAxisName)
    if ActionOrAxisName ~= nil and self.__cppinst.KeySettingManager ~= nil and self.__cppinst.KeySettingManager.ConfirmChangedTipsActionName ~= nil then
        self.__cppinst.KeySettingManager:ConfirmChangedTipsActionName(ActionOrAxisName)
    end
end

function SystemSettingHDInputPanel:AddFunctionKeyItemToPool(functionKeyItem)
    self._wtFunctionKeyPoolList:AddChild(functionKeyItem)
end

function SystemSettingHDInputPanel:_OnAnyOptionalBindCheckBoxChanged(functionKeyItem, bIsChecked)
    local row = functionKeyItem.rowInfo
    row.bIsOptionalBindEnable = bIsChecked
    self.__cppinst.KeySettingManager:SetFunctionMappingByRow(row, false, false, false)
    self:UpdateFunctionKeyItem(functionKeyItem)
end

function SystemSettingHDInputPanel:_OnClickAnyKeyButton(functionKeyItem, keyBtn)
    self.SelectingRowFunctionKeyItem = functionKeyItem
    self.SelectingRowKeyBtn = keyBtn
    self:SetCppValue("SelectingSettingTableRow", self.SelectingRowFunctionKeyItem.rowInfo)
    self:SetCppValue("SelectingKeySN", self.SelectingRowKeyBtn.keyIndex - 1)

    if not self.__cppinst.SelectingSettingTableRow.IsAxis then
        self.SelectingKeyMapping =
            self.__cppinst.KeySettingManager:GetActionKeyMappingByActionName(self.__cppinst.SelectingSettingTableRow.ActionOrAxisName, self.__cppinst.KeySettingManager.InputDeviceType_KM)
    else
        self.SelectingKeyMapping =
            self.__cppinst.KeySettingManager:GetAxisKeyMappingByAxisName(self.__cppinst.SelectingSettingTableRow.ActionOrAxisName, self.__cppinst.SelectingSettingTableRow.AxisScale, self.__cppinst.KeySettingManager.InputDeviceType_KM)
    end
    self.__cppinst:StartKeySelecting()
    self._wtScrollBox:SetConsumeMouseWheel(EConsumeMouseWheel.Never)

    -- Update BottomBar Btn
    self:SwitchBottomBarState(self.BottomBarState.Selecting)
    self:HideTipsBar()
end

-- @param:
-- resultType: 0: 成功绑定; 1: 与同分类其他功能冲突，解绑再绑定; -1: 异常; -2: 该功能已绑定该键; -3: 与重要功能冲突，需二次确认; -4: 与同分类内的重要功能冲突; -5: 已绑定该轴按键; -6: Enter键屏蔽
function SystemSettingHDInputPanel:_OnEndKeySelecting(resultType)
    if self.SelectingRowKeyBtn == nil then
        return
    end

    local fReconfirm = function(bHasConflictRow)
        -- RichText KeyIcon
        local KeyIconRichTxt = ""
        local Keys = self.__cppinst.KeySettingManager.GetValidKeysFromSettingRow(self.__cppinst.CachedRow, self.__cppinst.SelectingKeySN)
        for index, value in ipairs(Keys) do
            local KeyName = Keys[index]:GetFName()
            local Postfix = ""
            if KeyName == "MouseX" or KeyName == "MouseY" then
                local keyInfo = nil
                if self.__cppinst.SelectingKeySN == 0 then
                    keyInfo = self.__cppinst.CachedRow.KeyInfo
                elseif self.__cppinst.SelectingKeySN == 1 then
                    keyInfo = self.__cppinst.CachedRow.KeyInfo_Second
                end
                if keyInfo.IsPositiveInAxis then
                    Postfix = "_pos"
                else
                    Postfix = "_neg"
                end
            end
            KeyIconRichTxt = KeyIconRichTxt.."<dfmrichtext type=\"img\" id=\"KeyIcon_"..KeyName..Postfix.."\"/>"
            if index < #Keys then
                KeyIconRichTxt = KeyIconRichTxt.."<dfmrichtext type=\"img\" id=\"KeyIcon_Padding\"/>"
            end
        end
        local reconfirmTxt = string.format(Module.SystemSetting.Config.Loc.HDSetting.ImpConfirmTxt, KeyIconRichTxt, self.__cppinst.ConflictRows[#self.__cppinst.ConflictRows].FunctionName)
        local fConfirmCallBack = function()
            self.__cppinst:ConfirmBindKeyConflicted(bHasConflictRow)
            Timer.DelayCall(0.1, function() self:SwitchBottomBarState(self.curBottomBarState, true) end)
        end
        local fCancelCallBack = function()
            Timer.DelayCall(0.1, function() self:SwitchBottomBarState(self.curBottomBarState, true) end)
        end
        local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
        local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ImpConfirmConfirmTxt
        self.confirmWindowHandle = Module.CommonTips:ShowConfirmWindow(reconfirmTxt, CreateCallBack(fConfirmCallBack, self), CreateCallBack(fCancelCallBack, self), cancelTxt, confirmTxt)
    end
    if resultType == 0 then
        self:UpdateFunctionKeyItem(self.SelectingRowFunctionKeyItem)
        Module.SystemSetting.Config.Event.evtCloudSettingKeySettingChanged:Invoke()
    elseif resultType == 1 then
        self:UpdateFunctionKeyItem(self.SelectingRowFunctionKeyItem)
        for index, row in ipairs(self.__cppinst.ConflictRows) do
            self:UpdateFunctionKeyItem(self.InputFunctionKeyItemsTable[row.ActionOrAxisName])
        end
        Module.SystemSetting.Config.Event.evtCloudSettingKeySettingChanged:Invoke()
        -- self:ShowTipsBar(resultType)  She3 7.28 放开同分类冲突
    elseif resultType == -1 then
        self.SelectingRowKeyBtn:UpdateType(0)
    elseif resultType == -2 then
        self.SelectingRowKeyBtn:UpdateType(0)
        self:ShowTipsBar(resultType)
    elseif resultType == -3 then
        self.SelectingRowKeyBtn:UpdateType(0)
        fReconfirm(false)
    elseif resultType == -4 then
        self.SelectingRowKeyBtn:UpdateType(0)
        fReconfirm(true)
    elseif resultType == -5 then
        self.SelectingRowKeyBtn:UpdateType(0)
        self:ShowTipsBar(resultType)
    elseif resultType == -6 then
        self.SelectingRowKeyBtn:UpdateType(0)
        self:ShowTipsBar(resultType)
    end
    self.SelectingRowKeyBtn:UpdateLine()
    self._wtScrollBox:SetConsumeMouseWheel(EConsumeMouseWheel.WhenScrollingPossible)

    -- Update BottomBar Btn
    self:SwitchBottomBarState(self.BottomBarState.Common)

    local HeroDesc = Module.SystemSetting.Field:GetHeroDesc()
    if isvalid(HeroDesc) then
        HeroDesc:_UpdateHasConfigTips()
    end
end

function SystemSettingHDInputPanel:_OnChangeKeyInputMode(functionKeyItem, droDownIndex)
    if functionKeyItem.rowInfo.KeyActionLogic ~= 0 and functionKeyItem.rowInfo.FunctionBusinessLogic == 0 then -- 切换/按住
        -- 0: Switch, 1: Hold
        if droDownIndex == 0 then
            self.__cppinst:NotifyChangeInputMode(functionKeyItem.rowInfo.ActionOrAxisName, EKeyActionLogic.Switch)
        else
            self.__cppinst:NotifyChangeInputMode(functionKeyItem.rowInfo.ActionOrAxisName, EKeyActionLogic.Hold)
        end
    
        -- 特殊处理左右探头
        local LeanPeekPairName = nil
        if functionKeyItem.rowInfo.ActionOrAxisName == "LeftLeanPeek" then
            LeanPeekPairName = "RightLeanPeek"
        elseif functionKeyItem.rowInfo.ActionOrAxisName == "RightLeanPeek" then
            LeanPeekPairName = "LeftLeanPeek"
        end
        if LeanPeekPairName ~= nil then
            self.InputFunctionKeyItemsTable[LeanPeekPairName]._wtModeSelectorDropDownBox:SetTabIndex(droDownIndex - 1, false)
            self.InputFunctionKeyItemsTable[LeanPeekPairName]._wtModeSelectorDropDownBox:RefreshDropDownBox(false)
            if droDownIndex == 1 then
                self.__cppinst:NotifyChangeInputMode(LeanPeekPairName, EKeyActionLogic.Switch)
            else
                self.__cppinst:NotifyChangeInputMode(LeanPeekPairName, EKeyActionLogic.Hold)
            end
        end
    elseif functionKeyItem.rowInfo.FunctionBusinessLogic ~= 0 and functionKeyItem.rowInfo.KeyActionLogic == 0 then -- 业务的耦合逻辑
        self.__cppinst:NotifyChangeFunctionBusinessLogic(functionKeyItem.rowInfo.ActionOrAxisName, droDownIndex)
    else
        logerror("LogKeySetting SystemSettingHDInputPanel:_OnChangeKeyInputMode KeyActionLogic and FunctionBusinessLogic conflict!")
    end

    local HeroDesc = Module.SystemSetting.Field:GetHeroDesc()
    if isvalid(HeroDesc) then
        HeroDesc:_UpdateHasConfigTips()
    end
end

function SystemSettingHDInputPanel:_OnHoverAnyKeyButton(functionKeyItem, keyBtn)
    if self.__cppinst.bIsSelectingKey then
        return
    end

    self:SetCppValue("bIsHoveringKey", true)
    self.HoveringRowFunctionKeyItem = functionKeyItem
    self.HoveringRowKeyBtn = keyBtn
    self:SetCppValue("HoveringSettingTableRow", self.HoveringRowFunctionKeyItem.rowInfo)
    self:SetCppValue("HoveringKeySN", self.HoveringRowKeyBtn.keyIndex - 1)

    -- Update BottomBar Btn
    if (self.HoveringRowKeyBtn.keyIndex == 1 and self.HoveringRowFunctionKeyItem.rowInfo.KeyInfo.Key.KeyName ~= "None") or (self.HoveringRowKeyBtn.keyIndex == 2 and self.HoveringRowFunctionKeyItem.rowInfo.KeyInfo_Second.Key.KeyName ~= "None") then
        self:SwitchBottomBarState(self.BottomBarState.Hovering)
    end
end

function SystemSettingHDInputPanel:_OnUnhoverAnyKeyButton(functionKeyItem, keyBtn)
    if self.__cppinst.bIsSelectingKey then
        return
    end

    self:SetCppValue("bIsHoveringKey", false)

    -- Update BottomBar Btn
    self:SwitchBottomBarState(self.BottomBarState.Common)
end

function SystemSettingHDInputPanel:UpdateHoveringRow()
    self:UpdateFunctionKeyItem(self.HoveringRowFunctionKeyItem)
end

-- @param:
-- resultType: 1: 与同分类其他功能冲突，解绑再绑定; -2: 该功能已绑定该键; -5: 已绑定该轴按键; -6: Enter键屏蔽
function SystemSettingHDInputPanel:ShowTipsBar(resultType)
    self._wtTipsBarKeyIconBox:SetVisibility(ESlateVisibility.Collapsed)
    self._wtTipsBarRevokeBtn:SetVisibility(ESlateVisibility.Collapsed)
    if resultType == 1 then
        self._wtTipsBarKeyIconBox:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        self._wtTipsBarRevokeBtn:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
        local row = self.__cppinst.KeySettingManager:GetRowByActionOrAxisName(self.__cppinst.SelectingSettingTableRow.ActionOrAxisName, self.__cppinst.SelectingSettingTableRow.AxisScale, false)
        self._wtTipsBarKeyIconBox:InitBySettingTableRow(row, self.__cppinst.SelectingKeySN + 1, true, 0)
        self._wtTipsBarTextBlock:SetText(string.format(Module.SystemSetting.Config.Loc.HDSetting.KeyBindChangeTxt, self.__cppinst.ConflictRows[#self.__cppinst.ConflictRows].FunctionName, self.__cppinst.SelectingSettingTableRow.FunctionName))

    elseif resultType == -2 then
        self._wtTipsBarTextBlock:SetText(Module.SystemSetting.Config.Loc.HDSetting.KeyBindAlreadyBoundTxt)
    elseif resultType == -5 then
        self._wtTipsBarTextBlock:SetText(Module.SystemSetting.Config.Loc.HDSetting.AxisKeyBindAlreadyBoundTxt)
    elseif resultType == -6 then
        self._wtTipsBarTextBlock:SetText(Module.SystemSetting.Config.Loc.HDSetting.KeyEnterNotAvaliableTxt)
    end

    self._wtTipsBar:SetVisibility(ESlateVisibility.SelfHitTestInvisible)
    if isvalid(self._TipsBarTimerHandle) then
        Timer.CancelDelay(self._TipsBarTimerHandle)
    end
    self._TipsBarTimerHandle = Timer.DelayCall(self.TipsBarShowSeconds, self.HideTipsBar, self)
end

function SystemSettingHDInputPanel:HideTipsBar()
    self._wtTipsBar:SetVisibility(ESlateVisibility.Collapsed)
    Timer.CancelDelay(self._TipsBarTimerHandle)
    self._TipsBarTimerHandle = nil
end

function SystemSettingHDInputPanel:_OnClickTipsBarRevokeBtn()
    self.__cppinst.KeySettingManager:SetFunctionMappingByRow(self.__cppinst.SelectingSettingTableRow, false, false, false)
    for index, row in ipairs(self.__cppinst.ConflictRows) do
        self.__cppinst.KeySettingManager:SetFunctionMappingByRow(row, false, false, false)
    end
    self:UpdateFunctionKeyItem(self.SelectingRowFunctionKeyItem)
    for index, row in ipairs(self.__cppinst.ConflictRows) do
        self:UpdateFunctionKeyItem(self.InputFunctionKeyItemsTable[row.ActionOrAxisName])
    end
    self:HideTipsBar()
end

function SystemSettingHDInputPanel:SwitchBottomBarState(state, bForce)
    if self.curBottomBarState == state and bForce ~= true then
        return
    end
    self.curBottomBarState = state

    local resetList = {{actionName = "ResetInput", func = self._OnClickResetBtn, caller = self}}
    local globalList = Module.SystemSetting.Field:GetGlobalSummaryList()
    for _, v in ipairs(globalList) do
        table.insert(resetList, v)
    end
    local heroId = Module.SystemSetting.Field:GetCurrentHero()
    if heroId ~= nil and heroId ~= "0" then
        -- table.insert(resetList, {actionName = "ApplyGeneral", func = self._OnClickApplyGlobalBtn, caller = self})  不对单一分页进行应用全局
    end

    if state == self.BottomBarState.Common then
        Module.CommonBar:SetBottomBarTempInputSummaryList(resetList, false)
    end
    if state == self.BottomBarState.Hovering then
        local list = {
            {actionName = "Unbind", func = self._OnUnbindHoveringKey, caller = self},
            {actionName = "UnbindDelete", func = self._OnUnbindHoveringKey, caller = self, bHide = true}
        }
        for key, value in pairs(resetList) do
            table.insert(list, value)
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(list, false)
    end
    if state == self.BottomBarState.Selecting then
        Module.CommonBar:SetBottomBarTempInputSummaryList({
            {actionName = "CancelBind", func = self._Lua_CancelKeySelecting, caller = self}
        }, true, true)
    end
    if state == self.BottomBarState.Quit then
        Module.CommonBar:RecoverBottomBarInputSummaryList()
        -- Module.CommonBar:SetBottomBarInputSummaryList({}, false)
    end

end

function SystemSettingHDInputPanel:_Lua_CancelKeySelecting()
    self.__cppinst:CancelKeySelecting()
end

function SystemSettingHDInputPanel:_OnUnbindHoveringKey()
    self.__cppinst:UnbindHoveringKey()
    self:_OnUnhoverAnyKeyButton(nil, nil)

    local HeroDesc = Module.SystemSetting.Field:GetHeroDesc()
    if isvalid(HeroDesc) then
        HeroDesc:_UpdateHasConfigTips()
    end
    self:HideTipsBar()
end

function SystemSettingHDInputPanel:_OnClickApplyGlobalBtn()
    SystemSettingLogic.HDHeroSettingConfigApplyGlobal(Module.SystemSetting.Field:GetCurrentHero())

    local HeroDesc = Module.SystemSetting.Field:GetHeroDesc()
    if isvalid(HeroDesc) then
        HeroDesc:_UpdateHasConfigTips()
    end
end

function SystemSettingHDInputPanel:_OnClickResetBtn()
    local fReset = function()
        -- Reset key mapping
        self.__cppinst:ResetKeySettings()
        self:HideTipsBar()
        Timer.DelayCall(0.1, function() self:SwitchBottomBarState(self.curBottomBarState, true) end)
    end
    local fCancelCallBack = function()
        Timer.DelayCall(0.1, function() self:SwitchBottomBarState(self.curBottomBarState, true) end)
    end
    local cancelTxt = Module.SystemSetting.Config.Loc.HDEntrance.cancel
    local confirmTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetTxt
    self.confirmWindowHandle = Module.CommonTips:ShowConfirmWindow(self.resetContentTxt, CreateCallBack(fReset, self), CreateCallBack(fCancelCallBack, self), cancelTxt, confirmTxt)

    local HeroDesc = Module.SystemSetting.Field:GetHeroDesc()
    if isvalid(HeroDesc) then
        HeroDesc:_UpdateHasConfigTips()
    end
end

function SystemSettingHDInputPanel:_NotifySubTabType(inSettingMainView, inType)
    self.SettingMainView = inSettingMainView
    if inType == ESystemSettingHDInputPanel.CCC then
        -- 3C
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInput3CTxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {1, 2}
    elseif inType == ESystemSettingHDInputPanel.UI then
        -- UI
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInputUITxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {3, 5, 6}
    elseif inType == ESystemSettingHDInputPanel.Vehicle then
        -- Vehicle
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInputVehicleTxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {4, 12, 7}
    elseif inType == ESystemSettingHDInputPanel.GM then
        if VersionUtil.IsShipping() then
            return
        end
        -- GM
        self.resetContentTxt = Module.SystemSetting.Config.Loc.HDSetting.ResetInputGMTxt
        self.__cppinst.KeyFunctionTypeListInThisPanel = {0, 100}
    end

    self:UpdateFunctionKeyMappingList()
end

function SystemSettingHDInputPanel:OnMainViewSwitchLeave()
    self.__cppinst:CancelKeySelecting()
end

-- UI导航
function SystemSettingHDInputPanel:_RegisterNavGroup()
    local defaultFocusGroup = nil
    -- 滑动条导航
    local wtScrollBox = self:Wnd("InputPanelScrollBox", UIWidgetBase)
    if wtScrollBox then
        local navGroup = WidgetUtil.RegisterNavigationGroup(wtScrollBox, self, "Hittest")
        if navGroup then
            navGroup:AddNavWidgetToArray(wtScrollBox)
            navGroup:SetScrollRecipient(wtScrollBox)
            table.insert(self._NavGroups, navGroup)
            defaultFocusGroup = navGroup
        end
    end

    -- 为每个FunctionGroup创建NavGroup，然后默认Focus第一个FunctionGroup
    for functionType, groupInsID in pairs(self._SubUIIDlist) do
        local navGroup = nil
        local weakUIIns, instanceID = Facade.UIManager:GetSubUI(self.SettingMainView, UIName2ID.InputKeyFunctionGroup, groupInsID)
        local uiIns = getfromweak(weakUIIns)
        if uiIns then
            navGroup = WidgetUtil.RegisterNavigationGroup(uiIns, self, "Hittest")
        end

        if navGroup then
            navGroup:AddNavWidgetToArray(uiIns)
            table.insert(self._NavGroups, navGroup)
            if functionType == 1 or functionType == 4 then
                defaultFocusGroup = navGroup
            end
        end
    end

    if defaultFocusGroup then
        WidgetUtil.TryFocusDefaultWidgetByGroup(defaultFocusGroup)
    end
end

function SystemSettingHDInputPanel:_RemoveNavGroup()
    self._NavGroups = {}
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    WidgetUtil.RemoveNavigationGroup(self)
end

return SystemSettingHDInputPanel
