----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMInventory)
----- LOG FUNCTION AUTO GENERATE END -----------



local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local InventoryConfig = require "DFM.Business.Module.InventoryModule.InventoryConfig"
local InventoryLogic = require "DFM.Business.Module.InventoryModule.InventoryLogic"
local ItemOperaTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemOperaTool"
local ItemViewQualitySubView = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.ItemViewQualitySubView"
local CommonItemHighlight = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.CommonItemHighlight"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local IVQualityComponent = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVQualityComponent"
local IVDecBtnComponent   = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.IVDecBtnComponent"
local ItemBaseTool        = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemBaseTool"
local UGPInputHelper = import "GPInputHelper"
local UCollectionRoomConstantWrap = import "CollectionRoomConstantWrap"


--BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPInputHelper = import "GPInputHelper"
local EGPInputType = import "EGPInputType"
local UGPInputDelegates = import "GPInputDelegates"
--END MODIFICATION

---@class WarehouseDepositTab_HD : LuaUIBaseView
local WarehouseDepositTab_HD = ui("WarehouseDepositTab_HD")

WarehouseDepositTab_HD.DRAG_OVER_DELAY = 0.5
WarehouseDepositTab_HD.LONG_PRESS_TRIGGER_TIME = 0.4
WarehouseDepositTab_HD.LONG_PRESS_CANCEL_INTERVAL = 15

WarehouseDepositTab_HD.CAPACITY_UP_ICON = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_DragSelectedBox_De_03.Common_DragSelectedBox_De_03'"
WarehouseDepositTab_HD.CAPACITY_DOWN_ICON = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_DragSelectedBox_De_05.Common_DragSelectedBox_De_05'"

WarehouseDepositTab_HD.ETabStatus = {
	None = 0,
	Warehouse = 1,
	ExtWarehouse = 2,
	Available = 3,
	Locked = 4,
	VipDeposit = 5,
}

local function log(...)
	loginfo("[WarehouseDepositTab_HD]", ...)
end



function WarehouseDepositTab_HD:Ctor()
	self._tabIndex = 0
	self._tabStatus = WarehouseDepositTab_HD.ETabStatus.None
	self._bindDepositSlot = nil
	self._bSelected = false
	self._bMultiSelected = false
	self.onDropState = false

	self._wtBgImg = self:Wnd("wtBgImg", IVQualityComponent)
	self._wtIconImg = self:Wnd("wtIconImg", UIImage)

	self._wtBottomLine = self:Wnd("wtBottomLine", UIImage)

	self._wtSelectCheckBoxPanel = self:Wnd("wtSelectCheckBox", UIWidgetBase)
	self._wtSelectCheckBox = self._wtSelectCheckBoxPanel:Wnd("wtMainCheckBox", UICheckBox)
	self._wtSelectCheckBoxPanel:Collapsed()
	self._wtSelectCheckBox:SetIsChecked(false)

	self._wtHighlight = self:Wnd("wtHighlight", CommonItemHighlight)

	self._wtCapacityChangeRoot = self:Wnd("wtCapacityChangeRoot", UIWidgetBase)
	self._wtCapacityChangeIcon = self:Wnd("wtCapacityChangeIcon", UIImage)
	self._wtCapacityChangeNum = self:Wnd("wtCapacityChangeNum", UITextBlock)
	self._wtRemoveBtn = self:Wnd("wtRemoveBtn", IVDecBtnComponent)
	local dragAreaCls = Module.CommonWidget:GetDragAreaClass()
	self._wtDragArea = self:Wnd("wtDragArea", dragAreaCls)

	self._wtExtName = self:Wnd("wtExtName", UITextBlock)

	self._qualityPanel = self:Wnd("DFCanvasPanel_Quality", UIWidgetBase)
	self._qualityPanel:Collapsed()
	self._wtGreyMask = self:Wnd("WBP_SlotCompGreyMask", UIWidgetBase)

	-- 专门为tab展开样式所添加的控件
	self._wtExpansionBtn = self:Wnd("Expansion_Btn", DFCommonButtonOnly)
	self._wtExpansionBtn:Visible()
	self._wtExpansionBtn:Event("OnClicked", self.OnClicked, self)
	self._wtExtGridTxt = self:Wnd("wtExtGrid", UITextBlock)
	self._isOperateExtensionBox = false

	-- 新增替换高亮
	self._wtExtReplaceHighlight = self:Wnd("wtHighlight_Replacement", UIWidgetBase)

	self._bInExtManagementMode = false

	self._quality = nil
	self.tipsList = {}

	self._bReplaceMode = false

	self._wtDFTipsAnchor = UIUtil.WndTipsAnchor(self, "DFTipsAnchor", self._ShowExpansionTips, self._HideExpansionTips)

	--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
	self:SetCppValue("bIsFocusable", true)
	self._wtExpansionBtn:SetCppValue("IsFocusable", false)
	self._bInGamepadMoveOrderList = false
	--END MODIFICATION
end


--==================================================
--region Life function

function WarehouseDepositTab_HD:OnOpen()

end
function WarehouseDepositTab_HD:OnClose()
end
function WarehouseDepositTab_HD:OnShow()
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragStart, self._OnGlobalItemDragStart, self)
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemSimulateDragStart, self._OnGlobalItemDragStart, self)
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDrop, self._OnGlobalItemDrop, self)
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemSimulateDragStop, self._OnGlobalItemDragCancelled, self)
	self:AddLuaEvent(Module.CommonWidget.Config.Events.evtGlobalItemDragCancelled, self._OnGlobalItemDragCancelled, self)
	self:AddLuaEvent(Server.InventoryServer.Events.evtItemMove, self._OnItemMove, self)
    self:AddLuaEvent(Server.InventoryServer.Events.evtPostSortMultiPos, self._OnPostSortMultiPos, self)
	-- self:FlushExpansionTips()

	-- BEGIN MODIFICATION @ VIRTUOS : 
	self._OnNotifyInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self._HandleInputTypeChanged, self))
	-- --绑定Received事件用于辅助手柄输入
	self:Event("OnFocusReceivedEvent", self._OnFocusReceived, self)
	self:Event("OnFocusLostEvent", self._OnFocusLost, self)
	-- END MODIFICATION
end

function WarehouseDepositTab_HD:OnHide()
	self:RemoveAllLuaEvent()
	self._wtHighlight:Collapsed()
	-- BEGIN MODIFICATION @ VIRTUOS : 
	if self._OnNotifyInputTypeChangedHandle then
		UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self._OnNotifyInputTypeChangedHandle)
		self._OnNotifyInputTypeChangedHandle = nil
	end

	self:RemoveEvent("OnFocusReceivedEvent")
	self:RemoveEvent("OnFocusLostEvent")

	self:_RemoveOrderListShortCut()
	self:_EnableDepositTabGamepadShortCut(false)
	self:_CancelOrderList()
	self:_HideExpansionTips()
	-- END MODIFICATION
end
function WarehouseDepositTab_HD:OnInitExtraData()

end

function WarehouseDepositTab_HD:_TriggerSingleClickMove()
	local item = ItemOperaTool.GetCurrentSingleClickMoveItem()
	if not item then
		return false
	end
	local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
	local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()
	local bDrop = false
	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse then
		if self:OnDrop(nil, nil, {DefaultDragVisual = {item = item}}, false) then
			bDrop = true
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
		if not bExtendItem and self:OnDrop(nil, nil, {DefaultDragVisual = {item = item}}, false) then
			bDrop = true
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
		if bExtendItem and self:OnDrop(nil, nil, {DefaultDragVisual = {item = item}}, false) then
			bDrop = true
		end
	end
	self:ChangeDropState()
	Module.Looting:DoSelectItem(nil, false, false)
	LogAnalysisTool.DoSendItemOperateReport(ItemOperateActionMethod.ItemOperateActionMethod_SingleClick)
	return bDrop
end

function WarehouseDepositTab_HD:OnImmediateClicked()
	return not self:_TriggerSingleClickMove()
end

function WarehouseDepositTab_HD:OnClicked()
	if self._mainBtnCallback then
		self._mainBtnCallback(self._tabIndex)
		if self._reddotIns then
			self._reddotIns:SetReddotVisible(false)
			local currentData = Facade.ClockManager:GetFormattedServerDate()
			Facade.ConfigManager:SetUserString("ClickData", currentData)
		end
	end
	-- 点击后更新底边栏，因为有不选中任何扩容箱则不触发整理的规则
	-- Module.Inventory.Config.Events.evtBottomBarSettingArrMode:Invoke()
end

function WarehouseDepositTab_HD:OnLongPressed()
	-- Facade.UIManager:AsyncShowUI(UIName2ID.NewExtSelectionWindow, nil, nil, self._tabIndex)
	if self._longPressCallback then
		self._longPressCallback(self._tabIndex)
	end
end

function WarehouseDepositTab_HD:OnAnimFinished(anim)
    if anim == self.Anima_Increase_In then
        self:PlayWidgetAnim(self.Anima_Increase_Out)
    end

	if anim == self.Anima_Decrease_In then
        self:PlayWidgetAnim(self.Anima_Decrease_Out)
	end
end
--endregion
--==================================================


--==================================================
--region Public API

function WarehouseDepositTab_HD:InitDepositTab(index)
    -- 在做初始化操作之前先做一些准备处理
    local depositId = Server.InventoryServer:GetDepositIdByIndex(index)
	local extItemName, capacity, timeState

	self:FlushExpansionTips()
	self._tabIndex = index
	self._tabStatus = self:_GetTabStatusByIndex(index)
	self._bindDepositSlot = Server.InventoryServer:GetSlot(depositId)
    self:SetInExtManagerMode(self._bInExtManagementMode and true or false)
	self._wtCapacityChangeRoot:Collapsed()

	local bHandleLongPress = false
	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse
	or self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse
	or self._tabStatus == WarehouseDepositTab_HD.ETabStatus.VipDeposit then
		local extSelectedIconPath, qualityColor
		if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse then
			extSelectedIconPath = ItemConfig.WarehouseIconPath_Selected

			local warehouseDevice = Module.BlackSite:GetDeviceData(1010)
			local warehouseLvl = warehouseDevice and warehouseDevice:GetLevel() or 0
			qualityColor = ItemConfig.MapWarehouseLvl2LinearColor[warehouseLvl]
			extItemName = Module.Inventory.Config.Loc.WarehouseDepositoryTitle
			self._qualityPanel:Collapsed()
			self:SetCppValue("Type", 0)	-- Cur exist
		elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.VipDeposit then
            self:Collapsed()
            -- VIP扩容箱是目前废弃功能，先留着
            --[[
			-- 常驻VIP扩容箱时有两种状态，一种无道具，一种是过期的VIP扩容箱道具
			local extensionVipSlot = Server.InventoryServer:GetSlot(ESlotType.ExtensionVIP)
			self._bindDepositSlot = Server.InventoryServer:GetSlot(ESlotType.VipDepositContainer)
			local refExtItem = extensionVipSlot:GetEquipItem()
			if refExtItem then
				local extItemCfg = ItemConfigTool.GetExtentionBoxDescRowById(refExtItem.id)
				if extItemCfg then
					extSelectedIconPath = FLuaHelper.SoftObjectPtrToString(extItemCfg.BoxTabIcon)
				end
				self._quality = refExtItem.quality
				extItemName = refExtItem.name
				self:SetDepositTabState(refExtItem)
			end
            ]]--
		else
			local refExtItem = Server.InventoryServer:GetExtItemByIndex(index)
			if refExtItem then
				local extItemCfg = ItemConfigTool.GetExtentionBoxDescRowById(refExtItem.id)
				if extItemCfg then
					extSelectedIconPath = FLuaHelper.SoftObjectPtrToString(extItemCfg.BoxTabIcon)
				end
				self._quality = refExtItem.quality
				extItemName = refExtItem.name
				self._qualityPanel:SelfHitTestInvisible()
				self:SetCppValue("Type", 0)	-- Cur exist

				-- vip扩容箱得特殊处理
				if refExtItem.InSlot and refExtItem.InSlot.SlotType == ESlotType.ExtensionVIP then
					self:SetDepositTabState(refExtItem)
				end

				-- GTI箱子需要特殊处理
				local GTIExtIDPrefix = DFMGlobalConst.GetGlobalConst("GTICaseIconGivenList")
				local prefixArray = string.split(GTIExtIDPrefix, ",")
				for _, prefix in pairs(prefixArray) do
					if refExtItem and tostring(refExtItem.id) == prefix then
						self._isGTIExtItem = true
						break
					end
				end
			end

			bHandleLongPress = true
		end

		if extSelectedIconPath then
			self._wtIconImg:AsyncSetImagePath(extSelectedIconPath)
		end

		if self._quality and self._quality >= 2 and not self._isGTIExtItem then
			self:SetCppValue("NumType", self._quality - 2)
			self:BPSetType(1)
		elseif self._isGTIExtItem then
			self:SetCppValue("NumType", 6)
			self:BPSetType(1)
		else
			self._qualityPanel:Collapsed()
		end

		-- 获取当前扩容箱的容量信息
		if self._bindDepositSlot and self._bindDepositSlot:IsExtContainerSlot() then
			local usedCapacity = self._bindDepositSlot:GetUsedCapacity()
			local maxCapacity = self._bindDepositSlot:GetTotalCapacity()
			if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.VipDeposit then
				if usedCapacity == 0 then
					capacity = InventoryConfig.Loc.ItemNoActivationTips
				else
					capacity = StringUtil.Key2StrFormat(Module.Inventory.Config.Loc.NumFormat,{curNum = usedCapacity, maxNum = maxCapacity})
					timeState = InventoryConfig.Loc.ItemExpiredTips
				end
			else
				capacity = StringUtil.Key2StrFormat(Module.Inventory.Config.Loc.NumFormat,{curNum = usedCapacity, maxNum = maxCapacity})
				self._wtExtGridTxt:SetText(capacity)
			end
		else
			log("Current expansion box slot data not found")
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
		self:SetCppValue("Type", 1)	-- Available
		if index == Server.InventoryServer:GetCurDepositSlotNum() + 1 then
			self:SetBright(true)
			-- self:SetIsFocusable(true)
			if not self:GetIsLonger() then
				self:SetCppValue("bIsFocusable", true)
			end
		else
			self:SetBright(false)
			self:SetCppValue("bIsFocusable", false)
		end
		extItemName = Module.Inventory.Config.Loc.AddExtBtnText
	else
		self:SetCppValue("Type", 2)	-- Locke
		extItemName = Module.Inventory.Config.Loc.UnLockExtBtnText
	end

	self:SetCppValue("bHandleLongPress", bHandleLongPress)
	if bHandleLongPress then
		self:SetCppValue("LongPressInterval", WarehouseDepositTab_HD.LONG_PRESS_TRIGGER_TIME)
		self:SetCppValue("LongPressCancelDistance", WarehouseDepositTab_HD.LONG_PRESS_CANCEL_INTERVAL)
	end

	if IsHD() then
		table.insert(self.tipsList, extItemName)
		table.insert(self.tipsList, capacity)
		if timeState then
			table.insert(self.tipsList, timeState)
		end
	end
	self:SetCppValue("Seleted", false)
	self:Set_Style()	-- Implemented in bp
	if extItemName then
		self._wtExtName:SetText(extItemName)
	end
end

function WarehouseDepositTab_HD:GetTabIndex()
	return self._tabIndex
end

function WarehouseDepositTab_HD:SetInMultiMode(bInMultiMode)
	self:SetSelected(false)
	self:SetMultiSelected(false)
	self:SetReplacementMode(bInMultiMode)

	if bInMultiMode then
		local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
		if self._tabIndex <= curDepositNum then
			-- self._wtSelectCheckBox:Visible()
			self._wtSelectCheckBoxPanel:HitTestInvisible()	-- 通过父控件的点击来控制，因此不能是Visible
		else
			self._wtSelectCheckBoxPanel:Collapsed()
			self:SetRenderOpacity(0.2)
			self:SetReplacementMode(false)
		end
	else
		self._wtSelectCheckBoxPanel:Collapsed()
		self:SetRenderOpacity(1)
	end
end

function WarehouseDepositTab_HD:SetInExtManagerMode(bInExtManagerMode)
	local function unEquipExt()
		local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
		ItemOperaTool.DoUnEquipItem(refExtItem)
	end

	if not self:IsShowInPanel() then
		return
	end
	self._bInExtManagementMode = bInExtManagerMode
	self:SetReplacementMode(bInExtManagerMode)
	self:Visible()
	self._wtRemoveBtn:Collapsed()
	self._wtDragArea:Collapsed()

	--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
	self:SetCppValue("bIsFocusable", true)
	self:_RemoveOrderListShortCut()
	self:_EnableDepositTabGamepadShortCut(false)
	--END MODIFICATION

	if bInExtManagerMode then
		local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
		local bIsExtSlot = self._bindDepositSlot.SlotType == ESlotType.VipDepositContainer
		if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse and not bIsExtSlot then
			-- 已有的扩容箱具有交互逻辑
			self._wtRemoveBtn:SetVisibility(self._bSelected and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
			self._wtRemoveBtn:BindDecBtnClickedCallback(CreateCallBack(unEquipExt, self))
			self._wtDragArea:Visible()
		else
			local bMainExt = self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse
			self:SetReplacementMode(bMainExt)
			self:SetVisibility(bMainExt and ESlateVisibility.Visible or ESlateVisibility.HitTestInvisible)

			--BEGIN MODIFICATION @ VIRTUOS : UI Navigation
			self:SetCppValue("bIsFocusable", false)
			--END MODIFICATION
		end
	else
		if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
			self:SetCppValue("Type", 1)	-- Available
			if self._tabIndex == Server.InventoryServer:GetCurDepositSlotNum() + 1 then
				self:SetBright(true)
				-- self:SetIsFocusable(true)
				if not self:GetIsLonger() then
					self:SetCppValue("bIsFocusable", true)
				end
			else
				self:SetBright(false)
				self:SetCppValue("bIsFocusable", false)
			end
		end
	end
end

function WarehouseDepositTab_HD:SetRemoveBtnVisible(bShow)
	self._wtRemoveBtn:SetVisibility(bShow and ESlateVisibility.Visible or ESlateVisibility.Collapsed)
end

function WarehouseDepositTab_HD:SetInExtReplaceMode(bInExtReplaceMode, extItem2Replace)
	if self.GetVisibility and self:GetVisibility() == ESlateVisibility.Collapsed then
		return
	end
	self:SetReplacementMode(bInExtReplaceMode)
	if bInExtReplaceMode then
		if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
			-- self._wtHighlight:SelfHitTestInvisible()
			self._wtExtReplaceHighlight:SelfHitTestInvisible()
			local hightLightTabType
			local curExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
			if InventoryLogic.CheckExtItemReplacable(extItem2Replace, curExtItem) then
				hightLightTabType = 0
				self._wtExtReplaceHighlight:SelfHitTestInvisible()
				-- self._wtHighlight:SelfHitTestInvisible()
				-- self._wtHighlight:PlayDropReplaceValidAnim()
			else
				-- self._wtHighlight:Collapsed()
				self._wtExtReplaceHighlight:Collapsed()
			end
		else
			local bMainExt = self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse
			self:SetReplacementMode(bMainExt)
			self:SetVisibility(bMainExt and ESlateVisibility.Visible or ESlateVisibility.HitTestInvisible)
		end
	else
		-- self._wtHighlight:Collapsed()
		self._wtExtReplaceHighlight:Collapsed()
		self:Visible()
	end
end

function WarehouseDepositTab_HD:SetInSellMode(bInSellMode)
	if not self:IsShowInPanel() then
		return
	end
	if bInSellMode then
		local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()
		if self._tabIndex <= curDepositNum then
			self:SetRenderOpacity(1)
			self:Visible()
		else
			self:SetRenderOpacity(0.2)
			self:HitTestInvisible()
		end
	else
		self:SetRenderOpacity(1)
		self:Visible()
	end
end

function WarehouseDepositTab_HD:SetSelected(bSelected)
	self._bSelected = bSelected

	self:SetCppValue("Seleted", bSelected)
	self:Set_Style()	-- Implemented in bp

	-- 主仓库不需要显示remove按钮
	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse then
		self._wtRemoveBtn:Collapsed()
	end
end

function WarehouseDepositTab_HD:SetMultiSelected(bSelected)
	self._bMultiSelected = bSelected

	self._wtSelectCheckBox:SetIsChecked(bSelected)
end

function WarehouseDepositTab_HD:ShowValidHighlight()
	self._wtHighlight:SelfHitTestInvisible()
	self._wtHighlight:PlayDropValidAnim()
end

function WarehouseDepositTab_HD:ShowInvalidHighlight()
	self._wtHighlight:SelfHitTestInvisible()
	self._wtHighlight:PlayDropInvalidAnim()
end

function WarehouseDepositTab_HD:BindBtnCallback(callback, caller)
	self._mainBtnCallback = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:BindDragStayCallback(callback, caller)
	self._dragStayCallback = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:BindLongPressCallback(callback, caller)
	self._longPressCallback = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:HighlightTab(changeNum, bShowNum)
	bShowNum = setdefault(bShowNum, true)
	self._wtHighlight:Collapsed()
	if not changeNum then
		return
	end
	if changeNum > 0 then
		self._wtCapacityChangeRoot:SelfHitTestInvisible()
		self:_PlayCapacityUpAnim(changeNum, bShowNum)
	elseif changeNum < 0 then
		self._wtCapacityChangeRoot:SelfHitTestInvisible()
		self:_PlayCapacityDownAnim(changeNum)
	else
		self._wtCapacityChangeRoot:Collapsed()
	end
end

function WarehouseDepositTab_HD:_PlayCapacityUpAnim(changeNum, bShowNum)
	bShowNum = setdefault(bShowNum,true)
	-- self._wtCapacityChangeIcon:AsyncSetImagePath(WarehouseDepositTab_HD.CAPACITY_UP_ICON, false)
	if bShowNum then
		self._wtCapacityChangeNum:SelfHitTestInvisible()
		self._wtCapacityChangeNum:SetText("+" .. changeNum)
	else
		self._wtCapacityChangeNum:Collapsed()
	end

	self:PlayWidgetAnim(self.Anima_Increase_In)
end

function WarehouseDepositTab_HD:_PlayCapacityDownAnim(changeNum)
	-- self._wtCapacityChangeIcon:AsyncSetImagePath(WarehouseDepositTab_HD.CAPACITY_DOWN_ICON, false)
	self._wtCapacityChangeNum:Collapsed()
	self:PlayWidgetAnim(self.Anima_Decrease_In)
end

function WarehouseDepositTab_HD:SetDepositIsOperating(bIsOperating)
	self._isOperateExtensionBox = bIsOperating
end

function WarehouseDepositTab_HD:GetDepositIsOperating()
	return self._isOperateExtensionBox
end

--endregion
--==================================================


--==================================================
--region Private API

function WarehouseDepositTab_HD:_GetTabStatusByIndex(index)
	local maxDepositNum = Server.InventoryServer:GetMaxDepositSlotNum()
	local availableDepositNum = Server.InventoryServer:GetItemAvaiableDepositSlotNum()
	local curDepositNum = Server.InventoryServer:GetCurDepositSlotNum()

	if index <= 0 or index > maxDepositNum then
		return WarehouseDepositTab_HD.ETabStatus.None
	end

	if index == 1 then
		return WarehouseDepositTab_HD.ETabStatus.Warehouse
	elseif index <= curDepositNum then
		return WarehouseDepositTab_HD.ETabStatus.ExtWarehouse
	elseif index <= curDepositNum + availableDepositNum then
		return WarehouseDepositTab_HD.ETabStatus.Available
	elseif index == curDepositNum + availableDepositNum + 1 then
		return WarehouseDepositTab_HD.ETabStatus.VipDeposit
	else
		return WarehouseDepositTab_HD.ETabStatus.Locked
	end
end

function WarehouseDepositTab_HD:_AddBtnTips(text1, text2)
	table.insert(tipsList, {smallTip = text1})
	table.insert(tipsList, {smallTip = text2})
end

function WarehouseDepositTab_HD:ClearDelayTipsTimer()
	if self.hoverDelayHandle then
		Timer.CancelDelay(self.hoverDelayHandle)
		self.hoverDelayHandle = nil
	end
end

function WarehouseDepositTab_HD:_ShowExpansionTips()
	self:_HideExpansionTips()
	if self._bReplaceMode then
		return
	end

	-- BEGIN MODIFICATION @ VIRTUOS : 
	if WidgetUtil.IsGamepad() == true and self._bInGamepadMoveOrderList == true then
		return
	end
	-- END MODIFICATION

	self.hoverDelayHandle = Timer.DelayCall(0.1, self._InternalShowExpansionTips, self)
end

function WarehouseDepositTab_HD:_InternalShowExpansionTips()
	local contents = {}
	for _, tipText in pairs(self.tipsList) do
		table.insert(contents, {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = tipText}})
	end

	if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end

    self.hoverHandle = Module.CommonTips:ShowAssembledTips(contents, self._wtDFTipsAnchor)
end

function WarehouseDepositTab_HD:FlushExpansionTips()
	self.tipsList = {}
end

function WarehouseDepositTab_HD:_HideExpansionTips()
	self:ClearDelayTipsTimer()
	self:_InternalHideExpansionTips()
end

function WarehouseDepositTab_HD:_InternalHideExpansionTips()
	if self.hoverHandle then
        Module.CommonTips:RemoveAssembledTips(self.hoverHandle, self._wtDFTipsAnchor)
        self.hoverHandle = nil
    end
end

function WarehouseDepositTab_HD:_ExtIsFull()
	if self._bindDepositSlot and self._bindDepositSlot:IsExtContainerSlot() then
		local usedCapacity = self._bindDepositSlot:GetUsedCapacity()
		local maxCapacity = self._bindDepositSlot:GetTotalCapacity()
		return usedCapacity == maxCapacity
	end
end

function WarehouseDepositTab_HD:_RefreshExtCapacity()
	local capacity
	if self._bindDepositSlot and self._bindDepositSlot:IsExtContainerSlot() then
		local usedCapacity = self._bindDepositSlot:GetUsedCapacity()
		local maxCapacity = self._bindDepositSlot:GetTotalCapacity()
		capacity = StringUtil.Key2StrFormat(Module.Inventory.Config.Loc.NumFormat,{curNum = usedCapacity, maxNum = maxCapacity})
		self._wtExtGridTxt:SetText(capacity)
	else
		log("Current expansion box slot data not found")
	end
	self.tipsList[2] = capacity
end

function WarehouseDepositTab_HD:_OnItemMove(itemMoveInfo)
	local oldLoc = itemMoveInfo.OldLoc
    local newLoc = itemMoveInfo.NewLoc
	-- 涉及到扩容箱的移动都需要及时刷新扩容箱的容量
	if (oldLoc and oldLoc.ItemSlot and oldLoc.ItemSlot:IsDepositorySlot()) or (newLoc and newLoc.ItemSlot and newLoc.ItemSlot:IsDepositorySlot()) then
		self:_RefreshExtCapacity()
	end
end

function WarehouseDepositTab_HD:_OnPostSortMultiPos()
	self:_RefreshExtCapacity()
end

-- 根据deposittab是否进入整理或管理模式做不同的处理
function WarehouseDepositTab_HD:_bInReplaceOrManageMode(bInReplaceOrManageMode)
	if bInReplaceOrManageMode then
		self:SetReplacementMode(bInReplaceOrManageMode)
		self._wtDFTipsAnchor:Collapsed()
	end
end

--endregion
--==================================================

-----------------------------------------------------------------------
--region Drag & Drop


function WarehouseDepositTab_HD:CheckNeedPermanentHighLight(dragDropInfo)
	local dragItem = dragDropInfo.item
	---@type EquipmentFeature
	local equipmentFeature = dragItem and dragItem:GetFeature(EFeatureType.Equipment)
	local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()
	local bTypeFit = ItemOperaTool.VerifyItemForTargetSlot(dragItem, self._bindDepositSlot, false)
	if not bExtendItem and self._bindDepositSlot and dragDropInfo.curDepositId ~= self._bindDepositSlot.SlotType and bTypeFit then
		if ItemOperaTool.CheckIsEquippedNotEmptyContainer(dragItem) then
			return ItemOperaTool.TryPlaceContainerItem(dragItem, self._bindDepositSlot)
		else
			return self._bindDepositSlot:TryFindLocationForItem(dragItem)
		end
	end
	return false
end

---@param dragDropInfo ItemDragDropInfo
function WarehouseDepositTab_HD:_OnGlobalItemDragStart(dragDropInfo, operation, pointerEvent)
	local item = dragDropInfo.item
	---@type EquipmentFeature
	local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
	local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()

	self._wtHighlight:Collapsed()	-- Collapsed first
	if self:CheckNeedPermanentHighLight(dragDropInfo) then
		self._wtHighlight:SelfHitTestInvisible()
		self._wtHighlight:SetPutInValid()
		return
	end
	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse then
		
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
		if bExtendItem and not dragDropInfo.bSimulation then
			local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
			if InventoryLogic.CheckExtItemReplacable(item, refExtItem) and not self:_ExtIsFull() then
				-- 可替换(且扩容箱不为满)
				self._wtHighlight:SelfHitTestInvisible()
				self._wtHighlight:PlayDropReplaceValidAnim()
			else
				-- -- 不可替换
				-- self._wtHighlight:SelfHitTestInvisible()
				-- self._wtHighlight:PlayDropReplaceInvalidAnim()
			end
		else

		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
		if bExtendItem then
			-- 可放入(第一个高亮)
			if self._tabIndex == Server.InventoryServer:GetCurDepositSlotNum() + 1 then
				self._wtHighlight:SelfHitTestInvisible()
				self._wtHighlight:PlayDropValidAnim()
			end
		end
	else
		-- 锁定
	end
end

function WarehouseDepositTab_HD:_OnGlobalItemDrop(dragDropInfo, operation, pointerEvent)
	-- Timer.DelayCall(
    --     0.2,
    --     function()
			self._wtHighlight:Collapsed()
    --     end
    -- )
end

function WarehouseDepositTab_HD:_OnGlobalItemDragCancelled(dragDropInfo, operation, pointerEvent)
	self._wtHighlight:Collapsed()
end

function WarehouseDepositTab_HD:OnDragEnter(inGeometry, inDragDropEvent, operation)
	self:_StartDragOverTimer()

	local dragItemView = operation.DefaultDragVisual
	if not dragItemView then
		return
	end

    local item = dragItemView.item
	---@type EquipmentFeature
	local equipmentFeature = item:GetFeature(EFeatureType.Equipment)
	local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()

	self._wtHighlight:SelfHitTestInvisible()

	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse then
		-- 主仓库只有放入的逻辑
		local warehouseSlot = Server.InventoryServer:GetSlot(ESlotType.MainContainer)
		if ItemOperaTool.TryPlaceItemInSlot(item, warehouseSlot) then
			-- self._wtHighlight:PlayPutInValidAnim()
			self._wtHighlight:SetDoubleValid()
		else
			self._wtHighlight:PlayDropInvalidAnim()
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
		if bExtendItem then
			local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
			if InventoryLogic.CheckExtItemReplacable(item, refExtItem) then
				-- 可替换
				self._wtHighlight:SetDoubleValid()
			else
				-- 不可替换(由红色闪烁高亮改成常驻红色高亮)
				-- self._wtHighlight:PlayDropReplaceInvalidAnim()
				self._wtHighlight:PlayDropReplaceExtInvalidAnim()
			end
		else
			local depositId = Server.InventoryServer:GetDepositIdByIndex(self._tabIndex)
			local extWarehouseSlot = Server.InventoryServer:GetSlot(depositId)
			if ItemOperaTool.TryPlaceItemInSlot(item, extWarehouseSlot) then
				-- self._wtHighlight:PlayPutInValidAnim()
				self._wtHighlight:SetDoubleValid()
			else
				self._wtHighlight:PlayDropInvalidAnim()
			end
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
		if bExtendItem then
			-- 可放入
			-- self._wtHighlight:PlayDropValidAnim()
			self._wtHighlight:SetDoubleValid()
		else
			self._wtHighlight:PlayDropInvalidAnim()
		end
	else
		-- 锁定
		self._wtHighlight:PlayDropInvalidAnim()
	end
end

function WarehouseDepositTab_HD:OnDragLeave(inDragDropEvent, operation)
	local curDragDropInfo = Module.CommonWidget:GetCurDragDropInfo()
	if not curDragDropInfo then
		return
	end

	self:_OnGlobalItemDragStart(curDragDropInfo, operation, inDragDropEvent)

	self:_StopDragOverTimer()
end

function WarehouseDepositTab_HD:OnDrop(inGeometry, inDragDropEvent, inOperation, bShowTips)
	bShowTips = setdefault(bShowTips, true)
	self:_StopDragOverTimer()

	--- 准备一些必要的数据
	local dragItemView = inOperation.DefaultDragVisual
	if not dragItemView then
		return false
	end

	---@type ItemBase
	local dragItem = dragItemView.item
	---@type EquipmentFeature
	local equipmentFeature = dragItem:GetFeature(EFeatureType.Equipment)
	local adapterFeature = dragItem:GetFeature(EFeatureType.Adapter)
	local bExtendItem = equipmentFeature and equipmentFeature:IsExtendItem()
	local bCanMove
	self.onDropState = true

	-- 处理配件的移动
	local function AccessoriesMove(item)
		local targetLoc = self._bindDepositSlot:TryFindLocationForItem(item)
		if targetLoc then
			local fastEquipCheckResult = Module.Inventory:CommonOnDropForFastEquip(inOperation, self._bindDepositSlot, targetLoc.X, targetLoc.Y, 1, true, bShowTips)
			if fastEquipCheckResult ~= Module.Inventory.Config.EFastEquipCheckResult.NoFastEquip then
				-- 进到这里，说明需要走快拆的逻辑（快拆本身可能成功或者不合法）
				-- local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
				-- if refExtItem then
				-- 	local tip = string.format(InventoryConfig.Loc.ItemHasBeenPutInExtBoxTip, dragItem.name, refExtItem.name)
					-- Module.CommonTips:ShowSimpleTip("456")
				-- end
				return true
			end
		else
			-- 空间不足
			-- local txt = string.format(InventoryConfig.Loc.ExtItemSpaceNotEnough, self._bindDepositSlot:GetSlotName())
			-- Module.CommonTips:ShowSimpleTip(txt)
		end
		return false
	end

	-- 处理DIY展位道具的移动
	local function DIYMove(item)
		local targetLoc = self._bindDepositSlot:TryFindLocationForItem(item)
		if targetLoc then
			Module.CollectionRoom:WithdrawShowCabinet(dragItem.cabinetId, dragItem.gridId, targetLoc)
		end
	end

	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse then
		bCanMove = ItemOperaTool.DoPlaceItem(dragItem, self._bindDepositSlot, bShowTips)
		if not bCanMove and adapterFeature then
			return AccessoriesMove(dragItem)
		end
		
		if not bCanMove and dragItem:IsCollectionCabinetItem() then
			return DIYMove(dragItem)
		end

		--BEGIN MODIFICATION @ VIRTUOS :
		if IsHD() and bCanMove then
			Module.CommonWidget:BeginToFocusItemView(dragItem)
		end
		--END MODIFICATION

		return bCanMove
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
		if bExtendItem then
			local subType1 = ItemHelperTool.GetSubTypeById(dragItem.id)

			local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
			local subType2 = ItemHelperTool.GetSubTypeById(refExtItem.id)

			-- if subType1 == subType2 then
			if InventoryLogic.CheckExtItemReplacable(dragItem, refExtItem) then
				-- 可替换
				local targetExtSlot = Server.InventoryServer:GetExtSlotByIndex(self._tabIndex)
				return InventoryLogic.StartExtItemReplacementInvalidation(dragItem, targetExtSlot, bShowTips)
				-- ItemOperaTool.DoEquipExtItem(dragItem, targetExtSlot)
			else
				-- 不可替换
				if bShowTips then
					if refExtItem and refExtItem.InSlot.SlotType == ESlotType.ExtensionVIP then
						Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.VipExtCantReplaceTips)
					else
						Module.CommonTips:ShowSimpleTip(InventoryConfig.Loc.OnlySameTypeExtItemCanReplace)
					end
				end
			end
			return false
		else
			local bTypeFit, matchReason = ItemOperaTool.VerifyItemForTargetSlot(dragItem, self._bindDepositSlot, false)
			local bHasSpace = self._bindDepositSlot:TryFindLocationForItem(dragItem)
			local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)

			if bTypeFit and bHasSpace then
				bCanMove = ItemOperaTool.DoPlaceItem(dragItem, self._bindDepositSlot, bShowTips)
				if not bCanMove and adapterFeature then
					return AccessoriesMove(dragItem)
				end

				if not bCanMove and dragItem:IsCollectionCabinetItem() then
					return DIYMove(dragItem)
				end

				--BEGIN MODIFICATION @ VIRTUOS :
				if IsHD() and bCanMove then
					Module.CommonWidget:BeginToFocusItemView(dragItem)
				end
				--END MODIFICATION
				
				-- 道具从该入口放入扩容箱，有特殊的Tips
				---@param res pb_CSDepositEquipPropRes
				local function fOnRequestCallback(res)
					if res.result == 0 then
						local itemName = dragItem.name
						local extName = refExtItem.name
						local tip = string.format(InventoryConfig.Loc.ItemHasBeenPutInExtBoxTip, itemName, extName)
						if bShowTips then
							Module.CommonTips:ShowSimpleTip(tip)
						end
					end
				end
				Server.InventoryServer:SyncInventoryChanged(fOnRequestCallback)
				return bCanMove
			else
				-- 失败则根据原因展示Tip
				local txt
				if not bTypeFit then
					if matchReason == ItemSlot.EItemFitReason.QualityNotFit then
						txt = ItemOperaTool.Loc.ItemQualityNotFit
					else
						txt = string.format(InventoryConfig.Loc.ExtItemDontSupportThisItem, refExtItem.name)
					end
				elseif not bHasSpace then
					-- txt = string.format(InventoryConfig.Loc.ExtItemSpaceNotEnough, refExtItem.name)
				end
		
				if txt and bShowTips then
					Module.CommonTips:ShowSimpleTip(txt)
				end

				-- 失败则跳转至原位置
				if dragItem and dragItem.InSlot and dragItem.InSlot.SlotType >= 2 and dragItem.InSlot.SlotType <= 10 then
					local index = Server.InventoryServer:GetIndexByDepositId(dragItem.InSlot.SlotType)
					if index > 0 then
						InventoryConfig.Events.evtJumpToExtSlot:Invoke(index, dragItem)
					end
				end
			end
			return false
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
		if bExtendItem then
			local newExtId = Server.InventoryServer:GetAvailableExtId()
			local targetExtSlot = Server.InventoryServer:GetSlot(newExtId)
			return ItemOperaTool.DoEquipExtItem(dragItem, targetExtSlot, bShowTips)
		end
	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Locked then

	end
	return false
end

function WarehouseDepositTab_HD:_OnDragOverForAWhile()
	if self._dragStayCallback then
		self._dragStayCallback(self._tabIndex)
	end
end

function WarehouseDepositTab_HD:_StartDragOverTimer()
	self:_StopDragOverTimer()

	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Warehouse
	or self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
		self._timerHandle = Timer.DelayCall(WarehouseDepositTab_HD.DRAG_OVER_DELAY, self._OnDragOverForAWhile, self)
	end
end

function WarehouseDepositTab_HD:_StopDragOverTimer()
	Timer.CancelDelay(self._timerHandle)
    self._timerHandle = nil
end

function WarehouseDepositTab_HD:ChangeDropState()
	self.onDropState = false
end

function WarehouseDepositTab_HD:PlayLeftInAnim()
	self:PlayAnimation(self.WBP_WarehouseExpansionButton_left_in, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

function WarehouseDepositTab_HD:PlayLeftOutAnim()
	self:PlayAnimation(self.WBP_WarehouseExpansionButton_left_out, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
end

-- vip扩容箱在有权限和无权限状态下不同表现(一个ms都没活就无了)
function WarehouseDepositTab_HD:SetDepositTabState(extItem)
	self:Collapsed()
	-- local expiration = extItem and extItem.expireTimesTamp > 0
	-- if not expiration or ItemBaseTool.GetItemExpiredState(extItem) then
	-- 	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
	-- 		self:Collapsed()
	-- 	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.VipDeposit then
	-- 		self:Visible()
	-- 		self:SetCppValue("Type", 3)	-- Vip Unlock
	-- 		self._wtGreyMask:SelfHitTestInvisible()
	-- 		local extSlot = Server.InventoryServer:GetSlot(ESlotType.VipDepositContainer)
	-- 		if extSlot and extSlot and (extSlot:GetUsedCapacity() > 0) then
	-- 			-- 判断当前时间
	-- 			local currentData = Facade.ClockManager:GetFormattedServerDate()
	-- 			local lastData = Facade.ConfigManager:GetUserString("ClickData", "")
	-- 			if currentData ~= lastData then
	-- 				self._reddotIns = Module.ReddotTrie:CreateReddotIns(self, EReddotType.Normal, false, EReddotPlaceMode.RightTop)
	-- 				self._reddotIns:SetReddotVisible(true)
	-- 			end
	-- 		end
	-- 	end
	-- else
	-- 	if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.ExtWarehouse then
	-- 		self:Visible()
	-- 		self:SetCppValue("Type", 0)
	-- 		self._wtGreyMask:Collapsed()
	-- 	elseif self._tabStatus == WarehouseDepositTab_HD.ETabStatus.VipDeposit then
	-- 		self:Collapsed()
	-- 	end
	-- end
end

function WarehouseDepositTab_HD:IsShowInPanel()
	if self.Visibility == ESlateVisibility.Collapsed or self.Visibility == ESlateVisibility.Hidden then
		return false
	else
		return true
	end
end

function WarehouseDepositTab_HD:SetReplacementMode(bInReplaceMode)
	self._bReplaceMode = bInReplaceMode
	self:SetReplacement(bInReplaceMode)
end

--endregion
-----------------------------------------------------------------------

--BEGIN MODIFICATION @ VIRTUOS : ExtManage by Gamepad
function WarehouseDepositTab_HD:_InitOrderListShortCut()
	-- 开始移动Tab前禁用导航按键
	WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoNav_NoA, self)

	if not self._shortCut_OrderListUp then
		self._shortCut_OrderListUp = self:AddInputActionBinding("Setting_OrderListUp", EInputEvent.IE_Pressed, self._MoveOrderListUp, self, EDisplayInputActionPriority.UI_Stack)
	end

	if not self._shortCut_OrderListDown then
		self._shortCut_OrderListDown = self:AddInputActionBinding("Setting_OrderListDown", EInputEvent.IE_Pressed, self._MoveOrderListDown, self, EDisplayInputActionPriority.UI_Stack)
	end
	-- 绑定输入
	local summaryList = {}
	table.insert(summaryList, {actionName = "AcceptMoveDepositeTab_Gamepad", func = self._FinishOrderList, caller = self, bUIOnly = false, bHideIcon = false})
	table.insert(summaryList, {actionName = "BackMoveDepositeTab_Gamepad", func = self._CancelOrderList, caller = self, bUIOnly = false, bHideIcon = false})
	Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, true, true)
end

function WarehouseDepositTab_HD:_RemoveOrderListShortCut()
	WidgetUtil.DisableDynamicNavConfigsbyWidget(self)

	if self._shortCut_OrderListUp then
		self:RemoveInputActionBinding(self._shortCut_OrderListUp)
		self._shortCut_OrderListUp = nil
	end

	if self._shortCut_OrderListDown then
		self:RemoveInputActionBinding(self._shortCut_OrderListDown)
		self._shortCut_OrderListDown = nil
	end
end

function WarehouseDepositTab_HD:_BeginOrderList()
	if not WidgetUtil.IsGamepad() then
		return
	end

	if self._bInGamepadMoveOrderList == true then
		return
	end

	self._bInGamepadMoveOrderList = true
	self:_InitOrderListShortCut()
	self:_HideExpansionTips()
	self:SetRemoveBtnVisible(false)

	if self._gamepadMoveBegin then
		local cellGeo = self:GetCachedGeometry()
		self._gamepadMoveBegin(cellGeo)
	end

end

function WarehouseDepositTab_HD:_FinishOrderList()
	if self._bInGamepadMoveOrderList == false then
		return
	end

	self:_RemoveOrderListShortCut()

	if self._gamepadMoveFinish then
		local cellGeo = self:GetCachedGeometry()
		self._gamepadMoveFinish(cellGeo)
	end

	self._bInGamepadMoveOrderList = false
	-- 恢复到hover状态
	self:_SetInputSummaryOnGamepadHover()
	self:SetRemoveBtnVisible(true)
end

function WarehouseDepositTab_HD:_CancelOrderList()
	if self._bInGamepadMoveOrderList == false then
		return
	end

	self:_RemoveOrderListShortCut()

	if self._gamepadMoveCancel then
		local cellGeo = self:GetCachedGeometry()
		self._gamepadMoveCancel(cellGeo, self)
	end

	self._bInGamepadMoveOrderList = false
	-- 恢复到hover状态
	self:_SetInputSummaryOnGamepadHover()
	self:SetRemoveBtnVisible(true)
end

function WarehouseDepositTab_HD:_MoveOrderListUp()
	if self._gamepadMove and self._bInGamepadMoveOrderList == true then
		local cellGeo = self:GetCachedGeometry()
		self._gamepadMove(cellGeo, self, -1)
	end
end

function WarehouseDepositTab_HD:_MoveOrderListDown()
	if self._gamepadMove and self._bInGamepadMoveOrderList == true then
		local cellGeo = self:GetCachedGeometry()
		self._gamepadMove(cellGeo, self, 1)
	end
end

function WarehouseDepositTab_HD:BindGamepadMoveBeginDelegate(callback, caller)
	self._gamepadMoveBegin = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:BindGamepadMoveFinishDelegate(callback, caller)
	self._gamepadMoveFinish = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:BindGamepadMoveCancelDelegate(callback, caller)
	self._gamepadMoveCancel = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:BindGamepadMoveDelegate(callback, caller)
	self._gamepadMove = CreateCallBack(callback, caller)
end

function WarehouseDepositTab_HD:_OnFocusReceived()
	if self._bInExtManagementMode == true then

		if WidgetUtil.IsGamepad() then
			-- 用手柄导航时就默认更新选中效果
			self:OnClicked()
		end

		self:_EnableDepositTabGamepadShortCut(true)
		self:_SetInputSummaryOnGamepadHover()
	end
end

function WarehouseDepositTab_HD:_SetInputSummaryOnGamepadHover()
	local summaryList = {}
	table.insert(summaryList, {actionName = "BeginMoveDepositeTab_Gamepad", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
	table.insert(summaryList, {actionName = "RemoveDepositeTab_Gamepad", func = nil, caller = self, bUIOnly = true, bHideIcon = false})
	InventoryConfig.Events.evtWareHouseDepositTabOnFocusReceived:Invoke(summaryList)
end

function WarehouseDepositTab_HD:_OnFocusLost()
	if self._bInExtManagementMode == true then
		self:_EnableDepositTabGamepadShortCut(false)
		InventoryConfig.Events.evtWareHouseDepositTabOnFocusLost:Invoke()
	end
end

function WarehouseDepositTab_HD:_EnableDepositTabGamepadShortCut(bEnable)
	if bEnable then
		-- 开始调整
		if not self._beginMoveByGamepadHandler then
			self._beginMoveByGamepadHandler = self:AddInputActionBinding("BeginMoveDepositeTab_Gamepad", EInputEvent.IE_Pressed, self._BeginMoveTabByGamepad, self, EDisplayInputActionPriority.UI_Stack)
		end
		-- 移除
		if not self._removeDepositByGamepadHandler then
			self._removeDepositByGamepadHandler = self:AddInputActionBinding("RemoveDepositeTab_Gamepad", EInputEvent.IE_Pressed, self._RemoveDepositByGamepad, self, EDisplayInputActionPriority.UI_Stack)
		end
	else
		-- 开始调整
		if self._beginMoveByGamepadHandler then
			self:RemoveInputActionBinding(self._beginMoveByGamepadHandler)
			self._beginMoveByGamepadHandler = nil
		end
		-- 移除
		if self._removeDepositByGamepadHandler then
			self:RemoveInputActionBinding(self._removeDepositByGamepadHandler)
			self._removeDepositByGamepadHandler = nil
		end
	end
end

function WarehouseDepositTab_HD:_BeginMoveTabByGamepad()
	if WidgetUtil.IsGamepad() then
		if self._bInExtManagementMode == true and self._bInGamepadMoveOrderList == false then
			self:_BeginOrderList()
		end
	end
end

function WarehouseDepositTab_HD:_RemoveDepositByGamepad()
	if WidgetUtil.IsGamepad() then
		if self._bInExtManagementMode == true and self._bInGamepadMoveOrderList == false then
			local refExtItem = Server.InventoryServer:GetExtItemByIndex(self._tabIndex)
			ItemOperaTool.DoUnEquipItem(refExtItem)
		end
	end
end

function WarehouseDepositTab_HD:_HandleInputTypeChanged(CurInputType)
	if CurInputType ~= EGPInputType.Gamepad then
		if self._bInGamepadMoveOrderList == true then
			self:_CancelOrderList()
		end
	end
end

function WarehouseDepositTab_HD:SetInExtArrangeMode(bInExtArrageMode)
	if not IsHD() then
		return 
	end

	self:SetCppValue("bIsFocusable", true)

	if bInExtArrageMode then
		local bIsEmptySlot = self._bindDepositSlot.SlotType == ESlotType.None
		if bIsEmptySlot then
			self:SetCppValue("bIsFocusable", false)
		end
	else
		if self._tabStatus == WarehouseDepositTab_HD.ETabStatus.Available then
			self:SetCppValue("Type", 1)	-- Available
			if self._tabIndex == Server.InventoryServer:GetCurDepositSlotNum() + 1 then
				self:SetBright(true)
				-- self:SetIsFocusable(true)
				if not self:GetIsLonger() then
					self:SetCppValue("bIsFocusable", true)
				end
			else
				self:SetBright(false)
				self:SetCppValue("bIsFocusable", false)
			end
		end
	end
end
--END MODIFICATION

return WarehouseDepositTab_HD