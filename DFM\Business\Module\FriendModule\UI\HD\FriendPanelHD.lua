----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMFriend)
local FriendLogic = require "DFM.Business.Module.FriendModule.Logic.FriendLogic"
----- LOG FUNCTION AUTO GENERATE END -----------

--- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local UGPUINavigationUtils = import "GPUINavigationUtils"
--- END MODIFICATION

---@class FriendPanelHD : LuaUIBaseView
local FriendPanelHD = ui("FriendPanelHD")
local DiscordBindLogic = require "DFM.Business.Module.FriendModule.Logic.DiscordBindLogic"
local Functional       = require "DFM.Business.DataStruct.Common.Base.Functional"
local LIST_MAX_NUMBER = 16

---@enum 好友列表类型枚举
local EPanelType = {
    Game = 1,
    Open = 2,
    Discord = 3,
}

function FriendPanelHD:Ctor()
    self._wtFriendPanel = self:Wnd("wtFriendPanel", UIWidgetBase)
    self._wtFindPanel = self:Wnd("wtFindPanel", UIWidgetBase)
    self._wtPageText = self:Wnd("wtPageText", UITextBlock)

    self._wtFriendEmptyPanel = self:Wnd("wtNoAnything", UIWidgetBase)
    self._wtFindEmptyPanel = self:Wnd("EmptySlot1", UIWidgetBase)

    self._wtLeftBtn = self:Wnd("wtLeftBtn", UIButton)
    self._wtLeftBtn:Event("OnClicked", self._OnBtnLeftClick, self)
    self._wtRightBtn = self:Wnd("wtRightBtn", UIButton)
    self._wtRightBtn:Event("OnClicked", self._OnBtnRightClick, self)

    self._wtChangeTypeTab = self:Wnd("wtChangeTypeTab", UIWidgetBase)
    self._wtChangeTypeTab:Event("OnTabIndexChanged", self._OnChangeTypeTab, self)

    self._wtToFindFriend = self:Wnd("DFRichTextBlock_0", UITextBlock)
    
    local function OnHighFrequency()
        Module.CommonTips:ShowSimpleTip(Module.Activity.Config.Loc.HighFrequency)
    end
    local fDiscordCallback = CreateCallBack(Functional.HighFrequencyFilter(self._OnDiscordBindBtnClick, 10, OnHighFrequency), self)

    --- Discord账号绑定按钮
    self._discordBindBtn = self:Wnd("WBP_CommonButtonV3S1_91", DFCommonButtonOnly)
    self._discordBindBtn:BP_SetMainTitle(Module.Friend.Config.DiscordBindPopText[4])
    self._discordBindBtn:Event("OnClicked", fDiscordCallback, self)


    --- BEGIN MODIFICATION @ VIRTUOS
    self._wtAddFriendPanel = self:Wnd("wtAddFriendPanel", DFCommonButtonOnly)
    self._wtAddFriendPanel:Event("OnClicked", self._OnBtnAddFriendClick, self)

    self._wtBlackPlayer = self:Wnd("wtBlackPlayer", DFCommonButtonOnly)
    self._wtBlackPlayer:Event("OnClicked", self._OnBtnBlackListClick, self)

    self._wtToFindPanel = self:Wnd("WBP_CommonButtonV2S2_37", DFCommonButtonOnly)
    self._wtToFindPanel:Event("OnClicked", self._OnBtnAddFindFriendClick, self)

    if IsHD() then
        self._wtRootContainer = self:Wnd("DFCanvasPanel_29", UIWidgetBase)
        self._wtPageKeyIcon = self:Wnd("KeyIcon_Page_Gamepad", HDKeyIconBox)
    end

    self._wtPingtaiTxt = self:Wnd("DFTextBlock", UITextBlock)
    self._wtPingtaiTxt:SetText(string.format(Module.Friend.Config.OpenFriend,
        Module.Friend.Config.OpenFriendTxtTbl[Server.SDKInfoServer:GetChannel()]))
    --- END MODIFICATION

    local fOnChangedCallbackIns = CreateCallBack(self._OnValueChange, self)
    self._wtPlayerInput = UIUtil.WndInputBoxWithBtn(self, "wtFindFriendBox", -1, nil, fOnChangedCallbackIns)
    
    self._wtSearchTxt = self._wtPlayerInput:Wnd("wtDFEditableTextBox", UITextBlock)

    if IsHD() and self._wtPlayerInput then --- MODIFICATION @ VIRTUOS
        self._wtPlayerSearchBox = self._wtPlayerInput:Wnd("wtDFEditableTextBox", UIWidgetBase)
    end
    
    self._wtFriendList = UIUtil.WndScrollGridBox(self, "wtFriendList", self._OnGetFriendCount, self._OnProcessFriendWidget)

    self._wtFindGamePlayer =  self:Wnd("wtFindGamePlayer",UIWidgetBase)
    self._wtFindOpenPlayer =  self:Wnd("wtFindOpenPlayer",UIWidgetBase)

    self._wtGamePanel = self:Wnd("wtGamePanel", UIWidgetBase)
    self._wtOpenPanel = self:Wnd("wtOpenPanel", UIWidgetBase)

    self._gameFriendList = Server.FriendServer:GetGameFriendPanelList()
    self._openFriendList = Server.FriendServer:GetOpenFriendPanelList()
    self._discordFriendList = Server.FriendServer:GetDiscordFriendPanelList()
    self._friendList = {}
    self._index = 1
    self._maxPage = 1
    self._friendIndex = 1
    self._panelType = EPanelType.Game

    self._gameFriendSearch = {}
    self._openFriendSearch = {}
    self:RefreshBlackBtn()
    self:BuildTopTabData()
    
    
    self._wtChangeTypeTab = UIUtil.WndTabGroupBox(self, "wtChangeTypeTab", self.GetTabItemCount, self.ProcessTabWidget, self._OnChangeTypeTab)
    
    if IsHD() and self._wtChangeTypeTab then --- MODIFICATION @ VIRTUOS
        self._wtTabKeyIconLeft = self._wtChangeTypeTab:Wnd("WBP_GamepadKeyIconBox_Left", HDKeyIconBox)
        self._wtTabKeyIconRight = self._wtChangeTypeTab:Wnd("WBP_GamepadKeyIconBox_Right", HDKeyIconBox)
    end
end

-----------------------------------------------------------------------
--region 生命周期

function FriendPanelHD:OnOpen()
    local key = string.format("FriendMessage_Type%s", 2)
    local data = Module.ReddotTrie:GetReddotData(EReddotTrieObserverType.Friend, key)
    self._reddot= Module.ReddotTrie:RegisterStaticReddotDot(self._wtAddFriendPanel, {{reddotData = data,reddotStyle={reddotType=EReddotType.Normal}}})
    self:AddListeners()
end

function FriendPanelHD:OnShowBegin()
    self:RefreshFriendPanel()
    
    if IsHD() then
        self:_EnableGamepadFeature()
    end

    -- PS5术语："平台游戏好友"区分大小写
    if IsPS5Family() then
        self:_SetTabFontStyleByID(0, "PSHeader_01")
    end
end

function FriendPanelHD:OnShow()
    if IsBuildRegionGlobal() then
        DiscordBindLogic.CheckDiscordFriendBindState()
    end
end

function FriendPanelHD:OnHideBegin()
    if IsHD() then
        self:_DisableGamepadFeature()
    end
end

function FriendPanelHD:OnHide()
    self:_RemoveTickTimer()
end

-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
function FriendPanelHD:OnClose()
    self._nothingIns = nil
    self:_RemoveTickTimer()
    Facade.UIManager:ClearSubUIByParent(self, self._wtFriendEmptyPanel)
    Facade.UIManager:ClearSubUIByParent(self, self._wtFindEmptyPanel)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 顶栏Tab

--- 初始顶栏tab数据
function FriendPanelHD:BuildTopTabData()
    --- 游戏好友信息
    self._TopTabData = {
        {keyText = Module.Friend.Config.GameFriend,
        iconPath = Module.Friend.Config.FriendChannelIcon[EChannelType.kChannelNone]},
    }

    --- 添加平台好友信息
    if self:CheckCanShowChannelFriend() and not IsConsole() then
        local OpenStr  = Module.Friend.Config.OpenFriendTxtTbl [Server.SDKInfoServer:GetChannel()]
        local OpenIcon = Module.Friend.Config.FriendChannelIcon[Server.SDKInfoServer:GetChannel()]  --- 显示Icon 未启用
        if OpenStr then
            table.insert(self._TopTabData, {keyText = string.format(Module.Friend.Config.OpenFriend, OpenStr), iconPath = OpenIcon})
        else
            table.insert(self._TopTabData, {keyText = Module.Friend.Config.OpenFriendStr, iconPath = OpenIcon})
        end
    end

    --- 主机平台好友信息
    if IsConsole() then
        --- XSX需要作为主要好友列表显示
        self._panelType = EPanelType.Open
        table.insert(self._TopTabData, 1, {keyText = Module.Friend.Config.OpenFriendTxtTbl[Server.SDKInfoServer:GetChannel()]})
        self._wtPlatformSearchText = self:Wnd("DFTextBlock", UITextBlock)
        self._wtPlatformSearchText:SetText(Module.Friend.Config.OpenFriendTxtTbl[Server.SDKInfoServer:GetChannel()])
    end

    --- 海外Discord好友信息
    if self:CheckCanShowDiscordFriend() and not IsConsole() then
        local OpenStr  = Module.Friend.Config.OpenFriendTxtTbl [EChannelType.kChannelDiscord]
        local OpenIcon = Module.Friend.Config.FriendChannelIcon[EChannelType.kChannelDiscord]
        table.insert(self._TopTabData, {keyText = string.format(Module.Friend.Config.OpenFriend, OpenStr), iconPath = OpenIcon})
    end

    --- 控制Tab元素宽度
    if #self._TopTabData >= 3 then
        self._wtChangeTypeTab:SetItemOptionSize(FVector2D(300, 88.0))
    end
end

function FriendPanelHD:GetTabItemCount()
    -- local tabNum = 1
    -- -- 平台or主机 好友
    -- if self:CheckCanShowChannelFriend() or IsConsole() then tabNum = tabNum + 1 end
    -- --- She3.0 海外新增Discord平台
    -- if IsBuildRegionGlobal() then tabNum = tabNum + 1 end

    -- return tabNum
    return #self._TopTabData
end

-- 判断后台是否开启了平台好友的功能
function FriendPanelHD:CheckCanShowChannelFriend()
    local  checkResult =  Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemOpenFriend)
    return checkResult == EFirstLockResult.Open and not IsBuildRegionGlobal()
end

-- 判断后台是否开启了Discord好友的功能
function FriendPanelHD:CheckCanShowDiscordFriend()
    local loginChannel  =  Server.SDKInfoServer:GetChannel()
    local  checkResult  =  Module.ModuleSwitcher:CheckModuleSwitcher(SwitchSystemID.SwitchSystemOpenFriend)-- TODO
    return checkResult == EFirstLockResult.Open and IsBuildRegionGlobal()
end

function FriendPanelHD:ProcessTabWidget(position, widget)
    local dataIdx = position + 1
    local topTabData = self._TopTabData[dataIdx]
    if not topTabData then
        return
    end
    
    --- She3.0 标签文字改图标/未实装
    -- if topTabData.iconPath and topTabData.iconPath ~= "" then
    --     widget:AsyncSetImageIconPathAllState(topTabData.iconPath, true)
    --     widget.wtCommonCheckBtn:SetIsShowMainTitle(false)
    --     widget.wtCommonCheckBtn:SetIsShowIcon(true)
    --     widget:SetIsLocked(false)
    -- else
    -- end
    widget:SetMainTitle(topTabData.keyText)
end

--- 设置标题文字样式（PS5用
function FriendPanelHD:_SetTabFontStyleByID(index, styleID)
    local mainTab = self._wtChangeTypeTab:GetItemWidgetByIndex(index)
    if mainTab == nil then
        return
    end

    local textBlockWidget = mainTab:Wnd("DFCommonCheckButton", DFCommonButtonOnly):Wnd("TextBlockMain", UITextBlock)
    if textBlockWidget and textBlockWidget.SetFontStyleID then
        textBlockWidget:SetFontStyleID(styleID)
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 响应事件

function FriendPanelHD:AddListeners()
    self:AddLuaEvent(Server.FriendServer.Events.evtFriendListGetEnd, self.RefreshFriendPanel, self)
    self:AddLuaEvent(Server.FriendServer.Events.evtBlackList, self.RefreshBlackBtn, self)  
    self:AddLuaEvent(DiscordBindLogic.evtDiscordBindFriendSuccess, self.RefreshFriendPanel, self)
end

function FriendPanelHD:_RemoveTickTimer()
    if self._friendListRefresh then
        self._friendListRefresh:Release()
        self._friendListRefresh = nil
    end
end

function FriendPanelHD:RefreshBlackBtn()
    local blackList = Server.FriendServer:GetBlackListData()
    if #blackList == 0 then
        self._wtBlackPlayer:Collapsed()
    else
        self._wtBlackPlayer:Visible()
    end
end

function FriendPanelHD:_OnBtnAddFriendClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendAddMainPanel, nil, nil)
end

function FriendPanelHD:_OnDiscordBindBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendDiscordBindPop, nil, nil)
end

function FriendPanelHD:_OnBtnAddFindFriendClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendAddMainPanel, nil, nil, self._wtSearchTxt:GetText())
    self._wtSearchTxt:SetText("")
end

function FriendPanelHD:_OnBtnBlackListClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.FriendAddBlack, nil, nil)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 好友列表

function FriendPanelHD:_isOpenFindPanel(isOpen)
    if isOpen then
        self._wtFriendPanel:Collapsed()
        self._wtFindPanel:SelfHitTestInvisible()
    else
        self._wtFriendPanel:SelfHitTestInvisible()
        self._wtFindPanel:Collapsed()
    end
end

function FriendPanelHD:_OnChangeTypeTab(index)
    --- 主机好友第一位显示
    if IsConsole() then
        local indexToPanelType = {
            [0] = EPanelType.Open,
            [1] = EPanelType.Game,
            [2] = EPanelType.Discord,
        }
        self._panelType = indexToPanelType[index]
    elseif IsBuildRegionGlobal() then
        local indexToPanelType = {
            [0] = EPanelType.Game,
            [1] = EPanelType.Discord,
        }
        self._panelType = indexToPanelType[index]
    else
        self._panelType = index + 1
    end

    -- PS5术语："平台游戏好友"区分大小写
    if IsPS5Family() then
        self:_SetTabFontStyleByID(0, "PSHeader_01")
    end

    self:RefreshFriendPanel()
end

function FriendPanelHD:_OnBtnLeftClick()
    if self._index > 1  then
        self._index = self._index - 1
        self:RefreshPageNumber()
        self._wtFriendList:RefreshAllItems()

        --- BEGIN MODIFICATION @ VIRTUOS
        -- 翻页后设置默认导航
        if IsHD() and self._wtFriendListNavGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtFriendListNavGroup)
        end
        --- END MODIFICATION
    end
end

function FriendPanelHD:_OnBtnRightClick()
    if self._index < self._maxPage  then
        self._index = self._index + 1
        self:RefreshPageNumber()
        self._wtFriendList:RefreshAllItems()

        --- BEGIN MODIFICATION @ VIRTUOS
        -- 翻页后设置默认导航
        if IsHD() and self._wtFriendListNavGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtFriendListNavGroup)
        end
        --- END MODIFICATION
    end
end

function FriendPanelHD:RefreshPageNumber()
    self._maxPage = math.ceil(#self._friendList / LIST_MAX_NUMBER)
    local isOpen = self._maxPage > 1
    if isOpen then
        self._wtLeftBtn:Visible()
        self._wtRightBtn:Visible()
        self._wtPageText:Visible()
        local parms = {
            ["curNum"] = self._index,
            ["maxNum"] = self._maxPage
        }
        self._wtPageText:SetText(StringUtil.Key2StrFormat(Module.Friend.Config.NumFormat, parms))

        if self._index == 1 then
            self._wtLeftBtn:Collapsed()
        end
        if self._index == self._maxPage then
            self._wtRightBtn:Collapsed()
        end
    else
        self._wtLeftBtn:Collapsed()
        self._wtRightBtn:Collapsed()
        self._wtPageText:Collapsed()
    end

    --- BEGIN MODIFICATION @ VIRTUOS
    -- 刷新KeyIcon的显示
    if IsHD() then
        self:_RefreshPageKeyIconVisibility()
    end
    --- END MODIFICATION
end

function FriendPanelHD:RefreshFriendPanel()

    self._gameFriendList = Server.FriendServer:GetGameFriendPanelList()
    self._openFriendList = Server.FriendServer:GetOpenFriendPanelList()
    self._discordFriendList = Server.FriendServer:GetDiscordFriendPanelList()
    
    self._index = 1
    if self._panelType == EPanelType.Game then
        self._friendList = Server.FriendServer:GetGameFriendPanelList()
    elseif self._panelType == EPanelType.Open then
        self._friendList = Server.FriendServer:GetOpenFriendPanelList()
    else
        self._friendList = Server.FriendServer:GetDiscordFriendPanelList()
    end
    
    if #self._friendList == 0 then
        self:CreateFriendNothingUIByText()
    else
        self._discordBindBtn:Collapsed()
        self._wtFriendEmptyPanel:Collapsed()
        if not self._friendListRefresh then
            self._friendListRefresh = Timer:NewIns(5, 0)
            self._friendListRefresh:AddListener(self._GetFriendState, self)
            self._friendListRefresh:Start()
        end
    end
    self:_GetFriendState()
    FriendLogic.SortFriendList(self._friendList)
    self:RefreshPageNumber()
    self._wtFriendList:RefreshAllItems()
end

function FriendPanelHD:_OnGetFriendCount()
    local size = #self._friendList or 0
    local num = size < self._index * LIST_MAX_NUMBER and (size - (self._index - 1) * LIST_MAX_NUMBER) or LIST_MAX_NUMBER
    return num
end

function FriendPanelHD:_OnProcessFriendWidget(i, widget)
    local friendInfo = self._friendList[LIST_MAX_NUMBER * (self._index - 1) + i]
    widget:ShowUI(friendInfo, self._panelType)
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 好友状态刷新

function FriendPanelHD:_GetFriendState()
    local friendIdList = {}
    local bContinue = true
    for index = self._friendIndex , self._friendIndex + 49 do
        if self._friendList[index] then
            table.insert(friendIdList, self._friendList[index].player_id)
        else
            self._friendIndex = 1
            bContinue = false
            break
        end
    end
    local CallFunction = CreateCallBack(self._RefreshFriendState, self, bContinue)
    Server.FriendServer:ReFetchFriendStateList(CallFunction, friendIdList)
end

function FriendPanelHD:_RefreshFriendState(bContinue, stateList)
    for i, info in ipairs(self._friendList) do
        local stateInfo = stateList[info.player_id]
        if stateInfo then
            self._friendList[i].state = stateInfo.state
            self._friendList[i].fighting_time = stateInfo.fighting_time
            self._friendList[i].member_num = stateInfo.member_num
            self._friendList[i].team_id = stateInfo.team_id
            self._friendList[i].mode_info = stateInfo.mode_info
            self._friendList[i].plat = stateInfo.PlatID
        end
    end
    if bContinue then
        self:_GetFriendState()
    else
        FriendLogic.SortFriendList(self._friendList)
        self._wtFriendList:RefreshVisibleItems()
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 搜索好友

function FriendPanelHD:_NoSearchFriend()
    self._wtFindEdit:SetText("")
    self:_isOpenFindPanel(false)
    self:_OnClearSearchList()
end

function FriendPanelHD:_OnClearSearchList()
    self._gameFriendSearch = {}
    self._openFriendSearch = {}
    --self._wtFindGamePlayer:RefreshAllItems()
    --self._wtFindOpenPlayer:RefreshAllItems()
    Facade.UIManager:RemoveSubUIByParent(self, self._wtFindGamePlayer)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtFindOpenPlayer)
end


function FriendPanelHD:_OnValueChange(text)
    local num ,str = StringUtil.GetRealWidth(text, 35)
    if num >= 35 then
      Module.CommonTips:AssignNextTipType_HD(true, false)
      Module.CommonTips:ShowSimpleTip(Module.Friend.Config.SearchFriendLimit)
      self._wtSearchTxt:SetText(str)
      text = str
    end
    Facade.UIManager:RemoveSubUIByParent(self, self._wtFindGamePlayer)
    Facade.UIManager:RemoveSubUIByParent(self, self._wtFindOpenPlayer)
    if string.isempty(text) then
        self:_isOpenFindPanel(false)
        self:_OnClearSearchList()
        self._wtAddFriendPanel:Visible()
        self:RefreshBlackBtn()
        return
    end
    self._wtBlackPlayer:Collapsed()
    self._wtAddFriendPanel:Collapsed()
    self._wtToFindFriend:SetText(string.format(Module.Friend.Config.GotoFindFriend, tostring(text)))
    local isNumber = Module.Friend:is_number(text)
    self:_isOpenFindPanel(true)
    self._gameFriendSearch = {}
    self._openFriendSearch = {}
 
    for _, info in ipairs(self._gameFriendList) do
        local playerIdStr = Module.RoleInfo:GOpenIdEncryption(ULuautils.GetUInt64String(info.player_id))
        if string.find(string.upper(info.nick_name) , string.upper(tostring(text))) then
            loginfo(string.format("GameFriendFind Pass NickName : %s  SearchText : %s", info.nick_name, text))
            table.insert(self._gameFriendSearch, info)
        elseif isNumber and string.find(playerIdStr, tostring(text)) then
            loginfo(string.format("GameFriendFind Pass PlayerId : %s  SearchText : %s", playerIdStr, text))
            table.insert(self._gameFriendSearch, info)
        end
    end

    for _, info in ipairs(self._openFriendList) do
        local playerIdStr = Module.RoleInfo:GOpenIdEncryption(ULuautils.GetUInt64String(info.player_id))
        if string.find(string.upper(info.nick_name), string.upper(tostring(text))) then
            loginfo(string.format("OpenFriendFind Pass NickName : %s  SearchText : %s", info.nick_name, text))
            table.insert(self._openFriendSearch, info)
        elseif isNumber and string.find(playerIdStr, tostring(text)) then
            loginfo(string.format("OpenFriendFind Pass PlayerId : %s  SearchText : %s", playerIdStr, text))
            table.insert(self._openFriendSearch, info)
        end
    end

    if #self._gameFriendSearch == 0 then
        self._wtGamePanel:Collapsed()  
    else
        self._wtGamePanel:Visible()
    end

    if #self._openFriendSearch == 0 then
        self._wtOpenPanel:Collapsed()
    else
        self._wtOpenPanel:Visible()
    end

    if #self._openFriendSearch == 0 and #self._gameFriendSearch == 0 then
        self:CreateFindNothingUIByText()
    else
        self._wtFindEmptyPanel:Collapsed()
    end

    for index, info in ipairs(self._gameFriendSearch) do
        local chatWidget, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.FriendBoxHD, self._wtFindGamePlayer, nil, info, EPanelType.Game)
        local widget = getfromweak(chatWidget)
        if widget then
            widget:Visible()
        end
    end

    for index, info in ipairs(self._openFriendSearch) do
        local chatWidget, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.FriendBoxHD, self._wtFindOpenPlayer, nil, info, EPanelType.Open)
        local widget = getfromweak(chatWidget)
        if widget then
            widget:Visible()
        end
    end
end

function FriendPanelHD:CreateFindNothingUIByText()
    if not self._instanceID1 then
        self._wtFindEmptyPanel:HitTestInvisible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtFindEmptyPanel)
        self._instanceID1 = instanceID
        local emptyBg = getfromweak(weakUIIns)
        if emptyBg then
            emptyBg:BP_SetText(Module.Friend.Config.NothingFindFriend)
            emptyBg:BP_SetTypeWithParam(1)
            emptyBg:Visible()
        end
    else
        self._wtFindEmptyPanel:HitTestInvisible()
    end
end

function FriendPanelHD:CreateFriendNothingUIByText()
    if not self._nothingIns then
        self._wtFriendEmptyPanel:HitTestInvisible()
        local weakUIIns, instanceID = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtFriendEmptyPanel)
        self._nothingIns = getfromweak(weakUIIns)
        if self._nothingIns then
            self._nothingIns:BP_SetTypeWithParam(1)
            self._nothingIns:Visible()
        end
    else
        self._wtFriendEmptyPanel:HitTestInvisible()
    end
    
    if self._panelType == EPanelType.Discord and not Module.Friend.Field:GetIsDiscordFriendBinded() then
        self:SetEmpty(1)
        self._discordBindBtn:Visible()
        self._nothingIns:BP_SetText(Module.Friend.Config.DiscordNotBinded)
    else
        self:SetEmpty(0)
        self._discordBindBtn:Collapsed()
        self._nothingIns:BP_SetText(Module.Friend.Config.NothingFriend)
    end
end

--endregion
-----------------------------------------------------------------------

-----------------------------------------------------------------------
--region 手柄相关

function FriendPanelHD:_OpenPreciseSearchByGamepad()
    if IsHD() and self._wtFindPanel:IsVisible() then
        self:_OnBtnAddFindFriendClick()
    end
end

-- 聚焦到好友搜索框
function FriendPanelHD:_FocusToPlayerSearchBox()
    -- 如果已经被Focus了，则不需要再被Focus
    if IsHD() and self._wtPlayerSearchBox and not UGPUINavigationUtils.IsWidgetInFocusPath(0, self._wtPlayerSearchBox) then
        self._wtPlayerSearchBox:SetFocus()
    end
end

function FriendPanelHD:_SelectNextFriendTab()
    if not IsHD() then
        return 
    end
    local index = self._wtChangeTypeTab:GetCurSelectedIndex()
    if index < self._wtChangeTypeTab:GetMaxIndex() then
        self._wtChangeTypeTab:SetTabIndex(index + 1, true)
        if IsHD() and self._wtFriendListNavGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtFriendListNavGroup)
        end
    end
end

function FriendPanelHD:_SelectPrevFriendTab()
    if not IsHD() then
        return 
    end
    local index = self._wtChangeTypeTab:GetCurSelectedIndex()
    if index > 0 then
        self._wtChangeTypeTab:SetTabIndex(index - 1, true)
        if IsHD() and self._wtFriendListNavGroup then
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._wtFriendListNavGroup)
        end
    end
end

function FriendPanelHD:_RefreshPageKeyIconVisibility()
    if IsHD() and self._wtPageKeyIcon then
        if self._maxPage > 1 then
            self._wtPageKeyIcon:Visible()
        else
            self._wtPageKeyIcon:Collapsed()
        end
    end
end

function FriendPanelHD:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 必须要显示的时候才注册gamepad相关feature
    if not self:IsVisible() then
        return
    end

    if self._wtRootContainer and (not self._wtFriendListNavGroup) then
        -- 注册导航组
        self._wtFriendListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtRootContainer, self, "Hittest")
        if self._wtFriendListNavGroup then
            -- 好友列表
            self._wtFriendListNavGroup:AddNavWidgetToArray(self._wtFriendList)
            -- 搜索框
            self._wtFriendListNavGroup:AddNavWidgetToArray(self._wtPlayerInput)
        end
    end

    -- 输入绑定
    self._addFriendInputHandle = self:AddInputActionBinding("AddFriend_Gamepad", EInputEvent.IE_Pressed, self._OnBtnAddFriendClick,self, EDisplayInputActionPriority.UI_Stack)
    self._openBlackInputHandle = self:AddInputActionBinding("OpenBlackPlayer_Gamepad", EInputEvent.IE_Pressed, self._OnBtnBlackListClick,self, EDisplayInputActionPriority.UI_Stack)
    self._preciseSearchInputHandle = self:AddInputActionBinding("PreciseSearch_Gamepad", EInputEvent.IE_Pressed, self._OpenPreciseSearchByGamepad,self, EDisplayInputActionPriority.UI_Stack)
    self._SelectNextFriendTabHandle = self:AddInputActionBinding("Common_SwitchToNextTab_Trigger", EInputEvent.IE_Pressed, self._SelectNextFriendTab,self, EDisplayInputActionPriority.UI_Stack)
    self._SelectPrevFriendTabHandle = self:AddInputActionBinding("Common_SwitchToPrevTab_Trigger", EInputEvent.IE_Pressed, self._SelectPrevFriendTab,self, EDisplayInputActionPriority.UI_Stack)
    self._PagePrevFriendList = self:AddInputActionBinding("Common_SwitchToPrevTab", EInputEvent.IE_Pressed, self._OnBtnLeftClick, self, EDisplayInputActionPriority.UI_Stack)
    self._PageNextFriendList = self:AddInputActionBinding("Common_SwitchToNextTab", EInputEvent.IE_Pressed, self._OnBtnRightClick, self, EDisplayInputActionPriority.UI_Stack)

    -- Key Icon
    self._wtAddFriendPanel:SetDisplayInputAction("AddFriend_Gamepad", true, nil, true)
    self._wtBlackPlayer:SetDisplayInputAction("OpenBlackPlayer_Gamepad", true, nil, true)
    self._wtToFindPanel:SetDisplayInputAction("PreciseSearch_Gamepad", true, nil, true)
    if self._wtChangeTypeTab:GetMaxIndex() > 0 then
        if self._wtTabKeyIconLeft then
            self._wtTabKeyIconLeft:SelfHitTestInvisible()
            self._wtTabKeyIconLeft:SetOnlyDisplayOnGamepad(true)
            self._wtTabKeyIconLeft:InitByDisplayInputActionName("Common_SwitchToPrevTab_Trigger", true, 0, false)
        end
    
        if self._wtTabKeyIconRight then
            self._wtTabKeyIconRight:SelfHitTestInvisible()
            self._wtTabKeyIconRight:SetOnlyDisplayOnGamepad(true)
            self._wtTabKeyIconRight:InitByDisplayInputActionName("Common_SwitchToNextTab_Trigger", true, 0, false)
        end

        if self._wtPageKeyIcon then
            self._wtPageKeyIcon:SetOnlyDisplayOnGamepad(true)
            self._wtPageKeyIcon:InitByDisplayInputActionName("SwitchPage_RightStick", true, 0, false)
        end
    end

    if not self._inputSummaries then
        self._inputSummaries = {
            {actionName = "Select_Gamepad", func = nil, caller = self, bUIOnly = true},
            {actionName = "SearchFriend_Gamepad", func = self._FocusToPlayerSearchBox, caller = self},
        }
        Module.CommonBar:SetBottomBarTempInputSummaryList(self._inputSummaries, false, false)
    end
end

function FriendPanelHD:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end
    if self._wtFriendListNavGroup then
        WidgetUtil.RemoveNavigationGroup(self)
        self._wtFriendListNavGroup = nil
    end

    -- 移除输入绑定
    self:RemoveInputActionBinding(self._addFriendInputHandle)
    self:RemoveInputActionBinding(self._openBlackInputHandle)
    self:RemoveInputActionBinding(self._preciseSearchInputHandle)
    self:RemoveInputActionBinding(self._SelectNextFriendTabHandle)
    self:RemoveInputActionBinding(self._SelectPrevFriendTabHandle)
    self:RemoveInputActionBinding(self._PagePrevFriendList)
    self:RemoveInputActionBinding(self._PageNextFriendList)

    if self._inputSummaries then
        self._inputSummaries = nil
        Module.CommonBar:RecoverBottomBarInputSummaryList()
    end
end
--endregion
-----------------------------------------------------------------------


return FriendPanelHD