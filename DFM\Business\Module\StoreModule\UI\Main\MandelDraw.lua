----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMStore)
----- LOG FUNCTION AUTO GENERATE END -----------

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
-- END MODIFICATION

---@class MandelDraw : LuaUIBaseView
local MandelDraw = ui("MandelDraw")
local StoreConfig = Module.Store.Config
local StoreLogic = require "DFM.Business.Module.StoreModule.Logic.StoreLogic"
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local WeaponHelperTool = require "DFM.StandaloneLua.BusinessTool.WeaponHelperTool"
local FAnchors = import "Anchors"
local UDFCommonMediaView = import "DFCommonMediaView"
local RuntimeIconTool = require "DFM.StandaloneLua.BusinessTool.RuntimeIconTool"
local FRTIParamData = import "RTIParamData"
-- local StoreLotteryProbDistributionItem = require "DFM.Business.DataStruct.StoreStruct.StoreLotteryProbDistributionItem"

function MandelDraw:Ctor()
    self.historyAllCount = 0
    self._prizeGroups = {}
    self.coreGroup = {}
    self.history = {}
    self.historyToShow = {}
    self.propToHistory = {}
    self.bHasRestore = false
    self.curSelectGroupIndex = 0
    self.mandelCount = 0
    self.keyCount = 0
    self.isPlayVideo = 0
    self.paddedGoodsAndDrawNum = 0
    self.lastClickDrawTime = 0
    self.bNeedUseKey = true
    self.bOpenOtherOverviewButton = false

    self._wtBoxNameText = self:Wnd("wtBoxNameText", UITextBlock)

    self._wtTextDrawOne_Mandel = self:Wnd("DFTextBlock_DrawOne_Mandel", UITextBlock)
    self._wtTextDrawOne_Key = self:Wnd("DFTextBlock_DrawOne_Key", UITextBlock)
    self._wtTextDrawTen_Mandel = self:Wnd("DFTextBlock_DrawTen_Mandel", UITextBlock)
    self._wtTextDrawTen_Key = self:Wnd("DFTextBlock_DrawTen_Key", UITextBlock)

    self._wtGuaranteedGroupHintText = self:Wnd("wtGuaranteedGroupHintText", UITextBlock)
    self._wtGuaranteedPropHintText = self:Wnd("wtGuaranteedPropHintText", UITextBlock)

    self._wtCannotRestoreHintText = self:Wnd("wtCannotRestoreHintText", UITextBlock)
    self._wtCannotRestoreHintImg = self:Wnd("wtCannotRestoreHintImg", UIWidgetBase)

    self._wtSingleActionBtn = self:Wnd("wtSingleActionBtn", UIButton)
    self._wtMultipleActionBtn = self:Wnd("wtMultipleActionBtn", UIButton)
    self._wtClosePrizesPanelBtn = self:Wnd("DFButton_149", UIButton)

    self._wtSingleActionBtn:Event("OnClicked", self._OnDrawOneClick, self)
    self._wtMultipleActionBtn:Event("OnClicked", self._OnDrawTenClick, self)
    self._wtClosePrizesPanelBtn:Event("OnClicked", self._OnClosePrizesPanelBtnClick, self)



    self._keyImage1 = self:Wnd("DFImage_1", UIWidgetBase)

    self._wtPlatformSizeBox_0 = self:Wnd("PlatformSizeBox_0", UIWidgetBase)

    self._wtDFBorder_0 = self:Wnd("DFBorder_0", UIWidgetBase)
    self._wtDFHorizontalBox_0 = self:Wnd("DFHorizontalBox_0", UIWidgetBase)

    self._wtPlatformPaddingBox_0 = self:Wnd("PlatformPaddingBox_0", UIWidgetBase)

    self._wtNameIcon = self:Wnd("DFImage_244", UIWidgetBase)

    --新UI框架
    self.uiNavIDList = {
        UIName2ID.StorePrizesList,

    }

    Facade.UIManager:RegSwitchSubUI(self, self.uiNavIDList)

    self._wtSingleActionBtn:BP_SetMainTitle(StoreConfig.Loc.MandelDrawOneTip)
    self._wtMultipleActionBtn:BP_SetMainTitle(StoreConfig.Loc.MandelDrawTenTip)

    self._wtVideoBtn = self:Wnd("wtPlayVideoBtn", UIButton)
    self._wtVideoBtn:Event("OnClicked", self._OnButtonVideoClick, self)

    self._wtVideoJump = self:Wnd("wtSkipBtn", CommonSkipOverBg)
    self._wtVideoJump:SetKeepSelfOnClick(true)
    self._wtVideoJump:BindClickEvent(self._OnButtonVideoJumpClick, self)



    -- ---@type DFMCommonMediaImage 视频播放控件
    -- self._mediaImage = self:Wnd("DFMCommonMediaImage_336", UIMediaImage)
    -- self._mediaImage:Collapsed()
    --视频专用
    ---@type CommonVideoComponent
    self._mediaComponent = self:Wnd("WBP_CommonVideoComponent", CommonVideoComponent)
    self._mediaComponent:InitComponent(false)
    if self._mediaComponent then
        self._mediaComponent:Collapsed()
    end

    --背景视频专用
    ---@type CommonVideoComponent
    self._mediaBackground = self:Wnd("WBP_CommonVideoComponent_1", CommonVideoComponent)
    self._mediaBackground:InitComponent(false)
    if self._mediaBackground then
        self._mediaBackground:HitTestInvisible()
    end

    self.showRewardsDetail = false

    self.core_time_assured = 0
    self.acquisition_guaranteed = 0
    self.open_times = 0

    self._keyImage6 = self:Wnd("DFImage_6", UIWidgetBase)
    self._keyImage4 = self:Wnd("DFImage_4", UIWidgetBase)
    self._DrawOneRoot = self:Wnd("DFCanvasPanel_0", UIWidgetBase)
    self._DrawTenRoot = self:Wnd("DFCanvasPanel_1", UIWidgetBase)

    -- self:HideGlobalUI()

    if self._wtVideoJump ~= nil then
        self._wtPCSkipTxt = self._wtVideoJump:Wnd("wtSkipText_PCOnly", UITextBlock)
        self._wtAnSkipTxt = self._wtVideoJump:Wnd("wtSkipText", UITextBlock)
        --初始化[跳过]文本
        if DFHD_LUA == 1 then
            if self._wtPCSkipTxt then
                self._wtPCSkipTxt:SetText(StoreConfig.Loc.StoreVideoSkip)
            end
        else
            if self._wtAnSkipTxt then
                self._wtAnSkipTxt:SetText(StoreConfig.Loc.StoreVideoSkipMobile)
            end
        end
    end

    --奖池总览按钮
    self._wtWBP_DFCommonButtonV2S2 = self:Wnd("wtWBP_DFCommonButtonV2S2", DFCommonButtonOnly)
    self._wtWBP_DFCommonButtonV2S2:Event("OnClicked", self._OnOpenPrizeOverviewButtonClicked, self)

    --概率和抽奖记录总览按钮
    self._wtCommonCheckInstruction = self:Wnd("wtCommonCheckInstruction", DFCommonButtonOnly)
    self._wtCommonCheckInstruction:Event("OnCheckStateChanged", self._OnOpenOtherOverviewButtonClicked, self)
    self._wtCheckBox = self._wtCommonCheckInstruction:Wnd("DFCheckBox_Icon", UICheckBox)

    --曼德尔名字
    self._wtMandelName = self:Wnd("wtMandelName",UITextBlock)

    --已拥有曼德尔砖数
    self._wtOwnedBoxCount = self:Wnd("wtOwnedBoxCount",UITextBlock)

    self._initSuccess = false
    self._prizeGroups = nil

    self.enablePlayFullScreenCG = UDFCommonMediaView.EnableCGMediaPlay()


    -- 右下角砖数量面板
    self._wtBrickCountPanel = self:Wnd("DFCanvasPanel_4", UIWidgetBase)

    -- 右下角选择奖励面板
    self._wtRewardChoosePanel = self:Wnd("DFVerticalBox_0", UIWidgetBase)
    self._wtRewardChoosePanelRedDot = Module.ReddotTrie:CreateReddotIns(self._wtRewardChoosePanel, nil, nil, nil, FVector2D(-40, -60))   -- 奖励面板红点
    -- 活跃砖挂饰展馆图标
    self._wtPendantGalleryIcon = self:Wnd("DFImage_126", UIImage)
    -- 活跃砖挂饰展馆个数展示
    self._wtPendantGalleryCount = self:Wnd("DFRichTextBlock_55", UITextBlock)
    -- 曼德尔砖高概率奖励选择加号
    self._wtUpRewardChoosePlus = self:Wnd("DFImage_122", UIWidgetBase)
    -- 曼德尔砖高概率武器图标展示
    self._wtUpRewardWeaponIcon = self:Wnd("DFImage_77", UIImage)
    -- 选择的头奖名称
    self._wtUpRewardItemName = self:Wnd("DFTextBlock_79", UITextBlock)

    -- 隐藏右下角砖数量面板
    self._wtBrickCountPanel:Collapsed()
    -- 显示右下角选择奖励面板
    self._wtRewardChoosePanel:SelfHitTestInvisible()
    self._wtPendantGalleryIcon:Collapsed()
    self._wtPendantGalleryCount:Collapsed()
    self._wtUpRewardChoosePlus:Collapsed()
    self._wtUpRewardWeaponIcon:Collapsed()

    -- 右下角曼德尔砖样式
    self:SetPrizeMode(0)

    -- 高概率奖励选择按钮
    self._wtUpRewardChooseBtn = self:Wnd("DFButton_129", UIButton)
    self._wtUpRewardChooseBtn:Event("OnClicked", self._OnUpRewardChooseBtnClick, self)

    self._wtUpRewardIcon = self:Wnd("WBP_SlotCompIconImage", UIWidgetBase):Wnd("wtMainIcon", UIImage)
    self._wtUpRewardIcon.OnIconLoaded:Add(CreateCPlusCallBack(self.OnUpRewardIconLoaded,self))

    -- 累抽奖励按钮
    self._wtContinuousRewardPanel = self:Wnd("DFCanvasPanel_12", UIWidgetBase)
    self._wtContinuousRewardPanelRedDot = Module.ReddotTrie:CreateReddotIns(self._wtContinuousRewardPanel, nil, nil, nil, FVector2D(-30, 0))   -- 累抽奖励按钮红点
    self._wtContinuousRewardBtn = self:Wnd("DFButton_448", UIButton)
    self._wtContinuousRewardBtn:Event("OnClicked", self._OnContinuousRewardBtnClick, self)
    self._wtImageContinuousReward = self:Wnd("DFImage_488", UIImage)
end

function MandelDraw:HideGlobalUI()
    if (IsBuildRegionGlobal() or IsBuildRegionGA()) or self.bNeedUseKey == false then
        self._keyImage1:Collapsed()
        self._keyImage4:Collapsed()
        self._keyImage6:Collapsed()
        self._wtTextDrawOne_Key:Collapsed()
        self._wtTextDrawTen_Key:Collapsed()
    end
end

-- UI构造时触发, 用于初始化外部传参UI数据逻辑，由UIManager触发（仅触发一次）
-- 子节点蓝图不会触发该方法
-- Module加载好子控件所需的图片后，将数据传入主UI界面
function MandelDraw:OnInitExtraData(mandelID, tabID)
    --mandelID open from other system
    --tabID with click second tab


    self._initSuccess = false
    if mandelID ~= nil then
        loginfo("[MandelDraw] OnInitExtraData, mandelID:" .. mandelID)
        self.openWithMandeID = mandelID
    else
        self.openWithMandeID = 0
    end

    local needOpenTabID = 1
    if tabID ~= nil then
        needOpenTabID = tabID
    end

    self.allDrawDatas = Server.StoreServer:GetDrawByType(1)
    if self.allDrawDatas ~= nil and #self.allDrawDatas >= 1 then
        self.drawData = self.allDrawDatas[needOpenTabID]
        self:SetUp(self.drawData.MandelItemId)
        self:SetVfx(self.drawData.MandelItemId)
        StoreLogic.SetLotteryUIArtLogo(self.drawData.MandelItemId, self._wtNameIcon)
    end

    self._mediaComponent:Collapsed()
    self._wtVideoJump:Collapsed()
    self._wtGuaranteedGroupHintText:Hidden()


    self:SetPanelPosition(1)

    Server.StoreServer:ClearBoxInfoCacheByMandelId(self.drawData.MandelItemId)

    local loadPrizeCallback = CreateCallBack(self.OnLoadPrizeCallback, self)
    Module.Store:LoadBlindboxPrizesWithOtherInfo(self.drawData.MandelItemId, loadPrizeCallback)

    -- 设置单抽数字
    self._wtTextDrawOne_Mandel:SetText(1)
    self._wtTextDrawOne_Key:SetText(1)
    self._jumpOneceCommitTranstion = false

    -- 上报打开曼德尔砖界面
    local boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(self.drawData.MandelItemId)
    if itemInfo then
        boxId = itemInfo.ConnectedPool or 0
    end
    LogAnalysisTool.ShopEnterMandelOpenUIFlow(boxId, EMandelOpenSource.Store)
end

function MandelDraw:OnLoadPrizeCallback(prizeGroup)
    self._initSuccess = true
    self:SetNewGiftDetail(prizeGroup)
    ---@type ItemBase[] 
    local allItem = {}
    for i,v in ipairs(prizeGroup) do
        local items = v.items
        if items and #items > 0 then
            for index, value in ipairs(items) do
                table.insert(allItem, value)
            end
        end
    end

    local rewards = {}

    for idx, item in ipairs(allItem) do
        -- local storeItem = StoreLotteryProbDistributionItem:NewIns()
        -- storeItem.ID = item.id
        -- storeItem.SpecialItem = item.id
        -- storeItem.SortIndex = idx
        -- storeItem.RewardItem = tostring(item.id)
        -- storeItem.CustomId = MandelId
        -- storeItem.CustomItemBase = item
        table.insert(rewards, item.id)
    end
    Module.Store.Field:SetMandelDrawItemList(rewards)
end

-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function MandelDraw:OnOpen()
    self:AddListeners()
    if self.drawData ~= nil then
        self.lotteryPoolID = 0
        local itemInfo = ItemConfigTool.GetItemConfigById(self.drawData.MandelItemId)

        if itemInfo then
            self.lotteryPoolID = itemInfo.ConnectedPool or 0

            if self._wtBoxNameText ~= nil then
                self._wtBoxNameText:SetText(itemInfo.name)
            end
        end

        self.drawBoxConfig = Server.StoreServer:GetLotteryBoxConfigByID(self.lotteryPoolID)
        -- 读取服务器发来的SpecialItemBuyConf表格，获取数据
        self.buyManderKeysConfig = Server.StoreServer:GetSpecialItemInfoByPresentItemId(self.drawData.LotteryKeyId)

        if self.drawBoxConfig ~= nil then

        end

    end

    -- 预加载曼德尔砖抽奖场景
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.LotteryV2, true, nil, nil, false, 30)
    Facade.LevelLoadManager:AsyncOperateStreamLevelsBySubStage(ESubStage.Lottery, true, nil, nil, false, 30)

    self._wtRewardChoosePanel:Collapsed()
end

function MandelDraw:AddListeners()
    self:AddLuaEvent(Module.Store.Config.evtMandelDrawKeysBought, self._RefreshCurrencyNum, self)
    self:AddLuaEvent(Module.Store.Config.evtStoreMainPanelRefreshCurrencyStateDone, self._OnEvtStoreMainPanelRefreshCurrencyStateDone, self) --商城主界面货币栏刷新完成
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelUpRewardChooseResult, self._OnEvtStoreMandelUpRewardChooseResult, self) -- 高概率奖励选择结果
end

function MandelDraw:RefreshCurDrawData()
    if self.drawData == nil then
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelData)
        return
    end

    --mandel
    self.allMandels = Server.CollectionServer:GetCollectionPropById(self.drawData.MandelItemId)
    if self.allMandels ~= nil then
        self.mandelCount = self.allMandels.num
    else
        self.mandelCount = 0
    end

    --key
    self.allMandelKeys = Server.CollectionServer:GetCollectionPropById(self.drawData.LotteryKeyId)
    if self.allMandelKeys ~= nil then
        self.keyCount = self.allMandelKeys.num
    else
        self.keyCount = 0
    end

    self._mandelItem = ItemBase:NewIns(self.drawData.MandelItemId)
    if self._mandelItem then
        self._wtMandelName:SetText(self._mandelItem.name)
    end

    self._wtOwnedBoxCount:SetText(tostring(self.mandelCount))

    self:_RefreshCurrencyNum(self.drawData.LotteryKeyId ,0)
end

function MandelDraw:_loadprize()
    -- loginfo("[MandelDraw] Loadprize")
    -- self._wtPrizesScrollBoxHistory:Collapsed()

    -- if self.bHasRestore then
    --     self._wtCannotRestoreHintText:SetText(StoreConfig.Loc.MandelTabItemOwnedTip)
    --     self._wtCannotRestoreHintText:Visible()
    --     self._wtCannotRestoreHintImg:Visible()
    -- else
    --     self._wtCannotRestoreHintText:SetText(StoreConfig.Loc.MandelTabItemCouldRepeatTip)
    --     self._wtCannotRestoreHintText:Visible()
    --     self._wtCannotRestoreHintImg:Collapsed()
    -- end

    -- if self._wtPlatformPaddingBox_0 ~= nil then
    --     self._wtPlatformPaddingBox_0:Hidden()
    -- end

    -- if self.emptyContent ~= nil then
    --     local emptyContent = getfromweak(self.emptyContent)
    --     if emptyContent ~= nil then
    --         emptyContent:Collapsed()
    --     end
    -- end
    -- if self.creatPrizeList ~= true then
    --     self:_CreatePrizeListByGroupId(1)
    --     self:_CreatePrizeListByGroupId(2)
    --     self.creatPrizeList = true
    -- end
end

function MandelDraw:SetNewGiftDetail(prizeGroups, openCount)
    if prizeGroups and  #prizeGroups > 0 then
        self.acquisition_guaranteed = 0
        self.core_time_assured = 0

        self._prizeGroups = prizeGroups

        for key, group in pairs(prizeGroups) do
            if group.core_flag == true and group.prob ~= 0 then
                self.core_time_assured = group.time_assured
                self.coreGroup = group
            end

            for index, item in ipairs(group.items) do
                if item.acquisition_guaranteed > 0 then
                    self.acquisition_guaranteed = item.acquisition_guaranteed
                    self.guaranteedItem = item
                end

                if item.restore_flag ~= nil and item.restore_flag == false then
                    self.bHasRestore = true
                end
            end
        end

        --open_count
        self.open_times = openCount
        if self.core_time_assured > 0 then
            self._wtGuaranteedGroupHintText:SetText(StringUtil.Key2StrFormat(StoreConfig.Loc.GuaranteedGroupHint,
               { remainCount = tostring(self.core_time_assured)} ))
            self._wtGuaranteedGroupHintText:SelfHitTestInvisible()
        end

        if self.acquisition_guaranteed > 0 then
            self._wtGuaranteedPropHintText:SelfHitTestInvisible()
            self._wtGuaranteedPropHintText:SetText(StringUtil.Key2StrFormat(StoreConfig.Loc.GuaranteedPropHint,
                { remainCount = tostring(self.acquisition_guaranteed), name = self.guaranteedItem.name}))
        end

        if self.curSelectGroupIndex == 0 then
            self:_loadprize()
        else
            self:_loadSweephistroy()
        end

    end

    -- 刷新解析指定
    self:RefreshUpReward()

    -- 刷新累抽奖励红点
    self._wtContinuousRewardPanelRedDot:SetReddotVisible(StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId))
    if StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId) then
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C006"))
    else
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C001"))
    end

    -- 更新红点
    self._wtRewardChoosePanelRedDot:SetReddotVisible(Server.StoreServer:IsMandelUpRewardRedDot(self.drawData.LotteryId))
    
    -- 刷新累抽奖励面板
    self:RefreshContinuousPanel()
end

function MandelDraw:_CreatePrizeListByGroupId(idx)
    -- if self._prizeGroups ~= nil and #self._prizeGroups >= idx then
    --     local weakGroupUIIns, instanceId = Facade.UIManager:AddSubUI(self, UIName2ID.StorePrizesList,
    --         self._wtPrizesScrollBox)
    --     local groupWidget = getfromweak(weakGroupUIIns)
    --     if groupWidget ~= nil then
    --         groupWidget:SetInfo(self._prizeGroups[idx].items, self._prizeGroups[idx].title_main, nil, 1, idx == 2, 2)
    --     end
    -- end
end

function MandelDraw:_OnMandelPropItemClick(item)
    loginfo("[MandelDraw] _OnMandelPropItemClick item.name:" .. item.name)

    if item then
        if item.itemMainType == EItemType.WeaponSkin then
            -- Facade.UIManager:AsyncShowUI(UIName2ID.CollectionWeaponSkinDetailPage, nil, nil, item)
            Module.Collection:ShowWeaponSkinDetailPage(item)
        else
            Module.ItemDetail:OpenItemDetailPanel(item, detailBtn, nil, nil, nil, nil, nil, nil, nil, nil)
        end
    end
end

function MandelDraw:_OnStorePaddingMandelItems(paddedGoodsToDrawCount, showPrice, price)
    self.paddedGoodsAndDrawNum = paddedGoodsToDrawCount
    self.paddedShowPrice = showPrice
    self.paddedprice = price
end

function MandelDraw:_OnStorePaddingMandelItemsRecord(nowMandelInfo, nowKeyInfo)
    self.nowMandelInfo = nowMandelInfo
    self.nowKeyInfo = nowKeyInfo
end

function MandelDraw:_OnButtonVideoClick()
    self.showRewardsDetail = false
    -- 播放视频
    if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" then
        local needProgressBar = false
        local needSkipBtn = true

        -- 屏蔽光标
        if IsHD() then
            WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true, false)
        end

        -- 如果是全屏播放的视频，调用CommonVideoFullScreenView
        -- Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoFullScreenView, nil, nil, self.CurSelectMovies, needProgressBar, needSkipBtn)
        Module.CommonWidget:ShowFullScreenVideoView(self.CurSelectMovies, needProgressBar, needSkipBtn, self.OnVedioPlayEnd, self, 1, 0)

        LogAnalysisTool.DoSendStoreVideoEventReportLog(2, self.CurSelectMovies, 2)
    end
end

function MandelDraw:PlayVideo(playStyle)
    if self._mediaComponent == nil then
        return
    end

    self.isPlayVideo = 1

    loginfo("[MandelDraw] _OnButtonVideoClick")
    if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" then
        Module.Store.Field:SetVideoAutoPlay(self.CurSelectMovies)
        Server.StoreServer:AddPlayedPV2List(self.drawData.LotteryId)

        local needProgressBar = false
        local needSkipBtn = true

        -- 屏蔽光标
        if IsHD() then
            WidgetUtil.SetFreeAnalogCursorIsBlocked(self, true, false)
        end

        -- 如果是全屏播放的视频，调用CommonVideoFullScreenView
        -- Facade.UIManager:AsyncShowUI(UIName2ID.CommonVideoFullScreenView, nil, nil, self.CurSelectMovies, needProgressBar, needSkipBtn)
        Module.CommonWidget:ShowFullScreenVideoView(self.CurSelectMovies, needProgressBar, needSkipBtn, self.OnVedioPlayEnd, self, 1, 0)

        LogAnalysisTool.DoSendStoreVideoEventReportLog(2, self.CurSelectMovies, playStyle)
    end
end

function MandelDraw:OnVedioPlayEnd()
    -- 恢复虚拟光标
    if IsHD() then
        WidgetUtil.SetFreeAnalogCursorIsBlocked(self, false)
    end
end

function MandelDraw:OnMediaPlayEnd()
    if self._mediaComponent == nil then
        return
    end

    local allLayers = {}
    for k, v in pairs(EUILayer) do
        table.insert(allLayers, v)
    end
    Facade.UIManager:LayersOnly(allLayers, ELayerRuleChangeReason.BusinessPending)

    if self.isPlayVideo == 0 then
        return
    end

    loginfo("[MandelDraw] OnMediaPlayEnd")
    self:PlayAnimation(self.WBP_Store_TheChestOpensMain_PlayVideo, 0, 1, EUMGSequencePlayMode.Reverse, 1, false)
    self._wtVideoBtn:Visible()
    self._mediaComponent:Collapsed()
    self._wtVideoJump:Collapsed()
    self._wtDFBorder_0:Visible()
    self._wtDFHorizontalBox_0:Visible()
    self._wtBoxNameText:Visible()
    self._wtNameIcon:Visible()

    self.isPlayVideo = 0
end

function MandelDraw:_OnButtonVideoJumpClick()
    if self._mediaComponent == nil then
        return
    end

    local allLayers = {}
    for k, v in pairs(EUILayer) do
        table.insert(allLayers, v)
    end
    Facade.UIManager:LayersOnly(allLayers, ELayerRuleChangeReason.BusinessPending)

    if self.isPlayVideo == 0 then
        return
    end

    self.isPlayVideo = 0

    loginfo("[MandelDraw] _OnButtonVideoJumpClick")
    self:PlayAnimation(self.WBP_Store_TheChestOpensMain_PlayVideo, 0, 1, EUMGSequencePlayMode.Reverse, 1, false)

    self._wtVideoBtn:Visible()
    self._mediaComponent:Stop()
    self._mediaComponent:Collapsed()
    self._wtVideoJump:Collapsed()
    self._wtDFBorder_0:Visible()
    self._wtDFHorizontalBox_0:Visible()
    self._wtBoxNameText:Visible()
    self._wtNameIcon:Visible()

    if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" then
        LogAnalysisTool.DoSendStoreVideoEventReportLog(2, self.CurSelectMovies, 3)
    end
end

function MandelDraw:OnMediaPlayBegin()

end

function MandelDraw:OnCloseBtnClick()

end


-- UI关闭时触发, 顺序后于OnHide（仅触发一次）
---@overload fun(LuaUIBaseView, OnClose)
function MandelDraw:OnClose()
    logerror("MandelDraw:OnClose")

    -- self:RemoveLuaEvent(Module.CommonBar.Config.evtOnChangeButtonClicked)

    self.emptyContent = nil
    self.creatPrizeList = false

    Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self.drawData.LotteryId))

    self:RemoveAllLuaEvent()
    -- Facade.UIManager:ClearSubUIByParent(self, self._wtPrizesScrollBox)
end

function MandelDraw:OnShowBegin()

    if self.drawData ~= nil then
        LogAnalysisTool.DoSendStoreViewPageReportLog(3, self.drawData.MandelItemId, 0, 0)

        self.CurSelectMovies = self.drawData.MoviesRowName
        self.ISAutoPlayVideo = self.drawData.MoviesAuto
        if self.CurSelectMovies == nil or self.CurSelectMovies == "" or self.enablePlayFullScreenCG == false then
            self._wtVideoBtn:Collapsed()
        else
            self._wtVideoBtn:Visible()
        end
    end

    local currencyId = StoreLogic.GetMandelTopBarIDByItemID(self.drawData.MandelItemId)

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_EnableGamepadFeature()
    end
    --- END MODIFICATION

    self:_EnableOpenBoxBtn(true)
    
    self:_PlayVideo()
end

function MandelDraw:OnHideBegin()
    self._mediaBackground:Stop()

    --- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
    if IsHD() then
        self:_DisableGamepadFeature()
    end
    --- END MODIFICATION

    self:_ClearTimer()
end

-- UI OnOpen后、调用Show()显示时触发
-- 做表现
---@overload fun(LuaUIBaseView, OnShow)
function MandelDraw:OnShow()
    logerror("MandelDraw:OnShow")
    self:RegisterEventListeners()

    self:AddLuaEvent(Server.RewardServer.Events.evtOnDrawShowFinished, self._OnOpenBlindBoxFinished, self)
    -- self:AddLuaEvent(Module.CommonBar.Config.evtOnChangeButtonClicked, self._OnChangeButtonClicked, self)

    --【【CN】【PC】【必现】商城曼德尔转开启后的奖励展示界面，按键盘空格无法关闭页面，鼠标点击可以页面关闭按钮可以关闭】https://tapd.woa.com/Project_D/bugtrace/bugs/view?bug_id=1020421949123169535
    self:AddLuaEvent(Module.Reward.Config.Events.evtShowReward, self._OnShowReward, self)

    self.paddedGoodsAndDrawNum = 0

    self:RefreshCurDrawData()

    -- if IsHD() and self._bIsShowingRewardPop ~= true then
    --     self._inputMonitor = Facade.UIManager:GetInputMonitor()
    --     self._handle = self._inputMonitor:AddDisplayActionBinding("JumpOver", EInputEvent.IE_Pressed,
    --         self._OnButtonVideoJumpClick, self, EDisplayInputActionPriority.UI_Pop)
    -- end
    if self.CurSelectMovies ~= nil and self.CurSelectMovies ~= "" then
        local ItemList = Module.Store.Field:GetMandelDrawItemList()
        local OwnedItems = {}
        for key, ItemId in pairs(ItemList) do
            local isOwnedItem = Server.CollectionServer:IsOwnedWeaponSkin(ItemId)
            if isOwnedItem then
                table.insert(OwnedItems, ItemId)
            end
        end
        
        local isOwned = StoreLogic.GetIsItemOwned(self.CurSelectMovies, OwnedItems)
        local hasPlayed = Server.StoreServer:GetIsStorePvPlayed(self.drawData.LotteryId)
        if isOwned then
            hasPlayed = true
        end
        local needAutoPlay = Module.Store.Field:CheckVideoNeedAutoPlay(self.CurSelectMovies)
        if (not hasPlayed or hasPlayed == nil) and self.ISAutoPlayVideo ~= nil and self.ISAutoPlayVideo > 0 then
            self:PlayVideo(1)
        end
    end

    Module.Store.Config.evtMandelDrawShow:Invoke()

    if Module.Store.Field:GetCanMandelStopAudio() then
        if self.drawData then
            -- 播放进入音乐
            local music = Module.Store:GetLotteryMusic(self.drawData.LotteryId)
            Module.Store:SetCurMandelMusicName(music)
            Facade.SoundManager:PlayUIAudioEvent(music)
            loginfo("MandelDraw Facade.SoundManager:PlayUIAudioEvent musicName = "..music)
        end
    else
        if not self.bOpenOtherOverviewButton then
            Module.Store.Field:SetCanMandelStopAudio(true)
        end
    end
    -- Facade.SoundManager:PlayMusicByName(DFMAudioRes.UI_Store_Music_Lottery_Spectrun_Blitz_Enter)

    self:ReportRedPoint()

    -- 视频
    self.CurSelectMovies = self.drawData.MoviesRowName
    self.ISAutoPlayVideo = self.drawData.MoviesAuto
    if self.CurSelectMovies == nil or self.CurSelectMovies == "" or self.enablePlayFullScreenCG == false then
        self._wtVideoBtn:Collapsed()
    else
        self._wtVideoBtn:Visible()
    end

    self:_SetBottombar()
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function MandelDraw:OnHide()
    logerror("MandelDraw:OnShow OnHide")
    -- self:_OnButtonVideoJumpClick()
    self:UnRegisterEventListeners()

    self:RemoveLuaEvent(Server.RewardServer.Events.evtOnDrawShowFinished)
    self:RemoveLuaEvent(Module.Reward.Config.Events.evtShowReward)

    if IsHD() and self._inputMonitor ~= nil and self._handle ~= nil then
        self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._handle)
    end

    -- loginfo(debug.traceback())

    Module.Store.Config.evtMandelDrawHide:Invoke()

    if self.showRewardsDetail then
        self.showRewardsDetail = false
    end

    if self.drawData and Module.Store.Field:GetCanMandelStopAudio() then
        Facade.SoundManager:StopUIAudioEvent(Module.Store:GetLotteryMusic(self.drawData.LotteryId))
        loginfo("MandelDraw Facade.SoundManager:StopUIAudioEvent musicName = "..Module.Store:GetLotteryMusic(self.drawData.LotteryId))
    end
end

function MandelDraw:Update(dt)

end

function MandelDraw:RegisterEventListeners()
    self:AddLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData, self._OnUpdateCollectionData, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreOpenBoxSuccess, self._OnMandelOpenBoxSucess, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyChange, self._OnServerLotteryItemBuySuc, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyFailed, self._OnServerLotteryItemBuyFailed,
        self)

    self:AddLuaEvent(Module.Store.Config.evtStoreMandelPropItemClick, self._OnMandelPropItemClick, self)
    self:AddLuaEvent(Module.Store.Config.evtStorePaddingMandelItems, self._OnStorePaddingMandelItems, self)
    self:AddLuaEvent(Module.Store.Config.evtStorePaddingMandelItemsRecord, self._OnStorePaddingMandelItemsRecord, self)
    self:AddLuaEvent(Server.StoreServer.Events.evtStoreBoxItem, self._OnBoxInfoUpdate, self)

    Facade.ProtoManager.Events.evtOnRelayConnected:AddListener(self._OnRelayConnected, self)  --断线重连

    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnRewardPanelClosed, self) --奖励弹窗关闭
end

function MandelDraw:UnRegisterEventListeners()
    self:RemoveLuaEvent(Server.CollectionServer.Events.evtUpdateCollectionData)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreOpenBoxSuccess)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyChange)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreMandelLotteryItemBuyFailed)

    self:RemoveLuaEvent(Module.Store.Config.evtStoreMandelPropItemClick)
    self:RemoveLuaEvent(Module.Store.Config.evtStorePaddingMandelItems)
    self:RemoveLuaEvent(Module.Store.Config.evtStorePaddingMandelItemsRecord)
    self:RemoveLuaEvent(Server.StoreServer.Events.evtStoreBoxItem)

    Facade.ProtoManager.Events.evtOnRelayConnected:RemoveListener(self._OnRelayConnected, self)

    self:RemoveLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnRewardPanelClosed)
end

function MandelDraw:_OnUpdateCollectionData()
    self:RefreshCurDrawData()
end

function MandelDraw:_OnOpenBlindBoxFinished()
    local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
    Module.Store:LoadBlindboxPrizesWithOtherInfo(self.drawData.MandelItemId, loadPrizeCallback)
end

function MandelDraw:_OnChangeButtonClicked()
    self.showRewardsDetail = false
end

------event-----
function MandelDraw:_OnBoxInfoUpdate(data)
    if data == nil then
        logerror("[MandelDraw]:_OnBoxInfoUpdate : GetGiftInfoByItemId is nil, item id :")
        return
    end

    self:RefreshContinuousPanel()

    -- 刷新累抽奖励红点
    self._wtContinuousRewardPanelRedDot:SetReddotVisible(StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId))
    if StoreLogic.IsMandelContinuousRewardBtnRedDot(self.boxId) then
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C006"))
    else
        self._wtImageContinuousReward:SetColorAndOpacity(Facade.ColorManager:GetSlateColorByRowName("C001"))
    end
end

function MandelDraw:_OnRewardPanelClosed()
    -- 国内外IOS和海外GooglePlay评分
    if PLATFORM_IOS or (PLATFORM_ANDROID and (not IsBuildRegionCN())) then
        Module.GCloudSDK:CheckAndRequestAppStoreWindow(EAppStoreWindowSource.Shop)
    end
end

function MandelDraw:_OnEvtStoreMainPanelRefreshCurrencyStateDone()
    Module.Store.Config.evtStoreMainPanelRefreshCurrencyState:Invoke(StoreLogic.GetMandelTopBarIDByItemID(self.drawData.MandelItemId))
end

function MandelDraw:_OnEvtStoreMandelUpRewardChooseResult(result)
    if result == 0 then
        local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
        Module.Store:LoadBlindboxPrizesWithOtherInfo(self.drawData.MandelItemId, loadPrizeCallback)
    end 
end

function MandelDraw:_OnRelayConnected()
    Server.StoreServer:SendShopGetBuyRecordReq()
    local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
    Module.Store:LoadBlindboxPrizesWithOtherInfo(self.drawData.MandelItemId, loadPrizeCallback)

    self:RefreshCurDrawData()
end

function MandelDraw:_OnServerLotteryItemBuyFailed(result)
    LogAnalysisTool.DoSendStorePaddedGoodsEventReportLog(self.drawData.LotteryId, self.drawData.MandelItemId,
        self.drawData.LotteryKeyId, self.nowMandelInfo, self.nowKeyInfo, 2, result)
end

function MandelDraw:_OnServerLotteryItemBuySuc(dataChange)
    if self.paddedGoodsAndDrawNum ~= nil and self.paddedGoodsAndDrawNum > 0 then
        self.allMandels = Server.CollectionServer:GetCollectionPropById(self.drawData.MandelItemId)
        if self.allMandels ~= nil then
            self.mandelCount = self.allMandels.num
        else
            self.mandelCount = 0
        end

        local openCount = 1
        if self.paddedGoodsAndDrawNum == 1 then
            openCount = 1
        else
            openCount = 10
        end

        if self.mandelCount < openCount then
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawMandelLotteryItemUpdateWithNotAll)
        else
            if self.paddedprice < self.paddedShowPrice then
                Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawMandelLotteryItemUpdateWithPriceTip)
            else
                Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawMandelLotteryItemUpdate)
            end

            self:_openMandelBox(openCount)
        end

        self.paddedGoodsAndDrawNum = 0
        self.paddedShowPrice = 0
        self.paddedprice = 0
        return
    end

    local itemList = {}
    if dataChange then
        if dataChange.prop_changes then
            local prop_changes = dataChange.prop_changes
            for _, propChange in ipairs(prop_changes) do
                if propChange.prop then
                    local item = ItemBase:New(propChange.prop.id, propChange.prop.num, propChange.prop.gid)
                    item.bGiveaway = propChange.prop.bGiveaway
                    local weaponFeature = item:GetFeature(EFeatureType.Weapon)
                    if weaponFeature and weaponFeature:IsWeaponSkin() then
                        -- 蓝图需要手动设置一下枪械信息
                        local weaponDescription = WeaponHelperTool.GetWeaponDescriptionFromSkinID(item.id)
                        local propinfo = WeaponAssemblyTool.Desc_To_PropInfo(weaponDescription)
                        item:SetRawPropInfo(propinfo)
                    else
                        item:SetRawPropInfo(propChange.prop)
                    end
                    table.insert(itemList, item)
                end
            end
        end

        -- if dataChange.currency_changes then
        --     local currency_changes = dataChange.currency_changes
        --     for _, currencyChange in ipairs(currency_changes) do
        --         if currencyChange.delta ~= 0 then
        --             local item = ItemBase:New(currencyChange.currency_id, currencyChange.delta)
        --             table.insert(itemList, item)
        --         end
        --     end
        -- end
    end
    Module.Reward:OpenRewardPanel(Module.Store.Config.Loc.GetItemWindow, nil, itemList)

    LogAnalysisTool.DoSendStorePaddedGoodsEventReportLog(self.drawData.LotteryId, self.drawData.MandelItemId,
        self.drawData.LotteryKeyId, self.nowMandelInfo, self.nowKeyInfo, 1, 0)
end

function MandelDraw:_OnMandelOpenBoxSucess(dataChange)

end

function MandelDraw:_OnDrawOneClick()
    loginfo("[MandelDraw] _OnDrawOneClick")
    self.showRewardsDetail = false

    if self.drawData == nil then
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelData)
        return
    end

    local nowClcikTime = Facade.ClockManager:GetLocalTimestamp()
    local timeSpan = nowClcikTime - self.lastClickDrawTime
    if timeSpan < 1.5 then
        loginfo("[MandelDraw] _OnDrawOneClick timeSpan < 1")
        return
    end
    self.lastClickDrawTime = nowClcikTime


    if self.allMandels ~= nil then
        self.mandelCount = self.allMandels.num
    end

    if self.allMandelKeys ~= nil then
        self.keyCount = self.allMandelKeys.num
    end

    local needMandel = 1 - self.mandelCount
    local needKey = 1 - self.keyCount

    -- if (IsBuildRegionGlobal() or IsBuildRegionGA()) or self.bNeedUseKey == false then
    --     needKey = 0
    -- end

    if needMandel > 0 or needKey > 0 then
        self:_CheckMandelAndKeyCountNotEnough(needMandel, needKey, 1)
    else
        --draw
        self:_openMandelBox(1)
    end
end

function MandelDraw:_openMandelBox(openCount)
    if not openCount then
        return
    end
    local mandelItem = Server.CollectionServer:GetCollectionItemById(self.drawData.MandelItemId)
    if mandelItem == nil then
        logerror("[MandelDraw] _openMandelBox but mandelItem == nil")
        return
    end

    if openCount > mandelItem.num then
        loginfo("[MandelDraw] _openMandelBox but openCount > mandelItem.num")
        return
    end

    -- Server.StoreServer:OpenMandelBox({mandelItem}, 1, false)
    local fOnResCallback = function(res)
        if isvalid(self)  then
            self._jumpOneceCommitTranstion = true
        end
    end
    Server.RewardServer:OpenBlindBox(mandelItem, openCount , fOnResCallback)
    self:_EnableOpenBoxBtn(false)
    self._btnEnableTimer = Timer.DelayCall(3, function ()
        self:_EnableOpenBoxBtn(true)
    end,self)
end

function MandelDraw:_EnableOpenBoxBtn(bEnable)
    if bEnable then
        self._wtSingleActionBtn:SetIsEnabled(true)
        self._wtMultipleActionBtn:SetIsEnabled(true)
    else
        self._wtSingleActionBtn:SetIsEnabled(false)
        self._wtMultipleActionBtn:SetIsEnabled(false)
    end
end

function MandelDraw:_CheckMandelAndKeyCountNotEnough(needMandelCount, needKeyCount, drawCount)
    if needMandelCount <= 0 and needKeyCount <= 0 then
        return
    end

    self.paddedGoodsAndDrawNum = 0

    if needMandelCount > 0 then
        local function OnGetMandelInfo(SaleListInfo)
            loginfo("[MandelDraw] OnGetMandelInfo")

            local saleInfo = SaleListInfo
            if saleInfo ~= nil and saleInfo.sale_lists ~= nil and #saleInfo.sale_lists > 0 then
                local mandelFee = 0
                local keyFee = 0

                local nowNeedCount = needMandelCount
                for k, v in pairs(saleInfo.sale_lists) do
                    if nowNeedCount <= 0 then
                        break
                    end
                    local couldBuyNum = math.min(nowNeedCount, v.selling_num)
                    mandelFee = mandelFee + couldBuyNum * v.price
                    nowNeedCount = nowNeedCount - couldBuyNum
                end

                if needKeyCount < 0 then
                    needKeyCount = 0
                end
                if self.buyManderKeysConfig ~= nil then
                    keyFee = needKeyCount * self.buyManderKeysConfig.price
                end

                Facade.UIManager:AsyncShowUI(UIName2ID.StoreMandelPaddedGoods, nil, nil, self.drawData.MandelItemId,
                    needMandelCount - nowNeedCount, mandelFee, self.drawData.LotteryKeyId, needKeyCount, keyFee,
                    self.drawData.LotteryId, drawCount)

            else
                Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelInMarketNow)
                LogAnalysisTool.DoSendStorePaddedGoodsEventReportLog(self.drawData.LotteryId, self.drawData.MandelItemId
                    , self.drawData.LotteryKeyId, "", "", 2, 3)
            end
        end

        if Server.MarketServer:CheckIsInSaleList(self.drawData.MandelItemId) then
            -- 拉取拍卖行最新数据再进行购买
            Server.MarketServer:FetchSaleList(self.drawData.MandelItemId, false, OnGetMandelInfo)
        else
            Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelInMarketNow)
        end
    else
        local keyFee = 0
        if self.buyManderKeysConfig ~= nil then
            keyFee = needKeyCount * self.buyManderKeysConfig.price
        end
        
        local fOnBuySuccessCallback = function ()
            self:_openMandelBox(drawCount)
        end

        Module.Store.OpenStoreMandelBuyKeysPanel(nil, self.buyManderKeysConfig, self.drawData.LotteryId, 0,
            needKeyCount, 1, fOnBuySuccessCallback)
    end
end

function MandelDraw:_OnDrawTenClick()
    loginfo("[MandelDraw] _OnDrawTenClick")
    self.showRewardsDetail = false

    if self.drawData == nil then
        Module.CommonTips:ShowSimpleTip(StoreConfig.Loc.MandelDrawNoMandelData)
        return
    end

    local nowClcikTime = Facade.ClockManager:GetLocalTimestamp()
    local timeSpan = nowClcikTime - self.lastClickDrawTime
    if timeSpan < 1.5 then
        loginfo("[MandelDraw] _OnDrawTenClick timeSpan < 1")
        return
    end

    self.lastClickDrawTime = nowClcikTime

    if self.allMandels ~= nil then
        self.mandelCount = self.allMandels.num
    end

    if self.allMandelKeys ~= nil then
        self.keyCount = self.allMandelKeys.num
    end

    local needMandel = 10 - self.mandelCount
    local needKey = 10 - self.keyCount

    -- if (IsBuildRegionGlobal() or IsBuildRegionGA()) or self.bNeedUseKey == false then
    --     needKey = 0
    -- end

    if needMandel > 0 then -- 缺曼德尔砖时，不管缺不缺量子密钥
        self:_CheckMandelAndKeyCountNotEnough(needMandel, needKey, 10)
    elseif needMandel <= 0 and needKey > 0 then -- 只缺量子密钥
        self:_OnBuyKeysClick(needKey,10)
    else
        --draw
        self:_openMandelBox(10)
    end
end


function MandelDraw:_OnShowRewardsClick()
    if self.showRewardsDetail then
        self.showRewardsDetail = false
    else
        self.showRewardsDetail = true
    end

end

function MandelDraw:_OnClosePrizesPanelBtnClick()
    if self.showRewardsDetail then
        self.showRewardsDetail = false
    end
end

function MandelDraw:_OnUpRewardChooseBtnClick()
    logerror("MandelDraw:_OnUpRewardChooseBtnClick")
    Module.Store.Field:SetCanMandelStopAudio(false)
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreParseSpecify, nil, nil, self.drawData.LotteryId, self.boxId, EMandelOpenSource.Store)

    -- 上报点击
    local groupID = nil
    local tCoreGroup = Server.StoreServer:GetMandelLotteryCoreGroup(self.boxId)
    if tCoreGroup then
        groupID = tCoreGroup.group_id
    end
    if groupID then
        LogAnalysisTool.ShopEnterMandelBoxUpUIFlow(self.boxId, groupID, EMandelOpenSource.Store)
    end
end

function MandelDraw:_OnContinuousRewardBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.StoreMandelContinuousReward, nil, nil, self.boxId)
end

function MandelDraw:_OnBuyKeysClick(needKey,drawCount)
    LogAnalysisTool.SignButtonClicked(10153005)
    loginfo("[MandelDraw] _OnBuyKeysClick")
    self.showRewardsDetail = false
    local needKeyCount = needKey or 1

    if self.drawData ~= nil then
        local mandelCount = 0
        if self.allMandels ~= nil then
            mandelCount = self.allMandels.num
        end

        local keyCount = 0
        if self.allMandelKeys ~= nil then
            keyCount = self.allMandelKeys.num
        end

        local fOnBuySuccessCallback = function ()
            self:_openMandelBox(drawCount)
        end
        Module.Store.OpenStoreMandelBuyKeysPanel(nil, self.buyManderKeysConfig,
            self.drawData.LotteryId, mandelCount, needKeyCount, needKeyCount, fOnBuySuccessCallback)
    end
end

function MandelDraw:_OnGroupBoxCount()
    return 2
end

function MandelDraw:_OnGroupBoxWidget(position, itemWidget)

    if position == 0 then
        itemWidget:SetMainTitle(StoreConfig.Loc.MandelTabReward)
    else
        itemWidget:SetMainTitle(StoreConfig.Loc.MandelTabHistory)
    end

    --是否解锁显示
    -- itemWidget:SetIsLocked(false)

end


function MandelDraw:_loadSweephistroy()
    -- self._wtCannotRestoreHintText:Collapsed()
    -- self._wtCannotRestoreHintImg:Collapsed()

    -- self._wtPrizesScrollBox:Collapsed()

    -- local newHistory = Server.StoreServer:GetLotteryRecordsByLotteryId(self.lotteryPoolID)
    -- if newHistory ~= nil and self.history ~= nil then
    --     if #self.history ~= #newHistory then
    --         self.history = newHistory
    --         self.historyToShow = {}
    --         self.propToHistory = {}

    --         table.sort(self.history, function(a, b)
    --             return a.open_time > b.open_time
    --         end)

    --         self.historyAllCount = 0
    --         local counter = 0
    --         for historyid, historyInfo in pairs(self.history) do
    --             -- body
    --             if historyInfo.add_props ~= nil and #historyInfo.add_props > 0 then
    --                 self.historyAllCount = self.historyAllCount + #historyInfo.add_props
    --                 for k,v in pairs(historyInfo.add_props) do
    --                     table.insert(self.historyToShow, v)
    --                     counter = counter + 1
    --                     self.propToHistory[counter] = historyInfo
    --                 end
    --             end
    --         end
    --     end

    --     if #self.history > 0 then
    --         if self._wtPlatformPaddingBox_0 ~= nil then
    --             self._wtPlatformPaddingBox_0:Hidden()
    --         end

    --         self._wtPrizesScrollBoxHistory:Visible()
    --         self._wtPrizesScrollBoxHistory:RefreshAllItems()
    --     else
    --         if self._wtPlatformPaddingBox_0 ~= nil then
    --             self._wtPlatformPaddingBox_0:Visible()
    --         end

    --         if not self.emptyContent then
    --             ---创建emptyContent
    --             self.emptyContent = Facade.UIManager:AddSubUI(self, UIName2ID.CommonEmptyContent, self._wtPlatformPaddingBox_0)
    --             local emptyContent = getfromweak(self.emptyContent)
    --             local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(emptyContent)
    --             local anchors = FAnchors()
    --             anchors.Minimum = FVector2D(0, 0)
    --             anchors.Maximum = FVector2D(1, 1)
    --             if canvasSlot then
    --                 canvasSlot:SetAnchors(anchors)
    --                 canvasSlot:SetOffsets(FMargin(0,0,0,0))
    --             end
    --             emptyContent:BP_SetText(StoreConfig.Loc.MandelHistoryNoAnyThing)
    --             emptyContent:BP_SetTypeWithParam(1)
    --             emptyContent:SetRenderTranslation(FVector2D(0,-75))
    --             emptyContent:Visible()
    --             self.isContentOwner = true
    --         else
    --             local emptyContent = getfromweak(self.emptyContent)
    --             if emptyContent ~= nil then
    --                 emptyContent:Visible()
    --             end
    --         end
    --     end
    -- end

end


function MandelDraw:_OnShowReward(bIsShowing)
    if not bIsShowing then
        self:RefreshCurDrawData()
        local loadPrizeCallback = CreateCallBack(self.SetNewGiftDetail, self)
        Module.Store:LoadBlindboxPrizesWithOtherInfo(self.drawData.MandelItemId, loadPrizeCallback)
    else
        self._bIsShowingRewardPop = true
        if IsHD() and self._inputMonitor ~= nil and self._handle ~= nil then
            self._inputMonitor:RemoveDisplayActoinBingingForHandle(self._handle)
            self._handle = nil
        end
    end
end

function MandelDraw:_OnGetBreakChildRecursiveLifeToggle(uiState)
    return true
end

function MandelDraw:_OnOpenPrizeOverviewButtonClicked()
    logerror("MandelDraw:_OnOpenPrizeOverviewButtonClicked")
    if not self.drawData then
        return
    end
    Module.Store.Field:SetCanMandelStopAudio(false)
    Module.Store.OpenStorePrizeOverviewPanel(nil, nil, self.drawData.MandelItemId)
    LogAnalysisTool.SignButtonClicked(10153001)
end

function MandelDraw:_OnOpenOtherOverviewButtonClicked()
    if  not self.drawData then
        return
    end
    logerror("MandelDraw:_OnOpenOtherOverviewButtonClicked")
    Module.Store.Field:SetCanMandelStopAudio(false)
    self.bOpenOtherOverviewButton = true
    local fOnCloseCallback = function ()
        self._wtCheckBox:SetIsChecked(false, false)
        Module.Store.Field:SetCanMandelStopAudio(true)
        self.bOpenOtherOverviewButton = false
    end
    Module.Store.OpenStorePrizePopPanel(nil, fOnCloseCallback, self.drawData.MandelItemId)
end

function MandelDraw:OnUpRewardIconLoaded(DFMImage, Tile)
    local weaponMaterial = self._wtUpRewardWeaponIcon:GetDynamicMaterial()
    if weaponMaterial then
        weaponMaterial:SetTextureParameterValue("Texture_Main", Tile.Texture)

        loginfo("MandelDraw:OnUpRewardIconLoaded====RawSize:", Tile.RawSize.X, Tile.RawSize.Y)
        local size = self._wtUpRewardWeaponIcon.slot:GetSize()
        loginfo("MandelDraw:OnUpRewardIconLoaded====self._wtUpRewardWeaponIcon RawSize:", size.X, size.Y)
        
        local fRatio = Tile.RawSize.Y / Tile.RawSize.X

        self._wtUpRewardWeaponIcon.slot:SetSize(FVector2D(size.X, size.X * fRatio)) 
    end
    self._wtUpRewardWeaponIcon:SelfHitTestInvisible()
    self._wtUpRewardChoosePlus:Collapsed()
    self._wtUpRewardIcon:Collapsed()
end

function MandelDraw:_RefreshCurrencyNum(itemId, buyNum)
    --刷新密钥的数量--32320000001
    local num = self.keyCount + buyNum
    Server.CurrencyServer:RefreshCurrencyNum(itemId, 1, num)
end

function MandelDraw:ReportRedPoint()
    if self.drawData ~= nil then
        Server.StoreServer:CheckAndSetThirdTabReddotByGroupID(1, self.drawData.LotteryId)
    end
end

function MandelDraw:RefreshUpReward()
    
    self.boxId = 0
    local itemInfo = ItemConfigTool.GetItemConfigById(self.drawData.MandelItemId)
    if itemInfo then
        self.boxId = itemInfo.ConnectedPool or 0
    end

    local coreGroup = Server.StoreServer:GetMandelLotteryCoreGroup(self.boxId)
    -- 判断是否只有一个核心奖励
    self.iCoreCount = 0
    if coreGroup == nil then
        self.iCoreCount = 0
    else
        for index, item in pairs(coreGroup.prop_list) do
            self.iCoreCount = self.iCoreCount + 1
        end
    end
    if self.iCoreCount <= 1 then
        self._wtRewardChoosePanel:Collapsed() 
        return
    else
        self._wtRewardChoosePanel:SelfHitTestInvisible()
    end
    
    local UpRewardID = Server.StoreServer:GetMandelIDUpReward(self.boxId)
    if UpRewardID == nil then
        -- 未选择高概率奖励
        self._wtUpRewardChoosePlus:SelfHitTestInvisible()
        self._wtUpRewardWeaponIcon:Collapsed()
        self._wtUpRewardItemName:SelfHitTestInvisible()
        self._wtUpRewardItemName:SetText(StoreConfig.Loc.MandelUpRewardSelectTitle)
    else
        -- 已选择高概率奖励
        self._wtUpRewardChoosePlus:Collapsed()
        self._wtUpRewardWeaponIcon:Collapsed()
        self._wtUpRewardItemName:SelfHitTestInvisible()

        -- 设置奖励图标
        local wtItem = ItemBase:New(UpRewardID, 1)
        local param = FRTIParamData() 
        param.bShouldMerge = false
        self._wtUpRewardIcon:SelfHitTestInvisible()
        RuntimeIconTool.SetItemIcon(wtItem, self._wtUpRewardIcon, param)
        self._wtUpRewardItemName:SetText(wtItem.name)
    end

    self:_SetBottombar()
end

function MandelDraw:RefreshContinuousPanel()
    local ContinuousRewardCount = StoreLogic.GetMandelContinuousRewardCount(self.boxId)
    if ContinuousRewardCount == nil or ContinuousRewardCount <= 0 then
        self._wtContinuousRewardPanel:Collapsed()  
    else
        self._wtContinuousRewardPanel:SelfHitTestInvisible()
    end
    self:_SetBottombar()
end

--- BEGIN MODIFICATION @ VIRTUOS : UI Navigation 
function MandelDraw:_EnableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 切换页签的时候也会触发onshowbegin，要确保visible的时候才初始化手柄功能
    if not self:IsVisible() then
        return
    end

    --配置keyIcon
    if self._wtSingleActionBtn and not self._LotteryLongPressHandle then
        self._LotteryLongPressHandle = self:AddInputActionBinding(
            "MallLottery",
            EInputEvent.IE_Pressed,
            self._OnDrawOneClick,
            self,
            EDisplayInputActionPriority.UI_Stack
        )

        self._wtSingleActionBtn:SetDisplayInputActionWithLongPress(self._LotteryLongPressHandle, self, "MallLottery", true, nil, true)
    end
    if self._wtMultipleActionBtn and not self._LotteryTenLongPressHandle then
        self._LotteryTenLongPressHandle = self:AddInputActionBinding(
            "MallLotteryTen",
            EInputEvent.IE_Pressed,
            self._OnDrawTenClick,
            self,
            EDisplayInputActionPriority.UI_Stack
        )
        self._wtMultipleActionBtn:SetDisplayInputActionWithLongPress(self._LotteryTenLongPressHandle, self, "MallLotteryTen", true, nil, true)
    end

    if self.wtWBP_DFCommonButtonV2S2 then
        self.wtWBP_DFCommonButtonV2S2:SetDisplayInputAction("MallCadreDetails", true, nil, true)

        if not self._CadreDetails then
            self._CadreDetails = self:AddInputActionBinding(
            "MallCadreDetails", 
            EInputEvent.IE_Pressed, 
            self._OnOpenPrizeOverviewButtonClicked,
            self, 
            EDisplayInputActionPriority.UI_Stack
            )  
        end
    end

    self:_SetBottombar()

    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoA_Direction, self)
end

function MandelDraw:_DisableGamepadFeature()
    if not IsHD() then
        return 
    end

    -- 移除输入
    if self._LotteryLongPressHandle then
        self:RemoveInputActionBinding(self._LotteryLongPressHandle)
        self._LotteryLongPressHandle = nil
    end

    if self._LotteryTenLongPressHandle then
        self:RemoveInputActionBinding(self._LotteryTenLongPressHandle)
        self._LotteryTenLongPressHandle = nil
    end

    if self._CadreDetails then
        self:RemoveInputActionBinding(self._CadreDetails)
        self._CadreDetails = nil
    end

    --移除按键提示
    Module.CommonBar:RecoverBottomBarInputSummaryList()

    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
end

function MandelDraw:_SetBottombar()
    if not IsHD() then
        return 
    end

    Module.CommonBar:RecoverBottomBarInputSummaryList()

    --显示按键提示
    local summaryList ={}

    if self._wtRewardChoosePanel and self._wtRewardChoosePanel:IsVisible() then
        table.insert(summaryList, {actionName = "ChoseReward", func = self._OnUpRewardChooseBtnClick, caller = self, bUIOnly = false, bHideIcon = false}) 
    end

    if self._wtContinuousRewardPanel and self._wtContinuousRewardPanel:IsVisible() then
        table.insert(summaryList, {actionName = "ContinueReward", func = self._OnContinuousRewardBtnClick, caller = self, bUIOnly = false, bHideIcon = false}) 
    end

    if self._wtVideoBtn and self._wtVideoBtn:IsVisible() then
        table.insert(summaryList, {actionName = "MallDemo", func = self._OnButtonVideoClick, caller = self, bUIOnly = false, bHideIcon = false}) 
    end

    table.insert(summaryList, {actionName = "ProbabilityForStore",func = self._OnOpenOtherOverviewButtonClicked, caller = self ,bUIOnly = false, bHideIcon = false})

    Module.CommonBar:SetBottomBarTempInputSummaryList(summaryList, false, true)
end
--- END MODIFICATION

function MandelDraw:_ClearTimer()
 if self._videoPlaytransitionTimer then
        Timer.CancelDelay(self._videoPlaytransitionTimer)
        self._videoPlaytransitionTimer = nil
        Facade.UIManager:CommitTransition(false)
    end

    if self._btnEnableTimer then
        Timer.CancelDelay(self._btnEnableTimer)
        self._btnEnableTimer = nil
    end
end

function MandelDraw:_PlayVideo()
    if self._jumpOneceCommitTranstion then
        if self.drawData and self.drawData.MandelItemId then
            Module.Store:SetMandelDrawMediaBG(self.drawData.MandelItemId, self._mediaBackground)
        end
        self._jumpOneceCommitTranstion = false
       return
    end
    Facade.UIManager:CommitTransition(true) --屏蔽视频和背景同时绘制的画面杂乱
    --播放背景视频
    if self.drawData and self.drawData.MandelItemId then
        Module.Store:SetMandelDrawMediaBG(self.drawData.MandelItemId, self._mediaBackground)
    end
    self._videoPlaytransitionTimer =  Timer.DelayCall(0.2, function()
        Facade.UIManager:CommitTransition(false)
        self._videoPlaytransitionTimer = nil
    end)
end

return MandelDraw
