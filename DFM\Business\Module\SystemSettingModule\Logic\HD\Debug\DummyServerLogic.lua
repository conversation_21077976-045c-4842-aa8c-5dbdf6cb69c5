----- <PERSON><PERSON><PERSON> FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

local DummyServerLogic = {}

local defaultCopmmonSettingJsonStr = "{\"MDVList\":{\"1\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"2\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"3\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"4\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"0\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153]},\"SimpleSettingData\":{\"GamepadSensitivityPresetMode\":1,\"ExtraSensitivityDataHDVerticalValue\":0.0,\"Display_PerformanceStatus\":true,\"bInventoryAutoNewline\":false,\"SideAimingControlMode\":0,\"PassengerWeaponVerticalSensitivityFPP_Gamepad\":100.0,\"bEnableGamepadRumble\":true,\"SensitivityMode_HelicopterDriver_Gamepad\":3,\"ADSExtraSensitivityDataHDDelayTime\":25.0,\"SaveVersion\":4,\"AimVerticalGamepadSensitivity\":75.0,\"MpMapScale\":100.0,\"MDVSwitchingMode\":0,\"bAutoParachute\":false,\"AirPlaneVision\":240.0,\"DriverWeaponVerticalSensitivityFPP_Gamepad\":100.0,\"bEnablePickupDetailHUD\":true,\"CannonCamFollow\":false,\"Rescue_ActionLogic_Gamepad\":1,\"MDV_Driver_Gamepad\":1.3300000429153,\"ADSExtraSensitivityDataHDVerticalValue\":20.0,\"BaseADSSensitivity_Driver\":1.0,\"AimHorizontalSensitivity\":1.0,\"AbandonRescue_ActionLogic_Gamepad\":1,\"bHurtCloseLootPanel\":false,\"GamepadRightThumbMaximumThreshold\":100.0,\"HorizontalSensitivity\":1.0,\"SideAimType_Gamepad\":0,\"AutoSprintType_Gamepad\":1,\"bGunnerVerticalMMReversed\":false,\"bInfantryVerticalMMReversedGamepad\":false,\"bHelicopterVerticalMMReversedGamepad\":false,\"GamepadRightThumbDeadZone\":10.0,\"HelicopterDriverWeaponHorizontalSensitivityFPP_Gamepad\":80.0,\"bEnableScopeCustom\":false,\"bMixedLoading\":true,\"InfantryVision\":80.0,\"bEnableDriverADSCustomGamepad\":false,\"bSwitchWeaponsIncludingBattlefieldProps_GamePad\":false,\"VerticalGamepadSensitivity\":120.0,\"bEnableLBRBtoRBRT\":false,\"PeekOpenMode_Gamepad\":0,\"MDV\":1.3300000429153,\"bSafeBoxPreferMarkItem\":true,\"HelicopterDriverWeaponSensitivityTPP\":5.0,\"bMPSingleClickCreateLocMark\":true,\"DriverWeaponSensitivityTPP\":5.0,\"VehicleVision\":150.0,\"bAllVerticalMMReversedGamepad\":false,\"SafeBoxPriceThreshold\":10000,\"bCanMouseTurnMelee\":true,\"bEnableBreath\":true,\"HitEffectColor\":0,\"bSceneFOVCalcOpenCameraFOV\":false,\"ToggleAiming_ActionLogic_Gamepad\":1,\"bCanMouseTurnMelee_GamePad\":false,\"bAirVehicleAutoBalanceAssist\":false,\"SensitivityMode\":0,\"SlideDiveTrigger_Gamepad\":0,\"SuperSprintsSwitch_Gamepad\":0,\"bAllVerticalMMReversed\":false,\"bEnablePassengerADSCustomGamepad\":false,\"DriverWeaponVerticalSensitivityTPP_Gamepad\":240.0,\"GamepadSensitivityCurveType\":0,\"MDV_Passenger_Gamepad\":1.3300000429153,\"PassengerWeaponSensitivityFPP\":5.0,\"bMapAutoRotateMp\":false,\"RouletteColorA\":100.0,\"ExtraSensitivityDataHDHorizontalValue\":220.0,\"bScopeOpenAutoPeek_Gamepad\":false,\"MDV_HelicopterDriver_Gamepad\":1.3300000429153,\"ExtraSensitivityDataHDDelayTime\":0.0,\"bInventorySortEveryEnter\":false,\"bEnableHelicopterDriverADSCustomGamepad\":false,\"BaseADSSensitivity_HelicopterDriver_Gamepad\":1.0,\"HelicopterDriverWeaponVerticalSensitivityTPP_Gamepad\":80.0,\"AimVerticalSensitivity\":1.0,\"SensitivityMode_Passenger\":1,\"HelicopterDriverWeaponHorizontalSensitivityTPP_Gamepad\":80.0,\"bEnableAdaptiveTrigger\":true,\"GamepadMDVSwitchingMode\":0,\"bInfantryVerticalMMReversed\":false,\"HelicopterDriverWeaponSensitivityFPP\":5.0,\"MDV_HelicopterDriver\":1.3300000429153,\"SensitivityMode_Passenger_Gamepad\":3,\"HorizontalGamepadSensitivity\":160.0,\"Sprint_ActionLogic_Gamepad\":0,\"AimHorizontalGamepadSensitivity\":110.0,\"DriverWeaponHorizontalSensitivityTPP_Gamepad\":300.0,\"ADS_Sensitivty_Ratio\":1.0,\"BaseADSSensitivity_Passenger\":1.0,\"PassengerWeaponHorizontalSensitivityFPP_Gamepad\":150.0,\"VerticalSensitivity\":1.0,\"DrugDuration\":0,\"bReloadBreakMirror\":false,\"DriverWeaponHorizontalSensitivityFPP_Gamepad\":150.0,\"CannonCamFollowGamepad\":false,\"MDV_Passenger\":1.3300000429153,\"GamepadLeftThumbMaximumThreshold\":100.0,\"bGunnerVerticalMMReversedGamepad\":false,\"bSprintCloseLootPanel\":false,\"DriverWeaponSensitivityFPP\":5.0,\"bVehicleVerticalMMReversed\":false,\"HelicopterDriverWeaponVerticalSensitivityFPP_Gamepad\":80.0,\"SensitivityMode_HelicopterDriver\":1,\"ADSExtraSensitivityDataHDStartTime\":100.0,\"FlagColorA\":100.0,\"BaseADSSensitivity_Driver_Gamepad\":1.0,\"GamepadLeftThumbDeadZone\":10.0,\"ExtraSensitivityDataHDStartTime\":33.0,\"Sensitivity\":3.0,\"GamepadMDV\":1.3300000429153,\"MDV_Driver\":1.3300000429153,\"bShareSpoils\":false,\"bHelicopterVerticalMMReversed\":true,\"bFixedWingTPP\":true,\"SensitivityMode_Driver\":1,\"bAimAssistorGamepad\":true,\"BaseADSSensitivity_HelicopterDriver\":1.0,\"BaseADSSensitivity_Passenger_Gamepad\":1.0,\"bVaultTriggerMode\":false,\"ADSExtraSensitivityDataHDHorizontalValue\":20.0,\"bSafeBoxPreferHighValueItem\":true,\"bVehicleVerticalMMReversedGamepad\":false},\"MDVListGamepad\":{\"1\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"2\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"3\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"4\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153],\"0\":[1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153,1.3300000429153]},\"ADSListGamepad\":{\"1\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]],\"2\":[[3.0,1.0],[3.0,1.0],[3.0,1.0],[3.0,1.0]],\"3\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]],\"4\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]],\"0\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]]},\"ADSList\":{\"1\":[[2.6600000858307,1.0],[2.2699999809265,1.0],[2.0,1.0],[1.6200000047684,1.0]],\"2\":[[3.0,1.0],[3.0,1.0],[3.0,1.0],[3.0,1.0]],\"3\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]],\"4\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]],\"0\":[[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0],[1.0,1.0]]}}"
local defaultKeySettingJsonStr = "{\"SavedVersion\":6,\"HeroJsonObjs\":[{\"KMSettingDiffRows\":[],\"HeroId\":\"0\"}]}"
local defaultMeta = "{\"Slots\":[{\"index\":1,\"timestamp\":1754916711,\"name\":\"+++1\"}],\"SaveVersion\":4}"

DummyServerLogic.DebugOn = isexist("DFM.Business.Module.SystemSettingModule.Logic.HD.Debug.DummyServerLogic_DO_NOT_SUBMIT")
DummyServerLogic.Case = {
    NewPlayer = {
        name = "NewPlayer",
        data = {},
    },
    OldPlayerWithoutMeta = {
        name = "OldPlayer1", -- 老玩家，但是还没有在新的云设置实现版本下开过游戏
        data = {
            {type = "CommonSettingHD", key = "Data", value = defaultCopmmonSettingJsonStr},
            {type = "CommonSettingHD", key = "DataKeySetting", value = defaultKeySettingJsonStr},
        },
    },
    OldPlayerNormal = {
        name = "OldPlayer2", -- 老玩家，并在新的云设置实现版本下开过游戏
        data = {
            {type = "CommonSettingHD", key = "Meta", value = defaultMeta},
            {type = "CommonSettingHD", key = "Data", value = defaultCopmmonSettingJsonStr},
            {type = "CommonSettingHD", key = "Data1", value = defaultCopmmonSettingJsonStr},
            {type = "CommonSettingHD", key = "DataKeySetting", value = defaultKeySettingJsonStr},
            {type = "CommonSettingHD", key = "DataKeySetting1", value = defaultKeySettingJsonStr},
        },
    },
    OldPlayerDeleteAll = {
        name = "OldPlayer3", -- 老玩家，并在新的云设置实现版本下开过游戏，并删除了所有设置
        data = {
            {type = "CommonSettingHD", key = "Meta", value = defaultMeta},
            {type = "CommonSettingHD", key = "Data", value = defaultCopmmonSettingJsonStr},
            {type = "CommonSettingHD", key = "Data1", value = defaultCopmmonSettingJsonStr},
            {type = "CommonSettingHD", key = "DataKeySetting", value = defaultKeySettingJsonStr},
            {type = "CommonSettingHD", key = "DataKeySetting1", value = defaultKeySettingJsonStr},
        },
    },
}
local _Data = {}
local _RequestQueue = {}
local _TimerHandle = nil
local log = function(...) print("[DummyServerLogic]", ...) end

function DummyServerLogic.PrepareCase(case)
    if DummyServerLogic.DebugOn then
        _Data = case.data
    end
end

function DummyServerLogic.Request(type, key, value, callback, dummyResult)
    if DummyServerLogic.DebugOn then
        table.insert(_RequestQueue, {reqType = type, reqKey = key, reqValue = value, resCallback = callback, dummyRes = dummyResult })
        DummyServerLogic._PumpQueueDelay()
    end
end

function DummyServerLogic._PumpQueue()
    local data = _RequestQueue[1]
    if data then
        table.remove(_RequestQueue, 1)
        if data.resCallback then
            local res = data.dummyRes
            if not res then
                if data.reqKey then
                    log("putvalue:", data.reqKey, data.reqValue)
                    local bFoundOld = false
                    if #_Data > 0 then
                        for k, v in ipairs(_Data) do
                            if v.type == data.reqType and v.key == data.reqKey then
                                v.value = data.reqValue
                                _Data[k] = v
                                bFoundOld = true
                                break
                            end
                        end
                    end
                    if not bFoundOld then
                        table.insert(_Data, {type = data.reqType, key = data.reqKey, value = data.reqValue})
                    end
                end
                res = {
                    result = 0,
                    kv_array = _Data,
                }
            end
            log("res:", table.dump(res))
            data.resCallback(res)
        end
        DummyServerLogic._PumpQueueDelay()
    end

end

function DummyServerLogic._PumpQueueDelay()
    if _TimerHandle == nil then
        _TimerHandle = Timer.DelayCall(0.1, function()
            _TimerHandle = nil
            DummyServerLogic._PumpQueue()
        end)
    end
end

return DummyServerLogic