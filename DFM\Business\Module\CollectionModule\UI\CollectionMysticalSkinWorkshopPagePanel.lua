----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMCollection)
----- LOG FUNCTION AUTO GENERATE END -----------



local CollectionMysticalSkinWorkshopPagePanel = ui("CollectionMysticalSkinWorkshopPagePanel")
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local ItemHelperTool = require "DFM.StandaloneLua.BusinessTool.ItemHelperTool"
local CollectionLogic = require "DFM.Business.Module.CollectionModule.Logic.CollectionLogic"
local CommonItemHighlight = require "DFM.Business.Module.CommonWidgetModule.UI.ItemView.Components.CommonItemHighlight"
local HDKeyIconBox = require "DFM.Business.Module.CommonWidgetModule.UI.HD.HDKeyIconBox"
local ETipsTriggerReason = import("ETipsTriggerReason")
local EGPInputModeType = import "EGPInputModeType"
local CollectionConfig = Module.Collection.Config

-- BEGIN MODIFICATION @ VIRTUOS : Navigation
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local ECheckButtonState = import"ECheckButtonState"
local EGPUINavGroupTransSimpleStrategy = import "EGPUINavGroupTransSimpleStrategy"
-- END MODIFICATION

function CollectionMysticalSkinWorkshopPagePanel:Ctor()
    self._wtDropDown = UIUtil.WndDropDownBox(self, "wtDropDown", self._OnSortOptionTabIndexChanged)
    self._wtFilterBtn = self:Wnd("wtFilterBtn", DFCommonButtonOnly)
    self._wtFilterBtn:Event("OnClicked",self._OnFilterBtnClick,self)
    self._wtEmptyBtn = self:Wnd("wtEmptyBtn", DFCommonButtonOnly)
    self._wtEmptyBtn:Event("OnClicked",self._OnEmptyBtnClick,self)
    self._wtSourceItemGridBox = UIUtil.WndScrollGridBox(self, "wtSourceItemGridBox", self._OnGetSourceItemsCount, self._OnProcessSourceItemWidget)
    self._wtSourceDragSelectBox = self:Wnd("wtSourceDragSelectBox", CommonItemHighlight)
    self._wtTargetItemGridBox = UIUtil.WndScrollGridBox(self, "wtTargetItemGridBox", self._OnGetTargetItemsCount, self._OnProcessTargetItemWidget)
    self._wtTargetDragSelectBox = self:Wnd("wtTargetDragSelectBox", CommonItemHighlight)
    self._wtQualityIcon = self:Wnd("wtQualityIcon", UIImage)
    self._wtRecombineDesTxt = self:Wnd("wtRecombineDesTxt", UITextBlock)
    self._wtRecombineDesTxt:SetText(CollectionConfig.Loc.MysticalSkinRecombineDes)
    self._wtRecombineNumTxt = self:Wnd("wtRecombineNumTxt", UITextBlock)
    self._wtRecombineResultHintTxt = self:Wnd("wtRecombineResultHintTxt", UITextBlock)
    self._wtRecombineResultHintTxt:SetText(CollectionConfig.Loc.MatrixRecombinationHint)
    self._wtClearTargetListBtn = self:Wnd("wtClearTargetListBtn", DFCommonButtonOnly)
    self._wtClearTargetListBtn:Event("OnClicked", self._OnResetMysticalSkinPool, self)
    self._wtFillTargetListBtn = self:Wnd("wtFillTargetListBtn", DFCommonButtonOnly)
    self._wtFillTargetListBtn:Event("OnClicked", self._OnFillMysticalSkinPool, self)
    self._wtFillTargetListBtn:Event("OnDeClicked", self._OnFillTargetListBtnDeClicked, self)
    self._wtRecombineBtn = self:Wnd("wtRecombineBtn", DFCommonButtonOnly)
    self._wtRecombineBtn:Event("OnClicked", self._OnRecombineMysticalSkins, self)
    self._wtRecombineBtn:Event("OnDeClicked", self._OnRecombineBtnDeClicked, self)
    self._wtRecombineBtn:SetMainTitle(CollectionConfig.Loc.Recombine)
    self._wtTipCheckBtn = self:Wnd("wtTipCheckBtn", DFCheckBoxOnly)
    self._wtTipCheckBtn:Event("OnCheckStateChanged", self._OnShowTipCheckBoxStateChanged, self)
    self._wtTipAnchor = UIUtil.WndTipsAnchor(self, "wtTipAnchor", self._OnShowInstruction, self._OnHideInstruction)
    self._wtSourceEmptyHint = self:Wnd("wtSourceEmptyHint", UIWidgetBase)
    self._wtSourceEmptyHint:SetCppValue("Set_Type", 1)
    self._wtSourceEmptyHint:BP_Set_Type()
    self._wtTargetEmptyHint = self:Wnd("wtTargetEmptyHint", UIWidgetBase)
    self._wtTargetEmptyHint:SetCppValue("Set_Type", 4)
    self._wtTargetEmptyHint:SetCppValue("Text", CollectionConfig.Loc.MysticalSkinAddToRecombineHint)
    self._wtTargetEmptyHint:BP_Set_Type()
    self._wtVectorAnimComp = self:Wnd("wtVectorAnimComp", UIWidgetBase)
    self:SetMode(0)
    self._dropDownIndex = -1
    self._sortTitleList = {}
    self._srotFuncList = {}
    self._sourceWeaponSkinItems = {}
    self._targetWeaponSkinItems = {}
    self._selectedSourcePos = -1
    self._selectedTargetPos = -1
    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.HideInfo)
    if IsHD() then
        self._wtFillTargetListBtn:Collapsed()
    end
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, {})
    self:SetType(0)
end


function CollectionMysticalSkinWorkshopPagePanel:OnInitExtraData()
    self._sortTitleList = {}
    table.insert(self._sortTitleList, CollectionConfig.Loc.QualitySortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.QualitySortDecend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.WearSortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.WearSortDecend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.GainTimeSortAscend)
    table.insert(self._sortTitleList, CollectionConfig.Loc.GainTimeSortDecend)
    self._srotFuncList = {}
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinQualitySortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinQualitySortDecend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinWearSortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinWearSortDecend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinGainTimeSortAscend)
    table.insert(self._srotFuncList, CollectionLogic.MysticalSkinGainTimeSortDecend)
end


-- UI打开时触发, 顺序先于OnShow
-- 子控件被主UI创建，若有图片资源加载，在此设置（进入视口后）
---@overload fun(LuaUIBaseView, OnOpen)
function CollectionMysticalSkinWorkshopPagePanel:OnOpen()
    self:SetHasTabBar(not IsHD())
end

---@overload fun(LuaUIBaseView, OnClose)
function CollectionMysticalSkinWorkshopPagePanel:OnClose()
    self:RemoveAllLuaEvent()
    self._selectedSourceCell = nil
    self._selectedTargetCell = nil
end

function CollectionMysticalSkinWorkshopPagePanel:OnShowBegin()
    self:EnableGamepadFeature()
end

function CollectionMysticalSkinWorkshopPagePanel:OnHideBegin()
    self:DisableGamepadFeature()
    self:ClosePopup()
    self:_StopDragTimer()
end

---@overload fun(LuaUIBaseView, OnShow)
function CollectionMysticalSkinWorkshopPagePanel:OnShow()
    self:SetCPPValue("WantedInputMode", EGPInputModeType.UIOnly)
end

-- UI OnClose前、调用Hide()隐藏时触发
---@overload fun(LuaUIBaseView, OnHide)
function CollectionMysticalSkinWorkshopPagePanel:OnHide()
end


-- 动画结束回调
---@overload fun(LuaUIBaseView, OnAnimFinished)
function CollectionMysticalSkinWorkshopPagePanel:OnAnimFinished(anim)
    if anim == self.WBP_Collections_ProductionMatrix_in then
        if not hasdestroy(self._selectedSourceCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedSourceCell, true)
        elseif not hasdestroy(self._selectedTargetCell) then
            WidgetUtil.SetUserFocusToWidget(self._selectedTargetCell, true)
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:ToggleControlAndListeners(bEnable, bFullReset)
    if bEnable == true then
        self:AddListeners()
    else
        if bFullReset == true then
            self._selectedSourcePos = -1
            self._selectedSourceCell = nil
            self._selectedTargetPos = -1
            self._selectedTargetCell = nil
        end
        self:RemoveAllLuaEvent()
    end
end

function CollectionMysticalSkinWorkshopPagePanel:AddListeners()
	self:AddLuaEvent(CollectionConfig.Events.evtOnAddWorkshopItemToTargetPool, self._AddToTargetList, self)
    self:AddLuaEvent(CollectionConfig.Events.evtOnRemoveWorkshopItemFromTargetPool, self._RemoveFromTargetList, self)
    self:AddLuaEvent(CollectionConfig.Events.evtOnWorkshopFilterUpdated, self._OnWorkshopFilterUpdated, self)
    self:AddLuaEvent(Server.GunsmithServer.Events.evtCSWAssemblyApplySkinRes, self._OnWeaponSkinApplied, self)
end


function CollectionMysticalSkinWorkshopPagePanel:RefreshView(mainTabIndex, bResetList, bResetTab)
    if bResetTab == true then
        self._dropDownIndex = 1
        UIUtil.InitDropDownBox(self._wtDropDown, self._sortTitleList, {}, 0)
    else
        self._dropDownIndex = self._dropDownIndex > 0 and self._dropDownIndex or 1   
    end
    self:_OnRefreshWeaponSkinItems(bResetList)
end

function CollectionMysticalSkinWorkshopPagePanel:_OnRefreshWeaponSkinItems(bResetList)
    self._wtDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
    self._sourceWeaponSkinItems = {}
    if bResetList then
        self._targetWeaponSkinItems = {}
    elseif #self._targetWeaponSkinItems > 0 then
        local validTargetSkins = {}
        for index, skinItem in ipairs(self._targetWeaponSkinItems) do
            if Server.CollectionServer:IsOwnedWeaponSkin(skinItem.id, skinItem.gid) == true then
                table.insert(validTargetSkins, skinItem)
            end
        end
        self._targetWeaponSkinItems = validTargetSkins
    end
    self._consumeNum = CollectionConfig.RecombineNum
    self._targetSLotNum = CollectionConfig.DefaultWorkshopSlotNum
    if #self._targetWeaponSkinItems > 0 then
        local recombineInfoDataRow = Facade.TableManager:GetRowByKey("MysticalSkinCombineBaseDataTable", tostring(self._targetWeaponSkinItems[1].quality))
        if isvalid(recombineInfoDataRow) and recombineInfoDataRow.CombineConsumeNum > 0 then
            self._consumeNum = recombineInfoDataRow.CombineConsumeNum
        end
        local param = {
            ["countNum"] = tostring(#self._targetWeaponSkinItems),
            ["maxNum"] = tostring(self._consumeNum)
        }
        self._wtRecombineNumTxt:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.NumFormat, param))
        self._wtRecombineNumTxt:SelfHitTestInvisible()
        self._wtQualityIcon:AsyncSetImagePath(CollectionConfig.QualityIconMapping[self._targetWeaponSkinItems[1].quality])
        self._wtQualityIcon:SetColorAndOpacity(ItemConfigTool.GetItemQualityLinearColor(self._targetWeaponSkinItems[1].quality))
        self._wtQualityIcon:SelfHitTestInvisible()
        --[[
        self._wtRecombineResultHintTxt:SetText(StringUtil.Key2StrFormat(CollectionConfig.Loc.MatrixRecombinationRuleHint,
        {["skinNum"] = tostring(self._consumeNum),
        ["qualityName"] = CollectionConfig.QualityType2Name[self._targetWeaponSkinItems[1].quality]}))
        --]]
    else
        self._wtRecombineNumTxt:Collapsed()
        --self._wtRecombineResultHintTxt:SetText(CollectionConfig.Loc.MatrixRecombinationHint)
        self._wtQualityIcon:Collapsed()
    end
    --[[
    if #self._targetWeaponSkinItems > 18 then
        self._targetSLotNum = (math.ceil(#self._targetWeaponSkinItems/2)+1)*2
    end
    --]]
    local filterData = CollectionLogic.GetLastMysticalSkinFilterData()
    local selectedQualityIDs = filterData.selectedQualityIDs
    local selectedGunTypeIDs = filterData.selectedGunTypeIDs
    local seasonChoice = filterData.seasonChoice
    local weaponChoice = filterData.weaponChoice
    local wearRange = filterData.wearRange
    self._bShowWearDetailValueOnItem = wearRange.min > 0 or wearRange.max < 5
    local ownedMysticalSkinsWithType = CollectionLogic.GetWeaponSkins(CollectionConfig.EItemGroup.Owned, CollectionConfig.EItemType.Mystical, nil, nil, nil, seasonChoice)
    if table.nums(selectedQualityIDs) > 0 then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            if selectedQualityIDs[skinItem.quality] ~= nil then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    if table.nums(selectedGunTypeIDs) > 0 then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            if selectedGunTypeIDs[skinItem.itemSubType] ~= nil then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    if weaponChoice ~= nil and weaponChoice > 0 then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            if CollectionLogic.GetBaseWeaponIdFromSkinId(skinItem.id) == weaponChoice then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    if self._bShowWearDetailValueOnItem then
        local filteredSkins = {} 
        for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
            local wearValue = skinItem:GetRawPropInfo().mystical_skin_data.wear/ItemConfigTool.GetMysticalWearRate()
            if wearValue >= wearRange.min and wearValue <= wearRange.max then
                table.insert(filteredSkins, skinItem)
            end
        end
        ownedMysticalSkinsWithType = filteredSkins
    end
    for index, skinItem in ipairs(ownedMysticalSkinsWithType) do
        local bShouldIgnore = false
        for index, targetSkinItem in ipairs(self._targetWeaponSkinItems) do
            if skinItem.id == targetSkinItem.id and skinItem.gid == targetSkinItem.gid or skinItem.quality ~= targetSkinItem.quality then
                bShouldIgnore = true
                break
            end
        end
        if bShouldIgnore == false then
            table.insert(self._sourceWeaponSkinItems, skinItem)
        end
    end
    table.sort(self._sourceWeaponSkinItems, self._srotFuncList[self._dropDownIndex])
    if bResetList then
        self._selectedSourcePos = -1
        self._selectedTargetPos = -1
        self._selectedSourceCell = nil
        self._selectedTargetCell = nil
        self._lastSelectedSourcePos = -1
        self._lastSelectedTargetPos = -1
        if IsHD() and WidgetUtil.IsGamepad() and #self._sourceWeaponSkinItems > 0 then
            self._lastSelectedSourcePos = 0
        end
        self._wtDropDown:BP_SetMainTabText(self._sortTitleList[self._dropDownIndex] or "")
        self._wtTargetItemGridBox:Visible()
        self._wtSourceItemGridBox:RefreshAllItems()
        self._wtTargetItemGridBox:RefreshAllItems()
    else
        if #self._targetWeaponSkinItems == 0 then
            self._selectedTargetPos = -1
            if self._lastSelectedSourcePos < self._firstVisibleSourcePos or self._lastSelectedSourcePos > self._lastVisibleSourcePos then
                self._lastSelectedSourcePos = self._firstVisibleSourcePos
            end
        end
        if #self._sourceWeaponSkinItems == 0 then
            self._selectedSourcePos = -1
            self._lastSelectedTargetPos = #self._targetWeaponSkinItems-1
        end
        if #self._targetWeaponSkinItems == 0 then
            self._selectedTargetPos = -1
        end
        self._wtSourceItemGridBox:RefreshVisibleItems()
        self._wtTargetItemGridBox:RefreshVisibleItems()
    end
    if self._wtVectorAnimComp then
        self._wtVectorAnimComp:SetType(1)
    end
    if #self._sourceWeaponSkinItems == 0 then
        self._wtSourceEmptyHint:SelfHitTestInvisible()
    else
        self._wtSourceEmptyHint:Collapsed()
    end
    if #self._targetWeaponSkinItems == 0 then
        self._wtTargetEmptyHint:SelfHitTestInvisible()
        self._wtTargetItemGridBox:Hidden()
    else
        self._wtTargetEmptyHint:Collapsed()
        self._wtTargetItemGridBox:Visible()
    end
    self._shortcutList = {}
    if IsHD() and WidgetUtil.IsGamepad() then
        if self._selectedSourcePos > -1 or self._selectedTargetPos > -1 then
            table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = self._OpenDetailPanel, caller = self ,bUIOnly = false, bHideIcon = false})
        end
        if self._selectedSourcePos > -1 then
            table.insert(self._shortcutList, {actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalSkinPool, caller = self ,bUIOnly = false, bHideIcon = false}) 
        end
    else
        table.insert(self._shortcutList, {actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalSkinPool, caller = self ,bUIOnly = false, bHideIcon = false}) 
    end
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
    self:UpdateBackground()
    self:_RefreshActionBtns()
end


function CollectionMysticalSkinWorkshopPagePanel:OnRefreshModel(curSubStageType)

end


function CollectionMysticalSkinWorkshopPagePanel:_OnGetTabItemCount()
    return #self._sortTitleList
end


function CollectionMysticalSkinWorkshopPagePanel:_OnSortOptionTabIndexChanged(position)
    if self._dropDownIndex ~= position + 1 then
        self._dropDownIndex = position + 1
        self:_OnRefreshWeaponSkinItems(true)
        --UIUtil.SetDropDownBoxByIndex(self._wtDropDown, position)
    else
        if IsHD() and WidgetUtil.IsGamepad() then
            self._wtDropDown:BP_SwitchCheckButtonState(ECheckButtonState.Unchecked)
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnGetSourceItemsCount()
    return #self._sourceWeaponSkinItems or 0
end

function CollectionMysticalSkinWorkshopPagePanel:_OnProcessSourceItemWidget(position, itemWidget)
    self._firstVisibleSourcePos = math.min(self._firstVisibleSourcePos or 0, position)
    self._lastVisibleSourcePos = math.max(self._lastVisibleSourcePos or 0, position)
    local item = self._sourceWeaponSkinItems[position + 1]
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetCppValue("bHandleClick", true)
    itemWidget:SetButtonEnable(false)
    if isvalid(item) then
        local bLocked = false
        local lockText = ""
        if #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= item.quality then
            bLocked = true
            lockText = CollectionConfig.Loc.QualityDifferent
        end
        if item.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
            bLocked = true
            lockText = CollectionConfig.Loc.CannotRecombine
        end
        itemWidget:BindClickCallback(CreateCallBack(self._OnSourceWeaponSkinItemClick, self, itemWidget, position)) 
        local mysticalInfo = item:GetRawPropInfo().mystical_skin_data
        if IsHD() and WidgetUtil.IsGamepad() then
            itemWidget:BindDetailCallback(nil) 
            itemWidget:BindDoubleClickCallback(nil)
            itemWidget:BindDragCallback(nil)
        else
            itemWidget:BindDetailCallback(CreateCallBack(self._OpenDetailPanel, self, item, itemWidget, true)) 
            if bLocked == false then
                itemWidget:BindDoubleClickCallback(CreateCallBack(self._AddToTargetList, self, item))
                itemWidget:BindDragCallback(CreateCallBack(self._OnDragItem, self, item, true))
            else
                itemWidget:BindDoubleClickCallback(nil)
                itemWidget:BindDragCallback(nil)
            end
        end
        if IsHD() then
            local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local rarity = 0
            if mysticalInfo then
                rarity = mysticalInfo.rarity
                if Module.Inventory.Config.MysticalSkinRarityTxtMapping[rarity] then
                    rateText = Module.Inventory.Config.MysticalSkinRarityTxtMapping[rarity]
                end
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    wearText = ItemConfigTool.GetMysticalWearConfig(mysticalInfo.wear) or ""
                    wearText = string.format(Module.ItemDetail.Config.Loc.MysticalSkinWearWithValue, wearText, wear)
                end
            end
            local bShowUniqueNumRarityWear = rarity == MysticalSkinRarityType.RarityHigh
            local bShowUniqueNumRarityWearKillCount = rarity == MysticalSkinRarityType.RarityPremium or rarity == MysticalSkinRarityType.RarityExtraordinary
            local bShowWearOnly = item.quality > ItemConfig.EWeaponSkinQualityType.Unknown and item.quality < ItemConfig.EWeaponSkinQualityType.Orange
            seasonIndex = 1
            local skinConfig = CollectionLogic.GetWeaponSkinDataRow(item.id)
            if skinConfig then 
                local seasonIdList = CollectionLogic.GetWeaponSkinSeasonIDList()
                for index, seasonId in ipairs(seasonIdList) do
                    if skinConfig.SeasonID == seasonId then 
                        seasonIndex = index
                        break
                    end
                end
            end
            local instruction = {
                {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = mysticalInfo.custom_name ~= "" and mysticalInfo.custom_name or item.name}},
                {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = StringUtil.Key2StrFormat(CollectionConfig.Loc.SeasonTip,{["SeasonIndex"] = seasonIndex})}, styleRowId = "C001"},
            }
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.UniqueNum, value = tostring(mysticalInfo.unique_no)}}
                )
                table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Rarity, value = rateText}}
            )
            end
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true or bShowWearOnly == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Wear, value = wearText}}
                )
            end
            if bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.KillCount, value = MathUtil.GetNumberFormatStr(mysticalInfo.kill_cnter)}}
                )
            end
            if bLocked == false then
                table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = "MysticalWorkshop_ClickToAdd"}}}}
            )
            end
            itemWidget:SetGivenInstructionContents(instruction)
        end
        local itemWearText = ""
        if self._bShowWearDetailValueOnItem == true then
            if mysticalInfo then
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    itemWearText = StringUtil.Key2StrFormat(CollectionConfig.Loc.ItemDetailWear, {["value"] = wear})
                end
            end
        end
        itemWidget:InitCollectionMysticalSkinItem(item, CollectionLogic.GetBaseWeaponIdFromSkinId(item.id), CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid), bLocked, lockText, self._bShowWearDetailValueOnItem and itemWearText or nil)
    end
    if IsHD() and WidgetUtil.IsGamepad() and self._selectedTargetPos == -1 and self._lastSelectedSourcePos == position then
        WidgetUtil.SetUserFocusToWidget(itemWidget, true)
        itemWidget:ShowGivenInstruction()
    elseif self._selectedSourcePos == position then
        self._selectedSourceCell = itemWidget
        itemWidget:SetSelected(item, true)
    else
        itemWidget:SetSelected(nil, false)
    end
    itemWidget:Visible()
end

function CollectionMysticalSkinWorkshopPagePanel:_OnSourceWeaponSkinItemClick(itemCell, position)
    self._selectedTargetPos = -1
    if self._selectedSourcePos ~= position then
        if not hasdestroy(self._selectedTargetCell) then
            self._selectedTargetCell:SetSelected(nil, false)
        end
        if not hasdestroy(self._selectedSourceCell) then
            self._selectedSourceCell:SetSelected(nil, false)
        end
        self._selectedSourceCell = itemCell
        self._selectedSourcePos = position
        self._lastSelectedSourcePos = position
        self._selectedSourceCell:SetSelected(nil, true)
        self._bShouldIgnoreItemClick = false
    elseif IsHD() and WidgetUtil.IsGamepad() then
        if self._bShouldIgnoreItemClick then
            self._bShouldIgnoreItemClick = false
            return
        end
        local item = self._sourceWeaponSkinItems[position + 1]
        self:_AddToTargetList(item)
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnGetTargetItemsCount()
    return self._targetSLotNum or 0
end

function CollectionMysticalSkinWorkshopPagePanel:_OnProcessTargetItemWidget(position, itemWidget)
    self._firstVisibleTargetPos = math.min(self._firstVisibleTargetPos or 0, position)
    self._lastVisibleTargetPos = math.max(self._lastVisibleTargetPos or 0, position)
    local item = self._targetWeaponSkinItems[position + 1]
    itemWidget:SetCppValue("bIsFocusable", IsHD() and WidgetUtil.IsGamepad() and isvalid(item))
    itemWidget:SetCppValue("bHandleClick", IsHD() and WidgetUtil.IsGamepad())
    itemWidget:SetButtonEnable(false)
    itemWidget:BindClickCallback(CreateCallBack(self._OnTargetWeaponSkinItemClick, self, itemWidget, position))
    itemWidget:BindDetailCallback(nil)
    itemWidget:BindDoubleClickCallback(nil)
    itemWidget:BindDragCallback(nil)
    if isvalid(item) then
        local itemWearText = nil
        local mysticalInfo = item:GetRawPropInfo().mystical_skin_data
        if WidgetUtil.IsGamepad() then
        else
            itemWidget:BindDetailCallback(CreateCallBack(self._OpenDetailPanel, self, item, itemWidget, false))
            itemWidget:BindDoubleClickCallback(CreateCallBack(self._RemoveFromTargetList, self, item))
            itemWidget:BindDragCallback(CreateCallBack(self._OnDragItem, self, item, false))
        end
        if IsHD() then
            local rateText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local wearText = Module.ItemDetail.Config.Loc.MysticalSkinDefaultText
            local rarity = 0
            if mysticalInfo then
                rarity = mysticalInfo.rarity
                if Module.Inventory.Config.MysticalSkinRarityTxtMapping[rarity] then
                    rateText = Module.Inventory.Config.MysticalSkinRarityTxtMapping[rarity]
                end
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    wearText = ItemConfigTool.GetMysticalWearConfig(mysticalInfo.wear) or ""
                    wearText = string.format(Module.ItemDetail.Config.Loc.MysticalSkinWearWithValue, wearText, wear)
                end
            end
            local bShowUniqueNumRarityWear = rarity == MysticalSkinRarityType.RarityHigh
            local bShowUniqueNumRarityWearKillCount = rarity == MysticalSkinRarityType.RarityPremium or rarity == MysticalSkinRarityType.RarityExtraordinary
            local bShowWearOnly = item.quality > ItemConfig.EWeaponSkinQualityType.Unknown and item.quality < ItemConfig.EWeaponSkinQualityType.Orange
            seasonIndex = 1
            local skinConfig = CollectionLogic.GetWeaponSkinDataRow(item.id)
            if skinConfig then 
                local seasonIdList = CollectionLogic.GetWeaponSkinSeasonIDList()
                for index, seasonId in ipairs(seasonIdList) do
                    if skinConfig.SeasonID == seasonId then 
                        seasonIndex = index
                        break
                    end
                end
            end
            local instruction = {
                {id = UIName2ID.Assembled_CommonMessageTips_V1, data = {textContent = mysticalInfo.custom_name ~= "" and mysticalInfo.custom_name or item.name}},
                {id = UIName2ID.Assembled_CommonMessageTips_V2, data = {textContent = StringUtil.Key2StrFormat(CollectionConfig.Loc.SeasonTip,{["SeasonIndex"] = seasonIndex})}, styleRowId = "C001"},
            }
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.UniqueNum, value = tostring(mysticalInfo.unique_no)}}
                )
                table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Rarity, value = rateText}}
            )
            end
            if bShowUniqueNumRarityWear == true or bShowUniqueNumRarityWearKillCount == true or bShowWearOnly == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.Wear, value = wearText}}
                )
            end
            if bShowUniqueNumRarityWearKillCount == true then
                table.insert(instruction, 
                    {id = UIName2ID.Assembled_CommonMessageTips_V3, data = {name = CollectionConfig.Loc.KillCount, value = MathUtil.GetNumberFormatStr(mysticalInfo.kill_cnter)}}
                )
            end
            table.insert(instruction, 
                {id = UIName2ID.Assembled_CommonKeyTips_V2, data = {summaryList = {{actionName = "MysticalWorkshop_ClickToRemove"}}}}
            )
            itemWidget:SetGivenInstructionContents(instruction)
        end
        if self._bShowWearDetailValueOnItem == true then
            if mysticalInfo then
                if mysticalInfo.wear then
                    local wearScale = ItemConfigTool.GetMysticalWearRate()
                    local wearScaleDec = #tostring(wearScale) - 1
                    local wear = string.format("%."..wearScaleDec.."f", mysticalInfo.wear/wearScale)
                    itemWearText = StringUtil.Key2StrFormat(CollectionConfig.Loc.ItemDetailWear, {["value"] = wear})
                end
            end
        end
        itemWidget:InitCollectionMysticalSkinItem(item, item and CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) or nil, nil, nil, self._bShowWearDetailValueOnItem and itemWearText or nil)
    else
        itemWidget:InitCollectionMysticalSkinItem()
    end
    if IsHD() and WidgetUtil.IsGamepad() and self._selectedSourcePos == -1 and self._lastSelectedTargetPos == position then
        WidgetUtil.SetUserFocusToWidget(itemWidget, true)
        itemWidget:ShowGivenInstruction()
    elseif self._selectedTargetPos == position then
        itemWidget:SetSelected(item, true)
        self._selectedTargetCell = itemWidget
    else
        itemWidget:SetSelected(nil, false)
    end
    itemWidget:Visible()
end

function CollectionMysticalSkinWorkshopPagePanel:_OnTargetWeaponSkinItemClick(itemCell, position)
    local item = self._targetWeaponSkinItems[position + 1]
    if isvalid(item) then
        self._selectedSourcePos = -1
        if self._selectedTargetPos ~= position then
            if not hasdestroy(self._selectedSourceCell) then
                self._selectedSourceCell:SetSelected(nil, false)
            end
            if not hasdestroy(self._selectedTargetCell) then
                self._selectedTargetCell:SetSelected(nil, false)
            end
            self._selectedTargetCell = itemCell
            self._selectedTargetPos = position
            self._lastSelectedTargetPos = position
            self._selectedTargetCell:SetSelected(nil, true)
            self._bShouldIgnoreItemClick = false
        elseif IsHD() and WidgetUtil.IsGamepad() then
            if self._bShouldIgnoreItemClick then
                self._bShouldIgnoreItemClick = false
                return
            end
            self._selectedTargetCell:HideGivenInstruction()
            self:_RemoveFromTargetList(item)
        end
    elseif self._selectedSourcePos ~= -1 then
        self:_AddToTargetList(self._sourceWeaponSkinItems[self._selectedSourcePos+1])
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_AddToTargetList(sourceItem)
    Module.ItemDetail:CloseItemDetailPanel()
    if sourceItem then
        if sourceItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange then
            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.CannotRecombineTip)
            return
        end
        if #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= sourceItem.quality then
            return
        end
        if #self._targetWeaponSkinItems >= self._consumeNum then
            Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.MatrixRecombinationEnoughTip)
            return
        end
        local targetIndex = nil
        for index, item in ipairs(self._targetWeaponSkinItems) do
            if item.id == sourceItem.id and item.gid == sourceItem.gid then
                targetIndex = index
                break
            end
        end
        if targetIndex == nil then
            table.insert(self._targetWeaponSkinItems, sourceItem)
            if self._lastSelectedSourcePos+1 >= #self._sourceWeaponSkinItems then
                self._lastSelectedSourcePos = #self._sourceWeaponSkinItems -2
                self._bShouldIgnoreItemClick = true
            end
            if not IsHD() or not WidgetUtil.IsGamepad() then
                self._selectedSourcePos = -1
            end
            self:_OnRefreshWeaponSkinItems(false)
            self._wtTargetItemGridBox:ScrollToItem(#self._targetWeaponSkinItems-1, false, false, 10, 0, true)
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RemoveFromTargetList(targetItem)
    Module.ItemDetail:CloseItemDetailPanel()
    if targetItem then
        local targetIndex = nil
        for index, item in ipairs(self._targetWeaponSkinItems) do
            if item.id == targetItem.id and item.gid == targetItem.gid then
                targetIndex = index
                break
            end
        end
        if targetIndex ~= nil then
            table.remove(self._targetWeaponSkinItems, targetIndex)
            if self._lastSelectedTargetPos+1 > #self._targetWeaponSkinItems then
                self._lastSelectedTargetPos = #self._targetWeaponSkinItems  -1
                self._bShouldIgnoreItemClick = true
            end
            if not IsHD() or not WidgetUtil.IsGamepad() then
                self._selectedTargetPos = -1
            end
            self:_OnRefreshWeaponSkinItems(false)
            self._wtTargetItemGridBox:ScrollToItem(#self._targetWeaponSkinItems-1, false, false, 10, 0, true)
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnDragItem(item, bFromSource)
    if bFromSource == true then
        self._sourceDragItem = item
        self._targetDragItem = nil
        if self._wtVectorAnimComp then
            self._wtVectorAnimComp:SetType(2)
            self._wtVectorAnimComp:PlayAnimation(self._wtVectorAnimComp.WBP_Collections_ProductionMatrix_Vector_Onetimecycle, 0, 1, EUMGSequencePlayMode.Forward, 1, false)
        end
    else
        self._sourceDragItem = nil
        self._targetDragItem = item
    end
    self:_StartDragTimer()
end


function CollectionMysticalSkinWorkshopPagePanel:_OnResetMysticalSkinPool()
    self._targetWeaponSkinItems = {}
    self:_OnRefreshWeaponSkinItems(false)
end

function CollectionMysticalSkinWorkshopPagePanel:_OnFillMysticalSkinPool()
    if #self._targetWeaponSkinItems < self._consumeNum and #self._targetWeaponSkinItems > 0 and #self._sourceWeaponSkinItems > 0 then
        local weaponSkinItemsForFill = {}
        for index, skinItem in ipairs(self._sourceWeaponSkinItems) do
            if skinItem.quality == self._targetWeaponSkinItems[1].quality then
                table.insert(weaponSkinItemsForFill, skinItem)
            end
        end
        table.sort(weaponSkinItemsForFill, function (a, b)
            local wearA = a:GetRawPropInfo().mystical_skin_data.wear
            local wearB = b:GetRawPropInfo().mystical_skin_data.wear
            if wearA ~= wearB then
                return wearA > wearB
            end
            if a.gid ~= b.gid then
                return a.gid < b.gid
            end
        end)
        for i = 1, self._consumeNum - #self._targetWeaponSkinItems, 1 do
            table.insert(self._targetWeaponSkinItems, weaponSkinItemsForFill[i])
        end
        self:_OnRefreshWeaponSkinItems(false)
    elseif #self._targetWeaponSkinItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOneSkinToPool)
    end
end


function CollectionMysticalSkinWorkshopPagePanel:_OnRecombineMysticalSkins()
    if self._targetWeaponSkinItems and #self._targetWeaponSkinItems > 0 then
        local newSkinNum = math.floor(#self._targetWeaponSkinItems/self._consumeNum)
        local fCallbackIns = CreateCallBack(function(self, res)
            if res.result == 0 then
                self._targetWeaponSkinItems = {}
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.MatrixRecombinationSuccessTip)
                local newWeaponSkins = {}
                for i, propChange in ipairs(res.data_change) do
                    propChange.prop.gid = setdefault(propChange.prop.gid, 0)
                    if propChange.change_type == PropChangeType.Add and propChange.reason == ePropChangeReason.CollectionCombineAddSkin then
                        local item = ItemHelperTool.CreateItemByPropInfo(propChange.prop, propChange)
                        if item then
                            table.insert(newWeaponSkins, item)
                        end
                    end
                end
                if #newWeaponSkins > 0 then
                    Module.Reward:OpenRewardPanel(ServerTipCode.GetItemTitle, nil, newWeaponSkins, nil, false, false, true)
                end
            end
        end,self)
        for index, skinItem in ipairs(self._targetWeaponSkinItems) do
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(skinItem.id, skinItem.gid) == true then
                Module.CommonTips:ShowConfirmWindow(
                    CollectionConfig.Loc.ResetSKinTip,
                    function()
                        Module.CommonTips:ShowConfirmWindow(
                            StringUtil.Key2StrFormat(CollectionConfig.Loc.MatrixRecombinationConfirmationTip,
                                {["skinNum"] = tostring(newSkinNum*self._consumeNum),
                                ["qualityName"] = CollectionConfig.QualityType2Name[self._targetWeaponSkinItems[1].quality],
                                ["qualityName"] = CollectionConfig.QualityType2Name[self._targetWeaponSkinItems[1].quality]}),
                            function()
                                CollectionLogic.RecombineMysticalSkins(self._targetWeaponSkinItems, false, fCallbackIns)
                            end,
                            function()
                            end,
                            CollectionConfig.Loc.Cancel,
                            CollectionConfig.Loc.Confirm
                        )
                    end,
                    function()
                    end,
                    CollectionConfig.Loc.Cancel,
                    CollectionConfig.Loc.Confirm
                )
                return
            end
        end
        Module.CommonTips:ShowConfirmWindow(
            StringUtil.Key2StrFormat(CollectionConfig.Loc.MatrixRecombinationConfirmationTip,
                {["skinNum"] = tostring(newSkinNum*self._consumeNum),
                ["qualityName"] = CollectionConfig.QualityType2Name[self._targetWeaponSkinItems[1].quality],
                ["qualityName"] = CollectionConfig.QualityType2Name[self._targetWeaponSkinItems[1].quality]}),
            function()
                CollectionLogic.RecombineMysticalSkins(self._targetWeaponSkinItems, false, fCallbackIns)
            end,
            function()
            end,
            CollectionConfig.Loc.Cancel,
            CollectionConfig.Loc.Confirm
        )
    else
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddMysticalSkinToRecombineTip)
    end
end


function CollectionMysticalSkinWorkshopPagePanel:_OnRecombineBtnDeClicked()
    if #self._targetWeaponSkinItems < self._consumeNum then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddMysticalSkinToRecombineTip) 
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnFillTargetListBtnDeClicked()
    if #self._targetWeaponSkinItems == 0 then
        Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AddAtLeastOneSkinToPool)
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RefreshActionBtns()
    if #self._targetWeaponSkinItems > 0 then
        self._wtClearTargetListBtn:SelfHitTestInvisible()
    else
        self._wtClearTargetListBtn:Collapsed()
    end
    self._wtFillTargetListBtn:SetIsEnabledStyle(#self._targetWeaponSkinItems > 0 and #self._sourceWeaponSkinItems > 0)
    self._wtRecombineBtn:SetIsEnabledStyle(#self._targetWeaponSkinItems >= self._consumeNum)
    self:_AddRecombineInputAction()
end  

function CollectionMysticalSkinWorkshopPagePanel:_OpenDetailPanel(item, refWidget, bAdd)
    if IsHD() and WidgetUtil.IsGamepad() then
        if not item then
            if self._selectedSourcePos > -1 then
                item = self._sourceWeaponSkinItems[self._selectedSourcePos+1]
                refWidget = self._selectedSourceCell
                bAdd = true
            elseif self._selectedTargetPos > -1 then
                item = self._targetWeaponSkinItems[self._selectedTargetPos+1]
                refWidget = self._selectedTargetCell
                bAdd = false
            end
        end
    end
    if not item then
        return
    end
    local function fOnDetailPanelLoaded(detailIns)
        if detailIns then
            detailIns:SetDetailBtnVisible(true)
            local fCallbackIns = CreateCallBack(function(self, res)
                Module.Collection:ShowWeaponSkinDetailPage(item)
            end,self)
            detailIns:SetDetailBtnClickedCallback(fCallbackIns, self)
            self._detailView = detailIns
            if IsHD() and WidgetUtil.IsGamepad() then
                self._bShouldIgnoreItemClick = true
            end
        end
    end
    local function fOnDetailPanelClosed(detailIns)
        if IsHD() and WidgetUtil.IsGamepad() then
            WidgetUtil.SetUserFocusToWidget(refWidget, true)
        end
    end
    local btnTypeList = {}
    if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid) then
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.WeaponSkinApplied)
    else
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.ApplyWeaponSkin)
    end
    if item.quality < ItemConfig.EWeaponSkinQualityType.Orange then
        if bAdd == true then
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.AddWorkshopItemToTargetPool)
        else
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RemoveWorkshopItemFromTargetPool)
        end
    end 
    table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Trade)
    Module.ItemDetail:OpenItemDetailPanel(item, refWidget, false, true, btnTypeList, nil, fOnDetailPanelLoaded, nil, nil, fOnDetailPanelClosed)
end

function CollectionMysticalSkinWorkshopPagePanel:_OnShowTipCheckBoxStateChanged(bChecked)
    if not IsHD() then
        if bChecked then
            self:_OnShowInstruction()
        else
            self:_OnHideInstruction()
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnShowInstruction()
    local datas = {}
    table.insert(datas, {
        textContent = CollectionConfig.Loc.MatrixRecombinationTip,
        styleRowId = "C000"
    })
	self._tipHandle = Module.CommonTips:ShowCommonMessagesWithAnchor(datas, self._wtTipAnchor)
    if IsHD() and WidgetUtil.IsGamepad() then
        self._bShouldIgnoreItemClick = true
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnHideInstruction(reason)
    if self._tipHandle then
        Module.CommonTips:RemoveCommonMessageWithAnchor(self._tipHandle, self._wtTipAnchor)
        self._tipHandle = nil
        if reason ~= ETipsTriggerReason.Click then
            self._wtTipCheckBtn:SetSelectState(false, false)
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_StartDragTimer()
    self:_StopDragTimer()
    self._dragTimer = Timer:NewIns(0.1, 0)
    self._dragTimer:AddListener(self._DragTick, self)
    self._dragTimer:Start()
end

function CollectionMysticalSkinWorkshopPagePanel:_StopDragTimer()
    if isvalid(self._refreshTimer) then
        self._dragTimer:Release()
    end
    self._dragTimer = nil
end

function CollectionMysticalSkinWorkshopPagePanel:_DragTick()
    local previewItem = Module.CommonWidget:GetOrCreateDragItemView()
    if previewItem then
        local absolutePoint = UWidgetLayoutLibrary.GetMousePositionOnPlatform()
        if self._sourceDragItem then
            local bInsideSourceBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtSourceItemGridBox, absolutePoint)
            if bInsideSourceBox == false then
                self._wtTargetDragSelectBox:SelfHitTestInvisible()
                if self._sourceDragItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange 
                or #self._targetWeaponSkinItems > 0 
                and self._targetWeaponSkinItems[1].quality ~= self._sourceDragItem.quality 
                or 
                #self._targetWeaponSkinItems >= self._consumeNum then
                    self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonInvalid)
                else
                    self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
                end
            else
                self._wtTargetDragSelectBox:Collapsed()
            end
        elseif self._targetDragItem then
            local bInsideTargetBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtTargetItemGridBox, absolutePoint)
            if bInsideTargetBox == false then
                self._wtSourceDragSelectBox:SelfHitTestInvisible()
                self._wtSourceDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
            else
                self._wtSourceDragSelectBox:Collapsed()
            end
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:OnNativeOnMouseMove(inGeometry, inGestureEvent)
    --[[
    logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPagePanel:OnNativeOnMouseMove")
    if self._sourceDragItem then
        logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPagePanel:OnNativeOnMouseMove sourceDragItem valid")
        local absolutePosition = inGestureEvent:GetScreenSpacePosition()
        local bInsideTargetBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtTargetItemGridBox, absolutePosition)
        if bInsideTargetBox == true then
            self._wtTargetDragSelectBox:SelfHitTestInvisible()
            if self._sourceDragItem.quality >= ItemConfig.EWeaponSkinQualityType.Orange or #self._targetWeaponSkinItems > 0 and self._targetWeaponSkinItems[1].quality ~= self._sourceDragItem.quality or 
            #self._targetWeaponSkinItems >= self._consumeNum then
                self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonInvalid)
            else
                self._wtTargetDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
            end
        else
            self._wtTargetDragSelectBox:Collapsed()
        end
    elseif self._targetDragItem then
        logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPagePanel:OnNativeOnMouseMove targetDragItem valid")
        local absolutePosition = inGestureEvent:GetScreenSpacePosition()
        local bInsideSourceBox = UIUtil.CheckAbsolutePointInsideWidget(self._wtSourceItemGridBox, absolutePosition)
        if bInsideSourceBox == true then
            self._wtSourceDragSelectBox:SelfHitTestInvisible()
            self._wtSourceDragSelectBox:SetHighlightStyle(CommonItemHighlight.EHighlightStyle.CommonValid)
        else
            self._wtSourceDragSelectBox:Collapsed()
        end
    else
        logerror("[v_dzhanshen] CollectionMysticalSkinWorkshopPagePanel:OnNativeOnMouseMove no Item valid")
    end
	if UGPInputHelper.IsTouchOrLMBEvent(inGestureEvent) then
    end
    --]]
end


function CollectionMysticalSkinWorkshopPagePanel:OnHandleMouseButtonUpEvent(mouseEvent)
    local popController = Facade.UIManager:GetLayerControllerByType(EUILayer.Pop)
    local lastPopUI = popController ~= nil and popController:TryGetLastPopUI() or nil
    if not hasdestroy(self) and (lastPopUI == self._detailView or hasdestroy(lastPopUI)) then
        local beginTouchPos = mouseEvent:GetScreenSpacePosition()
        local isUnderDetailView = false
        if not hasdestroy(self._detailView) then
            local detailViewMainPanel = self._detailView:GetMainPanel()
            if not hasdestroy(detailViewMainPanel) then
                local rootGeometry = detailViewMainPanel:GetCachedGeometry()
                isUnderDetailView = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
            end
        end
        if not IsHD() or not WidgetUtil.IsGamepad() then
            if self._sourceDragItem or self._targetDragItem then
                self._wtSourceDragSelectBox:Collapsed()
                self._wtTargetDragSelectBox:Collapsed()
                if self._sourceDragItem then
                    local rootGeometry = self._wtSourceItemGridBox:GetCachedGeometry()
                    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                    if isUnder == false then
                        self:_AddToTargetList(self._sourceDragItem)
                    end
                elseif self._targetDragItem then
                    local rootGeometry = self._wtTargetItemGridBox:GetCachedGeometry()
                    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                    if isUnder == false then
                        self:_RemoveFromTargetList(self._targetDragItem)
                    end
                end
                self._sourceDragItem = nil
                self._targetDragItem = nil
            else
                if isUnderDetailView == false and #self._targetWeaponSkinItems == 0 then
                    local rootGeometry = self._wtTargetEmptyHint:GetCachedGeometry()
                    local isUnder = USlateBlueprintLibrary.IsUnderLocation(rootGeometry, beginTouchPos)
                    if isUnder then
                        if self._selectedSourcePos ~= -1 and self._selectedSourceCell ~= nil then
                            self:_AddToTargetList(self._sourceWeaponSkinItems[self._selectedSourcePos+1])
                        end
                    end
                end
            end
        end
        local sourceRootGeometry = self._wtSourceItemGridBox:GetCachedGeometry()
        local isUnderSourceRootGeometry = USlateBlueprintLibrary.IsUnderLocation(sourceRootGeometry, beginTouchPos)
        local targetRootGeometry = self._wtTargetItemGridBox:GetCachedGeometry()
        local isUnderTargetRootGeometry = USlateBlueprintLibrary.IsUnderLocation(targetRootGeometry, beginTouchPos)
        local targetRootGeometry2 = self._wtTargetEmptyHint:GetCachedGeometry()
        local isUnderTargetRootGeometry2 = USlateBlueprintLibrary.IsUnderLocation(targetRootGeometry2, beginTouchPos)
        if not isUnderSourceRootGeometry and not isUnderTargetRootGeometry and not isUnderTargetRootGeometry2 and not isUnderDetailView then
            if not hasdestroy(self._selectedSourceCell) then
                self._selectedSourceCell:SetSelected(nil, false)
            end
            if not hasdestroy(self._selectedTargetCell) then
                self._selectedTargetCell:SetSelected(nil, false)
            end
            self._selectedSourcePos = -1
            self._selectedTargetPos = -1
            Module.ItemDetail:CloseAllPopUI()
        end
    end
    self:_StopDragTimer()
    if self._wtVectorAnimComp then
        self._wtVectorAnimComp:SetType(1)
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnEmptyBtnClick()
    if self._selectedTargetPos ~= -1 and self._selectedTargetCell ~= nil then
        self:_RemoveFromTargetList(self._targetWeaponSkinItems[self._selectedTargetPos+1])
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnWorkshopFilterUpdated()
    self:_OnRefreshWeaponSkinItems(true)
end

function CollectionMysticalSkinWorkshopPagePanel:_OnWeaponSkinApplied(res)
    if self._detailView then
        local item = nil
        local itemWidget = nil
        local btnTypeList = {}   
        if self._selectedSourcePos ~= -1 then
            item = self._sourceWeaponSkinItems[self._selectedSourcePos+1]
            itemWidget = self._selectedSourceCell
            if item.quality < ItemConfig.EWeaponSkinQualityType.Orange then
                table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.AddWorkshopItemToTargetPool)
            end 
        elseif self._selectedTargetPos ~= -1 then
            item = self._targetWeaponSkinItems[self._selectedTargetPos+1]
            itemWidget = self._selectedTargetCell
            table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.RemoveWorkshopItemFromTargetPool) 
        end
        if item then
            if CollectionLogic.CheckIfSkinAppliedOnWeapon(item.id, item.gid, res) then
                table.insert(btnTypeList, 1, Module.ItemDetail.Config.ButtonType.WeaponSkinApplied)
                Module.CommonTips:ShowSimpleTip(CollectionConfig.Loc.AppearanceApplied)
            else
                table.insert(btnTypeList, 1, Module.ItemDetail.Config.ButtonType.ApplyWeaponSkin)
            end 
        end   
        table.insert(btnTypeList, Module.ItemDetail.Config.ButtonType.Trade)
        self._detailView:UpdateItem(item, itemWidget, false, true, btnTypeList, nil)
        self._detailView:SetDetailBtnVisible(true)
        self:_OnRefreshWeaponSkinItems(false)      
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OnFilterBtnClick()
    Facade.UIManager:AsyncShowUI(UIName2ID.CollectionMysticalSkinFilterPanel, nil, self, self._targetWeaponSkinItems[1])
end  

function CollectionMysticalSkinWorkshopPagePanel:ClosePopup()
    self:_OnHideInstruction()
end

function CollectionMysticalSkinWorkshopPagePanel:BindSetBackgourndCallback(callback, caller)
    self._setBackgourndCallback = SafeCallBack(callback, caller)
end

function CollectionMysticalSkinWorkshopPagePanel:UpdateBackground()
    if self._setBackgourndCallback then
        self._setBackgourndCallback(nil, false)
    end
end

--初始化相关
function CollectionMysticalSkinWorkshopPagePanel:EnableGamepadFeature()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    self._wtClearTargetListBtn:SetDisplayInputAction("Collection_Clear_Gamepad", true, nil, true)
    self._wtRecombineBtn:SetDisplayInputAction("Collection_Recombine_Gamepad", true, nil, true)
    if self._wtDropDown then
        if not self._wtSortModeCheckButton then
            self._wtSortModeCheckButton = self._wtDropDown:Wnd("DFCommonCheckButton", UIWidgetBase)
        end
        if self._wtSortModeCheckButton then
            local keyIcon = self._wtSortModeCheckButton:Wnd("wtKeyIcon", HDKeyIconBox)
            if keyIcon then
                keyIcon:SetOnlyDisplayOnGamepad(true)
                keyIcon:InitByDisplayInputActionName("Collection_Sort_Gamepad", true, 0, false)   
            end
            self._wtSortModeCheckButton:Event("OnCheckButtonStateChanged",self._OnSortModeDropDownBoxOpenStateChanged,self)
        end
    end
    if not self._wtDropDownHandle then
        self._wtDropDownHandle = self:AddInputActionBinding("Collection_Sort_Gamepad", EInputEvent.IE_Pressed, self._OpenDropDown,self, EDisplayInputActionPriority.UI_Stack)
    end 
    self._wtFilterBtn:SetDisplayInputAction("Collection_FilterRight_Gamepad", true, nil, true)   
    if not self._wtFilterBtnHandle then
        self._wtFilterBtnHandle = self:AddInputActionBinding("Collection_FilterRight_Gamepad", EInputEvent.IE_Pressed, self._OnFilterBtnClick,self, EDisplayInputActionPriority.UI_Stack)
    end   
    if not self._wtNavGroupScourceWeaponSkin then
        self._wtNavGroupScourceWeaponSkin = WidgetUtil.RegisterNavigationGroup(self._wtSourceItemGridBox, self, "Hittest")
        if self._wtNavGroupScourceWeaponSkin then
            local navStrategy = self._wtNavGroupScourceWeaponSkin:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupScourceWeaponSkin:SetScrollRecipient(self._wtSourceItemGridBox)
            self._wtNavGroupScourceWeaponSkin:AddNavWidgetToArray(self._wtSourceItemGridBox)
            self._wtNavGroupScourceWeaponSkin:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end
    if not self._wtNavGroupTargetWeaponSkin then
        self._wtNavGroupTargetWeaponSkin = WidgetUtil.RegisterNavigationGroup(self._wtTargetItemGridBox, self, "Hittest")
        if self._wtNavGroupTargetWeaponSkin then
            local navStrategy = self._wtNavGroupTargetWeaponSkin:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupTargetWeaponSkin:SetScrollRecipient(self._wtTargetItemGridBox)
            self._wtNavGroupTargetWeaponSkin:AddNavWidgetToArray(self._wtTargetItemGridBox)
            self._wtNavGroupTargetWeaponSkin:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
        end
    end 
    self._wtGainedFromActivityHint = self:Wnd("wtGainedFromActivityHint", UIWidgetBase)
    if not self._wtNavGroupTipCheckBtn then 
        self._wtNavGroupTipCheckBtn = WidgetUtil.RegisterNavigationGroup(self._wtGainedFromActivityHint, self, "Hittest")
        if self._wtNavGroupTipCheckBtn then
            local navStrategy = self._wtNavGroupTipCheckBtn:GetOwnerNavStrategy()
            if navStrategy then
                navStrategy:SetDifferentParentTransStrategy(EGPUINavGroupTransSimpleStrategy.LastFocusedOrHittestOrDistance)
            end  
            self._wtNavGroupTipCheckBtn:AddNavWidgetToArray(self._wtTipCheckBtn)
        end
    end
    if not self._hNavigationChangedFocus then
        self._hNavigationChangedFocus = self._wtNavGroupScourceWeaponSkin.OnNavGroupFocusReceivedEvent:Add(self._SourceGroupOnFocus, self)
    end
    if not self._hNavigationChangedFocus2 then
        self._hNavigationChangedFocus2 = self._wtNavGroupTargetWeaponSkin.OnNavGroupFocusReceivedEvent:Add(self._TargetGroupOnFocus, self)
    end
    self:_AddClearTargetPoolInputAction()
    self._bShouldIgnoreItemClick = true
    if not hasdestroy(self._selectedSourceCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedSourceCell, true)
    elseif not hasdestroy(self._selectedTargetCell) then
        WidgetUtil.SetUserFocusToWidget(self._selectedTargetCell, true)
    end
    self._NavConfigHandler = WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
end

function CollectionMysticalSkinWorkshopPagePanel:DisableGamepadFeature()
    if not IsHD() then
        return
    end
    self._bShouldIgnoreItemClick = false
    self:_RemoveClearTargetPoolInputAction()
    self:_RemoveRecombineInputAction()
    self:_RemoveSortModeDropDownNavGroup()
    self:_RemoveDropDownShortcuts()
    if self._wtFilterBtnHandle then
        self:RemoveInputActionBinding(self._wtFilterBtnHandle)
    end
    if self._wtDropDownHandle then
        self:RemoveInputActionBinding(self._wtDropDownHandle)
    end
    if self._wtSortModeCheckButton then
        self._wtSortModeCheckButton:RemoveEvent("OnCheckButtonStateChanged")
    end
    if self._hNavigationChangedFocus then
        self._wtNavGroupScourceWeaponSkin.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus)
    end
    if self._hNavigationChangedFocus2 then
        self._wtNavGroupTargetWeaponSkin.OnNavGroupFocusReceivedEvent:Remove(self._hNavigationChangedFocus2)
    end
    if self._NavConfigHandler then
        WidgetUtil.DisableDynamicNavConfig(self._NavConfigHandler)
    end
    self._NavConfigHandler = nil
    self._wtDropDownHandle = nil
    self._wtFilterBtnHandle = nil
    self._wtNavGroupScourceWeaponSkin = nil
    self._wtNavGroupTargetWeaponSkin = nil
    self._wtNavGroupTipCheckBtn = nil
    self._wtClearTargetListBtnHandle = nil
    self._wtRecombineBtnHandle = nil
    self._hNavigationChangedFocus = nil
    self._hNavigationChangedFocus2 = nil
    WidgetUtil.RemoveNavigationGroup(self)
end

function CollectionMysticalSkinWorkshopPagePanel:OnInputTypeChanged(inputType)
    Module.ItemDetail:CloseAllPopUI()
    self._bShouldIgnoreItemClick = true
    self._wtSourceItemGridBox:RefreshVisibleItems()
    self._wtTargetItemGridBox:RefreshVisibleItems()
    if #self._sourceWeaponSkinItems == 0 and #self._targetWeaponSkinItems == 0 then
        self._shortcutList = {}
        CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_SetDefaultGamepadFocus(bPriorityOnSource)
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if bPriorityOnSource then
        WidgetUtil.TryFocusDefaultWidgetByGroup(#self._sourceWeaponSkinItems > 0 and self._wtNavGroupScourceWeaponSkin or self._wtNavGroupTargetWeaponSkin)
    else
        WidgetUtil.TryFocusDefaultWidgetByGroup(#self._targetWeaponSkinItems > 0 and self._wtNavGroupTargetWeaponSkin or self._wtNavGroupScourceWeaponSkin)
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_SourceGroupOnFocus()
    self._bShouldIgnoreItemClick = true
    self._shortcutList = {}
    if #self._sourceWeaponSkinItems > 0 then
        table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = self._OpenDetailPanel, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    table.insert(self._shortcutList, {actionName = "MysticalWorkshop_FillPool",func = self._OnFillMysticalSkinPool, caller = self ,bUIOnly = false, bHideIcon = false}) 
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
end

function CollectionMysticalSkinWorkshopPagePanel:_TargetGroupOnFocus()
    self._bShouldIgnoreItemClick = true
    self._shortcutList = {}
    if #self._targetWeaponSkinItems > 0 then
        table.insert(self._shortcutList, {actionName = "GunSkin_ShowDetail",func = self._OpenDetailPanel, caller = self ,bUIOnly = false, bHideIcon = false})
    end
    CollectionLogic.RegStackUIInputSummary(self._shortcutList, false)
end

function CollectionMysticalSkinWorkshopPagePanel:_AddClearTargetPoolInputAction()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtClearTargetListBtnHandle then 
        self._wtClearTargetListBtnHandle = self:AddHoldInputActionBinding("Collection_Clear_Gamepad",self._OnResetMysticalSkinPool, self, EDisplayInputActionPriority.UI_Stack)
        self:AddHoldInputActionReleaseBinding(self._wtClearTargetListBtnHandle, self.OnClearTargetListBtnLongPressFinished, self)
        if self._wtClearTargetListBtn then
            local _wtCommonButtonHD = self._wtClearTargetListBtn:Wnd("DFCommonButton", UIWidgetBase)
            if _wtCommonButtonHD then
                self._wtClearTargetListBtnKeyIcon = _wtCommonButtonHD:Wnd("KeyIcon", UIWidgetBase)
                if self._wtClearTargetListBtnKeyIcon then
                    self._wtClearTargetListBtnKeyIcon:BP_ShowHoldProgressBarTips(true)
                end
            end
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RemoveClearTargetPoolInputAction()
    if self._wtClearTargetListBtnHandle then
        self:RemoveHoldInputActionBinding(self._wtClearTargetListBtnHandle)
    end
    self._wtClearTargetListBtnHandle = nil
end


function CollectionMysticalSkinWorkshopPagePanel:_AddRecombineInputAction()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._wtRecombineBtnHandle then 
        self._wtRecombineBtnHandle = self:AddHoldInputActionBinding("Collection_Recombine_Gamepad",self._OnRecombineMysticalSkins, self, EDisplayInputActionPriority.UI_Stack)
        -- 监听confirm按钮长按结束事件
        self:AddHoldInputActionReleaseBinding(self._wtRecombineBtnHandle, self.OnRecombineBtnLongPressFinished, self)
        if self._wtRecombineBtn then
            local _wtCommonButtonHD = self._wtRecombineBtn:Wnd("DFCommonButton_PCOnly", UIWidgetBase)
            if _wtCommonButtonHD then
                self._wtRecombineBtnKeyIcon = _wtCommonButtonHD:Wnd("KeyIcon", UIWidgetBase)
                if self._wtRecombineBtnKeyIcon then
                    self._wtRecombineBtnKeyIcon:BP_ShowHoldProgressBarTips(true)
                end
            end
        end
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RemoveRecombineInputAction()
    if self._wtRecombineBtnHandle then
        self:RemoveHoldInputActionBinding(self._wtRecombineBtnHandle)
    end
    self._wtRecombineBtnHandle = nil
end

function CollectionMysticalSkinWorkshopPagePanel:_OnSortModeDropDownBoxOpenStateChanged(eCheckButtonState)
    if not IsHD() then
        return
    end
    if eCheckButtonState == ECheckButtonState.UncheckedPressed then
        self:_RegisterSortModeDropDownNavGroup()
        self:_InitDropDownShortcuts()
        self._bShouldIgnoreItemClick = true
    elseif eCheckButtonState == ECheckButtonState.Unchecked then
        self:_RemoveSortModeDropDownNavGroup()
        self:_RemoveDropDownShortcuts()
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RegisterSortModeDropDownNavGroup()
    if not IsHD() or not WidgetUtil.IsGamepad() then
        return
    end
    if not self._SortModeDropDownListNavGroup then
        if self._wtDropDown and self._wtDropDown.ScrollGridBox then
            self._SortModeDropDownListNavGroup = WidgetUtil.RegisterNavigationGroup(self._wtDropDown.ScrollGridBox, self._wtDropDown, "Hittest")
        end
        if self._SortModeDropDownListNavGroup then 
            self._SortModeDropDownListNavGroup:AddNavWidgetToArray(self._wtDropDown.ScrollGridBox)
            self._SortModeDropDownListNavGroup:SetScrollRecipient(self._wtDropDown.ScrollGridBox)
            self._SortModeDropDownListNavGroup:MarkIsStackControlGroup()
            if self._sortTitleList[self._dropDownIndex] then
                self._wtDropDown.ScrollGridBox:ScrollToItem(self._dropDownIndex - 1, false, false, 10, 0, false)
            end
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._SortModeDropDownListNavGroup)
        end  
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RemoveSortModeDropDownNavGroup()
    if not IsHD() then
        return
    end
    WidgetUtil.RemoveNavigationGroup(self._wtDropDown)
    if self._SortModeDropDownListNavGroup then
        self._SortModeDropDownListNavGroup = nil
    end  
end

function CollectionMysticalSkinWorkshopPagePanel:_InitDropDownShortcuts()
    if not self._closeDropDownHandler then
        self._closeDropDownHandler = self:AddInputActionBinding("Back_Gamepad", EInputEvent.IE_Pressed, self._CloseDropDown, self, EDisplayInputActionPriority.UI_Pop)
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_RemoveDropDownShortcuts()
    if self._closeDropDownHandler then
        self:RemoveInputActionBinding(self._closeDropDownHandler)
        self._closeDropDownHandler = nil
    end
end

function CollectionMysticalSkinWorkshopPagePanel:_OpenDropDown()
    self._wtSortModeCheckButton:NavigationClick()
end

function CollectionMysticalSkinWorkshopPagePanel:_CloseDropDown()
    self._wtSortModeCheckButton:NavigationClick()
end

function CollectionMysticalSkinWorkshopPagePanel:OnRecombineBtnLongPressFinished()
    if not IsHD() then
        return 
    end
    if self._wtRecombineBtnKeyIcon then
        self._wtRecombineBtnKeyIcon:BP_UpdateProgressBar(0) 
    end
end

function CollectionMysticalSkinWorkshopPagePanel:OnClearTargetListBtnLongPressFinished()
    if not IsHD() then
        return 
    end
    if self._wtClearTargetListBtnKeyIcon then
        self._wtClearTargetListBtnKeyIcon:BP_UpdateProgressBar(0) 
    end
end
-- END MODIFICATION


return CollectionMysticalSkinWorkshopPagePanel
