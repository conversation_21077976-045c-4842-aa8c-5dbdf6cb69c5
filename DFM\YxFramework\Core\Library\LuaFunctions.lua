---------------------------------------------------------------------------------
--- 独立于框架的全局系统方法
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
--- system extends
---------------------------------------------------------------------------------
function declare_if_nil(table, key, initValue)
	if not table[key] then
		table[key] = initValue
	end
end

function setdefault(value, default)
    if value ~= nil then
        return value
    else
        return default
    end
end

function countstep(value, step)
    if value ~= nil then
        return value + step
    else
        return step
    end
end

function isvalid(tb)
	if tb == nil then
		return false
	end
	if type(tb) == "userdata" and not slua.isValid(tb) then
		return false
    -- elseif type(tb) == "table" and tb.__cppinst and not slua.isValid(tb.__cppinst) then
    --     return false
    end
	return true
end

function isinvalid(tb)
    return not isvalid(tb)
end

function iscppvalid(t)
    if not t then return false end

    local tt = type(t)
    if tt == "userdasta" then
        return slua.isValid(t)
    elseif tt == "table" then
        local cppinst = rawget(t, "__cppinst")
        if cppinst then
            return slua.isValid(cppinst)
        end
        return false
    end

    return false
end

function hasdestroy(obj)
    if type(obj) == 'userdata' then return not isvalid(obj) end

    local _has_destroy_ = obj == nil and true or rawget(obj, "_has_destroy_")
    return _has_destroy_
end

function issubclass(A, B)
    if (type(A) == "userdata") and (type(B) == "userdata") then
        return ULuautils.IsSubClass(A, B)
    else
        if (type(A) == "table") and A.__cppinst then
            return ULuautils.IsSubClass( A.__cppinst, B)
        end

        if (type(B) == "table") and B.__cppinst then
            return ULuautils.IsSubClass( A, B.__cppinst)
        end

        if (type(A) == "table") and A.__cppinst and (type(B) == "table") and B.__cppinst then
            return ULuautils.IsSubClass( A.__cppinst, B.__cppinst)
        end
        print("!!!  error IsSubClass A ro B not userdata ")
        print("!!!   A  ",A,"  type  ", type(A))
        print("!!!   A  ",B,"  type  ", type(B))
    end
    return false
end

function getcpp(t)
    if type(t) == "table" then
        t = t.__cppinst 
    end
    getmetatable(t).classname = UKismetSystemLibrary.GetObjectName(getmetatable(t))
    return getmetatable(t).classname
end

function checknumber(value, base)
    return tonumber(value, base) or 0
end

function checkint(value)
    return math.round(checknumber(value))
end

function checkbool(value)
    return (value ~= nil and value ~= false)
end

function checktable(value)
    if type(value) ~= "table" then
        value = {}
    end
    return value
end

function isset(hashtable, key)
    local t = type(hashtable)
    return (t == "table" or t == "userdata") and hashtable[key] ~= nil
end

function ensure(isTrue, msg)
    if not isTrue then
        error(msg)
    end
end

function combine(tb, combineTable)
    for k, v in pairs(combineTable) do
        if type(v) == "function" then
            tb[k] = v
        end
    end
end

function extendclass(classA, classB)
    for k, v in pairs(classB) do
        if type(v) == "function" then
            if k == "Ctor" or k == "Destroy" then
                local fOldFunc = classA[k]
                local fExtndFunc = v
                local fNewFunc = function(self, ...)
                    fOldFunc(self, ...)
                    fExtndFunc(self, ...)
                end
                v = fNewFunc
            else
                ensure(classA[k] == nil, "Function with same name exists when entend class.")
            end
            classA[k] = v
            -- rawset(classA, k, v)
        end
    end

    -- 对于扩展类，标记为已处理
    rawset(classB, "__ft", true)
end

function enum2string(enum, value)
    assert(type(enum) == "table")
    for k,v in pairs(enum) do
        if v == value then
            return k
        end
    end

    return string.format("enum2string value not found in enum", value)
end

---------------------------------------------------------------------------------
--- math extends
---------------------------------------------------------------------------------
function math.round(value)
    value = checknumber(value)
    return math.floor(value + 0.5)
end

function math.clamp(value, minValue, maxValue)  
    if value < minValue then
        return minValue
    end
    if value > maxValue then
        return maxValue
    end
    return value
end

function math.lerp(curValue, targetValue, alpha)
    alpha = math.clamp(alpha, 0, 1)
    return curValue + (targetValue - curValue) * alpha
end

function math.interpto(curValue, targetValue, delta, speed)
    if speed <= 0 then
        return curValue
    end
    local dist = targetValue - curValue
    local deltaStep = dist * math.clamp(delta * speed, 0, 1)
    return curValue + deltaStep
end

function math.newrandomseed()
    local ok, socket =
        pcall(
        function()
            return require("socket")
        end
    )

    if ok then
        math.randomseed(socket.gettime() * 1000)
    else
        math.randomseed(os.time())
    end
    math.random()
    math.random()
    math.random()
    math.random()
end

local oldceil = math.ceil
function math.ceil(x)
    if type(x) == "userdata" then
        return oldceil(LuaUint64.value(x));
    end
    return oldceil(x);
end

local oldfloor = math.floor
function math.floor(x)
    if type(x) == "userdata" then
        return oldfloor(LuaUint64.value(x));
    end
    return oldfloor(x);
end
---------------------------------------------------------------------------------
--- table extends
---------------------------------------------------------------------------------
function dump(data, showMetatable, lastCount)
    if type(data) == "table" and data._cname ~= nil then
        logwarning("only allow dump raw table, abort dump")
        logwarning("dump only basic info", data, data._cname)
        return 
    end

	local output = ""

	local function append(...)
		local args = {...}
		local str
		if not pcall(function() str = string.format(table.unpack(args)) end) then
			str = args[1]
		end
		output = output..str
	end

	local dumpDic = {}
	local function dumpex(data, showMetatable, lastCount)
		if type(data) ~= "table" then
			if type(data) == "string" then
				append("\""..data.."\"")
			else
				append(tostring(data))
			end
		else
			append(" "..tostring(data))
			if not dumpDic[data] then
				dumpDic[data] = true

				local count = lastCount or 0
				count = count + 1
				append(" {\n")
				if showMetatable then
					for i = 1, count do
						append("\t")
					end
					local mt = getmetatable(data)
					append("\"__metatable\" = ")
					dumpex(mt, showMetatable, count)
					append(",\n")
				end
				for key, value in pairs(data) do
					for i = 1, count do
						append("\t")
					end
					--print("---- "..tostring(key))
					if type(key) == "string" then
						append("\""..key.."\" =")
					elseif type(key) == "number" then
						append("["..key.."] = ")
					else
						append(tostring(key))
					end
					dumpex(value, showMetatable, count)
					append(",\n")
				end
				for i = 1, lastCount or 0 do
					append("\t")
				end
				append("}")
			end
		end
		if not lastCount then
			append("\n")
		end
	end

	dumpex(data, showMetatable, lastCount)
	print(output)
end

-- 克隆一个table，例如 local same = clone(sometable)
function clone(t, cache, bIncludeMetaTable)
    if type(t) ~= "table" then
        return t
    end
    cache = cache or {}
    bIncludeMetaTable = setdefault(bIncludeMetaTable, true)
    local result = {}
    cache[t] = result
    local key, value = next(t, nil)
    while key do
        if type(value) == "table" then
            if not cache[value] then
                cache[value] = clone(value, cache)
            end
            result[key] = cache[value]
        else
            result[key] = value
        end
        key, value = next(t, key)
    end
    -- for k, v in pairs(t) do
    --     if type(v) == "table" then
    --         if not cache[v] then
    --             cache[v] = clone(v, cache)
    --         end
    --         result[k] = cache[v]
    --     else
    --         result[k] = v
    --     end
    -- end
    if bIncludeMetaTable then
        local mt = getmetatable(t)
        if mt then
            setmetatable(result, mt)
        end
    end
    return result
end

function deepcopy(dst, src)
    bIncludeMetaTable = setdefault(bIncludeMetaTable, true)
    local key, value = next(src, nil)
    while key do
        if type(value) == "table" and value ~= src then
            if type(dst[key]) ~= "table" then
                dst[key] = {}
            end
            deepcopy(dst[key], value)
        else
            dst[key] = value
        end
        key, value = next(src, key)
    end
    if bIncludeMetaTable then
        local mt = getmetatable(src)
        if mt then
            setmetatable(dst, mt)
        end
    end
    -- for k, v in pairs(src) do
    --     if type(v) == "table" and v ~= src then
    --         if type(dst[k]) ~= "table" then
    --             dst[k] = {}
    --         end
    --         deepcopy(dst[k], v)
    --     else
    --         dst[k] = v
    --     end
    -- end
end

---@generic T
---@param src T
---@param isCpyMetaTable boolean
---@return T
function simpleclone(src, isCpyMetaTable)
    local dest = {}
    if type(src) == "table" then
        for key, value in pairs(src) do
            dest[key] = value
        end
    elseif type(src) == "userdata" then
        if IsDynamicUD(src) then
            return CloneDynamicUD(src)
        end
    end
    if isCpyMetaTable then
        local scrMeta = getmetatable(src)
        setmetatable(dest, scrMeta)
    end
    return dest
end

function table.nums(t)
    if t == nil then
        return 0
    end
    local count = 0
    for k, v in pairs(t or {}) do
        count = count + 1
    end
    return count
end


function table.first(t)
    if t == nil then
        return nil
    end
    for k, v in pairs(t or {}) do
        return v
    end
    return nil
end

function table.keys(hashtable)
    local keys = {}
    if hashtable then
        for k, v in pairs(hashtable) do
            keys[#keys + 1] = k
        end
    else
        error('hashtable is nil',hashtable)
    end
    return keys
end

function table.values(hashtable)
    local values = {}
    for k, v in pairs(hashtable) do
        values[#values + 1] = v
    end
    return values
end

function table.merge(dest, src)
    for k, v in pairs(src) do
        dest[k] = v
    end
end

function table.insertto(dest, src, begin)
    begin = checkint(begin)
    if begin <= 0 then
        begin = #dest + 1
    end

    local len = #src
    if len <= 0 then
        return dest
    end
    for i = 0, len - 1 do
        dest[i + begin] = src[i + 1]
    end
end

function table.indexof(array, value, begin)
    for i = begin or 1, #array do
        if array[i] == value then
            return i
        end
    end
    return 0
end

function table.keyof(hashtable, value)
    for k, v in pairs(hashtable) do
        if v == value then
            return k
        end
    end
    return 0
end

function table.removeByFunc(tb, conditionFunc)
    -- body
    if tb ~= nil and next(tb) ~= nil then
        -- todo
        for i = #tb, 1, -1 do
            if conditionFunc(tb[i]) then
                -- todo
                table.remove(tb, i)
            end
        end
    end
end

function table.removebyvalue(array, value, bRemoveAll)
    local c, i, max = 0, 1, #array
    while i <= max do
        if array[i] == value then
            table.remove(array, i)
            c = c + 1
            i = i - 1
            max = max - 1
            if not bRemoveAll then
                break
            end
        end
        i = i + 1
    end
    return c
end

function table.removebyvalueret(array, value, bRemoveAll)
    local ret = {}
    for i = 1, #array do
        if array[i] == value then
            table.insert(ret, i)
            if not bRemoveAll then
                break
            end
        end
    end
    for idx = #ret, 1, -1 do
        table.remove(array, ret[idx])
    end
    return ret
end

function table.map(t, fn)
    for k, v in pairs(t) do
        t[k] = fn(v, k)
    end
end

function table.walk(t, fn)
    for k, v in pairs(t) do
        fn(v, k)
    end
end

function table.filter(t, fn)
    for k, v in pairs(t) do
        if not fn(v, k) then
            t[k] = nil
        end
    end
end

function table.find(t, fn)
    for k, v in pairs(t) do
        if fn(v, k) then
            return v, k
        end
    end
end

function table.contains(t, value)
    for k, v in pairs(t) do
        if v == value then
            return true
        end
    end
    return false
end

function table.containskey(t, value)
    for k, v in pairs(t) do
        if k == value then
            return true
        end
    end
    return false
end

function table.mapcontainsclear(t, value)
    local ret = {}
    for k, v in pairs(t) do
        if v == value then
            table.insert(ret, k)
        end
    end
    for _, k in ipairs(ret) do
        t[k] = nil
    end
    return ret
end

function table.arrcontainsclear(array, value)
    local ret = {}
    if array ~= nil and next(array) ~= nil then
        for i = #array, 1, -1 do
            if array[i] == value then
                table.insert(ret, i)
            end
        end
        for idx = #ret, 1, -1 do
            table.remove(array, ret[idx])
        end
    end
    return ret
end

-- table的插入排序（比table.sort更稳定）
---@param list T[]
---@param comp? fun(a: T, b: T):boolean
function table.insertionSort(list, comp)
    if comp then
        for i = 2, #list, 1 do
            local tmp = list[i]
            local j = i - 1
            while j >= 1 and comp(tmp, list[j]) do
                list[j+1] = list[j]
                j = j - 1
            end
            list[j+1] = tmp
        end
    end
end

-- 直接选择排序插入
function table.directlyInsert(list, value, comp)
    -- 默认升序,小于号表示升序，大于号表示降序，不需要等号，保证稳定性
    local sortType = setdefault(comp, function (a, b)
        return a < b
    end)
    local len = #list
    for i = len, 0, -1 do
        if i == 0  then
            list[1] = value
        else
            if sortType(value, list[i]) then
                list[i+1] = list[i]
            else
                list[i+1] = value
                break
            end
        end

    end
end

---@param t table
---@param value v
---@param bOrder boolean 是否按从小到大的顺序
function table.orderfindkey(t, value, bOrder)
    local length = #t
    if bOrder then
        for i = 1, length do
            if t[i] == value then
                return i
            end
        end
    else
        for i = -length, -1 do
            if t[-i] == value then
                return -i
            end
        end
    end
    return false
end

---@param t table
---@param value v
---@param bOrder boolean 是否按从小到大的顺序
function table.orderfindkeyByFunc(t, processFunc, bOrder)
    local length = #t
    if bOrder then
        for i = 1, length do
            if processFunc(t[i]) == true then
                return i
            end
        end
    else
        for i = -length, -1 do
            if processFunc(t[-i]) == true then
                return -i
            end
        end
    end
    return false
end

function table.isInList(v, t)
    if not t then
        return false
    end
    for _, value in ipairs(t or {}) do
        if value == v then
            return true
        end
    end
    return false
end

function table.findall(t, fn)
    local Ret = {}
    for k, v in pairs(t) do
        if fn(v, k) then
            table.insert(Ret, v)
        end
    end
    return Ret
end

function table.getkeys(t)
    local list = {}
    for k, v in pairs(t) do
        list[#list + 1] = k
    end
    return list
end

function table.getkeynum(t)
    local count = 0
    for i, v in pairs(t) do
        count = count + 1
    end
    return count
end

function table.unique(t, bArray)
    local check = {}
    local n = {}
    local idx = 1
    for k, v in pairs(t) do
        if not check[v] then
            if bArray then
                n[idx] = v
                idx = idx + 1
            else
                n[k] = v
            end
            check[v] = true
        end
    end
    return n
end

function table.empty(t)
    for k in pairs(t) do
        t[k] = nil
    end
end

function table.isempty(t)
    return not t or next(t) == nil
end

function table.dump(t)
    local str = "{"
    if type(t) ~= "table" then
        str = str .. tostring(t)
    else
        for k, v in pairs(t) do
            str = str .. k .. ":"
            if type(v) ~= "table" then
                str = str .. tostring(v)
            else
                str = str .. table.dump(v)
            end
            str = str .. ", "
        end
    end
    str = str .. "}"

    return str
end

function table.exchangekv(t)
    local exchangeT = {}
    for k, v in pairs(t) do
        exchangeT[v] = k
    end
    return exchangeT
end

function table.tolist(t)
    local array = {}
    local insert = table.insert
    for _, v in pairs(t) do
        insert(array, v)
    end
    return array
end

function table.append(dst, src)
    local insert = table.insert
    for _, v in ipairs(src) do
        insert(dst, v)
    end
    return dst
end

function table.partial(func, ...)
    local curriedArgs = table.pack(...)
    local curriedArgsNum = curriedArgs.n
    return function(...)
        local Args = {}
        for Idx = 1, curriedArgsNum do
            Args[Idx] = curriedArgs[Idx]
        end

        local reservedArgs = table.pack(...)
        local reservedArgsNum = reservedArgs.n
        for Idx = 1, reservedArgsNum do
            Args[Idx + curriedArgsNum] = reservedArgs[Idx]
        end

        return func(table.unpack(Args, 1, curriedArgsNum + reservedArgsNum))
    end
end

--- idx-list完全相同
function table.equal(dst, src)
    if src and dst then
        if #src == 0 and #dst == 0 then
            return true
        end
        if #src ~= #dst then
            return false
        end
        for i, v in ipairs(src) do
            if dst[i] ~= v then
                return false
            end
        end
        return true
    else
        if src == nil and dst == nil then
            return true
        end
        return false
    end
end

--- key-map完全相同
function table.equal_map(dst, src)
    if src and dst then
        for k, v in pairs(src) do
            if dst[k] ~= v then
                return false
            end
        end
        for k, v in pairs(dst) do
            if src[k] ~= v then
                return false
            end
        end
        return true
    else
        if src == nil and dst == nil then
            return true
        end
        return false
    end
end
---------------------------------------------------------------------------------
--- string extends
---------------------------------------------------------------------------------
string._htmlspecialchars_set = {}
string._htmlspecialchars_set["&"] = "&amp;"
string._htmlspecialchars_set['"'] = "&quot;"
string._htmlspecialchars_set["'"] = "&#039;"
string._htmlspecialchars_set["<"] = "&lt;"
string._htmlspecialchars_set[">"] = "&gt;"

function string.htmlspecialchars(input)
    for k, v in pairs(string._htmlspecialchars_set) do
        input = string.gsub(input, k, v)
    end
    return input
end

function string.restorehtmlspecialchars(input)
    for k, v in pairs(string._htmlspecialchars_set) do
        input = string.gsub(input, v, k)
    end
    return input
end

function string.nl2br(input)
    return string.gsub(input, "\n", "<br />")
end

function string.text2html(input)
    input = string.gsub(input, "\t", "    ")
    input = string.htmlspecialchars(input)
    input = string.gsub(input, " ", "&nbsp;")
    input = string.nl2br(input)
    return input
end

function string.split(input, delimiter)
    input = tostring(input)
    delimiter = tostring(delimiter)
    if (delimiter == "") then
        return false
    end
    local pos, arr = 0, {}
    -- for each divider found
    for st, sp in function()
        return string.find(input, delimiter, pos, true)
    end do
        table.insert(arr, string.sub(input, pos, st - 1))
        pos = sp + 1
    end
    table.insert(arr, string.sub(input, pos))
    return arr
end

function string.ltrim(input)
    return string.gsub(input, "^[ \t\n\r]+", "")
end

function string.rtrim(input)
    return string.gsub(input, "[ \t\n\r]+$", "")
end

function string.trim(input)
    input = string.gsub(input, "^[ \t\n\r]+", "")
    return string.gsub(input, "[ \t\n\r]+$", "")
end

function string.ucfirst(input)
    return string.upper(string.sub(input, 1, 1)) .. string.sub(input, 2)
end

--获取字符的codepoint编码
function string.charunicode(char)
	--十进制codepoint
	local codepoint = utf8.codepoint(char)
	--基础字符直接转16进制
	if codepoint < 0x10000 then
		return '%u' .. string.upper(string.format("%04x", codepoint))
	end
	--扩展字符分为高低代理对
	--代码点减去 0x10000，会得到一个位于 0x000000 和 0x0fffff 之间的数字
	local p = codepoint - 0x10000
	--高位代理
	local h = math.floor(p / 0x400 + 0xd800)
	--低位代理
	local l = math.fmod(p, 0x400) + 0xdc00
	--拼接一下高低代理形成代理对
	return '%u' .. string.upper(string.format("%04x", h)) .. '%u' .. string.upper(string.format("%04x", l))
end

--把字符中的多字节字符转成unicode codepoint（目前的使用场景是处理url传递参数中的特殊字符）
function string.encodeunicode(str)
	-- 计算字符串宽度
	-- 可以计算出字符宽度，用于显示使用
	local len = #str
	local newStr = ""
	local i = 1
	while (i <= len) do
		local curByte = string.byte(str, i)
		local byteCount = 1
		if curByte > 0 and curByte <= 127 then
			byteCount = 1 --1字节字符
		elseif curByte >= 192 and curByte < 223 then
			byteCount = 2 --双字节字符
		elseif curByte >= 224 and curByte < 239 then
			byteCount = 3 --汉字
		elseif curByte >= 240 and curByte <= 247 then
			byteCount = 4 --4字节字符
		end
		
		local char = string.sub(str, i, i + byteCount - 1)
		i = i + byteCount

		if byteCount > 1 then
			newStr = newStr .. string.charunicode(char)
		else
			newStr = newStr .. char
		end
	end
	return newStr
end

local function urlencodechar(char)
    return "%" .. string.format("%02X", string.byte(char))
end

function string.urlencode(input)
    -- convert line endings
    input = string.gsub(tostring(input), "\n", "\r\n")
    -- escape all characters but alphanumeric, '.' and '-'
    input = string.gsub(input, "([^%w%.%- ])", urlencodechar)
    -- convert spaces to "+" symbols
    return string.gsub(input, " ", "+")
end

function string.urldecode(input)
    input = string.gsub(input, "+", " ")
    input =
        string.gsub(
        input,
        "%%(%x%x)",
        function(h)
            return string.char(checknumber(h, 16))
        end
    )
    input = string.gsub(input, "\r\n", "\n")
    return input
end

function string.utf8len(input)
    local len = string.len(input)
    local left = len
    local cnt = 0
    local arr = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
    while left ~= 0 do
        local tmp = string.byte(input, -left)
        local i = #arr
        while arr[i] do
            if tmp >= arr[i] then
                left = left - i
                break
            end
            i = i - 1
        end
        cnt = cnt + 1
    end
    return cnt
end

function string.utf8sub(input, i, j)
    if i == 0 then i = 1 end
    if j == nil then j = -1 end
    if i == nil or j == 0 or (i * j > 0 and i > j) then
        return "" -- string.sub()也是不支持j为0的
    end

    local lenAccArr = {}
    lenAccArr[0] = 0
    local len = string.len(input)
    local left = len
    local cnt = 0
    local arr = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
    while left ~= 0 do
        local tmp = string.byte(input, -left)
        local k = #arr
        while arr[k] do
            if tmp >= arr[k] then
                cnt = cnt + 1
                lenAccArr[cnt] = lenAccArr[cnt - 1] + k
                left = left - k
                break
            end
            k = k - 1
        end
    end
    
    if i < 0 then i = math.max(1, cnt + i + 1) end
    if j < 0 then j = math.max(1, cnt + j + 1) end
    if i > j then
        return ""
    end

    return string.sub(input, lenAccArr[i - 1] + 1, lenAccArr[j])
end

-- 用于在主串ss的第inPos位插入字符串s
function string.insertSub(ss, s, inPos)
    local function utf8ByteCnt(char)
        local arr = {0, 0xc0, 0xe0, 0xf0, 0xf8, 0xfc}
        for i = #arr, 1, -1 do
            if char >= arr[i] then
                return i
            end
        end
    end
    local curIndex = 1
    local curPos = 1
    while curPos < inPos do
        local char = string.byte(ss, curIndex)
        curIndex = curIndex + utf8ByteCnt(char)
        curPos = curPos + 1
    end
    return ss:sub(1, curIndex-1)..s..ss:sub(curIndex)
end

function string.formatnumberthousands(num)
    local formatted = tostring(checknumber(num))
    local k
    while true do
        formatted, k = string.gsub(formatted, "^(-?%d+)(%d%d%d)", "%1,%2")
        if k == 0 then
            break
        end
    end
    return formatted
end

function string.isempty(str)
    if str == nil then
        return true
    end
    local s = tostring(str)
    if s == nil then
        return true
    end
    if s == "" then
        return true
    end

    return false
end

function string.starts_with(str, start)
    return str:sub(1, #start) == start
end

function string.ends_with(str, ending)
    return ending == "" or str:sub(-(#ending)) == ending
end

function string.sub_start(str, start)
    if string.starts_with(str, start) then
        local startIdx = #start + 1
        local endIdx = #str
        return str:sub(startIdx, endIdx)
    end
end

function string.instead(s, num, c)
    local pre = string.sub(s, 1, num - 1)
    local suf = string.sub(s, num + 1, -1)
    return pre .. c .. suf
end

local oldformat = string.format
function string.format(fmt, ...)
    if type(fmt) == "string" then
        return oldformat(fmt, ...)
    end
    return loc_format(fmt, ...)
end

local oldlen = string.len
function string.len(s)
    if type(s) == "userdata" then
        s = tostring(s)
    end
    return oldlen(s)
end

local oldmatch = string.match
function string.match(s, pattern, init)
    if type(s) == "userdata" then
        s = tostring(s)
    end
    return oldmatch(s, pattern, init)
end

local oldsub = string.sub
function string.sub(s, i, j)
    if type(s) == "userdata" then
        s = tostring(s)
    end
    return oldsub(s, i, j)
end

local oldgmatch = string.gmatch
function string.gmatch(s, pattern, init)
    if type(s) == "userdata" then
        s = tostring(s)
    end
    return oldgmatch(s, pattern, init)
end

local oldgsub = string.gsub
function string.gsub(s, pattern, repl, n)
    if type(s) == "userdata" then
        s = tostring(s)
    end
    return oldgsub(s, pattern, repl, n)
end

local oldfind = string.find
function string.find(s, pattern, init, plain)
    if type(s) == 'userdata' then
        s = tostring(s)
    end
    return oldfind(s, pattern, init, plain)
end

local oldconcat = table.concat
function table.concat(list, sep, i, j)
    if type(list) ~= 'table' then
        return ''
    end

    if #list == 0 then
        return ''
    end

    for k, v in pairs(list) do
        if type(v) == 'userdata' then
            list[k] = tostring(v)
        end
    end

    if type(sep) == 'userdata' then
        sep = tostring(sep)
    end

    return oldconcat(list, sep, i, j)
end

---------------------------------------------------------------------------------
--- file extends
---------------------------------------------------------------------------------
function isexist_ori(path)
    local function func()
        require(path)
    end
    return xpcall(func, function()
        path = {}
    end)
end

-- is required file return a table 
function istable(path)
    if isexist(path) then
        local t = require(path)
        return type(t) == 'table'        
    end
    return false
end

---------------------------------------------------------------------------------
--- object global funcs
---------------------------------------------------------------------------------
local _cppinstance_str = "__cppinst"
local funcstr = "function"
function lua_getfield(t, k)
    local v = rawget(t, k)
    if v then return v end

    local class = getmetatable(t)
    while class do
        -- loginfo("cppindex", t, class, k)
        v = rawget(class, k)
        if v then
            if type(v) == funcstr then
                rawset(t, k, v)
            end
            return v
        end
        local meta = getmetatable(class)
        if meta ~= class then
            class = meta
        else
            loginfo("Error meta here", class, meta, t._traceback)
            return nil
        end
    end
    return v
end

-- 是否存在某个luafunction
function isexistfunc(t, funcname)
    local class = getmetatable(t)
    while class do
        local v = rawget(class, funcname)
        if v then
            if type(v) == "function" then
                return true
            end
        end
        class = getmetatable(class)
    end
    return false
end

---------------------------------------------------------------------------------
--- 通用元方法 & 类方法
---------------------------------------------------------------------------------
function addfunc(des_t, src_t)
    for k, v in pairs(src_t) do
        if type(v) == "function" and k ~= "Ctor" and k ~= "Destroy" and not rawget(des_t, k) then
            des_t[k] = v
        end
    end
end

function __gcForTable(t)
    if t._has_destroy_ or rawget(t, "__ismeta") then
        return
    else
        local function f()
            t:Release()
        end
        trycall(f)
    end
end

function get_ctor_list(cls)
    if not cls then
        return {}
    end
    if cls._ctor_list then
        return cls._ctor_list
    end

    local ctorList = {}
    local super = cls:Super()
    local superCtorList = get_ctor_list(super)
    for _, ctor in ipairs(superCtorList) do
        table.insert(ctorList, ctor)
    end
    
    local ctor = cls.Ctor
    if ctor then
        table.insert(ctorList, ctor)
    end
    cls._ctor_list = ctorList

    return ctorList
end

-- 之前是直接存到class上，如果GC发生在class迭代器调用的时候，可能导致迭代器调用异常
-- 现在把它转存出来
gMapCls2DestroyFuncs = {}
gMapCls2ReleasePools = {}

function get_destroy_list(cls)
    if not cls then
        return {}
    end
    if gMapCls2DestroyFuncs[cls] then
        return gMapCls2DestroyFuncs[cls]
    end

    local destroyList = {}
    local super = cls:Super()
    local superDestroyList = get_destroy_list(super)
    for _, destroy in ipairs(superDestroyList) do
        table.insert(destroyList, destroy)
    end
    
    local destroy = cls.Destroy
    if destroy then
        table.insert(destroyList, destroy)
    end
    gMapCls2DestroyFuncs[cls] = destroyList

    return destroyList
end

function get_releasepool_list(cls)
    if not cls then
        return {}
    end
    if gMapCls2ReleasePools[cls] then
        return gMapCls2ReleasePools[cls]
    end

    local releasepoolList = {}
    local super = cls:Super()
    local superDestroyList = get_releasepool_list(super)
    for _, destroy in ipairs(superDestroyList) do
        table.insert(releasepoolList, destroy)
    end
    
    local releasepool = cls.ReleasePool
    if releasepool then
        table.insert(releasepoolList, releasepool)
    end
    gMapCls2ReleasePools[cls] = releasepoolList

    return releasepoolList
end

-- 每次生成实例时，会调用这个函数，负责从父类到子类的顺序，依次调用Ctor函数
local ctorStr = "Ctor"
function ctor_recursively(theclass, ins, ...)
    if theclass == nil then
        return
    end
    local super = theclass:Super()
    if super then
        ctor_recursively(super, ins, ...)
    end
    local Ctor = rawget(theclass, ctorStr)
    if Ctor then
        Ctor(ins, ...)
    end
end

-- 每次release实例时，会调用这个函数，负责从子类到父类的顺序，依次调用Destroy函数
local destroyStr = "Destroy"
function intern_destroy(theclass, ins, ...)
    if theclass == nil then
        return
    end
    local Destroy = rawget(theclass, destroyStr)
    if Destroy then
        Destroy(ins, ...)
    end
    local super = theclass.Super and theclass:Super()
    if super then
        intern_destroy(super, ins, ...)
    end
end

---------------------------------------------------------------------------------
--- Class: __index & __newindex
---------------------------------------------------------------------------------
-- local __cache_not_cpp_property_name = {}
-- index函数，先找lua类里，再找c++类里的
local __newCppIndex = __newCppIndex
local getObjClass = _GetObjClass
function __indexcpp(t, k)
    local v
    local class = getmetatable(t)
    while class do
        v = rawget(class, k)
        if v then
            if type(v) == "function" then
                rawset(t, k, v)
            end
            return v
        end
        local meta = getmetatable(class)
        if meta ~= class then
            class = meta
        else
            print("Error meta here", class, meta, t._traceback)
            return nil
        end
    end

    local LuaPropertyNotFoundInCpp = rawget(t, 2)
    if LuaPropertyNotFoundInCpp and LuaPropertyNotFoundInCpp[k] then
        return nil
    end

    local cppinst = rawget(t, _cppinstance_str)
    if isvalid(cppinst) then
        local retType
        v, retType = __newCppIndex(cppinst, k)
        if retType == 3 or retType == 4 then    -- ufunction or extension function
            rawset(t, k, v)
        elseif retType == 0 then    -- none
            LuaPropertyNotFoundInCpp[k] = true
        end
    end
    return v
end

-- __index meta function with optimization in "pseudo" code
-- The real implementation will in c++

--- 判断是方法，还是一个成员的getter
local function bIsFunction(value)

end

--- 获得一个表的Cpp缓存表
local function getClsCache(t)
    return rawget(t, "_cppClsCache")
end

--- 一个pushNil方法
local function pushNil()
    return nil
end

--- 添加缓存到Cpp缓存表
local function add2ClsCache(t, k, ret)
    local cppClsCache = getClsCache(t)
    if ret then
        cppClsCache[t] = ret
    else
        cppClsCache[t] = pushNil
    end
end

function __optimizedIndexCpp(t, k)
    local ret

    -- Step 1: get from luabasecls
    local luacls = rawget(t, "_meta_")  -- faster than getmetatable
    ret = rawget(luacls, k)
    if ret then
        if type(ret) == "function" then
            rawset(t, k, ret)
        end
        return ret
    end

    -- Step 2: get from cpp class cache
    local cppClsCache = getClsCache(t)
    if cppClsCache then
        ret = rawget(cppClsCache, k)
        if ret then
            if bIsFunction(ret) then
                rawset(t, k, ret)
            else
                -- call getter to get the real value
                ret = ret(t)
            end

            return ret
        end
    end

    -- Step 3: get from cppinstance and add to cache
    local cppinst = rawget(t, _cppinstance_str)
    if cppinst then
        local ret = __newCppIndex(cppinst, k)
        add2ClsCache(ret)
        if bIsFunction(ret) then
            rawset(t, k, ret)
        else
            -- call getter to get the real value
            ret = ret(t)
        end

        return ret
    end
end
---------------------------------------------------------------------------------
--- NodeClass: _index & __newindex (TODO data node)
---------------------------------------------------------------------------------
function __nodeindex(t, k)
	local v
	local class = getmetatable(t)
	while class do
		v = rawget(class, k)
		if v then
			if type(v) == GlobalStr.FUNCTION then
				rawset(t, k, v)
			end
			return v
		end
		local meta = getmetatable(class)
		if meta ~= class then
			class = meta
		else
			print("Error meta here", class, meta, t._traceback)
			return nil
		end
	end
	return v
end

function __nodenewindex(t, k, v)
	rawset(t, k, v)
end