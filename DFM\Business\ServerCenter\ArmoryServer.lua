----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaSArmory)
----- LOG FUNCTION AUTO GENERATE END -----------

local ArmoryItemData    = require("DFM.Business.DataStruct.ArmoryStruct.ArmoryItemData")
local Log               = require("DFM.Business.DataStruct.Common.Base.Log")
local Filter            = require("DFM.Business.DataStruct.Common.Base.Filter")
local Table             = require("DFM.Business.DataStruct.Common.Base.Table")

local EGameFlowStageType = import "EGameFlowStageType"
local logctx = Log.CreateLogContext("[Armory][ArmoryServer]", LogUtil.LogInfo, LogUtil.LogWarning, LogUtil.LogError, LogUtil.LogFatal)

local ArmoryItemState   = ArmoryItemData.ArmoryItemState

---@class ArmoryServer:ServerBase
local ArmoryServer = class("ArmoryServer", require("DFM.YxFramework.Managers.Server.ServerBase"))

local function EncodeArmoryLastStageAndUnlockState(taskStage, unlockState)
    return taskStage + (unlockState and 100 or 0)
end

local function DecodeArmoryLastStageAndUnlockState(encoded)
    local taskStage = encoded % 100
    local unlockState = encoded >= 100
    ---@class ArmoryLastStateInfo
    local result = {
        lastShownStage = taskStage,
        lastUnlockState = unlockState,
    }
    return result
end

function ArmoryServer:Ctor()
    logctx:Info("[Server Life Cycle] Ctor")
    self.armoryItems = {} ---@type ArmoryItemData[]
    self.idMap = {} ---@type table<integer, integer> 从itemID到relatedID的映射，用于展示奖励弹窗时倒查实际物品id
    self.bRedDot = false

    self.Events = {
        evtArmoryUpdateRedDotState = LuaEvent:NewIns("evtArmoryUpdateRedDotState"), -- evtArmoryUpdateRedDotState(bRedDot)
    }
end

---@return ArmoryItemData?
function ArmoryServer:TryFindItemByGoalID(goalID)
    local found = {}
    for _, item in pairs(self.armoryItems) do
        for _, task in pairs(item.unlockTasks) do
            if task and task.goalID == goalID then
                table.insert(found, item)
            end
        end
    end

    if #found == 0 then return nil end

    local Sort = require "DFM.Business.DataStruct.Common.Base.Sort"
    Sort.MergeSort(found,
        Sort.Preferenced({
            -- 优先返回正激活的项目
            Sort.MatchCriteria(function(item) return item.bActivated end),
            -- 然后是队列最靠前的项目
            Sort.ByField("queueOrder")
        })
    )

    return found[1]
end

---@return ArmoryItemData[]
function ArmoryServer:GetArmoryItems()
    return self.armoryItems
end

function ArmoryServer:OnInitServer()
end

function ArmoryServer:OnGameFlowChangeEnter(gameFlowType)
end

function ArmoryServer:OnLoadingLogin2Frontend(gameFlowType)
    self:FetchServerData()
end

function ArmoryServer:OnLoadingFrontend2Game()
    self:FetchServerData()
end

function ArmoryServer:OnLoadingGame2Frontend()
    self:FetchServerData()
end

function ArmoryServer:FetchServerData(callback)

    local rawLastStateInfo = Server.TipsRecordServer:GetMap(Server.TipsRecordServer.keys.ArmoryLastStateShown)
    local lastStateInfo = {}
    for armoryId, lastStateCode in pairs(rawLastStateInfo) do
        lastStateInfo[armoryId] = DecodeArmoryLastStageAndUnlockState(lastStateCode)
    end

    local onRes = function(res)
        logctx:Info("[Net] CSArmoryGetPlayerDataRes")

        if self:IntegrityCheck_CSArmoryGetPlayerDataRes(res) then
            self:SaveResponseData(res, lastStateInfo)
        else
            logctx:Info("[Failsafe] CSArmoryGetPlayerDataRes - Server response invalid, no data saved.")
        end

        if callback then
            callback()
        end
    end

    local req = pb.CSArmoryGetPlayerDataReq:New()
    req:Request(onRes, {bEnableHighFrequency = true})
end

---@param armoryIdList integer[]
function ArmoryServer:UpdateLastShownState(armoryIdList)
    local updateSet = Table.Exchange(armoryIdList)

    local map = {}
    for _, item in pairs(self.armoryItems) do
        if updateSet[item.itemID] then
            item.lastShownTaskStage = item:GetCurrentStageAndTask()
            item.lastShownUnlockState = item:GetState() >= ArmoryItemState.Completed
        end
        map[tostring(item.itemID)] = EncodeArmoryLastStageAndUnlockState(item.lastShownTaskStage, item.bUnlocked)
    end
    Server.TipsRecordServer:SetMap(Server.TipsRecordServer.keys.ArmoryLastStateShown, map)

    self:UpdateRedDotState()
end

function ArmoryServer:RequestActivateArmoryItem(armoryItemID, callback)
    local function OnRes(res)
        if res.result ~= 0 then
            LogUtil.LogError("[Armory][ArmoryServer] failed to activate armory item.")
            return
        end
        self:FetchServerData(callback)
    end

    local req = pb.CSPlayerAcceptArmoryTaskReq:New()
    req.armory_id = armoryItemID
    req:Request(OnRes, {bEnableHighFrequency = true})
end

function ArmoryServer:RequestClaimItem(armoryItemID, callback)
    local function OnRes(res)
        if res.result ~= 0 then
            LogUtil.LogError("[Armory][ArmoryServer] failed to claim armory reward.")
            return
        end

        self:FetchServerData(callback)
    end

    local req = pb.CSPlayerArmoryTakeRewardReq:New()
    req.armory_id = armoryItemID
    req:Request(OnRes, {bEnableHighFrequency = true})
end

function ArmoryServer:RequestEnqueueArmoryItem(armoryItemID, callback)
    local function OnRes(res)
        if res.result ~= 0 then
            LogUtil.LogError("[Armory][ArmoryServer] failed to enqueue armory item.")
            return
        end
        self:FetchServerData(callback)
    end

    local req = pb.CSPlayerArmoryAddQueueReq:New()
    req.armory_id = armoryItemID
    req:Request(OnRes, {bEnableHighFrequency = true})
end

-- 调试开关：不检查服务器下发数据合法性
-- __armory_debug_nointegrity = true
---@param res pbCSArmoryGetPlayerDataRes
function ArmoryServer:IntegrityCheck_CSArmoryGetPlayerDataRes(res)

    local bValid = true

    -- Check result code
    if res.result ~= 0 then
        bValid = false
        logctx:Error("[Integrity][CSArmoryGetPlayerDataRes][Failed] result=", res.result)
        return false
    end

    -- return bValid
    return true
end


-- 调试开关：强制所有已解锁的物品都设置为 [已解锁且需要播放解锁动画] 状态
-- __armory_debug2 = true

---@param res               pb_CSArmoryGetPlayerDataRes
---@param lastStateInfo     table<integer, ArmoryLastStateInfo>
function ArmoryServer:SaveResponseData(res, lastStateInfo)
    -- local pendingAnimationCheck = table.exchangekv(res.red_dot_item_ids)
    local queueOrderByArmoryID = table.exchangekv(res.wait_active_armory_ids)

    -- 反注册所有军械库道具获取途径信息
    Server.ItemUnlockPathServer:RemoveItemUnlockPathsByGroup(self.serverName)

    ---@param data pb_PlayerArmoryData
    local function PBArmoryItemToClientRepresentation(idx, data)
        local result = ArmoryItemData.Create()
        
        result.itemID = data.item_id
        result.seasonID = data.season_id
        result.actualItemID = data.related_id
        result.itemType = data.item_type
        result.onShelfTime = data.on_shelf_time
        result.offShelfTime = data.off_shelf_time
        result.bActivated = false --TODO
        result.queueOrder = queueOrderByArmoryID[result.itemID] or 0
        result.unlockTime = data.finish_stamp

        local mapkey = tostring(data.item_id)
        if lastStateInfo[mapkey] then
            result.lastShownTaskStage = lastStateInfo[mapkey].lastShownStage
            result.lastShownUnlockState = lastStateInfo[mapkey].lastUnlockState
        else
            result.lastShownTaskStage = 1
            result.lastShownUnlockState = false
        end

        if __armory_debug3 then
            result.onShelfTime = 0
            result.offShelfTime = Facade.ClockManager:GetLocalTimestamp() + 100000
        end

        -- TODO result.seasonID = ???
        result.shouldPlayUnlockAnim = false

        result.unlockTasks = {}
        for stageIdx, goalInfo in ipairs(data.armory_objective) do
            local goalConfig = Facade.TableManager:GetRowByKey("ActivityTaskGoals", goalInfo.task_id)
            assert(goalConfig, string.format("军械库：请策划检查活动目标表 id = %d 配置是否缺失", goalInfo.task_id))

            local taskStruct = {
                taskDesc = goalConfig.Remark,
                taskGoal = goalConfig.RequiredCount,
                taskProgress = data.armory_objective[stageIdx].value,
                goalID = data.armory_objective[stageIdx].task_id,
            }
            result.unlockTasks[stageIdx] = taskStruct
        end

        result.bUnlocked = (data.state == 1)
        result.bActivated = (data.state == 0)

        -- 处于未解锁且上架状态，注册该道具的获取途径
        if (not result.bCompleted) and result:IsOnShelf() then
            logctx:Info("Register unlock path for item ", result.itemID)
            Server.ItemUnlockPathServer:AddItemUnlockPathInfo(self.serverName, EArmedForceMode.MP, EItemUnlockSources.Armory, result.itemID, 21400000, {result.itemID})
        end

        -- 因为第一次拉取数据应该是在登录，而且之后大概率军械库里的物品不会变，所以Get一下，把itemBase结构都创建到缓存里
        -- 每次打开界面的时候可以省下对每个物品创建itemBase的时间
        result:GetItemBase()

        return {{result}}
    end

    local function PBArmoryItemToClientRepresentation_Protected(idx, data)
        local success, result = pcall(PBArmoryItemToClientRepresentation, idx, data)
        if success then return result end
        
        LogUtil.LogError("[Armory][ArmoryServer]: "..result)
        return {}
    end

    self.armoryItems = Table.Transform(res.armory_data, PBArmoryItemToClientRepresentation_Protected)
    self.idMap = Table.Transform(res.armory_data, function(_, item) return {{item.item_id, item.related_id}} end)

    self:UpdateRedDotState()
end

function ArmoryServer:UpdateRedDotState()
    self.bRedDot = Filter.AnyMatch(self.armoryItems,
        ---@param item ArmoryItemData
        function(item)
            return item:GetState() == ArmoryItemData.ArmoryItemState.Completed
        end
    )
    self.Events.evtArmoryUpdateRedDotState:Invoke(self.bRedDot)
end

function ArmoryServer:CheckRedDotState()
    return self.bRedDot
end

-- hint only
if false then
    Server.ArmoryServer = nil ---@type ArmoryServer
end

return ArmoryServer
