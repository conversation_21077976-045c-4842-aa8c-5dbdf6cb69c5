----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMGamelet)
----- LOG FUNCTION AUTO GENERATE END -----------

local GameletBase = ui("GameletBase")

local GameletLogic = require "DFM.Business.Module.GameletModule.Logic.GameletLogic"
local FAnchors = import "Anchors"
local UGPInputDelegates = import "GPInputDelegates"
local EGPInputType = import("EGPInputType")

function GameletBase:Ctor()
    self._wtAppPageRoot = self:Wnd("DFCanvasPanel_40", UIWidgetBase)
    self.appId = nil
    self.anchor = nil
    self.offset = nil
    
    -- 可选参数
    self.sceneType = 0  -- 场景类型
end

function GameletBase:OnInitExtraData(args)
    if self.appId ~= nil and self.appId ~= args.iAppID then
        self:CloseApp()
        self:InitExtraDataInternal(args)
        self:OpenApp()
    else
        self:InitExtraDataInternal(args)
    end
end

function GameletBase:OnOpen()
    Module.Gamelet.Config.evtPandoraShowAppPage:AddListener(self.OpenPage,self)
    Module.Gamelet.Config.evtPandoraHideAppPage:AddListener(self.HidePage,self)
    Module.Gamelet.Config.evtPandoraShowItemScene:AddListener(self.ShowItemScene,self)

    if not self.OnEventGamepadChangeDetectedHandle then
        self.OnEventGamepadChangeDetectedHandle = UGPInputDelegates.Get(GetGameInstance()).OnGamepadChangeDetected:Add(CreateCPlusCallBack(self.OnEventGamepadChangeDetected, self))
    end
    if not self.OnEventInputTypeChangedHandle then
        self.OnEventInputTypeChangedHandle = UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Add(CreateCPlusCallBack(self.OnEventInputTypeChanged, self))
    end
    
    self:OpenApp()
end

function GameletBase:OnShow()
    GameletLogic.SendAppShow(self.appId)
    GameletLogic.SendPandoraGameEvent(self.appId, "Show")
end

function GameletBase:OnHide()
    GameletLogic.SendPandoraGameEvent(self.appId, "Hide")
    Module.ItemDetail:CloseAllPopUI()
end

function GameletBase:OnClose()
    Module.Gamelet.Config.evtPandoraShowAppPage:RemoveListener(self.OpenPage,self)
    Module.Gamelet.Config.evtPandoraHideAppPage:RemoveListener(self.HidePage,self)
    Module.Gamelet.Config.evtPandoraShowItemScene:RemoveListener(self.ShowItemScene,self)
    
    if self.OnEventGamepadChangeDetectedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnGamepadChangeDetected:Remove(self.OnEventGamepadChangeDetectedHandle)
        self.OnEventGamepadChangeDetectedHandle=nil
    end

    if self.OnEventInputTypeChangedHandle then
        UGPInputDelegates.Get(GetGameInstance()).OnInputTypeChanged:Remove(self.OnEventInputTypeChangedHandle)
        self.OnEventInputTypeChangedHandle = nil
    end
    
    self:CloseApp()
end

function GameletBase:OpenPage(widget,appInfo)
end

function GameletBase:HidePage(widget,appInfo)
end

function GameletBase:OnEventGamepadChangeDetected(GamepadInputType)
    GameletLogic.SendInputTypeChangeEvent(self.appId)
end

function GameletBase:OnEventInputTypeChanged(InputType)
    GameletLogic.SendInputTypeChangeEvent(self.appId)
end

function GameletBase:ShowItemScene(appID, itemID, type, extraParam)
    if appID ~= self.appId then
        return
    end
    
    if type ~= self.sceneType then
        return
    end
    
    GameletLogic.ShowReward(self, itemID, type, extraParam)
end

function GameletBase:SetWidgetOffset(widget, appInfo, zOrder)
    local app_info = appInfo
    widget.slot:SetZOrder(zOrder)

    loginfo("GameletBase SetWidgetOffset app", app_info.belongToApp, "page", appInfo.appPage)

    local anchorJson = app_info.windowConfig.anchor
    if anchorJson then
        self.anchor = anchorJson
    end

    local offsetJson = app_info.windowConfig.offset
    if offsetJson then
        self.offset = offsetJson
    end

    if self.anchor then
        local anchor = FAnchors()
        anchor.Minimum = FVector2D(self.anchor.minX, self.anchor.minY)
        anchor.Maximum = FVector2D(self.anchor.maxX, self.anchor.maxY)
        widget.Slot:SetAnchors(anchor)
    else
        local anchor = FAnchors()
        anchor.Minimum = FVector2D(0, 0)
        anchor.Maximum = FVector2D(1, 1)
        widget.Slot:SetAnchors(anchor)
    end

    if self.offset then
        widget.Slot:SetOffsets(FMargin(self.offset.left, self.offset.top, self.offset.right, self.offset.bottom))
    else
        widget.Slot:SetOffsets(FMargin(0, 0, 0, 0))
    end
end

function GameletBase:OpenApp()
end

function GameletBase:CloseApp()
    GameletLogic.SendPandoraGameEvent(self.appId, "Close")

    self.appId = nil
end

function GameletBase:InitExtraDataInternal(args)
    self.appId = args.iAppID
end

return GameletBase
