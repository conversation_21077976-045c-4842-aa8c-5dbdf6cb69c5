----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMSystemSetting)
----- LOG FUNCTION AUTO GENERATE END -----------

---@class SystemSettingHDMainView

local SystemSettingHDMainView = ui("SystemSettingHDMainView")
local SystemSettingLogic = require "DFM.Business.Module.SystemSettingModule.Logic.SystemSettingLogic"
local SettingConfig = require "DFM.Business.Module.SystemSettingModule.SystemSettingConfig"
local CommonSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CommonSettingLogicHD"
local SettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingLogicHD"
local SettingRegLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.SettingRegLogicHD"
local VideoSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.VideoSettingLogicHD"
local UClientVideoSettingHD = import("ClientVideoSettingHD")
local ClientGameSettingHD = import("ClientGameSettingHD").Get(GetGameInstance())
local CloudSettingLogicHD = require "DFM.Business.Module.SystemSettingModule.Logic.HD.CloudSettingLogicHD"
local Config = require "DFM.Business.Module.SystemSettingModule.SystemSettingConfig"
local SystemUtil = require "DFM.YxFramework.Managers.UI.Util.UISystemUtil"
local UDFHDKeySettingManager = import "DFHDKeySettingManager"

local ESystemSettingHDPanel = Config.ESystemSettingHDPanel
local ESystemSettingHDInputPanel = Config.ESystemSettingHDInputPanel
local UDFMGameGPM = import "DFMGameGPM"
local EGPInputType = import("EGPInputType")

--BEGIN MODIFICATION @VIRTUOS
local ESystemSettingHDInputPanel_Gamepad = Config.ESystemSettingHDInputPanel_Gamepad
local UDFMGameplayInputManager = import("DFMGameplayInputManager")
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
--END MODIFICATION
local LogAnalysisTool = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.LogAnalysisTool"
local ButtonIdConfig  = require "DFM.StandaloneLua.BusinessTool.LogAnalysis.ButtonIdConfig"
local ButtonIdEnum = ButtonIdConfig.Enum

-- 玩家修改选项后到显示上传状态之间等待的秒数，等待期间都会重新计时
local cloudSettingUploadStateDisplayDelayTime = 5
local cloudSettingUploadStateAutoDisappearTime = 1
local _cloudSettingCommand = {
    Init = 1,
    ChangeSetting = 2,
    Uninit = 3,
}
local _cloudSaveState = {
    None = 1, -- 不显示
    Uploading = 2, -- 上传中
    UploadSuccess = 3,
    UploadFail = 4,
}

local _uiIndexCount = 0
local function _AutoUIIndex()
    _uiIndexCount = _uiIndexCount + 1
    return _uiIndexCount
end
local _tabIndexCount = 0
local function _AutoTabIndex()
    _tabIndexCount = _tabIndexCount + 1
    return _tabIndexCount
end
local _subTabIndexCount = 0
local function _AutoSubTabIndex()
    _subTabIndexCount = _subTabIndexCount + 1
    return _subTabIndexCount
end

function SystemSettingHDMainView:Ctor()
    self:_BindWidget()
    self:_BindBtnEvent()
    if DFCONSOLE_LUA ~= 1 then
        local globalLst = {
            {actionName = "SettingManage", func = self._OnClickSettingManageBtn, caller = self},
            {actionName = "SettingManage_Gamepad", func = self._OnClickSettingManageBtn, caller = self, bUIOnly = true},
        }
        Module.SystemSetting.Field:SetGlobalSummaryList(globalLst)
    end
end

function SystemSettingHDMainView:_OnClickSettingManageBtn()
    Facade.UIManager:AsyncShowUI(UIName2ID.SystemSettingHDCloudSettingWindow)
end

function SystemSettingHDMainView:_BindWidget()
    -- 主面板
    self._wtRootMain = self:Wnd("_wtRootMain", UIWidgetBase)

    -- BEGIN MODIFICATION @ VIRTUOS : 显示LI Pass绑定账号UI，需要全屏UI作为根组件
    self._wtCanvasPanel = self:Wnd("CanvasPanel_0", UIWidgetBase)
    -- END MODIFICATION

    --self._wtCreditBtn = self:Wnd("_wtCreditBtn", UIButton)
end

function SystemSettingHDMainView:_BindBtnEvent()
    -- if  IsBuildRegionCN() then
    --     self._wtCreditBtn:SetVisibility(ESlateVisibility.Visible)
    --     self._wtCreditBtn:Event("OnClicked", self._OnClickBtnCredit, self)
    -- else
    --     self._wtCreditBtn:SetVisibility(ESlateVisibility.Collapsed)
    -- end
end

function SystemSettingHDMainView:_BindSwitchSubUI()
    Facade.UIManager:UnRegSwitchSubUI(self)

    self._wtCloudSettingState = self:Wnd("CloudSave", UIWidgetBase)
    self._wtCloudSettingStateText= self:Wnd("DFTextBlock_149", UITextBlock)
    -- 0 uploading, 1 success, 2 fail
    self._wtUploadStateSwitcher = self:Wnd("DFWidgetSwitcher_1", UIWidgetSwitcher)

    self.uiType2subUIIndexMap = {}
    self.tabIndex2uiTypeMap = {}
    self.subTabUiType2subTabIndexMap = {}
    self.subTabIndex2subTabUiTypeMap = {}
    self.tabTxtTbl = {}
    self.subTabTbl = {}
    self._systemSettingHDPanelUI = {}

    _uiIndexCount = 0
    _tabIndexCount = 0
    -- 添加Gameplay
    self.uiType2subUIIndexMap[ESystemSettingHDPanel.GamePlaySetting] = _AutoUIIndex()
    self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.GamePlaySetting
    table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.GamePlaySettingTxt)
    table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDGamePlayPanel)

    -- BEGIN MODIFICATION @ VIRTUOS : Not keyboard setting at console platform
    if DFCONSOLE_LUA ~= 1 then
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting] = {}
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting] = {}
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting] = {}
        self.inputSubTbl = {}
        self.inputSubTblImgList = {}
        -- 添加Input
        self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.InputSetting
        table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.InputSettingTxt)
        _subTabIndexCount = 0
        -- 子标签InputSensitivity
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.Sensitivity] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.Sensitivity] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.Sensitivity]] = ESystemSettingHDInputPanel.Sensitivity
        table.insert(self.inputSubTbl, SettingConfig.Loc.HDSettingTab.InputSettingSensitivityTxt)
        table.insert(self.inputSubTblImgList, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0116.Common_Tips_Icon_0116'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputSensitivityPanel)
        -- 子标签CCC
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.CCC] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.CCC] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.CCC]] = ESystemSettingHDInputPanel.CCC
        table.insert(self.inputSubTbl, SettingConfig.Loc.HDSettingTab.InputSetting3CTxt)
        table.insert(self.inputSubTblImgList, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0117.Common_Tips_Icon_0117'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel)
        -- 子标签UI
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.UI] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.UI] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.UI]] = ESystemSettingHDInputPanel.UI
        table.insert(self.inputSubTbl, SettingConfig.Loc.HDSettingTab.InputSettingUITxt)
        table.insert(self.inputSubTblImgList, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0112.Common_Tips_Icon_0112'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel)
        -- 子标签Vehicle
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.Vehicle] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.Vehicle] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.Vehicle]] = ESystemSettingHDInputPanel.Vehicle
        table.insert(self.inputSubTbl, SettingConfig.Loc.HDSettingTab.InputSettingVehicleTxt)
        table.insert(self.inputSubTblImgList, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0118.Common_Tips_Icon_0118'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel)
        -- GM
        if not VersionUtil.IsShipping() then
            self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.GM] = _AutoUIIndex()
            self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.GM] = _AutoSubTabIndex()
            self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting][ESystemSettingHDInputPanel.GM]] = ESystemSettingHDInputPanel.GM
            table.insert(self.inputSubTbl, "GM")
            table.insert(self.inputSubTblImgList, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0105.Common_Tips_Icon_0105'")
            table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel)
        end
        self.subTabTbl[_tabIndexCount] = {
            tabTxtList = self.inputSubTbl,
            fCallbackIns = SafeCallBack(self._OnClickSubTab, self),
            defalutIdx = self._defaultSelectedInputTabType,
            imgPathList = self.inputSubTblImgList
        }
    end
    -- END MODIFICATION
    -- 添加Gamepad
    if WidgetUtil.IsGamepadEnable() then
        self.inputSubTbl_Gamepad = {}
        self.inputSubTblImgList_Gamepad = {}
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad] = {}
        self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.InputSetting_Gamepad
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad] = {}
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting_Gamepad] = {}
        table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.InputSettingTxt_Gamepad)
        _subTabIndexCount = 0
        -- 子标签InputSensitivity
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Sensitivity] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Sensitivity] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Sensitivity] = self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Sensitivity]
        table.insert(self.inputSubTbl_Gamepad, SettingConfig.Loc.HDSettingTab.InputSettingGamepadSensitivityTxt)
        table.insert(self.inputSubTblImgList_Gamepad, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0120.Common_Tips_Icon_0120'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputSensitivityPanelGamepad)
        -- 子标签Control
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Control] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Control] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting_Gamepad][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Control]] = ESystemSettingHDInputPanel_Gamepad.Control
        table.insert(self.inputSubTbl_Gamepad, SettingConfig.Loc.HDSettingTab.InputSettingControlTxt_Gamepad)
        table.insert(self.inputSubTblImgList_Gamepad, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0119.Common_Tips_Icon_0119'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputControlPanel_Gamepad)
        -- 子标签CCC
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.CCC] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.CCC] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting_Gamepad][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.CCC]] = ESystemSettingHDInputPanel_Gamepad.CCC
        table.insert(self.inputSubTbl_Gamepad, SettingConfig.Loc.HDSettingTab.InputSetting3CTxt)
        table.insert(self.inputSubTblImgList_Gamepad, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0117.Common_Tips_Icon_0117'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel_Gamepad)
        -- 子标签Aiming
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Aiming] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Aiming] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting_Gamepad][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Aiming]] = ESystemSettingHDInputPanel_Gamepad.Aiming
        table.insert(self.inputSubTbl_Gamepad, SettingConfig.Loc.HDSettingTab.InputSettingAimingTxt)
        table.insert(self.inputSubTblImgList_Gamepad, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0106.Common_Tips_Icon_0106'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel_Gamepad)
        -- 子标签Vehicle
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Vehicle] = _AutoUIIndex()
        self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Vehicle] = _AutoSubTabIndex()
        self.subTabIndex2subTabUiTypeMap[ESystemSettingHDPanel.InputSetting_Gamepad][self.subTabUiType2subTabIndexMap[ESystemSettingHDPanel.InputSetting_Gamepad][ESystemSettingHDInputPanel_Gamepad.Vehicle]] = ESystemSettingHDInputPanel_Gamepad.Vehicle
        table.insert(self.inputSubTbl_Gamepad, SettingConfig.Loc.HDSettingTab.InputSettingVehicleTxt)
        table.insert(self.inputSubTblImgList_Gamepad, "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_Tips_Icon_0118.Common_Tips_Icon_0118'")
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDInputPanel_Gamepad)
        self.subTabTbl[_tabIndexCount] = {
            tabTxtList = self.inputSubTbl_Gamepad,
            fCallbackIns = SafeCallBack(self._OnClickSubTab, self),
            defalutIdx = self._defaultSelectedInputTabType_Gamepad,
            imgPathList = self.inputSubTblImgList_Gamepad
        }
    end
    -- 添加UI
    self.uiType2subUIIndexMap[ESystemSettingHDPanel.UISetting] = _AutoUIIndex()
    self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.UISetting
    table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.UISettingTxt)
    table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDUIPanel)
    -- 添加Video

    self.uiType2subUIIndexMap[ESystemSettingHDPanel.VideoSetting] = _AutoUIIndex()
    self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.VideoSetting
    table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.VideoSettingTxt)
    table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDVideoPanel)

    -- 添加Radio
    self.uiType2subUIIndexMap[ESystemSettingHDPanel.RadioSetting] = _AutoUIIndex()
    self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.RadioSetting
    table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.RadioSettingTxt)
    table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDRadioPanel)
    -- 添加Privacy
    --if not IsBuildRegionCN() then
    if Facade.GameFlowManager:CheckIsInFrontEnd() then
        self.uiType2subUIIndexMap[ESystemSettingHDPanel.PrivacySetting] = _AutoUIIndex()
        self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.PrivacySetting
        table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.PrivacySettingTxt)
        table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDPrivacyPanel)
    end
    --end

    -- BEGIN MODIFICATION @ CloudyLu : NO download center panel at console platform
    if DFCONSOLE_LUA ~= 1 then
        if PLATFORM_WINDOWS and Module.ExpansionPackCoordinator:IsSupportLitePackage() then
            self.uiType2subUIIndexMap[ESystemSettingHDPanel.DownloadCenter] = _AutoUIIndex()
            self.tabIndex2uiTypeMap[_AutoTabIndex()] = ESystemSettingHDPanel.DownloadCenter
            table.insert(self.tabTxtTbl, SettingConfig.Loc.HDSettingTab.DownloadCenterTxt)
            table.insert(self._systemSettingHDPanelUI, UIName2ID.SystemSettingHDDownloadCenterPanel)
        end
    end
    -- END MODIFICATION

    self._defaultSelectedTabType = SettingConfig.ESystemSettingHDPanel.GamePlaySetting
    self._defaultSelectedInputTabType = SettingConfig.ESystemSettingHDInputPanel.Sensitivity
    -- 控制器默认打开的子Tab
    self._defaultSelectedInputTabType_Gamepad = SettingConfig.ESystemSettingHDInputPanel_Gamepad.Sensitivity

    -- 接框架
    Module.CommonBar:RegStackUITopBarTitle(self, SettingConfig.Loc.HDSetting.HDSettingTxt)
    local ETopBarStyleFlag = Module.CommonBar.Config.ETopBarStyleFlag
    Module.CommonBar:RegStackUITopBarStyle(self, ETopBarStyleFlag.Title)
    Module.CommonBar:RegStackUITopBarTabGroupRegInfo(
        self,
        {
            tabTxtList = self.tabTxtTbl,
            fCallbackIns = SafeCallBack(self._OnClickTab, self),
            defalutIdx = self._defaultSelectedTabType,
            tertiaryTabs = self.subTabTbl
        }
    )

    Facade.UIManager:RegSwitchSubUI(self, self._systemSettingHDPanelUI)

    self._bPendingSwitch = false
    local defaultTab = Module.SystemSetting.Field:GetHDDefaultTab()
    loginfo("SystemSettingHDMainView:_BindSwitchSubUI() "..tostring(defaultTab))
    if defaultTab then
        Module.SystemSetting.Field:SetCurrentTabTypeHD(defaultTab)
        Module.SystemSetting.Field:SetHDDefaultTab(nil)
    else
        Module.SystemSetting.Field:SetCurrentTabTypeHD(self._defaultSelectedTabType)
    end
end

function SystemSettingHDMainView:OnOpen()
    Module.SystemSetting.Field:SetRootPanel(self._wtRootMain)
    Module.SystemSetting.Field:SetIsInBHDSetting(false)

    -- BEGIN MODIFICATION @ VIRTUOS : 显示LI Pass绑定账号UI，需要全屏UI作为根组件
    Module.SystemSetting.Field:SetLIUIRoot(self._wtCanvasPanel)
    -- END MODIFICATION

    self:_BindSwitchSubUI()
end

function SystemSettingHDMainView:OnInitExtraData()
    -- loginfo("SystemSettingHDMainView:OnInitExtraData()")
end

function SystemSettingHDMainView:OnShowBegin()
    Module.SystemSetting.Config.Event.evtSettingMainShowBeginHD:Invoke()

    -- BEGIN MODIFICATION @ VIRTUOS : 防止被ModeHall抢掉A键位
    WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.Default, self)
    -- END MODIFICATION
end

function SystemSettingHDMainView:OnShow()
    UDFMGameGPM.BeginExtTag("EXCLUDE_SystemSettingHDMainView")
    UDFMGameGPM.BeginExclude()
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:AddState(UE.GameHUDSate.GHS_EscPanel, true)
    end
    Module.SystemSetting.Field:SetCurrentHero(nil)
    self:_SetCloudSettingCommand(_cloudSettingCommand.Init)

    local DFHDKeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    if isvalid(DFHDKeySettingMgr) then
        DFHDKeySettingMgr:OnEnterSettingPanel()
    end
end

function SystemSettingHDMainView:OnHideBegin()
    -- BEGIN MODIFICATION @ VIRTUOS : 防止被ModeHall抢掉A键位
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    -- END MODIFICATION
end

function SystemSettingHDMainView:OnActivate()
    Facade.UIManager:RegSwitchSubUI(self, self._systemSettingHDPanelUI)
end

function SystemSettingHDMainView:OnDeactivate()
    Facade.UIManager:UnRegSwitchSubUI(self)
end

function SystemSettingHDMainView:OnHide()
    SettingRegLogicHD.FlushPendingSaveObjs()
    UDFMGameGPM.EndExtTag("EXCLUDE_SystemSettingHDMainView")
    UDFMGameGPM.EndExclude()
    local hudStateManager = UE.HUDStateManager.Get(GetWorld())
    if hudStateManager then
        hudStateManager:RemoveState(UE.GameHUDSate.GHS_EscPanel, true)
    end
    Module.SystemSetting:ClientAddTgLog_HDSetting()
    Module.SystemSetting.Config.Event.evtSettingMainHideHD:Invoke()
    Module.SystemSetting.Field:SetCurrentTabTypeHD(self._defaultSelectedTabType)

    self:_SetCloudSettingCommand(_cloudSettingCommand.Uninit)
    self._curSelectedSubTabType = nil
    self._curSelectedTabType = nil

    local DFHDKeySettingMgr = UDFHDKeySettingManager.Get(GetGameInstance())
    if isvalid(DFHDKeySettingMgr) then
        DFHDKeySettingMgr:OnLeaveSettingPanel()
    end
    ClientGameSettingHD:NotifyCloseSystemSetting()
end

function SystemSettingHDMainView:OnClose()
    CommonSettingLogicHD.RemoveDesc()
    SettingRegLogicHD.FlushPendingSaveObjs()
    Facade.UIManager:UnRegSwitchSubUI(self)
    Module.SystemSetting.Config.Event.evtSettingMainHideHD:Invoke()
    Facade.UIManager:RemoveAllSubUI(self)
    Module.SystemSetting.Field:ClearMainViewBelongings()

    Module.SystemSetting.Field:SetLIUIRoot(nil)
end

function SystemSettingHDMainView:_OnResetAll()
    -- SettingLogicHD.DoReset()
    SettingLogicHD.ResetCurrentSettings()
end

function SystemSettingHDMainView:_DoSwitchPanel()
    if self._bPendingSwitch then
        return
    end

    if self.curPanelUIIns then
        if self.curPanelUIIns.OnMainViewSwitchLeave then
            self.curPanelUIIns:OnMainViewSwitchLeave()
        end
    end

    Module.SystemSetting.Field:SetCurrentTabTypeHD(self._curSelectedTabType)
    Module.SystemSetting.Field:SetCurrentSubTabTypeHD(self._curSelectedSubTabType)
    local uiIndex = self.uiType2subUIIndexMap[self._curSelectedTabType]
    if type(uiIndex) == "table" then
        uiIndex = uiIndex[self._curSelectedSubTabType]
    end
    local subWeakUIIns, instanceID = Facade.UIManager:SwitchSubUIByIndex(self, uiIndex, self._wtRootMain)
    -- local subUIIns = getfromweak(subWeakUIIns)
    -- if subUIIns._wtDescRootPanel  then
    --     Module.SystemSetting.Field:SetDescRootPanelHD(subUIIns._wtDescRootPanel)
    -- end
    -- Module.SystemSetting.Field:SetCurrentSettingPanelHD(subUIIns)

    -- BEGIN MODIFICATION @TanXiaoLiang - VIRTUOS
    local subUIIns = getfromweak(subWeakUIIns)
    self.curPanelUIIns = subUIIns
    if subUIIns._NotifySubTabType then
        subUIIns:_NotifySubTabType(self, self._curSelectedSubTabType)
    end
    if subUIIns.OnMainViewSwitchEnter then
        subUIIns:OnMainViewSwitchEnter()
    end
    -- END MODIFICATION

    local HeroDesc = Module.SystemSetting.Field:GetHeroDesc()
    if HeroDesc then
        Facade.UIManager:CloseUIByHandle(HeroDesc._HeroListWidgetHandle)
    end

    if self._curSelectedTabType == ESystemSettingHDPanel.DownloadCenter then
        LogAnalysisTool.SignButtonClicked(ButtonIdEnum.SystemDownloadCenter)
    end
end

function SystemSettingHDMainView:_OnClickSubTab(subTabIndex, lastSubTabIndex)
    if self._curSelectedTabType and self.subTabIndex2subTabUiTypeMap[self._curSelectedTabType] then
        if self._curSelectedSubTabType == self.subTabIndex2subTabUiTypeMap[self._curSelectedTabType][subTabIndex] then
            -- do nothing
        else
            self._curSelectedSubTabType = self.subTabIndex2subTabUiTypeMap[self._curSelectedTabType][subTabIndex]
            self:_DoSwitchPanel()
        end
    end
end

function SystemSettingHDMainView:_OnClickTab(tabIndex)
    loginfo("[george] _OnClickTab( " .. tabIndex .. " )")
    local lastSelectedTabType = self._curSelectedTabType
    if self._curSelectedTabType == self.tabIndex2uiTypeMap[tabIndex] then
        -- do nothing
    else
        self._curSelectedSubTabType = nil
        self._curSelectedTabType = self.tabIndex2uiTypeMap[tabIndex]
        -- 打开对应面板
        local function SwitchPanel(bForceSwitch)
            if type(self.uiType2subUIIndexMap[self._curSelectedTabType]) == "table" and not bForceSwitch then
            else
                self:_DoSwitchPanel()
            end
        end
        local function OnCancel()
            local bForceSwitch = self._bPendingSwitch
            self._bPendingSwitch = false
            CommonSettingLogicHD.ClearPendingSettings()
            SettingRegLogicHD.FlushPendingSaveObjs()

            SwitchPanel(bForceSwitch)
        end

        -- 检查是否有未应用的视频设置
        if CommonSettingLogicHD.AnyPendingSetting() and lastSelectedTabType == ESystemSettingHDPanel.VideoSetting then
            local OnConfirm = function()
                local bForceSwitch = self._bPendingSwitch
                self._bPendingSwitch = false

                VideoSettingLogicHD.StartConfirmDisplayModes()

                VideoSettingLogicHD.ApplyPendingSettings()

                VideoSettingLogicHD.EndConfirmDisplayModes()
                SwitchPanel(bForceSwitch)
            end
            self._bPendingSwitch = true
            VideoSettingLogicHD.ConfirmVideoSettingsBeforeLeave(OnConfirm, OnCancel, self)
        else
            OnCancel()
        end
    end
end

function SystemSettingHDMainView:_OnClickBtnCredit()
    local H5Url = "https://gamecredit.qq.com/static/index.htm#/"
    SystemUtil.OpenWeb(H5Url)
end

function SystemSettingHDMainView:_OnUploadStateChanged(state)
    if state == "start" then
        self:_SetCloudSettingState(_cloudSaveState.Uploading)
        self._uploadCount = self._uploadCount + 1
        loginfo("Start _uploadCount", self._uploadCount)
    elseif state == "success" then
        --全部上传成功才算成功
        self._uploadCount = self._uploadCount - 1
        if self._uploadCount == 0 then
            self:_SetCloudSettingState(_cloudSaveState.UploadSuccess)
        end
        loginfo("Success _uploadCount", self._uploadCount)
    elseif state == "fail" then
        -- 发现失败就算全失败
        self._uploadCount = 0
        self:_SetCloudSettingState(_cloudSaveState.UploadFail)
        loginfo("Fail _uploadCount", self._uploadCount)
    end
end

function SystemSettingHDMainView:_OnKeySettingChanged()
    if WidgetUtil.GetCurrentInputType() == EGPInputType.MouseAndKeyboard then
        self._keySettingChanged = true
        self:_SetCloudSettingCommand(_cloudSettingCommand.ChangeSetting)
    end
end

function SystemSettingHDMainView:_OnModifiedSetting(id, value)
    if table.contains(Module.SystemSetting.Config.SimpleCloudSettingIDs, id) then
        if not self._commonSettingChanged then
            self._commonSettingChanged = {}
        end
        table.insert(self._commonSettingChanged, id)
        self:_SetCloudSettingCommand(_cloudSettingCommand.ChangeSetting)
    end
end

function SystemSettingHDMainView:_SetCloudSettingCommand(command)
    local fSaveFunc = function()
        self._saveDelayHandle = nil
        self._uploadCount = 0 -- 上传动作数量，目前最大为2个，分别为设置和按键
        CloudSettingLogicHD.CheckAndSendSetting(self._commonSettingChanged, self._keySettingChanged)
        self._commonSettingChanged = {}
        self._keySettingChanged = false
    end

    if command == _cloudSettingCommand.Init then
        self:AddLuaEvent(Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged, self._OnUploadStateChanged, self)
        self:AddLuaEvent(Module.SystemSetting.Config.Event.evtCloudSettingKeySettingChanged, self._OnKeySettingChanged, self)
        self:AddLuaEvent(Module.SystemSetting.Config.Event.evtSettingValueChangedHD, self._OnModifiedSetting, self)
        self:_SetCloudSettingState(_cloudSaveState.None)
        self._cloudSavePendingCommand = nil
    elseif command == _cloudSettingCommand.ChangeSetting then
        if self._lastCloudSettingState == _cloudSaveState.Uploading then
            self._cloudSavePendingCommand = _cloudSettingCommand.ChangeSetting
        else
            self._cloudSavePendingCommand = nil
            Timer.CancelDelay(self._saveDelayHandle)
            self._saveDelayHandle = Timer.DelayCall(cloudSettingUploadStateDisplayDelayTime, fSaveFunc)
        end
    elseif command == _cloudSettingCommand.Uninit then
        self:RemoveLuaEvent(Module.SystemSetting.Config.Event.evtCloudSettingUploadStateChanged, self)
        self:RemoveLuaEvent(Module.SystemSetting.Config.Event.evtCloudSettingKeySettingChanged, self)
        self:RemoveLuaEvent(Module.SystemSetting.Config.Event.evtSettingValueChangedHD, self)
        self:_SetCloudSettingState(_cloudSaveState.None)
        self._cloudSavePendingCommand = nil

        if self._saveDelayHandle then
            Timer.CancelDelay(self._saveDelayHandle)
            self._saveDelayHandle = nil
            fSaveFunc()
        end
    end
end

function SystemSettingHDMainView:_SetCloudSettingState(state)
    loginfo("_SetCloudSettingState", state)
    local fHideFunc = function()
        if self._lastCloudSettingState ~= _cloudSaveState.Uploading then
            self._wtCloudSettingState:Collapsed()
            self._hideDelayHandle = nil
            self._lastCloudSettingState = _cloudSaveState.None
            self._cloudSaveResult = nil
        end
    end

    local bUploadHasResult = false
    if _cloudSaveState.None == state then
        Timer.CancelDelay(self._hideDelayHandle)
        fHideFunc()
    elseif _cloudSaveState.Uploading == state then
        if self._lastCloudSettingState ~= _cloudSaveState.Uploading then
            self._wtCloudSettingStateText:SetText(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.Uploading)
            self._wtUploadStateSwitcher:SetActiveWidgetIndex(0)
            self._wtCloudSettingState:SelfHitTestInvisible()
            self._lastCloudSettingState = _cloudSaveState.Uploading
            -- 防止上传过快看不到这个状态
            self._holdingUploadingHandle = Timer.DelayCall(cloudSettingUploadStateAutoDisappearTime,
                function()
                    self._holdingUploadingHandle = nil
                    if self._cloudSaveResult then
                        self:_SetCloudSettingState(self._cloudSaveResult)
                    end
                end
            )
        end
    elseif _cloudSaveState.UploadSuccess == state then
        if self._lastCloudSettingState == _cloudSaveState.Uploading then
            if self._holdingUploadingHandle then
                self._cloudSaveResult = _cloudSaveState.UploadSuccess
            else
                self._wtCloudSettingStateText:SetText(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.UploadSuccess)
                self._wtUploadStateSwitcher:SetActiveWidgetIndex(1)
                self._cloudSaveResult = nil
                self._lastCloudSettingState = state

                Timer.CancelDelay(self._hideDelayHandle)
                self._hideDelayHandle = Timer.DelayCall(cloudSettingUploadStateAutoDisappearTime, fHideFunc)
                bUploadHasResult = true
            end
        end
    elseif _cloudSaveState.UploadFail == state then
        if self._lastCloudSettingState == _cloudSaveState.Uploading then
            if self._holdingUploadingHandle then
                self._cloudSaveResult = _cloudSaveState.UploadSuccess
            else
                self._wtCloudSettingStateText:SetText(Module.SystemSetting.Config.Loc.HDSetting.CloudSettingTxt.UploadFail)
                self._wtUploadStateSwitcher:SetActiveWidgetIndex(2)
                self._cloudSaveResult = nil
                self._lastCloudSettingState = state

                Timer.CancelDelay(self._hideDelayHandle)
                self._hideDelayHandle = Timer.DelayCall(cloudSettingUploadStateAutoDisappearTime, fHideFunc)
                bUploadHasResult = true
            end
        end
    end

    -- 上传完成之后再执行下一次上传
    if bUploadHasResult then
        if self._cloudSavePendingCommand then
            self:_SetCloudSettingCommand(self._cloudSavePendingCommand)
        end
    end
end

return SystemSettingHDMainView
