local UKismetSystemLibrary = import "KismetSystemLibrary"
local Locations = require "Automation.PerfTest.Locations"

return function()
    local GameInstance = GetGameInstance()

    for i, Location in ipairs(Locations.SolLocations_InFront) do
        local Cmd = string.format("GMSpawnInLocation %s %f %f %f", "PerfTest_AI_MoveAround", Location.x, Location.y, Location.z)
        UKismetSystemLibrary.ExecuteConsoleCommand(GameInstance, Cmd)
    end
end
