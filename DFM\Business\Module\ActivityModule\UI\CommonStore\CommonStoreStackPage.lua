----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------

--- 活动通用商城 重构于明日方舟商城
---@class CommonStoreStackPage : LuaUIBaseView
local CommonStoreStackPage = ui("CommonStoreStackPage")
local ActivityRedDotLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityRedDotLogic"
local EGPUINavWidgetFocusedAction = import"EGPUINavWidgetFocusedAction"
local ActivityLogic = require "DFM.Business.Module.ActivityModule.Logic.ActivityLogic"
local ItemConfigTool = require "DFM.StandaloneLua.BusinessTool.StructTool.ItemConfigTool"
local UIThemeUtil = require "DFM.YxFramework.Managers.UI.Util.UIThemeUtil"
local WidgetUtil = require "DFM.YxFramework.Util.WidgetUtil"
local Config = Module.Activity.Config

CommonStoreStackPage.evtItemClicked = LuaEvent:NewIns("CommonStoreStackPage.evtItemClicked")
CommonStoreStackPage.evtSwitchCollect = LuaEvent:NewIns("CommonStoreStackPage.evtSwitchCollect")

function CommonStoreStackPage:Ctor()
    self._wtPageBoxs = {
        [1] = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_27", self._GetItemCount, self._ProcessItemWidget),
        [2] = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView",    self._GetItemCount, self._ProcessItemWidget),
        [3] = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_1",  self._GetItemCount, self._ProcessItemWidget),
        [4] = UIUtil.WndWaterfallScrollBox(self, "DFWaterfallScrollView_2",  self._GetItemCount, self._ProcessItemWidget),
    }
    self._wtBackGroundCDN = self:Wnd("DFCDNImage_93", DFCDNImage)
    self._wtPropIcon = {
        [1] = self:Wnd("DFCDNImage_58", DFCDNImage),--cdn
        [2] = self:Wnd("DFImage_286",   UIImage),   --奖励
        [3] = self:Wnd("DFImage",       UIImage),   --头像
        [4] = self:Wnd("DFImage_1",     UIImage),   --名片
    }
    self._wtPropQuality = self:Wnd("wtQualityIcon",          UIImage)           --图标
    self._wtPropName = self:Wnd("wtTextWeaponName",       UITextBlock)       --名称
    self._wtPropDesc = self:Wnd("DFRichTextBlock_64",     UITextBlock)       --描述
    self._wtExchgNum = self:Wnd("DFTextBlock_61",         UITextBlock)       --兑换名称/次数
    self._wtDetailBtn = self:Wnd("wtButtonPreview",        DFCommonButtonOnly)--详情
    self._wtExchangeBtn = self:Wnd("WBP_DFCommonButtonV1S2", DFCommonButtonOnly)--兑换

    self._wtDetailBtn:Event("OnClicked",   self.OnDetailBtnClicked, self)--点击
    self._wtExchangeBtn:Event("OnClicked",   self.OnExchangeBtnClicked, self)--点击
    self._wtExchangeBtn:Event("OnDeClicked", self.OnExchangeBtnDeClicked, self)--禁用点击
    --详情位置
    self._wtDiapanel = self:Wnd("DFCanvasPosReContainer_4", UIWidgetBase)    --位置
    -- self._wtRewardbg = self:Wnd("DFImage_52",             UIImage)           --背景
    --输入框逻辑
    self._wtInputBox = self:Wnd("wtDFCommonAddDecInputBoxFactor", DFCommonAddDecInputBox)
    self._wtInputBox:BindCustomAddOnClicked(self._OnAddClicked, self)
    self._wtInputBox:BindCustomDecOnClicked(self._OnDecClicked, self)
    self._wtInputBox:Event("OnAddDecInputBoxCurNumChanged",   self._OnAddNumChanged, self)
    self._wtInputBox:Event("OnAddDecInputBoxCurNumCommitted", self._OnAddNumCommitt, self)

    -- UIThemeUtil.CheckIfAutoApplyTheme(self._wtExchangeBtn)
end

-----------------------------------------------------生命周期-------------------------------------------------------
--#region

function CommonStoreStackPage:OnInitExtraData(activityID, pageIdx, targetItemIdx)
    self._activityID = activityID
    self._curPageIdx = pageIdx
    self._targetItemIdx = targetItemIdx
end

function CommonStoreStackPage:OnShowBegin()
    self:_AddEventListener()
    self:_InitData()
    self:_InitPanel()
end

function CommonStoreStackPage:OnHideBegin()
    self:RemoveAllLuaEvent()
    self:SwitchGamePadAdaptation()
    if self._addTimer then
        self._addTimer:Stop()
    end
    Module.ItemDetail:CloseItemDetailPanel()
end

function CommonStoreStackPage:OnClose()
    if self._addTimer then
        self._addTimer:Stop()
        self._addTimer:Release()
        self._addTimer = nil
    end
    self:SwitchGamePadAdaptation()
    self._wtPageBoxs = nil
    self._wtPropIcon = nil
end

--#endregion
------------------------------------------------数据初始化与界面刷新--------------------------------------------------
--#region

function CommonStoreStackPage:_InitData()
    if self._activityID then
        local activityInfo = Server.ActivityServer:GetActivityInfoByActivityID(self._activityID)
        if activityInfo then
            --背景
            self._pageDatas = activityInfo.exchange_pages

            --- 创建哈希表，方便为兑换物品分类id-类型
            local ItemIdFilter = {}
            for index, page in ipairs(activityInfo.exchange_pages or {}) do
                for _, id in ipairs(page.exchange_ids or {}) do
                    ItemIdFilter[id] = page.format
                end
            end
            local tempList = {}
            --数据分类
            for _, reward in ipairs(activityInfo.prop_exchange_items or {}) do
                local id = reward.prop and reward.prop.id
                local num = reward.prop and reward.prop.num
                local plan_id = 0
                local currency_id = 0
                local currency_num = 0
                for _, plan in ipairs(reward.plans or {}) do
                    plan_id = plan.plan_id
                    currency_id = plan.prop and plan.prop[1] and plan.prop[1].id
                    currency_num = plan.prop and plan.prop[1] and plan.prop[1].num
                    break
                end
                local data = General.GetData(id, num, reward.exchange_count_max, reward.exchanged_count, reward.exchange_id, currency_id, currency_num, reward.next_refresh_time, reward.unlock_time)
                local pageType = ItemIdFilter[reward.exchange_id]
                if pageType then
                    if tempList[pageType] == nil then
                        tempList[pageType] = {}
                    end
                    if tempList[pageType] and data then
                        --数据扩展
                        General.ExtendData(data, "desc", reward.refresh_desc)
                        General.ExtendData(data, "collect", reward.is_focus)
                        General.ExtendData(data, "plan_id", plan_id)
                        General.ExtendData(data, "back_image", reward.back_image)
                        General.ExtendData(data, "cdn",     reward.image)
                        General.ExtendData(data, "min",     General.IsHaved(data.id) and data.max or data.min)
                        table.insert(tempList[pageType], data)
                    end
                end
            end
            for index, page in ipairs(self._pageDatas or {}) do
                if page then
                    page.list = tempList[page.format]
                    --已兑换之后
                    if page.list then
                        table.sort(page.list, function (a, b)
                            if a and b then
                                local state1 = General.GetExchange(a)
                                local state2 = General.GetExchange(b)
                                if state1 == 2 or state2 == 2 then
                                    if state1 ~= 2 and state2 == 2 then
                                        return true
                                    end
                                end
                            end
                            return false
                        end)
                    end
                end
            end

            local sort = function(a, b)
                if a and b then
                    if a.format ~= b.format then
                        return General.Greater(b.format, a.format)
                    end
                end
                return false
            end
            table.sort(self._pageDatas, sort)
        end
    end
end

function CommonStoreStackPage:_InitPanel()
    self:AddStackTabBar(self._curPageIdx or 1)
end

--- 刷新页签
function CommonStoreStackPage:RefreshPageByIndex(index, isView)
    if self._pageDatas and index then
        self._list = self._pageDatas[index].list
        self._curPageIdx = index
        self._isShowCollect = true
        --记录页签类型
        self._tabFormat = self._pageDatas[index].format
        --大奖唯一判断
        local isShow = true
        if self._list and index == 1 then
            if #self._list <= 1 then
                isShow = false
            end
        end
        --box刷新
        for key, box in ipairs(self._wtPageBoxs or {}) do
            if key == self._tabFormat and isShow then
                self._pageBox = box
                if self._list and #self._list > 0 then
                    box:RefreshAllItems()
                    box:SetVisibility(ESlateVisibility.Visible)
                else
                    box:SetVisibility(ESlateVisibility.Collapsed)
                end
            else
                box:SetVisibility(ESlateVisibility.Collapsed)
            end
        end
        --启动位置控制
        if isShow then
            self:SetPosition(self._wtDiapanel, IsHD() and 914 or 808)
        else
            self:SetPosition(self._wtDiapanel, IsHD() and 184 or 236)
            --刷新界面
            if self._list then
                self._isShowCollect = false
                self._curItemIndex = index
                self:_RefreshView(self._curPageIdx, self._list[1])
            end
        end
    end
end

--#endregion
--------------------------------------------------事件监听与刷新处理--------------------------------------------------
--#region

function CommonStoreStackPage:_AddEventListener()
    self:AddLuaEvent(Server.ActivityServer.Events.evtActivityDataUpdateGeneral, self._OnDataUpdateGeneral, self)
    self:AddLuaEvent(Module.Reward.Config.Events.evtCloseRewardPanel, self._OnCloseRewardPanel, self)
    self:AddLuaEvent(Config.evtActivitySubBetweenInteractive, self._OnSubBetweenInteractive, self)
    self:AddLuaEvent(self.evtItemClicked, self.OnItemClicked, self)
end

function CommonStoreStackPage:_OnCloseRewardPanel()
    self:SwitchGamePadAdaptation(self._curSelectItemData)
end

function CommonStoreStackPage:_OnDataUpdateGeneral(key, data)
    if key == General.GetKey(self._activityID, EActSubInteractive.Next1) then
        --收到收藏回包(刷新手柄)data == uuid
        if data then
            --手柄根据当前奖励刷新
            self:SwitchGamePadAdaptation(self._curSelectItemData)
            self:_SetTabReddots()
        end
    elseif key == General.GetKey(self._activityID, EActSubInteractive.Next2) then
        --收到兑换回包(刷新界面,头像数据同步比较慢,延迟0.1s)
        Timer.DelayCall(0.1, function()
            self:_InitData()
            self:RefreshPageByIndex(self._curPageIdx, true)
            self:_SetTabReddots()
            --打开奖励展示
            self:SwitchGamePadAdaptation()
            self:ShowRewardPop(data)
        end, self)
    end
end

function CommonStoreStackPage:ShowRewardPop(res, tittle)
    local data_change = nil
    if res then
        data_change = res.data_change
        --错误提示
        Module.Activity:ShowErrorTipIfNeeded(res.expand_info)
    end
    if data_change == nil then
        --仓库已满,已通过邮件下发
        Module.CommonTips:ShowSimpleTip(Module.Activity.Config.Loc.SentViaEmail)
        return
    end
    --获取的奖励
    local itemList = {}
    for _, reward in ipairs(data_change.prop_changes or {}) do
        local e = PropChangeType
        local t = reward.change_type
        if t == e.Add or t == e.PropChangeNone or t == e.Modify or t == e.SendBuyMail then
            if reward.prop and reward.prop.num > 0 then
                --获取武器玄学皮肤
                local itemData = General.GetSkinItemData(reward.prop)
                if itemData then
                    table.insert(itemList, itemData)
                end
            end
        end
    end
    --道具转换货币
    for _, propChange in ipairs(data_change.currency_changes or {}) do
        if propChange.delta and propChange.currency_id then
            table.insert(itemList, ItemBase:NewIns(propChange.currency_id, propChange.delta))
            itemList[#itemList].src_id = propChange.src_id
        end
    end

    if #itemList == 0 then
        return
    end
    tittle = tittle or Module.Activity.Config.Loc.GetReward

    Module.Reward:OpenRewardPanel(tittle, nil, itemList, nil, nil, nil, true)
end

--- 选中item后刷新
function CommonStoreStackPage:_OnSubBetweenInteractive(key, index, data)
    if key == General.GetKey(self._activityID, EActSubInteractive.Item) then
        self._curItemIndex = index
        self:_RefreshView(self._curPageIdx, data)
    end
end

--- 选中item后刷新
function CommonStoreStackPage:OnItemClicked(index, data)
    self._curItemIndex = index
    self:_RefreshView(self._curPageIdx, data)
end
--#endregion
----------------------------------------------------刷新二级界面-----------------------------------------------------
--#region

function CommonStoreStackPage:_RefreshView(pageIndex, data)
    if data then
        local isHave, num = General.IsHave(data.max, data.min)
        self._wtInputBox:InitNum(1, 1, 1, num)
        self._wtExchgNum:SetText(General.GetTextColor({
            {txt = data.desc},
            {txt = ":"},
            {txt = " "},
            {txt = num},
        }))

        if self._tabFormat == 2 and isHave then
            if num > 1 then
                self._wtInputBox:SetVisibility(ESlateVisibility.Visible)
            else
                self._wtInputBox:SetVisibility(ESlateVisibility.HitTestSelfOnly)
            end
            self._wtExchgNum:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        else
            self._wtInputBox:SetVisibility(ESlateVisibility.Collapsed)
            self._wtExchgNum:SetVisibility(ESlateVisibility.Collapsed)
        end

        local itemData = General.GetSkinItemData({id = data.id})
        self._curSelectItemData = data
        self._itemData = itemData
        if itemData then
            self:SetImgQuality(self._wtPropQuality, itemData)
            self._wtPropName:SetText(itemData.name or "")
            self._wtPropDesc:SetText(General.GetItemDataDesc(itemData))
            self:_SetItemImage(pageIndex, itemData)
        end
        self:_SetBtnState(data)
        --手柄根据当前奖励刷新
        self:SwitchGamePadAdaptation(data)
    end
end

function CommonStoreStackPage:_SetItemImage(pageIndex, itemData)
    for index, value in ipairs(self._wtPropIcon or {}) do
        --奖励
        if index == self._tabFormat then
            if index == 1 then
                if self._curSelectItemData.back_image ~= "" then
                    local url = string.format("Resource/Texture/Activity/%s", self._curSelectItemData.back_image)
                    value:SetCDNImage(url, true, Module.CDNIcon.Config.ECdnTagEnum.Activity)
                end
            else
                if itemData then
                    value:AsyncSetImagePath(itemData.itemIconPath or "")
                end
            end
            value:SetVisibility(ESlateVisibility.HitTestSelfOnly)
        else
            value:SetVisibility(ESlateVisibility.Collapsed)
        end
    end

    -- 背景
    -- 页签1为大奖，单独显示CDN图
    if pageIndex ~= 1 and self._pageDatas[pageIndex].back_image ~= "" then
        local url = string.format("Resource/Texture/Activity/%s", self._pageDatas[pageIndex].back_image)
        self._wtBackGroundCDN:SetCDNImage(url, true, Module.CDNIcon.Config.ECdnTagEnum.Activity)
    end
end

--#endregion
-----------------------------------------------------添加导航栏------------------------------------------------------
--#region
function CommonStoreStackPage:AddStackTabBar(defalutIdx)
    local SetLockInfo = function(key, value)
        local txt = General.GetTextColor({
            {txt = value.name,},
            {txt = General.GetTimeStr(value.unlock_time, true),},
            {txt = Config.Loc.After,},
            {txt = Config.Loc.Unlock,},
        })
        local info = {
            bIsUnlocked = false, -- 是否解锁
            rawUnlocktips = txt,
            rawUnlockBtnTxt = txt,
            unlocktips = txt,
            unlockBtnTxt = txt,
            unlock1Type =  1,
            unlock1Condition = 3,
            unlock2Type =  0,
            unlock2Condition = 0,
        }
        Server.ModuleUnlockServer:SetModuleUnLockInfo(key, info)
    end
    self._reddots = {}
    local isLock = false
    local unlocks = {}
    local tabTxts = {}
    -- local themes = {}
    local imgPaths = {}
    for key, page in ipairs(self._pageDatas or {}) do
        if General.IsTimeExpired(page.unlock_time) then
            table.insert(unlocks, {})
        else
            isLock = true
            SetLockInfo(key, page)
            table.insert(unlocks, {secondLockID = key})
        end
        table.insert(imgPaths, page.icon or "")
        -- table.insert(themes, 0)
        table.insert(tabTxts, General.LocalizeText(page.name))
        table.insert(self._reddots, General.GetKey("CommonStoreStackPage", key))
    end
    --启动定时器
    if self._addTimer then
        self._addTimer:Stop()
    end
    if isLock then
        local SetTipsTxt = function()
            if self._timing == nil then
                self._timing = 0
            end
            self._timing = self._timing + 1
            for key, value in ipairs(self._pageDatas or {}) do
                if General.IsTimeExpired(value.unlock_time) then
                    if value.isLock then
                        self:_InitPanel()
                        value.isLock = false
                    end
                else
                    value.isLock = true
                    -- 每30s更新一次
                    if self._timing % 30 == 0 then
                        SetLockInfo(key, value)
                    end
                end
            end
        end
        if self._addTimer == nil then
            self._addTimer = Timer:NewIns(1, 0)
            self._addTimer:AddListener(SetTipsTxt, self)
        end
        self._addTimer:Start()
    end

    local tabs = {
        tabTxtList            = tabTxts,
        imgPathList           = imgPaths,
        reddotTrieRegItemList = self:AddReddots(self._reddots),
        fCallbackIns          = SafeCallBack(self._OnTabBarClicked, self),
        defalutIdx            = defalutIdx,
        bNewReddotTrie        = true,
        bPostCallbackWhenPop  = true,
        -- themeIDList           = themes,
        lockDataList          = unlocks,
    }
    self:RegStackTopBar(tabs)
    self:_SetTabReddots()
    self:_ShowActNavigationBar()
end

function CommonStoreStackPage:RegStackTopBar(tabs, tabLevel)
    if tabs then
        Module.CommonBar:RegStackUITopBarTitle(self, Config.Loc.RewardExchange)
        Module.CommonBar:RegStackUITopBarTabGroupRegInfo(self, tabs)
        Module.CommonBar:SetTopTabGroup(tabs, tabLevel or 2)
    end
end

function CommonStoreStackPage:AddReddots(keys, callBack)
    --创建红点节点/并刷新
    local manager = ActivityRedDotLogic.Manager
    for index, value in ipairs(keys or {}) do
        if manager then
            local isReddot = false
            if callBack then
                isReddot = callBack(index)
            end
            if manager.CreateNode and manager.SetDataByKey then
                manager:CreateNode(value)
                manager:SetDataByKey(value, isReddot)
            end
        end
    end
    --是否创建导航栏红点key
    if callBack == nil then
        local reddots = {}
        for index, value in ipairs(keys or {}) do
            local reddot = {
                uiNavId = UIName2ID.TopBar,
                reddotDataConfigWithStyleList = {{obType = EReddotTrieObserverType.Activity, key = value}},
            }
            table.insert(reddots, reddot)
        end
        return reddots
    end
end

--刷新红点
function CommonStoreStackPage:_SetTabReddots()
    --红点刷新
    self:AddReddots(self._reddots, function(dfindex)
        if self._pageDatas then
            local page = self._pageDatas[dfindex]
            for _, data in ipairs(General.GetObjectData(page, "list") or {}) do
                local state = General.GetExchange(data)
                --收藏情况下
                if page and data.collect then
                    if page and state == 4 then
                        if General.IsTimeExpired(page.unlock_time) then
                            return true
                        end
                    end
                end
            end
        end
        return false
    end)
end

function CommonStoreStackPage:_OnTabBarClicked(curIdx, lastIdx, tabLevel)
    --如果有uuid,计算出奖励下标
    Module.ItemDetail:CloseItemDetailPanel()
    self._curItemIndex = 1
    if self._targetItemIdx and self._targetItemIdx ~= 1 then
        self._curItemIndex = self._targetItemIdx
        self._targetItemIdx = nil
    end

    --设置自己的样式包(通知页签加载样式)
    -- Facade.UIManager:SetAutoApplyThemeID(EThemeIDType.CrossOver, EApplyThemeIDChangeReason.TabSelection)
    self:RefreshPageByIndex(curIdx)
end

---加载活动货币栏
function CommonStoreStackPage:_ShowActNavigationBar()
    local list = {}
    for index, value in ipairs(Server.ActivityServer:GetCurrencyColumn() or {}) do
        local clientId = General.GetCurrencyID(value.id)
        if clientId ~= 0 then
            table.insert(list, clientId)
        end
    end

    Module.CommonBar:RegStackUITopBarStyle(self, Module.CommonBar.Config.TopBarStyle.Default)

    local IsExistAct = function (act_id)
        local act = Server.ActivityServer:GetActivityInfoByActivityID(act_id)
        if act then
            local time = act.start_date
            if type(time) == "number" and time <= Facade.ClockManager:GetServerTimestamp() then
                return true
            else
                return false
            end
        else
            return false
        end
    end

    if IsExistAct(1070510001) then
        table.insert(list, ECurrencyClientId.LogisticsVoucher)
    end
    if IsExistAct(1080530001) then
        table.insert(list, ECurrencyClientId.CooperateVoucher)
    end
    table.insert(list, ECurrencyClientId.Tina)
    Module.CommonBar:RegStackUITopBarCurrencyTypeList(self, list)
end
--#endregion
--------------------------------------------------WaterfallBox逻辑--------------------------------------------------
--#region

function CommonStoreStackPage:_GetItemCount()
    if self._list then
        return #self._list
    else
        return 0
    end
end

function CommonStoreStackPage:_ProcessItemWidget(position, itemWidget)
    local index = position
    if self._list and index and itemWidget then
        local data = self._list[index]
        if data then
            --如果是当前奖励(刷新界面)
            if index == self._curItemIndex then
                self:_RefreshView(self._curPageIdx, data)
            end
            itemWidget:InitData(self._activityID, index, data, self._curItemIndex)
        end
    end
end
--#endregion
-----------------------------------------------------输入框逻辑------------------------------------------------------
--#region

function CommonStoreStackPage:_OnAddClicked(curNum)
    return curNum + 1
end

function CommonStoreStackPage:_OnDecClicked(curNum)
    return curNum - 1
end

--- 此二者用于手柄操作
--- 暂时不知道如何直接绑定到点击事件上，所以只能自行实现
function CommonStoreStackPage:_OnInputBoxDecClicked()
    if self._curSelectItemData then
        if self._num == nil then
            self._num = 0
        end
        self._num = self._num - 1
        local isHave, num = General.IsHave(self._curSelectItemData.max, self._curSelectItemData.min)
        self._wtInputBox:InitNum(self._num, 1, 1, num)
        self:_OnAddNumChanged(self._num, 1)
    end
end

function CommonStoreStackPage:_OnInputBoxAddClicked()
    if self._curSelectItemData then
        if self._num == nil then
            self._num = 0
        end
        self._num = self._num + 1
        local isHave, num = General.IsHave(self._curSelectItemData.max, self._curSelectItemData.min)
        self._wtInputBox:InitNum(self._num, 1, 1, num)
        self:_OnAddNumChanged(self._num, 1)
    end
end

function CommonStoreStackPage:_OnAddNumChanged(curNum, changeNum)
    self._num = curNum > 0 and curNum or math.abs(curNum)
    if self._num == 0 then
        self._num = 1
    end
    self:_OnNumUpdate()
end

function CommonStoreStackPage:_OnAddNumCommitt(curNum, changeNum)
    self._num = curNum > 0 and curNum or math.abs(curNum)
    if self._num == 0 then
        self._num = 1
    end
    self:_OnNumUpdate()
end

function CommonStoreStackPage:_OnNumUpdate()
    self:_SetBtnState(self._curSelectItemData, self._num)
end
--#endregion
------------------------------------------------------按钮回调-------------------------------------------------------
--#region

function CommonStoreStackPage:OnDetailBtnClicked()
    self:ShowItemDetail3D(self._itemData, self._wtDetailBtn)
end

function CommonStoreStackPage:OnExchangeBtnClicked()
    --发送到服务器(点击兑换)----->
    Server.ActivityServer:SendFangzhouExchange(self._activityID, self._curSelectItemData.uuid, self._curSelectItemData.plan_id, self._num)
end

function CommonStoreStackPage:OnExchangeBtnDeClicked()
    --点击提示
    local itemData = General.GetSkinItemData({id = self._curSelectItemData.currencyId})
    if itemData then
        Module.CommonTips:ShowSimpleTip(General.GetText(Config.Loc.Insufficient, itemData.name))
    end
end

function CommonStoreStackPage:ShowItemDetail3D(itemData, btn)
    if itemData == nil then
        return
    end

    loginfo("ShowItemDetail3D open itemData name = ", itemData.name)

    if itemData.itemMainType == EItemType.WeaponSkin then
        Module.Collection:ShowWeaponSkinDetailPage(itemData)--武器皮肤(包括刀皮)
    elseif itemData.itemMainType == EItemType.Adapter then
        Module.Collection:ShowHangingDetailPage(itemData)--配件(挂饰)
    elseif itemData.itemMainType == EItemType.Fashion then
        Facade.UIManager:AsyncShowUI(UIName2ID.StaffLotteryAccessoriesPreview, nil, nil, itemData.id)--干员皮肤
    else
        Module.ItemDetail:OpenItemDetailPanel(itemData, btn)--详情页
    end
end
--#endregion

-------------------------------------------------------手柄处理------------------------------------------------------
--#region

---开关手柄功能，data为空时关闭
function CommonStoreStackPage:SwitchGamePadAdaptation(data)
    self:_RemoveHandleAdaptation()

    if data then
        local state = General.GetExchange(data)
        self._navGroup = WidgetUtil.RegisterNavigationGroup(self._box, self, "Hittest")
        if isvalid(self._navGroup) then
            self._navGroup:AddNavWidgetToArray(self._box)
            self._navGroup:SetScrollRecipient(self._box)
            self._navGroup:MarkIsStackControlGroup()
            self._navGroup:SetWidgetFocusedAction(EGPUINavWidgetFocusedAction.Click)
            WidgetUtil.TryFocusDefaultWidgetByGroup(self._navGroup)
        end
        
        local collectInput = nil
        --模拟收藏点击 Y
        if state > 2 and self._isShowCollect then
            local actionName = data.collect and "ActivityCancelCollection" or "ActivityCollect"
            collectInput = {actionName = actionName, func = function()
                self.evtSwitchCollect:Invoke(self._curItemIndex)
            end, caller = self, bUIOnly = false}
        end
        
        --模拟详情点击
        local topBarInputList = {{actionName = "RewardsDetails_ActivityPrize", func = function()
            --模拟详情点击
            self:_OnBtnClicked(EActSubInteractive.Next1, 2)
            end, caller = self, bUIOnly = false},
        }

        if collectInput then
            table.insert(topBarInputList, 1, collectInput)
        end
        Module.CommonBar:SetBottomBarTempInputSummaryList(topBarInputList)

        --兑换X按钮
        if self._XInputHandle == nil then
            self._XInputHandle = self:AddInputActionBinding("SingleDraw_ActivityPrize", EInputEvent.IE_Pressed, self.OnExchangeBtnClicked, self, EDisplayInputActionPriority.UI_Stack)
        end
        if self._XInputHandle then
            self._wtExchangeBtn:SetDisplayInputActionWithLongPress(self._XInputHandle, self, "SingleDraw_ActivityPrize", true, nil, true)
        end

        --物资页签
        local isHave, num = General.IsHave(data.max, data.min)
        if self._tabFormat == 2 and num > 1 then
            --让出方向键和A键
            WidgetUtil.EnableDynamicNavConfig(WidgetUtil.EUINavDynamicType.NoLR, self)
            self:SwitchInputBoxGamepadAction(true)
        end
    else
        WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    end
end

---右侧数量输入框手柄功能开关
function CommonStoreStackPage:SwitchInputBoxGamepadAction(isBool)
    if self._decHandle then
        self:RemoveInputActionBinding(self._decHandle)
        self._decHandle = nil
    end
    if self._addHandle then
        self:RemoveInputActionBinding(self._addHandle)
        self._addHandle = nil
    end
    if isBool then
        self._wtInputBox:InitDecAddInputActionName("Inventory_SliderLeft_Gamepad", "Inventory_SliderRight_Gamepad")
        if self._decHandle == nil then
            self._decHandle = self:AddInputActionBinding("Inventory_SliderLeft_Gamepad", EInputEvent.IE_Pressed, self._OnInputBoxDecClicked, self, EDisplayInputActionPriority.UI_Stack)
        end
        if self._addHandle == nil then
            self._addHandle = self:AddInputActionBinding("Inventory_SliderRight_Gamepad", EInputEvent.IE_Pressed, self._OnInputBoxAddClicked, self, EDisplayInputActionPriority.UI_Stack)
        end
    end
end

function CommonStoreStackPage:_RemoveHandleAdaptation()
    self._navGroup = nil
    WidgetUtil.RemoveNavigationGroup(self)
    WidgetUtil.DisableDynamicNavConfigsbyWidget(self)
    Module.CommonBar:RecoverBottomBarInputSummaryList()
    if self._XInputHandle then
        self:RemoveInputActionBinding(self._XInputHandle)
    end
    self._XInputHandle = nil
end
--#endregion
----------------------------------------------------通用函数ssy------------------------------------------------------
--#region

function CommonStoreStackPage:SetPosition(widget, x, y)
    if widget then
        local canvasSlot = UWidgetLayoutLibrary.SlotAsCanvasSlot(widget)
        if canvasSlot then
            if canvasSlot.GetPosition then
                local position = canvasSlot:GetPosition()
                if position then
                    x = x or position.X
                    y = y or position.Y
                end
            end
            if canvasSlot.SetPosition and x and y then
                canvasSlot:SetPosition(FVector2D(x, y))
            end
        end
    end
end

function CommonStoreStackPage:SetImgQuality(widget, itemData)
    if widget and itemData then
        if itemData.quality then
            --道具品质图标
            local QualityIconMapping = {
                [0] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
                [1] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0401.Common_ItemProp_Icon_0401'",
                [2] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0402.Common_ItemProp_Icon_0402'",
                [3] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0403.Common_ItemProp_Icon_0403'",
                [4] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0404.Common_ItemProp_Icon_0404'",
                [5] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0405.Common_ItemProp_Icon_0405'",
                [6] = "PaperSprite'/Game/UI/UIAtlas/Common/BakedSprite/Common_ItemProp_Icon_0406.Common_ItemProp_Icon_0406'",
            }
            local path = QualityIconMapping[itemData.quality]
            local color = ItemConfigTool.GetItemQualityLinearColor(itemData.quality)
            if path and color and widget.AsyncSetImagePath then
                widget:AsyncSetImagePath(path or "", true)
                if widget.SetColorAndOpacity then
                    widget:SetColorAndOpacity(color)
                end
            end
        end
    end
end

function CommonStoreStackPage:_SetBtnState(data, times)
    if data then
        local str, enable
        local state = General.GetExchange(data, times)
        --计算数量
        local num = data.currencyNum
        if times and num then
            num = num * times
        end
        if state == 0 then
            str = Config.Loc.ToBeUnlocked
        elseif state == 2 then
            str = Config.Loc.AlreadyRedeemed
        elseif state == 3 then
            enable = false
            str = General.GetCurrencyStr(data.currencyId, num, 1)
        elseif state == 4 then
            enable = true
            str = General.GetCurrencyStr(data.currencyId, num)
        end
        self._wtExchangeBtn:SetMainTitle(str)
        if enable ~= nil then
            self._wtExchangeBtn:SetIsEnabledStyle(enable)
        else
            self._wtExchangeBtn:SetBtnEnable(false)
        end
    end
end

--#endregion
return CommonStoreStackPage