---@class GameTeamInviteViewModel : LuaObject
local GameTeamInviteViewModel = class('GameTeamInviteViewModel', LuaObject)
local ChatLogic = require "DFM.Business.Module.ChatModule.Logic.ChatLogic"

function GameTeamInviteViewModel:Ctor()
    self.mPendingReqNum = 0
    self.mInviteDataCache = {}
    self.mCurIndex = nil
    self.mCycleTimer = nil
    self.mIsInGameStage = false
end

function GameTeamInviteViewModel:AddListeners()
    self:AddLuaEvent(Server.TeamServer.Events.evtWhenGamingReceiveTeamInvite, self._OnReceiveTeamInvite, self)
end

function GameTeamInviteViewModel:_OnReceiveTeamInvite(ntf, fConfirmFunction, fRefuseFunction)
    if not self.mIsInGameStage then
        return
    end

    if not Server.AccountServer:IsPlayerInGame() then
        return
    end

    self:AddSingleCacheData(ntf, fConfirmFunction, fRefuseFunction)
end

function GameTeamInviteViewModel:AddSingleCacheData(cacheData, fConfirmFunction, fRefuseFunction)
    if cacheData == nil then
        return
    end

    cacheData.custom_arrive_time = TimeUtil:GetCurrentTime()
    cacheData.custom_invitestate = Module.Team.Config.InGameInviteStateEnum.PendingRes
    cacheData.customConfirmFunc = fConfirmFunction
    cacheData.customRefuseFunc = fRefuseFunction
    table.insert(self.mInviteDataCache, cacheData)
    self.mPendingReqNum = self.mPendingReqNum + 1
    -- 广播新邀请到chatServer
    Server.ChatServer:AddTeamInviteMsg(#self.mInviteDataCache)

    self:UpdateCurIndex(true)
    if self.mCycleTimer == nil then
        self:StartCycleTimer()
    end

    Module.Chat.Config.evtInGameTeamInviteUpdate:Invoke(Module.Team.Config.InGameInviteStateEnum.PendingRes)
end

function GameTeamInviteViewModel:RejectInviteByIndex(index)
    local dataValue = self:GetCacheData(index)
    if dataValue and dataValue.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes then
        if dataValue.customRefuseFunc then

            if dataValue.inviteType == "TeamInvite" then
                dataValue.customRefuseFunc(dataValue.TeamID, dataValue.Inviter.player_id, "", dataValue.source)
            else
                dataValue.customRefuseFunc("")
            end
        end

        dataValue.custom_invitestate = Module.Team.Config.InGameInviteStateEnum.Rejected
        self.mPendingReqNum = self.mPendingReqNum - 1
        if index == self.mCurIndex then
            self:UpdateCurIndex()
        end
        Module.Chat.Config.evtInGameTeamInviteUpdate:Invoke(Module.Team.Config.InGameInviteStateEnum.Rejected)
    end
end

function GameTeamInviteViewModel:AgreeInviteByIndex(index)
    local dataValue = self:GetCacheData(index)
    if dataValue and dataValue.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes then
        if dataValue.customConfirmFunc then
            if dataValue.inviteType == "TeamApply" then
                dataValue.customConfirmFunc()
            else
                dataValue.customConfirmFunc(dataValue.TeamID, dataValue.Inviter.player_id, dataValue.source)
            end
        end

        dataValue.custom_invitestate = Module.Team.Config.InGameInviteStateEnum.Agreed
        self.mPendingReqNum = self.mPendingReqNum - 1
        if index == self.mCurIndex then
            self:UpdateCurIndex()
        end
        Module.Chat.Config.evtInGameTeamInviteUpdate:Invoke(Module.Team.Config.InGameInviteStateEnum.Agreed)
    end
end

function GameTeamInviteViewModel:GetCacheData(index)
    return self.mInviteDataCache[index]
end

function GameTeamInviteViewModel:ConvenientAgreeInvite()
    local dataValue, index = self:GetTheOldestPendingReq()
    if dataValue and index then
        self:AgreeInviteByIndex(index)
    end
end

function GameTeamInviteViewModel:ConvenientRejectInvite()
    local dataValue, index = self:GetTheOldestPendingReq()
    if dataValue and index then
        self:RejectInviteByIndex(index)
    end
end

function GameTeamInviteViewModel:HaveOldestPendingReq()
    local dataValue, index = self:GetTheOldestPendingReq()
    if dataValue and index then
        return true
    end

    return false
end

function GameTeamInviteViewModel:GetTheOldestPendingReq()
    -- 数据异常先刷一次
    if self.mCurIndex == nil or self.mCurIndex == -1 then
        self:UpdateCurIndex()
    end

    -- 直接返回nil
    if self.mCurIndex == nil or self.mCurIndex == -1 then
        return nil, nil
    end

    local startIndex = self.mCurIndex
    local endIndex = #self.mInviteDataCache
    for index = startIndex, endIndex do
        local element = self.mInviteDataCache[index]
        if element and element.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes then
            return element, index
        end
    end

    return nil, nil
end

function GameTeamInviteViewModel:GetPendingSum()
    return self.mPendingReqNum
end

function GameTeamInviteViewModel:GetTargetModeStr(dataValue)
    local modeName = Module.Social.Config.Loc.NoTarget
    if dataValue.inviteType ~= "TeamInvite" then
        return modeName
    end

    if (not dataValue.Modes) or (not dataValue.Modes[1].game_mode) then
        return modeName
    end

    if dataValue.Modes[1].game_mode == MatchGameMode.TDMGameMode then
        modeName = Module.GameMode:GetTDMModeNameByGroupId(dataValue.GroupID)
    else
        if dataValue.Modes[1].game_rule == MatchGameRule.ArenaGameRule then
            modeName = Module.GameMode:GetGameRuleNameByMatchModeId(dataValue.Modes[1].match_mode_id)
        else
            modeName = Module.GameMode:GetStandardMapNameByMatchModeId(dataValue.Modes[1].match_mode_id)
        end
    end
    return modeName
end

function GameTeamInviteViewModel:UpdateCurIndex(isAddElement)
    isAddElement = setdefault(isAddElement, false)
    if self.mCurIndex == nil then  -- 第一次赋值
        self.mCurIndex = 1  -- 赋原始值
    elseif self.mCurIndex == -1 then  -- 至少经历过一次所有请求都过期
        if isAddElement then
            self.mCurIndex = #self.mInviteDataCache
        end
    else  -- 常规情况
        local maxIndex = #self.mInviteDataCache
        -- 超出范围值 直接把下标拉回第一个
        if self.mCurIndex > maxIndex then
            self.mCurIndex = 1
        end

        local function CanTriggerStateChange()
            local curTime = TimeUtil:GetCurrentTime()
            local startIndex = self.mCurIndex
            local endIndex = maxIndex

            local nothingChange = true
            for index = startIndex, endIndex do
                local result = self:SingleItemBeExpired(index, curTime)

                -- 有条目应该从未处理切换到已过期/其他
                if result then
                    nothingChange = false
                    -- 所有预约组队条目都已过期
                    if index == endIndex then
                        self.mCurIndex = -1
                    end
                else
                    local element = self.mInviteDataCache[index]
                    -- 当前最旧的条目依旧没有过期
                    if index == self.mCurIndex and element.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes then
                        break
                    end

                    if element and element.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes then
                        self.mCurIndex = index
                    end
                end
            end

            return nothingChange, startIndex
        end

        local nothingChangeValue, startIndexVal = CanTriggerStateChange()

        if not nothingChangeValue then
            Module.Chat.Config.evtInGameTeamInviteUpdate:Invoke(Module.Team.Config.InGameInviteStateEnum.Expired, startIndexVal)
        end
    end
end

function GameTeamInviteViewModel:SingleItemBeExpired(index, curTime)
    local element = self.mInviteDataCache[index]
    -- 异常情况,一般不会发生
    if element == nil then
        return false
    end

    -- 该请求应该过期
    if curTime > element.custom_arrive_time + Module.Social.Config.DEFAULT_NTF_REMAIN_COLD_TIME then
        if element.custom_invitestate == Module.Team.Config.InGameInviteStateEnum.PendingRes then
            element.custom_invitestate = Module.Team.Config.InGameInviteStateEnum.Expired
            self.mPendingReqNum = self.mPendingReqNum - 1
            return true
        end
    end

    return false
end

function GameTeamInviteViewModel:RemoveCycleTimer()
    if self.mCycleTimer ~= nil then
        self.mCycleTimer:Stop()
        self.mCycleTimer:Release()
        self.mCycleTimer = nil
    end
end

function GameTeamInviteViewModel:StartCycleTimer()
    local TimeArrive = function()
        self:UpdateCurIndex()
    end

    self:RemoveCycleTimer()
    if #self.mInviteDataCache <= 0 then
        return
    end

    self.mCycleTimer = Timer:NewIns(1, 0)
    self.mCycleTimer:AddListener(TimeArrive)
    self.mCycleTimer:Start()
end

function GameTeamInviteViewModel:ExitGameStage()
    self.mInviteDataCache = {}
    self.mPendingReqNum = 0
    self.mCurIndex = nil
    self.mIsInGameStage = false
    self:RemoveLuaEvent(Server.TeamServer.Events.evtWhenGamingReceiveTeamInvite, self._OnReceiveTeamInvite, self)
end

function GameTeamInviteViewModel:EnterameStage()
    self.mIsInGameStage = true
    self:AddLuaEvent(Server.TeamServer.Events.evtWhenGamingReceiveTeamInvite, self._OnReceiveTeamInvite, self)
end

function GameTeamInviteViewModel:RemoveCacheData()
    self:RemoveCycleTimer()
    self.mInviteDataCache = {}
    self.mPendingReqNum = 0
    self.mCurIndex = nil
end

function GameTeamInviteViewModel:Clear()
    self:RemoveAllLuaEvent()
    self:RemoveCacheData()
end

return GameTeamInviteViewModel
