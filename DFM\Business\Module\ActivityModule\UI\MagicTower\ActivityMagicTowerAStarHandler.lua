----- LOG FUNCTION AUTO GENERATE -----------
local loginfo, logwarning, logerror = GenLocalLogFunc(ELuaLogCategory.LuaMActivity)
----- LOG FUNCTION AUTO GENERATE END -----------





-- lcoal ActivityMagicTowerAStarNode = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerAStarNode"
local ActivityMagicTowerAStarHandler = {}
local ConfigManager = require "DFM.Business.Module.ActivityModule.UI.MagicTower.ActivityMagicTowerConfigManager"
local _specialPointConfig = ConfigManager.GetSpecialPointConfigTable()
local SpecialPointType = {
    HERO_SPAWN        = 1,  -- 主角出生点
    NEWBIE_SPAWN      = 2,  -- 新手主角出生点
    FREE_EXIT         = 3,  -- 无条件撤离点
    PAY_EXIT          = 4,  -- 丢钱撤离点
    LEVER             = 5,  -- 拉闸点
    LEVER_LOCKED_EXIT = 6,  -- 需拉闸撤离点
    TELEPORT          = 7,  -- 联通传送点
    DIALOGUE          = 8,  -- 纯对话点
    WALL              = 9,  -- 墙
    SPAWN             = 10, -- 生成点
    DOOR              = 11, -- 门
}
-- A*算法主函数
function ActivityMagicTowerAStarHandler.aStar(grid, start, goal,allWall)
    if start.x == goal.x and start.y == goal.y then
        return nil
    end

    local openList = {}
    local closedList = {}
    
    -- 初始化起点
    start.g = 0
    start.h = ActivityMagicTowerAStarHandler.manhattanDistance(start, goal)
    start.f = start.g + start.h
    table.insert(openList, start)
    
    while #openList > 0 do
        -- 按f值排序开放列表
        table.sort(openList, function(a, b) return a.f < b.f end)
        
        -- 获取f值最小的节点
        local current = table.remove(openList, 1)
        
        -- 检查是否到达目标
        if current.x == goal.x and current.y == goal.y then
            -- 回溯路径
            local path = {}
            while current do
                table.insert(path, 1, {x = current.x, y = current.y})
                current = current.parent
            end

            if #path >= 1 then
                table.remove(path, 1)
            end
            return path
        end
        
        -- 将当前节点加入关闭列表
        table.insert(closedList, current)
        
        -- 检查邻居节点
        local neighbors = ActivityMagicTowerAStarHandler.getNeighbors(current, grid,allWall)
        for _, neighbor in ipairs(neighbors) do
            -- 跳过已处理的节点
            if not ActivityMagicTowerAStarHandler.isInList(closedList, neighbor) then
                -- 计算g值（假设每步代价为1）
                local tentative_g = current.g + 1
                
                -- 检查是否在开放列表中
                local inOpen = ActivityMagicTowerAStarHandler.isInList(openList, neighbor)
                
                if not inOpen or tentative_g < neighbor.g then
                    -- 更新节点信息
                    neighbor.parent = current
                    neighbor.g = tentative_g
                    neighbor.h = ActivityMagicTowerAStarHandler.manhattanDistance(neighbor, goal)
                    neighbor.f = neighbor.g + neighbor.h
                    
                    if not inOpen then
                        table.insert(openList, neighbor)
                    end
                end
            end
        end
    end
    
    -- 没有找到路径
    return nil
end

-- 计算曼哈顿距离（启发式函数）
function ActivityMagicTowerAStarHandler.manhattanDistance(start, goal)
    return math.abs(start.x - goal.x) + math.abs(start.y - goal.y)
end

-- 检查节点是否在列表中
function ActivityMagicTowerAStarHandler.isInList(list, MagicTowerMapNode)
    for _, n in ipairs(list) do
        if n.x == MagicTowerMapNode.x and n.y == MagicTowerMapNode.y then
            return true
        end
    end
    return false
end

-- 获取邻居节点（四方向）
function ActivityMagicTowerAStarHandler.getNeighbors(MagicTowerMapNode, grid,allWall)
    local neighbors = {}
    local directions = {
        {-1, 0}, {1, 0}, {0, -1}, {0, 1} -- 上下左右
    }
    
    for _, dir in ipairs(directions) do
        local x, y = MagicTowerMapNode.x + dir[1], MagicTowerMapNode.y + dir[2]
        
        -- 检查边界和障碍物
        if x >= 1 and x <= #grid and y >= 1 and y <= #grid[1] then
           local specialPointID = grid[x][y]
            if grid[x][y] ~= 2030901 then -- 假设1表示障碍物
                local node = {
                    x = x, y = y,
                    g = 0, h = 0, f = 0,
                    parent = nil
                }
                if allWall then 
                    local config = _specialPointConfig[specialPointID]
                    if specialPointID == 0  or ( config and config.specialPointType ~= SpecialPointType.DIALOGUE and config.specialPointType ~= SpecialPointType.SPAWN ) then  
                        table.insert(neighbors, node)
                    end
                else
                    table.insert(neighbors, node)
                end
            end
        end
    end
    
    return neighbors
end

return ActivityMagicTowerAStarHandler

-- -- 示例使用
-- local grid = {
--     {0, 0, 0, 0, 0},
--     {0, 1, 1, 1, 0}, -- 1表示障碍物
--     {0, 0, 0, 0, 0},
--     {0, 1, 1, 1, 0},
--     {0, 0, 0, 0, 0}
-- }

-- local start = MagicTowerMapNode:new(1, 1)
-- local goal = MagicTowerMapNode:new(5, 5)

-- local path = aStar(grid, start, goal)

-- if path then
--     print("找到路径：")
--     for i, node in ipairs(path) do
--         print(i..": ("..node.x..", "..node.y..")")
--     end
-- else
--     print("未找到路径")
-- end